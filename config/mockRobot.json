[{"notes": "登录", "operation": 10100, "duration": 1, "jsonParam": {}}, {"notes": "玩家数据", "operation": 11003, "duration": 1, "jsonParam": {"User": true, "Bags": true, "Equips": true, "Artifacts": true, "Emblems": true, "GlobalAttr": true, "Heroes": true, "Formation": true, "Dungeon": true, "Tasks": true, "GuildTalent": true, "Guidance": true, "SevendayLogin": true, "ClientInfo": true, "SummonData": true, "Crystal": true, "PushGift": true, "OperateActivity": true, "Forest": true, "Dispatch": true}}, {"notes": "问卷信息", "operation": 11058, "duration": 0, "jsonParam": {}}, {"notes": "客户端设置语言", "operation": 11076, "duration": 0, "jsonParam": {"lang": "zh"}}, {"notes": "获取游戏内公告", "operation": 16301, "duration": 0, "jsonParam": {"lang": "zh"}}, {"notes": "获取限时礼包信息", "operation": 15201, "duration": 0, "jsonParam": {}}, {"notes": "好友信息", "operation": 11501, "duration": 0, "jsonParam": {}}, {"notes": "获取功能状态信息", "operation": 16201, "duration": 0, "jsonParam": {"func_id": [204, 213, 214, 215, 189]}}, {"notes": "获取vip信息", "operation": 15101, "duration": 0, "jsonParam": {}}, {"notes": "获取月卡信息", "operation": 15301, "duration": 0, "jsonParam": {}}, {"notes": "图鉴信息", "operation": 14601, "duration": 0, "jsonParam": {}}, {"notes": "充值信息", "operation": 14901, "duration": 0, "jsonParam": {}}, {"notes": "商店信息", "operation": 11301, "duration": 0, "jsonParam": {}}, {"notes": "嘉年华信息", "operation": 14301, "duration": 0, "jsonParam": {}}, {"notes": "神器首发信息", "operation": 16815, "duration": 0, "jsonParam": {}}, {"notes": "获取战令信息", "operation": 15401, "duration": 0, "jsonParam": {"sys_id": [1, 2, 11, 12, 13, 14]}}, {"notes": "获取头像边框信息", "operation": 13001, "duration": 0, "jsonParam": {}}, {"notes": "功勋信息", "operation": 14501, "duration": 0, "jsonParam": {}}, {"notes": "活动阶梯礼包xml信息", "operation": 15703, "duration": 0, "jsonParam": {}}, {"notes": "活动阶梯礼包data信息", "operation": 15701, "duration": 0, "jsonParam": {}}, {"notes": "获取新功能开启信息", "operation": 14801, "duration": 0, "jsonParam": {}}, {"notes": "累抽信息", "operation": 17401, "duration": 0, "jsonParam": {}}, {"notes": "红点信息", "operation": 11055, "duration": 1, "jsonParam": {"id_list": [[10131], [10152], [10941, 10942]]}}, {"notes": "神魔抽卡信息", "operation": 15901, "duration": 0, "jsonParam": {}}, {"notes": "link信息", "operation": 16101, "duration": 0, "jsonParam": {}}, {"notes": "个人boss信息", "operation": 13601, "duration": 0, "jsonParam": {}}, {"notes": "材料本信息", "operation": 12801, "duration": 0, "jsonParam": {}}, {"notes": "设置名字", "operation": 11049, "duration": 1, "jsonParam": {}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": [], "selfHero": ["4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10101}}, {"notes": "引导打点", "operation": 9903, "duration": 0, "jsonParam": {"guideid": "2004"}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 20, "step": 2004}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 20}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["1_101020"], "selfHero": ["4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10102}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 21, "step": 2103}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 21}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["3_101040"], "selfHero": ["4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10103}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 50, "step": 5003}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 50}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": [], "selfHero": ["3_24992", "4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10104}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 60}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 71, "step": 7101}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 71}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10105}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 80, "step": 8001}}, {"notes": "挂机预览", "operation": 11807, "duration": 1, "jsonParam": {}}, {"notes": "主线挂机奖励", "operation": 11803, "duration": 1, "jsonParam": {}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 80, "step": 8003}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980, "num": 1}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 80, "step": 8007}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 80}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980, "num": 3}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992, "num": 3}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 81}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 90}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10106}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10107}}, {"notes": "商店信息", "operation": 11301, "duration": 0, "jsonParam": {}}, {"notes": "点金信息", "operation": 13201, "duration": 0, "jsonParam": {}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 101}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 232, "step": 23201}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 232}}, {"notes": "女神圣所信息", "operation": 15801, "duration": 1, "jsonParam": {}}, {"notes": "女神圣所解锁", "operation": 15809, "duration": 1, "jsonParam": {"goddess_id": 1}}, {"notes": "女神圣所治疗", "operation": 15818, "duration": 1, "jsonParam": {"goddess_id": 1}}, {"notes": "召唤", "operation": 12001, "duration": 1, "jsonParam": {"id": 3, "plural": true}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 110}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 141, "step": 14101}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 141}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 100}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 150}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "引导打点", "operation": 9903, "duration": 0, "jsonParam": {"guideid": "16001"}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 160, "step": 16001}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 160}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": [], "selfHero": ["1_11992", "3_24992", "4_13980", "5_23982"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10201}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 162}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10202}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 4}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992, "num": 4}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 23982, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 23982, "num": 4}}, {"notes": "图鉴激活", "operation": 14607, "duration": 0, "jsonParam": {"hero_sysId": 23982, "link_attr_id": 23982005}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 927, "step": 92707}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 927}}, {"notes": "图鉴激活", "operation": 14607, "duration": 0, "jsonParam": {"hero_sysId": 13980, "link_attr_id": 13980005}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["2_102030"], "selfHero": ["1_11992", "3_24992", "4_13980", "5_23982"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10203}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["2_102040"], "selfHero": ["1_11992", "3_24992", "4_13980", "5_23982"]}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 161}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10204}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["2_102040"], "selfHero": ["1_11992", "3_24992", "4_13980", "5_23982"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10205}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["2_102040"], "selfHero": ["1_11992", "3_24992", "4_13980", "5_23982"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10206}}, {"notes": "领取主线通关奖励", "operation": 12103, "duration": 1, "jsonParam": {"ids": [28000]}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10207}}, {"notes": "加速", "operation": 11805, "duration": 1, "jsonParam": {}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 0, "jsonParam": {"group": 190, "step": 19002}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 190}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 200}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 23982}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 1, "hid": 23982, "num": 1}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 1, "hid": 23982, "num": 10}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 3, "hid": 23982, "num": 10}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 3, "hid": 23982, "num": 10}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 3, "hid": 23982, "num": 9}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 2, "hid": 23982, "num": 10}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 2, "hid": 23982, "num": 10}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 2, "hid": 23982, "num": 9}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 4, "hid": 23982, "num": 10}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 4, "hid": 23982, "num": 10}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 4, "hid": 23982, "num": 9}}, {"notes": "强化装备", "operation": 12305, "duration": 1, "jsonParam": {"part": 1, "hid": 23982, "num": 9}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 211}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 23982, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 23982, "num": 8}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 220}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10208}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10209}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 221}}, {"notes": "领取主线通关奖励", "operation": 12103, "duration": 1, "jsonParam": {"ids": [28001]}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "selfHero": ["1_11992", "2_13980", "3_24992", "4_22981", "5_23982"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10210}}, {"notes": "设置客户端信息", "operation": 11068, "duration": 0, "jsonParam": {"infos": {"1": [44]}}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 290}}, {"notes": "获取材料本信息", "operation": 12801, "duration": 1, "jsonParam": {}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [46]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 46, "selfHero": ["1_11992", "2_13980", "3_24992", "4_22981", "5_23982"]}}, {"notes": "材料本战斗", "operation": 12803, "duration": 3, "jsonParam": {"type": 46}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [46]}}, {"notes": "材料本战斗", "operation": 12803, "duration": 3, "jsonParam": {"type": 46}}, {"notes": "材料本预览奖励", "operation": 12807, "duration": 1, "jsonParam": {}}, {"notes": "材料本领取奖励", "operation": 12809, "duration": 1, "jsonParam": {}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 291}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10211}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10212}}, {"notes": "排行榜成就展示", "operation": 13401, "duration": 0, "jsonParam": {"rank_id": 2}}, {"notes": "聊天获取token", "operation": 11615, "duration": 0, "jsonParam": {}}, {"notes": "领取主线通关奖励", "operation": 12103, "duration": 1, "jsonParam": {"ids": [28002]}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 224}}, {"notes": "女神圣所信息", "operation": 15801, "duration": 1, "jsonParam": {}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 222}}, {"notes": "女神圣所治疗", "operation": 15818, "duration": 1, "jsonParam": {"goddess_id": 1}}, {"notes": "领取功勋奖励", "operation": 14503, "duration": 1, "jsonParam": {"level": 1, "type": 3, "ids": [110001002]}}, {"notes": "领取功勋奖励", "operation": 14503, "duration": 1, "jsonParam": {"level": 1, "type": 3, "ids": [110001003]}}, {"notes": "加速", "operation": 11805, "duration": 1, "jsonParam": {}}, {"notes": "加速", "operation": 11805, "duration": 1, "jsonParam": {}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 13980}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980, "num": 4}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 11992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 11992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 11992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 11992}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 4}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 22981}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 22981, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 22981, "num": 4}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 22981, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 22981, "num": 4}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10301}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10302}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10303}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10304}}, {"notes": "设置客户端信息", "operation": 11068, "duration": 0, "jsonParam": {"infos": {"1": [44, 2]}}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10305}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10306}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10307}}, {"notes": "设置客户端信息", "operation": 11068, "duration": 0, "jsonParam": {"infos": {"1": [44, 2, 23]}}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 230}}, {"notes": "派遣任务", "operation": 12903, "duration": 1, "jsonParam": {"id": 1008, "hids": [11992, 24992]}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10308}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10309}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10310}}, {"notes": "设置客户端信息", "operation": 11068, "duration": 0, "jsonParam": {"infos": {"1": [44, 2, 23, 40]}}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 240}}, {"notes": "获取爬塔信息", "operation": 12501, "duration": 0, "jsonParam": {}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [40]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 40, "selfHero": ["1_11992", "2_13980", "3_24992", "4_22981", "5_23982"]}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 3, "jsonParam": {"type": 1}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 260}}, {"notes": "英雄突破", "operation": 11905, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 270}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10311}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10312}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10313}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 271}}, {"notes": "领取嘉年华奖励", "operation": 14303, "duration": 1, "jsonParam": {"carnival_id": 1, "id": 1001003}}, {"notes": "领取嘉年华奖励", "operation": 14303, "duration": 1, "jsonParam": {"carnival_id": 1, "id": 1001010}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10314}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10315}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 274}}, {"notes": "777抽获取数据", "operation": 17201, "duration": 0, "jsonParam": {"id": 1}}, {"notes": "777抽领取抽卡券", "operation": 17203, "duration": 1, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽抽卡", "operation": 17205, "duration": 2, "jsonParam": {"id": 1}}, {"notes": "777抽选卡组", "operation": 17207, "duration": 1, "jsonParam": {"id": 1, "select_group_key": 1}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10401}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10402}}, {"notes": "材料本加速", "operation": 12811, "duration": 1, "jsonParam": {"type": 46}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 1}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 1}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 2}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 2}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 2}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 2}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 2}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 3}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 3}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 3}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 3}}, {"notes": "购买金币", "operation": 13203, "duration": 1, "jsonParam": {"sys_id": 3}}, {"notes": "获取主线战令", "operation": 15401, "duration": 0, "jsonParam": {"sys_id": [1]}}, {"notes": "领取主线战令", "operation": 15403, "duration": 0, "jsonParam": {"ids": [10001], "sys_id": 1}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 13980}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980, "num": 5}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 23982}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 23982}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 23982}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 23982}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 23982}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 23982}}, {"notes": "英雄突破", "operation": 11905, "duration": 1, "jsonParam": {"hid": 23982}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 22981}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 22981}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 24992}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10403}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10404}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10405}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 281}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 282}}, {"notes": "领取主线通关奖励", "operation": 12103, "duration": 1, "jsonParam": {"ids": [28006]}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10406}}, {"notes": "图鉴领取奖励", "operation": 14605, "duration": 1, "jsonParam": {"type": 1, "ids": [13980]}}, {"notes": "图鉴领取奖励", "operation": 14605, "duration": 1, "jsonParam": {"type": 1, "ids": [23982]}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 23982}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 23982}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 24992}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10407}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10408}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10409}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10410}}, {"notes": "设置客户端信息", "operation": 11068, "duration": 0, "jsonParam": {"infos": {"1": [44, 2, 23, 40, 32]}}}, {"notes": "拉取竞技场", "operation": 12401, "duration": 1, "jsonParam": {"show_top3": true}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 223}}, {"notes": "拉取竞技场", "operation": 12401, "duration": 1, "jsonParam": {"show_top3": true}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [34]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 34, "selfHero": ["1_11992", "2_13980", "3_24992", "4_22981", "5_23982"]}}, {"notes": "竞技场战斗", "operation": 12405, "duration": 3, "jsonParam": {}}, {"notes": "拉取竞技场", "operation": 12401, "duration": 1, "jsonParam": {"show_top3": true}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [35]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 35, "selfHero": ["1_11992", "2_13980", "3_24992", "4_22981", "5_23982"]}}, {"notes": "领取竞技场奖励", "operation": 12413, "duration": 1, "jsonParam": {"ids": [1]}}, {"notes": "刷新竞技场对手", "operation": 12403, "duration": 1, "jsonParam": {}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [34]}}, {"notes": "竞技场战斗", "operation": 12405, "duration": 3, "jsonParam": {}}, {"notes": "拉取竞技场", "operation": 12401, "duration": 1, "jsonParam": {"show_top3": true}}, {"notes": "领取主线通关奖励", "operation": 12103, "duration": 1, "jsonParam": {"ids": [28007]}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 22981}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 22981}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 11992}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "穿戴装备", "operation": 12303, "duration": 1, "jsonParam": {"type": 3, "hid": 24992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "装备转移", "operation": 12320, "duration": 1, "jsonParam": {"hid": 11992}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [40]}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 3, "jsonParam": {"type": 1}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [40]}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 3, "jsonParam": {"type": 1}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [40]}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 3, "jsonParam": {"type": 1}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [40]}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 3, "jsonParam": {"type": 1}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [40]}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 3, "jsonParam": {"type": 1}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [40]}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "爬塔战斗", "operation": 12503, "duration": 5, "jsonParam": {"type": 1}}, {"notes": "英雄突破", "operation": 11905, "duration": 1, "jsonParam": {"hid": 11992}}, {"notes": "英雄突破", "operation": 11905, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "英雄突破", "operation": 11905, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "英雄突破", "operation": 11905, "duration": 1, "jsonParam": {"hid": 23982}}, {"notes": "领取任务", "operation": 12103, "duration": 1, "jsonParam": {"ids": [10005, 10006, 10007, 10008, 10012]}}, {"notes": "领取任务", "operation": 12103, "duration": 1, "jsonParam": {"ids": [10000, 10001, 10002]}}, {"notes": "领取任务", "operation": 12103, "duration": 1, "jsonParam": {"ids": [12000, 15300, 19000, 19100, 44000, 58101]}}, {"notes": "领取任务", "operation": 12103, "duration": 1, "jsonParam": {"ids": [12001, 19001, 19101, 44001]}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 13980}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 22981}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 11992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 24992}}, {"notes": "强化装备", "operation": 12318, "duration": 1, "jsonParam": {"hid": 23992}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 22981}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 23982}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10411}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10412}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10413}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10414}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10415}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 300}}, {"notes": "先知抽卡", "operation": 12001, "duration": 1, "jsonParam": {"id": 5, "is_ten": false}}, {"notes": "领取主线通关奖励", "operation": 12103, "duration": 1, "jsonParam": {"ids": [28008]}}, {"notes": "获取布阵信息", "operation": 11031, "duration": 0, "jsonParam": {"formation_ids": [7]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10416}}]