<?xml version="1.0" encoding="utf-8"?>
<config>
  <server>
    <port></port> <!-- 监听的端口 -->
    <property name="MaxReadMsgSize">1310720</property>  <!-- 接收包的最大长度 -->
    <property name="ReadMsgQueueSize">0</property>  <!-- 接收包的缓存队列长度 -->
    <property name="ReadTimeOut">600</property>  <!-- 接收包的超时(second) -->
    <property name="MaxWriteMsgSize">1310720</property> <!-- 发送包的最大长度 -->
    <property name="WriteMsgQueueSize">1024</property> <!-- 发送包的缓存队列长度  -->
    <property name="WriteTimeOut">600</property>  <!-- 发送包的超时(second) -->
  </server>
  <actors>
    <actor>
      <!-- logic actor -->
      <kind>1</kind>
      <name>logic</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- redis actor -->
      <kind>2</kind>
      <name>redis</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>50</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- crossmaster actor -->
      <kind>3</kind>
      <name>crossmaster</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- module actor -->
      <kind>4</kind>
      <name>module</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>4096</timer_size>
    </actor>
    <actor>
      <!-- mongo actor -->
      <kind>7</kind>
      <name>mongo</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
  </actors>
  <etcd>
    <servers>192.168.0.89:2379</servers>
    <dial_timeout>10</dial_timeout>
    <request_timeout>10</request_timeout>
	  <cross_partition>/game/x/crosspartition/</cross_partition>
  </etcd>
  <!-- <ahead>
    <log_topic_resource>ahead-resource-ngame</log_topic_resource>
  </ahead> -->
</config>
