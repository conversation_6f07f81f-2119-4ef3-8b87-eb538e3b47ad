[{"notes": "TA-设备激活", "operation": 201, "duration": 0, "jsonParam": {}}, {"notes": "TA-资源版本核对", "operation": 202, "duration": 0, "jsonParam": {"event": "gameResReqBegin"}}, {"notes": "TA-资源解压", "operation": 202, "duration": 0, "jsonParam": {"event": "gameResDecBegin"}}, {"notes": "HTTP-获取网关列表", "operation": 101, "duration": 0, "jsonParam": {"url": "http://************:38421/alive/gateway"}}, {"notes": "TA-游戏请求服务器列表", "operation": 202, "duration": 0, "jsonParam": {"event": "gameGetServerListBegin"}}, {"notes": "HTTP-获取服务器列表", "operation": 102, "duration": 0, "jsonParam": {"url": "http://************:18084/serverlist/v4"}}, {"notes": "HTTP-获取公告", "operation": 103, "duration": 0, "jsonParam": {"url": "http://************:18090/notice/list/v5"}}, {"notes": "HTTP-SDK客户端初始化", "operation": 104, "duration": 0, "jsonParam": {"url": "https://sdkapi.omnidreamgames.com/user/init"}}, {"notes": "HTTP-SDK客户端登录", "operation": 105, "duration": 0, "jsonParam": {"url": "https://sdkapi.omnidreamgames.com/user/platformLogin"}}, {"notes": "HTTP-登录校验", "operation": 106, "duration": 0, "jsonParam": {"url": "http://************:8076/login"}}, {"notes": "HTTP-获取角色列表", "operation": 120, "duration": 0, "jsonParam": {"url": "http://************:40001/account"}}, {"notes": "登录", "operation": 10100, "duration": 1, "jsonParam": {}}, {"notes": "TA-开始播放CG", "operation": 202, "duration": 0, "jsonParam": {"event": "gameBeginCG"}}, {"notes": "玩家数据", "operation": 11003, "duration": 1, "jsonParam": {"User": true, "Bags": true, "Equips": true, "Artifacts": true, "Emblems": true, "GlobalAttr": true, "Heroes": true, "Formation": true, "Dungeon": true, "Tasks": true, "GuildTalent": true, "Guidance": true, "SevendayLogin": true, "ClientInfo": true, "SummonData": true, "Crystal": true, "PushGift": true}}, {"notes": "问卷信息", "operation": 11058, "duration": 0, "jsonParam": {}}, {"notes": "客户端设置语言", "operation": 11076, "duration": 0, "jsonParam": {"lang": "zh"}}, {"notes": "充值信息", "operation": 14901, "duration": 0, "jsonParam": {}}, {"notes": "获取月卡信息", "operation": 15301, "duration": 0, "jsonParam": {}}, {"notes": "获取限时礼包信息", "operation": 15201, "duration": 0, "jsonParam": {}}, {"notes": "商店信息", "operation": 11301, "duration": 0, "jsonParam": {}}, {"notes": "嘉年华信息", "operation": 14301, "duration": 0, "jsonParam": {}}, {"notes": "好友信息", "operation": 11501, "duration": 0, "jsonParam": {}}, {"notes": "图鉴信息", "operation": 14601, "duration": 0, "jsonParam": {}}, {"notes": "功勋信息", "operation": 14501, "duration": 0, "jsonParam": {}}, {"notes": "获取vip信息", "operation": 15101, "duration": 0, "jsonParam": {}}, {"notes": "获取新功能开启信息", "operation": 14801, "duration": 0, "jsonParam": {}}, {"notes": "获取战令信息", "operation": 15401, "duration": 0, "jsonParam": {"sys_id": [1, 2]}}, {"notes": "红点信息", "operation": 11055, "duration": 1, "jsonParam": {"id_list": [[10941, 10942], [10942]]}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": [], "selfHero": ["4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10101}}, {"notes": "TA-引导打点", "operation": 203, "duration": 0, "jsonParam": {"guideid": "2004"}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 20, "step": 2004}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 20}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["1_101020"], "selfHero": ["4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10102}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 21, "step": 2103}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 21}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["3_101040"], "selfHero": ["1_11992", "4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10103}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 50, "step": 5003}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 50}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": [], "selfHero": ["1_11992", "3_24992", "4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10104}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 60, "step": 6004}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 60}}, {"notes": "TA-引导打点", "operation": 203, "duration": 0, "jsonParam": {"guideid": "7101"}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 71, "step": 7101}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 71}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10105}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 80, "step": 8001}}, {"notes": "挂机预览", "operation": 11807, "duration": 1, "jsonParam": {}}, {"notes": "主线挂机奖励", "operation": 11803, "duration": 1, "jsonParam": {}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 80, "step": 8003}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980, "num": 1}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 80, "step": 8006}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 80}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 24992, "num": 1}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 81}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 90}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10106}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 100}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10107}}, {"notes": "商店信息", "operation": 11301, "duration": 0, "jsonParam": {}}, {"notes": "点金信息", "operation": 13201, "duration": 0, "jsonParam": {}}, {"notes": "设置客户端信息", "operation": 11068, "duration": 0, "jsonParam": {"infos": {"1": [10]}}}, {"notes": "召唤", "operation": 12001, "duration": 1, "jsonParam": {"id": 3, "plural": true}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 110}}, {"notes": "获取评分状态", "operation": 15601, "duration": 0, "jsonParam": {}}, {"notes": "评分", "operation": 15603, "duration": 0, "jsonParam": {"score": 5}}, {"notes": "召唤", "operation": 12001, "duration": 1, "jsonParam": {"id": 3}}, {"notes": "召唤", "operation": 12001, "duration": 1, "jsonParam": {"id": 1}}, {"notes": "TA-引导打点", "operation": 203, "duration": 0, "jsonParam": {"guideid": "12001"}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 120, "step": 12001}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 120}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 12005, "num": 1}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 12005, "num": 8}}, {"notes": "设置名字", "operation": 11049, "duration": 1, "jsonParam": {}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 140, "step": 14002}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 140}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 150}}, {"notes": "TA-引导打点", "operation": 203, "duration": 0, "jsonParam": {"guideid": "16001"}}, {"notes": "新手引导-完成节点", "operation": 14203, "duration": 1, "jsonParam": {"group": 160, "step": 16001}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 160}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": [], "selfHero": ["1_11992", "3_24992", "4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10201}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": [], "selfHero": ["1_11992", "3_24992", "4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10202}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["2_102030"], "selfHero": ["1_11992", "3_24992", "4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10203}}, {"notes": "新手引导-完成组", "operation": 14205, "duration": 1, "jsonParam": {"group": 161}}, {"notes": "布阵", "operation": 11029, "duration": 1, "jsonParam": {"FuncId": 7, "helpHero": ["2_102040"], "selfHero": ["1_11992", "3_12005", "4_13980"]}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10204}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 12005, "num": 2}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 13980, "num": 10}}, {"notes": "英雄升级", "operation": 11903, "duration": 1, "jsonParam": {"id": 11992, "num": 5}}, {"notes": "主线关卡", "operation": 11801, "duration": 3, "jsonParam": {"sys_id": 10205}}, {"notes": "HTTP-钻石充值", "operation": 130, "duration": 1, "jsonParam": {"payNotifyUrl": "http://106.75.232.161:8095/recharge/forward/v1", "orderCreateUrl": "https://sdkapi.omnidreamgames.com/order/create", "googleCallbackUrl": "https://sdkapi.omnidreamgames.com/order/complete/google", "apstoreCallbackUrl": "https://sdkapi.omnidreamgames.com/order/complete/appstore", "price": 99, "rechargeType": 0, "id": 1, "internalId": 10006, "productID": "com.omnidream.60", "sdkPid": "diamond_1_006", "opGroup": 104, "op": 5}}]