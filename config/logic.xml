<?xml version="1.0" encoding="utf-8"?>
<config>
  <clients>
    <client>
      <server>gateway</server>
      <property name="MaxReadMsgSize">65536</property>  <!-- 接收包的最大长度 -->
      <property name="ReadMsgQueueSize">0</property>  <!-- 接收包的缓存队列长度 -->
      <property name="ReadTimeOut">600</property>  <!-- 接收包的超时(second) -->
      <property name="MaxWriteMsgSize">131072</property> <!-- 发送包的最大长度 -->
      <property name="WriteMsgQueueSize">1024</property> <!-- 发送包的缓存队列长度  -->
      <property name="WriteTimeOut">600</property>  <!-- 发送包的超时(second) -->
    </client>
    <client>
      <server>cross</server>
      <property name="MaxReadMsgSize">1310720</property>  <!-- 接收包的最大长度 -->
      <property name="ReadMsgQueueSize">0</property>  <!-- 接收包的缓存队列长度 -->
      <property name="ReadTimeOut">600</property>  <!-- 接收包的超时(second) -->
      <property name="MaxWriteMsgSize">1310720</property> <!-- 发送包的最大长度 -->
      <property name="WriteMsgQueueSize">1024</property> <!-- 发送包的缓存队列长度  -->
      <property name="WriteTimeOut">600</property>  <!-- 发送包的超时(second) -->
    </client>
	<client>
      <server>crossmaster</server>
      <property name="MaxReadMsgSize">131072</property>  <!-- 接收包的最大长度 -->
      <property name="ReadMsgQueueSize">0</property>  <!-- 接收包的缓存队列长度 -->
      <property name="ReadTimeOut">600</property>  <!-- 接收包的超时(second) -->
      <property name="MaxWriteMsgSize">131072</property> <!-- 发送包的最大长度 -->
      <property name="WriteMsgQueueSize">1024</property> <!-- 发送包的缓存队列长度  -->
      <property name="WriteTimeOut">600</property>  <!-- 发送包的超时(second) -->
    </client>
  </clients>
  <actors>
    <actor>
      <!-- gateway actor -->
      <kind>1</kind>
      <name>gateway</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- redis actor -->
      <kind>2</kind>
      <name>redis</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>50</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- platform http actor -->
      <kind>3</kind>
      <name>platform</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- account actor -->
      <kind>4</kind>
      <name>account</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- cross actor -->
      <kind>5</kind>
      <name>cross</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- oss actor -->
      <kind>6</kind>
      <name>oss</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
    <actor>
      <!-- mongo actor -->
      <kind>7</kind>
      <name>mongo</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>
	<actor>
      <!-- cross master actor -->
      <kind>8</kind>
      <name>cross_master</name>
      <msg_queue_size>10000</msg_queue_size>
      <rate>500</rate>
      <timer_size>1024</timer_size>
    </actor>

  </actors>
  <etcd>
    <servers>192.168.0.89:2379</servers>
    <dial_timeout>10</dial_timeout>
    <request_timeout>10</request_timeout>
    <logic>/game/x/logic/node/</logic>
	<cross_activity>/game/x/cross/newnode/</cross_activity>
	<cross_match_id>4000010</cross_match_id>
  </etcd>
</config>
