[{"version": "0.3.0", "scope": "node", "name": "<PERSON><PERSON>", "category": "action", "title": "<PERSON><PERSON>", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GetServerList", "category": "action", "title": "GetServerList", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "Flush", "category": "action", "title": "Flush", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsSeasonDungeonEnough", "category": "condition", "title": "IsSeasonDungeonEnough", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeGetData", "category": "action", "title": "TalentTreeGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeLevelUp", "category": "action", "title": "TalentTreeLevelUp", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeReset", "category": "action", "title": "TalentTreeReset", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeReceiveTaskAwards", "category": "action", "title": "TalentTreeReceiveTaskAwards", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeHot", "category": "action", "title": "TalentTreeHot", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonGetData", "category": "action", "title": "SeasonDungeonGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonFight", "category": "action", "title": "SeasonDungeonFight", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeIsHaveTaskAwards", "category": "condition", "title": "TalentTreeIsHaveTaskAwards", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "RepeatDuration", "category": "decorator", "title": "RepeatDuration", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GmTestData", "category": "action", "title": "GmTestData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonFormation", "category": "action", "title": "SeasonDungeonFormation", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "MemRandom", "category": "composite", "title": "MemRandom", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsReconnect", "category": "condition", "title": "IsReconnect", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "Reconnect", "category": "action", "title": "Reconnect", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "DungeonFight", "category": "action", "title": "DungeonFight", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "UserFormation", "category": "action", "title": "UserFormation", "description": null, "properties": {"formation_id": 0}}, {"version": "0.3.0", "scope": "node", "name": "Timer", "category": "decorator", "title": "Timer", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GoldBuyGet", "category": "action", "title": "GoldBuyGet", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GoldBuyGetGold", "category": "action", "title": "GoldBuyGetGold", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonLinkGetData", "category": "action", "title": "SeasonLinkGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonLinkActivate", "category": "action", "title": "SeasonLinkActivate", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "EmblemWear", "category": "action", "title": null, "description": "穿纹章", "properties": {}, "parent": null}, {"version": "0.3.0", "scope": "node", "name": "EmblemLevelUp", "category": "action", "title": null, "description": "纹章升级", "properties": {}, "parent": null}, {"version": "0.3.0", "scope": "node", "name": "EmblemDecompose", "category": "action", "title": null, "description": "纹章分解", "properties": {}, "parent": null}]