{"name": "omini", "description": "", "data": {"version": "0.3.0", "scope": "project", "selectedTree": "74ee69d5-357a-4f9a-85a6-f37521fb64c4", "trees": [{"version": "0.3.0", "scope": "tree", "id": "98df9d87-1b2d-45a9-8522-25ea53fc8ffb", "title": "A behavior tree", "description": "", "root": "35353205-a206-481d-95b9-e7e5eac6b9bb", "properties": {}, "nodes": {"35353205-a206-481d-95b9-e7e5eac6b9bb": {"id": "35353205-a206-481d-95b9-e7e5eac6b9bb", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -672, "y": -168}, "children": ["0b618b88-d202-48e1-8bd0-3f72b2d54dfa", "2116d52c-3e17-4f68-8b3e-9848d5373d49"]}, "1bda2bc1-824f-4441-8ab3-43a175ad5e4a": {"id": "1bda2bc1-824f-4441-8ab3-43a175ad5e4a", "name": "1b5c8087-c815-4c1b-854c-049103ce400b", "category": "tree", "title": "TalentTree", "description": "", "properties": {}, "display": {"x": 348, "y": 12}}, "2116d52c-3e17-4f68-8b3e-9848d5373d49": {"id": "2116d52c-3e17-4f68-8b3e-9848d5373d49", "name": "RepeatDuration", "category": "decorator", "title": "RepeatDuration <duration>ms", "description": "", "properties": {"duration": -1}, "display": {"x": -468, "y": 36}, "child": "09e71e62-a45d-4854-84ac-1c6ba2eb9886"}, "cef24e5e-1673-4684-8d2d-2f75d836c8e5": {"id": "cef24e5e-1673-4684-8d2d-2f75d836c8e5", "name": "MemRandom", "category": "composite", "title": "MemRandom", "description": "", "properties": {}, "display": {"x": 96, "y": 12}, "children": ["1bda2bc1-824f-4441-8ab3-43a175ad5e4a", "1f7529e6-44b1-4940-812d-c274dea651fb", "700565cd-d9c4-4ab4-bbb5-be028b918c6e", "f1a5b856-1cc6-49fd-b26b-dc4b928ac871", "1e6e2834-a8fe-4c90-903d-8cd6f2c256e8"]}, "0b618b88-d202-48e1-8bd0-3f72b2d54dfa": {"id": "0b618b88-d202-48e1-8bd0-3f72b2d54dfa", "name": "189911a4-1e0a-4789-90eb-6e18493798ca", "category": "tree", "title": "Base", "description": "", "properties": {}, "display": {"x": -396, "y": -180}}, "8a2a90fa-164f-4d49-817b-1f35222429a6": {"id": "8a2a90fa-164f-4d49-817b-1f35222429a6", "name": "IsReconnect", "category": "condition", "title": "IsReconnect <weight>weight", "description": "", "properties": {"weight": -1}, "display": {"x": 228, "y": -216}}, "ccc38db1-b379-4529-9173-f1ecc93ef04e": {"id": "ccc38db1-b379-4529-9173-f1ecc93ef04e", "name": "189911a4-1e0a-4789-90eb-6e18493798ca", "category": "tree", "title": "Base", "description": "", "properties": {}, "display": {"x": 216, "y": -72}}, "e7783cfe-cc8f-4944-aab4-1e473015ec19": {"id": "e7783cfe-cc8f-4944-aab4-1e473015ec19", "name": "Reconnect", "category": "action", "title": "Reconnect", "description": "", "properties": {}, "display": {"x": 216, "y": -132}}, "5384f785-327f-4725-a070-ca1e290085bb": {"id": "5384f785-327f-4725-a070-ca1e290085bb", "name": "MemPriority", "category": "composite", "title": "MemPriority", "description": "", "properties": {}, "display": {"x": -72, "y": -132}, "children": ["0c1e474a-73e5-452b-8e7d-7e23aceb07b0", "02780749-4847-4512-8f23-c5f1dc739506"]}, "0c1e474a-73e5-452b-8e7d-7e23aceb07b0": {"id": "0c1e474a-73e5-452b-8e7d-7e23aceb07b0", "name": "Inverter", "category": "decorator", "title": "Inverter", "description": "", "properties": {}, "display": {"x": 36, "y": -216}, "child": "8a2a90fa-164f-4d49-817b-1f35222429a6"}, "02780749-4847-4512-8f23-c5f1dc739506": {"id": "02780749-4847-4512-8f23-c5f1dc739506", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": 36, "y": -132}, "children": ["e7783cfe-cc8f-4944-aab4-1e473015ec19", "ccc38db1-b379-4529-9173-f1ecc93ef04e"]}, "1f7529e6-44b1-4940-812d-c274dea651fb": {"id": "1f7529e6-44b1-4940-812d-c274dea651fb", "name": "658eecdd-4a65-4b5b-834f-6a1ba5136bfa", "category": "tree", "title": "dungeon", "description": "", "properties": {}, "display": {"x": 360, "y": 96}}, "09e71e62-a45d-4854-84ac-1c6ba2eb9886": {"id": "09e71e62-a45d-4854-84ac-1c6ba2eb9886", "name": "Timer", "category": "decorator", "title": "Timer", "description": "", "properties": {"duration": 1000}, "display": {"x": -240, "y": 24}, "child": "b5d863bf-90d0-4487-8ca7-4392a23a0a1d"}, "700565cd-d9c4-4ab4-bbb5-be028b918c6e": {"id": "700565cd-d9c4-4ab4-bbb5-be028b918c6e", "name": "d705e1dd-356d-4d71-8f69-3f41d93bceee", "category": "tree", "title": "GoldBuy", "description": "", "properties": {}, "display": {"x": 360, "y": 192}}, "f1a5b856-1cc6-49fd-b26b-dc4b928ac871": {"id": "f1a5b856-1cc6-49fd-b26b-dc4b928ac871", "name": "41ffe798-c550-47e4-bfdd-af0565f620aa", "category": "tree", "title": "SeasonLink", "description": "", "properties": {}, "display": {"x": 360, "y": 276}}, "b5d863bf-90d0-4487-8ca7-4392a23a0a1d": {"id": "b5d863bf-90d0-4487-8ca7-4392a23a0a1d", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -120, "y": 12}, "children": ["5384f785-327f-4725-a070-ca1e290085bb", "cef24e5e-1673-4684-8d2d-2f75d836c8e5"]}, "1e6e2834-a8fe-4c90-903d-8cd6f2c256e8": {"id": "1e6e2834-a8fe-4c90-903d-8cd6f2c256e8", "name": "74ee69d5-357a-4f9a-85a6-f37521fb64c4", "category": "tree", "title": "emblem", "description": "", "properties": {}, "display": {"x": 360, "y": 348}}}, "display": {"camera_x": 1127, "camera_y": 669.5, "camera_z": 1, "x": -768, "y": -168}}, {"version": "0.3.0", "scope": "tree", "id": "1b5c8087-c815-4c1b-854c-049103ce400b", "title": "TalentTree", "description": "", "root": "d44bf0f9-d359-46ab-8589-e5b18cc14788", "properties": {}, "nodes": {"32168206-6b91-4b25-b43f-4a07f8d2d4a8": {"id": "32168206-6b91-4b25-b43f-4a07f8d2d4a8", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": "", "properties": {"res1": "4-10141-10000;4-10142-10000;4-10143-10000", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 96, "y": -720}}, "0e62a447-453c-4b27-9187-e946da361b4e": {"id": "0e62a447-453c-4b27-9187-e946da361b4e", "name": "MemPriority", "category": "composite", "title": "MemPriority", "description": "", "properties": {}, "display": {"x": -336, "y": -648}, "children": ["32168206-6b91-4b25-b43f-4a07f8d2d4a8", "4b80cb2e-62df-4d39-b230-fa75be041ebf"]}, "4b80cb2e-62df-4d39-b230-fa75be041ebf": {"id": "4b80cb2e-62df-4d39-b230-fa75be041ebf", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": "资源配置格式 type-value-count;type-value-count", "properties": {"res1": "4-10141-10000;4-10142-10000;4-10143-10000", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 108, "y": -612}}, "7a8ed9e0-fa96-4c71-ab9e-63282d2a22ec": {"id": "7a8ed9e0-fa96-4c71-ab9e-63282d2a22ec", "name": "MemPriority", "category": "composite", "title": "MemPriority", "description": "", "properties": {}, "display": {"x": -360, "y": -336}, "children": ["51f3ae53-9186-49f7-bbf0-c068fb843707", "bc4d37f5-e685-470e-8256-fdb8dc5cb713", "a0f7deae-a4ec-4fae-a4c3-bf8728cddd12"]}, "51f3ae53-9186-49f7-bbf0-c068fb843707": {"id": "51f3ae53-9186-49f7-bbf0-c068fb843707", "name": "IsSeasonDungeonEnough", "category": "condition", "title": "IsSeasonDungeonEnough", "description": "", "properties": {"targetID": 80105}, "display": {"x": 132, "y": -504}}, "a0f7deae-a4ec-4fae-a4c3-bf8728cddd12": {"id": "a0f7deae-a4ec-4fae-a4c3-bf8728cddd12", "name": "SeasonDungeonFight", "category": "action", "title": "SeasonDungeonFight", "description": "", "properties": {"target": 80105}, "display": {"x": 228, "y": -216}}, "b4317518-6b71-4804-911c-5a31d32c7d75": {"id": "b4317518-6b71-4804-911c-5a31d32c7d75", "name": "TalentTreeGetData", "category": "action", "title": "TalentTreeGetData", "description": "", "properties": {}, "display": {"x": -108, "y": -156}}, "e46ce33f-e664-400c-8071-de0db87623fc": {"id": "e46ce33f-e664-400c-8071-de0db87623fc", "name": "TalentTreeLevelUp", "category": "action", "title": "TalentTreeLevelUp", "description": "", "properties": {}, "display": {"x": -72, "y": -72}}, "37e1b0b4-04f5-4ce8-b890-23f30c4cb320": {"id": "37e1b0b4-04f5-4ce8-b890-23f30c4cb320", "name": "TalentTreeReceiveTaskAwards", "category": "action", "title": "TalentTreeReceiveTaskAwards", "description": "", "properties": {}, "display": {"x": 228, "y": 156}}, "fdc48755-82e4-4a5d-b54c-e8fe765f7f0a": {"id": "fdc48755-82e4-4a5d-b54c-e8fe765f7f0a", "name": "TalentTreeReset", "category": "action", "title": "TalentTreeReset", "description": "", "properties": {}, "display": {"x": -276, "y": 240}}, "c452b86e-3a8f-43f6-84ec-344c61b49c62": {"id": "c452b86e-3a8f-43f6-84ec-344c61b49c62", "name": "TalentTreeHot", "category": "action", "title": "TalentTreeHot", "description": "", "properties": {}, "display": {"x": -288, "y": 360}}, "5e9847b0-fd69-414b-8abb-378de806e63c": {"id": "5e9847b0-fd69-414b-8abb-378de806e63c", "name": "MemPriority", "category": "composite", "title": "MemPriority", "description": "", "properties": {}, "display": {"x": -396, "y": 12}, "children": ["f06a3c97-2656-4867-8b62-ca40b53c4b5d", "37e1b0b4-04f5-4ce8-b890-23f30c4cb320"]}, "f06a3c97-2656-4867-8b62-ca40b53c4b5d": {"id": "f06a3c97-2656-4867-8b62-ca40b53c4b5d", "name": "Inverter", "category": "decorator", "title": "Inverter", "description": "", "properties": {}, "display": {"x": -84, "y": 12}, "child": "241f5bcc-d4c2-4b07-9cad-c9c8ae4b0213"}, "241f5bcc-d4c2-4b07-9cad-c9c8ae4b0213": {"id": "241f5bcc-d4c2-4b07-9cad-c9c8ae4b0213", "name": "TalentTreeIsHaveTaskAwards", "category": "condition", "title": "TalentTreeIsHaveTaskAwards", "description": "", "properties": {}, "display": {"x": 252, "y": 0}}, "76e138e1-9f58-449a-b0c6-86b6cb5da0d9": {"id": "76e138e1-9f58-449a-b0c6-86b6cb5da0d9", "name": "SeasonDungeonFormation", "category": "action", "title": "SeasonDungeonFormation", "description": "", "properties": {}, "display": {"x": 252, "y": -336}}, "bc4d37f5-e685-470e-8256-fdb8dc5cb713": {"id": "bc4d37f5-e685-470e-8256-fdb8dc5cb713", "name": "Inverter", "category": "decorator", "title": "Inverter", "description": "", "properties": {}, "display": {"x": -108, "y": -336}, "child": "76e138e1-9f58-449a-b0c6-86b6cb5da0d9"}, "d44bf0f9-d359-46ab-8589-e5b18cc14788": {"id": "d44bf0f9-d359-46ab-8589-e5b18cc14788", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -780, "y": -72}, "children": ["0e62a447-453c-4b27-9187-e946da361b4e", "7a8ed9e0-fa96-4c71-ab9e-63282d2a22ec", "b4317518-6b71-4804-911c-5a31d32c7d75", "e46ce33f-e664-400c-8071-de0db87623fc", "5e9847b0-fd69-414b-8abb-378de806e63c", "fdc48755-82e4-4a5d-b54c-e8fe765f7f0a", "c452b86e-3a8f-43f6-84ec-344c61b49c62"]}}, "display": {"camera_x": 1127, "camera_y": 669.5, "camera_z": 0.75, "x": -948, "y": -72}}, {"version": "0.3.0", "scope": "tree", "id": "189911a4-1e0a-4789-90eb-6e18493798ca", "title": "Base", "description": "", "root": "250e0c8a-044a-4b50-8214-f61caeac4b00", "properties": {}, "nodes": {"250e0c8a-044a-4b50-8214-f61caeac4b00": {"id": "250e0c8a-044a-4b50-8214-f61caeac4b00", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -480, "y": -24}, "children": ["9a72e34b-44cb-478d-85df-c5f139fd4994", "f6e753b9-caac-4805-830f-95be536a9ff6", "0b295f56-03fb-4f9f-88eb-ddbe1a8d8b3b", "ac5e0ef6-ddb4-45b5-b2e1-ebc0bcc3e6c4", "7f9313b4-3ef5-4ae5-8ed4-de869f71d0c4"]}, "9a72e34b-44cb-478d-85df-c5f139fd4994": {"id": "9a72e34b-44cb-478d-85df-c5f139fd4994", "name": "GetServerList", "category": "action", "title": "GetServerList", "description": "", "properties": {}, "display": {"x": -108, "y": -180}}, "f6e753b9-caac-4805-830f-95be536a9ff6": {"id": "f6e753b9-caac-4805-830f-95be536a9ff6", "name": "<PERSON><PERSON>", "category": "action", "title": "<PERSON><PERSON>", "description": "", "properties": {}, "display": {"x": -108, "y": -108}}, "0b295f56-03fb-4f9f-88eb-ddbe1a8d8b3b": {"id": "0b295f56-03fb-4f9f-88eb-ddbe1a8d8b3b", "name": "GmTestData", "category": "action", "title": "GmTestData", "description": "", "properties": {"stage": 90}, "display": {"x": -108, "y": -24}}, "ac5e0ef6-ddb4-45b5-b2e1-ebc0bcc3e6c4": {"id": "ac5e0ef6-ddb4-45b5-b2e1-ebc0bcc3e6c4", "name": "Flush", "category": "action", "title": "Flush", "description": "", "properties": {}, "display": {"x": -108, "y": 60}}, "7f9313b4-3ef5-4ae5-8ed4-de869f71d0c4": {"id": "7f9313b4-3ef5-4ae5-8ed4-de869f71d0c4", "name": "SeasonDungeonGetData", "category": "action", "title": "SeasonDungeonGetData", "description": "", "properties": {}, "display": {"x": -84, "y": 144}}}, "display": {"camera_x": 1280, "camera_y": 688.5, "camera_z": 1, "x": -612, "y": -24}}, {"version": "0.3.0", "scope": "tree", "id": "658eecdd-4a65-4b5b-834f-6a1ba5136bfa", "title": "dungeon", "description": "主线副本", "root": "0fc0d0b7-f7dd-4804-a5c7-080114889474", "properties": {}, "nodes": {"95c55d29-6c27-4148-812d-d6c790d815a8": {"id": "95c55d29-6c27-4148-812d-d6c790d815a8", "name": "DungeonFight", "category": "action", "title": "DungeonFight", "description": "", "properties": {}, "display": {"x": 108, "y": 0}}, "0fc0d0b7-f7dd-4804-a5c7-080114889474": {"id": "0fc0d0b7-f7dd-4804-a5c7-080114889474", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -144, "y": -36}, "children": ["a5d024c5-a3c9-447e-86c5-c4bb913ba3e0", "95c55d29-6c27-4148-812d-d6c790d815a8"]}, "a5d024c5-a3c9-447e-86c5-c4bb913ba3e0": {"id": "a5d024c5-a3c9-447e-86c5-c4bb913ba3e0", "name": "UserFormation", "category": "action", "title": "UserFormation", "description": "", "properties": {"formation_id": 7}, "display": {"x": 96, "y": -120}}}, "display": {"camera_x": 960, "camera_y": 508.5, "camera_z": 1, "x": -276, "y": -12}}, {"version": "0.3.0", "scope": "tree", "id": "d705e1dd-356d-4d71-8f69-3f41d93bceee", "title": "GoldBuy", "description": "", "root": "e95eef7c-3e76-4e32-bc4f-4ac967d22421", "properties": {}, "nodes": {"e95eef7c-3e76-4e32-bc4f-4ac967d22421": {"id": "e95eef7c-3e76-4e32-bc4f-4ac967d22421", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -624, "y": -84}, "children": ["a4e179a7-806d-4764-bc02-63941432ec3e", "abffd531-6965-485c-84ad-2f2006885d2c", "b2d34a9a-b878-4497-a138-e31324a7c52d"]}, "abffd531-6965-485c-84ad-2f2006885d2c": {"id": "abffd531-6965-485c-84ad-2f2006885d2c", "name": "GoldBuyGet", "category": "action", "title": "GoldBuyGet", "description": "", "properties": {}, "display": {"x": -264, "y": -144}}, "b2d34a9a-b878-4497-a138-e31324a7c52d": {"id": "b2d34a9a-b878-4497-a138-e31324a7c52d", "name": "GoldBuyGetGold", "category": "action", "title": "GoldBuyGetGold", "description": "", "properties": {}, "display": {"x": -264, "y": -24}}, "a4e179a7-806d-4764-bc02-63941432ec3e": {"id": "a4e179a7-806d-4764-bc02-63941432ec3e", "name": "MemPriority", "category": "composite", "title": "MemPriority", "description": "", "properties": {}, "display": {"x": -360, "y": -288}, "children": ["21add9be-4438-4311-8fe5-60c714ef52c0", "8392b8cb-39db-4786-882d-b3edd9ce277e"]}, "21add9be-4438-4311-8fe5-60c714ef52c0": {"id": "21add9be-4438-4311-8fe5-60c714ef52c0", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": "", "properties": {"res1": "2-0-50", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": -36, "y": -384}}, "8392b8cb-39db-4786-882d-b3edd9ce277e": {"id": "8392b8cb-39db-4786-882d-b3edd9ce277e", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": "", "properties": {"res1": "2-0-50", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": -36, "y": -264}}}, "display": {"camera_x": 1280, "camera_y": 670.5, "camera_z": 1, "x": -780, "y": -84}}, {"version": "0.3.0", "scope": "tree", "id": "41ffe798-c550-47e4-bfdd-af0565f620aa", "title": "SeasonLink", "description": "", "root": "61b6c74a-cc64-48cd-a4ed-9dd1eb82a07b", "properties": {}, "nodes": {"f2b4d9e2-8fff-4c4a-838c-4d3665a6f95e": {"id": "f2b4d9e2-8fff-4c4a-838c-4d3665a6f95e", "name": "SeasonLinkGetData", "category": "action", "title": "SeasonLinkGetData", "description": "", "properties": {}, "display": {"x": 456, "y": 96}}, "22cab35c-1efe-4ee9-90b3-d993fd373b51": {"id": "22cab35c-1efe-4ee9-90b3-d993fd373b51", "name": "SeasonLinkActivate", "category": "action", "title": "SeasonLinkActivate", "description": "", "properties": {}, "display": {"x": 456, "y": 216}}, "61b6c74a-cc64-48cd-a4ed-9dd1eb82a07b": {"id": "61b6c74a-cc64-48cd-a4ed-9dd1eb82a07b", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": 60, "y": -12}, "children": ["b3a1be92-85fc-4b0e-b7b1-4e0052b862aa", "f2b4d9e2-8fff-4c4a-838c-4d3665a6f95e", "22cab35c-1efe-4ee9-90b3-d993fd373b51"]}, "b3a1be92-85fc-4b0e-b7b1-4e0052b862aa": {"id": "b3a1be92-85fc-4b0e-b7b1-4e0052b862aa", "name": "MemPriority", "category": "composite", "title": "MemPriority", "description": "", "properties": {}, "display": {"x": 216, "y": -84}, "children": ["07c99907-062c-44c0-8653-c23641492cba", "fb1e88c1-6818-449b-aecf-afc73aba7a7d"]}, "07c99907-062c-44c0-8653-c23641492cba": {"id": "07c99907-062c-44c0-8653-c23641492cba", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": "", "properties": {"res1": "4-190007-1", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 456, "y": -144}}, "fb1e88c1-6818-449b-aecf-afc73aba7a7d": {"id": "fb1e88c1-6818-449b-aecf-afc73aba7a7d", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": "", "properties": {"res1": "4-190007-1", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 444, "y": -24}}}, "display": {"camera_x": 1122, "camera_y": 594.5, "camera_z": 1, "x": -84, "y": -12}}, {"version": "0.3.0", "scope": "tree", "id": "74ee69d5-357a-4f9a-85a6-f37521fb64c4", "title": "emblem", "description": "纹章", "root": "7a8fe223-17bf-4844-869d-c062b0d32f4b", "properties": {}, "nodes": {"7a8fe223-17bf-4844-869d-c062b0d32f4b": {"id": "7a8fe223-17bf-4844-869d-c062b0d32f4b", "name": "MemRandom", "category": "composite", "title": "MemRandom", "description": "", "properties": {}, "display": {"x": -528, "y": -12}, "children": ["f0c8532c-8b8e-49d1-a3f3-75fdfd7d6980", "c95089b8-dc2f-41fc-a769-330cd7ac5db8", "46897a3c-e14e-47b5-95ca-496a3ca5a6be"]}, "c95089b8-dc2f-41fc-a769-330cd7ac5db8": {"id": "c95089b8-dc2f-41fc-a769-330cd7ac5db8", "name": "EmblemDecompose", "category": "action", "title": "EmblemDecompose", "description": "纹章分解", "properties": {}, "display": {"x": -252, "y": -12}}, "e6c6afbb-afa5-4fbb-836c-f853f1353fc9": {"id": "e6c6afbb-afa5-4fbb-836c-f853f1353fc9", "name": "EmblemLevelUp", "category": "action", "title": "EmblemLevelUp", "description": "纹章升级", "properties": {}, "display": {"x": 168, "y": -180}}, "46897a3c-e14e-47b5-95ca-496a3ca5a6be": {"id": "46897a3c-e14e-47b5-95ca-496a3ca5a6be", "name": "EmblemWear", "category": "action", "title": "EmblemWear", "description": "穿纹章", "properties": {}, "display": {"x": -252, "y": 276}}, "f0c8532c-8b8e-49d1-a3f3-75fdfd7d6980": {"id": "f0c8532c-8b8e-49d1-a3f3-75fdfd7d6980", "name": "Sequence", "category": "composite", "title": "Sequence", "description": "", "properties": {}, "display": {"x": -276, "y": -288}, "children": ["b0f5a9ca-fa40-4dcd-89ec-55edffb7fd9a", "e6c6afbb-afa5-4fbb-836c-f853f1353fc9"]}, "b0f5a9ca-fa40-4dcd-89ec-55edffb7fd9a": {"id": "b0f5a9ca-fa40-4dcd-89ec-55edffb7fd9a", "name": "Priority", "category": "composite", "title": "Priority", "description": "", "properties": {}, "display": {"x": 84, "y": -324}, "children": ["c2b11bb5-7340-4409-98aa-becf58c897c8", "302814ab-aedf-4174-9d2c-260dde8708a5"]}, "c2b11bb5-7340-4409-98aa-becf58c897c8": {"id": "c2b11bb5-7340-4409-98aa-becf58c897c8", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": "", "properties": {"res1": "4-10012-5000", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 288, "y": -372}}, "302814ab-aedf-4174-9d2c-260dde8708a5": {"id": "302814ab-aedf-4174-9d2c-260dde8708a5", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": "", "properties": {"res1": "4-10012-5000", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 276, "y": -300}}}, "display": {"camera_x": 1280, "camera_y": 684.5, "camera_z": 1, "x": -696, "y": -12}}], "custom_nodes": [{"version": "0.3.0", "scope": "node", "name": "<PERSON><PERSON>", "category": "action", "title": "<PERSON><PERSON>", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GetServerList", "category": "action", "title": "GetServerList", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "Flush", "category": "action", "title": "Flush", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsSeasonDungeonEnough", "category": "condition", "title": "IsSeasonDungeonEnough", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeGetData", "category": "action", "title": "TalentTreeGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeLevelUp", "category": "action", "title": "TalentTreeLevelUp", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeReset", "category": "action", "title": "TalentTreeReset", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeReceiveTaskAwards", "category": "action", "title": "TalentTreeReceiveTaskAwards", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeHot", "category": "action", "title": "TalentTreeHot", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonGetData", "category": "action", "title": "SeasonDungeonGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonFight", "category": "action", "title": "SeasonDungeonFight", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeIsHaveTaskAwards", "category": "condition", "title": "TalentTreeIsHaveTaskAwards", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "RepeatDuration", "category": "decorator", "title": "RepeatDuration", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GmTestData", "category": "action", "title": "GmTestData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonFormation", "category": "action", "title": "SeasonDungeonFormation", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "MemRandom", "category": "composite", "title": "MemRandom", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsReconnect", "category": "condition", "title": "IsReconnect", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "Reconnect", "category": "action", "title": "Reconnect", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "DungeonFight", "category": "action", "title": "DungeonFight", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "UserFormation", "category": "action", "title": "UserFormation", "description": null, "properties": {"formation_id": 0}}, {"version": "0.3.0", "scope": "node", "name": "Timer", "category": "decorator", "title": "Timer", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GoldBuyGet", "category": "action", "title": "GoldBuyGet", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GoldBuyGetGold", "category": "action", "title": "GoldBuyGetGold", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonLinkGetData", "category": "action", "title": "SeasonLinkGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonLinkActivate", "category": "action", "title": "SeasonLinkActivate", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "EmblemWear", "category": "action", "title": null, "description": "穿纹章", "properties": {}, "parent": null}, {"version": "0.3.0", "scope": "node", "name": "EmblemLevelUp", "category": "action", "title": null, "description": "纹章升级", "properties": {}, "parent": null}, {"version": "0.3.0", "scope": "node", "name": "EmblemDecompose", "category": "action", "title": null, "description": "纹章分解", "properties": {}, "parent": null}], "custom_folders": []}, "path": "D:\\行为树\\配置文件\\omini.b3"}