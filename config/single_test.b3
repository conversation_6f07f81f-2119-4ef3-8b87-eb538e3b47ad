{"name": "emblem", "description": "", "data": {"version": "0.3.0", "scope": "project", "selectedTree": "74ee69d5-357a-4f9a-85a6-f37521fb64c4", "trees": [{"version": "0.3.0", "scope": "tree", "id": "9b745bf6-fdec-4191-9d4e-f0ed7b70476a", "title": "A root", "description": "", "root": "640eae15-a571-446c-9c12-e00105710a94", "properties": {}, "nodes": {"640eae15-a571-446c-9c12-e00105710a94": {"id": "640eae15-a571-446c-9c12-e00105710a94", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -708, "y": -384}, "children": ["d2e61f87-7198-4e96-91b2-25f81a0f2756", "6fd18a36-296f-4183-a7a1-3d67a65a12aa"]}, "d2e61f87-7198-4e96-91b2-25f81a0f2756": {"id": "d2e61f87-7198-4e96-91b2-25f81a0f2756", "name": "189911a4-1e0a-4789-90eb-6e18493798ca", "category": "tree", "title": "Base", "description": "", "properties": {}, "display": {"x": -504, "y": -384}}, "6fd18a36-296f-4183-a7a1-3d67a65a12aa": {"id": "6fd18a36-296f-4183-a7a1-3d67a65a12aa", "name": "RepeatDuration", "category": "decorator", "title": "RepeatDuration", "description": "", "properties": {"duration": -1}, "display": {"x": -504, "y": -216}, "child": "cbada9ea-656f-4ef3-8852-d35f89385993"}, "cbada9ea-656f-4ef3-8852-d35f89385993": {"id": "cbada9ea-656f-4ef3-8852-d35f89385993", "name": "Timer", "category": "decorator", "title": "Timer", "description": "", "properties": {"duration": 1000}, "display": {"x": -312, "y": -216}, "child": "8085ac69-f4df-467b-98d6-bcb6368af412"}, "8085ac69-f4df-467b-98d6-bcb6368af412": {"id": "8085ac69-f4df-467b-98d6-bcb6368af412", "name": "Priority", "category": "composite", "title": "Priority", "description": "", "properties": {}, "display": {"x": -168, "y": -216}, "children": ["57868573-bf0a-4083-b1a4-e7a8e3f3fde6", "9c097f5c-c70f-4b52-89df-41ded7300f0f"]}, "57868573-bf0a-4083-b1a4-e7a8e3f3fde6": {"id": "57868573-bf0a-4083-b1a4-e7a8e3f3fde6", "name": "Sequence", "category": "composite", "title": "Sequence", "description": "", "properties": {}, "display": {"x": 12, "y": -480}, "children": ["54092b41-fc28-403a-862f-b70ce69a1c63", "7d9dbfeb-dcf4-4997-ba75-59a0f0341ce6", "fc7ea5a6-1c21-4305-8f5d-30c62c86901d", "5521bd79-ceaf-4fae-9622-c844a454dbee"]}, "9c097f5c-c70f-4b52-89df-41ded7300f0f": {"id": "9c097f5c-c70f-4b52-89df-41ded7300f0f", "name": "74ee69d5-357a-4f9a-85a6-f37521fb64c4", "category": "tree", "title": "emblem", "description": "纹章", "properties": {}, "display": {"x": 84, "y": -216}}, "54092b41-fc28-403a-862f-b70ce69a1c63": {"id": "54092b41-fc28-403a-862f-b70ce69a1c63", "name": "IsReconnect", "category": "condition", "title": "IsReconnect", "description": "", "properties": {"weight": -1}, "display": {"x": 216, "y": -552}}, "7d9dbfeb-dcf4-4997-ba75-59a0f0341ce6": {"id": "7d9dbfeb-dcf4-4997-ba75-59a0f0341ce6", "name": "Reconnect", "category": "action", "title": "Reconnect", "description": "", "properties": {}, "display": {"x": 216, "y": -480}}, "5521bd79-ceaf-4fae-9622-c844a454dbee": {"id": "5521bd79-ceaf-4fae-9622-c844a454dbee", "name": "Flush", "category": "action", "title": "Flush", "description": "", "properties": {}, "display": {"x": 216, "y": -348}}, "fc7ea5a6-1c21-4305-8f5d-30c62c86901d": {"id": "fc7ea5a6-1c21-4305-8f5d-30c62c86901d", "name": "<PERSON><PERSON>", "category": "action", "title": "<PERSON><PERSON>", "description": "", "properties": {}, "display": {"x": 216, "y": -420}}}, "display": {"camera_x": 1280, "camera_y": 684.5, "camera_z": 1, "x": -816, "y": -384}}, {"version": "0.3.0", "scope": "tree", "id": "189911a4-1e0a-4789-90eb-6e18493798ca", "title": "Base", "description": "", "root": "250e0c8a-044a-4b50-8214-f61caeac4b00", "properties": {}, "nodes": {"250e0c8a-044a-4b50-8214-f61caeac4b00": {"id": "250e0c8a-044a-4b50-8214-f61caeac4b00", "name": "MemSequence", "category": "composite", "title": "MemSequence", "description": "", "properties": {}, "display": {"x": -480, "y": -24}, "children": ["9a72e34b-44cb-478d-85df-c5f139fd4994", "f6e753b9-caac-4805-830f-95be536a9ff6", "0b295f56-03fb-4f9f-88eb-ddbe1a8d8b3b", "ac5e0ef6-ddb4-45b5-b2e1-ebc0bcc3e6c4"]}, "9a72e34b-44cb-478d-85df-c5f139fd4994": {"id": "9a72e34b-44cb-478d-85df-c5f139fd4994", "name": "GetServerList", "category": "action", "title": "GetServerList", "description": "", "properties": {}, "display": {"x": -108, "y": -180}}, "f6e753b9-caac-4805-830f-95be536a9ff6": {"id": "f6e753b9-caac-4805-830f-95be536a9ff6", "name": "<PERSON><PERSON>", "category": "action", "title": "<PERSON><PERSON>", "description": "", "properties": {}, "display": {"x": -108, "y": -108}}, "0b295f56-03fb-4f9f-88eb-ddbe1a8d8b3b": {"id": "0b295f56-03fb-4f9f-88eb-ddbe1a8d8b3b", "name": "GmTestData", "category": "action", "title": "GmTestData", "description": "", "properties": {"stage": 90}, "display": {"x": -108, "y": -24}}, "ac5e0ef6-ddb4-45b5-b2e1-ebc0bcc3e6c4": {"id": "ac5e0ef6-ddb4-45b5-b2e1-ebc0bcc3e6c4", "name": "Flush", "category": "action", "title": "Flush", "description": "", "properties": {}, "display": {"x": -108, "y": 60}}}, "display": {"camera_x": 1280, "camera_y": 688.5, "camera_z": 1, "x": -612, "y": -24}}, {"version": "0.3.0", "scope": "tree", "id": "74ee69d5-357a-4f9a-85a6-f37521fb64c4", "title": "emblem", "description": "纹章", "root": "7a8fe223-17bf-4844-869d-c062b0d32f4b", "properties": {}, "nodes": {"7a8fe223-17bf-4844-869d-c062b0d32f4b": {"id": "7a8fe223-17bf-4844-869d-c062b0d32f4b", "name": "MemRandom", "category": "composite", "title": "MemRandom", "description": "", "properties": {}, "display": {"x": -528, "y": -12}, "children": ["f0c8532c-8b8e-49d1-a3f3-75fdfd7d6980", "c95089b8-dc2f-41fc-a769-330cd7ac5db8", "46897a3c-e14e-47b5-95ca-496a3ca5a6be"]}, "c95089b8-dc2f-41fc-a769-330cd7ac5db8": {"id": "c95089b8-dc2f-41fc-a769-330cd7ac5db8", "name": "EmblemDecompose", "category": "action", "title": "EmblemDecompose", "description": "纹章分解", "properties": {}, "display": {"x": -252, "y": -12}}, "e6c6afbb-afa5-4fbb-836c-f853f1353fc9": {"id": "e6c6afbb-afa5-4fbb-836c-f853f1353fc9", "name": "EmblemLevelUp", "category": "action", "title": "EmblemLevelUp", "description": "纹章升级", "properties": {}, "display": {"x": 168, "y": -180}}, "46897a3c-e14e-47b5-95ca-496a3ca5a6be": {"id": "46897a3c-e14e-47b5-95ca-496a3ca5a6be", "name": "EmblemWear", "category": "action", "title": "EmblemWear", "description": "穿纹章", "properties": {}, "display": {"x": -252, "y": 276}}, "f0c8532c-8b8e-49d1-a3f3-75fdfd7d6980": {"id": "f0c8532c-8b8e-49d1-a3f3-75fdfd7d6980", "name": "Sequence", "category": "composite", "title": "Sequence", "description": "", "properties": {}, "display": {"x": -276, "y": -288}, "children": ["b0f5a9ca-fa40-4dcd-89ec-55edffb7fd9a", "e6c6afbb-afa5-4fbb-836c-f853f1353fc9"]}, "b0f5a9ca-fa40-4dcd-89ec-55edffb7fd9a": {"id": "b0f5a9ca-fa40-4dcd-89ec-55edffb7fd9a", "name": "Priority", "category": "composite", "title": "Priority", "description": "", "properties": {}, "display": {"x": 84, "y": -324}, "children": ["c2b11bb5-7340-4409-98aa-becf58c897c8", "302814ab-aedf-4174-9d2c-260dde8708a5"]}, "c2b11bb5-7340-4409-98aa-becf58c897c8": {"id": "c2b11bb5-7340-4409-98aa-becf58c897c8", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": "", "properties": {"res1": "4-10012-5000", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 288, "y": -372}}, "302814ab-aedf-4174-9d2c-260dde8708a5": {"id": "302814ab-aedf-4174-9d2c-260dde8708a5", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": "", "properties": {"res1": "4-10012-5000", "res2": "nil", "res3": "nil", "res4": "nil", "res5": "nil"}, "display": {"x": 276, "y": -300}}}, "display": {"camera_x": 1280, "camera_y": 684.5, "camera_z": 1, "x": -696, "y": -12}}], "custom_nodes": [{"version": "0.3.0", "scope": "node", "name": "<PERSON><PERSON>", "category": "action", "title": "<PERSON><PERSON>", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GetServerList", "category": "action", "title": "GetServerList", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "Flush", "category": "action", "title": "Flush", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsResourceEnough", "category": "condition", "title": "IsResourceEnough", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsSeasonDungeonEnough", "category": "condition", "title": "IsSeasonDungeonEnough", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeGetData", "category": "action", "title": "TalentTreeGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeLevelUp", "category": "action", "title": "TalentTreeLevelUp", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeReset", "category": "action", "title": "TalentTreeReset", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeReceiveTaskAwards", "category": "action", "title": "TalentTreeReceiveTaskAwards", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeHot", "category": "action", "title": "TalentTreeHot", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonGetData", "category": "action", "title": "SeasonDungeonGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonFight", "category": "action", "title": "SeasonDungeonFight", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GmAddResources", "category": "action", "title": "GmAddResources", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "TalentTreeIsHaveTaskAwards", "category": "condition", "title": "TalentTreeIsHaveTaskAwards", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "RepeatDuration", "category": "decorator", "title": "RepeatDuration", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GmTestData", "category": "action", "title": "GmTestData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonDungeonFormation", "category": "action", "title": "SeasonDungeonFormation", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "MemRandom", "category": "composite", "title": "MemRandom", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "IsReconnect", "category": "condition", "title": "IsReconnect", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "Reconnect", "category": "action", "title": "Reconnect", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "DungeonFight", "category": "action", "title": "DungeonFight", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "UserFormation", "category": "action", "title": "UserFormation", "description": null, "properties": {"formation_id": 0}}, {"version": "0.3.0", "scope": "node", "name": "Timer", "category": "decorator", "title": "Timer", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GoldBuyGet", "category": "action", "title": "GoldBuyGet", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "GoldBuyGetGold", "category": "action", "title": "GoldBuyGetGold", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonLinkGetData", "category": "action", "title": "SeasonLinkGetData", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "SeasonLinkActivate", "category": "action", "title": "SeasonLinkActivate", "description": null, "properties": {}}, {"version": "0.3.0", "scope": "node", "name": "EmblemWear", "category": "action", "title": null, "description": "穿纹章", "properties": {}, "parent": null}, {"version": "0.3.0", "scope": "node", "name": "EmblemLevelUp", "category": "action", "title": null, "description": "纹章升级", "properties": {}, "parent": null}, {"version": "0.3.0", "scope": "node", "name": "EmblemDecompose", "category": "action", "title": null, "description": "纹章分解", "properties": {}, "parent": null}], "custom_folders": []}, "path": "D:\\行为树\\配置文件\\single_test.b3"}