<?xml version="1.0" encoding="UTF-8"?>
<!--trade_group_id=int:交易组id goods_id=int:货物id goods_buy_ap=int:买入1个消耗体力 goods_max_num=int:货物买入数量限制 -->

<root>
    <data trade_group_id="1001" goods_id="1" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1002" goods_id="1" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1003" goods_id="2" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1004" goods_id="2" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1005" goods_id="1" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1006" goods_id="1" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1007" goods_id="2" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1008" goods_id="2" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1009" goods_id="1" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1010" goods_id="1" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1011" goods_id="2" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1012" goods_id="2" goods_buy_ap="0" goods_max_num="10" />
    <data trade_group_id="1" goods_id="1" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="1" goods_id="2" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="2" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="2" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="2" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="3" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="3" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="3" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="4" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="4" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="4" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="4" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="5" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="5" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="5" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="5" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="6" goods_id="19" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="6" goods_id="20" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="6" goods_id="21" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="7" goods_id="3" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="7" goods_id="4" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="8" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="8" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="8" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="9" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="9" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="9" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="10" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="10" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="10" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="10" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="11" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="11" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="11" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="11" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="12" goods_id="22" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="12" goods_id="23" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="12" goods_id="24" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="13" goods_id="1" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="13" goods_id="2" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="14" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="14" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="14" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="15" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="15" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="15" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="16" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="16" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="16" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="16" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="17" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="17" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="17" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="17" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="18" goods_id="25" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="18" goods_id="26" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="18" goods_id="27" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="19" goods_id="3" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="19" goods_id="4" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="20" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="20" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="20" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="21" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="21" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="21" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="22" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="22" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="22" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="22" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="23" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="23" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="23" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="23" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="24" goods_id="28" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="24" goods_id="29" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="24" goods_id="30" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="25" goods_id="1" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="25" goods_id="2" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="26" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="26" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="26" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="27" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="27" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="27" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="28" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="28" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="28" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="28" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="29" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="29" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="29" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="29" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="30" goods_id="19" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="30" goods_id="20" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="30" goods_id="21" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="31" goods_id="3" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="31" goods_id="4" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="32" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="32" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="32" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="33" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="33" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="33" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="34" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="34" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="34" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="34" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="35" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="35" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="35" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="35" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="36" goods_id="22" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="36" goods_id="23" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="36" goods_id="24" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="37" goods_id="1" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="37" goods_id="2" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="38" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="38" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="38" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="39" goods_id="5" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="39" goods_id="6" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="39" goods_id="7" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="40" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="40" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="40" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="40" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="41" goods_id="11" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="41" goods_id="12" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="41" goods_id="13" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="41" goods_id="14" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="42" goods_id="25" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="42" goods_id="26" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="42" goods_id="27" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="43" goods_id="3" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="43" goods_id="4" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="44" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="44" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="44" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="45" goods_id="8" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="45" goods_id="9" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="45" goods_id="10" goods_buy_ap="0" goods_max_num="30" />
    <data trade_group_id="46" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="46" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="46" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="46" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="47" goods_id="15" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="47" goods_id="16" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="47" goods_id="17" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="47" goods_id="18" goods_buy_ap="0" goods_max_num="40" />
    <data trade_group_id="48" goods_id="28" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="48" goods_id="29" goods_buy_ap="0" goods_max_num="20" />
    <data trade_group_id="48" goods_id="30" goods_buy_ap="0" goods_max_num="20" />
</root>
