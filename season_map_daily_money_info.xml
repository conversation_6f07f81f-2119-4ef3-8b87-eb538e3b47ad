<?xml version="1.0" encoding="UTF-8"?>
<!--explore_value=int:完成条件 daily_money=int:每日额外工资 -->

<root>
    <data explore_value="1" daily_money="20000" />
    <data explore_value="2" daily_money="21000" />
    <data explore_value="3" daily_money="22000" />
    <data explore_value="4" daily_money="23000" />
    <data explore_value="5" daily_money="24000" />
    <data explore_value="6" daily_money="25000" />
    <data explore_value="7" daily_money="26000" />
    <data explore_value="8" daily_money="27000" />
    <data explore_value="9" daily_money="28000" />
    <data explore_value="10" daily_money="29000" />
    <data explore_value="12" daily_money="31000" />
    <data explore_value="14" daily_money="33000" />
    <data explore_value="16" daily_money="35000" />
    <data explore_value="18" daily_money="37000" />
    <data explore_value="20" daily_money="39000" />
    <data explore_value="22" daily_money="41000" />
    <data explore_value="25" daily_money="45000" />
    <data explore_value="30" daily_money="50000" />
    <data explore_value="35" daily_money="55000" />
    <data explore_value="40" daily_money="60000" />
    <data explore_value="45" daily_money="65000" />
    <data explore_value="50" daily_money="70000" />
    <data explore_value="55" daily_money="75000" />
    <data explore_value="60" daily_money="80000" />
    <data explore_value="65" daily_money="85000" />
    <data explore_value="70" daily_money="90000" />
    <data explore_value="75" daily_money="95000" />
    <data explore_value="80" daily_money="100000" />
    <data explore_value="85" daily_money="105000" />
    <data explore_value="90" daily_money="110000" />
    <data explore_value="95" daily_money="115000" />
    <data explore_value="100" daily_money="120000" />
    <data explore_value="105" daily_money="125000" />
    <data explore_value="110" daily_money="130000" />
    <data explore_value="115" daily_money="135000" />
    <data explore_value="120" daily_money="140000" />
    <data explore_value="125" daily_money="145000" />
    <data explore_value="130" daily_money="150000" />
    <data explore_value="135" daily_money="155000" />
    <data explore_value="140" daily_money="160000" />
    <data explore_value="145" daily_money="165000" />
    <data explore_value="150" daily_money="170000" />
    <data explore_value="155" daily_money="175000" />
    <data explore_value="160" daily_money="180000" />
    <data explore_value="165" daily_money="185000" />
    <data explore_value="170" daily_money="190000" />
    <data explore_value="175" daily_money="195000" />
    <data explore_value="180" daily_money="200000" />
    <data explore_value="184" daily_money="205000" />
</root>
