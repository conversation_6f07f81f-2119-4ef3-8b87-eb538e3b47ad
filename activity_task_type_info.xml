<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:任务类型 type=int:逻辑类型 add_type=int:记录类型 finish_type=int:完成条件类型 parameter1=int:参数X compare_type_param=int:参数比较方法 is_client=int:是否前端处理 desc=string:后台显示 -->

<root>
    <data id="1504000" type="1005" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="英雄升星多少次" />
    <data id="1505004" type="1010" add_type="1" finish_type="1" parameter1="4" compare_type_param="0" is_client="0" desc="获得多少个任意种族的4星英雄" />
    <data id="1505005" type="1010" add_type="1" finish_type="1" parameter1="5" compare_type_param="0" is_client="0" desc="获得多少个任意种族的5星英雄" />
    <data id="1505006" type="1010" add_type="1" finish_type="1" parameter1="6" compare_type_param="0" is_client="0" desc="获得多少个任意种族的6星英雄" />
    <data id="1505007" type="1010" add_type="1" finish_type="1" parameter1="7" compare_type_param="0" is_client="0" desc="获得多少个任意种族的7星英雄" />
    <data id="1505008" type="1010" add_type="1" finish_type="1" parameter1="8" compare_type_param="0" is_client="0" desc="获得多少个任意种族的8星英雄" />
    <data id="1505009" type="1010" add_type="1" finish_type="1" parameter1="9" compare_type_param="0" is_client="0" desc="获得多少个任意种族的9星英雄" />
    <data id="1505010" type="1010" add_type="1" finish_type="1" parameter1="10" compare_type_param="0" is_client="0" desc="获得多少个任意种族的10星英雄" />
    <data id="1505011" type="1010" add_type="1" finish_type="1" parameter1="11" compare_type_param="0" is_client="0" desc="获得多少个任意种族的11星英雄" />
    <data id="1505012" type="1010" add_type="1" finish_type="1" parameter1="12" compare_type_param="0" is_client="0" desc="获得多少个任意种族的12星英雄" />
    <data id="1505013" type="1010" add_type="1" finish_type="1" parameter1="13" compare_type_param="0" is_client="0" desc="获得多少个任意种族的13星英雄" />
    <data id="1505014" type="1010" add_type="1" finish_type="1" parameter1="14" compare_type_param="0" is_client="0" desc="获得多少个任意种族的14星英雄" />
    <data id="1506006" type="4001" add_type="1" finish_type="1" parameter1="6" compare_type_param="0" is_client="0" desc="获得多少个帝国6星英雄" />
    <data id="1506007" type="4001" add_type="1" finish_type="1" parameter1="7" compare_type_param="0" is_client="0" desc="获得多少个帝国7星英雄" />
    <data id="1506008" type="4001" add_type="1" finish_type="1" parameter1="8" compare_type_param="0" is_client="0" desc="获得多少个帝国8星英雄" />
    <data id="1506009" type="4001" add_type="1" finish_type="1" parameter1="9" compare_type_param="0" is_client="0" desc="获得多少个帝国9星英雄" />
    <data id="1507006" type="4002" add_type="1" finish_type="1" parameter1="6" compare_type_param="0" is_client="0" desc="获得多少个森林6星英雄" />
    <data id="1507007" type="4002" add_type="1" finish_type="1" parameter1="7" compare_type_param="0" is_client="0" desc="获得多少个森林7星英雄" />
    <data id="1507008" type="4002" add_type="1" finish_type="1" parameter1="8" compare_type_param="0" is_client="0" desc="获得多少个森林8星英雄" />
    <data id="1507009" type="4002" add_type="1" finish_type="1" parameter1="9" compare_type_param="0" is_client="0" desc="获得多少个森林9星英雄" />
    <data id="1508006" type="4003" add_type="1" finish_type="1" parameter1="6" compare_type_param="0" is_client="0" desc="获得多少个月影6星英雄" />
    <data id="1508007" type="4003" add_type="1" finish_type="1" parameter1="7" compare_type_param="0" is_client="0" desc="获得多少个月影7星英雄" />
    <data id="1508008" type="4003" add_type="1" finish_type="1" parameter1="8" compare_type_param="0" is_client="0" desc="获得多少个月影8星英雄" />
    <data id="1508009" type="4003" add_type="1" finish_type="1" parameter1="9" compare_type_param="0" is_client="0" desc="获得多少个月影9星英雄" />
    <data id="1509006" type="4004" add_type="1" finish_type="1" parameter1="6" compare_type_param="0" is_client="0" desc="获得多少个神使6星英雄" />
    <data id="1509007" type="4004" add_type="1" finish_type="1" parameter1="7" compare_type_param="0" is_client="0" desc="获得多少个神使7星英雄" />
    <data id="1509008" type="4004" add_type="1" finish_type="1" parameter1="8" compare_type_param="0" is_client="0" desc="获得多少个神使8星英雄" />
    <data id="1509009" type="4004" add_type="1" finish_type="1" parameter1="9" compare_type_param="0" is_client="0" desc="获得多少个神使8星英雄" />
    <data id="1510006" type="4005" add_type="1" finish_type="1" parameter1="6" compare_type_param="0" is_client="0" desc="获得多少个魔裔6星英雄" />
    <data id="1510007" type="4005" add_type="1" finish_type="1" parameter1="7" compare_type_param="0" is_client="0" desc="获得多少个魔裔7星英雄" />
    <data id="1510008" type="4005" add_type="1" finish_type="1" parameter1="8" compare_type_param="0" is_client="0" desc="获得多少个魔裔8星英雄" />
    <data id="1510009" type="4005" add_type="1" finish_type="1" parameter1="9" compare_type_param="0" is_client="0" desc="获得多少个魔裔9星英雄" />
    <data id="1511010" type="4006" add_type="2" finish_type="1" parameter1="10" compare_type_param="0" is_client="0" desc="同时拥有多少个10星英雄" />
    <data id="1511011" type="4006" add_type="2" finish_type="1" parameter1="11" compare_type_param="0" is_client="0" desc="同时拥有多少个11星英雄" />
    <data id="1511012" type="4006" add_type="2" finish_type="1" parameter1="12" compare_type_param="0" is_client="0" desc="同时拥有多少个12星英雄" />
    <data id="1511013" type="4006" add_type="2" finish_type="1" parameter1="13" compare_type_param="0" is_client="0" desc="同时拥有多少个13星英雄" />
    <data id="1511014" type="4006" add_type="2" finish_type="1" parameter1="14" compare_type_param="0" is_client="0" desc="同时拥有多少个14星英雄" />
    <data id="2301000" type="2045" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="累计宝石置换x次" />
    <data id="2800000" type="1003" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="加速挂机X次" />
    <data id="3800000" type="1031" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="接取多少个任意品质悬赏任务" />
    <data id="3800004" type="1031" add_type="1" finish_type="1" parameter1="4" compare_type_param="0" is_client="0" desc="接取多少个紫色品质悬赏任务" />
    <data id="3800005" type="1031" add_type="1" finish_type="1" parameter1="5" compare_type_param="0" is_client="0" desc="接取多少个橙色品质悬赏任务" />
    <data id="3800006" type="1031" add_type="1" finish_type="1" parameter1="6" compare_type_param="0" is_client="0" desc="接取多少个红色品质悬赏任务" />
    <data id="3801000" type="1032" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="接取多少个紫色以上品质悬赏任务" />
    <data id="3900000" type="1002" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="任意招募多少次" />
    <data id="3900003" type="1002" add_type="1" finish_type="1" parameter1="3" compare_type_param="0" is_client="0" desc="高级招募多少次" />
    <data id="3901000" type="1011" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="先知召唤多少次" />
    <data id="3902000" type="1012" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="抽神器多少次" />
    <data id="4100000" type="1014" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="神秘商店刷新多少次" />
    <data id="4101000" type="1015" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="神秘商店买多少次商品" />
    <data id="4200000" type="1016" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="参与多少次竞技场挑战" />
    <data id="4201000" type="1017" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="竞技场获得多少次胜利" />
    <data id="4206000" type="1050" add_type="2" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="竞技场最高段位达到X" />
    <data id="4300000" type="1020" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="挑战多少次材料本" />
    <data id="4500000" type="1022" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="挑战任意公会BOSS多少次" />
    <data id="4600000" type="1023" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="迷宫玩法战胜最终BOSS多少次" />
    <data id="4601000" type="1039" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="累计在迷宫中击败任意敌人x个" />
    <data id="4800000" type="1025" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="个人BOSS挑战多少次" />
    <data id="4802000" type="2080" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="通关个人BOSS任意不同的BOSS层数X次" />
    <data id="4900000" type="1027" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="种植幽暗密林多少次" />
    <data id="4905001" type="1051" add_type="1" finish_type="1" parameter1="1" compare_type_param="0" is_client="0" desc="密林种植X次普通花" />
    <data id="4905002" type="1051" add_type="1" finish_type="1" parameter1="2" compare_type_param="0" is_client="0" desc="密林种植X次PVP花" />
    <data id="2300020" type="2033" add_type="1" finish_type="1" parameter1="20" compare_type_param="0" is_client="0" desc="获取青翠宝石及以上x个" />
    <data id="2300030" type="2033" add_type="1" finish_type="1" parameter1="30" compare_type_param="0" is_client="0" desc="获取湛蓝宝石及以上x个" />
    <data id="2300040" type="2033" add_type="1" finish_type="1" parameter1="40" compare_type_param="0" is_client="0" desc="获取皇紫宝石及以上x个" />
    <data id="2300050" type="2033" add_type="1" finish_type="1" parameter1="50" compare_type_param="0" is_client="0" desc="获取璨金宝石及以上x个" />
    <data id="2300060" type="2033" add_type="1" finish_type="1" parameter1="60" compare_type_param="0" is_client="0" desc="获取灵魂宝石及以上x个" />
    <data id="2300070" type="2033" add_type="1" finish_type="1" parameter1="70" compare_type_param="0" is_client="0" desc="获取精魄宝石及以上x个" />
    <data id="2100002" type="2030" add_type="1" finish_type="1" parameter1="20" compare_type_param="0" is_client="0" desc="获得x个普通及以上品质的纹章" />
    <data id="2100003" type="2030" add_type="1" finish_type="1" parameter1="30" compare_type_param="0" is_client="0" desc="获得x个优良及以上品质的纹章" />
    <data id="2100004" type="2030" add_type="1" finish_type="1" parameter1="40" compare_type_param="0" is_client="0" desc="获得x个稀有及以上品质的纹章" />
    <data id="2100005" type="2030" add_type="1" finish_type="1" parameter1="50" compare_type_param="0" is_client="0" desc="获得x个史诗及以上品质的纹章" />
    <data id="2100006" type="2030" add_type="1" finish_type="1" parameter1="60" compare_type_param="0" is_client="0" desc="获得x个传说及以上品质的纹章" />
    <data id="4304045" type="2084" add_type="2" finish_type="1" parameter1="45" compare_type_param="0" is_client="0" desc="经验本累积达到多少星" />
    <data id="4304046" type="2084" add_type="2" finish_type="1" parameter1="46" compare_type_param="0" is_client="0" desc="金币本累积达到多少星" />
    <data id="4304047" type="2084" add_type="2" finish_type="1" parameter1="47" compare_type_param="0" is_client="0" desc="装备本累积达到多少星" />
    <data id="4304048" type="2084" add_type="2" finish_type="1" parameter1="48" compare_type_param="0" is_client="0" desc="英雄本累积达到多少星" />
    <data id="1004000" type="8001" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="累计登录多少天" />
    <data id="1005000" type="8002" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="累计消耗多少钻石" />
    <data id="1006000" type="8003" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="高级招募抽卡x-y次（随机区间）" />
    <data id="1007000" type="8004" add_type="2" finish_type="1" parameter1="60" compare_type_param="0" is_client="0" desc="抽卡获得品质60及以上英雄1次（含高级、普通、先知抽）" />
    <data id="1700000" type="2010" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="拥有多少把神器" />
    <data id="1700040" type="2010" add_type="1" finish_type="1" parameter1="40" compare_type_param="0" is_client="0" desc="拥有多少把紫色神器" />
    <data id="1700050" type="2010" add_type="1" finish_type="1" parameter1="50" compare_type_param="0" is_client="0" desc="拥有多少把橙色神器" />
    <data id="1700060" type="2010" add_type="1" finish_type="1" parameter1="60" compare_type_param="0" is_client="0" desc="拥有多少把红色神器" />
    <data id="1701003" type="2011" add_type="1" finish_type="1" parameter1="3" compare_type_param="0" is_client="0" desc="拥有多少把3星神器" />
    <data id="1701005" type="2011" add_type="1" finish_type="1" parameter1="5" compare_type_param="0" is_client="0" desc="拥有多少把5星神器" />
    <data id="1902020" type="2034" add_type="2" finish_type="1" parameter1="20" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套绿色品质及以上的装备" />
    <data id="1902030" type="2034" add_type="2" finish_type="1" parameter1="30" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套蓝色品质及以上的装备" />
    <data id="1902035" type="2034" add_type="2" finish_type="1" parameter1="35" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套蓝+品质及以上的装备" />
    <data id="1902040" type="2034" add_type="2" finish_type="1" parameter1="40" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套紫色品质及以上的装备" />
    <data id="1902045" type="2034" add_type="2" finish_type="1" parameter1="45" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套紫+品质及以上的装备" />
    <data id="1902050" type="2034" add_type="2" finish_type="1" parameter1="50" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套橙色品质及以上的装备" />
    <data id="1902055" type="2034" add_type="2" finish_type="1" parameter1="55" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套橙+品质及以上的装备" />
    <data id="1902060" type="2034" add_type="2" finish_type="1" parameter1="60" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套红色品质及以上的装备" />
    <data id="1702010" type="5001" add_type="2" finish_type="1" parameter1="10" compare_type_param="0" is_client="1" desc="拥有[0]个10级神器" />
    <data id="1702015" type="5001" add_type="2" finish_type="1" parameter1="15" compare_type_param="0" is_client="1" desc="拥有[0]个15级神器" />
    <data id="1702020" type="5001" add_type="2" finish_type="1" parameter1="20" compare_type_param="0" is_client="1" desc="拥有[0]个20级神器" />
    <data id="1702025" type="5001" add_type="2" finish_type="1" parameter1="25" compare_type_param="0" is_client="1" desc="拥有[0]个25级神器" />
    <data id="1702030" type="5001" add_type="2" finish_type="1" parameter1="30" compare_type_param="0" is_client="1" desc="拥有[0]个30级神器" />
    <data id="1702035" type="5001" add_type="2" finish_type="1" parameter1="35" compare_type_param="0" is_client="1" desc="拥有[0]个35级神器" />
    <data id="1702040" type="5001" add_type="2" finish_type="1" parameter1="40" compare_type_param="0" is_client="1" desc="拥有[0]个40级神器" />
    <data id="1702045" type="5001" add_type="2" finish_type="1" parameter1="45" compare_type_param="0" is_client="1" desc="拥有[0]个45级神器" />
    <data id="1702050" type="5001" add_type="2" finish_type="1" parameter1="50" compare_type_param="0" is_client="1" desc="拥有[0]个50级神器" />
    <data id="1702055" type="5001" add_type="2" finish_type="1" parameter1="55" compare_type_param="0" is_client="1" desc="拥有[0]个55级神器" />
    <data id="1702060" type="5001" add_type="2" finish_type="1" parameter1="60" compare_type_param="0" is_client="1" desc="拥有[0]个60级神器" />
    <data id="1702065" type="5001" add_type="2" finish_type="1" parameter1="65" compare_type_param="0" is_client="1" desc="拥有[0]个65级神器" />
    <data id="1702070" type="5001" add_type="2" finish_type="1" parameter1="70" compare_type_param="0" is_client="1" desc="拥有[0]个70级神器" />
    <data id="1702075" type="5001" add_type="2" finish_type="1" parameter1="75" compare_type_param="0" is_client="1" desc="拥有[0]个75级神器" />
    <data id="1702080" type="5001" add_type="2" finish_type="1" parameter1="80" compare_type_param="0" is_client="1" desc="拥有[0]个80级神器" />
    <data id="1702085" type="5001" add_type="2" finish_type="1" parameter1="85" compare_type_param="0" is_client="1" desc="拥有[0]个85级神器" />
    <data id="1702090" type="5001" add_type="2" finish_type="1" parameter1="90" compare_type_param="0" is_client="1" desc="拥有[0]个90级神器" />
    <data id="1702095" type="5001" add_type="2" finish_type="1" parameter1="95" compare_type_param="0" is_client="1" desc="拥有[0]个95级神器" />
    <data id="1702100" type="5001" add_type="2" finish_type="1" parameter1="100" compare_type_param="0" is_client="1" desc="拥有[0]个100级神器" />
    <data id="2303020" type="5101" add_type="2" finish_type="1" parameter1="20" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套普通及以上品质宝石" />
    <data id="2303030" type="5101" add_type="2" finish_type="1" parameter1="30" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套优良及以上品质宝石" />
    <data id="2303040" type="5101" add_type="2" finish_type="1" parameter1="40" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套稀有及以上品质宝石" />
    <data id="2303050" type="5101" add_type="2" finish_type="1" parameter1="50" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套史诗及以上品质宝石" />
    <data id="2303060" type="5101" add_type="2" finish_type="1" parameter1="60" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套传说及以上品质宝石" />
    <data id="2303035" type="5101" add_type="2" finish_type="1" parameter1="35" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套优良+及以上品质宝石" />
    <data id="2303045" type="5101" add_type="2" finish_type="1" parameter1="45" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套稀有+及以上品质宝石" />
    <data id="2303055" type="5101" add_type="2" finish_type="1" parameter1="55" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套史诗+及以上品质宝石" />
    <data id="1914002" type="5102" add_type="2" finish_type="1" parameter1="2" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼2级及以上的装备" />
    <data id="1914004" type="5102" add_type="2" finish_type="1" parameter1="4" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼4级及以上的装备" />
    <data id="1914006" type="5102" add_type="2" finish_type="1" parameter1="6" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼6级及以上的装备" />
    <data id="1914008" type="5102" add_type="2" finish_type="1" parameter1="8" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼8级及以上的装备" />
    <data id="1914010" type="5102" add_type="2" finish_type="1" parameter1="10" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼10级及以上的装备" />
    <data id="1914012" type="5102" add_type="2" finish_type="1" parameter1="12" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼12级及以上的装备" />
    <data id="1914014" type="5102" add_type="2" finish_type="1" parameter1="14" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼14级及以上的装备" />
    <data id="1914016" type="5102" add_type="2" finish_type="1" parameter1="16" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼16级及以上的装备" />
    <data id="1914018" type="5102" add_type="2" finish_type="1" parameter1="18" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼18级及以上的装备" />
    <data id="1914020" type="5102" add_type="2" finish_type="1" parameter1="20" compare_type_param="1" is_client="1" desc="[0]个英雄穿戴齐一套精炼20级及以上的装备" />
    <data id="1517001" type="4013" add_type="2" finish_type="1" parameter1="1" compare_type_param="0" is_client="0" desc="将任意帝国英雄提升至[0]星" />
    <data id="1517002" type="4013" add_type="2" finish_type="1" parameter1="2" compare_type_param="0" is_client="0" desc="将任意森林英雄提升至[0]星" />
    <data id="1517003" type="4013" add_type="2" finish_type="1" parameter1="3" compare_type_param="0" is_client="0" desc="将任意月影英雄提升至[0]星" />
    <data id="1517004" type="4013" add_type="2" finish_type="1" parameter1="4" compare_type_param="0" is_client="0" desc="将任意神使英雄提升至[0]星" />
    <data id="1517005" type="4013" add_type="2" finish_type="1" parameter1="5" compare_type_param="0" is_client="0" desc="将任意魔裔英雄提升至[0]星" />
    <data id="5111001" type="5111" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="进行多少次宝石升级" />
    <data id="5112001" type="5112" add_type="1" finish_type="1" parameter1="30" compare_type_param="1" is_client="0" desc="将多少颗宝石提升至蓝色及以上品质" />
    <data id="5113003" type="5113" add_type="1" finish_type="1" parameter1="30" compare_type_param="1" is_client="1" desc="拥有多少颗蓝色及以上品质的宝石" />
    <data id="5113004" type="5113" add_type="1" finish_type="1" parameter1="40" compare_type_param="1" is_client="1" desc="拥有多少颗紫色及以上品质的宝石" />
    <data id="5113005" type="5113" add_type="1" finish_type="1" parameter1="50" compare_type_param="1" is_client="1" desc="拥有多少颗橙色及以上品质的宝石" />
    <data id="5113006" type="5113" add_type="1" finish_type="1" parameter1="60" compare_type_param="1" is_client="1" desc="拥有多少颗红色及以上品质的宝石" />
    <data id="2110000" type="2102" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="符文进行任意养成[0]次（不含转移）" />
    <data id="2101002" type="2038" add_type="2" finish_type="1" parameter1="20" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套绿色品质及以上的纹章" />
    <data id="2101003" type="2038" add_type="2" finish_type="1" parameter1="30" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套蓝色品质及以上的纹章" />
    <data id="2101004" type="2038" add_type="2" finish_type="1" parameter1="40" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套紫色品质及以上的纹章" />
    <data id="2101005" type="2038" add_type="2" finish_type="1" parameter1="50" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套橙色品质及以上的纹章" />
    <data id="2101006" type="2038" add_type="2" finish_type="1" parameter1="60" compare_type_param="0" is_client="1" desc="多少英雄穿戴齐一套红色品质及以上的纹章" />
    <data id="2103000" type="2031" add_type="2" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="等级最高的纹章达到多少级" />
    <data id="6100008" type="6102" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="累积在羁绊召唤抽[0]次" />
    <data id="6100007" type="6101" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="累积在天赐神兵卡池内召唤[0]次" />
    <data id="1009000" type="1080" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="累积在神魔首发卡池内召唤[0]次" />
    <data id="3900004" type="1002" add_type="1" finish_type="1" parameter1="1" compare_type_param="0" is_client="0" desc="普通招募[0]次" />
    <data id="2201001" type="2201" add_type="1" finish_type="1" parameter1="0" compare_type_param="0" is_client="0" desc="世界boss挑战或扫荡X次" />
</root>
