<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:类型 type=int:类型 class=int:档次 class_weight=int:档次权重 -->

<root>
    <data id="1" type="1" class="10102" class_weight="879" />
    <data id="2" type="1" class="10103" class_weight="100" />
    <data id="3" type="1" class="10104" class_weight="20" />
    <data id="4" type="1" class="10105" class_weight="1" />
    <data id="5" type="2" class="10202" class_weight="300" />
    <data id="6" type="2" class="10203" class_weight="540" />
    <data id="7" type="2" class="10204" class_weight="150" />
    <data id="8" type="2" class="10205" class_weight="10" />
    <data id="9" type="3" class="10303" class_weight="800" />
    <data id="10" type="3" class="10304" class_weight="150" />
    <data id="11" type="3" class="10305" class_weight="50" />
    <data id="12" type="4" class="10405" class_weight="10" />
    <data id="13" type="5" class="10504" class_weight="850" />
    <data id="14" type="5" class="10505" class_weight="150" />
    <data id="15" type="6" class="10604" class_weight="850" />
    <data id="16" type="6" class="10605" class_weight="150" />
    <data id="17" type="7" class="10704" class_weight="850" />
    <data id="18" type="7" class="10705" class_weight="150" />
    <data id="19" type="8" class="10801" class_weight="4000" />
    <data id="20" type="8" class="10802" class_weight="750" />
    <data id="21" type="8" class="10804" class_weight="4500" />
    <data id="22" type="8" class="10805" class_weight="750" />
    <data id="23" type="9" class="10905" class_weight="10" />
    <data id="24" type="10" class="11005" class_weight="10" />
    <data id="25" type="11" class="11105" class_weight="10" />
    <data id="26" type="12" class="11205" class_weight="10" />
    <data id="27" type="13" class="11305" class_weight="10" />
</root>
