<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:ID rank_min=int:伤害排名下限 rank_max=int:伤害排名下限 rank_type1=int:奖励1 rank_value1=int:奖励1 rank_count1=int:奖励1 rank_type2=int:奖励2 rank_value2=int:奖励2 rank_count2=int:奖励2 rank_type3=int:奖励3 rank_value3=int:奖励3 rank_count3=int:奖励3 rank_type4=int:奖励4 rank_value4=int:奖励4 rank_count4=int:奖励4 avatar=int:是否有头像 -->

<root>
    <data id="1" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="15" rank_value3="30003" rank_count3="1" rank_type4="0" rank_value4="0" rank_count4="0" avatar="1" />
    <data id="1" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="1" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="1" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9018" rank_count1="240" rank_type2="9" rank_value2="9016" rank_count2="144" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="1" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9018" rank_count1="150" rank_type2="9" rank_value2="9016" rank_count2="90" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="2" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="15" rank_value3="30003" rank_count3="1" rank_type4="0" rank_value4="0" rank_count4="0" avatar="1" />
    <data id="2" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="2" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="2" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9018" rank_count1="240" rank_type2="9" rank_value2="9016" rank_count2="144" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="2" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9018" rank_count1="150" rank_type2="9" rank_value2="9016" rank_count2="90" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="3" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="15" rank_value3="30003" rank_count3="1" rank_type4="0" rank_value4="0" rank_count4="0" avatar="1" />
    <data id="3" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="3" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9018" rank_count1="300" rank_type2="9" rank_value2="9016" rank_count2="180" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="3" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9018" rank_count1="240" rank_type2="9" rank_value2="9016" rank_count2="144" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="3" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9018" rank_count1="150" rank_type2="9" rank_value2="9016" rank_count2="90" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="4" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="15" rank_value3="30003" rank_count3="1" rank_type4="0" rank_value4="0" rank_count4="0" avatar="1" />
    <data id="4" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="4" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="4" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9018" rank_count1="280" rank_type2="9" rank_value2="9016" rank_count2="168" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="4" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9018" rank_count1="175" rank_type2="9" rank_value2="9016" rank_count2="105" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="5" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="15" rank_value3="30003" rank_count3="1" rank_type4="0" rank_value4="0" rank_count4="0" avatar="1" />
    <data id="5" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="5" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="5" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9018" rank_count1="280" rank_type2="9" rank_value2="9016" rank_count2="168" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="5" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9018" rank_count1="175" rank_type2="9" rank_value2="9016" rank_count2="105" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="6" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="15" rank_value3="30003" rank_count3="1" rank_type4="0" rank_value4="0" rank_count4="0" avatar="1" />
    <data id="6" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="6" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9018" rank_count1="350" rank_type2="9" rank_value2="9016" rank_count2="210" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="6" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9018" rank_count1="280" rank_type2="9" rank_value2="9016" rank_count2="168" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="6" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9018" rank_count1="175" rank_type2="9" rank_value2="9016" rank_count2="105" rank_type3="0" rank_value3="0" rank_count3="0" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="7" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="15" rank_value4="30003" rank_count4="1" avatar="1" />
    <data id="7" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="7" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="7" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="96" rank_type2="9" rank_value2="9018" rank_count2="320" rank_type3="9" rank_value3="9016" rank_count3="192" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="7" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="60" rank_type2="9" rank_value2="9018" rank_count2="200" rank_type3="9" rank_value3="9016" rank_count3="120" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="8" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="15" rank_value4="30003" rank_count4="1" avatar="1" />
    <data id="8" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="8" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="8" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="96" rank_type2="9" rank_value2="9018" rank_count2="320" rank_type3="9" rank_value3="9016" rank_count3="192" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="8" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="60" rank_type2="9" rank_value2="9018" rank_count2="200" rank_type3="9" rank_value3="9016" rank_count3="120" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="9" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="15" rank_value4="30003" rank_count4="1" avatar="1" />
    <data id="9" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="9" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="9" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="96" rank_type2="9" rank_value2="9018" rank_count2="320" rank_type3="9" rank_value3="9016" rank_count3="192" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="9" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="60" rank_type2="9" rank_value2="9018" rank_count2="200" rank_type3="9" rank_value3="9016" rank_count3="120" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="10" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="15" rank_value4="30002" rank_count4="1" avatar="1" />
    <data id="10" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="10" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="10" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="10" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="250" rank_type3="9" rank_value3="9016" rank_count3="150" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="11" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="15" rank_value4="30002" rank_count4="1" avatar="1" />
    <data id="11" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="11" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="11" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="11" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="250" rank_type3="9" rank_value3="9016" rank_count3="150" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="12" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="15" rank_value4="30002" rank_count4="1" avatar="1" />
    <data id="12" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="12" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="500" rank_type3="9" rank_value3="9016" rank_count3="300" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="12" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="400" rank_type3="9" rank_value3="9016" rank_count3="240" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="12" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="250" rank_type3="9" rank_value3="9016" rank_count3="150" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="13" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="15" rank_value4="30002" rank_count4="1" avatar="1" />
    <data id="13" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="13" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="13" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="440" rank_type3="9" rank_value3="9016" rank_count3="264" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="13" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="275" rank_type3="9" rank_value3="9016" rank_count3="165" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="14" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="15" rank_value4="30002" rank_count4="1" avatar="1" />
    <data id="14" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="14" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="14" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="440" rank_type3="9" rank_value3="9016" rank_count3="264" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="14" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="275" rank_type3="9" rank_value3="9016" rank_count3="165" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="15" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="15" rank_value4="30002" rank_count4="1" avatar="1" />
    <data id="15" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="15" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="550" rank_type3="9" rank_value3="9016" rank_count3="330" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="15" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="440" rank_type3="9" rank_value3="9016" rank_count3="264" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="15" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="275" rank_type3="9" rank_value3="9016" rank_count3="165" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="16" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="15" rank_value4="30001" rank_count4="1" avatar="1" />
    <data id="16" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="16" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="16" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="480" rank_type3="9" rank_value3="9016" rank_count3="288" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="16" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="300" rank_type3="9" rank_value3="9016" rank_count3="180" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="17" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="15" rank_value4="30001" rank_count4="1" avatar="1" />
    <data id="17" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="17" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="17" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="480" rank_type3="9" rank_value3="9016" rank_count3="288" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="17" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="300" rank_type3="9" rank_value3="9016" rank_count3="180" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="18" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="15" rank_value4="30001" rank_count4="1" avatar="1" />
    <data id="18" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="18" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="150" rank_type2="9" rank_value2="9018" rank_count2="600" rank_type3="9" rank_value3="9016" rank_count3="360" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="18" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="120" rank_type2="9" rank_value2="9018" rank_count2="480" rank_type3="9" rank_value3="9016" rank_count3="288" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="18" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="75" rank_type2="9" rank_value2="9018" rank_count2="300" rank_type3="9" rank_value3="9016" rank_count3="180" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="19" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="15" rank_value4="30001" rank_count4="1" avatar="1" />
    <data id="19" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="19" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="19" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="160" rank_type2="9" rank_value2="9018" rank_count2="520" rank_type3="9" rank_value3="9016" rank_count3="312" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="19" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="100" rank_type2="9" rank_value2="9018" rank_count2="325" rank_type3="9" rank_value3="9016" rank_count3="195" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="20" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="15" rank_value4="30001" rank_count4="1" avatar="1" />
    <data id="20" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="20" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="20" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="160" rank_type2="9" rank_value2="9018" rank_count2="520" rank_type3="9" rank_value3="9016" rank_count3="312" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="20" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="100" rank_type2="9" rank_value2="9018" rank_count2="325" rank_type3="9" rank_value3="9016" rank_count3="195" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="21" rank_min="1" rank_max="1" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="15" rank_value4="30001" rank_count4="1" avatar="1" />
    <data id="21" rank_min="2" rank_max="2" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="21" rank_min="3" rank_max="3" rank_type1="9" rank_value1="9054" rank_count1="200" rank_type2="9" rank_value2="9018" rank_count2="650" rank_type3="9" rank_value3="9016" rank_count3="390" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="21" rank_min="4" rank_max="5" rank_type1="9" rank_value1="9054" rank_count1="160" rank_type2="9" rank_value2="9018" rank_count2="520" rank_type3="9" rank_value3="9016" rank_count3="312" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
    <data id="21" rank_min="6" rank_max="10" rank_type1="9" rank_value1="9054" rank_count1="100" rank_type2="9" rank_value2="9018" rank_count2="325" rank_type3="9" rank_value3="9016" rank_count3="195" rank_type4="0" rank_value4="0" rank_count4="0" avatar="0" />
</root>
