## 玩家14天参考养成
# 玩家战队信息
base:
  level: 80                             #战队等级

######################################################################################

# 英雄（已发放的英雄的统一养成）
hero:
  ids: [13001,14002,13003,11004,12005,12006,12007,13009,12010,13011,22001,23002,21003,22004,24005,23006,22007,23008,23009,24010,24011,32001,31002,34003,33004,32005,34006,34007,34008,33010,31011,33012,42001,43002,41003,44004,43005,43006,43007,42008,44009,43010,41011,43012,43013,43014,53001,52002,51003,54004,52005,53006,53007,51008,52009,53010,53011,53012,52013,53014,52015,52016,11982,13980,13981,21980,22981,23982,32980,33981,44980,43981,52980,51981,14991,11992,23991,24992,31991,32992,43991,44992,51990,52992,11997,13998,21997,24998,31997,33998,43997,52997,43015,42016,53017,22012,42019,42020,53018,13013,64001,12014,41021,53019,52020,42022,53021,52022,33013,41023,42024,53023,34014,64002,42025,41026,52024,32015,44027,53025,52026,21013,43028,54027,52028]                       #英雄系统id
  stars: [13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,9,9,9,9,9,9,9,9,9,9,9,9,5,5,5,5,5,5,5,5,5,5,4,4,4,4,4,4,4,4,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15]                       #英雄星级（四星英雄星级上限是5，那么就只能升到5）
  levels: [98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,74,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98]                       #英雄等级
  stages: [27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,17,17,17,17,17,17,17,17,17,17,17,17,15,15,15,15,15,15,15,15,15,15,0,0,0,0,0,0,0,0,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27]                       #英雄突破
  awaken_levels: [6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,6,0,0,0,6,0,6,0,0,0,0,0,0,6,0,0,0,6,0,0,0,0,6,0,0,0,6,0,0,0]                       #三系觉醒

  gemslot: [1,2]                        #宝石槽位
  gemlevel: [41,41]                       #宝石等级，和上面的槽位一一对应

# 英雄装备（已发放的装备的统一养成）
equip:
  strength: 176                          #装备强化
  refine: 24                             #装备精炼
  enchant: 10                            #装备附魔
  evolution: 0                          #装备进阶（红品质装备才有）

# 纹章（已发放的纹章的统一养成）
emblem:
  suitId: [10140,10240,10340,10440,20140,20240,20340,20440,30140,30240,30340,30440,40140,40240,40340,40440,50140,50240,50340,50440]                       #纹章系统id
  level: [100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100]                       #星级
  hero: [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]                       #无专属英雄填0，有专属英雄填随机池中的英雄系统ID

# 神器（已发放的神器的养成，为指定ID的神器设定养成）
artifact:
  ids: [71301,71302,71303,71401,71402,71403,71404,71405,71406,71501,71502,71503,71504,71505,71506,72501,71601,71602,71603,71604,71605,71606,71607,71608,71609,71610,71611,71612,71613,71614,71615,71616,71617,71618,71619,71620,71621,71622,71623,71624,71625,71626,71627,71628,71629,71630,71631]                       #神器系统id
  stars: [10,10,10,8,8,8,8,8,8,7,7,7,7,7,7,7,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3]                       #星级
  strength: [80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80]                       #强化

# 宠物（已发放的宠物的养成，为指定ID的宠物设定养成）
pokemon:
  ids: [130001,140001,150001,160001,230001,240001,250001,330001,340001,350001,430001,440001,450001,460001,530001,540001,550001,560001,630001,640001,650001]                       #神器系统id
  stars: [10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10]                       #星级

######################################################################################

# 主线当前关卡id
dungeon:
  id: 12460

# 爬塔当前关卡层数
tower:
  floor: 660

# 引导
guide:
  # 是否跳过所有引导 0-否 1-是
  skip: 1

# 竞技场分数
arena:
  score: 1350

# 水晶
#crystal:
  #crystal_achieve_task_ids: [1524101,1524102,1524103,1524104,1524105,1524106,1524201,1524202,1524203,1524204,1524205,1524206,1524301,1524302,1524303,1524101,1524102,1524103,1524104,1524105,1524106,1524201,1524202,1524203,1524204,1524101,1524102,1524103,1524104,1524105,1524106,1524101,1524102,1524103,1524101]
  #crystal_achieve_task_values: [6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,8,8,8,8,8,8,9,9,9,10]

# 条件爬塔
tower_star:
  dungeon_id: 160001    # 章节id

# 材料本
trial:
  types: [45,46,47,48]  # 材料本类型
  level: [23,23,23,23]   # 等级
  star: 1      # 星数: 材料本当前只有1star的概念

# 个人boss
mirage:
  hurdle_ids: [108,208,308,408,508,608]    # 关卡id，最多可填写6个boss的关卡id

# 公会技能
guildtalent:
  kinds: [1,2,3,4,5,6,7]           # 节点kind
  levels: [20,17,17,17,17,17,17]          # 等级，和kind一一对应

# 图鉴
handbook:
  handbook_task_ids: [13001012,13001300,13001400,14002012,14002300,14002400,13003012,13003300,13003400,11004012,11004300,11004400,12005012,12005300,12005400,12006012,12006300,12006400,12007012,12007300,12007400,22001012,22001300,22001400,23002012,23002300,23002400,21003012,21003300,21003400,22004012,22004300,22004400,24005012,24005300,24005400,23006012,23006300,23006400,22007012,22007300,22007400,23008012,23008300,23008400,32001012,32001300,32001400,31002012,31002300,31002400,34003012,34003300,34003400,33004012,33004300,33004400,32005012,32005300,32005400,34006012,34006300,34006400,34007012,34007300,34007400,42001012,42001200,42001300,42001400,43002012,43002200,43002300,43002400,41003012,41003200,41003300,41003400,44004012,44004200,44004300,44004400,43005012,43005200,43005300,43005400,43006012,43006200,43006300,43006400,43007012,43007200,43007300,43007400,53001012,53001200,53001300,53001400,52002012,52002200,52002300,52002400,51003012,51003200,51003300,51003400,54004012,54004200,54004300,54004400,52005012,52005200,52005300,52005400,53006012,53006200,53006300,53006400,53007012,53007200,53007300,53007400,11982300,11982400,13980300,13980400,13981300,13981400,21980300,21980400,22981300,22981400,23982300,23982400,32980300,32980400,33981300,33981400,44980300,44980400,43981300,43981400,52980300,52980400,51981300,51981400,11982009,13980009,13981009,21980009,22981009,23982009,32980009,33981009,44980009,43981009,52980009,51981009]                 # 图鉴type

#  契约之所
goddesscontract:
  exp: 45000                         # 通过exp来计算契约之所的level, exp配在goddess_contract_bless_info.xml中

# 遗物图鉴
remainbook:
  level: 1

# 赛季装备
season_jewelry:
  skill_rare: 60    # 稀有度