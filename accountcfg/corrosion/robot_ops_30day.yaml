## 玩家14天参考养成
# 玩家战队信息
base:
  level: 59                             #战队等级

######################################################################################

# 英雄（已发放的英雄的统一养成）
hero:
  ids: [32005,34006,21003,41003,51003]                          #英雄系统id
  stars: [9,7,7,7,7]                        #英雄星级（四星英雄星级上限是5，那么就只能升到5）
  levels: [59,59,59,59,59]                     #英雄等级
  stages: [11,10,10,10,10]                     #英雄突破
  awaken_levels: [0,0,0,0,0]                     #英雄觉醒等级
  gemslot: [1,2]                        #宝石槽位
  gemlevel: [31,31]                       #宝石等级，和上面的槽位一一对应

# 英雄装备（已发放的装备的统一养成）
equip:
  strength: 112                          #装备强化
  refine: 8                             #装备精炼
  enchant: 2                            #装备附魔
  evolution: 0                          #装备进阶（红品质装备才有）

# 纹章（已发放的纹章的统一养成）
emblem:
  suitId: [30140,30140,20240,40240,50240]                 #套装索引ID（背包里得有相应符文）
  level: [38,38,38,38,38]                        #对应套装的强化等级
  hero: [0,0,0,0,0]                       #无专属英雄填0，有专属英雄填随机池中的英雄系统ID

# 神器（已发放的神器的养成，为指定ID的神器设定养成）
artifact:
  ids: [71301,71302,71303,71401,71402,71403,71404,71501,71502]
  stars: [1,1,1,1,1,1,1,3,3]               #星级
  strength: [45,45,45,45,45,45,45,45,45]    #强化

######################################################################################

# 主线当前关卡id
dungeon:
  id: 11840

# 爬塔当前关卡层数
tower:
  floor: 400

# 引导
guide:
  # 是否跳过所有引导 0-否 1-是
  skip: 1

# 竞技场分数
arena:
  score: 1350

# 图鉴
handbook:
  handbook_task_ids: [13001006,14002006,13003006,13001007,14002007,13003007,13001008,13001009]                   # 图鉴任务id

# 条件爬塔
tower_star:
  dungeon_id: 160001    # 章节id

# 材料本
trial:
  types: [45,46,47,48]  # 材料本类型
  level: [13,13,13,13]    # 等级
  star: 1      # 星数: 材料本当前只有1star的概念

# 个人boss
mirage:
  hurdle_ids: [103,203,303,403,503,603]    # 关卡id，最多可填写6个boss的关卡id

# 公会技能
guildtalent:
  kinds: [1,2,3,4,5,6,7]           # 节点kind
  levels: [14,8,8,8,8,8,8]          # 等级，和kind一一对应


#  契约之所
goddesscontract:
  exp: 15000                         # 通过exp来计算契约之所的level, exp配在goddess_contract_bless_info.xml中

# 遗物图鉴
remainbook:
  level: 1