<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:编号 key=string:配置值 value=int:配置值 string_value=string:配置值 -->

<root>
    <data id="1" key="SUMMON_PLURAL" value="10" string_value="0" />
    <data id="2" key="HERO_RARE_LIMITED" value="50" string_value="0" />
    <data id="3" key="MAX_FIRST_RED_CARD_TIMES" value="50" string_value="0" />
    <data id="4" key="INT_CHANCE_60" value="0" string_value="0" />
    <data id="5" key="CHANCE_NODE_60" value="15" string_value="0" />
    <data id="6" key="CHANCE_ADD_60" value="19" string_value="0" />
    <data id="7" key="GROUP_ID_60_GUARANTEE" value="50" string_value="0" />
    <data id="8" key="INT_CHANCE_50" value="500" string_value="0" />
    <data id="9" key="CHANCE_NODE_50" value="0" string_value="0" />
    <data id="10" key="CHANCE_ADD_50" value="0" string_value="0" />
    <data id="11" key="GROUP_ID_50_GUARANTEE" value="10" string_value="0" />
    <data id="12" key="INT_CHANCE_40" value="4500" string_value="0" />
    <data id="13" key="CHANCE_NODE_40" value="0" string_value="0" />
    <data id="14" key="CHANCE_ADD_40" value="0" string_value="0" />
    <data id="15" key="GROUP_ID_40_GUARANTEE" value="10" string_value="0" />
    <data id="16" key="INT_FRAGMENT_CHANCE_60" value="1000" string_value="0" />
    <data id="17" key="FRAGMENT_CHANCE_NODE_60" value="0" string_value="0" />
    <data id="18" key="FRAGMENT_CHANCE_ADD_60" value="0" string_value="0" />
    <data id="19" key="FRAGMENT_GROUP_ID_60_GUARANTEE" value="9999" string_value="0" />
    <data id="20" key="UP_HREO_GUARANTEE" value="1" string_value="0" />
    <data id="21" key="RED_SUMMON_REDUCE_COUNTS" value="0" string_value="2:10000" />
    <data id="22" key="FREE_TIMES" value="1" string_value="0" />
</root>
