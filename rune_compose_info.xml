<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:编号 target_rune=int:目标符文 rune_id=int:合成消耗符文 count_1=int:消耗数量1 chance_1=int:合成真实概率权重1 res_type_1=int:合成消耗资源类型1 res_id_1=int:合成消耗资源ID1 res_count_1=int:合成消耗资源数量1 fail_type_1=int:失败返还材料类型1 fail_id_1=int:失败返还材料ID1 fail_count_1=int:失败返还材料数量1 count_2=int:消耗数量2 chance_2=int:合成真实概率权重2 res_type_2=int:合成消耗资源类型2 res_id_2=int:合成消耗资源ID2 res_count_2=int:合成消耗资源数量2 fail_type_2=int:失败返还材料类型2 fail_id_2=int:失败返还材料ID2 fail_count_2=int:失败返还材料数量2 count_3=int:消耗数量3 chance_3=int:合成真实概率权重3 res_type_3=int:合成消耗资源类型3 res_id_3=int:合成消耗资源ID3 res_count_3=int:合成消耗资源数量3 fail_type_3=int:失败返还材料类型3 fail_id_3=int:失败返还材料ID3 fail_count_3=int:失败返还材料数量3 count_4=int:消耗数量4 chance_4=int:合成真实概率权重4 res_type_4=int:合成消耗资源类型4 res_id_4=int:合成消耗资源ID4 res_count_4=int:合成消耗资源数量4 fail_type_4=int:失败返还材料类型4 fail_id_4=int:失败返还材料ID4 fail_count_4=int:失败返还材料数量4 -->

<root>
    <data id="1" target_rune="800003" rune_id="800002" count_1="2" chance_1="2000" res_type_1="3" res_id_1="0" res_count_1="20000" fail_type_1="3" fail_id_1="0" fail_count_1="4" count_2="3" chance_2="4000" res_type_2="3" res_id_2="0" res_count_2="40000" fail_type_2="3" fail_id_2="0" fail_count_2="6" count_3="4" chance_3="7000" res_type_3="3" res_id_3="0" res_count_3="60000" fail_type_3="3" fail_id_3="0" fail_count_3="8" count_4="5" chance_4="10000" res_type_4="4" res_id_4="0" res_count_4="80000" fail_type_4="3" fail_id_4="0" fail_count_4="10" />
    <data id="2" target_rune="800004" rune_id="800003" count_1="2" chance_1="2000" res_type_1="3" res_id_1="0" res_count_1="100000" fail_type_1="3" fail_id_1="0" fail_count_1="20" count_2="3" chance_2="4000" res_type_2="3" res_id_2="0" res_count_2="200000" fail_type_2="3" fail_id_2="0" fail_count_2="30" count_3="4" chance_3="7000" res_type_3="3" res_id_3="0" res_count_3="300000" fail_type_3="3" fail_id_3="0" fail_count_3="40" count_4="5" chance_4="10000" res_type_4="4" res_id_4="0" res_count_4="400000" fail_type_4="3" fail_id_4="0" fail_count_4="50" />
    <data id="3" target_rune="800005" rune_id="800004" count_1="2" chance_1="2000" res_type_1="3" res_id_1="0" res_count_1="200000" fail_type_1="3" fail_id_1="0" fail_count_1="100" count_2="3" chance_2="4000" res_type_2="3" res_id_2="0" res_count_2="400000" fail_type_2="3" fail_id_2="0" fail_count_2="150" count_3="4" chance_3="7000" res_type_3="3" res_id_3="0" res_count_3="600000" fail_type_3="3" fail_id_3="0" fail_count_3="200" count_4="5" chance_4="10000" res_type_4="4" res_id_4="0" res_count_4="800000" fail_type_4="3" fail_id_4="0" fail_count_4="250" />
    <data id="4" target_rune="800006" rune_id="800005" count_1="2" chance_1="2000" res_type_1="3" res_id_1="0" res_count_1="400000" fail_type_1="3" fail_id_1="0" fail_count_1="1000" count_2="3" chance_2="4000" res_type_2="3" res_id_2="0" res_count_2="800000" fail_type_2="3" fail_id_2="0" fail_count_2="1500" count_3="4" chance_3="7000" res_type_3="3" res_id_3="0" res_count_3="1200000" fail_type_3="3" fail_id_3="0" fail_count_3="2000" count_4="5" chance_4="10000" res_type_4="4" res_id_4="0" res_count_4="1600000" fail_type_4="3" fail_id_4="0" fail_count_4="2500" />
    <data id="5" target_rune="800007" rune_id="800006" count_1="2" chance_1="2000" res_type_1="3" res_id_1="0" res_count_1="600000" fail_type_1="3" fail_id_1="0" fail_count_1="10000" count_2="3" chance_2="4000" res_type_2="3" res_id_2="0" res_count_2="1200000" fail_type_2="3" fail_id_2="0" fail_count_2="15000" count_3="4" chance_3="7000" res_type_3="3" res_id_3="0" res_count_3="1800000" fail_type_3="3" fail_id_3="0" fail_count_3="20000" count_4="5" chance_4="10000" res_type_4="4" res_id_4="0" res_count_4="2400000" fail_type_4="3" fail_id_4="0" fail_count_4="25000" />
</root>
