## 卡牌服务器框架图
![](docs/image/CardServerFramework.jpg)

## Environment
* centos 7.6
* go version go1.14
* [golangci-lint@v1.23.8](https://github.com/golangci/golangci-lint)
* goimports
* [protobuf@v3.5.1](https://github.com/protocolbuffers/protobuf/tree/v3.5.1)
* [go protobuf@1.3.2](https://github.com/golang/protobuf)

~~~
yum install -y zlib-devel clang.x86_64

/etc/bashrc
export PATH=/usr/local/go/bin:$PATH
export GO111MODULE=on
export GOPROXY=https://goproxy.cn,direct
export GOPRIVATE=1************,*.qdream.com
export GOINSECURE=1************,*.qdream.com

.bashrc
export PATH=$HOME/go/bin:$PATH
export TMPDIR=$HOME/tmp

go get github.com/golangci/golangci-lint/cmd/golangci-lint@v1.23.8
go get golang.org/x/tools/cmd/goimports
~~~

$HOME/.gitconfig
~~~
[url "************************:"]
      insteadOf = http://gitlab.qdreaming.com/
~~~

## Usage
#### 1. ETCD 
1. 启动etcd `主程配置`
~~~
etcd --listen-client-urls http://*************:2379 --advertise-client-urls http://*************:2379
    * ************* 修改为目标服务器ip
~~~
2. 配置gateway `主程配置`
~~~
etcdctl --endpoints=*************:2379 put /game/x/gateway/node/*************:38422 '{
    "id": 1,
    "weight": 1,
    "domain": "*************",
    "port": "38422"
}'
    * id: gateway service id `最大值20, 21~31为保留段`
    * weight: 服务权重
    * domain: 服务器域名
    * port: 对外的监听端口
    * /game/x/gateway/node/*************:38422 修改为目标网关ip与client port : /game/x/gateway/node/ip:port
~~~
3. 配置parrot `主程配置`
~~~
etcdctl --endpoints=*************:2379 put /game/x/parrot/node/*************:48422 '{"weight" : 1, "domain" : "*************", "port": "48422"}'
~~~
4. 配置world(跨服)
~~~
etcdctl --endpoints=*************:2379 put /game/x/world/node/wb/*************:30001 '{"redis" : "127.0.0.1:9898", "password" : "ngame", "redis-index" : 1, "modules":[1]}'
    * modules 代表该跨服开启的功能模块,从1开始
    * wb 这里代表不同平台，内网开发代表自己的服务器，用自己的账号名称区分开就好
    * 30001端口可以换成自己的，替换最后一位为自己的node-id 
    * redis-index 也用自己分配的redis-index
~~~
5. 配置logic(游戏服务)
~~~
etcdctl --endpoints=*************:2379 put /game/x/logic/node/*************:10001 '{
    "server-id": **********,
    "app-id": 1,
    "op-group": 0,
    "node-id": 1,
    "redis": "127.0.0.1:9898",
    "redis-index": 1,
    "start-service-tm": **********,
    "gateway-cluster": {
        "etcd": "*************:2379",
        "target": "/game/x/gateway/node/",
        "guard": ""
    },
    "world-cluster": {
        "etcd": "*************:2379",
        "target": "/game/x/world/node/wb/"
    },
    "service": {
        "account-size": 20000,
        "online-size": 5000,
        "queue-pop-size": 10,
        "max-cache-entries": 5000,
        "user-save-interval": 50,
        "queue-size": 2000,
        "enable-trace-log": true,
        "ip-accounts": 100,
        "device-id-accounts": 100,
        "ip-accounts-hw": 20,
        "device-id-accounts-hw": 10
    }
}'
     * *************:10001 修改为logic配置HTTP服务器地址 (每个Logic对应一个唯一的Address)
     * server-id：fmt.Sprintf("%d%04d%05d", app-id, op-group, node-id) 
     * node-id: 区服id，保证唯一，最大值32767(15bit)
     * world-cluster: target内网开发都用自己的节点，大家跨服分开使用
     * ip和device-id的限制配置为0就代表无限制
     * start-service-tm：首次开服时间，格式为整型**********(2020-09-18 18:18:18)，开服后不可以修改（会影响轮次商店数据刷新）
~~~

----
#### 2. RedisLV
* 启动RedisLV服务（默认端口使用9898，每个游戏服对应唯一的RedisLV）

----
#### 3. Game
1. make `（仅在首次安装通用库和所有服务时，使用命令： make init）`
2. cd bin
3. **start**
    1. `主程负责`
    ~~~
    ./cmd.sh start gateway -client_port=38422
    ./cmd.sh start parrot -addr=:48422 -etcd=*************:2379 -key=/game/x/parrot/alive/*************:48422 -debug=:22321 
    ./cmd.sh start eagle -addr=:8080 -etcd=*************:2379 -keys=gateway,parrot -debug=:12321 
    ~~~
    2. `开发人员负责`
    ~~~
	./cmd.sh start service -name=world -http=:30001 -grpc=:40001
    ./cmd.sh start service -name=logic -http=:10001 -grpc=:20001
    ./robot 
        * world启动一次就行了，后续world没有修改可以不用stop
        * world的端口30001要和etcd里配置的一致，后续的可以换成30002， 40001同理
    ~~~
4. **stop**
    1. `开发人员负责`
    ~~~
	./cmd.sh stop service -name=world -http=:30001 -grpc=:40001
    ./cmd.sh stop service -name=logic -http=:10001 -grpc=:20001
        * world启动一次就行了，后续world没有修改可以不用stop
    ~~~
    2. `主程负责`
    ~~~
    ./cmd.sh stop eagle -addr=:8080 -etcd=*************:2379 -keys=gateway,parrot -debug=:12321
    ./cmd.sh stop parrot -addr=:48422 -etcd=*************:2379 -key=/game/x/parrot/alive/*************:48422 -debug=:22321
    ./cmd.sh stop gateway -client_port=38422
    ~~~
    
## 游戏协议
![](docs/gameproto.jpg)
* `Len` `Proto` `UID` `CSeq` `SSeq` `Flags` 均使用大端编码
* ProtoData - Protobuf Marshal

## pkg说明
* proto -> github.com/gogo/protobuf/proto
* time -> gitlab.qdreaming.com/kit/sea/time
