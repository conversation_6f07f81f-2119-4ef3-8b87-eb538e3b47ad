<?xml version="1.0" encoding="UTF-8"?>
<config>
	<version>2041000</version> <!-- v1.3.2 => 1003002 -->
	<gm>0</gm> <!-- GM权限: 0表示关闭(服务器上线需要关闭) -->
	<name_length>5</name_length> <!--玩家名字长度-->
	<app_key>KPEPtEe2</app_key> <!-- 登录密钥，由SDK提供 -->
	<exceed_time>172800</exceed_time> <!-- 登录超时时间，单位秒 -->
	<resource_log_time>01:00-01:30</resource_log_time> <!-- 资源日志收集的时间 -->
	<monitor_cmds_interval>60</monitor_cmds_interval> <!-- 定时记录玩家协议 -->
	<redis_password>ngame</redis_password>
	<command_limit>
		<!--协议操作间隔限制 id-协议id interval-最低间隔时间(毫秒)，小于则判定请求太快-->
		<!--注意被判断太快会丢弃协议，客户端会转圈处理，所以间隔不能太严苛-->
		<!--cmd id="11029" interval="10000"/-->
		<cmd id="18301" interval="500" /> <!-- 公测返利请求数据 -->
		<cmd id="18303" interval="500" /> <!-- 公测返利奖励领取 -->
	</command_limit>
	<data_version>2025-02-28 19:18:10 +0800 @9c6fb51ade36e9e7e9fe03cfdff6592d04f02d24</data_version> <!-- 数据表版本号.不要手动修改.默认保持为空 -->
</config>
