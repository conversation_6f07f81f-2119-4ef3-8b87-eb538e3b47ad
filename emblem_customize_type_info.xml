<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:id type_group_id=int:套装组id type_id=int:套装id -->

<root>
    <data id="1" type_group_id="1" type_id="101" />
    <data id="2" type_group_id="1" type_id="104" />
    <data id="3" type_group_id="1" type_id="102" />
    <data id="4" type_group_id="1" type_id="103" />
    <data id="5" type_group_id="2" type_id="201" />
    <data id="6" type_group_id="2" type_id="204" />
    <data id="7" type_group_id="2" type_id="202" />
    <data id="8" type_group_id="2" type_id="203" />
    <data id="9" type_group_id="3" type_id="301" />
    <data id="10" type_group_id="3" type_id="304" />
    <data id="11" type_group_id="3" type_id="302" />
    <data id="12" type_group_id="3" type_id="303" />
    <data id="13" type_group_id="4" type_id="401" />
    <data id="14" type_group_id="4" type_id="402" />
    <data id="15" type_group_id="4" type_id="403" />
    <data id="16" type_group_id="4" type_id="404" />
    <data id="17" type_group_id="5" type_id="501" />
    <data id="18" type_group_id="5" type_id="502" />
    <data id="19" type_group_id="5" type_id="503" />
    <data id="20" type_group_id="5" type_id="504" />
    <data id="21" type_group_id="6" type_id="101" />
    <data id="22" type_group_id="6" type_id="104" />
    <data id="23" type_group_id="6" type_id="102" />
    <data id="24" type_group_id="6" type_id="103" />
    <data id="25" type_group_id="7" type_id="201" />
    <data id="26" type_group_id="7" type_id="204" />
    <data id="27" type_group_id="7" type_id="202" />
    <data id="28" type_group_id="7" type_id="203" />
    <data id="29" type_group_id="8" type_id="301" />
    <data id="30" type_group_id="8" type_id="304" />
    <data id="31" type_group_id="8" type_id="302" />
    <data id="32" type_group_id="8" type_id="303" />
    <data id="33" type_group_id="9" type_id="401" />
    <data id="34" type_group_id="9" type_id="402" />
    <data id="35" type_group_id="9" type_id="403" />
    <data id="36" type_group_id="9" type_id="404" />
    <data id="37" type_group_id="10" type_id="501" />
    <data id="38" type_group_id="10" type_id="502" />
    <data id="39" type_group_id="10" type_id="503" />
    <data id="40" type_group_id="10" type_id="504" />
    <data id="41" type_group_id="11" type_id="101" />
    <data id="42" type_group_id="11" type_id="104" />
    <data id="43" type_group_id="11" type_id="102" />
    <data id="44" type_group_id="11" type_id="103" />
    <data id="45" type_group_id="12" type_id="201" />
    <data id="46" type_group_id="12" type_id="204" />
    <data id="47" type_group_id="12" type_id="202" />
    <data id="48" type_group_id="12" type_id="203" />
    <data id="49" type_group_id="13" type_id="301" />
    <data id="50" type_group_id="13" type_id="304" />
    <data id="51" type_group_id="13" type_id="302" />
    <data id="52" type_group_id="13" type_id="303" />
    <data id="53" type_group_id="14" type_id="401" />
    <data id="54" type_group_id="14" type_id="402" />
    <data id="55" type_group_id="14" type_id="403" />
    <data id="56" type_group_id="14" type_id="404" />
    <data id="57" type_group_id="15" type_id="501" />
    <data id="58" type_group_id="15" type_id="502" />
    <data id="59" type_group_id="15" type_id="503" />
    <data id="60" type_group_id="15" type_id="504" />
    <data id="61" type_group_id="16" type_id="101" />
    <data id="62" type_group_id="16" type_id="104" />
    <data id="63" type_group_id="16" type_id="102" />
    <data id="64" type_group_id="16" type_id="103" />
    <data id="65" type_group_id="17" type_id="201" />
    <data id="66" type_group_id="17" type_id="204" />
    <data id="67" type_group_id="17" type_id="202" />
    <data id="68" type_group_id="17" type_id="203" />
    <data id="69" type_group_id="18" type_id="301" />
    <data id="70" type_group_id="18" type_id="304" />
    <data id="71" type_group_id="18" type_id="302" />
    <data id="72" type_group_id="18" type_id="303" />
    <data id="73" type_group_id="19" type_id="401" />
    <data id="74" type_group_id="19" type_id="402" />
    <data id="75" type_group_id="19" type_id="403" />
    <data id="76" type_group_id="19" type_id="404" />
    <data id="77" type_group_id="20" type_id="501" />
    <data id="78" type_group_id="20" type_id="502" />
    <data id="79" type_group_id="20" type_id="503" />
    <data id="80" type_group_id="20" type_id="504" />
    <data id="81" type_group_id="21" type_id="101" />
    <data id="82" type_group_id="21" type_id="104" />
    <data id="83" type_group_id="21" type_id="102" />
    <data id="84" type_group_id="21" type_id="103" />
    <data id="85" type_group_id="22" type_id="201" />
    <data id="86" type_group_id="22" type_id="204" />
    <data id="87" type_group_id="22" type_id="202" />
    <data id="88" type_group_id="22" type_id="203" />
    <data id="89" type_group_id="23" type_id="301" />
    <data id="90" type_group_id="23" type_id="304" />
    <data id="91" type_group_id="23" type_id="302" />
    <data id="92" type_group_id="23" type_id="303" />
    <data id="93" type_group_id="24" type_id="401" />
    <data id="94" type_group_id="24" type_id="402" />
    <data id="95" type_group_id="24" type_id="403" />
    <data id="96" type_group_id="24" type_id="404" />
    <data id="97" type_group_id="25" type_id="501" />
    <data id="98" type_group_id="25" type_id="502" />
    <data id="99" type_group_id="25" type_id="503" />
    <data id="100" type_group_id="25" type_id="504" />
</root>
