// 文件从Gateway中Copy, 禁止修改
syntax = "proto3";

option go_package = "app/protos/out/cg";
package cg;

enum RET {
  ERROR = 0;
  OK = 1;
  SERVER_MAINTAIN = 2;               // 服务器维护中
  USER_NOT_EXIST = 3;                // 玩家不存在
  USER_NAME_REPEAT = 4;              // 创角时，角色名字重复
  LOGIN_REPEAT = 5;                  // 重复登录
  QUEUING = 6;                       // 排队中
  ACCOUNT_SIZE_OUT_OF_LIMIT = 7;     // 账号数量超过限制
  GATEWAY_CLIENTS_OUT_OF_LIMIT = 8;  // 当前网关服务器人数超上限
}

enum ID {
  MSG_NONE = 0;
  MSG_LOGIC_MIN = 10000;
  MSG_LOGIC_MAX = 100000;
  MSG_BEGIN = 10001;
  MSG_END = 10999;
  // 服务器与服务器之间使用
  MSG_C2G_SayHi = 10002;
  MSG_G2C_SayHi = 10003;
  MSG_C2G_KeepAlive = 10004;
  MSG_G2C_KeepAlive = 10005;
  // 客户端与服务器之间使用
  MSG_C2G_Login = 10100;
  MSG_G2C_Login = 10101;
  MSG_C2G_Create = 10102;
  MSG_G2C_Create = 10103;
  MSG_C2G_Offline = 10104;
  MSG_G2C_Offline = 10105;
  MSG_G2C_Broadcast = 10106;
  MSG_C2G_QueryQueue = 10107;
  MSG_G2C_QueryQueue = 10108;
  MSG_C2G_Ping = 10109;
  MSG_G2C_Pong = 10110;
}

/************** server <> server ****************/
message C2G_SayHi {
  uint64 id = 1;
  repeated uint64 current = 2;
  repeated uint64 del = 3;
}

message G2C_SayHi {}

message C2G_KeepAlive {
  string extra = 1;  // json
}

message G2C_KeepAlive {
  string extra = 1;  // json
}

/************** client <> server ****************/
message C2G_Login {
  string token = 1;
  uint64 server_id = 2;
  uint64 version = 3;      // 客户端版本号
  uint64 sync_cookie = 4;  // 消息增量发送机制
  string net_addr = 5;     // 客户端网络地址
  string extra = 6;        // json
}

message G2C_Login {
  uint32 ret = 1;
  uint64 uid = 2;
  uint64 version = 3;      // 服务器端版本号
  uint64 sync_cookie = 4;  // 消息增量发送机制中使用的cookie
  uint32 sync = 5;         // 0: 表示需要全部flush; >0 : 表示可以增量同步
  string extra = 6;        // json
}

message C2G_Create {
  string name = 1;
  uint32 type = 2;           // 角色类型
  repeated uint32 face = 3;  // 捏脸
  string extra = 4;          // json
}

message G2C_Create {
  uint32 ret = 1;
  uint64 uid = 2;
  uint64 sync_cookie = 3;  // 消息增量发送机制中使用的cookie
  uint32 sync = 4;         // 0: 表示需要全部flush; >0 : 表示可以增量同步
  string extra = 5;        // json
}

message C2G_Offline {}

message G2C_Offline {}

message G2C_Broadcast {
  repeated uint64 ids = 1;  // client session ids
  uint32 cmd = 2;           // 需要广播的消息ID
  bytes info = 3;           // 需要广播的消息体
}

message C2G_QueryQueue {}

message G2C_QueryQueue {
  uint32 len = 1;  // 当前位置，0表示已经登录
}

message C2G_Ping {}

message G2C_Pong {}
