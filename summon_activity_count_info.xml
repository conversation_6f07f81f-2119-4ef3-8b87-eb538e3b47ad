<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:编号 count_type=int:抽卡计数类型 guarantee_number=int:活动周期内触发保底计数 summon_number=int:活动周期内抽卡数量 guarantee_class=int:触发活动保底的抽取池 guarantee_rare=int:活动保底品质 -->

<root>
    <data id="30101" count_type="301" guarantee_number="0" summon_number="60" guarantee_class="10306" guarantee_rare="60" />
    <data id="30102" count_type="301" guarantee_number="1" summon_number="60" guarantee_class="10306" guarantee_rare="60" />
    <data id="30103" count_type="301" guarantee_number="2" summon_number="60" guarantee_class="10306" guarantee_rare="60" />
    <data id="50101" count_type="501" guarantee_number="0" summon_number="30" guarantee_class="10506" guarantee_rare="60" />
    <data id="50102" count_type="501" guarantee_number="1" summon_number="30" guarantee_class="10506" guarantee_rare="60" />
    <data id="50103" count_type="501" guarantee_number="2" summon_number="30" guarantee_class="10506" guarantee_rare="60" />
    <data id="60101" count_type="601" guarantee_number="0" summon_number="30" guarantee_class="10606" guarantee_rare="60" />
    <data id="60102" count_type="601" guarantee_number="1" summon_number="30" guarantee_class="10606" guarantee_rare="60" />
    <data id="60103" count_type="601" guarantee_number="2" summon_number="30" guarantee_class="10606" guarantee_rare="60" />
    <data id="70101" count_type="701" guarantee_number="0" summon_number="30" guarantee_class="10706" guarantee_rare="60" />
    <data id="70102" count_type="701" guarantee_number="1" summon_number="30" guarantee_class="10706" guarantee_rare="60" />
    <data id="70103" count_type="701" guarantee_number="2" summon_number="30" guarantee_class="10706" guarantee_rare="60" />
    <data id="80101" count_type="801" guarantee_number="0" summon_number="60" guarantee_class="10806" guarantee_rare="60" />
    <data id="80102" count_type="801" guarantee_number="1" summon_number="60" guarantee_class="10806" guarantee_rare="60" />
    <data id="80103" count_type="801" guarantee_number="2" summon_number="60" guarantee_class="10806" guarantee_rare="60" />
    <data id="140101" count_type="1401" guarantee_number="0" summon_number="60" guarantee_class="11405" guarantee_rare="50" />
    <data id="140102" count_type="1401" guarantee_number="1" summon_number="80" guarantee_class="11405" guarantee_rare="50" />
    <data id="170101" count_type="1701" guarantee_number="0" summon_number="50" guarantee_class="0" guarantee_rare="60" />
    <data id="170102" count_type="1701" guarantee_number="1" summon_number="75" guarantee_class="0" guarantee_rare="60" />
    <data id="170103" count_type="1701" guarantee_number="2" summon_number="75" guarantee_class="0" guarantee_rare="60" />
    <data id="30201" count_type="302" guarantee_number="0" summon_number="60" guarantee_class="10306" guarantee_rare="60" />
    <data id="30202" count_type="302" guarantee_number="1" summon_number="60" guarantee_class="10306" guarantee_rare="60" />
    <data id="30203" count_type="302" guarantee_number="2" summon_number="60" guarantee_class="10306" guarantee_rare="60" />
    <data id="50201" count_type="502" guarantee_number="0" summon_number="30" guarantee_class="10506" guarantee_rare="60" />
    <data id="50202" count_type="502" guarantee_number="1" summon_number="30" guarantee_class="10506" guarantee_rare="60" />
    <data id="50203" count_type="502" guarantee_number="2" summon_number="30" guarantee_class="10506" guarantee_rare="60" />
    <data id="60201" count_type="602" guarantee_number="0" summon_number="30" guarantee_class="10606" guarantee_rare="60" />
    <data id="60202" count_type="602" guarantee_number="1" summon_number="30" guarantee_class="10606" guarantee_rare="60" />
    <data id="60203" count_type="602" guarantee_number="2" summon_number="30" guarantee_class="10606" guarantee_rare="60" />
    <data id="70201" count_type="702" guarantee_number="0" summon_number="30" guarantee_class="10706" guarantee_rare="60" />
    <data id="70202" count_type="702" guarantee_number="1" summon_number="30" guarantee_class="10706" guarantee_rare="60" />
    <data id="70203" count_type="702" guarantee_number="2" summon_number="30" guarantee_class="10706" guarantee_rare="60" />
    <data id="80201" count_type="802" guarantee_number="0" summon_number="60" guarantee_class="10806" guarantee_rare="60" />
    <data id="80202" count_type="802" guarantee_number="1" summon_number="60" guarantee_class="10806" guarantee_rare="60" />
    <data id="80203" count_type="802" guarantee_number="2" summon_number="60" guarantee_class="10806" guarantee_rare="60" />
    <data id="140201" count_type="1402" guarantee_number="0" summon_number="60" guarantee_class="11405" guarantee_rare="50" />
    <data id="140202" count_type="1402" guarantee_number="1" summon_number="80" guarantee_class="11405" guarantee_rare="50" />
    <data id="170201" count_type="1702" guarantee_number="0" summon_number="50" guarantee_class="0" guarantee_rare="60" />
    <data id="170202" count_type="1702" guarantee_number="1" summon_number="75" guarantee_class="0" guarantee_rare="60" />
    <data id="170203" count_type="1702" guarantee_number="2" summon_number="75" guarantee_class="0" guarantee_rare="60" />
</root>
