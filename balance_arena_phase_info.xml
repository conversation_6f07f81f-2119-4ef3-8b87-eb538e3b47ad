<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:序号 phase_open=int:阶段开始时间（开赛后多少秒） phase_close=int:阶段结束时间（开赛后多少秒） if_set_formation=int:是否可以布阵 phase_type=int:主要阶段 phase_stage=int:次要阶段 battle_num=int:战斗场次 -->

<root>
    <data id="1" phase_open="0" phase_close="86400" if_set_formation="0" phase_type="1" phase_stage="0" battle_num="0" />
    <data id="2" phase_open="86400" phase_close="172800" if_set_formation="1" phase_type="2" phase_stage="0" battle_num="0" />
    <data id="3" phase_open="172800" phase_close="212400" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="1" />
    <data id="4" phase_open="212400" phase_close="214200" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="1" />
    <data id="5" phase_open="214200" phase_close="216000" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="2" />
    <data id="6" phase_open="216000" phase_close="217800" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="2" />
    <data id="7" phase_open="217800" phase_close="219600" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="3" />
    <data id="8" phase_open="219600" phase_close="221400" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="3" />
    <data id="9" phase_open="221400" phase_close="223200" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="4" />
    <data id="10" phase_open="223200" phase_close="225000" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="4" />
    <data id="11" phase_open="225000" phase_close="226800" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="5" />
    <data id="12" phase_open="226800" phase_close="228600" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="5" />
    <data id="13" phase_open="228600" phase_close="230400" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="6" />
    <data id="14" phase_open="230400" phase_close="232200" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="6" />
    <data id="15" phase_open="232200" phase_close="234000" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="7" />
    <data id="16" phase_open="234000" phase_close="235800" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="7" />
    <data id="17" phase_open="235800" phase_close="237600" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="8" />
    <data id="18" phase_open="237600" phase_close="239400" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="8" />
    <data id="19" phase_open="239400" phase_close="241200" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="9" />
    <data id="20" phase_open="241200" phase_close="243000" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="9" />
    <data id="21" phase_open="243000" phase_close="244800" if_set_formation="1" phase_type="3" phase_stage="1" battle_num="10" />
    <data id="22" phase_open="244800" phase_close="246600" if_set_formation="0" phase_type="3" phase_stage="2" battle_num="10" />
    <data id="23" phase_open="246600" phase_close="259200" if_set_formation="1" phase_type="3" phase_stage="3" battle_num="10" />
    <data id="24" phase_open="259200" phase_close="302400" if_set_formation="1" phase_type="4" phase_stage="1" battle_num="1" />
    <data id="25" phase_open="302400" phase_close="304200" if_set_formation="0" phase_type="4" phase_stage="2" battle_num="1" />
    <data id="26" phase_open="304200" phase_close="306000" if_set_formation="1" phase_type="4" phase_stage="1" battle_num="2" />
    <data id="27" phase_open="306000" phase_close="307800" if_set_formation="0" phase_type="4" phase_stage="2" battle_num="2" />
    <data id="28" phase_open="307800" phase_close="309600" if_set_formation="1" phase_type="4" phase_stage="1" battle_num="3" />
    <data id="29" phase_open="309600" phase_close="311400" if_set_formation="0" phase_type="4" phase_stage="2" battle_num="3" />
    <data id="30" phase_open="311400" phase_close="313200" if_set_formation="1" phase_type="4" phase_stage="1" battle_num="4" />
    <data id="31" phase_open="313200" phase_close="315000" if_set_formation="0" phase_type="4" phase_stage="2" battle_num="4" />
    <data id="32" phase_open="315000" phase_close="316800" if_set_formation="1" phase_type="4" phase_stage="1" battle_num="5" />
    <data id="33" phase_open="316800" phase_close="318600" if_set_formation="0" phase_type="4" phase_stage="2" battle_num="5" />
    <data id="34" phase_open="318600" phase_close="320400" if_set_formation="1" phase_type="4" phase_stage="1" battle_num="6" />
    <data id="35" phase_open="320400" phase_close="322200" if_set_formation="0" phase_type="4" phase_stage="2" battle_num="6" />
    <data id="36" phase_open="322200" phase_close="324000" if_set_formation="1" phase_type="4" phase_stage="1" battle_num="7" />
    <data id="37" phase_open="324000" phase_close="325800" if_set_formation="0" phase_type="4" phase_stage="2" battle_num="7" />
    <data id="38" phase_open="325800" phase_close="345600" if_set_formation="1" phase_type="4" phase_stage="3" battle_num="7" />
    <data id="39" phase_open="345600" phase_close="388800" if_set_formation="1" phase_type="5" phase_stage="1" battle_num="1" />
    <data id="40" phase_open="388800" phase_close="390600" if_set_formation="0" phase_type="5" phase_stage="2" battle_num="1" />
    <data id="41" phase_open="390600" phase_close="403200" if_set_formation="1" phase_type="5" phase_stage="1" battle_num="2" />
    <data id="42" phase_open="403200" phase_close="405000" if_set_formation="0" phase_type="5" phase_stage="2" battle_num="2" />
    <data id="43" phase_open="405000" phase_close="417600" if_set_formation="1" phase_type="5" phase_stage="1" battle_num="3" />
    <data id="44" phase_open="417600" phase_close="419400" if_set_formation="0" phase_type="5" phase_stage="2" battle_num="3" />
    <data id="45" phase_open="419400" phase_close="432000" if_set_formation="1" phase_type="5" phase_stage="3" battle_num="3" />
    <data id="46" phase_open="432000" phase_close="460800" if_set_formation="1" phase_type="6" phase_stage="1" battle_num="4" />
    <data id="47" phase_open="460800" phase_close="462600" if_set_formation="0" phase_type="6" phase_stage="2" battle_num="4" />
    <data id="48" phase_open="462600" phase_close="475200" if_set_formation="1" phase_type="6" phase_stage="1" battle_num="5" />
    <data id="49" phase_open="475200" phase_close="477000" if_set_formation="0" phase_type="6" phase_stage="2" battle_num="5" />
    <data id="50" phase_open="477000" phase_close="489600" if_set_formation="1" phase_type="6" phase_stage="1" battle_num="6" />
    <data id="51" phase_open="489600" phase_close="491400" if_set_formation="0" phase_type="6" phase_stage="2" battle_num="6" />
    <data id="52" phase_open="491400" phase_close="504000" if_set_formation="1" phase_type="7" phase_stage="1" battle_num="7" />
    <data id="53" phase_open="504000" phase_close="505800" if_set_formation="0" phase_type="7" phase_stage="2" battle_num="7" />
    <data id="54" phase_open="505800" phase_close="518400" if_set_formation="0" phase_type="7" phase_stage="3" battle_num="0" />
    <data id="55" phase_open="518400" phase_close="604800" if_set_formation="0" phase_type="8" phase_stage="0" battle_num="0" />
</root>
