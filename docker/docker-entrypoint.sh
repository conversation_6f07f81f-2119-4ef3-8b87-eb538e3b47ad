#!/bin/bash

# 获取主机用户id
USER_ID=${LOCAL_USER_ID:-9001}
GROUP_ID=${LOCAL_GROUP_ID:-9001}
# 给主机用户授权制定的非绑定挂载目录
chown -R $USER_ID:$GROUP_ID /project

# 创建和主机用户相同uid的用户，名为user
useradd --shell /bin/bash -u $USER_ID -o -c "" -m dev
usermod -a -G root dev
export HOME=/home/<USER>

mkdir /home/<USER>/tmp

export GOPATH=/project/go
export PATH=/project/go/bin:$PATH
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

mkdir /home/<USER>/.ssh
cp -r /project/.ssh/* /home/<USER>/.ssh/
cp /project/.gitconfig /home/<USER>/.gitconfig

chown -R $USER_ID:$GROUP_ID /home/<USER>

exec /usr/local/bin/gosu dev "$@"
