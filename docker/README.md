1. 创建镜像。

   这个正常情况下大家不需要执行,已经执行过了，并且推送到了本地的仓库。如果需要修改让主程来执行

   ```bash
   sudo docker build -t ngamedev:1.0  ./
   ```

2. 正常修改代码.

3. 执行编译

   build.sh后面的参数 代表是make的target 不传就是make service
   比如要全部重新编译就执行./build.sh all
   只修改代表就执行./build.sh service

   ```bash
   ./build.sh all
   ```

   build.sh脚本的解释说明

   LOCAL_USER_ID LOCAL_GROUP_ID 环境变量 设定docker里执行的时候用宿主机相同的用户,让编译出来的程序保持和宿主机权限一致

   --add-host设定域名解析

   -v 挂在工作目录。在宿主机上git lone 代码之后 挂在到/project/ngame/server

   -v 挂在go mod 缓存目录防止每次重启都需要重新下载。自己本地的目录到/project/go/pkg

   -v 挂载.ssh目录。先在宿主机上使用ssh免密登录gitlab. 然后挂在到/project/.ssh。 最后会通过entry.sh脚本复制到/home/<USER>/.ssh里面

   ```bash
	sudo docker run --rm -e LOCAL_USER_ID=$(id -u $USER) -e LOCAL_GROUP_ID=$(id -g $USER) --add-host=gitlab.qdream.com:************* -v ~/ngame/server/:/project/ngame/server -v ~/go/pkg:/project/go/pkg -v ~/.ssh:/project/.ssh -w /project/ngame/server harbor.qdream.com/ngame/ngamedev:1.0 sh -c "make ${TARGET}"
   ```

4. logic服务的启动还是和以前一样，在宿主机来启动，这个脚本主要用来帮助新人减少环境配置，写完代码就可以编译
