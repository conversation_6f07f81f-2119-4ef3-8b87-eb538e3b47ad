FROM centos:7.9.2009

# 基础环境配置
RUN     rm -rf /etc/yum.repos.d/* && \
        curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo && \
        curl -o /etc/yum.repos.d/epel.repo https://mirrors.aliyun.com/repo/epel-7.repo && \
        yum clean all && \
        yum makecache fast && \
        yum install -y psmisc tree wget curl vim bash-completion iproute && \
		yum install -y gcc automake autoconf libtool make && \
		yum install -y lbzip2 && \
		yum install -y git 

# 安装go环境
RUN wget https://go.dev/dl/go1.22.1.linux-amd64.tar.gz
#COPY go1.22.1.linux-amd64.tar.gz ./
RUN tar -C /usr/local -xzf go1.22.1.linux-amd64.tar.gz
RUN  rm go1.22.1.linux-amd64.tar.gz


RUN mkdir /usr/local/clang-format
RUN wget https://github.com/llvm/llvm-project/releases/download/llvmorg-8.0.1/clang+llvm-8.0.1-x86_64-linux-gnu-ubuntu-14.04.tar.xz
#COPY clang+llvm-8.0.1-x86_64-linux-gnu-ubuntu-14.04.tar.xz ./
RUN tar -xJvf clang+llvm-8.0.1-x86_64-linux-gnu-ubuntu-14.04.tar.xz -C /usr/local/clang-format --strip-components=1
RUN rm clang+llvm-8.0.1-x86_64-linux-gnu-ubuntu-14.04.tar.xz


RUN mkdir /project
RUN mkdir /project/tmp
RUN mkdir /project/go

COPY dockerCfg/gitconfig  /project/.gitconfig
COPY dockerCfg/bashrc /etc/bashrc

ENV PATH="/usr/local/clang-format/bin:/usr/local/go/bin:${PATH}"
ENV GOPATH="/project/go"

RUN go install golang.org/x/tools/cmd/goimports@v0.21.0
RUN go install github.com/golang/protobuf/protoc-gen-go@v1.3.2
RUN go install github.com/gogo/protobuf/protoc-gen-gofast@v1.3.1

# 下载gosu二进制文件
RUN curl -o /usr/local/bin/gosu -sSL "https://github.com/tianon/gosu/releases/download/1.17/gosu-amd64"

# 设置gosu可执行权限
RUN chmod +x /usr/local/bin/gosu


# 设置环境变量
#安装对应的工具

COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod a+x /usr/local/bin/docker-entrypoint.sh

WORKDIR /project

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
