<?xml version="1.0" encoding="UTF-8"?>
<!--key=string:配置值 type=int:类型 value=int:值 count=int:数量 -->

<root>
    <data key="TOWERSTAR_CHAPTER_OPEN1" type="0" value="0" count="2" />
    <data key="TOWERSTAR_CHAPTER_OPEN2" type="0" value="0" count="2" />
    <data key="TOWERSTAR_SKIP" type="0" value="0" count="0" />
    <data key="TOWERSTAR_NEXT_STAGE" type="0" value="0" count="10012" />
    <data key="TOWERSTAR_SKIP_FLOOR_COMMON" type="0" value="0" count="0" />
    <data key="TOWERSTAR_SKIP_FLOOR_ELITE" type="0" value="0" count="0" />
    <data key="TOWERSTAR_REWARD_OPEN" type="0" value="0" count="0" />
</root>
