<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:序号 key=string:方法名 type=int:类型 value=int:值 count=int:数量 -->

<root>
    <data id="1" key="OBJECT_DELETE_ITEM" type="4" value="10194" count="1" />
    <data id="2" key="OBJECT_DELETE_TIME" type="0" value="0" count="1" />
    <data id="3" key="OBJECT_DELETE_BUY" type="0" value="0" count="1032" />
    <data id="4" key="OBJECT_DELETE_BUY_LIMIT" type="0" value="0" count="4" />
    <data id="5" key="OBJECT_UP_LEVEL" type="0" value="0" count="11" />
    <data id="6" key="OBJECT_UP_ITEM" type="4" value="10195" count="1" />
    <data id="7" key="OBJECT_UP_TIME" type="0" value="0" count="1" />
    <data id="8" key="OBJECT_UP_BUY" type="0" value="0" count="1033" />
    <data id="9" key="OBJECT_UP_BUY_LIMIT" type="0" value="0" count="4" />
    <data id="10" key="GAME_TIME" type="0" value="0" count="79" />
    <data id="11" key="GAME_TIME_BEGIN" type="0" value="0" count="2" />
    <data id="12" key="GAME_TIME_DAILY" type="0" value="0" count="2" />
    <data id="13" key="GAME_TIME_MAX" type="0" value="0" count="8" />
    <data id="14" key="SHOW_SKIN" type="19" value="3301202" count="0" />
    <data id="15" key="REWARD_TASK_ID_1" type="0" value="0" count="60709" />
    <data id="16" key="REWARD_TASK_ID_2" type="0" value="0" count="60816" />
    <data id="17" key="REWARD_TASK_ID_3" type="0" value="0" count="60916" />
    <data id="18" key="NOTICE_STEP" type="0" value="0" count="10" />
</root>
