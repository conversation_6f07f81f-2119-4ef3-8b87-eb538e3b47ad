syntax = "proto3";

option go_package = "app/protos/out/common";

package common;

// NOTE:
// 前后端约定规则如果枚举只给后端使用增加"_S"结尾，所以定义枚举时如无特殊用意不要使用"_S"结尾

// 养成属性
enum ATTRINDEX {
  //-------------- 基础属性 面板显示 1-50 -------------
  MIN = 0;
  HP = 1;      // 生命
  ATTACK = 2;  // 攻击
  DEFEND = 3;  // 防御

  // ！基础属性最大值，增加新属性时递增 主要用于前端通信用，后端使用MAX
  BASE_MAX = 4;

  //------------- 功效类属性 加成基础属性 51-MAX ------
  HP_ADD_PER = 51;      // 生命增加百分比
  ATTACK_ADD_PER = 52;  // 攻击增加百分比
  DEFEND_ADD_PER = 53;  // 防御增加百分比

  // ！增加新属性时递增
  MAX = 54;
}

// 品质
enum QUALITY {
  // 灰绿蓝紫橙红金
  NONE_QUALITY = 0;
  GREY = 10;
  //  WHITE = 2;
  GREEN = 20;
  BLUE = 30;
  PURPLE = 40;
  ORANGE = 50;
  RED = 60;
  GOLDEN = 70;
}

// 奖励类型
enum RESOURCE {
  NONE_RESOURCE = 0;
  PEXP = 1;                // 主角经验
  DIAMOND = 2;             // 钻石
  GOLD = 3;                // 金币
  ITEM = 4;                // item
  FRAGMENT = 5;            // 碎片
  HERO = 6;                // 英雄
  EQUIP = 7;               // 装备
  GEM = 8;                 // 宝石
  TOKEN = 9;               // 基础资源 - 货币
  ARTIFACT = 10;           // 神器部件
  EMBLEM = 11;             // 纹章
  ARTIFACT_FRAGMENT = 13;  // 神器碎片
  VIP_EXP = 14;            // vip经验
  AVATAR = 15;             // 头像
  EMBLEM_FRAGMENT = 16;    // 符文碎片
  GUILD_CHEST_ITEM = 17;   // 公会宝箱
  RITE_MARK = 18;          // 永恒印记
  SKIN = 19;               // 皮肤
  COUPON = 20;             // 代金券
  MONUMENT_RUNE = 21;      // 羁绊丰碑符石
  REMAIN = 22;             // 遗物
  REMAIN_COST_ITEM = 23;   // 遗物-COST道具
  REMAIN_FRAGMENT = 24;    // 遗物-碎片
  SEASON_JEWELRY = 25;     // 赛季装备
  TITLE = 26;              // 称号
  SEASON_MAP_GOODS = 27;   // 赛季地图道具
  POKEMON_FRAGMENT = 28;   // 宠物碎片
  POKEMON = 29;            // 宠物
}

// 钻石类型
enum DIAMOND_TYPE {
  PRESENT = 0;   // 游戏内赠送
  RECHARGE = 1;  // 充值
}

// 道具类型
enum ITEM_TYPE {
  NONE_ITEM_TYPE = 0;
  BOX_ITEM_TYPE = 2;
  SELECTIVE_TYPE = 3;                  // 自选礼包
  GOLD_ITEM_TYPE = 4;                  // N小时金币收益道具
  HERO_EXP_ITEM_TYPE = 5;              // N小时英雄经验收益道具
  MAZE_ITEM_TYPE = 6;                  // 迷宫中使用的道具
  AVATAR_ITEM_TYPE = 7;                // 头像框使用道具
  SKIN_ITEM_TYPE = 16;                 // 皮肤道具
  AWAKEN_MATERIAL_ITEM_TYPE = 17;      // 觉醒材料
  SEASON_JEWELRY_SELECTIVE_TYPE = 19;  // 自选礼包（赛季装备）
}

enum TOKEN_TYPE {
  NONE_TOKEN_TYPE = 0;
  GOLD_POINT = 9001;                  // 金币
  PRESTIGE = 9002;                    // 声望点
  FRIEND_LIKE = 9003;                 // 友情点
  ORACLE_QUARTZ = 9004;               // 星命盘（先知商店的货币）
  ORACLE_ESSENCE = 9005;              // 先知精华（英雄转换资源）
  DESTINY_CARD = 9006;                // 星命卡牌（先知兑换抽卡的资源）
  DIVINITY = 9007;                    // 神格（英雄分解获得的货币，英雄商店里使用）
  ARENA_REPUTATION = 9008;            // 竞技声望(竞技场排名奖励获得的货币，竞技商店里用)
  ARTIFACT_POINT = 9009;              // 淬炼值（神器宝库积分）
  GEM_CRAFT_ODDS = 9010;              // 宝石合成幸运值
  BOUNTY_INTEL = 9011;                // 情报点
  EMBLEM_CURRENCY = 9012;             // 刻印环
  SCROLL_SHARD = 9013;                // 残卷碎片
  MAZE_VAULT_KEY = 9014;              // 宝库钥匙
  SPIRITUAL_ECHO = 9015;              // 圣者遗物
  GUILD_CONTRIBUTION = 9016;          // 公会贡献
  FOREST_PETAL = 9017;                // 恶魔花瓣
  GUILD_SKILL_POINT = 9018;           // 贤者墨（公会技能币）
  LOSE_POINT = 9019;                  // 失落币
  WRESTLE_POINT = 9020;               // 神树争霸代币
  DEBUT_POINT = 9021;                 // 英雄首发积分
  GUILD_TALENT_POINT_1 = 9022;        // 公会天赋点1
  GUILD_TALENT_POINT_2 = 9023;        // 公会天赋点2
  GUILD_TALENT_POINT_3 = 9024;        // 公会天赋点3
  GUILD_TALENT_POINT_4 = 9025;        // 公会天赋点4
  GUILD_TALENT_POINT_5 = 9026;        // 公会天赋点5
  GUILD_TALENT_POINT_6 = 9027;        // 公会天赋点6
  ARTIFACT_DEBUT_DRAW_POINTS = 9050;  // 神器首发抽卡积分
  ARTIFACT_DEBUT_SHOP_TOKEN = 9051;   // 神器首发商店代币
  DAILY_SPECIAL_SCORE = 9053;         // 每日特惠积分
  //  DISORDER_LAND_KEY = 9058;           // 失序空间 - 钥匙  -- 废弃
  //  DISORDER_LAND_STAMINA = 9059;       // 失序空间 - 体力  -- 废弃
  MAZE_ADVANCED_TOKEN = 9067;  // 高级迷宫币
}

// 资源属性类型
enum RESOURCE_ATTR_TYPE {
  RESAT_NONE = 0;
  RESAT_ADDITIVE_HERO = 1;
  RESAT_SKILL = 2;
  RESAT_AWARD_FLAG = 101;         // 奖励发放标志（见RESOURCE_REWARD_FLAG定义）
  RESAT_FROM_ID = 102;            // 产物来源ID（用于分解产物）
  RESAT_TASK_ID = 103;            // 产物来源任务ID（用于任务奖励）
  RESAT_ITEM_EXPIRED_TIME = 104;  // 物品过期时间
  RESAT_IGNORE = 105;             // 忽略不处理
}

// 资源奖励标志
enum RESOURCE_REWARD_FLAG {
  RRF_NORMAL = 0;            // 正常奖励
  RRF_NEW_DECOMPOSED = 1;    // 新资源被分解
  RRF_OWNED_DECOMPOSED = 2;  // 已有资源被分解
  RRF_DECOMPOSE_RESULT = 3;  // 分解产物
}

// 邮件标签
enum MAIL_FLAG {
  NEW = 0;     // 新邮件
  READ = 1;    // 已读
  DRAW = 2;    // 已领
  DELETE = 3;  // 已删除
}

enum CAREER {
  ZY_NONE = 0;
  ZY_HUN = 1;
  ZY_SHU = 2;
  ZY_TI = 3;
}

// 功能ID
enum FUNCID {
  MODULE_NONE = 0;
  // 前后端共用
  MODULE_ACHIEVE = 1;     // 成就
  MODULE_DAILY_TASK = 2;  // 每日任务
  MODULE_FRIEND = 3;      // 好友
  // MODULE_CHAT = 4;                         //聊天 -废弃
  MODULE_CITY = 5;              // 主城
  MODULE_FIELD = 6;             // 野外
  MODULE_DUNGEON = 7;           // 战役
  MODULE_HERO = 8;              // 英雄
  MODULE_BAG = 9;               // 背包
  MODULE_SUMMON = 10;           // 酒馆(召唤)
  MODULE_GUILD = 11;            // 佣兵团（公会）
  MODULE_SHOP = 12;             // 商店
  MODULE_CRYSTAL = 13;          // 水晶
  MODULE_ASSOC = 14;            // 先知
  MODULE_LEADERBOARD = 15;      // 纪念碑（排行榜）
  MODULE_SPINNER = 16;          // 占卜屋（转盘）
  MODULE_WEEKLY_TASK = 17;      // 每周任务
  MODULE_ARTIFACT_SUMMON = 18;  // 神器抽卡
  MODULE_SPEED_ONHOOK = 22;     // 挂机加速
  MODULE_DISPATCH = 23;         // 悬赏任务
  MODULE_DIAMOND_SHOP = 24;     // 钻石商店
  MODULE_GENERAL_SHOP = 25;     // 神秘商店
  MODULE_SKILL_SHOP = 26;       // 技能商店
  MODULE_ARENA_SHOP = 27;       // 竞技商店
  MODULE_HERO_SHOP = 28;        // 英雄商店
  MODULE_ACTIVE_SHOP = 29;      // 活跃商店
  MODULE_HONOR_SHOP = 30;       // 荣誉商店
  MODULE_MAIL = 31;             // 邮件
  MODULE_ARENA = 32;            // 竞技场
  MODULE_ARENA_RANK = 33;       // 竞技场排行榜
  // MODULE_ARENA_ATTACK = 34;                  //竞技场进攻阵容
  // MODULE_ARENA_DEFENSE = 35;                 //竞技场防守阵容
  MODULE_EQUIP = 36;        // 装备
  MODULE_GEM = 37;          // 宝石
  MODULE_EMBLEM = 38;       // 纹章
  MODULE_ARTIFACT = 39;     // 神器
  MODULE_TOWER = 40;        // 爬塔
  MODULE_TRIAL = 44;        // 材料本
  MODULE_TRIAL_GOLD = 45;   // 材料本（金币）
  MODULE_TRIAL_EXP = 46;    // 材料本（经验）
  MODULE_TRIAL_EQUIP = 47;  // 材料本（装备）
  MODULE_TRIAL_HERO = 48;   // 材料本（英雄）
  // MODULE_TRIAL_EMBLEM = 49;                  //材料本（纹章）
  MODULE_AVATAR = 50;                // 头像设置
  MODULE_GOLD_BUY = 53;              // 点金
  MODULE_HANDBOOK = 58;              // 图鉴
  MODULE_MAZE = 60;                  // 迷宫
  MODULE_RANK_ACHIEVE_POWER = 61;    // 排行成就 - 战力
  MODULE_RANK_ACHIEVE_DUNGEON = 62;  // 排行成就 - 主线
  MODULE_RANK_ACHIEVE_TOWER = 63;    // 排行成就 - 爬塔
  MODULE_RANK_ACHIEVE_ARENA = 64;    // 排行成就 - 竞技场
  MODULE_EQUIP_STRENGTH = 65;        // 装备 - 突破
  MODULE_EQUIP_REFINE = 66;          // 装备 - 精炼
  MODULE_EQUIP_ENCHANT = 67;         // 装备 - 附魔
  MODULE_EQUIP_EVOLUTION = 68;       // 装备 - 进阶
  MODULE_MIRAGE = 69;                // 个人boss
  // MODULE_MIRAGE_RACE_EMPIRE = 70;            // 个人boss - 帝国
  // MODULE_MIRAGE_RACE_FOREST = 71;            // 个人boss - 森林
  // MODULE_MIRAGE_RACE_MOON = 72;              // 个人boss - 月影
  // MODULE_MIRAGE_RACE_PROTOSS = 73;           // 个人boss - 神族
  // MODULE_MIRAGE_RACE_DEMON = 74;             // 个人boss - 魔族
  MODULE_BELLTOWER = 75;                  // 钟楼
  MODULE_BELLTOWER_HERO_BACK = 76;        // 钟楼 - 英雄回退
  MODULE_BELLTOWER_HERO_REVIVE = 77;      // 钟楼 - 英雄重生
  MODULE_BELLTOWER_HERO_DISBAND = 78;     // 钟楼 - 英雄分解
  MODULE_BELLTOWER_HERO_CHANGE = 79;      // 钟楼 - 英雄转换
  MODULE_MEMORY = 80;                     // 境界
  MODULE_FOREST = 81;                     // 密林
  MODULE_TALES = 82;                      // 英雄列传
  MODULE_GUILD_DUNGEON = 83;              // 公会副本
  MODULE_TALES_ELITE = 85;                // 英雄列传强敌
  MODULE_BELLTOWER_EQUIP_REVIVE = 86;     // 钟楼-装备重生
  MODULE_BELLTOWER_EQUIP_DECOMPOSE = 87;  // 钟楼-装备分解
  MODULE_BELLTOWER_GEM_DECOMPOSE = 88;    // 钟楼-宝石分解
  // MODULE_FOREST_ATTACK = 91;                 //密林进攻阵容
  // MODULE_FOREST_DEFENSE = 92;                //密林防守阵容
  MODULE_ARTIFACT_STRENGTH = 93;             // 装备 - 强化
  MODULE_ARTIFACT_FORGE = 94;                // 装备 - 铸造
  MODULE_MASTER = 95;                        // 成长大师
  MODULE_GUILD_TALENT = 96;                  // 公会天赋
  MODULE_CARNIVAL_SEVEN = 97;                // 七日嘉年华
  MODULE_CARNIVAL_FOURTEEN = 98;             // 十四日嘉年华
  MODULE_BELLTOWER_ARTIFACT_REVIVE = 101;    // 钟楼 - 神器重生
  MODULE_HERO_STAGE_UP = 102;                // 英雄 - 突破
  MODULE_SEVENDAY_LOGIN = 104;               // 七日登录活动
  MODULE_MEDAL = 110;                        // 功勋
  MODULE_DUEL = 111;                         // 切磋
  MODULE_TOWERSTAR = 113;                    // 条件爬塔
  MODULE_CHAT = 114;                         // 聊天
  MODULE_CHAT_WORLD = 115;                   // 聊天 - 世界频道解锁
  MODULE_CHAT_WORLD_SPEAK = 116;             // 聊天 - 世界频道发言
  MODULE_CHAT_PRIVATE = 117;                 // 聊天 - 私聊解锁+发言
  MODULE_CHAT_GUILD = 118;                   // 聊天 - 公会频道解锁+发言
  MODULE_CHAT_SYSTEM = 119;                  // 聊天 - 系统频道解锁
  MODULE_CRYSTAL_EQUIPMENT = 120;            // 水晶 - 装备
  MODULE_CRYSTAL_EMBLEM = 121;               // 水晶 - 符文
  MODULE_CRYSTAL_GEM = 122;                  // 水晶 - 宝石
  MODULE_CRYSTAL_HIDDEN_WEAPON = 123;        // 水晶 - 暗器
  MODULE_CRYSTAL_BLESSING = 124;             // 水晶 - 祝福
  MODULE_PUSH_GIFT = 134;                    // 推送礼包
  MODULE_FORECAST = 126;                     // 新功能开启
  MODULE_MONTHLY_CARD = 133;                 // 月卡
  MODULE_OPERATE_ACTIVITY = 135;             // 配置活动
  MODULE_VIP = 148;                          // vip
  MODULE_ARTIFACT_POINTS_EXCHANGE = 151;     // 神器积分兑换
  MODULE_TOWER_JUMP = 152;                   // 爬塔-跳关
  MODULE_GIFT_CODE = 156;                    // 礼包码
  MODULE_MIRAGE_HELL = 159;                  // 个人boss - 地狱模式
  MODULE_MAIN_PASS = 161;                    // 主线战令
  MODULE_TOWER_PASS = 162;                   // 爬塔战令
  MODULE_SHARE = 165;                        // 分享
  MODULE_PASS_DAILY_ACTIVE = 167;            // 战令 - 日常活跃战令
  MODULE_PASS_MAZE_ACTIVE = 168;             // 战令 - 迷宫活跃战令
  MODULE_ROUND_ACTIVITY = 169;               // 轮次活动
  MODULE_CHAT_LIKE = 171;                    // 聊天 - 点赞
  MODULE_GUILD_RECRUIT = 172;                // 公会 - 招募
  MODULE_RANK_ACHIEVE_MIRAGE_EMPIRE = 173;   // 排行成就 - 个人boss - 帝国
  MODULE_RANK_ACHIEVE_MIRAGE_FOREST = 174;   // 排行成就 - 个人boss - 森林
  MODULE_RANK_ACHIEVE_MIRAGE_MOON = 175;     // 排行成就 - 个人boss - 月影
  MODULE_RANK_ACHIEVE_MIRAGE_PROTOSS = 176;  // 排行成就 - 个人boss - 神族
  MODULE_RANK_ACHIEVE_MIRAGE_DEMON = 181;    // 排行成就 - 个人boss - 魔族
  MODULE_HERO_CONVERSION = 188;              // 英雄兑换
  MODULE_WRESTLE = 189;                      // 神树争霸
  MODULE_GODDESS_CONTRACT = 190;             // 契约制所
  // MODULE_WRESTLE_DEFENSE = 200;              // 神树争霸 - 防守阵容
  MODULE_SOCIAL = 204;                   // 社区
  MODULE_FLOWER = 206;                   // 密林 0.9.8
  MODULE_RANK_ACHIEVE_MIRAGE_SIX = 207;  // 排行成就 - 个人boss - 第6个
  // MODULE_FLOWER_ATTACK = 208;                // 密林 - 攻击阵容
  // MODULE_FLOWER_DEFENSE = 209;               // 密林 - 防守阵容
  MODULE_DAILY_WISH = 210;                  // 每日许愿
  MODULE_DIAMOND_PASS = 211;                // 钻石战令
  MODULE_PROPHET_PASS = 212;                // 精英幻境战令
  MODULE_DIVINE_DEMON = 213;                // 神魔抽卡
  MODULE_LINK_SUMMON = 214;                 // 流派抽卡
  MODULE_ARTIFACT_DEBUT = 215;              // 神器首发
  MODULE_GUILD_DONATE = 217;                // 公会捐赠
  MODULE_COGNITION = 220;                   // 战斗认知
  MODULE_TOWER_SEASON = 221;                // 百塔
  MODULE_CHAT_PARTITION = 223;              // 聊天 - 战区频道
  MODULE_GOD_PRESENT = 225;                 // 777抽活动
  MODULE_SAVE_H5_DESKTOP = 226;             // H5-保存至桌面
  MODULE_HERO_EXCHANGE = 233;               // 英雄转换
  MODULE_DROP_ACTIVITY = 234;               // 掉落活动
  MODULE_GUILD_CHEST = 236;                 // 公会宝箱
  MODULE_MONTHLY_TASK = 237;                // 每月任务
  MODULE_WORLD_BOSS = 240;                  // 世界boss
  MODULE_ACTIVITY_STORY = 242;              // 活动故事
  MODULE_SKIN = 243;                        // 皮肤
  MODULE_ASSISTANCE_ACTIVITY = 244;         // 助力活动
  MODULE_PEAK = 246;                        // 巅峰竞技场
  MODULE_RITE = 248;                        // 永恒仪式
  MODULE_GUILD_DUNGEON_BOX_ALL_RECV = 249;  // 公会副本宝箱一键领取
  MODULE_SEASON = 250;                      // 赛季
  MODULE_SEASON_DUNGEON = 251;              // 赛季主线
  MODULE_SEASON_LEVEL = 255;                // 赛季等级
  MODULE_DISORDER_LAND = 256;               // 失序空间
  MODULE_PRE_SEASON = 258;                  // 赛季前奖励
  MODULE_SHOP_COUPON = 259;                 // 代金券商城
  MODULE_COUPON_USE = 260;                  // 代金券支付
  MODULE_HERO_AWAKEN = 261;                 // 英雄觉醒
  MODULE_REMAIN = 263;                      // 遗物系统
  MODULE_PROMOTION = 265;                   // 新推送礼包
  MODULE_SEASON_ARENA = 267;                // 赛季竞技场
  MODULE_EMBLEM_UPGRADE = 272;              // 符文 - 升阶
  MODULE_GST = 270;                         // 公会战
  MODULE_ASSISTANT = 273;                   // 减负小助手
  MODULE_SEASON_LINK = 274;                 // 赛季羁绊
  MODULE_STORY_REVIEW = 275;                // 剧情回忆录
  MODULE_EMBLEM_SUCCINCT = 277;             // 符文洗炼
  MODULE_PYRAMID = 280;                     // 金字塔活动
  MODULE_NEW_YEAR_ACTIVITY = 281;           // 新年活动
  MODULE_SEASON_FLASH_BACK = 282;           // 赛季回顾
  MODULE_HERO_CONVERT = 286;                // 溢出英雄转换觉醒道具
  MODULE_GST_BOSS = 291;                    // 公会boss战-魔物入侵
  MODULE_LINE_TASK = 292;                   // 链式任务
  MODULE_ACTIVITY_TURN_TABLE = 294;         // 周年庆活动
  MODULE_GST_DRAGON = 297;                  // 公会战-龙战
  MODULE_HOT_RANK = 299;                    // 热度榜
  MODULE_GUILD_MOBILIZATION = 300;          // 公会竞赛
  MODULE_ACTIVITY_SCHEDULE = 303;           // 活动日历
  MODULE_BOSS_RUSH = 310;                   // Boss挑战
  MODULE_TALENT_TREE = 315;                 // 天赋树
  MODULE_DUEL_1 = 319;                      // 切磋-1v1
  MODULE_DUEL_2 = 320;                      // 切磋-2v2
  MODULE_DUEL_3 = 321;                      // 切磋-5v5
  MODULE_SELECT_SUMMON = 323;               // 选择抽卡
  MODULE_POKEMON = 324;                     // 宠物
  MODULE_GST_ORE = 330;                     // 公会战占矿
  MODULE_GST_TECH = 331;                    // 公会战科技
  MODULE_CARNIVAL_SEASON = 332;             // 赛季启动 - 赛季嘉年华
  MODULE_ACTIVITY_RANK_TOWER_SEASON = 333;  // 赛季启动 - 百塔冲榜
  MODULE_GST_CHALLENGE = 335;               // 公会战新擂台赛
  MODULE_SEASON_DOOR = 336;                 // 赛季开门玩法
  MODULE_SEASON_JEWELRY = 338;              // 赛季装备
  MODULE_SEASON_COMPLIANCE = 341;           // 赛季冲榜
  MODULE_TITLE = 342;                       // 称号
  MODULE_SEASON_MAP = 343;                  // 赛季地图
  MODULE_SEASON_SHOP = 345;                 // 赛季商店
  MODULE_ACTIVITY_COUPON = 346;             // 代金券活动
  MODULE_DAILY_ATTENDANCE_HERO = 347;       // 登录送英雄
  MODULE_POKEMON_SUMMON = 351;              // 宠物抽卡
  MODULE_TOWER_POKEMON = 352;               // 宠物爬塔
  MODULE_ACTIVITY_GODDESS_CONTRACT = 1009;  // 活动契约活动
  MODULE_ACTIVITY_COMPLIANCE = 1011;        // 开服冲榜活动
  MODULE_ACTIVITY_TOWER = 1014;             // 地宫冲榜活动
  MODULE_ACTIVITY_MIRAGE = 1016;            // 幻境冲榜活动
  MODULE_ACTIVITY_WEB = 1019;               // 官网充值费返利
  MODULE_SEASON_RETURN = 9996;              // 赛季回流
  MODULE_ACTIVITY_RETURN = 9997;            // 回流活动

  // 仅后端使用 命名结尾必须加"_S"
  MODULE_SHOP_S = 10001;                // 商店
  MODULE_ARENA_OPPONENT_S = 10002;      // 竞技场对手
  MODULE_RANK_ACHIEVE_AWARD_S = 10003;  // 全服成就领奖信息
  MODULE_GLOBAL_ATTR_S = 10004;         // 全局属性
  MODULE_DROP_S = 10005;                // 掉落
  MODULE_GUIDANCE_S = 10006;            // 新手引导
  MODULE_CLIENT_INFO_S = 10007;         // 客户端配置
  MODULE_ORDERS_S = 10008;              // 充值订单
  MODULE_RECHARGE_S = 10009;            // 充值
  MODULE_VIP_S = 10010;                 // vip
  MODULE_ACTIVITY_RECHARGE_S = 10011;   // 限时礼包
  // MODULE_CRYSTAL_ACHIEVE_S = 10012;        //水晶成就
  MODULE_PASS_S = 10013;                      // 战令
  MODULE_RATE_S = 10014;                      // 评分
  MODULE_QUESTIONNAIRE_S = 10015;             // 问卷
  MODULE_LINK_S = 10016;                      // 联结
  MODULE_FLOWER_S = 10017;                    // 密林
  MODULE_MONTH_TASKS_S = 10018;               // 全民无双
  MODULE_ACHIEVEMENTS_SHOWCASE_S = 10019;     // 成就展示
  MODULE_ChAT_GROUP_TAG_S = 10020;            // 聊天标签
  MODULE_DAILY_ATTENDANCE_S = 10021;          // 累登
  MODULE_DAILY_SPECIAL_S = 10022;             // 每日特惠
  MODULE_USER_GUILD_CHEST_ITEM_S = 10023;     // 用户公会宝箱物品
  MODULE_DIVINE_DEMON_WISH_RECORD_S = 10024;  // 神魔抽卡心愿随机组记录
  MODULE_SHARE_GROWTH_S = 10025;              // 共享养成
  MODULE_ACTIVITY_LIFELONG_GIFTS_S = 10026;   // 终身礼包
  MODULE_COMPLIANCE_TASKS_S = 10027;          // 竞赛领奖
}

// 购买功能ID
enum PURCHASEID {
  NONE = 0;
  SPEED_ONHOOK = 1;                             // 加速挂机
  TOWER_SWEEP_COUNT = 3;                        // 爬塔扫荡次数
  ARENA_FIGHT_COUNT = 4;                        // 竞技场每日免费挑战次数
  ARENA_LIKE_COUNT = 5;                         // 竞技场每日点赞可点赞次数
  TRIAL_GOLD_COUNT = 6;                         // 材料本(金币)领奖次数
  TRIAL_EXP_COUNT = 7;                          // 材料本(经验)领奖次数
  TRIAL_HERO_COUNT = 8;                         // 材料本(英雄)领奖次数
  TRIAL_EQUIP_COUNT = 9;                        // 材料本(装备)领奖次数
  TRIAL_EMBLEM_COUNT = 10;                      // 材料本(纹章)领奖次数
  DISPATCH_REFRESH_FREE_COUNT = 11;             // 悬赏任务每日免费刷新次数
  MIRAGE_SKY_FIGHT_COUNT = 12;                  // 个人boss - 天空挑战：每日挑战次数
  FOREST_PEACHFUL_TREE_START_FEED_COUNT = 14;   // 矿战每日开始献祭次数 - 和平树
  FOREST_LOOT_COUNT = 15;                       // 矿战每日掠夺次数
  GUILD_DUNGEON_FIGHT_COUNT = 16;               // 公会副本战斗次数
  TALES_ELITE_COUNT = 17;                       // 英雄列传强敌次数
  MEDAL_DAILY_AWARD_COUNT = 18;                 // 功勋每日领奖次数
  TOWERSTAR_DAILY_AWARD_COUNT = 19;             // 条件爬塔每日领奖次数
  MONTHLY_CARD_GROW_UP_DAILY_AWARD_COUNT = 20;  // 成长月卡每日领奖次数
  MONTHLY_CARD_SUPPLY_DAILY_AWARD_COUNT = 21;   // 补给月卡每日领奖次数
  MIRAGE_HELL_FIGHT_COUNT = 22;                 // 个人boss - 地狱挑战：每日挑战次数
  FOREST_PVP_TREE_START_FEED_COUNT = 23;        // 矿战每日开始献祭次数 - pvp树
  ARENA_REFRESH_COUNT = 24;                     // 竞技场刷新
  SHARE_RECV_AWARD_COUNT = 25;                  // 分享领奖次数
  WRESTLE_LIKE_COUNT = 26;                      // 神树争霸点赞次数
  GUILD_TALENT_FIRST_RESET_COUNT = 27;          // 公会技能 - 第一分支重置次数
  GUILD_TALENT_SECOND_RESET_COUNT = 28;         // 公会技能 - 第二分支重置次数
  GUILD_TALENT_THIRD_RESET_COUNT = 29;          // 公会技能 - 第三分支重置次数
  GUILD_TALENT_FOURTH_RESET_COUNT = 30;         // 公会技能 - 第四分支重置次数
  GUILD_TALENT_FIFTH_RESET_COUNT = 31;          // 公会技能 - 第五分支重置次数
  GUILD_TALENT_SIXTH_RESET_COUNT = 32;          // 公会技能 - 第六分支重置次数
  // FLOWER_OCCUPY_ADD_TIME = 33;                  //密林据点模式 - 增加占领时间
  FLOWER_PLANT_DAILY_COUNT = 34;               // 密林协助模式 - 种植次数
  FLOWER_SNATCH_DAILY_COUNT = 35;              // 密林协助模式 - 每日协助次数
  MONTH_TASKS_DAILY_AWARD_RECV_COUNT = 36;     // 全民无双 - 每日奖励领取次数
  DAILY_SPECIAL_DAILY_AWARD = 38;              // 每日特惠 - 每日奖励领取次数
  WORLD_BOSS_DAILY_WORSHIP_COUNT = 39;         // 世界boss - 每日膜拜次数
  TOWER_SEASON_WEEKLY_LIKE_COUNT = 40;         // 百塔 - 排行榜每周点赞
  PEAK_WORSHIP_COUNT = 45;                     // 巅峰竞技场 - 每日膜拜次数
  DISORDER_LAND_BUY_COUNT = 47;                // 失序空间 - 每周能购买的次数
  MIRAGE_COUPON_BUY_COUNT = 48;                // 个人boss - 每日代金券购买次数
  GST_CONTRIBUTION_RANK_LIKE_COUNT = 49;       // 公会战-贡献榜点赞次数
  GST_KILL_RANK_LIKE_COUNT = 50;               // 公会战-击杀榜点赞次数
  GST_TRIPLE_KILL_RANK_LIKE_COUNT = 51;        // 公会战-3杀榜点赞次数
  SEASON_ARENA_REFRESH_OPPONENT_COUNT = 52;    // 赛季竞技场 - 每日代金券刷新对手次数
  SEASON_ARENA_BUY_CHALLENGE_COUNT = 53;       // 赛季竞技场 - 每日代金券购买次数
  DROP_ACTIVITY_DAILY_REWARD = 54;             // 掉落活动 - 每日奖励
  GST_BOSS_BUY_CHALLENGE_COUNT = 55;           // 公会boss战 - 挑战令购买次数
  ACTIVITY_TURAN_TABLE_BUY_TICKET = 56;        // 周年庆 - 购买门票
  GST_DRAGON_BUY_CHALLENGE_COUNT = 57;         // 公会战龙战 - 购买次数
  BOSS_RUSH_BUY_COUNT = 60;                    // Boss挑战 - 每周能购买的次数
  ACTIVITY_COMPLIANCE_RANK_LIKE_COUNT = 61;    // 羁绊冲榜 - 每日点赞次数
  GST_ORE_FIGHT = 71;                          // 公会战占矿战斗
  GST_ORE_ASSIST = 72;                         // 占矿协助
  ACTIVITY_TOWER_SEASON_RANK_LIKE_COUNT = 73;  // 百塔冲榜 - 每日点赞次数
  SYNTHESIS_GAME_CHALLENGE_COUNT = 79;         // 合成小游戏 - 挑战次数
  ACTIVITY_TOWER_RANK_LIKE_COUNT = 101;        // 地宫冲榜 - 每日点赞次数
  ACTIVITY_MIRAGE_RANK_LIKE_COUNT = 102;       // 幻境冲榜 - 每日点赞次数
}

// 购买方式
enum PURCHASE_TYPE {
  PT_NONE = 0;
  PT_NORMAL = 1;  // 普通购买
  PT_COUPON = 2;  // 代金券购买
}

// 单次购买数量限制
enum SHOP {
  SHOP_NONE = 0;
  BUY_NUM_MAX = 999;  // 单次购买数量限制
}

enum MAU {
  MAU_NONE = 0;
  MAU_RESERVE = 1;  // 仅用于格式化时能保持一致
}

enum CONFIG {
  CONFIG_NONE = 0;
  HERO_SLOT_MIN_NUM = 4;                               // 英雄位置初始个数
  HERO_SLOT_ADD_COST = 5;                              // 英雄位置购买消耗钻石
  HERO_SLOT_ADD_NUM = 6;                               // 英雄位置每次增加个数
  HERO_SLOT_MAX_NUM = 7;                               // 英雄位置最大个数
  DUNGEON_MAX_TURN = 8;                                // 战役最大回合数
  HERO_DISBAND_MAX_NUM = 21;                           // 英雄分解最大数量
  ITEM_USE_MAX_NUM = 22;                               // 道具单次使用上限
  ITEM_SELL_MAX_NUM = 23;                              // 道具单次出售上限
  GOODS_BUY_MAX_NUM = 24;                              // 商品单次购买数量上限
  ARENA_REFRESH_CD = 25;                               // 基础竞技场刷新对手CD（秒）
  ARENA_MAX_DAILY_LIKE_NUM = 26;                       // 基础竞技场每日最大点赞数量上限
  ARENA_BOT_LV_ADD_NUM = 27;                           // 基础竞技场机器人等级范围上限加成数（level + x）
  ARENA_BOT_LV_SUB_NUM = 28;                           // 基础竞技场机器人等级范围下限加成数 (level - x)
  ARENA_SCORE_DIFF_LIMIT = 29;                         // 基础竞技场分数差值上限
  ARENA_SHOW_MIN_RANK = 30;                            // 基础竞技场排行榜，展示名次的最小值
  ARENA_BOT_MIN_SCORE = 31;                            // 基础竞技场机器人积分最小值
  ARENA_BOT_MAX_SCORE = 32;                            // 基础竞技场机器人积分最大值
  ARENA_DAILY_FREE_FIGHT_NUM = 33;                     // 基础竞技场每日免费挑战次数
  ARENA_DAILY_AWARD_TM = 34;                           // 基础竞技场每日结算时间（分钟）
  ARENA_SEASON_RESET_TM = 35;                          // 基础竞技场赛季结算重置时间（分钟）
  USER_NAME_MAX_LENGTH = 45;                           // 用户昵称最大长度 14
  USER_NAME_SET_COST = 46;                             // 修改用户昵称消耗的元宝是 固定的
  DISPATCH_INITIAL_TASK1 = 47;                         // 玩家初始悬赏任务1
  DISPATCH_INITIAL_TASK2 = 48;                         // 玩家初始悬赏任务2
  DISPATCH_INITIAL_TASK3 = 49;                         // 玩家初始悬赏任务3
  DISPATCH_NEW_TASK_NUM = 50;                          // 悬赏任务每次刷新的任务条数
  DISPATCH_SPEED_REMAIN_TIME = 51;                     // 悬赏任务剩余多少秒时加速免费
  DISPATCH_FORCE_RED_COUNTS = 52;                      // 悬赏任务保底刷出红色任务的累积次数
  DISPATCH_MAX_FINISHED = 53;                          // 悬赏任务已完成未领取数量
  CHAT_FREQUENCY_TIME = 54;                            // 聊天间隔时间 单位ms
  FRIEND_MAX_NUM = 73;                                 // 好友数量
  FRIEND_BLACK_MAX_NUM = 74;                           // 好友黑名单数量
  FRIEND_RECV_MAX_COUNT = 75;                          // 每日可领取好友点赞数量
  FRIEND_SEND_MAX_COUNT = 76;                          // 每日可好友点赞数量
  FRIEND_RECOMMEND_ACTIVE_DAY = 77;                    // 好友推荐的活跃天数
  FRIEND_RECOMMEND_RANGE_LEVEL = 78;                   // 好友推荐的等级范围
  FRIEND_RECOMMEND_MIN_LEVEL = 79;                     // 好友推荐的最小等级
  FRIEND_RECOMMEND_MAX_LEVEL = 80;                     // 好友推荐的最大等级
  FRIEND_RECOMMEND_NUM = 81;                           // 好友推荐的个数
  FRIEND_REQUEST_MAX_NUM = 82;                         // 好友申请列表上限
  EQUIP_CONFIG_BAG_MAX_LIMIT_ID = 83;                  // 装备背包上限
  FRIEND_RECEIVE_PER_GOLD = 85;                        // 每次领取对应的金币数量
  FRIEND_RECEIVE_PER_POINT = 86;                       // 每次领取对应的友情点数
  HERO_LEVEL_ONEKEY_OFF = 87;                          // 英雄到了这个等级关闭一键升10级
  HERO_LEVEL_ONEKEY_NUM = 88;                          // 英雄一键升级最大10级
  BOT_NAME_A_MAX = 90;                                 // 机器人名字首位最大随机数
  BOT_NAME_B_MAX = 91;                                 // 机器人名字第二位最大随机数
  HANDBOOK_HERO_AWARD = 92;                            // 历史首次获得五星英雄可获得的图鉴奖励
  ONHOOK_MAX_TIME = 104;                               // 挂机离线时长
  EMBLEM_UMDECOMPOSE_RARE = 122;                       // 符文稀有度 >= x不可以分解
  WISHLIST_CD = 123;                                   // 心愿单重置周期（天）
  WISHLIST_RESET_VALUE = 127;                          // 心愿单重置保底计算参数
  OVERALL_RATING_MULTIPLE = 128;                       // 全局属性模块评分放大系数
  WISHLIST_CD_NEW = 129;                               // 心愿单新手重置天数
  WISHLIST_RESET_TIMES = 130;                          // 心愿单新手重置次数
  MAX_GIFTS_SEND_NUM = 131;                            // 女武神赠送礼物的上限
  DEBUT_SCORE_EXCHANGE = 132;                          // 英雄首发 - 1个积分兑换资源的数量
  GODDESS_CONTRACT_LEVEL_UP_ITEM = 133;                // 契约之所升级物品
  AVATAR_USE_ITEM_ONCE = 134;                          // 头像框使用道具上线
  ENCHANT_LIMIT_RARE = 136;                            // 装备附魔装备品质限制
  HERO_BAG_BUY_GROUP = 137;                            // 英雄背包购买组
  GODDESS_TOUCH_LIMIT = 145;                           // 女武神每日抚摸上限
  LINK1_UNLOCK_HEROSTAR = 148;                         // 英雄羁绊1的星级条件
  LINK2_UNLOCK_HEROSTAR = 149;                         // 英雄羁绊2的星级条件
  LINK3_UNLOCK_HEROSTAR = 150;                         // 英雄羁绊3的星级条件
  LINK4_UNLOCK_HERO_STAR = 151;                        // 符文羁绊解锁英雄星级
  OSS_OPEN_LV = 154;                                   // 战报服务的开启等级
  ARTIFACT_EXCHANGE_COST_NUM = 155;                    // 神器积分兑换消耗资源的数量
  HERO_TRANSLATION = 165;                              // 英雄转换所需溢出英雄数量-已废弃
  ARTIFACT_GUARANTEE_ID = 166;                         // 神器保底表ID
  GOD_PRESENT_HERO_REDUCE_RARE = 167;                  // 神之馈赠抽卡下降英雄品质
  DROP_ACTIVITY_DUNGEON_RATE = 168;                    // 掉落活动 - 主线掉落频率（秒）
  EMBLEM_SLOT_BUY_GROUP = 175;                         // 符文背包栏位购买组id
  ACTIVITY_TICKET_INCREASE_DAILY = 177;                // 人鱼活动入场券每日免费数量
  ACTIVITY_TICKET_INCREASE_LIMIT = 178;                // 人鱼活动入场券免费持有上限
  EMBLEM_SLOT_ADD_NUM = 181;                           // 符文背包栏位每次增加个数
  ACTIVITY_STORY_SHEILD_DAYS = 182;                    // 新服人鱼活动活动隐藏天数
  ASSISTANCE_ACTIVITY_NAME_NUM = 183;                  // 助力活动名字个数
  FRAGMENT_COMPOSE_MAX_UNIT_NUM = 186;                 // 碎片一键合成最大合成种类数量
  SEASON_OPEN_SERVER_DAY_LIMIT = 191;                  // 赛季玩法开启的服务器天数限制
  AWAKEN_UNIVERSAL_ITEM = 193;                         // 觉醒万能养成道具材料ID
  LINK5_UNLOCK_HEROSTAR = 194;                         // 觉醒英雄解锁星级
  LINK5_UNLOCK_HEROSTAR_PRE = 195;                     // 觉醒英雄预解锁星级
  LINK5_ID = 196;                                      // 觉醒英雄羁绊效果
  SEASON_RETURN_RECEIVE = 199;                         // 赛季回流领奖解锁赛季主线ID
  MIRAGE_BUY_COUNT_COST_ITEM_ID = 201;                 // 个人 boss - 道具value购买次数
  SKIN_DECOMPOSE_DIAMOND_LIMIT = 202;                  // 皮肤分解 - 钻石上限值
  SKIN_DECOMPOSE_GOLD_LIMIT = 203;                     // 皮肤分解 - 金币上限值
  ASSISTANT_SHOP_CHOOSE_GOODS_LIMIT = 204;             // 小助手商品勾选上限
  SEASON_LINK_ACTIVE_ITEM = 205;                       // 赛季羁绊激活道具
  SEASON_LINK_ACTIVE_ITEM_COUNT = 206;                 // 赛季羁绊激活道具数量
  RITE_10_RECYCLE_POINT = 207;                         // 白色印记回收分数
  RITE_20_RECYCLE_POINT = 208;                         // 绿色印记回收分数
  RITE_30_RECYCLE_POINT = 209;                         // 蓝色印记回收分数
  RITE_40_RECYCLE_POINT = 210;                         // 紫色印记回收分数
  RITE_50_RECYCLE_POINT = 211;                         // 橙色印记回收分数
  RITE_60_RECYCLE_POINT = 212;                         // 红色印记回收分数
  RITE_POWER_RECYCLE_POINT = 213;                      // 威能回收分数
  SEASON_LINK_10_RARE_RECYCLE_POINT = 215;             // 白色符石品质回收分数
  SEASON_LINK_20_RARE_RECYCLE_POINT = 216;             // 绿色符石品质回收分数
  SEASON_LINK_30_RARE_RECYCLE_POINT = 217;             // 蓝色符石品质回收分数
  SEASON_LINK_40_RARE_RECYCLE_POINT = 218;             // 紫色符石品质回收分数
  SEASON_LINK_50_RARE_RECYCLE_POINT = 219;             // 橙色符石品质回收分数
  SEASON_LINK_60_RARE_RECYCLE_POINT = 220;             // 红色符石品质回收分数
  SEASON_LINK_70_RARE_RECYCLE_POINT = 221;             // 粉色符石品质回收分数
  SEASON_LINK_50_LV_RECYCLE_POINT = 222;               // 橙色符石品质回收分数
  SEASON_LINK_60_LV_RECYCLE_POINT = 223;               // 红色符石品质回收分数
  SEASON_LINK_30_LV_RECYCLE_POINT = 224;               // 蓝色符石品质回收分数
  SEASON_LINK_40_LV_RECYCLE_POINT = 225;               // 紫色符石品质回收分数
  NEW_YEAR_ACTIVITY_SHEILD_DAY = 230;                  // 新年活动屏蔽天数
  ASIAN_GVG_SHIELD_TIME = 231;                         // 公会战屏蔽截止时间
  HERO_CONVERT_OPEN = 233;                             // 溢出英雄转换开启条件
  HERO_CONVERT_NUM = 235;                              // 溢出英雄转换数量
  ACTIVITY_RECHARGE_DAILY_WISH = 238;                  // 活动充值每日许愿自动充值金额
  TURNTABLE_TICKETS_BUY_DAY = 239;                     // 转盘活动购买门票时间
  TURNTABLE_RANDOM_BUFF_NUM = 240;                     // 转盘活动随机BUFF数量
  HOT_RANK_REFRESH_TIME = 242;                         // 热度榜刷新天数
  TOWERSTAR_DYNAMIC_FORMATION_OPEN_CHAPTER = 245;      // 条件爬塔动态阵容开启章节
  BLESSED_HERO_POWER_RATE = 246;                       // 赐福英雄战力折算万分比
  SOCIETY_MAIL_DIAMOND_AWARD = 247;                    // 社区邮件的奖励钻石数量
  SOCIETY_MAIL_PUSH_LEVEL = 248;                       // 社区邮件的推送关卡
  DUEL_COOLDOWN_TIME = 249;                            // 切磋cd（分钟）
  ACTIVITY_TOWER_SEASON_RANK_OPEN = 250;               // 百塔冲榜 - 第几天开启
  ACTIVITY_TOWER_SEASON_RANK_END = 251;                // 百塔冲榜 - 第几天结束
  ACTIVITY_TOWER_SEASON_RANK_AWARD_END = 252;          // 百塔冲榜 - 第几天领奖结束
  ACTIVITY_TOWER_SEASON_RANK_DAILY_LIKE_AWARD = 253;   // 百塔冲榜 - 每日点赞奖励
  ACTIVITY_DROP_SERVER_DATA = 254;                     // 开服时间在该时间戳之前的服务器不开启新服远洋活动掉落
  ACTIVITY_TOWER_SEASON_RANK_OPEN_ROUND_2 = 256;       // 百塔冲榜第二轮 - 第几天开启
  ACTIVITY_TOWER_SEASON_RANK_END_ROUND_2 = 257;        // 百塔冲榜第二轮 - 第几天结束
  ACTIVITY_TOWER_SEASON_RANK_AWARD_END_ROUND_2 = 258;  // 百塔冲榜第二轮 - 第几天领奖结束
  RECHARGE_REFUND_DUNGEON_CN = 259;                    // 国服充值返利主线关卡
  RECHARGE_REFUND_TIME_CN = 260;                       // 国服充值返利充值时间
  RECHARGE_BY_COUPON_COST = 261;                       // 使用代金券充值的系数
  HERO_AWAKEN_CONVERT_RES_LIMIT = 262;                 // 英雄觉醒材料转换数量限制
  MAIL_TIME_LIMIT = 264;                               // 邮件已读已领过期时间限制
  MAIL_NUM_LIMIT = 265;                                // 邮件每次获取数量限制
  COMPLIANCE_OPEN_DATE = 10022;                        // 羁绊达标冲榜活动服务器开服第几天开
  COMPLIANCE_DURATION = 10023;                         // 羁绊达标冲榜活动持续几天结束
  COMPLIANCE_LIKE_DIAMOND = 10026;                     // 冲榜活动点赞钻石数
  ACTIVITY_TOWER_DURATION = 10027;                     // 地宫冲榜活动持续天数
  COMPLIANCE_OPEN_SERVER = 10028;                      // 羁绊冲榜活动开服屏蔽
  ACTIVITY_TOWER_LIKE_DIAMOND = 10030;                 // 地宫冲榜活动点赞钻石数
  ACTIVITY_MIRAGE_LIKE_DIAMOND = 10032;                // 幻境冲榜活动点赞钻石数
  ACTIVITY_MIRAGE_DURATION = 10033;                    // 幻境冲榜活动持续天数
  ACTIVITY_TOWER_RANK_REMAIN = 10034;                  // 地宫冲榜活动展示期
  ACTIVITY_MIRAGE_RANK_REMAIN = 10035;                 // 幻境冲榜活动展示期
  OFFLINE_VIP_AWARD_MAILID = 20001;                    // 官网累充奖励发奖邮件id 越南专用
}

enum EQUIPMENT_ATRR {
  EQUIPMENT_ATRR_NONE = 0;
  PRO = 1;  // 副属性
  ADD = 2;  // 额外属性
}

// Knight id
enum KNIGHT_ID {
  KNIGHT_ID_NONE = 0;
  MAIN_ROLE_MALE = 1;    // 男
  MAIN_ROLE_FEMALE = 2;  // 女
}

enum PURCHASE {
  FACTION_NONE_NUM = 0;
  FACTION_PILL_NUM = 1;  // 门派炼丹房次数
}

// 排行榜ID 后端排行榜唯一id
enum RANK {
  ID_NONE = 0;
  ID_TOWER = 1;  // 炼心塔
}

enum RANK_TYPE {
  MINIMUM = 0;
  MAXIMUM = 1;
}

enum ACHIEVE_TYPE {
  OF_COMMON_ACHIEVE = 0;
  OF_DAILY_ACHIEVE = 1;
}

// 掉落类型
enum DROP_TYPE {
  DROP_TYPE_NONE = 0;
  DT_RECURSION = 1000;  // 递归掉落
}

enum TASK_TYPE_PROGRESS_TYPE {
  PROGRESS_TYPE_NONE = 0;
  PT_ADD = 1;        // 累加的，比较大小。攻打100次主线副本。
  PT_MAX = 2;        // 覆盖型的，取最大值。强化到100级。
  PT_MIN = 3;        // 覆盖型的，取最小值。竞技场取得第1名。
  PT_UNIQUE = 4;     // 重复型的，比如强化6种不同类型的装备。
  PT_MORE_THAN = 5;  // 累加行，悬赏任务完成蓝色及以上品质X次
}

enum TASK_TYPE_FINISH_TYPE {
  FT_NONE = 0;
  FT_GREATER = 1;  // 进度值大于要求值算完成
  FT_LESS = 2;     // 进度值小于要求值
}

enum TASK_RESET_TYPE {
  RET_NONE = 0;
  RET_ACHIEVE = 1;  // 成就类型
  RET_DAILY = 2;    // 日常类型
  RET_WEEKLY = 3;   // 周常类型
  RET_MONTHLY = 4;  // 月常类型
  RET_LINE = 5;     // 链式类型
}

enum MAIL_COND {
  TYPE_MAIL_COND_NONE = 0;
  TYPE_CREATE_TIME = 1;
  TYPE_LEVEL = 2;
  TYPE_VIP = 3;
}

// 商店ID
enum SHOPID {
  NONE_SHOP = 0;
  // DIAMOND_SHOP = 201;  //钻石商店
  GENERAL_SHOP = 101;  // 优惠限购
  SKILL_SHOP = 102;    // 技能商店
  ARENA_SHOP = 201;    // 竞技商店
  HERO_SHOP = 202;     // 英雄商店
  // ACTIVE_SHOP = 302;   //活跃商店
  EMBLEM_SHOP = 203;        // 纹章商店(也叫 幻境商店)
  GEM_SHOP = 204;           // 宝石商店(也叫 密林商店)
  GUILD_SHOP = 205;         // 公会商店
  PROPHET_SHOP = 206;       // 先知商店
  ARTIFACT_SHOP = 207;      // 神器商店(也叫 迷宫商店)
  WORLD_BOSS_SHOP = 209;    // 世界boss商店
  SEASON_MAP_SHOP = 30002;  // 赛季地图商店
}

// 商店类型
enum SHOP_TYPE {
  ST_NONE = 0;
  ST_RANDOM = 1;          // 随机
  ST_FIXED = 2;           // 固定
  ST_ROUND = 3;           // 轮次
  ST_RANDOM_BOX = 4;      // 按商品格子随机商品
  ST_ARTIFACT_DEBUT = 6;  // 神器首发商店
}

// 全局属性类型
enum GLOBAL_ATTR_TYPE {
  GA_NONE = 0;
  GA_ARTIFACT = 1;  // 神器
  GA_MEMORY = 2;    // 境界(回忆)
}

// 宝石系统相关
enum GEM_CONFIG {
  GEM_NONE = 0;
  GEM_LEFT_SLOT = 1;                   // 宝石左槽位
  GEM_RIGHT_SLOT = 2;                  // 宝石右槽位
  GEM_BAG_MAX_LIMIT_ID = 36;           // 宝石背包最大上限；config_info.xml: id=36 对应的 value
  GEM_LEFT_SLOT_UNLOCK_LEVEL_ID = 41;  // 宝石左槽位解锁英雄等级；function_info.xml: id=41 对应的 value
  GEM_RIGHT_SLOT_UNLOCK_STAR_ID = 42;  // 宝石右槽位解锁英雄星数；function_info.xml: id=42 对应的 value
}

// 材料本类型
enum TRIAL_TYPE {
  NONE_TRIAL = 0;
  GOLD_TRIAL = 45;    // 金币本
  EXP_TRIAL = 46;     // 经验本
  EQUIP_TRIAL = 47;   // 英雄本
  HERO_TRIAL = 48;    // 宝石本
  EMBLEM_TRIAL = 49;  // 纹章本
}

// 纹章相关
enum EMBLEM_CONFIG {
  EMBLEM_NONE = 0;
  BAG_MAX_LIMIT_ID = 62;   // 纹章背包最大上限；config_info.xml: id=62 对应的 value
  SLOT_ONE_UNLOCK = 54;    // 纹章槽位1解锁条件：function_info.xml: id=54
  SLOT_TWO_UNLOCK = 55;    // 纹章槽位2解锁条件：function_info.xml: id=55
  SLOT_THREE_UNLOCK = 56;  // 纹章槽位3解锁条件：function_info.xml: id=54
  SLOT_FOUR_UNLOCK = 57;   // 纹章槽位4解锁条件：function_info.xml: id=55
  MAX_LEVELUP_COUNT = 10;  // 单次最大升级次数
}

// 封禁类型
enum BAN {
  TYPE_NONE = 0;
  TYPE_CHAT = 1;             // 禁止聊天
  TYPE_LOGIN = 2;            // 禁止登陆
  TYPE_LOGIN_TEMPORARY = 3;  // 临时禁止禁止登陆
  TYPE_MAX = 4;              // 最大值
}

// 参数限制
enum PARAM_LIMIT {
  PL_NONE = 0;
  PL_HANDBOOK_TYPE_MAX_NUM = 20;  // 图鉴类型最大数量
}

enum HANDBOOK {
  HT_NONE = 0;
  HT_HERO = 1;    // 英雄图鉴
  HT_FETTER = 2;  // 英雄羁绊
  HT_MAX = 3;     // 图鉴type最大值
}

// 穿脱操作类型
enum WEAR_OP {
  WEAR_NONE = 0;
  WEAR = 1;
  TAKEOFF = 2;
}

enum OFFLINE {
  REASON_OTHERS = 0;         // 其他
  REASON_NORMAL = 1;         // 正常登出
  REASON_GM_KICK = 2;        // 后台踢下线
  REASON_REPEATED_KICK = 3;  // 重复登录挤下线
  REASON_RESTART = 4;        // 服务器重启下线
  REASON_WRONG = 5;          // 出问题了
}

enum TIME_TYPE {
  NOT_USE = 0;
  NORMAL_DATE = 1;  // 按正常日期
  SERVER_OPEN = 2;  // 按开服时间
  USER_CREATE = 3;  // 按创角时间
  MAX_SIZE = 4;     // 最大值
}

// 密林状态
enum FOREST_STAGE {
  WAITING = 0;   // 待献祭
  FEEDING = 1;   // 献祭中
  GROWING = 2;   // 生长中
  FS_NONE = 99;  // 错误状态
}

// 红点
enum RED_POINT {
  RP_NONE = 0;
  GUILD_TODAY_SIGN_IN = 10111;           // 公会是否签到
  GUILD_DUNGEON_FIGHT = 10131;           // 公会副本 - 挑战
  GUILd_DUNGEON_BOSS_BOX_AWARD = 10132;  // 公会副本 - 是否Boss宝箱奖励
  GUILD_DUNGEON_CHAPTER_TASK = 10133;    // 公会副本 - 是否有章节任务奖励
  GUILD_TALENT_LEVEL_CAN_UP = 10141;     //  公会天赋是否可升级
  GUILD_HAVE_APPLY = 10121;              // 公会是否有申请
  GUILD_DUNGEON_TOP_DIVISION = 10152;    // 公会副本赛季最高段位奖励
  GUILD_HAVE_COMBINE_APPLY = 10185;      // 公会是否有合并请求
  RANK_ACHIEVE_POWER = 10601;            // 排行成就 - 战力
  RANK_ACHIEVE_DUNGEON = 10602;          // 排行成就 - 主线
  RANK_ACHIEVE_TOWER = 10603;            // 排行成就 - 爬塔
  RANK_ACHIEVE_ARENA = 10604;            // 排行成就 - 竞技场
  RANK_ACHIEVE_MIRAGE_EMPIRE = 10606;    // 排行成就 - 个人boss - 帝国
  RANK_ACHIEVE_MIRAGE_FOREST = 10607;    // 排行成就 - 个人boss - 森林
  RANK_ACHIEVE_MIRAGE_MOON = 10608;      // 排行成就 - 个人boss - 月影
  RANK_ACHIEVE_MIRAGE_PROTOSS = 10609;   // 排行成就 - 个人boss - 神族
  RANK_ACHIEVE_MIRAGE_DEMON = 10610;     // 排行成就 - 个人boss - 魔族
  RANK_ACHIEVE_MIRAGE_SIX = 10622;       // 排行成就 - 个人boss - 第6个boss
  FRIEND_HAVE_LIKE_RECEIVE = 10911;      // 好友是否有奖励可以领取
  FRIEND_HAVE_REQUEST = 10912;           // 好友是否有申请
  MAZE_HAVE_EVENT_NO_COMPLETE = 20401;   // 迷宫-是否有未完成的事件
  TALES_HAVE_CHAPTER_FIGHT = 20602;      // 列传-是否有故事可以继续
  TALES_HAVE_AWARD_TAKE = 20603;         // 列传-是否有奖励可以领取
  // TALES_HAVE_ELITE_FIGHT = 20605;        // 列传-是否有强敌可以挑战
  // TALES_HAVE_ELITE_WIPE = 20606;         // 列传-是否有强敌可以扫荡
  TALES_ELITE_OPEN_AND_HAVE_NUM = 20607;   // 列传-强敌解锁并且有挑战次数(前端需求)
  DISPATCH_HAVE_AWARD_RECEIVE = 40502;     // 悬赏-是否有奖励可领取
  GOLD_BUY_HAVE_FREE_COUNT = 80201;        // 点金-金币免费次数
  UNREAD_MAIL = 10941;                     // 未读邮件
  NOT_DRAW_MAIL = 10942;                   // 待领奖邮件
  TOWER_CAN_JUMP = 20202;                  // 爬塔 - 可跳关
  TOWER_CAN_FIGHT = 20203;                 // 爬塔 - 可战斗（未到顶层）
  MIRAGE_UNCLAIMED_AWARD_EMPIRE = 20491;   // 个人boss有未领取奖励 - 帝国
  MIRAGE_UNCLAIMED_AWARD_FOREST = 20492;   // 个人boss有未领取奖励 - 森林
  MIRAGE_UNCLAIMED_AWARD_MOON = 20493;     // 个人boss有未领取奖励 - 月影
  MIRAGE_UNCLAIMED_AWARD_PROTOSS = 20494;  // 个人boss有未领取奖励 - 神族
  MIRAGE_UNCLAIMED_AWARD_DEMON = 20495;    // 个人boss有未领取奖励 - 魔族
  MIRAGE_UNCLAIMED_AWARD_SIX = 20496;      // 个人boss有未领取奖励 - 第6个boss
  MIRAGE_CAN_FIGHT_EMPIRE = 20541;         // 个人boss有未通关的关卡 - 帝国
  MIRAGE_CAN_FIGHT_FOREST = 20542;         // 个人boss有未通关的关卡 - 森林
  MIRAGE_CAN_FIGHT_MOON = 20543;           // 个人boss有未通关的关卡 - 月影
  MIRAGE_CAN_FIGHT_PROTOSS = 20544;        // 个人boss有未通关的关卡 - 神族
  MIRAGE_CAN_FIGHT_DEMON = 20545;          // 个人boss有未通关的关卡 - 魔族
  MIRAGE_CAN_FIGHT_SIX = 20546;            // 个人boss有未通关的关卡 - 第6个boss
  TOWERSTAR_DAILY = 20211;                 // 条件爬塔-每日奖励
  TOWERSTAR_FIGHT = 20212;                 // 条件爬塔-能否战斗
  TOWERSTAR_STAR = 20220;                  // 条件爬塔-星级奖励
  // MEDAL_DAILY_AWARD = 40801;               // 功勋-每日领奖
  // MEDAL_TASK_AWARD = 40810;                // 功勋-任务领奖
  // MEDAL_LEVEL_AWARD = 40820;               // 功勋-等级领奖
  ARENA_DIVISION_TASK = 20107;            // 竞技场段位任务
  WRESTLE_BE_DEFEATED = 130005;           // 神树争霸-被击败过
  WRESTLE_PROMOTED_TASK = 130008;         // 神树争霸-有晋级任务
  WRESTLE_LIKE = 130009;                  // 神树争霸-可点赞
  TOWER_SEASON_TASK_RECV_AWARD = 221002;  // 百塔 - 任务奖励
  // DISORDER_LAND_MAP1_HAVE_NO_COMPLETE = 100410;  // 失序空间 - 普通地图 - 节点未完成
  //  DISORDER_LAND_MAP1_STAMINA_IS_FULL = 100423;  // 失序空间 - 普通地图 - 体力已满
  // DISORDER_LAND_MAP2_HAVE_NO_COMPLETE = 100420;  // 失序空间 - 困难地图 - 节点未完成
  //  DISORDER_LAND_MAP2_STAMINA_IS_FULL = 100433;  // 失序空间 - 困难地图 - 体力已满
  // DISORDER_LAND_MAP3_HAVE_NO_COMPLETE = 100430;  // 失序空间 - 困难地图 - 节点未完成
  //  DISORDER_LAND_MAP3_STAMINA_IS_FULL = 100443;  // 失序空间 - 困难地图 - 体力已满
  //  DISORDER_LAND_MAP4_STAMINA_IS_FULL = 100453;  // 失序空间 - 困难地图 - 体力已满
  //  DISORDER_LAND_MAP5_STAMINA_IS_FULL = 100463;  // 失序空间 - 困难地图 - 体力已满
  //  DISORDER_LAND_MAP6_STAMINA_IS_FULL = 100473;  // 失序空间 - 困难地图 - 体力已满
  //  DISORDER_LAND_MAP7_STAMINA_IS_FULL = 100493;  // 失序空间 - 困难地图 - 体力已满
  //  DISORDER_LAND_MAP8_STAMINA_IS_FULL = 100502;  // 失序空间 - 困难地图 - 体力已满
  DISORDER_LAND_MAP_STAMINA_IS_FULL = 100403;  // 失序空间 - 体力已满
  SEASON_COMPLIANCE_AWARD = 341002;            // 赛季冲榜奖励红点
  SEASON_MAP_TASK2_RECV_AWARD = 343001;        // 大地图 - 主线任务奖励
  SEASON_MAP_TASK1_RECV_AWARD = 343002;        // 大地图 - 格子成就任务奖励
  TOWER_POKEMON_TASK_RECV_AWARD = 352001;      // 宠物爬塔 - 任务奖励
  TOWER_POKEMON_SHOP_RECV_AWARD = 352003;      // 宠物爬塔 - 商店兑换
}

enum RESET_TYPE {
  RESET_NULL = 0;     // 不重置
  DAILY = 1;          // 每日重置
  WEEKLY = 2;         // 每周重置
  MONTHLY = 3;        // 每月重置
  WEEKLY_FRIDAY = 4;  // 每周五重置
}

// 爬塔类型
enum TOWER_TYPE {
  TT_NONE = 0;
  TT_COMMON = 1;  // 当前使用的爬塔类型
}

// 爬塔跳关限制
enum TOWER_JUMP {
  TJ_NONE = 0;
  TJ_COUNT_LIMIT = 60;  // 限制单次最多跳关数量
}

// 竞技场结算时间
enum ARENA_TIME {
  AT_NONE = 0;
  DAILY_AWARD = 75600;   // 竞技场每天发奖时间 21*3600 （每天的21:00）
  SEASON_RESET = 85500;  // 竞技场每周日重置时间 23*3600+45*60 （每周日的23:45）
}

// 英雄的水晶属性类型
enum HERO_CRYSTAL_TYPE {
  HCT_FREEDOM = 0;    // 自由
  HCT_CONTRACT = 1;   // 缔约
  HCT_RESONANCE = 2;  // 共鸣
}

// 水晶共鸣属性类型
enum CRYSTAL_SHARE_ATTR_TYPE {
  CSAT_ALL = 0;        // 全部
  CSAT_HERO = 1;       // 英雄
  CSAT_EQUIPMENT = 2;  // 装备
  CSAT_GEM = 3;        // 宝石
  CSAT_EMBLEM = 4;     // 纹章
  CSAT_NONE = -1;      // 默认错误类型
}

// 充值类型
enum RECHARGE_TYPE {
  RT_NORMAL = 0;             // 普通充值
  RT_MONTHLY_CARD = 1;       // 月卡
  RT_FIRST_GIFT = 2;         // 首充礼包
  RT_ACTIVITY_RECHARGE = 3;  // 限时礼包
  RT_PASS = 4;               // 战令
  RT_PUSH_GIFT = 5;          // 推送礼包
  RT_GIFT = 6;               // 直冲活动
  RT_VIP_GIFT = 7;           // VIP付费礼包
  RT_WEB_DIAMOND = 8;        // 官网-钻石充值
  RT_WEB_GIFT = 9;           // 官网-礼包充值
  RT_COUPON = 10;            // 充值代金券
  RT_WEB_COUPON = 11;        // 官网充值代金券
  RT_PROMOTION = 12;         // 新推送礼包
  RT_LIFELONG_GIFT = 13;     // 终身礼包
  RT_ACTIVITY_COUPON = 14;   // 代金券活动
}

// 订单状态
enum ORDER_STATUS {
  OS_NONE = 0;     // 0-未到账新订单
  OS_SAVE = 1;     // 1-已入库
  OS_SUCCESS = 2;  // 2-已发货
  OS_FAIL = 3;     // 失败
}

// 订单类型
enum ORDER_TYPE {
  OT_RECHARGE = 0;     // 充值订单
  OT_REPLACEMENT = 1;  // 后台补单
  OT_WELFARE = 2;      // 福利订单
  OT_COUPON = 3;       // 代金券
}

// 退款状态
enum REFUND_STATUS {
  RS_NONE = 0;
  RS_SUCCESS = 1;
  RS_FAIL = 2;
}

// 限时礼包类型
enum ACTIVITY_RECHARGE_SHOP {
  ARS_NONE = 0;
  ARS_DAY_RESET = 1;          // 日充值商店
  ARS_WEEK_RESET = 2;         // 周重置商店
  ARS_OPEN_LIMIT = 100;       // 开服礼包
  ARS_DAILY_ONE_CLICK = 101;  // 每日一键购买商店
  ARS_DAILY_SPECIAL = 102;    // 每日特惠商店
  ARS_PUZZLE_SHOP = 603;      // 拼图活动商店
}

// 显示礼包购买类型
enum ACTIVITY_RECHARGE_SHOP_BUY_TYPE {
  ARSBT_SUCCESS = 0;  // 购买成功
  ARSBT_CHECK = 1;    // 购买检查
}

// 礼包限购类型
enum ACTIVITY_RECHARGE_GIFT_RESET_TYPE {
  ARGET_NONE = 0;
  ARGET_DAILY = 1;
}

// 任务逻辑类型
enum TASK_TYPE {
  TTYPE_NONE = 0;
  TTYPE_HAVE_EMPIRE_HERO = 4001;    // 获得多少个帝国x星英雄
  TTYPE_HAVE_FOREST_HERO = 4002;    // 获得多少个森林x星英雄
  TTYPE_HAVE_MOON_HERO = 4003;      // 获得多少个月影x星英雄
  TTYPE_HAVE_PROTOSS_HERO = 4004;   // 获得多少个神使x星英雄
  TTYPE_HAVE_DEMON_HERO = 4005;     // 获得多少个魔裔x星英雄
  TTYPE_CONTINUE_LOGIN_DAY = 8001;  // 连续登陆天数
}

enum TASK_TYPE_ID {
  TTYPE_ID_NONE = 0;
  TTYPE_ID_MIRAGE_PASS_EMPIRE_FLOOR = 4801001;       // 帝国个人BOSS到多少层
  TTYPE_ID_MIRAGE_PASS_FOREST_FLOOR = 4801002;       // 森林个人BOSS到多少层
  TTYPE_ID_MIRAGE_PASS_MOON_FLOOR = 4801003;         // 月影个人BOSS到多少层
  TTYPE_ID_MIRAGE_PASS_PROTOSS_FLOOR = 4801004;      // 神使个人BOSS到多少层
  TTYPE_ID_MIRAGE_PASS_DEMON_FLOOR = 4801005;        // 魔裔个人BOSS到多少层 Task_info_Type 表中未配置
  TTYPE_ID_SUMMON_DAILY_ADVANCED_COUNT = 1006000;    // 高级高级招募抽卡
  TTYPE_ID_SUMMON_HERO_RARE_TRUE_FIVE = 1007000;     // 抽到真5英雄
  TTYPE_ID_MAZE_BOSS_FIGHT_WIN = 4600000;            // 击败迷宫BOSS多少次
  TTYPE_ID_MAIN_DUNGEON_FLOOR = 2801000;             // 主线任务到达多少层
  TTYPE_ID_COMPLETE_ANY_DISPATCH_TASK = 3803000;     // 完成任意品质悬赏任务
  TTYPE_ID_COMPLETE_GREEN_DISPATCH_TASK = 3804000;   // 完成绿色及以上的悬赏任务
  TTYPE_ID_COMPLETE_BLUE_DISPATCH_TASK = 3805000;    // 完成蓝色及以上的悬赏任务
  TTYPE_ID_COMPLETE_PURPLE_DISPATCH_TASK = 3806000;  // 完成紫色及以上的悬赏任务
  TTYPE_ID_COMPLETE_ORANGE_DISPATCH_TASK = 3807000;  // 完成橙色及以上的悬赏任务
  TTYPE_ID_COMPLETE_RED_DISPATCH_TASK = 3808000;     // 完成红色及以上的悬赏任务
  TTYPE_ID_TOWER_SEASON_ATTACH_FLOOR = 5900000;      // 百塔到达最高到达多少层
}

// 密林 - 树的类型
enum FOREST_TREE_TYPE {
  FTT_NONE = 0;
  FTT_PEACEFUL = 1;  // 和平
  FTT_PVP = 2;       // PVP
}

// 密林 - pvp树开启的密林等级要求
enum FOREST_PVP_TREE {
  FPT_NONE = 0;
  OLD_OPEN_LV = 5;  // 老版，pvp树开启等级
  OPEN_LV = 9;      // 新版，pvp树开启等级
}

// 头像框类型
enum AVATAR_TYPE {
  ATP_NONE = 0;
  ATP_ARENA_DIVISION = 1000;  // 竞技场段位头像框，避免跟以后加属性时重复，从1000开始编号
  ATP_ARENA_FOREVER = 1001;   // 竞技场段位永久头像框
}

enum GM_XML {
  OPERATE_NONE = 0;
  OPERATE_ACTIVITY_INFO = 1;
  OPERATE_GIFT_INFO = 2;
  OPERATE_MAX = 3;
}

enum OPERATE_ACTIVITY_TYPE {
  OPERATE_ACTIVITY_TYPE_NONE = 0;
  OPERATE_ACTIVITY_TYPE_TASK = 3;  // 任务
  OPERATE_ACTIVITY_TYPE_GIFT = 4;  // 直充
  PROMOTION_DISCOUNT_GIFT = 6;     // 新推送礼包-折扣礼包
  PROMOTION_OPTIONAL_GIFT = 7;     // 新推送礼包-自选礼包
  PROMOTION_CHAIN_GIFT = 8;        // 新推送礼包-连购礼包
}

enum PROMOTION_GIFT_LIMIT_TYPE {
  PGLT_NONE = 0;
  PGLT_RECHARGE_AMOUNT = 1;  // 累充金额
  PGLT_USER_LEVEL = 2;       // 玩家等级
  PGLT_PGP = 3;              // 人群包
  PGLT_MAX = 4;
}

enum CHAT_LIKE {
  CL_NONE = 0;
  CL_GET_LIST_MAX_LENGTH = 50;  // 获取点赞列表最大长度
  CL_MAX_LIKE_NUM = 100;        // 最大点赞数量
}

// 跨服竞技场 - 战场等级
enum WRESTLE_LEVEL {
  WL_ALL = 0;    // 全部
  WL_FIRST = 1;  // 第一个战场
  WL_SECOND = 2;
  WL_THIRD = 3;
  WL_FOURTH = 4;
  WL_FIFTH = 5;    // 最高级别战场
  WL_SIXTH = 6;    // 最高级别战场
  WL_SEVENTH = 7;  // 最高级别战场
  WL_EIGHTH = 8;   // 最高级别战场
  WL_NINTH = 9;    // 最高级别战场
  WL_MAX = 10;     // 不可以>=WL_MAX
}

// 阵容id
enum FORMATION_ID {
  FI_NONE = 0;
  FI_DUNGEON = 7;         // 主线
  FI_ARENA_ATTACK = 34;   // 竞技场进攻阵容
  FI_ARENA_DEFENSE = 35;  // 竞技场防守阵容
  FI_TOWER = 40;          // 爬塔
  FI_TRIAL_GOLD = 45;     // 材料本（金币）
  FI_TRIAL_EXP = 46;      // 材料本（经验）
  FI_TRIAL_EQUIP = 47;    // 材料本（装备）
  FI_TRIAL_HERO = 48;     // 材料本（英雄）
  // FI_TRIAL_EMBLEM = 49;          //材料本（纹章）
  FI_MAZE = 60;                         // 迷宫
  FI_MIRAGE_RACE_EMPIRE = 70;           // 个人boss - 帝国
  FI_MIRAGE_RACE_FOREST = 71;           // 个人boss - 森林
  FI_MIRAGE_RACE_MOON = 72;             // 个人boss - 月影
  FI_MIRAGE_RACE_PROTOSS = 73;          // 个人boss - 神族
  FI_MIRAGE_RACE_DEMON = 74;            // 个人boss - 魔族
  FI_MIRAGE_RACE_SIX = 75;              // 个人boss - 第6个boss
  FI_TALES = 82;                        // 英雄列传
  FI_GUILD_DUNGEON = 83;                // 公会副本
  FI_TALES_ELITE = 85;                  // 英雄列传强敌
  FI_FOREST_ATTACK = 91;                // 密林进攻阵容
  FI_FOREST_DEFENSE = 92;               // 密林防守阵容
  FI_TOWERSTAR = 113;                   // 条件爬塔
  FI_GODDESS_CONTRACT = 190;            // 契约之所阵容进攻阵容
  FI_CROSS_ARENA_ATTACK = 199;          // 跨服竞技场进攻阵容
  FI_CROSS_ARENA_DEFENSE = 200;         // 跨服竞技场防守阵容
  FI_FLOWER_ATTACK = 208;               // 0.9.8密林进攻阵容
  FI_FLOWER_DEFENSE = 209;              // 0.9.8密林防守阵容
  FI_TOWER_SEASON = 221;                // 百塔
  FI_WORLD_BOSS = 240;                  // 世界boss
  FI_ACTIVITY_STORY = 242;              // 活动故事
  FI_ACTIVITY_STORY_EQUAL = 243;        // 活动故事公平模式
  FI_SEASON_DUNGEON = 251;              // 赛季主线
  FI_GUILD_DUNGEON_1 = 301;             // 公会副本阵容ID1
  FI_GUILD_DUNGEON_2 = 302;             // 公会副本阵容ID2
  FI_GUILD_DUNGEON_3 = 303;             // 公会副本阵容ID3
  FI_GUILD_DUNGEON_4 = 304;             // 公会副本阵容ID4
  FI_GUILD_DUNGEON_5 = 305;             // 公会副本阵容ID5
  FI_GUILD_DUNGEON_6 = 306;             // 公会副本阵容ID6
  FI_PEAK_1 = 307;                      // 巅峰竞技场5队
  FI_PEAK_2 = 308;                      // 巅峰竞技场7队
  FI_PRE_SEASON_LINK_1 = 401;           // 赛季羁绊PVE预设阵容1
  FI_PRE_SEASON_LINK_2 = 402;           // 赛季羁绊PVE预设阵容2
  FI_PRE_SEASON_LINK_3 = 403;           // 赛季羁绊PVE预设阵容3
  FI_PRE_SEASON_LINK_4 = 404;           // 赛季羁绊PVE预设阵容4
  FI_PRE_SEASON_LINK_5 = 405;           // 赛季羁绊PVE预设阵容5
  FI_PRE_SEASON_LINK_6 = 406;           // 赛季羁绊PVE预设阵容6
  FI_PRE_SEASON_LINK_7 = 407;           // 赛季羁绊PVE预设阵容7
  FI_PRE_SEASON_LINK_8 = 408;           // 赛季羁绊PVE预设阵容8
  FI_PRE_SEASON_LINK_9 = 409;           // 赛季羁绊PVE预设阵容9
  FI_PRE_SEASON_LINK_10 = 410;          // 赛季羁绊PVE预设阵容10
  FI_DISORDER_LAND_HURDLE_A = 420;      // 失序空间 - A类关卡阵容ID
  FI_DISORDER_LAND_HURDLE_B = 421;      // 失序空间 - B类关卡阵容ID
  FI_DISORDER_LAND_HURDLE_C = 422;      // 失序空间 - C类关卡阵容ID
  FI_DISORDER_LAND_HURDLE_D = 423;      // 失序空间 - D类关卡阵容ID
  FI_DISORDER_LAND_HURDLE_E = 424;      // 失序空间 - E类关卡阵容ID
  FI_DISORDER_LAND_HURDLE_F = 425;      // 失序空间 - F类关卡阵容ID
  FI_DISORDER_LAND_HURDLE_G = 426;      // 失序空间 - G类关卡阵容ID
  FI_DISORDER_LAND_HURDLE_H = 427;      // 失序空间 - H类关卡阵容ID
  FI_GST = 430;                         // 公会战
  FI_PRE_SEASON_LINK_PVP_1 = 431;       // 赛季羁绊PVP预设阵容1
  FI_PRE_SEASON_LINK_PVP_2 = 432;       // 赛季羁绊PVP预设阵容2
  FI_PRE_SEASON_LINK_PVP_3 = 433;       // 赛季羁绊PVP预设阵容3
  FI_PRE_SEASON_LINK_PVP_4 = 434;       // 赛季羁绊PVP预设阵容4
  FI_PRE_SEASON_LINK_PVP_5 = 435;       // 赛季羁绊PVP预设阵容5
  FI_PRE_SEASON_LINK_PVP_6 = 436;       // 赛季羁绊PVP预设阵容6
  FI_PRE_SEASON_LINK_PVP_7 = 437;       // 赛季羁绊PVP预设阵容7
  FI_PRE_SEASON_LINK_PVP_8 = 438;       // 赛季羁绊PVP预设阵容8
  FI_PRE_SEASON_LINK_PVP_9 = 439;       // 赛季羁绊PVP预设阵容9
  FI_PRE_SEASON_LINK_PVP_10 = 440;      // 赛季羁绊PVP预设阵容10
  FI_SEASON_ARENA_THREE_ATTACK = 450;   // 赛季竞技场三队进攻阵容
  FI_SEASON_ARENA_THREE_DEFENSE = 451;  // 赛季竞技场三队防守阵容
  FI_SEASON_ARENA_FIVE_ATTACK = 452;    // 赛季竞技场五队进攻阵容
  FI_SEASON_ARENA_FIVE_DEFENSE = 453;   // 赛季竞技场五队防守阵容
  FI_SEASON_ARENA_SEVEN_ATTACK = 454;   // 赛季竞技场七队进攻阵容
  FI_SEASON_ARENA_SEVEN_DEFENSE = 455;  // 赛季竞技场七队防守阵容
  FI_SEASON_ARENA_NINE_ATTACK = 456;    // 赛季竞技场九队进攻阵容
  FI_SEASON_ARENA_NINE_DEFENSE = 457;   // 赛季竞技场九队防守阵容
  FI_GST_BOSS_1 = 461;                  // 公会boss战阵容1
  FI_GST_BOSS_2 = 462;                  // 公会boss战阵容2
  FI_GST_BOSS_3 = 463;                  // 公会boss战阵容3
  FI_GST_BOSS_4 = 464;                  // 公会boss战阵容4
  FI_GST_BOSS_5 = 465;                  // 公会boss战阵容5
  FI_GST_BOSS_6 = 466;                  // 公会boss战阵容6
  FI_GST_BOSS_7 = 467;                  // 公会boss战阵容7
  FI_GST_BOSS_8 = 468;                  // 公会boss战阵容8
  FI_GST_DRAGON_1 = 481;                // 公会战龙战阵容1
  FI_GST_DRAGON_2 = 482;                // 公会战龙战阵容2
  FI_GST_DRAGON_3 = 483;                // 公会战龙战阵容3
  FI_GST_DRAGON_4 = 484;                // 公会战龙战阵容4
  FI_GST_DRAGON_5 = 485;                // 公会战龙战阵容5
  FI_BOSS_RUSH_1 = 490;                 // Boss挑战阵容1
  FI_BOSS_RUSH_2 = 491;                 // Boss挑战阵容2
  FI_BOSS_RUSH_3 = 492;                 // Boss挑战阵容3
  FI_BOSS_RUSH_4 = 493;                 // Boss挑战阵容4
  FI_BOSS_RUSH_5 = 494;                 // Boss挑战阵容5
  FI_BOSS_RUSH_6 = 495;                 // Boss挑战阵容6
  FI_BOSS_RUSH_7 = 496;                 // Boss挑战阵容7
  FI_BOSS_RUSH_8 = 497;                 // Boss挑战阵容8
  FI_GST_ORE_1 = 521;                   // 公会ore阵容1
  FI_GST_ORE_2 = 522;                   // 公会ore阵容2
  FI_GST_ORE_3 = 523;                   // 公会ore阵容3
  FI_GST_ORE_4 = 524;                   // 公会ore阵容4
  FI_GST_ORE_5 = 525;                   // 公会ore阵容5
  FI_GST_ORE_6 = 526;                   // 公会ore阵容6
  FI_GST_ORE_7 = 527;                   // 公会ore阵容7
  FI_GST_ORE_8 = 528;                   // 公会ore阵容8
  FI_DUEL_1 = 529;                      // 切磋阵容 1v1
  FI_DUEL_2 = 530;                      // 切磋阵容 2v2
  FI_DUEL_3 = 531;                      // 切磋阵容 5v5
  FI_GST_CHALLENGE = 535;               // 公会战新擂台赛
  FI_SEASON_DOOR_1 = 536;               // 开门玩法
  FI_SEASON_DOOR_2 = 537;               // 开门玩法
  FI_SEASON_DOOR_3 = 538;               // 开门玩法
  FI_SEASON_DOOR_4 = 539;               // 开门玩法
  FI_SEASON_DOOR_5 = 540;               // 开门玩法
  FI_SEASON_DOOR_6 = 541;               // 开门玩法
  FI_SEASON_DOOR_7 = 542;               // 开门玩法
  FI_SEASON_DOOR_8 = 543;               // 开门玩法
  FI_SEASON_MAP_1 = 544;
  FI_SEASON_MAP_2 = 545;
  FI_SEASON_MAP_3 = 546;
  FI_SEASON_MAP_4 = 547;
  FI_SEASON_MAP_5 = 548;
  FI_SEASON_MAP_6 = 549;
  FI_SEASON_MAP_7 = 550;
  FI_SEASON_MAP_8 = 551;
  FI_SEASON_MAP_9 = 552;
  FI_SEASON_MAP_10 = 553;
  FI_SEASON_MAP_11 = 554;
  FI_SEASON_MAP_12 = 555;
  FI_SEASON_MAP_13 = 556;
  FI_SEASON_MAP_14 = 557;
  FI_SEASON_MAP_15 = 558;
  FI_SEASON_MAP_16 = 559;
  FI_SEASON_MAP_17 = 560;
  FI_TOWER_POKEMON = 561;
}

// 跨服竞技场结算时间
enum CROSS_ARENA_TIME {
  CAT_NONE = 0;
  CAT_RESET_BEGIN = 75600;  // 重置结算开始 21*3600 （每天的21:00）
  CAT_RESET_END = 77400;    // 重置结算结束 21*3600 + 1800（每天的21:30）
}

enum WRESTLE_STATUS {
  WS_NONE = 0;       // 正常
  WS_RESETTING = 1;  // 结算中
  WS_PREPARING = 2;  // 准备中
}

// 跨服竞技场对手状态
enum WRESTLE_OP_STATUS {
  WOS_NONE = 0;      // 正常
  WOS_REVENGE = 1;   // 可复仇
  WOS_DEFEATED = 2;  // 已击败
}

enum WRESTLE_RANK {
  WR_NONE = 0;
  WR_CURRENT = 1;       // 当前排名
  WR_HALL_OF_NAME = 2;  // 荣誉殿堂排名
}

// 公会副本周结算时间
enum GUILD_DUNGEON_WEEKLY_TIME {
  GDWT_NONE = 0;
  WEEKLY_RESET_CLOSE = 72000;  // 公会副本每周日关闭时间 20*3600 （每周四的20:00）
  WEEKLY_RESET_BEGIN = 75600;  // 公会副本每周日重置时间 21*3600 （每周四的21:00）
  WEEKLY_RESET_END = 86400;    // 公会副本每周日重置结束时间 24*3600 （每周四的24:00）
}

// 公会副本月结算时间
enum GUILD_DUNGEON_MONTHLY_TIME {
  GDMT_NONE = 0;
  MONTHLY_RESET_CLOSE = 72000;  // 公会副本赛季关闭时间   20*3600    （赛季最后一天的20:00）
  MONTHLY_RESET_BEGIN = 75600;  // 公会副本赛季重置时间 21*3600 （赛季最后一天的21:00）
  MONTHLY_RESET_END = 86400;    // 公会副本赛季重置结束时间  24*3600  （赛季最后一天的24:00）
}

enum DB_BASE_USER {
  ICON_EXPIRED = 0;          // 头像过期时间
  FARM_EXPIRED = 1;          // 头像框过期时间
  IMAGE_EXPIRED = 2;         // 形象过期时间
  CHAT_BUBBLES_EXPIRED = 3;  // 聊天气泡过期时间
  TITLE_EXPIRED = 4;         // 称号过期时间
  MAX_EXPIRED = 5;
}

enum AVATAR_DEFAULT {
  AVATAR_NONE = 0;
  ICON = 1;
  FRAME = 1000;
  IMAGE = 50000;
  CHAT_BUBBLES = 13000;
}

enum AVATAR_SET_INDEX {
  ASI_ICON = 0;
  ASI_FRAME = 1;
  ASI_IMAGE = 2;
  ASI_CHAT_BUBBLES = 3;
  ASI_MAX = 4;
}

// 密林 - 据点模式开启等级
enum FLOWER_OCCUPY {
  FO_NONE = 0;
  FO_OPEN_LV = 6;
}

// 密林 - 日志类型，cl.FlowerLogDetail中的type
enum FLOWER_LOG {
  FL_NONE = 0;
  FL_SNATCHED = 1;      // 被抢夺，防守失败（抢夺模式）
  FL_OCCUPIED = 2;      // 被进攻，防守失败（据点模式）
  FL_OCCUPIED_WIN = 3;  // 进攻，成功抢到据点（据点模式）
  FL_ASSISTED = 4;      // 玩家被协助后收到日志，用于赠送友情点（协助模式）
  FL_ASSIST = 5;        // 玩家协助后收到其他玩家的感谢日志，用于收取友情点（协助模式）
}

// 密林 - 日志类型，用于C2L_FlowerLogList参数
enum FLOWER_LOG_TYPE {
  FLT_NONE = 0;
  FLT_SNATCH = 1;  // 掠夺模式
  FLT_OCCUPY = 2;  // 据点模式
}

// 密林 - 据点模式占领结束类型
enum FLOWER_OCCUPY_END {
  FOE_NONE = 0;
  FOE_TIME_UP = 1;          // 占领时间到了
  FOE_ABANDON = 2;          // 占了新据点，老据点结算
  FOE_ROBBED_REASSIGN = 3;  // 被打后，分配新据点
  FOE_ROBBED_END = 4;       // 被打后，未分配新据点
}

// 密林状态
enum FLOWER_STAGE {
  FS_WAITING = 0;   // 待献祭
  FS_FEEDING = 1;   // 献祭中
  FS_GROWING = 2;   // 生长中
  FS_FS_NONE = 99;  // 错误状态
}

enum GODDESS_BODY {
  TOUCH_TYPE_NONE = 0;
  TOUCH_TYPE_NORMAL = 1;
  TOUCH_TYPE_INTIMATE = 2;
}

// enum NEW_EMBLEM_ATTR {
//  ATTR_NONE = 0;
//  ATTR_ADDITIVE_HERO = 1;
//  ATTR_SKILL = 2;
//}

enum DAILY_WISH {
  WEIGHT_NONE = 0;
  WEIGHT_START_ID = 1;
  WEIGHT_END_ID = 7;
  WEIGHT_MAX = 8;
}

// 神器首发 - 活动类型
enum ARTIFACT_DEBUT_TYPE {
  ADT_NONE = 0;
  ADT_NEW = 1;     // 新服活动
  ADT_COMMON = 2;  // 通服活动
}

// 神器首发 - 抽卡分类
enum ARTIFACT_DEBUT_SUMMON {
  CATEGORY_NONE = 0;
  CATEGORY_SENIOR = 1;  // 高级抽
  CATEGORY_JUNIOR = 2;  // 低级抽
}

// 神器首发 - 活动领奖类型
enum ARTIFACT_DEBUT_ACT_RECV {
  ADAR_NONE = 0;
  ADAR_LOGIN = 1;   // 累登活动
  ADAR_SUMMON = 2;  // 累抽活动
  ADAR_PUZZLE = 3;  // 拼图活动
}

// 神器首发 - 抽卡活动分类
enum ARTIFACT_DEBUT_DRAW_ACT_TYPE {
  DRAW_NONE = 0;
  DRAW_WHOLE = 1;     // 旧抽卡活动-可以抽到完整神器
  DRAW_FRAGMENT = 2;  // 新抽卡活动-只能抽到神器碎片
}

enum DIVINE_DEMON_TASK_TYPE {
  DTT_NONE = 0;
  DTT_SUMMON = 1;   // 累抽任务
  DTT_ACTIVE = 2;   // 活跃任务
  DTT_UP_STAR = 3;  // 升星任务
}

enum DIVINE_DEMON_SUMMON_TYPE {
  DST_NONE = 0;
  DSN_SINGLE = 1;  // 单抽
  DSN_MANY = 2;    // 连抽
}

enum DIVINE_DEMON_SUMMON_COST_TYPE {
  DDSCT_NONE = 0;
  DDSCT_DIAMOND = 1;      // 钻石
  DDSCT_SUMMON_ITEM = 2;  // 抽卡卷
  DDSCT_SUMMON_FREE = 3;  // 免费
  DDSCT_MAX = 4;
}

// 轮次活动类型
enum ROUND_ACTIVITY_TYPE {
  RAT_NONE = 0;
  RAT_HERO_SENIOR = 1;     // 高抽返利活动
  RAT_PROPHET = 2;         // 先知抽返利活动
  RAT_ARTIFACT = 3;        // 常驻神器抽返利活动
  RAT_LINK = 4;            // 流派抽返利活动
  RAT_ARTIFACT_DEBUT = 5;  // 神器首发活动
  RAT_DIVINE_DEMON = 6;    // 神魔首发活动
  RAT_MIRAGE = 8;          // 个人 boss活动
  RAT_WORLD_BOSS = 9;      // 世界boss活动
}

// 百塔结算时间
enum TOWER_SEASON_TIME {
  TST_NONE = 0;
  TST_RESET_BEGIN = 72000;  // 重置结算开始 20*3600 （每天的20:00）
  TST_RESET_END = 86400;    // 重置结束时间 24*4600 （每天的24:00）
}

enum GOD_PRESENT_BEHAVIOR_TYPE {
  GPBT_NONE = 0;
  GPBT_SUMMON = 1;
  GPBT_REPLACE = 2;
  GPBT_COLLECTED = 3;
}

// 累登 - 领奖 op type
enum DAILY_ATTENDANCE_AWARD {
  DAA_NONE = 0;
  DAA_FIRST = 1;   // 第一次领奖
  DAA_SECOND = 2;  // 第二次领奖
  DAA_BOTH = 3;    // 2次奖励都领取
}

// 每日特惠 - 领奖 type
enum DAILY_SPECIAL_RECV_AWARD {
  DSRA_NONE = 0;
  DSRA_DAILY = 1;  // 每日领奖
  DSRA_SCORE = 2;  // 积分领奖
}

// 公会宝箱 - 点赞类型
enum GUILD_CHEST_LIKE_TYPE {
  GCLT_NONE = 0;
  GCLT_NORMAL = 1;
  GCLT_SPECIFICAL = 2;
}

// 世界boss - 难度等级
enum WORLD_BOSS_LEVEL {
  WBL_NONE = 0;
  WBL_NORMAL = 1;      // 普通等级
  WBL_HARD = 2;        // 困难等级
  WBL_NIGHT_MIRE = 3;  // 噩梦等级
  WBL_MAX = 4;
}

// 世界boss - 房间日志类型
enum WORLD_BOSS_LOG_TYPE {
  WBLT_NONE = 0;
  WBLT_HURT = 1;         // 任意玩家造成伤害
  WBLT_JOIN_ROOM = 2;    // 任意玩家匹配进入房间
  WBLT_RANK_FIRST = 3;   // 任意玩家获得第一名
  WBLT_RANK_CHANGE = 4;  // 玩家排名变化
}

// 世界boss - 排行榜类型
enum WORLD_BOSS_RANK_TYPE {
  WBRT_NONE = 0;
  WBRT_PARTITION = 1;   // 战区榜
  WBRT_ROOM = 2;        // 房间榜
  WBRT_FIGHT_STAR = 3;  // 讨伐之星
  WBRT_SELF = 4;        // 玩家自身
  WBRT_MAX = 5;
}

// 世界boss - 战斗类型
enum WORLD_BOSS_FIGHT_TYPE {
  WBFT_NONE = 0;
  WBFT_FIGHT = 1;  // 挑战
  WBFT_SWEEP = 2;  // 扫荡
}

// 百塔 - 排行榜
enum TOWER_SEASON_RANK {
  TSR_NONE = 0;
  TSR_CURRENT = 1;             // 本赛季排名
  TSR_HALL_OF_NAME = 2;        // 荣誉殿堂排名
  TSR_HALL_OF_NAME_TOP_3 = 3;  // 上赛季top3
  TSR_CURRENT_ACTIVITY = 4;    // 本赛季冲榜排名
}

enum ACTIVITY_STORY_FIGHT_TYPE {
  ASFT_NONE = 0;
  ASFT_FIGHT = 1;  // 挑战
  ASFT_SWEEP = 2;  // 扫荡
}

// 永恒仪式 - 克制状态
enum RITE_RESTRICT_STATE {
  RRS_NONE = 0;           // 无克制
  RRS_RESTRICT = 1;       // 克制对手
  RRS_BE_RESTRICTED = 2;  // 被对手克制
}

// 失序空间 - 体力购买
enum DISORDER_LAND_BUY_TYPE {
  DLBT_NONE = 0;
  DLBT_DIAMOND = 1;  // 钻石购买
  DLBT_ITEM = 2;     // 道具购买
}

// 失序空间 - 难度等级
enum DISORDER_LAND_LEVEL {
  DLL_NONE = 0;
  DLL_NORMAL = 1;              // 普通等级
  DLL_HARD = 2;                // 困难等级
  DLL_NIGHT_MIRE = 3;          // 噩梦等级
  DLL_DISORDER_FOUR = 4;       // 第四个难度
  DLL_DISORDER_FIVE = 5;       // 第五个难度
  DLL_DISORDER_SIX = 6;        // 第六个难度
  DLL_DISORDER_SEVEN = 7;      // 第七个难度
  DLL_DISORDER_EIGHT = 8;      // 第八个难度
  DLL_DISORDER_NINE = 9;       // 第九个难度
  DLL_DISORDER_TEN = 10;       // 第10个难度
  DLL_DISORDER_ELEVEN = 11;    // 第11个难度
  DLL_DISORDER_TWELVE = 12;    // 第12个难度
  DLL_DISORDER_THIRTEEN = 13;  // 第13个难度
  DLL_DISORDER_FOURTEEN = 14;  // 第14个难度
  DLL_DISORDER_FIFTEEN = 15;   // 第15个难度
  DLL_DISORDER_SIXTEEN = 16;   // 第16个难度
  DLL_DISORDER_MAX = 17;
}

// 失序空间 - 事件类型
enum DISORDER_LAND_EVENT_TYPE {
  DLET_NONE = 0;
  DLET_BATTLE = 1;  // 关卡事件
  DLET_STONE = 2;   // 祝福石
  DLET_BOX = 3;     // 宝箱
  DLET_RELIC = 4;   // 遗物
  DLET_MAX = 5;
}

// 失序空间 - 节点解锁
enum DISORDER_LAND_NODE_UNLOCK {
  DLNU_NONE = 0;
  DLNU_UNLOCK = 1;  // 默认解锁
  DLNU_ANY = 2;     // 关联节点任意一个解锁
  DLNU_ALL = 3;     // 关联节点都解锁
  DLNU_MAX = 4;
}

// 失序空间 - 品质概率加成
enum DISORDER_LAND_RARE {
  DLR_NONE = 0;
  DLR_WHITE = 10;   // 白色
  DLR_GREEN = 20;   // 绿色
  DLR_BLUE = 30;    // 蓝色
  DLR_PURPLE = 40;  // 紫色
  DLR_ORANGE = 50;  // 橙色
  DLR_RED = 60;     // 红色
  DLR_MIGHT = 70;   // 威能
  DLR_MAX = 71;
}

// 失序空间 - 概率加成类型
enum DISORDER_LAND_ADD {
  DLA_NONE = 0;
  DLA_BATTLE = 1;  // 通关加成
  DLA_STONE = 2;   // 祝福石加成
  DLA_MAX = 3;
}

// 失序空间 - 战斗类型
enum DISORDER_LAND_BATTLE {
  DLB_NONE = 0;
  DLB_BATTLE = 1;  // 挑战
  DLB_SWEEP = 2;   // 扫荡
  DLB_MAX = 3;
}

// 失序空间 - group_tag
enum DISORDER_LAND_GROUP_TAG {
  DLGT_NONE = 0;
  DLGT_TAG1 = 1;
  DLGT_TAG2 = 2;
  DLGT_TAG3 = 3;
  DLGT_MAX = 4;
}

// 失序空间 - 关卡类型
enum DISORDER_LAND_HURDLE_TYPE {
  DLHT_NONE = 0;
  DLHT_A = 1;
  DLHT_B = 2;
  DLHT_C = 3;
  DLHT_D = 4;
  DLHT_E = 5;
  DLHT_F = 6;
  DLHT_G = 7;
  DLHT_H = 8;
  DLHT_MAX = 9;
}

// 巅峰竞技场 - 排行榜类型
enum PEAK_RANK_TYPE {
  PRT_NONE = 0;
  PRT_SEASON_TOP3 = 1;  // 巅峰霸主
  PRT_PHASE_TOP8 = 2;   // 名人堂
  PRT_SEASON_ALL = 3;   // 赛季积分榜
}

// 巅峰竞技场 - 每轮持续时间(秒)，为提高测试效率，可以适当减小此值
//  xxx正式环境必须设为86400
enum PEAK_ROUND_DURATION {
  PRD_NONE = 0;
  PRD_TIME = 86400;
}

// 巅峰竞技场 - 玩法进度状态
enum PEAK_STATUS {
  PS_NONE = 0;
  PS_INIT = 1;    // 初始化中
  PS_RESET = 2;   // 结算重置中
  PS_NORMAL = 3;  // 正常
}

// 巅峰竞技场 - 赛季状态
enum PEAK_SEASON_STATE {
  PSS_INIT = 0;     // 赛季未开始
  PSS_RUNNING = 1;  // 赛季进行中
  PSS_END = 2;      // 赛季已完成
}

// 巅峰竞技场 - 每期状态
enum PEAK_PHASE_STATE {
  PPS_INIT = 0;       // 未开始
  PPS_INIT_DATA = 1;  // 数据初始化
  PPS_RUNNING = 2;    // 进行中
  PPS_END = 3;        // 已完成
}

// 巅峰竞技场 - 每轮状态
enum PEAK_ROUND_STATE {
  PRS_INIT = 0;     // 未开始
  PRS_RUNNING = 1;  // 进行中
  PRS_END = 2;      // 已完成
}

// 巅峰竞技场 - 赛季排行榜最多展示人数
enum PEAK_SEASON_RANK {
  PSR_NONE = 0;
  PSR_SHOW_COUNT = 200;
}

enum SEASON_DUNGEON_TYPE {
  SDT_NORMAL = 0;
  SDT_THROUGH = 1;
}

enum SEASON_DATA_RECORD_TYPE {
  SDRT_NONE = 0;
  SDRT_LEVEL = 10001;
  SDRT_DUNGEON = 10002;
  SDRT_GET_GREY_MARK = 10003;
  SDRT_GET_GREEN_MARK = 10004;
  SDRT_GET_BLUE_MARK = 10005;
  SDRT_GET_PURPLE_MARK = 10006;
  SDRT_GET_ORANGE_MARK = 10007;
  SDRT_GET_RED_MARK = 10008;
  SDRT_ACTIVATION_GREY_RITE = 10009;
  SDRT_ACTIVATION_GREEN_RITE = 10010;
  SDRT_ACTIVATION_BLUE_RITE = 10011;
  SDRT_ACTIVATION_PURPLE_RITE = 10012;
  SDRT_ACTIVATION_ORANGE_RITE = 10013;
  SDRT_ACTIVATION_RED_RITE = 10014;
  SDRT_GET_POWER_MARK = 10015;
  SDRT_PVE_WIN_WITH_RITE = 10016;
  SDRT_PEAK_BEAST_RANK = 10017;
  SDRT_DISORDER_LAND_FIRST_WIN = 10018;
  SDRT_DISORDER_LAND_GET_BOX = 10019;
  SDRT_DISORDER_LAND_GET_RELIC = 10020;
  SDRT_DISORDER_LAND_GET_STONE = 10021;
}

// 赛季回流类型
enum SEASON_RETURN_TYPE {
  SRT_NONE = 0;
  SRT_OPEN = 1;    // 首次进入
  SRT_RETURN = 2;  // 非首次进入
}

// 个人Boss扫荡类型
enum MIRAGE_SWEEP_TYPE {
  MST_NONE = 0;
  MST_NORMAL = 1;     // 普通扫荡
  MST_ASSISTANT = 2;  // 小助手扫荡
}
enum GVG_DONATE_TYPE {
  GVGD_NONE = 0;
  GVGD_ITEM = 1;
  GVGD_MANAGER = 2;
  GVGD_MAX = 3;
}

enum EMBLEM_SUCCINCT_LOCK_TYPE {
  ESLT_NONE = 0;
  ESLT_SKILL = 1;  // 锁定词条
  ESLT_SUIT = 2;   // 锁定套装
}

enum EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE {
  ESLOSOT_NONE = 0;
  ESLOSOT_LOCK = 1;  // 锁定
  ESLOSOT_SAVE = 2;  // 保存
}

// 符文词缀类型
enum EMBLEM_AFFIX_TYPE {
  EAT_NONE = 0;
  EAT_ANCIENT = 1;  // 远古
  EAT_ARCHAIC = 2;  // 太古
}

enum DEL_MAIL_TYPE {
  MAIL_NO_CHANGE = 0;
  MAIL_DELETE = 1;
  MAIL_CHANGE = 2;
}

// 设置推送标识
enum PUSH_SET_TYPE {
  PST_NONE = 0;
  PST_FLOWER = 1;  // 密林花坛被抢占
  PST_MAX = 2;
}

// 0功能未开始
// 1玩法开启后每个休赛期
// 2玩法开启正常攻打，在本状态下做赛季ID和轮次的更新
// 3玩法算期开始
// 4玩法结算完成
// 5轮次结束 直接进入Break
// 0->2->3->4->5->1-> |2->3->4->5->1-> |2->3->4->5->1-> |2->3->4->5->1
enum SEASON_ARENA {
  SA_CLOSE = 0;
  SA_ROUND_BREAK = 1;
  SA_OPEN = 2;
  SA_SETTLEMENT_START = 3;
  SA_SETTLEMENT_COMPLETE = 4;
  SA_ROUND_END = 5;
}

enum SEASON_ARENA_RESET {
  SAR_NONE = 0;
  SAR_RESET_BEGIN_TIME = 72000;
  SAR_RESET_END_TIME = 73800;
}

enum GUILD_CREATE_DAY {
  GCD_LESS_EQUAL = 0;  // 小于等于指定天数
  GCD_GREATE = 1;      // 大于指定天数
}

// GSTboss - 挑战令购买
enum GST_BOSS_BUY_TYPE {
  GBBT_NONE = 0;
  GBBT_DIAMOND = 1;  // 钻石购买
  GBBT_ITEM = 2;     // 道具购买
}

enum GUILD_APPLY_TYPE {
  GAT_NONE = 0;
  GAT_USER = 1;           // 玩家申请
  GAT_GUILD_COMBINE = 2;  // 公会请求合并
}

enum GUILD_COMBINE_APPLY_TYPE {
  GCAT_NONE = 0;
  GCAT_REQUEST = 1;  // 申请合并
  GCAT_INVITE = 2;   // 邀请被合并
  GCAT_CANCEL = 3;   // 取消请求
}

enum GUILD_COMBINE_STATUS {
  GCS_NONE = 0;
  GCS_CAN_OPERATE = 1;       // 可操作
  GCS_HAS_OPERATED = 2;      // 已操作
  GCS_FUNC_LOCKED = 3;       // 对方公会合并功能未解锁
  GCS_MEMBER_NUM_LIMIT = 4;  // 不可操作：合并人数限制
  GCS_LEADER_NOT_JOIN = 5;   // 不可操作：会长不可加入（cd、活跃度）
}

enum FIRST_GIFT_REWARD_DAY {
  FGRD_NONE = 0;
  FGRD_DAY_ONE = 1;    // 第一天
  FGRD_DAY_TWO = 2;    // 第二天
  FGRD_DAY_THREE = 3;  // 第三天
}

enum GST_DRAGON_POS {
  GSTDP_NONE = 0;
  GSTDP_ONE = 1;    // 位置1
  GSTDP_TWO = 2;    // 位置2
  GSTDP_THREE = 3;  // 位置3
  GSTDP_FOUR = 4;   // 位置4
}

enum GST_DRAGON_HP_SLOT {
  GSTDHS_NONE = 0;
  GSTDHS_INIT = 1;  // 初始血条
}

enum FORMATION_TYPE {
  FTYPE_NONE = 0;
  FTYPE_PVE = 1;
  FTYPE_PVP = 2;
}

// 热度榜结算时间
enum HOT_RANK_CROSS_MERGE_TIME {
  HRCMT_NONE = 0;
  HRCMT_BEGIN = 5400;  // 重置结算开始 1.5*3600 （每天的1:30）
  HRCMT_END = 7200;    // 重置结束时间 2*3600 （每天的2:00）
}

// 英雄类型
enum HERO_TAG {
  HT_MARTERIAL = 0;  // 材料英雄
  HT_BLESSED = 1;    // 赐福英雄
  HT_CONTRACT = 2;   // 缔约英雄
}

// 共享养成属性类型
enum SHARE_GROWTH_TYPE {
  SGT_ALL = 0;        // 全部
  SGT_HERO = 1;       // 英雄
  SGT_EQUIPMENT = 2;  // 装备
}

// Boss挑战 - 体力购买
enum BOSS_RUSH_BUY_TYPE {
  BRBT_NONE = 0;
  BRBT_DIAMOND = 1;  // 钻石购买
  BRBT_ITEM = 2;     // 道具购买
}

// 公会任务竞赛 - 排行类型
enum GUILD_MOBILIZATION_RANK_TYPE {
  GMRT_NONE = 0;
  GMRT_SIMPLE = 1;  // simple rank(valid3)
  GMRT_NORMAL = 2;  // normal rank(all)
}

// 玩家切磋 - 状态
enum DUEL_STATUS_TYPE {
  DSST_NONE = 0;
  DSST_OPEN = 1;   // 默认开启状态
  DSST_CLOSE = 2;  // 关闭状态
}

enum SELECT_SUMMON_TYPE {
  SST_NONE = 0;
  SST_SINGLE = 1;  // 单抽
  SST_MANY = 2;    // 连抽
}

enum SELECT_SUMMON_COST_TYPE {
  SSCT_NONE = 0;
  SSCT_FREE = 1;
  SSCT_ITEM = 2;
}

enum SEASON_JEWELRY_OPERATE_TYPE {
  JOT_NONE = 0;
  JOT_TAKE_OFF = 1;       // 脱
  JOT_AUTO_TAKE_OFF = 2;  // 一键脱
  JOT_WEAR = 3;           // 穿/替换
  JOT_AUTO_WEAR = 4;      // 单人一键穿
  JOT_AUTO_WEAR_ALL = 5;  // 多人一键穿
}

enum SEASON_JEWELRY_SKILL_POS {
  JSP_NONE = 0;
  JSP_ONE = 1;    // 词条1
  JSP_TWO = 2;    // 词条2
  JSP_THREE = 3;  // 词条3
}

enum SEASON_DOOR_REWARD_TYPE {
  DRP_NONE = 0;
  DRP_REST = 1;  // 挂机奖励
  DRP_OIL = 2;   // 涂油奖励
}

enum SEASON_DOOR_OIL_OPERATION_TYPE {
  SDOOT_NONE = 0;
  SDOOT_LIMITED = 1;    // 限制数量
  SDOOT_UNLIMITED = 2;  // 不限数量
}

enum POKEMON_SUMMON_TYPE {
  PSST_NONE = 0;
  PST_SINGLE = 1;  // 单抽
  PST_MANY = 2;    // 连抽
}

enum POKEMON_SUMMON_COST_TYPE {
  PSCT_NONE = 0;
  PSCT_DIAMOND = 1;
  PSCT_FREE = 2;
  PSCT_ITEM = 3;
  PSCT_MAX = 4;
}

enum SYNTHESIS_GAME_ITEM_TYPE {
  SGIT_NONE = 0;
  SGIT_DELETE = 1;  // 兽笼
  SGIT_UP = 2;      // 兽粮
}

// 公平竞技场
enum BALANCE_ARENA_RESULT {
  BAR_NONE = 0;
  BAR_3VS0 = 1;  // 3比0
  BAR_2VS1 = 2;  // 2比1
  BAR_1VS2 = 3;  // 1比2
  BAR_0VS3 = 4;  // 0比3
}
