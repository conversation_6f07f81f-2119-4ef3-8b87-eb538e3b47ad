<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:货物编号 rare=int:货物品质 first_price=int:货物初始价格 min_price=int:货物最小价格 buy_max_price=int:买入最高价格 max_price=int:卖出最高价格 sell_ap=int:货物出售一个消耗的体力 if_reset=int:是否每日重置价格 -->

<root>
    <data id="1" rare="30" first_price="50" min_price="15" buy_max_price="400" max_price="500" sell_ap="0" if_reset="1" />
    <data id="2" rare="30" first_price="50" min_price="15" buy_max_price="400" max_price="500" sell_ap="0" if_reset="1" />
    <data id="3" rare="30" first_price="50" min_price="15" buy_max_price="400" max_price="500" sell_ap="0" if_reset="1" />
    <data id="4" rare="30" first_price="50" min_price="15" buy_max_price="400" max_price="500" sell_ap="0" if_reset="1" />
    <data id="5" rare="40" first_price="100" min_price="30" buy_max_price="900" max_price="1000" sell_ap="0" if_reset="1" />
    <data id="6" rare="40" first_price="100" min_price="30" buy_max_price="900" max_price="1000" sell_ap="0" if_reset="1" />
    <data id="7" rare="40" first_price="100" min_price="30" buy_max_price="900" max_price="1000" sell_ap="0" if_reset="1" />
    <data id="8" rare="40" first_price="100" min_price="30" buy_max_price="900" max_price="1000" sell_ap="0" if_reset="1" />
    <data id="9" rare="40" first_price="100" min_price="30" buy_max_price="900" max_price="1000" sell_ap="0" if_reset="1" />
    <data id="10" rare="40" first_price="100" min_price="30" buy_max_price="900" max_price="1000" sell_ap="0" if_reset="1" />
    <data id="11" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="12" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="13" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="14" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="15" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="16" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="17" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="18" rare="50" first_price="150" min_price="45" buy_max_price="1300" max_price="1500" sell_ap="0" if_reset="1" />
    <data id="19" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="20" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="21" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="22" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="23" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="24" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="25" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="26" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="27" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="28" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="29" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
    <data id="30" rare="60" first_price="200" min_price="60" buy_max_price="1800" max_price="2000" sell_ap="0" if_reset="1" />
</root>
