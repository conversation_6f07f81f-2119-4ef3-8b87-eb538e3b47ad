pipeline{
    agent { label 'ngame_server' }
    stages{
        stage('checkout') {
            steps{
                checkout([$class: 'SubversionSCM', additionalCredentials: [], excludedCommitMessages: '', excludedRegions: '', excludedRevprop: '', excludedUsers: '', 
                        filterChangelog: false, ignoreDirPropChanges: false, includedRegions: '', 
                        locations: [[cancelProcessOnExternalsFail: true, 
                            credentialsId: '7fe1d61f-89cb-4fdb-a846-3c72a8d65717', 
                            depthOption: 'infinity', 
                            ignoreExternalsOption: true, 
                            local: 'svn_data', 
                            remote: 'http://************:6688/svn/nag/Develop/trunk/RawContent/dist/server']], 
                        quietOperation: true, 
                        workspaceUpdater: [$class: 'UpdateUpdater']])
                dir('data'){
                    git credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/data.git'
                }
                
            }
        }
        stage('copy'){
            steps{
                sh 'cp -r svn_data/*.xml data/'
                sh 'cp -r svn_data/language/zh/*.xml data/language/zh/'
            }
        }
        stage('commit'){
            steps{
                sh 'cd data && git add *.xml language/zh/*.xml'
                sh 'cd data && git commit -a -m "jenkins commit"'
            }
        }
        stage('push'){
            steps{
                withCredentials([usernamePassword(credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USERNAME')]) {
                    sh('cd data && git push --set-upstream http://${GIT_USERNAME}:${GIT_PASSWORD}@gitlab.qdream.com/ngame-backend/data.git master')
                }
            }
        }

        stage('reboot') {
            steps{
                withEnv(['JENKINS_NODE_COOKIE=dontkillme']) {
					sh('cp data/*.xml /data/qa/data/ -r')
                    dir('/data/qa') {
                        sh('sed -i "s|<gm>0</gm>|<gm>1</gm>|" data/server.xml')
                        sh("""cd bin && sed -i '6a USER_NAME="jenkins"' cmd.sh""")
                        sh('cd bin && ./cmd.sh restart service -name=logic -http=:10010 -grpc=:20010 -log_level=DEBUG')
                    }
                }
            }
        }
    }
}
