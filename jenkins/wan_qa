pipeline{
    agent { label 'ngame_server' }
	parameters { 
        choice(name: 'BRANCH', choices: ['master', '0.5.0', '0.5.5', '0.6.0', '0.6.3', '0.7.4'], description: '请选择分支')
    }
    stages{
        stage('checkout') {
            steps{
				echo "当前分支 : ${env.BRANCH_NAME}"
				echo "当前分支1 : ${params.BRANCH}"
                echo "当前环境 : ${env.env}"
                echo "当前提交 : ${env.commit}"
                echo "GIT_BRANCH : ${env.GIT_BRANCH}"
                git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/server.git'
                dir('data') {
                    git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/data.git'
                }
                dir('protos/out') {
                    git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame/proto.git'
                }
		dir('playbooks') {
                    git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/wb/wangb.git'
                }
            }
        }
        
        stage('build') {
            steps{
                withEnv(['GOPROXY=https://goproxy.cn', 'PATH+WHATEVER=/home/<USER>/go/bin']) {
                    sh('make')
                }
            }
        }

        stage('deploy') {
            steps{
                sh('tar -cjf logic.tar.bz2 bin/service bin/cmd.sh data/*.xml config/*.xml config/*.yaml')
		sh('cd playbooks/playbook && ansible-playbook -i hosts qa1.yml')
            }
        }
    }
}
