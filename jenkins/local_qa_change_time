pipeline{
    agent { label 'ngame_server' }
    parameters {
        string(name: 'Timestamp', defaultValue: '', description: '不填时间，查看服务器当前时间！(格式： 2021-01-02 13:04:10)')
    }
    stages{
		stage('checkout') {
            steps{
                git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/server.git'
                dir('playbooks') {
                    git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/wb/wangb.git'
                }
            }
        }
        stage('change_time') {
            steps{
                script {
                    if (params.Timestamp != "") {
						sh("""echo '修改前服务器时间: ' && cd playbooks/playbook && ansible -i hosts localQa -m shell -a 'date' """)
						sh("""echo '修改服务器时间...' && cd playbooks/playbook && ansible -i hosts localQa -m shell -a 'date -s "${params.Timestamp}"' """)
						sh("""echo '重启服务器...' && cd playbooks/playbook && ansible -i hosts localQa -m shell -a 'cd game/bin && pwd && setsid ./cmd.sh restart service -name=logic -http=:10011 -grpc=:20011 -log_level=DEBUG' """)
                    } else {
						sh("""echo '服务器当前时间: ' && cd playbooks/playbook && ansible -i hosts localQa -m shell -a 'date' """)
                    }
                }
            }
        }
    }
}
