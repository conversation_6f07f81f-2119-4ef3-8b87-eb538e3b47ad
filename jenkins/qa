pipeline{
    agent { label 'ngame_server' }
    stages{
        stage('checkout') {
            steps{
                git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/server.git'
                dir('data') {
                    git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/data.git'
                }
                dir('protos/out') {
                    git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame/proto.git'
                }
            }
        }
        
        stage('build') {
            steps{
                withEnv(['GOPROXY=https://goproxy.cn', 'PATH+WHATEVER=/home/<USER>/go/bin']) {
                    sh('make init')
                }
            }
        }

        stage('deploy') {
            steps{
                sh('tar -cjf logic.tar.bz2 bin/service bin/cmd.sh data/*.xml config/*.xml config/*.yaml')
                sh('cp logic.tar.bz2 /data/qa/')
                sh('cd /data/qa && tar -xf logic.tar.bz2')
            }
        }
        
        stage('reboot') {
            steps{
                withEnv(['JENKINS_NODE_COOKIE=dontkillme']) {
                    dir('/data/qa') {
                        sh('sed -i "s|<gm>0</gm>|<gm>1</gm>|" data/server.xml')
                        sh('sed -i "s|<gateways></gateways>|<gateways></gateways>|" data/server.xml')
                        sh('sed -i "s|<world>/game/x/world/node/wb/</world>|<world>/game/x/world/node/jenkins/</world>|" config/world.xml')
                        sh("""cd bin && sed -i '6a USER_NAME="jenkins"' cmd.sh""")
                        sh('cd bin && ./cmd.sh restart service -name=logic -http=:10010 -grpc=:20010 -log_level=DEBUG')
                    }
                }
            }
        }
    }
}
