pipeline{
    agent { label 'ngame_server' }
    stages{
        stage('checkout') {
            steps{
                git branch: 'master', credentialsId: '9cefcbd2-97b2-4bef-8c0f-5939282cb14e', url: 'http://gitlab.qdream.com/ngame-backend/server.git'
                dir('data') {
                    git branch: 'master', credentialsId: '9cefcbd2-97b2-4bef-8c0f-5939282cb14e', url: 'http://gitlab.qdream.com/ngame-backend/data.git'
                }
                dir('protos/out') {
                    git branch: 'master', credentialsId: '9cefcbd2-97b2-4bef-8c0f-5939282cb14e', url: 'http://gitlab.qdream.com/ngame/proto.git'
                }
            }
        }
        
        stage('build') {
            steps{
                withEnv(['GOPROXY=https://goproxy.cn', 'PATH+WHATEVER=/home/<USER>/go/bin']) {
                    sh('make init')
                }
            }
            post {
                failure {
					wrap([$class: 'BuildUser']){
                    	script{
							sh("curl -X POST -H \"Content-Type: application/json\" -d \'{\"msg_type\":\"interactive\",\"card\":{\"config\":{\"wide_screen_mode\":true,\"enable_forward\":true},\"elements\":[{\"tag\":\"div\",\"text\":{\"content\":\"触发原因: ${BUILD_USER}\",\"tag\":\"lark_md\"}},{\"actions\":[{\"tag\":\"button\",\"text\":{\"content\":\"具体日志 :玫瑰:\",\"tag\":\"lark_md\"},\"url\":\"${BUILD_URL}console\",\"type\":\"default\",\"value\":{}}],\"tag\":\"action\"}],\"header\":{\"title\":{\"content\":\"代码检测出错\",\"tag\":\"plain_text\"}}}}\' https://open.feishu.cn/open-apis/bot/v2/hook/fdfd9c58-052b-4282-927c-3622f5c453cc ")
                    	}
					}
                }
            }
        }
    }
	
}
