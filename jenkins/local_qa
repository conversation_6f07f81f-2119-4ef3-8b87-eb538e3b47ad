pipeline{
    agent { label 'ngame_server' }
	parameters { 
        choice(name: '<PERSON><PERSON><PERSON>', choices: ['master', '0.5.0', '0.5.5', '0.6.0', '0.6.3', '0.7.4'], description: '请选择分支')
    }
    stages{
        stage('checkout-svn-data') {
			when {
                expression { params.BRANCH != 'master' }
            }
            steps{
                checkout([$class: 'SubversionSCM', additionalCredentials: [], excludedCommitMessages: '', excludedRegions: '', excludedRevprop: '', excludedUsers: '', 
                        filterChangelog: false, ignoreDirPropChanges: false, includedRegions: '', 
                        locations: [[cancelProcessOnExternalsFail: true, 
                            credentialsId: '7fe1d61f-89cb-4fdb-a846-3c72a8d65717', 
                            depthOption: 'infinity', 
                            ignoreExternalsOption: true, 
                            local: 'svn_data', 
                            remote: "http://************:6688/svn/nag/Develop/branches/Version${params.<PERSON>ANCH}-Alpha/RawContent/dist/server"]], 
                        quietOperation: true, 
                        workspaceUpdater: [$class: 'UpdateUpdater']])
                dir('git_data'){
                    git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/data.git'
                }
			}
        }
        stage('copy-svn-data'){
			when {
                expression { params.BRANCH != 'master' }
            }
            steps{
                sh 'cp svn_data/*.xml git_data/'
            }
        }
        stage('commit-git-data'){
			when {
                expression { params.BRANCH != 'master' }
            }
            steps{
                sh 'cd git_data && git add *.xml'
                sh 'cd git_data && git commit -a --allow-empty -m "branch jenkins commit"'
            }
        }
        stage('push-git-data'){
			when {
                expression { params.BRANCH != 'master' }
            }
            steps{
                withCredentials([usernamePassword(credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USERNAME')]) {
                  sh("cd git_data && git push --set-upstream http://${GIT_USERNAME}:${GIT_PASSWORD}@gitlab.qdream.com/ngame-backend/data.git ${params.BRANCH}")
                }
            }
        }
        stage('checkout') {
            steps{
                git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/server.git'
                dir('data') {
                    git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/data.git'
                }
                dir('protos/out') {
                    git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame/proto.git'
                }
		dir('playbooks') {
                    git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/wb/wangb.git'
                }
            }
        }
        
        stage('build') {
            steps{
                withEnv(['GOPROXY=https://goproxy.cn', 'PATH+WHATEVER=/home/<USER>/go/bin']) {
                    sh('make')
                }
            }
        }

        stage('deploy') {
            steps{
                sh('tar -cjf logic.tar.bz2 bin/service bin/cmd.sh data/*.xml config/*.xml config/*.yaml')
				sh("cd playbooks/playbook && ansible-playbook -i hosts localQa.yml")
            }
        }
    }
}
