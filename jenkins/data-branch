pipeline{
    agent { label 'ngame_server' }
	parameters { 
        choice(name: 'BRANCH', choices: ['0.5.0', '0.5.5', '0.6.0', '0.6.3', '0.7.4'], description: '请选择分支')
    }
    stages{
        stage('checkout') {
            steps{
                checkout([$class: 'SubversionSCM', additionalCredentials: [], excludedCommitMessages: '', excludedRegions: '', excludedRevprop: '', excludedUsers: '', 
                        filterChangelog: false, ignoreDirPropChanges: false, includedRegions: '', 
                        locations: [[cancelProcessOnExternalsFail: true, 
                            credentialsId: '7fe1d61f-89cb-4fdb-a846-3c72a8d65717', 
                            depthOption: 'infinity', 
                            ignoreExternalsOption: true, 
                            local: 'svn_data', 
                            remote: "http://************:6688/svn/nag/Develop/branches/Version${params.BRANCH}-Alpha/RawContent/dist/server"]], 
                        quietOperation: true, 
                        workspaceUpdater: [$class: 'UpdateUpdater']])
                dir('data'){
                    git branch: "${params.BRANCH}", credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/data.git'
                }
                
            }
        }
        stage('copy'){
            steps{
                sh 'cp svn_data/*.xml data/'
            }
        }
        stage('commit'){
            steps{
                sh 'cd data && git add *.xml'
                sh 'cd data && git commit -a -m "jenkins commit"'
            }
        }
        stage('push'){
            steps{
                withCredentials([usernamePassword(credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', passwordVariable: 'GIT_PASSWORD', usernameVariable: 'GIT_USERNAME')]) {
                  sh("cd data && git push --set-upstream http://${GIT_USERNAME}:${GIT_PASSWORD}@gitlab.qdream.com/ngame-backend/data.git ${params.BRANCH}")
                }
            }
        }
    }
}
