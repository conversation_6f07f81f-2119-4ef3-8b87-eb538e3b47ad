pipeline{
    agent { label 'ngame_server' }
    stages{
        stage('checkout') {
            steps{
                git branch: 'master', credentialsId: 'e1999693-8d48-4525-90a9-6d605f0294cb', url: 'http://gitlab.qdream.com/ngame-backend/server.git'
                checkout([$class: 'SubversionSCM', additionalCredentials: [], excludedCommitMessages: '', excludedRegions: '', excludedRevprop: '', excludedUsers: '', 
                        filterChangelog: false, ignoreDirPropChanges: false, includedRegions: '', 
                        locations: [[cancelProcessOnExternalsFail: true, 
                            credentialsId: '7fe1d61f-89cb-4fdb-a846-3c72a8d65717', 
                            depthOption: 'infinity', 
                            ignoreExternalsOption: true, 
                            local: 'svn_data', 
                            remote: 'http://************:6688/svn/nag/Develop/trunk/tools']], 
                        quietOperation: true, 
                        workspaceUpdater: [$class: 'UpdateUpdater']])
             }
                
        }
        stage('build') {
            steps{
                withEnv(['GOPROXY=https://goproxy.cn', 'PATH+WHATEVER=/home/<USER>/go/bin']) {
					sh('make xml_git')
					sh('make protos_git')
					sh('make data-check')
                }
            }
        }

        stage('copy'){
            steps{
                sh 'cp -rf bin/data-check.exe ./svn_data/'
            }
        }

        stage('commit'){
            steps{
				withCredentials([usernamePassword(credentialsId: '7fe1d61f-89cb-4fdb-a846-3c72a8d65717', passwordVariable: 'SVN_PASSWORD', usernameVariable: 'SVN_USERNAME')]) {
					sh( 'cd ./svn_data && svn commit -m "jenkins提交" --username ${SVN_USERNAME} --password ${SVN_PASSWORD} --no-auth-cache')
				}
            }
        }
    }
}
