<?xml version="1.0" encoding="UTF-8"?>
<!--key=string:配置值 type=int:类型 value=int:值 count=int:数量 -->

<root>
    <data key="JUMP_NEED_MULTI" type="0" value="0" count="20000" />
    <data key="JUMP_NEED_COUNT" type="0" value="0" count="3" />
    <data key="JUMP_COUNT_REDUCE" type="0" value="0" count="1" />
    <data key="START_JUMP_FLOOR" type="0" value="0" count="11" />
    <data key="TOWER_SKIP_FLOOR_COMMON" type="0" value="0" count="1" />
    <data key="TOWER_SKIP_FLOOR_ELITE" type="0" value="0" count="1" />
    <data key="TOWER_SKIP_FLOOR" type="0" value="0" count="101" />
    <data key="TOWER_SHOW_NEXT_FLOOR" type="0" value="0" count="30" />
    <data key="JUMP_SWEEP_MULTI" type="0" value="0" count="10001" />
    <data key="TOWER_REWARD_OPEN" type="0" value="0" count="0" />
    <data key="TOWER_RANKING_LIKE" type="0" value="0" count="3" />
    <data key="TOWER_RANKING_LIKE_HISTORY" type="0" value="0" count="3" />
    <data key="TOWER_RANKING_LIKE_REWARDS" type="2" value="0" count="50" />
    <data key="TOWER_RANKING_LIKE_HISTORY_REWARDS" type="2" value="0" count="100" />
</root>
