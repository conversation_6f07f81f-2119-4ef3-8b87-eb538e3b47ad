<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:id condition1=int:激活条件1 condition2=int:激活条件2 condition3=int:激活条件3 raise_passive_skill_1=int:战斗效果1 raise_passive_skill_2=int:战斗效果2 raise_passive_skill_3=int:战斗效果3 raise_passive_skill_4=int:战斗效果4 raise_passive_skill_5=int:战斗效果5 raise_passive_skill_6=int:战斗效果6 -->

<root>
    <data id="1001" condition1="201" condition2="0" condition3="0" raise_passive_skill_1="3010101" raise_passive_skill_2="30101001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1002" condition1="202" condition2="0" condition3="0" raise_passive_skill_1="30101002" raise_passive_skill_2="3010101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1003" condition1="203" condition2="0" condition3="0" raise_passive_skill_1="30101003" raise_passive_skill_2="3010101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1004" condition1="204" condition2="0" condition3="0" raise_passive_skill_1="3010102" raise_passive_skill_2="30101004" raise_passive_skill_3="30101003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1005" condition1="205" condition2="0" condition3="0" raise_passive_skill_1="30101005" raise_passive_skill_2="3010102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1006" condition1="206" condition2="0" condition3="0" raise_passive_skill_1="30101006" raise_passive_skill_2="3010102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1007" condition1="207" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1101" condition1="211" condition2="0" condition3="0" raise_passive_skill_1="3010201" raise_passive_skill_2="30102001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1102" condition1="212" condition2="0" condition3="0" raise_passive_skill_1="30102002" raise_passive_skill_2="3010201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1103" condition1="213" condition2="0" condition3="0" raise_passive_skill_1="30102003" raise_passive_skill_2="3010201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1104" condition1="214" condition2="0" condition3="0" raise_passive_skill_1="3010202" raise_passive_skill_2="30102004" raise_passive_skill_3="30102003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1105" condition1="215" condition2="0" condition3="0" raise_passive_skill_1="30102005" raise_passive_skill_2="3010202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1106" condition1="216" condition2="0" condition3="0" raise_passive_skill_1="30102006" raise_passive_skill_2="3010202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1107" condition1="217" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1201" condition1="221" condition2="0" condition3="0" raise_passive_skill_1="3010301" raise_passive_skill_2="30103001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1202" condition1="222" condition2="0" condition3="0" raise_passive_skill_1="30103002" raise_passive_skill_2="3010301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1203" condition1="223" condition2="0" condition3="0" raise_passive_skill_1="30103003" raise_passive_skill_2="3010301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1204" condition1="224" condition2="0" condition3="0" raise_passive_skill_1="3010302" raise_passive_skill_2="30103004" raise_passive_skill_3="30103003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1205" condition1="225" condition2="0" condition3="0" raise_passive_skill_1="30103005" raise_passive_skill_2="3010302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1206" condition1="226" condition2="0" condition3="0" raise_passive_skill_1="30103006" raise_passive_skill_2="3010302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="1207" condition1="227" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2001" condition1="301" condition2="0" condition3="0" raise_passive_skill_1="3020101" raise_passive_skill_2="30101001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2002" condition1="302" condition2="0" condition3="0" raise_passive_skill_1="30201002" raise_passive_skill_2="3020101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2003" condition1="303" condition2="0" condition3="0" raise_passive_skill_1="30201003" raise_passive_skill_2="3020101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2004" condition1="304" condition2="0" condition3="0" raise_passive_skill_1="3020102" raise_passive_skill_2="30101004" raise_passive_skill_3="30201003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2005" condition1="305" condition2="0" condition3="0" raise_passive_skill_1="30201005" raise_passive_skill_2="3020102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2006" condition1="306" condition2="0" condition3="0" raise_passive_skill_1="30201006" raise_passive_skill_2="3020102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2007" condition1="307" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2101" condition1="311" condition2="0" condition3="0" raise_passive_skill_1="3020201" raise_passive_skill_2="30102001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2102" condition1="312" condition2="0" condition3="0" raise_passive_skill_1="30202002" raise_passive_skill_2="3020201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2103" condition1="313" condition2="0" condition3="0" raise_passive_skill_1="30202003" raise_passive_skill_2="3020201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2104" condition1="314" condition2="0" condition3="0" raise_passive_skill_1="3020202" raise_passive_skill_2="30102004" raise_passive_skill_3="30202003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2105" condition1="315" condition2="0" condition3="0" raise_passive_skill_1="30202005" raise_passive_skill_2="3020202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2106" condition1="316" condition2="0" condition3="0" raise_passive_skill_1="30202006" raise_passive_skill_2="3020202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2107" condition1="317" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2201" condition1="321" condition2="0" condition3="0" raise_passive_skill_1="3020301" raise_passive_skill_2="30103001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2202" condition1="322" condition2="0" condition3="0" raise_passive_skill_1="30203002" raise_passive_skill_2="3020301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2203" condition1="323" condition2="0" condition3="0" raise_passive_skill_1="30203003" raise_passive_skill_2="3020301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2204" condition1="324" condition2="0" condition3="0" raise_passive_skill_1="3020302" raise_passive_skill_2="30103004" raise_passive_skill_3="30203003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2205" condition1="325" condition2="0" condition3="0" raise_passive_skill_1="30203005" raise_passive_skill_2="3020302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2206" condition1="326" condition2="0" condition3="0" raise_passive_skill_1="30203006" raise_passive_skill_2="3020302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="2207" condition1="327" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3001" condition1="401" condition2="0" condition3="0" raise_passive_skill_1="3030101" raise_passive_skill_2="30101001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3002" condition1="402" condition2="0" condition3="0" raise_passive_skill_1="30301002" raise_passive_skill_2="3030101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3003" condition1="403" condition2="0" condition3="0" raise_passive_skill_1="30301003" raise_passive_skill_2="3030101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3004" condition1="404" condition2="0" condition3="0" raise_passive_skill_1="3030102" raise_passive_skill_2="30101004" raise_passive_skill_3="30301003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3005" condition1="405" condition2="0" condition3="0" raise_passive_skill_1="30301005" raise_passive_skill_2="3030102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3006" condition1="406" condition2="0" condition3="0" raise_passive_skill_1="30301006" raise_passive_skill_2="3030102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3007" condition1="407" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3101" condition1="411" condition2="0" condition3="0" raise_passive_skill_1="3030201" raise_passive_skill_2="30102001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3102" condition1="412" condition2="0" condition3="0" raise_passive_skill_1="30302002" raise_passive_skill_2="3030201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3103" condition1="413" condition2="0" condition3="0" raise_passive_skill_1="30302003" raise_passive_skill_2="3030201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3104" condition1="414" condition2="0" condition3="0" raise_passive_skill_1="3030202" raise_passive_skill_2="30102004" raise_passive_skill_3="30302003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3105" condition1="415" condition2="0" condition3="0" raise_passive_skill_1="30302005" raise_passive_skill_2="3030202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3106" condition1="416" condition2="0" condition3="0" raise_passive_skill_1="30302006" raise_passive_skill_2="3030202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3107" condition1="417" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3201" condition1="421" condition2="0" condition3="0" raise_passive_skill_1="3030301" raise_passive_skill_2="30103001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3202" condition1="422" condition2="0" condition3="0" raise_passive_skill_1="30303002" raise_passive_skill_2="3030301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3203" condition1="423" condition2="0" condition3="0" raise_passive_skill_1="30303003" raise_passive_skill_2="3030301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3204" condition1="424" condition2="0" condition3="0" raise_passive_skill_1="3030302" raise_passive_skill_2="30103004" raise_passive_skill_3="30303003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3205" condition1="425" condition2="0" condition3="0" raise_passive_skill_1="30303005" raise_passive_skill_2="3030302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3206" condition1="426" condition2="0" condition3="0" raise_passive_skill_1="30303006" raise_passive_skill_2="3030302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="3207" condition1="427" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4001" condition1="501" condition2="0" condition3="0" raise_passive_skill_1="3040101" raise_passive_skill_2="30101001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4002" condition1="502" condition2="0" condition3="0" raise_passive_skill_1="30401002" raise_passive_skill_2="3040101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4003" condition1="503" condition2="0" condition3="0" raise_passive_skill_1="30401003" raise_passive_skill_2="3040101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4004" condition1="504" condition2="0" condition3="0" raise_passive_skill_1="3040102" raise_passive_skill_2="30101004" raise_passive_skill_3="30401003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4005" condition1="505" condition2="0" condition3="0" raise_passive_skill_1="30401005" raise_passive_skill_2="3040102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4006" condition1="506" condition2="0" condition3="0" raise_passive_skill_1="30401006" raise_passive_skill_2="3040102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4007" condition1="507" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4101" condition1="511" condition2="0" condition3="0" raise_passive_skill_1="3040201" raise_passive_skill_2="30102001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4102" condition1="512" condition2="0" condition3="0" raise_passive_skill_1="30402002" raise_passive_skill_2="3040201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4103" condition1="513" condition2="0" condition3="0" raise_passive_skill_1="30402003" raise_passive_skill_2="3040201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4104" condition1="514" condition2="0" condition3="0" raise_passive_skill_1="3040202" raise_passive_skill_2="30102004" raise_passive_skill_3="30402003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4105" condition1="515" condition2="0" condition3="0" raise_passive_skill_1="30402005" raise_passive_skill_2="3040202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4106" condition1="516" condition2="0" condition3="0" raise_passive_skill_1="30402006" raise_passive_skill_2="3040202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4107" condition1="517" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4201" condition1="521" condition2="0" condition3="0" raise_passive_skill_1="3040301" raise_passive_skill_2="30103001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4202" condition1="522" condition2="0" condition3="0" raise_passive_skill_1="30403002" raise_passive_skill_2="3040301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4203" condition1="523" condition2="0" condition3="0" raise_passive_skill_1="30403003" raise_passive_skill_2="3040301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4204" condition1="524" condition2="0" condition3="0" raise_passive_skill_1="3040302" raise_passive_skill_2="30103004" raise_passive_skill_3="30403003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4205" condition1="525" condition2="0" condition3="0" raise_passive_skill_1="30403005" raise_passive_skill_2="3040302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4206" condition1="526" condition2="0" condition3="0" raise_passive_skill_1="30403006" raise_passive_skill_2="3040302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="4207" condition1="527" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5001" condition1="601" condition2="0" condition3="0" raise_passive_skill_1="3050101" raise_passive_skill_2="30101001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5002" condition1="602" condition2="0" condition3="0" raise_passive_skill_1="30501002" raise_passive_skill_2="3050101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5003" condition1="603" condition2="0" condition3="0" raise_passive_skill_1="30501003" raise_passive_skill_2="3050101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5004" condition1="604" condition2="0" condition3="0" raise_passive_skill_1="3050102" raise_passive_skill_2="30101004" raise_passive_skill_3="30501003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5005" condition1="605" condition2="0" condition3="0" raise_passive_skill_1="30501005" raise_passive_skill_2="3050102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5006" condition1="606" condition2="0" condition3="0" raise_passive_skill_1="30501006" raise_passive_skill_2="3050102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5007" condition1="607" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5101" condition1="611" condition2="0" condition3="0" raise_passive_skill_1="3050201" raise_passive_skill_2="30102001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5102" condition1="612" condition2="0" condition3="0" raise_passive_skill_1="30502002" raise_passive_skill_2="3050201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5103" condition1="613" condition2="0" condition3="0" raise_passive_skill_1="30502003" raise_passive_skill_2="3050201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5104" condition1="614" condition2="0" condition3="0" raise_passive_skill_1="3050202" raise_passive_skill_2="30102004" raise_passive_skill_3="30502003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5105" condition1="615" condition2="0" condition3="0" raise_passive_skill_1="30502005" raise_passive_skill_2="3050202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5106" condition1="616" condition2="0" condition3="0" raise_passive_skill_1="30502006" raise_passive_skill_2="3050202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5107" condition1="617" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5201" condition1="621" condition2="0" condition3="0" raise_passive_skill_1="3050301" raise_passive_skill_2="30103001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5202" condition1="622" condition2="0" condition3="0" raise_passive_skill_1="30503002" raise_passive_skill_2="3050301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5203" condition1="623" condition2="0" condition3="0" raise_passive_skill_1="30503003" raise_passive_skill_2="3050301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5204" condition1="624" condition2="0" condition3="0" raise_passive_skill_1="3050302" raise_passive_skill_2="30103004" raise_passive_skill_3="30503003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5205" condition1="625" condition2="0" condition3="0" raise_passive_skill_1="30503005" raise_passive_skill_2="3050302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5206" condition1="626" condition2="0" condition3="0" raise_passive_skill_1="30503006" raise_passive_skill_2="3050302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="5207" condition1="627" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6001" condition1="701" condition2="0" condition3="0" raise_passive_skill_1="3060101" raise_passive_skill_2="30101001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6002" condition1="702" condition2="0" condition3="0" raise_passive_skill_1="30601002" raise_passive_skill_2="3060101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6003" condition1="703" condition2="0" condition3="0" raise_passive_skill_1="30601003" raise_passive_skill_2="3060101" raise_passive_skill_3="30101001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6004" condition1="704" condition2="0" condition3="0" raise_passive_skill_1="3060102" raise_passive_skill_2="30101004" raise_passive_skill_3="30601003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6005" condition1="705" condition2="0" condition3="0" raise_passive_skill_1="30601005" raise_passive_skill_2="3060102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6006" condition1="706" condition2="0" condition3="0" raise_passive_skill_1="30601006" raise_passive_skill_2="3060102" raise_passive_skill_3="30101004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6007" condition1="707" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6101" condition1="711" condition2="0" condition3="0" raise_passive_skill_1="3060201" raise_passive_skill_2="30102001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6102" condition1="712" condition2="0" condition3="0" raise_passive_skill_1="30602002" raise_passive_skill_2="3060201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6103" condition1="713" condition2="0" condition3="0" raise_passive_skill_1="30602003" raise_passive_skill_2="3060201" raise_passive_skill_3="30102001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6104" condition1="714" condition2="0" condition3="0" raise_passive_skill_1="3060202" raise_passive_skill_2="30102004" raise_passive_skill_3="30602003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6105" condition1="715" condition2="0" condition3="0" raise_passive_skill_1="30602005" raise_passive_skill_2="3060202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6106" condition1="716" condition2="0" condition3="0" raise_passive_skill_1="30602006" raise_passive_skill_2="3060202" raise_passive_skill_3="30102004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6107" condition1="717" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6201" condition1="721" condition2="0" condition3="0" raise_passive_skill_1="3060301" raise_passive_skill_2="30103001" raise_passive_skill_3="0" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6202" condition1="722" condition2="0" condition3="0" raise_passive_skill_1="30603002" raise_passive_skill_2="3060301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6203" condition1="723" condition2="0" condition3="0" raise_passive_skill_1="30603003" raise_passive_skill_2="3060301" raise_passive_skill_3="30103001" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6204" condition1="724" condition2="0" condition3="0" raise_passive_skill_1="3060302" raise_passive_skill_2="30103004" raise_passive_skill_3="30603003" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6205" condition1="725" condition2="0" condition3="0" raise_passive_skill_1="30603005" raise_passive_skill_2="3060302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6206" condition1="726" condition2="0" condition3="0" raise_passive_skill_1="30603006" raise_passive_skill_2="3060302" raise_passive_skill_3="30103004" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="6207" condition1="727" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="0" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="12008" condition1="2008" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="12009" condition1="2009" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="12010" condition1="2010" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101009" raise_passive_skill_5="30101010" raise_passive_skill_6="0" />
    <data id="12011" condition1="2011" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101011" raise_passive_skill_5="30101010" raise_passive_skill_6="0" />
    <data id="12012" condition1="2012" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101012" raise_passive_skill_5="30101010" raise_passive_skill_6="0" />
    <data id="12013" condition1="2013" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101012" raise_passive_skill_5="30101013" raise_passive_skill_6="0" />
    <data id="12014" condition1="2014" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101014" raise_passive_skill_5="30101013" raise_passive_skill_6="0" />
    <data id="12015" condition1="2015" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101015" raise_passive_skill_5="30101013" raise_passive_skill_6="0" />
    <data id="12016" condition1="2016" condition2="0" condition3="0" raise_passive_skill_1="3010103" raise_passive_skill_2="30101007" raise_passive_skill_3="30101006" raise_passive_skill_4="30101015" raise_passive_skill_5="30101016" raise_passive_skill_6="0" />
    <data id="12108" condition1="2108" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="12109" condition1="2109" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="12110" condition1="2110" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102009" raise_passive_skill_5="30102010" raise_passive_skill_6="0" />
    <data id="12111" condition1="2111" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102011" raise_passive_skill_5="30102010" raise_passive_skill_6="0" />
    <data id="12112" condition1="2112" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102012" raise_passive_skill_5="30102010" raise_passive_skill_6="0" />
    <data id="12113" condition1="2113" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102012" raise_passive_skill_5="30102013" raise_passive_skill_6="0" />
    <data id="12114" condition1="2114" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102014" raise_passive_skill_5="30102013" raise_passive_skill_6="0" />
    <data id="12115" condition1="2115" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102015" raise_passive_skill_5="30102013" raise_passive_skill_6="0" />
    <data id="12116" condition1="2116" condition2="0" condition3="0" raise_passive_skill_1="3010203" raise_passive_skill_2="30102007" raise_passive_skill_3="30102006" raise_passive_skill_4="30102015" raise_passive_skill_5="30102016" raise_passive_skill_6="0" />
    <data id="12208" condition1="2208" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="12209" condition1="2209" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="12210" condition1="2210" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103009" raise_passive_skill_5="30103010" raise_passive_skill_6="0" />
    <data id="12211" condition1="2211" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103011" raise_passive_skill_5="30103010" raise_passive_skill_6="0" />
    <data id="12212" condition1="2212" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103012" raise_passive_skill_5="30103010" raise_passive_skill_6="0" />
    <data id="12213" condition1="2213" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103012" raise_passive_skill_5="30103013" raise_passive_skill_6="0" />
    <data id="12214" condition1="2214" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103014" raise_passive_skill_5="30103013" raise_passive_skill_6="0" />
    <data id="12215" condition1="2215" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103015" raise_passive_skill_5="30103013" raise_passive_skill_6="0" />
    <data id="12216" condition1="2216" condition2="0" condition3="0" raise_passive_skill_1="3010303" raise_passive_skill_2="30103007" raise_passive_skill_3="30103006" raise_passive_skill_4="30103015" raise_passive_skill_5="30103016" raise_passive_skill_6="0" />
    <data id="13008" condition1="3008" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="13009" condition1="3009" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="13010" condition1="3010" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201009" raise_passive_skill_5="30201010" raise_passive_skill_6="0" />
    <data id="13011" condition1="3011" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201011" raise_passive_skill_5="30201010" raise_passive_skill_6="0" />
    <data id="13012" condition1="3012" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201012" raise_passive_skill_5="30201010" raise_passive_skill_6="0" />
    <data id="13013" condition1="3013" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201012" raise_passive_skill_5="30201013" raise_passive_skill_6="0" />
    <data id="13014" condition1="3014" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201014" raise_passive_skill_5="30201013" raise_passive_skill_6="0" />
    <data id="13015" condition1="3015" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201015" raise_passive_skill_5="30201013" raise_passive_skill_6="0" />
    <data id="13016" condition1="3016" condition2="0" condition3="0" raise_passive_skill_1="3020103" raise_passive_skill_2="30101007" raise_passive_skill_3="30201006" raise_passive_skill_4="30201015" raise_passive_skill_5="30201016" raise_passive_skill_6="0" />
    <data id="13108" condition1="3108" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="13109" condition1="3109" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="13110" condition1="3110" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202009" raise_passive_skill_5="30202010" raise_passive_skill_6="0" />
    <data id="13111" condition1="3111" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202011" raise_passive_skill_5="30202010" raise_passive_skill_6="0" />
    <data id="13112" condition1="3112" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202012" raise_passive_skill_5="30202010" raise_passive_skill_6="0" />
    <data id="13113" condition1="3113" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202012" raise_passive_skill_5="30202013" raise_passive_skill_6="0" />
    <data id="13114" condition1="3114" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202014" raise_passive_skill_5="30202013" raise_passive_skill_6="0" />
    <data id="13115" condition1="3115" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202015" raise_passive_skill_5="30202013" raise_passive_skill_6="0" />
    <data id="13116" condition1="3116" condition2="0" condition3="0" raise_passive_skill_1="3020203" raise_passive_skill_2="30102007" raise_passive_skill_3="30202006" raise_passive_skill_4="30202015" raise_passive_skill_5="30202016" raise_passive_skill_6="0" />
    <data id="13208" condition1="3208" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="13209" condition1="3209" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="13210" condition1="3210" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203009" raise_passive_skill_5="30203010" raise_passive_skill_6="0" />
    <data id="13211" condition1="3211" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203011" raise_passive_skill_5="30203010" raise_passive_skill_6="0" />
    <data id="13212" condition1="3212" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203012" raise_passive_skill_5="30203010" raise_passive_skill_6="0" />
    <data id="13213" condition1="3213" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203012" raise_passive_skill_5="30203013" raise_passive_skill_6="0" />
    <data id="13214" condition1="3214" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203014" raise_passive_skill_5="30203013" raise_passive_skill_6="0" />
    <data id="13215" condition1="3215" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203015" raise_passive_skill_5="30203013" raise_passive_skill_6="0" />
    <data id="13216" condition1="3216" condition2="0" condition3="0" raise_passive_skill_1="3020303" raise_passive_skill_2="30103007" raise_passive_skill_3="30203006" raise_passive_skill_4="30203015" raise_passive_skill_5="30203016" raise_passive_skill_6="0" />
    <data id="14008" condition1="4008" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="14009" condition1="4009" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="14010" condition1="4010" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301009" raise_passive_skill_5="30301010" raise_passive_skill_6="0" />
    <data id="14011" condition1="4011" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301011" raise_passive_skill_5="30301010" raise_passive_skill_6="0" />
    <data id="14012" condition1="4012" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301012" raise_passive_skill_5="30301010" raise_passive_skill_6="0" />
    <data id="14013" condition1="4013" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301012" raise_passive_skill_5="30301013" raise_passive_skill_6="0" />
    <data id="14014" condition1="4014" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301014" raise_passive_skill_5="30301013" raise_passive_skill_6="0" />
    <data id="14015" condition1="4015" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301015" raise_passive_skill_5="30301013" raise_passive_skill_6="0" />
    <data id="14016" condition1="4016" condition2="0" condition3="0" raise_passive_skill_1="3030103" raise_passive_skill_2="30101007" raise_passive_skill_3="30301006" raise_passive_skill_4="30301015" raise_passive_skill_5="30301016" raise_passive_skill_6="0" />
    <data id="14108" condition1="4108" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="14109" condition1="4109" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="14110" condition1="4110" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302009" raise_passive_skill_5="30302010" raise_passive_skill_6="0" />
    <data id="14111" condition1="4111" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302011" raise_passive_skill_5="30302010" raise_passive_skill_6="0" />
    <data id="14112" condition1="4112" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302012" raise_passive_skill_5="30302010" raise_passive_skill_6="0" />
    <data id="14113" condition1="4113" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302012" raise_passive_skill_5="30302013" raise_passive_skill_6="0" />
    <data id="14114" condition1="4114" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302014" raise_passive_skill_5="30302013" raise_passive_skill_6="0" />
    <data id="14115" condition1="4115" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302015" raise_passive_skill_5="30302013" raise_passive_skill_6="0" />
    <data id="14116" condition1="4116" condition2="0" condition3="0" raise_passive_skill_1="3030203" raise_passive_skill_2="30102007" raise_passive_skill_3="30302006" raise_passive_skill_4="30302015" raise_passive_skill_5="30302016" raise_passive_skill_6="0" />
    <data id="14208" condition1="4208" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="14209" condition1="4209" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="14210" condition1="4210" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303009" raise_passive_skill_5="30303010" raise_passive_skill_6="0" />
    <data id="14211" condition1="4211" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303011" raise_passive_skill_5="30303010" raise_passive_skill_6="0" />
    <data id="14212" condition1="4212" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303012" raise_passive_skill_5="30303010" raise_passive_skill_6="0" />
    <data id="14213" condition1="4213" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303012" raise_passive_skill_5="30303013" raise_passive_skill_6="0" />
    <data id="14214" condition1="4214" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303014" raise_passive_skill_5="30303013" raise_passive_skill_6="0" />
    <data id="14215" condition1="4215" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303015" raise_passive_skill_5="30303013" raise_passive_skill_6="0" />
    <data id="14216" condition1="4216" condition2="0" condition3="0" raise_passive_skill_1="3030303" raise_passive_skill_2="30103007" raise_passive_skill_3="30303006" raise_passive_skill_4="30303015" raise_passive_skill_5="30303016" raise_passive_skill_6="0" />
    <data id="15008" condition1="5008" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="15009" condition1="5009" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="15010" condition1="5010" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401009" raise_passive_skill_5="30401010" raise_passive_skill_6="0" />
    <data id="15011" condition1="5011" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401011" raise_passive_skill_5="30401010" raise_passive_skill_6="0" />
    <data id="15012" condition1="5012" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401012" raise_passive_skill_5="30401010" raise_passive_skill_6="0" />
    <data id="15013" condition1="5013" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401012" raise_passive_skill_5="30401013" raise_passive_skill_6="0" />
    <data id="15014" condition1="5014" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401014" raise_passive_skill_5="30401013" raise_passive_skill_6="0" />
    <data id="15015" condition1="5015" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401015" raise_passive_skill_5="30401013" raise_passive_skill_6="0" />
    <data id="15016" condition1="5016" condition2="0" condition3="0" raise_passive_skill_1="3040103" raise_passive_skill_2="30101007" raise_passive_skill_3="30401006" raise_passive_skill_4="30401015" raise_passive_skill_5="30401016" raise_passive_skill_6="0" />
    <data id="15108" condition1="5108" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="15109" condition1="5109" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="15110" condition1="5110" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402009" raise_passive_skill_5="30402010" raise_passive_skill_6="0" />
    <data id="15111" condition1="5111" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402011" raise_passive_skill_5="30402010" raise_passive_skill_6="0" />
    <data id="15112" condition1="5112" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402012" raise_passive_skill_5="30402010" raise_passive_skill_6="0" />
    <data id="15113" condition1="5113" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402012" raise_passive_skill_5="30402013" raise_passive_skill_6="0" />
    <data id="15114" condition1="5114" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402014" raise_passive_skill_5="30402013" raise_passive_skill_6="0" />
    <data id="15115" condition1="5115" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402015" raise_passive_skill_5="30402013" raise_passive_skill_6="0" />
    <data id="15116" condition1="5116" condition2="0" condition3="0" raise_passive_skill_1="3040203" raise_passive_skill_2="30102007" raise_passive_skill_3="30402006" raise_passive_skill_4="30402015" raise_passive_skill_5="30402016" raise_passive_skill_6="0" />
    <data id="15208" condition1="5208" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="15209" condition1="5209" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="15210" condition1="5210" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403009" raise_passive_skill_5="30403010" raise_passive_skill_6="0" />
    <data id="15211" condition1="5211" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403011" raise_passive_skill_5="30403010" raise_passive_skill_6="0" />
    <data id="15212" condition1="5212" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403012" raise_passive_skill_5="30403010" raise_passive_skill_6="0" />
    <data id="15213" condition1="5213" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403012" raise_passive_skill_5="30403013" raise_passive_skill_6="0" />
    <data id="15214" condition1="5214" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403014" raise_passive_skill_5="30403013" raise_passive_skill_6="0" />
    <data id="15215" condition1="5215" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403015" raise_passive_skill_5="30403013" raise_passive_skill_6="0" />
    <data id="15216" condition1="5216" condition2="0" condition3="0" raise_passive_skill_1="3040303" raise_passive_skill_2="30103007" raise_passive_skill_3="30403006" raise_passive_skill_4="30403015" raise_passive_skill_5="30403016" raise_passive_skill_6="0" />
    <data id="16008" condition1="6008" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="16009" condition1="6009" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="16010" condition1="6010" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501009" raise_passive_skill_5="30501010" raise_passive_skill_6="0" />
    <data id="16011" condition1="6011" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501011" raise_passive_skill_5="30501010" raise_passive_skill_6="0" />
    <data id="16012" condition1="6012" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501012" raise_passive_skill_5="30501010" raise_passive_skill_6="0" />
    <data id="16013" condition1="6013" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501012" raise_passive_skill_5="30501013" raise_passive_skill_6="0" />
    <data id="16014" condition1="6014" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501014" raise_passive_skill_5="30501013" raise_passive_skill_6="0" />
    <data id="16015" condition1="6015" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501015" raise_passive_skill_5="30501013" raise_passive_skill_6="0" />
    <data id="16016" condition1="6016" condition2="0" condition3="0" raise_passive_skill_1="3050103" raise_passive_skill_2="30101007" raise_passive_skill_3="30501006" raise_passive_skill_4="30501015" raise_passive_skill_5="30501016" raise_passive_skill_6="0" />
    <data id="16108" condition1="6108" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="16109" condition1="6109" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="16110" condition1="6110" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502009" raise_passive_skill_5="30502010" raise_passive_skill_6="0" />
    <data id="16111" condition1="6111" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502011" raise_passive_skill_5="30502010" raise_passive_skill_6="0" />
    <data id="16112" condition1="6112" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502012" raise_passive_skill_5="30502010" raise_passive_skill_6="0" />
    <data id="16113" condition1="6113" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502012" raise_passive_skill_5="30502013" raise_passive_skill_6="0" />
    <data id="16114" condition1="6114" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502014" raise_passive_skill_5="30502013" raise_passive_skill_6="0" />
    <data id="16115" condition1="6115" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502015" raise_passive_skill_5="30502013" raise_passive_skill_6="0" />
    <data id="16116" condition1="6116" condition2="0" condition3="0" raise_passive_skill_1="3050203" raise_passive_skill_2="30102007" raise_passive_skill_3="30502006" raise_passive_skill_4="30502015" raise_passive_skill_5="30502016" raise_passive_skill_6="0" />
    <data id="16208" condition1="6208" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="16209" condition1="6209" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="16210" condition1="6210" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503009" raise_passive_skill_5="30503010" raise_passive_skill_6="0" />
    <data id="16211" condition1="6211" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503011" raise_passive_skill_5="30503010" raise_passive_skill_6="0" />
    <data id="16212" condition1="6212" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503012" raise_passive_skill_5="30503010" raise_passive_skill_6="0" />
    <data id="16213" condition1="6213" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503012" raise_passive_skill_5="30503013" raise_passive_skill_6="0" />
    <data id="16214" condition1="6214" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503014" raise_passive_skill_5="30503013" raise_passive_skill_6="0" />
    <data id="16215" condition1="6215" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503015" raise_passive_skill_5="30503013" raise_passive_skill_6="0" />
    <data id="16216" condition1="6216" condition2="0" condition3="0" raise_passive_skill_1="3050303" raise_passive_skill_2="30103007" raise_passive_skill_3="30503006" raise_passive_skill_4="30503015" raise_passive_skill_5="30503016" raise_passive_skill_6="0" />
    <data id="17008" condition1="7008" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="17009" condition1="7009" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="17010" condition1="7010" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601009" raise_passive_skill_5="30601010" raise_passive_skill_6="0" />
    <data id="17011" condition1="7011" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601011" raise_passive_skill_5="30601010" raise_passive_skill_6="0" />
    <data id="17012" condition1="7012" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601012" raise_passive_skill_5="30601010" raise_passive_skill_6="0" />
    <data id="17013" condition1="7013" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601012" raise_passive_skill_5="30601013" raise_passive_skill_6="0" />
    <data id="17014" condition1="7014" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601014" raise_passive_skill_5="30601013" raise_passive_skill_6="0" />
    <data id="17015" condition1="7015" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601015" raise_passive_skill_5="30601013" raise_passive_skill_6="0" />
    <data id="17016" condition1="7016" condition2="0" condition3="0" raise_passive_skill_1="3060103" raise_passive_skill_2="30101007" raise_passive_skill_3="30601006" raise_passive_skill_4="30601015" raise_passive_skill_5="30601016" raise_passive_skill_6="0" />
    <data id="17108" condition1="7108" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="17109" condition1="7109" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="17110" condition1="7110" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602009" raise_passive_skill_5="30602010" raise_passive_skill_6="0" />
    <data id="17111" condition1="7111" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602011" raise_passive_skill_5="30602010" raise_passive_skill_6="0" />
    <data id="17112" condition1="7112" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602012" raise_passive_skill_5="30602010" raise_passive_skill_6="0" />
    <data id="17113" condition1="7113" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602012" raise_passive_skill_5="30602013" raise_passive_skill_6="0" />
    <data id="17114" condition1="7114" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602014" raise_passive_skill_5="30602013" raise_passive_skill_6="0" />
    <data id="17115" condition1="7115" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602015" raise_passive_skill_5="30602013" raise_passive_skill_6="0" />
    <data id="17116" condition1="7116" condition2="0" condition3="0" raise_passive_skill_1="3060203" raise_passive_skill_2="30102007" raise_passive_skill_3="30602006" raise_passive_skill_4="30602015" raise_passive_skill_5="30602016" raise_passive_skill_6="0" />
    <data id="17208" condition1="7208" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603008" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="17209" condition1="7209" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603009" raise_passive_skill_5="0" raise_passive_skill_6="0" />
    <data id="17210" condition1="7210" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603009" raise_passive_skill_5="30603010" raise_passive_skill_6="0" />
    <data id="17211" condition1="7211" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603011" raise_passive_skill_5="30603010" raise_passive_skill_6="0" />
    <data id="17212" condition1="7212" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603012" raise_passive_skill_5="30603010" raise_passive_skill_6="0" />
    <data id="17213" condition1="7213" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603012" raise_passive_skill_5="30603013" raise_passive_skill_6="0" />
    <data id="17214" condition1="7214" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603014" raise_passive_skill_5="30603013" raise_passive_skill_6="0" />
    <data id="17215" condition1="7215" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603015" raise_passive_skill_5="30603013" raise_passive_skill_6="0" />
    <data id="17216" condition1="7216" condition2="0" condition3="0" raise_passive_skill_1="3060303" raise_passive_skill_2="30103007" raise_passive_skill_3="30603006" raise_passive_skill_4="30603015" raise_passive_skill_5="30603016" raise_passive_skill_6="0" />
</root>
