<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:id func_id=int:模块id task_id=int:任务id progress=int:成就类型值 award_type1=int:奖励道具类型1 award_value1=int:奖励道具类型值1 award_size1=int:奖励道具数量1 award_type2=int:奖励道具类型2 award_value2=int:奖励道具类型值2 award_size2=int:奖励道具数量2 award_type3=int:奖励道具类型3 award_value3=int:奖励道具类型值3 award_size3=int:奖励道具数量3 jump=int:跳转界面 -->

<root>
    <data id="1" func_id="1" task_id="1" progress="100" award_type1="23" award_value1="0" award_size1="100" award_type2="1" award_value2="0" award_size2="100" award_type3="2" award_value3="0" award_size3="10" jump="0" />
</root>
