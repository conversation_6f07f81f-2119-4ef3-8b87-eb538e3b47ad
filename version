#!/bin/bash

compile=`date +"%F %T %z"`" by "`go version`
if [ $? -ne 0 ]; then
  compile="unknown datetime"
fi

function CreateVersion()
{
  version=`git log --date=iso --pretty=format:"%cd @%H" -1`
  if [ $? -ne 0 ]; then
    version="unknown version"
  fi

  describe=`git describe --tags 2>/dev/null`
  if [ $? -eq 0 ]; then
    version="${version} @${describe}"
  fi
  echo $version
}

Version=$(CreateVersion)

ROOT=$(pwd)
cd data && DataVersion=$(CreateVersion)
`sed -i "s|<data_version>.*</data_version>|<data_version>${DataVersion}</data_version>|" server.xml`
cd $ROOT
cd protos/out && ProtoVersion=$(CreateVersion)
cd $ROOT

cat << EOF | gofmt > src/app/version/version.go
package version
const (
Version = "$Version"
DataVersion = "$DataVersion"
ProtoVersion = "$ProtoVersion"
Compile = "$compile"
)
EOF

cat << EOF > bin/version
Version = $Version
DataVersion = $DataVersion
ProtoVersion = $ProtoVersion
Compile = $compile
EOF
