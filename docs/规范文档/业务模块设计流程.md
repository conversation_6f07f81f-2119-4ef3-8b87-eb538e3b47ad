# 业务模块设计流程

[TOC]

### 问题

1. #####  命名不规范，容易产生歧义

   ![img](https://api2.mubu.com/v3/document_image/510daf38-b0cb-4545-a576-1dd5d5fb40bd-12605556.jpg)
   

2. ##### 函数层级不清晰

   ![img](https://api2.mubu.com/v3/document_image/a5bdf70a-dc1d-4244-a836-073ed2efe5aa-12605556.jpg)
   

3. ##### 函数的抽象程度不够好：a函数内部分逻辑，b,c函数中也有

4. ##### 整体掌控感较弱，想到哪，写到哪

5. ##### 联调时，才发现未满足策划预期




### **分享的目的**

##### 希望能够帮助到有此困惑的同事，欢迎大家指正和帮忙完善



### **设计的过程**

#### 策划案的阅读

##### 他要什么？

*设计初期*

- 策划宣讲需求前，通读文档，尽量将需求内容转化为自己最能理解的描述来帮助理解。记下所有疑问点（跟自己的理解有什么出入？），便于会上讨论。阅读过程中，可以借助量表的设计来帮助理解
- 会后消化理解，所有有疑问的需求。在此过程中，可与策划充分讨论，做到完全理解，统一思想

##### 从他的案子，到我的系统

*设计与实现中*

  - 如果你完全可以用自己的话描述清楚整个系统了（最好拉着策划，跟他复述一遍 --- 二次确认）。那么此时可以放手设计了
  - 在需求充分内化后，可能会发现逻辑问题，量表配置不合理的问题等等。这时应该再次与策划确认，自己的想法是否更合理

##### 我的系统，是你想要的吗？

*实现后*

  - 查缺补漏，核对各协议是否符合预期

#### 数据结构的设计

*底层基础决定上层建筑*

- 抽象划分出合理的结构单元
- 确认数据归属类型
  - 公共数据
  - 个人数据

#### 量表管理

- 检查确认量表数据设计
  - 定义是否合理：关联表之间，是否可以拆分或者合并
  - 格式是否规范：例如，资源（type,value,count），属性（type,attr）…
  - 命名是否规范，是否可以简化：例如，用lev代表等级
- 梳理量表间的关联关系，需注意加载顺序
- 启动时的数据检查
  - 目的
    - 问题前置：减少因数据配置错误，导致的系统错误
    - 提高开发效率：在开发阶段，遇到因配置错误导致的异常时，可以快速定位处理问题
  - 方法
    - 验证数据界限：对于已明确范围的数据，可以采用
    - 验证关联表间的数据有效性：例如，有关联关系的tree,seed量表，seed中数据会关联tree_id。当加载seed表数据时，就有必要验证，数据中的tree_id在tree表中对应的数据是否存在
- 对外提供的接口
  - 获取一条数据
  - 获取一组数据
  - 一组中随机一条数据
  - 根据参数，返回一条关联表数据 - 例如：seed表会根据不同的分数，关联到一个tree_id，接口最终返回的是tree表的配置tree_cfg

#### 协议设计

- 按时序设计
- 按功能定名：增、删、查、改
- 入参
- 出参

#### manager的设计

*用来管理model（例如：heroesManager与hero间的关系）*

- 主要功能：承接db和内存对象，管理数据
- 根据数据类型分类，不同类型，加载方式不同
  - 全局数据的管理器
  - 个人数据的管理器
- 常规接口
  - 初始化：NewXxxManager
  - 加载：Load
  - 获取：Get
  - 添加：Add
  - 修改：Update
  - 删除：Delete
  - flush数据：FlushAll
  - 设置保存标识：SetChange
  - 保存：Save
- 其他接口，根据业务而定（个性化的需求，往往需要重点考虑）

#### model的设计

- 主要功能：对对象中的数据，查询和修改
- 常规接口
  - 初始化 - NewXxx
  - flush数据 - Flush
  - 获取 - GetData
  - GetXxx
  - SetXxx
  - UpdateXxx
- 其他接口，根据业务与数据结构而定（个性化的需求，往往需要重点考虑）
- 函数设计原则
  - 可见性/访问权限
    - 对外的接口，提供的是对一系列数据的管理
    - 真正修改数据的方法，都是私有的
  - 粒度控制 --- 根据业务性质，如果一组数据必须同时变动，那就把他们封装到一个函数中处理

    ![img](https://api2.mubu.com/v3/document_image/727118c6-820c-42c2-861a-e68ad63d74a0-12605556.jpg)
    
  
- 规范命名
  - 简单易懂
  - 动词或动词加名词

#### command搭积木

- 参数验证
  - 量表数据
  - 玩家数据
- 业务逻辑中的接口调用
  - 量表管理器中的接口
  - manager中的接口
  - model中的接口
- 错误日志
- 返回数据
- 行为日志

#### 设计会

- 目的：讨论业务模块设计的合理性

- 要求：开会前，开发者需要完成业务梳理，初步完成协议以及核心接口的设计

- 结果：提出优化或改进意见，帮助完善业务设计

  

### 自测的重要性

*再牛B的设计，最终还需实践来检验*

- 尽量对着需求文档（最终确认版），进行测试验证
- 常规参数
- 边界值
- 关联性：系统内（或其他系统）数据变化后，可能会影响到当前协议的执行
- 举例：密林系统自测问题

  ![img](https://api2.mubu.com/v3/document_image/64dd4d1e-837e-4def-88f8-bd1aa9ee1000-12605556.jpg)
  

### 联调

- 作用：通过客户端，再次验证功能的实现，进一步完善协议设计实现
- 目标：强化设计与自测，联调阶段仅提供协助
- 注意：如果在此阶段，发现很多问题。那说明，前面做的不够好，该好好复盘反思了！
  

### 复盘总结

- 系统核心逻辑的梳理
- 设计上得到的启发
- 过程中犯下的错误
- 做的不好的地方
- 举例：密林系统开发总结

  ![img](https://api2.mubu.com/v3/document_image/92a77058-39a0-42a3-8293-4a409c54da75-12605556.jpg)
  

