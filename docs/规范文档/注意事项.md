## 以下必须准守

1. 【强制规定】`禁止定义全局变量`，配置文件相关除外。
2. 【强制规定】方法的接收者命名禁止使用this，推荐使用结构体首字母小写。
3. 【强制规定】LogicService中`禁止使用标准库math/rand`, 必须使用sea/math/rand替换。
4. 【强制规定】功能完成之后，必须`添加相应的Robot操作`。
5. 【强制规定】服务器返回到客户端的协议中必须包含ret字段（代表处理结果，并且方便统一处理）。
6. 【强制规定】代码长度最长一屏，最好不要超过半屏（超过考虑提炼函数）。

---
## 以下视情况而定

1. 【建议】仅在package内部使用的变量，结构体，函数等，首单词小写（私有化）。
2. 【建议】简化日志类型分级，只有`三级Error/Info/Debug`。
3. 【建议】goxml中manager中的数据指针尽量避免被外部引用导致常驻内存，影响配置文件热更新。例如（*goxml.KnightInfo被character.Knight）
4. 【建议】对逻辑较复杂的函数进行注释，方便后续维护，格式参考如下:
~~~
//函数功能描述
//@param arg1 - 参数1
//@param arg2 - 参数2
//@return uint32 - 返回值1
//@return string - 返回值2
func (t *TestData) Process(arg1, arg2 uint32) (uint32, string) {
return 0, ""
}
~~~
5. 【建议】提交代码时，按照固定格式写注释:
~~~
git commit xxx.go -m "模块名中文[ModuleName]-修改内容纪要"
~~~

---
## 参考

