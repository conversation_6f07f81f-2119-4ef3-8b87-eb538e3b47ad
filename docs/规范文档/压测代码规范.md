## robot代码规范

1. 所有logic返回的数据的处理函数的返回值修改，不要都是true，根据返回的ret判断

   ```go
   type L2CMirageDetailCommand struct {
   }
   
   func (c L2CMirageDetailCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
   	recv := &cl.L2C_MirageDetail{}
   	if err := proto.Unmarshal(data, recv); err != nil {
   		l4g.Error("L2CMirageDetailCommand unmarshal error: %s", err)
   		return false
   	}
   	return true /////===》》改为return recv.Ret == uint32(ret.RET_OK)
   }
   ```

2. 如果在初始化的时候需要布阵，请调用方法

   ```go
   func SetFormation(r *Robot, id uint32) bool {
   	
   }
   ```

3. Init方法规范

   里面主要是做初始化的工作，比如布阵，比如拉取info信息等。不要做其他的事情。

   ```go
   //初始化请求模块数据 并添加流程锁，防止重复拉取
   func (m *Mirage) C2LInit() {
   }
   ```

4. randAction注册

   注意，拉取info信息的接口不要注册到randActions里面

   ```go
   func InitMirageActions() {
       ////====》注意这些拉取初始信息的数据，不要注册进来，除非每次拉取信息都与额外的修改数据的工作
       //gMirageActions = append(gMirageActions, (*Mirage).C2LMirageList)
       gMirageActions = append(gMirageActions, (*Mirage).C2LMirageDetail)
       gMirageActions = append(gMirageActions, (*Mirage).C2LMirageFight)
       gMirageActions = append(gMirageActions, (*Mirage).C2LMirageRecvStarAward)
       gMirageActions = append(gMirageActions, (*Mirage).C2LMirageSaveAffixes)
   }
   ```

5. robot的参数设置

   所有的参数都封装一个随机函数出来

   在随机函数里面轻调用RandNormal()方法 如果返回true，在下面生成正常的数据，否则就用任意随机的数据。相关公用的随机函数放在helper.go里面

   想要调整生成正常数据的概率在config/robot.xml里配置，最大值10000 代表一定是正常数据

   大家有疑问可以讨论：比如也可以不按照参数的力度，直接随机整个struct。

   ```go
   func (d *Formation) C2LFormation() {
       r := d.u 
       fid := RandFormationId()
       pos1 := RandFormationHeroPos()
       pos2 := RandFormationArtifactPos()
       infos := make([]*cl.FormationInfo, len(pos1))
       artifacts := make([]*cl.FormationArtifactInfo, len(pos2))
       for k, v := range pos1 {
           infos[k] = &cl.FormationInfo{
               Pos: uint32(v),
               Hid: RandHeroId(r),
           }   
       }   
       for k, v := range pos2 {
           artifacts[k] = &cl.FormationArtifactInfo{
               Pos:   uint32(v),
               Aid:   RandArtifactSysId(r),
               Stage: RandArtifactStage(r),
           }   
       }   
       formation := &cl.Formation{
           Id:        fid,
           Info:      infos,
           Artifacts: artifacts,
       }   
   }
   
   ////随机一个阵容id出来
   func RandFormationId() uint32 {
       if RandNormal() {////正常情况
           return RandFromSlice([]uint32{1, 7, 35, 40})
       }
       return rand.Uint32() //随机情况
   }
   ```

6. 一些在登录的时候flush的数据，在robot里面也flush进来，放在robot里面，在收到OpResources的时候请更新对应的数据，请修改对应的数据，比如hero,equiopment等

   ```go
   type Robot struct {
         act        *actor.Actor
         broker     *tcp.Broker
       
       
          heroes           []*cl.Hero /////===》》在收到OpResources的时候需要更新到这里。
         artifacts        []*cl.Artifact
         formations       map[uint32]*cl.Formation
         emblems          []*cl.EmblemInfo
         equips           []*cl.Equipment
       	/////====>继续添加一些特定的基础基础
   
   }
   ```

   