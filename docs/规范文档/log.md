- 日志类型定义

  - 文件

    ```
    protos/in/log.proto
    ```

  - 分类

    - LOG_TYPE相关日志，由平台定义，项目组只引用，不修改
    - SUB_TYPE相关日志，由项目组定义，表示玩家游戏内的行为类型
    - RESOURCE_CHANGE 相关日志,由项目组定义，表示玩家资源变化原因，产出和消耗有可能是同一原因的只定义一条
    - SUB_TYPE和RESOURCE_CHANGE的定义都是按模块划分，基础的是1000以内的

- 行为日志函数格式

  - 函数均定义在app/logic/character/log.go中，同一模块内的日志埋点，建议放在一起
  - 函数命名推荐以LogProtocol的方式，比如妖灵升级protocol为C2L_PetUpLevel,那么日志埋点函数命名为LogPetUpLevel
  - 函数第一个参数必须时servicer接口，后面的参数根据需要自己定义
  - 函数内newLogMessage生成的日志变量必须定义成msg，后期平台通过脚本读取每条日志的字段和注释信息
  - 日志类型分为LogType和SubType，除了几条基础日志，游戏内日志的LogType均是固定的，我们只需要定义SUB_TYPE，并且在定义类型后标记注释
  - 每个msg成员变量赋值都，都需要带上注释，表明该字段的意义，方便平台脚本读取
  - 如果遇到slice或者map结构，建议都通过json转换成字符串,[]byte转换string统一使用util.String(bytes)
  - 游戏逻辑日志埋点中不允许出现资源相关类的数据，资源产销必须经过 Award，Consume或Trade这几个函数
  - 具体参考

```go
func (u *User) LogPetUpLevel(srv servicer, pet *Knight, preLvl uint32) {
  msg := u.newLogMessage(srv)
  msg.TypeName = "妖灵升级"
  msg.LogSubType = uint32(log.SUB_TYPE_ID_PET_UP_LEVEL)
  msg.Param10 = pet.ID()             //唯一id
  msg.Param11 = uint64(pet.BaseID()) //配置id
  msg.Param12 = uint64(preLvl)       //升级前等级
  msg.Param13 = uint64(pet.Level())  //升级后等级
  if skills := goxml.KnightSkillLevelInfoM.GetActivateSkills(
    pet.Base(), preLvl, pet.Level()); skills != nil {
		if bytes, err := json.Marshal(skills); err == nil {
    	msg.Param1 = string(bytes) //本次激活技能
		}
  }
  srv.WriteLogMessage(msg)
}
```

- 资源产销
  - 游戏内资源产销只经过Award、Consume、Trade三个函数，所以这三个函数在最后都加了一个参数表示资源变动原因，默认现在都填0了，每个人负责将自己模块内的资源变动原因完善。
