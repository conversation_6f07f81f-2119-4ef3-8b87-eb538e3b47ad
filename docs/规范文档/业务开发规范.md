## 开发规范

1. 给前端的协议中，系统表的ID命名为sys_id,自增的ID命名为id

```protobuf
message HeroBody {
  uint64 id = 1;                      //自增的ID
  uint32 sys_id = 2;                  //英雄配置hero_info.xml表id
  uint32 star = 3;                    //星级
  uint32 level = 4;                   //等级
 
}
```

2. 所有的功能id放在连续的一起，并且分配一个相对大的区间 并且有统一的前缀，方便以后做脚本

```protobuf
  //普通竞技场 前缀全部是CommonArena 协议ID是连续的 并且一个单独模块一般预留100个协议ID
  MSG_MIN_CommonArena = 12400;
  MSG_C2L_CommonArenaInfo = 12401;  //竞技场信息
  MSG_L2C_CommonArenaInfo = 12402;
  MSG_C2L_CommonArenaRefresh = 12403;  //刷新对手
  MSG_L2C_CommonArenaRefresh = 12404;
  MSG_C2L_CommonArenaFight = 12405;  //战斗
  MSG_L2C_CommonArenaFight = 12406;
  MSG_C2L_CommonArenaRecordList = 12407;  //战报信息
  MSG_L2C_CommonArenaRecordList = 12408;
  MSG_C2L_CommonArenaLike = 12409;  //普通竞技场点赞
  MSG_L2C_CommonArenaLike = 12410;
  MSG_C2L_CommonArenaRank = 12411;  //普通竞技场排行榜
  MSG_L2C_CommonArenaRank = 12412;

```

3. 服务器启动的时候加载的数据一定要控制大小，能不加载就不加载，必须加载也得控制大小，参照竞技场设计

4. 全服发奖的逻辑要注意时间点，不要都放在12点全服重置的高峰期，参照竞技场

5. 版本估时的规范

   - 设计的时间
   - 数据初始化时间
   - 列出N个协议的时间
   - 自测时间（这里要完成所有机器人的编码）
   - 联调时间

6. 如果是需要在C2L_Flush协议里推给前端的数据，在对应的Get协议里不需要判断function open。其他协议正常判断

7. 所有打印error的地方都要带上信息，比如command里的打印都加上玩家的uid，比如goxml里的加载错误都加上表的唯一id方便排查

8. 前端传的切片和map一定要判断长度在使用。前端传的切片和map一定要判断里面是否有重复的数据，是否有消费自己，补充自己的情况（用材料升级等）

9. 数据的初始化尽量放在get协议里，不要放在数据load的地方（在load的时候初始化会导致创角多次写db，影响创角效率，也是数据浪费）

10. 业务逻辑抛出事件只抛出一个类型的事件（以Ie开头），其他的任务的事件根据自己的情况在前面的事件的处理函数里在抛出新的事件。保证一个事件的触发点就一个地方。

    ```go
    //command.go
    c.User.FireCommonEvent(c.Srv.EventM(), event.IeNewHeroEvent, 1, heroSysId)
    
    //event/event.go
    const (
        //以下是成就/每日任务, 统一Ae开头
        AeNewHero         = 1006 //今天获取的新英雄数
        
        //以下是游戏内部事件
        IeNewHero          = 10007 //获得新英雄了
    )
              
        
    //character/evnet.go
    func doNewHeroEvent(evt event.Eventer, param interface{}) {
        event := evt.(aevent.CommonEvent)
        user := event.User.(*User)
        //srv := param.(servicer)
    
        //账号创建时，初始资源
        User.FireCommonEvent(c.Srv.EventM(), event.AeNewHeroEvent, 1, heroSysId)
        User.FireCommonEvent(c.Srv.EventM(), event.AeXXX, 1, heroSysId)
    }
    ```
    
11. goxml里的各种manager数据，尽量不要在character的user里直接引用。方便热更新之后能重新获取数据，

12. goxml你的各种manager里的数据除了Datas本身需要注意热加载的判断，其他数据在load的时候都无脑重新赋值更新。

    ```go
    type DispatchInfoManager struct {
        Datas       map[uint32]*DispatchInfoExt //这个数据需要做热更新检查
        RandomPool  map[uint32]map[uint32]uint32 //这个不需要 无脑初始化
        RedTaskPool map[uint32]map[uint32]uint32 //这个不需要 无脑初始化
    }
    ```

13. 在一堆数据里根据权限随机取数据的情况，不要用map作为容器，用slice。保证随机的稳定性。

    ```go
    ////这里的pool 用切片
    func randomTaskId(rand *rand.Rand, pool map[uint32]uint32) uint32 {
        var totalWeight, taskId uint32
        for _, v := range pool {
            totalWeight += v
        }
    
        if totalWeight == uint32(0) {
            return taskId
        }
    
        randNum := rand.Intn(int(totalWeight))
        var curTotal uint32
        for k, v := range pool {
            curTotal += v
            if uint32(randNum) < curTotal {
                taskId = k
                break
            }
        }
    
        return taskId
    }
    ```

14. 协议里调用战斗接口出错之后返回给前端的错误码不要用RET_ERROR,统一用RET_BATTLE_ERROR

15. 判断切片是否为空的时候不要用nil判断，都用len判断

    ```go
    var awards []*cl.Resource
    awards = goxml.GetAward()
    if awards!=nil {  ////这里用len(awards) > 0
    	user.Award(awards)
    }
    ```

16. 在协议里设置到获取或者修改每日重置或者周重置里保存的数据，command要继承base.TimelyResetUserCommand

    ```go
    type C2LFriendRecvLikeCommand struct {
        base.TimelyResetUserCommand ////注意这里
    }
    
    func (c *C2LFriendRecvLikeCommand) Execute(ctx context.Context) bool {
       //...
        if cnt := c.User.GetFriendRecvCount(); cnt != 0 { ////这里用到了每日重置你的数据
        }
    }
    ```

17. 在需要客户端传一些不可叠加的消耗资源的时候，比如分解的时候消耗的英雄，比如纹章升星的时候消耗的同名纹章等资源的时候。一定要检查前端传过来的资源是否里面有重复的，需要消耗的资源是否包含了不能被消耗的资源（比如要升星的那个纹章）

    纹章升星的示例代码

    协议

    ```protobuf
    // 纹章升阶
    message C2L_EmblemStarUp {
      uint64 id = 1;                        // 纹章id
      uint32 add_star = 2;                  //增加的阶级
      repeated uint64 cost_emblem_ids = 3;  // 消耗的纹章id
    }
    
    message L2C_EmblemStarUp {
      uint32 ret = 1;
      uint64 id = 2;                        // 纹章id
      uint32 add_star = 3;                  //增加的阶级
      repeated uint64 cost_emblem_ids = 4;  // 消耗的纹章id
      EmblemInfo emblem = 5;                // 纹章
    }
    
    ```

    command 代码

    ```go
    type C2LEmblemStarUpCommand struct {
    	base.UserCommand
    }
    
    func (c *C2LEmblemStarUpCommand) Execute(ctx context.Context) bool {
    	cmsg := &cl.C2L_EmblemStarUp{}
    	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
    		l4g.Error("C2L_EmblemStarUp Unmarshal error: %d %s", c.Msg.UID, err)
    		return false
    	}
        
    	var costs, awards []*cl.Resource
    	tmpEmblemIds := make(map[uint64]struct{})////=======>判断是否是重复的
    	for i := 0; i < len(cmsg.CostEmblemIds); i++ {
    		tmpId := cmsg.CostEmblemIds[i]
    		if _, exist := tmpEmblemIds[tmpId]; exist {
    			l4g.Error("[C2L_EmblemStarUp] emblemId repeat. uid:%d, emblemId:%d", c.Msg.UID, i)
    			return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
    
    		} else {
    			tmpEmblemIds[tmpId] = struct{}{}
    		}
    
    		if cmsg.Id == tmpId {////=======>判断是否是重复的 判断是否是自己的
    			l4g.Error("[C2L_EmblemStarUp] cost emblem id error. %d", c.Msg.UID)
    			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
    		}
    	}
    	//xxxx 处理逻辑
    
    	return true
    }
    ```

18. 所有的养成添加之后要注意离线玩家的添加规则。具体地方如下：@艾斯 补充

19. 所有的业务逻辑开始的地方添加trace日志（参照command/equip/command.go q强化和附魔方法）

    新的create_msg.sh脚本已经添加上了该功能

    ```go
    func (c *C2LEquipEnchantCommand) Execute(ctx context.Context) bool {
        cmsg := &cl.C2L_EquipEnchant{}
        if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
            l4g.Error("C2L_EquipEnchant Unmarshal error: %d %s", c.Msg.UID, err)
            return false
        }   
    
        c.Trace(cmsg) ////====>解析完消息之后立即Trace Msg
    
        l4g.Debug("user: %d C2L_EquipEnchant: %s", c.Msg.UID, cmsg)
        //edit here
        smsg := &cl.L2C_EquipEnchant{
            Ret:         uint32(cret.RET_OK),
            Id:          cmsg.Id,
            TargetLevel: cmsg.TargetLevel,
        }   
    ```

20. 所有的业务结束前需要记录日志，记录的内容如下

    - 前端传递过来的数据，

    - 是玩家身上发生变化的数据，变化前和变化后的都记录

    - 资源类数据，记得记录下系统Id的数据，方便做分析。

      如果上面两条数据有重复的记录一次就行了

      如果有复杂的数据，比如一键强化这种，把装备的变化数据在protos/in/log.proto里定义特殊的格式，在业务里合并传递过来

      示例：参照装备强化和装备附魔的command接口

      logSubType 定义在protos/in/log.proto里（资源变化的类型以后就不需要了和行为类型合二为一，可以讨论，楼下是分开的） 注意按模块划分，每个模块留100个

      

      ```go
      func (u *User) LogEquipStrength(srv servicer, id uint64, targetLevel uint32, oneKey bool, hid uint64, equips []*log.EquipStrength) {
          msg := u.newLogMessage(srv)
          msg.TypeName = "装备强化"
          msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_STRENGTH)
          msg.Param10 = id                            //装备id,如果是一键就是0
          msg.Param11 = uint64(targetLevel)           //强化到目标等级
          msg.Param12 = uint64(util.If(oneKey, 1, 0)) //一键1，否则未0
          msg.Param13 = hid                           //英雄id
          if bytes, err := json.Marshal(equips); err == nil {
              msg.Param1 = util.String(bytes) //装备强化信息
          } else {
              l4g.Error("LogEquipStrength json marshal error: %v", err)    }   
          srv.WriteLogMessage(msg)
      }
      
      func (u *User) LogEquipEnchant(srv servicer, equip *Equip, preLevel uint32) {
          msg := u.newLogMessage(srv)
          msg.TypeName = "装备附魔"
          msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_ENCHANT)
          msg.Param10 = equip.Data.Id                   //装备id
          msg.Param11 = uint64(equip.Data.SysId)        //装备系统id
          msg.Param12 = uint64(preLevel)                //附魔前等级
          msg.Param13 = uint64(equip.Data.EnchantLevel) //附魔到目标等级
          srv.WriteLogMessage(msg)
      }
      ```
    
21. 返回给前端的显示资源，都用Award方法返回的resource给前端，（除非特殊情况，比如抽将，比如一次是10个东西）。

    ```go
            if len(awards) > 0 {
                _, resources := m.owner.Award(srv, awards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_MAZE_TRIGGER_BATTLE_EVENT), 0)
                sMsg.Awards = m.resClone(resources) ////======>注意这里用返回的resources,不是原来的
            }    
    ```

22. struct的成员都用小写，在本struct所在的文件内访问成员属性可以直接访问，在~~本文件外必须用函数访问。（注意不是封装到package这一层）~~

    - 函数名在包内使用的，尽量都小写。包外调用的，都大小写。但是避免大写里面原封不动的调用一个小写，遇到这种情况，直接把小写换成大写直接用。

23. 游戏内加锁都建议用带有过期时间的锁。可以解决玩家异常掉线没有解锁到的情况

24. 游戏的错误日志的格式

    ```tex
    uid: %d 协议名: 错误描述. 参数1:%d 参数2:%d
    //错误描述后面的点号不能少。如果没有参数，那么错误描述后面的点号可以省略
    //参数都放在最后 多个参数用空格分隔开
    //如果没有uid 就省略开始的uid:%d部分
    //如果实在函数内部的， 协议名直接换struct.函数名的形式
    ```

    

    ```go
    if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FOREST)) {
            l4g.Errorf("user: %d C2L_ForestFeedSpecial: function not open", c.Msg.UID)
            return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
        } 
    
    //检查等级
    if info.LimitLv > forest.GetLv() {
        l4g.Errorf("user: %d C2L_ForestUpdateSlot: level limit. lv:%d requestLv:%d",
                   c.Msg.UID, forest.GetLv(), info.LimitLv)
        return c.Error(smsg, uint32(cret.RET_COMMON_LEVEL_NOT_ENOUGH))
    }
    
    ```

25. 错误日志格式补充

    - 所有的参数类的错误，一定要把前端传的参数都打印出来
    - 参数类的错误不要和其他错误混合在一起，比如把参数错误和重复领奖混在一起判断

26. 错误码的使用规范

    - 功能未开启：FUNCTION_NOT_OPEN
    - 客户端参数简单检查未通过（包括参数非法，参数重复等）：CLIENT_REQUEST_ERROR
    - 系统表数据不存在：SYSTEM_DATA_ERROR
    - 等级不足：LEVEL_NOT_ENOUGH
    - 重复领奖：REPEATED_RECEIVE_AWARD
    - 正常逻辑不可能出现的错误：ERROR(尽量不要用SERVER_ERROR 这个代表服务器异常，一般表示服务器连接不上的错误，前端可能有特殊用途) 
    - 玩家数据没有查到：各个系统自己定义，比如英雄不存在，装备不存在。
    - 玩家锁失败，各个系统定义，一般是对方正在xxx中
    - 玩家cd中，各个系统定义，搜索cd中，调整cd中。
    - 限制玩家单位时间内操作频率的：OPERATE_TOO_OFTEN

27. 不要在character里面引用activity的包

28. 尽量也不要在activity里引用character的包。如果有能改就改。这两个包逻辑上来说是平行的关系

29. 在保存数据的时候比如有修改和删除等多个操作的时候都用redis mcall操作

    ```go
    func (r *RedisActor) SaveForest(msg *r2l.L2R_SaveForest) {
        calls := 0 
        if len(msg.Changes) > 0 {
            r.ropCl.SetSomeForestMCall(msg.Changes)
            calls++
           
        }    
        if len(msg.Deletes) > 0 {
            r.ropCl.RemSomeForestMCall(msg.Deletes)
          
            calls++
        }    
    
        for i := 0; i < calls; i++ {
            if reply := r.client.GetReply(); reply.Err != nil {
                l4g.Errorf("save forest error: %s %s", msg, reply.Err)
                ctx.Stop()
            }
        }
    }
    ```

30. 协议最好不要传map,特别是数量很少的时候，一个map很废的，用协议来频繁创建不划算

31. 减少logic command里出现查询系统表的次数。有的话尽量合并。减少代码阅读的难度

    老的代码：

    ```go
    rare := goxml.ForestScoreInfoM.GetRare(tree.Score)
    	if rare == 0 {
    		l4g.Errorf("user: %d C2L_ForestStartPlant: score config not exist. score:%d",
    			c.Msg.UID, tree.Score)
    		return c.Error(smsg, uint32(cret.RET_COMMON_SYSTEM_DATA_ERROR))
    	}
    
    	info := goxml.ForestTreeInfoM.Index(rare)
    	if info == nil {
    		l4g.Errorf("user: %d C2L_ForestStartPlant: tree config not exist. rare:%d",
    			c.Msg.UID, rare)
    		return c.Error(smsg, uint32(cret.RET_COMMON_SYSTEM_DATA_ERROR))
    	}
    
    ```

    

    新的代码：

    ```go
    info := goxml.ForestScoreInfoM.GetTreeInfoByScore(tree.Score)
    	if info == nil {
    		l4g.Errorf("user: %d C2L_ForestStartPlant: tree config not exist. score:%d",
    			c.Msg.UID, tree.Score)
    		return c.Error(smsg, uint32(cret.RET_COMMON_SYSTEM_DATA_ERROR))
    	}
    ```

32. 尽量少用float做运算，效率较低。

33. init或者new之后直接把数据返回，不需要在去get返回。

34. 在需要落地保存usersnaphot的时候，用精简版usersnaphot，减少存储消耗。待封装。

35. 











## 开发tips

1. 开发业务时，先保证没有bug，在保证没有性能问题。在对框架研究不是很深入的前提下，不要过多考虑性能问题。等积累足够了，在一起考虑

2. 减少多层的for嵌套，防止性能出现问题

3. 在自己本地记得拉取策划配置的excel表，方便自己确认策划的配表有没有问题。excel表里的注释更多，查找也比较方便

   svn地址：http://************:6688/svn/nag/Develop/trunk/RawContent/Property

   账号密码：请找麦迪文或者公司IT

4. 做业务逻辑条件的时候，先把简单条件判断的内容放在前面（比如参数检查，系统表是否存在等），复杂判断的内容（比如用户数据是否存在，次数是否满足等）放在后面

5. 尽量减少策划把表都配置到config_info表里，这样会导致这个表耦合非常高，每个系统里的数据都在这个表里有，一不小心策划改错了会影响所有表

6. 提交代码之前一定要先diff，在提交

7. 提交完代码之后要在jenkins上执行一下检查脚本，防止漏提交代码

   jenkins地址:http://192.168.0.235:8090/

   账号：root

   密码：root123

   选择`后端检查`执行

8. 

