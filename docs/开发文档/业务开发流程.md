## 业务开发流程
1. 了解需求，分析需求
2. 开始设计，写设计文档, 设计文档主要包含以下几个部分内容
	1. 数据存储结构设计
	2. xml表结构设计(设计完和策划核对)
	3. 前后端协议设计(设计完和前端核对)
	4. 业务核心流程
	5. 业务的核心难点
3. 多看之前的业务实现，遵守现有项目规范，不要自由发挥
4. 复用已实现的公共函数，不需要自己再另定义一套


## 编码流程
1. **xml - 策划配置数据管理**
	1. 本地修改好xml结构之后，修改tools/build_xml_data.sh，添加对应的xmlc命令为了生成Info文件。
	    如果配置文件较为复杂，不方便通过info文件直接使用，则建议通过使用manager方式获取配置数据（命令中添加'-simple=false'）。
	2. 在项目根目录，执行`make xml`来生成对应的go文件。该info文件不需要提交git。如使用manger方式，xxx_info_manager.go文件需要手动创建。
	3. 如果简单版info文件满足不了需求，可以手动添加manager文件，修改tools/build_xml_data.sh的info生成方式。以下是两种生成方式的案例代码：
        1.  简单模式：`xmlc -in $PROJECT_PATH/data/hero_info.xml -out $PROJECT_PATH/src/app/logic/goxml/`
	    2.  manager模式：`xmlc -simple=false -in $PROJECT_PATH/data/hero_data_info.xml -out $PROJECT_PATH/src/app/logic/goxml/`
	4. manager模式中的命名规范，对外提供数据的结构体以InfoManager结尾。如果原Info内容不能直接在InfoManager中包含使用的话，需要使用中间结构体，其命名以InfoExt结尾。
	    例如hero_stage_info.xml，主结构体命名为`HeroStageInfoManager`，中间结构体命名为`HeroStageInfoExt`
	5. 打开src/app/logic/goxml/all.go文件，将刚刚添加的表，填写到var全局变量和Load方法中
2. **proto - 协议&数据结构管理**
	1. 对外的协议是放在ngame/proto/out里，对内的协议是放在server/protos/in里面，注意两个地方git地址不一样
	2. 本地修改好protos结构之后，如果是需要落地的结构，需要执行`make db`, 如果是协议部分，需要执行`make proto`
3. **command - 接口协议**(*个人理解，相当于MVC结构中的C*)
	1. 调用`tools/create_msg.sh daily_task "日常任务模块" 11700 11710`生成对应的command包,自动生成src/app/logic/commnad/dailytask/comman.go，修改协议逻辑就可以
	2. 调用`tools/register_msg.sh daily_task "日常任务"`,会在src/app/logic/services/commands.go里注册协议
4. **module - 数据逻辑**(*个人理解，相当于MVC结构中的M*)
    1.  与主角关联密切，且数据量较小的模块
    	1. 修改'src/app/logic/character/moduler.go' 里面添加注册和获取的方法
    	2. 修改'src/app/logic/character/load.go'里面的loadDBModule方法里添加新模块的load方法
    	3. 执行`tools/module_template.sh daily_task cl`(cl表示这个文件存储的包是放cl还是db)character下添加新的模块文件dailytask.go
    	   这里注意包的名字和模块的名字最好相同
    2.  数据量较大，单独模块。如英雄，装备等。以下用英雄模块举例
    	1. 在src/app/logic/character/下，新建hero_mangager.go文件，并创建HeroM结构体（类），用于管理所有英雄。
    	    并创建`NewHeroM()`，`Load()`方法，分别用于实例化HeroM和加载英雄数据
    	2. 修改src/app/logic/character/user.go，在User结构体中，添加 `heroes *HeroM` 和 实例化heroM的方法`HeroManager()`
    	3. 触发实例化和数据加载。在src/app/logic/character/load.go中的`loadDBUser`，`load`方法中，添加`u.heroes = NewHeroM(dbUser.Id)`，`u.HeroManager().Load(recv.Heroes)`
    	4. 如何将英雄数据从数据库中读取出来？需要找到上一步中`u.HeroManager().Load(recv.Heroes)`的`recv`，
    	    查找到具体的请求数据是在`src/app/logic/db/redis_actor.go`的`LoginByUID()`添加的，将获取英雄数据的相关代码添加到此方法，即可在登录时拿到英雄数据了
5. **robot - 测试**
    1.  生成robot测试模板。进入tools目录，执行`./create_robot_code.sh 模块名 模块描述 开始协议id 结束协议id`，即可生成对应的robot脚本
        以英雄模块举例：
        1.  执行命令：`./create_robot_code.sh hero 英雄 11900 11912`
        2.  在src/app/tools/robot/下，打开新生成的hero.go
        3.  找到C2LInit()方法，添加待测试的协议，如添加`h.C2LGetHeroes()`
        4.  此例子中不需要参数。如果需要，则需找到对应的`C2LGetHeroes`方法，按需求填写待测试的参数
        5.  可以在`L2CGetHeroes`方法中，对返回数据进行处理
        6.  通过修改config/robot.xml文件，可以对待测试的模块，进行开关调节
    2.  编译
        1.  编译服务端代码：在根目录执行 `make service`
        2.  编译客户端(robot)：在根目录执行 `make tools`
    3.  运行测试
        1.  编译完成后，会在bin/目录下生成对应的可执行文件
        2.  启动服务端程序：`./cmd.sh start service  -name=logic -http=:10002 -grpc=:20002 -log_level=DEBUG` ，此处的'10002'需要根据开发者的配置进行替换
        3.  启动客户端(robot)程序：`./robot -node=1000000002 -log_level=DEBUG`，此处的'1000000002'需要根据开发者的配置进行替换
    4.  查看运行结果
        1.  在`log/`目录下的，logic.log和robot.log查找相关数据即可
        2.  如果运行出现panic错误，可以在server_errors.log查看信息
6. **编码完成提交**
	1. 先提交protos/out里的内容
	2. 让策划提交表
	3. 提交server代码

## 常见公共模块 TODO
1. **通用错误编码**
    1.  文件位置：./protos/out/ret.proto
    2.  添加错误码：将自定义的错误码，以`key=>value`格式填入`enum RET {}`结构中，例如`HERO_NOT_EXIST = 250;//英雄数据不存在`
    3.  编译proto文件：在根目录下，执行`make protos`
    4.  错误码的使用：`ret.RET_HERO_NOT_EXIST`，错误返回案例：`return c.Error(msg, uint32(ret.RET_HERO_NOT_EXIST), fmt.Sprintf("hero not exist, uid:%v, id:%v", c.Msg.UID, cmsg.Id))`
2. **flush方法**
    1.  原理：获取玩家数据，再将clone后的数据返回给客户端
    2.  目的：防止数据变化
3. 发奖接口
  
4. xml整合	
