### 概述
- ngame后端组均在linux下使用vim开发，具体工具按照个人编码习惯自行配置，也可以使用组内推荐配置来搭建，见文档 [vim下golang开发环境搭建](http://gitlab.qdream.com/ngame-backend/server/blob/master/docs/vim下golang开发环境搭建.md)。
- 后端代码均通过git版本控制系统管理，如果对git不是很熟悉的，可参考 [git工作流](http://note.youdao.com/noteshare?id=7c970f5c5008ae43c9def8e36d14f9cd)

### 代码获取
- 后端代码由gitlab控制，由主程创建开发者账号，并分配账号权限。
- 后端开发人员需要在开发机上生成SSH密钥对，并且将密钥添加到gitlab上自己的账号下，以获取代码拉取权限。具体操作，可以参考gitlab用户设置页面的SSH Keys页签中的说明。
- 开发机环境配置
    - 在用户终端执行以下命令
    export PATH=$HOME/go/bin:$PATH
    export TMPDIR=$HOME/tmp
    mkdir ~/tmp
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
    go install golang.org/x/tools/cmd/goimports@latest
    - 配置gitlab的用户名和邮箱(与gitlab上一致),修改用户目录下的.gitconfig,新增
    [url "*********************:"]
      insteadOf = http://gitlab.qdream.com/

- Clone Code
    - 在gitlab上完成密钥配置后，可以在开发机上拉取代码，并保证时最新的代码
    ```
    <NAME_EMAIL>/ngame-backend/server.git
    git pull
    ```
    - 拉取到最新代码后，进入server目录```cd server```，并且执行```make init```，此时Makefile会调用脚本自动拉取配置表data和客户端协议proto/out对应的git库，```make init```只有在首次编译时使用，其他的```make```参数请查看Makefile源文件。

### 代码结构
- server目录结构如下
```
├── bin
├── config
├── data
├── deps
├── Dockerfile
├── docs
├── images
├── jenkins
├── log
├── Makefile
├── protos
│   ├── in
│   └── out
├── src
│   └── app
│       ├── doc.go
│       ├── go.mod
│       ├── go.sum
│       ├── logic
│       ├── world
│       ├── postman
│       ├── protos
│       ├── service
│       └── tools
├── tools
├── README.md
└── version
```
- 当前代码主要是分三个部分：
    - 前后端协议
        - server/protos/out
        - 前后端协议的git仓库是独立于游戏代码的，在首次编译时，执行```make init```后，协议就会被clone至server/protos/out目录下
    - 策划配置表
        - server/data
        - 策划数据表的git仓库是独立于游戏代码的，在首次编译时，执行```make init```后，协议就会被clone至server/data目录下
    - 后端代码和工具
        - deps - 代码依赖库
        - jenkins - 更新、重启服务器脚本
        - tools - 辅助功能性脚本
        - src/app - 后端主要逻辑代码, 包括：
            - battle - 战斗
            - character - 角色属性等
            - command - 协议处理逻辑
            - gateway - 游戏服和网关连接部分
            - goxml - 和策划表对应的表结构逻辑
            - db - 数据库操作部分
            - 等等

### 常用工具
- xmlc 
    - 通过策划配置的数据表生成一个配置读取代码文件至```server/app/logic/goxml```中，例如```knight_info.xml```生成的代码文件为```knight_info.go```，当我们需要对表数据进行预处理时，可以复制```knight_info.go```并重命名成```knight_info_manager.go```，```knight_info.go```只作为表结构定义。具体使用可以见```tools/build_xml.sh```，快捷操作为```make xml```。
    - 项目地址 [xmlc](http://gitlab.qdream.com/kit/xmlc)
- redisop
    - redisop是一个工具，它利用proto文件内的特殊注释，生成redis操作的go代码。
    - 项目内使用对应脚本为```tools/build_db_data.sh```，快捷操作为```make db```。
    - 项目地址和说明文档 [redisop](http://gitlab.qdream/kit/redisop)
- protoclone
    - protoclone主要用来生成proto内定义的数据结构的clone方法。
    - 项目内使用对应脚本为```tools/build_proto_go.sh```， 快捷操作为```make protos```。
    - 项目地址 [protoclone](http://gitlab.qdream.com/kit/protoclone)

### 服务器配置
- 跨服配置, 先配置etcd
  ```
  etcdctl --endpoints=*************:2379 put /game/x/world/node/wb/*************:3000X '{"redis" : "127.0.0.1:9898", "redis-index" : X, "modules":[1]}'
  ```
  - modules 代表该跨服开启的功能模块,从1开始
  - X代表自己的node-id，可以替换成自己的
  - wb 这里代表不同平台，内网开发代表自己的服务器，用自己的账号名称区分开就好

- 服务器的配置都在ectd，所以需要在etcd上注册新服务器，操作如下：
    ```
    etcdctl --endpoints=*************:2379 put /game/x/logic/node/*************:1000X '{
        "server-id": 100000000X,
        "app-id": 1,
        "op-group": 0,
        "node-id": X,
        "redis": "127.0.0.1:9898",
        "redis-index": X,
        "gateway-cluster": {
            "etcd": "*************:2379",
            "target": "/game/x/gateway/node/",
            "guard": ""
        },
		"world-cluster": {
		    "etcd": "*************:2379",
			"target": "/game/x/world/node/wb/"
		},
		"service": {
			"account-size": 20000,
			"online-size": 5000,
			"queue-pop-size": 10,
			"max-cache-entries": 5000,
			"user-save-interval": 50,
			"queue-size": 2000,
			"enable-trace-log": true,
			"ip-accounts": 100,
			"device-id-accounts": 100
		}

	}'
	```
    - ```server-id``` 最后一位X改成自己的id, 当前1-3都有人占了, 10是策划测试服
    - ```node-id``` 同```server-id```最后一位
    - ```redis-index``` 同```server-id```最后一位
	- ```world-clutster```里的```target```换成自己的，把里面的```wb```,换成自己制定的名字，然后修改config/world.xml你的target一致
    - 当前etcd版本为3.4.1
    - 这边只是举例说明，实际的详细可参考 [服务器文档](http://gitlab.qdream.com/ngame-backend/server/blob/master/README.md)
- 启动服务器
    ```
    cd server/bin
    启动
    ./cmd.sh start service -name=world -http=:3000X -grpc=:4000X -log_level=DEBUG -config=../config/world.xml
    ./cmd.sh start service -name=logic -http=:1000X -grpc=:2000X -log_level=DEBUG -config=../config/logic.xml
    关闭
    ./cmd.sh stop service -name=world -http=:3000X -grpc=:4000X -log_level=DEBUG
    ./cmd.sh stop service -name=logic -http=:1000X -grpc=:2000X -log_level=DEBUG
    重启
    ./cmd.sh restart service -name=world -http=:3000X -grpc=:4000X -log_level=DEBUG -config=../config/world.xml
    ./cmd.sh restart service -name=logic -http=:1000X -grpc=:2000X -log_level=DEBUG -config=../config/logic.xml
    ```
    - X 为每个人对应的唯一id（与上述etcd配置相同）
    - ```/home/<USER>/sX```为游戏内行为日志目录
    - 服务器日志在```server/log```下
- 客户端地址
    ```
    http://192.168.0.243:8666/long/
    ```
    - 服务器配置完成后，需要向客户端提供server-id和服务器名，更新客户端显示的服务器列表
    
### 开发流程说明
 - 日常开发主要在以下目录:
    - 对外协议 ```server/protos/out/```
    - 对内协议 ```server/protos/in/```
    - 模块逻辑 ```server/src/app/logic/character/```
    - 协议逻辑 ```server/src/app/logic/command/```
    - 机器人测试 ```server/src/app/tool/robot/```
- 具体的后端开发流程参考文档 [后端开发流程说明](http://gitlab.qdream.com/ngame-backend/server/blob/master/docs/业务开发流程.md)

### 注意事项
- 请阅读 [注意事项](http://gitlab.qdream.com/ngame-backend/server/blob/master/docs/注意事项.md)
