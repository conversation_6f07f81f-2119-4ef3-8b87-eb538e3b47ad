### 开发环境
- centos7.7 + vim8.2 + golang1.14
- 如果机器上未安装vim或golang，请自行安装
- 最新版本的vim-go需要vim版本8.0, 推荐使用最新版本的vim, 内网的开发环境vim已经是8.2版本
### vim配置
- 安装管理插件，此处推荐vim-plug，也可以已自行配置其他管理器插件
- 更多特性见：https://github.com/junegunn/vim-plug
```
curl -fLo ~/.vim/autoload/plug.vim --create-dirs \
    https://raw.githubusercontent.com/junegunn/vim-plug/master/plug.vim
	* 如果无法下载直接去gitlab的doc目录上面下载
```
- vim-plug安装完成后，创建.vimrc配置文件
```
touch ~/.vimrc
```
- 在.vimrc中配置需要的插件并保存
```
call plug#begin('~/.vim/plugged')

"vim-go插件
Plug 'fatih/vim-go', { 'do': ':GoInstallBinaries' }
"跳转插件,这个先注释，会有冲突
"Plug 'dgryski/vim-godef'
"显示配色
Plug 'crusoexia/vim-monokai'

call plug#end()
```
- 在vim命令模式下安装插件
```
:PlugInstall
```
- 在.vimrc下继续添加一下内容，部分配置可根据个人习惯调整
``` 
"关闭vi的兼容
set nocompatible

filetype off                  " required
filetype plugin indent on

syntax on
"filetype plugin indent on
syntax enable
colorscheme monokai

"设置backspace生效
set backspace=indent,eol,start

"设置行号
set number
"设置tab缩进
set tabstop=4
"这个可以控制换行的时候自动缩进的举例
set shiftwidth=4
"查找高亮
set hlsearch


"vim内部编码
set encoding=utf-8
"vim编辑文件编码
set fileencoding=utf-8
"打开文件编码集合
set fileencodings=ucs-bom,utf-8,cp936,gb18030,big5,euc-jp,euc-kr,latin1

"golang语法高亮
au BufRead,BufNewFile *.go set filetype=go

let g:go_version_warning = 0
let g:go_disable_autoinstall = 0
let g:go_highlight_functions = 1
let g:go_highlight_methods = 1
let g:go_highlight_structs = 1
let g:go_highlight_operators = 1
let g:go_highlight_build_constraints = 1

```
- 配色设置
- 上述安装过程中，已将monokai配色下载至～/.vim/plugged/vim-monokai中
- 手动创建 ~/.vim/colors/目录，并且移动monokai.vim 至colors中
- 测试下来这个配色效果没有生效...
```
mkdir -p ~/.vim/colors/
cp ~/.vim/plugged/vim-monokai/colors/monokai.vim ~/.vim/colors

```
- 在.vimrc中设置配色
```
colorscheme monokai
```
### 最后
- 基本配置到此结束，可以实现golang语法高亮显示，保存文件时自动格式化，以及跳转查看函数和变量定义(光标移动至需要查看的函数或变量，按gd跳转)
