## 环境搭建步骤详细

```
该文档暂时不是最新的，请看新人指引文档
```
1. 安装go `主程负责`
2. yum install -y zlib-devel clang.x86_64(clang-format协议用的) `主程负责`
3. !配置/etc/bashrc `主程负责`
	~~~
	export PATH=/usr/local/go/bin:$PATH
	export GO111MODULE=on
	export GOPROXY=https://goproxy.cn,direct
	~~~
4. 配置~/.bashrc
	~~~
	export PATH=$HOME/go/bin:$PATH
	export TMPDIR=$HOME/tmp
	export ETCDCTL_API=3
	~~~
5. go get golang.org/x/tools/cmd/goimports
6. 安装golangci-lint，还不知道怎么安装。。。
7. 下载server代码，先申请开通git权限
	~~~
	1. mkdir ngame //可以自己改
	2. git clone http://*************:backend/server.git
	3. cd server
	4. 手动拉取下gateway的代码
		1. cd src
		2. mkdir kit
		3. cd kit
		4. git clone http://*************:backend/gateway.git
	5. make go//编译全部
	~~~
8. 安装redisLV `主程负责`
9. 启动redisLV,内网大家用不同的db index就可以了 `主程负责`
10. etcd启动 `主程负责`
  ~~~
  etcd --listen-client-urls http://*************:2379 --advertise-client-urls http://*************:2379
      * ************* 修改为目标服务器ip
  ~~~
11. 配置gateway `主程配置`
  ~~~
  etcdctl --endpoints=*************:2379 put /game/x/gateway/node/*************:38422 '{"id": 1, "weight" : 1, "domain" : "*************", "port": "38422"}'
      * id: gateway service id `最大值20, 21~31为保留段`
      * weight: 服务权重
      * domain: 服务器域名
      * port: 对外的监听端口
      * /game/x/gateway/node/*************:38422 修改为目标网关ip与client port : /game/x/gateway/node/ip:port
  ~~~
12. 配置logic(游戏服务)
  ~~~
  etcdctl --endpoints=*************:2379 put /game/x/logic/node/*************:10001 '{"game-id" : 1, "app-id" : 0, "platform-id": 0, "node-id": 1, "redis": "127.0.0.1:9898", "redis-index": 1, "gateway-cluster": {"etcd": "*************:2379", "target": "/game/x/gateway/node/"}, "service":{"online-size": 5000, "queue-pop-size": 50}}'
       * *************:10001 修改为logic配置HTTP服务器地址 (每个Logic对应一个唯一的Address)
       * game-id：fmt.Sprintf("%d%04d%05d", app-id, platform-id, node-id) (研发配置同node-id一样就行)
       * node-id: 区服id，保证唯一，最大值32767(15bit)
       * service: 服务器相关配置。online-size:服务器最高在线人数，queue-pop-size:每次最多从排队队列中取多少位登陆
	   (个人自己修改：10001的端口号改为自己的, redis-index改为自己的, 都往上递增)
  ~~~
13. 启动游戏服
	~~~
	1. cd server
	2. 修改配置config/logic.xml
		1. 修改etcd的配置相关IP地址
	3. ./cmd.sh start service -name=logic -http=:10001 -grpc=:20001 (端口号修改为自己的，往上递增就好了，和上面的logic etcd配置一致)
	4. 启动完成后在server/log下面有logic.log日志，查看该日志确认是否启动完成。
	~~~
14. 关闭游戏服
	1. ./cmd.sh stop service -name=logic -http=:10001 -grpc=:20001
15. 后续本地开发，在server目录下修改protos和data, 然后make app就可以编译测试。后续proots单独提交 


