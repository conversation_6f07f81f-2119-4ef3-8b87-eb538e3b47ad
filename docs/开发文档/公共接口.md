## 公共接口

1. 通用排行榜

   游戏类的所有排行榜的生成，先让策划配置ranking_info表。然后修改相应代码

   - 获取排行榜的通用接口

     ```go
     message C2L_GetCommonRank {
       uint32 rank_id = 1;  //排行榜id,特殊的排行榜可以自己在接口里特殊实现，比如竞技场的，通用的不用修改接口，值需要添加自己的排行榜对象就可以了
     }
     
     message RankValue {
       uint64 value = 1;  //对应排名数值
       UserSnapshot user = 2; 
       uint64 param1 = 3; 
       uint32 param2 = 4; 
       uint32 param3 = 5; 
     }
     
     message L2C_GetCommonRank {
       uint32 ret = 1; 
       uint32 rank_id = 2; 
       repeated RankValue list = 3; 
       uint32 self_rank = 4;
       RankValue self_value = 5;
     }
     ```

     

   - app/goxml/ranking_info_const.go里添加各个排行榜的id常量

     ```go
     //这里和ranking_info表里配置的id一致
     const (
         PowerRankId       uint32 = 1 //战力排行榜
         DungeonRankId     uint32 = 2 //主线排行榜
         TowerRankId       uint32 = 3 //爬塔竞技场
         CommonArenaRankId uint32 = 4 //竞技场排行榜
         Hero1RankId       uint32 = 5 //光种族应英雄战力排行榜
         Hero2RankId       uint32 = 6 //暗种族应英雄战力排行榜
         Hero3RankId       uint32 = 7 //帝国种族应英雄战力排行榜
         Hero4RankId       uint32 = 8 //绿种族应英雄战力排行榜
         Hero5RankId       uint32 = 9 //月种族应英雄战力排行榜
     )
     
     ```

     

   - logic/service/register_common_rank.go里注册排行榜

     ```go
     package service
     
     import (
         "app/goxml"
         "app/logic/rank"
         "fmt"
     )
     
     //这里要修改 排行榜id和对应的接口对象
     var gRankMethod = map[uint32]rank.CommonRankValuer{
         goxml.PowerRankId:       &rank.UserPower{}, //
         goxml.DungeonRankId:     &rank.UserDungeon{},
         goxml.TowerRankId:       &rank.UserDungeon{},
         goxml.CommonArenaRankId: &rank.UserDungeon{},
         goxml.Hero1RankId:       &rank.UserDungeon{},
         goxml.Hero2RankId:       &rank.UserDungeon{},
         goxml.Hero3RankId:       &rank.UserDungeon{},
         goxml.Hero4RankId:       &rank.UserDungeon{},
         goxml.Hero5RankId:       &rank.UserDungeon{},
     }
     
     //这里有时候要修改，特殊类分数会降的排行榜修改在总数量的基础上额外增加一些排行榜数量
     var gRankAddNum = map[uint32]uint32{
         goxml.Hero1RankId: 100, //分数有可能下降的要多预留一些数量
         goxml.Hero2RankId: 100, //分数有可能下降的要多预留一些数量
         goxml.Hero3RankId: 100, //分数有可能下降的要多预留一些数量
         goxml.Hero4RankId: 100, //分数有可能下降的要多预留一些数量
         goxml.Hero5RankId: 100, //分数有可能下降的要多预留一些数量
     }
     
     ```

     rank包修改，添加common_rank_xxx.go

     ```go
     //logic/rank/common_rank_dungeon.go
     package rank
     
     import (
         "app/protos/in/db"
         "app/protos/out/cl"
         "github.com/gogo/protobuf/proto"
         //l4g "github.com/ivanabc/log4go"
     )
     
     type UserDungeon struct {
         db.RankDungeon
     }
     
     //UserDungeon 必须要实现的几个接口
     type CommonRankValuer interface {
         CmpScore(interface{}, interface{}) int //分数比较的接口（不包含key)，比较的大小如果符合预期想要的左边大于右边或者左边小于右边，返回-1，否则返回1
         CmpKey(interface{}, interface{}) int //分数相同的情况下，比较key,比较的时候符合预期的返回-1
         Key() uint64 //获取key 一般是uid
         ShowValue() *cl.RankValue     //排行榜显示用的,一般值需要填充value，有的有额外的，请自己编写。
         NewElement() CommonRankValuer //初始化一个自己出来， 就是初始化一个空的接口提出来，方便Load使用
         Load([]byte) error            //从db中加载数据
         Save() []byte                 //保存数据， 序列化接口，一般是直接序列化里面的protobuf对象
     }
     
     ```

     在db里添加排行榜要落地的数据接口

     ```protobuf
     //主线排行榜
     //protos/in/db.proto
     message RankDungeon {
       uint64 uid = 1;
       uint32 dungeon_id = 2; //主线id
       int64 tm = 3;  //时间
     }
     ```

2. 战斗接口

   所有的业务的战斗接口，都新建一个，放在character/battle.go里，参照里面的例子，注意修改自己的阵容id


   

3. 有可能离线玩家战斗接口

   - 战斗前先判断玩家是否在线，如果在线直接用对方的User.
   - 如果不在线参照C2L_GetUserBattleData接口，具体实现在logic/common/user/common.go里,战斗的逻辑

   ```go
   for _, v := range requestUids {
                   if snapm[v] == nil {
                       l4g.Error("user: %d C2LGetUserBattleDataCommand: get battle data nil:%d",
                           cbuid, v)
                       smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
                       cbuser.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, smsg)
                       return true
                   }   
                   newUser := character.GetUserFromUserBattleData(snapm[v])
       //这个newUser就是对手的User对象你，后面就可以调用这个newUser来和在线的一样处理了
                   smsg.Users = append(smsg.Users, newUser.FlushBattleData(formationId))
               }   
   
   ```

4. 通用战斗存储

   当业务逻辑理需要存储战报的时候，在对应的业务战斗结束后调用


   调用完成后，自己维护自己的战报列表，里面只需要引用战报id就ok

   前端访问的时候是先打开战报的列表，包括每个业务自己要显示的信息，然后查看战报的时候，调用通用接口。

   ```protobuf
   message C2L_GetBattleReport { uint64 id = 1; }
   ```

   如果战报时间过期了，每个业务需要手动调用删除战斗的接口(记得一定要过期删除)

   ```go
   character.DelBattleReport(srv servicer, ids []uint64)
   //ids 要删除的战报id,支持批量删除
   ```

5. 拉取玩家阵容的接口

   获取玩家指定玩法阵容的接口

   C2L_GetUserBattleData

6. 通用http请求。

   在command需要请求第三方的http服务获取数据的时候。调用的接口

   - 接口说明

     ```go
     //url 要访问的url
     //method 支持三种方式get post 和postjon(传递的是json格式数据，利用url.Values，设置一个json的filed)
     //handler，回调函数，需要实现 ProcHttpResponse(response *http.Response, err error)方法
     func (h *HTTPManager) SendHttpRequest(url string, method uint32, data url.Values, handler HttpResponseHandler) {}
     ```

     

   - 举例

     ```go
     func (c *C2TestCommand) Execute(ctx context.Context) bool {
         cmsg := &cl.C2L_Test{}
         if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
             l4g.Error("C2L_Test Unmarshal error: %d %s", c.Msg.UID, err)
             return false
         }   
     
         l4g.Debug("user: %d C2L_Test: %s", c.Msg.UID, cmsg)
     
         data := url.Values{} //参数全部用url.Values的方式传递，如果是postJosn的方式
         data.Set("name", "中国")
         data.Set("age", "1=8")
         request := &GetRequest{
             user: c.User,
         }   
         c.Srv.GetHTTPManager().SendHttpRequest("http://httpbin.org/get", helper.HTTP_POST, data, request)
     }
     
     type GetRequest struct {
         user *character.User
     }
     
     func (r *GetRequest) ProcHttpResponse(response *http.Response, err error) {
         smsg := &cl.L2C_BattleTest{
             Ret: uint32(ret.RET_OK),
             //Report: data,
         }
         if err == nil {
             data, _ := ioutil.ReadAll(response.Body)
             l4g.Debug("get real data %s", data)
             smsg.Ret = uint32(ret.RET_OK)
         } else {
             l4g.Error("get data err %s", err)
             smsg.Ret = uint32(ret.RET_ERROR)
         }
     
         r.user.SendCmdToGateway(cl.ID_MSG_L2C_BattleTest, smsg)
     }
     ```

   

7. 通用次数处理接口

   游戏业务中，对于某一操作有次数限制的情况下会用到此接口。

   目前使用到的业务：挂机加速 爬塔扫荡 竞技场挑战/门票购买 竞技场点赞 金币本次数 经验本次数 英雄本次数 宝石本次数 纹章本次数 悬赏任务 个人boss每日免费次数 幽暗密林每日种树次数 幽暗密林每日抢夺次数 公会副本每日攻打次数 列传强敌副本每日挑战次数 功勋每日奖励

   根据自己的业务的次数需求在number_type_info.xml表中添加对应的信息

   ```xml
    <!--type=int:购买类型 free_num=int:每日免费次数 refresh_type=int:刷新类型 cost_limit=int:参与消耗上限 cost_type=int:参与消耗类型 cost_value=int:参与消耗值 cost_count=int:参与消耗数量 buy_type=int:购买类型 buy_cost_type=int:购买消耗类型 buy_cost_value=int:购买消耗值 buy_group=int:购买消耗数量组 buy_limit=int:每日购买上限 -->
     
    <root>
       <data type="1" free_num="1" refresh_type="1" cost_limit="0" cost_type="0" cost_value="0" cost_count="0" buy_type="1" buy_cost_type="2" buy_cost_value="0" buy_group="1" buy_limit="3" />
    </root>
    
    1. type:  每种次数的唯一type类型
    2. free_num:  免费使用次数                                           如： 竞技场的每日免费次数、高级抽卡的每日免费次数、
    3. refresh_type:  0-不刷新; 1-日刷新;
    4. cost_limit： 0-不限制使用资源的次数
    5. cost_type、cost_value、cost_count:  分别为消耗资源时的类型、值、数量       如：竞技场消耗的挑战券，高级抽卡消耗的道具，积分抽卡消耗的积分
    6. buy_type:  0-不能购买; 1-使用时购买; 2-单独购买                    如：  爬塔扫荡即配置1，高级抽卡消耗钻石即配置1，竞技场挑战即配置0
    7. buy_limit： 0-不限制购买次数
    8. buy_cost_type、buy_cost_value、buy_group:  分别为购买次数消耗的资源类型、资源值以及索引到buy_price_info.xml表的价格group      如： 爬塔购买扫荡次数消耗的钻石、高级抽卡次数购买消耗的钻石
      
    注：若5和8都配置，则会优先使用5的资源，5不够的情况下且8的buy_type为使用时够买，不够的次数就会消耗8来购买
   ```

   注： 这些不同type的次数信息会在登录时通过user的num_info字段传给前端， 之后的次数变化会推送给前端， 业务接口协议中不需要考虑次数的刷新


    - 接口说明
     
     在业务中使用number_type_info.xml对应的次数类型时，会检查次数是否足够，以及是否需要消耗，
     直接调用CheckNumByType()接口，对于接口的返回，
     统一处理方式：
        先判断 uint32 == RET_OK, 等于则继续，不等于直接返回前端错误码
        然后判断 []*cl.Resource == nil, 等于则代码继续执行，无需做其它处理，不等于就消耗掉[]*cl.Resource中的资源
        
     注： 对于不同的type是否可以购买、是否消耗道具、是否道具和购买都可以等情况，CheckNumByType()会按配置分类处理，按统一方式处理接口返回即可
     ```go
     //根据玩法类型与消耗次数，检查次数或资源是否满足
     //@param	uint32 numType	玩法类型
     //@param	uint32 num		要使用的次数
     //@return	uint32			状态码
     //@return	[]*cl.Resource	检查到需要的消耗
     //@return	*cl.NumInfo		需要消耗的次数统计信息（用于AddNumByType接口使用）
     func (u *User) CheckNumByType(numType, num uint32) (uint32, []*cl.Resource, *cl.NumInfo) {}
     ```
     
     在使用完次数后需要对玩家数据进行修改，调用 AddNumByType（）
     注：对于不同的type是否只修改使用次数、是否使用次数和购买次数都修改的情况，CheckNumByType()会按配置分类处理
     ```go
     //根据玩法类型，更新次数相关统计数据
     //@param uint32 numType 	玩法类型
     //@param uint32 numSummary 待添加的次数统计数据
     func (u *User) AddNumByType(numType uint32, numSummary *cl.NumInfo) {}
     ```

8. 通用阵容战力更新接口

   游戏业务中，会影响到阵容战力的操作，需要调用此接口。

   不同的更新情况分为不同的type
   注： 后续有新的更新类型，需要增加type并在UpdateFormationsPowerByType中添加对应的操作

   ```go
    //更新阵容战力类型
    const (
       FormationPowerUpdateByHero       uint32 = 1 // 英雄变化，改变阵容战力
       FormationPowerUpdateByGlobalAttr uint32 = 2 // 全局属性变化，改变阵容战力
       FormationPowerUpdateByIds        uint32 = 3 // 根据阵容id, 改变阵容战力 --- 暂未使用
    )   
   ```

   调用接口时传入对应的type和数据， 给前端推送战力已在此接口处理
   注： 后续有新开发的阵容，需要在此接口中添加新阵容的处理

   ```go
   // updateType    更新阵容战力的type类型
   // id          更新需要的数据，   type类型为英雄的话，id为英雄id； type类型为全局属性的话， id为nil
   func (fm *FormationM) UpdateFormationsPowerByType(updateType uint32, id interface{}) {
        var needFormationIds []uint32

    	switch updateType {
    	case FormationPowerUpdateByHero: //  英雄需要修改的阵容
    		heroIds, ok := id.([]uint64)
    		if ok {
    			needFormationIds = append(needFormationIds, fm.u.GetFormationIdsByHeroes(heroIds)...)
    		}
    	case FormationPowerUpdateByGlobalAttr: // 全局属性需要修改的阵容
    		needFormationIds = goxml.FormationInfoM.GetNonemptyFids()
    	case FormationPowerUpdateByIds: // 阵容id
    		ids, ok := id.([]uint32)
    		if ok {
    			needFormationIds = ids
    		}
    
    	default:
    		l4g.Errorf("user: %d update formation power updateType %d  error", fm.u.ID(), updateType)
    		return
    	}
    
    	if len(needFormationIds) > 0 {
    		fm.updateFormationsPower(srv, needFormationIds)
    	}
   }
   ```

   目前使用到的业务： 英雄的相关养成，神器的相关养成， 装备的相关养成

   例： c.User.FormationManager().UpdateFormationsPowerByType(character.FormationPowerUpdateByHero, hero.Data.Id)
        c.User.FormationManager().UpdateFormationsPowerByType(character.FormationPowerUpdateByGlobalAttr, nil)
        c.User.FormationManager().UpdateFormationsPowerByType(character.FormationPowerUpdateByArtifact, artifact.Data.SysId)

9. 在一组数据中，去重随机指定个数的数据

   根据权重在x条数据中，去重随机出y条时（y<=x）,可以使用本接口。具体使用发放，可参考goxml.ShopRandomGoodsInfoM.Generate

   ```go
   /**
    在一组数据中，去重随机指定个数的数据
    @param rd *rand.Rand
    @param num int 要随机得到的数据个数
    @param totalWeight int 待随机的数据组的总权重
    @param data IRandom
    @return []interface{} 一组随机结果
    @return error
    */
    func RandomSomeNoRepeat(rd *rand.Rand, num, totalWeight int, data IRandom) ([]interface{}, error) {
        if num == 0 || totalWeight == 0 || data == nil {
            l4g.Error("RandomSomeNoRepeat: param error. %d %d %v", num, totalWeight, data)
            return nil, errors.New("check param failed")
        }   
    
        infoLen := data.GetLen()
        if num > infoLen {
            l4g.Error("RandomSomeNoRepeat: wrong num. %d %d", num, infoLen)
            return nil, errors.New("wrong param: num")
        }   
    
        l4g.Info("num->%d, totalWeight->%d, len->%d\n", num, totalWeight, infoLen)
        list := make([]interface{}, 0, num)
        for i := 0; i < num; i++ {
            rNum := rd.RandBetween(1, totalWeight)
            for j := 0; j < infoLen-i; j++ {
                weight := data.GetWeight(j)
                if rNum <= weight {
                    item := data.GetItem(j)
                    list = append(list, item)
                    data.Switch(j, infoLen-i-1)
                    totalWeight -= weight
                    break
                } else {
                    rNum -= weight
                }   
            }   
        }   
        return list, nil 
    }
   ```

10. 计算上一次每日重置时间

    根据每日重置时间点，计算上一次重置时间

    ```go
    /**
    计算上一次每日重置时间
    @param tm int 每日重置的时间点，单位是秒 - 如：凌晨1点，即3600
    @return int64
    */
    func CalcLastDailyResetTime(tm uint32) int64 {
        resetTm := int64(util.DailyZero() + tm)
        if time.Now().Unix() < resetTm {
            resetTm -= util.DaySecs //未到重置时间点，返回前一天的时间点
        }
        return resetTm
    }
    ```

11. 红点接口 - C2L_FlushRedPoint

    使用场景： 玩家登录时，前端把无法判断的红点，传给后端，后端判断后，把需要显示的红点传给前端

    后端红点增加步骤：

    - 将red_point_info.xml表中要增加的红点ID，设置常量

      ```protobuf
      common.proto
      
      // 红点
      enum RED_POINT {
        RP_NONE = 0;
        GUILD_TODAY_SIGN_IN = 10111;
        GUILd_DUNGEON_CHAPTER_AWARD = 10132;
        GUILD_DUNGEON_CHAPTER_CHANGE = 10133;
        GUILD_TALENT_LEVEL_CAN_UP = 10141;
        GUILD_HAVE_APPLY = 10120;
        ...
      }
      ```

    - 在redpoint/command.go的checkRedPoint函数中增加对应的case 和 检查方法

      ```go
      //根据id检查红点是否需要显示
      func checkRedPoint(srv command.Servicer, user *character.User, ID common.RED_POINT) bool {
      	switch ID {
      	case common.RED_POINT_GUILD_TODAY_SIGN_IN:
      		return checkGuildSignIn(user, srv)
      	case common.RED_POINT_GUILd_DUNGEON_CHAPTER_AWARD:
      		return checkGuildDungeonChapterAward(user, srv)
        ...
      }
      ```

    - 普通的检查方法放到character/red_point.go中，如：材料本，activity中的数据做红点判断的，可以直接放到redpoint/command.go中，如：公会