# 数数对接工作引导

## 1.业务架构

![](ta_adapter_architecture.png)

## 2.环境准备

#### 2.1. filebeat

- 软件地址：`/home/<USER>/filebeat/filebeat-7.14.1-linux-x86_64.tar.gz`

- 配置文件地址：`/home/<USER>/filebeat/filebeat-7.14.1-linux-x86_64/filebeat.yml`

- 修改配置文件

  ```yaml
  paths:
      - /home/<USER>/go/server/log/dc.log
      - /home/<USER>/go/server/log/resource.log
  
  paths:
        - /home/<USER>/go/server/log/logic.log
        
  #将以上路径改为自己服务器的日志路径
  ```

- 启动filebeat

  ```shell
  ./filebeat -e -c filebeat.yml
  ```

#### 2.2. adapter配置

- 配置文件地址：`项目根目录/src/app/tools/adapter/config`

- 修改配置文件

  ```yaml
  log: ./logs/adapter.log
  mode: server
  
  restful:
   addr: :40006 ###这里需要不同的端口，避免因端口占用，而无法启动(建议使用端口：长风-40005，十二-40006，艾斯-40007，先儿-40008)
   mode: debug
  
  kafka:
   brokers: 192.168.0.230:9092
   consumer_ticker: "10s"
   version: "2.4.0"
   consumer_fetch_size: 4096
   consumer_fetch_min_size: 100 
   consumer_net_read_timeout: 5
   topics: "log-ngame"
   start_partition: 0
   end_partition: 2
  
  leveldb:
   dir: ./data
   cache_size: 1024
   block_size: 32
   write_buffer_size: 64
   max_open_files: 500 
   
  thinkingdata:
   log_dir: /data/taLog/wzw/ ###将wzw改为自己名字首字母(长风-wb，艾斯-wj，先儿-hmx)
   batch_size: 50
  
  wmdata:
   log_dir: ./wm_logs/
  ```

- 启动adapter

  ```shell
  ./cmd.sh start adapter
  ```

## 3.adapter业务开发

#### 3.1. 注册message - 以爬塔业务为例

- 文件地址：`项目根目录/src/app/tools/adapter/service/logger/message.go`

- 修改文件：

  ```go
  package logger
  
  import (
      marena "adapter/service/message/arena"
      mbase "adapter/service/message/base"
      mdungeon "adapter/service/message/dungeon"
      mforest "adapter/service/message/forest"
      mhero "adapter/service/message/hero"
      mmail "adapter/service/message/mail"
      mmirage "adapter/service/message/mirage"
      mrankachieve "adapter/service/message/rankachieve"
      mresource "adapter/service/message/resource"
      mshop "adapter/service/message/shop"
      mtower "adapter/service/message/tower" //---新增---
  )
  
  func (l *Logger) RegisterMessages() {
      marena.Init(l.msgMgr)
      mbase.Init(l.msgMgr)
      mdungeon.Init(l.msgMgr)
      mforest.Init(l.msgMgr)
      mhero.Init(l.msgMgr)
      mmail.Init(l.msgMgr)
      mmirage.Init(l.msgMgr)
      mrankachieve.Init(l.msgMgr)
      mresource.Init(l.msgMgr)
      mshop.Init(l.msgMgr)
      mtower.Init(l.msgMgr) //---新增---
   }
  ```

#### 3.2. 添加业务处理文件 - 以爬塔业务为例

- 业务文件目录：`项目根目录/src/app/tools/adapter/service/message/`

- 创建文件

  ```shell
  mkdir tower
  cd tower
  vim message.go
  ```

- 业务代码

  ```go
  package tower
  
  import (
      "adapter/service/helper"
      sub "adapter/service/proto/log"
  
      "gitlab.qdream.com/platform/proto/da"
  )
  
  func Init(msgMgr *helper.MessageM) {
      msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TOWER_FIGHT), //爬塔 - 战斗
          &TowerFightMessage{}, "tower_fight", 12, 0, 0, 4)
      msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TOWER_SWEEP), //爬塔 - 扫荡
          &TowerSweepMessage{}, "tower_sweep", 11, 0, 0, 3)
  }
  
  type TowerFightMessage struct {
      helper.BaseMessage
  }
  
  func (m *TowerFightMessage) Execute(log *da.Log) bool {
      r := m.GetRecorder()
      r.GetLogicProperties(log)
      win := helper.FormatBool(log.Param12) 
      r.SetTrack("tower_type", log.Param10) //这里的各个param与项目的character/log.go要对应上
      r.SetTrack("tower_floor", log.Param11)
      r.SetTrack("win", win)
      if win {
          r.SetAfter("tower_floor", log.Param11)
      }
      ret := r.Write(log)
      m.PutRecorder(r)
      return ret
  }
  
  type TowerSweepMessage struct {
      helper.BaseMessage
  }
  
  func (m *TowerSweepMessage) Execute(log *da.Log) bool {
      r := m.GetRecorder()
      r.GetLogicProperties(log)
      r.SetTrack("tower_type", log.Param10)
      r.SetTrack("tower_floor", log.Param11)
      ret := r.Write(log)
      m.PutRecorder(r)
      return ret
  }
  ```

#### 3.3. 编译重启adapter

- 编译，在adapter根目录下执行

  ```shell
  make
  ```

- 重启

  ```shell
  ./cmd.sh restart adapter
  ```

## 4.adapter测试

- 转换后的日志目录：日志会生成至，adapter配置文件`项目根目录/src/app/tools/adapter/config`所设定的地址

  ```yaml
  thinkingdata:
   log_dir: /data/taLog/wzw/ ###将wzw改为自己名字首字母(长风-wb，艾斯-wj，先儿-hmx)
   batch_size: 50
  ```

- 核对日志内容是否与业务代码吻合

  ```json
  {"#account_id":"**********_****************","#type":"track","#time":"2021-10-08 14:53:56","#event_name":"tower_fight","#ip":"**************","properties":{"#lib":"    Golang","#lib_version":"1.2.0","#zone_offset":8,"account_id":"loba50","channel":"1","context_id":****************,"device_id":"device-id:**********","level":89,"pow    er_value":57398,"server_id":"**********","tower_floor":4,"tower_type":1,"user_id":"****************","vip_level":0,"win":true}}
  
  {"#account_id":"**********_****************","#type":"track","#time":"2021-10-08 14:54:15","#event_name":"tower_sweep","#ip":"**************","properties":{"#lib":"    Golang","#lib_version":"1.2.0","#zone_offset":8,"account_id":"loba50","channel":"1","context_id":****************,"device_id":"device-id:**********","level":89,"pow    er_value":57398,"server_id":"**********","tower_floor":4,"tower_type":1,"user_id":"****************","vip_level":0}}
  ```

## 5.与运营的协作

![](ta_adapter_cooperate.png)

- 使用飞书共享文档
- 属性类型，定义后不能修改
- 将已完成的业务，及时更新至此协作文档
- 运营同事，将通过此文档来验收和确认日志对接工作

## 6.数数后台

#### 6.1. 维度表

- 翻译日志中的自定义内容，方便查看统计
  - 例如：我们资源类型中的type-value-count，type为2代表钻石，type为3代表金币...
- 日志转换的过程中，注意收集这里数据，形成key-value结构的维度表