# ctx
    
## 概述

 ctx模块对标准库中的context进行了简单的封装，加了Wait接口用于等待子协程的退出



## 接口说明

#### func (g *Group) CreateChild() *Group
创建子Group

#### func (g *Group) Finish()
通知父Group自己已退出

#### func (g *Group) Stop()
通知子Group退出

#### (g *Group) Wait()
等待子Group退出完成

## 示例

``` go
 
func startG(group *Group) {
	go func() {
		select {
		case <- time.After(2*time.Second):
			panic("timeout")
		case <-group.Done():
		}
		group.Stop()
		group.Wait()
		group.Finish()
	}()
}

func TestGroup(t *testing.T) {
	bg := NewGroup(context.Background())

	startG(bg.CreateChild())
	startG(bg.CreateChild())

	time.Sleep(1*time.Second)

	bg.Stop()
	bg.Wait()
}

```
