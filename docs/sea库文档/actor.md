# Actor

## 概述

actor封装了一个协程，主要用来循环处理消息（调用消息的Process接口）。

``` go
type Receiver interface{}

type Messager interface {
	Process(Receiver) error
}

type Actor struct {
	id       uint64 //唯一id
	kind     uint32
	kindName string
	msgQ     chan Messager   // => 消息队列。用来接收消息，并循环处理
	eventM   *event.Manager  // => 事件管理器
	ticker   *time.Ticker    
	TickerCB func(int64)    
	timerM   *timer.Manager  // => 定时器管理器

	ctx *ctx.Group
}
```

``` go
func (a *Actor) Run(param Receiver) {
    for {
		select {
		case msg := <-a.msgQ:        // => 循环从channel里面取消息并处理
			if err := msg.Process(param); err != nil {
				l4g.Error("[actor] msg process failed: %+v %s", msg, err)
			}
		case now := <-a.ticker.C:   // => 定时处理timerM, 并调用TickerCB
			a.oneTicker(now.Unix())
		case <-a.ctx.Done():
			l4g.Info("[actor] chan close....(%d-%s)", a.id, a.kindName)
			return
		}
		a.eventM.Process(param)
	}
}
```

## 接口说明

 ### func (a *Actor) MsgQSize() uint32
 当前队列里面等待处理的消息数量

### func (a *Actor) AddMessage(msg Messager) bool
 队列里面添加消息（如果满了会阻塞）

### func (a *Actor) AddEvent(evt event.Eventer) bool 
 事件管理器添加事件

 ### func (a *Actor) WatchEvent(kind uint32, fn *event.Callback)
 事件管理器监听某个类型的事件

### func (a *Actor) AddTimer(i timer.TimeOuter, e int64, iv int64) (uint32, bool)
定时器管理器添加一个定时任务

## 示例

``` go

type MessageTest struct {
	Id uint64
}

func (mt *MessageTest) Process(r Receiver) error {
	t, ok := r.(*testing.T)
	if !ok || t == nil {
		panic("receiver error")
	}
	t.Logf("msg:%d process", mt.Id)
	return nil
}

func TestActor(t *testing.T) {
	cfg := &Config{
		Kind:      1,
		KindName:  "test",
		MsgQSize:  10000,
		Rate:      500,
		TimerSize: 1024,
	}
	bg := ctx.NewGroup(context.Background())
	act := NewActor(bg.CreateChild(), cfg)
	go act.Run(t)
	for i := 0; i < 10; i++ {
		m := &MessageTest{Id: uint64(i)}
		act.AddMessage(m)
		time.Sleep(time.Millisecond * 1)
	}

	bg.Stop()
	bg.Wait()
	t.Log("test finished")
}

```

