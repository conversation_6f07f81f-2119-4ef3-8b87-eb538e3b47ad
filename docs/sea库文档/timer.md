# Timer

## 概述

timer模块是定时器管理器。 基于优先级队列(heap)实现， 根据结束时间排序。 

_如果对heap不熟悉，推荐先看看标准库 container/heap_

## 接口说明

### func (m *Manager) AddTimer(i TimeOuter, e int64, iv int64) uint32
添加一个定时器， e: timeout时间， iv: 循环间隔

### func (m *Manager) RemoveTimer(id uint32) 
移除指定id的定时器

### func (m *Manager) Run(now int64, limit int) 
循环 从优先级队列顶部取超时的定时器并且弹出，并调用这些超时定时器的超时处理函数

## 示例

``` go

type MessageTest struct {
	Id uint64
}

func (mt *MessageTest) Process(r Receiver) error {
	t, ok := r.(*testing.T)
	if !ok || t == nil {
		panic("receiver error")
	}
	t.Logf("msg:%d process", mt.Id)
	return nil
}

func TestActor(t *testing.T) {
	cfg := &Config{
		Kind:      1,
		KindName:  "test",
		MsgQSize:  10000,
		Rate:      500,
		TimerSize: 1024,
	}
	bg := ctx.NewGroup(context.Background())
	act := NewActor(bg.CreateChild(), cfg)
	go act.Run(t)
	for i := 0; i < 10; i++ {
		m := &MessageTest{Id: uint64(i)}
		act.AddMessage(m)
		time.Sleep(time.Millisecond * 1)
	}

	bg.Stop()
	bg.Wait()
	t.Log("test finished")
}

```
