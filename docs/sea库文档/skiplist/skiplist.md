## skiplist

### 概述
 
    skiplist跳表

### struct 简介

1. Level是 节点（Node）每层的数据
``` go 
type Level struct {
	forward *Node     // 下一个节点
	span    uint32    // 距离下一个节点的距离
}
```

2. Node是链表（SkipList）的每一个节点
``` go
type Node struct {
	value    interface{}   //节点存储的值
	backward *Node         //上一个节点
	level    []Level       //节点的层级数据
}
```

3. SkipList 主体结构
``` go
type SkipList struct {
	head, tail    *Node     // 头和尾节点
	length, level uint32    // 长度和高度
	Comparatorer            // 比较函数
}
```

4. Comparatoer 元素比较接口。 
``` go
type Comparatorer interface {
	CmpScore(interface{}, interface{}) int
	CmpKey(interface{}, interface{}) int
}

    先比较CmpScore, 相等就再比较CmpKey

    s.CmpScore(next.value, value) < 0 || (s.CmpScore(next.value, value) == 0 && s.<PERSON>mp<PERSON>ey(next.value, value) < 0)
```

### 接口说明

1. Insert 添加元素value。返回添加成功后的节点和排行
``` go
func (s *SkipList) Insert(value interface{}) (*Node, int)
```

2. Delete 删除元素。 成功返回1，失败0
``` go
func (s *SkipList) Delete(value interface{}) int 
```

3. DeleteNode 删除节点。 参数：x 要删除的节点; update 需要更新的节点（即 从0到最高层，每个指向节点x的节点）
``` go
func (s *SkipList) DeleteNode(x *Node, update []*Node)
```

4. GetRank 获取元素的排名
``` go
func (s *SkipList) GetRank(value interface{}) uint32 
```

5. GetRankAndNode 获取元素的排名和节点
``` go
func (s *SkipList) GetRankAndNode(value interface{}) (uint32, *Node)
```

6. GetNodeByRank 通过排名获得节点
``` go
func (s *SkipList) GetNodeByRank(rank uint32) *Node 
```
