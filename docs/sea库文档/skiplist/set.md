## Set

### 概述

   Set 有序集合。功能和实现都类似于 redis/zset。 由跳表skiplist和hashmap实现。    
   key相关的操作通过 map快速定位到元素。 排名和score相关由skiplist来定位

``` go

// 元素接口
type Valuer interface {
	Key() uint64
	Score() uint64
}

// Set结构体
type Set struct {
	sl    *SkipList                 // 跳表       
	index map[uint64]Valuer         // 元素map  id=>元素
}
```

### 接口说明

1. Insert：添加元素。 Insert之前检查element是否存在(GetElement)。   
 如果添加重复元素，sl会存在重复，但是index只有一份最新的

``` go
func (s *Set) Insert(value Valuer)
```

2. GetElement 通过Key获取元素
``` go
func (s *Set) GetElement(key uint64) Valuer 
```

3. Delete 删除元素。 
``` go
func (s *Set) Delete(value Valuer) Valuer 
```

4. GetRank 获取排名
``` go
func (s *Set) GetRank(key uint64) uint32
```

5. GetRankAndNode 获取排名和节点
``` go
func (s *Set) GetRankAndNode(key uint64) (uint32, *Node) 
```

6. GetNodeByRank 通过排名获取节点
``` go
func (s *Set) GetNodeByRank(rank uint32) *Node
```

6. GetRangeByRank 获取排名在[start, end]范围内的节点
``` go
func (s *Set) GetRangeByRank(start, end uint32) []interface{} 
```

7. DeleteRangeByRank 删除排名在[start, end]范围内的节点
``` go
func (s *Set) DeleteRangeByRank(start, end uint32) uint32
```

``` go
  //特定查找参数结构
type RangeSpec struct {
	Min, Max     uint64     // 查找范围 [min, max]
	MinEx, MaxEx bool       // 是否排除边界min和max
	Reverse      bool       // NOTE:false-正序(递增) true-逆序(递减)
	Num          int        // 搜索的时候用来控制数量 0-无限制 !0-上限
	CheckF       func(interface{}) bool  //过滤函数
}
```

8. RandomInRange 范围段内随机取出count个节点
``` go
func (s *Set) RandomInRange(rd *rand.Rand, rg *RangeSpec, count uint32) []*Node 
```

9. GetRangeByScore 范围内所有满足条件的节点
``` go
func (s *Set) GetRangeByScore(rg *RangeSpec) []interface{} 
```
