#time

## 概述

time 在标准库time的基础上做了些改变。本地时间每300ms才会有变化

## 接口说明

### AccurateNow() Time
获取标准库time.Now时间

### Now() Time
获取本地时间

## 示例

```
func TestNow(t *testing.T) {
	for i := 0; i < 10; i++ {
		fmt.Printf("Now:			%v \n", Now())
		fmt.Printf("AccurateNow:	%v \n", AccurateNow())
		time.Sleep(100*time.Millisecond)
	}
}

```

结果
```
=== RUN   TestNow
Now:			2021-07-09 16:18:17.1079977 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.1151335 +0800 CST m=+0.008668401 
Now:			2021-07-09 16:18:17.1079977 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.2202949 +0800 CST m=+0.113829801 
Now:			2021-07-09 16:18:17.1079977 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.3302291 +0800 CST m=+0.223764001 
Now:			2021-07-09 16:18:17.419724 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.4359355 +0800 CST m=+0.329470401 
Now:			2021-07-09 16:18:17.419724 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.5409401 +0800 CST m=+0.434475001 
Now:			2021-07-09 16:18:17.419724 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.6510582 +0800 CST m=+0.544593101 
Now:			2021-07-09 16:18:17.7091622 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.7566909 +0800 CST m=+0.650225801 
Now:			2021-07-09 16:18:17.7091622 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.8672553 +0800 CST m=+0.760790201 
Now:			2021-07-09 16:18:17.7091622 +0800 CST 
AccurateNow:	2021-07-09 16:18:17.9722782 +0800 CST m=+0.865813101 
Now:			2021-07-09 16:18:18.0196456 +0800 CST 
AccurateNow:	2021-07-09 16:18:18.0828087 +0800 CST m=+0.976343601 
```
