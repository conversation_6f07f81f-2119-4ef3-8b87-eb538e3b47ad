# Event

## 概述

一个简单的事件管理器

```
type Manager struct {
	kind2Fns map[uint32][]*Callback  // 事件类型 =》 回调函数
	events   *list.List              // 事件列表
}
```

## 接口说明

### type Callback = func(Eventer, interface{}) 
事件触发后的回调函数

### Watch(kind uint32, fn *Callback) bool
监听某个类型的事件， fn是回调函数

### (m *Manager) Add(event Eventer)
添加触发的事件到列表

### (m *Manager) Process(param interface{}) (int, int)
触发事件列表的事件。 根据事件类型调用回调函数。 最后清空列表

### 示例

``` go

type Event1 struct {
	K uint32
}

func (e *Event1) Kind() uint32 {
	return 1
}

type Event2 struct {
	K uint32
}

func (e *Event2) Kind() uint32 {
	return 2
}

func TestEvent(t *testing.T) {
	m := NewManager()
	cb11 := func(e Eventer, p interface{}) {
		t.Logf("cb11: %v, %v \n", e, p)
	}
	cb12 := func(e Eventer, p interface{}) {
		t.Logf("cb12: %v, %v \n", e, p)
	}
	if !m.Watch(1, &cb11) {
		t.Error("watch failed")
	}
	if !m.Watch(1, &cb12) {
		t.Error("watch failed")
	}
	if m.Watch(1, &cb12) {
		t.Error("duplicate watch")
	}
	if !m.Watch(2, &cb12) {
		t.Error("watch failed")
	}
	m.Add(&Event1{1})
	m.Add(&Event2{2})
	m.Process(33)
}

```
