## 天神服务器端游戏代码结构

1. 代码目录结构

   ```
   ├── bin					//编译后的可执行文件
   ├── config				//一些xml配置文件
   ├── data				//策划的数据表配置文件 make all的时候从单独的git库clone过来
   ├── deps				//一些依赖的第三方库和脚本
   ├── docs				//一些开发和说明文档
   ├── jenkins				//服务器重启等相关jenkins脚本
   ├── log					//游戏产生的日志目录
   ├── Makefile			//
   ├── protos				//协议文件
   │   ├── in				//server端内部的协议包括logic和db的，gm等
   │   └── out				//server和client的协议 make all的时候从单独的git库clone过来
   ├── src
   │   └── app
   │       ├── go.mod
   │       ├── go.sum
   │       ├── goxml			//策划表xml生成的对应的go文件
   │       ├── logic			//游戏服核心逻辑代码
   │       	├── activity 	//全局数据，好友，竞技场，工会等
   │       	├── character 	//角色属性等
   │       	├── command  	//协议处理逻辑
   │       	├── gateway 	// 游戏服和网关连接部分
   │       	├── db 			//redis处理相关脚本
   │       	├── battle 		//战斗逻辑
   │       	├── mail 		//邮件相关
   │       	├── rank 		//通用排行榜相关
   │       	├── helper 		//业务逻辑基础方法的封装 比如token的解析等
   │       	├── report 		//战报存储和定时删除服务
   │       	├── request 	//通用的请求服务，支持超时和多消息返回
   │       	├── world 		//跨服连接部分
   │       ├── world		//跨服核心逻辑代码
   │       ├── postman		//grpc接口注册
   │       ├── protos		//proto协议生成的go文件
   │       ├── service		//服务入口
   │       └── tools		//脚本工具
   ├── tools
   ├── README.md
   └── version
   ```

2. 服务器框架

   ![CardServerFramework](../image/CardServerFramework.jpg)

   3. 服务器配置环境

      - centos 7.6
      - go 1.15(随着go版本提升)
      - protobuf@v3.5.1

   4. logic设计

      ![logic](../image/logic-design.jpg)

   5. 数据保存方式

      - 玩家的大部分数据都是存在一个key里的，比如user:1001，里面是利用的是redis的hash结构，可以只更新部分field，部分数据比如英雄等是单独的key,比如user_hero:1001来存储的。

      - 数据存储工具是用的redisLV(redis+leveldb).写入redis的数据实时落地
      - logic里的数据是通过定时写入db，定时时间可以配置
      - 一些关键数据，比如玩家的重要资源发生改变会实时写入db

   6. 常用工具

      - redisop,
        - redis代码自动生成工具
        - 项目内使用对应脚本为`tools/build_db_data.sh`，快捷操作为`make db`。
     - 项目地址和说明文档 [redisop](http://gitlab.qdream.com/kit/redisop)
      - xmlc,
     - xml代码自动生成工具
        - 具体使用可以见`tools/build_xml.sh`，快捷操作为`make xml`。
     - 项目地址和说明文档 [xmlc](http://gitlab.qdream.com/kit/xmlc)
      - protoclone
     - 用来生成proto定义的数据结构的clone方法
        - 项目内使用对应脚本为`tools/build_proto_go.sh`， 快捷操作为`make protos`。
        - 项目地址和说明文档 [protoclone](http://gitlab.qdream.com/kit/protoclone)
   - robotclient
        - 用来和前端联调之前方便自己自测的模拟客户端工具，支持发送协议，可手动输入参数。仅限windows下使用
        - 执行bin下的make.bat会自动拉去最新的协议文件，或者自己修改make.bat,手动覆盖自己的协议文件，就会生成最新的客户端工具
        - 项目地址和说明文档 [robotclient](http://gitlab.qdream.com/ngame-backend/robotclient)
   
   7. 消息的数据格式
   
      ![gameproto](../image/gameproto.jpg)
   
   8. 重点模块
   
      1. 登录
      2. 创角
   
   
