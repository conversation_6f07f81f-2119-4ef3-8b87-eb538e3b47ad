## 战斗集群架构图
![](../image/BattleServer.jpg)

## 1. LogicServe
1. 通过Http协议访问battleServe
2. 每个服设置一个http访问池，防止高并发开启太多goroutine.
3. 访问数据通过protobuf序列化之后通过post以二进制数据的方式传递给http服务器

## 2. Haproxy
1. 用来做中间的负责均衡用的，单服承载能力强，可以承受4-5W链接。
		一台主机器，在利用keepalived技术搭建一台备份机器，实现高可用。当一台挂了之后会自动切换到另外一台
	
## 3. battleServer
1. nodejs开发，搭建一个Http服务器。
2. 开启过个进程，监听不同的端口充分利用多核，前端通过haproxy反向代理
3. battleServer是无状态的，可以动态扩展
4. 客户端战斗会打包为一个JS文件，并export出来一个方法给服务器调用。
	
## 4. 流程
1. 所有的战斗数据属性后端计算，在战斗开始之前和随机种子一起传递给前端，并缓存该次战斗相关属性数据，前端需要验证的时候，后端再将缓存的战斗属性发送给BattleServer做复盘验证。
	
## 5. 其他问题
1. 服务器闪断重启，玩家已经开始但还没有验证的战斗将无法进行验证，提示玩家重新开始战斗。
2. 需要实现战斗服热更新。
3. 前后端的V8版本号需要一致，在开始之前约定好。

## 6. 性能
1. 需要测试LogicServe连接Haproxy的http keepalive的利用率情况。减少http反复链接
