## 天神登录

1. 协议

   ```protobuf
   /************** client <> server ****************/
   message C2G_Login {
     string token = 1;			//里面包含玩家的账号 渠道等信息，通过秘钥加密
     uint64 server_id = 2;		//服务器ID
     uint64 version = 3;      //客户端版本号
     uint64 sync_cookie = 4;  //消息增量发送机制标记，登录后从服务器端获取的，下次登录在发送过来， 第一次为0
     string net_addr = 5;     //客户端网络地址
     string extra = 6;        // json
   }
   
   message G2C_Login {
     uint32 ret = 1;
     uint64 uid = 2;
     uint64 version = 3;      //服务器端版本号
     uint64 sync_cookie = 4;  //消息增量发送机制中使用的cookie
     uint32 sync = 5;  // 0: 表示需要全部flush; >0 : 表示可以增量同步
     string extra = 6;  // json
   }
   
   ```

2. 消息处理流程。

   - logic收到登录消息
     - 判断当前在线玩家数量是否超过限制，如果超过限制，把登录消息丢入到排队队列里去，返回给前端在排队中。
       - 在服务器的定时执行中，判断一下当前在线人数是否小于限制，小的话就从排队队列里取出消息处理。
       - 排队队列用跳表实现
     - 解析token数据，得到玩家信息
     - 判断玩家是否能登录
     - 判断玩家是否在登录或者已经登录，如果有，踢出前面的玩家
     - 从缓存数据里获取玩家，如果有，直接从缓存登录
     - 如果缓存没有，把消息丢给db，从redis里查找数据登录
     - 如果有角色，登录成功，加载登录数据。触发登录事件。
     - 没有角色，直接发起创角请求给db
   - 创角
     - 检查创角是否满足条件，账号限制，ip限制等
     - 设置保存基础信息，账号，user等数据
     - 返回创角信息给logic
     - logic收到之后，加载数据，触发登录事件
     - 返回给前端