#### 战斗设计文档

1. 战斗为5V5的回合制战斗。站位从1-5.具体布阵是从前排到后排，从上往下，具体可参照策划案

2. 战斗分为攻击队伍和防守队伍。攻击队伍index=0,防守方index=1

3. 队伍的每个战斗单位为Uniter接口。英雄对象为member,神器对象为artifactManager。每个uniter有一个唯一id=index*100+pos(pos为其在队伍中的站位的位置,神器为位置6)

4. 战斗中的多个神器只是作为一个神器管理对象的多个技能，按顺序释放。

5. 战斗的入口为battle.Manager.Run()

6. 战斗外调用的接口可以参照character.AttackMonster().阅读战斗的代码也可以从这个入口开始。

7. 执行流程

   1. 流程图

      ![oneExecutioner](../image/oneExecutioner.jpg)

   2. 整个战斗的执行流程独立管理的，抽象为OneExecutioner接口。不同的执行阶段对应不同的对象

   3. OneBattle:一场战斗

   4. OneRound:一个回合

      ```go
      type OneRound struct {
          *OneBattle
          oneAction    *OneAction
          index        uint32
          triggerCount map[uint32]map[uint32]uint32 //触发次数
          speed        []*Speed
          runed        map[uint32]struct{} //本回合已经行动过的 member id=>
          absorbHurt   int64               // 守护铠吸收的伤害   在回合开始的时候可能会移除神器身上的守护铠buff
      }
      ```

      

   5. OneAction:一个回合里的一个uniter的一次出手

      ```go
      type OneAction struct {
          *OneRound
          u         Uniter
          oneSkills []*OneSkill
      }
      ```

      

   6. OneSkill:一次出手里面的一次技能，可能一次出手有多个技能释放

      ```go
      type OneSkill struct {
          *OneAction
          attack         Uniter
          skill          *Skill
          isFollow       bool
          oneEffects     []*OneSkillEffect
          triggerCount   map[uint32]map[uint32]uint32 //mem id=>psId=>触发次数
          crit           bool
          kill           bool
          cure           int64
          hurt           int64
          overCure       int64
          overHurt       int64
          addControlBuff bool
          hasGuard       bool //是否处罚了保护
          attrChange     map[uint32]Uniter
          psAttrChange   map[uint32]Uniter
      }
      ```

      

   7. OneSkillEffect:一次技能里的一段效果，每一个技能有多段效果

   8. OneSkillAttack:一个技能效果你的一次攻击，有明确的防守方了。

   9. 整个结构是通过匿名继承上一个阶段的对象实现串联起来。每一个对象里面都包含prepare, run, end三个方法，代表这个阶段的初始化阶段，执行阶段和结束阶段。

8. 技能管理:

   1. 一个uniter所有的主动和被动技能用一个skillManager对象管理。一个uniter身上不能出现重复id的主动技能,也不能出现重复id的被动技能,一般一个英雄有2-3个主动技能，被动技能数量不限制

9. 主动技能的设计：

   1. 一个主动技能在战斗是作为Skill对象存在

   2. 一个主动技能分为3段效果，每段效果都按照概率，目标选择，作用效果，效果参数的顺序来执行

   3. 目标选择的方法在target.go里

   4. 作用效果分为：伤害，治疗，加buff，清除buff，复活五种类型。具体的实现在battle/active_skill_effect.go

      ```go
      manager.DoSkillHurt //伤害
      manager.DoSkillCure //治疗
      manager.DoSkillAddBuff //添加buff
      manager.DoSkillRemoveBuff //清除buff
      manager.DoSkillResurrection //复活
      manager.DoSkillHurtSelf //自残 这个不触发任何被动
      ```

   5. 主动技能的一些效果需要借助被动技能来实现，这些被动效果填在主动技能的passive_1和passive_2字段。这些效果会在技能执行的开始添加到uniter的被动技能里，在技能执行结束后立马清除掉。

   6. 主动技能里也可能包含常驻的被动技能。这些被动效果放在PassiveSkill1，PassiveSkill2，PassiveSkill3字段里。

10. 被动技能的设计

    - 被动技能在战斗中是作为PassiveSkillEffect对象存在

       - 被动技能的策划配表比较简单，直接把整个效果定义为一个类型，然后配几个参数方便扩展

       - 程序的具体实现是把策划配的类型转化为程序内需要用到的数据，具体的格式在goxml/passive_skill_const.go里

         ```go
         package goxml
         
         var gBattlePassives = map[uint32][]uint32{
             10000006: {TriggerBattleInit, CondNone, EffectChangeHaloAttr, EffectTimeLater, EveNone},                                           //存活的光
         环效果
             10000007: {TriggerBattleInit, CondNone, EffectChangeHaloPsAttr, EffectTimeLater, EveNone},                                         //光环效果
         ，修改一个系数
             10000008: {TriggerBattleInit, CondNone, EffectBindSkill, EffectTimeLater, EveNone},                                                //初始化的
         时候绑定技能
             10000013: {TriggerBattleInit, CondNone, EffectHpLowerAttr, EffectTimeLater, EveNone},                                              //战斗初始
         的时候修改自己的血量属性变化
             10000014: {TriggerBattleInit, CondNone, EffectHpHigherAttr, EffectTimeLater, EveNone},                                             //战斗初始
         的时候修改自己的血量属性变化
             10100003: {TriggerBattlePrepare, CondNone, EffectAddBuff, EffectTimeLater, EveSkillEnd},                                           //战吼加buff
         }
         ```

         

       - 每一个被动技能分为触发时机Trigger,触发条件condition,触发效果，执行时机

             - 触发时机：在战斗内的埋点的地方。具体埋点的位置会在脑图里标记出来。这个位置严禁任意修改，每做一个新效果都需要和策划确认该效果的触发时机是在哪里。

                ```go
                //触发被动
                //triggerUniter 触发者
                //trigger 触发时机
                //triggerType 对应的就是OneBattle OneRound等不同的阶段标识
                //data 对应的就是OneBattle OneRound等具体的对象，因为被动的执行过程中需要用到的一些具体数据需要在对应的执行环境中取
                func (m *Manager) TriggerPSE(triggerUniter Uniter, trigger, triggerType uint32, data interface{}) { 
                }
                ```

                

          - 触发条件：自定义的具体实现在battle/condition.go里面

          - 触发效果：被动技能的具体效果，具体的效果在manager.DoPassiveSkillxxxx函数里面实现.具体的实现代码在battle/passive_skill_effect.go

          - 是否立即执行：立即执行和延迟执行

            - 原则上一个主动技能的释放是一个完整的效果，中间不会出现任何其他的效果，所有触发的被动效果都会被放到主动技能执行完之后在执行。
            - 但是有一些特殊效果需要立即执行的，比如攻击前修改攻守双方的属性，比如加血后产生的过量治疗转化为护盾等（这个效果非常特殊，按道理应该延后执行的，但是因为这个效果不改变施法者，也不改变被施法者，可以合并在技能里实现）
            - 延迟执行有多个执行的时机。在这些具体的时机调用manager.DoLatePassiveSkill
              - 回合开始的时候结算完buff效果，
              - 每个uniter行动前的结算完buff效果
              - 行动前主动技能释放完后，
              - 追击技能释放完后
              - 每个uniter行动结束后

          - 限制次数。（指的是一次技能释放过程中的限制次数）。

          - 执行顺序.为了在多次触发的过程中，保证执行顺序的合理。

            - 比如一个技能打了3个目标，那么在攻击每个目标时会触发一个被动，被攻击的目标也会触发一个被动。为了保证完整性，可以让攻击者触发的先做，在统一来做被攻击的。比如先全部结算完吸血，在全部做反弹，这样就避免了被反弹死的情况。

              ```go
              //事件优先级，越小的执行优先级越高
              const (
                  EveNone         = iota
                  EveDrainHp      //吸血
                  EveRebounds     //反弹
                  EveOneAttack    //每次攻击时
                  EveOneAttackDef //被攻击时触发
                  EveOneEffect    //每段效果后
                  EveOneEffectDef //被每段效果后
                  //EveDead      //死亡后的效果
                  EveSkillEnd //攻击后, 追加效果
                  EveHpLess   //血量低于xx%回血
                  EveDead     //死亡后的效果
              )
              ```

              

   5. 被动技能的执行

      每个被动技能开始执行的时候会生成一个新的PassiveSkillExecution对象，在被动技能的整个执行过程中这个对象都会一直传递过去

      延迟执行的被动技能在延迟队列里面保存的也是这个对象。

      ```go
      type PassiveSkillExecution struct {
      	id            uint32 //指针的id
          effect        *PassiveSkillEffect //具体的被动技能的效果
          triggerUniter Uniter
          trigger       uint32
          ExecutionData
          priority uint32 //优先级
      }
      
      type ExecutionData struct {
          t         uint32      //执行流程的实际 TriggerInOneSkill
          data      interface{} //具体的数据 *OneSkill,这个数据会被后面执行覆盖
          condValue int64       //条件检查的时候附带的值
          // TODO: triggerValue int64 //触发的时候附带的值 待扩展
      }
      ```

      

   6. 技能选择顺序。参考member.ChooseNowSkill()函数

   7. 注意点

      - 被动技能的触发时机和描述文字对应的关系。参照goxml/passive_skill_const.go
        - 攻击/治疗阶段  =>  OneSkillEffect.Preapre oneSkillEffect.End
        - 伤害/治疗 前/后 => OneSkillAttack.Prepare OneSkillAttack.End
        - 技能释放后 => OneSkill.End()
        - 技能结束后 => OneAction.End()
      - 被动技能条件的对象
        - owner => 被动技能的拥有者
        - attack => 实际的本次攻击中的攻击方
        - defense =>实际的本次攻击中的防守方，如果是治疗的话 attack和defense是同一个队伍
        - op => 实际的attack的对手方
      - 被动技能条件的时间周期
        - 有些条件是指定范围的，比如整个技能期间，或者一个effect期间。没有特别指定的就是当前实时的状态。
      - 

11. buff设计

    1. 每个新产生的buff都有一个自增的为一ID。删除buff的时候需要指定这个唯一的ID
    2. 所有的Buff配置在buff_info表里。buff按照type分类。所有的type效果配置在buff_type_info表里
    3. 同一个type代表一种效果。type有叠加规则，无叠加，可叠加，直接覆盖。对于后面来说是按照type来设计切片。按照规则添加和删除对应的buff.
    4. buff对属性的影响会在addbuff和removebuff的时候直接计算好，生成冗余数据
    5. 注意控制类的Buff add的时候有特殊概率控制，参照公式110。其他的Buff就走正常概率
    6. buff.value字段意义

       - buff的被使用次数，一些需要计算次数的buff中会用到，比如buff_type=304的免疫负面非控制类buff，并且是次数控制
       - dot 或者 hot类的buff 他的值。每次上buff的时候确定
       - 魔法反噬的值（注意，魔法反噬不算dot)

12. 战报的生成

       1. 战斗是通过在具体的效果函数内埋点，调用report对象的具体方法拼接而成。
       2. 有一个调试战报，可以通过打开gDebugReort开启。调试战斗详细记录了每个主动技能和被动技能的执行过程和公式的详细参数,方便QA测试

13. 战斗测试

    - 在battle包下面执行go test -v就可以执行一场测试战斗，具体的测试战斗可以修改battle_test.go文件修改

    - 启动网页测试服务，

      - make battle
      - cd bin && ./battle -addr=:12123

    - 网页测试的格式

      ```json
      [{
          "title":"攻击方1号位", //title 随便填
          "num":1, //序号，攻击方1-5，防守方6-10
          "id":0, //英雄的系统id
          "skills":[], //技能id，按照普通技能，主动1，主动2，被动1，被动2的格式填写skill_info表里的id
          "passive_skills":[], //为了方便，可以直接添加passive_skill_info表里的id生成一些被动效果
          "attrs":[{"id":1,"value":100},{"id":2,"value":1000},{"id":3,"value":10}] //所有的属性，这里的属性就是战斗力的属性，完全覆盖其他地方的属性
      },
       {
           "title":"防守方1号位",
           "num":6,
           "id":0,
           "skills":[],
           "passive_skills":[],
           "attrs":[{"id":1,"value":100},{"id":2,"value":1000},{"id":3,"value":10}]
       }
      ]
      ```

      
