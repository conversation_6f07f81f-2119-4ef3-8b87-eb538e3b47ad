# login-check压测报告



### 测试环境

|  配置  |              地址              |        用途        |
| :----: | :----------------------------: | :----------------: |
| 8核16G | 压测机器人服务器113.31.107.156 | 简单接收检验服请求 |
| 8核16G |  压测游戏服务器106.75.239.59   |      登录校验      |
| 8核16G |         106.75.223.145         |      wrk压测       |



### 压测命令

1. -t24  24个线程
2. -c2000  2000个连接
3. -d120  压测时间120s

```
./wrk -t24 -c2000 -d120s --timeout=30s --latency  -s post.lua http://106.75.239.59:8076/login
```





### 压测数据

1. client数量

   ```
   http:
     parallel: 32
   ```

2. 压测结果

   ```
   Running 2m test @ http://106.75.239.59:8076/login
     24 threads and 2000 connections
     Thread Stats   Avg      Stdev     Max   +/- Stdev
       Latency   273.39ms  272.02ms   1.35s    84.02%
       Req/Sec   387.23     76.75   720.00     68.00%
     Latency Distribution
        50%  206.96ms
        75%  418.05ms
        90%  670.80ms
        99%    1.08s
     1110384 requests in 2.00m, 450.05MB read
   Requests/sec:   9246.40
   Transfer/sec:      3.75MB
   ```

3. pprof 

   ```
   wj@wujiedeMacBook-Pro pprof % go tool pprof pprof.newLoginCheck.alloc_objects.alloc_space.inuse_objects.inuse_space.001.pb.gz
   File: newLoginCheck
   Type: inuse_space
   Time: Dec 13, 2021 at 10:53am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 38907.58kB, 95.00% of 40955.67kB total
   Showing top 20 nodes out of 108
         flat  flat%   sum%        cum   cum%
   10278.10kB 25.10% 25.10% 10278.10kB 25.10%  bufio.NewReaderSize
    7192.03kB 17.56% 42.66%  7192.03kB 17.56%  bufio.NewWriterSize (inline)
    4295.82kB 10.49% 53.15%  4295.82kB 10.49%  runtime/pprof.(*profMap).lookup
    2048.39kB  5.00% 58.15%  2048.39kB  5.00%  net/textproto.(*Reader).ReadMIMEHeader
    2048.31kB  5.00% 63.15%  2048.31kB  5.00%  net/http.(*Server).newConn
    1536.56kB  3.75% 66.90%  1536.56kB  3.75%  runtime.malg
    1536.24kB  3.75% 70.65%  7172.90kB 17.51%  net/http.(*conn).readRequest
    1536.20kB  3.75% 74.40%  1536.20kB  3.75%  math/big.nat.make (inline)
    1184.27kB  2.89% 77.29%  1184.27kB  2.89%  runtime/pprof.StartCPUProfile
    1106.08kB  2.70% 79.99%  1106.08kB  2.70%  srv/utils.NewHTTPClient
    1024.25kB  2.50% 82.49%  3072.63kB  7.50%  net/http.readRequest
    1024.14kB  2.50% 85.00%  1024.14kB  2.50%  net/http.setupRewindBody
     512.31kB  1.25% 86.25%   512.31kB  1.25%  reflect.unsafe_NewArray
     512.25kB  1.25% 87.50%   512.25kB  1.25%  io.ReadAll
     512.14kB  1.25% 88.75%   512.14kB  1.25%  github.com/gin-gonic/gin.(*Context).Set
     512.14kB  1.25% 90.00%   512.14kB  1.25%  github.com/go-playground/validator/v10.New.func1
     512.12kB  1.25% 91.25%   512.12kB  1.25%  github.com/gin-gonic/gin.(*Engine).allocateContext
     512.12kB  1.25% 92.50%   512.12kB  1.25%  net/http.send.func1
     512.05kB  1.25% 93.75%  4096.98kB 10.00%  srv/service/verify.Verify
     512.05kB  1.25% 95.00%  1024.07kB  2.50%  net/http.(*persistConn).readLoop
   (pprof) 
   ```

   ```
   wj@wujiedeMacBook-Pro Downloads % go tool pprof profile
   File: newLoginCheck
   Type: cpu
   Time: Dec 13, 2021 at 10:52am (CST)
   Duration: 1mins, Total samples = 7.65mins (762.88%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 5.07mins, 66.37% of 7.65mins total
   Dropped 1075 nodes (cum <= 0.04mins)
   Showing top 20 nodes out of 193
         flat  flat%   sum%        cum   cum%
     1.76mins 22.98% 22.98%   1.76mins 22.98%  math/big.addMulVVW
     1.06mins 13.91% 36.89%   2.94mins 38.50%  math/big.nat.montgomery
     0.47mins  6.18% 43.06%   0.49mins  6.46%  syscall.Syscall
     0.28mins  3.68% 46.75%   0.57mins  7.44%  runtime.scanobject
     0.17mins  2.27% 49.01%   0.17mins  2.27%  runtime.markBits.isMarked (inline)
     0.16mins  2.11% 51.12%   0.16mins  2.11%  math/big.mulAddVWW
     0.15mins  1.90% 53.02%   1.01mins 13.16%  runtime.mallocgc
     0.14mins  1.81% 54.83%   0.15mins  2.00%  runtime.findObject
     0.13mins  1.75% 56.58%   0.13mins  1.75%  math/big.subVV
     0.10mins  1.34% 57.92%   0.10mins  1.34%  runtime.memmove
     0.09mins  1.23% 59.16%   0.44mins  5.70%  math/big.nat.divBasic
     0.09mins  1.13% 60.28%   0.09mins  1.13%  runtime.memclrNoHeapPointers
     0.08mins  1.04% 61.32%   0.33mins  4.30%  runtime.gentraceback
     0.06mins  0.84% 62.16%   3.07mins 40.17%  math/big.nat.expNNMontgomery
     0.06mins  0.83% 63.00%   0.06mins  0.84%  runtime.pageIndexOf (partial-inline)
     0.06mins  0.77% 63.76%   0.06mins  0.77%  runtime.nextFreeFast
     0.06mins  0.73% 64.49%   0.08mins  1.02%  runtime.heapBitsSetType
     0.05mins  0.66% 65.15%   0.06mins  0.77%  math/big.divWW
     0.05mins  0.63% 65.79%   0.05mins  0.64%  math/big.lehmerSimulate
     0.04mins  0.59% 66.37%   0.08mins  1.07%  runtime.sweepone
   (pprof) 
   ```

4. profile文件[profile-svg](./login-check.svg)

