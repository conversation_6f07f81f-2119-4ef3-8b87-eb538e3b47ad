## 压测进度

| 模块 | 负责人 | 压测状态 | 问题                                                         |
| :--: | :----: | :------: | :----------------------------------------------------------- |
| 好友 |  长风  |  已压测  | 整体符合要求，最大瓶颈在logic的save函数消耗，目前无更好的优化手段。 |
| 密林 |  十二  |  已压测  | 战斗逻辑优化后，5000人压测的情况下，无压力（每秒约330次战斗） |
| 境界 |  先儿  |  已压测  | 1. 目前没有加入布阵，正常情况下5000人没有性能压力，符合压测业务指标要求，后续加上布阵再压测一下 2 . 加入布阵，正常情况下5000人没有性能压力，符合压测业务指标要求 |
| 爬塔 |  十二  |  已压测  | 1500人压测的情况下，消息稍有堆积（每秒约700次战斗）。1000人压测时，无压力（每秒约500次战斗） |
| 点金 |  先儿  |  已压测  | 正常情况下5000人没有性能压力，符合压测业务指标要求                        |
| 主线 |  十二  |  已压测  | 1500人压测的情况下，平均消息堆积1500左右（每秒约597次战斗），消息返回有延迟。瓶颈在战斗协议 |
| 布阵 |  长风  |  已压测  | 目前没有加入神器，正常情况下5000人没有性能压力，后续在加上神器测试一下。 |
| 竞技场 |  十二  |  已压测  | 5000人压测的情况下，消息略有堆积（每秒约943次战斗，持续回合很少）。改用miniSnapshot后内存指标有所下降。整体符合要求，主要耗时在db保存竞技场协议 |
| 神器 |  艾斯  |  已压测  | 完全用新号，压测前主线布阵，符合压测指标，计算属性时有个读表占用时间比较久，之后研究下原因 |
| 宝石 | 先儿 | 已压测 | 5000人，每人发200个宝石，加入布阵，消息略有堆积，不过整体符合业务压测指标要求，还有优化空间，后续再测试 |
| 英雄 | 十二 | 已压测 | 5000人，发80个英雄，布阵3个（不能为空的阵容），指标正常符合预期 |
| 公会 |  艾斯  |  已压测  | 5000人，符合压测指标，没有明显的公会操作产生的压力 |
| 商店 | 十二 | 已压测 | 5000人，符合压测指标 |
| 悬赏 | 先儿 | 已压测 | 正常情况下5000人没有性能压力，符合压测业务指标要求 |
| 材料本 | 艾斯 | 已压测 | 1500人压测情况下，符合压测指标 |
| 装备 | 艾斯 | 已压测 | 5000人，新建高级号，符合压测指标 |
| 公会副本 | 艾斯 | 已压测 | 5000人，内存占用比较大 3000人符合指标 |
| 水晶 | 十二 | 已压测 | 打开英雄、装备、宝石、纹章等多个模块与水晶协同测试，初期有消息积压，主要由于机器人资源发放（伴随任务完成）引起的。未见水晶系统异常 |

### 压测手册

[压测手册](./天神压测手册.md)
