## 天神压测手册

#### 测试环境

|     用途     |                     配置                     |              地址               |
| :----------: | :------------------------------------------: | :-----------------------------: |
| 机器人服务器 | 8核（AMD EPYC Processor 2.9GHZ）16G 固态硬盘 | 113.31.107.156（172.21.173.36） |
|  游戏服务器  |               8核16G 固态硬盘                | 106.75.239.59（172.21.254.181） |
|  网关服务器  |               8核16G 固态硬盘                |  *************（172.21.8.19）   |

登录：

用publick key的方式登录，3个服务器都一样。用外网地址登录

用户名：roobot, 端口号：22001

key:    roobot_ts.roobot_ts文件



#### 操作方法：

- 创角测试

  1. 更新游戏服代码：在*************的jenkins机器上执行“压测游戏服”脚本

  2. 更新压测脚本代码：在*************的jenkins机器上执行“压测机器人”脚本

  3. 登录游戏服：

  4. cd ~/game/data 并清空game_initial_info.xml 里的所有数据（要保留xml的基础格式数据）。

  5. 修改~/game/config/robot.xml，关闭其他功能的开启，只做登录创角

  6. cd ~/下，执行clear_logic_log.sh脚本(清空日志)和clear_logic_db.sh脚本（清空redis数据）

  7. 登录机器人服务器

  8. cd ~/game/bin 执行./benchmark_create.sh robot 50000 4000 300(第一个参数robot代码账号前缀， 第二个参数代表总注册人数 第三个参数代表每秒的并发量一般是2000 4000 5000， 第四个参数代表脚本多少秒之后自动结束， 一般是180左右)

  9. 注意压测脚本执行完后，在获取106.75.239.59:10001/debug/pprof来获取profile和heap的分析数据，注意heap的数据要直接访问106.75.239.59:10001debug/pprof/heap获取（不是直接访问连接，没有?debug=1）

  10. 执行完成后填写压测报告。

  11. 延迟曲线的获取

      1. 在压测机器人的服务器上
      2. cd ~/game/log
      3. cat robot.log-2021-04-09* |grep CommandStat |grep cmd:10100|grep ret:true|awk '{print $2"."$8}'|awk -F "." '{print $1":"$3}'|awk -F ":" '{print $1":"$2":"$3","$5}'> delay10.csv(注意把事件换成自己测试的时间，下载的文件名换成自己的)
      4. 下载delay10.csv
      5. 用excel打开把第二列的时间单位ms通过全局替换替换掉
      6. 选中第一列和第二列，插入=>二位折线图。

  12. 延迟分布图的获取

      1. 和上面一样，把第一列的时间去掉,只保留第二列，然后选中第二列，做一下简单数据分析，分析的模板和公式参考delay.csv的文档。然后插入饼图

  13. cpu使用率等图表的获取

      1. 登录http://*************:3000/

         admin

         mq2021TS

      2. 点击Dashboards/Manager 选择NodeExproter...V20201010模板进去。

      3. 时间间隔选2s,时间选择你压测的时间，把cpu 网络等信息截图存入压测报告中（截图放入excel注意清晰度，可以先放到网页在截图会更清晰点）

      4. 进程的内存消耗可以选择Named processes模板截图。

  14. db消息延迟和堆积

      1. 登录游戏服
      2. cd ~/game/log
      3. cat logic.log*|grep "EROR"| grep -v "no found" 这里采集延迟消息
      4. cat logic.log*|grep "left"|grep "2021/04/09 19:20" (具体时间自己修改) 这个查看是有有消息堆积
      
  15. 插入profile和heap的pprof文件
  
- 业务测试

  1. 先在内网测试，注意控制测试人数在1000以下，保证消息的正确率能达到80%以上，整体性能没什么问题在去外网压测。

  2. 注意养成类的业务压测一定要先把主线布阵完成，让养成能走到属性计算里面。

  3. 业务压测如果需要发资源，请使用c2l_gm协议发送资源，发送之前判断一下自己的资源够不够，不够了就发。

  4. 更新游戏服代码：在*************的jenkins机器上执行“压测游戏服”脚本

  5. 更新压测脚本代码：在*************的jenkins机器上执行“压测机器人”脚本

  6. 登录游戏服：

     - cd ~/game/bin下
       - 如果需要清空数据就执行clear_logic_db.sh;
       - 执行logic_robot_prepare.sh脚本

  7. 登录机器人服务器

     - cd ~/game/bin下：
       - 执行/robot_benchmark_logic.sh friend robot 5000 1000 300  (注：robot_benchmark_logic.sh的具体使用方法参考script/README.md)
       - 执行完成后执行/robot_analyse.sh

  8. 登录游戏服

     - cd ~/game/bin下，
       - 执行./logic_analyse.sh

  9. 登录http://*************:3000/

     admin

     mq2021TS

     1. 点击Dashboards/Manager 选择my-go-metric模板进去。

     2. 将整体数据截图保存在报告里面

  10. heap和cpu相关的pprof文件会自动保存在bin/pprof文件下。

  11. 分析pprof

      1. 因为是用jenkins账号打包的，为了方便使用pprof的list命令可以用jenkins的账号登录*************进行go tool pprof 分析。

         账号：jenkins 密码：jkins234

- 战斗压测

     先生成高级账号，压测就是和排行榜前50名随机战斗

  1. 登录游戏服：

     - cd ~/game/bin下
       - clear_logic_db.sh;

  2. 登录机器人服

     1. cd ~/game/bin下：
        - 执行./robot_benchmark_logic.sh battle root 1500 1500 60 2 1  (注：robot_benchmark_logic.sh的具体使用方法参考script/README.md)
          - 这个主要是在游戏服生成1500个高级账号

  3. 登录游戏服

     - cd ~/game/bin下

     - 执行logic_robot_prepare.sh脚本

  4. 开始压测

     1. 分别压测800， 1000， 1200人5分钟
     2. 压测的脚本是
        1. ./robot_benchmark_logic.sh battle root 800 800 300 3 0
        2. ./robot_benchmark_logic.sh battle root 1000 1000 300 3 0
        3. ./robot_benchmark_logic.sh battle root 1200 1200 300 3 0
     3. 压测完成后执行
        1. ./battle_analyse.sh(统计战斗返回的回合数和战报大小)
        2. ./robot_analyse.sh (和业务一样，分析消息的延迟分布)
        3. 统计具体的消息延迟
           1. cat ../log/robot.log* |grep CommandStat |grep cmd:11053|grep ret:true|awk '{print $2"."$8}'|awk -F "." '{print $1":"$3}'|awk -F ":" '{print $1":"$2":"$3","$5}'> delay112716.csv
        4. 去http://*************:3000/分析cpu,内存和网络带宽等信息
        5. 有问题分析pprof
     4. 分析游戏服数据（和业务压测一样）



#### 业务压测指标

所有业务的压测指标都在

1. 消息的正确率达到80%以上，每个单独的消息正确率达到50%以上。
2. 核心消息的占比要符合实际情况：比如你爬塔的压测，不能战斗的总消息占比大大低于获取爬塔信息的。
3. 每秒的发送消息数应该接近或者大于总压测人数。
4. 消息延迟0-10ms的数据超过50%，0-100ms的延迟超过90%，消息的延迟都在500ms以下。（如果超过，需要研究下是哪里消耗过大）
5. logic 单个消息出现超过的情况不超过0.05%。如果5分钟的压测，一般不超过50条
6. go-metric指标
   - 内存不超过4G。并且在压测结束后，内存能回到正常水平1G以下。
   - cpu使用不超过200%。
   - 每分钟gc次数控制在30以下。
   - goroutine数量控制在60以下



## 压测辅助脚本

1. script/logic_clear_db.sh

   作用: 清除游戏服db数据库

   使用: 放到压测服~/game/bin下面. ./logic_clear_db.sh

   参数：无

2. script/logic_robot_prepare.sh

   作用：为了保证压测的业务成功概率高，需要修改一些数据表的配置，不同的业务自己添加就好.并且会清空logic的日志。修改完之后会自动重启游戏service

   使用: 放到压测服~/game/bin下面. ./logic_robot_prepare.sh

   参数：无

3. script/robot_benchmark_logic.sh
   作用：压测业务逻辑脚本

   使用: 放到压测robot服~/game/bin下 执行

   ./robot_benchmark_logic.sh flower root 5000 500 300 2 30 true true 1000100002 ************:38422 ************:10002

   参数：

   - 参数1：模块名，flower， 代表会自动开启flower模块的测试
   - 参数2：机器人账号前缀，想要注册新的账号就换一个不同的前缀就可以了
   - 参数3：总的机器人数量。普通的业务测试，建议用5000
   - 参数4：每秒登录数量。建议用1000
   - 参数5：压测时间。单位s。建议至少300
   - 参数6：测试模式，0-低级测试模式 1-版署建号模式 2-低中高级号测试 3-跟踪协议测试，默认是0
   - 参数7：生成什么样的账号，参数6为2时生效。机器人模板类型
   - 参数8：是否自动加入公会，默认false
   - 参数9：创号时是否发资源，默认true
   - 参数10：logic服务器id，默认的是1010600001
   - 参数11：网关地址，内网请修改为自己的，外网可以不用传，默认的是*********:38422
   - 参数12：服务器profile地址，内网请修改为自己的，外网可以不用传，默认的是*********:30001


4. script/robot_analyse.sh

   作用：分析压测日志的脚本

   使用:  放到压测robot服~/game/bin下;./robot_analyse.sh

   参数：无

5. script/logic_analyse.sh

   作用：分析压测logic日志的脚本

   使用:  放到压测logic服~/game/bin下;./logic_analyse.sh或者./logic_analyse.sh 2021-06-02

   参数：

   - 参数1，可以不传，默认为当前日期。

