## 公会副本压测报告

####    不在公会的，会有一些没有公会的请求，在公会的只会有公会副本相关的请求

#### - 5000人

1. robot数据

   ```
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh
   2021-11-28 18:10:23 压测数据分析
   压测基础情况
   开始时间：2021/11/28 18:01:49, 结束时间:2021/11/28 18:06:39, 耗时:290(s)
   登录成功机器人总数：5000
   总消息数:318742， 平均每秒消息数:1099
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:13503,MSG_C2L_GuildGetMyInfo                      2579    2579            0.80%  100.00%
   协议:cmd:10100,                                            5000    5000            1.54%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            1.54%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  5000    5000            1.54%  100.00%
   协议:cmd:13905,MSG_C2L_GuildDungeonFight                  76537   79588           23.64%   96.17%
   协议:cmd:13501,MSG_C2L_GuildList                           3628    3628            1.12%  100.00%
   协议:cmd:13907,MSG_C2L_GuildDungeonUserDamageRank         79015   79015           24.41%  100.00%
   协议:cmd:13521,MSG_C2L_GuildUserApply                      2175    2175            0.67%  100.00%
   协议:cmd:13535,MSG_C2L_GuildGetDeclaration                  710     710            0.22%  100.00%
   协议:cmd:13903,MSG_C2L_GuildDungeonAwardReceive           41887   43212           12.94%   96.93%
   协议:cmd:13901,MSG_C2L_GuildDungeonInfo                   96708   96708           29.87%  100.00%
   协议:cmd:13531,MSG_C2L_GuildSearch                          723     723            0.22%  100.00%
   协议:cmd:13505,MSG_C2L_GuildCreate                          404     404            0.12%  100.00%
   协议正确数:319366 协议总数:323742 正确比:98.65%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   13.49%   6.82%  18.15%   9.11%   8.84%  43.58%   0.00%   0.00%
   协议:cmd:10100,                                          0.00%   0.24%   0.60%   2.06%   5.52%  15.10%  25.10%  51.38%
   协议:cmd:11003,MSG_C2L_Flush                             0.06%   0.30%   0.56%   2.70%   3.56%  11.26%  18.74%  62.82%
   协议:cmd:11025,MSG_C2L_GM                                0.02%   0.34%   0.56%   2.68%   3.58%  11.18%  18.76%  62.88%
   协议:cmd:13905,MSG_C2L_GuildDungeonFight                 2.67%   4.60%  14.32%  29.71%  44.75%   3.95%   0.00%   0.00%
   协议:cmd:13501,MSG_C2L_GuildList                        15.13%  10.64%  13.37%  12.71%  16.79%  31.37%   0.00%   0.00%
   协议:cmd:13907,MSG_C2L_GuildDungeonUserDamageRank        2.87%   4.65%  14.32%  29.48%  44.61%   4.06%   0.00%   0.00%
   协议:cmd:13521,MSG_C2L_GuildUserApply                   11.95%   6.67%  11.59%   8.14%   4.51%  57.15%   0.00%   0.00%
   协议:cmd:13535,MSG_C2L_GuildGetDeclaration              12.25%   7.46%  13.24%   7.32%   3.66%  56.06%   0.00%   0.00%
   协议:cmd:13903,MSG_C2L_GuildDungeonAwardReceive          3.57%   5.41%  15.95%  30.92%  40.87%   3.28%   0.00%   0.00%
   协议:cmd:13901,MSG_C2L_GuildDungeonInfo                  3.69%   4.23%  14.49%  30.38%  43.15%   4.06%   0.00%   0.00%
   协议:cmd:13531,MSG_C2L_GuildSearch                      11.76%   7.19%  11.89%   6.36%   4.98%  57.81%   0.00%   0.00%
   协议:cmd:13505,MSG_C2L_GuildCreate                      15.59%  11.63%  13.37%   9.65%  17.82%  31.93%   0.00%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. 游戏数据

   ```
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh
   
   --------------------------
   
   所有超时的left
   [2021/11/28 18:01:28.207105] gate(pck/s: 1636 sum: 2865 left:1036) logic db(pck/s: 706 sum: 1263 left:1) redis db(pck/s: 706 sum: 1273 left:52) [2021/11/28 18:01:29.104979] gate(pck/s: 1674 sum: 4539 left:1606) logic db(pck/s: 709 sum: 1972 left:8) redis db(pck/s: 717 sum: 1990 left:64) [2021/11/28 18:01:30.313195] gate(pck/s: 1892 sum: 6431 left:2466) logic db(pck/s: 774 sum: 2746 left:0) redis db(pck/s: 767 sum: 2757 left:28) [2021/11/28 18:01:31.212513] gate(pck/s: 1627 sum: 8058 left:3086) logic db(pck/s: 672 sum: 3418 left:0) redis db(pck/s: 673 sum: 3430 left:26) [2021/11/28 18:01:32.117536] gate(pck/s: 1685 sum: 9743 left:3259) logic db(pck/s: 727 sum: 4145 left:0) redis db(pck/s: 728 sum: 4158 left:0) [2021/11/28 18:01:33.321041] gate(pck/s: 2150 sum: 11893 left:2757) logic db(pck/s: 826 sum: 4971 left:2) redis db(pck/s: 829 sum: 4987 left:71) [2021/11/28 18:01:34.219211] gate(pck/s: 984 sum: 12877 left:2125) logic db(pck/s: 174 sum: 5145 left:0) redis db(pck/s: 173 sum: 5160 left:0) [2021/11/28 18:01:37.226530] gate(pck/s: 9641 sum: 30427 left:1370) logic db(pck/s: 0 sum: 5145 left:0) redis db(pck/s: 2 sum: 5166 left:0)
   
   --------------------------
   
   logic服务器启动时间:1.45505592s
   logic账号总数:5000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:      100006(  18)                                0.00%  38.89%  44.44%  16.67%   0.00%   0.00%   0.00%
   协议:       13907(   5)                               20.00%  20.00%  60.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100043( 308)                                0.00%  38.31%  60.39%   1.30%   0.00%   0.00%   0.00%
   协议:      100007(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100002(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13905(  97)                               61.86%  10.31%  23.71%   4.12%   0.00%   0.00%   0.00%
   ```

3. metric

   ![](./pic/guilddungeon-********-01.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-**************
   File: service
   Type: inuse_objects
   Time: Nov 28, 2021 at 6:04pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 9295994, 80.56% of ******** total
   Dropped 208 nodes (cum <= 57694)
   Showing top 20 nodes out of 92
         flat  flat%   sum%        cum   cum%
      1119609  9.70%  9.70%    1119609  9.70%  app/logic/character.(*GemM).Load
       884776  7.67% 17.37%     884776  7.67%  app/protos/out/cl.(*EmblemInfo).Unmarshal
       819250  7.10% 24.47%    1704026 14.77%  app/logic/db/redisop.ClClient.GetSomeEmblemInfoByReply
       799600  6.93% 31.40%    2085799 18.08%  app/logic/db/redisop.ClClient.GetSomeGemInfoByReply
       794672  6.89% 38.29%    1286199 11.15%  app/protos/out/cl.(*GemInfo).Unmarshal
       557090  4.83% 43.12%     557090  4.83%  app/protos/out/cl.(*FormationInfo).Clone (inline)
       491527  4.26% 47.38%     491527  4.26%  app/protos/out/cl.(*GemAttr).Unmarshal
       428614  3.71% 51.09%     626561  5.43%  app/protos/in/db.(*ModuleAttr).Unmarshal
       401167  3.48% 54.57%     401167  3.48%  app/logic/character.(*EquipM).Load
       360464  3.12% 57.69%    1267095 10.98%  app/protos/out/cl.(*Formation).Clone
       349541  3.03% 60.72%     349541  3.03%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
       341013  2.96% 63.67%     341013  2.96%  app/protos/out/cl.(*Handbooks).Unmarshal
       332485  2.88% 66.56%     332485  2.88%  app/logic/character.(*EmblemM).Load
       308042  2.67% 69.23%     308042  2.67%  app/logic/db/redisop.ClClient.GetSomeEquipmentByReply
       288380  2.50% 71.73%     288380  2.50%  app/logic/character.initHeroFromData
       271524  2.35% 74.08%     271524  2.35%  app/protos/out/cl.(*HeroBody).Unmarshal
       224718  1.95% 76.03%     496242  4.30%  app/logic/db/redisop.ClClient.GetSomeHeroBodyByReply
       196612  1.70% 77.73%     196612  1.70%  app/protos/out/cl.(*HeroBody).Clone
       196609  1.70% 79.43%     196609  1.70%  app/logic/character.(*ArtifactM).load
       130301  1.13% 80.56%     130301  1.13%  app/protos/out/cl.(*Carnival).Unmarshal
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Nov 28, 2021 at 6:04pm (CST)
   Duration: 30.14s, Total samples = 14.84s (49.23%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 5.34s, 35.98% of 14.84s total
   Dropped 732 nodes (cum <= 0.07s)
   Showing top 20 nodes out of 284
         flat  flat%   sum%        cum   cum%
        1.16s  7.82%  7.82%      1.91s 12.87%  runtime.scanobject
        0.39s  2.63% 10.44%      0.39s  2.63%  runtime.futex
        0.35s  2.36% 12.80%      0.44s  2.96%  runtime.mapaccess1_fast32
        0.33s  2.22% 15.03%      1.39s  9.37%  app/logic/battle.(*PsSkillMgr).TriggerPsSkill
        0.33s  2.22% 17.25%      0.37s  2.49%  runtime.findObject
        0.32s  2.16% 19.41%      1.11s  7.48%  runtime.mallocgc
        0.32s  2.16% 21.56%      0.44s  2.96%  runtime.mapiternext
        0.32s  2.16% 23.72%      0.34s  2.29%  syscall.Syscall
        0.21s  1.42% 25.13%      0.21s  1.42%  runtime.markBits.isMarked (inline)
        0.20s  1.35% 26.48%      0.24s  1.62%  runtime.heapBitsSetType
        0.18s  1.21% 27.70%      0.18s  1.21%  runtime.epollwait
        0.16s  1.08% 28.77%      0.16s  1.08%  app/protos/out/cl.sovCl
        0.15s  1.01% 29.78%      1.17s  7.88%  app/logic/battle.(*OneSkillAttack).DoSkillHurt
        0.14s  0.94% 30.73%      0.14s  0.94%  runtime.duffzero
        0.14s  0.94% 31.67%      0.14s  0.94%  runtime.memmove
        0.14s  0.94% 32.61%      0.14s  0.94%  runtime.nextFreeFast (inline)
        0.13s  0.88% 33.49%      0.13s  0.88%  app/logic/character.(*User).ResetDaily
        0.13s  0.88% 34.37%      0.13s  0.88%  runtime.(*itabTableType).find
        0.12s  0.81% 35.18%      1.11s  7.48%  app/logic/battle.(*OneSkillAttack).prepare
        0.12s  0.81% 35.98%      0.12s  0.81%  runtime.nanotime
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Nov 28, 2021 at 6:04pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 3.57GB, 50.36% of 7.10GB total
   Dropped 671 nodes (cum <= 0.04GB)
   Showing top 20 nodes out of 205
         flat  flat%   sum%        cum   cum%
       0.56GB  7.88%  7.88%     0.56GB  7.88%  app/logic/db/redisop.ClClient.SetSomeGuildDungeonLogMCallSKs
       0.39GB  5.46% 13.34%     0.45GB  6.31%  app/logic/db/redisop.DbClient.SetUserMCallSKs
       0.37GB  5.26% 18.60%     0.40GB  5.65%  encoding/xml.(*Decoder).rawToken
       0.31GB  4.35% 22.95%     0.32GB  4.54%  gitlab.qdream.com/kit/sea/redis/resp.readBulkStr
       0.30GB  4.21% 27.16%     0.62GB  8.75%  gitlab.qdream.com/kit/sea/redis/resp.readArray
       0.19GB  2.71% 29.87%     0.19GB  2.71%  app/logic/battle.newAttrManager
       0.19GB  2.64% 32.51%     0.19GB  2.64%  gitlab.qdream.com/kit/sea/redis.messageToReply
       0.17GB  2.36% 34.87%     0.17GB  2.36%  app/protos/out/cl.(*HeroBody).Clone
       0.16GB  2.31% 37.18%     0.17GB  2.37%  github.com/gogo/protobuf/proto.Marshal
       0.11GB  1.58% 38.75%     0.15GB  2.16%  app/logic/battle.(*PsSkillMgr).CastPsSkill
       0.11GB  1.52% 40.28%     0.11GB  1.52%  app/logic/db/redisop.ClClient.SetSomeFormationMCallSKs
       0.10GB  1.36% 41.64%     0.10GB  1.36%  app/logic/activity/guild.(*Guild).Snapshot
       0.09GB  1.27% 42.91%     0.09GB  1.27%  app/protos/out/cl.(*FormationInfo).Clone
       0.08GB  1.18% 44.10%     0.08GB  1.18%  app/logic/character.(*User).NewUserSnapshot
       0.08GB  1.17% 45.27%     0.15GB  2.18%  app/logic/db/redisop.ClClient.GetSomeGemInfoByReply
       0.08GB  1.14% 46.40%     0.14GB  2.03%  app/protos/out/cl.(*EmblemInfo).Clone
       0.08GB  1.09% 47.49%     0.08GB  1.09%  app/protos/out/cl.(*TaskTypeProgress).Clone
       0.07GB  1.02% 48.51%     0.13GB  1.82%  app/logic/db/redisop.ClClient.GetSomeEmblemInfoByReply
       0.07GB  0.93% 49.44%     0.07GB  0.94%  app/logic/character.JSONMarshal
       0.07GB  0.92% 50.36%     0.12GB  1.74%  app/protos/out/cl.(*GemInfo).Clone
   (pprof)
   ```



#### - 3000人

1. robot数据

   ```
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh
   2021-11-28 20:53:48 压测数据分析
   压测基础情况
   开始时间：2021/11/28 20:47:33, 结束时间:2021/11/28 20:52:27, 耗时:294(s)
   登录成功机器人总数：3000
   总消息数:838647， 平均每秒消息数:2852
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:13503,MSG_C2L_GuildGetMyInfo                       149     150            0.02%   99.33%
   协议:cmd:10100,                                            3000    3000            0.36%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               3000    3000            0.36%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  3000    3000            0.36%  100.00%
   协议:cmd:13905,MSG_C2L_GuildDungeonFight                 245138  247919           29.13%   98.88%
   协议:cmd:13501,MSG_C2L_GuildList                            205     205            0.02%  100.00%
   协议:cmd:13521,MSG_C2L_GuildUserApply                       124     124            0.01%  100.00%
   协议:cmd:13535,MSG_C2L_GuildGetDeclaration                   38      38            0.00%  100.00%
   协议:cmd:13907,MSG_C2L_GuildDungeonUserDamageRank        247082  247102           29.36%   99.99%
   协议:cmd:13903,MSG_C2L_GuildDungeonAwardReceive           67951   68486            8.07%   99.22%
   协议:cmd:13901,MSG_C2L_GuildDungeonInfo                  268475  268554           31.90%   99.97%
   协议:cmd:13531,MSG_C2L_GuildSearch                           43      43            0.01%  100.00%
   协议:cmd:13505,MSG_C2L_GuildCreate                           26      26            0.00%  100.00%
   协议正确数:838231 协议总数:841647 正确比:99.59%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   94.63%   2.01%   3.36%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:10100,                                          0.00%   0.00%   1.37%   2.00%   6.77%  29.17%  40.80%  19.90%
   协议:cmd:11003,MSG_C2L_Flush                             0.17%   0.17%   1.23%   3.63%   4.80%  20.97%  34.27%  34.77%
   协议:cmd:11025,MSG_C2L_GM                                0.10%   0.20%   1.10%   3.77%   4.77%  21.00%  34.17%  34.90%
   协议:cmd:13905,MSG_C2L_GuildDungeonFight                 0.37%   1.04%   4.02%   7.74%  14.37%  52.23%  20.22%   0.01%
   协议:cmd:13501,MSG_C2L_GuildList                        99.51%   0.49%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13521,MSG_C2L_GuildUserApply                   97.58%   0.81%   1.61%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13535,MSG_C2L_GuildGetDeclaration              100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13907,MSG_C2L_GuildDungeonUserDamageRank        0.32%   0.93%   3.48%   7.15%  15.04%  52.13%  20.94%   0.01%
   协议:cmd:13903,MSG_C2L_GuildDungeonAwardReceive          0.50%   1.16%   4.37%   8.37%  15.59%  52.23%  17.76%   0.01%
   协议:cmd:13901,MSG_C2L_GuildDungeonInfo                  1.44%   1.05%   4.08%   7.52%  13.97%  52.02%  19.91%   0.01%
   协议:cmd:13531,MSG_C2L_GuildSearch                      100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13505,MSG_C2L_GuildCreate                      96.15%   0.00%   3.85%   0.00%   0.00%   0.00%   0.00%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. logic数据

   ```
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh
   
   --------------------------
   
   所有超时的left
   [2021/11/28 20:47:16.265568] gate(pck/s: 2107 sum: 3497 left:1373) logic db(pck/s: 975 sum: 1691 left:0) redis db(pck/s: 972 sum: 1700 left:26) [2021/11/28 20:47:17.165478] gate(pck/s: 1638 sum: 5135 left:1979) logic db(pck/s: 791 sum: 2482 left:17) redis db(pck/s: 809 sum: 2509 left:25) [2021/11/28 20:47:18.064245] gate(pck/s: 1651 sum: 6786 left:1884) logic db(pck/s: 851 sum: 3333 left:0) redis db(pck/s: 835 sum: 3344 left:0) [2021/11/28 20:47:22.279566] gate(pck/s: 3774 sum: 16487 left:1481) logic db(pck/s: 920 sum: 4570 left:0) redis db(pck/s: 1246 sum: 4942 left:0) [2021/11/28 20:47:23.178974] gate(pck/s: 2548 sum: 19035 left:1715) logic db(pck/s: 593 sum: 5163 left:0) redis db(pck/s: 813 sum: 5755 left:0) [2021/11/28 20:47:24.077783] gate(pck/s: 3024 sum: 22059 left:1807) logic db(pck/s: 589 sum: 5752 left:0) redis db(pck/s: 778 sum: 6533 left:0) [2021/11/28 20:47:25.256210] gate(pck/s: 6044 sum: 28103 left:1602) logic db(pck/s: 865 sum: 6617 left:0) redis db(pck/s: 1089 sum: 7622 left:4) [2021/11/28 20:47:26.155488] gate(pck/s: 2443 sum: 30546 left:1540) logic db(pck/s: 595 sum: 7212 left:2) redis db(pck/s: 754 sum: 8376 left:0) [2021/11/28 20:47:28.266411] gate(pck/s: 3640 sum: 36739 left:1579) logic db(pck/s: 971 sum: 8843 left:0) redis db(pck/s: 1176 sum: 10361 left:5) [2021/11/28 20:47:29.162062] gate(pck/s: 2386 sum: 39125 left:1670) logic db(pck/s: 582 sum: 9425 left:3) redis db(pck/s: 734 sum: 11095 left:0) [2021/11/28 20:47:31.270185] gate(pck/s: 3634 sum: 45308 left:1601) logic db(pck/s: 951 sum: 11029 left:0) redis db(pck/s: 1181 sum: 13097 left:11) [2021/11/28 20:47:32.168856] gate(pck/s: 2450 sum: 47758 left:1663) logic db(pck/s: 636 sum: 11665 left:2) redis db(pck/s: 768 sum: 13865 left:0) [2021/11/28 20:47:34.277270] gate(pck/s: 4771 sum: 55142 left:3316) logic db(pck/s: 900 sum: 13263 left:0) redis db(pck/s: 1084 sum: 15773 left:22) [2021/11/28 20:47:35.175966] gate(pck/s: 4183 sum: 59325 left:1749) logic db(pck/s: 654 sum: 13917 left:2) redis db(pck/s: 777 sum: 16550 left:0) [2021/11/28 20:47:37.256574] gate(pck/s: 3472 sum: 65296 left:1645) logic db(pck/s: 954 sum: 15509 left:4) redis db(pck/s: 1132 sum: 18415 left:0) [2021/11/28 20:47:38.151536] gate(pck/s: 2420 sum: 67716 left:1507) logic db(pck/s: 658 sum: 16167 left:3) redis db(pck/s: 771 sum: 19186 left:0) [2021/11/28 20:47:40.259848] gate(pck/s: 3481 sum: 73843 left:1568) logic db(pck/s: 926 sum: 17810 left:0) redis db(pck/s: 1131 sum: 21149 left:11) [2021/11/28 20:47:41.159369] gate(pck/s: 2298 sum: 76141 left:1679) logic db(pck/s: 626 sum: 18436 left:0) redis db(pck/s: 734 sum: 21883 left:0) [2021/11/28 20:47:43.266722] gate(pck/s: 3319 sum: 82024 left:1885) logic db(pck/s: 857 sum: 19942 left:0) redis db(pck/s: 1073 sum: 23755 left:5) [2021/11/28 20:47:44.165593] gate(pck/s: 3674 sum: 85698 left:3324) logic db(pck/s: 768 sum: 20710 left:3) redis db(pck/s: 923 sum: 24678 left:0) [2021/11/28 20:47:46.273866] gate(pck/s: 3608 sum: 93828 left:1583) logic db(pck/s: 1007 sum: 22322 left:0) redis db(pck/s: 1196 sum: 26593 left:29) [2021/11/28 20:47:47.173539] gate(pck/s: 2505 sum: 96333 left:1671) logic db(pck/s: 681 sum: 23003 left:4) redis db(pck/s: 817 sum: 27410 left:1) [2021/11/28 20:47:49.281094] gate(pck/s: 3466 sum: 102265 left:1663) logic db(pck/s: 876 sum: 24504 left:0) redis db(pck/s: 1048 sum: 29212 left:21) [2021/11/28 20:47:50.180727] gate(pck/s: 2283 sum: 104548 left:2066) logic db(pck/s: 599 sum: 25103 left:2) redis db(pck/s: 720 sum: 29932 left:14) [2021/11/28 20:47:52.257588] gate(pck/s: 3396 sum: 110785 left:1694) logic db(pck/s: 926 sum: 26741 left:0) redis db(pck/s: 1101 sum: 31896 left:0) [2021/11/28 20:47:53.156409] gate(pck/s: 2524 sum: 113309 left:1436) logic db(pck/s: 648 sum: 27389 left:0) redis db(pck/s: 773 sum: 32669 left:0) [2021/11/28 20:47:54.056416] gate(pck/s: 2924 sum: 116233 left:1427) logic db(pck/s: 691 sum: 28080 left:1) redis db(pck/s: 800 sum: 33469 left:0) [2021/11/28 20:47:55.264063] gate(pck/s: 6067 sum: 122300 left:1753) logic db(pck/s: 870 sum: 28950 left:0) redis db(pck/s: 1049 sum: 34518 left:10) [2021/11/28 20:47:56.162841] gate(pck/s: 2432 sum: 124732 left:1796) logic db(pck/s: 642 sum: 29592 left:5) redis db(pck/s: 760 sum: 35278 left:0) [2021/11/28 20:47:58.271003] gate(pck/s: 3397 sum: 130762 left:1831) logic db(pck/s: 875 sum: 31135 left:0) redis db(pck/s: 1013 sum: 37060 left:8) [2021/11/28 20:47:59.173180] gate(pck/s: 2672 sum: 133434 left:1797) logic db(pck/s: 723 sum: 31858 left:1) redis db(pck/s: 853 sum: 37913 left:1) [2021/11/28 20:48:01.278566] gate(pck/s: 3355 sum: 139314 left:1867) logic db(pck/s: 885 sum: 33405 left:0) redis db(pck/s: 1069 sum: 39763 left:18) [2021/11/28 20:48:02.176571] gate(pck/s: 2776 sum: 142090 left:1787) logic db(pck/s: 723 sum: 34128 left:1) redis db(pck/s: 890 sum: 40653 left:0) [2021/11/28 20:48:04.259475] gate(pck/s: 4490 sum: 149074 left:3735) logic db(pck/s: 938 sum: 35688 left:2) redis db(pck/s: 1127 sum: 42515 left:0) [2021/11/28 20:48:05.152693] gate(pck/s: 4532 sum: 153606 left:1498) logic db(pck/s: 670 sum: 36358 left:3) redis db(pck/s: 1288 sum: 43803 left:0) [2021/11/28 20:48:07.260843] gate(pck/s: 3404 sum: 159505 left:1767) logic db(pck/s: 881 sum: 37900 left:0) redis db(pck/s: 1778 sum: 47194 left:1) [2021/11/28 20:48:08.160704] gate(pck/s: 2553 sum: 162058 left:1668) logic db(pck/s: 650 sum: 38550 left:0) redis db(pck/s: 1461 sum: 48655 left:0) [2021/11/28 20:48:10.268255] gate(pck/s: 3584 sum: 168218 left:1550) logic db(pck/s: 901 sum: 40124 left:0) redis db(pck/s: 1109 sum: 50806 left:13) [2021/11/28 20:48:11.166800] gate(pck/s: 2403 sum: 170621 left:1628) logic db(pck/s: 634 sum: 40758 left:3) redis db(pck/s: 738 sum: 51544 left:0) [2021/11/28 20:48:13.274549] gate(pck/s: 3611 sum: 176736 left:1515) logic db(pck/s: 908 sum: 42361 left:0) redis db(pck/s: 1084 sum: 53434 left:27) [2021/11/28 20:48:14.173595] gate(pck/s: 3232 sum: 179968 left:3642) logic db(pck/s: 677 sum: 43038 left:0) redis db(pck/s: 767 sum: 54201 left:0) [2021/11/28 20:48:16.282368] gate(pck/s: 3384 sum: 187989 left:1693) logic db(pck/s: 905 sum: 44584 left:0) redis db(pck/s: 1045 sum: 55982 left:35) [2021/11/28 20:48:17.180930] gate(pck/s: 2602 sum: 190591 left:1800) logic db(pck/s: 693 sum: 45277 left:2) redis db(pck/s: 809 sum: 56791 left:0) [2021/11/28 20:48:19.258295] gate(pck/s: 3387 sum: 196501 left:1675) logic db(pck/s: 947 sum: 46890 left:0) redis db(pck/s: 1112 sum: 58700 left:4) [2021/11/28 20:48:20.157323] gate(pck/s: 2510 sum: 199011 left:1574) logic db(pck/s: 689 sum: 47579 left:3) redis db(pck/s: 797 sum: 59497 left:0) [2021/11/28 20:48:22.265992] gate(pck/s: 3636 sum: 205088 left:1612) logic db(pck/s: 912 sum: 49151 left:0) redis db(pck/s: 1104 sum: 61367 left:13) [2021/11/28 20:48:23.164473] gate(pck/s: 2471 sum: 207559 left:1649) logic db(pck/s: 644 sum: 49795 left:4) redis db(pck/s: 790 sum: 62157 left:0) [2021/11/28 20:48:24.063317] gate(pck/s: 2936 sum: 210495 left:1589) logic db(pck/s: 647 sum: 50442 left:0) redis db(pck/s: 777 sum: 62934 left:4) [2021/11/28 20:48:25.281320] gate(pck/s: 6176 sum: 216671 left:1575) logic db(pck/s: 926 sum: 51368 left:0) redis db(pck/s: 1125 sum: 64059 left:4) [2021/11/28 20:48:26.171124] gate(pck/s: 2416 sum: 219087 left:1740) logic db(pck/s: 627 sum: 51995 left:2) redis db(pck/s: 759 sum: 64818 left:0) [2021/11/28 20:48:28.278556] gate(pck/s: 3512 sum: 225114 left:1662) logic db(pck/s: 930 sum: 53571 left:0) redis db(pck/s: 1109 sum: 66709 left:25) [2021/11/28 20:48:29.177837] gate(pck/s: 2555 sum: 227669 left:1791) logic db(pck/s: 663 sum: 54234 left:0) redis db(pck/s: 805 sum: 67514 left:2) [2021/11/28 20:48:31.257022] gate(pck/s: 3296 sum: 233502 left:1788) logic db(pck/s: 884 sum: 55769 left:5) redis db(pck/s: 1040 sum: 69308 left:0) [2021/11/28 20:48:32.154309] gate(pck/s: 2579 sum: 236081 left:1574) logic db(pck/s: 690 sum: 56459 left:1) redis db(pck/s: 833 sum: 70141 left:0) [2021/11/28 20:48:34.263195] gate(pck/s: 4761 sum: 243353 left:3419) logic db(pck/s: 992 sum: 58125 left:0) redis db(pck/s: 1140 sum: 72039 left:5) [2021/11/28 20:48:35.161242] gate(pck/s: 4278 sum: 247631 left:1601) logic db(pck/s: 666 sum: 58791 left:0) redis db(pck/s: 749 sum: 72788 left:0) [2021/11/28 20:48:37.269070] gate(pck/s: 3538 sum: 253726 left:1561) logic db(pck/s: 916 sum: 60418 left:0) redis db(pck/s: 1054 sum: 74693 left:7) [2021/11/28 20:48:38.168153] gate(pck/s: 2434 sum: 256160 left:1640) logic db(pck/s: 653 sum: 61071 left:3) redis db(pck/s: 757 sum: 75450 left:2) [2021/11/28 20:48:40.275716] gate(pck/s: 3574 sum: 262249 left:1534) logic db(pck/s: 979 sum: 62709 left:0) redis db(pck/s: 1157 sum: 77372 left:17) [2021/11/28 20:48:41.176473] gate(pck/s: 2036 sum: 264285 left:2148) logic db(pck/s: 490 sum: 63199 left:1) redis db(pck/s: 580 sum: 77952 left:67) [2021/11/28 20:48:42.073571] gate(pck/s: 2698 sum: 266983 left:1051) logic db(pck/s: 759 sum: 63958 left:0) redis db(pck/s: 904 sum: 78856 left:12) [2021/11/28 20:48:43.259122] gate(pck/s: 3702 sum: 270685 left:1615) logic db(pck/s: 1015 sum: 64973 left:2) redis db(pck/s: 1175 sum: 80031 left:0) [2021/11/28 20:48:44.152681] gate(pck/s: 2937 sum: 273622 left:3467) logic db(pck/s: 604 sum: 65577 left:1) redis db(pck/s: 689 sum: 80720 left:0) [2021/11/28 20:48:46.262555] gate(pck/s: 3408 sum: 282090 left:1689) logic db(pck/s: 936 sum: 67211 left:0) redis db(pck/s: 1131 sum: 82671 left:0) [2021/11/28 20:48:47.157603] gate(pck/s: 2285 sum: 284375 left:1843) logic db(pck/s: 603 sum: 67814 left:8) redis db(pck/s: 703 sum: 83374 left:0) [2021/11/28 20:48:49.266293] gate(pck/s: 3641 sum: 290733 left:1586) logic db(pck/s: 938 sum: 69460 left:0) redis db(pck/s: 1134 sum: 85369 left:11) [2021/11/28 20:48:50.166282] gate(pck/s: 2428 sum: 293161 left:1651) logic db(pck/s: 617 sum: 70077 left:3) redis db(pck/s: 730 sum: 86099 left:0) [2021/11/28 20:48:52.273074] gate(pck/s: 3414 sum: 299081 left:1742) logic db(pck/s: 906 sum: 71643 left:0) redis db(pck/s: 1069 sum: 87925 left:15) [2021/11/28 20:48:53.172015] gate(pck/s: 2641 sum: 301722 left:1701) logic db(pck/s: 733 sum: 72376 left:3) redis db(pck/s: 836 sum: 88761 left:0) [2021/11/28 20:48:54.071214] gate(pck/s: 2874 sum: 304596 left:1788) logic db(pck/s: 672 sum: 73048 left:0) redis db(pck/s: 764 sum: 89525 left:7) [2021/11/28 20:48:55.279971] gate(pck/s: 6109 sum: 310705 left:1609) logic db(pck/s: 971 sum: 74019 left:0) redis db(pck/s: 1624 sum: 91149 left:19) [2021/11/28 20:48:56.179188] gate(pck/s: 2502 sum: 313207 left:1809) logic db(pck/s: 668 sum: 74687 left:6) redis db(pck/s: 1529 sum: 92678 left:0) [2021/11/28 20:48:57.078186] gate(pck/s: 2297 sum: 315504 left:1145) logic db(pck/s: 590 sum: 75277 left:0) redis db(pck/s: 1451 sum: 94129 left:17) [2021/11/28 20:48:58.258818] gate(pck/s: 3308 sum: 318812 left:2017) logic db(pck/s: 885 sum: 76162 left:0) redis db(pck/s: 1792 sum: 95921 left:0) [2021/11/28 20:48:59.155308] gate(pck/s: 2687 sum: 321499 left:1730) logic db(pck/s: 714 sum: 76876 left:0) redis db(pck/s: 1059 sum: 96980 left:0) [2021/11/28 20:49:01.263569] gate(pck/s: 3359 sum: 327461 left:1847) logic db(pck/s: 908 sum: 78492 left:0) redis db(pck/s: 1061 sum: 98903 left:7) [2021/11/28 20:49:02.162636] gate(pck/s: 2602 sum: 330063 left:1728) logic db(pck/s: 738 sum: 79230 left:1) redis db(pck/s: 842 sum: 99745 left:0) [2021/11/28 20:49:04.270597] gate(pck/s: 4524 sum: 337162 left:3647) logic db(pck/s: 926 sum: 80867 left:0) redis db(pck/s: 1100 sum: 101664 left:11) [2021/11/28 20:49:05.169135] gate(pck/s: 4509 sum: 341671 left:1712) logic db(pck/s: 685 sum: 81552 left:2) redis db(pck/s: 809 sum: 102473 left:0) [2021/11/28 20:49:07.277140] gate(pck/s: 3526 sum: 347687 left:1642) logic db(pck/s: 944 sum: 83141 left:0) redis db(pck/s: 1096 sum: 104323 left:5) [2021/11/28 20:49:08.175542] gate(pck/s: 2572 sum: 350259 left:1738) logic db(pck/s: 708 sum: 83849 left:0) redis db(pck/s: 816 sum: 105139 left:0) [2021/11/28 20:49:10.257938] gate(pck/s: 3362 sum: 356116 left:1697) logic db(pck/s: 922 sum: 85443 left:2) redis db(pck/s: 1079 sum: 106986 left:0) [2021/11/28 20:49:11.151568] gate(pck/s: 2561 sum: 358677 left:1466) logic db(pck/s: 694 sum: 86137 left:0) redis db(pck/s: 807 sum: 107793 left:5) [2021/11/28 20:49:13.259966] gate(pck/s: 3353 sum: 364492 left:1792) logic db(pck/s: 921 sum: 87761 left:0) redis db(pck/s: 1048 sum: 109645 left:2) [2021/11/28 20:49:14.159012] gate(pck/s: 3397 sum: 367889 left:3425) logic db(pck/s: 729 sum: 88490 left:2) redis db(pck/s: 849 sum: 110494 left:0) [2021/11/28 20:49:16.270035] gate(pck/s: 3479 sum: 376074 left:1671) logic db(pck/s: 910 sum: 90087 left:0) redis db(pck/s: 1057 sum: 112341 left:1) [2021/11/28 20:49:17.165938] gate(pck/s: 2502 sum: 378576 left:1669) logic db(pck/s: 670 sum: 90757 left:0) redis db(pck/s: 787 sum: 113128 left:0) [2021/11/28 20:49:19.273691] gate(pck/s: 3434 sum: 384549 left:1779) logic db(pck/s: 882 sum: 92330 left:0) redis db(pck/s: 1078 sum: 115020 left:7) [2021/11/28 20:49:20.172731] gate(pck/s: 2725 sum: 387274 left:1663) logic db(pck/s: 727 sum: 93057 left:2) redis db(pck/s: 889 sum: 115909 left:0) [2021/11/28 20:49:22.280594] gate(pck/s: 3592 sum: 393247 left:1552) logic db(pck/s: 966 sum: 94661 left:0) redis db(pck/s: 1132 sum: 117773 left:30) [2021/11/28 20:49:23.179893] gate(pck/s: 2350 sum: 395597 left:1907) logic db(pck/s: 634 sum: 95295 left:0) redis db(pck/s: 725 sum: 118498 left:0) [2021/11/28 20:49:24.078616] gate(pck/s: 3037 sum: 398634 left:2005) logic db(pck/s: 640 sum: 95935 left:0) redis db(pck/s: 725 sum: 119223 left:13) [2021/11/28 20:49:25.257650] gate(pck/s: 5839 sum: 404473 left:1770) logic db(pck/s: 951 sum: 96886 left:1) redis db(pck/s: 1082 sum: 120305 left:0) [2021/11/28 20:49:26.156431] gate(pck/s: 2545 sum: 407018 left:1724) logic db(pck/s: 697 sum: 97583 left:0) redis db(pck/s: 831 sum: 121136 left:0) [2021/11/28 20:49:28.264392] gate(pck/s: 3648 sum: 413075 left:1671) logic db(pck/s: 1010 sum: 99256 left:0) redis db(pck/s: 1175 sum: 123076 left:6) [2021/11/28 20:49:29.163829] gate(pck/s: 2538 sum: 415613 left:1613) logic db(pck/s: 674 sum: 99930 left:1) redis db(pck/s: 792 sum: 123868 left:0) [2021/11/28 20:49:31.271367] gate(pck/s: 3557 sum: 421687 left:1545) logic db(pck/s: 965 sum: 101539 left:0) redis db(pck/s: 1098 sum: 125715 left:17) [2021/11/28 20:49:32.170640] gate(pck/s: 2324 sum: 424011 left:1809) logic db(pck/s: 655 sum: 102194 left:0) redis db(pck/s: 755 sum: 126470 left:0) [2021/11/28 20:49:34.277565] gate(pck/s: 4614 sum: 431198 left:3529) logic db(pck/s: 955 sum: 103828 left:0) redis db(pck/s: 1104 sum: 128362 left:19) [2021/11/28 20:49:35.177159] gate(pck/s: 4467 sum: 435665 left:1776) logic db(pck/s: 680 sum: 104508 left:3) redis db(pck/s: 821 sum: 129183 left:0) [2021/11/28 20:49:37.256793] gate(pck/s: 3405 sum: 441535 left:1690) logic db(pck/s: 946 sum: 106088 left:1) redis db(pck/s: 1082 sum: 130984 left:0) [2021/11/28 20:49:38.152682] gate(pck/s: 2401 sum: 443936 left:1657) logic db(pck/s: 660 sum: 106748 left:1) redis db(pck/s: 762 sum: 131746 left:0) [2021/11/28 20:49:40.261500] gate(pck/s: 3405 sum: 449974 left:1748) logic db(pck/s: 927 sum: 108400 left:1) redis db(pck/s: 1079 sum: 133660 left:0) [2021/11/28 20:49:41.159805] gate(pck/s: 2558 sum: 452532 left:1643) logic db(pck/s: 712 sum: 109112 left:0) redis db(pck/s: 810 sum: 134470 left:3) [2021/11/28 20:49:43.268111] gate(pck/s: 3528 sum: 458548 left:1638) logic db(pck/s: 983 sum: 110797 left:0) redis db(pck/s: 1116 sum: 136391 left:8) [2021/11/28 20:49:44.167478] gate(pck/s: 3303 sum: 461851 left:3530) logic db(pck/s: 656 sum: 111453 left:0) redis db(pck/s: 757 sum: 137148 left:0) [2021/11/28 20:49:46.274665] gate(pck/s: 3355 sum: 469955 left:1716) logic db(pck/s: 912 sum: 113041 left:0) redis db(pck/s: 1857 sum: 140232 left:17) [2021/11/28 20:49:47.173888] gate(pck/s: 2288 sum: 472243 left:2074) logic db(pck/s: 664 sum: 113705 left:0) redis db(pck/s: 1457 sum: 141689 left:0) [2021/11/28 20:49:48.073064] gate(pck/s: 2658 sum: 474901 left:1035) logic db(pck/s: 691 sum: 114396 left:0) redis db(pck/s: 1530 sum: 143219 left:15) [2021/11/28 20:49:49.282280] gate(pck/s: 3576 sum: 478477 left:1672) logic db(pck/s: 976 sum: 115372 left:0) redis db(pck/s: 1396 sum: 144615 left:13) [2021/11/28 20:49:50.181698] gate(pck/s: 2443 sum: 480920 left:1981) logic db(pck/s: 672 sum: 116044 left:0) redis db(pck/s: 812 sum: 145427 left:0) [2021/11/28 20:49:51.080971] gate(pck/s: 2602 sum: 483522 left:1006) logic db(pck/s: 702 sum: 116746 left:0) redis db(pck/s: 825 sum: 146252 left:16) [2021/11/28 20:49:52.258360] gate(pck/s: 3256 sum: 486778 left:1860) logic db(pck/s: 903 sum: 117649 left:1) redis db(pck/s: 1045 sum: 147297 left:0) [2021/11/28 20:49:53.156655] gate(pck/s: 2728 sum: 489506 left:1557) logic db(pck/s: 690 sum: 118339 left:0) redis db(pck/s: 793 sum: 148090 left:0) [2021/11/28 20:49:54.061986] gate(pck/s: 2814 sum: 492320 left:1641) logic db(pck/s: 657 sum: 118996 left:1) redis db(pck/s: 747 sum: 148837 left:0) [2021/11/28 20:49:55.264687] gate(pck/s: 6041 sum: 498361 left:1755) logic db(pck/s: 943 sum: 119939 left:0) redis db(pck/s: 1105 sum: 149942 left:5) [2021/11/28 20:49:56.164131] gate(pck/s: 2603 sum: 500964 left:1613) logic db(pck/s: 673 sum: 120612 left:5) redis db(pck/s: 789 sum: 150731 left:0) [2021/11/28 20:49:58.271945] gate(pck/s: 3465 sum: 506826 left:1725) logic db(pck/s: 908 sum: 122175 left:0) redis db(pck/s: 1027 sum: 152501 left:5) [2021/11/28 20:49:59.171568] gate(pck/s: 2512 sum: 509338 left:1820) logic db(pck/s: 702 sum: 122877 left:0) redis db(pck/s: 804 sum: 153305 left:0) [2021/11/28 20:50:01.287343] gate(pck/s: 3610 sum: 515476 left:1550) logic db(pck/s: 979 sum: 124581 left:0) redis db(pck/s: 1121 sum: 155236 left:9) [2021/11/28 20:50:02.178064] gate(pck/s: 2183 sum: 517659 left:2089) logic db(pck/s: 593 sum: 125174 left:1) redis db(pck/s: 699 sum: 155935 left:0) [2021/11/28 20:50:04.258483] gate(pck/s: 4659 sum: 525049 left:3493) logic db(pck/s: 943 sum: 126845 left:0) redis db(pck/s: 1127 sum: 157921 left:0) [2021/11/28 20:50:05.154011] gate(pck/s: 4286 sum: 529335 left:1620) logic db(pck/s: 663 sum: 127508 left:1) redis db(pck/s: 773 sum: 158694 left:0) [2021/11/28 20:50:07.262679] gate(pck/s: 3299 sum: 535162 left:1857) logic db(pck/s: 935 sum: 129102 left:0) redis db(pck/s: 1077 sum: 160545 left:0) [2021/11/28 20:50:08.161415] gate(pck/s: 2577 sum: 537739 left:1747) logic db(pck/s: 745 sum: 129847 left:0) redis db(pck/s: 845 sum: 161390 left:0) [2021/11/28 20:50:10.268969] gate(pck/s: 3554 sum: 543872 left:1607) logic db(pck/s: 969 sum: 131521 left:0) redis db(pck/s: 1098 sum: 163274 left:12) [2021/11/28 20:50:11.168191] gate(pck/s: 2447 sum: 546319 left:1704) logic db(pck/s: 667 sum: 132188 left:2) redis db(pck/s: 747 sum: 164021 left:0) [2021/11/28 20:50:13.275942] gate(pck/s: 3564 sum: 552300 left:1674) logic db(pck/s: 922 sum: 133754 left:0) redis db(pck/s: 1049 sum: 165794 left:11) [2021/11/28 20:50:14.174508] gate(pck/s: 3513 sum: 555813 left:3609) logic db(pck/s: 696 sum: 134450 left:0) redis db(pck/s: 789 sum: 166583 left:3) [2021/11/28 20:50:16.257483] gate(pck/s: 3361 sum: 563622 left:1743) logic db(pck/s: 912 sum: 135973 left:1) redis db(pck/s: 1038 sum: 168322 left:0) [2021/11/28 20:50:17.151735] gate(pck/s: 2432 sum: 566054 left:1671) logic db(pck/s: 664 sum: 136637 left:2) redis db(pck/s: 767 sum: 169089 left:0) [2021/11/28 20:50:18.081455] gate(pck/s: 2683 sum: 568737 left:1057) logic db(pck/s: 708 sum: 137345 left:0) redis db(pck/s: 830 sum: 169919 left:14) [2021/11/28 20:50:19.259715] gate(pck/s: 3288 sum: 572025 left:1836) logic db(pck/s: 920 sum: 138265 left:3) redis db(pck/s: 1058 sum: 170977 left:0) [2021/11/28 20:50:20.157703] gate(pck/s: 2588 sum: 574613 left:1711) logic db(pck/s: 675 sum: 138940 left:0) redis db(pck/s: 773 sum: 171750 left:0) [2021/11/28 20:50:22.266487] gate(pck/s: 3214 sum: 580361 left:2008) logic db(pck/s: 848 sum: 140450 left:0) redis db(pck/s: 1005 sum: 173513 left:4) [2021/11/28 20:50:23.164604] gate(pck/s: 2911 sum: 583272 left:1649) logic db(pck/s: 762 sum: 141212 left:2) redis db(pck/s: 894 sum: 174407 left:0) [2021/11/28 20:50:24.064550] gate(pck/s: 2968 sum: 586240 left:1590) logic db(pck/s: 697 sum: 141909 left:0) redis db(pck/s: 794 sum: 175201 left:1) [2021/11/28 20:50:25.272823] gate(pck/s: 5990 sum: 592230 left:1647) logic db(pck/s: 917 sum: 142826 left:0) redis db(pck/s: 1053 sum: 176254 left:14) [2021/11/28 20:50:26.172825] gate(pck/s: 2473 sum: 594703 left:1811) logic db(pck/s: 695 sum: 143521 left:3) redis db(pck/s: 790 sum: 177044 left:0) [2021/11/28 20:50:27.080998] gate(pck/s: 2460 sum: 597163 left:1091) logic db(pck/s: 661 sum: 144182 left:0) redis db(pck/s: 752 sum: 177796 left:3) [2021/11/28 20:50:28.279804] gate(pck/s: 3194 sum: 600357 left:1977) logic db(pck/s: 832 sum: 145014 left:0) redis db(pck/s: 963 sum: 178759 left:15) [2021/11/28 20:50:29.178629] gate(pck/s: 2940 sum: 603297 left:1773) logic db(pck/s: 820 sum: 145834 left:3) redis db(pck/s: 941 sum: 179700 left:0) [2021/11/28 20:50:31.258172] gate(pck/s: 3432 sum: 609144 left:1633) logic db(pck/s: 930 sum: 147386 left:1) redis db(pck/s: 1080 sum: 181491 left:0) [2021/11/28 20:50:32.154911] gate(pck/s: 2330 sum: 611474 left:1732) logic db(pck/s: 628 sum: 148014 left:1) redis db(pck/s: 711 sum: 182202 left:0) [2021/11/28 20:50:34.263422] gate(pck/s: 4520 sum: 618610 left:3652) logic db(pck/s: 906 sum: 149655 left:0) redis db(pck/s: 1064 sum: 184103 left:3) [2021/11/28 20:50:35.162559] gate(pck/s: 4368 sum: 622978 left:1770) logic db(pck/s: 647 sum: 150302 left:0) redis db(pck/s: 1117 sum: 185220 left:0) [2021/11/28 20:50:36.062621] gate(pck/s: 2257 sum: 625235 left:1100) logic db(pck/s: 603 sum: 150905 left:0) redis db(pck/s: 1451 sum: 186671 left:1) [2021/11/28 20:50:37.270038] gate(pck/s: 3228 sum: 628463 left:2238) logic db(pck/s: 884 sum: 151789 left:0) redis db(pck/s: 1773 sum: 188444 left:26) [2021/11/28 20:50:38.169313] gate(pck/s: 2763 sum: 631226 left:2084) logic db(pck/s: 775 sum: 152564 left:1) redis db(pck/s: 1562 sum: 190006 left:0) [2021/11/28 20:50:40.277313] gate(pck/s: 3454 sum: 637519 left:1670) logic db(pck/s: 967 sum: 154252 left:0) redis db(pck/s: 1156 sum: 192326 left:14) [2021/11/28 20:50:41.175540] gate(pck/s: 2465 sum: 639984 left:1886) logic db(pck/s: 666 sum: 154918 left:0) redis db(pck/s: 754 sum: 193080 left:1) [2021/11/28 20:50:42.076500] gate(pck/s: 2329 sum: 642313 left:1184) logic db(pck/s: 614 sum: 155532 left:0) redis db(pck/s: 695 sum: 193775 left:12) [2021/11/28 20:50:43.258889] gate(pck/s: 3577 sum: 645890 left:1749) logic db(pck/s: 981 sum: 156513 left:0) redis db(pck/s: 1119 sum: 194894 left:0) [2021/11/28 20:50:44.183382] gate(pck/s: 3666 sum: 649556 left:3891) logic db(pck/s: 769 sum: 157282 left:1) redis db(pck/s: 885 sum: 195779 left:0) [2021/11/28 20:50:45.081740] gate(pck/s: 4457 sum: 654013 left:1015) logic db(pck/s: 654 sum: 157936 left:0) redis db(pck/s: 744 sum: 196523 left:17) [2021/11/28 20:50:46.259926] gate(pck/s: 3423 sum: 657436 left:1642) logic db(pck/s: 955 sum: 158891 left:0) redis db(pck/s: 1090 sum: 197613 left:2) [2021/11/28 20:50:47.159268] gate(pck/s: 2467 sum: 659903 left:1617) logic db(pck/s: 653 sum: 159544 left:1) redis db(pck/s: 741 sum: 198354 left:0) [2021/11/28 20:50:49.267417] gate(pck/s: 3567 sum: 665972 left:1604) logic db(pck/s: 920 sum: 161187 left:0) redis db(pck/s: 1077 sum: 200255 left:7) [2021/11/28 20:50:50.166325] gate(pck/s: 2461 sum: 668433 left:1718) logic db(pck/s: 647 sum: 161834 left:2) redis db(pck/s: 742 sum: 200997 left:0) [2021/11/28 20:50:52.275055] gate(pck/s: 3589 sum: 674499 left:1572) logic db(pck/s: 1009 sum: 163516 left:0) redis db(pck/s: 1146 sum: 202926 left:18) [2021/11/28 20:50:53.172944] gate(pck/s: 2467 sum: 676966 left:1800) logic db(pck/s: 705 sum: 164221 left:4) redis db(pck/s: 817 sum: 203743 left:0) [2021/11/28 20:50:54.072366] gate(pck/s: 3006 sum: 679972 left:1597) logic db(pck/s: 698 sum: 164919 left:0) redis db(pck/s: 793 sum: 204536 left:20) [2021/11/28 20:50:55.280736] gate(pck/s: 6068 sum: 686040 left:1534) logic db(pck/s: 981 sum: 165900 left:0) redis db(pck/s: 1140 sum: 205676 left:18) [2021/11/28 20:50:56.180357] gate(pck/s: 2415 sum: 688455 left:1885) logic db(pck/s: 658 sum: 166558 left:7) redis db(pck/s: 754 sum: 206430 left:0) [2021/11/28 20:50:57.079533] gate(pck/s: 2312 sum: 690767 left:1168) logic db(pck/s: 624 sum: 167182 left:0) redis db(pck/s: 701 sum: 207131 left:11) [2021/11/28 20:50:58.258429] gate(pck/s: 3630 sum: 694397 left:1616) logic db(pck/s: 1022 sum: 168204 left:0) redis db(pck/s: 1161 sum: 208292 left:0) [2021/11/28 20:50:59.155704] gate(pck/s: 2506 sum: 696903 left:1542) logic db(pck/s: 701 sum: 168905 left:1) redis db(pck/s: 799 sum: 209091 left:0) [2021/11/28 20:51:01.263630] gate(pck/s: 3506 sum: 702829 left:1634) logic db(pck/s: 906 sum: 170490 left:0) redis db(pck/s: 1054 sum: 210900 left:3) [2021/11/28 20:51:02.162742] gate(pck/s: 2397 sum: 705226 left:1721) logic db(pck/s: 629 sum: 171119 left:2) redis db(pck/s: 721 sum: 211621 left:0) [2021/11/28 20:51:04.271376] gate(pck/s: 4642 sum: 712436 left:3517) logic db(pck/s: 983 sum: 172809 left:0) redis db(pck/s: 1129 sum: 213564 left:15) [2021/11/28 20:51:05.169592] gate(pck/s: 4359 sum: 716795 left:1781) logic db(pck/s: 657 sum: 173466 left:1) redis db(pck/s: 744 sum: 214308 left:0) [2021/11/28 20:51:07.277990] gate(pck/s: 3390 sum: 722729 left:1693) logic db(pck/s: 908 sum: 175052 left:0) redis db(pck/s: 1034 sum: 216105 left:16) [2021/11/28 20:51:08.177210] gate(pck/s: 2564 sum: 725293 left:1865) logic db(pck/s: 725 sum: 175777 left:0) redis db(pck/s: 831 sum: 216936 left:0) [2021/11/28 20:51:10.258485] gate(pck/s: 3471 sum: 731269 left:1646) logic db(pck/s: 933 sum: 177350 left:1) redis db(pck/s: 1094 sum: 218768 left:0) [2021/11/28 20:51:11.152629] gate(pck/s: 2467 sum: 733736 left:1575) logic db(pck/s: 652 sum: 178002 left:1) redis db(pck/s: 747 sum: 219515 left:0) [2021/11/28 20:51:13.261257] gate(pck/s: 3421 sum: 739664 left:1680) logic db(pck/s: 952 sum: 179614 left:0) redis db(pck/s: 1074 sum: 221345 left:3) [2021/11/28 20:51:14.159804] gate(pck/s: 3298 sum: 742962 left:3463) logic db(pck/s: 699 sum: 180313 left:1) redis db(pck/s: 772 sum: 222117 left:0) [2021/11/28 20:51:16.267555] gate(pck/s: 3434 sum: 751080 left:1704) logic db(pck/s: 929 sum: 181986 left:0) redis db(pck/s: 1065 sum: 224014 left:8) [2021/11/28 20:51:17.167166] gate(pck/s: 2550 sum: 753630 left:1727) logic db(pck/s: 705 sum: 182691 left:1) redis db(pck/s: 823 sum: 224837 left:0) [2021/11/28 20:51:19.275078] gate(pck/s: 3613 sum: 759740 left:1524) logic db(pck/s: 934 sum: 184302 left:0) redis db(pck/s: 1086 sum: 226689 left:9) [2021/11/28 20:51:20.174268] gate(pck/s: 2347 sum: 762087 left:1863) logic db(pck/s: 636 sum: 184938 left:1) redis db(pck/s: 712 sum: 227401 left:0) [2021/11/28 20:51:22.282222] gate(pck/s: 3550 sum: 768212 left:1502) logic db(pck/s: 928 sum: 186552 left:0) redis db(pck/s: 1062 sum: 229253 left:35) [2021/11/28 20:51:23.181307] gate(pck/s: 2405 sum: 770617 left:1907) logic db(pck/s: 622 sum: 187174 left:0) redis db(pck/s: 724 sum: 229977 left:0) [2021/11/28 20:51:24.079539] gate(pck/s: 2926 sum: 773543 left:2124) logic db(pck/s: 626 sum: 187800 left:0) redis db(pck/s: 713 sum: 230690 left:13) [2021/11/28 20:51:25.257705] gate(pck/s: 5905 sum: 779448 left:1727) logic db(pck/s: 890 sum: 188690 left:1) redis db(pck/s: 1387 sum: 232077 left:0) [2021/11/28 20:51:26.157441] gate(pck/s: 2287 sum: 781735 left:1891) logic db(pck/s: 610 sum: 189300 left:10) redis db(pck/s: 1388 sum: 233465 left:0) [2021/11/28 20:51:28.265069] gate(pck/s: 3475 sum: 787929 left:1692) logic db(pck/s: 939 sum: 190997 left:0) redis db(pck/s: 1781 sum: 236878 left:2) [2021/11/28 20:51:29.164483] gate(pck/s: 2570 sum: 790499 left:1659) logic db(pck/s: 686 sum: 191683 left:1) redis db(pck/s: 1092 sum: 237970 left:0) [2021/11/28 20:51:31.272413] gate(pck/s: 3435 sum: 796384 left:1698) logic db(pck/s: 933 sum: 193267 left:0) redis db(pck/s: 1054 sum: 239840 left:6) [2021/11/28 20:51:32.171150] gate(pck/s: 2501 sum: 798885 left:1843) logic db(pck/s: 683 sum: 193950 left:0) redis db(pck/s: 796 sum: 240636 left:0) [2021/11/28 20:51:34.278929] gate(pck/s: 4392 sum: 805899 left:3675) logic db(pck/s: 901 sum: 195536 left:0) redis db(pck/s: 1042 sum: 242469 left:0) [2021/11/28 20:51:35.178239] gate(pck/s: 4527 sum: 810426 left:1921) logic db(pck/s: 669 sum: 196205 left:0) redis db(pck/s: 777 sum: 243246 left:13) [2021/11/28 20:51:37.254675] gate(pck/s: 3354 sum: 816352 left:1695) logic db(pck/s: 944 sum: 197848 left:2) redis db(pck/s: 1052 sum: 245092 left:0) [2021/11/28 20:51:38.157954] gate(pck/s: 2499 sum: 818851 left:1657) logic db(pck/s: 711 sum: 198559 left:1) redis db(pck/s: 795 sum: 245887 left:0) [2021/11/28 20:51:40.263234] gate(pck/s: 3089 sum: 824481 left:2047) logic db(pck/s: 799 sum: 200043 left:1) redis db(pck/s: 934 sum: 247605 left:3) [2021/11/28 20:51:41.161282] gate(pck/s: 2852 sum: 827333 left:1690) logic db(pck/s: 783 sum: 200826 left:0) redis db(pck/s: 879 sum: 248484 left:4) [2021/11/28 20:51:43.268567] gate(pck/s: 3495 sum: 833370 left:1646) logic db(pck/s: 946 sum: 202469 left:0) redis db(pck/s: 1084 sum: 250368 left:0) [2021/11/28 20:51:44.167547] gate(pck/s: 3434 sum: 836804 left:3447) logic db(pck/s: 693 sum: 203162 left:0) redis db(pck/s: 786 sum: 251154 left:8) [2021/11/28 20:51:46.275830] gate(pck/s: 3528 sum: 844917 left:1539) logic db(pck/s: 975 sum: 204836 left:1) redis db(pck/s: 1114 sum: 253084 left:0) [2021/11/28 20:51:47.175165] gate(pck/s: 2414 sum: 847331 left:1834) logic db(pck/s: 643 sum: 205479 left:0) redis db(pck/s: 709 sum: 253793 left:29) [2021/11/28 20:51:49.251595] gate(pck/s: 3343 sum: 853217 left:1673) logic db(pck/s: 901 sum: 207106 left:2) redis db(pck/s: 1001 sum: 255597 left:0) [2021/11/28 20:51:50.189845] gate(pck/s: 2521 sum: 855738 left:1972) logic db(pck/s: 667 sum: 207773 left:0) redis db(pck/s: 754 sum: 256351 left:10) [2021/11/28 20:51:51.081535] gate(pck/s: 2503 sum: 858241 left:1092) logic db(pck/s: 692 sum: 208465 left:4) redis db(pck/s: 791 sum: 257142 left:0) [2021/11/28 20:51:52.258754] gate(pck/s: 3241 sum: 861482 left:1855) logic db(pck/s: 928 sum: 209393 left:0) redis db(pck/s: 1011 sum: 258153 left:0) [2021/11/28 20:51:53.158715] gate(pck/s: 2711 sum: 864193 left:1620) logic db(pck/s: 744 sum: 210137 left:5) redis db(pck/s: 834 sum: 258987 left:0) [2021/11/28 20:51:54.057099] gate(pck/s: 2803 sum: 866996 left:1691) logic db(pck/s: 659 sum: 210796 left:0) redis db(pck/s: 724 sum: 259711 left:0) [2021/11/28 20:51:55.265724] gate(pck/s: 5998 sum: 872994 left:1770) logic db(pck/s: 946 sum: 211742 left:0) redis db(pck/s: 1062 sum: 260773 left:0) [2021/11/28 20:51:56.164622] gate(pck/s: 2269 sum: 875263 left:2046) logic db(pck/s: 611 sum: 212353 left:0) redis db(pck/s: 689 sum: 261462 left:5) [2021/11/28 20:51:58.273466] gate(pck/s: 3337 sum: 881394 left:1814) logic db(pck/s: 916 sum: 214007 left:0) redis db(pck/s: 1066 sum: 263379 left:0) [2021/11/28 20:51:59.172083] gate(pck/s: 2629 sum: 884023 left:1856) logic db(pck/s: 726 sum: 214733 left:0) redis db(pck/s: 827 sum: 264206 left:8) [2021/11/28 20:52:01.280302] gate(pck/s: 3062 sum: 889655 left:2024) logic db(pck/s: 796 sum: 216198 left:1) redis db(pck/s: 914 sum: 265916 left:0) [2021/11/28 20:52:02.178631] gate(pck/s: 1949 sum: 891604 left:2820) logic db(pck/s: 532 sum: 216730 left:0) redis db(pck/s: 602 sum: 266518 left:11) [2021/11/28 20:52:03.078362] gate(pck/s: 2856 sum: 894460 left:1611) logic db(pck/s: 767 sum: 217497 left:2) redis db(pck/s: 886 sum: 267404 left:0) [2021/11/28 20:52:04.255606] gate(pck/s: 4750 sum: 899210 left:3925) logic db(pck/s: 1004 sum: 218501 left:0) redis db(pck/s: 1139 sum: 268543 left:0) [2021/11/28 20:52:05.158103] gate(pck/s: 4597 sum: 903807 left:1826) logic db(pck/s: 657 sum: 219158 left:1) redis db(pck/s: 773 sum: 269316 left:0) [2021/11/28 20:52:06.054514] gate(pck/s: 2116 sum: 905923 left:1277) logic db(pck/s: 610 sum: 219768 left:1) redis db(pck/s: 674 sum: 269990 left:3) [2021/11/28 20:52:07.262966] gate(pck/s: 3904 sum: 909827 left:1768) logic db(pck/s: 1079 sum: 220847 left:2) redis db(pck/s: 1215 sum: 271205 left:0) [2021/11/28 20:52:08.161635] gate(pck/s: 2663 sum: 912490 left:1603) logic db(pck/s: 732 sum: 221579 left:0) redis db(pck/s: 823 sum: 272028 left:2) [2021/11/28 20:52:10.269794] gate(pck/s: 3426 sum: 918407 left:1633) logic db(pck/s: 926 sum: 223189 left:6) redis db(pck/s: 1041 sum: 273844 left:0) [2021/11/28 20:52:11.169215] gate(pck/s: 2451 sum: 920858 left:1830) logic db(pck/s: 655 sum: 223844 left:0) redis db(pck/s: 733 sum: 274577 left:6) [2021/11/28 20:52:13.277567] gate(pck/s: 3532 sum: 926951 left:1548) logic db(pck/s: 943 sum: 225492 left:1) redis db(pck/s: 1050 sum: 276413 left:0) [2021/11/28 20:52:14.175589] gate(pck/s: 3832 sum: 930783 left:1213) logic db(pck/s: 487 sum: 225979 left:0) redis db(pck/s: 1383 sum: 277796 left:936)
   
   --------------------------
   
   logic服务器启动时间:1.47185871s
   logic账号总数:5000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:      100006(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       11025(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13907(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100043( 489)                                0.00%   1.84%  49.90%  45.19%   3.07%   0.00%   0.00%
   协议:      100007(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100017(   4)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13905( 494)                               88.87%  10.12%   1.01%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric

   ![](./pic/guilddungeon-********-02.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-********205003
   File: service
   Type: inuse_objects
   Time: Nov 28, 2021 at 8:49pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 5179819, 74.77% of 6927363 total
   Dropped 252 nodes (cum <= 34636)
   Showing top 20 nodes out of 106
         flat  flat%   sum%        cum   cum%
       524328  7.57%  7.57%    1327172 19.16%  app/logic/db/redisop.ClClient.GetSomeGemInfoByReply
       462872  6.68% 14.25%     462872  6.68%  app/logic/character.(*EmblemM).Load
       458759  6.62% 20.87%     458759  6.62%  app/protos/out/cl.(*GemAttr).Unmarshal
       415080  5.99% 26.87%     415080  5.99%  app/protos/out/cl.(*EmblemInfo).Unmarshal
       409625  5.91% 32.78%     824705 11.91%  app/logic/db/redisop.ClClient.GetSomeEmblemInfoByReply
       344085  4.97% 37.75%     802844 11.59%  app/protos/out/cl.(*GemInfo).Unmarshal
       330524  4.77% 42.52%     330524  4.77%  app/logic/character.(*GemM).Load
       277742  4.01% 46.53%     358027  5.17%  app/protos/in/db.(*ModuleAttr).Unmarshal
       270352  3.90% 50.43%     270352  3.90%  app/protos/out/cl.(*FormationInfo).Clone (inline)
       242703  3.50% 53.93%     242703  3.50%  app/protos/out/cl.(*Handbooks).Unmarshal
       191940  2.77% 56.70%     191940  2.77%  app/protos/out/cl.(*HeroBody).Unmarshal
       190068  2.74% 59.45%     190068  2.74%  app/logic/character.initHeroFromData
       182584  2.64% 62.08%     374524  5.41%  app/logic/db/redisop.ClClient.GetSomeHeroBodyByReply
       163848  2.37% 64.45%     576201  8.32%  app/protos/out/cl.(*Formation).Clone
       144190  2.08% 66.53%     144190  2.08%  app/logic/db/redisop.ClClient.GetSomeEquipmentByReply
       142001  2.05% 68.58%     142001  2.05%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
       134198  1.94% 70.52%     134198  1.94%  app/logic/character.(*EquipM).Load
       109228  1.58% 72.09%     141997  2.05%  app/logic/helper/sync.(*MessageSync).append
        98308  1.42% 73.51%      98308  1.42%  app/logic/character.(*Shop).load
        87384  1.26% 74.77%     283995  4.10%  app/logic/character.(*User).pushMsg
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********205003
   File: service
   Type: cpu
   Time: Nov 28, 2021 at 8:49pm (CST)
   Duration: 30.13s, Total samples = 46.51s (154.34%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 19.49s, 41.90% of 46.51s total
   Dropped 1041 nodes (cum <= 0.23s)
   Showing top 20 nodes out of 287
         flat  flat%   sum%        cum   cum%
        7.04s 15.14% 15.14%      9.89s 21.26%  runtime.scanobject
        1.25s  2.69% 17.82%      1.42s  3.05%  runtime.findObject
        1.22s  2.62% 20.45%      5.01s 10.77%  runtime.mallocgc
        1.11s  2.39% 22.83%      1.34s  2.88%  runtime.mapaccess1_fast32
        1.09s  2.34% 25.18%      1.10s  2.37%  syscall.Syscall
        0.85s  1.83% 27.00%      1.17s  2.52%  runtime.heapBitsSetType
        0.83s  1.78% 28.79%      1.37s  2.95%  runtime.mapiternext
        0.71s  1.53% 30.32%      0.71s  1.53%  runtime.futex
        0.69s  1.48% 31.80%      0.69s  1.48%  runtime.markBits.isMarked (inline)
        0.64s  1.38% 33.18%      3.65s  7.85%  app/logic/battle.(*PsSkillMgr).TriggerPsSkill
        0.62s  1.33% 34.51%      0.62s  1.33%  runtime.nextFreeFast
        0.50s  1.08% 35.58%      0.50s  1.08%  app/protos/out/cl.sovCl
        0.43s  0.92% 36.51%      0.43s  0.92%  runtime.epollwait
        0.38s  0.82% 37.33%      0.38s  0.82%  runtime.memclrNoHeapPointers
        0.38s  0.82% 38.14%      0.38s  0.82%  runtime.memmove
        0.37s   0.8% 38.94%      1.75s  3.76%  github.com/ivanabc/log4go.Logger.intLogf
        0.36s  0.77% 39.71%      0.49s  1.05%  app/logic/battle.(*AttrManager).GetAttr
        0.35s  0.75% 40.46%      1.33s  2.86%  runtime.greyobject
        0.35s  0.75% 41.22%      0.39s  0.84%  runtime.heapBitsForAddr (inline)
        0.32s  0.69% 41.90%      0.88s  1.89%  app/logic/battle.(*OneSkillAttack).end
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-********205003
   File: service
   Type: alloc_space
   Time: Nov 28, 2021 at 8:49pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 8799.82MB, 60.14% of 14633.08MB total
   Dropped 731 nodes (cum <= 73.17MB)
   Showing top 20 nodes out of 188
         flat  flat%   sum%        cum   cum%
    2700.14MB 18.45% 18.45%  2703.14MB 18.47%  app/logic/db/redisop.ClClient.SetSomeGuildDungeonLogMCallSKs
     728.74MB  4.98% 23.43%   732.74MB  5.01%  app/logic/battle.newAttrManager
     671.89MB  4.59% 28.02%   691.89MB  4.73%  github.com/gogo/protobuf/proto.Marshal
     508.43MB  3.47% 31.50%   571.02MB  3.90%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     474.03MB  3.24% 34.74%   484.53MB  3.31%  gitlab.qdream.com/kit/sea/redis/resp.readBulkStr
     456.01MB  3.12% 37.85%   628.52MB  4.30%  app/logic/battle.(*PsSkillMgr).CastPsSkill
     357.63MB  2.44% 40.30%   843.66MB  5.77%  gitlab.qdream.com/kit/sea/redis/resp.readArray
     341.07MB  2.33% 42.63%   341.07MB  2.33%  gitlab.qdream.com/kit/sea/redis.messageToReply
     263.54MB  1.80% 44.43%   265.04MB  1.81%  app/logic/character.JSONMarshal
     261.62MB  1.79% 46.22%   280.62MB  1.92%  encoding/xml.(*Decoder).rawToken
     249.03MB  1.70% 47.92%   249.03MB  1.70%  app/logic/character.rebuildReport.func1
     244.01MB  1.67% 49.59%  1526.05MB 10.43%  app/logic/battle.(*OneSkill).run
     234.52MB  1.60% 51.19%   234.52MB  1.60%  app/protos/out/cl.(*GuildDungeonLog).Clone
     233.51MB  1.60% 52.79%   233.51MB  1.60%  app/logic/battle.(*OneSkill).SetSkillTargets (inline)
     197.03MB  1.35% 54.13%   197.03MB  1.35%  app/logic/character.(*User).NewUserSnapshot
     191.52MB  1.31% 55.44%   191.52MB  1.31%  app/logic/battle.(*Team).AltAttr
     180.04MB  1.23% 56.67%   228.55MB  1.56%  app/logic/db/redisop.DbClient.GetOfflineUserByReply
     179.54MB  1.23% 57.90%   199.04MB  1.36%  app/logic/command/guilddungeon.(*AsyncC2LGuildDungeonUserDamageRankReq).Resp
     166.50MB  1.14% 59.04%   518.02MB  3.54%  app/logic/battle.(*PsSkillMgr).DelayExecute
     161.02MB  1.10% 60.14%   161.02MB  1.10%  app/logic/command/guilddungeon.(*C2LGuildDungeonFightCommand).getAltAttr
   (pprof)
   ```
