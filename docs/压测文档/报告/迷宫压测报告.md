## 迷宫压测报告

#### 第一版

4000人时，gateway，redis db有少量的消息积压

1. robot数据

   ```
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh
   2021-11-05 11:05:32 压测数据分析
   压测基础情况
   开始时间：2021/11/05 10:59:51, 结束时间:2021/11/05 11:04:47, 耗时:296(s)
   登录成功机器人总数：4000
   总消息数:1591985， 平均每秒消息数:5378
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:13305,MSG_C2L_MazeRecoveryHero                  309079  309079           19.37%  100.00%
   协议:cmd:10100,                                            4000    4000            0.25%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               4000    4000            0.25%  100.00%
   协议:cmd:13303,MSG_C2L_MazeTriggerEvent                  233047  263540           14.60%   88.43%
   协议:cmd:13309,MSG_C2L_MazeBuyRevive                     283007  283007           17.73%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  4000    4000            0.25%  100.00%
   协议:cmd:13301,MSG_C2L_MazeGetMap                          4000    4000            0.25%  100.00%
   协议:cmd:11029,MSG_C2L_Formation                           7309   10976            0.46%   66.59%
   协议:cmd:13307,MSG_C2L_MazeGetGrid                       301543  301543           18.89%  100.00%
   协议正确数:1153869 协议总数:1595985 正确比:72.30%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:13305,MSG_C2L_MazeRecoveryHero                 80.94%  13.51%   5.11%   0.44%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:10100,                                         21.38%  33.77%  42.95%   1.90%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                            95.25%   1.88%   2.88%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13303,MSG_C2L_MazeTriggerEvent                 80.10%  14.14%   5.02%   0.50%   0.05%   0.00%   0.00%   0.19%
   协议:cmd:13309,MSG_C2L_MazeBuyRevive                    81.42%  13.46%   4.68%   0.43%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11025,MSG_C2L_GM                                0.43%   0.57%   2.62%   5.65%  11.65%  46.62%  32.45%   0.00%
   协议:cmd:13301,MSG_C2L_MazeGetMap                       14.12%  15.57%   4.88%  39.20%   0.62%   0.00%   0.05%  25.55%
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.02%   0.01%   0.12%   0.09%  23.20%  76.57%
   协议:cmd:13307,MSG_C2L_MazeGetGrid                      80.54%  13.93%   4.93%   0.56%   0.04%   0.00%   0.00%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. 游戏数据

   ```
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh
   
   --------------------------
   
   所有超时的left
   [2021/11/05 11:04:48.171393] gate(pck/s: 4266 sum: 1717812 left:0) logic db(pck/s: 3 sum: 85233 left:0) redis db(pck/s: 2990 sum: 142980 left:1110) [2021/11/05 10:59:52.090457] gate(pck/s: 1248 sum: 8325 left:1220) logic db(pck/s: 1000 sum: 8000 left:0) redis db(pck/s: 1317 sum: 8333 left:178) [2021/11/05 10:59:53.268648] gate(pck/s: 3285 sum: 11610 left:467) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 4569 sum: 12902 left:2179) [2021/11/05 10:59:55.066239] gate(pck/s: 3857 sum: 15934 left:5188) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1 sum: 16017 left:0) [2021/11/05 10:59:56.275376] gate(pck/s: 78301 sum: 94235 left:9999) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1 sum: 16018 left:0) [2021/11/05 10:59:57.174235] gate(pck/s: 55301 sum: 149536 left:10000) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1 sum: 16019 left:0) [2021/11/05 10:59:58.073241] gate(pck/s: 55211 sum: 204747 left:10000) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1 sum: 16020 left:0) [2021/11/05 10:59:59.282592] gate(pck/s: 78636 sum: 283383 left:10000) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1 sum: 16021 left:0) [2021/11/05 11:00:00.181301] gate(pck/s: 57141 sum: 340524 left:10000) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1 sum: 16022 left:0) [2021/11/05 11:00:01.080243] gate(pck/s: 48059 sum: 388583 left:9996) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 2 sum: 16024 left:0)
   
   --------------------------
   
   logic服务器启动时间:1.540533159s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:      100042(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100038(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13303(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:     event:6(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13305(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13307(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议: event_10001(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:    event:49(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100041(  13)                               92.31%   7.69%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13309(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric
   ![dispatch](./pic/maze_20211105_1.png)

4. pprof 

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Nov 5, 2021 at 11:02am (CST)
   Duration: 30.10s, Total samples = 15.65s (52.00%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 5690ms, 36.36% of 15650ms total
   Dropped 745 nodes (cum <= 78.25ms)
   Showing top 10 nodes out of 272
         flat  flat%   sum%        cum   cum%
       1740ms 11.12% 11.12%     3110ms 19.87%  runtime.scanobject
        800ms  5.11% 16.23%      850ms  5.43%  runtime.findObject
        500ms  3.19% 19.42%      500ms  3.19%  runtime.epollwait
        460ms  2.94% 22.36%      490ms  3.13%  syscall.Syscall
        420ms  2.68% 25.05%     1510ms  9.65%  runtime.mallocgc
        410ms  2.62% 27.67%      460ms  2.94%  runtime.mapaccess1_fast32
        400ms  2.56% 30.22%      510ms  3.26%  runtime.heapBitsSetType
        390ms  2.49% 32.72%      590ms  3.77%  runtime.mapiternext
        300ms  1.92% 34.63%      300ms  1.92%  runtime.markBits.isMarked (inline)
        270ms  1.73% 36.36%      270ms  1.73%  runtime.futex
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Nov 5, 2021 at 11:02am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 135.56MB, 51.04% of 265.57MB total
   Dropped 180 nodes (cum <= 1.33MB)
   Showing top 10 nodes out of 195
         flat  flat%   sum%        cum   cum%
      32.01MB 12.05% 12.05%    32.01MB 12.05%  app/logic/character.(*MazeM).initEventID
      18.50MB  6.97% 19.02%       24MB  9.04%  app/logic/character.newHero (inline)
      18.01MB  6.78% 25.80%    18.01MB  6.78%  app/logic/battle.NewAttrManager
      14.52MB  5.47% 31.27%    14.52MB  5.47%  app/logic/helper/monitor.(*Cmds).Add
      13.01MB  4.90% 36.17%    13.01MB  4.90%  app/logic/battle.(*BuffManager).addBuff
         12MB  4.52% 40.69%       12MB  4.52%  app/logic/battle.NewPsAttr
          8MB  3.01% 43.70%        8MB  3.01%  app/logic/character.(*Hero).calcTotalAttr
       7.50MB  2.83% 46.52%    31.51MB 11.86%  app/logic/character.(*HeroM).Add
       6.50MB  2.45% 48.97%     6.50MB  2.45%  app/logic/character.(*Handbook).Add
       5.50MB  2.07% 51.04%     5.50MB  2.07%  app/logic/character.(*User).TaskTypeOnEvent
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Nov 5, 2021 at 11:02am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 2464.76MB, 40.25% of 6123.71MB total
   Dropped 778 nodes (cum <= 30.62MB)
   Showing top 10 nodes out of 235
         flat  flat%   sum%        cum   cum%
     666.53MB 10.88% 10.88%   740.54MB 12.09%  app/logic/battle.(*Manager).TriggerPSE
     373.53MB  6.10% 16.98%   599.55MB  9.79%  context.WithDeadline
     263.99MB  4.31% 21.30%   266.99MB  4.36%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     225.52MB  3.68% 24.98%   226.02MB  3.69%  time.AfterFunc
     186.51MB  3.05% 28.02%   186.51MB  3.05%  container/list.(*List).insertValue (inline)
     179.52MB  2.93% 30.95%   180.02MB  2.94%  app/logic/character.(*User).mergeResources
     172.55MB  2.82% 33.77%   172.55MB  2.82%  strings.(*Builder).WriteString
     154.01MB  2.51% 36.29%   154.01MB  2.51%  app/protos/out/cl.(*Handbook).Clone
     127.01MB  2.07% 38.36%   343.51MB  5.61%  app/logic/character.(*User).pushMsg
     115.60MB  1.89% 40.25%   127.10MB  2.08%  encoding/xml.(*Decoder).rawToken
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-**************
   File: service
   Type: inuse_objects
   Time: Nov 5, 2021 at 11:02am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 1405810, 47.83% of 2939425 total
   Dropped 214 nodes (cum <= 14697)
   Showing top 10 nodes out of 161
         flat  flat%   sum%        cum   cum%
       441938 15.03% 15.03%     441938 15.03%  app/logic/character.(*MazeM).initEventID
       304291 10.35% 25.39%     376386 12.80%  app/logic/character.newHero
       115261  3.92% 29.31%     115261  3.92%  app/logic/battle.(*BuffManager).addBuff
       109226  3.72% 33.02%     109226  3.72%  app/logic/battle.NewPSEDynamic
        97565  3.32% 36.34%      97565  3.32%  app/logic/battle.NewPsAttr
        76072  2.59% 38.93%      76072  2.59%  app/logic/character.(*User).TaskTypeOnEvent
        72095  2.45% 41.38%      72095  2.45%  app/logic/character.initHeroFromData (inline)
        65538  2.23% 43.61%      65538  2.23%  container/list.(*List).insertValue
        64369  2.19% 45.80%      64369  2.19%  app/logic/character.(*Avatar).Add
        59455  2.02% 47.83%      59455  2.02%  app/logic/character.(*Handbook).Add
   (pprof)
   ```

总结：

​		4000人时，消息有少量积压，每个robot登录时发送资源和布主线、迷宫2个阵容，导致CPU利用率在250%左右，布阵结束，正式开始跑迷宫业务逻辑时，CPU利用率恢复正常水平50%左右。但迷宫中的最大瓶颈在于战斗事件，后期计划等十二那边的不同养成程度机器人实现完成，战斗事件和其他事件分开，再重新进行压测。
