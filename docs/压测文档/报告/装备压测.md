## 装备压测报告


####  - 建高级号加装备养成

1. robot数据

   ```
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh
   2021-11-28 14:56:03 压测数据分析
   压测基础情况
   开始时间：2021/11/28 14:50:48, 结束时间:2021/11/28 14:55:05, 耗时:257(s)
   登录成功机器人总数：5000
   总消息数:1267289， 平均每秒消息数:4931
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:10100,                                            5000    5000            0.39%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.39%  100.00%
   协议:cmd:12315,MSG_C2L_EquipRevive                       179797  179949           14.13%   99.92%
   协议:cmd:12307,MSG_C2L_EquipRefine                       190971  191141           15.01%   99.91%
   协议:cmd:11025,MSG_C2L_GM                                 10000   10000            0.79%  100.00%
   协议:cmd:12303,MSG_C2L_EquipWear                         194569  196024           15.29%   99.26%
   协议:cmd:12313,MSG_C2L_EquipDecompose                    195077  195206           15.33%   99.93%
   协议:cmd:12309,MSG_C2L_EquipEnchant                      179261  190739           14.09%   93.98%
   协议:cmd:12311,MSG_C2L_EquipEvolution                     12018  103818            0.94%   11.58%
   协议:cmd:12301,MSG_C2L_EquipGet                            5000    5000            0.39%  100.00%
   协议:cmd:12305,MSG_C2L_EquipStrength                     186807  190412           14.68%   98.11%
   协议正确数:1163500 协议总数:1272289 正确比:91.45%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:10100,                                          0.24%   0.16%   0.06%   0.16%   0.96%   2.28%   3.48%  92.66%
   协议:cmd:11003,MSG_C2L_Flush                             0.06%   0.00%   0.08%   0.12%   0.56%   1.18%   3.06%  94.94%
   协议:cmd:12315,MSG_C2L_EquipRevive                      97.62%   0.55%   0.78%   0.21%   0.22%   0.24%   0.00%   0.38%
   协议:cmd:12307,MSG_C2L_EquipRefine                      98.48%   0.55%   0.77%   0.18%   0.02%   0.00%   0.00%   0.00%
   协议:cmd:11025,MSG_C2L_GM                                0.01%   0.01%   0.05%   0.06%   0.28%   0.57%  12.10%  86.92%
   协议:cmd:12303,MSG_C2L_EquipWear                        97.83%   0.46%   0.74%   0.20%   0.20%   0.21%   0.00%   0.36%
   协议:cmd:12313,MSG_C2L_EquipDecompose                   97.82%   0.48%   0.74%   0.18%   0.20%   0.22%   0.00%   0.36%
   协议:cmd:12309,MSG_C2L_EquipEnchant                     98.53%   0.50%   0.75%   0.19%   0.02%   0.00%   0.00%   0.00%
   协议:cmd:12311,MSG_C2L_EquipEvolution                   98.35%   0.48%   0.91%   0.24%   0.02%   0.00%   0.00%   0.00%
   协议:cmd:12301,MSG_C2L_EquipGet                          0.00%   0.00%   0.00%   0.00%   0.00%   0.00%  21.00%  79.00%
   协议:cmd:12305,MSG_C2L_EquipStrength                    98.45%   0.54%   0.79%   0.19%   0.02%   0.00%   0.00%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. 游戏数据

   ```
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh
   
   --------------------------
   
   所有超时的left
   [2021/11/28 14:54:53.260462] gate(pck/s: 5000 sum: 1418825 left:0) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 2730 sum: 53039 left:2337) [2021/11/28 14:54:54.159532] gate(pck/s: 0 sum: 1418825 left:0) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1212 sum: 54251 left:1125) [2021/11/28 14:49:54.172916] gate(pck/s: 836 sum: 1334 left:1438) logic db(pck/s: 838 sum: 1267 left:315) redis db(pck/s: 1172 sum: 1793 left:14) [2021/11/28 14:49:55.046309] gate(pck/s: 440 sum: 1774 left:2198) logic db(pck/s: 416 sum: 1683 left:379) redis db(pck/s: 602 sum: 2395 left:0) [2021/11/28 14:49:56.251739] gate(pck/s: 825 sum: 2599 left:3327) logic db(pck/s: 805 sum: 2488 left:502) redis db(pck/s: 1091 sum: 3486 left:0) [2021/11/28 14:49:57.150609] gate(pck/s: 339 sum: 2938 left:4330) logic db(pck/s: 333 sum: 2821 left:381) redis db(pck/s: 333 sum: 3819 left:0) [2021/11/28 14:49:58.054327] gate(pck/s: 794 sum: 3732 left:4601) logic db(pck/s: 777 sum: 3598 left:649) redis db(pck/s: 1142 sum: 4961 left:0) [2021/11/28 14:49:59.259036] gate(pck/s: 706 sum: 4438 left:4555) logic db(pck/s: 723 sum: 4321 left:719) redis db(pck/s: 948 sum: 5909 left:0) [2021/11/28 14:50:00.157897] gate(pck/s: 332 sum: 4770 left:4509) logic db(pck/s: 339 sum: 4660 left:676) redis db(pck/s: 413 sum: 6322 left:0) [2021/11/28 14:50:01.063352] gate(pck/s: 866 sum: 5636 left:4623) logic db(pck/s: 854 sum: 5514 left:886) redis db(pck/s: 1148 sum: 7470 left:0) [2021/11/28 14:50:02.269312] gate(pck/s: 1329 sum: 6965 left:5344) logic db(pck/s: 1250 sum: 6764 left:1012) redis db(pck/s: 1965 sum: 9435 left:0) [2021/11/28 14:50:03.162613] gate(pck/s: 417 sum: 7382 left:5345) logic db(pck/s: 391 sum: 7155 left:1024) redis db(pck/s: 447 sum: 9882 left:43) [2021/11/28 14:50:04.064129] gate(pck/s: 305 sum: 7687 left:5346) logic db(pck/s: 347 sum: 7502 left:1023) redis db(pck/s: 442 sum: 10324 left:0) [2021/11/28 14:50:05.243136] gate(pck/s: 398 sum: 8085 left:5282) logic db(pck/s: 391 sum: 7893 left:1024) redis db(pck/s: 523 sum: 10847 left:0) [2021/11/28 14:50:06.174411] gate(pck/s: 265 sum: 8350 left:5205) logic db(pck/s: 244 sum: 8137 left:1024) redis db(pck/s: 284 sum: 11131 left:0) [2021/11/28 14:50:07.075961] gate(pck/s: 239 sum: 8589 left:5208) logic db(pck/s: 271 sum: 8408 left:1016) redis db(pck/s: 271 sum: 11402 left:0) [2021/11/28 14:50:08.246753] gate(pck/s: 274 sum: 8863 left:5197) logic db(pck/s: 231 sum: 8639 left:1024) redis db(pck/s: 238 sum: 11640 left:0) [2021/11/28 14:50:09.149640] gate(pck/s: 204 sum: 9067 left:5217) logic db(pck/s: 212 sum: 8851 left:949) redis db(pck/s: 203 sum: 11843 left:0) [2021/11/28 14:50:10.052441] gate(pck/s: 198 sum: 9265 left:5271) logic db(pck/s: 187 sum: 9038 left:823) redis db(pck/s: 161 sum: 12004 left:0) [2021/11/28 14:50:11.256985] gate(pck/s: 206 sum: 9471 left:5241) logic db(pck/s: 206 sum: 9244 left:735) redis db(pck/s: 222 sum: 12226 left:0) [2021/11/28 14:50:12.153481] gate(pck/s: 218 sum: 9689 left:7750) logic db(pck/s: 242 sum: 9486 left:514) redis db(pck/s: 131 sum: 12357 left:0) [2021/11/28 14:50:13.058047] gate(pck/s: 244 sum: 9933 left:7990) logic db(pck/s: 242 sum: 9728 left:272) redis db(pck/s: 122 sum: 12479 left:0) [2021/11/28 14:50:14.264994] gate(pck/s: 326 sum: 10259 left:8208) logic db(pck/s: 272 sum: 10000 left:0) redis db(pck/s: 166 sum: 12645 left:0) [2021/11/28 14:50:15.165993] gate(pck/s: 198 sum: 10457 left:8010) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 100 sum: 12745 left:0) [2021/11/28 14:50:16.058401] gate(pck/s: 864 sum: 11321 left:7146) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 97 sum: 12842 left:0) [2021/11/28 14:50:17.270315] gate(pck/s: 744 sum: 12065 left:6402) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 134 sum: 12976 left:0) [2021/11/28 14:50:18.173941] gate(pck/s: 210 sum: 12275 left:6193) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 106 sum: 13082 left:0) [2021/11/28 14:50:19.069557] gate(pck/s: 202 sum: 12477 left:5991) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 102 sum: 13184 left:0) [2021/11/28 14:50:20.249666] gate(pck/s: 192 sum: 12669 left:5799) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 97 sum: 13281 left:0) [2021/11/28 14:50:21.144909] gate(pck/s: 201 sum: 12870 left:5598) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 101 sum: 13382 left:0) [2021/11/28 14:50:22.072465] gate(pck/s: 220 sum: 13090 left:8810) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 111 sum: 13493 left:0) [2021/11/28 14:50:23.255217] gate(pck/s: 283 sum: 13373 left:8527) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 143 sum: 13636 left:0) [2021/11/28 14:50:24.150402] gate(pck/s: 236 sum: 13609 left:8291) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 119 sum: 13755 left:0) [2021/11/28 14:50:25.053984] gate(pck/s: 188 sum: 13797 left:8103) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 95 sum: 13850 left:0) [2021/11/28 14:50:26.263292] gate(pck/s: 347 sum: 14144 left:7756) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 174 sum: 14024 left:0) [2021/11/28 14:50:27.158651] gate(pck/s: 245 sum: 14389 left:7511) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 123 sum: 14147 left:0) [2021/11/28 14:50:28.056356] gate(pck/s: 265 sum: 14654 left:7247) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 134 sum: 14281 left:0) [2021/11/28 14:50:29.251106] gate(pck/s: 222 sum: 14876 left:7025) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 112 sum: 14393 left:0) [2021/11/28 14:50:30.163778] gate(pck/s: 2343 sum: 17219 left:4682) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 57 sum: 14450 left:0) [2021/11/28 14:50:31.067730] gate(pck/s: 286 sum: 17505 left:4396) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 117 sum: 14567 left:0) [2021/11/28 14:50:32.277669] gate(pck/s: 274 sum: 17779 left:8731) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 138 sum: 14705 left:0) [2021/11/28 14:50:33.175125] gate(pck/s: 252 sum: 18031 left:8479) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 127 sum: 14832 left:0) [2021/11/28 14:50:34.075378] gate(pck/s: 257 sum: 18288 left:8222) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 129 sum: 14961 left:0) [2021/11/28 14:50:35.247504] gate(pck/s: 7902 sum: 26190 left:6298) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 91 sum: 15052 left:0) [2021/11/28 14:50:36.146944] gate(pck/s: 3578 sum: 29768 left:7300) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 2237 sum: 17289 left:1025) [2021/11/28 14:50:37.045680] gate(pck/s: 3341 sum: 33109 left:5491) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 2402 sum: 19691 left:1961) [2021/11/28 14:50:38.254448] gate(pck/s: 7843 sum: 40952 left:0) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 3415 sum: 23106 left:1946)
   
   --------------------------
   
   logic服务器启动时间:1.62195216s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:  event_2033(  13)                               15.38%  15.38%  30.77%  23.08%  15.38%   0.00%   0.00%
   协议:       11025( 135)                               92.59%   0.00%   2.96%   2.96%   0.74%   0.74%   0.00%
   协议:      100006(  85)                                0.00%   9.41%  11.76%  29.41%  32.94%  16.47%   0.00%
   协议:  event_1010(   3)                                0.00%   0.00%  33.33%  66.67%   0.00%   0.00%   0.00%
   协议: event_10007(   1)                                0.00%   0.00%   0.00%   0.00%  100.00%   0.00%   0.00%
   协议: event_10016(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12305(   2)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   协议:    event:44(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:  event_2010(   2)                                0.00%   0.00%  50.00%  50.00%   0.00%   0.00%   0.00%
   协议:  event:1379(1748)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:  event_2030(   6)                               16.67%   0.00%  16.67%  16.67%  33.33%  16.67%   0.00%
   协议:      100004(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric

   ![](./pic/equip-********.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-********145318
   File: service
   Type: inuse_objects
   Time: Nov 28, 2021 at 2:53pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for ********, 86.78% of ******** total
   Dropped 224 nodes (cum <= 60897)
   Showing top 20 nodes out of 79
         flat  flat%   sum%        cum   cum%
      2703466 22.20% 22.20%    2703466 22.20%  app/logic/character.NewEmblem
      1337000 10.98% 33.17%    2606807 21.40%  app/logic/character.newGem
      1269807 10.43% 43.60%    1269807 10.43%  app/logic/character.generateGemAttr
      1265549 10.39% 53.99%    1593253 13.08%  app/logic/character.newHero
       773356  6.35% 60.34%     773356  6.35%  app/logic/character.newEquip
       540705  4.44% 64.78%     540705  4.44%  app/protos/out/cl.(*FormationInfo).Clone (inline)
       410537  3.37% 68.15%     410537  3.37%  app/logic/character.(*Handbook).Add
       327704  2.69% 70.84%     327704  2.69%  app/logic/character.initHeroFromData (inline)
       316774  2.60% 73.44%    1086865  8.92%  app/protos/out/cl.(*Formation).Clone
       313266  2.57% 76.01%     313266  2.57%  app/logic/character.(*User).TaskTypeOnEvent
       229386  1.88% 77.90%     229386  1.88%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
       191355  1.57% 79.47%     191355  1.57%  app/logic/character.(*Avatar).Add
       184959  1.52% 80.99%    2791766 22.92%  app/logic/character.(*GemM).AddGem
       131071  1.08% 82.06%     327682  2.69%  app/logic/helper/sync.(*MessageSync).Push
       120153  0.99% 83.05%     120153  0.99%  container/list.(*List).insertValue (inline)
       101425  0.83% 83.88%     101425  0.83%  app/protos/out/cl.(*HeroBody).Clone
        98305  0.81% 84.69%      98305  0.81%  app/logic/character.newArtifact
        87384  0.72% 85.41%     415066  3.41%  app/logic/character.(*User).pushMsg
        87381  0.72% 86.13%     109227   0.9%  app/logic/helper/sync.(*MessageSync).append
        79587  0.65% 86.78%      79587  0.65%  app/logic/character.(*Hero).WearEquipment
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********145318
   File: service
   Type: cpu
   Time: Nov 28, 2021 at 2:53pm (CST)
   Duration: 30.15s, Total samples = 17.63s (58.47%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 7.78s, 44.13% of 17.63s total
   Dropped 519 nodes (cum <= 0.09s)
   Showing top 20 nodes out of 256
         flat  flat%   sum%        cum   cum%
        2.05s 11.63% 11.63%      3.05s 17.30%  runtime.scanobject
        0.81s  4.59% 16.22%      0.92s  5.22%  syscall.Syscall
        0.55s  3.12% 19.34%      0.58s  3.29%  runtime.findObject
        0.48s  2.72% 22.06%      1.84s 10.44%  runtime.mallocgc
        0.46s  2.61% 24.67%      0.46s  2.61%  runtime.epollwait
        0.42s  2.38% 27.06%      0.42s  2.38%  runtime.futex
        0.42s  2.38% 29.44%      0.50s  2.84%  runtime.mapaccess1_fast32
        0.37s  2.10% 31.54%      0.50s  2.84%  runtime.heapBitsSetType
        0.28s  1.59% 33.13%      0.33s  1.87%  runtime.nanotime
        0.22s  1.25% 34.37%      0.22s  1.25%  runtime.nextFreeFast
        0.22s  1.25% 35.62%      0.61s  3.46%  runtime.selectgo
        0.20s  1.13% 36.76%      0.20s  1.13%  github.com/json-iterator/go.(*Stream).WriteString
        0.18s  1.02% 37.78%      0.28s  1.59%  github.com/json-iterator/go.(*Stream).WriteStringWithHTMLEscaped
        0.18s  1.02% 38.80%      0.18s  1.02%  runtime.markBits.isMarked (inline)
        0.17s  0.96% 39.76%      0.17s  0.96%  runtime.memmove
        0.16s  0.91% 40.67%      0.23s  1.30%  runtime.mapiternext
        0.16s  0.91% 41.58%      0.62s  3.52%  runtime.netpoll
        0.15s  0.85% 42.43%      0.22s  1.25%  app/logic/character.(*User).CheckHeroInFormation
        0.15s  0.85% 43.28%      0.15s  0.85%  app/logic/character.(*User).ResetDaily
        0.15s  0.85% 44.13%      0.15s  0.85%  runtime.(*itabTableType).find
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-********145318
   File: service
   Type: alloc_space
   Time: Nov 28, 2021 at 2:53pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 12041.03MB, 72.85% of 16527.70MB total
   Dropped 669 nodes (cum <= 82.64MB)
   Showing top 20 nodes out of 110
         flat  flat%   sum%        cum   cum%
    1617.64MB  9.79%  9.79%  2233.18MB 13.51%  app/logic/character.(*User).TaskTypeOnEvent
     920.54MB  5.57% 15.36%   920.54MB  5.57%  container/list.(*List).insertValue (inline)
     882.56MB  5.34% 20.70%  1070.58MB  6.48%  app/logic/character.(*User).mergeResources
     819.37MB  4.96% 25.65%   853.37MB  5.16%  app/logic/db/redisop.ClClient.SetSomeGemInfoMCallSKs
     725.69MB  4.39% 30.05%   727.19MB  4.40%  app/logic/db/redisop.ClClient.SetSomeEquipmentMCallSKs
     695.53MB  4.21% 34.25%  1588.06MB  9.61%  app/logic/character.(*User).pushMsg
     691.74MB  4.19% 38.44%   691.74MB  4.19%  app/logic/db/redisop.ClClient.SetSomeEmblemInfoMCallSKs
     664.54MB  4.02% 42.46%   664.54MB  4.02%  app/protos/out/cl.(*TaskTypeProgress).Clone
     537.55MB  3.25% 45.71%   573.59MB  3.47%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     532.51MB  3.22% 48.93%   949.22MB  5.74%  app/logic/command/equip.(*C2LEquipStrengthCommand).Execute
     526.04MB  3.18% 52.12%   857.56MB  5.19%  context.WithDeadline
     476.63MB  2.88% 55.00%   782.16MB  4.73%  app/logic/character.(*Equip).MergeReturnRes
     471.03MB  2.85% 57.85%   798.54MB  4.83%  app/logic/character.(*User).FireCommonEvent
     432.41MB  2.62% 60.47%   437.41MB  2.65%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
     413.51MB  2.50% 62.97%  2002.08MB 12.11%  app/logic/character.(*User).SendCmdToGateway
     381.65MB  2.31% 65.28%   407.65MB  2.47%  encoding/xml.(*Decoder).rawToken
     331.53MB  2.01% 67.28%   331.53MB  2.01%  time.AfterFunc
     331.02MB  2.00% 69.29%  1171.05MB  7.09%  app/logic/character.(*Achieve).update
     313.03MB  1.89% 71.18%   313.03MB  1.89%  app/goxml.(*EquipRefineInfoManager).GetCosts
     276.51MB  1.67% 72.85%   801.03MB  4.85%  app/logic/helper/sync.(*MessageSync).append
   (pprof)
   ```



