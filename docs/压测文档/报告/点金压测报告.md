## 点金压测报告



压测结果：

​		符合业务压测指标

1. robot数据

   ```
   2021-07-05 19:00:12 压测数据分析
   压测基础情况
   开始时间：2021/07/05 18:54:55, 结束时间:2021/07/05 18:59:49, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:1479301， 平均每秒消息数:5031
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:13201,MSG_C2L_GoldBuyGet                          5000    5000 100.00%
   协议:cmd:13203,MSG_C2L_GoldBuyGetGold                   1469301 1469301 100.00%
   协议正确数:1484301 协议总数:1484301 正确比:100.00%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000-
   协议:cmd:10100,                                         15.02%  23.64%  53.14%   7.36%   0.84%   0.00%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                            96.20%   2.12%   1.68%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13201,MSG_C2L_GoldBuyGet                       98.08%   1.70%   0.22%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13203,MSG_C2L_GoldBuyGetGold                   98.87%   0.92%   0.21%   0.00%   0.00%   0.00%   0.00%
   
   ---------------------
   ```
   
2. 游戏数据

   ```
   
   --------------------------
   
   所有超时的left
   
   --------------------------
   
   logic服务器启动时间:979.85696ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:       10104(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100006(   4)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13203(  14)                               35.71%  64.29%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric
   ![点金](./pic/点金.png)

4. pprof 

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Jul 5, 2021 at 6:57pm (CST)
   Duration: 30.14s, Total samples = 10.34s (34.31%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 3820ms, 36.94% of 10340ms total
   Dropped 298 nodes (cum <= 51.70ms)
   Showing top 10 nodes out of 192
         flat  flat%   sum%        cum   cum%
        940ms  9.09%  9.09%     1580ms 15.28%  runtime.scanobject
        710ms  6.87% 15.96%      850ms  8.22%  syscall.Syscall
        460ms  4.45% 20.41%      460ms  4.45%  runtime.futex
        450ms  4.35% 24.76%     1090ms 10.54%  runtime.mallocgc
        310ms  3.00% 27.76%      310ms  3.00%  runtime.epollwait
        220ms  2.13% 29.88%      260ms  2.51%  runtime.findObject
        210ms  2.03% 31.91%      480ms  4.64%  runtime.selectgo
        190ms  1.84% 33.75%      220ms  2.13%  runtime.heapBitsSetType
        180ms  1.74% 35.49%      180ms  1.74%  github.com/json-iterator/go.(*Stream).WriteString
        150ms  1.45% 36.94%      150ms  1.45%  runtime.markBits.isMarked (inline)
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 5, 2021 at 6:57pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 63.01MB, 44.38% of 141.99MB total
   Dropped 87 nodes (cum <= 0.71MB)
   Showing top 10 nodes out of 153
         flat  flat%   sum%        cum   cum%
         13MB  9.16%  9.16%    16.50MB 11.62%  app/logic/character.newHero (inline)
          8MB  5.64% 14.79%    24.51MB 17.26%  app/logic/character.(*HeroM).Add
       7.50MB  5.28% 20.08%     7.50MB  5.28%  app/logic/character.(*Handbook).Add
       5.50MB  3.88% 23.95%     5.50MB  3.88%  app/logic/helper/monitor.(*Cmds).Add
          5MB  3.52% 27.47%        5MB  3.52%  app/goxml.GenSimpleResource
          5MB  3.52% 31.00%        5MB  3.52%  app/logic/character.(*User).TaskTypeOnEvent
          5MB  3.52% 34.52%        5MB  3.52%  app/logic/character.(*User).mergeResources
          5MB  3.52% 38.04%        5MB  3.52%  container/list.(*List).insertValue (inline)
       4.50MB  3.17% 41.21%       12MB  8.45%  app/logic/character.(*User).SendSelfToClient
       4.50MB  3.17% 44.38%     4.50MB  3.17%  app/logic/character.(*Avatar).Add
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Jul 5, 2021 at 6:57pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 1818.84MB, 59.56% of 3053.77MB total
   Dropped 385 nodes (cum <= 15.27MB)
   Showing top 10 nodes out of 122
         flat  flat%   sum%        cum   cum%
     343.52MB 11.25% 11.25%   562.04MB 18.40%  context.WithDeadline
     306.15MB 10.03% 21.27%   308.65MB 10.11%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     218.52MB  7.16% 28.43%   218.52MB  7.16%  time.AfterFunc
     208.54MB  6.83% 35.26%   434.05MB 14.21%  app/logic/character.(*User).SendSelfToClient
     183.01MB  5.99% 41.25%   184.51MB  6.04%  app/logic/character.(*User).mergeResources
     164.01MB  5.37% 46.62%   164.01MB  5.37%  container/list.(*List).insertValue (inline)
     119.01MB  3.90% 50.52%   354.51MB 11.61%  app/logic/character.(*User).pushMsg
      97.51MB  3.19% 53.71%    97.51MB  3.19%  app/goxml.GenSimpleResource (inline)
      89.58MB  2.93% 56.65%    89.58MB  2.93%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
         89MB  2.91% 59.56%  1513.11MB 49.55%  app/logic/command/goldbuy.(*C2LGoldBuyGetGoldCommand).Execute
   (pprof)
   ```

