## 公会压测

1. 第一版

  - Robot数据   :    条件设置有问题，协议的执行数量差别较大

    ```
    2021-06-17 16:26:08 压测数据分析
    压测基础情况
    开始时间：2021/06/17 16:26:00, 结束时间:2021/06/17 16:26:02, 耗时:2(s)
    登录成功机器人总数：5000
    总消息数:13745， 平均每秒消息数:6872
    
    --------------------------
    
    协议正确率分布
    协议                                                      正确      总数      正确比   
    协议:cmd:13523,MSG_C2L_GuildSendMail                         78      78 100.00%
    协议:cmd:13503,MSG_C2L_GuildGetMyInfo                      6168    6168 100.00%
    协议:cmd:13517,MSG_C2L_GuildApplyList                       179     179 100.00%
    协议:cmd:10100,                                            5000    5000 100.00%
    协议:cmd:13533,MSG_C2L_GuildGetMembers                      257     257 100.00%
    协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
    协议:cmd:13527,MSG_C2L_GuildQuit                             88      88 100.00%
    协议:cmd:13509,MSG_C2L_GuildManagerMember                     0       1   0.00%
    协议:cmd:13507,MSG_C2L_GuildLogList                         187     187 100.00%
    协议:cmd:13513,MSG_C2L_GuildSetName                          93      94  98.94%
    协议:cmd:13515,MSG_C2L_GuildModifyNotice                     97      97 100.00%
    协议:cmd:13501,MSG_C2L_GuildList                            548     548 100.00%
    协议:cmd:13535,MSG_C2L_GuildGetDeclaration                    3     175   1.71%
    协议:cmd:13525,MSG_C2L_GuildSignIn                          156     156 100.00%
    协议:cmd:13521,MSG_C2L_GuildUserApply                       278     281  98.93%
    协议:cmd:13529,MSG_C2L_GuildDisband                          75      75 100.00%
    协议:cmd:13531,MSG_C2L_GuildSearch                            8       8 100.00%
    协议:cmd:13511,MSG_C2L_GuildModifyInfo                       83      83 100.00%
    协议:cmd:13505,MSG_C2L_GuildCreate                          270     270 100.00%
    协议正确数:18568 协议总数:18745 正确比:99.06%
    
    --------------------------
    
    协议延迟分布
         协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
    协议:cmd:13523,MSG_C2L_GuildSendMail                    88.46%   5.13%   6.41%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   98.48%   1.07%   0.44%   0.02%   0.00%   0.00%   0.00% 
    协议:cmd:13517,MSG_C2L_GuildApplyList                   94.41%   4.47%   1.12%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:10100,                                          2.82%  53.76%  38.86%   4.56%   0.00%   0.00%   0.00% 
    协议:cmd:13533,MSG_C2L_GuildGetMembers                  91.83%   5.45%   2.33%   0.39%   0.00%   0.00%   0.00% 
    协议:cmd:11003,MSG_C2L_Flush                            94.20%   2.42%   3.22%   0.16%   0.00%   0.00%   0.00% 
    协议:cmd:13527,MSG_C2L_GuildQuit                        97.73%   2.27%   0.00%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:13507,MSG_C2L_GuildLogList                     89.30%   8.02%   2.14%   0.53%   0.00%   0.00%   0.00% 
    协议:cmd:13513,MSG_C2L_GuildSetName                     91.40%   6.45%   0.00%   0.00%   2.15%   0.00%   0.00% 
    协议:cmd:13515,MSG_C2L_GuildModifyNotice                93.81%   4.12%   2.06%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:13501,MSG_C2L_GuildList                        94.16%   4.74%   1.09%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:13535,MSG_C2L_GuildGetDeclaration              66.67%   0.00%  33.33%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:13525,MSG_C2L_GuildSignIn                      91.67%   2.56%   5.77%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:13521,MSG_C2L_GuildUserApply                   93.53%   3.96%   0.36%   0.00%   2.16%   0.00%   0.00% 
    协议:cmd:13529,MSG_C2L_GuildDisband                     89.33%   6.67%   0.00%   0.00%   4.00%   0.00%   0.00% 
    协议:cmd:13531,MSG_C2L_GuildSearch                      75.00%   0.00%  25.00%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:13511,MSG_C2L_GuildModifyInfo                  91.57%   3.61%   3.61%   1.20%   0.00%   0.00%   0.00% 
    协议:cmd:13505,MSG_C2L_GuildCreate                      94.44%   3.33%   0.00%   0.37%   1.85%   0.00%   0.00% 
    
    ---------------------
    
    logic服数情况
    ```

    

   - 游戏数据

     ```
     --------------------------
     
     所有超时的left
     
     --------------------------
     
     logic服务器启动时间:1.054252693s
     logic账号总数:5000
     
     --------------------------
     
     协议处理超时分布
          协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
     协议:      100007(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
     
     ```

2. 第二版

   - robot数据

     ```go
     2021-06-22 20:18:43 压测数据分析
     压测基础情况
     开始时间：2021/06/22 20:13:37, 结束时间:2021/06/22 20:18:32, 耗时:295(s)
     登录成功机器人总数：5000
     总消息数:1354761， 平均每秒消息数:4592
     
     --------------------------
     
     协议正确率分布
     协议                                                      正确      总数      正确比   
     协议:cmd:13523,MSG_C2L_GuildSendMail                      65695   67082  97.93%
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                    187463  187466 100.00%
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                    7556    7634  98.98%
     协议:cmd:13517,MSG_C2L_GuildApplyList                     66628   66691  99.91%
     协议:cmd:10100,                                            5000    5000 100.00%
     协议:cmd:13533,MSG_C2L_GuildGetMembers                   139837  174078  80.33%
     协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
     协议:cmd:13527,MSG_C2L_GuildQuit                          54877   54990  99.79%
     协议:cmd:13509,MSG_C2L_GuildManagerMember                  7983    9232  86.47%
     协议:cmd:13513,MSG_C2L_GuildSetName                       61090   63869  95.65%
     协议:cmd:13507,MSG_C2L_GuildLogList                      173269  173352  99.95%
     协议:cmd:13501,MSG_C2L_GuildList                         160970  161430  99.72%
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                  66536   66596  99.91%
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration                32178   32245  99.79%
     协议:cmd:13521,MSG_C2L_GuildUserApply                     79885   96768  82.55%
     协议:cmd:13525,MSG_C2L_GuildSignIn                         4986    4986 100.00%
     协议:cmd:13529,MSG_C2L_GuildDisband                       35026   42135  83.13%
     协议:cmd:13531,MSG_C2L_GuildSearch                        32028   32304  99.15%
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                    66940   66999  99.91%
     协议:cmd:13505,MSG_C2L_GuildCreate                        36516   41904  87.14%
     协议正确数:1289463 协议总数:1359761 正确比:94.83%
     
     --------------------------
     
     协议延迟分布
          协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
     协议:cmd:13523,MSG_C2L_GuildSendMail                    21.37%  14.21%  10.64%  11.08%  19.99%  18.03%   0.00% 
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   46.17%  16.27%  10.84%   7.60%   9.61%   7.74%   0.00% 
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                 46.63%  23.03%  14.57%   7.54%   6.84%   1.26%   0.00% 
     协议:cmd:13517,MSG_C2L_GuildApplyList                   21.78%  14.39%  10.66%  11.49%  19.35%  17.58%   0.00% 
     协议:cmd:10100,                                         28.54%  35.70%  31.00%   4.76%   0.00%   0.00%   0.00% 
     协议:cmd:13533,MSG_C2L_GuildGetMembers                  38.45%  19.85%  11.32%   8.13%  12.53%   7.89%   0.00% 
     协议:cmd:11003,MSG_C2L_Flush                            94.34%   1.40%   4.26%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13527,MSG_C2L_GuildQuit                        48.35%  23.67%  11.22%   6.25%   8.36%   2.15%   0.00% 
     协议:cmd:13509,MSG_C2L_GuildManagerMember               48.27%  22.25%  13.20%   7.69%   6.66%   1.63%   0.00% 
     协议:cmd:13513,MSG_C2L_GuildSetName                     20.48%  13.89%  10.36%  11.14%  20.67%  18.71%   0.00% 
     协议:cmd:13507,MSG_C2L_GuildLogList                     38.40%  19.90%  11.36%   8.24%  12.32%   7.98%   0.00% 
     协议:cmd:13501,MSG_C2L_GuildList                        42.68%  27.39%  11.91%   6.83%   8.75%   2.16%   0.00% 
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                21.64%  14.15%  10.92%  11.42%  19.63%  17.41%   0.00% 
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration              47.78%  24.31%  13.26%   7.60%   6.42%   0.64%   0.00% 
     协议:cmd:13521,MSG_C2L_GuildUserApply                   47.84%  23.75%  12.95%   8.09%   6.62%   0.77%   0.00% 
     协议:cmd:13525,MSG_C2L_GuildSignIn                      73.33%  12.05%   5.05%   7.54%   1.70%   0.26%   0.00% 
     协议:cmd:13529,MSG_C2L_GuildDisband                     14.28%   9.10%  10.11%  14.10%  23.04%  23.63%   0.00% 
     协议:cmd:13531,MSG_C2L_GuildSearch                      47.95%  24.09%  13.08%   7.96%   6.23%   0.71%   0.00% 
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                  21.61%  14.23%  10.58%  11.67%  19.79%  17.39%   0.00% 
     协议:cmd:13505,MSG_C2L_GuildCreate                      14.58%  10.16%   9.83%  13.77%  23.45%  22.20%   0.00% 
     
     ---------------------
     
     logic服数情况
     ```

   - logic服数据

     ```
     --------------------------
     
     所有超时的left
     [2021/06/22 20:18:33.120589] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:33.11863099 +0800 CST msgs count per second: gate(pck/s: 8032 sum: 1790157  left:6206) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 234 sum: 32130 left:0) 
     [2021/06/22 20:13:39.259511] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:13:39.236393852 +0800 CST msgs count per second: gate(pck/s: 8469 sum: 18907  left:1555) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 1 sum: 6148 left:0) 
     [2021/06/22 20:13:51.258429] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:13:51.231406296 +0800 CST msgs count per second: gate(pck/s: 8351 sum: 105773  left:1609) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 6176 left:0) 
     [2021/06/22 20:14:01.301760] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:14:01.291165993 +0800 CST msgs count per second: gate(pck/s: 4656 sum: 174579  left:1746) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 6201 left:0) 
     [2021/06/22 20:14:21.328258] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:14:21.231841014 +0800 CST msgs count per second: gate(pck/s: 7911 sum: 305623  left:2010) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 6253 left:0) 
     [2021/06/22 20:14:27.310724] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:14:27.30205296 +0800 CST msgs count per second: gate(pck/s: 8270 sum: 346354  left:1864) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 994 sum: 10479 left:0) 
     [2021/06/22 20:14:34.315723] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:14:34.304104494 +0800 CST msgs count per second: gate(pck/s: 4110 sum: 393004  left:1686) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 11284 left:0) 
     [2021/06/22 20:14:54.311198] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:14:54.29745318 +0800 CST msgs count per second: gate(pck/s: 7697 sum: 519279  left:1984) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 11337 left:0) 
     [2021/06/22 20:14:57.255405] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:14:57.241619343 +0800 CST msgs count per second: gate(pck/s: 7339 sum: 536557  left:1382) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 11344 left:0) 
     [2021/06/22 20:15:30.312736] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:15:30.312252126 +0800 CST msgs count per second: gate(pck/s: 7352 sum: 743064  left:2075) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 16429 left:0) 
     [2021/06/22 20:15:40.314959] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:15:40.305793949 +0800 CST msgs count per second: gate(pck/s: 3798 sum: 806145  left:2004) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 16456 left:0) 
     [2021/06/22 20:15:57.240631] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:15:57.231458927 +0800 CST msgs count per second: gate(pck/s: 7679 sum: 915218  left:1042) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 16499 left:0) 
     [2021/06/22 20:16:00.464716] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:00.446458398 +0800 CST msgs count per second: gate(pck/s: 8356 sum: 933262  left:1806) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 16507 left:0) 
     [2021/06/22 20:16:10.292850] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:10.272981956 +0800 CST msgs count per second: gate(pck/s: 3880 sum: 995875  left:1991) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 29 sum: 21534 left:0) 
     [2021/06/22 20:16:12.275559] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:12.249251821 +0800 CST msgs count per second: gate(pck/s: 12566 sum: 1012948  left:1147) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21539 left:0) 
     [2021/06/22 20:16:30.262417] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:30.231920298 +0800 CST msgs count per second: gate(pck/s: 6307 sum: 1114340  left:1271) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 21586 left:0) 
     [2021/06/22 20:16:33.231671] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:33.231380547 +0800 CST msgs count per second: gate(pck/s: 10843 sum: 1132939  left:1187) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21594 left:0) 
     [2021/06/22 20:16:36.238506] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:36.231472899 +0800 CST msgs count per second: gate(pck/s: 6294 sum: 1146898  left:1210) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21602 left:0) 
     [2021/06/22 20:16:37.168781] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:37.139096374 +0800 CST msgs count per second: gate(pck/s: 3917 sum: 1150815  left:1011) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21605 left:0) 
     [2021/06/22 20:16:39.245364] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:39.231516335 +0800 CST msgs count per second: gate(pck/s: 6569 sum: 1161157  left:1275) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21610 left:0) 
     [2021/06/22 20:16:42.252555] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:42.231436271 +0800 CST msgs count per second: gate(pck/s: 7945 sum: 1176857  left:3608) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21618 left:0) 
     [2021/06/22 20:16:43.244538] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:43.224926734 +0800 CST msgs count per second: gate(pck/s: 6479 sum: 1183336  left:2990) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 21620 left:0) 
     [2021/06/22 20:16:45.259527] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:45.231387383 +0800 CST msgs count per second: gate(pck/s: 6560 sum: 1194945  left:1353) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21625 left:0) 
     [2021/06/22 20:16:48.088903] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:48.071312833 +0800 CST msgs count per second: gate(pck/s: 3125 sum: 1205973  left:2132) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 21632 left:0) 
     [2021/06/22 20:16:51.242553] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:51.231390654 +0800 CST msgs count per second: gate(pck/s: 6473 sum: 1223583  left:1469) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21639 left:0) 
     [2021/06/22 20:16:52.141450] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:52.133367039 +0800 CST msgs count per second: gate(pck/s: 5341 sum: 1228924  left:1287) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 21642 left:0) 
     [2021/06/22 20:16:53.072021] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:53.04848321 +0800 CST msgs count per second: gate(pck/s: 4845 sum: 1233769  left:3368) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 272 sum: 21914 left:0) 
     [2021/06/22 20:16:54.249345] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:54.231403203 +0800 CST msgs count per second: gate(pck/s: 9368 sum: 1243137  left:1587) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 776 sum: 22690 left:0) 
     [2021/06/22 20:16:57.258335] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:57.231429246 +0800 CST msgs count per second: gate(pck/s: 6324 sum: 1257936  left:1680) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 801 sum: 25464 left:0) 
     [2021/06/22 20:16:58.155341] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:16:58.15090712 +0800 CST msgs count per second: gate(pck/s: 1392 sum: 1259328  left:3375) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 381 sum: 25845 left:582) 
     [2021/06/22 20:17:00.232397] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:00.231386442 +0800 CST msgs count per second: gate(pck/s: 6465 sum: 1272683  left:1302) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 29 sum: 26661 left:0) 
     [2021/06/22 20:17:01.162328] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:01.131419006 +0800 CST msgs count per second: gate(pck/s: 4083 sum: 1276766  left:1093) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26664 left:0) 
     [2021/06/22 20:17:03.270536] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:03.24961366 +0800 CST msgs count per second: gate(pck/s: 9015 sum: 1290606  left:3453) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26668 left:0) 
     [2021/06/22 20:17:06.254520] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:06.234006262 +0800 CST msgs count per second: gate(pck/s: 6631 sum: 1307451  left:1577) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26676 left:0) 
     [2021/06/22 20:17:08.044398] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:08.039427147 +0800 CST msgs count per second: gate(pck/s: 3014 sum: 1314676  left:1122) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26681 left:0) 
     [2021/06/22 20:17:09.272712] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:09.231450772 +0800 CST msgs count per second: gate(pck/s: 8366 sum: 1323042  left:1366) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26684 left:0) 
     [2021/06/22 20:17:12.263728] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:12.231439018 +0800 CST msgs count per second: gate(pck/s: 8772 sum: 1339642  left:3273) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26691 left:0) 
     [2021/06/22 20:17:13.159572] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:13.133110693 +0800 CST msgs count per second: gate(pck/s: 6891 sum: 1346533  left:1137) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26693 left:0) 
     [2021/06/22 20:17:15.236473] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:15.233255117 +0800 CST msgs count per second: gate(pck/s: 6380 sum: 1357358  left:1499) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26698 left:0) 
     [2021/06/22 20:17:16.166783] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:16.135559014 +0800 CST msgs count per second: gate(pck/s: 4486 sum: 1361844  left:1068) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26701 left:0) 
     [2021/06/22 20:17:18.243580] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:18.231403163 +0800 CST msgs count per second: gate(pck/s: 5453 sum: 1371372  left:2561) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26706 left:0) 
     [2021/06/22 20:17:21.250408] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:21.231399022 +0800 CST msgs count per second: gate(pck/s: 6426 sum: 1387448  left:1804) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26714 left:0) 
     [2021/06/22 20:17:22.156916] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:22.146690631 +0800 CST msgs count per second: gate(pck/s: 5677 sum: 1393125  left:1951) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26717 left:0) 
     [2021/06/22 20:17:23.055167] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:23.031464472 +0800 CST msgs count per second: gate(pck/s: 6657 sum: 1399782  left:1549) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26719 left:0) 
     [2021/06/22 20:17:24.263425] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:24.2419311 +0800 CST msgs count per second: gate(pck/s: 8217 sum: 1407999  left:1588) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26722 left:0) 
     [2021/06/22 20:17:27.233610] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:27.231506599 +0800 CST msgs count per second: gate(pck/s: 6526 sum: 1423060  left:1375) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26729 left:0) 
     [2021/06/22 20:17:30.240416] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:30.231390559 +0800 CST msgs count per second: gate(pck/s: 6706 sum: 1438213  left:1555) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26737 left:0) 
     [2021/06/22 20:17:33.233260] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:33.233159977 +0800 CST msgs count per second: gate(pck/s: 11093 sum: 1458101  left:1914) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26745 left:0) 
     [2021/06/22 20:17:36.254404] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:36.231382321 +0800 CST msgs count per second: gate(pck/s: 6805 sum: 1473959  left:1524) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26753 left:0) 
     [2021/06/22 20:17:39.261900] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:39.231625495 +0800 CST msgs count per second: gate(pck/s: 7339 sum: 1489467  left:1398) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26761 left:0) 
     [2021/06/22 20:17:40.160463] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:40.131430352 +0800 CST msgs count per second: gate(pck/s: 4064 sum: 1493531  left:1037) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 26763 left:0) 
     [2021/06/22 20:17:42.237409] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:42.231573966 +0800 CST msgs count per second: gate(pck/s: 7936 sum: 1505755  left:3520) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 26769 left:0) 
     [2021/06/22 20:17:43.105848] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:43.103223821 +0800 CST msgs count per second: gate(pck/s: 3765 sum: 1509520  left:4117) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 234 sum: 27003 left:0) 
     [2021/06/22 20:17:44.035508] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:44.031453891 +0800 CST msgs count per second: gate(pck/s: 6739 sum: 1516259  left:1383) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 758 sum: 27761 left:0) 
     [2021/06/22 20:17:45.244310] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:45.23147098 +0800 CST msgs count per second: gate(pck/s: 7993 sum: 1524252  left:1732) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 862 sum: 28623 left:0) 
     [2021/06/22 20:17:48.220627] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:48.197078365 +0800 CST msgs count per second: gate(pck/s: 3279 sum: 1536297  left:4222) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 765 sum: 31356 left:198) 
     [2021/06/22 20:17:49.150600] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:49.132686672 +0800 CST msgs count per second: gate(pck/s: 6776 sum: 1543073  left:1585) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 402 sum: 31759 left:0) 
     [2021/06/22 20:17:51.258507] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:51.231510339 +0800 CST msgs count per second: gate(pck/s: 6717 sum: 1554980  left:1510) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 31790 left:0) 
     [2021/06/22 20:17:52.157499] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:52.142570694 +0800 CST msgs count per second: gate(pck/s: 4951 sum: 1559931  left:2287) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31793 left:0) 
     [2021/06/22 20:17:53.056931] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:53.032056269 +0800 CST msgs count per second: gate(pck/s: 4554 sum: 1564485  left:3864) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 31795 left:0) 
     [2021/06/22 20:17:54.235538] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:54.231466558 +0800 CST msgs count per second: gate(pck/s: 10162 sum: 1574647  left:1656) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31798 left:0) 
     [2021/06/22 20:17:57.242364] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:57.231419263 +0800 CST msgs count per second: gate(pck/s: 6682 sum: 1589981  left:1638) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31806 left:0) 
     [2021/06/22 20:17:58.049725] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:17:58.047860559 +0800 CST msgs count per second: gate(pck/s: 2265 sum: 1592246  left:1355) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 1 sum: 31807 left:0) 
     [2021/06/22 20:18:00.238601] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:00.232508333 +0800 CST msgs count per second: gate(pck/s: 6364 sum: 1605000  left:1711) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31813 left:0) 
     [2021/06/22 20:18:03.022634] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:03.011123688 +0800 CST msgs count per second: gate(pck/s: 8112 sum: 1622245  left:1466) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 31820 left:0) 
     [2021/06/22 20:18:04.154490] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:04.131777641 +0800 CST msgs count per second: gate(pck/s: 7343 sum: 1629588  left:1193) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 4 sum: 31824 left:0) 
     [2021/06/22 20:18:06.262411] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:06.238440453 +0800 CST msgs count per second: gate(pck/s: 6573 sum: 1640816  left:1781) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31829 left:0) 
     [2021/06/22 20:18:08.031157] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:08.028780234 +0800 CST msgs count per second: gate(pck/s: 2979 sum: 1648147  left:1098) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 31833 left:0) 
     [2021/06/22 20:18:09.238374] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:09.232969365 +0800 CST msgs count per second: gate(pck/s: 8044 sum: 1656191  left:1430) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31836 left:0) 
     [2021/06/22 20:18:12.245509] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:12.23141545 +0800 CST msgs count per second: gate(pck/s: 8146 sum: 1672829  left:3723) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31844 left:0) 
     [2021/06/22 20:18:13.082451] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:13.075234764 +0800 CST msgs count per second: gate(pck/s: 5622 sum: 1678451  left:1927) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 1 sum: 31845 left:0) 
     [2021/06/22 20:18:15.252787] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:15.233693997 +0800 CST msgs count per second: gate(pck/s: 6706 sum: 1691755  left:1545) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31851 left:0) 
     [2021/06/22 20:18:18.259437] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:18.231435031 +0800 CST msgs count per second: gate(pck/s: 6291 sum: 1706636  left:1959) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 31858 left:0) 
     [2021/06/22 20:18:21.235563] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:21.235198239 +0800 CST msgs count per second: gate(pck/s: 6433 sum: 1722239  left:1488) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31866 left:0) 
     [2021/06/22 20:18:22.134572] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:22.131421546 +0800 CST msgs count per second: gate(pck/s: 5374 sum: 1727613  left:1420) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 31868 left:0) 
     [2021/06/22 20:18:24.242542] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:24.237661575 +0800 CST msgs count per second: gate(pck/s: 7411 sum: 1742599  left:1523) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31874 left:0) 
     [2021/06/22 20:18:27.249513] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:27.231384102 +0800 CST msgs count per second: gate(pck/s: 6969 sum: 1758080  left:1441) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 4 sum: 31883 left:0) 
     [2021/06/22 20:18:28.148502] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:28.131454429 +0800 CST msgs count per second: gate(pck/s: 3894 sum: 1761974  left:1043) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 2 sum: 31885 left:0) 
     [2021/06/22 20:18:30.256383] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-22 20:18:30.231414749 +0800 CST msgs count per second: gate(pck/s: 6542 sum: 1773155  left:1731) logic db(pck/s: 0 sum: 6140 left:0) redis db(pck/s: 3 sum: 31891 left:0) 
     
     --------------------------
     
     logic服务器启动时间:982.749816ms
     logic账号总数:5000
     
     --------------------------
     
     协议处理超时分布
          协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
     协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13501(  62)                               70.97%  19.35%   9.68%   0.00%   0.00%   0.00%   0.00% 
     协议:      100007(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13511(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13503(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13521(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100045( 214)                                0.00%  89.25%  10.75%   0.00%   0.00%   0.00%   0.00% 
     协议:       13513(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13533(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13515(   5)                               80.00%  20.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13509(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13527(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       11006(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
     ```

   - 分析

     C2L_GuildList 协议延迟比较厉害，应该是还没分页的原因





3. 第三版（分页后）

   - robot数据

     ```go
     2021-06-23 20:32:45 压测数据分析
     压测基础情况
     开始时间：2021/06/23 20:26:28, 结束时间:2021/06/23 20:31:22, 耗时:294(s)
     登录成功机器人总数：5000
     总消息数:2464368， 平均每秒消息数:8382
     
     --------------------------
     
     协议正确率分布
     协议                                                      正确      总数      正确比   
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                    179872  179872 100.00%
     协议:cmd:13523,MSG_C2L_GuildSendMail                      60503   61682  98.09%
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                     144     191  75.39%
     协议:cmd:10100,                                            5000    5000 100.00%
     协议:cmd:13517,MSG_C2L_GuildApplyList                     60864   60864 100.00%
     协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
     协议:cmd:13533,MSG_C2L_GuildGetMembers                    64431   66364  97.09%
     协议:cmd:13527,MSG_C2L_GuildQuit                           2550    2550 100.00%
     协议:cmd:13509,MSG_C2L_GuildManagerMember                    79     103  76.70%
     协议:cmd:13507,MSG_C2L_GuildLogList                       66062   66063 100.00%
     协议:cmd:13513,MSG_C2L_GuildSetName                       57752   60976  94.71%
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                  61086   61086 100.00%
     协议:cmd:13501,MSG_C2L_GuildList                         831324  831324 100.00%
     协议:cmd:13525,MSG_C2L_GuildSignIn                         4838    4838 100.00%
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration               166206  166208 100.00%
     协议:cmd:13521,MSG_C2L_GuildUserApply                     10523  499161   2.11%
     协议:cmd:13529,MSG_C2L_GuildDisband                       85336   85444  99.87%
     协议:cmd:13531,MSG_C2L_GuildSearch                       165942  165955  99.99%
     协议:cmd:13505,MSG_C2L_GuildCreate                        84541   85459  98.93%
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                    61228   61228 100.00%
     协议正确数:1973281 协议总数:2469368 正确比:79.91%
     
     --------------------------
     
     协议延迟分布
          协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   95.63%   2.75%   1.56%   0.07%   0.00%   0.00%   0.00% 
     协议:cmd:13523,MSG_C2L_GuildSendMail                    96.01%   2.44%   1.48%   0.07%   0.00%   0.00%   0.00% 
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                 96.53%   1.39%   2.08%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:10100,                                         16.10%  35.30%  42.92%   5.60%   0.08%   0.00%   0.00% 
     协议:cmd:13517,MSG_C2L_GuildApplyList                   96.04%   2.39%   1.52%   0.05%   0.00%   0.00%   0.00% 
     协议:cmd:11003,MSG_C2L_Flush                            93.64%   1.44%   4.92%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13533,MSG_C2L_GuildGetMembers                  96.36%   2.19%   1.41%   0.05%   0.00%   0.00%   0.00% 
     协议:cmd:13527,MSG_C2L_GuildQuit                        97.96%   1.76%   0.27%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13509,MSG_C2L_GuildManagerMember               96.20%   2.53%   1.27%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13507,MSG_C2L_GuildLogList                     96.31%   2.24%   1.40%   0.04%   0.00%   0.00%   0.00% 
     协议:cmd:13513,MSG_C2L_GuildSetName                     95.90%   2.54%   1.49%   0.07%   0.00%   0.00%   0.00% 
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                96.00%   2.41%   1.53%   0.06%   0.00%   0.00%   0.00% 
     协议:cmd:13501,MSG_C2L_GuildList                        96.14%   2.35%   1.45%   0.06%   0.00%   0.00%   0.00% 
     协议:cmd:13525,MSG_C2L_GuildSignIn                      95.62%   2.38%   2.00%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13521,MSG_C2L_GuildUserApply                   96.64%   1.95%   1.27%   0.14%   0.00%   0.00%   0.00% 
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration              95.78%   2.67%   1.49%   0.06%   0.00%   0.00%   0.00% 
     协议:cmd:13529,MSG_C2L_GuildDisband                     95.84%   2.59%   1.50%   0.07%   0.00%   0.00%   0.00% 
     协议:cmd:13531,MSG_C2L_GuildSearch                      95.72%   2.72%   1.50%   0.06%   0.00%   0.00%   0.00% 
     协议:cmd:13505,MSG_C2L_GuildCreate                      96.12%   2.36%   1.47%   0.06%   0.00%   0.00%   0.00% 
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                  96.09%   2.40%   1.45%   0.06%   0.00%   0.00%   0.00% 
     
     ---------------------
     
     logic服数情况
     
     ```

   - logic 数据

     ```go
     --------------------------
     
     所有超时的left
     [2021/06/23 20:30:53.358143] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-23 20:30:53.195455486 +0800 CST msgs count per second: gate(pck/s: 11663 sum: 2355924  left:1329) logic db(pck/s: 0 sum: 7581 left:0) redis db(pck/s: 2 sum: 33247 left:0) 
     
     --------------------------
     
     logic服务器启动时间:1.073014196s
     logic账号总数:5000
     
     --------------------------
     
     协议处理超时分布
          协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
     协议:       13501(   9)                               55.56%  44.44%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100007(   1)                                0.00%   0.00%   0.00%  100.00%   0.00%   0.00%   0.00% 
     协议:       13511(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13521(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100045( 149)                                0.00%  94.63%   5.37%   0.00%   0.00%   0.00%   0.00% 
     协议:       13505(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13535(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100002(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13529(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     
     ```

   - 分析

     延迟比之前好一点了，但是分页后C2L_GuildUserApply的正确率有问题，因为apply会比较集中公会人数容易到达上限，需要改下代码

4. 第四版

   - robot数据

     ```go
     2021-06-24 14:41:14 压测数据分析
     压测基础情况
     开始时间：2021/06/24 14:35:15, 结束时间:2021/06/24 14:40:09, 耗时:294(s)
     登录成功机器人总数：5000
     总消息数:2231917， 平均每秒消息数:7591
     
     --------------------------
     
     协议正确率分布
     协议                                                      正确      总数      正确比   
     协议:cmd:13523,MSG_C2L_GuildSendMail                      53626   54544  98.32%
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                    204872  204872 100.00%
     协议:cmd:10100,                                            5000    5000 100.00%
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                      42     245  17.14%
     协议:cmd:13517,MSG_C2L_GuildApplyList                     54551   54551 100.00%
     协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
     协议:cmd:13533,MSG_C2L_GuildGetMembers                    82106   93331  87.97%
     协议:cmd:13527,MSG_C2L_GuildQuit                          19193   19195  99.99%
     协议:cmd:13509,MSG_C2L_GuildManagerMember                   189     201  94.03%
     协议:cmd:13507,MSG_C2L_GuildLogList                       93480   93481 100.00%
     协议:cmd:13513,MSG_C2L_GuildSetName                       51378   54145  94.89%
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                  54576   54576 100.00%
     协议:cmd:13501,MSG_C2L_GuildList                         804386  804386 100.00%
     协议:cmd:13525,MSG_C2L_GuildSignIn                         4984    4984 100.00%
     协议:cmd:13521,MSG_C2L_GuildUserApply                     24497  251372   9.75%
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration               160557  160633  99.95%
     协议:cmd:13529,MSG_C2L_GuildDisband                       80662   80811  99.82%
     协议:cmd:13531,MSG_C2L_GuildSearch                       160256  160287  99.98%
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                    54201   54201 100.00%
     协议:cmd:13505,MSG_C2L_GuildCreate                        80518   81102  99.28%
     协议正确数:1994074 协议总数:2236917 正确比:89.14%
     
     --------------------------
     
     协议延迟分布
          协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
     协议:cmd:13523,MSG_C2L_GuildSendMail                    98.75%   1.07%   0.18%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   96.53%   2.08%   1.38%   0.02%   0.00%   0.00%   0.00% 
     协议:cmd:10100,                                          4.28%  30.24%  52.84%  10.48%   2.16%   0.00%   0.00% 
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                 100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13517,MSG_C2L_GuildApplyList                   98.65%   1.19%   0.16%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:11003,MSG_C2L_Flush                            94.50%   1.80%   3.64%   0.06%   0.00%   0.00%   0.00% 
     协议:cmd:13533,MSG_C2L_GuildGetMembers                  98.49%   1.22%   0.28%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13527,MSG_C2L_GuildQuit                        98.25%   1.28%   0.47%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13509,MSG_C2L_GuildManagerMember               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13507,MSG_C2L_GuildLogList                     98.46%   1.24%   0.30%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13513,MSG_C2L_GuildSetName                     98.70%   1.15%   0.15%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                98.75%   1.11%   0.14%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13501,MSG_C2L_GuildList                        98.67%   1.15%   0.18%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13525,MSG_C2L_GuildSignIn                      98.76%   0.90%   0.34%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13521,MSG_C2L_GuildUserApply                   97.59%   1.71%   0.71%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration              97.15%   1.99%   0.85%   0.01%   0.00%   0.00%   0.00% 
     协议:cmd:13529,MSG_C2L_GuildDisband                     97.57%   1.76%   0.67%   0.01%   0.00%   0.00%   0.00% 
     协议:cmd:13531,MSG_C2L_GuildSearch                      96.97%   2.06%   0.96%   0.02%   0.00%   0.00%   0.00% 
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                  98.61%   1.19%   0.20%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13505,MSG_C2L_GuildCreate                      98.73%   1.09%   0.18%   0.00%   0.00%   0.00%   0.00% 
     
     ---------------------
     
     logic服数情况
     
     ```

   - logic 数据

     ```go
     --------------------------
     
     所有超时的left
     
     --------------------------
     
     logic服务器启动时间:1.083883244s
     logic账号总数:5000
     
     --------------------------
     
     协议处理超时分布
          协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
     协议:     event:0(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100007(   1)                                0.00%   0.00%   0.00%  100.00%   0.00%   0.00%   0.00% 
     协议:       13521(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13503(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100045(  98)                                0.00%  98.98%   1.02%   0.00%   0.00%   0.00%   0.00% 
     协议:       13505(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13533(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13507(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100002(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     
     ```

   - 分析

     + guildList优化后，延迟比之前好了
     + userApply和applyRatify还是有问题，userApply是因为公会的申请列表满了，applyRatify是因为一下子管理的申请者超出了上限，本地人少的时候这两个不是很明显


4. 第四版

   - robot数据

     ```go
     2021-06-24 14:41:14 压测数据分析
     压测基础情况
     开始时间：2021/06/24 14:35:15, 结束时间:2021/06/24 14:40:09, 耗时:294(s)
     登录成功机器人总数：5000
     总消息数:2231917， 平均每秒消息数:7591
     
     --------------------------
     
     协议正确率分布
     协议                                                      正确      总数      正确比   
     协议:cmd:13523,MSG_C2L_GuildSendMail                      53626   54544  98.32%
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                    204872  204872 100.00%
     协议:cmd:10100,                                            5000    5000 100.00%
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                      42     245  17.14%
     协议:cmd:13517,MSG_C2L_GuildApplyList                     54551   54551 100.00%
     协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
     协议:cmd:13533,MSG_C2L_GuildGetMembers                    82106   93331  87.97%
     协议:cmd:13527,MSG_C2L_GuildQuit                          19193   19195  99.99%
     协议:cmd:13509,MSG_C2L_GuildManagerMember                   189     201  94.03%
     协议:cmd:13507,MSG_C2L_GuildLogList                       93480   93481 100.00%
     协议:cmd:13513,MSG_C2L_GuildSetName                       51378   54145  94.89%
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                  54576   54576 100.00%
     协议:cmd:13501,MSG_C2L_GuildList                         804386  804386 100.00%
     协议:cmd:13525,MSG_C2L_GuildSignIn                         4984    4984 100.00%
     协议:cmd:13521,MSG_C2L_GuildUserApply                     24497  251372   9.75%
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration               160557  160633  99.95%
     协议:cmd:13529,MSG_C2L_GuildDisband                       80662   80811  99.82%
     协议:cmd:13531,MSG_C2L_GuildSearch                       160256  160287  99.98%
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                    54201   54201 100.00%
     协议:cmd:13505,MSG_C2L_GuildCreate                        80518   81102  99.28%
     协议正确数:1994074 协议总数:2236917 正确比:89.14%
     
     --------------------------
     
     协议延迟分布
          协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
     协议:cmd:13523,MSG_C2L_GuildSendMail                    98.75%   1.07%   0.18%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   96.53%   2.08%   1.38%   0.02%   0.00%   0.00%   0.00% 
     协议:cmd:10100,                                          4.28%  30.24%  52.84%  10.48%   2.16%   0.00%   0.00% 
     协议:cmd:13519,MSG_C2L_GuildApplyRatify                 100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13517,MSG_C2L_GuildApplyList                   98.65%   1.19%   0.16%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:11003,MSG_C2L_Flush                            94.50%   1.80%   3.64%   0.06%   0.00%   0.00%   0.00% 
     协议:cmd:13533,MSG_C2L_GuildGetMembers                  98.49%   1.22%   0.28%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13527,MSG_C2L_GuildQuit                        98.25%   1.28%   0.47%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13509,MSG_C2L_GuildManagerMember               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13507,MSG_C2L_GuildLogList                     98.46%   1.24%   0.30%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13513,MSG_C2L_GuildSetName                     98.70%   1.15%   0.15%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13515,MSG_C2L_GuildModifyNotice                98.75%   1.11%   0.14%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13501,MSG_C2L_GuildList                        98.67%   1.15%   0.18%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13525,MSG_C2L_GuildSignIn                      98.76%   0.90%   0.34%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13521,MSG_C2L_GuildUserApply                   97.59%   1.71%   0.71%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13535,MSG_C2L_GuildGetDeclaration              97.15%   1.99%   0.85%   0.01%   0.00%   0.00%   0.00% 
     协议:cmd:13529,MSG_C2L_GuildDisband                     97.57%   1.76%   0.67%   0.01%   0.00%   0.00%   0.00% 
     协议:cmd:13531,MSG_C2L_GuildSearch                      96.97%   2.06%   0.96%   0.02%   0.00%   0.00%   0.00% 
     协议:cmd:13511,MSG_C2L_GuildModifyInfo                  98.61%   1.19%   0.20%   0.00%   0.00%   0.00%   0.00% 
     协议:cmd:13505,MSG_C2L_GuildCreate                      98.73%   1.09%   0.18%   0.00%   0.00%   0.00%   0.00% 
     
     ---------------------
     
     logic服数情况
     
     ```

   - logic 数据

     ```go
     --------------------------
     
     所有超时的left
     
     --------------------------
     
     logic服务器启动时间:1.083883244s
     logic账号总数:5000
     
     --------------------------
     
     协议处理超时分布
          协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
     协议:     event:0(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100007(   1)                                0.00%   0.00%   0.00%  100.00%   0.00%   0.00%   0.00% 
     协议:       13521(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13503(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100045(  98)                                0.00%  98.98%   1.02%   0.00%   0.00%   0.00%   0.00% 
     协议:       13505(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13533(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
     协议:       13507(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     协议:      100002(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
     
     ```

   - 分析

     + guildList优化后，延迟比之前好了
     + userApply和applyRatify还是有问题，userApply是因为公会的申请列表满了，applyRatify是因为一下子管理的申请者超出了上限，本地人少的时候这两个不是很明显
     

#### 第五版 - 公会代码整体修改后压测

1. robot数据

   ```
		[roobot@172-21-173-36 bin]$ ./robot_analyse.sh 
		2021-08-04 21:53:58 压测数据分析
		压测基础情况
		开始时间：2021/08/04 21:48:32, 结束时间:2021/08/04 21:53:26, 耗时:294(s)
		登录成功机器人总数：5000
		总消息数:2295890， 平均每秒消息数:7809

		--------------------------

		协议正确率分布
		协议                                                      正确      总数      正确总占比  正确比   
		协议:cmd:13523,MSG_C2L_GuildSendMail                      34822   39621            1.51%   87.89%
		协议:cmd:13503,MSG_C2L_GuildGetMyInfo                    251556  251556           10.93%  100.00%
		协议:cmd:13519,MSG_C2L_GuildApplyRatify                    1850    2001            0.08%   92.45%
		协议:cmd:13517,MSG_C2L_GuildApplyList                     39455   39463            1.71%   99.98%
		协议:cmd:10100,                                            5000    5000            0.22%  100.00%
		协议:cmd:13533,MSG_C2L_GuildGetMembers                   146352  219631            6.36%   66.64%
		协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.22%  100.00%
		协议:cmd:13527,MSG_C2L_GuildQuit                          79918   79921            3.47%  100.00%
		协议:cmd:13513,MSG_C2L_GuildSetName                       28520   38616            1.24%   73.86%
		协议:cmd:13509,MSG_C2L_GuildManagerMember                  2323    2505            0.10%   92.73%
		协议:cmd:13507,MSG_C2L_GuildLogList                      218452  218460            9.49%  100.00%
		协议:cmd:13515,MSG_C2L_GuildModifyNotice                  39612   39619            1.72%   99.98%
		协议:cmd:13501,MSG_C2L_GuildList                         644835  644835           28.03%  100.00%
		协议:cmd:13525,MSG_C2L_GuildSignIn                         4972    4972            0.22%  100.00%
		协议:cmd:13535,MSG_C2L_GuildGetDeclaration               128861  128867            5.60%  100.00%
		协议:cmd:13521,MSG_C2L_GuildUserApply                    200456  314882            8.71%   63.66%
		协议:cmd:13529,MSG_C2L_GuildDisband                       41996   42357            1.83%   99.15%
		协议:cmd:13531,MSG_C2L_GuildSearch                       128537  128595            5.59%   99.95%
		协议:cmd:13511,MSG_C2L_GuildModifyInfo                    39921   39929            1.74%   99.98%
		协议:cmd:13505,MSG_C2L_GuildCreate                        43005   55060            1.87%   78.11%
		协议正确数:2085443 协议总数:2300890 正确比:90.64%

		--------------------------

		协议延迟分布
		     协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
		协议:cmd:13523,MSG_C2L_GuildSendMail                    98.66%   0.93%   0.42%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   96.19%   1.95%   1.86%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13519,MSG_C2L_GuildApplyRatify                 97.84%   1.57%   0.59%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13517,MSG_C2L_GuildApplyList                   98.61%   0.95%   0.44%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:10100,                                         16.76%  21.74%  53.26%   7.58%   0.66%   0.00%   0.00%   0.00% 
		协议:cmd:13533,MSG_C2L_GuildGetMembers                  97.78%   1.33%   0.90%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:11003,MSG_C2L_Flush                            95.02%   3.64%   1.34%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13527,MSG_C2L_GuildQuit                        97.46%   1.46%   1.07%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13513,MSG_C2L_GuildSetName                     98.43%   1.10%   0.47%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13509,MSG_C2L_GuildManagerMember               97.59%   1.64%   0.77%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13507,MSG_C2L_GuildLogList                     97.18%   1.61%   1.19%   0.02%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13515,MSG_C2L_GuildModifyNotice                98.56%   1.00%   0.43%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13501,MSG_C2L_GuildList                        98.64%   1.01%   0.35%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13525,MSG_C2L_GuildSignIn                      97.30%   1.67%   1.03%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13535,MSG_C2L_GuildGetDeclaration              97.57%   1.52%   0.91%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13521,MSG_C2L_GuildUserApply                   97.48%   1.60%   0.93%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13529,MSG_C2L_GuildDisband                     97.65%   1.50%   0.85%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13531,MSG_C2L_GuildSearch                      97.50%   1.59%   0.91%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13511,MSG_C2L_GuildModifyInfo                  98.59%   1.04%   0.37%   0.00%   0.00%   0.00%   0.00%   0.00% 
		协议:cmd:13505,MSG_C2L_GuildCreate                      98.61%   0.99%   0.40%   0.00%   0.00%   0.00%   0.00%   0.00% 

		---------------------

		logic服数情况
   ```
   
2. 游戏数据

   ```
	[roobot@172-21-254-181 bin]$ ./logic_analyse.sh 

	--------------------------

	所有超时的left
	[2021/08/04 21:53:27.025787] gate(pck/s: 14078 sum: 2453532 left:1691) logic db(pck/s: 52 sum: 29517 left:0) redis db(pck/s: 1326 sum: 66647 left:2036) 

	--------------------------

	logic服务器启动时间:1.118654013s
	logic账号总数:0

	--------------------------

	协议处理超时分布
	     协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
	协议:       10104(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:      100043( 441)                                0.00%  41.50%  56.69%   1.36%   0.45%   0.00%   0.00% 
	协议:       13501(   5)                               40.00%  40.00%  20.00%   0.00%   0.00%   0.00%   0.00% 
	协议:       13521(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:       13513(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:       13533(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:       13507(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:       13527(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:       13529(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   ```
   
3. metric
   
   ![](./pic/guild-********.png)

4. pprof

   ```
 	[roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-********215102
	File: service
	Type: inuse_objects
	Time: Aug 4, 2021 at 9:51pm (CST)
	Entering interactive mode (type "help" for commands, "o" for options)
	(pprof) top20
	Showing nodes accounting for 1658766, 62.66% of 2647401 total
	Dropped 145 nodes (cum <= 13237)
	Showing top 20 nodes out of 140
	      flat  flat%   sum%        cum   cum%
	    262158  9.90%  9.90%     353915 13.37%  app/logic/character.newHero
	    141533  5.35% 15.25%     141533  5.35%  app/logic/character.(*Handbook).Add
	    136544  5.16% 20.41%     136544  5.16%  app/logic/character.newShop
	    124060  4.69% 25.09%     124060  4.69%  app/logic/character.(*User).TaskTypeOnEvent
	    103778  3.92% 29.01%     103778  3.92%  app/protos/out/cl.(*Mail).Clone
	     91757  3.47% 32.48%      91757  3.47%  app/logic/character.initHeroFromData (inline)
	     85203  3.22% 35.70%      85203  3.22%  app/protos/out/cl.(*GuildLogInfo).Clone
	     80754  3.05% 38.75%      80754  3.05%  app/logic/character.(*Avatar).Add
	     76460  2.89% 41.64%      87383  3.30%  app/logic/command/guild.(*AsyncC2LGuildGetMembersReq).Resp
	     72095  2.72% 44.36%      72095  2.72%  app/logic/activity/guild.(*Guild).Snapshot
	     66073  2.50% 46.85%      66073  2.50%  app/logic/activity/base.NewLogs
	     65538  2.48% 49.33%      65538  2.48%  container/list.(*List).insertValue (inline)
	     65536  2.48% 51.81%      67959  2.57%  fmt.Sprintf
	     54613  2.06% 53.87%      54613  2.06%  encoding/json.(*decodeState).literalStore
	     52431  1.98% 55.85%     111826  4.22%  app/logic/mail.NewBox
	     43692  1.65% 57.50%      61169  2.31%  app/logic/character.(*User).loadDBModule
	     38230  1.44% 58.94%      38230  1.44%  app/logic/character.newEmblemM
	     32773  1.24% 60.18%      32773  1.24%  app/logic/character.(*User).NewUserSnapshot
	     32769  1.24% 61.42%      32769  1.24%  app/logic/character.newGemM
	     32769  1.24% 62.66%      32769  1.24%  app/logic/character.newRankAchieveAward
	(pprof) 
   ```
   
   ```
	[roobot@172-21-173-36 pprof]$ go tool pprof profile-********215102
	File: service
	Type: cpu
	Time: Aug 4, 2021 at 9:51pm (CST)
	Duration: 30.10s, Total samples = 13.73s (45.62%)
	Entering interactive mode (type "help" for commands, "o" for options)
	(pprof) top20
	Showing nodes accounting for 7.17s, 52.22% of 13.73s total
	Dropped 462 nodes (cum <= 0.07s)
	Showing top 20 nodes out of 234
	      flat  flat%   sum%        cum   cum%
	     1.89s 13.77% 13.77%      3.81s 27.75%  runtime.scanobject
	     0.89s  6.48% 20.25%      0.95s  6.92%  runtime.findObject
	     0.65s  4.73% 24.98%      0.65s  4.73%  runtime.markBits.isMarked (inline)
	     0.41s  2.99% 27.97%      0.41s  2.99%  runtime.epollwait
	     0.39s  2.84% 30.81%      0.48s  3.50%  syscall.Syscall
	     0.38s  2.77% 33.58%      0.38s  2.77%  runtime.futex
	     0.35s  2.55% 36.13%      0.46s  3.35%  app/protos/out/cl.(*GuildLogInfo).Clone
	     0.34s  2.48% 38.60%      0.45s  3.28%  runtime.heapBitsSetType
	     0.25s  1.82% 40.42%      1.23s  8.96%  runtime.mallocgc
	     0.18s  1.31% 41.73%      0.53s  3.86%  runtime.selectgo
	     0.17s  1.24% 42.97%      0.59s  4.30%  runtime.netpoll
	     0.16s  1.17% 44.14%      0.34s  2.48%  app/logic/activity/guild.(*Guild).resetDaily
	     0.16s  1.17% 45.30%      0.19s  1.38%  runtime.nanotime
	     0.16s  1.17% 46.47%      0.16s  1.17%  runtime.nextFreeFast (inline)
	     0.15s  1.09% 47.56%      0.15s  1.09%  runtime.memmove
	     0.14s  1.02% 48.58%      0.15s  1.09%  runtime.pageIndexOf (inline)
	     0.13s  0.95% 49.53%      0.13s  0.95%  app/logic/character.(*User).CreateTime
	     0.13s  0.95% 50.47%      0.14s  1.02%  runtime.mapaccess1_fast64
	     0.12s  0.87% 51.35%      0.16s  1.17%  runtime.lock2
	     0.12s  0.87% 52.22%      0.13s  0.95%  runtime.mapaccess1_fast32
	(pprof) 

   ```
   
   ```
	[roobot@172-21-173-36 pprof]$ go tool pprof allocs-********215102
	File: service
	Type: alloc_space
	Time: Aug 4, 2021 at 9:51pm (CST)
	Entering interactive mode (type "help" for commands, "o" for options)
	(pprof) top20
	Showing nodes accounting for 3.78GB, 72.81% of 5.19GB total
	Dropped 508 nodes (cum <= 0.03GB)
	Showing top 20 nodes out of 132
	      flat  flat%   sum%        cum   cum%
	    0.85GB 16.43% 16.43%     0.86GB 16.53%  app/logic/db/redisop.ClClient.SetSomeGuildLogInfoMCallSKs
	    0.43GB  8.29% 24.73%     0.43GB  8.29%  app/protos/out/cl.(*GuildLogInfo).Clone (inline)
	    0.29GB  5.65% 30.38%     0.29GB  5.65%  app/logic/character.(*User).NewUserSnapshot
	    0.27GB  5.15% 35.53%     0.27GB  5.20%  app/logic/db/redisop.DbClient.SetUserMCallSKs
	    0.24GB  4.70% 40.24%     0.24GB  4.70%  app/logic/activity/guild.(*Guild).Snapshot
	    0.20GB  3.94% 44.18%     0.33GB  6.39%  context.WithDeadline
	    0.19GB  3.73% 47.90%     0.19GB  3.73%  app/logic/db/redisop.DbClient.SetSomeGuildUserMCall
	    0.17GB  3.24% 51.15%     0.19GB  3.75%  app/logic/command/guild.(*AsyncC2LGuildGetMembersReq).Resp
	    0.16GB  3.00% 54.15%     0.16GB  3.00%  container/list.(*List).insertValue (inline)
	    0.14GB  2.77% 56.91%     0.14GB  2.77%  gitlab.qdream.com/kit/sea/skiplist.(*Set).GetRangeByRank
	    0.13GB  2.44% 59.36%     0.13GB  2.44%  time.AfterFunc
	    0.11GB  2.20% 61.56%     0.11GB  2.20%  app/logic/session.(*Client).Process
	    0.09GB  1.78% 63.33%     0.28GB  5.44%  app/logic/character.(*User).pushMsg
	    0.09GB  1.77% 65.10%     0.09GB  1.77%  app/logic/activity/base.(*Logs).GetAll
	    0.09GB  1.76% 66.86%     0.09GB  1.83%  app/logic/db/redisop.DbClient.SetSomeGuildMCall
	    0.08GB  1.52% 68.39%     0.09GB  1.65%  encoding/xml.(*Decoder).rawToken
	    0.08GB  1.49% 69.88%     0.08GB  1.50%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
	    0.05GB  1.05% 70.93%     0.11GB  2.15%  app/logic/character.(*User).SendSelfToClient
	    0.05GB  0.95% 71.88%     0.05GB  0.95%  strings.(*Builder).WriteString
	    0.05GB  0.93% 72.81%     0.53GB 10.13%  app/logic/command/guild.(*C2LGuildLogListCommand).Execute
	(pprof) 
   ```
