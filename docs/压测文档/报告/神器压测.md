## 神器压测报告


#### 第一版 - 由于神器铸造的资源给的不够多，后面的神器铸造都报了资源不足

1. robot数据

   ```
    2021-07-19 19:22:23 压测数据分析
	压测基础情况
	开始时间：2021/07/19 19:17:17, 结束时间:2021/07/19 19:22:11, 耗时:294(s)
	登录成功机器人总数：5000
	总消息数:1091469， 平均每秒消息数:3712
	
	--------------------------
	
	协议正确率分布
	协议                                                      正确      总数      正确总占比  正确比   
	协议:cmd:12601,MSG_C2L_ArtifactList                        5000    5000            0.46%  100.00%
	协议:cmd:12607,MSG_C2L_ArtifactStrength                  348486  360861           31.78%   96.57%
	协议:cmd:10100,                                            5000    5000            0.46%  100.00%
	协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.46%  100.00%
	协议:cmd:12609,MSG_C2L_ArtifactForge                     107667  360894            9.82%   29.83%
	协议:cmd:12605,MSG_C2L_ArtifactStarUp                    289714  289714           26.42%  100.00%
	协议:cmd:12603,MSG_C2L_ArtifactActivate                   70000   70000            6.38%  100.00%
	协议正确数:830867 协议总数:1096469 正确比:75.78%
	
	--------------------------
	
	协议延迟分布
	     协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
	协议:cmd:12601,MSG_C2L_ArtifactList                     98.68%   1.32%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:cmd:12607,MSG_C2L_ArtifactStrength                 98.45%   1.21%   0.34%   0.01%   0.00%   0.00%   0.00%   0.00% 
	协议:cmd:10100,                                         10.98%  14.78%  56.80%  16.14%   1.30%   0.00%   0.00%   0.00% 
	协议:cmd:11003,MSG_C2L_Flush                            94.86%   3.72%   1.42%   0.00%   0.00%   0.00%   0.00%   0.00% 
	协议:cmd:12609,MSG_C2L_ArtifactForge                    98.39%   1.19%   0.41%   0.01%   0.00%   0.00%   0.00%   0.00% 
	协议:cmd:12605,MSG_C2L_ArtifactStarUp                   98.48%   1.18%   0.34%   0.01%   0.00%   0.00%   0.00%   0.00% 
	协议:cmd:12603,MSG_C2L_ArtifactActivate                 98.12%   1.09%   0.78%   0.02%   0.00%   0.00%   0.00%   0.00% 
	
   ```
   
2. 游戏数据

   ```
   --------------------------
   
   所有超时的left
   
   --------------------------
   
   logic服务器启动时间:962.061977ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12605(   6)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12607(   7)                               57.14%  28.57%  14.29%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   4)                                0.00%  75.00%  25.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12609(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   ```
   
3. metric
   


#### 第二版 - 增加了初始资源，协议正确率没问题了

1. robot数据

   ```
   2021-07-19 19:34:54 压测数据分析
   压测基础情况
   开始时间：2021/07/19 19:29:54, 结束时间:2021/07/19 19:34:49, 耗时:295(s)
   登录成功机器人总数：5000
   总消息数:1046184， 平均每秒消息数:3546
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:12601,MSG_C2L_ArtifactList                        5000    5000            0.48%  100.00%
   协议:cmd:12607,MSG_C2L_ArtifactStrength                  349272  361679           33.23%   96.57%
   协议:cmd:10100,                                            5000    5000            0.48%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.48%  100.00%
   协议:cmd:12609,MSG_C2L_ArtifactForge                     315106  315106           29.98%  100.00%
   协议:cmd:12605,MSG_C2L_ArtifactStarUp                    289399  289399           27.53%  100.00%
   协议:cmd:12603,MSG_C2L_ArtifactActivate                   70000   70000            6.66%  100.00%
   协议正确数:1038777 协议总数:1051184 正确比:98.82%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
   协议:cmd:12601,MSG_C2L_ArtifactList                     97.62%   1.58%   0.80%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12607,MSG_C2L_ArtifactStrength                 97.94%   1.55%   0.51%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:10100,                                         15.66%  22.64%  56.46%   5.24%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.52%   1.78%   1.70%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12609,MSG_C2L_ArtifactForge                    97.90%   1.56%   0.54%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12605,MSG_C2L_ArtifactStarUp                   97.87%   1.60%   0.53%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12603,MSG_C2L_ArtifactActivate                 97.86%   1.80%   0.35%   0.00%   0.00%   0.00%   0.00%   0.00% 
   
   ```
   
2. 游戏数据

   ```
   --------------------------
   
   所有超时的left
   
   --------------------------
   
   logic服务器启动时间:1.053471011s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   5)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       10104(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12603(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12605(   5)                               60.00%  40.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12607(  11)                               72.73%  27.27%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12609(   7)                               57.14%  42.86%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   ```
   
3. metric
   
    ![](./pic/artifact-20210719.png)


#### 第三版 - 压测前增加了布阵，都用新号。

1. robot数据

   ```
     2021-07-20 22:44:21 压测数据分析
    压测基础情况
    开始时间：2021/07/20 22:38:56, 结束时间:2021/07/20 22:43:50, 耗时:294(s)
    登录成功机器人总数：5000
    总消息数:1342268， 平均每秒消息数:4565
    
    --------------------------
    
    协议正确率分布
    协议                                                      正确      总数      正确总占比  正确比   
    协议:cmd:12601,MSG_C2L_ArtifactList                        5000    5000            0.37%  100.00%
    协议:cmd:12607,MSG_C2L_ArtifactStrength                  415626  459971           30.85%   90.36%
    协议:cmd:10100,                                            5000    5000            0.37%  100.00%
    协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.37%  100.00%
    协议:cmd:11025,MSG_C2L_GM                                     0    5000            0.00%    0.00%
    协议:cmd:12609,MSG_C2L_ArtifactForge                     372133  401633           27.62%   92.65%
    协议:cmd:12605,MSG_C2L_ArtifactStarUp                    321195  390664           23.84%   82.22%
    协议:cmd:11029,MSG_C2L_Formation                           5000    5000            0.37%  100.00%
    协议:cmd:12603,MSG_C2L_ArtifactActivate                   70000   70000            5.20%  100.00%
    协议正确数:1198954 协议总数:1347268 正确比:88.99%
    
    --------------------------
    
    协议延迟分布
         协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
    协议:cmd:12601,MSG_C2L_ArtifactList                     61.12%  22.64%  16.24%   0.00%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:12607,MSG_C2L_ArtifactStrength                 69.34%  14.04%  14.12%   2.39%   0.11%   0.00%   0.00%   0.00% 
    协议:cmd:10100,                                         16.78%  20.80%  55.46%   6.20%   0.76%   0.00%   0.00%   0.00% 
    协议:cmd:11003,MSG_C2L_Flush                            96.16%   1.80%   2.04%   0.00%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:12609,MSG_C2L_ArtifactForge                    68.11%  14.41%  14.86%   2.50%   0.12%   0.00%   0.00%   0.00% 
    协议:cmd:12605,MSG_C2L_ArtifactStarUp                   66.29%  15.03%  15.80%   2.74%   0.14%   0.00%   0.00%   0.00% 
    协议:cmd:11029,MSG_C2L_Formation                        61.40%  22.88%  15.72%   0.00%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:12603,MSG_C2L_ArtifactActivate                 70.79%  15.13%  10.92%   2.79%   0.38%   0.00%   0.00%   0.00% 

   ```
   
2. 游戏数据

   ```
    --------------------------
   
   所有超时的left
   [2021/07/20 22:43:51.186514] gate(pck/s: 3501 sum: 1493980 left:34) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 457 sum: 45856    left:1121) 
   
   --------------------------
   
   logic服务器启动时间:994.122552ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       10104(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   7)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:1(   4)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12603(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12605(   6)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_1009(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12607(   4)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12609(   5)                               40.00%  20.00%  40.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100023(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   ```
   
3. metric
   
   ![](./pic/artifact-********.png)

4. pprof

   ```
      [roobot@172-21-173-36 pprof]$ go tool pprof profile-********224126
   File: service
   Type: cpu
   Time: Jul 20, 2021 at 10:41pm (CST)
   Duration: 30s, Total samples = 20.24s (67.46%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 10.85s, 53.61% of 20.24s total
   Dropped 460 nodes (cum <= 0.10s)
   Showing top 20 nodes out of 188
         flat  flat%   sum%        cum   cum%
        1.86s  9.19%  9.19%      3.16s 15.61%  runtime.scanobject
        1.23s  6.08% 15.27%      1.66s  8.20%  runtime.mapaccess1_fast32
           1s  4.94% 20.21%         1s  4.94%  runtime.epollwait
        0.75s  3.71% 23.91%      0.83s  4.10%  runtime.findObject
        0.68s  3.36% 27.27%      1.76s  8.70%  runtime.mallocgc
        0.60s  2.96% 30.24%      2.36s 11.66%  app/logic/character.(*Artifact).calcAttr
        0.60s  2.96% 33.20%      0.84s  4.15%  runtime.mapiternext
        0.52s  2.57% 35.77%      0.52s  2.57%  runtime.memhash32
        0.46s  2.27% 38.04%      0.47s  2.32%  syscall.Syscall
        0.38s  1.88% 39.92%      0.38s  1.88%  runtime.markBits.isMarked (inline)
        0.37s  1.83% 41.75%      0.77s  3.80%  runtime.mapassign_fast32
        0.36s  1.78% 43.53%      0.47s  2.32%  app/goxml.(*ArtifactInfoManager).Index (inline)
        0.35s  1.73% 45.26%      0.35s  1.73%  runtime.futex
        0.32s  1.58% 46.84%      0.46s  2.27%  runtime.heapBitsSetType
        0.31s  1.53% 48.37%      0.92s  4.55%  runtime.mapiterinit
        0.24s  1.19% 49.56%      2.55s 12.60%  runtime.findrunnable
        0.23s  1.14% 50.69%      0.29s  1.43%  runtime.nanotime (inline)
        0.21s  1.04% 51.73%      0.21s  1.04%  runtime.add (inline)
        0.19s  0.94% 52.67%      0.21s  1.04%  runtime.lock2
        0.19s  0.94% 53.61%      0.19s  0.94%  runtime.memclrNoHeapPointers
   ```
   
   ```
      [roobot@172-21-173-36 pprof]$ go tool pprof heap-********224126
   File: service
   Type: inuse_space
   Time: Jul 20, 2021 at 10:41pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 102.16MB, 64.29% of 158.91MB total
   Dropped 88 nodes (cum <= 0.79MB)
   Showing top 20 nodes out of 140
         flat  flat%   sum%        cum   cum%
         15MB  9.44%  9.44%    19.50MB 12.27%  app/logic/character.newHero (inline)
       9.50MB  5.98% 15.42%     9.50MB  5.98%  app/logic/character.(*Handbook).Add
       8.50MB  5.35% 20.77%       14MB  8.81%  app/logic/character.(*User).SendSelfToClient
       7.01MB  4.41% 25.18%     7.01MB  4.41%  app/logic/helper/monitor.(*Cmds).Add
          7MB  4.41% 29.58%        7MB  4.41%  app/logic/character.(*Hero).calcTotalAttr
          6MB  3.78% 33.36%    11.50MB  7.24%  app/logic/character.(*User).pushMsg
       5.50MB  3.46% 36.82%     5.50MB  3.46%  app/protos/out/cl.(*AttrInfo).Clone
          5MB  3.15% 39.97%        6MB  3.78%  app/logic/character.(*User).TaskTypeOnEvent
       4.50MB  2.83% 42.80%     4.50MB  2.83%  app/logic/character.initHeroFromData (inline)
       4.50MB  2.83% 45.63%     4.50MB  2.83%  container/list.(*List).insertValue (inline)
          4MB  2.52% 48.15%        4MB  2.52%  app/logic/character.(*Avatar).Add
       3.50MB  2.20% 50.35%     6.50MB  4.09%  app/logic/character.(*ArtifactM).add
          3MB  1.89% 52.24%     6.50MB  4.09%  app/logic/character.(*User).initModule
          3MB  1.89% 54.13%    22.50MB 14.16%  app/logic/character.(*HeroM).Add
          3MB  1.89% 56.02%        3MB  1.89%  github.com/google/btree.NewFreeList
          3MB  1.89% 57.91%        3MB  1.89%  app/logic/character.newArtifact (inline)
       2.64MB  1.66% 59.57%     3.18MB  2.00%  compress/flate.NewWriter
       2.50MB  1.57% 61.15%     2.50MB  1.57%  app/goxml.GenSimpleResource
       2.50MB  1.57% 62.72%     2.50MB  1.57%  app/protos/out/cl.(*Formation).Unmarshal
       2.50MB  1.57% 64.29%     2.50MB  1.57%  app/logic/character.newMazeM
   ```
   
   ```
      [roobot@172-21-173-36 pprof]$ go tool pprof allocs-********224126
   File: service
   Type: alloc_space
   Time: Jul 20, 2021 at 10:41pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 4197.45MB, 76.78% of 5467.04MB total
   Dropped 405 nodes (cum <= 27.34MB)
   Showing top 20 nodes out of 121
         flat  flat%   sum%        cum   cum%
    1192.11MB 21.81% 21.81%  1192.11MB 21.81%  app/logic/character.(*Artifact).calcAttr
     437.02MB  7.99% 29.80%  1629.12MB 29.80%  app/logic/character.(*ArtifactM).UpdateArtifactGlobalAttr
     314.18MB  5.75% 35.55%   317.18MB  5.80%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     276.02MB  5.05% 40.59%   466.03MB  8.52%  context.WithDeadline
     243.01MB  4.45% 45.04%   243.01MB  4.45%  container/list.(*List).insertValue (inline)
     190.01MB  3.48% 48.52%   190.01MB  3.48%  time.AfterFunc
     185.51MB  3.39% 51.91%   465.52MB  8.52%  app/logic/character.(*User).pushMsg
     169.03MB  3.09% 55.00%   345.54MB  6.32%  app/logic/character.(*User).SendSelfToClient
     148.51MB  2.72% 57.72%   148.51MB  2.72%  app/protos/out/cl.(*AttrInfo).Clone
     147.51MB  2.70% 60.41%   147.51MB  2.70%  app/logic/character.(*User).mergeResources
     139.26MB  2.55% 62.96%   139.26MB  2.55%  app/logic/db/redisop.DbClient.SetSomeCommonRankMCallSKs
     122.61MB  2.24% 65.20%   123.61MB  2.26%  app/logic/db/redisop.ClClient.SetSomeArtifactMCallSKs
        110MB  2.01% 67.22%   576.02MB 10.54%  app/logic/character.(*User).SendCmdToGateway
      95.51MB  1.75% 68.96%   128.01MB  2.34%  app/logic/character.(*User).TaskTypeOnEvent
      81.57MB  1.49% 70.46%    95.07MB  1.74%  encoding/xml.(*Decoder).rawToken
         77MB  1.41% 71.86%       77MB  1.41%  app/logic/session.(*Client).Process
      74.07MB  1.35% 73.22%    74.07MB  1.35%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
      72.50MB  1.33% 74.55%   318.02MB  5.82%  app/logic/character.(*ArtifactM).SendArtifactGlobalAttrToClient
      61.50MB  1.12% 75.67%    68.50MB  1.25%  app/logic/character.(*User).CalFormationPower
      60.50MB  1.11% 76.78%   496.53MB  9.08%  app/logic/character.(*User).Consume
   ```
