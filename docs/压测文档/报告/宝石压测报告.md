## 宝石压测报告

#### 第一版

1. robot数据

   ```
   2021-08-02 17:15:15 压测数据分析
   压测基础情况
   开始时间：2021/08/02 17:10:10, 结束时间:2021/08/02 17:13:08, 耗时:178(s)
   登录成功机器人总数：5000
   总消息数:824098， 平均每秒消息数:4629
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:10100,                                            5000   10000            0.60%   50.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.60%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  5000    5000            0.60%  100.00%
   协议:cmd:12709,MSG_C2L_GemConvert                        201447  201447           24.15%  100.00%
   协议:cmd:12705,MSG_C2L_GemCompose                        201463  201463           24.15%  100.00%
   协议:cmd:12707,MSG_C2L_GemDecompose                      200928  200928           24.09%  100.00%
   协议:cmd:12703,MSG_C2L_GemWear                           200260  200260           24.01%  100.00%
   协议:cmd:12701,MSG_C2L_GetGems                             5000    5000            0.60%  100.00%
   协议:cmd:11029,MSG_C2L_Formation                           5000    5000            0.60%  100.00%
   协议正确数:829098 协议总数:834098 正确比:99.40%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:10100,                                         14.10%  20.72%  57.60%   7.56%   0.02%   0.00%   0.00%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                            96.36%   1.82%   1.82%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11025,MSG_C2L_GM                                0.00%   0.06%   0.94%   0.92%   2.54%   8.68%   9.86%  77.00%
   协议:cmd:12709,MSG_C2L_GemConvert                       98.82%   0.73%   0.44%   0.01%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12705,MSG_C2L_GemCompose                       98.81%   0.74%   0.44%   0.01%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12707,MSG_C2L_GemDecompose                     98.86%   0.70%   0.43%   0.01%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12703,MSG_C2L_GemWear                          98.81%   0.74%   0.44%   0.01%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12701,MSG_C2L_GetGems                           2.16%  14.12%  12.62%  16.56%  30.32%  24.22%   0.00%   0.00%
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%  100.00%
   
   ---------------------
   
   logic服数情况
   ```

2. 游戏数据

   ```
   
   --------------------------
   
   所有超时的left
   [2021/08/02 17:10:11.137418] gate(pck/s: 976 sum: 11044 left:3302) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 0 sum: 20008 left:0) [2021/08/02 17:10:12.048141] gate(pck/s: 955 sum: 11999 left:3047) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 0 sum: 20008 left:0) [2021/08/02 17:10:13.245363] gate(pck/s: 1208 sum: 13207 left:2220) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 0 sum: 20008 left:0) [2021/08/02 17:10:14.118594] gate(pck/s: 850 sum: 14057 left:5292) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 0 sum: 20008 left:0) [2021/08/02 17:10:15.044836] gate(pck/s: 933 sum: 14990 left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1 sum: 20009 left:0)
   
   --------------------------
   
   logic服务器启动时间:1.297965791s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:       12705(   3)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       11025(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100006(  17)                                0.00%  82.35%  17.65%   0.00%   0.00%   0.00%   0.00%
   协议:       12707(   4)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12709(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:  event_1030(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12701(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100004(   4)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100023(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric
   ![gem](./pic/gem-********.png)

4. pprof 

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Aug 2, 2021 at 5:12pm (CST)
   Duration: 30s, Total samples = 7.53s (25.10%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 2670ms, 35.46% of 7530ms total
   Dropped 329 nodes (cum <= 37.65ms)
   Showing top 10 nodes out of 239
         flat  flat%   sum%        cum   cum%
        500ms  6.64%  6.64%      890ms 11.82%  runtime.scanobject
        380ms  5.05% 11.69%      380ms  5.05%  runtime.futex
        350ms  4.65% 16.33%      430ms  5.71%  syscall.Syscall
        320ms  4.25% 20.58%      320ms  4.25%  runtime.epollwait
        320ms  4.25% 24.83%      860ms 11.42%  runtime.mallocgc
        190ms  2.52% 27.36%      200ms  2.66%  runtime.findObject
        170ms  2.26% 29.61%      220ms  2.92%  runtime.heapBitsSetType
        160ms  2.12% 31.74%      160ms  2.12%  github.com/json-iterator/go.(*Stream).WriteString
        150ms  1.99% 33.73%      200ms  2.66%  runtime.nanotime
        130ms  1.73% 35.46%      130ms  1.73%  runtime.markBits.isMarked (inline)
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Aug 2, 2021 at 5:12pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 198.79MB, 65.01% of 305.78MB total
   Dropped 161 nodes (cum <= 1.53MB)
   Showing top 10 nodes out of 107
         flat  flat%   sum%        cum   cum%
      54.27MB 17.75% 17.75%   139.77MB 45.71%  app/logic/character.(*GemM).AddGem
         46MB 15.04% 32.79%    46.50MB 15.21%  app/logic/character.generateGemAttr
      43.50MB 14.23% 47.02%    85.51MB 27.96%  app/logic/character.newGem
         15MB  4.91% 51.92%    21.50MB  7.03%  app/logic/character.newHero (inline)
       9.50MB  3.11% 55.03%     9.50MB  3.11%  app/logic/character.(*Handbook).Add
          7MB  2.29% 57.32%        7MB  2.29%  app/logic/character.(*Hero).calcTotalAttr
       6.50MB  2.13% 59.45%     6.50MB  2.13%  app/logic/character.initHeroFromData (inline)
       6.01MB  1.96% 61.41%     6.01MB  1.96%  app/logic/helper/monitor.(*Cmds).Add
       5.50MB  1.80% 63.21%     7.50MB  2.45%  app/logic/character.(*User).initModule
       5.50MB  1.80% 65.01%       27MB  8.83%  app/logic/character.(*HeroM).Add
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   ^[[AFile: service
   Type: alloc_space
   Time: Aug 2, 2021 at 5:12pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 3386.01MB, 56.87% of 5953.61MB total
   Dropped 454 nodes (cum <= 29.77MB)
   Showing top 10 nodes out of 132
         flat  flat%   sum%        cum   cum%
    1150.28MB 19.32% 19.32%  1211.28MB 20.35%  app/logic/db/redisop.ClClient.SetSomeGemInfoMCallSKs
     417.03MB  7.00% 26.33%   682.55MB 11.46%  context.WithDeadline
     321.69MB  5.40% 31.73%   325.19MB  5.46%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     290.08MB  4.87% 36.60%   381.10MB  6.40%  app/logic/character.(*User).mergeResources
     265.02MB  4.45% 41.05%   265.52MB  4.46%  time.AfterFunc
     228.01MB  3.83% 44.88%   228.01MB  3.83%  app/logic/character.(*Gem).MergeGemAttr
     212.01MB  3.56% 48.44%   212.01MB  3.56%  app/protos/out/cl.(*GemAttr).Clone (inline)
     201.02MB  3.38% 51.82%   413.03MB  6.94%  app/protos/out/cl.(*GemInfo).Clone
     160.87MB  2.70% 54.52%   227.91MB  3.83%  app/logic/character.(*User).LogResourceAdd
     140.01MB  2.35% 56.87%   140.01MB  2.35%  app/protos/out/cl.(*GemAttr).MarshalToSizedBuffer
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-**************
   File: service
   Type: inuse_objects
   Time: Aug 2, 2021 at 5:12pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 3329584, 72.03% of 4622673 total
   Dropped 186 nodes (cum <= 23113)
   Showing top 10 nodes out of 82
         flat  flat%   sum%        cum   cum%
      1219012 26.37% 26.37%    2275821 49.23%  app/logic/character.newGem
      1136001 24.57% 50.94%    1152385 24.93%  app/logic/character.generateGemAttr
       234851  5.08% 56.03%     320054  6.92%  app/logic/character.newHero
       185689  4.02% 60.04%     185689  4.02%  app/protos/out/cl.(*HeroBody).Clone
       109227  2.36% 62.41%     283992  6.14%  app/logic/helper/sync.(*MessageSync).Push
       102523  2.22% 64.62%     102523  2.22%  app/logic/character.(*Handbook).Add
        98307  2.13% 66.75%      98307  2.13%  container/list.(*List).insertValue (inline)
        87381  1.89% 68.64%      98304  2.13%  app/logic/helper/sync.(*MessageSync).append
        85203  1.84% 70.48%      85203  1.84%  app/logic/character.initHeroFromData (inline)
        71390  1.54% 72.03%      71390  1.54%  app/logic/character.(*User).TaskTypeOnEvent
   (pprof)
   ```
