## 好友测报告



#### 老的修改

​	修改保存条件，只要满足了大于200条就开始保存，消息处理不过来了。

1. robot

   ```shell
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh 
   2021-06-08 18:56:34 压测数据分析
   压测基础情况
   开始时间：2021/06/08 18:31:57, 结束时间:2021/06/08 18:41:51, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:4779457， 平均每秒消息数:8046
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     60852  107257  56.73%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    523618  822679  63.65%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    89766  189482  47.37%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 638972  853970  74.82%
   协议:cmd:11503,MSG_C2L_FriendAdd                         435872  799306  54.53%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   481880  607243  79.36%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 85058  156472  54.36%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     237367  268177  88.51%
   协议:cmd:11520,MSG_C2L_FriendSearch                      226435  268019  84.48%
   协议:cmd:11501,MSG_C2L_FriendInfo                        256649  272942  94.03%
   协议:cmd:11508,MSG_C2L_FriendDelete                      373827  428910  87.16%
   协议正确数:3420296 协议总数:4784457 正确比:71.49%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         31.94%  58.76%   7.90%   0.36%   1.04%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.14%   1.24%   3.62%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                    0.88%   1.25%   4.10%   6.53%  13.07%  30.32%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    0.57%   0.21%   2.09%   3.27%  11.52%  40.36%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                   1.02%   1.27%   3.98%   6.58%  12.85%  30.41%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 1.04%   1.31%   5.78%   9.73%  15.90%  24.32%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                         0.66%   0.29%   2.14%   3.31%  11.40%  38.11%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                   0.97%   1.26%   3.80%   6.46%  12.75%  30.52%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                1.05%   1.33%   4.02%   6.71%  13.09%  30.56%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                     0.90%   1.24%   3.90%   6.31%  12.67%  30.10%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                      1.01%   1.33%   3.91%   6.56%  12.77%  30.25%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                        1.02%   1.46%   4.36%   7.45%  12.56%  29.73%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                      0.96%   1.31%   3.94%   6.57%  12.97%  30.48%   0.00% 
   
   
   ```

2. logic

   ```
   left堆满了10000条消息，处理不过来了。
   
   logic服务器启动时间:2.081853823s
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       11522(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(  14)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(3645)                                0.08%  41.89%  57.48%   0.55%   0.00%   0.00%   0.00% 
   协议:      100007(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11506(3491)                                0.43%  50.07%  48.50%   1.00%   0.00%   0.00%   0.00% 
   协议:       11516(3280)                                0.37%  53.32%  45.73%   0.55%   0.03%   0.00%   0.00% 
   协议:       11508(4060)                                0.05%  49.66%  49.48%   0.79%   0.02%   0.00%   0.00% 
   协议:       11518(  18)                               61.11%  22.22%  16.67%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save(18619)                                0.00%  50.41%  48.89%   0.69%   0.01%   0.00%   0.00% 
   协议:       11501(   9)                               55.56%  33.33%  11.11%   0.00%   0.00%   0.00%   0.00% 
   协议:       11510( 908)                                0.77%  50.66%  47.69%   0.88%   0.00%   0.00%   0.00% 
   协议:       11520(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022(18617)                                0.00%   4.36%  94.45%   1.19%   0.00%   0.01%   0.00% 
   协议:       11503(2955)                                2.81%  51.95%  44.81%   0.44%   0.00%   0.00%   0.00% 
   协议:       11512( 460)                                1.09%  53.04%  44.57%   1.30%   0.00%   0.00%   0.00% 
   
   ```



### 调整

把保存协议进行了修改，去掉很多的clone，直接在logic里面marshal好传byte进来

1. robot

   ```shell
   2021-06-09 11:34:06 压测数据分析
   压测基础情况
   开始时间：2021/06/09 11:23:29, 结束时间:2021/06/09 11:33:23, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:5043016， 平均每秒消息数:8489
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     66371  107776  61.58%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    846567 1011555  83.69%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    95498  192311  49.66%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 872778  892089  97.84%
   协议:cmd:11503,MSG_C2L_FriendAdd                         527519  818090  64.48%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   564328  612423  92.15%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 92333  156094  59.15%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     252649  270376  93.44%
   协议:cmd:11520,MSG_C2L_FriendSearch                      243394  270499  89.98%
   协议:cmd:11508,MSG_C2L_FriendDelete                      417629  431654  96.75%
   协议:cmd:11501,MSG_C2L_FriendInfo                        275149  275149 100.00%
   协议正确数:4264215 协议总数:5048016 正确比:84.47%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         40.88%  52.38%   6.74%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.18%   1.06%   3.76%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   75.66%   4.74%   3.59%   3.57%   6.92%   5.52%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   75.22%   5.69%   6.20%   8.16%   4.44%   0.28%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  76.19%   4.19%   3.41%   3.50%   7.04%   5.67%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                75.49%   5.90%   8.86%   4.98%   2.98%   1.79%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        74.56%   4.73%   6.12%   8.44%   5.50%   0.64%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  74.46%   4.31%   3.62%   3.79%   7.61%   6.21%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               76.30%   4.26%   3.41%   3.48%   7.02%   5.53%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    76.35%   4.05%   3.36%   3.55%   6.96%   5.73%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     76.19%   4.19%   3.42%   3.50%   7.00%   5.71%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     76.05%   4.22%   3.41%   3.53%   7.02%   5.77%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       74.75%   4.28%   3.62%   3.59%   7.08%   6.68%   0.00% 
   ```

2. logic

   ```
   2021-06-09 11:34:06 压测数据分析
   压测基础情况
   开始时间：2021/06/09 11:23:29, 结束时间:2021/06/09 11:33:23, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:5043016， 平均每秒消息数:8489
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     66371  107776  61.58%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    846567 1011555  83.69%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    95498  192311  49.66%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 872778  892089  97.84%
   协议:cmd:11503,MSG_C2L_FriendAdd                         527519  818090  64.48%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   564328  612423  92.15%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 92333  156094  59.15%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     252649  270376  93.44%
   协议:cmd:11520,MSG_C2L_FriendSearch                      243394  270499  89.98%
   协议:cmd:11508,MSG_C2L_FriendDelete                      417629  431654  96.75%
   协议:cmd:11501,MSG_C2L_FriendInfo                        275149  275149 100.00%
   协议正确数:4264215 协议总数:5048016 正确比:84.47%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         40.88%  52.38%   6.74%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.18%   1.06%   3.76%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   75.66%   4.74%   3.59%   3.57%   6.92%   5.52%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   75.22%   5.69%   6.20%   8.16%   4.44%   0.28%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  76.19%   4.19%   3.41%   3.50%   7.04%   5.67%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                75.49%   5.90%   8.86%   4.98%   2.98%   1.79%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        74.56%   4.73%   6.12%   8.44%   5.50%   0.64%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  74.46%   4.31%   3.62%   3.79%   7.61%   6.21%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               76.30%   4.26%   3.41%   3.48%   7.02%   5.53%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    76.35%   4.05%   3.36%   3.55%   6.96%   5.73%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     76.19%   4.19%   3.42%   3.50%   7.00%   5.71%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     76.05%   4.22%   3.41%   3.53%   7.02%   5.77%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       74.75%   4.28%   3.62%   3.59%   7.08%   6.68%   0.00% 
   
   ```

3. metric

   gc次数会有回落，很不错。

   ![friend-********](./pic/friend-********.png)



4. profile

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********112829 
   File: service
   Type: cpu
   Time: Jun 9, 2021 at 11:28am (CST)
   Duration: 30.19s, Total samples = 24.65s (81.66%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 10190ms, 41.34% of 24650ms total
   Dropped 502 nodes (cum <= 123.25ms)
   Showing top 10 nodes out of 213
         flat  flat%   sum%        cum   cum%
       3020ms 12.25% 12.25%     4830ms 19.59%  runtime.scanobject
       1120ms  4.54% 16.80%     1200ms  4.87%  app/protos/in/db.(*FriendLike).Size
        970ms  3.94% 20.73%      990ms  4.02%  runtime.findObject
        890ms  3.61% 24.34%     1160ms  4.71%  app/logic/activity/friend.(*Friends).GetFriendNum
        850ms  3.45% 27.79%     1510ms  6.13%  runtime.mapiternext
        830ms  3.37% 31.16%     2100ms  8.52%  runtime.mallocgc
        670ms  2.72% 33.87%      680ms  2.76%  github.com/golang/snappy.encodeBlock
        660ms  2.68% 36.55%      660ms  2.68%  runtime.epollwait
        640ms  2.60% 39.15%      710ms  2.88%  syscall.Syscall
        540ms  2.19% 41.34%      540ms  2.19%  runtime.markBits.isMarked (inline)
   
   ```

5. heap 

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-********112829 
   File: service
   Type: inuse_space
   Time: Jun 9, 2021 at 11:28am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 315.51MB, 75.84% of 416.02MB total
   Dropped 159 nodes (cum <= 2.08MB)
   Showing top 10 nodes out of 103
         flat  flat%   sum%        cum   cum%
     217.42MB 52.26% 52.26%   217.42MB 52.26%  app/protos/in/db.(*Friends).Unmarshal
      15.50MB  3.73% 55.99%    15.50MB  3.73%  app/logic/activity/friend.(*Friends).besentLike
      15.07MB  3.62% 59.61%    15.07MB  3.62%  github.com/gogo/protobuf/proto.(*Buffer).grow
         15MB  3.61% 63.22%       15MB  3.61%  app/logic/character.(*User).NewUserSnapshot
         13MB  3.12% 66.34%       13MB  3.12%  app/logic/activity/friend.(*Friends).sendLikeOne
      12.01MB  2.89% 69.23%    12.01MB  2.89%  app/logic/activity/friend.(*Friends).add
      10.01MB  2.41% 71.63%    10.01MB  2.41%  app/logic/helper/monitor.(*Cmds).Add
       7.50MB  1.80% 73.44%     7.50MB  1.80%  container/list.(*List).insertValue
       5.51MB  1.32% 74.76%     5.51MB  1.32%  app/logic/character.(*User).AddRecommendedFriend
       4.50MB  1.08% 75.84%        8MB  1.92%  app/logic/character.(*User).initModule
   
   ```

调整后的正式测试


#### 第一版

5000人第一次。老版本robota

总结：消息0-10只有82%，gc一直在涨，cpu消耗一直再涨

left最大到5000

1. robot

   ```
   2021-06-10 11:40:30 压测数据分析
   压测基础情况
   开始时间：2021/06/10 11:28:12, 结束时间:2021/06/10 11:38:06, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:4917025， 平均每秒消息数:8277
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     72559  107891  67.25%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    632948  791522  79.97%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    95588  194777  49.08%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 958775  971976  98.64%
   协议:cmd:11503,MSG_C2L_FriendAdd                         546891  839955  65.11%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   580045  616296  94.12%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 82927  141791  58.49%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     265032  270794  97.87%
   协议:cmd:11520,MSG_C2L_FriendSearch                      239684  269657  88.88%
   协议:cmd:11508,MSG_C2L_FriendDelete                      391745  431789  90.73%
   协议:cmd:11501,MSG_C2L_FriendInfo                        275577  275577 100.00%
   协议正确数:4151771 协议总数:4922025 正确比:84.35%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         25.54%  36.10%  37.88%   0.48%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   82.79%   3.19%   3.86%   4.66%   4.64%   0.86%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.80%   1.04%   3.16%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   79.73%   4.53%   8.94%   5.42%   1.15%   0.24%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  82.33%   3.24%   3.89%   4.73%   4.94%   0.87%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                82.71%   5.50%   6.79%   2.93%   1.77%   0.30%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        81.75%   4.10%   7.75%   4.85%   1.32%   0.23%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  81.43%   3.29%   4.11%   4.92%   5.28%   0.97%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               80.66%   3.48%   4.24%   5.20%   5.42%   0.99%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    82.23%   3.15%   3.90%   4.82%   4.97%   0.92%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     82.03%   3.24%   3.96%   4.80%   5.04%   0.93%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     81.42%   3.32%   4.14%   4.97%   5.18%   0.97%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       82.52%   3.16%   3.90%   4.63%   4.89%   0.90%   0.00% 
   
   ---------------------
   
   ```

   

2. logic

   ```shell
   
   所有超时的left
   [2021/06/10 11:37:59.241023] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:59.066940733 +0800 CST msgs count per second: gate(pck/s: 6400 sum: 5152864  left:1808) logic db(pck/s: 37 sum: 36652 left:0) redis db(pck/s: 38 sum: 97208 left:0) 
   [2021/06/10 11:38:07.179359] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:38:07.166955561 +0800 CST msgs count per second: gate(pck/s: 11004 sum: 5229114  left:0) logic db(pck/s: 26 sum: 36991 left:0) redis db(pck/s: 2682 sum: 100209 left:2360) 
   [2021/06/10 11:31:12.419595] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:31:12.272724861 +0800 CST msgs count per second: gate(pck/s: 12792 sum: 1543427  left:1475) logic db(pck/s: 55 sum: 18063 left:0) redis db(pck/s: 56 sum: 38249 left:19) 
   [2021/06/10 11:31:45.341099] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:31:45.32623747 +0800 CST msgs count per second: gate(pck/s: 10757 sum: 1827522  left:1379) logic db(pck/s: 68 sum: 19572 left:0) redis db(pck/s: 69 sum: 44790 left:0) 
   [2021/06/10 11:32:06.277348] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:32:06.267005898 +0800 CST msgs count per second: gate(pck/s: 11882 sum: 2011552  left:1983) logic db(pck/s: 58 sum: 20488 left:0) redis db(pck/s: 59 sum: 45726 left:4) 
   [2021/06/10 11:32:22.248649] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:32:22.170507925 +0800 CST msgs count per second: gate(pck/s: 5775 sum: 2152289  left:1050) logic db(pck/s: 66 sum: 21206 left:0) redis db(pck/s: 797 sum: 50965 left:0) 
   [2021/06/10 11:32:40.237915] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:32:40.174480646 +0800 CST msgs count per second: gate(pck/s: 4920 sum: 2311894  left:1092) logic db(pck/s: 28 sum: 21987 left:0) redis db(pck/s: 28 sum: 52258 left:0) 
   [2021/06/10 11:32:42.326428] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:32:42.26699643 +0800 CST msgs count per second: gate(pck/s: 10895 sum: 2330444  left:1182) logic db(pck/s: 63 sum: 22091 left:0) redis db(pck/s: 64 sum: 52364 left:0) 
   [2021/06/10 11:32:59.175315] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:32:59.079335846 +0800 CST msgs count per second: gate(pck/s: 6708 sum: 2480028  left:1199) logic db(pck/s: 42 sum: 22892 left:0) redis db(pck/s: 43 sum: 53181 left:0) 
   [2021/06/10 11:33:00.279380] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:33:00.26729622 +0800 CST msgs count per second: gate(pck/s: 10355 sum: 2490383  left:1312) logic db(pck/s: 50 sum: 22942 left:0) redis db(pck/s: 51 sum: 53232 left:15) 
   [2021/06/10 11:33:12.299405] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:33:12.266993568 +0800 CST msgs count per second: gate(pck/s: 10704 sum: 2595739  left:1423) logic db(pck/s: 58 sum: 23493 left:0) redis db(pck/s: 849 sum: 58300 left:0) 
   [2021/06/10 11:33:25.268506] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:33:25.21133157 +0800 CST msgs count per second: gate(pck/s: 5596 sum: 2707579  left:1866) logic db(pck/s: 55 sum: 24054 left:0) redis db(pck/s: 56 sum: 59367 left:0) 
   [2021/06/10 11:33:37.188720] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:33:37.186046429 +0800 CST msgs count per second: gate(pck/s: 7318 sum: 2817677  left:1335) logic db(pck/s: 22 sum: 24621 left:0) redis db(pck/s: 22 sum: 59945 left:0) 
   [2021/06/10 11:33:39.349517] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:33:39.266961661 +0800 CST msgs count per second: gate(pck/s: 11024 sum: 2837386  left:1614) logic db(pck/s: 62 sum: 24726 left:0) redis db(pck/s: 63 sum: 60052 left:0) 
   [2021/06/10 11:34:03.289242] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:03.266949762 +0800 CST msgs count per second: gate(pck/s: 9495 sum: 3047611  left:2079) logic db(pck/s: 54 sum: 25828 left:0) redis db(pck/s: 638 sum: 66145 left:0) 
   [2021/06/10 11:34:15.388407] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:15.267018887 +0800 CST msgs count per second: gate(pck/s: 10858 sum: 3156295  left:2166) logic db(pck/s: 62 sum: 26403 left:0) redis db(pck/s: 63 sum: 66762 left:0) 
   [2021/06/10 11:34:25.294714] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:25.185267556 +0800 CST msgs count per second: gate(pck/s: 3579 sum: 3243835  left:2100) logic db(pck/s: 21 sum: 26846 left:1) redis db(pck/s: 22 sum: 67215 left:0) 
   [2021/06/10 11:34:35.182841] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:35.067557529 +0800 CST msgs count per second: gate(pck/s: 6333 sum: 3331147  left:1332) logic db(pck/s: 55 sum: 27306 left:0) redis db(pck/s: 56 sum: 67683 left:0) 
   [2021/06/10 11:34:36.400167] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:36.266940776 +0800 CST msgs count per second: gate(pck/s: 12774 sum: 3343921  left:4268) logic db(pck/s: 60 sum: 27366 left:0) redis db(pck/s: 61 sum: 67744 left:0) 
   [2021/06/10 11:34:45.270632] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:45.266907309 +0800 CST msgs count per second: gate(pck/s: 8482 sum: 3420305  left:2117) logic db(pck/s: 45 sum: 27777 left:0) redis db(pck/s: 46 sum: 68163 left:0) 
   [2021/06/10 11:34:56.170197] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:56.067578677 +0800 CST msgs count per second: gate(pck/s: 6083 sum: 3516963  left:2199) logic db(pck/s: 52 sum: 28277 left:0) redis db(pck/s: 53 sum: 73674 left:0) 
   [2021/06/10 11:34:57.273390] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:34:57.266950578 +0800 CST msgs count per second: gate(pck/s: 14397 sum: 3531360  left:1655) logic db(pck/s: 47 sum: 28324 left:0) redis db(pck/s: 48 sum: 73722 left:5) 
   [2021/06/10 11:35:06.294358] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:35:06.267294379 +0800 CST msgs count per second: gate(pck/s: 12796 sum: 3610963  left:2195) logic db(pck/s: 46 sum: 28774 left:0) redis db(pck/s: 47 sum: 74180 left:17) 
   [2021/06/10 11:35:15.284370] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:35:15.266919738 +0800 CST msgs count per second: gate(pck/s: 10754 sum: 3690328  left:1096) logic db(pck/s: 36 sum: 29159 left:0) redis db(pck/s: 37 sum: 74573 left:25) 
   [2021/06/10 11:35:32.170811] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:35:32.067576586 +0800 CST msgs count per second: gate(pck/s: 6042 sum: 3840803  left:1121) logic db(pck/s: 55 sum: 29951 left:0) redis db(pck/s: 56 sum: 75380 left:0) 
   [2021/06/10 11:35:33.283732] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:35:33.276187245 +0800 CST msgs count per second: gate(pck/s: 9153 sum: 3849956  left:2075) logic db(pck/s: 50 sum: 30001 left:0) redis db(pck/s: 51 sum: 75431 left:0) 
   [2021/06/10 11:35:42.432630] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:35:42.267275532 +0800 CST msgs count per second: gate(pck/s: 11313 sum: 3934112  left:2709) logic db(pck/s: 47 sum: 30378 left:0) redis db(pck/s: 751 sum: 80047 left:0) 
   [2021/06/10 11:35:49.198248] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:35:49.181716857 +0800 CST msgs count per second: gate(pck/s: 4710 sum: 3994929  left:1424) logic db(pck/s: 30 sum: 30676 left:0) redis db(pck/s: 30 sum: 81120 left:0) 
   [2021/06/10 11:35:51.450732] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:35:51.266905696 +0800 CST msgs count per second: gate(pck/s: 11003 sum: 4014868  left:3029) logic db(pck/s: 63 sum: 30793 left:0) redis db(pck/s: 64 sum: 81239 left:0) 
   [2021/06/10 11:36:05.256510] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:05.067339861 +0800 CST msgs count per second: gate(pck/s: 6920 sum: 4135445  left:2046) logic db(pck/s: 49 sum: 31418 left:0) redis db(pck/s: 50 sum: 81876 left:0) 
   [2021/06/10 11:36:06.373912] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:06.266898134 +0800 CST msgs count per second: gate(pck/s: 12104 sum: 4147549  left:4141) logic db(pck/s: 62 sum: 31480 left:0) redis db(pck/s: 63 sum: 81939 left:0) 
   [2021/06/10 11:36:14.282739] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:14.067572202 +0800 CST msgs count per second: gate(pck/s: 5859 sum: 4216380  left:2448) logic db(pck/s: 42 sum: 31843 left:0) redis db(pck/s: 43 sum: 82309 left:0) 
   [2021/06/10 11:36:15.427009] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:15.266972803 +0800 CST msgs count per second: gate(pck/s: 11368 sum: 4227748  left:2625) logic db(pck/s: 78 sum: 31921 left:0) redis db(pck/s: 79 sum: 82388 left:0) 
   [2021/06/10 11:36:21.403534] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:21.267011918 +0800 CST msgs count per second: gate(pck/s: 10873 sum: 4282558  left:2241) logic db(pck/s: 66 sum: 32198 left:0) redis db(pck/s: 67 sum: 82670 left:0) 
   [2021/06/10 11:36:28.242413] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:28.167833286 +0800 CST msgs count per second: gate(pck/s: 4043 sum: 4343809  left:1825) logic db(pck/s: 40 sum: 32482 left:0) redis db(pck/s: 415 sum: 83343 left:0) 
   [2021/06/10 11:36:36.287401] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:36.267046249 +0800 CST msgs count per second: gate(pck/s: 11889 sum: 4414591  left:2390) logic db(pck/s: 55 sum: 32857 left:0) redis db(pck/s: 56 sum: 88342 left:14) 
   [2021/06/10 11:36:45.401801] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:45.267007579 +0800 CST msgs count per second: gate(pck/s: 11400 sum: 4495372  left:2280) logic db(pck/s: 69 sum: 33289 left:0) redis db(pck/s: 70 sum: 88782 left:0) 
   [2021/06/10 11:36:58.185611] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:36:58.167807884 +0800 CST msgs count per second: gate(pck/s: 4338 sum: 4612287  left:1367) logic db(pck/s: 25 sum: 33880 left:0) redis db(pck/s: 25 sum: 89384 left:0) 
   [2021/06/10 11:37:05.307820] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:05.067056296 +0800 CST msgs count per second: gate(pck/s: 5888 sum: 4670608  left:2790) logic db(pck/s: 34 sum: 34209 left:0) redis db(pck/s: 35 sum: 89719 left:0) 
   [2021/06/10 11:37:06.568115] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:06.567674787 +0800 CST msgs count per second: gate(pck/s: 14412 sum: 4685020  left:5773) logic db(pck/s: 61 sum: 34270 left:0) redis db(pck/s: 62 sum: 89781 left:0) 
   [2021/06/10 11:37:11.268813] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:11.067253855 +0800 CST msgs count per second: gate(pck/s: 6369 sum: 4725708  left:2259) logic db(pck/s: 50 sum: 34479 left:0) redis db(pck/s: 51 sum: 89994 left:0) 
   [2021/06/10 11:37:16.308382] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:16.291111726 +0800 CST msgs count per second: gate(pck/s: 10411 sum: 4774029  left:1078) logic db(pck/s: 24 sum: 34695 left:0) redis db(pck/s: 25 sum: 90215 left:20) 
   [2021/06/10 11:37:21.268405] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:21.267011534 +0800 CST msgs count per second: gate(pck/s: 9959 sum: 4816581  left:1329) logic db(pck/s: 41 sum: 34928 left:0) redis db(pck/s: 1129 sum: 93402 left:76) 
   [2021/06/10 11:37:22.401267] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:22.16790524 +0800 CST msgs count per second: gate(pck/s: 7215 sum: 4823796  left:3565) logic db(pck/s: 40 sum: 34968 left:0) redis db(pck/s: 1172 sum: 94574 left:0) 
   [2021/06/10 11:37:27.282607] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:27.267717287 +0800 CST msgs count per second: gate(pck/s: 14646 sum: 4871350  left:1849) logic db(pck/s: 39 sum: 35181 left:0) redis db(pck/s: 40 sum: 95710 left:14) 
   [2021/06/10 11:37:34.271197] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:34.177231067 +0800 CST msgs count per second: gate(pck/s: 4084 sum: 4929190  left:2275) logic db(pck/s: 37 sum: 35489 left:0) redis db(pck/s: 38 sum: 96024 left:0) 
   [2021/06/10 11:37:40.317967] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:40.16790077 +0800 CST msgs count per second: gate(pck/s: 5494 sum: 4985061  left:2416) logic db(pck/s: 43 sum: 35799 left:0) redis db(pck/s: 44 sum: 96339 left:0) 
   [2021/06/10 11:37:45.293432] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:45.267003306 +0800 CST msgs count per second: gate(pck/s: 10493 sum: 5027893  left:1653) logic db(pck/s: 46 sum: 36018 left:0) redis db(pck/s: 47 sum: 96562 left:22) 
   [2021/06/10 11:37:46.361593] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:46.166894547 +0800 CST msgs count per second: gate(pck/s: 8854 sum: 5036747  left:4887) logic db(pck/s: 52 sum: 36070 left:0) redis db(pck/s: 53 sum: 96615 left:0) 
   [2021/06/10 11:37:52.323292] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:37:52.181509868 +0800 CST msgs count per second: gate(pck/s: 4435 sum: 5089509  left:2801) logic db(pck/s: 63 sum: 36337 left:0) redis db(pck/s: 64 sum: 96887 left:0) 
   
   --------------------------
   
   logic服务器启动时间:942.412644ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11522(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(   4)                               25.00%  75.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11506(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11516(   6)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11508(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11518(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save( 551)                                1.27%   5.08%  15.97%  23.96%  43.38%  10.34%   0.00% 
   协议:       11501(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11510(   2)                               50.00%   0.00%  50.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11520(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022( 550)                                0.00%   4.91%  13.27%  20.55%  34.00%  27.27%   0.00% 
   协议:       11503(  10)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   
   ```

3. metric

   ![friend-********-11-01](./pic/friend-********-11-01.png)
   
4. profile

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-************** 
   File: service
   Type: cpu
   Time: Jun 10, 2021 at 11:33am (CST)
   Duration: 30.16s, Total samples = 28.71s (95.21%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 13530ms, 47.13% of 28710ms total
   Dropped 531 nodes (cum <= 143.55ms)
   Showing top 10 nodes out of 190
         flat  flat%   sum%        cum   cum%
       5420ms 18.88% 18.88%     9070ms 31.59%  runtime.scanobject
       1880ms  6.55% 25.43%     1950ms  6.79%  runtime.findObject
       1290ms  4.49% 29.92%     1750ms  6.10%  app/protos/in/db.(*FriendLike).Clone
       1020ms  3.55% 33.47%     1020ms  3.55%  runtime.markBits.isMarked (inline)
        860ms  3.00% 36.47%     1270ms  4.42%  runtime.mapiternext
        720ms  2.51% 38.98%     2440ms  8.50%  runtime.mallocgc
        640ms  2.23% 41.21%      670ms  2.33%  syscall.Syscall
        620ms  2.16% 43.36%      620ms  2.16%  runtime.epollwait
        560ms  1.95% 45.32%      780ms  2.72%  app/logic/activity/friend.(*Friends).GetFriendNum
        520ms  1.81% 47.13%      530ms  1.85%  github.com/golang/snappy.encodeBlock
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-************** 
   File: service
   Type: inuse_space
   Time: Jun 10, 2021 at 11:33am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 136.62MB, 59.16% of 230.95MB total
   Dropped 123 nodes (cum <= 1.15MB)
   Showing top 10 nodes out of 153
         flat  flat%   sum%        cum   cum%
         26MB 11.26% 11.26%       26MB 11.26%  app/protos/in/db.(*FriendLike).Clone (inline)
      24.55MB 10.63% 21.89%    59.55MB 25.79%  app/protos/in/db.(*Friends).Clone
      18.52MB  8.02% 29.91%    18.52MB  8.02%  app/logic/activity/friend.(*Friends).sendLikeOne
      14.01MB  6.07% 35.98%    14.01MB  6.07%  app/logic/activity/friend.(*Friends).besentLike
      11.50MB  4.98% 40.96%    11.50MB  4.98%  app/logic/character.(*User).NewUserSnapshot
      11.03MB  4.77% 45.73%    11.03MB  4.77%  app/logic/activity/friend.(*Friends).init
       8.51MB  3.68% 49.41%     8.51MB  3.68%  app/logic/helper/monitor.(*Cmds).Add
          8MB  3.46% 52.88%        8MB  3.46%  app/logic/activity/friend.(*Friends).add
          8MB  3.46% 56.34%        8MB  3.46%  app/protos/in/db.(*Friend).Clone (inline)
       6.51MB  2.82% 59.16%     6.51MB  2.82%  app/logic/character.(*User).AddRecommendedFriend
   
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-************** 
   File: service
   Type: alloc_space
   Time: Jun 10, 2021 at 11:33am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 11248.26MB, 67.19% of 16740.11MB total
   Dropped 525 nodes (cum <= 83.70MB)
   Showing top 10 nodes out of 116
         flat  flat%   sum%        cum   cum%
    2141.42MB 12.79% 12.79%  2148.31MB 12.83%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
    1794.79MB 10.72% 23.51%  4179.90MB 24.97%  app/protos/in/db.(*Friends).Clone
    1426.07MB  8.52% 32.03%  1426.07MB  8.52%  app/protos/in/db.(*FriendLike).Clone (inline)
    1217.96MB  7.28% 39.31%  1217.96MB  7.28%  app/logic/db/redisop.CompressSnappyWithFlag
    1051.18MB  6.28% 45.59%  1051.18MB  6.28%  github.com/gogo/protobuf/proto.(*Buffer).grow (inline)
    1041.65MB  6.22% 51.81%  1041.65MB  6.22%  app/logic/character.(*User).NewUserSnapshot
     981.64MB  5.86% 57.67%  5518.59MB 32.97%  app/logic/db/redisop.DbClient.SetSomeFriends
     807.04MB  4.82% 62.50%   807.04MB  4.82%  app/protos/in/db.(*Friend).Clone (inline)
     403.50MB  2.41% 64.91%   411.50MB  2.46%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     383.02MB  2.29% 67.19%   383.02MB  2.29%  container/list.(*List).insertValue
   
   ```

   

#### 第2版

原来偶有5000人的基础上增加

left最大到7000

1. robot

   ```shell
   2021-06-10 12:02:39 压测数据分析
   压测基础情况
   开始时间：2021/06/10 11:52:40, 结束时间:2021/06/10 12:02:34, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:4921176， 平均每秒消息数:8284
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     72367  107809  67.13%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    633596  792044  80.00%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    94826  194603  48.73%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 959926  972133  98.74%
   协议:cmd:11503,MSG_C2L_FriendAdd                         549615  844763  65.06%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   583365  617350  94.50%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 82287  141898  57.99%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     264290  269402  98.10%
   协议:cmd:11520,MSG_C2L_FriendSearch                      239506  269670  88.81%
   协议:cmd:11508,MSG_C2L_FriendDelete                      391814  431747  90.75%
   协议:cmd:11501,MSG_C2L_FriendInfo                        274757  274757 100.00%
   协议正确数:4156349 协议总数:4926176 正确比:84.37%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         23.28%  32.54%  42.08%   2.10%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   84.05%   2.32%   3.80%   4.54%   4.65%   0.65%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.90%   1.60%   2.50%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   81.65%   3.85%   8.29%   5.33%   0.84%   0.05%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  83.41%   2.53%   3.87%   4.63%   4.89%   0.67%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                84.15%   4.65%   6.49%   3.09%   1.40%   0.22%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        83.23%   3.81%   7.29%   4.73%   0.86%   0.08%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  82.91%   2.47%   4.07%   4.88%   4.94%   0.73%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               82.48%   2.56%   4.25%   4.98%   5.03%   0.70%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    83.60%   2.44%   3.94%   4.59%   4.73%   0.70%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     83.35%   2.41%   3.95%   4.73%   4.82%   0.73%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     82.80%   2.47%   4.05%   4.94%   4.98%   0.75%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       83.77%   2.44%   3.85%   4.58%   4.66%   0.70%   0.00% 
   
   ---------------------
   
   ```

   

2. logic

   ```shell
   所有超时的left
   [2021/06/10 12:02:32.312595] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:32.294325284 +0800 CST msgs count per second: gate(pck/s: 9691 sum: 5197528  left:2128) logic db(pck/s: 53 sum: 36995 left:0) redis db(pck/s: 54 sum: 97557 left:13) 
   [2021/06/10 12:02:35.288499] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:35.28584049 +0800 CST msgs count per second: gate(pck/s: 17544 sum: 5231274  left:0) logic db(pck/s: 27 sum: 37133 left:0) redis db(pck/s: 972 sum: 98642 left:4112) 
   [2021/06/10 11:56:34.149227] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:56:34.101458509 +0800 CST msgs count per second: gate(pck/s: 5887 sum: 2008154  left:1450) logic db(pck/s: 36 sum: 20633 left:0) redis db(pck/s: 37 sum: 45872 left:0) 
   [2021/06/10 11:56:35.377226] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:56:35.377030164 +0800 CST msgs count per second: gate(pck/s: 17084 sum: 2025238  left:1470) logic db(pck/s: 59 sum: 20692 left:0) redis db(pck/s: 60 sum: 45932 left:21) 
   [2021/06/10 11:57:03.273970] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:57:03.200496047 +0800 CST msgs count per second: gate(pck/s: 5453 sum: 2266263  left:1140) logic db(pck/s: 52 sum: 21969 left:0) redis db(pck/s: 53 sum: 52237 left:0) 
   [2021/06/10 11:57:19.188446] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:57:19.085940934 +0800 CST msgs count per second: gate(pck/s: 5812 sum: 2408442  left:1109) logic db(pck/s: 46 sum: 22679 left:0) redis db(pck/s: 46 sum: 52962 left:0) 
   [2021/06/10 11:57:20.306827] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:57:20.300155685 +0800 CST msgs count per second: gate(pck/s: 9236 sum: 2417678  left:2178) logic db(pck/s: 58 sum: 22737 left:0) redis db(pck/s: 59 sum: 53021 left:0) 
   [2021/06/10 11:57:50.335307] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:57:50.285833469 +0800 CST msgs count per second: gate(pck/s: 10738 sum: 2684387  left:1752) logic db(pck/s: 73 sum: 24080 left:0) redis db(pck/s: 74 sum: 59392 left:0) 
   [2021/06/10 11:58:17.338240] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:58:17.286359065 +0800 CST msgs count per second: gate(pck/s: 10832 sum: 2926829  left:1814) logic db(pck/s: 63 sum: 25274 left:1) redis db(pck/s: 65 sum: 60612 left:0) 
   [2021/06/10 11:58:27.210613] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:58:27.188143425 +0800 CST msgs count per second: gate(pck/s: 3901 sum: 3012737  left:1502) logic db(pck/s: 21 sum: 25743 left:0) redis db(pck/s: 682 sum: 62487 left:0) 
   [2021/06/10 11:58:29.420805] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:58:29.285930284 +0800 CST msgs count per second: gate(pck/s: 11861 sum: 3033122  left:1995) logic db(pck/s: 67 sum: 25853 left:0) redis db(pck/s: 1040 sum: 64751 left:0) 
   [2021/06/10 11:59:00.324568] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:00.185867776 +0800 CST msgs count per second: gate(pck/s: 6350 sum: 3306737  left:2030) logic db(pck/s: 63 sum: 27201 left:0) redis db(pck/s: 64 sum: 67577 left:0) 
   [2021/06/10 11:59:10.210689] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:10.202450787 +0800 CST msgs count per second: gate(pck/s: 6168 sum: 3393535  left:1502) logic db(pck/s: 49 sum: 27645 left:0) redis db(pck/s: 50 sum: 68030 left:0) 
   [2021/06/10 11:59:11.299104] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:11.289745904 +0800 CST msgs count per second: gate(pck/s: 9752 sum: 3403287  left:1889) logic db(pck/s: 59 sum: 27704 left:0) redis db(pck/s: 60 sum: 68090 left:0) 
   [2021/06/10 11:59:19.281009] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:19.085833999 +0800 CST msgs count per second: gate(pck/s: 6499 sum: 3474153  left:2318) logic db(pck/s: 37 sum: 28058 left:0) redis db(pck/s: 1414 sum: 71881 left:0) 
   [2021/06/10 11:59:20.451050] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:20.286127736 +0800 CST msgs count per second: gate(pck/s: 11463 sum: 3485616  left:2687) logic db(pck/s: 62 sum: 28120 left:0) redis db(pck/s: 782 sum: 72663 left:0) 
   [2021/06/10 11:59:28.274429] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:28.085829646 +0800 CST msgs count per second: gate(pck/s: 5593 sum: 3554486  left:2211) logic db(pck/s: 38 sum: 28466 left:0) redis db(pck/s: 39 sum: 73868 left:0) 
   [2021/06/10 11:59:29.397151] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:29.286055862 +0800 CST msgs count per second: gate(pck/s: 11332 sum: 3565818  left:2211) logic db(pck/s: 44 sum: 28510 left:0) redis db(pck/s: 45 sum: 73913 left:0) 
   [2021/06/10 11:59:39.282749] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:39.205871275 +0800 CST msgs count per second: gate(pck/s: 4549 sum: 3653564  left:1887) logic db(pck/s: 53 sum: 28962 left:0) redis db(pck/s: 54 sum: 74374 left:0) 
   [2021/06/10 11:59:49.218804] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:49.08583168 +0800 CST msgs count per second: gate(pck/s: 6421 sum: 3741970  left:1637) logic db(pck/s: 61 sum: 29420 left:0) redis db(pck/s: 62 sum: 74841 left:0) 
   [2021/06/10 11:59:50.302559] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:50.285826284 +0800 CST msgs count per second: gate(pck/s: 9806 sum: 3751776  left:1956) logic db(pck/s: 47 sum: 29467 left:0) redis db(pck/s: 48 sum: 74889 left:0) 
   [2021/06/10 11:59:59.429098] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 11:59:59.285915944 +0800 CST msgs count per second: gate(pck/s: 11255 sum: 3834525  left:2559) logic db(pck/s: 57 sum: 29913 left:0) redis db(pck/s: 58 sum: 75343 left:0) 
   [2021/06/10 12:00:13.215633] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:00:13.085868911 +0800 CST msgs count per second: gate(pck/s: 5996 sum: 3953835  left:1581) logic db(pck/s: 43 sum: 30560 left:0) redis db(pck/s: 385 sum: 81002 left:0) 
   [2021/06/10 12:00:14.306527] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:00:14.285930481 +0800 CST msgs count per second: gate(pck/s: 10562 sum: 3964397  left:4254) logic db(pck/s: 56 sum: 30616 left:0) redis db(pck/s: 57 sum: 81059 left:0) 
   [2021/06/10 12:00:20.362695] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:00:20.285827631 +0800 CST msgs count per second: gate(pck/s: 10531 sum: 4020131  left:2422) logic db(pck/s: 47 sum: 30874 left:0) redis db(pck/s: 48 sum: 81322 left:0) 
   [2021/06/10 12:00:26.325739] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:00:26.285968433 +0800 CST msgs count per second: gate(pck/s: 7124 sum: 4074771  left:2499) logic db(pck/s: 49 sum: 31151 left:0) redis db(pck/s: 50 sum: 81604 left:0) 
   [2021/06/10 12:00:33.290424] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:00:33.185931745 +0800 CST msgs count per second: gate(pck/s: 4389 sum: 4133353  left:2113) logic db(pck/s: 49 sum: 31447 left:0) redis db(pck/s: 50 sum: 81906 left:0) 
   [2021/06/10 12:00:48.261095] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:00:48.196071371 +0800 CST msgs count per second: gate(pck/s: 3480 sum: 4270832  left:2048) logic db(pck/s: 16 sum: 32170 left:0) redis db(pck/s: 16 sum: 82642 left:0) 
   [2021/06/10 12:00:56.294993] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:00:56.286528868 +0800 CST msgs count per second: gate(pck/s: 8388 sum: 4343376  left:2567) logic db(pck/s: 42 sum: 32540 left:0) redis db(pck/s: 542 sum: 83583 left:0) 
   [2021/06/10 12:01:02.288428] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:02.285828456 +0800 CST msgs count per second: gate(pck/s: 8745 sum: 4394669  left:2149) logic db(pck/s: 50 sum: 32805 left:0) redis db(pck/s: 561 sum: 87948 left:3) 
   [2021/06/10 12:01:16.278942] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:16.08597104 +0800 CST msgs count per second: gate(pck/s: 5797 sum: 4521398  left:2272) logic db(pck/s: 45 sum: 33444 left:0) redis db(pck/s: 46 sum: 88940 left:0) 
   [2021/06/10 12:01:17.410112] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:17.285825743 +0800 CST msgs count per second: gate(pck/s: 11261 sum: 4532659  left:2357) logic db(pck/s: 68 sum: 33512 left:1) redis db(pck/s: 70 sum: 89010 left:0) 
   [2021/06/10 12:01:24.277315] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:24.185829673 +0800 CST msgs count per second: gate(pck/s: 4721 sum: 4589995  left:3927) logic db(pck/s: 54 sum: 33868 left:0) redis db(pck/s: 55 sum: 89371 left:0) 
   [2021/06/10 12:01:30.218213] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:30.218159624 +0800 CST msgs count per second: gate(pck/s: 9632 sum: 4646208  left:1060) logic db(pck/s: 40 sum: 34131 left:0) redis db(pck/s: 41 sum: 89639 left:9) 
   [2021/06/10 12:01:32.436234] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:32.286691387 +0800 CST msgs count per second: gate(pck/s: 11140 sum: 4664750  left:2645) logic db(pck/s: 66 sum: 34243 left:0) redis db(pck/s: 67 sum: 89753 left:0) 
   [2021/06/10 12:01:46.231576] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:46.08601099 +0800 CST msgs count per second: gate(pck/s: 6600 sum: 4790401  left:1713) logic db(pck/s: 45 sum: 34849 left:0) redis db(pck/s: 504 sum: 90895 left:0) 
   [2021/06/10 12:01:47.381133] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:47.285832199 +0800 CST msgs count per second: gate(pck/s: 10154 sum: 4800555  left:2633) logic db(pck/s: 53 sum: 34902 left:0) redis db(pck/s: 872 sum: 91767 left:0) 
   [2021/06/10 12:01:54.452226] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:01:54.194962858 +0800 CST msgs count per second: gate(pck/s: 6237 sum: 4858780  left:7515) logic db(pck/s: 41 sum: 35234 left:0) redis db(pck/s: 42 sum: 95764 left:0) 
   [2021/06/10 12:02:00.219710] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:00.185819224 +0800 CST msgs count per second: gate(pck/s: 4143 sum: 4912018  left:1650) logic db(pck/s: 47 sum: 35510 left:0) redis db(pck/s: 48 sum: 96045 left:0) 
   [2021/06/10 12:02:07.215574] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:07.085867306 +0800 CST msgs count per second: gate(pck/s: 6085 sum: 4976166  left:1582) logic db(pck/s: 45 sum: 35836 left:0) redis db(pck/s: 46 sum: 96377 left:0) 
   [2021/06/10 12:02:08.442388] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:08.422248339 +0800 CST msgs count per second: gate(pck/s: 10923 sum: 4987089  left:2988) logic db(pck/s: 59 sum: 35895 left:0) redis db(pck/s: 60 sum: 96437 left:9) 
   [2021/06/10 12:02:14.316403] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:14.285855439 +0800 CST msgs count per second: gate(pck/s: 9262 sum: 5036348  left:5353) logic db(pck/s: 52 sum: 36152 left:0) redis db(pck/s: 53 sum: 96699 left:0) 
   [2021/06/10 12:02:19.353753] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:19.092329528 +0800 CST msgs count per second: gate(pck/s: 6124 sum: 5081767  left:3455) logic db(pck/s: 46 sum: 36366 left:0) redis db(pck/s: 47 sum: 96917 left:0) 
   [2021/06/10 12:02:25.239258] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:25.085842769 +0800 CST msgs count per second: gate(pck/s: 9050 sum: 5137026  left:1824) logic db(pck/s: 48 sum: 36659 left:0) redis db(pck/s: 49 sum: 97215 left:0) 
   [2021/06/10 12:02:26.386023] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:02:26.286030685 +0800 CST msgs count per second: gate(pck/s: 10275 sum: 5147301  left:2691) logic db(pck/s: 49 sum: 36708 left:0) redis db(pck/s: 50 sum: 97265 left:0) 
   
   --------------------------
   
   logic服务器启动时间:1.212392193s
   logic账号总数:5000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       11522(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   3)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100007(   1)                                0.00%   0.00%   0.00%   0.00%   0.00%  100.00%   0.00% 
   协议:       11506(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11516(   7)                               57.14%  14.29%  28.57%   0.00%   0.00%   0.00%   0.00% 
   协议:       11508(   6)                               83.33%  16.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11518(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save( 551)                                1.63%   4.90%  17.24%  24.14%  41.02%  11.07%   0.00% 
   协议:       10100(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11510(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11501(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022( 550)                                0.00%   3.09%  15.82%  21.27%  33.09%  26.73%   0.00% 
   协议:       11512(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11503(   8)                               62.50%  25.00%  12.50%   0.00%   0.00%   0.00%   0.00% 
   
   ```

3. metric

   ![friend-********-11-02](./pic/friend-********-11-02.png)
   
4. profile

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********11
   profile-**************  profile-**************  
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-************** 
   File: service
   Type: cpu
   Time: Jun 10, 2021 at 11:57am (CST)
   Duration: 30.13s, Total samples = 26.35s (87.46%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 11420ms, 43.34% of 26350ms total
   Dropped 491 nodes (cum <= 131.75ms)
   Showing top 10 nodes out of 211
         flat  flat%   sum%        cum   cum%
       4510ms 17.12% 17.12%     7620ms 28.92%  runtime.scanobject
       1440ms  5.46% 22.58%     1550ms  5.88%  runtime.findObject
       1110ms  4.21% 26.79%     1540ms  5.84%  app/protos/in/db.(*FriendLike).Clone
        750ms  2.85% 29.64%      750ms  2.85%  runtime.markBits.isMarked (inline)
        700ms  2.66% 32.30%     2560ms  9.72%  runtime.mallocgc
        680ms  2.58% 34.88%     1030ms  3.91%  runtime.mapiternext
        600ms  2.28% 37.15%      600ms  2.28%  github.com/golang/snappy.encodeBlock
        590ms  2.24% 39.39%      590ms  2.24%  runtime.epollwait
        530ms  2.01% 41.40%      600ms  2.28%  syscall.Syscall
        510ms  1.94% 43.34%      680ms  2.58%  runtime.heapBitsSetType
        
        
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-************** 
   File: service
   Type: inuse_space
   Time: Jun 10, 2021 at 11:57am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 190.45MB, 62.32% of 305.58MB total
   Dropped 197 nodes (cum <= 1.53MB)
   Showing top 10 nodes out of 122
         flat  flat%   sum%        cum   cum%
      79.15MB 25.90% 25.90%    79.15MB 25.90%  app/protos/in/db.(*Friends).Unmarshal
      15.54MB  5.08% 30.99%    15.54MB  5.08%  app/logic/activity/friend.(*Friends).init
      14.52MB  4.75% 35.74%    14.52MB  4.75%  app/logic/activity/friend.(*Friends).besentLike
      14.15MB  4.63% 40.37%    14.15MB  4.63%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
      14.04MB  4.60% 44.96%    14.04MB  4.60%  app/logic/db/redisop.CompressSnappyWithFlag
      14.02MB  4.59% 49.55%    14.02MB  4.59%  app/logic/activity/friend.(*Friends).sendLikeOne
      12.02MB  3.93% 53.48%    28.02MB  9.17%  app/protos/in/db.(*Friends).Clone
         10MB  3.27% 56.76%       10MB  3.27%  app/protos/in/db.(*FriendLike).Clone (inline)
          9MB  2.95% 59.70%        9MB  2.95%  app/logic/activity/friend.(*Friends).add
       8.01MB  2.62% 62.32%     8.01MB  2.62%  app/logic/helper/monitor.(*Cmds).Add
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-************** 
   File: service
   Type: alloc_space
   Time: Jun 10, 2021 at 11:57am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 11252.44MB, 67.46% of 16679.31MB total
   Dropped 517 nodes (cum <= 83.40MB)
   Showing top 10 nodes out of 125
         flat  flat%   sum%        cum   cum%
    2178.26MB 13.06% 13.06%  2187.07MB 13.11%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
    1810.79MB 10.86% 23.92%  4170.90MB 25.01%  app/protos/in/db.(*Friends).Clone
    1406.56MB  8.43% 32.35%  1406.56MB  8.43%  app/protos/in/db.(*FriendLike).Clone (inline)
    1267.54MB  7.60% 39.95%  1267.54MB  7.60%  app/logic/db/redisop.CompressSnappyWithFlag
    1079.20MB  6.47% 46.42%  1079.20MB  6.47%  github.com/gogo/protobuf/proto.(*Buffer).grow (inline)
     986.64MB  5.92% 52.33%   986.64MB  5.92%  app/logic/character.(*User).NewUserSnapshot
     972.94MB  5.83% 58.17%  5620.05MB 33.69%  app/logic/db/redisop.DbClient.SetSomeFriends
     799.54MB  4.79% 62.96%   799.54MB  4.79%  app/protos/in/db.(*Friend).Clone (inline)
     383.44MB  2.30% 65.26%   388.94MB  2.33%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     367.53MB  2.20% 67.46%   588.04MB  3.53%  context.WithDeadline
   
   
   ```





#### 第三版

15000人

left最大也就4000多

1. robot

   ```shell
   2021-06-10 12:24:57 压测数据分析
   压测基础情况
   开始时间：2021/06/10 12:14:09, 结束时间:2021/06/10 12:24:04, 耗时:595(s)
   登录成功机器人总数：5000
   总消息数:4907827， 平均每秒消息数:8248
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     72495  107826  67.23%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    629088  785506  80.09%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    95922  194550  49.30%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 957915  967580  99.00%
   协议:cmd:11503,MSG_C2L_FriendAdd                         547573  841597  65.06%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   582615  617071  94.42%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 82827  142406  58.16%
   协议:cmd:11520,MSG_C2L_FriendSearch                      239766  269617  88.93%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     264079  270067  97.78%
   协议:cmd:11508,MSG_C2L_FriendDelete                      390794  431530  90.56%
   协议:cmd:11501,MSG_C2L_FriendInfo                        275077  275077 100.00%
   协议正确数:4148151 协议总数:4912827 正确比:84.44%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         26.40%  35.76%  36.24%   1.00%   0.60%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   84.72%   3.15%   4.32%   3.82%   3.57%   0.42%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.84%   1.52%   2.64%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   83.24%   4.77%   8.99%   2.57%   0.37%   0.06%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  84.68%   3.12%   4.22%   3.84%   3.71%   0.44%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                85.80%   5.35%   5.99%   1.56%   1.14%   0.16%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        84.62%   4.42%   7.98%   2.40%   0.52%   0.07%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  83.94%   3.21%   4.47%   4.01%   3.91%   0.47%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               83.24%   3.35%   4.58%   4.20%   4.12%   0.51%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    84.54%   3.09%   4.28%   3.85%   3.77%   0.46%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     84.46%   3.17%   4.21%   3.81%   3.89%   0.46%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     83.99%   3.16%   4.38%   4.04%   3.94%   0.49%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       84.83%   3.07%   4.18%   3.81%   3.67%   0.45%   0.00% 
   ```

   

2. logic

   ```shell
   --------------------------
   
   所有超时的left
   [2021/06/10 12:16:44.050618] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:16:44.031791153 +0800 CST msgs count per second: gate(pck/s: 5534 sum: 1309331  left:1220) logic db(pck/s: 35 sum: 16801 left:0) redis db(pck/s: 35 sum: 36962 left:0) 
   [2021/06/10 12:17:32.102716] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:17:32.031695168 +0800 CST msgs count per second: gate(pck/s: 5090 sum: 1727449  left:1359) logic db(pck/s: 43 sum: 19026 left:0) redis db(pck/s: 93 sum: 44234 left:0) 
   [2021/06/10 12:17:59.072618] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:17:59.031709053 +0800 CST msgs count per second: gate(pck/s: 4582 sum: 1965576  left:1030) logic db(pck/s: 17 sum: 20295 left:0) redis db(pck/s: 17 sum: 45529 left:0) 
   [2021/06/10 12:18:23.078568] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:18:23.031675618 +0800 CST msgs count per second: gate(pck/s: 4954 sum: 2175922  left:1126) logic db(pck/s: 48 sum: 21391 left:0) redis db(pck/s: 98 sum: 51648 left:0) 
   [2021/06/10 12:18:25.135220] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:18:25.131799993 +0800 CST msgs count per second: gate(pck/s: 8421 sum: 2198334  left:1653) logic db(pck/s: 39 sum: 21488 left:0) redis db(pck/s: 40 sum: 51748 left:0) 
   [2021/06/10 12:18:41.089969] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:18:41.036360418 +0800 CST msgs count per second: gate(pck/s: 5184 sum: 2335241  left:1398) logic db(pck/s: 41 sum: 22164 left:0) redis db(pck/s: 42 sum: 52439 left:0) 
   [2021/06/10 12:19:32.132059] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:19:32.031674308 +0800 CST msgs count per second: gate(pck/s: 5275 sum: 2786708  left:1707) logic db(pck/s: 30 sum: 24448 left:0) redis db(pck/s: 30 sum: 59771 left:0) 
   [2021/06/10 12:19:44.100961] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:19:44.031775101 +0800 CST msgs count per second: gate(pck/s: 4685 sum: 2892264  left:3112) logic db(pck/s: 53 sum: 24992 left:0) redis db(pck/s: 54 sum: 60326 left:0) 
   [2021/06/10 12:19:55.203633] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:19:55.131847955 +0800 CST msgs count per second: gate(pck/s: 8800 sum: 2994839  left:1323) logic db(pck/s: 54 sum: 25501 left:0) redis db(pck/s: 239 sum: 61029 left:0) 
   [2021/06/10 12:19:57.356877] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:19:57.248456303 +0800 CST msgs count per second: gate(pck/s: 11024 sum: 3014226  left:1042) logic db(pck/s: 59 sum: 25603 left:0) redis db(pck/s: 1252 sum: 62966 left:0) 
   [2021/06/10 12:20:10.161741] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:20:10.140405548 +0800 CST msgs count per second: gate(pck/s: 6789 sum: 3125349  left:1158) logic db(pck/s: 35 sum: 26178 left:0) redis db(pck/s: 35 sum: 66536 left:13) 
   [2021/06/10 12:20:24.336821] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:20:24.232678364 +0800 CST msgs count per second: gate(pck/s: 12953 sum: 3252006  left:1522) logic db(pck/s: 62 sum: 26835 left:0) redis db(pck/s: 63 sum: 67206 left:0) 
   [2021/06/10 12:20:34.200910] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:20:34.131681046 +0800 CST msgs count per second: gate(pck/s: 6879 sum: 3336338  left:2787) logic db(pck/s: 41 sum: 27286 left:0) redis db(pck/s: 42 sum: 67666 left:0) 
   [2021/06/10 12:20:44.049602] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:20:44.036262025 +0800 CST msgs count per second: gate(pck/s: 5189 sum: 3423225  left:1281) logic db(pck/s: 19 sum: 27720 left:0) redis db(pck/s: 19 sum: 68109 left:0) 
   [2021/06/10 12:20:55.199757] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:20:55.131674662 +0800 CST msgs count per second: gate(pck/s: 8382 sum: 3527433  left:1626) logic db(pck/s: 32 sum: 28236 left:0) redis db(pck/s: 32 sum: 73636 left:0) 
   [2021/06/10 12:20:57.356349] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:20:57.232508294 +0800 CST msgs count per second: gate(pck/s: 10915 sum: 3546549  left:1025) logic db(pck/s: 57 sum: 28339 left:0) redis db(pck/s: 58 sum: 73741 left:0) 
   [2021/06/10 12:21:05.091716] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:05.03206928 +0800 CST msgs count per second: gate(pck/s: 7544 sum: 3614678  left:1440) logic db(pck/s: 50 sum: 28662 left:0) redis db(pck/s: 51 sum: 74071 left:0) 
   [2021/06/10 12:21:06.255978] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:06.244337865 +0800 CST msgs count per second: gate(pck/s: 9482 sum: 3624160  left:1540) logic db(pck/s: 51 sum: 28713 left:0) redis db(pck/s: 52 sum: 74123 left:0) 
   [2021/06/10 12:21:14.151878] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:14.032119516 +0800 CST msgs count per second: gate(pck/s: 4768 sum: 3690816  left:4272) logic db(pck/s: 39 sum: 29063 left:0) redis db(pck/s: 40 sum: 74480 left:0) 
   [2021/06/10 12:21:22.197382] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:22.13191152 +0800 CST msgs count per second: gate(pck/s: 4842 sum: 3763643  left:2137) logic db(pck/s: 26 sum: 29422 left:0) redis db(pck/s: 26 sum: 74846 left:0) 
   [2021/06/10 12:21:30.267503] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:30.237061492 +0800 CST msgs count per second: gate(pck/s: 9349 sum: 3838349  left:1219) logic db(pck/s: 44 sum: 29773 left:0) redis db(pck/s: 45 sum: 75204 left:13) 
   [2021/06/10 12:21:37.211054] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:37.151439341 +0800 CST msgs count per second: gate(pck/s: 5459 sum: 3900401  left:1651) logic db(pck/s: 34 sum: 30119 left:0) redis db(pck/s: 514 sum: 76799 left:0) 
   [2021/06/10 12:21:44.045472] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:44.032161701 +0800 CST msgs count per second: gate(pck/s: 5147 sum: 3958194  left:1126) logic db(pck/s: 22 sum: 30428 left:0) redis db(pck/s: 22 sum: 80871 left:0) 
   [2021/06/10 12:21:53.090015] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:53.032020787 +0800 CST msgs count per second: gate(pck/s: 4935 sum: 4039501  left:1410) logic db(pck/s: 50 sum: 30824 left:0) redis db(pck/s: 51 sum: 81275 left:0) 
   [2021/06/10 12:21:54.301874] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:21:54.246357584 +0800 CST msgs count per second: gate(pck/s: 9434 sum: 4048935  left:4106) logic db(pck/s: 55 sum: 30879 left:0) redis db(pck/s: 56 sum: 81331 left:0) 
   [2021/06/10 12:22:03.360032] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:03.231987496 +0800 CST msgs count per second: gate(pck/s: 11180 sum: 4131576  left:1139) logic db(pck/s: 61 sum: 31288 left:0) redis db(pck/s: 62 sum: 81748 left:0) 
   [2021/06/10 12:22:09.232498] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:09.232066453 +0800 CST msgs count per second: gate(pck/s: 5663 sum: 4184325  left:1602) logic db(pck/s: 23 sum: 31560 left:0) redis db(pck/s: 23 sum: 82025 left:0) 
   [2021/06/10 12:22:16.184194] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:16.131736809 +0800 CST msgs count per second: gate(pck/s: 5019 sum: 4247890  left:2062) logic db(pck/s: 32 sum: 31879 left:0) redis db(pck/s: 32 sum: 82350 left:0) 
   [2021/06/10 12:22:25.209456] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:25.131753462 +0800 CST msgs count per second: gate(pck/s: 7725 sum: 4328785  left:2136) logic db(pck/s: 21 sum: 32266 left:0) redis db(pck/s: 143 sum: 82867 left:0) 
   [2021/06/10 12:22:31.161993] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:31.131695751 +0800 CST msgs count per second: gate(pck/s: 4797 sum: 4378642  left:2137) logic db(pck/s: 42 sum: 32514 left:0) redis db(pck/s: 495 sum: 87714 left:0) 
   [2021/06/10 12:22:37.133153] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:37.131707783 +0800 CST msgs count per second: gate(pck/s: 5028 sum: 4434531  left:1696) logic db(pck/s: 27 sum: 32795 left:0) redis db(pck/s: 27 sum: 88284 left:2) 
   [2021/06/10 12:22:55.150174] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:55.131779811 +0800 CST msgs count per second: gate(pck/s: 8904 sum: 4597789  left:1516) logic db(pck/s: 24 sum: 33634 left:0) redis db(pck/s: 24 sum: 89139 left:12) 
   [2021/06/10 12:22:57.361268] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:22:57.239801008 +0800 CST msgs count per second: gate(pck/s: 11356 sum: 4617157  left:1182) logic db(pck/s: 57 sum: 33737 left:0) redis db(pck/s: 58 sum: 89244 left:0) 
   [2021/06/10 12:23:04.210137] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:23:04.131740917 +0800 CST msgs count per second: gate(pck/s: 5891 sum: 4673563  left:3524) logic db(pck/s: 49 sum: 34002 left:0) redis db(pck/s: 50 sum: 89515 left:0) 
   [2021/06/10 12:23:11.135625] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:23:11.031938845 +0800 CST msgs count per second: gate(pck/s: 4877 sum: 4735773  left:1985) logic db(pck/s: 19 sum: 34319 left:0) redis db(pck/s: 19 sum: 89838 left:0) 
   [2021/06/10 12:23:25.142979] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:23:25.131663285 +0800 CST msgs count per second: gate(pck/s: 7794 sum: 4863057  left:2070) logic db(pck/s: 47 sum: 34993 left:0) redis db(pck/s: 48 sum: 95525 left:0) 
   [2021/06/10 12:23:27.413521] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:23:27.231692467 +0800 CST msgs count per second: gate(pck/s: 11018 sum: 4883466  left:1761) logic db(pck/s: 63 sum: 35111 left:0) redis db(pck/s: 64 sum: 95645 left:0) 
   [2021/06/10 12:23:37.200893] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:23:37.131888477 +0800 CST msgs count per second: gate(pck/s: 5076 sum: 4968704  left:2043) logic db(pck/s: 49 sum: 35552 left:0) redis db(pck/s: 50 sum: 96094 left:0) 
   [2021/06/10 12:23:43.339525] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:23:43.131869386 +0800 CST msgs count per second: gate(pck/s: 6342 sum: 5021243  left:1042) logic db(pck/s: 41 sum: 35831 left:0) redis db(pck/s: 41 sum: 96378 left:0) 
   [2021/06/10 12:23:56.087932] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:23:56.031987099 +0800 CST msgs count per second: gate(pck/s: 5003 sum: 5138669  left:1347) logic db(pck/s: 38 sum: 36414 left:0) redis db(pck/s: 38 sum: 96972 left:0) 
   [2021/06/10 12:24:04.197608] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-10 12:24:04.137191628 +0800 CST msgs count per second: gate(pck/s: 3427 sum: 5205979  left:4274) logic db(pck/s: 64 sum: 36787 left:0) redis db(pck/s: 65 sum: 97352 left:0) 
   
   --------------------------
   
   logic服务器启动时间:1.643971347s
   logic账号总数:10000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11522(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(   7)                               42.86%  42.86%  14.29%   0.00%   0.00%   0.00%   0.00% 
   协议:      100007(   1)                                0.00%   0.00%   0.00%   0.00%   0.00%   0.00%  100.00% 
   协议:       11506(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100018(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11516(   7)                               28.57%  71.43%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11518(   2)                               50.00%   0.00%  50.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save( 554)                                1.44%   4.69%  18.59%  26.71%  42.96%   5.60%   0.00% 
   协议:       11510(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11501(   4)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022( 553)                                0.00%   3.80%  15.73%  20.43%  35.26%  24.77%   0.00% 
   协议:       11503(   7)                               85.71%  14.29%   0.00%   0.00%   0.00%   0.00%   0.00% 
   
   ```

3. metric

   ![friend-********-11-03](./pic/friend-********-11-03.png)
   
4. profile

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-************** 
   File: service
   Type: cpu
   Time: Jun 10, 2021 at 12:19pm (CST)
   Duration: 30.24s, Total samples = 25.72s (85.05%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 11480ms, 44.63% of 25720ms total
   Dropped 475 nodes (cum <= 128.60ms)
   Showing top 10 nodes out of 219
         flat  flat%   sum%        cum   cum%
       4820ms 18.74% 18.74%     7390ms 28.73%  runtime.scanobject
       1410ms  5.48% 24.22%     1430ms  5.56%  runtime.findObject
       1020ms  3.97% 28.19%     1480ms  5.75%  app/protos/in/db.(*FriendLike).Clone
        730ms  2.84% 31.03%     2520ms  9.80%  runtime.mallocgc
        730ms  2.84% 33.86%     1110ms  4.32%  runtime.mapiternext
        690ms  2.68% 36.55%      690ms  2.68%  runtime.markBits.isMarked (inline)
        570ms  2.22% 38.76%      610ms  2.37%  syscall.Syscall
        540ms  2.10% 40.86%      540ms  2.10%  github.com/golang/snappy.encodeBlock
        520ms  2.02% 42.88%      670ms  2.60%  app/logic/activity/friend.(*Friends).GetFriendNum
        450ms  1.75% 44.63%      450ms  1.75%  runtime.epollwait
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-************** 
   File: service
   Type: inuse_space
   Time: Jun 10, 2021 at 12:19pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 262.80MB, 69.17% of 379.94MB total
   Dropped 134 nodes (cum <= 1.90MB)
   Showing top 10 nodes out of 150
         flat  flat%   sum%        cum   cum%
     161.81MB 42.59% 42.59%   161.81MB 42.59%  app/protos/in/db.(*Friends).Unmarshal
      15.02MB  3.95% 46.54%    15.02MB  3.95%  app/logic/activity/friend.(*Friends).besentLike
      14.03MB  3.69% 50.23%    30.03MB  7.90%  app/protos/in/db.(*Friends).Clone
      13.51MB  3.56% 53.79%    13.51MB  3.56%  app/logic/activity/friend.(*Friends).sendLikeOne
      11.53MB  3.03% 56.83%    11.53MB  3.03%  app/logic/activity/friend.(*Friends).init
      11.36MB  2.99% 59.81%    11.36MB  2.99%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
      11.04MB  2.90% 62.72%    11.04MB  2.90%  app/logic/db/redisop.CompressSnappyWithFlag
       9.50MB  2.50% 65.22%     9.50MB  2.50%  app/protos/in/db.(*FriendLike).Clone (inline)
       8.50MB  2.24% 67.46%     8.50MB  2.24%  app/logic/character.(*User).NewUserSnapshot
       6.50MB  1.71% 69.17%     6.50MB  1.71%  app/logic/activity/friend.(*Friends).add
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-************** 
   File: service
   Type: alloc_space
   Time: Jun 10, 2021 at 12:19pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 11102.75MB, 66.27% of 16752.57MB total
   Dropped 471 nodes (cum <= 83.76MB)
   Showing top 10 nodes out of 130
         flat  flat%   sum%        cum   cum%
    2145.78MB 12.81% 12.81%  2151.57MB 12.84%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
    1790.34MB 10.69% 23.50%  4120.95MB 24.60%  app/protos/in/db.(*Friends).Clone
    1403.56MB  8.38% 31.87%  1403.56MB  8.38%  app/protos/in/db.(*FriendLike).Clone (inline)
    1222.96MB  7.30% 39.17%  1222.96MB  7.30%  app/logic/db/redisop.CompressSnappyWithFlag
    1050.66MB  6.27% 45.45%  1050.66MB  6.27%  github.com/gogo/protobuf/proto.(*Buffer).grow (inline)
     994.28MB  5.94% 51.38%  5547.53MB 33.11%  app/logic/db/redisop.DbClient.SetSomeFriends
     978.14MB  5.84% 57.22%   978.14MB  5.84%  app/logic/character.(*User).NewUserSnapshot
     767.04MB  4.58% 61.80%   767.04MB  4.58%  app/protos/in/db.(*Friend).Clone (inline)
     390.95MB  2.33% 64.13%   396.95MB  2.37%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     359.03MB  2.14% 66.27%   569.54MB  3.40%  context.WithDeadline
   
   ```

   



#### 第四版

byte存储和加载， 5000人



1. robot

   ```shell
   2021-06-10 13:32:21 压测数据分析
   压测基础情况
   开始时间：2021/06/10 13:21:34, 结束时间:2021/06/10 13:31:28, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:4911798， 平均每秒消息数:8269
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     71879  107076  67.13%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    630533  789321  79.88%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    95092  195006  48.76%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 954336  968797  98.51%
   协议:cmd:11503,MSG_C2L_FriendAdd                         548024  842480  65.05%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   582342  617870  94.25%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 82456  141555  58.25%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     263761  269294  97.95%
   协议:cmd:11520,MSG_C2L_FriendSearch                      239631  269928  88.78%
   协议:cmd:11508,MSG_C2L_FriendDelete                      389988  430174  90.66%
   协议:cmd:11501,MSG_C2L_FriendInfo                        275297  275297 100.00%
   协议正确数:4143339 协议总数:4916798 正确比:84.27%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         24.50%  31.40%  40.34%   3.76%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   81.79%   2.99%   3.72%   4.72%   5.47%   1.31%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.38%   1.76%   2.86%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   79.50%   4.36%   7.24%   7.13%   1.67%   0.10%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  81.35%   2.86%   3.84%   5.00%   5.62%   1.34%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                81.80%   4.74%   7.58%   3.67%   1.77%   0.44%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        81.11%   4.12%   6.66%   6.29%   1.66%   0.16%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  80.44%   3.07%   3.88%   5.24%   5.94%   1.42%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               79.94%   3.09%   3.96%   5.39%   6.04%   1.58%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    81.32%   2.96%   3.73%   4.97%   5.66%   1.36%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     81.02%   2.93%   3.81%   5.02%   5.81%   1.41%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     80.56%   3.04%   3.92%   5.11%   5.93%   1.44%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       81.55%   2.96%   3.66%   4.88%   5.54%   1.41%   0.00% 
   
   ```

   

2. logic

   ```shell
   --------------------------
   
   所有超时的left
   [2021/06/10 13:31:24.250201] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:31:24.250123379 +0800 CST msgs count per second: gate(pck/s: 4032 sum: 5171539  left:2848) logic db(pck/s: 14 sum: 36878 left:0) redis db(pck/s: 14 sum: 97500 left:10) 
   [2021/06/10 13:31:29.266743] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:31:29.243833976 +0800 CST msgs count per second: gate(pck/s: 11966 sum: 5216764  left:611) logic db(pck/s: 34 sum: 37122 left:0) redis db(pck/s: 1062 sum: 98776 left:3361) 
   [2021/06/10 13:24:47.298399] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:24:47.240167658 +0800 CST msgs count per second: gate(pck/s: 11905 sum: 1647491  left:1391) logic db(pck/s: 48 sum: 18757 left:0) redis db(pck/s: 49 sum: 39021 left:0) 
   [2021/06/10 13:25:10.092494] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:25:10.040620432 +0800 CST msgs count per second: gate(pck/s: 7438 sum: 1846993  left:1026) logic db(pck/s: 25 sum: 19754 left:0) redis db(pck/s: 25 sum: 45040 left:0) 
   [2021/06/10 13:25:39.177123] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:25:39.147778643 +0800 CST msgs count per second: gate(pck/s: 7238 sum: 2101816  left:1318) logic db(pck/s: 39 sum: 21111 left:0) redis db(pck/s: 40 sum: 46426 left:0) 
   [2021/06/10 13:25:41.266441] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:25:41.2401296 +0800 CST msgs count per second: gate(pck/s: 10554 sum: 2122442  left:1784) logic db(pck/s: 46 sum: 21178 left:0) redis db(pck/s: 862 sum: 47792 left:0) 
   [2021/06/10 13:25:58.143276] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:25:58.040303552 +0800 CST msgs count per second: gate(pck/s: 5790 sum: 2265597  left:1405) logic db(pck/s: 26 sum: 21920 left:0) redis db(pck/s: 26 sum: 52253 left:0) 
   [2021/06/10 13:25:59.300496] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:25:59.240314145 +0800 CST msgs count per second: gate(pck/s: 13512 sum: 2279109  left:4210) logic db(pck/s: 75 sum: 21995 left:0) redis db(pck/s: 76 sum: 52329 left:0) 
   [2021/06/10 13:26:12.181591] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:26:12.140190129 +0800 CST msgs count per second: gate(pck/s: 4103 sum: 2393444  left:1282) logic db(pck/s: 20 sum: 22565 left:0) redis db(pck/s: 20 sum: 52911 left:0) 
   [2021/06/10 13:26:14.282946] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:26:14.240219491 +0800 CST msgs count per second: gate(pck/s: 10222 sum: 2411893  left:1853) logic db(pck/s: 55 sum: 22663 left:0) redis db(pck/s: 56 sum: 53011 left:0) 
   [2021/06/10 13:26:29.253725] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:26:29.253555116 +0800 CST msgs count per second: gate(pck/s: 13365 sum: 2545723  left:2571) logic db(pck/s: 43 sum: 23353 left:0) redis db(pck/s: 44 sum: 53715 left:4) 
   [2021/06/10 13:26:43.090472] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:26:43.05138342 +0800 CST msgs count per second: gate(pck/s: 4937 sum: 2666039  left:1111) logic db(pck/s: 22 sum: 23982 left:0) redis db(pck/s: 22 sum: 59357 left:0) 
   [2021/06/10 13:26:44.246577] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:26:44.243569433 +0800 CST msgs count per second: gate(pck/s: 10862 sum: 2676901  left:1793) logic db(pck/s: 64 sum: 24046 left:0) redis db(pck/s: 66 sum: 59423 left:0) 
   [2021/06/10 13:26:45.241476] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:26:45.140264529 +0800 CST msgs count per second: gate(pck/s: 8001 sum: 2684902  left:1700) logic db(pck/s: 35 sum: 24081 left:0) redis db(pck/s: 35 sum: 59458 left:0) 
   [2021/06/10 13:26:59.279545] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:26:59.240157477 +0800 CST msgs count per second: gate(pck/s: 12203 sum: 2808925  left:4549) logic db(pck/s: 64 sum: 24742 left:0) redis db(pck/s: 65 sum: 60132 left:0) 
   [2021/06/10 13:27:08.240450] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:08.240399666 +0800 CST msgs count per second: gate(pck/s: 8893 sum: 2887915  left:1526) logic db(pck/s: 59 sum: 25174 left:0) redis db(pck/s: 60 sum: 60573 left:0) 
   [2021/06/10 13:27:09.250355] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:09.140212613 +0800 CST msgs count per second: gate(pck/s: 9310 sum: 2897225  left:3979) logic db(pck/s: 32 sum: 25206 left:0) redis db(pck/s: 32 sum: 60605 left:0) 
   [2021/06/10 13:27:18.160155] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:18.140168521 +0800 CST msgs count per second: gate(pck/s: 3389 sum: 2974687  left:1186) logic db(pck/s: 17 sum: 25629 left:0) redis db(pck/s: 16 sum: 61036 left:10) 
   [2021/06/10 13:27:20.328095] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:20.240241515 +0800 CST msgs count per second: gate(pck/s: 14198 sum: 2998568  left:2421) logic db(pck/s: 55 sum: 25736 left:0) redis db(pck/s: 539 sum: 61628 left:0) 
   [2021/06/10 13:27:29.266131] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:29.240117231 +0800 CST msgs count per second: gate(pck/s: 11759 sum: 3077413  left:2453) logic db(pck/s: 66 sum: 26195 left:0) redis db(pck/s: 67 sum: 66613 left:0) 
   [2021/06/10 13:27:30.237052] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:30.141956107 +0800 CST msgs count per second: gate(pck/s: 8422 sum: 3085835  left:1905) logic db(pck/s: 28 sum: 26223 left:0) redis db(pck/s: 28 sum: 66641 left:0) 
   [2021/06/10 13:27:41.255147] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:41.240189944 +0800 CST msgs count per second: gate(pck/s: 11185 sum: 3184771  left:1432) logic db(pck/s: 76 sum: 26761 left:0) redis db(pck/s: 78 sum: 67190 left:0) 
   [2021/06/10 13:27:42.239645] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:42.14023637 +0800 CST msgs count per second: gate(pck/s: 6924 sum: 3191695  left:1989) logic db(pck/s: 35 sum: 26796 left:0) redis db(pck/s: 35 sum: 67225 left:0) 
   [2021/06/10 13:27:52.101954] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:52.040254029 +0800 CST msgs count per second: gate(pck/s: 5303 sum: 3278343  left:1295) logic db(pck/s: 23 sum: 27238 left:0) redis db(pck/s: 23 sum: 67676 left:0) 
   [2021/06/10 13:27:53.252110] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:27:53.24017486 +0800 CST msgs count per second: gate(pck/s: 9361 sum: 3287704  left:2614) logic db(pck/s: 51 sum: 27289 left:0) redis db(pck/s: 52 sum: 67728 left:3) 
   [2021/06/10 13:28:02.242103] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:02.24014292 +0800 CST msgs count per second: gate(pck/s: 11190 sum: 3369329  left:1828) logic db(pck/s: 61 sum: 27712 left:0) redis db(pck/s: 63 sum: 68160 left:0) 
   [2021/06/10 13:28:03.267106] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:03.140127205 +0800 CST msgs count per second: gate(pck/s: 8166 sum: 3377495  left:2317) logic db(pck/s: 28 sum: 27740 left:0) redis db(pck/s: 28 sum: 68188 left:0) 
   [2021/06/10 13:28:12.199051] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:12.140224886 +0800 CST msgs count per second: gate(pck/s: 3350 sum: 3457082  left:1834) logic db(pck/s: 19 sum: 28161 left:0) redis db(pck/s: 696 sum: 70387 left:0) 
   [2021/06/10 13:28:21.155907] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:21.14020048 +0800 CST msgs count per second: gate(pck/s: 2497 sum: 3536662  left:1857) logic db(pck/s: 14 sum: 28541 left:0) redis db(pck/s: 14 sum: 74006 left:0) 
   [2021/06/10 13:28:23.401131] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:23.25168262 +0800 CST msgs count per second: gate(pck/s: 12148 sum: 3558306  left:1699) logic db(pck/s: 77 sum: 28675 left:0) redis db(pck/s: 78 sum: 74142 left:0) 
   [2021/06/10 13:28:32.409264] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:32.240133751 +0800 CST msgs count per second: gate(pck/s: 12152 sum: 3638486  left:1963) logic db(pck/s: 55 sum: 29075 left:0) redis db(pck/s: 56 sum: 74550 left:0) 
   [2021/06/10 13:28:39.261021] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:39.140126723 +0800 CST msgs count per second: gate(pck/s: 6528 sum: 3696671  left:5313) logic db(pck/s: 26 sum: 29385 left:0) redis db(pck/s: 25 sum: 74866 left:0) 
   [2021/06/10 13:28:47.254096] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:47.240187873 +0800 CST msgs count per second: gate(pck/s: 10963 sum: 3770347  left:1107) logic db(pck/s: 67 sum: 29759 left:0) redis db(pck/s: 69 sum: 75248 left:0) 
   [2021/06/10 13:28:48.287765] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:48.14014634 +0800 CST msgs count per second: gate(pck/s: 6177 sum: 3776524  left:2894) logic db(pck/s: 36 sum: 29795 left:0) redis db(pck/s: 36 sum: 75284 left:0) 
   [2021/06/10 13:28:55.163620] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:55.040215693 +0800 CST msgs count per second: gate(pck/s: 5070 sum: 3838004  left:1968) logic db(pck/s: 23 sum: 30096 left:0) redis db(pck/s: 23 sum: 75591 left:0) 
   [2021/06/10 13:28:56.332252] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:28:56.240647676 +0800 CST msgs count per second: gate(pck/s: 11006 sum: 3849010  left:3040) logic db(pck/s: 53 sum: 30149 left:0) redis db(pck/s: 54 sum: 75645 left:0) 
   [2021/06/10 13:29:03.181251] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:03.140139035 +0800 CST msgs count per second: gate(pck/s: 2686 sum: 3910511  left:2108) logic db(pck/s: 13 sum: 30457 left:0) redis db(pck/s: 754 sum: 78470 left:0) 
   [2021/06/10 13:29:10.124829] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:10.040215328 +0800 CST msgs count per second: gate(pck/s: 7709 sum: 3974351  left:1634) logic db(pck/s: 27 sum: 30785 left:0) redis db(pck/s: 27 sum: 81293 left:0) 
   [2021/06/10 13:29:11.391437] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:11.240165497 +0800 CST msgs count per second: gate(pck/s: 12440 sum: 3986791  left:2368) logic db(pck/s: 73 sum: 30858 left:0) redis db(pck/s: 74 sum: 81367 left:0) 
   [2021/06/10 13:29:18.262180] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:18.140191961 +0800 CST msgs count per second: gate(pck/s: 4653 sum: 4043668  left:2756) logic db(pck/s: 21 sum: 31197 left:0) redis db(pck/s: 21 sum: 81712 left:0) 
   [2021/06/10 13:29:25.139383] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:25.040197009 +0800 CST msgs count per second: gate(pck/s: 5526 sum: 4105900  left:1705) logic db(pck/s: 26 sum: 31525 left:0) redis db(pck/s: 26 sum: 82046 left:0) 
   [2021/06/10 13:29:26.290819] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:26.240134279 +0800 CST msgs count per second: gate(pck/s: 10550 sum: 4116450  left:2833) logic db(pck/s: 59 sum: 31584 left:0) redis db(pck/s: 60 sum: 82106 left:0) 
   [2021/06/10 13:29:33.164603] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:33.140182191 +0800 CST msgs count per second: gate(pck/s: 2392 sum: 4178158  left:1983) logic db(pck/s: 9 sum: 31893 left:0) redis db(pck/s: 9 sum: 82421 left:0) 
   [2021/06/10 13:29:40.109413] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:40.040209548 +0800 CST msgs count per second: gate(pck/s: 8250 sum: 4242778  left:1450) logic db(pck/s: 27 sum: 32194 left:0) redis db(pck/s: 27 sum: 82728 left:0) 
   [2021/06/10 13:29:41.388667] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:41.240221931 +0800 CST msgs count per second: gate(pck/s: 11186 sum: 4253964  left:2937) logic db(pck/s: 52 sum: 32246 left:0) redis db(pck/s: 53 sum: 82781 left:0) 
   [2021/06/10 13:29:47.273659] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:47.240138335 +0800 CST msgs count per second: gate(pck/s: 10946 sum: 4305380  left:1966) logic db(pck/s: 63 sum: 32506 left:0) redis db(pck/s: 65 sum: 83047 left:0) 
   [2021/06/10 13:29:48.349720] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:48.140154759 +0800 CST msgs count per second: gate(pck/s: 8054 sum: 4313434  left:2884) logic db(pck/s: 46 sum: 32552 left:0) redis db(pck/s: 46 sum: 83093 left:0) 
   [2021/06/10 13:29:54.211604] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:29:54.140199747 +0800 CST msgs count per second: gate(pck/s: 3050 sum: 4366049  left:2529) logic db(pck/s: 15 sum: 32806 left:0) redis db(pck/s: 490 sum: 86338 left:0) 
   [2021/06/10 13:30:01.189990] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:01.047897584 +0800 CST msgs count per second: gate(pck/s: 5206 sum: 4430176  left:2207) logic db(pck/s: 19 sum: 33115 left:0) redis db(pck/s: 19 sum: 88667 left:0) 
   [2021/06/10 13:30:06.278120] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:06.140198864 +0800 CST msgs count per second: gate(pck/s: 5275 sum: 4473497  left:3168) logic db(pck/s: 29 sum: 33360 left:0) redis db(pck/s: 28 sum: 88916 left:0) 
   [2021/06/10 13:30:10.083773] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:10.040128281 +0800 CST msgs count per second: gate(pck/s: 7885 sum: 4511000  left:1015) logic db(pck/s: 27 sum: 33531 left:0) redis db(pck/s: 27 sum: 89090 left:0) 
   [2021/06/10 13:30:11.271757] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:11.240246651 +0800 CST msgs count per second: gate(pck/s: 10267 sum: 4521267  left:2586) logic db(pck/s: 44 sum: 33575 left:0) redis db(pck/s: 45 sum: 89135 left:23) 
   [2021/06/10 13:30:17.247139] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:17.241405105 +0800 CST msgs count per second: gate(pck/s: 9338 sum: 4572528  left:2240) logic db(pck/s: 56 sum: 33870 left:0) redis db(pck/s: 57 sum: 89435 left:15) 
   [2021/06/10 13:30:18.344413] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:18.140423183 +0800 CST msgs count per second: gate(pck/s: 9374 sum: 4581902  left:3069) logic db(pck/s: 53 sum: 33923 left:0) redis db(pck/s: 54 sum: 89489 left:0) 
   [2021/06/10 13:30:24.147531] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:24.140127159 +0800 CST msgs count per second: gate(pck/s: 2567 sum: 4634873  left:1793) logic db(pck/s: 12 sum: 34172 left:0) redis db(pck/s: 12 sum: 89743 left:0) 
   [2021/06/10 13:30:31.150751] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:31.040346495 +0800 CST msgs count per second: gate(pck/s: 5299 sum: 4699833  left:1818) logic db(pck/s: 35 sum: 34482 left:0) redis db(pck/s: 35 sum: 90059 left:0) 
   [2021/06/10 13:30:32.348085] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:32.257492572 +0800 CST msgs count per second: gate(pck/s: 10843 sum: 4710676  left:3364) logic db(pck/s: 58 sum: 34540 left:0) redis db(pck/s: 59 sum: 90118 left:0) 
   [2021/06/10 13:30:38.265195] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:38.240206029 +0800 CST msgs count per second: gate(pck/s: 10451 sum: 4760768  left:2763) logic db(pck/s: 58 sum: 34833 left:0) redis db(pck/s: 59 sum: 90416 left:3) 
   [2021/06/10 13:30:44.279666] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:44.258008761 +0800 CST msgs count per second: gate(pck/s: 9931 sum: 4816483  left:3190) logic db(pck/s: 54 sum: 35102 left:0) redis db(pck/s: 647 sum: 93677 left:4) 
   [2021/06/10 13:30:50.262715] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:50.240456836 +0800 CST msgs count per second: gate(pck/s: 14622 sum: 4873511  left:1667) logic db(pck/s: 73 sum: 35400 left:0) redis db(pck/s: 75 sum: 95995 left:0) 
   [2021/06/10 13:30:51.362885] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:51.140232357 +0800 CST msgs count per second: gate(pck/s: 6883 sum: 4880394  left:3350) logic db(pck/s: 34 sum: 35434 left:0) redis db(pck/s: 34 sum: 96029 left:0) 
   [2021/06/10 13:30:57.275737] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:30:57.140226006 +0800 CST msgs count per second: gate(pck/s: 3033 sum: 4929606  left:3235) logic db(pck/s: 14 sum: 35666 left:0) redis db(pck/s: 14 sum: 96266 left:0) 
   [2021/06/10 13:31:02.333169] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:31:02.253750057 +0800 CST msgs count per second: gate(pck/s: 9919 sum: 4976806  left:3996) logic db(pck/s: 47 sum: 35871 left:0) redis db(pck/s: 48 sum: 96475 left:0) 
   [2021/06/10 13:31:07.287685] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:31:07.040270484 +0800 CST msgs count per second: gate(pck/s: 5314 sum: 5017920  left:3957) logic db(pck/s: 26 sum: 36079 left:1) redis db(pck/s: 26 sum: 96688 left:0) 
   [2021/06/10 13:31:12.211975] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:31:12.140131369 +0800 CST msgs count per second: gate(pck/s: 2956 sum: 5065144  left:2565) logic db(pck/s: 20 sum: 36311 left:0) redis db(pck/s: 20 sum: 96923 left:0) 
   [2021/06/10 13:31:18.210905] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:31:18.14022067 +0800 CST msgs count per second: gate(pck/s: 2350 sum: 5115525  left:2538) logic db(pck/s: 12 sum: 36599 left:0) redis db(pck/s: 12 sum: 97216 left:0) 
   
   --------------------------
   
   logic服务器启动时间:924.062805ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11522(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       10104(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11506(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11516(   7)                               42.86%  57.14%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11508(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11518(   3)                               66.67%   0.00%  33.33%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save( 548)                                1.82%   5.47%  16.79%  20.07%  36.50%  19.34%   0.00% 
   协议:       11510(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11501(   5)                               20.00%  80.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11520(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022( 539)                                0.00%  12.62%  38.22%  38.78%   9.83%   0.56%   0.00% 
   协议:       11006(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11503(  16)                               43.75%  50.00%   6.25%   0.00%   0.00%   0.00%   0.00% 
   
   ```

3. metric

   ![friend-********-13-01](./pic/friend-********-13-01.png)
   
4. profile

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********132634 
   File: service
   Type: cpu
   Time: Jun 10, 2021 at 1:26pm (CST)
   Duration: 30.10s, Total samples = 22.92s (76.14%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 9620ms, 41.97% of 22920ms total
   Dropped 447 nodes (cum <= 114.60ms)
   Showing top 10 nodes out of 224
         flat  flat%   sum%        cum   cum%
       3420ms 14.92% 14.92%     5910ms 25.79%  runtime.scanobject
       1110ms  4.84% 19.76%     1210ms  5.28%  runtime.findObject
       1100ms  4.80% 24.56%     1130ms  4.93%  app/protos/in/db.(*FriendLike).Size
        770ms  3.36% 27.92%      770ms  3.36%  runtime.markBits.isMarked (inline)
        720ms  3.14% 31.06%     1120ms  4.89%  runtime.mapiternext
        620ms  2.71% 33.77%      830ms  3.62%  app/logic/activity/friend.(*Friends).GetFriendNum
        520ms  2.27% 36.04%      520ms  2.27%  runtime.epollwait
        480ms  2.09% 38.13%      480ms  2.09%  github.com/golang/snappy.encodeBlock
        460ms  2.01% 40.14%     2000ms  8.73%  runtime.mallocgc
        420ms  1.83% 41.97%      440ms  1.92%  syscall.Syscall
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-********132634 
   File: service
   Type: inuse_space
   Time: Jun 10, 2021 at 1:26pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 88.09MB, 51.70% of 170.37MB total
   Dropped 80 nodes (cum <= 0.85MB)
   Showing top 10 nodes out of 157
         flat  flat%   sum%        cum   cum%
      14.51MB  8.52%  8.52%    14.51MB  8.52%  app/logic/activity/friend.(*Friends).sendLikeOne
         12MB  7.04% 15.56%       12MB  7.04%  app/logic/character.(*User).NewUserSnapshot
      11.53MB  6.77% 22.33%    11.53MB  6.77%  github.com/gogo/protobuf/proto.(*Buffer).grow
      11.51MB  6.75% 29.09%    11.51MB  6.75%  app/logic/activity/friend.(*Friends).besentLike
      10.01MB  5.87% 34.96%    10.01MB  5.87%  app/logic/helper/monitor.(*Cmds).Add
       9.02MB  5.30% 40.26%     9.02MB  5.30%  app/logic/activity/friend.(*Friends).init
       5.50MB  3.23% 43.48%     5.50MB  3.23%  app/logic/character.newShop
          5MB  2.94% 46.42%        5MB  2.94%  app/logic/character.(*User).AddRecommendedFriend
          5MB  2.93% 49.36%        5MB  2.93%  app/logic/activity/friend.(*Friends).add
          4MB  2.35% 51.70%        4MB  2.35%  github.com/google/btree.NewFreeList
   
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-********132634 
   File: service
   Type: alloc_space
   Time: Jun 10, 2021 at 1:26pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 8098.36MB, 65.76% of 12315.90MB total
   Dropped 502 nodes (cum <= 61.58MB)
   Showing top 10 nodes out of 126
         flat  flat%   sum%        cum   cum%
    2111.62MB 17.15% 17.15%  2120.71MB 17.22%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
    1190.38MB  9.67% 26.81%  1190.38MB  9.67%  app/logic/db/redisop.CompressSnappyWithFlag
    1004.06MB  8.15% 34.96%  1004.06MB  8.15%  github.com/gogo/protobuf/proto.(*Buffer).grow (inline)
     981.14MB  7.97% 42.93%   981.14MB  7.97%  app/logic/character.(*User).NewUserSnapshot
     955.58MB  7.76% 50.69%  1960.14MB 15.92%  app/logic/activity/friend.(*Manager).Save
     456.58MB  3.71% 54.40%   456.58MB  3.71%  strings.(*Builder).WriteString
     384.94MB  3.13% 57.52%   389.44MB  3.16%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     363.53MB  2.95% 60.47%   579.54MB  4.71%  context.WithDeadline
     363.52MB  2.95% 63.42%   363.52MB  2.95%  container/list.(*List).insertValue
     287.02MB  2.33% 65.76%   287.02MB  2.33%  app/logic/session.(*Client).Process
   
   ```









#### 第5版

byte存储和加载，10000人

left最大：5617

1. robot

   ```
   2021-06-10 14:09:01 压测数据分析
   压测基础情况
   开始时间：2021/06/10 13:43:24, 结束时间:2021/06/10 13:53:18, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:4907015， 平均每秒消息数:8260
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     72237  107685  67.08%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    626860  783614  80.00%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    95352  195347  48.81%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 954905  967239  98.72%
   协议:cmd:11503,MSG_C2L_FriendAdd                         548695  843051  65.08%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   583251  617518  94.45%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 82578  141073  58.54%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     263095  269508  97.62%
   协议:cmd:11520,MSG_C2L_FriendSearch                      239816  269868  88.86%
   协议:cmd:11508,MSG_C2L_FriendDelete                      390671  431459  90.55%
   协议:cmd:11501,MSG_C2L_FriendInfo                        275653  275653 100.00%
   协议正确数:4143113 协议总数:4912015 正确比:84.35%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         21.78%  30.66%  45.40%   2.16%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   83.06%   2.78%   3.72%   4.52%   4.95%   0.97%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.40%   1.90%   2.70%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   80.53%   4.30%   8.31%   5.94%   0.84%   0.08%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  82.73%   2.79%   3.66%   4.70%   5.07%   1.06%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                82.74%   5.45%   6.90%   2.97%   1.61%   0.33%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        81.93%   4.30%   7.52%   5.12%   1.01%   0.12%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  81.91%   2.91%   3.84%   4.87%   5.38%   1.10%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               81.31%   2.96%   3.97%   5.08%   5.54%   1.14%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    82.83%   2.78%   3.61%   4.68%   5.05%   1.04%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     82.49%   2.76%   3.73%   4.74%   5.22%   1.07%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     81.93%   2.91%   3.81%   4.92%   5.35%   1.09%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       82.96%   2.81%   3.56%   4.61%   5.04%   1.03%   0.00% 
   
   ---------------------
   
   ```

   

2. logic

   ```shell
   
   所有超时的left
   [2021/06/10 13:47:00.209373] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:00.161018931 +0800 CST msgs count per second: gate(pck/s: 6166 sum: 1852314  left:1147) logic db(pck/s: 24 sum: 19814 left:0) redis db(pck/s: 24 sum: 45035 left:0) 
   [2021/06/10 13:47:02.289399] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:02.273867315 +0800 CST msgs count per second: gate(pck/s: 10925 sum: 1870523  left:1002) logic db(pck/s: 55 sum: 19913 left:0) redis db(pck/s: 56 sum: 45136 left:0) 
   [2021/06/10 13:47:21.170170] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:21.16098587 +0800 CST msgs count per second: gate(pck/s: 4661 sum: 2037159  left:1055) logic db(pck/s: 29 sum: 20787 left:0) redis db(pck/s: 30 sum: 46029 left:0) 
   [2021/06/10 13:47:22.146033] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:22.061170689 +0800 CST msgs count per second: gate(pck/s: 6966 sum: 2044125  left:1562) logic db(pck/s: 40 sum: 20827 left:0) redis db(pck/s: 40 sum: 46069 left:0) 
   [2021/06/10 13:47:39.179636] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:39.162644102 +0800 CST msgs count per second: gate(pck/s: 4654 sum: 2190886  left:4061) logic db(pck/s: 16 sum: 21538 left:0) redis db(pck/s: 16 sum: 51796 left:0) 
   [2021/06/10 13:47:41.267135] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:41.261887779 +0800 CST msgs count per second: gate(pck/s: 9956 sum: 2213672  left:1362) logic db(pck/s: 62 sum: 21647 left:0) redis db(pck/s: 63 sum: 51908 left:0) 
   [2021/06/10 13:47:57.164490] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:57.160977787 +0800 CST msgs count per second: gate(pck/s: 3702 sum: 2349117  left:1476) logic db(pck/s: 14 sum: 22361 left:0) redis db(pck/s: 14 sum: 52637 left:3) 
   [2021/06/10 13:47:59.322140] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:47:59.261498649 +0800 CST msgs count per second: gate(pck/s: 14767 sum: 2373190  left:1629) logic db(pck/s: 79 sum: 22497 left:0) redis db(pck/s: 80 sum: 52775 left:0) 
   [2021/06/10 13:48:15.172030] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:15.160942146 +0800 CST msgs count per second: gate(pck/s: 4150 sum: 2509859  left:1282) logic db(pck/s: 28 sum: 23225 left:0) redis db(pck/s: 29 sum: 53519 left:0) 
   [2021/06/10 13:48:16.156493] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:16.060953829 +0800 CST msgs count per second: gate(pck/s: 7154 sum: 2517013  left:1635) logic db(pck/s: 34 sum: 23259 left:0) redis db(pck/s: 34 sum: 53553 left:0) 
   [2021/06/10 13:48:17.279991] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:17.261010504 +0800 CST msgs count per second: gate(pck/s: 10530 sum: 2527543  left:1890) logic db(pck/s: 59 sum: 23318 left:0) redis db(pck/s: 60 sum: 53613 left:8) 
   [2021/06/10 13:48:31.106458] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:31.061115444 +0800 CST msgs count per second: gate(pck/s: 4474 sum: 2650842  left:1620) logic db(pck/s: 23 sum: 23933 left:0) redis db(pck/s: 23 sum: 59241 left:0) 
   [2021/06/10 13:48:33.250156] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:33.161074006 +0800 CST msgs count per second: gate(pck/s: 5956 sum: 2670681  left:1768) logic db(pck/s: 28 sum: 24044 left:0) redis db(pck/s: 28 sum: 59354 left:0) 
   [2021/06/10 13:48:45.180083] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:45.161043515 +0800 CST msgs count per second: gate(pck/s: 4316 sum: 2774687  left:1487) logic db(pck/s: 26 sum: 24621 left:0) redis db(pck/s: 27 sum: 59943 left:0) 
   [2021/06/10 13:48:46.223790] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:46.060985424 +0800 CST msgs count per second: gate(pck/s: 7760 sum: 2782447  left:2546) logic db(pck/s: 31 sum: 24652 left:0) redis db(pck/s: 31 sum: 59974 left:0) 
   [2021/06/10 13:48:47.317544] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:47.261580171 +0800 CST msgs count per second: gate(pck/s: 10951 sum: 2793398  left:1366) logic db(pck/s: 47 sum: 24699 left:0) redis db(pck/s: 48 sum: 60022 left:0) 
   [2021/06/10 13:48:57.177288] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:57.161374329 +0800 CST msgs count per second: gate(pck/s: 4290 sum: 2880474  left:1388) logic db(pck/s: 28 sum: 25149 left:0) redis db(pck/s: 29 sum: 60482 left:0) 
   [2021/06/10 13:48:59.293213] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:48:59.261754863 +0800 CST msgs count per second: gate(pck/s: 13873 sum: 2902587  left:2807) logic db(pck/s: 37 sum: 25214 left:0) redis db(pck/s: 38 sum: 60548 left:0) 
   [2021/06/10 13:49:09.187839] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:09.161085319 +0800 CST msgs count per second: gate(pck/s: 6036 sum: 2987153  left:3761) logic db(pck/s: 24 sum: 25665 left:0) redis db(pck/s: 24 sum: 61008 left:0) 
   [2021/06/10 13:49:18.164054] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:18.161287977 +0800 CST msgs count per second: gate(pck/s: 2714 sum: 3065158  left:2012) logic db(pck/s: 16 sum: 26076 left:0) redis db(pck/s: 81 sum: 66427 left:2) 
   [2021/06/10 13:49:30.192032] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:30.166352095 +0800 CST msgs count per second: gate(pck/s: 4348 sum: 3176697  left:1504) logic db(pck/s: 8 sum: 26639 left:0) redis db(pck/s: 8 sum: 67001 left:16) 
   [2021/06/10 13:49:40.131085] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:40.060929893 +0800 CST msgs count per second: gate(pck/s: 6125 sum: 3263025  left:1905) logic db(pck/s: 24 sum: 27071 left:0) redis db(pck/s: 24 sum: 67442 left:0) 
   [2021/06/10 13:49:41.306107] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:41.261578111 +0800 CST msgs count per second: gate(pck/s: 11520 sum: 3274545  left:1686) logic db(pck/s: 74 sum: 27145 left:0) redis db(pck/s: 75 sum: 67517 left:0) 
   [2021/06/10 13:49:51.178975] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:51.160917434 +0800 CST msgs count per second: gate(pck/s: 4624 sum: 3362687  left:1284) logic db(pck/s: 26 sum: 27588 left:0) redis db(pck/s: 27 sum: 67970 left:0) 
   [2021/06/10 13:49:52.207139] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:52.060929493 +0800 CST msgs count per second: gate(pck/s: 7388 sum: 3370075  left:2265) logic db(pck/s: 34 sum: 27622 left:0) redis db(pck/s: 34 sum: 68004 left:0) 
   [2021/06/10 13:49:53.326080] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:49:53.279970395 +0800 CST msgs count per second: gate(pck/s: 11339 sum: 3381414  left:1404) logic db(pck/s: 58 sum: 27680 left:0) redis db(pck/s: 59 sum: 68063 left:0) 
   [2021/06/10 13:50:00.195071] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:00.161056586 +0800 CST msgs count per second: gate(pck/s: 2702 sum: 3441410  left:2364) logic db(pck/s: 17 sum: 27948 left:0) redis db(pck/s: 393 sum: 68713 left:0) 
   [2021/06/10 13:50:09.198737] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:09.161165575 +0800 CST msgs count per second: gate(pck/s: 4732 sum: 3519211  left:4358) logic db(pck/s: 15 sum: 28358 left:0) redis db(pck/s: 15 sum: 73756 left:0) 
   [2021/06/10 13:50:17.285654] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:17.260919155 +0800 CST msgs count per second: gate(pck/s: 10073 sum: 3590949  left:1761) logic db(pck/s: 49 sum: 28733 left:0) redis db(pck/s: 50 sum: 74138 left:16) 
   [2021/06/10 13:50:24.173175] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:24.161032775 +0800 CST msgs count per second: gate(pck/s: 2747 sum: 3652775  left:2234) logic db(pck/s: 9 sum: 29040 left:0) redis db(pck/s: 9 sum: 74451 left:0) 
   [2021/06/10 13:50:26.436619] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:26.261695409 +0800 CST msgs count per second: gate(pck/s: 12515 sum: 3674941  left:1049) logic db(pck/s: 72 sum: 29175 left:0) redis db(pck/s: 73 sum: 74588 left:0) 
   [2021/06/10 13:50:31.078734] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:31.060946376 +0800 CST msgs count per second: gate(pck/s: 4558 sum: 3716113  left:1112) logic db(pck/s: 24 sum: 29361 left:0) redis db(pck/s: 24 sum: 74778 left:0) 
   [2021/06/10 13:50:32.285121] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:32.261195739 +0800 CST msgs count per second: gate(pck/s: 11589 sum: 3727702  left:1914) logic db(pck/s: 64 sum: 29425 left:0) redis db(pck/s: 66 sum: 74844 left:0) 
   [2021/06/10 13:50:33.354185] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:33.161030792 +0800 CST msgs count per second: gate(pck/s: 8747 sum: 3736449  left:1485) logic db(pck/s: 43 sum: 29468 left:0) redis db(pck/s: 43 sum: 74887 left:0) 
   [2021/06/10 13:50:39.167292] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:39.160936492 +0800 CST msgs count per second: gate(pck/s: 6324 sum: 3787520  left:2676) logic db(pck/s: 17 sum: 29741 left:0) redis db(pck/s: 17 sum: 75165 left:12) 
   [2021/06/10 13:50:48.279949] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:48.16547713 +0800 CST msgs count per second: gate(pck/s: 4530 sum: 3866298  left:2244) logic db(pck/s: 32 sum: 30137 left:0) redis db(pck/s: 32 sum: 75569 left:0) 
   [2021/06/10 13:50:56.312883] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:50:56.2759682 +0800 CST msgs count per second: gate(pck/s: 10850 sum: 3939462  left:1839) logic db(pck/s: 47 sum: 30488 left:0) redis db(pck/s: 220 sum: 80327 left:30) 
   [2021/06/10 13:51:03.161823] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:03.160969112 +0800 CST msgs count per second: gate(pck/s: 4581 sum: 4002208  left:1351) logic db(pck/s: 32 sum: 30834 left:0) redis db(pck/s: 33 sum: 81280 left:0) 
   [2021/06/10 13:51:04.219150] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:04.060930097 +0800 CST msgs count per second: gate(pck/s: 7160 sum: 4009368  left:2660) logic db(pck/s: 32 sum: 30866 left:0) redis db(pck/s: 32 sum: 81312 left:0) 
   [2021/06/10 13:51:05.379702] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:05.261499518 +0800 CST msgs count per second: gate(pck/s: 11695 sum: 4021063  left:1224) logic db(pck/s: 63 sum: 30929 left:0) redis db(pck/s: 64 sum: 81376 left:0) 
   [2021/06/10 13:51:11.283095] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:11.260940688 +0800 CST msgs count per second: gate(pck/s: 11156 sum: 4076707  left:1450) logic db(pck/s: 82 sum: 31218 left:0) redis db(pck/s: 84 sum: 81671 left:0) 
   [2021/06/10 13:51:12.298573] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:12.161094939 +0800 CST msgs count per second: gate(pck/s: 6579 sum: 4083286  left:2344) logic db(pck/s: 38 sum: 31256 left:1) redis db(pck/s: 39 sum: 81710 left:0) 
   [2021/06/10 13:51:19.199962] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:19.061258606 +0800 CST msgs count per second: gate(pck/s: 6173 sum: 4141613  left:5163) logic db(pck/s: 20 sum: 31556 left:0) redis db(pck/s: 20 sum: 82015 left:0) 
   [2021/06/10 13:51:20.339204] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:20.261759165 +0800 CST msgs count per second: gate(pck/s: 14690 sum: 4156303  left:1942) logic db(pck/s: 54 sum: 31610 left:0) redis db(pck/s: 55 sum: 82070 left:0) 
   [2021/06/10 13:51:26.326140] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:26.261169644 +0800 CST msgs count per second: gate(pck/s: 10833 sum: 4206085  left:1629) logic db(pck/s: 73 sum: 31899 left:2) redis db(pck/s: 77 sum: 82367 left:0) 
   [2021/06/10 13:51:33.187613] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:33.165455267 +0800 CST msgs count per second: gate(pck/s: 4983 sum: 4268837  left:1143) logic db(pck/s: 15 sum: 32189 left:0) redis db(pck/s: 15 sum: 82660 left:17) 
   [2021/06/10 13:51:34.202967] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:34.061006084 +0800 CST msgs count per second: gate(pck/s: 6245 sum: 4275082  left:2581) logic db(pck/s: 41 sum: 32230 left:0) redis db(pck/s: 42 sum: 82702 left:0) 
   [2021/06/10 13:51:35.374840] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:35.261746069 +0800 CST msgs count per second: gate(pck/s: 11431 sum: 4286513  left:1486) logic db(pck/s: 59 sum: 32289 left:0) redis db(pck/s: 60 sum: 82762 left:0) 
   [2021/06/10 13:51:40.096981] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:40.061187042 +0800 CST msgs count per second: gate(pck/s: 5672 sum: 4329978  left:1448) logic db(pck/s: 27 sum: 32508 left:0) redis db(pck/s: 372 sum: 83330 left:0) 
   [2021/06/10 13:51:41.265374] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:41.260993679 +0800 CST msgs count per second: gate(pck/s: 9670 sum: 4339648  left:2957) logic db(pck/s: 54 sum: 32562 left:0) redis db(pck/s: 723 sum: 84053 left:3) 
   [2021/06/10 13:51:48.218541] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:48.16109812 +0800 CST msgs count per second: gate(pck/s: 2703 sum: 4397613  left:2797) logic db(pck/s: 18 sum: 32900 left:0) redis db(pck/s: 96 sum: 88384 left:0) 
   [2021/06/10 13:51:55.191722] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:55.06093292 +0800 CST msgs count per second: gate(pck/s: 4628 sum: 4461061  left:2551) logic db(pck/s: 20 sum: 33200 left:0) redis db(pck/s: 20 sum: 88690 left:0) 
   [2021/06/10 13:51:56.364596] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:51:56.271672675 +0800 CST msgs count per second: gate(pck/s: 12273 sum: 4473334  left:1707) logic db(pck/s: 70 sum: 33270 left:0) redis db(pck/s: 71 sum: 88761 left:0) 
   [2021/06/10 13:52:02.277997] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:02.260947172 +0800 CST msgs count per second: gate(pck/s: 11848 sum: 4529595  left:1368) logic db(pck/s: 73 sum: 33548 left:0) redis db(pck/s: 75 sum: 89045 left:0) 
   [2021/06/10 13:52:03.257231] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:03.168260608 +0800 CST msgs count per second: gate(pck/s: 5401 sum: 4534996  left:2955) logic db(pck/s: 24 sum: 33572 left:0) redis db(pck/s: 24 sum: 89069 left:0) 
   [2021/06/10 13:52:09.169141] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:09.161006632 +0800 CST msgs count per second: gate(pck/s: 6031 sum: 4589202  left:2797) logic db(pck/s: 14 sum: 33831 left:0) redis db(pck/s: 14 sum: 89333 left:6) 
   [2021/06/10 13:52:10.271202] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:10.061678176 +0800 CST msgs count per second: gate(pck/s: 10885 sum: 4600087  left:2899) logic db(pck/s: 45 sum: 33876 left:0) redis db(pck/s: 46 sum: 89379 left:0) 
   [2021/06/10 13:52:11.468134] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:11.261758362 +0800 CST msgs count per second: gate(pck/s: 11868 sum: 4611955  left:1885) logic db(pck/s: 58 sum: 33934 left:0) redis db(pck/s: 59 sum: 89438 left:0) 
   [2021/06/10 13:52:15.176192] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:15.167099804 +0800 CST msgs count per second: gate(pck/s: 3814 sum: 4641777  left:1661) logic db(pck/s: 9 sum: 34089 left:0) redis db(pck/s: 9 sum: 89596 left:11) 
   [2021/06/10 13:52:16.267124] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:16.06093642 +0800 CST msgs count per second: gate(pck/s: 7918 sum: 4649695  left:3058) logic db(pck/s: 44 sum: 34133 left:0) redis db(pck/s: 45 sum: 89641 left:0) 
   [2021/06/10 13:52:17.444072] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:17.274871158 +0800 CST msgs count per second: gate(pck/s: 12354 sum: 4662049  left:1541) logic db(pck/s: 74 sum: 34207 left:0) redis db(pck/s: 75 sum: 89716 left:0) 
   [2021/06/10 13:52:21.195757] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:21.16334588 +0800 CST msgs count per second: gate(pck/s: 4807 sum: 4698479  left:1240) logic db(pck/s: 13 sum: 34366 left:0) redis db(pck/s: 13 sum: 89878 left:13) 
   [2021/06/10 13:52:28.121167] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:28.060936557 +0800 CST msgs count per second: gate(pck/s: 4319 sum: 4754761  left:1781) logic db(pck/s: 25 sum: 34693 left:0) redis db(pck/s: 25 sum: 90211 left:0) 
   [2021/06/10 13:52:29.273595] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:29.261664361 +0800 CST msgs count per second: gate(pck/s: 11713 sum: 4766474  left:5617) logic db(pck/s: 62 sum: 34755 left:0) redis db(pck/s: 63 sum: 90274 left:0) 
   [2021/06/10 13:52:36.290565] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:36.16358155 +0800 CST msgs count per second: gate(pck/s: 4688 sum: 4828006  left:3121) logic db(pck/s: 19 sum: 35075 left:0) redis db(pck/s: 604 sum: 94967 left:0) 
   [2021/06/10 13:52:43.226025] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:43.060919565 +0800 CST msgs count per second: gate(pck/s: 5372 sum: 4891348  left:2948) logic db(pck/s: 34 sum: 35400 left:0) redis db(pck/s: 34 sum: 95932 left:0) 
   [2021/06/10 13:52:44.392670] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:44.261977868 +0800 CST msgs count per second: gate(pck/s: 11631 sum: 4902979  left:1565) logic db(pck/s: 54 sum: 35454 left:0) redis db(pck/s: 55 sum: 95987 left:0) 
   [2021/06/10 13:52:49.076226] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:49.061229241 +0800 CST msgs count per second: gate(pck/s: 5606 sum: 4942369  left:2135) logic db(pck/s: 22 sum: 35658 left:0) redis db(pck/s: 22 sum: 96195 left:0) 
   [2021/06/10 13:52:50.266045] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:50.264218999 +0800 CST msgs count per second: gate(pck/s: 14100 sum: 4956469  left:2781) logic db(pck/s: 39 sum: 35697 left:0) redis db(pck/s: 40 sum: 96235 left:15) 
   [2021/06/10 13:52:56.280461] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:56.26091846 +0800 CST msgs count per second: gate(pck/s: 11397 sum: 5010296  left:1122) logic db(pck/s: 63 sum: 35989 left:0) redis db(pck/s: 64 sum: 96533 left:0) 
   [2021/06/10 13:52:57.285895] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:52:57.161077078 +0800 CST msgs count per second: gate(pck/s: 5011 sum: 5015307  left:2912) logic db(pck/s: 16 sum: 36005 left:0) redis db(pck/s: 16 sum: 96549 left:0) 
   [2021/06/10 13:53:02.263321] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:53:02.260917255 +0800 CST msgs count per second: gate(pck/s: 9014 sum: 5063273  left:2383) logic db(pck/s: 43 sum: 36248 left:0) redis db(pck/s: 44 sum: 96796 left:13) 
   [2021/06/10 13:53:07.168952] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:53:07.061000341 +0800 CST msgs count per second: gate(pck/s: 4360 sum: 5102498  left:2334) logic db(pck/s: 22 sum: 36477 left:0) redis db(pck/s: 22 sum: 97029 left:0) 
   [2021/06/10 13:53:12.161784] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:53:12.160931549 +0800 CST msgs count per second: gate(pck/s: 3174 sum: 5150180  left:1615) logic db(pck/s: 13 sum: 36711 left:0) redis db(pck/s: 13 sum: 97267 left:7) 
   [2021/06/10 13:53:18.338435] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 13:53:18.161086465 +0800 CST msgs count per second: gate(pck/s: 3668 sum: 5200234  left:2683) logic db(pck/s: 24 sum: 36976 left:0) redis db(pck/s: 24 sum: 97537 left:0) 
   
   --------------------------
   
   logic服务器启动时间:1.007509643s
   logic账号总数:5000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11522(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(   5)                               80.00%  20.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100007(   1)                                0.00%   0.00%   0.00%  100.00%   0.00%   0.00%   0.00% 
   协议:       11506(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11516(   9)                               55.56%  22.22%  22.22%   0.00%   0.00%   0.00%   0.00% 
   协议:       11508(   6)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11518(   4)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save( 550)                                1.09%   5.82%  15.09%  20.91%  40.00%  17.09%   0.00% 
   协议:       11510(   4)                               50.00%  25.00%  25.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11501(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11520(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022( 545)                                0.00%  11.56%  39.45%  38.17%  10.46%   0.37%   0.00% 
   协议:       11503(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100005(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   
   ```

3. metric

   ![friend-********-13-02](./pic/friend-********-13-02.png)
   
4. profile

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********13
   profile-********132634  profile-**************  
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-************** 
   File: service
   Type: cpu
   Time: Jun 10, 2021 at 1:48pm (CST)
   Duration: 30.10s, Total samples = 23.72s (78.81%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 9770ms, 41.19% of 23720ms total
   Dropped 458 nodes (cum <= 118.60ms)
   Showing top 10 nodes out of 234
         flat  flat%   sum%        cum   cum%
       3210ms 13.53% 13.53%     5420ms 22.85%  runtime.scanobject
       1260ms  5.31% 18.84%     1320ms  5.56%  app/protos/in/db.(*FriendLike).Size
       1110ms  4.68% 23.52%     1150ms  4.85%  runtime.findObject
        760ms  3.20% 26.73%      760ms  3.20%  runtime.markBits.isMarked (inline)
        720ms  3.04% 29.76%     1050ms  4.43%  runtime.mapiternext
        580ms  2.45% 32.21%      630ms  2.66%  syscall.Syscall
        560ms  2.36% 34.57%      570ms  2.40%  github.com/golang/snappy.encodeBlock
        530ms  2.23% 36.80%      530ms  2.23%  runtime.epollwait
        530ms  2.23% 39.04%      530ms  2.23%  runtime.futex
        510ms  2.15% 41.19%      630ms  2.66%  runtime.mapaccess1_fast64
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-************** 
   File: service
   Type: inuse_space
   Time: Jun 10, 2021 at 1:48pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 117.26MB, 55.83% of 210.03MB total
   Dropped 138 nodes (cum <= 1.05MB)
   Showing top 10 nodes out of 168
         flat  flat%   sum%        cum   cum%
      28.66MB 13.64% 13.64%    28.66MB 13.64%  github.com/golang/snappy.Decode
      16.02MB  7.63% 21.27%    16.02MB  7.63%  app/logic/activity/friend.(*Friends).sendLikeOne
         15MB  7.14% 28.41%       15MB  7.14%  app/logic/character.(*User).NewUserSnapshot
      13.52MB  6.44% 34.85%    13.52MB  6.44%  app/logic/activity/friend.(*Friends).besentLike
      11.53MB  5.49% 40.34%    11.53MB  5.49%  app/logic/activity/friend.(*Friends).init
      10.03MB  4.77% 45.11%    10.03MB  4.77%  github.com/gogo/protobuf/proto.(*Buffer).grow
       8.01MB  3.81% 48.92%     8.01MB  3.81%  app/logic/helper/monitor.(*Cmds).Add
       6.50MB  3.09% 52.02%     6.50MB  3.09%  app/logic/activity/friend.(*Friends).add
          4MB  1.91% 53.93%        4MB  1.91%  app/logic/character.(*User).AddRecommendedFriend
          4MB  1.90% 55.83%        4MB  1.90%  app/logic/character.newShop
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-************** 
   File: service
   Type: alloc_space
   Time: Jun 10, 2021 at 1:48pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 8259.75MB, 65.69% of 12572.99MB total
   Dropped 489 nodes (cum <= 62.86MB)
   Showing top 10 nodes out of 132
         flat  flat%   sum%        cum   cum%
    2168.09MB 17.24% 17.24%  2180.07MB 17.34%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
    1210.94MB  9.63% 26.88%  1210.94MB  9.63%  app/logic/db/redisop.CompressSnappyWithFlag
    1022.65MB  8.13% 35.01%  1022.65MB  8.13%  app/logic/character.(*User).NewUserSnapshot
    1012.09MB  8.05% 43.06%  1012.09MB  8.05%  github.com/gogo/protobuf/proto.(*Buffer).grow (inline)
     961.34MB  7.65% 50.70%  1973.93MB 15.70%  app/logic/activity/friend.(*Manager).Save
     453.08MB  3.60% 54.31%   453.08MB  3.60%  strings.(*Builder).WriteString
     405.51MB  3.23% 57.53%   408.51MB  3.25%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     386.52MB  3.07% 60.61%   386.52MB  3.07%  container/list.(*List).insertValue
     353.02MB  2.81% 63.42%   567.04MB  4.51%  context.WithDeadline
     286.52MB  2.28% 65.69%   286.52MB  2.28%  app/logic/session.(*Client).Process
   
   ```

   

#### 第6版

byte存储和加载， 5000人

left最高4000



1. robot

   ```shell
   2021-06-10 14:33:06 压测数据分析
   压测基础情况
   开始时间：2021/06/10 14:22:10, 结束时间:2021/06/10 14:32:04, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:4893870， 平均每秒消息数:8238
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     72485  107616  67.36%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    620560  782208  79.33%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    94757  194214  48.79%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 946891  966643  97.96%
   协议:cmd:11503,MSG_C2L_FriendAdd                         541946  835381  64.87%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   574780  616339  93.26%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 81848  140522  58.25%
   协议:cmd:11520,MSG_C2L_FriendSearch                      240019  269848  88.95%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     263907  269961  97.76%
   协议:cmd:11508,MSG_C2L_FriendDelete                      389778  431267  90.38%
   协议:cmd:11501,MSG_C2L_FriendInfo                        274871  274871 100.00%
   协议正确数:4111842 协议总数:4898870 正确比:83.93%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         25.14%  34.46%  39.30%   1.10%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   76.93%   5.66%   5.00%   5.32%   5.91%   1.19%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.14%   0.98%   2.88%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   72.92%   7.19%   8.91%   7.30%   3.50%   0.18%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  76.67%   5.54%   5.01%   5.30%   6.12%   1.37%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                76.92%   7.30%   8.56%   4.57%   2.19%   0.47%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        75.70%   6.21%   7.97%   6.77%   3.15%   0.21%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  75.61%   5.61%   5.14%   5.59%   6.60%   1.45%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               74.98%   5.72%   5.43%   5.80%   6.66%   1.41%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    76.59%   5.59%   4.97%   5.34%   6.18%   1.34%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     76.50%   5.50%   5.03%   5.34%   6.25%   1.37%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     75.81%   5.70%   5.11%   5.48%   6.50%   1.41%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       76.72%   5.61%   4.99%   5.19%   6.12%   1.36%   0.00% 
   
   ---------------------
   
   ```

   

2. logic

   ```shell
   
   所有超时的left
   [2021/06/10 14:32:01.412215] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:32:01.198742631 +0800 CST msgs count per second: gate(pck/s: 7884 sum: 5163313  left:3439) logic db(pck/s: 27 sum: 36543 left:0) redis db(pck/s: 27 sum: 97103 left:0) 
   [2021/06/10 14:25:19.209575] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:25:19.198694744 +0800 CST msgs count per second: gate(pck/s: 4623 sum: 1605890  left:1250) logic db(pck/s: 24 sum: 18401 left:0) redis db(pck/s: 24 sum: 38596 left:1) 
   [2021/06/10 14:25:21.310218] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:25:21.306863288 +0800 CST msgs count per second: gate(pck/s: 11983 sum: 1625083  left:1498) logic db(pck/s: 76 sum: 18509 left:0) redis db(pck/s: 78 sum: 38707 left:0) 
   [2021/06/10 14:25:22.266780] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:25:22.198806678 +0800 CST msgs count per second: gate(pck/s: 6666 sum: 1631749  left:1485) logic db(pck/s: 32 sum: 18541 left:0) redis db(pck/s: 32 sum: 38739 left:0) 
   [2021/06/10 14:25:24.340895] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:25:24.298704708 +0800 CST msgs count per second: gate(pck/s: 12080 sum: 1650199  left:1598) logic db(pck/s: 67 sum: 18642 left:0) redis db(pck/s: 68 sum: 38842 left:0) 
   [2021/06/10 14:25:49.205556] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:25:49.200954901 +0800 CST msgs count per second: gate(pck/s: 2283 sum: 1865470  left:1698) logic db(pck/s: 9 sum: 19720 left:0) redis db(pck/s: 9 sum: 44944 left:9) 
   [2021/06/10 14:25:51.318101] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:25:51.317615499 +0800 CST msgs count per second: gate(pck/s: 12210 sum: 1886117  left:1409) logic db(pck/s: 67 sum: 19843 left:0) redis db(pck/s: 69 sum: 45070 left:0) 
   [2021/06/10 14:25:52.284492] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:25:52.198688332 +0800 CST msgs count per second: gate(pck/s: 6397 sum: 1892514  left:1748) logic db(pck/s: 41 sum: 19884 left:0) redis db(pck/s: 41 sum: 45111 left:0) 
   [2021/06/10 14:26:12.305074] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:12.298685341 +0800 CST msgs count per second: gate(pck/s: 11691 sum: 2068944  left:1064) logic db(pck/s: 68 sum: 20847 left:0) redis db(pck/s: 69 sum: 46094 left:0) 
   [2021/06/10 14:26:14.139004] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:14.098829767 +0800 CST msgs count per second: gate(pck/s: 4973 sum: 2079555  left:1402) logic db(pck/s: 17 sum: 20894 left:0) redis db(pck/s: 17 sum: 46142 left:0) 
   [2021/06/10 14:26:15.312130] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:15.298687404 +0800 CST msgs count per second: gate(pck/s: 16357 sum: 2095912  left:2890) logic db(pck/s: 69 sum: 20963 left:0) redis db(pck/s: 71 sum: 46213 left:0) 
   [2021/06/10 14:26:16.275787] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:16.199395982 +0800 CST msgs count per second: gate(pck/s: 7979 sum: 2103891  left:2098) logic db(pck/s: 23 sum: 20986 left:0) redis db(pck/s: 541 sum: 46754 left:0) 
   [2021/06/10 14:26:18.391127] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:18.298696726 +0800 CST msgs count per second: gate(pck/s: 13114 sum: 2124484  left:1037) logic db(pck/s: 62 sum: 21090 left:0) redis db(pck/s: 922 sum: 48719 left:0) 
   [2021/06/10 14:26:33.323096] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:33.298771829 +0800 CST msgs count per second: gate(pck/s: 12027 sum: 2253086  left:1668) logic db(pck/s: 56 sum: 21742 left:0) redis db(pck/s: 57 sum: 52010 left:0) 
   [2021/06/10 14:26:34.248347] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:34.198751869 +0800 CST msgs count per second: gate(pck/s: 5477 sum: 2258563  left:1912) logic db(pck/s: 30 sum: 21772 left:0) redis db(pck/s: 30 sum: 52040 left:0) 
   [2021/06/10 14:26:36.345467] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:36.298778079 +0800 CST msgs count per second: gate(pck/s: 15220 sum: 2282714  left:1942) logic db(pck/s: 55 sum: 21868 left:0) redis db(pck/s: 56 sum: 52138 left:0) 
   [2021/06/10 14:26:45.331695] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:45.311450262 +0800 CST msgs count per second: gate(pck/s: 17730 sum: 2362133  left:1371) logic db(pck/s: 75 sum: 22294 left:0) redis db(pck/s: 76 sum: 52573 left:0) 
   [2021/06/10 14:26:50.173343] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:50.098853005 +0800 CST msgs count per second: gate(pck/s: 4034 sum: 2397491  left:1677) logic db(pck/s: 14 sum: 22482 left:0) redis db(pck/s: 14 sum: 52765 left:0) 
   [2021/06/10 14:26:51.303534] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:51.298963662 +0800 CST msgs count per second: gate(pck/s: 12218 sum: 2409709  left:1951) logic db(pck/s: 64 sum: 22546 left:0) redis db(pck/s: 66 sum: 52831 left:0) 
   [2021/06/10 14:26:52.294918] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:52.198704702 +0800 CST msgs count per second: gate(pck/s: 6926 sum: 2416635  left:2192) logic db(pck/s: 30 sum: 22576 left:0) redis db(pck/s: 30 sum: 52861 left:0) 
   [2021/06/10 14:26:54.406114] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:26:54.298795035 +0800 CST msgs count per second: gate(pck/s: 13267 sum: 2436600  left:1166) logic db(pck/s: 69 sum: 22690 left:0) redis db(pck/s: 70 sum: 52977 left:0) 
   [2021/06/10 14:27:10.213128] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:10.198744454 +0800 CST msgs count per second: gate(pck/s: 2958 sum: 2575365  left:1656) logic db(pck/s: 22 sum: 23375 left:0) redis db(pck/s: 981 sum: 57835 left:0) 
   [2021/06/10 14:27:11.197789] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:11.098930634 +0800 CST msgs count per second: gate(pck/s: 6915 sum: 2582280  left:2009) logic db(pck/s: 32 sum: 23407 left:0) redis db(pck/s: 780 sum: 58615 left:0) 
   [2021/06/10 14:27:12.329961] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:12.29886816 +0800 CST msgs count per second: gate(pck/s: 11151 sum: 2593431  left:2587) logic db(pck/s: 60 sum: 23467 left:0) redis db(pck/s: 100 sum: 58715 left:0) 
   [2021/06/10 14:27:13.310407] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:13.198733528 +0800 CST msgs count per second: gate(pck/s: 8595 sum: 2602026  left:2300) logic db(pck/s: 38 sum: 23505 left:0) redis db(pck/s: 95 sum: 58810 left:0) 
   [2021/06/10 14:27:15.433603] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:15.299353667 +0800 CST msgs count per second: gate(pck/s: 17503 sum: 2625997  left:1774) logic db(pck/s: 78 sum: 23613 left:0) redis db(pck/s: 79 sum: 58920 left:0) 
   [2021/06/10 14:27:24.319261] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:24.298699071 +0800 CST msgs count per second: gate(pck/s: 11750 sum: 2699375  left:2699) logic db(pck/s: 79 sum: 23999 left:0) redis db(pck/s: 81 sum: 59315 left:0) 
   [2021/06/10 14:27:25.263895] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:25.198693953 +0800 CST msgs count per second: gate(pck/s: 7579 sum: 2706954  left:4766) logic db(pck/s: 26 sum: 24025 left:0) redis db(pck/s: 26 sum: 59341 left:0) 
   [2021/06/10 14:27:37.214162] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:37.198724601 +0800 CST msgs count per second: gate(pck/s: 9235 sum: 2815980  left:1497) logic db(pck/s: 64 sum: 24603 left:0) redis db(pck/s: 66 sum: 59931 left:0) 
   [2021/06/10 14:27:38.211695] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:38.098912004 +0800 CST msgs count per second: gate(pck/s: 6749 sum: 2822729  left:2275) logic db(pck/s: 33 sum: 24636 left:0) redis db(pck/s: 33 sum: 59964 left:0) 
   [2021/06/10 14:27:39.324491] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:39.298746661 +0800 CST msgs count per second: gate(pck/s: 10841 sum: 2833570  left:2906) logic db(pck/s: 50 sum: 24686 left:0) redis db(pck/s: 51 sum: 60015 left:0) 
   [2021/06/10 14:27:49.236036] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:49.19891861 +0800 CST msgs count per second: gate(pck/s: 2253 sum: 2919968  left:2407) logic db(pck/s: 12 sum: 25153 left:0) redis db(pck/s: 12 sum: 60491 left:0) 
   [2021/06/10 14:27:51.396625] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:27:51.315102015 +0800 CST msgs count per second: gate(pck/s: 12447 sum: 2941170  left:1598) logic db(pck/s: 71 sum: 25265 left:1) redis db(pck/s: 73 sum: 60606 left:0) 
   [2021/06/10 14:28:01.208164] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:01.198756395 +0800 CST msgs count per second: gate(pck/s: 2339 sum: 3025721  left:1932) logic db(pck/s: 6 sum: 25699 left:0) redis db(pck/s: 620 sum: 65819 left:5) 
   [2021/06/10 14:28:03.371894] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:03.315861093 +0800 CST msgs count per second: gate(pck/s: 11747 sum: 3046145  left:2169) logic db(pck/s: 67 sum: 25812 left:0) redis db(pck/s: 140 sum: 66163 left:0) 
   [2021/06/10 14:28:12.306572] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:12.298765924 +0800 CST msgs count per second: gate(pck/s: 12144 sum: 3127679  left:1063) logic db(pck/s: 80 sum: 26230 left:0) redis db(pck/s: 81 sum: 66590 left:0) 
   [2021/06/10 14:28:13.205132] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:13.198788206 +0800 CST msgs count per second: gate(pck/s: 3785 sum: 3131464  left:1938) logic db(pck/s: 14 sum: 26244 left:0) redis db(pck/s: 14 sum: 66604 left:6) 
   [2021/06/10 14:28:15.470938] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:15.29882312 +0800 CST msgs count per second: gate(pck/s: 17165 sum: 3157268  left:2400) logic db(pck/s: 77 sum: 26368 left:0) redis db(pck/s: 78 sum: 66730 left:0) 
   [2021/06/10 14:28:23.181777] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:23.098850169 +0800 CST msgs count per second: gate(pck/s: 3345 sum: 3218875  left:2286) logic db(pck/s: 10 sum: 26704 left:0) redis db(pck/s: 10 sum: 67073 left:0) 
   [2021/06/10 14:28:24.329609] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:24.298701262 +0800 CST msgs count per second: gate(pck/s: 10336 sum: 3229211  left:3785) logic db(pck/s: 70 sum: 26774 left:0) redis db(pck/s: 71 sum: 67144 left:0) 
   [2021/06/10 14:28:32.164008] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:32.09888261 +0800 CST msgs count per second: gate(pck/s: 4195 sum: 3298881  left:1836) logic db(pck/s: 20 sum: 27129 left:0) redis db(pck/s: 19 sum: 67506 left:0) 
   [2021/06/10 14:28:33.355155] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:33.340631488 +0800 CST msgs count per second: gate(pck/s: 11558 sum: 3310439  left:3336) logic db(pck/s: 58 sum: 27187 left:0) redis db(pck/s: 59 sum: 67565 left:10) 
   [2021/06/10 14:28:40.206151] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:40.198813076 +0800 CST msgs count per second: gate(pck/s: 3336 sum: 3373234  left:1643) logic db(pck/s: 7 sum: 27504 left:0) redis db(pck/s: 7 sum: 67888 left:16) 
   [2021/06/10 14:28:42.456428] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:42.298748972 +0800 CST msgs count per second: gate(pck/s: 12309 sum: 3393638  left:1648) logic db(pck/s: 54 sum: 27614 left:0) redis db(pck/s: 55 sum: 68000 left:0) 
   [2021/06/10 14:28:45.330394] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:45.30249481 +0800 CST msgs count per second: gate(pck/s: 17729 sum: 3424062  left:1344) logic db(pck/s: 78 sum: 27742 left:0) redis db(pck/s: 77 sum: 68131 left:0) 
   [2021/06/10 14:28:50.125335] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:50.099495367 +0800 CST msgs count per second: gate(pck/s: 3869 sum: 3460309  left:1357) logic db(pck/s: 13 sum: 27946 left:0) redis db(pck/s: 1085 sum: 72154 left:0) 
   [2021/06/10 14:28:51.362718] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:28:51.300952042 +0800 CST msgs count per second: gate(pck/s: 12429 sum: 3472738  left:2582) logic db(pck/s: 49 sum: 27995 left:0) redis db(pck/s: 1006 sum: 73160 left:37) 
   [2021/06/10 14:29:00.325145] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:00.31580137 +0800 CST msgs count per second: gate(pck/s: 12779 sum: 3554887  left:1246) logic db(pck/s: 79 sum: 28457 left:0) redis db(pck/s: 80 sum: 73861 left:0) 
   [2021/06/10 14:29:01.261840] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:01.198749423 +0800 CST msgs count per second: gate(pck/s: 3723 sum: 3558610  left:2644) logic db(pck/s: 16 sum: 28473 left:0) redis db(pck/s: 16 sum: 73877 left:0) 
   [2021/06/10 14:29:11.191184] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:11.098906302 +0800 CST msgs count per second: gate(pck/s: 4516 sum: 3645397  left:2451) logic db(pck/s: 36 sum: 28940 left:0) redis db(pck/s: 36 sum: 74353 left:0) 
   [2021/06/10 14:29:12.334786] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:12.298748684 +0800 CST msgs count per second: gate(pck/s: 11255 sum: 3656652  left:3586) logic db(pck/s: 44 sum: 28984 left:0) redis db(pck/s: 45 sum: 74398 left:0) 
   [2021/06/10 14:29:19.204122] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:19.198700306 +0800 CST msgs count per second: gate(pck/s: 10860 sum: 3719901  left:1354) logic db(pck/s: 61 sum: 29289 left:0) redis db(pck/s: 63 sum: 74710 left:0) 
   [2021/06/10 14:29:20.290480] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:20.183656141 +0800 CST msgs count per second: gate(pck/s: 6776 sum: 3726677  left:3633) logic db(pck/s: 27 sum: 29316 left:0) redis db(pck/s: 27 sum: 74737 left:0) 
   [2021/06/10 14:29:21.473952] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:21.298761059 +0800 CST msgs count per second: gate(pck/s: 13477 sum: 3740154  left:1684) logic db(pck/s: 65 sum: 29381 left:0) redis db(pck/s: 66 sum: 74803 left:0) 
   [2021/06/10 14:29:27.155179] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:27.025345336 +0800 CST msgs count per second: gate(pck/s: 6558 sum: 3789764  left:1735) logic db(pck/s: 48 sum: 29632 left:0) redis db(pck/s: 48 sum: 75059 left:0) 
   [2021/06/10 14:29:28.294330] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:28.198699596 +0800 CST msgs count per second: gate(pck/s: 9620 sum: 3799384  left:3221) logic db(pck/s: 53 sum: 29685 left:0) redis db(pck/s: 54 sum: 75113 left:0) 
   [2021/06/10 14:29:30.337189] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:30.303750617 +0800 CST msgs count per second: gate(pck/s: 12353 sum: 3820943  left:1409) logic db(pck/s: 75 sum: 29810 left:0) redis db(pck/s: 76 sum: 75240 left:0) 
   [2021/06/10 14:29:36.316243] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:36.298683494 +0800 CST msgs count per second: gate(pck/s: 15980 sum: 3875500  left:2125) logic db(pck/s: 67 sum: 30079 left:0) redis db(pck/s: 252 sum: 75698 left:0) 
   [2021/06/10 14:29:45.439391] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:45.298803264 +0800 CST msgs count per second: gate(pck/s: 14702 sum: 3954323  left:3587) logic db(pck/s: 63 sum: 30443 left:0) redis db(pck/s: 64 sum: 80886 left:0) 
   [2021/06/10 14:29:51.124940] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:51.004408781 +0800 CST msgs count per second: gate(pck/s: 7020 sum: 4001481  left:1357) logic db(pck/s: 48 sum: 30689 left:0) redis db(pck/s: 48 sum: 81137 left:0) 
   [2021/06/10 14:29:52.254431] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:29:52.198745404 +0800 CST msgs count per second: gate(pck/s: 9415 sum: 4010896  left:3017) logic db(pck/s: 52 sum: 30741 left:0) redis db(pck/s: 53 sum: 81190 left:0) 
   [2021/06/10 14:30:00.310377] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:00.298715239 +0800 CST msgs count per second: gate(pck/s: 10621 sum: 4084825  left:3170) logic db(pck/s: 41 sum: 31082 left:0) redis db(pck/s: 42 sum: 81538 left:14) 
   [2021/06/10 14:30:04.240274] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:04.238139579 +0800 CST msgs count per second: gate(pck/s: 3551 sum: 4118597  left:1741) logic db(pck/s: 16 sum: 31271 left:0) redis db(pck/s: 17 sum: 81731 left:0) 
   [2021/06/10 14:30:06.311464] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:06.298694489 +0800 CST msgs count per second: gate(pck/s: 13468 sum: 4140640  left:2835) logic db(pck/s: 67 sum: 31370 left:0) redis db(pck/s: 69 sum: 81832 left:0) 
   [2021/06/10 14:30:07.336099] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:07.19874786 +0800 CST msgs count per second: gate(pck/s: 7755 sum: 4148395  left:3591) logic db(pck/s: 33 sum: 31403 left:0) redis db(pck/s: 33 sum: 81865 left:0) 
   [2021/06/10 14:30:14.243961] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:14.099920015 +0800 CST msgs count per second: gate(pck/s: 4810 sum: 4204679  left:3115) logic db(pck/s: 28 sum: 31712 left:0) redis db(pck/s: 28 sum: 82180 left:0) 
   [2021/06/10 14:30:15.542690] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:15.30470311 +0800 CST msgs count per second: gate(pck/s: 18201 sum: 4222880  left:2702) logic db(pck/s: 81 sum: 31793 left:1) redis db(pck/s: 83 sum: 82263 left:0) 
   [2021/06/10 14:30:20.202447] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:20.099217317 +0800 CST msgs count per second: gate(pck/s: 3790 sum: 4260297  left:2642) logic db(pck/s: 24 sum: 31978 left:0) redis db(pck/s: 24 sum: 82451 left:0) 
   [2021/06/10 14:30:21.349589] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:21.298787492 +0800 CST msgs count per second: gate(pck/s: 11047 sum: 4271344  left:3487) logic db(pck/s: 54 sum: 32032 left:0) redis db(pck/s: 55 sum: 82506 left:0) 
   [2021/06/10 14:30:27.311179] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:27.298703494 +0800 CST msgs count per second: gate(pck/s: 11130 sum: 4328436  left:2375) logic db(pck/s: 78 sum: 32331 left:0) redis db(pck/s: 897 sum: 83759 left:0) 
   [2021/06/10 14:30:28.278199] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:28.198745099 +0800 CST msgs count per second: gate(pck/s: 5838 sum: 4334274  left:3025) logic db(pck/s: 20 sum: 32351 left:0) redis db(pck/s: 678 sum: 84437 left:0) 
   [2021/06/10 14:30:34.234614] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:34.198741536 +0800 CST msgs count per second: gate(pck/s: 2064 sum: 4385023  left:2444) logic db(pck/s: 3 sum: 32614 left:0) redis db(pck/s: 60 sum: 88099 left:8) 
   [2021/06/10 14:30:41.223045] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:41.101714997 +0800 CST msgs count per second: gate(pck/s: 3862 sum: 4446573  left:2928) logic db(pck/s: 10 sum: 32898 left:0) redis db(pck/s: 10 sum: 88389 left:0) 
   [2021/06/10 14:30:42.558767] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:42.30007366 +0800 CST msgs count per second: gate(pck/s: 15213 sum: 4461786  left:1115) logic db(pck/s: 90 sum: 32988 left:0) redis db(pck/s: 91 sum: 88480 left:0) 
   [2021/06/10 14:30:47.117397] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:47.110641148 +0800 CST msgs count per second: gate(pck/s: 3477 sum: 4502117  left:1210) logic db(pck/s: 17 sum: 33176 left:0) redis db(pck/s: 17 sum: 88672 left:0) 
   [2021/06/10 14:30:48.298735] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:48.298695335 +0800 CST msgs count per second: gate(pck/s: 10069 sum: 4512186  left:3450) logic db(pck/s: 52 sum: 33228 left:0) redis db(pck/s: 53 sum: 88725 left:8) 
   [2021/06/10 14:30:55.216700] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:30:55.202765398 +0800 CST msgs count per second: gate(pck/s: 310 sum: 4570385  left:5715) logic db(pck/s: 2 sum: 33537 left:0) redis db(pck/s: 2 sum: 89040 left:0) 
   [2021/06/10 14:31:01.225685] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:01.207006937 +0800 CST msgs count per second: gate(pck/s: 2830 sum: 4628981  left:1801) logic db(pck/s: 0 sum: 33791 left:0) redis db(pck/s: 0 sum: 89299 left:16) 
   [2021/06/10 14:31:02.296701] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:02.098999892 +0800 CST msgs count per second: gate(pck/s: 7531 sum: 4636512  left:3437) logic db(pck/s: 55 sum: 33846 left:0) redis db(pck/s: 56 sum: 89355 left:0) 
   [2021/06/10 14:31:08.131556] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:08.098948883 +0800 CST msgs count per second: gate(pck/s: 3732 sum: 4689778  left:1419) logic db(pck/s: 28 sum: 34149 left:0) redis db(pck/s: 28 sum: 89663 left:0) 
   [2021/06/10 14:31:09.316220] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:09.298740672 +0800 CST msgs count per second: gate(pck/s: 10711 sum: 4700489  left:3206) logic db(pck/s: 55 sum: 34204 left:0) redis db(pck/s: 56 sum: 89719 left:15) 
   [2021/06/10 14:31:15.330152] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:15.299133347 +0800 CST msgs count per second: gate(pck/s: 16464 sum: 4758714  left:2291) logic db(pck/s: 76 sum: 34536 left:0) redis db(pck/s: 77 sum: 90057 left:0) 
   [2021/06/10 14:31:16.305967] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:16.198688978 +0800 CST msgs count per second: gate(pck/s: 4115 sum: 4762829  left:4076) logic db(pck/s: 14 sum: 34550 left:0) redis db(pck/s: 144 sum: 90201 left:0) 
   [2021/06/10 14:31:22.203870] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:22.198738183 +0800 CST msgs count per second: gate(pck/s: 960 sum: 4812201  left:2680) logic db(pck/s: 2 sum: 34792 left:0) redis db(pck/s: 340 sum: 95059 left:0) 
   [2021/06/10 14:31:28.251844] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:28.207136494 +0800 CST msgs count per second: gate(pck/s: 9008 sum: 4868702  left:3259) logic db(pck/s: 42 sum: 35074 left:0) redis db(pck/s: 43 sum: 95606 left:0) 
   [2021/06/10 14:31:34.209172] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:34.198786337 +0800 CST msgs count per second: gate(pck/s: 3222 sum: 4919968  left:2152) logic db(pck/s: 9 sum: 35336 left:0) redis db(pck/s: 9 sum: 95873 left:16) 
   [2021/06/10 14:31:39.324101] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:39.298731316 +0800 CST msgs count per second: gate(pck/s: 13340 sum: 4972521  left:1039) logic db(pck/s: 74 sum: 35620 left:0) redis db(pck/s: 75 sum: 96162 left:0) 
   [2021/06/10 14:31:41.192467] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:41.099036367 +0800 CST msgs count per second: gate(pck/s: 4248 sum: 4982806  left:2467) logic db(pck/s: 28 sum: 35663 left:0) redis db(pck/s: 28 sum: 96206 left:0) 
   [2021/06/10 14:31:42.346456] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:42.301945133 +0800 CST msgs count per second: gate(pck/s: 9933 sum: 4992739  left:3969) logic db(pck/s: 54 sum: 35717 left:0) redis db(pck/s: 55 sum: 96261 left:0) 
   [2021/06/10 14:31:47.254616] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:47.098987557 +0800 CST msgs count per second: gate(pck/s: 5554 sum: 5038512  left:3271) logic db(pck/s: 19 sum: 35954 left:0) redis db(pck/s: 19 sum: 96502 left:0) 
   [2021/06/10 14:31:48.444838] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:48.298707054 +0800 CST msgs count per second: gate(pck/s: 12015 sum: 5050527  left:2772) logic db(pck/s: 64 sum: 36018 left:0) redis db(pck/s: 65 sum: 96567 left:0) 
   [2021/06/10 14:31:53.135037] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:53.109770341 +0800 CST msgs count per second: gate(pck/s: 3591 sum: 5088644  left:1460) logic db(pck/s: 16 sum: 36205 left:0) redis db(pck/s: 16 sum: 96758 left:0) 
   [2021/06/10 14:31:54.307294] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:31:54.298728339 +0800 CST msgs count per second: gate(pck/s: 9789 sum: 5098433  left:4029) logic db(pck/s: 40 sum: 36245 left:0) redis db(pck/s: 41 sum: 96799 left:0) 
   [2021/06/10 14:32:00.311170] [INFO] (/home/<USER>/ngame/server/src/app/logic/service/timer.go:46) NOW: 2021-06-10 14:32:00.298683123 +0800 CST msgs count per second: gate(pck/s: 9526 sum: 5155429  left:2657) logic db(pck/s: 71 sum: 36516 left:0) redis db(pck/s: 73 sum: 97076 left:0) 
   
   --------------------------
   
   logic服务器启动时间:1.173491046s
   logic账号总数:10000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       11522(   5)                               80.00%  20.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   6)                                0.00%  83.33%  16.67%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(   2)                               50.00%   0.00%  50.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100007(   1)                                0.00%   0.00%   0.00%   0.00%   0.00%  100.00%   0.00% 
   协议:       11506(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11516(   5)                               60.00%  20.00%  20.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11508(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11518(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save( 551)                                2.00%   5.08%  14.16%  23.41%  38.48%  16.88%   0.00% 
   协议:       11510(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11501(   2)                               50.00%   0.00%  50.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11520(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022( 539)                                0.00%  10.95%  39.15%  43.41%   6.12%   0.37%   0.00% 
   协议:       11512(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11503(   7)                               42.86%  57.14%   0.00%   0.00%   0.00%   0.00%   0.00% 
   
   ```

3. metric

   ![friend-********-13-03](./pic/friend-********-13-03.png)
   
4. profile

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********142710 
   File: service
   Type: cpu
   Time: Jun 10, 2021 at 2:27pm (CST)
   Duration: 30.11s, Total samples = 23.31s (77.40%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 9380ms, 40.24% of 23310ms total
   Dropped 452 nodes (cum <= 116.55ms)
   Showing top 10 nodes out of 219
         flat  flat%   sum%        cum   cum%
       2980ms 12.78% 12.78%     4990ms 21.41%  runtime.scanobject
        900ms  3.86% 16.65%     1030ms  4.42%  runtime.findObject
        890ms  3.82% 20.46%      930ms  3.99%  app/protos/in/db.(*FriendLike).Size
        870ms  3.73% 24.20%     1500ms  6.44%  runtime.mapiternext
        730ms  3.13% 27.33%      730ms  3.13%  runtime.epollwait
        630ms  2.70% 30.03%     1600ms  6.86%  runtime.mallocgc
        630ms  2.70% 32.73%      630ms  2.70%  runtime.markBits.isMarked (inline)
        610ms  2.62% 35.35%      630ms  2.70%  app/protos/in/db.(*Friend).Size
        590ms  2.53% 37.88%      590ms  2.53%  github.com/golang/snappy.encodeBlock
        550ms  2.36% 40.24%      760ms  3.26%  app/logic/activity/friend.(*Friends).GetFriendNum
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-********142710 
   File: service
   Type: inuse_space
   Time: Jun 10, 2021 at 2:27pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 161.79MB, 60.31% of 268.26MB total
   Dropped 111 nodes (cum <= 1.34MB)
   Showing top 10 nodes out of 164
         flat  flat%   sum%        cum   cum%
      59.81MB 22.30% 22.30%    59.81MB 22.30%  github.com/golang/snappy.Decode
      20.52MB  7.65% 29.95%    20.52MB  7.65%  app/logic/activity/friend.(*Friends).sendLikeOne
      14.51MB  5.41% 35.36%    14.51MB  5.41%  app/logic/activity/friend.(*Friends).besentLike
      13.53MB  5.04% 40.40%    13.53MB  5.04%  app/logic/activity/friend.(*Friends).init
      11.36MB  4.23% 44.63%    11.36MB  4.23%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
      10.50MB  3.91% 48.55%    10.50MB  3.91%  app/logic/character.(*User).NewUserSnapshot
          9MB  3.36% 51.90%        9MB  3.36%  app/logic/character.newShop
       8.53MB  3.18% 55.08%     8.53MB  3.18%  app/logic/db/redisop.CompressSnappyWithFlag
       7.02MB  2.62% 57.70%     7.02MB  2.62%  github.com/gogo/protobuf/proto.(*Buffer).grow
       7.01MB  2.61% 60.31%     7.01MB  2.61%  app/logic/helper/monitor.(*Cmds).Add
   
   
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-********142710 
   File: service
   Type: alloc_space
   Time: Jun 10, 2021 at 2:27pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 8020.56MB, 64.59% of 12417.85MB total
   Dropped 480 nodes (cum <= 62.09MB)
   Showing top 10 nodes out of 136
         flat  flat%   sum%        cum   cum%
    2097.54MB 16.89% 16.89%  2099.18MB 16.90%  gitlab.qdream.com/kit/sea/redis/resp.appendStr
    1161.28MB  9.35% 26.24%  1161.28MB  9.35%  app/logic/db/redisop.CompressSnappyWithFlag
     992.07MB  7.99% 34.23%  1949.98MB 15.70%  app/logic/activity/friend.(*Manager).Save
     969.64MB  7.81% 42.04%   969.64MB  7.81%  app/logic/character.(*User).NewUserSnapshot
     957.92MB  7.71% 49.75%   957.92MB  7.71%  github.com/gogo/protobuf/proto.(*Buffer).grow (inline)
     452.08MB  3.64% 53.40%   452.08MB  3.64%  strings.(*Builder).WriteString
     399.48MB  3.22% 56.61%   401.98MB  3.24%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     366.03MB  2.95% 59.56%   603.05MB  4.86%  context.WithDeadline
     348.02MB  2.80% 62.36%   348.02MB  2.80%  container/list.(*List).insertValue
     276.52MB  2.23% 64.59%   276.52MB  2.23%  app/logic/session.(*Client).Process
   
   ```

   
