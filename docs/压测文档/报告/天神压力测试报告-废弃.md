## 天神压力测试报告

##### **一、项目概述**

对服务器进行性能测试，通过测试评估服务端的稳定情况



##### **二、测试环境**

* 客户端：服务机器人robot
* 服务器版本：**0.7.0**
* 机器人环境配置：

| 系统            | 配置                                         | 地址                            |      备注      |
| --------------- | -------------------------------------------- | ------------------------------- | :------------: |
| 云主机/Centos-7 | 8核（AMD EPYC Processor 2.9GHZ）16G 固态硬盘 | 113.31.107.156（172.21.173.36） | 执行机器人脚本 |

* 游戏服务器环境配置：

| 系统            | 配置           | 地址                            | 备注       |
| --------------- | -------------- | ------------------------------- | ---------- |
| 云主机/Centos-7 | 8核16G固态硬盘 | 106.75.239.59（172.21.254.181） | 游戏服务器 |
| 云主机/Centos-7 | 8核16G固态硬盘 | 113.31.113.60（172.21.8.19）    | 网关服务器 |



##### **三、测试指标参考**

1. 客户端连接协议：TCP
2. 并发数：每秒创建数、维持机器人连接数（在线人数）
3. 风险评估：低、中、高
4. 硬件信息：内存、CPU、带宽



##### **四、测试任务和方案**

######1、登录创建测试：登录->创建角色->进入游戏（仅心跳无其他交互）

​	限定最高创建数（50000），调整秒并发创建数，统计创角延迟时间、统计硬件资源使用情况、服务器消息处理速度

1. 总人数50000，创建数2000/秒
2. 总人数50000，创建数4000/秒
3. 总人数50000，创建数6000/秒

###### 2、战斗测试：登录->随机在线玩家进行PK（登录高级账号、循环执行）

​	统计消息延迟情况、硬件资源使用情况、服务器消息处理速度

1. 5分钟并发500
2. 5分钟并发600
3. 5分钟并发700

###### 3、注册峰值测试：

1. 2000注册数：低级号

   1. redislv（数据库）内存占用

   2. 2000同时在线，5%的概率随机掉线重连，开启所有功能测试，统计消息延迟情况、硬件资源使用情况、redislv写入超时情况

   3. 服务器重启时间

   4. 客户端在线体验流畅度

      

2. 3000注册数：低级号

   1. redislv（数据库）内存占用

   2. 3000同时在线，5%的概率随机掉线重连，开启所有功能测试，统计消息延迟情况、硬件资源使用情况、redislv写入超时情况

   3. 服务器重启时间

   4. 客户端在线体验流畅度

      

3. 4000注册数：低级号

   1. redislv（数据库）内存占用
   2. 4000同时在线，5%的概率随机掉线重连，开启所有功能测试，统计消息延迟情况、硬件资源使用情况、redislv写入超时情况
   3. 服务器重启时间
   4. 客户端在线体验流畅度
      

4. 5000注册数：低级号

   1. redislv（数据库）内存占用
   2. 5000同时在线，5%的概率随机掉线重连，开启所有功能测试，统计消息延迟情况、硬件资源使用情况、redislv写入超时情况
   3. 服务器重启时间
   4. 客户端在线体验流畅度



##### **五、测试结果**

1. 登录->创建角色->进入游戏（仅心跳无其他交互）

   1. 

   2. | 测试场景 | 风险评估 | 备注 |
      | -------- | -------- | ---- |
      | 2000/秒  |          |      |
      | 4000/秒  |          |      |
      | 6000/秒  |          |      |

      数据分析：



2. 登录->随机在线玩家进行PK（循环执行）
   1. 使用50000注册峰值测试时生成的高级号进行测试的结果:
   2. 数据分析:



3.  注册峰值测试

   1. 2000注册数：

      1. redislv内存占用： 使用info memory查看内存使用，玩家数据内存占用正常，无异常

         | 注册数      | Redislv 内存累计总量(MB/KB) |                   |             |
         | ----------- | --------------------------- | ----------------- | ----------- |
         |             | Used_memory_rss             | used_memory_human | 平均占用/人 |
         | 初始状态    | 266.31M                     | 0.84M             | 0           |
         | 低级号 2000 | 164.90M                     | 34.39M            | 17.18K      |
         |             |                             |                   |             |

      2.  2000同时在线，开启所有功能测试

         性能数据如下表：

         | 协议延迟分布 | 每秒网络带宽(avg) | Redis 超时 | 启动耗时     |
         | ------------ | ----------------- | ---------- | ------------ |
         | 0~500ms      | -4.43Mb ~ 1.11Mb  | 77         | 1.436250531s |

         

      3. 在线体验：使用客户端创角登录正常，功能点击流畅，基本无延迟感

         

   2. 3000注册数：

      1. redislv内存占用： 使用info memory查看内存使用，玩家数据内存占用正常，无异常

         | 注册数      | Redislv 内存累计总量(MB/KB) |                   |             |
         | ----------- | --------------------------- | ----------------- | ----------- |
         |             | Used_memory_rss             | used_memory_human | 平均占用/人 |
         | 初始状态    |                             |                   | 0           |
         | 低级号 2000 | 252.45M                     | 34.76M            | 17.80K      |
         |             |                             |                   |             |

      2. 3000同时在线，开启所有功能测试

         性能数据如下表：

         | 协议延迟分布 | 每秒网络带宽(avg) | Redis 超时 | 启动耗时     |
         | ------------ | ----------------- | ---------- | ------------ |
         | 0~500ms      | -4.43Mb ~ 1.11Mb  | 77         | 1.436250531s |

         

      3. 在线体验：使用客户端创角登录正常，功能点击流畅，基本无延迟感

      

   3. 4000注册数:

      1. redislv内存占用： 使用info memory查看内存使用，玩家数据内存占用正常，无异常

         | 注册数      | Redislv 内存累计总量(MB/KB) |                   |             |
         | ----------- | --------------------------- | ----------------- | ----------- |
         |             | Used_memory_rss             | used_memory_human | 平均占用/人 |
         | 初始状态    |                             |                   | 0           |
         | 低级号 2000 | 252.45M                     | 34.76M            | 17.80K      |
         |             |                             |                   |             |

      2. 4000同时在线，开启所有功能测试

         性能数据如下表：

         | 协议延迟分布 | 每秒网络带宽(avg) | Redis 超时 | 启动耗时     |
         | ------------ | ----------------- | ---------- | ------------ |
         | 0~500ms      | -4.43Mb ~ 1.11Mb  | 77         | 1.436250531s |

         

      3. 在线体验：使用客户端创角登录正常，功能点击流畅，基本无延迟感

      

   4. 5000注册数:

      1. redislv内存占用： 使用info memory查看内存使用，玩家数据内存占用正常，无异常

         | 注册数      | Redislv 内存累计总量(MB/KB) |                   |             |
         | ----------- | --------------------------- | ----------------- | ----------- |
         |             | Used_memory_rss             | used_memory_human | 平均占用/人 |
         | 初始状态    |                             |                   | 0           |
         | 低级号 2000 | 252.45M                     | 34.76M            | 17.80K      |
         |             |                             |                   |             |

      2. 4000同时在线，开启所有功能测试

         性能数据如下表：

         | 协议延迟分布 | 每秒网络带宽(avg) | Redis 超时 | 启动耗时     |
         | ------------ | ----------------- | ---------- | ------------ |
         | 0~500ms      | -4.43Mb ~ 1.11Mb  | 77         | 1.436250531s |

         

      3. 在线体验：使用客户端创角登录正常，功能点击流畅，基本无延迟感

​											

#####**六、压测出现的问题和改进**



###### 	1、问题：





###### 	2、改进：



##### **七、详细测试信息**

###### 登录->创建角色->进入游戏（仅心跳无其他交互）

######战斗测试：登录->随机在线玩家进行PK（登录高级账号、循环执行）

######注册峰值测试

1. 2000注册数，同时在线，5%的概率随机掉线重连，开启所有功能随机测试

2. | 在线人数 | 运行时长 | Redis 写入超时情况 |
   | -------- | -------- | ------------------ |
   | 2000     | 300s     | 77                 |

   请求成功率：

   | 协议正确数 | 协议总数 | 正确比 |
   | ---------- | -------- | ------ |
   | 365272     | 395941   | 92.25% |

   

3. 协议正确率分布：

   ```shell
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:13511,MSG_C2L_GuildModifyInfo                      264     264            0.07%  100.00%
   协议:cmd:12605,MSG_C2L_ArtifactStarUp                         0    3400            0.00%    0.00%
   协议:cmd:13605,MSG_C2L_MirageFight                            0    6570            0.00%    0.00%
   协议:cmd:12905,MSG_C2L_DispatchGetAwards                   2007    2069            0.51%   97.00%
   协议:cmd:11925,MSG_C2L_HeroChangeSave                         1       1            0.00%  100.00%
   协议:cmd:13609,MSG_C2L_MirageSaveAffixes                      0    3402            0.00%    0.00%
   协议:cmd:13303,MSG_C2L_MazeTriggerEvent                    2174    2566            0.55%   84.72%
   协议:cmd:13515,MSG_C2L_GuildModifyNotice                    276     276            0.07%  100.00%
   协议:cmd:11921,MSG_C2L_HeroRevive                          1284    1284            0.32%  100.00%
   协议:cmd:12409,MSG_C2L_ArenaLike                            932    1783            0.24%   52.27%
   协议:cmd:13109,MSG_C2L_EmblemDecompose                     1650    1650            0.42%  100.00%
   协议:cmd:11508,MSG_C2L_FriendDelete                         377    1955            0.10%   19.28%
   协议:cmd:14003,MSG_C2L_TalesChapterFight                   1766    1766            0.45%  100.00%
   协议:cmd:13527,MSG_C2L_GuildQuit                            929     929            0.23%  100.00%
   协议:cmd:13112,MSG_C2L_EmblemBlessing                      1650    1650            0.42%  100.00%
   协议:cmd:14001,MSG_C2L_TalesList                           3911    3911            0.99%  100.00%
   协议:cmd:13715,MSG_C2L_ForestSpeedGrow                      223     223            0.06%  100.00%
   协议:cmd:13309,MSG_C2L_MazeBuyRevive                       3314    3314            0.84%  100.00%
   协议:cmd:13509,MSG_C2L_GuildManagerMember                    34      35            0.01%   97.14%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                           25    2047            0.01%    1.22%
   协议:cmd:13725,MSG_C2L_ForestRevenge                         17      17            0.00%  100.00%
   协议:cmd:13701,MSG_C2L_ForestInfo                         11360   11360            2.87%  100.00%
   协议:cmd:13705,MSG_C2L_ForestFeedGoblin                     814     814            0.21%  100.00%
   协议:cmd:13723,MSG_C2L_ForestLogList                       5336    5336            1.35%  100.00%
   协议:cmd:13513,MSG_C2L_GuildSetName                         238     247            0.06%   96.36%
   协议:cmd:13305,MSG_C2L_MazeRecoveryHero                     690    1584            0.17%   43.56%
   协议:cmd:13501,MSG_C2L_GuildList                           6696    6696            1.69%  100.00%
   协议:cmd:13301,MSG_C2L_MazeGetMap                         11522   11522            2.91%  100.00%
   协议:cmd:12907,MSG_C2L_DispatchRefreshTask                 4481    4481            1.13%  100.00%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                    17      17            0.00%  100.00%
   协议:cmd:13523,MSG_C2L_GuildSendMail                        281     283            0.07%   99.29%
   协议:cmd:12501,MSG_C2L_TowerList                          11414   11414            2.88%  100.00%
   协议:cmd:12609,MSG_C2L_ArtifactForge                       3371    3371            0.85%  100.00%
   协议:cmd:11909,MSG_C2L_HeroBuySlot                         4040    4040            1.02%  100.00%
   协议:cmd:14009,MSG_C2L_TalesEliteFight                      237     237            0.06%  100.00%
   协议:cmd:12701,MSG_C2L_GetGems                            11551   11551            2.92%  100.00%
   协议:cmd:11913,MSG_C2L_HeroDecompose                       1190    1205            0.30%   98.76%
   协议:cmd:13103,MSG_C2L_EmblemWear                          1652    1654            0.42%   99.88%
   协议:cmd:13201,MSG_C2L_GoldBuyGet                         11522   11522            2.91%  100.00%
   协议:cmd:12611,MSG_C2L_ArtifactRevive                      1545    1607            0.39%   96.14%
   协议:cmd:11803,MSG_C2L_DungeonRecvAward                    2862    2905            0.72%   98.52%
   协议:cmd:11905,MSG_C2L_HeroStageUp                            0    2379            0.00%    0.00%
   协议:cmd:13721,MSG_C2L_ForestLoot                           408     687            0.10%   59.39%
   协议:cmd:13307,MSG_C2L_MazeGetGrid                         4142    4142            1.05%  100.00%
   协议:cmd:11901,MSG_C2L_HeroList                           11417   11417            2.88%  100.00%
   协议:cmd:13711,MSG_C2L_ForestUpdateSlot                    1276    1279            0.32%   99.77%
   协议:cmd:12709,MSG_C2L_GemConvert                          3421    3421            0.86%  100.00%
   协议:cmd:13531,MSG_C2L_GuildSearch                         1345    1346            0.34%   99.93%
   协议:cmd:12407,MSG_C2L_ArenaLogList                        2747    2747            0.69%  100.00%
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                     190     192            0.05%   98.96%
   协议:cmd:12703,MSG_C2L_GemWear                             3247    3306            0.82%   98.22%
   协议:cmd:11809,MSG_C2L_Dungeon                            11481   11481            2.90%  100.00%
   协议:cmd:13203,MSG_C2L_GoldBuyGetGold                     13588   13588            3.43%  100.00%
   协议:cmd:12607,MSG_C2L_ArtifactStrength                     912    3364            0.23%   27.11%
   协议:cmd:12601,MSG_C2L_ArtifactList                       11496   11496            2.90%  100.00%
   协议:cmd:12411,MSG_C2L_ArenaRank                           3519    3519            0.89%  100.00%
   协议:cmd:11919,MSG_C2L_HeroBack                             817     817            0.21%  100.00%
   协议:cmd:13603,MSG_C2L_MirageDetail                        3259    3259            0.82%  100.00%
   协议:cmd:14011,MSG_C2L_TalesEliteWipe                        20      20            0.01%  100.00%
   协议:cmd:14007,MSG_C2L_TalesChapterTakeReward               349     349            0.09%  100.00%
   协议:cmd:12705,MSG_C2L_GemCompose                          3313    3313            0.84%  100.00%
   协议:cmd:12505,MSG_C2L_TowerSweep                          6786    6786            1.71%  100.00%
   协议:cmd:12403,MSG_C2L_ArenaRefresh                        2706    2706            0.68%  100.00%
   协议:cmd:13529,MSG_C2L_GuildDisband                         259     259            0.07%  100.00%
   协议:cmd:11807,MSG_C2L_DungeonPreview                      3371    3371            0.85%  100.00%
   协议:cmd:11506,MSG_C2L_FriendConfirm                        852    1178            0.22%   72.33%
   协议:cmd:13517,MSG_C2L_GuildApplyList                       287     287            0.07%  100.00%
   协议:cmd:13535,MSG_C2L_GuildGetDeclaration                 1345    1346            0.34%   99.93%
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus                3234    3245            0.82%   99.66%
   协议:cmd:13533,MSG_C2L_GuildGetMembers                     1672    2457            0.42%   68.05%
   协议:cmd:13525,MSG_C2L_GuildSignIn                          934     934            0.24%  100.00%
   协议:cmd:12707,MSG_C2L_GemDecompose                        3339    3339            0.84%  100.00%
   协议:cmd:13717,MSG_C2L_ForestHarvest                         85      85            0.02%  100.00%
   协议:cmd:12503,MSG_C2L_TowerFight                          6598    6598            1.67%  100.00%
   协议:cmd:13507,MSG_C2L_GuildLogList                        2336    2336            0.59%  100.00%
   协议:cmd:13601,MSG_C2L_MirageList                         11356   11356            2.87%  100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                        84     494            0.02%   17.00%
   协议:cmd:13505,MSG_C2L_GuildCreate                          687     696            0.17%   98.71%
   协议:cmd:13105,MSG_C2L_EmblemLevelUp                       1669    1669            0.42%  100.00%
   协议:cmd:13503,MSG_C2L_GuildGetMyInfo                     14922   14922            3.77%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  2000    2000            0.51%  100.00%
   协议:cmd:11907,MSG_C2L_HeroStarUp                            38      38            0.01%  100.00%
   协议:cmd:12903,MSG_C2L_DispatchReceiveTask                 4343    4542            1.10%   95.62%
   协议:cmd:12401,MSG_C2L_ArenaInfo                          11480   11480            2.90%  100.00%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                      115     225            0.03%   51.11%
   协议:cmd:13519,MSG_C2L_GuildApplyRatify                      12      12            0.00%  100.00%
   协议:cmd:11518,MSG_C2L_FriendRecommend                     3362    3389            0.85%   99.20%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                   7408    7461            1.87%   99.29%
   协议:cmd:13703,MSG_C2L_ForestStartFeed                     1338    1338            0.34%  100.00%
   协议:cmd:12405,MSG_C2L_ArenaFight                          1936    2808            0.49%   68.95%
   协议:cmd:11801,MSG_C2L_DungeonFight                        3563    3563            0.90%  100.00%
   协议:cmd:13713,MSG_C2L_ForestStartPlant                     485     485            0.12%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               2292    2292            0.58%  100.00%
   协议:cmd:13521,MSG_C2L_GuildUserApply                      1962    1981            0.50%   99.04%
   协议:cmd:13107,MSG_C2L_EmblemStageUp                       1663    1663            0.42%  100.00%
   协议:cmd:13116,MSG_C2L_EmblemRevive                         413     413            0.10%  100.00%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                        80     711            0.02%   11.25%
   协议:cmd:11501,MSG_C2L_FriendInfo                         12813   12813            3.24%  100.00%
   协议:cmd:13101,MSG_C2L_EmblemGet                          11465   11465            2.90%  100.00%
   协议:cmd:11520,MSG_C2L_FriendSearch                         360    1175            0.09%   30.64%
   协议:cmd:10100,                                           21236   21236            5.36%  100.00%
   协议:cmd:11029,MSG_C2L_Formation                           3484    3484            0.88%  100.00%
   协议:cmd:13709,MSG_C2L_ForestFeedSpecial                    481     481            0.12%  100.00%
   协议:cmd:14005,MSG_C2L_TalesChapterFinish                  1902    1902            0.48%  100.00%
   协议:cmd:11503,MSG_C2L_FriendAdd                           3354    5275            0.85%   63.58%
   协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward               3244    3244            0.82%  100.00%
   协议:cmd:13719,MSG_C2L_ForestSearch                        1835    1835            0.46%  100.00%
   协议:cmd:12901,MSG_C2L_DispatchTasks                      11486   11486            2.90%  100.00%
   ```

   

4. 协议延迟分布：

   1. robot端：

      1. 协议延迟统计：

         | 延迟范围(ms) | count  |
         | ------------ | ------ |
         | 0~10         | 93079  |
         | 10~20        | 101721 |
         | 20~50        | 144035 |
         | 50~100       | 32837  |
         | 100~200      | 5137   |
         | 200~500      | 1267   |
         | 500~1000     | 1803   |
         | 1000~100000  | 2414   |

      2. 饼图：

         ![proto_delay_2000](./pic/proto_delay_2000.png)

      3. 协议延迟分布：

         ```shell
         协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
         协议:cmd:13511,MSG_C2L_GuildModifyInfo                  23.48%  25.00%  38.26%  11.36%   1.89%   0.00%   0.00%   0.00%
         协议:cmd:12905,MSG_C2L_DispatchGetAwards                21.82%  26.36%  41.41%   8.92%   1.44%   0.05%   0.00%   0.00%
         协议:cmd:11925,MSG_C2L_HeroChangeSave                    0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
         协议:cmd:13303,MSG_C2L_MazeTriggerEvent                 23.09%  28.33%  37.30%   9.66%   1.61%   0.00%   0.00%   0.00%
         协议:cmd:13515,MSG_C2L_GuildModifyNotice                25.36%  30.07%  35.14%   9.42%   0.00%   0.00%   0.00%   0.00%
         协议:cmd:11921,MSG_C2L_HeroRevive                       23.36%  26.40%  38.94%   9.97%   1.32%   0.00%   0.00%   0.00%
         协议:cmd:12409,MSG_C2L_ArenaLike                        23.07%  26.93%  38.95%   9.23%   1.82%   0.00%   0.00%   0.00%
         协议:cmd:13109,MSG_C2L_EmblemDecompose                  24.00%  27.21%  40.24%   7.76%   0.79%   0.00%   0.00%   0.00%
         协议:cmd:11508,MSG_C2L_FriendDelete                     23.08%  25.20%  41.38%   7.69%   2.39%   0.27%   0.00%   0.00%
         协议:cmd:14003,MSG_C2L_TalesChapterFight                17.16%  26.78%  42.64%  11.16%   2.21%   0.06%   0.00%   0.00%
         协议:cmd:13527,MSG_C2L_GuildQuit                        21.31%  23.47%  43.70%  10.12%   1.40%   0.00%   0.00%   0.00%
         协议:cmd:13112,MSG_C2L_EmblemBlessing                   23.64%  28.18%  39.15%   7.52%   1.52%   0.00%   0.00%   0.00%
         协议:cmd:14001,MSG_C2L_TalesList                        22.04%  26.64%  40.14%   9.36%   1.76%   0.05%   0.00%   0.00%
         协议:cmd:13715,MSG_C2L_ForestSpeedGrow                  24.22%  21.52%  40.81%  11.66%   1.79%   0.00%   0.00%   0.00%
         协议:cmd:13309,MSG_C2L_MazeBuyRevive                    24.74%  26.49%  37.60%   9.51%   1.54%   0.12%   0.00%   0.00%
         协议:cmd:13509,MSG_C2L_GuildManagerMember               32.35%  23.53%  26.47%  17.65%   0.00%   0.00%   0.00%   0.00%
         协议:cmd:11903,MSG_C2L_HeroLevelUp                      23.35%  26.40%  34.52%  13.71%   2.03%   0.00%   0.00%   0.00%
         协议:cmd:13725,MSG_C2L_ForestRevenge                    17.65%  29.41%  41.18%   5.88%   5.88%   0.00%   0.00%   0.00%
         协议:cmd:13701,MSG_C2L_ForestInfo                       26.21%  27.30%  36.73%   8.27%   1.46%   0.04%   0.00%   0.00%
         协议:cmd:13705,MSG_C2L_ForestFeedGoblin                 22.24%  25.68%  43.37%   7.37%   1.23%   0.12%   0.00%   0.00%
         协议:cmd:13723,MSG_C2L_ForestLogList                    23.43%  28.13%  38.70%   8.23%   1.46%   0.06%   0.00%   0.00%
         协议:cmd:13513,MSG_C2L_GuildSetName                     26.05%  23.95%  37.39%  11.34%   1.26%   0.00%   0.00%   0.00%
         协议:cmd:13305,MSG_C2L_MazeRecoveryHero                 19.42%  25.22%  43.04%  10.43%   1.88%   0.00%   0.00%   0.00%
         协议:cmd:13501,MSG_C2L_GuildList                        24.78%  27.93%  37.37%   8.50%   1.42%   0.01%   0.00%   0.00%
         协议:cmd:13301,MSG_C2L_MazeGetMap                       25.75%  27.13%  37.29%   8.43%   1.36%   0.03%   0.00%   0.00%
         协议:cmd:12907,MSG_C2L_DispatchRefreshTask              24.32%  26.07%  39.54%   8.61%   1.41%   0.04%   0.00%   0.00%
         协议:cmd:11512,MSG_C2L_FriendRemBlacklist               11.76%  29.41%  58.82%   0.00%   0.00%   0.00%   0.00%   0.00%
         协议:cmd:13523,MSG_C2L_GuildSendMail                    28.47%  23.13%  38.79%   8.54%   1.07%   0.00%   0.00%   0.00%
         协议:cmd:12501,MSG_C2L_TowerList                        26.06%  26.83%  37.71%   8.16%   1.22%   0.04%   0.00%   0.00%
         协议:cmd:12609,MSG_C2L_ArtifactForge                    23.29%  27.94%  38.92%   8.51%   1.31%   0.03%   0.00%   0.00%
         协议:cmd:11909,MSG_C2L_HeroBuySlot                      24.21%  28.29%  37.33%   8.66%   1.49%   0.02%   0.00%   0.00%
         协议:cmd:14009,MSG_C2L_TalesEliteFight                  16.46%  21.52%  50.21%   9.70%   2.11%   0.00%   0.00%   0.00%
         协议:cmd:12701,MSG_C2L_GetGems                          25.86%  27.65%  37.22%   8.13%   1.12%   0.03%   0.00%   0.00%
         协议:cmd:11913,MSG_C2L_HeroDecompose                    25.04%  26.89%  36.97%  10.08%   1.01%   0.00%   0.00%   0.00%
         协议:cmd:13103,MSG_C2L_EmblemWear                       24.52%  27.60%  38.62%   7.93%   1.27%   0.06%   0.00%   0.00%
         协议:cmd:13201,MSG_C2L_GoldBuyGet                       26.74%  27.17%  36.24%   8.44%   1.38%   0.03%   0.00%   0.00%
         协议:cmd:12611,MSG_C2L_ArtifactRevive                   23.43%  27.06%  39.48%   8.28%   1.68%   0.06%   0.00%   0.00%
         协议:cmd:11803,MSG_C2L_DungeonRecvAward                 24.53%  26.55%  37.49%   9.57%   1.68%   0.17%   0.00%   0.00%
         协议:cmd:11905,MSG_C2L_HeroStageUp                      21.76%  23.82%  37.94%  14.12%   1.18%   1.18%   0.00%   0.00%
         协议:cmd:13721,MSG_C2L_ForestLoot                       12.25%  26.47%  47.55%  12.01%   1.72%   0.00%   0.00%   0.00%
         协议:cmd:13307,MSG_C2L_MazeGetGrid                      22.60%  27.52%  39.01%   9.34%   1.40%   0.12%   0.00%   0.00%
         协议:cmd:11901,MSG_C2L_HeroList                         25.64%  27.77%  36.94%   8.49%   1.15%   0.03%   0.00%   0.00%
         协议:cmd:13711,MSG_C2L_ForestUpdateSlot                 21.94%  26.33%  41.07%   9.40%   1.25%   0.00%   0.00%   0.00%
         协议:cmd:12709,MSG_C2L_GemConvert                       25.23%  26.22%  38.56%   8.77%   1.20%   0.03%   0.00%   0.00%
         协议:cmd:13531,MSG_C2L_GuildSearch                      23.20%  26.77%  40.89%   8.18%   0.97%   0.00%   0.00%   0.00%
         协议:cmd:12407,MSG_C2L_ArenaLogList                     22.97%  25.99%  40.15%   9.28%   1.49%   0.11%   0.00%   0.00%
         协议:cmd:11923,MSG_C2L_HeroChangeRandom                 20.53%  24.21%  45.26%   9.47%   0.53%   0.00%   0.00%   0.00%
         协议:cmd:12703,MSG_C2L_GemWear                          23.34%  27.63%  39.08%   8.59%   1.29%   0.06%   0.00%   0.00%
         协议:cmd:11809,MSG_C2L_Dungeon                          26.65%  27.18%  36.29%   8.44%   1.38%   0.06%   0.00%   0.00%
         协议:cmd:12607,MSG_C2L_ArtifactStrength                 23.68%  24.56%  41.34%   9.76%   0.66%   0.00%   0.00%   0.00%
         协议:cmd:13203,MSG_C2L_GoldBuyGetGold                   24.19%  26.60%  38.70%   9.02%   1.43%   0.05%   0.00%   0.00%
         协议:cmd:12601,MSG_C2L_ArtifactList                     26.77%  26.84%  36.64%   8.54%   1.19%   0.02%   0.00%   0.00%
         协议:cmd:12411,MSG_C2L_ArenaRank                        25.15%  28.96%  36.63%   8.16%   1.08%   0.03%   0.00%   0.00%
         协议:cmd:11919,MSG_C2L_HeroBack                         23.26%  26.32%  39.78%   9.42%   1.22%   0.00%   0.00%   0.00%
         协议:cmd:13603,MSG_C2L_MirageDetail                     24.82%  27.06%  38.17%   8.75%   1.10%   0.09%   0.00%   0.00%
         协议:cmd:14011,MSG_C2L_TalesEliteWipe                   25.00%  15.00%  40.00%  15.00%   5.00%   0.00%   0.00%   0.00%
         协议:cmd:14007,MSG_C2L_TalesChapterTakeReward           21.49%  25.21%  39.26%  12.03%   2.01%   0.00%   0.00%   0.00%
         协议:cmd:12705,MSG_C2L_GemCompose                       24.27%  27.01%  39.18%   7.94%   1.51%   0.09%   0.00%   0.00%
         协议:cmd:12505,MSG_C2L_TowerSweep                       25.33%  26.55%  38.00%   8.74%   1.33%   0.04%   0.00%   0.00%
         协议:cmd:12403,MSG_C2L_ArenaRefresh                     23.21%  26.90%  39.50%   8.87%   1.52%   0.00%   0.00%   0.00%
         协议:cmd:13529,MSG_C2L_GuildDisband                     18.92%  23.55%  48.65%   7.72%   1.16%   0.00%   0.00%   0.00%
         协议:cmd:11807,MSG_C2L_DungeonPreview                   24.12%  26.61%  39.48%   8.69%   1.04%   0.06%   0.00%   0.00%
         协议:cmd:11506,MSG_C2L_FriendConfirm                    22.65%  25.35%  41.78%   9.04%   1.06%   0.12%   0.00%   0.00%
         协议:cmd:13517,MSG_C2L_GuildApplyList                   19.51%  28.57%  40.77%   8.71%   2.44%   0.00%   0.00%   0.00%
         协议:cmd:13535,MSG_C2L_GuildGetDeclaration              25.13%  24.83%  39.18%   9.52%   1.34%   0.00%   0.00%   0.00%
         协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus             24.68%  26.59%  38.50%   8.81%   1.42%   0.00%   0.00%   0.00%
         协议:cmd:13533,MSG_C2L_GuildGetMembers                  21.29%  27.92%  41.39%   8.10%   1.26%   0.04%   0.00%   0.00%
         协议:cmd:13525,MSG_C2L_GuildSignIn                      24.73%  25.16%  37.90%  11.03%   1.07%   0.11%   0.00%   0.00%
         协议:cmd:12707,MSG_C2L_GemDecompose                     25.91%  26.33%  38.54%   7.97%   1.26%   0.00%   0.00%   0.00%
         协议:cmd:13717,MSG_C2L_ForestHarvest                    22.35%  30.59%  35.29%   8.24%   3.53%   0.00%   0.00%   0.00%
         协议:cmd:12503,MSG_C2L_TowerFight                       18.70%  29.22%  41.24%   9.38%   1.42%   0.03%   0.00%   0.00%
         协议:cmd:13507,MSG_C2L_GuildLogList                     20.93%  26.67%  42.85%   8.43%   1.11%   0.00%   0.00%   0.00%
         协议:cmd:13601,MSG_C2L_MirageList                       26.02%  27.18%  36.98%   8.45%   1.34%   0.03%   0.00%   0.00%
         协议:cmd:11514,MSG_C2L_FriendSendLike                   19.05%  35.71%  29.76%  13.10%   2.38%   0.00%   0.00%   0.00%
         协议:cmd:13505,MSG_C2L_GuildCreate                      26.64%  27.37%  36.97%   8.15%   0.87%   0.00%   0.00%   0.00%
         协议:cmd:13105,MSG_C2L_EmblemLevelUp                    23.79%  26.48%  39.54%   8.81%   1.38%   0.00%   0.00%   0.00%
         协议:cmd:13503,MSG_C2L_GuildGetMyInfo                   25.77%  26.62%  38.31%   8.15%   1.12%   0.03%   0.00%   0.00%
         协议:cmd:11025,MSG_C2L_GM                                0.15%   0.30%   2.00%   5.25%   4.25%  15.90%  23.70%  48.45%
         协议:cmd:11907,MSG_C2L_HeroStarUp                       21.05%  28.95%  42.11%   7.89%   0.00%   0.00%   0.00%   0.00%
         协议:cmd:12903,MSG_C2L_DispatchReceiveTask              25.60%  26.87%  37.95%   8.34%   1.22%   0.02%   0.00%   0.00%
         协议:cmd:12401,MSG_C2L_ArenaInfo                        26.95%  26.69%  36.63%   8.48%   1.20%   0.04%   0.00%   0.00%
         协议:cmd:11510,MSG_C2L_FriendBlacklist                  17.39%  28.70%  40.87%  10.43%   2.61%   0.00%   0.00%   0.00%
         协议:cmd:13519,MSG_C2L_GuildApplyRatify                 25.00%  25.00%  41.67%   8.33%   0.00%   0.00%   0.00%   0.00%
         协议:cmd:11518,MSG_C2L_FriendRecommend                  25.25%  27.16%  37.69%   9.01%   0.83%   0.06%   0.00%   0.00%
         协议:cmd:11522,MSG_C2L_FriendRequestInfo                25.57%  24.50%  44.47%   4.95%   0.50%   0.01%   0.00%   0.00%
         协议:cmd:13703,MSG_C2L_ForestStartFeed                  27.88%  27.20%  36.62%   7.55%   0.75%   0.00%   0.00%   0.00%
         协议:cmd:12405,MSG_C2L_ArenaFight                       24.85%  25.88%  39.15%   9.30%   0.83%   0.00%   0.00%   0.00%
         协议:cmd:11801,MSG_C2L_DungeonFight                     18.89%  28.94%  41.06%   9.51%   1.54%   0.06%   0.00%   0.00%
         协议:cmd:13713,MSG_C2L_ForestStartPlant                 22.06%  25.15%  41.86%   9.28%   1.65%   0.00%   0.00%   0.00%
         协议:cmd:11003,MSG_C2L_Flush                             4.14%   5.58%   4.80%   4.93%   3.71%  13.92%  20.77%  42.15%
         协议:cmd:13521,MSG_C2L_GuildUserApply                   26.76%  26.71%  37.77%   7.59%   1.17%   0.00%   0.00%   0.00%
         协议:cmd:13107,MSG_C2L_EmblemStageUp                    24.11%  27.24%  37.40%   9.68%   1.38%   0.18%   0.00%   0.00%
         协议:cmd:13116,MSG_C2L_EmblemRevive                     20.34%  23.49%  45.52%   8.72%   1.94%   0.00%   0.00%   0.00%
         协议:cmd:11516,MSG_C2L_FriendRecvLike                   22.50%  32.50%  30.00%  15.00%   0.00%   0.00%   0.00%   0.00%
         协议:cmd:11501,MSG_C2L_FriendInfo                       26.22%  27.01%  37.07%   8.55%   1.13%   0.02%   0.00%   0.00%
         协议:cmd:13101,MSG_C2L_EmblemGet                        26.12%  27.20%  36.78%   8.52%   1.26%   0.10%   0.00%   0.00%
         协议:cmd:11520,MSG_C2L_FriendSearch                     26.94%  28.33%  35.83%   7.50%   1.39%   0.00%   0.00%   0.00%
         协议:cmd:10100,                                         20.04%  25.53%  35.41%   8.59%   1.86%   2.29%   4.02%   2.26%
         协议:cmd:11029,MSG_C2L_Formation                        30.14%  27.96%  34.18%   6.63%   1.09%   0.00%   0.00%   0.00%
         协议:cmd:13709,MSG_C2L_ForestFeedSpecial                21.62%  27.44%  41.79%   7.69%   1.46%   0.00%   0.00%   0.00%
         协议:cmd:14005,MSG_C2L_TalesChapterFinish               21.77%  26.24%  41.17%   9.57%   1.26%   0.00%   0.00%   0.00%
         协议:cmd:11503,MSG_C2L_FriendAdd                        24.36%  23.97%  40.43%  10.17%   1.07%   0.00%   0.00%   0.00%
         协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward            24.85%  26.88%  38.10%   8.57%   1.60%   0.00%   0.00%   0.00%
         协议:cmd:13719,MSG_C2L_ForestSearch                     23.16%  28.99%  38.42%   8.34%   1.04%   0.05%   0.00%   0.00%
         协议:cmd:12901,MSG_C2L_DispatchTasks                    26.04%  27.09%  37.20%   8.18%   1.39%   0.10%   0.00%   0.00%
         ```

         

         

   2. 游戏服端：

      1. 消息堆积统计：

         | 时间                       | 网关 | logic服 | redis db |
         | -------------------------- | ---- | ------- | -------- |
         | 2021/11/16 11:06:43.247335 | 1424 | 86      | 8        |
         | 2021/11/16 11:06:44.149265 | 1430 | 58      | 69       |
         | 2021/11/16 11:06:45.045604 | 1249 | 0       | 0        |

      2. 协议延迟分布：

         | 协议名称                      | 数量 | 0~10(ms) | 10~20(ms) | 20~50(ms) |
         | ----------------------------- | ---- | -------- | --------- | --------- |
         | MSG_C2L_GM                    | 1    | 100%     | 0.00%     | 0.00%     |
         | MSG_C2L_ForestLoot            | 23   | 95.65%   | 4.35%     | 0.00%     |
         | MSG_L2R_SaveGuild             | 1    | 0.00%    | 0.00%     | 100%      |
         | MSG_C2L_TowerFight            | 184  | 98.37%   | 1.63%     | 0.00%     |
         | MSG_C2L_DungeonSpeedRecvAward | 1    | 100.00%  | 0.00%     | 0.00%     |
         | MSG_C2L_ForestRevenge         | 4    | 75.00%   | 25%       | 0.00%     |
         | MSG_C2L_TalesChapterFight     | 11   | 100.00%  | 0.00%     | 0.00%     |
         | MSG_C2L_MazeTriggerEvent      | 2    | 100.00%  | 0.00%     | 0.00%     |
         | MSG_C2L_ArenaFight            | 14   | 100.00%  | 0.00%     | 0.00%     |
         | MSG_R2L_GetUserBattleSnapshot | 2    | 100.00%  | 0.00%     | 0.00%     |
         | MSG_C2L_TalesEliteFight       | 2    | 100.00%  | 0.00%     | 0.00%     |
         | MSG_C2L_DungeonFight          | 23   | 95.65%   | 4.35%     | 0.00%     |

         

      3. pprof profile：

         * Profile

           ```shell
           [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
           File: service
           Type: cpu
           Time: Nov 16, 2021 at 11:09am (CST)
           Duration: 30.09s, Total samples = 14.78s (49.12%)
           Entering interactive mode (type "help" for commands, "o" for options)
           (pprof) top
           Showing nodes accounting for 6490ms, 43.91% of 14780ms total
           Dropped 1000 nodes (cum <= 73.90ms)
           Showing top 10 nodes out of 250
                 flat  flat%   sum%        cum   cum%
               3030ms 20.50% 20.50%     4740ms 32.07%  runtime.scanobject
                840ms  5.68% 26.18%      910ms  6.16%  runtime.findObject
                490ms  3.32% 29.50%      530ms  3.59%  syscall.Syscall
                440ms  2.98% 32.48%     1580ms 10.69%  runtime.mallocgc
                360ms  2.44% 34.91%      360ms  2.44%  runtime.futex
                360ms  2.44% 37.35%      360ms  2.44%  runtime.markBits.isMarked (inline)
                340ms  2.30% 39.65%      340ms  2.30%  runtime.epollwait
                290ms  1.96% 41.61%      350ms  2.37%  runtime.heapBitsSetType
                180ms  1.22% 42.83%      210ms  1.42%  runtime.mapaccess1_fast32
                160ms  1.08% 43.91%     4820ms 32.61%  runtime.gcDrain
           (pprof)
           ```

           

         * heap

           ```shell
           [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
           File: service
           Type: inuse_space
           Time: Nov 16, 2021 at 11:09am (CST)
           Entering interactive mode (type "help" for commands, "o" for options)
           (pprof) top
           Showing nodes accounting for 220.54MB, 49.18% of 448.45MB total
           Dropped 285 nodes (cum <= 2.24MB)
           Showing top 10 nodes out of 160
                 flat  flat%   sum%        cum   cum%
              60.51MB 13.49% 13.49%    70.51MB 15.72%  app/logic/battle.(*OneSkillEnd).run
              38.01MB  8.48% 21.97%    49.51MB 11.04%  app/logic/battle.(*OneActionEnd).run
              27.50MB  6.13% 28.10%       28MB  6.24%  app/logic/battle.(*OneSkillAttackRun).DoSkillHurt
              19.50MB  4.35% 32.45%    23.50MB  5.24%  app/logic/character.newHero
              17.01MB  3.79% 36.24%    17.01MB  3.79%  app/logic/character.(*MazeM).initEventID
                 14MB  3.12% 39.36%    16.50MB  3.68%  app/logic/battle.(*OneSkillAttackRun).DoSkillAddBuff
              12.50MB  2.79% 42.15%    12.50MB  2.79%  app/protos/out/cl.(*FormationInfo).Clone
              11.50MB  2.57% 44.72%    11.50MB  2.57%  app/logic/character.(*Handbook).Add
              10.50MB  2.34% 47.06%    11.50MB  2.56%  app/logic/battle.(*OneSkillAttackRun).DoSkillCure
               9.50MB  2.12% 49.18%     9.50MB  2.12%  app/logic/character.generateGemAttr
           (pprof)

         * allocs

           ```shell
           [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
           File: service
           Type: alloc_space
           Time: Nov 16, 2021 at 11:09am (CST)
           Entering interactive mode (type "help" for commands, "o" for options)
           (pprof) top
           Showing nodes accounting for 1560.89MB, 29.86% of 5226.76MB total
           Dropped 1090 nodes (cum <= 26.13MB)
           Showing top 10 nodes out of 241
                 flat  flat%   sum%        cum   cum%
             256.47MB  4.91%  4.91%   287.01MB  5.49%  app/logic/db/redisop.DbClient.SetUserMCallSKs
             234.58MB  4.49%  9.39%   237.58MB  4.55%  app/logic/battle.NewAttrManager
             232.07MB  4.44% 13.83%   232.07MB  4.44%  strings.(*Builder).WriteString
             151.52MB  2.90% 16.73%   205.53MB  3.93%  fmt.Sprintf
             137.51MB  2.63% 19.36%   190.02MB  3.64%  app/logic/character.(*User).TaskTypeOnEvent
             126.61MB  2.42% 21.79%   138.11MB  2.64%  encoding/xml.(*Decoder).rawToken
             118.51MB  2.27% 24.05%   118.51MB  2.27%  container/list.(*List).insertValue
             106.10MB  2.03% 26.08%   106.10MB  2.03%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
             103.51MB  1.98% 28.06%   164.01MB  3.14%  context.WithDeadline
              94.02MB  1.80% 29.86%   230.54MB  4.41%  github.com/ivanabc/log4go.Logger.intLogf
           (pprof)							
           ```

5. 监控图表：

   1. 游戏服务器

      ![logic_net_2000](./pic/logic_net_2000.png)

      ![logic_2000](./pic/logic_2000.png)

   2. 网关服务器

      ![gateway_mem_2000](./pic/gateway_mem_2000.png)

      ![gateway_cpu_2000](./pic/gateway_cpu_2000.png)

      ![gateway_net_2000](./pic/gateway_net_2000.png)

​			

​	
