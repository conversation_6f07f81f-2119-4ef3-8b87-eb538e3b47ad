## 材料本压测报告


####  - 1000人

1. robot数据

   ```
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh
   2021-11-28 16:55:54 压测数据分析
   压测基础情况
   开始时间：2021/11/28 16:50:44, 结束时间:2021/11/28 16:55:41, 耗时:297(s)
   登录成功机器人总数：1000
   总消息数:299001， 平均每秒消息数:1006
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:12805,MSG_C2L_TrialSweep                        147650  147725           49.22%   99.95%
   协议:cmd:10100,                                            1000    1000            0.33%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               1000    1000            0.33%  100.00%
   协议:cmd:12801,MSG_C2L_TrialGetInfo                        1000    1000            0.33%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  1000    1000            0.33%  100.00%
   协议:cmd:12803,MSG_C2L_TrialFight                        148276  148276           49.43%  100.00%
   协议正确数:299926 协议总数:300001 正确比:99.98%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:12805,MSG_C2L_TrialSweep                        1.01%   2.46%   5.36%  10.01%  19.07%  57.93%   4.16%   0.00%
   协议:cmd:10100,                                         95.00%   5.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                            11.40%  16.50%  48.90%  23.20%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12801,MSG_C2L_TrialGetInfo                     96.70%   3.30%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11025,MSG_C2L_GM                                8.60%  16.30%  47.40%  27.70%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12803,MSG_C2L_TrialFight                        0.75%   2.39%   5.50%   9.96%  19.00%  58.05%   4.33%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. 游戏数据

   ```
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh
   
   --------------------------
   
   所有超时的left
   [2021/11/28 16:40:12.251713] gate(pck/s: 2165 sum: 3686 left:1076) logic db(pck/s: 846 sum: 1400 left:0) redis db(pck/s: 841 sum: 1409 left:23) [2021/11/28 16:40:16.158900] gate(pck/s: 1327 sum: 10537 left:1035) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:17.057869] gate(pck/s: 1575 sum: 12112 left:1460) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:18.266441] gate(pck/s: 1969 sum: 14081 left:1491) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:19.165698] gate(pck/s: 1556 sum: 15637 left:1935) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:20.064715] gate(pck/s: 1640 sum: 17277 left:4295) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:21.273557] gate(pck/s: 4126 sum: 21403 left:2169) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:22.150387] gate(pck/s: 1458 sum: 22861 left:2711) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:23.070940] gate(pck/s: 1676 sum: 24537 left:3035) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:24.249166] gate(pck/s: 2087 sum: 26624 left:2948) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:25.148541] gate(pck/s: 1500 sum: 28124 left:3449) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:26.047278] gate(pck/s: 1656 sum: 29780 left:3793) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:27.264761] gate(pck/s: 2073 sum: 31853 left:3720) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:28.155160] gate(pck/s: 1432 sum: 33285 left:4288) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:29.054234] gate(pck/s: 1518 sum: 34803 left:4770) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:30.262684] gate(pck/s: 2101 sum: 36904 left:6669) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:31.162763] gate(pck/s: 1596 sum: 38500 left:7073) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:32.061027] gate(pck/s: 1985 sum: 40485 left:7088) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:33.269980] gate(pck/s: 3422 sum: 43907 left:5666) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:34.169189] gate(pck/s: 1613 sum: 45520 left:6053) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:35.068324] gate(pck/s: 1433 sum: 46953 left:6621) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:36.246834] gate(pck/s: 2077 sum: 49030 left:6544) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:37.145525] gate(pck/s: 1610 sum: 50640 left:6934) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:38.045437] gate(pck/s: 1516 sum: 52156 left:7418) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:39.253181] gate(pck/s: 1881 sum: 54037 left:7537) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:40.152348] gate(pck/s: 1642 sum: 55679 left:9895) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:41.051526] gate(pck/s: 1664 sum: 57343 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:42.260915] gate(pck/s: 2343 sum: 59686 left:9888) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:43.160346] gate(pck/s: 1772 sum: 61458 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:44.058144] gate(pck/s: 2895 sum: 64353 left:9221) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:45.267453] gate(pck/s: 2903 sum: 67256 left:8319) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:46.165929] gate(pck/s: 1741 sum: 68997 left:8578) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:47.065435] gate(pck/s: 1730 sum: 70727 left:8848) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:48.285761] gate(pck/s: 2124 sum: 72851 left:8724) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:49.173210] gate(pck/s: 1424 sum: 74275 left:9300) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:50.073615] gate(pck/s: 1618 sum: 75893 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:51.250622] gate(pck/s: 1904 sum: 77797 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:52.148674] gate(pck/s: 1623 sum: 79420 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:53.047999] gate(pck/s: 1755 sum: 81175 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:54.257688] gate(pck/s: 2081 sum: 83256 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:55.157771] gate(pck/s: 2782 sum: 86038 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:56.055483] gate(pck/s: 2405 sum: 88443 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:57.263996] gate(pck/s: 2187 sum: 90630 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:58.163629] gate(pck/s: 1723 sum: 92353 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:40:59.062117] gate(pck/s: 1533 sum: 93886 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:41:00.271225] gate(pck/s: 2178 sum: 96064 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 2012 left:0) [2021/11/28 16:41:01.170123] gate(pck/s: 1522 sum: 97586 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 465 sum: 2477 left:0) [2021/11/28 16:41:02.069966] gate(pck/s: 1464 sum: 99050 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 867 sum: 3344 left:0) [2021/11/28 16:41:03.247435] gate(pck/s: 1688 sum: 100738 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 668 sum: 4012 left:0) [2021/11/28 16:41:04.146914] gate(pck/s: 1657 sum: 102395 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:05.045154] gate(pck/s: 1716 sum: 104111 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:06.253698] gate(pck/s: 3442 sum: 107553 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:07.153428] gate(pck/s: 2524 sum: 110077 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:08.052226] gate(pck/s: 1760 sum: 111837 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:09.262030] gate(pck/s: 2282 sum: 114119 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:10.160431] gate(pck/s: 1737 sum: 115856 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:11.059626] gate(pck/s: 1793 sum: 117649 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:12.267948] gate(pck/s: 2112 sum: 119761 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:13.167166] gate(pck/s: 1718 sum: 121479 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:14.066090] gate(pck/s: 1707 sum: 123186 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:15.274980] gate(pck/s: 2288 sum: 125474 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:16.173845] gate(pck/s: 1681 sum: 127155 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:17.073643] gate(pck/s: 2385 sum: 129540 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:18.250801] gate(pck/s: 3258 sum: 132798 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:19.150267] gate(pck/s: 1533 sum: 134331 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:20.048715] gate(pck/s: 1722 sum: 136053 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:21.258226] gate(pck/s: 2193 sum: 138246 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:22.157364] gate(pck/s: 1537 sum: 139783 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:23.055636] gate(pck/s: 1410 sum: 141193 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:24.265271] gate(pck/s: 2064 sum: 143257 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:25.163848] gate(pck/s: 1513 sum: 144770 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:26.063129] gate(pck/s: 1615 sum: 146385 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:27.279765] gate(pck/s: 2085 sum: 148470 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:28.171195] gate(pck/s: 1701 sum: 150171 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:29.071144] gate(pck/s: 2966 sum: 153137 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:30.247670] gate(pck/s: 2316 sum: 155453 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:31.147246] gate(pck/s: 1541 sum: 156994 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:32.046305] gate(pck/s: 1491 sum: 158485 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:33.255565] gate(pck/s: 2066 sum: 160551 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:34.155013] gate(pck/s: 1598 sum: 162149 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:35.053452] gate(pck/s: 1510 sum: 163659 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:36.263238] gate(pck/s: 2000 sum: 165659 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:37.160734] gate(pck/s: 1567 sum: 167226 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:38.059912] gate(pck/s: 1593 sum: 168819 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:39.269346] gate(pck/s: 1838 sum: 170657 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:40.168080] gate(pck/s: 2214 sum: 172871 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:41.067091] gate(pck/s: 3021 sum: 175892 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:42.245993] gate(pck/s: 2352 sum: 178244 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:43.144211] gate(pck/s: 1747 sum: 179991 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:44.043851] gate(pck/s: 1540 sum: 181531 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:45.252032] gate(pck/s: 2281 sum: 183812 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:46.151437] gate(pck/s: 1694 sum: 185506 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:47.050374] gate(pck/s: 1775 sum: 187281 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:48.258783] gate(pck/s: 2189 sum: 189470 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:49.158458] gate(pck/s: 1422 sum: 190892 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:50.057307] gate(pck/s: 1653 sum: 192545 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 4012 left:0) [2021/11/28 16:41:51.266604] gate(pck/s: 3215 sum: 195760 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 465 sum: 4477 left:0) [2021/11/28 16:41:52.165133] gate(pck/s: 2366 sum: 198126 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 775 sum: 5252 left:0) [2021/11/28 16:41:53.063736] gate(pck/s: 1384 sum: 199510 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 706 sum: 5958 left:0) [2021/11/28 16:41:54.273091] gate(pck/s: 2201 sum: 201711 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 54 sum: 6012 left:0) [2021/11/28 16:41:55.172492] gate(pck/s: 1552 sum: 203263 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:41:56.072354] gate(pck/s: 1413 sum: 204676 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:41:57.248875] gate(pck/s: 2055 sum: 206731 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:41:58.148540] gate(pck/s: 1564 sum: 208295 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:41:59.047252] gate(pck/s: 1414 sum: 209709 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:00.257882] gate(pck/s: 2086 sum: 211795 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:01.155104] gate(pck/s: 1583 sum: 213378 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:02.055390] gate(pck/s: 1632 sum: 215010 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:03.264187] gate(pck/s: 3496 sum: 218506 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:04.162517] gate(pck/s: 1911 sum: 220417 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:05.061639] gate(pck/s: 1530 sum: 221947 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:06.270404] gate(pck/s: 2154 sum: 224101 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:07.169000] gate(pck/s: 1595 sum: 225696 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:08.068136] gate(pck/s: 1760 sum: 227456 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:09.246572] gate(pck/s: 2089 sum: 229545 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:10.145172] gate(pck/s: 1678 sum: 231223 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:11.044171] gate(pck/s: 1738 sum: 232961 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:12.253674] gate(pck/s: 2247 sum: 235208 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:13.152146] gate(pck/s: 1751 sum: 236959 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:14.050956] gate(pck/s: 2417 sum: 239376 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:15.260760] gate(pck/s: 3465 sum: 242841 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:16.159048] gate(pck/s: 1700 sum: 244541 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:17.059071] gate(pck/s: 1828 sum: 246369 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:18.266821] gate(pck/s: 2381 sum: 248750 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:19.166739] gate(pck/s: 1442 sum: 250192 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:20.065073] gate(pck/s: 1709 sum: 251901 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:21.243730] gate(pck/s: 1986 sum: 253887 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:22.172818] gate(pck/s: 1623 sum: 255510 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:23.073666] gate(pck/s: 1747 sum: 257257 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:24.258343] gate(pck/s: 2081 sum: 259338 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:25.149647] gate(pck/s: 2780 sum: 262118 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:26.048477] gate(pck/s: 2488 sum: 264606 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:27.257373] gate(pck/s: 2132 sum: 266738 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:28.157830] gate(pck/s: 1606 sum: 268344 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:29.055144] gate(pck/s: 1471 sum: 269815 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:30.264157] gate(pck/s: 1897 sum: 271712 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:31.163281] gate(pck/s: 1544 sum: 273256 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:32.062803] gate(pck/s: 1409 sum: 274665 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:33.270919] gate(pck/s: 1999 sum: 276664 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:34.170038] gate(pck/s: 1524 sum: 278188 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:35.068754] gate(pck/s: 1577 sum: 279765 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:36.247182] gate(pck/s: 1772 sum: 281537 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:37.147728] gate(pck/s: 3086 sum: 284623 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:38.046037] gate(pck/s: 1972 sum: 286595 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:39.253916] gate(pck/s: 2087 sum: 288682 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:40.153843] gate(pck/s: 1661 sum: 290343 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 6012 left:0) [2021/11/28 16:42:41.051932] gate(pck/s: 1376 sum: 291719 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 424 sum: 6436 left:0) [2021/11/28 16:42:42.261653] gate(pck/s: 2033 sum: 293752 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 816 sum: 7252 left:0) [2021/11/28 16:42:43.160642] gate(pck/s: 1575 sum: 295327 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 643 sum: 7895 left:0) [2021/11/28 16:42:44.060354] gate(pck/s: 1647 sum: 296974 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 117 sum: 8012 left:0) [2021/11/28 16:42:45.268112] gate(pck/s: 1975 sum: 298949 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:46.166712] gate(pck/s: 1670 sum: 300619 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:47.065734] gate(pck/s: 1678 sum: 302297 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:48.245386] gate(pck/s: 3184 sum: 305481 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:49.173776] gate(pck/s: 2774 sum: 308255 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:50.074070] gate(pck/s: 1538 sum: 309793 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:51.252110] gate(pck/s: 2074 sum: 311867 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:52.150817] gate(pck/s: 1561 sum: 313428 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:53.049339] gate(pck/s: 1462 sum: 314890 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:54.258361] gate(pck/s: 2136 sum: 317026 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:55.156919] gate(pck/s: 1605 sum: 318631 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:56.056605] gate(pck/s: 1492 sum: 320123 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:57.265253] gate(pck/s: 2147 sum: 322270 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:58.165058] gate(pck/s: 1514 sum: 323784 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:42:59.064500] gate(pck/s: 1277 sum: 325061 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:00.272500] gate(pck/s: 3716 sum: 328777 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:01.170846] gate(pck/s: 1998 sum: 330775 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:02.070265] gate(pck/s: 1504 sum: 332279 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:03.248477] gate(pck/s: 2067 sum: 334346 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:04.147797] gate(pck/s: 1668 sum: 336014 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:05.046817] gate(pck/s: 1727 sum: 337741 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:06.254910] gate(pck/s: 2173 sum: 339914 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:07.154177] gate(pck/s: 1411 sum: 341325 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:08.052747] gate(pck/s: 1566 sum: 342891 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:09.261716] gate(pck/s: 2063 sum: 344954 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:10.161068] gate(pck/s: 1710 sum: 346664 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:11.059669] gate(pck/s: 2294 sum: 348958 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:12.268832] gate(pck/s: 3394 sum: 352352 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:13.167875] gate(pck/s: 1658 sum: 354010 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:14.067437] gate(pck/s: 1581 sum: 355591 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:15.245263] gate(pck/s: 2217 sum: 357808 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:16.143973] gate(pck/s: 1809 sum: 359617 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:17.073957] gate(pck/s: 1664 sum: 361281 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:18.251955] gate(pck/s: 2132 sum: 363413 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:19.151134] gate(pck/s: 1616 sum: 365029 left:9998) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:20.050907] gate(pck/s: 1591 sum: 366620 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8012 left:0) [2021/11/28 16:43:21.259138] gate(pck/s: 2196 sum: 368816 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 1 sum: 8013 left:0) [2021/11/28 16:43:22.158274] gate(pck/s: 2409 sum: 371225 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:23.057421] gate(pck/s: 2864 sum: 374089 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:24.266271] gate(pck/s: 2384 sum: 376473 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:25.165559] gate(pck/s: 1725 sum: 378198 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:26.064450] gate(pck/s: 1649 sum: 379847 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:27.272705] gate(pck/s: 1876 sum: 381723 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:28.172494] gate(pck/s: 1597 sum: 383320 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:29.071777] gate(pck/s: 1451 sum: 384771 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:30.249270] gate(pck/s: 2082 sum: 386853 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 8013 left:0) [2021/11/28 16:43:31.148373] gate(pck/s: 1563 sum: 388416 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 378 sum: 8391 left:0) [2021/11/28 16:43:32.047024] gate(pck/s: 1353 sum: 389769 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 679 sum: 9070 left:100) [2021/11/28 16:43:33.255752] gate(pck/s: 1962 sum: 391731 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 826 sum: 9896 left:0) [2021/11/28 16:43:34.155157] gate(pck/s: 3076 sum: 394807 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 117 sum: 10013 left:0) [2021/11/28 16:43:35.055135] gate(pck/s: 1951 sum: 396758 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:36.263410] gate(pck/s: 2074 sum: 398832 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:37.163230] gate(pck/s: 1460 sum: 400292 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:38.061257] gate(pck/s: 1488 sum: 401780 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:39.270520] gate(pck/s: 2036 sum: 403816 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:40.169201] gate(pck/s: 1537 sum: 405353 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:41.068281] gate(pck/s: 1622 sum: 406975 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:42.247290] gate(pck/s: 1973 sum: 408948 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:43.145706] gate(pck/s: 1485 sum: 410433 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:44.043976] gate(pck/s: 1759 sum: 412192 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:45.253409] gate(pck/s: 3072 sum: 415264 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:46.152898] gate(pck/s: 2791 sum: 418055 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:47.051691] gate(pck/s: 1592 sum: 419647 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:48.259973] gate(pck/s: 2039 sum: 421686 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:49.159167] gate(pck/s: 1581 sum: 423267 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:50.057978] gate(pck/s: 1467 sum: 424734 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:51.267247] gate(pck/s: 1984 sum: 426718 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:52.165881] gate(pck/s: 1649 sum: 428367 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:53.065386] gate(pck/s: 1713 sum: 430080 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:54.244284] gate(pck/s: 1860 sum: 431940 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:55.173085] gate(pck/s: 1729 sum: 433669 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:56.071664] gate(pck/s: 1652 sum: 435321 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:57.250443] gate(pck/s: 4083 sum: 439404 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:58.149277] gate(pck/s: 1796 sum: 441200 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:43:59.048411] gate(pck/s: 1473 sum: 442673 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:00.257541] gate(pck/s: 2299 sum: 444972 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:01.157341] gate(pck/s: 1603 sum: 446575 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:02.055341] gate(pck/s: 1628 sum: 448203 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:03.264680] gate(pck/s: 1973 sum: 450176 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:04.164074] gate(pck/s: 1458 sum: 451634 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:05.062529] gate(pck/s: 1639 sum: 453273 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:06.271351] gate(pck/s: 2184 sum: 455457 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:07.169946] gate(pck/s: 1700 sum: 457157 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:08.069361] gate(pck/s: 2938 sum: 460095 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:09.248387] gate(pck/s: 2810 sum: 462905 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:10.147376] gate(pck/s: 1708 sum: 464613 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:11.044711] gate(pck/s: 1657 sum: 466270 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:12.253838] gate(pck/s: 2199 sum: 468469 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:13.152901] gate(pck/s: 1809 sum: 470278 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:14.051939] gate(pck/s: 1547 sum: 471825 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:15.260738] gate(pck/s: 2416 sum: 474241 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:16.160157] gate(pck/s: 1637 sum: 475878 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:17.058862] gate(pck/s: 1744 sum: 477622 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:18.268812] gate(pck/s: 2494 sum: 480116 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:19.167095] gate(pck/s: 2977 sum: 483093 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:20.065840] gate(pck/s: 1872 sum: 484965 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 10013 left:0) [2021/11/28 16:44:21.244153] gate(pck/s: 2114 sum: 487079 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 378 sum: 10391 left:0) [2021/11/28 16:44:22.174683] gate(pck/s: 1599 sum: 488678 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 725 sum: 11116 left:0) [2021/11/28 16:44:23.073414] gate(pck/s: 1500 sum: 490178 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 715 sum: 11831 left:0) [2021/11/28 16:44:24.251509] gate(pck/s: 1612 sum: 491790 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 182 sum: 12013 left:0) [2021/11/28 16:44:25.151173] gate(pck/s: 1843 sum: 493633 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:26.049389] gate(pck/s: 1626 sum: 495259 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:27.258645] gate(pck/s: 2177 sum: 497436 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:28.157874] gate(pck/s: 1486 sum: 498922 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:29.055904] gate(pck/s: 1447 sum: 500369 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:30.264760] gate(pck/s: 3107 sum: 503476 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:31.163848] gate(pck/s: 2657 sum: 506133 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:32.062726] gate(pck/s: 1784 sum: 507917 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:33.271880] gate(pck/s: 2290 sum: 510207 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:34.171375] gate(pck/s: 1542 sum: 511749 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:35.070062] gate(pck/s: 1617 sum: 513366 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:36.248285] gate(pck/s: 2111 sum: 515477 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:37.147078] gate(pck/s: 1533 sum: 517010 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:38.046372] gate(pck/s: 1645 sum: 518655 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:39.254958] gate(pck/s: 1915 sum: 520570 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:40.153941] gate(pck/s: 1614 sum: 522184 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:41.053676] gate(pck/s: 1630 sum: 523814 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:42.262591] gate(pck/s: 4062 sum: 527876 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:43.161265] gate(pck/s: 1799 sum: 529675 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:44.060591] gate(pck/s: 1808 sum: 531483 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:45.269247] gate(pck/s: 2141 sum: 533624 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:46.168956] gate(pck/s: 1723 sum: 535347 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:47.066739] gate(pck/s: 1713 sum: 537060 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:48.245612] gate(pck/s: 2225 sum: 539285 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:49.145438] gate(pck/s: 1729 sum: 541014 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:50.074677] gate(pck/s: 1578 sum: 542592 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:51.252496] gate(pck/s: 2222 sum: 544814 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:52.151233] gate(pck/s: 2645 sum: 547459 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:53.050240] gate(pck/s: 2679 sum: 550138 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:54.277689] gate(pck/s: 2289 sum: 552427 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:55.158764] gate(pck/s: 1438 sum: 553865 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:56.057048] gate(pck/s: 1640 sum: 555505 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:57.266348] gate(pck/s: 2331 sum: 557836 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:58.164881] gate(pck/s: 1754 sum: 559590 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:44:59.064526] gate(pck/s: 1623 sum: 561213 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:00.272767] gate(pck/s: 2160 sum: 563373 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:01.171725] gate(pck/s: 1671 sum: 565044 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:02.071514] gate(pck/s: 1618 sum: 566662 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:03.249351] gate(pck/s: 3570 sum: 570232 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:04.148777] gate(pck/s: 2414 sum: 572646 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:05.047275] gate(pck/s: 1469 sum: 574115 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:06.255928] gate(pck/s: 2295 sum: 576410 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:07.155398] gate(pck/s: 1668 sum: 578078 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:08.059715] gate(pck/s: 1688 sum: 579766 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:09.263333] gate(pck/s: 2098 sum: 581864 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:10.162117] gate(pck/s: 1548 sum: 583412 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 12013 left:0) [2021/11/28 16:45:11.060680] gate(pck/s: 1637 sum: 585049 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 330 sum: 12343 left:0) [2021/11/28 16:45:12.269744] gate(pck/s: 2179 sum: 587228 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 773 sum: 13116 left:0) [2021/11/28 16:45:13.169389] gate(pck/s: 1700 sum: 588928 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 629 sum: 13745 left:0) [2021/11/28 16:45:14.068425] gate(pck/s: 2382 sum: 591310 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 268 sum: 14013 left:0) [2021/11/28 16:45:15.246360] gate(pck/s: 3344 sum: 594654 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:16.144871] gate(pck/s: 1734 sum: 596388 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:17.044582] gate(pck/s: 1552 sum: 597940 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:18.252981] gate(pck/s: 2226 sum: 600166 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:19.153347] gate(pck/s: 1459 sum: 601625 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:20.050726] gate(pck/s: 1663 sum: 603288 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:21.260960] gate(pck/s: 2066 sum: 605354 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:22.159369] gate(pck/s: 1586 sum: 606940 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:23.058678] gate(pck/s: 1678 sum: 608618 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:24.268152] gate(pck/s: 2183 sum: 610801 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:25.166564] gate(pck/s: 2385 sum: 613186 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:26.065647] gate(pck/s: 2684 sum: 615870 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:27.275022] gate(pck/s: 2143 sum: 618013 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:28.172668] gate(pck/s: 1659 sum: 619672 left:9998) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:29.071896] gate(pck/s: 1606 sum: 621278 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:30.250635] gate(pck/s: 1981 sum: 623259 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:31.149732] gate(pck/s: 1600 sum: 624859 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:32.048144] gate(pck/s: 1536 sum: 626395 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:33.256658] gate(pck/s: 2094 sum: 628489 left:9998) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:34.155991] gate(pck/s: 1720 sum: 630209 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:35.054816] gate(pck/s: 1542 sum: 631751 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:36.264058] gate(pck/s: 2376 sum: 634127 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:37.163188] gate(pck/s: 3264 sum: 637391 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:38.062654] gate(pck/s: 1592 sum: 638983 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:39.270962] gate(pck/s: 1929 sum: 640912 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:40.173934] gate(pck/s: 1597 sum: 642509 left:9998) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:41.068683] gate(pck/s: 1363 sum: 643872 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:42.248431] gate(pck/s: 1795 sum: 645667 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:43.146682] gate(pck/s: 1476 sum: 647143 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:44.045263] gate(pck/s: 1548 sum: 648691 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:45.253987] gate(pck/s: 2297 sum: 650988 left:10000) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:46.154087] gate(pck/s: 1502 sum: 652490 left:9115) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:47.051752] gate(pck/s: 1693 sum: 654183 left:7422) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:48.260800] gate(pck/s: 2775 sum: 656958 left:4647) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 0 sum: 14013 left:0) [2021/11/28 16:45:49.159729] gate(pck/s: 4161 sum: 661119 left:486) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 367 sum: 14380 left:1150)
   
   --------------------------
   
   logic服务器启动时间:1.52463008s
   logic账号总数:5000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100007(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12803( 129)                               82.17%  13.18%   4.65%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric 
    1000 人压测前有测试2000人的，有些数据有影响
   ![](./pic/trial-********-01.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-**************
   File: service
   Type: inuse_objects
   Time: Nov 28, 2021 at 4:53pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 1679224, 80.96% of 2074121 total
   Dropped 150 nodes (cum <= 10370)
   Showing top 20 nodes out of 110
         flat  flat%   sum%        cum   cum%
       213005 10.27% 10.27%     311310 15.01%  app/protos/out/cl.(*GemInfo).Unmarshal
       155657  7.50% 17.77%     199349  9.61%  app/logic/db/redisop.ClClient.GetSomeEmblemInfoByReply
       150744  7.27% 25.04%     462054 22.28%  app/logic/db/redisop.ClClient.GetSomeGemInfoByReply
       139272  6.71% 31.76%     139272  6.71%  app/protos/out/cl.(*FormationInfo).Clone (inline)
       132445  6.39% 38.14%     132445  6.39%  app/logic/character.(*EmblemM).Load
        98305  4.74% 42.88%      98305  4.74%  app/protos/out/cl.(*GemAttr).Unmarshal
        91757  4.42% 47.31%      91757  4.42%  app/logic/character.initHeroFromData
        88263  4.26% 51.56%      88263  4.26%  app/logic/character.(*GemM).Load
        86008  4.15% 55.71%     113315  5.46%  app/protos/in/db.(*ModuleAttr).Unmarshal
        74756  3.60% 59.31%      74756  3.60%  app/protos/out/cl.(*Handbooks).Unmarshal
        71000  3.42% 62.74%     264887 12.77%  app/protos/out/cl.(*Formation).Clone
        66706  3.22% 65.95%      77629  3.74%  app/logic/character.(*ShopM).load
        54615  2.63% 68.58%      54615  2.63%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
        49152  2.37% 70.95%      49152  2.37%  gitlab.qdream.com/kit/sea/skiplist.NewNode
        43692  2.11% 73.06%      43692  2.11%  app/protos/out/cl.(*EmblemInfo).Unmarshal
        40962  1.97% 75.04%      40962  1.97%  app/protos/out/cl.(*Carnival).Unmarshal
        32770  1.58% 76.62%      32770  1.58%  app/logic/db/redisop.ClClient.GetSomeEquipmentByReply
        32770  1.58% 78.20%      32770  1.58%  app/protos/out/cl.(*Medal).Unmarshal
        32768  1.58% 79.78%      32768  1.58%  app/protos/in/db.(*ResourceAttr).Unmarshal
        24577  1.18% 80.96%      24577  1.18%  app/protos/out/cl.(*TaskTypeProgress).Clone
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Nov 28, 2021 at 4:53pm (CST)
   Duration: 30.16s, Total samples = 23.34s (77.38%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 8.63s, 36.98% of 23.34s total
   Dropped 820 nodes (cum <= 0.12s)
   Showing top 20 nodes out of 270
         flat  flat%   sum%        cum   cum%
        1.81s  7.75%  7.75%      2.72s 11.65%  runtime.scanobject
        0.65s  2.78% 10.54%      0.80s  3.43%  runtime.mapaccess1_fast32
        0.64s  2.74% 13.28%      2.62s 11.23%  app/logic/battle.(*PsSkillMgr).TriggerPsSkill
        0.64s  2.74% 16.02%      2.02s  8.65%  runtime.mallocgc
        0.52s  2.23% 18.25%      0.52s  2.23%  runtime.futex
        0.52s  2.23% 20.48%      0.87s  3.73%  runtime.mapiternext
        0.44s  1.89% 22.37%      0.46s  1.97%  runtime.findObject
        0.43s  1.84% 24.21%      0.62s  2.66%  runtime.heapBitsSetType
        0.33s  1.41% 25.62%      1.36s  5.83%  github.com/ivanabc/log4go.Logger.intLogf
        0.33s  1.41% 27.04%      0.98s  4.20%  runtime.mapiterinit
        0.33s  1.41% 28.45%      0.35s  1.50%  syscall.Syscall
        0.25s  1.07% 29.52%      0.27s  1.16%  runtime.heapBitsForAddr (inline)
        0.24s  1.03% 30.55%      0.24s  1.03%  app/protos/out/cl.sovCl
        0.24s  1.03% 31.58%      0.24s  1.03%  runtime.nextFreeFast
        0.23s  0.99% 32.56%      0.25s  1.07%  runtime.(*itabTableType).find
        0.22s  0.94% 33.50%      1.74s  7.46%  app/logic/battle.(*OneSkillAttack).DoSkillHurt
        0.21s   0.9% 34.40%      2.14s  9.17%  app/logic/battle.(*OneSkillAttack).prepare
        0.21s   0.9% 35.30%      0.21s   0.9%  runtime.duffcopy
        0.20s  0.86% 36.16%      0.29s  1.24%  runtime.mapaccess2_fast32
        0.19s  0.81% 36.98%      0.19s  0.81%  runtime.memhash32
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Nov 28, 2021 at 4:53pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 17183.90MB, 64.49% of 26643.81MB total
   Dropped 713 nodes (cum <= 133.22MB)
   Showing top 20 nodes out of 204
         flat  flat%   sum%        cum   cum%
    2580.17MB  9.68%  9.68%  2658.68MB  9.98%  github.com/gogo/protobuf/proto.Marshal
    2408.30MB  9.04% 18.72%  2430.80MB  9.12%  app/logic/battle.newAttrManager
    1988.05MB  7.46% 26.18%  5200.40MB 19.52%  app/logic/battle.(*PsSkillMgr).CastPsSkill
    1314.25MB  4.93% 31.12%  1315.25MB  4.94%  fmt.Sprintf
    1111.03MB  4.17% 35.29%  5234.90MB 19.65%  app/logic/battle.(*PsSkillMgr).DelayExecute
     832.52MB  3.12% 38.41%  7631.99MB 28.64%  app/logic/battle.(*OneSkill).run
     813.61MB  3.05% 41.47%   816.12MB  3.06%  app/logic/character.JSONMarshal
     787.53MB  2.96% 44.42%   787.53MB  2.96%  app/logic/battle.(*OneSkill).SetSkillTargets (inline)
     779.54MB  2.93% 47.35%   781.04MB  2.93%  app/logic/battle.(*BuffManager).checkAddBeforeAdd
     741.58MB  2.78% 50.13%   741.58MB  2.78%  app/logic/character.rebuildReport.func1
     623.03MB  2.34% 52.47%   623.03MB  2.34%  app/logic/battle.(*PsSkillMgr).addPsSkillTriggers
     478.51MB  1.80% 54.26%   754.04MB  2.83%  app/logic/battle.CondDefenseInOwnerTarget
     478.51MB  1.80% 56.06%   478.51MB  1.80%  app/logic/battle.NewPsSkillHalo (inline)
     464.54MB  1.74% 57.80%   464.54MB  1.74%  app/logic/battle.TargetSelfAllNoSelf
     329.51MB  1.24% 59.04%   329.51MB  1.24%  app/logic/battle.NewArgsBuffBeRemove (inline)
     319.52MB  1.20% 60.24%   525.04MB  1.97%  context.WithDeadline
     310.51MB  1.17% 61.41%   310.51MB  1.17%  container/list.(*List).insertValue
     275.53MB  1.03% 62.44%   275.53MB  1.03%  app/logic/battle.(*Team).GetAroundPos
     275.04MB  1.03% 63.47%   316.60MB  1.19%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     272.63MB  1.02% 64.49%   293.13MB  1.10%  encoding/xml.(*Decoder).rawToken
   (pprof)
   ```

#### - 1500人

1. robot数据

   ```
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh
   2021-11-28 17:23:20 压测数据分析
   压测基础情况
   开始时间：2021/11/28 17:18:13, 结束时间:2021/11/28 17:23:10, 耗时:297(s)
   登录成功机器人总数：1500
   总消息数:447892， 平均每秒消息数:1508
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:12805,MSG_C2L_TrialSweep                        221617  221693           49.31%   99.97%
   协议:cmd:10100,                                            1500    1500            0.33%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               1500    1500            0.33%  100.00%
   协议:cmd:12801,MSG_C2L_TrialGetInfo                        1500    1500            0.33%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  1500    1500            0.33%  100.00%
   协议:cmd:12803,MSG_C2L_TrialFight                        221699  221699           49.33%  100.00%
   协议正确数:449316 协议总数:449392 正确比:99.98%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:12805,MSG_C2L_TrialSweep                        0.65%   1.32%   3.94%   6.50%  12.58%  41.45%  33.56%   0.00%
   协议:cmd:10100,                                          0.07%   0.00%   2.20%   4.53%  17.80%  63.47%  11.93%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                             0.20%   0.33%   2.00%   7.53%  13.20%  74.87%   1.87%   0.00%
   协议:cmd:12801,MSG_C2L_TrialGetInfo                     95.93%   4.07%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11025,MSG_C2L_GM                                0.13%   0.40%   1.80%   7.67%  13.00%  75.00%   2.00%   0.00%
   协议:cmd:12803,MSG_C2L_TrialFight                        0.47%   1.27%   3.95%   6.35%  12.69%  41.17%  34.10%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. logic数据

   ```
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh
   
   --------------------------
   
   所有超时的left
   [2021/11/28 17:18:07.141958] gate(pck/s: 2176 sum: 15101 left:1463) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 1511 left:0) [2021/11/28 17:18:17.031442] gate(pck/s: 2082 sum: 31392 left:1673) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 1511 left:0) [2021/11/28 17:18:27.228821] gate(pck/s: 2785 sum: 48503 left:1063) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 1511 left:0) [2021/11/28 17:18:37.118700] gate(pck/s: 2021 sum: 64452 left:1615) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 1511 left:0) [2021/11/28 17:18:47.038587] gate(pck/s: 1910 sum: 80761 left:1807) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 1511 left:0) [2021/11/28 17:19:07.126358] gate(pck/s: 2074 sum: 114060 left:1510) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 3011 left:0) [2021/11/28 17:19:17.045806] gate(pck/s: 2080 sum: 130444 left:1627) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 3011 left:0) [2021/11/28 17:19:20.022967] gate(pck/s: 1158 sum: 135567 left:1004) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 3011 left:0) [2021/11/28 17:19:27.246879] gate(pck/s: 2684 sum: 147481 left:1091) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 3011 left:0) [2021/11/28 17:19:37.134430] gate(pck/s: 2093 sum: 163554 left:1519) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 3011 left:0) [2021/11/28 17:19:47.023308] gate(pck/s: 1942 sum: 179919 left:1655) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 4511 left:0) [2021/11/28 17:19:59.020176] gate(pck/s: 1197 sum: 200056 left:1019) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 4511 left:0) [2021/11/28 17:20:07.119266] gate(pck/s: 1877 sum: 212982 left:1594) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 4511 left:0) [2021/11/28 17:20:17.031473] gate(pck/s: 1734 sum: 229134 left:1943) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 4511 left:0) [2021/11/28 17:20:27.230660] gate(pck/s: 2584 sum: 246311 left:1267) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 4511 left:0) [2021/11/28 17:20:37.119709] gate(pck/s: 2195 sum: 262618 left:1461) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 6011 left:0) [2021/11/28 17:20:47.038810] gate(pck/s: 1831 sum: 278796 left:1784) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 6011 left:0) [2021/11/28 17:21:07.128093] gate(pck/s: 1845 sum: 312008 left:1574) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 6011 left:0) [2021/11/28 17:21:17.046905] gate(pck/s: 2148 sum: 328497 left:1586) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 6011 left:0) [2021/11/28 17:21:27.246556] gate(pck/s: 2832 sum: 345492 left:1092) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 7511 left:0) [2021/11/28 17:21:37.135724] gate(pck/s: 1712 sum: 361349 left:1736) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 7511 left:0) [2021/11/28 17:21:47.024062] gate(pck/s: 2207 sum: 377974 left:1612) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 7512 left:0) [2021/11/28 17:22:07.144295] gate(pck/s: 2197 sum: 411236 left:1352) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 7512 left:0) [2021/11/28 17:22:17.033039] gate(pck/s: 1818 sum: 427338 left:1751) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 9012 left:0) [2021/11/28 17:22:37.120865] gate(pck/s: 2162 sum: 460674 left:1417) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 9012 left:0) [2021/11/28 17:22:47.040362] gate(pck/s: 2150 sum: 477076 left:1516) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 9012 left:0) [2021/11/28 17:22:57.239559] gate(pck/s: 2878 sum: 494150 left:2409) logic db(pck/s: 0 sum: 1500 left:0) redis db(pck/s: 0 sum: 9012 left:0)
   
   --------------------------
   
   logic服务器启动时间:1.56665092s
   logic账号总数:5000
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:       11025(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100007(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12803(  53)                               83.02%  11.32%   5.66%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric

   ![](./pic/trial-********-02.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-********172043
   File: service
   Type: inuse_objects
   Time: Nov 28, 2021 at 5:20pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 2790393, 78.09% of 3573349 total
   Dropped 181 nodes (cum <= 17866)
   Showing top 20 nodes out of 129
         flat  flat%   sum%        cum   cum%
       394651 11.04% 11.04%     394651 11.04%  app/logic/character.(*EquipM).Load
       327685  9.17% 20.21%     327685  9.17%  app/protos/out/cl.(*GemAttr).Unmarshal
       249055  6.97% 27.18%     814322 22.79%  app/logic/db/redisop.ClClient.GetSomeGemInfoByReply
       237582  6.65% 33.83%     565267 15.82%  app/protos/out/cl.(*GemInfo).Unmarshal
       196620  5.50% 39.34%     327698  9.17%  app/logic/db/redisop.ClClient.GetSomeEmblemInfoByReply
       155657  4.36% 43.69%     155657  4.36%  app/protos/out/cl.(*FormationInfo).Clone (inline)
       155270  4.35% 48.04%     155270  4.35%  app/logic/character.(*EmblemM).Load
       131078  3.67% 51.70%     131078  3.67%  app/protos/out/cl.(*EmblemInfo).Unmarshal
       128008  3.58% 55.29%     128008  3.58%  app/protos/out/cl.(*Handbooks).Unmarshal
        99482  2.78% 58.07%     151289  4.23%  app/protos/in/db.(*ModuleAttr).Unmarshal
        91757  2.57% 60.64%      91757  2.57%  app/logic/character.initHeroFromData
        87384  2.45% 63.08%      87384  2.45%  container/list.(*List).insertValue
        84269  2.36% 65.44%     163854  4.59%  app/logic/db/redisop.ClClient.GetSomeHeroBodyByReply
        79585  2.23% 67.67%      79585  2.23%  app/protos/out/cl.(*HeroBody).Unmarshal
        78649  2.20% 69.87%      78649  2.20%  app/logic/db/redisop.ClClient.GetSomeEquipmentByReply
        67006  1.88% 71.75%      67006  1.88%  app/logic/character.(*GemM).Load
        65539  1.83% 73.58%      65539  1.83%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
        60076  1.68% 75.26%     281272  7.87%  app/protos/out/cl.(*Formation).Clone
        57348  1.60% 76.87%      57348  1.60%  app/goxml.(*MonsterInfoManager).Load
        43692  1.22% 78.09%      43692  1.22%  app/logic/character.(*Shop).load
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$  go tool pprof profile-********172043
   File: service
   Type: cpu
   Time: Nov 28, 2021 at 5:20pm (CST)
   Duration: 30.11s, Total samples = 35.13s (116.67%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 13.39s, 38.12% of 35.13s total
   Dropped 886 nodes (cum <= 0.18s)
   Showing top 20 nodes out of 266
         flat  flat%   sum%        cum   cum%
        3.72s 10.59% 10.59%      5.12s 14.57%  runtime.scanobject
        1.03s  2.93% 13.52%      1.66s  4.73%  runtime.mapiternext
        0.91s  2.59% 16.11%      1.19s  3.39%  runtime.mapaccess1_fast32
        0.80s  2.28% 18.39%      3.19s  9.08%  runtime.mallocgc
        0.75s  2.13% 20.52%      3.60s 10.25%  app/logic/battle.(*PsSkillMgr).TriggerPsSkill
        0.68s  1.94% 22.46%      0.75s  2.13%  runtime.findObject
        0.65s  1.85% 24.31%      0.65s  1.85%  runtime.futex
        0.64s  1.82% 26.13%      1.04s  2.96%  runtime.heapBitsSetType
        0.46s  1.31% 27.44%      0.46s  1.31%  runtime.nextFreeFast (inline)
        0.42s  1.20% 28.64%      0.42s  1.20%  app/protos/out/cl.sovCl
        0.41s  1.17% 29.80%      0.41s  1.17%  runtime.markBits.isMarked (inline)
        0.41s  1.17% 30.97%      0.42s  1.20%  syscall.Syscall
        0.38s  1.08% 32.05%      0.41s  1.17%  runtime.heapBitsForAddr (inline)
        0.35s     1% 33.05%      1.38s  3.93%  runtime.mapiterinit
        0.33s  0.94% 33.99%      0.33s  0.94%  runtime.memhash32
        0.32s  0.91% 34.90%      0.32s  0.91%  runtime.duffcopy
        0.29s  0.83% 35.72%      0.42s  1.20%  app/protos/out/cl.(*BattleBaseEffect).Size
        0.28s   0.8% 36.52%      1.99s  5.66%  github.com/ivanabc/log4go.Logger.intLogf
        0.28s   0.8% 37.32%      0.28s   0.8%  runtime.isEmpty (inline)
        0.28s   0.8% 38.12%      0.28s   0.8%  runtime.memclrNoHeapPointers
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-********172043
   File: service
   Type: alloc_space
   Time: Nov 28, 2021 at 5:20pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top 20
   Showing nodes accounting for 5051.12MB, 60.68% of 8324.56MB total
   Dropped 661 nodes (cum <= 41.62MB)
   Showing top 20 nodes out of 213
         flat  flat%   sum%        cum   cum%
     782.28MB  9.40%  9.40%   804.28MB  9.66%  github.com/gogo/protobuf/proto.Marshal
     677.22MB  8.14% 17.53%   680.22MB  8.17%  app/logic/battle.newAttrManager
     551.51MB  6.63% 24.16%  1457.11MB 17.50%  app/logic/battle.(*PsSkillMgr).CastPsSkill
     396.07MB  4.76% 28.92%   397.57MB  4.78%  fmt.Sprintf
     299.01MB  3.59% 32.51%  1453.11MB 17.46%  app/logic/battle.(*PsSkillMgr).DelayExecute
     240.51MB  2.89% 35.40%   240.51MB  2.89%  app/logic/battle.(*OneSkill).SetSkillTargets (inline)
     231.51MB  2.78% 38.18%  2237.64MB 26.88%  app/logic/battle.(*OneSkill).run
     218.94MB  2.63% 40.81%   219.94MB  2.64%  app/logic/character.JSONMarshal
     218.51MB  2.62% 43.43%   219.01MB  2.63%  app/logic/battle.(*BuffManager).checkAddBeforeAdd
     203.12MB  2.44% 45.87%   220.12MB  2.64%  encoding/xml.(*Decoder).rawToken
     199.02MB  2.39% 48.26%   199.02MB  2.39%  app/logic/character.rebuildReport.func1
     177.01MB  2.13% 50.39%   177.01MB  2.13%  app/logic/battle.(*PsSkillMgr).addPsSkillTriggers
        143MB  1.72% 52.11%      143MB  1.72%  app/logic/battle.NewPsSkillHalo (inline)
        138MB  1.66% 53.77%   225.51MB  2.71%  app/logic/battle.CondDefenseInOwnerTarget
     117.01MB  1.41% 55.17%   117.01MB  1.41%  app/logic/battle.TargetSelfAllNoSelf
      94.01MB  1.13% 56.30%    98.01MB  1.18%  gitlab.qdream.com/kit/sea/redis/resp.readBulkStr
      93.37MB  1.12% 57.42%   191.38MB  2.30%  gitlab.qdream.com/kit/sea/redis/resp.readArray
         93MB  1.12% 58.54%       93MB  1.12%  container/list.(*List).insertValue
      90.50MB  1.09% 59.63%    90.50MB  1.09%  app/logic/battle.NewArgsBuffBeRemove (inline)
      87.51MB  1.05% 60.68%    87.51MB  1.05%  app/logic/battle.(*Team).GetAroundPos
   (pprof)
   ```

   


