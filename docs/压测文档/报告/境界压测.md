## 境界压测报告

#### 第一版

没有布阵，全局属性没有加到英雄身上

1. robot数据

   ```
   2021-07-20 10:16:59 压测数据分析
   压测基础情况
   开始时间：2021/07/20 10:10:28, 结束时间:2021/07/20 10:15:22, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:1480000， 平均每秒消息数:5034
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:10100,                                            5000    5000            0.34%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.34%  100.00%
   协议:cmd:13801,MSG_C2L_MemoryLatest                        5000    5000            0.34%  100.00%
   协议:cmd:13803,MSG_C2L_MemoryUnlock                     1400000 1470000           94.28%   95.24%
   协议正确数:1415000 协议总数:1485000 正确比:95.29%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:10100,                                         14.64%  21.42%  54.86%   8.28%   0.80%   0.00%   0.00%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                            95.08%   3.26%   1.66%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13801,MSG_C2L_MemoryLatest                     99.24%   0.42%   0.34%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13803,MSG_C2L_MemoryUnlock                     99.07%   0.74%   0.19%   0.00%   0.00%   0.00%   0.00%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. 游戏数据

   ```
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh
   
   --------------------------
   
   所有超时的left
   [2021/07/20 10:15:23.176688] gate(pck/s: 8680 sum: 1640097 left:0) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 3600 sum: 48702 left:1473)
   
   --------------------------
   
   logic服务器启动时间:1.086705244s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:      100006(   3)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13803(  13)                               69.23%  23.08%   7.69%   0.00%   0.00%   0.00%   0.00%
   协议:       11006(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric
   ![memory](./pic/memory-********-1.png)

4. pprof 

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Jul 20, 2021 at 10:12am (CST)
   Duration: 30.02s, Total samples = 9.55s (31.82%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 3510ms, 36.75% of 9550ms total
   Dropped 281 nodes (cum <= 47.75ms)
   Showing top 10 nodes out of 211
         flat  flat%   sum%        cum   cum%
        760ms  7.96%  7.96%     1360ms 14.24%  runtime.scanobject
        550ms  5.76% 13.72%      620ms  6.49%  syscall.Syscall
        370ms  3.87% 17.59%      370ms  3.87%  runtime.epollwait
        340ms  3.56% 21.15%      440ms  4.61%  runtime.mapiternext
        330ms  3.46% 24.61%      850ms  8.90%  runtime.mallocgc
        300ms  3.14% 27.75%      300ms  3.14%  runtime.futex
        290ms  3.04% 30.79%      310ms  3.25%  runtime.findObject
        200ms  2.09% 32.88%      200ms  2.09%  runtime.markBits.isMarked (inline)
        190ms  1.99% 34.87%      190ms  1.99%  runtime.nanotime
        180ms  1.88% 36.75%      180ms  1.88%  github.com/json-iterator/go.(*Stream).WriteString
   (pprof)
   ```

   

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 20, 2021 at 10:12am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 60.42MB, 42.82% of 141.09MB total
   Dropped 120 nodes (cum <= 0.71MB)
   Showing top 10 nodes out of 151
         flat  flat%   sum%        cum   cum%
         14MB  9.92%  9.92%       20MB 14.18%  app/logic/character.newHero (inline)
       6.51MB  4.61% 14.53%     6.51MB  4.61%  app/logic/helper/monitor.(*Cmds).Add
       6.50MB  4.61% 19.14%     6.50MB  4.61%  app/logic/character.(*Avatar).Add
          6MB  4.25% 23.39%        6MB  4.25%  app/logic/character.initHeroFromData (inline)
       5.50MB  3.90% 27.29%     5.50MB  3.90%  app/logic/character.(*Handbook).Add
       5.50MB  3.90% 31.19%     5.50MB  3.90%  app/protos/out/cl.(*AttrInfo).Clone
       4.50MB  3.19% 34.38%     4.50MB  3.19%  app/logic/character.(*User).mergeResources
       4.41MB  3.12% 37.51%     4.41MB  3.12%  compress/flate.NewWriter
          4MB  2.84% 40.34%       24MB 17.01%  app/logic/character.(*HeroM).Add
       3.50MB  2.48% 42.82%        5MB  3.55%  app/logic/character.(*User).initModule
   (pprof)
   ```

   

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Jul 20, 2021 at 10:12am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 1441.67MB, 57.60% of 2503.10MB total
   Dropped 391 nodes (cum <= 12.52MB)
   Showing top 10 nodes out of 133
         flat  flat%   sum%        cum   cum%
     282.52MB 11.29% 11.29%   444.03MB 17.74%  context.WithDeadline
     277.03MB 11.07% 22.35%   279.53MB 11.17%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     161.51MB  6.45% 28.81%   161.51MB  6.45%  time.AfterFunc
     137.01MB  5.47% 34.28%   137.01MB  5.47%  container/list.(*List).insertValue (inline)
     132.01MB  5.27% 39.55%   132.01MB  5.27%  app/protos/out/cl.(*AttrInfo).Clone
     110.01MB  4.39% 43.95%   112.01MB  4.47%  app/logic/character.(*User).mergeResources
        110MB  4.39% 48.34%   282.51MB 11.29%  app/logic/character.(*User).pushMsg
      79.57MB  3.18% 51.52%    80.07MB  3.20%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
         79MB  3.16% 54.68%  1051.56MB 42.01%  app/logic/command/memory.(*C2LMemoryUnlockCommand).Execute
         73MB  2.92% 57.60%       73MB  2.92%  app/logic/session.(*Client).Process
   (pprof)
   ```



#### 第二版

加上布阵

1. robot数据

   ```
   2021-07-21 14:55:15 压测数据分析
   压测基础情况
   开始时间：2021/07/21 14:50:09, 结束时间:2021/07/21 14:55:04, 耗时:295(s)
   登录成功机器人总数：5000
   总消息数:1485695， 平均每秒消息数:5036
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:10100,                                            5000    5000            0.34%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.34%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                     0    5000            0.00%    0.00%
   协议:cmd:13801,MSG_C2L_MemoryLatest                        5000    5000            0.34%  100.00%
   协议:cmd:13803,MSG_C2L_MemoryUnlock                     1400000 1465695           93.92%   95.52%
   协议:cmd:11029,MSG_C2L_Formation                           5000    5000            0.34%  100.00%
   协议正确数:1420000 协议总数:1490695 正确比:95.26%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:10100,                                         18.70%  23.94%  53.92%   3.44%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                            97.08%   1.66%   1.26%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13801,MSG_C2L_MemoryLatest                     79.52%  10.22%   9.74%   0.52%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:13803,MSG_C2L_MemoryUnlock                     96.61%   2.38%   0.80%   0.20%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11029,MSG_C2L_Formation                        79.86%  10.04%   9.80%   0.30%   0.00%   0.00%   0.00%   0.00%
   
   ---------------------
   
   logic服数情况
   ```

2. 游戏数据

   ```
   
   --------------------------
   
   所有超时的left
   
   --------------------------
   
   logic服务器启动时间:983.011177ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:     event:1(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议: event_10006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       13803(   7)                               14.29%  57.14%  28.57%   0.00%   0.00%   0.00%   0.00%
   协议:  event_1009(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:    event:57(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议: event_10001(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100023(  11)                                0.00%  81.82%  18.18%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric
   ![memory](./pic/memory-********.png)

4. pprof 

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Jul 21, 2021 at 2:52pm (CST)
   Duration: 30s, Total samples = 15.29s (50.97%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 5390ms, 35.25% of 15290ms total
   Dropped 341 nodes (cum <= 76.45ms)
   Showing top 10 nodes out of 214
         flat  flat%   sum%        cum   cum%
       1150ms  7.52%  7.52%     1960ms 12.82%  runtime.scanobject
        730ms  4.77% 12.30%      900ms  5.89%  runtime.mapaccess1_fast32
        650ms  4.25% 16.55%      700ms  4.58%  syscall.Syscall
        630ms  4.12% 20.67%      630ms  4.12%  runtime.epollwait
        540ms  3.53% 24.20%      640ms  4.19%  runtime.mapiternext
        420ms  2.75% 26.95%      470ms  3.07%  runtime.findObject
        410ms  2.68% 29.63%     1280ms  8.37%  runtime.mallocgc
        350ms  2.29% 31.92%      350ms  2.29%  runtime.futex
        270ms  1.77% 33.68%      340ms  2.22%  runtime.heapBitsSetType
        240ms  1.57% 35.25%      250ms  1.64%  runtime.nanotime
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 21, 2021 at 2:52pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 78.01MB, 47.25% of 165.11MB total
   Dropped 67 nodes (cum <= 0.83MB)
   Showing top 10 nodes out of 158
         flat  flat%   sum%        cum   cum%
      18.50MB 11.21% 11.21%       24MB 14.54%  app/logic/character.newHero (inline)
         11MB  6.66% 17.87%       11MB  6.66%  app/logic/character.(*Handbook).Add
          8MB  4.85% 22.72%        8MB  4.85%  app/logic/character.(*Hero).calcTotalAttr
          8MB  4.85% 27.56%        8MB  4.85%  container/list.(*List).insertValue (inline)
          6MB  3.63% 31.20%        6MB  3.63%  app/logic/character.(*User).TaskTypeOnEvent
       5.50MB  3.33% 34.53%     5.50MB  3.33%  app/logic/helper/monitor.(*Cmds).Add
       5.50MB  3.33% 37.86%     5.50MB  3.33%  app/protos/out/cl.(*AttrInfo).Clone
       5.50MB  3.33% 41.19%     5.50MB  3.33%  app/logic/character.initHeroFromData (inline)
       5.50MB  3.33% 44.52%       16MB  9.69%  app/logic/character.(*User).pushMsg
       4.50MB  2.73% 47.25%    28.50MB 17.26%  app/logic/character.(*HeroM).Add
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Jul 21, 2021 at 2:52pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 1992.22MB, 53.64% of 3714.14MB total
   Dropped 393 nodes (cum <= 18.57MB)
   Showing top 10 nodes out of 131
         flat  flat%   sum%        cum   cum%
     328.24MB  8.84%  8.84%   331.24MB  8.92%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     291.02MB  7.84% 16.67%   469.53MB 12.64%  context.WithDeadline
     266.01MB  7.16% 23.84%   266.01MB  7.16%  container/list.(*List).insertValue (inline)
     208.51MB  5.61% 29.45%   507.02MB 13.65%  app/logic/character.(*User).pushMsg
     179.53MB  4.83% 34.28%   353.54MB  9.52%  app/logic/character.(*User).SendSelfToClient
     178.51MB  4.81% 39.09%   178.51MB  4.81%  time.AfterFunc
     154.87MB  4.17% 43.26%   154.87MB  4.17%  app/logic/db/redisop.DbClient.SetSomeCommonRankMCallSKs
     143.51MB  3.86% 47.12%   143.51MB  3.86%  app/protos/out/cl.(*AttrInfo).Clone
     131.01MB  3.53% 50.65%   176.51MB  4.75%  app/logic/character.(*User).TaskTypeOnEvent
        111MB  2.99% 53.64%   618.52MB 16.65%  app/logic/character.(*User).SendCmdToGateway
   (pprof)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-**************
   File: service
   Type: inuse_objects
   Time: Jul 21, 2021 at 2:52pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 1274850, 52.54% of 2426396 total
   Dropped 106 nodes (cum <= 12131)
   Showing top 10 nodes out of 119
         flat  flat%   sum%        cum   cum%
       322236 13.28% 13.28%     394331 16.25%  app/logic/character.newHero
       174768  7.20% 20.48%     174768  7.20%  container/list.(*List).insertValue (inline)
       160260  6.60% 27.09%     160260  6.60%  app/logic/character.(*Handbook).Add
       120153  4.95% 32.04%     404148 16.66%  app/logic/character.(*User).pushMsg
       109227  4.50% 36.54%     207534  8.55%  app/logic/helper/sync.(*MessageSync).append
        82704  3.41% 39.95%      82704  3.41%  app/protos/out/cl.(*AttrInfo).Clone
        79974  3.30% 43.25%      79974  3.30%  app/logic/character.(*User).TaskTypeOnEvent
        76971  3.17% 46.42%      76971  3.17%  app/logic/db/redisop.DbClient.SetSomeCommonRankMCallSKs
        76462  3.15% 49.57%      76462  3.15%  app/logic/character.(*User).CloneNumInfo
        72095  2.97% 52.54%      72095  2.97%  app/logic/character.initHeroFromData (inline)
   (pprof)
   ```

   
