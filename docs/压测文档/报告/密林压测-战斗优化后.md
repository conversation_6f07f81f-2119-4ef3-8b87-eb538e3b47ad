## 密林压测报告

注：

1. MSG_C2L_Formation的超时是人为造成的，结果可以忽略
2. MSG_C2L_ForestLoot战斗协议正确率仅20%，是因业务类型导致无法全时段掠夺

#### 第一版

1. robot数据

   ```
   2021-07-01 19:33:47 压测数据分析
   压测基础情况
   开始时间：2021/07/01 19:28:07, 结束时间:2021/07/01 19:33:02, 耗时:295(s)
   登录成功机器人总数：5000
   总消息数:1478585， 平均每秒消息数:5012
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:13711,MSG_C2L_ForestUpdateSlot                  154336  224536  68.74%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:13713,MSG_C2L_ForestStartPlant                   79830   79830 100.00%
   协议:cmd:13703,MSG_C2L_ForestStartFeed                    81601   81601 100.00%
   协议:cmd:13715,MSG_C2L_ForestSpeedGrow                    78962   78962 100.00%
   协议:cmd:13725,MSG_C2L_ForestRevenge                      52282   52282 100.00%
   协议:cmd:13721,MSG_C2L_ForestLoot                         40612  201830  20.12%
   协议:cmd:13723,MSG_C2L_ForestLogList                     438870  438870 100.00%
   协议:cmd:11029,MSG_C2L_Formation                          10000   10000 100.00%
   协议:cmd:13701,MSG_C2L_ForestInfo                          5000    5000 100.00%
   协议:cmd:13719,MSG_C2L_ForestSearch                       92637   92637 100.00%
   协议:cmd:13717,MSG_C2L_ForestHarvest                      78075   78075 100.00%
   协议:cmd:13709,MSG_C2L_ForestFeedSpecial                  52290   52290 100.00%
   协议:cmd:13705,MSG_C2L_ForestFeedGoblin                   77672   77672 100.00%
   协议正确数:1252167 协议总数:1483585 正确比:84.40%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         14.28%  22.58%  55.54%   6.34%   1.26%   0.00%   0.00% 
   协议:cmd:13711,MSG_C2L_ForestUpdateSlot                 52.08%  25.69%  18.92%   3.16%   0.14%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.84%   1.78%   1.38%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:13713,MSG_C2L_ForestStartPlant                 50.10%  26.40%  20.05%   3.30%   0.15%   0.00%   0.00% 
   协议:cmd:13703,MSG_C2L_ForestStartFeed                  51.31%  25.58%  19.65%   3.37%   0.09%   0.00%   0.00% 
   协议:cmd:13715,MSG_C2L_ForestSpeedGrow                  49.65%  26.70%  20.24%   3.28%   0.13%   0.00%   0.00% 
   协议:cmd:13725,MSG_C2L_ForestRevenge                    37.92%  31.01%  26.68%   4.22%   0.18%   0.00%   0.00% 
   协议:cmd:13721,MSG_C2L_ForestLoot                       45.36%  29.06%  21.85%   3.59%   0.14%   0.00%   0.00% 
   协议:cmd:13723,MSG_C2L_ForestLogList                    52.16%  25.29%  19.20%   3.24%   0.11%   0.00%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%  100.00%   0.00%   0.00% 
   协议:cmd:13701,MSG_C2L_ForestInfo                       100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:13719,MSG_C2L_ForestSearch                     50.35%  26.02%  20.08%   3.44%   0.12%   0.00%   0.00% 
   协议:cmd:13717,MSG_C2L_ForestHarvest                    49.12%  26.56%  20.69%   3.51%   0.13%   0.00%   0.00% 
   协议:cmd:13709,MSG_C2L_ForestFeedSpecial                49.98%  26.44%  20.15%   3.30%   0.14%   0.00%   0.00% 
   协议:cmd:13705,MSG_C2L_ForestFeedGoblin                 50.61%  25.78%  20.16%   3.34%   0.11%   0.00%   0.00%
   ```
   
2. 游戏数据

   ```
   --------------------------
   
   所有超时的left
   
   --------------------------
   
   logic服务器启动时间:925.868447ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   5)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13721(   9)                               88.89%  11.11%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13713(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13723(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13715(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13725(   9)                               77.78%  22.22%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13717(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100048( 184)                                0.00%  96.74%   3.26%   0.00%   0.00%   0.00%   0.00% 
   协议:       13719(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100049(  10)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100003(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       event(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13711(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric
   ![](./pic/forest-********-1.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 1, 2021 at 6:31pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 70.02MB, 39.87% of 175.61MB total
   Dropped 130 nodes (cum <= 0.88MB)
   Showing top 20 nodes out of 295
         flat  flat%   sum%        cum   cum%
        1.52s  7.93%  7.93%      2.72s 14.20%  runtime.scanobject
        0.74s  3.86% 11.80%      0.74s  3.86%  runtime.epollwait
        0.73s  3.81% 15.61%      0.83s  4.33%  runtime.mapaccess1_fast32
        0.64s  3.34% 18.95%      0.67s  3.50%  syscall.Syscall
        0.57s  2.97% 21.92%      0.61s  3.18%  runtime.findObject
        0.55s  2.87% 24.79%      1.31s  6.84%  runtime.mallocgc
        0.48s  2.51% 27.30%      0.48s  2.51%  runtime.futex
        0.37s  1.93% 29.23%      0.37s  1.93%  runtime.markBits.isMarked (inline)
        0.28s  1.46% 30.69%      0.43s  2.24%  runtime.mapiternext
        0.27s  1.41% 32.10%      0.29s  1.51%  app/logic/character.(*User).ResetDaily
        0.25s  1.30% 33.40%      0.32s  1.67%  runtime.heapBitsSetType
        0.24s  1.25% 34.66%      0.25s  1.30%  runtime.nanotime (inline)
        0.24s  1.25% 35.91%         1s  5.22%  runtime.netpoll
        0.22s  1.15% 37.06%      1.41s  7.36%  app/logic/battle.(*OneSkillAttack).DoSkillHurt
        0.21s  1.10% 38.15%      0.21s  1.10%  app/protos/out/cl.sovCl
        0.19s  0.99% 39.14%      1.98s 10.33%  runtime.findrunnable
        0.19s  0.99% 40.14%      0.64s  3.34%  runtime.selectgo
        0.18s  0.94% 41.08%      0.38s  1.98%  app/logic/activity/forest.(*Manager).cleanSearchList
        0.18s  0.94% 42.01%      0.93s  4.85%  gitlab.qdream.com/kit/sea/skiplist.(*Set).GetRangeByScore
        0.16s  0.84% 42.85%      0.16s  0.84%  runtime.unlock
   ```

   

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-********193037 
   File: service
   Type: inuse_space
   Time: Jul 1, 2021 at 7:30pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 110.74MB, 63.22% of 175.17MB total
   Dropped 92 nodes (cum <= 0.88MB)
   Showing top 20 nodes out of 209
         flat  flat%   sum%        cum   cum%
         13MB  7.42%  7.42%       19MB 10.85%  app/logic/character.newHero (inline)
      11.51MB  6.57% 13.99%    11.51MB  6.57%  app/logic/helper/monitor.(*Cmds).Add
          8MB  4.57% 18.56%        8MB  4.57%  app/logic/character.(*Handbook).Add
       7.50MB  4.28% 22.84%     7.50MB  4.28%  app/logic/character.(*Hero).calcTotalAttr
          7MB  4.00% 26.84%        7MB  4.00%  app/logic/character.newShop
          7MB  4.00% 30.83%    13.50MB  7.71%  app/logic/character.newGem
       6.53MB  3.73% 34.56%     6.53MB  3.73%  github.com/gogo/protobuf/proto.Marshal
       6.50MB  3.71% 38.28%     6.50MB  3.71%  app/logic/character.generateGemAttr
          6MB  3.43% 41.70%        6MB  3.43%  app/logic/character.initHeroFromData (inline)
       5.50MB  3.14% 44.84%    24.50MB 13.99%  app/logic/character.(*HeroM).Add
          4MB  2.28% 47.13%        4MB  2.28%  github.com/google/btree.NewFreeList
       3.66MB  2.09% 49.22%     4.66MB  2.66%  app/logic/db.(*RedisActor).Create
       3.53MB  2.01% 51.23%     3.53MB  2.01%  compress/flate.NewWriter
       3.50MB  2.00% 53.23%       17MB  9.71%  app/logic/character.(*GemM).AddGem
       3.50MB  2.00% 55.23%     3.50MB  2.00%  app/logic/character.NewForestLog
       3.50MB  2.00% 57.22%     3.50MB  2.00%  app/logic/character.(*Avatar).Add
          3MB  1.71% 58.94%        3MB  1.71%  app/logic/character.(*User).TaskTypeOnEvent
       2.50MB  1.43% 60.36%        5MB  2.86%  app/logic/character.(*User).initModule
       2.50MB  1.43% 61.79%     2.50MB  1.43%  app/goxml.GenSimpleResource
       2.50MB  1.43% 63.22%     2.50MB  1.43%  container/list.(*List).insertValue
   ```

   

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-********193037
   File: service
   Type: alloc_space
   Time: Jul 1, 2021 at 7:30pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 2.11GB, 54.38% of 3.88GB total
   Dropped 641 nodes (cum <= 0.02GB)
   Showing top 20 nodes out of 193
         flat  flat%   sum%        cum   cum%
       0.34GB  8.74%  8.74%     0.35GB  8.94%  app/logic/db/redisop.ClClient.SetSomeForest
       0.30GB  7.86% 16.60%     0.31GB  7.94%  app/logic/db/redisop.DbClient.SetUserMCallSKs
       0.20GB  5.11% 21.70%     0.31GB  8.08%  context.WithDeadline
       0.17GB  4.32% 26.03%     0.17GB  4.32%  gitlab.qdream.com/kit/sea/skiplist.(*Set).GetRangeByScore
       0.15GB  3.83% 29.86%     0.16GB  4.08%  github.com/gogo/protobuf/proto.Marshal
       0.12GB  3.16% 33.02%     0.12GB  3.16%  container/list.(*List).insertValue (inline)
       0.12GB  2.97% 35.99%     0.12GB  2.97%  time.AfterFunc
       0.08GB  2.15% 38.14%     0.08GB  2.15%  app/logic/session.(*Client).Process
       0.08GB  2.12% 40.25%     0.08GB  2.12%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
       0.07GB  1.91% 42.17%     0.22GB  5.65%  app/logic/character.(*User).pushMsg
       0.07GB  1.81% 43.98%     0.07GB  1.93%  encoding/xml.(*Decoder).rawToken
       0.06GB  1.50% 45.48%     0.06GB  1.50%  strings.(*Builder).WriteString
       0.05GB  1.27% 46.75%     0.07GB  1.81%  app/logic/character.(*User).initModule
       0.05GB  1.26% 48.01%     0.08GB  2.13%  app/protos/out/cl.(*Forest).Clone
       0.05GB  1.22% 49.23%     0.07GB  1.90%  github.com/json-iterator/go.(*frozenConfig).Marshal
       0.04GB  1.15% 50.38%     0.05GB  1.37%  compress/flate.NewWriter
       0.04GB  1.06% 51.44%     0.04GB  1.10%  app/logic/db/redisop.ClClient.SetSomeGemInfoMCallSKs
       0.04GB  1.06% 52.50%     0.04GB  1.11%  app/logic/character.(*User).mergeResources
       0.04GB  0.96% 53.45%     0.25GB  6.57%  app/logic/character.(*User).SendCmdToGateway
       0.04GB  0.93% 54.38%     0.06GB  1.46%  app/logic/character.(*Hero).calcTotalAttr
   ```

   

#### 第二版 - 修改日志管理方式后

1. robot数据

   ```
   2021-07-02 12:34:58 压测数据分析
   压测基础情况
   开始时间：2021/07/02 12:28:54, 结束时间:2021/07/02 12:33:48, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:1482973， 平均每秒消息数:5044
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:13711,MSG_C2L_ForestUpdateSlot                  153779  224377  68.54%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:13713,MSG_C2L_ForestStartPlant                   80217   80217 100.00%
   协议:cmd:13703,MSG_C2L_ForestStartFeed                    81990   81990 100.00%
   协议:cmd:13715,MSG_C2L_ForestSpeedGrow                    79371   79371 100.00%
   协议:cmd:13721,MSG_C2L_ForestLoot                         42228  201458  20.96%
   协议:cmd:13725,MSG_C2L_ForestRevenge                      49940   49941 100.00%
   协议:cmd:13723,MSG_C2L_ForestLogList                     444304  444304 100.00%
   协议:cmd:11029,MSG_C2L_Formation                          10000   10000 100.00%
   协议:cmd:13701,MSG_C2L_ForestInfo                          5000    5000 100.00%
   协议:cmd:13709,MSG_C2L_ForestFeedSpecial                  52619   52619 100.00%
   协议:cmd:13719,MSG_C2L_ForestSearch                       91646   91648 100.00%
   协议:cmd:13705,MSG_C2L_ForestFeedGoblin                   78574   78574 100.00%
   协议:cmd:13717,MSG_C2L_ForestHarvest                      78474   78474 100.00%
   协议正确数:1258142 协议总数:1487973 正确比:84.55%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         16.20%  23.58%  54.14%   5.72%   0.36%   0.00%   0.00% 
   协议:cmd:13711,MSG_C2L_ForestUpdateSlot                 43.29%  24.42%  22.72%   7.75%   1.82%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.54%   1.72%   1.74%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:13713,MSG_C2L_ForestStartPlant                 41.83%  24.46%  23.09%   8.48%   2.14%   0.00%   0.00% 
   协议:cmd:13703,MSG_C2L_ForestStartFeed                  43.19%  23.86%  22.70%   8.23%   2.02%   0.00%   0.00% 
   协议:cmd:13721,MSG_C2L_ForestLoot                       37.03%  26.51%  25.33%   9.01%   2.12%   0.00%   0.00% 
   协议:cmd:13715,MSG_C2L_ForestSpeedGrow                  41.01%  24.70%  23.72%   8.53%   2.04%   0.00%   0.00% 
   协议:cmd:13725,MSG_C2L_ForestRevenge                    31.49%  26.93%  27.78%  10.97%   2.82%   0.00%   0.00% 
   协议:cmd:13723,MSG_C2L_ForestLogList                    43.74%  23.94%  22.46%   8.03%   1.84%   0.00%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%  100.00%   0.00%   0.00% 
   协议:cmd:13701,MSG_C2L_ForestInfo                       92.90%   7.02%   0.08%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:13709,MSG_C2L_ForestFeedSpecial                42.06%  24.35%  23.36%   8.24%   1.99%   0.00%   0.00% 
   协议:cmd:13719,MSG_C2L_ForestSearch                     42.25%  24.11%  23.15%   8.51%   1.98%   0.00%   0.00% 
   协议:cmd:13705,MSG_C2L_ForestFeedGoblin                 42.32%  24.12%  23.23%   8.35%   1.99%   0.00%   0.00% 
   协议:cmd:13717,MSG_C2L_ForestHarvest                    40.42%  25.11%  23.71%   8.62%   2.14%   0.00%   0.00%
   ```
   
2. 游戏数据

   ```
   
   所有超时的left
   [2021/07/02 12:33:49.031178] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:33:49.018665481 +0800 CST msgs count per second: gate(pck/s: 12585 sum: 1643005  left:0) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 1855 sum: 52324 left:3198) 
   [2021/07/02 12:30:08.250242] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:30:08.24528606 +0800 CST msgs count per second: gate(pck/s: 9811 sum: 423002  left:1685) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 30171 left:0) 
   [2021/07/02 12:31:05.227389] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:31:05.218621384 +0800 CST msgs count per second: gate(pck/s: 6262 sum: 734572  left:1450) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 1 sum: 35255 left:0) 
   [2021/07/02 12:31:08.240242] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:31:08.222984235 +0800 CST msgs count per second: gate(pck/s: 9887 sum: 752897  left:1528) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 35259 left:0) 
   [2021/07/02 12:31:38.246697] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:31:38.230989031 +0800 CST msgs count per second: gate(pck/s: 9969 sum: 918092  left:1212) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 40293 left:0) 
   [2021/07/02 12:32:08.219093] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:32:08.218812488 +0800 CST msgs count per second: gate(pck/s: 9655 sum: 1082854  left:1051) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 40325 left:0) 
   [2021/07/02 12:32:20.232134] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:32:20.218727311 +0800 CST msgs count per second: gate(pck/s: 6722 sum: 1149850  left:1215) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 45340 left:0) 
   [2021/07/02 12:32:38.227354] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:32:38.218644535 +0800 CST msgs count per second: gate(pck/s: 8820 sum: 1247174  left:1886) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 45365 left:0) 
   [2021/07/02 12:33:26.343524] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:33:26.218810129 +0800 CST msgs count per second: gate(pck/s: 8587 sum: 1511752  left:1251) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 50440 left:0) 
   [2021/07/02 12:33:38.243120] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-02 12:33:38.218830769 +0800 CST msgs count per second: gate(pck/s: 9945 sum: 1578078  left:1242) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2 sum: 50456 left:0) 
   
   --------------------------
   
   logic服务器启动时间:1.001718233s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       13703(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   5)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13721(   8)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13705(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13715(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13725(  16)                               81.25%  18.75%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13709(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100048( 238)                                0.00%  94.12%   5.88%   0.00%   0.00%   0.00%   0.00% 
   协议:       13719(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11006(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13711(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric
   ![](./pic/forest-20210702-1.png)


