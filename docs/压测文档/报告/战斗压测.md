## 战斗压测报告

1. robot数据

```shell
    压测基础情况
    开始时间：2021/06/30 11:53:59, 结束时间:2021/06/30 12:10:38, 耗时:999(s)
    登录成功机器人总数：1000
    总消息数:1002315， 平均每秒消息数:1003

    协议正确率分布
    协议                                                      正确      总数      正确比   
    协议:cmd:10100,                                            1000    1000 100.00%
    协议:cmd:11053,MSG_C2L_RobotBattle                       998000  998000 100.00%
    协议:cmd:11031,MSG_C2L_GetFormation                        1000    1000 100.00%
    协议:cmd:11003,MSG_C2L_Flush                               1000    1000 100.00%
    协议:cmd:11029,MSG_C2L_Formation                           2315    2315 100.00%
    协议正确数:1003315 协议总数:1003315 正确比:100.00%

    协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
    协议:cmd:10100,                                          2.10%   9.70%  31.50%  53.00%   3.70%   0.00%   0.00% 
    协议:cmd:11053,MSG_C2L_RobotBattle                       1.42%   5.45%  11.94%  17.13%  36.59%  27.46%   0.00% 
    协议:cmd:11031,MSG_C2L_GetFormation                     94.40%   1.00%   4.60%   0.00%   0.00%   0.00%   0.00% 
    协议:cmd:11003,MSG_C2L_Flush                            85.90%   8.60%   5.00%   0.50%   0.00%   0.00%   0.00% 
    协议:cmd:11029,MSG_C2L_Formation                         2.94%   5.66%   9.94%  18.32%  34.00%  29.16%   0.00% 

```

2. 游戏服数据

```shell

    所有超时的left
    [2021/06/30 12:00:28.217256] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:00:28.195754963 +0800 CST msgs count per second: gate(pck/s: 1161 sum: 431118  left:1254) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 8007 left:0) 
    [2021/06/30 12:00:58.225325] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:00:58.195829287 +0800 CST msgs count per second: gate(pck/s: 1152 sum: 464115  left:1260) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 9007 left:0) 
    [2021/06/30 12:01:28.202012] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:01:28.195691078 +0800 CST msgs count per second: gate(pck/s: 1031 sum: 496913  left:1465) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 9007 left:0) 
    [2021/06/30 12:01:58.210577] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:01:58.195724424 +0800 CST msgs count per second: gate(pck/s: 1132 sum: 530005  left:1376) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 10007 left:0) 
    [2021/06/30 12:02:28.218073] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:02:28.195888854 +0800 CST msgs count per second: gate(pck/s: 977 sum: 563131  left:1253) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 11007 left:0) 
    [2021/06/30 12:02:58.226722] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:02:58.196027825 +0800 CST msgs count per second: gate(pck/s: 1240 sum: 596134  left:1253) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 11007 left:0) 
    [2021/06/30 12:03:28.234158] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:03:28.210306865 +0800 CST msgs count per second: gate(pck/s: 1241 sum: 629200  left:1190) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 12007 left:0) 
    [2021/06/30 12:03:58.211468] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:03:58.19581217 +0800 CST msgs count per second: gate(pck/s: 1034 sum: 662056  left:1337) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 12007 left:0) 
    [2021/06/30 12:04:28.198253] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:04:28.19568305 +0800 CST msgs count per second: gate(pck/s: 870 sum: 694858  left:1538) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 13007 left:0) 
    [2021/06/30 12:04:58.196655] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:04:58.195786656 +0800 CST msgs count per second: gate(pck/s: 873 sum: 727910  left:1489) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 14007 left:0) 
    [2021/06/30 12:05:28.204790] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:05:28.200409561 +0800 CST msgs count per second: gate(pck/s: 982 sum: 761001  left:1401) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 14007 left:0) 
    [2021/06/30 12:05:58.212215] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:05:58.195691233 +0800 CST msgs count per second: gate(pck/s: 991 sum: 794018  left:1387) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 15007 left:0) 
    [2021/06/30 12:06:28.220624] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:06:28.196168117 +0800 CST msgs count per second: gate(pck/s: 1098 sum: 827114  left:1294) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 15007 left:0) 
    [2021/06/30 12:06:58.197262] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:06:58.195667589 +0800 CST msgs count per second: gate(pck/s: 955 sum: 860000  left:1411) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 16007 left:0) 
    [2021/06/30 12:07:28.236204] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:07:28.211312225 +0800 CST msgs count per second: gate(pck/s: 1144 sum: 893187  left:1227) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 17007 left:0) 
    [2021/06/30 12:07:58.232305] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:07:58.210779286 +0800 CST msgs count per second: gate(pck/s: 1338 sum: 926296  left:1121) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 17007 left:0) 
    [2021/06/30 12:08:28.221148] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:08:28.195790674 +0800 CST msgs count per second: gate(pck/s: 1009 sum: 959209  left:1211) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 18007 left:0) 
    [2021/06/30 12:08:58.198081] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:08:58.195831394 +0800 CST msgs count per second: gate(pck/s: 1048 sum: 992019  left:1404) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 18008 left:0) 
    [2021/06/30 12:09:28.227876] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:09:28.205729048 +0800 CST msgs count per second: gate(pck/s: 1093 sum: 1025142  left:1284) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 19008 left:0) 
    [2021/06/30 12:09:58.214105] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:09:58.210109285 +0800 CST msgs count per second: gate(pck/s: 1060 sum: 1058074  left:1355) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 20008 left:0) 
    [2021/06/30 12:10:28.223242] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 12:10:28.195696052 +0800 CST msgs count per second: gate(pck/s: 1001 sum: 1091109  left:1323) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 20008 left:0) 
    [2021/06/30 11:54:01.213707] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:54:01.195780175 +0800 CST msgs count per second: gate(pck/s: 369 sum: 3387  left:1094) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 1007 left:0) 
    [2021/06/30 11:54:28.214196] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:54:28.210796714 +0800 CST msgs count per second: gate(pck/s: 1165 sum: 35069  left:1258) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 1007 left:0) 
    [2021/06/30 11:54:58.222268] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:54:58.195759124 +0800 CST msgs count per second: gate(pck/s: 1085 sum: 68140  left:1198) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 2007 left:0) 
    [2021/06/30 11:55:28.199016] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:55:28.195707585 +0800 CST msgs count per second: gate(pck/s: 883 sum: 100857  left:1485) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 2007 left:0) 
    [2021/06/30 11:55:58.207072] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:55:58.195737854 +0800 CST msgs count per second: gate(pck/s: 1049 sum: 133972  left:1373) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 3007 left:0) 
    [2021/06/30 11:56:28.215176] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:56:28.195800689 +0800 CST msgs count per second: gate(pck/s: 1141 sum: 167132  left:1216) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 3007 left:0) 
    [2021/06/30 11:56:58.223702] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:56:58.195708925 +0800 CST msgs count per second: gate(pck/s: 1158 sum: 200109  left:1242) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 4007 left:0) 
    [2021/06/30 11:57:28.200431] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:57:28.195726956 +0800 CST msgs count per second: gate(pck/s: 1110 sum: 232993  left:1361) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 5007 left:0) 
    [2021/06/30 11:57:58.255475] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:57:58.214170839 +0800 CST msgs count per second: gate(pck/s: 1181 sum: 266184  left:1173) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 5007 left:0) 
    [2021/06/30 11:58:28.216714] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:58:28.195692003 +0800 CST msgs count per second: gate(pck/s: 1137 sum: 299074  left:1286) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 6007 left:0) 
    [2021/06/30 11:58:58.224089] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:58:58.199846037 +0800 CST msgs count per second: gate(pck/s: 1064 sum: 332162  left:1201) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 6007 left:0) 
    [2021/06/30 11:59:28.202431] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:59:28.195795025 +0800 CST msgs count per second: gate(pck/s: 1067 sum: 364952  left:1414) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 7007 left:0) 
    [2021/06/30 11:59:58.209055] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-30 11:59:58.19566631 +0800 CST msgs count per second: gate(pck/s: 912 sum: 397931  left:1438) logic db(pck/s: 0 sum: 1000 left:0) redis db(pck/s: 0 sum: 8007 left:0) 



    协议处理超时分布
     协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
    协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
    协议:      100007(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
    协议:       11053(  83)                               68.67%  22.89%   8.43%   0.00%   0.00%   0.00%   0.00% 
    协议:      100002(   2)                                0.00%  50.00%  50.00%   0.00%   0.00%   0.00%   0.00% 
```

3. profile分析

    3.1 cpu
```
    (pprof) top30
Showing nodes accounting for 6.88s, 53.13% of 12.95s total
Dropped 460 nodes (cum <= 0.06s)
Showing top 30 nodes out of 240
      flat  flat%   sum%        cum   cum%
     1.18s  9.11%  9.11%      1.51s 11.66%  runtime.mapaccess1_fast32
     0.85s  6.56% 15.68%      1.34s 10.35%  runtime.scanobject
     0.41s  3.17% 18.84%      0.61s  4.71%  runtime.mapiternext
     0.37s  2.86% 21.70%      0.37s  2.86%  runtime.futex
     0.29s  2.24% 23.94%      0.29s  2.24%  runtime.memhash32
     0.28s  2.16% 26.10%      0.57s  4.40%  runtime.mapassign_fast32
     0.25s  1.93% 28.03%      2.19s 16.91%  app/logic/battle.(*OneSkillAttack).DoSkillHurt
     0.25s  1.93% 29.96%      0.86s  6.64%  runtime.mallocgc
     0.23s  1.78% 31.74%      0.28s  2.16%  runtime.findObject
     0.20s  1.54% 33.28%      0.20s  1.54%  app/protos/out/cl.sovCl
     0.20s  1.54% 34.83%      0.20s  1.54%  runtime.add (inline)
     0.19s  1.47% 36.29%      0.30s  2.32%  runtime.mapaccess2_fast32
     0.18s  1.39% 37.68%      0.18s  1.39%  runtime.isEmpty (inline)
     0.18s  1.39% 39.07%      0.18s  1.39%  runtime.write1
     0.16s  1.24% 40.31%      0.22s  1.70%  app/logic/battle.(*AttrManager).GetAttr
     0.16s  1.24% 41.54%      0.16s  1.24%  app/logic/battle.(*AttrManager).Reset
     0.16s  1.24% 42.78%      0.16s  1.24%  runtime.epollwait
     0.14s  1.08% 43.86%      0.19s  1.47%  runtime.heapBitsSetType
     0.14s  1.08% 44.94%      0.14s  1.08%  runtime.nextFreeFast (inline)
     0.13s  1.00% 45.95%      0.39s  3.01%  runtime.mapiterinit
     0.12s  0.93% 46.87%      0.12s  0.93%  runtime.duffcopy
     0.12s  0.93% 47.80%      0.12s  0.93%  runtime.memclrNoHeapPointers
     0.10s  0.77% 48.57%      0.19s  1.47%  app/logic/battle.(*OneRound).ChooseMember
     0.09s  0.69% 49.27%      0.09s  0.69%  app/logic/battle.(*Manager).IsFinish
     0.09s  0.69% 49.96%      0.33s  2.55%  app/logic/battle.(*Member).GetPassiveSkillEffects
     0.09s  0.69% 50.66%      0.49s  3.78%  app/logic/battle.(*OneSkillAttack).Prepare
     0.09s  0.69% 51.35%      0.62s  4.79%  runtime.newobject (partial-inline)
     0.08s  0.62% 51.97%      0.72s  5.56%  app/logic/battle.(*BuffManager).Reset
     0.08s  0.62% 52.59%      0.23s  1.78%  github.com/ivanabc/log4go.Logger.intLogf
     0.07s  0.54% 53.13%      0.23s  1.78%  app/logic/battle.(*Manager).DoLatePassiveSkills

```

    3.2 memory-alloc
```
    (pprof) top30
Showing nodes accounting for 7.31GB, 77.88% of 9.39GB total
Dropped 468 nodes (cum <= 0.05GB)
Showing top 30 nodes out of 162
      flat  flat%   sum%        cum   cum%
    1.08GB 11.52% 11.52%     1.17GB 12.48%  github.com/gogo/protobuf/proto.Marshal
    0.64GB  6.77% 18.28%     0.64GB  6.77%  app/logic/battle.(*Member).Flush
    0.52GB  5.51% 23.79%     0.52GB  5.51%  app/logic/battle.(*SkillManager).Reset
    0.50GB  5.32% 29.11%     1.33GB 14.20%  app/logic/battle.(*SkillManager).AddNewSkill
    0.36GB  3.82% 32.93%     0.36GB  3.82%  app/logic/battle.(*Team).AltAttr
    0.31GB  3.28% 36.21%     1.80GB 19.23%  app/logic/battle.(*Team).InitMember
    0.31GB  3.26% 39.46%     0.32GB  3.37%  app/logic/battle.(*BuffManager).addBuff
    0.27GB  2.88% 42.34%     0.92GB  9.85%  app/logic/battle.(*SkillManager).AddPassiveSkillEffect
    0.26GB  2.77% 45.12%     0.26GB  2.77%  app/logic/battle.(*BuffManager).Reset
    0.25GB  2.62% 47.74%     0.30GB  3.21%  app/logic/battle.(*Manager).TriggerPSE
    0.23GB  2.45% 50.18%     0.23GB  2.45%  app/logic/battle.(*Report).AddBeHurtSta
    0.21GB  2.22% 52.41%     0.21GB  2.22%  app/logic/battle.(*Manager).AddTriggerMember
    0.20GB  2.13% 54.53%     0.20GB  2.13%  app/logic/battle.(*Member).NewInit
    0.18GB  1.97% 56.50%     0.18GB  1.97%  app/logic/battle.(*Report).AddHurtSta
    0.18GB  1.97% 58.47%     0.18GB  1.97%  internal/reflectlite.Swapper
    0.17GB  1.82% 60.29%     0.30GB  3.18%  app/logic/battle.NewPassiveSkillEffect
    0.16GB  1.75% 62.03%     0.21GB  2.19%  app/logic/battle.NewTeam
    0.16GB  1.70% 63.73%     0.16GB  1.70%  app/logic/battle.(*Member).MakeInitAttr
    0.15GB  1.57% 65.30%     0.36GB  3.79%  app/logic/battle.(*SkillManager).addPassiveSkillEffect
    0.14GB  1.45% 66.74%     0.22GB  2.34%  context.WithDeadline
    0.13GB  1.41% 68.15%     0.13GB  1.41%  app/logic/battle.GetFormationAltAttr
    0.13GB  1.36% 69.51%     0.13GB  1.36%  app/logic/battle.NewPSEDynamic (inline)
    0.12GB  1.27% 70.78%     0.12GB  1.27%  app/logic/battle.(*Report).NewSkill
    0.12GB  1.23% 72.02%     0.17GB  1.78%  app/logic/battle.(*Manager).DoLatePassiveSkills
    0.10GB  1.03% 73.05%     0.10GB  1.03%  app/logic/battle.(*Manager).Init
    0.10GB  1.02% 74.07%     0.44GB  4.69%  app/logic/character.(*User).NewTeam
    0.09GB  0.98% 75.05%     0.11GB  1.21%  compress/flate.NewWriter
    0.09GB  0.98% 76.03%     0.29GB  3.06%  app/logic/character.NewMonsterGroupTeam
    0.09GB  0.96% 76.99%     0.09GB  0.96%  app/protos/out/cl.(*BattleReport).MarshalToSizedBuffer
    0.08GB  0.89% 77.88%     0.08GB  0.89%  time.AfterFunc

```

    3.3 heap-alloc_objects
```
[roobot@172-21-173-36 pprof]$ go tool pprof -alloc_objects heap-************** 
File: service
Type: alloc_objects
Time: Jun 30, 2021 at 12:02pm (CST)
Entering interactive mode (type "help" for commands, "o" for options)
(pprof) top30
Showing nodes accounting for *********, 74.57% of ********* total
Dropped 477 nodes (cum <= 792836)
Showing top 30 nodes out of 153
      flat  flat%   sum%        cum   cum%
  ********  7.30%  7.30%   ********  7.30%  app/logic/battle.(*SkillManager).Reset
   9863314  6.22% 13.52%   ********  8.19%  app/logic/battle.(*SkillManager).addPassiveSkillEffect
   7105839  4.48% 18.00%    7105839  4.48%  app/logic/battle.(*Member).Flush
   6968895  4.39% 22.39%    9123044  5.75%  app/logic/battle.(*Manager).TriggerPSE
   6029468  3.80% 26.19%    6029468  3.80%  internal/reflectlite.Swapper
   5822038  3.67% 29.87%    5822038  3.67%  app/logic/battle.(*BuffManager).Reset
   5341344  3.37% 33.23%    5341344  3.37%  app/logic/battle.(*Member).MakeInitAttr
   4849777  3.06% 36.29%    6205987  3.91%  app/logic/battle.(*Manager).DoLatePassiveSkills
   4784635  3.02% 39.31%   ******** 14.10%  app/logic/battle.(*SkillManager).AddNewSkill
   4467572  2.82% 42.13%    4467572  2.82%  app/logic/battle.(*Member).NewInit
   4292734  2.71% 44.83%    4292734  2.71%  app/logic/battle.NewPSEDynamic (inline)
   3823099  2.41% 47.25%    8115833  5.12%  app/logic/battle.NewPassiveSkillEffect
   3457155  2.18% 49.43%    4577903  2.89%  context.WithDeadline
   3437362  2.17% 51.59%    3437362  2.17%  app/logic/battle.(*Team).AltAttr
   3122524  1.97% 53.56%    3122524  1.97%  app/logic/battle.(*Manager).AddTriggerMember
   3055847  1.93% 55.49%    3055847  1.93%  app/logic/battle.(*Report).AddBeHurtSta
   2930704  1.85% 57.34%    3102353  1.96%  app/logic/battle.(*BuffManager).addBuff
   2593621  1.64% 58.97%   23695292 14.94%  app/logic/battle.(*SkillManager).AddPassiveSkillEffect
   2490403  1.57% 60.54%    2490403  1.57%  app/logic/battle.TargetOpSingal
   2470658  1.56% 62.10%    2470658  1.56%  app/logic/battle.(*Report).AddHurtSta
   2333695  1.47% 63.57%   30180180 19.03%  app/logic/battle.(*Team).InitMember
   2203967  1.39% 64.96%    2400578  1.51%  gitlab.qdream.com/kit/sea/redis/resp.readBulkStr
   2162780  1.36% 66.33%    2162780  1.36%  app/logic/battle.(*Manager).Init
   2150073  1.36% 67.68%    2150073  1.36%  app/logic/battle.GetFormationAltAttr
   2080821  1.31% 69.00%    6045847  3.81%  app/logic/battle.(*Team).GetMaxAttr
   1850145  1.17% 70.16%    2778610  1.75%  app/logic/battle.NewTeam
   1769498  1.12% 71.28%    2359329  1.49%  app/logic/battle.TargetOpSamePos
   1769497  1.12% 72.40%    1769497  1.12%  app/logic/battle.(*PublicHpGroup).DecHp
   1747678  1.10% 73.50%    4833431  3.05%  app/logic/character.(*User).pushMsg
   1703958  1.07% 74.57%    1757212  1.11%  app/logic/battle.(*Manager).GetPassiveTarget

```
