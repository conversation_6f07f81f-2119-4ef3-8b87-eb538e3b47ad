## 水晶压测报告


#### 第一版 - 5000人，使用中等配置机器人，开启英雄，装备，宝石，纹章和水晶五个模块

1. robot数据

   ```
   开始时间：2022/03/02 12:17:58, 结束时间:2022/03/02 12:22:25, 耗时:267(s)
   登录成功机器人总数：5000
   总消息数:1178673， 平均每秒消息数:4414

   --------------------------

   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:11925,MSG_C2L_HeroChangeSave                         4       5            0.00%   80.00%
   协议:cmd:11921,MSG_C2L_HeroRevive                         12451   13845            1.05%   89.93%
   协议:cmd:13109,MSG_C2L_EmblemDecompose                    32952   32952            2.78%  100.00%
   协议:cmd:12301,MSG_C2L_EquipGet                            4999    4999            0.42%  100.00%
   协议:cmd:14701,MSG_C2L_CrystalAddHero                     14810   18141            1.25%   81.64%
   协议:cmd:13112,MSG_C2L_EmblemBlessing                     32781   32781            2.77%  100.00%
   协议:cmd:12309,MSG_C2L_EquipEnchant                       30136   36868            2.55%   81.74%
   协议:cmd:12307,MSG_C2L_EquipRefine                        30730   36449            2.60%   84.31%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                        20101   26031            1.70%   77.22%
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                   4999    4999            0.42%  100.00%
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp             28217   43637            2.38%   64.66%
   协议:cmd:11909,MSG_C2L_HeroBuySlot                        49734   84046            4.20%   59.17%
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement           43746   43746            3.70%  100.00%
   协议:cmd:12701,MSG_C2L_GetGems                             4999    4999            0.42%  100.00%
   协议:cmd:11913,MSG_C2L_HeroDecompose                      17990   24276            1.52%   74.11%
   协议:cmd:13103,MSG_C2L_EmblemWear                         18493   32619            1.56%   56.69%
   协议:cmd:12311,MSG_C2L_EquipEvolution                      1910    8597            0.16%   22.22%
   协议:cmd:11905,MSG_C2L_HeroStageUp                        11236   26341            0.95%   42.66%
   协议:cmd:11901,MSG_C2L_HeroList                            5003    5003            0.42%  100.00%
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                  13095   13095            1.11%  100.00%
   协议:cmd:12709,MSG_C2L_GemConvert                         28245   65588            2.39%   43.06%
   协议:cmd:12703,MSG_C2L_GemWear                            37762   65473            3.19%   57.68%
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                    7912   11919            0.67%   66.38%
   协议:cmd:11919,MSG_C2L_HeroBack                            8701    8701            0.74%  100.00%
   协议:cmd:12313,MSG_C2L_EquipDecompose                     41931   41931            3.54%  100.00%
   协议:cmd:12705,MSG_C2L_GemCompose                         63929   65424            5.40%   97.71%
   协议:cmd:12303,MSG_C2L_EquipWear                          33436   42489            2.82%   78.69%
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                  43277   43277            3.66%  100.00%
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus               46996   59392            3.97%   79.13%
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD                 11510   11954            0.97%   96.29%
   协议:cmd:12707,MSG_C2L_GemDecompose                       65463   65463            5.53%  100.00%
   协议:cmd:12305,MSG_C2L_EquipStrength                          0   36998            0.00%    0.00%
   协议:cmd:13105,MSG_C2L_EmblemLevelUp                      32634   32634            2.76%  100.00%
   协议:cmd:11907,MSG_C2L_HeroStarUp                          4422    6473            0.37%   68.31%
   协议:cmd:11025,MSG_C2L_GM                                  9999    9999            0.84%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.42%  100.00%
   协议:cmd:13107,MSG_C2L_EmblemStageUp                      32922   32922            2.78%  100.00%
   协议:cmd:13116,MSG_C2L_EmblemRevive                       29798   32440            2.52%   91.86%
   协议:cmd:13101,MSG_C2L_EmblemGet                           4999    4999            0.42%  100.00%
   协议:cmd:10100,                                            5000    5000            0.42%  100.00%
   协议:cmd:12315,MSG_C2L_EquipRevive                        38773   42168            3.28%   91.95%
   协议正确数:931095 协议总数:1183673 正确比:78.66%

   --------------------------

   协议延迟分布
      协议名称                                              0-10  10-20  20-50  50-100 100-200 200-************ 1000-100000 
   协议:cmd:11925,MSG_C2L_HeroChangeSave                   100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11921,MSG_C2L_HeroRevive                       97.07%   1.16%   0.93%   0.33%   0.22%   0.25%   0.05%   0.00% 
   协议:cmd:13109,MSG_C2L_EmblemDecompose                  97.81%   1.02%   0.73%   0.19%   0.08%   0.15%   0.03%   0.00% 
   协议:cmd:12301,MSG_C2L_EquipGet                         21.60%   7.78%  11.92%   7.38%   5.18%  13.22%  25.71%   7.20% 
   协议:cmd:14701,MSG_C2L_CrystalAddHero                   97.85%   1.17%   0.64%   0.20%   0.09%   0.05%   0.00%   0.00% 
   协议:cmd:13112,MSG_C2L_EmblemBlessing                   97.81%   0.92%   0.81%   0.20%   0.12%   0.11%   0.02%   0.00% 
   协议:cmd:12309,MSG_C2L_EquipEnchant                     98.99%   0.65%   0.33%   0.02%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12307,MSG_C2L_EquipRefine                      98.91%   0.65%   0.42%   0.02%   0.00%   0.01%   0.00%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                      98.00%   1.05%   0.73%   0.15%   0.03%   0.04%   0.00%   0.00% 
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                22.18%   7.04%  11.76%   8.14%   5.64%  14.38%  23.84%   7.00% 
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp           98.87%   0.61%   0.49%   0.03%   0.00%   0.01%   0.00%   0.00% 
   协议:cmd:11909,MSG_C2L_HeroBuySlot                      96.93%   1.31%   0.96%   0.36%   0.17%   0.21%   0.06%   0.00% 
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement         97.89%   0.96%   0.74%   0.19%   0.10%   0.10%   0.02%   0.00% 
   协议:cmd:11913,MSG_C2L_HeroDecompose                    97.47%   0.98%   0.84%   0.36%   0.14%   0.18%   0.03%   0.00% 
   协议:cmd:12701,MSG_C2L_GetGems                          22.68%   7.68%  11.66%   8.06%   4.50%  12.98%  24.78%   7.64% 
   协议:cmd:13103,MSG_C2L_EmblemWear                       97.24%   1.12%   0.80%   0.42%   0.14%   0.24%   0.03%   0.00% 
   协议:cmd:12311,MSG_C2L_EquipEvolution                   98.85%   0.84%   0.31%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                      97.53%   0.98%   0.77%   0.29%   0.12%   0.25%   0.05%   0.00% 
   协议:cmd:11901,MSG_C2L_HeroList                         23.41%   6.40%  11.67%   8.18%   4.74%  13.33%  25.62%   6.66% 
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                99.13%   0.53%   0.33%   0.01%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12709,MSG_C2L_GemConvert                       99.11%   0.49%   0.39%   0.01%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12703,MSG_C2L_GemWear                          97.42%   1.09%   0.82%   0.31%   0.15%   0.17%   0.05%   0.00% 
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                 97.55%   1.00%   0.80%   0.39%   0.09%   0.15%   0.03%   0.00% 
   协议:cmd:11919,MSG_C2L_HeroBack                         97.71%   1.05%   0.71%   0.23%   0.09%   0.16%   0.05%   0.00% 
   协议:cmd:12313,MSG_C2L_EquipDecompose                   97.92%   0.91%   0.70%   0.21%   0.09%   0.13%   0.04%   0.00% 
   协议:cmd:12705,MSG_C2L_GemCompose                       97.77%   1.01%   0.72%   0.21%   0.12%   0.14%   0.03%   0.00% 
   协议:cmd:12303,MSG_C2L_EquipWear                        97.62%   1.03%   0.82%   0.25%   0.10%   0.14%   0.04%   0.00% 
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                97.87%   1.01%   0.68%   0.19%   0.11%   0.10%   0.03%   0.00% 
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus             97.71%   0.95%   0.79%   0.27%   0.11%   0.14%   0.03%   0.00% 
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD               98.99%   0.56%   0.43%   0.02%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12707,MSG_C2L_GemDecompose                     98.04%   0.93%   0.62%   0.20%   0.08%   0.12%   0.02%   0.00% 
   协议:cmd:13105,MSG_C2L_EmblemLevelUp                    97.84%   0.94%   0.76%   0.16%   0.09%   0.17%   0.03%   0.00% 
   协议:cmd:11907,MSG_C2L_HeroStarUp                       97.54%   1.27%   0.84%   0.29%   0.02%   0.05%   0.00%   0.00% 
   协议:cmd:11025,MSG_C2L_GM                               11.28%   3.64%   5.84%   3.87%   2.74%   7.58%  14.11%  50.93% 
   协议:cmd:11003,MSG_C2L_Flush                             0.04%   0.08%   0.08%   0.12%   0.60%   1.90%   2.58%  94.60% 
   协议:cmd:13107,MSG_C2L_EmblemStageUp                    97.87%   0.94%   0.76%   0.19%   0.11%   0.11%   0.02%   0.00% 
   协议:cmd:13116,MSG_C2L_EmblemRevive                     97.59%   0.98%   0.89%   0.22%   0.14%   0.13%   0.04%   0.00% 
   协议:cmd:13101,MSG_C2L_EmblemGet                        22.38%   7.32%  12.08%   7.64%   4.88%  13.46%  24.72%   7.50% 
   协议:cmd:10100,                                          0.24%   0.02%   0.32%   0.18%   0.94%   2.22%   6.84%  89.24% 
   协议:cmd:12315,MSG_C2L_EquipRevive                      97.79%   1.03%   0.68%   0.24%   0.10%   0.13%   0.03%   0.00%
   ```
   
2. 游戏数据

   ```
   所有超时的left
   [2022/03/02 12:17:33.224269] gate(pck/s: 984 sum: 4919 left:4794) logic db(pck/s: 958 sum: 4953 left:548) redis db(pck/s: 1299 sum: 6614 left:0) [2022/03/02 12:17:34.128181] gate(pck/s: 729 sum: 5648 left:4900) logic db(pck/s: 827 sum: 5780 left:631) redis db(pck/s: 1025 sum: 7639 left:0) [2022/03/02 12:17:35.020683] gate(pck/s: 502 sum: 6150 left:4806) logic db(pck/s: 493 sum: 6273 left:627) redis db(pck/s: 641 sum: 8280 left:0) [2022/03/02 12:17:36.230088] gate(pck/s: 703 sum: 6853 left:4755) logic db(pck/s: 626 sum: 6899 left:601) redis db(pck/s: 803 sum: 9083 left:0) [2022/03/02 12:17:37.132929] gate(pck/s: 894 sum: 7747 left:6164) logic db(pck/s: 865 sum: 7764 left:800) redis db(pck/s: 1212 sum: 10295 left:0) [2022/03/02 12:17:38.033269] gate(pck/s: 415 sum: 8162 left:6119) logic db(pck/s: 445 sum: 8209 left:815) redis db(pck/s: 568 sum: 10863 left:0) [2022/03/02 12:17:39.240350] gate(pck/s: 687 sum: 8849 left:6146) logic db(pck/s: 633 sum: 8842 left:758) redis db(pck/s: 771 sum: 11634 left:0) [2022/03/02 12:17:40.137766] gate(pck/s: 312 sum: 9161 left:6100) logic db(pck/s: 323 sum: 9165 left:625) redis db(pck/s: 347 sum: 11981 left:0) [2022/03/02 12:17:41.039165] gate(pck/s: 256 sum: 9417 left:6076) logic db(pck/s: 297 sum: 9462 left:509) redis db(pck/s: 310 sum: 12291 left:0) [2022/03/02 12:17:42.249886] gate(pck/s: 350 sum: 9767 left:6368) logic db(pck/s: 350 sum: 9812 left:188) redis db(pck/s: 204 sum: 12495 left:0) [2022/03/02 12:17:43.144751] gate(pck/s: 225 sum: 9992 left:6487) logic db(pck/s: 172 sum: 9984 left:16) redis db(pck/s: 114 sum: 12609 left:0) [2022/03/02 12:17:44.042352] gate(pck/s: 219 sum: 10211 left:6301) logic db(pck/s: 16 sum: 10000 left:0) redis db(pck/s: 110 sum: 12719 left:0) [2022/03/02 12:17:45.221463] gate(pck/s: 429 sum: 10640 left:5872) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 215 sum: 12934 left:0) 
   [2022/03/02 12:17:46.122117] gate(pck/s: 323 sum: 10963 left:5549) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 163 sum: 13097 left:0) 
   [2022/03/02 12:17:47.049868] gate(pck/s: 290 sum: 11253 left:8294) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 146 sum: 13243 left:0) 
   [2022/03/02 12:17:48.228351] gate(pck/s: 361 sum: 11614 left:7933) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 182 sum: 13425 left:0) 
   [2022/03/02 12:17:49.125485] gate(pck/s: 1331 sum: 12945 left:6602) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 94 sum: 13519 left:0) 
   [2022/03/02 12:17:50.030616] gate(pck/s: 596 sum: 13541 left:6006) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 121 sum: 13640 left:0) 
   [2022/03/02 12:17:51.238653] gate(pck/s: 358 sum: 13899 left:5648) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 180 sum: 13820 left:0) 
   [2022/03/02 12:17:52.133557] gate(pck/s: 188 sum: 14087 left:5460) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 95 sum: 13915 left:0) 
   [2022/03/02 12:17:53.035489] gate(pck/s: 286 sum: 14373 left:5174) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 145 sum: 14060 left:0) 
   [2022/03/02 12:17:54.247472] gate(pck/s: 378 sum: 14751 left:4797) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 190 sum: 14250 left:0) 
   [2022/03/02 12:17:55.139490] gate(pck/s: 316 sum: 15067 left:4481) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 159 sum: 14409 left:0) 
   [2022/03/02 12:17:56.039799] gate(pck/s: 328 sum: 15395 left:4153) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 165 sum: 14574 left:0) 
   [2022/03/02 12:17:57.220874] gate(pck/s: 416 sum: 15811 left:8280) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 209 sum: 14783 left:0) 
   [2022/03/02 12:17:58.150683] gate(pck/s: 320 sum: 16131 left:7960) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 160 sum: 14943 left:0) 
   [2022/03/02 12:17:59.050425] gate(pck/s: 300 sum: 16431 left:7660) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 151 sum: 15094 left:0) 
   [2022/03/02 12:18:00.223888] gate(pck/s: 7963 sum: 24394 left:4256) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 151 sum: 15245 left:2) 
   [2022/03/02 12:18:01.122571] gate(pck/s: 6114 sum: 30508 left:3552) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 2059 sum: 17304 left:6) 
   [2022/03/02 12:22:27.226319] gate(pck/s: 4397 sum: 1327163 left:853) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1193 sum: 51592 left:3043) 
   [2022/03/02 12:22:28.125359] gate(pck/s: 853 sum: 1328016 left:0) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 2197 sum: 53789 left:1708) 

   --------------------------

   logic服务器启动时间:
   logic账号总数:

   --------------------------

   协议处理超时分布
      协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       10104(   3)                                0.00%  33.33%  33.33%  33.33%   0.00%   0.00%   0.00% 
   协议:       13107(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(  54)                                0.00%   5.56%  37.04%  38.89%  18.52%   0.00%   0.00% 
   协议:       11025(   4)                                0.00%  50.00%  25.00%  25.00%   0.00%   0.00%   0.00% 
   协议:  event_2033(   4)                               50.00%   0.00%  25.00%  25.00%   0.00%   0.00%   0.00% 
   协议:   event:869( 743)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_4006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_2030(   6)                               33.33%  33.33%  16.67%  16.67%   0.00%   0.00%   0.00% 
   协议: event_10002(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric
   ![](./pic/crystal-********-1.png)

4. pprof

   ```
   [release@localhost crystalTest]$ go tool pprof profile-********-1
   File: service
   Type: cpu
   Time: Mar 2, 2022 at 12:20pm (CST)
   Duration: 30.17s, Total samples = 13.38s (44.35%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 5.83s, 43.57% of 13.38s total
   Dropped 638 nodes (cum <= 0.07s)
   Showing top 20 nodes out of 252
         flat  flat%   sum%        cum   cum%
      0.90s  6.73%  6.73%      1.89s 14.13%  runtime.scanobject
      0.72s  5.38% 12.11%      0.77s  5.75%  syscall.Syscall
      0.60s  4.48% 16.59%      0.60s  4.48%  runtime.futex
      0.46s  3.44% 20.03%      1.52s 11.36%  runtime.mallocgc
      0.37s  2.77% 22.80%      0.39s  2.91%  runtime.mapaccess1_fast32
      0.36s  2.69% 25.49%      0.38s  2.84%  runtime.findObject
      0.29s  2.17% 27.65%      0.34s  2.54%  runtime.heapBitsSetType
      0.23s  1.72% 29.37%      0.23s  1.72%  runtime.markBits.isMarked (inline)
      0.22s  1.64% 31.02%      0.22s  1.64%  runtime.nextFreeFast (inline)
      0.21s  1.57% 32.59%      0.55s  4.11%  runtime.selectgo
      0.20s  1.49% 34.08%      0.20s  1.49%  runtime.epollwait
      0.19s  1.42% 35.50%      0.19s  1.42%  runtime.heapBits.bits (inline)
      0.17s  1.27% 36.77%      0.17s  1.27%  runtime.memmove
      0.16s  1.20% 37.97%      0.24s  1.79%  runtime.mapiternext
      0.14s  1.05% 39.01%      0.15s  1.12%  runtime.(*itabTableType).find
      0.13s  0.97% 39.99%      0.13s  0.97%  github.com/json-iterator/go.(*Stream).WriteString
      0.13s  0.97% 40.96%      0.15s  1.12%  runtime.step
      0.12s   0.9% 41.85%      0.12s   0.9%  runtime.write1
      0.12s   0.9% 42.75%      0.12s   0.9%  time.Now
      0.11s  0.82% 43.57%      0.14s  1.05%  app/protos/out/cl.(*TaskTypeProgress).Clone
   ```

   ```
   [release@localhost crystalTest]$ go tool pprof heap-********-1
   File: service
   Type: inuse_space
   Time: Mar 2, 2022 at 12:20pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 505.89MB, 77.29% of 654.53MB total
   Dropped 322 nodes (cum <= 3.27MB)
   Showing top 20 nodes out of 93
         flat  flat%   sum%        cum   cum%
      72.51MB 11.08% 11.08%    93.51MB 14.29%  app/logic/character.newHero (inline)
         54MB  8.25% 19.33%       54MB  8.25%  app/logic/character.NewEmblem (inline)
         37MB  5.65% 24.98%    62.50MB  9.55%  app/logic/character.newGem
      34.58MB  5.28% 30.27%    97.09MB 14.83%  app/logic/character.(*GemM).AddGem
      31.50MB  4.81% 35.08%       32MB  4.89%  app/logic/character.(*User).TaskTypeOnEvent
      28.07MB  4.29% 39.37%    82.07MB 12.54%  app/logic/character.(*EmblemM).addEmblem
         28MB  4.28% 43.65%       28MB  4.28%  app/logic/character.newEquip (inline)
         28MB  4.28% 47.92%       28MB  4.28%  app/protos/out/cl.(*FormationInfo).Clone (inline)
      27.05MB  4.13% 52.06%   120.56MB 18.42%  app/logic/character.(*HeroM).Add
         26MB  3.97% 56.03%       26MB  3.97%  app/logic/character.generateGemAttr
      25.50MB  3.90% 59.93%    25.50MB  3.90%  app/logic/character.(*Handbook).add
         21MB  3.21% 63.13%       21MB  3.21%  app/logic/character.initHeroFromData (inline)
      16.03MB  2.45% 65.58%    44.03MB  6.73%  app/logic/character.(*EquipM).Add
      15.50MB  2.37% 67.95%    15.50MB  2.37%  app/logic/character.(*Avatar).Add
      15.50MB  2.37% 70.32%    15.50MB  2.37%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
         15MB  2.29% 72.61%    58.50MB  8.94%  app/protos/out/cl.(*Formation).Clone
      9.50MB  1.45% 74.06%     9.50MB  1.45%  app/logic/character.(*Hero).calcTotalAttr
      9.01MB  1.38% 75.44%    16.51MB  2.52%  app/logic/character.(*User).initModule
      6.10MB  0.93% 76.37%     6.10MB  0.93%  app/goxml.(*MonsterInfoManager).Load
         6MB  0.92% 77.29%        6MB  0.92%  app/logic/character.(*User).mergeResources
   ```

   ```
   [release@localhost crystalTest]$ go tool pprof allocs-********-1
   File: service
   Type: alloc_space
   Time: Mar 2, 2022 at 12:20pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 9352.42MB, 67.63% of 13829.53MB total
   Dropped 867 nodes (cum <= 69.15MB)
   Showing top 20 nodes out of 138
         flat  flat%   sum%        cum   cum%
   1504.63MB 10.88% 10.88%  2220.19MB 16.05%  app/logic/character.(*User).TaskTypeOnEvent
   823.04MB  5.95% 16.83%   823.04MB  5.95%  container/list.(*List).insertValue (inline)
   661.04MB  4.78% 21.61%   661.04MB  4.78%  app/protos/out/cl.(*TaskTypeProgress).Clone
   612.03MB  4.43% 26.04%  1430.06MB 10.34%  app/logic/character.(*User).pushMsg
   569.64MB  4.12% 30.16%   575.64MB  4.16%  app/logic/db/redisop.DbClient.SetUserMCallSKs
   534.01MB  3.86% 34.02%   536.01MB  3.88%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
   488.99MB  3.54% 37.55%   505.99MB  3.66%  app/logic/db/redisop.ClClient.SetSomeGemInfoMCallSKs
   472.74MB  3.42% 40.97%   595.76MB  4.31%  app/logic/character.(*User).mergeResources
   460.53MB  3.33% 44.30%   740.55MB  5.35%  context.WithDeadline
   453.45MB  3.28% 47.58%   454.45MB  3.29%  app/logic/db/redisop.ClClient.SetSomeEmblemInfoMCallSKs
   428.41MB  3.10% 50.68%   429.41MB  3.11%  app/logic/db/redisop.ClClient.SetSomeEquipmentMCallSKs
   427.22MB  3.09% 53.77%   467.22MB  3.38%  encoding/xml.(*Decoder).rawToken
   339.52MB  2.46% 56.22%   580.53MB  4.20%  app/logic/character.(*User).FireCommonEvent
   317.51MB  2.30% 58.52%  1748.07MB 12.64%  app/logic/character.(*User).SendCmdToGateway
   280.02MB  2.02% 60.54%   280.02MB  2.02%  time.AfterFunc
   264.54MB  1.91% 62.46%   264.54MB  1.91%  strings.(*Builder).WriteString
   238.51MB  1.72% 64.18%   739.53MB  5.35%  app/logic/helper/sync.(*MessageSync).append
   167.51MB  1.21% 65.39%   555.03MB  4.01%  app/logic/character.(*Achieve).update
   154.56MB  1.12% 66.51%   196.10MB  1.42%  app/logic/character.(*User).LogResourceAdd
   154.52MB  1.12% 67.63%   154.52MB  1.12%  app/protos/out/cl.(*HeroBody).Clone
   ```

   

#### 第二版 -  4000人，使用中等配置机器人，开启英雄，装备，宝石，纹章和水晶五个模块

1. robot数据

   ```
   2022-03-02 13:30:26 压测数据分析
   压测基础情况
   开始时间：2022/03/02 13:14:06, 结束时间:2022/03/02 13:18:39, 耗时:273(s)
   登录成功机器人总数：4000
   总消息数:965237， 平均每秒消息数:3535

   --------------------------

   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:11925,MSG_C2L_HeroChangeSave                         6       8            0.00%   75.00%
   协议:cmd:11921,MSG_C2L_HeroRevive                         10511   11671            1.08%   90.06%
   协议:cmd:13109,MSG_C2L_EmblemDecompose                    26638   26638            2.75%  100.00%
   协议:cmd:12301,MSG_C2L_EquipGet                            4000    4000            0.41%  100.00%
   协议:cmd:14701,MSG_C2L_CrystalAddHero                     12188   14899            1.26%   81.80%
   协议:cmd:13112,MSG_C2L_EmblemBlessing                     26902   26902            2.78%  100.00%
   协议:cmd:12309,MSG_C2L_EquipEnchant                       24719   30369            2.55%   81.40%
   协议:cmd:12307,MSG_C2L_EquipRefine                        25322   29916            2.61%   84.64%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                        16528   21317            1.71%   77.53%
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                   4000    4000            0.41%  100.00%
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp             23325   35768            2.41%   65.21%
   协议:cmd:11909,MSG_C2L_HeroBuySlot                        39896   68571            4.12%   58.18%
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement           35643   35643            3.68%  100.00%
   协议:cmd:11913,MSG_C2L_HeroDecompose                      14865   20138            1.53%   73.82%
   协议:cmd:12701,MSG_C2L_GetGems                             4000    4000            0.41%  100.00%
   协议:cmd:13103,MSG_C2L_EmblemWear                         15124   26682            1.56%   56.68%
   协议:cmd:12311,MSG_C2L_EquipEvolution                      1519    7188            0.16%   21.13%
   协议:cmd:11905,MSG_C2L_HeroStageUp                         9263   21496            0.96%   43.09%
   协议:cmd:11901,MSG_C2L_HeroList                            4006    4006            0.41%  100.00%
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                  10799   10799            1.11%  100.00%
   协议:cmd:12709,MSG_C2L_GemConvert                         23755   53704            2.45%   44.23%
   协议:cmd:12703,MSG_C2L_GemWear                            30531   53489            3.15%   57.08%
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                    6482    9752            0.67%   66.47%
   协议:cmd:11919,MSG_C2L_HeroBack                            7056    7056            0.73%  100.00%
   协议:cmd:12313,MSG_C2L_EquipDecompose                     34342   34342            3.54%  100.00%
   协议:cmd:12705,MSG_C2L_GemCompose                         52480   53740            5.41%   97.66%
   协议:cmd:12303,MSG_C2L_EquipWear                          27195   34549            2.81%   78.71%
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                  35917   35917            3.71%  100.00%
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus               38366   48513            3.96%   79.08%
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD                  9479    9936            0.98%   95.40%
   协议:cmd:12707,MSG_C2L_GemDecompose                       53880   53880            5.56%  100.00%
   协议:cmd:12305,MSG_C2L_EquipStrength                          0   30041            0.00%    0.00%
   协议:cmd:13105,MSG_C2L_EmblemLevelUp                      26685   26685            2.75%  100.00%
   协议:cmd:11907,MSG_C2L_HeroStarUp                          3606    5398            0.37%   66.80%
   协议:cmd:11025,MSG_C2L_GM                                  8000    8000            0.83%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               4000    4000            0.41%  100.00%
   协议:cmd:13107,MSG_C2L_EmblemStageUp                      26867   26867            2.77%  100.00%
   协议:cmd:13116,MSG_C2L_EmblemRevive                       24399   26547            2.52%   91.91%
   协议:cmd:13101,MSG_C2L_EmblemGet                           4000    4000            0.41%  100.00%
   协议:cmd:10100,                                            4000    4000            0.41%  100.00%
   协议:cmd:12315,MSG_C2L_EquipRevive                        31901   34810            3.29%   91.64%
   协议正确数:762195 协议总数:969237 正确比:78.64%

   --------------------------

   协议延迟分布
      协议名称                                              0-10  10-20  20-50  50-100 100-200 200-************ 1000-100000 
   协议:cmd:11925,MSG_C2L_HeroChangeSave                   83.33%  16.67%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11921,MSG_C2L_HeroRevive                       97.43%   1.38%   0.85%   0.28%   0.07%   0.00%   0.00%   0.00% 
   协议:cmd:13109,MSG_C2L_EmblemDecompose                  98.00%   1.11%   0.72%   0.12%   0.04%   0.00%   0.00%   0.00% 
   协议:cmd:12301,MSG_C2L_EquipGet                         27.77%  13.43%  14.92%  16.70%   6.93%  14.67%   5.58%   0.00% 
   协议:cmd:14701,MSG_C2L_CrystalAddHero                   97.78%   1.24%   0.86%   0.11%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:13112,MSG_C2L_EmblemBlessing                   97.95%   1.09%   0.69%   0.20%   0.07%   0.00%   0.00%   0.00% 
   协议:cmd:12309,MSG_C2L_EquipEnchant                     98.66%   0.77%   0.54%   0.03%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12307,MSG_C2L_EquipRefine                      98.67%   0.83%   0.48%   0.01%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                      98.09%   1.10%   0.74%   0.06%   0.01%   0.01%   0.00%   0.00% 
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                27.73%  12.95%  15.00%  18.43%   5.70%  14.95%   5.25%   0.00% 
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp           98.81%   0.72%   0.47%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11909,MSG_C2L_HeroBuySlot                      97.17%   1.41%   1.07%   0.29%   0.07%   0.00%   0.00%   0.00% 
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement         97.93%   1.12%   0.74%   0.18%   0.03%   0.00%   0.00%   0.00% 
   协议:cmd:11913,MSG_C2L_HeroDecompose                    97.39%   1.44%   0.84%   0.27%   0.06%   0.00%   0.00%   0.00% 
   协议:cmd:12701,MSG_C2L_GetGems                          28.20%  14.12%  14.72%  17.68%   6.50%  14.12%   4.65%   0.00% 
   协议:cmd:13103,MSG_C2L_EmblemWear                       97.41%   1.34%   0.90%   0.29%   0.06%   0.00%   0.00%   0.00% 
   协议:cmd:12311,MSG_C2L_EquipEvolution                   98.95%   0.46%   0.59%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                      97.55%   1.28%   0.85%   0.25%   0.05%   0.01%   0.00%   0.00% 
   协议:cmd:11901,MSG_C2L_HeroList                         27.83%  13.23%  14.55%  17.20%   6.69%  15.38%   5.12%   0.00% 
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                98.71%   0.72%   0.56%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12709,MSG_C2L_GemConvert                       98.96%   0.71%   0.32%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12703,MSG_C2L_GemWear                          97.47%   1.29%   0.89%   0.28%   0.06%   0.00%   0.00%   0.00% 
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                 97.66%   1.22%   0.80%   0.26%   0.06%   0.00%   0.00%   0.00% 
   协议:cmd:11919,MSG_C2L_HeroBack                         97.62%   1.16%   0.88%   0.28%   0.06%   0.00%   0.00%   0.00% 
   协议:cmd:12313,MSG_C2L_EquipDecompose                   97.79%   1.14%   0.84%   0.19%   0.04%   0.00%   0.00%   0.00% 
   协议:cmd:12705,MSG_C2L_GemCompose                       97.94%   1.10%   0.74%   0.19%   0.04%   0.00%   0.00%   0.00% 
   协议:cmd:12303,MSG_C2L_EquipWear                        97.43%   1.38%   0.92%   0.22%   0.06%   0.00%   0.00%   0.00% 
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                97.96%   1.10%   0.75%   0.16%   0.03%   0.00%   0.00%   0.00% 
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus             97.59%   1.30%   0.79%   0.25%   0.07%   0.00%   0.00%   0.00% 
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD               99.00%   0.62%   0.38%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12707,MSG_C2L_GemDecompose                     97.87%   1.14%   0.76%   0.19%   0.03%   0.01%   0.00%   0.00% 
   协议:cmd:13105,MSG_C2L_EmblemLevelUp                    97.77%   1.26%   0.73%   0.21%   0.02%   0.00%   0.00%   0.00% 
   协议:cmd:11907,MSG_C2L_HeroStarUp                       97.62%   1.55%   0.72%   0.08%   0.03%   0.00%   0.00%   0.00% 
   协议:cmd:11025,MSG_C2L_GM                               14.45%   6.49%   7.39%   8.53%   3.69%   8.56%   4.35%  46.55% 
   协议:cmd:11003,MSG_C2L_Flush                             0.05%   0.05%   0.22%   0.35%   0.65%   2.40%   3.23%  93.05% 
   协议:cmd:13107,MSG_C2L_EmblemStageUp                    97.70%   1.21%   0.87%   0.18%   0.03%   0.00%   0.00%   0.00% 
   协议:cmd:13116,MSG_C2L_EmblemRevive                     97.75%   1.21%   0.82%   0.19%   0.03%   0.00%   0.00%   0.00% 
   协议:cmd:13101,MSG_C2L_EmblemGet                        28.07%  13.73%  14.80%  17.00%   6.15%  14.88%   5.38%   0.00% 
   协议:cmd:10100,                                          0.18%   0.10%   0.22%   0.15%   2.23%   1.35%   8.50%  87.28% 
   协议:cmd:12315,MSG_C2L_EquipRevive                      97.78%   1.19%   0.83%   0.18%   0.02%   0.00%   0.00%   0.00%
   ```
   
2. 游戏数据

   ```
   所有超时的left
   [2022/03/02 13:13:48.016382] gate(pck/s: 887 sum: 5256 left:3729) logic db(pck/s: 923 sum: 5281 left:710) redis db(pck/s: 1182 sum: 7084 left:0) [2022/03/02 13:13:49.223972] gate(pck/s: 723 sum: 5979 left:3754) logic db(pck/s: 783 sum: 6064 left:736) redis db(pck/s: 973 sum: 8057 left:0) [2022/03/02 13:13:50.121972] gate(pck/s: 704 sum: 6683 left:3725) logic db(pck/s: 783 sum: 6847 left:799) redis db(pck/s: 1000 sum: 9057 left:0) [2022/03/02 13:13:51.018928] gate(pck/s: 286 sum: 6969 left:5199) logic db(pck/s: 265 sum: 7112 left:623) redis db(pck/s: 234 sum: 9291 left:0) [2022/03/02 13:13:52.228101] gate(pck/s: 405 sum: 7374 left:5148) logic db(pck/s: 394 sum: 7506 left:446) redis db(pck/s: 421 sum: 9712 left:0) [2022/03/02 13:13:53.131852] gate(pck/s: 201 sum: 7575 left:5241) logic db(pck/s: 195 sum: 7701 left:299) redis db(pck/s: 151 sum: 9863 left:0) [2022/03/02 13:13:54.030391] gate(pck/s: 276 sum: 7851 left:5563) logic db(pck/s: 299 sum: 8000 left:0) redis db(pck/s: 140 sum: 10003 left:0) 
   [2022/03/02 13:13:55.237033] gate(pck/s: 435 sum: 8286 left:5128) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 219 sum: 10222 left:0) 
   [2022/03/02 13:13:56.139534] gate(pck/s: 311 sum: 8597 left:4817) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 158 sum: 10380 left:0) 
   [2022/03/02 13:13:57.036696] gate(pck/s: 280 sum: 8877 left:4537) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 142 sum: 10522 left:0) 
   [2022/03/02 13:13:58.211941] gate(pck/s: 333 sum: 9210 left:4204) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 168 sum: 10690 left:0) 
   [2022/03/02 13:13:59.110789] gate(pck/s: 206 sum: 9416 left:3998) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 104 sum: 10794 left:0) 
   [2022/03/02 13:14:00.008816] gate(pck/s: 293 sum: 9709 left:3706) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 150 sum: 10944 left:0) 
   [2022/03/02 13:14:01.222072] gate(pck/s: 435 sum: 10144 left:6324) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 219 sum: 11163 left:0) 
   [2022/03/02 13:14:02.121772] gate(pck/s: 274 sum: 10418 left:6050) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 139 sum: 11302 left:0) 
   [2022/03/02 13:14:03.019493] gate(pck/s: 1594 sum: 12012 left:4456) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 95 sum: 11397 left:0) 
   [2022/03/02 13:14:04.229464] gate(pck/s: 356 sum: 12368 left:4100) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 180 sum: 11577 left:0) 
   [2022/03/02 13:14:05.131006] gate(pck/s: 278 sum: 12646 left:3822) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 141 sum: 11718 left:0) 
   [2022/03/02 13:14:06.022902] gate(pck/s: 266 sum: 12912 left:3556) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 135 sum: 11853 left:0) 
   [2022/03/02 13:14:07.246064] gate(pck/s: 334 sum: 13246 left:3222) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 169 sum: 12022 left:0) 
   [2022/03/02 13:14:08.130963] gate(pck/s: 2168 sum: 15414 left:1901) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 87 sum: 12109 left:0) 
   [2022/03/02 13:18:41.117324] gate(pck/s: 5276 sum: 1085016 left:1999) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 386 sum: 40789 left:647) 
   [2022/03/02 13:18:42.015749] gate(pck/s: 2967 sum: 1087983 left:0) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1774 sum: 42563 left:1863)

   --------------------------

   logic服务器启动时间:
   logic账号总数:

   --------------------------

   协议处理超时分布
      协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       11025(   6)                               33.33%  33.33%  33.33%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(  32)                                0.00%  18.75%  62.50%  18.75%   0.00%   0.00%   0.00% 
   协议:   event:869( 699)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_1010(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       14713(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议: event_10016(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12303(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       14715(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_2030(   3)                               33.33%  33.33%   0.00%  33.33%   0.00%   0.00%   0.00% 
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric

   ![](./pic/crystal-********-2.png)

4. pprof

   ```
   [release@localhost crystalTest]$ go tool pprof profile-********-2
   File: service
   Type: cpu
   Time: Mar 2, 2022 at 1:16pm (CST)
   Duration: 30.18s, Total samples = 12.82s (42.47%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 5.90s, 46.02% of 12.82s total
   Dropped 606 nodes (cum <= 0.06s)
   Showing top 20 nodes out of 256
         flat  flat%   sum%        cum   cum%
      1.57s 12.25% 12.25%      3.11s 24.26%  runtime.scanobject
      0.56s  4.37% 16.61%      0.59s  4.60%  runtime.findObject
      0.52s  4.06% 20.67%      0.60s  4.68%  syscall.Syscall
      0.37s  2.89% 23.56%      0.37s  2.89%  runtime.futex
      0.33s  2.57% 26.13%      0.33s  2.57%  runtime.markBits.isMarked (inline)
      0.29s  2.26% 28.39%      0.29s  2.26%  runtime.heapBits.bits (inline)
      0.24s  1.87% 30.27%         1s  7.80%  runtime.mallocgc
      0.23s  1.79% 32.06%      0.28s  2.18%  runtime.heapBitsSetType
      0.21s  1.64% 33.70%      0.21s  1.64%  runtime.epollwait
      0.21s  1.64% 35.34%      0.27s  2.11%  runtime.mapaccess1_fast32
      0.20s  1.56% 36.90%      0.48s  3.74%  runtime.selectgo
      0.17s  1.33% 38.22%      0.17s  1.33%  github.com/json-iterator/go.(*Stream).WriteString
      0.16s  1.25% 39.47%      0.22s  1.72%  runtime.mapiternext
      0.15s  1.17% 40.64%      0.15s  1.17%  runtime.nextFreeFast (inline)
      0.14s  1.09% 41.73%      0.14s  1.09%  runtime.write1
      0.11s  0.86% 42.59%      0.11s  0.86%  app/protos/out/cl.sovCl
      0.11s  0.86% 43.45%      0.92s  7.18%  github.com/json-iterator/go.(*structEncoder).Encode
      0.11s  0.86% 44.31%      0.11s  0.86%  runtime.(*itabTableType).find
      0.11s  0.86% 45.16%      0.33s  2.57%  runtime.mapiterinit
      0.11s  0.86% 46.02%      0.11s  0.86%  runtime.memclrNoHeapPointers
   ```
   
   ```
   [release@localhost crystalTest]$ go tool pprof heap-********-2
   File: service
   Type: inuse_space
   Time: Mar 2, 2022 at 1:16pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 437.31MB, 78.59% of 556.44MB total
   Dropped 265 nodes (cum <= 2.78MB)
   Showing top 20 nodes out of 93
         flat  flat%   sum%        cum   cum%
      62.50MB 11.23% 11.23%    85.51MB 15.37%  app/logic/character.newHero (inline)
      52.50MB  9.44% 20.67%    52.50MB  9.44%  app/logic/character.NewEmblem (inline)
         36MB  6.47% 27.14%       36MB  6.47%  app/logic/character.(*User).TaskTypeOnEvent
      33.50MB  6.02% 33.16%    33.50MB  6.02%  app/logic/character.newEquip (inline)
         32MB  5.75% 38.91%       58MB 10.42%  app/logic/character.newGem
      27.50MB  4.94% 43.85%    27.50MB  4.94%  app/logic/character.(*Handbook).add
      26.50MB  4.76% 48.62%    26.50MB  4.76%  app/logic/character.generateGemAttr
         23MB  4.13% 52.75%       23MB  4.13%  app/logic/character.initHeroFromData (inline)
         20MB  3.59% 56.34%       20MB  3.59%  app/protos/out/cl.(*FormationInfo).Clone (inline)
      19.55MB  3.51% 59.86%    77.55MB 13.94%  app/logic/character.(*GemM).AddGem
      19.05MB  3.42% 63.28%   104.55MB 18.79%  app/logic/character.(*HeroM).Add
      17.54MB  3.15% 66.43%    70.05MB 12.59%  app/logic/character.(*EmblemM).addEmblem
      14.53MB  2.61% 69.04%    48.04MB  8.63%  app/logic/character.(*EquipM).Add
      11.50MB  2.07% 71.11%    11.50MB  2.07%  app/logic/character.(*Avatar).Add
         11MB  1.98% 73.09%    38.50MB  6.92%  app/protos/out/cl.(*Formation).Clone
      7.50MB  1.35% 74.44%     7.50MB  1.35%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
         7MB  1.26% 75.70%        7MB  1.26%  app/logic/character.(*User).mergeResources
      5.50MB  0.99% 76.68%       44MB  7.91%  app/logic/character.(*FormationM).InitFormationByExistOne
      5.50MB  0.99% 77.67%        6MB  1.08%  app/logic/character.(*Hero).calcTotalAttr
      5.10MB  0.92% 78.59%     5.10MB  0.92%  app/goxml.(*MonsterInfoManager).Load
   ```
   
   ```
   [release@localhost crystalTest]$ go tool pprof allocs-********-2
   File: service
   Type: alloc_space
   Time: Mar 2, 2022 at 1:16pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 7.19GB, 67.73% of 10.61GB total
   Dropped 803 nodes (cum <= 0.05GB)
   Showing top 20 nodes out of 140
         flat  flat%   sum%        cum   cum%
      1.21GB 11.44% 11.44%     1.78GB 16.77%  app/logic/character.(*User).TaskTypeOnEvent
      0.62GB  5.84% 17.28%     0.62GB  5.84%  container/list.(*List).insertValue (inline)
      0.51GB  4.84% 22.12%     0.51GB  4.84%  app/protos/out/cl.(*TaskTypeProgress).Clone
      0.46GB  4.30% 26.42%     1.07GB 10.05%  app/logic/character.(*User).pushMsg
      0.40GB  3.81% 30.23%     0.41GB  3.84%  app/logic/db/redisop.DbClient.SetUserMCallSKs
      0.40GB  3.75% 33.98%     0.40GB  3.76%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
      0.39GB  3.67% 37.65%     0.42GB  3.94%  encoding/xml.(*Decoder).rawToken
      0.38GB  3.62% 41.28%     0.39GB  3.70%  app/logic/db/redisop.ClClient.SetSomeGemInfoMCallSKs
      0.34GB  3.19% 44.46%     0.42GB  3.94%  app/logic/character.(*User).mergeResources
      0.33GB  3.15% 47.61%     0.33GB  3.16%  app/logic/db/redisop.ClClient.SetSomeEmblemInfoMCallSKs
      0.33GB  3.11% 50.72%     0.57GB  5.34%  context.WithDeadline
      0.32GB  3.00% 53.72%     0.32GB  3.00%  app/logic/db/redisop.ClClient.SetSomeEquipmentMCallSKs
      0.27GB  2.50% 56.23%     0.46GB  4.36%  app/logic/character.(*User).FireCommonEvent
      0.26GB  2.47% 58.69%     1.33GB 12.53%  app/logic/character.(*User).SendCmdToGateway
      0.24GB  2.23% 60.92%     0.24GB  2.23%  time.AfterFunc
      0.19GB  1.79% 62.71%     0.19GB  1.79%  strings.(*Builder).WriteString
      0.18GB  1.72% 64.43%     0.55GB  5.16%  app/logic/helper/sync.(*MessageSync).append
      0.12GB  1.17% 65.60%     0.42GB  3.93%  app/logic/character.(*Achieve).update
      0.12GB  1.14% 66.74%     0.12GB  1.14%  app/protos/out/cl.(*HeroBody).Clone
      0.10GB  0.99% 67.73%     0.14GB  1.28%  app/logic/character.(*User).LogResourceAdd
   ```
   
   

#### 第三版 -  4000人，使用中等配置机器人，开启英雄和水晶两个个模块

优化后，使用和申请内存数据有所下降，参考pprof top20的统计数据
heap: 286.26MB -> 218.23MB
allocs: 8.41GB -> 7.85GB

1. robot数据 --- 平均每秒943个战斗协议（持续回合很短，大概一回合就结束了）

   ```
   2022-03-02 13:56:46 压测数据分析
   压测基础情况
   开始时间：2022/03/02 13:34:28, 结束时间:2022/03/02 13:39:00, 耗时:272(s)
   登录成功机器人总数：4000
   总消息数:912035， 平均每秒消息数:3353

   --------------------------

   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:10100,                                            4000    4000            0.44%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               4000    4000            0.44%  100.00%
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                    9802   17411            1.07%   56.30%
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                  85777   85777            9.36%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  4000    4000            0.44%  100.00%
   协议:cmd:11919,MSG_C2L_HeroBack                           17839   17839            1.95%  100.00%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                         5803   53998            0.63%   10.75%
   协议:cmd:11921,MSG_C2L_HeroRevive                         26855   28495            2.93%   94.24%
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp             43816   89917            4.78%   48.73%
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD                 26698   29569            2.91%   90.29%
   协议:cmd:14701,MSG_C2L_CrystalAddHero                     29461   32490            3.22%   90.68%
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                   4000    4000            0.44%  100.00%
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                  27955   27955            3.05%  100.00%
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement           89789   89789            9.80%  100.00%
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus              111861  140533           12.21%   79.60%
   协议:cmd:11901,MSG_C2L_HeroList                            4000    4000            0.44%  100.00%
   协议:cmd:11907,MSG_C2L_HeroStarUp                          1220    1220            0.13%  100.00%
   协议:cmd:11913,MSG_C2L_HeroDecompose                      39798   39962            4.34%   99.59%
   协议:cmd:11905,MSG_C2L_HeroStageUp                        25416   54373            2.77%   46.74%
   协议:cmd:11909,MSG_C2L_HeroBuySlot                        40000  186707            4.37%   21.42%
   协议正确数:602090 协议总数:916035 正确比:65.73%

   --------------------------

   协议延迟分布
      协议名称                                              0-10  10-20  20-50  50-100 100-200 200-************ 1000-100000 
   协议:cmd:10100,                                          0.18%   0.18%   0.18%   0.45%   1.15%   3.17%   6.15%  88.55% 
   协议:cmd:11003,MSG_C2L_Flush                             0.10%   0.07%   0.07%   0.53%   0.73%   2.08%   3.00%  93.42% 
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                 98.94%   0.77%   0.30%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                98.75%   0.91%   0.34%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11025,MSG_C2L_GM                                0.05%   0.05%   0.12%   0.53%   0.75%   2.05%   3.00%  93.45% 
   协议:cmd:11919,MSG_C2L_HeroBack                         98.83%   0.83%   0.34%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                      98.74%   0.91%   0.34%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11921,MSG_C2L_HeroRevive                       98.77%   0.92%   0.31%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp           98.76%   0.89%   0.35%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD               98.75%   0.90%   0.36%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14701,MSG_C2L_CrystalAddHero                   98.72%   0.95%   0.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                85.92%   2.60%   2.38%   3.67%   5.42%   0.00%   0.00%   0.00% 
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                98.78%   0.91%   0.32%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement         98.77%   0.90%   0.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus             98.91%   0.76%   0.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11901,MSG_C2L_HeroList                         84.30%   2.57%   3.00%   3.67%   6.45%   0.00%   0.00%   0.00% 
   协议:cmd:11909,MSG_C2L_HeroBuySlot                      98.89%   0.80%   0.30%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11907,MSG_C2L_HeroStarUp                       98.85%   0.82%   0.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11913,MSG_C2L_HeroDecompose                    98.85%   0.83%   0.32%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                      98.92%   0.75%   0.33%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```

2. 游戏数据

   ```
   所有超时的left
   [2022/03/02 13:34:09.064251] gate(pck/s: 420 sum: 4660 left:3953) logic db(pck/s: 430 sum: 4802 left:298) redis db(pck/s: 477 sum: 6229 left:0) [2022/03/02 13:34:10.246848] gate(pck/s: 1227 sum: 5887 left:3816) logic db(pck/s: 1169 sum: 5971 left:653) redis db(pck/s: 1689 sum: 7918 left:0) [2022/03/02 13:34:11.141526] gate(pck/s: 394 sum: 6281 left:3766) logic db(pck/s: 393 sum: 6364 left:581) redis db(pck/s: 470 sum: 8388 left:0) [2022/03/02 13:34:12.044564] gate(pck/s: 562 sum: 6843 left:5293) logic db(pck/s: 601 sum: 6965 left:535) redis db(pck/s: 688 sum: 9076 left:0) [2022/03/02 13:34:13.252459] gate(pck/s: 400 sum: 7243 left:5279) logic db(pck/s: 398 sum: 7363 left:442) redis db(pck/s: 457 sum: 9533 left:0) [2022/03/02 13:34:14.151689] gate(pck/s: 274 sum: 7517 left:5253) logic db(pck/s: 302 sum: 7665 left:318) redis db(pck/s: 317 sum: 9850 left:0) [2022/03/02 13:34:15.051790] gate(pck/s: 308 sum: 7825 left:5469) logic db(pck/s: 279 sum: 7944 left:56) redis db(pck/s: 172 sum: 10022 left:0) [2022/03/02 13:34:16.258756] gate(pck/s: 393 sum: 8218 left:5189) logic db(pck/s: 56 sum: 8000 left:0) redis db(pck/s: 197 sum: 10219 left:0) 
   [2022/03/02 13:34:17.155438] gate(pck/s: 302 sum: 8520 left:4887) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 152 sum: 10371 left:0) 
   [2022/03/02 13:34:18.059903] gate(pck/s: 265 sum: 8785 left:4622) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 134 sum: 10505 left:0) 
   [2022/03/02 13:34:19.237128] gate(pck/s: 293 sum: 9078 left:4329) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 147 sum: 10652 left:0) 
   [2022/03/02 13:34:20.166410] gate(pck/s: 315 sum: 9393 left:4014) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 159 sum: 10811 left:0) 
   [2022/03/02 13:34:21.062067] gate(pck/s: 316 sum: 9709 left:3698) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 159 sum: 10970 left:0) 
   [2022/03/02 13:34:22.243997] gate(pck/s: 338 sum: 10047 left:6365) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 170 sum: 11140 left:0) 
   [2022/03/02 13:34:23.141436] gate(pck/s: 1116 sum: 11163 left:5249) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 98 sum: 11238 left:0) 
   [2022/03/02 13:34:24.039024] gate(pck/s: 677 sum: 11840 left:4572) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 102 sum: 11340 left:0) 
   [2022/03/02 13:34:25.250758] gate(pck/s: 354 sum: 12194 left:4218) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 178 sum: 11518 left:0) 
   [2022/03/02 13:34:26.151642] gate(pck/s: 252 sum: 12446 left:3967) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 127 sum: 11645 left:0) 
   [2022/03/02 13:34:27.055789] gate(pck/s: 206 sum: 12652 left:3761) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 104 sum: 11749 left:0) 
   [2022/03/02 13:34:28.256901] gate(pck/s: 336 sum: 12988 left:3425) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 169 sum: 11918 left:0) 
   [2022/03/02 13:34:29.155898] gate(pck/s: 265 sum: 13253 left:3160) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 134 sum: 12052 left:0) 
   [2022/03/02 13:39:02.137965] gate(pck/s: 4275 sum: 1031496 left:2569) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 483 sum: 32887 left:948)
   [2022/03/02 13:39:03.037132] gate(pck/s: 2569 sum: 1034065 left:0) logic db(pck/s: 0 sum: 8000 left:0) redis db(pck/s: 1826 sum: 34713 left:1701) 

   --------------------------

   logic服务器启动时间:
   logic账号总数:

   --------------------------

   协议处理超时分布
      协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(  33)                                0.00%  36.36%  51.52%  12.12%   0.00%   0.00%   0.00% 
   协议:       11025(   5)                               80.00%  20.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_2033(   4)                               50.00%   0.00%  50.00%   0.00%   0.00%   0.00%   0.00% 
   协议:   event:869( 839)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_1010(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_4006(   2)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11921(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11913(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_2030(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric

   ![](./pic/crystal-********-3.png)

4. pprof

   ```
   [release@localhost crystalTest]$ go tool pprof profile-********-3
   File: service
   Type: cpu
   Time: Mar 2, 2022 at 1:36pm (CST)
   Duration: 30.10s, Total samples = 10.05s (33.39%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 4.58s, 45.57% of 10.05s total
   Dropped 493 nodes (cum <= 0.05s)
   Showing top 20 nodes out of 254
         flat  flat%   sum%        cum   cum%
      0.80s  7.96%  7.96%      1.51s 15.02%  runtime.scanobject
      0.54s  5.37% 13.33%      0.56s  5.57%  syscall.Syscall
      0.46s  4.58% 17.91%      0.46s  4.58%  runtime.futex
      0.37s  3.68% 21.59%      0.41s  4.08%  runtime.findObject
      0.32s  3.18% 24.78%      1.10s 10.95%  runtime.mallocgc
      0.28s  2.79% 27.56%      0.29s  2.89%  runtime.mapaccess1_fast32
      0.19s  1.89% 29.45%      0.24s  2.39%  runtime.mapiternext
      0.18s  1.79% 31.24%      0.51s  5.07%  runtime.selectgo
      0.17s  1.69% 32.94%      0.26s  2.59%  runtime.heapBitsSetType
      0.15s  1.49% 34.43%      0.15s  1.49%  runtime.nextFreeFast (inline)
      0.14s  1.39% 35.82%      0.14s  1.39%  runtime.heapBits.bits (inline)
      0.14s  1.39% 37.21%      0.14s  1.39%  runtime.write1
      0.12s  1.19% 38.41%      0.12s  1.19%  runtime.memmove
      0.12s  1.19% 39.60%      0.12s  1.19%  time.Now
      0.11s  1.09% 40.70%      0.11s  1.09%  runtime.epollwait
      0.10s     1% 41.69%      0.11s  1.09%  runtime.(*itabTableType).find
      0.10s     1% 42.69%      1.63s 16.22%  runtime.gcDrain
      0.10s     1% 43.68%      0.17s  1.69%  runtime.mapiterinit
      0.10s     1% 44.68%      0.24s  2.39%  runtime.pcvalue
      0.09s   0.9% 45.57%      0.09s   0.9%  runtime.markBits.isMarked (inline)
   ```

   ```
   [release@localhost crystalTest]$ go tool pprof heap-********-3
   File: service
   Type: inuse_space
   Time: Mar 2, 2022 at 1:36pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 397.27MB, 79.39% of 500.39MB total
   Dropped 244 nodes (cum <= 2.50MB)
   Showing top 20 nodes out of 98
         flat  flat%   sum%        cum   cum%
      70.01MB 13.99% 13.99%    86.51MB 17.29%  app/logic/character.newHero (inline)
      46.50MB  9.29% 23.28%    46.50MB  9.29%  app/logic/character.NewEmblem (inline)
      34.51MB  6.90% 30.18%    35.01MB  7.00%  app/logic/character.(*User).TaskTypeOnEvent
      26.50MB  5.30% 35.48%    46.50MB  9.29%  app/logic/character.newGem
      22.50MB  4.50% 39.97%    22.50MB  4.50%  app/logic/character.(*Handbook).add
         20MB  4.00% 43.97%       20MB  4.00%  app/logic/character.generateGemAttr
      19.55MB  3.91% 47.88%    66.05MB 13.20%  app/logic/character.(*EmblemM).addEmblem
      19.55MB  3.91% 51.78%    66.05MB 13.20%  app/logic/character.(*GemM).AddGem
      19.50MB  3.90% 55.68%    19.50MB  3.90%  app/protos/out/cl.(*FormationInfo).Clone (inline)
      17.01MB  3.40% 59.08%    17.01MB  3.40%  app/logic/character.(*Avatar).Add
      16.50MB  3.30% 62.38%    16.50MB  3.30%  app/logic/character.initHeroFromData (inline)
         16MB  3.20% 65.57%       16MB  3.20%  app/logic/character.newEquip (inline)
         15MB  3.00% 68.57%       15MB  3.00%  app/protos/out/cl.(*FormationArtifactInfo).Clone (inline)
      13.52MB  2.70% 71.27%   100.02MB 19.99%  app/logic/character.(*HeroM).Add
      8.50MB  1.70% 72.97%       43MB  8.59%  app/protos/out/cl.(*Formation).Clone
      8.10MB  1.62% 74.59%     8.10MB  1.62%  app/goxml.(*MonsterInfoManager).Load
      8.01MB  1.60% 76.19%    24.01MB  4.80%  app/logic/character.(*EquipM).Add
      5.50MB  1.10% 77.29%    48.50MB  9.69%  app/logic/character.(*FormationM).InitFormationByExistOne
      5.50MB  1.10% 78.39%     5.50MB  1.10%  app/logic/character.(*Hero).calcTotalAttr
      5.01MB  1.00% 79.39%    10.51MB  2.10%  app/logic/character.(*User).initModule
   ```

   ```
   [release@localhost crystalTest]$ go tool pprof allocs-********-3
   File: service
   Type: alloc_space
   Time: Mar 2, 2022 at 1:36pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 6876.76MB, 68.66% of 10016.37MB total
   Dropped 715 nodes (cum <= 50.08MB)
   Showing top 20 nodes out of 144
         flat  flat%   sum%        cum   cum%
   1192.61MB 11.91% 11.91%  1759.15MB 17.56%  app/logic/character.(*User).TaskTypeOnEvent
   625.03MB  6.24% 18.15%   625.03MB  6.24%  container/list.(*List).insertValue (inline)
   481.53MB  4.81% 22.95%   481.53MB  4.81%  app/protos/out/cl.(*TaskTypeProgress).Clone
   419.02MB  4.18% 27.14%  1001.54MB 10.00%  app/logic/character.(*User).pushMsg
   410.39MB  4.10% 31.23%   411.89MB  4.11%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
   391.20MB  3.91% 35.14%   417.70MB  4.17%  encoding/xml.(*Decoder).rawToken
   375.88MB  3.75% 38.89%   388.88MB  3.88%  app/logic/db/redisop.ClClient.SetSomeGemInfoMCallSKs
   355.36MB  3.55% 42.44%   355.36MB  3.55%  app/logic/db/redisop.ClClient.SetSomeEmblemInfoMCallSKs
   347.03MB  3.46% 45.91%   564.05MB  5.63%  context.WithDeadline
   308.67MB  3.08% 48.99%   313.67MB  3.13%  app/logic/db/redisop.DbClient.SetUserMCallSKs
   265.51MB  2.65% 51.64%  1267.05MB 12.65%  app/logic/character.(*User).SendCmdToGateway
   262.14MB  2.62% 54.25%   344.15MB  3.44%  app/logic/character.(*User).mergeResources
   257.04MB  2.57% 56.82%   257.04MB  2.57%  strings.(*Builder).WriteString
   252.52MB  2.52% 59.34%   457.02MB  4.56%  app/logic/character.(*User).FireCommonEvent
   216.52MB  2.16% 61.50%   217.02MB  2.17%  time.AfterFunc
   178.67MB  1.78% 63.29%   178.67MB  1.78%  app/logic/db/redisop.ClClient.SetSomeEquipmentMCallSKs
   173.50MB  1.73% 65.02%   533.02MB  5.32%  app/logic/helper/sync.(*MessageSync).append
   127.62MB  1.27% 66.29%   129.12MB  1.29%  app/logic/db/redisop.ClClient.SetSomeFormationMCallSKs
   126.01MB  1.26% 67.55%   410.52MB  4.10%  app/logic/character.(*Achieve).update
   110.52MB  1.10% 68.66%   214.26MB  2.14%  github.com/ivanabc/log4go.Logger.intLogf
   ```



#### 第一版 - 老号5000人（无发奖与养成逻辑），使用中等配置机器人，开启英雄，装备，宝石，纹章和水晶五个模块

1. robot数据

   ```
   开始时间：2022/03/03 11:10:38, 结束时间:2022/03/03 11:15:32, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:1225992， 平均每秒消息数:4170

   --------------------------

   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:11925,MSG_C2L_HeroChangeSave                         5       7            0.00%   71.43%
   协议:cmd:11921,MSG_C2L_HeroRevive                         13902   15308            1.13%   90.82%
   协议:cmd:13109,MSG_C2L_EmblemDecompose                    36118   36118            2.93%  100.00%
   协议:cmd:12301,MSG_C2L_EquipGet                            5000    5000            0.41%  100.00%
   协议:cmd:14701,MSG_C2L_CrystalAddHero                     16929   19411            1.38%   87.21%
   协议:cmd:13112,MSG_C2L_EmblemBlessing                     36093   36093            2.93%  100.00%
   协议:cmd:12309,MSG_C2L_EquipEnchant                       33243   41587            2.70%   79.94%
   协议:cmd:12307,MSG_C2L_EquipRefine                        34654   41428            2.82%   83.65%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                        22184   29082            1.80%   76.28%
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                   5000    5000            0.41%  100.00%
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp             32945   48449            2.68%   68.00%
   协议:cmd:11909,MSG_C2L_HeroBuySlot                        49950   93215            4.06%   53.59%
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement           48630   48630            3.95%  100.00%
   协议:cmd:11913,MSG_C2L_HeroDecompose                      19558   27035            1.59%   72.34%
   协议:cmd:12701,MSG_C2L_GetGems                             5000    5000            0.41%  100.00%
   协议:cmd:13103,MSG_C2L_EmblemWear                         20474   36366            1.66%   56.30%
   协议:cmd:12311,MSG_C2L_EquipEvolution                      1368    6963            0.11%   19.65%
   协议:cmd:11905,MSG_C2L_HeroStageUp                        12256   28978            1.00%   42.29%
   协议:cmd:11901,MSG_C2L_HeroList                            5005    5005            0.41%  100.00%
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                  15221   15221            1.24%  100.00%
   协议:cmd:12709,MSG_C2L_GemConvert                             0   73121            0.00%    0.00%
   协议:cmd:12703,MSG_C2L_GemWear                                0   72379            0.00%    0.00%
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                    8363   12966            0.68%   64.50%
   协议:cmd:11919,MSG_C2L_HeroBack                            9415    9415            0.76%  100.00%
   协议:cmd:12313,MSG_C2L_EquipDecompose                     47177   47177            3.83%  100.00%
   协议:cmd:12303,MSG_C2L_EquipWear                          36451   47080            2.96%   77.42%
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                  47905   47905            3.89%  100.00%
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus               51820   66521            4.21%   77.90%
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD                 13523   14067            1.10%   96.13%
   协议:cmd:12707,MSG_C2L_GemDecompose                           0   72369            0.00%    0.00%
   协议:cmd:12305,MSG_C2L_EquipStrength                          0   41515            0.00%    0.00%
   协议:cmd:13105,MSG_C2L_EmblemLevelUp                      36044   36044            2.93%  100.00%
   协议:cmd:11907,MSG_C2L_HeroStarUp                          4671    7113            0.38%   65.67%
   协议:cmd:11025,MSG_C2L_GM                                  5000    5000            0.41%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.41%  100.00%
   协议:cmd:13107,MSG_C2L_EmblemStageUp                      36167   36167            2.94%  100.00%
   协议:cmd:13116,MSG_C2L_EmblemRevive                       33477   36382            2.72%   92.02%
   协议:cmd:13101,MSG_C2L_EmblemGet                           5000    5000            0.41%  100.00%
   协议:cmd:10100,                                            5000    5000            0.41%  100.00%
   协议:cmd:12315,MSG_C2L_EquipRevive                        43130   46875            3.50%   92.01%
   协议正确数:801678 协议总数:1230992 正确比:65.12%

   --------------------------

   协议延迟分布
      协议名称                                              0-10  10-20  20-50  50-100 100-200 200-************ 1000-100000 
   协议:cmd:10100,                                          1.46%   7.08%  37.48%  34.10%  14.64%   5.24%   0.00%   0.00% 
   协议:cmd:13101,MSG_C2L_EmblemGet                        37.60%   8.44%  13.58%  11.20%  14.32%  14.86%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            87.66%   8.68%   3.66%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                 98.27%   0.98%   0.44%   0.18%   0.07%   0.06%   0.00%   0.00% 
   协议:cmd:11925,MSG_C2L_HeroChangeSave                   100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12315,MSG_C2L_EquipRevive                      98.52%   0.79%   0.45%   0.12%   0.09%   0.04%   0.00%   0.00% 
   协议:cmd:14705,MSG_C2L_CrystalUnlockSlot                98.43%   0.82%   0.50%   0.14%   0.07%   0.03%   0.00%   0.00% 
   协议:cmd:12307,MSG_C2L_EquipRefine                      98.93%   0.67%   0.39%   0.01%   0.01%   0.00%   0.00%   0.00% 
   协议:cmd:13105,MSG_C2L_EmblemLevelUp                    98.45%   0.84%   0.51%   0.12%   0.05%   0.04%   0.00%   0.00% 
   协议:cmd:11025,MSG_C2L_GM                               39.30%   8.06%  12.74%  12.20%  12.76%  14.94%   0.00%   0.00% 
   协议:cmd:11919,MSG_C2L_HeroBack                         98.36%   0.82%   0.57%   0.13%   0.07%   0.04%   0.00%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                      98.87%   0.71%   0.36%   0.04%   0.02%   0.00%   0.00%   0.00% 
   协议:cmd:12303,MSG_C2L_EquipWear                        98.40%   0.84%   0.50%   0.15%   0.08%   0.03%   0.00%   0.00% 
   协议:cmd:11921,MSG_C2L_HeroRevive                       98.24%   0.81%   0.54%   0.25%   0.09%   0.07%   0.00%   0.00% 
   协议:cmd:13109,MSG_C2L_EmblemDecompose                  98.46%   0.76%   0.55%   0.12%   0.06%   0.04%   0.00%   0.00% 
   协议:cmd:13107,MSG_C2L_EmblemStageUp                    98.48%   0.75%   0.56%   0.11%   0.07%   0.02%   0.00%   0.00% 
   协议:cmd:13103,MSG_C2L_EmblemWear                       98.14%   0.89%   0.58%   0.19%   0.13%   0.08%   0.00%   0.00% 
   协议:cmd:12313,MSG_C2L_EquipDecompose                   98.57%   0.70%   0.49%   0.13%   0.08%   0.02%   0.00%   0.00% 
   协议:cmd:14715,MSG_C2L_CrystalBlessingLevelUp           99.00%   0.58%   0.41%   0.01%   0.01%   0.00%   0.00%   0.00% 
   协议:cmd:13116,MSG_C2L_EmblemRevive                     98.52%   0.73%   0.53%   0.10%   0.07%   0.04%   0.00%   0.00% 
   协议:cmd:12309,MSG_C2L_EquipEnchant                     99.03%   0.63%   0.34%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14707,MSG_C2L_CrystalSpeedSlotCD               99.09%   0.55%   0.35%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:13112,MSG_C2L_EmblemBlessing                   98.53%   0.75%   0.49%   0.12%   0.08%   0.03%   0.00%   0.00% 
   协议:cmd:14701,MSG_C2L_CrystalAddHero                   97.93%   0.99%   0.61%   0.20%   0.18%   0.08%   0.00%   0.00% 
   协议:cmd:12311,MSG_C2L_EquipEvolution                   99.12%   0.66%   0.22%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12301,MSG_C2L_EquipGet                         38.04%   8.90%  12.80%  12.38%  12.72%  15.16%   0.00%   0.00% 
   协议:cmd:12701,MSG_C2L_GetGems                          39.60%   8.66%  12.96%  10.88%  12.76%  15.14%   0.00%   0.00% 
   协议:cmd:14709,MSG_C2L_CrystalGetAllData                40.58%   7.76%  12.36%  11.76%  12.72%  14.82%   0.00%   0.00% 
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus             98.49%   0.75%   0.47%   0.16%   0.08%   0.04%   0.00%   0.00% 
   协议:cmd:14703,MSG_C2L_CrystalRemoveHero                99.07%   0.63%   0.30%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:14713,MSG_C2L_CrystalActiveAchievement         98.55%   0.70%   0.53%   0.10%   0.09%   0.03%   0.00%   0.00% 
   协议:cmd:11901,MSG_C2L_HeroList                         39.60%   7.95%  12.45%  11.97%  12.81%  15.22%   0.00%   0.00% 
   协议:cmd:11907,MSG_C2L_HeroStarUp                       98.37%   1.07%   0.39%   0.13%   0.04%   0.00%   0.00%   0.00% 
   协议:cmd:11913,MSG_C2L_HeroDecompose                    98.20%   0.95%   0.55%   0.16%   0.11%   0.03%   0.00%   0.00% 
   协议:cmd:11909,MSG_C2L_HeroBuySlot                      98.01%   0.92%   0.62%   0.26%   0.12%   0.07%   0.00%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                      98.34%   0.76%   0.47%   0.23%   0.15%   0.07%   0.00%   0.00%
   ```
   
2. 游戏数据

   ```
   所有超时的left
   [2022/03/03 11:15:32.181138] gate(pck/s: 1978 sum: 1385855 left:0) logic db(pck/s: 0 sum: 5000 left:0) redis db(pck/s: 1857 sum: 42873 left:2512) [2022/03/03 11:15:31.283362] gate(pck/s: 12017 sum: 1383877 left:1978) logic db(pck/s: 0 sum: 5000 left:0) redis db(pck/s: 711 sum: 41016 left:2311) 

   --------------------------

   logic服务器启动时间:
   logic账号总数:

   --------------------------

   协议处理超时分布
      协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       10104(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11919(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       13109(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11905(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:84(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12703(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric
   ![](./pic/crystal-********-1.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Mar 3, 2022 at 11:13am (CST)
   Duration: 30.02s, Total samples = 11.34s (37.77%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 4.99s, 44.00% of 11.34s total
   Dropped 507 nodes (cum <= 0.06s)
   Showing top 20 nodes out of 258
         flat  flat%   sum%        cum   cum%
      0.97s  8.55%  8.55%      1.95s 17.20%  runtime.scanobject
      0.55s  4.85% 13.40%      0.60s  5.29%  syscall.Syscall
      0.48s  4.23% 17.64%      0.53s  4.67%  runtime.findObject
      0.34s  3.00% 20.63%      1.34s 11.82%  runtime.mallocgc
      0.30s  2.65% 23.28%      0.30s  2.65%  runtime.futex
      0.27s  2.38% 25.66%      0.29s  2.56%  runtime.mapaccess1_fast32
      0.22s  1.94% 27.60%      0.27s  2.38%  runtime.heapBitsSetType
      0.21s  1.85% 29.45%      0.21s  1.85%  runtime.epollwait
      0.20s  1.76% 31.22%      0.20s  1.76%  runtime.heapBits.bits (inline)
      0.20s  1.76% 32.98%      0.58s  5.11%  runtime.selectgo
      0.19s  1.68% 34.66%      0.27s  2.38%  runtime.mapiternext
      0.19s  1.68% 36.33%      0.19s  1.68%  runtime.nextFreeFast (inline)
      0.15s  1.32% 37.65%      0.15s  1.32%  runtime.memclrNoHeapPointers
      0.14s  1.23% 38.89%      0.14s  1.23%  runtime.markBits.isMarked (inline)
      0.12s  1.06% 39.95%      0.12s  1.06%  runtime.memmove
      0.10s  0.88% 40.83%      0.11s  0.97%  runtime.(*itabTableType).find
      0.09s  0.79% 41.62%      0.12s  1.06%  runtime.mapassign_fast32
      0.09s  0.79% 42.42%      0.09s  0.79%  runtime.nanotime
      0.09s  0.79% 43.21%      0.11s  0.97%  runtime.sellock
      0.09s  0.79% 44.00%      0.09s  0.79%  runtime.unlock2
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Mar 3, 2022 at 11:13am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 429.21MB, 70.69% of 607.18MB total
   Dropped 271 nodes (cum <= 3.04MB)
   Showing top 20 nodes out of 108
         flat  flat%   sum%        cum   cum%
      44.50MB  7.33%  7.33%    44.50MB  7.33%  app/protos/out/cl.(*Formation).Unmarshal
      40.50MB  6.67% 14.00%       78MB 12.85%  app/logic/db/redisop.ClClient.GetSomeGemInfoByReply
         31MB  5.11% 19.11%       36MB  5.93%  app/logic/character.newHero (inline)
         29MB  4.78% 23.88%    37.50MB  6.18%  app/protos/out/cl.(*GemInfo).Unmarshal
      25.50MB  4.20% 28.08%    25.50MB  4.20%  app/logic/db/redisop.ClClient.GetSomeHeroBodyByReply
      23.51MB  3.87% 31.95%    28.51MB  4.69%  app/protos/in/db.(*ModuleAttr).Unmarshal
      23.50MB  3.87% 35.82%    23.50MB  3.87%  app/protos/out/cl.(*Handbooks).Unmarshal
      23.05MB  3.80% 39.62%    37.55MB  6.18%  app/logic/character.(*EquipM).Add
      21.50MB  3.54% 43.16%       40MB  6.59%  app/logic/db/redisop.ClClient.GetSomeEmblemInfoByReply
      20.54MB  3.38% 46.54%    20.54MB  3.38%  app/logic/character.(*GemM).Load
      20.50MB  3.38% 49.92%    20.50MB  3.38%  app/logic/character.initHeroFromData (inline)
      20.04MB  3.30% 53.22%    20.04MB  3.30%  app/logic/character.(*EmblemM).Load
      18.50MB  3.05% 56.27%    18.50MB  3.05%  app/protos/out/cl.(*EmblemInfo).Unmarshal
      17.04MB  2.81% 59.07%    53.04MB  8.74%  app/logic/character.(*HeroM).Add
         16MB  2.64% 61.71%       16MB  2.64%  app/logic/db/redisop.ClClient.GetSomeEquipmentByReply
      14.50MB  2.39% 64.10%    14.50MB  2.39%  app/logic/character.newEquip (inline)
      10.51MB  1.73% 65.83%    20.02MB  3.30%  app/logic/character.(*User).initModule
         10MB  1.65% 67.48%       10MB  1.65%  app/logic/character.(*User).TaskTypeOnEvent
         10MB  1.65% 69.12%       10MB  1.65%  app/protos/out/cl.(*CrystalBlessingAchieve).Unmarshal
      9.50MB  1.56% 70.69%       54MB  8.89%  app/logic/db/redisop.ClClient.GetSomeFormationByReply
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Mar 3, 2022 at 11:13am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 3173.42MB, 53.22% of 5962.41MB total
   Dropped 691 nodes (cum <= 29.81MB)
   Showing top 20 nodes out of 212
         flat  flat%   sum%        cum   cum%
   309.16MB  5.19%  5.19%   314.66MB  5.28%  app/logic/db/redisop.DbClient.SetUserMCallSKs
   289.27MB  4.85% 10.04%   290.27MB  4.87%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
   245.04MB  4.11% 14.15%   250.04MB  4.19%  gitlab.qdream.com/kit/sea/redis/resp.readBulkStr
   241.63MB  4.05% 18.20%   257.63MB  4.32%  app/logic/character.(*User).mergeResources
   229.72MB  3.85% 22.05%   231.22MB  3.88%  app/logic/db/redisop.ClClient.SetSomeEquipmentMCallSKs
   207.52MB  3.48% 25.53%   342.53MB  5.74%  context.WithDeadline
   200.76MB  3.37% 28.90%   450.80MB  7.56%  gitlab.qdream.com/kit/sea/redis/resp.readArray
   197.17MB  3.31% 32.21%   217.17MB  3.64%  encoding/xml.(*Decoder).rawToken
   156.03MB  2.62% 34.82%   156.03MB  2.62%  strings.(*Builder).WriteString
   156.01MB  2.62% 37.44%   156.01MB  2.62%  container/list.(*List).insertValue (inline)
   141.01MB  2.37% 39.80%   141.01MB  2.37%  gitlab.qdream.com/kit/sea/redis.messageToReply
   135.01MB  2.26% 42.07%   135.01MB  2.26%  time.AfterFunc
   111.51MB  1.87% 43.94%   111.51MB  1.87%  app/protos/out/cl.(*TaskTypeProgress).Clone
      92.01MB  1.54% 45.48%    92.01MB  1.54%  app/protos/out/cl.(*HeroBody).Clone
      89.51MB  1.50% 46.98%   177.52MB  2.98%  app/logic/character.(*User).TaskTypeOnEvent
      76.55MB  1.28% 48.27%    76.55MB  1.28%  app/logic/character.(*Emblem).GetStrengthReturnRes
      75.51MB  1.27% 49.53%   130.09MB  2.18%  github.com/ivanabc/log4go.Logger.intLogf
      74.50MB  1.25% 50.78%   213.01MB  3.57%  app/logic/character.(*User).pushMsg
         73MB  1.22% 52.01%       73MB  1.22%  app/protos/out/cl.(*GemAttr).Clone
      72.51MB  1.22% 53.22%    72.51MB  1.22%  app/protos/out/cl.(*Equipment).Clone
   ```