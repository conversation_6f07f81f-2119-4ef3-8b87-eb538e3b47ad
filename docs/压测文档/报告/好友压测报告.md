## 好友测报告

1. 第一版

2. Robot数据

   ```shell
   压测基础情况
   开始时间：2021/06/02 14:45:08, 结束时间:2021/06/02 14:50:01, 耗时:293(s)
   登录成功机器人总数：5000
   总消息数:1894642， 平均每秒消息数:6466
   协议正确率分布
   协议                                                   正确    总数     正确比   
   协议:cmd:10100,                                         5000   5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                            5000   5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                   8034 133156   6.03%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   6416 143324   4.48%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  59971 132255  45.34%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                416304 417121  99.80%
   协议:cmd:11503,MSG_C2L_FriendAdd                        142495 263505  54.08%
   协议:cmd:11518,MSG_C2L_FriendRecommend                  130485 132782  98.27%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               46329 131832  35.14%
   协议:cmd:11506,MSG_C2L_FriendConfirm                    125866 132441  95.04%
   协议:cmd:11520,MSG_C2L_FriendSearch                     8057 132752   6.07%
   协议:cmd:11501,MSG_C2L_FriendInfo                       137675 137675 100.00%
   协议:cmd:11508,MSG_C2L_FriendDelete                     114725 132799  86.39%
   协议正确数:1206357 协议总数:1899642 正确比:63.50%
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-500 500-1000 1000-100000- 
   协议:cmd:10100,                                          0.24%   3.10%   9.06%  21.84%  65.76%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   99.08%   0.80%   0.12%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                             4.88%   9.82%  13.74%  29.84%  41.72%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   98.22%   0.89%   0.86%   0.00%   0.03%   0.00%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  96.43%   2.61%   0.96%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                96.66%   1.59%   1.74%   0.00%   0.01%   0.00%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        97.61%   1.34%   1.05%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  96.55%   2.48%   0.97%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     96.61%   2.37%   1.02%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               96.20%   2.70%   1.11%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    96.60%   2.42%   0.99%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       96.61%   2.39%   0.99%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     96.49%   2.50%   1.01%   0.00%   0.00%   0.00%   0.00% 
   ```

3. 游戏数据

   ```shell
   \n--------------------------\n
   所有超时的left
   [2021/06/02 14:46:10.382484] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:46:10.366488939 +0800 CST msgs count per second: gate(pck/s: 6492 sum: 450450  left:138) logic db(pck/s: 92 sum: 10489 left:0) redis db(pck/s: 93 sum: 15588 left:0) 
   [2021/06/02 14:46:29.069759] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:46:29.062250607 +0800 CST msgs count per second: gate(pck/s: 4713 sum: 578769  left:160) logic db(pck/s: 69 sum: 12141 left:0) redis db(pck/s: 70 sum: 17271 left:0) 
   [2021/06/02 14:46:52.172183] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:46:52.162219081 +0800 CST msgs count per second: gate(pck/s: 4652 sum: 737725  left:121) logic db(pck/s: 69 sum: 14212 left:0) redis db(pck/s: 70 sum: 24380 left:0) 
   [2021/06/02 14:46:55.171140] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:46:55.162228281 +0800 CST msgs count per second: gate(pck/s: 4648 sum: 761913  left:105) logic db(pck/s: 77 sum: 14493 left:0) redis db(pck/s: 78 sum: 24666 left:0) 
   [2021/06/02 14:47:11.072578] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:47:11.062213327 +0800 CST msgs count per second: gate(pck/s: 4697 sum: 867973  left:103) logic db(pck/s: 68 sum: 15938 left:0) redis db(pck/s: 69 sum: 26134 left:1) 
   [2021/06/02 14:47:22.173740] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:47:22.162220917 +0800 CST msgs count per second: gate(pck/s: 4793 sum: 945127  left:139) logic db(pck/s: 66 sum: 16901 left:0) redis db(pck/s: 67 sum: 27115 left:0) 
   [2021/06/02 14:47:35.070346] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:47:35.062204146 +0800 CST msgs count per second: gate(pck/s: 4548 sum: 1036460  left:137) logic db(pck/s: 59 sum: 18114 left:1) redis db(pck/s: 838 sum: 29807 left:0) 
   [2021/06/02 14:47:47.070753] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:47:47.062411491 +0800 CST msgs count per second: gate(pck/s: 4703 sum: 1118330  left:175) logic db(pck/s: 66 sum: 19232 left:0) redis db(pck/s: 67 sum: 34484 left:0) 
   [2021/06/02 14:48:02.069022] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:48:02.062425574 +0800 CST msgs count per second: gate(pck/s: 2995 sum: 1219046  left:137) logic db(pck/s: 49 sum: 20571 left:0) redis db(pck/s: 50 sum: 35844 left:0) 
   [2021/06/02 14:48:14.070897] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:48:14.062678247 +0800 CST msgs count per second: gate(pck/s: 5921 sum: 1305704  left:160) logic db(pck/s: 71 sum: 21667 left:0) redis db(pck/s: 72 sum: 36956 left:0) 
   [2021/06/02 14:48:29.071965] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:48:29.062237383 +0800 CST msgs count per second: gate(pck/s: 4528 sum: 1406779  left:189) logic db(pck/s: 71 sum: 23075 left:0) redis db(pck/s: 655 sum: 43284 left:0) 
   [2021/06/02 14:48:41.071738] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:48:41.06223871 +0800 CST msgs count per second: gate(pck/s: 4568 sum: 1488313  left:185) logic db(pck/s: 78 sum: 24211 left:0) redis db(pck/s: 79 sum: 44536 left:0) 
   [2021/06/02 14:48:44.072623] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:48:44.062189062 +0800 CST msgs count per second: gate(pck/s: 5763 sum: 1512613  left:167) logic db(pck/s: 75 sum: 24494 left:0) redis db(pck/s: 76 sum: 44823 left:1) 
   [2021/06/02 14:48:53.070087] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:48:53.062197748 +0800 CST msgs count per second: gate(pck/s: 7139 sum: 1572634  left:315) logic db(pck/s: 68 sum: 25291 left:0) redis db(pck/s: 69 sum: 45632 left:0) 
   [2021/06/02 14:48:56.069664] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:48:56.062211133 +0800 CST msgs count per second: gate(pck/s: 4571 sum: 1594449  left:109) logic db(pck/s: 62 sum: 25545 left:0) redis db(pck/s: 63 sum: 45891 left:0) 
   [2021/06/02 14:49:08.070363] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:08.062250984 +0800 CST msgs count per second: gate(pck/s: 4631 sum: 1676026  left:161) logic db(pck/s: 66 sum: 26594 left:0) redis db(pck/s: 67 sum: 46956 left:0) 
   [2021/06/02 14:49:20.070305] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:20.062295017 +0800 CST msgs count per second: gate(pck/s: 4724 sum: 1757647  left:159) logic db(pck/s: 69 sum: 27708 left:0) redis db(pck/s: 198 sum: 53086 left:0) 
   [2021/06/02 14:49:23.074382] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:23.062181271 +0800 CST msgs count per second: gate(pck/s: 6878 sum: 1779092  left:527) logic db(pck/s: 52 sum: 27978 left:0) redis db(pck/s: 53 sum: 53361 left:0) 
   [2021/06/02 14:49:38.078069] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:38.06245723 +0800 CST msgs count per second: gate(pck/s: 4686 sum: 1882759  left:101) logic db(pck/s: 73 sum: 29409 left:0) redis db(pck/s: 74 sum: 54817 left:3) 
   [2021/06/02 14:49:43.190070] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:43.162300196 +0800 CST msgs count per second: gate(pck/s: 9217 sum: 1920651  left:135) logic db(pck/s: 66 sum: 29870 left:0) redis db(pck/s: 68 sum: 55287 left:0) 
   [2021/06/02 14:49:47.072558] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:47.062305639 +0800 CST msgs count per second: gate(pck/s: 4638 sum: 1945033  left:197) logic db(pck/s: 52 sum: 30191 left:0) redis db(pck/s: 53 sum: 55614 left:0) 
   [2021/06/02 14:49:53.078629] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:53.062235708 +0800 CST msgs count per second: gate(pck/s: 7220 sum: 1986183  left:148) logic db(pck/s: 76 sum: 30756 left:0) redis db(pck/s: 77 sum: 56189 left:2) 
   [2021/06/02 14:49:58.176330] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:49:58.162228393 +0800 CST msgs count per second: gate(pck/s: 4651 sum: 2021974  left:192) logic db(pck/s: 79 sum: 31278 left:0) redis db(pck/s: 80 sum: 56720 left:0) 
   [2021/06/02 14:50:01.180428] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:50:01.162227996 +0800 CST msgs count per second: gate(pck/s: 4632 sum: 2041012  left:191) logic db(pck/s: 70 sum: 31571 left:0) redis db(pck/s: 71 sum: 57018 left:0) 
   [2021/06/02 14:50:02.068587] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-06-02 14:50:02.062203512 +0800 CST msgs count per second: gate(pck/s: 8733 sum: 2049745  left:0) logic db(pck/s: 54 sum: 31625 left:0) redis db(pck/s: 3105 sum: 60123 left:1959) 
        协议名称                                    0-10   10-20   20-50   50-100  100-1000 1000-10000 
   协议: 11514(   5)                               20.00%  80.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11506(  76)                               30.26%  39.47%  30.26%   0.00%   0.00%   0.00% 
   协议: 11516(   2)                               50.00%   0.00%  50.00%   0.00%   0.00%   0.00% 
   协议: 11508(  83)                               32.53%  38.55%  28.92%   0.00%   0.00%   0.00% 
   协议: 11510(  49)                               34.69%  46.94%  18.37%   0.00%   0.00%   0.00% 
   协议:100022( 323)                                0.00%  46.75%  52.63%   0.62%   0.00%   0.00% 
   协议: 11512(  42)                               26.19%  30.95%  42.86%   0.00%   0.00%   0.00% 
   协议: 11503(  60)                               35.00%  41.67%  23.33%   0.00%   0.00%   0.00% 
   ```

   

2. 第二版

   robot

   ```shell
   2021-06-03 16:40:01 压测数据分析
   压测基础情况
   开始时间：2021/06/03 16:23:14, 结束时间:2021/06/03 16:28:08, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:2308229， 平均每秒消息数:7851
   \n--------------------------\n
   协议正确率分布
   协议                                                   正确    总数     正确比   
   协议:cmd:10100,                                         5000   5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                            5000   5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                   67669 133090  50.84%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   421934 571600  73.82%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  64765 132301  48.95%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                400807 402945  99.47%
   协议:cmd:11503,MSG_C2L_FriendAdd                        139959 261191  53.58%
   协议:cmd:11518,MSG_C2L_FriendRecommend                  128611 132349  97.18%
   协议:cmd:11506,MSG_C2L_FriendConfirm                    125617 133039  94.42%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               61387 132822  46.22%
   协议:cmd:11520,MSG_C2L_FriendSearch                     7968 132966   5.99%
   协议:cmd:11508,MSG_C2L_FriendDelete                     128319 133144  96.38%
   协议:cmd:11501,MSG_C2L_FriendInfo                       137782 137782 100.00%
   协议正确数:1694818 协议总数:2313229 正确比:73.27%
   \n--------------------------\n
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-500 500-1000 1000-100000- 
   协议:cmd:10100,                                         95.76%   4.00%   0.24%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                             9.78%  26.54%  60.66%   3.02%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   77.44%   5.62%   9.65%   6.23%   1.06%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   84.02%   9.25%   6.33%   0.33%   0.08%   0.00%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  77.18%   5.61%   9.83%   6.34%   1.04%   0.00%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                83.61%   7.95%   5.77%   2.27%   0.40%   0.00%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        81.42%   9.38%   7.70%   1.25%   0.24%   0.00%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  76.86%   5.50%   9.98%   6.52%   1.14%   0.00%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     76.27%   6.05%  10.04%   6.60%   1.04%   0.00%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               77.04%   5.58%   9.89%   6.44%   1.05%   0.00%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    77.08%   5.49%   9.81%   6.54%   1.09%   0.00%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     77.14%   5.51%   9.83%   6.47%   1.06%   0.00%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       77.96%   5.45%   9.32%   6.24%   1.03%   0.00%   0.00% 
   \n---------------------\n
   
   ```

   游戏服情况

   ```shell
   所有超时的left
   
        协议名称                                    0-10   10-20   20-50   50-100  100-1000 1000-10000 
   协议:100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11522(   1)                                0.00%   0.00%  100.00%   0.00%   0.00%   0.00% 
   协议: 11514(  59)                                0.00%   8.47%  35.59%  45.76%  10.17%   0.00% 
   协议:100007(   1)                                0.00%   0.00%   0.00%  100.00%   0.00%   0.00% 
   协议: 11506(  87)                                0.00%   3.45%  31.03%  45.98%  19.54%   0.00% 
   协议: 11516(  42)                                4.76%  16.67%  38.10%  35.71%   4.76%   0.00% 
   协议: 11508(  69)                                0.00%   2.90%  39.13%  42.03%  15.94%   0.00% 
   协议: 11518(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 10100(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11003(   3)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00% 
   协议:100002(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11501(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11510(  36)                                0.00%   0.00%  33.33%  52.78%  13.89%   0.00% 
   协议:100003(   6)                               83.33%  16.67%   0.00%   0.00%   0.00%   0.00% 
   协议:100022( 434)                                0.00%   2.07%  28.57%  43.09%  26.27%   0.00% 
   协议: 11503(  36)                                2.78%   8.33%  41.67%  33.33%  13.89%   0.00% 
   协议: 11512(  36)                                0.00%   0.00%  19.44%  55.56%  25.00%   0.00% 
   
   ```
   
   metric
   
   ![friend-20210603](./pic/friend-20210603.png)





### 第三版

1. robot

   ```shell
   2021-06-04 16:47:06 压测数据分析
   压测基础情况
   开始时间：2021/06/04 16:42:05, 结束时间:2021/06/04 16:46:58, 耗时:293(s)
   登录成功机器人总数：5000
   总消息数:2538358， 平均每秒消息数:8663
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     35146   53077  66.22%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    431045  526054  81.94%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    46444   93623  49.61%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 440386  460036  95.73%
   协议:cmd:11503,MSG_C2L_FriendAdd                         260446  401667  64.84%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   277022  302372  91.62%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     127261  132846  95.80%
   协议:cmd:11520,MSG_C2L_FriendSearch                      119742  133094  89.97%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 48367   80066  60.41%
   协议:cmd:11501,MSG_C2L_FriendInfo                        137753  137753 100.00%
   协议:cmd:11508,MSG_C2L_FriendDelete                      204346  212770  96.04%
   协议正确数:2137958 协议总数:2543358 正确比:84.06%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-500 500-1000 1000-100000- 
   协议:cmd:10100,                                          0.82%   6.50%  34.22%  31.74%  26.72%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            81.50%  13.08%   5.26%   0.12%   0.04%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   33.87%   7.15%  25.68%  25.30%   8.00%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   34.68%  10.45%  34.61%  17.76%   2.51%   0.00%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  33.74%   6.66%  25.09%  26.09%   8.43%   0.00%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                37.25%  12.14%  29.73%  17.04%   3.83%   0.00%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        33.49%   9.67%  33.24%  20.36%   3.24%   0.00%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  32.49%   6.80%  25.05%  26.79%   8.87%   0.00%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    33.94%   6.50%  24.78%  26.25%   8.53%   0.00%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     33.59%   6.72%  25.25%  26.09%   8.35%   0.00%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               33.93%   7.15%  25.97%  25.30%   7.65%   0.00%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       32.89%   6.90%  25.61%  26.35%   8.24%   0.00%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     33.67%   6.67%  25.03%  26.14%   8.48%   0.00%   0.00% 
   
   ```

2. logic

   ```shell
   gateway堆积消息明显
   
   协议处理超时分布
        协议名称                                    0-10   10-20   20-50   50-100  100-1000 1000-10000 
   协议: 11522(   3)                               33.33%   0.00%  66.67%   0.00%   0.00%   0.00% 
   协议: 11514(   9)                                0.00%   0.00%  22.22%  22.22%  55.56%   0.00% 
   协议:100007(   1)                                0.00%   0.00%   0.00%   0.00%  100.00%   0.00% 
   协议: 11506(  53)                                0.00%   1.89%   3.77%  45.28%  49.06%   0.00% 
   协议: 11516(  12)                               16.67%   8.33%  25.00%  50.00%   0.00%   0.00% 
   协议: 11508(  84)                                0.00%   1.19%   3.57%  38.10%  57.14%   0.00% 
   协议: 11518(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:100002(   3)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11510(  16)                                0.00%   0.00%  12.50%  37.50%  50.00%   0.00% 
   协议:100003(   4)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11520(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00% 
   协议:100022( 334)                                0.00%   1.20%   5.99%  28.14%  64.67%   0.00% 
   协议: 11503(  24)                                4.17%   8.33%  12.50%  50.00%  25.00%   0.00% 
   协议: 11512(  22)                                4.55%   0.00%   0.00%  50.00%  45.45%   0.00% 
   ```

3. metric

   ![friend-20210604](./pic/friend-20210604.png)

4. pprofile

   参考 profile/friend-allocs-20210604 profile/friend-heap-20210604 profile/friend-profile-20210604

   5. 主要的问题

      1. 延迟时间有超过100ms的
      2. gc次数超过5次

    6. 问题分析

       主要的出现的情况还是消耗在gc扫描的时间上，

       ```shell
       [jenkins@new230 pprof]$ go tool pprof profile-************** 
       File: service
       Type: cpu
       Time: Jun 4, 2021 at 4:42pm (CST)
       Duration: 30.19s, Total samples = 25.28s (83.75%)
       Entering interactive mode (type "help" for commands, "o" for options)
       (pprof) top
       Showing nodes accounting for 9330ms, 36.91% of 25280ms total
       Dropped 456 nodes (cum <= 126.40ms)
       Showing top 10 nodes out of 240
             flat  flat%   sum%        cum   cum%
           3700ms 14.64% 14.64%     5610ms 22.19%  runtime.scanobject
            810ms  3.20% 17.84%      950ms  3.76%  runtime.findObject
            800ms  3.16% 21.00%     2270ms  8.98%  runtime.mallocgc
            750ms  2.97% 23.97%     1140ms  4.51%  runtime.mapiternext
            710ms  2.81% 26.78%      710ms  2.81%  runtime.epollwait
            660ms  2.61% 29.39%      710ms  2.81%  syscall.Syscall
            590ms  2.33% 31.72%      900ms  3.56%  app/protos/in/db.(*Friend).Clone
            520ms  2.06% 33.78%      720ms  2.85%  runtime.mapaccess1_fast64
            410ms  1.62% 35.40%      410ms  1.62%  runtime.markBits.isMarked (inline)
            380ms  1.50% 36.91%      380ms  1.50%  runtime.futex
       
       ```

       内存的主要消耗实在hero那块的属性计算部分

       ```shell
       [jenkins@new230 pprof]$ go tool pprof heap-************** 
       File: service
       Type: inuse_space
       Time: Jun 4, 2021 at 4:42pm (CST)
       Entering interactive mode (type "help" for commands, "o" for options)
       (pprof) top
       Showing nodes accounting for 671.67MB, 73.38% of 915.32MB total
       Dropped 185 nodes (cum <= 4.58MB)
       Showing top 10 nodes out of 69
             flat  flat%   sum%        cum   cum%
         133.06MB 14.54% 14.54%   133.06MB 14.54%  app/logic/character.(*Hero).updateHeroAttr
         123.07MB 13.45% 27.98%   123.07MB 13.45%  app/logic/character.(*Hero).updateBaseAttr
         104.50MB 11.42% 39.40%   361.13MB 39.45%  app/logic/character.(*Hero).loadAttr
          85.53MB  9.34% 48.74%    86.03MB  9.40%  app/logic/db/redisop.ClClient.GetSomeEquipmentByReply
          59.06MB  6.45% 55.20%    59.06MB  6.45%  app/protos/in/db.(*Friends).Unmarshal
          55.43MB  6.06% 61.25%    55.43MB  6.06%  app/logic/character.(*EquipM).Load
          34.50MB  3.77% 65.02%    63.50MB  6.94%  app/logic/db/redisop.ClClient.GetSomeGemInfoByReply
             28MB  3.06% 68.08%   389.14MB 42.51%  app/logic/character.NewHeroFromData
          26.50MB  2.90% 70.98%    26.50MB  2.90%  app/protos/out/cl.(*HeroBody).Clone
             22MB  2.40% 73.38%    34.50MB  3.77%  app/logic/db/redisop.ClClient.GetSomeEmblemInfoByReply
       
       ```

    7. 优化措施

       - 等英雄计算属性完成之后在重新进行一次压测。

#### 第四版

1. robot数据

   ```shell
   [roobot@172-21-173-36 bin]$ ./robot_benchmark_logic.sh friend robota 5000 1000 300
   开启friend模块
   清空日志信息
   Mon Jun  7 16:28:43 CST 2021
   开始压测
   Mon Jun  7 16:33:43 CST 2021
   压测结束
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh 
   2021-06-07 16:34:02 压测数据分析
   压测基础情况
   开始时间：2021/06/07 16:28:49, 结束时间:2021/06/07 16:33:43, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:2376003， 平均每秒消息数:8081
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     37657   53286  70.67%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    252083  328413  76.76%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    47221   97288  48.54%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 484910  487518  99.47%
   协议:cmd:11503,MSG_C2L_FriendAdd                         271851  419013  64.88%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   288014  306475  93.98%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 36691   63222  58.04%
   协议:cmd:11520,MSG_C2L_FriendSearch                      116364  132676  87.71%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     129630  132541  97.80%
   协议:cmd:11501,MSG_C2L_FriendInfo                        137329  137329 100.00%
   协议:cmd:11508,MSG_C2L_FriendDelete                      182394  213242  85.53%
   协议正确数:1994144 协议总数:2381003 正确比:83.75%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-500 500-1000 1000-100000- 
   协议:cmd:10100,                                         36.54%  57.28%   5.10%   1.08%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.72%   0.72%   3.56%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   91.77%   3.75%   2.89%   1.53%   0.06%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   93.66%   3.84%   2.25%   0.21%   0.03%   0.00%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  92.00%   3.51%   2.94%   1.46%   0.08%   0.00%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                92.54%   4.27%   2.65%   0.49%   0.04%   0.00%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        93.22%   4.12%   2.34%   0.31%   0.01%   0.00%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  91.71%   3.69%   2.97%   1.56%   0.07%   0.00%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               91.30%   3.77%   3.18%   1.65%   0.10%   0.00%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     91.90%   3.59%   2.96%   1.46%   0.09%   0.00%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    92.11%   3.60%   2.83%   1.40%   0.06%   0.00%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     91.76%   3.66%   2.96%   1.54%   0.08%   0.00%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       92.17%   3.57%   2.80%   1.38%   0.07%   0.00%   0.00% 
   
   ---------------------
   
   ```

2. logic数据

   ```shell
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh 
   
   --------------------------
   left 在可控范围内，
   --------------------------
   
   logic服务器启动时间:1.296481319s
   
   --------------------------
   
   协议处理超时分布
        协议名称                                    0-10   10-20   20-50   50-100  100-1000 1000-10000 
   协议:100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11522(   2)                               50.00%   0.00%  50.00%   0.00%   0.00%   0.00% 
   协议: 11514(  28)                               21.43%  21.43%  28.57%  25.00%   3.57%   0.00% 
   协议:100007(   1)                                0.00%   0.00%   0.00%   0.00%  100.00%   0.00% 
   协议: 11506(  77)                               22.08%  19.48%  32.47%  24.68%   1.30%   0.00% 
   协议: 11516(  16)                               18.75%  31.25%  25.00%  25.00%   0.00%   0.00% 
   协议: 11508( 108)                               23.15%  12.04%  20.37%  42.59%   1.85%   0.00% 
   协议: 11518(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议: 11510(  31)                               22.58%  19.35%  19.35%  32.26%   6.45%   0.00% 
   协议:100022( 372)                                0.00%  22.31%  30.11%  36.29%  11.29%   0.00% 
   协议: 11512(  25)                               24.00%  16.00%  20.00%  36.00%   4.00%   0.00% 
   协议: 11503(  42)                               14.29%  28.57%  26.19%  30.95%   0.00%   0.00% 
   
   ```

3. 分析

   ```shell
   [jenkins@new230 pprof]$ go tool profile-************** 
   go tool: bad tool name "profile-**************"
   [jenkins@new230 pprof]$ go tool pprof profile-************** 
   File: service
   Type: cpu
   Time: Jun 7, 2021 at 4:28pm (CST)
   Duration: 30.02s, Total samples = 12.71s (42.33%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 4380ms, 34.46% of 12710ms total
   Dropped 404 nodes (cum <= 63.55ms)
   Showing top 10 nodes out of 239
         flat  flat%   sum%        cum   cum%
       1220ms  9.60%  9.60%     1860ms 14.63%  runtime.scanobject
        560ms  4.41% 14.00%      660ms  5.19%  syscall.Syscall
        460ms  3.62% 17.62%      460ms  3.62%  runtime.futex
        440ms  3.46% 21.09%     1190ms  9.36%  runtime.mallocgc
        390ms  3.07% 24.15%      390ms  3.07%  runtime.epollwait
        340ms  2.68% 26.83%      410ms  3.23%  app/logic/character.(*User).ResetDaily
        290ms  2.28% 29.11%      510ms  4.01%  runtime.mapiternext
        250ms  1.97% 31.08%      280ms  2.20%  runtime.findObject
        220ms  1.73% 32.81%      260ms  2.05%  app/logic/character.(*User).IsFunctionOpen
        210ms  1.65% 34.46%      300ms  2.36%  runtime.heapBitsSetType
   (pprof) quit
   
   ```

   ```shell
   [jenkins@new230 pprof]$ go tool pprof heap-************** 
   File: service
   Type: inuse_space
   Time: Jun 7, 2021 at 4:29pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 116.82MB, 63.15% of 185MB total
   Dropped 76 nodes (cum <= 0.93MB)
   Showing top 10 nodes out of 161
         flat  flat%   sum%        cum   cum%
      74.64MB 40.35% 40.35%    74.64MB 40.35%  app/protos/in/db.(*Friends).Unmarshal
      14.03MB  7.58% 47.93%    14.03MB  7.58%  app/logic/activity/friend.(*Friends).init
       8.50MB  4.59% 52.53%     8.50MB  4.59%  app/logic/character.newShop
       3.50MB  1.89% 54.42%       10MB  5.41%  app/logic/db/redisop.DbClient.GetUserByReply
       3.50MB  1.89% 56.31%     3.50MB  1.89%  app/logic/character.(*User).NewUserSnapshot
       2.64MB  1.43% 57.74%     3.18MB  1.72%  compress/flate.NewWriter
       2.50MB  1.35% 59.09%        4MB  2.16%  app/logic/command/friend.getOnlineRecommendListByLevel
       2.50MB  1.35% 60.44%        5MB  2.70%  app/logic/mail.NewBox
       2.50MB  1.35% 61.79%     2.50MB  1.35%  app/protos/out/cl.(*DailyTask).Unmarshal
       2.50MB  1.35% 63.15%     2.50MB  1.35%  app/protos/out/cl.(*AchieveInfo).Unmarshal
   
   ```

   

#### 第五版

1. robot数据

   ```shell
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh 
   2021-06-07 18:04:05 压测数据分析
   压测基础情况
   开始时间：2021/06/07 17:54:30, 结束时间:2021/06/07 17:59:24, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:2389286， 平均每秒消息数:8126
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11514,MSG_C2L_FriendSendLike                     37433   53347  70.17%
   协议:cmd:11516,MSG_C2L_FriendRecvLike                    255708  331826  77.06%
   协议:cmd:11510,MSG_C2L_FriendBlacklist                    46969   97135  48.35%
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                 488617  491224  99.47%
   协议:cmd:11503,MSG_C2L_FriendAdd                         275071  423218  65.00%
   协议:cmd:11518,MSG_C2L_FriendRecommend                   291251  306830  94.92%
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist                 36769   63294  58.09%
   协议:cmd:11506,MSG_C2L_FriendConfirm                     130623  133500  97.84%
   协议:cmd:11520,MSG_C2L_FriendSearch                      116194  132515  87.68%
   协议:cmd:11508,MSG_C2L_FriendDelete                      182913  213367  85.73%
   协议:cmd:11501,MSG_C2L_FriendInfo                        138030  138030 100.00%
   协议正确数:2009578 协议总数:2394286 正确比:83.93%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:10100,                                         28.20%  35.44%  34.26%   2.10%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.86%   1.54%   2.60%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11514,MSG_C2L_FriendSendLike                   93.16%   1.68%   2.46%   2.23%   0.47%   0.00%   0.00% 
   协议:cmd:11516,MSG_C2L_FriendRecvLike                   92.65%   2.99%   3.35%   0.80%   0.21%   0.00%   0.00% 
   协议:cmd:11510,MSG_C2L_FriendBlacklist                  92.92%   1.70%   2.65%   2.23%   0.50%   0.00%   0.00% 
   协议:cmd:11522,MSG_C2L_FriendRequestInfo                93.26%   2.88%   2.75%   0.92%   0.19%   0.00%   0.00% 
   协议:cmd:11503,MSG_C2L_FriendAdd                        93.04%   3.07%   2.95%   0.78%   0.16%   0.00%   0.00% 
   协议:cmd:11518,MSG_C2L_FriendRecommend                  93.02%   1.77%   2.51%   2.25%   0.45%   0.00%   0.00% 
   协议:cmd:11512,MSG_C2L_FriendRemBlacklist               92.08%   1.75%   3.15%   2.50%   0.52%   0.00%   0.00% 
   协议:cmd:11506,MSG_C2L_FriendConfirm                    93.05%   1.73%   2.60%   2.15%   0.47%   0.00%   0.00% 
   协议:cmd:11520,MSG_C2L_FriendSearch                     92.95%   1.74%   2.64%   2.21%   0.46%   0.00%   0.00% 
   协议:cmd:11508,MSG_C2L_FriendDelete                     92.66%   1.78%   2.72%   2.34%   0.51%   0.00%   0.00% 
   协议:cmd:11501,MSG_C2L_FriendInfo                       93.25%   1.75%   2.47%   2.08%   0.44%   0.00%   0.00% 
   
   ---------------------
   
   logic服数情况
   
   ```

   

2. logic数据

   ```shell
   logic服务器启动时间:1.46081737s
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       11522(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11514(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100007(   1)                                0.00%   0.00%   0.00%   0.00%   0.00%   0.00%  100.00% 
   协议:       11516(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11508(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11518(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(friend)Save( 279)                                3.23%  11.47%  31.54%  47.67%   6.09%   0.00%   0.00% 
   协议:       11510(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11501(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100022( 277)                                0.00%  10.11%  28.52%  39.71%  20.22%   1.44%   0.00% 
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11503(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11006(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   
   ```

3. metric

   ![friend-20210607](./pic/friend-20210607.png)

4. 问题

   gc次数越来越频繁，cpu的消耗越来越多。





