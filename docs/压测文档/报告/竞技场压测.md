## 竞技场压测报告

注：MSG_C2L_Formation延迟是人为的，可忽略不计


#### 第一版 - 4000人

1. robot数据 --- 平均每秒755个战斗协议

   ```
   2021-07-12 19:12:19 压测数据分析
   压测基础情况
   开始时间：2021/07/12 18:59:07, 结束时间:2021/07/12 19:04:03, 耗时:296(s)
   登录成功机器人总数：4000
   总消息数:1193609， 平均每秒消息数:4032
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:10100,                                            4000    4000            0.33%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               4000    4000            0.33%  100.00%
   协议:cmd:12405,MSG_C2L_ArenaFight                        226526  235581           19.67%   96.16%
   协议:cmd:12407,MSG_C2L_ArenaLogList                      234913  234913           19.62%  100.00%
   协议:cmd:12401,MSG_C2L_ArenaInfo                           4000    4000            0.33%  100.00%
   协议:cmd:12403,MSG_C2L_ArenaRefresh                      168161  235914           19.70%   71.28%
   协议:cmd:12411,MSG_C2L_ArenaRank                         237308  237308           19.82%  100.00%
   协议:cmd:12409,MSG_C2L_ArenaLike                         137976  233893           19.53%   58.99%
   协议:cmd:11029,MSG_C2L_Formation                           8000    8000            0.67%  100.00%
   协议正确数:1024884 协议总数:1197609 正确比:85.58%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
   协议:cmd:10100,                                         15.10%  20.30%  54.50%   8.30%   1.80%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.47%   1.88%   1.65%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12405,MSG_C2L_ArenaFight                       72.27%  12.94%  11.15%   2.09%   1.46%   0.09%   0.00%   0.00% 
   协议:cmd:12407,MSG_C2L_ArenaLogList                     72.42%  12.98%  10.99%   2.11%   1.40%   0.11%   0.00%   0.00% 
   协议:cmd:12401,MSG_C2L_ArenaInfo                        98.62%   1.38%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12403,MSG_C2L_ArenaRefresh                     72.59%  12.75%  11.06%   2.11%   1.38%   0.12%   0.00%   0.00% 
   协议:cmd:12411,MSG_C2L_ArenaRank                        72.83%  12.63%  10.90%   2.12%   1.42%   0.11%   0.00%   0.00% 
   协议:cmd:12409,MSG_C2L_ArenaLike                        74.81%  12.52%   9.59%   1.90%   1.12%   0.06%   0.00%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%  100.00%   0.00%   0.00%   0.00%
   ```
   
2. 游戏数据

   ```
   所有超时的left
   
   --------------------------
   
   logic服务器启动时间:1.096105119s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       12407(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   5)                                0.00%  80.00%  20.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100025(  35)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_1010(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12409( 109)                               31.19%  44.95%  21.10%   2.75%   0.00%   0.00%   0.00% 
   协议:      100028( 413)                                0.00%  10.17%  40.68%  34.62%  12.59%   1.94%   0.00% 
   协议:       12411(   9)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12403(   4)                               25.00%  50.00%  25.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12405( 225)                               20.00%  42.67%  34.67%   1.78%   0.89%   0.00%   0.00% 
   ```
   
3. metric
   ![](./pic/arena-********-1.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Jul 12, 2021 at 7:01pm (CST)
   Duration: 30.08s, Total samples = 25.72s (85.49%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 15.19s, 59.06% of 25.72s total
   Dropped 658 nodes (cum <= 0.13s)
   Showing top 20 nodes out of 225
         flat  flat%   sum%        cum   cum%
        5.06s 19.67% 19.67%      8.90s 34.60%  runtime.scanobject
        1.95s  7.58% 27.26%      2.04s  7.93%  runtime.findObject
        1.19s  4.63% 31.88%      1.19s  4.63%  runtime.markBits.isMarked (inline)
           1s  3.89% 35.77%      1.08s  4.20%  syscall.Syscall
        0.85s  3.30% 39.07%      0.85s  3.30%  runtime.epollwait
        0.73s  2.84% 41.91%      3.13s 12.17%  runtime.mallocgc
        0.54s  2.10% 44.01%      0.80s  3.11%  runtime.heapBitsSetType
        0.39s  1.52% 45.53%      0.74s  2.88%  app/protos/out/cl.(*UserSnapshot).Clone
        0.39s  1.52% 47.05%      0.39s  1.52%  runtime.futex
        0.38s  1.48% 48.52%      0.38s  1.48%  runtime.memclrNoHeapPointers
        0.36s  1.40% 49.92%      0.36s  1.40%  app/protos/out/cl.sovCl
        0.33s  1.28% 51.21%      1.24s  4.82%  app/protos/out/cl.(*ArenaLog).Clone
        0.31s  1.21% 52.41%      0.31s  1.21%  runtime.nextFreeFast
        0.27s  1.05% 53.46%      0.27s  1.05%  runtime.memmove
        0.27s  1.05% 54.51%      0.31s  1.21%  runtime.nanotime
        0.25s  0.97% 55.48%      0.46s  1.79%  app/protos/out/cl.encodeVarintCl
        0.25s  0.97% 56.45%      0.25s  0.97%  runtime.pageIndexOf (inline)
        0.23s  0.89% 57.35%      0.54s  2.10%  runtime.selectgo
        0.22s  0.86% 58.20%      0.25s  0.97%  runtime.heapBitsForAddr (inline)
        0.22s  0.86% 59.06%      0.39s  1.52%  runtime.mapiternext
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 12, 2021 at 7:01pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 231.05MB, 73.70% of 313.52MB total
   Dropped 176 nodes (cum <= 1.57MB)
   Showing top 20 nodes out of 128
         flat  flat%   sum%        cum   cum%
      46.01MB 14.67% 14.67%    46.01MB 14.67%  app/logic/activity/arena.NewArenaLog
      35.51MB 11.32% 26.00%    35.51MB 11.32%  app/protos/out/cl.(*UserSnapshot).Clone
         26MB  8.29% 34.29%       26MB  8.29%  app/logic/character.(*User).NewUserSnapshot
      23.52MB  7.50% 41.80%    23.52MB  7.50%  app/logic/db/redisop.ClClient.SetSomeArenaLogMCallSKs
      11.50MB  3.67% 45.46%    16.50MB  5.26%  app/logic/character.newHero (inline)
      11.48MB  3.66% 49.13%    17.48MB  5.58%  gitlab.qdream.com/kit/sea/skiplist.(*Set).Insert
         10MB  3.19% 52.32%    45.51MB 14.51%  app/protos/out/cl.(*ArenaLog).Clone
       8.01MB  2.55% 54.87%     8.01MB  2.55%  app/logic/helper/monitor.(*Cmds).Add
          8MB  2.55% 57.42%        8MB  2.55%  app/logic/command/arena.AsyncC2LArenaRank
       7.50MB  2.39% 59.82%     7.50MB  2.39%  app/logic/character.(*Hero).calcTotalAttr
          6MB  1.91% 61.73%        6MB  1.91%  app/logic/character.(*Handbook).Add
          6MB  1.91% 63.64%        6MB  1.91%  gitlab.qdream.com/kit/sea/skiplist.NewNode (inline)
          5MB  1.60% 65.24%        5MB  1.60%  app/logic/activity/base.NewLogs
          5MB  1.59% 66.83%        5MB  1.59%  app/logic/character.initHeroFromData (inline)
       4.50MB  1.44% 68.27%     4.50MB  1.44%  app/logic/character.(*Avatar).Add
       4.01MB  1.28% 69.55%     4.01MB  1.28%  app/logic/activity/base.(*Logs).AddLog
          4MB  1.28% 70.82%        4MB  1.28%  github.com/gogo/protobuf/proto.Marshal
       3.50MB  1.12% 71.94%       20MB  6.38%  app/logic/character.(*HeroM).Add
          3MB  0.96% 72.90%        4MB  1.28%  app/logic/character.(*User).GetLikeList
       2.50MB   0.8% 73.70%     2.50MB   0.8%  github.com/google/btree.NewFreeList
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Jul 12, 2021 at 7:01pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 6.79GB, 69.37% of 9.78GB total
   Dropped 526 nodes (cum <= 0.05GB)
   Showing top 20 nodes out of 150
         flat  flat%   sum%        cum   cum%
       1.87GB 19.08% 19.08%     1.88GB 19.17%  app/logic/db/redisop.ClClient.SetSomeArenaLogMCallSKs
       1.05GB 10.68% 29.76%     1.05GB 10.68%  app/protos/out/cl.(*UserSnapshot).Clone
       0.87GB  8.92% 38.68%     0.87GB  8.92%  app/logic/character.(*User).NewUserSnapshot
       0.51GB  5.18% 43.86%     0.53GB  5.44%  app/logic/command/arena.AsyncC2LArenaRank
       0.49GB  5.06% 48.92%     0.50GB  5.14%  app/logic/db/redisop.DbClient.SetSomeBattleReportMCall
       0.30GB  3.06% 51.98%     1.34GB 13.75%  app/protos/out/cl.(*ArenaLog).Clone
       0.26GB  2.64% 54.62%     0.26GB  2.65%  app/logic/db/redisop.DbClient.SetUserMCallSKs
       0.16GB  1.68% 56.29%     0.27GB  2.72%  context.WithDeadline
       0.14GB  1.39% 57.68%     0.14GB  1.39%  app/protos/out/cl.(*BattleMember).Clone
       0.12GB  1.27% 58.96%     0.13GB  1.33%  app/logic/db/redisop.DbClient.SetSomeReportTmMCall
       0.12GB  1.24% 60.19%     0.12GB  1.24%  app/logic/db/redisop.ClClient.SetSomeArenaMCall
       0.12GB  1.21% 61.40%     0.12GB  1.21%  app/logic/battle.(*SkillManager).Reset
       0.11GB  1.17% 62.57%     0.11GB  1.17%  container/list.(*List).insertValue (inline)
       0.11GB  1.12% 63.69%     0.11GB  1.12%  gitlab.qdream.com/kit/sea/skiplist.(*Set).GetRanklist
       0.11GB  1.11% 64.79%     0.33GB  3.40%  app/logic/battle.(*SkillManager).AddNewSkill
       0.11GB  1.09% 65.89%     0.11GB  1.09%  app/logic/activity/base.(*Logs).GetAll
       0.10GB  1.04% 66.93%     0.10GB  1.04%  time.AfterFunc
       0.09GB  0.92% 67.85%     0.95GB  9.71%  app/logic/character.(*UserManager).getUserSnapshots
       0.08GB  0.77% 68.62%     0.08GB  0.77%  app/logic/session.(*Client).Process
       0.07GB  0.75% 69.37%     1.70GB 17.35%  app/logic/command/arena.(*C2LArenaRankCommand).Execute
   ```

   

#### 第二版 - 5000人

1. robot数据 --- 平均每秒942个战斗协议

   ```
   2021-07-12 19:31:43 压测数据分析
   压测基础情况
   开始时间：2021/07/12 19:24:48, 结束时间:2021/07/12 19:29:43, 耗时:295(s)
   登录成功机器人总数：5000
   总消息数:1487946， 平均每秒消息数:5043
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:10100,                                            5000    5000            0.33%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.33%  100.00%
   协议:cmd:12405,MSG_C2L_ArenaFight                        282834  293618           19.67%   96.33%
   协议:cmd:12407,MSG_C2L_ArenaLogList                      292821  292821           19.61%  100.00%
   协议:cmd:12401,MSG_C2L_ArenaInfo                           5000    5000            0.33%  100.00%
   协议:cmd:12403,MSG_C2L_ArenaRefresh                      212059  293735           19.67%   72.19%
   协议:cmd:12411,MSG_C2L_ArenaRank                         296122  296122           19.83%  100.00%
   协议:cmd:12409,MSG_C2L_ArenaLike                         172290  291650           19.54%   59.07%
   协议:cmd:11029,MSG_C2L_Formation                          10000   10000            0.67%  100.00%
   协议正确数:1281126 协议总数:1492946 正确比:85.81%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
   协议:cmd:10100,                                         15.46%  22.70%  55.74%   6.10%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.46%   1.92%   1.62%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12405,MSG_C2L_ArenaFight                       52.82%  14.03%  23.55%   5.81%   2.85%   0.94%   0.00%   0.00% 
   协议:cmd:12407,MSG_C2L_ArenaLogList                     52.89%  14.34%  23.21%   5.86%   2.82%   0.87%   0.00%   0.00% 
   协议:cmd:12401,MSG_C2L_ArenaInfo                        99.00%   0.78%   0.22%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12403,MSG_C2L_ArenaRefresh                     52.86%  14.44%  23.35%   5.62%   2.79%   0.93%   0.00%   0.00% 
   协议:cmd:12411,MSG_C2L_ArenaRank                        53.16%  14.20%  23.15%   5.76%   2.82%   0.91%   0.00%   0.00% 
   协议:cmd:12409,MSG_C2L_ArenaLike                        57.04%  13.91%  20.82%   4.97%   2.54%   0.72%   0.00%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%  100.00%   0.00%   0.00%   0.00%
   ```
   
2. 游戏数据 --- 消息平均积压数量2200左右

   ```
   所有超时的left
   [2021/07/12 19:25:32.171845] gate(pck/s: 3994 sum: 256994 left:1006) logic db(pck/s: 0 sum: 14999 left:0) redis db(pck/s: 613 sum: 67534 left:174) [2021/07/12 19:25:38.142545] gate(pck/s: 3401 sum: 291401 left:1064) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 1329 sum: 77584 left:189) [2021/07/12 19:25:56.183583] gate(pck/s: 3773 sum: 391775 left:1227) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 541 sum: 96424 left:239) [2021/07/12 19:26:11.157266] gate(pck/s: 3010 sum: 471013 left:1757) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 398 sum: 111257 left:204) [2021/07/12 19:26:35.186744] gate(pck/s: 2860 sum: 605866 left:2140) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 421 sum: 140103 left:161) [2021/07/12 19:26:49.286245] gate(pck/s: 5566 sum: 681657 left:1350) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 837 sum: 154099 left:308) [2021/07/12 19:26:53.242820] gate(pck/s: 4201 sum: 702209 left:3745) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 423 sum: 158090 left:218) [2021/07/12 19:27:04.290360] gate(pck/s: 9847 sum: 766159 left:1850) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 900 sum: 169151 left:218) [2021/07/12 19:27:14.148428] gate(pck/s: 5286 sum: 821242 left:1347) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 731 sum: 179527 left:276) [2021/07/12 19:27:23.169545] gate(pck/s: 6302 sum: 869313 left:1644) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 584 sum: 193417 left:280) [2021/07/12 19:27:31.260396] gate(pck/s: 6297 sum: 911809 left:1203) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 942 sum: 201336 left:340) [2021/07/12 19:27:38.208973] gate(pck/s: 2721 sum: 950733 left:2279) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 369 sum: 208376 left:155) [2021/07/12 19:28:23.154356] gate(pck/s: 4621 sum: 1197638 left:2753) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 420 sum: 258200 left:260) [2021/07/12 19:28:25.262398] gate(pck/s: 5549 sum: 1211378 left:1639) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 847 sum: 260206 left:239) [2021/07/12 19:28:32.275262] gate(pck/s: 3653 sum: 1246671 left:1347) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 411 sum: 267239 left:287) [2021/07/12 19:28:33.084803] gate(pck/s: 5286 sum: 1251957 left:1112) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 1123 sum: 268362 left:20) [2021/07/12 19:28:38.158466] gate(pck/s: 3736 sum: 1281754 left:1073) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 420 sum: 273157 left:355) [2021/07/12 19:28:43.336456] gate(pck/s: 7851 sum: 1308511 left:2454) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 876 sum: 278187 left:352) [2021/07/12 19:28:47.148415] gate(pck/s: 2234 sum: 1330253 left:2344) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 372 sum: 282147 left:35) [2021/07/12 19:28:53.162341] gate(pck/s: 5939 sum: 1363959 left:1783) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 386 sum: 288045 left:371) [2021/07/12 19:29:07.330906] gate(pck/s: 5173 sum: 1441227 left:1794) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 759 sum: 305937 left:231) [2021/07/12 19:29:29.190264] gate(pck/s: 3239 sum: 1561262 left:1761) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 495 sum: 324056 left:13) 
   
   --------------------------
   
   logic服务器启动时间:984.528288ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       12407(   9)                               55.56%  33.33%  11.11%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100025(  31)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12409( 102)                               15.69%  42.16%  36.27%   4.90%   0.98%   0.00%   0.00% 
   协议:  event_1010(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100028( 412)                                0.00%  11.17%  23.06%  37.86%  23.30%   4.61%   0.00% 
   协议:       12411(   9)                               55.56%  44.44%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12403(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       10100(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12405( 245)                               17.55%  36.33%  44.08%   1.63%   0.41%   0.00%   0.00% 
   协议: event_10002(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric

   ![](./pic/arena-********-2.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Jul 12, 2021 at 7:27pm (CST)
   Duration: 30s, Total samples = 32.74s (109.13%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 20.42s, 62.37% of 32.74s total
   Dropped 673 nodes (cum <= 0.16s)
   Showing top 20 nodes out of 225
         flat  flat%   sum%        cum   cum%
        6.85s 20.92% 20.92%     12.53s 38.27%  runtime.scanobject
        2.86s  8.74% 29.66%         3s  9.16%  runtime.findObject
        1.74s  5.31% 34.97%      1.74s  5.31%  runtime.markBits.isMarked (inline)
        1.31s  4.00% 38.97%      1.35s  4.12%  syscall.Syscall
        1.14s  3.48% 42.46%      3.96s 12.10%  runtime.mallocgc
        0.82s  2.50% 44.96%      0.82s  2.50%  runtime.epollwait
        0.81s  2.47% 47.43%      1.02s  3.12%  runtime.heapBitsSetType
        0.53s  1.62% 49.05%      0.53s  1.62%  runtime.futex
        0.50s  1.53% 50.58%      0.50s  1.53%  runtime.memmove
        0.47s  1.44% 52.02%      0.47s  1.44%  runtime.pageIndexOf (inline)
        0.42s  1.28% 53.30%      1.02s  3.12%  app/protos/out/cl.(*UserSnapshot).Clone
        0.36s  1.10% 54.40%      1.66s  5.07%  app/protos/out/cl.(*ArenaLog).Clone
        0.35s  1.07% 55.47%      0.35s  1.07%  app/protos/out/cl.sovCl
        0.35s  1.07% 56.54%      0.35s  1.07%  runtime.nextFreeFast
        0.34s  1.04% 57.57%      2.47s  7.54%  runtime.findrunnable
        0.33s  1.01% 58.58%      0.35s  1.07%  runtime.nanotime (inline)
        0.32s  0.98% 59.56%      1.98s  6.05%  app/logic/activity/arena.(*ArenaLog).Flush
        0.32s  0.98% 60.54%      0.48s  1.47%  runtime.mapiternext
        0.32s  0.98% 61.51%      0.32s  0.98%  runtime.memclrNoHeapPointers
        0.28s  0.86% 62.37%      2.56s  7.82%  runtime.greyobject
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 12, 2021 at 7:27pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 286.26MB, 72.93% of 392.49MB total
   Dropped 206 nodes (cum <= 1.96MB)
   Showing top 20 nodes out of 122
         flat  flat%   sum%        cum   cum%
      59.01MB 15.03% 15.03%    59.01MB 15.03%  app/logic/activity/arena.NewArenaLog
      47.01MB 11.98% 27.01%    47.01MB 11.98%  app/protos/out/cl.(*UserSnapshot).Clone
         31MB  7.90% 34.91%       31MB  7.90%  app/logic/character.(*User).NewUserSnapshot
      27.03MB  6.89% 41.80%    27.03MB  6.89%  app/logic/db/redisop.ClClient.SetSomeArenaLogMCallSKs
         17MB  4.33% 46.13%    64.01MB 16.31%  app/protos/out/cl.(*ArenaLog).Clone
      13.50MB  3.44% 49.57%       14MB  3.57%  app/logic/command/arena.AsyncC2LArenaRank
      13.50MB  3.44% 53.01%    18.50MB  4.71%  app/logic/character.newHero (inline)
      10.50MB  2.68% 55.68%    10.50MB  2.68%  gitlab.qdream.com/kit/sea/skiplist.NewNode (inline)
          9MB  2.29% 57.98%        9MB  2.29%  app/logic/character.(*Handbook).Add
       8.01MB  2.04% 60.02%     8.01MB  2.04%  app/logic/helper/monitor.(*Cmds).Add
       7.52MB  1.92% 61.93%    18.02MB  4.59%  gitlab.qdream.com/kit/sea/skiplist.(*Set).Insert
          6MB  1.53% 63.46%        6MB  1.53%  app/logic/character.(*Hero).calcTotalAttr
       5.16MB  1.31% 64.78%     6.66MB  1.70%  app/logic/db.(*RedisActor).Create
       5.01MB  1.28% 66.05%     5.01MB  1.28%  app/logic/activity/base.(*Logs).AddLog
          5MB  1.27% 67.33%        5MB  1.27%  app/logic/character.(*Avatar).Add
          5MB  1.27% 68.60%        5MB  1.27%  app/logic/character.initHeroFromData (inline)
       4.50MB  1.15% 69.75%        8MB  2.04%  app/logic/character.(*User).initModule
       4.50MB  1.15% 70.89%     4.50MB  1.15%  github.com/google/btree.NewFreeList
          4MB  1.02% 71.91%        4MB  1.02%  app/logic/activity/base.NewLogs
          4MB  1.02% 72.93%    22.50MB  5.73%  app/logic/character.(*HeroM).Add
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Jul 12, 2021 at 7:27pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 8.41GB, 68.84% of 12.21GB total
   Dropped 539 nodes (cum <= 0.06GB)
   Showing top 20 nodes out of 151
         flat  flat%   sum%        cum   cum%
       2.24GB 18.35% 18.35%     2.25GB 18.44%  app/logic/db/redisop.ClClient.SetSomeArenaLogMCallSKs
       1.29GB 10.60% 28.95%     1.29GB 10.60%  app/protos/out/cl.(*UserSnapshot).Clone
       1.16GB  9.48% 38.43%     1.16GB  9.48%  app/logic/character.(*User).NewUserSnapshot
       0.64GB  5.21% 43.63%     0.67GB  5.48%  app/logic/command/arena.AsyncC2LArenaRank
       0.57GB  4.68% 48.32%     0.59GB  4.81%  app/logic/db/redisop.DbClient.SetSomeBattleReportMCall
       0.38GB  3.11% 51.42%     1.67GB 13.71%  app/protos/out/cl.(*ArenaLog).Clone
       0.34GB  2.75% 54.18%     0.34GB  2.76%  app/logic/db/redisop.DbClient.SetUserMCallSKs
       0.21GB  1.73% 55.91%     0.34GB  2.77%  context.WithDeadline
       0.17GB  1.42% 57.32%     0.17GB  1.42%  app/protos/out/cl.(*BattleMember).Clone
       0.17GB  1.36% 58.68%     0.17GB  1.39%  app/logic/db/redisop.DbClient.SetSomeReportTmMCall
       0.15GB  1.25% 59.92%     0.15GB  1.25%  app/logic/db/redisop.ClClient.SetSomeArenaMCall
       0.15GB  1.21% 61.14%     0.15GB  1.21%  container/list.(*List).insertValue (inline)
       0.15GB  1.19% 62.32%     0.15GB  1.19%  app/logic/battle.(*SkillManager).Reset
       0.13GB  1.06% 63.39%     0.13GB  1.06%  gitlab.qdream.com/kit/sea/skiplist.(*Set).GetRanklist
       0.13GB  1.04% 64.43%     0.13GB  1.04%  time.AfterFunc
       0.13GB  1.04% 65.46%     0.13GB  1.04%  app/logic/activity/base.(*Logs).GetAll
       0.12GB     1% 66.46%     1.26GB 10.31%  app/logic/character.(*UserManager).getUserSnapshots
       0.11GB  0.88% 67.34%     0.36GB  2.93%  app/logic/battle.(*SkillManager).AddNewSkill
       0.09GB  0.76% 68.10%     2.19GB 17.92%  app/logic/command/arena.(*C2LArenaRankCommand).Execute
       0.09GB  0.74% 68.84%     0.28GB  2.33%  app/logic/character.(*User).pushMsg
   ```
   
   

#### 第二版 - 5000人 - 微小调整：1. snapshot改为miniSnapshot  2. 修改了量表管理

优化后，使用和申请内存数据有所下降，参考pprof top20的统计数据
heap: 286.26MB -> 218.23MB
allocs: 8.41GB -> 7.85GB

1. robot数据 --- 平均每秒943个战斗协议（持续回合很短，大概一回合就结束了）

   ```
   2021-07-13 17:09:37 压测数据分析
   压测基础情况
   开始时间：2021/07/13 17:02:29, 结束时间:2021/07/13 17:07:23, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:1488516， 平均每秒消息数:5062
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:10100,                                            5000    5000            0.33%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.33%  100.00%
   协议:cmd:12405,MSG_C2L_ArenaFight                        282977  293936           19.68%   96.27%
   协议:cmd:12407,MSG_C2L_ArenaLogList                      293792  293792           19.67%  100.00%
   协议:cmd:12401,MSG_C2L_ArenaInfo                           5000    5000            0.33%  100.00%
   协议:cmd:12403,MSG_C2L_ArenaRefresh                      209529  293705           19.67%   71.34%
   协议:cmd:12411,MSG_C2L_ArenaRank                         295306  295306           19.77%  100.00%
   协议:cmd:12409,MSG_C2L_ArenaLike                         172018  291777           19.54%   58.96%
   协议:cmd:11029,MSG_C2L_Formation                          10000   10000            0.67%  100.00%
   协议正确数:1278622 协议总数:1493516 正确比:85.61%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
   协议:cmd:10100,                                         18.42%  23.10%  53.40%   4.88%   0.20%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            97.06%   1.94%   1.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12405,MSG_C2L_ArenaFight                       62.31%  17.88%  13.96%   3.68%   1.87%   0.30%   0.00%   0.00% 
   协议:cmd:12407,MSG_C2L_ArenaLogList                     62.17%  17.66%  14.27%   3.71%   1.87%   0.33%   0.00%   0.00% 
   协议:cmd:12401,MSG_C2L_ArenaInfo                        89.44%   9.80%   0.76%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:12403,MSG_C2L_ArenaRefresh                     62.36%  17.42%  14.39%   3.63%   1.85%   0.35%   0.00%   0.00% 
   协议:cmd:12411,MSG_C2L_ArenaRank                        62.59%  17.56%  14.07%   3.57%   1.88%   0.33%   0.00%   0.00% 
   协议:cmd:12409,MSG_C2L_ArenaLike                        66.20%  16.39%  12.11%   3.50%   1.58%   0.21%   0.00%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%  100.00%   0.00%   0.00%   0.00%
   ```

2. 游戏数据 --- 消息略有积压

   ```
   所有超时的left
   [2021/07/13 17:07:24.299250] gate(pck/s: 12090 sum: 1647099 left:0) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 2566 sum: 335537 left:3076) [2021/07/13 17:03:59.109040] gate(pck/s: 3795 sum: 513782 left:1206) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 746 sum: 119141 left:52) [2021/07/13 17:05:43.286331] gate(pck/s: 4170 sum: 1084615 left:1048) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 607 sum: 233109 left:212) [2021/07/13 17:06:23.108359] gate(pck/s: 3816 sum: 1303818 left:1187) logic db(pck/s: 0 sum: 15000 left:0) redis db(pck/s: 703 sum: 277971 left:59) 
   
   --------------------------
   
   logic服务器启动时间:1.051702395s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:       12407(  10)                               70.00%  20.00%  10.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100006(   6)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100025(  24)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       12409( 106)                               29.25%  30.19%  38.68%   1.89%   0.00%   0.00%   0.00% 
   协议:      100028( 440)                                0.00%  12.05%  33.64%  27.50%  23.64%   2.95%   0.23% 
   协议:       12411(   9)                               55.56%  33.33%  11.11%   0.00%   0.00%   0.00%   0.00% 
   协议:       12403(   3)                               66.67%   0.00%  33.33%   0.00%   0.00%   0.00%   0.00% 
   协议:       12405( 225)                               25.78%  31.11%  41.33%   1.78%   0.00%   0.00%   0.00%
   ```

3. metric

   ![](./pic/arena-********-1.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Jul 13, 2021 at 5:04pm (CST)
   Duration: 30.30s, Total samples = 33.06s (109.10%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 20.16s, 60.98% of 33.06s total
   Dropped 714 nodes (cum <= 0.17s)
   Showing top 20 nodes out of 223
         flat  flat%   sum%        cum   cum%
        6.18s 18.69% 18.69%     12.22s 36.96%  runtime.scanobject
        3.18s  9.62% 28.31%      3.33s 10.07%  runtime.findObject
        1.79s  5.41% 33.73%      1.79s  5.41%  runtime.markBits.isMarked (inline)
        1.25s  3.78% 37.51%      1.30s  3.93%  syscall.Syscall
        1.03s  3.12% 40.62%      1.03s  3.12%  runtime.epollwait
        1.01s  3.06% 43.68%      3.79s 11.46%  runtime.mallocgc
        0.68s  2.06% 45.74%      0.81s  2.45%  runtime.heapBitsSetType
        0.51s  1.54% 47.28%      0.51s  1.54%  runtime.memmove
        0.49s  1.48% 48.76%      0.49s  1.48%  runtime.pageIndexOf (inline)
        0.48s  1.45% 50.21%      0.85s  2.57%  app/protos/out/cl.(*MiniUserSnapshot).Clone (inline)
        0.44s  1.33% 51.54%      3.17s  9.59%  runtime.findrunnable
        0.44s  1.33% 52.87%      0.44s  1.33%  runtime.futex
        0.38s  1.15% 54.02%      1.46s  4.42%  app/protos/out/cl.(*ArenaLog).Clone
        0.37s  1.12% 55.14%      0.40s  1.21%  runtime.nanotime (inline)
        0.35s  1.06% 56.20%      0.35s  1.06%  runtime.memclrNoHeapPointers
        0.33s     1% 57.20%      0.33s     1%  runtime.nextFreeFast (inline)
        0.32s  0.97% 58.17%      0.95s  2.87%  runtime.selectgo
        0.32s  0.97% 59.13%      0.32s  0.97%  runtime.unlock
        0.31s  0.94% 60.07%     11.92s 36.06%  runtime.gcDrain
        0.30s  0.91% 60.98%      0.32s  0.97%  runtime.heapBitsForAddr (inline)
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 13, 2021 at 5:04pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20 
   Showing nodes accounting for 218.23MB, 68.06% of 320.62MB total
   Dropped 168 nodes (cum <= 1.60MB)
   Showing top 20 nodes out of 121
         flat  flat%   sum%        cum   cum%
      35.51MB 11.07% 11.07%    35.51MB 11.07%  app/logic/character.(*User).NewUserSnapshot
         26MB  8.11% 19.18%       26MB  8.11%  app/protos/out/cl.(*MiniUserSnapshot).Clone (inline)
         15MB  4.68% 23.86%    20.50MB  6.39%  app/logic/character.newHero (inline)
      13.50MB  4.21% 28.07%    13.50MB  4.21%  app/logic/activity/arena.NewArenaLog
         13MB  4.05% 32.13%       39MB 12.16%  app/protos/out/cl.(*ArenaLog).Clone
         12MB  3.74% 35.87%    82.93MB 25.86%  app/logic/command/arena.(*C2LArenaFightCommand).Execute
      11.51MB  3.59% 39.46%    11.51MB  3.59%  app/logic/db/redisop.ClClient.SetSomeArenaLogMCallSKs
         11MB  3.43% 42.89%    11.50MB  3.59%  app/logic/command/arena.AsyncC2LArenaRank
         11MB  3.43% 46.32%       11MB  3.43%  gitlab.qdream.com/kit/sea/skiplist.NewNode (inline)
       8.50MB  2.65% 48.98%     8.50MB  2.65%  app/logic/character.(*Hero).calcTotalAttr
       8.50MB  2.65% 51.63%     8.50MB  2.65%  app/logic/character.(*Handbook).Add
       7.52MB  2.35% 53.97%    18.52MB  5.78%  gitlab.qdream.com/kit/sea/skiplist.(*Set).Insert
       7.50MB  2.34% 56.31%    28.01MB  8.73%  app/logic/character.(*HeroM).Add
       7.01MB  2.19% 58.50%     7.01MB  2.19%  app/logic/activity/base.(*Logs).AddLog
          7MB  2.18% 60.68%        7MB  2.18%  app/logic/character.(*User).NewMiniUserSnapshot
       5.50MB  1.72% 62.40%     5.50MB  1.72%  app/logic/character.initHeroFromData (inline)
          5MB  1.56% 63.96%        5MB  1.56%  app/logic/activity/base.NewLogs (inline)
          5MB  1.56% 65.52%        5MB  1.56%  container/list.(*List).insertValue (inline)
       4.16MB  1.30% 66.82%     4.66MB  1.45%  app/logic/db.(*RedisActor).Create
          4MB  1.25% 68.06%        4MB  1.25%  app/logic/helper/monitor.(*Cmds).Add
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Jul 13, 2021 at 5:04pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 7.85GB, 67.42% of 11.64GB total
   Dropped 525 nodes (cum <= 0.06GB)
   Showing top 20 nodes out of 157
         flat  flat%   sum%        cum   cum%
       2.27GB 19.48% 19.48%     2.28GB 19.60%  app/logic/db/redisop.ClClient.SetSomeArenaLogMCallSKs
       1.13GB  9.73% 29.21%     1.13GB  9.73%  app/logic/character.(*User).NewUserSnapshot
       0.66GB  5.69% 34.89%     0.69GB  5.93%  app/logic/command/arena.AsyncC2LArenaRank
       0.66GB  5.65% 40.54%     0.66GB  5.65%  app/protos/out/cl.(*MiniUserSnapshot).Clone (inline)
       0.56GB  4.81% 45.36%     0.58GB  4.95%  app/logic/db/redisop.DbClient.SetSomeBattleReportMCall
       0.38GB  3.26% 48.62%     1.04GB  8.91%  app/protos/out/cl.(*ArenaLog).Clone
       0.33GB  2.82% 51.44%     0.33GB  2.85%  app/logic/db/redisop.DbClient.SetUserMCallSKs
       0.20GB  1.72% 53.15%     0.32GB  2.71%  context.WithDeadline
       0.19GB  1.62% 54.77%     0.19GB  1.62%  strings.(*Builder).WriteString
       0.16GB  1.41% 56.18%     0.16GB  1.41%  app/protos/out/cl.(*BattleMember).Clone
       0.16GB  1.40% 57.58%     0.16GB  1.40%  app/logic/battle.(*SkillManager).Reset
       0.16GB  1.39% 58.97%     0.16GB  1.39%  app/logic/db/redisop.ClClient.SetSomeArenaMCall
       0.15GB  1.30% 60.26%     0.15GB  1.30%  container/list.(*List).insertValue
       0.15GB  1.29% 61.55%     0.15GB  1.32%  app/logic/db/redisop.DbClient.SetSomeReportTmMCall
       0.12GB  1.05% 62.60%     0.12GB  1.05%  gitlab.qdream.com/kit/sea/skiplist.(*Set).GetRanklist
       0.12GB  1.04% 63.65%     1.25GB 10.77%  app/logic/character.(*UserManager).getUserSnapshots
       0.12GB  1.01% 64.65%     0.12GB  1.01%  app/logic/activity/base.(*Logs).GetAll
       0.12GB  0.99% 65.64%     0.12GB  0.99%  time.AfterFunc
       0.11GB  0.93% 66.57%     0.37GB  3.20%  app/logic/battle.(*SkillManager).AddNewSkill
       0.10GB  0.84% 67.42%     0.16GB  1.41%  github.com/ivanabc/log4go.Logger.intLogf
   ```

   