## 主线压测报告

注： 为了能够打过关卡中的npc。启动时，加入了对英雄的升级和突破逻辑，消息比较集中会有积压。对于爬塔系统本身的测试，可以将其忽略

#### 第一版 - 5000人

1. robot数据 --- 平均每秒1800个战斗协议

   ```
   2021-07-07 11:55:10 压测数据分析
   压测基础情况
   开始时间：2021/07/07 11:49:57, 结束时间:2021/07/07 11:54:52, 耗时:295(s)
   登录成功机器人总数：5000
   总消息数:1549553， 平均每秒消息数:5252
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:11803,MSG_C2L_DungeonRecvAward                   48248  168030  28.71%
   协议:cmd:10100,                                            5000    5000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000 100.00%
   协议:cmd:11809,MSG_C2L_Dungeon                             5000    5000 100.00%
   协议:cmd:11801,MSG_C2L_DungeonFight                      146443  540053  27.12%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                         5000   75000   6.67%
   协议:cmd:11807,MSG_C2L_DungeonPreview                    176828  352178  50.21%
   协议:cmd:11029,MSG_C2L_Formation                           5000    5000 100.00%
   协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward             175112  349292  50.13%
   协议:cmd:11905,MSG_C2L_HeroStageUp                         5000   50000  10.00%
   协议正确数:576631 协议总数:1554553 正确比:37.09%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:11803,MSG_C2L_DungeonRecvAward                  0.15%   0.15%   0.81%   1.36%   2.18%   5.33%   0.00% 
   协议:cmd:10100,                                         16.26%  23.80%  56.10%   3.84%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.34%   2.06%   1.60%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11809,MSG_C2L_Dungeon                           0.00%   0.00%   0.00%   0.00%   0.00%   3.62%   0.00% 
   协议:cmd:11801,MSG_C2L_DungeonFight                      0.39%   0.45%   1.67%   2.40%   3.87%   8.72%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                       0.00%   0.00%   0.00%   0.00%  10.69%  19.72%   0.00% 
   协议:cmd:11807,MSG_C2L_DungeonPreview                    0.31%   0.33%   1.25%   1.69%   2.51%   4.68%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%   9.08%  17.52%   0.00% 
   协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward             0.30%   0.31%   1.22%   1.65%   2.49%   4.35%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                       0.00%   0.00%   0.00%   0.00%  10.68%  19.72%   0.00% 
   
   ---------------------
   
   ```
   
2. 游戏数据 --- 消息堆积严重

   ```
   [2021/07/07 11:54:56.058366] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:54:56.03675107 +0800 CST msgs count per second: gate(pck/s: 3596 sum: 1715384  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1738 sum: 420695 left:0) 
   [2021/07/07 11:54:57.266561] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:54:57.235858181 +0800 CST msgs count per second: gate(pck/s: 4183 sum: 1719567  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 2274 sum: 422969 left:0) 
   [2021/07/07 11:54:58.165198] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:54:58.136244459 +0800 CST msgs count per second: gate(pck/s: 3975 sum: 1723542  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1900 sum: 424869 left:0) 
   [2021/07/07 11:54:59.065681] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:54:59.036212633 +0800 CST msgs count per second: gate(pck/s: 3761 sum: 1727303  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1192 sum: 426061 left:0) 
   [2021/07/07 11:55:00.242702] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:55:00.23590329 +0800 CST msgs count per second: gate(pck/s: 5234 sum: 1732537  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1476 sum: 427537 left:0) 
   [2021/07/07 11:55:01.142215] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:55:01.136195944 +0800 CST msgs count per second: gate(pck/s: 3520 sum: 1736057  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 929 sum: 428466 left:0) 
   [2021/07/07 11:55:02.041005] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:55:02.036232601 +0800 CST msgs count per second: gate(pck/s: 3472 sum: 1739529  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 981 sum: 429447 left:0) 
   [2021/07/07 11:55:03.251412] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:55:03.235892876 +0800 CST msgs count per second: gate(pck/s: 5363 sum: 1744892  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1501 sum: 430948 left:0) 
   [2021/07/07 11:55:04.151398] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:55:04.136461726 +0800 CST msgs count per second: gate(pck/s: 3908 sum: 1748800  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1091 sum: 432039 left:0) 
   [2021/07/07 11:55:05.047794] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:55:05.038067419 +0800 CST msgs count per second: gate(pck/s: 4303 sum: 1753103  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 934 sum: 432973 left:0) 
   [2021/07/07 11:55:06.256261] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:55:06.235941539 +0800 CST msgs count per second: gate(pck/s: 13381 sum: 1766484  left:0) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 2992 sum: 435965 left:3142) 
   [2021/07/07 11:49:58.147233] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:49:58.135861225 +0800 CST msgs count per second: gate(pck/s: 11553 sum: 20362  left:10000) logic db(pck/s: 1200 sum: 10000 left:0) redis db(pck/s: 2584 sum: 20091 left:0) 
   [2021/07/07 11:49:59.046312] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:49:59.035853582 +0800 CST msgs count per second: gate(pck/s: 60337 sum: 80699  left:9995) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1 sum: 20092 left:0) 
   [2021/07/07 11:50:00.255384] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:00.236426847 +0800 CST msgs count per second: gate(pck/s: 66375 sum: 147074  left:3322) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1 sum: 20093 left:0) 
   [2021/07/07 11:50:03.262272] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:03.236165868 +0800 CST msgs count per second: gate(pck/s: 11489 sum: 169093  left:1487) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1653 sum: 22865 left:0) 
   [2021/07/07 11:50:06.238578] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:06.235930006 +0800 CST msgs count per second: gate(pck/s: 6651 sum: 183849  left:1341) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1695 sum: 26570 left:1) 
   [2021/07/07 11:50:07.168208] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:07.147160434 +0800 CST msgs count per second: gate(pck/s: 3896 sum: 187745  left:1367) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 990 sum: 27560 left:11) 
   [2021/07/07 11:50:09.245631] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:09.235906417 +0800 CST msgs count per second: gate(pck/s: 6971 sum: 198907  left:1413) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1707 sum: 30332 left:0) 
   [2021/07/07 11:50:12.322482] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:12.236312279 +0800 CST msgs count per second: gate(pck/s: 8439 sum: 215259  left:5945) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1650 sum: 33947 left:41) 
   [2021/07/07 11:50:13.151298] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:13.135886918 +0800 CST msgs count per second: gate(pck/s: 7314 sum: 222573  left:1184) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1085 sum: 35032 left:1) 
   [2021/07/07 11:50:15.259299] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:15.23585433 +0800 CST msgs count per second: gate(pck/s: 6112 sum: 232964  left:2516) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1522 sum: 37631 left:0) 
   [2021/07/07 11:50:16.176323] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:16.135926203 +0800 CST msgs count per second: gate(pck/s: 4395 sum: 237359  left:1944) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1128 sum: 38759 left:0) 
   [2021/07/07 11:50:18.268534] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:18.235899104 +0800 CST msgs count per second: gate(pck/s: 6288 sum: 248133  left:2530) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1593 sum: 41437 left:5) 
   [2021/07/07 11:50:19.165217] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:19.135987577 +0800 CST msgs count per second: gate(pck/s: 4301 sum: 252434  left:1608) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1090 sum: 42527 left:0) 
   [2021/07/07 11:50:21.242851] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:21.236051247 +0800 CST msgs count per second: gate(pck/s: 5834 sum: 262667  left:2626) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1466 sum: 45125 left:0) 
   [2021/07/07 11:50:22.141552] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:22.135913593 +0800 CST msgs count per second: gate(pck/s: 5567 sum: 268234  left:2464) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1231 sum: 46356 left:0) 
   [2021/07/07 11:50:24.249404] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:24.236495129 +0800 CST msgs count per second: gate(pck/s: 6175 sum: 282963  left:2360) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1573 sum: 49043 left:0) 
   [2021/07/07 11:50:25.157357] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:25.135892478 +0800 CST msgs count per second: gate(pck/s: 4462 sum: 287425  left:1445) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1136 sum: 50179 left:0) 
   [2021/07/07 11:50:27.256594] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:27.235908864 +0800 CST msgs count per second: gate(pck/s: 6122 sum: 297860  left:2550) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1570 sum: 52858 left:1) 
   [2021/07/07 11:50:28.139225] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:28.135943832 +0800 CST msgs count per second: gate(pck/s: 4389 sum: 302249  left:1304) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1161 sum: 54019 left:0) 
   [2021/07/07 11:50:30.263460] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:30.235890202 +0800 CST msgs count per second: gate(pck/s: 5783 sum: 312556  left:3067) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1550 sum: 56792 left:2) 
   [2021/07/07 11:50:31.173125] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:31.135950511 +0800 CST msgs count per second: gate(pck/s: 4095 sum: 316651  left:2591) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1099 sum: 57891 left:0) 
   [2021/07/07 11:50:32.064768] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:32.036840995 +0800 CST msgs count per second: gate(pck/s: 4306 sum: 320957  left:2467) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1144 sum: 59035 left:11) 
   [2021/07/07 11:50:33.239594] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:33.238844711 +0800 CST msgs count per second: gate(pck/s: 9969 sum: 330926  left:4297) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1483 sum: 60518 left:0) 
   [2021/07/07 11:50:34.138390] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:34.136404602 +0800 CST msgs count per second: gate(pck/s: 4635 sum: 335561  left:2968) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1242 sum: 61760 left:1) 
   [2021/07/07 11:50:35.055710] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:35.035855659 +0800 CST msgs count per second: gate(pck/s: 4221 sum: 339782  left:2662) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1191 sum: 62951 left:0) 
   [2021/07/07 11:50:36.246603] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:36.23870804 +0800 CST msgs count per second: gate(pck/s: 5474 sum: 345256  left:5067) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1615 sum: 64566 left:0) 
   [2021/07/07 11:50:37.145539] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:37.138346704 +0800 CST msgs count per second: gate(pck/s: 4337 sum: 349593  left:4060) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1235 sum: 65801 left:1) 
   [2021/07/07 11:50:38.044265] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:38.035895013 +0800 CST msgs count per second: gate(pck/s: 4606 sum: 354199  left:3080) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1351 sum: 67152 left:0) 
   [2021/07/07 11:50:39.253377] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:39.236111401 +0800 CST msgs count per second: gate(pck/s: 5356 sum: 359555  left:5793) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1608 sum: 68760 left:0) 
   [2021/07/07 11:50:40.158837] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:40.135869888 +0800 CST msgs count per second: gate(pck/s: 4167 sum: 363722  left:5182) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1259 sum: 70019 left:0) 
   [2021/07/07 11:50:41.054190] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:41.039763991 +0800 CST msgs count per second: gate(pck/s: 4527 sum: 368249  left:4174) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1325 sum: 71344 left:0) 
   [2021/07/07 11:50:42.260793] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:42.235955894 +0800 CST msgs count per second: gate(pck/s: 5372 sum: 373621  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1615 sum: 72959 left:0) 
   [2021/07/07 11:50:43.159522] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:43.13606086 +0800 CST msgs count per second: gate(pck/s: 6105 sum: 379726  left:9195) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1422 sum: 74381 left:0) 
   [2021/07/07 11:50:59.062531] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:50:59.03590245 +0800 CST msgs count per second: gate(pck/s: 4520 sum: 466626  left:5850) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1167 sum: 100937 left:0) 
   [2021/07/07 11:51:00.240610] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:00.236018347 +0800 CST msgs count per second: gate(pck/s: 5424 sum: 472050  left:8196) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1442 sum: 102379 left:0) 
   [2021/07/07 11:51:01.139519] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:01.135942732 +0800 CST msgs count per second: gate(pck/s: 4566 sum: 476616  left:6954) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1215 sum: 103594 left:0) 
   [2021/07/07 11:51:02.038523] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:02.036004431 +0800 CST msgs count per second: gate(pck/s: 4400 sum: 481016  left:6913) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1170 sum: 104764 left:0) 
   [2021/07/07 11:51:03.247224] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:03.235949591 +0800 CST msgs count per second: gate(pck/s: 5467 sum: 486483  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1421 sum: 106185 left:1) 
   [2021/07/07 11:51:04.146209] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:04.135857634 +0800 CST msgs count per second: gate(pck/s: 8498 sum: 494981  left:8704) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1081 sum: 107266 left:3) 
   [2021/07/07 11:51:05.045644] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:05.035863881 +0800 CST msgs count per second: gate(pck/s: 5198 sum: 500179  left:7122) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1166 sum: 108432 left:0) 
   [2021/07/07 11:51:06.261422] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:06.235910713 +0800 CST msgs count per second: gate(pck/s: 5357 sum: 505536  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1424 sum: 109856 left:0) 
   [2021/07/07 11:51:07.154302] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:07.137456875 +0800 CST msgs count per second: gate(pck/s: 4624 sum: 510160  left:8660) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1168 sum: 111024 left:5) 
   [2021/07/07 11:51:08.052538] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:08.037030987 +0800 CST msgs count per second: gate(pck/s: 3513 sum: 513673  left:8729) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 930 sum: 111954 left:0) 
   [2021/07/07 11:51:09.266852] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 11:51:09.236143879 +0800 CST msgs count per second: gate(pck/s: 5499 sum: 519172  left:10000) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1418 sum: 113372 left:0) 
   ...
   --------------------------
   
   logic服务器启动时间:1.076103162s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006( 133)                                0.00%  99.25%   0.75%   0.00%   0.00%   0.00%   0.00% 
   协议:       11803(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:2(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(/home/<USER>/go/pkg/mod/gitlab.qdream.com/kit/sea@v0.0.0-20210705080433-8f4dd478eb9f/event/event.go:57)(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:3(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11807(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11801( 126)                               84.13%  14.29%   1.59%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric
   ![](./pic/dungeon-********-1.png)


#### 第二版 - 1000人

1. robot数据 --- 平均每秒400个战斗协议

   1. MSG_C2L_DungeonSpeedRecvAward，平均每个玩家访问73次，目前给的免费次数是290次，因此未测到付费购买次数的情况。下一版，修改免费次数再测
   
   ```
   2021-07-07 13:07:38 压测数据分析
   压测基础情况
   开始时间：2021/07/07 13:02:29, 结束时间:2021/07/07 13:07:28, 耗时:299(s)
   登录成功机器人总数：1000
   总消息数:325985， 平均每秒消息数:1090
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:11803,MSG_C2L_DungeonRecvAward                   22290   29592  75.32%
   协议:cmd:10100,                                            1000    1000 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               1000    1000 100.00%
   协议:cmd:11809,MSG_C2L_Dungeon                             1000    1000 100.00%
   协议:cmd:11801,MSG_C2L_DungeonFight                      120073  120073 100.00%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                         1000   15000   6.67%
   协议:cmd:11807,MSG_C2L_DungeonPreview                     74414   74414 100.00%
   协议:cmd:11029,MSG_C2L_Formation                           1000    1000 100.00%
   协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward              73906   73906 100.00%
   协议:cmd:11905,MSG_C2L_HeroStageUp                         1000   10000  10.00%
   协议正确数:296683 协议总数:326985 正确比:90.73%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:11803,MSG_C2L_DungeonRecvAward                  2.60%   3.94%  11.70%  15.58%  26.32%  39.51%   0.00% 
   协议:cmd:10100,                                         14.90%  24.40%  53.40%   7.30%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            96.20%   2.50%   1.30%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11809,MSG_C2L_Dungeon                           0.00%   0.00%   0.00%   0.00%   8.60%  91.40%   0.00% 
   协议:cmd:11801,MSG_C2L_DungeonFight                      2.94%   4.21%  11.52%  15.90%  26.15%  38.92%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                       0.00%   0.00%   0.00%   0.00%  67.41%  32.59%   0.00% 
   协议:cmd:11807,MSG_C2L_DungeonPreview                    3.97%   4.48%  11.89%  15.59%  25.44%  38.28%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%  55.30%  44.70%   0.00% 
   协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward             3.87%   4.49%  11.73%  15.48%  25.74%  38.32%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                       0.00%   0.00%   0.00%   0.00%  66.65%  33.35%   0.00% 
   ```
   
2. 游戏数据

   ```
   所有超时的left
   [2021/07/07 13:05:08.273213] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 13:05:08.245737926 +0800 CST msgs count per second: gate(pck/s: 1390 sum: 201408  left:1496) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 291 sum: 48533 left:0) 
   [2021/07/07 13:05:38.250886] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 13:05:38.243621242 +0800 CST msgs count per second: gate(pck/s: 1251 sum: 234272  left:1118) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 301 sum: 56338 left:0) 
   [2021/07/07 13:06:08.258222] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 13:06:08.232301158 +0800 CST msgs count per second: gate(pck/s: 1275 sum: 267299  left:1351) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 293 sum: 65371 left:0) 
   [2021/07/07 13:07:08.242898] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 13:07:08.232250647 +0800 CST msgs count per second: gate(pck/s: 1190 sum: 333220  left:1020) logic db(pck/s: 0 sum: 2000 left:0) redis db(pck/s: 284 sum: 82000 left:0) 
   
   --------------------------
   
   logic服务器启动时间:1.09138926s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(  52)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11803(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:(/home/<USER>/go/pkg/mod/gitlab.qdream.com/kit/sea@v0.0.0-20210705080433-8f4dd478eb9f/event/event.go:57)(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11805(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:3(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100038(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11801(  73)                               72.60%  23.29%   4.11%   0.00%   0.00%   0.00%   0.00% 
   ```

3. metric
   ![](./pic/dungeon-********-2.png)

#### 第三版 - 1500人

1. robot数据 --- 平均每秒597个战斗协议

   ```
   压测基础情况
   开始时间：2021/07/07 15:17:22, 结束时间:2021/07/07 15:22:21, 耗时:299(s)
   登录成功机器人总数：1500
   总消息数:487500， 平均每秒消息数:1630
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确比   
   协议:cmd:11803,MSG_C2L_DungeonRecvAward                   33221   43995  75.51%
   协议:cmd:10100,                                            1500    1500 100.00%
   协议:cmd:11003,MSG_C2L_Flush                               1500    1500 100.00%
   协议:cmd:11801,MSG_C2L_DungeonFight                      179302  179302 100.00%
   协议:cmd:11809,MSG_C2L_Dungeon                             1500    1500 100.00%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                         1500   22500   6.67%
   协议:cmd:11807,MSG_C2L_DungeonPreview                    111605  111605 100.00%
   协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward              94216  110598  85.19%
   协议:cmd:11029,MSG_C2L_Formation                           1500    1500 100.00%
   协议:cmd:11905,MSG_C2L_HeroStageUp                         1500   15000  10.00%
   协议正确数:427344 协议总数:489000 正确比:87.39%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-100000- 
   协议:cmd:11803,MSG_C2L_DungeonRecvAward                  1.57%   2.81%   8.37%  10.94%  19.64%  39.78%   0.00% 
   协议:cmd:10100,                                         14.13%  22.80%  57.20%   5.87%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.80%   1.47%   2.73%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11801,MSG_C2L_DungeonFight                      1.66%   3.42%   7.99%  11.22%  19.44%  39.71%   0.00% 
   协议:cmd:11809,MSG_C2L_Dungeon                           0.00%   0.00%   0.00%   0.00%   0.00%  96.40%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                       0.00%   0.00%   0.00%   0.00%  40.19%  57.02%   0.00% 
   协议:cmd:11807,MSG_C2L_DungeonPreview                    2.36%   3.88%   8.49%  11.09%  19.09%  38.97%   0.00% 
   协议:cmd:11805,MSG_C2L_DungeonSpeedRecvAward             2.70%   4.34%   8.92%  11.63%  20.00%  37.99%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                         0.00%   0.00%   0.00%   0.00%  30.13%  54.60%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                       0.00%   0.00%   0.00%   0.00%  40.09%  57.09%   0.00% 
   ```

2. 游戏数据 --- 消息平均积压数量1500左右

   ```
   所有超时的left
   [2021/07/07 15:17:23.186837] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:17:23.168381792 +0800 CST msgs count per second: gate(pck/s: 5765 sum: 7773  left:7615) logic db(pck/s: 1000 sum: 3000 left:0) redis db(pck/s: 2069 sum: 6087 left:0) 
   [2021/07/07 15:18:01.286427] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:18:01.268080852 +0800 CST msgs count per second: gate(pck/s: 3093 sum: 105105  left:1407) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 748 sum: 20500 left:0) 
   [2021/07/07 15:18:31.293927] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:18:31.268097923 +0800 CST msgs count per second: gate(pck/s: 2573 sum: 154088  left:1927) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 519 sum: 34156 left:0) 
   [2021/07/07 15:18:41.183262] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:18:41.168296908 +0800 CST msgs count per second: gate(pck/s: 1398 sum: 169978  left:1117) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 337 sum: 37888 left:0) 
   [2021/07/07 15:18:43.292220] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:18:43.287483874 +0800 CST msgs count per second: gate(pck/s: 1917 sum: 174433  left:1083) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 472 sum: 38680 left:5) 
   [2021/07/07 15:18:52.280814] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:18:52.268452312 +0800 CST msgs count per second: gate(pck/s: 3441 sum: 189458  left:1059) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 491 sum: 42132 left:0) 
   [2021/07/07 15:18:58.303347] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:18:58.268008609 +0800 CST msgs count per second: gate(pck/s: 1945 sum: 198463  left:1055) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 508 sum: 44454 left:0) 
   [2021/07/07 15:19:01.270900] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:01.268136515 +0800 CST msgs count per second: gate(pck/s: 2405 sum: 203423  left:2095) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 486 sum: 45616 left:1) 
   [2021/07/07 15:19:04.277869] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:04.268006884 +0800 CST msgs count per second: gate(pck/s: 1883 sum: 208901  left:1117) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 551 sum: 48249 left:0) 
   [2021/07/07 15:19:07.290131] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:07.269730759 +0800 CST msgs count per second: gate(pck/s: 1978 sum: 213496  left:1023) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 497 sum: 49464 left:0) 
   [2021/07/07 15:19:10.292653] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:10.268007871 +0800 CST msgs count per second: gate(pck/s: 1954 sum: 217973  left:1046) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 527 sum: 50648 left:0) 
   [2021/07/07 15:19:11.265320] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:11.177321621 +0800 CST msgs count per second: gate(pck/s: 1493 sum: 219466  left:2553) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 336 sum: 50984 left:5) 
   [2021/07/07 15:19:13.304182] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:13.281291487 +0800 CST msgs count per second: gate(pck/s: 1988 sum: 224007  left:1012) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 533 sum: 51872 left:0) 
   [2021/07/07 15:19:16.275477] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:16.268039442 +0800 CST msgs count per second: gate(pck/s: 1858 sum: 228377  left:1142) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 456 sum: 52965 left:0) 
   [2021/07/07 15:19:19.293049] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:19.26840441 +0800 CST msgs count per second: gate(pck/s: 1779 sum: 232799  left:1221) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 489 sum: 54199 left:0) 
   [2021/07/07 15:19:22.290216] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:22.268345606 +0800 CST msgs count per second: gate(pck/s: 3385 sum: 238905  left:1115) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 516 sum: 55443 left:0) 
   [2021/07/07 15:19:25.295992] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:25.26851514 +0800 CST msgs count per second: gate(pck/s: 1997 sum: 243517  left:1003) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 569 sum: 56744 left:0) 
   [2021/07/07 15:19:28.272550] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:28.268037223 +0800 CST msgs count per second: gate(pck/s: 1881 sum: 247902  left:1119) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 516 sum: 57941 left:0) 
   [2021/07/07 15:19:31.278938] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:31.270679574 +0800 CST msgs count per second: gate(pck/s: 2233 sum: 252754  left:2267) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 539 sum: 59210 left:0) 
   [2021/07/07 15:19:34.285886] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:34.269367924 +0800 CST msgs count per second: gate(pck/s: 1847 sum: 258368  left:1153) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 512 sum: 60465 left:0) 
   [2021/07/07 15:19:37.294389] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:37.271930335 +0800 CST msgs count per second: gate(pck/s: 1925 sum: 262946  left:1076) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 520 sum: 61717 left:0) 
   [2021/07/07 15:19:40.268922] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:40.268085721 +0800 CST msgs count per second: gate(pck/s: 1785 sum: 267307  left:1215) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 479 sum: 62914 left:0) 
   [2021/07/07 15:19:41.199454] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:41.168181195 +0800 CST msgs count per second: gate(pck/s: 1547 sum: 268854  left:1592) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 366 sum: 63280 left:0) 
   [2021/07/07 15:19:43.275791] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:43.268447576 +0800 CST msgs count per second: gate(pck/s: 1806 sum: 273328  left:1194) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 461 sum: 64075 left:0) 
   [2021/07/07 15:19:46.283337] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:46.268313097 +0800 CST msgs count per second: gate(pck/s: 1841 sum: 277863  left:1159) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 517 sum: 65321 left:0) 
   [2021/07/07 15:19:49.290661] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:49.268953571 +0800 CST msgs count per second: gate(pck/s: 1928 sum: 282451  left:1072) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 496 sum: 66506 left:0) 
   [2021/07/07 15:19:52.297236] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:52.282113044 +0800 CST msgs count per second: gate(pck/s: 3370 sum: 288393  left:1130) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 1309 sum: 68459 left:0) 
   [2021/07/07 15:19:55.272791] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:55.268640615 +0800 CST msgs count per second: gate(pck/s: 1812 sum: 292835  left:1188) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 483 sum: 70328 left:0) 
   [2021/07/07 15:19:58.279983] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:19:58.268013704 +0800 CST msgs count per second: gate(pck/s: 1845 sum: 297369  left:1155) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 465 sum: 71458 left:0) 
   [2021/07/07 15:20:01.313836] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:01.268271581 +0800 CST msgs count per second: gate(pck/s: 2275 sum: 302299  left:2225) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 482 sum: 72597 left:0) 
   [2021/07/07 15:20:04.294351] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:04.268637362 +0800 CST msgs count per second: gate(pck/s: 1776 sum: 307800  left:1224) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 442 sum: 73702 left:0) 
   [2021/07/07 15:20:07.269837] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:07.268060675 +0800 CST msgs count per second: gate(pck/s: 1864 sum: 312388  left:1137) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 479 sum: 74840 left:0) 
   [2021/07/07 15:20:10.277054] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:10.268101247 +0800 CST msgs count per second: gate(pck/s: 1798 sum: 316823  left:1202) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 451 sum: 75972 left:0) 
   [2021/07/07 15:20:11.186003] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:11.16875191 +0800 CST msgs count per second: gate(pck/s: 1423 sum: 318246  left:1440) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 316 sum: 76288 left:0) 
   [2021/07/07 15:20:16.290946] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:16.268298789 +0800 CST msgs count per second: gate(pck/s: 1918 sum: 327443  left:1082) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 482 sum: 78321 left:0) 
   [2021/07/07 15:20:19.298233] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:19.26917928 +0800 CST msgs count per second: gate(pck/s: 1831 sum: 331857  left:1169) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 469 sum: 79460 left:0) 
   [2021/07/07 15:20:22.274820] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:22.268063711 +0800 CST msgs count per second: gate(pck/s: 3320 sum: 337846  left:1180) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 449 sum: 80613 left:0) 
   [2021/07/07 15:20:23.219746] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:23.174392517 +0800 CST msgs count per second: gate(pck/s: 1348 sum: 339194  left:1052) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 342 sum: 80955 left:0) 
   [2021/07/07 15:20:25.281060] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:25.268396787 +0800 CST msgs count per second: gate(pck/s: 1789 sum: 342315  left:1211) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 479 sum: 81805 left:0) 
   [2021/07/07 15:20:28.288618] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:28.269011723 +0800 CST msgs count per second: gate(pck/s: 1789 sum: 346816  left:1211) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 475 sum: 82990 left:0) 
   [2021/07/07 15:20:31.295208] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:31.268271018 +0800 CST msgs count per second: gate(pck/s: 2125 sum: 351652  left:2375) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 493 sum: 84215 left:0) 
   [2021/07/07 15:20:34.284228] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:34.26838736 +0800 CST msgs count per second: gate(pck/s: 1817 sum: 357344  left:1183) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 508 sum: 85444 left:0) 
   [2021/07/07 15:20:37.282923] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:37.268106422 +0800 CST msgs count per second: gate(pck/s: 1783 sum: 361810  left:1218) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 473 sum: 86627 left:0) 
   [2021/07/07 15:20:40.317351] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:40.286851108 +0800 CST msgs count per second: gate(pck/s: 1808 sum: 366336  left:1192) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 453 sum: 87810 left:0) 
   [2021/07/07 15:20:41.184230] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:41.168210721 +0800 CST msgs count per second: gate(pck/s: 1387 sum: 367723  left:1405) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 338 sum: 88148 left:0) 
   [2021/07/07 15:20:43.293359] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:43.272601388 +0800 CST msgs count per second: gate(pck/s: 1807 sum: 372335  left:1193) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 1137 sum: 90359 left:0) 
   [2021/07/07 15:20:46.299503] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:46.268483216 +0800 CST msgs count per second: gate(pck/s: 1854 sum: 376882  left:1146) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 497 sum: 91676 left:0) 
   [2021/07/07 15:20:49.276735] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:49.268314263 +0800 CST msgs count per second: gate(pck/s: 1796 sum: 381325  left:1204) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 479 sum: 92865 left:0) 
   [2021/07/07 15:20:52.281850] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:52.275721141 +0800 CST msgs count per second: gate(pck/s: 3315 sum: 387344  left:1185) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 443 sum: 94036 left:0) 
   [2021/07/07 15:20:55.288937] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:55.268171498 +0800 CST msgs count per second: gate(pck/s: 1742 sum: 391771  left:1258) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 477 sum: 95173 left:0) 
   [2021/07/07 15:20:58.309708] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:20:58.268041639 +0800 CST msgs count per second: gate(pck/s: 1845 sum: 396375  left:1155) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 471 sum: 96340 left:1) 
   [2021/07/07 15:21:01.273056] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:01.268172228 +0800 CST msgs count per second: gate(pck/s: 2137 sum: 401167  left:2363) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 472 sum: 97476 left:0) 
   [2021/07/07 15:21:04.280787] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:04.268165875 +0800 CST msgs count per second: gate(pck/s: 1793 sum: 406823  left:1207) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 434 sum: 98591 left:0) 
   [2021/07/07 15:21:07.291976] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:07.26803014 +0800 CST msgs count per second: gate(pck/s: 1787 sum: 411317  left:1214) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 444 sum: 99655 left:0) 
   [2021/07/07 15:21:10.293365] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:10.268656408 +0800 CST msgs count per second: gate(pck/s: 1841 sum: 415872  left:1159) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 347 sum: 100638 left:0) 
   [2021/07/07 15:21:11.192210] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:11.168449499 +0800 CST msgs count per second: gate(pck/s: 1418 sum: 417290  left:1563) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 276 sum: 100914 left:0) 
   [2021/07/07 15:21:13.270163] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:13.268574435 +0800 CST msgs count per second: gate(pck/s: 1771 sum: 421802  left:1229) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 397 sum: 101614 left:0) 
   [2021/07/07 15:21:16.277272] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:16.268674849 +0800 CST msgs count per second: gate(pck/s: 1730 sum: 426261  left:1270) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 406 sum: 102626 left:0) 
   [2021/07/07 15:21:19.283447] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:19.269002529 +0800 CST msgs count per second: gate(pck/s: 1778 sum: 430810  left:1222) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 357 sum: 103532 left:0) 
   [2021/07/07 15:21:22.290199] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:22.268268709 +0800 CST msgs count per second: gate(pck/s: 3310 sum: 436842  left:1190) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 365 sum: 104428 left:0) 
   [2021/07/07 15:21:25.297184] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:25.268456577 +0800 CST msgs count per second: gate(pck/s: 1838 sum: 441370  left:1162) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 358 sum: 105320 left:0) 
   [2021/07/07 15:21:28.303783] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:28.282114474 +0800 CST msgs count per second: gate(pck/s: 1820 sum: 445853  left:1180) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 306 sum: 106108 left:0) 
   [2021/07/07 15:21:31.311193] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:31.282435789 +0800 CST msgs count per second: gate(pck/s: 2105 sum: 450638  left:2395) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 255 sum: 106810 left:0) 
   [2021/07/07 15:21:34.287872] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:34.273287009 +0800 CST msgs count per second: gate(pck/s: 1782 sum: 456315  left:1218) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 473 sum: 108982 left:0) 
   [2021/07/07 15:21:37.294763] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:37.268465819 +0800 CST msgs count per second: gate(pck/s: 1865 sum: 460898  left:1136) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 241 sum: 109591 left:0) 
   [2021/07/07 15:21:40.287800] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:40.28577734 +0800 CST msgs count per second: gate(pck/s: 1739 sum: 465273  left:1261) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 224 sum: 110139 left:0) 
   [2021/07/07 15:21:41.207528] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:41.182989307 +0800 CST msgs count per second: gate(pck/s: 1595 sum: 466868  left:1882) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 158 sum: 110297 left:0) 
   [2021/07/07 15:21:43.277254] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:43.268079863 +0800 CST msgs count per second: gate(pck/s: 1768 sum: 471302  left:1232) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 170 sum: 110612 left:0) 
   [2021/07/07 15:21:46.284455] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:46.268013631 +0800 CST msgs count per second: gate(pck/s: 1822 sum: 475856  left:1178) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 156 sum: 111032 left:0) 
   [2021/07/07 15:21:49.291748] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:49.286563441 +0800 CST msgs count per second: gate(pck/s: 1866 sum: 480401  left:1134) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 143 sum: 111442 left:0) 
   [2021/07/07 15:21:52.298440] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:52.268888968 +0800 CST msgs count per second: gate(pck/s: 3323 sum: 486358  left:1177) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 132 sum: 111809 left:0) 
   [2021/07/07 15:21:55.274905] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:55.268391495 +0800 CST msgs count per second: gate(pck/s: 1823 sum: 490858  left:1177) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 121 sum: 112152 left:0) 
   [2021/07/07 15:21:58.281261] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:21:58.268273944 +0800 CST msgs count per second: gate(pck/s: 1793 sum: 495329  left:1207) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 101 sum: 112446 left:0) 
   [2021/07/07 15:22:01.288748] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:01.268903826 +0800 CST msgs count per second: gate(pck/s: 2127 sum: 500163  left:2373) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 101 sum: 112698 left:0) 
   [2021/07/07 15:22:04.295375] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:04.276123909 +0800 CST msgs count per second: gate(pck/s: 1840 sum: 505876  left:1160) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 76 sum: 112910 left:0) 
   [2021/07/07 15:22:07.273084] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:07.268638377 +0800 CST msgs count per second: gate(pck/s: 1775 sum: 510311  left:1226) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 66 sum: 113085 left:0) 
   [2021/07/07 15:22:10.278310] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:10.268395086 +0800 CST msgs count per second: gate(pck/s: 1788 sum: 514825  left:1212) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 50 sum: 113238 left:0) 
   [2021/07/07 15:22:11.212834] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:11.172793642 +0800 CST msgs count per second: gate(pck/s: 1499 sum: 516324  left:2115) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 41 sum: 113279 left:0) 
   [2021/07/07 15:22:13.284863] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:13.268079693 +0800 CST msgs count per second: gate(pck/s: 1817 sum: 520854  left:1183) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 50 sum: 113357 left:0) 
   [2021/07/07 15:22:16.291776] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:16.268430431 +0800 CST msgs count per second: gate(pck/s: 1736 sum: 525273  left:1264) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 42 sum: 113465 left:0) 
   [2021/07/07 15:22:19.301169] [INFO] (/home/<USER>/ngame/workspace/压测游戏服/src/app/logic/service/timer.go:46) NOW: 2021-07-07 15:22:19.272384068 +0800 CST msgs count per second: gate(pck/s: 1840 sum: 529878  left:1160) logic db(pck/s: 0 sum: 3000 left:0) redis db(pck/s: 39 sum: 113566 left:0) 
   
   --------------------------
   
   logic服务器启动时间:971.624321ms
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(  63)                                0.00%  96.83%   3.17%   0.00%   0.00%   0.00%   0.00% 
   协议:(/home/<USER>/go/pkg/mod/gitlab.qdream.com/kit/sea@v0.0.0-20210705080433-8f4dd478eb9f/event/event.go:57)(   3)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:3(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11805(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:6(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11905(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100023(   2)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11801(  96)                               69.79%  26.04%   4.17%   0.00%   0.00%   0.00%   0.00%
   ```

3. metric

   ![](./pic/dungeon-********-3.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Jul 7, 2021 at 3:19pm (CST)
   Duration: 30.20s, Total samples = 33.30s (110.27%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 13.25s, 39.79% of 33.30s total
   Dropped 780 nodes (cum <= 0.17s)
   Showing top 20 nodes out of 263
         flat  flat%   sum%        cum   cum%
        2.55s  7.66%  7.66%      3.21s  9.64%  runtime.mapaccess1_fast32
        1.84s  5.53% 13.18%      3.06s  9.19%  runtime.scanobject
        0.84s  2.52% 15.71%      1.99s  5.98%  runtime.mallocgc
        0.79s  2.37% 18.08%      0.79s  2.37%  runtime.epollwait
        0.74s  2.22% 20.30%      1.04s  3.12%  runtime.mapiternext
        0.68s  2.04% 22.34%      0.74s  2.22%  runtime.findObject
        0.67s  2.01% 24.35%      0.67s  2.01%  runtime.memhash32
        0.51s  1.53% 25.89%      0.51s  1.53%  runtime.futex
        0.50s  1.50% 27.39%      0.51s  1.53%  syscall.Syscall
        0.49s  1.47% 28.86%      4.95s 14.86%  app/logic/battle.(*OneSkillAttack).DoSkillHurt
        0.45s  1.35% 30.21%      0.45s  1.35%  app/protos/out/cl.sovCl
        0.44s  1.32% 31.53%      3.02s  9.07%  app/logic/battle.(*OneSkillAttack).Prepare
        0.41s  1.23% 32.76%      0.98s  2.94%  runtime.mapiterinit
        0.37s  1.11% 33.87%      0.52s  1.56%  app/logic/battle.(*AttrManager).GetAttr
        0.37s  1.11% 34.98%      0.48s  1.44%  runtime.heapBitsSetType
        0.36s  1.08% 36.07%      0.56s  1.68%  runtime.mapaccess2_fast32
        0.32s  0.96% 37.03%      0.49s  1.47%  app/logic/battle.(*ExecutionPool).ResetBaseEffect
        0.32s  0.96% 37.99%      0.32s  0.96%  runtime.duffcopy
        0.30s   0.9% 38.89%      3.02s  9.07%  app/logic/battle.(*Manager).TriggerPSE
        0.30s   0.9% 39.79%      0.30s   0.9%  runtime.markBits.isMarked (inline)
   ```

   

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Jul 7, 2021 at 3:19pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 70.84MB, 64.85% of 109.24MB total
   Dropped 116 nodes (cum <= 0.55MB)
   Showing top 20 nodes out of 170
         flat  flat%   sum%        cum   cum%
      23.70MB 21.70% 21.70%    23.70MB 21.70%  github.com/gogo/protobuf/proto.Marshal
          8MB  7.32% 29.02%        8MB  7.32%  app/logic/character.NewEquip (inline)
       3.50MB  3.21% 32.23%        8MB  7.33%  app/logic/character.(*HeroM).Add
       3.50MB  3.20% 35.43%     3.50MB  3.20%  app/logic/character.(*Handbook).Add
       3.50MB  3.20% 38.64%     4.50MB  4.12%  app/logic/character.newHero (inline)
          3MB  2.75% 41.38%        3MB  2.75%  app/logic/character.(*Hero).calcTotalAttr
       2.64MB  2.42% 43.80%     2.64MB  2.42%  compress/flate.NewWriter
       2.50MB  2.29% 46.09%     3.50MB  3.20%  app/logic/character.(*User).TaskTypeOnEvent
       2.50MB  2.29% 48.38%        3MB  2.75%  app/logic/character.(*User).addAllNum
       2.04MB  1.87% 50.25%     3.22MB  2.95%  app/logic/db.(*RedisActor).Create
          2MB  1.83% 52.09%       10MB  9.16%  app/logic/character.(*EquipM).Add
          2MB  1.83% 53.92%        2MB  1.83%  app/logic/character.(*User).mergeResources
          2MB  1.83% 55.75%     2.50MB  2.29%  app/logic/helper/sync.(*MessageSync).append
       1.63MB  1.49% 57.24%     1.63MB  1.49%  app/goxml.(*MonsterInfoManager).Load
       1.50MB  1.37% 58.61%     1.50MB  1.37%  app/logic/helper/monitor.(*Cmds).Add
       1.50MB  1.37% 59.98%     2.50MB  2.29%  app/logic/character.(*User).initModule
       1.50MB  1.37% 61.36%     1.50MB  1.37%  app/goxml.(*SkillInfoManager).Load
       1.50MB  1.37% 62.73%     1.50MB  1.37%  app/goxml.(*TowerInfo).prepare
       1.16MB  1.06% 63.79%     1.16MB  1.06%  bufio.NewReaderSize (inline)
       1.16MB  1.06% 64.85%     2.31MB  2.12%  gitlab.qdream.com/kit/sea/redis.DialTimeout
   ```

    

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Jul 7, 2021 at 3:19pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 2874.14MB, 58.05% of 4951.24MB total
   Dropped 558 nodes (cum <= 24.76MB)
   Showing top 20 nodes out of 175
         flat  flat%   sum%        cum   cum%
    1008.30MB 20.36% 20.36%  1034.80MB 20.90%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     401.34MB  8.11% 28.47%   424.35MB  8.57%  github.com/gogo/protobuf/proto.Marshal
     120.01MB  2.42% 30.89%   121.51MB  2.45%  app/logic/character.(*User).mergeResources
     104.01MB  2.10% 32.99%   104.01MB  2.10%  app/logic/battle.(*Member).Flush
     103.09MB  2.08% 35.08%   105.09MB  2.12%  app/logic/db/redisop.ClClient.SetSomeEquipmentMCallSKs
      95.01MB  1.92% 37.00%   175.03MB  3.54%  context.WithDeadline
      93.51MB  1.89% 38.88%   292.52MB  5.91%  app/logic/battle.(*SkillManager).AddNewSkill
         88MB  1.78% 40.66%       88MB  1.78%  internal/reflectlite.Swapper
         85MB  1.72% 42.38%       85MB  1.72%  app/logic/battle.(*SkillManager).Reset
         85MB  1.72% 44.10%       85MB  1.72%  container/list.(*List).insertValue
      79.51MB  1.61% 45.70%    80.02MB  1.62%  time.AfterFunc
      74.02MB  1.49% 47.20%    74.02MB  1.49%  github.com/json-iterator/go.(*frozenConfig).Marshal
      73.16MB  1.48% 48.67%    86.52MB  1.75%  compress/flate.NewWriter
      72.07MB  1.46% 50.13%    81.07MB  1.64%  encoding/xml.(*Decoder).rawToken
         72MB  1.45% 51.58%       72MB  1.45%  app/logic/battle.(*Manager).GetPassiveTarget
      70.51MB  1.42% 53.01%    70.51MB  1.42%  app/logic/battle.(*BuffManager).addBuff
      70.01MB  1.41% 54.42%   206.51MB  4.17%  app/logic/battle.(*SkillManager).AddPassiveSkillEffect
      66.51MB  1.34% 55.76%    66.51MB  1.34%  app/logic/battle.(*Team).AltAttr
      56.58MB  1.14% 56.91%    56.58MB  1.14%  app/logic/db/redisop.DbClient.SetSomeCommonRankMCallSKs
      56.50MB  1.14% 58.05%    56.50MB  1.14%  app/protos/out/cl.(*Avatar).Clone
   ```

   