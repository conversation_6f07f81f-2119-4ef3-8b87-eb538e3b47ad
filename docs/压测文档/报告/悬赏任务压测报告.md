## 悬赏任务压测报告

#### 第一版

1. robot数据

   ```
   2021-08-11 18:13:24 压测数据分析
   压测基础情况
   开始时间：2021/08/11 18:08:19, 结束时间:2021/08/11 18:13:13, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:1371551， 平均每秒消息数:4665
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比
   协议:cmd:10100,                                            5000    5000            0.36%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.36%  100.00%
   协议:cmd:12905,MSG_C2L_DispatchGetAwards                 321366  380993           23.35%   84.35%
   协议:cmd:11025,MSG_C2L_GM                                  5000    5000            0.36%  100.00%
   协议:cmd:12903,MSG_C2L_DispatchReceiveTask               388054  487093           28.19%   79.67%
   协议:cmd:12901,MSG_C2L_DispatchTasks                       5000    5000            0.36%  100.00%
   协议:cmd:12907,MSG_C2L_DispatchRefreshTask               488465  488465           35.48%  100.00%
   协议正确数:1217885 协议总数:1376551 正确比:88.47%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000
   协议:cmd:10100,                                         99.06%   0.92%   0.02%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11003,MSG_C2L_Flush                            97.30%   0.46%   2.24%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12905,MSG_C2L_DispatchGetAwards                98.99%   0.85%   0.16%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:11025,MSG_C2L_GM                               99.64%   0.26%   0.10%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12903,MSG_C2L_DispatchReceiveTask              99.06%   0.81%   0.13%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12901,MSG_C2L_DispatchTasks                    99.54%   0.36%   0.10%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:cmd:12907,MSG_C2L_DispatchRefreshTask              99.00%   0.85%   0.15%   0.00%   0.00%   0.00%   0.00%   0.00%
   
   ---------------------
   
   logic服数情况
   ```
   
2. 游戏数据

   ```
   
   --------------------------
   
   所有超时的left
   [2021/08/11 18:13:14.127937] gate(pck/s: 5202 sum: 1646021 left:793) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1028 sum: 131655 left:1343) [2021/08/11 18:01:29.122595] gate(pck/s: 6102 sum: 120652 left:740) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 1524 sum: 21631 left:2546)
   
   --------------------------
   
   logic服务器启动时间:1.300331991s
   logic账号总数:0
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000
   协议:      100006(   7)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:     event:2(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:  event_1030(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:  event_1031(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12903(   4)                               25.00%  75.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12905(   3)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00%
   协议:       12907(   6)                               66.67%  33.33%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric
   ![dispatch](./pic/dispatch_20210811.png)

4. pprof 

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-**************
   File: service
   Type: cpu
   Time: Aug 11, 2021 at 6:10pm (CST)
   Duration: 30s, Total samples = 13.20s (44.00%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 5220ms, 39.55% of 13200ms total
   Dropped 418 nodes (cum <= 66ms)
   Showing top 10 nodes out of 225
         flat  flat%   sum%        cum   cum%
       1530ms 11.59% 11.59%     2780ms 21.06%  runtime.scanobject
        680ms  5.15% 16.74%      800ms  6.06%  syscall.Syscall
        580ms  4.39% 21.14%      580ms  4.39%  runtime.epollwait
        530ms  4.02% 25.15%      560ms  4.24%  runtime.findObject
        410ms  3.11% 28.26%      410ms  3.11%  runtime.futex
        380ms  2.88% 31.14%      430ms  3.26%  runtime.heapBitsSetType
        320ms  2.42% 33.56%     1250ms  9.47%  runtime.mallocgc
        320ms  2.42% 35.98%      320ms  2.42%  runtime.markBits.isMarked (inline)
        280ms  2.12% 38.11%      300ms  2.27%  runtime.nanotime
        190ms  1.44% 39.55%      190ms  1.44%  runtime.nextFreeFast (inline)
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof heap-**************
   File: service
   Type: inuse_space
   Time: Aug 11, 2021 at 6:10pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 87.02MB, 50.10% of 173.70MB total
   Dropped 119 nodes (cum <= 0.87MB)
   Showing top 10 nodes out of 141
         flat  flat%   sum%        cum   cum%
         19MB 10.94% 10.94%       25MB 14.39%  app/logic/character.newHero
         13MB  7.49% 18.42%       14MB  8.06%  app/logic/character.(*User).TaskTypeOnEvent
       8.50MB  4.89% 23.32%     8.50MB  4.89%  container/list.(*List).insertValue
          8MB  4.61% 27.92%        8MB  4.61%  app/logic/character.(*Handbook).Add
       7.50MB  4.32% 32.24%     7.50MB  4.32%  app/protos/out/cl.(*DispatchTask).Clone
          7MB  4.03% 36.28%        7MB  4.03%  app/logic/helper/monitor.(*Cmds).Add
          7MB  4.03% 40.31%     7.50MB  4.32%  app/logic/character.(*Dispatch).addDispatchTask
       6.50MB  3.74% 44.05%    12.50MB  7.20%  app/logic/character.(*User).initModule
          6MB  3.45% 47.50%        6MB  3.45%  app/logic/character.initHeroFromData (inline)
       4.50MB  2.59% 50.10%     4.50MB  2.59%  app/logic/character.(*Avatar).Add
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Aug 11, 2021 at 6:10pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 2922.37MB, 54.68% of 5344.97MB total
   Dropped 421 nodes (cum <= 26.72MB)
   Showing top 10 nodes out of 138
         flat  flat%   sum%        cum   cum%
     995.73MB 18.63% 18.63%  1009.73MB 18.89%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     527.04MB  9.86% 28.49%   821.56MB 15.37%  context.WithDeadline
     294.52MB  5.51% 34.00%   294.52MB  5.51%  time.AfterFunc
     227.51MB  4.26% 38.26%   227.51MB  4.26%  container/list.(*List).insertValue (inline)
     217.52MB  4.07% 42.33%   217.52MB  4.07%  app/protos/out/cl.(*DispatchTask).Clone
     149.01MB  2.79% 45.11%   150.01MB  2.81%  app/logic/character.(*User).mergeResources
     144.01MB  2.69% 47.81%   398.52MB  7.46%  app/logic/character.(*User).pushMsg
     130.52MB  2.44% 50.25%   340.54MB  6.37%  app/logic/character.(*User).SendSelfToClient
     124.01MB  2.32% 52.57%   462.53MB  8.65%  app/logic/character.(*Dispatch).addDispatchTask
     112.51MB  2.10% 54.68%   112.51MB  2.10%  app/protos/out/cl.(*TaskTypeProgress).Clone
   (pprof)
   ```
   
   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-**************
   File: service
   Type: inuse_objects
   Time: Aug 11, 2021 at 6:10pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top
   Showing nodes accounting for 1297441, 50.53% of 2567913 total
   Dropped 132 nodes (cum <= 12839)
   Showing top 10 nodes out of 128
         flat  flat%   sum%        cum   cum%
       338620 13.19% 13.19%     417269 16.25%  app/logic/character.newHero
       185691  7.23% 20.42%     185691  7.23%  container/list.(*List).insertValue (inline)
       159560  6.21% 26.63%     175944  6.85%  app/logic/character.(*User).TaskTypeOnEvent
       124525  4.85% 31.48%     124525  4.85%  app/protos/out/cl.(*DispatchTask).Clone (inline)
       105877  4.12% 35.60%     105877  4.12%  app/logic/character.(*Handbook).Add
        87381  3.40% 39.01%     218457  8.51%  app/logic/helper/sync.(*MessageSync).append
        83018  3.23% 42.24%      88479  3.45%  app/logic/character.(*Dispatch).addDispatchTask
        78649  3.06% 45.30%      78649  3.06%  app/logic/character.initHeroFromData (inline)
        72091  2.81% 48.11%     166029  6.47%  app/logic/command/dispatch.(*C2LDispatchReceiveTaskCommand).Execute
        62029  2.42% 50.53%      62029  2.42%  app/logic/character.(*Avatar).Add
   (pprof)
   ```
