## 英雄压测报告


#### 第一版

1. robot数据

   ```
   2021-08-04 11:04:02 压测数据分析
   压测基础情况
   开始时间：2021/08/04 10:57:47, 结束时间:2021/08/04 11:02:42, 耗时:295(s)
   登录成功机器人总数：5000
   总消息数:1439337， 平均每秒消息数:4879
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:10100,                                            5000    5000            0.35%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.35%  100.00%
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                   24500   50052            1.70%   48.95%
   协议:cmd:11025,MSG_C2L_GM                                  5000    5000            0.35%  100.00%
   协议:cmd:11919,MSG_C2L_HeroBack                            9231    9231            0.64%  100.00%
   协议:cmd:11903,MSG_C2L_HeroLevelUp                       131992  140979            9.14%   93.63%
   协议:cmd:11921,MSG_C2L_HeroRevive                         32672   32672            2.26%  100.00%
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus              346129  364193           23.96%   95.04%
   协议:cmd:11901,MSG_C2L_HeroList                            5000    5000            0.35%  100.00%
   协议:cmd:11029,MSG_C2L_Formation                          15000   15000            1.04%  100.00%
   协议:cmd:11907,MSG_C2L_HeroStarUp                         10594   10594            0.73%  100.00%
   协议:cmd:11913,MSG_C2L_HeroDecompose                      73714  140524            5.10%   52.46%
   协议:cmd:11909,MSG_C2L_HeroBuySlot                       350000  519784           24.23%   67.34%
   协议:cmd:11905,MSG_C2L_HeroStageUp                        96089  141308            6.65%   68.00%
   协议正确数:1109921 协议总数:1444337 正确比:76.85%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-************ 1000-100000 
   协议:cmd:10100,                                         20.72%  33.00%  44.20%   2.08%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.64%   1.56%   2.80%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11923,MSG_C2L_HeroChangeRandom                 98.70%   0.94%   0.31%   0.06%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11025,MSG_C2L_GM                                0.22%   0.10%   1.26%   1.96%   1.50%   9.56%  13.70%  71.70% 
   协议:cmd:11919,MSG_C2L_HeroBack                         98.86%   0.76%   0.35%   0.03%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11903,MSG_C2L_HeroLevelUp                      98.60%   0.96%   0.39%   0.04%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11921,MSG_C2L_HeroRevive                       98.52%   1.08%   0.35%   0.05%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11911,MSG_C2L_HeroUpdateLockStatus             98.60%   0.97%   0.38%   0.05%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11901,MSG_C2L_HeroList                         82.58%  12.92%   4.50%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                        64.98%   1.46%   0.23%   0.00%   0.00%   0.00%  33.33%   0.00% 
   协议:cmd:11909,MSG_C2L_HeroBuySlot                      98.56%   0.94%   0.42%   0.08%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11907,MSG_C2L_HeroStarUp                       98.84%   0.76%   0.35%   0.05%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11913,MSG_C2L_HeroDecompose                    98.61%   0.93%   0.41%   0.06%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11905,MSG_C2L_HeroStageUp                      98.66%   0.93%   0.36%   0.05%   0.00%   0.00%   0.00%   0.00%
   ```
   
2. 游戏数据

   ```
   所有超时的left
   [2021/08/04 11:02:43.260796] gate(pck/s: 11866 sum: 1598902 left:0) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 3831 sum: 49245 left:1285) 
   
   --------------------------
   
   logic服务器启动时间:
   logic账号总数:
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(  12)                                0.00%  91.67%   8.33%   0.00%   0.00%   0.00%   0.00% 
   协议:       10104(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11903(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11913(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11923(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11905(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11909(   4)                                0.00%  50.00%  50.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
3. metric
   ![](./pic/hero-********-1.png)

4. pprof

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-********110017
   File: service
   Type: cpu
   Time: Aug 4, 2021 at 11:00am (CST)
   Duration: 30s, Total samples = 9.99s (33.30%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 4.89s, 48.95% of 9.99s total
   Dropped 397 nodes (cum <= 0.05s)
   Showing top 20 nodes out of 254
         flat  flat%   sum%        cum   cum%
        0.72s  7.21%  7.21%      0.72s  7.21%  runtime.epollwait
        0.69s  6.91% 14.11%      1.14s 11.41%  runtime.scanobject
        0.48s  4.80% 18.92%      0.57s  5.71%  syscall.Syscall
        0.36s  3.60% 22.52%      0.36s  3.60%  runtime.futex
        0.31s  3.10% 25.63%      0.34s  3.40%  runtime.nanotime (inline)
        0.26s  2.60% 28.23%      0.29s  2.90%  runtime.findObject
        0.24s  2.40% 30.63%      0.31s  3.10%  runtime.heapBitsSetType
        0.23s  2.30% 32.93%      0.49s  4.90%  runtime.selectgo
        0.20s  2.00% 34.93%      0.92s  9.21%  runtime.mallocgc
        0.20s  2.00% 36.94%      0.20s  2.00%  runtime.memmove
        0.17s  1.70% 38.64%      0.17s  1.70%  github.com/json-iterator/go.(*Stream).WriteString
        0.15s  1.50% 40.14%      0.19s  1.90%  runtime.mapaccess1_fast32
        0.15s  1.50% 41.64%      0.87s  8.71%  runtime.netpoll
        0.12s  1.20% 42.84%      1.53s 15.32%  runtime.findrunnable
        0.12s  1.20% 44.04%      0.12s  1.20%  runtime.nextFreeFast (inline)
        0.10s  1.00% 45.05%      0.12s  1.20%  app/logic/character.(*User).ResetDaily
        0.10s  1.00% 46.05%      0.46s  4.60%  github.com/json-iterator/go.(*structFieldEncoder).Encode
        0.10s  1.00% 47.05%      0.10s  1.00%  runtime.add (inline)
        0.10s  1.00% 48.05%      0.10s  1.00%  runtime.unlock2
        0.09s   0.9% 48.95%      0.09s   0.9%  container/list.(*List).remove
   ```

   ```
   [roobot@172-21-173-36 go tool pprof heap-********110017
   File: service
   Type: inuse_space
   Time: Aug 4, 2021 at 11:00am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 144.26MB, 69.17% of 208.55MB total
   Dropped 153 nodes (cum <= 1.04MB)
   Showing top 20 nodes out of 132
         flat  flat%   sum%        cum   cum%
      28.57MB 13.70% 13.70%    46.57MB 22.33%  app/logic/character.(*HeroM).Add
      27.52MB 13.19% 26.89%    27.52MB 13.19%  app/logic/character.(*Handbook).Add
      14.50MB  6.95% 33.85%    14.50MB  6.95%  app/logic/character.(*User).TaskTypeOnEvent
         14MB  6.71% 40.56%       18MB  8.63%  app/logic/character.newHero (inline)
       8.01MB  3.84% 44.40%     8.01MB  3.84%  app/logic/character.(*HeroM).Delete
          7MB  3.36% 47.76%        7MB  3.36%  app/logic/helper/monitor.(*Cmds).Add
       5.50MB  2.64% 50.40%     5.50MB  2.64%  app/logic/character.(*Avatar).Add
       5.50MB  2.64% 53.04%     5.50MB  2.64%  container/list.(*List).insertValue (inline)
          4MB  1.92% 54.96%        4MB  1.92%  app/logic/character.initHeroFromData (inline)
       3.53MB  1.69% 56.65%     4.06MB  1.95%  compress/flate.NewWriter
       3.50MB  1.68% 58.33%     3.50MB  1.68%  github.com/google/btree.NewFreeList
       3.50MB  1.68% 60.00%        7MB  3.36%  app/logic/character.(*User).SendSelfToClient
       3.50MB  1.68% 61.68%       12MB  5.75%  app/logic/character.(*User).pushMsg
       2.50MB  1.20% 62.88%     3.68MB  1.76%  app/logic/db.(*RedisActor).Create
       2.50MB  1.20% 64.08%     2.50MB  1.20%  app/logic/character.(*User).loadFinished
       2.50MB  1.20% 65.28%     2.50MB  1.20%  app/logic/character.newGemM
       2.12MB  1.02% 66.29%     2.12MB  1.02%  app/logic/character.(*UserManager).addIndex
          2MB  0.96% 67.25%     7.50MB  3.60%  app/logic/character.(*User).initModule
          2MB  0.96% 68.21%        2MB  0.96%  app/goxml.(*MonsterInfoManager).Load
          2MB  0.96% 69.17%     2.50MB  1.20%  app/protos/out/cl.(*C2L_Formation).Unmarshal
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof  -inuse_objects heap-********110017
   File: service
   Type: inuse_objects
   Time: Aug 4, 2021 at 11:00am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 1849777, 67.23% of 2751299 total
   Dropped 170 nodes (cum <= 13756)
   Showing top 20 nodes out of 115
         flat  flat%   sum%        cum   cum%
       362244 13.17% 13.17%     362244 13.17%  app/logic/character.(*Handbook).Add
       245771  8.93% 22.10%     298203 10.84%  app/logic/character.newHero (inline)
       166194  6.04% 28.14%     166194  6.04%  app/logic/character.(*User).TaskTypeOnEvent
       131073  4.76% 32.90%     131073  4.76%  github.com/google/btree.NewWithFreeList
       120153  4.37% 37.27%     120153  4.37%  container/list.(*List).insertValue (inline)
       109226  3.97% 41.24%     273069  9.93%  app/logic/helper/sync.(*MessageSync).Push
        81921  2.98% 44.22%      81921  2.98%  app/logic/character.(*FormationM).autoFormations
        76461  2.78% 47.00%     349530 12.70%  app/logic/character.(*User).pushMsg
        76073  2.76% 49.76%      76073  2.76%  app/logic/character.(*Avatar).Add
        60076  2.18% 51.95%      60076  2.18%  app/logic/character.newGemM
        54615  1.99% 53.93%      54615  1.99%  app/logic/character.(*User).loadFinished
        52432  1.91% 55.84%      52432  1.91%  app/logic/character.initHeroFromData (inline)
        43692  1.59% 57.42%      65538  2.38%  app/logic/character.(*User).loadDBModule
        43692  1.59% 59.01%      43692  1.59%  app/logic/character.newHeroM
        43690  1.59% 60.60%      98305  3.57%  app/logic/helper/sync.(*MessageSync).append
        43690  1.59% 62.19%      43690  1.59%  app/protos/in/db.(*Bags).Clone
        39685  1.44% 63.63%     337888 12.28%  app/logic/character.(*HeroM).Add
        33551  1.22% 64.85%     131857  4.79%  app/logic/character.(*User).SendSelfToClient
        32769  1.19% 66.04%      32769  1.19%  app/logic/character.(*DailyTask).load
        32769  1.19% 67.23%      32769  1.19%  app/logic/character.NewEquipM
   ```

   ```
   [roobot@172-21-173-36 pprof]$ go tool pprof allocs-**************
   File: service
   Type: alloc_space
   Time: Aug 4, 2021 at 11:00am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 2909.59MB, 70.95% of 4100.86MB total
   Dropped 439 nodes (cum <= 20.50MB)
   Showing top 20 nodes out of 133
         flat  flat%   sum%        cum   cum%
     584.57MB 14.25% 14.25%   585.07MB 14.27%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
     302.64MB  7.38% 21.63%   303.14MB  7.39%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     280.02MB  6.83% 28.46%   441.53MB 10.77%  context.WithDeadline
     218.51MB  5.33% 33.79%   218.51MB  5.33%  container/list.(*List).insertValue (inline)
     180.53MB  4.40% 38.19%   199.53MB  4.87%  app/logic/character.(*User).mergeResources
     161.51MB  3.94% 42.13%   161.51MB  3.94%  time.AfterFunc
     144.52MB  3.52% 45.66%   186.52MB  4.55%  app/logic/character.(*User).TaskTypeOnEvent
     109.50MB  2.67% 48.33%   326.01MB  7.95%  app/logic/character.(*User).pushMsg
     104.51MB  2.55% 50.87%   104.51MB  2.55%  app/protos/out/cl.(*HeroBody).Clone
     102.51MB  2.50% 53.37%   133.51MB  3.26%  app/logic/character.newHero (inline)
      91.58MB  2.23% 55.61%   100.58MB  2.45%  encoding/xml.(*Decoder).rawToken
      87.57MB  2.14% 57.74%    88.07MB  2.15%  app/logic/character.JSONMarshal
      83.52MB  2.04% 59.78%   185.02MB  4.51%  app/logic/character.(*User).SendSelfToClient
      83.01MB  2.02% 61.80%   147.01MB  3.58%  app/logic/character.(*User).FireCommonEvent (inline)
      77.50MB  1.89% 63.69%    77.50MB  1.89%  app/protos/out/cl.(*TaskTypeProgress).Clone
      70.50MB  1.72% 65.41%    70.50MB  1.72%  app/logic/session.(*Client).Process
         70MB  1.71% 67.12%   396.01MB  9.66%  app/logic/character.(*User).SendCmdToGateway
      61.51MB  1.50% 68.62%   347.11MB  8.46%  app/logic/command/hero.(*C2LHeroDecomposeCommand).Execute
      53.08MB  1.29% 69.91%   237.09MB  5.78%  app/logic/character.(*HeroM).Add
      42.51MB  1.04% 70.95%   117.02MB  2.85%  app/logic/character.(*User).LogResourceDec
   ```

