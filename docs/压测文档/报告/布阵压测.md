## 布阵压测

1. 总结

2. robot

   ```shell
   [roobot@172-21-173-36 bin]$ ./robot_analyse.sh 
   2021-07-07 17:03:59 压测数据分析
   压测基础情况
   开始时间：2021/07/07 16:53:43, 结束时间:2021/07/07 17:03:37, 耗时:594(s)
   登录成功机器人总数：5000
   总消息数:2975005， 平均每秒消息数:5008
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:10100,                                            5000    5000            0.17%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.17%  100.00%
   协议:cmd:11029,MSG_C2L_Formation                        2574042 2970005           99.66%   86.67%
   协议正确数:2584042 协议总数:2980005 正确比:86.71%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
   协议:cmd:10100,                                         13.58%  18.74%  55.36%  12.18%   0.14%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.28%   2.78%   1.94%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11029,MSG_C2L_Formation                        98.13%   1.62%   0.25%   0.00%   0.00%   0.00%   0.00%   0.00% 
   
   ---------------------
   
   logic服数情况
   
   ```

3. logic

   ```shell
   [roobot@172-21-254-181 bin]$ ./logic_analyse.sh 
   
   --------------------------
   
   所有超时的left
   [2021/07/07 17:03:38.216588] gate(pck/s: 7502 sum: 3277578 left:494) logic db(pck/s: 0 sum: 10000 left:0) redis db(pck/s: 769 sum: 76373 left:1510) 
   
   --------------------------
   
   logic服务器启动时间:1.083371289s
   logic账号总数:1500
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   5)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:1(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11029(   5)                               60.00%  40.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11003(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:    event:57(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100004(   1)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:      100005(   1)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   ```

4. metric

   cpu:峰值：80%，稳定平均40%左右

   内存：峰值：500M

   每分钟gc次数峰值：8

   性能没问题，省略图

5. profile

   pprofie

   ```shell
   [roobot@172-21-173-36 bin]$ go tool pprof pprof/profile-************** 
   File: service
   Type: cpu
   Time: Jul 7, 2021 at 4:58pm (CST)
   Duration: 30s, Total samples = 6.01s (20.03%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top10
   Showing nodes accounting for 2360ms, 39.27% of 6010ms total
   Dropped 210 nodes (cum <= 30.05ms)
   Showing top 10 nodes out of 179
         flat  flat%   sum%        cum   cum%
        560ms  9.32%  9.32%      560ms  9.32%  runtime.futex
        310ms  5.16% 14.48%      310ms  5.16%  runtime.epollwait
        310ms  5.16% 19.63%      570ms  9.48%  syscall.Syscall
        260ms  4.33% 23.96%      480ms  7.99%  runtime.scanobject
        250ms  4.16% 28.12%      590ms  9.82%  runtime.mallocgc
        200ms  3.33% 31.45%     1930ms 32.11%  app/logic/command/user.(*C2LFormationCommand).Execute
        160ms  2.66% 34.11%      410ms  6.82%  runtime.selectgo
        110ms  1.83% 35.94%      120ms  2.00%  runtime.findObject
        100ms  1.66% 37.60%      150ms  2.50%  runtime.heapBitsSetType
        100ms  1.66% 39.27%      100ms  1.66%  runtime.lock
   
   ```

   heap

   ```shell
   [roobot@172-21-173-36 bin]$ go tool pprof pprof/heap-************** 
   File: service
   Type: inuse_space
   Time: Jul 7, 2021 at 4:58pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top10
   Showing nodes accounting for 114.53MB, 58.95% of 194.29MB total
   Dropped 110 nodes (cum <= 0.97MB)
   Showing top 10 nodes out of 152
         flat  flat%   sum%        cum   cum%
      34.51MB 17.76% 17.76%    34.51MB 17.76%  app/logic/character.(*Hero).calcTotalAttr
         18MB  9.26% 27.03%       18MB  9.26%  app/protos/out/cl.(*Formation).Unmarshal
       9.50MB  4.89% 31.92%     9.50MB  4.89%  app/logic/character.(*Handbook).Add
       9.50MB  4.89% 36.81%    15.50MB  7.98%  app/logic/character.newHero (inline)
          9MB  4.63% 41.44%       27MB 13.90%  app/protos/out/cl.(*C2L_Formation).Unmarshal
       8.51MB  4.38% 45.82%     8.51MB  4.38%  app/logic/helper/monitor.(*Cmds).Add
       7.50MB  3.86% 49.68%     9.50MB  4.89%  app/logic/character.(*User).initModule
       6.50MB  3.35% 53.03%       22MB 11.33%  app/logic/character.(*HeroM).Add
          6MB  3.09% 56.12%        6MB  3.09%  app/logic/character.initHeroFromData (inline)
       5.50MB  2.83% 58.95%    42.01MB 21.62%  app/logic/character.(*FormationM).NewFormation
   
   ```

   heap alloc objects

   ```shell
   [roobot@172-21-173-36 bin]$ go tool pprof -alloc_objects pprof/heap-************** 
   File: service
   Type: alloc_objects
   Time: Jul 7, 2021 at 4:58pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top10
   Showing nodes accounting for ********, 60.09% of ******** total
   Dropped 435 nodes (cum <= 299958)
   Showing top 10 nodes out of 101
         flat  flat%   sum%        cum   cum%
      6922552 11.54% 11.54%    6922552 11.54%  app/protos/out/cl.(*Formation).Unmarshal
      5406936  9.01% 20.55%    7294515 12.16%  context.WithDeadline
      4594243  7.66% 28.21%    4594243  7.66%  app/logic/session.(*Client).Process
      4270950  7.12% 35.33%    4270950  7.12%  container/list.(*List).insertValue (inline)
      3495385  5.83% 41.16%   ******** 41.84%  app/logic/command/user.(*C2LFormationCommand).Execute
      3211337  5.35% 46.51%    6914282 11.53%  app/logic/helper/sync.(*MessageSync).Push
      2981991  4.97% 51.48%    9896273 16.50%  app/logic/character.(*User).pushMsg
      1887579  3.15% 54.63%    1887579  3.15%  time.AfterFunc
      1671218  2.79% 57.41%    1672500  2.79%  app/logic/command/base.(*Command).OnAfter
      1605681  2.68% 60.09%   ******** 77.30%  app/logic/service.(*LogicService).Run
   
   ```

   allocs

   ```shell
   [roobot@172-21-173-36 bin]$ go tool pprof pprof/allocs-************** 
   File: service
   Type: alloc_space
   Time: Jul 7, 2021 at 4:58pm (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top10
   Showing nodes accounting for 2193.42MB, 58.10% of 3775.34MB total
   Dropped 410 nodes (cum <= 18.88MB)
   Showing top 10 nodes out of 126
         flat  flat%   sum%        cum   cum%
     405.50MB 10.74% 10.74%   407.50MB 10.79%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     363.34MB  9.62% 20.36%   364.84MB  9.66%  app/logic/db/redisop.ClClient.SetSomeFormationMCallSKs
     314.02MB  8.32% 28.68%   314.02MB  8.32%  app/protos/out/cl.(*Formation).Unmarshal
     227.52MB  6.03% 34.71%   371.53MB  9.84%  context.WithDeadline
     195.51MB  5.18% 39.89%   195.51MB  5.18%  container/list.(*List).insertValue (inline)
     170.51MB  4.52% 44.40%   170.51MB  4.52%  app/logic/session.(*Client).Process
     144.01MB  3.81% 48.22%   144.01MB  3.81%  time.AfterFunc
     133.51MB  3.54% 51.75%   447.53MB 11.85%  app/protos/out/cl.(*C2L_Formation).Unmarshal
     133.01MB  3.52% 55.28%  1150.11MB 30.46%  app/logic/command/user.(*C2LFormationCommand).Execute
     106.50MB  2.82% 58.10%   348.01MB  9.22%  app/logic/character.(*User).pushMsg
   
   ```

   

   

   ```
   
   ```

   