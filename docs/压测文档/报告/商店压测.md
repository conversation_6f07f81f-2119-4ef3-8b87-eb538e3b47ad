## 商店压测报告


#### 第一版

1. robot数据

   ```
   2021-08-10 10:35:31 压测数据分析
   压测基础情况
   开始时间：2021/08/10 10:30:22, 结束时间:2021/08/10 10:35:16, 耗时:294(s)
   登录成功机器人总数：5000
   总消息数:1465001， 平均每秒消息数:4982
   
   --------------------------
   
   协议正确率分布
   协议                                                      正确      总数      正确总占比  正确比   
   协议:cmd:10100,                                            5000    5000            0.34%  100.00%
   协议:cmd:11003,MSG_C2L_Flush                               5000    5000            0.34%  100.00%
   协议:cmd:11303,MSG_C2L_ShopBuy                           371613  725037           25.28%   51.25%
   协议:cmd:11301,MSG_C2L_ShopList                            5000    5000            0.34%  100.00%
   协议:cmd:11025,MSG_C2L_GM                                  5000    5000            0.34%  100.00%
   协议:cmd:11305,MSG_C2L_ShopRefresh                       525000  724964           35.71%   72.42%
   协议正确数:916613 协议总数:1470001 正确比:62.35%
   
   --------------------------
   
   协议延迟分布
        协议名称                                              0-10  10-20  20-50  50-100 100-200 200-500 500-1000 1000-100000 
   协议:cmd:10100,                                         26.06%  36.20%  35.26%   2.48%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11003,MSG_C2L_Flush                            95.70%   1.76%   2.54%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11303,MSG_C2L_ShopBuy                          98.09%   1.47%   0.44%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11301,MSG_C2L_ShopList                         97.08%   2.66%   0.26%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11025,MSG_C2L_GM                               66.52%  33.48%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:cmd:11305,MSG_C2L_ShopRefresh                      98.00%   1.52%   0.48%   0.00%   0.00%   0.00%   0.00%   0.00%
   ```
   
2. 游戏数据

   ```
   --------------------------
   
   所有超时的left
   
   --------------------------
   
   logic服务器启动时间:
   logic账号总数:
   
   --------------------------
   
   协议处理超时分布
        协议名称                                          0-10   10-20   20-50   50-100  100-200  200-500  500-10000 
   协议:      100006(   9)                                0.00%  100.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:     event:2(   2)                               100.00%   0.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11303(   4)                               25.00%  50.00%  25.00%   0.00%   0.00%   0.00%   0.00% 
   协议:       11305(   6)                               33.33%  66.67%   0.00%   0.00%   0.00%   0.00%   0.00% 
   协议:  event_1014(   2)                               50.00%  50.00%   0.00%   0.00%   0.00%   0.00%   0.00% 
   ```
   
3. metric
   ![](./pic/shop-********-1.png)

4. pprof

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof profile-************** 
   File: service
   Type: cpu
   Time: Aug 10, 2021 at 10:32am (CST)
   Duration: 30.03s, Total samples = 11.57s (38.53%)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 5.69s, 49.18% of 11.57s total
   Dropped 332 nodes (cum <= 0.06s)
   Showing top 20 nodes out of 240
         flat  flat%   sum%        cum   cum%
        1.14s  9.85%  9.85%      2.23s 19.27%  runtime.scanobject
        0.54s  4.67% 14.52%      0.57s  4.93%  runtime.findObject
        0.46s  3.98% 18.50%      0.48s  4.15%  syscall.Syscall
        0.41s  3.54% 22.04%      1.23s 10.63%  runtime.mallocgc
        0.36s  3.11% 25.15%      0.36s  3.11%  runtime.markBits.isMarked (inline)
        0.33s  2.85% 28.00%      0.33s  2.85%  runtime.futex
        0.26s  2.25% 30.25%      0.26s  2.25%  runtime.epollwait
        0.26s  2.25% 32.50%      0.30s  2.59%  runtime.heapBitsSetType
        0.23s  1.99% 34.49%      0.24s  2.07%  runtime.nanotime
        0.22s  1.90% 36.39%      0.24s  2.07%  runtime.mapaccess1_fast32
        0.21s  1.82% 38.20%      0.52s  4.49%  runtime.selectgo
        0.19s  1.64% 39.84%      0.30s  2.59%  app/logic/character.(*User).ResetDaily
        0.18s  1.56% 41.40%      0.24s  2.07%  runtime.mapaccess2_fast32
        0.18s  1.56% 42.96%      0.18s  1.56%  runtime.nextFreeFast (inline)
        0.16s  1.38% 44.34%      0.25s  2.16%  runtime.mapiternext
        0.12s  1.04% 45.38%      0.12s  1.04%  github.com/json-iterator/go.(*Stream).WriteString
        0.12s  1.04% 46.41%      0.13s  1.12%  runtime.step
        0.11s  0.95% 47.36%      0.91s  7.87%  github.com/json-iterator/go.(*structEncoder).Encode
        0.11s  0.95% 48.31%      0.11s  0.95%  runtime.(*itabTableType).find
        0.10s  0.86% 49.18%      0.28s  2.42%  runtime.mapiterinit
   ```

   ```shell
   [roobot@172-21-173-36 go tool pprof heap-************** 
   File: service
   Type: inuse_space
   Time: Aug 10, 2021 at 10:32am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 92.83MB, 57.42% of 161.67MB total
   Dropped 87 nodes (cum <= 0.81MB)
   Showing top 20 nodes out of 165
         flat  flat%   sum%        cum   cum%
         14MB  8.66%  8.66%       19MB 11.75%  app/logic/character.newHero (inline)
         10MB  6.19% 14.85%    10.50MB  6.50%  app/logic/character.(*User).TaskTypeOnEvent
       7.50MB  4.64% 19.49%     7.50MB  4.64%  app/logic/character.(*Handbook).Add
          7MB  4.33% 23.82%        7MB  4.33%  app/logic/character.newShop
       5.50MB  3.40% 27.22%     5.50MB  3.40%  app/logic/helper/monitor.(*Cmds).Add
          5MB  3.09% 30.31%        5MB  3.09%  app/logic/character.initHeroFromData (inline)
       4.50MB  2.78% 33.10%     4.50MB  2.78%  github.com/google/btree.NewFreeList (inline)
       4.50MB  2.78% 35.88%     4.50MB  2.78%  app/logic/character.(*Shop).updateOneGoods
       4.16MB  2.57% 38.45%     5.16MB  3.19%  app/logic/db.(*RedisActor).Create
       3.53MB  2.18% 40.63%     3.53MB  2.18%  compress/flate.NewWriter
       3.50MB  2.17% 42.80%    22.50MB 13.92%  app/logic/character.(*HeroM).Add
       3.50MB  2.16% 44.96%     3.50MB  2.16%  container/list.(*List).insertValue
          3MB  1.86% 46.82%     7.50MB  4.64%  app/logic/mail.NewBox
          3MB  1.86% 48.68%        3MB  1.86%  app/logic/character.(*Avatar).Add
       2.50MB  1.55% 50.22%     2.50MB  1.55%  app/goxml.(*MonsterInfoManager).Load
       2.50MB  1.55% 51.77%     2.50MB  1.55%  app/logic/character.generateGemAttr
       2.50MB  1.55% 53.32%     2.50MB  1.55%  app/logic/character.NewEquipM
       2.50MB  1.55% 54.86%     2.50MB  1.55%  app/protos/in/db.(*Bags).Clone
       2.13MB  1.32% 56.18%     2.13MB  1.32%  google.golang.org/protobuf/internal/strs.(*Builder).AppendFullName
          2MB  1.24% 57.42%        7MB  4.33%  app/logic/character.(*User).initModule
   ```

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof -inuse_objects heap-************** 
   File: service
   Type: inuse_objects
   Time: Aug 10, 2021 at 10:32am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 1511382, 60.03% of 2517563 total
   Dropped 127 nodes (cum <= 12587)
   Showing top 20 nodes out of 125
         flat  flat%   sum%        cum   cum%
       229388  9.11%  9.11%     294929 11.71%  app/logic/character.newHero
       152923  6.07% 15.19%     152923  6.07%  app/logic/character.newShop
       121717  4.83% 20.02%     129909  5.16%  app/logic/character.(*User).TaskTypeOnEvent
       110402  4.39% 24.41%     110402  4.39%  app/logic/character.(*Handbook).Add
       109226  4.34% 28.74%     109226  4.34%  app/protos/in/db.(*Bags).Clone
        76461  3.04% 31.78%      76461  3.04%  container/list.(*List).insertValue (inline)
        65541  2.60% 34.38%      65541  2.60%  app/logic/character.initHeroFromData (inline)
        65538  2.60% 36.99%      65538  2.60%  app/logic/character.generateGemAttr
        60076  2.39% 39.37%      60076  2.39%  app/logic/character.NewEquipM
        58986  2.34% 41.72%      58986  2.34%  app/logic/character.(*Shop).updateOneGoods
        54613  2.17% 43.89%     174764  6.94%  app/logic/helper/sync.(*MessageSync).Push
        50245  2.00% 45.88%      50245  2.00%  app/protos/out/cl.(*ShopInfo).Clone
        47108  1.87% 47.75%      47108  1.87%  github.com/google/btree.NewFreeList
        45643  1.81% 49.57%      45643  1.81%  app/logic/character.(*Avatar).Add
        45058  1.79% 51.36%      45058  1.79%  app/goxml.(*MonsterInfoManager).Load
        43692  1.74% 53.09%      43692  1.74%  app/logic/character.(*RankAchieveAward).load
        43692  1.74% 54.83%      43692  1.74%  app/logic/character.newArtifactM
        43692  1.74% 56.56%      43692  1.74%  app/logic/character.newMirageM
        43691  1.74% 58.30%      65539  2.60%  app/logic/character.glob..func8
        43690  1.74% 60.03%      76459  3.04%  app/logic/helper/sync.(*MessageSync).append
   ```

   ```shell
   [roobot@172-21-173-36 pprof]$ go tool pprof  allocs-************** 
   File: service
   Type: alloc_space
   Time: Aug 10, 2021 at 10:32am (CST)
   Entering interactive mode (type "help" for commands, "o" for options)
   (pprof) top20
   Showing nodes accounting for 2280.27MB, 68.01% of 3352.93MB total
   Dropped 399 nodes (cum <= 16.76MB)
   Showing top 20 nodes out of 137
         flat  flat%   sum%        cum   cum%
     399.50MB 11.92% 11.92%   407.50MB 12.15%  app/logic/db/redisop.DbClient.SetUserMCallSKs
     293.02MB  8.74% 20.65%   479.54MB 14.30%  context.WithDeadline
     192.51MB  5.74% 26.40%   192.51MB  5.74%  container/list.(*List).insertValue (inline)
     186.51MB  5.56% 31.96%   186.51MB  5.56%  time.AfterFunc
     122.50MB  3.65% 35.61%   330.51MB  9.86%  app/logic/character.(*User).pushMsg
     117.51MB  3.50% 39.12%   117.51MB  3.50%  app/protos/out/cl.(*ShopInfo).Clone
     106.01MB  3.16% 42.28%   106.01MB  3.16%  app/logic/character.(*User).mergeResources
      99.51MB  2.97% 45.25%   132.01MB  3.94%  app/logic/character.(*User).TaskTypeOnEvent
      95.52MB  2.85% 48.10%   195.52MB  5.83%  app/logic/character.(*User).SendSelfToClient
      89.52MB  2.67% 50.76%    89.52MB  2.67%  strings.(*Builder).WriteString
      80.50MB  2.40% 53.17%    80.50MB  2.40%  app/logic/session.(*Client).Process
      78.07MB  2.33% 55.49%    89.07MB  2.66%  encoding/xml.(*Decoder).rawToken
         62MB  1.85% 57.34%    95.51MB  2.85%  app/logic/character.(*User).FireCommonEvent (inline)
      60.51MB  1.80% 59.15%   150.51MB  4.49%  app/logic/character.(*ShopM).FlushShops
      59.50MB  1.77% 60.92%   390.01MB 11.63%  app/logic/character.(*User).SendCmdToGateway
      54.55MB  1.63% 62.55%    55.55MB  1.66%  app/logic/db/redisop.ClClient.SetSomeHeroBodyMCallSKs
         54MB  1.61% 64.16%   674.55MB 20.12%  app/logic/command/shop.(*C2LShopBuyCommand).Execute
      44.50MB  1.33% 65.49%    44.50MB  1.33%  app/logic/character.(*Shop).CalcCostsAndAwards
      42.50MB  1.27% 66.76%    42.50MB  1.27%  app/protos/out/cl.(*TaskTypeProgress).Clone
      42.01MB  1.25% 68.01%    84.61MB  2.52%  github.com/ivanabc/log4go.Logger.intLogf
   ```
