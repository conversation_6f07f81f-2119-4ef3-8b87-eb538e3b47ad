<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.30.1 (20201013.1554)
 -->
<!-- Title: adapter Pages: 1 -->
<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<script type="text/ecmascript"><![CDATA[
/**
 *  SVGPan library 1.2.2
 * ======================
 *
 * Given an unique existing element with id "viewport" (or when missing, the
 * first g-element), including the library into any SVG adds the following
 * capabilities:
 *
 *  - Mouse panning
 *  - Mouse zooming (using the wheel)
 *  - Object dragging
 *
 * You can configure the behaviour of the pan/zoom/drag with the variables
 * listed in the CONFIGURATION section of this file.
 *
 * Known issues:
 *
 *  - Zooming (while panning) on Safari has still some issues
 *
 * Releases:
 *
 * 1.2.2, Tue Aug 30 17:21:56 CEST 2011, Andrea Leofreddi
 *	- Fixed viewBox on root tag (#7)
 *	- Improved zoom speed (#2)
 *
 * 1.2.1, Mon Jul  4 00:33:18 CEST 2011, Andrea Leofreddi
 *	- Fixed a regression with mouse wheel (now working on Firefox 5)
 *	- Working with viewBox attribute (#4)
 *	- Added "use strict;" and fixed resulting warnings (#5)
 *	- Added configuration variables, dragging is disabled by default (#3)
 *
 * 1.2, Sat Mar 20 08:42:50 GMT 2010, Zeng Xiaohui
 *	Fixed a bug with browser mouse handler interaction
 *
 * 1.1, Wed Feb  3 17:39:33 GMT 2010, Zeng Xiaohui
 *	Updated the zoom code to support the mouse wheel on Safari/Chrome
 *
 * 1.0, Andrea Leofreddi
 *	First release
 *
 * This code is licensed under the following BSD license:
 *
 * Copyright 2009-2017 Andrea Leofreddi <<EMAIL>>. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are
 * permitted provided that the following conditions are met:
 *
 *    1. Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *    2. Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *    3. Neither the name of the copyright holder nor the names of its
 *       contributors may be used to endorse or promote products derived from
 *       this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY COPYRIGHT HOLDERS AND CONTRIBUTORS ''AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
 * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are those of the
 * authors and should not be interpreted as representing official policies, either expressed
 * or implied, of Andrea Leofreddi.
 */

"use strict";

/// CONFIGURATION
/// ====>

var enablePan = 1; // 1 or 0: enable or disable panning (default enabled)
var enableZoom = 1; // 1 or 0: enable or disable zooming (default enabled)
var enableDrag = 0; // 1 or 0: enable or disable dragging (default disabled)
var zoomScale = 0.2; // Zoom sensitivity

/// <====
/// END OF CONFIGURATION

var root = document.documentElement;

var state = 'none', svgRoot = null, stateTarget, stateOrigin, stateTf;

setupHandlers(root);

/**
 * Register handlers
 */
function setupHandlers(root){
	setAttributes(root, {
		"onmouseup" : "handleMouseUp(evt)",
		"onmousedown" : "handleMouseDown(evt)",
		"onmousemove" : "handleMouseMove(evt)",
		//"onmouseout" : "handleMouseUp(evt)", // Decomment this to stop the pan functionality when dragging out of the SVG element
	});

	if(navigator.userAgent.toLowerCase().indexOf('webkit') >= 0)
		window.addEventListener('mousewheel', handleMouseWheel, false); // Chrome/Safari
	else
		window.addEventListener('DOMMouseScroll', handleMouseWheel, false); // Others
}

/**
 * Retrieves the root element for SVG manipulation. The element is then cached into the svgRoot global variable.
 */
function getRoot(root) {
	if(svgRoot == null) {
		var r = root.getElementById("viewport") ? root.getElementById("viewport") : root.documentElement, t = r;

		while(t != root) {
			if(t.getAttribute("viewBox")) {
				setCTM(r, t.getCTM());

				t.removeAttribute("viewBox");
			}

			t = t.parentNode;
		}

		svgRoot = r;
	}

	return svgRoot;
}

/**
 * Instance an SVGPoint object with given event coordinates.
 */
function getEventPoint(evt) {
	var p = root.createSVGPoint();

	p.x = evt.clientX;
	p.y = evt.clientY;

	return p;
}

/**
 * Sets the current transform matrix of an element.
 */
function setCTM(element, matrix) {
	var s = "matrix(" + matrix.a + "," + matrix.b + "," + matrix.c + "," + matrix.d + "," + matrix.e + "," + matrix.f + ")";

	element.setAttribute("transform", s);
}

/**
 * Dumps a matrix to a string (useful for debug).
 */
function dumpMatrix(matrix) {
	var s = "[ " + matrix.a + ", " + matrix.c + ", " + matrix.e + "\n  " + matrix.b + ", " + matrix.d + ", " + matrix.f + "\n  0, 0, 1 ]";

	return s;
}

/**
 * Sets attributes of an element.
 */
function setAttributes(element, attributes){
	for (var i in attributes)
		element.setAttributeNS(null, i, attributes[i]);
}

/**
 * Handle mouse wheel event.
 */
function handleMouseWheel(evt) {
	if(!enableZoom)
		return;

	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	var delta;

	if(evt.wheelDelta)
		delta = evt.wheelDelta / 360; // Chrome/Safari
	else
		delta = evt.detail / -9; // Mozilla

	var z = Math.pow(1 + zoomScale, delta);

	var g = getRoot(svgDoc);
	
	var p = getEventPoint(evt);

	p = p.matrixTransform(g.getCTM().inverse());

	// Compute new scale matrix in current mouse position
	var k = root.createSVGMatrix().translate(p.x, p.y).scale(z).translate(-p.x, -p.y);

        setCTM(g, g.getCTM().multiply(k));

	if(typeof(stateTf) == "undefined")
		stateTf = g.getCTM().inverse();

	stateTf = stateTf.multiply(k.inverse());
}

/**
 * Handle mouse move event.
 */
function handleMouseMove(evt) {
	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	var g = getRoot(svgDoc);

	if(state == 'pan' && enablePan) {
		// Pan mode
		var p = getEventPoint(evt).matrixTransform(stateTf);

		setCTM(g, stateTf.inverse().translate(p.x - stateOrigin.x, p.y - stateOrigin.y));
	} else if(state == 'drag' && enableDrag) {
		// Drag mode
		var p = getEventPoint(evt).matrixTransform(g.getCTM().inverse());

		setCTM(stateTarget, root.createSVGMatrix().translate(p.x - stateOrigin.x, p.y - stateOrigin.y).multiply(g.getCTM().inverse()).multiply(stateTarget.getCTM()));

		stateOrigin = p;
	}
}

/**
 * Handle click event.
 */
function handleMouseDown(evt) {
	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	var g = getRoot(svgDoc);

	if(
		evt.target.tagName == "svg"
		|| !enableDrag // Pan anyway when drag is disabled and the user clicked on an element
	) {
		// Pan mode
		state = 'pan';

		stateTf = g.getCTM().inverse();

		stateOrigin = getEventPoint(evt).matrixTransform(stateTf);
	} else {
		// Drag mode
		state = 'drag';

		stateTarget = evt.target;

		stateTf = g.getCTM().inverse();

		stateOrigin = getEventPoint(evt).matrixTransform(stateTf);
	}
}

/**
 * Handle mouse button release event.
 */
function handleMouseUp(evt) {
	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	if(state == 'pan' || state == 'drag') {
		// Quit pan mode
		state = '';
	}
}
]]></script><g id="viewport" transform="scale(0.5,0.5) translate(0,0)"><g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 2772)">
<title>adapter</title>
<polygon fill="white" stroke="white" points="-4,5 -4,-2772 2237,-2772 2237,5 -4,5"/>
<g id="clust1" class="cluster"><title>cluster_L</title>
<polygon fill="none" stroke="black" points="771,-2536 771,-2760 1205,-2760 1205,-2536 771,-2536"/>
</g>
<!-- File: adapter -->
<g id="node1" class="node"><title>File: adapter</title>
<g id="a_node1"><a xlink:title="adapter">
<polygon fill="#f8f8f8" stroke="black" points="1197,-2751.5 779,-2751.5 779,-2544.5 1197,-2544.5 1197,-2751.5"/>
<text text-anchor="start" x="787" y="-2734.7" font-family="Times,serif" font-size="16.00">File: adapter</text>
<text text-anchor="start" x="787" y="-2716.7" font-family="Times,serif" font-size="16.00">Build ID: a92d37f073fca23940ca35b7759655dcb0c947f2</text>
<text text-anchor="start" x="787" y="-2698.7" font-family="Times,serif" font-size="16.00">Type: cpu</text>
<text text-anchor="start" x="787" y="-2680.7" font-family="Times,serif" font-size="16.00">Time: Nov 20, 2021 at 8:58pm (CST)</text>
<text text-anchor="start" x="787" y="-2662.7" font-family="Times,serif" font-size="16.00">Duration: 1mins, Total samples = 141.17s (234.53%)</text>
<text text-anchor="start" x="787" y="-2644.7" font-family="Times,serif" font-size="16.00">Showing nodes accounting for 99.21s, 70.28% of 141.17s total</text>
<text text-anchor="start" x="787" y="-2626.7" font-family="Times,serif" font-size="16.00">Dropped 544 nodes (cum &lt;= 0.71s)</text>
<text text-anchor="start" x="787" y="-2608.7" font-family="Times,serif" font-size="16.00">Dropped 143 edges (freq &lt;= 0.14s)</text>
<text text-anchor="start" x="787" y="-2590.7" font-family="Times,serif" font-size="16.00">Showing top 80 nodes out of 222</text>
<text text-anchor="start" x="787" y="-2553.7" font-family="Times,serif" font-size="16.00">See https://git.io/JfYMW for how to read the graph</text>
</a>
</g>
</g>
<!-- N1 -->
<g id="node1" class="node"><title>N1</title>
<g id="a_node1"><a xlink:title="runtime.mallocgc (15.25s)">
<polygon fill="#ede6e0" stroke="#b28152" points="1720,-556 1572,-556 1572,-472 1720,-472 1720,-556"/>
<text text-anchor="middle" x="1646" y="-538.4" font-family="Times,serif" font-size="17.00">runtime</text>
<text text-anchor="middle" x="1646" y="-519.4" font-family="Times,serif" font-size="17.00">mallocgc</text>
<text text-anchor="middle" x="1646" y="-500.4" font-family="Times,serif" font-size="17.00">7.19s (5.09%)</text>
<text text-anchor="middle" x="1646" y="-481.4" font-family="Times,serif" font-size="17.00">of 15.25s (10.80%)</text>
</a>
</g>
</g>
<!-- N7 -->
<g id="node7" class="node"><title>N7</title>
<g id="a_node7"><a xlink:title="runtime.systemstack (18.23s)">
<polygon fill="#ede4dd" stroke="#b2743f" points="530,-416 444,-416 444,-368 530,-368 530,-416"/>
<text text-anchor="middle" x="487" y="-404.8" font-family="Times,serif" font-size="9.00">runtime</text>
<text text-anchor="middle" x="487" y="-394.8" font-family="Times,serif" font-size="9.00">systemstack</text>
<text text-anchor="middle" x="487" y="-384.8" font-family="Times,serif" font-size="9.00">0.08s (0.057%)</text>
<text text-anchor="middle" x="487" y="-374.8" font-family="Times,serif" font-size="9.00">of 18.23s (12.91%)</text>
</a>
</g>
</g>
<!-- N1&#45;&gt;N7 -->
<g id="edge48" class="edge"><title>N1&#45;&gt;N7</title>
<g id="a_edge48"><a xlink:title="runtime.mallocgc ... runtime.systemstack (2.78s)">
<path fill="none" stroke="#b2ada0" stroke-dasharray="1,5" d="M1571.74,-505.311C1355.13,-482.884 727.557,-417.907 540.335,-398.522"/>
<polygon fill="#b2ada0" stroke="#b2ada0" points="540.502,-395.021 530.194,-397.472 539.781,-401.984 540.502,-395.021"/>
</a>
</g>
<g id="a_edge48&#45;label"><a xlink:title="runtime.mallocgc ... runtime.systemstack (2.78s)">
<text text-anchor="middle" x="1084" y="-442.3" font-family="Times,serif" font-size="14.00"> 2.78s</text>
</a>
</g>
</g>
<!-- N68 -->
<g id="node68" class="node"><title>N68</title>
<g id="a_node68"><a xlink:title="runtime.heapBitsSetType (2.16s)">
<polygon fill="#edeceb" stroke="#b2aea4" points="1699.25,-417 1592.75,-417 1592.75,-367 1699.25,-367 1699.25,-417"/>
<text text-anchor="middle" x="1646" y="-402.6" font-family="Times,serif" font-size="13.00">runtime</text>
<text text-anchor="middle" x="1646" y="-388.6" font-family="Times,serif" font-size="13.00">heapBitsSetType</text>
<text text-anchor="middle" x="1646" y="-374.6" font-family="Times,serif" font-size="13.00">2.16s (1.53%)</text>
</a>
</g>
</g>
<!-- N1&#45;&gt;N68 -->
<g id="edge58" class="edge"><title>N1&#45;&gt;N68</title>
<g id="a_edge58"><a xlink:title="runtime.mallocgc &#45;&gt; runtime.heapBitsSetType (2.16s)">
<path fill="none" stroke="#b2aea4" d="M1646,-471.703C1646,-457.224 1646,-441.131 1646,-427.289"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="1649.5,-427.207 1646,-417.207 1642.5,-427.207 1649.5,-427.207"/>
</a>
</g>
<g id="a_edge58&#45;label"><a xlink:title="runtime.mallocgc &#45;&gt; runtime.heapBitsSetType (2.16s)">
<text text-anchor="middle" x="1663" y="-442.3" font-family="Times,serif" font-size="14.00"> 2.16s</text>
</a>
</g>
</g>
<!-- N53 -->
<g id="node53" class="node"><title>N53</title>
<g id="a_node53"><a xlink:title="runtime.memclrNoHeapPointers (1.72s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="1524.25,-415.5 1393.75,-415.5 1393.75,-368.5 1524.25,-368.5 1524.25,-415.5"/>
<text text-anchor="middle" x="1459" y="-401.9" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="1459" y="-388.9" font-family="Times,serif" font-size="12.00">memclrNoHeapPointers</text>
<text text-anchor="middle" x="1459" y="-375.9" font-family="Times,serif" font-size="12.00">1.72s (1.22%)</text>
</a>
</g>
</g>
<!-- N1&#45;&gt;N53 -->
<g id="edge70" class="edge"><title>N1&#45;&gt;N53</title>
<g id="a_edge70"><a xlink:title="runtime.mallocgc ... runtime.memclrNoHeapPointers (1.30s)">
<path fill="none" stroke="#b2b0aa" stroke-dasharray="1,5" d="M1571.78,-486.297C1552.09,-477.52 1531.46,-466.696 1514,-454 1502.33,-445.513 1491.29,-434.203 1482.21,-423.629"/>
<polygon fill="#b2b0aa" stroke="#b2b0aa" points="1484.74,-421.205 1475.66,-415.745 1479.36,-425.675 1484.74,-421.205"/>
</a>
</g>
<g id="a_edge70&#45;label"><a xlink:title="runtime.mallocgc ... runtime.memclrNoHeapPointers (1.30s)">
<text text-anchor="middle" x="1531" y="-442.3" font-family="Times,serif" font-size="14.00"> 1.30s</text>
</a>
</g>
</g>
<!-- N2 -->
<g id="node2" class="node"><title>N2</title>
<g id="a_node2"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher (38.76s)">
<polygon fill="#edddd5" stroke="#b23900" points="1300,-2257 1214,-2257 1214,-2199 1300,-2199 1300,-2257"/>
<text text-anchor="middle" x="1257" y="-2245.8" font-family="Times,serif" font-size="9.00">helper</text>
<text text-anchor="middle" x="1257" y="-2235.8" font-family="Times,serif" font-size="9.00">(*MessageM)</text>
<text text-anchor="middle" x="1257" y="-2225.8" font-family="Times,serif" font-size="9.00">Dispatcher</text>
<text text-anchor="middle" x="1257" y="-2215.8" font-family="Times,serif" font-size="9.00">0.07s (0.05%)</text>
<text text-anchor="middle" x="1257" y="-2205.8" font-family="Times,serif" font-size="9.00">of 38.76s (27.46%)</text>
</a>
</g>
</g>
<!-- N72 -->
<g id="node72" class="node"><title>N72</title>
<g id="a_node72"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).Execute (18.76s)">
<polygon fill="#ede4dd" stroke="#b2713b" points="1299,-2131 1215,-2131 1215,-2087 1299,-2087 1299,-2131"/>
<text text-anchor="middle" x="1257" y="-2120.6" font-family="Times,serif" font-size="8.00">resource</text>
<text text-anchor="middle" x="1257" y="-2111.6" font-family="Times,serif" font-size="8.00">(*ResourceMessage)</text>
<text text-anchor="middle" x="1257" y="-2102.6" font-family="Times,serif" font-size="8.00">Execute</text>
<text text-anchor="middle" x="1257" y="-2093.6" font-family="Times,serif" font-size="8.00">0 of 18.76s (13.29%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N72 -->
<g id="edge12" class="edge"><title>N2&#45;&gt;N72</title>
<g id="a_edge12"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher &#45;&gt; adapter/service/message/resource.(*ResourceMessage).Execute (18.76s)">
<path fill="none" stroke="#b2713b" d="M1257,-2198.98C1257,-2181.65 1257,-2159.38 1257,-2141.45"/>
<polygon fill="#b2713b" stroke="#b2713b" points="1260.5,-2141.2 1257,-2131.2 1253.5,-2141.2 1260.5,-2141.2"/>
</a>
</g>
<g id="a_edge12&#45;label"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher &#45;&gt; adapter/service/message/resource.(*ResourceMessage).Execute (18.76s)">
<text text-anchor="middle" x="1277" y="-2160.3" font-family="Times,serif" font-size="14.00"> 18.76s</text>
</a>
</g>
</g>
<!-- N3 -->
<g id="node3" class="node"><title>N3</title>
<g id="a_node3"><a xlink:title="adapter/service/helper.(*MsgRecorder).Write (34.29s)">
<polygon fill="#edddd5" stroke="#b23d00" points="1376,-1918 1290,-1918 1290,-1860 1376,-1860 1376,-1918"/>
<text text-anchor="middle" x="1333" y="-1906.8" font-family="Times,serif" font-size="9.00">helper</text>
<text text-anchor="middle" x="1333" y="-1896.8" font-family="Times,serif" font-size="9.00">(*MsgRecorder)</text>
<text text-anchor="middle" x="1333" y="-1886.8" font-family="Times,serif" font-size="9.00">Write</text>
<text text-anchor="middle" x="1333" y="-1876.8" font-family="Times,serif" font-size="9.00">0.05s (0.035%)</text>
<text text-anchor="middle" x="1333" y="-1866.8" font-family="Times,serif" font-size="9.00">of 34.29s (24.29%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N3 -->
<g id="edge27" class="edge"><title>N2&#45;&gt;N3</title>
<g id="a_edge27"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... adapter/service/helper.(*MsgRecorder).Write (9.93s)">
<path fill="none" stroke="#b29673" stroke-dasharray="1,5" d="M1283.76,-2198.76C1290.25,-2190.63 1296.58,-2181.41 1301,-2172 1338.77,-2091.55 1339.2,-1984.47 1336.16,-1928.23"/>
<polygon fill="#b29673" stroke="#b29673" points="1339.64,-1927.77 1335.54,-1918.01 1332.65,-1928.2 1339.64,-1927.77"/>
</a>
</g>
<g id="a_edge27&#45;label"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... adapter/service/helper.(*MsgRecorder).Write (9.93s)">
<text text-anchor="middle" x="1351" y="-2050.3" font-family="Times,serif" font-size="14.00"> 9.93s</text>
</a>
</g>
</g>
<!-- N73 -->
<g id="node73" class="node"><title>N73</title>
<g id="a_node73"><a xlink:title="adapter/service/message/resource.(*CurrencyMessage).newCurrencyMessage (8.07s)">
<polygon fill="#edeae6" stroke="#b29c7f" points="1457,-2138 1361,-2138 1361,-2080 1457,-2080 1457,-2138"/>
<text text-anchor="middle" x="1409" y="-2126.8" font-family="Times,serif" font-size="9.00">resource</text>
<text text-anchor="middle" x="1409" y="-2116.8" font-family="Times,serif" font-size="9.00">(*CurrencyMessage)</text>
<text text-anchor="middle" x="1409" y="-2106.8" font-family="Times,serif" font-size="9.00">newCurrencyMessage</text>
<text text-anchor="middle" x="1409" y="-2096.8" font-family="Times,serif" font-size="9.00">0.02s (0.014%)</text>
<text text-anchor="middle" x="1409" y="-2086.8" font-family="Times,serif" font-size="9.00">of 8.07s (5.72%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N73 -->
<g id="edge29" class="edge"><title>N2&#45;&gt;N73</title>
<g id="a_edge29"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... adapter/service/message/resource.(*CurrencyMessage).newCurrencyMessage (8.07s)">
<path fill="none" stroke="#b29c7f" stroke-dasharray="1,5" d="M1300.05,-2203.51C1315.39,-2194.42 1332.47,-2183.44 1347,-2172 1357.19,-2163.98 1367.47,-2154.42 1376.65,-2145.25"/>
<polygon fill="#b29c7f" stroke="#b29c7f" points="1379.24,-2147.6 1383.75,-2138.01 1374.24,-2142.7 1379.24,-2147.6"/>
</a>
</g>
<g id="a_edge29&#45;label"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... adapter/service/message/resource.(*CurrencyMessage).newCurrencyMessage (8.07s)">
<text text-anchor="middle" x="1383" y="-2160.3" font-family="Times,serif" font-size="14.00"> 8.07s</text>
</a>
</g>
</g>
<!-- N42 -->
<g id="node42" class="node"><title>N42</title>
<g id="a_node42"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (7.53s)">
<polygon fill="#edeae6" stroke="#b29e82" points="1155.25,-2028 1078.75,-2028 1078.75,-1970 1155.25,-1970 1155.25,-2028"/>
<text text-anchor="middle" x="1117" y="-2016.8" font-family="Times,serif" font-size="9.00">go</text>
<text text-anchor="middle" x="1117" y="-2006.8" font-family="Times,serif" font-size="9.00">(*frozenConfig)</text>
<text text-anchor="middle" x="1117" y="-1996.8" font-family="Times,serif" font-size="9.00">Unmarshal</text>
<text text-anchor="middle" x="1117" y="-1986.8" font-family="Times,serif" font-size="9.00">0.08s (0.057%)</text>
<text text-anchor="middle" x="1117" y="-1976.8" font-family="Times,serif" font-size="9.00">of 7.53s (5.33%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N42 -->
<g id="edge92" class="edge"><title>N2&#45;&gt;N42</title>
<g id="a_edge92"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.59s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1224.76,-2198.98C1207.36,-2182.57 1186.55,-2160.65 1172,-2138 1161.03,-2120.91 1142.75,-2072.64 1130.3,-2038.01"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1133.45,-2036.43 1126.79,-2028.19 1126.86,-2038.79 1133.45,-2036.43"/>
</a>
</g>
<g id="a_edge92&#45;label"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.59s)">
<text text-anchor="middle" x="1189" y="-2105.3" font-family="Times,serif" font-size="14.00"> 0.59s</text>
</a>
</g>
</g>
<!-- N46 -->
<g id="node46" class="node"><title>N46</title>
<g id="a_node46"><a xlink:title="sync.(*Pool).Get (1.27s)">
<polygon fill="#edecec" stroke="#b2b0aa" points="1299,-840.5 1221,-840.5 1221,-777.5 1299,-777.5 1299,-840.5"/>
<text text-anchor="middle" x="1260" y="-828.5" font-family="Times,serif" font-size="10.00">sync</text>
<text text-anchor="middle" x="1260" y="-817.5" font-family="Times,serif" font-size="10.00">(*Pool)</text>
<text text-anchor="middle" x="1260" y="-806.5" font-family="Times,serif" font-size="10.00">Get</text>
<text text-anchor="middle" x="1260" y="-795.5" font-family="Times,serif" font-size="10.00">0.29s (0.21%)</text>
<text text-anchor="middle" x="1260" y="-784.5" font-family="Times,serif" font-size="10.00">of 1.27s (0.9%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N46 -->
<g id="edge110" class="edge"><title>N2&#45;&gt;N46</title>
<g id="a_edge110"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... sync.(*Pool).Get (0.33s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1213.79,-2214.38C1189.56,-2205.59 1160.06,-2191.84 1139,-2172 1058.08,-2095.77 1056.93,-2056.85 1020,-1952 916.999,-1659.58 922.893,-1569.9 932,-1260 933.742,-1200.71 936.664,-1178.4 976,-1134 1005.37,-1100.85 1028.07,-1111.61 1061,-1082 1095.17,-1051.27 1101.73,-1040.2 1124,-1000 1147.44,-957.682 1143.24,-942.592 1162,-898 1168.48,-882.606 1167.06,-876.62 1178,-864 1179.55,-862.215 1195.13,-851.85 1212.31,-840.67"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1214.33,-843.53 1220.81,-835.15 1210.52,-837.659 1214.33,-843.53"/>
</a>
</g>
<g id="a_edge110&#45;label"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... sync.(*Pool).Get (0.33s)">
<text text-anchor="middle" x="948" y="-1543.3" font-family="Times,serif" font-size="14.00"> 0.33s</text>
</a>
</g>
</g>
<!-- N26 -->
<g id="node26" class="node"><title>N26</title>
<g id="a_node26"><a xlink:title="runtime.mapassign_faststr (3.78s)">
<polygon fill="#edece9" stroke="#b2aa9a" points="1811,-1466 1711,-1466 1711,-1406 1811,-1406 1811,-1466"/>
<text text-anchor="middle" x="1761" y="-1452.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="1761" y="-1439.4" font-family="Times,serif" font-size="12.00">mapassign_faststr</text>
<text text-anchor="middle" x="1761" y="-1426.4" font-family="Times,serif" font-size="12.00">1.30s (0.92%)</text>
<text text-anchor="middle" x="1761" y="-1413.4" font-family="Times,serif" font-size="12.00">of 3.78s (2.68%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N26 -->
<g id="edge129" class="edge"><title>N2&#45;&gt;N26</title>
<g id="a_edge129"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... runtime.mapassign_faststr (0.20s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1300.46,-2227.63C1436.94,-2228.45 1848,-2221.66 1848,-2110 1848,-2110 1848,-2110 1848,-1546 1848,-1517.32 1829.37,-1491.84 1809.36,-1472.75"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1811.7,-1470.15 1801.95,-1466.01 1806.99,-1475.33 1811.7,-1470.15"/>
</a>
</g>
<g id="a_edge129&#45;label"><a xlink:title="adapter/service/helper.(*MessageM).Dispatcher ... runtime.mapassign_faststr (0.20s)">
<text text-anchor="middle" x="1865" y="-1830.3" font-family="Times,serif" font-size="14.00"> 0.20s</text>
</a>
</g>
</g>
<!-- N14 -->
<g id="node14" class="node"><title>N14</title>
<g id="a_node14"><a xlink:title="adapter/service/logger.(*Logger).OnWrite (32.80s)">
<polygon fill="#edddd5" stroke="#b23f00" points="1376,-1805 1290,-1805 1290,-1747 1376,-1747 1376,-1805"/>
<text text-anchor="middle" x="1333" y="-1793.8" font-family="Times,serif" font-size="9.00">logger</text>
<text text-anchor="middle" x="1333" y="-1783.8" font-family="Times,serif" font-size="9.00">(*Logger)</text>
<text text-anchor="middle" x="1333" y="-1773.8" font-family="Times,serif" font-size="9.00">OnWrite</text>
<text text-anchor="middle" x="1333" y="-1763.8" font-family="Times,serif" font-size="9.00">0.06s (0.043%)</text>
<text text-anchor="middle" x="1333" y="-1753.8" font-family="Times,serif" font-size="9.00">of 32.80s (23.23%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N14 -->
<g id="edge4" class="edge"><title>N3&#45;&gt;N14</title>
<g id="a_edge4"><a xlink:title="adapter/service/helper.(*MsgRecorder).Write &#45;&gt; adapter/service/logger.(*Logger).OnWrite (32.80s)">
<path fill="none" stroke="#b23f00" stroke-width="2" d="M1333,-1859.99C1333,-1846.45 1333,-1829.99 1333,-1815.31"/>
<polygon fill="#b23f00" stroke="#b23f00" points="1336.5,-1815 1333,-1805 1329.5,-1815 1336.5,-1815"/>
</a>
</g>
<g id="a_edge4&#45;label"><a xlink:title="adapter/service/helper.(*MsgRecorder).Write &#45;&gt; adapter/service/logger.(*Logger).OnWrite (32.80s)">
<text text-anchor="middle" x="1353" y="-1830.3" font-family="Times,serif" font-size="14.00"> 32.80s</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N26 -->
<g id="edge118" class="edge"><title>N3&#45;&gt;N26</title>
<g id="a_edge118"><a xlink:title="adapter/service/helper.(*MsgRecorder).Write &#45;&gt; runtime.mapassign_faststr (0.24s)">
<path fill="none" stroke="#b2b2b0" d="M1376.3,-1876.5C1416.35,-1864.31 1476.02,-1842.05 1519,-1808 1635.82,-1715.45 1714.45,-1550.41 1745.77,-1475.67"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1749.12,-1476.75 1749.7,-1466.17 1742.65,-1474.07 1749.12,-1476.75"/>
</a>
</g>
<g id="a_edge118&#45;label"><a xlink:title="adapter/service/helper.(*MsgRecorder).Write &#45;&gt; runtime.mapassign_faststr (0.24s)">
<text text-anchor="middle" x="1685" y="-1656.3" font-family="Times,serif" font-size="14.00"> 0.24s</text>
</a>
</g>
</g>
<!-- N4 -->
<g id="node4" class="node"><title>N4</title>
<g id="a_node4"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).init.func1 (29.67s)">
<polygon fill="#edded5" stroke="#b24200" points="1765,-1208 1671,-1208 1671,-1134 1765,-1134 1765,-1208"/>
<text text-anchor="middle" x="1718" y="-1196" font-family="Times,serif" font-size="10.00">thinkingdata</text>
<text text-anchor="middle" x="1718" y="-1185" font-family="Times,serif" font-size="10.00">(*LogConsumer)</text>
<text text-anchor="middle" x="1718" y="-1174" font-family="Times,serif" font-size="10.00">init</text>
<text text-anchor="middle" x="1718" y="-1163" font-family="Times,serif" font-size="10.00">func1</text>
<text text-anchor="middle" x="1718" y="-1152" font-family="Times,serif" font-size="10.00">0.25s (0.18%)</text>
<text text-anchor="middle" x="1718" y="-1141" font-family="Times,serif" font-size="10.00">of 29.67s (21.02%)</text>
</a>
</g>
</g>
<!-- N54 -->
<g id="node54" class="node"><title>N54</title>
<g id="a_node54"><a xlink:title="fmt.Fprintln (23.03s)">
<polygon fill="#ede1d9" stroke="#b25d20" points="1761,-1074 1675,-1074 1675,-1026 1761,-1026 1761,-1074"/>
<text text-anchor="middle" x="1718" y="-1062.8" font-family="Times,serif" font-size="9.00">fmt</text>
<text text-anchor="middle" x="1718" y="-1052.8" font-family="Times,serif" font-size="9.00">Fprintln</text>
<text text-anchor="middle" x="1718" y="-1042.8" font-family="Times,serif" font-size="9.00">0.07s (0.05%)</text>
<text text-anchor="middle" x="1718" y="-1032.8" font-family="Times,serif" font-size="9.00">of 23.03s (16.31%)</text>
</a>
</g>
</g>
<!-- N4&#45;&gt;N54 -->
<g id="edge7" class="edge"><title>N4&#45;&gt;N54</title>
<g id="a_edge7"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).init.func1 &#45;&gt; fmt.Fprintln (23.03s)">
<path fill="none" stroke="#b25d20" d="M1718,-1133.97C1718,-1118.19 1718,-1099.78 1718,-1084.32"/>
<polygon fill="#b25d20" stroke="#b25d20" points="1721.5,-1084.15 1718,-1074.15 1714.5,-1084.15 1721.5,-1084.15"/>
</a>
</g>
<g id="a_edge7&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).init.func1 &#45;&gt; fmt.Fprintln (23.03s)">
<text text-anchor="middle" x="1738" y="-1104.3" font-family="Times,serif" font-size="14.00"> 23.03s</text>
</a>
</g>
</g>
<!-- N41 -->
<g id="node41" class="node"><title>N41</title>
<g id="a_node41"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName (5.35s)">
<polygon fill="#edebe8" stroke="#b2a590" points="1932.25,-1079 1849.75,-1079 1849.75,-1021 1932.25,-1021 1932.25,-1079"/>
<text text-anchor="middle" x="1891" y="-1067.8" font-family="Times,serif" font-size="9.00">thinkingdata</text>
<text text-anchor="middle" x="1891" y="-1057.8" font-family="Times,serif" font-size="9.00">(*LogConsumer)</text>
<text text-anchor="middle" x="1891" y="-1047.8" font-family="Times,serif" font-size="9.00">constructFileName</text>
<text text-anchor="middle" x="1891" y="-1037.8" font-family="Times,serif" font-size="9.00">0.09s (0.064%)</text>
<text text-anchor="middle" x="1891" y="-1027.8" font-family="Times,serif" font-size="9.00">of 5.35s (3.79%)</text>
</a>
</g>
</g>
<!-- N4&#45;&gt;N41 -->
<g id="edge38" class="edge"><title>N4&#45;&gt;N41</title>
<g id="a_edge38"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).init.func1 &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName (5.35s)">
<path fill="none" stroke="#b2a590" d="M1765.24,-1144.47C1780.18,-1135.9 1796.56,-1125.97 1811,-1116 1824.41,-1106.74 1838.4,-1095.85 1850.86,-1085.65"/>
<polygon fill="#b2a590" stroke="#b2a590" points="1853.34,-1088.15 1858.81,-1079.08 1848.88,-1082.75 1853.34,-1088.15"/>
</a>
</g>
<g id="a_edge38&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).init.func1 &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName (5.35s)">
<text text-anchor="middle" x="1849" y="-1104.3" font-family="Times,serif" font-size="14.00"> 5.35s</text>
</a>
</g>
</g>
<!-- N50 -->
<g id="node50" class="node"><title>N50</title>
<g id="a_node50"><a xlink:title="runtime.convTstring (1.92s)">
<polygon fill="#edeceb" stroke="#b2afa6" points="1888.25,-958 1805.75,-958 1805.75,-906 1888.25,-906 1888.25,-958"/>
<text text-anchor="middle" x="1847" y="-946" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="1847" y="-935" font-family="Times,serif" font-size="10.00">convTstring</text>
<text text-anchor="middle" x="1847" y="-924" font-family="Times,serif" font-size="10.00">0.11s (0.078%)</text>
<text text-anchor="middle" x="1847" y="-913" font-family="Times,serif" font-size="10.00">of 1.92s (1.36%)</text>
</a>
</g>
</g>
<!-- N4&#45;&gt;N50 -->
<g id="edge119" class="edge"><title>N4&#45;&gt;N50</title>
<g id="a_edge119"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).init.func1 &#45;&gt; runtime.convTstring (0.24s)">
<path fill="none" stroke="#b2b2b0" d="M1749.64,-1133.77C1754.04,-1128.01 1758.32,-1121.97 1762,-1116 1787.07,-1075.37 1772.76,-1052.28 1806,-1018 1818.73,-1004.87 1832.84,-1015.21 1843,-1000 1849.13,-990.821 1851.27,-979.261 1851.54,-968.339"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1855.03,-968.072 1851.28,-958.162 1848.03,-968.246 1855.03,-968.072"/>
</a>
</g>
<g id="a_edge119&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).init.func1 &#45;&gt; runtime.convTstring (0.24s)">
<text text-anchor="middle" x="1823" y="-1046.3" font-family="Times,serif" font-size="14.00"> 0.24s</text>
</a>
</g>
</g>
<!-- N5 -->
<g id="node5" class="node"><title>N5</title>
<g id="a_node5"><a xlink:title="adapter/service/kafka_consumer.(*Manager).Run.func1 (56.66s)">
<polygon fill="#eddbd5" stroke="#b22a00" points="1299,-2674.5 1215,-2674.5 1215,-2621.5 1299,-2621.5 1299,-2674.5"/>
<text text-anchor="middle" x="1257" y="-2664.1" font-family="Times,serif" font-size="8.00">kafka_consumer</text>
<text text-anchor="middle" x="1257" y="-2655.1" font-family="Times,serif" font-size="8.00">(*Manager)</text>
<text text-anchor="middle" x="1257" y="-2646.1" font-family="Times,serif" font-size="8.00">Run</text>
<text text-anchor="middle" x="1257" y="-2637.1" font-family="Times,serif" font-size="8.00">func1</text>
<text text-anchor="middle" x="1257" y="-2628.1" font-family="Times,serif" font-size="8.00">0 of 56.66s (40.14%)</text>
</a>
</g>
</g>
<!-- N10 -->
<g id="node10" class="node"><title>N10</title>
<g id="a_node10"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process (56.66s)">
<polygon fill="#eddbd5" stroke="#b22a00" points="1309,-2491.5 1205,-2491.5 1205,-2428.5 1309,-2428.5 1309,-2491.5"/>
<text text-anchor="middle" x="1257" y="-2479.5" font-family="Times,serif" font-size="10.00">kafka_consumer</text>
<text text-anchor="middle" x="1257" y="-2468.5" font-family="Times,serif" font-size="10.00">(*PartitionConsumer)</text>
<text text-anchor="middle" x="1257" y="-2457.5" font-family="Times,serif" font-size="10.00">Process</text>
<text text-anchor="middle" x="1257" y="-2446.5" font-family="Times,serif" font-size="10.00">0.14s (0.099%)</text>
<text text-anchor="middle" x="1257" y="-2435.5" font-family="Times,serif" font-size="10.00">of 56.66s (40.14%)</text>
</a>
</g>
</g>
<!-- N5&#45;&gt;N10 -->
<g id="edge1" class="edge"><title>N5&#45;&gt;N10</title>
<g id="a_edge1"><a xlink:title="adapter/service/kafka_consumer.(*Manager).Run.func1 &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).Process (56.66s)">
<path fill="none" stroke="#b22a00" stroke-width="3" d="M1257,-2621.14C1257,-2590.25 1257,-2538.2 1257,-2501.85"/>
<polygon fill="#b22a00" stroke="#b22a00" points="1260.5,-2501.58 1257,-2491.58 1253.5,-2501.58 1260.5,-2501.58"/>
</a>
</g>
<g id="a_edge1&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*Manager).Run.func1 &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).Process (56.66s)">
<text text-anchor="middle" x="1277" y="-2514.3" font-family="Times,serif" font-size="14.00"> 56.66s</text>
</a>
</g>
</g>
<!-- N6 -->
<g id="node6" class="node"><title>N6</title>
<g id="a_node6"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add (28.70s)">
<polygon fill="#edded5" stroke="#b24300" points="1383,-1576 1297,-1576 1297,-1518 1383,-1518 1383,-1576"/>
<text text-anchor="middle" x="1340" y="-1564.8" font-family="Times,serif" font-size="9.00">thinkingdata</text>
<text text-anchor="middle" x="1340" y="-1554.8" font-family="Times,serif" font-size="9.00">(*TDAnalytics)</text>
<text text-anchor="middle" x="1340" y="-1544.8" font-family="Times,serif" font-size="9.00">add</text>
<text text-anchor="middle" x="1340" y="-1534.8" font-family="Times,serif" font-size="9.00">0.08s (0.057%)</text>
<text text-anchor="middle" x="1340" y="-1524.8" font-family="Times,serif" font-size="9.00">of 28.70s (20.33%)</text>
</a>
</g>
</g>
<!-- N31 -->
<g id="node31" class="node"><title>N31</title>
<g id="a_node31"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).Add (15.85s)">
<polygon fill="#ede6df" stroke="#b27e4e" points="1383,-1465 1297,-1465 1297,-1407 1383,-1407 1383,-1465"/>
<text text-anchor="middle" x="1340" y="-1453.8" font-family="Times,serif" font-size="9.00">thinkingdata</text>
<text text-anchor="middle" x="1340" y="-1443.8" font-family="Times,serif" font-size="9.00">(*LogConsumer)</text>
<text text-anchor="middle" x="1340" y="-1433.8" font-family="Times,serif" font-size="9.00">Add</text>
<text text-anchor="middle" x="1340" y="-1423.8" font-family="Times,serif" font-size="9.00">0.08s (0.057%)</text>
<text text-anchor="middle" x="1340" y="-1413.8" font-family="Times,serif" font-size="9.00">of 15.85s (11.23%)</text>
</a>
</g>
</g>
<!-- N6&#45;&gt;N31 -->
<g id="edge16" class="edge"><title>N6&#45;&gt;N31</title>
<g id="a_edge16"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).Add (15.85s)">
<path fill="none" stroke="#b27e4e" d="M1340,-1517.93C1340,-1505.06 1340,-1489.6 1340,-1475.65"/>
<polygon fill="#b27e4e" stroke="#b27e4e" points="1343.5,-1475.35 1340,-1465.35 1336.5,-1475.35 1343.5,-1475.35"/>
</a>
</g>
<g id="a_edge16&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).Add (15.85s)">
<text text-anchor="middle" x="1360" y="-1488.3" font-family="Times,serif" font-size="14.00"> 15.85s</text>
</a>
</g>
</g>
<!-- N25 -->
<g id="node25" class="node"><title>N25</title>
<g id="a_node25"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.formatProperties (11.35s)">
<polygon fill="#ede8e3" stroke="#b2906a" points="1240.25,-1464 1145.75,-1464 1145.75,-1408 1240.25,-1408 1240.25,-1464"/>
<text text-anchor="middle" x="1193" y="-1451.2" font-family="Times,serif" font-size="11.00">thinkingdata</text>
<text text-anchor="middle" x="1193" y="-1439.2" font-family="Times,serif" font-size="11.00">formatProperties</text>
<text text-anchor="middle" x="1193" y="-1427.2" font-family="Times,serif" font-size="11.00">0.49s (0.35%)</text>
<text text-anchor="middle" x="1193" y="-1415.2" font-family="Times,serif" font-size="11.00">of 11.35s (8.04%)</text>
</a>
</g>
</g>
<!-- N6&#45;&gt;N25 -->
<g id="edge22" class="edge"><title>N6&#45;&gt;N25</title>
<g id="a_edge22"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.formatProperties (11.35s)">
<path fill="none" stroke="#b2906a" d="M1302.14,-1517.93C1282.48,-1503.35 1258.33,-1485.44 1237.74,-1470.18"/>
<polygon fill="#b2906a" stroke="#b2906a" points="1239.58,-1467.18 1229.46,-1464.03 1235.41,-1472.8 1239.58,-1467.18"/>
</a>
</g>
<g id="a_edge22&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.formatProperties (11.35s)">
<text text-anchor="middle" x="1296" y="-1488.3" font-family="Times,serif" font-size="14.00"> 11.35s</text>
</a>
</g>
</g>
<!-- N37 -->
<g id="node37" class="node"><title>N37</title>
<g id="a_node37"><a xlink:title="time.Time.Format (4.06s)">
<polygon fill="#edebe9" stroke="#b2a998" points="2161.25,-961 2084.75,-961 2084.75,-903 2161.25,-903 2161.25,-961"/>
<text text-anchor="middle" x="2123" y="-949.8" font-family="Times,serif" font-size="9.00">time</text>
<text text-anchor="middle" x="2123" y="-939.8" font-family="Times,serif" font-size="9.00">Time</text>
<text text-anchor="middle" x="2123" y="-929.8" font-family="Times,serif" font-size="9.00">Format</text>
<text text-anchor="middle" x="2123" y="-919.8" font-family="Times,serif" font-size="9.00">0.04s (0.028%)</text>
<text text-anchor="middle" x="2123" y="-909.8" font-family="Times,serif" font-size="9.00">of 4.06s (2.88%)</text>
</a>
</g>
</g>
<!-- N6&#45;&gt;N37 -->
<g id="edge78" class="edge"><title>N6&#45;&gt;N37</title>
<g id="a_edge78"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add ... time.Time.Format (0.85s)">
<path fill="none" stroke="#b2b1ad" stroke-dasharray="1,5" d="M1383.22,-1528.37C1506.89,-1477.3 1859.1,-1327.52 1949,-1242 2032.41,-1162.65 2086.81,-1034.35 2110.07,-970.952"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="2113.43,-971.946 2113.54,-961.352 2106.85,-969.569 2113.43,-971.946"/>
</a>
</g>
<g id="a_edge78&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add ... time.Time.Format (0.85s)">
<text text-anchor="middle" x="1981" y="-1230.3" font-family="Times,serif" font-size="14.00"> 0.85s</text>
</a>
</g>
</g>
<!-- N51 -->
<g id="node51" class="node"><title>N51</title>
<g id="a_node51"><a xlink:title="runtime.gcDrain (10.39s)">
<polygon fill="#ede9e4" stroke="#b29470" points="531.25,-308 442.75,-308 442.75,-256 531.25,-256 531.25,-308"/>
<text text-anchor="middle" x="487" y="-296" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="487" y="-285" font-family="Times,serif" font-size="10.00">gcDrain</text>
<text text-anchor="middle" x="487" y="-274" font-family="Times,serif" font-size="10.00">0.34s (0.24%)</text>
<text text-anchor="middle" x="487" y="-263" font-family="Times,serif" font-size="10.00">of 10.39s (7.36%)</text>
</a>
</g>
</g>
<!-- N7&#45;&gt;N51 -->
<g id="edge25" class="edge"><title>N7&#45;&gt;N51</title>
<g id="a_edge25"><a xlink:title="runtime.systemstack ... runtime.gcDrain (10.39s)">
<path fill="none" stroke="#b29470" stroke-dasharray="1,5" d="M487,-367.831C487,-353.392 487,-334.529 487,-318.258"/>
<polygon fill="#b29470" stroke="#b29470" points="490.5,-318.013 487,-308.013 483.5,-318.013 490.5,-318.013"/>
</a>
</g>
<g id="a_edge25&#45;label"><a xlink:title="runtime.systemstack ... runtime.gcDrain (10.39s)">
<text text-anchor="middle" x="507" y="-334.3" font-family="Times,serif" font-size="14.00"> 10.39s</text>
</a>
</g>
</g>
<!-- N44 -->
<g id="node44" class="node"><title>N44</title>
<g id="a_node44"><a xlink:title="runtime.lock2 (1.69s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="272,-310 182,-310 182,-254 272,-254 272,-310"/>
<text text-anchor="middle" x="227" y="-297.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="227" y="-285.2" font-family="Times,serif" font-size="11.00">lock2</text>
<text text-anchor="middle" x="227" y="-273.2" font-family="Times,serif" font-size="11.00">0.91s (0.64%)</text>
<text text-anchor="middle" x="227" y="-261.2" font-family="Times,serif" font-size="11.00">of 1.69s (1.20%)</text>
</a>
</g>
</g>
<!-- N7&#45;&gt;N44 -->
<g id="edge125" class="edge"><title>N7&#45;&gt;N44</title>
<g id="a_edge125"><a xlink:title="runtime.systemstack ... runtime.lock2 (0.23s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M443.79,-378.914C416.031,-370.686 379.419,-358.965 348,-346 317.132,-333.263 310.6,-327.456 281,-312 280.909,-311.952 280.817,-311.905 280.726,-311.857"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="282.519,-308.846 272.04,-307.289 279.261,-315.041 282.519,-308.846"/>
</a>
</g>
<g id="a_edge125&#45;label"><a xlink:title="runtime.systemstack ... runtime.lock2 (0.23s)">
<text text-anchor="middle" x="365" y="-334.3" font-family="Times,serif" font-size="14.00"> 0.23s</text>
</a>
</g>
</g>
<!-- N69 -->
<g id="node69" class="node"><title>N69</title>
<g id="a_node69"><a xlink:title="runtime.casgstatus (1.09s)">
<polygon fill="#edecec" stroke="#b2b0ab" points="386,-312 290,-312 290,-252 386,-252 386,-312"/>
<text text-anchor="middle" x="338" y="-298.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="338" y="-285.4" font-family="Times,serif" font-size="12.00">casgstatus</text>
<text text-anchor="middle" x="338" y="-272.4" font-family="Times,serif" font-size="12.00">1.02s (0.72%)</text>
<text text-anchor="middle" x="338" y="-259.4" font-family="Times,serif" font-size="12.00">of 1.09s (0.77%)</text>
</a>
</g>
</g>
<!-- N7&#45;&gt;N69 -->
<g id="edge132" class="edge"><title>N7&#45;&gt;N69</title>
<g id="a_edge132"><a xlink:title="runtime.systemstack ... runtime.casgstatus (0.20s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M443.618,-369.84C430.773,-362.882 416.956,-354.685 405,-346 394.089,-338.073 383.083,-328.519 373.234,-319.273"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="375.592,-316.685 365.949,-312.296 370.75,-321.74 375.592,-316.685"/>
</a>
</g>
<g id="a_edge132&#45;label"><a xlink:title="runtime.systemstack ... runtime.casgstatus (0.20s)">
<text text-anchor="middle" x="422" y="-334.3" font-family="Times,serif" font-size="14.00"> 0.20s</text>
</a>
</g>
</g>
<!-- N8 -->
<g id="node8" class="node"><title>N8</title>
<g id="a_node8"><a xlink:title="syscall.Syscall (28.69s)">
<polygon fill="#edded5" stroke="#b24300" points="759.25,-720 556.75,-720 556.75,-608 759.25,-608 759.25,-720"/>
<text text-anchor="middle" x="658" y="-696.8" font-family="Times,serif" font-size="24.00">syscall</text>
<text text-anchor="middle" x="658" y="-670.8" font-family="Times,serif" font-size="24.00">Syscall</text>
<text text-anchor="middle" x="658" y="-644.8" font-family="Times,serif" font-size="24.00">27.70s (19.62%)</text>
<text text-anchor="middle" x="658" y="-618.8" font-family="Times,serif" font-size="24.00">of 28.69s (20.32%)</text>
</a>
</g>
</g>
<!-- N8&#45;&gt;N69 -->
<g id="edge120" class="edge"><title>N8&#45;&gt;N69</title>
<g id="a_edge120"><a xlink:title="syscall.Syscall ... runtime.casgstatus (0.24s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M556.721,-647.656C498.829,-633.569 429.668,-606.763 388,-556 332.743,-488.681 330.589,-380.33 334.097,-322.448"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="337.604,-322.467 334.807,-312.248 330.621,-321.981 337.604,-322.467"/>
</a>
</g>
<g id="a_edge120&#45;label"><a xlink:title="syscall.Syscall ... runtime.casgstatus (0.24s)">
<text text-anchor="middle" x="360" y="-442.3" font-family="Times,serif" font-size="14.00"> 0.24s</text>
</a>
</g>
</g>
<!-- N9 -->
<g id="node9" class="node"><title>N9</title>
<g id="a_node9"><a xlink:title="adapter/service/logger.(*Logger).Write (47.85s)">
<polygon fill="#eddcd5" stroke="#b23100" points="1300,-2376 1214,-2376 1214,-2318 1300,-2318 1300,-2376"/>
<text text-anchor="middle" x="1257" y="-2364.8" font-family="Times,serif" font-size="9.00">logger</text>
<text text-anchor="middle" x="1257" y="-2354.8" font-family="Times,serif" font-size="9.00">(*Logger)</text>
<text text-anchor="middle" x="1257" y="-2344.8" font-family="Times,serif" font-size="9.00">Write</text>
<text text-anchor="middle" x="1257" y="-2334.8" font-family="Times,serif" font-size="9.00">0.07s (0.05%)</text>
<text text-anchor="middle" x="1257" y="-2324.8" font-family="Times,serif" font-size="9.00">of 47.85s (33.90%)</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N2 -->
<g id="edge3" class="edge"><title>N9&#45;&gt;N2</title>
<g id="a_edge3"><a xlink:title="adapter/service/logger.(*Logger).Write &#45;&gt; adapter/service/helper.(*MessageM).Dispatcher (38.76s)">
<path fill="none" stroke="#b23900" stroke-width="2" d="M1257,-2317.98C1257,-2302.89 1257,-2284.06 1257,-2267.6"/>
<polygon fill="#b23900" stroke="#b23900" points="1260.5,-2267.2 1257,-2257.2 1253.5,-2267.2 1260.5,-2267.2"/>
</a>
</g>
<g id="a_edge3&#45;label"><a xlink:title="adapter/service/logger.(*Logger).Write &#45;&gt; adapter/service/helper.(*MessageM).Dispatcher (38.76s)">
<text text-anchor="middle" x="1277" y="-2288.3" font-family="Times,serif" font-size="14.00"> 38.76s</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N42 -->
<g id="edge36" class="edge"><title>N9&#45;&gt;N42</title>
<g id="a_edge36"><a xlink:title="adapter/service/logger.(*Logger).Write &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (6.43s)">
<path fill="none" stroke="#b2a289" d="M1238.25,-2317.63C1228.26,-2302.4 1215.84,-2283.25 1205,-2266 1179.02,-2224.62 1165.29,-2217.7 1148,-2172 1131.29,-2127.83 1123.44,-2073.8 1119.85,-2038.21"/>
<polygon fill="#b2a289" stroke="#b2a289" points="1123.34,-2037.87 1118.91,-2028.24 1116.37,-2038.53 1123.34,-2037.87"/>
</a>
</g>
<g id="a_edge36&#45;label"><a xlink:title="adapter/service/logger.(*Logger).Write &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (6.43s)">
<text text-anchor="middle" x="1165" y="-2160.3" font-family="Times,serif" font-size="14.00"> 6.43s</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N8 -->
<g id="edge57" class="edge"><title>N9&#45;&gt;N8</title>
<g id="a_edge57"><a xlink:title="adapter/service/logger.(*Logger).Write ... syscall.Syscall (2.17s)">
<path fill="none" stroke="#b2aea4" stroke-dasharray="1,5" d="M1214.15,-2317.99C1194.07,-2303.74 1170.54,-2285.37 1152,-2266 1104.08,-2215.95 1098.52,-2197.49 1063,-2138 793.286,-1686.26 686.5,-1583.69 528,-1082 486.245,-949.835 427.994,-893.878 494,-772 506.49,-748.939 526.344,-730.059 547.992,-714.936"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="550.007,-717.799 556.364,-709.323 546.109,-711.985 550.007,-717.799"/>
</a>
</g>
<g id="a_edge57&#45;label"><a xlink:title="adapter/service/logger.(*Logger).Write ... syscall.Syscall (2.17s)">
<text text-anchor="middle" x="742" y="-1543.3" font-family="Times,serif" font-size="14.00"> 2.17s</text>
</a>
</g>
</g>
<!-- N10&#45;&gt;N9 -->
<g id="edge2" class="edge"><title>N10&#45;&gt;N9</title>
<g id="a_edge2"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/logger.(*Logger).Write (47.85s)">
<path fill="none" stroke="#b23100" stroke-width="2" d="M1257,-2428.37C1257,-2415.39 1257,-2400.11 1257,-2386.38"/>
<polygon fill="#b23100" stroke="#b23100" points="1260.5,-2386.24 1257,-2376.24 1253.5,-2386.24 1260.5,-2386.24"/>
</a>
</g>
<g id="a_edge2&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/logger.(*Logger).Write (47.85s)">
<text text-anchor="middle" x="1277" y="-2398.3" font-family="Times,serif" font-size="14.00"> 47.85s</text>
</a>
</g>
</g>
<!-- N38 -->
<g id="node38" class="node"><title>N38</title>
<g id="a_node38"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset (8.01s)">
<polygon fill="#edeae6" stroke="#b29d7f" points="1139,-2376 1045,-2376 1045,-2318 1139,-2318 1139,-2376"/>
<text text-anchor="middle" x="1092" y="-2364.8" font-family="Times,serif" font-size="9.00">kafka_consumer</text>
<text text-anchor="middle" x="1092" y="-2354.8" font-family="Times,serif" font-size="9.00">(*PartitionConsumer)</text>
<text text-anchor="middle" x="1092" y="-2344.8" font-family="Times,serif" font-size="9.00">SetOffset</text>
<text text-anchor="middle" x="1092" y="-2334.8" font-family="Times,serif" font-size="9.00">0.04s (0.028%)</text>
<text text-anchor="middle" x="1092" y="-2324.8" font-family="Times,serif" font-size="9.00">of 8.01s (5.67%)</text>
</a>
</g>
</g>
<!-- N10&#45;&gt;N38 -->
<g id="edge30" class="edge"><title>N10&#45;&gt;N38</title>
<g id="a_edge30"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset (8.01s)">
<path fill="none" stroke="#b29d7f" d="M1211.48,-2428.37C1190,-2413.93 1164.31,-2396.65 1142.27,-2381.82"/>
<polygon fill="#b29d7f" stroke="#b29d7f" points="1143.95,-2378.73 1133.7,-2376.05 1140.05,-2384.54 1143.95,-2378.73"/>
</a>
</g>
<g id="a_edge30&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset (8.01s)">
<text text-anchor="middle" x="1197" y="-2398.3" font-family="Times,serif" font-size="14.00"> 8.01s</text>
</a>
</g>
</g>
<!-- N22 -->
<g id="node22" class="node"><title>N22</title>
<g id="a_node22"><a xlink:title="runtime.selectgo (3.62s)">
<polygon fill="#edecea" stroke="#b2aa9b" points="663,-962 567,-962 567,-902 663,-902 663,-962"/>
<text text-anchor="middle" x="615" y="-948.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="615" y="-935.4" font-family="Times,serif" font-size="12.00">selectgo</text>
<text text-anchor="middle" x="615" y="-922.4" font-family="Times,serif" font-size="12.00">1.68s (1.19%)</text>
<text text-anchor="middle" x="615" y="-909.4" font-family="Times,serif" font-size="12.00">of 3.62s (2.56%)</text>
</a>
</g>
</g>
<!-- N10&#45;&gt;N22 -->
<g id="edge111" class="edge"><title>N10&#45;&gt;N22</title>
<g id="a_edge111"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.selectgo (0.30s)">
<path fill="none" stroke="#b2b2b0" d="M1204.71,-2456.4C1039.89,-2447.5 542,-2415.09 542,-2348 542,-2348 542,-2348 542,-1049 542,-1019.46 559.166,-991.293 576.85,-970.112"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="579.685,-972.186 583.618,-962.348 574.408,-967.586 579.685,-972.186"/>
</a>
</g>
<g id="a_edge111&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.selectgo (0.30s)">
<text text-anchor="middle" x="559" y="-1714.3" font-family="Times,serif" font-size="14.00"> 0.30s</text>
</a>
</g>
</g>
<!-- N11 -->
<g id="node11" class="node"><title>N11</title>
<g id="a_node11"><a xlink:title="encoding/json.mapEncoder.encode (11.72s)">
<polygon fill="#ede8e3" stroke="#b28f68" points="1340.25,-966 1245.75,-966 1245.75,-898 1340.25,-898 1340.25,-966"/>
<text text-anchor="middle" x="1293" y="-953.2" font-family="Times,serif" font-size="11.00">json</text>
<text text-anchor="middle" x="1293" y="-941.2" font-family="Times,serif" font-size="11.00">mapEncoder</text>
<text text-anchor="middle" x="1293" y="-929.2" font-family="Times,serif" font-size="11.00">encode</text>
<text text-anchor="middle" x="1293" y="-917.2" font-family="Times,serif" font-size="11.00">0.44s (0.31%)</text>
<text text-anchor="middle" x="1293" y="-905.2" font-family="Times,serif" font-size="11.00">of 11.72s (8.30%)</text>
</a>
</g>
</g>
<!-- N80 -->
<g id="node80" class="node"><title>N80</title>
<g id="a_node80"><a xlink:title="sort.quickSort_func (2.58s)">
<polygon fill="#edecea" stroke="#b2ada2" points="1126.25,-835 1043.75,-835 1043.75,-783 1126.25,-783 1126.25,-835"/>
<text text-anchor="middle" x="1085" y="-823" font-family="Times,serif" font-size="10.00">sort</text>
<text text-anchor="middle" x="1085" y="-812" font-family="Times,serif" font-size="10.00">quickSort_func</text>
<text text-anchor="middle" x="1085" y="-801" font-family="Times,serif" font-size="10.00">0.14s (0.099%)</text>
<text text-anchor="middle" x="1085" y="-790" font-family="Times,serif" font-size="10.00">of 2.58s (1.83%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N80 -->
<g id="edge54" class="edge"><title>N11&#45;&gt;N80</title>
<g id="a_edge54"><a xlink:title="encoding/json.mapEncoder.encode ... sort.quickSort_func (2.58s)">
<path fill="none" stroke="#b2ada2" stroke-dasharray="1,5" d="M1245.54,-903.392C1212.52,-884.183 1168.58,-858.619 1135.01,-839.094"/>
<polygon fill="#b2ada2" stroke="#b2ada2" points="1136.68,-836.016 1126.28,-834.013 1133.16,-842.067 1136.68,-836.016"/>
</a>
</g>
<g id="a_edge54&#45;label"><a xlink:title="encoding/json.mapEncoder.encode ... sort.quickSort_func (2.58s)">
<text text-anchor="middle" x="1219" y="-868.3" font-family="Times,serif" font-size="14.00"> 2.58s</text>
</a>
</g>
</g>
<!-- N27 -->
<g id="node27" class="node"><title>N27</title>
<g id="a_node27"><a xlink:title="encoding/json.(*encodeState).reflectValue (13.56s)">
<polygon fill="#ede7e1" stroke="#b2885c" points="1334,-1200 1252,-1200 1252,-1142 1334,-1142 1334,-1200"/>
<text text-anchor="middle" x="1293" y="-1188.8" font-family="Times,serif" font-size="9.00">json</text>
<text text-anchor="middle" x="1293" y="-1178.8" font-family="Times,serif" font-size="9.00">(*encodeState)</text>
<text text-anchor="middle" x="1293" y="-1168.8" font-family="Times,serif" font-size="9.00">reflectValue</text>
<text text-anchor="middle" x="1293" y="-1158.8" font-family="Times,serif" font-size="9.00">0.10s (0.071%)</text>
<text text-anchor="middle" x="1293" y="-1148.8" font-family="Times,serif" font-size="9.00">of 13.56s (9.61%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N27 -->
<g id="edge56" class="edge"><title>N11&#45;&gt;N27</title>
<g id="a_edge56"><a xlink:title="encoding/json.mapEncoder.encode ... encoding/json.(*encodeState).reflectValue (2.19s)">
<path fill="none" stroke="#b2aea4" stroke-dasharray="1,5" d="M1245.43,-966.212C1229.46,-980.167 1213.55,-997.861 1205,-1018 1203.77,-1020.91 1198.53,-1098.11 1210,-1116 1218.14,-1128.7 1230.45,-1139.04 1243.12,-1147.14"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="1241.52,-1150.26 1251.9,-1152.4 1245.12,-1144.26 1241.52,-1150.26"/>
</a>
</g>
<g id="a_edge56&#45;label"><a xlink:title="encoding/json.mapEncoder.encode ... encoding/json.(*encodeState).reflectValue (2.19s)">
<text text-anchor="middle" x="1222" y="-1046.3" font-family="Times,serif" font-size="14.00"> 2.19s</text>
</a>
</g>
</g>
<!-- N79 -->
<g id="node79" class="node"><title>N79</title>
<g id="a_node79"><a xlink:title="reflect.copyVal (2.08s)">
<polygon fill="#edeceb" stroke="#b2aea5" points="1571.25,-835 1488.75,-835 1488.75,-783 1571.25,-783 1571.25,-835"/>
<text text-anchor="middle" x="1530" y="-823" font-family="Times,serif" font-size="10.00">reflect</text>
<text text-anchor="middle" x="1530" y="-812" font-family="Times,serif" font-size="10.00">copyVal</text>
<text text-anchor="middle" x="1530" y="-801" font-family="Times,serif" font-size="10.00">0.15s (0.11%)</text>
<text text-anchor="middle" x="1530" y="-790" font-family="Times,serif" font-size="10.00">of 2.08s (1.47%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N79 -->
<g id="edge59" class="edge"><title>N11&#45;&gt;N79</title>
<g id="a_edge59"><a xlink:title="encoding/json.mapEncoder.encode ... reflect.copyVal (2.08s)">
<path fill="none" stroke="#b2aea5" stroke-dasharray="1,5" d="M1340.45,-912.2C1378.77,-896.338 1433.6,-872.096 1479,-846 1481.87,-844.353 1484.77,-842.587 1487.67,-840.754"/>
<polygon fill="#b2aea5" stroke="#b2aea5" points="1489.84,-843.517 1496.28,-835.108 1486,-837.663 1489.84,-843.517"/>
</a>
</g>
<g id="a_edge59&#45;label"><a xlink:title="encoding/json.mapEncoder.encode ... reflect.copyVal (2.08s)">
<text text-anchor="middle" x="1462" y="-868.3" font-family="Times,serif" font-size="14.00"> 2.08s</text>
</a>
</g>
</g>
<!-- N62 -->
<g id="node62" class="node"><title>N62</title>
<g id="a_node62"><a xlink:title="encoding/json.(*encodeState).string (1.72s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="1686,-845.5 1590,-845.5 1590,-772.5 1686,-772.5 1686,-845.5"/>
<text text-anchor="middle" x="1638" y="-831.9" font-family="Times,serif" font-size="12.00">json</text>
<text text-anchor="middle" x="1638" y="-818.9" font-family="Times,serif" font-size="12.00">(*encodeState)</text>
<text text-anchor="middle" x="1638" y="-805.9" font-family="Times,serif" font-size="12.00">string</text>
<text text-anchor="middle" x="1638" y="-792.9" font-family="Times,serif" font-size="12.00">1.04s (0.74%)</text>
<text text-anchor="middle" x="1638" y="-779.9" font-family="Times,serif" font-size="12.00">of 1.72s (1.22%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N62 -->
<g id="edge81" class="edge"><title>N11&#45;&gt;N62</title>
<g id="a_edge81"><a xlink:title="encoding/json.mapEncoder.encode &#45;&gt; encoding/json.(*encodeState).string (0.77s)">
<path fill="none" stroke="#b2b1ad" d="M1340.39,-910.416C1352.19,-905.841 1364.92,-901.359 1377,-898 1423.04,-885.196 1436.73,-891.946 1483,-880 1527.23,-868.58 1538.79,-865.719 1580,-846 1580.2,-845.904 1580.4,-845.808 1580.6,-845.711"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="1582.4,-848.722 1589.7,-841.045 1579.21,-842.493 1582.4,-848.722"/>
</a>
</g>
<g id="a_edge81&#45;label"><a xlink:title="encoding/json.mapEncoder.encode &#45;&gt; encoding/json.(*encodeState).string (0.77s)">
<text text-anchor="middle" x="1555" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.77s</text>
</a>
</g>
</g>
<!-- N56 -->
<g id="node56" class="node"><title>N56</title>
<g id="a_node56"><a xlink:title="runtime.makeslice (1.82s)">
<polygon fill="#edeceb" stroke="#b2afa6" points="1470.25,-835 1387.75,-835 1387.75,-783 1470.25,-783 1470.25,-835"/>
<text text-anchor="middle" x="1429" y="-823" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="1429" y="-812" font-family="Times,serif" font-size="10.00">makeslice</text>
<text text-anchor="middle" x="1429" y="-801" font-family="Times,serif" font-size="10.00">0.11s (0.078%)</text>
<text text-anchor="middle" x="1429" y="-790" font-family="Times,serif" font-size="10.00">of 1.82s (1.29%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N56 -->
<g id="edge84" class="edge"><title>N11&#45;&gt;N56</title>
<g id="a_edge84"><a xlink:title="encoding/json.mapEncoder.encode &#45;&gt; runtime.makeslice (0.69s)">
<path fill="none" stroke="#b2b1ae" d="M1330.16,-897.936C1349.66,-880.593 1373.44,-859.435 1392.79,-842.218"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1395.37,-844.609 1400.51,-835.347 1390.71,-839.379 1395.37,-844.609"/>
</a>
</g>
<g id="a_edge84&#45;label"><a xlink:title="encoding/json.mapEncoder.encode &#45;&gt; runtime.makeslice (0.69s)">
<text text-anchor="middle" x="1383" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.69s</text>
</a>
</g>
</g>
<!-- N47 -->
<g id="node47" class="node"><title>N47</title>
<g id="a_node47"><a xlink:title="runtime.mapiternext (2s)">
<polygon fill="#edeceb" stroke="#b2aea5" points="814.25,-841 723.75,-841 723.75,-777 814.25,-777 814.25,-841"/>
<text text-anchor="middle" x="769" y="-826.6" font-family="Times,serif" font-size="13.00">runtime</text>
<text text-anchor="middle" x="769" y="-812.6" font-family="Times,serif" font-size="13.00">mapiternext</text>
<text text-anchor="middle" x="769" y="-798.6" font-family="Times,serif" font-size="13.00">1.80s (1.28%)</text>
<text text-anchor="middle" x="769" y="-784.6" font-family="Times,serif" font-size="13.00">of 2s (1.42%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N47 -->
<g id="edge93" class="edge"><title>N11&#45;&gt;N47</title>
<g id="a_edge93"><a xlink:title="encoding/json.mapEncoder.encode ... runtime.mapiternext (0.59s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1245.75,-927.944C1160.14,-921.199 974.776,-901.004 829,-846 827.167,-845.308 825.327,-844.561 823.489,-843.768"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="824.879,-840.554 814.34,-839.464 821.899,-846.888 824.879,-840.554"/>
</a>
</g>
<g id="a_edge93&#45;label"><a xlink:title="encoding/json.mapEncoder.encode ... runtime.mapiternext (0.59s)">
<text text-anchor="middle" x="945" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.59s</text>
</a>
</g>
</g>
<!-- N18 -->
<g id="node18" class="node"><title>N18</title>
<g id="a_node18"><a xlink:title="[adapter] (3.73s)">
<polygon fill="#edecea" stroke="#b2aa9a" points="1264,-686 1174,-686 1174,-642 1264,-642 1264,-686"/>
<text text-anchor="middle" x="1219" y="-673.2" font-family="Times,serif" font-size="11.00">[adapter]</text>
<text text-anchor="middle" x="1219" y="-661.2" font-family="Times,serif" font-size="11.00">0.81s (0.57%)</text>
<text text-anchor="middle" x="1219" y="-649.2" font-family="Times,serif" font-size="11.00">of 3.73s (2.64%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N18 -->
<g id="edge96" class="edge"><title>N11&#45;&gt;N18</title>
<g id="a_edge96"><a xlink:title="encoding/json.mapEncoder.encode ... [adapter] (0.56s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1303.75,-897.858C1312.63,-865.079 1321.74,-813.889 1308,-772 1297.8,-740.92 1273.5,-712.789 1252.87,-693.18"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1255.19,-690.558 1245.46,-686.351 1250.44,-695.704 1255.19,-690.558"/>
</a>
</g>
<g id="a_edge96&#45;label"><a xlink:title="encoding/json.mapEncoder.encode ... [adapter] (0.56s)">
<text text-anchor="middle" x="1333" y="-805.3" font-family="Times,serif" font-size="14.00"> 0.56s</text>
</a>
</g>
</g>
<!-- N12 -->
<g id="node12" class="node"><title>N12</title>
<g id="a_node12"><a xlink:title="runtime.mcall (21.91s)">
<polygon fill="#ede2da" stroke="#b26327" points="160,-835 66,-835 66,-783 160,-783 160,-835"/>
<text text-anchor="middle" x="113" y="-823" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="113" y="-812" font-family="Times,serif" font-size="10.00">mcall</text>
<text text-anchor="middle" x="113" y="-801" font-family="Times,serif" font-size="10.00">0.12s (0.085%)</text>
<text text-anchor="middle" x="113" y="-790" font-family="Times,serif" font-size="10.00">of 21.91s (15.52%)</text>
</a>
</g>
</g>
<!-- N16 -->
<g id="node16" class="node"><title>N16</title>
<g id="a_node16"><a xlink:title="runtime.schedule (21.65s)">
<polygon fill="#ede2da" stroke="#b26429" points="160,-540 66,-540 66,-488 160,-488 160,-540"/>
<text text-anchor="middle" x="113" y="-528" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="113" y="-517" font-family="Times,serif" font-size="10.00">schedule</text>
<text text-anchor="middle" x="113" y="-506" font-family="Times,serif" font-size="10.00">0.29s (0.21%)</text>
<text text-anchor="middle" x="113" y="-495" font-family="Times,serif" font-size="10.00">of 21.65s (15.34%)</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N16 -->
<g id="edge11" class="edge"><title>N12&#45;&gt;N16</title>
<g id="a_edge11"><a xlink:title="runtime.mcall ... runtime.schedule (20.54s)">
<path fill="none" stroke="#b26930" stroke-dasharray="1,5" d="M113,-782.765C113,-730.517 113,-610.375 113,-550.402"/>
<polygon fill="#b26930" stroke="#b26930" points="116.5,-550.278 113,-540.278 109.5,-550.278 116.5,-550.278"/>
</a>
</g>
<g id="a_edge11&#45;label"><a xlink:title="runtime.mcall ... runtime.schedule (20.54s)">
<text text-anchor="middle" x="133" y="-660.3" font-family="Times,serif" font-size="14.00"> 20.54s</text>
</a>
</g>
</g>
<!-- N77 -->
<g id="node77" class="node"><title>N77</title>
<g id="a_node77"><a xlink:title="runtime.goschedImpl (1.72s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="76.25,-688 -0.25,-688 -0.25,-640 76.25,-640 76.25,-688"/>
<text text-anchor="middle" x="38" y="-676.8" font-family="Times,serif" font-size="9.00">runtime</text>
<text text-anchor="middle" x="38" y="-666.8" font-family="Times,serif" font-size="9.00">goschedImpl</text>
<text text-anchor="middle" x="38" y="-656.8" font-family="Times,serif" font-size="9.00">0.04s (0.028%)</text>
<text text-anchor="middle" x="38" y="-646.8" font-family="Times,serif" font-size="9.00">of 1.72s (1.22%)</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N77 -->
<g id="edge79" class="edge"><title>N12&#45;&gt;N77</title>
<g id="a_edge79"><a xlink:title="runtime.mcall ... runtime.goschedImpl (0.83s)">
<path fill="none" stroke="#b2b1ad" stroke-dasharray="1,5" d="M86.5847,-782.853C78.7643,-774.3 70.7621,-764.287 65,-754 55.2375,-736.571 48.605,-715.202 44.3572,-697.771"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="47.769,-696.99 42.1261,-688.023 40.9455,-698.552 47.769,-696.99"/>
</a>
</g>
<g id="a_edge79&#45;label"><a xlink:title="runtime.mcall ... runtime.goschedImpl (0.83s)">
<text text-anchor="middle" x="82" y="-742.3" font-family="Times,serif" font-size="14.00"> 0.83s</text>
</a>
</g>
</g>
<!-- N13 -->
<g id="node13" class="node"><title>N13</title>
<g id="a_node13"><a xlink:title="github.com/Shopify/sarama.withRecover (10.22s)">
<polygon fill="#ede9e4" stroke="#b29571" points="902,-1068 822,-1068 822,-1032 902,-1032 902,-1068"/>
<text text-anchor="middle" x="862" y="-1057.1" font-family="Times,serif" font-size="8.00">sarama</text>
<text text-anchor="middle" x="862" y="-1048.1" font-family="Times,serif" font-size="8.00">withRecover</text>
<text text-anchor="middle" x="862" y="-1039.1" font-family="Times,serif" font-size="8.00">0 of 10.22s (7.24%)</text>
</a>
</g>
</g>
<!-- N59 -->
<g id="node59" class="node"><title>N59</title>
<g id="a_node59"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver (4.20s)">
<polygon fill="#edebe9" stroke="#b2a997" points="997.25,-961 918.75,-961 918.75,-903 997.25,-903 997.25,-961"/>
<text text-anchor="middle" x="958" y="-949.8" font-family="Times,serif" font-size="9.00">sarama</text>
<text text-anchor="middle" x="958" y="-939.8" font-family="Times,serif" font-size="9.00">(*Broker)</text>
<text text-anchor="middle" x="958" y="-929.8" font-family="Times,serif" font-size="9.00">responseReceiver</text>
<text text-anchor="middle" x="958" y="-919.8" font-family="Times,serif" font-size="9.00">0.07s (0.05%)</text>
<text text-anchor="middle" x="958" y="-909.8" font-family="Times,serif" font-size="9.00">of 4.20s (2.98%)</text>
</a>
</g>
</g>
<!-- N13&#45;&gt;N59 -->
<g id="edge41" class="edge"><title>N13&#45;&gt;N59</title>
<g id="a_edge41"><a xlink:title="github.com/Shopify/sarama.withRecover &#45;&gt; github.com/Shopify/sarama.(*Broker).responseReceiver (4.20s)">
<path fill="none" stroke="#b2a997" d="M876.165,-1031.88C889.762,-1015.45 910.729,-990.119 928.035,-969.207"/>
<polygon fill="#b2a997" stroke="#b2a997" points="930.787,-971.373 934.466,-961.437 925.394,-966.91 930.787,-971.373"/>
</a>
</g>
<g id="a_edge41&#45;label"><a xlink:title="github.com/Shopify/sarama.withRecover &#45;&gt; github.com/Shopify/sarama.(*Broker).responseReceiver (4.20s)">
<text text-anchor="middle" x="933" y="-988.3" font-family="Times,serif" font-size="14.00"> 4.20s</text>
</a>
</g>
</g>
<!-- N45 -->
<g id="node45" class="node"><title>N45</title>
<g id="a_node45"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive (3.89s)">
<polygon fill="#edece9" stroke="#b2aa99" points="900.25,-954 823.75,-954 823.75,-910 900.25,-910 900.25,-954"/>
<text text-anchor="middle" x="862" y="-943.6" font-family="Times,serif" font-size="8.00">sarama</text>
<text text-anchor="middle" x="862" y="-934.6" font-family="Times,serif" font-size="8.00">(*Broker)</text>
<text text-anchor="middle" x="862" y="-925.6" font-family="Times,serif" font-size="8.00">sendAndReceive</text>
<text text-anchor="middle" x="862" y="-916.6" font-family="Times,serif" font-size="8.00">0 of 3.89s (2.76%)</text>
</a>
</g>
</g>
<!-- N13&#45;&gt;N45 -->
<g id="edge42" class="edge"><title>N13&#45;&gt;N45</title>
<g id="a_edge42"><a xlink:title="github.com/Shopify/sarama.withRecover ... github.com/Shopify/sarama.(*Broker).sendAndReceive (3.89s)">
<path fill="none" stroke="#b2aa99" stroke-dasharray="1,5" d="M862,-1031.88C862,-1014.12 862,-985.944 862,-964.205"/>
<polygon fill="#b2aa99" stroke="#b2aa99" points="865.5,-964.124 862,-954.124 858.5,-964.125 865.5,-964.124"/>
</a>
</g>
<g id="a_edge42&#45;label"><a xlink:title="github.com/Shopify/sarama.withRecover ... github.com/Shopify/sarama.(*Broker).sendAndReceive (3.89s)">
<text text-anchor="middle" x="879" y="-988.3" font-family="Times,serif" font-size="14.00"> 3.89s</text>
</a>
</g>
</g>
<!-- N13&#45;&gt;N22 -->
<g id="edge74" class="edge"><title>N13&#45;&gt;N22</title>
<g id="a_edge74"><a xlink:title="github.com/Shopify/sarama.withRecover ... runtime.selectgo (0.99s)">
<path fill="none" stroke="#b2b1ac" stroke-dasharray="1,5" d="M821.729,-1032.96C784.822,-1017.91 728.654,-994.158 672.341,-966.016"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="673.81,-962.838 663.305,-961.463 670.66,-969.089 673.81,-962.838"/>
</a>
</g>
<g id="a_edge74&#45;label"><a xlink:title="github.com/Shopify/sarama.withRecover ... runtime.selectgo (0.99s)">
<text text-anchor="middle" x="762" y="-988.3" font-family="Times,serif" font-size="14.00"> 0.99s</text>
</a>
</g>
</g>
<!-- N21 -->
<g id="node21" class="node"><title>N21</title>
<g id="a_node21"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track (25.16s)">
<polygon fill="#ede0d8" stroke="#b25313" points="1376,-1689 1290,-1689 1290,-1631 1376,-1631 1376,-1689"/>
<text text-anchor="middle" x="1333" y="-1677.8" font-family="Times,serif" font-size="9.00">thinkingdata</text>
<text text-anchor="middle" x="1333" y="-1667.8" font-family="Times,serif" font-size="9.00">(*TDAnalytics)</text>
<text text-anchor="middle" x="1333" y="-1657.8" font-family="Times,serif" font-size="9.00">track</text>
<text text-anchor="middle" x="1333" y="-1647.8" font-family="Times,serif" font-size="9.00">0.05s (0.035%)</text>
<text text-anchor="middle" x="1333" y="-1637.8" font-family="Times,serif" font-size="9.00">of 25.16s (17.82%)</text>
</a>
</g>
</g>
<!-- N14&#45;&gt;N21 -->
<g id="edge5" class="edge"><title>N14&#45;&gt;N21</title>
<g id="a_edge5"><a xlink:title="adapter/service/logger.(*Logger).OnWrite &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track (25.16s)">
<path fill="none" stroke="#b25313" d="M1333,-1746.82C1333,-1732.57 1333,-1715.06 1333,-1699.58"/>
<polygon fill="#b25313" stroke="#b25313" points="1336.5,-1699.25 1333,-1689.25 1329.5,-1699.25 1336.5,-1699.25"/>
</a>
</g>
<g id="a_edge5&#45;label"><a xlink:title="adapter/service/logger.(*Logger).OnWrite &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track (25.16s)">
<text text-anchor="middle" x="1353" y="-1714.3" font-family="Times,serif" font-size="14.00"> 25.16s</text>
</a>
</g>
</g>
<!-- N65 -->
<g id="node65" class="node"><title>N65</title>
<g id="a_node65"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).user (7.58s)">
<polygon fill="#edeae6" stroke="#b29e82" points="1268.25,-1689 1191.75,-1689 1191.75,-1631 1268.25,-1631 1268.25,-1689"/>
<text text-anchor="middle" x="1230" y="-1677.8" font-family="Times,serif" font-size="9.00">thinkingdata</text>
<text text-anchor="middle" x="1230" y="-1667.8" font-family="Times,serif" font-size="9.00">(*TDAnalytics)</text>
<text text-anchor="middle" x="1230" y="-1657.8" font-family="Times,serif" font-size="9.00">user</text>
<text text-anchor="middle" x="1230" y="-1647.8" font-family="Times,serif" font-size="9.00">0.04s (0.028%)</text>
<text text-anchor="middle" x="1230" y="-1637.8" font-family="Times,serif" font-size="9.00">of 7.58s (5.37%)</text>
</a>
</g>
</g>
<!-- N14&#45;&gt;N65 -->
<g id="edge32" class="edge"><title>N14&#45;&gt;N65</title>
<g id="a_edge32"><a xlink:title="adapter/service/logger.(*Logger).OnWrite &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).user (7.58s)">
<path fill="none" stroke="#b29e82" d="M1307.54,-1746.82C1293.92,-1731.75 1277.01,-1713.03 1262.45,-1696.92"/>
<polygon fill="#b29e82" stroke="#b29e82" points="1264.83,-1694.32 1255.52,-1689.25 1259.63,-1699.02 1264.83,-1694.32"/>
</a>
</g>
<g id="a_edge32&#45;label"><a xlink:title="adapter/service/logger.(*Logger).OnWrite &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).user (7.58s)">
<text text-anchor="middle" x="1305" y="-1714.3" font-family="Times,serif" font-size="14.00"> 7.58s</text>
</a>
</g>
</g>
<!-- N15 -->
<g id="node15" class="node"><title>N15</title>
<g id="a_node15"><a xlink:title="runtime.scanobject (9.39s)">
<polygon fill="#ede9e5" stroke="#b29877" points="549.25,-200 424.75,-200 424.75,-120 549.25,-120 549.25,-200"/>
<text text-anchor="middle" x="487" y="-183.2" font-family="Times,serif" font-size="16.00">runtime</text>
<text text-anchor="middle" x="487" y="-165.2" font-family="Times,serif" font-size="16.00">scanobject</text>
<text text-anchor="middle" x="487" y="-147.2" font-family="Times,serif" font-size="16.00">5.62s (3.98%)</text>
<text text-anchor="middle" x="487" y="-129.2" font-family="Times,serif" font-size="16.00">of 9.39s (6.65%)</text>
</a>
</g>
</g>
<!-- N55 -->
<g id="node55" class="node"><title>N55</title>
<g id="a_node55"><a xlink:title="runtime.greyobject (2.24s)">
<polygon fill="#edeceb" stroke="#b2aea4" points="597.25,-66 492.75,-66 492.75,-2 597.25,-2 597.25,-66"/>
<text text-anchor="middle" x="545" y="-51.6" font-family="Times,serif" font-size="13.00">runtime</text>
<text text-anchor="middle" x="545" y="-37.6" font-family="Times,serif" font-size="13.00">greyobject</text>
<text text-anchor="middle" x="545" y="-23.6" font-family="Times,serif" font-size="13.00">2.17s (1.54%)</text>
<text text-anchor="middle" x="545" y="-9.6" font-family="Times,serif" font-size="13.00">of 2.24s (1.59%)</text>
</a>
</g>
</g>
<!-- N15&#45;&gt;N55 -->
<g id="edge60" class="edge"><title>N15&#45;&gt;N55</title>
<g id="a_edge60"><a xlink:title="runtime.scanobject &#45;&gt; runtime.greyobject (2.04s)">
<path fill="none" stroke="#b2aea5" d="M505.352,-119.766C511.994,-105.564 519.503,-89.5115 526.185,-75.2258"/>
<polygon fill="#b2aea5" stroke="#b2aea5" points="529.372,-76.6714 530.439,-66.1304 523.032,-73.7056 529.372,-76.6714"/>
</a>
</g>
<g id="a_edge60&#45;label"><a xlink:title="runtime.scanobject &#45;&gt; runtime.greyobject (2.04s)">
<text text-anchor="middle" x="537" y="-90.3" font-family="Times,serif" font-size="14.00"> 2.04s</text>
</a>
</g>
</g>
<!-- N40 -->
<g id="node40" class="node"><title>N40</title>
<g id="a_node40"><a xlink:title="runtime.findObject (2.55s)">
<polygon fill="#edeceb" stroke="#b2ada2" points="474.25,-59 383.75,-59 383.75,-9 474.25,-9 474.25,-59"/>
<text text-anchor="middle" x="429" y="-44.6" font-family="Times,serif" font-size="13.00">runtime</text>
<text text-anchor="middle" x="429" y="-30.6" font-family="Times,serif" font-size="13.00">findObject</text>
<text text-anchor="middle" x="429" y="-16.6" font-family="Times,serif" font-size="13.00">2.55s (1.81%)</text>
</a>
</g>
</g>
<!-- N15&#45;&gt;N40 -->
<g id="edge62" class="edge"><title>N15&#45;&gt;N40</title>
<g id="a_edge62"><a xlink:title="runtime.scanobject &#45;&gt; runtime.findObject (1.73s)">
<path fill="none" stroke="#b2afa7" d="M468.648,-119.766C460.953,-103.313 452.096,-84.3774 444.708,-68.5817"/>
<polygon fill="#b2afa7" stroke="#b2afa7" points="447.728,-66.778 440.321,-59.2028 441.387,-69.7438 447.728,-66.778"/>
</a>
</g>
<g id="a_edge62&#45;label"><a xlink:title="runtime.scanobject &#45;&gt; runtime.findObject (1.73s)">
<text text-anchor="middle" x="477" y="-90.3" font-family="Times,serif" font-size="14.00"> 1.73s</text>
</a>
</g>
</g>
<!-- N20 -->
<g id="node20" class="node"><title>N20</title>
<g id="a_node20"><a xlink:title="runtime.findrunnable (18.08s)">
<polygon fill="#ede4dd" stroke="#b27440" points="163.25,-420 62.75,-420 62.75,-364 163.25,-364 163.25,-420"/>
<text text-anchor="middle" x="113" y="-407.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="113" y="-395.2" font-family="Times,serif" font-size="11.00">findrunnable</text>
<text text-anchor="middle" x="113" y="-383.2" font-family="Times,serif" font-size="11.00">0.49s (0.35%)</text>
<text text-anchor="middle" x="113" y="-371.2" font-family="Times,serif" font-size="11.00">of 18.08s (12.81%)</text>
</a>
</g>
</g>
<!-- N16&#45;&gt;N20 -->
<g id="edge14" class="edge"><title>N16&#45;&gt;N20</title>
<g id="a_edge14"><a xlink:title="runtime.schedule &#45;&gt; runtime.findrunnable (18.08s)">
<path fill="none" stroke="#b27440" d="M113,-487.844C113,-471.221 113,-449.137 113,-430.462"/>
<polygon fill="#b27440" stroke="#b27440" points="116.5,-430.211 113,-420.211 109.5,-430.211 116.5,-430.211"/>
</a>
</g>
<g id="a_edge14&#45;label"><a xlink:title="runtime.schedule &#45;&gt; runtime.findrunnable (18.08s)">
<text text-anchor="middle" x="133" y="-442.3" font-family="Times,serif" font-size="14.00"> 18.08s</text>
</a>
</g>
</g>
<!-- N16&#45;&gt;N69 -->
<g id="edge97" class="edge"><title>N16&#45;&gt;N69</title>
<g id="a_edge97"><a xlink:title="runtime.schedule ... runtime.casgstatus (0.54s)">
<path fill="none" stroke="#b2b1af" stroke-dasharray="1,5" d="M143.757,-487.819C181.088,-456.527 244.991,-400.568 293,-346 299.997,-338.047 306.951,-329.019 313.225,-320.333"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="316.093,-322.341 319.012,-312.156 310.378,-318.297 316.093,-322.341"/>
</a>
</g>
<g id="a_edge97&#45;label"><a xlink:title="runtime.schedule ... runtime.casgstatus (0.54s)">
<text text-anchor="middle" x="293" y="-388.3" font-family="Times,serif" font-size="14.00"> 0.54s</text>
</a>
</g>
</g>
<!-- N17 -->
<g id="node17" class="node"><title>N17</title>
<g id="a_node17"><a xlink:title="runtime.usleep (10.09s)">
<polygon fill="#ede9e4" stroke="#b29572" points="176.25,-68 49.75,-68 49.75,-0 176.25,-0 176.25,-68"/>
<text text-anchor="middle" x="113" y="-49.6" font-family="Times,serif" font-size="18.00">runtime</text>
<text text-anchor="middle" x="113" y="-29.6" font-family="Times,serif" font-size="18.00">usleep</text>
<text text-anchor="middle" x="113" y="-9.6" font-family="Times,serif" font-size="18.00">10.09s (7.15%)</text>
</a>
</g>
</g>
<!-- N18&#45;&gt;N1 -->
<g id="edge52" class="edge"><title>N18&#45;&gt;N1</title>
<g id="a_edge52"><a xlink:title="[adapter] ... runtime.mallocgc (2.63s)">
<path fill="none" stroke="#b2ada1" stroke-dasharray="1,5" d="M1264.12,-645.394C1311.98,-626.905 1389.94,-597.334 1458,-574 1491.72,-562.438 1529.15,-550.511 1561.67,-540.433"/>
<polygon fill="#b2ada1" stroke="#b2ada1" points="1563.07,-543.663 1571.6,-537.368 1561.01,-536.975 1563.07,-543.663"/>
</a>
</g>
<g id="a_edge52&#45;label"><a xlink:title="[adapter] ... runtime.mallocgc (2.63s)">
<text text-anchor="middle" x="1475" y="-578.3" font-family="Times,serif" font-size="14.00"> 2.63s</text>
</a>
</g>
</g>
<!-- N49 -->
<g id="node49" class="node"><title>N49</title>
<g id="a_node49"><a xlink:title="runtime.typedmemmove (1.63s)">
<polygon fill="#edeceb" stroke="#b2afa8" points="1182.25,-540 1099.75,-540 1099.75,-488 1182.25,-488 1182.25,-540"/>
<text text-anchor="middle" x="1141" y="-528" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="1141" y="-517" font-family="Times,serif" font-size="10.00">typedmemmove</text>
<text text-anchor="middle" x="1141" y="-506" font-family="Times,serif" font-size="10.00">0.42s (0.3%)</text>
<text text-anchor="middle" x="1141" y="-495" font-family="Times,serif" font-size="10.00">of 1.63s (1.15%)</text>
</a>
</g>
</g>
<!-- N18&#45;&gt;N49 -->
<g id="edge117" class="edge"><title>N18&#45;&gt;N49</title>
<g id="a_edge117"><a xlink:title="[adapter] &#45;&gt; runtime.typedmemmove (0.24s)">
<path fill="none" stroke="#b2b2b0" d="M1207.82,-641.795C1195.05,-617.562 1173.97,-577.567 1158.91,-548.975"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1162,-547.342 1154.24,-540.127 1155.81,-550.606 1162,-547.342"/>
</a>
</g>
<g id="a_edge117&#45;label"><a xlink:title="[adapter] &#45;&gt; runtime.typedmemmove (0.24s)">
<text text-anchor="middle" x="1197" y="-578.3" font-family="Times,serif" font-size="14.00"> 0.24s</text>
</a>
</g>
</g>
<!-- N19 -->
<g id="node19" class="node"><title>N19</title>
<g id="a_node19"><a xlink:title="runtime.futex (4.56s)">
<polygon fill="#edebe9" stroke="#b2a895" points="374,-189.5 272,-189.5 272,-130.5 374,-130.5 374,-189.5"/>
<text text-anchor="middle" x="323" y="-173.5" font-family="Times,serif" font-size="15.00">runtime</text>
<text text-anchor="middle" x="323" y="-156.5" font-family="Times,serif" font-size="15.00">futex</text>
<text text-anchor="middle" x="323" y="-139.5" font-family="Times,serif" font-size="15.00">4.56s (3.23%)</text>
</a>
</g>
</g>
<!-- N36 -->
<g id="node36" class="node"><title>N36</title>
<g id="a_node36"><a xlink:title="runtime.stealWork (13.08s)">
<polygon fill="#ede7e2" stroke="#b2895f" points="164,-312 62,-312 62,-252 164,-252 164,-312"/>
<text text-anchor="middle" x="113" y="-298.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="113" y="-285.4" font-family="Times,serif" font-size="12.00">stealWork</text>
<text text-anchor="middle" x="113" y="-272.4" font-family="Times,serif" font-size="12.00">1.47s (1.04%)</text>
<text text-anchor="middle" x="113" y="-259.4" font-family="Times,serif" font-size="12.00">of 13.08s (9.27%)</text>
</a>
</g>
</g>
<!-- N20&#45;&gt;N36 -->
<g id="edge20" class="edge"><title>N20&#45;&gt;N36</title>
<g id="a_edge20"><a xlink:title="runtime.findrunnable &#45;&gt; runtime.stealWork (13.08s)">
<path fill="none" stroke="#b2895f" d="M113,-363.747C113,-351.199 113,-336.072 113,-322.32"/>
<polygon fill="#b2895f" stroke="#b2895f" points="116.5,-322.137 113,-312.137 109.5,-322.137 116.5,-322.137"/>
</a>
</g>
<g id="a_edge20&#45;label"><a xlink:title="runtime.findrunnable &#45;&gt; runtime.stealWork (13.08s)">
<text text-anchor="middle" x="133" y="-334.3" font-family="Times,serif" font-size="14.00"> 13.08s</text>
</a>
</g>
</g>
<!-- N20&#45;&gt;N44 -->
<g id="edge123" class="edge"><title>N20&#45;&gt;N44</title>
<g id="a_edge123"><a xlink:title="runtime.findrunnable ... runtime.lock2 (0.23s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M157.128,-363.841C164.756,-358.342 172.36,-352.3 179,-346 187.719,-337.729 196.079,-327.762 203.309,-318.222"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="206.249,-320.132 209.352,-310.001 200.609,-315.985 206.249,-320.132"/>
</a>
</g>
<g id="a_edge123&#45;label"><a xlink:title="runtime.findrunnable ... runtime.lock2 (0.23s)">
<text text-anchor="middle" x="211" y="-334.3" font-family="Times,serif" font-size="14.00"> 0.23s</text>
</a>
</g>
</g>
<!-- N21&#45;&gt;N6 -->
<g id="edge10" class="edge"><title>N21&#45;&gt;N6</title>
<g id="a_edge10"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add (21.93s)">
<path fill="none" stroke="#b26327" d="M1334.77,-1630.99C1335.62,-1617.45 1336.66,-1600.99 1337.58,-1586.31"/>
<polygon fill="#b26327" stroke="#b26327" points="1341.1,-1586.2 1338.23,-1576 1334.11,-1585.76 1341.1,-1586.2"/>
</a>
</g>
<g id="a_edge10&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add (21.93s)">
<text text-anchor="middle" x="1357" y="-1598.3" font-family="Times,serif" font-size="14.00"> 21.93s</text>
</a>
</g>
</g>
<!-- N52 -->
<g id="node52" class="node"><title>N52</title>
<g id="a_node52"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties (3.49s)">
<polygon fill="#edecea" stroke="#b2ab9c" points="1278.25,-1573 1195.75,-1573 1195.75,-1521 1278.25,-1521 1278.25,-1573"/>
<text text-anchor="middle" x="1237" y="-1561" font-family="Times,serif" font-size="10.00">thinkingdata</text>
<text text-anchor="middle" x="1237" y="-1550" font-family="Times,serif" font-size="10.00">mergeProperties</text>
<text text-anchor="middle" x="1237" y="-1539" font-family="Times,serif" font-size="10.00">0.15s (0.11%)</text>
<text text-anchor="middle" x="1237" y="-1528" font-family="Times,serif" font-size="10.00">of 3.49s (2.47%)</text>
</a>
</g>
</g>
<!-- N21&#45;&gt;N52 -->
<g id="edge47" class="edge"><title>N21&#45;&gt;N52</title>
<g id="a_edge47"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties (2.86s)">
<path fill="none" stroke="#b2aca0" d="M1308.78,-1630.99C1295.72,-1615.9 1279.53,-1597.17 1265.83,-1581.33"/>
<polygon fill="#b2aca0" stroke="#b2aca0" points="1268.11,-1578.62 1258.92,-1573.34 1262.81,-1583.2 1268.11,-1578.62"/>
</a>
</g>
<g id="a_edge47&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties (2.86s)">
<text text-anchor="middle" x="1305" y="-1598.3" font-family="Times,serif" font-size="14.00"> 2.86s</text>
</a>
</g>
</g>
<!-- N21&#45;&gt;N26 -->
<g id="edge137" class="edge"><title>N21&#45;&gt;N26</title>
<g id="a_edge137"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track &#45;&gt; runtime.mapassign_faststr (0.17s)">
<path fill="none" stroke="#b2b2b1" d="M1376.39,-1636.49C1453.57,-1596.46 1614.75,-1512.86 1701.95,-1467.63"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1703.66,-1470.69 1710.92,-1462.98 1700.43,-1464.47 1703.66,-1470.69"/>
</a>
</g>
<g id="a_edge137&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).track &#45;&gt; runtime.mapassign_faststr (0.17s)">
<text text-anchor="middle" x="1621" y="-1543.3" font-family="Times,serif" font-size="14.00"> 0.17s</text>
</a>
</g>
</g>
<!-- N66 -->
<g id="node66" class="node"><title>N66</title>
<g id="a_node66"><a xlink:title="runtime.unlock2 (1.04s)">
<polygon fill="#edecec" stroke="#b2b0ab" points="593,-837 503,-837 503,-781 593,-781 593,-837"/>
<text text-anchor="middle" x="548" y="-824.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="548" y="-812.2" font-family="Times,serif" font-size="11.00">unlock2</text>
<text text-anchor="middle" x="548" y="-800.2" font-family="Times,serif" font-size="11.00">0.84s (0.6%)</text>
<text text-anchor="middle" x="548" y="-788.2" font-family="Times,serif" font-size="11.00">of 1.04s (0.74%)</text>
</a>
</g>
</g>
<!-- N22&#45;&gt;N66 -->
<g id="edge105" class="edge"><title>N22&#45;&gt;N66</title>
<g id="a_edge105"><a xlink:title="runtime.selectgo ... runtime.unlock2 (0.41s)">
<path fill="none" stroke="#b2b2af" stroke-dasharray="1,5" d="M598.781,-901.709C589.547,-885.032 577.9,-863.999 568.091,-846.283"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="571.057,-844.415 563.151,-837.362 564.933,-847.806 571.057,-844.415"/>
</a>
</g>
<g id="a_edge105&#45;label"><a xlink:title="runtime.selectgo ... runtime.unlock2 (0.41s)">
<text text-anchor="middle" x="603" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.41s</text>
</a>
</g>
</g>
<!-- N22&#45;&gt;N7 -->
<g id="edge131" class="edge"><title>N22&#45;&gt;N7</title>
<g id="a_edge131"><a xlink:title="runtime.selectgo ... runtime.systemstack (0.20s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M566.75,-909.541C540.754,-895.36 510.675,-874.166 494,-846 440.02,-754.82 471.92,-713.652 480,-608 481.783,-584.685 486.217,-579.315 488,-556 490.847,-518.775 488.236,-509.333 488,-472 487.905,-457.026 487.709,-440.456 487.516,-426.375"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="491.013,-426.109 487.37,-416.16 484.013,-426.209 491.013,-426.109"/>
</a>
</g>
<g id="a_edge131&#45;label"><a xlink:title="runtime.selectgo ... runtime.systemstack (0.20s)">
<text text-anchor="middle" x="497" y="-660.3" font-family="Times,serif" font-size="14.00"> 0.20s</text>
</a>
</g>
</g>
<!-- N23 -->
<g id="node23" class="node"><title>N23</title>
<g id="a_node23"><a xlink:title="regexp.(*Regexp).doOnePass (8.70s)">
<polygon fill="#ede9e5" stroke="#b29a7b" points="1279.25,-1353.5 1160.75,-1353.5 1160.75,-1260.5 1279.25,-1260.5 1279.25,-1353.5"/>
<text text-anchor="middle" x="1220" y="-1337.5" font-family="Times,serif" font-size="15.00">regexp</text>
<text text-anchor="middle" x="1220" y="-1320.5" font-family="Times,serif" font-size="15.00">(*Regexp)</text>
<text text-anchor="middle" x="1220" y="-1303.5" font-family="Times,serif" font-size="15.00">doOnePass</text>
<text text-anchor="middle" x="1220" y="-1286.5" font-family="Times,serif" font-size="15.00">4.21s (2.98%)</text>
<text text-anchor="middle" x="1220" y="-1269.5" font-family="Times,serif" font-size="15.00">of 8.70s (6.16%)</text>
</a>
</g>
</g>
<!-- N61 -->
<g id="node61" class="node"><title>N61</title>
<g id="a_node61"><a xlink:title="regexp/syntax.(*Inst).MatchRunePos (2.46s)">
<polygon fill="#edeceb" stroke="#b2ada2" points="1488,-1203 1390,-1203 1390,-1139 1488,-1139 1488,-1203"/>
<text text-anchor="middle" x="1439" y="-1188.6" font-family="Times,serif" font-size="13.00">syntax</text>
<text text-anchor="middle" x="1439" y="-1174.6" font-family="Times,serif" font-size="13.00">(*Inst)</text>
<text text-anchor="middle" x="1439" y="-1160.6" font-family="Times,serif" font-size="13.00">MatchRunePos</text>
<text text-anchor="middle" x="1439" y="-1146.6" font-family="Times,serif" font-size="13.00">2.46s (1.74%)</text>
</a>
</g>
</g>
<!-- N23&#45;&gt;N61 -->
<g id="edge55" class="edge"><title>N23&#45;&gt;N61</title>
<g id="a_edge55"><a xlink:title="regexp.(*Regexp).doOnePass ... regexp/syntax.(*Inst).MatchRunePos (2.46s)">
<path fill="none" stroke="#b2ada2" stroke-dasharray="1,5" d="M1279.47,-1264.49C1282.64,-1262.87 1285.82,-1261.36 1289,-1260 1322.5,-1245.65 1337.08,-1259.58 1369,-1242 1383.33,-1234.11 1396.67,-1222.42 1407.79,-1210.83"/>
<polygon fill="#b2ada2" stroke="#b2ada2" points="1410.55,-1213 1414.76,-1203.28 1405.41,-1208.25 1410.55,-1213"/>
</a>
</g>
<g id="a_edge55&#45;label"><a xlink:title="regexp.(*Regexp).doOnePass ... regexp/syntax.(*Inst).MatchRunePos (2.46s)">
<text text-anchor="middle" x="1409" y="-1230.3" font-family="Times,serif" font-size="14.00"> 2.46s</text>
</a>
</g>
</g>
<!-- N23&#45;&gt;N46 -->
<g id="edge116" class="edge"><title>N23&#45;&gt;N46</title>
<g id="a_edge116"><a xlink:title="regexp.(*Regexp).doOnePass &#45;&gt; sync.(*Pool).Get (0.27s)">
<path fill="none" stroke="#b2b2b0" d="M1205.33,-1260.27C1191.77,-1215.42 1172.6,-1144.81 1165,-1082 1161.58,-1053.76 1162.71,-1046.35 1165,-1018 1170.57,-948.884 1154.51,-923.569 1190,-864 1195.83,-854.21 1204.17,-845.556 1213.07,-838.203"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1215.22,-840.967 1220.99,-832.08 1210.94,-835.43 1215.22,-840.967"/>
</a>
</g>
<g id="a_edge116&#45;label"><a xlink:title="regexp.(*Regexp).doOnePass &#45;&gt; sync.(*Pool).Get (0.27s)">
<text text-anchor="middle" x="1182" y="-1046.3" font-family="Times,serif" font-size="14.00"> 0.27s</text>
</a>
</g>
</g>
<!-- N24 -->
<g id="node24" class="node"><title>N24</title>
<g id="a_node24"><a xlink:title="runtime.memmove (2.69s)">
<polygon fill="#edecea" stroke="#b2ada1" points="2010.25,-417 1919.75,-417 1919.75,-367 2010.25,-367 2010.25,-417"/>
<text text-anchor="middle" x="1965" y="-402.6" font-family="Times,serif" font-size="13.00">runtime</text>
<text text-anchor="middle" x="1965" y="-388.6" font-family="Times,serif" font-size="13.00">memmove</text>
<text text-anchor="middle" x="1965" y="-374.6" font-family="Times,serif" font-size="13.00">2.69s (1.91%)</text>
</a>
</g>
</g>
<!-- N25&#45;&gt;N23 -->
<g id="edge28" class="edge"><title>N25&#45;&gt;N23</title>
<g id="a_edge28"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.formatProperties ... regexp.(*Regexp).doOnePass (8.70s)">
<path fill="none" stroke="#b29a7b" stroke-dasharray="1,5" d="M1198.79,-1407.76C1201.54,-1394.84 1204.92,-1378.92 1208.18,-1363.62"/>
<polygon fill="#b29a7b" stroke="#b29a7b" points="1211.63,-1364.2 1210.29,-1353.69 1204.78,-1362.74 1211.63,-1364.2"/>
</a>
</g>
<g id="a_edge28&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.formatProperties ... regexp.(*Regexp).doOnePass (8.70s)">
<text text-anchor="middle" x="1223" y="-1376.3" font-family="Times,serif" font-size="14.00"> 8.70s</text>
</a>
</g>
</g>
<!-- N25&#45;&gt;N47 -->
<g id="edge86" class="edge"><title>N25&#45;&gt;N47</title>
<g id="a_edge86"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.formatProperties ... runtime.mapiternext (0.65s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1177.71,-1407.69C1169.32,-1392.18 1159.02,-1372.28 1151,-1354 1109.19,-1258.73 1149.87,-1201.85 1071,-1134 982.327,-1057.71 893.486,-1166.88 813,-1082 779.31,-1046.47 789.872,-912.153 781,-864 780.227,-859.803 779.36,-855.436 778.456,-851.084"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="781.823,-850.091 776.305,-841.046 774.978,-851.557 781.823,-850.091"/>
</a>
</g>
<g id="a_edge86&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.formatProperties ... runtime.mapiternext (0.65s)">
<text text-anchor="middle" x="1061" y="-1104.3" font-family="Times,serif" font-size="14.00"> 0.65s</text>
</a>
</g>
</g>
<!-- N26&#45;&gt;N1 -->
<g id="edge71" class="edge"><title>N26&#45;&gt;N1</title>
<g id="a_edge71"><a xlink:title="runtime.mapassign_faststr ... runtime.mallocgc (1.12s)">
<path fill="none" stroke="#b2b0ab" stroke-dasharray="1,5" d="M1811.13,-1431.08C1925.92,-1420.74 2198,-1387.8 2198,-1308 2198,-1308 2198,-1308 2198,-871 2198,-677.936 2063.28,-637.62 1881,-574 1831.79,-556.825 1775.16,-542.453 1729.83,-532.196"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="1730.56,-528.773 1720.04,-530.005 1729.03,-535.604 1730.56,-528.773"/>
</a>
</g>
<g id="a_edge71&#45;label"><a xlink:title="runtime.mapassign_faststr ... runtime.mallocgc (1.12s)">
<text text-anchor="middle" x="2215" y="-988.3" font-family="Times,serif" font-size="14.00"> 1.12s</text>
</a>
</g>
</g>
<!-- N48 -->
<g id="node48" class="node"><title>N48</title>
<g id="a_node48"><a xlink:title="encoding/json.structEncoder.encode (13.30s)">
<polygon fill="#ede7e1" stroke="#b2895e" points="1337.25,-1081.5 1248.75,-1081.5 1248.75,-1018.5 1337.25,-1018.5 1337.25,-1081.5"/>
<text text-anchor="middle" x="1293" y="-1069.5" font-family="Times,serif" font-size="10.00">json</text>
<text text-anchor="middle" x="1293" y="-1058.5" font-family="Times,serif" font-size="10.00">structEncoder</text>
<text text-anchor="middle" x="1293" y="-1047.5" font-family="Times,serif" font-size="10.00">encode</text>
<text text-anchor="middle" x="1293" y="-1036.5" font-family="Times,serif" font-size="10.00">0.41s (0.29%)</text>
<text text-anchor="middle" x="1293" y="-1025.5" font-family="Times,serif" font-size="10.00">of 13.30s (9.42%)</text>
</a>
</g>
</g>
<!-- N27&#45;&gt;N48 -->
<g id="edge19" class="edge"><title>N27&#45;&gt;N48</title>
<g id="a_edge19"><a xlink:title="encoding/json.(*encodeState).reflectValue &#45;&gt; encoding/json.structEncoder.encode (13.30s)">
<path fill="none" stroke="#b2895e" d="M1293,-1141.8C1293,-1126.91 1293,-1108.38 1293,-1091.94"/>
<polygon fill="#b2895e" stroke="#b2895e" points="1296.5,-1091.5 1293,-1081.5 1289.5,-1091.5 1296.5,-1091.5"/>
</a>
</g>
<g id="a_edge19&#45;label"><a xlink:title="encoding/json.(*encodeState).reflectValue &#45;&gt; encoding/json.structEncoder.encode (13.30s)">
<text text-anchor="middle" x="1313" y="-1104.3" font-family="Times,serif" font-size="14.00"> 13.30s</text>
</a>
</g>
</g>
<!-- N28 -->
<g id="node28" class="node"><title>N28</title>
<g id="a_node28"><a xlink:title="runtime.gcBgMarkWorker (11.19s)">
<polygon fill="#ede8e3" stroke="#b2916b" points="479,-538 397,-538 397,-490 479,-490 479,-538"/>
<text text-anchor="middle" x="438" y="-526.8" font-family="Times,serif" font-size="9.00">runtime</text>
<text text-anchor="middle" x="438" y="-516.8" font-family="Times,serif" font-size="9.00">gcBgMarkWorker</text>
<text text-anchor="middle" x="438" y="-506.8" font-family="Times,serif" font-size="9.00">0.02s (0.014%)</text>
<text text-anchor="middle" x="438" y="-496.8" font-family="Times,serif" font-size="9.00">of 11.19s (7.93%)</text>
</a>
</g>
</g>
<!-- N28&#45;&gt;N7 -->
<g id="edge24" class="edge"><title>N28&#45;&gt;N7</title>
<g id="a_edge24"><a xlink:title="runtime.gcBgMarkWorker ... runtime.systemstack (10.96s)">
<path fill="none" stroke="#b2926d" stroke-dasharray="1,5" d="M436.811,-489.718C436.895,-474.394 438.696,-454.242 446,-438 448.166,-433.183 451.048,-428.533 454.285,-424.167"/>
<polygon fill="#b2926d" stroke="#b2926d" points="457.221,-426.106 460.851,-416.152 451.806,-421.67 457.221,-426.106"/>
</a>
</g>
<g id="a_edge24&#45;label"><a xlink:title="runtime.gcBgMarkWorker ... runtime.systemstack (10.96s)">
<text text-anchor="middle" x="466" y="-442.3" font-family="Times,serif" font-size="14.00"> 10.96s</text>
</a>
</g>
</g>
<!-- N29 -->
<g id="node29" class="node"><title>N29</title>
<g id="a_node29"><a xlink:title="runtime.slicebytetostring (3.52s)">
<polygon fill="#edecea" stroke="#b2ab9c" points="1960.25,-835 1875.75,-835 1875.75,-783 1960.25,-783 1960.25,-835"/>
<text text-anchor="middle" x="1918" y="-823" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="1918" y="-812" font-family="Times,serif" font-size="10.00">slicebytetostring</text>
<text text-anchor="middle" x="1918" y="-801" font-family="Times,serif" font-size="10.00">0.39s (0.28%)</text>
<text text-anchor="middle" x="1918" y="-790" font-family="Times,serif" font-size="10.00">of 3.52s (2.49%)</text>
</a>
</g>
</g>
<!-- N29&#45;&gt;N1 -->
<g id="edge46" class="edge"><title>N29&#45;&gt;N1</title>
<g id="a_edge46"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.mallocgc (2.90s)">
<path fill="none" stroke="#b2aca0" d="M1919.56,-782.977C1921.07,-735.488 1917.23,-631.881 1861,-574 1827.09,-539.092 1774.62,-524.242 1730.01,-518.168"/>
<polygon fill="#b2aca0" stroke="#b2aca0" points="1730.38,-514.688 1720.03,-516.935 1729.52,-521.635 1730.38,-514.688"/>
</a>
</g>
<g id="a_edge46&#45;label"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.mallocgc (2.90s)">
<text text-anchor="middle" x="1935" y="-660.3" font-family="Times,serif" font-size="14.00"> 2.90s</text>
</a>
</g>
</g>
<!-- N29&#45;&gt;N24 -->
<g id="edge124" class="edge"><title>N29&#45;&gt;N24</title>
<g id="a_edge124"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.memmove (0.23s)">
<path fill="none" stroke="#b2b2b1" d="M1931.98,-782.681C1940.66,-765.546 1951.08,-742.091 1956,-720 1979.3,-615.413 1973.14,-487.371 1968.29,-427.255"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1971.77,-426.85 1967.44,-417.181 1964.8,-427.441 1971.77,-426.85"/>
</a>
</g>
<g id="a_edge124&#45;label"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.memmove (0.23s)">
<text text-anchor="middle" x="1990" y="-578.3" font-family="Times,serif" font-size="14.00"> 0.23s</text>
</a>
</g>
</g>
<!-- N30 -->
<g id="node30" class="node"><title>N30</title>
<g id="a_node30"><a xlink:title="internal/poll.(*FD).Write (24.28s)">
<polygon fill="#ede1d8" stroke="#b25718" points="705,-840.5 611,-840.5 611,-777.5 705,-777.5 705,-840.5"/>
<text text-anchor="middle" x="658" y="-828.5" font-family="Times,serif" font-size="10.00">poll</text>
<text text-anchor="middle" x="658" y="-817.5" font-family="Times,serif" font-size="10.00">(*FD)</text>
<text text-anchor="middle" x="658" y="-806.5" font-family="Times,serif" font-size="10.00">Write</text>
<text text-anchor="middle" x="658" y="-795.5" font-family="Times,serif" font-size="10.00">0.16s (0.11%)</text>
<text text-anchor="middle" x="658" y="-784.5" font-family="Times,serif" font-size="10.00">of 24.28s (17.20%)</text>
</a>
</g>
</g>
<!-- N30&#45;&gt;N8 -->
<g id="edge6" class="edge"><title>N30&#45;&gt;N8</title>
<g id="a_edge6"><a xlink:title="internal/poll.(*FD).Write ... syscall.Syscall (23.80s)">
<path fill="none" stroke="#b25a1c" stroke-dasharray="1,5" d="M658,-777.323C658,-763.527 658,-746.681 658,-730.276"/>
<polygon fill="#b25a1c" stroke="#b25a1c" points="661.5,-730.149 658,-720.149 654.5,-730.149 661.5,-730.149"/>
</a>
</g>
<g id="a_edge6&#45;label"><a xlink:title="internal/poll.(*FD).Write ... syscall.Syscall (23.80s)">
<text text-anchor="middle" x="678" y="-742.3" font-family="Times,serif" font-size="14.00"> 23.80s</text>
</a>
</g>
</g>
<!-- N71 -->
<g id="node71" class="node"><title>N71</title>
<g id="a_node71"><a xlink:title="encoding/json.Marshal (14.41s)">
<polygon fill="#ede7e1" stroke="#b28457" points="1382,-1325 1298,-1325 1298,-1289 1382,-1289 1382,-1325"/>
<text text-anchor="middle" x="1340" y="-1314.1" font-family="Times,serif" font-size="8.00">json</text>
<text text-anchor="middle" x="1340" y="-1305.1" font-family="Times,serif" font-size="8.00">Marshal</text>
<text text-anchor="middle" x="1340" y="-1296.1" font-family="Times,serif" font-size="8.00">0 of 14.41s (10.21%)</text>
</a>
</g>
</g>
<!-- N31&#45;&gt;N71 -->
<g id="edge17" class="edge"><title>N31&#45;&gt;N71</title>
<g id="a_edge17"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).Add &#45;&gt; encoding/json.Marshal (14.41s)">
<path fill="none" stroke="#b28457" d="M1340,-1406.81C1340,-1385.53 1340,-1356.49 1340,-1335.4"/>
<polygon fill="#b28457" stroke="#b28457" points="1343.5,-1335.18 1340,-1325.18 1336.5,-1335.18 1343.5,-1335.18"/>
</a>
</g>
<g id="a_edge17&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).Add &#45;&gt; encoding/json.Marshal (14.41s)">
<text text-anchor="middle" x="1360" y="-1376.3" font-family="Times,serif" font-size="14.00"> 14.41s</text>
</a>
</g>
</g>
<!-- N31&#45;&gt;N29 -->
<g id="edge103" class="edge"><title>N31&#45;&gt;N29</title>
<g id="a_edge103"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).Add &#45;&gt; runtime.slicebytetostring (0.42s)">
<path fill="none" stroke="#b2b2af" d="M1383.21,-1411.94C1410.03,-1396.84 1444.66,-1375.92 1473,-1354 1520.49,-1317.27 1517.6,-1289.29 1570,-1260 1651.68,-1214.35 1689.67,-1248.53 1774,-1208 1815.9,-1187.86 1920.27,-1123.61 1941,-1082 1958.45,-1046.98 1920.8,-913.842 1918,-880 1917.07,-868.803 1916.82,-856.563 1916.87,-845.495"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="1920.37,-845.358 1916.99,-835.314 1913.37,-845.269 1920.37,-845.358"/>
</a>
</g>
<g id="a_edge103&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).Add &#45;&gt; runtime.slicebytetostring (0.42s)">
<text text-anchor="middle" x="1946" y="-1104.3" font-family="Times,serif" font-size="14.00"> 0.42s</text>
</a>
</g>
</g>
<!-- N32 -->
<g id="node32" class="node"><title>N32</title>
<g id="a_node32"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 (5.39s)">
<polygon fill="#edebe8" stroke="#b2a590" points="1065.25,-1336 982.75,-1336 982.75,-1278 1065.25,-1278 1065.25,-1336"/>
<text text-anchor="middle" x="1024" y="-1324.8" font-family="Times,serif" font-size="9.00">log4go</text>
<text text-anchor="middle" x="1024" y="-1314.8" font-family="Times,serif" font-size="9.00">NewFileLogWriter</text>
<text text-anchor="middle" x="1024" y="-1304.8" font-family="Times,serif" font-size="9.00">func2</text>
<text text-anchor="middle" x="1024" y="-1294.8" font-family="Times,serif" font-size="9.00">0.05s (0.035%)</text>
<text text-anchor="middle" x="1024" y="-1284.8" font-family="Times,serif" font-size="9.00">of 5.39s (3.82%)</text>
</a>
</g>
</g>
<!-- N76 -->
<g id="node76" class="node"><title>N76</title>
<g id="a_node76"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write (4.35s)">
<polygon fill="#edebe9" stroke="#b2a896" points="1062.25,-1200 985.75,-1200 985.75,-1142 1062.25,-1142 1062.25,-1200"/>
<text text-anchor="middle" x="1024" y="-1188.8" font-family="Times,serif" font-size="9.00">log4go</text>
<text text-anchor="middle" x="1024" y="-1178.8" font-family="Times,serif" font-size="9.00">(*FileLogWriter)</text>
<text text-anchor="middle" x="1024" y="-1168.8" font-family="Times,serif" font-size="9.00">write</text>
<text text-anchor="middle" x="1024" y="-1158.8" font-family="Times,serif" font-size="9.00">0.03s (0.021%)</text>
<text text-anchor="middle" x="1024" y="-1148.8" font-family="Times,serif" font-size="9.00">of 4.35s (3.08%)</text>
</a>
</g>
</g>
<!-- N32&#45;&gt;N76 -->
<g id="edge40" class="edge"><title>N32&#45;&gt;N76</title>
<g id="a_edge40"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; github.com/ivanabc/log4go.(*FileLogWriter).write (4.35s)">
<path fill="none" stroke="#b2a896" d="M1024,-1277.91C1024,-1258.26 1024,-1231.77 1024,-1210.2"/>
<polygon fill="#b2a896" stroke="#b2a896" points="1027.5,-1210.05 1024,-1200.05 1020.5,-1210.05 1027.5,-1210.05"/>
</a>
</g>
<g id="a_edge40&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; github.com/ivanabc/log4go.(*FileLogWriter).write (4.35s)">
<text text-anchor="middle" x="1041" y="-1230.3" font-family="Times,serif" font-size="14.00"> 4.35s</text>
</a>
</g>
</g>
<!-- N32&#45;&gt;N22 -->
<g id="edge77" class="edge"><title>N32&#45;&gt;N22</title>
<g id="a_edge77"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; runtime.selectgo (0.90s)">
<path fill="none" stroke="#b2b1ac" d="M991.497,-1277.88C983.954,-1271.76 975.835,-1265.48 968,-1260 864.819,-1187.82 813.22,-1204.86 724,-1116 681.519,-1073.69 649.127,-1011.57 631.083,-971.704"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="634.173,-970.038 626.915,-962.32 627.776,-972.88 634.173,-970.038"/>
</a>
</g>
<g id="a_edge77&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; runtime.selectgo (0.90s)">
<text text-anchor="middle" x="741" y="-1104.3" font-family="Times,serif" font-size="14.00"> 0.90s</text>
</a>
</g>
</g>
<!-- N33 -->
<g id="node33" class="node"><title>N33</title>
<g id="a_node33"><a xlink:title="fmt.Sprintf (2.85s)">
<polygon fill="#edecea" stroke="#b2aca0" points="1766.25,-958 1683.75,-958 1683.75,-906 1766.25,-906 1766.25,-958"/>
<text text-anchor="middle" x="1725" y="-946" font-family="Times,serif" font-size="10.00">fmt</text>
<text text-anchor="middle" x="1725" y="-935" font-family="Times,serif" font-size="10.00">Sprintf</text>
<text text-anchor="middle" x="1725" y="-924" font-family="Times,serif" font-size="10.00">0.14s (0.099%)</text>
<text text-anchor="middle" x="1725" y="-913" font-family="Times,serif" font-size="10.00">of 2.85s (2.02%)</text>
</a>
</g>
</g>
<!-- N64 -->
<g id="node64" class="node"><title>N64</title>
<g id="a_node64"><a xlink:title="fmt.(*pp).printArg (1.73s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="2061.25,-840.5 1978.75,-840.5 1978.75,-777.5 2061.25,-777.5 2061.25,-840.5"/>
<text text-anchor="middle" x="2020" y="-828.5" font-family="Times,serif" font-size="10.00">fmt</text>
<text text-anchor="middle" x="2020" y="-817.5" font-family="Times,serif" font-size="10.00">(*pp)</text>
<text text-anchor="middle" x="2020" y="-806.5" font-family="Times,serif" font-size="10.00">printArg</text>
<text text-anchor="middle" x="2020" y="-795.5" font-family="Times,serif" font-size="10.00">0.38s (0.27%)</text>
<text text-anchor="middle" x="2020" y="-784.5" font-family="Times,serif" font-size="10.00">of 1.73s (1.23%)</text>
</a>
</g>
</g>
<!-- N33&#45;&gt;N64 -->
<g id="edge72" class="edge"><title>N33&#45;&gt;N64</title>
<g id="a_edge72"><a xlink:title="fmt.Sprintf ... fmt.(*pp).printArg (1.09s)">
<path fill="none" stroke="#b2b0ab" stroke-dasharray="1,5" d="M1766.52,-910.157C1776.06,-905.799 1786.27,-901.478 1796,-898 1826.25,-887.185 1835.17,-889.04 1866,-880 1912.26,-866.434 1925.95,-867.693 1969,-846 1969.31,-845.845 1969.62,-845.687 1969.93,-845.529"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="1971.86,-848.456 1978.88,-840.523 1968.45,-842.346 1971.86,-848.456"/>
</a>
</g>
<g id="a_edge72&#45;label"><a xlink:title="fmt.Sprintf ... fmt.(*pp).printArg (1.09s)">
<text text-anchor="middle" x="1941" y="-868.3" font-family="Times,serif" font-size="14.00"> 1.09s</text>
</a>
</g>
</g>
<!-- N33&#45;&gt;N29 -->
<g id="edge99" class="edge"><title>N33&#45;&gt;N29</title>
<g id="a_edge99"><a xlink:title="fmt.Sprintf &#45;&gt; runtime.slicebytetostring (0.52s)">
<path fill="none" stroke="#b2b1af" d="M1764.99,-905.929C1795.27,-886.947 1837,-860.781 1869.24,-840.571"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="1871.45,-843.316 1878.06,-835.039 1867.73,-837.386 1871.45,-843.316"/>
</a>
</g>
<g id="a_edge99&#45;label"><a xlink:title="fmt.Sprintf &#45;&gt; runtime.slicebytetostring (0.52s)">
<text text-anchor="middle" x="1845" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.52s</text>
</a>
</g>
</g>
<!-- N33&#45;&gt;N46 -->
<g id="edge136" class="edge"><title>N33&#45;&gt;N46</title>
<g id="a_edge136"><a xlink:title="fmt.Sprintf ... sync.(*Pool).Get (0.17s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1683.73,-907.646C1654.59,-892.373 1614.11,-873.546 1576,-864 1462.35,-835.53 1423.75,-884.242 1313,-846 1311.37,-845.438 1309.75,-844.813 1308.14,-844.134"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1309.55,-840.929 1299.02,-839.724 1306.5,-847.23 1309.55,-840.929"/>
</a>
</g>
<g id="a_edge136&#45;label"><a xlink:title="fmt.Sprintf ... sync.(*Pool).Get (0.17s)">
<text text-anchor="middle" x="1640" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.17s</text>
</a>
</g>
</g>
<!-- N34 -->
<g id="node34" class="node"><title>N34</title>
<g id="a_node34"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord (3.40s)">
<polygon fill="#edecea" stroke="#b2ab9d" points="1618.25,-1076 1527.75,-1076 1527.75,-1024 1618.25,-1024 1618.25,-1076"/>
<text text-anchor="middle" x="1573" y="-1064" font-family="Times,serif" font-size="10.00">log4go</text>
<text text-anchor="middle" x="1573" y="-1053" font-family="Times,serif" font-size="10.00">FormatLogRecord</text>
<text text-anchor="middle" x="1573" y="-1042" font-family="Times,serif" font-size="10.00">0.33s (0.23%)</text>
<text text-anchor="middle" x="1573" y="-1031" font-family="Times,serif" font-size="10.00">of 3.40s (2.41%)</text>
</a>
</g>
</g>
<!-- N34&#45;&gt;N33 -->
<g id="edge80" class="edge"><title>N34&#45;&gt;N33</title>
<g id="a_edge80"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; fmt.Sprintf (0.79s)">
<path fill="none" stroke="#b2b1ad" d="M1610.65,-1023.91C1621.55,-1016.41 1633.37,-1008.04 1644,-1000 1658.72,-988.864 1674.46,-976.069 1688.1,-964.663"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="1690.47,-967.245 1695.87,-958.132 1685.97,-961.887 1690.47,-967.245"/>
</a>
</g>
<g id="a_edge80&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; fmt.Sprintf (0.79s)">
<text text-anchor="middle" x="1682" y="-988.3" font-family="Times,serif" font-size="14.00"> 0.79s</text>
</a>
</g>
</g>
<!-- N43 -->
<g id="node43" class="node"><title>N43</title>
<g id="a_node43"><a xlink:title="runtime.growslice (2.05s)">
<polygon fill="#edeceb" stroke="#b2aea5" points="1604.25,-958 1521.75,-958 1521.75,-906 1604.25,-906 1604.25,-958"/>
<text text-anchor="middle" x="1563" y="-946" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="1563" y="-935" font-family="Times,serif" font-size="10.00">growslice</text>
<text text-anchor="middle" x="1563" y="-924" font-family="Times,serif" font-size="10.00">0.36s (0.26%)</text>
<text text-anchor="middle" x="1563" y="-913" font-family="Times,serif" font-size="10.00">of 2.05s (1.45%)</text>
</a>
</g>
</g>
<!-- N34&#45;&gt;N43 -->
<g id="edge82" class="edge"><title>N34&#45;&gt;N43</title>
<g id="a_edge82"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.growslice (0.76s)">
<path fill="none" stroke="#b2b1ad" d="M1543.42,-1023.83C1537.15,-1016.82 1531.4,-1008.72 1528,-1000 1523.65,-988.822 1526.86,-977.085 1532.8,-966.613"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="1535.79,-968.441 1538.32,-958.154 1529.92,-964.615 1535.79,-968.441"/>
</a>
</g>
<g id="a_edge82&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.growslice (0.76s)">
<text text-anchor="middle" x="1545" y="-988.3" font-family="Times,serif" font-size="14.00"> 0.76s</text>
</a>
</g>
</g>
<!-- N34&#45;&gt;N56 -->
<g id="edge106" class="edge"><title>N34&#45;&gt;N56</title>
<g id="a_edge106"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord ... runtime.makeslice (0.38s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1535.12,-1023.69C1515.6,-1008.92 1492.85,-988.738 1478,-966 1453.63,-928.682 1440.72,-878.459 1434.38,-845.199"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1437.77,-844.254 1432.55,-835.034 1430.88,-845.496 1437.77,-844.254"/>
</a>
</g>
<g id="a_edge106&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord ... runtime.makeslice (0.38s)">
<text text-anchor="middle" x="1495" y="-928.3" font-family="Times,serif" font-size="14.00"> 0.38s</text>
</a>
</g>
</g>
<!-- N34&#45;&gt;N50 -->
<g id="edge114" class="edge"><title>N34&#45;&gt;N50</title>
<g id="a_edge114"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.convTstring (0.28s)">
<path fill="none" stroke="#b2b2b0" d="M1618.39,-1024.25C1623.25,-1021.99 1628.19,-1019.86 1633,-1018 1662.17,-1006.76 1671.57,-1010.56 1701,-1000 1733.55,-988.322 1768.81,-972.24 1796.53,-958.759"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1798.19,-961.844 1805.63,-954.297 1795.11,-955.559 1798.19,-961.844"/>
</a>
</g>
<g id="a_edge114&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.convTstring (0.28s)">
<text text-anchor="middle" x="1755" y="-988.3" font-family="Times,serif" font-size="14.00"> 0.28s</text>
</a>
</g>
</g>
<!-- N34&#45;&gt;N24 -->
<g id="edge122" class="edge"><title>N34&#45;&gt;N24</title>
<g id="a_edge122"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord ... runtime.memmove (0.23s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1598.92,-1023.9C1605.85,-1016.53 1613.07,-1008.24 1619,-1000 1649.1,-958.208 1640.04,-936.724 1674,-898 1700.03,-868.322 1720.64,-876.246 1746,-846 1804.56,-776.171 1913.14,-519.252 1951.3,-426.596"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1954.63,-427.719 1955.19,-417.139 1948.15,-425.058 1954.63,-427.719"/>
</a>
</g>
<g id="a_edge122&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord ... runtime.memmove (0.23s)">
<text text-anchor="middle" x="1827" y="-742.3" font-family="Times,serif" font-size="14.00"> 0.23s</text>
</a>
</g>
</g>
<!-- N35 -->
<g id="node35" class="node"><title>N35</title>
<g id="a_node35"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf (2.94s)">
<polygon fill="#edecea" stroke="#b2ac9f" points="1432.25,-1079 1355.75,-1079 1355.75,-1021 1432.25,-1021 1432.25,-1079"/>
<text text-anchor="middle" x="1394" y="-1067.8" font-family="Times,serif" font-size="9.00">log4go</text>
<text text-anchor="middle" x="1394" y="-1057.8" font-family="Times,serif" font-size="9.00">Logger</text>
<text text-anchor="middle" x="1394" y="-1047.8" font-family="Times,serif" font-size="9.00">intLogf</text>
<text text-anchor="middle" x="1394" y="-1037.8" font-family="Times,serif" font-size="9.00">0.07s (0.05%)</text>
<text text-anchor="middle" x="1394" y="-1027.8" font-family="Times,serif" font-size="9.00">of 2.94s (2.08%)</text>
</a>
</g>
</g>
<!-- N35&#45;&gt;N33 -->
<g id="edge85" class="edge"><title>N35&#45;&gt;N33</title>
<g id="a_edge85"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; fmt.Sprintf (0.67s)">
<path fill="none" stroke="#b2b1ae" d="M1432.4,-1039.05C1466.69,-1029.87 1518.13,-1015.37 1562,-1000 1600.08,-986.664 1642.17,-969.291 1674.05,-955.578"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1675.8,-958.63 1683.59,-951.448 1673.03,-952.205 1675.8,-958.63"/>
</a>
</g>
<g id="a_edge85&#45;label"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; fmt.Sprintf (0.67s)">
<text text-anchor="middle" x="1620" y="-988.3" font-family="Times,serif" font-size="14.00"> 0.67s</text>
</a>
</g>
</g>
<!-- N35&#45;&gt;N46 -->
<g id="edge130" class="edge"><title>N35&#45;&gt;N46</title>
<g id="a_edge130"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; sync.(*Pool).Get (0.20s)">
<path fill="none" stroke="#b2b2b1" d="M1402.12,-1020.8C1411.72,-980.923 1422.1,-908.01 1385,-864 1361.23,-835.803 1337.17,-862.114 1304,-846 1303.8,-845.903 1303.6,-845.805 1303.4,-845.706"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1304.97,-842.57 1294.55,-840.625 1301.48,-848.64 1304.97,-842.57"/>
</a>
</g>
<g id="a_edge130&#45;label"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; sync.(*Pool).Get (0.20s)">
<text text-anchor="middle" x="1429" y="-928.3" font-family="Times,serif" font-size="14.00"> 0.20s</text>
</a>
</g>
</g>
<!-- N57 -->
<g id="node57" class="node"><title>N57</title>
<g id="a_node57"><a xlink:title="runtime.runqgrab (10.98s)">
<polygon fill="#ede8e3" stroke="#b2926d" points="160.25,-188 65.75,-188 65.75,-132 160.25,-132 160.25,-188"/>
<text text-anchor="middle" x="113" y="-175.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="113" y="-163.2" font-family="Times,serif" font-size="11.00">runqgrab</text>
<text text-anchor="middle" x="113" y="-151.2" font-family="Times,serif" font-size="11.00">0.89s (0.63%)</text>
<text text-anchor="middle" x="113" y="-139.2" font-family="Times,serif" font-size="11.00">of 10.98s (7.78%)</text>
</a>
</g>
</g>
<!-- N36&#45;&gt;N57 -->
<g id="edge23" class="edge"><title>N36&#45;&gt;N57</title>
<g id="a_edge23"><a xlink:title="runtime.stealWork ... runtime.runqgrab (10.98s)">
<path fill="none" stroke="#b2926d" stroke-dasharray="1,5" d="M113,-251.951C113,-235.868 113,-215.698 113,-198.441"/>
<polygon fill="#b2926d" stroke="#b2926d" points="116.5,-198.136 113,-188.136 109.5,-198.136 116.5,-198.136"/>
</a>
</g>
<g id="a_edge23&#45;label"><a xlink:title="runtime.stealWork ... runtime.runqgrab (10.98s)">
<text text-anchor="middle" x="133" y="-222.3" font-family="Times,serif" font-size="14.00"> 10.98s</text>
</a>
</g>
</g>
<!-- N74 -->
<g id="node74" class="node"><title>N74</title>
<g id="a_node74"><a xlink:title="time.Time.AppendFormat (3.04s)">
<polygon fill="#edecea" stroke="#b2ac9f" points="2170,-843 2080,-843 2080,-775 2170,-775 2170,-843"/>
<text text-anchor="middle" x="2125" y="-830.2" font-family="Times,serif" font-size="11.00">time</text>
<text text-anchor="middle" x="2125" y="-818.2" font-family="Times,serif" font-size="11.00">Time</text>
<text text-anchor="middle" x="2125" y="-806.2" font-family="Times,serif" font-size="11.00">AppendFormat</text>
<text text-anchor="middle" x="2125" y="-794.2" font-family="Times,serif" font-size="11.00">0.78s (0.55%)</text>
<text text-anchor="middle" x="2125" y="-782.2" font-family="Times,serif" font-size="11.00">of 3.04s (2.15%)</text>
</a>
</g>
</g>
<!-- N37&#45;&gt;N74 -->
<g id="edge45" class="edge"><title>N37&#45;&gt;N74</title>
<g id="a_edge45"><a xlink:title="time.Time.Format &#45;&gt; time.Time.AppendFormat (3.04s)">
<path fill="none" stroke="#b2ac9f" d="M2123.46,-902.936C2123.71,-888.136 2124.01,-869.658 2124.29,-853.086"/>
<polygon fill="#b2ac9f" stroke="#b2ac9f" points="2127.79,-853.12 2124.45,-843.064 2120.79,-853.005 2127.79,-853.12"/>
</a>
</g>
<g id="a_edge45&#45;label"><a xlink:title="time.Time.Format &#45;&gt; time.Time.AppendFormat (3.04s)">
<text text-anchor="middle" x="2142" y="-868.3" font-family="Times,serif" font-size="14.00"> 3.04s</text>
</a>
</g>
</g>
<!-- N37&#45;&gt;N29 -->
<g id="edge75" class="edge"><title>N37&#45;&gt;N29</title>
<g id="a_edge75"><a xlink:title="time.Time.Format &#45;&gt; runtime.slicebytetostring (0.98s)">
<path fill="none" stroke="#b2b1ac" d="M2084.72,-906.656C2079.8,-903.679 2074.81,-900.728 2070,-898 2026.09,-873.081 2012.17,-872.177 1969,-846 1966.22,-844.314 1963.39,-842.529 1960.56,-840.691"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="1962.39,-837.704 1952.13,-835.065 1958.51,-843.527 1962.39,-837.704"/>
</a>
</g>
<g id="a_edge75&#45;label"><a xlink:title="time.Time.Format &#45;&gt; runtime.slicebytetostring (0.98s)">
<text text-anchor="middle" x="2053" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.98s</text>
</a>
</g>
</g>
<!-- N60 -->
<g id="node60" class="node"><title>N60</title>
<g id="a_node60"><a xlink:title="runtime.cgocall (4.96s)">
<polygon fill="#edebe8" stroke="#b2a793" points="1005.25,-2266 886.75,-2266 886.75,-2190 1005.25,-2190 1005.25,-2266"/>
<text text-anchor="middle" x="946" y="-2250" font-family="Times,serif" font-size="15.00">runtime</text>
<text text-anchor="middle" x="946" y="-2233" font-family="Times,serif" font-size="15.00">cgocall</text>
<text text-anchor="middle" x="946" y="-2216" font-family="Times,serif" font-size="15.00">4.91s (3.48%)</text>
<text text-anchor="middle" x="946" y="-2199" font-family="Times,serif" font-size="15.00">of 4.96s (3.51%)</text>
</a>
</g>
</g>
<!-- N38&#45;&gt;N60 -->
<g id="edge39" class="edge"><title>N38&#45;&gt;N60</title>
<g id="a_edge39"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... runtime.cgocall (4.96s)">
<path fill="none" stroke="#b2a793" stroke-dasharray="1,5" d="M1044.96,-2332.99C1025.06,-2325.73 1002.69,-2315.04 986,-2300 978.196,-2292.97 971.553,-2284.04 966.055,-2274.91"/>
<polygon fill="#b2a793" stroke="#b2a793" points="969.082,-2273.15 961.134,-2266.14 962.979,-2276.58 969.082,-2273.15"/>
</a>
</g>
<g id="a_edge39&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... runtime.cgocall (4.96s)">
<text text-anchor="middle" x="1003" y="-2288.3" font-family="Times,serif" font-size="14.00"> 4.96s</text>
</a>
</g>
</g>
<!-- N38&#45;&gt;N35 -->
<g id="edge53" class="edge"><title>N38&#45;&gt;N35</title>
<g id="a_edge53"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... github.com/ivanabc/log4go.Logger.intLogf (2.61s)">
<path fill="none" stroke="#b2ada1" stroke-dasharray="1,5" d="M1068.74,-2317.63C1052.61,-2295.04 1034,-2261.87 1034,-2229 1034,-2229 1034,-2229 1034,-1775 1034,-1657.64 1062.84,-1337.47 1151,-1260 1182.86,-1232.01 1308.49,-1269.23 1341,-1242 1364.27,-1222.5 1380.64,-1139.14 1388.57,-1089.22"/>
<polygon fill="#b2ada1" stroke="#b2ada1" points="1392.06,-1089.53 1390.13,-1079.11 1385.15,-1088.46 1392.06,-1089.53"/>
</a>
</g>
<g id="a_edge53&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... github.com/ivanabc/log4go.Logger.intLogf (2.61s)">
<text text-anchor="middle" x="1053" y="-1714.3" font-family="Times,serif" font-size="14.00"> 2.61s</text>
</a>
</g>
</g>
<!-- N39 -->
<g id="node39" class="node"><title>N39</title>
<g id="a_node39"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).newResourceMessage (18.18s)">
<polygon fill="#ede4dd" stroke="#b2743f" points="1305,-2028 1209,-2028 1209,-1970 1305,-1970 1305,-2028"/>
<text text-anchor="middle" x="1257" y="-2016.8" font-family="Times,serif" font-size="9.00">resource</text>
<text text-anchor="middle" x="1257" y="-2006.8" font-family="Times,serif" font-size="9.00">(*ResourceMessage)</text>
<text text-anchor="middle" x="1257" y="-1996.8" font-family="Times,serif" font-size="9.00">newResourceMessage</text>
<text text-anchor="middle" x="1257" y="-1986.8" font-family="Times,serif" font-size="9.00">0.02s (0.014%)</text>
<text text-anchor="middle" x="1257" y="-1976.8" font-family="Times,serif" font-size="9.00">of 18.18s (12.88%)</text>
</a>
</g>
</g>
<!-- N39&#45;&gt;N3 -->
<g id="edge15" class="edge"><title>N39&#45;&gt;N3</title>
<g id="a_edge15"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).newResourceMessage &#45;&gt; adapter/service/helper.(*MsgRecorder).Write (16.83s)">
<path fill="none" stroke="#b27a48" d="M1263.54,-1969.98C1266.99,-1958.78 1272,-1946.16 1279,-1936 1281.54,-1932.31 1284.46,-1928.73 1287.58,-1925.32"/>
<polygon fill="#b27a48" stroke="#b27a48" points="1290.16,-1927.69 1294.71,-1918.12 1285.18,-1922.77 1290.16,-1927.69"/>
</a>
</g>
<g id="a_edge15&#45;label"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).newResourceMessage &#45;&gt; adapter/service/helper.(*MsgRecorder).Write (16.83s)">
<text text-anchor="middle" x="1299" y="-1940.3" font-family="Times,serif" font-size="14.00"> 16.83s</text>
</a>
</g>
</g>
<!-- N39&#45;&gt;N26 -->
<g id="edge113" class="edge"><title>N39&#45;&gt;N26</title>
<g id="a_edge113"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).newResourceMessage ... runtime.mapassign_faststr (0.29s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1305.09,-1983.55C1321.47,-1978.89 1339.96,-1973.92 1357,-1970 1454.28,-1947.63 1799,-1989.82 1799,-1890 1799,-1890 1799,-1890 1799,-1546 1799,-1521.61 1790.41,-1495.77 1781.4,-1475.37"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1784.49,-1473.73 1777.12,-1466.12 1778.14,-1476.66 1784.49,-1473.73"/>
</a>
</g>
<g id="a_edge113&#45;label"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).newResourceMessage ... runtime.mapassign_faststr (0.29s)">
<text text-anchor="middle" x="1816" y="-1714.3" font-family="Times,serif" font-size="14.00"> 0.29s</text>
</a>
</g>
</g>
<!-- N39&#45;&gt;N50 -->
<g id="edge121" class="edge"><title>N39&#45;&gt;N50</title>
<g id="a_edge121"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).newResourceMessage ... runtime.convTstring (0.23s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1241.2,-1969.9C1199.7,-1891.98 1096.77,-1669.04 1186,-1518 1196.87,-1499.6 1203.26,-1497.28 1220,-1484 1231.89,-1474.57 1238.16,-1476.61 1249,-1466 1271.72,-1443.75 1265.96,-1428.93 1288,-1406 1306.11,-1387.15 1312.65,-1383.75 1336,-1372 1358.97,-1360.44 1369.57,-1368.23 1391,-1354 1393.08,-1352.62 1625.52,-1084.02 1627,-1082 1646.65,-1055.1 1637.99,-1036.03 1666,-1018 1722.09,-981.9 1760.82,-1038.9 1815,-1000 1825.82,-992.228 1833.12,-979.809 1837.98,-967.763"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1841.3,-968.872 1841.37,-958.278 1834.71,-966.515 1841.3,-968.872"/>
</a>
</g>
<g id="a_edge121&#45;label"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).newResourceMessage ... runtime.convTstring (0.23s)">
<text text-anchor="middle" x="1237" y="-1488.3" font-family="Times,serif" font-size="14.00"> 0.23s</text>
</a>
</g>
</g>
<!-- N41&#45;&gt;N37 -->
<g id="edge49" class="edge"><title>N41&#45;&gt;N37</title>
<g id="a_edge49"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName &#45;&gt; time.Time.Format (2.74s)">
<path fill="none" stroke="#b2ada1" d="M1932.51,-1026.47C1957.5,-1013.31 1990.17,-996.831 2020,-984 2041.7,-974.669 2048.75,-976.299 2070,-966 2071.98,-965.04 2073.98,-964.024 2075.99,-962.968"/>
<polygon fill="#b2ada1" stroke="#b2ada1" points="2077.69,-966.025 2084.75,-958.124 2074.3,-959.899 2077.69,-966.025"/>
</a>
</g>
<g id="a_edge49&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName &#45;&gt; time.Time.Format (2.74s)">
<text text-anchor="middle" x="2037" y="-988.3" font-family="Times,serif" font-size="14.00"> 2.74s</text>
</a>
</g>
</g>
<!-- N41&#45;&gt;N33 -->
<g id="edge66" class="edge"><title>N41&#45;&gt;N33</title>
<g id="a_edge66"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName &#45;&gt; fmt.Sprintf (1.39s)">
<path fill="none" stroke="#b2b0a9" d="M1850.82,-1020.92C1826.16,-1003.69 1794.56,-981.611 1769.2,-963.888"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="1771.06,-960.918 1760.86,-958.059 1767.05,-966.656 1771.06,-960.918"/>
</a>
</g>
<g id="a_edge66&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName &#45;&gt; fmt.Sprintf (1.39s)">
<text text-anchor="middle" x="1835" y="-988.3" font-family="Times,serif" font-size="14.00"> 1.39s</text>
</a>
</g>
</g>
<!-- N41&#45;&gt;N50 -->
<g id="edge83" class="edge"><title>N41&#45;&gt;N50</title>
<g id="a_edge83"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName &#45;&gt; runtime.convTstring (0.70s)">
<path fill="none" stroke="#b2b1ae" d="M1894.65,-1020.71C1895.13,-1009.02 1894.31,-995.522 1890,-984 1887.71,-977.878 1884.34,-971.977 1880.48,-966.496"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1882.98,-964.014 1874.09,-958.259 1877.45,-968.305 1882.98,-964.014"/>
</a>
</g>
<g id="a_edge83&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*LogConsumer).constructFileName &#45;&gt; runtime.convTstring (0.70s)">
<text text-anchor="middle" x="1911" y="-988.3" font-family="Times,serif" font-size="14.00"> 0.70s</text>
</a>
</g>
</g>
<!-- N78 -->
<g id="node78" class="node"><title>N78</title>
<g id="a_node78"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal (7.22s)">
<polygon fill="#edeae7" stroke="#b29f84" points="1155.25,-1918 1078.75,-1918 1078.75,-1860 1155.25,-1860 1155.25,-1918"/>
<text text-anchor="middle" x="1117" y="-1906.8" font-family="Times,serif" font-size="9.00">go</text>
<text text-anchor="middle" x="1117" y="-1896.8" font-family="Times,serif" font-size="9.00">(*Iterator)</text>
<text text-anchor="middle" x="1117" y="-1886.8" font-family="Times,serif" font-size="9.00">ReadVal</text>
<text text-anchor="middle" x="1117" y="-1876.8" font-family="Times,serif" font-size="9.00">0.03s (0.021%)</text>
<text text-anchor="middle" x="1117" y="-1866.8" font-family="Times,serif" font-size="9.00">of 7.22s (5.11%)</text>
</a>
</g>
</g>
<!-- N42&#45;&gt;N78 -->
<g id="edge34" class="edge"><title>N42&#45;&gt;N78</title>
<g id="a_edge34"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).ReadVal (7.22s)">
<path fill="none" stroke="#b29f84" d="M1117,-1969.91C1117,-1957.26 1117,-1942.12 1117,-1928.44"/>
<polygon fill="#b29f84" stroke="#b29f84" points="1120.5,-1928.33 1117,-1918.33 1113.5,-1928.33 1120.5,-1928.33"/>
</a>
</g>
<g id="a_edge34&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).ReadVal (7.22s)">
<text text-anchor="middle" x="1134" y="-1940.3" font-family="Times,serif" font-size="14.00"> 7.22s</text>
</a>
</g>
</g>
<!-- N43&#45;&gt;N1 -->
<g id="edge67" class="edge"><title>N43&#45;&gt;N1</title>
<g id="a_edge67"><a xlink:title="runtime.growslice &#45;&gt; runtime.mallocgc (1.38s)">
<path fill="none" stroke="#b2b0a9" d="M1604.48,-910.295C1652.76,-886.094 1726.2,-848.949 1728,-846 1756.62,-799.067 1700.57,-646.052 1667.63,-565.554"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="1670.78,-564.019 1663.73,-556.109 1664.31,-566.688 1670.78,-564.019"/>
</a>
</g>
<g id="a_edge67&#45;label"><a xlink:title="runtime.growslice &#45;&gt; runtime.mallocgc (1.38s)">
<text text-anchor="middle" x="1747" y="-742.3" font-family="Times,serif" font-size="14.00"> 1.38s</text>
</a>
</g>
</g>
<!-- N43&#45;&gt;N53 -->
<g id="edge134" class="edge"><title>N43&#45;&gt;N53</title>
<g id="a_edge134"><a xlink:title="runtime.growslice &#45;&gt; runtime.memclrNoHeapPointers (0.19s)">
<path fill="none" stroke="#b2b2b1" d="M1604.02,-905.905C1641.8,-882.593 1692.4,-850.771 1695,-846 1710.75,-817.127 1713.01,-799.521 1695,-772 1663.4,-723.703 1612.13,-765.01 1576,-720 1544.24,-680.44 1590.58,-643.146 1554,-608 1516.38,-571.854 1471.95,-629.619 1438,-590 1398.47,-543.863 1423.49,-467.603 1442.8,-424.756"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1446,-426.177 1447.05,-415.635 1439.66,-423.219 1446,-426.177"/>
</a>
</g>
<g id="a_edge134&#45;label"><a xlink:title="runtime.growslice &#45;&gt; runtime.memclrNoHeapPointers (0.19s)">
<text text-anchor="middle" x="1593" y="-660.3" font-family="Times,serif" font-size="14.00"> 0.19s</text>
</a>
</g>
</g>
<!-- N44&#45;&gt;N19 -->
<g id="edge108" class="edge"><title>N44&#45;&gt;N19</title>
<g id="a_edge108"><a xlink:title="runtime.lock2 ... runtime.futex (0.37s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M248.784,-253.77C262.024,-237.219 279.104,-215.87 293.54,-197.825"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="296.45,-199.791 299.964,-189.795 290.984,-195.418 296.45,-199.791"/>
</a>
</g>
<g id="a_edge108&#45;label"><a xlink:title="runtime.lock2 ... runtime.futex (0.37s)">
<text text-anchor="middle" x="292" y="-222.3" font-family="Times,serif" font-size="14.00"> 0.37s</text>
</a>
</g>
</g>
<!-- N45&#45;&gt;N30 -->
<g id="edge64" class="edge"><title>N45&#45;&gt;N30</title>
<g id="a_edge64"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive ... internal/poll.(*FD).Write (1.68s)">
<path fill="none" stroke="#b2afa7" stroke-dasharray="1,5" d="M823.925,-909.946C793.817,-893.103 750.863,-868.64 714,-846 713.911,-845.945 713.821,-845.89 713.732,-845.835"/>
<polygon fill="#b2afa7" stroke="#b2afa7" points="715.612,-842.883 705.27,-840.581 711.919,-848.83 715.612,-842.883"/>
</a>
</g>
<g id="a_edge64&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive ... internal/poll.(*FD).Write (1.68s)">
<text text-anchor="middle" x="788" y="-868.3" font-family="Times,serif" font-size="14.00"> 1.68s</text>
</a>
</g>
</g>
<!-- N45&#45;&gt;N18 -->
<g id="edge98" class="edge"><title>N45&#45;&gt;N18</title>
<g id="a_edge98"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive ... [adapter] (0.53s)">
<path fill="none" stroke="#b2b1af" stroke-dasharray="1,5" d="M885.251,-909.817C893.294,-901.299 901.628,-890.911 907,-880 928.6,-836.13 896.4,-809.317 928,-772 996.848,-690.697 1065.9,-765.992 1162,-720 1174.85,-713.85 1186.63,-703.671 1196.11,-693.731"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="1198.77,-696.009 1202.89,-686.247 1193.58,-691.31 1198.77,-696.009"/>
</a>
</g>
<g id="a_edge98&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive ... [adapter] (0.53s)">
<text text-anchor="middle" x="945" y="-805.3" font-family="Times,serif" font-size="14.00"> 0.53s</text>
</a>
</g>
</g>
<!-- N46&#45;&gt;N18 -->
<g id="edge138" class="edge"><title>N46&#45;&gt;N18</title>
<g id="a_edge138"><a xlink:title="sync.(*Pool).Get ... [adapter] (0.17s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1251.2,-777.323C1244.33,-753.339 1234.81,-720.139 1227.9,-696.058"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1231.24,-694.997 1225.12,-686.349 1224.51,-696.927 1231.24,-694.997"/>
</a>
</g>
<g id="a_edge138&#45;label"><a xlink:title="sync.(*Pool).Get ... [adapter] (0.17s)">
<text text-anchor="middle" x="1262" y="-742.3" font-family="Times,serif" font-size="14.00"> 0.17s</text>
</a>
</g>
</g>
<!-- N67 -->
<g id="node67" class="node"><title>N67</title>
<g id="a_node67"><a xlink:title="runtime.gcWriteBarrier (0.88s)">
<polygon fill="#edecec" stroke="#b2b1ac" points="892.25,-690 809.75,-690 809.75,-638 892.25,-638 892.25,-690"/>
<text text-anchor="middle" x="851" y="-678" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="851" y="-667" font-family="Times,serif" font-size="10.00">gcWriteBarrier</text>
<text text-anchor="middle" x="851" y="-656" font-family="Times,serif" font-size="10.00">0.29s (0.21%)</text>
<text text-anchor="middle" x="851" y="-645" font-family="Times,serif" font-size="10.00">of 0.88s (0.62%)</text>
</a>
</g>
</g>
<!-- N47&#45;&gt;N67 -->
<g id="edge135" class="edge"><title>N47&#45;&gt;N67</title>
<g id="a_edge135"><a xlink:title="runtime.mapiternext ... runtime.gcWriteBarrier (0.18s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M786.793,-776.971C799.933,-754.056 817.816,-722.87 831.372,-699.23"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="834.582,-700.668 836.52,-690.252 828.509,-697.186 834.582,-700.668"/>
</a>
</g>
<g id="a_edge135&#45;label"><a xlink:title="runtime.mapiternext ... runtime.gcWriteBarrier (0.18s)">
<text text-anchor="middle" x="825" y="-742.3" font-family="Times,serif" font-size="14.00"> 0.18s</text>
</a>
</g>
</g>
<!-- N48&#45;&gt;N11 -->
<g id="edge21" class="edge"><title>N48&#45;&gt;N11</title>
<g id="a_edge21"><a xlink:title="encoding/json.structEncoder.encode &#45;&gt; encoding/json.mapEncoder.encode (11.72s)">
<path fill="none" stroke="#b28f68" d="M1293,-1018.22C1293,-1005.49 1293,-990.497 1293,-976.665"/>
<polygon fill="#b28f68" stroke="#b28f68" points="1296.5,-976.371 1293,-966.371 1289.5,-976.371 1296.5,-976.371"/>
</a>
</g>
<g id="a_edge21&#45;label"><a xlink:title="encoding/json.structEncoder.encode &#45;&gt; encoding/json.mapEncoder.encode (11.72s)">
<text text-anchor="middle" x="1313" y="-988.3" font-family="Times,serif" font-size="14.00"> 11.72s</text>
</a>
</g>
</g>
<!-- N49&#45;&gt;N24 -->
<g id="edge87" class="edge"><title>N49&#45;&gt;N24</title>
<g id="a_edge87"><a xlink:title="runtime.typedmemmove &#45;&gt; runtime.memmove (0.65s)">
<path fill="none" stroke="#b2b1ae" d="M1182.29,-508.627C1256.13,-500.451 1415.46,-481.182 1548,-454 1575.04,-448.455 1580.91,-443.293 1608,-438 1713.77,-417.33 1839.11,-404.083 1909.61,-397.641"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1909.96,-401.124 1919.6,-396.74 1909.33,-394.152 1909.96,-401.124"/>
</a>
</g>
<g id="a_edge87&#45;label"><a xlink:title="runtime.typedmemmove &#45;&gt; runtime.memmove (0.65s)">
<text text-anchor="middle" x="1625" y="-442.3" font-family="Times,serif" font-size="14.00"> 0.65s</text>
</a>
</g>
</g>
<!-- N49&#45;&gt;N7 -->
<g id="edge109" class="edge"><title>N49&#45;&gt;N7</title>
<g id="a_edge109"><a xlink:title="runtime.typedmemmove ... runtime.systemstack (0.35s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1099.64,-511.975C983.558,-508.601 656.788,-495.569 559,-454 554.303,-452.003 537.12,-437.498 520.718,-423.134"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="522.605,-420.132 512.788,-416.149 517.979,-425.385 522.605,-420.132"/>
</a>
</g>
<g id="a_edge109&#45;label"><a xlink:title="runtime.typedmemmove ... runtime.systemstack (0.35s)">
<text text-anchor="middle" x="576" y="-442.3" font-family="Times,serif" font-size="14.00"> 0.35s</text>
</a>
</g>
</g>
<!-- N50&#45;&gt;N1 -->
<g id="edge61" class="edge"><title>N50&#45;&gt;N1</title>
<g id="a_edge61"><a xlink:title="runtime.convTstring &#45;&gt; runtime.mallocgc (1.80s)">
<path fill="none" stroke="#b2afa7" d="M1851.33,-905.956C1860.8,-842.469 1876.47,-673.243 1795,-574 1778.48,-553.876 1754.13,-540.581 1729.88,-531.811"/>
<polygon fill="#b2afa7" stroke="#b2afa7" points="1730.84,-528.441 1720.25,-528.552 1728.6,-535.072 1730.84,-528.441"/>
</a>
</g>
<g id="a_edge61&#45;label"><a xlink:title="runtime.convTstring &#45;&gt; runtime.mallocgc (1.80s)">
<text text-anchor="middle" x="1875" y="-742.3" font-family="Times,serif" font-size="14.00"> 1.80s</text>
</a>
</g>
</g>
<!-- N51&#45;&gt;N15 -->
<g id="edge31" class="edge"><title>N51&#45;&gt;N15</title>
<g id="a_edge31"><a xlink:title="runtime.gcDrain ... runtime.scanobject (7.94s)">
<path fill="none" stroke="#b29d80" stroke-dasharray="1,5" d="M487,-255.844C487,-242.652 487,-226.02 487,-210.383"/>
<polygon fill="#b29d80" stroke="#b29d80" points="490.5,-210.296 487,-200.296 483.5,-210.296 490.5,-210.296"/>
</a>
</g>
<g id="a_edge31&#45;label"><a xlink:title="runtime.gcDrain ... runtime.scanobject (7.94s)">
<text text-anchor="middle" x="504" y="-222.3" font-family="Times,serif" font-size="14.00"> 7.94s</text>
</a>
</g>
</g>
<!-- N52&#45;&gt;N26 -->
<g id="edge51" class="edge"><title>N52&#45;&gt;N26</title>
<g id="a_edge51"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties &#45;&gt; runtime.mapassign_faststr (2.69s)">
<path fill="none" stroke="#b2ada1" d="M1278.51,-1521.78C1281.67,-1520.38 1284.85,-1519.1 1288,-1518 1297.73,-1514.6 1575.18,-1468.04 1700.54,-1447.09"/>
<polygon fill="#b2ada1" stroke="#b2ada1" points="1701.46,-1450.48 1710.74,-1445.38 1700.3,-1443.58 1701.46,-1450.48"/>
</a>
</g>
<g id="a_edge51&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties &#45;&gt; runtime.mapassign_faststr (2.69s)">
<text text-anchor="middle" x="1491" y="-1488.3" font-family="Times,serif" font-size="14.00"> 2.69s</text>
</a>
</g>
</g>
<!-- N52&#45;&gt;N47 -->
<g id="edge91" class="edge"><title>N52&#45;&gt;N47</title>
<g id="a_edge91"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties ... runtime.mapiternext (0.60s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1214.26,-1520.86C1202.26,-1508.62 1186.77,-1494.35 1171,-1484 1140.55,-1464.01 900.719,-1373.58 870,-1354 787.712,-1301.54 768.579,-1282.88 706,-1208 608.096,-1090.85 457.11,-1012.59 558,-898 578.874,-874.292 670.643,-893.915 699,-880 713.935,-872.671 727.583,-860.877 738.783,-849.042"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="741.668,-851.07 745.778,-841.305 736.476,-846.375 741.668,-851.07"/>
</a>
</g>
<g id="a_edge91&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties ... runtime.mapiternext (0.60s)">
<text text-anchor="middle" x="723" y="-1167.3" font-family="Times,serif" font-size="14.00"> 0.60s</text>
</a>
</g>
</g>
<!-- N70 -->
<g id="node70" class="node"><title>N70</title>
<g id="a_node70"><a xlink:title="os.(*File).Write (22.62s)">
<polygon fill="#ede2da" stroke="#b25f23" points="767,-961 681,-961 681,-903 767,-903 767,-961"/>
<text text-anchor="middle" x="724" y="-949.8" font-family="Times,serif" font-size="9.00">os</text>
<text text-anchor="middle" x="724" y="-939.8" font-family="Times,serif" font-size="9.00">(*File)</text>
<text text-anchor="middle" x="724" y="-929.8" font-family="Times,serif" font-size="9.00">Write</text>
<text text-anchor="middle" x="724" y="-919.8" font-family="Times,serif" font-size="9.00">0.02s (0.014%)</text>
<text text-anchor="middle" x="724" y="-909.8" font-family="Times,serif" font-size="9.00">of 22.62s (16.02%)</text>
</a>
</g>
</g>
<!-- N54&#45;&gt;N70 -->
<g id="edge9" class="edge"><title>N54&#45;&gt;N70</title>
<g id="a_edge9"><a xlink:title="fmt.Fprintln &#45;&gt; os.(*File).Write (22.11s)">
<path fill="none" stroke="#b26226" d="M1674.7,-1031.48C1659.82,-1026.21 1642.9,-1021.03 1627,-1018 1461.77,-986.573 1416.86,-1010.55 1249,-1000 1224.81,-998.479 837.684,-971.165 814,-966 801.683,-963.314 788.775,-959.328 776.705,-955.047"/>
<polygon fill="#b26226" stroke="#b26226" points="777.825,-951.73 767.231,-951.569 775.412,-958.301 777.825,-951.73"/>
</a>
</g>
<g id="a_edge9&#45;label"><a xlink:title="fmt.Fprintln &#45;&gt; os.(*File).Write (22.11s)">
<text text-anchor="middle" x="1269" y="-988.3" font-family="Times,serif" font-size="14.00"> 22.11s</text>
</a>
</g>
</g>
<!-- N54&#45;&gt;N64 -->
<g id="edge88" class="edge"><title>N54&#45;&gt;N64</title>
<g id="a_edge88"><a xlink:title="fmt.Fprintln ... fmt.(*pp).printArg (0.63s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1756.15,-1025.89C1761.7,-1023.01 1767.42,-1020.28 1773,-1018 1801.54,-1006.34 1811.44,-1011.63 1840,-1000 1890.49,-979.435 2026.44,-927.046 2054,-880 2059.7,-870.262 2058.07,-859.426 2053.27,-849.246"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2056.31,-847.505 2048.35,-840.512 2050.21,-850.942 2056.31,-847.505"/>
</a>
</g>
<g id="a_edge88&#45;label"><a xlink:title="fmt.Fprintln ... fmt.(*pp).printArg (0.63s)">
<text text-anchor="middle" x="2055" y="-928.3" font-family="Times,serif" font-size="14.00"> 0.63s</text>
</a>
</g>
</g>
<!-- N56&#45;&gt;N1 -->
<g id="edge63" class="edge"><title>N56&#45;&gt;N1</title>
<g id="a_edge63"><a xlink:title="runtime.makeslice &#45;&gt; runtime.mallocgc (1.71s)">
<path fill="none" stroke="#b2afa7" d="M1435.96,-782.708C1447.98,-742.429 1475.69,-662.684 1520,-608 1533.8,-590.964 1551.42,-575.432 1569.07,-562.147"/>
<polygon fill="#b2afa7" stroke="#b2afa7" points="1571.17,-564.942 1577.16,-556.204 1567.03,-559.298 1571.17,-564.942"/>
</a>
</g>
<g id="a_edge63&#45;label"><a xlink:title="runtime.makeslice &#45;&gt; runtime.mallocgc (1.71s)">
<text text-anchor="middle" x="1537" y="-660.3" font-family="Times,serif" font-size="14.00"> 1.71s</text>
</a>
</g>
</g>
<!-- N57&#45;&gt;N17 -->
<g id="edge26" class="edge"><title>N57&#45;&gt;N17</title>
<g id="a_edge26"><a xlink:title="runtime.runqgrab &#45;&gt; runtime.usleep (10.09s)">
<path fill="none" stroke="#b29572" d="M113,-131.788C113,-116.174 113,-96.2074 113,-78.4695"/>
<polygon fill="#b29572" stroke="#b29572" points="116.5,-78.3329 113,-68.3329 109.5,-78.333 116.5,-78.3329"/>
</a>
</g>
<g id="a_edge26&#45;label"><a xlink:title="runtime.runqgrab &#45;&gt; runtime.usleep (10.09s)">
<text text-anchor="middle" x="133" y="-90.3" font-family="Times,serif" font-size="14.00"> 10.09s</text>
</a>
</g>
</g>
<!-- N58 -->
<g id="node58" class="node"><title>N58</title>
<g id="a_node58"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString (3.44s)">
<polygon fill="#edecea" stroke="#b2ab9c" points="1493.25,-1691.5 1410.75,-1691.5 1410.75,-1628.5 1493.25,-1628.5 1493.25,-1691.5"/>
<text text-anchor="middle" x="1452" y="-1679.5" font-family="Times,serif" font-size="10.00">go</text>
<text text-anchor="middle" x="1452" y="-1668.5" font-family="Times,serif" font-size="10.00">(*Iterator)</text>
<text text-anchor="middle" x="1452" y="-1657.5" font-family="Times,serif" font-size="10.00">ReadString</text>
<text text-anchor="middle" x="1452" y="-1646.5" font-family="Times,serif" font-size="10.00">0.43s (0.3%)</text>
<text text-anchor="middle" x="1452" y="-1635.5" font-family="Times,serif" font-size="10.00">of 3.44s (2.44%)</text>
</a>
</g>
</g>
<!-- N58&#45;&gt;N29 -->
<g id="edge68" class="edge"><title>N58&#45;&gt;N29</title>
<g id="a_edge68"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString ... runtime.slicebytetostring (1.37s)">
<path fill="none" stroke="#b2b0a9" stroke-dasharray="1,5" d="M1493.32,-1650.2C1520.39,-1642.88 1555.36,-1630.29 1581,-1610 1595.31,-1598.68 1594.33,-1591.47 1604,-1576 1650.24,-1502.06 1650.5,-1476.38 1702,-1406 1805.1,-1265.09 1903.83,-1278.77 1967,-1116 1971.99,-1103.14 2033.91,-1059.51 1962,-864 1959.36,-856.821 1955.34,-849.893 1950.78,-843.531"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="1953.22,-840.967 1944.31,-835.234 1947.7,-845.272 1953.22,-840.967"/>
</a>
</g>
<g id="a_edge68&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString ... runtime.slicebytetostring (1.37s)">
<text text-anchor="middle" x="1916" y="-1230.3" font-family="Times,serif" font-size="14.00"> 1.37s</text>
</a>
</g>
</g>
<!-- N58&#45;&gt;N43 -->
<g id="edge100" class="edge"><title>N58&#45;&gt;N43</title>
<g id="a_edge100"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString ... runtime.growslice (0.52s)">
<path fill="none" stroke="#b2b1af" stroke-dasharray="1,5" d="M1493.63,-1645.86C1534.89,-1629.93 1592,-1598.71 1592,-1548 1592,-1548 1592,-1548 1592,-1233 1592,-1168.69 1570.29,-1153.76 1535,-1100 1528.96,-1090.8 1522.07,-1092.23 1518,-1082 1501.89,-1041.49 1505.14,-1024.2 1522,-984 1524.54,-977.95 1528.02,-972.034 1531.88,-966.5"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="1534.96,-968.239 1538.2,-958.153 1529.37,-964.015 1534.96,-968.239"/>
</a>
</g>
<g id="a_edge100&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString ... runtime.growslice (0.52s)">
<text text-anchor="middle" x="1609" y="-1303.3" font-family="Times,serif" font-size="14.00"> 0.52s</text>
</a>
</g>
</g>
<!-- N59&#45;&gt;N8 -->
<g id="edge50" class="edge"><title>N59&#45;&gt;N8</title>
<g id="a_edge50"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... syscall.Syscall (2.72s)">
<path fill="none" stroke="#b2ada1" stroke-dasharray="1,5" d="M921.91,-902.662C903.541,-887.145 881.64,-866.863 865,-846 841.419,-816.435 848.939,-799.519 823,-772 806.915,-754.935 787.582,-739.324 767.904,-725.616"/>
<polygon fill="#b2ada1" stroke="#b2ada1" points="769.692,-722.599 759.457,-719.859 765.749,-728.384 769.692,-722.599"/>
</a>
</g>
<g id="a_edge50&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... syscall.Syscall (2.72s)">
<text text-anchor="middle" x="882" y="-805.3" font-family="Times,serif" font-size="14.00"> 2.72s</text>
</a>
</g>
</g>
<!-- N59&#45;&gt;N56 -->
<g id="edge112" class="edge"><title>N59&#45;&gt;N56</title>
<g id="a_edge112"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver &#45;&gt; runtime.makeslice (0.30s)">
<path fill="none" stroke="#b2b2b0" d="M997.409,-924.785C1052.09,-915.954 1153.96,-898.753 1240,-880 1269.81,-873.504 1345.03,-858.183 1373,-846 1376.93,-844.286 1380.9,-842.313 1384.82,-840.19"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1386.88,-843.042 1393.8,-835.021 1383.39,-836.974 1386.88,-843.042"/>
</a>
</g>
<g id="a_edge112&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver &#45;&gt; runtime.makeslice (0.30s)">
<text text-anchor="middle" x="1328" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.30s</text>
</a>
</g>
</g>
<!-- N59&#45;&gt;N18 -->
<g id="edge126" class="edge"><title>N59&#45;&gt;N18</title>
<g id="a_edge126"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... [adapter] (0.22s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M957.66,-902.995C958.772,-867.845 966,-807.828 1000,-772 1052.05,-717.149 1097.11,-758.818 1162,-720 1173.73,-712.982 1184.95,-703.119 1194.28,-693.68"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1196.98,-695.922 1201.34,-686.263 1191.91,-691.098 1196.98,-695.922"/>
</a>
</g>
<g id="a_edge126&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... [adapter] (0.22s)">
<text text-anchor="middle" x="1017" y="-805.3" font-family="Times,serif" font-size="14.00"> 0.22s</text>
</a>
</g>
</g>
<!-- N62&#45;&gt;N24 -->
<g id="edge139" class="edge"><title>N62&#45;&gt;N24</title>
<g id="a_edge139"><a xlink:title="encoding/json.(*encodeState).string ... runtime.memmove (0.16s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1686.03,-792.783C1706.12,-784.246 1728.19,-771.677 1743,-754 1786.61,-701.952 1755.81,-667.787 1788,-608 1827.11,-535.364 1892.71,-464.043 1932.12,-424.552"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1935.01,-426.617 1939.64,-417.087 1930.08,-421.65 1935.01,-426.617"/>
</a>
</g>
<g id="a_edge139&#45;label"><a xlink:title="encoding/json.(*encodeState).string ... runtime.memmove (0.16s)">
<text text-anchor="middle" x="1825" y="-578.3" font-family="Times,serif" font-size="14.00"> 0.16s</text>
</a>
</g>
</g>
<!-- N63 -->
<g id="node63" class="node"><title>N63</title>
<g id="a_node63"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 (2.02s)">
<polygon fill="#edeceb" stroke="#b2aea5" points="1052,-1081.5 958,-1081.5 958,-1018.5 1052,-1018.5 1052,-1081.5"/>
<text text-anchor="middle" x="1005" y="-1069.5" font-family="Times,serif" font-size="10.00">log4go</text>
<text text-anchor="middle" x="1005" y="-1058.5" font-family="Times,serif" font-size="10.00">NewFileLogWriter</text>
<text text-anchor="middle" x="1005" y="-1047.5" font-family="Times,serif" font-size="10.00">func1</text>
<text text-anchor="middle" x="1005" y="-1036.5" font-family="Times,serif" font-size="10.00">0.27s (0.19%)</text>
<text text-anchor="middle" x="1005" y="-1025.5" font-family="Times,serif" font-size="10.00">of 2.02s (1.43%)</text>
</a>
</g>
</g>
<!-- N63&#45;&gt;N22 -->
<g id="edge69" class="edge"><title>N63&#45;&gt;N22</title>
<g id="a_edge69"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; runtime.selectgo (1.36s)">
<path fill="none" stroke="#b2b0a9" d="M988.688,-1018.22C980.165,-1005.34 968.469,-991.618 954,-984 898.437,-954.746 732.095,-984.213 672,-966 671.549,-965.863 671.097,-965.722 670.645,-965.577"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="671.774,-962.262 661.182,-962.005 669.301,-968.811 671.774,-962.262"/>
</a>
</g>
<g id="a_edge69&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; runtime.selectgo (1.36s)">
<text text-anchor="middle" x="991" y="-988.3" font-family="Times,serif" font-size="14.00"> 1.36s</text>
</a>
</g>
</g>
<!-- N63&#45;&gt;N18 -->
<g id="edge107" class="edge"><title>N63&#45;&gt;N18</title>
<g id="a_edge107"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; [adapter] (0.37s)">
<path fill="none" stroke="#b2b2b0" d="M1025.27,-1018.24C1028.98,-1012.27 1032.71,-1006 1036,-1000 1044.04,-985.359 1045.52,-981.394 1052,-966 1070.76,-921.408 1053.6,-895.866 1090,-864 1117.09,-840.279 1142.96,-870.878 1169,-846 1210.22,-806.616 1218.46,-736.579 1219.54,-696.145"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1223.04,-696.104 1219.67,-686.058 1216.04,-696.009 1223.04,-696.104"/>
</a>
</g>
<g id="a_edge107&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; [adapter] (0.37s)">
<text text-anchor="middle" x="1107" y="-868.3" font-family="Times,serif" font-size="14.00"> 0.37s</text>
</a>
</g>
</g>
<!-- N64&#45;&gt;N24 -->
<g id="edge90" class="edge"><title>N64&#45;&gt;N24</title>
<g id="a_edge90"><a xlink:title="fmt.(*pp).printArg ... runtime.memmove (0.61s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M2021.09,-777.469C2022.51,-713.132 2021.3,-559.548 1984,-438 1982.89,-434.382 1981.59,-430.658 1980.2,-426.975"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1983.34,-425.406 1976.38,-417.422 1976.84,-428.007 1983.34,-425.406"/>
</a>
</g>
<g id="a_edge90&#45;label"><a xlink:title="fmt.(*pp).printArg ... runtime.memmove (0.61s)">
<text text-anchor="middle" x="2031" y="-578.3" font-family="Times,serif" font-size="14.00"> 0.61s</text>
</a>
</g>
</g>
<!-- N65&#45;&gt;N6 -->
<g id="edge35" class="edge"><title>N65&#45;&gt;N6</title>
<g id="a_edge35"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).user &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add (6.77s)">
<path fill="none" stroke="#b2a187" d="M1229.3,-1630.86C1230.37,-1618.36 1233.64,-1604.21 1242,-1594 1255.65,-1577.34 1267.45,-1585.04 1287,-1576 1287.2,-1575.91 1287.4,-1575.81 1287.6,-1575.72"/>
<polygon fill="#b2a187" stroke="#b2a187" points="1289.31,-1578.79 1296.8,-1571.3 1286.27,-1572.48 1289.31,-1578.79"/>
</a>
</g>
<g id="a_edge35&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).user &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).add (6.77s)">
<text text-anchor="middle" x="1259" y="-1598.3" font-family="Times,serif" font-size="14.00"> 6.77s</text>
</a>
</g>
</g>
<!-- N65&#45;&gt;N52 -->
<g id="edge89" class="edge"><title>N65&#45;&gt;N52</title>
<g id="a_edge89"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).user &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties (0.63s)">
<path fill="none" stroke="#b2b1ae" d="M1191.52,-1638.42C1174.93,-1626.63 1161.13,-1610.85 1170,-1594 1174.11,-1586.19 1180.21,-1579.46 1187.08,-1573.73"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1189.53,-1576.27 1195.43,-1567.47 1185.33,-1570.67 1189.53,-1576.27"/>
</a>
</g>
<g id="a_edge89&#45;label"><a xlink:title="github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.(*TDAnalytics).user &#45;&gt; github.com/ThinkingDataAnalytics/go&#45;sdk/thinkingdata.mergeProperties (0.63s)">
<text text-anchor="middle" x="1187" y="-1598.3" font-family="Times,serif" font-size="14.00"> 0.63s</text>
</a>
</g>
</g>
<!-- N66&#45;&gt;N19 -->
<g id="edge133" class="edge"><title>N66&#45;&gt;N19</title>
<g id="a_edge133"><a xlink:title="runtime.unlock2 ... runtime.futex (0.20s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M547.609,-780.65C547.381,-763.279 547.115,-740.32 547,-720 546.717,-670.223 527.518,-653.807 547,-608 560.04,-577.339 587.96,-586.661 601,-556 621.573,-507.628 614.948,-485.763 593,-438 576,-401.004 572.001,-387.845 539,-364 517.81,-348.689 506.494,-357.467 483,-346 458.85,-334.212 451.69,-331.31 433,-312 411.047,-289.319 413.262,-277.746 395,-252 381.998,-233.67 366.655,-214.035 353.481,-197.713"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="355.835,-195.06 346.814,-189.505 350.402,-199.473 355.835,-195.06"/>
</a>
</g>
<g id="a_edge133&#45;label"><a xlink:title="runtime.unlock2 ... runtime.futex (0.20s)">
<text text-anchor="middle" x="617" y="-442.3" font-family="Times,serif" font-size="14.00"> 0.20s</text>
</a>
</g>
</g>
<!-- N67&#45;&gt;N7 -->
<g id="edge94" class="edge"><title>N67&#45;&gt;N7</title>
<g id="a_edge94"><a xlink:title="runtime.gcWriteBarrier ... runtime.systemstack (0.58s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M813.31,-637.947C731.625,-583.624 545.079,-459.413 539,-454 528.914,-445.018 519.001,-434.079 510.58,-423.957"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="513.194,-421.624 504.172,-416.069 507.76,-426.037 513.194,-421.624"/>
</a>
</g>
<g id="a_edge94&#45;label"><a xlink:title="runtime.gcWriteBarrier ... runtime.systemstack (0.58s)">
<text text-anchor="middle" x="705" y="-510.3" font-family="Times,serif" font-size="14.00"> 0.58s</text>
</a>
</g>
</g>
<!-- N70&#45;&gt;N30 -->
<g id="edge8" class="edge"><title>N70&#45;&gt;N30</title>
<g id="a_edge8"><a xlink:title="os.(*File).Write &#45;&gt; internal/poll.(*FD).Write (22.60s)">
<path fill="none" stroke="#b26023" d="M699.513,-902.967C693.953,-895.781 688.398,-887.855 684,-880 678.769,-870.656 674.207,-860.054 670.426,-849.979"/>
<polygon fill="#b26023" stroke="#b26023" points="673.712,-848.773 667.045,-840.539 667.122,-851.134 673.712,-848.773"/>
</a>
</g>
<g id="a_edge8&#45;label"><a xlink:title="os.(*File).Write &#45;&gt; internal/poll.(*FD).Write (22.60s)">
<text text-anchor="middle" x="704" y="-868.3" font-family="Times,serif" font-size="14.00"> 22.60s</text>
</a>
</g>
</g>
<!-- N71&#45;&gt;N27 -->
<g id="edge18" class="edge"><title>N71&#45;&gt;N27</title>
<g id="a_edge18"><a xlink:title="encoding/json.Marshal ... encoding/json.(*encodeState).reflectValue (13.56s)">
<path fill="none" stroke="#b2885c" stroke-dasharray="1,5" d="M1333.95,-1288.76C1326.98,-1268.89 1315.32,-1235.64 1306.22,-1209.7"/>
<polygon fill="#b2885c" stroke="#b2885c" points="1309.49,-1208.43 1302.88,-1200.16 1302.88,-1210.75 1309.49,-1208.43"/>
</a>
</g>
<g id="a_edge18&#45;label"><a xlink:title="encoding/json.Marshal ... encoding/json.(*encodeState).reflectValue (13.56s)">
<text text-anchor="middle" x="1337" y="-1230.3" font-family="Times,serif" font-size="14.00"> 13.56s</text>
</a>
</g>
</g>
<!-- N71&#45;&gt;N43 -->
<g id="edge95" class="edge"><title>N71&#45;&gt;N43</title>
<g id="a_edge95"><a xlink:title="encoding/json.Marshal &#45;&gt; runtime.growslice (0.57s)">
<path fill="none" stroke="#b2b1ae" d="M1375.66,-1288.89C1417.72,-1267.89 1483.64,-1232.09 1497,-1208 1545.43,-1120.69 1427.51,-1068.93 1480,-984 1489.4,-968.783 1500.64,-975.165 1516,-966 1517.28,-965.234 1518.58,-964.444 1519.88,-963.636"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1521.97,-966.456 1528.48,-958.099 1518.17,-960.571 1521.97,-966.456"/>
</a>
</g>
<g id="a_edge95&#45;label"><a xlink:title="encoding/json.Marshal &#45;&gt; runtime.growslice (0.57s)">
<text text-anchor="middle" x="1514" y="-1104.3" font-family="Times,serif" font-size="14.00"> 0.57s</text>
</a>
</g>
</g>
<!-- N72&#45;&gt;N39 -->
<g id="edge13" class="edge"><title>N72&#45;&gt;N39</title>
<g id="a_edge13"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).Execute &#45;&gt; adapter/service/message/resource.(*ResourceMessage).newResourceMessage (18.18s)">
<path fill="none" stroke="#b2743f" d="M1257,-2086.92C1257,-2073.22 1257,-2054.96 1257,-2038.71"/>
<polygon fill="#b2743f" stroke="#b2743f" points="1260.5,-2038.38 1257,-2028.38 1253.5,-2038.38 1260.5,-2038.38"/>
</a>
</g>
<g id="a_edge13&#45;label"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).Execute &#45;&gt; adapter/service/message/resource.(*ResourceMessage).newResourceMessage (18.18s)">
<text text-anchor="middle" x="1277" y="-2050.3" font-family="Times,serif" font-size="14.00"> 18.18s</text>
</a>
</g>
</g>
<!-- N72&#45;&gt;N42 -->
<g id="edge101" class="edge"><title>N72&#45;&gt;N42</title>
<g id="a_edge101"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.51s)">
<path fill="none" stroke="#b2b1af" d="M1229.67,-2086.92C1210.33,-2071.99 1183.94,-2051.64 1161.57,-2034.38"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="1163.59,-2031.52 1153.53,-2028.18 1159.31,-2037.06 1163.59,-2031.52"/>
</a>
</g>
<g id="a_edge101&#45;label"><a xlink:title="adapter/service/message/resource.(*ResourceMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.51s)">
<text text-anchor="middle" x="1213" y="-2050.3" font-family="Times,serif" font-size="14.00"> 0.51s</text>
</a>
</g>
</g>
<!-- N73&#45;&gt;N3 -->
<g id="edge33" class="edge"><title>N73&#45;&gt;N3</title>
<g id="a_edge33"><a xlink:title="adapter/service/message/resource.(*CurrencyMessage).newCurrencyMessage &#45;&gt; adapter/service/helper.(*MsgRecorder).Write (7.53s)">
<path fill="none" stroke="#b29e82" d="M1399.22,-2079.95C1385.72,-2041.22 1361.33,-1971.26 1346.22,-1927.91"/>
<polygon fill="#b29e82" stroke="#b29e82" points="1349.46,-1926.58 1342.86,-1918.29 1342.85,-1928.89 1349.46,-1926.58"/>
</a>
</g>
<g id="a_edge33&#45;label"><a xlink:title="adapter/service/message/resource.(*CurrencyMessage).newCurrencyMessage &#45;&gt; adapter/service/helper.(*MsgRecorder).Write (7.53s)">
<text text-anchor="middle" x="1399" y="-1995.3" font-family="Times,serif" font-size="14.00"> 7.53s</text>
</a>
</g>
</g>
<!-- N74&#45;&gt;N24 -->
<g id="edge128" class="edge"><title>N74&#45;&gt;N24</title>
<g id="a_edge128"><a xlink:title="time.Time.AppendFormat ... runtime.memmove (0.21s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M2115.7,-774.597C2098.39,-713.634 2059.05,-580.605 2016,-472 2009.91,-456.646 2009.56,-452.123 2001,-438 1998.47,-433.835 1995.61,-429.626 1992.62,-425.542"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1995.24,-423.207 1986.39,-417.385 1989.68,-427.456 1995.24,-423.207"/>
</a>
</g>
<g id="a_edge128&#45;label"><a xlink:title="time.Time.AppendFormat ... runtime.memmove (0.21s)">
<text text-anchor="middle" x="2076" y="-578.3" font-family="Times,serif" font-size="14.00"> 0.21s</text>
</a>
</g>
</g>
<!-- N75 -->
<g id="node75" class="node"><title>N75</title>
<g id="a_node75"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField (5.84s)">
<polygon fill="#edebe8" stroke="#b2a48d" points="1510,-1807.5 1394,-1807.5 1394,-1744.5 1510,-1744.5 1510,-1807.5"/>
<text text-anchor="middle" x="1452" y="-1795.5" font-family="Times,serif" font-size="10.00">go</text>
<text text-anchor="middle" x="1452" y="-1784.5" font-family="Times,serif" font-size="10.00">(*generalStructDecoder)</text>
<text text-anchor="middle" x="1452" y="-1773.5" font-family="Times,serif" font-size="10.00">decodeOneField</text>
<text text-anchor="middle" x="1452" y="-1762.5" font-family="Times,serif" font-size="10.00">0.18s (0.13%)</text>
<text text-anchor="middle" x="1452" y="-1751.5" font-family="Times,serif" font-size="10.00">of 5.84s (4.14%)</text>
</a>
</g>
</g>
<!-- N75&#45;&gt;N58 -->
<g id="edge43" class="edge"><title>N75&#45;&gt;N58</title>
<g id="a_edge43"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField ... github.com/json&#45;iterator/go.(*Iterator).ReadString (3.44s)">
<path fill="none" stroke="#b2ab9c" stroke-dasharray="1,5" d="M1452,-1744.45C1452,-1731.42 1452,-1716.01 1452,-1702.01"/>
<polygon fill="#b2ab9c" stroke="#b2ab9c" points="1455.5,-1701.63 1452,-1691.63 1448.5,-1701.63 1455.5,-1701.63"/>
</a>
</g>
<g id="a_edge43&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField ... github.com/json&#45;iterator/go.(*Iterator).ReadString (3.44s)">
<text text-anchor="middle" x="1469" y="-1714.3" font-family="Times,serif" font-size="14.00"> 3.44s</text>
</a>
</g>
</g>
<!-- N76&#45;&gt;N34 -->
<g id="edge44" class="edge"><title>N76&#45;&gt;N34</title>
<g id="a_edge44"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write &#45;&gt; github.com/ivanabc/log4go.FormatLogRecord (3.40s)">
<path fill="none" stroke="#b2ab9d" d="M1062.64,-1155.72C1108.04,-1139.54 1185.94,-1113.67 1255,-1100 1336.47,-1083.88 1359.06,-1095.52 1441,-1082 1466.4,-1077.81 1494.19,-1071.54 1517.7,-1065.73"/>
<polygon fill="#b2ab9d" stroke="#b2ab9d" points="1518.64,-1069.11 1527.49,-1063.28 1516.94,-1062.32 1518.64,-1069.11"/>
</a>
</g>
<g id="a_edge44&#45;label"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write &#45;&gt; github.com/ivanabc/log4go.FormatLogRecord (3.40s)">
<text text-anchor="middle" x="1272" y="-1104.3" font-family="Times,serif" font-size="14.00"> 3.40s</text>
</a>
</g>
</g>
<!-- N76&#45;&gt;N70 -->
<g id="edge104" class="edge"><title>N76&#45;&gt;N70</title>
<g id="a_edge104"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write ... os.(*File).Write (0.42s)">
<path fill="none" stroke="#b2b2af" stroke-dasharray="1,5" d="M985.72,-1165.44C939.074,-1158.88 858.618,-1144.34 795,-1116 770.787,-1105.21 759.976,-1104.52 746,-1082 725.313,-1048.66 721.318,-1003.22 721.555,-971.315"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="725.06,-971.125 721.779,-961.051 718.062,-970.972 725.06,-971.125"/>
</a>
</g>
<g id="a_edge104&#45;label"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write ... os.(*File).Write (0.42s)">
<text text-anchor="middle" x="763" y="-1046.3" font-family="Times,serif" font-size="14.00"> 0.42s</text>
</a>
</g>
</g>
<!-- N77&#45;&gt;N16 -->
<g id="edge73" class="edge"><title>N77&#45;&gt;N16</title>
<g id="a_edge73"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.schedule (1.09s)">
<path fill="none" stroke="#b2b0ab" d="M42.4551,-639.733C46.6071,-621.192 53.8555,-594.957 65,-574 69.7894,-564.994 76.0591,-556.05 82.4796,-547.952"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="85.2395,-550.107 88.9012,-540.165 79.8388,-545.653 85.2395,-550.107"/>
</a>
</g>
<g id="a_edge73&#45;label"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.schedule (1.09s)">
<text text-anchor="middle" x="82" y="-578.3" font-family="Times,serif" font-size="14.00"> 1.09s</text>
</a>
</g>
</g>
<!-- N77&#45;&gt;N44 -->
<g id="edge102" class="edge"><title>N77&#45;&gt;N44</title>
<g id="a_edge102"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.lock2 (0.46s)">
<path fill="none" stroke="#b2b1af" d="M30.5749,-639.629C14.5322,-584.093 -16.5799,-442.969 53,-364 83.1434,-329.789 112.828,-365.594 154,-346 168.88,-338.919 183.11,-327.968 194.99,-317.171"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="197.57,-319.549 202.441,-310.14 192.766,-314.458 197.57,-319.549"/>
</a>
</g>
<g id="a_edge102&#45;label"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.lock2 (0.46s)">
<text text-anchor="middle" x="34" y="-442.3" font-family="Times,serif" font-size="14.00"> 0.46s</text>
</a>
</g>
</g>
<!-- N78&#45;&gt;N75 -->
<g id="edge37" class="edge"><title>N78&#45;&gt;N75</title>
<g id="a_edge37"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal ... github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField (5.84s)">
<path fill="none" stroke="#b2a48d" stroke-dasharray="1,5" d="M1155.31,-1881.49C1226.39,-1869.38 1372,-1844.38 1377,-1842 1391.29,-1835.19 1404.98,-1824.9 1416.67,-1814.5"/>
<polygon fill="#b2a48d" stroke="#b2a48d" points="1419.07,-1817.04 1424.04,-1807.68 1414.32,-1811.9 1419.07,-1817.04"/>
</a>
</g>
<g id="a_edge37&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal ... github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField (5.84s)">
<text text-anchor="middle" x="1420" y="-1830.3" font-family="Times,serif" font-size="14.00"> 5.84s</text>
</a>
</g>
</g>
<!-- N78&#45;&gt;N18 -->
<g id="edge127" class="edge"><title>N78&#45;&gt;N18</title>
<g id="a_edge127"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal ... [adapter] (0.22s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1110.18,-1859.73C1101.48,-1824.43 1085.58,-1762.33 1069,-1710 1018.14,-1549.42 968.337,-1519.87 939,-1354 931.724,-1312.86 938.137,-1301.77 939,-1260 941.223,-1152.38 882.595,-1102.72 949,-1018 966.7,-995.417 989.726,-1019.25 1011,-1000 1060.12,-955.548 1019.61,-904.548 1072,-864 1095.03,-846.176 1112.9,-864.964 1135,-846 1162.47,-822.429 1153.78,-804.84 1169,-772 1180.96,-746.187 1194.64,-716.923 1204.67,-695.531"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1207.92,-696.838 1209,-686.298 1201.58,-693.866 1207.92,-696.838"/>
</a>
</g>
<g id="a_edge127&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal ... [adapter] (0.22s)">
<text text-anchor="middle" x="956" y="-1303.3" font-family="Times,serif" font-size="14.00"> 0.22s</text>
</a>
</g>
</g>
<!-- N79&#45;&gt;N1 -->
<g id="edge65" class="edge"><title>N79&#45;&gt;N1</title>
<g id="a_edge65"><a xlink:title="reflect.copyVal ... runtime.mallocgc (1.61s)">
<path fill="none" stroke="#b2afa8" stroke-dasharray="1,5" d="M1562.18,-782.924C1580.38,-766.929 1601.85,-744.621 1614,-720 1637.93,-671.509 1644.8,-609.394 1646.39,-566.309"/>
<polygon fill="#b2afa8" stroke="#b2afa8" points="1649.89,-566.153 1646.68,-556.058 1642.9,-565.954 1649.89,-566.153"/>
</a>
</g>
<g id="a_edge65&#45;label"><a xlink:title="reflect.copyVal ... runtime.mallocgc (1.61s)">
<text text-anchor="middle" x="1661" y="-660.3" font-family="Times,serif" font-size="14.00"> 1.61s</text>
</a>
</g>
</g>
<!-- N79&#45;&gt;N18 -->
<g id="edge115" class="edge"><title>N79&#45;&gt;N18</title>
<g id="a_edge115"><a xlink:title="reflect.copyVal &#45;&gt; [adapter] (0.27s)">
<path fill="none" stroke="#b2b2b0" d="M1496.67,-782.888C1490.92,-779.017 1484.89,-775.236 1479,-772 1411.14,-734.706 1327.55,-702.547 1273.78,-683.478"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1274.69,-680.089 1264.1,-680.072 1272.37,-686.692 1274.69,-680.089"/>
</a>
</g>
<g id="a_edge115&#45;label"><a xlink:title="reflect.copyVal &#45;&gt; [adapter] (0.27s)">
<text text-anchor="middle" x="1456" y="-742.3" font-family="Times,serif" font-size="14.00"> 0.27s</text>
</a>
</g>
</g>
<!-- N80&#45;&gt;N49 -->
<g id="edge76" class="edge"><title>N80&#45;&gt;N49</title>
<g id="a_edge76"><a xlink:title="sort.quickSort_func ... runtime.typedmemmove (0.92s)">
<path fill="none" stroke="#b2b1ac" stroke-dasharray="1,5" d="M1089.82,-782.765C1099.81,-730.517 1122.77,-610.375 1134.23,-550.402"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="1137.73,-550.757 1136.17,-540.278 1130.85,-549.443 1137.73,-550.757"/>
</a>
</g>
<g id="a_edge76&#45;label"><a xlink:title="sort.quickSort_func ... runtime.typedmemmove (0.92s)">
<text text-anchor="middle" x="1141" y="-660.3" font-family="Times,serif" font-size="14.00"> 0.92s</text>
</a>
</g>
</g>
</g>
</g></svg>
