<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.30.1 (20201013.1554)
 -->
<!-- Title: adapter Pages: 1 -->
<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<script type="text/ecmascript"><![CDATA[
/**
 *  SVGPan library 1.2.2
 * ======================
 *
 * Given an unique existing element with id "viewport" (or when missing, the
 * first g-element), including the library into any SVG adds the following
 * capabilities:
 *
 *  - Mouse panning
 *  - Mouse zooming (using the wheel)
 *  - Object dragging
 *
 * You can configure the behaviour of the pan/zoom/drag with the variables
 * listed in the CONFIGURATION section of this file.
 *
 * Known issues:
 *
 *  - Zooming (while panning) on Safari has still some issues
 *
 * Releases:
 *
 * 1.2.2, Tue Aug 30 17:21:56 CEST 2011, Andrea Leofreddi
 *	- Fixed viewBox on root tag (#7)
 *	- Improved zoom speed (#2)
 *
 * 1.2.1, Mon Jul  4 00:33:18 CEST 2011, Andrea Leofreddi
 *	- Fixed a regression with mouse wheel (now working on Firefox 5)
 *	- Working with viewBox attribute (#4)
 *	- Added "use strict;" and fixed resulting warnings (#5)
 *	- Added configuration variables, dragging is disabled by default (#3)
 *
 * 1.2, Sat Mar 20 08:42:50 GMT 2010, Zeng Xiaohui
 *	Fixed a bug with browser mouse handler interaction
 *
 * 1.1, Wed Feb  3 17:39:33 GMT 2010, Zeng Xiaohui
 *	Updated the zoom code to support the mouse wheel on Safari/Chrome
 *
 * 1.0, Andrea Leofreddi
 *	First release
 *
 * This code is licensed under the following BSD license:
 *
 * Copyright 2009-2017 Andrea Leofreddi <<EMAIL>>. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are
 * permitted provided that the following conditions are met:
 *
 *    1. Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *    2. Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *    3. Neither the name of the copyright holder nor the names of its
 *       contributors may be used to endorse or promote products derived from
 *       this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY COPYRIGHT HOLDERS AND CONTRIBUTORS ''AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
 * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are those of the
 * authors and should not be interpreted as representing official policies, either expressed
 * or implied, of Andrea Leofreddi.
 */

"use strict";

/// CONFIGURATION
/// ====>

var enablePan = 1; // 1 or 0: enable or disable panning (default enabled)
var enableZoom = 1; // 1 or 0: enable or disable zooming (default enabled)
var enableDrag = 0; // 1 or 0: enable or disable dragging (default disabled)
var zoomScale = 0.2; // Zoom sensitivity

/// <====
/// END OF CONFIGURATION

var root = document.documentElement;

var state = 'none', svgRoot = null, stateTarget, stateOrigin, stateTf;

setupHandlers(root);

/**
 * Register handlers
 */
function setupHandlers(root){
	setAttributes(root, {
		"onmouseup" : "handleMouseUp(evt)",
		"onmousedown" : "handleMouseDown(evt)",
		"onmousemove" : "handleMouseMove(evt)",
		//"onmouseout" : "handleMouseUp(evt)", // Decomment this to stop the pan functionality when dragging out of the SVG element
	});

	if(navigator.userAgent.toLowerCase().indexOf('webkit') >= 0)
		window.addEventListener('mousewheel', handleMouseWheel, false); // Chrome/Safari
	else
		window.addEventListener('DOMMouseScroll', handleMouseWheel, false); // Others
}

/**
 * Retrieves the root element for SVG manipulation. The element is then cached into the svgRoot global variable.
 */
function getRoot(root) {
	if(svgRoot == null) {
		var r = root.getElementById("viewport") ? root.getElementById("viewport") : root.documentElement, t = r;

		while(t != root) {
			if(t.getAttribute("viewBox")) {
				setCTM(r, t.getCTM());

				t.removeAttribute("viewBox");
			}

			t = t.parentNode;
		}

		svgRoot = r;
	}

	return svgRoot;
}

/**
 * Instance an SVGPoint object with given event coordinates.
 */
function getEventPoint(evt) {
	var p = root.createSVGPoint();

	p.x = evt.clientX;
	p.y = evt.clientY;

	return p;
}

/**
 * Sets the current transform matrix of an element.
 */
function setCTM(element, matrix) {
	var s = "matrix(" + matrix.a + "," + matrix.b + "," + matrix.c + "," + matrix.d + "," + matrix.e + "," + matrix.f + ")";

	element.setAttribute("transform", s);
}

/**
 * Dumps a matrix to a string (useful for debug).
 */
function dumpMatrix(matrix) {
	var s = "[ " + matrix.a + ", " + matrix.c + ", " + matrix.e + "\n  " + matrix.b + ", " + matrix.d + ", " + matrix.f + "\n  0, 0, 1 ]";

	return s;
}

/**
 * Sets attributes of an element.
 */
function setAttributes(element, attributes){
	for (var i in attributes)
		element.setAttributeNS(null, i, attributes[i]);
}

/**
 * Handle mouse wheel event.
 */
function handleMouseWheel(evt) {
	if(!enableZoom)
		return;

	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	var delta;

	if(evt.wheelDelta)
		delta = evt.wheelDelta / 360; // Chrome/Safari
	else
		delta = evt.detail / -9; // Mozilla

	var z = Math.pow(1 + zoomScale, delta);

	var g = getRoot(svgDoc);
	
	var p = getEventPoint(evt);

	p = p.matrixTransform(g.getCTM().inverse());

	// Compute new scale matrix in current mouse position
	var k = root.createSVGMatrix().translate(p.x, p.y).scale(z).translate(-p.x, -p.y);

        setCTM(g, g.getCTM().multiply(k));

	if(typeof(stateTf) == "undefined")
		stateTf = g.getCTM().inverse();

	stateTf = stateTf.multiply(k.inverse());
}

/**
 * Handle mouse move event.
 */
function handleMouseMove(evt) {
	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	var g = getRoot(svgDoc);

	if(state == 'pan' && enablePan) {
		// Pan mode
		var p = getEventPoint(evt).matrixTransform(stateTf);

		setCTM(g, stateTf.inverse().translate(p.x - stateOrigin.x, p.y - stateOrigin.y));
	} else if(state == 'drag' && enableDrag) {
		// Drag mode
		var p = getEventPoint(evt).matrixTransform(g.getCTM().inverse());

		setCTM(stateTarget, root.createSVGMatrix().translate(p.x - stateOrigin.x, p.y - stateOrigin.y).multiply(g.getCTM().inverse()).multiply(stateTarget.getCTM()));

		stateOrigin = p;
	}
}

/**
 * Handle click event.
 */
function handleMouseDown(evt) {
	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	var g = getRoot(svgDoc);

	if(
		evt.target.tagName == "svg"
		|| !enableDrag // Pan anyway when drag is disabled and the user clicked on an element
	) {
		// Pan mode
		state = 'pan';

		stateTf = g.getCTM().inverse();

		stateOrigin = getEventPoint(evt).matrixTransform(stateTf);
	} else {
		// Drag mode
		state = 'drag';

		stateTarget = evt.target;

		stateTf = g.getCTM().inverse();

		stateOrigin = getEventPoint(evt).matrixTransform(stateTf);
	}
}

/**
 * Handle mouse button release event.
 */
function handleMouseUp(evt) {
	if(evt.preventDefault)
		evt.preventDefault();

	evt.returnValue = false;

	var svgDoc = evt.target.ownerDocument;

	if(state == 'pan' || state == 'drag') {
		// Quit pan mode
		state = '';
	}
}
]]></script><g id="viewport" transform="scale(0.5,0.5) translate(0,0)"><g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 2718)">
<title>adapter</title>
<polygon fill="white" stroke="white" points="-4,5 -4,-2718 2843,-2718 2843,5 -4,5"/>
<g id="clust1" class="cluster"><title>cluster_L</title>
<polygon fill="none" stroke="black" points="862,-2482 862,-2706 1296,-2706 1296,-2482 862,-2482"/>
</g>
<!-- File: adapter -->
<g id="node1" class="node"><title>File: adapter</title>
<g id="a_node1"><a xlink:title="adapter">
<polygon fill="#f8f8f8" stroke="black" points="1288,-2697.5 870,-2697.5 870,-2490.5 1288,-2490.5 1288,-2697.5"/>
<text text-anchor="start" x="878" y="-2680.7" font-family="Times,serif" font-size="16.00">File: adapter</text>
<text text-anchor="start" x="878" y="-2662.7" font-family="Times,serif" font-size="16.00">Build ID: a92d37f073fca23940ca35b7759655dcb0c947f2</text>
<text text-anchor="start" x="878" y="-2644.7" font-family="Times,serif" font-size="16.00">Type: cpu</text>
<text text-anchor="start" x="878" y="-2626.7" font-family="Times,serif" font-size="16.00">Time: Nov 20, 2021 at 8:15pm (CST)</text>
<text text-anchor="start" x="878" y="-2608.7" font-family="Times,serif" font-size="16.00">Duration: 30.13s, Total samples = 129.40s (429.43%)</text>
<text text-anchor="start" x="878" y="-2590.7" font-family="Times,serif" font-size="16.00">Showing nodes accounting for 90.10s, 69.63% of 129.40s total</text>
<text text-anchor="start" x="878" y="-2572.7" font-family="Times,serif" font-size="16.00">Dropped 453 nodes (cum &lt;= 0.65s)</text>
<text text-anchor="start" x="878" y="-2554.7" font-family="Times,serif" font-size="16.00">Dropped 139 edges (freq &lt;= 0.13s)</text>
<text text-anchor="start" x="878" y="-2536.7" font-family="Times,serif" font-size="16.00">Showing top 80 nodes out of 198</text>
<text text-anchor="start" x="878" y="-2499.7" font-family="Times,serif" font-size="16.00">See https://git.io/JfYMW for how to read the graph</text>
</a>
</g>
</g>
<!-- N1 -->
<g id="node1" class="node"><title>N1</title>
<g id="a_node1"><a xlink:title="runtime.mallocgc (18.03s)">
<polygon fill="#ede4dc" stroke="#b26d36" points="1441,-558 1271,-558 1271,-462 1441,-462 1441,-558"/>
<text text-anchor="middle" x="1356" y="-538" font-family="Times,serif" font-size="20.00">runtime</text>
<text text-anchor="middle" x="1356" y="-516" font-family="Times,serif" font-size="20.00">mallocgc</text>
<text text-anchor="middle" x="1356" y="-494" font-family="Times,serif" font-size="20.00">8.61s (6.65%)</text>
<text text-anchor="middle" x="1356" y="-472" font-family="Times,serif" font-size="20.00">of 18.03s (13.93%)</text>
</a>
</g>
</g>
<!-- N5 -->
<g id="node5" class="node"><title>N5</title>
<g id="a_node5"><a xlink:title="runtime.systemstack (15.87s)">
<polygon fill="#ede5de" stroke="#b27845" points="2374,-409 2280,-409 2280,-357 2374,-357 2374,-409"/>
<text text-anchor="middle" x="2327" y="-397" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="2327" y="-386" font-family="Times,serif" font-size="10.00">systemstack</text>
<text text-anchor="middle" x="2327" y="-375" font-family="Times,serif" font-size="10.00">0.10s (0.077%)</text>
<text text-anchor="middle" x="2327" y="-364" font-family="Times,serif" font-size="10.00">of 15.87s (12.26%)</text>
</a>
</g>
</g>
<!-- N1&#45;&gt;N5 -->
<g id="edge36" class="edge"><title>N1&#45;&gt;N5</title>
<g id="a_edge36"><a xlink:title="runtime.mallocgc ... runtime.systemstack (3.58s)">
<path fill="none" stroke="#b2aa99" stroke-dasharray="1,5" d="M1441.32,-498.017C1635.36,-473.037 2105.57,-412.505 2269.49,-391.404"/>
<polygon fill="#b2aa99" stroke="#b2aa99" points="2270.37,-394.82 2279.84,-390.072 2269.47,-387.877 2270.37,-394.82"/>
</a>
</g>
<g id="a_edge36&#45;label"><a xlink:title="runtime.mallocgc ... runtime.systemstack (3.58s)">
<text text-anchor="middle" x="2003" y="-432.3" font-family="Times,serif" font-size="14.00"> 3.58s</text>
</a>
</g>
</g>
<!-- N65 -->
<g id="node65" class="node"><title>N65</title>
<g id="a_node65"><a xlink:title="runtime.heapBitsSetType (2.04s)">
<polygon fill="#edeceb" stroke="#b2aea4" points="1409.25,-409.5 1302.75,-409.5 1302.75,-356.5 1409.25,-356.5 1409.25,-409.5"/>
<text text-anchor="middle" x="1356" y="-394.3" font-family="Times,serif" font-size="14.00">runtime</text>
<text text-anchor="middle" x="1356" y="-379.3" font-family="Times,serif" font-size="14.00">heapBitsSetType</text>
<text text-anchor="middle" x="1356" y="-364.3" font-family="Times,serif" font-size="14.00">2.04s (1.58%)</text>
</a>
</g>
</g>
<!-- N1&#45;&gt;N65 -->
<g id="edge56" class="edge"><title>N1&#45;&gt;N65</title>
<g id="a_edge56"><a xlink:title="runtime.mallocgc &#45;&gt; runtime.heapBitsSetType (2.04s)">
<path fill="none" stroke="#b2aea4" d="M1356,-461.743C1356,-447.777 1356,-432.73 1356,-419.629"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="1359.5,-419.597 1356,-409.597 1352.5,-419.597 1359.5,-419.597"/>
</a>
</g>
<g id="a_edge56&#45;label"><a xlink:title="runtime.mallocgc &#45;&gt; runtime.heapBitsSetType (2.04s)">
<text text-anchor="middle" x="1373" y="-432.3" font-family="Times,serif" font-size="14.00"> 2.04s</text>
</a>
</g>
</g>
<!-- N52 -->
<g id="node52" class="node"><title>N52</title>
<g id="a_node52"><a xlink:title="runtime.memclrNoHeapPointers (1.79s)">
<polygon fill="#edeceb" stroke="#b2afa6" points="1284,-409.5 1136,-409.5 1136,-356.5 1284,-356.5 1284,-409.5"/>
<text text-anchor="middle" x="1210" y="-394.3" font-family="Times,serif" font-size="14.00">runtime</text>
<text text-anchor="middle" x="1210" y="-379.3" font-family="Times,serif" font-size="14.00">memclrNoHeapPointers</text>
<text text-anchor="middle" x="1210" y="-364.3" font-family="Times,serif" font-size="14.00">1.79s (1.38%)</text>
</a>
</g>
</g>
<!-- N1&#45;&gt;N52 -->
<g id="edge65" class="edge"><title>N1&#45;&gt;N52</title>
<g id="a_edge65"><a xlink:title="runtime.mallocgc ... runtime.memclrNoHeapPointers (1.47s)">
<path fill="none" stroke="#b2afa8" stroke-dasharray="1,5" d="M1300.8,-461.743C1283.04,-446.533 1263.77,-430.04 1247.58,-416.175"/>
<polygon fill="#b2afa8" stroke="#b2afa8" points="1249.77,-413.442 1239.9,-409.597 1245.22,-418.76 1249.77,-413.442"/>
</a>
</g>
<g id="a_edge65&#45;label"><a xlink:title="runtime.mallocgc ... runtime.memclrNoHeapPointers (1.47s)">
<text text-anchor="middle" x="1296" y="-432.3" font-family="Times,serif" font-size="14.00"> 1.47s</text>
</a>
</g>
</g>
<!-- N2 -->
<g id="node2" class="node"><title>N2</title>
<g id="a_node2"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process (63.97s)">
<polygon fill="#edd9d5" stroke="#b22200" points="1404,-2438 1292,-2438 1292,-2370 1404,-2370 1404,-2438"/>
<text text-anchor="middle" x="1348" y="-2425.2" font-family="Times,serif" font-size="11.00">kafka_consumer</text>
<text text-anchor="middle" x="1348" y="-2413.2" font-family="Times,serif" font-size="11.00">(*PartitionConsumer)</text>
<text text-anchor="middle" x="1348" y="-2401.2" font-family="Times,serif" font-size="11.00">Process</text>
<text text-anchor="middle" x="1348" y="-2389.2" font-family="Times,serif" font-size="11.00">0.28s (0.22%)</text>
<text text-anchor="middle" x="1348" y="-2377.2" font-family="Times,serif" font-size="11.00">of 63.97s (49.44%)</text>
</a>
</g>
</g>
<!-- N7 -->
<g id="node7" class="node"><title>N7</title>
<g id="a_node7"><a xlink:title="adapter/service/wm_logger.(*Logger).Write (39.33s)">
<polygon fill="#eddcd5" stroke="#b23500" points="1395,-2317.5 1301,-2317.5 1301,-2254.5 1395,-2254.5 1395,-2317.5"/>
<text text-anchor="middle" x="1348" y="-2305.5" font-family="Times,serif" font-size="10.00">wm_logger</text>
<text text-anchor="middle" x="1348" y="-2294.5" font-family="Times,serif" font-size="10.00">(*Logger)</text>
<text text-anchor="middle" x="1348" y="-2283.5" font-family="Times,serif" font-size="10.00">Write</text>
<text text-anchor="middle" x="1348" y="-2272.5" font-family="Times,serif" font-size="10.00">0.15s (0.12%)</text>
<text text-anchor="middle" x="1348" y="-2261.5" font-family="Times,serif" font-size="10.00">of 39.33s (30.39%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N7 -->
<g id="edge2" class="edge"><title>N2&#45;&gt;N7</title>
<g id="a_edge2"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/wm_logger.(*Logger).Write (39.33s)">
<path fill="none" stroke="#b23500" stroke-width="2" d="M1348,-2369.76C1348,-2356.74 1348,-2341.68 1348,-2327.99"/>
<polygon fill="#b23500" stroke="#b23500" points="1351.5,-2327.86 1348,-2317.86 1344.5,-2327.86 1351.5,-2327.86"/>
</a>
</g>
<g id="a_edge2&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/wm_logger.(*Logger).Write (39.33s)">
<text text-anchor="middle" x="1368" y="-2340.3" font-family="Times,serif" font-size="14.00"> 39.33s</text>
</a>
</g>
</g>
<!-- N20 -->
<g id="node20" class="node"><title>N20</title>
<g id="a_node20"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset (22.75s)">
<polygon fill="#ede0d8" stroke="#b25515" points="1667,-2315 1573,-2315 1573,-2257 1667,-2257 1667,-2315"/>
<text text-anchor="middle" x="1620" y="-2303.8" font-family="Times,serif" font-size="9.00">kafka_consumer</text>
<text text-anchor="middle" x="1620" y="-2293.8" font-family="Times,serif" font-size="9.00">(*PartitionConsumer)</text>
<text text-anchor="middle" x="1620" y="-2283.8" font-family="Times,serif" font-size="9.00">SetOffset</text>
<text text-anchor="middle" x="1620" y="-2273.8" font-family="Times,serif" font-size="9.00">0.05s (0.039%)</text>
<text text-anchor="middle" x="1620" y="-2263.8" font-family="Times,serif" font-size="9.00">of 22.75s (17.58%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N20 -->
<g id="edge4" class="edge"><title>N2&#45;&gt;N20</title>
<g id="a_edge4"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset (22.75s)">
<path fill="none" stroke="#b25515" d="M1404.14,-2398.29C1448.66,-2392.55 1511.12,-2379.87 1559,-2352 1571.7,-2344.61 1583.37,-2333.76 1593.03,-2323.03"/>
<polygon fill="#b25515" stroke="#b25515" points="1595.82,-2325.15 1599.69,-2315.29 1590.51,-2320.59 1595.82,-2325.15"/>
</a>
</g>
<g id="a_edge4&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset (22.75s)">
<text text-anchor="middle" x="1599" y="-2340.3" font-family="Times,serif" font-size="14.00"> 22.75s</text>
</a>
</g>
</g>
<!-- N16 -->
<g id="node16" class="node"><title>N16</title>
<g id="a_node16"><a xlink:title="runtime.selectgo (5.04s)">
<polygon fill="#edebe8" stroke="#b2a58f" points="2494,-1092 2384,-1092 2384,-1024 2494,-1024 2494,-1092"/>
<text text-anchor="middle" x="2439" y="-1076.8" font-family="Times,serif" font-size="14.00">runtime</text>
<text text-anchor="middle" x="2439" y="-1061.8" font-family="Times,serif" font-size="14.00">selectgo</text>
<text text-anchor="middle" x="2439" y="-1046.8" font-family="Times,serif" font-size="14.00">2.17s (1.68%)</text>
<text text-anchor="middle" x="2439" y="-1031.8" font-family="Times,serif" font-size="14.00">of 5.04s (3.89%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N16 -->
<g id="edge97" class="edge"><title>N2&#45;&gt;N16</title>
<g id="a_edge97"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.selectgo (0.59s)">
<path fill="none" stroke="#b2b1ae" d="M1404.24,-2403.5C1640.32,-2404.87 2539,-2402.5 2539,-2287 2539,-2287 2539,-2287 2539,-1185 2539,-1151.23 2516.69,-1121.25 2493.15,-1099.02"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2495.25,-1096.2 2485.49,-1092.08 2490.55,-1101.38 2495.25,-1096.2"/>
</a>
</g>
<g id="a_edge97&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.selectgo (0.59s)">
<text text-anchor="middle" x="2556" y="-1714.3" font-family="Times,serif" font-size="14.00"> 0.59s</text>
</a>
</g>
</g>
<!-- N43 -->
<g id="node43" class="node"><title>N43</title>
<g id="a_node43"><a xlink:title="runtime.convTstring (2.19s)">
<polygon fill="#edeceb" stroke="#b2aea3" points="2141.25,-1084 2058.75,-1084 2058.75,-1032 2141.25,-1032 2141.25,-1084"/>
<text text-anchor="middle" x="2100" y="-1072" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="2100" y="-1061" font-family="Times,serif" font-size="10.00">convTstring</text>
<text text-anchor="middle" x="2100" y="-1050" font-family="Times,serif" font-size="10.00">0.07s (0.054%)</text>
<text text-anchor="middle" x="2100" y="-1039" font-family="Times,serif" font-size="10.00">of 2.19s (1.69%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N43 -->
<g id="edge119" class="edge"><title>N2&#45;&gt;N43</title>
<g id="a_edge119"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.convTstring (0.33s)">
<path fill="none" stroke="#b2b2b0" d="M1404.16,-2401.99C1606.31,-2397.61 2285,-2376.28 2285,-2287 2285,-2287 2285,-2287 2285,-1947 2285,-1880.46 2343.49,-1882.2 2361,-1818 2425.02,-1583.22 2396.51,-1503.01 2320,-1272 2310.66,-1243.79 2281.3,-1173.7 2261,-1152 2230.37,-1119.25 2185.56,-1094.65 2150.99,-1079.01"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="2152.09,-1075.67 2141.53,-1074.83 2149.27,-1082.07 2152.09,-1075.67"/>
</a>
</g>
<g id="a_edge119&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.convTstring (0.33s)">
<text text-anchor="middle" x="2403" y="-1714.3" font-family="Times,serif" font-size="14.00"> 0.33s</text>
</a>
</g>
</g>
<!-- N18 -->
<g id="node18" class="node"><title>N18</title>
<g id="a_node18"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf (5.60s)">
<polygon fill="#edebe8" stroke="#b2a38b" points="1504,-1220 1414,-1220 1414,-1152 1504,-1152 1504,-1220"/>
<text text-anchor="middle" x="1459" y="-1207.2" font-family="Times,serif" font-size="11.00">log4go</text>
<text text-anchor="middle" x="1459" y="-1195.2" font-family="Times,serif" font-size="11.00">Logger</text>
<text text-anchor="middle" x="1459" y="-1183.2" font-family="Times,serif" font-size="11.00">intLogf</text>
<text text-anchor="middle" x="1459" y="-1171.2" font-family="Times,serif" font-size="11.00">0.40s (0.31%)</text>
<text text-anchor="middle" x="1459" y="-1159.2" font-family="Times,serif" font-size="11.00">of 5.60s (4.33%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N18 -->
<g id="edge125" class="edge"><title>N2&#45;&gt;N18</title>
<g id="a_edge125"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process ... github.com/ivanabc/log4go.Logger.intLogf (0.30s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1379.17,-2369.87C1383.79,-2364.14 1388.25,-2358.07 1392,-2352 1493.84,-2187.22 1558,-2142.71 1558,-1949 1558,-1949 1558,-1949 1558,-1780 1558,-1663.18 1562.21,-1633.61 1579,-1518 1582.4,-1494.62 1587.27,-1489.46 1590,-1466 1601.73,-1365.32 1658.52,-1316.98 1595,-1238 1573.44,-1211.2 1551.36,-1230.34 1513.96,-1219.48"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1514.7,-1216.03 1504.11,-1216.02 1512.38,-1222.63 1514.7,-1216.03"/>
</a>
</g>
<g id="a_edge125&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process ... github.com/ivanabc/log4go.Logger.intLogf (0.30s)">
<text text-anchor="middle" x="1575" y="-1777.3" font-family="Times,serif" font-size="14.00"> 0.30s</text>
</a>
</g>
</g>
<!-- N76 -->
<g id="node76" class="node"><title>N76</title>
<g id="a_node76"><a xlink:title="runtime.convT64 (1.13s)">
<polygon fill="#edecec" stroke="#b2b0aa" points="2252.25,-1212 2169.75,-1212 2169.75,-1160 2252.25,-1160 2252.25,-1212"/>
<text text-anchor="middle" x="2211" y="-1200" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="2211" y="-1189" font-family="Times,serif" font-size="10.00">convT64</text>
<text text-anchor="middle" x="2211" y="-1178" font-family="Times,serif" font-size="10.00">0.14s (0.11%)</text>
<text text-anchor="middle" x="2211" y="-1167" font-family="Times,serif" font-size="10.00">of 1.13s (0.87%)</text>
</a>
</g>
</g>
<!-- N2&#45;&gt;N76 -->
<g id="edge150" class="edge"><title>N2&#45;&gt;N76</title>
<g id="a_edge150"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.convT64 (0.13s)">
<path fill="none" stroke="#b2b2b1" d="M1404.13,-2403.53C1597.33,-2404.87 2227.01,-2405.08 2299,-2352 2323.79,-2333.72 2323,-2317.8 2323,-2287 2323,-2287 2323,-2287 2323,-1371 2323,-1310.85 2278.79,-1253.65 2245.92,-1219.44"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="2248.19,-1216.76 2238.69,-1212.08 2243.2,-1221.66 2248.19,-1216.76"/>
</a>
</g>
<g id="a_edge150&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).Process &#45;&gt; runtime.convT64 (0.13s)">
<text text-anchor="middle" x="2340" y="-1777.3" font-family="Times,serif" font-size="14.00"> 0.13s</text>
</a>
</g>
</g>
<!-- N3 -->
<g id="node3" class="node"><title>N3</title>
<g id="a_node3"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher (22.84s)">
<polygon fill="#ede0d8" stroke="#b25414" points="1395,-2177.5 1301,-2177.5 1301,-2114.5 1395,-2114.5 1395,-2177.5"/>
<text text-anchor="middle" x="1348" y="-2165.5" font-family="Times,serif" font-size="10.00">wm_logger</text>
<text text-anchor="middle" x="1348" y="-2154.5" font-family="Times,serif" font-size="10.00">(*MessageM)</text>
<text text-anchor="middle" x="1348" y="-2143.5" font-family="Times,serif" font-size="10.00">Dispatcher</text>
<text text-anchor="middle" x="1348" y="-2132.5" font-family="Times,serif" font-size="10.00">0.14s (0.11%)</text>
<text text-anchor="middle" x="1348" y="-2121.5" font-family="Times,serif" font-size="10.00">of 22.84s (17.65%)</text>
</a>
</g>
</g>
<!-- N74 -->
<g id="node74" class="node"><title>N74</title>
<g id="a_node74"><a xlink:title="adapter/service/wm_logger.(*MessageM).GetMessage (5.64s)">
<polygon fill="#edebe7" stroke="#b2a38b" points="1389.25,-2037.5 1306.75,-2037.5 1306.75,-1974.5 1389.25,-1974.5 1389.25,-2037.5"/>
<text text-anchor="middle" x="1348" y="-2025.5" font-family="Times,serif" font-size="10.00">wm_logger</text>
<text text-anchor="middle" x="1348" y="-2014.5" font-family="Times,serif" font-size="10.00">(*MessageM)</text>
<text text-anchor="middle" x="1348" y="-2003.5" font-family="Times,serif" font-size="10.00">GetMessage</text>
<text text-anchor="middle" x="1348" y="-1992.5" font-family="Times,serif" font-size="10.00">0.14s (0.11%)</text>
<text text-anchor="middle" x="1348" y="-1981.5" font-family="Times,serif" font-size="10.00">of 5.64s (4.36%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N74 -->
<g id="edge23" class="edge"><title>N3&#45;&gt;N74</title>
<g id="a_edge23"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*MessageM).GetMessage (5.64s)">
<path fill="none" stroke="#b2a38b" d="M1348,-2114.37C1348,-2094.72 1348,-2068.98 1348,-2047.62"/>
<polygon fill="#b2a38b" stroke="#b2a38b" points="1351.5,-2047.54 1348,-2037.54 1344.5,-2047.54 1351.5,-2047.54"/>
</a>
</g>
<g id="a_edge23&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*MessageM).GetMessage (5.64s)">
<text text-anchor="middle" x="1365" y="-2060.3" font-family="Times,serif" font-size="14.00"> 5.64s</text>
</a>
</g>
</g>
<!-- N9 -->
<g id="node9" class="node"><title>N9</title>
<g id="a_node9"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog (7.32s)">
<polygon fill="#edeae6" stroke="#b29d80" points="1179.25,-1922 1090.75,-1922 1090.75,-1870 1179.25,-1870 1179.25,-1922"/>
<text text-anchor="middle" x="1135" y="-1910" font-family="Times,serif" font-size="10.00">wm_logger</text>
<text text-anchor="middle" x="1135" y="-1899" font-family="Times,serif" font-size="10.00">GetBaseFromLog</text>
<text text-anchor="middle" x="1135" y="-1888" font-family="Times,serif" font-size="10.00">0.12s (0.093%)</text>
<text text-anchor="middle" x="1135" y="-1877" font-family="Times,serif" font-size="10.00">of 7.32s (5.66%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N9 -->
<g id="edge34" class="edge"><title>N3&#45;&gt;N9</title>
<g id="a_edge34"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... adapter/service/wm_logger.GetBaseFromLog (4.09s)">
<path fill="none" stroke="#b2a896" stroke-dasharray="1,5" d="M1300.85,-2134.55C1252.49,-2121.11 1179.49,-2092.68 1144,-2038 1123.69,-2006.71 1124.33,-1962.84 1128.2,-1932.38"/>
<polygon fill="#b2a896" stroke="#b2a896" points="1131.71,-1932.6 1129.68,-1922.2 1124.78,-1931.6 1131.71,-1932.6"/>
</a>
</g>
<g id="a_edge34&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... adapter/service/wm_logger.GetBaseFromLog (4.09s)">
<text text-anchor="middle" x="1161" y="-2002.3" font-family="Times,serif" font-size="14.00"> 4.09s</text>
</a>
</g>
</g>
<!-- N8 -->
<g id="node8" class="node"><title>N8</title>
<g id="a_node8"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (22.65s)">
<polygon fill="#ede0d8" stroke="#b25516" points="734,-1812.5 640,-1812.5 640,-1749.5 734,-1749.5 734,-1812.5"/>
<text text-anchor="middle" x="687" y="-1800.5" font-family="Times,serif" font-size="10.00">go</text>
<text text-anchor="middle" x="687" y="-1789.5" font-family="Times,serif" font-size="10.00">(*frozenConfig)</text>
<text text-anchor="middle" x="687" y="-1778.5" font-family="Times,serif" font-size="10.00">Unmarshal</text>
<text text-anchor="middle" x="687" y="-1767.5" font-family="Times,serif" font-size="10.00">0.07s (0.054%)</text>
<text text-anchor="middle" x="687" y="-1756.5" font-family="Times,serif" font-size="10.00">of 22.65s (17.50%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N8 -->
<g id="edge46" class="edge"><title>N3&#45;&gt;N8</title>
<g id="a_edge46"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (2.57s)">
<path fill="none" stroke="#b2ada0" stroke-dasharray="1,5" d="M1300.98,-2141.96C1206.87,-2135.03 999.693,-2115.18 945,-2072 907.667,-2042.53 928.271,-2013.65 902,-1974 895.922,-1964.83 891.843,-1964.62 885,-1956 856.486,-1920.09 860.865,-1901.98 828,-1870 803.288,-1845.95 770.925,-1825.18 743.439,-1809.87"/>
<polygon fill="#b2ada0" stroke="#b2ada0" points="744.776,-1806.61 734.322,-1804.89 741.419,-1812.75 744.776,-1806.61"/>
</a>
</g>
<g id="a_edge46&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (2.57s)">
<text text-anchor="middle" x="902" y="-1944.3" font-family="Times,serif" font-size="14.00"> 2.57s</text>
</a>
</g>
</g>
<!-- N56 -->
<g id="node56" class="node"><title>N56</title>
<g id="a_node56"><a xlink:title="adapter/service/wm_logger.(*GainItemMessage).Execute (2.50s)">
<polygon fill="#edecea" stroke="#b2ada1" points="1288.25,-2037.5 1187.75,-2037.5 1187.75,-1974.5 1288.25,-1974.5 1288.25,-2037.5"/>
<text text-anchor="middle" x="1238" y="-2025.5" font-family="Times,serif" font-size="10.00">wm_logger</text>
<text text-anchor="middle" x="1238" y="-2014.5" font-family="Times,serif" font-size="10.00">(*GainItemMessage)</text>
<text text-anchor="middle" x="1238" y="-2003.5" font-family="Times,serif" font-size="10.00">Execute</text>
<text text-anchor="middle" x="1238" y="-1992.5" font-family="Times,serif" font-size="10.00">0.11s (0.085%)</text>
<text text-anchor="middle" x="1238" y="-1981.5" font-family="Times,serif" font-size="10.00">of 2.50s (1.93%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N56 -->
<g id="edge47" class="edge"><title>N3&#45;&gt;N56</title>
<g id="a_edge47"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*GainItemMessage).Execute (2.50s)">
<path fill="none" stroke="#b2ada1" d="M1323.59,-2114.37C1307.42,-2094.09 1286.09,-2067.33 1268.77,-2045.6"/>
<polygon fill="#b2ada1" stroke="#b2ada1" points="1271.31,-2043.18 1262.34,-2037.54 1265.84,-2047.54 1271.31,-2043.18"/>
</a>
</g>
<g id="a_edge47&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*GainItemMessage).Execute (2.50s)">
<text text-anchor="middle" x="1307" y="-2060.3" font-family="Times,serif" font-size="14.00"> 2.50s</text>
</a>
</g>
</g>
<!-- N67 -->
<g id="node67" class="node"><title>N67</title>
<g id="a_node67"><a xlink:title="adapter/service/wm_logger.(*AddCoinMessage).Execute (2.38s)">
<polygon fill="#edecea" stroke="#b2ada2" points="1097,-2035 1005,-2035 1005,-1977 1097,-1977 1097,-2035"/>
<text text-anchor="middle" x="1051" y="-2023.8" font-family="Times,serif" font-size="9.00">wm_logger</text>
<text text-anchor="middle" x="1051" y="-2013.8" font-family="Times,serif" font-size="9.00">(*AddCoinMessage)</text>
<text text-anchor="middle" x="1051" y="-2003.8" font-family="Times,serif" font-size="9.00">Execute</text>
<text text-anchor="middle" x="1051" y="-1993.8" font-family="Times,serif" font-size="9.00">0.06s (0.046%)</text>
<text text-anchor="middle" x="1051" y="-1983.8" font-family="Times,serif" font-size="9.00">of 2.38s (1.84%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N67 -->
<g id="edge48" class="edge"><title>N3&#45;&gt;N67</title>
<g id="a_edge48"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*AddCoinMessage).Execute (2.38s)">
<path fill="none" stroke="#b2ada2" d="M1300.67,-2142.61C1244.26,-2137.52 1149.75,-2121.6 1087,-2072 1077.86,-2064.77 1070.68,-2054.55 1065.23,-2044.35"/>
<polygon fill="#b2ada2" stroke="#b2ada2" points="1068.23,-2042.52 1060.69,-2035.07 1061.94,-2045.59 1068.23,-2042.52"/>
</a>
</g>
<g id="a_edge48&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*AddCoinMessage).Execute (2.38s)">
<text text-anchor="middle" x="1104" y="-2060.3" font-family="Times,serif" font-size="14.00"> 2.38s</text>
</a>
</g>
</g>
<!-- N64 -->
<g id="node64" class="node"><title>N64</title>
<g id="a_node64"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute (1.98s)">
<polygon fill="#edeceb" stroke="#b2aea4" points="893,-2035 783,-2035 783,-1977 893,-1977 893,-2035"/>
<text text-anchor="middle" x="838" y="-2023.8" font-family="Times,serif" font-size="9.00">wm_logger</text>
<text text-anchor="middle" x="838" y="-2013.8" font-family="Times,serif" font-size="9.00">(*DungeonFightMessage)</text>
<text text-anchor="middle" x="838" y="-2003.8" font-family="Times,serif" font-size="9.00">Execute</text>
<text text-anchor="middle" x="838" y="-1993.8" font-family="Times,serif" font-size="9.00">0.04s (0.031%)</text>
<text text-anchor="middle" x="838" y="-1983.8" font-family="Times,serif" font-size="9.00">of 1.98s (1.53%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N64 -->
<g id="edge57" class="edge"><title>N3&#45;&gt;N64</title>
<g id="a_edge57"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*DungeonFightMessage).Execute (1.98s)">
<path fill="none" stroke="#b2aea4" d="M1300.74,-2143.02C1194.63,-2137.74 941.418,-2120.3 873,-2072 863.282,-2065.14 856.01,-2054.71 850.688,-2044.2"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="853.838,-2042.68 846.5,-2035.03 847.471,-2045.58 853.838,-2042.68"/>
</a>
</g>
<g id="a_edge57&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/wm_logger.(*DungeonFightMessage).Execute (1.98s)">
<text text-anchor="middle" x="890" y="-2060.3" font-family="Times,serif" font-size="14.00"> 1.98s</text>
</a>
</g>
</g>
<!-- N59 -->
<g id="node59" class="node"><title>N59</title>
<g id="a_node59"><a xlink:title="adapter/service/collect.(*Collect).WriteAll (1.57s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="1796.25,-1340.5 1713.75,-1340.5 1713.75,-1277.5 1796.25,-1277.5 1796.25,-1340.5"/>
<text text-anchor="middle" x="1755" y="-1328.5" font-family="Times,serif" font-size="10.00">collect</text>
<text text-anchor="middle" x="1755" y="-1317.5" font-family="Times,serif" font-size="10.00">(*Collect)</text>
<text text-anchor="middle" x="1755" y="-1306.5" font-family="Times,serif" font-size="10.00">WriteAll</text>
<text text-anchor="middle" x="1755" y="-1295.5" font-family="Times,serif" font-size="10.00">0.09s (0.07%)</text>
<text text-anchor="middle" x="1755" y="-1284.5" font-family="Times,serif" font-size="10.00">of 1.57s (1.21%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N59 -->
<g id="edge63" class="edge"><title>N3&#45;&gt;N59</title>
<g id="a_edge63"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/collect.(*Collect).WriteAll (1.57s)">
<path fill="none" stroke="#b2afa7" d="M1395.02,-2124.46C1419.6,-2111.98 1448.89,-2094.16 1470,-2072 1485.04,-2056.22 1644.99,-1745.36 1655,-1726 1679.27,-1679.08 1686.14,-1666.97 1700,-1616 1725.38,-1522.64 1742.18,-1410.01 1749.98,-1350.86"/>
<polygon fill="#b2afa7" stroke="#b2afa7" points="1753.46,-1351.16 1751.28,-1340.8 1746.52,-1350.26 1753.46,-1351.16"/>
</a>
</g>
<g id="a_edge63&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher &#45;&gt; adapter/service/collect.(*Collect).WriteAll (1.57s)">
<text text-anchor="middle" x="1681" y="-1714.3" font-family="Times,serif" font-size="14.00"> 1.57s</text>
</a>
</g>
</g>
<!-- N24 -->
<g id="node24" class="node"><title>N24</title>
<g id="a_node24"><a xlink:title="fmt.Sprintf (4.20s)">
<polygon fill="#edebe9" stroke="#b2a895" points="1683.25,-1084 1600.75,-1084 1600.75,-1032 1683.25,-1032 1683.25,-1084"/>
<text text-anchor="middle" x="1642" y="-1072" font-family="Times,serif" font-size="10.00">fmt</text>
<text text-anchor="middle" x="1642" y="-1061" font-family="Times,serif" font-size="10.00">Sprintf</text>
<text text-anchor="middle" x="1642" y="-1050" font-family="Times,serif" font-size="10.00">0.24s (0.19%)</text>
<text text-anchor="middle" x="1642" y="-1039" font-family="Times,serif" font-size="10.00">of 4.20s (3.25%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N24 -->
<g id="edge104" class="edge"><title>N3&#45;&gt;N24</title>
<g id="a_edge104"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... fmt.Sprintf (0.45s)">
<path fill="none" stroke="#b2b1af" stroke-dasharray="1,5" d="M1375.53,-2114.37C1385.61,-2101.92 1396.4,-2086.95 1404,-2072 1411.14,-2057.96 1411.68,-2053.58 1414,-2038 1418.82,-2005.69 1402.9,-1773.47 1417,-1744 1465.94,-1641.75 1551.63,-1676.66 1614,-1582 1692.93,-1462.2 1596.5,-1367.01 1704,-1272 1741.78,-1238.61 1780.1,-1289.4 1816,-1254 1837.77,-1232.53 1856.66,-1141.43 1837,-1118 1807.41,-1082.73 1779.05,-1113.38 1735,-1100 1720.87,-1095.71 1706.04,-1089.84 1692.48,-1083.89"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="1693.87,-1080.67 1683.32,-1079.77 1691,-1087.06 1693.87,-1080.67"/>
</a>
</g>
<g id="a_edge104&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... fmt.Sprintf (0.45s)">
<text text-anchor="middle" x="1618" y="-1604.3" font-family="Times,serif" font-size="14.00"> 0.45s</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N18 -->
<g id="edge118" class="edge"><title>N3&#45;&gt;N18</title>
<g id="a_edge118"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... github.com/ivanabc/log4go.Logger.intLogf (0.34s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1365.25,-2114.21C1372.13,-2101.36 1379.85,-2086.15 1386,-2072 1392.38,-2057.3 1395.29,-2053.79 1398,-2038 1402.8,-2009.96 1398.55,-2002.44 1398,-1974 1397.11,-1927.75 1399.27,-1915.96 1394,-1870 1391.32,-1846.62 1387.04,-1841.34 1384,-1818 1364.78,-1670.48 1338.62,-1630.92 1362,-1484 1376.87,-1390.57 1416.5,-1286.59 1440.25,-1229.8"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1443.54,-1231.01 1444.21,-1220.43 1437.1,-1228.29 1443.54,-1231.01"/>
</a>
</g>
<g id="a_edge118&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... github.com/ivanabc/log4go.Logger.intLogf (0.34s)">
<text text-anchor="middle" x="1381" y="-1659.3" font-family="Times,serif" font-size="14.00"> 0.34s</text>
</a>
</g>
</g>
<!-- N31 -->
<g id="node31" class="node"><title>N31</title>
<g id="a_node31"><a xlink:title="runtime.growslice (3.61s)">
<polygon fill="#edece9" stroke="#b2a999" points="227,-828 137,-828 137,-772 227,-772 227,-828"/>
<text text-anchor="middle" x="182" y="-815.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="182" y="-803.2" font-family="Times,serif" font-size="11.00">growslice</text>
<text text-anchor="middle" x="182" y="-791.2" font-family="Times,serif" font-size="11.00">0.51s (0.39%)</text>
<text text-anchor="middle" x="182" y="-779.2" font-family="Times,serif" font-size="11.00">of 3.61s (2.79%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N31 -->
<g id="edge138" class="edge"><title>N3&#45;&gt;N31</title>
<g id="a_edge138"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... runtime.growslice (0.21s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1300.86,-2142.05C1211.99,-2135.61 1014.23,-2117.09 854,-2072 816.811,-2061.54 803.783,-2062.61 774,-2038 747.582,-2016.17 758.404,-1994.57 731,-1974 670.19,-1928.34 639.115,-1948.93 568,-1922 372.963,-1848.15 309.765,-1836.09 159,-1692 44.4791,-1582.55 0,-1531.41 0,-1373 0,-1373 0,-1373 0,-1125 0,-1006.69 93.3838,-891.63 146.368,-835.868"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="149.146,-838.029 153.562,-828.399 144.104,-833.173 149.146,-838.029"/>
</a>
</g>
<g id="a_edge138&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... runtime.growslice (0.21s)">
<text text-anchor="middle" x="33" y="-1488.3" font-family="Times,serif" font-size="14.00"> 0.21s</text>
</a>
</g>
</g>
<!-- N13 -->
<g id="node13" class="node"><title>N13</title>
<g id="a_node13"><a xlink:title="[adapter] (6.12s)">
<polygon fill="#edeae7" stroke="#b2a188" points="1080.25,-687 975.75,-687 975.75,-637 1080.25,-637 1080.25,-687"/>
<text text-anchor="middle" x="1028" y="-672.6" font-family="Times,serif" font-size="13.00">[adapter]</text>
<text text-anchor="middle" x="1028" y="-658.6" font-family="Times,serif" font-size="13.00">1.41s (1.09%)</text>
<text text-anchor="middle" x="1028" y="-644.6" font-family="Times,serif" font-size="13.00">of 6.12s (4.73%)</text>
</a>
</g>
</g>
<!-- N3&#45;&gt;N13 -->
<g id="edge145" class="edge"><title>N3&#45;&gt;N13</title>
<g id="a_edge145"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... [adapter] (0.15s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1300.91,-2143.66C1226.45,-2139.03 1081.25,-2119.36 996,-2038 954.011,-1997.93 949.722,-1978.4 936,-1922 861.635,-1616.36 988.421,-1532.3 1026,-1220 1044.79,-1063.87 1053.99,-1025.06 1062,-868 1065.1,-807.18 1049.33,-737.434 1038.23,-696.979"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1041.53,-695.783 1035.45,-687.105 1034.79,-697.679 1041.53,-695.783"/>
</a>
</g>
<g id="a_edge145&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).Dispatcher ... [adapter] (0.15s)">
<text text-anchor="middle" x="1009" y="-1428.3" font-family="Times,serif" font-size="14.00"> 0.15s</text>
</a>
</g>
</g>
<!-- N4 -->
<g id="node4" class="node"><title>N4</title>
<g id="a_node4"><a xlink:title="adapter/service/kafka_consumer.(*Manager).Run.func1 (63.97s)">
<polygon fill="#edd9d5" stroke="#b22200" points="1390,-2620.5 1306,-2620.5 1306,-2567.5 1390,-2567.5 1390,-2620.5"/>
<text text-anchor="middle" x="1348" y="-2610.1" font-family="Times,serif" font-size="8.00">kafka_consumer</text>
<text text-anchor="middle" x="1348" y="-2601.1" font-family="Times,serif" font-size="8.00">(*Manager)</text>
<text text-anchor="middle" x="1348" y="-2592.1" font-family="Times,serif" font-size="8.00">Run</text>
<text text-anchor="middle" x="1348" y="-2583.1" font-family="Times,serif" font-size="8.00">func1</text>
<text text-anchor="middle" x="1348" y="-2574.1" font-family="Times,serif" font-size="8.00">0 of 63.97s (49.44%)</text>
</a>
</g>
</g>
<!-- N4&#45;&gt;N2 -->
<g id="edge1" class="edge"><title>N4&#45;&gt;N2</title>
<g id="a_edge1"><a xlink:title="adapter/service/kafka_consumer.(*Manager).Run.func1 &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).Process (63.97s)">
<path fill="none" stroke="#b22200" stroke-width="3" d="M1348,-2567.25C1348,-2536.68 1348,-2485.22 1348,-2448.44"/>
<polygon fill="#b22200" stroke="#b22200" points="1351.5,-2448.02 1348,-2438.02 1344.5,-2448.02 1351.5,-2448.02"/>
</a>
</g>
<g id="a_edge1&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*Manager).Run.func1 &#45;&gt; adapter/service/kafka_consumer.(*PartitionConsumer).Process (63.97s)">
<text text-anchor="middle" x="1368" y="-2460.3" font-family="Times,serif" font-size="14.00"> 63.97s</text>
</a>
</g>
</g>
<!-- N47 -->
<g id="node47" class="node"><title>N47</title>
<g id="a_node47"><a xlink:title="runtime.gcDrain (8.15s)">
<polygon fill="#ede9e5" stroke="#b2997a" points="2372,-304 2282,-304 2282,-248 2372,-248 2372,-304"/>
<text text-anchor="middle" x="2327" y="-291.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="2327" y="-279.2" font-family="Times,serif" font-size="11.00">gcDrain</text>
<text text-anchor="middle" x="2327" y="-267.2" font-family="Times,serif" font-size="11.00">0.29s (0.22%)</text>
<text text-anchor="middle" x="2327" y="-255.2" font-family="Times,serif" font-size="11.00">of 8.15s (6.30%)</text>
</a>
</g>
</g>
<!-- N5&#45;&gt;N47 -->
<g id="edge16" class="edge"><title>N5&#45;&gt;N47</title>
<g id="a_edge16"><a xlink:title="runtime.systemstack ... runtime.gcDrain (8.15s)">
<path fill="none" stroke="#b2997a" stroke-dasharray="1,5" d="M2327,-356.85C2327,-344.183 2327,-328.569 2327,-314.518"/>
<polygon fill="#b2997a" stroke="#b2997a" points="2330.5,-314.152 2327,-304.152 2323.5,-314.152 2330.5,-314.152"/>
</a>
</g>
<g id="a_edge16&#45;label"><a xlink:title="runtime.systemstack ... runtime.gcDrain (8.15s)">
<text text-anchor="middle" x="2344" y="-326.3" font-family="Times,serif" font-size="14.00"> 8.15s</text>
</a>
</g>
</g>
<!-- N38 -->
<g id="node38" class="node"><title>N38</title>
<g id="a_node38"><a xlink:title="runtime.futex (2.09s)">
<polygon fill="#edeceb" stroke="#b2aea4" points="2485.25,-302.5 2390.75,-302.5 2390.75,-249.5 2485.25,-249.5 2485.25,-302.5"/>
<text text-anchor="middle" x="2438" y="-287.3" font-family="Times,serif" font-size="14.00">runtime</text>
<text text-anchor="middle" x="2438" y="-272.3" font-family="Times,serif" font-size="14.00">futex</text>
<text text-anchor="middle" x="2438" y="-257.3" font-family="Times,serif" font-size="14.00">2.09s (1.62%)</text>
</a>
</g>
</g>
<!-- N5&#45;&gt;N38 -->
<g id="edge123" class="edge"><title>N5&#45;&gt;N38</title>
<g id="a_edge123"><a xlink:title="runtime.systemstack ... runtime.futex (0.31s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M2351.98,-356.933C2362.93,-346.08 2375.99,-333.299 2388,-322 2392.3,-317.95 2396.86,-313.742 2401.4,-309.598"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="2403.93,-312.033 2408.98,-302.722 2399.22,-306.848 2403.93,-312.033"/>
</a>
</g>
<g id="a_edge123&#45;label"><a xlink:title="runtime.systemstack ... runtime.futex (0.31s)">
<text text-anchor="middle" x="2405" y="-326.3" font-family="Times,serif" font-size="14.00"> 0.31s</text>
</a>
</g>
</g>
<!-- N6 -->
<g id="node6" class="node"><title>N6</title>
<g id="a_node6"><a xlink:title="github.com/Shopify/sarama.withRecover (19.98s)">
<polygon fill="#ede2da" stroke="#b26328" points="1017,-1204 933,-1204 933,-1168 1017,-1168 1017,-1204"/>
<text text-anchor="middle" x="975" y="-1193.1" font-family="Times,serif" font-size="8.00">sarama</text>
<text text-anchor="middle" x="975" y="-1184.1" font-family="Times,serif" font-size="8.00">withRecover</text>
<text text-anchor="middle" x="975" y="-1175.1" font-family="Times,serif" font-size="8.00">0 of 19.98s (15.44%)</text>
</a>
</g>
</g>
<!-- N29 -->
<g id="node29" class="node"><title>N29</title>
<g id="a_node29"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver (10.62s)">
<polygon fill="#ede8e3" stroke="#b28f69" points="1017,-1087 935,-1087 935,-1029 1017,-1029 1017,-1087"/>
<text text-anchor="middle" x="976" y="-1075.8" font-family="Times,serif" font-size="9.00">sarama</text>
<text text-anchor="middle" x="976" y="-1065.8" font-family="Times,serif" font-size="9.00">(*Broker)</text>
<text text-anchor="middle" x="976" y="-1055.8" font-family="Times,serif" font-size="9.00">responseReceiver</text>
<text text-anchor="middle" x="976" y="-1045.8" font-family="Times,serif" font-size="9.00">0.04s (0.031%)</text>
<text text-anchor="middle" x="976" y="-1035.8" font-family="Times,serif" font-size="9.00">of 10.62s (8.21%)</text>
</a>
</g>
</g>
<!-- N6&#45;&gt;N29 -->
<g id="edge12" class="edge"><title>N6&#45;&gt;N29</title>
<g id="a_edge12"><a xlink:title="github.com/Shopify/sarama.withRecover &#45;&gt; github.com/Shopify/sarama.(*Broker).responseReceiver (10.62s)">
<path fill="none" stroke="#b28f69" d="M975.137,-1167.74C975.28,-1149.66 975.511,-1120.67 975.697,-1097.17"/>
<polygon fill="#b28f69" stroke="#b28f69" points="979.197,-1097.17 975.777,-1087.15 972.197,-1097.12 979.197,-1097.17"/>
</a>
</g>
<g id="a_edge12&#45;label"><a xlink:title="github.com/Shopify/sarama.withRecover &#45;&gt; github.com/Shopify/sarama.(*Broker).responseReceiver (10.62s)">
<text text-anchor="middle" x="996" y="-1122.3" font-family="Times,serif" font-size="14.00"> 10.62s</text>
</a>
</g>
</g>
<!-- N41 -->
<g id="node41" class="node"><title>N41</title>
<g id="a_node41"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive (6.64s)">
<polygon fill="#edeae7" stroke="#b29f84" points="611.25,-1087 534.75,-1087 534.75,-1029 611.25,-1029 611.25,-1087"/>
<text text-anchor="middle" x="573" y="-1075.8" font-family="Times,serif" font-size="9.00">sarama</text>
<text text-anchor="middle" x="573" y="-1065.8" font-family="Times,serif" font-size="9.00">(*Broker)</text>
<text text-anchor="middle" x="573" y="-1055.8" font-family="Times,serif" font-size="9.00">sendAndReceive</text>
<text text-anchor="middle" x="573" y="-1045.8" font-family="Times,serif" font-size="9.00">0.03s (0.023%)</text>
<text text-anchor="middle" x="573" y="-1035.8" font-family="Times,serif" font-size="9.00">of 6.64s (5.13%)</text>
</a>
</g>
</g>
<!-- N6&#45;&gt;N41 -->
<g id="edge21" class="edge"><title>N6&#45;&gt;N41</title>
<g id="a_edge21"><a xlink:title="github.com/Shopify/sarama.withRecover ... github.com/Shopify/sarama.(*Broker).sendAndReceive (6.64s)">
<path fill="none" stroke="#b29f84" stroke-dasharray="1,5" d="M932.773,-1179.72C851.195,-1169.31 677.752,-1146.11 653,-1134 633.924,-1124.66 616.282,-1109.22 602.447,-1094.77"/>
<polygon fill="#b29f84" stroke="#b29f84" points="604.932,-1092.3 595.571,-1087.34 599.794,-1097.06 604.932,-1092.3"/>
</a>
</g>
<g id="a_edge21&#45;label"><a xlink:title="github.com/Shopify/sarama.withRecover ... github.com/Shopify/sarama.(*Broker).sendAndReceive (6.64s)">
<text text-anchor="middle" x="670" y="-1122.3" font-family="Times,serif" font-size="14.00"> 6.64s</text>
</a>
</g>
</g>
<!-- N6&#45;&gt;N16 -->
<g id="edge96" class="edge"><title>N6&#45;&gt;N16</title>
<g id="a_edge96"><a xlink:title="github.com/Shopify/sarama.withRecover ... runtime.selectgo (0.60s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1017.1,-1180.51C1084.98,-1173.43 1222.86,-1159.69 1340,-1152 1535.3,-1139.18 1585.67,-1157.26 1780,-1134 1816,-1129.69 1824.04,-1122.69 1860,-1118 1988.05,-1101.3 2021.53,-1113.08 2150,-1100 2227.05,-1092.16 2315.1,-1079.07 2373.55,-1069.79"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2374.43,-1073.19 2383.76,-1068.16 2373.33,-1066.28 2374.43,-1073.19"/>
</a>
</g>
<g id="a_edge96&#45;label"><a xlink:title="github.com/Shopify/sarama.withRecover ... runtime.selectgo (0.60s)">
<text text-anchor="middle" x="1877" y="-1122.3" font-family="Times,serif" font-size="14.00"> 0.60s</text>
</a>
</g>
</g>
<!-- N6&#45;&gt;N13 -->
<g id="edge106" class="edge"><title>N6&#45;&gt;N13</title>
<g id="a_edge106"><a xlink:title="github.com/Shopify/sarama.withRecover ... [adapter] (0.44s)">
<path fill="none" stroke="#b2b1af" stroke-dasharray="1,5" d="M962.362,-1167.73C950.94,-1151.2 934.632,-1125.13 926,-1100 894.98,-1009.69 883.509,-978.55 907,-886 925.666,-812.461 973.992,-736.926 1003.61,-695.374"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="1006.62,-697.192 1009.63,-687.036 1000.94,-693.095 1006.62,-697.192"/>
</a>
</g>
<g id="a_edge106&#45;label"><a xlink:title="github.com/Shopify/sarama.withRecover ... [adapter] (0.44s)">
<text text-anchor="middle" x="924" y="-921.3" font-family="Times,serif" font-size="14.00"> 0.44s</text>
</a>
</g>
</g>
<!-- N7&#45;&gt;N3 -->
<g id="edge3" class="edge"><title>N7&#45;&gt;N3</title>
<g id="a_edge3"><a xlink:title="adapter/service/wm_logger.(*Logger).Write &#45;&gt; adapter/service/wm_logger.(*MessageM).Dispatcher (22.84s)">
<path fill="none" stroke="#b25414" d="M1348,-2254.37C1348,-2234.72 1348,-2208.98 1348,-2187.62"/>
<polygon fill="#b25414" stroke="#b25414" points="1351.5,-2187.54 1348,-2177.54 1344.5,-2187.54 1351.5,-2187.54"/>
</a>
</g>
<g id="a_edge3&#45;label"><a xlink:title="adapter/service/wm_logger.(*Logger).Write &#45;&gt; adapter/service/wm_logger.(*MessageM).Dispatcher (22.84s)">
<text text-anchor="middle" x="1368" y="-2224.3" font-family="Times,serif" font-size="14.00"> 22.84s</text>
</a>
</g>
</g>
<!-- N7&#45;&gt;N8 -->
<g id="edge9" class="edge"><title>N7&#45;&gt;N8</title>
<g id="a_edge9"><a xlink:title="adapter/service/wm_logger.(*Logger).Write &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (16.26s)">
<path fill="none" stroke="#b27642" d="M1300.99,-2285.21C1149.98,-2284.62 687,-2272.58 687,-2147 687,-2147 687,-2147 687,-1895 687,-1871.06 687,-1844.27 687,-1822.78"/>
<polygon fill="#b27642" stroke="#b27642" points="690.5,-1822.69 687,-1812.69 683.5,-1822.69 690.5,-1822.69"/>
</a>
</g>
<g id="a_edge9&#45;label"><a xlink:title="adapter/service/wm_logger.(*Logger).Write &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (16.26s)">
<text text-anchor="middle" x="707" y="-2002.3" font-family="Times,serif" font-size="14.00"> 16.26s</text>
</a>
</g>
</g>
<!-- N25 -->
<g id="node25" class="node"><title>N25</title>
<g id="a_node25"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal (22.05s)">
<polygon fill="#ede1d8" stroke="#b2591a" points="730,-1692 644,-1692 644,-1634 730,-1634 730,-1692"/>
<text text-anchor="middle" x="687" y="-1680.8" font-family="Times,serif" font-size="9.00">go</text>
<text text-anchor="middle" x="687" y="-1670.8" font-family="Times,serif" font-size="9.00">(*Iterator)</text>
<text text-anchor="middle" x="687" y="-1660.8" font-family="Times,serif" font-size="9.00">ReadVal</text>
<text text-anchor="middle" x="687" y="-1650.8" font-family="Times,serif" font-size="9.00">0.02s (0.015%)</text>
<text text-anchor="middle" x="687" y="-1640.8" font-family="Times,serif" font-size="9.00">of 22.05s (17.04%)</text>
</a>
</g>
</g>
<!-- N8&#45;&gt;N25 -->
<g id="edge5" class="edge"><title>N8&#45;&gt;N25</title>
<g id="a_edge5"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).ReadVal (22.05s)">
<path fill="none" stroke="#b2591a" d="M687,-1749.22C687,-1734.82 687,-1717.53 687,-1702.3"/>
<polygon fill="#b2591a" stroke="#b2591a" points="690.5,-1702.14 687,-1692.14 683.5,-1702.14 690.5,-1702.14"/>
</a>
</g>
<g id="a_edge5&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).ReadVal (22.05s)">
<text text-anchor="middle" x="707" y="-1714.3" font-family="Times,serif" font-size="14.00"> 22.05s</text>
</a>
</g>
</g>
<!-- N70 -->
<g id="node70" class="node"><title>N70</title>
<g id="a_node70"><a xlink:title="sync.(*Pool).Get (1.18s)">
<polygon fill="#edecec" stroke="#b2b0aa" points="1551,-959 1461,-959 1461,-891 1551,-891 1551,-959"/>
<text text-anchor="middle" x="1506" y="-946.2" font-family="Times,serif" font-size="11.00">sync</text>
<text text-anchor="middle" x="1506" y="-934.2" font-family="Times,serif" font-size="11.00">(*Pool)</text>
<text text-anchor="middle" x="1506" y="-922.2" font-family="Times,serif" font-size="11.00">Get</text>
<text text-anchor="middle" x="1506" y="-910.2" font-family="Times,serif" font-size="11.00">0.30s (0.23%)</text>
<text text-anchor="middle" x="1506" y="-898.2" font-family="Times,serif" font-size="11.00">of 1.18s (0.91%)</text>
</a>
</g>
</g>
<!-- N8&#45;&gt;N70 -->
<g id="edge147" class="edge"><title>N8&#45;&gt;N70</title>
<g id="a_edge147"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal ... sync.(*Pool).Get (0.15s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M734.37,-1773.09C774.013,-1765.78 830.994,-1751.65 875,-1726 1017.55,-1642.9 1071.69,-1616.2 1140,-1466 1158.8,-1424.66 1142.27,-1409.26 1146,-1364 1152.4,-1286.49 1130.58,-1073.45 1183,-1016 1187.96,-1010.56 1436.13,-966.664 1443,-964 1445.88,-962.881 1448.79,-961.634 1451.68,-960.293"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1453.45,-963.322 1460.84,-955.73 1450.33,-957.056 1453.45,-963.322"/>
</a>
</g>
<g id="a_edge147&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal ... sync.(*Pool).Get (0.15s)">
<text text-anchor="middle" x="1163" y="-1368.3" font-family="Times,serif" font-size="14.00"> 0.15s</text>
</a>
</g>
</g>
<!-- N8&#45;&gt;N13 -->
<g id="edge153" class="edge"><title>N8&#45;&gt;N13</title>
<g id="a_edge153"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal &#45;&gt; [adapter] (0.13s)">
<path fill="none" stroke="#b2b2b1" d="M734.087,-1770.05C785.618,-1756.07 861,-1725.18 861,-1664 861,-1664 861,-1664 861,-799 861,-742.738 918.694,-705.63 966.26,-684.597"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="967.794,-687.747 975.618,-680.603 965.047,-681.309 967.794,-687.747"/>
</a>
</g>
<g id="a_edge153&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal &#45;&gt; [adapter] (0.13s)">
<text text-anchor="middle" x="878" y="-1242.3" font-family="Times,serif" font-size="14.00"> 0.13s</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N8 -->
<g id="edge62" class="edge"><title>N9&#45;&gt;N8</title>
<g id="a_edge62"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (1.59s)">
<path fill="none" stroke="#b2afa7" stroke-dasharray="1,5" d="M1090.67,-1875.84C1085.74,-1873.82 1080.78,-1871.84 1076,-1870 1053.17,-1861.22 1045.8,-1863.09 1024,-1852 1012.9,-1846.35 1012.52,-1840.72 1001,-1836 915.861,-1801.1 808.784,-1788.75 744.229,-1784.39"/>
<polygon fill="#b2afa7" stroke="#b2afa7" points="744.445,-1780.89 734.244,-1783.75 744.002,-1787.88 744.445,-1780.89"/>
</a>
</g>
<g id="a_edge62&#45;label"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (1.59s)">
<text text-anchor="middle" x="1041" y="-1840.3" font-family="Times,serif" font-size="14.00"> 1.59s</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N24 -->
<g id="edge86" class="edge"><title>N9&#45;&gt;N24</title>
<g id="a_edge86"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog &#45;&gt; fmt.Sprintf (0.84s)">
<path fill="none" stroke="#b2b1ac" d="M1146.05,-1869.77C1162.77,-1831.46 1195.37,-1756.3 1222,-1692 1242.15,-1643.35 1247.44,-1631.28 1266,-1582 1279.63,-1545.82 1285.25,-1537.41 1295,-1500 1324.8,-1385.67 1319.19,-1354.3 1340,-1238 1346.85,-1199.73 1328.93,-1179.9 1356,-1152 1374.92,-1132.49 1570.72,-1112.21 1595,-1100 1600.21,-1097.38 1605.3,-1094.05 1610.1,-1090.43"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="1612.31,-1093.14 1617.87,-1084.12 1607.9,-1087.71 1612.31,-1093.14"/>
</a>
</g>
<g id="a_edge86&#45;label"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog &#45;&gt; fmt.Sprintf (0.84s)">
<text text-anchor="middle" x="1317" y="-1488.3" font-family="Times,serif" font-size="14.00"> 0.84s</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N13 -->
<g id="edge87" class="edge"><title>N9&#45;&gt;N13</title>
<g id="a_edge87"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... [adapter] (0.79s)">
<path fill="none" stroke="#b2b1ad" stroke-dasharray="1,5" d="M1115.47,-1869.77C1092.08,-1836.95 1056,-1776.94 1056,-1719 1056,-1719 1056,-1719 1056,-1491 1056,-1393.57 1054.77,-1368.79 1066,-1272 1079.34,-1157.01 1032.22,-1097.93 1114,-1016 1144.36,-985.584 1181.59,-1031.09 1209,-998 1240.75,-959.665 1228.67,-931.727 1209,-886 1204.4,-875.314 1197.85,-876.585 1190,-868 1137.03,-810.054 1081.63,-736.758 1051.31,-695.354"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="1054.13,-693.281 1045.4,-687.266 1048.47,-697.407 1054.13,-693.281"/>
</a>
</g>
<g id="a_edge87&#45;label"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... [adapter] (0.79s)">
<text text-anchor="middle" x="1083" y="-1305.3" font-family="Times,serif" font-size="14.00"> 0.79s</text>
</a>
</g>
</g>
<!-- N44 -->
<g id="node44" class="node"><title>N44</title>
<g id="a_node44"><a xlink:title="runtime.makeslice (2.21s)">
<polygon fill="#edeceb" stroke="#b2aea3" points="1334.25,-951 1251.75,-951 1251.75,-899 1334.25,-899 1334.25,-951"/>
<text text-anchor="middle" x="1293" y="-939" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="1293" y="-928" font-family="Times,serif" font-size="10.00">makeslice</text>
<text text-anchor="middle" x="1293" y="-917" font-family="Times,serif" font-size="10.00">0.15s (0.12%)</text>
<text text-anchor="middle" x="1293" y="-906" font-family="Times,serif" font-size="10.00">of 2.21s (1.71%)</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N44 -->
<g id="edge107" class="edge"><title>N9&#45;&gt;N44</title>
<g id="a_edge107"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... runtime.makeslice (0.41s)">
<path fill="none" stroke="#b2b1af" stroke-dasharray="1,5" d="M1123.48,-1869.97C1121.28,-1864.18 1119.27,-1857.97 1118,-1852 1076.12,-1654.49 1089.39,-1599.51 1102,-1398 1107.34,-1312.61 1092.16,-1079.04 1150,-1016 1175.81,-987.865 1201.4,-1017.88 1234,-998 1249.54,-988.522 1262.82,-973.448 1272.83,-959.537"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="1275.86,-961.311 1278.63,-951.085 1270.09,-957.351 1275.86,-961.311"/>
</a>
</g>
<g id="a_edge107&#45;label"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... runtime.makeslice (0.41s)">
<text text-anchor="middle" x="1119" y="-1428.3" font-family="Times,serif" font-size="14.00"> 0.41s</text>
</a>
</g>
</g>
<!-- N21 -->
<g id="node21" class="node"><title>N21</title>
<g id="a_node21"><a xlink:title="runtime.slicebytetostring (6s)">
<polygon fill="#edeae7" stroke="#b2a289" points="1445,-830 1353,-830 1353,-770 1445,-770 1445,-830"/>
<text text-anchor="middle" x="1399" y="-816.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="1399" y="-803.4" font-family="Times,serif" font-size="12.00">slicebytetostring</text>
<text text-anchor="middle" x="1399" y="-790.4" font-family="Times,serif" font-size="12.00">0.70s (0.54%)</text>
<text text-anchor="middle" x="1399" y="-777.4" font-family="Times,serif" font-size="12.00">of 6s (4.64%)</text>
</a>
</g>
</g>
<!-- N9&#45;&gt;N21 -->
<g id="edge109" class="edge"><title>N9&#45;&gt;N21</title>
<g id="a_edge109"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... runtime.slicebytetostring (0.39s)">
<path fill="none" stroke="#b2b2af" stroke-dasharray="1,5" d="M1137.57,-1869.71C1151.95,-1728.96 1223.03,-1047.5 1254,-1016 1272.6,-997.087 1345.84,-1002.36 1372,-998 1455.76,-984.041 1505.56,-1029.17 1560,-964 1582.23,-937.395 1577.9,-915.689 1560,-886 1537.3,-848.354 1491.75,-826.525 1454.88,-814.438"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="1455.81,-811.063 1445.22,-811.427 1453.73,-817.746 1455.81,-811.063"/>
</a>
</g>
<g id="a_edge109&#45;label"><a xlink:title="adapter/service/wm_logger.GetBaseFromLog ... runtime.slicebytetostring (0.39s)">
<text text-anchor="middle" x="1212" y="-1368.3" font-family="Times,serif" font-size="14.00"> 0.39s</text>
</a>
</g>
</g>
<!-- N10 -->
<g id="node10" class="node"><title>N10</title>
<g id="a_node10"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString (10.11s)">
<polygon fill="#ede8e3" stroke="#b2926c" points="829,-1099.5 713,-1099.5 713,-1016.5 829,-1016.5 829,-1099.5"/>
<text text-anchor="middle" x="771" y="-1084.3" font-family="Times,serif" font-size="14.00">go</text>
<text text-anchor="middle" x="771" y="-1069.3" font-family="Times,serif" font-size="14.00">(*Iterator)</text>
<text text-anchor="middle" x="771" y="-1054.3" font-family="Times,serif" font-size="14.00">ReadString</text>
<text text-anchor="middle" x="771" y="-1039.3" font-family="Times,serif" font-size="14.00">1.98s (1.53%)</text>
<text text-anchor="middle" x="771" y="-1024.3" font-family="Times,serif" font-size="14.00">of 10.11s (7.81%)</text>
</a>
</g>
</g>
<!-- N42 -->
<g id="node42" class="node"><title>N42</title>
<g id="a_node42"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).readStringSlowPath (4.33s)">
<polygon fill="#edebe9" stroke="#b2a794" points="832.25,-964 709.75,-964 709.75,-886 832.25,-886 832.25,-964"/>
<text text-anchor="middle" x="771" y="-949.6" font-family="Times,serif" font-size="13.00">go</text>
<text text-anchor="middle" x="771" y="-935.6" font-family="Times,serif" font-size="13.00">(*Iterator)</text>
<text text-anchor="middle" x="771" y="-921.6" font-family="Times,serif" font-size="13.00">readStringSlowPath</text>
<text text-anchor="middle" x="771" y="-907.6" font-family="Times,serif" font-size="13.00">1.59s (1.23%)</text>
<text text-anchor="middle" x="771" y="-893.6" font-family="Times,serif" font-size="13.00">of 4.33s (3.35%)</text>
</a>
</g>
</g>
<!-- N10&#45;&gt;N42 -->
<g id="edge31" class="edge"><title>N10&#45;&gt;N42</title>
<g id="a_edge31"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).readStringSlowPath (4.33s)">
<path fill="none" stroke="#b2a794" d="M771,-1016.27C771,-1002.98 771,-988.1 771,-974.312"/>
<polygon fill="#b2a794" stroke="#b2a794" points="774.5,-974.025 771,-964.025 767.5,-974.025 774.5,-974.025"/>
</a>
</g>
<g id="a_edge31&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).readStringSlowPath (4.33s)">
<text text-anchor="middle" x="788" y="-986.3" font-family="Times,serif" font-size="14.00"> 4.33s</text>
</a>
</g>
</g>
<!-- N10&#45;&gt;N21 -->
<g id="edge38" class="edge"><title>N10&#45;&gt;N21</title>
<g id="a_edge38"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString &#45;&gt; runtime.slicebytetostring (3.42s)">
<path fill="none" stroke="#b2aa9a" d="M829.204,-1034.28C877.393,-1015.46 947.653,-988.009 1009,-964 1097.46,-929.379 1115.85,-909.072 1208,-886 1274.42,-869.37 1303.95,-905.865 1361,-868 1371.45,-861.067 1379.34,-850.226 1385.14,-839.27"/>
<polygon fill="#b2aa9a" stroke="#b2aa9a" points="1388.36,-840.652 1389.54,-830.123 1382.05,-837.619 1388.36,-840.652"/>
</a>
</g>
<g id="a_edge38&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString &#45;&gt; runtime.slicebytetostring (3.42s)">
<text text-anchor="middle" x="1225" y="-921.3" font-family="Times,serif" font-size="14.00"> 3.42s</text>
</a>
</g>
</g>
<!-- N39 -->
<g id="node39" class="node"><title>N39</title>
<g id="a_node39"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).nextToken (1.79s)">
<polygon fill="#edeceb" stroke="#b2afa6" points="691.25,-964 586.75,-964 586.75,-886 691.25,-886 691.25,-964"/>
<text text-anchor="middle" x="639" y="-949.6" font-family="Times,serif" font-size="13.00">go</text>
<text text-anchor="middle" x="639" y="-935.6" font-family="Times,serif" font-size="13.00">(*Iterator)</text>
<text text-anchor="middle" x="639" y="-921.6" font-family="Times,serif" font-size="13.00">nextToken</text>
<text text-anchor="middle" x="639" y="-907.6" font-family="Times,serif" font-size="13.00">1.70s (1.31%)</text>
<text text-anchor="middle" x="639" y="-893.6" font-family="Times,serif" font-size="13.00">of 1.79s (1.38%)</text>
</a>
</g>
</g>
<!-- N10&#45;&gt;N39 -->
<g id="edge113" class="edge"><title>N10&#45;&gt;N39</title>
<g id="a_edge113"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).nextToken (0.38s)">
<path fill="none" stroke="#b2b2af" d="M729.958,-1016.27C715.519,-1001.94 699.212,-985.756 684.433,-971.089"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="686.879,-968.585 677.316,-964.025 681.948,-973.554 686.879,-968.585"/>
</a>
</g>
<g id="a_edge113&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadString &#45;&gt; github.com/json&#45;iterator/go.(*Iterator).nextToken (0.38s)">
<text text-anchor="middle" x="726" y="-986.3" font-family="Times,serif" font-size="14.00"> 0.38s</text>
</a>
</g>
</g>
<!-- N11 -->
<g id="node11" class="node"><title>N11</title>
<g id="a_node11"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField (17.58s)">
<polygon fill="#ede4dd" stroke="#b26f39" points="750.25,-1466 623.75,-1466 623.75,-1398 750.25,-1398 750.25,-1466"/>
<text text-anchor="middle" x="687" y="-1453.2" font-family="Times,serif" font-size="11.00">go</text>
<text text-anchor="middle" x="687" y="-1441.2" font-family="Times,serif" font-size="11.00">(*generalStructDecoder)</text>
<text text-anchor="middle" x="687" y="-1429.2" font-family="Times,serif" font-size="11.00">decodeOneField</text>
<text text-anchor="middle" x="687" y="-1417.2" font-family="Times,serif" font-size="11.00">0.51s (0.39%)</text>
<text text-anchor="middle" x="687" y="-1405.2" font-family="Times,serif" font-size="11.00">of 17.58s (13.59%)</text>
</a>
</g>
</g>
<!-- N19 -->
<g id="node19" class="node"><title>N19</title>
<g id="a_node19"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode (10.74s)">
<polygon fill="#ede8e3" stroke="#b28f68" points="695,-1345.5 575,-1345.5 575,-1272.5 695,-1272.5 695,-1345.5"/>
<text text-anchor="middle" x="635" y="-1331.9" font-family="Times,serif" font-size="12.00">go</text>
<text text-anchor="middle" x="635" y="-1318.9" font-family="Times,serif" font-size="12.00">(*structFieldDecoder)</text>
<text text-anchor="middle" x="635" y="-1305.9" font-family="Times,serif" font-size="12.00">Decode</text>
<text text-anchor="middle" x="635" y="-1292.9" font-family="Times,serif" font-size="12.00">0.63s (0.49%)</text>
<text text-anchor="middle" x="635" y="-1279.9" font-family="Times,serif" font-size="12.00">of 10.74s (8.30%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N19 -->
<g id="edge13" class="edge"><title>N11&#45;&gt;N19</title>
<g id="a_edge13"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField &#45;&gt; github.com/json&#45;iterator/go.(*structFieldDecoder).Decode (9.61s)">
<path fill="none" stroke="#b29470" d="M672.791,-1397.94C667.122,-1384.75 660.506,-1369.35 654.407,-1355.16"/>
<polygon fill="#b29470" stroke="#b29470" points="657.449,-1353.37 650.285,-1345.57 651.018,-1356.14 657.449,-1353.37"/>
</a>
</g>
<g id="a_edge13&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField &#45;&gt; github.com/json&#45;iterator/go.(*structFieldDecoder).Decode (9.61s)">
<text text-anchor="middle" x="682" y="-1368.3" font-family="Times,serif" font-size="14.00"> 9.61s</text>
</a>
</g>
</g>
<!-- N57 -->
<g id="node57" class="node"><title>N57</title>
<g id="a_node57"><a xlink:title="runtime.mapaccess1_faststr (2.99s)">
<polygon fill="#edecea" stroke="#b2ab9d" points="833,-1341 713,-1341 713,-1277 833,-1277 833,-1341"/>
<text text-anchor="middle" x="773" y="-1326.6" font-family="Times,serif" font-size="13.00">runtime</text>
<text text-anchor="middle" x="773" y="-1312.6" font-family="Times,serif" font-size="13.00">mapaccess1_faststr</text>
<text text-anchor="middle" x="773" y="-1298.6" font-family="Times,serif" font-size="13.00">1.61s (1.24%)</text>
<text text-anchor="middle" x="773" y="-1284.6" font-family="Times,serif" font-size="13.00">of 2.99s (2.31%)</text>
</a>
</g>
</g>
<!-- N11&#45;&gt;N57 -->
<g id="edge43" class="edge"><title>N11&#45;&gt;N57</title>
<g id="a_edge43"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField &#45;&gt; runtime.mapaccess1_faststr (2.98s)">
<path fill="none" stroke="#b2ab9d" d="M710.5,-1397.94C721.074,-1383.06 733.645,-1365.37 744.73,-1349.78"/>
<polygon fill="#b2ab9d" stroke="#b2ab9d" points="747.773,-1351.54 750.713,-1341.36 742.067,-1347.48 747.773,-1351.54"/>
</a>
</g>
<g id="a_edge43&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField &#45;&gt; runtime.mapaccess1_faststr (2.98s)">
<text text-anchor="middle" x="750" y="-1368.3" font-family="Times,serif" font-size="14.00"> 2.98s</text>
</a>
</g>
</g>
<!-- N12 -->
<g id="node12" class="node"><title>N12</title>
<g id="a_node12"><a xlink:title="adapter/service/collect.(*Collect).run (8.12s)">
<polygon fill="#ede9e5" stroke="#b29a7a" points="2043.25,-1340.5 1960.75,-1340.5 1960.75,-1277.5 2043.25,-1277.5 2043.25,-1340.5"/>
<text text-anchor="middle" x="2002" y="-1328.5" font-family="Times,serif" font-size="10.00">collect</text>
<text text-anchor="middle" x="2002" y="-1317.5" font-family="Times,serif" font-size="10.00">(*Collect)</text>
<text text-anchor="middle" x="2002" y="-1306.5" font-family="Times,serif" font-size="10.00">run</text>
<text text-anchor="middle" x="2002" y="-1295.5" font-family="Times,serif" font-size="10.00">0.10s (0.077%)</text>
<text text-anchor="middle" x="2002" y="-1284.5" font-family="Times,serif" font-size="10.00">of 8.12s (6.28%)</text>
</a>
</g>
</g>
<!-- N60 -->
<g id="node60" class="node"><title>N60</title>
<g id="a_node60"><a xlink:title="adapter/service/collect.(*Collect).write (3.29s)">
<polygon fill="#edecea" stroke="#b2aa9b" points="1605.25,-1217.5 1522.75,-1217.5 1522.75,-1154.5 1605.25,-1154.5 1605.25,-1217.5"/>
<text text-anchor="middle" x="1564" y="-1205.5" font-family="Times,serif" font-size="10.00">collect</text>
<text text-anchor="middle" x="1564" y="-1194.5" font-family="Times,serif" font-size="10.00">(*Collect)</text>
<text text-anchor="middle" x="1564" y="-1183.5" font-family="Times,serif" font-size="10.00">write</text>
<text text-anchor="middle" x="1564" y="-1172.5" font-family="Times,serif" font-size="10.00">0.17s (0.13%)</text>
<text text-anchor="middle" x="1564" y="-1161.5" font-family="Times,serif" font-size="10.00">of 3.29s (2.54%)</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N60 -->
<g id="edge40" class="edge"><title>N12&#45;&gt;N60</title>
<g id="a_edge40"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; adapter/service/collect.(*Collect).write (3.29s)">
<path fill="none" stroke="#b2aa9b" d="M1960.35,-1293.85C1912.35,-1278.04 1830.82,-1252.74 1759,-1238 1695.82,-1225.03 1675.6,-1242.08 1615,-1220 1614.9,-1219.96 1614.8,-1219.93 1614.7,-1219.89"/>
<polygon fill="#b2aa9b" stroke="#b2aa9b" points="1616.09,-1216.68 1605.53,-1215.93 1613.32,-1223.11 1616.09,-1216.68"/>
</a>
</g>
<g id="a_edge40&#45;label"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; adapter/service/collect.(*Collect).write (3.29s)">
<text text-anchor="middle" x="1844" y="-1242.3" font-family="Times,serif" font-size="14.00"> 3.29s</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N16 -->
<g id="edge61" class="edge"><title>N12&#45;&gt;N16</title>
<g id="a_edge61"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.selectgo (1.89s)">
<path fill="none" stroke="#b2aea5" d="M2043.29,-1304.91C2136.97,-1297.04 2361.39,-1272.96 2412,-1220 2441.59,-1189.04 2445.65,-1138.55 2444.02,-1102.36"/>
<polygon fill="#b2aea5" stroke="#b2aea5" points="2447.5,-1101.86 2443.39,-1092.09 2440.51,-1102.29 2447.5,-1101.86"/>
</a>
</g>
<g id="a_edge61&#45;label"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.selectgo (1.89s)">
<text text-anchor="middle" x="2459" y="-1182.3" font-family="Times,serif" font-size="14.00"> 1.89s</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N21 -->
<g id="edge85" class="edge"><title>N12&#45;&gt;N21</title>
<g id="a_edge85"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.slicebytetostring (0.87s)">
<path fill="none" stroke="#b2b1ac" d="M2009.43,-1277.26C2013.12,-1260.45 2017.16,-1239.21 2019,-1220 2021.88,-1189.92 2022.55,-1182.01 2019,-1152 2010.94,-1083.92 1998.81,-896.898 1947,-852 1910.4,-820.281 1588.01,-806.814 1455.5,-802.584"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="1455.26,-799.075 1445.16,-802.261 1455.05,-806.072 1455.26,-799.075"/>
</a>
</g>
<g id="a_edge85&#45;label"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.slicebytetostring (0.87s)">
<text text-anchor="middle" x="2031" y="-1054.3" font-family="Times,serif" font-size="14.00"> 0.87s</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N43 -->
<g id="edge91" class="edge"><title>N12&#45;&gt;N43</title>
<g id="a_edge91"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.convTstring (0.63s)">
<path fill="none" stroke="#b2b1ae" d="M2031.34,-1277.36C2037.16,-1270.11 2042.77,-1262.12 2047,-1254 2074.13,-1201.9 2088.44,-1134.54 2095.1,-1094.17"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2098.56,-1094.69 2096.67,-1084.26 2091.65,-1093.59 2098.56,-1094.69"/>
</a>
</g>
<g id="a_edge91&#45;label"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.convTstring (0.63s)">
<text text-anchor="middle" x="2100" y="-1182.3" font-family="Times,serif" font-size="14.00"> 0.63s</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N76 -->
<g id="edge95" class="edge"><title>N12&#45;&gt;N76</title>
<g id="a_edge95"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.convT64 (0.61s)">
<path fill="none" stroke="#b2b1ae" d="M2043.56,-1304.19C2081.78,-1298.74 2138.11,-1285.6 2176,-1254 2186.26,-1245.44 2193.89,-1233.1 2199.37,-1221.33"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2202.6,-1222.67 2203.3,-1212.1 2196.16,-1219.93 2202.6,-1222.67"/>
</a>
</g>
<g id="a_edge95&#45;label"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; runtime.convT64 (0.61s)">
<text text-anchor="middle" x="2207" y="-1242.3" font-family="Times,serif" font-size="14.00"> 0.61s</text>
</a>
</g>
</g>
<!-- N77 -->
<g id="node77" class="node"><title>N77</title>
<g id="a_node77"><a xlink:title="sync.(*Pool).Put (0.88s)">
<polygon fill="#edecec" stroke="#b2b1ac" points="1900,-1092 1810,-1092 1810,-1024 1900,-1024 1900,-1092"/>
<text text-anchor="middle" x="1855" y="-1079.2" font-family="Times,serif" font-size="11.00">sync</text>
<text text-anchor="middle" x="1855" y="-1067.2" font-family="Times,serif" font-size="11.00">(*Pool)</text>
<text text-anchor="middle" x="1855" y="-1055.2" font-family="Times,serif" font-size="11.00">Put</text>
<text text-anchor="middle" x="1855" y="-1043.2" font-family="Times,serif" font-size="11.00">0.28s (0.22%)</text>
<text text-anchor="middle" x="1855" y="-1031.2" font-family="Times,serif" font-size="11.00">of 0.88s (0.68%)</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N77 -->
<g id="edge111" class="edge"><title>N12&#45;&gt;N77</title>
<g id="a_edge111"><a xlink:title="adapter/service/collect.(*Collect).run ... sync.(*Pool).Put (0.38s)">
<path fill="none" stroke="#b2b2af" stroke-dasharray="1,5" d="M1995.45,-1277.3C1984.95,-1230.22 1963.54,-1143 1944,-1118 1934.43,-1105.75 1927.06,-1108.44 1914,-1100 1912.31,-1098.91 1910.6,-1097.79 1908.88,-1096.66"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="1910.47,-1093.51 1900.21,-1090.87 1906.58,-1099.33 1910.47,-1093.51"/>
</a>
</g>
<g id="a_edge111&#45;label"><a xlink:title="adapter/service/collect.(*Collect).run ... sync.(*Pool).Put (0.38s)">
<text text-anchor="middle" x="1998" y="-1182.3" font-family="Times,serif" font-size="14.00"> 0.38s</text>
</a>
</g>
</g>
<!-- N12&#45;&gt;N18 -->
<g id="edge121" class="edge"><title>N12&#45;&gt;N18</title>
<g id="a_edge121"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; github.com/ivanabc/log4go.Logger.intLogf (0.32s)">
<path fill="none" stroke="#b2b2b0" d="M1960.73,-1300.56C1885.33,-1286.89 1731.97,-1258.76 1721,-1254 1709.91,-1249.19 1710.3,-1242.29 1699,-1238 1624.38,-1209.69 1594.15,-1243.47 1514.26,-1219.77"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1514.85,-1216.28 1504.26,-1216.58 1512.73,-1222.95 1514.85,-1216.28"/>
</a>
</g>
<g id="a_edge121&#45;label"><a xlink:title="adapter/service/collect.(*Collect).run &#45;&gt; github.com/ivanabc/log4go.Logger.intLogf (0.32s)">
<text text-anchor="middle" x="1738" y="-1242.3" font-family="Times,serif" font-size="14.00"> 0.32s</text>
</a>
</g>
</g>
<!-- N13&#45;&gt;N1 -->
<g id="edge30" class="edge"><title>N13&#45;&gt;N1</title>
<g id="a_edge30"><a xlink:title="[adapter] ... runtime.mallocgc (4.47s)">
<path fill="none" stroke="#b2a793" stroke-dasharray="1,5" d="M1053.43,-636.842C1064.12,-627.57 1077.08,-617.44 1090,-610 1143.67,-579.104 1208.75,-554.684 1261.11,-537.827"/>
<polygon fill="#b2a793" stroke="#b2a793" points="1262.26,-541.133 1270.73,-534.767 1260.14,-534.462 1262.26,-541.133"/>
</a>
</g>
<g id="a_edge30&#45;label"><a xlink:title="[adapter] ... runtime.mallocgc (4.47s)">
<text text-anchor="middle" x="1170" y="-580.3" font-family="Times,serif" font-size="14.00"> 4.47s</text>
</a>
</g>
</g>
<!-- N14 -->
<g id="node14" class="node"><title>N14</title>
<g id="a_node14"><a xlink:title="runtime.scanobject (9.18s)">
<polygon fill="#ede9e4" stroke="#b29573" points="2392.25,-196 2261.75,-196 2261.75,-112 2392.25,-112 2392.25,-196"/>
<text text-anchor="middle" x="2327" y="-178.4" font-family="Times,serif" font-size="17.00">runtime</text>
<text text-anchor="middle" x="2327" y="-159.4" font-family="Times,serif" font-size="17.00">scanobject</text>
<text text-anchor="middle" x="2327" y="-140.4" font-family="Times,serif" font-size="17.00">5s (3.86%)</text>
<text text-anchor="middle" x="2327" y="-121.4" font-family="Times,serif" font-size="17.00">of 9.18s (7.09%)</text>
</a>
</g>
</g>
<!-- N51 -->
<g id="node51" class="node"><title>N51</title>
<g id="a_node51"><a xlink:title="runtime.greyobject (2.31s)">
<polygon fill="#edeceb" stroke="#b2ada2" points="2316.25,-56.5 2221.75,-56.5 2221.75,-3.5 2316.25,-3.5 2316.25,-56.5"/>
<text text-anchor="middle" x="2269" y="-41.3" font-family="Times,serif" font-size="14.00">runtime</text>
<text text-anchor="middle" x="2269" y="-26.3" font-family="Times,serif" font-size="14.00">greyobject</text>
<text text-anchor="middle" x="2269" y="-11.3" font-family="Times,serif" font-size="14.00">2.31s (1.79%)</text>
</a>
</g>
</g>
<!-- N14&#45;&gt;N51 -->
<g id="edge51" class="edge"><title>N14&#45;&gt;N51</title>
<g id="a_edge51"><a xlink:title="runtime.scanobject &#45;&gt; runtime.greyobject (2.22s)">
<path fill="none" stroke="#b2ada3" d="M2307.36,-111.696C2300.25,-96.726 2292.29,-79.9827 2285.47,-65.6445"/>
<polygon fill="#b2ada3" stroke="#b2ada3" points="2288.63,-64.1409 2281.18,-56.6124 2282.31,-67.1465 2288.63,-64.1409"/>
</a>
</g>
<g id="a_edge51&#45;label"><a xlink:title="runtime.scanobject &#45;&gt; runtime.greyobject (2.22s)">
<text text-anchor="middle" x="2315" y="-82.3" font-family="Times,serif" font-size="14.00"> 2.22s</text>
</a>
</g>
</g>
<!-- N40 -->
<g id="node40" class="node"><title>N40</title>
<g id="a_node40"><a xlink:title="runtime.findObject (2.70s)">
<polygon fill="#edecea" stroke="#b2ac9f" points="2437,-59.5 2335,-59.5 2335,-0.5 2437,-0.5 2437,-59.5"/>
<text text-anchor="middle" x="2386" y="-43.5" font-family="Times,serif" font-size="15.00">runtime</text>
<text text-anchor="middle" x="2386" y="-26.5" font-family="Times,serif" font-size="15.00">findObject</text>
<text text-anchor="middle" x="2386" y="-9.5" font-family="Times,serif" font-size="15.00">2.70s (2.09%)</text>
</a>
</g>
</g>
<!-- N14&#45;&gt;N40 -->
<g id="edge59" class="edge"><title>N14&#45;&gt;N40</title>
<g id="a_edge59"><a xlink:title="runtime.scanobject &#45;&gt; runtime.findObject (1.96s)">
<path fill="none" stroke="#b2aea4" d="M2346.97,-111.696C2353.71,-97.7672 2361.19,-82.3032 2367.78,-68.6697"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="2370.98,-70.0914 2372.19,-59.5651 2364.68,-67.0438 2370.98,-70.0914"/>
</a>
</g>
<g id="a_edge59&#45;label"><a xlink:title="runtime.scanobject &#45;&gt; runtime.findObject (1.96s)">
<text text-anchor="middle" x="2379" y="-82.3" font-family="Times,serif" font-size="14.00"> 1.96s</text>
</a>
</g>
</g>
<!-- N15 -->
<g id="node15" class="node"><title>N15</title>
<g id="a_node15"><a xlink:title="syscall.Syscall (12.02s)">
<polygon fill="#ede7e2" stroke="#b2895f" points="1275,-714 1099,-714 1099,-610 1275,-610 1275,-714"/>
<text text-anchor="middle" x="1187" y="-692.4" font-family="Times,serif" font-size="22.00">syscall</text>
<text text-anchor="middle" x="1187" y="-668.4" font-family="Times,serif" font-size="22.00">Syscall</text>
<text text-anchor="middle" x="1187" y="-644.4" font-family="Times,serif" font-size="22.00">11.68s (9.03%)</text>
<text text-anchor="middle" x="1187" y="-620.4" font-family="Times,serif" font-size="22.00">of 12.02s (9.29%)</text>
</a>
</g>
</g>
<!-- N46 -->
<g id="node46" class="node"><title>N46</title>
<g id="a_node46"><a xlink:title="runtime.lock2 (1.66s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="2601.25,-957 2496.75,-957 2496.75,-893 2601.25,-893 2601.25,-957"/>
<text text-anchor="middle" x="2549" y="-942.6" font-family="Times,serif" font-size="13.00">runtime</text>
<text text-anchor="middle" x="2549" y="-928.6" font-family="Times,serif" font-size="13.00">lock2</text>
<text text-anchor="middle" x="2549" y="-914.6" font-family="Times,serif" font-size="13.00">1.12s (0.87%)</text>
<text text-anchor="middle" x="2549" y="-900.6" font-family="Times,serif" font-size="13.00">of 1.66s (1.28%)</text>
</a>
</g>
</g>
<!-- N16&#45;&gt;N46 -->
<g id="edge94" class="edge"><title>N16&#45;&gt;N46</title>
<g id="a_edge94"><a xlink:title="runtime.selectgo ... runtime.lock2 (0.62s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M2466.76,-1023.94C2481.79,-1006.04 2500.46,-983.801 2516.21,-965.045"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2519.02,-967.151 2522.77,-957.243 2513.65,-962.65 2519.02,-967.151"/>
</a>
</g>
<g id="a_edge94&#45;label"><a xlink:title="runtime.selectgo ... runtime.lock2 (0.62s)">
<text text-anchor="middle" x="2518" y="-986.3" font-family="Times,serif" font-size="14.00"> 0.62s</text>
</a>
</g>
</g>
<!-- N61 -->
<g id="node61" class="node"><title>N61</title>
<g id="a_node61"><a xlink:title="runtime.unlock2 (1.13s)">
<polygon fill="#edecec" stroke="#b2b0aa" points="2478,-955 2382,-955 2382,-895 2478,-895 2478,-955"/>
<text text-anchor="middle" x="2430" y="-941.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="2430" y="-928.4" font-family="Times,serif" font-size="12.00">unlock2</text>
<text text-anchor="middle" x="2430" y="-915.4" font-family="Times,serif" font-size="12.00">0.97s (0.75%)</text>
<text text-anchor="middle" x="2430" y="-902.4" font-family="Times,serif" font-size="12.00">of 1.13s (0.87%)</text>
</a>
</g>
</g>
<!-- N16&#45;&gt;N61 -->
<g id="edge99" class="edge"><title>N16&#45;&gt;N61</title>
<g id="a_edge99"><a xlink:title="runtime.selectgo ... runtime.unlock2 (0.58s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M2436.73,-1023.94C2435.5,-1006.04 2433.97,-983.801 2432.68,-965.045"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2436.17,-964.769 2431.99,-955.033 2429.19,-965.249 2436.17,-964.769"/>
</a>
</g>
<g id="a_edge99&#45;label"><a xlink:title="runtime.selectgo ... runtime.unlock2 (0.58s)">
<text text-anchor="middle" x="2452" y="-986.3" font-family="Times,serif" font-size="14.00"> 0.58s</text>
</a>
</g>
</g>
<!-- N37 -->
<g id="node37" class="node"><title>N37</title>
<g id="a_node37"><a xlink:title="runtime.gcWriteBarrier (1.69s)">
<polygon fill="#edeceb" stroke="#b2afa6" points="2327,-690 2237,-690 2237,-634 2327,-634 2327,-690"/>
<text text-anchor="middle" x="2282" y="-677.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="2282" y="-665.2" font-family="Times,serif" font-size="11.00">gcWriteBarrier</text>
<text text-anchor="middle" x="2282" y="-653.2" font-family="Times,serif" font-size="11.00">0.58s (0.45%)</text>
<text text-anchor="middle" x="2282" y="-641.2" font-family="Times,serif" font-size="11.00">of 1.69s (1.31%)</text>
</a>
</g>
</g>
<!-- N16&#45;&gt;N37 -->
<g id="edge114" class="edge"><title>N16&#45;&gt;N37</title>
<g id="a_edge114"><a xlink:title="runtime.selectgo ... runtime.gcWriteBarrier (0.37s)">
<path fill="none" stroke="#b2b2af" stroke-dasharray="1,5" d="M2411.43,-1023.78C2398.35,-1006.74 2383.36,-985.159 2373,-964 2328.94,-874.039 2301.29,-758.409 2289.16,-700.119"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="2292.58,-699.353 2287.15,-690.257 2285.72,-700.756 2292.58,-699.353"/>
</a>
</g>
<g id="a_edge114&#45;label"><a xlink:title="runtime.selectgo ... runtime.gcWriteBarrier (0.37s)">
<text text-anchor="middle" x="2351" y="-856.3" font-family="Times,serif" font-size="14.00"> 0.37s</text>
</a>
</g>
</g>
<!-- N17 -->
<g id="node17" class="node"><title>N17</title>
<g id="a_node17"><a xlink:title="runtime.mcall (14.79s)">
<polygon fill="#ede6df" stroke="#b27d4c" points="2824,-1456 2738,-1456 2738,-1408 2824,-1408 2824,-1456"/>
<text text-anchor="middle" x="2781" y="-1444.8" font-family="Times,serif" font-size="9.00">runtime</text>
<text text-anchor="middle" x="2781" y="-1434.8" font-family="Times,serif" font-size="9.00">mcall</text>
<text text-anchor="middle" x="2781" y="-1424.8" font-family="Times,serif" font-size="9.00">0.03s (0.023%)</text>
<text text-anchor="middle" x="2781" y="-1414.8" font-family="Times,serif" font-size="9.00">of 14.79s (11.43%)</text>
</a>
</g>
</g>
<!-- N27 -->
<g id="node27" class="node"><title>N27</title>
<g id="a_node27"><a xlink:title="runtime.schedule (14.35s)">
<polygon fill="#ede6e0" stroke="#b27f4f" points="2828,-1212 2734,-1212 2734,-1160 2828,-1160 2828,-1212"/>
<text text-anchor="middle" x="2781" y="-1200" font-family="Times,serif" font-size="10.00">runtime</text>
<text text-anchor="middle" x="2781" y="-1189" font-family="Times,serif" font-size="10.00">schedule</text>
<text text-anchor="middle" x="2781" y="-1178" font-family="Times,serif" font-size="10.00">0.27s (0.21%)</text>
<text text-anchor="middle" x="2781" y="-1167" font-family="Times,serif" font-size="10.00">of 14.35s (11.09%)</text>
</a>
</g>
</g>
<!-- N17&#45;&gt;N27 -->
<g id="edge10" class="edge"><title>N17&#45;&gt;N27</title>
<g id="a_edge10"><a xlink:title="runtime.mcall ... runtime.schedule (13.33s)">
<path fill="none" stroke="#b28456" stroke-dasharray="1,5" d="M2781,-1407.6C2781,-1364.62 2781,-1272.86 2781,-1222.28"/>
<polygon fill="#b28456" stroke="#b28456" points="2784.5,-1222.14 2781,-1212.14 2777.5,-1222.14 2784.5,-1222.14"/>
</a>
</g>
<g id="a_edge10&#45;label"><a xlink:title="runtime.mcall ... runtime.schedule (13.33s)">
<text text-anchor="middle" x="2801" y="-1305.3" font-family="Times,serif" font-size="14.00"> 13.33s</text>
</a>
</g>
</g>
<!-- N79 -->
<g id="node79" class="node"><title>N79</title>
<g id="a_node79"><a xlink:title="runtime.goschedImpl (1.63s)">
<polygon fill="#edeceb" stroke="#b2afa7" points="2744.25,-1333 2667.75,-1333 2667.75,-1285 2744.25,-1285 2744.25,-1333"/>
<text text-anchor="middle" x="2706" y="-1321.8" font-family="Times,serif" font-size="9.00">runtime</text>
<text text-anchor="middle" x="2706" y="-1311.8" font-family="Times,serif" font-size="9.00">goschedImpl</text>
<text text-anchor="middle" x="2706" y="-1301.8" font-family="Times,serif" font-size="9.00">0.03s (0.023%)</text>
<text text-anchor="middle" x="2706" y="-1291.8" font-family="Times,serif" font-size="9.00">of 1.63s (1.26%)</text>
</a>
</g>
</g>
<!-- N17&#45;&gt;N79 -->
<g id="edge82" class="edge"><title>N17&#45;&gt;N79</title>
<g id="a_edge82"><a xlink:title="runtime.mcall ... runtime.goschedImpl (0.95s)">
<path fill="none" stroke="#b2b0ab" stroke-dasharray="1,5" d="M2755.72,-1407.97C2747.69,-1399.72 2739.28,-1389.98 2733,-1380 2725.85,-1368.63 2720.07,-1355.06 2715.73,-1342.91"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="2718.94,-1341.47 2712.42,-1333.12 2712.31,-1343.71 2718.94,-1341.47"/>
</a>
</g>
<g id="a_edge82&#45;label"><a xlink:title="runtime.mcall ... runtime.goschedImpl (0.95s)">
<text text-anchor="middle" x="2750" y="-1368.3" font-family="Times,serif" font-size="14.00"> 0.95s</text>
</a>
</g>
</g>
<!-- N80 -->
<g id="node80" class="node"><title>N80</title>
<g id="a_node80"><a xlink:title="runtime.Caller (2.07s)">
<polygon fill="#edeceb" stroke="#b2aea4" points="1340.25,-1082 1263.75,-1082 1263.75,-1034 1340.25,-1034 1340.25,-1082"/>
<text text-anchor="middle" x="1302" y="-1070.8" font-family="Times,serif" font-size="9.00">runtime</text>
<text text-anchor="middle" x="1302" y="-1060.8" font-family="Times,serif" font-size="9.00">Caller</text>
<text text-anchor="middle" x="1302" y="-1050.8" font-family="Times,serif" font-size="9.00">0.04s (0.031%)</text>
<text text-anchor="middle" x="1302" y="-1040.8" font-family="Times,serif" font-size="9.00">of 2.07s (1.60%)</text>
</a>
</g>
</g>
<!-- N18&#45;&gt;N80 -->
<g id="edge53" class="edge"><title>N18&#45;&gt;N80</title>
<g id="a_edge53"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; runtime.Caller (2.07s)">
<path fill="none" stroke="#b2aea4" d="M1413.68,-1157.6C1409.15,-1155.49 1404.55,-1153.57 1400,-1152 1375.76,-1143.61 1302.66,-1153.5 1286,-1134 1276,-1122.29 1278.3,-1105.85 1283.83,-1091.35"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="1287.04,-1092.73 1287.83,-1082.17 1280.63,-1089.93 1287.04,-1092.73"/>
</a>
</g>
<g id="a_edge53&#45;label"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; runtime.Caller (2.07s)">
<text text-anchor="middle" x="1303" y="-1122.3" font-family="Times,serif" font-size="14.00"> 2.07s</text>
</a>
</g>
</g>
<!-- N18&#45;&gt;N24 -->
<g id="edge83" class="edge"><title>N18&#45;&gt;N24</title>
<g id="a_edge83"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; fmt.Sprintf (0.94s)">
<path fill="none" stroke="#b2b1ac" d="M1502.61,-1151.69C1517.92,-1140.52 1535.43,-1128.29 1552,-1118 1566.53,-1108.98 1571.51,-1109.07 1586,-1100 1590.97,-1096.89 1596.08,-1093.47 1601.09,-1089.99"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="1603.26,-1092.74 1609.39,-1084.1 1599.21,-1087.03 1603.26,-1092.74"/>
</a>
</g>
<g id="a_edge83&#45;label"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; fmt.Sprintf (0.94s)">
<text text-anchor="middle" x="1569" y="-1122.3" font-family="Times,serif" font-size="14.00"> 0.94s</text>
</a>
</g>
</g>
<!-- N18&#45;&gt;N70 -->
<g id="edge112" class="edge"><title>N18&#45;&gt;N70</title>
<g id="a_edge112"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; sync.(*Pool).Get (0.38s)">
<path fill="none" stroke="#b2b2af" d="M1465,-1151.93C1473.43,-1105.49 1488.78,-1020.88 1498.12,-969.419"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="1501.62,-969.742 1499.96,-959.278 1494.73,-968.492 1501.62,-969.742"/>
</a>
</g>
<g id="a_edge112&#45;label"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf &#45;&gt; sync.(*Pool).Get (0.38s)">
<text text-anchor="middle" x="1507" y="-1054.3" font-family="Times,serif" font-size="14.00"> 0.38s</text>
</a>
</g>
</g>
<!-- N18&#45;&gt;N46 -->
<g id="edge152" class="edge"><title>N18&#45;&gt;N46</title>
<g id="a_edge152"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf ... runtime.lock2 (0.13s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1504.2,-1155.22C1507.11,-1153.98 1510.06,-1152.89 1513,-1152 1561.53,-1137.31 1933.86,-1170.55 1969,-1134 1973.93,-1128.87 1971.81,-1124.53 1969,-1118 1964.4,-1107.31 1954.6,-1110.69 1950,-1100 1935.25,-1065.7 1924.91,-1043.64 1950,-1016 2030.59,-927.232 2373.32,-1002.08 2487,-964 2489.41,-963.193 2491.82,-962.278 2494.23,-961.277"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="2495.79,-964.408 2503.4,-957.037 2492.86,-958.054 2495.79,-964.408"/>
</a>
</g>
<g id="a_edge152&#45;label"><a xlink:title="github.com/ivanabc/log4go.Logger.intLogf ... runtime.lock2 (0.13s)">
<text text-anchor="middle" x="1967" y="-1054.3" font-family="Times,serif" font-size="14.00"> 0.13s</text>
</a>
</g>
</g>
<!-- N34 -->
<g id="node34" class="node"><title>N34</title>
<g id="a_node34"><a xlink:title="github.com/json&#45;iterator/go.(*placeholderDecoder).Decode (8.59s)">
<polygon fill="#ede9e5" stroke="#b29877" points="690,-1217.5 580,-1217.5 580,-1154.5 690,-1154.5 690,-1217.5"/>
<text text-anchor="middle" x="635" y="-1205.5" font-family="Times,serif" font-size="10.00">go</text>
<text text-anchor="middle" x="635" y="-1194.5" font-family="Times,serif" font-size="10.00">(*placeholderDecoder)</text>
<text text-anchor="middle" x="635" y="-1183.5" font-family="Times,serif" font-size="10.00">Decode</text>
<text text-anchor="middle" x="635" y="-1172.5" font-family="Times,serif" font-size="10.00">0.18s (0.14%)</text>
<text text-anchor="middle" x="635" y="-1161.5" font-family="Times,serif" font-size="10.00">of 8.59s (6.64%)</text>
</a>
</g>
</g>
<!-- N19&#45;&gt;N34 -->
<g id="edge15" class="edge"><title>N19&#45;&gt;N34</title>
<g id="a_edge15"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*placeholderDecoder).Decode (8.59s)">
<path fill="none" stroke="#b29877" d="M635,-1272.35C635,-1258.44 635,-1242.41 635,-1228.02"/>
<polygon fill="#b29877" stroke="#b29877" points="638.5,-1227.89 635,-1217.89 631.5,-1227.89 638.5,-1227.89"/>
</a>
</g>
<g id="a_edge15&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*placeholderDecoder).Decode (8.59s)">
<text text-anchor="middle" x="652" y="-1242.3" font-family="Times,serif" font-size="14.00"> 8.59s</text>
</a>
</g>
</g>
<!-- N45 -->
<g id="node45" class="node"><title>N45</title>
<g id="a_node45"><a xlink:title="github.com/json&#45;iterator/go.(*sliceDecoder).Decode (4.14s)">
<polygon fill="#edebe9" stroke="#b2a895" points="364.25,-1080 287.75,-1080 287.75,-1036 364.25,-1036 364.25,-1080"/>
<text text-anchor="middle" x="326" y="-1069.6" font-family="Times,serif" font-size="8.00">go</text>
<text text-anchor="middle" x="326" y="-1060.6" font-family="Times,serif" font-size="8.00">(*sliceDecoder)</text>
<text text-anchor="middle" x="326" y="-1051.6" font-family="Times,serif" font-size="8.00">Decode</text>
<text text-anchor="middle" x="326" y="-1042.6" font-family="Times,serif" font-size="8.00">0 of 4.14s (3.20%)</text>
</a>
</g>
</g>
<!-- N19&#45;&gt;N45 -->
<g id="edge81" class="edge"><title>N19&#45;&gt;N45</title>
<g id="a_edge81"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).Decode (0.97s)">
<path fill="none" stroke="#b2b0ab" d="M590.728,-1272.32C528.537,-1222.21 416.627,-1132.03 360.326,-1086.66"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="362.418,-1083.85 352.435,-1080.3 358.025,-1089.3 362.418,-1083.85"/>
</a>
</g>
<g id="a_edge81&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).Decode (0.97s)">
<text text-anchor="middle" x="539" y="-1182.3" font-family="Times,serif" font-size="14.00"> 0.97s</text>
</a>
</g>
</g>
<!-- N26 -->
<g id="node26" class="node"><title>N26</title>
<g id="a_node26"><a xlink:title="github.com/json&#45;iterator/go.(*OptionalDecoder).Decode (4.85s)">
<polygon fill="#edebe8" stroke="#b2a691" points="344.25,-831.5 245.75,-831.5 245.75,-768.5 344.25,-768.5 344.25,-831.5"/>
<text text-anchor="middle" x="295" y="-819.5" font-family="Times,serif" font-size="10.00">go</text>
<text text-anchor="middle" x="295" y="-808.5" font-family="Times,serif" font-size="10.00">(*OptionalDecoder)</text>
<text text-anchor="middle" x="295" y="-797.5" font-family="Times,serif" font-size="10.00">Decode</text>
<text text-anchor="middle" x="295" y="-786.5" font-family="Times,serif" font-size="10.00">0.25s (0.19%)</text>
<text text-anchor="middle" x="295" y="-775.5" font-family="Times,serif" font-size="10.00">of 4.85s (3.75%)</text>
</a>
</g>
</g>
<!-- N19&#45;&gt;N26 -->
<g id="edge108" class="edge"><title>N19&#45;&gt;N26</title>
<g id="a_edge108"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*OptionalDecoder).Decode (0.41s)">
<path fill="none" stroke="#b2b1af" d="M574.937,-1296.92C483.571,-1276.4 313.011,-1222.84 244,-1100 236.364,-1086.41 237.423,-917.094 246,-886 250.384,-870.106 258.459,-854.079 266.806,-840.292"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="269.926,-841.906 272.294,-831.579 264.003,-838.175 269.926,-841.906"/>
</a>
</g>
<g id="a_edge108&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*OptionalDecoder).Decode (0.41s)">
<text text-anchor="middle" x="261" y="-1054.3" font-family="Times,serif" font-size="14.00"> 0.41s</text>
</a>
</g>
</g>
<!-- N19&#45;&gt;N13 -->
<g id="edge133" class="edge"><title>N19&#45;&gt;N13</title>
<g id="a_edge133"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; [adapter] (0.25s)">
<path fill="none" stroke="#b2b2b0" d="M669.001,-1272.31C696.805,-1238.69 727.284,-1188.23 699,-1152 678.701,-1126 658.669,-1143.22 627,-1134 581.12,-1120.64 565.785,-1124.9 525,-1100 506.558,-1088.74 394.378,-983.917 386,-964 372.559,-932.045 375.395,-919.005 386,-886 406.074,-823.531 419.686,-805.606 472,-766 585.13,-680.351 646.592,-734.489 787,-714 848.238,-705.064 917.25,-689.934 965.68,-678.469"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="966.776,-681.806 975.692,-676.082 965.152,-674.997 966.776,-681.806"/>
</a>
</g>
<g id="a_edge133&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*structFieldDecoder).Decode &#45;&gt; [adapter] (0.25s)">
<text text-anchor="middle" x="431" y="-986.3" font-family="Times,serif" font-size="14.00"> 0.25s</text>
</a>
</g>
</g>
<!-- N23 -->
<g id="node23" class="node"><title>N23</title>
<g id="a_node23"><a xlink:title="runtime.cgocall (17.74s)">
<polygon fill="#ede4dc" stroke="#b26f38" points="1851.25,-2202 1648.75,-2202 1648.75,-2090 1851.25,-2090 1851.25,-2202"/>
<text text-anchor="middle" x="1750" y="-2178.8" font-family="Times,serif" font-size="24.00">runtime</text>
<text text-anchor="middle" x="1750" y="-2152.8" font-family="Times,serif" font-size="24.00">cgocall</text>
<text text-anchor="middle" x="1750" y="-2126.8" font-family="Times,serif" font-size="24.00">17.50s (13.52%)</text>
<text text-anchor="middle" x="1750" y="-2100.8" font-family="Times,serif" font-size="24.00">of 17.74s (13.71%)</text>
</a>
</g>
</g>
<!-- N20&#45;&gt;N23 -->
<g id="edge7" class="edge"><title>N20&#45;&gt;N23</title>
<g id="a_edge7"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... runtime.cgocall (17.74s)">
<path fill="none" stroke="#b26f38" stroke-dasharray="1,5" d="M1646.62,-2256.74C1659.5,-2243.06 1675.52,-2226.06 1691.07,-2209.55"/>
<polygon fill="#b26f38" stroke="#b26f38" points="1693.86,-2211.7 1698.17,-2202.02 1688.77,-2206.9 1693.86,-2211.7"/>
</a>
</g>
<g id="a_edge7&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... runtime.cgocall (17.74s)">
<text text-anchor="middle" x="1702" y="-2224.3" font-family="Times,serif" font-size="14.00"> 17.74s</text>
</a>
</g>
</g>
<!-- N20&#45;&gt;N18 -->
<g id="edge32" class="edge"><title>N20&#45;&gt;N18</title>
<g id="a_edge32"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... github.com/ivanabc/log4go.Logger.intLogf (4.30s)">
<path fill="none" stroke="#b2a794" stroke-dasharray="1,5" d="M1620,-2256.98C1620,-2229.1 1620,-2185.09 1620,-2147 1620,-2147 1620,-2147 1620,-1843 1620,-1715.51 1546.28,-1701.18 1501,-1582 1482.33,-1532.87 1484.91,-1518.1 1478,-1466 1467,-1383.03 1462.2,-1285.29 1460.23,-1230.29"/>
<polygon fill="#b2a794" stroke="#b2a794" points="1463.73,-1230.08 1459.89,-1220.21 1456.73,-1230.32 1463.73,-1230.08"/>
</a>
</g>
<g id="a_edge32&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... github.com/ivanabc/log4go.Logger.intLogf (4.30s)">
<text text-anchor="middle" x="1606" y="-1714.3" font-family="Times,serif" font-size="14.00"> 4.30s</text>
</a>
</g>
</g>
<!-- N20&#45;&gt;N76 -->
<g id="edge141" class="edge"><title>N20&#45;&gt;N76</title>
<g id="a_edge141"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... runtime.convT64 (0.17s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1667.13,-2284.67C1812.94,-2282.41 2247,-2265.7 2247,-2147 2247,-2147 2247,-2147 2247,-1308 2247,-1277.95 2236.67,-1245.38 2227.08,-1221.61"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="2230.28,-1220.18 2223.19,-1212.31 2223.82,-1222.89 2230.28,-1220.18"/>
</a>
</g>
<g id="a_edge141&#45;label"><a xlink:title="adapter/service/kafka_consumer.(*PartitionConsumer).SetOffset ... runtime.convT64 (0.17s)">
<text text-anchor="middle" x="2264" y="-1714.3" font-family="Times,serif" font-size="14.00"> 0.17s</text>
</a>
</g>
</g>
<!-- N21&#45;&gt;N1 -->
<g id="edge29" class="edge"><title>N21&#45;&gt;N1</title>
<g id="a_edge29"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.mallocgc (4.67s)">
<path fill="none" stroke="#b2a692" d="M1394.64,-769.8C1387.61,-722.689 1373.63,-629.057 1364.55,-568.276"/>
<polygon fill="#b2a692" stroke="#b2a692" points="1367.97,-567.446 1363.03,-558.072 1361.04,-568.479 1367.97,-567.446"/>
</a>
</g>
<g id="a_edge29&#45;label"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.mallocgc (4.67s)">
<text text-anchor="middle" x="1403" y="-658.3" font-family="Times,serif" font-size="14.00"> 4.67s</text>
</a>
</g>
</g>
<!-- N32 -->
<g id="node32" class="node"><title>N32</title>
<g id="a_node32"><a xlink:title="runtime.memmove (2.41s)">
<polygon fill="#edecea" stroke="#b2ada1" points="1968.25,-688.5 1873.75,-688.5 1873.75,-635.5 1968.25,-635.5 1968.25,-688.5"/>
<text text-anchor="middle" x="1921" y="-673.3" font-family="Times,serif" font-size="14.00">runtime</text>
<text text-anchor="middle" x="1921" y="-658.3" font-family="Times,serif" font-size="14.00">memmove</text>
<text text-anchor="middle" x="1921" y="-643.3" font-family="Times,serif" font-size="14.00">2.41s (1.86%)</text>
</a>
</g>
</g>
<!-- N21&#45;&gt;N32 -->
<g id="edge92" class="edge"><title>N21&#45;&gt;N32</title>
<g id="a_edge92"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.memmove (0.63s)">
<path fill="none" stroke="#b2b1ae" d="M1445.32,-786.931C1540.92,-762.024 1759.82,-704.994 1863.94,-677.866"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1864.86,-681.244 1873.65,-675.336 1863.09,-674.47 1864.86,-681.244"/>
</a>
</g>
<g id="a_edge92&#45;label"><a xlink:title="runtime.slicebytetostring &#45;&gt; runtime.memmove (0.63s)">
<text text-anchor="middle" x="1662" y="-736.3" font-family="Times,serif" font-size="14.00"> 0.63s</text>
</a>
</g>
</g>
<!-- N22 -->
<g id="node22" class="node"><title>N22</title>
<g id="a_node22"><a xlink:title="runtime.findrunnable (12.05s)">
<polygon fill="#ede7e2" stroke="#b2895f" points="2828.25,-1086 2733.75,-1086 2733.75,-1030 2828.25,-1030 2828.25,-1086"/>
<text text-anchor="middle" x="2781" y="-1073.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="2781" y="-1061.2" font-family="Times,serif" font-size="11.00">findrunnable</text>
<text text-anchor="middle" x="2781" y="-1049.2" font-family="Times,serif" font-size="11.00">0.41s (0.32%)</text>
<text text-anchor="middle" x="2781" y="-1037.2" font-family="Times,serif" font-size="11.00">of 12.05s (9.31%)</text>
</a>
</g>
</g>
<!-- N54 -->
<g id="node54" class="node"><title>N54</title>
<g id="a_node54"><a xlink:title="runtime.stealWork (7.24s)">
<polygon fill="#edeae6" stroke="#b29d80" points="2829,-955 2733,-955 2733,-895 2829,-895 2829,-955"/>
<text text-anchor="middle" x="2781" y="-941.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="2781" y="-928.4" font-family="Times,serif" font-size="12.00">stealWork</text>
<text text-anchor="middle" x="2781" y="-915.4" font-family="Times,serif" font-size="12.00">0.88s (0.68%)</text>
<text text-anchor="middle" x="2781" y="-902.4" font-family="Times,serif" font-size="12.00">of 7.24s (5.60%)</text>
</a>
</g>
</g>
<!-- N22&#45;&gt;N54 -->
<g id="edge17" class="edge"><title>N22&#45;&gt;N54</title>
<g id="a_edge17"><a xlink:title="runtime.findrunnable &#45;&gt; runtime.stealWork (7.24s)">
<path fill="none" stroke="#b29d80" d="M2781,-1029.86C2781,-1011.26 2781,-986.306 2781,-965.516"/>
<polygon fill="#b29d80" stroke="#b29d80" points="2784.5,-965.386 2781,-955.386 2777.5,-965.386 2784.5,-965.386"/>
</a>
</g>
<g id="a_edge17&#45;label"><a xlink:title="runtime.findrunnable &#45;&gt; runtime.stealWork (7.24s)">
<text text-anchor="middle" x="2798" y="-986.3" font-family="Times,serif" font-size="14.00"> 7.24s</text>
</a>
</g>
</g>
<!-- N55 -->
<g id="node55" class="node"><title>N55</title>
<g id="a_node55"><a xlink:title="runtime.epollwait (2.29s)">
<polygon fill="#edeceb" stroke="#b2ada2" points="2714.25,-951.5 2619.75,-951.5 2619.75,-898.5 2714.25,-898.5 2714.25,-951.5"/>
<text text-anchor="middle" x="2667" y="-936.3" font-family="Times,serif" font-size="14.00">runtime</text>
<text text-anchor="middle" x="2667" y="-921.3" font-family="Times,serif" font-size="14.00">epollwait</text>
<text text-anchor="middle" x="2667" y="-906.3" font-family="Times,serif" font-size="14.00">2.29s (1.77%)</text>
</a>
</g>
</g>
<!-- N22&#45;&gt;N55 -->
<g id="edge49" class="edge"><title>N22&#45;&gt;N55</title>
<g id="a_edge49"><a xlink:title="runtime.findrunnable ... runtime.epollwait (2.26s)">
<path fill="none" stroke="#b2ada2" stroke-dasharray="1,5" d="M2757.38,-1029.86C2739.61,-1009.43 2715.15,-981.326 2696.15,-959.502"/>
<polygon fill="#b2ada2" stroke="#b2ada2" points="2698.63,-957.017 2689.43,-951.771 2693.35,-961.613 2698.63,-957.017"/>
</a>
</g>
<g id="a_edge49&#45;label"><a xlink:title="runtime.findrunnable ... runtime.epollwait (2.26s)">
<text text-anchor="middle" x="2745" y="-986.3" font-family="Times,serif" font-size="14.00"> 2.26s</text>
</a>
</g>
</g>
<!-- N22&#45;&gt;N46 -->
<g id="edge149" class="edge"><title>N22&#45;&gt;N46</title>
<g id="a_edge149"><a xlink:title="runtime.findrunnable ... runtime.lock2 (0.15s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M2745.97,-1029.86C2738.89,-1024.93 2731.36,-1020.08 2724,-1016 2705.52,-1005.76 2699.29,-1006.62 2680,-998 2648.42,-983.889 2640.28,-980.722 2610,-964 2609.02,-963.457 2608.03,-962.905 2607.03,-962.346"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="2608.42,-959.106 2598.01,-957.145 2604.93,-965.171 2608.42,-959.106"/>
</a>
</g>
<g id="a_edge149&#45;label"><a xlink:title="runtime.findrunnable ... runtime.lock2 (0.15s)">
<text text-anchor="middle" x="2697" y="-986.3" font-family="Times,serif" font-size="14.00"> 0.15s</text>
</a>
</g>
</g>
<!-- N75 -->
<g id="node75" class="node"><title>N75</title>
<g id="a_node75"><a xlink:title="fmt.(*pp).doPrintf (2.67s)">
<polygon fill="#edecea" stroke="#b2aca0" points="1834,-961.5 1738,-961.5 1738,-888.5 1834,-888.5 1834,-961.5"/>
<text text-anchor="middle" x="1786" y="-947.9" font-family="Times,serif" font-size="12.00">fmt</text>
<text text-anchor="middle" x="1786" y="-934.9" font-family="Times,serif" font-size="12.00">(*pp)</text>
<text text-anchor="middle" x="1786" y="-921.9" font-family="Times,serif" font-size="12.00">doPrintf</text>
<text text-anchor="middle" x="1786" y="-908.9" font-family="Times,serif" font-size="12.00">0.62s (0.48%)</text>
<text text-anchor="middle" x="1786" y="-895.9" font-family="Times,serif" font-size="12.00">of 2.67s (2.06%)</text>
</a>
</g>
</g>
<!-- N24&#45;&gt;N75 -->
<g id="edge45" class="edge"><title>N24&#45;&gt;N75</title>
<g id="a_edge45"><a xlink:title="fmt.Sprintf &#45;&gt; fmt.(*pp).doPrintf (2.67s)">
<path fill="none" stroke="#b2aca0" d="M1669.76,-1031.74C1689.44,-1013.84 1716.32,-989.39 1739.27,-968.51"/>
<polygon fill="#b2aca0" stroke="#b2aca0" points="1741.85,-970.898 1746.89,-961.58 1737.14,-965.72 1741.85,-970.898"/>
</a>
</g>
<g id="a_edge45&#45;label"><a xlink:title="fmt.Sprintf &#45;&gt; fmt.(*pp).doPrintf (2.67s)">
<text text-anchor="middle" x="1740" y="-986.3" font-family="Times,serif" font-size="14.00"> 2.67s</text>
</a>
</g>
</g>
<!-- N24&#45;&gt;N21 -->
<g id="edge98" class="edge"><title>N24&#45;&gt;N21</title>
<g id="a_edge98"><a xlink:title="fmt.Sprintf &#45;&gt; runtime.slicebytetostring (0.59s)">
<path fill="none" stroke="#b2b1ae" d="M1643.65,-1031.7C1644.98,-988.689 1641.1,-901.188 1593,-852 1573.92,-832.491 1505.51,-817.756 1455.31,-809.261"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1455.65,-805.77 1445.21,-807.594 1454.51,-812.677 1455.65,-805.77"/>
</a>
</g>
<g id="a_edge98&#45;label"><a xlink:title="fmt.Sprintf &#45;&gt; runtime.slicebytetostring (0.59s)">
<text text-anchor="middle" x="1657" y="-921.3" font-family="Times,serif" font-size="14.00"> 0.59s</text>
</a>
</g>
</g>
<!-- N24&#45;&gt;N70 -->
<g id="edge127" class="edge"><title>N24&#45;&gt;N70</title>
<g id="a_edge127"><a xlink:title="fmt.Sprintf ... sync.(*Pool).Get (0.29s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1615.78,-1031.74C1596.5,-1013.17 1569.91,-987.556 1547.72,-966.188"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1550.01,-963.537 1540.38,-959.12 1545.16,-968.579 1550.01,-963.537"/>
</a>
</g>
<g id="a_edge127&#45;label"><a xlink:title="fmt.Sprintf ... sync.(*Pool).Get (0.29s)">
<text text-anchor="middle" x="1596" y="-986.3" font-family="Times,serif" font-size="14.00"> 0.29s</text>
</a>
</g>
</g>
<!-- N35 -->
<g id="node35" class="node"><title>N35</title>
<g id="a_node35"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).Decode (18.02s)">
<polygon fill="#ede4dc" stroke="#b26d36" points="745,-1581.5 629,-1581.5 629,-1518.5 745,-1518.5 745,-1581.5"/>
<text text-anchor="middle" x="687" y="-1569.5" font-family="Times,serif" font-size="10.00">go</text>
<text text-anchor="middle" x="687" y="-1558.5" font-family="Times,serif" font-size="10.00">(*generalStructDecoder)</text>
<text text-anchor="middle" x="687" y="-1547.5" font-family="Times,serif" font-size="10.00">Decode</text>
<text text-anchor="middle" x="687" y="-1536.5" font-family="Times,serif" font-size="10.00">0.12s (0.093%)</text>
<text text-anchor="middle" x="687" y="-1525.5" font-family="Times,serif" font-size="10.00">of 18.02s (13.93%)</text>
</a>
</g>
</g>
<!-- N25&#45;&gt;N35 -->
<g id="edge6" class="edge"><title>N25&#45;&gt;N35</title>
<g id="a_edge6"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal &#45;&gt; github.com/json&#45;iterator/go.(*generalStructDecoder).Decode (18.02s)">
<path fill="none" stroke="#b26d36" d="M687,-1633.99C687,-1621.3 687,-1606.04 687,-1592.09"/>
<polygon fill="#b26d36" stroke="#b26d36" points="690.5,-1591.74 687,-1581.74 683.5,-1591.74 690.5,-1591.74"/>
</a>
</g>
<g id="a_edge6&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal &#45;&gt; github.com/json&#45;iterator/go.(*generalStructDecoder).Decode (18.02s)">
<text text-anchor="middle" x="707" y="-1604.3" font-family="Times,serif" font-size="14.00"> 18.02s</text>
</a>
</g>
</g>
<!-- N25&#45;&gt;N45 -->
<g id="edge52" class="edge"><title>N25&#45;&gt;N45</title>
<g id="a_edge52"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).Decode (2.09s)">
<path fill="none" stroke="#b2aea4" d="M643.707,-1659.93C535.151,-1653.58 260,-1629.7 260,-1551 260,-1551 260,-1551 260,-1185 260,-1148.77 281.674,-1112.69 300.181,-1088.48"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="303.09,-1090.44 306.555,-1080.43 297.603,-1086.1 303.09,-1090.44"/>
</a>
</g>
<g id="a_edge52&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).Decode (2.09s)">
<text text-anchor="middle" x="277" y="-1368.3" font-family="Times,serif" font-size="14.00"> 2.09s</text>
</a>
</g>
</g>
<!-- N25&#45;&gt;N26 -->
<g id="edge69" class="edge"><title>N25&#45;&gt;N26</title>
<g id="a_edge69"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal &#45;&gt; github.com/json&#45;iterator/go.(*OptionalDecoder).Decode (1.28s)">
<path fill="none" stroke="#b2b0a9" d="M643.966,-1660.93C571.841,-1658.23 422.512,-1648.81 301,-1616 230.856,-1597.06 151,-1623.66 151,-1551 151,-1551 151,-1551 151,-924 151,-900.211 197.011,-864.015 237.017,-837.04"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="239.058,-839.885 245.443,-831.431 235.179,-834.058 239.058,-839.885"/>
</a>
</g>
<g id="a_edge69&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).ReadVal &#45;&gt; github.com/json&#45;iterator/go.(*OptionalDecoder).Decode (1.28s)">
<text text-anchor="middle" x="168" y="-1242.3" font-family="Times,serif" font-size="14.00"> 1.28s</text>
</a>
</g>
</g>
<!-- N26&#45;&gt;N35 -->
<g id="edge70" class="edge"><title>N26&#45;&gt;N35</title>
<g id="a_edge70"><a xlink:title="github.com/json&#45;iterator/go.(*OptionalDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*generalStructDecoder).Decode (1.27s)">
<path fill="none" stroke="#b2b0a9" d="M246.829,-831.616C240.55,-837.624 234.941,-844.441 231,-852 176.75,-956.05 163.674,-1289.2 238,-1380 333.507,-1496.68 518.47,-1532.85 618.609,-1544.03"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="618.305,-1547.51 628.619,-1545.09 619.046,-1540.55 618.305,-1547.51"/>
</a>
</g>
<g id="a_edge70&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*OptionalDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*generalStructDecoder).Decode (1.27s)">
<text text-anchor="middle" x="208" y="-1182.3" font-family="Times,serif" font-size="14.00"> 1.27s</text>
</a>
</g>
</g>
<!-- N26&#45;&gt;N13 -->
<g id="edge84" class="edge"><title>N26&#45;&gt;N13</title>
<g id="a_edge84"><a xlink:title="github.com/json&#45;iterator/go.(*OptionalDecoder).Decode &#45;&gt; [adapter] (0.89s)">
<path fill="none" stroke="#b2b1ac" d="M301.451,-768.206C305.842,-754.951 313.104,-740.561 325,-732 376.408,-695.002 802.66,-672.783 965.288,-665.584"/>
<polygon fill="#b2b1ac" stroke="#b2b1ac" points="965.774,-669.066 975.612,-665.132 965.468,-662.073 965.774,-669.066"/>
</a>
</g>
<g id="a_edge84&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*OptionalDecoder).Decode &#45;&gt; [adapter] (0.89s)">
<text text-anchor="middle" x="342" y="-736.3" font-family="Times,serif" font-size="14.00"> 0.89s</text>
</a>
</g>
</g>
<!-- N27&#45;&gt;N22 -->
<g id="edge11" class="edge"><title>N27&#45;&gt;N22</title>
<g id="a_edge11"><a xlink:title="runtime.schedule &#45;&gt; runtime.findrunnable (12.05s)">
<path fill="none" stroke="#b2895f" d="M2781,-1159.8C2781,-1141.7 2781,-1116.99 2781,-1096.57"/>
<polygon fill="#b2895f" stroke="#b2895f" points="2784.5,-1096.34 2781,-1086.34 2777.5,-1096.34 2784.5,-1096.34"/>
</a>
</g>
<g id="a_edge11&#45;label"><a xlink:title="runtime.schedule &#45;&gt; runtime.findrunnable (12.05s)">
<text text-anchor="middle" x="2801" y="-1122.3" font-family="Times,serif" font-size="14.00"> 12.05s</text>
</a>
</g>
</g>
<!-- N71 -->
<g id="node71" class="node"><title>N71</title>
<g id="a_node71"><a xlink:title="runtime.casgstatus (0.76s)">
<polygon fill="#ededec" stroke="#b2b1ad" points="2715.25,-1081.5 2632.75,-1081.5 2632.75,-1034.5 2715.25,-1034.5 2715.25,-1081.5"/>
<text text-anchor="middle" x="2674" y="-1067.9" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="2674" y="-1054.9" font-family="Times,serif" font-size="12.00">casgstatus</text>
<text text-anchor="middle" x="2674" y="-1041.9" font-family="Times,serif" font-size="12.00">0.76s (0.59%)</text>
</a>
</g>
</g>
<!-- N27&#45;&gt;N71 -->
<g id="edge120" class="edge"><title>N27&#45;&gt;N71</title>
<g id="a_edge120"><a xlink:title="runtime.schedule ... runtime.casgstatus (0.33s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M2759.6,-1159.8C2742.41,-1139.55 2718.19,-1111.03 2699.9,-1089.5"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="2702.34,-1086.97 2693.2,-1081.61 2697.01,-1091.5 2702.34,-1086.97"/>
</a>
</g>
<g id="a_edge120&#45;label"><a xlink:title="runtime.schedule ... runtime.casgstatus (0.33s)">
<text text-anchor="middle" x="2755" y="-1122.3" font-family="Times,serif" font-size="14.00"> 0.33s</text>
</a>
</g>
</g>
<!-- N28 -->
<g id="node28" class="node"><title>N28</title>
<g id="a_node28"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord (5.28s)">
<polygon fill="#edebe8" stroke="#b2a48e" points="1722,-1214 1624,-1214 1624,-1158 1722,-1158 1722,-1214"/>
<text text-anchor="middle" x="1673" y="-1201.2" font-family="Times,serif" font-size="11.00">log4go</text>
<text text-anchor="middle" x="1673" y="-1189.2" font-family="Times,serif" font-size="11.00">FormatLogRecord</text>
<text text-anchor="middle" x="1673" y="-1177.2" font-family="Times,serif" font-size="11.00">0.56s (0.43%)</text>
<text text-anchor="middle" x="1673" y="-1165.2" font-family="Times,serif" font-size="11.00">of 5.28s (4.08%)</text>
</a>
</g>
</g>
<!-- N28&#45;&gt;N31 -->
<g id="edge68" class="edge"><title>N28&#45;&gt;N31</title>
<g id="a_edge68"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.growslice (1.29s)">
<path fill="none" stroke="#b2b0a9" d="M1627.93,-1157.96C1623.32,-1155.75 1618.62,-1153.71 1614,-1152 1576.16,-1137.98 1564.49,-1142.32 1525,-1134 1492.5,-1127.15 1484.99,-1121.9 1452,-1118 1349.69,-1105.91 623.615,-1129.8 525,-1100 444.274,-1075.6 435.644,-1044.06 365,-998 354.261,-990.998 352.869,-986.846 341,-982 301.215,-965.756 279.09,-991.419 246,-964 208.126,-932.617 192.562,-875.797 186.235,-838.227"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="189.669,-837.528 184.685,-828.178 182.751,-838.595 189.669,-837.528"/>
</a>
</g>
<g id="a_edge68&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.growslice (1.29s)">
<text text-anchor="middle" x="382" y="-986.3" font-family="Times,serif" font-size="14.00"> 1.29s</text>
</a>
</g>
</g>
<!-- N28&#45;&gt;N24 -->
<g id="edge71" class="edge"><title>N28&#45;&gt;N24</title>
<g id="a_edge71"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; fmt.Sprintf (1.22s)">
<path fill="none" stroke="#b2b0aa" d="M1650.73,-1157.92C1645.93,-1150.59 1641.53,-1142.35 1639,-1134 1635.19,-1121.43 1634.85,-1107.13 1635.83,-1094.42"/>
<polygon fill="#b2b0aa" stroke="#b2b0aa" points="1639.34,-1094.5 1636.92,-1084.18 1632.38,-1093.76 1639.34,-1094.5"/>
</a>
</g>
<g id="a_edge71&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; fmt.Sprintf (1.22s)">
<text text-anchor="middle" x="1656" y="-1122.3" font-family="Times,serif" font-size="14.00"> 1.22s</text>
</a>
</g>
</g>
<!-- N28&#45;&gt;N44 -->
<g id="edge101" class="edge"><title>N28&#45;&gt;N44</title>
<g id="a_edge101"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord ... runtime.makeslice (0.53s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M1624.95,-1157.73C1606.88,-1147.3 1589.76,-1137.15 1586,-1134 1570.78,-1121.24 1567.04,-1117.14 1557,-1100 1536.89,-1065.66 1554.17,-1044.1 1526,-1016 1478.42,-968.535 1446.58,-988.511 1384,-964 1370.92,-958.875 1356.87,-953.13 1343.82,-947.69"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1345.1,-944.431 1334.52,-943.795 1342.39,-950.887 1345.1,-944.431"/>
</a>
</g>
<g id="a_edge101&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord ... runtime.makeslice (0.53s)">
<text text-anchor="middle" x="1574" y="-1054.3" font-family="Times,serif" font-size="14.00"> 0.53s</text>
</a>
</g>
</g>
<!-- N28&#45;&gt;N32 -->
<g id="edge110" class="edge"><title>N28&#45;&gt;N32</title>
<g id="a_edge110"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.memmove (0.39s)">
<path fill="none" stroke="#b2b2af" d="M1680.45,-1157.82C1692.43,-1115.72 1716.57,-1037.38 1736,-1016 1753.78,-996.435 1826.25,-984.457 1843,-964 1859.96,-943.279 1897.49,-773.859 1913.51,-698.691"/>
<polygon fill="#b2b2af" stroke="#b2b2af" points="1916.97,-699.244 1915.62,-688.735 1910.12,-697.79 1916.97,-699.244"/>
</a>
</g>
<g id="a_edge110&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.memmove (0.39s)">
<text text-anchor="middle" x="1887" y="-921.3" font-family="Times,serif" font-size="14.00"> 0.39s</text>
</a>
</g>
</g>
<!-- N28&#45;&gt;N43 -->
<g id="edge116" class="edge"><title>N28&#45;&gt;N43</title>
<g id="a_edge116"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.convTstring (0.35s)">
<path fill="none" stroke="#b2b2b0" d="M1722.2,-1173.23C1779.31,-1159.63 1876.37,-1136.72 1960,-1118 1997.26,-1109.66 2009.16,-1115.62 2044,-1100 2050.21,-1097.22 2056.4,-1093.67 2062.28,-1089.83"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="2064.4,-1092.61 2070.64,-1084.05 2060.42,-1086.86 2064.4,-1092.61"/>
</a>
</g>
<g id="a_edge116&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; runtime.convTstring (0.35s)">
<text text-anchor="middle" x="1977" y="-1122.3" font-family="Times,serif" font-size="14.00"> 0.35s</text>
</a>
</g>
</g>
<!-- N28&#45;&gt;N77 -->
<g id="edge139" class="edge"><title>N28&#45;&gt;N77</title>
<g id="a_edge139"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; sync.(*Pool).Put (0.21s)">
<path fill="none" stroke="#b2b2b1" d="M1697.63,-1157.74C1710.91,-1144.33 1728.19,-1128.77 1746,-1118 1766.21,-1105.77 1775.01,-1110.83 1796,-1100 1797.75,-1099.1 1799.5,-1098.15 1801.26,-1097.17"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1803.17,-1100.11 1810,-1092.01 1799.61,-1094.08 1803.17,-1100.11"/>
</a>
</g>
<g id="a_edge139&#45;label"><a xlink:title="github.com/ivanabc/log4go.FormatLogRecord &#45;&gt; sync.(*Pool).Put (0.21s)">
<text text-anchor="middle" x="1763" y="-1122.3" font-family="Times,serif" font-size="14.00"> 0.21s</text>
</a>
</g>
</g>
<!-- N29&#45;&gt;N15 -->
<g id="edge19" class="edge"><title>N29&#45;&gt;N15</title>
<g id="a_edge19"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... syscall.Syscall (6.81s)">
<path fill="none" stroke="#b29f83" stroke-dasharray="1,5" d="M996.336,-1028.84C1003.1,-1019.19 1010.55,-1008.24 1017,-998 1052.45,-941.757 1060.43,-927.04 1091,-868 1115.81,-820.081 1141.62,-764.593 1160.22,-723.439"/>
<polygon fill="#b29f83" stroke="#b29f83" points="1163.52,-724.649 1164.44,-714.094 1157.14,-721.772 1163.52,-724.649"/>
</a>
</g>
<g id="a_edge19&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... syscall.Syscall (6.81s)">
<text text-anchor="middle" x="1116" y="-856.3" font-family="Times,serif" font-size="14.00"> 6.81s</text>
</a>
</g>
</g>
<!-- N78 -->
<g id="node78" class="node"><title>N78</title>
<g id="a_node78"><a xlink:title="github.com/rcrowley/go&#45;metrics.(*StandardHistogram).Update (1.33s)">
<polygon fill="#edeceb" stroke="#b2b0a9" points="780.25,-831.5 673.75,-831.5 673.75,-768.5 780.25,-768.5 780.25,-831.5"/>
<text text-anchor="middle" x="727" y="-819.5" font-family="Times,serif" font-size="10.00">go&#45;metrics</text>
<text text-anchor="middle" x="727" y="-808.5" font-family="Times,serif" font-size="10.00">(*StandardHistogram)</text>
<text text-anchor="middle" x="727" y="-797.5" font-family="Times,serif" font-size="10.00">Update</text>
<text text-anchor="middle" x="727" y="-786.5" font-family="Times,serif" font-size="10.00">0.11s (0.085%)</text>
<text text-anchor="middle" x="727" y="-775.5" font-family="Times,serif" font-size="10.00">of 1.33s (1.03%)</text>
</a>
</g>
</g>
<!-- N29&#45;&gt;N78 -->
<g id="edge80" class="edge"><title>N29&#45;&gt;N78</title>
<g id="a_edge80"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... github.com/rcrowley/go&#45;metrics.(*StandardHistogram).Update (0.97s)">
<path fill="none" stroke="#b2b0ab" stroke-dasharray="1,5" d="M976.556,-1028.99C976.057,-993.417 971.063,-931.224 945,-886 914.13,-832.435 842.917,-812.517 790.239,-805.172"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="790.663,-801.697 780.301,-803.902 789.777,-808.641 790.663,-801.697"/>
</a>
</g>
<g id="a_edge80&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... github.com/rcrowley/go&#45;metrics.(*StandardHistogram).Update (0.97s)">
<text text-anchor="middle" x="988" y="-921.3" font-family="Times,serif" font-size="14.00"> 0.97s</text>
</a>
</g>
</g>
<!-- N29&#45;&gt;N44 -->
<g id="edge88" class="edge"><title>N29&#45;&gt;N44</title>
<g id="a_edge88"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver &#45;&gt; runtime.makeslice (0.73s)">
<path fill="none" stroke="#b2b1ad" d="M1017.18,-1045.09C1053.26,-1034.22 1106.84,-1016.98 1152,-998 1182.71,-985.094 1215.95,-968.269 1242.44,-954.129"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="1244.36,-957.069 1251.51,-949.25 1241.05,-950.904 1244.36,-957.069"/>
</a>
</g>
<g id="a_edge88&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver &#45;&gt; runtime.makeslice (0.73s)">
<text text-anchor="middle" x="1201" y="-986.3" font-family="Times,serif" font-size="14.00"> 0.73s</text>
</a>
</g>
</g>
<!-- N29&#45;&gt;N13 -->
<g id="edge122" class="edge"><title>N29&#45;&gt;N13</title>
<g id="a_edge122"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... [adapter] (0.32s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M988.368,-1028.68C995.7,-1010.55 1004.43,-986.322 1009,-964 1028.49,-868.824 1029.71,-753.496 1028.9,-697.223"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1032.4,-697.037 1028.73,-687.099 1025.4,-697.159 1032.4,-697.037"/>
</a>
</g>
<g id="a_edge122&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).responseReceiver ... [adapter] (0.32s)">
<text text-anchor="middle" x="1041" y="-856.3" font-family="Times,serif" font-size="14.00"> 0.32s</text>
</a>
</g>
</g>
<!-- N30 -->
<g id="node30" class="node"><title>N30</title>
<g id="a_node30"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 (8.11s)">
<polygon fill="#ede9e5" stroke="#b29a7a" points="1581,-1463.5 1487,-1463.5 1487,-1400.5 1581,-1400.5 1581,-1463.5"/>
<text text-anchor="middle" x="1534" y="-1451.5" font-family="Times,serif" font-size="10.00">log4go</text>
<text text-anchor="middle" x="1534" y="-1440.5" font-family="Times,serif" font-size="10.00">NewFileLogWriter</text>
<text text-anchor="middle" x="1534" y="-1429.5" font-family="Times,serif" font-size="10.00">func2</text>
<text text-anchor="middle" x="1534" y="-1418.5" font-family="Times,serif" font-size="10.00">0.07s (0.054%)</text>
<text text-anchor="middle" x="1534" y="-1407.5" font-family="Times,serif" font-size="10.00">of 8.11s (6.27%)</text>
</a>
</g>
</g>
<!-- N49 -->
<g id="node49" class="node"><title>N49</title>
<g id="a_node49"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write (7.22s)">
<polygon fill="#edeae6" stroke="#b29d80" points="1572.25,-1338 1495.75,-1338 1495.75,-1280 1572.25,-1280 1572.25,-1338"/>
<text text-anchor="middle" x="1534" y="-1326.8" font-family="Times,serif" font-size="9.00">log4go</text>
<text text-anchor="middle" x="1534" y="-1316.8" font-family="Times,serif" font-size="9.00">(*FileLogWriter)</text>
<text text-anchor="middle" x="1534" y="-1306.8" font-family="Times,serif" font-size="9.00">write</text>
<text text-anchor="middle" x="1534" y="-1296.8" font-family="Times,serif" font-size="9.00">0.05s (0.039%)</text>
<text text-anchor="middle" x="1534" y="-1286.8" font-family="Times,serif" font-size="9.00">of 7.22s (5.58%)</text>
</a>
</g>
</g>
<!-- N30&#45;&gt;N49 -->
<g id="edge18" class="edge"><title>N30&#45;&gt;N49</title>
<g id="a_edge18"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; github.com/ivanabc/log4go.(*FileLogWriter).write (7.22s)">
<path fill="none" stroke="#b29d80" d="M1534,-1400.47C1534,-1384.58 1534,-1365.01 1534,-1348.13"/>
<polygon fill="#b29d80" stroke="#b29d80" points="1537.5,-1348.04 1534,-1338.04 1530.5,-1348.04 1537.5,-1348.04"/>
</a>
</g>
<g id="a_edge18&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; github.com/ivanabc/log4go.(*FileLogWriter).write (7.22s)">
<text text-anchor="middle" x="1551" y="-1368.3" font-family="Times,serif" font-size="14.00"> 7.22s</text>
</a>
</g>
</g>
<!-- N30&#45;&gt;N16 -->
<g id="edge89" class="edge"><title>N30&#45;&gt;N16</title>
<g id="a_edge89"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; runtime.selectgo (0.73s)">
<path fill="none" stroke="#b2b1ad" d="M1581.18,-1428.44C1757.52,-1417.76 2373.24,-1370.05 2480,-1220 2505.02,-1184.84 2486.79,-1135.78 2467.31,-1101.08"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="2470.16,-1099.01 2462.09,-1092.14 2464.11,-1102.54 2470.16,-1099.01"/>
</a>
</g>
<g id="a_edge89&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func2 &#45;&gt; runtime.selectgo (0.73s)">
<text text-anchor="middle" x="2480" y="-1242.3" font-family="Times,serif" font-size="14.00"> 0.73s</text>
</a>
</g>
</g>
<!-- N31&#45;&gt;N1 -->
<g id="edge44" class="edge"><title>N31&#45;&gt;N1</title>
<g id="a_edge44"><a xlink:title="runtime.growslice &#45;&gt; runtime.mallocgc (2.69s)">
<path fill="none" stroke="#b2ac9f" d="M224.084,-771.923C247.613,-757.966 277.971,-741.85 307,-732 648.581,-616.097 1074.2,-549.021 1260.83,-523.27"/>
<polygon fill="#b2ac9f" stroke="#b2ac9f" points="1261.46,-526.718 1270.89,-521.891 1260.51,-519.782 1261.46,-526.718"/>
</a>
</g>
<g id="a_edge44&#45;label"><a xlink:title="runtime.growslice &#45;&gt; runtime.mallocgc (2.69s)">
<text text-anchor="middle" x="766" y="-658.3" font-family="Times,serif" font-size="14.00"> 2.69s</text>
</a>
</g>
</g>
<!-- N31&#45;&gt;N52 -->
<g id="edge131" class="edge"><title>N31&#45;&gt;N52</title>
<g id="a_edge131"><a xlink:title="runtime.growslice &#45;&gt; runtime.memclrNoHeapPointers (0.27s)">
<path fill="none" stroke="#b2b2b0" d="M200.439,-771.986C230.042,-730.722 292.187,-652.333 364,-610 615.816,-461.558 966.616,-408.608 1125.65,-391.445"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1126.27,-394.899 1135.85,-390.366 1125.53,-387.938 1126.27,-394.899"/>
</a>
</g>
<g id="a_edge131&#45;label"><a xlink:title="runtime.growslice &#45;&gt; runtime.memclrNoHeapPointers (0.27s)">
<text text-anchor="middle" x="437" y="-580.3" font-family="Times,serif" font-size="14.00"> 0.27s</text>
</a>
</g>
</g>
<!-- N33 -->
<g id="node33" class="node"><title>N33</title>
<g id="a_node33"><a xlink:title="runtime.usleep (5.41s)">
<polygon fill="#edebe8" stroke="#b2a48d" points="2837.25,-694.5 2724.75,-694.5 2724.75,-629.5 2837.25,-629.5 2837.25,-694.5"/>
<text text-anchor="middle" x="2781" y="-676.9" font-family="Times,serif" font-size="17.00">runtime</text>
<text text-anchor="middle" x="2781" y="-657.9" font-family="Times,serif" font-size="17.00">usleep</text>
<text text-anchor="middle" x="2781" y="-638.9" font-family="Times,serif" font-size="17.00">5.41s (4.18%)</text>
</a>
</g>
</g>
<!-- N34&#45;&gt;N10 -->
<g id="edge24" class="edge"><title>N34&#45;&gt;N10</title>
<g id="a_edge24"><a xlink:title="github.com/json&#45;iterator/go.(*placeholderDecoder).Decode ... github.com/json&#45;iterator/go.(*Iterator).ReadString (5.54s)">
<path fill="none" stroke="#b2a38c" stroke-dasharray="1,5" d="M667.922,-1154.5C683.329,-1140.22 702.044,-1122.89 719.277,-1106.92"/>
<polygon fill="#b2a38c" stroke="#b2a38c" points="722.117,-1109.06 727.074,-1099.7 717.359,-1103.93 722.117,-1109.06"/>
</a>
</g>
<g id="a_edge24&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*placeholderDecoder).Decode ... github.com/json&#45;iterator/go.(*Iterator).ReadString (5.54s)">
<text text-anchor="middle" x="725" y="-1122.3" font-family="Times,serif" font-size="14.00"> 5.54s</text>
</a>
</g>
</g>
<!-- N34&#45;&gt;N45 -->
<g id="edge75" class="edge"><title>N34&#45;&gt;N45</title>
<g id="a_edge75"><a xlink:title="github.com/json&#45;iterator/go.(*placeholderDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).Decode (1.11s)">
<path fill="none" stroke="#b2b0aa" d="M579.933,-1168.88C530.486,-1153.62 456.439,-1128.79 395,-1100 385.821,-1095.7 376.286,-1090.53 367.34,-1085.34"/>
<polygon fill="#b2b0aa" stroke="#b2b0aa" points="369.01,-1082.26 358.624,-1080.16 365.438,-1088.28 369.01,-1082.26"/>
</a>
</g>
<g id="a_edge75&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*placeholderDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).Decode (1.11s)">
<text text-anchor="middle" x="490" y="-1122.3" font-family="Times,serif" font-size="14.00"> 1.11s</text>
</a>
</g>
</g>
<!-- N35&#45;&gt;N11 -->
<g id="edge8" class="edge"><title>N35&#45;&gt;N11</title>
<g id="a_edge8"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField (17.58s)">
<path fill="none" stroke="#b26f39" d="M687,-1518.22C687,-1505.49 687,-1490.5 687,-1476.67"/>
<polygon fill="#b26f39" stroke="#b26f39" points="690.5,-1476.37 687,-1466.37 683.5,-1476.37 690.5,-1476.37"/>
</a>
</g>
<g id="a_edge8&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*generalStructDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*generalStructDecoder).decodeOneField (17.58s)">
<text text-anchor="middle" x="707" y="-1488.3" font-family="Times,serif" font-size="14.00"> 17.58s</text>
</a>
</g>
</g>
<!-- N36 -->
<g id="node36" class="node"><title>N36</title>
<g id="a_node36"><a xlink:title="runtime.gcBgMarkWorker (8.84s)">
<polygon fill="#ede9e5" stroke="#b29775" points="2367.25,-534 2286.75,-534 2286.75,-486 2367.25,-486 2367.25,-534"/>
<text text-anchor="middle" x="2327" y="-522.8" font-family="Times,serif" font-size="9.00">runtime</text>
<text text-anchor="middle" x="2327" y="-512.8" font-family="Times,serif" font-size="9.00">gcBgMarkWorker</text>
<text text-anchor="middle" x="2327" y="-502.8" font-family="Times,serif" font-size="9.00">0.01s (0.0077%)</text>
<text text-anchor="middle" x="2327" y="-492.8" font-family="Times,serif" font-size="9.00">of 8.84s (6.83%)</text>
</a>
</g>
</g>
<!-- N36&#45;&gt;N5 -->
<g id="edge14" class="edge"><title>N36&#45;&gt;N5</title>
<g id="a_edge14"><a xlink:title="runtime.gcBgMarkWorker ... runtime.systemstack (8.74s)">
<path fill="none" stroke="#b29776" stroke-dasharray="1,5" d="M2327,-485.781C2327,-467.178 2327,-440.767 2327,-419.511"/>
<polygon fill="#b29776" stroke="#b29776" points="2330.5,-419.242 2327,-409.242 2323.5,-419.242 2330.5,-419.242"/>
</a>
</g>
<g id="a_edge14&#45;label"><a xlink:title="runtime.gcBgMarkWorker ... runtime.systemstack (8.74s)">
<text text-anchor="middle" x="2344" y="-432.3" font-family="Times,serif" font-size="14.00"> 8.74s</text>
</a>
</g>
</g>
<!-- N37&#45;&gt;N5 -->
<g id="edge76" class="edge"><title>N37&#45;&gt;N5</title>
<g id="a_edge76"><a xlink:title="runtime.gcWriteBarrier ... runtime.systemstack (1.08s)">
<path fill="none" stroke="#b2b0ab" stroke-dasharray="1,5" d="M2266.87,-633.828C2247.08,-594.457 2217.74,-519.926 2243,-462 2250.85,-443.992 2265.14,-428.273 2279.73,-415.757"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="2282.3,-418.176 2287.83,-409.138 2277.87,-412.756 2282.3,-418.176"/>
</a>
</g>
<g id="a_edge76&#45;label"><a xlink:title="runtime.gcWriteBarrier ... runtime.systemstack (1.08s)">
<text text-anchor="middle" x="2260" y="-506.3" font-family="Times,serif" font-size="14.00"> 1.08s</text>
</a>
</g>
</g>
<!-- N48 -->
<g id="node48" class="node"><title>N48</title>
<g id="a_node48"><a xlink:title="github.com/Shopify/sarama.versionedDecode (3.33s)">
<polygon fill="#edecea" stroke="#b2aa9b" points="473,-949 395,-949 395,-901 473,-901 473,-949"/>
<text text-anchor="middle" x="434" y="-937.8" font-family="Times,serif" font-size="9.00">sarama</text>
<text text-anchor="middle" x="434" y="-927.8" font-family="Times,serif" font-size="9.00">versionedDecode</text>
<text text-anchor="middle" x="434" y="-917.8" font-family="Times,serif" font-size="9.00">0.03s (0.023%)</text>
<text text-anchor="middle" x="434" y="-907.8" font-family="Times,serif" font-size="9.00">of 3.33s (2.57%)</text>
</a>
</g>
</g>
<!-- N41&#45;&gt;N48 -->
<g id="edge41" class="edge"><title>N41&#45;&gt;N48</title>
<g id="a_edge41"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive &#45;&gt; github.com/Shopify/sarama.versionedDecode (3.23s)">
<path fill="none" stroke="#b2ab9c" d="M543.181,-1028.9C520.534,-1007.55 489.432,-978.242 466.215,-956.361"/>
<polygon fill="#b2ab9c" stroke="#b2ab9c" points="468.346,-953.56 458.668,-949.248 463.545,-958.654 468.346,-953.56"/>
</a>
</g>
<g id="a_edge41&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive &#45;&gt; github.com/Shopify/sarama.versionedDecode (3.23s)">
<text text-anchor="middle" x="525" y="-986.3" font-family="Times,serif" font-size="14.00"> 3.23s</text>
</a>
</g>
</g>
<!-- N62 -->
<g id="node62" class="node"><title>N62</title>
<g id="a_node62"><a xlink:title="github.com/Shopify/sarama.(*Broker).send (3.19s)">
<polygon fill="#edecea" stroke="#b2ab9c" points="568.25,-954 491.75,-954 491.75,-896 568.25,-896 568.25,-954"/>
<text text-anchor="middle" x="530" y="-942.8" font-family="Times,serif" font-size="9.00">sarama</text>
<text text-anchor="middle" x="530" y="-932.8" font-family="Times,serif" font-size="9.00">(*Broker)</text>
<text text-anchor="middle" x="530" y="-922.8" font-family="Times,serif" font-size="9.00">send</text>
<text text-anchor="middle" x="530" y="-912.8" font-family="Times,serif" font-size="9.00">0.04s (0.031%)</text>
<text text-anchor="middle" x="530" y="-902.8" font-family="Times,serif" font-size="9.00">of 3.19s (2.47%)</text>
</a>
</g>
</g>
<!-- N41&#45;&gt;N62 -->
<g id="edge42" class="edge"><title>N41&#45;&gt;N62</title>
<g id="a_edge42"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive &#45;&gt; github.com/Shopify/sarama.(*Broker).send (3.19s)">
<path fill="none" stroke="#b2ab9c" d="M563.775,-1028.9C557.541,-1009.9 549.234,-984.598 542.419,-963.836"/>
<polygon fill="#b2ab9c" stroke="#b2ab9c" points="545.654,-962.466 539.209,-954.056 539.003,-964.649 545.654,-962.466"/>
</a>
</g>
<g id="a_edge42&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).sendAndReceive &#45;&gt; github.com/Shopify/sarama.(*Broker).send (3.19s)">
<text text-anchor="middle" x="570" y="-986.3" font-family="Times,serif" font-size="14.00"> 3.19s</text>
</a>
</g>
</g>
<!-- N42&#45;&gt;N31 -->
<g id="edge72" class="edge"><title>N42&#45;&gt;N31</title>
<g id="a_edge72"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).readStringSlowPath ... runtime.growslice (1.17s)">
<path fill="none" stroke="#b2b0aa" stroke-dasharray="1,5" d="M727.92,-885.676C710.526,-872.508 689.496,-859.264 668,-852 576.973,-821.242 327.93,-861.944 236,-834 234.302,-833.484 232.603,-832.901 230.91,-832.263"/>
<polygon fill="#b2b0aa" stroke="#b2b0aa" points="231.893,-828.873 221.329,-828.068 229.086,-835.285 231.893,-828.873"/>
</a>
</g>
<g id="a_edge72&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).readStringSlowPath ... runtime.growslice (1.17s)">
<text text-anchor="middle" x="717" y="-856.3" font-family="Times,serif" font-size="14.00"> 1.17s</text>
</a>
</g>
</g>
<!-- N42&#45;&gt;N21 -->
<g id="edge126" class="edge"><title>N42&#45;&gt;N21</title>
<g id="a_edge126"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).readStringSlowPath &#45;&gt; runtime.slicebytetostring (0.30s)">
<path fill="none" stroke="#b2b2b0" d="M832.353,-900.371C849.188,-894.766 867.601,-889.426 885,-886 1002.13,-862.938 1034.85,-885.08 1153,-868 1221.51,-858.096 1238.45,-853.054 1305,-834 1317.59,-830.396 1330.95,-826.038 1343.53,-821.687"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1344.69,-824.987 1352.97,-818.373 1342.38,-818.382 1344.69,-824.987"/>
</a>
</g>
<g id="a_edge126&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*Iterator).readStringSlowPath &#45;&gt; runtime.slicebytetostring (0.30s)">
<text text-anchor="middle" x="1255" y="-856.3" font-family="Times,serif" font-size="14.00"> 0.30s</text>
</a>
</g>
</g>
<!-- N43&#45;&gt;N1 -->
<g id="edge54" class="edge"><title>N43&#45;&gt;N1</title>
<g id="a_edge54"><a xlink:title="runtime.convTstring &#45;&gt; runtime.mallocgc (2.06s)">
<path fill="none" stroke="#b2aea4" d="M2100,-1031.89C2100,-1005.52 2100,-962.844 2100,-926 2100,-926 2100,-926 2100,-661 2100,-530.191 1652.47,-512.059 1451.6,-510.509"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="1451.37,-507.007 1441.35,-510.442 1451.32,-514.007 1451.37,-507.007"/>
</a>
</g>
<g id="a_edge54&#45;label"><a xlink:title="runtime.convTstring &#45;&gt; runtime.mallocgc (2.06s)">
<text text-anchor="middle" x="2117" y="-796.3" font-family="Times,serif" font-size="14.00"> 2.06s</text>
</a>
</g>
</g>
<!-- N44&#45;&gt;N1 -->
<g id="edge55" class="edge"><title>N44&#45;&gt;N1</title>
<g id="a_edge55"><a xlink:title="runtime.makeslice &#45;&gt; runtime.mallocgc (2.06s)">
<path fill="none" stroke="#b2aea4" d="M1296.85,-898.787C1306.76,-833.797 1333.38,-659.306 1347.28,-568.182"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="1350.74,-568.71 1348.79,-558.296 1343.82,-567.654 1350.74,-568.71"/>
</a>
</g>
<g id="a_edge55&#45;label"><a xlink:title="runtime.makeslice &#45;&gt; runtime.mallocgc (2.06s)">
<text text-anchor="middle" x="1339" y="-736.3" font-family="Times,serif" font-size="14.00"> 2.06s</text>
</a>
</g>
</g>
<!-- N73 -->
<g id="node73" class="node"><title>N73</title>
<g id="a_node73"><a xlink:title="github.com/json&#45;iterator/go.(*sliceDecoder).doDecode (4.14s)">
<polygon fill="#edebe9" stroke="#b2a895" points="338.25,-956.5 255.75,-956.5 255.75,-893.5 338.25,-893.5 338.25,-956.5"/>
<text text-anchor="middle" x="297" y="-944.5" font-family="Times,serif" font-size="10.00">go</text>
<text text-anchor="middle" x="297" y="-933.5" font-family="Times,serif" font-size="10.00">(*sliceDecoder)</text>
<text text-anchor="middle" x="297" y="-922.5" font-family="Times,serif" font-size="10.00">doDecode</text>
<text text-anchor="middle" x="297" y="-911.5" font-family="Times,serif" font-size="10.00">0.11s (0.085%)</text>
<text text-anchor="middle" x="297" y="-900.5" font-family="Times,serif" font-size="10.00">of 4.14s (3.20%)</text>
</a>
</g>
</g>
<!-- N45&#45;&gt;N73 -->
<g id="edge33" class="edge"><title>N45&#45;&gt;N73</title>
<g id="a_edge33"><a xlink:title="github.com/json&#45;iterator/go.(*sliceDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).doDecode (4.14s)">
<path fill="none" stroke="#b2a895" d="M318.238,-1035.95C314.378,-1024.8 309.926,-1010.8 307,-998 304.7,-987.943 302.85,-976.966 301.396,-966.684"/>
<polygon fill="#b2a895" stroke="#b2a895" points="304.855,-966.146 300.072,-956.692 297.916,-967.065 304.855,-966.146"/>
</a>
</g>
<g id="a_edge33&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*sliceDecoder).Decode &#45;&gt; github.com/json&#45;iterator/go.(*sliceDecoder).doDecode (4.14s)">
<text text-anchor="middle" x="324" y="-986.3" font-family="Times,serif" font-size="14.00"> 4.14s</text>
</a>
</g>
</g>
<!-- N46&#45;&gt;N38 -->
<g id="edge142" class="edge"><title>N46&#45;&gt;N38</title>
<g id="a_edge142"><a xlink:title="runtime.lock2 ... runtime.futex (0.16s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M2538.25,-892.814C2530.72,-868.194 2522,-832.81 2522,-801 2522,-801 2522,-801 2522,-382 2522,-353.434 2502.81,-328.186 2482.77,-309.611"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="2484.9,-306.823 2475.09,-302.826 2480.27,-312.069 2484.9,-306.823"/>
</a>
</g>
<g id="a_edge142&#45;label"><a xlink:title="runtime.lock2 ... runtime.futex (0.16s)">
<text text-anchor="middle" x="2539" y="-580.3" font-family="Times,serif" font-size="14.00"> 0.16s</text>
</a>
</g>
</g>
<!-- N47&#45;&gt;N14 -->
<g id="edge20" class="edge"><title>N47&#45;&gt;N14</title>
<g id="a_edge20"><a xlink:title="runtime.gcDrain ... runtime.scanobject (6.66s)">
<path fill="none" stroke="#b29f84" stroke-dasharray="1,5" d="M2327,-247.77C2327,-235.528 2327,-220.661 2327,-206.448"/>
<polygon fill="#b29f84" stroke="#b29f84" points="2330.5,-206.26 2327,-196.26 2323.5,-206.26 2330.5,-206.26"/>
</a>
</g>
<g id="a_edge20&#45;label"><a xlink:title="runtime.gcDrain ... runtime.scanobject (6.66s)">
<text text-anchor="middle" x="2344" y="-218.3" font-family="Times,serif" font-size="14.00"> 6.66s</text>
</a>
</g>
</g>
<!-- N72 -->
<g id="node72" class="node"><title>N72</title>
<g id="a_node72"><a xlink:title="runtime.gentraceback (1.94s)">
<polygon fill="#edeceb" stroke="#b2aea5" points="2501,-182 2411,-182 2411,-126 2501,-126 2501,-182"/>
<text text-anchor="middle" x="2456" y="-169.2" font-family="Times,serif" font-size="11.00">runtime</text>
<text text-anchor="middle" x="2456" y="-157.2" font-family="Times,serif" font-size="11.00">gentraceback</text>
<text text-anchor="middle" x="2456" y="-145.2" font-family="Times,serif" font-size="11.00">0.53s (0.41%)</text>
<text text-anchor="middle" x="2456" y="-133.2" font-family="Times,serif" font-size="11.00">of 1.94s (1.50%)</text>
</a>
</g>
</g>
<!-- N47&#45;&gt;N72 -->
<g id="edge93" class="edge"><title>N47&#45;&gt;N72</title>
<g id="a_edge93"><a xlink:title="runtime.gcDrain ... runtime.gentraceback (0.62s)">
<path fill="none" stroke="#b2b1ae" stroke-dasharray="1,5" d="M2356.27,-247.77C2374.99,-230.356 2399.42,-207.63 2419.42,-189.029"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="2421.91,-191.496 2426.84,-182.122 2417.14,-186.371 2421.91,-191.496"/>
</a>
</g>
<g id="a_edge93&#45;label"><a xlink:title="runtime.gcDrain ... runtime.gentraceback (0.62s)">
<text text-anchor="middle" x="2409" y="-218.3" font-family="Times,serif" font-size="14.00"> 0.62s</text>
</a>
</g>
</g>
<!-- N48&#45;&gt;N13 -->
<g id="edge67" class="edge"><title>N48&#45;&gt;N13</title>
<g id="a_edge67"><a xlink:title="github.com/Shopify/sarama.versionedDecode ... [adapter] (1.34s)">
<path fill="none" stroke="#b2b0a9" stroke-dasharray="1,5" d="M442.34,-900.849C456.021,-866.205 486.543,-801.194 534,-766 601.881,-715.659 848.014,-682.696 965.177,-669.509"/>
<polygon fill="#b2b0a9" stroke="#b2b0a9" points="965.873,-672.953 975.425,-668.369 965.099,-665.996 965.873,-672.953"/>
</a>
</g>
<g id="a_edge67&#45;label"><a xlink:title="github.com/Shopify/sarama.versionedDecode ... [adapter] (1.34s)">
<text text-anchor="middle" x="551" y="-796.3" font-family="Times,serif" font-size="14.00"> 1.34s</text>
</a>
</g>
</g>
<!-- N48&#45;&gt;N31 -->
<g id="edge146" class="edge"><title>N48&#45;&gt;N31</title>
<g id="a_edge146"><a xlink:title="github.com/Shopify/sarama.versionedDecode ... runtime.growslice (0.15s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M394.834,-904.783C380.1,-898.12 363.032,-891.059 347,-886 308.514,-873.855 294.143,-885.954 258,-868 241.671,-859.888 226.105,-847.361 213.362,-835.324"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="215.8,-832.813 206.2,-828.33 210.909,-837.821 215.8,-832.813"/>
</a>
</g>
<g id="a_edge146&#45;label"><a xlink:title="github.com/Shopify/sarama.versionedDecode ... runtime.growslice (0.15s)">
<text text-anchor="middle" x="275" y="-856.3" font-family="Times,serif" font-size="14.00"> 0.15s</text>
</a>
</g>
</g>
<!-- N49&#45;&gt;N28 -->
<g id="edge26" class="edge"><title>N49&#45;&gt;N28</title>
<g id="a_edge26"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write &#45;&gt; github.com/ivanabc/log4go.FormatLogRecord (5.28s)">
<path fill="none" stroke="#b2a48e" d="M1550.98,-1279.99C1560.2,-1266.27 1572.48,-1250.13 1586,-1238 1589.05,-1235.26 1601.51,-1227.71 1616.08,-1219.22"/>
<polygon fill="#b2a48e" stroke="#b2a48e" points="1617.95,-1222.19 1624.85,-1214.15 1614.44,-1216.13 1617.95,-1222.19"/>
</a>
</g>
<g id="a_edge26&#45;label"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write &#45;&gt; github.com/ivanabc/log4go.FormatLogRecord (5.28s)">
<text text-anchor="middle" x="1603" y="-1242.3" font-family="Times,serif" font-size="14.00"> 5.28s</text>
</a>
</g>
</g>
<!-- N63 -->
<g id="node63" class="node"><title>N63</title>
<g id="a_node63"><a xlink:title="os.(*File).Write (3.44s)">
<polygon fill="#edece9" stroke="#b2aa9a" points="1441.25,-1089.5 1358.75,-1089.5 1358.75,-1026.5 1441.25,-1026.5 1441.25,-1089.5"/>
<text text-anchor="middle" x="1400" y="-1077.5" font-family="Times,serif" font-size="10.00">os</text>
<text text-anchor="middle" x="1400" y="-1066.5" font-family="Times,serif" font-size="10.00">(*File)</text>
<text text-anchor="middle" x="1400" y="-1055.5" font-family="Times,serif" font-size="10.00">Write</text>
<text text-anchor="middle" x="1400" y="-1044.5" font-family="Times,serif" font-size="10.00">0.08s (0.062%)</text>
<text text-anchor="middle" x="1400" y="-1033.5" font-family="Times,serif" font-size="10.00">of 3.44s (2.66%)</text>
</a>
</g>
</g>
<!-- N49&#45;&gt;N63 -->
<g id="edge73" class="edge"><title>N49&#45;&gt;N63</title>
<g id="a_edge73"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write ... os.(*File).Write (1.13s)">
<path fill="none" stroke="#b2b0aa" stroke-dasharray="1,5" d="M1495.73,-1299.42C1456.59,-1288.38 1397.86,-1265.02 1371,-1220 1348.92,-1182.99 1363.2,-1133.29 1378.5,-1098.95"/>
<polygon fill="#b2b0aa" stroke="#b2b0aa" points="1381.76,-1100.24 1382.81,-1089.69 1375.42,-1097.28 1381.76,-1100.24"/>
</a>
</g>
<g id="a_edge73&#45;label"><a xlink:title="github.com/ivanabc/log4go.(*FileLogWriter).write ... os.(*File).Write (1.13s)">
<text text-anchor="middle" x="1388" y="-1182.3" font-family="Times,serif" font-size="14.00"> 1.13s</text>
</a>
</g>
</g>
<!-- N50 -->
<g id="node50" class="node"><title>N50</title>
<g id="a_node50"><a xlink:title="internal/poll.(*FD).Write (5.31s)">
<polygon fill="#edebe8" stroke="#b2a48d" points="1296.25,-829 1219.75,-829 1219.75,-771 1296.25,-771 1296.25,-829"/>
<text text-anchor="middle" x="1258" y="-817.8" font-family="Times,serif" font-size="9.00">poll</text>
<text text-anchor="middle" x="1258" y="-807.8" font-family="Times,serif" font-size="9.00">(*FD)</text>
<text text-anchor="middle" x="1258" y="-797.8" font-family="Times,serif" font-size="9.00">Write</text>
<text text-anchor="middle" x="1258" y="-787.8" font-family="Times,serif" font-size="9.00">0.03s (0.023%)</text>
<text text-anchor="middle" x="1258" y="-777.8" font-family="Times,serif" font-size="9.00">of 5.31s (4.10%)</text>
</a>
</g>
</g>
<!-- N50&#45;&gt;N15 -->
<g id="edge27" class="edge"><title>N50&#45;&gt;N15</title>
<g id="a_edge27"><a xlink:title="internal/poll.(*FD).Write ... syscall.Syscall (5.20s)">
<path fill="none" stroke="#b2a48e" stroke-dasharray="1,5" d="M1243.29,-770.821C1236.09,-757.035 1227.13,-739.878 1218.5,-723.334"/>
<polygon fill="#b2a48e" stroke="#b2a48e" points="1221.48,-721.476 1213.75,-714.231 1215.27,-724.716 1221.48,-721.476"/>
</a>
</g>
<g id="a_edge27&#45;label"><a xlink:title="internal/poll.(*FD).Write ... syscall.Syscall (5.20s)">
<text text-anchor="middle" x="1248" y="-736.3" font-family="Times,serif" font-size="14.00"> 5.20s</text>
</a>
</g>
</g>
<!-- N53 -->
<g id="node53" class="node"><title>N53</title>
<g id="a_node53"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 (1.92s)">
<polygon fill="#edeceb" stroke="#b2aea5" points="2403,-1217.5 2309,-1217.5 2309,-1154.5 2403,-1154.5 2403,-1217.5"/>
<text text-anchor="middle" x="2356" y="-1205.5" font-family="Times,serif" font-size="10.00">log4go</text>
<text text-anchor="middle" x="2356" y="-1194.5" font-family="Times,serif" font-size="10.00">NewFileLogWriter</text>
<text text-anchor="middle" x="2356" y="-1183.5" font-family="Times,serif" font-size="10.00">func1</text>
<text text-anchor="middle" x="2356" y="-1172.5" font-family="Times,serif" font-size="10.00">0.21s (0.16%)</text>
<text text-anchor="middle" x="2356" y="-1161.5" font-family="Times,serif" font-size="10.00">of 1.92s (1.48%)</text>
</a>
</g>
</g>
<!-- N53&#45;&gt;N16 -->
<g id="edge74" class="edge"><title>N53&#45;&gt;N16</title>
<g id="a_edge74"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; runtime.selectgo (1.11s)">
<path fill="none" stroke="#b2b0aa" d="M2370.43,-1154.25C2376.31,-1142.6 2383.5,-1129.4 2391,-1118 2394.94,-1112.01 2399.37,-1105.91 2403.91,-1100.01"/>
<polygon fill="#b2b0aa" stroke="#b2b0aa" points="2406.68,-1102.14 2410.12,-1092.12 2401.18,-1097.81 2406.68,-1102.14"/>
</a>
</g>
<g id="a_edge74&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; runtime.selectgo (1.11s)">
<text text-anchor="middle" x="2408" y="-1122.3" font-family="Times,serif" font-size="14.00"> 1.11s</text>
</a>
</g>
</g>
<!-- N53&#45;&gt;N13 -->
<g id="edge102" class="edge"><title>N53&#45;&gt;N13</title>
<g id="a_edge102"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; [adapter] (0.52s)">
<path fill="none" stroke="#b2b1ae" d="M2332.06,-1154.47C2276.57,-1086.26 2131.38,-922.036 1968,-852 1714.84,-743.479 1633.09,-767.901 1360,-732 1300.38,-724.162 1145.9,-736.163 1090,-714 1078.46,-709.425 1067.45,-701.86 1057.99,-693.94"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="1060.24,-691.262 1050.43,-687.262 1055.61,-696.507 1060.24,-691.262"/>
</a>
</g>
<g id="a_edge102&#45;label"><a xlink:title="github.com/ivanabc/log4go.NewFileLogWriter.func1 &#45;&gt; [adapter] (0.52s)">
<text text-anchor="middle" x="2161" y="-921.3" font-family="Times,serif" font-size="14.00"> 0.52s</text>
</a>
</g>
</g>
<!-- N68 -->
<g id="node68" class="node"><title>N68</title>
<g id="a_node68"><a xlink:title="runtime.runqgrab (6.06s)">
<polygon fill="#edeae7" stroke="#b2a188" points="2829,-830 2733,-830 2733,-770 2829,-770 2829,-830"/>
<text text-anchor="middle" x="2781" y="-816.4" font-family="Times,serif" font-size="12.00">runtime</text>
<text text-anchor="middle" x="2781" y="-803.4" font-family="Times,serif" font-size="12.00">runqgrab</text>
<text text-anchor="middle" x="2781" y="-790.4" font-family="Times,serif" font-size="12.00">0.65s (0.5%)</text>
<text text-anchor="middle" x="2781" y="-777.4" font-family="Times,serif" font-size="12.00">of 6.06s (4.68%)</text>
</a>
</g>
</g>
<!-- N54&#45;&gt;N68 -->
<g id="edge22" class="edge"><title>N54&#45;&gt;N68</title>
<g id="a_edge22"><a xlink:title="runtime.stealWork ... runtime.runqgrab (6.06s)">
<path fill="none" stroke="#b2a188" stroke-dasharray="1,5" d="M2781,-894.851C2781,-878.579 2781,-858.084 2781,-840.4"/>
<polygon fill="#b2a188" stroke="#b2a188" points="2784.5,-840.366 2781,-830.366 2777.5,-840.366 2784.5,-840.366"/>
</a>
</g>
<g id="a_edge22&#45;label"><a xlink:title="runtime.stealWork ... runtime.runqgrab (6.06s)">
<text text-anchor="middle" x="2798" y="-856.3" font-family="Times,serif" font-size="14.00"> 6.06s</text>
</a>
</g>
</g>
<!-- N56&#45;&gt;N9 -->
<g id="edge64" class="edge"><title>N56&#45;&gt;N9</title>
<g id="a_edge64"><a xlink:title="adapter/service/wm_logger.(*GainItemMessage).Execute &#45;&gt; adapter/service/wm_logger.GetBaseFromLog (1.49s)">
<path fill="none" stroke="#b2afa8" d="M1208.76,-1974.34C1195.51,-1960.45 1179.83,-1944.01 1166.34,-1929.86"/>
<polygon fill="#b2afa8" stroke="#b2afa8" points="1168.53,-1927.09 1159.1,-1922.27 1163.47,-1931.92 1168.53,-1927.09"/>
</a>
</g>
<g id="a_edge64&#45;label"><a xlink:title="adapter/service/wm_logger.(*GainItemMessage).Execute &#45;&gt; adapter/service/wm_logger.GetBaseFromLog (1.49s)">
<text text-anchor="middle" x="1207" y="-1944.3" font-family="Times,serif" font-size="14.00"> 1.49s</text>
</a>
</g>
</g>
<!-- N56&#45;&gt;N8 -->
<g id="edge103" class="edge"><title>N56&#45;&gt;N8</title>
<g id="a_edge103"><a xlink:title="adapter/service/wm_logger.(*GainItemMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.47s)">
<path fill="none" stroke="#b2b1af" d="M1187.49,-1976.63C1185.65,-1975.73 1183.81,-1974.85 1182,-1974 1167.43,-1967.2 1059.29,-1932.36 1047,-1922 1026.43,-1904.66 1036.69,-1887.2 1016,-1870 982.48,-1842.13 965.784,-1848.42 924,-1836 863.389,-1817.98 792.535,-1802.55 744.195,-1792.86"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="744.724,-1789.4 734.233,-1790.88 743.359,-1796.26 744.724,-1789.4"/>
</a>
</g>
<g id="a_edge103&#45;label"><a xlink:title="adapter/service/wm_logger.(*GainItemMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.47s)">
<text text-anchor="middle" x="1064" y="-1892.3" font-family="Times,serif" font-size="14.00"> 0.47s</text>
</a>
</g>
</g>
<!-- N56&#45;&gt;N24 -->
<g id="edge144" class="edge"><title>N56&#45;&gt;N24</title>
<g id="a_edge144"><a xlink:title="adapter/service/wm_logger.(*GainItemMessage).Execute &#45;&gt; fmt.Sprintf (0.15s)">
<path fill="none" stroke="#b2b2b1" d="M1239.54,-1974.33C1244.52,-1905.6 1265.37,-1737.23 1352,-1634 1443.1,-1525.45 1547.42,-1585.87 1623,-1466 1650.18,-1422.88 1613.89,-1275.87 1648,-1238 1673.26,-1209.95 1707.31,-1249.38 1731,-1220 1749.97,-1196.47 1742.26,-1180.05 1731,-1152 1725.28,-1137.75 1700.04,-1111.99 1677.89,-1091.19"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1680.24,-1088.59 1670.54,-1084.35 1675.48,-1093.72 1680.24,-1088.59"/>
</a>
</g>
<g id="a_edge144&#45;label"><a xlink:title="adapter/service/wm_logger.(*GainItemMessage).Execute &#45;&gt; fmt.Sprintf (0.15s)">
<text text-anchor="middle" x="1593" y="-1546.3" font-family="Times,serif" font-size="14.00"> 0.15s</text>
</a>
</g>
</g>
<!-- N58 -->
<g id="node58" class="node"><title>N58</title>
<g id="a_node58"><a xlink:title="encoding/json.Marshal (5.16s)">
<polygon fill="#edebe8" stroke="#b2a48e" points="1351.25,-1920 1274.75,-1920 1274.75,-1872 1351.25,-1872 1351.25,-1920"/>
<text text-anchor="middle" x="1313" y="-1908.8" font-family="Times,serif" font-size="9.00">json</text>
<text text-anchor="middle" x="1313" y="-1898.8" font-family="Times,serif" font-size="9.00">Marshal</text>
<text text-anchor="middle" x="1313" y="-1888.8" font-family="Times,serif" font-size="9.00">0.03s (0.023%)</text>
<text text-anchor="middle" x="1313" y="-1878.8" font-family="Times,serif" font-size="9.00">of 5.16s (3.99%)</text>
</a>
</g>
</g>
<!-- N66 -->
<g id="node66" class="node"><title>N66</title>
<g id="a_node66"><a xlink:title="encoding/json.structEncoder.encode (3.89s)">
<polygon fill="#edebe9" stroke="#b2a997" points="1522,-1817.5 1426,-1817.5 1426,-1744.5 1522,-1744.5 1522,-1817.5"/>
<text text-anchor="middle" x="1474" y="-1803.9" font-family="Times,serif" font-size="12.00">json</text>
<text text-anchor="middle" x="1474" y="-1790.9" font-family="Times,serif" font-size="12.00">structEncoder</text>
<text text-anchor="middle" x="1474" y="-1777.9" font-family="Times,serif" font-size="12.00">encode</text>
<text text-anchor="middle" x="1474" y="-1764.9" font-family="Times,serif" font-size="12.00">0.71s (0.55%)</text>
<text text-anchor="middle" x="1474" y="-1751.9" font-family="Times,serif" font-size="12.00">of 3.89s (3.01%)</text>
</a>
</g>
</g>
<!-- N58&#45;&gt;N66 -->
<g id="edge35" class="edge"><title>N58&#45;&gt;N66</title>
<g id="a_edge35"><a xlink:title="encoding/json.Marshal ... encoding/json.structEncoder.encode (3.89s)">
<path fill="none" stroke="#b2a997" stroke-dasharray="1,5" d="M1345.97,-1871.86C1366.54,-1857.42 1393.57,-1838.45 1417.49,-1821.66"/>
<polygon fill="#b2a997" stroke="#b2a997" points="1419.63,-1824.44 1425.8,-1815.83 1415.61,-1818.71 1419.63,-1824.44"/>
</a>
</g>
<g id="a_edge35&#45;label"><a xlink:title="encoding/json.Marshal ... encoding/json.structEncoder.encode (3.89s)">
<text text-anchor="middle" x="1411" y="-1840.3" font-family="Times,serif" font-size="14.00"> 3.89s</text>
</a>
</g>
</g>
<!-- N58&#45;&gt;N31 -->
<g id="edge100" class="edge"><title>N58&#45;&gt;N31</title>
<g id="a_edge100"><a xlink:title="encoding/json.Marshal &#45;&gt; runtime.growslice (0.54s)">
<path fill="none" stroke="#b2b1ae" d="M1274.68,-1882.48C1237.87,-1870.47 1180.67,-1851.87 1131,-1836 953.457,-1779.26 912.156,-1753.86 731,-1710 670.956,-1695.46 238.264,-1643.61 183,-1616 163.313,-1606.16 157.825,-1601.16 147,-1582 101.807,-1502 112.834,-1471.36 103,-1380 83.9985,-1203.49 107.625,-1157.09 137,-982 145.497,-931.356 160.736,-873.997 171.103,-837.678"/>
<polygon fill="#b2b1ae" stroke="#b2b1ae" points="174.481,-838.593 173.888,-828.015 167.755,-836.655 174.481,-838.593"/>
</a>
</g>
<g id="a_edge100&#45;label"><a xlink:title="encoding/json.Marshal &#45;&gt; runtime.growslice (0.54s)">
<text text-anchor="middle" x="120" y="-1368.3" font-family="Times,serif" font-size="14.00"> 0.54s</text>
</a>
</g>
</g>
<!-- N59&#45;&gt;N43 -->
<g id="edge115" class="edge"><title>N59&#45;&gt;N43</title>
<g id="a_edge115"><a xlink:title="adapter/service/collect.(*Collect).WriteAll &#45;&gt; runtime.convTstring (0.35s)">
<path fill="none" stroke="#b2b2b0" d="M1796.48,-1295.57C1819.95,-1286.9 1848.59,-1273.37 1869,-1254 1906.36,-1218.54 1883.44,-1183.74 1924,-1152 1949.3,-1132.2 1964.73,-1147.23 1994,-1134 2018.49,-1122.93 2022.04,-1115.5 2044,-1100 2048.49,-1096.83 2053.16,-1093.49 2057.8,-1090.14"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="2060.04,-1092.83 2066.08,-1084.12 2055.93,-1087.17 2060.04,-1092.83"/>
</a>
</g>
<g id="a_edge115&#45;label"><a xlink:title="adapter/service/collect.(*Collect).WriteAll &#45;&gt; runtime.convTstring (0.35s)">
<text text-anchor="middle" x="1941" y="-1182.3" font-family="Times,serif" font-size="14.00"> 0.35s</text>
</a>
</g>
</g>
<!-- N59&#45;&gt;N21 -->
<g id="edge124" class="edge"><title>N59&#45;&gt;N21</title>
<g id="a_edge124"><a xlink:title="adapter/service/collect.(*Collect).WriteAll &#45;&gt; runtime.slicebytetostring (0.30s)">
<path fill="none" stroke="#b2b2b0" d="M1796.33,-1288.62C1811.87,-1279.77 1828.72,-1268.09 1841,-1254 1873.67,-1216.51 1853.43,-1188.69 1887,-1152 1898.02,-1139.96 1910.76,-1148.09 1919,-1134 1922.59,-1127.86 1923.72,-1123.32 1919,-1118 1896.41,-1092.57 1793.18,-1121.71 1767,-1100 1724.7,-1064.91 1751.41,-1032.62 1730,-982 1711.1,-937.309 1712.45,-920.173 1678,-886 1655.13,-863.317 1644.14,-863.365 1614,-852 1561.72,-832.284 1499.33,-818.494 1455.26,-810.268"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1455.77,-806.805 1445.31,-808.448 1454.51,-813.69 1455.77,-806.805"/>
</a>
</g>
<g id="a_edge124&#45;label"><a xlink:title="adapter/service/collect.(*Collect).WriteAll &#45;&gt; runtime.slicebytetostring (0.30s)">
<text text-anchor="middle" x="1784" y="-1054.3" font-family="Times,serif" font-size="14.00"> 0.30s</text>
</a>
</g>
</g>
<!-- N59&#45;&gt;N18 -->
<g id="edge137" class="edge"><title>N59&#45;&gt;N18</title>
<g id="a_edge137"><a xlink:title="adapter/service/collect.(*Collect).WriteAll &#45;&gt; github.com/ivanabc/log4go.Logger.intLogf (0.21s)">
<path fill="none" stroke="#b2b2b1" d="M1713.48,-1281.47C1688.24,-1266.51 1654.83,-1248.72 1623,-1238 1578.9,-1223.14 1561.57,-1235.53 1513.69,-1219.65"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1514.63,-1216.27 1504.03,-1216.21 1512.28,-1222.86 1514.63,-1216.27"/>
</a>
</g>
<g id="a_edge137&#45;label"><a xlink:title="adapter/service/collect.(*Collect).WriteAll &#45;&gt; github.com/ivanabc/log4go.Logger.intLogf (0.21s)">
<text text-anchor="middle" x="1678" y="-1242.3" font-family="Times,serif" font-size="14.00"> 0.21s</text>
</a>
</g>
</g>
<!-- N60&#45;&gt;N63 -->
<g id="edge50" class="edge"><title>N60&#45;&gt;N63</title>
<g id="a_edge50"><a xlink:title="adapter/service/collect.(*Collect).write ... os.(*File).Write (2.22s)">
<path fill="none" stroke="#b2ada3" stroke-dasharray="1,5" d="M1522.58,-1156.68C1519.4,-1154.98 1516.19,-1153.4 1513,-1152 1483.58,-1139.11 1468.55,-1153.46 1443,-1134 1431.18,-1125 1422.08,-1111.75 1415.35,-1098.88"/>
<polygon fill="#b2ada3" stroke="#b2ada3" points="1418.38,-1097.11 1410.87,-1089.64 1412.08,-1100.16 1418.38,-1097.11"/>
</a>
</g>
<g id="a_edge50&#45;label"><a xlink:title="adapter/service/collect.(*Collect).write ... os.(*File).Write (2.22s)">
<text text-anchor="middle" x="1460" y="-1122.3" font-family="Times,serif" font-size="14.00"> 2.22s</text>
</a>
</g>
</g>
<!-- N60&#45;&gt;N24 -->
<g id="edge128" class="edge"><title>N60&#45;&gt;N24</title>
<g id="a_edge128"><a xlink:title="adapter/service/collect.(*Collect).write ... fmt.Sprintf (0.28s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1578.16,-1154.38C1583.9,-1142.75 1590.85,-1129.53 1598,-1118 1603.3,-1109.46 1609.5,-1100.59 1615.54,-1092.41"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1618.52,-1094.27 1621.74,-1084.18 1612.93,-1090.06 1618.52,-1094.27"/>
</a>
</g>
<g id="a_edge128&#45;label"><a xlink:title="adapter/service/collect.(*Collect).write ... fmt.Sprintf (0.28s)">
<text text-anchor="middle" x="1615" y="-1122.3" font-family="Times,serif" font-size="14.00"> 0.28s</text>
</a>
</g>
</g>
<!-- N61&#45;&gt;N38 -->
<g id="edge143" class="edge"><title>N61&#45;&gt;N38</title>
<g id="a_edge143"><a xlink:title="runtime.unlock2 ... runtime.futex (0.16s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M2432.23,-894.694C2433.94,-869.786 2436,-833.069 2436,-801 2436,-801 2436,-801 2436,-382 2436,-359.021 2436.48,-333.219 2436.98,-312.943"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="2440.48,-312.973 2437.23,-302.888 2433.48,-312.796 2440.48,-312.973"/>
</a>
</g>
<g id="a_edge143&#45;label"><a xlink:title="runtime.unlock2 ... runtime.futex (0.16s)">
<text text-anchor="middle" x="2453" y="-580.3" font-family="Times,serif" font-size="14.00"> 0.16s</text>
</a>
</g>
</g>
<!-- N62&#45;&gt;N50 -->
<g id="edge60" class="edge"><title>N62&#45;&gt;N50</title>
<g id="a_edge60"><a xlink:title="github.com/Shopify/sarama.(*Broker).send ... internal/poll.(*FD).Write (1.95s)">
<path fill="none" stroke="#b2aea5" stroke-dasharray="1,5" d="M559.722,-895.927C565.145,-892.029 570.997,-888.507 577,-886 643.44,-858.252 666.514,-876.601 738,-868 911.602,-847.113 1117.46,-819.847 1209.42,-807.536"/>
<polygon fill="#b2aea5" stroke="#b2aea5" points="1210.09,-810.977 1219.54,-806.18 1209.16,-804.039 1210.09,-810.977"/>
</a>
</g>
<g id="a_edge60&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).send ... internal/poll.(*FD).Write (1.95s)">
<text text-anchor="middle" x="884" y="-856.3" font-family="Times,serif" font-size="14.00"> 1.95s</text>
</a>
</g>
</g>
<!-- N62&#45;&gt;N78 -->
<g id="edge135" class="edge"><title>N62&#45;&gt;N78</title>
<g id="a_edge135"><a xlink:title="github.com/Shopify/sarama.(*Broker).send ... github.com/rcrowley/go&#45;metrics.(*StandardHistogram).Update (0.23s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M563.378,-895.916C567.872,-892.462 572.49,-889.071 577,-886 604.787,-867.082 637.061,-848.405 664.539,-833.397"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="666.536,-836.295 673.66,-828.453 663.2,-830.141 666.536,-836.295"/>
</a>
</g>
<g id="a_edge135&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).send ... github.com/rcrowley/go&#45;metrics.(*StandardHistogram).Update (0.23s)">
<text text-anchor="middle" x="647" y="-856.3" font-family="Times,serif" font-size="14.00"> 0.23s</text>
</a>
</g>
</g>
<!-- N62&#45;&gt;N13 -->
<g id="edge136" class="edge"><title>N62&#45;&gt;N13</title>
<g id="a_edge136"><a xlink:title="github.com/Shopify/sarama.(*Broker).send ... [adapter] (0.22s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M540.157,-895.994C554.561,-860.307 584.354,-799.273 630,-766 731.461,-692.042 881.413,-671.09 965.305,-665.22"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="965.697,-668.702 975.449,-664.561 965.244,-661.716 965.697,-668.702"/>
</a>
</g>
<g id="a_edge136&#45;label"><a xlink:title="github.com/Shopify/sarama.(*Broker).send ... [adapter] (0.22s)">
<text text-anchor="middle" x="647" y="-796.3" font-family="Times,serif" font-size="14.00"> 0.22s</text>
</a>
</g>
</g>
<!-- N63&#45;&gt;N50 -->
<g id="edge39" class="edge"><title>N63&#45;&gt;N50</title>
<g id="a_edge39"><a xlink:title="os.(*File).Write &#45;&gt; internal/poll.(*FD).Write (3.36s)">
<path fill="none" stroke="#b2aa9b" d="M1404.01,-1026.18C1407.95,-981.769 1408.04,-899.866 1364,-852 1348.42,-835.069 1326.03,-823.163 1305.83,-815.138"/>
<polygon fill="#b2aa9b" stroke="#b2aa9b" points="1307.03,-811.849 1296.44,-811.613 1304.57,-818.403 1307.03,-811.849"/>
</a>
</g>
<g id="a_edge39&#45;label"><a xlink:title="os.(*File).Write &#45;&gt; internal/poll.(*FD).Write (3.36s)">
<text text-anchor="middle" x="1422" y="-921.3" font-family="Times,serif" font-size="14.00"> 3.36s</text>
</a>
</g>
</g>
<!-- N64&#45;&gt;N8 -->
<g id="edge77" class="edge"><title>N64&#45;&gt;N8</title>
<g id="a_edge77"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (1.05s)">
<path fill="none" stroke="#b2b0ab" d="M822.31,-1976.95C802.776,-1942.73 767.733,-1883.56 733,-1836 729.282,-1830.91 725.212,-1825.7 721.079,-1820.61"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="723.651,-1818.23 714.574,-1812.76 718.262,-1822.7 723.651,-1818.23"/>
</a>
</g>
<g id="a_edge77&#45;label"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (1.05s)">
<text text-anchor="middle" x="807" y="-1892.3" font-family="Times,serif" font-size="14.00"> 1.05s</text>
</a>
</g>
</g>
<!-- N64&#45;&gt;N9 -->
<g id="edge117" class="edge"><title>N64&#45;&gt;N9</title>
<g id="a_edge117"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; adapter/service/wm_logger.GetBaseFromLog (0.34s)">
<path fill="none" stroke="#b2b2b0" d="M893.198,-1986.89C941.674,-1970.71 1013.94,-1945.94 1076,-1922 1077.64,-1921.37 1079.3,-1920.72 1080.98,-1920.05"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1082.7,-1923.13 1090.67,-1916.15 1080.09,-1916.64 1082.7,-1923.13"/>
</a>
</g>
<g id="a_edge117&#45;label"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; adapter/service/wm_logger.GetBaseFromLog (0.34s)">
<text text-anchor="middle" x="1042" y="-1944.3" font-family="Times,serif" font-size="14.00"> 0.34s</text>
</a>
</g>
</g>
<!-- N64&#45;&gt;N24 -->
<g id="edge130" class="edge"><title>N64&#45;&gt;N24</title>
<g id="a_edge130"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; fmt.Sprintf (0.27s)">
<path fill="none" stroke="#b2b2b0" d="M893.07,-1993.42C923.369,-1987.25 961.677,-1979.76 996,-1974 1053.09,-1964.42 1069.99,-1974.03 1125,-1956 1155.23,-1946.09 1170.12,-1948.32 1188,-1922 1260.43,-1815.38 1193.78,-1759.76 1222,-1634 1235.76,-1572.7 1250.77,-1560.69 1267,-1500 1307.75,-1347.59 1238.63,-1275.34 1337,-1152 1349.16,-1136.76 1359.29,-1142.15 1377,-1134 1392.17,-1127.02 1394.9,-1122.43 1411,-1118 1490.22,-1096.19 1519.56,-1132.56 1595,-1100 1600.48,-1097.64 1605.77,-1094.39 1610.71,-1090.75"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1613.07,-1093.35 1618.66,-1084.35 1608.68,-1087.9 1613.07,-1093.35"/>
</a>
</g>
<g id="a_edge130&#45;label"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; fmt.Sprintf (0.27s)">
<text text-anchor="middle" x="1279" y="-1546.3" font-family="Times,serif" font-size="14.00"> 0.27s</text>
</a>
</g>
</g>
<!-- N64&#45;&gt;N31 -->
<g id="edge151" class="edge"><title>N64&#45;&gt;N31</title>
<g id="a_edge151"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; runtime.growslice (0.13s)">
<path fill="none" stroke="#b2b2b1" d="M782.845,-1977.54C655.655,-1913.94 345.251,-1756.99 248,-1692 184.221,-1649.38 160.527,-1643.74 115,-1582 -34.3004,-1379.54 71.7263,-1264.31 112,-1016 121.419,-957.922 117.465,-941.499 137,-886 142.922,-869.174 151.738,-851.59 160.001,-836.809"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="163.113,-838.418 165.044,-828.001 157.038,-834.94 163.113,-838.418"/>
</a>
</g>
<g id="a_edge151&#45;label"><a xlink:title="adapter/service/wm_logger.(*DungeonFightMessage).Execute &#45;&gt; runtime.growslice (0.13s)">
<text text-anchor="middle" x="70" y="-1428.3" font-family="Times,serif" font-size="14.00"> 0.13s</text>
</a>
</g>
</g>
<!-- N66&#45;&gt;N32 -->
<g id="edge129" class="edge"><title>N66&#45;&gt;N32</title>
<g id="a_edge129"><a xlink:title="encoding/json.structEncoder.encode ... runtime.memmove (0.28s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M1522.18,-1749.51C1526.74,-1747.4 1531.39,-1745.51 1536,-1744 1606.51,-1720.86 1628.36,-1735.19 1702,-1726 1799.77,-1713.8 2141,-1762.52 2141,-1664 2141,-1664 2141,-1664 2138,-1245 2138,-1241.89 2137.87,-1241.11 2138,-1238 2140.67,-1176.49 2147.33,-1161.51 2150,-1100 2151.62,-1062.7 2164.43,-1050.43 2150,-1016 2105.43,-909.62 2022.58,-933.939 1965,-834 1940.42,-791.331 1929.36,-734.878 1924.53,-698.742"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1927.99,-698.159 1923.28,-688.667 1921.04,-699.023 1927.99,-698.159"/>
</a>
</g>
<g id="a_edge129&#45;label"><a xlink:title="encoding/json.structEncoder.encode ... runtime.memmove (0.28s)">
<text text-anchor="middle" x="2158" y="-1242.3" font-family="Times,serif" font-size="14.00"> 0.28s</text>
</a>
</g>
</g>
<!-- N67&#45;&gt;N9 -->
<g id="edge66" class="edge"><title>N67&#45;&gt;N9</title>
<g id="a_edge66"><a xlink:title="adapter/service/wm_logger.(*AddCoinMessage).Execute &#45;&gt; adapter/service/wm_logger.GetBaseFromLog (1.40s)">
<path fill="none" stroke="#b2b0a8" d="M1063.97,-1976.84C1070.12,-1964.9 1078.07,-1951.2 1087,-1940 1089.86,-1936.41 1093.04,-1932.86 1096.36,-1929.44"/>
<polygon fill="#b2b0a8" stroke="#b2b0a8" points="1099.08,-1931.68 1103.79,-1922.18 1094.19,-1926.67 1099.08,-1931.68"/>
</a>
</g>
<g id="a_edge66&#45;label"><a xlink:title="adapter/service/wm_logger.(*AddCoinMessage).Execute &#45;&gt; adapter/service/wm_logger.GetBaseFromLog (1.40s)">
<text text-anchor="middle" x="1104" y="-1944.3" font-family="Times,serif" font-size="14.00"> 1.40s</text>
</a>
</g>
</g>
<!-- N67&#45;&gt;N8 -->
<g id="edge90" class="edge"><title>N67&#45;&gt;N8</title>
<g id="a_edge90"><a xlink:title="adapter/service/wm_logger.(*AddCoinMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.71s)">
<path fill="none" stroke="#b2b1ad" d="M1030.76,-1976.71C1022,-1965.02 1011.4,-1951.52 1001,-1940 993.208,-1931.36 990.288,-1930.16 982,-1922 943.933,-1884.51 944.265,-1862.73 898,-1836 850.31,-1808.45 788.583,-1794.77 744.23,-1788.09"/>
<polygon fill="#b2b1ad" stroke="#b2b1ad" points="744.592,-1784.61 734.197,-1786.66 743.6,-1791.54 744.592,-1784.61"/>
</a>
</g>
<g id="a_edge90&#45;label"><a xlink:title="adapter/service/wm_logger.(*AddCoinMessage).Execute &#45;&gt; github.com/json&#45;iterator/go.(*frozenConfig).Unmarshal (0.71s)">
<text text-anchor="middle" x="999" y="-1892.3" font-family="Times,serif" font-size="14.00"> 0.71s</text>
</a>
</g>
</g>
<!-- N68&#45;&gt;N33 -->
<g id="edge25" class="edge"><title>N68&#45;&gt;N33</title>
<g id="a_edge25"><a xlink:title="runtime.runqgrab &#45;&gt; runtime.usleep (5.41s)">
<path fill="none" stroke="#b2a48d" d="M2781,-769.825C2781,-750.996 2781,-726.195 2781,-705.229"/>
<polygon fill="#b2a48d" stroke="#b2a48d" points="2784.5,-704.979 2781,-694.979 2777.5,-704.979 2784.5,-704.979"/>
</a>
</g>
<g id="a_edge25&#45;label"><a xlink:title="runtime.runqgrab &#45;&gt; runtime.usleep (5.41s)">
<text text-anchor="middle" x="2798" y="-736.3" font-family="Times,serif" font-size="14.00"> 5.41s</text>
</a>
</g>
</g>
<!-- N69 -->
<g id="node69" class="node"><title>N69</title>
<g id="a_node69"><a xlink:title="fmt.(*pp).printArg (1.98s)">
<polygon fill="#edeceb" stroke="#b2aea4" points="2064,-834 1974,-834 1974,-766 2064,-766 2064,-834"/>
<text text-anchor="middle" x="2019" y="-821.2" font-family="Times,serif" font-size="11.00">fmt</text>
<text text-anchor="middle" x="2019" y="-809.2" font-family="Times,serif" font-size="11.00">(*pp)</text>
<text text-anchor="middle" x="2019" y="-797.2" font-family="Times,serif" font-size="11.00">printArg</text>
<text text-anchor="middle" x="2019" y="-785.2" font-family="Times,serif" font-size="11.00">0.40s (0.31%)</text>
<text text-anchor="middle" x="2019" y="-773.2" font-family="Times,serif" font-size="11.00">of 1.98s (1.53%)</text>
</a>
</g>
</g>
<!-- N69&#45;&gt;N37 -->
<g id="edge134" class="edge"><title>N69&#45;&gt;N37</title>
<g id="a_edge134"><a xlink:title="fmt.(*pp).printArg ... runtime.gcWriteBarrier (0.24s)">
<path fill="none" stroke="#b2b2b0" stroke-dasharray="1,5" d="M2064.31,-773.402C2068.92,-770.874 2073.54,-768.37 2078,-766 2128.32,-739.242 2186.39,-710.085 2227.68,-689.645"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="2229.33,-692.731 2236.75,-685.162 2226.23,-686.456 2229.33,-692.731"/>
</a>
</g>
<g id="a_edge134&#45;label"><a xlink:title="fmt.(*pp).printArg ... runtime.gcWriteBarrier (0.24s)">
<text text-anchor="middle" x="2157" y="-736.3" font-family="Times,serif" font-size="14.00"> 0.24s</text>
</a>
</g>
</g>
<!-- N69&#45;&gt;N32 -->
<g id="edge140" class="edge"><title>N69&#45;&gt;N32</title>
<g id="a_edge140"><a xlink:title="fmt.(*pp).printArg ... runtime.memmove (0.20s)">
<path fill="none" stroke="#b2b2b1" stroke-dasharray="1,5" d="M1995.03,-765.731C1979.99,-744.858 1960.64,-718.017 1945.5,-697.006"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1948.19,-694.751 1939.51,-688.684 1942.51,-698.843 1948.19,-694.751"/>
</a>
</g>
<g id="a_edge140&#45;label"><a xlink:title="fmt.(*pp).printArg ... runtime.memmove (0.20s)">
<text text-anchor="middle" x="1999" y="-736.3" font-family="Times,serif" font-size="14.00"> 0.20s</text>
</a>
</g>
</g>
<!-- N73&#45;&gt;N26 -->
<g id="edge37" class="edge"><title>N73&#45;&gt;N26</title>
<g id="a_edge37"><a xlink:title="github.com/json&#45;iterator/go.(*sliceDecoder).doDecode &#45;&gt; github.com/json&#45;iterator/go.(*OptionalDecoder).Decode (3.45s)">
<path fill="none" stroke="#b2aa9a" d="M296.5,-893.279C296.248,-877.723 295.937,-858.626 295.664,-841.849"/>
<polygon fill="#b2aa9a" stroke="#b2aa9a" points="299.162,-841.697 295.5,-831.755 292.163,-841.811 299.162,-841.697"/>
</a>
</g>
<g id="a_edge37&#45;label"><a xlink:title="github.com/json&#45;iterator/go.(*sliceDecoder).doDecode &#45;&gt; github.com/json&#45;iterator/go.(*OptionalDecoder).Decode (3.45s)">
<text text-anchor="middle" x="314" y="-856.3" font-family="Times,serif" font-size="14.00"> 3.45s</text>
</a>
</g>
</g>
<!-- N74&#45;&gt;N58 -->
<g id="edge28" class="edge"><title>N74&#45;&gt;N58</title>
<g id="a_edge28"><a xlink:title="adapter/service/wm_logger.(*MessageM).GetMessage &#45;&gt; encoding/json.Marshal (5.16s)">
<path fill="none" stroke="#b2a48e" d="M1337.99,-1974.42C1336,-1968.32 1333.93,-1961.96 1332,-1956 1329.26,-1947.54 1326.3,-1938.39 1323.56,-1929.88"/>
<polygon fill="#b2a48e" stroke="#b2a48e" points="1326.86,-1928.71 1320.47,-1920.27 1320.2,-1930.86 1326.86,-1928.71"/>
</a>
</g>
<g id="a_edge28&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).GetMessage &#45;&gt; encoding/json.Marshal (5.16s)">
<text text-anchor="middle" x="1349" y="-1944.3" font-family="Times,serif" font-size="14.00"> 5.16s</text>
</a>
</g>
</g>
<!-- N74&#45;&gt;N70 -->
<g id="edge132" class="edge"><title>N74&#45;&gt;N70</title>
<g id="a_edge132"><a xlink:title="adapter/service/wm_logger.(*MessageM).GetMessage &#45;&gt; sync.(*Pool).Get (0.25s)">
<path fill="none" stroke="#b2b2b0" d="M1361.12,-1974.35C1363.14,-1968.37 1364.92,-1962.07 1366,-1956 1369.38,-1937 1366.23,-1916.93 1360,-1870 1314.21,-1525.27 1252.01,-1446.37 1221,-1100 1217.67,-1062.82 1197.03,-1044.62 1221,-1016 1235.64,-998.522 1299.78,-1003.09 1322,-998 1376.45,-985.529 1391.73,-986.181 1443,-964 1445.84,-962.772 1448.7,-961.436 1451.57,-960.023"/>
<polygon fill="#b2b2b0" stroke="#b2b2b0" points="1453.4,-963.013 1460.66,-955.291 1450.17,-956.804 1453.4,-963.013"/>
</a>
</g>
<g id="a_edge132&#45;label"><a xlink:title="adapter/service/wm_logger.(*MessageM).GetMessage &#45;&gt; sync.(*Pool).Get (0.25s)">
<text text-anchor="middle" x="1306" y="-1488.3" font-family="Times,serif" font-size="14.00"> 0.25s</text>
</a>
</g>
</g>
<!-- N75&#45;&gt;N69 -->
<g id="edge58" class="edge"><title>N75&#45;&gt;N69</title>
<g id="a_edge58"><a xlink:title="fmt.(*pp).doPrintf &#45;&gt; fmt.(*pp).printArg (1.98s)">
<path fill="none" stroke="#b2aea4" d="M1834.16,-893.916C1838.8,-891.185 1843.47,-888.507 1848,-886 1886.31,-864.792 1930.61,-842.842 1964.52,-826.56"/>
<polygon fill="#b2aea4" stroke="#b2aea4" points="1966.28,-829.596 1973.79,-822.124 1963.26,-823.281 1966.28,-829.596"/>
</a>
</g>
<g id="a_edge58&#45;label"><a xlink:title="fmt.(*pp).doPrintf &#45;&gt; fmt.(*pp).printArg (1.98s)">
<text text-anchor="middle" x="1926" y="-856.3" font-family="Times,serif" font-size="14.00"> 1.98s</text>
</a>
</g>
</g>
<!-- N76&#45;&gt;N1 -->
<g id="edge79" class="edge"><title>N76&#45;&gt;N1</title>
<g id="a_edge79"><a xlink:title="runtime.convT64 &#45;&gt; runtime.mallocgc (0.99s)">
<path fill="none" stroke="#b2b0ab" d="M2214.71,-1159.73C2226.03,-1072.48 2251.52,-780.019 2114,-610 2092.25,-583.109 2077.3,-585.331 2044,-576 1935.12,-545.495 1613.61,-524.673 1451.6,-515.813"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="1451.33,-512.294 1441.16,-515.247 1450.96,-519.283 1451.33,-512.294"/>
</a>
</g>
<g id="a_edge79&#45;label"><a xlink:title="runtime.convT64 &#45;&gt; runtime.mallocgc (0.99s)">
<text text-anchor="middle" x="2232" y="-856.3" font-family="Times,serif" font-size="14.00"> 0.99s</text>
</a>
</g>
</g>
<!-- N79&#45;&gt;N27 -->
<g id="edge78" class="edge"><title>N79&#45;&gt;N27</title>
<g id="a_edge78"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.schedule (1.01s)">
<path fill="none" stroke="#b2b0ab" d="M2712.42,-1284.88C2716.95,-1270.71 2723.84,-1252.55 2733,-1238 2736.96,-1231.7 2741.77,-1225.5 2746.8,-1219.71"/>
<polygon fill="#b2b0ab" stroke="#b2b0ab" points="2749.58,-1221.85 2753.73,-1212.1 2744.41,-1217.14 2749.58,-1221.85"/>
</a>
</g>
<g id="a_edge78&#45;label"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.schedule (1.01s)">
<text text-anchor="middle" x="2750" y="-1242.3" font-family="Times,serif" font-size="14.00"> 1.01s</text>
</a>
</g>
</g>
<!-- N79&#45;&gt;N46 -->
<g id="edge105" class="edge"><title>N79&#45;&gt;N46</title>
<g id="a_edge105"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.lock2 (0.45s)">
<path fill="none" stroke="#b2b1af" d="M2696.53,-1284.96C2670.69,-1222.08 2598.67,-1046.86 2565.78,-966.822"/>
<polygon fill="#b2b1af" stroke="#b2b1af" points="2568.82,-965.009 2561.78,-957.09 2562.34,-967.67 2568.82,-965.009"/>
</a>
</g>
<g id="a_edge105&#45;label"><a xlink:title="runtime.goschedImpl &#45;&gt; runtime.lock2 (0.45s)">
<text text-anchor="middle" x="2650" y="-1122.3" font-family="Times,serif" font-size="14.00"> 0.45s</text>
</a>
</g>
</g>
<!-- N80&#45;&gt;N13 -->
<g id="edge148" class="edge"><title>N80&#45;&gt;N13</title>
<g id="a_edge148"><a xlink:title="runtime.Caller &#45;&gt; [adapter] (0.15s)">
<path fill="none" stroke="#b2b2b1" d="M1317.76,-1033.98C1339.26,-999.439 1371.99,-933.221 1343,-886 1334.26,-871.771 1291.58,-858.005 1276,-852 1247.63,-841.064 1234.45,-852.074 1210,-834 1181.25,-812.744 1191.9,-791.662 1167,-766 1138.24,-736.364 1123.09,-738.7 1090,-714 1081.45,-707.619 1072.47,-700.495 1064.08,-693.642"/>
<polygon fill="#b2b2b1" stroke="#b2b2b1" points="1066.2,-690.85 1056.26,-687.189 1061.75,-696.251 1066.2,-690.85"/>
</a>
</g>
<g id="a_edge148&#45;label"><a xlink:title="runtime.Caller &#45;&gt; [adapter] (0.15s)">
<text text-anchor="middle" x="1334" y="-856.3" font-family="Times,serif" font-size="14.00"> 0.15s</text>
</a>
</g>
</g>
</g>
</g></svg>
