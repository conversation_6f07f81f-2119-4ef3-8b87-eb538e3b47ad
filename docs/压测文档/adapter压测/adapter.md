# adapter压测报告

### 测试环境

|      系统       |  配置  |     地址     |     备注      |
| :-------------: | :----: | :----------: | :-----------: |
| 阿里云/centos-7 | 8核16G | 10.148.74.30 | adapter服务器 |
| 阿里云/centos-7 | 8核16G | 10.148.74.29 | kafka服务器（3个Partition） |



### 数数的压测

1. 测试结果数据

    | 统计项  | 数值  |
    | :---- | :-------- |
    | kafka数据量    | 1864w |
    | 生成数数日志量 | 6574w |
    | 总耗时 | 1736s |
    | 每秒处理kafka数据量 | 1.07w/s |
    | 每秒生成数数日志量 | 3.78w/s |

2. 系统性能监控

   - 主要性能指标
     
   | 统计项   | 数值      |
   | -------- | --------- |
   | cpu      | 42%       |
   | 内存     | 1.05G     |
   | 网络下载 | 107.2Mb/s |
   
   - 压测中

     ![](ta-1.png)

   - 结束后
     ![](ta-2.png)

3. pprof
   
   ![](ta-3.png)
   
4. 这是数数压测生成的[profile-svg](./profile_adapter_ta.svg)



### 完美的压测

1. 测试结果数据

    | 统计项  | 数值  |
    | :---- | :-------- |
    | kafka数据量    | 1864w （日志）+ 15w（快照） |
    | 生成数数日志量 | 2094w |
    | 总耗时 | 410s |
    | 每秒生成数数日志量 | 5.1w/s |

2. 系统性能监控

   - 主要性能指标

     | 统计项   | 数值      |
     | -------- | --------- |
     | cpu      | 62%       |
     | 内存     | 1.05G     |
     | 网络下载 | 441.3Mb/s |
    - 压测中
      ![](wm-1.png)
    - 结束后
      ![](wm-2.png)

3. pprof

   ![](wm-3.png)

4. 这是完美压测生成的[profile-svg](./profile_adapter_wm.svg)



### 测试步骤

1. 生成组装测试数据
2. kafka创建新的topic，专门用于压测
3. 将准备好的数据，导入新建的topic中
4. 将adapter中的kafkaConsumer修改为，从最老数据开始消费（用于测试的临时修改）
5. 生成可执行文件
6. 将项目发布到用于压测的adapter服务器
7. 修改配置文件
8. 启动adapter，执行压测