#!/bin/bash
robot_start_time=`cat ../log/robot.log*|grep "all login finished"|awk -F "." '{print $1}'|awk -F "[" '{print $2}'`
robot_end_time=`cat ../log/robot.log*|grep "CommandStat"|awk -F "." '{print $1}'|awk -F "[" '{print $2}'|sort|tail -1`
robot_start_time_int=`date -d "$robot_start_time" +%s`
robot_end_time_int=`date -d "$robot_end_time" +%s` #变量必须用双引号
robot_use_time=$[$robot_end_time_int-$robot_start_time_int] #减法必须这样。。。直接-不行 []不行 `expr $a-$b`也不行
robot_count=`cat ../log/robot.log*|grep "online suc"|wc -l`
robot_msg_count=`cat ../log/robot.log*|grep "CommandStat"|grep -v cmd:11006|grep -v cmd:10100|wc -l`
robot_per_msg=$[$robot_msg_count/$robot_use_time] 

echo "耗时:$robot_use_time(s)"
echo "登录成功机器人总数:$robot_count"
echo "总消息数:$robot_msg_count"
echo "平均每秒消息数:$robot_per_msg"