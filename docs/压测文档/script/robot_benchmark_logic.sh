#!/bin/bash

if [ $# -lt 5 ]; then
    exit 1
fi

module=$1
if [ ! "$module" ] ;then
  echo "module is empty";
  exit 1
fi

mode=$6
stage=$7
join_guild=$8
send_resource=$9
node=${10}
gateway=${11}
profile_addr=${12}

if [ ! "$join_guild" ] ;then
  join_guild=false
fi

if [ ! "$send_resource" ] ;then
  send_resource=true
fi

if [ ! "$node" ] ;then
  node=1010600001
fi

if [ ! "$gateway" ] ;then
  gateway=10.6.6.14:38422
fi

if [ ! "$profile_addr" ] ;then
  profile_addr=10.6.6.17:30001
fi


config_file="../config/robot.xml"

echo "开启${module}模块"
# 这里sed的命令必须用双引号括住
sed -i "s/name=\"${module}\" prob=\"0\"/name=\"${module}\" prob=\"1\"/g" $config_file

./clean_log.sh
date
echo "开始压测"
./robot -node=$node  -uuid=$2 -num=$3 -tnum=$4 -slice=10 -timer=$5  -mode=$mode -stage=$stage -join_guild=$join_guild -send_resource=$send_resource -gateway=$gateway -profile_addr=$profile_addr -log_level=INFO &> ../log/robot_error.log


date
echo "压测结束"
