#!/bin/bash

current=`date "+%Y-%m-%d %H:%M:%S"`
echo "$current 压测数据分析"

echo "压测基础情况"
robot_start_time=`cat ../log/robot.log*|grep "all login finished"|awk -F "." '{print $1}'|awk -F "[" '{print $2}'`
robot_end_time=`cat ../log/robot.log*|grep "CommandStat"|awk -F "." '{print $1}'|awk -F "[" '{print $2}'|sort|tail -1`
robot_start_time_int=`date -d "$robot_start_time" +%s`
robot_end_time_int=`date -d "$robot_end_time" +%s` #变量必须用双引号
robot_use_time=$[$robot_end_time_int-$robot_start_time_int] #减法必须这样。。。直接-不行 []不行 `expr $a-$b`也不行
robot_count=`cat ../log/robot.log*|grep "online suc"|wc -l`
robot_msg_count=`cat ../log/robot.log*|grep "CommandStat"|grep -v cmd:11006|grep -v cmd:10100|wc -l`
robot_per_msg=$[$robot_msg_count/$robot_use_time] 

echo "开始时间：$robot_start_time, 结束时间:$robot_end_time, 耗时:$robot_use_time(s)"
echo "登录成功机器人总数：$robot_count"
echo "总消息数:$robot_msg_count， 平均每秒消息数:$robot_per_msg"


echo -e "\n--------------------------\n"
echo "协议正确率分布"
robot_cmd_info=`cat ../log/robot.log* |grep CommandStat |grep -v cmd:11006|awk  '
	{
		if($0 ~ "ret:true")
		{
			x[$7]++;cx[$7]++;true_t++;all_t++
		} else 
		{
			cx[$7]++;all_t++
		}
	} 
	END {
	{printf ("%-55s %-7s %-7s %-6s %-6s\n","协议", "正确","总数","正确总占比","正确比");}
	{
	for(i in cx)
	{
		printf ("协议:%-50s %7d %7d %15.2f%%  %6.2f%\n",i,x[i], cx[i],x[i]/all_t*100,x[i]/cx[i]*100);
	}
	}
        {printf ("协议正确数:%5d 协议总数:%5d 正确比:%5.2f%\n",true_t,all_t,true_t/all_t*100)}}'`


OLD_IFS=$IFS
IFS=$'\n'
for index in ${robot_cmd_info[@]}
do
  echo ${index}
done
IFS=$OLD_IFS


echo -e "\n--------------------------\n"
echo "协议延迟分布"
robot_cmd_delay_info=`cat ../log/robot.log* |grep CommandStat |grep -v cmd:11006|grep "ret:true"|awk '{print $7":"$8}'|awk -F ":" '
	BEGIN{
		delay_info[1]="0,10";
		delay_info[2]="10,20";
		delay_info[3]="20,50";
		delay_info[4]="50,100";
		delay_info[5]="100,200";
		delay_info[6]="200,500";
		delay_info[7]="500,1000";
		delay_info[8]="1000,100000";
		delay_count=8;
	} 
	{cmd=$1":"$2;total++;x[cmd]++;}
	 {for (i=1;i<=delay_count;i++) 
	   {
		j=delay_info[i];
		split(j,jarr,",");
		if($4+0>=jarr[1]+0 && $4+0 < jarr[2]+0){px[cmd,j]++;}
           }
         } END {
	 {printf("%-53s", "     协议名称")}
        {for (i=1;i<=delay_count;i++) 
          {
 		j=delay_info[i];
		split(j,jarr,",");
		printf ("%3s-%s ", jarr[1],jarr[2])
	  }
	}
	{printf("\n")}
	{for (i in x) 
		{
			printf("协议:%-50s", i);
			{ for(j=1;j<=delay_count;j++)
				 {
					k=delay_info[j];
					printf(" %5.2f% ",px[i,k]/x[i]*100);
				}
			} 
			{printf("\n");}
		 }
	}}'` 
#robot_cmd_delay_info=`cat ../log/robot.log* |grep CommandStat |grep -v cmd:11006|grep -v cmd:10100|grep "ret:true"|awk '{print $7":"$8}'|awk -F ":" 'BEGIN{delay_info["10"]="0-10ms";delay_info["20"]="10-20ms";delay_info["50"]="20-50ms";delay_info["100"]="50-100ms";delay_info["500"]="100-500ms";delay_info["1000"]="500-1000ms";delay_info["100000"]="1000+ms";} {cmd=$1":"$2;total++;x[cmd]++;} {for (i in delay_info) {j=delay_info[i];if($4+0<i+0){px[cmd,j]++;}}} END {for (i in x) {printf("协议:%s 延迟:", i);{ for(j in delay_info) {k=delay_info[j];printf(" %.1f% ",px[i,k]/x[i]*100);}} {printf("\n");} }}'` 
#robot_cmd_delay_info=`cat ../log/robot.log* |grep CommandStat |grep -v cmd:11006|grep -v cmd:10100|grep "ret:true"|awk '{print $7":"$8}'|awk -F ":" 'BEGIN{delay_info["10"]="0-10ms";delay_info["20"]="10-20ms";}END {for (i in delay_info) {printf(" %s %s",i, delay_info[i]);}}'` 



OLD_IFS=$IFS
IFS=$'\n'
for index in ${robot_cmd_delay_info[@]}
do
  echo ${index}
done
IFS=$OLD_IFS



#echo -e "\n---------------------\n"
#echo "logic服数情况"

