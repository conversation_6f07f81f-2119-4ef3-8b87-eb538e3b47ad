## 压测辅助脚本

1. logic_clear_db.sh

   作用: 清除游戏服db数据库

   使用: 放到压测服~/game/bin下面. ./logic_clear_db.sh

   参数：无

2. logic_robot_prepare.sh

   作用：为了保证压测的业务成功概率高，需要修改一些数据表的配置，不同的业务自己添加就好.并且会清空logic的日志。修改完之后会自动重启游戏service

   使用: 放到压测服~/game/bin下面. ./logic_robot_prepare.sh

   参数：无

3. robot_benchmark_logic.sh
   作用：压测业务逻辑脚本

   使用: 放到压测robot服~/game/bin下 执行

   ./robot_benchmark_logic.sh flower root 5000 500 300 2 30 true true 1000100002 192.168.0.89:38422 192.168.0.89:10002
   

   参数：

   - 参数1：模块名，flower， 代表会自动开启flower模块的测试
   - 参数2：机器人账号前缀，想要注册新的账号就换一个不同的前缀就可以了
   - 参数3：总的机器人数量。普通的业务测试，建议用5000
   - 参数4：每秒登录数量。建议用1000
   - 参数5：压测时间。单位s。建议至少300
   - 参数6：测试模式，0-低级测试模式 1-版署建号模式 2-低中高级号测试 3-跟踪协议测试，默认是0
   - 参数7：生成什么样的账号，参数6为2时生效。机器人模板类型
   - 参数8：是否自动加入公会，默认false
   - 参数9：创号时是否发资源，默认true
   - 参数10：logic服务器id，默认的是1010600001
   - 参数11：网关地址，内网请修改为自己的，外网可以不用传，默认的是10.6.6.14:38422
   - 参数12：服务器profile地址，内网请修改为自己的，外网可以不用传，默认的是10.6.6.17:30001


4. 外网压测操作建议，以密林为例

- 为避免测试结果受到创号发奖逻辑影响，压测过程需要执行两步
  - 第一步基础数据准备（创号、发资源、初始数据、加入公会）
  - 第二步正式业务压测
- 数据准备：`./robot_benchmark_logic.sh flower a 5000 500 90 2 30 true`
  - flower - 业务模块名称
  - a - 机器人的uuid和name前缀
  - 5000 - 机器人数量
  - 90 - 压测时间
  - 30 - 机器人模板类型
  - true - 是否自动加入公会（默认false不加入）
- 业务压测：`./robot_benchmark_logic.sh flower a 5000 500 300 2 30 false false`
  - 倒数第二个false - 是否自动加入公会（默认false不加入）
  - 倒数第一个false - 是否走创号发奖流程（默认true，执行发奖）
   
5. robot_analyse.sh

   作用：分析压测日志的脚本

   使用:  放到压测robot服~/game/bin下;./robot_analyse.sh

   参数：无

6. logic_analyse.sh

   作用：分析压测logic日志的脚本

   使用:  放到压测logic服~/game/bin下;./logic_analyse.sh或者./logic_analyse.sh 2021-06-02

   参数：

   - 参数1，可以不传，默认为当前日期。

7. battlke_analyse.sh
   作用: 分析压测服战斗压测后的战斗回合数和战报大小
   使用: 放到压测robot服~/game/bin下;./battle_analyse.sh
   参数: 无
