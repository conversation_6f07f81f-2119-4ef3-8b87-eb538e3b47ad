!/bin/bash

current_dir=`pwd`
goxml_config_file="../data/config_info.xml"
goxml_forest_config_info_file="../data/forest_config_info.xml"
goxml_forest_tree_info_file="../data/forest_tree_info.xml"
goxml_num_config_info_file="../data/number_type_info.xml"
goxml_gold_buy_info_file="../data/gold_buy_info.xml"
goxml_memory_chip_info_file="../data/memory_chip_info.xml"
goxml_function_info_file="../data/function_info.xml"
goxml_number_type_info_file="../data/number_type_info.xml"
goxml_tales_elite_info_file="../data/tales_elite_info.xml"
goxml_tales_info_file="../data/tales_info.xml"
goxml_server_file="../data/server.xml"
goxml_dispatch_info_file="../data/dispatch_info.xml"
goxml_maze_buy_revive_info_file="../data/maze_buy_revive_info.xml"
goxml_emblem_level_info_file="../data/emblem_level_info.xml"
goxml_artifact_strength_info_file="../data/artifact_strength_info.xml"
goxml_towerstar_config_info_file="../data/towerstar_config_info.xml"
goxml_towerstar_chapter_info_file="../data/towerstar_chapter_info.xml"
goxml_game_initial_info_file="../data/game_initial_info.xml"

echo "$goxml_game_initial_info_file.xml里的数据"
echo '<?xml version="1.0" encoding="UTF-8"?><root></root>' > $goxml_game_initial_info_file

echo "英雄"
sed -i 's/<\/root>/\n\t<data id=\"1\" type=\"9\" value=\"13980\" count=\"1\" \/><\/root>/g' $goxml_game_initial_info_file

echo "添加item"
sed -i 's/<\/root>/\n\t<data id=\"2\" type=\"4\" value=\"10001\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"3\" type=\"4\" value=\"10002\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"4\" type=\"4\" value=\"10003\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"5\" type=\"4\" value=\"10004\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"6\" type=\"4\" value=\"10005\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"7\" type=\"4\" value=\"10006\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"8\" type=\"4\" value=\"10007\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"9\" type=\"4\" value=\"10008\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"10\" type=\"4\" value=\"10009\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"11\" type=\"4\" value=\"10010\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"12\" type=\"4\" value=\"10011\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"13\" type=\"4\" value=\"10012\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"14\" type=\"4\" value=\"10013\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"15\" type=\"4\" value=\"10014\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"16\" type=\"4\" value=\"10015\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"17\" type=\"4\" value=\"10016\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"18\" type=\"4\" value=\"10017\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"19\" type=\"4\" value=\"10018\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"20\" type=\"4\" value=\"10019\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"21\" type=\"4\" value=\"10020\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"22\" type=\"4\" value=\"10021\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"23\" type=\"4\" value=\"10022\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"24\" type=\"4\" value=\"10023\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"25\" type=\"4\" value=\"10024\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"26\" type=\"4\" value=\"10025\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"27\" type=\"4\" value=\"10026\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"28\" type=\"4\" value=\"10027\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"29\" type=\"4\" value=\"10028\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"30\" type=\"4\" value=\"10029\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"31\" type=\"4\" value=\"10030\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"32\" type=\"4\" value=\"10031\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"33\" type=\"4\" value=\"10032\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"34\" type=\"4\" value=\"10038\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"35\" type=\"4\" value=\"10039\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"36\" type=\"4\" value=\"10051\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"37\" type=\"4\" value=\"10052\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"38\" type=\"4\" value=\"10053\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"39\" type=\"4\" value=\"10054\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"40\" type=\"4\" value=\"10055\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"41\" type=\"4\" value=\"100001\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"42\" type=\"4\" value=\"100002\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"43\" type=\"4\" value=\"100003\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"44\" type=\"4\" value=\"100004\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"45\" type=\"4\" value=\"100005\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"46\" type=\"4\" value=\"100006\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"47\" type=\"4\" value=\"80001\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"48\" type=\"4\" value=\"80002\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"49\" type=\"4\" value=\"80003\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"50\" type=\"4\" value=\"80004\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"51\" type=\"4\" value=\"80005\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"52\" type=\"4\" value=\"80006\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"53\" type=\"4\" value=\"80007\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"54\" type=\"4\" value=\"80008\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"55\" type=\"4\" value=\"80009\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"56\" type=\"4\" value=\"80010\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"57\" type=\"4\" value=\"80011\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"58\" type=\"4\" value=\"80012\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"59\" type=\"4\" value=\"80013\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"60\" type=\"4\" value=\"80014\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"61\" type=\"4\" value=\"80015\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"62\" type=\"4\" value=\"80016\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"63\" type=\"4\" value=\"81001\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"64\" type=\"4\" value=\"81002\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"65\" type=\"4\" value=\"80001\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"66\" type=\"4\" value=\"80001\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"67\" type=\"4\" value=\"80002\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"68\" type=\"4\" value=\"80003\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"69\" type=\"4\" value=\"80004\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"70\" type=\"4\" value=\"80005\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"71\" type=\"4\" value=\"80006\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"72\" type=\"4\" value=\"80007\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"73\" type=\"4\" value=\"80008\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"74\" type=\"4\" value=\"80009\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"75\" type=\"4\" value=\"80010\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"76\" type=\"4\" value=\"80011\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"77\" type=\"4\" value=\"80012\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"78\" type=\"4\" value=\"80013\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"79\" type=\"4\" value=\"80014\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"80\" type=\"4\" value=\"80015\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"81\" type=\"4\" value=\"10012\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"82\" type=\"9\" value=\"9028\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"83\" type=\"13\" value=\"571301\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"84\" type=\"13\" value=\"571302\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"85\" type=\"13\" value=\"571303\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"86\" type=\"13\" value=\"571401\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"87\" type=\"13\" value=\"571402\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"88\" type=\"13\" value=\"571403\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"89\" type=\"13\" value=\"571404\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"90\" type=\"13\" value=\"571405\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"91\" type=\"13\" value=\"571406\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"92\" type=\"13\" value=\"571501\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"93\" type=\"13\" value=\"571502\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"94\" type=\"13\" value=\"571503\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"95\" type=\"13\" value=\"571504\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"96\" type=\"13\" value=\"571505\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"97\" type=\"13\" value=\"571506\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"98\" type=\"13\" value=\"572501\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"99\" type=\"13\" value=\"571601\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"100\" type=\"13\" value=\"571602\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"101\" type=\"13\" value=\"571603\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file
sed -i 's/<\/root>/\n\t<data id=\"102\" type=\"13\" value=\"571604\" count=\"100000\" \/><\/root>/g' $goxml_game_initial_info_file

echo "修改神器limit level"
sed -i 's/limit_level=\"[0-9][0-9]*\"/limit_level=\"1\"/' $goxml_artifact_strength_info_file

echo "修改纹章limit level"
sed -i 's/limit_level=\"[0-9][0-9]*\"/limit_level=\"1\"/' $goxml_emblem_level_info_file

echo "修改好友领取点赞次数上限为100000"
sed -i 's/id=\"75\" config_value=\".*\"/id=\"75\" config_value=\"100000\"/g' $goxml_config_file

echo "修改好友点赞次数上限为100000"
sed -i 's/id=\"76\" config_value=\".*\"/id=\"76\" config_value=\"100000\"/g' $goxml_config_file

echo "修改密林搜索CD为1秒"
sed -i 's/key=\"SEARCH_CD\" type=\"0\" value=\"0\" count=\".*\"/key=\"SEARCH_CD\" type=\"0\" value=\"0\" count=\"1\"/' $goxml_forest_config_info_file

echo "修改密林加速生长的消耗为1钻石"
sed -i 's/key=\"SPEED_COST\" type=\"2\" value=\"0\" count=\".*\"/key=\"SPEED_COST\" type=\"2\" value=\"0\" count=\"1\"/' $goxml_forest_config_info_file

echo "修改每日免费加速领取挂机奖励次数为60次"
sed -i 's/type=\"1\" free_num=\"[0-9]\"/type=\"1\" free_num=\"60\"/' $goxml_num_config_info_file

echo "修改每日爬塔免费扫荡次数为120次"
sed -i 's/type=\"3\" free_num=\"[0-9]\"/type=\"3\" free_num=\"120\"/' $goxml_num_config_info_file

echo "修改点金宝箱每日购买次数为50000次"
sed -i 's/id=\"1\" free_limit=\"[0-9]\"/id=\"1\" free_limit=\"50000\"/' $goxml_gold_buy_info_file
sed -i 's/id=\"2\" free_limit=\"0\" buy_limit=\"[0-9]\"/id=\"2\" free_limit=\"0\" buy_limit=\"50000\"/' $goxml_gold_buy_info_file
sed -i 's/id=\"3\" free_limit=\"0\" buy_limit=\"[0-9]\"/id=\"3\" free_limit=\"0\" buy_limit=\"50000\"/' $goxml_gold_buy_info_file

echo "修改每日列传强敌挑战次数"
sed -i 's/type=\"17\" free_num=\".\"/type=\"17\" free_num=\"1000\"/g' $goxml_number_type_info_file
sed -i 's/num=\".\"/num=\"100\"/g' $goxml_tales_elite_info_file
sed -i 's/dungeon=\"[1-9][0-9]*\"/dungeon=\"0\"/g' $goxml_tales_info_file

echo "修改悬赏任务解锁等级为1级"
sed -i 's/lv=\"[1-9][0-9]*\"/lv=\"1\"/g' $goxml_function_info_file

echo "修改悬赏任务资源消耗数量"
sed -i 's/cost_count=\"[1-9][0-9]*\"/cost_count=\"1\"/g' $goxml_dispatch_info_file

echo "修改悬赏任务英雄数量、英雄星数、英雄种族"
sed -i 's/hero_count=\"[0-9]\" star_grade=\"[0-9]\" race1=\"[0-9]\" race2=\"[0-9]\" race3=\"[0-9]\"/hero_count=\"1\" star_grade=\"1\" race1=\"0\" race2=\"0\" race3=\"0\"/g' $goxml_dispatch_info_file

echo "开启server.xml GM"
sed -i 's/<gm>0/<gm>1/g' $goxml_server_file

echo "清空maze_buy_revive_info.xml里的数据"
echo '<?xml version="1.0" encoding="UTF-8"?><root></root>' > $goxml_maze_buy_revive_info_file

echo "迷宫-购买复生神像"
sed -i 's/<\/root>/\n\t<data num=\"1\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"2\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"3\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"4\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"5\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"6\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"7\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"8\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"9\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"10\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"11\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"12\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"13\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"14\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"15\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"16\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"17\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"18\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"19\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"20\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"21\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"22\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"23\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"24\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"25\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"26\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"27\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"28\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"29\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"30\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"31\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"32\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"33\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"34\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"35\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"36\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"37\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"38\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"39\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"40\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"41\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"42\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"43\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"44\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"45\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"46\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"47\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"48\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"49\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file
sed -i 's/<\/root>/\n\t<data num=\"50\" type=\"3\" value=\"0\" count=\"1\" \/>\n<\/root>/g' $goxml_maze_buy_revive_info_file

echo "条件爬塔"
sed -i 's/\"2\"/\"99\"/g' $goxml_towerstar_config_info_file
sed -i 's/type=\"19\" free_num=\".\"/type=\"19\" free_num=\"100\"/g' $goxml_number_type_info_file
sed -i 's/unlock_level=\"[0-9][0-9]*\"/unlock_level=\"1\"/' $goxml_towerstar_chapter_info_file

echo "开始关闭服务器"
cd ../bin
./cmd.sh stop service -name=logic -http=:10001 -grpc=:20001 -log_level=INFO

./logic_clear_log.sh

echo "开始重启服务器"
cd ../bin
./cmd.sh start service -name=logic -http=:10001 -grpc=:20001 -log_level=INFO

