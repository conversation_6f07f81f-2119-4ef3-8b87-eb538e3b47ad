#!/bin/bash

#logic_msg_left=`cat ../log/logic.log*|grep "msgs count per second"|awk -F ":" '{
#	if($2+0>0 || $4+0>0 || $6+0>0) {print $0;}
#}'`



getDate=$1
if [ ! "$getDate" ] ;then
  getDate=`date "+%Y-%m-%d"` 
fi

echo -e "\n--------------------------\n"
echo "所有超时的left"
logic_msg_left=`cat ../log/logic.log*|grep "msgs count per second"|awk '/left:[1-9][0-9]{3,}/'|awk '
	{
		for (i=1;i<=NF;i++) {
			if (i==1 || i==2 || i>=14) {
				printf("%s ", $i);
			}
		}
	
	}'` 


OLD_IFS=$IFS
IFS=$'\n'
for index in ${logic_msg_left[@]}
do
  echo ${index}
done
IFS=$OLD_IFS

echo -e "\n--------------------------\n"
logic_start_use_time=`cat ../log/logic.log*|grep "running, consume"|awk '{print $NF}'`
echo "logic服务器启动时间:$logic_start_use_time"

#account_size=`cat ../log/logic.log*|grep "load.go"|grep "account size"|awk '{print $NF}'`
#echo "logic账号总数:$account_size"

echo -e "\n--------------------------\n"
echo "协议处理超时分布"
logic_execute_time=`cat ../log/logic.log* |grep "execute time:" |awk  '
        BEGIN{
                delay_info[1]="0,10";
                delay_info[2]="10,20";
                delay_info[3]="20,50";
                delay_info[4]="50,100";
                delay_info[5]="100,200";
                delay_info[6]="200,500";
		delay_info[7]="500,10000";
	}
	 {cmd=$(NF-3);total++;x[cmd]++;}
         {for (i=1;i<=7;i++) 
           {
                j=delay_info[i];
                split(j,jarr,",");
                if($NF+0>=jarr[1]+0 && $NF+0 < jarr[2]+0){px[cmd,j]++;}
           }
         } END {
         {printf("%-48s", "     协议名称")}
        {for (i=1;i<=7;i++) 
          {
                j=delay_info[i];
                split(j,jarr,",");
                printf ("%4s-%s ", jarr[1],jarr[2])
          }
        }
        {printf("\n")}
        {for (i in x) 
                {
                        printf("协议:%12s(%4d)%30s", i, x[i]," ");
                        { for(j=1;j<=7;j++)
                                 {
                                        k=delay_info[j];
                                        printf(" %5.2f% ",px[i,k]/x[i]*100);
                                }
                        } 
                        {printf("\n");}
                 }
        }}'`

OLD_IFS=$IFS
IFS=$'\n'
for index in ${logic_execute_time[@]}
do
  echo ${index}
done
IFS=$OLD_IFS

#echo "获取online折线图csv"
#cat ../log/logic.log* |grep onlines |grep caches|awk '{print $2"."$10}'|awk -F "." '{print $1":"$3}'|awk -F"," '{print $1}'|awk -F ":" '{print $1":"$2":"$3","$4}' > ../log/logic.log.onlie.csv
#echo "获取cache折线图csv"
#cat ../log/logic.log* |grep onlines |grep caches|awk '{print $2"."$12}'|awk -F "." '{print $1":"$3}'|awk -F ":" '{print $1":"$2":"$3","$4}' > ../log/logic.log.cache.csv


