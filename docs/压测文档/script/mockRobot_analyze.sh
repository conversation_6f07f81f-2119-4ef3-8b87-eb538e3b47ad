#!/bin/bash

current=`date "+%Y-%m-%d %H:%M:%S"`
echo "$current 压测数据分析"
logs="../log/mockRobot.log*"
success_count=`cat ${logs}|grep "AddSuc"|wc -l`
robot_start_time=`cat ${logs}|grep "TotalNum"|awk -F "." '{print $1}'|awk -F "[" '{print $2}'`
robot_end_time=`cat ${logs}|grep "AddSuc"|awk -F "." '{print $1}'|awk -F "[" '{print $2}'|sort|tail -1`
robot_start_time_int=`date -d "$robot_start_time" +%s`
robot_end_time_int=`date -d "$robot_end_time" +%s`
robot_use_time=$[$robot_end_time_int-$robot_start_time_int]
offline_count=`cat ${logs}|grep "offline"|wc -l`
offline_robot_count=`cat ${logs} | grep offline | awk -F " " '{print $5}' |sort|uniq|wc -l`



echo "压测基础情况"
echo "开始时间：$robot_start_time, 结束时间:$robot_end_time, 耗时:$robot_use_time(s)"
echo "成功总数：${success_count}"
echo "触发离线机器人总数: ${offline_robot_count}  触发离线次数: ${offline_count}"

echo -e "\n--------------------------\n"
echo "协议延迟分布"
robot_cmd_delay_info=`cat ${logs} |grep delay |awk '{print $8":"$10}'|awk -F ":" '
	BEGIN{
		delay_info[1]="0,10";
		delay_info[2]="10,20";
		delay_info[3]="20,50";
		delay_info[4]="50,100";
		delay_info[5]="100,200";
		delay_info[6]="200,500";
		delay_info[7]="500,1000";
		delay_info[8]="1000,10000";
		delay_info[9]="10000,100000";
		delay_count=9;
	} 
	{cmd=$2;total++;x[cmd]++;div=1000000;}
	 {for (i=1;i<=delay_count;i++) 
	   {
		j=delay_info[i];
		split(j,jarr,",");
		if($4/div+0>=jarr[1]+0 && $4/div+0 < jarr[2]+0){px[cmd,j]++;}
           }
         } END {
	 {printf("%-10s\t", "协议名称")}
	 {printf("%-10s\t", "协议数量")}
        {for (i=1;i<=delay_count;i++) 
          {
 		j=delay_info[i];
		split(j,jarr,",");
		printf ("%5s-%-5s\t", jarr[1],jarr[2])
	  }
	}
	{printf("\n")}
	{for (i in x) 
		{
			printf("%-10s\t", i)
			printf("%-10d\t", x[i]);
			{ for(j=1;j<=delay_count;j++)
				 {
					k=delay_info[j];
					printf("%9.2f%-1s\t",px[i,k]/x[i]*100, "%");
				}
			} 
			{printf("\n");}
		 }
	}}'` 


OLD_IFS=$IFS
IFS=$'\n'
for index in ${robot_cmd_delay_info[@]}
do
  echo ${index}
done
IFS=$OLD_IFS

echo -e "\n---------------------\n"

echo "获取延迟折线图csv"
#cat ${logs} |grep delay|awk '{print $2"."$10}'|awk -F "." '{print $1":"$3}'|awk -F ":" '{print $1":"$2":"$3","$5/1000000}'> ../log/mockRobot.log.csv

