#!/bin/bash

#robot1 100 200 300
#账号格式 登录人数，每秒人数，运行时间(s)
./robot_clear_log.sh
send_resource=$5
stage=$6
if [ ! "$send_resource" ] ;then
  send_resource=false
fi
if [ ! "$stage" ] ;then
  stage=1
fi
echo "send_resource"
echo $send_resource
date
./robot -node=1010600001  -uuid=$1 -num=$2 -tnum=$3 -slice=10 -send_resource=$send_resource -mode=2 -stage=$stage -gateway=10.6.6.14:38422 -log_level=INFO &> ../log/robot_error.log &

uid=`ps -ef | grep robot | grep 1010600001 | grep -v grep | grep -v $0 | awk '{print $2}'`
sleep $4 && kill -9 $uid
