#!/bin/bash
robot_total_round=`cat ../log/robot.log*|grep "roundNum"|awk -F " " '{print $6}'|awk -F ":" '{sum += $2};END {print sum}'`
robot_total_size=`cat ../log/robot.log*|grep "roundNum"|awk -F " " '{print $7}'|awk -F ":" '{sum += $2};END {print sum}'`
robot_msg_count=`cat ../log/robot.log*|grep "roundNum"|wc -l`
robot_per_round=`echo "scale=2;a = $robot_total_round/$robot_msg_count;if (length(a)==scale(a)) print 0;print a" | bc ; echo`
robot_per_size=$[$robot_total_size/$robot_msg_count]

echo "战斗消息总数:$robot_msg_count"
echo "总回合数:$robot_total_round"
echo "平均回合数:$robot_per_round"
echo "总战报大小:$robot_total_size"
echo "平均战报大小:$robot_per_size"
