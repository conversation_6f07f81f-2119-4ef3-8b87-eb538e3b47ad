syntax = "proto3";

option go_package = "app/protos/out/cl";
import "bt.proto";
package cl;

// 每个功能预留100个ID
enum ID {
  MSG_NONE = 0;
  MSG_MIN = 10000;
  MSG_MAX = 100000;
  MSG_BEGIN = 11000;  // 当前文件中使用的ID段起始值
  MSG_END = 99999;    // 当前文件中使用的ID段截止值

  // 基础机制
  MSG_MIN_Base = 11001;
  MSG_C2L_Flush = 11003;  // 推送数据 NOLOG
  MSG_L2C_Flush = 11004;
  MSG_L2C_GetUser = 11005;
  MSG_C2L_KeepAlive = 11006;  // 心跳 NOLOG
  MSG_L2C_KeepAlive = 11007;
  MSG_L2C_OpResources = 11012;
  MSG_L2C_GetBags = 11013;
  MSG_L2C_BattleReport = 11014;
  MSG_C2L_AddPurchaseNum = 11021;  // 通用次数添加
  MSG_L2C_AddPurchaseNum = 11022;
  MSG_C2L_GetCommonRank = 11023;  // 获取通用排行榜 NOLOG
  MSG_L2C_GetCommonRank = 11024;
  MSG_C2L_GM = 11025;  // GM命令 NOLOG
  MSG_L2C_GM = 11026;
  MSG_C2L_ViewUser = 11027;  // 查看玩家信息 NOLOG
  MSG_L2C_ViewUser = 11028;
  MSG_C2L_Formation = 11029;  // 布阵
  MSG_L2C_Formation = 11030;
  MSG_C2L_GetFormation = 11031;  // 所有布阵信息 NOLOG
  MSG_L2C_GetFormation = 11032;
  MSG_C2L_BattleTest = 11033;  // 战斗测试协议 NOLOG
  MSG_L2C_BattleTest = 11034;
  MSG_C2L_GetUserBattleData = 11035;  // 获取玩家战斗数据 NOLOG
  MSG_L2C_GetUserBattleData = 11036;
  MSG_C2L_GetBattleReport = 11037;  // 查看战报 NOLOG
  MSG_L2C_GetBattleReport = 11038;
  MSG_C2L_SetServerTime = 11039;  // 设置服务器时间
  MSG_L2C_SetServerTime = 11040;
  MSG_L2C_OpNum = 11041;           // 次数信息变化
  MSG_L2C_OpGlobalAttr = 11044;    // 全局属性变化
  MSG_C2L_TestBattleData = 11045;  // 战斗测试协议 NOLOG
  MSG_L2C_TestBattleData = 11046;
  MSG_C2L_GlobalAttrGet = 11047;  // 获取全局属性 NOLOG
  MSG_L2C_GlobalAttrGet = 11048;
  MSG_C2L_SetName = 11049;  // 修改昵称 NOLOG //日志记录入口在redis/command.go中，已确认记录。现有逻辑无法检出，避免提示错误，故设为免检
  MSG_L2C_SetName = 11050;
  MSG_C2L_RobotBattle = 11053;  // NOLOG
  MSG_L2C_RobotBattle = 11054;
  MSG_C2L_FlushRedPoint = 11055;  // 红点信息 NOLOG
  MSG_L2C_FlushRedPoint = 11056;
  MSG_L2C_NewMailTip = 11057;         // 新邮件提示 - 红点
  MSG_C2L_SyncQuestionnaire = 11058;  // 同步问卷 NOLOG
  MSG_L2C_SyncQuestionnaire = 11059;
  MSG_C2L_Duel = 11060;  // 切磋
  MSG_L2C_Duel = 11061;
  MSG_C2L_Accusation = 11062;  // 举报
  MSG_L2C_Accusation = 11063;
  MSG_C2L_GetClientInfo = 11066;  // 拉取客户端信息 NOLOG
  MSG_L2C_GetClientInfo = 11067;
  MSG_C2L_SetClientInfo = 11068;  // 设置客户端信息 NOLOG
  MSG_L2C_SetClientInfo = 11069;
  MSG_C2L_GetUserSnapshots = 11070;  // 获取玩家快照列表 NOLOG
  MSG_L2C_GetUserSnapshots = 11071;
  MSG_C2L_RecvShareAward = 11072;  // 分享领奖
  MSG_L2C_RecvShareAward = 11073;
  MSG_C2L_GetGiftCodeAward = 11074;  // 获取礼包码奖励 NOLOG
  MSG_L2C_GetGiftCodeAward = 11075;
  MSG_C2L_ClientSetMultiLang = 11076;  // 客户端设置语言 NOLOG
  MSG_L2C_ClientSetMultiLang = 11077;
  MSG_C2L_ClientGetMultiLang = 11078;  // 客户端获取设置的语言 NOLOG
  MSG_L2C_ClientGetMultiLang = 11079;
  MSG_L2C_NotifyBanCmd = 11080;        // 封禁协议通知
  MSG_C2L_GetCommonRankFirst = 11081;  // 获取通用排行榜首页数据 NOLOG
  MSG_L2C_GetCommonRankFirst = 11082;
  MSG_C2L_UpdateTop5 = 11083;  // 更新top5 NOLOG
  MSG_L2C_UpdateTop5 = 11084;
  MSG_C2L_ViewFormation = 11085;  // 查看玩家战斗数据 NOLOG
  MSG_L2C_ViewFormation = 11086;
  MSG_C2L_GlobalAttrScoreGet = 11087;  // 查看全局属性评分  NOLOG
  MSG_L2C_GlobalAttrScoreGet = 11088;
  MSG_C2L_GetCrossRankFirst = 11089;  // 获取跨服排行榜首页数据 NOLOG
  MSG_L2C_GetCrossRankFirst = 11090;
  MSG_L2C_QuestionnaireUpdate = 11091;  // 问卷更新推送
  MSG_C2L_OssBattleReport = 11093;      // oss战报测试协议 NOLOG
  MSG_L2C_OssBattleReport = 11094;      // oss战报信息
  MSG_C2L_OSSUrl = 11095;               // 获取战报url NOLOG
  MSG_L2C_OSSUrl = 11096;
  MSG_C2L_GetCognitionLog = 11097;  // 获取战斗认知日志  NOLOG
  MSG_L2C_GetCognitionLog = 11098;

  // 道具
  MSG_MIN_Item = 11100;
  MSG_C2L_UseItem = 11101;  // 使用道具 NOLOG
  MSG_L2C_UseItem = 11102;
  MSG_C2L_SellItem = 11103;  // 道具出售 NOLOG
  MSG_L2C_SellItem = 11104;
  MSG_C2L_ItemSelect = 11105;  // 兑换自选礼包
  MSG_L2C_ItemSelect = 11106;

  // 邮件
  MSG_MIN_Mail = 11200;
  MSG_C2L_GetMails = 11201;  // 获取邮件 NOLOG
  MSG_L2C_GetMails = 11202;
  MSG_C2L_ReadMail = 11203;  // 查看邮件
  MSG_L2C_ReadMail = 11204;
  MSG_C2L_DrawMails = 11205;  // 邮件领奖
  MSG_L2C_DrawMails = 11206;
  MSG_C2L_DeleteMails = 11207;  // 删除邮件
  MSG_L2C_DeleteMails = 11208;

  // 商店
  MSG_MIN_Shop = 11300;
  MSG_C2L_ShopList = 11301;  // 获取商店信息 NOLOG
  MSG_L2C_ShopList = 11302;
  MSG_C2L_ShopBuy = 11303;  // 购买商品
  MSG_L2C_ShopBuy = 11304;
  MSG_C2L_ShopRefresh = 11305;  // 刷新商店(随机商店)
  MSG_L2C_ShopRefresh = 11306;
  MSG_C2L_ShopReset = 11307;  // 重置商店
  MSG_L2C_ShopReset = 11308;

  // 好友
  MSG_MIN_Friend = 11500;
  MSG_C2L_FriendInfo = 11501;  // 获取好友信息 NOLOG
  MSG_L2C_FriendInfo = 11502;
  MSG_C2L_FriendAdd = 11503;  // 添加好友请求
  MSG_L2C_FriendAdd = 11504;
  MSG_L2C_FriendNotify = 11505;   // 新动态通知被点赞好友申请
  MSG_C2L_FriendConfirm = 11506;  // 请求确认
  MSG_L2C_FriendConfirm = 11507;
  MSG_C2L_FriendDelete = 11508;  // 删除好友
  MSG_L2C_FriendDelete = 11509;
  MSG_C2L_FriendBlacklist = 11510;  // 拉黑
  MSG_L2C_FriendBlacklist = 11511;
  MSG_C2L_FriendRemBlacklist = 11512;  // 移除黑名单 NOLOG
  MSG_L2C_FriendRemBlacklist = 11513;
  MSG_C2L_FriendSendLike = 11514;  // 点赞
  MSG_L2C_FriendSendLike = 11515;
  MSG_C2L_FriendRecvLike = 11516;  // 接受点赞
  MSG_L2C_FriendRecvLike = 11517;
  MSG_C2L_FriendRecommend = 11518;  // 推荐好友 NOLOG
  MSG_L2C_FriendRecommend = 11519;
  MSG_C2L_FriendSearch = 11520;  // 查询玩家 NOLOG
  MSG_L2C_FriendSearch = 11521;
  MSG_C2L_FriendRequestInfo = 11522;  // 好友申请信息 NOLOG
  MSG_L2C_FriendRequestInfo = 11523;
  MSG_C2L_FriendGetBlacklist = 11524;  // 获取黑名单列表 NOLOG
  MSG_L2C_FriendGetBlacklist = 11525;

  // 聊天
  MSG_MIN_Chat = 11600;
  // MSG_C2L_Chat = 11601;  // 聊天
  // MSG_L2C_Chat = 11602;
  // MSG_C2L_ChatGetMessages = 11603;  // 上线获取聊天消息 NOLOG
  // MSG_L2C_ChatGetMessages = 11604;
  // MSG_L2C_ChatNotify = 11607;                // 新消息通知
  // MSG_C2L_ChatGetPrivateMessageNum = 11608;  // 获取私聊离线消息数量 NOLOG
  // MSG_L2C_ChatGetPrivateMessageNum = 11609;
  // MSG_C2L_ChatLike = 11610;  // 聊天消息点赞
  // MSG_L2C_ChatLike = 11611;
  // MSG_C2L_ChatGetLikeList = 11612;  // 获取点赞列表 NOLOG
  // MSG_L2C_ChatGetLikeList = 11613;
  // MSG_L2C_ChatLikeNotify = 11614;  // 点赞更新通知
  MSG_C2L_ChatGetToken = 11615;  // 获取点赞列表 NOLOG
  MSG_L2C_ChatGetToken = 11616;
  MSG_L2C_ChatGroupUpdateNotify = 11617;  // 聊天 group 变化
  MSG_C2L_ChatSyncChatTag = 11618;        // 同步聊天群组 NOLOG
  MSG_L2C_ChatSyncChatTag = 11619;
  MSG_C2L_ChatCostShareCount = 11620;  // 扣除聊天分享次数 NOLOG
  MSG_L2C_ChatCostShareCount = 11621;

  // 主线
  MSG_MIN_Dungeon = 11800;
  MSG_C2L_DungeonFight = 11801;  // 战斗
  MSG_L2C_DungeonFight = 11802;
  MSG_C2L_DungeonRecvAward = 11803;  // 领取挂机奖励
  MSG_L2C_DungeonRecvAward = 11804;
  MSG_C2L_DungeonSpeedRecvAward = 11805;  // 加速领取挂机奖励
  MSG_L2C_DungeonSpeedRecvAward = 11806;
  MSG_C2L_DungeonPreview = 11807;  // 挂机预览 NOLOG
  MSG_L2C_DungeonPreview = 11808;
  MSG_C2L_Dungeon = 11809;  // 获取主线信息 NOLOG
  MSG_L2C_Dungeon = 11810;

  // 英雄
  MSG_MIN_Hero = 11900;
  MSG_C2L_HeroList = 11901;  // 英雄列表 NOLOG
  MSG_L2C_HeroList = 11902;
  MSG_C2L_HeroLevelUp = 11903;  // 升级
  MSG_L2C_HeroLevelUp = 11904;
  MSG_C2L_HeroStageUp = 11905;  // 突破
  MSG_L2C_HeroStageUp = 11906;
  MSG_C2L_HeroStarUp = 11907;  // 升星
  MSG_L2C_HeroStarUp = 11908;
  MSG_C2L_HeroBuySlot = 11909;  // 购买英雄栏位
  MSG_L2C_HeroBuySlot = 11910;
  MSG_C2L_HeroUpdateLockStatus = 11911;  // 锁定
  MSG_L2C_HeroUpdateLockStatus = 11912;
  MSG_C2L_HeroDecompose = 11913;  // 分解
  MSG_L2C_HeroDecompose = 11914;
  MSG_C2L_HeroTestAttr = 11915;  // 测试接口，前端用来比较英雄属性和战力 NOLOG
  MSG_L2C_HeroTestAttr = 11916;
  MSG_L2C_HeroesUpdate = 11918;
  MSG_C2L_HeroBack = 11919;  // 回退
  MSG_L2C_HeroBack = 11920;
  MSG_C2L_HeroRevive = 11921;  // 重生
  MSG_L2C_HeroRevive = 11922;
  MSG_L2C_HeroUpdateStarLimit = 11927;  // 更新升星限制等级
  MSG_C2L_HeroGetStarUpCosts = 11928;   // 获取升星消耗数据 NOLOG
  MSG_L2C_HeroGetStarUpCosts = 11929;
  MSG_C2L_HeroTestCalPower = 11930;  // 测试接口，机器人用来测试计算英雄战力的性能 NOLOG
  MSG_L2C_HeroTestCalPower = 11931;
  MSG_C2L_HeroConversion = 11932;  // 英雄兑换
  MSG_L2C_HeroConversion = 11933;
  MSG_C2L_HeroGemLevelUp = 11934;  // 英雄宝石升级
  MSG_L2C_HeroGemLevelUp = 11935;
  MSG_C2L_HeroExchange = 11936;  // 英雄转换
  MSG_L2C_HeroExchange = 11937;
  MSG_C2L_HeroAwaken = 11938;  // 英雄觉醒
  MSG_L2C_HeroAwaken = 11939;
  MSG_C2L_HeroConvert = 11940;  // 溢出英雄转换觉醒道具
  MSG_L2C_HeroConvert = 11941;
  MSG_C2L_HeroStarUpRedList = 11942;  // 英雄升星吞噬红卡列表
  MSG_L2C_HeroStarUpRedList = 11943;
  MSG_C2L_HeroTagUpdate = 11944;  // 交换英雄tag类型
  MSG_L2C_HeroTagUpdate = 11945;
  MSG_C2L_HeroConvertAwakenItem = 11946;  // 英雄觉醒材料转换
  MSG_L2C_HeroConvertAwakenItem = 11947;
  MSG_C2L_HeroRareUp = 11948;  // 升星
  MSG_L2C_HeroRareUp = 11949;

  // 召唤
  MSG_MIN_Summon = 12000;
  MSG_C2L_Summon = 12001;  // 召唤
  MSG_L2C_Summon = 12002;
  MSG_C2L_SummonGetData = 12003;  // 获取召唤信息 NOLOG
  MSG_L2C_SummonGetData = 12004;
  MSG_C2L_SummonSetHeroAutoDecompose = 12005;  // 设置召唤是否自动分解  NOLOG
  MSG_L2C_SummonSetHeroAutoDecompose = 12006;
  MSG_C2L_SummonSetWishList = 12007;  // 设置心愿单 NOLOG
  MSG_L2C_SummonSetWishList = 12008;
  MSG_C2L_SummonArtifactPointsExchange = 12009;  // 神器积分兑换 NOLOG
  MSG_L2C_SummonArtifactPointsExchange = 12010;
  MSG_C2L_SummonSimulation = 12011;  // NOLOG
  MSG_L2C_SummonSimulation = 12012;

  // 任务系统
  MSG_MIN_Task = 12100;
  MSG_C2L_TaskGetInfo = 12101;  // 获取任务信息 NOLOG
  MSG_L2C_TaskGetInfo = 12102;
  MSG_C2L_TaskReceiveAward = 12103;  // 领取任务奖励
  MSG_L2C_TaskReceiveAward = 12104;
  MSG_L2C_TaskUpdate = 12105;
  MSG_C2L_TaskAdsWatch = 12106;
  MSG_L2C_TaskAdsWatch = 12107;

  // 碎片
  MSG_MIN_Fragment = 12200;
  MSG_C2L_FragmentCompose = 12201;  // 碎片合成
  MSG_L2C_FragmentCompose = 12202;
  MSG_C2L_FragmentEmblemCompose = 12203;  // 纹章碎片
  MSG_L2C_FragmentEmblemCompose = 12204;
  MSG_C2L_FragmentComposeAll = 12205;  // 碎片一键合成
  MSG_L2C_FragmentComposeAll = 12206;
  MSG_C2L_FragmentComposeTest = 12207;  // 碎片合成测试
  MSG_L2C_FragmentComposeTest = 12208;

  // 装备equip
  MSG_MIN_Equip = 12300;
  MSG_C2L_EquipGet = 12301;  // 获取装备信息 NOLOG
  MSG_L2C_EquipGet = 12302;
  MSG_C2L_EquipWear = 12303;  // 装备穿戴
  MSG_L2C_EquipWear = 12304;
  MSG_C2L_EquipStrength = 12305;  // 装备强化
  MSG_L2C_EquipStrength = 12306;
  MSG_C2L_EquipRefine = 12307;  // 装备精炼
  MSG_L2C_EquipRefine = 12308;
  MSG_C2L_EquipEnchant = 12309;  // 装备附魔
  MSG_L2C_EquipEnchant = 12310;
  MSG_C2L_EquipEvolution = 12311;  // 装备进阶
  MSG_L2C_EquipEvolution = 12312;
  MSG_C2L_EquipDecompose = 12313;  // 装备分解
  MSG_L2C_EquipDecompose = 12314;
  MSG_C2L_EquipRevive = 12315;  // 装备重生
  MSG_L2C_EquipRevive = 12316;
  MSG_L2C_EquipUpdate = 12317;            // 推送变化了的装备
  MSG_C2L_EquipMultipleStrength = 12318;  // 装备一键强化 NOLOG
  MSG_L2C_EquipMultipleStrength = 12319;
  MSG_C2L_EquipGrowTransfer = 12320;  // 装备养成转移
  MSG_L2C_EquipGrowTransfer = 12321;
  MSG_C2L_EquipSetAutoDecompose = 12322;  // 设置装备自动分解
  MSG_L2C_EquipSetAutoDecompose = 12323;

  // 竞技场
  MSG_MIN_Arena = 12400;
  MSG_C2L_ArenaInfo = 12401;  // 竞技场信息 NOLOG
  MSG_L2C_ArenaInfo = 12402;
  MSG_C2L_ArenaRefresh = 12403;  // 刷新对手
  MSG_L2C_ArenaRefresh = 12404;
  MSG_C2L_ArenaFight = 12405;  // 战斗
  MSG_L2C_ArenaFight = 12406;
  MSG_C2L_ArenaLogList = 12407;  // 战报信息 NOLOG
  MSG_L2C_ArenaLogList = 12408;
  MSG_C2L_ArenaLike = 12409;  // 竞技场点赞
  MSG_L2C_ArenaLike = 12410;
  MSG_C2L_ArenaRank = 12411;  // 竞技场排行榜 NOLOG
  MSG_L2C_ArenaRank = 12412;
  MSG_C2L_ArenaRecvAward = 12413;  // 领取任务奖励
  MSG_L2C_ArenaRecvAward = 12414;

  // 爬塔
  MSG_MIN_Tower = 12500;
  MSG_C2L_TowerList = 12501;  // 塔的信息 NOLOG
  MSG_L2C_TowerList = 12502;
  MSG_C2L_TowerFight = 12503;  // 战斗
  MSG_L2C_TowerFight = 12504;
  MSG_C2L_TowerSweep = 12505;  // 扫荡 NOLOG 日志已与TowerFight合并
  MSG_L2C_TowerSweep = 12506;
  MSG_C2L_TowerJump = 12507;  // 跳关 NOLOG 日志已与TowerFight合并
  MSG_L2C_TowerJump = 12508;

  // 神器
  MSG_MIN_Artifact = 12600;
  MSG_C2L_ArtifactList = 12601;  // 神器列表 NOLOG
  MSG_L2C_ArtifactList = 12602;
  MSG_C2L_ArtifactActivate = 12603;  // 神器激活
  MSG_L2C_ArtifactActivate = 12604;
  MSG_C2L_ArtifactStarUp = 12605;  // 神器升星
  MSG_L2C_ArtifactStarUp = 12606;
  MSG_C2L_ArtifactStrength = 12607;  // 神器强化
  MSG_L2C_ArtifactStrength = 12608;
  MSG_C2L_ArtifactForge = 12609;  // 神器铸造
  MSG_L2C_ArtifactForge = 12610;
  MSG_C2L_ArtifactRevive = 12611;  // 神器重生
  MSG_L2C_ArtifactRevive = 12612;

  // 宝石
  MSG_MIN_Gem = 12700;
  MSG_C2L_GetGems = 12701;  // 获取宝石列表 NOLOG
  MSG_L2C_GetGems = 12702;
  MSG_C2L_GemWear = 12703;  // 宝石穿戴替换卸下
  MSG_L2C_GemWear = 12704;
  MSG_C2L_GemCompose = 12705;  // 宝石合成
  MSG_L2C_GemCompose = 12706;
  MSG_C2L_GemDecompose = 12707;  // 宝石分解
  MSG_L2C_GemDecompose = 12708;
  MSG_C2L_GemConvert = 12709;  // 宝石置换
  MSG_L2C_GemConvert = 12710;
  MSG_C2L_UpdateGems = 12711;  // 更新宝石
  MSG_L2C_UpdateGems = 12712;

  // 材料本
  MSG_MIN_Trial = 12800;
  MSG_C2L_TrialGetInfo = 12801;  // 获取材料本信息 NOLOG
  MSG_L2C_TrialGetInfo = 12802;
  MSG_C2L_TrialFight = 12803;  // 材料本战斗
  MSG_L2C_TrialFight = 12804;
  MSG_C2L_TrialPreview = 12807;  // 预览奖励
  MSG_L2C_TrialPreview = 12808;
  MSG_C2L_TrialAward = 12809;  // 领取奖励
  MSG_L2C_TrialAward = 12810;
  MSG_C2L_TrialSpeed = 12811;  // 加速器挂机
  MSG_L2C_TrialSpeed = 12812;

  // 悬赏
  MSG_MIN_Dispatch = 12900;
  MSG_C2L_DispatchTasks = 12901;  // 获取悬赏任务列表 NOLOG
  MSG_L2C_DispatchTasks = 12902;
  MSG_C2L_DispatchReceiveTask = 12903;  // 接取悬赏任务
  MSG_L2C_DispatchReceiveTask = 12904;
  MSG_C2L_DispatchGetAwards = 12905;  // 获取悬赏任务奖励
  MSG_L2C_DispatchGetAwards = 12906;
  MSG_C2L_DispatchRefreshTask = 12907;  // 刷新悬赏任务
  MSG_L2C_DispatchRefreshTask = 12908;
  MSG_L2C_DispatchLevelUpdate = 12909;  // 悬赏升级下发的新协议

  // 头像边框
  MSG_MIN_Avatar = 13000;
  MSG_C2L_AvatarGetInfo = 13001;  // 获取所有头像信息 NOLOG
  MSG_L2C_AvatarGetInfo = 13002;
  MSG_C2L_AvatarSetIcon = 13003;  // 修改头像 NOLOG
  MSG_L2C_AvatarSetIcon = 13004;
  MSG_L2C_AvatarNew = 13005;  // 激活新头像

  // 纹章
  MSG_MIN_Emblem = 13100;
  MSG_C2L_EmblemGet = 13101;  // 获取纹章列表 NOLOG
  MSG_L2C_EmblemGet = 13102;
  MSG_C2L_EmblemWear = 13103;  // 纹章穿戴替换卸下
  MSG_L2C_EmblemWear = 13104;
  MSG_C2L_EmblemLevelUp = 13105;  // 纹章升级
  MSG_L2C_EmblemLevelUp = 13106;
  MSG_C2L_EmblemDecompose = 13107;  // 纹章分解
  MSG_L2C_EmblemDecompose = 13108;
  MSG_L2C_EmblemUpdate = 13109;        // 推送变化了的纹章
  MSG_C2L_EmblemGrowTransfer = 13110;  // 纹章养成转移
  MSG_L2C_EmblemGrowTransfer = 13111;
  MSG_C2L_EmblemSetAutoDecompose = 13112;  // 设置装备自动分解
  MSG_L2C_EmblemSetAutoDecompose = 13113;
  MSG_C2L_EmblemCustomize = 13114;  // 符文定制
  MSG_L2C_EmblemCustomize = 13115;
  MSG_C2L_EmblemBuySlot = 13116;  // 购买符文栏位
  MSG_L2C_EmblemBuySlot = 13117;
  MSG_C2L_EmblemUpgrade = 13118;  // 符文升阶
  MSG_L2C_EmblemUpgrade = 13119;
  MSG_C2L_EmblemSuccinct = 13120;  // 符文洗炼
  MSG_L2C_EmblemSuccinct = 13121;
  MSG_C2L_EmblemSuccinctLockOrSave = 13122;  // 符文洗炼结果锁定或保存
  MSG_L2C_EmblemSuccinctLockOrSave = 13123;
  MSG_C2L_EmblemSuccinctItemConflate = 13124;  // 符文洗炼道具合成
  MSG_L2C_EmblemSuccinctItemConflate = 13125;

  // 点金
  MSG_MIN_GoldBuy = 13200;
  MSG_C2L_GoldBuyGet = 13201;  // 获取点金信息 NOLOG
  MSG_L2C_GoldBuyGet = 13202;
  MSG_C2L_GoldBuyGetGold = 13203;  // 领取金币
  MSG_L2C_GoldBuyGetGold = 13204;

  // 迷宫
  MSG_MIN_Maze = 13300;
  MSG_C2L_MazeGetMap = 13301;  // 获取迷宫地图信息
  MSG_L2C_MazeGetMap = 13302;
  MSG_C2L_MazeTriggerEvent = 13303;  // 触发地图中的事件
  MSG_L2C_MazeTriggerEvent = 13304;
  MSG_C2L_MazeRecoveryHero = 13305;  // 复活迷宫中出战的英雄
  MSG_L2C_MazeRecoveryHero = 13306;
  MSG_C2L_MazeGetGrid = 13307;  // 格子信息匹配敌人灵魂祭坛buff选择事件问题
  MSG_L2C_MazeGetGrid = 13308;
  MSG_C2L_MazeBuyRevive = 13309;  // 购买复生神像
  MSG_L2C_MazeBuyRevive = 13310;
  MSG_C2L_MazeUseItem = 13311;  // 使用道具
  MSG_L2C_MazeUseItem = 13312;
  MSG_C2L_MazeTaskReceiveAward = 13313;  //  领取任务奖励
  MSG_L2C_MazeTaskReceiveAward = 13314;
  MSG_L2C_MazeTaskUpdate = 13315;        // 更新任务进度
  MSG_C2L_MazeGetSelectMapData = 13316;  // 获取选择模板等级时需要的数据
  MSG_L2C_MazeGetSelectMapData = 13317;
  MSG_C2L_MazeSelectBuff = 13318;  // 选择buff
  MSG_L2C_MazeSelectBuff = 13319;
  MSG_C2L_MazeSweep = 13320;  // 扫荡
  MSG_L2C_MazeSweep = 13321;

  // 排行成就
  MSG_MIN_RankAchieve = 13400;
  MSG_C2L_RankAchieveList = 13401;  // 排行成就列表展示 NOLOG
  MSG_L2C_RankAchieveList = 13402;
  MSG_C2L_RankAchieveRecvAward = 13403;  // 领取排行成就奖励
  MSG_L2C_RankAchieveRecvAward = 13404;
  MSG_L2C_RankAchieveNotify = 13405;  // 成就达成通知

  // 公会
  MSG_MIN_Guild = 13500;
  MSG_C2L_GuildList = 13501;  // 获取公会列表 NOLOG
  MSG_L2C_GuildList = 13502;
  MSG_C2L_GuildGetMyInfo = 13503;  // 获取自己的公会信息 NOLOG
  MSG_L2C_GuildGetMyInfo = 13504;
  MSG_C2L_GuildCreate = 13505;  // 创建公会   NOLOG
  MSG_L2C_GuildCreate = 13506;
  MSG_C2L_GuildLogList = 13507;  // 获取公会日志 NOLOG
  MSG_L2C_GuildLogList = 13508;
  MSG_C2L_GuildManagerMember = 13509;  // 公会成员管理(任命为会长、任命为副会长(撤销)、踢出公会) NOLOG
  MSG_L2C_GuildManagerMember = 13510;
  MSG_C2L_GuildModifyInfo = 13511;  // 修改公会信息(头像、设置加入条件、宣言)  NOLOG
  MSG_L2C_GuildModifyInfo = 13512;
  MSG_C2L_GuildSetName = 13513;  // 修改昵称   NOLOG
  MSG_L2C_GuildSetName = 13514;
  MSG_C2L_GuildModifyNotice = 13515;  // 修改公告  NOLOG
  MSG_L2C_GuildModifyNotice = 13516;
  MSG_C2L_GuildApplyList = 13517;  // 获取公会申请列表(会长or副会长) NOLOG
  MSG_L2C_GuildApplyList = 13518;
  MSG_C2L_GuildApplyRatify = 13519;  // 审核公会申请  NOLOG
  MSG_L2C_GuildApplyRatify = 13520;
  MSG_C2L_GuildUserApply = 13521;  // 申请公会(申请、取消申请、快速加入、快速转会等)  NOLOG
  MSG_L2C_GuildUserApply = 13522;
  MSG_C2L_GuildSendMail = 13523;  // 发送公会邮件(会长or副会长) NOLOG
  MSG_L2C_GuildSendMail = 13524;
  MSG_C2L_GuildQuit = 13527;  // 退出公会  NOLOG
  MSG_L2C_GuildQuit = 13528;
  MSG_C2L_GuildDisband = 13529;  // 解散公会  NOLOG
  MSG_L2C_GuildDisband = 13530;
  MSG_C2L_GuildSearch = 13531;  // 查找公会 NOLOG
  MSG_L2C_GuildSearch = 13532;
  MSG_C2L_GuildGetMembers = 13533;  // 获取公会成员 NOLOG
  MSG_L2C_GuildGetMembers = 13534;
  MSG_C2L_GuildGetDeclaration = 13535;  // 获取公会宣言 NOLOG
  MSG_L2C_GuildGetDeclaration = 13536;
  MSG_L2C_GuildNotify = 13537;          // 推送玩家变化
  MSG_L2C_GuildUpdateInfo = 13538;      // 推送公会等级和经验
  MSG_C2L_GuildSendRecruitMsg = 13539;  // 发送公会招募信息  NOLOG
  MSG_L2C_GuildSendRecruitMsg = 13540;
  MSG_C2L_GuildDonate = 13541;  // 公会捐献  NOLOG
  MSG_L2C_GuildDonate = 13542;
  MSG_C2L_GuildGetDonateAward = 13543;  // 获取捐献奖励  NOLOG
  MSG_L2C_GuildGetDonateAward = 13544;
  MSG_C2L_GuildDonateLogList = 13545;  // 获取公会捐赠日志 NOLOG
  MSG_L2C_GuildDonateLogList = 13546;
  MSG_C2L_GuildMainInfo = 13547;  // 进入公会，获取公会主信息 NOLOG
  MSG_L2C_GuildMainInfo = 13548;
  MSG_C2L_GuildRank = 13549;  // 公会排行  NOLOG
  MSG_L2C_GuildRank = 13550;
  MSG_C2L_GuildListGetDetail = 13551;  // 公会列表，获取某个公会的详细信息 NOLOG
  MSG_L2C_GuildListGetDetail = 13552;
  MSG_C2L_GuildGetBadgeList = 13553;  // 获取公会已解锁徽章  NOLOG
  MSG_L2C_GuildGetBadgeList = 13554;
  MSG_L2C_GuildSyncKickCount = 13555;         // 向公会官员同步当日踢人次数
  MSG_C2L_GuildGetDivisionAwardInfo = 13556;  // 获取公会段位奖励相关信息
  MSG_L2C_GuildGetDivisionAwardInfo = 13557;
  MSG_C2L_GuildGetMedals = 13558;
  MSG_L2C_GuildGetMedals = 13559;
  MSG_C2L_GuildMedalLike = 13560;
  MSG_L2C_GuildMedalLike = 13561;
  MSG_C2L_GuildCombineApply = 13562;  // 公会合并申请
  MSG_L2C_GuildCombineApply = 13563;
  MSG_C2L_GuildCombineCheck = 13564;  // 公会合并确认
  MSG_L2C_GuildCombineCheck = 13565;
  MSG_C2L_GuildCombineRatify = 13566;  // 审核公会合并请求
  MSG_L2C_GuildCombineRatify = 13567;

  // 个人boss(幻境)
  MSG_MIN_Mirage = 13600;
  MSG_C2L_MirageList = 13601;  // 副本列表信息 NOLOG
  MSG_L2C_MirageList = 13602;
  MSG_C2L_MirageDetail = 13603;  // 单个副本详情数据展示 NOLOG
  MSG_L2C_MirageDetail = 13604;
  MSG_C2L_MirageFight = 13605;  // 战斗
  MSG_L2C_MirageFight = 13606;
  //  MSG_C2L_MirageRecvStarAward = 13607;  //领取星数奖励
  //  MSG_L2C_MirageRecvStarAward = 13608;
  MSG_C2L_MirageSaveAffixes = 13609;  // 保存词缀
  MSG_L2C_MirageSaveAffixes = 13610;
  MSG_C2L_MiragePowerCrush = 13611;  // 战力碾压 NOLOG 日志已与MirageFight合并
  MSG_L2C_MiragePowerCrush = 13612;
  MSG_C2L_MirageSweep = 13613;  // 扫荡 NOLOG 日志已与MirageFight合并
  MSG_L2C_MirageSweep = 13614;
  MSG_C2L_MirageReceiveAward = 13615;  // 领取奖励
  MSG_L2C_MirageReceiveAward = 13616;
  MSG_C2L_MirageBuyCount = 13617;  // 购买次数
  MSG_L2C_MirageBuyCount = 13618;
  MSG_C2L_MirageTestDrop = 13619;  // 掉落测试
  MSG_L2C_MirageTestDrop = 13620;

  // 密林
  MSG_MIN_Forest = 13700;
  MSG_C2L_ForestInfo = 13701;  // 密林信息 NOLOG
  MSG_L2C_ForestInfo = 13702;
  MSG_C2L_ForestStartFeed = 13703;  // 开始献祭(喂养种子)
  MSG_L2C_ForestStartFeed = 13704;
  MSG_C2L_ForestFeedGoblin = 13705;  // 献祭哥布林
  MSG_L2C_ForestFeedGoblin = 13706;
  MSG_C2L_ForestChangeGoblin = 13707;  // 更换哥布林
  MSG_L2C_ForestChangeGoblin = 13708;
  MSG_C2L_ForestFeedSpecial = 13709;  // 使用特殊道具树更新至最高品质
  MSG_L2C_ForestFeedSpecial = 13710;
  MSG_C2L_ForestUpdateSlot = 13711;  // 更新采集槽位数据
  MSG_L2C_ForestUpdateSlot = 13712;
  MSG_C2L_ForestStartPlant = 13713;  // 开始种树
  MSG_L2C_ForestStartPlant = 13714;
  MSG_C2L_ForestSpeedGrow = 13715;  // 加速生长
  MSG_L2C_ForestSpeedGrow = 13716;
  MSG_C2L_ForestHarvest = 13717;  // 收获资源
  MSG_L2C_ForestHarvest = 13718;
  MSG_C2L_ForestSearch = 13719;  // 搜索
  MSG_L2C_ForestSearch = 13720;
  MSG_C2L_ForestLoot = 13721;  // 掠夺
  MSG_L2C_ForestLoot = 13722;
  MSG_C2L_ForestLogList = 13723;  // 被掠夺日志列表 NOLOG
  MSG_L2C_ForestLogList = 13724;
  MSG_C2L_ForestRevenge = 13725;  // 复仇
  MSG_L2C_ForestRevenge = 13726;
  MSG_C2L_ForestRecvLvAward = 13727;  // 领取等级奖励
  MSG_L2C_ForestRecvLvAward = 13728;

  // 境界
  MSG_MIN_Memory = 13800;
  MSG_C2L_MemoryLatest = 13801;  // 最新的回忆 NOLOG
  MSG_L2C_MemoryLatest = 13802;
  MSG_C2L_MemoryUnlock = 13803;  // 回忆点解锁
  MSG_L2C_MemoryUnlock = 13804;

  // 公会副本
  MSG_MIN_GuildDungeon = 13900;
  MSG_C2L_GuildDungeonInfo = 13901;  // 进入公会副本 NOLOG
  MSG_L2C_GuildDungeonInfo = 13902;
  MSG_C2L_GuildDungeonRecvChapterTaskAward = 13903;  // 领取章节奖励
  MSG_L2C_GuildDungeonRecvChapterTaskAward = 13904;
  MSG_C2L_GuildDungeonFight = 13905;  // 挑战关卡
  MSG_L2C_GuildDungeonFight = 13906;
  MSG_C2L_GuildDungeonUserDamageRank = 13907;  // 公会成员副本伤害（历史最高）排行 NOLOG
  MSG_L2C_GuildDungeonUserDamageRank = 13908;
  MSG_L2C_GuildDungeonNotify = 13909;   // 推送公会的最新章节
  MSG_C2L_GuildDungeonLogList = 13910;  // 攻打日志 NOLOG
  MSG_L2C_GuildDungeonLogList = 13911;
  MSG_C2L_GuildDungeonChapterInfo = 13912;  // 进入章节  NOLOG
  MSG_L2C_GuildDungeonChapterInfo = 13913;
  MSG_C2L_GuildDungeonRecvBossBoxAward = 13914;  // 领取boss宝箱奖励
  MSG_L2C_GuildDungeonRecvBossBoxAward = 13915;
  MSG_C2L_GuildDungeonBuyChallengeTimes = 13916;  // 公会副本购买挑战次数
  MSG_L2C_GuildDungeonBuyChallengeTimes = 13917;
  MSG_C2L_GuildDungeonSeasonDivisionAward = 13926;  // 赛季首通段位奖励
  MSG_L2C_GuildDungeonSeasonDivisionAward = 13927;
  MSG_C2L_GuildDungeonSetFocus = 13928;  // 集火   NOLOG
  MSG_L2C_GuildDungeonSetFocus = 13929;
  MSG_C2L_GuildDungeonHallOfFame = 13930;  // 获取荣耀殿堂数据   NOLOG
  MSG_L2C_GuildDungeonHallOfFame = 13931;
  MSG_C2L_GuildDungeonTop3Guild = 13932;  // 获取赛季TOP3公会  NOLOG
  MSG_L2C_GuildDungeonTop3Guild = 13933;
  MSG_C2L_GuildDungeonGetStrategy = 13934;  // 获取秘技信息  NOLOG
  MSG_L2C_GuildDungeonGetStrategy = 13935;
  MSG_C2L_GuildDungeonUseStrategy = 13936;  // 使用秘技   NOLOG
  MSG_L2C_GuildDungeonUseStrategy = 13937;
  MSG_L2C_GuildDungeonStrategyUseNotify = 13938;    // 秘技使用推送
  MSG_C2L_GuildDungeonRecvAllBossBoxAward = 13939;  // 一键领取boss宝箱奖励
  MSG_L2C_GuildDungeonRecvAllBossBoxAward = 13940;
  MSG_C2L_GuildDungeonGetMembersFightInfo = 13941;  // 获取挑战成员列表
  MSG_L2C_GuildDungeonGetMembersFightInfo = 13942;

  // 英雄列传
  MSG_MIN_Tales = 14000;
  MSG_C2L_TalesList = 14001;  // 列表信息 NOLOG
  MSG_L2C_TalesList = 14002;
  MSG_C2L_TalesChapterFight = 14003;  // 章节战斗
  MSG_L2C_TalesChapterFight = 14004;
  MSG_C2L_TalesChapterFinish = 14005;  // 章节完成
  MSG_L2C_TalesChapterFinish = 14006;
  MSG_C2L_TalesChapterTakeReward = 14007;  // 章节领取奖励
  MSG_L2C_TalesChapterTakeReward = 14008;
  MSG_C2L_TalesEliteFight = 14009;  // 强敌挑战
  MSG_L2C_TalesEliteFight = 14010;
  MSG_C2L_TalesEliteWipe = 14011;  // 强敌扫荡
  MSG_L2C_TalesEliteWipe = 14012;

  // 公会天赋
  MSG_MIN_GuildTalent = 14100;
  MSG_C2L_GuildTalentList = 14101;  // 公会天赋列表 NOLOG
  MSG_L2C_GuildTalentList = 14102;
  MSG_C2L_GuildTalentLevelUp = 14103;  // 公会天赋升级
  MSG_L2C_GuildTalentLevelUp = 14104;
  MSG_C2L_GuildTalentReset = 14105;  // 公会天赋重置
  MSG_L2C_GuildTalentReset = 14106;

  // 新手引导
  MSG_MIN_Guidance = 14200;
  MSG_C2L_GuidanceList = 14201;  // 新手引导-数据 NOLOG
  MSG_L2C_GuidanceList = 14202;
  MSG_C2L_GuidanceFinishStep = 14203;  // 新手引导-完成节点
  MSG_L2C_GuidanceFinishStep = 14204;
  MSG_C2L_GuidanceFinishGroup = 14205;  // 新手引导-完成组
  MSG_L2C_GuidanceFinishGroup = 14206;
  MSG_C2L_GuidanceSkip = 14209;  // 老玩家跳过一部分新手引导
  MSG_L2C_GuidanceSkip = 14210;

  // 嘉年华
  MSG_MIN_Carnival = 14300;
  MSG_C2L_CarnivalGetData = 14301;  // 获取嘉年华信息 NOLOG
  MSG_L2C_CarnivalGetData = 14302;
  MSG_C2L_CarnivalReceiveAward = 14303;  // 领取嘉年华奖励
  MSG_L2C_CarnivalReceiveAward = 14304;
  MSG_L2C_CarnivalUpdate = 14305;
  MSG_L2C_CarnivalNotify = 14306;

  // 七日登录
  MSG_MIN_SevenDayLogin = 14400;
  MSG_C2L_SevenDayLoginData = 14401;  // 请求数据 NOLOG
  MSG_L2C_SevenDayLoginData = 14402;
  MSG_C2L_SevenDayLoginTakeAward = 14403;  // 领取奖励
  MSG_L2C_SevenDayLoginTakeAward = 14404;

  // 功勋
  MSG_MIN_Medal = 14500;
  MSG_C2L_MedalGetData = 14501;  // 获取功勋信息 NOLOG
  MSG_L2C_MedalGetData = 14502;
  MSG_C2L_MedalReceiveAward = 14503;  // 领取功勋奖励
  MSG_L2C_MedalReceiveAward = 14504;
  MSG_L2C_MedalUpdate = 14505;             // 更新任务进度
  MSG_L2C_MedalGoddessCureUpdate = 14506;  // 女武神治疗更新

  // 图鉴
  MSG_MIN_Handbooks = 14600;
  MSG_C2L_HandbooksGetData = 14601;  // 图鉴获取 NOLOG
  MSG_L2C_HandbooksGetData = 14602;
  MSG_C2L_HandbooksActive = 14603;  // 图鉴激活
  MSG_L2C_HandbooksActive = 14604;
  MSG_C2L_HandbooksReceiveAwards = 14605;  // 图鉴奖励领取 NOLOG
  MSG_L2C_HandbooksReceiveAwards = 14606;
  MSG_C2L_HandbooksActiveHeroAttr = 14607;  // 英雄图鉴激活属性
  MSG_L2C_HandbooksActiveHeroAttr = 14608;

  // 水晶
  MSG_MIN_Crystal = 14700;
  MSG_C2L_CrystalAddHero = 14701;  // 添加共鸣英雄
  MSG_L2C_CrystalAddHero = 14702;
  MSG_C2L_CrystalRemoveHero = 14703;  // 移出共鸣英雄
  MSG_L2C_CrystalRemoveHero = 14704;
  MSG_C2L_CrystalUnlockSlot = 14705;  // 解锁槽位
  MSG_L2C_CrystalUnlockSlot = 14706;
  MSG_C2L_CrystalSpeedSlotCD = 14707;  // 加速完成槽位冷却
  MSG_L2C_CrystalSpeedSlotCD = 14708;
  MSG_C2L_CrystalGetAllData = 14709;  // 获取水晶信息 NOLOG
  MSG_L2C_CrystalGetAllData = 14710;
  MSG_L2C_CrystalGetShareAttr = 14711;       // 推送共鸣属性
  MSG_L2C_CrystalHeroesUpdate = 14712;       // 推送水晶中英雄状态的更新(缔约和共鸣英雄状态变化时推送)
  MSG_C2L_CrystalActiveAchievement = 14713;  // 激活成就
  MSG_L2C_CrystalActiveAchievement = 14714;
  MSG_L2C_CrystalUpdateAchievement = 14717;  // 更新成就进度

  // 新功能预告
  MSG_MIN_Forecast = 14800;
  MSG_C2L_ForecastGetData = 14801;  // 获取新功能开启信息 NOLOG
  MSG_L2C_ForecastGetData = 14802;
  MSG_C2L_ForecastReceiveAward = 14803;  //  领取功勋奖励
  MSG_L2C_ForecastReceiveAward = 14804;
  MSG_L2C_ForecastUpdate = 14805;  // 更新任务进度

  // 充值
  MSG_MIN_Recharge = 14900;
  MSG_C2L_RechargeGetData = 14901;  // 充值信息 NOLOG
  MSG_L2C_RechargeGetData = 14902;
  MSG_L2C_RechargeNotify = 14903;           // 推送订单到账信息
  MSG_L2C_RechargeFirstGiftNotify = 14904;  // 推送首充礼包信息
  MSG_C2L_RechargeSimulation = 14905;       // 伪充 NOLOG
  MSG_L2C_RechargeSimulation = 14906;
  MSG_L2C_WebRechargeNotify = 14907;  // 推送官网充值到账信息
  MSG_C2L_RechargeByCoupon = 14908;   // 代金券充值
  MSG_L2C_RechargeByCoupon = 14909;
  MSG_C2L_RechargeFirstGiftReward = 14910;  // 首充礼包领取奖励
  MSG_L2C_RechargeFirstGiftReward = 14911;

  // 条件爬塔
  MSG_MIN_Towerstar = 15000;
  MSG_C2L_TowerstarGetData = 15001;  // 条件爬塔信息 NOLOG
  MSG_L2C_TowerstarGetData = 15002;
  MSG_C2L_TowerstarFight = 15003;  // 条件爬塔战斗
  MSG_L2C_TowerstarFight = 15004;
  MSG_C2L_TowerstarStarRecvAward = 15005;  // 条件爬塔星级奖励 NOLOG
  MSG_L2C_TowerstarStarRecvAward = 15006;
  MSG_C2L_TowerstarDailyRecvAward = 15007;  // 条件爬塔每日奖励 NOLOG
  MSG_L2C_TowerstarDailyRecvAward = 15008;

  // vip
  MSG_MIN_Vip = 15100;
  MSG_C2L_VipInfoGet = 15101;  // 获取vip信息 NOLOG
  MSG_L2C_VipInfoGet = 15102;
  MSG_C2L_VipBuyGift = 15103;  // 购买礼包
  MSG_L2C_VipBuyGift = 15104;
  MSG_L2C_VipGiftRechargeNotify = 15105;  // vip付费礼包

  // 限时礼包
  MSG_MIN_ActivityRecharge = 15200;
  MSG_C2L_ActivityRechargeGet = 15201;  // 拉取信息 NOLOG
  MSG_L2C_ActivityRechargeGet = 15202;
  MSG_C2L_ActivityRechargeBuy = 15203;  // 购买礼包
  MSG_L2C_ActivityRechargeBuy = 15204;

  // 月卡
  MSG_MIN_MonthlyCard = 15300;
  MSG_C2L_MonthlyCardGetData = 15301;  // 获取月卡信息 NOLOG
  MSG_L2C_MonthlyCardGetData = 15302;
  MSG_C2L_MonthlyCardReceiveAward = 15303;  // 领取每日奖励
  MSG_L2C_MonthlyCardReceiveAward = 15304;
  MSG_L2C_MonthlyCardRechargeNotify = 15305;  // 充值

  // 战令
  MSG_MIN_Pass = 15400;
  MSG_C2L_PassGetData = 15401;  // 获取战令信息 NOLOG
  MSG_L2C_PassGetData = 15402;
  MSG_C2L_PassReceiveAward = 15403;  // 获取奖励
  MSG_L2C_PassReceiveAward = 15404;
  MSG_L2C_PassBuyNotify = 15405;  // 充值
  MSG_L2C_PassUpdate = 15406;     // 状态更新
  MSG_C2L_PassLevelBuy = 15407;   // 等级购买
  MSG_L2C_PassLevelBuy = 15408;

  // 推送礼包
  MSG_MIN_PushGift = 15500;
  MSG_C2L_PushGiftGetData = 15501;  // 获取推送礼包信息 NOLOG
  MSG_L2C_PushGiftGetData = 15502;
  MSG_L2C_PushGiftRechargeAward = 15503;  // 充值奖励
  MSG_L2C_PushGiftUpdate = 15504;         // 礼包信息

  // 评分
  MSG_MIN_Rate = 15600;
  MSG_C2L_RateGetStatus = 15601;  // 获取状态 NOLOG
  MSG_L2C_RateGetStatus = 15602;
  MSG_C2L_RateScore = 15603;  // 评分
  MSG_L2C_RateScore = 15604;

  // 活动阶梯礼包
  MSG_MIN_OperateActivity = 15700;
  MSG_C2L_OperateActivityGetData = 15701;  // 获取数据 NOLOG
  MSG_L2C_OperateActivityGetData = 15702;
  MSG_C2L_OperateActivityGetXML = 15703;  // 活动XML NOLOG
  MSG_L2C_OperateActivityGetXML = 15704;
  MSG_L2C_OperateActivityRecharge = 15705;      // 充值返回
  MSG_L2C_OperateActivityCanXMLUpdate = 15706;  // 更新后下发礼包ID
  MSG_C2L_OperateActivityInitActivity = 15707;  // 初始化数据
  MSG_L2C_OperateActivityInitActivity = 15708;
  MSG_C2L_OperateActivityGetTaskReward = 15709;  // 获取奖励
  MSG_L2C_OperateActivityGetTaskReward = 15710;
  MSG_L2C_OperateActivityUpdateData = 15711;
  MSG_C2L_OperateActivityPromotionSelectAward = 15712;  // 新推送礼包选择奖励物品 NOLOG
  MSG_L2C_OperateActivityPromotionSelectAward = 15713;
  MSG_C2L_OperateActivityPromotionRechargeCheck = 15714;  // 新推送礼包充值前的检查 NOLOG
  MSG_L2C_OperateActivityPromotionRechargeCheck = 15715;

  // 契约之所
  MSG_MIN_GoddessContract = 15800;
  MSG_C2L_GoddessContractGetData = 15801;  // 获取数据 NOLOG
  MSG_L2C_GoddessContractGetData = 15802;
  MSG_C2L_GoddessFeed = 15803;  // 喂食
  MSG_L2C_GoddessFeed = 15804;
  MSG_C2L_GoddessTouch = 15805;  // 抚摸
  MSG_L2C_GoddessTouch = 15806;
  MSG_C2L_GoddessStoryAward = 15807;  // 英雄故事领奖
  MSG_L2C_GoddessStoryAward = 15808;
  MSG_C2L_GoddessUnlock = 15809;  // 解锁对应英雄
  MSG_L2C_GoddessUnlock = 15810;
  MSG_C2L_GoddessChapterFight = 15811;  // 章节战斗
  MSG_L2C_GoddessChapterFight = 15812;
  MSG_C2L_GoddessCollect = 15813;  // 收藏女武神
  MSG_L2C_GoddessCollect = 15814;
  MSG_L2C_GoddessUpdateSuitIds = 15815;  // 获得新服装
  MSG_C2L_GoddessChangeSuit = 15816;     // 女武神换装
  MSG_L2C_GoddessChangeSuit = 15817;
  MSG_C2L_GoddessRecovery = 15818;  // 女武神疗伤
  MSG_L2C_GoddessRecovery = 15819;
  MSG_C2L_GoddessSuitUnlock = 15820;  // 女武神皮肤解锁
  MSG_L2C_GoddessSuitUnlock = 15821;
  MSG_C2L_ActivityGoddessRecvRecoveryAward = 15822;
  MSG_L2C_ActivityGoddessRecvRecoveryAward = 15823;
  MSG_C2L_ActivityGoddessActive = 15824;
  MSG_L2C_ActivityGoddessActive = 15825;

  // 神魔抽卡
  MSG_MIN_DivineDemon = 15900;
  MSG_C2L_DivineDemonGetOpenActivity = 15901;  // 获取开放的活动
  MSG_L2C_DivineDemonGetOpenActivity = 15902;
  MSG_C2L_DivineDemonSummon = 15903;  // 英雄抽卡
  MSG_L2C_DivineDemonSummon = 15904;
  MSG_C2L_DivineDemonReceiveTaskAward = 15905;  // 领取任务奖励
  MSG_L2C_DivineDemonReceiveTaskAward = 15906;
  MSG_L2C_DivineDemonUpdateTaskProgress = 15907;  // 任务进度更新
  MSG_L2C_DivineDemonUpdate = 15910;              // 活动配置更新，推送给前端，让前端拉取最新的数据

  // 神树争霸
  MSG_MIN_Wrestle = 16000;
  MSG_C2L_WrestleInfo = 16001;  // 获取基本信息   NOLOG
  MSG_L2C_WrestleInfo = 16002;
  MSG_C2L_WrestleMapInfo = 16003;  // 获取地图信息   NOLOG
  MSG_L2C_WrestleMapInfo = 16004;
  MSG_C2L_WrestleTopUserList = 16005;  // 获取头部玩家  NOLOG
  MSG_L2C_WrestleTopUserList = 16006;
  MSG_C2L_WrestleRoomInfo = 16007;  // 获取房间信息   NOLOG
  MSG_L2C_WrestleRoomInfo = 16008;
  MSG_C2L_WrestleFightLog = 16009;  // 获取历史记录   NOLOG
  MSG_L2C_WrestleFightLog = 16010;
  MSG_C2L_WrestleFight = 16011;  // 战斗  NOLOG
  MSG_L2C_WrestleFight = 16012;
  MSG_C2L_WrestleLike = 16013;  // 点赞  NOLOG
  MSG_L2C_WrestleLike = 16014;
  MSG_C2L_WrestleRankList = 16015;  // 获取排行榜   NOLOG
  MSG_L2C_WrestleRankList = 16016;
  MSG_C2L_WrestleRecvTaskAward = 16017;  // 领取任务奖励  NOLOG
  MSG_L2C_WrestleRecvTaskAward = 16018;
  MSG_C2L_WrestleHallOfFame = 16019;  // 获取荣誉殿堂  NOLOG
  MSG_L2C_WrestleHallOfFame = 16020;
  MSG_C2L_WrestleChangeRoom = 16021;  // 更换房间   NOLOG
  MSG_L2C_WrestleChangeRoom = 16022;
  MSG_L2C_WrestleRoomUpdate = 16023;  // 房间数据更新 NOLOG

  // 联结
  MSG_MIN_Link = 16100;
  MSG_C2L_LinkInfo = 16101;  // 获取基本信息   NOLOG
  MSG_L2C_LinkInfo = 16102;
  MSG_C2L_LinkSetView = 16103;  // 设置查看过的联结信息   NOLOG
  MSG_L2C_LinkSetView = 16104;

  // 功能状态：开 or 关
  MSG_MIN_Function = 16200;
  MSG_C2L_FunctionGetStatus = 16201;  // 获取功能状态 NOLOG
  MSG_L2C_FunctionGetStatus = 16202;

  // 公告
  MSG_MIN_Announcement = 16300;
  MSG_C2L_AnnouncementGetData = 16301;  // 获取公告信息 NOLOG
  MSG_L2C_AnnouncementGetData = 16302;
  MSG_L2C_AnnouncementUpdate = 16303;  // 公告更新推送

  // 幽暗密林 - 0.9.8
  MSG_MIN_Flower = 16400;
  MSG_C2L_FlowerMainInfo = 16401;  // 获取主界面信息 NOLOG
  MSG_L2C_FlowerMainInfo = 16402;
  MSG_C2L_FlowerStartFeed = 16403;  // 开始献祭(喂养种子)
  MSG_L2C_FlowerStartFeed = 16404;
  MSG_C2L_FlowerFeedGoblin = 16405;  // 献祭哥布林
  MSG_L2C_FlowerFeedGoblin = 16406;
  MSG_C2L_FlowerChangeGoblin = 16407;  // 更换哥布林
  MSG_L2C_FlowerChangeGoblin = 16408;
  MSG_C2L_FlowerFeedSpecial = 16409;  // 使用特殊道具，将种子升至最高品质
  MSG_L2C_FlowerFeedSpecial = 16410;
  MSG_C2L_FlowerStartPlant = 16411;  // 开始种花
  MSG_L2C_FlowerStartPlant = 16412;
  MSG_C2L_FlowerSpeedGrow = 16413;  // 加速生长
  MSG_L2C_FlowerSpeedGrow = 16414;
  MSG_C2L_FlowerHarvest = 16415;  // 收获资源
  MSG_L2C_FlowerHarvest = 16416;
  MSG_C2L_FlowerSearch = 16417;  // 搜索
  MSG_L2C_FlowerSearch = 16418;
  MSG_C2L_FlowerEnemiesInfo = 16419;  // 获取协助信息 NOLOG
  MSG_L2C_FlowerEnemiesInfo = 16420;
  MSG_C2L_FlowerSnatch = 16421;  // 协助玩家的花
  MSG_L2C_FlowerSnatch = 16422;
  MSG_C2L_FlowerLogList = 16423;  // 获取日志列表 NOLOG
  MSG_L2C_FlowerLogList = 16424;
  MSG_C2L_FlowerRevengeSnatch = 16425;  // 抢夺模式复仇
  MSG_L2C_FlowerRevengeSnatch = 16426;
  MSG_C2L_FlowerTimberInfo = 16427;  // 据点模式，获取森林信息 NOLOG
  MSG_L2C_FlowerTimberInfo = 16428;
  MSG_C2L_FlowerJungleInfo = 16429;  // 据点模式，获取丛林信息 NOLOG
  MSG_L2C_FlowerJungleInfo = 16430;
  MSG_C2L_FlowerOccupyAttack = 16431;  // 据点模式，攻打据点
  MSG_L2C_FlowerOccupyAttack = 16432;
  MSG_C2L_FlowerExtendOccupyTime = 16433;  // 据点模式，延长据点占领时间
  MSG_L2C_FlowerExtendOccupyTime = 16434;
  MSG_C2L_FlowerAttackLevelGuard = 16435;  // 攻打升级守卫
  MSG_L2C_FlowerAttackLevelGuard = 16436;
  MSG_C2L_FlowerLevelUp = 16437;  // 密林升级
  MSG_L2C_FlowerLevelUp = 16438;
  MSG_C2L_FlowerBuyOccupyAttackNum = 16439;  // 据点模式，购买进攻次数
  MSG_L2C_FlowerBuyOccupyAttackNum = 16440;
  MSG_C2L_FlowerPreviewOccupyAward = 16441;  // 据点模式，预览结算据点奖励 NOLOG
  MSG_L2C_FlowerPreviewOccupyAward = 16442;
  MSG_C2L_FlowerRecvOccupyAward = 16443;  // 据点模式，领取据点采集奖励
  MSG_L2C_FlowerRecvOccupyAward = 16444;
  MSG_C2L_FlowerOccupyRecommend = 16445;  // 据点模式，推荐占领 NOLOG
  MSG_L2C_FlowerOccupyRecommend = 16446;
  MSG_C2L_FlowerRevengeOccupy = 16447;  // 据点模式复仇
  MSG_L2C_FlowerRevengeOccupy = 16448;
  MSG_C2L_FlowerOccupyHistory = 16449;  // 据点历史结算数据 NOLOG
  MSG_L2C_FlowerOccupyHistory = 16450;
  MSG_C2L_FlowerShareFlowerbed = 16451;  // 向公会分享花坛位置 NOLOG
  MSG_L2C_FlowerShareFlowerbed = 16452;
  MSG_C2L_FlowerRecvPreviewOccupyAward = 16453;  // 据点模式，领取预览结算据点奖励
  MSG_L2C_FlowerRecvPreviewOccupyAward = 16454;
  MSG_C2L_FlowerAssistSendLike = 16455;  // 协助模式，赠送友情点
  MSG_L2C_FlowerAssistSendLike = 16456;
  MSG_C2L_FlowerAssistRecvLike = 16457;  // 协助模式，收取友情点
  MSG_L2C_FlowerAssistRecvLike = 16458;
  MSG_L2C_FlowerAssistNotify = 16459;      // 协助模式，通知
  MSG_C2L_FlowerOccupyAssistList = 16460;  // 小助手，占矿推荐列表 NOLOG
  MSG_L2C_FlowerOccupyAssistList = 16461;

  // 全民无双
  MSG_MIN_MonthTasks = 16500;
  MSG_C2L_MonthTasksGetData = 16501;  // 获取全民无双信息 NOLOG
  MSG_L2C_MonthTasksGetData = 16502;
  MSG_C2L_MonthTasksRecvAwards = 16503;  // 领取全民无双奖励 NOLOG
  MSG_L2C_MonthTasksRecvAwards = 16504;
  MSG_L2C_MonthTasksUpdate = 16505;  // 进度更新推送 NOLOG

  // 每日许愿
  MSG_MIN_DailyWish = 16600;     // 每日许愿
  MSG_C2L_DailyWishGet = 16601;  // 获取每日许愿数据 NOLOG
  MSG_L2C_DailyWishGet = 16602;
  MSG_C2L_DailyWishSummon = 16603;  // 抽取每日许愿奖励
  MSG_L2C_DailyWishSummon = 16604;
  MSG_C2L_DailyWishXmlGet = 16605;  // 获取每日活动XML信息
  MSG_L2C_DailyWishXmlGet = 16606;
  MSG_L2C_DailyWishXmlUpdate = 16607;  // 更新的XML信息
  MSG_L2C_DailyWishUpdate = 16608;     // 更新变动的每日许愿信息

  // 流派抽卡
  MSG_MIN_LinkHeroSummon = 16700;  // 流派抽卡
  MSG_C2L_LinkHeroSummon = 16701;  // 流派抽卡
  MSG_L2C_LinkHeroSummon = 16702;
  MSG_C2L_LinkHeroSummonGet = 16703;  // 获取抽卡数据
  MSG_L2C_LinkHeroSummonGet = 16704;
  MSG_L2C_LinkHeroSummonPoolTimeUpdate = 16705;  // 更新
  MSG_C2L_LinkHeroSummonTest = 16706;            // 抽卡测试
  MSG_L2C_LinkHeroSummonTest = 16707;

  // 神器首发
  MSG_MIN_ArtifactDebut = 16800;
  MSG_C2L_ArtifactDebutMainInfo = 16801;  // 获取玩法数据 NOLOG
  MSG_L2C_ArtifactDebutMainInfo = 16802;
  MSG_C2L_ArtifactDebutSetWish = 16803;  // 设置心愿神器
  MSG_L2C_ArtifactDebutSetWish = 16804;
  MSG_C2L_ArtifactDebutSummon = 16805;  // 抽卡
  MSG_L2C_ArtifactDebutSummon = 16806;
  MSG_C2L_ArtifactDebutRecvActAward = 16807;  // 领取活动奖励
  MSG_L2C_ArtifactDebutRecvActAward = 16808;
  MSG_C2L_ArtifactDebutRecvTaskAward = 16809;  // 领取任务奖励
  MSG_L2C_ArtifactDebutRecvTaskAward = 16810;
  MSG_L2C_ArtifactDebutUpdateTask = 16811;  // 更新任务进度
  MSG_C2L_ArtifactDebutOpenPuzzle = 16812;  // 拼图游戏掀格子
  MSG_L2C_ArtifactDebutOpenPuzzle = 16813;
  MSG_L2C_ArtifactDebutUpdateActivity = 16814;  // 活动配置更新
  MSG_C2L_ArtifactDebutGetActivity = 16815;     // 获取活动配置 NOLOG
  MSG_L2C_ArtifactDebutGetActivity = 16816;
  MSG_C2L_ArtifactDebutTestSummon = 16817;  // 抽卡测试 NOLOG
  MSG_L2C_ArtifactDebutTestSummon = 16818;

  // 基础机制补充
  MSG_MIN_BaseAdd = 16900;
  MSG_C2L_GetAchieveShowcase = 16901;  // 获取玩家面板展示成就    NOLOG
  MSG_L2C_GetAchieveShowcase = 16902;
  MSG_C2L_HasRecvH5DesktopReward = 16903;  // 获取是否已领取h5保存到桌面的奖励 NOLOG
  MSG_L2C_HasRecvH5DesktopReward = 16904;
  MSG_C2L_RecvH5DesktopReward = 16905;  // 领取h5保存到桌面的奖励
  MSG_L2C_RecvH5DesktopReward = 16906;
  MSG_C2L_Test = 16907;  // 测试协议 NOLOG
  MSG_L2C_Test = 16908;
  MSG_C2L_SeasonEnter = 16909;  // 进入赛季 NOLOG
  MSG_L2C_SeasonEnter = 16910;
  MSG_C2L_GetSeasonRankFirst = 16911;  //  获取赛季玩法排行榜首页数据 NOLOG
  MSG_L2C_GetSeasonRankFirst = 16912;
  MSG_C2L_GetSeasonRankList = 16913;  // 获取赛季排行榜 NOLOG
  MSG_L2C_GetSeasonRankList = 16914;
  MSG_C2L_GetSeasonFlashBackData = 16915;  //  获取赛季回顾数据 NOLOG
  MSG_L2C_GetSeasonFlashBackData = 16916;
  MSG_C2L_GetDefFormationPower = 16917;  // 获取防守阵容战力
  MSG_L2C_GetDefFormationPower = 16918;
  MSG_C2L_CommonRankLike = 16919;  // 通用排行榜点赞
  MSG_L2C_CommonRankLike = 16920;
  MSG_C2L_TestEtcdGiftCode = 16921;
  MSG_L2C_TestEtcdGiftCode = 16922;
  MSG_C2L_TestDrop = 16923;
  MSG_L2C_TestDrop = 16924;
  MSG_C2L_MuteAccount = 16925;
  MSG_L2C_MuteAccount = 16926;

  // 新服轮次活动
  MSG_MIN_RoundActivity = 17000;
  MSG_C2L_RoundActivityGetData = 17001;  // 获取活动数据 NOLOG
  MSG_L2C_RoundActivityGetData = 17002;
  MSG_C2L_RoundActivityRecvTaskAward = 17003;  // 领取任务奖励
  MSG_L2C_RoundActivityRecvTaskAward = 17004;
  MSG_L2C_RoundActivityUpdateTask = 17005;  // 更新任务进度

  // 赛季塔
  MSG_MIN_TowerSeason = 17100;
  MSG_C2L_TowerSeasonGetData = 17101;  // 获取赛季塔数据 NOLOG
  MSG_L2C_TowerSeasonGetData = 17102;
  MSG_C2L_TowerSeasonFight = 17103;  // 赛季塔战斗
  MSG_L2C_TowerSeasonFight = 17104;
  MSG_C2L_TowerSeasonRecvAward = 17105;  // 领取奖励
  MSG_L2C_TowerSeasonRecvAward = 17106;
  MSG_L2C_TowerSeasonUpdateTask = 17107;  // 更新任务进度
  MSG_C2L_TowerSeasonRankList = 17108;    // 排行榜  NOLOG
  MSG_L2C_TowerSeasonRankList = 17109;
  MSG_C2L_TowerSeasonRankLike = 17110;  // 排行榜点赞  NOLOG
  MSG_L2C_TowerSeasonRankLike = 17111;
  MSG_C2L_TowerSeasonCognitionLogs = 17112;  // 百塔通关记录（自己的） NOLOG
  MSG_L2C_TowerSeasonCognitionLogs = 17113;

  // 777抽活动
  MSG_MIN_GodPresent = 17200;
  MSG_C2L_GodPresentGetData = 17201;  // 获取数据 NOLOG
  MSG_L2C_GodPresentGetData = 17202;
  MSG_C2L_GodPresentRecvItem = 17203;  // 领取抽卡券
  MSG_L2C_GodPresentRecvItem = 17204;
  MSG_C2L_GodPresentSummon = 17205;  // 抽卡
  MSG_L2C_GodPresentSummon = 17206;
  MSG_C2L_GodPresentRecvAwards = 17207;  // 选定卡组，领取抽卡活动奖励
  MSG_L2C_GodPresentRecvAwards = 17208;

  // 掉落活动
  MSG_MIN_DropActivity = 17300;
  MSG_C2L_DropActivityMainInfo = 17301;  // 获取玩家数据 NOLOG
  MSG_L2C_DropActivityMainInfo = 17302;
  MSG_C2L_DropActivityExchange = 17303;  // 兑换
  MSG_L2C_DropActivityExchange = 17304;
  MSG_C2L_DropActivityGetActivity = 17305;  // 获取活动配置 NOLOG
  MSG_L2C_DropActivityGetActivity = 17306;
  MSG_L2C_DropActivityUpdateActivity = 17307;  // 活动配置更新
  MSG_C2L_DropActivityDailyReward = 17308;     // 获取每日奖励
  MSG_L2C_DropActivityDailyReward = 17309;

  // 累抽
  MSG_MIN_DailyAttendance = 17400;
  MSG_C2L_DailyAttendanceGetData = 17401;  // 获取数据 NOLOG
  MSG_L2C_DailyAttendanceGetData = 17402;
  MSG_C2L_DailyAttendanceRecvAward = 17403;  // 领奖 NOLOG
  MSG_L2C_DailyAttendanceRecvAward = 17404;
  MSG_L2C_DailyAttendanceUpdate = 17405;

  // 每日特惠
  MSG_MIN_DailySpecial = 17500;
  MSG_C2L_DailySpecialGetData = 17501;  // 获取数据 NOLOG
  MSG_L2C_DailySpecialGetData = 17502;
  MSG_C2L_DailySpecialRecvAward = 17503;  // 领奖  NOLOG
  MSG_L2C_DailySpecialRecvAward = 17504;

  // 公会宝箱
  MSG_MIN_GuildChest = 17600;
  MSG_C2L_GuildChestGetData = 17601;  // 获取数据 NOLOG
  MSG_L2C_GuildChestGetData = 17602;
  MSG_C2L_GuildChestRecv = 17603;  // 领取公会分享
  MSG_L2C_GuildChestRecv = 17604;
  MSG_C2L_GuildChestLike = 17605;  // 点赞公户分享
  MSG_L2C_GuildChestLike = 17606;
  MSG_C2L_GuildChestActivate = 17607;  // 激活公会分享
  MSG_L2C_GuildChestActivate = 17608;
  MSG_L2C_UserGuildChestItemNotify = 17609;  // 获得物品通知 NOLOG

  // 活动聚合页
  MSG_MIN_ActivityMix = 17700;
  MSG_C2L_ActivityMixGetData = 17701;
  MSG_L2C_ActivityMixGetData = 17702;

  // 世界boss
  MSG_MIN_WorldBoss = 17800;
  MSG_C2L_WorldBossGetData = 17801;  // 获取数据 NOLOG
  MSG_L2C_WorldBossGetData = 17802;
  MSG_C2L_WorldBossSelectLevel = 17803;  // 选择难度等级
  MSG_L2C_WorldBossSelectLevel = 17804;
  MSG_C2L_WorldBossRoomInfo = 17805;  // 房间信息 NOLOG
  MSG_L2C_WorldBossRoomInfo = 17806;
  MSG_C2L_WorldBossFight = 17807;  // 战斗: 挑战/扫荡
  MSG_L2C_WorldBossFight = 17808;
  MSG_C2L_WorldBossRecvAward = 17811;  // 领奖
  MSG_L2C_WorldBossRecvAward = 17812;
  MSG_C2L_WorldBossWorship = 17813;  // 膜拜
  MSG_L2C_WorldBossWorship = 17814;
  MSG_C2L_WorldBossRank = 17815;  // 排行榜
  MSG_L2C_WorldBossRank = 17816;
  MSG_C2L_WorldBossGetRoomLog = 17817;  // 获取房间日志 NOLOG
  MSG_L2C_WorldBossGetRoomLog = 17818;
  MSG_L2C_WorldBossTaskUpdate = 17819;  // 任务更新

  // 活动估时
  MSG_MIN_ActivityStory = 17900;
  MSG_C2L_ActivityStoryGetData = 17901;  // 获取信息
  MSG_L2C_ActivityStoryGetData = 17902;
  MSG_C2L_ActivityStoryLoginAward = 17903;  // 累登奖励
  MSG_L2C_ActivityStoryLoginAward = 17904;
  MSG_C2L_ActivityStoryExchange = 17905;  // 兑换
  MSG_L2C_ActivityStoryExchange = 17906;
  MSG_C2L_ActivityStoryFight = 17907;  // 活动主线战斗
  MSG_L2C_ActivityStoryFight = 17908;

  // 皮肤
  MSG_MIN_Skin = 18000;
  MSG_C2L_SkinList = 18001;  // 皮肤列表 NOLOG
  MSG_L2C_SkinList = 18002;
  MSG_C2L_SkinUse = 18003;  // 使用皮肤
  MSG_L2C_SkinUse = 18004;
  MSG_L2C_SkinNew = 18005;  // 推送最新皮肤信息

  // 赛季前助力
  MSG_MIN_AssistanceActivity = 18100;
  MSG_C2L_AssistanceActivityGetData = 18101;
  MSG_L2C_AssistanceActivityGetData = 18102;
  MSG_C2L_AssistanceActivityGetAward = 18103;
  MSG_L2C_AssistanceActivityGetAward = 18104;

  // 失序空间
  MSG_MIN_Disorderland = 18200;
  MSG_C2L_DisorderlandGetData = 18201;  // 获取数据   NOLOG
  MSG_L2C_DisorderlandGetData = 18202;
  MSG_C2L_DisorderlandTriggerEvent = 18203;  // 触发事件
  MSG_L2C_DisorderlandTriggerEvent = 18204;
  MSG_C2L_DisorderlandRank = 18205;  // 排行榜 NOLOG
  MSG_L2C_DisorderlandRank = 18206;
  MSG_C2L_DisorderlandBuyStamina = 18207;  // 购买体力
  MSG_L2C_DisorderlandBuyStamina = 18208;
  MSG_C2L_DisorderlandTestSweep = 18209;  // 测试扫荡
  MSG_L2C_DisorderlandTestSweep = 18210;

  // 赛季等级
  MSG_MIN_SeasonLevel = 18300;
  MSG_C2L_SeasonLevelGetData = 18301;  // 获取赛季等级数据 NOLOG
  MSG_L2C_SeasonLevelGetData = 18302;
  MSG_C2L_SeasonLevelUp = 18303;  // 升级 NOLOG
  MSG_L2C_SeasonLevelUp = 18304;
  MSG_C2L_SeasonLevelRecvTaskAwards = 18305;  // 领取任务奖励
  MSG_L2C_SeasonLevelRecvTaskAwards = 18306;
  MSG_C2L_SeasonLevelRecvLvAwards = 18307;  // 领取等级奖励
  MSG_L2C_SeasonLevelRecvLvAwards = 18308;
  MSG_L2C_SeasonLevelTaskUpdate = 18309;  // 同步任务进度

  // 永恒仪式
  MSG_MIN_Rite = 18400;
  MSG_C2L_RiteGetData = 18401;  // 获取信息
  MSG_L2C_RiteGetData = 18402;
  // MSG_C2L_RiteRareUp = 18403;  //激活/升品质
  // MSG_L2C_RiteRareUp = 18404;
  MSG_C2L_RiteTakeRareAwards = 18405;  // 领取品质奖励
  MSG_L2C_RiteTakeRareAwards = 18406;
  MSG_L2C_RiteChanged = 18421;  // 仪式变化

  // 赛季主线
  MSG_MIN_SeasonDungeon = 18500;
  MSG_C2L_SeasonDungeonGetData = 18501;
  MSG_L2C_SeasonDungeonGetData = 18502;
  MSG_C2L_SeasonDungeonFight = 18503;
  MSG_L2C_SeasonDungeonFight = 18504;
  MSG_C2L_SeasonDungeonRecvReward = 18505;
  MSG_L2C_SeasonDungeonRecvReward = 18506;
  MSG_L2C_SeasonDungeonUpdateTask = 18507;

  // 回流
  MSG_MIN_ActivityReturn = 18600;
  MSG_C2L_ActivityReturnGetData = 18601;
  MSG_L2C_ActivityReturnGetData = 18602;
  MSG_C2L_ActivityReturnTakeLoginAwards = 18603;
  MSG_L2C_ActivityReturnTakeLoginAwards = 18604;

  // 巅峰竞技场
  MSG_MIN_Peak = 18700;
  MSG_C2L_PeakBaseData = 18701;  // 基本信息 NOLOG
  MSG_L2C_PeakBaseData = 18702;
  MSG_C2L_PeakGetMatch = 18703;  // 获取比赛数据 NOLOG
  MSG_L2C_PeakGetMatch = 18704;
  MSG_C2L_PeakRecvInviteReward = 18705;  // 领取邀请奖励
  MSG_L2C_PeakRecvInviteReward = 18706;
  MSG_C2L_PeakFighterDetail = 18707;  // 选手完赛详情数据 NOLOG
  MSG_L2C_PeakFighterDetail = 18708;
  MSG_C2L_PeakGuessList = 18709;  // 竞猜列表 NOLOG
  MSG_L2C_PeakGuessList = 18710;
  MSG_C2L_PeakDoGuess = 18711;  // 竞猜下注
  MSG_L2C_PeakDoGuess = 18712;
  MSG_C2L_PeakRankList = 18713;  // 排行榜 NOLOG
  MSG_L2C_PeakRankList = 18714;
  MSG_L2C_PeakUpdateTip = 18715;  // 更新提示
  MSG_C2L_PeakWorship = 18716;    // 膜拜
  MSG_L2C_PeakWorship = 18717;
  MSG_C2L_PeakGetLastBattleReport = 18718;  // 获取上一场战报
  MSG_L2C_PeakGetLastBattleReport = 18719;

  // 赛季前奖励
  MSG_MIN_PreSeason = 18800;
  MSG_C2L_PreSeasonGetData = 18801;
  MSG_L2C_PreSeasonGetData = 18802;
  MSG_C2L_PreSeasonRecvAward = 18803;
  MSG_L2C_PreSeasonRecvAward = 18804;

  // 赛季回流
  MSG_MIN_SeasonReturn = 18900;
  MSG_C2L_SeasonReturnGetData = 18901;
  MSG_L2C_SeasonReturnGetData = 18902;
  MSG_C2L_SeasonReturnTakeAwards = 18903;
  MSG_L2C_SeasonReturnTakeAwards = 18904;

  // 小助手
  MSG_MIN_Assistant = 19000;
  MSG_C2L_AssistantGetData = 19001;
  MSG_L2C_AssistantGetData = 19002;

  // 赛季羁绊
  MSG_MIN_SeasonLink = 19100;
  MSG_C2L_SeasonLinkGetData = 19101;  // NOLOG
  MSG_L2C_SeasonLinkGetData = 19102;
  MSG_C2L_SeasonLinkActivate = 19103;
  MSG_L2C_SeasonLinkActivate = 19104;
  MSG_L2C_SeasonLinkMonumentChange = 19105;
  MSG_C2L_SeasonLinkMonumentTakeRareAwards = 19106;
  MSG_L2C_SeasonLinkMonumentTakeRareAwards = 19107;

  // 天赋树
  MSG_MIN_TalentTree = 19200;
  MSG_C2L_TalentTreeGetData = 19201;  // 获取数据
  MSG_L2C_TalentTreeGetData = 19202;
  MSG_C2L_TalentTreeLevelUp = 19203;  // 天赋树升级
  MSG_L2C_TalentTreeLevelUp = 19204;
  MSG_C2L_TalentTreeReset = 19205;  // 重置天赋树
  MSG_L2C_TalentTreeReset = 19206;
  MSG_C2L_TalentTreeReceiveTaskAwards = 19207;  // 领取任务奖励
  MSG_L2C_TalentTreeReceiveTaskAwards = 19208;
  MSG_L2C_TalentTreeTaskUpdate = 19209;
  MSG_C2L_TalentTreeHot = 19210;  // 天赋树养成热度
  MSG_L2C_TalentTreeHot = 19211;
  MSG_C2L_TalentTreePlanSave = 19212;
  MSG_L2C_TalentTreePlanSave = 19213;
  MSG_C2L_TalentTreePlanDelete = 19214;
  MSG_L2C_TalentTreePlanDelete = 19215;
  MSG_L2C_TalentTreeCultivateUpdate = 19216;

  // 公会战 funcID=258
  MSG_MIN_GuildSandTable = 25800;
  MSG_C2L_GSTGetData = 25801;  // 获取基础数据 NOLOG
  MSG_L2C_GSTGetData = 25802;
  MSG_C2L_GSTGetGroundData = 25803;  // 获取地块信息数据 NOLOG
  MSG_L2C_GSTGetGroundData = 25804;
  MSG_C2L_GSTGetTeamsData = 25805;  // 获取队伍数据 NOLOG
  MSG_L2C_GSTGetTeamsData = 25806;
  MSG_C2L_GSTGetLogData = 25807;  // 获取日志数据 NOLOG
  MSG_L2C_GSTGetLogData = 25808;
  MSG_C2L_GSTTeamOperate = 25809;  // 队伍操作.移动队伍,托管队伍 NOLOG
  MSG_L2C_GSTTeamOperate = 25810;
  MSG_C2L_GSTExchangeGroundTeam = 25811;  // 交换队伍出战顺序 NOLOG
  MSG_L2C_GSTExchangeGroundTeam = 25812;
  MSG_C2L_GSTRank = 25813;  // 排行榜 NOLOG
  MSG_L2C_GSTRank = 25814;
  MSG_C2L_GSTGetTasksData = 25815;  // 获取任务数据 NOLOG
  MSG_L2C_GSTGetTasksData = 25816;
  MSG_C2L_GSTGetTasksReward = 25817;  // 获取任务奖励 NOLOG
  MSG_L2C_GSTGetTasksReward = 25818;
  MSG_C2L_GSTGetHangUpReward = 25819;  // 放置奖励 NOLOG
  MSG_L2C_GSTGetHangUpReward = 25820;
  MSG_C2L_GSTDonate = 25821;  // 捐献祝福 NOLOG
  MSG_L2C_GSTDonate = 25822;
  MSG_C2L_GSTMessageEdit = 25823;  // 留言板编辑 NOLOG
  MSG_L2C_GSTMessageEdit = 25824;
  MSG_C2L_GSTPreviewHangUpReward = 25825;  // 预览放置奖励
  MSG_L2C_GSTPreviewHangUpReward = 25826;
  MSG_C2L_GSTGetGuildDonateData = 25827;  // 获取公会捐赠信息
  MSG_L2C_GSTGetGuildDonateData = 25828;
  MSG_C2L_GSTGetGuildDonateMemData = 25829;  // 公会捐赠成员数据
  MSG_L2C_GSTGetGuildDonateMemData = 25830;
  MSG_L2C_GSTTaskTypeUpdate = 25832;     // 公会战更新任务进度
  MSG_C2L_GSTGuildBuildGetData = 25835;  // 获取建筑信息
  MSG_L2C_GSTGuildBuildGetData = 25836;
  MSG_C2L_GSTGuildBuildDonate = 25837;  // 建筑捐赠
  MSG_L2C_GSTGuildBuildDonate = 25838;
  MSG_C2L_GSTGuildBuildDonateRank = 25839;  // 获取建捐赠排行榜
  MSG_L2C_GSTGuildBuildDonateRank = 25840;
  MSG_C2L_GSTGuildBuildDispatchHero = 25841;  // 公会建筑派遣英雄
  MSG_L2C_GSTGuildBuildDispatchHero = 25842;
  MSG_C2L_GSTGuildBuildGetTaskData = 25843;  // 获取捐赠任务
  MSG_L2C_GSTGuildBuildGetTaskData = 25844;
  MSG_C2L_GSTGuildBuildRecvTaskAward = 25845;  // 获取任务奖励
  MSG_L2C_GSTGuildBuildRecvTaskAward = 25846;
  MSG_L2C_GSTGuildBuildTaskUpdate = 25847;  // 建筑任务推送
  MSG_C2L_GSTGroupUsersRank = 25848;        // 小组内玩家排行榜
  MSG_L2C_GSTGroupUsersRank = 25849;
  MSG_C2L_GSTGroupUsersRankLike = 25850;  // 排行榜点赞
  MSG_L2C_GSTGroupUsersRankLike = 25851;
  MSG_C2L_GSTArenaVote = 25852;  // 擂台投票
  MSG_L2C_GSTArenaVote = 25853;
  MSG_C2L_GSTGetArenaVoteRecord = 25854;  // 获取投票记录
  MSG_L2C_GSTGetArenaVoteRecord = 25855;
  MSG_C2L_GSTBossGet = 25856;  // 获取boss信息
  MSG_L2C_GSTBossGet = 25857;
  MSG_C2L_GSTBossFight = 25858;  // boss战斗
  MSG_L2C_GSTBossFight = 25859;
  MSG_C2L_GSTBossAward = 25860;  // boss领奖
  MSG_L2C_GSTBossAward = 25861;
  MSG_C2L_GSTBossRank = 25862;  // boss排行
  MSG_L2C_GSTBossRank = 25863;
  MSG_C2L_GSTBossBuyChallenge = 25864;  // 购买挑战令
  MSG_L2C_GSTBossBuyChallenge = 25865;
  MSG_C2L_GSTScorePreview = 25866;  // 下回合积分预览
  MSG_L2C_GSTScorePreview = 25867;
  MSG_C2L_GuildMobilizationGuildRank = 25868;  //  公会竞赛公会排行(展示期走日志)
  MSG_L2C_GuildMobilizationGuildRank = 25869;
  MSG_L2C_GSTPushSta = 25898;  // 推送前端跨服战状态改变
  MSG_MAX_GuildSandTable = 25899;

  // 剧情回忆录
  MSG_MIN_StoryReview = 25900;
  MSG_C2L_StoryReviewGetData = 25901;  // NOLOG
  MSG_L2C_StoryReviewGetData = 25902;
  MSG_C2L_StoryReviewUnlock = 25903;
  MSG_L2C_StoryReviewUnlock = 25904;

  // 设置推送标识
  MSG_MIN_Push = 26000;
  MSG_C2L_SetPush = 26001;  // 设置推送标识 NOLOG
  MSG_L2C_SetPush = 26002;
  MSG_C2L_GetPush = 26003;  // 获取推送标识 NOLOG
  MSG_L2C_GetPush = 26004;

  MSG_MIN_NewYearActivity = 27000;
  MSG_C2L_NewYearActivityGetData = 27001;
  MSG_L2C_NewYearActivityGetData = 27002;
  MSG_C2L_NewYearActivityLoginAward = 27003;
  MSG_L2C_NewYearActivityLoginAward = 27004;

  // 金字塔活动
  MSG_MIN_Pyramid = 27100;
  MSG_C2L_PyramidGetData = 27101;  // NOLOG
  MSG_L2C_PyramidGetData = 27102;
  MSG_C2L_PyramidChooseAward = 27103;
  MSG_L2C_PyramidChooseAward = 27104;
  MSG_C2L_PyramidDraw = 27105;
  MSG_L2C_PyramidDraw = 27106;
  MSG_C2L_PyramidReceiveAwards = 27107;
  MSG_L2C_PyramidReceiveAwards = 27108;
  MSG_L2C_PyramidUpdateTask = 27109;
  MSG_C2L_PyramidTestDraw = 27110;  // NOLOG
  MSG_L2C_PyramidTestDraw = 27111;
  MSG_L2C_PyramidUpdateActivity = 27112;

  // 遗物系统
  MSG_MIN_Remain = 27200;
  MSG_C2L_RemainGetData = 27201;  // NOLOG
  MSG_L2C_RemainGetData = 27202;
  MSG_L2C_RemainUseItemsNotify = 27203;  // 使用道具通知
  MSG_C2L_RemainBookRecvExp = 27204;     // 遗物图鉴接收经验
  MSG_L2C_RemainBookRecvExp = 27205;
  MSG_C2L_RemainBookLevelUp = 27206;  // 遗物图鉴升级
  MSG_L2C_RemainBookLevelUp = 27207;
  MSG_L2C_RemainUpdate = 27208;  // 遗物升星通知

  // 赛季竞技场
  MSG_MIN_SeasonArena = 27300;
  MSG_C2L_SeasonArenaGetData = 27301;  // 获取基本信息
  MSG_L2C_SeasonArenaGetData = 27302;
  MSG_C2L_SeasonArenaRefresh = 27303;  // 刷新对手
  MSG_L2C_SeasonArenaRefresh = 27304;
  MSG_C2L_SeasonArenaFight = 27305;  // 战斗
  MSG_L2C_SeasonArenaFight = 27306;
  MSG_C2L_SeasonArenaLogList = 27307;  // 战报信息 NOLOG
  MSG_L2C_SeasonArenaLogList = 27308;
  MSG_C2L_SeasonArenaOfFame = 27309;  // 荣耀排行榜
  MSG_L2C_SeasonArenaOfFame = 27310;
  MSG_C2L_SeasonArenaDivisionAward = 27311;  // 领取段位奖励
  MSG_L2C_SeasonArenaDivisionAward = 27312;
  MSG_C2L_SeasonArenaTaskAward = 27313;  // 赛季竞技场任务奖励
  MSG_L2C_SeasonArenaTaskAward = 27314;
  MSG_C2L_SeasonArenaGetRankList = 27315;  // 获取排行榜数据
  MSG_L2C_SeasonArenaGetRankList = 27316;
  MSG_L2C_SeasonArenaState = 27317;              // 获取赛季竞技场状态
  MSG_L2C_SeasonArenaTaskUpdate = 27318;         // 更新任务进度
  MSG_C2L_SeasonArenaBuyChallengeCount = 27319;  // 购买挑战次数
  MSG_L2C_SeasonArenaBuyChallengeCount = 27320;
  MSG_C2L_SeasonArenaUseTicket = 27321;  // 使用门票
  MSG_L2C_SeasonArenaUseTicket = 27322;

  // 开箱子
  MSG_MIN_Box = 27400;
  MSG_C2L_BoxGet = 27401;  // 获取开宝箱数据 NOLOG
  MSG_L2C_BoxGet = 27402;
  MSG_C2L_BoxOpen = 27403;  // 箱子开启
  MSG_L2C_BoxOpen = 27404;
  MSG_C2L_BoxExchange = 27405;  // 箱子兑换
  MSG_L2C_BoxExchange = 27406;

  // 周年庆
  MSG_MIN_ActivityTurnTable = 27500;
  MSG_C2L_ActivityTurnTableGetData = 27501;
  MSG_L2C_ActivityTurnTableGetData = 27502;
  MSG_C2L_ActivityTurnTableSummon = 27503;
  MSG_L2C_ActivityTurnTableSummon = 27504;
  MSG_C2L_ActivityTurnTableRecvTask = 27505;
  MSG_L2C_ActivityTurnTableRecvTask = 27506;
  MSG_C2L_ActivityTurnTableRecvLogin = 27507;
  MSG_L2C_ActivityTurnTableRecvLogin = 27508;
  MSG_L2C_ActivityTurnTableUpdateTask = 27509;
  MSG_C2L_ActivityTurnTableSelectBuff = 27510;
  MSG_L2C_ActivityTurnTableSelectBuff = 27511;
  MSG_C2L_ActivityTurnTableTicketBuy = 27512;
  MSG_L2C_ActivityTurnTableTicketBuy = 27513;

  // 公会战龙战
  MSG_MIN_GuildSandTableDragon = 27600;
  MSG_C2L_GSTDragonGetData = 27601;  // 获取龙战数据 NOLOG
  MSG_L2C_GSTDragonGetData = 27602;
  MSG_C2L_GSTDragonGetCultivation = 27603;  // 获取龙战数据 NOLOG
  MSG_L2C_GSTDragonGetCultivation = 27604;
  MSG_C2L_GSTDragonShow = 27605;  // 修改龙展示
  MSG_L2C_GSTDragonShow = 27606;
  MSG_C2L_GSTDragonEvolve = 27607;  // 龙进化
  MSG_L2C_GSTDragonEvolve = 27608;
  MSG_C2L_GSTDragonFight = 27609;  // 龙战战斗
  MSG_L2C_GSTDragonFight = 27610;
  MSG_C2L_GSTDragonFightBuyCount = 27611;  // 龙战钻石购买挑战次数
  MSG_L2C_GSTDragonFightBuyCount = 27612;
  MSG_C2L_GSTDragonFightUseTicket = 27613;  // 龙战道具购买挑战次数
  MSG_L2C_GSTDragonFightUseTicket = 27614;
  MSG_C2L_GSTDragonSkillOperate = 27615;  // 龙战攻城技能
  MSG_L2C_GSTDragonSkillOperate = 27616;
  MSG_C2L_GSTDragonStrategySkill = 27617;  // 龙战祝福技能
  MSG_L2C_GSTDragonStrategySkill = 27618;
  MSG_C2L_GSTDragonTaskGetData = 27619;  // 龙战任务获取信息
  MSG_L2C_GSTDragonTaskGetData = 27620;
  MSG_C2L_GSTDragonTaskAward = 27621;  // 龙战任务领奖
  MSG_L2C_GSTDragonTaskAward = 27622;
  MSG_L2C_GSTDragonTaskUpdate = 27623;  // 龙战任务推送
  MSG_C2L_GSTDragonRank = 27624;        // 公会玩家排行
  MSG_L2C_GSTDragonRank = 27625;

  // 热度榜
  MSG_MIN_HotRank = 27700;
  MSG_C2L_HotRankGet = 27701;
  MSG_L2C_HotRankGet = 27702;

  // 冲榜活动 - 国服
  MSG_MIN_ActivityCompliance = 27800;
  MSG_C2L_ActivityComplianceGetData = 27801;
  MSG_L2C_ActivityComplianceGetData = 27802;
  MSG_C2L_ActivityComplianceRecvAward = 27803;
  MSG_L2C_ActivityComplianceRecvAward = 27804;
  MSG_L2C_ActivityComplianceScoreUpdate = 27805;
  MSG_C2L_ActivityComplianceLikeRank = 27806;
  MSG_L2C_ActivityComplianceLikeRank = 27807;

  // 公会竞赛
  MSG_MIN_GuildMobilization = 27900;
  MSG_C2L_GuildMobilizationGetData = 27901;
  MSG_L2C_GuildMobilizationGetData = 27902;
  MSG_C2L_GuildMobilizationAcceptTask = 27903;  // 接取任务
  MSG_L2C_GuildMobilizationAcceptTask = 27904;
  MSG_C2L_GuildMobilizationSignTask = 27905;  // 标记任务
  MSG_L2C_GuildMobilizationSignTask = 27906;
  MSG_C2L_GuildMobilizationFinishTaskLogs = 27907;  //  积分日志
  MSG_L2C_GuildMobilizationFinishTaskLogs = 27908;
  MSG_C2L_GuildMobilizationPersonalRank = 27909;  //  个人排行
  MSG_L2C_GuildMobilizationPersonalRank = 27910;
  MSG_C2L_GuildMobilizationRecvScoreLevel = 27911;  // 领取积分等级奖励
  MSG_L2C_GuildMobilizationRecvScoreLevel = 27912;
  MSG_C2L_GuildMobilizationFreshTask = 27913;  // 刷新任务
  MSG_L2C_GuildMobilizationFreshTask = 27914;
  MSG_C2L_GuildMobilizationBuyTimes = 27915;  // 购买次数
  MSG_L2C_GuildMobilizationBuyTimes = 27916;
  MSG_C2L_GuildMobilizationEditMessageBoard = 27917;  // 编辑公告
  MSG_L2C_GuildMobilizationEditMessageBoard = 27918;
  MSG_C2L_GuildMobilizationGiveUpTask = 27919;  // 放弃任务
  MSG_L2C_GuildMobilizationGiveUpTask = 27920;
  MSG_C2L_GuildMobilizationScoreAward = 27921;  // 积分领奖
  MSG_L2C_GuildMobilizationScoreAward = 27922;
  MSG_C2L_GuildMobilizationCancelTask = 27923;  // 公会竞赛取消任务
  MSG_L2C_GuildMobilizationCancelTask = 27924;
  MSG_L2C_GuildMobilizationUpdateTaskProgress = 27999;  // 任务更新通知

  // 活动日历
  MSG_MIN_ActivitySchedule = 28000;
  MSG_C2L_ActivityScheduleGetData = 28001;
  MSG_L2C_ActivityScheduleGetData = 28002;

  // Boss挑战
  MSG_MIN_BossRush = 28100;
  MSG_C2L_BossRushGetData = 28101;  // 获取Boss挑战数据 NOLOG
  MSG_L2C_BossRushGetData = 28102;
  MSG_C2L_BossRushFight = 28103;  // Boss挑战战斗
  MSG_L2C_BossRushFight = 28104;
  MSG_C2L_BossRushTaskAward = 28105;  // Boss挑战任务领奖
  MSG_L2C_BossRushTaskAward = 28106;
  MSG_L2C_BossRushTaskUpdate = 28107;  // Boss挑战任务进度推送
  MSG_C2L_BossRushBuyStamina = 28108;  // Boss挑战购买体力
  MSG_L2C_BossRushBuyStamina = 28109;

  // 活动聚合
  MSG_MIN_ActivitySum = 28200;
  MSG_C2L_ActivitySumGetData = 28201;
  MSG_L2C_ActivitySumGetData = 28202;
  MSG_C2L_ActivitySumLoginReward = 28203;
  MSG_L2C_ActivitySumLoginReward = 28204;
  MSG_C2L_ActivitySumTaskReward = 28205;
  MSG_L2C_ActivitySumTaskReward = 28206;
  MSG_C2L_ActivitySumActivePuzzleCell = 28207;
  MSG_L2C_ActivitySumActivePuzzleCell = 28208;
  MSG_L2C_ActivitySumTaskUpdate = 28209;
  MSG_C2L_ActivitySumTicketBuy = 28210;
  MSG_L2C_ActivitySumTicketBuy = 28211;
  MSG_C2L_ActivitySumExchange = 28212;
  MSG_L2C_ActivitySumExchange = 28213;
  MSG_C2L_ActivitySumTurnTableSummon = 28214;
  MSG_L2C_ActivitySumTurnTableSummon = 28215;
  MSG_C2L_ActivitySumTurnTableSelectBuff = 28216;
  MSG_L2C_ActivitySumTurnTableSelectBuff = 28217;
  MSG_C2L_ActivitySumMakeGift = 28218;
  MSG_L2C_ActivitySumMakeGift = 28219;
  MSG_C2L_ActivitySumFeed = 28220;
  MSG_L2C_ActivitySumFeed = 28221;
  MSG_C2L_ActivitySumShootGameFight = 28222;
  MSG_L2C_ActivitySumShootGameFight = 28223;
  MSG_C2L_ActivitySumSynthesisGameStart = 28224;  // 合成小游戏消耗挑战次数
  MSG_L2C_ActivitySumSynthesisGameStart = 28225;
  MSG_C2L_ActivitySumSynthesisGameUseItem = 28226;  // 合成小游戏使用道具
  MSG_L2C_ActivitySumSynthesisGameUseItem = 28227;
  MSG_C2L_ActivitySumSynthesisGameUpdate = 28228;  // 合成小游戏数据更新
  MSG_L2C_ActivitySumSynthesisGameUpdate = 28229;
  MSG_C2L_ActivitySumSynthesisGameBuyItem = 28230;  // 合成小游戏购买道具
  MSG_L2C_ActivitySumSynthesisGameBuyItem = 28231;

  // 玩家切磋
  MSG_MIN_Duel = 28300;
  // MSG_C2L_DuelGetData = 28301;  // 获取切磋数据 NOLOG
  // MSG_L2C_DuelGetData = 28302;
  MSG_C2L_DuelSetStatus = 28303;  // 切磋设置状态
  MSG_L2C_DuelSetStatus = 28304;
  MSG_C2L_DuelFight = 28305;  // 切磋战斗
  MSG_L2C_DuelFight = 28306;

  // 选英雄抽卡
  MSG_MIN_SelectSummon = 28400;
  MSG_C2L_SelectSummonGetOpenActivity = 28401;  // 获取开放的活动
  MSG_L2C_SelectSummonGetOpenActivity = 28402;
  MSG_C2L_SelectSummonSummon = 28403;  // 英雄抽卡
  MSG_L2C_SelectSummonSummon = 28404;
  MSG_L2C_SelectSummonUpdate = 28405;  // 活动配置更新，推送给前端，让前端拉取最新的数据
  MSG_C2L_SelectSummonTestSummon = 28406;
  MSG_L2C_SelectSummonTestSummon = 28407;

  // 称号
  MSG_MIN_Title = 28500;
  MSG_C2L_TitleList = 28501;  // 称号列表
  MSG_L2C_TitleList = 28502;
  MSG_C2L_TitleUse = 28503;  // 使用称号
  MSG_L2C_TitleUse = 28504;
  MSG_L2C_TitleNew = 28505;  // 推送最新称号信息

  // 公会战-占矿
  MSG_MIN_GuildSandTableOre = 33000;
  MSG_C2L_GSTOreGetData = 33001;  // 获取数据
  MSG_L2C_GSTOreGetData = 33002;
  MSG_C2L_GSTOreFight = 33003;  // 打矿
  MSG_L2C_GSTOreFight = 33004;
  MSG_C2L_GSTOreBuyTimes = 33005;  // 购买次数
  MSG_L2C_GSTOreBuyTimes = 33006;
  MSG_C2L_GSTOreSearchAssist = 33007;  // 搜寻可协助的矿
  MSG_L2C_GSTOreSearchAssist = 33008;
  MSG_C2L_GSTOreOccupy = 33009;  // 改变矿占领状态
  MSG_L2C_GSTOreOccupy = 33010;
  MSG_C2L_GSTOreGetOreData = 33011;  // 获取指定矿数据
  MSG_L2C_GSTOreGetOreData = 33012;

  // 公会战-科技
  MSG_MIN_GuildSandTableTech = 33100;
  MSG_C2L_GSTTechGetData = 33101;  // 获取数据
  MSG_L2C_GSTTechGetData = 33102;
  MSG_C2L_GSTTechDonate = 33103;  // 捐献资源
  MSG_L2C_GSTTechDonate = 33104;
  MSG_C2L_GSTTechTaskReward = 33107;
  MSG_L2C_GSTTechTaskReward = 33108;
  MSG_C2L_GSTTechGuildUserRank = 33109;
  MSG_L2C_GSTTechGuildUserRank = 33110;
  MSG_C2L_GSTSkillAssemble = 33111;  // 号令集结技能
  MSG_L2C_GSTSkillAssemble = 33112;
  MSG_C2L_GSTTechSign = 33113;  // 标记科技
  MSG_L2C_GSTTechSign = 33114;
  MSG_L2C_GSTTechTaskUpdate = 33199;

  // 终身礼包
  MSG_MIN_ActivityLifelongGift = 33200;
  MSG_L2C_ActivityLifelongGiftUpdate = 33201;

  // 赛季启动
  MSG_MIN_SeasonStart = 33300;
  MSG_C2L_SeasonStartTowerRankList = 33301;  // 排行榜  NOLOG
  MSG_L2C_SeasonStartTowerRankList = 33302;
  MSG_C2L_SeasonStartTowerRankLike = 33303;  // 排行榜点赞  NOLOG
  MSG_L2C_SeasonStartTowerRankLike = 33304;

  // 公会战-新擂台战
  MSG_MIN_GuildSandTableChallenge = 33400;
  MSG_C2L_GSTChallengeGetData = 33401;  // 获取数据
  MSG_L2C_GSTChallengeGetData = 33402;
  MSG_C2L_GSTChallengeMatch = 33403;  // 获取匹配数据
  MSG_L2C_GSTChallengeMatch = 33404;
  MSG_C2L_GSTChallengeFight = 33405;  // 战斗
  MSG_L2C_GSTChallengeFight = 33406;
  MSG_C2L_GSTChallengeBuffChoose = 33407;  // buff选择
  MSG_L2C_GSTChallengeBuffChoose = 33408;
  MSG_C2L_GSTChallengeTaskReward = 33409;  // 任务奖励
  MSG_L2C_GSTChallengeTaskReward = 33410;
  MSG_L2C_GSTChallengeTaskUpdate = 33411;
  MSG_C2L_GSTChallengeRank = 33412;  // 排行榜
  MSG_L2C_GSTChallengeRank = 33413;
  MSG_C2L_GSTChallengeFightLogList = 33414;  // 战报信息 NOLOG
  MSG_L2C_GSTChallengeFightLogList = 33415;

  // 赛季冲榜
  MSG_MIN_SeasonCompliance = 33500;
  MSG_C2L_SeasonComplianceGetData = 33501;
  MSG_L2C_SeasonComplianceGetData = 33502;
  MSG_C2L_SeasonComplianceRecvReward = 33503;
  MSG_L2C_SeasonComplianceRecvReward = 33504;
  MSG_C2L_SeasonComplianceList = 33505;
  MSG_L2C_SeasonComplianceList = 33506;
  MSG_L2C_SeasonComplianceScoreChange = 33507;

  // 赛季开门玩法
  MSG_MIN_SeasonDoor = 33600;
  MSG_C2L_SeasonDoorGetData = 33601;  // 获取数据
  MSG_L2C_SeasonDoorGetData = 33602;
  MSG_C2L_SeasonDoorFightLine = 33603;  // 请求战斗队列
  MSG_L2C_SeasonDoorFightLine = 33604;
  MSG_C2L_SeasonDoorTaskReward = 33605;  // 任务奖励
  MSG_L2C_SeasonDoorTaskReward = 33606;
  MSG_C2L_SeasonDoorFight = 33607;  // 请求战斗
  MSG_L2C_SeasonDoorFight = 33608;
  MSG_C2L_SeasonDoorFightLineReward = 33609;  // 领取战斗队列奖励
  MSG_L2C_SeasonDoorFightLineReward = 33610;
  MSG_C2L_SeasonDoorLog = 33611;  // 日志
  MSG_L2C_SeasonDoorLog = 33612;
  MSG_C2L_SeasonDoorFightLineViewReward = 33613;  // 查看挂机奖励
  MSG_L2C_SeasonDoorFightLineViewReward = 33614;
  MSG_L2C_SeasonDoorTaskUpdate = 33699;  // 任务更新通知

  // 赛季装备
  MSG_MIN_SeasonJewelry = 33700;
  MSG_C2L_SeasonJewelryGetData = 33701;  // 获取赛季装备信息 NOLOG
  MSG_L2C_SeasonJewelryGetData = 33702;
  MSG_C2L_SeasonJewelryWear = 33703;  // 穿卸
  MSG_L2C_SeasonJewelryWear = 33704;
  MSG_C2L_SeasonJewelryLock = 33705;  // 锁定
  MSG_L2C_SeasonJewelryLock = 33706;
  MSG_C2L_SeasonJewelryDecompose = 33707;  // 分解
  MSG_L2C_SeasonJewelryDecompose = 33708;
  MSG_C2L_SeasonJewelrySetAutoDecompose = 33709;  // 设置自动分解
  MSG_L2C_SeasonJewelrySetAutoDecompose = 33710;
  MSG_L2C_SeasonJewelryUpdate = 33711;        // 状态推送
  MSG_C2L_SeasonJewelrySkillLevelUp = 33712;  // 词条升级
  MSG_L2C_SeasonJewelrySkillLevelUp = 33713;
  MSG_C2L_SeasonJewelrySkillClassUp = 33714;  // 词条升阶
  MSG_L2C_SeasonJewelrySkillClassUp = 33715;
  MSG_C2L_SeasonJewelrySkillChange = 33716;  // 词条洗炼
  MSG_L2C_SeasonJewelrySkillChange = 33717;
  MSG_C2L_SeasonJewelrySkillChangeConfirm = 33718;  // 词条洗炼确认
  MSG_L2C_SeasonJewelrySkillChangeConfirm = 33719;
  MSG_C2L_SeasonJewelryTestReRollSkill = 33720;  // 测试协议：重随目标稀有度词条
  MSG_L2C_SeasonJewelryTestReRollSkill = 33721;

  // 赛季商店
  MSG_MIN_SeasonShop = 33800;
  MSG_C2L_SeasonShopGetData = 33801;  // 获取商店信息 NOLOG
  MSG_L2C_SeasonShopGetData = 33802;
  MSG_C2L_SeasonShopBuy = 33803;  // 购买商品
  MSG_L2C_SeasonShopBuy = 33804;

  // 冲榜领奖
  MSG_MIN_ComplianceTasks = 33900;
  MSG_C2L_ComplianceTasksGetData = 33901;
  MSG_L2C_ComplianceTasksGetData = 33902;
  MSG_C2L_ComplianceTasksRecvTask = 33903;
  MSG_L2C_ComplianceTasksRecvTask = 33904;
  MSG_L2C_ComplianceTasksUpdate = 33905;

  // 代金券活动
  MSG_MIN_ActivityCoupon = 34000;
  MSG_C2L_ActivityCouponGetData = 34001;
  MSG_L2C_ActivityCouponGetData = 34002;
  MSG_C2L_ActivityCouponBuy = 34003;
  MSG_L2C_ActivityCouponBuy = 34004;
  MSG_L2C_ActivityCouponXmlUpdate = 34005;

  MSG_MIN_DailyAttendanceHero = 35000;
  MSG_C2L_DailyAttendanceHeroGetData = 35001;
  MSG_L2C_DailyAttendanceHeroGetData = 35002;
  MSG_C2L_DailyAttendanceHeroRecvAward = 35003;
  MSG_L2C_DailyAttendanceHeroRecvAward = 35004;

  // 赛季地图玩法
  MSG_MIN_SeasonMap = 34300;
  MSG_C2L_SeasonMapGetData = 34301;  // 获取数据
  MSG_L2C_SeasonMapGetData = 34302;
  MSG_C2L_SeasonMapFight = 34303;  // 请求战斗
  MSG_L2C_SeasonMapFight = 34304;
  MSG_C2L_SeasonMapMovePosition = 34305;  // 移动到指定点
  MSG_L2C_SeasonMapMovePosition = 34306;
  MSG_C2L_SeasonMapTaskReward = 34307;  // 任务奖励
  MSG_L2C_SeasonMapTaskReward = 34308;
  MSG_C2L_SeasonMapDialogue = 34309;  // 剧情
  MSG_L2C_SeasonMapDialogue = 34310;
  MSG_C2L_SeasonMapTrade = 34311;  // 交易
  MSG_L2C_SeasonMapTrade = 34312;
  MSG_C2L_SeasonMapMaster = 34313;  // 大师
  MSG_L2C_SeasonMapMaster = 34314;
  MSG_C2L_SeasonMapAltar = 34315;  // 祭坛
  MSG_L2C_SeasonMapAltar = 34316;
  MSG_C2L_SeasonMapPositionLogs = 34321;  // 位置日志
  MSG_L2C_SeasonMapPositionLogs = 34322;
  MSG_C2L_SeasonMapGetRankList = 34325;  // 获取排行榜
  MSG_L2C_SeasonMapGetRankList = 34326;
  MSG_C2L_SeasonMapBuyStamina = 34327;  // 购买体力
  MSG_L2C_SeasonMapBuyStamina = 34328;
  MSG_C2L_SeasonMapPassPosition = 34395;  // 测试协议
  MSG_L2C_SeasonMapPassPosition = 34396;
  MSG_L2C_SeasonMapRecoverTimeUpdate = 34398;  // 恢复时间更新通知
  MSG_L2C_SeasonMapTaskUpdate = 34399;         // 任务更新通知

  // 宠物
  MSG_MIN_Pokemon = 34400;
  MSG_C2L_PokemonGetData = 34401;
  MSG_L2C_PokemonGetData = 34402;
  MSG_C2L_PokemonSetBall = 34403;
  MSG_L2C_PokemonSetBall = 34404;
  MSG_C2L_PokemonActivate = 34405;
  MSG_L2C_PokemonActivate = 34406;
  MSG_C2L_PokemonStarUp = 34407;
  MSG_L2C_PokemonStarUp = 34408;
  MSG_C2L_PokemonMasterReward = 34409;
  MSG_L2C_PokemonMasterReward = 34410;
  MSG_C2L_PokemonPotentialLevelUp = 34411;
  MSG_L2C_PokemonPotentialLevelUp = 34412;
  MSG_C2L_PokemonSetShow = 34413;
  MSG_L2C_PokemonSetShow = 34414;

  // 宠物抽卡
  MSG_MIN_PokemonSummon = 34500;
  MSG_C2L_PokemonSummonGetOpenActivity = 34501;  // 获取开放的活动
  MSG_L2C_PokemonSummonGetOpenActivity = 34502;
  MSG_C2L_PokemonSummonSummon = 34503;  // 英雄抽卡
  MSG_L2C_PokemonSummonSummon = 34504;
  MSG_L2C_PokemonSummonUpdate = 34505;  // 活动配置更新，推送给前端，让前端拉取最新的数据
  MSG_C2L_PokemonSummonTestSummon = 34506;
  MSG_L2C_PokemonSummonTestSummon = 34507;

  // 宠物爬塔
  MSG_MIN_TowerPokemon = 35200;
  MSG_C2L_TowerPokemonGetData = 35201;  // 获取数据 NOLOG
  MSG_L2C_TowerPokemonGetData = 35202;
  MSG_C2L_TowerPokemonFight = 35203;  // 战斗
  MSG_L2C_TowerPokemonFight = 35204;
  MSG_C2L_TowerPokemonRecvAward = 35205;  // 领取奖励
  MSG_L2C_TowerPokemonRecvAward = 35206;
  MSG_C2L_TowerPokemonBuyGoods = 35207;  // 购买物品
  MSG_L2C_TowerPokemonBuyGoods = 35208;
  MSG_L2C_TowerPokemonUpdateTask = 35299;  // 更新任务进度

  // 公平竞技场
  MSG_MIN_BalanceArena = 35300;
  MSG_C2L_BalanceArenaGetData = 35301;  // 获取基本信息
  MSG_L2C_BalanceArenaGetData = 35302;
  MSG_C2L_BalanceArenaCardDraw = 35303;  // 抽牌
  MSG_L2C_BalanceArenaCardDraw = 35304;
  MSG_C2L_BalanceArenasCardChoose = 35305;  // 选牌
  MSG_L2C_BalanceArenaCardChoose = 35306;
  MSG_C2L_BalanceArenaCardCustomize = 35307;  // 自选
  MSG_L2C_BalanceArenaCardCustomize = 35308;
  MSG_C2L_BalanceArenaTeamUp = 35309;  // 组队（可报名）
  MSG_L2C_BalanceArenaTeamUp = 35310;
  MSG_C2L_BalanceArenaGetRankList = 35397;  // 获取排行榜数据
  MSG_L2C_BalanceArenaGetRankList = 35398;
  MSG_L2C_BalanceArenaPushState = 35399;  // 状态推送
}

message UserSnapshot {
  uint64 id = 1;
  string name = 2;
  uint32 level = 3;
  uint32 career = 4;
  uint32 base_id = 5;  // 头像相关
  uint32 star = 6;
  int64 power = 7;                        // 战力
  int64 hp = 8;                           // 血量
  int64 offline_time = 9;                 // 离线时间
  bool online = 10;                       // 在线
  int64 create_time = 11;                 // 创角时间戳
  int64 arena_power = 12;                 // 竞技场防守战力
  uint64 guild_id = 13;                   // 公会id
  string guild_name = 14;                 // 公会名字
  int64 max_power = 15;                   // 最高战力
  int64 forest_power = 16;                // 密林防守战力
  uint32 vip = 17;                        // vip
  repeated uint64 top5_heros = 18;        // top5hero
  int64 defense_power = 19;               // 防守战力
  uint64 sid = 20;                        // 服务器ID
  uint32 show_hero = 21;                  // 战力最高英雄sysID
  repeated int64 expire_time = 22;        // 0:头像,1:头像框过期时间,2:形象过期时间,3:聊天汽包,4:称号过期时间
  int64 crystal_power = 23;               // 水晶总战力
  uint32 area_id = 24;                    // 战区ID
  uint32 image = 25;                      // 形象ID
  uint32 season_lv = 26;                  // 赛季等级
  int64 season_power = 27;                // 赛季战力
  uint32 season_id = 28;                  // 赛季ID
  uint32 peak_score = 29;                 // 巅峰竞技场赛季积分
  uint32 peak_rank = 30;                  // 巅峰竞技场赛季排名
  int64 season_top_power = 31;            // 赛季最高战力
  int64 season_crystal_top35_power = 32;  // 赛季水晶最高的35名英雄
  uint32 title = 33;                      // 称号
  uint32 pokemon_image = 34;              // 宠物形象ID
}

message MiniUserSnapshot {
  uint64 id = 1;
  string name = 2;
  uint32 level = 3;
  uint32 base_id = 4;              // 头像相关
  int64 power = 5;                 // 战力
  repeated int64 expire_time = 6;  // 0:头像,1:头像框过期时间
  uint64 sid = 7;                  // 服务器ID
  uint32 vip = 8;
  int64 cur_formation_power = 9;  // 当前阵容战力
  uint32 title = 10;              // 称号
}

message User {
  uint64 id = 1;
  string name = 2;
  uint32 level = 3;
  uint32 career = 4;
  uint32 base_id = 5;                     // 头像相关
  uint64 diamond = 6;                     // 钻石
  uint64 gold = 7;                        // 元宝
  uint64 exp = 8;                         // 修为
  uint32 energy = 9;                      // 体力
  uint32 energy_tm = 10;                  // 上次体力结算时间
  int64 create_time = 11;                 // 创角时间戳
  map<uint32, uint32> purchase_num = 12;  // 玩法通用购买次数
  repeated uint32 face = 13;              // 捏脸
  int64 daily_play_limit = 14;            // 防沉迷游戏限制 未成年玩家时长到点的时间戳 成年玩家为0
  uint32 hero_slot = 15;                  // 英雄栏位数量
  map<uint32, NumInfo> num_info = 16;     // 玩法通用次数信息
  int64 power = 17;                       // 战力
  uint64 guild_id = 18;                   // 公会id
  string guild_name = 19;                 // 公会名字
  uint32 vip = 20;                        // vip等级
  uint32 vip_exp = 21;                    // vip经验
  uint32 hero_star_limit = 22;            // 英雄升星限制等级
  string lang = 23;                       // 前端语言
  uint32 recharge_amount = 24;            // 充值总额
  repeated uint64 top5_heros = 25;        // top5英雄id
  int64 max_power = 26;                   // 最高战力
  repeated int64 expire_time = 27;        // 0:头像,1:头像框过期时间,2:形象过期时间,3:聊天气泡ID,4:称号
  int64 crystal_power = 28;               // 水晶总战力
  uint32 area_id = 29;                    // 战区ID
  int64 offline_time = 30;                // 离线时间
  uint32 image = 31;                      // 形象ID
  uint32 emblem_slot = 32;                // 符文栏位数量
  uint32 season_lv = 33;                  // 赛季等级
  int64 season_power = 34;                // 赛季战力
  uint32 season_id = 35;                  // 赛季ID
  bool season_enter = 36;                 // 是否已点击赛季入口
  int64 coupon = 37;                      // 代金券
  int64 season_top_power = 38;            // 赛季最高战力
  bool assistant_unlock = 39;             // 是否已解锁小助手
  int64 season_crystal_top35_power = 40;  // 赛季水晶最高的35名英雄
  uint32 chat_bubbles = 41;               // 聊天气泡ID
  uint32 max_blessed_hero_cnt = 42;       // 赐福英雄数量的最大值
  Duel duel = 43;                         // 切磋数据
  int64 season_enter_time = 44;           // 进入赛季时间
  uint32 title = 45;                      // 称号
  uint32 can_mute = 46;                   // 是否可以禁言账号
  uint32 pokemon_image = 47;              // 宠物形象ID
}

message C2L_Flush {
  bool user = 1;
  bool bags = 2;
  bool equips = 3;     // equips必须在heroes前
  bool artifacts = 4;  // 须在heroes前
  bool gems = 5;       // 玩家身上的宝石列表，必须在heroes前
  bool emblems = 6;    // 纹章,须在heroes前
  bool global_attr = 7;
  bool heroes = 8;
  bool formation = 9;  // formation必须在heroes后面
  bool dungeon = 10;
  bool tasks = 11;
  bool guild_talent = 12;             // 公会天赋
  bool guidance = 13;                 // 引导
  bool sevenday_login = 14;           // 7日登录
  bool client_info = 15;              // 客户端配置
  bool summon_data = 16;              // 抽卡相关信息
  bool crystal = 17;                  // 水晶数据
  bool push_gift = 18;                // 推送礼包
  bool operate_activity = 19;         // 配置配置活动
  bool forest = 20;                   // 密林
  bool dispatch = 21;                 // 悬赏任务
  bool skin = 22;                     // 皮肤
  bool rite = 23;                     // 永恒仪式
  bool pre_season = 24;               // 赛季前奖励
  bool activity_lifelong_gifts = 25;  // 终身礼包
  bool season_jewelry = 26;           // 赛季装备
  bool pokemon = 27;                  // 宠物
}

message L2C_Flush {
  uint32 ret = 1;
  bool user = 2;
  bool bags = 3;
  bool equips = 4;
  bool artifacts = 5;
  bool gems = 6;
  bool emblems = 7;
  bool global_attr = 8;
  bool heroes = 9;
  bool formation = 10;
  bool dungeon = 11;
  bool tasks = 12;
  bool guild_talent = 13;
  bool guidance = 14;
  bool sevenday_login = 15;           // 7日登录
  bool summon_data = 16;              // 抽卡相关信息
  bool crystal = 17;                  // 水晶数据
  bool push_gift = 18;                // 推送礼包
  bool operate_activity = 19;         // 配置配置活动
  bool forest = 20;                   // 密林
  bool dispatch = 21;                 // 悬赏任务
  bool skin = 22;                     // 皮肤
  bool rite = 23;                     // 永恒仪式
  bool pre_season = 24;               // 赛季前奖励
  bool activity_lifelong_gifts = 25;  // 终身礼包
  bool season_jewelry = 26;           // 赛季装备
  bool pokemon = 27;                  // 宠物
}

message L2C_GetUser {
  User user = 1;
}

message C2L_KeepAlive {
  int64 now = 1;
}

message L2C_KeepAlive {
  int64 now = 1;
  int32 zone = 2;
  int64 open = 3;           // 开服时间
  uint32 data_version = 4;  // 数据表版本号
}

message Attr {
  uint32 type = 1;
  int64 value = 2;
}

message Resource {
  uint64 id = 1;
  uint32 type = 2;
  uint32 value = 3;
  uint32 count = 4;
  repeated Attr attrs = 5;
  uint64 total_count = 6;  // 后端日志用，记录此资源拥有数量
  int64 gen_time = 7;      // 邮件生成时间
}

message Resources {
  repeated Resource ress = 1;
}

message L2C_GetBags {
  map<uint32, uint32> items = 1;
  map<uint32, uint32> fragments = 2;
  map<uint32, uint64> tokens = 3;
  map<uint32, uint32> artifact_fragments = 4;
  map<uint32, uint32> emblem_fragments = 5;
  map<uint32, uint32> remain_fragments = 6;
  map<uint32, ExpiredItems> expired_item = 7;  // 过期道具
  map<uint32, uint32> season_map_goods = 8;    // 赛季地图道具
  map<uint32, uint32> pokemon_fragments = 9;   // 宠物碎片
}

message L2C_OpResources {
  repeated Resource increase = 1;  // 生成的资源
  repeated Resource reduce = 2;    // 消耗的资源
}

message L2C_OpNum {
  map<uint32, NumInfo> num_info = 1;
}

message C2L_UseItem {
  uint32 id = 1;
  uint32 count = 2;
  uint32 param0 = 3;
}

message L2C_UseItem {
  uint32 ret = 1;
  repeated Resource awards = 2;
}

message C2L_SellItem {
  uint32 id = 1;
  uint32 count = 2;
}

message L2C_SellItem {
  uint32 ret = 1;
  uint32 id = 2;
  uint32 count = 3;
}

// 自选礼包内，物品信息
message SubItem {
  uint32 id = 1;     // item_selective_group_info表中的唯一id
  uint32 count = 2;  // 物品数量
}

message C2L_ItemSelect {
  uint32 id = 1;  // 礼包道具id - item_info表中id
  repeated SubItem sub_items = 2;
}

message L2C_ItemSelect {
  uint32 ret = 1;
  uint32 id = 2;     // 礼包道具id - item_info表中id
  uint32 count = 3;  // 兑换礼包的数量
  repeated Resource awards = 4;
}

message MailCond {
  uint32 type = 1;
  uint64 value = 2;
  bool ge = 3;  // 大于等于
}

//$<ROP redis|map|m:uint64 buffer=128>
message Mail {
  uint64 id = 1;                 //$<ROP unique >
  uint32 base_id = 2;            // 系统邮件类型
  int64 create_time = 3;         // 邮件发送时间
  uint32 flag = 4;               // 邮件状态 0-新邮件 1-已读 2-已领 3-已删除
  repeated string params = 5;    // 邮件内容
  repeated Resource awards = 6;  // 奖励
  uint32 type = 7;               // 邮件类型 0-普通邮件 1-全服邮件
  repeated MailCond and = 8;     // and or 二选一
  repeated MailCond or = 9;
  int64 expired_time = 10;  // 过期时间
  uint64 process_id = 11;
  uint32 reason = 12;             // 发邮件的reason和行为日志里的seaon一致
  repeated uint32 ops = 13;       // 运营商
  repeated uint32 channels = 14;  // 二级运营商
  bool multi_lang = 15;           // 是否使用多语言
  int64 gen_time = 16;            // 实际的生成时间
  uint64 gm_id = 17;              // gm邮件ID (目前只有全服邮件用)
  bool gm_del = 18;               // gm删除标记 (目前只有全服邮件用)
}

// 获取邮件
message C2L_GetMails {
  uint64 max_id = 1;  // 客户端中最大的邮件ID
}

message L2C_GetMails {
  uint32 ret = 1;
  repeated Mail mails = 2;
}

// 读邮件
message C2L_ReadMail {
  uint64 id = 1;
}

message L2C_ReadMail {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 flag = 3;  // 邮件状态，对应common.MAIL_FLAG
}

// 领取邮件奖励
message C2L_DrawMails {
  repeated uint64 ids = 1;
}

message L2C_DrawMails {
  uint32 ret = 1;
  repeated uint64 ids = 2;
  uint32 flag = 3;  // 邮件状态，对应common.MAIL_FLAG
  repeated Resource awards = 4;
}

// 删除邮件
message C2L_DeleteMails {
  repeated uint64 ids = 1;
}

message L2C_DeleteMails {
  uint32 ret = 1;
  repeated uint64 ids = 2;
}

message C2L_AddPurchaseNum {
  uint32 type = 1;  // 玩法次数类型
  uint32 num = 2;   // 本次购买次数
}

message L2C_AddPurchaseNum {
  uint32 ret = 1;
  uint32 type = 2;
  uint32 num = 3;
  uint32 purchase_num = 4;  // 总购买次数
}

message AchieveTask {
  uint32 id = 1;
  uint64 progress = 2;
  bool awarded = 3;
  bool finished = 4;
}

message AchieveInfo {
  map<uint32, TaskTypeProgress> task_type_progress = 1;
  map<uint32, bool> awarded = 2;
}

// 商店系统
message Shop {
  uint32 sys_id = 1;                   // 商店id
  uint32 free_refresh_left_num = 2;    // 随机商店 - 免费刷新剩余次数
  int64 free_refresh_recover_tm = 3;   // 随机商店 - 免费刷新次数，上次恢复时间
  uint32 paid_refresh_num = 4;         // 随机商店 - 付费刷新次数
  int64 paid_refresh_reset_tm = 5;     // 随机商店 - 上一次付费刷新次数重置时间
  map<uint32, uint32> goods_list = 6;  // 商品列表 sysGoodsId => boughtNum
  int64 shop_reset_tm = 7;             // 商店重置时间
  uint32 round = 8;                    // 轮次商店 - 商品轮次
}

message C2L_ShopList {
  repeated uint32 sys_ids = 1;  // 不传默认返回所有的情况
}

message L2C_ShopList {
  uint32 ret = 1;
  repeated uint32 sys_ids = 2;
  map<uint32, Shop> shops = 3;  // 商店列表
}

message C2L_ShopBuy {
  uint32 sys_id = 1;        // 商店系统表id
  uint32 sys_goods_id = 2;  // 商品系统表id
  uint32 num = 3;           // 购买数量
}

message L2C_ShopBuy {
  uint32 ret = 1;
  uint32 sys_id = 2;        // 商店系统表id
  uint32 sys_goods_id = 3;  // 商品系统表id
  uint32 num = 4;           // 购买数量
  repeated Resource awards = 5;
  repeated Resource costs = 6;
}

message C2L_ShopRefresh {
  uint32 sys_id = 1;  // 商店系统表id
  bool is_auto = 2;   // 是否小助手刷新
}

message L2C_ShopRefresh {
  uint32 ret = 1;
  uint32 sys_id = 2;  // 商店系统表id
  Shop info = 3;      // 商店信息
  bool is_auto = 4;   // 是否小助手刷新
}

message C2L_ShopReset {
  uint32 sys_id = 1;  // 商店系统表id
}

message L2C_ShopReset {
  uint32 ret = 1;
  uint32 sys_id = 2;  // 商店系统表id
}

// 好友
message C2L_FriendInfo {}

message Friend {
  uint64 id = 1;
  uint32 snd_like = 2;  // 0-未点赞 1-已点赞
  uint32 rcv_like = 3;  // 0-未被点赞 1-被点赞 2-已领取
  bool black = 4;       // true-黑名单
  UserSnapshot user = 5;
  uint64 server_id = 6;
}

// 好友申请
message FriendRequest {
  int64 tm = 1;   // 申请时间
  uint64 id = 2;  // 玩家id
  UserSnapshot user = 3;
}

message L2C_FriendInfo {
  uint32 ret = 1;
  repeated Friend friends = 2;     // 好友列表
  int64 daily_zero = 3;            // 每日刷新时间
  uint32 rcv_cnt = 4;              // 已领取次数
  uint32 snd_cnt = 5;              // 已发送次数
  repeated uint64 black_list = 6;  // 黑名单id列表
}

message C2L_FriendGetBlacklist {}

message L2C_FriendGetBlacklist {
  uint32 ret = 1;
  repeated Friend friends = 2;  // 黑名单列表列表
}

message C2L_FriendAdd {
  repeated ServerUser users = 1;
}

message L2C_FriendAdd {
  uint32 ret = 1;
  repeated ServerUser users = 2;  // 返回成功发送请求的id
}

message L2C_FriendNotify {
  bool request = 1;        // 是否有新的好友请求,每次进去好友请求页面都重新发请求
  uint64 rcv_like_id = 2;  // 接收到好友的点赞
  Friend add_friend = 3;   // 新好友
  uint64 del_friend = 4;   // 删除好友 被拉黑或者被删除好友
}

message C2L_FriendConfirm {
  repeated uint64 id = 1;
  bool accept = 2;  // true-同意接受
}

message L2C_FriendConfirm {
  uint32 ret = 1;
  bool accept = 2;
  repeated uint64 id = 3;      // 需要从申请列表里删除的id,包括已经
  repeated Friend friend = 4;  // 添加新好友到列表
}

message C2L_FriendDelete {
  uint64 id = 1;
}

message L2C_FriendDelete {
  uint32 ret = 1;
  uint64 id = 2;  // 成功后，前端删除好友
}

message C2L_FriendBlacklist {
  uint64 id = 1;
  uint64 server_id = 2;
}

message L2C_FriendBlacklist {
  uint32 ret = 1;
  uint64 id = 2;
  Friend friend = 4;  // 新增黑名单信息
  uint64 server_id = 5;
}

message C2L_FriendRemBlacklist {
  uint64 id = 1;
}

message L2C_FriendRemBlacklist {
  uint32 ret = 1;
  uint64 id = 2;
}

message C2L_FriendSendLike {
  uint32 tp = 1;  // 0-个人 1-全部
  uint64 id = 2;
}

message L2C_FriendSendLike {
  uint32 ret = 1;
  uint32 tp = 2;
  repeated uint64 ids = 3;       // 已点赞
  uint32 snd_cnt = 4;            // 已点赞次数
  repeated Resource awards = 5;  // 发送友情点的奖励
}

message C2L_FriendRecvLike {
  uint32 tp = 1;  // 0-个人 1-全部
  uint64 id = 2;  // 个人领取玩家id
}

message L2C_FriendRecvLike {
  uint32 ret = 1;
  uint32 tp = 2;
  repeated uint64 ids = 3;       // 已成功领取id
  uint32 rcv_cnt = 4;            // 更新已领取次数
  repeated Resource awards = 5;  // 获得的奖励
}

message C2L_FriendRecommend {}

message L2C_FriendRecommend {
  uint32 ret = 1;
  repeated UserSnapshot users = 2;
}

message C2L_FriendSearch {
  uint64 id = 1;
  string name = 2;
}

message L2C_FriendSearch {
  uint32 ret = 1;
  repeated UserSnapshot user = 2;  // 名称查找，重名的一起返回
}

message C2L_FriendRequestInfo {}

message L2C_FriendRequestInfo {
  uint32 ret = 1;
  repeated FriendRequest requests = 2;  // 好友申请 添加
}

message C2L_GetCommonRank {
  uint32 rank_id = 1;  // 排行榜id
}

message RankValue {
  uint64 value = 1;  // 对应排名数值
  UserSnapshot user = 2;
  uint64 param1 = 3;
  uint64 param2 = 4;
  GuildSnapshot guild = 5;
  uint64 id = 6;
  uint32 liked_count = 7;  // 排行榜显示点赞数量时使用：目前使用排行ID（11）
  uint64 param3 = 8;
  uint64 sid = 9;         // 玩家服务器id
  string report_id = 10;  // 战报id
  uint64 param4 = 11;
  uint64 param5 = 12;
  bytes show_data = 13;  // 额外数据结构(根据自身业务定) proto的二进制数据
}

message L2C_GetCommonRank {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated RankValue list = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
}

message C2L_GetCommonRankFirst {
  repeated uint32 rank_ids = 1;  // 排行榜id列表
}

message FirstRankValue {
  uint32 rank_id = 1;  // 排行榜id
  RankValue rank_value = 2;
}

message L2C_GetCommonRankFirst {
  uint32 ret = 1;
  repeated FirstRankValue list = 2;
}

message C2L_GM {
  repeated Resource awards = 1;
  uint32 test_data = 2;
  uint32 guild_level = 3;  // 自动创建或加入公会后需要设置的公会等级
}

message L2C_GM {
  uint32 ret = 1;
  repeated Resource awards = 2;
  uint64 guild_id = 3;
}

message C2L_ViewUser {
  uint64 id = 1;
  uint32 formation_id = 2;        // 传对应的阵容ID，UserSnapshot的defense_power字段会传对应的防守战力
  uint64 server_id = 3;           // 玩家所在服务器id
  repeated ServerUser users = 4;  // 批量查看玩家
}

message L2C_ViewUser {
  uint32 ret = 1;
  uint32 formation_id = 2;
  UserSnapshot user = 3;
  repeated UserSnapshot users = 4;  // 批量返回
}

message C2L_ViewFormation {
  uint64 uid = 1;        // 玩家id
  uint32 fid = 2;        // 阵容id
  uint64 server_id = 3;  // 玩家所在服务器id
}

message L2C_ViewFormation {
  uint32 ret = 1;
  uint64 uid = 2;
  uint32 fid = 3;
  UserBattleData data = 4;
}

message TaskTypeProgress {
  uint32 task_type_id = 1;       // 类型id
  uint64 progress = 2;           // 一般情况下只需要用这个值
  map<uint32, bool> values = 3;  // 额外使用，不重复id使用
}

// 新的日常任务模块
message DailyTask {
  map<uint32, TaskTypeProgress> task_type_progress = 1;  // 不同的任务进度
  map<uint32, bool> awarded = 2;                         // 已经领取的奖励id
}

//$<ROP redis|map|uf:uint64 buffer=1024>
message Formation {
  uint32 id = 1;  //$<ROP unique >
  reserved 2;
  reserved "info";
  reserved 3;
  reserved "artifacts";
  repeated FormationTeamInfo teams = 4;  // 队伍信息
  string name = 10;                      // 阵容名称
  uint32 sort_id = 11;                   // 排序号（仅用于前端排序显示）
}

message FormationTeamInfo {
  repeated FormationInfo info = 1;
  repeated FormationArtifactInfo artifacts = 2;
  FormationRiteInfo rite_info = 3;
  repeated FormationRemainInfo remain_info = 4;
}

message FormationInfo {
  uint32 pos = 1;
  uint64 hid = 2;  // 英雄id
  uint64 uid = 3;  // 英雄所属玩家id，0代表自己的
  //  uint64 hp = 4;//当前血量, 待定字段，暂时不生效
  map<uint32, uint32> links = 5;  // 全能英雄所选可变羁绊 羁绊类型=>羁绊id (选自动时删除此项)
}

message FormationArtifactInfo {
  uint32 pos = 1;
  uint32 aid = 2;  // 神器id
}

message FormationRitePower {
  uint32 pos = 1;
  uint32 power_id = 2;  // 威能id
}

message FormationRiteInfo {
  uint32 rite_id = 1;  // 永恒仪式ID
  repeated FormationRitePower rite_powers = 2;
}

// 遗物布阵信息
message FormationRemainInfo {
  uint32 pos = 1;
  uint32 id = 2;  // 遗物id
}

// 布阵
message C2L_Formation {
  uint32 formation_id = 1;  // 阵容ID
  Formation formation = 2;
  uint32 type = 3;  // 阵容改变类型: 默认0-非主线第一队布阵 1-水晶内交换位置 2-水晶外交换位置(主线快速上阵推荐)
}

message L2C_Formation {
  uint32 ret = 1;
  uint32 formation_id = 2;
  Formation formation = 3;
  uint32 type = 4;  // 阵容改变类型: 默认0-非主线第一队布阵 1-水晶内交换位置 2-水晶外交换位置(主线快速上阵推荐)
}

message C2L_GetFormation {
  repeated uint32 formation_ids = 1;  // 不传默认返回所有的情况
}

message L2C_GetFormation {
  uint32 ret = 1;
  repeated uint32 formation_ids = 2;
  map<uint32, Formation> formations = 3;
}

message C2L_BattleTest {}

message L2C_BattleTest {
  uint32 ret = 1;
  bt.MultipleTeamsReport report = 2;
}

message C2L_RobotBattle {
  uint32 func_id = 1;
  uint64 user_id = 2;
  repeated uint32 params = 3;
  BotData data = 4;
}

message L2C_RobotBattle {
  uint32 ret = 1;
  uint32 func_id = 2;
  uint64 user_id = 3;
  bool win = 4;
  string report_id = 5;
}

message RedPointIdList {
  repeated uint32 ids = 1;
}
message C2L_FlushRedPoint {
  repeated RedPointIdList id_list = 1;  // 此参数为多个数组，
  //  每个数组内的id是或的关系，只找其中一个需要显示的id
}

message L2C_FlushRedPoint {
  uint32 ret = 1;
  repeated uint32 ids = 2;  // 返回需要显示的红点id
}

message L2C_NewMailTip {
  uint32 ret = 1;
  bool bag_full = 2;  // 是否因背包满而发的邮件
}

message L2C_BattleReport {
  uint32 ret = 1;
  string id = 2;      // 战报id
  uint32 total = 3;   // 总的分包个数，
  uint32 order = 4;   // 序号，从0开始，最大为total-1
  bytes report = 5;   // 序列化之后的战报，拼接起来用proto.unmarshal反序列化
  bool compress = 6;  // 是否snappy压缩。当为true时，report字段为snappy压缩过的，则report的解析需改为：先拼接起来，再snappy解压，再proto.unmarshal反序列化
}

// 主线数据
message Dungeon {
  uint32 sys_id = 1;                  // 战役关卡id
  int64 onhook_tm = 2;                // 挂机开始时间
  int64 calc_tm = 3;                  // 奖励预结算时间
  repeated Resource awards = 4;       // 预结算但未领取的奖励
  repeated Resource base_awards = 5;  // 固定+随机掉落的奖励，不包括VIP加成，供goddess使用
}

// 战斗
message C2L_DungeonFight {
  uint32 sys_id = 1;  // 关卡id
  bytes client_data = 2;
  bool quick = 3;  // 快速战斗
}
message L2C_DungeonFight {
  uint32 ret = 1;
  uint32 sys_id = 2;  // 关卡id
  bool win = 3;       // 是否胜利
  repeated Resource awards = 4;
  string report_id = 5;    // 战报id
  int64 onhook_tm = 6;     // 挂机开始时间
  bool quick = 7;          // 快速战斗
  uint32 defeat_team = 8;  // 战败队伍序号
}

// 领取挂机奖励
message C2L_DungeonRecvAward {}
message L2C_DungeonRecvAward {
  uint32 ret = 1;
  repeated Resource awards = 2;
  int64 onhook_tm = 3;                   // 挂机开始时间
  repeated Resource goddess_awards = 4;  // 女神家园奖励加成
}

// 加速领取挂机奖励
message C2L_DungeonSpeedRecvAward {}
message L2C_DungeonSpeedRecvAward {
  uint32 ret = 1;
  repeated Resource awards = 2;
  repeated Resource goddess_awards = 3;  // 女神家园奖励加成
}

// 挂机预览
message C2L_DungeonPreview {}
message L2C_DungeonPreview {
  uint32 ret = 1;
  repeated Resource awards = 2;
  int64 calc_tm = 3;                     // 奖励预结算时间
  repeated Resource goddess_awards = 4;  // 女神家园奖励加成
}

// 获取主线信息
message C2L_Dungeon {}
message L2C_Dungeon {
  uint32 ret = 1;
  uint32 sys_id = 2;    // 战役关卡id
  int64 onhook_tm = 3;  // 挂机开始时间
  int64 calc_tm = 4;    // 奖励预结算时间
}

// 英雄模块
//$<ROP redis|map|h:uint64 >
message HeroBody {
  uint64 id = 1;                            //$<ROP unique >    //英雄id
  uint32 sys_id = 2;                        // 英雄配置hero_info.xml表id
  uint32 star = 3;                          // 星级
  uint32 level = 4;                         // 等级
  uint32 stage = 5;                         // 突破等级
  bool locked = 6;                          // 是否已锁定
  map<uint32, uint64> equipment = 7;        // 装备，位置=>装备id 注：位置1234为装备
  map<uint32, uint64> emblem = 8;           // 纹章，位置=>纹章id 注：位置只有12
  uint32 changing_sys_id = 9;               // 英雄转换，随机到的英雄id
  map<uint32, uint64> gem = 10;             // 宝石，位置=>宝石id 注：位置只有1,2 todo 0.9.8已废弃
  uint64 snapshot_power = 11;               // 战力，非实时计算，仅用于完美快照记录
  repeated Gem gems = 12;                   // 宝石
  uint32 awaken_level = 13;                 // 觉醒等级
  uint32 tag = 14;                          // 英雄标签 0-材料英雄 1-赐福英雄 2-缔约英雄
  map<uint32, uint64> season_jewelry = 15;  // 赛季装备，位置=>装备id 注：位置1234为装备
  HeroRare rare = 16;                       // 品质
}

message HeroRare {
  uint32 rare = 1;
  uint32 rare_level = 2;
}

message Gem {
  uint32 slot = 1;
  uint32 level = 2;
}

//$<ROP redis|map|hsuc:uint64 >
message HeroStarUpCosts {
  uint64 id = 1;                              //$<ROP unique >    //英雄id
  map<uint32, uint32> star5_costs = 2;        // 5星英雄消耗数据 英雄系统id=>数量
  repeated StarUpCosts other_star_costs = 3;  // 升星消耗掉的其他英雄数据
}

message StarUpCosts {
  uint32 sys_id = 1;                          // 英雄系统id
  uint32 star = 2;                            // 英雄星级
  map<uint32, uint32> star5_costs = 3;        // 5星英雄消耗数据 英雄系统id=>数量
  repeated StarUpCosts other_star_costs = 4;  // 升星消耗掉的其他英雄数据
}

// 返回给客户端的英雄数据
message Hero {
  HeroBody data = 1;
}

// 获取英雄列表
message C2L_HeroList {}

message L2C_HeroList {
  uint32 ret = 1;
  repeated Hero heroes = 2;  // 英雄列表
}

// 英雄升级
message C2L_HeroLevelUp {
  uint64 id = 1;   // 英雄id
  uint32 num = 2;  // 提升等级数量
}

message L2C_HeroLevelUp {
  uint32 ret = 1;
  uint64 id = 2;   // 英雄id
  uint32 num = 3;  // 提升等级数量
  Hero hero = 4;   // 英雄详情
}

// 英雄突破
message C2L_HeroStageUp {
  uint64 id = 1;  // 英雄id
}

message L2C_HeroStageUp {
  uint32 ret = 1;
  uint64 id = 2;  // 英雄id
  Hero hero = 3;  // 英雄详情
}

// 英雄升星
message C2L_HeroStarUp {
  uint64 id = 1;                 // 英雄id
  repeated uint64 hero_ids = 2;  // 待消耗的英雄id
}

message L2C_HeroStarUp {
  uint32 ret = 1;
  uint64 id = 2;                 // 英雄id
  Hero hero = 3;                 // 英雄详情
  repeated uint64 hero_ids = 4;  // 消耗的英雄id
  repeated Resource awards = 5;  // 返还的资源
}

// 购买英雄栏位
message C2L_HeroBuySlot {
  uint32 count = 1;
}

message L2C_HeroBuySlot {
  uint32 ret = 1;
  uint32 slots = 2;  // 最新英雄栏位数量
}

// 英雄锁定
message C2L_HeroUpdateLockStatus {
  uint64 id = 1;  // 英雄id
  bool lock = 2;  // 是否锁定
}

message L2C_HeroUpdateLockStatus {
  uint32 ret = 1;
  uint64 id = 2;  // 英雄id
  bool lock = 3;  // 是否锁定
}

// 英雄分解
message C2L_HeroDecompose {
  repeated uint64 hero_ids = 1;  // 待分解的英雄id
}

message L2C_HeroDecompose {
  uint32 ret = 1;
  repeated uint64 hero_ids = 2;  // 待分解的英雄id
  repeated Resource awards = 3;  // 分解后返还的资源
}

// 测试接口，前端用来比较英雄属性和战力
message C2L_HeroTestAttr {
  uint64 hid = 1;
}

message L2C_HeroTestAttr {
  uint32 ret = 1;
  map<int32, int64> base_attr = 2;
  map<int32, int64> skill_attr = 3;
  map<int32, int64> equip_attr = 4;
  map<int32, int64> attr = 5;        // 所有养成的总属性
  map<int32, int64> total_attr = 6;  // 属性合并之后的值
  uint64 power = 7;
  map<int32, int64> gem_attr = 8;                   // 宝石属性
  map<int32, int64> emblem_attr = 9;                // 纹章属性
  map<int32, int64> stage_attr = 10;                // 英雄突破属性
  map<int32, int64> awake_attr = 11;                // 英雄觉醒属性
  map<int32, int64> global_attr = 12;               // 全局属性
  map<int32, int64> memory_global_attr = 13;        // 境界的全局属性
  map<int32, int64> artifact_global_attr = 14;      // 神器的全局属性
  map<int32, int64> guild_talent_global_attr = 15;  // 公会天赋的全局属性
  map<int32, int64> master_attr = 16;               // 成长大师的属性
  map<int32, int64> handbook_global_attr = 17;      // 图鉴的属性
  map<int32, int64> crystal_global_attr = 18;       // 水晶的属性
  map<int32, int64> goddess_contract_attr = 19;     // 契约之所的属性
  map<int32, int64> remain_book = 20;               // 遗物图鉴的属性
  map<int32, int64> pokemon_global_attr = 21;       // 宠物的全局属性
}

// 更新英雄数据
message C2L_HeroesUpdate {}

message L2C_HeroesUpdate {
  uint32 ret = 1;
  repeated Hero heroes = 2;  // 英雄列表
}

// 英雄回退
message C2L_HeroBack {
  uint64 id = 1;  // 待回退的英雄id
}

message L2C_HeroBack {
  uint32 ret = 1;
  uint64 id = 2;                 // 待回退的英雄id
  repeated Resource awards = 3;  // 回退后返还的资源
  Hero hero = 4;                 // 最新英雄数据
}

// 英雄重生
message C2L_HeroRevive {
  uint64 id = 1;  // 待重生的英雄id
}

message L2C_HeroRevive {
  uint32 ret = 1;
  uint64 id = 2;                 // 待重生的英雄id
  repeated Resource awards = 3;  // 重生后返还的资源
  Hero hero = 4;                 // 最新英雄数据
}

// 英雄转换 - 随机
message C2L_HeroChangeRandom {
  uint64 id = 1;  // 待转换的英雄id
}

message L2C_HeroChangeRandom {
  uint32 ret = 1;
  uint64 id = 2;  // 待转换的英雄id
  Hero hero = 3;  // 最新英雄数据
}

// 英雄转换 - 保存
message C2L_HeroChangeSave {
  uint64 id = 1;     // 待转换的英雄id
  bool is_save = 2;  // 是否保存
}

message L2C_HeroChangeSave {
  uint32 ret = 1;
  uint64 id = 2;     // 待转换的英雄id
  bool is_save = 3;  // 是否保存
  Hero hero = 4;     // 最新英雄数据
}

// 推送升星限制等级
message L2C_HeroUpdateStarLimit {
  uint32 ret = 1;
  uint32 limit_star = 2;  // 限制等级，升星不可超过此值
}

// 获取升星消耗数据
message C2L_HeroGetStarUpCosts {
  uint64 id = 1;  // 英雄id
}

message L2C_HeroGetStarUpCosts {
  uint32 ret = 1;
  uint64 id = 2;                        // 英雄id
  HeroStarUpCosts costs = 3;            // 升星消耗数据
  repeated Resource revive_awards = 4;  // 重生返还资源
}

message C2L_HeroConversion {
  repeated uint64 hero_ids = 1;
}

message L2C_HeroConversion {
  uint32 ret = 1;
  repeated uint64 hero_ids = 2;
  repeated Resource awards = 3;
}

message C2L_HeroGemLevelUp {
  uint64 id = 1;        // 英雄id
  uint32 gem_slot = 2;  // 宝石槽位
  uint32 up_num = 3;    // 提升等级数量
}

message L2C_HeroGemLevelUp {
  uint32 ret = 1;
  uint64 id = 2;        // 英雄id
  uint32 gem_slot = 3;  // 宝石槽位
  uint32 up_num = 4;    // 提升等级数量
  Hero hero = 5;        // 最新英雄数据
}

// 英雄转换
message C2L_HeroExchange {
  uint32 target_hero_sys_id = 1;  // 目标英雄系统id
  repeated uint64 hero_ids = 2;
}
message L2C_HeroExchange {
  uint32 ret = 1;
  uint32 target_hero_sys_id = 2;  // 目标英雄系统id
  repeated uint64 hero_ids = 3;
  repeated Resource awards = 4;
}

// 英雄觉醒
message C2L_HeroAwaken {
  uint64 id = 1;  // 英雄id
}

message L2C_HeroAwaken {
  uint32 ret = 1;
  uint64 id = 2;  // 英雄id
  Hero hero = 4;  // 英雄详情
}

// 溢出英雄转换觉醒道具
message C2L_HeroConvert {
  uint64 target_hero_id = 1;     // 目标英雄id
  repeated uint64 hero_ids = 2;  // 用于转换的英雄id
}

message L2C_HeroConvert {
  uint32 ret = 1;
  uint64 target_hero_id = 2;  // 目标英雄id
  repeated uint64 hero_ids = 3;
  repeated Resource awards = 4;
}

message C2L_HeroStarUpRedList {}

message L2C_HeroStarUpRedList {
  uint32 ret = 1;
  repeated uint64 hero_ids = 2;  // 当前吞噬红卡英雄列表
}

message HeroTag {
  uint64 hero_id = 1;  // 英雄id
  uint32 tag = 2;      // 英雄标签 0-材料英雄 1-赐福英雄 2-缔约英雄
}

// 更新英雄tag类型
message C2L_HeroTagUpdate {
  repeated uint64 hero_ids = 1;  // 要交换的两个英雄(第一个是原赐福英雄，第二个是原缔约英雄)
}
message L2C_HeroTagUpdate {
  uint32 ret = 1;
  repeated HeroTag heroes = 2;  // 更新后的英雄tag列表
}

message C2L_HeroConvertAwakenItem {
  map<uint32, uint32> awaken_item = 1;
}

message L2C_HeroConvertAwakenItem {
  uint32 ret = 1;
  map<uint32, uint32> awaken_item = 2;
  repeated Resource awards = 3;
}

message C2L_Summon {
  uint32 id = 1;    // 召唤类型
  bool plural = 2;  // 是否多次召唤
}

message L2C_Summon {
  uint32 ret = 1;
  uint32 id = 2;                     // 召唤类型
  bool plural = 3;                   // 是否多次召唤
  repeated Resource awards = 4;      // 奖励
  uint32 advanced_summon_count = 5;  // 高级召唤次数 (评分功能使用)
  uint32 left_guarantee = 6;         // 还剩多少次必中
}

message C2L_SummonGetData {}

message L2C_SummonGetData {
  uint32 ret = 1;
  uint32 basic_summon_free_count = 2;               // 普通召唤次数（免费）
  uint32 advanced_summon_free_count = 3;            // 高级召唤次数（免费）
  uint32 artifact_summon_free_count = 4;            // 神器召唤次数免费 (免费)
  uint32 artifact_summon_num_to_special_count = 5;  // 神器召唤距离保底有几次
  bool hero_auto_decompose = 6;                     // 普通英雄自动分解summon
  map<uint32, uint32> summon_count = 7;             // 不同类型的抽卡总次数 key: 对应summon_type_info的id   value: 此类型抽卡总次数
  WishListMes wish_list_mes = 8;                    // 心愿单信息
  map<uint32, uint32> left_guarantees = 9;          // group=>还剩多少次必中
}

message WishListMes {
  uint32 num_to_open = 1;           // 心愿单距离开启还需几抽 （0表示已开启）
  repeated WishInfo wish_list = 2;  // 心愿单
  int64 start_tm = 3;               // 心愿单周期开始时间
  uint32 reset_times = 4;           // 心愿单重置次数
  uint32 num_to_guarantee = 5;      // 心愿单距离保底有几次 (0表示没有保底了)
}

message C2L_SummonSetHeroAutoDecompose {
  bool auto_decompose = 1;  // 自动分解
}

message L2C_SummonSetHeroAutoDecompose {
  uint32 ret = 1;
  bool auto_decompose = 2;
}

message WishInfo {
  uint32 slot = 1;  // 心愿单格位 (1:左格位  2:右格位)
  uint32 id = 2;    // 心愿id
  bool lock = 3;    // 是否锁定
}

message C2L_SummonSetWishList {
  uint32 slot = 1;     // 心愿格位
  uint32 wish_id = 2;  // 心愿id, 传0表示去掉此格位的id
}

message L2C_SummonSetWishList {
  uint32 ret = 1;
  uint32 slot = 2;
  uint32 wish_id = 3;
  uint32 num_to_guarantee = 4;  // 心愿单距离保底有几次 (0表示没有保底了)
}

message C2L_SummonArtifactPointsExchange {
  uint32 award_id = 1;
}

message L2C_SummonArtifactPointsExchange {
  uint32 ret = 1;
  uint32 award_id = 2;
  repeated Resource awards = 3;
}

message C2L_SummonSimulation {
  uint32 id = 1;  // 召唤类型
  uint32 count = 2;
}

message L2C_SummonSimulation {
  uint32 ret = 1;
  map<uint32, uint32> hero_count = 2;
  map<uint32, uint32> pool_count = 3;
  map<uint32, uint32> rare_count = 4;
  map<uint32, uint32> top_rare_summon_count = 5;
}

// 新的周常任务模块
message WeeklyTask {
  map<uint32, TaskTypeProgress> task_type_progress = 1;  // 不同的任务进度
  map<uint32, bool> awarded = 2;                         // 已经领取的奖励id
}

// 新的周常任务模块
message MonthlyTask {
  map<uint32, TaskTypeProgress> task_type_progress = 1;  // 不同的任务进度
  map<uint32, bool> awarded = 2;                         // 已经领取的奖励id
}

message C2L_TaskGetInfo {
  uint32 type = 1;  // 获取任务的类型， 为0代表全部获取 >0获取其中的1个
}

message L2C_TaskGetInfo {
  uint32 ret = 1;
  uint32 type = 2;
  AchieveInfo achieve = 3;
  DailyTask daily_task = 4;
  WeeklyTask weekly_task = 5;
  MonthlyTask monthly_task = 6;
  LineTask line_task = 7;
}

// 越南观看广告
message C2L_TaskAdsWatch {}

message L2C_TaskAdsWatch {
  uint32 ret = 1;
}

// 支持批量领取
message C2L_TaskReceiveAward {
  repeated uint32 ids = 1;    // 任务id
  bool partition_awards = 2;  // 返回的奖励是否区分，默认合并不区分
  uint32 box_type = 3;        // 1：日常宝箱  2：周常宝箱
}

message L2C_TaskReceiveAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  repeated Resource awards = 3;
  bool partition_awards = 4;
  uint32 box_type = 5;                   // 2：日常宝箱  3：周常宝箱
  repeated Resource goddess_awards = 6;  // 女神家园奖励加成
}

// 更新奖励
message L2C_TaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> achieve_progress = 2;       // 不同的任务进度
  map<uint32, TaskTypeProgress> daily_task_progress = 3;    // 不同的任务进度
  map<uint32, TaskTypeProgress> weekly_task_progress = 4;   // 不同的任务进度
  map<uint32, TaskTypeProgress> monthly_task_progress = 5;  // 每月重置的任务进度
  map<uint32, TaskTypeProgress> line_task_progress = 6;     // 链式任务
}

message C2L_FragmentCompose {
  uint32 id = 1;   // 碎片id
  uint32 num = 2;  // 合成数量
}

message L2C_FragmentCompose {
  uint32 ret = 1;
  uint32 id = 2;
  uint32 num = 3;
  repeated Resource awards = 4;
}

message C2L_FragmentEmblemCompose {
  uint32 id = 1;   // 碎片id
  uint32 num = 2;  // 合成数量
}

message L2C_FragmentEmblemCompose {
  uint32 ret = 1;
  uint32 id = 2;
  uint32 num = 3;
  repeated Resource awards = 4;
}

// 碎片合成单元
message FragmentComposeUnit {
  uint32 id = 1;   // 碎片id
  uint32 num = 2;  // 合成数量
}

message C2L_FragmentComposeAll {
  repeated FragmentComposeUnit units = 1;  // 需要合成的碎片
}

message L2C_FragmentComposeAll {
  uint32 ret = 1;
  repeated FragmentComposeUnit units = 2;
  repeated Resource awards = 3;
}

message C2L_FragmentComposeTest {
  repeated FragmentComposeUnit units = 1;  // 需要合成的碎片
}

message L2C_FragmentComposeTest {
  uint32 ret = 1;
  map<uint32, uint32> hero_count = 2;
}

// 装备
//$<ROP redis|map|e:uint64 >
message Equipment {
  uint64 id = 1;                        //$<ROP unique >    //装备id
  uint32 sys_id = 2;                    // 装备配置equip_info.xml表id
  uint32 strength_level = 3;            // 强化等级
  uint32 refine_exp = 4;                // 精炼进度
  uint32 refine_level = 5;              // 精炼等级
  uint32 enchant_level = 6;             // 附魔等级
  uint32 evolution_level = 7;           // 进阶等级
  uint64 hid = 8;                       // 英雄id
  int64 score = 9;                      // 评分
  uint32 enchant_seed = 10;             // 附魔种子ID
  repeated Resource enchant_cost = 11;  // 附魔消耗
}

message C2L_EquipGet {}

message L2C_EquipGet {
  uint32 ret = 1;
  repeated Equipment equips = 2;  // 装备列表
  repeated uint32 auto_rare = 3;  // 自动分解的装备品质（目前只针对主线战斗和主线宝箱的装备）
}

message C2L_EquipWear {
  uint32 type = 1;           // 操作类型 1：穿 2：脱
  uint64 hid = 2;            // 英雄id
  repeated uint64 eids = 3;  // 装备id
}

message L2C_EquipWear {
  uint32 ret = 1;
  uint32 type = 2;           // 操作类型 1：穿 2：脱 3.一键穿 4.一键脱
  uint64 hid = 3;            // 英雄id
  repeated uint64 eids = 4;  // 装备id
  repeated Equipment equips = 5;
  repeated Hero heroes = 6;  // 变化的英雄
}

message C2L_EquipStrength {  // 强化
  uint64 id = 1;             // 装备唯一id
  uint32 target_level = 2;   // 强化到几级
}

message L2C_EquipStrength {
  uint32 ret = 1;
  uint64 id = 2;  // 装备唯一id
  uint32 target_level = 3;
  Equipment equip = 4;  // 强化后的装备
}

message C2L_EquipMultipleStrength {  // 一键强化
  repeated uint64 ids = 1;
  repeated uint32 target_lvs = 2;
}

message L2C_EquipMultipleStrength {
  uint32 ret = 1;
  repeated uint64 ids = 2;
  repeated uint32 target_lvs = 3;
  repeated Equipment equips = 4;  // 强化后的装备
}

message C2L_EquipRefine {  // 精炼
  uint64 id = 1;           //  装备唯一id
  bool quick = 2;          // 快速精炼
}

message L2C_EquipRefine {
  uint32 ret = 1;
  uint64 id = 2;        //  装备唯一id
  bool quick = 3;       // 快速精炼
  Equipment equip = 4;  // 精炼后的装备
}

message C2L_EquipEnchant {  // 附魔
  uint64 id = 1;            //  装备唯一id
  reserved 'target_level';
  reserved 2;
}

message L2C_EquipEnchant {
  uint32 ret = 1;
  uint64 id = 2;  //  装备唯一id
  reserved 'target_level';
  reserved 3;
  Equipment equip = 4;  // 附魔后的装备
}

message C2L_EquipEvolution {  // 进阶
  uint64 id = 1;              //  装备唯一id
}

message L2C_EquipEvolution {
  uint32 ret = 1;
  uint64 id = 2;        //  装备唯一id
  Equipment equip = 3;  // 进阶后的装备
}

message C2L_EquipDecompose {  // 分解
  repeated uint64 ids = 1;
}

message L2C_EquipDecompose {
  uint32 ret = 1;
  repeated uint64 ids = 2;
  repeated Resource awards = 3;
}

message C2L_EquipRevive {  // 重生
  uint64 id = 1;
}

message L2C_EquipRevive {
  uint32 ret = 1;
  uint64 id = 2;
  repeated Resource awards = 3;
  Equipment equip = 4;
}

message L2C_EquipUpdate {
  uint32 ret = 1;
  repeated Equipment equips = 2;
}

message C2L_EquipGrowTransfer {       // 装备养成转移
  repeated uint64 grow_equips = 1;    // 需要养成的装备
  repeated uint64 revive_equips = 2;  // 被重生的装备
}

message L2C_EquipGrowTransfer {
  uint32 ret = 1;
  repeated uint64 grow_equips = 2;    // 需要养成的装备
  repeated uint64 revive_equips = 3;  // 被重生的装备
  repeated Equipment equips = 4;
  repeated Resource left_res = 5;  // 转移后剩余资源
}

message C2L_EquipSetAutoDecompose {  // 装备自动分解设置
  repeated uint32 auto_rare = 1;     // 需要自动分解的装备品质
}

message L2C_EquipSetAutoDecompose {
  uint32 ret = 1;
  repeated uint32 auto_rare = 2;
}

// 竞技场 - 落地数据
//$<ROP redis|map|arena >
message Arena {
  uint64 id = 1;                    //$<ROP unique>
  uint32 score = 2;                 // 竞技场积分
  int64 tm = 3;                     // 积分更新时间
  uint32 liked = 4;                 // 被点赞数量
  bool reset_opponents = 5;         // 是否需要重置对手(赛季重置时,设置的标记)
  uint32 log_index_id = 6;          // 日志序号Id
  bool new_log_tip = 7;             // 新日志提示
  uint32 defeat_count = 8;          // 连续战败次数
  uint32 season_refresh_count = 9;  // 本赛季刷新次数
  int64 defense_power = 10;         // 竞技场防守战力，冗余数据用于匹配
  ArenaTask task = 11;              // 任务数据
  uint64 fight_count = 12;          // 累计战斗次数
  uint32 win_reward_times = 13;     // 胜利奖励未触发保底次数
  uint32 defeat_reward_times = 14;  // 失败奖励未触发保底次数
}

message ArenaTask {
  uint32 life_best_division = 1;         // 玩家历史最高段位
  uint32 season_best_division = 2;       // 当前赛季达到的最高段位
  uint32 prev_season_best_division = 3;  // 前一个赛季达到的最高段位
  uint64 season_received_award = 4;      // 赛季已领奖励，位存储
  uint64 life_received_award = 5;        // 竞技场成就已领奖励，位存储
  int64 reset_tm = 6;                    // 赛季重置时间
  int64 push_time = 7;                   // 推送时间；玩家当前赛季的段位达到第一名，推一个消息到聊天的系统频道
}

// 竞技场 - 玩家信息
message ArenaUserInfo {
  uint32 score = 1;  // 积分
  uint32 rank = 2;   // 排名
  UserSnapshot user = 3;
  bool bot = 4;         // 是否是机器人
  uint32 division = 5;  // 段位
  uint32 liked = 6;     // 被点赞数
}

// 竞技场战斗前后，分数与排名
message ArenaUserScoreAndRank {
  uint32 new_score = 1;  // 战斗前，分数
  uint32 old_score = 2;  // 战斗后，分数
  uint32 new_rank = 3;   // 战斗前，排名
  uint32 old_rank = 4;   // 战斗后，排名
}

// 上个赛季前三名信息
//$<ROP redis|map|arena_last_season_top >
message ArenaLastSeasonTop {
  uint32 id = 1;      //$<ROP unique > //占位用，其值永远为1
  uint32 season = 2;  // 赛季id
  repeated ArenaUserInfo users = 3;
}

// 竞技场信息
message C2L_ArenaInfo {
  bool show_top3 = 1;  // 是否展示上赛季前三名信息
}

message L2C_ArenaInfo {
  uint32 ret = 1;
  uint32 rank = 2;
  uint32 score = 3;
  int64 refresh_tm = 4;
  repeated ArenaUserInfo opponents = 5;  // 对手列表
  bool new_log_tip = 6;                  // 新日志提示
  bool resetting = 7;                    // 是否正在赛季结算中
  ArenaTask task = 8;                    // 任务数据
  uint64 fight_count = 9;                // 累计战斗次数
  uint32 season = 10;                    // 当前赛季
  ArenaLastSeasonTop top_players = 11;   // 上个赛季前三名信息
  repeated uint64 like_uids = 12;        // 今日已点赞列表
}

// 竞技场 - 刷新对手
message C2L_ArenaRefresh {}

message L2C_ArenaRefresh {
  uint32 ret = 1;
  int64 refresh_tm = 2;
  repeated ArenaUserInfo opponents = 3;  // 对手列表
}

// 竞技场 - 战斗
message C2L_ArenaFight {
  uint64 id = 1;     // 对手玩家id
  uint32 score = 2;  // 对手玩家分数
  bytes client_data = 3;
}

message L2C_ArenaFight {
  uint32 ret = 1;
  uint64 id = 2;  // 对手玩家id
  bool win = 3;   // 是否胜利
  repeated Resource awards = 4;
  string report_id = 5;                       // 战报id
  uint32 score = 6;                           // 对手玩家分数（入参回传）
  ArenaUserScoreAndRank self_change = 7;      // 玩家，战斗前后分数与排名
  ArenaUserScoreAndRank opponent_change = 8;  // 对手，战斗前后分数与排名
}

// 竞技场 - 战报信息
message C2L_ArenaLogList {}

// 竞技场战报数据
//$<ROP redis|map|arena_log:uint64 >
message ArenaLog {
  uint32 id = 1;                  //$<ROP unique> //日志循环队列id
  MiniUserSnapshot attacker = 2;  // 进攻方
  MiniUserSnapshot defender = 3;  // 防守方
  string report_id = 4;           // 战报id
  int64 tm = 5;                   // 记录的时间
  int32 score_change = 6;         // 积分变化
  bool win = 7;                   // attacker是否胜利
  bool bot = 8;                   // defender是否是机器人
  uint64 log_id = 9;              // 日志唯一id
}

message ArenaLogs {
  repeated ArenaLog logs = 1;
}
message ArenaLogDeletes {
  repeated uint32 ids = 2;
}

message L2C_ArenaLogList {
  uint32 ret = 1;
  repeated ArenaLog logs = 2;
}

// 竞技场 - 点赞
message C2L_ArenaLike {
  uint64 id = 1;  // 玩家id
}

message L2C_ArenaLike {
  uint32 ret = 1;
  uint64 id = 2;
  repeated Resource awards = 3;
  uint32 award_id = 4;
}

// 竞技场 - 排行榜
message C2L_ArenaRank {}

// 竞技场 - 玩家排行榜信息
message ArenaRankInfo {
  uint32 score = 1;  // 积分
  uint32 liked = 2;
  UserSnapshot user = 3;
}

message L2C_ArenaRank {
  uint32 ret = 1;
  uint32 rank = 2;
  uint32 score = 3;
  uint32 liked = 4;  // 被点赞次数
  repeated ArenaRankInfo list = 5;
  repeated uint64 like_uids = 6;  // 今日已点赞列表
}

// 竞技场 - 领取任务奖励
message C2L_ArenaRecvAward {
  repeated uint32 ids = 1;  // 任务id列表，arena_division_task_info.xml中id
}

message L2C_ArenaRecvAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;       // 任务id列表，arena_division_task_info.xml中id
  repeated Resource awards = 3;  // 任务奖励
}

// 玩家的战斗数据
message UserBattleData {
  UserSnapshot user = 1;
  uint32 formation_id = 2;
  Formation formation = 3;
  repeated Hero heroes = 4;
  repeated Equipment equips = 5;
  repeated Artifact artifacts = 6;
  GlobalAttr global_attr = 7;
  repeated EmblemInfo emblems = 9;
  uint32 artifact_num = 10;                         // 拥有的神器数量
  CrystalShareAttr crystal_attr = 11;               // 水晶-共鸣属性 (2.16.5-cn水晶重构后废弃)
  repeated uint64 resonance_heroes = 12;            // 水晶-共鸣英雄列表 (2.16.5-cn水晶重构后废弃)
  AchievementsShowcase achieves = 13;               // 成就展示
  repeated Skin skins = 14;                         // 皮肤数据
  repeated Rite rites = 15;                         // 永恒仪式
  SeasonLink season_link = 16;                      // 赛季羁绊
  repeated SeasonAddInfo season_add = 17;           // 赛季加成数据
  uint32 remain_book_level = 18;                    // 遗物图鉴等级
  repeated Remain remains = 19;                     // 上阵的遗物
  TalentTreeCultivate talent_tree_cul = 20;         // 天赋树养成信息
  ShareGrowth share_growth = 21;                    // 共享养成
  Duel duel = 22;                                   // 切磋数据
  repeated SeasonJewelry season_jewelry_list = 23;  // 赛季装备
  repeated Pokemon pokemons = 24;                   // 宠物数据
}

message AchievementsShowcase {
  map<uint32, FunctionShowcase> element = 1;  // key：funcID  value: 当前进度
}

message FunctionShowcase {
  uint32 value = 1;                      // funcID 下有一个数据时, 保存在value1
  map<uint32, uint32> sub_id_value = 2;  // funcID 下有多个数据时, 保存在value2   key为功能里具体的区分ID
}

message C2L_GetUserBattleData {
  repeated uint64 uids = 1;
  uint32 formation_id = 2;
  uint32 type = 3;  // 0 - 对手数据(实时)
  // 1 - 对手数据(玩家阵容的拷贝)
}

message L2C_GetUserBattleData {
  uint32 ret = 1;
  repeated uint64 uids = 2;
  uint32 formation_id = 3;
  uint32 type = 4;
  repeated UserBattleData users = 5;
}

message C2L_GetBattleReport {
  string id = 1;
  int64 tm = 2;  // 战报时间
}

// 实际的战报还是用L2C_BattleReport推送过去的
message L2C_GetBattleReport {
  uint32 ret = 1;
  string id = 2;
  int64 tm = 3;
}

message C2L_SetServerTime {
  string server_time = 1;
}

message L2C_SetServerTime {
  uint32 ret = 1;
}

// 爬塔信息
message Tower {
  uint32 type = 1;   // 类型
  uint32 floor = 2;  // 当前通过的层数
}

message C2L_TowerList {}
message L2C_TowerList {
  uint32 ret = 1;
  repeated Tower towers = 2;
}

message C2L_TowerFight {
  uint32 type = 1;  // 类型
  bytes client_data = 2;
  bool quick = 3;  // 快速战斗
}
message L2C_TowerFight {
  uint32 ret = 1;
  uint32 type = 2;
  repeated Resource awards = 3;
  string report_id = 4;  // 战报id
  bool win = 5;
  bool quick = 6;
  uint32 defeat_team = 7;  // 战败队伍序号
}

message C2L_TowerSweep {
  uint32 type = 1;  // 类型
}
message L2C_TowerSweep {
  uint32 ret = 1;
  uint32 type = 2;
  repeated Resource awards = 3;
}

message C2L_TowerJump {
  uint32 type = 1;   // 类型
  uint32 floor = 2;  // 要跳至的关卡
}
message L2C_TowerJump {
  uint32 ret = 1;
  uint32 type = 2;
  uint32 floor = 3;
  repeated Resource awards = 4;  // 跳关所得奖励
}

message NumInfo {
  uint32 use_num = 1;       // 使用次数   注: 这个使用次数只包含免费次数的使用和购买次数的使用
  uint32 purchase_num = 2;  // 购买次数
  uint32 cost_num = 3;      // 通过消耗资源使用的次数
  uint32 left_num = 4;      // 未使用的次数；注：number_type为个人boss,竞技场，密林时有值
  uint32 num_max = 5;       // 次数上限；注：number_type为个人boss,竞技场，密林时有值
}

// 神器
//$<ROP redis|map|a:uint64 >
message Artifact {
  uint32 sys_id = 1;       //$<ROP unique >       // 神器的系统id
  uint32 star = 2;         // 神器星级
  uint32 strength_lv = 3;  // 神器强化等级
  uint32 forge_lv = 4;     // 铸造等级
  int64 score = 5;         // 神器评分
}

message C2L_ArtifactList {}

message L2C_ArtifactList {
  uint32 ret = 1;
  repeated Artifact artifacts = 2;  // 神器
}

message C2L_ArtifactActivate {
  uint32 sys_id = 1;  // 神器id
}

message L2C_ArtifactActivate {
  uint32 ret = 1;
  uint32 sys_id = 2;      // 神器id
  Artifact artifact = 3;  // 神器信息
}

message C2L_ArtifactStarUp {
  uint32 sys_id = 1;  // 神器id
}

message L2C_ArtifactStarUp {
  uint32 ret = 1;
  uint32 sys_id = 2;      // 神器id
  Artifact artifact = 3;  // 神器信息
}

message C2L_ArtifactStrength {
  uint32 sys_id = 1;  // 神器id
  uint32 add_lv = 2;  // 增加的等级
}

message L2C_ArtifactStrength {
  uint32 ret = 1;
  uint32 sys_id = 2;      // 神器id
  uint32 add_lv = 3;      // 增加的等级
  Artifact artifact = 4;  // 神器信息
}

message C2L_ArtifactForge {
  uint32 sys_id = 1;  // 神器id
  uint32 add_lv = 2;  // 增加的等级
}

message L2C_ArtifactForge {
  uint32 ret = 1;
  uint32 sys_id = 2;      // 神器id
  uint32 add_lv = 3;      // 增加的等级
  Artifact artifact = 4;  // 神器信息
}

message C2L_ArtifactRevive {
  uint32 sys_id = 1;  // 神器id
}

message L2C_ArtifactRevive {
  uint32 ret = 1;
  uint32 sys_id = 2;          // 神器id
  repeated Resource res = 3;  // 重生返还的资源
  Artifact artifact = 4;      // 重生后的神器信息
}

// 宝石模块
//$<ROP redis|map|g:uint64 >
message GemInfo {
  uint64 id = 1;             //$<ROP unique > //宝石唯一id
  uint32 sys_id = 2;         // 宝石系统id(配置gem_info.xml表里的id)
  uint64 hero_id = 3;        // 英雄id，不为0时，表示被穿戴
  GemAttr gem_attr = 4;      // 宝石永久属性
  GemAttr tmp_gem_attr = 5;  // 宝石临时属性
  int64 score = 6;           // 宝石评分
}

// 宝石属性
message GemAttr {
  repeated uint32 attr_id = 1;  // 宝石基础属性id
  //  repeated uint32 advance_attr_id = 2;  // 宝石高级属性id
}

// 获取宝石列表
message C2L_GetGems {}

message L2C_GetGems {
  uint32 ret = 1;
  repeated GemInfo gems = 2;  // 宝石列表
}

// 宝石穿戴、替换、卸下
message C2L_GemWear {
  uint64 hero_id = 1;  // 英雄id
  uint32 slot = 2;     // 槽位
  uint64 id = 3;       // 宝石id
  uint32 type = 4;     // 1: 穿 2：卸下 3：更换
}

message L2C_GemWear {
  uint32 ret = 1;
  uint64 hero_id = 2;  // 英雄id
  uint32 slot = 3;     // 槽位
  uint64 id = 4;       // 宝石唯一id(客户端传来的)
  uint32 type = 7;
  repeated GemInfo gems = 8;  // 变化的宝石
  repeated Hero heroes = 9;   // 变化的英雄
}

// 宝石合成
message C2L_GemCompose {
  //  repeated uint64 id = 1;             // 来源宝石唯一id
  uint32 sys_id = 2;  // 目标宝石系统id
  //  reserved 1;
  //  reserved "id";
  repeated uint64 ids = 3;            // 来源宝石唯一id
  repeated uint32 inherit_attrs = 4;  // 属性继承
}

message L2C_GemCompose {
  uint32 ret = 1;
  bool is_success = 2;  // 合成宝石是否成功
  GemInfo gem = 3;      // 合成的宝石
  uint32 sys_id = 4;    // 目标宝石系统id
  //  repeated uint64 id = 5;             // 来源宝石的唯一id
  repeated Resource res = 6;  // 失败返回祝福值跟置换粉尘给前端；
  //  reserved 5;
  //  reserved "id";
  repeated uint64 ids = 7;            // 来源宝石的唯一id
  repeated uint32 inherit_attrs = 8;  // 属性继承
}

// 宝石分解
message C2L_GemDecompose {
  repeated uint64 ids = 1;  // 宝石唯一id
}

message L2C_GemDecompose {
  uint32 ret = 1;
  repeated uint64 ids = 2;    // 被分解的宝石唯一id
  repeated Resource res = 3;  // 分解宝石获得的物品
}

// 宝石置换：刷新宝石的属性
message C2L_GemConvert {
  uint64 id = 1;  // 宝石唯一id
  //  bool is_save = 2;                // 是否点了保存
  repeated uint32 lock_attrs = 3;  // 锁住的属性
  uint32 type = 4;                 // 1：置换未保存 2：保存原属性(清除临时属性) 3：保存置换属性
  reserved 2;
  reserved "is_save";
}

message L2C_GemConvert {
  uint32 ret = 1;
  uint64 id = 2;  // 宝石唯一id(客户端传过来的)
  //  bool is_save = 3;                // 是否点了保存
  GemInfo gem = 4;                 // 置换后的宝石
  repeated uint32 lock_attrs = 5;  // 锁住的属性
  uint32 type = 6;                 // 1：置换未保存 2：保存原属性(清除临时属性) 3：保存置换属性
  reserved 3;
  reserved "is_save";
}

message GlobalAttr {
  AttrInfo artifact_attr = 1;  // 神器的全局属性
  reserved 2;
  AttrInfo memory_attr = 3;              // 回忆点的全局属性
  map<uint32, AttrInfo> guild_attr = 4;  // 公会的全局属性: key: hero job
  AttrInfo handbook_attr = 5;            // 图鉴的全局属性
  AttrInfo crystal_blessing_attr = 6;    // 水晶祝福相关全局属性 (2.16.5-cn水晶重构后废弃)
  AttrInfo goddess_contract_attr = 7;    // 契约之所全局属性
  ShareGrowth share_growth = 8;          // 共享养成
  AttrInfo pokemon_attr = 9;             // 宠物的全局属性
}

message GlobalScore {
  int64 artifact_score = 1;
  int64 memory_attr = 2;
  int64 guild_score = 3;
  int64 handbook_score = 4;
  int64 crystal_blessing_score = 5;  // 2.16.5-cn水晶重构后废弃
  int64 goddess_contract_score = 6;
}

message AttrInfo {
  map<uint32, int64> attr = 1;  // key : attr type    value: attr value
  repeated uint32 ps_attr = 2;  // 被动技能
}

message TargetAttr {
  uint32 target = 1;
  int32 attr = 2;
  int64 value = 3;
}

message L2C_OpGlobalAttr {
  GlobalAttr global_attr = 1;
}

message C2L_TestBattleData {
  uint32 formation_id = 1;
}

message L2C_TestBattleData {
  uint32 ret = 1;
}

message C2L_GlobalAttrGet {}

message L2C_GlobalAttrGet {
  uint32 ret = 1;
  GlobalAttr global_attr = 2;
}

message C2L_GlobalAttrScoreGet {}

message L2C_GlobalAttrScoreGet {
  uint32 ret = 1;
  GlobalScore score = 2;
}

// 材料本信息
message TrialInfo {
  uint32 type = 1;   // 材料本类型
  uint32 level = 2;  // 当前所处难度
  uint32 star = 3;   // 当前level的星数
  reserved 4;
  TrialOnHook on_hook = 5;  // 挂机信息
  int64 open_time = 6;      // 开启时间
}

message TrialOnHook {
  int64 on_hook_tm = 1;                 // 挂机开始时间
  int64 last_calc_tm = 2;               // 上一次结算时间
  repeated Resource awards = 3;         // 计算器资源
  int64 last_random_drop_tm = 4;        // 上一次随机掉落的计算时间
  int64 save_random_duration = 5;       // 不足一次的存储时间
  int64 random_drop_start_tm = 6;       // 随机掉落开始时间
  int64 calc_reward_duration = 7;       // 挂机奖励有效时长
  int64 calc_rand_reward_duration = 8;  // 挂机随机奖励有效时长
  repeated int64 left_res_count = 9;    // 挂机产生的资源计算余数
}

message C2L_TrialGetInfo {}

message L2C_TrialGetInfo {
  uint32 ret = 1;
  repeated TrialInfo trial_info = 2;
  map<uint32, uint32> give_count = 3;  // 材料本赠送次数
}

message C2L_TrialFight {
  uint32 type = 1;  // 材料本类型
  bytes client_data = 2;
}

message L2C_TrialFight {
  uint32 ret = 1;
  uint32 type = 2;
  repeated Resource awards = 3;
  string report_id = 4;  // 战报id
  bool win = 5;
  uint32 star = 6;        // 此次战斗是几星
  uint32 give_count = 7;  // 此材料本的赠送次数
}

message C2L_TrialPreview {}

message L2C_TrialPreview {
  uint32 ret = 1;
  repeated Resource awards = 2;
  map<uint32, int64> on_hook_tm = 3;  // 材料本ID 有效挂机时长
}

message C2L_TrialAward {}

message L2C_TrialAward {
  uint32 ret = 1;
  repeated Resource awards = 2;
  map<uint32, int64> trial_start_time = 3;  // 材料本ID 本次挂机开始时间
}

message C2L_TrialSpeed {
  map<uint32, uint32> type_count = 1;  // 材料本ID 次数
}

message L2C_TrialSpeed {
  uint32 ret = 1;
  map<uint32, uint32> type_count = 2;  // 材料本ID 次数
  repeated Resource awards = 3;
}

message C2L_UpdateGems {}

message L2C_UpdateGems {
  uint32 ret = 1;
  repeated GemInfo gems = 2;  // 宝石列表
}

// 悬赏任务
message DispatchTask {
  uint64 id = 1;                 // 悬赏任务id
  uint32 sys_id = 2;             // 量表dispatch_info.xml中的id
  repeated uint64 hero_ids = 3;  // 被派遣的英雄id
  int64 time = 4;                // 接取任务的时间戳
  uint32 dispatch_level = 5;     // 接取任务的悬赏等级
}

message Dispatch {
  uint32 floors_count = 1;           // 当前出红色任务的保底次数
  repeated DispatchTask tasks = 2;   // 悬赏任务列表
  bool first_refresh = 3;            // 首次刷新：必出紫色任务
  DispatchLevelInfo level_info = 4;  // 等级信息
  DispatchCD task_cd = 5;            // 任务 cd
}

message DispatchCD {
  int64 start_time = 1;               // 起始时间
  map<uint32, uint32> round = 2;      // 轮次；根据起始时间和量表中配置的 cd 时间计算得出 key: 任务 sysID
  map<uint32, uint32> max_count = 3;  // 刷出的次数，轮次变了清空重新记录 key: 任务 sysID
}

message DispatchLevelInfo {
  uint32 dispatch_level = 1;                        // 探索等级
  map<uint32, TaskTypeProgress> task_progress = 2;  // 当前升级的任务进度
}

message C2L_DispatchTasks {}
message L2C_DispatchTasks {
  uint32 ret = 1;
  repeated DispatchTask tasks = 2;   // 悬赏任务列表
  DispatchLevelInfo level_info = 3;  // 悬赏任务等级信息
  DispatchCD task_cd = 4;            // cd数据
}

// 接取任务消耗情报点
message C2L_DispatchReceiveTask {
  uint64 id = 1;                 // 悬赏任务id
  repeated uint64 hero_ids = 2;  // 派遣英雄id
}

message L2C_DispatchReceiveTask {
  uint32 ret = 1;
  uint64 id = 2;
  repeated uint64 hero_ids = 3;
  DispatchTask task = 4;
}

message C2L_DispatchGetAwards {
  repeated uint64 id = 1;  // 悬赏任务id
  bool is_speed = 2;       // 是否钻石加速
  bool is_assistant = 3;   // 是否小助手
}

message L2C_DispatchGetAwards {
  uint32 ret = 1;
  repeated uint64 id = 2;
  bool is_speed = 3;
  repeated Resource awards = 4;
  bool is_assistant = 5;
}

message C2L_DispatchRefreshTask {}
message L2C_DispatchRefreshTask {
  uint32 ret = 1;
  repeated DispatchTask tasks = 2;    // 悬赏任务列表
  map<uint32, uint32> max_count = 3;  // cd中的出现最大次数
}

message L2C_DispatchLevelUpdate {  // 探索等级升级下发的协议
  uint32 ret = 1;
  DispatchLevelInfo level_info = 2;
}

message C2L_SetName {
  string name = 1;
}

message L2C_SetName {
  uint32 ret = 1;
  string name = 2;
}

// 图鉴信息
message Handbooks {
  repeated Handbook handbooks = 1;   // todo 0.13.0版本之后可废弃
  repeated HeroHandbook heroes = 2;  // 英雄图鉴
}

message Handbook {
  uint32 type = 1;    // 图鉴类型
  uint32 sys_id = 2;  // 系统id
  int64 time = 3;     // 获得时间
  bool award = 4;     // 奖励是否领取
  bool active = 5;    // 是否激活
}

message HeroHandbook {
  uint32 type = 1;                      // 图鉴类型
  uint32 sys_id = 2;                    // 系统id
  int64 time = 3;                       // 获得时间
  bool award = 4;                       // 奖励是否领取
  bool active = 5;                      // 是否激活
  uint32 current_link_1_star_attr = 6;  // 当前所处的图鉴星级属性
  uint32 link_1_star_attr = 7;          // 可升级的图鉴星级属性ID
  uint32 link_2_attr = 8;               // link2图鉴属性ID （link2即hero_info的link2）
  uint32 link_3_attr = 9;               // link3图鉴属性ID （link3即hero_info的link3）
  uint32 link_4_attr = 10;              // link4图鉴属性ID （link4即hero_info的link4）
  uint32 link_attr_active = 11;         // 羁绊属性激活情况按位存储
  // (第0位: star属性是否激活 第1位:link2是否激活 第2位:link3是否激活 第3位:link4是否激活 第4位：link5是否激活)
  uint32 link_5_attr = 12;          // link5图鉴属性ID
  uint32 emblem_exclusive_lv = 13;  // 符文专属技能等级
}

message Avatar {
  uint32 sys_id = 1;     // 系统表的id
  int64 start_time = 2;  // 头像激活的时间
  int64 end_time = 3;    // 过期时间，暂时用不上
}

message C2L_AvatarGetInfo {}
message L2C_AvatarGetInfo {
  uint32 ret = 1;
  repeated Avatar avatars = 2;
}

message C2L_AvatarSetIcon {
  repeated uint32 ids = 1;  // 0:头像 1:头像框 2:形象  每次都三个一起传
}

message L2C_AvatarSetIcon {
  uint32 ret = 1;
  repeated uint32 ids = 2;
}

message L2C_AvatarNew {
  uint32 ret = 1;
  repeated Avatar avatars = 2;
}

// 纹章
//$<ROP redis|map|em:uint64 >
message EmblemInfo {
  uint64 id = 1;                    //$<ROP unique > //纹章唯一id
  uint32 sys_id = 2;                // 系统id
  uint32 level = 3;                 // 强化等级db
  EmblemBlessingInfo blessing = 4;  // v098修复脚本需要 暂时放开 祝福信息
  uint64 hid = 5;                   // 英雄id
  int64 score = 6;                  // 纹章评分
  uint32 additive_hero = 7;         // 加成英雄
  uint32 skill_id = 8;              // 专属词条ID
  EmblemSuccinctInfo succinct = 9;  // 洗炼信息
}

message EmblemSuccinctInfo {
  uint32 tmp_skill_id = 1;                  // 洗炼产生的临时词条ID
  uint32 tmp_sys_id = 2;                    // 洗炼产生的临时系统ID
  uint32 tmp_affix_id = 3;                  // 洗炼产生的临时词缀ID
  uint32 lock_id = 4;                       // 0-代表没有锁定 1-代表锁定词条 2-代表锁定套装(同时锁定系统ID和词缀ID)
  uint32 affix_id = 5;                      // 词缀ID
  uint32 orange_skill_guarantee_count = 6;  // 橙色词条保底记数
  uint32 red_skill_guarantee_count = 7;     // 红色词条保底记数
  uint32 affix_1_guarantee_count = 8;       // 远古词缀保底记数
  uint32 affix_2_guarantee_count = 9;       // 太古词缀保底记数
}

message EmblemBlessingInfo {
  uint32 level = 1;                       // 祝福等级
  map<uint32, uint32> attr_progress = 2;  // 祝福属性进度
}

// 获取纹章
message C2L_EmblemGet {}

message L2C_EmblemGet {
  uint32 ret = 1;
  repeated EmblemInfo emblems = 2;  // 纹章列表
  repeated uint32 auto_rare = 3;    // 自动分解的装备品质（目前只针个人BOSS）
}

// 纹章穿戴、替换、
message C2L_EmblemWear {
  repeated uint64 id = 1;  // 纹章id
  uint64 hid = 2;          // 纹章对应的英雄id
  uint32 type = 3;         // 操作类型 1：穿,替换 2：脱 3:一件穿，替换 4：一件脱
}

message L2C_EmblemWear {
  uint32 ret = 1;
  repeated uint64 id = 2;           // 纹章id
  uint64 hid = 3;                   // 纹章对应的英雄id
  uint32 type = 4;                  // 操作类型 1：穿,替换 2：脱
  repeated EmblemInfo emblems = 5;  // 变化的纹章
  repeated Hero heroes = 6;         // 变化的英雄
}

// 纹章升级
message C2L_EmblemLevelUp {
  repeated EmblemLevelUpInfo level_up_infos = 1;
}

message L2C_EmblemLevelUp {
  uint32 ret = 1;
  repeated EmblemLevelUpInfo level_up_infos = 2;
  repeated EmblemInfo emblem = 3;  // 纹章
}

message EmblemLevelUpInfo {
  uint64 id = 1;             // 纹章id
  uint32 targets_level = 2;  // 增加的等级
}

// 纹章分解
message C2L_EmblemDecompose {
  repeated uint64 ids = 1;  // 要分解的纹章id
}

message L2C_EmblemDecompose {
  uint32 ret = 1;
  repeated uint64 ids = 2;
  repeated Resource awards = 3;  // 返还资源
}

message L2C_EmblemUpdate {
  uint32 ret = 1;
  repeated EmblemInfo emblems = 2;
}

message EmblemGrowTransfer {
  uint64 grow_emblem = 1;    // 需要养成的纹章
  uint64 revive_emblem = 2;  // 被重生的纹章
}

message C2L_EmblemGrowTransfer {  // 纹章养成转移
  repeated EmblemGrowTransfer transfer_info = 1;
}

message L2C_EmblemGrowTransfer {
  uint32 ret = 1;
  repeated EmblemGrowTransfer transfer_info = 2;
  repeated EmblemInfo emblems = 3;
  repeated Resource left_res = 4;  // 转移后剩余资源
}

message C2L_EmblemSetAutoDecompose {  // 装备自动分解设置
  repeated uint32 auto_rare = 1;      // 需要自动分解的装备品质
}

message L2C_EmblemSetAutoDecompose {
  uint32 ret = 1;
  repeated uint32 auto_rare = 2;
}

message C2L_EmblemCustomize {
  uint32 customize = 1;
  uint32 hero_id = 2;
  uint32 type_id = 3;
  uint32 pos_id = 4;
  uint32 magic_skill = 5;
  uint32 item_id = 6;  // 物品ID
}

message L2C_EmblemCustomize {
  uint32 ret = 1;
  uint32 customize = 2;
  uint32 hero_id = 3;
  uint32 type_id = 4;
  uint32 pos_id = 5;
  uint32 magic_skill = 6;
  repeated Resource awards = 7;  // 返还资源
  uint32 item_id = 8;            // 物品ID
}

// 购买符文栏位
message C2L_EmblemBuySlot {
  uint32 count = 1;
}
message L2C_EmblemBuySlot {
  uint32 ret = 1;
  uint32 count = 2;  // 购买次数（参数回传）
  uint32 slots = 3;  // 最新符文栏位数量
}

// 符文升阶
message C2L_EmblemUpgrade {
  repeated uint64 ids = 1;  // 升阶的纹章id
}

message L2C_EmblemUpgrade {
  uint32 ret = 1;
  repeated uint64 ids = 2;  // 升阶的纹章id
  repeated EmblemInfo emblems = 3;
}

// 符文洗炼
message C2L_EmblemSuccinct {
  uint64 emblem_id = 1;
}

message L2C_EmblemSuccinct {
  uint32 ret = 1;
  uint64 emblem_id = 2;
  EmblemInfo emblem = 3;
}

// 符文洗炼锁定
message C2L_EmblemSuccinctLockOrSave {
  uint64 emblem_id = 1;
  uint32 op_type = 2;  // 操作类型：  1: 锁定 2：保存
  uint32 lock_id = 3;  // 0-代表没有锁定 1-代表锁定词条 2-代表锁定套装(同时锁定系统ID和词缀ID)
}

message L2C_EmblemSuccinctLockOrSave {
  uint32 ret = 1;
  uint64 emblem_id = 2;
  uint32 op_type = 3;  // 操作类型：  1: 锁定 2：保存
  uint32 lock_id = 4;
  EmblemInfo emblem = 5;
}

// 符文洗炼道具合成
message C2L_EmblemSuccinctItemConflate {
  repeated Resource target_resources = 1;
}

message L2C_EmblemSuccinctItemConflate {
  uint32 ret = 1;
  repeated Resource target_resources = 2;
  repeated Resource awards = 3;
}

// 金币宝箱
message GoldChest {
  uint32 sys_id = 1;     // 金币宝箱系统id
  uint32 use_count = 2;  // 使用次数
}

// 点金
message GoldBuy {
  int64 last_time = 1;            // 上一次刷新时间
  repeated GoldChest chests = 2;  // 金币宝箱列表
}

message C2L_GoldBuyGet {}
message L2C_GoldBuyGet {
  uint32 ret = 1;
  repeated GoldChest chests = 2;  // 金币宝箱列表
}

message C2L_GoldBuyGetGold {
  uint32 sys_id = 1;  // 金币宝箱系统id
}
message L2C_GoldBuyGetGold {
  uint32 ret = 1;
  uint32 sys_id = 2;             // 金币宝箱系统id
  repeated Resource awards = 3;  // 获得的金币
}

// 迷宫地图
//$<ROP redis|map|maze_map >
message MazeMap {
  uint32 sys_id = 1;              // 地图量表id
  int64 create_time = 2;          // 地图生成时间
  int64 expire_time = 3;          // 地图到期重置时间
  map<uint32, uint32> grids = 4;  // 格子列表:格子id => 事件type
  uint32 map_type = 5;            //$<ROP unique >   //地图type（1: 新手模板; 2: 轮次模板）
}

// basic grid
message BaseGrid {
  uint32 grid_id = 1;     // 格子id
  uint32 event_id = 2;    // 事件id
  uint32 status = 3;      // 格子状态：0:未走过，1:走过
  uint32 event_type = 4;  // 事件type
}

// 进入迷宫中的英雄或怪物的血量信息
message MazeHp {
  uint64 id = 1;      // 英雄id or 怪物sys_id
  uint32 hp_pct = 2;  // 英雄当前血量比；万分比
}

// 匹配的玩家敌人
message MazeEnemy {
  uint64 user_id = 1;          // 新生成的玩家唯一id（调用通用阵容接口返回的）
  repeated MazeHp enemys = 2;  // 敌人的血量比信息
  uint64 old_user_id = 3;      //  从排行榜获取的玩家id（去重用, 前端不用这个）
  uint32 monster_group = 4;    // 怪物组；有值时，表示匹配的是机器人
}

// 地图格子
message MazeGrid {
  BaseGrid grid = 1;               // 基础格子
  MazeEnemy enemy = 2;             // 敌人
  repeated uint32 soul_buffs = 3;  // 灵魂祭坛 要保存随机出来的3个buff
  uint32 question = 4;             // 选择、猜剪影、魔晶矿事件的问题id
  uint32 box_id = 5;               // 魔盒id
  uint32 client_select_id = 6;     // 选择、猜剪影、魔晶矿、魔盒事件成功时记录，maze_answer_info的id
  // 当触发了祝福者事件后，诅咒地块事件有值，maze_curse_info 的id
  MazeBeSelectBuff be_select_buff = 7;  // 待选择的BUFF
}

// 地图中获得的buff（只在地图中生效）
message MazeBuff {
  uint32 type = 1;
  uint32 value = 2;
  uint32 count = 3;
  uint32 battle_limit = 4;  // 战斗场次限制
}

message MazeTask {
  uint32 score = 1;                                 // 任务积分
  map<uint32, bool> task_awarded = 2;               // 已领取过奖励的taskID key:maze_task_info.xml的id
  uint32 level = 3;                                 // 任务level
  map<uint32, TaskTypeProgress> task_progress = 4;  // 任务进度
}

// 迷宫中缓存的玩家信息
//$<ROP redis|map|mp:uint64 >
message MazePlayer {
  uint64 id = 1;                        //$<ROP unique >  // 玩家id
  uint32 sys_id = 2;                    // 地图量表id
  uint32 cur_grid_id = 3;               // 当前格子id
  int64 expire_time = 4;                // 地图过期时间
  int64 max_power = 5;                  // 第一次进入地图时，玩家的历史最高战力
  uint32 map_level = 6;                 // 地图难度等级
  repeated Resource award_record = 8;   // 奖励记录
  repeated MazeHp heroes = 9;           // 英雄血量比（只存有变化的）
  map<uint32, MazeGrid> grids = 10;     // 所有的格子信息
  uint32 win_boss_num = 11;             // 打赢Boss的次数
  uint32 buy_revive_count = 12;         // 购买复生神像的次数
  uint32 round_num = 13;                // 迷宫的轮次(不重置)
  repeated MazeBuff buffs = 14;         // 获得的buff（迷宫中生效）
  repeated Resource items = 15;         // 获得的道具资源（迷宫中生效）
  uint32 guard_max_awarded = 16;        // 宝库守卫最大领奖记录
  MazeTask task = 17;                   // 任务相关
  bool blessing_event = 18;             // 触发了祝福者事件
  repeated uint32 alter_grid_ids = 19;  // 使用真视之眼出现的灵魂祭坛grid_id
  bool used_trap_remover = 20;          // 使用了陷阱拆除器
  uint32 guard_battle_count = 21;       // 守卫战斗次数
  repeated uint32 pass_num = 22;        // 通过不同level maze的次数. index: 表示 maze level
  reserved 23;
  uint32 guard_max_hurt = 24;            // 守卫本轮最高伤害
  bool is_sweep = 25;                    // 是否扫荡
  repeated uint32 max_guard_hurts = 26;  // 守卫历史最高伤害；index: 表示 maze level
}

// 获取迷宫信息
message C2L_MazeGetMap {
  uint32 level = 1;
}

message L2C_MazeGetMap {
  uint32 ret = 1;
  int64 expire_time = 2;       // 地图到期重置时间
  MazePlayer maze_player = 3;  // 玩家保存的地图格子信息
  uint32 level = 4;            // maze level
}

// 迷宫事件
message C2L_MazeTriggerEvent {
  uint32 grid_id = 1;  // 目标格子坐标
  uint32 param = 2;    // 灵魂祭坛: param = buffID
  // 选择、猜剪影、获得道具: param = answerID
  // 生命之泉: 1 - 复活 ;  2 - 治愈
  // 魔盒：1 - 放弃 ;  2 - 开启
  // 敌人事件(type=3,4,5)：督军事件在地图上可见时有值 param = 1
  repeated uint64 hero_ids = 3;  // 魔盒: hero_id
  bytes client_data = 4;
}

message L2C_MazeTriggerEvent {
  uint32 ret = 1;
  uint32 param = 2;                   // 灵魂祭坛、选择事件时有值：选中的buffId或问题答案
  string report_id = 3;               // 战报id
  bool win = 4;                       // 战斗结果
  repeated Resource awards = 5;       // 获得的资源（战斗事件）
  repeated MazeHp heroes = 6;         // 英雄信息（上阵的英雄血量比）
  repeated MazeBuff buff_change = 7;  // buff变化后的值(迷宫中生效)
  repeated Resource item_change = 8;  // 道具使用后变化后的值
  bool success = 9;                   // 选择事件的答案是否成功
  MazeGrid grid = 10;
  repeated uint64 hero_ids = 11;
  repeated Resource monthly_card_awards = 12;  // 月卡奖励
  uint32 guard_max_awarded = 13;               // 宝库守卫最大领奖记录
  bool blessing_event = 14;                    // 触发了祝福者事件
  uint32 guard_battle_count = 15;              // 守卫战斗次数
  uint32 guard_max_hurt = 16;                  // 守卫本轮最高伤害
}

message MazeBeSelectBuff {
  repeated uint32 success = 1;
  repeated uint32 failed = 2;
}

// 复活英雄: 所有的英雄血量比拉满
message C2L_MazeRecoveryHero {}
message L2C_MazeRecoveryHero {
  uint32 ret = 1;
}

// 进入格子：获取匹配的敌人信息、灵魂祭坛随机的buff、选择事件随机的问题Id
message C2L_MazeGetGrid {
  uint32 grid_id = 1;
}

message L2C_MazeGetGrid {
  uint32 ret = 1;
  MazeGrid grid = 2;
}

// 购买复生神像
message C2L_MazeBuyRevive {
  uint32 buy_count = 1;  // 购买次数
}
message L2C_MazeBuyRevive {
  uint32 ret = 1;
  uint32 buy_count = 2;
  uint32 cur_buy_count = 3;      // 已经购买的次数
  repeated Resource awards = 4;  // 获得的复生神像资源
}

// 迷宫 - 使用道具
message C2L_MazeUseItem {
  uint32 item_id = 1;  // 道具id
  uint32 grid_id = 2;  // 格子id: 使用裁决之剑,真视之眼道具时有值
}

message L2C_MazeUseItem {
  uint32 ret = 1;
  uint32 item_id = 2;                 // 道具id
  uint32 grid_id = 3;                 // 格子id: 使用裁决之剑道具时有值
  repeated Resource item_change = 4;  // 道具变化
  repeated MazeBuff buff_change = 5;  // buff变化
  repeated Resource awards = 6;
  repeated MazeGrid grids = 7;
  repeated Resource monthly_card_awards = 8;  // 月卡奖励
}

// 迷宫 - 领取任务奖励
message C2L_MazeTaskReceiveAward {
  //  uint32 task_id = 1;
  reserved 1;
  reserved "task_id";
  repeated uint32 task_ids = 2;
}

message L2C_MazeTaskReceiveAward {
  uint32 ret = 1;
  //  uint32 task_id = 2;
  repeated Resource task_awards = 3;   // 任务奖励
  repeated Resource level_awards = 4;  // 等级奖励
  uint32 current_score = 5;            // 当前积分
  reserved 2;
  reserved "task_id";
  repeated uint32 task_ids = 6;
}

// 迷宫-更新任务进度
message L2C_MazeTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
}

// 迷宫 - 获取选择模板等级时需要的数据
message C2L_MazeGetSelectMapData {}
message L2C_MazeGetSelectMapData {
  uint32 ret = 1;
  uint32 map_level = 2;
  repeated uint32 pass_num = 3;  // 通过不同level maze的次数. index: 表示 maze level
  int64 max_power = 4;
  int64 map_expire_time = 5;            // 模板的过期时间
  repeated uint32 max_guard_hurts = 6;  // 守卫历史最高伤害；index: 表示 maze level
}

// 迷宫 - 选择buff
message C2L_MazeSelectBuff {
  uint32 grid_id = 1;
  uint32 buff_id = 2;
}

message L2C_MazeSelectBuff {
  uint32 ret = 1;
  uint32 grid_id = 2;
  uint32 buff_id = 3;
  repeated MazeBuff buffs = 4;
  MazeGrid grid = 5;
}

// 迷宫 - 扫荡
message C2L_MazeSweep {
  uint32 level = 1;
}

message L2C_MazeSweep {
  uint32 ret = 1;
  uint32 level = 2;
  repeated Resource awards = 3;
  repeated MazeGrid grids = 4;
}

// 排行成就列表展示
message C2L_RankAchieveList {
  uint32 rank_id = 1;  // 排行榜id
}

message RankAchieve {
  uint32 id = 1;   // 成就目标量表id
  uint64 uid = 2;  // 首位达成目标的玩家id
}

message L2C_RankAchieveList {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated RankAchieve users = 3;    // 首个达成成就的玩家列表
  repeated uint32 received_ids = 4;  // 已领奖励的目标id列表
  repeated UserSnapshot users_snapshot = 5;
}

// 领取排行成就奖励
message C2L_RankAchieveRecvAward {
  repeated uint32 ids = 1;  // 排行成就量表id列表
}

message L2C_RankAchieveRecvAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;  // 排行成就量表id列表
  repeated Resource awards = 3;
  uint32 rank_id = 4;  // 排行榜id
}

// 排行成就达成通知
message L2C_RankAchieveNotify {
  uint32 id = 1;  // 排行成就量表id
}

message GuildSnapshot {
  uint64 id = 1;
  string name = 2;
  uint32 level = 3;
  uint32 badge = 4;           // 徽章
  uint32 member_count = 5;    // 人数
  string language = 7;        // 语言
  uint32 activity_point = 8;  // 7日活跃度
  uint32 join_type = 9;       // 加入类型
  uint32 label = 10;          // 标签
  uint32 lv_limit = 11;
  int64 power_limit = 12;
  uint32 dungeon_star = 13;         // 公会副本积分
  repeated int64 expire_time = 14;  // 0:图案,1:背景过期时间
  repeated GuildMedal medals = 15;  // 取每个功勋下的第一个勋章
  uint32 activity_member_cnt = 16;  // 活跃成员人数
}

message GuildInfo {
  uint64 id = 1;
  string name = 2;                  // 昵称
  uint32 badge = 3;                 // 徽章
  uint32 level = 4;                 // 等级
  uint32 exp = 5;                   // 公会经验
  string notice = 6;                // 公告
  uint32 member_count = 7;          // 人数
  uint32 join_type = 8;             // 入会条件(0:自动加入 1:需要申请 2:拒绝加入)
  int64 update_name_tm = 9;         // 更新公会名的时间
  uint32 donate_point_num = 10;     // 今日公会捐献进度值
  uint32 donate_person_num = 11;    // 今日公会捐赠人数
  uint64 notice_id = 12;            // 公会公告ID
  string language = 13;             // 语言
  uint32 label = 14;                // 标签
  uint32 lv_limit = 15;             // 等级限制
  int64 power_limit = 16;           // 战力限制
  uint32 activity_point = 17;       // 7日活跃度
  uint32 dungeon_star = 18;         // 公会副本积分
  bool dungeon_sign_up = 19;        // 副本是否报名
  repeated int64 expire_time = 20;  // 0:图案,1:背景过期时间
  uint32 kick_count = 21;           // 当天踢玩家次数
  uint32 division = 22;             // 公会段位
  uint64 guild_sid = 23;            // 建服ID
  repeated GuildMedal medals = 24;  // 激活的勋章
  int64 create_tm = 25;             // 创建时间
}

// 公会日志
//$<ROP redis|map|guild_log:uint64>
message GuildLogInfo {
  uint32 id = 1;            //$<ROP unique>
  uint32 log_type = 2;      // 日志类型
  int64 create_tm = 3;      // 发生时间
  string execute_name = 4;  // 执行者名称
  string accept_name = 5;   // 接受者名称
}

// 工会捐赠日志
//$<ROP redis|map|guild_donate_log:uint64>
message GuildDonateLogInfo {
  uint32 id = 1;               //$<ROP unique>
  string donate_name = 2;      // 捐赠者名字
  repeated Resource cost = 3;  // 消耗的资源
  int64 time = 4;              // 捐赠时间
}

message UserGuild {
  bool sign_in = 1;  // 今日是否签到
  uint32 grade = 2;  // 职位 1-leader(会长)  2-deputy(副会长)  3-member(成员)
  repeated uint64 apply_ids = 3;
  int64 leave_tm = 4;                             // 离开公会时间
  int64 send_mail_tm = 5;                         // 发送邮件时间
  int64 recruit_tm = 6;                           // 招募cd time
  uint32 donate_count = 7;                        // 捐赠次数
  repeated uint32 donate_award_ids = 8;           // 今日领取的捐赠ID
  uint32 weekly_activity_point = 9;               // 玩家七天的总活跃度 ps:上周三至本周二的活跃度
  string approver = 10;                           // 批准加入的管理员的名字
  int64 join_tm = 11;                             // 加入公会时间
  uint64 dungeon_chat_room_id = 12;               // 副本聊天房间ID
  repeated GuildLeaveCount guild_level_cnt = 14;  // 离开公会次数
  repeated uint32 received_divisions = 15;        // 赛季已领奖段位
  repeated GuildMedalLiked medal_liked = 17;      // 已送花勋章
}

message GuildLeaveCount {
  uint64 gid = 1;
  uint32 count = 2;
}

message C2L_GuildCreate {
  string name = 1;
  uint32 badge = 2;
  string language = 3;
  uint32 join_type = 4;    // 0:自动加入 1：需要审核 2：拒绝加入
  uint32 lv_limit = 5;     // 等级限制
  int64 power_limit = 6;   // 战力限制
  uint32 label = 7;        // 标签ID
  string declaration = 8;  // 宣言
}

message L2C_GuildCreate {
  uint32 ret = 1;
  string name = 2;
  uint32 badge = 3;
  string language = 4;
  uint32 join_type = 5;   // 0:自动加入 1：需要审核 2：拒绝加入
  uint32 lv_limit = 6;    // 等级限制
  int64 power_limit = 7;  // 战力限制
  uint32 label = 8;
  string declaration = 9;
  GuildInfo guild = 10;
}

message C2L_GuildList {
  uint32 page = 1;
  bool screen = 2;                      // 是否筛选
  string language = 3;                  //
  uint32 join_type = 4;                 //
  repeated uint32 member_count = 5;     // 公会人数区间  (列 [10,2]  上限是10，下限是2）
  uint32 label = 6;                     // 0 代表标签不做为筛选条件
  repeated uint32 level = 7;            // 公会等级区间 （列 [10,2]  上限是10，下限是2）
  uint32 division = 8;                  // 段位
  repeated uint32 activity_member = 9;  // 活跃人数区间 （列 [10,2]  上限是10，下限是2）
  bool transfer = 10;                   // 是否转会推荐列表
  bool combine = 11;                    // 是否合并推荐列表
}

message L2C_GuildList {
  uint32 ret = 1;
  uint32 page = 2;
  bool screen = 3;      // 是否筛选
  string language = 4;  //
  uint32 join_type = 5;
  repeated uint32 member_count = 6;  // 公会人数区间 (列 [10,2]  上限是10，下限是2）
  uint32 label = 7;
  repeated uint32 level = 8;  // 公会等级区间 （列 [10,2]  上限是10，下限是2）
  uint32 total = 9;           // 公会总数
  repeated GuildSnapshot guilds = 10;
  uint32 division = 11;
  repeated uint64 apply_ids = 12;
  repeated uint32 activity_member = 13;             // 活跃人数区间 （列 [10,2]  上限是10，下限是2）
  bool transfer = 14;                               // 是否转会推荐列表
  bool combine = 15;                                // 是否合并推荐列表
  repeated GuildCombineStatus combine_status = 16;  // 合并请求状态
}

message C2L_GuildGetMyInfo {}

message L2C_GuildGetMyInfo {
  uint32 ret = 1;
  GuildInfo guild = 2;  // 这里的GuildInfo只会给部分信息，全部信息在GuildMainInfo给
  UserGuild user = 3;
}

message GuildLogs {
  repeated GuildLogInfo logs = 1;
}

message GuildLogDeletes {
  repeated uint32 ids = 2;
}

message C2L_GuildLogList {}

message L2C_GuildLogList {
  uint32 ret = 1;
  repeated GuildLogInfo logs = 2;
}

message C2L_GuildManagerMember {
  uint64 id = 1;
  uint32 type = 2;  // 0-设为会长 1-更改副会长(设为副会长、若已经是副会长则撤销)
  // 2-踢出公会
}

message L2C_GuildManagerMember {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 type = 3;
  uint64 leader = 4;
  repeated uint64 deputy = 5;
  repeated uint64 regiment = 6;
}

message C2L_GuildModifyInfo {
  uint32 badge = 1;
  uint32 join_type = 2;
  string declaration = 3;
  string language = 4;
  uint32 lv_limit = 5;    // 等级限制
  int64 power_limit = 6;  // 战力限制
  uint32 label = 7;       // 标签
}

message L2C_GuildModifyInfo {
  uint32 ret = 1;
  uint32 badge = 2;
  uint32 join_type = 3;
  string declaration = 4;
  string language = 5;
  uint32 lv_limit = 6;    // 等级限制
  int64 power_limit = 7;  // 战力限制
  uint32 label = 8;       // 标签
}

message C2L_GuildSetName {
  string name = 1;
}

message L2C_GuildSetName {
  uint32 ret = 1;
  string name = 2;
  int64 update_name_tm = 3;  // 更新公会名的时间
}

message C2L_GuildModifyNotice {
  string notice = 1;
}

message L2C_GuildModifyNotice {
  uint32 ret = 1;
  string notice = 2;
  uint64 notice_id = 3;
}

message C2L_GuildApplyList {
  uint32 apply_type = 1;  // 申请类型 common.GUILD_APPLY_TYPE
}

message L2C_GuildApplyList {
  uint32 ret = 1;
  uint32 apply_type = 2;
  repeated UserSnapshot users = 3;
  repeated GuildCombineApplyItem combine_apply_list = 4;    // 我方申请合并列表
  repeated GuildCombineApplyItem combine_applied_list = 5;  // 其他公会向我方申请合并列表
}

message C2L_GuildApplyRatify {
  repeated uint64 id = 1;
  bool accept = 2;  // true:同意申请  false:拒绝申请
}

message L2C_GuildApplyRatify {
  uint32 ret = 1;
  repeated uint64 id = 2;
  bool accept = 3;
  repeated uint64 delete = 4;  // 需要从审核列表中去掉的Id
}

message C2L_GuildUserApply {
  uint64 id = 1;
  uint32 type = 2;  // 0-加入公会 1-申请公会 2-取消申请 3-快速加入 4-快速转会
}

message L2C_GuildUserApply {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 type = 3;
  repeated uint64 guilds = 4;                // 玩家的申请列表
  repeated GuildLeaveCount leave_count = 5;  // 离开公会的次数
  // uint64 old_gid = 5; // 老公会id
}

// 公会合并请求操作(发起请求、取消请求)
message C2L_GuildCombineApply {
  uint64 gid = 1;   // 目标公会id
  uint32 type = 2;  // common.GUILD_COMBINE_APPLY_TYPE
}

message L2C_GuildCombineApply {
  uint32 ret = 1;
  uint64 gid = 2;
  uint32 type = 3;
  repeated GuildCombineApplyItem combine_apply_list = 4;    // 我方公会的申请列表
  repeated GuildCombineApplyItem combine_applied_list = 5;  // 其他公会向我方申请合并列表
}

message GuildCombineApplyItem {
  uint64 source_gid = 1;    // TODO 发起方公会id，暂时先留着
  GuildSnapshot guild = 2;  // 对方公会信息（注意不是目标方公会，因为申请列表是对于我方公会而言的，如果目标方是我方公会，显示对方公会）
  uint32 type = 3;          // 合并申请类型 common.GUILD_COMBINE_APPLY_TYPE
  int64 expire_time = 4;    //  申请过期时间
}

// 公会合并确认
message C2L_GuildCombineCheck {
  uint64 source_gid = 1;  // 发起方公会id（不一定是我方公会）
  uint64 target_gid = 2;  // 目标方公会id
  uint32 apply_type = 3;  // 合并申请类型 common.GUILD_COMBINE_APPLY_TYPE
}

message L2C_GuildCombineCheck {
  uint32 ret = 1;
  uint64 source_gid = 2;  // 发起方公会id
  uint64 target_gid = 3;  // 目标方公会id
  uint32 apply_type = 4;
  repeated UserSnapshot join_users = 5;     // 加入的玩家
  repeated UserSnapshot dismiss_users = 6;  // 遣散的玩家
}

message C2L_GuildCombineRatify {
  repeated uint64 gids = 1;
  uint32 apply_type = 2;  // 合并申请类型 common.GUILD_COMBINE_APPLY_TYPE（同意申请时才发送）
  bool accept = 3;        // true:同意申请  false:拒绝申请
}

message L2C_GuildCombineRatify {
  uint32 ret = 1;
  repeated uint64 gids = 2;
  uint32 apply_type = 3;
  bool accept = 4;
  repeated GuildCombineApplyItem combine_apply_list = 5;    // 我方公会的申请列表
  repeated GuildCombineApplyItem combine_applied_list = 6;  // 其他公会向我方申请合并列表
}

message C2L_GuildSendMail {
  string title = 1;
  string content = 2;
}

message L2C_GuildSendMail {
  uint32 ret = 1;
  string title = 2;
  string content = 3;
}

message C2L_GuildQuit {}

message L2C_GuildQuit {
  uint32 ret = 1;
  repeated GuildLeaveCount leave_count = 2;  // 离开公会的次数
}

message C2L_GuildDisband {}

message L2C_GuildDisband {
  uint32 ret = 1;
  repeated GuildLeaveCount leave_count = 2;  // 离开公会的次数
}

message GuildCombineStatus {
  uint64 gid = 1;
  uint32 request_status = 2;  // 申请状态 common.GUILD_COMBINE_STATUS
  uint32 invite_status = 3;   // 邀请状态 common.GUILD_COMBINE_STATUS
}

message C2L_GuildSearch {
  uint64 id = 1;
  string name = 2;
  bool is_combine = 3;  // 是否公会合并界面
}

message L2C_GuildSearch {
  uint32 ret = 1;
  uint64 id = 2;
  string name = 3;
  GuildSnapshot guild = 4;
  bool is_combine = 5;                             // 是否公会合并界面
  repeated GuildCombineStatus combine_status = 6;  // 合并请求状态
}

message GuildMemberInfo {
  UserSnapshot info = 1;
  uint32 grade = 2;
  uint32 weekly_activity_point = 3;  // 活跃度
}

message C2L_GuildGetMembers {
  uint64 id = 1;
}

message L2C_GuildGetMembers {
  uint32 ret = 1;
  uint64 id = 2;
  repeated GuildMemberInfo members = 3;  // 公会所有成员
}

message C2L_GuildGetDeclaration {
  uint64 id = 1;
}

message L2C_GuildGetDeclaration {
  uint32 ret = 1;
  string declaration = 2;
}

message L2C_GuildNotify {
  uint32 ret = 1;
  uint64 id = 2;             // 公会id
  bool be_leader = 3;        // 成为会长
  bool be_deputy = 4;        // 成为副会长
  bool kicked = 5;           // 被踢
  bool approve = 6;          // 申请公会通过
  bool recall = 7;           // 被罢免职位
  bool new_apply = 8;        // 有新申请，推送给公会管理者
  bool be_regiment = 9;      // 成为团长
  bool combine_join = 10;    // 通过公会合并加入
  bool combine_kicked = 11;  // 由于公会合并被踢
  bool combine_apply = 12;   // 有合并请求
}

message L2C_GuildUpdateInfo {
  uint32 ret = 1;
  uint32 level = 2;              // 公会最新等级
  uint32 exp = 3;                // 最新经验
  uint32 donate_point_num = 4;   // 今日公会捐献进度值
  uint32 donate_person_num = 5;  // 今日公会捐赠人数
}

message C2L_GuildSendRecruitMsg {}
message L2C_GuildSendRecruitMsg {
  uint32 ret = 1;
}

message C2L_GuildDonate {
  uint32 id = 1;
}

message L2C_GuildDonate {
  uint32 ret = 1;
  uint32 id = 2;                     // 捐赠表ID
  uint32 donate_count = 3;           // 今日已捐献次数
  repeated Resource awards = 4;      // 玩家奖励
  uint32 level = 5;                  // 最新公会等级
  uint32 exp = 6;                    // 最新公会经验
  uint32 donate_point_num = 7;       // 最新公会进度值
  uint32 donate_person_num = 8;      // 今日公会捐赠人数
  uint32 weekly_activity_point = 9;  // 每周活跃值总和
  uint32 guild_activity = 10;        // 公会的活跃度
}

message C2L_GuildGetDonateAward {
  repeated uint32 ids = 1;  // 领奖ID
}

message L2C_GuildGetDonateAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;               // 领奖ID
  repeated uint32 donate_award_ids = 3;  // 已领取的
  repeated Resource awards = 4;          // 玩家奖励
}

message GuildDonateLogs {
  repeated GuildDonateLogInfo logs = 1;
}

message GuildDonateDeletes {
  repeated uint32 ids = 2;
}

message C2L_GuildDonateLogList {}

message L2C_GuildDonateLogList {
  uint32 ret = 1;
  repeated GuildDonateLogInfo logs = 2;
}

message C2L_GuildRank {
  uint32 rank_id = 1;
}

message L2C_GuildRank {
  uint32 ret = 1;
  uint32 rank_id = 2;  // 0: 等级 1: 段位 2：活跃度
  repeated RankValue list = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
}

message C2L_GuildMainInfo {}

message L2C_GuildMainInfo {
  uint32 ret = 1;
  GuildInfo guild = 2;
  repeated GuildMemberInfo members = 3;
  UserGuild user = 4;
  uint32 dungeon_season = 5;  // 副本当前赛季
  uint32 dungeon_round = 6;   // 副本当前轮次
  repeated GuildChestFinishRecv guild_chest_finish_recv = 7;
  bool have_transfer = 8;  // 有转会的推荐公会
  bool have_combine = 9;   // 有合并的推荐公会（仅推送给会长）
}

message C2L_GuildListGetDetail {
  uint64 gid = 1;
  uint32 partition = 2;  // 小战区
}

message L2C_GuildListGetDetail {
  uint32 ret = 1;
  uint64 gid = 2;
  string declaration = 3;
  repeated GuildMemberInfo members = 4;
  GuildSnapshot snapshot = 5;
}

message C2L_GuildGetBadgeList {}  //  获取公会已解锁徽章

message L2C_GuildGetBadgeList {
  uint32 ret = 1;
  repeated Avatar list = 2;  // 复用Avatar结构
}

// 向公会官员同步当日踢人次数
message L2C_GuildSyncKickCount {
  uint32 ret = 1;
  uint32 kick_count = 2;  // 当日踢人次数
}

message C2L_GuildGetMedals {
  uint32 type = 1;  // type 是功勋id。 为0就获取激活的所有勋章。不为0就获取对应功勋下的所有勋章
}

message L2C_GuildGetMedals {
  uint32 ret = 1;
  uint32 type = 2;
  repeated GuildMedal list = 3;
  uint32 liked_count = 4;  // 被送花次数
  repeated string top_names = 5;
}

message C2L_GuildMedalLike {
  uint32 medal_id = 1;  // 功勋id
  uint64 uid = 2;
}

message L2C_GuildMedalLike {
  uint32 ret = 1;
  uint32 medal_id = 2;  // 功勋id
  uint64 uid = 3;
  uint32 liked = 4;
}

message GuildMedal {
  uint32 medal_id = 1;
  uint64 uid = 2;
  string name = 3;
  uint32 base_id = 4;              // 头像相关
  repeated int64 expire_time = 5;  // 0:头像,1:头像框过期时间
  uint32 liked_count = 6;          // 总的点赞次数
  bool new = 7;
  uint32 param1 = 8;  // 排序参数1
  uint64 param2 = 9;  // 排序参数2
}

message GuildMedalLiked {
  uint64 uid = 1;
  uint32 medal_id = 2;
}

message C2L_GuildGetDivisionAwardInfo {}  //  获取公会段位奖励相关信息

message L2C_GuildGetDivisionAwardInfo {
  uint32 ret = 1;
  uint32 current_division = 2;
  uint32 season_top_division = 3;
  uint32 division_rank = 4;
  repeated uint32 received_divisions = 5;  // 赛季已领奖段位
}

// 个人boss数据 该玩法存的是所有已通关的数据并非只存最高层。
//$<ROP redis|map|mirage:uint64 >
message Mirage {
  uint32 sys_id = 1;            //$<ROP unique> //量表中的hurdle_id
  repeated uint32 affixes = 2;  // 词缀id列表
  //  map<uint32, bool> star_award = 3;  //星数奖励信息
  //  uint32 best_star = 4;              //当前关卡取得的最大星数
  reserved 3, 4;
  reserved "star_award", "best_star";
  bool is_first_pass = 5;     // 是否首次通关
  bool is_receive_award = 6;  // 是否已领取过一次性奖励
}

// 副本列表信息
message C2L_MirageList {}

message L2C_MirageList {
  uint32 ret = 1;
  map<uint32, Mirage> current_floors = 2;  // 副本类型 => 当前层信息
  uint32 sky_bonus_fight_count = 3;        // 天空boss - 赠送的挑战次数
  //  uint32 hell_bonus_fight_count = 4;       //地狱boss - 赠送的挑战次数
  reserved 4;
  reserved "hell_bonus_fight_count";
}

// 单个副本详情数据展示
message C2L_MirageDetail {
  uint32 copy_id = 1;  // 副本id
}

message L2C_MirageDetail {
  uint32 ret = 1;
  uint32 copy_id = 2;          // 副本id
  repeated Mirage floors = 3;  // 当前副本内，全部已打层数信息
}

// 战斗
message C2L_MirageFight {
  uint32 sys_id = 1;  // 关卡量表hurdle_id
  bytes client_data = 2;
}

message L2C_MirageFight {
  uint32 ret = 1;
  uint32 sys_id = 2;    // 关卡量表hurdle_id
  bool win = 3;         // 是否胜利
  bool first_pass = 4;  // 是否是首次过关
  //  bool best = 5;                  //是否是最好成绩(最高星数)
  //  uint32 star = 6;                //星数
  uint32 exceed_num = 7;          // 超过xx.yy%玩家，客户端使用时需/100，再加%
  repeated Resource awards = 8;   // 胜利奖励
  string report_id = 9;           // 战报id
  uint32 bonus_fight_count = 10;  // 赠送的挑战次数；根据类型来定：天空Boss或地狱Boss
  reserved 5, 6;
  reserved "best", "star";
}

// 领取奖励
message C2L_MirageReceiveAward {
  repeated uint32 sys_ids = 1;  // 关卡量表hurdle_id
  uint32 copy_id = 2;           // 副本id
}

message L2C_MirageReceiveAward {
  uint32 ret = 1;
  repeated uint32 sys_ids = 2;   // 关卡量表hurdle_id
  repeated Resource awards = 3;  // 星数对应的奖励
  uint32 copy_id = 4;            // 副本id
}

// 保存词缀
message C2L_MirageSaveAffixes {
  uint32 sys_id = 1;            // 关卡量表hurdle_id
  repeated uint32 affixes = 2;  // 已选词缀id列表
}

message L2C_MirageSaveAffixes {
  uint32 ret = 1;
  uint32 sys_id = 2;            // 关卡量表hurdle_id
  repeated uint32 affixes = 3;  // 已选词缀id列表
}

// 战力碾压
message C2L_MiragePowerCrush {
  uint32 sys_id = 1;  // 关卡量表hurdle_id
}

message L2C_MiragePowerCrush {
  uint32 ret = 1;
  uint32 sys_id = 2;
  repeated Resource awards = 4;
  bool first_pass = 5;  // 是否是首次过关
  //   bool best = 6;                 //是否是最好成绩(最高星数)
  //   uint32 star = 7;               //星数
  uint32 exceed_num = 8;         // 超过xx.yy%玩家，客户端使用时需/100，再加%
  uint32 bonus_fight_count = 9;  // 赠送的挑战次数；根据类型来定是天空Boss后地狱Boss
  reserved 6, 7;
  reserved "best", "star";
}

// 扫荡
message C2L_MirageSweep {
  uint32 sweep_type = 1;  // 1-normal 2-assistant refer to common.MIRAGE_SWEEP_TYPE
  map<uint32, uint32> id_count = 2;
}

message L2C_MirageSweep {
  uint32 ret = 1;
  uint32 sweep_type = 2;
  repeated Resource awards = 3;
  map<uint32, uint32> id_count = 4;
}

message C2L_MirageBuyCount {
  uint32 buy_count = 1;
}

message L2C_MirageBuyCount {
  uint32 ret = 1;
  uint32 buy_count = 2;
}

message C2L_MirageTestDrop {
  uint32 sys_id = 1;  // 关卡量表hurdle_id
  uint32 count = 2;
}

message L2C_MirageTestDrop {
  uint32 ret = 1;
  map<uint32, MirageHeroNum> epic_data = 2;  // pos => data 橙色符文
}

message MirageHeroNum {
  map<uint32, uint32> hero_id_num = 1;  // hero_sys_id => num
}

// 密林数据
//$<ROP redis|map|forest >
message Forest {
  uint64 id = 1;                                  //$<ROP unique> //玩家id
  int64 power = 2;                                // 玩家战力
  uint32 level = 3;                               // 密林等级
  uint64 exp = 4;                                 // 密林经验
  map<uint32, uint64> collect = 5;                // 采集队伍信息 采集位id=>英雄id TODO 待删除
  ForestTree peaceful_tree = 6;                   // 和平树的相关数据
  ForestTree pvp_tree = 7;                        // pvp树的相关数据
  uint32 guidance_feed_step = 8;                  // 引导喂哥布林的次数，用于新手引导，达到最大步骤后停止更新
  bool used_free_speed_grow = 9;                  // 是否已使用过免费加速生长（首次加速生长免费，用于引导）
  map<uint32, bool> received_lv_award_list = 10;  // 等级奖励，已领取列表
  repeated uint64 collect_hids = 11;              // 采集位英雄id列表
}

// 密林 - 种子和树的相关数据
message ForestTree {
  int64 start_tm = 1;              // 种树开始时间
  int64 end_tm = 2;                // 种树结束时间
  uint32 current_goblin = 3;       // 当前哥布林id
  uint32 next_goblin = 4;          // 下个哥布林id
  uint32 score = 5;                // 种子分数
  map<uint32, uint32> effect = 6;  // 采集位加成效果 类型=>效果 TODO 待删除
  uint32 looted_count = 7;         // 被抢次数（仅pvp_tree有此数据）
  bool need_harvest = 8;           // 是否使用道具加速成熟，待收获
  repeated uint64 robbers = 9;     // 掠夺者id（仅pvp_tree有此数据）
  bool changed = 10;               // 是否已更换过哥布林
  uint32 stage = 11;               // 当前阶段 0-待献祭 1-献祭中 2-生长中
  uint32 log_index_id = 12;        // 日志序号Id（仅pvp_tree有此数据）
  bool new_log_tip = 13;           // 新日志提示（仅pvp_tree有此数据）
  uint32 collect_total_star = 14;  // 采集位总星数
}

//$<ROP redis|map|forest_log:uint64 >
message ForestLog {
  uint32 id = 1;                //$<ROP unique> //日志循环队列id
  MiniUserSnapshot robber = 2;  // 掠夺者信息
  int64 tm = 3;                 // 掠夺时间
  repeated Resource res = 4;    // 被掠夺资源
  bool revenge = 5;             // 是否复仇成功
  uint64 log_id = 6;            // 日志唯一id
}

message ForestLogs {
  repeated ForestLog logs = 1;
}

message ForestLogDeletes {
  repeated uint32 ids = 2;
}

// 密林信息
message C2L_ForestInfo {}
message L2C_ForestInfo {
  uint32 ret = 1;
  Forest info = 2;
}

// 开始献祭（喂养种子）
message C2L_ForestStartFeed {
  uint32 type = 1;  // 花的类型 1-和平 2-pvp
}
message L2C_ForestStartFeed {
  uint32 ret = 1;
  uint32 type = 2;            // 花的类型 1-和平 2-pvp
  uint32 current_goblin = 3;  // 当前哥布林id
  uint32 next_goblin = 4;     // 下个哥布林id
}

// 献祭哥布林
message C2L_ForestFeedGoblin {
  uint32 type = 1;  // 花的类型 1-和平 2-pvp
}
message L2C_ForestFeedGoblin {
  uint32 ret = 1;
  uint32 type = 2;            // 花的类型 1-和平 2-pvp
  uint32 score = 3;           // 种子分数
  uint32 current_goblin = 4;  // 当前哥布林id
  uint32 next_goblin = 5;     // 下个哥布林id
}

// 更换哥布林
message C2L_ForestChangeGoblin {
  uint32 type = 1;  // 花的类型 1-和平 2-pvp
}
message L2C_ForestChangeGoblin {
  uint32 ret = 1;
  uint32 type = 2;            // 花的类型 1-和平 2-pvp
  uint32 current_goblin = 3;  // 当前哥布林id
  uint32 next_goblin = 4;     // 下个哥布林id
}

// 使用特殊道具，将树更新至最高品质
message C2L_ForestFeedSpecial {
  uint32 type = 1;  // 花的类型 1-和平 2-pvp
}
message L2C_ForestFeedSpecial {
  uint32 ret = 1;
  uint32 type = 2;   // 花的类型 1-和平 2-pvp
  uint32 score = 3;  // 种子分数
}

// 更新采集槽位数据
message C2L_ForestUpdateSlot {
  map<uint32, uint64> info = 1;  // 全量传递 采集位=>英雄id TODO 待删除
  bool clean = 2;                // 是否一键清空 TODO 待删除
  repeated uint64 hids = 3;      // 采集位英雄id列表
}
message L2C_ForestUpdateSlot {
  uint32 ret = 1;
  map<uint32, uint64> info = 2;  // TODO 待删除
  bool clean = 3;                // TODO 待删除
  repeated uint64 hids = 4;
}

// 开始种树
message C2L_ForestStartPlant {
  uint32 type = 1;  // 花的类型 1-和平 2-pvp
}
message L2C_ForestStartPlant {
  uint32 ret = 1;
  uint32 type = 2;                 // 花的类型 1-和平 2-pvp
  uint32 score = 3;                // 种子分数
  int64 start_tm = 4;              // 种树开始时间
  int64 end_tm = 5;                // 种树结束时间
  map<uint32, uint32> effect = 6;  // 采集位加成效果 类型=>效果
  uint32 collect_total_star = 7;   // 采集位总星数
}

// 加速生长
message C2L_ForestSpeedGrow {
  uint32 type = 1;  // 花的类型 1-和平 2-pvp
}
message L2C_ForestSpeedGrow {
  uint32 ret = 1;
  uint32 type = 2;  // 花的类型 1-和平 2-pvp
}

// 收获资源
message C2L_ForestHarvest {
  uint32 type = 1;  // 花的类型 1-和平 2-pvp
}
message L2C_ForestHarvest {
  uint32 ret = 1;
  uint32 type = 2;  // 花的类型 1-和平 2-pvp
  repeated Resource awards = 3;
  uint32 level = 4;                           // 密林等级
  uint64 exp = 5;                             // 密林经验
  bool crit = 6;                              // 是否暴击
  repeated Resource monthly_card_awards = 7;  // 月卡奖励
}

// 搜索
message C2L_ForestSearch {}
message L2C_ForestSearch {
  uint32 ret = 1;
  UserSnapshot enemy = 2;
  uint32 score = 3;                // 种子分数
  map<uint32, uint32> effect = 4;  // 采集位加成效果 类型=>效果 //TODO 待删除
  bool is_bot = 5;                 // 是否是机器人
  uint32 collect_total_star = 6;   // 采集位总星数
}

// 掠夺
message C2L_ForestLoot {
  uint64 uid = 1;  // 被打玩家id
  bytes client_data = 2;
}
message L2C_ForestLoot {
  uint32 ret = 1;
  uint64 uid = 2;
  bool win = 3;                  // 是否胜利
  repeated Resource awards = 4;  // 胜利奖励
  string report_id = 5;          // 战报id
}

// 被掠夺日志列表
message C2L_ForestLogList {}
message L2C_ForestLogList {
  uint32 ret = 1;
  repeated ForestLog logs = 2;
}

// 复仇
message C2L_ForestRevenge {
  uint64 log_id = 1;  // 日志id
  bytes client_data = 2;
}
message L2C_ForestRevenge {
  uint32 ret = 1;
  uint64 log_id = 2;
  bool win = 3;                  // 是否胜利
  repeated Resource awards = 4;  // 胜利奖励
  string report_id = 5;          // 战报id
}

// 领取等级奖励
message C2L_ForestRecvLvAward {
  repeated uint32 levels = 1;  // 要领取奖励的等级列表
}
message L2C_ForestRecvLvAward {
  uint32 ret = 1;
  repeated uint32 levels = 2;
  repeated Resource awards = 3;  // 奖励数据
}

// 回忆(只记录最新的)
message Memory {
  repeated uint32 chips = 2;  // 解锁的回忆点id列表
}

// 获取最新的回忆
message C2L_MemoryLatest {}
message L2C_MemoryLatest {
  uint32 ret = 1;
  Memory memory = 2;  // 最新的回忆
}

// 回忆点解锁
message C2L_MemoryUnlock {
  uint32 sys_id = 1;  // 回忆点id
}
message L2C_MemoryUnlock {
  uint32 ret = 1;
  uint32 sys_id = 2;
}

message C2L_GuildDungeonInfo {
  bool sign_up = 1;  // 报名
}

message L2C_GuildDungeonInfo {
  uint32 ret = 1;
  uint32 chapter = 2;                                        // 副本当前章节
  repeated uint32 received_chapter_task = 3;                 // 玩家已领章节任务
  uint64 history_max_damage = 4;                             // 玩家历史单次挑战造成的最高伤害
  uint32 challenge_times = 7;                                // 挑战次数
  int64 last_recover_tm = 8;                                 // 上次恢复时间
  uint32 buy_count = 9;                                      // 购买次数
  uint32 round = 10;                                         // 轮次
  repeated uint32 received_boss_box = 12;                    // 已领取的章节BOSS宝箱
  repeated GuildDungeonChapterRankTopGuild top_guild = 14;   // 上次结算章节排行的前三
  repeated uint32 no_left_box_boss = 15;                     // 没有剩余宝箱的章节
  repeated GuildDungeonRoomRankInfo rank_info = 16;          // 当前房间排名信息
  uint32 last_week_damage_rank = 17;                         // 上周公会内伤害排名
  uint32 season_rank_top_division = 18;                      // 本赛季最高段位
  uint32 pass_chapter = 19;                                  // 通关章节
  repeated uint32 recv_top_division = 20;                    // 赛季已领奖段位
  uint32 division_before_reset = 21;                         // 上一次结算前段位
  uint32 season = 22;                                        // 当前赛季
  bool sign_up = 23;                                         // 报名
  uint32 division_after_reset = 24;                          // 上一次结算后段位
  uint32 last_reset_room_rank = 25;                          // 上次结算时的房间内排名
  uint32 last_season_division_rank = 26;                     // 上赛季段位排名
  bool first_week = 27;                                      // 该战区第一周
  repeated GuildDungeonStrategyEffect strategy_effect = 28;  // 秘籍效果
  uint64 dungeon_chat_room_id = 29;                          // 副本聊天房间ID
  uint32 last_reset_add_point = 30;                          // 上次结算增加的积分
  uint32 partition = 31;                                     // 战区
  uint32 room_division = 32;                                 // 房间段位
  bool jump_chapter = 33;                                    // 是否跳章
}

message GuildDungeonStrategyEffect {
  uint32 id = 1;     // 秘技ID
  uint32 count = 2;  // 技能效果生效次数
}

message GuildDungeonChapterRankTopGuild {
  GuildSnapshot base = 1;
  uint32 chapter = 2;
  uint32 liked_count = 3;
}

message GuildDungeonBossInfo {
  uint32 id = 1;  // BossID
  reserved "GuildDungeonBossBoxInfo";
  reserved 2;
  uint32 hp_pct = 3;                 // 当前血量比；万分比
  uint64 uid = 4;                    // 尾刀玩家ID
  string name = 5;                   // 尾刀玩家名字
  int64 strengthen_time = 6;         // boss被强化的时间
  repeated uint32 strategy_ids = 7;  // 作用的秘技ID
}

message GuildDungeonBossBoxInfo {
  uint32 id = 1;        // 宝箱位置
  uint64 uid = 2;       // 领取者ID
  string name = 3;      // 领取者名字
  uint32 award_id = 4;  // 奖励id
}

message GuildDungeonRoomRankInfo {
  uint64 gid = 1;
  string name = 2;
  uint32 chapter = 3;
  uint32 chapter_rate = 4;
  uint32 score = 5;
  uint32 badge = 6;
  repeated int64 expire_time = 7;  // 0:图案,1:背景过期时间
  uint32 dead_boss_num = 8;        // 已击败boss数量
  uint32 live_boss_num = 9;        // 存活boss数量
}

message GuildDungeonSelfBossInfo {
  GuildDungeonBossInfo base = 1;
  reserved "received";
  reserved 2;
  uint64 best_damage = 3;
}

message C2L_GuildDungeonChapterInfo {
  uint32 chapter = 1;
  bool enter = 2;  // 是否进入章节
}

message L2C_GuildDungeonChapterInfo {
  uint32 ret = 1;
  uint32 chapter = 2;
  repeated GuildDungeonSelfBossInfo bosses = 3;
  repeated GuildDungeonTopDailyDamageUser top_users = 4;  // 伤害展示信息
  bool enter = 5;
  repeated GuildDungeonBossBoxInfo box_Info = 6;  // 公会副本章节宝箱
  bool is_recv = 7;                               // 自己是否已领取
  bool is_through = 8;                            // 章节是否通关
  repeated uint32 focus_boss = 9;                 // 当前被集火BOSS
  uint32 marked_num = 10;                         // 标记为可改变进度的成员数量
  bool marked = 11;                               // 是否被标记为可改变进度
}

message GuildDungeonTopDailyDamageUser {
  uint32 rank = 1;
  string name = 2;
  uint64 damage = 3;
}

message C2L_GuildDungeonRecvChapterTaskAward {
  repeated uint32 ids = 1;
}

message L2C_GuildDungeonRecvChapterTaskAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  repeated Resource awards = 3;
}

message C2L_GuildDungeonFight {
  uint32 chapter = 1;  //  章节
  uint32 monster = 2;  //  选择的怪物
  bool sweep = 3;      // 是否扫荡
  bytes client_data = 4;
}

message L2C_GuildDungeonFight {
  uint32 ret = 1;
  uint32 chapter = 2;
  uint32 monster = 3;
  bool win = 4;
  string report_id = 5;
  repeated Resource awards = 6;
  uint32 hp_pct = 7;                                         // 对手
  uint64 this_damage = 8;                                    // 玩家本次造成伤害
  uint64 history_max_damage = 9;                             // 玩家历史单次挑战造成的最高伤害
  repeated GuildDungeonTopDailyDamageUser top_users = 10;    // 伤害展示信息
  uint32 challenge_times = 11;                               // 挑战次数
  int64 last_recover_tm = 12;                                // 上次恢复时间
  repeated GuildDungeonSelfBossInfo bosses = 13;             // 本章节Boss信息
  repeated Resource kill_boss_awards = 14;                   // 尾刀奖励
  bool sweep = 15;                                           // 是否扫荡
  bool new_boss_damage = 16;                                 // 对此boss造成新记录
  uint32 reduce_hp_pct = 17;                                 // 扣除血量万分比
  repeated GuildDungeonRoomRankInfo rank_info = 18;          // 房间排名
  repeated GuildDungeonStrategyEffect strategy_effect = 19;  // 秘籍效果
  uint32 strategy_reduce_hp_pct = 20;                        // 秘技效果减少的Boss血条
  uint32 marked_num = 21;                                    // 标记为可改变进度的成员数量
  bool marked = 22;                                          // 是否被标记为可改变进度
}

message L2C_GuildDungeonNotify {  // 推送公会的最新章节
  uint32 ret = 1;
  uint32 chapter = 2;
  uint32 boss = 3;  // 被击败的BOSS
}

message C2L_GuildDungeonUserDamageRank {}

message L2C_GuildDungeonUserDamageRank {
  uint32 ret = 1;
  RankValue self = 2;
  repeated RankValue list = 3;
}

// 公会副本日志
message GuildDungeonLog {
  uint32 id = 1;
  uint32 log_type = 2;       // 日志类型
  int64 create_tm = 3;       // 发生时间
  uint32 chapter = 4;        // 章节id
  string member_name = 5;    // 成员名
  uint64 damage = 6;         // 本次造成伤害
  uint32 monster = 7;        // 本次攻打选择的怪物组
  uint32 reduce_hp_pct = 8;  // 扣除的血量万分比
}

message GuildDungeonLogs {
  repeated GuildDungeonLog logs = 1;
}

message GuildDungeonLogDeletes {
  repeated uint32 ids = 2;
}

message C2L_GuildDungeonLogList {}

message L2C_GuildDungeonLogList {
  uint32 ret = 1;
  repeated GuildDungeonLog list = 2;
}

message C2L_GuildDungeonRecvBossBoxAward {
  uint32 chapter = 1;
  uint32 box = 2;
}

message L2C_GuildDungeonRecvBossBoxAward {
  uint32 ret = 1;
  uint32 chapter = 2;
  uint32 box = 3;
  repeated GuildDungeonBossBoxInfo boxes = 4;
  repeated Resource awards = 5;
  repeated uint32 received_boss_box = 6;
}

message C2L_GuildDungeonBuyChallengeTimes {
  uint32 num = 1;  // 购买次数
}

message L2C_GuildDungeonBuyChallengeTimes {
  uint32 ret = 1;
  uint32 challenge_times = 2;  // 挑战次数
  int64 last_recover_tm = 3;   // 上次恢复时间
  uint32 buy_count = 4;        // 购买次数
}

message C2L_GuildDungeonSetMessageBoard {
  string new_message = 1;
}

message L2C_GuildDungeonSetMessageBoard {
  uint32 ret = 1;
  string new_message = 2;
}

// 公会副本日志
//$<ROP redis|map|guild_dungeon_message_board:uint64>
message GuildDungeonMessageBoardLog {
  uint32 id = 1;           //$<ROP unique>
  string member_name = 2;  // 成员名
  int64 create_tm = 3;     // 发生时间
  string message = 4;      // 留言
  uint64 log_id = 5;       // 日志唯一id
}

message GuildDungeonMessageBoardLogs {
  repeated GuildDungeonMessageBoardLog logs = 1;
}

message GuildDungeonMessageBoardLogDeletes {
  repeated uint32 ids = 2;
}

message C2L_GuildDungeonGetMessageBoard {}

message L2C_GuildDungeonGetMessageBoard {
  uint32 ret = 1;
  repeated GuildDungeonMessageBoardLog all_messages = 2;
}

message C2L_GuildDungeonDelMessageBoard {
  uint64 uniqID = 1;
}

message L2C_GuildDungeonDelMessageBoard {
  uint32 ret = 1;
  uint64 uniqID = 2;
}

message C2L_GuildDungeonRankLike {
  uint64 gid = 1;
}

message L2C_GuildDungeonRankLike {
  uint32 ret = 1;
  uint64 gid = 2;
  repeated Resource awards = 3;
}

message C2L_GuildDungeonSeasonDivisionAward {
  repeated uint32 division = 1;
}

message L2C_GuildDungeonSeasonDivisionAward {
  uint32 ret = 1;
  repeated uint32 division = 2;
  repeated uint32 recv_divison = 3;
  repeated Resource awards = 4;
}

message C2L_GuildDungeonSetFocus {
  uint32 chapter = 1;
  repeated uint32 focus_ids = 2;
}

message L2C_GuildDungeonSetFocus {
  uint32 ret = 1;
  uint32 chapter = 2;
  repeated uint32 focus_ids = 3;
}

message C2L_GuildDungeonHallOfFame {
  uint32 season_id = 1;
}

message L2C_GuildDungeonHallOfFame {
  uint32 ret = 1;
  uint32 season_id = 2;
  repeated GuildDungeonHallOfFameInfo list = 3;
  repeated uint32 season_ids = 4;  // 拥有数据的赛季ID
}

message GuildDungeonHallOfFameInfo {
  uint64 gid = 1;
  string name = 2;
  uint32 badge = 3;
  uint32 division = 4;
  uint32 star = 5;
  uint32 level = 6;  // 公会等级
}

message C2L_GuildDungeonTop3Guild {}

message L2C_GuildDungeonTop3Guild {
  uint32 ret = 1;
  repeated GuildDungeonHallOfFameInfo last_season_top_3 = 2;
  uint32 last_season_id = 3;  // 上赛季ID
}

message C2L_GuildDungeonGetStrategy {}

message L2C_GuildDungeonGetStrategy {
  uint32 ret = 1;
  repeated StrategyInfo strategy = 2;  // 秘技信息
  repeated GuildDungeonRoomRankInfo rank_info = 3;
}

message StrategyInfo {
  uint32 id = 1;                             // 秘技ID
  uint32 count = 2;                          // 次数
  repeated StrategyRestoreBoss restore = 3;  // 复活Boss的信息记录
}

message StrategyRestoreBoss {
  uint64 target_gid = 1;
  uint32 target_chapter = 2;
  uint32 count = 3;  // 该章节复活次数
}

message C2L_GuildDungeonUseStrategy {
  uint32 strategy_id = 1;
  uint64 gid = 2;
  uint32 use_count = 3;
}

message L2C_GuildDungeonUseStrategy {
  uint32 ret = 1;
  uint32 strategy_id = 2;
  uint64 gid = 3;
  uint32 use_count = 4;
  repeated StrategyInfo strategy = 5;  // 秘技信息
}

message L2C_GuildDungeonStrategyUseNotify {
  uint32 ret = 1;
  uint32 use_id = 2;         // 使用的秘技ID
  uint32 use_count = 3;      //
  uint64 use_member_id = 4;  // 使用者ID
}

message C2L_GuildDungeonRecvAllBossBoxAward {}

message L2C_GuildDungeonRecvAllBossBoxAward {
  uint32 ret = 1;
  repeated Resource awards = 5;  // 由于一键领取按钮在外部，只需要奖励就可以，红点默认消除
}

message C2L_GuildDungeonGetMembersFightInfo {}

message L2C_GuildDungeonGetMembersFightInfo {
  uint32 ret = 1;
  repeated GuildDungeonMemberFightInfo infos = 2;
}

message GuildDungeonMemberFightInfo {
  MiniUserSnapshot u_snapshot = 1;
  uint32 day_fight_num = 3;     // 今日挑战次数
  uint32 day_buy_num = 4;       // 今日购买次数
  uint64 day_fight_damage = 5;  // 今日挑战造成的伤害
  bool marked = 6;              //  是否被标记为可降低Boss血量
}

// 英雄列传
message Tales {
  map<uint32, TaleInfo> tales = 1;   // 列传信息;
  repeated uint32 elite_passed = 2;  // 通过的强敌
}

// 英雄列传-列传数据
message TaleInfo {
  uint32 id = 1;             // 当前列传id
  uint32 last_finished = 2;  // 上一个完成章节
  bool fight_pass = 3;       // 本章节是否战斗通过了
  uint32 taken_status = 4;   // 奖励领取状态
}

// 英雄列传-数据列表
message C2L_TalesList {}

message L2C_TalesList {
  uint32 ret = 1;
  Tales tales = 2;                      // 列传数据
  map<uint32, uint32> elite_count = 3;  // 强敌次数
}

// 英雄列传-章节战斗
message C2L_TalesChapterFight {
  uint32 id = 1;                // 章节id
  repeated uint32 assists = 2;  // 协助的怪物列表
  bytes client_data = 3;
}

message L2C_TalesChapterFight {
  uint32 ret = 1;
  uint32 id = 2;         // 章节id
  string report_id = 3;  // 战报id
  TaleInfo info = 4;     // 列传信息
  bool win = 5;          // 是否胜利
}

// 英雄列传-章节完成
message C2L_TalesChapterFinish {
  uint32 id = 1;  // 章节id
}

message L2C_TalesChapterFinish {
  uint32 ret = 1;
  uint32 id = 2;      // 章节id
  TaleInfo info = 3;  // 列传信息
}

// 英雄列传-章节领取奖励
message C2L_TalesChapterTakeReward {
  repeated uint32 ids = 1;  // 章节id
}

message L2C_TalesChapterTakeReward {
  uint32 ret = 1;
  repeated uint32 ids = 2;       // 章节id
  repeated Resource awards = 3;  // 奖励
  uint32 taken_status = 4;       // 奖励领取状态
}

// 英雄列传-强敌战斗
message C2L_TalesEliteFight {
  uint32 id = 1;  // 强敌id
  bytes client_data = 2;
}

message L2C_TalesEliteFight {
  uint32 ret = 1;
  uint32 id = 2;                 // 强敌id
  uint32 count = 3;              // 刷新今日挑战次数
  string report_id = 4;          // 战报id
  repeated Resource awards = 5;  // 奖励
  bool win = 6;                  // 是否胜利
}

// 英雄列传-强敌扫荡
message C2L_TalesEliteWipe {
  uint32 id = 1;  // 强敌id
}

message L2C_TalesEliteWipe {
  uint32 ret = 1;
  uint32 id = 2;                 // 强敌id
  uint32 count = 3;              // 刷新今日挑战次数
  repeated Resource awards = 4;  // 奖励
}

// 公会天赋
message GuildTalent {
  map<uint32, uint32> talents = 1;  // key -> kind; value -> level
}

// 公会天赋列表
message C2L_GuildTalentList {}
message L2C_GuildTalentList {
  uint32 ret = 1;
  GuildTalent list = 2;  // 天赋列表
}

// 公会天赋激活
message C2L_GuildTalentLevelUp {
  //  uint32 job = 1;  // 天赋类型
  reserved 1;
  reserved "job";
  uint32 kind = 2;       // 节点种类
  uint32 add_level = 3;  // 增加的level，第二层有值
}

message L2C_GuildTalentLevelUp {
  uint32 ret = 1;
  //  uint32 job = 2;          // 天赋类型
  GuildTalent talent = 3;  // 天赋信息
  reserved 2;
  reserved "job";
  uint32 kind = 4;  // 节点种类
  repeated Resource awards = 5;
  uint32 add_level = 6;
}

// 公会天赋重置
message C2L_GuildTalentReset {
  //  uint32 job = 1;  // 天赋类型
  reserved 1;
  reserved "job";
  uint32 kind = 2;  // 天赋树1~6主干分支可以重置
}

message L2C_GuildTalentReset {
  uint32 ret = 1;
  //  uint32 job = 2;             // 天赋类型
  GuildTalent talent = 3;     // 天赋信息
  repeated Resource res = 4;  // 天赋重置返还的资源
  reserved 2;
  reserved "job";
  uint32 kind = 5;  // 天赋树1~6主干分支可以重置
}

// 新手引导
message GuidanceGroup {
  uint32 group = 1;  // 组
  uint32 step = 2;   // 节点
}

message Guidance {
  repeated GuidanceGroup ongoing = 1;  // 进行中的组
  repeated uint32 finished = 2;        // 已完成的组
  bool close_flag = 3;                 // 关闭状态
  bool select_skip_tag = 4;            // 玩家是否选择跳过一部分引导；true: 跳，false：不跳
}

message C2L_GuidanceList {}

message L2C_GuidanceList {
  uint32 ret = 1;
  Guidance guidance = 2;
}

// 新手引导-完成节点
message C2L_GuidanceFinishStep {
  uint32 group = 1;
  uint32 step = 2;
}

message L2C_GuidanceFinishStep {
  uint32 ret = 1;
  GuidanceGroup data = 2;
}

// 新手引导-完成组
message C2L_GuidanceFinishGroup {
  uint32 group = 1;
}

message L2C_GuidanceFinishGroup {
  uint32 ret = 1;
  uint32 group = 2;
}

// 新手引导-老玩家跳过一部分新手引导
message C2L_GuidanceSkip {}

message L2C_GuidanceSkip {
  uint32 ret = 1;
  uint32 dungeon_id = 2;  // 战役关卡id
  repeated uint32 groups = 3;
  repeated Resource awards = 4;
}

message Carnival {
  uint32 id = 1;  // 嘉年华id
  map<uint32, TaskTypeProgress> task_type_progress = 2;
  map<uint32, bool> awarded = 3;
}

message C2L_CarnivalGetData {}

message L2C_CarnivalGetData {
  uint32 ret = 1;
  repeated Carnival carnival = 2;
}

// 支持批量领取
message C2L_CarnivalReceiveAward {
  uint32 carnival_id = 1;
  repeated uint32 ids = 2;  // 任务id
}

message L2C_CarnivalReceiveAward {
  uint32 ret = 1;
  uint32 carnival_id = 2;
  repeated uint32 ids = 3;
  repeated Resource awards = 4;
}

// 更新进度
message L2C_CarnivalUpdate {
  uint32 ret = 1;
  uint32 carnival_id = 2;
  map<uint32, TaskTypeProgress> carnival_progress = 3;  // 嘉年华任务进度
}

message BanInfo {
  uint64 id = 1;    // 玩家id
  int64 start = 2;  // 封禁开始时间
  int64 end = 3;    // 封禁结束时间; start == end && end == 0 结束封禁
}

message BanUsers {
  repeated BanInfo users = 1;
}

// 七日登录
message SevenDayLogin {
  uint32 days = 1;           // 累计登录天数
  uint32 reward_status = 2;  // 奖励领取状态
}
// 请求数据

message C2L_SevenDayLoginData {}

message L2C_SevenDayLoginData {
  uint32 ret = 1;
  SevenDayLogin data = 2;  // 数据
}
// 领取奖励
message C2L_SevenDayLoginTakeAward {
  uint32 id = 1;  // 量表id
}

message L2C_SevenDayLoginTakeAward {
  uint32 ret = 1;
  uint32 id = 2;
  uint32 reward_status = 3;      // 领取状态
  repeated Resource awards = 4;  // 奖励
}

message Medal {
  uint32 level = 1;                                 // 功勋等级
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
  map<uint32, bool> task_awarded = 4;               // 已领取过奖励的任务(只记录最新等级的功勋任务领取)
  map<uint32, bool> goddess_cure = 5;               // 女武神治疗  key: goddess_contract_elite_info表elite_id
  //  bool is_receive_level_award = 6;                  // 是否领取等级奖励
  reserved 6;
  reserved "is_receive_level_award";
}

// 功勋-获取功勋信息
message C2L_MedalGetData {}
message L2C_MedalGetData {
  uint32 ret = 1;
  Medal medal = 2;
}

// 功勋-领取奖励
message C2L_MedalReceiveAward {
  uint32 level = 1;              // 功勋等级
  uint32 type = 2;               // 奖励类型: 1-每日奖励; 2-功勋等级大奖; 3-任务奖励
  repeated uint32 task_ids = 3;  // 类型为任务奖励时有值
}

message L2C_MedalReceiveAward {
  uint32 ret = 1;
  uint32 level = 2;
  uint32 type = 3;
  repeated uint32 task_ids = 4;
  repeated Resource awards = 5;
}

// 功勋-更新任务进度
message L2C_MedalUpdate {
  uint32 ret = 1;
  uint32 level = 2;                                 // 功勋等级
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
}

message L2C_MedalGoddessCureUpdate {
  uint32 ret = 1;
  uint32 elite_id = 2;  // key：goddess_contract_elite_info表elite_id
}

message Questionnaire {
  uint32 id = 1;
  uint32 start_tm = 2;                // 开始时间
  uint32 end_tm = 3;                  // 结束时间
  uint32 start_lvl = 4;               // 问卷开启等级
  uint32 end_lvl = 5;                 // 问卷结束等级
  uint32 start_vip = 6;               // 问卷开启vip等级
  uint32 end_vip = 7;                 // 问卷结束vip等级
  uint32 srv_day = 8;                 // 问卷开启开服天数
  string address = 9;                 // 问卷地址
  repeated uint32 op_id = 10;         // 运营商
  repeated uint32 channel = 11;       // 渠道ID
  uint32 create_role_day = 12;        // 创角天数
  uint32 start_recharge_amount = 13;  // 问卷开启充值金额
  uint32 end_recharge_amount = 14;    // 问卷结束充值金额
  repeated Resource awards = 15;      // 奖励
  bool finish = 16;                   // 是否完成
  string content = 17;                // 问卷描述
  string default_lang = 18;           // 默认语言
}

message C2L_SyncQuestionnaire {
  string lang = 1;  // 语言类型
}

message L2C_SyncQuestionnaire {
  uint32 ret = 1;
  bool push = 2;                         // 是否是推送消息
  repeated Questionnaire questions = 3;  // 问卷消息
  string lang = 4;                       // 语言类型
}

message L2C_QuestionnaireUpdate {}

message C2L_Duel {
  uint64 rival_id = 1;
  bytes client_data = 2;
}

message L2C_Duel {
  uint32 ret = 1;
  uint64 rival_id = 2;
  bool win = 3;          // 是否胜利
  string report_id = 4;  // 战报id
}

message C2L_Accusation {
  uint64 uid = 1;
  uint32 reason = 2;
}

message L2C_Accusation {
  uint32 ret = 1;
  uint64 uid = 2;
  uint32 reason = 3;
}

// 获取图鉴信息
message C2L_HandbooksGetData {
  uint32 type = 1;
}
message L2C_HandbooksGetData {
  uint32 ret = 1;
  uint32 type = 2;
  Handbooks handbooks = 3;
}

message C2L_HandbooksActive {
  uint32 type = 1;          // 图鉴类型
  repeated uint32 ids = 2;  // 图鉴ids
}

message L2C_HandbooksActive {
  uint32 ret = 1;
  uint32 type = 2;
  repeated uint32 ids = 3;
}

message C2L_HandbooksReceiveAwards {
  uint32 type = 1;
  repeated uint32 ids = 2;
}

message L2C_HandbooksReceiveAwards {
  uint32 ret = 1;
  uint32 type = 2;
  repeated uint32 ids = 3;
  repeated Resource awards = 4;  // 奖励
}

message C2L_HandbooksActiveHeroAttr {  // 激活英雄图鉴的属性
  uint32 hero_sysId = 1;               // 英雄系统ID
  uint32 link_attr_id = 2;             // 羁绊属性ID
}

message L2C_HandbooksActiveHeroAttr {  // 激活英雄图鉴的属性
  uint32 ret = 1;
  uint32 hero_sysId = 2;     // 英雄系统ID
  uint32 link_attr_id = 3;   // 羁绊属性ID
  HeroHandbook changed = 4;  // 变化的英雄图鉴
}

// ---------------机器人测试-------
message BattleBots {
  map<uint32, BotData> bots = 1;
}

message BotData {
  Memory memory = 1;
  repeated Artifact artifacts = 2;
  repeated BotHero heroes = 3;
}

message BotHero {
  uint32 hero_id = 1;
  uint32 star = 2;
  uint32 level = 3;
  uint32 stage = 4;
  map<uint32, BotEquip> equips = 5;
  map<uint32, BotEmblem> emblems = 6;
  map<uint32, BotGem> gems = 7;
}

message BotEquip {
  uint32 equip_id = 1;
  uint32 strength_level = 2;
  uint32 refine_level = 3;
  uint32 enchant_level = 4;
  uint32 evolution_level = 5;
}

message BotEmblem {
  uint32 emblem_id = 1;
  uint32 level = 2;
  uint32 blessing_level = 3;
}

message BotGem {
  uint32 gem_id = 1;
}
//-----------------------------------------

message C2L_GetClientInfo {}

message L2C_GetClientInfo {
  uint32 ret = 1;
  ClientInfo client_info = 2;
}

message C2L_SetClientInfo {
  ClientInfo client_info = 1;
}

message L2C_SetClientInfo {
  uint32 ret = 1;
}

message ClientInfo {
  map<uint32, ClientInfoData> infos = 1;
}

message ClientInfoData {
  repeated uint32 datas = 1;
}

message ServerUser {
  uint64 sid = 1;
  uint64 uid = 2;
}

message C2L_GetUserSnapshots {
  repeated uint64 user_ids = 1;  // 玩家id
  uint32 fid = 2;                // 阵容id - 通过此参数获取对应防守阵容战力
}

message L2C_GetUserSnapshots {
  uint32 ret = 1;
  repeated UserSnapshot users = 2;
  uint32 fid = 3;  // 阵容id
}

// 废弃
message UserChatHead {
  uint64 id = 1;                   // 玩家id
  uint32 base_id = 2;              // 玩家头像
  string name = 3;                 // 玩家昵称
  uint32 level = 4;                // 等级
  int64 power = 5;                 // 战力
  uint32 grade = 6;                // 公会职位
  repeated int64 expire_time = 7;  // 0:头像,1:头像框过期时间
  uint32 vip = 8;
  uint64 server_id = 9;  // 服务器id
}

//$<ROP redis|map|msg:uint64 >
message Message {
  uint64 id = 1;        //$<ROP unique > 消息唯一ID；暂时只私聊有值
  uint64 sender = 2;    //  发送方ID
  int64 timestamp = 3;  //  发送时间
  string content = 4;   //  发送的消息内容
  UserChatHead user = 5;
  //  ChatParam param = 6;
  reserved 6;
  reserved "param";
  uint32 type = 7;  // 消息模板类型；对应chat_admin_info.xml的type字段
  // 1：世界聊天 2：公会聊天 3：私聊 4：加入公会的通知消息 5：公会招募的消息
  repeated string params = 8;
}

// 聊天专用
message ChatUserHead {
  string id = 1;       // 玩家id
  uint32 base_id = 2;  // 玩家头像
  string name = 3;     // 玩家昵称
  uint32 level = 4;    // 等级
  string power = 5;    // 战力
  uint32 grade = 6;    // 公会职位
  uint32 vip = 8;
  string server_id = 9;    // 服务器id
  uint64 guild_id = 10;    // 公会id
  string guild_name = 11;  // 公会名
  string uid = 12;
  uint32 chat_bubbles = 13;  // 聊天气泡
  uint32 title = 14;         // 称号
}

// 跨服聊天用的新结构
message ChatMessage {
  string id = 1;         //  消息唯一ID
  string sender = 2;     //  发送方ID
  string timestamp = 3;  //  发送时间
  string content = 4;    //  发送的消息内容
  ChatUserHead user = 5;
  uint32 type = 7;  // 消息模板类型；对应chat_admin_info.xml的type字段
  repeated string params = 8;
}

// 聊天点赞
//$<ROP redis|map|chat_like>
message ChatLike {
  uint64 id = 1;                   //$<ROP unique > 消息唯一ID
  repeated UserChatHead like = 2;  // 用slice，保证点赞顺序，不需要额外处理
}

// message ChatParam {
//  uint32 type = 1;
//  uint32 param = 2;
//}

message C2L_Chat {
  uint32 channel = 1;      // 渠道: 3 - 私聊；2 - 公会聊天；1 - 世界聊天
  uint64 receiver_id = 2;  // 接收方id（私聊时有值）
  string content = 3;      // 消息内容
}

message L2C_Chat {
  uint32 ret = 1;
  uint32 channel = 2;
  uint64 receiver_id = 3;
  string content = 4;
  int64 timestamp = 5;  // 时间戳
  uint64 id = 6;        // 消息唯一id（私聊时有值）
}

message C2L_ChatGetMessages {
  uint32 channel = 1;  // 渠道: 3 - 私聊；2 - 公会聊天；1 - 世界聊天
}

message L2C_ChatGetMessages {
  uint32 ret = 1;
  uint32 channel = 2;
  repeated Message msgs = 3;
}

message L2C_ChatNotify {
  uint32 channel = 1;
  Message msg = 2;
}

message L2C_ChatLikeNotify {
  uint64 msg_id = 1;
}

message C2L_ChatLike {
  uint64 msg_id = 1;  // 消息id
  uint32 type = 2;    //  消息类型
}

message L2C_ChatLike {
  uint32 ret = 1;
  uint32 type = 2;
  uint64 msg_id = 3;
}

message GetLikeListParam {
  uint64 msg_id = 1;
  uint32 type = 2;
}

message C2L_ChatGetLikeList {
  repeated GetLikeListParam params = 1;
}

message L2C_ChatGetLikeList {
  uint32 ret = 1;
  repeated GetLikeListParam params = 2;
  repeated ChatLike like_list = 3;
}

message C2L_ChatGetPrivateMessageNum {}

message L2C_ChatGetPrivateMessageNum {
  uint32 ret = 1;
  uint32 num = 2;  // 私聊离线消息数量
}

message C2L_ChatGetToken {}

message L2C_ChatGetToken {
  uint32 ret = 1;
  string token = 2;
  int64 expire_time = 3;
}

message L2C_ChatGroupUpdateNotify {
  string group_tag = 1;
}

message C2L_ChatSyncChatTag {}
message L2C_ChatSyncChatTag {
  uint32 ret = 1;
}

message C2L_ChatCostShareCount {
  uint32 type = 1;  // 分享频道类型
}

message L2C_ChatCostShareCount {
  uint32 ret = 1;
  uint32 type = 2;
}

// 水晶落地数据
message Crystal {
  map<uint32, CrystalSlot> slot = 1;    // 槽位信息 槽位id=>槽位信息
  CrystalShareAttr attr = 2;            // 共鸣属性
  repeated uint64 contract_heroes = 3;  // 缔约英雄id列表
  uint32 purchased_slot_count = 4;      // 槽位已购数量
  reserved 5;
  reserved "bless";
  CrystalBlessingAchieve achieve = 6;  // 水晶成就
}

// 水晶-槽位信息
message CrystalSlot {
  uint64 hid = 1;         // 英雄id
  int64 cd_end_time = 2;  // 槽位冷却结束时间
  uint32 id = 3;          // 槽位id
}

// 水晶-共鸣属性
message CrystalShareAttr {
  ShareHero hero = 1;                         // 英雄的共鸣属性
  map<uint32, ShareEquipment> equipment = 2;  // 装备的共鸣属性 位置=>具体属性
  ShareGem gem = 3;                           // 宝石的共鸣属性 //TODO版本更新后删除
  ShareEmblem emblem = 4;                     // 纹章的共鸣属性 //TODO版本更新后删除
  map<uint32, ShareGem> gems = 5;             // 宝石的共鸣属性 位置(1-左 2-右)=>具体属性
  map<uint32, ShareEmblem> emblems = 6;       // 纹章的共鸣属性 位置(1-左 2-右)=>具体属性
}

// 水晶-共鸣属性-英雄属性
message ShareHero {
  uint32 level = 1;  // 等级
  uint32 stage = 2;  // 突破等级
}

// 水晶-共鸣属性-装备属性
message ShareEquipment {
  uint32 sys_id = 1;           // 装备的系统id
  uint32 strength_level = 2;   // 强化等级
  uint32 refine_exp = 3;       // 精炼进度
  uint32 refine_level = 4;     // 精炼等级
  uint32 enchant_level = 5;    // 附魔等级
  uint32 evolution_level = 6;  // 进阶等级
}

// 水晶-共鸣属性-宝石属性
message ShareGem {
  uint32 rare = 1;  // 品质
}

// 水晶-共鸣属性-纹章属性
message ShareEmblem {
  uint32 stage = 1;  // 阶级
  uint32 level = 2;  // 强化等级
  reserved "blessing";
  reserved 3;
}

// 水晶-祝福
message CrystalBlessing {
  uint32 level = 1;                         // 祝福等级
  uint32 exp = 2;                           // 祝福经验值
  repeated uint32 active_achievements = 3;  // 已激活的祝福成就列表
}

// 水晶-祝福成就
message CrystalBlessingAchieve {
  map<uint32, bool> receive_awarded = 1;            // 已领取成就
  map<uint32, TaskTypeProgress> task_progress = 2;  // 任务进度
}

// 添加共鸣英雄
message C2L_CrystalAddHero {
  uint64 hid = 1;      // 英雄id
  uint32 slot_id = 2;  // 槽位id
}
message L2C_CrystalAddHero {
  uint32 ret = 1;
  uint64 hid = 2;                // 英雄id
  uint32 slot_id = 3;            // 槽位id
  Hero hero = 4;                 // 最新英雄数据
  repeated Resource awards = 5;  // 英雄重生返还的资源
}

// 移出共鸣英雄
message C2L_CrystalRemoveHero {
  uint32 slot_id = 1;  // 槽位id
}
message L2C_CrystalRemoveHero {
  uint32 ret = 1;
  uint32 slot_id = 2;  // 槽位id
  Hero hero = 3;       // 最新英雄数据
}

// 解锁槽位
message C2L_CrystalUnlockSlot {}
message L2C_CrystalUnlockSlot {
  uint32 ret = 1;
  uint32 purchased_num = 2;  // 槽位已购数量
}

// 加速完成槽位冷却
message C2L_CrystalSpeedSlotCD {
  uint32 slot_id = 1;  // 槽位id
}
message L2C_CrystalSpeedSlotCD {
  uint32 ret = 1;
  uint32 slot_id = 2;  // 槽位id
}

// 获取水晶信息
message C2L_CrystalGetAllData {}
message L2C_CrystalGetAllData {
  uint32 ret = 1;
  Crystal data = 2;  // 水晶信息
}

// 推送共鸣属性
message L2C_CrystalGetShareAttr {
  uint32 type = 1;            // common.CRYSTAL_SHARE_ATTR_TYPE 属性同步的类型 0-全部 1-英雄 2-装备 3-宝石 4-纹章
  CrystalShareAttr attr = 2;  // 共鸣属性
}

// 推送水晶中英雄状态的更新(缔约和共鸣英雄状态变化时推送)
message L2C_CrystalHeroesUpdate {
  map<uint32, CrystalSlot> slot = 1;    // 槽位信息 槽位id=>槽位信息 --- 只推有变化的数据
  repeated uint64 contract_heroes = 2;  // 缔约英雄id列表
}

// 激活成就
message C2L_CrystalActiveAchievement {
  repeated uint32 ids = 1;  // 成就id列表
}
message L2C_CrystalActiveAchievement {
  uint32 ret = 1;
  repeated uint32 ids = 2;  // 成就id列表
}

// 水晶成就-更新任务进度
message L2C_CrystalUpdateAchievement {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_progress = 2;  // 成就进度
}

// 新功能预告
message Forecast {
  map<uint32, bool> receive_awarded = 1;            // 已领取过奖励的功能 key:forecast_info.xml的id
  map<uint32, TaskTypeProgress> task_progress = 2;  // 任务进度
}

// 新功能预告-获取新功能预告信息
message C2L_ForecastGetData {}
message L2C_ForecastGetData {
  uint32 ret = 1;
  Forecast forecast = 2;
}

// 新功能预告-领取奖励
message C2L_ForecastReceiveAward {
  uint32 sys_id = 1;
}

message L2C_ForecastReceiveAward {
  uint32 ret = 1;
  uint32 sys_id = 2;
  repeated Resource awards = 3;
}

// 新功能预告-更新任务进度
message L2C_ForecastUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
}

message FirstGiftRewardStatus {
  uint32 gift_id = 1;        // 首充礼包id
  int64 buy_time = 2;        // 购买时间
  uint32 login_count = 3;    // 登录计数
  uint32 reward_status = 4;  // 领奖状态 bitmap
}

message C2L_RechargeGetData {}

message L2C_RechargeGetData {
  uint32 ret = 1;
  repeated uint32 normal_ids = 2;
  repeated FirstGiftRewardStatus first_gift_reward_status = 3;  // 首充礼包领奖状态
}

message C2L_RechargeSimulation {
  OrderCustomData custom = 1;
}

message L2C_RechargeSimulation {
  uint32 ret = 1;
  OrderCustomData custom = 2;
}

message OrderCustomData {
  uint32 type = 1;  // 充值类型 enum RECHARGE_TYPE
  uint32 id = 2;    // 充值id
  uint32 op_id = 3;
  uint32 op_group_id = 4;
  uint32 game_id = 5;
  uint32 channel = 6;
  uint32 internal_id = 7;     // 内部产品id（订单统一检查用）
  repeated int64 values = 8;  // 自定义参数
  string sdk_pid = 9;
}

message L2C_RechargeNotify {
  uint32 ret = 1;
  uint32 id = 2;          // 商品id
  string product_id = 3;  //
  uint32 amount = 4;      // 订单金额
  OrderCustomData custom = 5;
  repeated Resource awards = 6;        // 充值获得的奖励
  repeated Resource extra_awards = 7;  //  充值获得的额外奖励（客户端特殊显示用）
}

message L2C_RechargeFirstGiftNotify {
  uint32 ret = 1;
  repeated Resource awards = 2;
  repeated FirstGiftRewardStatus first_gift_reward_status = 3;  // 新首充礼包领奖状态
}

message C2L_RechargeFirstGiftReward {
  uint32 id = 1;   // 首充礼包id
  uint32 day = 2;  // 领奖天数
}

message L2C_RechargeFirstGiftReward {
  uint32 ret = 1;
  uint32 id = 2;
  uint32 day = 3;
  uint32 reward_status = 4;      // 领取状态
  repeated Resource awards = 5;  // 奖励
}

message C2L_RechargeByCoupon {
  OrderCustomData custom = 1;
}

message L2C_RechargeByCoupon {
  uint32 ret = 1;
  OrderCustomData custom = 2;
}

// 官网充值代金券配置
message CouponCfg {
  uint32 id = 1;
  uint64 start_time = 2;
  uint64 end_time = 3;
  repeated CouponRuleContent rule_content = 4;
  string op_ids = 5;
}

message CouponRuleContent {
  uint32 id = 1;
  CouponCondition condition = 2;
  repeated Resource awards = 3;
}

message CouponCondition {
  int32 start_coin = 1;
  int32 end_coin = 2;
  string coin_name = 3;
}

message Towerstar {
  map<uint32, TowerstarChapterInfo> chapter_info = 1;  // 章节信息 章节id=>信息
  uint32 current_unlock_dungeon = 2;                   // 当前解锁关卡
  uint32 current_open_chapter = 3;                     // 当前开启章节
  int64 open_chapter_update_time = 4;                  // 开启章节更新时间
  uint32 total_star = 5;                               // 总星数
  uint32 max_removed_chapter = 6;
}

message TowerstarChapterInfo {
  map<uint32, uint32> dungeon_info = 1;  // 关卡星级信息 关卡id=>星级信息(采用位存储星级信息)
  uint32 total_star = 2;                 // 章节总星数
  repeated uint32 reward_info = 3;       // 领取过的星级宝箱
}

message C2L_TowerstarGetData {
  uint32 chapter = 1;
}

message L2C_TowerstarGetData {
  uint32 ret = 1;
  Towerstar towerstar = 2;  // 条件爬塔数据
  uint32 chapter = 3;
}

message C2L_TowerstarFight {
  uint32 dungeon_id = 1;  // 副本id
  bytes client_data = 2;
  bool quick = 3;              // 快速战斗
  bool dynamic_formation = 4;  // 动态阵容
}

message L2C_TowerstarFight {
  uint32 ret = 1;
  uint32 dungeon_id = 2;         // 副本id
  uint32 star_info = 3;          // 战斗星级信息
  repeated Resource awards = 4;  // 首通奖励
  string report_id = 5;          // 战报id
  bool win = 6;                  // 是否战斗胜利
  bool quick = 7;                // 快速战斗
  bool dynamic_formation = 8;    // 动态阵容
}

message C2L_TowerstarStarRecvAward {
  repeated uint32 ids = 1;  // 奖励的id
}
message L2C_TowerstarStarRecvAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;       // 成功的id
  repeated Resource awards = 3;  // 奖励
}

message C2L_TowerstarDailyRecvAward {}
message L2C_TowerstarDailyRecvAward {
  uint32 ret = 1;
  repeated Resource awards = 2;  // 奖励
}

message VipInfo {
  uint32 gift_status = 1;              // 已经购买了的vip等级礼包记录(按位记录)
  map<uint32, uint32> buy_counts = 2;  // 付费vip礼包购买次数
}

// vip-获取vip信息
message C2L_VipInfoGet {}

message L2C_VipInfoGet {
  uint32 ret = 1;
  VipInfo vip_info = 2;
}

// vip-购买礼包
message C2L_VipBuyGift {
  uint32 level = 1;
}

message L2C_VipBuyGift {
  uint32 ret = 1;
  uint32 level = 2;
  repeated Resource awards = 3;
}

// 方便以后扩展，针对每个商店有过期时间等
// 而且新手商店有购买次数暂时也没地方存
message ActivityRecharge {
  map<uint32, ActivityRechargeShop> shops = 1;
}

message ActivityRechargeShop {
  map<uint32, uint32> buy_counts = 1;
}

message C2L_ActivityRechargeGet {}

message L2C_ActivityRechargeGet {
  uint32 ret = 1;
  ActivityRecharge activity_recharge = 2;
}

message C2L_ActivityRechargeBuy {
  uint32 shop_id = 1;
  uint32 gift_id = 2;
  uint32 buy_num = 3;  // 钻石购买才有值
}

message L2C_ActivityRechargeBuy {
  uint32 ret = 1;
  uint32 shop_id = 2;
  uint32 gift_id = 3;
  uint32 buy_type = 4;  // 0 购买成功，1 预购买
  repeated Resource awards = 5;
  ActivityRechargeShop shop = 6;
  uint32 buy_num = 7;
}

message MonthlyCard {
  repeated MonthlyCardData card_datas = 1;
  uint32 dispatch_refresh_count = 2;  // 月卡存在之后的刷新次数,每日重置
}

message MonthlyCardData {
  uint32 sys_id = 1;         // 月卡id
  int64 expire_time = 2;     // 月卡过期时间
  bool auto_send_award = 3;  // 是否需要自动发奖
  // false: 手动领奖置为false
  // true: 月卡初始化、resetDaily函数执行时置为true
}

message C2L_MonthlyCardGetData {}
message L2C_MonthlyCardGetData {
  uint32 ret = 1;
  MonthlyCard monthly_card = 2;
}

message C2L_MonthlyCardReceiveAward {
  uint32 sys_id = 1;  // 月卡id
}

message L2C_MonthlyCardReceiveAward {
  uint32 ret = 1;
  uint32 sys_id = 2;  // 月卡id
  repeated Resource awards = 3;
}

message L2C_MonthlyCardRechargeNotify {
  uint32 ret = 1;
  repeated Resource awards = 2;
  MonthlyCardData monthly_card = 3;
}

message PassData {
  uint32 sys_id = 1;                                // 战令ID
  bool buy = 2;                                     // 是否购买
  map<uint32, PassReceiveSate> receive = 3;         // 对应的奖励状态
  map<uint32, TaskTypeProgress> task_progress = 4;  // 任务进度
  PassActive active_pass = 5;
  bool buy2 = 6;  // 是否购买2
}

// 活跃战令独有的结构
message PassActive {
  int64 active_time = 1;                            // 激活时间，供战令周期重置时使用
  map<uint32, bool> receive_point = 2;              // 已领取过积分的任务 key:pass_cycle_task_info.xml的id
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
}

message PassReceiveSate {
  uint32 id = 1;     // pass_task_info中任务的ID
  bool free = 2;     // 免费奖励是否领取
  bool charge = 3;   // 充值奖励是否领取
  bool charge2 = 4;  // 充值奖励2是否领取
}

message C2L_PassGetData {
  //  repeated uint32 sys_id = 1;
  reserved 1;
  reserved "sys_id";
  repeated uint32 sys_ids = 2;
}

message L2C_PassGetData {
  uint32 ret = 1;
  //  repeated PassData pass = 2;  //战令数据
  reserved 2;
  reserved "pass";
  repeated PassData pass_datas = 3;  // 战令数据
  repeated uint32 sys_ids = 4;
}

message C2L_PassReceiveAward {
  uint32 sys_id = 1;
  repeated uint32 ids = 2;  // pass_task_info中任务的ID
}

message L2C_PassReceiveAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;                    // pass_task_info中任务的ID
  repeated Resource free_awards = 3;          // 返回免费奖励
  repeated Resource charge_awards = 4;        // 返回收费奖励
  repeated PassReceiveSate update_state = 5;  // 此次变化的奖励状态
  uint32 sys_id = 6;
}

message L2C_PassBuyNotify {
  uint32 ret = 1;
  uint32 sys_id = 2;             // 购买的战令ID
  repeated Resource awards = 3;  // 返回的资源
  uint32 buy_type = 4;           // 购买类型 1为Buy 2为Buy2
}

message L2C_PassUpdate {
  uint32 ret = 1;
  uint32 pass_id = 2;
  map<uint32, TaskTypeProgress> task_progress = 3;
  PassData pass_data = 4;  // 只有重置活跃战令时有值
  bool from_reset = 5;     // 是否由重置逻辑触发
}

message C2L_PassLevelBuy {
  uint32 sys_id = 1;             // 战令id
  repeated uint32 task_ids = 2;  // pass_task_info中id
}

message L2C_PassLevelBuy {
  uint32 ret = 1;
  uint32 sys_id = 2;
  repeated uint32 task_ids = 3;
  map<uint32, TaskTypeProgress> task_progress = 4;  // 任务进度
}

message PushGift {
  uint32 sys_id = 1;
  uint32 group_sys_id = 2;
  int64 end_time = 3;
  bool charge = 4;
}

message PushGiftInfo {
  map<uint32, PushGifts> push_gifts = 1;  // Key-触发条件 V—触发条件的礼包信息
  reserved 2;
  reserved "MazeBossCount";
  // uint32 MazeBossCount = 2;
  PushGiftCondition condition = 3;
}

message PushGiftCondition {
  map<uint32, DailyCondition> daily_trigger = 1;
  uint32 roll_num = 2;                // 每日抽卡次数的随机值
  int64 time = 3;                     // 刷新时间
  uint32 battle_failed_roll_num = 4;  // 战斗失败次数统计
}

message DailyCondition {
  uint64 progress = 1;  // 触发条件进度
  uint32 count = 2;     // 今日触发次数
  int64 time = 3;       //
}

message PushGifts {
  repeated PushGift push_gift = 1;      // 推送礼包队列
  uint32 weight = 2;                    // 权重
  uint32 up = 3;                        // 还剩多少次升权重
  uint32 down = 4;                      // 还剩多少次减权重
  map<uint32, uint32> group_count = 5;  // 组内能买多少次
}

message C2L_PushGiftGetData {}

message L2C_PushGiftGetData {
  uint32 ret = 1;
  PushGiftInfo push_gifts = 2;  // 礼包信息
}

message L2C_PushGiftRechargeAward {
  uint32 ret = 1;
  PushGift push_gift = 2;               // 充值的礼包信息
  repeated Resource awards = 3;         // 返回的资源
  map<uint32, uint32> group_count = 4;  // 礼包剩余购买次数
}

message L2C_PushGiftUpdate {
  uint32 ret = 1;
  repeated PushGift push_gift = 2;  // 生成的礼包信息
}

message C2L_RecvShareAward {
  uint32 type = 1;  // 领奖类型 默认为0
}

message L2C_RecvShareAward {
  uint32 ret = 1;
  repeated Resource awards = 2;
}

message Rate {
  uint32 score = 1;    // 分数
  string content = 2;  // 评论内容
}

message C2L_RateGetStatus {}
message L2C_RateGetStatus {
  uint32 ret = 1;
  bool status = 2;  // true: 需要评分， false: 不需要评分
}

message C2L_RateScore {
  uint32 score = 1;    // 分数
  string content = 2;  // 评论内容
}

message L2C_RateScore {
  uint32 ret = 1;
}

message C2L_GetGiftCodeAward {
  string code = 1;
}

message L2C_GetGiftCodeAward {
  uint32 ret = 1;
  string code = 2;
  repeated Resource awards = 3;
}

message C2L_ClientSetMultiLang {
  string lang = 1;   // 语言
  bool is_hand = 2;  // 玩家收到切换语言
}

message L2C_ClientSetMultiLang {
  uint32 ret = 1;
  bool is_hand = 2;  // 玩家收到切换语言
}

message C2L_ClientGetMultiLang {}

message L2C_ClientGetMultiLang {
  uint32 ret = 1;
  string lang = 2;          // 语言
  uint32 data_version = 3;  // 数据表版本号
}

message OperateActivityConfig {
  repeated OperateActivityInfo infos = 1;          // 活动信息
  repeated OperatePageInfo pages = 2;              // 页签信息
  repeated OperateGiftInfo gifts = 3;              // 直冲礼包
  repeated OperateTaskInfo tasks = 4;              // 任务信息
  repeated PromotionGiftInfo promotion_gifts = 5;  // 新推送礼包
}

message OperateActivityInfo {
  uint32 id = 1;                       // 活动id
  uint32 page_id = 2;                  // 所属页签
  uint32 type = 3;                     // 活动类型
  uint32 value = 4;                    // 活动特殊值(type=1表示掉落时限,type=5表示战令的充值金额)
  string name = 5;                     // 活动名称
  uint32 icon = 6;                     // 活动图标
  string flag = 7;                     // 活动角标
  uint32 description = 8;              // 活动描述
  string intro = 9;                    // 活动简介
  string picture = 10;                 // 活动背景图
  uint32 npc = 11;                     // 活动看板
  string title_font = 12;              // 活动标题艺术字
  uint32 level = 13;                   // 限制等级
  uint32 server_limit = 14;            // 开服限制
  uint32 time_type = 15;               // 时间类型
  uint32 view_time = 16;               // 预览时间
  uint32 open_time = 17;               // 开始时间
  uint32 end_time = 18;                // 结束时间
  uint32 force_end = 19;               // 强制结束
  uint32 function_pic = 20;            // 功能按钮图标
  string function_txt = 21;            // 功能按钮文字
  repeated uint32 function_btns = 22;  // 功能按钮跳转
  repeated Resource shows = 23;        // 显示资源
  uint32 vip_level = 24;               // vip等级限制
  uint32 dungeon_id = 25;              // 主线关卡ID
  uint32 login_put = 26;               // 客户端推送方式
  uint32 count_down = 27;              // 倒计时
  uint32 novice_protection = 28;       // 新手保护天数
  uint32 reset_type = 29;              // 重置类型
  uint32 client_template = 30;         // 前端模板
  string page_button_picture = 31;     // 切页按钮图片
  uint32 page_button_sort = 32;        // 切页按钮排序
  string page_button_name = 33;        // 切页按钮名称
  uint32 page_button_id = 34;          // 切页按钮ID
  reserved 35;
  reserved "page_button_icon";
  // string page_button_icon = 35;        // 切页按钮ICON
  uint32 res_coin = 36;          // 资源栏
  uint32 gift_module = 37;       // 礼物模板ID
  string gift_icon_path = 38;    // 礼物入口ICON路径
  string gift_pic = 39;          // 礼物界面图片
  string gift_name = 40;         // 礼物入口按钮名称
  uint32 is_round = 41;          // 是否是轮次活动 0:否 1:是
  uint32 round_type = 42;        // 轮次活动类型ID
  uint32 level_max = 43;         // 等级上限
  repeated uint32 pgp_ids = 44;  // 人群包ID
  uint32 recharge_min = 45;      // 累充限制最小值
  uint32 recharge_max = 46;      // 累充限制最大值
}

message OperatePageInfo {
  uint32 id = 1;        // 活动id
  string name = 2;      // 活动名称
  uint32 picture = 3;   // 背景图
  string flag = 4;      // 角标
  uint32 priority = 5;  // 优先级
}

message OperateGiftInfo {
  uint32 id = 1;                  // 礼包id
  uint32 act_id = 2;              // 活动id
  string icon = 3;                // 礼包icon
  string name = 4;                // 礼包名称
  uint32 count = 5;               // 礼包数量
  uint32 flag = 6;                // 礼包角标
  uint32 reset_type = 7;          // 重置类型
  uint32 number = 8;              // 购买次数
  uint32 internal_id = 9;         // 关联充值金额
  uint32 original_price = 10;     // 原价
  uint32 rebuy = 11;              // 已拥有状态下是否可购买
  uint32 redpoint = 12;           // 开放时是否出现红点
  repeated Resource awards = 13;  // 奖励
  uint32 amount_min = 14;         // 充值礼包下限
  uint32 amount_max = 15;         // 充值礼包上限
  string sdk_id = 16;             // SdkID
  string awards_specially = 17;   // 礼物特效
}

message OperateTaskInfo {
  uint32 id = 1;             // 活动任务ID
  uint32 act_id = 2;         // 活动id
  string name = 3;           // 任务描述
  uint32 event = 4;          // 任务类型
  uint32 describe_type = 5;  // 任务记录类型
  uint32 value1 = 6;
  uint32 value2 = 7;
  uint64 progress = 8;           // 进度完成值
  repeated Resource awards = 9;  // 奖励
  uint32 jump_target = 10;       // 跳转参数
  string awards_specially = 11;  // 礼物特效
  uint32 round = 12;             // 轮次
  uint32 big_specially = 13;     // 大奖特效
}

message PromotionGiftInfo {
  uint32 id = 1;                            // 礼包id
  uint32 act_id = 2;                        // 活动id
  string icon = 3;                          // 礼包icon
  string name = 4;                          // 礼包名称
  uint32 count = 5;                         // 礼包数量
  uint32 flag = 6;                          // 礼包角标
  uint32 reset_type = 7;                    // 重置类型
  uint32 number = 8;                        // 购买次数
  uint32 internal_id = 9;                   // 关联充值金额
  uint32 original_price = 10;               // 原价
  uint32 rebuy = 11;                        // 已拥有状态下是否可购买
  uint32 redpoint = 12;                     // 开放时是否出现红点
  repeated Resource awards = 13;            // 奖励
  uint32 amount_min = 14;                   // 充值礼包下限
  uint32 amount_max = 15;                   // 充值礼包上限
  string sdk_id = 16;                       // SdkID
  string awards_specially = 17;             // 礼物特效
  repeated Resources optional_awards = 18;  // 可选奖励
  uint32 index = 19;                        // 所属序号（连购礼包用）
  uint32 free = 20;                         // 是否免费
}

//$<ROP redis|map|o:uint64 >
message OperateActivity {
  uint32 id = 1;                     //$<ROP unique >
  OperateGift gift = 2;              // 直充
  OperateTask task = 3;              // 任务活动
  PromotionGift promotion_gift = 4;  // 新推送礼包
}

message GoodsState {
  uint32 id = 1;                     // 商品id
  uint32 count = 2;                  // 已购买次数
  int64 time = 3;                    // 重置时间
  repeated int32 target_awards = 4;  // 选择的奖励(自选礼包用,未选择时值为-1)
}

message OperateGift {
  int64 time = 1;                     // 检查时间
  map<uint32, GoodsState> goods = 2;  // 商品信息
  uint32 amount = 3;                  // 充值总金额
}

message OperateTask {
  int64 time = 1;
  map<uint32, bool> receive_awarded = 2;                 // 已领取的奖励状态
  map<uint32, TaskTypeProgress> task_type_progress = 3;  // 不同的任务进度
  bool round_reparation = 4;                             // 轮次补偿
}

message PromotionGift {
  map<uint32, GoodsState> goods = 1;  // 商品信息
  uint32 level = 2;                   // 初始化礼包时的等级
  uint32 amount = 3;                  // 初始化时的累充金额
  bool pgp_exist = 4;                 // 初始化时，是否属于人群包
}

message L2C_NotifyBanCmd {
  uint32 cmd = 1;
}

message C2L_OperateActivityGetData {}

message L2C_OperateActivityGetData {
  uint32 ret = 1;
  repeated OperateActivity activities = 2;
}

message L2C_OperateActivityCanXMLUpdate {
  uint32 ret = 1;
  repeated uint32 act_ids = 2;
  repeated uint32 delete = 3;
}

message C2L_OperateActivityGetXML {
  repeated uint32 act_ids = 1;
}

message L2C_OperateActivityGetXML {
  uint32 ret = 1;
  repeated uint32 act_ids = 2;
  OperateActivityConfig config = 3;
}

message L2C_OperateActivityRecharge {
  uint32 ret = 1;
  uint32 id = 2;
  OperateGift gift = 3;              // 礼包状态
  repeated Resource awards = 4;      // 充值获得
  PromotionGift promotion_gift = 5;  // 新推送礼包
}

message C2L_OperateActivityInitActivity {
  repeated uint32 act_ids = 1;
}

message L2C_OperateActivityInitActivity {
  uint32 ret = 1;
  repeated uint32 act_ids = 2;
  repeated OperateActivity activities = 3;
}

message C2L_OperateActivityGetTaskReward {
  uint32 act_id = 1;
  repeated uint32 task_ids = 2;
}

message L2C_OperateActivityGetTaskReward {
  uint32 ret = 1;
  uint32 act_id = 2;
  repeated uint32 task_ids = 3;
  OperateTask task = 4;
  repeated Resource reward = 5;
}

message L2C_OperateActivityUpdateData {
  uint32 ret = 1;
  repeated OperateActivity change = 2;
  repeated uint32 delete = 3;
}

message C2L_UpdateTop5 {
  repeated uint64 hero_ids = 1;
}

message L2C_UpdateTop5 {
  uint32 ret = 1;
}

message C2L_HeroTestCalPower {}

message L2C_HeroTestCalPower {
  uint32 ret = 1;
  int64 average_time = 2;
}

message Goddess {
  uint32 id = 1;  // 女神ID
  reserved 5, 6, 7, 8;
  reserved "last_finished", "fight_pass", "taken_status", "unlock_id";
  uint32 level = 2;               // 女神等级
  uint32 exp = 3;                 // 经验
  repeated uint32 favourite = 4;  // 喜欢食物的标记为
  // uint32 last_finished = 5;  //上一个完成章节
  // bool fight_pass = 6;       //本章节是否战斗通过了
  // uint32 taken_status = 7;   //奖励领取状态
  // uint32 unlock_id = 8;      //解锁章节ID
  repeated uint32 goddess_story = 9;  // 英雄起源已领奖的ID
  repeated uint32 suitIds = 10;       // 解锁的服装ID
  bool collect = 11;                  // 收藏状态
  uint32 equip_suit = 12;             // 装备的外观
  uint32 recovery_count = 13;         // 恢复次数
  int64 recovery_time = 14;           // 上次恢复时间
  bool activity_active = 15;          // 活动是否激活
}

message GoddessContractInfo {
  map<uint32, Goddess> Goddess = 1;
  //  uint32 level = 2;
  reserved 2;
  reserved "level";
  map<uint32, uint32> collections = 3;  // key: collectionID  value: unlock_level
  reserved 4;
  reserved "total_level_recv_level";
  reserved 5;
  reserved "activity_goddess_state";
  repeated uint32 activity_recv = 6;   // 活动领奖
  repeated uint32 activity_state = 7;  // 激活的女武神
}

message C2L_GoddessContractGetData {}

message L2C_GoddessContractGetData {
  uint32 ret = 1;
  uint32 total_exp = 2;
  uint32 total_level = 4;
  reserved 3;
  reserved "Goddess";
  GoddessContractInfo goddess_contract = 5;
  uint32 touch_count = 6;  // 已抚摸次数次数
}

message C2L_GoddessFeed {
  uint32 goddess_id = 1;  // 女武神ID
  uint32 item_id = 2;     // 物品ID
  uint32 item_count = 3;  // 物品个数
}

message L2C_GoddessFeed {
  uint32 ret = 1;
  uint32 goddess_id = 2;                // 武神ID
  uint32 item_id = 3;                   // 物品ID
  uint32 item_count = 4;                // 物品个数
  Goddess Goddess = 6;                  // 女武神信息
  uint32 total_level = 7;               // 总经验
  uint32 total_exp = 8;                 // 总等级
  map<uint32, uint32> collections = 9;  // key: collectionID  value: unlock_level
}

message C2L_GoddessTouch {
  uint32 goddess_id = 1;  // 女武神ID
  uint32 body_type = 2;   // 触摸区域类型
}

message L2C_GoddessTouch {
  uint32 ret = 1;
  uint32 goddess_id = 2;                // 女武神ID
  uint32 body_type = 3;                 // 触摸区域类型
  Goddess goddess = 5;                  // 女武神信息
  uint32 total_level = 6;               // 总经验
  uint32 total_exp = 7;                 // 总等级
  uint32 touch_count = 8;               // 剩余次数
  map<uint32, uint32> collections = 9;  // key: collectionID  value: unlock_level
}

message C2L_GoddessStoryAward {
  uint32 story_id = 1;  // 故事ID
}

message L2C_GoddessStoryAward {
  uint32 ret = 1;
  uint32 story_id = 2;           // 故事ID
  repeated Resource reward = 3;  // 资源
  Goddess goddess = 4;           // 女武神信息
}

message C2L_GoddessUnlock {
  uint32 goddess_id = 1;  // 女武神ID
}

message L2C_GoddessUnlock {
  uint32 ret = 1;
  uint32 goddess_id = 2;  // 女武神ID
  Goddess goddess = 3;    // 女武神信息
}

message C2L_GoddessChapterFight {
  uint32 id = 1;                // 章节id
  repeated uint32 assists = 2;  // 协助的怪物列表
  bytes client_data = 3;
}

message L2C_GoddessChapterFight {
  uint32 ret = 1;
  uint32 id = 2;         // 章节id
  string report_id = 3;  // 战报id
  bool win = 4;          // 是否胜利
}

message C2L_GoddessCollect {
  uint32 goddess_id = 1;
  bool collect = 2;
}

message L2C_GoddessCollect {
  uint32 ret = 1;
  uint32 goddess_id = 2;
  bool collect = 3;
  Goddess goddess = 4;
}

message L2C_GoddessUpdateSuitIds {
  uint32 ret = 1;
  repeated uint32 suits = 2;
  Goddess goddess = 3;
}

message C2L_GoddessChangeSuit {
  uint32 goddess_id = 1;
  uint32 suit_id = 2;
}

message L2C_GoddessChangeSuit {
  uint32 ret = 1;
  uint32 goddess_id = 2;
  uint32 suit_id = 3;
  Goddess goddess = 4;
}

message C2L_GoddessRecovery {
  uint32 goddess_id = 1;
}

message L2C_GoddessRecovery {
  uint32 ret = 1;
  uint32 goddess_id = 2;
  Goddess goddess = 3;
  uint32 total_level = 4;               // 总经验
  uint32 total_exp = 5;                 // 总等级
  map<uint32, uint32> collections = 6;  // key: collectionID  value: unlock_level
}

message C2L_GoddessSuitUnlock {
  uint32 suit_id = 1;
}

message L2C_GoddessSuitUnlock {
  uint32 ret = 1;
  uint32 suit_id = 2;
  Goddess goddess = 3;
}

message C2L_ActivityGoddessRecvRecoveryAward {
  uint32 id = 1;
}

message L2C_ActivityGoddessRecvRecoveryAward {
  uint32 ret = 1;
  uint32 id = 2;
  repeated uint32 recv = 3;
  repeated Resource reward = 4;  // 资源
}

message C2L_ActivityGoddessActive {
  uint32 id = 1;
}

message L2C_ActivityGoddessActive {
  uint32 ret = 1;
  uint32 id = 2;
  repeated uint32 activity_state = 3;
}

// 多个玩家简要快照
message MultipleMiniUserSnapshot {
  repeated MiniUserSnapshot mini_info = 1;
}

message C2L_WrestleInfo {}
message L2C_WrestleInfo {
  uint32 ret = 1;
  uint32 level = 2;                 // 赛场等级
  uint32 rank = 3;                  // 排名
  uint32 total_rank = 4;            // 总排名，赛季结算完毕，下赛季未开始显示
  uint32 season_status = 5;         // 赛季状态， 0：正常 1：结算 2：准备
  uint32 received_level_award = 6;  // 赛季已领等级奖励，位存储
  uint32 season_max_level = 7;      // 赛季达到的最高赛场等级
  bool be_defeated = 8;             // 被击败过
  uint32 pre_season_level = 9;      // 上一个赛季的最终赛场等级
  uint32 pre_season_rank = 10;      // 上一个赛季的最终排名
  int64 enter_tm = 11;              // 进入赛场时间
}

// 获取地图信息：赛场前三头像、当前所在赛场、上次所在赛场
message C2L_WrestleMapInfo {}
message L2C_WrestleMapInfo {
  uint32 ret = 1;
  map<uint32, MultipleMiniUserSnapshot> users = 2;  // key:赛场等级  value：玩家简要快照
  uint32 now_level = 3;                             // 当前所在赛场等级
  uint32 last_level = 4;                            // 上次所处赛场等级
  uint32 last_room_rank = 5;                        // 结算前房间内排名
}

// 获取赛场头部玩家
message C2L_WrestleTopUserList {
  uint32 level = 1;  // 赛场等级
}
message L2C_WrestleTopUserList {
  uint32 ret = 1;
  uint32 level = 2;  // 赛场等级
  repeated UserSnapshot top_users = 3;
}

message WrestleOpponentInfo {
  UserSnapshot base = 1;  // 基本信息
  uint32 rank = 2;        // 排名
  uint32 score = 3;       // 积分
  uint32 status = 4;      // 对手状态 0:正常 1：可复仇 2：已击败 对应 common.proto的WRESTLE_OP_STATUS
  int64 cd_start = 5;     // CD开始时间
  uint32 bot_id = 6;      // 机器人ID（为0代表不是机器人）
}

// 获取房间信息
message C2L_WrestleRoomInfo {
  bool need_change = 1;
}
message L2C_WrestleRoomInfo {
  uint32 ret = 1;
  repeated WrestleOpponentInfo opponents = 2;
  bool new_log_tip = 3;  // 新日志提示
  uint32 total_rank = 4;
  bool be_defeated = 5;                           // 被击败过
  bool need_change = 6;                           // 请求参数回传
  WrestleUserScoreAndRank last_fight_change = 7;  // 最后一场战斗前的徽章数
  uint32 room_id = 8;                             // 房间ID
  uint32 season_join_num = 9;                     // 赛季参与次数
  int64 next_change_room_tm = 10;                 // 下次更换房间的时间
}

// 获取历史记录
message C2L_WrestleFightLog {}
message L2C_WrestleFightLog {
  uint32 ret = 1;
  repeated WrestleLog logs = 2;
}

// wrestle历史记录信息
//$<ROP redis|map|wrestle_log:uint64 >
message WrestleLog {
  uint32 id = 1;                  //$<ROP unique> //日志循环队列id
  MiniUserSnapshot attacker = 2;  // 进攻方
  MiniUserSnapshot defender = 3;  // 防守方
  string report_id = 4;           // 战报id
  int64 tm = 5;                   // 记录的时间
  int32 rank_change = 6;          // 排名变化
  bool win = 7;                   // attacker是否胜利
  uint64 log_id = 8;              // 日志唯一id
  uint32 level = 9;               // 赛场等级
  bool bot = 10;                  // defender是否是机器人
  uint32 room_id = 11;            // 房间id
}

message WrestleLogs {
  repeated WrestleLog logs = 1;
}
message WrestleLogDeletes {
  repeated uint32 ids = 2;
}

// 战斗
message C2L_WrestleFight {
  uint64 id = 1;    // 对手玩家id
  uint64 sid = 2;   // 服务器ID
  uint32 rank = 3;  // 对手玩家排名或分数
  bytes client_data = 4;
}
message L2C_WrestleFight {
  uint32 ret = 1;
  uint64 id = 2;   // 对手玩家id
  uint64 sid = 3;  // 服务器ID
  bool win = 4;    // 是否胜利
  repeated Resource awards = 5;
  string report_id = 6;                         // 战报id
  WrestleUserScoreAndRank self_change = 8;      // 玩家，战斗前后分数与排名
  WrestleUserScoreAndRank opponent_change = 9;  // 对手，战斗前后分数与排名
  bool promoted = 10;                           // 是否自动晋升
}

// 战斗前后，分数与排名
message WrestleUserScoreAndRank {
  uint32 new_score = 1;  // 战斗前，分数
  uint32 old_score = 2;  // 战斗后，分数
  uint32 new_rank = 3;   // 战斗前，排名
  uint32 old_rank = 4;   // 战斗后，排名
}

// 点赞
message C2L_WrestleLike {
  uint64 id = 1;
}

message L2C_WrestleLike {
  uint32 ret = 1;
  uint64 id = 2;
  repeated Resource awards = 3;
  repeated uint32 like_num = 4;
}

message C2L_WrestleRankList {
  uint32 type = 1;  // 1:本赛季 2:上赛季
}

message L2C_WrestleRankList {
  uint32 ret = 1;
  uint32 type = 2;
  repeated RankValue list = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
}

// 领取任务奖励
message C2L_WrestleRecvTaskAward {
  repeated uint32 ids = 1;  // 任务id列表，wrestle_level_task_info.xml中id
}

message L2C_WrestleRecvTaskAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  repeated Resource awards = 3;
  uint32 received_level_award = 4;
}

// 荣耀殿堂数据
message C2L_WrestleHallOfFame {}

message L2C_WrestleHallOfFame {
  uint32 ret = 1;
  repeated UserSnapshot list = 2;
  repeated uint32 like_num = 3;  // 前三玩家被点赞数
  uint32 self_rank = 4;
  RankValue self_value = 5;  // 自己的排名数据
}

// 更换房间
message C2L_WrestleChangeRoom {}
message L2C_WrestleChangeRoom {
  uint32 ret = 1;
  uint32 room_id = 2;
  repeated WrestleOpponentInfo opponents = 3;
  int64 next_change_room_tm = 4;  // 下次更换房间的时间
  bool success = 5;               // 是否更换成功
}

// 房间数据更新
message L2C_WrestleRoomUpdate {
  uint32 ret = 1;
}

message L2C_VipGiftRechargeNotify {
  uint32 ret = 1;
  repeated Resource awards = 2;
  uint32 vip = 3;
}

message DivineDemonSummonV2 {
  repeated uint32 color_guarantee = 1;  // 0:紫色保底次数 1:橙色保底次数 2:红色保底次数
  uint32 not_up_hero_count = 2;         // 玩家距离上次没抽到Up将的次数
  uint32 diamond_summon_count = 3;      // 钻石抽次数
  uint32 daily_free_summon = 4;         // 每日免费抽取次数
  uint32 total_summon_count = 5;        // 抽取总次数
  uint32 total_red_card = 6;            // 总红卡
}

message DivineDemon {
  uint64 id = 1;                      // 神魔抽卡唯一id
  uint32 sys_id = 2;                  // 量表id
  DivineDemonSummon summon = 3;       // 抽卡数据，连抽时有值
  DivineDemonTask summon_task = 4;    // 累抽任务
  DivineDemonTask active_task = 5;    // 活跃任务
  DivineDemonTask up_star_task = 6;   // 升星任务
  DivineDemonSummonV2 summon_v2 = 7;  // 抽卡大小保底数据
}

message DivineDemonSummon {
  uint32 super_chance_status = 1;                          // sc状态，0：默认状态，1，2，3: 提升概率的不同状态
  int64 super_chance_start_time = 2;                       // sc开始时间；super_chance_status = 1，2，3 时有值
  uint32 summon_total_count = 3;                           // 抽卡总次数
  uint32 red_hero_count = 4;                               // 红卡次数
  uint32 plural_summon_count = 5;                          // 十连抽次数
  map<uint32, DivineDemonSummonRedCard> up_hero_data = 6;  // key: 每期卡池id  -- 已废弃
  uint32 wish_hero_count = 7;                              // 心愿将数量
  DivineDemonWishRecord wish_record = 8;
  uint32 diamond_summon_count = 9;  // 钻石抽次数
  bool is_display_wish_hero = 10;   // 前X抽是否抽中心愿将：X：量表中配置
}

message DivineDemonWishRecord {
  uint32 group = 1;           // 随机组
  uint32 red_card_count = 2;  // 红卡次数
}

message DivineDemonSummonRedCard {
  uint32 red_card_count = 1;  // 红卡次数：第一张红卡不是概率up英雄，则第二张红卡必定获得概率up英雄
  bool is_up_hero = 2;        // 是否是概率up英雄
}

message DivineDemonTask {
  map<uint32, TaskTypeProgress> task_progress = 1;  // 任务进度
  map<uint32, bool> receive_awarded = 2;            // 已领取奖励的任务
}

message DivineDemonOpenActivity {
  uint64 id = 1;         // 唯一id
  uint32 sys_id = 2;     // 活动id
  int64 start_time = 3;  // 开始时间
  int64 end_time = 4;    // 结束时间
}

message C2L_DivineDemonGetOpenActivity {}
message L2C_DivineDemonGetOpenActivity {
  uint32 ret = 1;
  repeated DivineDemonOpenActivity activities = 2;  // 可能会存在2个，非通服默认配置的活动 + 运营配置的通服活动
  DivineDemon divine_demon = 3;
  //  DivineDemonWishRecord wish_record = 4;
}

message C2L_DivineDemonSummon {
  uint64 id = 1;                // 活动id
  uint32 summon_type = 2;       // 抽卡类型：1：单抽 2：连抽
  uint32 cur_hero = 3;          // 英雄卡池
  uint32 summon_cost_type = 4;  // 抽卡消耗类型：1：钻石 (通服活动生效) 2：抽卡卷
}

message L2C_DivineDemonSummon {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 summon_type = 3;  // 抽卡类型
  repeated Resource awards = 4;
  DivineDemonSummon hero_summon = 5;
  uint32 cur_hero = 6;
  uint32 summon_cost_type = 7;        // 抽卡消耗类型：1：钻石 (通服活动生效) 2：抽卡卷
  DivineDemonSummonV2 summon_v2 = 8;  // 新的保底次数
}

message C2L_DivineDemonReceiveTaskAward {
  uint64 id = 1;
  repeated uint32 task_ids = 2;  // 任务id
  uint32 task_type = 3;          // 任务类型
}

message L2C_DivineDemonReceiveTaskAward {
  uint32 ret = 1;
  uint64 id = 2;
  repeated uint32 task_ids = 3;  // 任务id
  uint32 task_type = 4;          // 任务类型
  repeated Resource awards = 5;
}

message L2C_DivineDemonUpdateTaskProgress {
  uint32 ret = 1;
  uint64 id = 2;  // 神魔抽卡唯一id
  uint32 task_type = 3;
  map<uint32, TaskTypeProgress> task_progress = 4;
}

message L2C_DivineDemonUpdate {
  uint64 id = 1;
}

message C2L_GetCrossRankFirst {
  repeated uint32 rank_ids = 1;  // 排行榜id列表
}

message L2C_GetCrossRankFirst {
  uint32 ret = 1;
  repeated uint32 rank_ids = 2;
  repeated FirstRankValue list = 3;
}

message C2L_GetSeasonRankFirst {
  repeated uint32 rank_ids = 1;  // 排行榜id列表
}

message L2C_GetSeasonRankFirst {
  uint32 ret = 1;
  repeated uint32 rank_ids = 2;
  repeated FirstRankValue list = 3;
}

message C2L_GetSeasonRankList {
  uint32 ranking_id = 1;
}

message L2C_GetSeasonRankList {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated RankValue list = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
  uint32 total = 6;
}

message ViewLinks {
  repeated uint32 link_ids = 1;
}

// 联结数据
message LinkInfo {
  map<uint32, ViewLinks> view_links = 1;  // 查看过的联结信息 heroSysID =>
}

message C2L_LinkInfo {}

message L2C_LinkInfo {
  uint32 ret = 1;
  LinkInfo info = 2;
}

message C2L_LinkSetView {
  uint32 hero_sysID = 1;  // 英雄系统id
  uint32 link_id = 2;     // 联结id
}

message L2C_LinkSetView {
  uint32 ret = 1;
  uint32 hero_sysID = 2;
  uint32 link_id = 3;
}

message C2L_FunctionGetStatus {
  repeated uint32 func_id = 1;  // 功能id
}

message L2C_FunctionGetStatus {
  uint32 ret = 1;
  repeated uint32 func_id = 2;
  map<uint32, bool> status = 3;
}

message Announcement {
  uint32 id = 1;                     // 公告id
  string title = 2;                  // 公告标题
  uint32 title_align = 3;            // 标题位置：0-靠左对齐，1-居中，2-靠右对齐
  string content = 4;                // 公告内容
  uint32 weight = 5;                 // 权重
  int64 start_time = 6;              // 开始时间
  int64 end_time = 7;                // 结束时间
  uint32 type = 8;                   // 公告类型，1-公告，2-活动，3-须知
  int64 update_time = 9;             // 更新时间：显示红点使用
  repeated int64 params = 10;        // 参数
  repeated uint32 op_ids = 11;       // 要筛选的运营商id
  repeated uint32 channel_ids = 12;  // 要筛选的二级运营商id,对于37就是gid.为空就是不用筛选这个
}

message C2L_AnnouncementGetData {
  string lang = 1;  // 语言类型
}

message L2C_AnnouncementGetData {
  uint32 ret = 1;
  repeated Announcement announcements = 2;
  string lang = 3;  // 语言类型
}

message L2C_AnnouncementUpdate {}

// 幽暗密林数据
//$<ROP redis|map|flower >
message Flower {
  uint64 id = 1;                    //$<ROP unique> //玩家id
  uint32 level = 2;                 // 等级
  uint32 exp = 3;                   // 经验
  int64 power = 4;                  // 玩家战力 --- 冗余保存
  uint64 guild_id = 5;              // 公会id --- 冗余保存
  FlowerGuide guide = 6;            // 引导相关信息
  FlowerPlant plant = 7;            // 种花相关数据
  FlowerSnatch snatch = 8;          // 协助模式下，搜到的玩家信息
  FlowerOccupy occupy = 9;          // 据点数据
  uint32 log_index_id = 10;         // 日志序号Id TODO 待删除
  bool new_log_tip = 11;            // 新日志提示 TODO 待删除
  FlowerNewLogTip snatch_log = 12;  // 协助模式新日志提示
  FlowerNewLogTip occupy_log = 13;  // 据点模式新日志提示
}

// 幽暗密林 - 引导相关信息
message FlowerGuide {
  uint32 seed_feed_step = 1;  // 引导喂哥布林的次数，用于新手引导，达到最大步骤后停止更新
  bool speed_grow = 2;        // 是否已使用过免费加速生长（首次加速生长免费，用于引导）
}

// 幽暗密林 - 协助模式 - 种花相关信息
message FlowerPlant {
  uint32 stage = 1;             // 当前阶段 0-待献祭 1-献祭中 2-生长中
  FlowerSeed seed = 2;          // 种子信息
  uint32 level = 3;             // 种花时，密林的等级
  uint32 rare = 4;              // 种花时，由FlowerSeed的score，计算得来的花的品质
  int64 start_tm = 5;           // 种花开始时间
  int64 end_tm = 6;             // 种花结束时间
  bool need_harvest = 7;        // 是否使用道具加速成熟，待收获
  repeated uint64 robbers = 8;  // 协助者id列表(前端不能用来显示协助者列表)
  uint32 type = 9;              // 种花的种类
  uint32 flower_incr_id = 10;   // 花的自增id
}

// 幽暗密林 - 抢夺模式 - 种子信息
message FlowerSeed {
  uint32 current_goblin = 1;  // 当前哥布林id
  uint32 next_goblin = 2;     // 下个哥布林id
  bool changed_goblin = 3;    // 是否已更换过哥布林
  uint32 score = 4;           // 种子分数
}

// 幽暗密林 - 协助模式 - 刷到的可协助信息
message FlowerSnatch {
  repeated uint64 ids = 1;              // 可协助玩家id列表
  int64 tm = 2;                         // 刷新时间
  repeated FlowerSnatchEnemy bots = 3;  // 机器人信息
}

// 幽暗密林 - 据点模式 - 据点
message FlowerOccupy {
  uint32 jungle_id = 1;                     // 丛林id
  uint32 flowerbed_pos = 2;                 // 花坛位置（1-5）
  int64 start_tm = 3;                       // 当前据点占领开始时间
  int64 end_tm = 4;                         // 当前据点预期占领结束时间，被抢则中断
  bool active_occupy = 5;                   // 是否是主动占领
  repeated FlowerGuildBuff guild_buff = 6;  // 公会加成
  FlowerOccupyHistory history = 7;          // 已占据点数据
  int64 preview_tm = 8;                     // 预览时间
  FlowerOccupyAward preview_award = 9;      // 预览生成的奖励
  int64 share_tm = 10;                      // 上次向公会分享花坛的时间
  uint32 extend_tm_count = 11;              // 延长占领时间的次数
}

// 幽暗密林 - 据点模式 - 已结算据点数据
message FlowerOccupyHistory {
  uint32 last_jungle_id = 1;           // 前一个丛林id
  uint32 last_flowerbed_pos = 2;       // 前一个丛林花坛位置（1-5）
  int64 last_start_tm = 3;             // 前一个据点占领开始时间
  int64 last_occupy_tm = 4;            // 前一个据点实际占领时间
  uint64 enemy_id = 5;                 // 抢走此据点的玩家id
  FlowerOccupyAward settle_award = 6;  // 累计结算的奖励
  uint32 end_type = 7;                 // 结束类型 1-时间到 2-占了新据点，老据点结算 3-被打后，分配新据点 4-被打后，未分配新据点
}

// 幽暗密林 - 据点模式 - 占领奖励
message FlowerOccupyAward {
  repeated Resource res = 1;  // 资源
  uint32 exp = 2;             // 密林经验
}

// 幽暗密林 - 据点模式 - 玩法次数
message FlowerOccupyNum {
  uint32 left_num = 1;   // 剩余进攻次数
  int64 recover_tm = 2;  // 次数开始恢复时间
  uint32 buy_num = 3;    // 已购买次数
  int64 buy_time = 4;    // 最近一次购买时间
}

// 幽暗密林 - 据点模式 - 公会加成数据
message FlowerGuildBuff {
  int64 start_tm = 1;  // 公会加成开始时间
  int64 end_tm = 2;    // 公会加成结束时间
  uint32 level = 3;    // 公会加成级别（3/4/5人）
}

// 幽暗密林 - 掠夺模式 - 对手信息
message FlowerSnatchEnemy {
  UserSnapshot snapshot = 1;
  uint32 level = 2;
  uint32 score = 3;
  uint32 stage = 4;       // 花所处阶段（能不能被协助）
  uint32 left_count = 5;  // 剩余可抢被次数
  int64 end_tm = 6;       // 采集结束时间（搜到时的数据）
  bool be_snatch = 7;     // 是否自己被抢过
  bool is_bot = 8;        // 是否是机器人
  bool be_assist = 9;     // 是否自己被协助过
}

// 密林新日志提示
message FlowerNewLogTip {
  uint32 log_index_id = 1;  // 日志序号Id
  bool new_log_tip = 2;     // 新日志提示
}

// 密林日志详情
message FlowerLogDetail {
  uint32 type = 1;               // 日志类型，对应common.proto中的FLOWER_LOG类型
  repeated Resource res = 2;     // 资源变化
  uint32 jungle_id = 3;          // 丛林id
  uint32 flowerbed_pos = 4;      // 花坛位置（1-5）
  uint32 flower_incr_id = 5;     // 花的自增id
  uint32 flower_level = 6;       // 花的等级
  uint32 flower_seed_score = 7;  // 花的种子分数
}

// 密林掠夺模式日志
//$<ROP redis|map|flower_snatch_log:uint64 >
message FlowerSnatchLog {
  uint32 id = 1;               //$<ROP unique> //日志循环队列id
  MiniUserSnapshot enemy = 2;  // 对方信息
  int64 tm = 3;                // 战斗时间
  uint64 log_id = 4;           // 日志唯一id
  bool revenge = 5;            // 是否复仇成功
  FlowerLogDetail detail = 6;  // 日志详情
  bool is_sent = 7;            // 是否已赠送友情点
  bool is_recv = 8;            // 是否已收取友情点
}

message FlowerSnatchLogs {
  repeated FlowerSnatchLog logs = 1;
}
message FlowerSnatchLogDeletes {
  repeated uint32 ids = 1;
}

// 密林据点模式日志
//$<ROP redis|map|flower_occupy_log:uint64 >
message FlowerOccupyLog {
  uint32 id = 1;               //$<ROP unique> //日志循环队列id
  MiniUserSnapshot enemy = 2;  // 对方信息
  int64 tm = 3;                // 战斗时间
  uint64 log_id = 4;           // 日志唯一id
  bool revenge = 5;            // 是否复仇成功
  FlowerLogDetail detail = 6;  // 日志详情
}

message FlowerOccupyLogs {
  repeated FlowerOccupyLog logs = 1;
}
message FlowerOccupyLogDeletes {
  repeated uint32 ids = 1;
}

// 密林 - 返回给客户端的日志类型
message FlowerLog2Client {
  uint32 id = 1;               // 日志循环队列id
  MiniUserSnapshot enemy = 2;  // 对方信息
  int64 tm = 3;                // 战斗时间
  uint64 log_id = 4;           // 日志唯一id
  bool revenge = 5;            // 是否复仇成功
  FlowerLogDetail detail = 6;  // 日志详情
  bool is_sent = 7;            // 是否已赠送友情点
  bool is_recv = 8;            // 是否已收取友情点
}

// 幽暗密林 - 日志
//  TODO 待删除
//$<ROP redis|map|flower_log:uint64 >
message FlowerLog {
  uint32 id = 1;                   //$<ROP unique> //日志循环队列id
  MiniUserSnapshot enemy = 2;      // 对方信息
  int64 tm = 3;                    // 战斗时间
  repeated Resource res = 4;       // 资源变化
  FlowerLogSpecific specific = 5;  // 密林玩法特定数据
}

// 密林玩法特定数据
//  TODO 待删除
message FlowerLogSpecific {
  uint64 log_id = 1;         // 日志唯一id
  uint32 type = 2;           // 日志类型，对应common.proto中的FLOWER_LOG类型
  uint32 jungle_id = 3;      // 丛林id
  uint32 flowerbed_pos = 4;  // 花坛位置（1-5）
  bool revenge = 5;          // 是否复仇成功
}

// 密林 - 获取主界面信息
message C2L_FlowerMainInfo {}
message L2C_FlowerMainInfo {
  uint32 ret = 1;
  Flower flower = 2;
  FlowerOccupyNum num = 3;                    // 据点玩法次数信息
  repeated Resource monthly_card_awards = 4;  // 占矿月卡奖励展示
}

// 密林 - 开始献祭（喂养种子）
message C2L_FlowerStartFeed {}
message L2C_FlowerStartFeed {
  uint32 ret = 1;
  uint32 current_goblin = 2;  // 当前哥布林id
  uint32 next_goblin = 3;     // 下个哥布林id
}

// 密林 - 献祭哥布林
message C2L_FlowerFeedGoblin {}
message L2C_FlowerFeedGoblin {
  uint32 ret = 1;
  uint32 score = 2;           // 种子分数
  uint32 current_goblin = 3;  // 当前哥布林id
  uint32 next_goblin = 4;     // 下个哥布林id
}

// 密林 - 更换哥布林
message C2L_FlowerChangeGoblin {}
message L2C_FlowerChangeGoblin {
  uint32 ret = 1;
  uint32 current_goblin = 2;  // 当前哥布林id
  uint32 next_goblin = 3;     // 下个哥布林id
}

// 密林 - 使用特殊道具，将种子升至最高品质
message C2L_FlowerFeedSpecial {}
message L2C_FlowerFeedSpecial {
  uint32 ret = 1;
  uint32 score = 2;  // 种子分数
}

// 密林 - 开始种树
message C2L_FlowerStartPlant {}
message L2C_FlowerStartPlant {
  uint32 ret = 1;
  uint32 score = 2;           // 种子分数
  int64 start_tm = 3;         // 种树开始时间
  int64 end_tm = 4;           // 种树结束时间
  uint32 flower_incr_id = 5;  // 花的自增id
}

// 密林 - 加速生长
message C2L_FlowerSpeedGrow {}
message L2C_FlowerSpeedGrow {
  uint32 ret = 1;
}

// 密林 - 收获资源
message C2L_FlowerHarvest {}
message L2C_FlowerHarvest {
  uint32 ret = 1;
  repeated Resource awards = 2;
  uint32 level = 4;                           // 密林等级
  uint32 exp = 5;                             // 密林经验
  bool crit = 6;                              // 是否暴击
  repeated Resource monthly_card_awards = 7;  // 月卡奖励
}

// 密林 - 搜索
message C2L_FlowerSearch {}
message L2C_FlowerSearch {
  uint32 ret = 1;
  repeated FlowerSnatchEnemy enemies = 2;
}

// 密林 - 协助模式，获取目标信息
message C2L_FlowerEnemiesInfo {
  repeated uint64 uids = 1;
}
message L2C_FlowerEnemiesInfo {
  uint32 ret = 1;
  repeated FlowerSnatchEnemy enemies = 2;
}

// 密林 - 协助玩家的花
message C2L_FlowerSnatch {
  uint64 uid = 1;  // 被协助玩家id
  bytes client_data = 2;
  uint32 formation_id = 3;  // 使用的被协助玩家阵容id
}

message L2C_FlowerSnatch {
  uint32 ret = 1;
  uint64 uid = 2;
  bool win = 3;                  // 是否胜利
  repeated Resource awards = 4;  // 胜利奖励
  string report_id = 5;          // 战报id
  uint32 formation_id = 6;       // 使用的被协助玩家阵容id
}

// 密林 - 获取日志列表
message C2L_FlowerLogList {
  uint32 type = 1;  // 日志类型，同common.proto中的FLOWER_LOG_TYPE
}
message L2C_FlowerLogList {
  uint32 ret = 1;
  repeated FlowerLog2Client logs = 2;
  uint32 type = 3;  // 日志类型，同common.proto中的FLOWER_LOG_TYPE
}

// 密林 - 抢夺模式复仇
message C2L_FlowerRevengeSnatch {
  uint64 log_id = 1;  // 日志id
  bytes client_data = 2;
}
message L2C_FlowerRevengeSnatch {
  uint32 ret = 1;
  uint64 log_id = 2;
  bool win = 3;                  // 是否胜利
  repeated Resource awards = 4;  // 胜利奖励
  string report_id = 5;          // 战报id
}

// 密林 - 据点模式，获取森林信息
message C2L_FlowerTimberInfo {
  uint32 id = 1;                   // 森林id
  repeated uint32 jungle_ids = 2;  // 丛林id列表
  uint32 req_type = 3;             // 客户端请求类型，原样返回，用于前端显示逻辑
}
message L2C_FlowerTimberInfo {
  uint32 ret = 1;
  uint32 id = 2;                            // 森林id
  repeated FlowerJungleOccupy jungles = 3;  // 丛林占领信息
  repeated uint32 jungle_ids = 4;           // 丛林id列表
  uint32 req_type = 5;                      // 客户端请求类型，原样返回，用于前端显示逻辑
  uint32 cur_jungle_id = 6;                 // 玩家当前所占丛林id
  uint32 cur_flowerbed_pos = 7;             // 玩家当前所占花坛位置（1-5）
}

message FlowerJungleOccupy {
  uint32 id = 1;          // 丛林id
  uint32 count = 2;       // 被占花坛总数
  uint64 gid = 3;         // 所属公会id（公会占领时有值）
  string guild_name = 4;  // 公会名（公会占领时有值）
  uint32 ally_count = 5;  // 公会成员的数量（公会占领时有值）
}

// 密林 - 据点模式，获取丛林信息
message C2L_FlowerJungleInfo {
  uint32 id = 1;  // 丛林id
}
message L2C_FlowerJungleInfo {
  uint32 ret = 1;
  uint32 id = 2;
  repeated Flowerbed users = 3;
}

message Flowerbed {
  uint32 pos = 1;  // 花坛编号（1-5）
  UserSnapshot user = 2;
  int64 start_tm = 3;          // 当前据点占领开始时间
  int64 end_tm = 4;            // 当前据点预期占领结束时间，被抢则中断
  bool active_occupy = 5;      // 是否是主动占领
  uint32 extend_tm_count = 6;  // 延长占领时间的次数
}

// 密林 - 据点模式，攻打据点
message C2L_FlowerOccupyAttack {
  uint32 jungle_id = 1;      // 丛林id
  uint32 flowerbed_pos = 2;  // 花坛编号（1-5）
  uint64 uid = 3;            // 占领者uid，无人占据为0
  bytes client_data = 4;
}
message L2C_FlowerOccupyAttack {
  uint32 ret = 1;
  uint32 jungle_id = 2;         // 丛林id
  uint32 flowerbed_pos = 3;     // 花坛编号（1-5）
  uint64 uid = 4;               // 占领者uid，无人占据为0
  bool win = 5;                 // 是否胜利
  repeated Resource costs = 6;  // 打盟友时，花费资源
  string report_id = 7;         // 战报id
  FlowerOccupyNum num = 8;      // 据点玩法次数信息
}

message C2L_FlowerExtendOccupyTime {}
message L2C_FlowerExtendOccupyTime {
  uint32 ret = 1;
  int64 end_tm = 2;
  uint32 extend_tm_count = 3;  // 延长占领时间的次数
}

// 密林 - 攻打升级守卫
message C2L_FlowerAttackLevelGuard {
  bytes client_data = 1;
}
message L2C_FlowerAttackLevelGuard {
  uint32 ret = 1;
  uint32 level = 2;              // 最新密林等级
  bool win = 3;                  // 是否胜利
  repeated Resource awards = 4;  // 胜利奖励
  string report_id = 5;          // 战报id
}

// 密林 - 密林升级
message C2L_FlowerLevelUp {}
message L2C_FlowerLevelUp {
  uint32 ret = 1;
  uint32 level = 2;              // 最新密林等级
  uint32 exp = 3;                // 最新密林经验
  repeated Resource awards = 4;  // 胜利奖励
}

// 密林 - 据点模式，购买进攻次数
message C2L_FlowerBuyOccupyAttackNum {
  uint32 num = 1;  // 购买次数
}
message L2C_FlowerBuyOccupyAttackNum {
  uint32 ret = 1;
  uint32 num = 2;                // 购买次数
  FlowerOccupyNum num_info = 3;  // 当前次数信息
}

// 密林 - 据点模式，预览结算据点奖励
message C2L_FlowerPreviewOccupyAward {}
message L2C_FlowerPreviewOccupyAward {
  uint32 ret = 1;
  FlowerOccupyAward awards = 2;               // 预览占领所得奖励
  repeated Resource monthly_card_awards = 3;  // 预览月卡奖励
}

// 密林 - 据点模式，领取采集奖励
message C2L_FlowerRecvOccupyAward {}
message L2C_FlowerRecvOccupyAward {
  uint32 ret = 1;
  FlowerOccupyAward awards = 2;               // 领取到的奖励
  repeated Resource monthly_card_awards = 3;  // 月卡奖励
}

// 密林 - 据点模式，推荐占领
message C2L_FlowerOccupyRecommend {}
message L2C_FlowerOccupyRecommend {
  uint32 ret = 1;
  uint32 jungle_id = 2;      // 丛林id
  uint32 flowerbed_pos = 3;  // 花坛编号（1-5）
}

// 密林 - 据点模式复仇
message C2L_FlowerRevengeOccupy {
  uint64 log_id = 1;  // 日志id
  bytes client_data = 2;
}
message L2C_FlowerRevengeOccupy {
  uint32 ret = 1;
  uint64 log_id = 2;
  bool win = 3;                 // 是否胜利
  string report_id = 4;         // 战报id
  repeated Resource costs = 5;  // 反击消耗
  FlowerOccupyNum num = 6;      // 据点玩法次数信息
}

// 密林 - 据点历史结算数据
message C2L_FlowerOccupyHistory {}
message L2C_FlowerOccupyHistory {
  uint32 ret = 1;
  FlowerOccupyHistory history = 2;
  repeated Resource monthly_card_awards = 3;  // 占矿月卡奖励展示
}

// 密林 - 向公会分享花坛位置
message C2L_FlowerShareFlowerbed {
  uint32 id = 1;  // 丛林id
}
message L2C_FlowerShareFlowerbed {
  uint32 ret = 1;
  uint32 id = 2;       // 丛林id
  int64 share_tm = 3;  // 本次分享发生的时间
}

// 密林 - 据点模式，领取预览结算据点奖励
message C2L_FlowerRecvPreviewOccupyAward {}
message L2C_FlowerRecvPreviewOccupyAward {
  uint32 ret = 1;
  FlowerOccupyAward awards = 2;               // 预览占领所得奖励
  repeated Resource monthly_card_awards = 3;  // 预览月卡奖励
  int64 prview_tm = 4;                        // 奖励预览时间
}

message C2L_FlowerAssistSendLike {
  repeated uint64 log_ids = 1;  // 日志id
}

message L2C_FlowerAssistSendLike {
  uint32 ret = 1;
  repeated uint64 log_ids = 2;  // 日志id
}

message C2L_FlowerAssistRecvLike {
  repeated uint64 log_ids = 1;  // 日志id
}

message L2C_FlowerAssistRecvLike {
  uint32 ret = 1;
  repeated uint64 log_ids = 2;   // 日志id
  repeated Resource awards = 3;  // 获得的奖励
}

message L2C_FlowerAssistNotify {}

// 密林 - 小助手，占矿推荐地块
message FlowerOccupyAssist {
  uint32 jungle_id = 1;      // 丛林id
  uint32 flowerbed_pos = 2;  // 花坛编号（1-5）
  uint64 uid = 3;            // 占领者uid，无人占据为0
  int64 power = 4;           // 队伍战力
}

// 密林 - 小助手，占矿推荐列表
message C2L_FlowerOccupyAssistList {
  uint32 rare = 1;  // 矿脉品质 0-不指定品质 非0-对应品质值
}
message L2C_FlowerOccupyAssistList {
  uint32 ret = 1;
  uint32 rare = 2;  // 矿脉品质
  repeated FlowerOccupyAssist list = 3;
}

message MonthTasks {
  uint32 id = 1;  // 活动ID
  map<uint32, TaskTypeProgress> task_type_progress = 2;
  map<uint32, bool> awarded = 3;
  bool finish = 4;
  repeated uint32 fixed_reward_tasks = 5;  // taskId
}

// 全民无双积分达成消息
//$<ROP redis|map|month_tasks_msg >
message MonthTasksMsg {
  uint32 id = 1;                  //$<ROP unique >    //活动ID
  repeated Message messages = 2;  // 积分达成消息
}

message C2L_MonthTasksGetData {}

message L2C_MonthTasksGetData {
  uint32 ret = 1;
  repeated MonthTasks data = 2;         // 自己的数据
  repeated MonthTasksMsg messages = 3;  // 玩家目标积分达成的消息
}

message C2L_MonthTasksRecvAwards {
  uint32 id = 1;  // 活动ID
  repeated uint32 task_ids = 2;
  bool recv_daily_award = 3;  // 是否领取每日奖励
}

message L2C_MonthTasksRecvAwards {
  uint32 ret = 1;
  uint32 id = 2;
  repeated uint32 task_ids = 3;
  bool recv_daily_award = 4;
  repeated Resource awards = 5;
  bool finish = 6;
}

message L2C_MonthTasksUpdate {
  uint32 ret = 1;
  uint32 id = 2;                               // 活动ID
  map<uint32, TaskTypeProgress> progress = 3;  // 任务进度
  repeated uint32 fixed_reward_tasks = 4;      // 奖励被固定的任务ID
}

message C2L_OssBattleReport {}

message L2C_OssBattleReport {
  uint32 ret = 1;
  uint32 report_id = 2;
  string url = 3;
}

// 每日许愿
message DailyWish {
  uint32 id = 1;
  repeated uint32 take_award = 2;
  uint32 summon_count = 3;  // 剩余抽卡次数
  uint32 daily_count = 4;   // 今日触发次数
  bool is_new = 5;          // 是否是新服
}

message C2L_DailyWishGet {
  uint32 id = 1;
}

message L2C_DailyWishGet {
  uint32 ret = 1;
  uint32 id = 2;
  DailyWish daily_wish = 3;
  uint32 daily_recharge_amount = 4;
}

message C2L_DailyWishSummon {
  uint32 id = 1;
}

message L2C_DailyWishSummon {
  uint32 ret = 1;
  uint32 id = 2;
  DailyWish daily_wish = 3;
  repeated Resource award = 5;
}

message C2L_DailyWishXmlGet {
  repeated uint32 ids = 1;
}

message L2C_DailyWishXmlGet {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  repeated DailyWishActivityInfo infos = 3;
}

message L2C_DailyWishXmlUpdate {
  uint32 ret = 1;
  uint32 change_id = 2;
  uint32 delete = 3;
}

message L2C_DailyWishUpdate {
  uint32 ret = 1;
  DailyWish daily_wish = 2;
  uint32 daily_recharge_amount = 3;
}

message DailyWishActivityInfo {
  uint32 id = 1;
  string title = 2;                           // 标题
  string remarks = 3;                         // 备注
  uint32 time_type = 4;                       // 时间类型
  int64 start_time = 5;                       // 开始时间
  int64 end_time = 6;                         // 结束时间
  uint32 function_id = 7;                     // 充值跳转功能ID
  repeated DailyWishAwardInfo awardInfo = 8;  // 奖励信息
  uint32 force_end = 9;                       // 下线标记位  1 正常 2下线
  uint32 recharge_trigger = 10;               // 充值触发金额
  uint32 daily_max = 11;                      // 每天触发次数的上限
  uint32 server_limit = 12;                   // 开服天数限制
}

message DailyWishAwardInfo {
  uint32 id = 1;
  uint32 act_id = 2;             // 活动主ID
  repeated Resource awards = 3;  // 奖励
  uint32 grade = 4;              // 档位
}

message LinkSummonTime {
  uint32 id = 1;
  uint64 start_time = 2;
}

message LinkSummonTimes {
  repeated LinkSummonTime times = 1;
}

message LinkHeroSummon {
  repeated uint32 color_guarantee = 1;
  uint32 id = 2;
  ActivitySummonGuarantee activity_summon_guarantee = 3;
  uint32 drop_group_guarantee = 4;  // 掉落组保底计数
  bool first_draw = 5;              // 功能首次保底
}

message C2L_LinkHeroSummon {
  uint32 pool_id = 1;    // 卡池id
  bool plural = 2;       // 是否多次召唤
  uint32 wish_hero = 3;  // 心愿英雄
}

message L2C_LinkHeroSummon {
  uint32 ret = 1;
  uint32 pool_id = 2;              // 卡池ID
  bool plural = 3;                 // 是否多次召唤
  repeated Resource awards = 4;    // 奖励
  uint32 red_guarantee = 5;        // 红色保底次数
  uint32 free_link_summon = 6;     // 已抽取的免费抽卡次数
  uint32 wish_hero = 7;            // 心愿英雄
  uint32 wish_hero_guarantee = 8;  // 心愿保底次数
}

message C2L_LinkHeroSummonGet {}

message L2C_LinkHeroSummonGet {
  uint32 ret = 1;
  uint32 red_guarantee = 2;           // 红色保底次数
  repeated LinkSummonTime times = 3;  // 当期和下一期
  uint32 free_link_summon = 4;        // 已抽取的免费抽卡次数
  uint32 wish_hero_guarantee = 5;     // 心愿保底次数
}

message L2C_LinkHeroSummonPoolTimeUpdate {
  uint32 ret = 1;
  repeated LinkSummonTime times = 2;  // 当期和下一期
}

message C2L_LinkHeroSummonTest {
  uint32 pool_id = 1;    // 卡池id
  uint32 num = 2;        // 召唤次数
  uint32 wish_hero = 3;  // 心愿英雄
}

message L2C_LinkHeroSummonTest {
  uint32 ret = 1;
  map<uint32, uint32> pools = 2;
  map<uint32, uint32> heroes = 3;
  map<uint32, uint32> redHeroes = 4;
  map<uint32, uint32> up_continued_count = 5;
  map<uint32, uint32> not_up_continued_count = 6;
}

// 神器首发活动落地数据
message ArtifactDebut {
  ArtifactDebutBase base = 1;                // 活动基础信息
  repeated ArtifactDebutSummon summons = 2;  // 抽卡信息（初级和高级抽）
  ArtifactDebutSummonAct summon_act = 3;     // 累抽活动
  ArtifactDebutLoginAct login_act = 4;       // 累登活动
  ArtifactDebutTask task = 5;                // 成就任务
  ArtifactDebutPuzzleAct puzzle_act = 6;     // 拼图活动
  ArtifactDebutTask daily_task = 7;          // 每日任务
  ArtifactDebutGuarantee guarantee = 8;      // 抽卡保底
}

// 神器首发活动 - 基础信息
message ArtifactDebutBase {
  uint32 uniq_id = 1;        // 神器首发活动唯一id，新服活动时，用artifact_debut_time_info中的id。通服活动时，用gm发过来的id
  uint32 activity_type = 2;  // 活动类型common.ARTIFACT_DEBUT_TYPE
  uint32 activity_id = 3;    // 活动量表id
  int64 start_time = 4;      // 开始时间
  int64 end_time = 5;        // 结束时间
  bool is_off = 6;           // 是否已设置下架
}

// 神器首发活动 - 抽卡信息
message ArtifactDebutSummon {
  uint64 finish_groups = 1;  // 已抽尽的卡组，按位存储
  uint32 current_group = 2;  // 当前抽卡所用卡组
  uint64 current_ids = 3;    // 当前抽卡所用卡组中，已抽到的id列表，按位存储
  uint32 wish_aid = 4;       // 心愿神器id
  uint32 category = 5;       // 抽卡分类 common.ARTIFACT_DEBUT_SUMMON
}

// 神器首发活动 - 累抽活动
message ArtifactDebutSummonAct {
  uint64 received_ids = 1;  // 已领奖的活动id列表，按位存储
  uint32 count = 2;         // 累计抽卡次数
}

// 神器首发活动 - 累登活动
message ArtifactDebutLoginAct {
  uint64 received_ids = 1;  // 已领奖的活动id列表，按位存储
  uint32 count = 2;         // 累计登录次数
}

// 神器首发活动 - 任务
message ArtifactDebutTask {
  map<uint32, TaskTypeProgress> progress = 1;  // 任务进度
  map<uint32, bool> received = 2;              // 已领取奖励的任务
}

// 神器首发活动 - 拼图活动
message ArtifactDebutPuzzleAct {
  uint64 received_ids = 1;  // 已领奖的活动id列表，按位存储
  uint64 opened_ids = 2;    // 已掀开拼图的序号列表，按位存储
}

// 神器首发活动 - 抽卡保底
message ArtifactDebutGuarantee {
  uint32 regular_count = 1;  // 常规保底 - 抽卡计数
  uint32 round_count = 2;    // 轮次保底 - 抽卡计数
  uint32 round_group = 3;    // 轮次保底 - 当前所处组id
}

// 神器首发活动 - 获取玩法数据
message C2L_ArtifactDebutMainInfo {}
message L2C_ArtifactDebutMainInfo {
  uint32 ret = 1;
  ArtifactDebut data = 2;
  uint32 guarantee_left_count = 3;  // 剩余保底次数
}

// 神器首发活动 - 设置心愿神器
message C2L_ArtifactDebutSetWish {
  uint32 wish_aid = 1;  // 所选神器id
}
message L2C_ArtifactDebutSetWish {
  uint32 ret = 1;
  uint32 wish_aid = 2;  // 所选神器id
  uint32 uniq_id = 3;   // 神器首发活动唯一id
}

// 神器首发活动 - 抽卡
message C2L_ArtifactDebutSummon {
  uint32 category = 1;   // 类别  common.ARTIFACT_DEBUT_SUMMON
  bool is_multiple = 2;  // 是否是多抽（5次）
}

message L2C_ArtifactDebutSummon {
  uint32 ret = 1;
  uint32 category = 2;                 // 类别 common.ARTIFACT_DEBUT_SUMMON
  bool is_multiple = 3;                // 是否是多抽（5次）
  uint32 count = 4;                    // 高级抽累计抽卡次数
  repeated Resource raw_award = 5;     // 抽卡原始奖励
  repeated Resource award = 6;         // 抽卡最终奖励（经过合并和分解）
  repeated Resource points_award = 7;  // 积分兑换奖励
  uint32 uniq_id = 8;                  // 神器首发活动唯一id
  uint32 guarantee_left_count = 9;     // 剩余保底次数
}

// 神器首发活动 - 领取活动奖励
message C2L_ArtifactDebutRecvActAward {
  uint32 type = 1;          // 类型 common.ARTIFACT_DEBUT_ACT_RECV
  repeated uint32 ids = 2;  // 奖励的序号列表
}
message L2C_ArtifactDebutRecvActAward {
  uint32 ret = 1;
  uint32 type = 2;              // 类型 common.ARTIFACT_DEBUT_ACT_RECV
  repeated uint32 ids = 3;      // 奖励的序号列表
  repeated Resource award = 4;  // 奖励
  uint64 received_ids = 5;      // 已领奖的活动id列表，按位存储
  uint32 uniq_id = 6;           // 神器首发活动唯一id
}

// 神器首发活动 - 领取任务奖励
message C2L_ArtifactDebutRecvTaskAward {
  repeated uint32 ids = 1;  // 任务id列表
}
message L2C_ArtifactDebutRecvTaskAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;       // 任务id列表
  repeated Resource awards = 3;  // 任务奖励
  uint32 uniq_id = 4;            // 神器首发活动唯一id
}

// 神器首发活动 - 更新任务进度
message L2C_ArtifactDebutUpdateTask {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
  uint32 uniq_id = 3;  // 神器首发活动唯一id
}

// 神器首发活动 - 拼图游戏掀格子
message C2L_ArtifactDebutOpenPuzzle {
  repeated uint32 ids = 1;  // 拼图格子序号列表，位置与序号的对应关系由前后端约定
}
message L2C_ArtifactDebutOpenPuzzle {
  uint32 ret = 1;
  repeated uint32 ids = 2;  // 拼图格子序号列表，位置与序号的对应关系由前后端约定
  uint64 opened_ids = 3;    // 全部已掀开的格子id列表，按位存储
  uint32 uniq_id = 4;       // 神器首发活动唯一id
}

// 神器首发活动 - 活动配置更新
message L2C_ArtifactDebutUpdateActivity {
  ArtifactDebutBase data = 1;  // 更新的活动配置数据
}

// 神器首发活动 - 获取活动配置
message C2L_ArtifactDebutGetActivity {}
message L2C_ArtifactDebutGetActivity {
  uint32 ret = 1;
  repeated ArtifactDebutBase datas = 2;  // 当前及后续活动配置数据
}

// 神器首发活动 - 抽卡测试
message C2L_ArtifactDebutTestSummon {
  uint32 count = 1;  // 抽卡次数
}
message L2C_ArtifactDebutTestSummon {
  uint32 ret = 1;
  repeated cl.Resource drop_awards = 2;     // 掉落奖励
  map<uint32, uint32> fragment_record = 3;  // 红色神器碎片掉落记录
}

message C2L_OSSUrl {
  string reportID = 1;
}

message L2C_OSSUrl {
  uint32 ret = 1;
  string url = 2;
  string reportID = 3;
}

// 认知日志
message CognitionLog {
  uint64 sid = 1;
  uint64 uid = 2;
  uint32 op_group = 3;
  uint32 formation_id = 4;
  uint32 target_id = 5;
  string name = 6;
  uint32 base_id = 7;                                      // 头像相关
  int64 power = 8;                                         // 通关阵容的战力
  Formation formation = 9;                                 // 阵容信息
  repeated HeroBody heroes = 10;                           // 阵容英雄信息
  repeated Artifact artifacts = 11;                        // 阵容神器信息
  string report_id = 12;                                   // 战报ID
  uint32 round = 13;                                       // 轮次或者期数
  uint32 arena = 14;                                       // 战区ID
  uint64 damage = 15;                                      // 造成的伤害
  repeated Skin skins = 16;                                // 皮肤
  repeated CognitionLogLinkInfo attack_link_info = 17;     // 羁绊链接信息 多队按照数组下标
  uint32 season_id = 18;                                   // 赛季ID
  repeated Rite rites = 19;                                // 上阵的仪式数据
  repeated EmblemInfo emblems = 20;                        // 上阵的符文
  repeated SeasonAddInfo season_add = 21;                  // 赛季加成数据
  repeated Remain remains = 22;                            // 上阵的遗物
  repeated CognitionHeroPassiveSkill passive_skills = 23;  // 英雄的被动技能
  TalentTreeCultivate talent_tree = 24;                    // 天赋树数据
  repeated Pokemon pokemons = 25;                          // 宠物数据
}

message CognitionLogLinkInfo {
  repeated bt.LinkInfo attack_link_info = 1;
  repeated bt.LinkInfo attack_artifact_link_info = 2;
}

// 记录英雄的被动技能情况
message CognitionHeroPassiveSkill {
  uint32 sys_id = 1;               // 英雄系统ID
  uint32 emblem_exclusive_lv = 2;  // 符文专属技能等级
}

message C2L_GetCognitionLog {
  uint32 formation_id = 1;  // 阵容ID
  uint32 target_id = 2;     // 目标关卡ID
  uint32 page = 3;          // 请求序号
}

message L2C_GetCognitionLog {
  uint32 ret = 1;
  uint32 formation_id = 2;            // 阵容ID
  uint32 target_id = 3;               // 目标关卡ID
  repeated CognitionLog logs = 4;     // 日志
  repeated uint64 guild_members = 5;  // 公会成员IDs
  repeated uint64 friend_ids = 6;     // 好友IDs
  uint32 page = 7;                    // 请求序号
  int32 first_pass = 8;               // 本服首通（在logs中的index，-1代表没有）
  int32 less_power = 9;               // 最低战力（在logs中的index，-1代表没有）
  uint32 total = 10;                  // 日志总数
}

message C2L_GetAchieveShowcase {}

message L2C_GetAchieveShowcase {
  uint32 ret = 1;
  AchievementsShowcase achieves = 2;  // 成就展示
}

// 获取是否已领取h5保存到桌面的奖励
message C2L_HasRecvH5DesktopReward {}
message L2C_HasRecvH5DesktopReward {
  uint32 ret = 1;
  bool received = 2;  // 是否已领取
}

// 领取h5保存到桌面的奖励
message C2L_RecvH5DesktopReward {}
message L2C_RecvH5DesktopReward {
  uint32 ret = 1;
}

// 新服轮次活动
message RoundActivity {
  uint32 stage = 1;                                 // 活动期数
  uint32 category = 2;                              // 活动类别 common.ROUND_ACTIVITY_TYPE
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
  map<uint32, bool> task_received = 4;              // 已领取奖励的任务
}

// 新服轮次活动 - 获取活动数据
message C2L_RoundActivityGetData {}
message L2C_RoundActivityGetData {
  uint32 ret = 1;
  repeated RoundActivity datas = 2;  // 活动数据
}

// 新服轮次活动 - 领取任务奖励
message C2L_RoundActivityRecvTaskAward {
  repeated uint32 ids = 1;  // 任务id列表
}
message L2C_RoundActivityRecvTaskAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;       // 任务id列表
  repeated Resource awards = 3;  // 任务奖励
}

// 新服轮次活动 - 更新任务进度
message L2C_RoundActivityUpdateTask {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

// 官网充值奖励推送
message L2C_WebRechargeNotify {
  uint32 ret = 1;
  uint32 id = 2;                       // 商品id（对应activity_web_gift_info表的ID）
  uint32 recharge_type = 3;            // 充值类型（对应common.proto的recharge_type）
  repeated Resource awards = 4;        // 充值获得的奖励
  repeated Resource extra_awards = 5;  // 充值获得的额外奖励（客户端特殊显示用）
}

message TowerSeason {
  uint32 floor = 1;                                 // 层数
  uint32 season = 2;                                // 赛季
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
  map<uint32, bool> task_received = 4;              // 已领取奖励的任务
  int64 last_reset_time = 5;                        // 上一次的重置时间
  uint32 month_like_count = 6;                      // 荣耀殿堂本月点赞次数
  bool sync_rank = 7;                               // 是否同步过排行榜
  int64 floor_tm = 8;                               // 达到该层的时间
  uint32 round = 9;                                 // 轮次
  uint32 top_quick_battle_id = 10;                  // 最高快速战斗的量表ID
}

message C2L_TowerSeasonGetData {}

message L2C_TowerSeasonGetData {
  uint32 ret = 1;
  TowerSeason tower_season = 2;
  string tower_link = 3;
  string tower_community = 4;
  bool last_season_have_rank = 5;
  uint32 top_floor = 6;  // 最高层数
}

message C2L_TowerSeasonFight {
  uint32 floor = 1;   // 层数
  uint32 season = 2;  // 赛季
  bytes client_data = 3;
  uint32 round = 4;  // 轮次
  bool quick = 5;    // 快速战斗
}

message L2C_TowerSeasonFight {
  uint32 ret = 1;
  uint32 floor = 2;              // 层数
  uint32 season = 3;             // 赛季
  string report_id = 4;          // 战报id
  bool win = 5;                  // 是否胜利
  repeated Resource awards = 6;  // 通过奖励
  uint32 round = 7;              // 轮次
  bool quick = 8;                // 快速战斗
  uint32 top_floor = 9;          // 最高层数
  uint32 defeat_team = 10;       // 战败队伍序号
}

message C2L_TowerSeasonRecvAward {
  repeated uint32 task_ids = 1;
}

message L2C_TowerSeasonRecvAward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

// 百塔 - 更新任务进度
message L2C_TowerSeasonUpdateTask {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

message C2L_TowerSeasonRankList {
  uint32 type = 1;  // 1:本赛季 2:上赛季 3: 上赛季top3
}

message L2C_TowerSeasonRankList {
  uint32 ret = 1;
  uint32 type = 2;
  repeated RankValue list = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
  uint32 total = 6;
}

message C2L_TowerSeasonRankLike {
  uint32 type = 1;  // 1:本赛季 3: 上赛季top3
  uint64 id = 2;
}

message L2C_TowerSeasonRankLike {
  uint32 ret = 1;
  uint32 type = 2;
  uint64 id = 3;
  repeated Resource awards = 4;
  uint32 like_num = 5;
  uint32 month_like_count = 6;  // 荣耀殿堂本月点赞次数
}

message C2L_TowerSeasonCognitionLogs {
  uint32 page = 1;  // 请求序号
}

message L2C_TowerSeasonCognitionLogs {
  uint32 ret = 1;
  uint32 page = 2;
  repeated CognitionLog logs = 3;
  uint32 total = 4;
}

message ActivitySummonGuarantee {
  uint32 activity_id = 1;               // 活动ID
  uint32 activity_summon_count = 2;     // 活动ID的抽卡次数
  uint32 activity_guarantee_count = 3;  // 活动ID的第几次保底
  uint32 is_new_activity = 4;           // 是否是新服活动
}

message GodPresent {
  uint32 id = 1;                                  // 卡池ID
  repeated uint32 summon_group = 2;               // 抽卡组顺序
  uint32 sort_id = 3;                             // sort表ID
  repeated Resource stash_awards = 4;             // 上一抽资源
  repeated Resource rewards_to_be_collected = 5;  // 确认区奖励
  bool collected = 6;                             // 是否已领取
  uint32 summon_count = 7;                        // 抽卡次数
}

message GodPresentClient {
  uint32 id = 1;                                  // 卡池ID
  repeated Resource stash_awards = 2;             // 上一抽资源
  repeated Resource rewards_to_be_collected = 3;  // 确认区奖励
  bool collected = 4;                             // 是否已领取
  uint32 summon_count = 5;                        // 抽卡次数
}

message GodPresents {
  map<uint32, uint32> top_hero_count = 1;
  repeated GodPresent god_presents = 2;
}

message GodPresentsNew {
  repeated GodPresentNew datas = 1;
  map<uint32, uint32> hero_count = 2;        // 抽中特定品质英雄的数量 hero_sys_id => 抽中个数
  bool sent_notice_mail = 3;                 // 是否已发通知邮件   -- v2.3更新后废弃
  int64 login_time = 4;                      // 玩家登录的时间，第二期更新后开始有值
  bool is_send_second_mail = 5;              // 第二期补发奖励的邮件是否发放 -- v2.3更新后废弃
  map<uint32, bool> is_send_mail = 6;        // 是否发送通知邮件 key: 期数
  map<uint32, uint32> class_change_cnt = 7;  // 卡池替换次数， key: 期数  value：次数
}

message GodPresentNew {
  uint32 id = 1;                          // 子活动ID
  repeated uint32 groups = 2;             // 卡组id列表，按顺序抽卡
  repeated GodPresentSummon id_list = 3;  // 多组抽到的英雄量表id列表
  uint32 select_group_key = 4;            // 最终选中的第x组GodPresentSummon（从1开始）
  map<uint32, uint32> fragments = 5;      // 领奖时，得到的英雄碎片数据 id => count
}

// 抽到的英雄量表id列表
message GodPresentSummon {
  repeated uint32 ids = 1;
}

// 777抽活动 - 获取数据
message C2L_GodPresentGetData {}
message L2C_GodPresentGetData {
  uint32 ret = 1;
  repeated GodPresentNew god_presents = 2;
  int64 login_time = 3;   // 2.7.0开始不用这个字段
  uint32 create_day = 4;  // 后端计算出的创角天数,暂时没有用处，只是方便调试
}

// 777抽活动 - 领取抽卡券
message C2L_GodPresentRecvItem {
  uint32 id = 1;  // 子活动ID
}
message L2C_GodPresentRecvItem {
  uint32 ret = 1;
  uint32 id = 2;  // 子活动ID
}

// 777抽活动 - 抽卡
message C2L_GodPresentSummon {
  uint32 id = 1;  // 子活动ID
}
message L2C_GodPresentSummon {
  uint32 ret = 1;
  uint32 id = 2;                          // 子活动ID
  repeated GodPresentSummon id_list = 3;  // 多组抽到的英雄量表id列表
}

// 777抽活动 - 选定卡组，领取抽卡活动奖励
message C2L_GodPresentRecvAwards {
  uint32 id = 1;                // 子活动ID
  uint32 select_group_key = 2;  // 最终选中的第x组GodPresentSummon（从1开始）
}
message L2C_GodPresentRecvAwards {
  uint32 ret = 1;
  uint32 id = 2;                      // 子活动ID
  uint32 select_group_key = 3;        // 最终选中的第x组GodPresentSummon（从1开始）
  map<uint32, uint32> fragments = 4;  // 领奖时，得到的英雄碎片数据 id => count
}

// 掉落活动 - 配置数据
message DropActivityBase {
  uint32 id = 1;                                // 活动唯一id，由gm生成
  uint32 activity_id = 2;                       // drop_activity_info量表id
  int64 start_time = 3;                         // 开始时间
  int64 end_time = 4;                           // 结束时间
  uint32 op_status = 5;                         // 发布状态 1-发布 2-下架
  uint32 protect_day = 6;                       // 新服保护期
  repeated DropActivityExchangeRule rules = 7;  // 兑换规则
  bool new_server = 8;                          // 是否为新服
}

// 掉落活动 - 兑换规则
message DropActivityExchangeRule {
  uint32 tag = 1;                   // 显示页签 1-第一个页签 2-第二个页签
  uint32 id = 2;                    // 兑换规则id，需唯一且有序
  uint32 max = 3;                   // 最大兑换次数
  repeated Resource target = 4;     // 目标奖励
  repeated Resource condition = 5;  // 需消耗的材料
  uint32 recommend_type = 6;        // 推荐类型 0-无
  int64 open_buy_time = 7;          // 开启购买时间
  uint32 display_type = 8;          // 展示类型 只有tag为2会生效 2-热卖 1-折扣 0-默认
  uint32 display_sort = 9;          // 展示排序 只有tag为2会生效 各个展示类型的ID 从1开始
}

// 掉落活动 - 玩家兑换情况
message DropActivityExchange {
  uint32 id = 1;     // 规则id
  uint32 count = 2;  // 已兑换次数
}

// 掉落活动 - 玩家落地数据
message DropActivity {
  uint32 id = 1;                                    // 活动唯一id --- DropActivityBase.id
  repeated DropActivityExchange exchange_list = 2;  // 兑换详情数据
  bool new_server = 3;                              // 新服标记
}

// 掉落活动 - 获取玩家数据
message C2L_DropActivityMainInfo {}
message L2C_DropActivityMainInfo {
  uint32 ret = 1;
  DropActivity data = 2;
}

// 掉落活动 - 兑换
message C2L_DropActivityExchange {
  uint32 id = 1;       // 活动唯一id
  uint32 rule_id = 2;  // 兑换规则id
  uint32 count = 3;    // 兑换次数
}
message L2C_DropActivityExchange {
  uint32 ret = 1;
  uint32 id = 2;                 // 参数回传 - 活动唯一id
  uint32 rule_id = 3;            // 参数回传 - 兑换规则id
  uint32 count = 4;              // 参数回传 - 兑换次数
  repeated Resource awards = 5;  // 兑换得到的奖励
}

// 掉落活动 - 获取活动配置
message C2L_DropActivityGetActivity {}
message L2C_DropActivityGetActivity {
  uint32 ret = 1;
  repeated DropActivityBase datas = 2;  // 当前及后续活动配置数据
}

// 掉落活动 - 活动配置更新
message L2C_DropActivityUpdateActivity {
  DropActivityBase data = 1;  // 更新的活动配置数据
}

message C2L_DropActivityDailyReward {
  uint32 id = 1;
}

message L2C_DropActivityDailyReward {
  uint32 ret = 1;
  uint32 id = 2;
  repeated Resource awards = 3;
}

message C2L_Test {
  uint64 p1 = 1;
  uint64 p2 = 2;
  uint64 p3 = 3;
  uint64 p4 = 4;
  uint64 p5 = 5;
}

message L2C_Test {
  uint32 ret = 1;
}

// 累登
message DailyAttendance {
  uint32 login_count = 1;                      // 累登次数
  map<uint32, DailyAttendanceData> datas = 2;  // key: 轮次
}

message DailyAttendanceData {
  uint32 round = 1;                 // 轮次
  int64 first_login_time = 2;       // 本轮第一次登录时间
  int64 last_login_time = 3;        // 本轮最后一次登录时间
  uint64 first_award_receive = 4;   // 第一次奖励是否领取；按位存储
  uint64 is_second_award = 5;       // 是否存在第二次奖励；按位存储
  uint64 second_award_receive = 6;  // 第二次奖励是否领取；按位存储
  bool extra_award_receive = 7;     // 额外大奖是否领取
  bool mail_reward = 8;             // 是否已补发奖励
}

message C2L_DailyAttendanceGetData {}

message L2C_DailyAttendanceGetData {
  uint32 ret = 1;
  DailyAttendance data = 2;
}

message C2L_DailyAttendanceRecvAward {
  uint32 round = 1;  // 轮次
  repeated DailyAttendanceRecvParam params = 2;
  bool extra_award_receive = 3;  // 领取额外大奖
}

message DailyAttendanceRecvParam {
  uint32 day = 2;         // 第几天
  uint32 award_type = 3;  // 第几次领奖：1：第一次领奖  2：第二次领奖  3: 2次都领取
}

message L2C_DailyAttendanceRecvAward {
  uint32 ret = 1;
  uint32 round = 2;  // 轮次
  repeated DailyAttendanceRecvParam params = 3;
  bool extra_award_receive = 4;  // 领取额外大奖
  repeated Resource awards = 5;
  DailyAttendanceData data = 6;
}

message L2C_DailyAttendanceUpdate {
  DailyAttendanceData data = 1;
}

message DailySpecial {
  repeated uint32 receive_slots = 1;
}

message C2L_DailySpecialGetData {}

message L2C_DailySpecialGetData {
  uint32 ret = 1;
  DailySpecial daily_special = 2;
}

message C2L_DailySpecialRecvAward {
  uint32 type = 1;            // 领奖类型： 1：每日领奖   2：积分领奖
  repeated uint32 slots = 2;  // 积分领奖的槽位：0, 1，2
}

message L2C_DailySpecialRecvAward {
  uint32 ret = 1;
  uint32 type = 2;
  repeated Resource awards = 3;
  DailySpecial daily_special = 4;
  repeated uint32 slots = 5;  // 积分领奖的槽位：0, 1，2
}

message GuildChest {
  uint32 chest_id = 1;            // 表格ID
  uint64 uid = 2;                 // 用户ID
  uint64 id = 3;                  // 生成的唯一ID
  int64 expire_time = 4;          // 过期时间
  Resource surplus_resource = 5;  // 剩余资源
  uint32 recv_like = 6;           // 已收到的花
  repeated GuildChestSlotDetail recv_users = 7;
  string name = 8;                   //  创建者名字
  uint32 level = 9;                  //  创建者等级
  uint32 avatar_id = 10;             //  创建者头像
  uint32 create_flower = 11;         //  创建时创建者的花朵
  uint32 recv_chest_max_limit = 12;  //  该宝箱领取人数上限
  uint32 task_id = 13;               // 宝箱来源任务ID
}

message GuildChestSlotDetail {
  uint64 uid = 1;         //  领奖人ID
  Resource resource = 2;  //  已领取的资源
  uint32 like_level = 3;  //  点赞类型 0:未点赞 1：点赞 2：高级点赞
  int64 recv_time = 4;    //  领奖时间
  string name = 5;        //  领取者名字
  uint32 level = 6;       //  领取者等级
  uint32 avatar_id = 7;   //  领取者头像
}

message UserGuildChestItem {
  uint64 id = 1;          // 生成的唯一id
  uint32 chest_id = 2;    // 表格ID
  int64 expire_time = 3;  // 过期时间
  uint32 task_id = 4;     // 生成宝箱的任务id
}

message GuildChestFinishRecv {
  uint32 chest_id = 1;             // 表格ID
  uint32 recv_like = 2;            // 收到的点赞数量
  int64 create_time = 3;           // 生成时间
  repeated string recv_names = 4;  // 公会接收名字
  uint32 recv_count = 5;           // 该宝箱接收总人数
}

message C2L_GuildChestGetData {}

message L2C_GuildChestGetData {
  uint32 ret = 1;
  repeated GuildChestData guild_chests = 2;  // 公会分享
  repeated UserGuildChestItem items = 3;     // 未激活的物品
  uint32 user_chest_weekly_recv_count = 4;   // 玩家每周可领剩余次数
  uint32 recv_total_chest_like_token = 5;    // 收到花束的总数
}

message GuildChestData {
  uint64 id = 1;                      // 唯一ID
  int64 expire_time = 2;              // 过期时间
  uint32 like_type = 3;               // 点赞类型
  uint32 like_count = 4;              // 点赞数量
  Resource resource = 5;              // 已领取资源
  string name = 6;                    // 创建者名字
  uint32 level = 7;                   // 创建者等级
  uint32 avatar_id = 8;               // 创建者头像
  uint32 chest_id = 9;                // 表格ID
  uint64 uid = 10;                    // 用户ID
  uint32 this_chest_recv_count = 11;  // 宝箱已领取次数
  uint32 create_flower = 12;          // 创建时创建者的花朵
  uint32 recv_chest_max_limit = 13;   // 该宝箱领取人数上限
  uint32 task_id = 14;                // 宝箱来源任务ID
}

message C2L_GuildChestRecv {
  uint64 id = 1;
}

message L2C_GuildChestRecv {
  uint32 ret = 1;
  uint64 id = 2;
  GuildChest guild_chest = 3;
  repeated Resource awards = 4;
  uint32 user_chest_weekly_recv_count = 5;  // 玩家每周可领剩余次数
  uint32 this_chest_recv_count = 6;         // 宝箱已领取次数
}

message C2L_GuildChestLike {
  uint64 id = 1;
  uint32 like_type = 2;
}

message L2C_GuildChestLike {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 like_type = 3;
  GuildChest guild_chest = 4;
}

message C2L_GuildChestActivate {
  uint64 item_id = 1;
}

message L2C_GuildChestActivate {
  uint32 ret = 1;
  uint64 item_id = 2;
  GuildChest guild_chest = 3;
}

message L2C_UserGuildChestItemNotify {
  uint32 ret = 1;
  repeated UserGuildChestItem items = 2;  // 新增物品
  repeated uint64 expire_items = 3;       // 过期
  GuildChestData new_guild_chest = 4;     // 新增宝箱
}

message ActivityMix {
  uint32 id = 1;
  string creator = 2;
  string title = 3;
  uint32 time_type = 4;
  int64 start_time = 5;
  int64 end_time = 6;
  uint32 status = 7;
  uint32 weight = 8;
  string op_ids = 9;
  string en = 10;
  string zh = 11;
  string de = 12;
  string fr = 13;
  string th = 14;
  string it = 15;
  string es = 16;
  string pt = 17;
  string ru = 18;
  string link_url = 19;
  string inline_url = 20;
}

message C2L_ActivityMixGetData {}

message L2C_ActivityMixGetData {
  uint32 ret = 1;
  repeated ActivityMix activity_mixs = 2;
}

message LinkSetting {
  string tower_link = 1;
  string tower_community = 2;
}

message WorldBoss {
  WorldBossData data = 1;
  WorldBossTask task = 2;
}

message WorldBossData {
  uint32 unique_id = 1;    // 活动唯一id
  uint32 sys_id = 2;       // 活动量表id
  uint32 level = 3;        // 难度等级
  WorldBossRoom room = 4;  // 房间
  map<uint32, uint32> boss_type_level = 5;
}

message WorldBossRoom {
  uint32 room_id = 1;                   // 房间唯一id
  uint32 room_sys_id = 2;               // 房间系统id
  uint64 max_hurt = 3;                  // 最高伤害
  uint64 accumulative_hurt = 4;         // 累积伤害
  int64 recovery_time_fight_count = 5;  // 挑战次数的恢复起始时间
  uint32 fight_count = 6;               // 挑战次数
  uint64 max_hurt_score = 7;            // 最高伤害积分
  uint64 accumulative_hurt_score = 8;   // 累积伤害积分
}

message WorldBossTask {
  map<uint32, TaskTypeProgress> task_progress = 1;  // 任务进度
  map<uint32, bool> receive_awarded = 2;            // 已领取奖励的任务
}

message WorldBossRoomLog {
  uint64 id = 1;
  uint64 uid = 2;
  uint32 log_type = 3;         // 日志类型
  int64 create_time = 4;       // 创建时间
  repeated string params = 5;  // 不同的类型，参数值不一样
}

// 世界boss - 获取数据
message C2L_WorldBossGetData {}
message L2C_WorldBossGetData {
  uint32 ret = 1;
  WorldBoss world_boss = 2;
}

// 世界boss - 选择难度等级
message C2L_WorldBossSelectLevel {
  uint32 level = 1;
}
message L2C_WorldBossSelectLevel {
  uint32 ret = 1;
  uint32 level = 2;
}

message C2L_WorldBossRoomInfo {}

message L2C_WorldBossRoomInfo {
  uint32 ret = 1;
  WorldBossData data = 2;
  repeated RankValue list = 3;   // 前一名、玩家自己、后一名
  repeated RankValue users = 4;  // 房间内的玩家：给5个
  RankValue room_top = 5;        // 房间第1
  repeated WorldBossRoomLog logs = 6;
  uint32 partition_rank = 7;  // 战区排行
}

// 世界boss - 战斗
message C2L_WorldBossFight {
  bytes client_data = 1;
  uint32 type = 2;  // 1： 挑战  2： 扫荡
}

message L2C_WorldBossFight {
  uint32 ret = 1;
  uint32 type = 2;
  string report_id = 3;  // 战报i
  repeated Resource awards = 4;
  uint64 hurt = 5;  // 当前单次伤害
  uint64 accumulative_hurt = 6;
  uint32 fight_count = 7;
  int64 recovery_time_fight_count = 8;  // 挑战次数的恢复时间
  bool win = 9;
  uint64 max_hurt = 10;        // 最高单次伤害
  uint64 hurt_score = 11;      // 当前伤害积分
  uint64 max_hurt_score = 12;  // 最大伤害积分
  uint64 accumulative_hurt_score = 13;
}

// 世界boss - 领奖
message C2L_WorldBossRecvAward {
  repeated uint32 task_ids = 1;
}
message L2C_WorldBossRecvAward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

// 世界boss - 膜拜
message C2L_WorldBossWorship {}
message L2C_WorldBossWorship {
  uint32 ret = 1;
  repeated Resource awards = 2;
}

// 世界boss - 排行榜
message C2L_WorldBossRank {
  uint32 type = 1;        // 1：战区榜 2：房间榜 3：讨伐之星 4: 玩家自己
  uint32 level = 2;       // 战区榜有值：难度枚举
  uint32 start_rank = 3;  // 战区榜有值；【start_rank，end_rank】这个区间的所有值
  uint32 end_rank = 4;
}

message L2C_WorldBossRank {
  uint32 ret = 1;
  uint32 type = 2;
  uint32 level = 3;
  repeated RankValue list = 4;
  uint32 self_rank = 5;
  RankValue self_value = 6;
  uint32 start_rank = 7;
  uint32 end_rank = 8;
}

// 世界boss - 获取房间日志
message C2L_WorldBossGetRoomLog {
  uint32 num = 2;
}

message L2C_WorldBossGetRoomLog {
  uint32 ret = 1;
  uint32 num = 2;
  repeated WorldBossRoomLog logs = 3;
  repeated RankValue list = 4;  // 前一名、玩家自己、后一名
  RankValue room_top = 5;       // 房间第1
  uint32 partition_rank = 6;    // 战区排行
}

// 世界boss - 任务进度更新
message L2C_WorldBossTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_progress = 2;
}

// 掉落活动 - 玩家兑换情况
message ActivityStoryExchange {
  uint32 id = 1;     // 表ID
  uint32 count = 2;  // 已兑换次数
}

message ActivityStory {
  uint32 sys_id = 1;                                 // 活动系统ID
  uint32 continue_login_day = 2;                     // 持续登录时间
  map<uint32, bool> login_award = 3;                 // 登录奖励领取状态
  uint32 dungeon_id = 4;                             // 主线ID
  repeated ActivityStoryExchange exchange_list = 5;  // 兑换详情数据
  int64 ticket_refresh_time = 6;                     // 玩家门票刷新时间
}

message C2L_ActivityStoryGetData {}

message L2C_ActivityStoryGetData {
  uint32 ret = 1;
  ActivityStory data = 2;  // 活动信息
}

message C2L_ActivityStoryLoginAward {
  uint32 act_id = 1;
  repeated uint32 login_ids = 2;  // 登录任务ID
}

message L2C_ActivityStoryLoginAward {
  uint32 ret = 1;
  uint32 act_id = 2;
  repeated uint32 login_ids = 3;  // 登录任务ID
  repeated Resource awards = 4;   // 兑换得到的奖励
}

message C2L_ActivityStoryExchange {
  uint32 id = 1;      // 兑换表ID
  uint32 count = 2;   // 兑换次数
  uint32 act_id = 3;  // 活动ID
}

message L2C_ActivityStoryExchange {
  uint32 ret = 1;
  uint32 id = 2;                 // 参数回传 - 表ID
  uint32 count = 3;              // 参数回传 - 兑换次数
  repeated Resource awards = 4;  // 兑换得到的奖励
  uint32 act_id = 5;             // 活动ID
}

message C2L_ActivityStoryFight {
  uint32 dungeon_id = 1;
  bytes client_data = 2;
  uint32 type = 3;         // 1： 挑战  2： 扫荡
  uint32 act_id = 4;       // 活动ID
  uint32 sweep_count = 5;  // 扫荡次数
}

message L2C_ActivityStoryFight {
  uint32 ret = 1;
  uint32 dungeon_id = 2;
  uint32 type = 3;
  string report_id = 4;  // 战报id
  repeated Resource awards = 5;
  uint32 act_id = 6;  // 活动ID
  bool win = 7;       // 是否胜利
  uint32 sweep_count = 8;
}

//$<ROP redis|map|skin:uint64 >
message Skin {
  uint32 id = 1;       //$<ROP unique > //皮肤id
  int64 end_time = 2;  // 有效期截止时间 0-永久皮肤
  uint32 hero_id = 3;  // 所属英雄量表id，冗余字段
  bool in_use = 4;     // 是否正在使用中
}

// 皮肤 - 皮肤列表
message C2L_SkinList {}
message L2C_SkinList {
  uint32 ret = 1;
  repeated Skin skins = 2;  // 皮肤列表
}

// 皮肤 - 使用皮肤
message C2L_SkinUse {
  uint32 skin_id = 1;  // 皮肤id
}
message L2C_SkinUse {
  uint32 ret = 1;
  uint32 skin_id = 2;  // 皮肤id
}

// 皮肤 - 推送最新皮肤信息
message L2C_SkinNew {
  uint32 ret = 1;
  Skin skin = 2;
}

message AssistanceActivity {
  uint32 act_id = 1;
  map<uint32, bool> recv_award = 2;
}

message C2L_AssistanceActivityGetData {}

message L2C_AssistanceActivityGetData {
  uint32 ret = 1;
  AssistanceActivity assistance_activity = 2;
  repeated string name = 3;
}

message C2L_AssistanceActivityGetAward {
  uint32 act_id = 1;
  repeated uint32 award_ids = 2;
}

message L2C_AssistanceActivityGetAward {
  uint32 ret = 1;
  uint32 act_id = 2;
  repeated uint32 award_ids = 3;
  AssistanceActivity assistance_activity = 4;
  repeated Resource awards = 5;  // 奖励
}

message Disorderland {
  map<uint32, DisorderlandMap> map = 2;  // 地图；key：难度
  //  map<uint32, uint32> global_probability = 3;  // 全局概率
  //  repeated uint32 relics = 4;              // 遗物 - 属性加成
  int64 stamina_recover_time = 5;           // 体力恢复时间
  bool is_first = 6;                        // 是否首次进入本功能，需要做一些数据初始化
  map<uint32, uint32> pass_max_level = 7;   // 关卡事件最大等级 key: 关卡类型
  uint32 sweep_count = 8;                   // 扫荡次数
  map<uint64, uint32> guarantee_count = 9;  // 保底计数 key: 关卡id+符石等级 value: 当前保底计数(达到一定数量触发保底并重置)
  bool guild_medal_add = 10;                // 公会勋章加成是否已生效
  reserved 3, 4;
  reserved "global_probability", "relics";
}

message DisorderlandMap {
  map<uint32, DisorderlandNode> nodes = 1;
  //  map<uint32, uint32> pass_max_level = 2;  // 关卡事件最大等级 key: 关卡类型
  uint32 complete_node_num = 3;  // 完成的节点数量

  reserved 2;
  reserved "pass_max_level";
}

message DisorderlandNode {
  uint32 node_id = 1;
  bool status = 2;
  //  uint32 group_index = 3;  // disorderland_drop_group_info.xml中的group_tag
  //  uint32 event_type = 4;  // 事件 type

  reserved 3, 4;
  reserved "group_index", "event_type";
}

// 失序空间 - 获取数据
message C2L_DisorderlandGetData {}
message L2C_DisorderlandGetData {
  uint32 ret = 1;
  Disorderland disorderland = 2;
  //  map<uint32, uint32> global_probability = 3;  // 全局概率
}

// 失序空间 - 触发事件
message C2L_DisorderlandTriggerEvent {
  uint32 difficulty = 1;
  uint32 node_id = 2;
  uint32 fight_type = 3;  // 关卡挑战类型；1：挑战 2：扫荡
  //  uint32 param = 4;       // 关卡事件选择的掉落组
  bytes client_data = 5;
}

message L2C_DisorderlandTriggerEvent {
  uint32 ret = 1;
  uint32 difficulty = 2;
  uint32 fight_type = 3;  // 关卡挑战类型；1：挑战 2：扫荡
  //  uint32 param = 4;                            // 关卡事件选择的掉落组
  //  uint32 relics = 5;  // 遗物
  //  uint32 stone = 6;   // 祝福石
  //  map<uint32, uint32> global_probability = 7;  // 全局概率
  DisorderlandNode node = 8;
  repeated Resource awards = 9;
  int64 stamina_recover_time = 10;  // 体力恢复时间
  string report_id = 11;            // 战报id
  bool win = 12;
  uint32 node_id = 13;
  map<uint32, uint32> pass_max_level = 14;  // 关卡事件最大等级 key: 关卡类型
}

// 失序空间 - 获取排行榜
message C2L_DisorderlandRank {
  uint32 hurdle_type = 1;  // 关卡类型
  uint32 rank_id = 2;
}
message L2C_DisorderlandRank {
  uint32 ret = 1;
  uint32 hurdle_type = 2;
  repeated RankValue list = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
  uint32 rank_id = 6;
}

// 失序空间 - 购买体力
message C2L_DisorderlandBuyStamina {
  uint32 buy_type = 1;  // 1: 钻石购买  2: 道具购买
  uint32 buy_count = 2;
}
message L2C_DisorderlandBuyStamina {
  uint32 ret = 1;
  uint32 buy_type = 2;
  uint32 buy_count = 3;
  int64 stamina_recover_time = 4;  // 体力恢复时间
}

// 失序空间 - 测试扫荡
message C2L_DisorderlandTestSweep {
  uint32 dungeon_id = 1;   // 副本id
  uint32 sweep_count = 2;  // 扫荡次数
}

message L2C_DisorderlandTestSweep {
  uint32 ret = 1;
  uint32 dungeon_id = 2;
  map<uint32, SweepTestData> data = 3;  // key:随机到的符石唯一等级
}

message SweepTestData {
  uint32 total_count = 1;      // 随机到的次数
  uint32 improve_count = 2;    // 提升总次数
  uint32 guarantee_count = 3;  // 保底触发提升次数
}

message SeasonLevel {
  repeated uint32 received_lv_awards = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
  map<uint32, bool> task_awarded = 3;
}

// 玩家赛季埋点信息
//$<ROP redis|map|season_user_log:uint64 >
message SeasonUserLog {
  uint32 season_id = 1;  //$<ROP unique>
  uint64 uid = 2;
  uint64 sid = 3;
  uint32 op_group = 4;
  repeated SeasonUserLogPoint points = 5;  // 埋点
}

// 赛季埋点
message SeasonUserLogPoint {
  uint32 id = 1;
  repeated string values = 2;
  map<string, string> data = 3;
}

// 赛季等级 - 获取数据
message C2L_SeasonLevelGetData {}
message L2C_SeasonLevelGetData {
  uint32 ret = 1;
  uint32 level = 2;
  SeasonLevel tasks = 3;
}

// 赛季等级 - 升级
message C2L_SeasonLevelUp {
  uint32 target_lv = 1;
}
message L2C_SeasonLevelUp {
  uint32 ret = 1;
  uint32 target_lv = 2;
  int64 season_power = 3;
}

// 赛季等级 - 领取任务奖励
message C2L_SeasonLevelRecvTaskAwards {
  repeated uint32 ids = 1;  // 任务id
}
message L2C_SeasonLevelRecvTaskAwards {
  uint32 ret = 1;
  repeated uint32 ids = 2;       // 任务id
  repeated Resource awards = 3;  // 奖励
}

// 赛季等级 - 领取等级奖励
message C2L_SeasonLevelRecvLvAwards {
  repeated uint32 levels = 1;  // 等级ID
}
message L2C_SeasonLevelRecvLvAwards {
  uint32 ret = 1;
  repeated uint32 levels = 2;    // 等级ID
  repeated Resource awards = 3;  // 奖励
}

message L2C_SeasonLevelTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

message C2L_SeasonEnter {
  uint32 season_id = 1;
}

message L2C_SeasonEnter {
  uint32 ret = 1;
  uint32 season_id = 2;
  bool season_enter = 3;  // 是否已点击赛季入口
}

message RiteGrid {
  uint32 pos = 1;                 // 位置编号（1-5）
  uint64 mark_guid = 2;           // 装备印记唯一ID
  uint32 mark_id = 3;             // 装备印记ID（关联量表rite_mark_info的ID字段）
  repeated uint32 power_ids = 5;  // 威能ID列表（关联量表rite_power_info的ID字段）
}

//$<ROP redis|map|rite:uint64>
message Rite {
  uint32 rite_id = 1;                 //$<ROP unique >  //仪式ID（关联量表rite_info的ID字段）
  uint32 rare = 2;                    // 仪式品质
  repeated RiteGrid grids = 5;        // 阵格列表
  repeated uint32 awarded_rares = 7;  // 已领奖的品质
}

message C2L_RiteGetData {}

message L2C_RiteGetData {
  uint32 ret = 1;
  repeated Rite rites = 2;  // 永恒仪式列表
}

message C2L_RiteRareUp {
  uint32 rite_id = 1;
}

message L2C_RiteRareUp {
  uint32 ret = 1;
  Rite rite = 2;  // 升级后的仪式
}

message C2L_RiteTakeRareAwards {
  uint32 rite_id = 1;
  repeated uint32 rares = 2;  // 品质列表
}

message L2C_RiteTakeRareAwards {
  uint32 ret = 1;
  Rite rite = 2;  // 更新后的仪式
  repeated Resource awards = 5;
}

message L2C_RiteChanged {
  uint32 ret = 1;
  Rite rite = 2;  // 永恒仪式
}

message SeasonDungeon {
  uint32 dungeon_id = 1;
  map<uint32, TaskTypeProgress> task_progress = 2;  // 任务进度
  map<uint32, bool> receive_awarded = 3;            // 已领取奖励的任务
}

message C2L_SeasonDungeonGetData {}

message L2C_SeasonDungeonGetData {
  uint32 ret = 1;
  SeasonDungeon data = 2;
}

message C2L_SeasonDungeonFight {
  uint32 dungeon_id = 1;
  bytes client_data = 2;
}

message L2C_SeasonDungeonFight {
  uint32 ret = 1;
  uint32 dungeon_id = 2;
  string report_id = 3;  // 战报id
  repeated Resource awards = 4;
  bool win = 5;  // 是否胜利
}

message C2L_SeasonDungeonRecvReward {
  repeated uint32 task_ids = 1;
}

message L2C_SeasonDungeonRecvReward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
  SeasonDungeon season_dungeon = 4;
}

message L2C_SeasonDungeonUpdateTask {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

// 回流7日登录
message ActivityReturnLogin {
  uint32 day_count = 1;      // 累计登录天数
  uint32 reward_status = 2;  // 奖励领取状态
}

// 回流数据
message ActivityReturn {
  uint32 return_id = 1;           // 回流ID
  uint32 loss_day_count = 2;      // 流失天数
  int64 start_time = 3;           // 活动开启时间
  ActivityReturnLogin login = 7;  // 回流7日登录
}

message C2L_ActivityReturnGetData {}

message L2C_ActivityReturnGetData {
  uint32 ret = 1;
  ActivityReturn data = 2;  // 回流数据
}

// 领取奖励
message C2L_ActivityReturnTakeLoginAwards {
  uint32 day_index = 1;  // 第几天
}

message L2C_ActivityReturnTakeLoginAwards {
  uint32 ret = 1;
  uint32 day_index = 2;           // 第几天
  ActivityReturnLogin login = 3;  // 更新后的7日登陆数据
  repeated Resource awards = 4;   // 奖励
}

// 巅峰竞技场 - 玩家数据
//$<ROP redis|map|peak_user>
message PeakUser {
  uint64 uid = 1;                        //$<ROP unique > 玩家uid
  repeated PeakUserGuess all_guess = 2;  // 全部竞猜数据
}

// 巅峰竞技场 - 状态控制
message PeakState {
  uint32 season = 1;        // 哪个赛季，从1开始
  uint32 season_state = 2;  // 赛季的状态 0-刚开始 1-进行中 2-已结束
  uint32 phase = 3;         // 哪个阶段
  uint32 phase_state = 4;   // 0-刚开始 1-数据初始化 2-进行中 3-已结束
  uint32 round = 5;         // 哪一轮
  uint32 round_state = 6;   // 0-刚开始 1-战斗 2-结束
  bool open = 7;            // 是否开启
}

// 巅峰竞技场 - 竞猜数据
message PeakUserGuess {
  uint32 match_id = 1;    // 比赛场次id
  uint32 group = 2;       // 所在组id
  uint64 winner_uid = 3;  // 下注对象uid
  uint32 count = 4;       // 竞猜次数
  uint32 round = 5;       // 所属轮次
}

// 巅峰竞技场 - 对战数据
message PeakMatch {
  uint32 id = 1;                       // 比赛场次id
  PeakMatchFighter left_fighter = 2;   // 左侧玩家
  PeakMatchFighter right_fighter = 3;  // 右侧玩家
  string report_id = 4;                // 战报id
  uint64 winner_uid = 5;               // 获胜者uid
  uint32 round = 6;                    // 第几轮，小组赛:1-3 决赛:4-6 冠军赛:7
  uint32 area = 7;                     // 战区，小组赛/决赛:1上半区 2下半区  冠军赛:0
  uint32 group = 8;                    // 第几组，小组赛:1-16 决赛/冠军赛:0
  uint32 group_sub = 9;                // 每轮比赛中的场次序号:1-4
  int64 guess_start_tm = 10;           // 竞猜开始时间 0-非竞猜场次
}

// 巅峰竞技场 - 对战选手数据
message PeakMatchFighter {
  uint64 uid = 1;
  uint64 sid = 2;
  uint32 season_score = 3;    // 赛季积分
  uint32 guess_count = 5;     // 竞猜下注次数
  uint32 match_score = 6;     // 当前对战比分
  UserSnapshot snapshot = 7;  // 玩家快照
  uint32 pos = 8;             // 玩法占位id（世界boss排名）
}

// 巅峰竞技场 - 比赛结果
message PeakResult {
  uint64 uid = 1;             // 玩家ID
  uint64 sid = 2;             // 服务器ID
  uint32 rank = 3;            // 排名
  uint32 score = 4;           // 赛季积分
  uint32 phase = 5;           // 期数
  UserSnapshot snapshot = 6;  // 玩家快照
  uint32 real_rank = 7;       // 实际排名
}

// 巅峰竞技场 - 赛季积分与排名
message PeakRankScore {
  uint64 uid = 1;    // 玩家ID
  uint32 rank = 2;   // 排名
  uint32 score = 3;  // 赛季积分
}

// 巅峰竞技场 - 基本信息
message C2L_PeakBaseData {}
message L2C_PeakBaseData {
  uint32 ret = 1;
  uint32 score = 2;                      // 赛季积分
  PeakState state = 3;                   // 状态
  map<uint32, uint32> guess_status = 4;  // 轮次竞猜状态 round => 下注次数
  repeated PeakMatch my_matches = 5;     // 玩家所有参与的比赛数据
  bool recv_invite_reward = 6;           // 是否已领取邀请奖励
  PeakMatch guess_match = 7;             // 当前轮次竞猜的比赛数据
  uint32 rank = 8;                       // 赛季排名
}

// 巅峰竞技场 - 比赛参数信息
message PeakMatchParam {
  uint32 round = 1;  // 第几轮，小组赛:1-3 决赛:4-6 冠军赛:7
  uint32 group = 2;  // 第几组，小组赛:1-16 决赛/冠军赛:0
  uint32 area = 3;   // 战区，小组赛/决赛:1上半区 2下半区  冠军赛:0
}

// 巅峰竞技场 - 获取比赛数据
message C2L_PeakGetMatch {
  repeated PeakMatchParam params = 1;
}
message L2C_PeakGetMatch {
  uint32 ret = 1;
  repeated PeakMatchParam params = 2;  // 参数回传
  repeated PeakMatch matches = 3;      // 对战信息。根据参数，返回对应赛场数据（非全部对战数据）
}

// 巅峰竞技场 - 领取邀请奖励
message C2L_PeakRecvInviteReward {}
message L2C_PeakRecvInviteReward {
  uint32 ret = 1;
  repeated Resource reward = 2;
}

// 巅峰竞技场 - 选手完赛详情数据
message C2L_PeakFighterDetail {
  uint64 uid = 1;  // 玩家ID
}
message L2C_PeakFighterDetail {
  uint32 ret = 1;
  uint64 uid = 2;                  // 玩家ID
  repeated PeakMatch matches = 3;  // 玩家已完成的赛场比赛数据
}

// 巅峰竞技场 - 竞猜列表
message C2L_PeakGuessList {
  repeated uint32 rounds = 1;  // 轮次列表
}
message L2C_PeakGuessList {
  uint32 ret = 1;
  repeated uint32 rounds = 2;        // 轮次列表
  repeated PeakMatch matches = 3;    // 竞猜赛事数据
  repeated PeakUserGuess guess = 4;  // 全部竞猜数据
}

// 巅峰竞技场 - 竞猜下注
message C2L_PeakDoGuess {
  uint32 match_id = 1;  // 比赛场次id
  uint32 count = 2;     // 下注次数
  uint64 uid = 3;       // 下注选手id
}
message L2C_PeakDoGuess {
  uint32 ret = 1;
  uint32 match_id = 2;  // 比赛场次id
  uint32 count = 3;     // 下注次数
  uint64 uid = 4;       // 下注选手id
}

// 巅峰竞技场 - 排行榜
message C2L_PeakRankList {
  uint32 type = 1;        // 类型 1-巅峰霸主 2-名人堂 3-赛季榜
  uint32 start_rank = 2;  // 赛季榜分页，闭区间rank开始值
  uint32 end_rank = 3;    // 赛季榜分页，闭区间rank结束值
}
message L2C_PeakRankList {
  uint32 ret = 1;
  uint32 type = 2;               // 类型 1-巅峰霸主 2-名人堂 3-赛季榜
  uint32 start_rank = 3;         // 赛季榜分页，闭区间rank开始值
  uint32 end_rank = 4;           // 赛季榜分页，闭区间rank结束值
  repeated PeakResult list = 5;  // 排行数据列表
  PeakResult self = 6;           // 仅type为赛季榜时，才会有值
}

// 巅峰竞技场 - 数据更新提示
message L2C_PeakUpdateTip {
  uint32 ret = 1;
}

// 巅峰竞技场 - 膜拜
message C2L_PeakWorship {}
message L2C_PeakWorship {
  uint32 ret = 1;
  repeated Resource awards = 2;
}

// 巅峰竞技场 - 获取上一场战报
message C2L_PeakGetLastBattleReport {
  uint64 uid = 1;
}
message L2C_PeakGetLastBattleReport {
  uint32 ret = 1;
  uint64 uid = 2;
  string report_id = 3;  // 战报id
  bool is_attacker = 4;  // 是否进攻方
}

message PreSeason {
  map<uint32, bool> recv_award = 1;
}

message C2L_PreSeasonGetData {}

message L2C_PreSeasonGetData {
  uint32 ret = 1;
  PreSeason pre_season = 2;
  repeated string name = 3;
}

message C2L_PreSeasonRecvAward {
  uint32 award_id = 1;
}

message L2C_PreSeasonRecvAward {
  uint32 ret = 1;
  uint32 award_id = 2;
  PreSeason pre_season = 3;
  repeated Resource awards = 4;  // 奖励
}

// 赛季回流奖励数据
message SeasonReturnAward {
  uint64 award_id = 1;   // 奖励唯一ID
  uint32 return_id = 2;  // 回流ID（对应season_return_info表的id字段）
  uint32 day_count = 3;  // 赛季内流失天数或赛季开启天数
  int64 start_time = 4;  // 开始时间
  bool accepted = 7;     // 奖励是否已领取
}

// 赛季回流数据
message SeasonReturn {
  int64 join_season_time = 1;             // 本赛季进入赛季时间
  repeated SeasonReturnAward awards = 2;  // 回流奖励列表
}

message C2L_SeasonReturnGetData {}

message L2C_SeasonReturnGetData {
  uint32 ret = 1;
  SeasonReturn data = 2;  // 回流数据
}

// 领取奖励
message C2L_SeasonReturnTakeAwards {
  repeated uint64 award_ids = 1;  // 奖励唯一ID列表
}

message L2C_SeasonReturnTakeAwards {
  uint32 ret = 1;
  repeated uint64 award_ids = 2;  // 奖励唯一ID列表
  SeasonReturn data = 3;          // 更新后的7日登陆数据
  repeated Resource awards = 4;   // 奖励
}

// 小助手数据
message AssistantData {
  repeated uint64 arena_like_uids = 1;    // 竞技场点赞玩家列表
  repeated uint64 wrestle_like_uids = 2;  // 神树点赞玩家列表
  bool can_wrestle_like = 3;              // 神树能够点赞
}

message C2L_AssistantGetData {}

message L2C_AssistantGetData {
  uint32 ret = 1;
  AssistantData data = 2;
}

// 公会战-----------------------------------------------------------------------------

// 个人贡献来源
enum GSTUserScoreType {
  USTYPE_TOTAL = 0;          // 总贡献
  USTYPE_SEND_TEAM = 1;      // 派遣队伍
  USTYPE_PVE_FIGHT = 2;      // PVE战斗
  USTYPE_PVP_FIGHT = 3;      // PVP战斗
  USTYPE_GODDESS_BLESS = 4;  // 女神羁绊
  USTYPE_BUILD_TASK = 5;     // 建设任务
}

// 排行榜展示数据
message GSTUserScoreRankShow {
  map<int32, uint32> score = 1;
}

// 公会积分来源
enum GSTGuildScoreType {
  GSTYPE_TOTAL = 0;                               // 总积分
  GSTYPE_CONTROL_GROUND = 1;                      // 占领地块
  GSTYPE_CONTROL_GROUND_GODDESS_BLESS = 2;        // 占领地块女神羁绊加成
  GSTYPE_CONTROL_GROUND_GODDESS_BLESS_LEVEL = 3;  // 占领地块女神羁绊等级加成
  GSTYPE_BUILD = 4;                               // 王城建筑
  GSTYPE_BUILD_GODDESS_BLESS_LEVEL = 5;           // 王城建筑女神羁绊等级加成
  GSTYPE_ARENA_RANK = 6;                          // 擂台排名
  GSTYPE_CONTROL_GROUND_ONCE = 7;                 // 地块一次性积分
  GSTYPE_CHALLENGE_RANK = 8;                      // 新擂台赛排名
}

message GSTFormationTeamInfo {
  repeated GSTTeamHeroInfo heros = 1;
  uint32 id = 2;               // 所在地块id.如果派出去了就有
  bool hang_up = 3;            // 是否托管
  int64 ask_hang_up_time = 4;  // 管理请求托管时间
  uint32 team_index = 5;       // 第几只队伍
  GST_TEAM_STAGE state = 6;    // 行动状态
  bool change_manager = 7;     // 管理员行动后被自己改变状态(管理弹通知)
  uint32 skin_id = 8;          // 队伍的皮肤ID
  int64 power = 9;             // 战力
}

message GSTTeamHeroInfo {
  uint32 sys_id = 1;  // 系统id
  uint32 pos = 2;
  uint32 star = 3;
  bool actived_season_link = 4;  // 是否激活赛季羁绊
}

message GSTGetLogReq {
  GSTGetLogReqType type = 1;
  GSTGetLogReqData data = 2;
}

enum GSTGetLogReqType {
  ReqType_None = 0;
  ReqType_Personal_Contribution = 1;  // 个人贡献
  ReqType_Personal_Fight = 2;         // 个人战报
  ReqType_Guild = 3;                  // 公会日志
  ReqType_Ground = 4;                 // 房间地块日志
  ReqType_Build_Donate_Details = 5;   // 公会建筑捐献日志
  ReqType_Arena_Fight_Record = 6;     // 擂台战排名记录
  ReqType_GSTBoss_Fight_Record = 7;   // boss战战报记录
  ReqType_Dragon_Fight = 8;           // 龙战挑战记录
  ReqType_Guild_Mobilization = 9;     // 公会竞赛前3名
  ReqType_Ore_Change = 10;            // 矿变动
  ReqType_Ore_Assist = 11;            // 协助矿
  ReqType_Tech = 12;                  // 科技
  ReqType_Guild_Skill = 13;           // 技能
  ReqType_Challenge = 14;             // 新擂台赛排名记录
}

message GSTGetLogReqData {
  GSTGetLogPC pc = 1;
  GSTGetLogPF pf = 2;
  GSTGetLogGuild guild = 3;
  GSTGetLogGround ground = 4;
  GSTGetLogBuildDonateDetails build_donate_details = 5;
  GSTGetLogArenaFightRecord arena_fight_record = 6;
  GSTGetLogGSTBossFightRecord gstboss_fight_record = 7;
  GSTGetLogDragonFightRecord dragon_fight_record = 8;
  GSTGetLogGuildMobilizationRank guild_mobilization_rank = 9;
  GSTGetLogOreChange ore_change = 10;
  GSTGetLogOreAssist ore_assist = 11;
  GSTGetLogTech tech = 12;
  GSTGetLogGuildSkill guild_skill = 13;
  GSTGetLogChallengeRecord challenge = 14;
}

message GSTGetLogGuildSkill {
  uint64 group_id = 1;
  uint64 guild_id = 2;
  uint64 start_log_id = 3;
}

message GSTGetLogTech {
  uint32 season_id = 1;
  uint64 guild_id = 2;
  uint64 start_log_id = 3;
}

message GSTGetLogOreAssist {
  uint64 group_id = 1;
  uint64 start_log_id = 2;
}

message GSTGetLogOreChange {
  uint32 round = 1;
  uint64 start_log_id = 2;
}

message GSTGetLogGuildMobilizationRank {
  uint32 round = 1;  // 公会竞赛的期数
}

message GSTGetLogPC {
  uint64 group_id = 1;  // 房间id
  uint32 l_round = 2;   // 回合
}

message GSTGetLogPF {
  uint64 group_id = 1;  // 房间id
  uint32 l_round = 2;   // 回合
}

message GSTGetLogGuild {
  uint64 group_id = 1;  // 房间id
  uint32 l_round = 2;   // 回合
  uint64 guild_id = 3;
  uint32 season_id = 4;  // 赛季id
  uint32 round = 5;      // 轮次
}

message GSTGetLogGround {
  uint64 group_id = 1;      // 房间id
  uint32 l_round = 2;       // 回合
  uint64 start_log_id = 3;  // 起始日志id
  uint32 ground_id = 4;
}

message GSTGetLogBuildDonateDetails {
  uint64 group_id = 1;  // 房间id
  uint64 guild_id = 2;
  uint64 start_log_id = 3;  // 起始日志id
}

message GSTGetLogArenaFightRecord {
  uint64 group_id = 1;  // 房间id
  uint32 l_round = 2;   // 回合
  uint32 round = 3;     // 擂台回合
}

message GSTGetLogGSTBossFightRecord {
  uint64 group_id = 1;  // 房间id
  uint64 guild_id = 2;
  uint32 round = 3;         // 轮次
  uint64 start_log_id = 4;  // 起始日志id
}

message GSTGetLogDragonFightRecord {
  uint32 season_id = 1;     // 赛季id
  uint32 dragon_round = 2;  // 龙战场次
  uint64 user_id = 3;       // 玩家id
  uint64 guild_id = 4;      // 公会id
  uint64 start_log_id = 5;  // 起始日志id
}

message GSTGetLogChallengeRecord {
  uint64 group_id = 1;  // 房间id
  uint32 l_round = 2;   // 回合
  uint32 round = 3;     // 轮次
}

message GSTManyLog {
  repeated GSTLogInfo logs = 1;
}

message GSTLogInfo {
  GSTLogType log_type = 1;
  GSTLogData log_info = 2;
  uint32 l_round = 3;  // 回合
  uint64 _id = 4;
  uint64 guild_id = 5;
  uint64 group_id = 6;
  uint32 ground_id = 7;
  int64 gen_time = 8;  // 日志时间
  uint32 season_id = 9;
  uint32 round = 10;  // 轮次
}

enum GSTLogType {
  LogType_None = 0;
  LogType_Donate = 1;              // 捐献 (个人贡献日志)
  LogType_FightTeam = 2;           // 参战队伍 (个人贡献日志)
  LogType_FightResult = 3;         // 战斗 (个人贡献日志 + 个人战报记录 + 房间地块日志)
  LogType_RoundSettle = 4;         // 回合结算信息 (公会日志)
  LogType_UserWinsRank = 5;        // 回合结算完成的连胜成员排名 (公会日志)
  LogType_LRoundGuildRank = 6;     // 回合结算完成的公会排名 (公会日志)
  LogType_GroupSettle = 7;         // 房间地块结束结算(房间地块日志)
  LogType_BuildDonate = 8;         // 建筑捐赠任务贡献(个人贡献日志)
  LogType_BuildDonateDetails = 9;  // 建筑捐献详情
  LogType_ArenaFightRecord = 10;   // 擂台战斗排名结果
  LogType_GSTBossFight = 11;       // 公会boss战结果
  LogType_GSTDragonFight = 12;     // 龙战挑战记录
  LogType_GSTDragonSkill = 13;     // 龙战技能记录(房间地块日志)
  LogType_GuildMobilization = 14;  // 公会竞赛展示前三名
  LogType_OreChange = 15;          // 矿的变化
  LogType_OreAssist = 16;          // 矿协助日志
  LogType_Tech = 17;               // 科技日志
  LogType_GuildSkill = 18;         // 技能
  LogType_ChallengeRecord = 19;    // 新擂台赛单场排名结果
}

message GSTLogData {
  GSTDonate donate = 1;
  GSTLogFightTeam fight_team = 2;
  GSTFightResult fight_result = 3;
  GSTRoundSettle round_settle = 4;
  GSTUserWinsRank user_rank = 5;
  GSTLastLRoundGuildRank guild_rank = 6;
  GSTLogGroupSettle group_settle = 7;
  GSTBuildDonate build_donate = 8;
  GSTBuildDonateDetails build_donate_details = 9;
  GSTArenaFightRecord arena_fight_record = 10;
  GSTBossFightRecord boss_fight_record = 11;
  GSTDragonFightRecord dragon_fight_record = 12;
  GSTLogDragonSkill dragon_skill = 13;
  GSTLogGuildMobilization guild_mobilization_rank = 14;
  GSTLogOreChange ore_change = 15;
  GSTLogOreAssist ore_assist = 16;
  GSTLogTech tech = 17;
  GSTLogGuildSkill guild_skill = 18;
  GSTChallengeRecord challenge_record = 19;  // 新擂台赛记录
}

enum GSTSkillType {
  GSTSkillType_None = 0;
  GSTSkillType_DragonSkill = 1;
  GSTSkillType_BackDragonSkill = 2;
  GSTSkillType_Assemble = 3;
}

message GSTLogGuildSkill {
  GSTSkillType skill_type = 1;
  string name = 2;
  uint32 ground_id = 3;
}

message GSTLogTech {
  string name = 1;
  uint32 donate = 2;
  uint32 tech_id = 3;
}

enum OreLogType {
  OreLogType_None = 0;
  OreLogType_Fight = 1;   // 战斗,协助
  OreLogType_Tech = 2;    // 科技增加可占领数量
  OreLogType_Ground = 3;  // 地块变更
}
message GSTLogOreChange {
  OreLogType change_type = 1;
  GSTGuildUserOreData after_data = 2;
  repeated GSTGuildUserOreData lose_occupys = 3;
  repeated GSTGuildUserOreData add_occupys = 4;
}
message GSTLogOreAssist {
  bool is_assist = 1;  // 协助还是被协助
  GSTGuildUserOreData before_data = 2;
  string name = 3;
  uint32 progress = 4;
}

message GSTLogGuildMobilization {
  repeated RankValue ranks = 1;
}

message GSTLogDragonSkill {
  uint32 land_id = 1;
  repeated GSTLogDragonSkillGuildInfo guild_info = 2;
}

message GSTLogDragonSkillGuildInfo {
  string guild_name = 1;
  uint32 dragon_id = 2;
  uint32 dragon_level = 3;
}

message GSTLogGroupSettle {
  string before_control_guild = 1;
  uint64 before_guild_id = 2;
  uint32 before_land_id = 3;
  string after_control_guild = 4;  // 没有变更就没有
  uint64 after_guild_id = 5;
  repeated GSTLogGroupGuildSettle guild_info = 6;
  uint32 before_guild_partition = 7;
  uint32 after_guild_partition = 8;
}

message GSTLogGroupGuildSettle {
  uint64 guild_id = 1;
  string guild_name = 2;
  uint32 team_num = 3;
  uint32 win = 4;
  uint32 lose = 5;
  uint32 guild_partition = 6;
}

message GSTLogFightTeam {
  GSTFightTeam fight_team = 1;
  uint32 ground_id = 2;
}

message GSTDonate {
  uint64 uid = 1;
  uint32 score = 2;       // 产生的贡献值
  uint32 goddess_id = 3;  // 女武神ID
}

message GSTBuildDonate {
  uint32 task_id = 1;
  uint32 score = 2;  // 产生的贡献值
  uint64 uid = 3;    // 用户ID
}

message GSTBuildDonateDetails {
  GSTGuildUserBase user = 1;      // 玩家简要信息
  repeated Resource donates = 2;  // 捐赠的物品
  uint32 build_type = 3;          // 建筑类型
}

// 派遣的队伍信息
message GSTFightTeam {
  GSTGuildUserBase user = 1;           // 玩家简要信息
  uint32 team_index = 2;               // 队伍标记
  uint32 score = 3;                    // 产生的贡献值
  uint32 wins = 4;                     // 胜利场次
  uint32 fatigue_value = 5;            // 疲劳值
  map<uint32, int64> hp_pct = 6;       // 战斗结算中记录的剩余血量
  uint32 monster_id = 7;               // 如果是怪物
  int64 power = 8;                     // 队伍战力
  uint32 morale = 9;                   // 队伍士气(只在战报中有,战斗时才计算这个值)
  uint32 space_id = 10;                // 主城地块ID
  int32 popular_value = 11;            // 擂台战公会人气值
  uint32 monster_team_index = 12;      // 怪物当前的index(不再复用team_index!)
  uint32 monster_team_index_ext = 13;  // 针对怪物的扩展次数
  map<uint32, AltPassives> alt_passives = 14;
  repeated GSTTeamHeroInfo heros = 15;  // 战报展示英雄信息
  uint32 pvp_wins = 16;                 // pvp胜场
}

message AltPassives {
  repeated uint64 passives = 1;
}

// 战斗结果
message GSTFightResult {
  GSTFightTeam attack = 1;
  GSTFightTeam defense = 2;
  bool is_win = 3;
  string report_id = 4;  // 战报id
  int64 match_time = 5;
  uint32 ground_id = 6;           // 在哪个地块上战斗
  uint32 fight_id = 7;            // 战斗的唯一id
  string attack_guild_name = 8;   // 攻击方公会名
  string defense_guild_name = 9;  // 防守方公会名
  uint32 win = 10;                // 胜方连胜场次
  uint32 map_config_id = 11;      // 地图配置id
  uint32 arena_round = 12;        // 擂台轮次
}

// 每回合结算信息
message GSTRoundSettle {
  uint32 round = 1;                  // 轮次
  uint32 l_round = 2;                // 回合
  map<int32, uint32> old_score = 3;  // 地块积分
  map<int32, uint32> new_score = 4;  //
  uint32 old_ground = 5;             // 地块数量
  uint32 new_ground = 6;             //
  uint32 old_rank = 7;               // 排名
  uint32 new_rank = 8;
  uint32 old_division_score = 9;  // 段位积分
  uint32 quality = 10;            // 房间品质
  uint64 guild_id = 11;           // 给前端需要公会id
}

// 回合结算排名
message GSTUserWinsRank {
  repeated GSTGuildUserBase users = 1;  // 连胜排名
}

message GSTGuildUserBase {
  uint64 id = 1;
  string name = 2;
  uint32 season_lv = 3;                    // 赛季等级
  uint32 base_id = 4;                      // 头像相关
  uint64 sid = 5;                          // 服务器ID
  map<int32, uint32> score = 6;            // 贡献值
  uint64 guild_id = 7;                     // 公会id
  uint32 wins = 8;                         // 战斗结算的连胜
  map<uint32, uint32> donate_num = 9;      // key:女武神ID value:捐赠次数
  int64 season_link_power = 10;            // 丰碑战力加成
  repeated int64 expire_time = 11;         // 头像过期时间
  uint32 level = 12;                       // 等级
  uint32 image = 13;                       // 形象ID
  uint32 show_hero = 14;                   // 战力最高的英雄sysID
  uint32 remain_book_level = 15;           // 遗物图鉴等级
  string guild_name = 16;                  // 公会名
  uint32 talent_tree_lv = 17;              // 天赋树根节点等级
  uint32 title = 18;                       // 称号
  repeated SeasonAddInfo season_add = 19;  // 赛季加成数据
  uint32 pokemon_image = 20;               // 宠物形象ID
}

message GSTGuildUser {
  GSTGuildUserBase info = 1;                                         // 玩家信息
  repeated GSTFormationTeamInfo teams = 2;                           // 队伍信息
  uint32 use_free_donate_times = 3;                                  // 使用的捐献次数(月卡玩家可免费的次数)
  int64 login_time = 4;                                              // 登录时间(判断队伍是否托管)
  repeated uint64 added_guild = 5;                                   // 战斗期间加过的公会 退出公会时处理 轮次重置
  int64 reward_last_calc_time = 6;                                   // 上一次计算奖励的时间
  repeated Resource awards = 7;                                      // 缓存的放置奖励
  int64 reward_duration = 8;                                         // 挂机有效时长
  int64 save_reward_duration = 9;                                    // 不足一次的存储时间
  bool is_pre_award = 10;                                            // 是否领取过奖励
  int64 can_sign_time = 11;                                          // 玩家能够参与玩法的时间
  map<uint32, uint32> ground_num = 12;                               // key:地块类型 value:对应类型数量
  uint32 recv_hang_out_count = 13;                                   // 领取放置奖励的次数
  uint32 team_battle_count = 14;                                     // 队伍战斗次数
  uint32 team_battle_win_count = 15;                                 // 队伍战斗胜利次数
  uint32 team_operate_count = 16;                                    // 队伍派遣次数
  repeated bool team_operate_index = 17;                             // 本回合队伍移动标志位
  uint32 total_ground_num = 18;                                      // 参与占领地块总数
  repeated Resource dispatch_awards = 19;                            // 派遣奖励
  repeated Resource win_awards = 20;                                 // 胜利奖励
  uint32 set_team_count = 21;                                        // 设置队伍次数
  uint32 team_battle_pve_count = 22;                                 // 队伍PVE战斗次数
  uint32 team_battle_pve_win_count = 23;                             // 队伍PVE战斗胜利次数
  uint32 dispatch_award_team_num = 24;                               // 派遣队伍奖励数量
  uint32 win_award_team_num = 25;                                    // 战胜队伍奖励数量
  map<uint32, bool> seasonlink_actived = 26;                         // 赛季羁绊激活的英雄
  map<uint32, GSTBuildUserDispatchHeroes> build_dispatch_hero = 27;  // 建筑内派遣英雄
  GSTBuildTaskInfo build_task = 28;                                  // 建筑任务信息
  repeated Resource build_award = 29;                                // 建筑奖励
  uint32 add_fight_times = 30;                                       // 影响队伍的可战斗次数
  repeated GstGroupUserRankValue rank_values = 31;                   // 小组内玩家排行榜数据(包括贡献榜、杀敌榜、三杀榜)
  GstTripleKillInfo triple_kill_record = 32;                         //  三杀数据
  repeated GSTGuildUserTeamFightInfo lround_fight_info = 33;         // 当前回合战斗信息
  GSTGuildUserRoundSettle last_lround_settle_info = 34;              // 上回合结算信息
  uint32 kill_rank_value = 35;                                       // 击杀榜的杀敌数量
  uint32 arena_vote_times = 36;                                      // arena可投票次数
  uint32 add_vote_round = 37;                                        // 加过投票的擂台轮次
  uint32 boss_monster_group = 38;                                    // boss战 怪物组
  map<uint32, uint32> boss_award = 39;                               // boss战奖励 index：0未领奖 1已领奖
  map<uint32, uint64> max_damage = 40;                               // bossid:最大伤害 2回合更新
  uint64 total_damage = 41;                                          // 累计伤害
  uint32 fight_times = 42;                                           // 战斗次数
  GSTDragonUserSeasonData dragon_user_season = 43;                   // 龙战玩家赛季数据，赛季重置
  GSTDragonUserRoundData dragon_user = 44;                           // 龙战玩家场次数据，每场龙战重置
  GSTDragonTaskInfo dragon_task = 45;                                // 龙战任务信息，每场龙战重置
  GSTGuildUserOre ore = 46;                                          // 占矿信息
  GSTGuildUserTech tech = 47;                                        // 公会战科技
  GSTGuildUserChallenge challenge = 48;                              // 公会战新擂台赛
}

message GstGroupUserRankValue {  // 记录数据变化时间、点赞次数。真正的排行数据，复用GSTGuildUser的字段
  uint32 rank_id = 1;
  int64 tm = 2;
  uint32 liked_count = 3;
}

message GstTripleKillInfo {
  uint32 triple_kill_count = 1;              // 三杀次数
  repeated GstTripleKillTeamInfo teams = 5;  // 三杀队伍信息
}

message GstTripleKillTeamInfo {
  uint32 team_index = 1;
  uint32 continue_win_count = 2;  // 当前连赢几场
  bool have_pvp = 3;              // 是否包含pvp
}

message GSTGuildUserRoundSettle {
  uint32 round = 1;                  // 轮次
  uint32 l_round = 2;                // 回合
  map<int32, uint32> old_score = 3;  // 贡献
  map<int32, uint32> new_score = 4;
  uint32 old_rank = 5;  // 贡献排名
  uint32 new_rank = 6;
  repeated GSTGuildUserTeamFightInfo fight_info = 7;  // 参战队伍信息
}

message GSTGuildUserTeamFightInfo {
  uint32 team_index = 1;
  uint32 ground_id = 2;   // 地块id
  uint32 win_times = 3;   // 胜利场次
  uint32 lose_times = 4;  // 失败场次
  uint32 land_id = 5;
  uint32 score = 6;
  uint32 pvp_win_times = 7;  // pvp胜利场次
}

// 玩家的赛季龙战数据
message GSTDragonUserSeasonData {
  map<uint32, uint64> rank_max_damage = 1;  // 不同排行类型的龙的最大伤害，赛季重置
}

// 玩家单场龙战数据
message GSTDragonUserRoundData {
  uint64 battle_damage = 1;                                     // 本场龙战总伤害
  uint32 battle_count = 2;                                      // 本场龙战总挑战次数
  uint64 guild_damage = 3;                                      // 本场龙战公会内总伤害
  uint32 guild_count = 4;                                       // 本场龙战公会内总挑战次数
  map<uint32, GSTDragonUserRoundMaxDamage> pos_max_damage = 5;  // 本场龙战对不同位置的龙造成的最大伤害，扫荡用
  uint32 total_count = 6;                                       // 剩余的挑战次数
  uint32 diamond_buy_count = 7;                                 // 钻石购买了X次数
}

message GSTDragonUserRoundMaxDamage {
  repeated uint64 damages = 1;
  uint64 total_damage = 2;                        // 总伤害
  repeated GSTDragonUserTeamData team_datas = 3;  // 队伍数据
}

message GSTDragonUserTeamData {
  repeated uint32 team_heroes = 1;
  uint32 team_progress = 2;
  bool battle_hp_max = 3;  // 本次伤害是否达到龙的战斗血量上限（非机制血量上限）
}

enum GST_STAGE {
  Round0State_None = 0;
  Round0State_Sign = 1;                 // 报名期
  Round0State_Matching = 2;             // 匹配期
  Round0State_Finish = 3;               // 匹配完成
  RoundFightState_Operate = 4;          // 行动期
  RoundFightState_SyncGuild = 5;        // 同步公会
  RoundFightState_SyncGuildFinish = 6;  // 同步完公会
  RoundFightState_Fight = 7;            // 行动结算期
  RoundFightState_RewardGuild = 8;      // 给公会发积分结算
  RoundFightState_Reward = 9;           // 奖励结算期(轮次结算才有,最后一个回合)
  RoundFightState_Finish = 10;          // 行动期完成
}

enum GST_TEAM_STAGE {
  Gst_Team_None = 0;     // 未行动
  Gst_Team_Manager = 1;  // 管理员行动
  Gst_Team_Self = 2;     // 自己行动了
}

// 状态信息
message GSTSta {
  uint32 round = 1;       // 当前轮次
  uint32 l_round = 2;     // 回合数 0是报名期 后面26回合是战斗期
  GST_STAGE stage = 3;    // 回合时的状态变更.初始化时需要根据状态来更新定时器并处理异常状态
  uint32 season_id = 4;   // 赛季id
  bool reward_guild = 5;  // 轮次结算有没有结算公会积分
}

message GSTLastLRoundGuildRank {
  repeated GSTSimpleGuild guild_rank = 1;  // 公会排名
}

message GSTSimpleGuild {
  uint64 guild_id = 1;
  map<int32, uint32> score = 2;                     // 公会地块积分
  uint32 ground_num = 3;                            // 拥有地块数量
  string name = 4;                                  // 公会名
  uint32 division = 5;                              // 公会段位
  uint32 division_score = 6;                        // 公会段位积分
  uint32 badge = 7;                                 // 公会头像
  repeated uint64 user_ids = 8;                     // 公会成员
  map<uint32, GSTGoddessBless> goddess_donate = 9;  // 公会祝福信息
  map<uint32, uint32> ground_type_count = 10;       // 地块类型数量
  repeated uint64 leaders = 11;                     // 领导
  uint64 guild_sid = 12;                            // 公会服务器ID
  repeated int64 expire_time = 13;                  // 公会头像过期时间
  repeated uint32 first_item_lands = 14;            // 首占地块
  uint32 main_base_level = 15;                      // 主城建筑等级
  uint32 partition = 16;                            // 公会所在小分区
  uint32 dragon_pos = 17;                           // 龙位置
  uint32 dragon_show_id = 18;                       // 龙展示id
}

message GSTGoddessBless {
  uint32 goddess_id = 1;
  uint32 bless_count = 2;
  int64 donate_time = 3;  // 最后一次捐赠时间
}

message GSTBuild {
  uint32 build_type = 1;
  uint32 build_level = 2;
  repeated Resource donate_total = 3;
}

message GSTBuildDonateRank {
  uint64 uid = 1;
  repeated Resource donate_cost = 2;
  int64 last_donate_time = 3;
  uint32 total_cost_count = 4;
}

message GSTBuildDonateRankClient {
  GSTGuildUserBase base = 1;
  repeated Resource donate_cost = 2;
  int64 last_donate_time = 3;
  uint32 total_cost_count = 4;
}

message GSTBuildUserDispatchHeroes {
  uint32 build_type = 1;
  repeated GSTBuildDispatchHero build_dispatch_hero = 2;  // 建筑派遣英雄
}

message GSTBuildDispatchHero {
  uint32 sys_id = 1;
  uint32 star = 2;
  uint64 hero_id = 3;
}

// 客户端协议
message GSTBuildUserDispatchHeroesClient {
  uint32 build_type = 1;
  repeated GSTBuildDispatchHero build_dispatch_hero = 2;
  uint32 total_count = 3;
}

message GSTTaskInfo {
  map<uint32, TaskTypeProgress> task_type_progress = 1;
  map<uint32, bool> awarded = 2;
}

message GSTBuildTaskInfo {
  map<uint32, TaskTypeProgress> task_type_progress = 1;
  map<uint32, bool> awarded = 2;
}

message GSTDragonTaskInfo {
  map<uint32, TaskTypeProgress> task_type_progress = 1;
  map<uint32, bool> awarded = 2;
}

message GstGuildAddScore {
  uint64 gid = 1;
  uint32 season_id = 2;
  uint32 round = 3;
  uint32 score = 4;
}

message C2L_GSTGetData {
  uint32 type = 1;  // 获取简略和详细
}

message L2C_GSTGetData {
  uint32 ret = 1;
  uint32 type = 2;
  GSTSimpleData simple = 3;
  GSTComplexData complex = 4;
  GSTSta sta = 5;
  int64 reward_duration = 6;  // 挂机的有效时长
  bool box_have_reward = 7;   // 是否有挂机奖励
  string chat_room_id = 8;    // 聊天id
  bool signed = 9;            // 是否报过名了
}
message GSTSimpleData {
  bool team_not_move = 1;                         // 有队伍没有移动
  GSTTaskInfo gst_task_info = 2;                  // 任务信息
  GSTBuildTaskInfo gst_build_task_info = 3;       // 建筑任务信息
  uint32 build_main_level = 4;                    // 建筑主城等级
  uint32 dragon_show_id = 5;                      // 龙战龙展示id
  uint32 dragon_left_count = 6;                   // 龙战剩余挑战次数
  GSTDragonTaskInfo gst_dragon_task_info = 7;     // 龙战任务信息
  uint32 task_award_group = 8;                    // 任务组ID
  GSTTechTaskData gst_tech_task_data = 9;         // 科技任务
  GSTChallengeTaskData challenge_task_data = 10;  // 新擂台赛任务
}
message GSTComplexData {
  repeated GSTRoundSettle last_round_info = 1;                       // 上一回合信息变化
  GSTLastLRoundGuildRank last_lround_rank = 2;                       // 上一回合排名
  GSTMapInfo map_info = 3;                                           // 房间信息
  bool guild_leader_donate_free_cost = 4;                            // 公会捐赠会长免费次数是否使用
  string guild_message = 5;                                          // 公会战公告
  int64 can_sign_time = 6;                                           // 如果有值,需要等这个时间后才能加入
  int64 guild_can_match_time = 7;                                    // 如果有值,公会需要等这个时间后才能匹配
  uint32 max_ground = 8;                                             // 公会战令的最大地块
  repeated GSTGuildUserBase top_3 = 9;                               // 排名前三
  uint64 message_board_id = 10;                                      // 公告id
  GSTSimpleGuild guild_info = 11;                                    // 报名期时,自己公会的补充信息
  repeated GSTBuild builds = 12;                                     // 公会建筑信息
  map<uint32, GSTBuildUserDispatchHeroes> build_dispatch_hero = 13;  // 建筑内派遣英雄
  GSTGuildUserRoundSettle last_user_lround_info = 14;                // 上一回合玩家自身信息变化
  uint64 group_id = 15;                                              // 房间id
  repeated GSTArenaState arena_states = 16;                          // 是否开启擂台战
  map<uint32, GSTBossGroup> boss_group = 17;                         // 公会战boss组 guild_index:GSTBossGroup
  map<uint32, uint32> boss_award = 18;                               // boss战奖励 index：0未领奖 1已领奖
  map<uint32, uint64> max_damage = 19;                               // bossid:最大伤害 2回合更新
  GSTDragonCultivation dragon_cultivation = 20;                      // 龙养成
  GSTDragonSkill dragon_skill = 21;                                  // 龙攻城秘技
  GSTGuildTech guild_tech = 22;                                      // 公会科技
  bool challenge_finish = 23;                                        // 擂台赛是否完成
}

message GSTMapInfo {
  uint32 map_config_id = 1;
  map<uint32, GSTGroundInfo> GSTGroundInfos = 2;
  repeated uint64 guild_ids = 3;            // 公会顺序
  uint32 quality = 4;                       // 房间品质
  repeated GSTSimpleGuild guilds_info = 5;  // 公会信息
  repeated uint64 arena_guild_ids = 6;      // 擂台战公会顺序
}
message GSTGroundInfo {
  uint64 guild_id = 1;                      // 所属公会
  uint32 land_id = 2;                       // 地块量表类型id
  uint32 fight_num = 3;                     // 战斗队伍数量
  bool fighting = 4;                        // 正在战斗结算中
  repeated uint64 dragon_skill_guilds = 5;  // 对地块放龙战技能的公会
  uint32 dragon_skill_lround = 6;           // 龙战技能能释放的回合数
}

message C2L_GSTGetGroundData {
  uint32 id = 1;  // 地块id
}
message L2C_GSTGetGroundData {
  uint32 ret = 1;
  uint32 id = 2;  // 地块id
  GSTGroundTeamData team_data = 3;
  GSTFightTeam monster_team = 4;       // 怪物队伍信息
  string first_occupy_guild_name = 5;  // 首占公会ID
  GSTArenaInfo arena_info = 6;         // 如果是擂台有擂台信息
  uint32 arena_vote_times = 7;
}
message GSTGroundTeamData {
  repeated GSTGuildUserBase user_info = 1;
  repeated GSTGroundTeamInfo team_info = 2;
}
message GSTGroundTeamInfo {
  uint64 uid = 1;
  repeated GSTTeamHeroInfo heros = 2;
  uint32 team_index = 3;
  bool hang_up = 4;  // 是否托管
  int64 power = 5;   // 战力
}

message C2L_GSTGetTeamsData {
  uint32 type = 1;  // 自己的队伍,全部队伍(管理权限),队伍信息预览
}
message L2C_GSTGetTeamsData {
  uint32 ret = 1;
  uint32 type = 2;
  repeated GSTTeamData teams_data = 3;
}
message GSTTeamData {
  GSTGuildUserBase user_info = 1;  // 玩家展示信息
  repeated GSTFormationTeamInfo team_info = 2;
}

message C2L_GSTGetLogData {
  GSTGetLogReq req = 1;
}
message L2C_GSTGetLogData {
  uint32 ret = 1;
  GSTGetLogReq req = 2;
  GSTManyLog log = 3;
}

message C2L_GSTTeamOperate {
  uint32 type = 1;  // 队伍移动,撤回队伍,托管队伍,取消托管
  repeated GSTTeamOperate operates = 2;
  bool is_auto = 3;  // 是否为一键派遣
}
message GSTTeamOperate {
  repeated uint32 ids = 1;  // 地块路线
  repeated GSTFightTeam teams = 2;
}
message L2C_GSTTeamOperate {
  uint32 ret = 1;
  uint32 type = 2;
  repeated GSTTeamOperate operates = 3;
  map<uint32, GSTGroundInfo> GSTGroundInfos = 4;  // 更新地块队伍数量
  bool is_auto = 5;                               // 是否为一键派遣
}

message C2L_GSTExchangeGroundTeam {
  uint32 id = 1;  // 地块id
  repeated GSTFightTeam teams = 2;
}
message L2C_GSTExchangeGroundTeam {
  uint32 ret = 1;
  repeated GSTFightTeam teams = 2;
}

// 获取自己所在组的排行信息
message C2L_GSTRank {}
message L2C_GSTRank {
  uint32 ret = 1;
  GSTLastLRoundGuildRank guilds = 2;   // 公会排名
  repeated GSTGuildUserBase mems = 3;  // 排名
}

message C2L_GSTGetTasksData {}
message L2C_GSTGetTasksData {
  uint32 ret = 1;
  GSTTaskInfo gst_task_info = 2;
}

message C2L_GSTGetTasksReward {
  repeated uint32 task_ids = 1;
}

message L2C_GSTGetTasksReward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

message C2L_GSTGetHangUpReward {}

message L2C_GSTGetHangUpReward {
  uint32 ret = 1;
  repeated Resource awards = 2;
  int64 last_hang_up_time = 3;
}

message C2L_GSTDonate {
  uint32 goddess_id = 1;
  repeated GSTClientDonate gst_client_donates = 2;
}

message GSTClientDonate {
  uint32 donate_type = 1;  // 1.物品 2.会长免费次数
  uint32 count = 2;
  uint32 value = 3;  // 物品ID
}

message L2C_GSTDonate {
  uint32 ret = 1;
  uint32 goddess_id = 2;
  repeated GSTClientDonate gst_client_donates = 3;
  GSTGoddessBless blessInfo = 4;
}

message C2L_GSTMessageEdit {
  string message = 1;
}
message L2C_GSTMessageEdit {
  uint32 ret = 1;
  string message = 2;
  uint64 message_id = 3;
}

message GSTSetFormationTeamInfo {
  repeated GSTTeamHeroInfo heros = 1;
  uint32 team_index = 2;  // 队伍下标
  uint32 skin_id = 3;     // 队伍第一个英雄的皮肤ID
  int64 power = 4;
}

message C2L_GSTPreviewHangUpReward {}

message L2C_GSTPreviewHangUpReward {
  uint32 ret = 1;
  repeated Resource awards = 2;
  uint32 max_ground_count = 3;
  int64 reward_duration = 4;
  repeated Resource dispatch_award = 5;
  repeated Resource win_battle_award = 6;
  uint32 dispatch_award_team_count = 7;
  uint32 win_battle_award_team_count = 8;
  repeated Resource build_dispatch_award = 9;
  Resource ore_awards = 10;
}

message C2L_GSTGetGuildDonateData {}

message L2C_GSTGetGuildDonateData {
  uint32 ret = 1;
  repeated GSTSimpleGuild donate_rank = 2;
}

message C2L_GSTGetGuildDonateMemData {
  uint32 goddess_id = 1;
}

message L2C_GSTGetGuildDonateMemData {
  uint32 ret = 1;
  uint32 goddess_id = 2;
  repeated GSTGuildUserBase user_rank = 3;
}

message L2C_GSTTaskTypeUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
}

message L2C_GSTPushSta {
  GSTSta sta = 1;
}

message GstLogGeneral {
  uint64 guild_id = 1;
  uint32 season = 2;
  uint32 turn = 3;
  uint32 round = 4;
  uint64 room = 5;
  uint32 donate_point = 6;
  uint32 room_quality = 7;
}

message C2L_GSTGuildBuildGetData {}

message L2C_GSTGuildBuildGetData {
  uint32 ret = 1;
  repeated GSTBuild builds = 2;
  map<uint32, uint32> guild_build_dispatch = 3;
}

message C2L_GSTGuildBuildDonate {
  uint32 build_type = 1;
  repeated Resource cost = 2;
}

message L2C_GSTGuildBuildDonate {
  uint32 ret = 1;
  uint32 build_type = 2;
  repeated Resource cost = 3;
  GSTBuild build = 4;
}

message C2L_GSTGuildBuildDispatchHero {
  uint32 build_type = 1;
  repeated uint64 heroID = 2;
}

message L2C_GSTGuildBuildDispatchHero {
  uint32 ret = 1;
  map<uint32, GSTBuildUserDispatchHeroesClient> build_dispatch_hero = 2;
  uint32 build_type = 3;
}

message C2L_GSTGuildBuildDonateRank {}

message L2C_GSTGuildBuildDonateRank {
  uint32 ret = 1;
  repeated GSTBuildDonateRankClient rank = 2;
}

message C2L_GSTGuildBuildGetTaskData {}

message L2C_GSTGuildBuildGetTaskData {
  uint32 ret = 1;
  GSTBuildTaskInfo task = 2;
}

message C2L_GSTGuildBuildRecvTaskAward {
  repeated uint32 task_ids = 1;
}

message L2C_GSTGuildBuildRecvTaskAward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

message L2C_GSTGuildBuildTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
}

message C2L_GSTGroupUsersRank {
  uint32 rank_id = 1;  // 排行榜ID
}

message L2C_GSTGroupUsersRank {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated RankValue values = 3;  // 排行数据
  RankValue self_value = 4;
}

message C2L_GSTGroupUsersRankLike {
  uint32 rank_id = 1;
  uint64 id = 2;  // 玩家ID
}

message L2C_GSTGroupUsersRankLike {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 id = 3;
  uint32 liked_count = 4;
  repeated Resource awards = 5;
}

message C2L_GSTArenaVote {
  uint64 guild_id = 1;
  bool is_favour = 2;  // 是否赞成
}

message L2C_GSTArenaVote {
  uint32 ret = 1;
  uint64 guild_id = 2;
  GSTArenaVoteRecord record = 3;
  repeated Resource awards = 4;
}

message GSTArenaInfo {
  repeated GSTArenaState arena_states = 1;
  repeated GSTArenaGuildInfo guilds_info = 2;
}

message GSTArenaState {
  bool is_open = 1;   // 是否开启
  uint32 round = 2;   // 擂台轮次
  uint32 lround = 3;  // 开启回合
  GSTArenaFightRecord fight_record = 4;
}

message GSTArenaFightRecord {
  repeated GSTArenaGuildRank guild_rank = 1;     // 公会排名
  repeated GSTArenaGuildUserRank user_rank = 2;  // 成员排名
}

message GSTArenaGuildRank {
  GSTSimpleGuild guild = 1;
  uint32 wins = 2;
  uint32 miss_match = 3;  // 轮空场次
  uint32 team_num = 4;    // 队伍数量
}

message GSTArenaGuildUserRank {
  GSTGuildUserBase user = 1;
  uint32 wins = 2;
  uint32 miss_match = 3;
}

message GSTArenaGuildInfo {
  uint64 guild_id = 1;
  int32 favour_times = 2;                        // 赞成次数
  int32 oppose_times = 3;                        // 反对次数
  repeated GSTArenaVoteRecord vote_records = 4;  // 最新投票记录,固定条数
}
message GSTArenaVoteRecord {
  int64 time = 1;
  string guild_name = 2;
  string user_name = 3;
  bool is_favour = 4;
}

message C2L_GSTGetArenaVoteRecord {
  uint64 guild_id = 1;
}

message L2C_GSTGetArenaVoteRecord {
  uint32 ret = 1;
  uint64 guild_id = 2;
  GSTArenaGuildInfo record = 3;
}

message C2L_GSTBossGet {}
message L2C_GSTBossGet {
  uint32 ret = 1;
  GSTBossGroup boss_group = 2;              // 公会boss_group
  repeated GSTBossFightRank rank_list = 3;  // 前3名
  map<uint32, uint32> boss_award = 4;       // boss战奖励 index：0未领奖 1已领奖
  uint32 challenge_recover_time = 5;        // 挑战令恢复时间
}

message C2L_GSTBossFight {
  uint32 boss_id = 1;     // bossId
  bool sweep = 2;         // 是否扫荡
  bytes client_data = 3;  // 前端透传字段
}
message L2C_GSTBossFight {
  uint32 ret = 1;
  uint32 boss_id = 2;                 // bossId
  bool sweep = 3;                     // 是否扫荡
  string report_id = 4;               // 战报id
  uint64 damage = 5;                  // 伤害
  uint32 fight_hp = 6;                // 战斗血量
  GSTBoss boss = 7;                   // boss信息
  repeated cl.Resource awards = 8;    // 伤害奖励
  uint32 challenge_recover_time = 9;  // 挑战令恢复时间
  uint32 status = 10;                 // 战斗状态 0:初始 1:胜利 2:失败
  bool win = 11;
}

// 未领取奖励一次性领取
message C2L_GSTBossAward {}
message L2C_GSTBossAward {
  uint32 ret = 1;
  uint32 monster_group = 2;
  repeated uint32 award_index = 3;  // 奖励下标
  repeated cl.Resource awards = 4;  // 奖励
}

message C2L_GSTBossRank {}
message L2C_GSTBossRank {
  uint32 ret = 1;
  repeated GSTBossFightRank rank_list = 2;
}

// 购买挑战令
message C2L_GSTBossBuyChallenge {
  uint32 buy_type = 1;  // common.GST_BOSS_BUY_TYPE 1: 钻石购买  2: 道具购买
  uint32 buy_count = 2;
}
message L2C_GSTBossBuyChallenge {
  uint32 ret = 1;
  uint32 buy_type = 2;
  uint32 buy_count = 3;
  uint32 challenge_recover_time = 4;  // 挑战令恢复时间
}

message GSTBoss {
  uint32 boss_id = 1;
  uint32 residue_hp = 2;  // 剩余血量
}

message GSTBossGroup {
  uint32 status = 1;          // 战斗状态 0:初始 1:胜利 2:失败
  uint32 monster_group = 2;   // 怪物组
  uint32 monster_index = 3;   // 怪物index
  uint32 lround_live = 4;     // 存活回合数
  uint32 all_die_times = 5;   // 全体死亡次数
  repeated GSTBoss boss = 6;  // boss组
}

message GSTBossFightRank {
  GSTGuildUserBase base = 1;
  uint64 total_damage = 2;  // 累计伤害
  uint32 fight_times = 3;   // 战斗次数
}

enum GSTBossRecordType {
  RecordType_None = 0;
  RecordType_Come = 1;     // boss来袭
  RecordType_Hp = 2;       // 血量扣除
  RecordType_Award = 3;    // 瓜分奖励
  RecordType_Reborn = 4;   // 幻影重生
  RecordType_Timeout = 5;  // 超时失败
  RecordType_Damage = 6;   // 幻影伤害
}

message GSTBossFightRecord {
  GSTBossRecordType record_type = 1;
  string user_name = 2;
  uint32 monster_index = 3;  // 第几波怪物
  uint32 boss_id = 4;
  uint64 damge = 5;  // 伤害
  uint32 hp = 6;     // 扣除血量
}

message GSTBossUser {
  uint32 challenge_recover_time = 1;  // 挑战令恢复时间
}

// 公会养龙数据
message GSTDragonCultivation {
  map<uint32, uint32> evolution = 1;  // 进化数据 pos => dragon_id
  uint32 level = 2;                   // 等级
  uint32 show_pos = 3;                // 展示位置
}

// 公会龙战数据
message GSTDragonGuild {
  GSTDragonBattle battle = 2;                    // 本场龙战数据
  GSTDragonBattleResult last_battle_result = 3;  // 上场龙战结果
}

message GSTDragonUser {
  uint32 total_count = 1;  // 剩余挑战次数
  uint32 buy_count = 2;    // 钻石已购买挑战次数
}

message GSTDragonSkill {
  uint32 target_ground_id = 1;  // 攻城技能目标地块
  int64 op_time = 2;            // 操作时间
  uint32 skill_power = 3;       // 攻城技能能量
}

message GSTDragonBattle {
  GSTSimpleGuild opponent_guild = 1;               // 敌方公会
  repeated GSTDragonBattleData own_data = 2;       // 我方战斗数据
  repeated GSTDragonBattleData opponent_data = 3;  // 敌方战斗数据
}

message GSTDragonBattleData {
  uint32 pos = 1;              // 龙位置
  uint32 dragon_id = 2;        // 龙id
  uint32 dragon_level = 3;     // 龙等级
  bool is_show = 4;            // 是否为展示龙
  uint64 damage = 5;           // 被敌方攻击伤害
  uint32 hp_slot = 6;          // 血条
  uint32 left_hp = 7;          // 剩余hp
  uint64 user_max_damage = 8;  // 玩家单次最高伤害
}

message GSTDragonBattleResult {
  uint32 dragon_round = 1;             // 龙战场次
  GSTSimpleGuild own_guild = 2;        // 我方公会
  GSTSimpleGuild opponent_guild = 3;   // 敌方公会
  uint32 own_guild_progress = 4;       // 我方公会进度
  uint32 opponent_guild_progress = 5;  // 敌方公会进度
  bool is_win = 6;
}

enum GSTDragonFightRecordType {
  DFRT_None = 0;
  DFRT_User_Attack = 1;  // 玩家进攻龙
  DFRT_User_Kill = 2;    // 玩家击杀龙
}

message GSTDragonFightRecord {
  GSTDragonFightRecordType record_type = 1;              // 记录类型
  string attack_name = 2;                                // 攻击方名字
  uint64 attack_uid = 3;                                 // 攻击方uid
  string attack_guild_name = 4;                          // 攻击方公会名
  uint32 defense_dragon_id = 5;                          // 防守方龙id
  uint32 defense_dragon_level = 6;                       // 防守方龙等级
  uint32 dragon_round = 7;                               // 龙战场次
  uint64 damage = 8;                                     // 伤害
  uint32 reduce_hp = 9;                                  // 扣血
  bool is_bot = 10;                                      // 是否是机器人
  uint32 add_progress = 11;                              // 增加的总进度（万分比）
  repeated GSTDragonFightRecordTeamData team_data = 12;  // 各队数据
}

message GSTDragonFightRecordTeamData {
  uint64 team_damage = 1;               // 队伍伤害
  GSTDragonUserTeamData team_data = 2;  // 队伍数据
}

message GSTDragonFightRank {
  GSTGuildUserBase base = 1;
  uint64 total_damage = 2;  // 累计伤害
  uint32 fight_times = 3;   // 战斗次数
  bool is_bot = 4;          // 是否机器人
}

// 龙战基础数据
message C2L_GSTDragonGetData {}

message L2C_GSTDragonGetData {
  uint32 ret = 1;
  uint32 battle_turn = 2;                     // 当前龙战场次
  GSTDragonGuild dragon_guild = 3;            // 公会龙战数据
  GSTDragonUser dragon_user = 4;              // 玩家龙战数据
  GSTDragonSkill skill = 5;                   // 龙战技能
  repeated GSTDragonFightRank rank_list = 6;  // 公会内总伤害前3名
  uint32 strategy_skill_num = 7;              // 祝福技能能使用次数
}

message C2L_GSTDragonGetCultivation {}

message L2C_GSTDragonGetCultivation {
  uint32 ret = 1;
  GSTDragonCultivation dragon_guild = 2;
}

// 选择展示的龙
message C2L_GSTDragonShow {
  uint32 dragon_pos = 1;  // 选择第几条龙
}

message L2C_GSTDragonShow {
  uint32 ret = 1;
  uint32 dragon_pos = 2;
  uint32 dragon_id = 3;
}

// 龙进化
message C2L_GSTDragonEvolve {
  uint32 dragon_pos = 1;  // 第几条龙需要进化
  uint32 evolve_id = 2;   // 目标龙id
}

message L2C_GSTDragonEvolve {
  uint32 ret = 1;
  uint32 dragon_pos = 2;
  uint32 evolve_id = 3;
}

// 龙战战斗
message C2L_GSTDragonFight {
  uint32 dragon_pos = 1;
  uint32 dragon_id = 2;
  bool sweep = 3;
  bytes client_data = 4;  // 前端透传字段
}

message L2C_GSTDragonFight {
  uint32 ret = 1;
  uint32 dragon_pos = 2;
  uint32 dragon_id = 3;
  bool sweep = 4;
  string report_id = 5;
  uint64 fight_damage = 6;     // 总伤害
  uint32 reduce_hp = 7;        // 掉血量
  uint32 add_progress = 8;     // 总进度增加值
  GSTDragonBattle battle = 9;  // 本场龙战数据
  repeated Resource awards = 10;
  repeated GSTDragonFightRank rank_list = 11;            // 公会内总伤害前3名
  uint32 total_count = 12;                               // 剩余挑战次数
  repeated GSTDragonFightRecordTeamData team_data = 13;  // 队伍数据
}

// 钻石购买战斗次数
message C2L_GSTDragonFightBuyCount {
  uint32 count = 1;
}

message L2C_GSTDragonFightBuyCount {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 total_count = 3;
}

// 道具购买战斗次数
message C2L_GSTDragonFightUseTicket {
  uint32 count = 1;
}

message L2C_GSTDragonFightUseTicket {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 total_count = 3;
}

// 龙攻城技能操作
message C2L_GSTDragonSkillOperate {
  uint32 ground_id = 1;  // 操作地块id
}

message L2C_GSTDragonSkillOperate {
  uint32 ret = 1;
  uint32 ground_id = 2;
}

// 使用祝福技能
message C2L_GSTDragonStrategySkill {
  uint32 count = 1;
}

message L2C_GSTDragonStrategySkill {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 left_count = 3;
}

// 任务信息
message C2L_GSTDragonTaskGetData {}

message L2C_GSTDragonTaskGetData {
  uint32 ret = 1;
  GSTDragonTaskInfo task = 2;
}

// 任务领奖
message C2L_GSTDragonTaskAward {
  repeated uint32 task_ids = 1;
}

message L2C_GSTDragonTaskAward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

message L2C_GSTDragonTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
}

message C2L_GSTDragonRank {}

message L2C_GSTDragonRank {
  uint32 ret = 1;
  repeated GSTDragonFightRank rank_list = 2;
}

message GSTGuildUserOre {
  repeated GSTGuildUserOreData ore_datas = 1;  // 所有矿数据(包括专属矿)
  GSTGuildUserOreTimes fight_times = 2;        // 挑战次数
  GSTGuildUserOreTimes assist_times = 3;       // 协助次数
  Resource res = 4;                            // 待领取奖励
  map<uint32, uint32> max_progress = 5;        // 战斗最大进度  monster=>
}

message GSTGuildUserOreTimes {
  int32 can_times = 1;   // 能挑战的次数
  int64 fresh_time = 2;  // 随时间刷新次数的时间
}

message GSTGuildUserOreData {
  uint32 id = 1;                                // 矿点id
  uint32 progress = 2;                          // 占领进度
  bool is_occupy = 3;                           // 是否占领了
  repeated GSTAssistOreData assisted_data = 4;  // 被协助的数据
  uint32 ground_id = 5;                         // 所属地块
  bool is_exclusive = 6;                        // 是否是专属矿
  int64 gen_time = 7;
}

message GSTAssistOreData {
  GSTGuildUserBase user = 1;
  uint32 progress = 2;
}

message GSTGuildTech {
  repeated GSTTechData techs = 1;
  uint32 sign_id = 2;       // 标记优先的id
  uint32 skill_times = 3;   // 集结技能可使用次数
  uint32 donate_total = 4;  // 捐献总数
}

message GSTTechData {
  uint32 id = 1;
  uint32 level = 2;
  uint32 donate = 3;
}

message GSTGuildUserTech {
  GSTTechTaskData task = 1;
  uint32 donate = 2;  // 捐献的资源
  int64 donate_time = 3;
  repeated Resource awards = 4;  // 科技节点奖励
}

message GSTTechTaskData {
  map<uint32, TaskTypeProgress> task_type_progress = 1;
  map<uint32, bool> awarded = 2;
}

message C2L_GSTOreGetData {}
message L2C_GSTOreGetData {
  uint32 ret = 1;
  GSTGuildUserOre data = 2;
}

message C2L_GSTOreFight {
  bool is_assist = 1;
  uint64 uid = 2;  // 协助矿拥有者
  uint32 ground_id = 3;
  uint32 ore_id = 4;
  bool sweep = 5;         // 是否扫荡
  bytes client_data = 6;  // 前端透传字段
}
message L2C_GSTOreFight {
  uint32 ret = 1;
  bool is_assist = 2;
  uint64 uid = 3;
  uint32 ground_id = 4;
  uint32 ore_id = 5;
  bool sweep = 6;
  string report_id = 7;
  uint32 add_progress = 8;
  GSTGuildUserOreData ore = 9;  // 更新后的矿数据
  repeated Resource awards = 10;
  uint64 damage = 11;
  int32 can_use_times = 12;
}

message C2L_GSTOreBuyTimes {
  uint32 type = 1;  // 1 挑战 2 协助
  uint32 buy_count = 2;
}
message L2C_GSTOreBuyTimes {
  uint32 ret = 1;
  uint32 type = 2;
  uint32 buy_count = 3;
  int32 can_use_times = 4;  // 买完之后的值
}

message C2L_GSTOreSearchAssist {}
message L2C_GSTOreSearchAssist {
  uint32 ret = 1;
  repeated GSTAssistOre ores = 2;
}

message GSTAssistOre {
  GSTGuildUserBase user = 1;
  GSTGuildUserOreData ore = 2;
}

message C2L_GSTOreOccupy {
  uint32 ore_id = 1;
  uint32 ground_id = 2;
  bool is_occupy = 3;  // 占领还是取消占领
}
message L2C_GSTOreOccupy {
  uint32 ret = 1;
  uint32 ore_id = 2;
  uint32 ground_id = 3;
  bool is_occupy = 4;
}

message C2L_GSTOreGetOreData {
  uint64 uid = 1;
  uint32 ore_id = 2;
  uint32 ground_id = 3;
}
message L2C_GSTOreGetOreData {
  uint32 ret = 1;
  uint64 uid = 2;
  uint32 ore_id = 3;
  uint32 ground_id = 4;
  GSTGuildUserOreData ore = 5;
}

message C2L_GSTTechGetData {}
message L2C_GSTTechGetData {
  uint32 ret = 1;
  GSTGuildTech guild_tech = 2;
  GSTGuildUserTech user_tech = 3;
}

message C2L_GSTTechDonate {
  uint32 tech_id = 1;
  uint32 donate_num = 2;
}
message L2C_GSTTechDonate {
  uint32 ret = 1;
  GSTTechData tech = 2;
  uint32 donate_num = 3;
  uint32 cost_num = 4;
  uint32 donate_total = 5;  // 公会被捐总数
  repeated Resource awards = 6;
  uint32 skill_times = 7;
}

message C2L_GSTTechTaskReward {
  repeated uint32 task_ids = 1;
}

message L2C_GSTTechTaskReward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

message L2C_GSTTechTaskUpdate {
  map<uint32, TaskTypeProgress> task_type_progress = 1;
}

message C2L_GSTTechGuildUserRank {}
message L2C_GSTTechGuildUserRank {
  uint32 ret = 1;
  repeated GSTTechRankData ranks = 2;  // 前端排序
}
message GSTTechRankData {
  GSTGuildUserBase user = 1;
  uint32 donate = 2;
  int64 time = 3;
}

message C2L_GSTSkillAssemble {
  uint32 ground_id = 1;
}
message L2C_GSTSkillAssemble {
  uint32 ret = 1;
  uint32 ground_id = 2;
  uint32 left_times = 3;
}

message C2L_GSTTechSign {
  uint32 tech_id = 1;
}
message L2C_GSTTechSign {
  uint32 ret = 1;
  uint32 tech_id = 2;
}

// GSTEND---------------------------------------------------------------------------------

// 赛季羁绊数据
message SeasonLink {
  repeated uint32 activated_heroes = 1;       // 已激活羁绊的英雄id
  repeated SeasonLinkMonument monuments = 2;  // 羁绊丰碑数据
}

// 羁绊丰碑数据
//$<ROP redis|map|seasonlink_monument:uint64>
message SeasonLinkMonument {
  uint32 monument_sys_id = 1;              //$<ROP unique > 丰碑id
  uint32 unique_level = 2;                 // 玩法等级
  repeated MonumentRune runes = 3;         // 符石列表
  repeated uint32 awarded_unique_ids = 4;  // 已领奖的id
}

// 羁绊符石数据
message MonumentRune {
  uint32 pos = 1;          // 位置编号（1-6）
  uint32 rune_sys_id = 2;  // 符石Id（关联量表seasonlink_rune_info的Id字段）
}

// 赛季羁绊获取数据
message C2L_SeasonLinkGetData {}

message L2C_SeasonLinkGetData {
  uint32 ret = 1;
  SeasonLink data = 2;
}

// 赛季羁绊激活
message C2L_SeasonLinkActivate {
  uint32 hero_sys_id = 1;
}

message L2C_SeasonLinkActivate {
  uint32 ret = 1;
  uint32 hero_sys_id = 2;
  repeated uint32 activated_link = 3;
}

// 羁绊丰碑数据变化
message L2C_SeasonLinkMonumentChange {
  uint32 ret = 1;
  repeated SeasonLinkMonument monument = 2;
}

message C2L_SeasonLinkMonumentTakeRareAwards {
  uint32 monument_id = 1;
  repeated uint32 unique_ids = 2;  // 领奖id
}

message L2C_SeasonLinkMonumentTakeRareAwards {
  uint32 ret = 1;
  uint32 monument_id = 2;
  repeated uint32 awarded_ids = 3;  // 已领奖的id
  repeated Resource awards = 4;
}

// 剧情回忆录数据
message StoryReviewData {
  map<uint32, uint32> subplot_progress = 1;  // 已解锁的支线剧情 => 关卡进度
}

// 剧情回忆录获取数据
message C2L_StoryReviewGetData {}

message L2C_StoryReviewGetData {
  uint32 ret = 1;
  StoryReviewData data = 2;
}

// 剧情回忆录解锁
message C2L_StoryReviewUnlock {
  uint32 subplot_id = 1;
}

message L2C_StoryReviewUnlock {
  uint32 ret = 1;
  uint32 subplot_id = 2;  // 支线剧情id
  uint32 progress = 3;    // 支线剧情进度
}

// 设置推送标识
message C2L_SetPush {
  uint32 id = 1;  // 1: 密林花坛被抢夺
  bool is_push = 2;
}

message L2C_SetPush {
  uint32 ret = 1;
  uint32 id = 2;
  bool is_push = 3;
}

message C2L_GetPush {}

message L2C_GetPush {
  uint32 ret = 1;
  map<uint32, bool> push_setting = 2;
}

message NewYearActivity {
  uint32 sys_id = 1;                  // 活动系统ID
  uint32 continue_login_day = 2;      // 持续登录时间
  map<uint32, bool> login_award = 3;  // 登录奖励领取状态
}

message C2L_NewYearActivityGetData {}

message L2C_NewYearActivityGetData {
  uint32 ret = 1;
  NewYearActivity data = 2;
}

message C2L_NewYearActivityLoginAward {
  uint32 act_id = 1;
  repeated uint32 login_ids = 2;  // 登录任务ID
}

message L2C_NewYearActivityLoginAward {
  uint32 ret = 1;
  uint32 act_id = 2;
  repeated uint32 login_ids = 3;  // 登录任务ID
  repeated Resource awards = 4;   // 兑换得到的奖励
}

// 金字塔活动 - 配置数据
message PyramidActivityBase {
  uint32 id = 1;           // 活动唯一id，由gm生成
  uint32 activity_id = 2;  // pyramid_activity_info量表id
  int64 start_time = 3;    // 开始时间
  int64 end_time = 4;      // 结束时间
  uint32 op_status = 5;    // 发布状态 1-发布 2-下架
}

message Pyramid {
  uint32 period_id = 1;                             // 活动期数
  uint32 round = 2;                                 // 奖池轮次
  uint32 floor = 3;                                 // 当前层数
  uint32 period_num = 4;                            // 本期活动已抽奖次数
  map<uint32, Lattice> lattices = 5;                // lattice_id => data 格位数据（仅包含本轮已抽取过的格子）
  map<uint32, uint32> choose_draw_num = 6;          // choose_id => draw_num 自选奖励本期已抽取次数
  map<uint32, TaskTypeProgress> task_progress = 7;  // 任务进度
  map<uint32, bool> received_tasks = 8;             // 任务奖励领取情况
  bool is_delete_items = 9;                         // 当期活动是否清空道具
}

// 格位信息
message Lattice {
  uint32 lattice_id = 1;  // 格位id
  uint32 choose_id = 2;   // 自选奖励
  uint32 draw_num = 3;    // 被抽取次数
}

message C2L_PyramidGetData {}

message L2C_PyramidGetData {
  uint32 ret = 1;
  PyramidActivityBase config = 2;  // 活动配置
  Pyramid data = 3;                // 活动数据
}

// 自选奖励
message C2L_PyramidChooseAward {
  uint32 lattice_id = 1;  // 格位id
  uint32 choose_id = 2;   // 自选奖励
}

message L2C_PyramidChooseAward {
  uint32 ret = 1;
  uint32 lattice_id = 2;  // 格位id
  uint32 choose_id = 3;   // 自选奖励
}

// 抽奖(单次，不支持多次抽奖)
message C2L_PyramidDraw {}

message L2C_PyramidDraw {
  uint32 ret = 1;
  repeated Resource awards = 2;
  Pyramid data = 3;  // 抽奖后状态
}

// 领取任务奖励
message C2L_PyramidReceiveAwards {
  repeated uint32 task_ids = 1;
}

message L2C_PyramidReceiveAwards {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
  Pyramid pyramid = 4;
}

// 同步任务进度
message L2C_PyramidUpdateTask {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

// 同步活动配置
message L2C_PyramidUpdateActivity {
  uint32 ret = 1;
  PyramidActivityBase config = 2;
}

// 抽卡测试
message C2L_PyramidTestDraw {
  map<uint32, uint32> lattice_weight = 1;  // 格位权重配置 lattice_id => weight
  uint32 draw_rounds = 2;                  // 抽取轮数(需要抽到多少次大奖)
}

message PyramidTestResult {
  uint32 draw_big_prize_need_num = 1;  // 抽中大奖所需的抽奖次数
  uint32 sample_num = 2;               // 该抽奖次数的样本数
}

message L2C_PyramidTestDraw {
  uint32 ret = 1;
  repeated PyramidTestResult test_result = 2;  // 测试结果 (总共10条)
}

message C2L_GetSeasonFlashBackData {
  uint32 season_id = 1;  //
}

message L2C_GetSeasonFlashBackData {
  uint32 ret = 1;
  uint32 season_id = 2;
  repeated uint32 ids = 3;
  repeated SeasonUserLogPoint points = 4;
}

message C2L_GetDefFormationPower {
  repeated FormationSidUid formation_sid_uid = 1;
}

message L2C_GetDefFormationPower {
  uint32 ret = 1;
  repeated FormationSidUidPower formation_sid_uid_powers = 2;
}

message FormationSidUid {
  uint32 formation_id = 1;
  uint64 sid = 2;
  uint64 uid = 3;
}

message FormationSidUidPower {
  FormationSidUid formation_sid_uid = 1;
  int64 def_power = 2;
}

// 通用排行榜点赞
message C2L_CommonRankLike {
  uint32 rank_id = 1;
  uint64 uid = 2;
}

message L2C_CommonRankLike {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 uid = 3;
  repeated Resource awards = 4;
  uint32 like_count = 5;
}

message C2L_TestEtcdGiftCode {
  uint32 code_id = 1;
}

message L2C_TestEtcdGiftCode {
  uint32 ret = 1;
  uint32 code_id = 2;
  repeated Resource awards = 3;
}

// 定义 Remain struct 便于后期扩展，比如遗物养成等
//$<ROP redis|map|r:uint64 >
message Remain {
  uint32 id = 1;              //$<ROP unique > // 遗物 id
  uint32 star = 2;            // 星级
  uint32 star_recv = 3;       // 接收星级
  uint32 fragment_count = 4;  // 碎片数量
}

message RemainBook {
  uint32 level = 1;
  uint32 exp = 2;
}

message RemainItem {
  map<uint32, uint32> use_items = 1;
}

message C2L_RemainGetData {}
message L2C_RemainGetData {
  uint32 ret = 1;
  repeated Remain remains = 2;
  RemainItem item_info = 3;
  RemainBook remain_book = 4;
}

message L2C_RemainUseItemsNotify {
  uint32 ret = 1;
  RemainItem item_info = 2;  // 返回所有已使用的道具
}

message C2L_RemainBookRecvExp {
  uint32 remain_id = 1;
  uint32 recv_star = 2;
}

message L2C_RemainBookRecvExp {
  uint32 ret = 1;
  uint32 remain_id = 2;
  uint32 recv_star = 3;
  Remain remain = 4;
  RemainBook remain_book = 5;
}

message C2L_RemainBookLevelUp {
  uint32 target_level = 1;
}

message L2C_RemainBookLevelUp {
  uint32 ret = 1;
  uint32 target_level = 2;
  RemainBook remain_book = 3;
  repeated Resource awards = 4;  // 奖励
}

message L2C_RemainUpdate {
  uint32 ret = 1;
  repeated Remain remains = 2;
}

message SeasonAddInfo {
  uint32 type = 1;  // 1: 英雄星级  2: 神器星级  3: 英雄符文专属技能
  uint32 sys_id = 2;
  uint32 star = 3;
  uint32 emblem_exclusive_lv = 4;  // 符文专属技能等级
}

message C2L_OperateActivityPromotionSelectAward {
  uint32 gift_id = 1;
  uint32 award_index = 2;   // 礼包的第几个奖励
  uint32 target_index = 3;  // 选择奖励中的第几个物品
}

message L2C_OperateActivityPromotionSelectAward {
  uint32 ret = 1;
  uint32 gift_id = 2;
  uint32 award_index = 3;   // 礼包的第几个奖励
  uint32 target_index = 4;  // 选择奖励中的第几个物品
}

// 新推送礼包充值前的检查
message C2L_OperateActivityPromotionRechargeCheck {
  uint32 gift_id = 1;  // 礼包ID
}

message L2C_OperateActivityPromotionRechargeCheck {
  uint32 ret = 1;
  uint32 gift_id = 2;
  repeated Resource awards = 3;      // 免费礼包会有，免费礼包在check时会顺便领取奖励
  PromotionGift promotion_gift = 4;  //
}

message C2L_SeasonArenaGetData {
  bool cross = 1;
}

message L2C_SeasonArenaGetData {
  uint32 ret = 1;
  bool cross = 2;
  SeasonArenaData season_arena_data = 3;
  uint32 logic_partition = 4;  // 服务器当前小战区ID
}

message C2L_SeasonArenaRefresh {
  bool free = 1;
}

message L2C_SeasonArenaRefresh {
  uint32 ret = 1;
  int64 refresh_tm = 2;
  repeated SeasonArenaOpponentClient opponents = 3;  // 对手列表
  bool free = 4;
}

// 竞技场 - 战斗
message C2L_SeasonArenaFight {
  uint64 id = 1;        // 对手玩家id
  uint32 division = 2;  // 对手玩家段位
  bytes client_data = 3;
  uint64 sid = 4;  // 服务器ID
}

message L2C_SeasonArenaFight {
  uint32 ret = 1;
  uint64 id = 2;                                    // 对手玩家id
  bool win = 3;                                     // 是否胜利
  string report_id = 4;                             // 战报id
  SeasonArenaUserScoreAndRank self_change = 5;      // 玩家，战斗前后分数与排名
  SeasonArenaUserScoreAndRank opponent_change = 6;  // 对手，战斗前后分数与排名
  bytes client_data = 7;
  bool skip_formation = 8;  // 跳过布阵标记位
}

message SeasonArenaUserScoreAndRank {
  uint32 new_score = 1;  // 战斗前，分数
  uint32 old_score = 2;  // 战斗后，分数
  uint32 new_rank = 3;   // 战斗前，排名
  uint32 old_rank = 4;   // 战斗后，排名
}

// 竞技场 - 战报信息
message C2L_SeasonArenaLogList {}

// 竞技场战报数据
//$<ROP redis|map|season_arena_log:uint64 >
message SeasonArenaLog {
  uint32 id = 1;                  //$<ROP unique> //日志循环队列id
  MiniUserSnapshot attacker = 2;  // 进攻方
  MiniUserSnapshot defender = 3;  // 防守方
  string report_id = 4;           // 战报id
  int64 tm = 5;                   // 记录的时间
  int32 score_change = 6;         // 积分变化
  bool win = 7;                   // attacker是否胜利
  bool bot = 8;                   // defender是否是机器人
  uint64 log_id = 9;              // 日志唯一id
  uint32 team_count = 10;         // 进攻方队伍数量
}

message L2C_SeasonArenaLogList {
  uint32 ret = 1;
  repeated SeasonArenaLog logs = 2;
}

message SeasonArenaLogs {
  repeated SeasonArenaLog logs = 1;
}

message SeasonArenaLogDeletes {
  repeated uint32 ids = 2;
}

// 荣耀殿堂数据
message C2L_SeasonArenaOfFame {
  bool simple = 1;
}

message L2C_SeasonArenaOfFame {
  uint32 ret = 1;
  repeated cl.RankValue list = 2;
  uint32 self_rank = 3;
  cl.RankValue self_value = 4;  // 自己的排名数据
  bool simple = 5;
}

// 排行榜数据
message C2L_SeasonArenaGetRankList {}

message L2C_SeasonArenaGetRankList {
  uint32 ret = 1;
  repeated RankValue list = 2;
  uint32 self_rank = 3;
  RankValue self_value = 4;  // 自己的排名数据
}

message SeasonArenaTask {
  map<uint32, TaskTypeProgress> task_progress = 1;  // 任务进度
  map<uint32, bool> receive_awarded = 2;            // 已领取奖励的任务
}

message SeasonArenaOpponentClient {
  uint32 score = 1;  // 积分
  uint32 rank = 2;   // 排名
  UserSnapshot snapshot = 3;
  bool bot = 4;  // 是否是机器人
}

message SeasonArenaData {
  SeasonArenaSta sta = 1;
  SeasonArenaUserBase user = 2;
  repeated SeasonArenaOpponentClient users = 3;  // 对手信息
}

message SeasonArenaUserBase {
  uint32 score = 1;
  uint32 rank = 2;
  uint32 last_round_score = 3;
  uint32 last_round_rank = 4;
  uint32 cur_round_division = 5;
  SeasonArenaTask arena_task = 6;
  uint32 daily_challenge_count = 7;
  bool free_refresh_flag = 8;
  repeated uint32 recv_division_award = 9;
  bool new_fight_flag = 10;
  uint32 cur_round_top_division = 11;
  bool formation_skip = 12;
}

// 0本赛季功能未开启
// 1玩法开启正常攻打
// 2玩法结算期
// 3下发奖励
// 4结算完成
// 0->1->2->3->4->1->2->3->4->0

message SeasonArenaSta {
  uint32 season = 1;
  uint32 round = 2;
  uint32 sta = 3;
}

message C2L_SeasonArenaDivisionAward {
  repeated uint32 rev_division = 1;
}

message L2C_SeasonArenaDivisionAward {
  uint32 ret = 1;
  repeated uint32 rev_division = 2;
  repeated Resource award = 3;
}

message C2L_SeasonArenaTaskAward {
  repeated uint32 task_ids = 1;
}

message L2C_SeasonArenaTaskAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  repeated Resource awards = 3;
}

message L2C_SeasonArenaState {
  uint32 ret = 1;
  SeasonArenaSta sta = 2;
}

message C2L_SeasonArenaGetTaskData {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_progress = 3;  // 任务进度
}

message L2C_SeasonArenaTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_progress = 2;
}

message C2L_SeasonArenaBuyChallengeCount {
  uint32 count = 1;
}

message L2C_SeasonArenaBuyChallengeCount {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 challenge_count = 3;
}

message C2L_SeasonArenaUseTicket {
  uint32 count = 1;
}

message L2C_SeasonArenaUseTicket {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 challenge_count = 3;
}

message GuildMedalSeasonArenaBak {
  uint64 uid = 1;
  uint32 rank = 2;
  uint32 division = 3;
  uint32 score = 4;
}

message BoxOpenInfo {
  uint32 box_id = 1;   // 箱子表id
  uint32 item_id = 2;  // 道具表id
  uint32 count = 3;
}

// 获取开宝箱数据 NOLOG
message C2L_BoxGet {}
message L2C_BoxGet {
  uint32 ret = 1;
  int32 point = 2;     // 分值
  uint32 cur_box = 3;  // 当前箱子id
}

// 箱子开启
message C2L_BoxOpen {
  repeated BoxOpenInfo boxes = 1;
}
message L2C_BoxOpen {
  uint32 ret = 1;
  int32 point = 2;  // 分值
  repeated Resource awards = 3;
}

// 箱子兑换
message C2L_BoxExchange {}
message L2C_BoxExchange {
  uint32 ret = 1;
  int32 point = 2;     // 分值
  uint32 cur_box = 3;  // 当前箱子id
  repeated Resource awards = 4;
}

// 链式任务
message LineTask {
  map<uint32, TaskTypeProgress> task_type_progress = 1;  // 不同的任务进度
  uint32 award_id = 2;                                   // 当前领取的奖励id
  bool trigger_last = 3;                                 // 触发领取了最后一条任务
}

message ActivityTurnTable {
  uint32 sys_id = 1;                                     // 活动系统ID
  map<uint32, TaskTypeProgress> task_type_progress = 2;  // 任务进度
  map<uint32, bool> recv_task = 3;                       // 任务领奖状态
  repeated uint32 big_award_id = 4;                      // 不放回的大奖ID
  map<uint32, uint32> buff_ids = 5;                      // 抽奖BUFF
  uint32 continue_login_day = 6;                         // 持续登录时间
  map<uint32, bool> login_award = 7;                     // 登录奖励领取状态
  repeated uint32 select_buffs = 8;                      // 待选择buff
  uint32 guarantee_count = 9;                            // 抽奖次数
  repeated ActivityExchange activity_exchange = 10;      // 交换道具组
}

message ActivityPuzzle {
  uint32 sys_id = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;  // 任务进度
  map<uint32, bool> recv_task = 3;                       // 任务领奖状态
  uint32 copy_id = 4;
  repeated PuzzleCell cells = 5;                    // 状态
  uint32 continue_login_day = 6;                    // 持续登录时间
  map<uint32, bool> login_award = 7;                // 登录奖励领取状态
  uint32 puzzle_id = 8;                             // 组ID
  repeated ActivityExchange activity_exchange = 9;  // 交换道具组
}

message ActivityDrop {
  uint32 sys_id = 1;
  uint32 continue_login_day = 2;                    // 持续登录时间
  map<uint32, bool> login_award = 3;                // 登录奖励领取状态
  repeated ActivityExchange activity_exchange = 4;  // 交换道具组
}

message ActivityFeed {
  uint32 sys_id = 1;
  uint32 continue_login_day = 2;                         // 持续登录时间
  map<uint32, bool> login_award = 3;                     // 登录奖励领取状态
  map<uint32, TaskTypeProgress> task_type_progress = 4;  // 任务进度
  map<uint32, bool> recv_task = 5;                       // 任务领奖状态
  uint32 feed_level = 6;
  uint32 feed_exp = 7;
  uint32 feed_cond = 8;
  repeated ActivityExchange activity_exchange = 9;  // 交换道具组
  uint32 feed_count = 10;
}

message ActivityShoot {
  uint32 sys_id = 1;
  uint32 continue_login_day = 2;                    // 持续登录时间
  map<uint32, bool> login_award = 3;                // 登录奖励领取状态
  repeated ActivityExchange activity_exchange = 4;  // 交换道具组
  uint32 stage_id = 5;                              // 关卡ID
}

message ActivityCelebration {
  uint32 sys_id = 1;
  uint32 continue_login_day = 2;                         // 持续登录时间
  map<uint32, bool> login_award = 3;                     // 登录奖励领取状态
  repeated ActivityExchange activity_exchange = 4;       // 交换道具组
  map<uint32, TaskTypeProgress> task_type_progress = 5;  // 任务进度
  map<uint32, bool> recv_task = 6;                       // 任务领奖状态
  uint32 total_score = 7;                                // 总积分
  uint32 max_score = 8;                                  // 历史最高积分
}

message PuzzleCell {
  uint32 x = 1;
  uint32 y = 2;
}

message ActivitySubTask {
  map<uint32, TaskTypeProgress> task_type_progress = 1;  // 任务进度
  map<uint32, bool> recv_task = 2;                       // 任务领奖状态
}

message ActivitySubContinueLogin {
  uint32 continue_login_day = 1;      // 持续登录时间
  map<uint32, bool> login_award = 2;  // 登录奖励领取状态
}

message ActivitySubTurnTable {
  repeated uint32 big_award_id = 1;  // 不放回的大奖ID
  map<uint32, uint32> buff_ids = 2;  // 抽奖BUFF
  repeated uint32 select_buffs = 3;  // 待选择buff
  uint32 guarantee_count = 4;        // 抽奖次数
}

message ActivitySubPuzzle {
  uint32 copy_id = 1;
  repeated PuzzleCell cells = 2;  // 状态
  uint32 puzzle_id = 3;           // 组ID
}

message ActivitySubExchange {
  repeated ActivityExchange exchange = 1;
}

message ActivitySubShoot {
  uint32 stage_id = 1;
}

message ActivitySubFeed {
  uint32 feed_level = 1;
  uint32 feed_exp = 2;
  uint32 feed_cond = 3;
  uint32 feed_count = 4;
}

// 合成小游戏
message ActivitySubSynthesis {
  uint32 total_score = 1;                      // 总积分
  uint32 max_score = 2;                        // 历史最高积分
  uint32 max_synthesis_level = 3;              // 历史最高合成等级
  uint32 drop_count = 4;                       // 投掷次数
  map<uint32, uint32> item_daily_buy_num = 5;  // item_id => 当日已购买次数
  bool game_open = 6;                          // 游戏是否开启
}

//$<ROP redis|map|activity_sum:uint64 >
message ActivitySum {
  uint32 activity_type = 1;  //$<ROP unique>
  uint32 sys_id = 2;
  ActivitySubTask task = 3;
  ActivitySubContinueLogin login = 4;
  ActivitySubTurnTable turn_table = 5;
  ActivitySubPuzzle puzzle = 6;
  ActivitySubExchange exchange = 7;
  ActivityDrop drop = 8;
  ActivitySubFeed feed = 9;
  ActivitySubShoot shoot = 10;
  ActivitySubSynthesis synthesis = 11;
}

message ActivitySumClient {
  uint32 activity_type = 1;
  uint32 sys_id = 2;
  ActivityTurnTable turn_table = 3;
  ActivityPuzzle activity_puzzle = 4;
  ActivityDrop drop = 5;
  ActivityFeed feed = 6;
  ActivityShoot shoot = 7;
  ActivityCelebration celebration = 8;  // 周年庆活动
}

message C2L_ActivityTurnTableGetData {}

message L2C_ActivityTurnTableGetData {
  uint32 ret = 1;
  ActivityTurnTable activity_turn_table = 2;
}

message C2L_ActivityTurnTableSummon {
  uint32 buff = 1;
}

message L2C_ActivityTurnTableSummon {
  uint32 ret = 1;
  uint32 buff = 2;
  repeated Resource awards = 3;
  repeated uint32 big_award_id = 4;
  uint32 group_id = 5;
  map<uint32, uint32> buff_ids = 6;
  uint32 guarantee_count = 7;
}

message C2L_ActivityTurnTableRecvTask {
  repeated uint32 task_ids = 1;
}

message L2C_ActivityTurnTableRecvTask {
  uint32 ret = 1;
  repeated uint32 task_ids = 3;
  repeated Resource awards = 4;
}

message L2C_ActivityTurnTableUpdateTask {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

message C2L_ActivityTurnTableRecvLogin {
  repeated uint32 continue_login_days = 1;
}

message L2C_ActivityTurnTableRecvLogin {
  uint32 ret = 1;
  repeated uint32 continue_login_days = 2;
  repeated Resource awards = 3;
  map<uint32, bool> login_award = 4;
}

message C2L_ActivityTurnTableSelectBuff {
  uint32 buff_id = 1;
}

message L2C_ActivityTurnTableSelectBuff {
  uint32 ret = 1;
  uint32 buff_id = 2;
  map<uint32, uint32> buff_ids = 3;
}

message C2L_ActivityTurnTableTicketBuy {
  uint32 buy_count = 1;
}

message L2C_ActivityTurnTableTicketBuy {
  uint32 ret = 1;
  uint32 buy_count = 2;
  repeated Resource awards = 3;
}

message Empty {}

message C2L_GSTScorePreview {
  uint64 guild_id = 1;
}

message L2C_GSTScorePreview {
  uint32 ret = 1;
  uint64 guild_id = 2;
  uint32 score = 3;
}

message C2L_HotRankGet {
  uint32 rank_id = 1;
}

message L2C_HotRankGet {
  uint32 ret = 1;
  CultivateHotRank hot_rank = 2;
  int64 next_merge_time = 3;
}

message CultivateHotRank {
  repeated CultivateHeroData hero = 1;
  repeated CultivateArtifactData artifact = 2;
  repeated CultivateEmblemSkillData emblem_skill = 3;
  repeated CultivateFormationData formation = 4;
}

message CultivateHeroData {
  uint32 rank = 1;
  uint32 hot = 2;
  uint32 hero_id = 3;
}

message CultivateArtifactData {
  uint32 rank = 1;
  uint32 hot = 2;
  uint32 artifact_id = 3;
}

message CultivateEmblemSkillData {
  uint32 rank = 1;
  uint32 hot = 2;
  uint32 emblem_skill = 3;
  uint32 hero_id = 4;
}

message CultivateFormation {
  repeated uint32 heroes_sys_id = 1;
  repeated uint32 artifact_id = 2;
}

message CultivateFormationData {
  uint32 rank = 1;
  uint32 hot = 2;
  CultivateFormation formation = 3;
}

// 共享养成
message ShareGrowth {
  ShareHero hero = 1;                         // 英雄的共享属性
  map<uint32, ShareEquipment> equipment = 2;  // 装备的共享属性 位置=>具体属性
}

message ActivityCompliance {
  map<uint32, bool> recv_state = 1;
  bool reparation = 2;
  uint32 score = 3;
  repeated uint64 daily_like_users = 4;  // 每日点赞对象
}

message C2L_ActivityComplianceGetData {}

message L2C_ActivityComplianceGetData {
  uint32 ret = 1;
  ActivityCompliance data = 2;
  uint32 score = 3;
}

message C2L_ActivityComplianceRecvAward {
  uint32 id = 1;
}

message L2C_ActivityComplianceRecvAward {
  uint32 ret = 1;
  uint32 id = 2;
  repeated Resource awards = 3;
}

message L2C_ActivityComplianceScoreUpdate {
  uint32 score = 1;
}

message C2L_ActivityComplianceLikeRank {
  uint64 uid = 1;
}

message L2C_ActivityComplianceLikeRank {
  uint32 ret = 1;
  repeated uint64 daily_like_users = 2;  // 每日点赞对象
  repeated Resource awards = 3;
  uint32 like_count = 4;
  uint64 uid = 5;
}
enum GuildMobilizationTaskStatus {
  None = 0;
  Finished = 1;
  GetReward = 2;
}

message GuildMobilizationGuildTask {
  uint32 task_id = 1;
  repeated GuildMobilizationMemberTask members = 2;  // 接取的人
  bool is_high_priority = 3;                         // 是否高优先级
}

message GuildMobilizationMemberTask {
  uint64 uid = 1;
  int64 end_time = 2;  // 超时的时间点
  bool is_finish = 3;  // 是否完成
  string name = 4;     // 如果是自己就不会传
}

message C2L_GuildMobilizationGetData {}
message L2C_GuildMobilizationGetData {
  uint32 ret = 1;
  repeated GuildMobilizationGuildTask tasks = 2;
  uint32 score = 3;
  map<uint32, GuildMobilizationTaskStatus> score_levels = 4;  // 公会积分等级-玩家领取状态
  uint32 can_fresh_times = 5;                                 // 可刷新任务的次数
  uint32 bought_fresh_times = 6;                              // 刷新任务的已购买次数
  uint32 can_accept_task_times = 7;                           // 可接取任务的次数
  uint32 bought_task_times = 8;                               // 购买接取的次数
  map<uint32, GuildMobilizationTaskData> task_data = 9;       // 自己接取的任务
  MessageBoard message = 10;                                  // 公告
  uint32 user_score = 11;                                     // 玩家积分
  repeated uint32 received_ids = 12;                          // 玩家积分-已领奖积分id
}

message C2L_GuildMobilizationAcceptTask {
  uint32 task_id = 1;
}
message L2C_GuildMobilizationAcceptTask {
  uint32 ret = 1;
  uint32 task_id = 2;
  uint32 accept_task_times = 3;  // 可接取任务的次数
  GuildMobilizationMemberTask task = 4;
  GuildMobilizationTaskData task_data = 5;
}

message C2L_GuildMobilizationSignTask {
  uint32 task_id = 1;
  bool is_sign = 2;  // 加标记和撤销标记
}
message L2C_GuildMobilizationSignTask {
  uint32 ret = 1;
  uint32 task_id = 2;
  bool is_sign = 3;
}

message GuildMobilizationTaskData {
  int64 end_time = 1;
  bool finished = 2;
  TaskProgress task_progress = 3;
}

message GuildMobilizationTaskLog {
  string name = 1;
  uint32 task_id = 2;
  int64 gen_time = 3;
}
message C2L_GuildMobilizationFinishTaskLogs {}
message L2C_GuildMobilizationFinishTaskLogs {
  uint32 ret = 1;
  repeated GuildMobilizationTaskLog logs = 2;
}

message GuildMobilizationPersonalRank {
  uint64 uid = 1;
  string name = 2;
  uint32 base_id = 3;
  repeated int64 expire_time = 4;
  uint32 score = 5;
  uint64 sid = 6;
}
message C2L_GuildMobilizationPersonalRank {}
message L2C_GuildMobilizationPersonalRank {
  uint32 ret = 1;
  repeated GuildMobilizationPersonalRank ranks = 2;
}

message GuildMobilizationGuildRank {
  uint64 id = 1;
  string name = 2;
  uint32 level = 3;
  uint32 badge = 4;
  uint64 sid = 5;
  uint32 score = 6;
  int64 score_change_time = 7;
  repeated int64 expire_time = 8;
  uint32 partition = 9;  // 公会所在小分区
}
message C2L_GuildMobilizationGuildRank {
  uint32 rank_type = 1;  // common.GUILD_MOBILIZATION_RANK_TYPE
}
message L2C_GuildMobilizationGuildRank {
  uint32 ret = 1;
  uint32 rank_type = 2;
  repeated RankValue list = 3;
  RankValue self_value = 4;
}

message C2L_GuildMobilizationRecvScoreLevel {}
message L2C_GuildMobilizationRecvScoreLevel {
  uint32 ret = 1;
  map<uint32, GuildMobilizationTaskStatus> score_levels = 2;
  repeated Resource rewards = 3;
}

message C2L_GuildMobilizationFreshTask {
  uint32 task_id = 1;
}
message L2C_GuildMobilizationFreshTask {
  uint32 ret = 1;
  uint32 task_id = 2;
  GuildMobilizationGuildTask new_task = 3;
  uint32 can_fresh_times = 4;
}

message C2L_GuildMobilizationBuyTimes {
  uint32 type = 1;  // 1 刷新任务次数 2 个人接取任务次数
  uint32 buy_times = 2;
  uint32 need_diamond = 3;  // 前端传来做校验用
}
message L2C_GuildMobilizationBuyTimes {
  uint32 ret = 1;
  uint32 type = 2;
  uint32 need_diamond = 3;
  uint32 can_use_times = 4;
  uint32 had_bought_times = 5;
  uint32 buy_times = 6;
}

message C2L_GuildMobilizationScoreAward {
  repeated uint32 ids = 1;
}

message L2C_GuildMobilizationScoreAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  repeated Resource awards = 3;
  repeated uint32 received_ids = 4;  // 已领奖的id
}

message C2L_GuildMobilizationCancelTask {
  uint32 task_id = 1;
  uint64 uid = 2;
}

message L2C_GuildMobilizationCancelTask {
  uint32 ret = 1;
  uint32 task_id = 2;
  uint64 uid = 3;
  GuildMobilizationGuildTask task = 4;
}

message L2C_GuildMobilizationUpdateTaskProgress {
  uint32 ret = 1;
  repeated GuildMobilizationTaskProgress finished_tasks = 2;
}

message GuildMobilizationTaskProgress {
  uint32 task_id = 1;
  bool is_finish = 2;
  uint64 progress = 3;
}

message C2L_ActivityScheduleGetData {
  int64 start_time = 1;
  int64 end_time = 2;
}

message L2C_ActivityScheduleGetData {
  uint32 ret = 1;
  ActivityScheduleDatas data = 2;
}

message ActivityScheduleDatas {
  repeated ActivityScheduleData datas = 1;
}

message ActivityScheduleData {
  uint32 id = 1;
  uint32 schedule_id = 2;
  uint32 sort = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  repeated Resource rewards = 6;
}

message MessageBoard {
  string message = 1;
  uint64 message_id = 2;
}

message C2L_GuildMobilizationEditMessageBoard {
  string message = 1;
}

message L2C_GuildMobilizationEditMessageBoard {
  uint32 ret = 1;
  MessageBoard message = 2;
}

message C2L_GuildMobilizationGiveUpTask {
  uint32 task_id = 1;
}

message L2C_GuildMobilizationGiveUpTask {
  uint32 ret = 1;
  GuildMobilizationGuildTask task = 2;
  uint32 accept_task_times = 3;  // 可接取任务的次数
}

message TaskProgress {
  uint32 task_type_id = 1;       // 类型id
  uint64 progress = 2;           // 一般情况下只需要用这个值
  map<uint32, bool> values = 3;  // 额外使用，不重复id使用
  uint32 task_id = 4;            // 任务id
}

message TalentTreeNodeHot {
  uint32 id = 1;
  repeated Attr levels = 2;
}

// 天赋树养成信息
message TalentTreeCultivate {
  map<uint32, uint32> levels = 1;  //  等级
}

// 天赋树基础信息
message TalentTreeBase {
  uint32 reset_times = 1;                                //  洗点次数
  int64 last_add_times_tm = 2;                           //  上次增加次数时间
  map<uint32, TaskTypeProgress> task_type_progress = 3;  // 不同的任务进度
  map<uint32, bool> awarded = 4;                         // 已经领取的奖励id
  repeated TalentTreePlan plans = 5;
}

message TalentTreePlan {
  TalentTreeCultivate cultivate = 1;
  string name = 2;
  uint32 base_id = 3;
  int64 power = 4;
  string topic = 5;  // 描述
}

message C2L_TalentTreeGetData {}

message L2C_TalentTreeGetData {
  uint32 ret = 1;
  TalentTreeCultivate cultivate = 2;
  TalentTreeBase base = 3;
}

message C2L_TalentTreeLevelUp {
  repeated TalentTreeNode nodes = 1;
}

message TalentTreeNode {
  uint32 id = 1;
  uint32 target_level = 2;
}

message L2C_TalentTreeLevelUp {
  uint32 ret = 1;
  repeated TalentTreeNode nodes = 2;
  repeated Resource awards = 3;
  TalentTreeCultivate cultivate = 4;
}

message TalentTreeResetUnit {
  uint32 node_type = 1;       // 节点类型
  repeated uint32 pages = 2;  // 页签
}

message C2L_TalentTreeReset {
  repeated TalentTreeResetUnit units = 1;
  repeated uint32 node_ids = 2;
}

message L2C_TalentTreeReset {
  uint32 ret = 1;
  TalentTreeCultivate cultivate = 2;
  repeated Resource awards = 3;
  repeated uint32 node_ids = 4;
}

message C2L_TalentTreeReceiveTaskAwards {
  repeated uint32 ids = 1;
}

message L2C_TalentTreeReceiveTaskAwards {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  TalentTreeBase data = 3;
  repeated Resource awards = 4;
}

message L2C_TalentTreeTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

message L2C_TalentTreeCultivateUpdate {
  uint32 ret = 1;
  TalentTreeCultivate cultivate = 2;
}

message C2L_TalentTreeHot {}

message L2C_TalentTreeHot {
  uint32 ret = 1;
  repeated TalentTreeNodeHot nodes = 2;
  int64 next_refresh_tm = 3;  // 下次刷新时间
}

message C2L_TalentTreePlanSave {
  TalentTreePlan plan = 1;
}
message L2C_TalentTreePlanSave {
  uint32 ret = 1;
}

message C2L_TalentTreePlanDelete {
  uint32 index = 1;
}
message L2C_TalentTreePlanDelete {
  uint32 ret = 1;
  uint32 index = 2;
}

// Boss挑战
message BossRush {
  map<uint32, BossRushBoss> boss = 1;  // key: group_id
  int64 stamina_recover_time = 2;      // 体力恢复时间
  map<uint32, TaskTypeProgress> task_type_progress = 3;
  map<uint32, bool> task_awarded = 4;
  uint32 total_fight_count = 5;  // 累计攻打次数
}

message BossRushBoss {
  uint32 boss_id = 1;
  uint32 reduce_hp = 2;              // 掉血值。考虑后续新增难度，打完本难度boss时为最大值，不自动进入下一难度。
  BossRushMaxRecord max_record = 3;  // 最大进度记录，用于扫荡
}

message BossRushMaxRecord {
  string report_id = 1;                     // 战报
  repeated BossRushTeamData team_data = 2;  // 队伍数据
}

message BossRushTeamData {
  repeated uint32 team_heroes = 1;
  uint64 team_damage = 2;
  uint32 team_reduce_hp = 3;
}

// Boss挑战 - 获取信息
message C2L_BossRushGetData {}

message L2C_BossRushGetData {
  uint32 ret = 1;
  BossRush data = 2;
}

// Boss挑战 - 攻打Boss
message C2L_BossRushFight {
  uint32 boss_id = 1;
  bool is_sweep = 2;
  bytes client_data = 3;
}

message L2C_BossRushFight {
  uint32 ret = 1;
  uint32 boss_id = 2;
  bool is_sweep = 3;
  string report_id = 5;
  uint64 total_damage = 6;                   // 总伤害
  uint32 total_reduce_hp = 7;                // 总掉血量
  repeated BossRushTeamData teams_data = 8;  // 各队数据
  BossRushBoss boss_data = 9;                // Boss数据
  repeated Resource awards = 10;             // 战斗奖励
  repeated Resource progress_awards = 11;    // 进度奖励
  uint32 total_fight_count = 12;             // 累计攻打次数
}

// Boss挑战 - 任务领奖
message C2L_BossRushTaskAward {
  repeated uint32 task_ids = 1;
}

message L2C_BossRushTaskAward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

message L2C_BossRushTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
}

// Boss挑战 - 购买体力
message C2L_BossRushBuyStamina {
  uint32 buy_type = 1;  // 1: 钻石购买  2: 道具购买
  uint32 count = 2;     // 钻石：购买体力数量 道具：使用道具数量
}

message L2C_BossRushBuyStamina {
  uint32 ret = 1;
  uint32 buy_type = 2;
  uint32 count = 3;
  int64 stamina_recover_time = 4;  // 体力恢复时间
}

message C2L_ActivitySumGetData {
  repeated uint32 activity_type = 1;
}

message L2C_ActivitySumGetData {
  uint32 ret = 1;
  repeated uint32 activity_type = 2;
  repeated ActivitySumClient data = 3;
}

message C2L_ActivitySumLoginReward {
  uint32 activity_type = 1;
  uint32 activity_id = 2;
  repeated uint32 id = 3;
}

message L2C_ActivitySumLoginReward {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  repeated uint32 id = 4;
  repeated Resource rewards = 5;
}

message C2L_ActivitySumTaskReward {
  uint32 activity_type = 1;
  uint32 activity_id = 2;
  repeated uint32 task_ids = 3;
}

message L2C_ActivitySumTaskReward {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  repeated uint32 task_ids = 4;
  repeated Resource awards = 5;
}

message C2L_ActivitySumActivePuzzleCell {
  uint32 activity_type = 1;
  uint32 activity_id = 2;
  uint32 x = 3;
  uint32 y = 4;
}

message L2C_ActivitySumActivePuzzleCell {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  uint32 x = 4;
  uint32 y = 5;
  ActivityPuzzle puzzle = 6;
  repeated Resource awards = 7;
  uint32 old_copy_id = 8;
  uint32 old_puzzle_id = 9;
}

message L2C_ActivitySumTaskUpdate {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  map<uint32, TaskTypeProgress> task_progress = 4;
}

message C2L_ActivitySumTicketBuy {
  uint32 activity_type = 1;
  uint32 activity_id = 2;
  uint32 buy_count = 3;
}

message L2C_ActivitySumTicketBuy {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  uint32 buy_count = 4;
  repeated Resource awards = 5;
}

message C2L_ActivitySumExchange {
  uint32 activity_type = 1;
  uint32 activity_id = 2;
  uint32 exchange_id = 3;
  uint32 exchange_count = 4;
}

message L2C_ActivitySumExchange {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  uint32 exchange_id = 4;
  repeated ActivityExchange exchange = 5;
  repeated Resource awards = 6;
  uint32 exchange_count = 7;
}

message ActivityExchange {
  uint32 id = 1;     // 表ID
  uint32 count = 2;  // 已兑换次数
}

message C2L_ActivitySumTurnTableSummon {
  uint32 buff = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
}

message L2C_ActivitySumTurnTableSummon {
  uint32 ret = 1;
  uint32 buff = 2;
  repeated Resource awards = 3;
  repeated uint32 big_award_id = 4;
  uint32 group_id = 5;
  map<uint32, uint32> buff_ids = 6;
  uint32 guarantee_count = 7;
  uint32 activity_type = 8;
  uint32 activity_id = 9;
}

message C2L_ActivitySumTurnTableSelectBuff {
  uint32 buff_id = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
}

message L2C_ActivitySumTurnTableSelectBuff {
  uint32 ret = 1;
  uint32 buff_id = 2;
  map<uint32, uint32> buff_ids = 3;
  uint32 activity_type = 4;
  uint32 activity_id = 5;
}

// 玩家切磋数据
//$<ROP redis|hash|duel:uint64>
message Duel {
  int64 last_duel_time = 1;  // 上次切磋时间
  uint32 duel_status = 2;    // 自己的切磋状态 common.DUEL_STATUS_TYPE
}

message C2L_DuelSetStatus {
  uint32 duel_status = 1;  // common.DUEL_STATUS_TYPE
}

message L2C_DuelSetStatus {
  uint32 ret = 1;
  uint32 duel_status = 2;
}

message C2L_DuelFight {
  uint64 uid = 1;        // 玩家id
  uint32 fid = 2;        // 阵容id
  uint64 server_id = 3;  // 玩家所在服务器id
  bytes client_data = 4;
}

message L2C_DuelFight {
  uint32 ret = 1;
  uint64 uid = 2;
  uint32 fid = 3;
  uint64 server_id = 4;
  string report_id = 5;              // 战报id
  bool is_win = 6;                   // 是否胜利
  int64 last_duel_time = 7;          // 上次切磋时间
  UserSnapshot target_snapshot = 8;  // 对方快照
}

message C2L_ActivitySumMakeGift {
  uint32 activity_id = 1;
  uint32 activity_type = 2;
  uint32 gift_id = 3;
  uint32 count = 4;
}

message L2C_ActivitySumMakeGift {
  uint32 ret = 1;
  uint32 activity_id = 2;
  uint32 activity_type = 3;
  uint32 gift_id = 4;
  uint32 count = 5;
  repeated Resource awards = 6;
}

message C2L_ActivitySumFeed {
  uint32 activity_id = 1;
  uint32 activity_type = 2;
  uint32 gift_id = 3;
  uint32 count = 4;
}

message L2C_ActivitySumFeed {
  uint32 ret = 1;
  uint32 activity_id = 2;
  uint32 activity_type = 3;
  uint32 gift_id = 4;
  uint32 count = 5;
  repeated Resource awards = 6;
  uint32 feed_level = 7;
  uint32 feed_exp = 8;
  uint32 feed_cond = 9;
  uint32 feed_count = 10;
  uint32 add_exp = 11;
}

message C2L_ActivitySumShootGameFight {
  uint32 stage_id = 1;
  uint32 activity_id = 2;
  uint32 activity_type = 3;
}

message L2C_ActivitySumShootGameFight {
  uint32 ret = 1;
  uint32 activity_id = 2;
  uint32 activity_type = 3;
  uint32 stage_id = 4;
  repeated Resource awards = 5;
}

// 单局数据，总数据体现在任务进度上
message ActivitySumSynthesisGameSingleData {
  uint32 score = 1;                // 本局积分
  uint32 drop_count = 2;           // 本局投掷兽蛋次数
  uint32 max_synthesis_level = 3;  // 合成兽蛋的最高等级
}

// 对局开始，扣除次数
message C2L_ActivitySumSynthesisGameStart {
  uint32 activity_type = 1;  // 活动类型 activity_turntable_info.type
  uint32 activity_id = 2;    // 活动id
}

message L2C_ActivitySumSynthesisGameStart {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  map<uint32, uint32> item_daily_buy_num = 4;  // item_id => 当日已购买次数
}

// 对局中途，使用道具
message C2L_ActivitySumSynthesisGameUseItem {
  uint32 activity_type = 1;  // 活动类型 activity_turntable_info.type
  uint32 activity_id = 2;    // 活动id
  uint32 item_type = 3;      // 道具类型 common.SYNTHESIS_GAME_ITEM_TYPE
}

message L2C_ActivitySumSynthesisGameUseItem {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  uint32 item_type = 4;
}

// 对局结束，更新数据
message C2L_ActivitySumSynthesisGameUpdate {
  uint32 activity_type = 1;  // 活动类型 activity_turntable_info.type
  uint32 activity_id = 2;    // 活动id
  ActivitySumSynthesisGameSingleData data = 3;
}

message L2C_ActivitySumSynthesisGameUpdate {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  ActivitySumSynthesisGameSingleData data = 4;
  uint32 total_score = 5;  // 累计积分
  uint32 max_score = 6;    // 最大积分
}

message C2L_ActivitySumSynthesisGameBuyItem {
  uint32 activity_type = 1;  // 活动类型 activity_turntable_info.type
  uint32 activity_id = 2;    // 活动id
  uint32 item_type = 3;      // 道具类型 common.SYNTHESIS_GAME_ITEM_TYPE
  uint32 buy_num = 4;        // 购买数量
}

message L2C_ActivitySumSynthesisGameBuyItem {
  uint32 ret = 1;
  uint32 activity_type = 2;
  uint32 activity_id = 3;
  uint32 item_type = 4;
  uint32 buy_num = 5;
  uint32 daily_buy_num = 6;  // 当日已购买次数
}

message SelectSummonOpenActivity {
  uint64 id = 1;         // 唯一id
  uint32 sys_id = 2;     // 活动id
  int64 start_time = 3;  // 开始时间
  int64 end_time = 4;    // 结束时间
}

message SelectSummonSummon {
  repeated uint32 color_guarantee = 1;  // 0:紫色保底次数 1:橙色保底次数 2:红色保底次数
  uint32 not_up_hero_count = 2;         // 玩家距离上次没抽到Up将的次数
  uint32 total_summon_count = 3;        // 抽取总次数
  uint32 total_red_card = 4;            // 总红卡
  uint32 free_time = 5;                 // 本期抽了多少免费次数
}

message SelectSummon {
  uint64 id = 1;                  // gmxml
  uint32 sys_id = 2;              // 量表id
  SelectSummonSummon summon = 3;  // 抽卡数据
}

message C2L_SelectSummonGetOpenActivity {}
message L2C_SelectSummonGetOpenActivity {
  uint32 ret = 1;
  repeated SelectSummonOpenActivity activities = 2;
  SelectSummon select_summon = 3;
}

message C2L_SelectSummonSummon {
  uint64 id = 1;           // 活动id
  uint32 summon_type = 2;  // 抽卡类型：1：单抽 2：连抽
  uint32 cur_hero = 3;     // 英雄卡池
  uint32 cost_type = 4;    // 消耗类型 1：免费 2：道具
}

message L2C_SelectSummonSummon {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 summon_type = 3;  // 抽卡类型
  repeated Resource awards = 4;
  uint32 cost_type = 5;
  uint32 cur_hero = 6;
  SelectSummonSummon summon = 7;
}

message L2C_SelectSummonUpdate {
  uint64 id = 1;
}

message C2L_SelectSummonTestSummon {
  uint64 id = 1;            // 活动id
  uint32 summon_count = 2;  // 抽卡次数
  uint32 cur_hero = 3;      // 英雄卡池
}

message L2C_SelectSummonTestSummon {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 summon_count = 3;  // 抽卡次数
  uint32 cur_hero = 4;      // 英雄卡池
  map<uint32, uint32> red_card = 5;
  map<uint32, uint32> red_summon_count = 6;
  uint32 total_red_count = 7;
  map<uint32, uint32> orange_card = 8;
  map<uint32, uint32> purple_card = 9;
  map<uint32, uint32> red_fragment = 10;
  map<uint32, uint32> others_resource = 11;
}

message ActivityLifelongGift {
  uint32 id = 1;
  int64 expired_time = 2;
}

message ActivityLifelongGifts {
  map<uint32, ActivityLifelongGift> gifts = 1;
  map<uint32, TaskTypeProgress> task_progress = 2;
  map<uint32, bool> task_type_id_trigger = 3;
}

message L2C_ActivityLifelongGiftUpdate {
  uint32 ret = 1;
  map<uint32, ActivityLifelongGift> gifts = 2;
  repeated Resource awards = 3;
}

message ExpiredItem {
  uint64 id = 1;
  uint32 sys_id = 2;
  uint32 type = 3;
  uint32 count = 4;
  int64 expired_time = 5;
}

message ExpiredItems {
  repeated ExpiredItem items = 1;
}

message C2L_SeasonStartTowerRankList {}

message L2C_SeasonStartTowerRankList {
  uint32 ret = 1;
  repeated RankValue list = 2;
  uint32 self_rank = 3;
  RankValue self_value = 4;
  uint32 total = 5;
}

message C2L_SeasonStartTowerRankLike {
  uint64 uid = 1;
}

message L2C_SeasonStartTowerRankLike {
  uint32 ret = 1;
  uint64 uid = 2;
  repeated Resource awards = 3;
  uint32 like_num = 4;
}

message C2L_GSTChallengeGetData {}

message L2C_GSTChallengeGetData {
  uint32 ret = 1;
  repeated GSTChallengeGuildRank guild_rank = 2;     // 房间内公会排名
  repeated GSTChallengeGuildUserRank user_rank = 3;  // 展示的前三玩家
  GSTGuildUserChallenge user = 4;
  GSTChallengeLastTop last_top = 5;                  // 上一场展示数据
  uint32 room_rank = 6;                              // 房间内排名
  repeated GSTChallengeShowLog show_logs = 7;        // 跑马灯信息
  repeated GSTChallengeTeamInfo opponent_teams = 8;  // 对手的队伍信息
  repeated GSTChallengeTeamInfo self_teams = 9;      // 自己的队伍信息
}

message GSTChallengeGuildRank {
  GSTSimpleGuild guild = 1;
  uint32 score = 2;
  int64 score_tm = 4;  // 达到该积分的时间
}

message GSTChallengeGuildUserRank {
  UserSnapshot user = 1;
  uint32 score = 2;      // 积分
  uint32 total_win = 3;  // 总胜场
  int64 score_tm = 4;    // 达到该积分的时间
}

message GSTChallengeLastTop {
  repeated GSTChallengeGuildRank guild_top_3 = 1;     // 上一场公会前三
  repeated GSTChallengeGuildUserRank user_top_3 = 2;  // 上一场玩家前三
}

// 公会战Challenge的玩家数据
message GSTGuildUserChallenge {
  uint32 match_round = 1;                         // 第几轮匹配
  uint32 score = 2;                               // 单场的积分
  repeated uint32 wins = 3;                       // 每个匹配轮次的胜利数
  repeated uint32 buff = 4;                       // 选择的buff
  GSTChallengeTaskData task = 5;                  // 任务
  uint32 max_continue_win = 6;                    // 最大连胜次数
  repeated GSTChallengeMatchInfo match_data = 7;  // 匹配信息
  repeated GSTChallengeMatched matched = 8;       // 已经匹配过得对手
  uint32 total_score = 9;                         // 单轮公会战期间擂台赛的总积分
  int64 score_tm = 10;                            // 达到当前积分的时间
  bool buff_selected = 11;                        // 这一匹配轮次的buff是否已选择
  uint32 show_buff = 12;                          // 给玩家显示的buffID
  bool quit_limit = 13;                           // 退出公会后受到限制
}

message GSTChallengeMatched {
  uint64 uid = 1;
  uint32 team_index = 2;
}

message GSTChallengeTaskData {
  map<uint32, TaskTypeProgress> task_type_progress = 1;
  map<uint32, bool> awarded = 2;
}

message C2L_GSTChallengeMatch {}

message L2C_GSTChallengeMatch {
  uint32 ret = 1;
  repeated GSTChallengeTeamInfo opponent_teams = 2;  // 对手的队伍信息
  repeated GSTChallengeTeamInfo self_teams = 3;      // 自己的队伍信息
  GSTGuildUserChallenge data = 4;
}

message GSTChallengeMatchInfo {
  uint32 index = 1;                  // 第几只队伍
  uint32 status = 2;                 // 0: 未匹配 1:未打 2:win 3:lose 4: 淘汰
  uint32 win_times = 5;              // 连胜数
  map<uint32, int64> hp_pct = 6;     // 血量万分比
  uint64 op_uid = 7;                 // 对手ID
  uint32 op_team_index = 8;          // 匹配到的对手队伍序号
  map<uint32, int64> op_hp_pct = 9;  // key: pos value:战斗结束后对手血量万分比
  cl.GSTGuildUserBase bot = 10;      // 匹配的机器人
  uint32 total_wins = 11;            // 队伍的总胜场
}

message C2L_GSTChallengeFight {
  uint32 team_index = 1;  // 本次战斗的队伍
  bytes client_data = 2;  // 前端透传字段
}

message L2C_GSTChallengeFight {
  uint32 ret = 1;
  uint32 team_index = 2;  // 本次战斗的队伍
  GSTChallengeMatchInfo match_info = 3;
  repeated Resource awards = 4;
  repeated uint32 wins = 5;     // 总胜场
  uint32 max_continue_win = 6;  // 最大连胜
  uint32 room_rank = 7;         // 房间排名
  uint32 score = 8;             // 积分
}

message C2L_GSTChallengeBuffChoose {
  Attr buff = 1;
}

message L2C_GSTChallengeBuffChoose {
  uint32 ret = 1;
  Attr buff = 2;
  GSTGuildUserChallenge data = 3;
}

message C2L_GSTChallengeTaskReward {
  repeated uint32 task_ids = 1;
}

message L2C_GSTChallengeTaskReward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

message L2C_GSTChallengeTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
}

message C2L_GSTChallengeRank {
  uint32 rank_id = 1;
}

message L2C_GSTChallengeRank {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated GSTChallengeGuildUserRank data = 3;
}

message GSTChallengeRecord {
  repeated GSTChallengeGuildRank guild_rank = 1;     // 公会排名
  repeated GSTChallengeGuildUserRank user_rank = 2;  // 成员排名
}

message GSTChallengeTeamInfo {
  GSTGuildUserBase user_info = 1;  // 玩家展示信息
  repeated GSTTeamHeroInfo heroes = 2;
  uint32 team_index = 3;
}

// 竞技场战报数据
//$<ROP redis|map|gst_challenge_fight_log:uint64 >
message GSTChallengeFightLog {
  uint32 id = 1;                      //$<ROP unique> //日志循环队列id
  GSTChallengeTeamInfo attacker = 2;  // 进攻方
  GSTChallengeTeamInfo defender = 3;  // 防守方
  string report_id = 4;               // 战报id
  int64 tm = 5;                       // 记录的时间
  bool bot = 6;                       // defender是否是机器人
  GSTChallengeMatchInfo match = 7;
  uint32 match_round = 8;  // 第几轮匹配
  uint64 log_id = 9;       // 日志唯一id
}

message GSTChallengeFightLogs {
  repeated GSTChallengeFightLog logs = 1;
}
message GSTChallengeFightLogDeletes {
  repeated uint32 ids = 2;
}

// 战报记录
message C2L_GSTChallengeFightLogList {
  uint32 match_round = 1;  // 第几轮匹配
}

message L2C_GSTChallengeFightLogList {
  uint32 ret = 1;
  uint32 match_round = 2;  // 第几轮匹配
  repeated GSTChallengeFightLog logs = 3;
}

message GSTChallengeShowLog {
  string name = 1;
  string guild_name = 2;
  uint32 continue_win = 3;
}

message MultipleUserIds {
  repeated uint64 ids = 1;
}

message SeasonDoorFightLine {
  uint32 id = 1;                               // 量表id
  uint32 fight_times = 2;                      // 总战斗次数
  repeated SeasonDoorFightResult results = 3;  // 战斗结果模型
  repeated Resource rewards = 4;               // 未领取的挂机奖励
  int64 end_time = 5;                          // 挂机战斗结束时的时间
  int64 cd = 6;                                // 总挂机cd时间（v2.35后废弃）
  uint32 real_fight_times = 7;                 // 实际战斗次数（TODO 考虑使用results的长度替换）
  uint32 rest_settle_num = 8;                  // 挂机奖励结算次数（只结算未领奖）
  uint32 oil_id = 9;                           // 涂油id
  repeated Resource oil_rewards = 10;          // 未领取的涂油奖励
  int64 last_settle_time = 11;                 // 上次挂机奖励结算完成时间
  repeated Resource total_rewards = 12;        // 总的挂机奖励（日志记录）
  uint32 oil_op_type = 13;                     // 涂油操作类型 common.SEASON_DOOR_OIL_OPERATION_TYPE
  uint32 occupy_oil_num = 14;                  // 占用的涂油数量
}

message SeasonDoorBatchFightResult {
  string finnal_report_id = 1;  // 最后一场战报id
  uint32 win_times = 2;         // 挂机完成且全胜的次数
  float avg_round = 3;
  uint32 lose_times = 4;  // 挂机完成但未全胜的次数（两者相加为挂机完成次数）
}

message SeasonDoorFightLineLog {
  SeasonDoorFightLine result = 1;
  repeated uint32 hero_sysIds = 2;              // 日志展示英雄头像
  SeasonDoorBatchFightResult batch_result = 3;  // 批量战斗的结果
}

message SeasonDoorFightResult {
  repeated SeasonDoorWaveFightResult results = 1;  // 战斗波次结果
  string report_id = 2;                            // 战报id
  bool win = 3;                                    // 战斗胜利
  uint32 drop_group = 4;                           // 挂机掉落组
  int64 rest_time = 5;                             // 挂机时间
}

message SeasonDoorWaveFightResult {
  bool win = 1;
  uint32 round = 2;  // 战斗的回合数
}

message SeasonDoor {
  repeated SeasonDoorFightLine fight_lines = 1;
  map<uint32, TaskTypeProgress> task_progress = 2;  // 任务进度
  map<uint32, bool> receive_awarded = 3;            // 已领取奖励的任务
  map<uint32, uint32> passed_doors = 4;             // door=>level
  map<uint32, bool> pre_fight_result = 5;           // 上次是否通关
}

message C2L_SeasonDoorGetData {}
message L2C_SeasonDoorGetData {
  uint32 ret = 1;
  SeasonDoor data = 2;
}

message C2L_SeasonDoorFightLine {
  uint32 id = 1;           // 量表id
  uint32 oil_op_type = 2;  // 涂油操作类型 common.SEASON_DOOR_OIL_OPERATION_TYPE
  uint32 oil_id = 3;       // 涂油id
  uint32 oil_num = 4;      // 涂油数量
}
message L2C_SeasonDoorFightLine {
  uint32 ret = 1;
  uint32 id = 2;
  uint32 oil_op_type = 3;
  uint32 oil_id = 4;
  uint32 oil_num = 5;
}

message C2L_SeasonDoorFight {
  uint32 id = 1;  // 量表id
  bytes client_data = 2;
}
message L2C_SeasonDoorFight {
  uint32 ret = 1;
  uint32 id = 2;
  SeasonDoorFightResult result = 3;
  bt.BattleSummary report = 4;
  bool lastMultiFight = 5;        // 是否是批量战斗的最后一场
  bool pass = 6;                  // 是否通关
  repeated Resource rewards = 7;  // 战斗奖励
}

message C2L_SeasonDoorTaskReward {
  repeated uint32 task_ids = 1;
}
message L2C_SeasonDoorTaskReward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource rewards = 3;
}
message L2C_SeasonDoorTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
}

message C2L_SeasonDoorFightLineViewReward {
  uint32 id = 1;  // 量表id
}

message L2C_SeasonDoorFightLineViewReward {
  uint32 ret = 1;
  uint32 id = 2;
  repeated Resource rewards = 3;
  repeated Resource oil_rewards = 4;
  uint32 rest_settle_num = 5;  // 挂机奖励结算次数
  uint32 oil_op_type = 6;      // 涂油操作类型
  uint32 occupy_oil_num = 7;   // 占用的涂油数量（查看后更新）
}

message C2L_SeasonDoorFightLineReward {
  uint32 id = 1;         // 量表id
  bool finish_rest = 2;  // 是否停止挂机
}
message L2C_SeasonDoorFightLineReward {
  uint32 ret = 1;
  uint32 id = 2;
  bool finish_rest = 3;
  repeated Resource rewards = 4;
  repeated Resource oil_rewards = 5;
  uint32 rest_settle_num = 6;  // 挂机结算次数
}

message C2L_SeasonDoorLog {
  uint32 page = 1;
}
message L2C_SeasonDoorLog {
  uint32 ret = 1;
  repeated SeasonDoorFightLineLog logs = 2;
  uint32 page = 3;
  uint32 total = 4;
}

// 赛季装备
//$<ROP redis|map|sj:uint64 >
message SeasonJewelry {
  uint64 id = 1;                               //$<ROP unique >    //唯一id
  uint32 sys_id = 2;                           // season_jewelry_info.id
  uint64 hero_id = 3;                          // 穿戴的英雄id
  map<uint32, SeasonJewelrySkill> skills = 4;  // 词条 pos => skill
  uint32 rare = 5;                             // 品质
  uint32 change_num = 6;                       // 洗练次数
  uint32 guarantee_change_num = 7;             // 保底洗练次数
}

message SeasonJewelrySkill {
  uint32 skill_id = 1;         // 词条id
  uint32 skill_level = 2;      // 词条level
  uint32 skill_change_id = 3;  // 未选择的洗练结果(洗练这个行为更新数据，保存这个行为删除数据)
}

message C2L_SeasonJewelryGetData {}

message L2C_SeasonJewelryGetData {
  uint32 ret = 1;
  repeated SeasonJewelry jewelry_list = 2;              // 赛季装备列表
  SeasonJewelryDecomposeCfg auto_decompose_config = 3;  // 自动分解设置
  repeated uint64 lock_heroes = 4;                      // 锁定英雄
  uint32 total = 5;                                     // 总的分包个数
  uint32 order = 6;                                     // 序号，从0开始，最大为total-1
}

message SeasonJewelryWearObject {
  uint64 hid = 1;
  map<uint32, uint64> pos_jewelry = 2;  // 所有装备信息 pos => jewelry_unique_id
}

message C2L_SeasonJewelryWear {
  uint32 operate_type = 1;                       // 操作类型 common.SEASON_JEWELRY_OPERATE_TYPE
  repeated SeasonJewelryWearObject objects = 2;  // 操作对象
}

message L2C_SeasonJewelryWear {
  uint32 ret = 1;
  uint32 operate_type = 2;
  repeated SeasonJewelryWearObject objects = 3;
  repeated SeasonJewelry change_jewelry_list = 4;  // 变化的装备（装备上的hid）
  repeated Hero change_heroes = 5;                 // 变化的英雄（英雄上的jewelry_unique_id）
}

message C2L_SeasonJewelryLock {
  uint64 hid = 1;
  bool lock = 2;  // true: 锁定 false: 解锁
}

message L2C_SeasonJewelryLock {
  uint32 ret = 1;
  uint64 hid = 2;
  bool lock = 3;
}

message C2L_SeasonJewelryDecompose {
  repeated uint64 jewelry_unique_ids = 1;
}

message L2C_SeasonJewelryDecompose {
  uint32 ret = 1;
  repeated uint64 jewelry_unique_ids = 2;
  repeated Resource awards = 3;
}

message SeasonJewelryDecomposeCfg {
  repeated uint32 auto_rare = 1;  // 需要自动分解的装备品质
  uint32 auto_level = 2;          // 小于等于该等级自动分解
}

message C2L_SeasonJewelrySetAutoDecompose {
  SeasonJewelryDecomposeCfg config = 1;  // 自动分解设置
}

message L2C_SeasonJewelrySetAutoDecompose {
  uint32 ret = 1;
  SeasonJewelryDecomposeCfg config = 2;
}

message L2C_SeasonJewelryUpdate {
  uint32 ret = 1;
  repeated SeasonJewelry jewelry_list = 2;
}

message C2L_SeasonJewelrySkillLevelUp {  // 强化
  uint64 jewelry_unique_id = 1;          // 只能选择装备，词条随机
  uint32 level_up_count = 2;             // 强化次数
}

message L2C_SeasonJewelrySkillLevelUp {
  uint32 ret = 1;
  uint64 jewelry_unique_id = 2;
  uint32 level_up_count = 3;
  SeasonJewelry jewelry = 4;
}

message C2L_SeasonJewelrySkillClassUp {  // 升阶
  uint64 jewelry_unique_id = 1;          // 只能选择装备，词条随机
}

message L2C_SeasonJewelrySkillClassUp {
  uint32 ret = 1;
  uint64 jewelry_unique_id = 2;
  uint32 skill_pos = 3;
  SeasonJewelrySkill skill = 4;
}

message C2L_SeasonJewelrySkillChange {  // 洗炼
  uint64 jewelry_unique_id = 1;
  uint32 skill_pos = 2;   // 要洗炼的词条位置
  bool is_guarantee = 3;  // 是否保底洗炼
}

message L2C_SeasonJewelrySkillChange {
  uint32 ret = 1;
  uint64 jewelry_unique_id = 2;
  uint32 skill_pos = 3;
  bool is_guarantee = 4;
  SeasonJewelrySkill skill = 5;
}

message C2L_SeasonJewelrySkillChangeConfirm {  // 确认洗炼结果
  uint64 jewelry_unique_id = 1;
  uint32 skill_pos = 2;
  bool reserve = 3;  // true: 保留 false: 放弃
}

message L2C_SeasonJewelrySkillChangeConfirm {
  uint32 ret = 1;
  uint64 jewelry_unique_id = 2;
  uint32 skill_pos = 3;
  bool reserve = 4;
  SeasonJewelrySkill skill = 5;
}

message C2L_SeasonJewelryTestReRollSkill {
  uint32 skill_rare = 1;
}

message L2C_SeasonJewelryTestReRollSkill {
  uint32 ret = 1;
}

//$<ROP redis|map|title:uint64>
message Title {
  uint32 id = 1;         //$<ROP unique>
  int64 start_time = 2;  // 激活时间
  int64 end_time = 3;    // 过期时间
}

message C2L_TitleList {}
message L2C_TitleList {
  uint32 ret = 1;
  repeated Title list = 2;
}

message C2L_TitleUse {
  uint32 title_id = 1;
}

message L2C_TitleUse {
  uint32 ret = 1;
  uint32 title_id = 2;
}

message L2C_TitleNew {
  uint32 ret = 1;
  repeated Title titles = 2;
}

message C2L_SeasonComplianceGetData {
  uint32 stage = 1;  // 阶段ID
}

message L2C_SeasonComplianceGetData {
  uint32 ret = 1;
  repeated RankValue top3 = 2;                       // 前三名
  uint32 score = 3;                                  // 当前阶段的分数
  uint32 total_score = 4;                            // 总积分
  repeated uint32 recv = 5;                          // 当前阶段的领取积分
  uint32 less_point = 6;                             // 与上一名差的的分
  repeated ComplianceSourcePoint source_points = 7;  // 积分来源途径测速
  uint32 stage = 8;                                  // 阶段ID
  uint32 self_rank = 9;                              // 自己的排名
  uint32 total_less_point = 10;                      // 总榜上一名少的分
  uint32 total_self_rank = 11;                       // 总榜自己的排行
}

message SeasonCompliance {
  uint32 stage = 1;
  uint32 phase = 2;                                  // 期数
  repeated uint32 recv = 3;                          // 该期数该阶段的领奖状态
  repeated ComplianceSourcePoint source_points = 4;  // 积分来源途径
  uint32 total_score = 5;                            // 该阶段总分
}

message SeasonComplianceStage {
  uint32 stage_type = 1;
  uint32 phase = 2;
  uint32 stage = 3;
  bool last_stage_end = 4;
}

message ComplianceSourcePoint {
  uint32 source_id = 1;              // 来源ID
  uint32 point = 2;                  // 该来源的总分
  uint32 expand = 3;                 // 拓展字段 （英雄/神器ID）
  map<uint32, bool> emblem_pos = 4;  // 符文位置
}

message C2L_SeasonComplianceRecvReward {
  repeated uint32 recv = 1;
  uint32 stage_id = 2;
}

message L2C_SeasonComplianceRecvReward {
  uint32 ret = 1;
  repeated uint32 recv = 2;
  repeated Resource rewards = 3;
  uint32 stage_id = 4;
}

message C2L_SeasonComplianceList {
  uint32 stage_id = 1;  // 对应表里的RankID
  bool top3 = 2;
}

message L2C_SeasonComplianceList {
  uint32 ret = 1;
  uint32 stage_id = 2;  // 对应表里的RankID
  ComplianceList lists = 3;
  bool top3 = 4;
}

message ComplianceList {
  repeated RankValue list = 1;
  uint32 self_rank = 2;
  RankValue self_value = 3;
  uint32 total = 4;
  uint32 rank_id = 5;
  uint32 less_point = 6;
  uint32 last_point = 7;
}

message L2C_SeasonComplianceScoreChange {
  uint32 ret = 1;
  uint32 stage = 2;        // 阶段
  uint32 add_score = 3;    // 加的分
  uint32 total_score = 4;  // 总分
}

message ActivityWebDatas {
  map<uint32, ActivityWebData> datas = 1;
}

message ActivityWebData {
  uint32 group_id = 1;
  uint32 recharge_count = 2;
  repeated uint32 award_ids = 3;
}

message C2L_TestDrop {
  uint32 drop_group = 1;  // 掉落组
  uint32 count = 2;
}

message L2C_TestDrop {
  uint32 ret = 1;
  repeated Resource awards = 2;
}

message SeasonShop {
  uint32 shop_id = 1;                  // 商店id
  map<uint32, uint32> goods_list = 2;  // 商品列表 sysGoodsId => boughtNum
}

message C2L_SeasonShopGetData {
  uint32 shop_id = 1;  // 商店id
}

message L2C_SeasonShopGetData {
  uint32 ret = 1;
  uint32 shop_id = 2;
  cl.SeasonShop data = 3;
}

message C2L_SeasonShopBuy {
  uint32 shop_id = 1;
  uint32 sys_goods_id = 2;  // 商品系统表id
  uint32 num = 3;           // 购买数量
}

message L2C_SeasonShopBuy {
  uint32 ret = 1;
  uint32 shop_id = 2;
  uint32 sys_goods_id = 3;  // 商品系统表id
  uint32 num = 4;           // 购买数量
  repeated Resource awards = 5;
  repeated Resource costs = 6;
}

message ComplianceTask {
  map<uint32, TaskTypeProgress> progress = 1;  // 不同的任务进度
  map<uint32, bool> awarded = 2;
}

message ComplianceTasks {
  map<uint32, ComplianceTask> tasks = 1;
}

message C2L_ComplianceTasksGetData {
  uint32 compliance_type = 1;
}

message L2C_ComplianceTasksGetData {
  uint32 ret = 1;
  uint32 compliance_type = 2;
  ComplianceTask compliance_task = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
  repeated RankValue list = 6;
}

message C2L_ComplianceTasksRecvTask {
  repeated uint32 task_ids = 1;
  uint32 compliance_type = 2;
}

message L2C_ComplianceTasksRecvTask {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
  uint32 compliance_type = 4;
}

message L2C_ComplianceTasksUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
  uint32 compliance_type = 3;
  bool clear = 4;
}

message ActivityCoupon {
  uint32 act_id = 1;
  repeated uint32 recv_id = 2;
}

message ActivityCouponXml {
  uint32 act_id = 1;
  int64 open_day = 2;
  int64 close_day = 3;
  repeated ActivityCouponRes res = 4;
  uint32 template = 5;
  uint32 state = 6;
}

message ActivityCouponRes {
  uint32 day = 1;
  repeated Resource award = 2;
  repeated Resource cost = 3;
  uint32 internal_id = 4;
  string off = 5;
  string sdk_pid = 6;
  uint32 award_type = 7;
}

message C2L_ActivityCouponGetData {}

message L2C_ActivityCouponGetData {
  uint32 ret = 1;
  ActivityCoupon activity_coupon = 2;
  ActivityCouponXml activity_coupon_xml = 3;
}

message C2L_ActivityCouponBuy {
  uint32 sys_id = 1;
  uint32 reward_type = 2;
}

message L2C_ActivityCouponBuy {
  uint32 ret = 1;
  uint32 sys_id = 2;
  uint32 reward_type = 3;
  repeated Resource award = 4;
}

message L2C_ActivityCouponXmlUpdate {
  uint32 ret = 1;
  ActivityCouponXml datas = 2;
}

// 英雄累登
message DailyAttendanceHero {
  uint32 login_count = 1;             // 累登天数
  map<uint32, bool> recv_status = 2;  // 领奖状态
}

message C2L_DailyAttendanceHeroGetData {}

message L2C_DailyAttendanceHeroGetData {
  uint32 ret = 1;
  DailyAttendanceHero attendance = 2;
}

message C2L_DailyAttendanceHeroRecvAward {
  repeated uint32 recv_id = 1;
}

message L2C_DailyAttendanceHeroRecvAward {
  uint32 ret = 1;
  repeated uint32 recv_id = 2;
  repeated Resource award = 3;
}

enum SeasonMapEventType {
  EventType_None = 0;
  EventType_Connect = 1;
  EventType_Trade = 2;
  EventType_Master = 3;
  EventType_Altar = 4;
  EventType_Dialogue = 5;
}

message SeasonMapEvent {
  SeasonMapTradeEvent trade_event = 1;
  SeasonMapMasterEvent master_event = 2;
  SeasonMapAltarEvent altar_event = 3;
  SeasonMapConnectEvent connect_event = 4;
  SeasonMapDialogueEvent dialogue_event = 5;
}

message SeasonMapTradeEvent {
  map<uint32, uint32> buy_goods = 1;  // 已经购买的物品
}

message SeasonMapMasterEvent {
  uint32 task_id = 1;  // 为0就是结束了
}

message SeasonMapAltarEvent {
  uint32 reset_times = 1;
  repeated uint32 buffs = 2;  // 等待选择的buff
  uint32 choose_buff = 3;     // 选择的buff
  bool choosed = 4;
}

message SeasonMapConnectEvent {
  bool finish = 1;  // 是否完成
}

message SeasonMapDialogueEvent {
  bool finish = 1;  // 是否完成
}

message SeasonMapPosition {
  map<uint32, SeasonMapEvent> event_data = 1;
  repeated SeasonMapMonster monsters = 2;  // 怪物
}

enum SeasonMapRecoverType {
  SeasonMapRecoverType_None = 0;
  SeasonMapRecoverType_Fight_Stamina = 1;
  SeasonMapRecoverType_Goods_Stamina = 2;
  SeasonMapRecoverType_Currency = 3;
}

message SeasonMap {
  reserved 7, 8, 9;
  uint32 position = 1;                               // 当前所在位置
  map<uint32, SeasonMapPosition> position_data = 2;  // 位置数据
  map<uint32, int64> recover_times = 3;              // 恢复时间
  map<uint32, TaskTypeProgress> task_progress = 4;   // 任务
  map<uint32, bool> receive_awarded = 5;
  uint32 explore_rate = 6;  // 探索进度
  repeated uint32 altar_event_positions = 10;
}

message SeasonMapMonster {
  uint32 monster_id = 1;
  uint32 reduce_hp = 2;                   // 掉血值
  SeasonMapMonsterRecord max_record = 3;  // 最大伤害记录
}

message SeasonMapMonsterRecord {
  string report_id = 1;                      // 战报
  repeated SeasonMapTeamData team_data = 2;  // 队伍数据
  uint32 total_reduce_hp = 3;
  uint64 total_damage = 4;
}

message SeasonMapTeamData {
  repeated uint32 team_heroes = 1;
  uint64 team_damage = 2;
  uint32 team_reduce_hp = 3;
}

message SeasonMapSystemEvent {
  uint32 event_id = 1;    // 事件id
  int64 effect_time = 2;  // 事件生效时间
}

message C2L_SeasonMapGetData {}
message L2C_SeasonMapGetData {
  uint32 ret = 1;
  SeasonMap data = 2;
  repeated Resource rewards = 3;  // 货币恢复
}

message C2L_SeasonMapFight {
  uint32 position = 1;
  uint32 monster_id = 2;
  bool is_sweep = 3;
  bytes client_data = 4;
}
message L2C_SeasonMapFight {
  uint32 ret = 1;
  uint32 position = 2;
  uint32 monster_id = 3;
  bool is_sweep = 4;
  SeasonMapMonsterRecord record = 5;
  SeasonMapMonster monster_data = 6;
  repeated Resource rewards = 7;           // 战斗奖励
  repeated Resource progress_rewards = 8;  // 进度奖励
  uint32 explore_rate = 9;
  map<uint32, SeasonMapEvent> event_data = 10;
  string report_id = 11;
}

message C2L_SeasonMapMovePosition {
  uint32 position = 1;  // 目标位置
}
message L2C_SeasonMapMovePosition {
  uint32 ret = 1;
  uint32 position = 2;  // 目标位置
}

message C2L_SeasonMapTaskReward {
  uint32 type = 1;  // 任务类型
  repeated uint32 task_ids = 2;
}
message L2C_SeasonMapTaskReward {
  uint32 ret = 1;
  uint32 type = 2;
  repeated uint32 task_ids = 3;
  repeated Resource rewards = 4;
}

message C2L_SeasonMapDialogue {
  uint32 position = 1;  // 位置
}
message L2C_SeasonMapDialogue {
  uint32 ret = 1;
  uint32 position = 2;  // 位置
}

message C2L_SeasonMapTrade {
  uint32 position = 1;  // 位置
  uint32 goods_id = 2;  // 商品id
  uint32 count = 3;     // 商品数量
}
message L2C_SeasonMapTrade {
  uint32 ret = 1;
  uint32 position = 2;            // 位置
  uint32 goods_id = 3;            // 商品id
  uint32 count = 4;               // 商品数量
  repeated Resource rewards = 5;  // 奖励
  SeasonMapTradeEvent trade_event = 6;
}

message C2L_SeasonMapMaster {
  uint32 position = 1;  // 位置
}
message L2C_SeasonMapMaster {
  uint32 ret = 1;
  uint32 position = 2;  // 位置
  SeasonMapMasterEvent master_event = 3;
}

message C2L_SeasonMapAltar {
  uint32 position = 1;     // 位置
  uint32 choose_buff = 2;  // 选择的buff(默认操作为重置buff)
}
message L2C_SeasonMapAltar {
  uint32 ret = 1;
  uint32 position = 2;  // 位置
  SeasonMapAltarEvent altar_event = 3;
}

message L2C_SeasonMapTaskUpdate {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> task_type_progress = 2;
}

message L2C_SeasonMapRecoverTimeUpdate {
  uint32 ret = 1;
  map<uint32, int64> recover_times = 2;
}

message SeasonMapPositionLog {
  repeated SeasonMapMoveLog move_logs = 1;
  repeated SeasonMapEventLog event_logs = 2;
}

message SeasonMapMoveLog {
  string name = 1;
  uint32 image = 2;
  uint32 from = 3;           // 起点
  uint32 to = 4;             // 终点
  uint32 pokemon_image = 5;  // 宠物形象ID
}

message SeasonMapEventLog {
  string name = 1;
  uint32 image = 2;
  SeasonMapEventType event = 3;
}

message C2L_SeasonMapPositionLogs {
  repeated uint32 positions = 1;
}
message L2C_SeasonMapPositionLogs {
  uint32 ret = 1;
  repeated uint32 positions = 2;
  map<uint32, SeasonMapPositionLog> logs = 3;
}

enum SeasonMapPriceChangeType {
  SeasonMapPriceChangeType_None = 0;
  SeasonMapPriceChangeType_Up_Pct = 1;    // 价格上涨一个万分比
  SeasonMapPriceChangeType_Down_Pct = 2;  // 价格下跌一个万分比
}

message C2L_SeasonMapGetRankList {
  uint32 rank_id = 1;
}

message L2C_SeasonMapGetRankList {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated RankValue list = 3;
  uint32 self_rank = 4;
  RankValue self_value = 5;
  uint32 total = 6;
}

enum SeasonMapBuyStaminaType {
  SeasonMapBuyStaminaType_None = 0;
  SeasonMapBuyStaminaType_Diamond = 1;
  SeasonMapBuyStaminaType_Item = 2;
}

message C2L_SeasonMapBuyStamina {
  SeasonMapBuyStaminaType buy_type = 1;
  uint32 count = 2;                       // 钻石：购买体力数量 道具：使用道具数量
  SeasonMapRecoverType stamina_type = 3;  // 体力类型
}

message L2C_SeasonMapBuyStamina {
  uint32 ret = 1;
  SeasonMapBuyStaminaType buy_type = 2;
  uint32 count = 3;
  SeasonMapRecoverType stamina_type = 4;
}

message C2L_SeasonMapPassPosition {
  repeated uint32 positions = 1;  // 位置列表
}

message L2C_SeasonMapPassPosition {
  uint32 ret = 1;
}

message C2L_MuteAccount {
  uint64 uid = 1;
  uint32 mute_day = 2;
  uint64 server_id = 5;
}

message L2C_MuteAccount {
  uint32 ret = 1;
  uint64 uid = 2;
  uint32 mute_day = 3;
  uint64 server_id = 4;
}

// 宠物
//$<ROP redis|map|pokemon:uint64 >
message Pokemon {
  uint32 sys_id = 1;    //$<ROP unique >       // 系统id
  uint32 star = 2;      // 星级
  uint32 ball_pos = 3;  // 兽栏位置(>0表示已上阵)
}

// 兽栏
message PokemonBall {
  uint32 ball_type = 1;                    // 兽栏类型
  repeated PokemonBallConfig configs = 2;  // 兽栏设置
}
message PokemonBallConfig {
  uint32 pos = 1;
  uint32 pokemon_id = 2;
}

// 宠物 - 信息
message C2L_PokemonGetData {}

message L2C_PokemonGetData {
  uint32 ret = 1;
  repeated Pokemon pokemons = 2;               // 宠物列表
  repeated PokemonBall balls = 3;              // 兽栏
  uint32 master_exp = 4;                       // 大师经验
  uint32 master_level = 5;                     // 大师等级
  repeated uint32 rewarded_master_levels = 6;  // 已领奖的大师等级
  uint32 potential_level = 7;                  // 潜能等级
  uint32 show_pokemon_id = 8;                  // 展示的宠物id
}

// 宠物 - 兽栏设置
message C2L_PokemonSetBall {
  PokemonBall pokemon_ball = 1;
}

message L2C_PokemonSetBall {
  uint32 ret = 1;
  PokemonBall pokemon_ball = 2;
}

// 宠物 - 激活
message C2L_PokemonActivate {
  uint32 sys_id = 1;
}

message L2C_PokemonActivate {
  uint32 ret = 1;
  uint32 sys_id = 2;
  Pokemon pokemon = 3;
  uint32 master_exp = 4;
  uint32 master_level = 5;
}

// 宠物 - 升星
message C2L_PokemonStarUp {
  uint32 sys_id = 1;
}

message L2C_PokemonStarUp {
  uint32 ret = 1;
  uint32 sys_id = 2;
  Pokemon pokemon = 3;
  uint32 master_exp = 4;
  uint32 master_level = 5;
}

// 宠物 - 大师等级领奖
message C2L_PokemonMasterReward {
  repeated uint32 master_levels = 1;
}

message L2C_PokemonMasterReward {
  uint32 ret = 1;
  repeated Resource rewards = 2;
  repeated uint32 rewarded_master_levels = 3;
}

// 宠物 - 潜能升级
message C2L_PokemonPotentialLevelUp {}

message L2C_PokemonPotentialLevelUp {
  uint32 ret = 1;
  uint32 level = 2;
}

// 宠物 - 展示设置
message C2L_PokemonSetShow {
  uint32 sys_id = 1;
}

message L2C_PokemonSetShow {
  uint32 ret = 1;
  uint32 sys_id = 2;
}

message PokemonSummonOpenActivity {
  uint64 id = 1;         // 唯一id
  uint32 sys_id = 2;     // 活动id
  int64 start_time = 3;  // 开始时间
  int64 end_time = 4;    // 结束时间
}

message PokemonSummonSummon {
  repeated uint32 color_guarantee = 1;  // 0:紫色保底次数 1:橙色保底次数 2:红色保底次数
  bool first_draw = 2;                  // 首次保底标记
  uint32 total_summon_count = 3;        // 抽取总次数
  uint32 total_red_card = 4;            // 总红卡
  uint32 daily_free_time = 5;           // 每日免费次数
  uint32 diamond_summon_count = 6;      // 钻石抽次数
}

message PokemonSummon {
  uint64 id = 1;                   // gmxml
  uint32 sys_id = 2;               // 量表id
  PokemonSummonSummon summon = 3;  // 抽卡数据
}

message C2L_PokemonSummonGetOpenActivity {}
message L2C_PokemonSummonGetOpenActivity {
  uint32 ret = 1;
  repeated PokemonSummonOpenActivity activities = 2;
  PokemonSummon pokemon_summon = 3;
}

message C2L_PokemonSummonSummon {
  uint64 id = 1;           // 活动id
  uint32 summon_type = 2;  // 抽卡类型：1：单抽 2：连抽
  uint32 cur_hero = 3;     // 英雄卡池
  uint32 cost_type = 4;    // 消耗类型 1：免费 2：道具
}

message L2C_PokemonSummonSummon {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 summon_type = 3;  // 抽卡类型
  repeated Resource awards = 4;
  uint32 cost_type = 5;
  uint32 cur_hero = 6;
  PokemonSummonSummon summon = 7;
}

message L2C_PokemonSummonUpdate {
  uint64 id = 1;
}

message C2L_PokemonSummonTestSummon {
  uint64 id = 1;            // 活动id
  uint32 summon_count = 2;  // 抽卡次数
  uint32 cur_hero = 3;      // 英雄卡池
}

message L2C_PokemonSummonTestSummon {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 summon_count = 3;  // 抽卡次数
  uint32 cur_hero = 4;      // 英雄卡池
  map<uint32, uint32> red_card = 5;
  map<uint32, uint32> red_summon_count = 6;
  uint32 total_red_count = 7;
  map<uint32, uint32> orange_card = 8;
  map<uint32, uint32> purple_card = 9;
  map<uint32, uint32> red_fragment = 10;
  map<uint32, uint32> others_resource = 11;
}

message TowerPokemon {
  uint32 dungeon_id = 1;                            // 当前关卡
  map<uint32, TaskTypeProgress> task_progress = 2;  // 任务进度
  map<uint32, bool> task_received = 3;              // 已领取奖励的任务
  map<uint32, bool> buy_goods = 4;                  // 已经购买的物品
}

message C2L_TowerPokemonGetData {}
message L2C_TowerPokemonGetData {
  uint32 ret = 1;
  TowerPokemon tower_season = 2;
}

message C2L_TowerPokemonFight {
  uint32 dungeon_id = 1;
  bytes client_data = 2;
}
message L2C_TowerPokemonFight {
  uint32 ret = 1;
  uint32 dungeon_id = 2;
  string report_id = 3;  // 战报id
  bool win = 4;          // 是否胜利
}

message C2L_TowerPokemonRecvAward {
  repeated uint32 task_ids = 1;
}
message L2C_TowerPokemonRecvAward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated Resource awards = 3;
}

message L2C_TowerPokemonUpdateTask {
  uint32 ret = 1;
  map<uint32, TaskTypeProgress> progress = 2;
}

message C2L_TowerPokemonBuyGoods {
  uint32 id = 1;
}

message L2C_TowerPokemonBuyGoods {
  uint32 ret = 1;
  uint32 id = 2;
  repeated Resource awards = 3;
  repeated Resource costs = 4;
}

// 玩家官网充值发奖信息
//$<ROP redis|map|offline_vip_award:uint64 >
message OfflineVipAward {
  string id = 1;  //$<ROP unique>
  uint64 time = 2;
}

// 英雄升品
message C2L_HeroRareUp {
  uint64 id = 1;  // 英雄id
  uint32 rare_level = 2;
}

message L2C_HeroRareUp {
  uint32 ret = 1;
  uint64 id = 2;  // 英雄id
  uint32 rare_level = 3;
  Hero hero = 4;  // 英雄详情
}

// 公平竞技场 - 状态
message BalanceArenaState {
  uint32 season_id = 1;  // 赛季
  uint32 round = 2;      // 赛期
  uint32 stage = 3;      // 阶段id:balance_arena_phase_info.id
}

// 公平竞技场 - 卡牌数据
message BalanceArenaMatchCard {
  repeated uint32 public_heroes = 1;                       // 公共英雄
  repeated uint32 random_heroes = 2;                       // 随机英雄
  repeated uint32 random_artifacts = 3;                    // 随机神器
  uint32 draw_round = 4;                                   // 抽卡轮次
  repeated uint32 draw_heroes = 5;                         // 抽到的英雄(选完删除)
  repeated uint32 draw_artifacts = 6;                      // 抽到的神器（选完删除）
  repeated uint32 choose_heroes = 7;                       // 选择英雄
  repeated uint32 choose_artifacts = 8;                    // 选择神器
  repeated BalanceArenaHero private_heroes = 9;            // 自选英雄
  repeated BalanceArenaArtifact private_artifacts = 10;    // 自选神器
  repeated BalanceArenaHero available_heroes = 11;         // 英雄仓库 = 公共 + 随机 + 选择 + 自选，选定以后不可修改
  repeated BalanceArenaArtifact available_artifacts = 12;  // 神器仓库 = 公共 + 随机 + 选择 + 自选
  repeated BalanceArenaTeam teams = 13;                    // 当前队伍
  bool sign = 14;                                          // 是否已报名
}

message BalanceArenaHero {
  uint32 sys_id = 1;
  uint32 star = 2;
}

message BalanceArenaArtifact {
  uint32 sys_id = 1;
  uint32 star = 2;
}

message BalanceArenaTeam {
  repeated BalanceArenaHero heroes = 1;         // 英雄
  repeated BalanceArenaArtifact artifacts = 2;  // 神器
}

// 公平竞技场 - 基础数据
message C2L_BalanceArenaGetData {}
message L2C_BalanceArenaGetData {
  uint32 ret = 1;
  BalanceArenaState state = 3;             // 玩法状态
  BalanceArenaMatchCard card = 4;          // 卡牌数据
  repeated BalanceArenaMatch matches = 7;  // 比赛数据
}

message BalanceArenaMatch {
  uint32 match_type = 1;                                    // 比赛类型
  uint32 stage_id = 2;                                      // 所处阶段（大组赛和小组赛用于获取场次）
  UserSnapshot left_fighter = 3;                            // 左侧玩家
  UserSnapshot right_fighter = 4;                           // 右侧玩家
  string report_id = 5;                                     // 战报id
  uint64 winner_uid = 6;                                    // 获胜者uid
  uint32 result = 7;                                        // 比分 common.BALANCE_ARENA_RESULT
  BalanceArenaEliminationMatch elimination_match_data = 8;  // 淘汰赛数据
}

// 淘汰赛数据（参考peak_arena）
message BalanceArenaEliminationMatch {
  uint32 match_id = 1;   // 比赛场次id
  uint32 round = 2;      // 第几轮，小组赛:1-3 决赛:4-6 冠军赛:7
  uint32 area = 3;       // 战区，小组赛/决赛:1上半区 2下半区  冠军赛:0
  uint32 group = 4;      // 第几组，小组赛:1-16 决赛/冠军赛:0
  uint32 group_sub = 5;  // 每轮比赛中的场次序号:1-4
}

// 公平竞技场 - 抽牌
message C2L_BalanceArenaCardDraw {}
message L2C_BalanceArenaCardDraw {
  uint32 ret = 1;
  repeated uint32 card_group = 2;  // 抽到的卡牌组
}

// 公平竞技场 - 选牌
message C2L_BalanceArenaCardChoose {
  uint32 card_group = 1;  // 选中的卡牌组
}
message L2C_BalanceArenaCardChoose {
  uint32 ret = 1;
  uint32 card_group = 2;
}

// 公平竞技场 - 自选英雄/神器
message C2L_BalanceArenaCardCustomize {
  uint32 card_type = 1;  // 卡牌类型 1:英雄 2:神器
  uint32 sys_id = 2;
}
message L2C_BalanceArenaCardCustomize {
  uint32 ret = 1;
  uint32 card_type = 2;
  uint32 sys_id = 3;
}

// 公平竞技场 - 组队
message C2L_BalanceArenaTeamUp {
  repeated BalanceArenaTeam teams = 1;  // 组队
  bool sign = 2;                        // 是否报名
}
message L2C_BalanceArenaTeamUp {
  uint32 ret = 1;
  repeated BalanceArenaTeam teams = 2;
  bool sign_success = 3;  // 是否报名成功
}

// 公平竞技场 - 排行榜
message C2L_BalanceArenaGetRankList {
  uint32 rank_type = 1;  // 1.赛季排行 2.小组排行 3.大组排行
}

message L2C_BalanceArenaGetRankList {
  uint32 ret = 1;
  uint32 rank_type = 2;
  repeated RankValue list = 3;
  RankValue self_value = 4;  // 自己的排名数据
}

// 公平竞技场 - 状态更新
message L2C_BalanceArenaPushState {
  uint32 ret = 1;
  BalanceArenaState sta = 2;
}
