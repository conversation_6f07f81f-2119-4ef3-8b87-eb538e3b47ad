package main

import (
	"flag"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

var dir = flag.String("dir", "./server/", "xml路径")

func main() {
	logFilename := "./check.log"                   //日志文件的路径和名字
	cfg := util.NewL4GConfig("DEBUG", logFilename) //日志的等级，这个里面其实把这个文件封装成了一个字符串，加载进来了
	l4g.Global.LoadConfiguration("", []byte(cfg))  //加载配置， 如果是从文件加载的就是第一个参数是文件路径，第二个参数为nil
	defer l4g.Close()
	flag.Parse()

	//MainUi()
	//goxml.Load(*dir, true, true)
}
