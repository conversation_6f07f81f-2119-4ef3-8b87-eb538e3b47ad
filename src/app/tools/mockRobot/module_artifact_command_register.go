package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotArtifactCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArtifactList), L2CArtifactListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArtifactActivate), L2CArtifactActivateCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArtifactStarUp), L2CArtifactStarUpCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArtifactStrength), L2CArtifactStrengthCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArtifactForge), L2CArtifactForgeCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArtifactRevive), L2CArtifactReviveCommand{})

	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ArtifactDebutGetActivity), C2LArtifactDebutGetActivityCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArtifactDebutGetActivity), L2CArtifactDebutGetActivityCommand{})
}

type L2CArtifactListCommand struct {
}

func (c L2CArtifactListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArtifactList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArtifactListCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.artifact.L2CArtifactList(recv)
	return ret.RET(recv.Ret)
}

type L2CArtifactActivateCommand struct {
}

func (c L2CArtifactActivateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArtifactActivate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArtifactActivateCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.artifact.L2CArtifactActivate(recv)
	return ret.RET(recv.Ret)
}

type L2CArtifactStarUpCommand struct {
}

func (c L2CArtifactStarUpCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArtifactStarUp{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArtifactStarUpCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.artifact.L2CArtifactStarUp(recv)
	return ret.RET(recv.Ret)
}

type L2CArtifactStrengthCommand struct {
}

func (c L2CArtifactStrengthCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArtifactStrength{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArtifactStrengthCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.artifact.L2CArtifactStrength(recv)
	return ret.RET(recv.Ret)
}

type L2CArtifactForgeCommand struct {
}

func (c L2CArtifactForgeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArtifactForge{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArtifactForgeCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.artifact.L2CArtifactForge(recv)
	return ret.RET(recv.Ret)
}

type L2CArtifactReviveCommand struct {
}

func (c L2CArtifactReviveCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArtifactRevive{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArtifactReviveCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.artifact.L2CArtifactRevive(recv)
	return ret.RET(recv.Ret)
}

type C2LArtifactDebutGetActivityCommand struct {
}

func (c C2LArtifactDebutGetActivityCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	cmsg := &cl.C2L_ArtifactDebutGetActivity{}
	robot.SendCmd(ph.Cmd, cmsg)
	return ret.RET_OK
}

type L2CArtifactDebutGetActivityCommand struct {
}

func (c L2CArtifactDebutGetActivityCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArtifactDebutGetActivity{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArtifactDebutGetActivityCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
