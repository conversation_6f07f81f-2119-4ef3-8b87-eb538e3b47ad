package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
)

type GuildTalent struct {
	r       *Robot
	Talents map[uint32]uint32
}

func NewGuildTalent(r *Robot) *GuildTalent {
	g := &GuildTalent{
		r:       r,
		Talents: make(map[uint32]uint32),
	}
	return g
}

func (g *GuildTalent) C2LGuildTalentList() {
	cmsg := &cl.C2L_GuildTalentList{}
	//edit here
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GuildTalentList), cmsg)
}

func (g *GuildTalent) L2CGuildTalentList(recv *cl.L2C_GuildTalentList) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	if recv.List != nil && recv.List.Talents != nil {
		g.Talents = recv.List.Talents
	}
}

func (g *GuildTalent) C2LGuildTalentLevelUp() {
	cmsg := &cl.C2L_GuildTalentLevelUp{}
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GuildTalentLevelUp), cmsg)
}

func (g *GuildTalent) L2CGuildTalentLevelUp(recv *cl.L2C_GuildTalentLevelUp) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (g *GuildTalent) C2LGuildTalentReset() {
	cmsg := &cl.C2L_GuildTalentReset{}
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GuildTalentReset), cmsg)
}

func (g *GuildTalent) L2CGuildTalentReset(recv *cl.L2C_GuildTalentReset) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
