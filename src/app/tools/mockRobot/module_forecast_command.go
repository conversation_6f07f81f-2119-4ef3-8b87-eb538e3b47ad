package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotForecastCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ForecastGetData), C2LForecastGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ForecastGetData), L2CForecastGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ForecastUpdate), L2CForecastUpdateCommand{})
}

type C2LForecastGetDataCommand struct {
}

func (c C2LForecastGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, _ []byte) ret.RET {
	cmsg := &cl.C2L_ForecastGetData{}
	robot.SendCmd(ph.Cmd, cmsg)
	return ret.RET_OK
}

type L2CForecastGetDataCommand struct {
}

func (c L2CForecastGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ForecastGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CForecastGetDataCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}

type L2CForecastUpdateCommand struct {
}

func (c L2CForecastUpdateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ForecastGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CForecastUpdateCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
