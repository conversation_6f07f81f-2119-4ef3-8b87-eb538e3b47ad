package main

import (
	"errors"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
)

func (r *Robot) GetEagle() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "url")
	if url == "" {
		return errors.New("GetEagle url is empty")
	}
	l4g.Debugf("[GetEage] url is %s", url)
	resp, err := DoHttp(HTTPGet, url, nil, "")
	if err != nil {
		return err
	}
	l4g.Info("robot:%s get eagle :%s", r.uuid, resp)
	r.SetGateway(string(resp))
	return nil
}
