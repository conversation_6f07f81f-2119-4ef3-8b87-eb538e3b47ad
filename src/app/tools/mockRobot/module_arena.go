package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
	"gitlab.qdream.com/kit/sea/time"
)

type opInfo struct {
	id    uint64
	score uint32
}

type Arena struct {
	r         *Robot
	opData    []*opInfo
	rankList  []uint64
	refreshTm int64
}

func (a *Arena) setRefreshTm() {
	a.refreshTm = time.Now().Unix()
}

func (a *Arena) setOpponents(opponents []*cl.ArenaUserInfo) {
	if len(opponents) > 0 {
		opData := make([]*opInfo, 0, len(opponents))
		for _, op := range opponents {
			opData = append(opData, &opInfo{
				id:    op.User.Id,
				score: op.Score,
			})
		}
		a.opData = opData
	}
}

func (a *Arena) setRankUserList(list []*cl.ArenaRankInfo) {
	if len(list) > 0 {
		a.rankList = make([]uint64, 0, 130)
		for _, v := range list {
			a.rankList = append(a.rankList, v.User.Id)
		}
	}
}

func NewArena(r *Robot) *Arena {
	c := &Arena{
		r: r,
	}
	return c
}

func (a *Arena) randOneOpponent() *opInfo {
	if a.opData != nil {
		key := a.r.rd.RandBetween(0, len(a.opData)-1)
		return a.opData[key]
	}
	return nil
}

func (a *Arena) C2LArenaInfo() {
	cmsg := &cl.C2L_ArenaInfo{ShowTop3: true}

	//edit here
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArenaInfo), cmsg)
}

func (a *Arena) L2CArenaInfo(recv *cl.L2C_ArenaInfo) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	a.setOpponents(recv.Opponents)
}

func (a *Arena) C2LArenaRefresh() {
	cmsg := &cl.C2L_ArenaRefresh{}

	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArenaRefresh), cmsg)
}

func (a *Arena) L2CArenaRefresh(recv *cl.L2C_ArenaRefresh) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.setOpponents(recv.Opponents)
	a.setRefreshTm()
}

func (a *Arena) C2LArenaFight() {
	cmsg := &cl.C2L_ArenaFight{}
	opInfo := a.randOneOpponent()
	if opInfo == nil {
		a.C2LArenaRefresh()
		return
	}

	cmsg.Id = opInfo.id
	cmsg.Score = opInfo.score
	//edit here
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArenaFight), cmsg)
}

func (a *Arena) L2CArenaFight(recv *cl.L2C_ArenaFight) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *Arena) C2LArenaLogList() {
	cmsg := &cl.C2L_ArenaLogList{}
	//edit here
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArenaLogList), cmsg)
}

func (a *Arena) L2CArenaLogList(recv *cl.L2C_ArenaLogList) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *Arena) C2LArenaLike() {
	cmsg := &cl.C2L_ArenaLike{}
	//edit here

	if len(a.rankList) == 0 {
		a.C2LArenaRank()
		return
	}

	index := a.r.Rand().Intn(len(a.rankList))
	cmsg.Id = a.rankList[index]
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArenaLike), cmsg)
}

func (a *Arena) L2CArenaLike(recv *cl.L2C_ArenaLike) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *Arena) C2LArenaRank() {
	cmsg := &cl.C2L_ArenaRank{}
	//edit here
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArenaRank), cmsg)
}

func (a *Arena) L2CArenaRank(recv *cl.L2C_ArenaRank) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.setRankUserList(recv.List)
}

func (a *Arena) C2LArenaRecvAward() {
	cmsg := &cl.C2L_ArenaRecvAward{}
	param := a.r.mock.GetCurrentParam()
	jsonparser.ArrayEach(param, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		id, _ := jsonparser.GetInt(value)
		cmsg.Ids = append(cmsg.Ids, uint32(id))
	}, "ids")
	//edit here
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArenaRecvAward), cmsg)
}
