package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitRobotBattleCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_BattleReport), L2CBattleReportCommand{})
}

type L2CBattleReportCommand struct {
}

func (c L2CBattleReportCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_BattleReport{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CRobotBattleCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.battle.L2CBattleReport(recv)
	return ret.RET_OK
}
