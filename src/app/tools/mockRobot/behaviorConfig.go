package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
)

type configData struct {
	index      int
	Jindex     int `json:"index"`
	operation  uint32
	Joperation uint32 `json:"operation"`
	duration   uint32
	Jduration  uint32 `json:"duration"`
	notes      string
	Jnotes     string `json:"notes"`
	jsonParam  []byte
	JjsonParam string `json:"jsonParam"`
	breakOut   bool
	JbreakOut  bool `json:"breakOut"`
}
type behaviorConfig struct {
	TotalDatas  int
	configDatas []*configData
}

func (config *behaviorConfig) Init() {
	contents, err := os.ReadFile(*flagConfigDir + *flagBeHaviorConfig)
	if err != nil {
		panic(err.Error())
	}
	config.configDatas = make([]*configData, 0)
	index := 0
	jsonparser.ArrayEach(contents, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		configData := &configData{}
		operation, _ := jsonparser.GetInt(value, "operation")
		configData.operation = uint32(operation)
		configData.Joperation = uint32(operation)
		duration, _ := jsonparser.GetInt(value, "duration")
		configData.duration = uint32(duration)
		configData.Jduration = uint32(duration)
		configData.notes, _ = jsonparser.GetString(value, "notes")
		configData.Jnotes = configData.notes
		jsonParam, _, _, _ := jsonparser.Get(value, "jsonParam")
		configData.jsonParam = jsonParam

		newJson := string(jsonParam)
		newJson = strings.Replace(newJson, `"`, "", -1)
		newJson = strings.Replace(newJson, "\n", "", -1)
		newJson = strings.Replace(newJson, "\r", "", -1)
		newJson = strings.Replace(newJson, "\t", "", -1)
		newJson = strings.Replace(newJson, `\n`, "", -1)
		newJson = strings.Replace(newJson, " ", "", -1)
		configData.JjsonParam = newJson
		configData.breakOut, _ = jsonparser.GetBoolean(value, "breakOut")
		configData.JbreakOut = configData.breakOut

		configData.index = index
		configData.Jindex = index

		config.configDatas = append(config.configDatas, configData)

		index++
	})
	config.TotalDatas = len(config.configDatas)
	config.Save()
}

func (config *behaviorConfig) GetIndexOperation(index int) uint32 {
	if index >= config.TotalDatas {
		l4g.Errorf("behaviorConfig bad index:%d", index)
		return 0
	}
	return config.configDatas[index].operation
}

func (config *behaviorConfig) GetIndexJsonParam(index int) []byte {
	if index >= config.TotalDatas {
		l4g.Errorf("behaviorConfig bad index:%d", index)
		return make([]byte, 0)
	}
	return config.configDatas[index].jsonParam
}

func (config *behaviorConfig) GetIndexDuration(index int) uint32 {
	if index >= config.TotalDatas {
		l4g.Errorf("behaviorConfig bad index:%d", index)
		return 0
	}
	return config.configDatas[index].duration
}

func (config *behaviorConfig) GetIndexNotes(index int) string {
	if index >= config.TotalDatas {
		l4g.Errorf("behaviorConfig bad index:%d", index)
		return ""
	}
	return config.configDatas[index].notes
}

func (config *behaviorConfig) GetIndexBreakOut(index int) bool {
	if index >= config.TotalDatas {
		l4g.Errorf("behaviorConfig bad index:%d", index)
		return false
	}
	return config.configDatas[index].breakOut
}

func (config *behaviorConfig) Save() {
	jsonData, err := json.MarshalIndent(config.configDatas, "", "  ")
	if err != nil {
		fmt.Println("Error encoding JSON:", err)
		return
	}

	// 将JSON数据写入文件
	err = os.WriteFile(*flagConfigDir+"mockRobot_order.json", jsonData, 0644)
	if err != nil {
		fmt.Println("Error writing JSON to file:", err)
		return
	}
}
