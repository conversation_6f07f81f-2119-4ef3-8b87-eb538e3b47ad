package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotTaskCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TaskGetInfo), L2CTaskGetInfoCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_TaskReceiveAward), C2LTaskReceiveAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TaskReceiveAward), L2CTaskReceiveAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TaskUpdate), L2CTaskUpdateCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_DispatchReceiveTask), C2LDispatchReceiveTaskCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DispatchReceiveTask), L2CDispatchReceiveTaskCommand{})

}

type L2CTaskGetInfoCommand struct {
}

func (c L2CTaskGetInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_TaskGetInfo{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTaskGetInfoCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.task.L2CTaskGetInfo(recv)
	return ret.RET(recv.Ret)
}

type C2LTaskReceiveAwardCommand struct {
}

func (c C2LTaskReceiveAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {

	robot.task.C2LTaskReceiveAward()
	return ret.RET_OK
}

type L2CTaskReceiveAwardCommand struct {
}

func (c L2CTaskReceiveAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_TaskReceiveAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTaskReceiveAwardCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.task.L2CTaskReceiveAward(recv)
	return ret.RET(recv.Ret)
}

type L2CTaskUpdateCommand struct {
}

func (c L2CTaskUpdateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_TaskUpdate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTaskUpdateCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.task.L2CTaskUpdate(recv)
	return ret.RET(recv.Ret)
}

type C2LDispatchReceiveTaskCommand struct {
}

func (c C2LDispatchReceiveTaskCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.task.C2LDispatchReceiveTask()
	return ret.RET_OK
}

type L2CDispatchReceiveTaskCommand struct {
}

func (c L2CDispatchReceiveTaskCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_DispatchReceiveTask{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDispatchReceiveTaskCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
