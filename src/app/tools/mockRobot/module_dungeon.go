package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
)

type Dungeon struct {
	r *Robot
}

func NewDungeon(r *Robot) *Dungeon {
	d := &Dungeon{
		r: r,
	}
	return d
}

func (d *Dungeon) C2LDungeonFight() {
	cmsg := &cl.C2L_DungeonFight{}
	//edit here
	param := d.r.mock.GetCurrentParam()
	config, _ := jsonparser.GetInt(param, "sys_id")
	cmsg.SysId = uint32(config)
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_DungeonFight), cmsg)
}

func (d *Dungeon) L2CDungeonFight(recv *cl.L2C_DungeonFight) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	if !recv.Win {
		l4g.Errorf("Fight defeat : %s SysId: %d", d.r.uuid, recv.SysId)
	}
}

func (d *Dungeon) C2LDungeonRecvAward() {
	cmsg := &cl.C2L_DungeonRecvAward{}
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_DungeonRecvAward), cmsg)
}

func (d *Dungeon) L2CDungeonRecvAward(recv *cl.L2C_DungeonRecvAward) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (d *Dungeon) C2LDungeonSpeedRecvAward() {
	cmsg := &cl.C2L_DungeonSpeedRecvAward{}
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_DungeonSpeedRecvAward), cmsg)
}

func (d *Dungeon) L2CDungeonSpeedRecvAward(recv *cl.L2C_DungeonSpeedRecvAward) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
}

func (d *Dungeon) C2LDungeonPreview() {
	cmsg := &cl.C2L_DungeonPreview{}
	//edit here
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_DungeonPreview), cmsg)
}

func (d *Dungeon) L2CDungeonPreview(recv *cl.L2C_DungeonPreview) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
}

func (d *Dungeon) C2LDungeon() {
	cmsg := &cl.C2L_Dungeon{}
	//edit here
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_Dungeon), cmsg)
}

func (d *Dungeon) L2CDungeon(recv *cl.L2C_Dungeon) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

}
