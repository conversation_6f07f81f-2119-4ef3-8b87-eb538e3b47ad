package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
)

type SevenDay struct {
	r *Robot
}

func NewSevenDay(r *Robot) *SevenDay {
	m := &SevenDay{
		r: r,
	}
	return m
}

func (m *SevenDay) C2LSevenDayLoginData() {
	cmsg := &cl.C2L_SevenDayLoginData{}
	//edit here
	m.r.SendCmd(uint32(cl.ID_MSG_C2L_SevenDayLoginData), cmsg)
}

func (m *SevenDay) L2CSevenDayLoginData(recv *cl.L2C_SevenDayLoginData) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
