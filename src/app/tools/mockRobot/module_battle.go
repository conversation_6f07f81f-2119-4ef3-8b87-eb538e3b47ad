package main

import (
	"app/protos/out/bt"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

type Battle struct {
	r *Robot
}

func NewBattle(r *Robot) *Battle {
	b := &Battle{
		r: r,
	}
	return b
}

func (b *Battle) L2CBattleReport(recv *cl.L2C_BattleReport) {
	if recv.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("%s L2CBattleReport ret error :%d", b.r.Name(), recv.Ret)
		return
	}
	var battleReport bt.MultipleTeamsReport
	if err := proto.Unmarshal(recv.Report, &battleReport); err != nil {
		l4g.Errorf("%s L2CBattleReport Unmarshal error :%s", b.r.Name(), err.<PERSON><PERSON>r())
		return
	}
	for _, report := range battleReport.Reports {
		bytesData, _ := proto.Marshal(report)
		l4g.Infof("[Battle]%s roundNum:%d, reportSize:%d", b.r.Name(), len(report.Rounds)-1, len(bytesData))
	}
}
