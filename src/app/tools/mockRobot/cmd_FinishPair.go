package main

import (
	"app/protos/out/cg"
	"app/protos/out/cl"
)

func RegisterCmdFinishPair() {
	gRegisterCmdFinishPair = make(map[uint32]uint32)
	gRegisterCmdFinishPair[uint32(cg.ID_MSG_C2G_Login)] = uint32(cg.ID_MSG_G2C_Login)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_KeepAlive)] = uint32(cl.ID_MSG_L2C_KeepAlive)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_Flush)] = uint32(cl.ID_MSG_L2C_Flush)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_SyncQuestionnaire)] = uint32(cl.ID_MSG_L2C_SyncQuestionnaire)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_CarnivalGetData)] = uint32(cl.ID_MSG_L2C_CarnivalGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ShopList)] = uint32(cl.ID_MSG_L2C_ShopList)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_FriendInfo)] = uint32(cl.ID_MSG_L2C_FriendInfo)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_HandbooksGetData)] = uint32(cl.ID_MSG_L2C_HandbooksGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_MedalGetData)] = uint32(cl.ID_MSG_L2C_MedalGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_FlushRedPoint)] = uint32(cl.ID_MSG_L2C_FlushRedPoint)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_Formation)] = uint32(cl.ID_MSG_L2C_Formation)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DungeonFight)] = uint32(cl.ID_MSG_L2C_DungeonFight)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GuidanceFinishStep)] = uint32(cl.ID_MSG_L2C_GuidanceFinishStep)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GuidanceFinishGroup)] = uint32(cl.ID_MSG_L2C_GuidanceFinishGroup)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_HeroLevelUp)] = uint32(cl.ID_MSG_L2C_HeroLevelUp)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_EquipWear)] = uint32(cl.ID_MSG_L2C_EquipWear)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DungeonPreview)] = uint32(cl.ID_MSG_L2C_DungeonPreview)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DungeonRecvAward)] = uint32(cl.ID_MSG_L2C_DungeonRecvAward)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_Summon)] = uint32(cl.ID_MSG_L2C_Summon)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_SetName)] = uint32(cl.ID_MSG_L2C_SetName)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_SevenDayLoginData)] = uint32(cl.ID_MSG_L2C_SevenDayLoginData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_SetClientInfo)] = uint32(cl.ID_MSG_L2C_SetClientInfo)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GoldBuyGet)] = uint32(cl.ID_MSG_L2C_GoldBuyGet)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GoldBuyGetGold)] = uint32(cl.ID_MSG_L2C_GoldBuyGetGold)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ArenaInfo)] = uint32(cl.ID_MSG_L2C_ArenaInfo)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ArenaFight)] = uint32(cl.ID_MSG_L2C_ArenaFight)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ArenaRefresh)] = uint32(cl.ID_MSG_L2C_ArenaRefresh)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ArenaRecvAward)] = uint32(cl.ID_MSG_L2C_ArenaRecvAward)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DungeonSpeedRecvAward)] = uint32(cl.ID_MSG_L2C_DungeonSpeedRecvAward)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_EquipStrength)] = uint32(cl.ID_MSG_L2C_EquipStrength)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TaskReceiveAward)] = uint32(cl.ID_MSG_L2C_TaskReceiveAward)

	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ClientSetMultiLang)] = uint32(cl.ID_MSG_L2C_ClientSetMultiLang)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ClientGetMultiLang)] = uint32(cl.ID_MSG_L2C_ClientGetMultiLang)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_RechargeGetData)] = uint32(cl.ID_MSG_L2C_RechargeGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_MonthlyCardGetData)] = uint32(cl.ID_MSG_L2C_MonthlyCardGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ActivityRechargeGet)] = uint32(cl.ID_MSG_L2C_ActivityRechargeGet)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_VipInfoGet)] = uint32(cl.ID_MSG_L2C_VipInfoGet)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ForecastGetData)] = uint32(cl.ID_MSG_L2C_ForecastGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_PassGetData)] = uint32(cl.ID_MSG_L2C_PassGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_PassReceiveAward)] = uint32(cl.ID_MSG_L2C_PassReceiveAward)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_RateGetStatus)] = uint32(cl.ID_MSG_L2C_RateGetStatus)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_RateScore)] = uint32(cl.ID_MSG_L2C_RateScore)
	//gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ChatGetPrivateMessageNum)] = uint32(cl.ID_MSG_L2C_ChatGetPrivateMessageNum)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TowerList)] = uint32(cl.ID_MSG_L2C_TowerList)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TowerFight)] = uint32(cl.ID_MSG_L2C_TowerFight)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TowerSweep)] = uint32(cl.ID_MSG_L2C_TowerSweep)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_AnnouncementGetData)] = uint32(cl.ID_MSG_L2C_AnnouncementGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_FunctionGetStatus)] = uint32(cl.ID_MSG_L2C_FunctionGetStatus)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ArtifactDebutGetActivity)] = uint32(cl.ID_MSG_L2C_ArtifactDebutGetActivity)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_AvatarGetInfo)] = uint32(cl.ID_MSG_L2C_AvatarGetInfo)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DropActivityGetActivity)] = uint32(cl.ID_MSG_L2C_DropActivityGetActivity)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_OperateActivityGetXML)] = uint32(cl.ID_MSG_L2C_OperateActivityGetXML)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_OperateActivityGetData)] = uint32(cl.ID_MSG_L2C_OperateActivityGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DailyAttendanceGetData)] = uint32(cl.ID_MSG_L2C_DailyAttendanceGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DivineDemonGetOpenActivity)] = uint32(cl.ID_MSG_L2C_DivineDemonGetOpenActivity)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_LinkInfo)] = uint32(cl.ID_MSG_L2C_LinkInfo)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_MirageList)] = uint32(cl.ID_MSG_L2C_MirageList)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TrialGetInfo)] = uint32(cl.ID_MSG_L2C_TrialGetInfo)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TrialFight)] = uint32(cl.ID_MSG_L2C_TrialFight)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TrialPreview)] = uint32(cl.ID_MSG_L2C_TrialPreview)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TrialAward)] = uint32(cl.ID_MSG_L2C_TrialAward)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_TrialSpeed)] = uint32(cl.ID_MSG_L2C_TrialSpeed)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_RankAchieveList)] = uint32(cl.ID_MSG_L2C_RankAchieveList)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_ChatGetToken)] = uint32(cl.ID_MSG_L2C_ChatGetToken)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GetFormation)] = uint32(cl.ID_MSG_L2C_GetFormation)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GoddessContractGetData)] = uint32(cl.ID_MSG_L2C_GoddessContractGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GoddessUnlock)] = uint32(cl.ID_MSG_L2C_GoddessUnlock)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GoddessRecovery)] = uint32(cl.ID_MSG_L2C_GoddessRecovery)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_HandbooksActiveHeroAttr)] = uint32(cl.ID_MSG_L2C_HandbooksActiveHeroAttr)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_Summon)] = uint32(cl.ID_MSG_L2C_Summon)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_EquipMultipleStrength)] = uint32(cl.ID_MSG_L2C_EquipMultipleStrength)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_DispatchReceiveTask)] = uint32(cl.ID_MSG_L2C_DispatchReceiveTask)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_HeroStageUp)] = uint32(cl.ID_MSG_L2C_HeroStageUp)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_CarnivalReceiveAward)] = uint32(cl.ID_MSG_L2C_CarnivalReceiveAward)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GodPresentGetData)] = uint32(cl.ID_MSG_L2C_GodPresentGetData)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GodPresentRecvItem)] = uint32(cl.ID_MSG_L2C_GodPresentRecvItem)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GodPresentSummon)] = uint32(cl.ID_MSG_L2C_GodPresentSummon)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GodPresentRecvAwards)] = uint32(cl.ID_MSG_L2C_GodPresentRecvAwards)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_MedalReceiveAward)] = uint32(cl.ID_MSG_L2C_MedalReceiveAward)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_EquipGrowTransfer)] = uint32(cl.ID_MSG_L2C_EquipGrowTransfer)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_CrystalAddHero)] = uint32(cl.ID_MSG_L2C_CrystalAddHero)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_HandbooksReceiveAwards)] = uint32(cl.ID_MSG_L2C_HandbooksReceiveAwards)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GM)] = uint32(cl.ID_MSG_L2C_GM)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GuildMainInfo)] = uint32(cl.ID_MSG_L2C_GuildMainInfo)
	gRegisterCmdFinishPair[uint32(cl.ID_MSG_C2L_GSTGetData)] = uint32(cl.ID_MSG_L2C_GSTGetData)
}
