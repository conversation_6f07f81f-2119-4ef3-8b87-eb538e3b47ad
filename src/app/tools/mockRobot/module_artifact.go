package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
)

type Artifact struct {
	r             *Robot
	maxStar       uint32
	maxStrength   uint32
	maxForge      uint32
	starIndex     uint32
	strengthIndex uint32
	forgeIndex    uint32
	ownArtifIds   map[uint32]*cl.Artifact
	artifacts     []*cl.Artifact
}

func NewArtifact(r *Robot) *Artifact {
	a := &Artifact{
		r:           r,
		maxStar:     5,
		maxStrength: 100,
		maxForge:    35,
		ownArtifIds: make(map[uint32]*cl.Artifact),
		artifacts:   make([]*cl.Artifact, 0),
	}
	return a
}

func (a *Artifact) C2LArtifactList() {
	cmsg := &cl.C2L_ArtifactList{}
	//edit here
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactList), cmsg)
}

func (a *Artifact) L2CArtifactList(recv *cl.L2C_ArtifactList) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	a.artifacts = recv.Artifacts

	for _, artifact := range recv.Artifacts {
		a.ownArtifIds[artifact.SysId] = artifact
	}
}

func (a *Artifact) C2LArtifactActivate() {
	cmsg := &cl.C2L_ArtifactActivate{}
	//edit here
	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactActivate), cmsg)
}

func (a *Artifact) L2CArtifactActivate(recv *cl.L2C_ArtifactActivate) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	a.ownArtifIds[recv.SysId] = recv.Artifact
	//edit here
	a.artifacts = append(a.artifacts, recv.Artifact)
}

func (a *Artifact) C2LArtifactStarUp() {
	cmsg := &cl.C2L_ArtifactStarUp{}

	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactStarUp), cmsg)
}

func (a *Artifact) L2CArtifactStarUp(recv *cl.L2C_ArtifactStarUp) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.artifacts[a.starIndex].Star = recv.Artifact.Star
}

func (a *Artifact) C2LArtifactStrength() {
	cmsg := &cl.C2L_ArtifactStrength{}

	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactStrength), cmsg)
}

func (a *Artifact) L2CArtifactStrength(recv *cl.L2C_ArtifactStrength) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.artifacts[a.strengthIndex].StrengthLv = recv.Artifact.StrengthLv
}

func (a *Artifact) C2LArtifactForge() {
	cmsg := &cl.C2L_ArtifactForge{}
	//edit here

	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactForge), cmsg)
}

func (a *Artifact) L2CArtifactForge(recv *cl.L2C_ArtifactForge) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.artifacts[a.forgeIndex].ForgeLv = recv.Artifact.ForgeLv
}

func (a *Artifact) C2LArtifactRevive() {
	cmsg := &cl.C2L_ArtifactRevive{}

	a.r.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactRevive), cmsg)

}

func (a *Artifact) L2CArtifactRevive(recv *cl.L2C_ArtifactRevive) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
