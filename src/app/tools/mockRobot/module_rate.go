package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
)

type Rate struct {
	r *Robot
}

func NewRate(r *Robot) *Rate {
	rr := &Rate{
		r: r,
	}
	return rr
}

func (rr *Rate) C2LRateGetStatus() {
	cmsg := &cl.C2L_RateGetStatus{}
	//edit here
	rr.r.SendCmd(uint32(cl.ID_MSG_C2L_RateGetStatus), cmsg)
}

func (rr *Rate) L2CRateGetStatus(recv *cl.L2C_RateGetStatus) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (rr *Rate) C2LRateScore() {
	cmsg := &cl.C2L_RateScore{}
	param := rr.r.mock.GetCurrentParam()
	value, _ := jsonparser.GetInt(param, "score")
	cmsg.Score = uint32(value)
	rr.r.SendCmd(uint32(cl.ID_MSG_C2L_RateScore), cmsg)
}

func (rr *Rate) L2CRateScore(recv *cl.L2C_RateScore) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}
