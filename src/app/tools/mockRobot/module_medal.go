package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
)

type Medal struct {
	r *Robot
}

func NewMedal(r *Robot) *Medal {
	m := &Medal{
		r: r,
	}
	return m
}

func (m *Medal) C2LMedalGetData() {
	cmsg := &cl.C2L_MedalGetData{}
	//edit here
	m.r.SendCmd(uint32(cl.ID_MSG_C2L_MedalGetData), cmsg)
}

func (m *Medal) L2CMedalGetData(recv *cl.L2C_MedalGetData) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (m *Medal) C2LMedalReceiveAward() {
	cmsg := &cl.C2L_MedalReceiveAward{}
	param := m.r.mock.GetCurrentParam()
	level, _ := jsonparser.GetInt(param, "level")
	cmsg.Level = uint32(level)
	iType, _ := jsonparser.GetInt(param, "type")
	cmsg.Type = uint32(iType)
	jsonparser.ArrayEach(param, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		id, _ := jsonparser.GetInt(value)
		cmsg.TaskIds = append(cmsg.TaskIds, uint32(id))
	}, "ids")
	m.r.SendCmd(uint32(cl.ID_MSG_C2L_MedalReceiveAward), cmsg)
}

func (m *Medal) L2CMedalReceiveAward(recv *cl.L2C_MedalReceiveAward) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (m *Medal) L2CMedalUpdate(recv *cl.L2C_MedalUpdate) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
