package main

import (
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
)

type NoticeReqV5 struct {
	AppID     uint32  `json:"game_id,omitempty"`
	OpID      uint32  `json:"op_id,omitempty"`
	ChannelID uint32  `json:"channel_id,omitempty"`
	SDK       *SdkLog `json:"sdk,omitempty"`
	Time      string  `json:"time,omitempty"`
	Lang      string  `json:"lang,omitempty"`
	Sign      string  `json:"sign,omitempty" url:"-"`
}

func (r *Robot) GetNotice() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "url")
	if url == "" {
		return errors.New("GetNotice url is empty")
	}
	reqData := &NoticeReqV5{
		AppID:     r.appID,
		OpID:      r.op,
		ChannelID: 0,
		Time:      strconv.FormatUint(uint64(time.Now().Unix()), 10),
		Lang:      r.lang,
	}
	//reqData.Sign = BuildStructSignature(reqData, c.SignKey)
	data, err := json.Marshal(reqData)
	if err != nil {
		l4g.Errorf("json failed:%v", err)
		return err
	}
	l4g.Debugf("[GetNotice] send data reqData %v", reqData)

	result, err := DoHttp(HTTPPost, url, data, "application/json")

	l4g.Debug("[GetNotice]:%s getrespone :%s", r.uuid, result)
	if err != nil {
		l4g.Errorf("[GetNotice] failed:%v", err)
		return err
	}

	err = CheckHttpResp(result)
	if err != nil {
		l4g.Errorf("[GetRoleList] check resp error:%v", err)
		return err

	}

	return nil
}
