package main

import "app/protos/out/cl"

const (
	CustomCmd_Min                        uint32 = 0
	CustomCmd_HTTP_GetEagle              uint32 = 101
	CustomCmd_HTTP_GetServerList         uint32 = 102
	CustomCmd_HTTP_GetNotice             uint32 = 103
	CustomCmd_HTTP_SdkUserInit           uint32 = 104
	CustomCmd_HTTP_SdkPlatformLogin      uint32 = 105
	CustomCmd_HTTP_LoginCheck            uint32 = 106
	CustomCmd_HTTP_LoginCheck37          uint32 = 107
	CustomCmd_HTTP_GetRoleList           uint32 = 120
	CustomCmd_HTTP_SdkRecharge           uint32 = 130
	CustomCmd_HTTP_SdkGiftCode           uint32 = 131
	CustomCmd_HTTP_SdkCenterMe           uint32 = 132
	CustomCmd_HTTP_SdkChangePwdForPhone  uint32 = 133
	CustomCmd_HTTP_SdkChangePwdForNormal uint32 = 134
	CustomCmd_HTTP_SdkBind               uint32 = 135
	CustomCmd_HTTP_SdkUnbind             uint32 = 136
	CustomCmd_HTTP_SdkList               uint32 = 137
	CustomCmd_HTTP_SdkOrders             uint32 = 138
	CustomCmd_GST_TeamMove               uint32 = 301
	CustomCmd_TA_Min                     uint32 = 9900
	CustomCmd_TA_DeviceActive            uint32 = 9901
	CustomCmd_TA_RecordPoint             uint32 = 9902
	CustomCmd_TA_TrackKey                uint32 = 9903
	CustomCmd_Max                        uint32 = uint32(cl.ID_MSG_MIN)
)
