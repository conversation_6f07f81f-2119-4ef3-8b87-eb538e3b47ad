package main

import (
	"app/protos/out/ret"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitRobotTACommand() {
	gRobotCommandM.Register(CustomCmd_TA_DeviceActive, DeviceActive{})
	gRobotCommandM.Register(CustomCmd_TA_RecordPoint, RecordPoint{})
	gRobotCommandM.Register(CustomCmd_TA_TrackKey, TrackKey{})
}

type DeviceActive struct {
}

func (c DeviceActive) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	return SyncTA(robot, "device_active", map[string]interface{}{})
}

type RecordPoint struct {
}

func (c RecordPoint) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	param := robot.mock.GetCurrentParam()
	event, _ := jsonparser.GetString(param, "event")
	return SyncTA(robot, event, map[string]interface{}{})
}

type TrackKey struct {
}

func (c TrackKey) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	param := robot.mock.GetCurrentParam()
	value, _ := jsonparser.GetString(param, "guideid")
	properties := make(map[string]interface{})
	properties["guideid"] = value
	return SyncTA(robot, "trackKey", properties)
}

func SyncTA(robot *Robot, event string, properties map[string]interface{}) ret.RET {
	uid := robot.UUID()
	distinctID := robot.UUID()
	if err := robot.ta.Track(uid, distinctID, event, properties); err != nil {
		l4g.Errorf("[SyncTA] write %s track error: %v, properties: %+v", event, err, properties)
		return ret.RET_ERROR
	}

	l4g.Debugf("[SyncTA] write %s, properties: %+v", event, properties)

	err := robot.ta.Flush()
	if err != nil {
		l4g.Errorf("[SyncTA] Flush failed: %s", err.Error())
		return ret.RET_ERROR
	}
	return ret.RET_OK
}
