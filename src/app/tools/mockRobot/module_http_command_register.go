package main

import (
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitRobotHttpCommand() {
	gRobotCommandM.Register(CustomCmd_HTTP_GetEagle, GetEagleCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_GetServerList, GetServerListCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_GetNotice, GetNoticeCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkUserInit, SdkUserInitCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkPlatformLogin, SdkPlatformLoginCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_LoginCheck, LoginCheckCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_GetRoleList, GetRoleListCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkRecharge, SdkRechargeCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_LoginCheck37, LoginCheck37Command{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkGiftCode, SdkGiftCodeCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkCenterMe, SdkCenterMeCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkChangePwdForPhone, SdkChangePwdForPhoneCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkChangePwdForNormal, SdkChangePwdForNormalCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkBind, SdkBindCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkUnbind, SdkUnbindCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkList, SdkListCommand{})
	gRobotCommandM.Register(CustomCmd_HTTP_SdkOrders, SdkOrdersCommand{})
}

type GetEagleCommand struct {
}

func (c GetEagleCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.GetEagle(); err != nil {
		l4g.Error("robot:%s GetEagleCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type GetServerListCommand struct {
}

func (c GetServerListCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.GetServerList(); err != nil {
		l4g.Error("robot:%s GetServerListCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type GetNoticeCommand struct {
}

func (c GetNoticeCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.GetNotice(); err != nil {
		l4g.Error("robot:%s GetNoticeCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkUserInitCommand struct {
}

func (c SdkUserInitCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkUserInit(); err != nil {
		l4g.Error("robot:%s SdkUserInitCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkPlatformLoginCommand struct {
}

func (c SdkPlatformLoginCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkPlatformLogin(); err != nil {
		l4g.Error("robot:%s SdkPlatfromLoginCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type LoginCheckCommand struct {
}

func (c LoginCheckCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.LoginCheck(); err != nil {
		l4g.Error("robot:%s LoginCheckCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type LoginCheck37Command struct {
}

func (c LoginCheck37Command) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.LoginCheck37(); err != nil {
		l4g.Error("robot:%s LoginCheck37Command error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkRechargeCommand struct {
}

func (c SdkRechargeCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkRecharge(); err != nil {
		l4g.Error("robot:%s SdkRechargeCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type GetRoleListCommand struct {
}

func (c GetRoleListCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.GetRoleList(); err != nil {
		l4g.Error("robot:%s GetRoleListCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkGiftCodeCommand struct {
}

func (c SdkGiftCodeCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkGiftCode(); err != nil {
		l4g.Error("robot:%s SdkGiftCodeCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkCenterMeCommand struct {
}

func (c SdkCenterMeCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkCenterMe(); err != nil {
		l4g.Error("robot:%s SdkCenterMeCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkChangePwdForPhoneCommand struct {
}

func (c SdkChangePwdForPhoneCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkChangePwdForPhone(); err != nil {
		l4g.Error("robot:%s SdkChangePwdForPhoneCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkChangePwdForNormalCommand struct {
}

func (c SdkChangePwdForNormalCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkChangePwdForNormal(); err != nil {
		l4g.Error("robot:%s SdkChangePwdForNormalCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkBindCommand struct {
}

func (c SdkBindCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkBind(); err != nil {
		l4g.Error("robot:%s SdkBindCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkUnbindCommand struct {
}

func (c SdkUnbindCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkUnbind(); err != nil {
		l4g.Error("robot:%s SdkUnbindCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkListCommand struct {
}

func (c SdkListCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkList(); err != nil {
		l4g.Error("robot:%s SdkListCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}

type SdkOrdersCommand struct {
}

func (c SdkOrdersCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	if err := robot.SdkOrders(); err != nil {
		l4g.Error("robot:%s SdkOrdersCommand error %v", robot.uuid, err)
		return ret.RET_ERROR
	}
	return ret.RET_OK
}
