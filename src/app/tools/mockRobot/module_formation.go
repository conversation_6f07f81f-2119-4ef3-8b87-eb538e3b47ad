package main

import (
	//"app/logic/character"

	"app/protos/out/cl"
	"app/protos/out/ret"
	"strconv"
	"strings"

	"github.com/buger/jsonparser"
)

type Formation struct {
	r            *Robot
	formationMap map[uint32]*cl.Formation
}

func NewFormation(r *Robot) *Formation {
	a := &Formation{
		r:            r,
		formationMap: make(map[uint32]*cl.Formation),
	}
	return a
}

func (d *Formation) C2LGetFormation() {
	cmsg := &cl.C2L_GetFormation{}
	param := d.r.mock.GetCurrentParam()
	jsonparser.ArrayEach(param, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		id, _ := jsonparser.GetInt(value)
		cmsg.FormationIds = append(cmsg.FormationIds, uint32(id))
	}, "formation_ids")
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_GetFormation), cmsg)
}

func (d *Formation) L2CGetFormation(recv *cl.L2C_GetFormation) {
	if recv.Ret != uint32(ret.RET_OK) {
		return
	}
	for key, value := range recv.Formations {
		if _, exist := d.formationMap[key]; exist {
			d.formationMap[key] = value
		}
	}
}

func (d *Formation) parseId(config string, kind int32) (uint32, uint64) {
	values := strings.Split(config, "_")
	var pos uint32
	var hid uint64
	value, _ := strconv.Atoi(values[0])
	pos = uint32(value)
	hid, _ = strconv.ParseUint(values[1], 10, 64)
	if kind == 1 {
		//helpHero
	} else if kind == 2 {
		//selfHero
		hero := d.r.hero.GetHero(uint32(hid))
		hid = hero.GetData().GetId()
	}
	return pos, hid
}

func (d *Formation) C2LFormation() {
	cmsg := &cl.C2L_Formation{}
	param := d.r.mock.GetCurrentParam()
	value, _ := jsonparser.GetInt(param, "FuncId")
	cmsg.FormationId = uint32(value)
	cmsg.Formation = &cl.Formation{}
	cmsg.Formation.Id = cmsg.FormationId
	team := &cl.FormationTeamInfo{}
	jsonparser.ArrayEach(param, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		FormationInfo := &cl.FormationInfo{}
		FormationInfo.Pos, FormationInfo.Hid = d.parseId(string(value), 1)
		team.Info = append(team.Info, FormationInfo)
	}, "helpHero")
	jsonparser.ArrayEach(param, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		FormationInfo := &cl.FormationInfo{}
		FormationInfo.Pos, FormationInfo.Hid = d.parseId(string(value), 2)
		team.Info = append(team.Info, FormationInfo)
	}, "selfHero")

	cmsg.Formation.Teams = append(cmsg.Formation.Teams, team)
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_Formation), cmsg)
}

func (d *Formation) L2CFormation(recv *cl.L2C_Formation) {
	if recv.Ret != uint32(ret.RET_OK) {
		return
	}
	d.formationMap[recv.FormationId] = recv.Formation
}
