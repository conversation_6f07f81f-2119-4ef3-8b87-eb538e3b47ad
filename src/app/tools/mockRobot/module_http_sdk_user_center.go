package main

import (
	"encoding/json"
	"errors"
	"fmt"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SdkBaseResp struct {
	Code uint32 `json:"code,omitempty"`
}

// 模拟SDK的充值操作
func (r *Robot) SdkCenterMe() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	uid, _ := jsonparser.GetInt(param, "uid")
	if url == "" || appid == 0 || uid == 0 {
		return errors.New("[SDKCenterMe] no config value")
	}

	sendData := fmt.Sprintf("appID=%d&uid=%d", appid, uid)

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	if err != nil {
		l4g.Errorf("[SDKCenterMe] failed:%v", err)
		return errors.New("[SDKCenterMe] get uid error")
	}

	l4g.Info("result is %s", result)

	return nil
}

func (r *Robot) SdkChangePwdForPhone() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	uid, _ := jsonparser.GetInt(param, "uid")
	passwd, _ := jsonparser.GetString(param, "password")
	newPasswd, _ := jsonparser.GetString(param, "newPassword")
	if url == "" || appid == 0 || uid == 0 {
		return errors.New("[SDKChangePwdForPhone] no config value")
	}

	sendData := fmt.Sprintf("appID=%d&uid=%d&password=%s&newPassword=%s&code=1234", appid, uid, passwd, newPasswd)

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	if err != nil {
		l4g.Errorf("[SDKChangePwdForPhone] failed:%v", err)
		return errors.New("[SDKChangePwdForPhone] get uid error")
	}

	l4g.Info("result is %s", result)

	return nil
}

func (r *Robot) SdkChangePwdForNormal() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	uid, _ := jsonparser.GetInt(param, "uid")
	passwd, _ := jsonparser.GetString(param, "password")
	newPasswd, _ := jsonparser.GetString(param, "newPassword")
	if url == "" || appid == 0 || uid == 0 {
		return errors.New("[SDKChangePwdForNormal] no config value")
	}

	sendData := fmt.Sprintf("appID=%d&uid=%d&password=%s&newPassword=%s", appid, uid, passwd, newPasswd)

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	if err != nil {
		l4g.Errorf("[SDKChangePwdForNormal] failed:%v", err)
		return errors.New("[SDKChangePwdForNormal] get uid error")
	}

	l4g.Info("result is %s", result)

	resp := &SdkBaseResp{}
	err = json.Unmarshal(result, resp)
	if err != nil {
		l4g.Errorf("[SDKChangePwdForNormal] unmarshal failed:%v", err)
		return err
	}

	if resp.Code != 0 {
		l4g.Errorf("[SDKChangePwdForNormal] unmarshal failed:%v", err)
		return errors.New("[SDKChangePwdForNormal] code not 0")
	}

	l4g.Info("result is %s", result)

	return nil
}

func (r *Robot) SdkBind() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	uid, _ := jsonparser.GetInt(param, "uid")
	phoneNum, _ := jsonparser.GetString(param, "phoneNum")
	if url == "" || appid == 0 || uid == 0 {
		return errors.New("[SDKBind] no config value")
	}

	sendData := fmt.Sprintf("appID=%d&uid=%d&phoneNum=%s&code=1234", appid, uid, phoneNum)

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	if err != nil {
		l4g.Errorf("[SDKBind] failed:%v", err)
		return errors.New("[SDKBind] get uid error")
	}

	l4g.Info("result is %s", result)

	resp := &SdkBaseResp{}
	err = json.Unmarshal(result, resp)
	if err != nil {
		l4g.Errorf("[SDKBind] unmarshal failed:%v", err)
		return err
	}

	if resp.Code != 0 {
		l4g.Errorf("[SDKBind] unmarshal failed:%v", err)
		return errors.New("[SDKBind] code not 0")
	}

	l4g.Info("result is %s", result)

	return nil
}

func (r *Robot) SdkUnbind() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	uid, _ := jsonparser.GetInt(param, "uid")
	username, _ := jsonparser.GetString(param, "username")
	password, _ := jsonparser.GetString(param, "password")
	if url == "" || appid == 0 || uid == 0 {
		return errors.New("[SDKUnbind] no config value")
	}

	sendData := fmt.Sprintf("appID=%d&uid=%d&username=%s&password=%s&code=1234", appid, uid, username, password)

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	if err != nil {
		l4g.Errorf("[SDKUnbind] failed:%v", err)
		return errors.New("[SDKUnbind] get uid error")
	}

	l4g.Info("result is %s", result)

	resp := &SdkBaseResp{}
	err = json.Unmarshal(result, resp)
	if err != nil {
		l4g.Errorf("[SDKUnbind] unmarshal failed:%v", err)
		return err
	}

	if resp.Code != 0 {
		l4g.Errorf("[SDKUnbind] unmarshal failed:%v", err)
		return errors.New("[SDKUnbind] code not 0")
	}

	l4g.Info("result is %s", result)

	return nil
}

func (r *Robot) SdkList() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	uid, _ := jsonparser.GetInt(param, "uid")
	if url == "" || appid == 0 || uid == 0 {
		return errors.New("[SDKList] no config value")
	}

	sendData := fmt.Sprintf("appID=%d&uid=%d", appid, uid)

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	if err != nil {
		l4g.Errorf("[SDKList] failed:%v", err)
		return errors.New("[SDKList] get uid error")
	}

	l4g.Info("result is %s", result)

	resp := &SdkBaseResp{}
	err = json.Unmarshal(result, resp)
	if err != nil {
		l4g.Errorf("[SDKList] unmarshal failed:%v", err)
		return err
	}

	if resp.Code != 0 {
		l4g.Errorf("[SDKList] unmarshal failed:%v", err)
		return errors.New("[SDKList] code not 0")
	}

	l4g.Info("result is %s", result)

	return nil
}

func (r *Robot) SdkOrders() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	uid, _ := jsonparser.GetInt(param, "uid")
	startDate, _ := jsonparser.GetString(param, "startDate")
	if url == "" || appid == 0 || uid == 0 {
		return errors.New("[SDKOrders] no config value")
	}

	sendData := fmt.Sprintf("appID=%d&uid=%d&startTime=%s&currentPage=1&pageSize=20", appid, uid, startDate)

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	if err != nil {
		l4g.Errorf("[SDKOrders] failed:%v", err)
		return errors.New("[SDKOrders] get uid error")
	}

	l4g.Info("result is %s", result)

	resp := &SdkBaseResp{}
	err = json.Unmarshal(result, resp)
	if err != nil {
		l4g.Errorf("[SDKOrders] unmarshal failed:%v", err)
		return err
	}

	if resp.Code != 0 {
		l4g.Errorf("[SDKOrders] unmarshal failed:%v", err)
		return errors.New("[SDKOrders] code not 0")
	}

	l4g.Info("result is %s", result)

	return nil
}
