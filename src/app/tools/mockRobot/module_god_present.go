package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
)

type GodPresent struct {
	r *Robot
}

func NewGodPresent(r *Robot) *GodPresent {
	g := &GodPresent{
		r: r,
	}
	return g
}

func (g *GodPresent) C2LGodPresentGetData() {
	cmsg := &cl.C2L_GodPresentGetData{}
	//edit here
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GodPresentGetData), cmsg)
}

func (g *GodPresent) L2CGodPresentGetData(recv *cl.L2C_GodPresentGetData) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (g *GodPresent) C2LGodPresentRecvItem() {
	cmsg := &cl.C2L_GodPresentRecvItem{}
	//edit here
	param := g.r.mock.GetCurrentParam()
	id, _ := jsonparser.GetInt(param, "id")
	cmsg.Id = uint32(id)
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GodPresentRecvItem), cmsg)
}

func (g *GodPresent) L2CGodPresentRecvItem(recv *cl.L2C_GodPresentRecvItem) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (g *GodPresent) C2LGodPresentSummon() {
	cmsg := &cl.C2L_GodPresentSummon{}
	//edit here
	param := g.r.mock.GetCurrentParam()
	id, _ := jsonparser.GetInt(param, "id")
	cmsg.Id = uint32(id)
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GodPresentSummon), cmsg)
}

func (g *GodPresent) L2CGodPresentSummon(recv *cl.L2C_GodPresentSummon) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (g *GodPresent) C2LGodPresentRecvAwards() {
	cmsg := &cl.C2L_GodPresentRecvAwards{}
	//edit here
	param := g.r.mock.GetCurrentParam()
	id, _ := jsonparser.GetInt(param, "id")
	cmsg.Id = uint32(id)
	selectGroupKey, _ := jsonparser.GetInt(param, "select_group_key")
	cmsg.SelectGroupKey = uint32(selectGroupKey)
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GodPresentRecvAwards), cmsg)
}

func (g *GodPresent) L2CGodPresentRecvAwards(recv *cl.L2C_GodPresentRecvAwards) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
