package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGSTCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GSTGetData), C2LGSTGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GSTGetData), L2CGSTGetDataCommand{})
	gRobotCommandM.Register(CustomCmd_GST_TeamMove, C2CSGSTTeamMove{})
}

type C2LGSTGetDataCommand struct {
}

func (c C2LGSTGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.gst.C2LGSTGetData()
	return ret.RET_OK
}

type L2CGSTGetDataCommand struct {
}

func (c L2CGSTGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GSTGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2C_GSTGetData unmarshal error: %s", err)
		return ret.RET_ERROR
	}

	return ret.RET(recv.Ret)
}

type C2CSGSTTeamMove struct {
}

func (c C2CSGSTTeamMove) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	return robot.gst.C2CSGSTTeamMove()
}
