package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotFriendCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_FriendInfo), C2LFriendInfoCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_FriendInfo), L2CFriendInfoCommand{})
}

type C2LFriendInfoCommand struct {
}

func (c C2LFriendInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.friend.C2LFriendInfo()
	return ret.RET_OK
}

type L2CFriendInfoCommand struct {
}

func (c L2CFriendInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_FriendInfo{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CFriendInfoCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.friend.L2CFriendInfo(recv)
	return ret.RET(recv.Ret)
}
