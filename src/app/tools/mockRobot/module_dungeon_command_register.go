package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotDungeonCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_DungeonFight), C2LDungeonFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DungeonFight), L2CDungeonFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_DungeonRecvAward), C2LDungeonRecvAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DungeonRecvAward), L2CDungeonRecvAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_DungeonSpeedRecvAward), C2LDungeonSpeedRecvAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DungeonSpeedRecvAward), L2CDungeonSpeedRecvAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_DungeonPreview), C2LDungeonPreviewCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DungeonPreview), L2CDungeonPreviewCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_Dungeon), L2CDungeonCommand{})
}

type C2LDungeonFightCommand struct {
}

func (c C2LDungeonFightCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	robot.dungeon.C2LDungeonFight()
	return ret.RET_OK
}

type L2CDungeonFightCommand struct {
}

func (c L2CDungeonFightCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_DungeonFight{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDungeonFightCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.dungeon.L2CDungeonFight(recv)
	return ret.RET(recv.Ret)
}

type C2LDungeonRecvAwardCommand struct {
}

func (c C2LDungeonRecvAwardCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	robot.dungeon.C2LDungeonRecvAward()
	return ret.RET_OK
}

type L2CDungeonRecvAwardCommand struct {
}

func (c L2CDungeonRecvAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_DungeonRecvAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDungeonRecvAwardCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.dungeon.L2CDungeonRecvAward(recv)
	return ret.RET(recv.Ret)
}

type C2LDungeonSpeedRecvAwardCommand struct {
}

func (c C2LDungeonSpeedRecvAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {

	robot.dungeon.C2LDungeonSpeedRecvAward()
	return ret.RET_OK
}

type L2CDungeonSpeedRecvAwardCommand struct {
}

func (c L2CDungeonSpeedRecvAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_DungeonSpeedRecvAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDungeonSpeedRecvAwardCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.dungeon.L2CDungeonSpeedRecvAward(recv)
	return ret.RET(recv.Ret)
}

type C2LDungeonPreviewCommand struct {
}

func (c C2LDungeonPreviewCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	robot.dungeon.C2LDungeonPreview()
	return ret.RET_OK
}

type L2CDungeonPreviewCommand struct {
}

func (c L2CDungeonPreviewCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_DungeonPreview{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDungeonPreviewCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.dungeon.L2CDungeonPreview(recv)
	return ret.RET(recv.Ret)
}

type L2CDungeonCommand struct {
}

func (c L2CDungeonCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_Dungeon{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDungeonCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.dungeon.L2CDungeon(recv)
	return ret.RET(recv.Ret)
}
