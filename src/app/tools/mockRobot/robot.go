package main

import (
	"container/list"
	"errors"
	"fmt"
	"math"
	"time"

	"gitlab.qdream.com/kit/sea/math/rand"

	"app/goxml"
	"app/protos/out/cg"
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/zebra/parse"
	"gitlab.qdream.com/kit/sea/zebra/tcp"

	"github.com/ThinkingDataAnalytics/go-sdk/thinkingdata"
	l4g "github.com/ivanabc/log4go"
)

const (
	stateNone = iota //创建的机器人初始化
	stateInit
	stateLogin  //开始登录了
	stateQueue  //发现在排队中
	stateCreate //开始创角
	stateOnline //登录完成
)

const (
	//天神数数
	APP_ID string = "51898b70aef64d0db745d0b03898746e"
	TA_URL string = "http://170.106.154.61/logbus"

	//斗罗数数
	// APP_ID string = "b1cbba78dcc14bd2b875507403adc542"
	// TA_URL string = "http://117.50.94.34:8991/logbus"
)

type Robot struct {
	manager *RobotM

	act        *actor.Actor
	broker     *tcp.Broker
	cseq, sseq uint32
	loginUuid  string
	uuid       string
	ctx        *ctx.Group
	cfg        *tcp.Config
	msgs       *list.List
	rd         *rand.Rand

	syncCookie   uint64
	state        int
	cryptoState  bool
	isFinished   bool
	triggerQueue bool
	isReconnect  bool

	running bool

	appID, opGroup, op uint32
	pid, gid           uint32
	lang               string
	gateway            string
	serverID           uint64
	token              string //应该在登录验证之后设定
	sdkToken           string //sdk返回的token，验证用
	sdkAppMes          *SdkAppMes

	userdata *cl.User

	base        *Base
	mock        *Mock
	carnival    *Carnival
	shop        *Shop
	medal       *Medal
	formation   *Formation
	guidance    *Guidance
	dungeon     *Dungeon
	hero        *Hero
	equip       *Equip
	summon      *Summon
	task        *Task
	artifact    *Artifact
	battle      *Battle
	emblem      *Emblem
	guildTalent *GuildTalent
	sevenDay    *SevenDay
	friend      *Friend
	handBooks   *HandBooks
	goldBuy     *GoldBuy
	arena       *Arena
	rate        *Rate
	godPresent  *GodPresent
	gm          *GM
	guild       *Guild
	gst         *GST

	ta thinkingdata.TDAnalytics
}

func NewRobot(ctx *ctx.Group, m *RobotM, cfg *tcp.Config, uuid string, appID, opGroup, op uint32, lang string, sdkApp *SdkAppMes) *Robot {
	robot := &Robot{
		ctx:       ctx,
		manager:   m,
		cfg:       cfg,
		loginUuid: uuid,
		uuid:      uuid,
		state:     stateNone,
		msgs:      list.New(),
		serverID:  *flagNodeID,
		rd:        rand.New(time.Now().UnixNano()),
		appID:     appID,
		opGroup:   opGroup,
		op:        op,
		lang:      lang,
		sdkAppMes: sdkApp,
		userdata:  &cl.User{},
	}
	robot.act = actor.NewActor(robot.ctx.CreateChild(), &actor.Config{
		Kind:      111, //TODO 先随意定一个
		KindName:  "robot",
		MsgQSize:  1024,
		Rate:      500,
		TimerSize: 512,
	})
	robot.SetToken(genToken(uuid))
	robot.InitModule()
	robot.state = stateInit
	robot.InitTA()
	return robot
}

func (r *Robot) Init(broker *tcp.Broker) {
	r.broker = broker
	l4g.Info("robot %s connect init success localaddr:%s remoteaddr:%s",
		r.loginUuid, r.broker.LocalAddr(), r.broker.RemoteAddr())
	r.act.AddMessage(connectSucMessage{})
}

func (r *Robot) InitModule() {
	r.base = NewBase(r)
	r.mock = NewMock(r)
	r.carnival = NewCarnival(r)
	r.shop = NewShop(r)
	r.medal = NewMedal(r)
	r.formation = NewFormation(r)
	r.guidance = NewGuidance(r)
	r.dungeon = NewDungeon(r)
	r.hero = NewHeroM(r)
	r.equip = NewEquip(r)
	r.summon = NewSummon(r)
	r.task = NewTask(r)
	r.artifact = NewArtifact(r)
	r.battle = NewBattle(r)
	r.emblem = NewEmblem(r)
	r.guildTalent = NewGuildTalent(r)
	r.sevenDay = NewSevenDay(r)
	r.friend = NewFriend(r)
	r.handBooks = NewHandBooks(r)
	r.goldBuy = NewGoldBuy(r)
	r.arena = NewArena(r)
	r.rate = NewRate(r)
	r.godPresent = NewGodPresent(r)
	r.gm = NewGM(r)
	r.guild = NewGuild(r)
	r.gst = NewGST(r)
}

func (r *Robot) InitTA() {
	consumer, err := thinkingdata.NewBatchConsumer(TA_URL, APP_ID)
	if err != nil {
		l4g.Errorf("[InitTA] InitTA failed, err: %s", err.Error())
		return
	}
	r.ta = thinkingdata.New(consumer)
}

func (r *Robot) IsOnline() bool {
	return r.state == stateOnline
}

func (r *Robot) Process(buf []byte) {
	ph := new(parse.PackHead)
	parse.DecodePackHead(buf, ph)
	data := buf[parse.PackHeadSize:ph.Length]
	r.sseq = ph.SSeq

	r.act.AddMessage(respMessage{r, ph, data})

	/*
		result := gRobotCommandM.Dispatcher(r, ph, data)
		r.mock.TriggerRspCmd(ph.Cmd, result)
	*/
}

func (r *Robot) Send(ph *parse.PackHead, msg interface{}) {
	msgs := parse.NewMessageBatch()
	parse.AppendMessageBatch(msgs, ph, msg)
	r.broker.WriteMessages(msgs)
}

func (r *Robot) SendCmd(cmdID uint32, msg interface{}) {
	r.cseq++
	ph := &parse.PackHead{
		Cmd:  cmdID,
		CSeq: r.cseq,
		SSeq: r.sseq,
	}
	if r.cryptoState {
		ph.Flags |= parse.MsgFlagCrypto
	}
	r.Send(ph, msg)
}

func (r *Robot) Run() {
	defer r.stop()
	// r.connect(true)
	if *flagQps {
		r.StartMsg()
	} else {
		r.StartTimer()
	}
	r.act.Run(r)
	r.running = true
}

func (r *Robot) stop() {
	r.ctx.Stop()
	l4g.Infof("[mockRobot] stop %s", r.uuid)
	r.ctx.Wait()
	r.ctx.Finish()
}

func (r *Robot) Stop() {
	if !r.running {
		go r.act.Run(r)
		r.stop()
	}
}

func (r *Robot) quit() {
	r.isFinished = true
	l4g.Infof("[mockRobot] quit %s", r.uuid)
	r.ctx.Stop()
}

func (r *Robot) connect(first bool) error {
	if tcp.ClientServe(r.ctx.CreateChild(), r, r.cfg, 10*time.Second) {
		/*
			if !first {
				r.mock.AddLogin()
			}
			return
		*/
		return nil
	}

	l4g.Errorf("[mockRobot] connect server error: %s %+v", r.loginUuid, r.cfg)
	//r.addReconnectTimer(first)
	return errors.New("connect gateway error")

}

type reconnectTimer struct {
	r     *Robot
	first bool
}

func (r *reconnectTimer) TimeOut(now int64) {
	l4g.Infof("start reconnect, loginUuid: %s", r.r.loginUuid)
	r.r.connect(r.first)
}

func (r *Robot) finish(success bool) {
	l4g.Info("[mockRobot] finished. loginUuid: %s", r.uuid)
	r.isFinished = true
	if success {
		r.manager.AddSuc()
	} else {
		println(fmt.Sprintf("robot uuid:%s fail! msgIndex:%d cmd:%d", r.uuid, r.mock.msgIndex, r.mock.reqCmd))
		r.manager.AddFail()
	}
	r.ctx.Stop()
}

func (r *Robot) addReconnectTimer(first bool) {
	interval := int64(r.rd.Intn(5))
	r.act.AddTimer(&reconnectTimer{r, first}, interval+time.Now().Unix(), 0)
}

type reconnectMessage struct {
}

func (r *reconnectMessage) Process(param actor.Receiver) error {
	robot := param.(*Robot)
	robot.addReconnectTimer(false)
	return nil
}

type respMessage struct {
	robot *Robot
	ph    *parse.PackHead
	data  []byte
}

func (r respMessage) Process(param actor.Receiver) error {
	result := gRobotCommandM.Dispatcher(r.robot, r.ph, r.data)
	r.robot.mock.TriggerRspCmd(r.ph.Cmd, result)
	return nil
}

type connectSucMessage struct{}

func (r connectSucMessage) Process(param actor.Receiver) error {
	robot := param.(*Robot)
	robot.SetState(stateLogin)
	ph := &parse.PackHead{
		Cmd:  uint32(cg.ID_MSG_C2G_Login),
		CSeq: robot.cseq,
		SSeq: robot.sseq,
	}

	msg := &cg.C2G_Login{
		ServerId:   robot.serverID,
		Token:      robot.token, //genToken(robot.uuid),
		SyncCookie: robot.syncCookie,
		Version:    math.MaxUint64,
	}
	robot.Send(ph, msg)
	return nil
}

func (r *Robot) UUID() string     { return r.uuid }
func (r *Robot) SetState(st int)  { r.state = st }
func (r *Robot) ID() uint64       { return r.userdata.Id }
func (r *Robot) Name() string     { return r.userdata.Name }
func (r *Robot) Level() uint32    { return r.userdata.Level }
func (r *Robot) Rand() *rand.Rand { return r.rd }

func (r *Robot) Online(cookie uint64, sync uint32) {
	r.syncCookie = cookie
	if sync != 0 {
		var found bool
		for e := r.msgs.Front(); e != nil; e = e.Next() {
			msg := e.Value.(*parse.Message)
			if found {
				ph := &parse.PackHead{
					Cmd:  msg.Cmd,
					CSeq: msg.CSeq,
					SSeq: r.sseq,
				}
				r.Send(ph, msg.Data)
			}
			if msg.CSeq == sync {
				l4g.Info("%s msg sync start seq: %d", r.uuid, sync)
				found = true
			}
		}
	}
	r.SetState(stateOnline)
}

func (r *Robot) IncreaseResource(res *cl.Resource) {
	l4g.Debugf("robot :%s get new res:%+v", r.uuid, res)
	switch res.Type {
	case uint32(common.RESOURCE_DIAMOND):
		r.userdata.Diamond += uint64(res.Count)
	case uint32(common.RESOURCE_HERO):
		heroInfo := goxml.GetData().HeroInfoM.Index(res.Value)
		r.hero.heroes[res.Id] = &cl.Hero{
			Data: &cl.HeroBody{
				Id:    res.Id,
				SysId: res.Value,
				Star:  heroInfo.Star,
				Level: 1,
			},
		}
	case uint32(common.RESOURCE_EQUIP):
		r.equip.Add(
			&cl.Equipment{
				Id:    res.Id,
				SysId: res.Value,
			})
	}
}

func (r *Robot) ReduceResource(res *cl.Resource) {
	switch res.Type {
	case uint32(common.RESOURCE_DIAMOND):
		r.userdata.Diamond -= uint64(res.Count)
	}
}

func (r *Robot) Reconnect() {
	r.SetState(stateNone)
	r.SetReconnectStatus(true)
	r.broker.Stop()
	l4g.Info("%s trigger offline", r.loginUuid)
}

func (r *Robot) Close() {
	if r.isFinished {
		return
	}
	r.SetState(stateNone)
	l4g.Info("[mockRobot] connect close: %s %s add reconnect", r.loginUuid, r.cfg.Address)
	r.act.AddMessage(&reconnectMessage{})
}

func (r *Robot) SetGateway(url string) {
	r.gateway = url
	r.cfg.Address = url
}

func (r *Robot) SetServerID(serverID uint64) {
	l4g.Infof("robot:%s SetServerID:%d", r.uuid, serverID)
	r.serverID = serverID
	//内网测试临时赋值
	// r.serverID = *flagNodeID
}

func (r *Robot) SetToken(token string) {
	r.token = token
}

func (r *Robot) SetUuid(uid string) {
	r.uuid = uid
}

func (r *Robot) SetSDKToken(token string) {
	r.sdkToken = token
}

func (r *Robot) GetReconnectStatus() bool {
	return r.isReconnect
}

func (r *Robot) SetReconnectStatus(status bool) {
	r.isReconnect = status
}
