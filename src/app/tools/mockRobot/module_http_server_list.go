package main

import (
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

type SdkLog struct {
	//ID       string `json:"id,omitempty"`
	DeviceID string `json:"device_id,omitempty"`
}

type ServerListReqV4 struct {
	AppID   uint32  `json:"game_id,omitempty"`
	OpGroup uint32  `json:"op_group,omitempty"`
	Op      uint32  `json:"op_id,omitempty"`
	SDK     *SdkLog `json:"sdk,omitempty"`
}

type BaseResp struct {
	Code uint32 `json:"code,omitempty"`
	Msg  string `json:"msg,omitempty"`
}

type ServerListRespV4 struct {
	BaseResp
	Data ServerListDataV4 `json:"data,omitempty"`
}

type ServerListDataV4 struct {
	OpGroup string   `json:"op_group,omitempty"`
	Servers []string `json:"servers,omitempty"`
	IsShow  bool     `json:"is_show,omitempty"`
}

func (r *Robot) GetServerList() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "url")
	if url == "" {
		return errors.New("GetServerList url is empty")
	}
	reqData := &ServerListReqV4{
		AppID:   r.appID,
		OpGroup: r.opGroup,
		Op:      r.op,
	}
	//reqData.Sign = BuildStructSignature(reqData, c.SignKey)
	data, err := json.Marshal(reqData)
	if err != nil {
		l4g.Errorf("json failed:%v", err)
		return err
	}
	l4g.Debugf("[GetServerList] send data reqData %v", reqData)

	result, err := DoHttp(HTTPPost, url, data, "application/json")

	l4g.Debugf("[GetServerList] getrespone %s", result)
	if err != nil {
		l4g.Errorf("[GetServerList] failed:%v", err)
		return err
	}
	resp := &ServerListRespV4{}
	err = json.Unmarshal(result, resp)
	if err != nil {
		l4g.Errorf("[GetServerList] unmarshal failed:%v", err)
		return err
	}

	l4g.Debugf("[GetServerList]:%s list :%s", r.uuid, result)
	if len(resp.Data.Servers) == 0 {
		l4g.Errorf("[GetServerList] no find server:%v", resp)
		return err
	}

	rd := rand.New(time.Now().UnixNano())
	randServer := rd.Intn(len(resp.Data.Servers))
	server := resp.Data.Servers[randServer]
	serverArr := strings.Split(server, "|")
	serverID, _ := strconv.Atoi(serverArr[0])
	r.SetServerID(uint64(serverID))
	return nil
}
