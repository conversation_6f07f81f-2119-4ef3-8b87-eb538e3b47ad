package main

import (
	"errors"
	"fmt"
	"time"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

// 模拟SDK的充值操作
func (r *Robot) SdkGiftCode() error {
	param := r.mock.GetCurrentParam()
	url, _ := jsonparser.GetString(param, "sdkUrl")
	appid, _ := jsonparser.GetInt(param, "appid")
	giftid, _ := jsonparser.GetInt(param, "giftid")
	if url == "" || giftid == 0 || appid == 0 {
		return errors.New("[SDKGiftCode] no config value")
	}

	var uid uint64
	_, err := fmt.Sscanf(r.uuid, "robot%d", &uid)
	if err != nil {
		fmt.Println("Error:", err)
		return errors.New("[SDKGiftCode] get uid error")
	}

	uid = uid + 1

	sendData := fmt.Sprintf("appID=%d&uid=%d&giftID=%d&timestamp=%d", appid, uid, giftid, time.Now().Unix())

	l4g.Info("send data is %s", sendData)
	result, err := DoHttp(HTTPPost, url, util.Bytes(sendData), "application/x-www-form-urlencoded")
	l4g.Infof("[SDKGiftCode] uuid:%s uid:%d, appID:%d giftid %d", r.uuid, uid, appid, giftid)
	if err != nil {
		l4g.Errorf("[SDKGiftCode] failed:%v", err)
		return errors.New("[SDKGiftCode] get uid error")
	}

	l4g.Info("result is %s", result)

	return nil
}
