package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotActivityRechargeCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ActivityRechargeGet), C2LActivityRechargeGetCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ActivityRechargeGet), L2CActivityRechargeGetCommand{})
}

type C2LActivityRechargeGetCommand struct {
}

func (c C2LActivityRechargeGetCommand) Execute(robot *Robot, ph *parse.PackHead, _ []byte) ret.RET {
	cmsg := &cl.C2L_ActivityRechargeGet{}
	robot.SendCmd(ph.Cmd, cmsg)
	return ret.RET_OK
}

type L2CActivityRechargeGetCommand struct {
}

func (c L2CActivityRechargeGetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ActivityRechargeGet{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CActivityRechargeGetCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
