package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotPushGiftCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_PushGiftGetData), L2CPushGiftGetDataCommand{})
}

type L2CPushGiftGetDataCommand struct {
}

func (c L2CPushGiftGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_PushGiftGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CPushGiftGetDataCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
