package main

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"net/url"
	"sort"
	"strings"

	l4g "github.com/ivanabc/log4go"
)

const (
	HTTPGet  = "GET"
	HTTPPost = "POST"
)

func DoHttp(method string, url string, data []byte, contentType string) ([]byte, error) {
	//urlStr := c.URL + "/serverlist/v3"
	req, err := http.NewRequest(method, url, bytes.NewBuffer(data))
	if err != nil {
		l4g.Errorf("req failed:%v %s", url, err)
		return nil, err
	}
	if contentType != "" {
		//req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Content-Type", contentType)
	}

	client := &http.Client{
		Transport: &http.Transport{
			DisableKeepAlives: true,
		},
	}
	resp, err := client.Do(req)
	if err != nil {
		l4g.Errorf("req failed:%v %s", url, err)
		return nil, err
	}
	defer resp.Body.Close()
	if err != nil {
		l4g.Errorf("req failed:%v %s", url, err)
		return nil, err
	}
	result, _ := ioutil.ReadAll(resp.Body)
	return result, nil
}

func CheckHttpResp(result []byte) error {
	resp := &BaseResp{}
	err := json.Unmarshal(result, resp)
	if err != nil {
		l4g.Errorf("CheckHttpResp unmarshal failed:%v", err)
		return err
	}

	if resp.Code != 200 {
		l4g.Errorf("[GetRoleList] cod error :%d", resp.Code)
		return errors.New("http code error")
	}

	return nil
}

func getSignStr(values url.Values) string {
	var build strings.Builder
	keys := make([]string, 0, len(values))
	for key := range values {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	for _, key := range keys {
		if key != "sign" {
			build.WriteString(key)
			build.WriteString("=")
			build.WriteString(values.Get(key))
			build.WriteString("&")
		}
	}
	return strings.TrimRight(build.String(), "&")
}

func MD5(data string) string {
	d := []byte(data)
	m := md5.New()
	m.Write(d)
	return hex.EncodeToString(m.Sum(nil))
}
