package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotArenaCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ArenaInfo), C2LArenaInfoCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArenaInfo), L2CArenaInfoCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ArenaRefresh), C2LArenaRefreshCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArenaRefresh), L2CArenaRefreshCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ArenaFight), C2LArenaFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArenaFight), L2CArenaFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArenaLogList), L2CArenaLogListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArenaLike), L2CArenaLikeCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArenaRank), L2CArenaRankCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ArenaRecvAward), C2LArenaRecvAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ArenaRecvAward), L2CArenaRecvAwardCommand{})
}

type C2LArenaInfoCommand struct {
}

func (a C2LArenaInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.arena.C2LArenaInfo()
	return ret.RET_OK
}

type L2CArenaInfoCommand struct {
}

func (a L2CArenaInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArenaInfo{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArenaInfoCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.arena.L2CArenaInfo(recv)
	return ret.RET(recv.Ret)
}

type C2LArenaRefreshCommand struct {
}

func (a C2LArenaRefreshCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {

	robot.arena.C2LArenaRefresh()
	return ret.RET_OK
}

type L2CArenaRefreshCommand struct {
}

func (a L2CArenaRefreshCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArenaRefresh{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArenaRefreshCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.arena.L2CArenaRefresh(recv)
	return ret.RET(recv.Ret)
}

type C2LArenaFightCommand struct {
}

func (a C2LArenaFightCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.arena.C2LArenaFight()
	return ret.RET_OK
}

type L2CArenaFightCommand struct {
}

func (a L2CArenaFightCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArenaFight{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArenaFightCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.arena.L2CArenaFight(recv)
	return ret.RET(recv.Ret)
}

type L2CArenaLogListCommand struct {
}

func (a L2CArenaLogListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArenaLogList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArenaLogListCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.arena.L2CArenaLogList(recv)
	return ret.RET(recv.Ret)
}

type L2CArenaLikeCommand struct {
}

func (a L2CArenaLikeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArenaLike{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArenaLikeCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.arena.L2CArenaLike(recv)
	return ret.RET(recv.Ret)
}

type L2CArenaRankCommand struct {
}

func (a L2CArenaRankCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArenaRank{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArenaRankCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.arena.L2CArenaRank(recv)
	return ret.RET(recv.Ret)
}

type C2LArenaRecvAwardCommand struct {
}

func (a C2LArenaRecvAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.arena.C2LArenaRecvAward()
	return ret.RET_OK
}

type L2CArenaRecvAwardCommand struct {
}

func (a L2CArenaRecvAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_ArenaRecvAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CArenaRecvAwardsCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
