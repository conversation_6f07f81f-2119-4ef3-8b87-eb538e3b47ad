package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGoldBuyCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GoldBuyGet), C2LGoldBuyGetCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GoldBuyGet), L2CGoldBuyGetCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GoldBuyGetGold), C2LGoldBuyGetGoldCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GoldBuyGetGold), L2CGoldBuyGetGoldCommand{})
}

type C2LGoldBuyGetCommand struct {
}

func (c C2LGoldBuyGetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.goldBuy.C2LGoldBuyGet()
	return ret.RET_OK
}

type L2CGoldBuyGetCommand struct {
}

func (c L2CGoldBuyGetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GoldBuyGet{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGoldBuyGetCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.goldBuy.L2CGoldBuyGet(recv)
	return ret.RET(recv.Ret)
}

type C2LGoldBuyGetGoldCommand struct {
}

func (c C2LGoldBuyGetGoldCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.goldBuy.C2LGoldBuyGetGold()
	return ret.RET_OK
}

type L2CGoldBuyGetGoldCommand struct {
}

func (c L2CGoldBuyGetGoldCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GoldBuyGetGold{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGoldBuyGetGoldCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.goldBuy.L2CGoldBuyGetGold(recv)
	return ret.RET(recv.Ret)
}
