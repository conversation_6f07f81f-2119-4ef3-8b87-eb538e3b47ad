package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
)

type Task struct {
	r        *Robot
	taskInfo *cl.L2C_TaskGetInfo
}

func NewTask(r *Robot) *Task {
	a := &Task{
		r: r,
	}
	return a
}

func (d *Task) C2LTaskGetInfo() {
	cmsg := &cl.C2L_TaskGetInfo{}
	//edit here
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_TaskGetInfo), cmsg)
}

func (d *Task) L2CTaskGetInfo(recv *cl.L2C_TaskGetInfo) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	d.taskInfo = recv
}

func (d *Task) C2LTaskReceiveAward() {
	cmsg := &cl.C2L_TaskReceiveAward{}
	//edit here
	param := d.r.mock.GetCurrentParam()
	jsonparser.ArrayEach(param, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		id, _ := jsonparser.GetInt(value)
		cmsg.Ids = append(cmsg.Ids, uint32(id))
	}, "ids")
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_TaskReceiveAward), cmsg)
}

func (d *Task) L2CTaskReceiveAward(recv *cl.L2C_TaskReceiveAward) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *Task) L2CTaskUpdate(recv *cl.L2C_TaskUpdate) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *Task) C2LDispatchReceiveTask() {
	cmsg := &cl.C2L_DispatchReceiveTask{}
	//edit here
	param := d.r.mock.GetCurrentParam()
	id, _ := jsonparser.GetInt(param, "id")
	cmsg.Id = uint64(id)
	jsonparser.ArrayEach(param, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		id, _ := jsonparser.GetInt(value)
		hero := d.r.hero.GetHero(uint32(id))
		cmsg.HeroIds = append(cmsg.HeroIds, hero.Data.Id)
	}, "hids")
	d.r.SendCmd(uint32(cl.ID_MSG_C2L_DispatchReceiveTask), cmsg)
}
