package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
)

type Guidance struct {
	r    *Robot
	data *cl.Guidance
}

func NewGuidance(r *Robot) *Guidance {
	g := &Guidance{
		r: r,
	}
	return g
}

func (g *Guidance) IsGroupFinished(groupID uint32) bool {
	if g.data == nil {
		return false
	}
	for _, group := range g.data.Finished {
		if group == groupID {
			return true
		}
	}
	return false
}

func (g *Guidance) findOngoingGroup(groupID uint32) *cl.GuidanceGroup {
	if g.data == nil {
		return nil
	}
	for _, data := range g.data.Ongoing {
		if data.Group == groupID {
			return data
		}
	}
	return nil
}
func (g *Guidance) update(guidanceData *cl.GuidanceGroup) {
	if g.data == nil {
		g.data = &cl.Guidance{}
	}
	data := g.findOngoingGroup(guidanceData.Group)
	if data != nil {
		*data = *guidanceData
	} else {
		g.data.Ongoing = append(g.data.Ongoing, guidanceData)
	}
}

func (g *Guidance) C2LGuidanceList() {
	cmsg := &cl.C2L_GuidanceList{}
	//edit here
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GuidanceList), cmsg)
}

func (g *Guidance) L2CGuidanceList(recv *cl.L2C_GuidanceList) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	g.data = recv.Guidance

	//edit here
}

func (g *Guidance) GetNextNode(node uint32) uint32 {
	if node == 0 {
		return 1001
	}
	info := goxml.GetData().GuidanceInfoM.Index(node)
	if info == nil {
		return 0
	}
	if goxml.GetData().GuidanceInfoM.Index(node+1) != nil {
		return node + 1
	}
	if goxml.GetData().GuidanceInfoM.IsValidGroup(info.Group + 1) {
		return (info.Group+1)*100 + 1
	}
	return 0
}

func (g *Guidance) C2LGuidanceFinishNode() {
	cmsg := &cl.C2L_GuidanceFinishStep{}
	param := g.r.mock.GetCurrentParam()
	group, _ := jsonparser.GetInt(param, "group")
	step, _ := jsonparser.GetInt(param, "step")
	cmsg.Group = uint32(group)
	cmsg.Step = uint32(step)
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GuidanceFinishStep), cmsg)
}

func (g *Guidance) L2CGuidanceFinishNode(recv *cl.L2C_GuidanceFinishStep) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	g.update(recv.Data)
	//edit here
}

func (g *Guidance) C2LGuidanceFinishGroup() {
	cmsg := &cl.C2L_GuidanceFinishGroup{}
	//edit here
	param := g.r.mock.GetCurrentParam()
	value, _ := jsonparser.GetInt(param, "group")
	cmsg.Group = uint32(value)
	g.r.SendCmd(uint32(cl.ID_MSG_C2L_GuidanceFinishGroup), cmsg)
}

func (g *Guidance) L2CGuidanceFinishGroup(recv *cl.L2C_GuidanceFinishGroup) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	if g.data == nil {
		g.data = &cl.Guidance{}
	}
	g.data.Finished = append(g.data.Finished, recv.Group)
	//edit here
}
