package main

func InitRobotChatCommand() {
	//gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_ChatGetPrivateMessageNum), C2LChatGetPrivateMessageNumCommand{})
	//gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ChatGetPrivateMessageNum), L2CChatGetPrivateMessageNumCommand{})
}

//type C2LChatGetPrivateMessageNumCommand struct {
//}
//
//func (c C2LChatGetPrivateMessageNumCommand) Execute(robot *Robot, ph *parse.PackHead, _ []byte) ret.RET {
//	cmsg := &cl.C2L_ChatGetPrivateMessageNum{}
//	robot.SendCmd(ph.Cmd, cmsg)
//	return ret.RET_OK
//}
//
//type L2CChatGetPrivateMessageNumCommand struct {
//}
//
//func (c L2CChatGetPrivateMessageNumCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
//	recv := &cl.L2C_ChatGetPrivateMessageNum{}
//	if err := proto.Unmarshal(data, recv); err != nil {
//		l4g.Error("L2CChatGetPrivateMessageNumCommand unmarshal error: %s", err)
//		return ret.RET_ERROR
//	}
//	return ret.RET(recv.Ret)
//}
