package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotSummonCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SummonGetData), L2CSummonGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_Summon), C2LSummonCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_Summon), L2CSummonCommand{})
}

type L2CSummonGetDataCommand struct {
}

func (c L2CSummonGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_SummonGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSummonGetDataCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}

type C2LSummonCommand struct {
}

func (c C2LSummonCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	robot.summon.C2LSummon()
	return ret.RET_OK
}

type L2CSummonCommand struct {
}

func (c L2CSummonCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_Summon{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSummonCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.summon.L2CSummon(recv)
	return ret.RET(recv.Ret)
}
