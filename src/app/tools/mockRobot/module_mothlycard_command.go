package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotMonthlyCardCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_MonthlyCardGetData), C2LMonthlyCardGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MonthlyCardGetData), L2CMonthlyCardGetDataCommand{})
}

type C2LMonthlyCardGetDataCommand struct {
}

func (c C2LMonthlyCardGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, _ []byte) ret.RET {
	cmsg := &cl.C2L_MonthlyCardGetData{}
	robot.SendCmd(ph.Cmd, cmsg)
	return ret.RET_OK
}

type L2CMonthlyCardGetDataCommand struct {
}

func (c L2CMonthlyCardGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_MonthlyCardGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMonthlyCardGetDataCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
