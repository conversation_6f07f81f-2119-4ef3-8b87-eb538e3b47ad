package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
)

type Base struct {
	r          *Robot
	GlobalAttr *cl.GlobalAttr
}

func NewBase(r *Robot) *Base {
	b := &Base{
		r: r,
	}
	return b
}

func (b *Base) C2LViewUser() {
	cmsg := &cl.C2L_ViewUser{}

	cmsg.Id = b.r.userdata.Id
	//edit here
	b.r.SendCmd(uint32(cl.ID_MSG_C2L_ViewUser), cmsg)
}

func (b *Base) L2CViewUser(recv *cl.L2C_ViewUser) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (b *Base) L2COpGlobalAttr(recv *cl.L2C_OpGlobalAttr) {
	//	if recv.Ret != uint32(cret.RET_OK) {
	//		return
	//	}

	//edit here
}

func (b *Base) C2LTestBattleData() {
	cmsg := &cl.C2L_TestBattleData{}
	//edit here
	b.r.SendCmd(uint32(cl.ID_MSG_C2L_TestBattleData), cmsg)
}

func (b *Base) L2CTestBattleData(recv *cl.L2C_TestBattleData) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (b *Base) C2LGlobalAttrGet() {
	cmsg := &cl.C2L_GlobalAttrGet{}
	//edit here
	b.r.SendCmd(uint32(cl.ID_MSG_C2L_GlobalAttrGet), cmsg)
}

func (b *Base) L2CGlobalAttrGet(recv *cl.L2C_GlobalAttrGet) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	if recv.GetGlobalAttr() != nil {
		b.GlobalAttr = recv.GetGlobalAttr()
	} else {
		b.GlobalAttr = &cl.GlobalAttr{}
	}

}
