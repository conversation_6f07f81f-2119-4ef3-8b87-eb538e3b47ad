package main

import (
	"encoding/json"
	"errors"
	"net/url"
	"strconv"
	"strings"

	"github.com/buger/jsonparser"
	"github.com/google/go-querystring/query"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type UserInit struct {
	DeviceID string `json:"deviceID,omitempty" url:"deviceID"`
	//Mac string `json:"mac" url:"mac"`
	DeviceType string `json:"deviceType,omitempty" url:"deviceType"`
	DeviceOS   int    `json:"deviceOS,omitempty" url:"deviceOS"`
	DeviceDpi  string `json:"deviceDpi,omitempty" url:"deviceDpi"`
	ChannelID  int    `json:"channelID,omitempty" url:"channelID"`
	AppID      int    `json:"appID,omitempty" url:"appID"`
	TimeStamp  string `json:"timestamp,omitempty" url:"timestamp"`
	Sign       string `json:"sign,omitempty" url:"-"`
}

type UserInitResp struct {
	Code uint32 `json:"code,omitempty"`
}

func (r *Robot) SdkUserInit() error {
	param := r.mock.GetCurrentParam()
	reqUrl, _ := jsonparser.GetString(param, "url")
	if reqUrl == "" {
		return errors.New("sdkUserInit url is empty")
	}
	reqData := &UserInit{
		// DeviceID: "eb1a5263a94744c5bd6c89f0b696d398",
		DeviceID: r.loginUuid,
		//Mac: "",
		DeviceType: "Pixel+4a",
		DeviceOS:   1,
		DeviceDpi:  "1080*2160",
		ChannelID:  0,
		AppID:      int(r.sdkAppMes.Id),
		TimeStamp:  strconv.FormatInt(time.NowMsec(), 10),
	}
	var v url.Values
	var err error
	if v, err = query.Values(reqData); err != nil {
		return errors.New("userInit to values error")
	}
	signStr := getSignStr(v)
	signStr += "&secretKey=" + r.sdkAppMes.Key //secretKey需要在最后
	reqData.Sign = MD5(signStr)
	v.Set("sign", strings.ToUpper(reqData.Sign))
	//resp, err := http.PostForm(reqUrl, v)
	result, err := DoHttp(HTTPPost, reqUrl, util.Bytes(v.Encode()), "application/x-www-form-urlencoded")
	l4g.Infof("[SDKUserInit]:%s getresponse %s", r.uuid, result)
	if err != nil {
		l4g.Errorf("[SDKUserInit] failed:%+v", err)
		return err
	}
	userInitResp := &UserInitResp{}
	err = json.Unmarshal(result, userInitResp)
	if err != nil {
		l4g.Errorf("[SDKUserInit] unmarshal failed:%v", err)
		return err
	}
	l4g.Debugf("[SDKUserInit] userInitResp:%v", userInitResp)
	if userInitResp.Code != 0 {
		l4g.Errorf("[SDKUserInit] init Error:%v", userInitResp.Code)
		return errors.New("[SDKUserInit] init Error")
	}
	return nil
}
