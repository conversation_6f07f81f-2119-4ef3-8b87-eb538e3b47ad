package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotEquipCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipGet), L2CEquipGetCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_EquipWear), C2LEquipWearCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipWear), L2CEquipWearCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_EquipStrength), C2LEquipStrengthCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipStrength), L2CEquipStrengthCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_EquipMultipleStrength), C2LEquipMultipleStrengthCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipMultipleStrength), L2CEquipMultipleStrengthCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_EquipGrowTransfer), C2LEquipGrowTransferCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipGrowTransfer), L2CEquipGrowTransferCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipRefine), L2CEquipRefineCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipEnchant), L2CEquipEnchantCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipEvolution), L2CEquipEvolutionCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipDecompose), L2CEquipDecomposeCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_EquipRevive), L2CEquipReviveCommand{})
}

type L2CEquipGetCommand struct {
}

func (c L2CEquipGetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipGet{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipGetCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipGet(recv)
	return ret.RET(recv.Ret)
}

type C2LEquipWearCommand struct {
}

func (c C2LEquipWearCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	robot.equip.C2LEquipWear()
	return ret.RET_OK
}

type L2CEquipWearCommand struct {
}

func (c L2CEquipWearCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipWear{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipWearCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipWear(recv)
	return ret.RET(recv.Ret)
}

type C2LEquipStrengthCommand struct {
}

func (c C2LEquipStrengthCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.equip.C2LEquipStrength()
	return ret.RET_OK
}

type L2CEquipStrengthCommand struct {
}

func (c L2CEquipStrengthCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipStrength{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipStrengthCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipStrength(recv)
	if recv.Ret == uint32(ret.RET_NOT_ENOUGH_RESOURCES) {
		//跳过资源不足的检查
		return ret.RET(ret.RET_OK)
	}
	return ret.RET(recv.Ret)
}

type C2LEquipMultipleStrengthCommand struct {
}

func (c C2LEquipMultipleStrengthCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.equip.C2LEquipMultipleStrength()
	return ret.RET_OK
}

type L2CEquipMultipleStrengthCommand struct {
}

func (c L2CEquipMultipleStrengthCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipMultipleStrength{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipMultipleStrengthCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipMultipleStrength(recv)
	if recv.Ret == uint32(ret.RET_NOT_ENOUGH_RESOURCES) {
		//跳过资源不足的检查
		return ret.RET(ret.RET_OK)
	}
	return ret.RET(recv.Ret)
}

type L2CEquipRefineCommand struct {
}

func (c L2CEquipRefineCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipRefine{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipRefineCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipRefine(recv)
	if recv.Ret == uint32(ret.RET_NOT_ENOUGH_RESOURCES) {
		//跳过资源不足的检查
		return ret.RET(ret.RET_OK)
	}
	return ret.RET(recv.Ret)
}

type L2CEquipEnchantCommand struct {
}

func (c L2CEquipEnchantCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipEnchant{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipEnchantCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipEnchant(recv)
	return ret.RET(recv.Ret)
}

type L2CEquipEvolutionCommand struct {
}

func (c L2CEquipEvolutionCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipEvolution{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipEvolutionCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipEvolution(recv)
	return ret.RET(recv.Ret)
}

type L2CEquipDecomposeCommand struct {
}

func (c L2CEquipDecomposeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipDecompose{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipDecomposeCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipDecompose(recv)
	return ret.RET(recv.Ret)
}

type L2CEquipReviveCommand struct {
}

func (c L2CEquipReviveCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipRevive{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipReviveCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipRevive(recv)
	return ret.RET(recv.Ret)
}

type C2LEquipGrowTransferCommand struct {
}

func (c C2LEquipGrowTransferCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.equip.C2LEquipGrowTransfer()
	return ret.RET_OK
}

type L2CEquipGrowTransferCommand struct {
}

func (c L2CEquipGrowTransferCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_EquipGrowTransfer{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CEquipGrowTransferCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.equip.L2CEquipGrowTransfer(recv)
	return ret.RET(recv.Ret)
}
