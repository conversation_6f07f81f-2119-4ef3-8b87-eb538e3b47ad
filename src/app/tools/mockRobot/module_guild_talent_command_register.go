package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGuildTalentCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildTalentList), L2CGuildTalentListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildTalentLevelUp), L2CGuildTalentLevelUpCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildTalentReset), L2CGuildTalentResetCommand{})
}

type L2CGuildTalentListCommand struct {
}

func (c L2CGuildTalentListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GuildTalentList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildTalentListCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.guildTalent.L2CGuildTalentList(recv)
	return ret.RET(recv.Ret)
}

type L2CGuildTalentLevelUpCommand struct {
}

func (c L2CGuildTalentLevelUpCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GuildTalentLevelUp{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildTalentLevelUpCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.guildTalent.L2CGuildTalentLevelUp(recv)
	return ret.RET(recv.Ret)
}

type L2CGuildTalentResetCommand struct {
}

func (c L2CGuildTalentResetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GuildTalentReset{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildTalentResetCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.guildTalent.L2CGuildTalentReset(recv)
	return ret.RET(recv.Ret)
}
