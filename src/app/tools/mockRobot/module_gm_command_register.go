package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGMCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GM), C2LGMCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GM), L2CGMCommand{})
}

type C2LGMCommand struct {
}

func (c C2LGMCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.gm.C2LGM()
	return ret.RET_OK
}

type L2CGMCommand struct {
}

func (c L2CGMCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GM{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2C_GM unmarshal error: %s", err)
		return ret.RET_ERROR
	}

	return ret.RET(recv.Ret)
}
