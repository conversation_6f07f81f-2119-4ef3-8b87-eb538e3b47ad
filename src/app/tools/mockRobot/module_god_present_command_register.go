package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGodPresentCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GodPresentGetData), C2LGodPresentGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GodPresentRecvItem), C2LGodPresentRecvItemCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GodPresentSummon), C2LGodPresentSummonCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_GodPresentRecvAwards), C2LGodPresentRecvAwardsCommand{})

	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GodPresentGetData), L2CGodPresentGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GodPresentRecvItem), L2CGodPresentRecvItemCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GodPresentSummon), L2CGodPresentSummonCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GodPresentRecvAwards), L2CGodPresentRecvAwardsCommand{})
}

type C2LGodPresentGetDataCommand struct {
}

func (c C2LGodPresentGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.godPresent.C2LGodPresentGetData()
	return ret.RET_OK
}

type C2LGodPresentRecvItemCommand struct {
}

func (c C2LGodPresentRecvItemCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.godPresent.C2LGodPresentRecvItem()
	return ret.RET_OK
}

type C2LGodPresentSummonCommand struct {
}

func (c C2LGodPresentSummonCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.godPresent.C2LGodPresentSummon()
	return ret.RET_OK
}

type C2LGodPresentRecvAwardsCommand struct {
}

func (c C2LGodPresentRecvAwardsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	robot.godPresent.C2LGodPresentRecvAwards()
	return ret.RET_OK
}

type L2CGodPresentGetDataCommand struct {
}

func (c L2CGodPresentGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GodPresentGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGodPresentGetDataCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.godPresent.L2CGodPresentGetData(recv)
	return ret.RET(recv.Ret)
}

type L2CGodPresentRecvItemCommand struct {
}

func (c L2CGodPresentRecvItemCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GodPresentRecvItem{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGodPresentRecvItemCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.godPresent.L2CGodPresentRecvItem(recv)
	return ret.RET(recv.Ret)
}

type L2CGodPresentSummonCommand struct {
}

func (c L2CGodPresentSummonCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GodPresentSummon{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGodPresentSummonCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.godPresent.L2CGodPresentSummon(recv)
	return ret.RET(recv.Ret)
}

type L2CGodPresentRecvAwardsCommand struct {
}

func (c L2CGodPresentRecvAwardsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_GodPresentRecvAwards{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGodPresentRecvAwardsCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.godPresent.L2CGodPresentRecvAwards(recv)
	return ret.RET(recv.Ret)
}
