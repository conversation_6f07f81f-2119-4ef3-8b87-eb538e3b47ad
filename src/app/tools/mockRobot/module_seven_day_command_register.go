package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/buger/jsonparser"
	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotSevenDayCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_SevenDayLoginData), C2LSevenDayLoginDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SevenDayLoginData), L2CSevenDayLoginDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_C2L_SevenDayLoginTakeAward), C2LSevenDayLoginTakeAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SevenDayLoginTakeAward), L2CSevenDayLoginTakeAwardCommand{})
}

type C2LSevenDayLoginDataCommand struct {
}

func (c C2LSevenDayLoginDataCommand) Execute(robot *Robot, _ *parse.PackHead, _ []byte) ret.RET {
	robot.sevenDay.C2LSevenDayLoginData()
	return ret.RET_OK
}

type L2CSevenDayLoginDataCommand struct {
}

func (c L2CSevenDayLoginDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_SevenDayLoginData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSummonCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	robot.sevenDay.L2CSevenDayLoginData(recv)
	return ret.RET(recv.Ret)
}

type C2LSevenDayLoginTakeAwardCommand struct {
}

func (c C2LSevenDayLoginTakeAwardCommand) Execute(robot *Robot, ph *parse.PackHead, _ []byte) ret.RET {
	cmsg := &cl.C2L_SevenDayLoginTakeAward{}
	param := robot.mock.GetCurrentParam()
	value, _ := jsonparser.GetInt(param, "id")
	cmsg.Id = uint32(value)
	robot.SendCmd(ph.Cmd, cmsg)
	return ret.RET_OK
}

type L2CSevenDayLoginTakeAwardCommand struct {
}

func (c L2CSevenDayLoginTakeAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) ret.RET {
	recv := &cl.L2C_SevenDayLoginTakeAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSevenDayLoginTakeAwardCommand unmarshal error: %s", err)
		return ret.RET_ERROR
	}
	return ret.RET(recv.Ret)
}
