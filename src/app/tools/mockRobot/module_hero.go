package main

import (
	"app/goxml"
	"app/logic/character"
	"app/protos/out/cl"
	"app/protos/out/ret"
	cret "app/protos/out/ret"

	"github.com/buger/jsonparser"
	l4g "github.com/ivanabc/log4go"
)

type Hero struct {
	r             *Robot
	heroes        map[uint64]*cl.Hero
	specialHeroes map[uint64]*cl.Hero
	oldEquips     map[uint64][]uint64 //老的装备 做装备转移用的
}

func NewHeroM(r *Robot) *Hero {
	return &Hero{
		r:             r,
		heroes:        make(map[uint64]*cl.Hero),
		specialHeroes: make(map[uint64]*cl.Hero),
		oldEquips:     make(map[uint64][]uint64),
	}
}

func (h *Hero) UpdateHero(hero *cl.Hero) {
	h.heroes[hero.GetData().GetId()] = hero
}

func (h *Hero) GetHero(SysId uint32) *cl.Hero {
	var result *cl.Hero
	for _, hero := range h.heroes {
		if hero.Data.SysId == SysId {
			if result == nil {
				result = hero
			} else {
				if hero.Data.Level > result.Data.Level {
					result = hero
				}
			}
		}
	}
	return result
}

func (h *Hero) C2LHeroList() {
	cmsg := &cl.C2L_HeroList{}
	//edit here
	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroList), cmsg)
}

func (h *Hero) L2CHeroList(recv *cl.L2C_HeroList) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
	for _, hero := range recv.Heroes {
		info := goxml.GetData().HeroInfoM.Index(hero.GetData().SysId)
		if info == nil {
			l4g.Errorf("[Hero] %s robot load hero err. hid:%d sysId:%d",
				h.r.UUID(), hero.GetData().Id, hero.GetData().SysId)
			return
		}
		if info.Special == goxml.HeroCommon {
			h.heroes[hero.GetData().Id] = hero
		} else {
			h.specialHeroes[hero.GetData().Id] = hero
		}
	}
}

func (h *Hero) GetHeroDataByID(id uint64) *cl.HeroBody {
	if hero, exist := h.heroes[id]; exist {
		return hero.Data
	}
	return nil
}

func (h *Hero) setHeroLockStatus(id uint64, status bool) {
	for _, v := range h.heroes {
		if v.Data.Id == id {
			v.Data.Locked = status
		}
	}
}

func (h *Hero) C2LHeroLevelUp() {
	cmsg := &cl.C2L_HeroLevelUp{}
	//edit here
	param := h.r.mock.GetCurrentParam()
	id, _ := jsonparser.GetInt(param, "id")
	num, _ := jsonparser.GetInt(param, "num")
	hero := h.GetHero(uint32(id))
	cmsg.Id = hero.GetData().GetId()
	if num == 0 {
		num = int64(h.r.Level() - hero.GetData().GetLevel())
		if num <= 0 {
			l4g.Errorf("user:%s level:%d heroLevel:%d", h.r.uuid, h.r.Level(), hero.GetData().GetLevel())
			h.r.mock.TriggerRspCmd(uint32(cl.ID_MSG_L2C_HeroLevelUp), ret.RET_OK)
			return
		}
		if num > int64(goxml.GetData().ConfigInfoM.HeroOneKeyLvUpNum) {
			num = int64(goxml.GetData().ConfigInfoM.HeroOneKeyLvUpNum)
		}
	}

	cmsg.Num = uint32(num)
	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroLevelUp), cmsg)
}

func (h *Hero) L2CHeroLevelUp(recv *cl.L2C_HeroLevelUp) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	hero := h.GetHeroDataByID(recv.Id)
	if hero != nil {
		hero.Level += recv.Num
	}
}

func (h *Hero) C2LHeroStageUp() {
	cmsg := &cl.C2L_HeroStageUp{}
	param := h.r.mock.GetCurrentParam()
	id, _ := jsonparser.GetInt(param, "hid")
	hero := h.GetHero(uint32(id))
	cmsg.Id = hero.Data.Id
	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroStageUp), cmsg)
}

func (h *Hero) L2CHeroStageUp(recv *cl.L2C_HeroStageUp) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
	hero := h.GetHeroDataByID(recv.Id)
	if hero != nil {
		hero.Stage++
	}
}

func (h *Hero) C2LHeroStarUp() {
	cmsg := &cl.C2L_HeroStarUp{}

	//edit here
	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroStarUp), cmsg)
}

func (h *Hero) L2CHeroStarUp(recv *cl.L2C_HeroStarUp) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
}

func (h *Hero) C2LHeroBuySlot() {
	cmsg := &cl.C2L_HeroBuySlot{}
	//edit here
	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroBuySlot), cmsg)
}

func (h *Hero) L2CHeroBuySlot(recv *cl.L2C_HeroBuySlot) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (h *Hero) C2LHeroUpdateLockStatus() {
	cmsg := &cl.C2L_HeroUpdateLockStatus{}

	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroUpdateLockStatus), cmsg)
}

func (h *Hero) L2CHeroUpdateLockStatus(recv *cl.L2C_HeroUpdateLockStatus) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
	h.setHeroLockStatus(recv.Id, recv.Lock)
}

func (h *Hero) C2LHeroDecompose() {
	cmsg := &cl.C2L_HeroDecompose{}
	//edit here
	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroDecompose), cmsg)
}

func (h *Hero) L2CHeroDecompose(recv *cl.L2C_HeroDecompose) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

}

func (h *Hero) C2LHeroTestAttr() {
	cmsg := &cl.C2L_HeroTestAttr{}

	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroTestAttr), cmsg)
}

func (h *Hero) L2CHeroTestAttr(recv *cl.L2C_HeroTestAttr) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
}

func (h *Hero) C2LHeroBack() {
	cmsg := &cl.C2L_HeroBack{}

	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroBack), cmsg)
}

func (h *Hero) L2CHeroBack(recv *cl.L2C_HeroBack) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
	hero := h.GetHeroDataByID(recv.Id)
	if hero != nil {
		info := goxml.GetData().HeroInfoM.Index(hero.SysId)
		if info != nil {
			hero.Star = info.Star
		}
	}
}

func (h *Hero) C2LHeroRevive() {
	cmsg := &cl.C2L_HeroRevive{}

	h.r.SendCmd(uint32(cl.ID_MSG_C2L_HeroRevive), cmsg)

}

func (h *Hero) L2CHeroRevive(recv *cl.L2C_HeroRevive) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
	hero := h.GetHeroDataByID(recv.Id)
	if hero != nil {
		hero.Level = goxml.GetData().HeroLevelInfoM.GetDefaultLv()
		hero.Stage = goxml.GetData().HeroStageInfoM.GetDefaultStage()
	}
}

func (h *Hero) L2CHeroChangeRandom(recv *cl.L2C_HeroChangeRandom) {

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
}

func (h *Hero) L2CHeroChangeSave(recv *cl.L2C_HeroChangeSave) {
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	//edit here
	h.C2LHeroList()
}

func (h *Hero) IsCountEnough(info []*goxml.HeroRes) bool {
	var needTotal uint32
	for _, v := range info {
		needTotal += v.Count
	}
	return int(needTotal) <= len(h.heroes)+len(h.specialHeroes)-1
}

func (h *Hero) GetHidsBySysID(sysID, star, num uint32, existHids []uint64, selfHid uint64) []uint64 {
	var count uint32
	result := make([]uint64, 0, num)
	for _, v := range h.heroes {
		if v.Data.SysId != sysID {
			continue
		}
		if v.Data.Star != star {
			continue
		}
		if v.Data.Id == selfHid {
			continue
		}
		if v.Data.Locked {
			continue
		}

		var exist bool
		for _, id := range existHids {
			if id == v.Data.Id {
				exist = true
				break
			}
		}
		if exist {
			continue
		}

		result = append(result, v.Data.Id)
		count++
		if count >= num {
			break
		}
	}
	if count < num {
		l4g.Debugf("GetHidsBySysID. count not enough")
		return nil
	}
	return result
}

func (h *Hero) UpdateOldEquips(hero *cl.Hero) {
	h.oldEquips[hero.GetData().GetId()] = h.GetNowEquips(hero)
}

func (h *Hero) DeleteOldEquips(hero *cl.Hero) {
	delete(h.oldEquips, hero.GetData().GetId())
}

func (h *Hero) GetNowEquips(hero *cl.Hero) []uint64 {
	ids := make([]uint64, 0, 4)
	for i := uint32(1); i <= character.EquipWearMaxNum; i++ {
		if hero.GetData().Equipment != nil {
			if v, exist := hero.GetData().Equipment[i]; exist && v > 0 {
				ids = append(ids, v)
			}
		}
	}
	return ids
}

func (h *Hero) GetOldEquips(hero *cl.Hero) []uint64 {
	return h.oldEquips[hero.GetData().GetId()]
}

func (h *Hero) GetMinHero() uint64 {
	//获取一个等级最低的英雄
	for _, v := range h.heroes {
		if v.GetData().GetLevel() == 1 {
			return v.GetData().GetId()
		}
	}
	return uint64(0)
}
