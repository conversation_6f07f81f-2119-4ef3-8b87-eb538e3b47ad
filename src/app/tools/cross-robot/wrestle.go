package main

import (
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"time"

	l4g "github.com/ivanabc/log4go"
)

type WrestleFunc func(*Wrestle)

var gWrestleActions []WrestleFunc

func InitWrestleActions() {
	gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleInfo)
	gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleStart)
	gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleFinish)
	// gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleAward)
	// gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleTopList)
	gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleRank)
	gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleLastRank)
	gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleLike)
	// gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleTest)
	gWrestleActions = append(gWrestleActions, (*Wrestle).L2CWrestleRankFirst)
}

const LikeCount = 3

type Wrestle struct {
	*UserLoad
	usetotal int64
	msgid    uint32
	msgtime  map[uint32]time.Time
	send     uint32
	recv     uint32

	defenderList []*cr.Fighter
	likedList    map[uint64]bool

	attacker        *cr.Fighter
	currentDefender *cr.Fighter
}

func NewWrestle(r *Robot) *Wrestle {
	w := &Wrestle{
		UserLoad:     NewUserLoad(r),
		msgid:        0,
		msgtime:      make(map[uint32]time.Time),
		defenderList: make([]*cr.Fighter, 0, 10),
		likedList:    make(map[uint64]bool),
	}
	return w
}

func (w *Wrestle) resetData(attacker *cr.Fighter, defenders []*cr.Fighter) {
	w.attacker = attacker
	w.defenderList = w.defenderList[0:0]
	for _, v := range defenders {
		if v.Uid != w.u.uid {
			w.defenderList = append(w.defenderList, v)
			break
		}
	}
	w.likedList = make(map[uint64]bool)
}

func (w *Wrestle) chooseOneDefender() *cr.Fighter {
	count := len(w.defenderList)
	if count == 0 {
		return nil
	}

	key := w.u.rd.RandBetween(0, count-1)
	w.currentDefender = w.defenderList[key]
	return w.currentDefender
}

func (w *Wrestle) initLikedList(list []uint64) {
	if len(w.likedList) == 0 {
		for _, uid := range list {
			w.likedList[uid] = false
		}
	}
}

func (w *Wrestle) chooseOneLikeUid() uint64 {
	for uid, liked := range w.likedList {
		if !liked {
			return uid
		}
	}
	return 0
}

func (w *Wrestle) updateLikedList(uid uint64) {
	w.likedList[uid] = true
}

// 断线重连重置数据
func (w *Wrestle) reset() {
	w.load.reset()

	//edit here
}

func (w *Wrestle) RandAction() {
	l4g.Debugf("%s start wrestle.RandAction state:%d", w.u.name, w.load.state())
	switch w.load.state() {
	case dataLoadNone:
		w.C2LInit()
	case dataLoading:
		w.C2LRetryInit()
	default:
		if length := len(gWrestleActions); length != 0 {
			//降低info协议的触发概率至10%
			key := w.u.rd.Intn(length)
			if key == 0 {
				if !w.u.RandBool10P() {
					key += w.u.rd.Intn(length)
					if key == 0 {
						key++
					}
				}
			}

			gWrestleActions[key](w)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (w *Wrestle) C2LInit() {
	w.load.Lock()
	w.load.setState(dataLoading)

	//w.C2LWrestleFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	w.L2CWrestleInfo()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (w *Wrestle) L2CInitRet(ret uint32) {
	if w.load.state() == dataLoading {
		w.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			w.load.setState(dataLoaded)
		} else {
			w.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (w *Wrestle) C2LRetryInit() {
	if w.load.Lock() {
		//w.C2LWrestleFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (w *Wrestle) L2CWrestleInfo() {
	cmsg := &l2c.L2C_WrestleInfo{}

	//edit here
	cmsg.Snapshot = &cl.UserSnapshot{
		Id:           w.u.uid,
		Name:         w.u.name,
		Level:        100,
		BaseId:       1,
		Power:        w.u.power,
		OfflineTime:  1670051612,
		Online:       true,
		CreateTime:   1669019679,
		ArenaPower:   w.u.power,
		GuildId:      123,
		GuildName:    "天神第一公会",
		MaxPower:     w.u.power,
		ForestPower:  w.u.power,
		Vip:          1,
		Top5Heros:    []uint64{1126068544947716, 1126068544947698, 1126068544947723, 1126068544947715, 1126068544947722},
		DefensePower: w.u.power,
		Sid:          w.u.sid,
		ShowHero:     44004,
		ExpireTime:   []int64{0, 0},
	}
	cmsg.NeedChange = true
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleInfo), cmsg)
}

func (w *Wrestle) C2LWrestleInfo(recv *l2c.C2L_WrestleInfo) {
	l4g.Debug("[Wrestle] %s C2LWrestleInfo ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	for _, v := range recv.Defenders {
		l4g.Debug("[Wrestle] %s C2LWrestleInfo, defender:%+v",
			w.u.name, v)
	}

	for _, v := range recv.Top3 {
		l4g.Debug("[Wrestle] %s C2LWrestleInfo, topData:%+v",
			w.u.name, v)
	}

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	w.resetData(recv.Fighter, recv.Defenders)
	w.L2CInitRet(recv.Ret)

	//补发奖励只调一次 TODO 应是每个服只调一次，暂时先将其屏蔽
	// w.L2CWrestleAward()
}

func (w *Wrestle) L2CWrestleStart() {
	cmsg := &l2c.L2C_WrestleStart{}
	//edit here
	defender := w.chooseOneDefender()
	if defender == nil {
		l4g.Errorf("[Wrestle] %d  C2LWrestleStart no defender",
			w.u.sid)
		return
	}
	cmsg.DefenderUid = defender.Uid
	cmsg.DefenderSid = defender.Sid
	cmsg.DefenderRank = defender.Rank
	cmsg.AttackerSid = w.u.sid
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleStart), cmsg)
}

func (w *Wrestle) C2LWrestleStart(recv *l2c.C2L_WrestleStart) {
	l4g.Debug("[Wrestle] %s C2LWrestleStart ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	w.L2CWrestleFinish()
}

func (w *Wrestle) L2CWrestleFinish() {
	cmsg := &l2c.L2C_WrestleFinish{}
	//edit here
	if w.currentDefender == nil {
		return
	}

	cmsg.AttackerSid = w.attacker.Sid
	cmsg.AttackerUid = w.attacker.Uid
	cmsg.DefenderSid = w.currentDefender.Sid
	cmsg.DefenderUid = w.currentDefender.Uid
	cmsg.ReportId = "1126117495548305"

	cmsg.DefenderSnapshot = w.currentDefender.Snapshot
	cmsg.DefenderSnapshot.Power++ //战力+1

	cmsg.AttackerWin = w.u.RandBoolHalf()
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleFinish), cmsg)
}

func (w *Wrestle) C2LWrestleFinish(recv *l2c.C2L_WrestleFinish) {
	l4g.Debug("[Wrestle] %s C2LWrestleFinish ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (w *Wrestle) C2LWrestlePush(recv *l2c.C2L_WrestlePush) {
	l4g.Debug("[Wrestle] %s C2LWrestlePush ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (w *Wrestle) L2CWrestleAward() {
	cmsg := &l2c.L2C_WrestleAward{}
	//edit here
	cmsg.Sid = w.u.ServerID()
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleAward), cmsg)
}

func (w *Wrestle) C2LWrestleAward(recv *l2c.C2L_WrestleAward) {
	l4g.Debug("[Wrestle] %s C2LWrestleAward ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	// w.L2CInitRet(recv.Ret)
}

func (w *Wrestle) L2CWrestleTopList() {
	cmsg := &l2c.L2C_WrestleTopList{}
	//edit here
	cmsg.Level = uint32(w.u.rd.RandBetween(0, 5))
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleTopList), cmsg)
}

func (w *Wrestle) C2LWrestleTopList(recv *l2c.C2L_WrestleTopList) {
	l4g.Debug("[Wrestle] %s C2LWrestleTopList ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (w *Wrestle) L2CWrestleRank() {
	cmsg := &l2c.L2C_WrestleRank{}
	//edit here
	cmsg.Uid = w.attacker.Uid
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleRank), cmsg)
}

func (w *Wrestle) C2LWrestleRank(recv *l2c.C2L_WrestleRank) {
	l4g.Debug("[Wrestle] %s C2LWrestleRank ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (w *Wrestle) L2CWrestleLastRank() {
	cmsg := &l2c.L2C_WrestleLastRank{}
	//edit here
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleLastRank), cmsg)
}

func (w *Wrestle) C2LWrestleLastRank(recv *l2c.C2L_WrestleLastRank) {
	l4g.Debug("[Wrestle] %s C2LWrestleLastRank ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	if recv.Top50 != nil {
		top3 := make([]uint64, 0, LikeCount)
		for i, v := range recv.Top50.Snapshots {
			if i >= LikeCount {
				break
			}
			top3 = append(top3, v.Snapshot.Id)
		}
		w.initLikedList(top3)
	}
}

func (w *Wrestle) L2CWrestleLike() {
	cmsg := &l2c.L2C_WrestleLike{}
	//edit here
	uid := w.chooseOneLikeUid()
	if uid == 0 {
		return
	}
	cmsg.Uid = uid
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleLike), cmsg)
}

func (w *Wrestle) C2LWrestleLike(recv *l2c.C2L_WrestleLike) {
	l4g.Debug("[Wrestle] %s C2LWrestleLike ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	w.updateLikedList(recv.Uid)
}

func (w *Wrestle) L2CWrestleTest() {
	w.msgid++
	w.msgtime[w.msgid] = time.Now()
	cmsg := &l2c.L2C_WrestleTest{Msgid: w.msgid}
	w.send++

	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleTest), cmsg)
}

func (w *Wrestle) C2LWrestleTest(msg *l2c.C2L_WrestleTest) {
	msgid := msg.Msgid
	delay := time.Since(w.msgtime[msgid])
	w.recv++
	w.usetotal += int64(delay)
	if int64(delay) > 10000000 {
		l4g.Debug("C2LWrestleTest delay %d", delay)
	}
	delete(w.msgtime, msgid)
}

func (w *Wrestle) L2CWrestleRankFirst() {
	cmsg := &l2c.L2C_WrestleRankFirst{}
	//edit here
	cmsg.ClientMsgId = 123
	w.u.SendCmd(uint32(l2c.ID_MSG_L2C_WrestleRankFirst), cmsg)
}

func (w *Wrestle) C2LWrestleRankFirst(recv *l2c.C2L_WrestleRankFirst) {
	l4g.Debug("[Wrestle] %s C2LWrestleRankFirst ret:%s, recv:%+v",
		w.u.name, cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (w *Wrestle) DebugTime() (uint32, uint32, uint32) {
	if w.send == 0 {
		w.send = 1
	}
	l4g.Info("wrestle sendtotal:%d recvtotal:%d usetotal:%d average:%d", w.send, w.recv, w.usetotal, w.usetotal/int64(w.send))
	return w.send, w.recv, uint32(w.usetotal / int64(w.send))
}
