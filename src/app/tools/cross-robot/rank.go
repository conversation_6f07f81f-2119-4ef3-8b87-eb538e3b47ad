package main

/*
import (
	"app/protos/in/l2c"
	cret "app/protos/out/ret"
	"time"

	l4g "github.com/ivanabc/log4go"
)

type RankFunc func(*Rank)

var gRankActions []RankFunc

func InitRankActions() {
	gRankActions = append(gRankActions, (*Rank).L2CRankTest)
}

type Rank struct {
	*UserLoad
	msgid    uint32
	msgtime  map[uint32]time.Time
	send     uint32
	recv     uint32
	usetotal int64
}

func NewRank(r *Robot) *Rank {
	a := &Rank{
		UserLoad: NewUserLoad(r),
		msgid:    0,
		msgtime:  make(map[uint32]time.Time),
	}
	return a
}

//断线重连重置数据
func (d *Rank) reset() {
	d.load.reset()

	//edit here
}

func (d *Rank) RandAction() {
	switch d.load.state() {
	case dataLoadNone:
		d.L2CInit()
	case dataLoading:
		d.L2CRetryInit()
	default:
		if length := len(gRankActions); length != 0 {
			gRankActions[d.u.rd.Intn(length)](d)
		}
	}
}

//初始化请求模块数据 并添加流程锁，防止重复拉取
func (d *Rank) L2CInit() {
	d.load.Lock()
	d.load.setState(dataLoading)

	d.L2CRankLoad()
}

//请求协议返回后调用
func (d *Rank) C2LInitRet(ret uint32) {
	if d.load.state() == dataLoading {
		d.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			d.load.setState(dataLoaded)
		} else {
			d.load.setState(dataLoadNone)
		}
	}
}

//初始化请求超时 重新发起请求
func (d *Rank) L2CRetryInit() {
	if d.load.Lock() {
		d.L2CRankLoad()
	}
}

func (d *Rank) L2CRankLoad() {
	cmsg := &l2c.L2C_RankLoad{Ids: []uint32{1}}
	d.u.SendCmd(uint32(l2c.ID_MSG_L2C_RankLoad), cmsg)
}

func (d *Rank) C2LRankLoad(recv *l2c.C2L_RankLoad) {
	l4g.Debug("[Rank] %s C2LRankLoad ret:%s, recv:%+v",
		d.u.sid, cret.RET_name[int32(recv.Ret)], recv)
	d.C2LInitRet(recv.Ret)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *Rank) L2CRankTest() {
	d.msgid++
	d.msgtime[d.msgid] = time.Now()
	cmsg := &l2c.L2C_RankTest{Msgid: d.msgid}
	d.send++

	d.u.SendCmd(uint32(l2c.ID_MSG_L2C_RankTest), cmsg)
}

func (d *Rank) C2LRankTest(msg *l2c.C2L_RankTest) {
	msgid := msg.Msgid
	delay := time.Since(d.msgtime[msgid])
	d.recv++
	d.usetotal += int64(delay)
	if int64(delay) > 10000000 {
		l4g.Debug("w2ranktest delay %d", delay)
	}
	delete(d.msgtime, msgid)
}

func (d *Rank) DebugTime() (uint32, uint32, uint32) {
	if d.send == 0 {
		d.send = 1
	}
	l4g.Info("rank sendtotal:%d recvtotal:%d usetotal:%d average:%d", d.send, d.recv, d.usetotal, d.usetotal/int64(d.send))
	return d.send, d.recv, uint32(d.usetotal / int64(d.send))
}
*/
