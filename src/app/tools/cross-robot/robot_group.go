package main

import (
	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/time"
)

type ChildrenGroup struct {
	parent *Robot
	act    *actor.Actor
	uids   []uint64
}

func NewChildrenGroup(r *Robot, robotCount int) *ChildrenGroup {
	return &ChildrenGroup{
		parent: r,
		act: actor.NewActor(r.ctx.CreateChild(), &actor.Config{
			Kind:      222,
			KindName:  "childRobot",
			MsgQSize:  1024,
			Rate:      500,
			TimerSize: 512,
		}),
		uids: make([]uint64, 0, robotCount),
	}
}

func (c *ChildrenGroup) AddChild(uid uint64) {
	c.uids = append(c.uids, uid)
}

func (c *ChildrenGroup) InitTimer(requestRate int64) {
	c.act.AddTimer(&Timer{c}, requestRate+time.Now().Unix(), requestRate)
}

type Timer struct {
	c *ChildrenGroup
}

func (t *Timer) TimeOut(now int64) {
	for _, uid := range t.c.uids {
		robot := t.c.parent.GetChild(uid)
		gActionM.RandAction(robot)
	}
}
