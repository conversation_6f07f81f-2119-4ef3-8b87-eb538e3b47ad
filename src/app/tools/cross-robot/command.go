package main

import (
	"app/logic/session"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

type Command interface {
	Execute(*Robot, *session.Message, []byte) bool
}

type CommandM struct {
	cmdm map[uint32]Command
}

func NewCommandM() *CommandM {
	return &CommandM{
		cmdm: make(map[uint32]Command),
	}
}

func (m *CommandM) Register(id uint32, cmd Command) {
	m.cmdm[id] = cmd
}

func (m *CommandM) Dispatcher(robot *Robot, msg *session.Message) bool {
	if cmd, exist := m.cmdm[msg.Cmd]; exist {
		start := time.Now().UnixNano()
		buf := msg.Data[parse.PackHeadSize:msg.Length]
		ret := cmd.Execute(robot, msg, buf)
		executeTime := time.Now().UnixNano() - start
		if executeTime > 1000000 {
			l4g.Info("COMMAND EXECUTE TIME: %d %d", msg.Cmd, executeTime)
		}
		return ret
	}
	l4g.Error("[Command] no find cmd: %d", msg.Cmd)
	return false
}
