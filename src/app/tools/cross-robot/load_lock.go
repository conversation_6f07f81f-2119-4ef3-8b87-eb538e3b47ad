package main

import (
	"gitlab.qdream.com/kit/sea/util"
)

const (
	dataLoadNone = iota
	dataLoading
	dataLoaded
)

type LoadLock struct {
	load int
	pl   *util.ProcessLock
}

func (s *LoadLock) state() int     { return s.load }
func (s *LoadLock) setState(v int) { s.load = v }

func (s *LoadLock) reset() {
	s.load = dataLoadNone
	s.pl.UnLock()
}

func (s *LoadLock) Lock() bool { return s.pl.Lock() }
func (s *LoadLock) UnLock()    { s.pl.UnLock() }

type UserLoad struct {
	load *LoadLock
	u    *Robot
}

func NewUserLoad(robot *Robot) *UserLoad {
	return &UserLoad{
		load: &LoadLock{
			load: dataLoadNone,
			pl:   util.NewProcessLock(3), //统一默认3s请求加载超时时间
		},
		u: robot,
	}
}
