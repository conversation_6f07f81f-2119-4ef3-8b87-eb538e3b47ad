package main

import (
	l4g "github.com/ivanabc/log4go"
)

type xmlActionItem struct {
	Name string `xml:"name,attr"`
	Open int    `xml:"open,attr"`
}

type xmlActions struct {
	Items []xmlActionItem `xml:"item"`
}

type xmlConfig struct {
	Reconnect int        `xml:"reconnect"`
	Actions   xmlActions `xml:"action"`

	actions map[string]int
}

func (x *xmlConfig) Init() {
	x.actions = make(map[string]int)
	for _, v := range x.Actions.Items {
		x.actions[v.Name] = v.Open
	}
	l4g.Info("xmlConfig actions:%v reconnect:%d", x.actions, x.Reconnect)
}

func (x *xmlConfig) IsActionOpen(s string) bool {
	return x.actions[s] != 0
}
