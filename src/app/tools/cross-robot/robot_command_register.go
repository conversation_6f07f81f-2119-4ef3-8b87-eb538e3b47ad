package main

import (
	"app/logic/session"
	"app/protos/in/l2c"

	//	"app/protos/out/common"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

var gRobotCommandM = NewCommandM()

func InitRobotCommand() {
	gRobotCommandM.Register(uint32(l2c.ID_MSG_C2L_KeepAlive), C2LKeepAliveCommand{})
	gRobotCommandM.Register(uint32(l2c.ID_MSG_C2L_SayHi), C2LSayHiCommand{})

	//跨服竞技场
	InitRobotWrestleCommand()
	//<InitRobotCommand end>（这行不要删除不要修改，用于脚本识别插入）
}

type C2LSayHiCommand struct {
}

func (c C2LSayHiCommand) Execute(robot *Robot, msg *session.Message, data []byte) bool {
	recv := &l2c.C2L_SayHi{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("C2LSayHiCommand unmarshal error")
		return false
	}

	smsg := &l2c.L2C_SayHi{
		Id:      robot.ServerID(),
		Current: []uint64{robot.ServerID()},
	}

	client := robot.CrossM().GetClientByID(msg.SessionID)
	if client != nil {
		client.Write(&parse.PackHead{Cmd: uint32(l2c.ID_MSG_L2C_SayHi)}, smsg)
		l4g.Infof("L2C_SayHi: %s, sessionID: %d SessionNetAddr: %s logicServiceID: %d crossServiceID: %d",
			robot.Name(), msg.SessionID, util.Uint64ToNetAddr(msg.SessionNetAddr), robot.ServerID(), msg.UID)
	} else {
		l4g.Errorf("[L2C_SayHiCommand] no found cross session id: %d - %s",
			msg.SessionID, util.Uint64ToNetAddr(msg.SessionNetAddr))
	}
	l4g.Info("robot get sayhi: %s", recv)
	return true
}

type C2LKeepAliveCommand struct {
}

func (c C2LKeepAliveCommand) Execute(robot *Robot, msg *session.Message, data []byte) bool {
	return true
}
