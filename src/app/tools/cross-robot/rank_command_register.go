package main

/*
import (
	"app/protos/in/l2c"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func InitRankCommand() {
	gRobotCommandM.Register(uint32(l2c.ID_MSG_C2L_RankLoad), &C2LRankLoadCommand{})
	gRobotCommandM.Register(uint32(l2c.ID_MSG_C2L_RankTest), &C2LRankTestCommand{})
}

type C2LRankLoadCommand struct {
}

func (c *C2LRankLoadCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &l2c.C2L_RankLoad{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("C2Lrankload unmarshal error: %s", err)
		return false
	}
	robot.rank.C2LRankLoad(recv)
	return true
}

type C2LRankTestCommand struct {
}

func (c *C2LRankTestCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &l2c.C2L_RankTest{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("C2Lrankload unmarshal error: %s", err)
		return false
	}
	robot.rank.C2LRankTest(recv)
	return true
}
*/
