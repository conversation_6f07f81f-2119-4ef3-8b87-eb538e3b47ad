package main

import (
	"io"
	"net/http"
	"strconv"

	l4g "github.com/ivanabc/log4go"
)

func InitHTTPHandle() {
	http.HandleFunc("/add", HandleAddRobot)
	http.HandleFunc("/dec", HandleDecRobot)
}

func HTTPRet(w io.Writer, data []byte) {
	if _, e := w.Write(data); e != nil {
		l4g.Error("http ret:%v", e)
		return
	}
}

// 增加机器人
func HandleAddRobot(w http.ResponseWriter, r *http.Request) {
	_ = r.ParseForm()
	numstr := r.Form.Get("num")
	num, _ := strconv.ParseUint(numstr, 10, 32)
	if num == 0 {
		HTTPRet(w, []byte("num error"))
		return
	}
	//正在创建中
	if gRobotM.GetState() != RobotMStateIdle {
		HTTPRet(w, []byte("manager is creating, wait for a minute"))
		return
	}
	//go gRobotM.Start(uint32(num))
	HTTPRet(w, []byte("ok"))
	l4g.Info("[HTTP] add robot:%d", num)
}

// 减少机器人
func HandleDecRobot(w http.ResponseWriter, r *http.Request) {
	_ = r.ParseForm()
	numstr := r.Form.Get("num")
	num, _ := strconv.ParseUint(numstr, 10, 32)
	if num == 0 {
		HTTPRet(w, []byte("num error"))
		return
	}
	//正在创建中
	if gRobotM.GetState() != RobotMStateIdle {
		HTTPRet(w, []byte("manager is creating, wait for a minute"))
		return
	}
	HTTPRet(w, []byte("ok"))
	l4g.Info("[HTTP] add robot:%d", num)
}
