package main

import (
	"app/protos/in/l2c"
	"sync"
	"time"

	l4g "github.com/ivanabc/log4go"
)

type CommandElapse struct {
	Cumul int64
	Num   int64
	Nanos []int64
}

func (e *CommandElapse) average() int64 {
	if e.Num != 0 {
		return e.Cumul / e.Num
	}
	return 0
}

func (e *CommandElapse) cumul() int64 {
	elapse := time.Now().UnixNano() - e.<PERSON>[0]
	e.Num++
	e.Cumul += elapse
	return elapse
}

func (e *CommandElapse) pop() {
	e.Nanos = append(e.<PERSON>os[:0], e.<PERSON><PERSON>[1:]...)
}

type CommandStat struct {
	u      *Robot
	cmds   map[uint32]*CommandElapse //cmd->ns
	elapse []int64
	mtx    sync.RWMutex
}

func NewCommandStat(r *Robot) *CommandStat {
	return &CommandStat{
		u:    r,
		cmds: make(map[uint32]*CommandElapse, 1000),
	}
}

func (c *CommandStat) reset() {
	c.cmds = make(map[uint32]*CommandElapse, 1000)
}

func (c *CommandStat) send(id uint32) {
	//一个服一个gorutine时，不需要读写锁。多gorutine读写时需要锁
	// c.mtx.Lock()
	// defer c.mtx.Unlock()
	cmd, e := c.cmds[id]
	if !e {
		cmd = &CommandElapse{}
		c.cmds[id] = cmd
	}
	cmd.Nanos = append(cmd.Nanos, time.Now().UnixNano())
}

func (c *CommandStat) recv(id uint32) {
	//一个服一个gorutine时，不需要读写锁。多gorutine读写时需要锁
	// c.mtx.RLock()
	// defer c.mtx.RUnlock()
	if cmd, e := c.cmds[id-1]; e {
		if len(cmd.Nanos) != 0 {
			c.elapse = append(c.elapse, cmd.cumul())
		}
	}
}

func (c *CommandStat) setCmsStatus(id uint32, ok bool) {
	if cmd, e := c.cmds[id-1]; e {
		if len(cmd.Nanos) != 0 && len(c.elapse) > 0 {
			l4g.Info("[CommandStat] %s cmd:%d,%s elapse:%dms average:%dms %v ret:%v",
				c.u.Name(), id-1, l2c.ID_name[int32(id-1)], c.elapse[0]/1000000, cmd.average()/1000000, cmd, ok)
			cmd.pop()
			c.elapse = c.elapse[1:]
		} else {
			l4g.Debug("[CommandStat] %s cmd:%d send list empty", c.u.Name(), id)
		}
	}
}

//！计算思路有问题 废弃
//统计所有机器人成功返回的消息 累加数量和延迟(ms)用来计算RPS
//RPS(Requests Per Second)：吞吐率是服务器并发处理能力的量化描述，单位是 reqs/s
//指的是某个并发用户数下单位时间内处理的请求数
// type CommandRps struct {
// 	m ConcurrentMapU32
// }

// func NewCommandRps() *CommandRps {
// 	return &CommandRps{}
// }

// func (cq *CommandRps) Reset() {
// 	cq.m = NewConcurrentMapU32(50)
// }

// var upsertCb = func(exist bool, valueInMap, newValue uint64) uint64 {
// 	if exist {
// 		cnt := uint64(uint32(valueInMap>>32)+1) << 32
// 		elapse := uint64(uint32(valueInMap) + uint32(newValue))
// 		return cnt + elapse
// 	}
// 	return uint64(1)<<32 + newValue
// }

// //毫秒延迟 纳秒不支持
// func (cq *CommandRps) Add(key, value uint64) {
// 	cq.m.Upsert(key, value, upsertCb)
// }

// func (cq *CommandRps) Dump() {
// 	l4g.Info("-------------- CommandRps Start: --------------")
// 	var sc, se uint32
// 	for k, v := range cq.m.Items() {
// 		cnt := uint32(v >> 32)
// 		elapse := uint32(v)
// 		sc += cnt
// 		se += elapse
// 		if int32(k) < int32(cl.ID_MSG_BEGIN) {
// 			l4g.Info("command:%s(%d) num:%d elapse:%vms rps:%d", cg.ID_name[int32(k)], k, cnt, elapse, cnt/(elapse/1000))
// 		} else {
// 			l4g.Info("command:%s(%d) num:%d elapse:%vms rps:%d", cl.ID_name[int32(k)], k, cnt, elapse, cnt/(elapse/1000))
// 		}
// 	}
// 	if se != 0 {
// 		l4g.Info("total command num:%d elapse:%v rps:%d", sc, se, sc/(se/1000))
// 	}
// 	l4g.Info("-------------- CommandRps End: ----------------")
// }
