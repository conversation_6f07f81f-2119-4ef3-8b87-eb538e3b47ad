package main

import (
	"fmt"

	"gitlab.qdream.com/kit/redis"
)

func initClient() *redis.Client {
	redisAddr := fmt.Sprintf("%s:%s", *host, *port)
	client, err := redis.Dial("tcp", redisAddr, *password, uint32(*dbnum), false)
	panicError(err)

	return client
}

// 校验redis参数的合法性
func checkRequest(cmd string, rawArgs []string) error {
	if cmd == "hmset" {
		size := len(rawArgs)
		if size < 3 || size%2 != 1 {
			return fmt.Errorf("args num wrong")
		}
	} else if cmd == "hset" {
		size := len(rawArgs)
		if size != 3 {
			return fmt.Errorf("args num wrong")
		}
	}
	return nil
}

// TODO
// 对参数进行一些转换处理
// 比如hset的json输入转换为实际存储的pb blob格式
func convertRequest(cmd string, rawArgs []string) ([]interface{}, error) {
	var realArgs []interface{}
	if size := len(rawArgs); size > 0 {
		realArgs = make([]interface{}, 0, size)
		for i, arg := range rawArgs {
			if cmd == "hset" && i == 2 {
				realArg, err := encode(rawArgs[0]+":"+rawArgs[i-1], arg)
				if err != nil {
					return nil, err
				}
				realArgs = append(realArgs, realArg)
			} else if cmd == "hmset" && i >= 2 && (i-1)%2 == 1 { //代表的是值
				realArg, err := encode(rawArgs[0]+":"+rawArgs[i-1], arg)
				if err != nil {
					return nil, err
				}
				realArgs = append(realArgs, realArg)
			} else {
				realArgs = append(realArgs, arg)
			}
		}
	}
	return realArgs, nil
}

func printReply(cmd string, rawArgs []string, realArgs []interface{}, reply *redis.Reply) {
	_ = realArgs
	if reply.Type == redis.ErrorReply {
		fmt.Println(reply.Err)
	} else if reply.Type == redis.NilReply {
		fmt.Println("<nil>")
	} else if reply.Type == redis.StatusReply {
		s, err := reply.Str()
		if err == nil {
			fmt.Println(s)
		} else {
			fmt.Println(err)
		}
	} else if reply.Type == redis.IntegerReply {
		i, err := reply.Int64()
		if err == nil {
			fmt.Println(i)
		} else {
			fmt.Println(err)
		}
	} else if reply.Type == redis.BulkReply || reply.Type == redis.MultiReply {
		if cmd == "hgetall" {
			printReplyHGETALL(rawArgs, reply)
		} else if cmd == "hget" {
			printReplyHGET(rawArgs, reply)
		} else if cmd == "hmget" {
			printReplyHMGET(rawArgs, reply)
		} else {
			fmt.Println(reply.String())
		}
	} else {
		fmt.Println("unknown redis reply type:", reply.Type)
	}
}

func printReplyHGETALL(args []string, reply *redis.Reply) {
	listBytes, err := reply.ListBytes()
	panicError(err)
	for i := 0; i < len(listBytes)-1; i += 2 {
		key := args[0] + ":" + string(listBytes[i])
		buf, ok := decode(key, listBytes[i+1])
		if !ok {
			buf = listBytes[i+1]
		}
		fmt.Printf("(%d)%s (size=%d)\n%s\n", i/2+1, string(listBytes[i]), len(listBytes[i+1]), string(buf))
	}
}

func printReplyHGET(args []string, reply *redis.Reply) {
	buf, err := reply.Bytes()
	panicError(err)
	size := len(buf)
	key := args[0] + ":" + args[1]
	newbuf, ok := decode(key, buf)
	if ok {
		buf = newbuf
	}
	fmt.Printf("size=%d\n", size)
	fmt.Println(string(buf))
}

func printReplyHMGET(args []string, reply *redis.Reply) {
	listBytes, err := reply.ListBytes()
	panicError(err)
	for i := 0; i < len(listBytes); i++ {
		key := args[0] + ":" + args[i+1]
		buf, ok := decode(key, listBytes[i])
		if !ok {
			buf = listBytes[i]
		}
		fmt.Printf("(%d)%s (size=%d)\n%s\n", i+1, args[i+1], len(listBytes[i]), string(buf))
	}
}

func assertRET(ret string, mustRet string) {
	if ret != mustRet {
		panic(ret + "," + mustRet)
	}
}
