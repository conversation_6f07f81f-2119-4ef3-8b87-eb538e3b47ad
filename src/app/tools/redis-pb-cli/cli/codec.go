package main

import (
	"encoding/json"
	"fmt"
	"strings"

	credisop "app/cross/db/redisop"
	"app/logic/db/redisop"

	"github.com/gogo/protobuf/proto"
)

func tryGetPbObject(key string) proto.Message {
	if obj := redisop.GetclPbObject(key); obj != nil {
		return obj
	}
	if obj := redisop.GetdbPbObject(key); obj != nil {
		return obj
	}
	if obj := credisop.GetcrPbObject(key); obj != nil {
		return obj
	}
	return nil
}

func decode(key string, buf []byte) ([]byte, bool) {
	if len(buf) == 0 {
		s := "your search key is nil"
		return []byte(s), true
	}

	pbObject := tryGetPbObject(key)
	if pbObject == nil {
		return buf, false
	}

	var err error
	//复杂类型才用检测解压缩
	if buf[0] != 0 {
		buf, err = redisop.UnCompressWithFlag(buf)
		if err != nil {
			fmt.Printf("RedisOp UnCompress %s Unmarshal Error :%+v\n", key, err)
			return buf, false
		}
	} else {
		buf = buf[1:]
	}

	err = proto.Unmarshal(buf, pbObject)
	if err != nil {
		return buf, false
	}
	buf, _ = json.Marshal(pbObject)
	return buf, true
}

// encode用于编码设置到redis
// 必须不能出错
func encode(key string, rawArg string) (interface{}, error) {
	pbObject := tryGetPbObject(key)
	if pbObject == nil {
		var js map[string]interface{}
		if json.Unmarshal([]byte(rawArg), &js) == nil {
			//NOTE：是个json字符串，但是未找到对应的pb结构，那么必须返回错误，不能设置到redis
			//very important!!!
			//very important!!!
			//very important!!!
			return nil, fmt.Errorf("not found proto object")
		}
		if strings.Contains(rawArg, "{") || strings.Contains(rawArg, "}") {
			return nil, fmt.Errorf("may be json")
		}
		return rawArg, nil
	}

	//解析到对应的proto结构
	if err := json.Unmarshal([]byte(rawArg), pbObject); err != nil {
		return nil, err
	}

	//序列化为pb格式
	bytes, err := proto.Marshal(pbObject)
	if err != nil {
		return nil, err
	}
	blob := make([]byte, len(bytes)+1)
	blob[0] = 0
	copy(blob[1:], bytes)

	//压缩
	if len(blob) > redisop.CompressBytes {
		blob, err = redisop.CompressWithFlag(blob, redisop.CompressFlagSnappy)
		if err != nil {
			return nil, err
		}
	}

	return blob, nil
}
