package util

import (
	"bufio"
	"fmt"
	"io"
	"io/fs"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

func GetCurrentDir() string {
	dirPth, err := os.Getwd()
	if err != nil {
		log.Fatal(fmt.Sprintf("get cur path failed. err: %s", err))
	}

	return dirPth
}

func GetParentDir() string {
	return filepath.Dir(GetCurrentDir())
}

func GenerateLogPath(fileInfos []fs.FileInfo, dirName, fileName string) ([]string, int) {
	filePaths := make([]string, 0, len(fileInfos))
	var specialFilePath string
	for _, f := range fileInfos {
		if strings.Contains(f.Name(), fileName) {
			if f.Name() == fileName {
				// 为了保证文件中log日志时间上的有序性，特定义了specialFilePath
				specialFilePath = f.Name()
				continue
			}
			filePaths = append(filePaths, filepath.Join(dirName, f.Name()))
		}
	}

	if specialFilePath != "" {
		filePaths = append(filePaths, filepath.Join(dirName, specialFilePath))
	}

	return filePaths, len(filePaths)
}

func Read(filePath string, filter func(string), stopChan chan struct{}) {
	file, err := os.Open(filePath)
	if err != nil {
		log.Fatal(fmt.Sprintf("open file failed, error: %s", err))
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	for {
		line, err := reader.ReadString('\n')
		if err == io.EOF {
			stopChan <- struct{}{}
			break
		}

		filter(line)
	}
}

func GetTime(logStr string) (string, uint64) {
	var (
		execTime  string
		delayTime uint64
		err       error
	)
	fields := strings.Fields(logStr)
	if len(fields) > 7 {
		execSlice := strings.Split(fields[1], ".")
		if len(execSlice) == 2 {
			execTime = execSlice[0]
		}

		delaySlice := strings.Split(strings.TrimRight(fields[7], "ms"), ":")
		if len(delaySlice) == 2 {
			delayTime, err = strconv.ParseUint(delaySlice[1], 10, 64)
			if err != nil {
				log.Fatal(fmt.Sprintf("string parse uint64 failed. err: %s", err))
			}
		}
	}

	return execTime, delayTime
}

func OutPutDebug(log string) {
	fmt.Println(log)
}

func OutPutInfo(log string) {
	fmt.Printf("\n %c[1;40;33m%s%c[0m \n", 0x1B, log, 0x1B)
}

func OutPutError(log string) {
	fmt.Printf("\n %c[1;40;31m%s%c[0m \n", 0x1B, log, 0x1B)
}

func CountDown(secTime int) {
	ticker := time.NewTicker(time.Second)
	for {
		<-ticker.C
		OutPutDebug(fmt.Sprintf("压测开始倒计时: %d", secTime))
		secTime--
		if secTime == 0 {
			ticker.Stop()
			break
		}
	}
}

type TimeRound struct {
	Min uint64
	Max uint64
}

func (t *TimeRound) CheckInRound(delay uint64) bool {
	if delay >= t.Min && delay < t.Max {
		return true
	}
	return false
}

func (t *TimeRound) GetKey() string {
	return strconv.FormatUint(t.Min, 10) + "~" + strconv.FormatUint(t.Max, 10)
}

func GetTimeRoundIndex(delay uint64) int {
	for index, timeRound := range TimeRounds {
		if timeRound.CheckInRound(delay) {
			return index
		}
	}
	return len(TimeRounds) - 1
}

func GetTimeRoundKeys() []string {
	keys := make([]string, len(TimeRounds))
	for index, timeRound := range TimeRounds {
		keys[index] = timeRound.GetKey()
	}
	return keys
}

func WriteFile(path string, fileName string, buf []byte) error {
	err := os.MkdirAll(path, 0700)
	if err != nil {
		return err
	}

	file := filepath.Join(path, fileName)
	err = ioutil.WriteFile(file, buf, 0600)
	if err != nil {
		return err
	}
	return nil
}

func FormatTime(secTimeSlice []int64) []string {
	sTimeSlice := make([]string, len(secTimeSlice))
	for index, timestamp := range secTimeSlice {
		tm := time.Unix(timestamp, 0)
		sTimeSlice[index] = tm.Format(ChartTimeTemplate)
	}
	return sTimeSlice
}
