package logic

import (
	"benchmarkAgent/util"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/go-echarts/go-echarts/v2/charts"
	"github.com/go-echarts/go-echarts/v2/opts"
	"github.com/go-echarts/go-echarts/v2/types"
)

// LineChart ：延迟折线图
type LineChart struct {
	execTimeSlice  []string // 协议执行时的时间
	delayTimeSlice []uint64 // 协议的延迟时间
}

// PieChart ：延迟饼图
type PieChart struct {
	timeRound []string // 时间范围
	numSlice  []int    // 每个时间范围内的数量
}

// 在线人数曲线
type onlineNumLineChart struct {
	*LineChart
	index int
}

func newLineChart() *LineChart {
	return &LineChart{
		execTimeSlice:  make([]string, 0, 1000000),
		delayTimeSlice: make([]uint64, 0, 1000000),
	}
}

func newPieChart() *PieChart {
	return &PieChart{
		timeRound: make([]string, 8),
		numSlice:  make([]int, 8),
	}
}

func newOnlineNumLineChart() *onlineNumLineChart {
	return &onlineNumLineChart{
		&LineChart{
			execTimeSlice:  make([]string, 50),
			delayTimeSlice: make([]uint64, 50),
		},
		0,
	}
}

func (dl *LineChart) addTime(execTime string, delayTime uint64) {
	dl.execTimeSlice = append(dl.execTimeSlice, execTime)
	dl.delayTimeSlice = append(dl.delayTimeSlice, delayTime)
}

func (dl *LineChart) generateLineItems() []opts.LineData {
	items := make([]opts.LineData, 0)
	for i := 0; i < len(dl.delayTimeSlice); i++ {
		items = append(items, opts.LineData{Value: dl.delayTimeSlice[i]})
	}

	return items
}

// 创建一个延迟折线图
func (dl *LineChart) createLineChart(fileName, title string) {
	line := charts.NewLine()
	line.SetGlobalOptions(
		charts.WithInitializationOpts(opts.Initialization{
			Theme: types.ThemeInfographic,
		}),
		charts.WithTitleOpts(opts.Title{
			Title: title,
		}),
	)
	// 添加数据到 line instance
	line.SetXAxis(dl.execTimeSlice).AddSeries(title, dl.generateLineItems()).SetSeriesOptions(charts.WithLineChartOpts(opts.LineChart{Smooth: true}))

	filePath := util.GetCurrentDir() + "/" + fileName
	f, err := os.Create(filePath)
	if err != nil {
		log.Fatalf("create line chart failed. err: %s", err)
	}
	err = line.Render(f)
	if err != nil {
		log.Fatalf("render failed for create line chart. err: %s", err)
	}
	fmt.Printf("创建 %s 成功 \n", fileName)
}

func (dp *PieChart) initTimeRound() {
	dp.timeRound[0] = "0~10"
	dp.timeRound[1] = "10~20"
	dp.timeRound[2] = "20~50"
	dp.timeRound[3] = "50~100"
	dp.timeRound[4] = "100~200"
	dp.timeRound[5] = "200~500"
	dp.timeRound[6] = "500~1000"
	dp.timeRound[7] = "1000~10000"
}

func (dp *PieChart) updateNum(delayTime uint64) {
	if delayTime > 0 && delayTime <= 10 {
		dp.numSlice[0]++
	} else if delayTime > 10 && delayTime <= 20 {
		dp.numSlice[1]++
	} else if delayTime > 20 && delayTime <= 50 {
		dp.numSlice[2]++
	} else if delayTime > 50 && delayTime <= 100 {
		dp.numSlice[3]++
	} else if delayTime > 100 && delayTime <= 200 {
		dp.numSlice[4]++
	} else if delayTime > 200 && delayTime <= 500 {
		dp.numSlice[5]++
	} else if delayTime > 500 && delayTime <= 1000 {
		dp.numSlice[6]++
	} else if delayTime > 1000 && delayTime <= 10000 {
		dp.numSlice[7]++
	}
}

func (dp *PieChart) generatePieItems() []opts.PieData {
	items := make([]opts.PieData, 0)
	for i := 0; i < len(dp.timeRound); i++ {
		items = append(items, opts.PieData{
			Name:  dp.timeRound[i],
			Value: dp.numSlice[i],
		})
	}

	return items
}

// 创建一个延迟饼图
func (dp *PieChart) createPipChart(name string) {
	pie := charts.NewPie()
	pie.SetGlobalOptions(charts.WithTitleOpts(opts.Title{Title: "延迟分布饼图"})).SetSeriesOptions()
	pie.AddSeries("延迟分布饼图", dp.generatePieItems()).SetSeriesOptions(
		charts.WithPieChartOpts(
			opts.PieChart{
				Radius: 200, // 半径
			},
		),
		charts.WithLabelOpts(
			opts.Label{
				Show:      true,
				Formatter: "{b}(ms) : {c} 条",
			},
		),
	)

	fileName := util.GetCurrentDir() + "/" + name
	f, err := os.Create(fileName)
	if err != nil {
		log.Fatalf("create pip chart failed. err: %s", err)
	}
	err = pie.Render(f)
	if err != nil {
		log.Fatalf("render failed for create pip chart. err: %s", err)
	}
	fmt.Printf("创建 %s 成功 \n", name)
}

func (o *onlineNumLineChart) updateOnlineNumLine(logStr string, num *Num) {
	fields := strings.Fields(logStr)
	if len(fields) > 1 {
		execSlice := strings.Split(fields[1], ".")
		if len(execSlice) != 2 {
			return
		}

		exist := false
		for i, data := range o.execTimeSlice {
			if data == execSlice[0] {
				o.index = i
				exist = true
			}
		}

		if !exist && o.delayTimeSlice[o.index] > 0 {
			o.index++
		}
		o.execTimeSlice[o.index] = execSlice[0]
		o.delayTimeSlice[o.index] = num.onlineNum
	}
}
