package logic

import (
	"benchmarkAgent/cmd"
	"benchmarkAgent/config"
	"benchmarkAgent/util"
	"fmt"
	"io/ioutil"
	"log"
	"os/exec"
	"strings"
	"sync"

	"github.com/spf13/viper"
)

func GetRedisMem(wg *sync.WaitGroup) {
	defer wg.Done()
	command := util.GetCurrentDir() + viper.GetString("shell.redis_memory")
	outBytes, err := exec.Command("/bin/bash", "-c", command).Output()
	if err != nil {
		log.Fatalf("execute shell failed. shell: %s  error: %s", command, err)
	}

	for _, field := range strings.Fields(string(outBytes)) {
		if strings.Contains(field, "used_memory_human") { // 数据占用了多少内存
			fmt.Printf("redis内存占用: %s \n", field)
		}
	}
}

func GetRobotProtoDelay(wg *sync.WaitGroup) {
	defer wg.Done()

	command := util.GetCurrentDir() + viper.GetString("shell.robot_proto_delay")
	outBytes, err := exec.Command("/bin/bash", "-c", command).Output()
	if err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}

	fmt.Println("协议延迟分布")
	fmt.Println(string(outBytes))
}

func GetLogicProtoDelay(wg *sync.WaitGroup) {
	defer wg.Done()

	cmdIns := cmd.New(config.LogicIP, config.Username, config.SshKey, config.Port)
	fmt.Println(cmdIns.Run(viper.GetString("command.logic_proto_delay")))
}

func GetPprofCpu(wg *sync.WaitGroup) {
	defer wg.Done()

	command, cpuName := genPprofCommand("profile")
	outBytes, err := exec.Command("/bin/bash", "-c", command).Output()
	if err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}

	fmt.Printf("------------------------- pprof %s ------------------------------ \n", cpuName)
	fmt.Println(string(outBytes))
}

func GetPprofHeap(wg *sync.WaitGroup) {
	defer wg.Done()

	command, heapName := genPprofCommand("heap")
	outBytes, err := exec.Command("/bin/bash", "-c", command).Output()
	if err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}

	fmt.Printf("------------------------- pprof %s ------------------------------ \n", heapName)
	fmt.Println(string(outBytes))
}

func genPprofCommand(fileName string) (string, string) {
	name := getNewestPprofFile(fileName)
	command := util.GetCurrentDir() + viper.GetString("shell.pprof") + " " + name

	return command, name
}

func getNewestPprofFile(fileName string) string {
	files, err := ioutil.ReadDir(util.GetCurrentDir() + config.PprofPath)
	if err != nil {
		log.Fatalf("get file failed. err: %s", err)
	}
	box := make([]string, 0, len(files)/3)
	for _, f := range files {
		if strings.Contains(f.Name(), fileName) {
			box = append(box, f.Name())
		}
	}

	if len(box) > 0 {
		return box[len(box)-1]
	}

	return ""
}
