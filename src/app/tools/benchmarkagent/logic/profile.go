package logic

import (
	"benchmarkAgent/util"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

func PullProfile(addr string, sec int, path string) {
	go cpuProfile(addr, sec, path)
	go memProfile(addr, sec, path)
	go goroutineProfile(addr, sec, path)
	go traceProfile(addr, sec, path)
}

func cpuProfile(addr string, sec int, path string) {
	sleepTime := sec / 10
	time.Sleep(time.Duration(sleepTime) * time.Second)
	util.OutPutInfo("Begin cpuProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/profile?seconds=%d", addr, sec-2*sleepTime)
	resp, err := http.Get(addr)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		util.OutPutError(fmt.Sprintf("cpuProfile read body error %s", err.Error()))
		return
	}

	if err := util.WriteFile(path, "profile", body); err != nil {
		util.OutPutError(fmt.Sprintf("cpuProfile save file error %s", err.Error()))
		return
	}
	util.OutPutInfo("cpuProfile End")
}

func memProfile(addr string, sec int, path string) {
	sleepTime := sec / 2
	time.Sleep(time.Duration(sleepTime) * time.Second)
	heapProfile(addr, path)
	allocsProfile(addr, path)
}

func heapProfile(addr string, path string) {
	util.OutPutInfo("Begin heapProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/heap", addr)
	resp, err := http.Get(addr)
	if err != nil {
		util.OutPutError(fmt.Sprintf("heapProfile error %s", err.Error()))
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		util.OutPutError(fmt.Sprintf("heapProfile read body error %s", err.Error()))
		return
	}
	if err := util.WriteFile(path, "heap", body); err != nil {
		util.OutPutError(fmt.Sprintf("heapProfile save file error %s", err.Error()))
		return
	}
	util.OutPutInfo("heapProfile End")
}

func allocsProfile(addr string, path string) {
	util.OutPutInfo("Begin allocsProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/allocs", addr)
	resp, err := http.Get(addr)
	if err != nil {
		util.OutPutError(fmt.Sprintf("allocsProfile error %s", err.Error()))
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		util.OutPutError(fmt.Sprintf("allocsProfile read body error %s", err.Error()))
		return
	}
	if err := util.WriteFile(path, "allocs", body); err != nil {
		util.OutPutError(fmt.Sprintf("allocsProfile save file error %s", err.Error()))
		return
	}
	util.OutPutInfo("allocsProfile End")
}

func goroutineProfile(addr string, sec int, path string) {
	sleepTime := sec / 2
	time.Sleep(time.Duration(sleepTime) * time.Second)
	util.OutPutInfo("Begin goroutineProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/goroutine", addr)
	resp, err := http.Get(addr)
	if err != nil {
		util.OutPutError(fmt.Sprintf("goroutineProfile error %s", err.Error()))
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		util.OutPutError(fmt.Sprintf("goroutineProfile read body error %s", err.Error()))
		return
	}
	if err := util.WriteFile(path, "goroutine", body); err != nil {
		util.OutPutError(fmt.Sprintf("goroutineProfile save file error %s", err.Error()))
		return
	}
	util.OutPutInfo("goroutineProfile End")
}

func traceProfile(addr string, sec int, path string) {
	sleepTime := sec / 10
	time.Sleep(time.Duration(sleepTime) * time.Second)
	util.OutPutInfo("Begin traceProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/trace?seconds=%d", addr, sec-2*sleepTime)
	resp, err := http.Get(addr)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		util.OutPutError(fmt.Sprintf("traceProfile read body error %s", err.Error()))
		return
	}

	if err := util.WriteFile(path, "trace", body); err != nil {
		util.OutPutError(fmt.Sprintf("traceProfile save file error %s", err.Error()))
		return
	}
	util.OutPutInfo("traceProfile End")
}
