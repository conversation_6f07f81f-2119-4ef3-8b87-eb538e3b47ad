package logic

import (
	"benchmarkAgent/cmd"
	"benchmarkAgent/config"
	"benchmarkAgent/logop"
	"benchmarkAgent/util"
	"bufio"
	"bytes"
	"fmt"
	"io"
	"log"
	"os/exec"
	"sort"
	"strconv"
	"strings"

	"github.com/spf13/viper"
)

func resetRobotXml(fileName string) {
	util.OutPutInfo(fmt.Sprintf("重置robot_xml为%s", fileName))
	command := util.GetCurrentDir() + viper.GetString("shell.robot_xml") + " " + fileName
	if err := exec.Command("/bin/bash", "-c", command).Run(); err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}
}

func openRobotXml(module string) {
	util.OutPutInfo(fmt.Sprintf("robox.xml 打开%s", module))

	command := fmt.Sprintf(`sed -i "s/name=\"%s\" prob=\"0\"/name=\"%s\" prob=\"1\"/g" %s`,
		module, module, config.FileRobot)
	if err := exec.Command("/bin/bash", "-c", command).Run(); err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}
}

func resetLogicXml() {
	util.OutPutInfo("压测前,开始重置游戏服xml量表")

	cmdIns := cmd.New(config.LogicIP, config.Username, config.SshKey, config.Port)
	cmdIns.Run(viper.GetString("command.reset_logic_xml"))
	util.OutPutInfo("重置游戏服xml量表成功")
}

func CleanLogicDB() {
	util.OutPutInfo("开始清空游戏服db数据!")

	cmdIns := cmd.New(config.LogicIP, config.Username, config.SshKey, config.Port)
	cmdIns.Run(viper.GetString("command.clean_logic_db"))

	util.OutPutInfo("清空游戏服db数据成功!")
}

func CreateRobot(totalNum, concurrentNum, accountType int) {
	util.OutPutInfo("开始创建机器人!")

	resetRobotXml("robot.xml.create")

	second := (totalNum/concurrentNum + 1) * 5
	sendResource := "true"
	args := " " + util.RobotDefaultPre + " " + strconv.Itoa(totalNum) + " " + strconv.Itoa(concurrentNum) +
		" " + strconv.Itoa(second) + " " + sendResource + " " + strconv.Itoa(accountType)
	command := util.GetCurrentDir() + viper.GetString("shell.create_user") + args
	if err := exec.Command("/bin/bash", "-c", command).Run(); err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}
	util.OutPutInfo("创建机器人成功!")
}

func ModuleBenchmark(m *ModuleEntity) {
	util.OutPutInfo(fmt.Sprintf("开始压测%s!", m.module))

	args := " " + util.RobotDefaultPre + " " + strconv.Itoa(m.totalNum) + " " + strconv.Itoa(m.second)
	command := util.GetCurrentDir() + viper.GetString("shell.module_benchmark") + args
	if m.pprof == 1 {
		PullProfile(config.ProfileAddr, m.second, m.path)
	}
	err := exec.Command("/bin/bash", "-c", command).Run()
	if err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}
	util.OutPutInfo(fmt.Sprintf("压测%s成功!", m.module))
}

func CleanLogicLog() {
	cmdIns := cmd.New(config.LogicIP, config.Username, config.SshKey, config.Port)
	cmdIns.Run(viper.GetString("command.clean_logic_log"))

	util.OutPutInfo("清空游戏服日志成功!")
}

func CleanRobotLog() {
	util.OutPutInfo("开始清空机器人服日志!")

	command := util.GetCurrentDir() + viper.GetString("shell.clear_robot_log")
	if err := exec.Command("/bin/bash", "-c", command).Run(); err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}

	util.OutPutInfo("清空机器人服日志成功!")
}

func LogicPromethusProcess(m *ModuleEntity) {
	util.OutPutInfo("开始获取promethus数据!")
	start, end := m.benchStartTime.Unix(), m.benchEndTime.Unix()
	promethusCpu := PromethusInfo{
		Metric:   config.CpuMetric,
		Instance: config.LogicGoInstance,
		Start:    start,
		End:      end,
		Step:     "5s",
	}
	promethusCpu.Process(m)

	promethusMemory := PromethusInfo{
		Metric:   config.MemoryMetric,
		Instance: config.LogicGoInstance,
		Start:    start,
		End:      end,
		Step:     "5s",
	}
	promethusMemory.Process(m)

	promethusNetOut := PromethusInfo{
		Metric:   config.NetOutMetric,
		Instance: config.LogicSysInstance,
		Start:    start,
		End:      end,
		Step:     "5s",
	}
	promethusNetOut.Process(m)
	util.OutPutInfo("获取promethus数据完成!")
}

func BMLogBaseInfo(m *ModuleEntity) {
	util.OutPutInfo("压测结果: 开始基础信息收集!")
	command := util.GetCurrentDir() + viper.GetString("shell.analyze_base_info")
	cmd := exec.Command("/bin/bash", "-c", command)
	data, err := cmd.Output()
	if err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}
	outPutChangeToMetrics(m, data)

	util.OutPutInfo("压测结果: 基础信息收集完成!")
}

func BMLogCmdInfo(m *ModuleEntity) {
	util.OutPutInfo("压测结果: 开始协议信息收集!")

	cmd := exec.Command("/bin/bash", "-c", "cat ../log/robot.log* |grep CommandStat |grep -v cmd:11006")
	data, err := cmd.Output()
	if err != nil {
		log.Fatalf("BMLogCmdInfo: %s", err)
	}
	bReader := bytes.NewReader(data)
	reader := bufio.NewReader(bReader)
	var rightNum, totalNum int
	timeRoundsInfo := make([]float64, len(util.TimeRounds))
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			log.Fatal(err)
		}
		totalNum++
		if strings.Contains(line, "ret:true") {
			rightNum++
			_, delayTime := util.GetTime(line)
			timeRoundIndex := util.GetTimeRoundIndex(delayTime)
			timeRoundsInfo[timeRoundIndex]++
		}
	}
	m.AddMetrics("协议总数", strconv.Itoa(totalNum))
	m.AddMetrics("协议正确数", strconv.Itoa(rightNum))
	m.AddMetrics("正确比", strconv.FormatFloat(float64(rightNum)/float64(totalNum), 'f', 1, 64))

	chartPie := logop.ChartPie{
		TimeRound: util.GetTimeRoundKeys(),
		NumSlice:  timeRoundsInfo,
	}
	chartPie.Process(m.path, m.module+"_LogCmdInfo")

	util.OutPutInfo("压测结果: 协议信息收集完成!")
}

func ModuleLogProcess(m *ModuleEntity) {
	BMLogBaseInfo(m)
	BMLogCmdInfo(m)
}

func outPutChangeToMetrics(m *ModuleEntity, data []byte) {
	bReader := bytes.NewReader(data)
	reader := bufio.NewReader(bReader)
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			log.Fatal(err)
		}
		metrics := strings.Split(line, ":")
		m.AddMetrics(metrics[0], metrics[1])
	}
}

type CmdDelaySlice []*CmdDelay

func (c CmdDelaySlice) Len() int { return len(c) }
func (c CmdDelaySlice) Less(i, j int) bool {
	return strings.Compare(c[i].LogTime, c[j].LogTime) == -1
}
func (c CmdDelaySlice) Swap(i, j int) { c[i], c[j] = c[j], c[i] }

type CmdDelay struct {
	LogTime    string
	Min        float64
	Max        float64
	TotalDelay float64
	TotalNum   float64
}

func (c *CmdDelay) Add(delayTime float64) {
	c.TotalNum++
	if c.Min == 0 || delayTime < c.Min {
		c.Min = delayTime
	}
	if delayTime > c.Max {
		c.Max = delayTime
	}
	c.TotalDelay += delayTime
}

func BMLogCmdDelay(m *ModuleEntity, cmd int) {
	util.OutPutInfo(fmt.Sprintf("压测结果: 开始协议%d信息收集!", cmd))

	command := exec.Command("/bin/bash", "-c", "cat ../log/robot.log* |grep CommandStat |grep ret:true |grep cmd:"+strconv.Itoa(cmd))
	data, err := command.Output()
	if err != nil {
		log.Fatalf("BMLogCmdDelay: %s", err)
	}
	bReader := bytes.NewReader(data)
	reader := bufio.NewReader(bReader)
	cmdDelayMap := make(map[string]*CmdDelay)
	cmdDelaySlice := make(CmdDelaySlice, 0)
	var totalNum, maxDelay, totalDelay, avgDelay uint64
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			log.Fatal(err)
		}
		totalNum++
		logTime, delayTime := util.GetTime(line)
		if cmdDelay, exist := cmdDelayMap[logTime]; exist {
			cmdDelay.Add(float64(delayTime))
		} else {
			cmdDelay := &CmdDelay{
				LogTime: logTime,
			}
			cmdDelay.Add(float64(delayTime))
			cmdDelayMap[logTime] = cmdDelay
			cmdDelaySlice = append(cmdDelaySlice, cmdDelay)
		}
		if delayTime > maxDelay {
			maxDelay = delayTime
		}
		totalDelay += delayTime
	}
	avgDelay = totalDelay / totalNum
	m.AddMetrics(fmt.Sprintf("%d最大延迟", cmd), strconv.FormatUint(maxDelay, 10))
	m.AddMetrics(fmt.Sprintf("%d平均延迟", cmd), strconv.FormatUint(avgDelay, 10))
	sort.Sort(cmdDelaySlice)
	options := []string{"low", "avg", "high"}
	delayValues := make([][]float64, 0)
	lowDelay := make([]float64, 0)
	avgDelaySlice := make([]float64, 0)
	highDelay := make([]float64, 0)
	formatTimes := make([]string, 0)
	for _, cmdDelay := range cmdDelaySlice {
		formatTimes = append(formatTimes, cmdDelay.LogTime)
		lowDelay = append(lowDelay, cmdDelay.Min)
		avgDelaySlice = append(avgDelaySlice, cmdDelay.TotalDelay/cmdDelay.TotalNum)
		highDelay = append(highDelay, cmdDelay.Max)
	}
	delayValues = append(delayValues, lowDelay, avgDelaySlice, highDelay)
	chartLine := logop.ChartLine{
		FormatTime: formatTimes,
		Data:       delayValues,
		Options:    options,
	}
	chartLine.Process(m.path, m.module+"_LogCmdDelay"+strconv.Itoa(cmd))
	util.OutPutInfo(fmt.Sprintf("压测结果: 协议%d信息收集完成!", cmd))
}

//-----------------------------------------------------------------------------------------

var gExtraLogProcess = map[string]func(*ModuleEntity){
	"battle":        BattleLogProcess,
	"battle_report": BattleReportLogProcess,
}

func BattleLogProcess(m *ModuleEntity) {
	BMLogBattleBaseInfo(m)
	BMLogCmdDelay(m, 11053)
}

func BMLogBattleBaseInfo(m *ModuleEntity) {
	util.OutPutInfo("压测结果: 开始战斗基础信息收集!")

	command := util.GetCurrentDir() + viper.GetString("shell.battle_analyse")
	cmd := exec.Command("/bin/bash", "-c", command)
	data, err := cmd.Output()
	if err != nil {
		log.Fatalf("Execute Shell: %s failed with error: %s", command, err)
	}
	outPutChangeToMetrics(m, data)

	util.OutPutInfo("压测结果: 战斗基础信息完成!")
}

func BattleReportLogProcess(m *ModuleEntity) {
	//BMLogCmdDelay(m, 11093)
}
