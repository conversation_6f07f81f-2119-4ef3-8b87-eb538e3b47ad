package logic

import (
	"benchmarkAgent/config"
	"benchmarkAgent/util"
	"strings"
	"sync"
)

type FullBenchmark struct {
	*Benchmark
}

func NewFullBench() *FullBenchmark {
	fullBench := &FullBenchmark{
		newBenchmark(),
	}
	fullBench.init()

	return fullBench
}

func (f *FullBenchmark) init() {
	f.delayPie.initTimeRound()
	f.resetFuncBox()
}

func (f *FullBenchmark) resetFuncBox() {
	f.initFuncBox()
	f.funcBox = append(f.funcBox, f.genFullBenchmarkChart)
}

func (f *FullBenchmark) genFullBenchmarkChart(wg *sync.WaitGroup) {
	defer wg.Done()

	f.delayPie.createPipChart("full_pie.html")
	f.delayLine.createLineChart("full_line.html", "延迟折线")
}

func (f *FullBenchmark) filter(logStr string) {
	if strings.Contains(logStr, "cmd:11006") { // 心跳协议，去掉
		return
	} else if strings.Contains(logStr, "C2L_GM") { // 发送资源协议，去掉
		return
	} else if strings.Contains(logStr, "CommandStat") {
		f.logChan <- logStr
	}
}

func (f *FullBenchmark) Start() {
	go f.fetch(util.GetParentDir()+config.LogPath, "robot.log", f.filter)

	for {
		select {
		case logStr := <-f.logChan:
			f.process(logStr)
		case <-f.stopChan:
			f.stopTag++
			if f.stopTag == f.logFileNum {
				f.output()
			}
		}
	}
}

func (f *FullBenchmark) process(logStr string) {
	f.num.addTotalNum()
	f.num.addOnlineNum(logStr)
	execTime, delayTime := util.GetTime(logStr)
	if execTime == "" && delayTime == 0 {
		return
	}

	f.updateDelayTime(delayTime)
	f.num.addBattleNum(logStr)
	f.delayLine.addTime(execTime, delayTime)
	f.delayPie.updateNum(delayTime)
}
