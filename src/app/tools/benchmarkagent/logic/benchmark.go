package logic

import (
	"benchmarkAgent/util"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"sync"
)

type Benchmark struct {
	delayLine      *LineChart // 折线图数据
	delayPie       *PieChart  // 饼图数据
	num            *Num
	totalDelayTime uint64 // 总延迟时间
	maxDelayTime   uint64 // 最大延迟时间
	logChan        chan string
	stopChan       chan struct{}
	stopTag        int // 结束标识
	logFileNum     int // 日志文件数量
	wg             sync.WaitGroup
	funcBox        []func(*sync.WaitGroup)
}

func newBenchmark() *Benchmark {
	return &Benchmark{
		wg:        sync.WaitGroup{},
		logChan:   make(chan string, 7),
		stopChan:  make(chan struct{}, 1),
		num:       newNum(),
		delayLine: newLineChart(),
		delayPie:  newPieChart(),
	}
}

func (b *Benchmark) initFuncBox() {
	b.funcBox = append(b.funcBox, GetRedisMem)
	b.funcBox = append(b.funcBox, GetRobotProtoDelay)
	b.funcBox = append(b.funcBox, GetLogicProtoDelay)
	b.funcBox = append(b.funcBox, GetPprofCpu)
	b.funcBox = append(b.funcBox, GetPprofHeap)
}

func (b *Benchmark) updateDelayTime(delayTime uint64) {
	if b.maxDelayTime < delayTime {
		b.maxDelayTime = delayTime
	}

	b.totalDelayTime += delayTime
}

func (b *Benchmark) formatPrint() {
	avgDelay := b.totalDelayTime / uint64(b.num.totalNum)
	fmt.Printf("协议延迟: 0~%dms | 平均延迟：%dms  \n", b.maxDelayTime, avgDelay)
	fmt.Printf("登录次数：%d   |  战斗次数：%d \n", b.num.onlineNum, b.num.battleNum)
}

func (b *Benchmark) fetch(dirName, fileName string, filter func(string)) {
	files, err := ioutil.ReadDir(dirName)
	if err != nil {
		log.Fatalf("get file failed. err: %s", err)
	}

	var filePaths []string
	filePaths, b.logFileNum = util.GenerateLogPath(files, dirName, fileName)
	if b.logFileNum == 0 {
		log.Fatal("get file failed. file is nil")
	}

	for _, filePath := range filePaths {
		util.Read(filePath, filter, b.stopChan)
	}
}

func (b *Benchmark) output() {
	b.formatPrint()
	b.wg.Add(len(b.funcBox))
	for _, call := range b.funcBox {
		call(&b.wg)
	}
	b.wg.Wait()
	os.Exit(1)
}
