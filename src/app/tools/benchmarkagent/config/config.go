package config

import (
	"benchmarkAgent/util"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strconv"

	"github.com/mitchellh/go-homedir"
	"github.com/spf13/viper"
	"github.com/vicanso/go-charts/v2"
)

var (
	LogPath          string
	PprofPath        string
	LogicIP          string
	Username         string
	Port             int
	SshKey           string
	BattleProto      []string
	ProfileAddr      string
	PromethusAddr    string
	CpuMetric        string
	MemoryMetric     string
	NetOutMetric     string
	NetInMetric      string
	LogicSysInstance string
	FileRobot        string
	LogicGoInstance  string
)

func Init() {
	LogicIP = viper.GetString("logic_server.ip")
	Username = viper.GetString("logic_server.username")
	Port = viper.GetInt("logic_server.port")
	LogPath = viper.GetString("path.log")
	PprofPath = viper.GetString("path.pprof")
	SshKey = parseKeyFromFile()
	BattleProto = viper.GetStringSlice("battle_proto")
	ProfileAddr = LogicIP + ":" + strconv.Itoa(viper.GetInt("logic_server.profile_port"))
	PromethusAddr = viper.GetString("promethus.addr")
	CpuMetric = viper.GetString("promethus.cpu_used")
	MemoryMetric = viper.GetString("promethus.memory_used")
	NetOutMetric = viper.GetString("promethus.net_out")
	NetInMetric = viper.GetString("promethus.net_in")
	LogicSysInstance = viper.GetString("promethus.logic_sys_instance")
	LogicGoInstance = viper.GetString("promethus.logic_go_instance")
	FileRobot = viper.GetString("config.robot")
}

func LoadConfig() {
	yamlPath := util.GetParentDir() + "/config/benchmark.yaml"
	viper.SetConfigFile(yamlPath)
	if err := viper.ReadInConfig(); err != nil {
		log.Fatal(fmt.Sprintf("load config benchmark.yaml failed, error: %s", err))
	}

	buff, err := ioutil.ReadFile(util.GetParentDir() + "/config/ziti.ttf")
	if err != nil {
		panic(err)
	}
	err = charts.InstallFont("chinese", buff)
	if err != nil {
		panic(err)
	}

	Init()
}

func parseKeyFromFile() string {
	keyPath := util.GetParentDir() + viper.GetString("path.private_key")
	keyExpandPath, err := homedir.Expand(keyPath)
	if err != nil {
		log.Fatal("find key's home dir failed", err)
	}

	key, err := os.ReadFile(keyExpandPath)
	if err != nil {
		log.Fatal(fmt.Sprintf("ssh key file read failed. err: %s", err))
	}

	return string(key)
}
