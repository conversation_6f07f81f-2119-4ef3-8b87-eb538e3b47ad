package logop

import (
	"benchmarkAgent/util"

	"github.com/vicanso/go-charts/v2"

	seaUtil "gitlab.qdream.com/kit/sea/util"
)

type ChartLine struct {
	LogProcess
	FormatTime []string    // 时间轴
	Data       [][]float64 // 数据,多维数组是为了支持多条折线
	Options    []string    //折线的名称
}

func (c *ChartLine) Process(path string, fileName string) {
	p, err := charts.LineRender(
		c.Data,
		charts.XAxisDataOptionFunc(c.FormatTime),
		charts.WidthOptionFunc(2048),
		charts.HeightOptionFunc(2048),
		charts.PaddingOptionFunc(charts.Box{
			Top:    5,
			Bottom: 10,
		}),
		charts.LegendLabelsOptionFunc(c.Options, charts.PositionCenter),
		charts.ThemeOptionFunc(util.ChartDefaultTheme),
	)

	if err != nil {
		panic(err)
	}

	buf, err := p.Bytes()
	if err != nil {
		panic(err)
	}
	err = util.WriteFile(path, fileName+".png", buf)
	if err != nil {
		panic(err)
	}
}

type ChartPie struct {
	LogProcess
	TimeRound []string  // 时间范围
	NumSlice  []float64 // 对应数值
}

func (c *ChartPie) Process(path string, fileName string) {
	p, err := charts.PieRender(
		c.NumSlice,
		charts.WidthOptionFunc(2048),
		charts.HeightOptionFunc(2048),
		charts.PaddingOptionFunc(charts.Box{
			Top:    20,
			Right:  20,
			Bottom: 20,
			Left:   20,
		}),
		charts.LegendOptionFunc(charts.LegendOption{
			Orient: charts.OrientVertical,
			Data:   c.TimeRound,
			Left:   charts.PositionLeft,
		}),
		charts.PieSeriesShowLabel(),
		charts.ThemeOptionFunc(util.ChartDefaultTheme),
	)
	if err != nil {
		panic(err)
	}

	buf, err := p.Bytes()
	if err != nil {
		panic(err)
	}

	err = util.WriteFile(path, fileName+".png", buf)
	if err != nil {
		panic(err)
	}
}

type ChartTable struct {
	LogProcess
	Header []string //表头
	Data   [][]string
}

func (c *ChartTable) Process(path string, fileName string) {
	opt := charts.TableChartOption{
		Header:     c.Header,
		Data:       c.Data,
		FontFamily: "chinese",
		Width:      2048,
	}
	p, err := charts.TableOptionRender(opt)
	if err != nil {
		panic(err)
	}

	buf, err := p.Bytes()
	if err != nil {
		panic(err)
	}

	err = util.WriteFile(path, fileName+".png", buf)
	if err != nil {
		panic(err)
	}
}

type TextLog struct {
	LogProcess
	Log string
}

func (t *TextLog) Process(path string, fileName string) {
	buf := seaUtil.Bytes(t.Log)
	err := util.WriteFile(path, fileName+".txt", buf)
	if err != nil {
		panic(err)
	}
}
