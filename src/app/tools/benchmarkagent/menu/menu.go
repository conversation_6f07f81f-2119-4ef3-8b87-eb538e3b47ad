package menu

import (
	"benchmarkAgent/logic"
	"fmt"
)

var Manager *MainMenu

type Menu interface {
	menu() []*SubMenu
}

type SubMenu struct {
	name string
	exec func()
}

func newSubMenu(name string, exec func()) *SubMenu {
	return &SubMenu{
		name: name,
		exec: exec,
	}
}

type MainMenu struct {
	subMenuExec []*SubMenu
}

func NewMainMenu() *MainMenu {
	menu := &MainMenu{}
	menu.init()

	return menu
}

func (m *MainMenu) init() {
	m.subMenuExec = append(m.subMenuExec, newSubMenu("预创角", beforeCreateUser))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("压测", newBenchmarkMenu().run))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("获取压测数据", newBenchmarkDataMenu().run))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("重置游戏服xml量表", resetLogicXml))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("清空游戏服db数据", cleanLogicDB))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("清空游戏服日志", cleanLogicLog))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("清空机器人服日志", cleanRobotLog))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("功能压测", new(logic.ModuleEntity).Benchmark))
	m.subMenuExec = append(m.subMenuExec, newSubMenu("退出", quit))
}

func (m *MainMenu) Run() {
	fmt.Println("--------------- 欢迎使用天神压测系统 -----------------")
	execMenu(m)
}

func (m *MainMenu) menu() []*SubMenu {
	return m.subMenuExec
}

func init() {
	Manager = NewMainMenu()
}

type BenchmarkMenu struct {
	subMenuExec []*SubMenu
}

func newBenchmarkMenu() *BenchmarkMenu {
	menu := &BenchmarkMenu{
		subMenuExec: make([]*SubMenu, 3),
	}
	menu.init()

	return menu
}

func (b *BenchmarkMenu) init() {
	b.subMenuExec[0] = newSubMenu("全量压测", fullBenchmark)
	b.subMenuExec[1] = newSubMenu("创角压测", createUser)
	b.subMenuExec[2] = newSubMenu("单功能压测", singleModuleBenchmark)
}

func (b *BenchmarkMenu) menu() []*SubMenu {
	return b.subMenuExec
}

func (b *BenchmarkMenu) run() {
	execMenu(b)
}

type BenchmarkDataMenu struct {
	subMenuExec []*SubMenu
}

func newBenchmarkDataMenu() *BenchmarkDataMenu {
	menu := &BenchmarkDataMenu{
		subMenuExec: make([]*SubMenu, 3),
	}
	menu.init()

	return menu
}

func (b *BenchmarkDataMenu) init() {
	b.subMenuExec[0] = newSubMenu("全量压测数据", logic.NewFullBench().Start)
	b.subMenuExec[1] = newSubMenu("创角压测数据", logic.NewCreateUserBenchmark().Start)
	b.subMenuExec[2] = newSubMenu("单功能压测数据", logic.NewFullBench().Start)
}

func (b *BenchmarkDataMenu) menu() []*SubMenu {
	return b.subMenuExec
}

func (b *BenchmarkDataMenu) run() {
	execMenu(b)
}
