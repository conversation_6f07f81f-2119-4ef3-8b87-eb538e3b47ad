package main

import (
	"app/service/feishurobot"
	"bufio"
	"errors"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

var LogPath = flag.String("path", "/home/<USER>/namge/server/log/", "要监控的路径,多个用逗号隔开")
var MatchStr = flag.String("match", `runtime/debug.Stack()`, "要监控的关键字")
var DataRaceStr = flag.String("data_race", `DATA RACE`, "data race的监控")
var FileName = flag.String("file", "logic.log,cross.log,service_errors.log", "要监控文件,多个用逗号隔开")
var flagLogDir = flag.String("log_dir", "./", "service log dir")
var flagLogLevel = flag.String("log_level", "DEBUG", "log level")

var wg sync.WaitGroup

const ErrorLineCount int = 15

func main() {
	flag.Parse()
	var files []string

	logFilename := *flagLogDir + "monitor-log.log"
	cfg := util.NewL4GConfig(*flagLogLevel, logFilename)
	l4g.Global.LoadConfiguration("", []byte(cfg))
	defer l4g.Close()

	paths := strings.Split(*LogPath, ",")
	names := strings.Split(*FileName, ",")
	for _, p := range paths {
		for _, n := range names {
			mfiles, err := matchFile(p, n)
			if err != nil {
				log.Fatal("match file error :", err)
			}
			files = append(files, mfiles...)
		}
	}
	for _, f := range files {
		wg.Add(1)
		go MonitorFile(f, "")
	}
	wg.Wait()
}

func MonitorFile(filename string, match string) {
	defer wg.Done()
	l4g.Debugf("begin monitor file %s \n", filename)

	f, err := openFile(filename, true)
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			return
		}
		log.Fatal(err)
	}
	defer f.Close()
	timer := time.NewTicker(1 * time.Second)
	var offset int64 = 0
	for {
		select {
		case <-timer.C:
			err, offset = dealFile(f, offset)
			if err != nil {
				//closeFile(f)
				log.Fatal(err)
			}
			f2, ferr := openFile(filename, false)
			if ferr != nil {
				if errors.Is(ferr, os.ErrNotExist) {
					l4g.Debugf("open file :%s not exist:%s \n", filename, ferr)
					continue
				}
				log.Fatal(err)
			}
			fInfo, _ := f.Stat()
			f2Info, _ := f2.Stat()
			if os.SameFile(fInfo, f2Info) {
				closeFile(f2)
			} else {
				closeFile(f)
				l4g.Debugf("open new file:%s \n", filename)
				f = f2
				offset = 0
			}
		}
	}

}

func openFile(filename string, first bool) (*os.File, error) {
	f, err := os.Open(filename)
	if err != nil {
		return f, err
	}
	if first {
		//首次打开文件
		// 定位到文件末尾
		f.Seek(0, 2)
	}
	return f, nil
}

func closeFile(f *os.File) {
	f.Close()
}

func dealFile(f *os.File, offset int64) (error, int64) {
	fi, err := f.Stat()
	if err != nil {
		return err, offset
	}

	if fi.Size() < offset {
		// 文件被截断了
		offset = 0
		f.Seek(0, 0)
	}

	reader := bufio.NewReader(f)
	var errorsLine []string
	var preline string
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			//fmt.Printf("read line error is %s \n", err)
			break //这里要退出，不能return
		}
		//fmt.Println(line)

		if strings.Contains(line, *MatchStr) {
			//fmt.Println("======================")
			//fmt.Println(line)
			//fmt.Println("======================")
			if preline != "" && strings.Contains(preline, "panic") {
				errorsLine = append(errorsLine, fmt.Sprintf("file_path:[%s] has panic msg\n", f.Name()))
				errorsLine = append(errorsLine, preline)
				errorsLine = append(errorsLine, line)
			}
		} else if strings.Contains(line, *DataRaceStr) {
			errorsLine = append(errorsLine, fmt.Sprintf("file_path:[%s] has DATA RACE msg\n", f.Name()))
		} else {
			if len(errorsLine) > 0 && len(errorsLine) < ErrorLineCount {
				errorsLine = append(errorsLine, line)
			}
		}
		if len(errorsLine) >= ErrorLineCount {
			errStr := strings.Join(errorsLine, "")
			errorsLine = errorsLine[:0]
			//fmt.Println("++++++++++++++++++++++++")
			feishurobot.SendMsg(errStr)
			//fmt.Println(errStr)
			//fmt.Println("++++++++++++++++++++++++")
		}
		preline = line
	}
	if len(errorsLine) > 0 {
		errStr := strings.Join(errorsLine, "")
		errorsLine = errorsLine[:0]
		//fmt.Println("++++++++++++++++++++++++")
		feishurobot.SendMsg(errStr)
		//fmt.Println(errStr)
		//fmt.Println("++++++++++++++++++++++++")
	}

	offset, err = f.Seek(0, 1)
	if err != nil {
		return err, offset
	}
	return nil, offset
}

func matchFile(path string, name string) ([]string, error) {
	l4g.Debugf("begin match path:%s name:%s \n", path, name)
	pattern := filepath.Join(path, name)
	matches, err := filepath.Glob(pattern)
	if err != nil {
		l4g.Errorf("match error:%s \n", err)
	} else {
		l4g.Debugf("match all :%+v \n", matches)
	}
	return matches, err

}
