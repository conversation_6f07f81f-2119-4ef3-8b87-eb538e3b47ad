package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"sort"

	l4g "github.com/ivanabc/log4go"
)

type PassFunc func(*Pass)

var gPassActions []PassFunc

func InitPassActions() {
	//gPassActions = append(gPassActions, (*Pass).C2LPassGetData)
	gPassActions = append(gPassActions, (*Pass).C2LPassReceiveAward)
	gPassActions = append(gPassActions, (*Pass).C2LPassLevelBuy)
}

type Pass struct {
	*UserLoad
	levelBuyMap           map[uint32]uint32 // 等级购买的taskID,  key: passID   value: taskID 递增
	levelBuyCompleteSlice map[uint32][]uint32
}

func NewPass(r *Robot) *Pass {
	p := &Pass{
		UserLoad:              NewUserLoad(r),
		levelBuyMap:           make(map[uint32]uint32),
		levelBuyCompleteSlice: make(map[uint32][]uint32, 4),
	}
	return p
}

// 断线重连重置数据
func (p *Pass) reset() {
	p.load.reset()

}

func (p *Pass) RandAction() {
	switch p.load.state() {
	case dataLoadNone:
		p.C2LInit()
	case dataLoading:
		p.C2LRetryInit()
	default:
		if length := len(gPassActions); length != 0 {
			gPassActions[p.u.rd.Intn(length)](p)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (p *Pass) C2LInit() {
	p.load.Lock()
	p.load.setState(dataLoading)

	p.C2LPassGetData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (p *Pass) L2CInitRet(ret uint32) {
	if p.load.state() == dataLoading {
		p.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			p.load.setState(dataLoaded)
		} else {
			p.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (p *Pass) C2LRetryInit() {
	if p.load.Lock() {
		//p.C2LPassFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (p *Pass) C2LPassGetData() {
	cmsg := &cl.C2L_PassGetData{}
	sysIDs := make([]uint32, 0, len(goxml.GetData().PassInfoM.Datas))
	for id := range goxml.GetData().PassInfoM.Datas {
		sysIDs = append(sysIDs, id)
	}
	cmsg.SysIds = sysIDs
	p.u.SendCmd(uint32(cl.ID_MSG_C2L_PassGetData), cmsg)
}

func (p *Pass) L2CPassGetData(recv *cl.L2C_PassGetData) {
	l4g.Debug("[Pass] %s L2CPassGetData ret:%s, recv:%+v",
		p.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	p.L2CInitRet(recv.Ret)
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (p *Pass) C2LPassReceiveAward() {
	cmsg := &cl.C2L_PassReceiveAward{}

	//passID := p.randomPassID()
	passID := uint32(11)
	taskID := p.randomCompletedTaskID(passID)
	if taskID == 0 {
		return
	}

	cmsg.SysId = passID
	cmsg.Ids = []uint32{taskID}

	p.u.SendCmd(uint32(cl.ID_MSG_C2L_PassReceiveAward), cmsg)
}

func (p *Pass) L2CPassReceiveAward(recv *cl.L2C_PassReceiveAward) {
	l4g.Debug("[Pass] %s L2CPassReceiveAward ret:%s, recv:%+v",
		p.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (p *Pass) L2CPassBuyNotify(recv *cl.L2C_PassBuyNotify) {
	l4g.Debug("[Pass] %s L2CPassBuyNotify ret:%s, recv:%+v",
		p.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (p *Pass) C2LPassLevelBuy() {
	cmsg := &cl.C2L_PassLevelBuy{}
	//passID := p.randomPassIDByLevelBuy()
	passID := uint32(11)
	taskID := getTaskID(passID, p.levelBuyMap)

	cmsg.SysId = passID
	cmsg.TaskIds = []uint32{taskID}

	p.u.SendCmd(uint32(cl.ID_MSG_C2L_PassLevelBuy), cmsg)
}

func (p *Pass) L2CPassLevelBuy(recv *cl.L2C_PassLevelBuy) {
	l4g.Debug("[Pass] %s L2CPassLevelBuy ret:%s, recv:%+v",
		p.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	p.levelBuyCompleteSlice[recv.SysId] = append(p.levelBuyCompleteSlice[recv.SysId], recv.TaskIds...)
	for _, id := range recv.TaskIds {
		if id > p.levelBuyMap[recv.SysId] {
			p.levelBuyMap[recv.SysId] = id
		}
	}
}

func (p *Pass) randomPassID() uint32 {
	passIDs := make([]uint32, 0, len(goxml.GetData().PassInfoM.Datas))
	for id := range goxml.GetData().PassInfoM.Datas {
		passIDs = append(passIDs, id)
	}
	max := len(passIDs)
	if max > 0 {
		i := p.u.rd.Intn(max)
		return passIDs[i]
	}

	return 0
}

func (p *Pass) randomPassIDByLevelBuy() uint32 {
	passIDs := make([]uint32, 0, len(goxml.GetData().PassInfoM.Datas))
	for _, info := range goxml.GetData().PassInfoM.Datas {
		if info.Cycle > 0 {
			passIDs = append(passIDs, info.Id)
		}
	}
	max := len(passIDs)
	if max > 0 {
		i := p.u.rd.Intn(max)
		return passIDs[i]
	}

	return 0
}

func getTaskID(passID uint32, cache map[uint32]uint32) uint32 {
	if cache[passID] == 0 {
		taskIDs := make([]int, 0, 10)
		for _, info := range goxml.GetData().PassTaskInfoM.Datas {
			if info.PassId == passID {
				taskIDs = append(taskIDs, int(info.Id))
			}
		}
		if len(taskIDs) > 0 {
			sort.Ints(taskIDs)
			return uint32(taskIDs[0])
		}
	}

	return cache[passID] + 1
}

func (p *Pass) randomCompletedTaskID(passID uint32) uint32 {
	taskIDs := p.levelBuyCompleteSlice[passID]
	max := len(taskIDs)
	var taskID uint32
	if max > 0 {
		idx := p.u.rd.Intn(max)
		taskID = taskIDs[idx]
		//p.levelBuyCompleteSlice[passID] = append(p.levelBuyCompleteSlice[passID][:idx], p.levelBuyCompleteSlice[passID][idx+1:]...)
	}

	return taskID
}
