package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type WorldBossFunc func(*WorldBoss)

var gWorldBossActions []WorldBossFunc

func InitWorldBossActions() {
	gWorldBossActions = append(gWorldBossActions, (*WorldBoss).C2LWorldBossGetData)
	gWorldBossActions = append(gWorldBossActions, (*WorldBoss).C2LWorldBossSelectLevel)
	gWorldBossActions = append(gWorldBossActions, (*WorldBoss).C2LWorldBossRoomInfo)
	gWorldBossActions = append(gWorldBossActions, (*WorldBoss).C2LWorldBossGetRoomLog)
	gWorldBossActions = append(gWorldBossActions, (*WorldBoss).C2LWorldBossRank)
	gWorldBossActions = append(gWorldBossActions, (*WorldBoss).C2LWorldBossWorship)
	gWorldBossActions = append(gWorldBossActions, (*WorldBoss).C2LWorldBossFight)
}

type WorldBoss struct {
	*UserLoad
}

func NewWorldBoss(r *Robot) *WorldBoss {
	d := &WorldBoss{
		UserLoad: NewUserLoad(r),
	}
	return d
}

// 断线重连重置数据
func (d *WorldBoss) reset() {
	d.load.reset()

	//edit here
}

func (d *WorldBoss) RandAction() {
	switch d.load.state() {
	case dataLoadNone:
		d.C2LInit()
	case dataLoading:
		d.C2LRetryInit()
	default:
		if length := len(gWorldBossActions); length != 0 {
			gWorldBossActions[d.u.rd.Intn(length)](d)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (d *WorldBoss) C2LInit() {
	d.load.Lock()
	d.load.setState(dataLoading)

	d.C2LWorldBossGetData()
	//d.C2LWorldBossRoomInfo()
	//d.C2LWorldBossGetRoomLog()
	//d.C2LWorldBossRank()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (d *WorldBoss) L2CInitRet(ret uint32) {
	if d.load.state() == dataLoading {
		d.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			d.load.setState(dataLoaded)
		} else {
			d.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (d *WorldBoss) C2LRetryInit() {
	if d.load.Lock() {
		//d.C2LWorldBossFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (d *WorldBoss) C2LWorldBossGetData() {
	cmsg := &cl.C2L_WorldBossGetData{}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_WorldBossGetData), cmsg)
}

func (d *WorldBoss) L2CWorldBossGetData(recv *cl.L2C_WorldBossGetData) {
	l4g.Debug("[WorldBoss] %s L2CWorldBossGetData ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//fmt.Println(fmt.Sprintf("[WorldBoss] %s L2CWorldBossGetData ret:%s, recv:%+v",
	//	d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	if recv.WorldBoss == nil || recv.WorldBoss.Data == nil || recv.WorldBoss.Data.Room == nil {
		d.C2LWorldBossSelectLevel()
	}
}

func (d *WorldBoss) C2LWorldBossSelectLevel() {
	cmsg := &cl.C2L_WorldBossSelectLevel{}
	cmsg.Level = 3
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_WorldBossSelectLevel), cmsg)
}

func (d *WorldBoss) L2CWorldBossSelectLevel(recv *cl.L2C_WorldBossSelectLevel) {
	l4g.Debug("[WorldBoss] %s L2CWorldBossSelectLevel ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//fmt.Println(fmt.Sprintf("[WorldBoss] %s L2CWorldBossSelectLevel ret:%s, recv:%+v",
	//	d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	d.C2LWorldBossFight()
}

func (d *WorldBoss) C2LWorldBossRoomInfo() {
	cmsg := &cl.C2L_WorldBossRoomInfo{}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_WorldBossRoomInfo), cmsg)
}

func (d *WorldBoss) L2CWorldBossRoomInfo(recv *cl.L2C_WorldBossRoomInfo) {
	l4g.Debug("[WorldBoss] %s L2CWorldBossRoomInfo ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//fmt.Println(fmt.Sprintf("[WorldBoss] %s L2CWorldBossRoomInfo ret:%s, recv:%+v",
	//	d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (d *WorldBoss) C2LWorldBossGetRoomLog() {
	cmsg := &cl.C2L_WorldBossGetRoomLog{}
	cmsg.Num = 10
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_WorldBossGetRoomLog), cmsg)
}

func (d *WorldBoss) L2CWorldBossGetRoomLog(recv *cl.L2C_WorldBossGetRoomLog) {
	l4g.Debug("[WorldBoss] %s L2CWorldBossGetRoomLog ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//fmt.Println(fmt.Sprintf("[WorldBoss] %s L2CWorldBossGetRoomLog ret:%s, recv:%+v",
	//	d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (d *WorldBoss) C2LWorldBossRank() {
	cmsg := &cl.C2L_WorldBossRank{}
	cmsg.Type = 2
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_WorldBossRank), cmsg)
}

func (d *WorldBoss) L2CWorldBossRank(recv *cl.L2C_WorldBossRank) {
	l4g.Debug("[WorldBoss] %s L2CWorldBossRank ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//fmt.Println(fmt.Sprintf("[WorldBoss] %s L2CWorldBossRank ret:%s, recv:%+v",
	//	d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (d *WorldBoss) C2LWorldBossWorship() {
	cmsg := &cl.C2L_WorldBossWorship{}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_WorldBossWorship), cmsg)
}

func (d *WorldBoss) L2CWorldBossWorship(recv *cl.L2C_WorldBossWorship) {
	l4g.Debug("[WorldBoss] %s L2CWorldBossWorship ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//fmt.Println(fmt.Sprintf("[WorldBoss] %s L2CWorldBossWorship ret:%s, recv:%+v",
	//	d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (d *WorldBoss) C2LWorldBossFight() {
	cmsg := &cl.C2L_WorldBossFight{
		Type: 1,
	}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_WorldBossFight), cmsg)
}

func (d *WorldBoss) L2CWorldBossFight(recv *cl.L2C_WorldBossFight) {
	l4g.Debug("[WorldBoss] %s L2CWorldBossFight ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//fmt.Println(fmt.Sprintf("[WorldBoss] %s L2CWorldBossFight ret:%s, recv:%+v",
	//	d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}
