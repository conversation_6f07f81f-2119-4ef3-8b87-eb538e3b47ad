package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotActivityRechargeCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ActivityRechargeGet), L2CActivityRechargeGetCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ActivityRechargeBuy), L2CActivityRechargeBuyCommand{})
}

type L2CActivityRechargeGetCommand struct {
}

func (c L2CActivityRechargeGetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ActivityRechargeGet{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CActivityRechargeGetCommand unmarshal error: %s", err)
		return false
	}
	robot.activityRecharge.L2CActivityRechargeGet(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CActivityRechargeBuyCommand struct {
}

func (c L2CActivityRechargeBuyCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ActivityRechargeBuy{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CActivityRechargeBuyCommand unmarshal error: %s", err)
		return false
	}
	robot.activityRecharge.L2CActivityRechargeBuy(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
