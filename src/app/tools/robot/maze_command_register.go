package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotMazeCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MazeGetMap), L2CMazeGetMapCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MazeTriggerEvent), L2CMazeTriggerEventCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MazeRecoveryHero), L2CMazeRecoveryHeroCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MazeGetGrid), L2CMazeGetGridCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MazeBuyRevive), L2CMazeBuyReviveCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MazeUseItem), L2CMazeUseItemCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MazeTaskReceiveAward), L2CMazeTaskReceiveAwardCommand{})
}

type L2CMazeGetMapCommand struct {
}

func (c L2CMazeGetMapCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MazeGetMap{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMazeGetMapCommand unmarshal error: %s", err)
		return false
	}
	robot.maze.L2CMazeGetMap(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMazeTriggerEventCommand struct {
}

func (c L2CMazeTriggerEventCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MazeTriggerEvent{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMazeTriggerEventCommand unmarshal error: %s", err)
		return false
	}
	robot.maze.L2CMazeTriggerEvent(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMazeRecoveryHeroCommand struct {
}

func (c L2CMazeRecoveryHeroCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MazeRecoveryHero{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMazeRecoveryHeroCommand unmarshal error: %s", err)
		return false
	}
	robot.maze.L2CMazeRecoveryHero(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMazeGetGridCommand struct {
}

func (c L2CMazeGetGridCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MazeGetGrid{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMazeGetGridCommand unmarshal error: %s", err)
		return false
	}
	robot.maze.L2CMazeGetGrid(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMazeBuyReviveCommand struct {
}

func (c L2CMazeBuyReviveCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MazeBuyRevive{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMazeBuyReviveCommand unmarshal error: %s", err)
		return false
	}
	robot.maze.L2CMazeBuyReceive(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMazeUseItemCommand struct {
}

func (c L2CMazeUseItemCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MazeUseItem{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMazeUseItemCommand unmarshal error: %s", err)
		return false
	}

	robot.maze.L2CMazeUseItem(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMazeTaskReceiveAwardCommand struct {
}

func (c L2CMazeTaskReceiveAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MazeTaskReceiveAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMazeTaskReceiveAwardCommand unmarshal error: %s", err)
		return false
	}

	robot.maze.L2CMazeTaskReceiveAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
