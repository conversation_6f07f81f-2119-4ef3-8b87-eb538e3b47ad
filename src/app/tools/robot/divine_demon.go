package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"fmt"

	l4g "github.com/ivanabc/log4go"
)

type DivineDemonFunc func(*DivineDemon)

var gDivineDemonActions []DivineDemonFunc

func InitDivineDemonActions() {
	gDivineDemonActions = append(gDivineDemonActions, (*DivineDemon).C2LDivineDemonGetOpenActivity)
	gDivineDemonActions = append(gDivineDemonActions, (*DivineDemon).C2LDivineDemonSummon)
	gDivineDemonActions = append(gDivineDemonActions, (*DivineDemon).C2LDivineDemonReceiveTaskAward)
}

type DivineDemon struct {
	*UserLoad
}

func NewDivineDemon(r *Robot) *DivineDemon {
	d := &DivineDemon{
		UserLoad: NewUserLoad(r),
	}
	return d
}

// 断线重连重置数据
func (d *DivineDemon) reset() {
	d.load.reset()

	//edit here
}

func (d *DivineDemon) RandAction() {
	switch d.load.state() {
	case dataLoadNone:
		d.C2LInit()
	case dataLoading:
		d.C2LRetryInit()
	default:
		if length := len(gDivineDemonActions); length != 0 {
			gDivineDemonActions[d.u.rd.Intn(length)](d)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (d *DivineDemon) C2LInit() {
	d.load.Lock()
	d.load.setState(dataLoading)

	//d.C2LDivineDemonFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数

	//d.C2LDivineDemonGetOpenActivity()
	d.C2LDivineDemonSummon()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (d *DivineDemon) L2CInitRet(ret uint32) {
	if d.load.state() == dataLoading {
		d.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			d.load.setState(dataLoaded)
		} else {
			d.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (d *DivineDemon) C2LRetryInit() {
	if d.load.Lock() {
		//d.C2LDivineDemonFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (d *DivineDemon) C2LDivineDemonGetOpenActivity() {
	cmsg := &cl.C2L_DivineDemonGetOpenActivity{}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_DivineDemonGetOpenActivity), cmsg)
}

func (d *DivineDemon) L2CDivineDemonGetOpenActivity(recv *cl.L2C_DivineDemonGetOpenActivity) {
	l4g.Debug("[DivineDemon] %s L2CDivineDemonGetOpenActivity ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	fmt.Println(fmt.Sprintf("[DivineDemon] %s L2CDivineDemonGetOpenActivity ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *DivineDemon) C2LDivineDemonSummon() {
	cmsg := &cl.C2L_DivineDemonSummon{}
	cmsg.Id = 0
	cmsg.SummonType = 1

	d.u.SendCmd(uint32(cl.ID_MSG_C2L_DivineDemonSummon), cmsg)
}

func (d *DivineDemon) L2CDivineDemonSummon(recv *cl.L2C_DivineDemonSummon) {
	l4g.Debug("[DivineDemon] %s L2CDivineDemonSummon ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	fmt.Println(fmt.Sprintf("[DivineDemon] %s L2CDivineDemonSummon ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *DivineDemon) C2LDivineDemonReceiveTaskAward() {
	cmsg := &cl.C2L_DivineDemonReceiveTaskAward{}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_DivineDemonReceiveTaskAward), cmsg)
}

func (d *DivineDemon) L2CDivineDemonReceiveTaskAward(recv *cl.L2C_DivineDemonReceiveTaskAward) {
	l4g.Debug("[DivineDemon] %s L2CDivineDemonReceiveTaskAward ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *DivineDemon) L2CDivineDemonUpdateTaskProgress(recv *cl.L2C_DivineDemonUpdateTaskProgress) {
	l4g.Debug("[DivineDemon] %s L2CDivineDemonUpdateTaskProgress ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
