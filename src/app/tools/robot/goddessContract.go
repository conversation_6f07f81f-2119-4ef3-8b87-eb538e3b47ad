package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
)

type GoddessContractFunc func(*GoddessContarct)

var gGoddessContractActions []GoddessContractFunc

func InitGoddessContarctActions() {
	//gGoddessContarctActions = append(gGoddessContarctActions, (*GoddessContarct).C2LGoddessContractGetData)
	gGoddessContractActions = append(gGoddessContractActions, (*GoddessContarct).C2LGoddessFeed)
	gGoddessContractActions = append(gGoddessContractActions, (*GoddessContarct).C2LGoddessTouch)
	gGoddessContractActions = append(gGoddessContractActions, (*GoddessContarct).C2LGoddessStoryAward)
	gGoddessContractActions = append(gGoddessContractActions, (*GoddessContarct).C2LGoddessUnlock)
	gGoddessContractActions = append(gGoddessContractActions, (*GoddessContarct).C2LGoddessChapterFight)
}

type GoddessContarct struct {
	*UserLoad
	*cl.GoddessContractInfo
	unlockGoddess    []*goxml.GoddessContractInfo
	touchCount       uint32
	feedItem         []uint32
	lockGoddess      []*goxml.GoddessContractInfo
	fightDungeonInfo map[uint32][]*goxml.GoddessTalesDungeonInfoExt
}

func NewGoddessContract(r *Robot) *GoddessContarct {
	g := &GoddessContarct{
		UserLoad: NewUserLoad(r),
	}
	return g
}

// 断线重连重置数据
func (g *GoddessContarct) reset() {
	g.load.reset()

	//edit here
}

func (g *GoddessContarct) RandAction() {
	switch g.load.state() {
	case dataLoadNone:
		g.C2LInit()
	case dataLoading:
		g.C2LRetryInit()
	default:
		if length := len(gGoddessContractActions); length != 0 {
			gGoddessContractActions[g.u.rd.Intn(length)](g)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (g *GoddessContarct) C2LInit() {
	g.load.Lock()
	g.load.setState(dataLoading)

	//g.C2LGoddessContarctFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (g *GoddessContarct) L2CInitRet(ret uint32) {
	if g.load.state() == dataLoading {
		g.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			g.load.setState(dataLoaded)
		} else {
			g.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (g *GoddessContarct) C2LRetryInit() {
	if g.load.Lock() {
		g.C2LGoddessContractGetData()
	}
}

func (g *GoddessContarct) C2LGoddessContractGetData() {
	cmsg := &cl.C2L_GoddessContractGetData{}
	g.fightDungeonInfo = make(map[uint32][]*goxml.GoddessTalesDungeonInfoExt)
	for _, info := range goxml.GetData().GoddessTalesDungeonInfoM.Datas {
		if info.MonsterGroup > 0 {
			_, exist := g.fightDungeonInfo[info.TalesId]
			if !exist {
				g.fightDungeonInfo[info.TalesId] = make([]*goxml.GoddessTalesDungeonInfoExt, 0, 3)
			}
			g.fightDungeonInfo[info.TalesId] = append(g.fightDungeonInfo[info.TalesId], info)
		}
	}
	g.u.SendCmd(uint32(cl.ID_MSG_C2L_GoddessContractGetData), cmsg)
}

func (g *GoddessContarct) L2CGoddessContractGetData(recv *cl.L2C_GoddessContractGetData) {
	l4g.Debug("[GoddessContarct] %s L2CGoddessContractGetData ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	datas := goxml.GetData().GoddessContractInfoM.Datas
	g.GoddessContractInfo = recv.GoddessContract
	g.unlockGoddess = make([]*goxml.GoddessContractInfo, 0, len(datas))
	g.lockGoddess = make([]*goxml.GoddessContractInfo, 0, len(datas))
	for goddessID, info := range datas {
		_, exist := g.GoddessContractInfo.Goddess[goddessID]
		if exist {
			g.unlockGoddess = append(g.unlockGoddess, info)
		} else {
			g.lockGoddess = append(g.unlockGoddess, info)
		}
	}
	g.touchCount = recv.TouchCount
}

func (g *GoddessContarct) C2LGoddessFeed() {
	cmsg := &cl.C2L_GoddessFeed{}

	if !g.randomGoddessGiftAndCount(cmsg) {
		l4g.Errorf("uuid:%s C2LGoddessFeed rand params failed", g.u.UUID())
		return
	}

	g.u.SendCmd(uint32(cl.ID_MSG_C2L_GoddessFeed), cmsg)
}

func (g *GoddessContarct) L2CGoddessFeed(recv *cl.L2C_GoddessFeed) {
	l4g.Debug("[GoddessContarct] %s L2CGoddessFeed ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	g.Goddess[recv.GoddessId] = recv.Goddess
}

func (g *GoddessContarct) randomGoddessGiftAndCount(cmsg *cl.C2L_GoddessFeed) bool {
	index := g.u.rd.RandBetween(0, len(g.unlockGoddess)-1)
	goddessId := g.unlockGoddess[index]
	cmsg.GoddessId = goddessId.GoddessId

	index = g.u.rd.RandBetween(0, len(g.feedItem)-1)
	itemId := g.feedItem[index]
	cmsg.ItemId = itemId
	if g.u.itemsBag == nil {
		return false
	}

	count := g.u.rd.RandBetween(1, int(goxml.GetData().ConfigInfoM.GetGiftsNum()))
	cmsg.ItemCount = uint32(count)

	if g.u.itemsBag[cmsg.ItemId] < uint32(count) {
		return false
	}

	return true
}

func (g *GoddessContarct) C2LGoddessTouch() {
	cmsg := &cl.C2L_GoddessTouch{}

	if g.touchCount >= goxml.GetData().ConfigInfoM.FreeGoddessTouchCount() {
		g.RandAction()
	}

	g.u.SendCmd(uint32(cl.ID_MSG_C2L_GoddessTouch), cmsg)
}

func (g *GoddessContarct) L2CGoddessTouch(recv *cl.L2C_GoddessTouch) {
	l4g.Debug("[GoddessContarct] %s L2CGoddessTouch ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	g.touchCount = recv.TouchCount
	g.Goddess[recv.GoddessId] = recv.Goddess
}

func (g *GoddessContarct) C2LGoddessStoryAward() {
	cmsg := &cl.C2L_GoddessStoryAward{}

	eliteInfo := g.randomGoddessStoryParams()
	if eliteInfo == nil {
		g.RandAction()
	}
	cmsg.StoryId = eliteInfo.EliteId

	g.u.SendCmd(uint32(cl.ID_MSG_C2L_GoddessStoryAward), cmsg)
}

func (g *GoddessContarct) randomGoddessStoryParams() *goxml.GoddessContractEliteInfo {
	index := g.u.rd.RandBetween(0, len(g.unlockGoddess)-1)
	eliteInfos := goxml.GetData().GoddessContractEliteInfoM.GoddessElite(g.unlockGoddess[index].GoddessId)

	for _, eliteInfo := range eliteInfos {
		if eliteInfo == nil {
			continue
		}
		goddess, exist := g.Goddess[eliteInfo.Goddess]
		if !exist {
			return nil
		}
		if isStoryAwarded(goddess.GoddessStory, eliteInfo.EliteId) {
			continue
		}
		if goddess.Level >= eliteInfo.UnlockLevel {
			return eliteInfo
		}
	}
	return nil
}

func isStoryAwarded(finish []uint32, story uint32) bool {
	for _, id := range finish {
		if id == story {
			return true
		}
	}
	return false
}

func (g *GoddessContarct) L2CGoddessStoryAward(recv *cl.L2C_GoddessStoryAward) {
	l4g.Debug("[GoddessContarct] %s L2CGoddessStoryAward ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	g.Goddess[recv.Goddess.Id] = recv.Goddess
}

func (g *GoddessContarct) C2LGoddessUnlock() {
	cmsg := &cl.C2L_GoddessUnlock{}

	dungeonID := g.u.dungeon.currentID
	for _, goddessInfo := range g.unlockGoddess {
		if dungeonID >= goddessInfo.UnlockDungeon {
			cmsg.GoddessId = goddessInfo.GoddessId
			break
		}
	}

	g.u.SendCmd(uint32(cl.ID_MSG_C2L_GoddessUnlock), cmsg)
}

func (g *GoddessContarct) L2CGoddessUnlock(recv *cl.L2C_GoddessUnlock) {
	l4g.Debug("[GoddessContarct] %s L2CGoddessUnlock ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	g.Goddess[recv.GoddessId] = recv.Goddess
	g.unlockGoddess = append(g.unlockGoddess, goxml.GetData().GoddessContractInfoM.Index(recv.GoddessId))

	lens := len(g.lockGoddess)
	for i := 0; i < lens; i++ {
		if g.lockGoddess[i].GoddessId == recv.GoddessId {
			g.lockGoddess = append(g.lockGoddess[:i], g.lockGoddess[i+1:]...)
			lens--
			i--
		}
	}
}

func (g *GoddessContarct) C2LGoddessChapterFight() {
	cmsg := &cl.C2L_GoddessChapterFight{}
	//edit here
	info := g.randomGoddessChapterFight()
	if info == nil {
		g.RandAction()
	}
	cmsg.Id = info.Id
	cmsg.Assists = append(cmsg.Assists, info.MustAssistHeros...)
	cmsg.Assists = append(cmsg.Assists, info.AssistHeros...)
	if len(cmsg.Assists) > 5 {
		cmsg.Assists = cmsg.Assists[:5]
	}
	cmsg.ClientData = []byte{}

	g.u.SendCmd(uint32(cl.ID_MSG_C2L_GoddessChapterFight), cmsg)
}

func (g *GoddessContarct) L2CGoddessChapterFight(recv *cl.L2C_GoddessChapterFight) {
	l4g.Debug("[GoddessContarct] %s L2CGoddessChapterFight ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (g *GoddessContarct) randomGoddessChapterFight() *goxml.GoddessTalesDungeonInfoExt {
	index := g.u.rd.RandBetween(0, len(g.unlockGoddess)-1)
	goddessInfo := g.unlockGoddess[index]

	length := len(g.fightDungeonInfo[goddessInfo.GoddessId])
	index = g.u.rd.RandBetween(0, length-1)
	return g.fightDungeonInfo[goddessInfo.GoddessId][index]
}
