package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/golang/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitRobotTowerSeasonCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TowerSeasonGetData), L2CTowerSeasonGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TowerSeasonFight), L2CTowerSeasonFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TowerSeasonRecvAward), L2CTowerSeasonRecvAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TowerSeasonRankList), L2CTowerSeasonRankListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TowerSeasonRankLike), L2CTowerSeasonRankLikeCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TowerSeasonUpdateTask), L2CTowerSeasonUpdateTaskCommand{})
}

type L2CTowerSeasonGetDataCommand struct {
}

func (c L2CTowerSeasonGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TowerSeasonGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTowerSeasonGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.towerSeason.L2CTowerSeasonGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CTowerSeasonFightCommand struct {
}

func (c L2CTowerSeasonFightCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TowerSeasonFight{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTowerSeasonFightCommand unmarshal error: %s", err)
		return false
	}
	robot.towerSeason.L2CTowerSeasonFight(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CTowerSeasonRecvAwardCommand struct {
}

func (c L2CTowerSeasonRecvAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TowerSeasonRecvAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTowerSeasonRecvAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.towerSeason.L2CTowerSeasonRecvAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CTowerSeasonRankListCommand struct {
}

func (c L2CTowerSeasonRankListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TowerSeasonRankList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTowerSeasonRankListCommand unmarshal error: %s", err)
		return false
	}
	robot.towerSeason.L2CTowerSeasonRankList(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CTowerSeasonRankLikeCommand struct {
}

func (c L2CTowerSeasonRankLikeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TowerSeasonRankLike{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTowerSeasonRankLikeCommand unmarshal error: %s", err)
		return false
	}
	robot.towerSeason.L2CTowerSeasonRankLike(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CTowerSeasonUpdateTaskCommand struct {
}

func (c L2CTowerSeasonUpdateTaskCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TowerSeasonUpdateTask{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTowerSeasonUpdateTaskCommand unmarshal error: %s", err)
		return false
	}
	robot.towerSeason.L2CTowerSeasonUpdateTask(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
