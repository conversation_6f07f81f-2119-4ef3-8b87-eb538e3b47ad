package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGuildchestCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildChestGetData), L2CGuildChestGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildChestRecv), L2CGuildChestRecvCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildChestLike), L2CGuildChestLikeCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildChestActivate), L2CGuildChestActivateCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_UserGuildChestItemNotify), L2CUserGuildChestItemNotifyCommand{})
}

type L2CGuildChestGetDataCommand struct {
}

func (c L2CGuildChestGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildChestGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildChestGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.guildchest.L2CGuildChestGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildChestRecvCommand struct {
}

func (c L2CGuildChestRecvCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildChestRecv{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildChestRecvCommand unmarshal error: %s", err)
		return false
	}
	robot.guildchest.L2CGuildChestRecv(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildChestLikeCommand struct {
}

func (c L2CGuildChestLikeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildChestLike{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildChestLikeCommand unmarshal error: %s", err)
		return false
	}
	robot.guildchest.L2CGuildChestLike(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildChestActivateCommand struct {
}

func (c L2CGuildChestActivateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildChestActivate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildChestActivateCommand unmarshal error: %s", err)
		return false
	}
	robot.guildchest.L2CGuildChestActivate(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CUserGuildChestItemNotifyCommand struct {
}

func (c L2CUserGuildChestItemNotifyCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_UserGuildChestItemNotify{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CUserGuildChestItemNotifyCommand unmarshal error: %s", err)
		return false
	}
	robot.guildchest.L2CUserGuildChestItemNotify(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
