package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type DispatchFunc func(*Dispatch)

var gDispatchActions []DispatchFunc

func InitDispatchActions() {
	gDispatchActions = append(gDispatchActions, (*Dispatch).C2LDispatchReceiveTask)
	gDispatchActions = append(gDispatchActions, (*Dispatch).C2LDispatchGetAwards)
	gDispatchActions = append(gDispatchActions, (*Dispatch).C2LDispatchRefreshTask)
}

type Dispatch struct {
	*UserLoad
	tasks          []uint64
	cacheHeroes    []*cl.Hero
	receiveTasks   []uint64
	dispatchHeroID map[uint64]uint64 // taskID -> heroID
}

func NewDispatch(r *Robot) *Dispatch {
	d := &Dispatch{
		UserLoad:       NewUserLoad(r),
		dispatchHeroID: make(map[uint64]uint64),
	}
	return d
}

func (d *Dispatch) newResource(typ, value, count uint32) *cl.Resource {
	return &cl.Resource{
		Type:  typ,
		Value: value,
		Count: count,
	}
}

// 断线重连重置数据
func (d *Dispatch) reset() {
	d.load.reset()

	//edit here
}

func (d *Dispatch) RandAction() {
	switch d.load.state() {
	case dataLoadNone:
		d.C2LInit()
	case dataLoading:
		d.C2LRetryInit()
	default:
		if length := len(gDispatchActions); length != 0 {
			gDispatchActions[d.u.rd.Intn(length)](d)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (d *Dispatch) C2LInit() {
	d.load.Lock()
	d.load.setState(dataLoading)
	//awards := make([]*cl.Resource, 0, 17)
	//awards = append(awards, d.newResource(9, 9011, 20000))
	//awards = append(awards, d.newResource(2, 0, 99999999))
	//if !d.u.CheckHero() {
	//	awards = append(awards, d.u.GetRandHero(15)...)
	//}
	//d.u.GM(awards) // 发放压测所需资源
	d.C2LDispatchTasks()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (d *Dispatch) L2CInitRet(ret uint32) {
	if d.load.state() == dataLoading {
		d.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			d.load.setState(dataLoaded)
		} else {
			d.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (d *Dispatch) C2LRetryInit() {
	if d.load.Lock() {
		if !d.u.CheckHero() {
			return
		}
		d.C2LDispatchTasks() // 获取悬赏任务列表
	}
}

func (d *Dispatch) C2LDispatchTasks() {
	cmsg := &cl.C2L_DispatchTasks{}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_DispatchTasks), cmsg)
}

func (d *Dispatch) L2CDispatchTasks(recv *cl.L2C_DispatchTasks) {
	l4g.Debug("[Dispatch] %s L2CDispatchTasks ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	d.L2CInitRet(recv.Ret)
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	for _, task := range recv.Tasks {
		d.tasks = append(d.tasks, task.Id)
	}
}

func (d *Dispatch) C2LDispatchReceiveTask() {
	cmsg := &cl.C2L_DispatchReceiveTask{}

	if len(d.tasks) > 0 {
		index := d.u.rd.RandBetween(0, len(d.tasks)-1)
		cmsg.Id = d.tasks[index]
		if len(d.u.heroes) > 0 {
			i := d.u.rd.RandBetween(0, len(d.u.heroes)-1)
			cmsg.HeroIds = []uint64{d.u.heroes[i].Data.Id}
			d.cacheHeroes = append(d.cacheHeroes, d.u.heroes[i])
		}
		d.u.SendCmd(uint32(cl.ID_MSG_C2L_DispatchReceiveTask), cmsg)
	}
}

func (d *Dispatch) L2CDispatchReceiveTask(recv *cl.L2C_DispatchReceiveTask) {
	l4g.Debug("[Dispatch] %s L2CDispatchReceiveTask ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	d.receiveTasks = append(d.receiveTasks, recv.Id)
	for k, id := range d.tasks {
		if id == recv.Id {
			d.tasks = append(d.tasks[:k], d.tasks[k+1:]...)
			break
		}
	}

	if len(recv.HeroIds) > 0 {
		d.dispatchHeroID[recv.Id] = recv.HeroIds[0]

		for k, hero := range d.u.heroes {
			if hero.Data.Id == recv.HeroIds[0] {
				d.u.heroes = append(d.u.heroes[:k], d.u.heroes[k+1:]...)
				break
			}
		}
	}
}

func (d *Dispatch) C2LDispatchGetAwards() {
	cmsg := &cl.C2L_DispatchGetAwards{}
	if len(d.receiveTasks) > 0 {
		cmsg.Id = []uint64{d.receiveTasks[0]}
		d.receiveTasks = append(d.receiveTasks[:0], d.receiveTasks[1:]...)
		cmsg.IsSpeed = true
		d.u.SendCmd(uint32(cl.ID_MSG_C2L_DispatchGetAwards), cmsg)
	}
}

func (d *Dispatch) L2CDispatchGetAwards(recv *cl.L2C_DispatchGetAwards) {
	l4g.Debug("[Dispatch] %s L2CDispatchGetAwards ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	var taskID uint64
	if len(recv.Id) > 0 {
		taskID = recv.Id[0]
	}

	for k, hero := range d.cacheHeroes {
		if hero.Data.Id == d.dispatchHeroID[taskID] {
			d.u.heroes = append(d.u.heroes, hero)
			d.cacheHeroes = append(d.cacheHeroes[:k], d.cacheHeroes[k+1:]...)
			break
		}
	}
}

func (d *Dispatch) C2LDispatchRefreshTask() {
	cmsg := &cl.C2L_DispatchRefreshTask{}

	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_DispatchRefreshTask), cmsg)
}

func (d *Dispatch) L2CDispatchRefreshTask(recv *cl.L2C_DispatchRefreshTask) {
	l4g.Debug("[Dispatch] %s L2CDispatchRefreshTask ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	d.tasks = d.tasks[:0]
	for _, task := range recv.Tasks {
		d.tasks = append(d.tasks, task.Id)
	}
}
