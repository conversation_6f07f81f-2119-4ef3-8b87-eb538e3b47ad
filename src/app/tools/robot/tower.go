package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type TowerFunc func(*Tower)

var gTowerActions []TowerFunc

func InitTowerActions() {
	//gTowerActions = append(gTowerActions, (*Tower).C2LTowerList)
	gTowerActions = append(gTowerActions, (*Tower).C2LTowerFight)
	// gTowerActions = append(gTowerActions, (*Tower).C2LTowerSweep)
	// gTowerActions = append(gTowerActions, (*Tower).C2LTowerJump)
}

type Tower struct {
	*UserLoad
	type2floor map[uint32]uint32 //塔类型 => 当前层
}

func (t *Tower) setData(data []*cl.Tower) {
	if len(data) > 0 {
		t.type2floor = make(map[uint32]uint32, 1)
		for _, v := range data {
			t.type2floor[v.Type] = v.Floor
		}
	}
}

func (t *Tower) getFloor(typ uint32) uint32 {
	return t.type2floor[typ]
}

func (t *Tower) upFloor(typ uint32) {
	t.type2floor[typ]++
}

func (t *Tower) setFloor(typ, floor uint32) {
	t.type2floor[typ] = floor
}

func (t *Tower) randomOneType() uint32 {
	//目前仅有一种塔类型，直接返回
	return goxml.TowerTypeMain

	//TODO 增加其他类型后，再替换为以下逻辑
	/*
		if len(t.towerType) == 0 {
			return goxml.TowerTypeMain
		}
		k := t.u.Rand().RandBetween(0, len(t.towerType)-1)
		return t.towerType[k]
	*/
}

func (t *Tower) randomJumpFloor(typ uint32) uint32 {
	floor := t.getFloor(typ)
	return uint32(t.u.rd.RandBetween(int(floor), int(common.TOWER_JUMP_TJ_COUNT_LIMIT)))
}

func NewTower(r *Robot) *Tower {
	return &Tower{
		UserLoad: NewUserLoad(r),
	}
}

// 断线重连重置数据
func (t *Tower) reset() {
	t.load.reset()

	//edit here
}

func (t *Tower) RandAction() {
	switch t.load.state() {
	case dataLoadNone:
		t.C2LInit()
	case dataLoading:
		t.C2LRetryInit()
	default:
		if length := len(gTowerActions); length != 0 {
			gTowerActions[t.u.rd.Intn(length)](t)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (t *Tower) C2LInit() {
	t.load.Lock()
	t.load.setState(dataLoading)
	t.C2LTowerList()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (t *Tower) L2CInitRet(ret uint32) {
	if t.load.state() == dataLoading {
		t.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			t.load.setState(dataLoaded)
		} else {
			t.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (t *Tower) C2LRetryInit() {
	if t.load.Lock() {
		////检查设置爬塔阵容
		if !t.u.CheckFormation(40) {
			t.load.UnLock()
			return
		}
		t.C2LTowerList()
	}
}

func (t *Tower) C2LTowerList() {
	cmsg := &cl.C2L_TowerList{}
	//edit here
	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerList), cmsg)
}

func (t *Tower) L2CTowerList(recv *cl.L2C_TowerList) {
	l4g.Debug("[Tower] %s L2CTowerList ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	t.setData(recv.Towers)
	t.L2CInitRet(recv.Ret)
}

func (t *Tower) C2LTowerFight() {
	cmsg := &cl.C2L_TowerFight{}
	//edit here

	cmsg.Type = t.randomOneType()
	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerFight), cmsg)
}

func (t *Tower) L2CTowerFight(recv *cl.L2C_TowerFight) {
	l4g.Debug("[Tower] %s L2CTowerFight ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	t.upFloor(recv.Type)
}

func (t *Tower) C2LTowerSweep() {
	cmsg := &cl.C2L_TowerSweep{}
	//edit here
	cmsg.Type = t.randomOneType()
	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerSweep), cmsg)
}

func (t *Tower) L2CTowerSweep(recv *cl.L2C_TowerSweep) {
	l4g.Debug("[Tower] %s L2CTowerSweep ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (t *Tower) C2LTowerJump() {
	cmsg := &cl.C2L_TowerJump{}
	//edit here

	cmsg.Type = t.randomOneType()
	cmsg.Floor = t.randomJumpFloor(cmsg.Type)
	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerJump), cmsg)
}

func (t *Tower) L2CTowerJump(recv *cl.L2C_TowerJump) {
	l4g.Debug("[Tower] %s L2CTowerJump ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	t.setFloor(recv.Type, recv.Floor)
}
