package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotSeasonarenaCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaGetData), L2CSeasonArenaGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaRefresh), L2CSeasonArenaRefreshCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaFight), L2CSeasonArenaFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaLogList), L2CSeasonArenaLogListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaOfFame), L2CSeasonArenaOfFameCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaDivisionAward), L2CSeasonArenaDivisionAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaTaskAward), L2CSeasonArenaTaskAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaGetRankList), L2CSeasonArenaGetRankListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaState), L2CSeasonArenaStateCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaTaskUpdate), L2CSeasonArenaTaskUpdateCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonArenaBuyChallengeCount), L2CSeasonArenaBuyChallengeCountCommand{})
}

type L2CSeasonArenaGetDataCommand struct {
}

func (c L2CSeasonArenaGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaRefreshCommand struct {
}

func (c L2CSeasonArenaRefreshCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaRefresh{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaRefreshCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaRefresh(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaFightCommand struct {
}

func (c L2CSeasonArenaFightCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaFight{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaFightCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaFight(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaLogListCommand struct {
}

func (c L2CSeasonArenaLogListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaLogList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaLogListCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaLogList(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaOfFameCommand struct {
}

func (c L2CSeasonArenaOfFameCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaOfFame{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaOfFameCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaOfFame(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaDivisionAwardCommand struct {
}

func (c L2CSeasonArenaDivisionAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaDivisionAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaDivisionAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaDivisionAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaTaskAwardCommand struct {
}

func (c L2CSeasonArenaTaskAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaTaskAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaTaskAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaTaskAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaGetRankListCommand struct {
}

func (c L2CSeasonArenaGetRankListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaGetRankList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaGetRankListCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaGetRankList(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaStateCommand struct {
}

func (c L2CSeasonArenaStateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaState{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaStateCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaState(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaTaskUpdateCommand struct {
}

func (c L2CSeasonArenaTaskUpdateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaTaskUpdate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaTaskUpdateCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaTaskUpdate(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonArenaBuyChallengeCountCommand struct {
}

func (c L2CSeasonArenaBuyChallengeCountCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonArenaBuyChallengeCount{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonArenaBuyChallengeCountCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonarena.L2CSeasonArenaBuyChallengeCount(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
