package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type CrystalFunc func(*Crystal)

var gCrystalActions []CrystalFunc

func InitCrystalActions() {
	gCrystalActions = append(gCrystalActions, (*Crystal).C2LCrystalAddHero)
	gCrystalActions = append(gCrystalActions, (*Crystal).C2LCrystalRemoveHero)
	gCrystalActions = append(gCrystalActions, (*Crystal).C2LCrystalUnlockSlot)
	gCrystalActions = append(gCrystalActions, (*Crystal).C2LCrystalSpeedSlotCD)
	// gCrystalActions = append(gCrystalActions, (*Crystal).C2LCrystalGetAllData)
	gCrystalActions = append(gCrystalActions, (*Crystal).C2LCrystalActiveAchievement)
}

const initExistSysIDsLen = 15
const initTaskIDsLen = 50
const initSlotCount = 1

type Crystal struct {
	*UserLoad
	Data         *cl.Crystal
	Achievements *cl.CrystalBlessingAchieve
	existSysIDs  []uint32               //缔约与共鸣英雄系统id列表
	taskCfg      map[uint32][]*taskGoal //水晶成就配置列表
}

type taskGoal struct {
	id   uint32
	goal uint32
}

func NewCrystal(r *Robot) *Crystal {
	c := &Crystal{
		UserLoad:    NewUserLoad(r),
		Data:        &cl.Crystal{},
		existSysIDs: make([]uint32, 0, initExistSysIDsLen),
		taskCfg:     make(map[uint32][]*taskGoal, initTaskIDsLen),
	}
	c.checkInitAchievements()
	return c
}

func (c *Crystal) initCrystal(retData *cl.L2C_CrystalGetAllData) {
	c.Data = retData.Data
	c.Achievements = retData.Data.Achieve
	c.checkInitAchievements()
}

func (c *Crystal) checkInitAchievements() {
	if c.Achievements == nil {
		c.Achievements = &cl.CrystalBlessingAchieve{}
	}
	if c.Achievements.TaskProgress == nil {
		c.Achievements.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if c.Achievements.ReceiveAwarded == nil {
		c.Achievements.ReceiveAwarded = make(map[uint32]bool)
	}
}

func (c *Crystal) updateExistSysIDs() {
	hids := make([]uint64, 0, initExistSysIDsLen)
	hids = append(hids, c.Data.ContractHeroes...)
	for _, slot := range c.Data.Slot {
		if slot.Hid > 0 {
			hids = append(hids, slot.Hid)
		}
	}

	c.existSysIDs = c.getHeroSysIDsByHids(hids)
}

func (c *Crystal) getHeroSysIDsByHids(hids []uint64) []uint32 {
	result := make([]uint32, 0, len(hids))
	for _, hid := range hids {
		heroData := c.u.hero.GetHeroDataByID(hid)
		if heroData != nil {
			result = append(result, heroData.SysId)
		}
	}
	return result
}

func (c *Crystal) slotAddHero(slotID uint32, heroID uint64) {
	c.Data.Slot[slotID] = &cl.CrystalSlot{
		Hid:       heroID,
		CdEndTime: 0,
		Id:        slotID,
	}

	heroData := c.u.hero.GetHeroDataByID(heroID)
	if heroData != nil {
		if heroID > 0 {
			c.existSysIDs = append(c.existSysIDs, heroData.SysId)
		}
	}
}

func (c *Crystal) slotRemoveHero(slotID uint32) {
	if c.Data.Slot[slotID] == nil {
		l4g.Errorf("[Crystal] slot data err, slotID:%d", slotID)
		return
	}
	oldHero := c.Data.Slot[slotID]

	c.Data.Slot[slotID] = &cl.CrystalSlot{
		Hid:       0,
		CdEndTime: time.Now().Unix() + goxml.GetData().CrystalConfigInfoM.SlotCD,
		Id:        slotID,
	}

	heroData := c.u.hero.GetHeroDataByID(oldHero.Hid)
	if heroData != nil {
		for k, sid := range c.existSysIDs {
			if sid == heroData.SysId {
				c.existSysIDs = append(c.existSysIDs[:k], c.existSysIDs[k+1:]...)
				break
			}
		}
	}
}

func (c *Crystal) cleanSlotCD(slotID uint32) {
	for _, slot := range c.Data.Slot {
		if slot.Id == slotID {
			slot.CdEndTime = 0
			break
		}
	}
}

func (c *Crystal) addPurchasedCount() {
	c.Data.PurchasedSlotCount++
}

func (c *Crystal) updateContractHeroes(heroes []uint64) {
	c.Data.ContractHeroes = heroes
}

func (c *Crystal) getOneHeroIDForAddSlot() uint64 {
	return c.u.hero.GetOneHeroIDNotInListByRare(c.existSysIDs, goxml.GetData().CrystalConfigInfoM.RequireRare)
}

func (c *Crystal) getOneSlotIDForAddSlot() uint32 {
	now := time.Now().Unix()
	for _, slot := range c.Data.Slot {
		if slot.Hid == 0 && now >= slot.CdEndTime {
			return slot.Id
		}
	}
	return 0
}

// 去掉了成就完成的验证逻辑
// 默认全部完成
// 正常情况下，应改用getOneFinishTaskID()
func (c *Crystal) getOneFinishTaskIDForEasyTest() uint32 {
	return 0
}

func (c *Crystal) isActived(id uint32) bool {
	actived, exist := c.Achievements.ReceiveAwarded[id]
	if !exist {
		return false
	}
	return actived
}

func (c *Crystal) updateAchievementActive(ids []uint32) {
	for _, id := range ids {
		c.Achievements.ReceiveAwarded[id] = true
	}
}

func (c *Crystal) updateAchievementProcess(data map[uint32]*cl.TaskTypeProgress) {
	for id, process := range data {
		c.Achievements.TaskProgress[id] = process
	}
}

// 断线重连重置数据
func (c *Crystal) reset() {
	c.load.reset()

	//edit here
}

func (c *Crystal) RandAction() {
	switch c.load.state() {
	case dataLoadNone:
		c.C2LInit()
	case dataLoading:
		c.C2LRetryInit()
	default:
		if length := len(gCrystalActions); length != 0 {
			gCrystalActions[c.u.rd.Intn(length)](c)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (c *Crystal) C2LInit() {
	c.load.Lock()
	c.load.setState(dataLoading)

	c.C2LCrystalGetAllData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (c *Crystal) L2CInitRet(ret uint32) {
	if c.load.state() == dataLoading {
		c.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			c.load.setState(dataLoaded)
		} else {
			c.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (c *Crystal) C2LRetryInit() {
	// if c.load.Lock() {
	//c.C2LCrystalFlushInfo()
	// }
}

func (c *Crystal) C2LCrystalAddHero() {
	cmsg := &cl.C2L_CrystalAddHero{}
	//edit here
	cmsg.Hid = c.getOneHeroIDForAddSlot()
	cmsg.SlotId = c.getOneSlotIDForAddSlot()
	if cmsg.Hid > 0 && cmsg.SlotId > 0 {
		c.u.SendCmd(uint32(cl.ID_MSG_C2L_CrystalAddHero), cmsg)
	} else {
		l4g.Infof("[Crystal] %s L2CCrystalAddHero: getOneHeroForAddSlot err, hid:%d, slotID:%d",
			c.u.UUID(), cmsg.Hid, cmsg.SlotId)
	}
}

func (c *Crystal) L2CCrystalAddHero(recv *cl.L2C_CrystalAddHero) {
	l4g.Debug("[Crystal] %s L2CCrystalAddHero ret:%s, recv:%+v",
		c.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	c.slotAddHero(recv.SlotId, recv.Hid)
}

func (c *Crystal) C2LCrystalRemoveHero() {
	cmsg := &cl.C2L_CrystalRemoveHero{}
	//edit here
	for _, slot := range c.Data.Slot {
		if slot.Hid > 0 {
			cmsg.SlotId = slot.Id
			break
		}
	}

	if cmsg.SlotId > 0 {
		c.u.SendCmd(uint32(cl.ID_MSG_C2L_CrystalRemoveHero), cmsg)
	}
}

func (c *Crystal) L2CCrystalRemoveHero(recv *cl.L2C_CrystalRemoveHero) {
	l4g.Debug("[Crystal] %s L2CCrystalRemoveHero ret:%s, recv:%+v",
		c.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	c.slotRemoveHero(recv.SlotId)
}

func (c *Crystal) C2LCrystalUnlockSlot() {
	cmsg := &cl.C2L_CrystalUnlockSlot{}
	//edit here
	if c.Data.PurchasedSlotCount+initSlotCount < goxml.GetData().CrystalConfigInfoM.SlotMax {
		c.u.SendCmd(uint32(cl.ID_MSG_C2L_CrystalUnlockSlot), cmsg)
	}
}

func (c *Crystal) L2CCrystalUnlockSlot(recv *cl.L2C_CrystalUnlockSlot) {
	l4g.Debug("[Crystal] %s L2CCrystalUnlockSlot ret:%s, recv:%+v",
		c.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	c.addPurchasedCount()
}

func (c *Crystal) C2LCrystalSpeedSlotCD() {
	cmsg := &cl.C2L_CrystalSpeedSlotCD{}
	//edit here
	now := time.Now().Unix()
	for _, slot := range c.Data.Slot {
		if slot.CdEndTime > 0 && slot.CdEndTime > now {
			cmsg.SlotId = slot.Id
			break
		}
	}

	if cmsg.SlotId > 0 {
		c.u.SendCmd(uint32(cl.ID_MSG_C2L_CrystalSpeedSlotCD), cmsg)
	}
}

func (c *Crystal) L2CCrystalSpeedSlotCD(recv *cl.L2C_CrystalSpeedSlotCD) {
	l4g.Debug("[Crystal] %s L2CCrystalSpeedSlotCD ret:%s, recv:%+v",
		c.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	c.cleanSlotCD(recv.SlotId)
}

func (c *Crystal) C2LCrystalGetAllData() {
	cmsg := &cl.C2L_CrystalGetAllData{}
	//edit here
	c.u.SendCmd(uint32(cl.ID_MSG_C2L_CrystalGetAllData), cmsg)
}

func (c *Crystal) L2CCrystalGetAllData(recv *cl.L2C_CrystalGetAllData) {
	l4g.Debug("[Crystal] %s L2CCrystalGetAllData ret:%s, recv:%+v",
		c.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	c.initCrystal(recv)
	c.L2CInitRet(recv.Ret)
}

func (c *Crystal) L2CCrystalGetShareAttr(recv *cl.L2C_CrystalGetShareAttr) {
	l4g.Debug("[Crystal] %s L2CCrystalGetShareAttr recv:%+v", recv)

	//edit here
}

func (c *Crystal) L2CCrystalHeroesUpdate(recv *cl.L2C_CrystalHeroesUpdate) {
	l4g.Debug("[Crystal] %s L2CCrystalHeroesUpdate recv:%+v", c.u.UUID(), recv)

	//edit here
	if len(recv.ContractHeroes) > 0 {
		c.updateContractHeroes(recv.ContractHeroes)
	}

	if len(recv.Slot) > 0 {
		for slotID := range recv.Slot {
			c.slotRemoveHero(slotID)
		}
	}

	c.updateExistSysIDs()
}

func (c *Crystal) C2LCrystalActiveAchievement() {
	cmsg := &cl.C2L_CrystalActiveAchievement{}
	//edit here
	id := c.getOneFinishTaskIDForEasyTest()
	// id := c.getOneFinishTaskID() //TODO 正常逻辑，应该使用此逻辑。getOneFinishTaskIDForEasyTest()是为了方便测试
	if id > 0 {
		cmsg.Ids = append(cmsg.Ids, id)
		c.u.SendCmd(uint32(cl.ID_MSG_C2L_CrystalActiveAchievement), cmsg)
	}
}

func (c *Crystal) L2CCrystalActiveAchievement(recv *cl.L2C_CrystalActiveAchievement) {
	l4g.Debug("[Crystal] %s L2CCrystalActiveAchievement ret:%s, recv:%+v",
		c.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	c.updateAchievementActive(recv.Ids)
}

func (c *Crystal) L2CCrystalUpdateAchievement(recv *cl.L2C_CrystalUpdateAchievement) {
	l4g.Debug("[Crystal] %s L2CCrystalUpdateAchievement ret:%s, recv:%+v",
		c.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	c.updateAchievementProcess(recv.TaskProgress)
}
