package main

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	l4g "github.com/ivanabc/log4go"
)

/*
开始下载目标的profile文件
*/

func PullProfile(addr string) {
	go cpuProfile(addr)
	go memProfile(addr)
	go goroutineProfile(addr)
}

func cpuProfile(addr string) {
	sleepTime := *flagTimer / 10
	time.Sleep(time.Duration(sleepTime) * time.Second)
	l4g.Info("Begin StartCPUProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/profile?seconds=%d", addr, *flagTimer-2*sleepTime)
	dateStr := time.Now().Format("20060102150405")
	target := fmt.Sprintf("./pprof/profile-%s", dateStr)
	resp, err := http.Get(addr)
	if err != nil {
		l4g.Error("StartCPUProfile error %s", err)
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		l4g.Error("StartAllocsProfile read body error %s", err)
		return
	}
	if err := ioutil.WriteFile(target, body, 0600); err != nil {
		l4g.Error("StartCPUProfile save file error %s", err)
		return
	}
	l4g.Info("StartCPUProfile End")
}

func memProfile(addr string) {
	time.Sleep(time.Duration(*flagTimer/2) * time.Second)
	heapProfile(addr)
	allocsProfile(addr)

}

func heapProfile(addr string) {
	l4g.Info("Begin StartHeapProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/heap", addr)
	dateStr := time.Now().Format("20060102150405")
	target := fmt.Sprintf("./pprof/heap-%s", dateStr)
	resp, err := http.Get(addr)
	if err != nil {
		l4g.Error("StartHeapProfile error %s", err)
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		l4g.Error("StartAllocsProfile read body error %s", err)
		return
	}
	if err := ioutil.WriteFile(target, body, 0600); err != nil {
		l4g.Error("StartHeapProfile save file error %s", err)
		return
	}
	l4g.Info("HeapCPUProfile End")
}

func allocsProfile(addr string) {
	l4g.Info("Begin StartAllocsProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/allocs", addr)
	dateStr := time.Now().Format("20060102150405")
	target := fmt.Sprintf("./pprof/allocs-%s", dateStr)
	resp, err := http.Get(addr)
	if err != nil {
		l4g.Error("StartAllocsProfile error %s", err)
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		l4g.Error("StartAllocsProfile read body error %s", err)
		return
	}
	if err := ioutil.WriteFile(target, body, 0600); err != nil {
		l4g.Error("StartAllocsProfile save file error %s", err)
		return
	}
	l4g.Info("HeapAllocsProfile End")
}

func goroutineProfile(addr string) {
	time.Sleep(time.Duration(*flagTimer/2) * time.Second)
	l4g.Info("Begin StartCPUProfile")
	addr = fmt.Sprintf("http://%s/debug/pprof/goroutine", addr)
	dateStr := time.Now().Format("20060102150405")
	target := fmt.Sprintf("./pprof/goroutine-%s", dateStr)
	resp, err := http.Get(addr)
	if err != nil {
		l4g.Error("StartGoroutineProfile error %s", err)
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		l4g.Error("StartGoroutineProfile read body error %s", err)
		return
	}
	if err := ioutil.WriteFile(target, body, 0600); err != nil {
		l4g.Error("StartGoroutineProfile save file error %s", err)
		return
	}
	l4g.Info("StartGoroutineProfile End")
}
