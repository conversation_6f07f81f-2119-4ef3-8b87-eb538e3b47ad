package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotBaseCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ViewUser), L2CViewUserCommand{})
	// gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GetUserHeroData), L2CGetUserHeroDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TestBattleData), L2CTestBattleDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GlobalAttrGet), L2CGlobalAttrGetCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SyncQuestionnaire), L2CSyncQuestionnaireCommand{})
}

type L2CViewUserCommand struct {
}

func (c L2CViewUserCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ViewUser{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CViewUserCommand unmarshal error: %s", err)
		return false
	}
	robot.base.L2CViewUser(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

// type L2CGetUserHeroDataCommand struct {
// }

// func (c L2CGetUserHeroDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
// 	recv := &cl.L2C_GetUserHeroData{}
// 	if err := proto.Unmarshal(data, recv); err != nil {
// 		l4g.Error("L2CGetUserHeroDataCommand unmarshal error: %s", err)
// 		return false
// 	}
// 	robot.base.L2CGetUserHeroData(recv)
// 	return recv.Ret == uint32(ret.RET_OK)
// }

type L2CTestBattleDataCommand struct {
}

func (c L2CTestBattleDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TestBattleData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTestBattleDataCommand unmarshal error: %s", err)
		return false
	}
	robot.base.L2CTestBattleData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGlobalAttrGetCommand struct {
}

func (c L2CGlobalAttrGetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GlobalAttrGet{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGlobalAttrGetCommand unmarshal error: %s", err)
		return false
	}
	robot.base.L2CGlobalAttrGet(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSyncQuestionnaireCommand struct {
}

func (c L2CSyncQuestionnaireCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SyncQuestionnaire{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSyncQuestionnaireCommand unmarshal error: %s", err)
		return false
	}
	robot.base.L2CSyncQuestionnaireCommand(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
