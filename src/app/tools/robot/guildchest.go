package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type GuildchestFunc func(*Guildchest)

var gGuildchestActions []GuildchestFunc

func InitGuildchestActions() {
	//gGuildchestActions = append(gGuildchestActions, (*Guildchest).C2LGuildChestGetData)
	gGuildchestActions = append(gGuildchestActions, (*Guildchest).C2LGuildChestRecv)
	gGuildchestActions = append(gGuildchestActions, (*Guildchest).C2LGuildChestLike)
	gGuildchestActions = append(gGuildchestActions, (*Guildchest).C2LGuildChestActivate)
}

type Guildchest struct {
	*UserLoad
	item                     map[uint64]*cl.UserGuildChestItem
	guildChest               map[uint64]*cl.GuildChestData
	userChestWeeklyRecvCount uint32
}

type clGuildChest cl.GuildChest

func (g *clGuildChest) convert2Data() *cl.GuildChestData {
	data := &cl.GuildChestData{
		Id:                 g.Id,
		ExpireTime:         g.ExpireTime,
		LikeCount:          g.RecvLike,
		Name:               g.Name,
		Level:              g.Level,
		AvatarId:           g.AvatarId,
		ChestId:            g.ChestId,
		Uid:                g.Uid,
		ThisChestRecvCount: uint32(len(g.RecvUsers)),
		CreateFlower:       g.CreateFlower,
		RecvChestMaxLimit:  g.RecvChestMaxLimit,
	}
	var detail *cl.GuildChestSlotDetail
	for _, v := range g.RecvUsers {
		if v == nil {
			continue
		}
		if v.Uid == g.Uid {
			detail = v
			break
		}
	}
	if detail != nil {
		data.LikeType = detail.LikeLevel
		data.Resource = detail.Resource
	}
	return data
}

func NewGuildchest(r *Robot) *Guildchest {
	g := &Guildchest{
		UserLoad:   NewUserLoad(r),
		item:       make(map[uint64]*cl.UserGuildChestItem),
		guildChest: make(map[uint64]*cl.GuildChestData),
	}
	return g
}

// 断线重连重置数据
func (g *Guildchest) reset() {
	g.load.reset()

	//edit here
}

func (g *Guildchest) RandAction() {
	switch g.load.state() {
	case dataLoadNone:
		g.C2LInit()
	case dataLoading:
		g.C2LRetryInit()
	default:
		if length := len(gGuildchestActions); length != 0 {
			gGuildchestActions[g.u.rd.Intn(length)](g)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (g *Guildchest) C2LInit() {
	g.load.Lock()
	g.load.setState(dataLoading)

	//g.C2LGuildchestFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	g.C2LGuildChestGetData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (g *Guildchest) L2CInitRet(ret uint32) {
	if g.load.state() == dataLoading {
		g.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			g.load.setState(dataLoaded)
		} else {
			g.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (g *Guildchest) C2LRetryInit() {
	if g.load.Lock() {
		g.C2LGuildChestGetData()
		g.load.setState(dataLoaded)
	}
}

func (g *Guildchest) C2LGuildChestGetData() {
	cmsg := &cl.C2L_GuildChestGetData{}
	//edit here
	if g.u.userdata.GuildId == 0 {
		g.randCommandNoGuild()
		return
	}

	g.u.SendCmd(uint32(cl.ID_MSG_C2L_GuildChestGetData), cmsg)
}

func (g *Guildchest) L2CGuildChestGetData(recv *cl.L2C_GuildChestGetData) {
	l4g.Debug("[Guildchest] %s L2CGuildChestGetData ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	g.L2CInitRet(recv.Ret)

	g.guildChest = make(map[uint64]*cl.GuildChestData)
	g.item = make(map[uint64]*cl.UserGuildChestItem)
	//edit here
	for _, v := range recv.GuildChests {
		g.guildChest[v.Id] = v.Clone()
	}
	for _, v := range recv.Items {
		g.item[v.Id] = v.Clone()
	}
	g.userChestWeeklyRecvCount = recv.UserChestWeeklyRecvCount
}

func (g *Guildchest) C2LGuildChestRecv() {
	cmsg := &cl.C2L_GuildChestRecv{}

	if g.u.userdata.GuildId == 0 {
		g.randCommandNoGuild()
		return
	}
	now := time.Now().Unix()
	if g.userChestWeeklyRecvCount > 0 {
		for id, v := range g.guildChest {
			if v == nil {
				delete(g.guildChest, id)
			}
			if v.ExpireTime <= now {
				delete(g.guildChest, id)
			}
			cmsg.Id = v.Id
			break
		}
		if cmsg.Id > 0 {
			g.u.SendCmd(uint32(cl.ID_MSG_C2L_GuildChestRecv), cmsg)
		}
	}
}

func (g *Guildchest) L2CGuildChestRecv(recv *cl.L2C_GuildChestRecv) {
	l4g.Debug("[Guildchest] %s L2CGuildChestRecv ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	g.guildChest[recv.GuildChest.Id] = (*clGuildChest)(recv.GuildChest).convert2Data()
	g.userChestWeeklyRecvCount = recv.UserChestWeeklyRecvCount
}

func (g *Guildchest) C2LGuildChestLike() {
	cmsg := &cl.C2L_GuildChestLike{}
	//edit here
	if g.u.userdata.GuildId == 0 {
		g.randCommandNoGuild()
		return
	}

	for id, v := range g.guildChest {
		if v == nil {
			delete(g.guildChest, id)
		}
		if v.Resource != nil && v.LikeType == 0 && uint32(g.u.tokensBag[9055]) >= uint32(goxml.GetData().GuildConfigInfoM.GetLikeTypeFlowerCount(1)) {
			cmsg.Id = v.Id
			cmsg.LikeType = 1
			break
		}
	}
	if cmsg.Id > 0 {
		g.u.SendCmd(uint32(cl.ID_MSG_C2L_GuildChestLike), cmsg)
	}
}

func (g *Guildchest) L2CGuildChestLike(recv *cl.L2C_GuildChestLike) {
	l4g.Debug("[Guildchest] %s L2CGuildChestLike ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	g.guildChest[recv.GuildChest.Id] = (*clGuildChest)(recv.GuildChest).convert2Data()
	//edit here
}

func (g *Guildchest) C2LGuildChestActivate() {
	cmsg := &cl.C2L_GuildChestActivate{}

	if g.u.userdata.GuildId == 0 {
		g.randCommandNoGuild()
		return
	}

	now := time.Now().Unix()
	if len(g.guildChest) >= int(goxml.GetData().GuildConfigInfoM.GetGuildChestMaxNum()) {
		return
	}
	for id, v := range g.item {
		if v == nil {
			delete(g.item, id)
		}
		if v.ExpireTime < now {
			delete(g.item, id)
		}
		cmsg.ItemId = v.Id
		break
	}
	if cmsg.ItemId != 0 {
		g.u.SendCmd(uint32(cl.ID_MSG_C2L_GuildChestActivate), cmsg)
	}
}

func (g *Guildchest) L2CGuildChestActivate(recv *cl.L2C_GuildChestActivate) {
	l4g.Debug("[Guildchest] %s L2CGuildChestActivate ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	delete(g.item, recv.ItemId)
	g.guildChest[recv.GuildChest.Id] = (*clGuildChest)(recv.GuildChest).convert2Data()
	//edit here
}

func (g *Guildchest) L2CUserGuildChestItemNotify(recv *cl.L2C_UserGuildChestItemNotify) {
	l4g.Debug("[Guildchest] %s L2CUserGuildChestItemNotify ret:%s, recv:%+v",
		g.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	if recv.NewGuildChest != nil {
		g.guildChest[recv.NewGuildChest.Id] = recv.NewGuildChest.Clone()
	}
	if len(recv.Items) > 0 {
		for _, v := range recv.Items {
			g.item[v.Id] = v
		}
	}

	//edit here
}

func (g *Guildchest) randCommandNoGuild() {
	if g.u.RandBool90() {
		if !g.u.guild.creating {
			g.u.guild.C2LGuildList()
		}
	} else {
		if len(g.u.guild.userGuild.ApplyIds) < 1 {
			g.u.guild.C2LGuildCreate()
		}
	}
}
