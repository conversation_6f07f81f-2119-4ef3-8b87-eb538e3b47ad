package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"fmt"

	l4g "github.com/ivanabc/log4go"
)

type RateFunc func(*Rate)

var gRateActions []RateFunc

func InitRateActions() {
	gRateActions = append(gRateActions, (*Rate).C2LRateGetStatus)
	gRateActions = append(gRateActions, (*Rate).C2LRateScore)
}

type Rate struct {
	*UserLoad
}

func NewRate(r *Robot) *Rate {
	return &Rate{
		UserLoad: NewUserLoad(r),
	}
}

// 断线重连重置数据
func (r *Rate) reset() {
	r.load.reset()

	//edit here
}

func (r *Rate) RandAction() {
	switch r.load.state() {
	case dataLoadNone:
		r.C2LInit()
	case dataLoading:
		r.C2LRetryInit()
	default:
		if length := len(gRateActions); length != 0 {
			gRateActions[r.u.rd.Intn(length)](r)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (r *Rate) C2LInit() {
	r.load.Lock()
	r.load.setState(dataLoading)

	//r.C2LRateFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	//r.C2LRateGetStatus()
	r.C2LRateScore()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (r *Rate) L2CInitRet(ret uint32) {
	if r.load.state() == dataLoading {
		r.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			r.load.setState(dataLoaded)
		} else {
			r.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (r *Rate) C2LRetryInit() {
	if r.load.Lock() {
		//r.C2LRateFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (r *Rate) C2LRateGetStatus() {
	cmsg := &cl.C2L_RateGetStatus{}
	//edit here
	r.u.SendCmd(uint32(cl.ID_MSG_C2L_RateGetStatus), cmsg)
}

func (r *Rate) L2CRateGetStatus(recv *cl.L2C_RateGetStatus) {
	l4g.Debug("[Rate] %s L2CRateGetStatus ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	fmt.Println(fmt.Sprintf("[Rate] %s L2CRateGetStatus ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (r *Rate) C2LRateScore() {
	cmsg := &cl.C2L_RateScore{}
	cmsg.Score = 6
	cmsg.Content = "赞"
	//edit here
	r.u.SendCmd(uint32(cl.ID_MSG_C2L_RateScore), cmsg)
}

func (r *Rate) L2CRateScore(recv *cl.L2C_RateScore) {
	l4g.Debug("[Rate] %s L2CRateScore ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	fmt.Println(fmt.Sprintf("[Rate] %s L2CRateScore ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
