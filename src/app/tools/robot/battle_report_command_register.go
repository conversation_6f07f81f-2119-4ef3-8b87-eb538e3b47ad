package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotBattleReportCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_OssBattleReport), L2COssBattleReportCommand{})
}

type L2COssBattleReportCommand struct {
}

func (c L2COssBattleReportCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_OssBattleReport{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2COssBattleReportCommand unmarshal error: %s", err)
		return false
	}
	robot.battleReport.L2COssBattleReport(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
