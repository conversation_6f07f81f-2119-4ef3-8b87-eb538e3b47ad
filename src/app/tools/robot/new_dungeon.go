package main

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"time"

	"github.com/spf13/viper"
)

func newDungeonOp(r *Robot) {
	println("主线副本")
	cfg := viper.Sub("dungeon")
	level := cfg.GetInt("level")
	d := r.dungeon
	if d.load.state() != dataLoaded {
		d.C2LInit()
		time.Sleep(sleepInterval)
	}
	time.Sleep(time.Second)
	if d.currentID >= uint32(level) {
		return
	}
	fid := uint32(common.FORMATION_ID_FI_DUNGEON)
	r.SetFormation(fid, true)
	beginID := d.currentID + 1
	if d.currentID == 0 {
		beginID = 10001
	}
	for id := beginID; id <= uint32(level); id++ {
		time.Sleep(4 * time.Second)
		smsg := &cl.C2L_DungeonFight{
			SysId: id,
		}
		r.SendCmd(uint32(cl.ID_MSG_C2L_DungeonFight), smsg)
	}
}
