package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotSevendayloginCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SevenDayLoginData), L2CSevenDayLoginDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SevenDayLoginTakeAward), L2CSevenDayLoginTakeAwardCommand{})
}

type L2CSevenDayLoginDataCommand struct {
}

func (c L2CSevenDayLoginDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SevenDayLoginData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSevenDayLoginDataCommand unmarshal error: %s", err)
		return false
	}
	robot.sevendaylogin.L2CSevenDayLoginData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSevenDayLoginTakeAwardCommand struct {
}

func (c L2CSevenDayLoginTakeAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SevenDayLoginTakeAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSevenDayLoginTakeAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.sevendaylogin.L2CSevenDayLoginTakeAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
