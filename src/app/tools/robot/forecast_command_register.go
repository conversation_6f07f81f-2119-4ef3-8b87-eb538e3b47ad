package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotForecastCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ForecastGetData), L2CForecastGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ForecastReceiveAward), L2CForecastReceiveAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ForecastUpdate), L2CForecastUpdateCommand{})
}

type L2CForecastGetDataCommand struct {
}

func (c L2CForecastGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ForecastGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CForecastGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.forecast.L2CForecastGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CForecastReceiveAwardCommand struct {
}

func (c L2CForecastReceiveAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ForecastReceiveAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CForecastReceiveAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.forecast.L2CForecastReceiveAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CForecastUpdateCommand struct {
}

func (c L2CForecastUpdateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ForecastUpdate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CForecastUpdateCommand unmarshal error: %s", err)
		return false
	}
	robot.forecast.L2CForecastUpdate(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
