package main

import (
	//"app/logic/character"
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"math/rand"

	l4g "github.com/ivanabc/log4go"
)

type FormationFunc func(*Formation)

var gFormationActions []FormationFunc

func InitFormationActions() {
	//gFormationActions = append(gFormationActions, (*Formation).C2LGetFormation)
	gFormationActions = append(gFormationActions, (*Formation).C2LFormation)
}

type Formation struct {
	*UserLoad
}

func NewFormation(r *Robot) *Formation {
	a := &Formation{
		UserLoad: NewUserLoad(r),
	}
	a.load.setState(dataLoaded)
	return a
}

//断线重连重置数据
/*
func (d *Formation) reset() {
	d.load.reset()

	//edit here
}
*/

func (d *Formation) RandAction() {
	switch d.load.state() {
	case dataLoadNone:
		d.C2LInit()
	case dataLoading:
		d.C2LRetryInit()
	default:
		if length := len(gFormationActions); length != 0 {
			gFormationActions[d.u.rd.Intn(length)](d)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (d *Formation) C2LInit() {
	d.load.setState(dataLoaded)
	/*
		d.load.Lock()
		d.load.setState(dataLoading)
		//d.C2LFormation()
		d.C2LGetFormation()
	*/
}

// 请求协议返回后调用
func (d *Formation) L2CInitRet(ret uint32) {
	if d.load.state() == dataLoading {
		d.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			d.load.setState(dataLoaded)
		} else {
			d.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (d *Formation) C2LRetryInit() {
	if d.load.Lock() {
		d.C2LGetFormation()
	}
}

func (d *Formation) C2LGetFormation() {
	cmsg := &cl.C2L_GetFormation{}
	//edit here
	cmsg.FormationIds = d.randGetFormationID()
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_GetFormation), cmsg)
}

func (d *Formation) randGetFormationID() []uint32 {
	if d.u.RandNormal() {
		return goxml.GetData().FormationInfoM.GetAllFids()
	} else {
		return []uint32{d.u.RandUint32()}
	}
}

func (d *Formation) L2CGetFormation(recv *cl.L2C_GetFormation) {
	l4g.Debug("[Formation] %s L2CGetFormation ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	//d.L2CInitRet(recv.Ret)
	d.u.battle.L2CInitRet(recv.Ret)
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	d.u.formations = recv.Formations
	l4g.Infof("Formation L2CGetFormation %+v", d.u.formations)
	d.u.battle.L2CInitRet(recv.Ret)
	//edit here
}

func (d *Formation) C2LFormation() {
	r := d.u
	fid := d.randFormationID()
	_, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(fid)
	if teamNum == 0 {
		teamNum = 1
	}
	formation := &cl.Formation{
		Id: fid,
	}

	usedHero := make(map[uint32]struct{})
	usedArtifact := make(map[uint32]struct{})
	for i := 0; i < teamNum; i++ {
		pos1 := d.randFormationHeroPos()
		pos2 := d.randFormationArtifactPos()
		fInfos := make([]*cl.FormationInfo, 0, len(pos1))
		fArtifacts := make([]*cl.FormationArtifactInfo, 0, len(pos2))
		heroes := d.u.RandHeroIDByNum(usedHero, len(pos1))
		artifacts := d.u.RandArtifactIDByNum(usedArtifact, len(pos2))
		for k, v := range heroes {
			fInfos = append(fInfos, &cl.FormationInfo{
				Pos: uint32(pos1[k]),
				Hid: v,
			})
		}
		for k, v := range artifacts {
			usedArtifact[v] = struct{}{}
			fArtifacts = append(fArtifacts, &cl.FormationArtifactInfo{
				Pos: uint32(pos2[k]),
				Aid: v,
			})
		}
		formation.Teams = append(formation.Teams, &cl.FormationTeamInfo{
			Info:      fInfos,
			Artifacts: fArtifacts,
		})
	}

	cmsg := &cl.C2L_Formation{
		FormationId: fid,
		Formation:   formation,
	}
	r.SendCmd(uint32(cl.ID_MSG_C2L_Formation), cmsg)
}

func (d *Formation) L2CFormation(recv *cl.L2C_Formation) {
	l4g.Debug("[Formation] %s L2CFormation ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	d.L2CInitRet(recv.Ret)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	d.u.UpdateFormation(recv.FormationId, recv.Formation)
	//edit here
}

func (d *Formation) randFormationID() uint32 {
	if d.u.RandNormal() {
		fids := goxml.GetData().FormationInfoM.GetAllFids()
		if len(fids) == 0 {
			return uint32(0)
		}
		return fids[d.u.Rand().Intn(len(fids))]
	}
	return d.u.RandUint32()
}
func (d *Formation) randFormationHeroPos() []int {
	if d.u.RandNormal() {
		pos := d.u.Rand().Intn(5) + 1 //总共5个位置
		return d.u.RandSliceInt(1, 5, pos)
	}
	return d.u.RandSliceInt(0, 1000, rand.Intn(1000))
}

func (d *Formation) randFormationArtifactPos() []int {
	if d.u.RandNormal() {
		if len(d.u.artifacts) > 0 {
			num := len(d.u.artifacts)
			if num > 3 {
				num = 3
			}
			if num > 0 {
				pos := d.u.Rand().Intn(num) + 1 //总共3个位置
				return d.u.RandSliceInt(1, num, pos)
			}
		}
		return nil
	}
	return d.u.RandSliceInt(0, 1000, rand.Intn(1000))
}

func (d *Formation) RandSpecialHeroID() uint64 {
	if d.u.RandNormal() && len(d.u.specialHeroes) > 0 {
		max := len(d.u.specialHeroes)
		i := d.u.Rand().Intn(max)
		return d.u.specialHeroes[i].Data.Id
	}
	return d.u.RandUint64()
}

func (d *Formation) InitSeasonArenaAttack() bool {
	var setFormation bool
	if d.u.crystal.Data == nil {
		d.u.crystal.C2LCrystalGetAllData()
		setFormation = true
		return setFormation
	}

	existHero := make(map[uint64]struct{})
	for _, slotInfo := range d.u.crystal.Data.Slot {
		if slotInfo == nil {
			continue
		}
		existHero[slotInfo.Hid] = struct{}{}
	}
	for _, hid := range d.u.crystal.Data.ContractHeroes {
		if hid == 0 {
			continue
		}
		existHero[hid] = struct{}{}
	}
	existHeroS := make([]uint64, 0, len(existHero))
	for hid := range existHero {
		existHeroS = append(existHeroS, hid)
	}

	for _, attackID := range goxml.SeasonArenaDefender2Attack {
		formation, exist := d.u.formations[attackID]
		if exist {
			continue
		}

		formationInfo := goxml.GetData().FormationInfoM.Index(attackID)
		if formationInfo == nil {
			l4g.Errorf("u:%d formation get id:%d is failed", d.u.ID(), attackID)
			continue
		}

		if int(formationInfo.TeamNum) > len(existHero) {
			continue
		}

		tmp := make([]*cl.FormationTeamInfo, 0, formationInfo.TeamNum)
		for i := 0; i < int(formationInfo.TeamNum); i++ {
			tmp = append(tmp, &cl.FormationTeamInfo{
				Info: []*cl.FormationInfo{
					{
						Pos: 1,
						Hid: existHeroS[i],
					},
				},
			})
		}

		formation = &cl.Formation{
			Id:     attackID,
			Teams:  tmp,
			Name:   "",
			SortId: 0,
		}

		d.u.SendCmd(uint32(cl.ID_MSG_C2L_Formation), formation)
		setFormation = true
	}
	return setFormation
}

/*
func (d *Formation) randFormationHeroID() uint64 {
	if d.u.RandNormal() {
		return d.u.RandHeroID()
	}
	return d.u.RandUint64()
}

func (d *Formation) randFormationArtifactSysID() uint32 {
	if d.u.RandNormal() {
		max := len(d.u.artifacts)
		if max > 0 {
			i := d.u.Rand().Intn(max)
			return d.u.artifacts[i].SysId
		}
	}
	return d.u.RandUint32()
}
*/

/*
func (d *Formation) randFormationArtifactStage() uint32 {
	if d.u.RandNormal() && len(d.u.artifacts) > 0 {
		max := len(d.u.artifacts)
		i := d.u.Rand().Intn(max)
		return d.u.artifacts[i].Star
	}
	return d.u.RandUint32()
}
*/
