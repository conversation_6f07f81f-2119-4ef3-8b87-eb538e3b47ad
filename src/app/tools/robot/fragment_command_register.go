package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotFragmentCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_FragmentCompose), L2CFragmentComposeCommand{})
}

type L2CFragmentComposeCommand struct {
}

func (c L2CFragmentComposeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_FragmentCompose{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CFragmentComposeCommand unmarshal error: %s", err)
		return false
	}
	robot.fragment.L2CFragmentCompose(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
