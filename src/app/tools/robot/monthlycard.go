package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"fmt"

	l4g "github.com/ivanabc/log4go"
)

type MonthlycardFunc func(*Monthlycard)

var gMonthlycardActions []MonthlycardFunc

func InitMonthlycardActions() {
	gMonthlycardActions = append(gMonthlycardActions, (*Monthlycard).C2LMonthlyCardGetData)
	gMonthlycardActions = append(gMonthlycardActions, (*Monthlycard).C2LMonthlyCardReceiveAward)
}

type Monthlycard struct {
	*UserLoad
}

func NewMonthlycard(r *Robot) *Monthlycard {
	m := &Monthlycard{
		UserLoad: NewUserLoad(r),
	}
	return m
}

// 断线重连重置数据
func (m *Monthlycard) reset() {
	m.load.reset()

	//edit here
}

func (m *Monthlycard) RandAction() {
	switch m.load.state() {
	case dataLoadNone:
		m.C2LInit()
	case dataLoading:
		m.C2LRetryInit()
	default:
		if length := len(gMonthlycardActions); length != 0 {
			gMonthlycardActions[m.u.rd.Intn(length)](m)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (m *Monthlycard) C2LInit() {
	m.load.Lock()
	m.load.setState(dataLoading)

	//m.C2LMonthlycardFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	//m.C2LMonthlyCardGetData()
	m.C2LMonthlyCardReceiveAward()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (m *Monthlycard) L2CInitRet(ret uint32) {
	if m.load.state() == dataLoading {
		m.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			m.load.setState(dataLoaded)
		} else {
			m.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (m *Monthlycard) C2LRetryInit() {
	if m.load.Lock() {
		//m.C2LMonthlycardFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (m *Monthlycard) C2LMonthlyCardGetData() {
	cmsg := &cl.C2L_MonthlyCardGetData{}
	//edit here
	m.u.SendCmd(uint32(cl.ID_MSG_C2L_MonthlyCardGetData), cmsg)
}

func (m *Monthlycard) L2CMonthlyCardGetData(recv *cl.L2C_MonthlyCardGetData) {
	l4g.Debug("[Monthlycard] %s L2CMonthlyCardGetData ret:%s, recv:%+v",
		m.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	fmt.Println(fmt.Sprintf("[Monthlycard] %s L2CMonthlyCardGetData ret:%s, recv:%+v", m.u.UUID(), cret.RET_name[int32(recv.Ret)], recv))
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (m *Monthlycard) C2LMonthlyCardReceiveAward() {
	cmsg := &cl.C2L_MonthlyCardReceiveAward{}
	cmsg.SysId = 3
	//edit here
	m.u.SendCmd(uint32(cl.ID_MSG_C2L_MonthlyCardReceiveAward), cmsg)
}

func (m *Monthlycard) L2CMonthlyCardReceiveAward(recv *cl.L2C_MonthlyCardReceiveAward) {
	l4g.Debug("[Monthlycard] %s L2CMonthlyCardReceiveAward ret:%s, recv:%+v",
		m.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
