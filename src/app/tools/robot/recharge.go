package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type RechargeFunc func(*Recharge)

var gRechargeActions []RechargeFunc

func InitRechargeActions() {
	//gRechargeActions = append(gRechargeActions, (*Recharge).C2LRechargeGetData)
	gRechargeActions = append(gRechargeActions, (*Recharge).C2LRechargeSimulation)
}

type Recharge struct {
	*UserLoad
}

func NewRecharge(r *Robot) *Recharge {
	recharge := &Recharge{
		UserLoad: NewUserLoad(r),
	}
	return recharge
}

// 断线重连重置数据
func (r *Recharge) reset() {
	r.load.reset()

	//edit here
}

func (r *Recharge) RandAction() {
	switch r.load.state() {
	case dataLoadNone:
		r.C2LInit()
	case dataLoading:
		r.C2LRetryInit()
	default:
		if length := len(gRechargeActions); length != 0 {
			gRechargeActions[r.u.rd.Intn(length)](r)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (r *Recharge) C2LInit() {
	r.load.Lock()
	r.load.setState(dataLoading)

	//r.C2LRechargeFlushInfo() //TODO:修改为初始化请求函数，比如带有Info的函数
	//r.C2LRechargeSimulation()
	r.C2LRechargeGetData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (r *Recharge) L2CInitRet(ret uint32) {
	if r.load.state() == dataLoading {
		r.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			r.load.setState(dataLoaded)
		} else {
			r.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (r *Recharge) C2LRetryInit() {
	if r.load.Lock() {
		//r.C2LRechargeFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (r *Recharge) C2LRechargeGetData() {
	cmsg := &cl.C2L_RechargeGetData{}
	//edit here
	r.u.SendCmd(uint32(cl.ID_MSG_C2L_RechargeGetData), cmsg)
}

func (r *Recharge) L2CRechargeGetData(recv *cl.L2C_RechargeGetData) {
	l4g.Debug("[Recharge] %s L2CRechargeGetData ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	r.L2CInitRet(recv.Ret)
}

func (r *Recharge) L2CRechargeNotify(recv *cl.L2C_RechargeNotify) {
	l4g.Debug("[Recharge] %s L2CRechargeNotify ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (r *Recharge) L2CRechargeFirstGiftNotify(recv *cl.L2C_RechargeFirstGiftNotify) {
	l4g.Debug("[Recharge] %s L2CRechargeFirstGiftNotify ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (r *Recharge) C2LRechargeSimulation() {
	cmsg := &cl.C2L_RechargeSimulation{}
	//edit here
	cmsg.Custom = &cl.OrderCustomData{
		Type:       0,
		Id:         1,
		OpId:       3,
		OpGroupId:  2,
		GameId:     1,
		Channel:    0,
		InternalId: 10006,
	}
	r.u.SendCmd(uint32(cl.ID_MSG_C2L_RechargeSimulation), cmsg)
}

func (r *Recharge) L2CRechargeSimulation(recv *cl.L2C_RechargeSimulation) {
	l4g.Debug("[Recharge] %s L2CRechargeSimulation ret:%s, recv:%+v",
		r.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
