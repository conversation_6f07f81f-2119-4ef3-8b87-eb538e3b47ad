package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotSeasonLevelCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonLevelGetData), L2CSeasonLevelGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonLevelUp), L2CSeasonLevelUpCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonLevelRecvTaskAwards), L2CSeasonLevelRecvTaskAwardsCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonLevelRecvLvAwards), L2CSeasonLevelRecvLvAwardsCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonLevelTaskUpdate), L2CSeasonLevelTaskUpdateCommand{})
}

type L2CSeasonLevelGetDataCommand struct {
}

func (c L2CSeasonLevelGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonLevelGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonLevelGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonLevel.L2CSeasonLevelGetData(recv)
	return recv.Ret == uint32(cret.RET_OK)
}

type L2CSeasonLevelUpCommand struct {
}

func (c L2CSeasonLevelUpCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonLevelUp{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonLevelUpCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonLevel.L2CSeasonLevelUp(recv)
	return recv.Ret == uint32(cret.RET_OK)
}

type L2CSeasonLevelRecvTaskAwardsCommand struct {
}

func (c L2CSeasonLevelRecvTaskAwardsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonLevelRecvTaskAwards{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonLevelRecvTaskAwardsCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonLevel.L2CSeasonLevelRecvTaskAwards(recv)
	return recv.Ret == uint32(cret.RET_OK)
}

type L2CSeasonLevelRecvLvAwardsCommand struct {
}

func (c L2CSeasonLevelRecvLvAwardsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonLevelRecvLvAwards{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonLevelRecvLvAwardsCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonLevel.L2CSeasonLevelRecvLvAwards(recv)
	return recv.Ret == uint32(cret.RET_OK)
}

type L2CSeasonLevelTaskUpdateCommand struct {
}

func (c L2CSeasonLevelTaskUpdateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonLevelTaskUpdate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonLevelTaskUpdateCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonLevel.L2CSeasonLevelTaskUpdate(recv)
	return recv.Ret == uint32(cret.RET_OK)
}
