package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type BaseFunc func(*Base)

var gBaseActions []BaseFunc

func InitBaseActions() {
	gBaseActions = append(gBaseActions, (*Base).C2LViewUser)
	// gBaseActions = append(gBaseActions, (*Base).C2LGetUserHeroData)
	gBaseActions = append(gBaseActions, (*Base).C2LTestBattleData)
	gBaseActions = append(gBaseActions, (*Base).C2LGlobalAttrGet)
	gBaseActions = append(gBaseActions, (*Base).C2LSyncQuestionnaireCommand)
}

type Base struct {
	*UserLoad
}

func NewBase(r *Robot) *Base {
	b := &Base{
		UserLoad: NewUserLoad(r),
	}
	return b
}

// 断线重连重置数据
func (b *Base) reset() {
	b.load.reset()

	//edit here
}

func (b *Base) RandAction() {
	switch b.load.state() {
	case dataLoadNone:
		b.C2LInit()
	case dataLoading:
		b.C2LRetryInit()
	default:
		if length := len(gBaseActions); length != 0 {
			gBaseActions[b.u.rd.Intn(length)](b)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (b *Base) C2LInit() {
	b.load.Lock()
	b.load.setState(dataLoading)

	//b.C2LBaseFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	//b.C2LViewUser()
	//b.C2LTestBattleData()
	//b.C2LGlobalAttrGet()
	//b.C2LGetUserHeroData()
	b.C2LSyncQuestionnaireCommand()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (b *Base) L2CInitRet(ret uint32) {
	if b.load.state() == dataLoading {
		b.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			b.load.setState(dataLoaded)
		} else {
			b.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (b *Base) C2LRetryInit() {
	/*
		if b.load.Lock() {
			//b.C2LBaseFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
		}
	*/
}

func (b *Base) C2LViewUser() {
	cmsg := &cl.C2L_ViewUser{}

	cmsg.Id = b.u.userdata.Id
	//edit here
	b.u.SendCmd(uint32(cl.ID_MSG_C2L_ViewUser), cmsg)
}

func (b *Base) L2CViewUser(recv *cl.L2C_ViewUser) {
	l4g.Debug("[Base] %s L2CViewUser ret:%s, recv:%+v",
		b.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (b *Base) L2COpGlobalAttr(recv *cl.L2C_OpGlobalAttr) {
	l4g.Debug("[Base] %s L2COpGlobalAttr, recv:%+v",
		b.u.UUID(), recv)

	//	if recv.Ret != uint32(cret.RET_OK) {
	//		return
	//	}

	//edit here
}

func (b *Base) C2LTestBattleData() {
	cmsg := &cl.C2L_TestBattleData{}
	//edit here
	b.u.SendCmd(uint32(cl.ID_MSG_C2L_TestBattleData), cmsg)
}

func (b *Base) L2CTestBattleData(recv *cl.L2C_TestBattleData) {
	l4g.Debug("[Base] %s L2CTestBattleData ret:%s, recv:%+v",
		b.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (b *Base) C2LGlobalAttrGet() {
	cmsg := &cl.C2L_GlobalAttrGet{}
	//edit here
	b.u.SendCmd(uint32(cl.ID_MSG_C2L_GlobalAttrGet), cmsg)
}

func (b *Base) L2CGlobalAttrGet(recv *cl.L2C_GlobalAttrGet) {
	l4g.Debug("[Base] %s L2CGlobalAttrGet ret:%s, recv:%+v",
		b.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (b *Base) C2LSyncQuestionnaireCommand() {
	cmsg := &cl.C2L_SyncQuestionnaire{}
	//edit here
	b.u.SendCmd(uint32(cl.ID_MSG_C2L_SyncQuestionnaire), cmsg)
}

func (b *Base) L2CSyncQuestionnaireCommand(recv *cl.L2C_SyncQuestionnaire) {
	l4g.Debug("[Base] %s L2CSyncQuestionnaireCommand ret:%s, recv:%+v",
		b.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
