package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type ArtifactDebutFunc func(*ArtifactDebut)

var gArtifactDebutActions []ArtifactDebutFunc

func InitArtifactDebutActions() {
	// gArtifactDebutActions = append(gArtifactDebutActions, (*ArtifactDebut).C2LArtifactDebutMainInfo)
	gArtifactDebutActions = append(gArtifactDebutActions, (*ArtifactDebut).C2LArtifactDebutSetWish)
	gArtifactDebutActions = append(gArtifactDebutActions, (*ArtifactDebut).C2LArtifactDebutSummon)
	gArtifactDebutActions = append(gArtifactDebutActions, (*ArtifactDebut).C2LArtifactDebutRecvActAward)
	gArtifactDebutActions = append(gArtifactDebutActions, (*ArtifactDebut).C2LArtifactDebutRecvTaskAward)
	gArtifactDebutActions = append(gArtifactDebutActions, (*ArtifactDebut).C2LArtifactDebutOpenPuzzle)
	// gArtifactDebutActions = append(gArtifactDebutActions, (*ArtifactDebut).C2LArtifactDebutGetActivity)
}

type ArtifactDebut struct {
	*UserLoad
	data    *cl.ArtifactDebut
	wishAid uint32
	actID   uint32
}

func NewArtifactDebut(r *Robot) *ArtifactDebut {
	a := &ArtifactDebut{
		UserLoad: NewUserLoad(r),
	}
	return a
}

func (a *ArtifactDebut) loadData(data *cl.ArtifactDebut) {
	if data == nil {
		return
	}

	if data.Base != nil {
		a.actID = data.Base.ActivityId
	}

	if len(data.Summons) > 0 {
		for _, v := range a.data.Summons {
			if v.Category == uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR) {
				a.wishAid = v.WishAid
				break
			}
		}
	}
}

func (a *ArtifactDebut) getActID() uint32 {
	return a.actID
}

func (a *ArtifactDebut) setWishArtifact(aid uint32) {
	a.wishAid = aid
}

func (a *ArtifactDebut) haveWishArtifact() bool {
	return a.wishAid > 0
}

func (a *ArtifactDebut) randomWishArtifact() uint32 {
	actID := a.getActID()
	if actID == 0 {
		return 0
	}

	var list []uint32
	if actID < 100 {
		list = goxml.GetData().ArtifactDebutDrawOptionalInfoM.Index(goxml.GetData().ArtifactDebutActivityInfoM.GetNewServerActID())
	} else {
		list = goxml.GetData().ArtifactDebutDrawOptionalInfoM.Index(actID)
	}

	key := a.u.rd.RandBetween(0, len(list)-1)
	return list[key]
}

func (a *ArtifactDebut) randomActIDs() []uint32 {
	max := a.u.rd.RandBetween(1, 7)
	ret := make([]uint32, 0, max)
	for i := 1; i <= max; i++ {
		if a.u.RandBoolHalf() {
			continue
		}
		ret = append(ret, uint32(i))
	}
	return ret
}

func (a *ArtifactDebut) randomPuzzleIDs() []uint32 {
	max := a.u.rd.RandBetween(1, 9)
	ret := make([]uint32, 0, max)
	for i := 1; i <= max; i++ {
		if a.u.RandBoolHalf() {
			continue
		}
		ret = append(ret, uint32(i))
	}
	return ret
}

// 断线重连重置数据
func (a *ArtifactDebut) reset() {
	a.load.reset()

	//edit here
}

func (a *ArtifactDebut) RandAction() {
	switch a.load.state() {
	case dataLoadNone:
		a.C2LInit()
	case dataLoading:
		a.C2LRetryInit()
	default:
		if length := len(gArtifactDebutActions); length != 0 {
			gArtifactDebutActions[a.u.rd.Intn(length)](a)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (a *ArtifactDebut) C2LInit() {
	a.load.Lock()
	a.load.setState(dataLoading)

	a.C2LArtifactDebutGetActivity()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (a *ArtifactDebut) L2CInitRet(ret uint32) {
	if a.load.state() == dataLoading {
		a.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			a.load.setState(dataLoaded)
		} else {
			a.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (a *ArtifactDebut) C2LRetryInit() {
	if a.load.Lock() {
		//a.C2LArtifactDebutGetActivity()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (a *ArtifactDebut) C2LArtifactDebutMainInfo() {
	cmsg := &cl.C2L_ArtifactDebutMainInfo{}
	//edit here
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactDebutMainInfo), cmsg)
}

func (a *ArtifactDebut) L2CArtifactDebutMainInfo(recv *cl.L2C_ArtifactDebutMainInfo) {
	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutMainInfo ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.loadData(recv.Data)
	a.L2CInitRet(recv.Ret)
}

func (a *ArtifactDebut) C2LArtifactDebutSetWish() {
	cmsg := &cl.C2L_ArtifactDebutSetWish{}
	//edit here
	cmsg.WishAid = a.randomWishArtifact()
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactDebutSetWish), cmsg)
}

func (a *ArtifactDebut) L2CArtifactDebutSetWish(recv *cl.L2C_ArtifactDebutSetWish) {
	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutSetWish ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.setWishArtifact(recv.WishAid)
}

func (a *ArtifactDebut) C2LArtifactDebutSummon() {
	if !a.haveWishArtifact() {
		a.C2LArtifactDebutSetWish()
		return
	}
	cmsg := &cl.C2L_ArtifactDebutSummon{}
	//edit here
	cmsg.Category = uint32(a.u.rd.RandBetween(1, 2))
	cmsg.IsMultiple = a.u.RandBoolHalf()
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactDebutSummon), cmsg)
}

func (a *ArtifactDebut) L2CArtifactDebutSummon(recv *cl.L2C_ArtifactDebutSummon) {
	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutSummon ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *ArtifactDebut) C2LArtifactDebutRecvActAward() {
	cmsg := &cl.C2L_ArtifactDebutRecvActAward{}
	//edit here
	cmsg.Type = uint32(a.u.rd.RandBetween(1, 3))
	cmsg.Ids = a.randomActIDs()
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactDebutRecvActAward), cmsg)
}

func (a *ArtifactDebut) L2CArtifactDebutRecvActAward(recv *cl.L2C_ArtifactDebutRecvActAward) {
	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutRecvActAward ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *ArtifactDebut) C2LArtifactDebutRecvTaskAward() {
	cmsg := &cl.C2L_ArtifactDebutRecvTaskAward{}
	//edit here
	cmsg.Ids = a.randomActIDs()
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactDebutRecvTaskAward), cmsg)
}

func (a *ArtifactDebut) L2CArtifactDebutRecvTaskAward(recv *cl.L2C_ArtifactDebutRecvTaskAward) {
	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutRecvTaskAward ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

// func (a *ArtifactDebut) L2CArtifactDebutUpdateTask(recv *cl.L2C_ArtifactDebutUpdateTask) {
// 	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutUpdateTask ret:%s, recv:%+v",
// 		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

// 	if recv.Ret != uint32(cret.RET_OK) {
// 		return
// 	}

// 	//edit here
// }

func (a *ArtifactDebut) C2LArtifactDebutOpenPuzzle() {
	cmsg := &cl.C2L_ArtifactDebutOpenPuzzle{}
	//edit here
	cmsg.Ids = a.randomPuzzleIDs()
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactDebutOpenPuzzle), cmsg)
}

func (a *ArtifactDebut) L2CArtifactDebutOpenPuzzle(recv *cl.L2C_ArtifactDebutOpenPuzzle) {
	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutOpenPuzzle ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

// func (a *ArtifactDebut) L2CArtifactDebutUpdateActivity(recv *cl.L2C_ArtifactDebutUpdateActivity) {
// 	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutUpdateActivity ret:%s, recv:%+v",
// 		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

// 	if recv.Ret != uint32(cret.RET_OK) {
// 		return
// 	}

// 	//edit here
// }

func (a *ArtifactDebut) C2LArtifactDebutGetActivity() {
	cmsg := &cl.C2L_ArtifactDebutGetActivity{}
	//edit here
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArtifactDebutGetActivity), cmsg)
}

func (a *ArtifactDebut) L2CArtifactDebutGetActivity(recv *cl.L2C_ArtifactDebutGetActivity) {
	l4g.Debug("[ArtifactDebut] %s L2CArtifactDebutGetActivity ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	now := time.Now().Unix()
	for _, data := range recv.Datas {
		if data.StartTime >= now && now <= data.EndTime {
			a.C2LArtifactDebutMainInfo()
			break
		}
	}
}
