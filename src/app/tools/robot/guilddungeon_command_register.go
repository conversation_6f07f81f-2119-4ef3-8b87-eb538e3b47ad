package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGuildDungeonCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonInfo), L2CGuildDungeonInfoCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonRecvChapterTaskAward), L2CGuildDungeonRecvChapterTaskAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonFight), L2CGuildDungeonFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonUserDamageRank), L2CGuildDungeonUserDamageRankCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonLogList), L2CGuildDungeonLogListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonChapterInfo), L2CGuildDungeonChapterInfoCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonRecvBossBoxAward), L2CGuildDungeonRecvBossBoxAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonBuyChallengeTimes), L2CGuildDungeonBuyChallengeTimesCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonNotify), L2CGuildDungeonNotifyCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonSeasonDivisionAward), L2CGuildDungeonSeasonDivisionAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonSetFocus), L2CGuildDungeonSetFocusCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonHallOfFame), L2CGuildDungeonHallOfFameCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuildDungeonTop3Guild), L2CGuildDungeonTop3GuildCommand{})
}

type L2CGuildDungeonInfoCommand struct {
}

func (c L2CGuildDungeonInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonInfo{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonInfoCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonInfo(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonRecvChapterTaskAwardCommand struct {
}

func (c L2CGuildDungeonRecvChapterTaskAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonRecvChapterTaskAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonRecvChapterTaskAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonRecvChapterTaskAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonFightCommand struct {
}

func (c L2CGuildDungeonFightCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonFight{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonFightCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonFight(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonUserDamageRankCommand struct {
}

func (c L2CGuildDungeonUserDamageRankCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonUserDamageRank{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonUserDamageRankCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonUserDamageRank(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonLogListCommand struct {
}

func (c L2CGuildDungeonLogListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonLogList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonLogListCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonLogList(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonChapterInfoCommand struct {
}

func (c L2CGuildDungeonChapterInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonChapterInfo{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonChapterInfoCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonChapterInfo(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonRecvBossBoxAwardCommand struct {
}

func (c L2CGuildDungeonRecvBossBoxAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonRecvBossBoxAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonRecvBossBoxAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonRecvBossBoxAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonBuyChallengeTimesCommand struct {
}

func (c L2CGuildDungeonBuyChallengeTimesCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonBuyChallengeTimes{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonBuyChallengeTimesCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonBuyChallengeTimes(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonNotifyCommand struct {
}

func (c L2CGuildDungeonNotifyCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonNotify{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonNotifyCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonNotify(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonSeasonDivisionAwardCommand struct {
}

func (c L2CGuildDungeonSeasonDivisionAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonSeasonDivisionAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonSeasonDivisionAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonSeasonDivisionAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonSetFocusCommand struct {
}

func (c L2CGuildDungeonSetFocusCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonSetFocus{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonSetFocusCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonSetFocus(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonHallOfFameCommand struct {
}

func (c L2CGuildDungeonHallOfFameCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonHallOfFame{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonHallOfFameCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonHallOfFame(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuildDungeonTop3GuildCommand struct {
}

func (c L2CGuildDungeonTop3GuildCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuildDungeonTop3Guild{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuildDungeonTop3GuildCommand unmarshal error: %s", err)
		return false
	}
	robot.guilddungeon.L2CGuildDungeonTop3Guild(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
