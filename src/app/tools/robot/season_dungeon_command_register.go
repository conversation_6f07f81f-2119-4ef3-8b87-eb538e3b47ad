package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotSeasonDungeonCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonDungeonGetData), L2CSeasonDungeonGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonDungeonFight), L2CSeasonDungeonFightCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonDungeonRecvReward), L2CSeasonDungeonRecvRewardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonDungeonUpdateTask), L2CSeasonDungeonUpdateTaskCommand{})
}

type L2CSeasonDungeonGetDataCommand struct {
}

func (c L2CSeasonDungeonGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonDungeonGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonDungeonGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonDungeon.L2CSeasonDungeonGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonDungeonFightCommand struct {
}

func (c L2CSeasonDungeonFightCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonDungeonFight{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonDungeonFightCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonDungeon.L2CSeasonDungeonFight(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonDungeonRecvRewardCommand struct {
}

func (c L2CSeasonDungeonRecvRewardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonDungeonRecvReward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonDungeonRecvRewardCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonDungeon.L2CSeasonDungeonRecvReward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSeasonDungeonUpdateTaskCommand struct {
}

func (c L2CSeasonDungeonUpdateTaskCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonDungeonUpdateTask{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonDungeonUpdateTaskCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonDungeon.L2CSeasonDungeonUpdateTask(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
