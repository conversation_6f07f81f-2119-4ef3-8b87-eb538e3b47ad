package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGuidanceCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuidanceList), L2CGuidanceListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuidanceFinishStep), L2CGuidanceFinishNodeCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GuidanceFinishGroup), L2CGuidanceFinishGroupCommand{})
}

type L2CGuidanceListCommand struct {
}

func (c L2CGuidanceListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuidanceList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuidanceListCommand unmarshal error: %s", err)
		return false
	}
	robot.guidance.L2CGuidanceList(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuidanceFinishNodeCommand struct {
}

func (c L2CGuidanceFinishNodeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuidanceFinishStep{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuidanceFinishNodeCommand unmarshal error: %s", err)
		return false
	}
	robot.guidance.L2CGuidanceFinishNode(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGuidanceFinishGroupCommand struct {
}

func (c L2CGuidanceFinishGroupCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GuidanceFinishGroup{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGuidanceFinishGroupCommand unmarshal error: %s", err)
		return false
	}
	robot.guidance.L2CGuidanceFinishGroup(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
