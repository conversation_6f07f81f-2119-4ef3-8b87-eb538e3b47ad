package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	//	"app/protos/out/common"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

var gRobotCommandM = NewCommandM()

func InitRobotCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_KeepAlive), L2CKeepAliveCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GetUser), L2CGetUserCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GetBags), L2CGetBagsCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_Flush), L2CFlushCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_OpResources), L2COpResourcesCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GetCommonRank), L2CGetCommonRankCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_OpNum), L2COpNumCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GM), L2CGMCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SetName), L2CSetNameCommand{})

	//邮件
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GetMails), L2CGetMailsCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ReadMail), L2CReadMailCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DrawMails), L2CDrawMailsCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DeleteMails), L2CDeleteMailsCommand{})

	//商店
	InitRobotShopCommand()
	//好友
	InitRobotFriendCommand()
	//布阵任务
	InitRobotFormationCommand()
	//战役
	InitRobotDungeonCommand()
	//英雄
	InitRobotHeroCommand()
	//召唤
	InitRobotSummonCommand()
	//任务
	InitRobotTaskCommand()
	//碎片
	InitRobotFragmentCommand()
	//装备
	InitRobotEquipCommand()
	//爬塔
	InitRobotTowerCommand()
	//竞技场
	InitRobotArenaCommand()
	//神器
	InitRobotArtifactCommand()
	//基础
	InitRobotBaseCommand()
	//材料本
	InitRobotTrialCommand()
	//纹章
	InitRobotEmblemCommand()
	//点金
	InitRobotGoldBuyCommand()
	//悬赏
	InitRobotDispatchCommand()
	//道具
	InitRobotItemCommand()
	//装备
	InitRobotEquipCommand()
	//排行成就
	InitRobotRankAchieveCommand()
	//装备
	InitRobotEquipCommand()
	//迷宫
	InitRobotMazeCommand()
	//个人boss
	InitRobotMirageCommand()
	//公会
	InitRobotGuildCommand()
	//迷宫
	InitRobotMazeCommand()
	//英雄列传
	InitRobotTalesCommand()
	//境界
	InitRobotMemoryCommand()
	//密林
	InitRobotFlowerCommand()
	//公会副本
	InitRobotGuildDungeonCommand()
	//战斗
	InitRobotBattleCommand()
	//公会天赋
	InitRobotGuildTalentCommand()
	//头像
	InitRobotAvatarCommand()
	//新手引导
	InitRobotGuidanceCommand()
	//功勋
	InitRobotMedalCommand()
	//聊天
	InitRobotChatCommand()
	//新功能预告
	InitRobotForecastCommand()
	//七日登录
	InitRobotSevendayloginCommand()
	//月卡
	InitRobotMonthlycardCommand()
	//水晶
	InitRobotCrystalCommand()
	//条件爬塔
	InitRobotTowerstarCommand()
	//限时礼包
	InitRobotActivityRechargeCommand()
	//vip
	InitRobotVipCommand()
	//充值
	InitRobotRechargeCommand()
	//战令
	InitRobotPassCommand()
	//评分
	InitRobotRateCommand()
	//测试战力计算
	InitRobotCalPowerCommand()
	//神魔抽卡
	InitRobotDivineDemonCommand()
	//神树争霸
	InitRobotWrestleCommand()
	//契约之所
	InitRobotGoddessContractCommand()
	//测试oss战报写入
	InitRobotBattleReportCommand()
	//神器首发
	InitRobotArtifactDebutCommand()
	//图鉴
	InitRobotHandbookCommand()
	//777抽活动
	InitRobotGodPresentCommand()
	//掉落活动
	InitRobotDropActivityCommand()
	//公会宝箱
	InitRobotGuildchestCommand()
	//世界boss
	InitRobotWorldBossCommand()
	//百塔
	InitRobotTowerSeasonCommand()
	//皮肤
	InitRobotSkinCommand()
	//失序空间
	InitRobotDisorderLandCommand()
	//赛季等级
	InitRobotSeasonLevelCommand()
	//赛季羁绊
	InitRobotSeasonLinkCommand()
	//赛季主线
	InitRobotSeasonDungeonCommand()
	//新年活动
	InitRobotNewYearActivityCommand()
	//赛季竞技场
	InitRobotSeasonarenaCommand()
	//<InitRobotCommand end>（这行不要删除不要修改，用于脚本识别插入）
}

type L2CGetUserCommand struct {
}

func (c L2CGetUserCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GetUser{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGetUserCommand unmarshal error")
		return false
	}

	robot.userdata = recv.User
	l4g.Info("robot get user: %s", recv)
	return true
}

type L2CGetBagsCommand struct {
}

func (c L2CGetBagsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GetBags{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGetBagsCommand unmarshal error")
		return false
	}
	if len(recv.Items) > 0 {
		robot.itemsBag = recv.Items
	}
	if len(recv.Fragments) > 0 {
		robot.fragmentsBag = recv.Fragments
	}
	if len(recv.Tokens) > 0 {
		robot.tokensBag = recv.Tokens
	}

	if len(recv.ArtifactFragments) > 0 {
		robot.artifactFragmentsBag = recv.ArtifactFragments
	}

	l4g.Info("robot get items: %s", recv)
	return true
}

type L2CGetMailsCommand struct {
}

func (c L2CGetMailsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GetMails{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGetMailsCommand unmarshal error: %s", err)
		return false
	}
	robot.mail.RetInfo(recv)
	return true
}

type L2CReadMailCommand struct {
}

func (c L2CReadMailCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ReadMail{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CReadMailCommand unmarshal error: %s", err)
		return false
	}
	robot.mail.RetRead(recv)
	return true
}

type L2CDrawMailsCommand struct {
}

func (c L2CDrawMailsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_DrawMails{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDrawMailsCommand unmarshal error: %s", err)
		return false
	}
	robot.mail.RetDraw(recv)
	return true
}

type L2CDeleteMailsCommand struct {
}

func (c L2CDeleteMailsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_DeleteMails{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDeleteMailsCommand unmarshal error: %s", err)
		return false
	}
	robot.mail.RetDelete(recv)
	return true
}

type L2CKeepAliveCommand struct {
}

func (c L2CKeepAliveCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	return true
}

type L2CFlushCommand struct {
}

func (c L2CFlushCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	robot.LoginSuccess()
	return true
}

type L2COpResourcesCommand struct {
}

func (c L2COpResourcesCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_OpResources{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2COpResourcesCommand unmarshal error: %s", err)
		return false
	}
	l4g.Debug("robot %s recv L2C_OpResources: %s", robot.UUID(), recv)
	for _, res := range recv.Increase {
		robot.IncreaseResource(res)
	}
	for _, res := range recv.Reduce {
		robot.ReduceResource(res)
	}
	return true
}

type L2CGMCommand struct {
}

func (c L2CGMCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GM{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGMCommand unmarshal error: %s", err)
		return false
	}
	l4g.Debug("robot %s recv L2CGMCommand: %s", robot.Name(), recv)
	for _, res := range recv.Awards {
		robot.IncreaseResource(res)
	}

	robot.RequestSetName()
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSetNameCommand struct {
}

func (c L2CSetNameCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SetName{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSetNameCommand unmarshal error: %s", err)
		return false
	}

	robot.SetName(recv.Name)
	robot.manager.AddFinishJobCount()
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGetCommonRankCommand struct {
}

func (c L2CGetCommonRankCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GetCommonRank{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGetCommonRankCommand unmarshal error: %s", err)
		return false
	}

	if robot.friend.reqCommonRank {
		robot.friend.L2CGetCommonRank(recv)
	}

	return true
}

type L2COpNumCommand struct {
}

func (c L2COpNumCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_OpNum{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2COpNumCommand unmarshal error: %s", err)
		return false
	}
	l4g.Debug("robot %s recv L2C_OpNum: %s", robot.Name(), recv)
	for k, v := range recv.NumInfo {
		if len(robot.userdata.NumInfo) == 0 {
			robot.userdata.NumInfo = make(map[uint32]*cl.NumInfo)
		}
		robot.userdata.NumInfo[k] = v
	}
	return true
}
