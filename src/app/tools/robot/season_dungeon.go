package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
)

type SeasonDungeonFunc func(*SeasonDungeon)

var gSeasonDungeonActions []SeasonDungeonFunc

func InitSeasonDungeonActions() {
	gSeasonDungeonActions = append(gSeasonDungeonActions, (*SeasonDungeon).C2LSeasonDungeonGetData)
	gSeasonDungeonActions = append(gSeasonDungeonActions, (*SeasonDungeon).C2LSeasonDungeonFight)
	gSeasonDungeonActions = append(gSeasonDungeonActions, (*SeasonDungeon).C2LSeasonDungeonRecvReward)
}

type SeasonDungeon struct {
	*UserLoad
	dungeonData *cl.SeasonDungeon
}

func NewSeasonDungeon(r *Robot) *SeasonDungeon {
	s := &SeasonDungeon{
		UserLoad:    NewUserLoad(r),
		dungeonData: &cl.SeasonDungeon{},
	}
	return s
}

// 断线重连重置数据
func (s *SeasonDungeon) reset() {
	s.load.reset()

	//edit here
}

func (s *SeasonDungeon) RandAction() {
	switch s.load.state() {
	case dataLoadNone:
		s.C2LInit()
	case dataLoading:
		s.C2LRetryInit()
	default:
		if length := len(gSeasonDungeonActions); length != 0 {
			gSeasonDungeonActions[s.u.rd.Intn(length)](s)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (s *SeasonDungeon) C2LInit() {
	s.load.Lock()
	s.load.setState(dataLoading)

	s.C2LSeasonDungeonGetData()
	//s.C2LSeasonDungeonFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (s *SeasonDungeon) L2CInitRet(ret uint32) {
	if s.load.state() == dataLoading {
		s.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			s.load.setState(dataLoaded)
		} else {
			s.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (s *SeasonDungeon) C2LRetryInit() {
	if s.load.Lock() {
		//s.C2LSeasonDungeonFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (s *SeasonDungeon) C2LSeasonDungeonGetData() {
	cmsg := &cl.C2L_SeasonDungeonGetData{}
	//edit here
	s.u.SendCmd(uint32(cl.ID_MSG_C2L_SeasonDungeonGetData), cmsg)
}

func (s *SeasonDungeon) L2CSeasonDungeonGetData(recv *cl.L2C_SeasonDungeonGetData) {
	l4g.Debug("[SeasonDungeon] %s L2CSeasonDungeonGetData ret:%s, recv:%+v",
		s.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	s.dungeonData = recv.Data
	s.L2CInitRet(recv.Ret)
}

func (s *SeasonDungeon) C2LSeasonDungeonFight() {
	cmsg := &cl.C2L_SeasonDungeonFight{}
	//edit here
	dungeonInfo := goxml.GetData().SeasonDungeonInfoM.GetNextInfo(s.dungeonData.DungeonId, s.u.SeasonId())
	if dungeonInfo == nil {
		l4g.Errorf("[SeasonDungeon] uid:%d cur dungeon:%d C2LSeasonDungeonFight get Next seasonDungeon failed", s.u.ID(), s.dungeonData.DungeonId)
		return
	}

	if s.u.SeasonLevel() < dungeonInfo.UnLockLevel {
		l4g.Errorf("[SeasonDungeon] uid:%d cur season Level:%d C2LSeasonDungeonFight dungeon season level:%d", s.u.ID(), s.u.SeasonLevel(), dungeonInfo.UnLockLevel)
		return
	}

	cmsg.DungeonId = dungeonInfo.Id
	s.u.SendCmd(uint32(cl.ID_MSG_C2L_SeasonDungeonFight), cmsg)
}

func (s *SeasonDungeon) L2CSeasonDungeonFight(recv *cl.L2C_SeasonDungeonFight) {
	l4g.Debug("[SeasonDungeon] %s L2CSeasonDungeonFight ret:%s, recv:%+v",
		s.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	if recv.Win {
		s.dungeonData.DungeonId = recv.DungeonId
	}
	//edit here
}

func (s *SeasonDungeon) C2LSeasonDungeonRecvReward() {
	cmsg := &cl.C2L_SeasonDungeonRecvReward{}
	//edit here
	tasks := goxml.GetData().SeasonDungeonRewardInfoM.GetSeasonTask(s.u.SeasonId())
	var tasksIds []uint32
	for _, task := range tasks {
		if task == nil {
			continue
		}

		progress, exist := s.dungeonData.TaskProgress[task.TypeId]
		if !exist {
			continue
		}

		if s.u.seasonDungeon.dungeonData.ReceiveAwarded == nil {
			s.u.seasonDungeon.dungeonData.ReceiveAwarded = make(map[uint32]bool)
		}

		if !s.u.seasonDungeon.dungeonData.ReceiveAwarded[task.Id] && uint64(task.Value) >= progress.Progress {
			tasksIds = append(tasksIds, task.Id)
		}
	}

	if len(tasksIds) > 0 {
		cmsg.TaskIds = tasksIds
		s.u.SendCmd(uint32(cl.ID_MSG_C2L_SeasonDungeonRecvReward), cmsg)
	}
}

func (s *SeasonDungeon) L2CSeasonDungeonRecvReward(recv *cl.L2C_SeasonDungeonRecvReward) {
	l4g.Debug("[SeasonDungeon] %s L2CSeasonDungeonRecvReward ret:%s, recv:%+v",
		s.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	s.dungeonData = recv.SeasonDungeon
}

func (s *SeasonDungeon) L2CSeasonDungeonUpdateTask(recv *cl.L2C_SeasonDungeonUpdateTask) {
	l4g.Debug("[SeasonDungeon] %s L2CSeasonDungeonUpdateTask ret:%s, recv:%+v",
		s.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	if s.dungeonData.TaskProgress == nil {
		s.dungeonData.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	for id, v := range recv.Progress {
		s.dungeonData.TaskProgress[id] = v.Clone()
	}
}
