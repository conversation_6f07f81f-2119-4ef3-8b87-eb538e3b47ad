package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type EmblemFunc func(*Emblem)

var gEmblemActions []EmblemFunc

func InitEmblemActions() {
	gEmblemActions = append(gEmblemActions, (*Emblem).C2LEmblemWear)
	gEmblemActions = append(gEmblemActions, (*Emblem).C2LEmblemLevelUp)
	gEmblemActions = append(gEmblemActions, (*Emblem).C2LEmblemDecompose)
}

type Emblem struct {
	*UserLoad
	emblems           []*cl.EmblemInfo
	posEmblem         map[uint32][]*cl.EmblemInfo                       //pos
	collectionEmblems map[uint32]map[uint32]map[uint32][]*cl.EmblemInfo //suitId hero pos cl.emblem
	reqPos            []uint32
}

func NewEmblem(r *Robot) *Emblem {
	e := &Emblem{
		UserLoad: NewUserLoad(r),
	}
	return e
}

// 断线重连重置数据
func (e *Emblem) reset() {
	e.load.reset()

	//edit here
}

func (e *Emblem) RandAction() {
	switch e.load.state() {
	case dataLoadNone:
		e.C2LInit()
	case dataLoading:
		e.C2LRetryInit()
	default:
		if length := len(gEmblemActions); length != 0 {
			gEmblemActions[e.u.rd.Intn(length)](e)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (e *Emblem) C2LInit() {
	e.load.Lock()
	e.load.setState(dataLoading)
	awards := make([]*cl.Resource, 0, 40)
	if !e.u.CheckHero() {
		awards = append(awards, e.u.GetRandHero(10)...)
	}

	e.checkFormation(uint32(common.FORMATION_ID_FI_DUNGEON))
	e.C2LEmblemGet()
}

func (e *Emblem) checkFormation(id uint32) bool {
	if _, exist := e.u.formations[id]; !exist {
		e.u.SetTopFormation(id)
		return false
	}
	return true
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (e *Emblem) L2CInitRet(ret uint32) {
	if e.load.state() == dataLoading {
		e.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			e.load.setState(dataLoaded)
		} else {
			e.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (e *Emblem) C2LRetryInit() {
	if e.load.Lock() {
		if len(e.u.heroes) == 0 {
			e.load.UnLock()
			return
		}
		if len(e.emblems) == 0 {
			e.load.UnLock()
			return
		}
		if !e.checkFormation(uint32(common.FORMATION_ID_FI_DUNGEON)) {
			e.load.UnLock()
			return
		}
		e.C2LEmblemGet() //TODO:修改为初始化请求函数，比如带有Info的函数
	}
}

func (e *Emblem) GetByID(emblemID uint64) *cl.EmblemInfo {
	for _, emblem := range e.emblems {
		if emblem.Id == emblemID {
			return emblem
		}
	}
	return nil
}

func (e *Emblem) AddByResource(id uint64, sysID uint32, attrs []*cl.Attr) {
	if id == 0 {
		return
	}
	emblem := &cl.EmblemInfo{
		Id:    id,
		SysId: sysID,
		Level: 1,
	}
	for _, attr := range attrs {
		switch attr.Type {
		case uint32(common.RESOURCE_ATTR_TYPE_RESAT_ADDITIVE_HERO):
			emblem.AdditiveHero = uint32(attr.Value)
		case uint32(common.RESOURCE_ATTR_TYPE_RESAT_SKILL):
			emblem.SkillId = uint32(attr.Value)
		}
	}
	e.add(emblem)
}

func (e *Emblem) add(emblem *cl.EmblemInfo) {
	var change bool
	for _, em := range e.emblems {
		if em.Id == emblem.Id {
			em = emblem
			change = true
		}
	}
	if !change {
		e.emblems = append(e.emblems, emblem)
	}
	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.SysId)

	if emblemInfo == nil {
		l4g.Errorf("config emblem_info not found:%d", emblem.SysId)
		return
	}
	if len(e.posEmblem) == 0 {
		e.posEmblem = make(map[uint32][]*cl.EmblemInfo)
	}
	_, exist := e.posEmblem[emblemInfo.Pos]
	if !exist {
		e.posEmblem[emblemInfo.Pos] = make([]*cl.EmblemInfo, 0, 8)
	}
	e.posEmblem[emblemInfo.Pos] = append(e.posEmblem[emblemInfo.Pos], emblem)
	for _, em := range e.posEmblem[emblemInfo.Pos] {
		if em.Id == emblem.Id {
			em = emblem
		}
	}

	if len(e.collectionEmblems) == 0 {
		e.collectionEmblems = make(map[uint32]map[uint32]map[uint32][]*cl.EmblemInfo)
	}
	_, exist = e.collectionEmblems[emblem.AdditiveHero]
	if !exist {
		e.collectionEmblems[emblem.AdditiveHero] = make(map[uint32]map[uint32][]*cl.EmblemInfo)
	}
	_, exist = e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill]
	if !exist {
		e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill] = make(map[uint32][]*cl.EmblemInfo)
	}
	_, exist = e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos]
	if !exist {
		e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos] = make([]*cl.EmblemInfo, 0, 8)
	}
	emblemSlice := e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos]
	emblemSlice = append(emblemSlice, emblem)
	for _, em := range emblemSlice {
		if em.Id == emblem.Id {
			em = emblem
		}
	}
	e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos] = emblemSlice
}

func (e *Emblem) Remove(id uint64) {
	var emblem *cl.EmblemInfo

	for i, em := range e.emblems {
		if em.Id == id {
			emblem = em
			e.emblems = append(e.emblems[:i], e.emblems[i+1:]...)
			break
		}
	}
	if emblem == nil {
		return
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.SysId)
	if emblemInfo == nil {
		l4g.Errorf("config emblem_info not found:%d", emblem.SysId)
		return
	}

	if emblem == nil {
		return
	}

	if len(e.posEmblem) == 0 {
		return
	}

	_, exist := e.posEmblem[emblemInfo.Pos]
	if !exist {
		return
	}

	for i, em := range e.posEmblem[emblemInfo.Pos] {
		if em.Id == id {
			emblem = em
			e.posEmblem[emblemInfo.Pos] = append(e.posEmblem[emblemInfo.Pos][:i], e.posEmblem[emblemInfo.Pos][i+1:]...)
			break
		}
	}

	if len(e.collectionEmblems) == 0 {
		return
	}

	_, exist = e.collectionEmblems[emblem.AdditiveHero]
	if !exist {
		return
	}

	_, exist = e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill]
	if !exist {
		return
	}

	emblemSlice, exist := e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos]
	if !exist {
		return
	}

	for i, em := range emblemSlice {
		if em.Id == id {
			emblem = em
			emblemSlice = append(emblemSlice[:i], emblemSlice[i+1:]...)
			break
		}
	}
	e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos] = emblemSlice

}

func (e *Emblem) Update(emblem *cl.EmblemInfo) {
	e.emblems = append(e.emblems, emblem)
	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.SysId)

	if emblemInfo == nil {
		l4g.Errorf("config emblem_info not found:%d", emblem.SysId)
		return
	}
	if len(e.posEmblem) == 0 {
		e.posEmblem = make(map[uint32][]*cl.EmblemInfo)
	}
	_, exist := e.posEmblem[emblemInfo.Pos]
	if !exist {
		e.posEmblem[emblemInfo.Pos] = make([]*cl.EmblemInfo, 0, 8)
	}
	for i, em := range e.posEmblem[emblemInfo.Pos] {
		if em.Id == emblem.Id {
			e.posEmblem[emblemInfo.Pos][i] = emblem
			break
		}
	}

	if len(e.collectionEmblems) == 0 {
		e.collectionEmblems = make(map[uint32]map[uint32]map[uint32][]*cl.EmblemInfo)
	}
	_, exist = e.collectionEmblems[emblem.AdditiveHero]
	if !exist {
		e.collectionEmblems[emblem.AdditiveHero] = make(map[uint32]map[uint32][]*cl.EmblemInfo)
	}
	_, exist = e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill]
	if !exist {
		e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill] = make(map[uint32][]*cl.EmblemInfo)
	}
	_, exist = e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos]
	if !exist {
		e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos] = make([]*cl.EmblemInfo, 0, 8)
	}

	for i, em := range e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos] {
		if em.Id == emblem.Id {
			e.collectionEmblems[emblem.AdditiveHero][emblemInfo.SuitSkill][emblemInfo.Pos][i] = emblem
			break
		}
	}
}

func (e *Emblem) resetRandPos() {
	e.reqPos = []uint32{goxml.EmblemPos1, goxml.EmblemPos2, goxml.EmblemPos3, goxml.EmblemPos4}
}

func (e *Emblem) C2LEmblemGet() {
	cmsg := &cl.C2L_EmblemGet{}

	e.u.SendCmd(uint32(cl.ID_MSG_C2L_EmblemGet), cmsg)
}

func (e *Emblem) L2CEmblemGet(recv *cl.L2C_EmblemGet) {
	l4g.Debug("[Emblem] %s L2CEmblemGet ret:%s, recv:%+v",
		e.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	for _, em := range recv.Emblems {
		e.add(em)
	}
	e.L2CInitRet(recv.Ret)
	l4g.Infof("emblem get:%s, %v", e.u.UUID(), e.emblems)
}

func (e *Emblem) C2LEmblemWear() {
	cmsg := &cl.C2L_EmblemWear{}
	success := e.u.RandomHeroWearEmblem(cmsg)
	if !success {
		return
	}
	e.u.SendCmd(uint32(cl.ID_MSG_C2L_EmblemWear), cmsg)
}

func (e *Emblem) L2CEmblemWear(recv *cl.L2C_EmblemWear) {
	l4g.Debug("[Emblem] %s L2CEmblemWear ret:%s, recv:%+v",
		e.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	for _, emblem := range recv.Emblems {
		e.Update(emblem)
	}
	for _, hero := range recv.Heroes {
		for _, h := range e.u.heroes {
			if h.Data.Id == hero.Data.Id {
				*h = *hero
			}
		}
	}
	//edit here
}

func (e *Emblem) C2LEmblemLevelUp() {
	//edit here
	var em *cl.EmblemInfo

	index := e.u.rd.RandBetween(0, len(e.emblems)-1)

	emblem := e.emblems[index]
	info := goxml.GetData().EmblemInfoM.Index(emblem.SysId)
	if emblem.Level+1 > info.LevelMax {
		return
	}
	if goxml.GetData().EmblemLevelInfoM.Index(info.LevelIndex, emblem.Level+1) != nil {
		em = emblem
	}

	if em == nil {
		l4g.Errorf("C2LEmblemLevelUp found no valid emblem")
		return
	}
	CLevelUpInfos := make([]*cl.EmblemLevelUpInfo, 0, 1)
	CLevelUpInfos = append(CLevelUpInfos, &cl.EmblemLevelUpInfo{
		Id:           em.Id,
		TargetsLevel: em.Level + 1,
	})
	cmsg := &cl.C2L_EmblemLevelUp{
		LevelUpInfos: CLevelUpInfos,
	}
	e.u.SendCmd(uint32(cl.ID_MSG_C2L_EmblemLevelUp), cmsg)
}

func (e *Emblem) L2CEmblemLevelUp(recv *cl.L2C_EmblemLevelUp) {
	l4g.Debug("[Emblem] %s L2CEmblemLevelUp ret:%s, recv:%+v",
		e.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	emblem := e.GetByID(recv.Emblem[0].Id)
	emblem.Level = recv.Emblem[0].Level
	l4g.Infof("user:%s emblem levelup:%v", e.u.UUID(), *emblem)
	//edit here
}

func (e *Emblem) C2LEmblemDecompose() {
	cmsg := &cl.C2L_EmblemDecompose{}
	//edit here
	if len(e.emblems) == 0 {
		return
	}

	var em *cl.EmblemInfo
	for _, emblem := range e.emblems {
		if emblem.Hid > 0 {
			continue
		}
		if goxml.GetData().EmblemInfoM.GetRare(emblem.SysId) >= goxml.GetData().ConfigInfoM.EmblemDeComposeRareLimit {
			continue
		}
		em = emblem
		break
	}
	if em == nil {
		//e.C2LEmblemWear()
		return
	}
	cmsg.Ids = append(cmsg.Ids, em.Id)
	e.u.SendCmd(uint32(cl.ID_MSG_C2L_EmblemDecompose), cmsg)
}

func (e *Emblem) L2CEmblemDecompose(recv *cl.L2C_EmblemDecompose) {
	l4g.Debug("[Emblem] %s L2CEmblemDecompose ret:%s, recv:%+v",
		e.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	for _, id := range recv.Ids {
		e.Remove(id)
	}
	l4g.Infof("get awards res :%+v", recv.Awards)
	//edit here
}
