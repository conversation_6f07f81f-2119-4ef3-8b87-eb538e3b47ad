package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotDispatchCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DispatchTasks), L2CDispatchTasksCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DispatchReceiveTask), L2CDispatchReceiveTaskCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DispatchGetAwards), L2CDispatchGetAwardsCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_DispatchRefreshTask), L2CDispatchRefreshTaskCommand{})
}

type L2CDispatchTasksCommand struct {
}

func (c L2CDispatchTasksCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_DispatchTasks{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDispatchTasksCommand unmarshal error: %s", err)
		return false
	}
	robot.dispatch.L2CDispatchTasks(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CDispatchReceiveTaskCommand struct {
}

func (c L2CDispatchReceiveTaskCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_DispatchReceiveTask{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDispatchReceiveTaskCommand unmarshal error: %s", err)
		return false
	}
	robot.dispatch.L2CDispatchReceiveTask(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CDispatchGetAwardsCommand struct {
}

func (c L2CDispatchGetAwardsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_DispatchGetAwards{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDispatchGetAwardsCommand unmarshal error: %s", err)
		return false
	}
	robot.dispatch.L2CDispatchGetAwards(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CDispatchRefreshTaskCommand struct {
}

func (c L2CDispatchRefreshTaskCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_DispatchRefreshTask{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CDispatchRefreshTaskCommand unmarshal error: %s", err)
		return false
	}
	robot.dispatch.L2CDispatchRefreshTask(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
