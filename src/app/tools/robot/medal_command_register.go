package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotMedalCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MedalGetData), L2CMedalGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MedalReceiveAward), L2CMedalReceiveAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MedalUpdate), L2CMedalUpdateCommand{})
}

type L2CMedalGetDataCommand struct {
}

func (c L2CMedalGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MedalGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMedalGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.medal.L2CMedalGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMedalReceiveAwardCommand struct {
}

func (c L2CMedalReceiveAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MedalReceiveAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMedalReceiveAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.medal.L2CMedalReceiveAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMedalUpdateCommand struct {
}

func (c L2CMedalUpdateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MedalUpdate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMedalUpdateCommand unmarshal error: %s", err)
		return false
	}
	robot.medal.L2CMedalUpdate(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
