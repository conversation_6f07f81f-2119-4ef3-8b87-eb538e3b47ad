package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotCalPowerCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroTestCalPower), L2CHeroTestCalPowerCommand{})
}

type L2CHeroTestCalPowerCommand struct {
}

func (c L2CHeroTestCalPowerCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroTestCalPower{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroTestCalPowerCommand unmarshal error: %s", err)
		return false
	}
	robot.calPower.L2CHeroTestCalPower(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
