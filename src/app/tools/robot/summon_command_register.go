package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotSummonCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_Summon), L2CSummonCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SummonGetData), L2CSummonGetDataCommand{})
}

type L2CSummonCommand struct {
}

func (c L2CSummonCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_Summon{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSummonCommand unmarshal error: %s", err)
		return false
	}
	robot.summon.L2CSummon(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSummonGetDataCommand struct{}

func (c L2CSummonGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SummonGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSummonGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.summon.L2CSummonGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
