package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotItemCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_UseItem), L2CUseItemCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SellItem), L2CSellItemCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_ItemSelect), L2CItemSelectCommand{})
}

type L2CUseItemCommand struct {
}

func (c L2CUseItemCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_UseItem{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CUseItemCommand unmarshal error: %s", err)
		return false
	}
	robot.item.L2CUseItem(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSellItemCommand struct {
}

func (c L2CSellItemCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SellItem{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSellItemCommand unmarshal error: %s", err)
		return false
	}
	robot.item.L2CSellItem(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CItemSelectCommand struct {
}

func (c L2CItemSelectCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_ItemSelect{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CItemSelectCommand unmarshal error: %s", err)
		return false
	}
	robot.item.L2CItemSelect(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
