package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"time"

	l4g "github.com/ivanabc/log4go"
)

type SeasonLinkFunc func(*SeasonLink)

var gSeasonLinkActions []SeasonLinkFunc

func InitSeasonLinkActions() {
	gSeasonLinkActions = append(gSeasonLinkActions, (*SeasonLink).C2LSeasonLinkGetData)
}

type SeasonLink struct {
	*UserLoad
	data *cl.SeasonLink
}

func NewSeasonLink(r *Robot) *SeasonLink {
	s := &SeasonLink{
		UserLoad: NewUserLoad(r),
		data:     &cl.SeasonLink{},
	}
	return s
}

// 激活英雄羁绊
func (s *SeasonLink) ActivateHeroLink(heroSysId uint32) {
	s.data.ActivatedHeroes = append(s.data.ActivatedHeroes, heroSysId)
}

// 获取赛季羁绊数据
func (s *SeasonLink) Flush() *cl.SeasonLink {
	flushData := &cl.SeasonLink{}
	for h := range s.data.ActivatedHeroes {
		flushData.ActivatedHeroes = append(flushData.ActivatedHeroes, uint32(h))
	}
	flushData.Monuments = s.FlushMonuments()

	return flushData
}

func (s *SeasonLink) FlushMonuments() []*cl.SeasonLinkMonument {
	seasonId := goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix())
	if seasonId == 0 {
		return nil
	}

	var monuments []*cl.SeasonLinkMonument
	curSeasonMonumentInfo := goxml.GetData().SeasonLinkMonumentInfoM.GetRecordsBySeasonId(seasonId)
	for _, v := range curSeasonMonumentInfo {
		monument := s.getMonumentById(v.Id)
		if monument == nil {
			continue
		}
		monuments = append(monuments, monument)
	}
	return monuments
}

func (s *SeasonLink) getMonumentById(monumentId uint32) *cl.SeasonLinkMonument {
	for _, m := range s.data.Monuments {
		if m.MonumentSysId == monumentId {
			return m
		}
	}
	monument := s.newMonumentById(monumentId)
	s.data.Monuments = append(s.data.Monuments, monument)
	return monument
}

func (s *SeasonLink) newMonumentById(monumentId uint32) *cl.SeasonLinkMonument {
	monumentInfo := goxml.GetData().SeasonLinkMonumentRareInfoM.GetMinLevelUniqueRecordByMonumentId(monumentId)
	if monumentInfo == nil {
		l4g.Errorf("newMonumentById: invalid monumentId %d", monumentId)
	}
	monumentInitUniqueLevel := monumentInfo.LevelUnique

	// 丰碑
	monument := &cl.SeasonLinkMonument{
		MonumentSysId: monumentId,
		UniqueLevel:   monumentInitUniqueLevel,
	}
	// 符石
	var runes []*cl.MonumentRune
	beginPos := goxml.GetData().SeasonLinkRuneInfoM.GetMinPositionByMonumentId(monumentId)
	endPos := goxml.GetData().SeasonLinkRuneInfoM.GetMaxPositionByMonumentId(monumentId)
	for i := beginPos; i <= endPos; i++ {
		runeInfo := goxml.GetData().SeasonLinkRuneInfoM.GetRecordByMonumentIdPositionUniqueLevel(monumentId, i, monumentInitUniqueLevel)
		if runeInfo == nil {
			l4g.Error("newMonumentById: no runeInfo. monumentId %d i %d", monumentId, i)
			continue
		}
		runes = append(runes, &cl.MonumentRune{
			Pos:       i,
			RuneSysId: runeInfo.Id,
		})
	}
	if len(runes) > 0 {
		monument.Runes = runes
	}

	return monument
}

// 断线重连重置数据
func (s *SeasonLink) reset() {
	s.load.reset()

	//edit here
}

func (s *SeasonLink) RandAction() {
	switch s.load.state() {
	case dataLoadNone:
		s.C2LInit()
	case dataLoading:
		s.C2LRetryInit()
	default:
		if length := len(gSeasonLinkActions); length != 0 {
			gSeasonLinkActions[s.u.rd.Intn(length)](s)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (s *SeasonLink) C2LInit() {
	s.load.Lock()
	s.load.setState(dataLoading)

	s.C2LSeasonLinkGetData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (s *SeasonLink) L2CInitRet(ret uint32) {
	if s.load.state() == dataLoading {
		s.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			s.load.setState(dataLoaded)
		} else {
			s.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (s *SeasonLink) C2LRetryInit() {
	if s.load.Lock() {
	}
}

func (s *SeasonLink) C2LSeasonLinkGetData() {
	cmsg := &cl.C2L_SeasonLinkGetData{}
	//edit here
	s.u.SendCmd(uint32(cl.ID_MSG_C2L_SeasonLinkGetData), cmsg)
}

func (s *SeasonLink) L2CSeasonLinkGetData(recv *cl.L2C_SeasonLinkGetData) {
	l4g.Debug("[SeasonLink] %s L2CSeasonLinkGetData ret:%s, recv:%+v",
		s.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	s.data = s.u.seasonLink.Flush()
	l4g.Debugf("[SeasonLink] %d L2CSeasonLinkGetData data:%+v", s.u.ID(), s.data)

	s.L2CInitRet(recv.Ret)
}

func (s *SeasonLink) C2LSeasonLinkActivate() {
	cmsg := &cl.C2L_SeasonLinkActivate{}
	//edit here
	heroes := goxml.GetData().SeasonLinkInfoM.GetHeroIdRecordMapBySeasonId(goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix()))
	if len(heroes) == 0 {
		return
	}
	cmsg.HeroSysId = heroes[0].HeroId
	s.u.SendCmd(uint32(cl.ID_MSG_C2L_SeasonLinkActivate), cmsg)
}

func (s *SeasonLink) L2CSeasonLinkActivate(recv *cl.L2C_SeasonLinkActivate) {
	l4g.Debug("[SeasonLink] %s L2CSeasonLinkActivate ret:%s, recv:%+v",
		s.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	s.u.seasonLink.ActivateHeroLink(recv.HeroSysId)
}
