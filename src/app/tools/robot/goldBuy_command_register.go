package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGoldBuyCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GoldBuyGet), L2CGoldBuyGetCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GoldBuyGetGold), L2CGoldBuyGetGoldCommand{})
}

type L2CGoldBuyGetCommand struct {
}

func (c L2CGoldBuyGetCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GoldBuyGet{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGoldBuyGetCommand unmarshal error: %s", err)
		return false
	}
	robot.goldBuy.L2CGoldBuyGet(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGoldBuyGetGoldCommand struct {
}

func (c L2CGoldBuyGetGoldCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GoldBuyGetGold{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGoldBuyGetGoldCommand unmarshal error: %s", err)
		return false
	}
	robot.goldBuy.L2CGoldBuyGetGold(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
