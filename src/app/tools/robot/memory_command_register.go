package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotMemoryCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MemoryLatest), L2CMemoryLatestCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_MemoryUnlock), L2CMemoryUnlockCommand{})
}

type L2CMemoryLatestCommand struct {
}

func (c L2CMemoryLatestCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MemoryLatest{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMemoryLatestCommand unmarshal error: %s", err)
		return false
	}
	robot.memory.L2CMemoryLatest(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CMemoryUnlockCommand struct {
}

func (c L2CMemoryUnlockCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_MemoryUnlock{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CMemoryUnlockCommand unmarshal error: %s", err)
		return false
	}
	robot.memory.L2CMemoryUnlock(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
