package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type ArenaFunc func(*Arena)

var gArenaActions []ArenaFunc

func InitArenaActions() {
	// gArenaActions = append(gArenaActions, (*Arena).C2LArenaInfo)
	gArenaActions = append(gArenaActions, (*Arena).C2LArenaRefresh)
	gArenaActions = append(gArenaActions, (*Arena).C2LArenaFight)
	gArenaActions = append(gArenaActions, (*Arena).C2LArenaLogList)
	gArenaActions = append(gArenaActions, (*Arena).C2LArenaLike)
	gArenaActions = append(gArenaActions, (*Arena).C2LArenaRank)
	gArenaActions = append(gArenaActions, (*Arena).C2LArenaRecvAward)
}

type opInfo struct {
	id    uint64
	score uint32
}

type Arena struct {
	*UserLoad
	opData    []*opInfo
	rankList  []uint64
	refreshTm int64
}

func (a *Arena) randomTaskID() []uint32 {
	count := a.u.rd.Intn(3)
	ret := make([]uint32, 0, count)
	ret = append(ret, uint32(a.u.rd.RandBetween(1, int(goxml.GetData().ArenaDivisionTaskInfoM.MaxTaskID))))
	return ret
}

func (a *Arena) setRefreshTm() {
	a.refreshTm = time.Now().Unix()
}

func (a *Arena) setOpponents(opponents []*cl.ArenaUserInfo) {
	if len(opponents) > 0 {
		opData := make([]*opInfo, 0, len(opponents))
		for _, op := range opponents {
			opData = append(opData, &opInfo{
				id:    op.User.Id,
				score: op.Score,
			})
		}
		a.opData = opData
	}
}

func (a *Arena) setRankUserList(list []*cl.ArenaRankInfo) {
	if len(list) > 0 {
		a.rankList = make([]uint64, 0, 130)
		for _, v := range list {
			a.rankList = append(a.rankList, v.User.Id)
		}
	}
}

func NewArena(r *Robot) *Arena {
	c := &Arena{
		UserLoad: NewUserLoad(r),
	}
	return c
}

// 断线重连重置数据
func (a *Arena) reset() {
	a.load.reset()

	//edit here
}

func (a *Arena) RandAction() {
	switch a.load.state() {
	case dataLoadNone:
		a.C2LInit()
	case dataLoading:
		a.C2LRetryInit()
	default:
		if length := len(gArenaActions); length != 0 {
			gArenaActions[a.u.rd.Intn(length)](a)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (a *Arena) C2LInit() {
	a.load.Lock()
	a.load.setState(dataLoading)

	//初始数据 - 发放英雄以及其他资源
	/*if len(a.u.heroes) == 0 {
		a.u.InitData()
		a.u.Flush()

		if len(a.u.heroes) == 0 {
			l4g.Error("[Arena] %s InitData failed.", a.u.UUID())
			return
		}
	}*/

	//布阵
	//a.checkInitFormation(7)
	a.C2LArenaInfo()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (a *Arena) L2CInitRet(ret uint32) {
	if a.load.state() == dataLoading {
		a.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			a.load.setState(dataLoaded)
		} else {
			a.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (a *Arena) C2LRetryInit() {
	if a.load.Lock() {
		//a.C2LArenaFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
		a.C2LArenaInfo()
	}
}

func (a *Arena) randOneOpponent() *opInfo {
	if a.opData != nil {
		key := a.u.rd.RandBetween(0, len(a.opData)-1)
		return a.opData[key]
	}
	return nil
}

func (a *Arena) C2LArenaInfo() {
	cmsg := &cl.C2L_ArenaInfo{}

	//edit here
	cmsg.ShowTop3 = true
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArenaInfo), cmsg)
}

func (a *Arena) L2CArenaInfo(recv *cl.L2C_ArenaInfo) {
	l4g.Debug("[Arena] %s L2CArenaInfo ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	if a.opData == nil {
		a.setOpponents(recv.Opponents)
	}
	a.L2CInitRet(recv.Ret)
}

func (a *Arena) C2LArenaRefresh() {
	cmsg := &cl.C2L_ArenaRefresh{}
	//edit here
	if a.refreshTm+int64(goxml.GetData().ArenaConfigInfoM.RefreshCD) > time.Now().Unix() {
		a.C2LArenaLogList()
		return
	}
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArenaRefresh), cmsg)
}

func (a *Arena) L2CArenaRefresh(recv *cl.L2C_ArenaRefresh) {
	l4g.Debug("[Arena] %s L2CArenaRefresh ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.setOpponents(recv.Opponents)
	a.setRefreshTm()
}

func (a *Arena) C2LArenaFight() {
	cmsg := &cl.C2L_ArenaFight{}
	opInfo := a.randOneOpponent()
	if opInfo == nil {
		a.C2LArenaRefresh()
		return
	}

	//检查设置进攻阵容
	//a.checkInitFormation(34)

	cmsg.Id = opInfo.id
	cmsg.Score = opInfo.score
	//edit here
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArenaFight), cmsg)
}

func (a *Arena) L2CArenaFight(recv *cl.L2C_ArenaFight) {
	l4g.Debug("[Arena] %s L2CArenaFight ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *Arena) C2LArenaLogList() {
	cmsg := &cl.C2L_ArenaLogList{}
	//edit here
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArenaLogList), cmsg)
}

func (a *Arena) L2CArenaLogList(recv *cl.L2C_ArenaLogList) {
	l4g.Debug("[Arena] %s L2CArenaLogList ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *Arena) C2LArenaLike() {
	cmsg := &cl.C2L_ArenaLike{}
	//edit here

	if len(a.rankList) == 0 {
		a.C2LArenaRank()
		return
	}

	index := a.u.Rand().Intn(len(a.rankList))
	cmsg.Id = a.rankList[index]
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArenaLike), cmsg)
}

func (a *Arena) L2CArenaLike(recv *cl.L2C_ArenaLike) {
	l4g.Debug("[Arena] %s L2CArenaLike ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (a *Arena) C2LArenaRank() {
	cmsg := &cl.C2L_ArenaRank{}
	//edit here
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArenaRank), cmsg)
}

func (a *Arena) L2CArenaRank(recv *cl.L2C_ArenaRank) {
	l4g.Debug("[Arena] %s L2CArenaRank ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	a.setRankUserList(recv.List)
}

func (a *Arena) C2LArenaRecvAward() {
	cmsg := &cl.C2L_ArenaRecvAward{}
	//edit here
	cmsg.Ids = a.randomTaskID()
	a.u.SendCmd(uint32(cl.ID_MSG_C2L_ArenaRecvAward), cmsg)
}

func (a *Arena) L2CArenaRecvAward(recv *cl.L2C_ArenaRecvAward) {
	l4g.Debug("[Arena] %s L2CArenaRecvAward ret:%s, recv:%+v",
		a.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
