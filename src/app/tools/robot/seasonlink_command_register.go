package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitRobotSeasonLinkCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonLinkGetData), L2CSeasonLinkGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SeasonLinkActivate), L2CSeasonLinkActivateCommand{})
}

type L2CSeasonLinkGetDataCommand struct {
}

func (c L2CSeasonLinkGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonLinkGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonLevelGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonLink.L2CSeasonLinkGetData(recv)
	return recv.Ret == uint32(cret.RET_OK)
}

type L2CSeasonLinkActivateCommand struct {
}

func (c L2CSeasonLinkActivateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SeasonLinkActivate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSeasonLevelActivateCommand unmarshal error: %s", err)
		return false
	}
	robot.seasonLink.L2CSeasonLinkActivate(recv)
	return recv.Ret == uint32(cret.RET_OK)
}
