package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotTaskCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TaskGetInfo), L2CTaskGetInfoCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TaskReceiveAward), L2CTaskReceiveAwardCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_TaskUpdate), L2CTaskUpdateCommand{})
}

type L2CTaskGetInfoCommand struct {
}

func (c L2CTaskGetInfoCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TaskGetInfo{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTaskGetInfoCommand unmarshal error: %s", err)
		return false
	}
	robot.task.L2CTaskGetInfo(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CTaskReceiveAwardCommand struct {
}

func (c L2CTaskReceiveAwardCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TaskReceiveAward{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTaskReceiveAwardCommand unmarshal error: %s", err)
		return false
	}
	robot.task.L2CTaskReceiveAward(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CTaskUpdateCommand struct {
}

func (c L2CTaskUpdateCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_TaskUpdate{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CTaskUpdateCommand unmarshal error: %s", err)
		return false
	}
	robot.task.L2CTaskUpdate(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
