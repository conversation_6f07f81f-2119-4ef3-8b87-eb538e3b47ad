package main

import (
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type MedalFunc func(*Medal)

var gMedalActions []MedalFunc

func InitMedalActions() {
	gMedalActions = append(gMedalActions, (*Medal).C2LMedalGetData)
	//gMedalActions = append(gMedalActions, (*Medal).C2LMedalReceiveAward)
}

type Medal struct {
	*UserLoad
}

func NewMedal(r *Robot) *Medal {
	m := &Medal{
		UserLoad: NewUserLoad(r),
	}
	return m
}

// 断线重连重置数据
func (m *Medal) reset() {
	m.load.reset()

	//edit here
}

func (m *Medal) RandAction() {
	switch m.load.state() {
	case dataLoadNone:
		m.C2LInit()
	case dataLoading:
		m.C2LRetryInit()
	default:
		if length := len(gMedalActions); length != 0 {
			gMedalActions[m.u.rd.Intn(length)](m)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (m *Medal) C2LInit() {
	m.load.Lock()
	m.load.setState(dataLoading)

	//m.C2LMedalFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	m.C2LMedalGetData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (m *Medal) L2CInitRet(ret uint32) {
	if m.load.state() == dataLoading {
		m.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			m.load.setState(dataLoaded)
		} else {
			m.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (m *Medal) C2LRetryInit() {
	if m.load.Lock() {
		//m.C2LMedalFlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
		m.C2LMedalGetData()
	}
}

func (m *Medal) C2LMedalGetData() {
	cmsg := &cl.C2L_MedalGetData{}
	//edit here
	m.u.SendCmd(uint32(cl.ID_MSG_C2L_MedalGetData), cmsg)
}

func (m *Medal) L2CMedalGetData(recv *cl.L2C_MedalGetData) {
	l4g.Debug("[Medal] %s L2CMedalGetData ret:%s, recv:%+v",
		m.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	m.L2CInitRet(recv.Ret)
	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (m *Medal) C2LMedalReceiveAward() {
	cmsg := &cl.C2L_MedalReceiveAward{}
	//edit here
	cmsg.Type = 2
	cmsg.Level = 1
	//cmsg.TaskIds = []uint32{}
	m.u.SendCmd(uint32(cl.ID_MSG_C2L_MedalReceiveAward), cmsg)
}

func (m *Medal) L2CMedalReceiveAward(recv *cl.L2C_MedalReceiveAward) {
	l4g.Debug("[Medal] %s L2CMedalReceiveAward ret:%s, recv:%+v",
		m.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (m *Medal) L2CMedalUpdate(recv *cl.L2C_MedalUpdate) {
	l4g.Debug("[Medal] %s L2CMedalUpdate ret:%s, recv:%+v",
		m.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
