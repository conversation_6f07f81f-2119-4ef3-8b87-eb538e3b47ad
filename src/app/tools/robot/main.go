package main

import (
	"app/goxml"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"gitlab.qdream.com/kit/sea/ctx"
	snet "gitlab.qdream.com/kit/sea/net"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

var (
	flagGateway   = flag.String("gateway", "0.0.0.0:38422", "gateway server address")
	flagNodeID    = flag.Uint64("node", 1, "service node id")
	flagNum       = flag.Int("num", 1, "机器人总数量")
	flagSnum      = flag.Int("snum", 1, "机器人账号计数开始数量，比如1则表示创建uuid:N00000_uuid[1,num]")
	flagTnum      = flag.Int("tnum", 0, "每秒创建机器人数量，默认0表示不限制")
	flagUUID      = flag.String("uuid", "robot", "机器人uuid，将根据数量创建机器人账号为：N00000_uuid[1,num]")
	flagLogDir    = flag.String("log_dir", "../log/", "service log dir")
	flagLogLevel  = flag.String("log_level", "INFO", "log level")
	flagConfigDir = flag.String("config", "../config/", "config file")
	flagHTTP      = flag.String("http", ":9810", "http listen address")
	flagDataPath  = flag.String("data", "../data/", "service data path")
	flagPrefix    = flag.String("prefix", "", "uuid prefix")
	flagMode      = flag.Int("mode", 0, "0-低级测试模式 1-版署建号模式 2-初中高级号测试 3-跟踪协议测试")
	flagStage     = flag.Int("stage", 7, "90-高级号 30-中级号 7-低级号")
	//flagHigh      = flag.Bool("high", false, "建号模式时生成高级账号，否则为中级账号")
	flagSlice = flag.Int("slice", 1, "时间分片，压测时用")
	//flagStage     = flag.Int("stage", 1, "1-建号模式时生成高级账号 2-为中级账号 3-为低级号")
	flagRes = flag.Bool("res", false, "是否给给账号添加配置中资源")
	//flagTraceFile = flag.String("trace_file", "dc.log", "dc log file")
	flagTimer        = flag.Int("timer", 0, "定时退出倒计时（秒）")
	flagAllLogin     = flag.Bool("all_login", true, "是否所有人都登录完才开始执行action,默认是需要的")
	profileAddr      = flag.String("profile_addr", "10.6.6.17:10001", "获取profile的数据")
	flagOpenC2LFlush = flag.Bool("open_c2l_flush", true, "是否开启C2L_Flush,默认是需要的")
	flagOpenPprof    = flag.Bool("open_pprof", true, "是否开启pprof,默认是需要的")
	flagSendResource = flag.Bool("send_resource", true, "是否发送资源,默认是需要的")
	flagOnlyCreate   = flag.Bool("only_create", false, "是否只创建账号（发资源），设置昵称")
	flagSignUpFunc   = flag.Int("sign_up_func", 0, "玩法自动报名 1-跨服竞技场") //使用此参数时，要求send_resource是false，flagOnlyCreate是false
	// flagKeepOnline   = flag.Bool("keep_online", false, "执行完jekins任务后，是否保持在线")
	flagJoinGuild = flag.Bool("join_guild", false, "创号时，是否自动加入公会") //规则：有空闲公会则直接加入，没有空闲公会则创建
)

var (
	gConfig     = new(xmlConfig)
	gRobotM     *RobotM
	gActionM    = NewActionM()
	gCommandRps = NewCommandRps()
)

func init() {
	InitRobotCommand()
}

func main() {
	flag.Parse()

	logFilename := *flagLogDir + "robot.log"
	cfg := util.NewL4GConfig(*flagLogLevel, logFilename)
	cfg = strings.ReplaceAll(cfg, "10M", "50M") //日志文件大一点再生成新文件
	l4g.Global.LoadConfiguration("", []byte(cfg))
	defer l4g.Close()
	if *flagLogLevel != "DEBUG" && *flagLogLevel != "INFO" {
		l4g.Error("错误的日志等级: %s", *flagLogLevel)
		return
	}
	if err := util.LoadConfig(*flagConfigDir+"robot.xml", gConfig); err != nil {
		panic(fmt.Sprintf("load config %v fail: %v", *flagConfigDir, err))
	}
	fmt.Println("load log confg:", cfg)

	if *flagMode == RunningModeTrace {
		//LoadDC(*flagTraceFile)
	}
	LoadConfig()
	gConfig.Init()
	parse.NewReadBufferPool(128)
	parse.MaxWriteBatchBufferSize = 65536 //这个不能设置为1M 2W个就把内存给消耗完了
	parse.CryptoEncode = goxml.GetData().XorKeyM.EncodeNew

	bg := ctx.Background()
	debugSrv := snet.NewDebugServer(*flagHTTP)
	InitHTTPHandle(debugSrv.Handler.(*http.ServeMux))
	httpCtx := bg.CreateChild()
	go func() {
		tmp := httpCtx.CreateChild()
		l4g.Info(debugSrv.ListenAndServe())
		tmp.Finish()
	}()

	gRobotM = NewRobotM(bg.CreateChild())

	gActionM.Init()
	gCommandRps.Reset()
	go gRobotM.Start(uint32(*flagNum), *flagUUID, true)

	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP, syscall.SIGUSR1, os.Interrupt)

	//如果设置了定时时间，则启动定时退出协程
	if *flagTimer != 0 {
		go RunExitTimer(sigs, time.Duration(*flagTimer)*time.Second)
	}

	//如果仅创号，则完成后就退出
	if *flagOnlyCreate {
		for {
			timer := time.NewTimer(time.Second)
			<-timer.C
			if gRobotM.IsAllFinishJob() {
				go RunExitAfterJob(sigs)
				break
			}
		}
	}

	//jenkins执行玩法自动报名
	//此时，flagOnlyCreate必须是false，否则无法准确执行
	if !*flagOnlyCreate && *flagSignUpFunc > 0 {
		for {
			timer := time.NewTimer(time.Second)
			<-timer.C
			if gRobotM.IsAllFinishJob() {
				go RunExitAfterJob(sigs)
				break
			}
		}
	}

QUIT:
	for {
		select {
		case <-bg.Done():
			l4g.Info("all group context exit...")
			break QUIT
		case sig := <-sigs:
			l4g.Info("Signal: %s", sig.String())
			if sig == syscall.SIGUSR1 {
				level := l4g.GetFilterLevel("file")
				if level == l4g.INFO {
					l4g.ChangeFilterLevel("file", l4g.DEBUG)
					l4g.Info("change log level (debug)")
				} else {
					l4g.ChangeFilterLevel("file", l4g.INFO)
					l4g.Info("change log level (info)")
				}
			} else {
				break QUIT
			}
		}
	}
	gRobotM.Close()
	gCommandRps.Dump()

	//close http service
	debugSrv.Close()
	httpCtx.Wait()
	httpCtx.Finish()

	//after all service stoped
	ctx.Stop()
	ctx.Wait()
	l4g.Info("service exit")
}

// 定时退出
func RunExitTimer(sigs chan os.Signal, t time.Duration) {
	timer := time.NewTimer(t)
	<-timer.C
	sigs <- syscall.SIGTERM
	l4g.Info("exit timer:%v", t)
}

// 创号完成后退出
func RunExitAfterJob(sigs chan os.Signal) {
	sigs <- syscall.SIGTERM
	l4g.Info("RunExitAfterJob, add robot:%d")
}
