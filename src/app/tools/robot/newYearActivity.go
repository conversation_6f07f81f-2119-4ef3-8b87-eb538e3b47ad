package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"time"

	l4g "github.com/ivanabc/log4go"
)

type NewYearActivityFunc func(*NewYearActivity)

var gNewYearActivityActions []NewYearActivityFunc

func InitNewYearActivityActions() {
	gNewYearActivityActions = append(gNewYearActivityActions, (*NewYearActivity).C2LNewYearActivityGetData)
	gNewYearActivityActions = append(gNewYearActivityActions, (*NewYearActivity).C2LNewYearActivityLoginAward)
}

type NewYearActivity struct {
	*UserLoad
	newYearActivity *cl.NewYearActivity
}

func NewNewYearActivity(r *Robot) *NewYearActivity {
	n := &NewYearActivity{
		UserLoad: NewUserLoad(r),
		newYearActivity: &cl.NewYearActivity{
			LoginAward: make(map[uint32]bool),
		},
	}
	return n
}

// 断线重连重置数据
func (n *NewYearActivity) reset() {
	n.load.reset()

	//edit here
}

func (n *NewYearActivity) RandAction() {
	switch n.load.state() {
	case dataLoadNone:
		n.C2LInit()
	case dataLoading:
		n.C2LRetryInit()
	default:
		if length := len(gNewYearActivityActions); length != 0 {
			gNewYearActivityActions[n.u.rd.Intn(length)](n)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (n *NewYearActivity) C2LInit() {
	n.load.Lock()
	n.load.setState(dataLoading)
	now := time.Now().Unix()
	newYearInfo := goxml.GetData().NewYearInfoM.GetCurrent(now)
	if newYearInfo == nil {
		return
	}

	n.C2LNewYearActivityGetData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (n *NewYearActivity) L2CInitRet(ret uint32) {
	if n.load.state() == dataLoading {
		n.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			n.load.setState(dataLoaded)
		} else {
			n.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (n *NewYearActivity) C2LRetryInit() {
	if n.load.Lock() {
	}
}

func (n *NewYearActivity) C2LNewYearActivityGetData() {
	cmsg := &cl.C2L_NewYearActivityGetData{}
	//edit here
	n.u.SendCmd(uint32(cl.ID_MSG_C2L_NewYearActivityGetData), cmsg)
}

func (n *NewYearActivity) L2CNewYearActivityGetData(recv *cl.L2C_NewYearActivityGetData) {
	l4g.Debug("[NewYearActivity] %s L2CNewYearActivityGetData ret:%s, recv:%+v",
		n.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	n.newYearActivity = recv.Data
	n.L2CInitRet(recv.Ret)
}

func (n *NewYearActivity) C2LNewYearActivityLoginAward() {
	cmsg := &cl.C2L_NewYearActivityLoginAward{}
	newYearInfo := goxml.GetData().NewYearInfoM.Index(n.newYearActivity.SysId)
	if newYearInfo == nil {
		return
	}
	loginInfo := goxml.GetData().ActivityStoryLoginInfoM.GetRound(newYearInfo.LoginID)
	for _, v := range loginInfo {
		if v == nil {
			continue
		}
		if v.Days <= n.newYearActivity.ContinueLoginDay && !n.newYearActivity.LoginAward[v.LoginId] {
			cmsg.LoginIds = append(cmsg.LoginIds, v.LoginId)
		}
	}
	if len(cmsg.LoginIds) > 0 {
		cmsg.ActId = n.newYearActivity.SysId
	}
	n.u.SendCmd(uint32(cl.ID_MSG_C2L_NewYearActivityLoginAward), cmsg)
}

func (n *NewYearActivity) L2CNewYearActivityLoginAward(recv *cl.L2C_NewYearActivityLoginAward) {
	l4g.Debug("[NewYearActivity] %s L2CNewYearActivityLoginAward ret:%s, recv:%+v",
		n.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	for _, v := range recv.LoginIds {
		n.newYearActivity.LoginAward[v] = true
	}
}
