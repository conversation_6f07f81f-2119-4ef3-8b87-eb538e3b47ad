package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/golang/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotRechargeCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_RechargeGetData), L2CRechargeGetDataCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_RechargeNotify), L2CRechargeNotifyCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_RechargeFirstGiftNotify), L2CRechargeFirstGiftNotifyCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_RechargeSimulation), L2CRechargeSimulationCommand{})
}

type L2CRechargeGetDataCommand struct {
}

func (c L2CRechargeGetDataCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_RechargeGetData{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CRechargeGetDataCommand unmarshal error: %s", err)
		return false
	}
	robot.recharge.L2CRechargeGetData(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CRechargeNotifyCommand struct {
}

func (c L2CRechargeNotifyCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_RechargeNotify{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CRechargeNotifyCommand unmarshal error: %s", err)
		return false
	}
	robot.recharge.L2CRechargeNotify(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CRechargeFirstGiftNotifyCommand struct {
}

func (c L2CRechargeFirstGiftNotifyCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_RechargeFirstGiftNotify{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CRechargeFirstGiftNotifyCommand unmarshal error: %s", err)
		return false
	}
	robot.recharge.L2CRechargeFirstGiftNotify(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CRechargeSimulationCommand struct {
}

func (c L2CRechargeSimulationCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_RechargeSimulation{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CRechargeSimulationCommand unmarshal error: %s", err)
		return false
	}
	robot.recharge.L2CRechargeSimulation(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
