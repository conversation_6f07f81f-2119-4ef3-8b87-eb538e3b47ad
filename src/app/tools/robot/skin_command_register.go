package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/golang/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitRobotSkinCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SkinList), L2CSkinListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_SkinUse), L2CSkinUseCommand{})
}

type L2CSkinListCommand struct {
}

func (c L2CSkinListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SkinList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSkinListCommand unmarshal error: %s", err)
		return false
	}
	robot.skin.L2CSkinList(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CSkinUseCommand struct {
}

func (c L2CSkinUseCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_SkinUse{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CSkinUseCommand unmarshal error: %s", err)
		return false
	}
	robot.skin.L2CSkinUse(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
