package main

import (
	"app/protos/out/bt"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

type BattleFunc func(*Battle)

var gBattleActions []BattleFunc

func InitBattleActions() {
	gBattleActions = append(gBattleActions, (*Battle).TestBattleTPvP)
}

type Battle struct {
	*UserLoad
}

func NewBattle(r *Robot) *Battle {
	b := &Battle{
		UserLoad: NewUserLoad(r),
	}
	return b
}

// 断线重连重置数据
func (b *Battle) reset() {
	b.load.reset()

	//edit here
}

func (b *Battle) RandAction() {
	switch b.load.state() {
	case dataLoadNone:
		b.C2LInit()
	case dataLoading:
		b.C2LRetryInit()
	default:
		if length := len(gBattleActions); length != 0 {
			gBattleActions[b.u.rd.Intn(length)](b)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (b *Battle) C2LInit() {
	b.load.Lock()
	b.load.setState(dataLoading)
	//初始数据 - 发放英雄以及其他资源
	if _, exist := b.u.formations[uint32(common.FORMATION_ID_FI_DUNGEON)]; !exist {
		l4g.Error("[Battle] %s C2LInit failed.", b.u.UUID())
		return
	}
	//这边测试战斗的时候认为一定会生成高级号
	b.load.UnLock()
	b.load.setState(dataLoaded)
}

// 请求协议返回后调用
func (b *Battle) L2CInitRet(ret uint32) {
	if b.load.state() == dataLoading {
		b.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			b.load.setState(dataLoaded)
		} else {
			b.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (b *Battle) C2LRetryInit() {
	if b.load.Lock() {
		b.u.formation.C2LGetFormation()
	}
}

func (b *Battle) TestBattleTPvP() {
	cmsg := &cl.C2L_RobotBattle{
		FuncId: uint32(common.FORMATION_ID_FI_ARENA_DEFENSE),
	}
	b.u.SendCmd(uint32(cl.ID_MSG_C2L_RobotBattle), cmsg)
	l4g.Debugf("[Battle] %s  TestBattleTPvP :%+v", b.u.Name(), cmsg)
}

func (b *Battle) L2CTestBattle(recv *cl.L2C_RobotBattle) {
	l4g.Debugf("[Battle] %s L2C_RobotBattle ret:%s, recv:%+v",
		b.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	if recv.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("%s L2CTestBattle, funcId:%d,  ret error :%d", b.u.Name(), recv.FuncId, recv.Ret)
		return
	}
	l4g.Debugf("[Battle] %s funcId:%d, result:%v", b.u.Name(), recv.FuncId, recv.Win)
}

func (b *Battle) L2CBattleReport(recv *cl.L2C_BattleReport) {
	if recv.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("%s L2CBattleReport ret error :%d", b.u.Name(), recv.Ret)
		return
	}
	var battleReport bt.MultipleTeamsReport
	if err := proto.Unmarshal(recv.Report, &battleReport); err != nil {
		l4g.Errorf("%s L2CBattleReport Unmarshal error :%s", b.u.Name(), err.Error())
		return
	}
	for _, report := range battleReport.Reports {
		bytesData, _ := proto.Marshal(report)
		l4g.Infof("[Battle]%s roundNum:%d, reportSize:%d", b.u.Name(), len(report.Rounds)-1, len(bytesData))
	}
}
