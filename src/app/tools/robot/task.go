package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type TaskFunc func(*Task)

var gTaskActions []TaskFunc

func InitTaskActions() {
	gTaskActions = append(gTaskActions, (*Task).C2LTaskGetInfo)
	gTaskActions = append(gTaskActions, (*Task).C2LTaskReceiveAward)
}

type Task struct {
	*UserLoad
}

func NewTask(r *Robot) *Task {
	a := &Task{
		UserLoad: NewUserLoad(r),
	}
	return a
}

// 断线重连重置数据
func (d *Task) reset() {
	d.load.reset()

	//edit here
}

func (d *Task) RandAction() {
	switch d.load.state() {
	case dataLoadNone:
		d.C2LInit()
	case dataLoading:
		d.C2LRetryInit()
	default:
		if length := len(gTaskActions); length != 0 {
			gTaskActions[d.u.rd.Intn(length)](d)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (d *Task) C2LInit() {
	d.load.Lock()
	d.load.setState(dataLoading)

	d.C2LTaskGetInfo()
}

// 请求协议返回后调用
func (d *Task) L2CInitRet(ret uint32) {
	if d.load.state() == dataLoading {
		d.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			d.load.setState(dataLoaded)
		} else {
			d.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (d *Task) C2LRetryInit() {
	if d.load.Lock() {
		d.C2LTaskGetInfo()
	}
}

func (d *Task) C2LTaskGetInfo() {
	cmsg := &cl.C2L_TaskGetInfo{}
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_TaskGetInfo), cmsg)
}

func (d *Task) L2CTaskGetInfo(recv *cl.L2C_TaskGetInfo) {
	l4g.Debug("[Task] %s L2CTaskGetInfo ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	d.L2CInitRet(recv.Ret)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *Task) C2LTaskReceiveAward() {
	cmsg := &cl.C2L_TaskReceiveAward{}
	ids := make([]uint32, 0, 3)
	for k := range goxml.GetData().TaskInfoM.Datas {
		ids = append(ids, k)
		if len(ids) >= 3 {
			break
		}
	}
	cmsg.Ids = ids
	//edit here
	d.u.SendCmd(uint32(cl.ID_MSG_C2L_TaskReceiveAward), cmsg)
}

func (d *Task) L2CTaskReceiveAward(recv *cl.L2C_TaskReceiveAward) {
	l4g.Debug("[Task] %s L2CTaskReceiveAward ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	d.L2CInitRet(recv.Ret)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}

func (d *Task) L2CTaskUpdate(recv *cl.L2C_TaskUpdate) {
	l4g.Debug("[Task] %s L2CTaskUpdate ret:%s, recv:%+v",
		d.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)
	d.L2CInitRet(recv.Ret)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
}
