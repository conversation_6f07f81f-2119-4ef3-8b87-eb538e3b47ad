package main

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type TowerSeasonFunc func(season *TowerSeason)

var gTowerSeasonActions []TowerSeasonFunc

func InitTowerSeasonActions() {
	gTowerSeasonActions = append(gTowerSeasonActions, (*TowerSeason).C2LTowerSeasonGetData)
	gTowerSeasonActions = append(gTowerSeasonActions, (*TowerSeason).C2LTowerSeasonFight)
	//gTowerSeasonActions = append(gTowerSeasonActions, (*TowerSeason).C2LTowerSeasonRecvAward)
	//gTowerSeasonActions = append(gTowerSeasonActions, (*TowerSeason).C2LTowerSeasonRankList)
	//gTowerSeasonActions = append(gTowerSeasonActions, (*TowerSeason).C2LTowerSeasonRankLike)
}

type TowerSeason struct {
	*UserLoad
	data *cl.TowerSeason
}

func NewTowerSeason(r *Robot) *TowerSeason {
	return &TowerSeason{
		UserLoad: NewUserLoad(r),
	}
}

// 断线重连重置数据
func (t *TowerSeason) reset() {
	t.load.reset()

	//edit here
}

func (t *TowerSeason) RandAction() {
	switch t.load.state() {
	case dataLoadNone:
		t.C2LInit()
	case dataLoading:
		t.C2LRetryInit()
	default:
		if length := len(gTowerSeasonActions); length != 0 {
			gTowerSeasonActions[t.u.rd.Intn(length)](t)
		}
	}
}

// 初始化请求模块数据 并添加流程锁，防止重复拉取
func (t *TowerSeason) C2LInit() {
	t.load.Lock()
	t.load.setState(dataLoading)
	t.C2LTowerSeasonGetData()
}

// 请求协议返回后调用
// TODO:需要手动添加到初始化协议返回，一般是Info协议
func (t *TowerSeason) L2CInitRet(ret uint32) {
	if t.load.state() == dataLoading {
		t.load.UnLock()
		if ret == uint32(cret.RET_OK) {
			t.load.setState(dataLoaded)
		} else {
			t.load.setState(dataLoadNone)
		}
	}
}

// 初始化请求超时 重新发起请求
func (t *TowerSeason) C2LRetryInit() {
	if t.load.Lock() {
	}
}

func (t *TowerSeason) C2LTowerSeasonGetData() {
	cmsg := &cl.C2L_TowerSeasonGetData{}
	//edit here
	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerSeasonGetData), cmsg)
}

func (t *TowerSeason) L2CTowerSeasonGetData(recv *cl.L2C_TowerSeasonGetData) {
	l4g.Debug("[TowerSeason] %s L2CTowerSeasonGetData ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

	//edit here
	t.data = recv.TowerSeason
	t.L2CInitRet(recv.Ret)
}

func (t *TowerSeason) C2LTowerSeasonFight() {
	cmsg := &cl.C2L_TowerSeasonFight{}
	//edit here
	cmsg.Floor = goxml.GetData().TowerSeasonDungeonInfoM.GetNextDungeonId(t.data.Season, t.data.Round, t.data.Floor)
	cmsg.Season = t.data.Season
	cmsg.Round = t.data.Round

	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerSeasonFight), cmsg)
}

func (t *TowerSeason) L2CTowerSeasonFight(recv *cl.L2C_TowerSeasonFight) {
	l4g.Debug("[TowerSeason] %s L2CTowerSeasonFight ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
	t.data.Floor = recv.Floor
}

func (t *TowerSeason) C2LTowerSeasonRecvAward() {
	cmsg := &cl.C2L_TowerSeasonRecvAward{}
	//edit here

	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerSeasonRecvAward), cmsg)
}

func (t *TowerSeason) L2CTowerSeasonRecvAward(recv *cl.L2C_TowerSeasonRecvAward) {
	l4g.Debug("[TowerSeason] %s L2CTowerSeasonRecvAward ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (t *TowerSeason) C2LTowerSeasonRankLike() {
	cmsg := &cl.C2L_TowerSeasonRankLike{}
	//edit here

	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerSeasonRankLike), cmsg)
}

func (t *TowerSeason) L2CTowerSeasonRankLike(recv *cl.L2C_TowerSeasonRankLike) {
	l4g.Debug("[TowerSeason] %s L2CTowerSeasonRankLike ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}
}

func (t *TowerSeason) C2LTowerSeasonRankList() {
	cmsg := &cl.C2L_TowerSeasonRankList{}
	//edit here

	t.u.SendCmd(uint32(cl.ID_MSG_C2L_TowerSeasonRankList), cmsg)
}

func (t *TowerSeason) L2CTowerSeasonRankList(recv *cl.L2C_TowerSeasonRankList) {
	l4g.Debug("[TowerSeason] %s L2CTowerSeasonRankList ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

}

func (t *TowerSeason) L2CTowerSeasonUpdateTask(recv *cl.L2C_TowerSeasonUpdateTask) {
	l4g.Debug("[TowerSeason] %s L2CTowerSeasonUpdateTask ret:%s, recv:%+v",
		t.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

	if recv.Ret != uint32(cret.RET_OK) {
		return
	}

}
