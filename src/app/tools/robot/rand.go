package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"sort"

	l4g "github.com/ivanabc/log4go"
)

var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

func (r *Robot) RandBoolHalf() bool {
	return r.Rand().Intn(100) < 50
}

func (r *Robot) RandBool50() bool {
	return r.Rand().Intn(100) < 50
}

func (r *Robot) RandBool60() bool {
	return r.Rand().Intn(100) < 60
}

func (r *Robot) RandBool90() bool {
	return r.Rand().Intn(100) < 90
}

func (r *Robot) RandUint32() uint32 {
	return r.Rand().Uint32()
}

// 随机uint64 有一定概率随机到自己的id
func (r *Robot) RandUint64() uint64 {
	if r.<PERSON>().Intn(100) < 20 {
		return r.ID()
	} else {
		return r.Rand().Uint64()
	}
}

// 从原切片中，随机取出num个元素，形成新切片
func (r *Robot) RandNewSliceFormSlice(s []uint32, num int) []uint32 {
	if len(s) == 0 || num > len(s) || num == 0 {
		l4g.Errorf("RandNewSliceFormSlice param err, sLen:%d, num:%d", len(s), num)
		return nil
	}
	base := make([]uint32, len(s))
	newSlice := make([]uint32, num)
	copy(base, s)
	for i := 0; i < num; i++ {
		key := r.Rand().Intn(len(base))
		newSlice[i] = base[key]
		base = append(base[:key], base[key+1:]...)
	}
	return newSlice
}

func (r *Robot) RandFromSlice(s []uint32) uint32 {
	idx := r.Rand().Intn(len(s))
	return s[idx]
}

func (r *Robot) RandNormal() bool {
	l4g.Debugf("config rand rate %d", gConfig.RandNormalRate)
	return r.Rand().Intn(10000) < gConfig.RandNormalRate
}

func (r *Robot) RandSliceInt(begin, end int, num int) []int {
	max := end - begin + 1
	s := make([]int, 0, max)
	for i := begin; i <= end; i++ {
		s = append(s, i)
	}
	nums := make([]int, 0, num)
	for i := 0; i < num; i++ {
		p := r.Rand().Intn(max)
		nums = append(nums, s[p])
		s[max-1], s[p] = s[p], s[max-1]
		max = max - 1
	}
	return nums
}

func (r *Robot) RaiseHero(hid uint64) {
	if len(r.heroes) == 0 {
		return
	}
	for i := 0; i < 6; i++ {
		cmsg := &cl.C2L_HeroLevelUp{}
		//edit here
		cmsg.Id = hid
		cmsg.Num = 10
		r.SendCmd(uint32(cl.ID_MSG_C2L_HeroLevelUp), cmsg)
	}
	for i := 0; i < 10; i++ {
		cmsg := &cl.C2L_HeroStageUp{}
		//edit here
		cmsg.Id = hid
		r.SendCmd(uint32(cl.ID_MSG_C2L_HeroStageUp), cmsg)
	}
}

func (r *Robot) SetTopFormation(id uint32) {
	sort.Slice(r.heroes, func(i, j int) bool {
		if r.heroes[i].Data.Stage > r.heroes[j].Data.Stage {
			return true
		} else if r.heroes[i].Data.Stage < r.heroes[j].Data.Stage {
			return false
		}
		return r.heroes[i].Data.Level > r.heroes[j].Data.Level
	})
	_, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(id)
	if teamNum == 0 {
		teamNum = 1
	}

	formation := &cl.Formation{
		Id: id,
	}

	heroSysID := make(map[uint32]struct{}, 10)
	for i := 0; i < teamNum; i++ {
		infos := make([]*cl.FormationInfo, 0, 5)

		for _, h := range r.heroes {
			if len(infos) >= 5 {
				break
			}
			if _, exist := heroSysID[h.Data.SysId]; exist {
				continue
			}
			heroSysID[h.Data.SysId] = struct{}{}
			//if h.Data.Level == 1 {
			//	r.RaiseHero(h.Data.Id)
			//}
			infos = append(infos, &cl.FormationInfo{
				Pos: uint32(len(infos)) + 1,
				Hid: h.Data.Id,
			})
		}
		formation.Teams = append(formation.Teams, &cl.FormationTeamInfo{
			Info: infos,
		})
	}

	cmsg := &cl.C2L_Formation{
		FormationId: id,
		Formation:   formation,
	}
	r.SendCmd(uint32(cl.ID_MSG_C2L_Formation), cmsg)
}

/*
*
布阵， 用正确的数据布阵，上阵英雄数量随机
@param r 机器人对象
@param id 阵容id
@param full 是否上满人
*/
func (r *Robot) SetFormation(id uint32, full bool) {
	maxHero := len(r.heroes)
	maxArtifact := len(r.artifacts)
	maxPos := 5
	maxArtifactPos := 3
	if maxHero < maxPos {
		maxPos = maxHero
	}
	if maxPos < 1 {
		l4g.Error("robot %s SetFormation:%d error, hero empty", r.uuid, id)
		return
	}
	pos := 5
	if !full {
		pos = r.rd.RandBetween(1, maxPos)
	}
	artifactPos := maxArtifactPos
	if maxArtifact < artifactPos {
		artifactPos = maxArtifact
	}
	infos := make([]*cl.FormationInfo, pos)
	{
		heroes := r.GetHeroesByNum(pos, full)
		sPos := r.RandSliceInt(1, pos, pos)
		for k, v := range sPos {
			h := heroes[k]
			infos[k] = &cl.FormationInfo{
				Pos: uint32(v),
				Hid: h.Data.Id,
			}
		}
	}
	team := &cl.FormationTeamInfo{
		Info: infos,
	}
	if artifactPos > 0 {
		artifactInfos := make([]*cl.FormationArtifactInfo, artifactPos)
		artifacts := r.GetArtifactsByNum(artifactPos)
		sPos := r.RandSliceInt(1, 3, artifactPos)
		for k, v := range sPos {
			art := artifacts[k]
			artifactInfos[k] = &cl.FormationArtifactInfo{
				Pos: uint32(v),
				Aid: art.SysId,
			}
		}
		team.Artifacts = artifactInfos
	}
	cmsg := &cl.C2L_Formation{
		FormationId: id,
		Formation:   &cl.Formation{Id: id, Teams: []*cl.FormationTeamInfo{team}},
	}
	r.SendCmd(uint32(cl.ID_MSG_C2L_Formation), cmsg)
}

func (r *Robot) GetHeroesByNum(num int, full bool) []*cl.Hero {
	heroes := make([]*cl.Hero, 0, num)
	sameHero := make(map[uint32]struct{})
	getNum := 0
	if full {
		sort.Slice(r.heroes, func(i, j int) bool {
			return r.heroes[i].Data.Level > r.heroes[j].Data.Level
		})
	} else {
		r.Rand().Shuffle(len(r.heroes), func(i, j int) {
			r.heroes[i], r.heroes[j] = r.heroes[j], r.heroes[i]
		})
	}
	for _, v := range r.heroes {
		if _, exist := sameHero[v.Data.SysId]; !exist {
			heroes = append(heroes, v)
			getNum++
			sameHero[v.Data.SysId] = struct{}{}
			if getNum >= num {
				break
			}

		}
	}
	return heroes
}

func (r *Robot) GetArtifactsByNum(num int) []*cl.Artifact {
	artifacts := make([]*cl.Artifact, 0, num)
	l := len(r.artifacts)
	for i := 0; i < num; i++ {
		p := r.rd.RandBetween(0, l-1)
		artifacts = append(artifacts, r.artifacts[p])
		r.artifacts[l-1], r.artifacts[p] = r.artifacts[p], r.artifacts[l-1]
		l = l - 1
	}
	return artifacts
}

func (r *Robot) RandHeroID() uint64 {
	max := len(r.heroes)
	if max > 0 {
		i := r.Rand().Intn(max)
		return r.heroes[i].Data.Id
	}
	return uint64(0)
}

func (r *Robot) RandHeroIDByNum(used map[uint32]struct{}, num int) []uint64 {
	ids := make([]uint64, 0, num)
	max := len(r.heroes)
	l := max
	if l > 0 {
		for i := 0; i < num; i++ {
			if i >= max {
				break
			}
			index := r.Rand().Intn(l)
			if _, exist := used[r.heroes[index].Data.SysId]; !exist {
				ids = append(ids, r.heroes[index].Data.Id)
				used[r.heroes[index].Data.SysId] = struct{}{}
			} else {
				i--
			}
			r.heroes[index], r.heroes[l-1] = r.heroes[l-1], r.heroes[index]
			l = l - 1
		}
	}
	return ids
}

func (r *Robot) RandArtifactIDByNum(used map[uint32]struct{}, num int) []uint32 {
	ids := make([]uint32, 0, num)
	max := len(r.artifacts)
	l := max
	if l > 0 {
		for i := 0; i < num; i++ {
			if i >= max {
				break
			}
			index := r.Rand().Intn(l)
			if _, exist := used[r.artifacts[index].SysId]; !exist {
				ids = append(ids, r.artifacts[index].SysId)
			} else {
				i--
			}
			r.artifacts[index], r.artifacts[l-1] = r.artifacts[l-1], r.artifacts[index]
			l = l - 1
		}
	}
	return ids
}

/*
func (r *Robot) GetArtifactStageById(id uint32) uint32 {
	for _, v := range r.artifacts {
		if v.SysId == id {
			return v.Stage
		}
	}
	return 0
}*/

func (r *Robot) RandString(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[r.Rand().Intn(len(letterRunes))]
	}
	return string(b)
}

func (r *Robot) CheckFormation(id uint32) bool {
	if _, exist := r.formations[id]; !exist {
		r.SetTopFormation(id)
		return false
	}
	return true
}

func (r *Robot) CheckHero() bool {
	return len(r.heroes) > 0
}

func (r *Robot) RandGemSlot() uint32 {
	max := 2
	i := r.Rand().Intn(max)
	return uint32(i + 1)
}

func (r *Robot) RandomHeroWearEmblem(cmsg *cl.C2L_EmblemWear) bool {
	i := r.rd.RandBetween(0, len(r.heroes)-1)
	hero := r.heroes[i]
	if hero == nil {
		return false
	}

	poses := r.randomReqPos()
	ret := make([]*cl.EmblemInfo, 0, len(poses))

	var suitSkillId uint32
	var setOver bool
	for _, pos := range poses {
		suitIds, exist := r.emblem.collectionEmblems[hero.Data.SysId]
		if exist {
			if setOver {
				posEmblems, exist := r.emblem.collectionEmblems[hero.Data.SysId][suitSkillId]
				if !exist {
					emblem := r.randNormalEmblem(pos)
					ret = append(ret, emblem)
					continue
				}
				emblems, exist := posEmblems[pos]
				if !exist {
					emblem := r.randNormalEmblem(pos)
					ret = append(ret, emblem)
					continue
				}
				index := r.rd.RandBetween(0, len(emblems)-1)
				emblem := emblems[index]
				ret = append(ret, emblem)
			} else {
				for suitId := range suitIds {
					suitSkillId = suitId
					setOver = true
					break
				}
			}
		} else {
			emblem := r.randNormalEmblem(pos)
			ret = append(ret, emblem)
		}
	}
	cmsg.Hid = hero.Data.Id
	cmsg.Type = uint32(common.WEAR_OP_WEAR)
	cmsg.Id = make([]uint64, 0, len(poses))
	for _, em := range ret {
		cmsg.Id = append(cmsg.Id, em.Id)
	}
	return true
}

func (r *Robot) randomReqPos() []uint32 {
	count := r.rd.RandBetween(goxml.EmblemPosNone+1, goxml.EmblemPosMax)

	ret := make([]uint32, 0, count)
	r.emblem.resetRandPos()

	for i := 0; i < count; i++ {
		index := r.rd.RandBetween(0, len(r.emblem.reqPos)-1)
		ret = append(ret, r.emblem.reqPos[index])
		r.emblem.reqPos = append(r.emblem.reqPos[:index], r.emblem.reqPos[index+1:]...)
	}
	return ret
}

func (r *Robot) randNormalEmblem(pos uint32) *cl.EmblemInfo {
	posEmblems, exist := r.emblem.posEmblem[pos]
	if !exist {
		return nil
	}
	index := r.rd.RandBetween(0, len(posEmblems)-1)
	return posEmblems[index]
}
