package main

import (
	"app/logic/helper"
	"app/protos/out/cg"
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"time"

	l4g "github.com/ivanabc/log4go"
	b3core "github.com/magicsea/behavior3go/core"
	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/zebra/parse"
	"gitlab.qdream.com/kit/sea/zebra/tcp"
)

func NewRobot(ctx *ctx.Group, uuid string, blackboard *b3core.Blackboard) *Robot {
	robot := &Robot{
		cont:        ctx,
		uuid:        uuid,
		blackboard:  blackboard,
		messageChan: make(chan *parse.Message, 1),
		itemsBag:    make(map[uint32]uint32),
		tokensBag:   make(map[uint32]uint64),
		msgQ:        make(chan <PERSON><PERSON>, 1024),
		treeActions: make(chan *treeAction),
		rd:          rand.New(time.Now().UnixNano()),
	}
	l4g.Debugf("new robot uuid:%s", uuid)
	return robot
}

type Robot struct {
	act        *actor.Actor
	cont       *ctx.Group
	broker     *tcp.Broker
	uuid       string
	rd         *rand.Rand
	cseq, sseq uint32

	state      int //登录态
	syncCookie uint64

	blackboard *b3core.Blackboard

	respCmdId   uint32
	messageChan chan *parse.Message
	startTm     time.Time

	cryptoState bool

	userdata             *cl.User
	itemsBag             map[uint32]uint32
	fragmentsBag         map[uint32]uint32
	tokensBag            map[uint32]uint64
	artifactFragmentsBag map[uint32]uint32
	heroes               []*cl.Hero
	specialHeroes        []*cl.Hero
	artifacts            []*cl.Artifact
	formations           map[uint32]*cl.Formation

	dungeon *cl.L2C_Dungeon

	talentTreeCul  *cl.TalentTreeCultivate
	talentTreeBase *cl.TalentTreeBase

	seasonDungeon *cl.SeasonDungeon
	seasonLink    *cl.SeasonLink

	goldBuy *cl.GoldBuy
	emblem  *Emblem

	msgQ chan Messager

	treeActions   chan *treeAction
	currentAction *treeAction
}

type Receiver interface{}

type Messager interface {
	Process(Receiver) error
}

func (r *Robot) Init(broker *tcp.Broker) {
	r.broker = broker
	r.emblem = NewEmblem(r)
}

func (r *Robot) Run() {
	r.connect(true)
	// r.act.Run(r)

	ticker := time.NewTicker(10 * time.Second) // 执行一次，tree.Tick()
	defer ticker.Stop()

	for {
		select {
		case <-r.cont.Done():
			r.Exit()
			return
		case action := <-r.treeActions:
			action.Req(r)
			r.currentAction = action
			r.startTm = time.Now()
		case resp := <-r.msgQ:
			if err := resp.Process(r); err != nil {
				l4g.Error("[actor] msg process failed: %+v %s", resp, err)
			}
		case <-ticker.C:
			r.KeepAlive()
		default:
			if r.currentAction != nil {
				if time.Since(r.startTm) > 5*time.Second {
					r.actionRecv(nil)
				}
			}
		}
	}
}

func (r *Robot) connect(first bool) {
	cfg := &tcp.Config{
		MaxReadMsgSize:    65536,
		ReadMsgQueueSize:  0,
		ReadTimeOut:       600,
		MaxWriteMsgSize:   65536,
		WriteMsgQueueSize: 1024,
		WriteTimeOut:      600,
		Address:           *flagGateway,
	}

	if !tcp.ClientServe(r.cont.CreateChild(), r, cfg, 10*time.Second) {
		fmt.Errorf("[robot] connect server error: %s %+v", r.uuid, cfg)
		// r.act.AddTimer(&reconnectTimer{r, first}, int64(r.rd.Intn(10))+5+time.Now().Unix(), 0)
	}
	// r.Login()
}

func (r *Robot) Login(serverId uint64) {
	fmt.Printf("robot %s begin login \n", r.uuid)
	r.SetState(stateLogin)

	ph := &parse.PackHead{
		Cmd:  uint32(10100),
		CSeq: r.cseq,
		SSeq: r.sseq,
	}

	msg := &cg.C2G_Login{
		ServerId:   1000100003,
		Token:      genToken(r.uuid),
		SyncCookie: r.syncCookie,
		Version:    9999999,
	}
	r.Send(ph, msg)

	// r.loginReqTime = time.Now()
	l4g.Debug("%s req login:%v", r.uuid, msg)
}

func (r *Robot) Send(ph *parse.PackHead, msg interface{}) {
	l4g.Debug("robot :%s send ph:%+v, msg:%+v", r.uuid, ph, msg)
	msgs := parse.NewMessageBatch()
	parse.AppendMessageBatch(msgs, ph, msg)
	r.broker.WriteMessages(msgs)
	// r.cmdStat.send(ph.Cmd)
}

func (r *Robot) SetState(state int) {
	r.state = state
}

type responseMessage struct {
	buf []byte
}

func (r *responseMessage) Process(param Receiver) error {
	param.(*Robot).ProcessResponse(r.buf)
	return nil
}

func (r *Robot) Process(buf []byte) {
	// r.act.AddMessage(&responseMessage{buf})
	r.msgQ <- &responseMessage{buf: buf}
}

func (r *Robot) ProcessResponse(buf []byte) {
	ph := new(parse.PackHead)
	parse.DecodePackHead(buf, ph)
	data := buf[parse.PackHeadSize:ph.Length]

	if ph.Cmd == uint32(cg.ID_MSG_G2C_Login) && ph.Flags&parse.MsgFlagCrypto == parse.MsgFlagCrypto {
		r.cryptoState = true
	}

	l4g.Debugf("receiveCmdID:%d respCmdId:%d", ph.Cmd, r.respCmdId)

	if ph.Cmd == uint32(cg.ID_MSG_G2C_KeepAlive) {
		return
	} else if ph.Cmd == r.respCmdId {
		r.actionRecv(data)
	} else {
		r.sseq = ph.SSeq
		l4g.Debug("recv msg head: %+v", ph)
		ret := gRobotCommandM.Dispatcher(r, ph, data)
		if !ret {
			l4g.Errorf("Dispatcher error. cmdID:%d", ph.Cmd)
		}
	}
}

func (r *Robot) actionRecv(data []byte) {
	r.respCmdId = 0

	defer func() {
		r.currentAction = nil
	}()

	select {
	case r.currentAction.Status <- r.currentAction.Resp(r, data):
	case <-r.cont.Done():
		l4g.Infof("close when actionRecv uuid:%s", r.uuid)
		return
	}
	// r.currentAction.Status <- r.currentAction.Resp(r, data)
	// r.currentAction = nil
}

func (r *Robot) Online(cookie uint64, sync uint32) {
	//	l4g.Info("[Robot] online suc:%s delay:%v", r.uuid, time.Since(r.loginReqTime))
	println(time.Now().String(), "online", r.uuid)

	r.syncCookie = cookie
	//if sync == 0 {
	/*	go func() {
		if r.New != nil {
			r.New(r)
		}
	}() */
	//} else {
	r.LoginSuccess()
	//}
}

func (r *Robot) LoginSuccess() {
	r.SetState(stateOnline)
}

func (r *Robot) UUID() string {
	return r.uuid
}

func (r *Robot) Name() string {
	if r.userdata != nil {
		return r.userdata.Name
	}
	return ""
}

func (r *Robot) Close() {
	l4g.Infof("robot:%s close", r.UUID())
}

func (r *Robot) stop() {
	r.cont.Stop()
	l4g.Debugf("[robot] uuid:%s stop", r.UUID())
}

func (r *Robot) wait() {
	r.cont.Wait()
	r.cont.Finish()
}

func (r *Robot) Exit() {
	select {
	case action := <-r.treeActions:
		r.currentAction = action
		r.startTm = time.Now()
	default:
	}
	if r.currentAction != nil {
		r.actionRecv(nil)
	}
	r.stop()
	r.wait()
}

func (r *Robot) KeepAlive() {
	if r.state != stateOnline {
		return
	}
	msg := &cl.C2L_KeepAlive{
		Now: time.Now().UnixNano() / 1000000,
	}
	r.SendCmd(uint32(cl.ID_MSG_C2L_KeepAlive), msg)
}

func (r *Robot) SendCmd(cmdID uint32, msg interface{}) {
	r.cseq++
	ph := &parse.PackHead{
		Cmd:  cmdID,
		CSeq: r.cseq,
		SSeq: r.sseq,
	}
	if r.cryptoState {
		ph.Flags |= parse.MsgFlagCrypto
	}
	r.Send(ph, msg)

	/*if *flagMode == RunningModeTest {
		r.msgs.PushBack(&message{
			seq:  r.cseq,
			cmd:  cmdID,
			data: msg,
		})

		if r.msgs.Len() > maxMsgQueue {
			r.msgs.Remove(r.msgs.Front())
		}
	}*/
	l4g.Debug("send msg head: %+v", ph)
}

func (r *Robot) SendCmdWithRespID(cmdID uint32, msg interface{}, respID uint32) {
	r.cseq++
	ph := &parse.PackHead{
		Cmd:  cmdID,
		CSeq: r.cseq,
		SSeq: r.sseq,
	}
	if r.cryptoState {
		ph.Flags |= parse.MsgFlagCrypto
	}
	r.Send(ph, msg)
	r.respCmdId = respID

	/*if *flagMode == RunningModeTest {
		r.msgs.PushBack(&message{
			seq:  r.cseq,
			cmd:  cmdID,
			data: msg,
		})

		if r.msgs.Len() > maxMsgQueue {
			r.msgs.Remove(r.msgs.Front())
		}
	}*/
	l4g.Debug("send msg head: %+v", ph)
}

func (r *Robot) ID() uint64 { return r.userdata.Id }

func (r *Robot) Diamond() uint64 {
	return r.userdata.Diamond
}

func (r *Robot) Gold() uint64 {
	return r.userdata.Gold
}

func (r *Robot) Exp() uint64 {
	return r.userdata.Exp
}

func (r *Robot) checkItem(id, size uint32) bool {
	oldSize := r.itemsBag[id]
	//oldSize := u.GetItemCount(id)
	return oldSize >= size
}

func (r *Robot) checkFragment(id, size uint32) bool {
	oldSize := r.fragmentsBag[id]
	return oldSize >= size
}

func (r *Robot) checkToken(id uint32, size uint64) bool {
	oldSize := r.tokensBag[id]
	return oldSize >= size
}

func (r *Robot) checkResourcesSize(objs []*cl.Resource) bool {
	for _, obj := range objs {
		switch common.RESOURCE(obj.Type) {
		case common.RESOURCE_DIAMOND:
			if r.Diamond() < uint64(obj.Count) {
				l4g.Errorf("user %d has not enough diamond: %d %d", r.ID(), r.Diamond(), obj.Count)
				return false
			}
		case common.RESOURCE_GOLD:
			if r.Gold() < uint64(obj.Count) {
				l4g.Errorf("user %d has not enough gold: %d %d", r.ID(), r.Gold(), obj.Count)
				return false
			}
		case common.RESOURCE_PEXP:
			if r.Exp() < uint64(obj.Count) {
				l4g.Errorf("user %d has not enough exp: %d %d", r.ID(), r.Exp(), obj.Count)
				return false
			}
		case common.RESOURCE_ITEM:
			if !r.checkItem(obj.Value, obj.Count) {
				l4g.Errorf("user %d has not enough %d item: %d %d",
					r.ID(), obj.Value, r.itemsBag[obj.Value], obj.Count)
				return false
			}
		case common.RESOURCE_FRAGMENT:
			if !r.checkFragment(obj.Value, obj.Count) {
				l4g.Errorf("user %d has not enough %d fragment: %d %d",
					r.ID(), obj.Value, r.fragmentsBag[obj.Value], obj.Count)
				return false
			}
		case common.RESOURCE_HERO:
			/*heroM := u.HeroManager()
			if !heroM.CheckHeroExist(obj.Id) {
				l4g.Errorf("user %d hero not exist. hid: %d",
					u.ID(), obj.Id)
				return false
			}*/
		case common.RESOURCE_TOKEN:
			if !r.checkToken(obj.Value, uint64(obj.Count)) {
				l4g.Errorf("user %d has not enough %d token: %d %d",
					r.ID(), obj.Value, r.tokensBag[obj.Value], obj.Count)
				return false
			}
		case common.RESOURCE_EQUIP:
			/*equipM := u.EquipManager()
			if !equipM.CheckEquipExist(obj.Id) {
				l4g.Errorf("user %d equip not exist. eid: %d",
					u.ID(), obj.Id)
				return false
			}*/
		case common.RESOURCE_EMBLEM:
			/*emblemM := u.EmblemManager()
			if !emblemM.CheckEmblemExist(obj.Id) {
				l4g.Errorf("user %d emblem not exist. eid: %d",
					u.ID(), obj.Id)
				return false
			}*/
		case common.RESOURCE_ARTIFACT_FRAGMENT:
			/*if !u.checkArtifactFragment(obj.Value, obj.Count) {
				l4g.Errorf("user %d has not enough %d artifactFragment: %d %d",
					u.ID(), obj.Value, u.artifactFragmentsBag()[obj.Value], obj.Count)
				return false
			}*/
		case common.RESOURCE_EMBLEM_FRAGMENT:
			/*if !u.checkEmblemFragment(obj.Value, obj.Count) {
				l4g.Errorf("user %d has not enough %d emblemFragment: %d %d",
					u.ID(), obj.Value, u.emblemFragmentsBag()[obj.Value], obj.Count)
				return false
			}*/
		case common.RESOURCE_AVATAR:
		case common.RESOURCE_COUPON:
			if r.userdata.Coupon < int64(obj.Count) {
				l4g.Errorf("user %d has not enough coupon: %d %d", r.ID(), r.userdata.Coupon, obj.Count)
				return false
			}
		default:
			l4g.Errorf("user %d check resources, no found type: %d", r.ID(), obj.Type)
			return false
		}
	}
	return true
}

// 获取能上阵的5个英雄
func (r *Robot) GetFormationSuitHeroes(excludeHeroes []*Hero) []*Hero {
	getHeroes := make([]*Hero, 0, FormationNeedHeroCount)
	for _, v := range r.heroes {
		if hasSameHero(v.Data.Id, excludeHeroes) {
			continue
		}
		l := len(getHeroes)
		if l < FormationNeedHeroCount {
			getHeroes = append(getHeroes, NewHeroFromCl(v))
		} else {
			if v.Data.Star > getHeroes[l-1].GetClHero().Data.Star {
				getHeroes[l-1] = NewHeroFromCl(v)
			} else {
				continue
			}
		}
		//排序
		helper.ReorderLastElementDesc(getHeroes)
	}
	return getHeroes
}

func hasSameHero(id uint64, excludeHeroes []*Hero) bool {
	for _, v := range excludeHeroes {
		if id == v.GetClHero().Data.Id {
			return true
		}
	}
	return false
}

// 获取能上阵的3个神器
func (r *Robot) GetFormationSuitArtifacts(excludeArtifacts []*Artifact) []*Artifact {
	getArtifacts := make([]*Artifact, 0, FormationNeedArtifactCount)
	for _, v := range r.artifacts {
		if hasSameArtifact(v.SysId, excludeArtifacts) {
			continue
		}
		l := len(getArtifacts)
		if l < FormationNeedArtifactCount {
			getArtifacts = append(getArtifacts, NewArtifactFromCl(v))
		} else {
			if v.Score > getArtifacts[l-1].GetClArtifact().Score {
				getArtifacts[l-1] = NewArtifactFromCl(v)
			} else {
				continue
			}
		}
		//排序
		helper.ReorderLastElementDesc(getArtifacts)
	}
	return getArtifacts
}

func hasSameArtifact(id uint32, excludeArtifacts []*Artifact) bool {
	for _, v := range excludeArtifacts {
		if id == v.GetClArtifact().SysId {
			return true
		}
	}
	return false
}
