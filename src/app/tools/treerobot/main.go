package main

import (
	"app/goxml"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	b3 "github.com/magicsea/behavior3go"
	b3cfg "github.com/magicsea/behavior3go/config"
	b3core "github.com/magicsea/behavior3go/core"
	b3loader "github.com/magicsea/behavior3go/loader"
	"github.com/myzhan/boomer"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/math/rand"
)

var globalBoomer *boomer.Boomer

var structMaps *b3.RegisterStructMaps
var firstTree *b3core.BehaviorTree
var currentUserNum uint32

var mapTreesByID = sync.Map{}

var taskCtx *ctx.Group

func init() {
	//获取子树的方法
	b3core.SetSubTreeLoadFunc(func(id string) *b3core.BehaviorTree {
		// l4g.Infof("==>load subtree:%s", id)
		t, ok := mapTreesByID.Load(id)
		if ok {
			return t.(*b3core.BehaviorTree)
		}
		return nil
	})

	taskCtx = ctx.Background().CreateChild()
}

var (
	flagGateway      = flag.String("gateway", "************:38422", "gateway server address")
	flagLogDir       = flag.String("log_dir", "../log/", "service log dir")
	flagLogLevel     = flag.String("log_level", "DEBUG", "log level")
	locustMasterHost = flag.String("locust_master_host", "127.0.0.1", "locustMasterIp")
	locustMasterPort = flag.Int("locust_master_port", 8090, "locustMasterPort")
	treePath         = flag.String("tree_path", "../config/omini.b3", "行为树配置文件路径")
	uuidPrefix       = flag.String("uuid_prefix", "tree", "玩家账号前缀")
	serverId         = flag.Uint64("server_id", 1000100003, "服务器ID")
	flagDataPath     = flag.String("data", "../data/", "service data path")
)

func main() {
	flag.Parse()

	logFilename := *flagLogDir + "robot.log"
	cfg := util.NewL4GConfig(*flagLogLevel, logFilename)
	cfg = strings.ReplaceAll(cfg, "10M", "50M") //日志文件大一点再生成新文件
	l4g.Global.LoadConfiguration("", []byte(cfg))
	defer l4g.Close()
	if *flagLogLevel != "DEBUG" && *flagLogLevel != "INFO" {
		l4g.Error("错误的日志等级: %s", *flagLogLevel)
		return
	}

	goxml.Load(*flagDataPath, false, false)

	projectConfig, ok := b3cfg.LoadRawProjectCfg(*treePath)
	if !ok {
		l4g.Errorf("LoadTreeCfg err")
		return
	}

	//自定义节点注册
	structMaps = b3.NewRegisterStructMaps()
	NodesRegister(structMaps)

	//载入
	for _, v := range projectConfig.Data.Trees {
		tree := b3loader.CreateBevTreeFromConfig(&v, structMaps)
		tree.Print()
		//保存到树管理
		l4g.Infof("==>store subtree:%s", v.ID)
		mapTreesByID.Store(v.ID, tree)
		if firstTree == nil {
			firstTree = tree
		}
	}

	InitRobotCommand()

	task1 := &boomer.Task{
		Name:   "mainLoop",
		Weight: 0,
		Fn:     mainLoop,
	}
	globalBoomer = boomer.NewBoomer(*locustMasterHost, *locustMasterPort)
	globalBoomer.Run(task1)

	waitForQuit(globalBoomer)

	fmt.Println("shutdown")
}

func mainLoop() {
	fmt.Println("mainLoop start")

	index := atomic.AddUint32(&currentUserNum, 1)
	uuid := fmt.Sprintf("%s%d", *uuidPrefix, index)

	board := b3core.NewBlackboard()
	randO := rand.New(time.Now().UnixNano())
	board.SetMem("randObject", randO)

	tt := taskCtx.CreateChild()
	rb := NewRobot(tt.CreateChild(), uuid, board)
	go rb.Run()

	board.SetMem(keyRobot, rb)
	// rb.InitTimer()

	go treeTick(tt.CreateChild(), board)

BreakTask:
	for {
		select {
		case <-tt.Done():
			break BreakTask
		}
	}
	tt.Stop()
	tt.Wait()
	tt.Finish()
}

func treeTick(group *ctx.Group, board *b3core.Blackboard) {
	//输入板
	ticker := time.NewTicker(20 * time.Millisecond) // 20ms 执行一次，tree.Tick()
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			select {
			case <-group.Done():
				group.Stop()
				group.Finish()
				return
			default:
				firstTree.Tick(group, board)
			}
		}
	}
}

func waitForQuit(boom *boomer.Boomer) {
	wg := sync.WaitGroup{}
	wg.Add(1)

	quitByMe := false
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		<-c
		quitByMe = true
		boom.Quit()
		wg.Done()
	}()
	boomer.Events.Subscribe(boomer.EVENT_QUIT, func() {
		if !quitByMe {
			wg.Done()
		}
	})
	boomer.Events.Subscribe(boomer.EVENT_STOP, func() {
		if !quitByMe {
			wg.Done()
			/*
				l4g.Infof("event stop.")
				taskCtx.Stop()
				taskCtx.Wait()
				taskCtx.Finish()
				taskCtx = ctx.Background().CreateChild()
			*/
		}
	})

	wg.Wait()
	taskCtx.Stop()
	taskCtx.Wait()
	taskCtx.Finish()
	fmt.Println("taskCtx finished")
	ctx.Stop()
	ctx.Wait()
}
