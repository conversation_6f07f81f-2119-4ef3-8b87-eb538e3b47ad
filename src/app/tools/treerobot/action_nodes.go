package main

import (
	"app/protos/out/cg"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gitlab.qdream.com/kit/sea/ctx"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
	b3 "github.com/magicsea/behavior3go"
	b3cfg "github.com/magicsea/behavior3go/config"
	b3core "github.com/magicsea/behavior3go/core"
)

func actionsRegister(structMaps *b3.RegisterStructMaps) {
	structMaps.Register("GetServerList", new(GetServerList))
	structMaps.Register("Login", new(Login))
	structMaps.Register("Flush", new(Flush))
	structMaps.Register("GmAddResources", new(GmAddResources))
	structMaps.Register("GmTestData", new(GmTestData))
	structMaps.Register("Reconnect", new(Reconnect))

	userRegister(structMaps)
	dungeonRegister(structMaps)
	talentTreeRegister(structMaps)
	seasonDungeonRegister(structMaps)
	goldBuyRegister(structMaps)
	seasonLinkRegister(structMaps)
	emblemRegister(structMaps)
}

// 自定义action节点
type GetServerList struct {
	b3core.Action
	info string
}

func (g *GetServerList) Initialize(setting *b3cfg.BTNodeCfg) {
	g.Action.Initialize(setting)
}

func (g *GetServerList) OnTick(tick *b3core.Tick) b3.Status {
	rb, ok := tick.Blackboard.GetMem(keyRobot).(*Robot)
	if !ok {
		l4g.Error("GetServerList blackboard get robot failed")
		return b3.ERROR
	}
	start := time.Now()
	response, err := http.Get("http://192.168.0.89:12121/serverlist")
	if err != nil {
		globalBoomer.RecordFailure(g.GetName(), g.GetName(), 0, err.Error())
	} else {
		body, _ := ioutil.ReadAll(response.Body)
		elapsed := time.Since(start)
		var servers interface{}
		err = json.Unmarshal(body, &servers)
		// fmt.Println(servers)
		a, ok := servers.([]interface{})
		if !ok {
			l4g.Errorf("servers error:")
		} else {
			for _, server := range a {
				s := server.(map[string]interface{})
				if uint64(s["id"].(float64)) == *serverId {
					l4g.Debugf("server:%+v", server)
				}
			}
		}
		tick.Blackboard.SetMem("serverlist", servers)
		globalBoomer.RecordSuccess(g.GetName(), g.GetName(), int64(elapsed.Milliseconds()), int64(len(body)))
	}

	l4g.Debugf("GetServerList executed uuid:%s", rb.uuid)
	return b3.SUCCESS
}

// 自定义action节点
type Login struct {
	b3core.Action
}

func (l *Login) Initialize(setting *b3cfg.BTNodeCfg) {
	l.Action.Initialize(setting)
	// l.info = setting.GetPropertyAsString("info")
}

func (l *Login) OnTick(tick *b3core.Tick) b3.Status {
	action := &treeAction{
		Req: func(r *Robot) {
			l4g.Debugf("Login send request")
			reqMsg := &cg.C2G_Login{
				ServerId:   *serverId,
				Token:      genToken(r.uuid),
				SyncCookie: r.syncCookie,
				Version:    9999999,
			}
			r.SendCmdWithRespID(uint32(cg.ID_MSG_C2G_Login), reqMsg, uint32(cg.ID_MSG_G2C_Login))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cg.G2C_Login{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("G2C_Login unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("Login", "Login", elapsed.Milliseconds(), "ret error")
				return b3.FAILURE
			}
			r.Online(respMsg.SyncCookie, respMsg.Sync)
			globalBoomer.RecordSuccess("Login", "Login", elapsed.Milliseconds(), 0)
			l4g.Debugf("Login executed uuid:%s", r.UUID())
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}

	return handleTreeAction(action, tick)
}

// 自定义action节点
type Flush struct {
	b3core.Action
	info string
}

func (f *Flush) Initialize(setting *b3cfg.BTNodeCfg) {
	f.Action.Initialize(setting)
	// f.info = setting.GetPropertyAsString("info")
}

func (f *Flush) OnTick(tick *b3core.Tick) b3.Status {
	action := &treeAction{
		Req: func(r *Robot) {
			reqMsg := &cl.C2L_Flush{
				User:      true,
				Heroes:    true,
				Formation: true,
				Dungeon:   true,
			}
			r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_Flush), reqMsg, uint32(cl.ID_MSG_L2C_Flush))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cl.L2C_Flush{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("L2C_Flush unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("Flush", "Flush", elapsed.Milliseconds(), "ret error")
				return b3.FAILURE
			}
			globalBoomer.RecordSuccess("Flush", "Flush", elapsed.Milliseconds(), 0)
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}
	return handleTreeAction(action, tick)
}

// 发资源
type GmAddResources struct {
	b3core.Action
	resources []*cl.Resource
}

func (g *GmAddResources) Initialize(setting *b3cfg.BTNodeCfg) {
	g.Action.Initialize(setting)
	res1 := setting.GetPropertyAsString("res1")
	res2 := setting.GetPropertyAsString("res2")
	res3 := setting.GetPropertyAsString("res3")
	res4 := setting.GetPropertyAsString("res4")
	res5 := setting.GetPropertyAsString("res5")

	res := StringToResources(res1)
	if len(res) > 0 {
		g.resources = append(g.resources, res...)
	}
	res = StringToResources(res2)
	if len(res) > 0 {
		g.resources = append(g.resources, res...)
	}
	res = StringToResources(res3)
	if len(res) > 0 {
		g.resources = append(g.resources, res...)
	}
	res = StringToResources(res4)
	if len(res) > 0 {
		g.resources = append(g.resources, res...)
	}
	res = StringToResources(res5)
	if len(res) > 0 {
		g.resources = append(g.resources, res...)
	}
}

func StringToResources(s string) []*cl.Resource {
	if s == "nil" {
		return nil
	}
	res := strings.Split(s, ";")
	resources := make([]*cl.Resource, 0, len(res))
	for _, v := range res {
		params := strings.Split(v, "-")
		if len(params) < 3 {
			l4g.Error("gm res:%s params error", v)
			continue
		}
		resType, err := strconv.Atoi(params[0])
		if err != nil {
			l4g.Error("gm res:%s params error", v)
			continue
		}
		resValue, err := strconv.Atoi(params[1])
		if err != nil {
			l4g.Error("gm res:%s params error", v)
			continue
		}
		resCount, err := strconv.Atoi(params[2])
		if err != nil {
			l4g.Error("gm res:%s params error", v)
			continue
		}
		resources = append(resources, &cl.Resource{
			Type:  uint32(resType),
			Value: uint32(resValue),
			Count: uint32(resCount),
		})
	}
	return resources
}

func (g *GmAddResources) OnTick(tick *b3core.Tick) b3.Status {
	action := &treeAction{
		Req: func(r *Robot) {
			reqMsg := &cl.C2L_GM{}
			if len(g.resources) > 0 {
				tmp := make([]*cl.Resource, len(g.resources))
				copy(tmp, g.resources)
				reqMsg.Awards = tmp
			}
			r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_GM), reqMsg, uint32(cl.ID_MSG_L2C_GM))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cl.L2C_GM{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("L2C_SeasonDungeonFight unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("GM", "GmAddResources", elapsed.Milliseconds(), "ret error")
				return b3.FAILURE
			}
			globalBoomer.RecordSuccess("GM", "GmAddResources", elapsed.Milliseconds(), 0)
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}

	return handleTreeAction(action, tick)
}

// 发资源
type GmTestData struct {
	b3core.Action
	stage int
}

func (g *GmTestData) Initialize(setting *b3cfg.BTNodeCfg) {
	g.Action.Initialize(setting)
	g.stage = setting.GetPropertyAsInt("stage")
}

func (g *GmTestData) OnTick(tick *b3core.Tick) b3.Status {
	if g.stage == 0 {
		return b3.SUCCESS
	}
	action := &treeAction{
		Req: func(r *Robot) {
			reqMsg := &cl.C2L_GM{}
			reqMsg.TestData = uint32(g.stage)
			r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_GM), reqMsg, uint32(cl.ID_MSG_L2C_GM))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cl.L2C_GM{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("L2C_SeasonDungeonFight unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("GM", "GmTestData", elapsed.Milliseconds(), "ret error")
				return b3.FAILURE
			}
			globalBoomer.RecordSuccess("GM", "GmTestData", elapsed.Milliseconds(), 0)
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}

	return handleTreeAction(action, tick)
}

type Reconnect struct {
	b3core.Action
}

func (r *Reconnect) OnTick(tick *b3core.Tick) b3.Status {
	action := &treeAction{
		Status: make(chan b3.Status),
	}
	action.Req = func(r *Robot) {
		r.broker.Stop()
		r.connect(false)
		globalBoomer.RecordSuccess("reconnect", "reconnect", 0, 0)
		action.Status <- b3.SUCCESS
	}
	return handleTreeAction(action, tick)
}

type treeAction struct {
	Req    func(*Robot)
	Resp   func(*Robot, []byte) b3.Status
	Status chan b3.Status
}

func handleTreeAction(action *treeAction, tick *b3core.Tick) b3.Status {
	rb, ok := tick.Blackboard.GetMem(keyRobot).(*Robot)
	if !ok {
		l4g.Error("blackboard get robot failed")
		return b3.ERROR
	}
	group, ok := tick.GetTarget().(*ctx.Group)
	select {
	case rb.treeActions <- action:
	case <-group.Done():
		l4g.Infof("closed when send action uuid: %s", rb.uuid)
		return b3.FAILURE
	}
	select {
	case <-group.Done():
		l4g.Infof("closed when wait status uuid: %s", rb.uuid)
		return b3.FAILURE
	case status := <-action.Status:
		return status
	}
}
