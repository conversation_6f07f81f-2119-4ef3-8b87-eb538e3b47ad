package main

import (
	"time"

	"gitlab.qdream.com/kit/sea/math/rand"

	l4g "github.com/ivanabc/log4go"
	b3 "github.com/magicsea/behavior3go"
	b3core "github.com/magicsea/behavior3go/core"
)

// 组合节点类型
func compositesRegister(structMaps *b3.RegisterStructMaps) {
	structMaps.Register("MemRandom", new(MemRandom))
	structMaps.Register("Parallel", new(Parallel))
}

// MemRandom
// @Description:带记忆的随机节点。(能够记录执行到哪个节点)
type MemRandom struct {
	b3core.Composite
}

func (n *MemRandom) OnOpen(tick *b3core.Tick) {
	tick.Blackboard.Set("runningChild", -1, tick.GetTree().GetID(), n.GetID())
}

func (n *MemRandom) OnTick(tick *b3core.Tick) b3.Status {
	var index = tick.Blackboard.GetInt("runningChild", tick.GetTree().GetID(), n.GetID())
	if index < 0 {
		r, ok := tick.Blackboard.GetMem("randObject").(*rand.Rand)
		if !ok {
			l4g.Errorf("Get randObject error.")
			index = int(time.Now().Unix() % int64(n.GetChildCount()))
		} else {
			index = r.RandBetween(0, n.GetChildCount()-1)
		}
	}
	var status = n.GetChild(index).Execute(tick)
	if status != b3.FAILURE {
		if status == b3.RUNNING {
			tick.Blackboard.Set("runningChild", index, tick.GetTree().GetID(), n.GetID())
		}
		return status
	}
	return b3.SUCCESS
}

// Parallel
// @Description:并行节点
type Parallel struct {
	b3core.Composite
}

func (n *Parallel) OnOpen(tick *b3core.Tick) {
}

func (n *Parallel) OnTick(tick *b3core.Tick) b3.Status {

	// 并行执行多个子节点
	n.GetChildCount()

	return b3.SUCCESS
}
