package main

import (
	"time"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

type Command interface {
	Execute(*Robot, *parse.PackHead, []byte) bool
}

type CommandM struct {
	cmdm map[uint32]Command
}

func NewCommandM() *CommandM {
	return &CommandM{
		cmdm: make(map[uint32]Command),
	}
}

func (m *CommandM) Register(id uint32, cmd Command) {
	m.cmdm[id] = cmd
}

func (m *CommandM) Dispatcher(robot *Robot, ph *parse.PackHead, data []byte) bool {
	if cmd, exist := m.cmdm[ph.Cmd]; exist {
		start := time.Now().UnixNano()
		ret := cmd.Execute(robot, ph, data)
		executeTime := time.Now().UnixNano() - start
		if executeTime > 1000000 {
			l4g.Info("COMMAND EXECUTE TIME: %d %d", ph.Cmd, executeTime)
		}
		return ret
	}
	l4g.Error("[Command] no find cmd: %d", ph.Cmd)
	return false
}
