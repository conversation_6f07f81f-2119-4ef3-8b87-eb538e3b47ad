package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotGoldBuyCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GoldBuyGet), L2CGoldBuyCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GoldBuyGetGold), L2CGoldBuyGetGoldCommand{})
}

type L2CGoldBuyCommand struct {
}

func (c L2CGoldBuyCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GoldBuyGet{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGoldBuyCommand unmarshal error: %s", err)
		return false
	}
	l4g.Debug("L2CGoldBuyCommand recv:%+v", recv)
	if recv.Ret == uint32(ret.RET_OK) {
		if robot.goldBuy == nil {
			robot.goldBuy = &cl.GoldBuy{}
		}
		robot.goldBuy.Chests = recv.Chests
	}

	return recv.Ret == uint32(ret.RET_OK)
}

type L2CGoldBuyGetGoldCommand struct {
}

func (c L2CGoldBuyGetGoldCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GoldBuyGetGold{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGoldBuyGetGoldCommand unmarshal error: %s", err)
		return false
	}
	l4g.Debug("L2CGoldBuyGetGoldCommand recv:%+v", recv)

	if recv.Ret == uint32(ret.RET_OK) {
		if robot.goldBuy == nil {
			robot.goldBuy = &cl.GoldBuy{}
		}
		var isFind bool
		for _, v := range robot.goldBuy.Chests {
			if v.SysId == recv.SysId {
				v.UseCount++
				isFind = true
				break
			}
		}

		if !isFind {
			robot.goldBuy.Chests = append(robot.goldBuy.Chests, &cl.GoldChest{
				SysId:    recv.SysId,
				UseCount: 1,
			})
		}
	}

	return recv.Ret == uint32(ret.RET_OK)
}
