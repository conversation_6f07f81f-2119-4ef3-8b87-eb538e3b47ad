package main

import (
	"app/protos/out/cl"
	"app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotFormationCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_GetFormation), L2CGetFormationCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_Formation), L2CFormationCommand{})
}

type L2CGetFormationCommand struct {
}

func (c L2CGetFormationCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_GetFormation{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CGetFormationCommand unmarshal error: %s", err)
		return false
	}
	robot.formations = recv.Formations
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CFormationCommand struct {
}

func (c L2CFormationCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_Formation{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CFormationCommand unmarshal error: %s", err)
		return false
	}
	// robot.formation.L2CFormation(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
