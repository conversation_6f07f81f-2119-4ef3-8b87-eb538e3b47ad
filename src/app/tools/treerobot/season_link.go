package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"fmt"
	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	b3 "github.com/magicsea/behavior3go"
	"github.com/magicsea/behavior3go/core"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
)

func seasonLinkRegister(structMaps *b3.RegisterStructMaps) {
	structMaps.Register("SeasonLinkGetData", new(SeasonLinkGetData))
	structMaps.Register("SeasonLinkActivate", new(SeasonLinkActivate))
}

type SeasonLinkGetData struct {
	core.Action
}

func (n *SeasonLinkGetData) OnTick(tick *core.Tick) b3.Status {
	action := &treeAction{
		Req: func(r *Robot) {
			reqMsg := &cl.C2L_SeasonLinkGetData{}
			r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_SeasonLinkGetData), reqMsg, uint32(cl.ID_MSG_L2C_SeasonLinkGetData))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cl.L2C_SeasonLinkGetData{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("L2C_SeasonLinkGetData unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("SeasonLink", "SeasonLinkGetData", elapsed.Milliseconds(), fmt.Sprintf("ret:%d error", respMsg.Ret))
				return b3.FAILURE
			}
			r.setSeasonLinkData(respMsg)
			globalBoomer.RecordSuccess("SeasonLink", "SeasonLinkGetData", elapsed.Milliseconds(), 0)
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}

	return handleTreeAction(action, tick)
}

func (r *Robot) setSeasonLinkData(msg *cl.L2C_SeasonLinkGetData) {
	r.seasonLink = msg.Data
	if r.seasonLink == nil {
		r.seasonLink = &cl.SeasonLink{}
	}
}

type SeasonLinkActivate struct {
	core.Action
}

func (n *SeasonLinkActivate) OnTick(tick *core.Tick) b3.Status {
	rb, ok := tick.Blackboard.GetMem(keyRobot).(*Robot)
	if !ok {
		l4g.Error("blackboard get robot failed")
		return b3.ERROR
	}
	action := &treeAction{
		Req: func(r *Robot) {
			reqMsg := &cl.C2L_SeasonLinkActivate{
				HeroSysId: n.randomHeroSysId(rb.rd),
			}
			r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_SeasonLinkActivate), reqMsg, uint32(cl.ID_MSG_L2C_SeasonLinkActivate))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cl.L2C_SeasonLinkActivate{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("L2C_SeasonLinkActivate unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("SeasonLink", "SeasonLinkActivate", elapsed.Milliseconds(), fmt.Sprintf("ret:%d error", respMsg.Ret))
				return b3.FAILURE
			}
			if r.seasonLink != nil {
				r.seasonLink.ActivatedHeroes = append(r.seasonLink.ActivatedHeroes, respMsg.HeroSysId)
			}
			globalBoomer.RecordSuccess("SeasonLink", "SeasonLinkActivate", elapsed.Milliseconds(), 0)
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}

	return handleTreeAction(action, tick)
}

func (n *SeasonLinkActivate) randomHeroSysId(rd *rand.Rand) uint32 {
	seasonInfo := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(time.Now().Unix())
	if seasonInfo == nil {
		l4g.Error("SeasonLinkActivate get seasonInfo failed")
		return 0
	}

	heroLinkMap := goxml.GetData().SeasonLinkInfoM.GetHeroIdRecordMapBySeasonId(seasonInfo.Id)
	if heroLinkMap == nil {
		l4g.Error("SeasonLinkActivate get heroLinkMap failed")
		return 0
	}

	heroes := make([]uint32, 0, len(heroLinkMap))
	for heroId := range heroLinkMap {
		heroes = append(heroes, heroId)
	}
	if len(heroes) == 0 {
		l4g.Error("No heroes available in heroLinkMap")
		return 0
	}
	return heroes[rd.Intn(len(heroes))]
}
