package main

import (
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/math/rand"

	l4g "github.com/ivanabc/log4go"
	b3 "github.com/magicsea/behavior3go"
	b3cfg "github.com/magicsea/behavior3go/config"
	b3core "github.com/magicsea/behavior3go/core"
)

func conditionRegister(structMaps *b3.RegisterStructMaps) {
	structMaps.Register("IsReconnect", new(IsReconnect))
	structMaps.Register("IsResourceEnough", new(IsResourceEnough))
	structMaps.Register("IsSeasonDungeonEnough", new(IsSeasonDungeonEnough))
}

type IsReconnect struct {
	b3core.Condition
	weight int
}

func (c *IsReconnect) Initialize(setting *b3cfg.BTNodeCfg) {
	c.Condition.Initialize(setting)

	c.weight = setting.GetPropertyAsInt("weight")
}

func (c *IsReconnect) OnTick(tick *b3core.Tick) b3.Status {
	if c.weight == -1 {
		return b3.FAILURE
	}
	r, ok := tick.Blackboard.GetMem("randObject").(*rand.Rand)
	if !ok {
		l4g.Errorf("IsReconnect get rand error!")
		return b3.FAILURE
	} else {
		randNum := r.RandBetween(1, 100)
		if randNum <= c.weight {
			return b3.SUCCESS
		}
	}
	return b3.FAILURE
}

type IsResourceEnough struct {
	b3core.Condition
	resources []*cl.Resource
}

func (c *IsResourceEnough) Initialize(setting *b3cfg.BTNodeCfg) {
	c.Condition.Initialize(setting)

	res1 := setting.GetPropertyAsString("res1")
	res2 := setting.GetPropertyAsString("res2")
	res3 := setting.GetPropertyAsString("res3")
	res4 := setting.GetPropertyAsString("res4")
	res5 := setting.GetPropertyAsString("res5")

	res := StringToResources(res1)
	if len(res) > 0 {
		c.resources = append(c.resources, res...)
	}
	res = StringToResources(res2)
	if len(res) > 0 {
		c.resources = append(c.resources, res...)
	}
	res = StringToResources(res3)
	if len(res) > 0 {
		c.resources = append(c.resources, res...)
	}
	res = StringToResources(res4)
	if len(res) > 0 {
		c.resources = append(c.resources, res...)
	}
	res = StringToResources(res5)
	if len(res) > 0 {
		c.resources = append(c.resources, res...)
	}
}

func (c *IsResourceEnough) OnTick(tick *b3core.Tick) b3.Status {
	rb, ok := tick.Blackboard.GetMem(keyRobot).(*Robot)
	if !ok {
		l4g.Error("blackboard get robot failed")
		return b3.ERROR
	}

	l4g.Debugf("IsResourceEnough onTick uuid:%s", rb.UUID())

	ok = rb.checkResourcesSize(c.resources)
	if !ok {
		return b3.FAILURE
	}
	return b3.SUCCESS
}

type IsSeasonDungeonEnough struct {
	b3core.Condition
	targetID int
}

func (c *IsSeasonDungeonEnough) Initialize(setting *b3cfg.BTNodeCfg) {
	c.Condition.Initialize(setting)

	c.targetID = setting.GetPropertyAsInt("targetID")

}

func (c *IsSeasonDungeonEnough) OnTick(tick *b3core.Tick) b3.Status {
	rb, ok := tick.Blackboard.GetMem(keyRobot).(*Robot)
	if !ok {
		l4g.Error("blackboard get robot failed")
		return b3.ERROR
	}

	if rb.seasonDungeon == nil || rb.seasonDungeon.DungeonId < uint32(c.targetID) {
		return b3.FAILURE
	}

	return b3.SUCCESS
}
