package main

import (
	b3 "github.com/magicsea/behavior3go"
	b3cfg "github.com/magicsea/behavior3go/config"
	b3core "github.com/magicsea/behavior3go/core"
	"gitlab.qdream.com/kit/sea/time"
)

// 装饰节点类型
func decoratorsRegister(structMaps *b3.RegisterStructMaps) {
	structMaps.Register("RepeatDuration", new(RepeatDuration))
	structMaps.Register("Timer", new(Timer))
}

// RepeatDuration
// @Description:
type RepeatDuration struct {
	b3core.Decorator
	duration int
}

func (n *RepeatDuration) Initialize(setting *b3cfg.BTNodeCfg) {
	n.Decorator.Initialize(setting)

	n.duration = setting.GetPropertyAsInt("duration")
}

func (n *RepeatDuration) OnOpen(tick *b3core.Tick) {
	tick.Blackboard.Set("start", time.NowMsec(), tick.GetTree().GetID(), n.GetID())
}

func (n *RepeatDuration) OnTick(tick *b3core.Tick) b3.Status {

	if n.GetChild() == nil {
		return b3.ERROR
	}
	var start = tick.Blackboard.GetInt64("start", tick.GetTree().GetID(), n.GetID())
	var status = b3.SUCCESS
	for n.duration == -1 || time.NowMsec()-start > int64(n.duration) {
		status = n.GetChild().Execute(tick)
		return b3.RUNNING
		if status == b3.SUCCESS || status == b3.FAILURE {
			// continue
		} else {
			break
		}
	}
	return status
}

// Timer
// @Description:
type Timer struct {
	b3core.Decorator
	duration int
}

func (n *Timer) Initialize(setting *b3cfg.BTNodeCfg) {
	n.Decorator.Initialize(setting)
	n.duration = setting.GetPropertyAsInt("duration")
}

func (n *Timer) OnOpen(tick *b3core.Tick) {
	tick.Blackboard.Set("start", time.NowMsec(), tick.GetTree().GetID(), n.GetID())
}

func (n *Timer) OnTick(tick *b3core.Tick) b3.Status {
	if n.GetChild() == nil {
		return b3.ERROR
	}
	var start = tick.Blackboard.GetInt64("start", tick.GetTree().GetID(), n.GetID())
	var status = b3.SUCCESS
	if time.NowMsec()-start >= int64(n.duration) {
		status = n.GetChild().Execute(tick)
		return status
	}
	return b3.RUNNING
}
