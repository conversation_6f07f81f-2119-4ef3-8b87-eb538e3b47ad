package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"fmt"
	"time"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	b3 "github.com/magicsea/behavior3go"
	b3cfg "github.com/magicsea/behavior3go/config"
	"github.com/magicsea/behavior3go/core"
)

func userRegister(structMaps *b3.RegisterStructMaps) {
	structMaps.Register("UserFormation", new(UserFormation))
}

type UserFormation struct {
	core.Action
	formationID int
}

func (u *UserFormation) Initialize(setting *b3cfg.BTNodeCfg) {
	u.Action.Initialize(setting)
	u.formationID = setting.GetPropertyAsInt("formation_id")
}

func (u *UserFormation) OnTick(tick *core.Tick) b3.Status {
	action := &treeAction{
		Req: func(r *Robot) {
			_, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(uint32(u.formationID))
			if teamNum == 0 {
				teamNum = 1
			}

			formation := &cl.Formation{
				Id:    uint32(u.formationID),
				Teams: make([]*cl.FormationTeamInfo, 0, teamNum),
			}

			var excludeHeroes []*Hero
			var excludeArtifacts []*Artifact
			for i := 0; i < teamNum; i++ {
				team := &cl.FormationTeamInfo{}
				heroes := r.GetFormationSuitHeroes(excludeHeroes)
				for k, v := range heroes {
					team.Info = append(team.Info, &cl.FormationInfo{
						Pos: uint32(k + 1),
						Hid: v.GetClHero().Data.Id,
					})
				}

				artifacts := r.GetFormationSuitArtifacts(excludeArtifacts)
				for k, v := range artifacts {
					team.Artifacts = append(team.Artifacts, &cl.FormationArtifactInfo{
						Pos: uint32(k + 1),
						Aid: v.GetClArtifact().SysId,
					})
				}

				formation.Teams = append(formation.Teams, team)
				excludeHeroes = append(excludeHeroes, heroes...)
				excludeArtifacts = append(excludeArtifacts, artifacts...)
			}
			reqMsg := &cl.C2L_Formation{
				FormationId: uint32(u.formationID),
				Formation:   formation,
			}
			r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_Formation), reqMsg, uint32(cl.ID_MSG_L2C_Formation))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cl.L2C_Formation{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("L2C_Formation unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("formation", "Formation", elapsed.Milliseconds(), fmt.Sprintf("ret:%d error", respMsg.Ret))
				return b3.FAILURE
			}
			r.formations[respMsg.FormationId] = respMsg.Formation
			globalBoomer.RecordSuccess("formation", "Formation", elapsed.Milliseconds(), 0)
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}

	return handleTreeAction(action, tick)
}
