package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"time"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	b3 "github.com/magicsea/behavior3go"
	"github.com/magicsea/behavior3go/core"
)

func goldBuyRegister(structMaps *b3.RegisterStructMaps) {
	structMaps.Register("GoldBuyGet", new(GoldBuyGet))
	structMaps.Register("GoldBuyGetGold", new(GoldBuyGetGold))
}

type GoldBuyGet struct {
	core.Action
}

func (g *GoldBuyGet) OnTick(tick *core.Tick) b3.Status {
	action := &treeAction{
		Req: func(r *Robot) {
			reqMsg := &cl.C2L_GoldBuyGet{}
			r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_GoldBuyGet), reqMsg, uint32(cl.ID_MSG_L2C_GoldBuyGet))
		},
		Resp: func(r *Robot, data []byte) b3.Status {
			respMsg := &cl.L2C_GoldBuyGet{}
			if err := proto.Unmarshal(data, respMsg); err != nil {
				l4g.Error("L2C_GoldBuyGet unmarshal error: %s", err)
				return b3.ERROR
			}
			elapsed := time.Since(r.startTm)
			if respMsg.Ret != uint32(ret.RET_OK) {
				globalBoomer.RecordFailure("GoldBuy", "GoldBuyGet", elapsed.Milliseconds(), "ret error")
				return b3.FAILURE
			}
			if r.goldBuy == nil {
				r.goldBuy = &cl.GoldBuy{}
			}
			r.goldBuy.Chests = respMsg.Chests
			globalBoomer.RecordSuccess("GoldBuy", "GoldBuyGet", elapsed.Milliseconds(), int64(len(data)))
			return b3.SUCCESS
		},

		Status: make(chan b3.Status),
	}

	return handleTreeAction(action, tick)
}

type GoldBuyGetGold struct {
	core.Action
}

func (g *GoldBuyGetGold) OnTick(tick *core.Tick) b3.Status {
	action := &treeAction{
		Status: make(chan b3.Status),
	}
	action.Req = func(r *Robot) {
		reqMsg := &cl.C2L_GoldBuyGetGold{}
		useGold := make(map[uint32]uint32)
		for _, v := range goxml.GetData().GoldBuyInfoM.Datas {
			if v.FreeLimit > 0 {
				useGold[v.Id] = v.FreeLimit
			} else {
				useGold[v.Id] = v.BuyLimit
			}
		}
		var canRand []uint32
		for _, v := range r.goldBuy.Chests {
			if v.UseCount < useGold[v.SysId] {
				canRand = append(canRand, v.SysId)
			}
		}
		if len(canRand) == 0 {
			action.Status <- b3.SUCCESS
			return
		}

		reqMsg.SysId = canRand[0]
		r.SendCmdWithRespID(uint32(cl.ID_MSG_C2L_GoldBuyGetGold), reqMsg, uint32(cl.ID_MSG_L2C_GoldBuyGetGold))
	}
	action.Resp = func(r *Robot, data []byte) b3.Status {
		respMsg := &cl.L2C_GoldBuyGetGold{}
		if err := proto.Unmarshal(data, respMsg); err != nil {
			l4g.Error("L2C_GoldBuyGetGold unmarshal error: %s", err)
			return b3.ERROR
		}
		elapsed := time.Since(r.startTm)
		if respMsg.Ret != uint32(ret.RET_OK) {
			globalBoomer.RecordFailure("GoldBuy", "GoldBuyGetGold", elapsed.Milliseconds(), "ret error")
			return b3.FAILURE
		}
		if r.goldBuy == nil {
			r.goldBuy = &cl.GoldBuy{}
		}

		var isFind bool
		for _, v := range r.goldBuy.Chests {
			if v.SysId == respMsg.SysId {
				v.UseCount++
				isFind = true
				break
			}
		}

		if !isFind {
			r.goldBuy.Chests = append(r.goldBuy.Chests, &cl.GoldChest{
				SysId:    respMsg.SysId,
				UseCount: 1,
			})
		}

		globalBoomer.RecordSuccess("GoldBuy", "GoldBuyGetGold", elapsed.Milliseconds(), int64(len(data)))
		return b3.SUCCESS
	}

	return handleTreeAction(action, tick)
}
