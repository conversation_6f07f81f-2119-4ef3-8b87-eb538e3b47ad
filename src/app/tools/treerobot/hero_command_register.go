package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"

	l4g "github.com/ivanabc/log4go"
)

func InitRobotHeroCommand() {
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroList), L2CHeroListCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroLevelUp), L2CHeroLevelUpCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroStageUp), L2CHeroStageUpCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroStarUp), L2CHeroStarUpCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroBuySlot), L2CHeroBuySlotCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroUpdateLockStatus), L2CHeroUpdateLockStatusCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroDecompose), L2CHeroDecomposeCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroTestAttr), L2CHeroTestAttrCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroBack), L2CHeroBackCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroRevive), L2CHeroReviveCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroGetStarUpCosts), L2CHeroGetStarUpCostsCommand{})
	gRobotCommandM.Register(uint32(cl.ID_MSG_L2C_HeroGemLevelUp), L2CHeroGemLevelUpCommand{})
}

type L2CHeroListCommand struct {
}

func (c L2CHeroListCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroList{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroListCommand unmarshal error: %s", err)
		return false
	}
	for _, hero := range recv.Heroes {
		info := goxml.GetData().HeroInfoM.Index(hero.GetData().SysId)
		if info == nil {
			l4g.Errorf("[Hero] %s robot load hero err. hid:%d sysId:%d",
				robot.UUID(), hero.GetData().Id, hero.GetData().SysId)
			return false
		}
		if info.Special == goxml.HeroCommon {
			robot.heroes = append(robot.heroes, hero)
		} else {
		}
	}
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroLevelUpCommand struct {
}

func (c L2CHeroLevelUpCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroLevelUp{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroLevelUpCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroLevelUp(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroStageUpCommand struct {
}

func (c L2CHeroStageUpCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroStageUp{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroStageUpCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroStageUp(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroStarUpCommand struct {
}

func (c L2CHeroStarUpCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroStarUp{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroStarUpCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroStarUp(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroBuySlotCommand struct {
}

func (c L2CHeroBuySlotCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroBuySlot{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroBuySlotCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroBuySlot(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroUpdateLockStatusCommand struct {
}

func (c L2CHeroUpdateLockStatusCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroUpdateLockStatus{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroUpdateLockStatusCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroUpdateLockStatus(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroDecomposeCommand struct {
}

func (c L2CHeroDecomposeCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroDecompose{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroDecomposeCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroDecompose(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroTestAttrCommand struct {
}

func (c L2CHeroTestAttrCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroTestAttr{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("HeroTestAttrCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroTestAttr(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroBackCommand struct {
}

func (c L2CHeroBackCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroBack{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroBackCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroBack(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroReviveCommand struct {
}

func (c L2CHeroReviveCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroRevive{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroReviveCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroRevive(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroChangeRandomCommand struct {
}

func (c L2CHeroChangeRandomCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroChangeRandom{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroChangeRandomCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroChangeRandom(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroChangeSaveCommand struct {
}

func (c L2CHeroChangeSaveCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroChangeSave{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroChangeSaveCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroChangeSave(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroGetStarUpCostsCommand struct {
}

func (c L2CHeroGetStarUpCostsCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroGetStarUpCosts{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroGetStarUpCostsCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroGetStarUpCosts(recv)
	return recv.Ret == uint32(ret.RET_OK)
}

type L2CHeroGemLevelUpCommand struct {
}

func (c L2CHeroGemLevelUpCommand) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
	recv := &cl.L2C_HeroGemLevelUp{}
	if err := proto.Unmarshal(data, recv); err != nil {
		l4g.Error("L2CHeroGemLevelUpCommand unmarshal error: %s", err)
		return false
	}
	// robot.hero.L2CHeroGemLevelUp(recv)
	return recv.Ret == uint32(ret.RET_OK)
}
