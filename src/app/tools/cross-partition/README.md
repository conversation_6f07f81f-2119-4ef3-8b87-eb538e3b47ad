# cross-partition介绍

### 用途

- 跨服业务压测时，快速实现跨服组分区设置

  

### 使用方法

- 编译，在项目根目录下执行

  ```shel
  make cross-partition
  ```

- cross-partition可执行文件，将生成在`./tools`目录下

- 将cross-partition复制到目标跨服服务器上，再执行：

  ```shell
  ./partition -activity_id=2 -redis_index=1 -start_sid=90001 -server_count=4 -total_part=100
  ```

- 参数解释：

  - activity_id: 跨服玩法id（1-router 2-wrestle）。默认值 `2`
  - redis_addr：redis地址。默认值 `0.0.0.0:9898`
  - redis_index: 跨服数据所在的redis index。默认值 `2`
  - start_sid：压测服的首个logic服务器id。默认值 `90001`
  - server_count：一个分区有多少个logic服。默认值 `4`
  - total_part：分区总数。默认值 `100`

  

### 实现简介，以上述默认参数设置为例

- 以start_sid设置的90001，作为起始id
- 如果server_count设置为4，total_part设置为100
- 则会生成出，4个服为1个分区，共100个分区的跨服构成
  - 即90001-90004在1个分区，90005-90008在第2个分区，以此类推



### 注意事项

- 会依赖项目proto编译后的文件，为确保正确执行，建议先编译proto
- 生成的logic服务器的id，必须确保在gm服务器数据中已存在（即gm数据库的server表中存在）