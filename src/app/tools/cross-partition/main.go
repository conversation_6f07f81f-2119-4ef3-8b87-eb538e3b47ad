package main

import (
	"app/cross/db/redisop"
	"app/protos/in/cr"
	"app/protos/in/r2c"
	"flag"
	"fmt"
	"time"

	"gitlab.qdream.com/kit/redis"
)

var (
	flagActID          = flag.Int("activity_id", 2, "玩法id")
	flagRedisAddr      = flag.String("redis_addr", "0.0.0.0:9898", "redis address")
	flagRedisPsword    = flag.String("redis_psword", "ngame", "redis password")
	flagRedisIndex     = flag.Int("redis_index", 2, "redis db index")
	flagStartSid       = flag.Uint64("start_sid", 90001, "start server id")
	flagServersPerPart = flag.Int("server_count", 4, "servers per partition")
	flagTotalPart      = flag.Int("total_part", 100, "total partitions")
)

type RedisManager struct {
	client *redis.Client
	ropCr  *redisop.CrClient
}

func partitionRule() *r2c.C2R_SaveActInfo {
	totalServerCount := *flagServersPerPart * *flagTotalPart
	data := &r2c.C2R_SaveActInfo{
		Id:    uint32(*flagActID),
		Parts: make([]*cr.ActPartition, 0, totalServerCount),
	}

	baseSid := *flagStartSid
	for partID := 1; partID <= *flagTotalPart; partID++ {
		for i := 0; i < *flagServersPerPart; i++ {
			data.Parts = append(data.Parts, &cr.ActPartition{
				Sid:     baseSid,
				NowPart: uint32(partID),
			})
			baseSid++
		}
	}
	return data
}

func main() {
	flag.Parse()
	printParam()

	redisM := NewRedisManager()
	data := partitionRule()
	redisM.delPartition(uint64(data.Id))
	redisM.updatePartition(data)
}

func printParam() {
	fmt.Printf("activityID: %d, redisAddr: %s, redisIndex: %d\n", *flagActID, *flagRedisAddr, *flagRedisIndex)
	fmt.Printf("startSid: %d, serversPerPart: %d, totalPart: %d\n", *flagStartSid, *flagServersPerPart, *flagTotalPart)
	fmt.Println()
}

func NewRedisManager() *RedisManager {
	var (
		redisAddr = *flagRedisAddr
		password  = *flagRedisPsword
		dbIndex   = *flagRedisIndex
	)
	client, err := redis.DialTimeout("tcp", redisAddr, password, uint32(dbIndex), 5*time.Second, true)
	if err != nil {
		fmt.Printf("connect redis serve error: %s %s\n", redisAddr, err)
		panic(err)
	}
	if ret, err := client.Cmd("auth", password).Str(); err != nil || ret != "OK" {
		fmt.Printf("redis password error: %s-%s %s\n", redisAddr, password, err)
		panic(err)
	}
	if ret, err := client.Cmd("select", dbIndex).Str(); err != nil || ret != "OK" {
		fmt.Printf("select redis db index error: %s-%d %s\n", redisAddr, dbIndex, err)
		panic(err)
	}
	fmt.Printf("connect redis(%s-%d) success\n", redisAddr, dbIndex)

	return &RedisManager{
		client: client,
		ropCr:  &redisop.CrClient{Client: client},
	}
}

func (r *RedisManager) delPartition(activityID uint64) {
	startTime := time.Now()
	r.ropCr.DelAllActPartitionSK(activityID)

	//统计执行时间
	endTime := time.Now()
	fmt.Printf("RedisManager.delPartition delPartition:%d, useTime:%v\n", activityID, endTime.Sub(startTime))
}

func (r *RedisManager) updatePartition(data *r2c.C2R_SaveActInfo) {
	startTime := time.Now()
	i := 0
	if len(data.Parts) > 0 {
		r.ropCr.SetSomeActPartitionMCallSK(uint64(data.Id), data.Parts)
		i++
	}
	for j := 0; j < i; j++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			fmt.Printf("RedisManager save act partition :%d error: %v\n", j, reply.Err)
			return
		}
	}

	//统计执行时间
	endTime := time.Now()
	fmt.Printf("RedisManager.SaveActInfo data:%+v, useTime:%v\n", data, endTime.Sub(startTime))
}
