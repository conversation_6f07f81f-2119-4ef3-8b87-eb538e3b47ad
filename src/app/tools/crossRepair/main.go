/*
Copyright © 2019 NAME HERE <EMAIL ADDRESS>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package main

import (
	"app/tools/crossRepair/cmd"
	"time"

	l4g "github.com/ivanabc/log4go"
)

func main() {
	l4g.ChangeFilterLevel("stdout", l4g.INFO)
	cmd.Execute()
	//防止日志没有打印完
	time.Sleep(time.Duration(5) * time.Second)
}
