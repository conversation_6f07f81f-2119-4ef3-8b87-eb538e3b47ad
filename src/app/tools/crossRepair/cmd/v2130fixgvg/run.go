package v2130fixgvg

import (
	"app/tools/crossRepair/cmd/redis"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var fixFailedCount uint32

func Run(cmd *cobra.Command, args []string, crossRedisClient *redis.Redis) {
	fixGVG(crossRedisClient)
	if fixFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGVG Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGVG Failed, need check error log! failedCount:%d\n", fixFailedCount)
	}
}

func fixGVG(r *redis.Redis) {
	gstM, err := r.RopCr.GetGSTManagerSK(9)
	if err != nil {
		l4g.Errorf("GSTFIX. get GST error: %v, partition:%d", err, 9)
		fixFailedCount++
		return
	}
	l4g.Infof("GST data:%+v", gstM)
	if gstM == nil || gstM.Sta == nil || gstM.Sta.State == nil || gstM.Sta.State.SeasonId == 0 {
		l4g.Errorf("GST data error :%+v", gstM)
		fixFailedCount++
		return
	}
	gstM.Sta.State.RewardGuild = true
	err = r.RopCr.SetGSTManagerSK(9, gstM)
	if err != nil {
		l4g.Errorf("GSTFIX. set GST error: %v, partition:%d", err, 9)
		fixFailedCount++
		return
	}
}
