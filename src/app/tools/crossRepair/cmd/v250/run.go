package v250

import (
	"app/goxml"
	"app/logic/helper"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"
	"time"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath                = flag.String("cV250configData", "../data/", "service data path")
	fixGuildDivisionFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)

	fixGuildDivision(redisClient)

	l4g.Info("\n")
	if fixGuildDivisionFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildDivision Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildDivision Failed, need check error log! failedCount:%d\n", fixGuildDivisionFailedCount)
	}
	l4g.Info("finish repair")
}

func fixGuildDivision(r *redis.Redis) {
	r.RopCr.GetAllGuildMCall()
	r.RopCr.GetAllGuildDungeonMCall()

	guilds, err := r.RopCr.GetSomeGuildByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf(" fixGuildDivision: get guilds error:%s", err)
		fixGuildDivisionFailedCount++
		return
	}

	guildDungeons, err := r.RopCr.GetSomeGuildDungeonByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf(" fixGuildDivision: get guildDungeons error:%s", err)
		fixGuildDivisionFailedCount++
		return
	}

	changes := make([]*cr.GuildDungeon, 0, len(guildDungeons))

	for gid, dungeon := range guildDungeons {
		if dungeon.Star <= 2000 {
			continue
		}
		guild := guilds[gid]
		if guild == nil {
			l4g.Errorf(" fixGuildDivision: get guild nil. gid:%d", gid)
			continue
		}
		activityPoint := GetWeeklyActivityPoint(guild)
		if activityPoint < 3000 {
			info := goxml.GetData().GuildDungeonDivisionInoM.GetDivisionByStar(dungeon.GetStar())
			if info == nil {
				l4g.Errorf(" fixGuildDivision: get divisionInfo nil. star:%d", dungeon.GetStar())
				fixGuildDivisionFailedCount++
				return
			}
			resetInfo := goxml.GetData().GuildDungeonDivisionInoM.GetDivisionByStar(info.ResetPoint)
			if resetInfo == nil {
				l4g.Errorf(" fixGuildDivision: get divisionInfo nil. star:%d", info.ResetPoint)
				fixGuildDivisionFailedCount++
				return
			}
			dungeon.Division = resetInfo.Id
			dungeon.Star = info.ResetPoint
			changes = append(changes, dungeon)
		}
	}

	if len(changes) > 0 {
		err = r.RopCr.SetSomeGuildDungeon(changes)
		if err != nil {
			fixGuildDivisionFailedCount++
			l4g.Errorf("fixGuildDivision: save changed guildDungeon error:%s", err)
			return
		}
	}

	l4g.Info("fixGuildDivision success")
}

func GetWeeklyActivityPoint(g *cr.Guild) uint32 {
	timerResetDaily(g, int64(util.DailyZeroByTime(time.Now().Unix())))

	activityPoints := uint32(0)
	for _, member := range g.Members {
		activityPoints += GetMemberActivityPoint(g, member)
	}
	return activityPoints
}

func timerResetDaily(g *cr.Guild, dailyZero int64) bool {
	if dailyZero == g.DailyZero {
		return false
	}

	membersDailyReset(g, dailyZero)
	return true
}

func membersDailyReset(g *cr.Guild, nowDailyZero int64) {
	for _, member := range g.Members {
		memberResetWeeklyActivity(g, nowDailyZero, member)
	}
}

func memberResetWeeklyActivity(g *cr.Guild, nowDailyZero int64, member *cr.GuildMember) {
	//大于等于7天重制
	day := uint32((nowDailyZero - g.DailyZero) / util.DaySecs)
	if day >= 7 {
		member.ActivityPoint = make([]uint32, 7)
		return
	}
	weekDay := helper.WeekdayNoTransform(nowDailyZero)
	index := weekDay
	if len(member.ActivityPoint) < 7 {
		tmp := make([]uint32, 7)
		for index, v := range member.ActivityPoint {
			tmp[index] = v
		}
		member.ActivityPoint = tmp
	}
	for i := day; i > 0; i-- {
		member.ActivityPoint[index] = 0
		if index == 0 {
			index = 7
		}
		index--
	}
	return
}

func GetMemberActivityPoint(g *cr.Guild, member *cr.GuildMember) uint32 {
	if member == nil {
		return 0
	}
	points := uint32(0)
	for _, point := range member.ActivityPoint {
		points += point
	}
	return points
}
