package v190

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/out/common"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/util"
)

var (
	flagDataPath                            = flag.String("V190configData", "../data/", "service data path")
	towerSeasonResetFile                    = flag.String("towerSeasonResetPath", "./towerRankData", "rank file path")
	fixTowerSeasonRankFailed                uint32
	fixGuildDungeonRankResetTimeFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	fixTowerSeasonReset(redisClient)
	fixGuildDungeonRankResetTime(redisClient)

	if fixTowerSeasonRankFailed == 0 {
		fmt.Printf("[SUCCESS] fixTowerSeasonRank Success!\n")
	} else {
		fmt.Printf("[FAILED] Failed, need check error log! failedCount:%d\n", fixTowerSeasonRankFailed)
	}
	if fixGuildDungeonRankResetTimeFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildDungeonRankResetTime Success!\n")
	} else {
		fmt.Printf("[FAILED] Failed, need check error log! failedCount:%d\n", fixGuildDungeonRankResetTimeFailedCount)
	}

	l4g.Infof("finish repair")
}

func fixTowerSeasonReset(r *redis.Redis) {
	rankResetDatas, err := r.RopCr.GetAllRankResetSK(21)
	if err != nil {
		l4g.Errorf("getTowerSeasonRank: get towerSeasonReset error. err:%s", err)
		return
	}

	var changeRankReset []*cr.RankReset
	for _, data := range rankResetDatas {
		var isChange bool
		if data.BeginResetTime > 0 {
			dailyZero := util.DailyZeroByTime(int64(data.BeginResetTime))
			awardTm := dailyZero + uint32(common.TOWER_SEASON_TIME_TST_RESET_BEGIN)
			data.BeginResetTime = uint64(awardTm)
			isChange = true
		}

		if data.LastResetTime > 0 {
			dailyZero := util.DailyZeroByTime(int64(data.LastResetTime))
			awardTm := dailyZero + uint32(common.TOWER_SEASON_TIME_TST_RESET_BEGIN)
			data.LastResetTime = uint64(awardTm)
			isChange = true
		}

		if data.ResetTime > 0 {
			dailyZero := util.DailyZeroByTime(int64(data.ResetTime))
			awardTm := dailyZero + uint32(common.TOWER_SEASON_TIME_TST_RESET_BEGIN)
			data.ResetTime = uint64(awardTm)
			isChange = true
		}
		l4g.Infof("data:%v", data)
		if isChange {
			changeRankReset = append(changeRankReset, data)
		}
	}

	err = r.RopCr.SetSomeRankResetSK(21, changeRankReset)
	if err != nil {
		l4g.Errorf("setTowerSeasonRank: set towerSeasonReset error. err:%s", err)
		fixTowerSeasonRankFailed++
		return
	}
}

func fixGuildDungeonRankResetTime(r *redis.Redis) {
	r.RopCr.GetAllRankResetMCallSK(goxml.GuildDungeonChapterRankId)
	r.RopCr.GetAllRankResetMCallSK(goxml.GuildDungeonUserDamageRankId)

	chapterRankResetData, err := r.RopCr.GetSomeRankResetByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("getGuildDungeonChapterRank: get guildDungeonChapterRank error. err:%s", err)
		fixGuildDungeonRankResetTimeFailedCount++
		return
	}
	damageRankResetData, err := r.RopCr.GetSomeRankResetByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("getGuildDungeonDamageRank: get guildDungeonDamageRank error. err:%s", err)
		fixGuildDungeonRankResetTimeFailedCount++
		return
	}

	chapterRankChangeList := guildDungeonRankDataChangeResetTime(chapterRankResetData)
	damageRankChangeList := guildDungeonRankDataChangeResetTime(damageRankResetData)

	if len(chapterRankChangeList) > 0 {
		err = r.RopCr.SetSomeRankResetSK(goxml.GuildDungeonChapterRankId, chapterRankChangeList)
		if err != nil {
			l4g.Errorf("setChapterRank: set chapterRank error. err:%s", err)
			fixGuildDungeonRankResetTimeFailedCount++
			return
		}
	}
	if len(damageRankChangeList) > 0 {
		err = r.RopCr.SetSomeRankResetSK(goxml.GuildDungeonUserDamageRankId, damageRankChangeList)
		if err != nil {
			l4g.Errorf("setDamageRank: set damageRank error. err:%s", err)
			fixGuildDungeonRankResetTimeFailedCount++
			return
		}
	}
}

func guildDungeonRankDataChangeResetTime(data map[uint64]*cr.RankReset) []*cr.RankReset {
	changeList := make([]*cr.RankReset, 0, len(data))
	for _, resetData := range data {
		change := false
		if resetData.BeginResetTime > 0 {
			dailyZero := util.DailyZeroByTime(int64(resetData.BeginResetTime))
			newTm := dailyZero + uint32(common.GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_BEGIN)
			resetData.BeginResetTime = uint64(newTm)
			change = true
		}

		if resetData.LastResetTime > 0 {
			dailyZero := util.DailyZeroByTime(int64(resetData.LastResetTime))
			newTm := dailyZero + uint32(common.GUILD_DUNGEON_WEEKLY_TIME_WEEKLY_RESET_BEGIN)
			resetData.LastResetTime = uint64(newTm)
			change = true
		}
		if change {
			l4g.Infof("guildDungeonRankChange rankId:%d sid:%d", resetData.RankId, resetData.Sid)
			changeList = append(changeList, resetData)
		}
	}
	return changeList
}
