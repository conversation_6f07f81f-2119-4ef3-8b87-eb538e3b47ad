package v160_towerSeasonDel

import (
	"app/goxml"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath         = flag.String("V160configDataTowerSeasonDel", "../data/", "service data path")
	DelResetAwardFailed  uint32
	DelCurRandDataFailed uint32
	DelOldRandDataFailed uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	DelRankAndReset(redisClient)
	if DelResetAwardFailed == 0 {
		fmt.Printf("[SUCCESS] DelResetAward Success!\n")
	} else {
		fmt.Printf("[FAILED] DelResetAward Failed, need check error log! failedCount:%d\n", DelResetAwardFailed)
	}

	if DelCurRandDataFailed == 0 {
		fmt.Printf("[SUCCESS] DelCurRandData Success!\n")
	} else {
		fmt.Printf("[FAILED] DelCurRandData Failed, need check error log! failedCount:%d\n", DelCurRandDataFailed)
	}

	if DelOldRandDataFailed == 0 {
		fmt.Printf("[SUCCESS] DelOldRandData Success!\n")
	} else {
		fmt.Printf("[FAILED] DelOldRandData Failed, need check error log! failedCount:%d\n", DelOldRandDataFailed)
	}

	fmt.Printf("del rank data finish")
}

func DelRankAndReset(r *redis.Redis) {
	rankResetDatas, err := r.RopCr.GetAllRankResetSK(21)
	if err != nil {
		l4g.Errorf("getTowerSeasonRank: get towerSeasonReset error. err:%s", err)
		return
	}

	for _, v := range rankResetDatas {
		if v == nil {
			continue
		}
		nowKey := fmt.Sprintf("%d:%d", v.GetRankId(), v.GetSid())
		if err := r.RopCr.DelAllRankDataSKs(nowKey); err != nil {
			l4g.Errorf("del cur rank error: rank_id:%d sid:%d %s", v.GetRankId(), v.GetSid(), err)
			DelCurRandDataFailed++
			return
		}
		oldKey := fmt.Sprintf("%s:%d:%d", "old", v.GetRankId(), v.GetSid())
		if err := r.RopCr.DelAllRankDataSKs(oldKey); err != nil {
			l4g.Errorf("del old rank error: rank_id:%d sid:%d %s", v.GetRankId(), v.GetSid(), err)
			DelOldRandDataFailed++
			return
		}
	}

	if error := r.RopCr.DelAllRankResetSKs("21"); error != nil {
		l4g.Errorf("del rank reset failed id:%d error:%s", 21, error)
		DelResetAwardFailed++
		return
	}
}
