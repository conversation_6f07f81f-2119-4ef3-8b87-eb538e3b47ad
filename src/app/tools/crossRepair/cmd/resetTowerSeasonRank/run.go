package resetTowerSeasonRank

import (
	"app/goxml"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/ctx"

	l4g "github.com/ivanabc/log4go"
)

var (
	flagDataPath                    = flag.String("rTowerSeasonConfigData", "../data/", "service data path")
	resetTowerSeasonRankFailedCount uint32 //重置公会副本
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	delTowerSeasonRank(redisClient)

	l4g.Info("\n")
	if resetTowerSeasonRankFailedCount == 0 {
		fmt.Printf("[SUCCESS] resetTowerSeasonRank Success!\n")
	} else {
		fmt.Printf("[FAILED] resetTowerSeasonRank Failed, need check error log! failedCount:%d\n", resetTowerSeasonRankFailedCount)
	}

	l4g.Info("finish repair")
}

func delTowerSeasonRank(r *redis.Redis) {

	reply := r.Client.Cmd("redislv_keys", "crank:old:21*")
	list, err := reply.List()

	for _, key := range list {
		params := strings.Split(key, ":")
		newKey := fmt.Sprintf("old:%d:%s", goxml.TowerSeasonRankID, params[3])
		if err = r.RopCr.DelAllRankDataSKs(newKey); err != nil {
			l4g.Errorf("RankReset: rank_id:%d key:%s %s", goxml.TowerSeasonRankID, key, err)
			ctx.Stop()
			return
		}
	}

	reply = r.Client.Cmd("redislv_keys", "crank:21*")
	list, err = reply.List()

	for _, key := range list {
		params := strings.Split(key, ":")
		newKey := fmt.Sprintf("%d:%s", goxml.TowerSeasonRankID, params[2])
		if err = r.RopCr.DelAllRankDataSKs(newKey); err != nil {
			l4g.Errorf("RankReset: rank_id:%d key:%s %s", goxml.TowerSeasonRankID, key, err)
			ctx.Stop()
			return
		}
	}

	err = r.RopCr.DelAllRankResetSK(goxml.TowerSeasonRankID)
	if err != nil {
		l4g.Errorf("delTowerSeasonRank: del rankData error. rankId:%d err:%s",
			goxml.TowerSeasonRankID, err)
		resetTowerSeasonRankFailedCount++
		return
	}

	l4g.Info("delTowerSeasonRank success")
}
