package setWorldBossToPeakInit

import (
	"app/goxml"
	"app/logic/rank"
	"app/protos/in/cr"
	"app/protos/in/db"
	"app/tools/crossRepair/cmd/redis"
	"fmt"
	"sort"

	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/ctx"

	l4g "github.com/ivanabc/log4go"
)

var (
	partitionID uint32 = 1
	serverID    uint64 = 1000100002
	failedCount uint32
	curRankData []*cr.WorldBossUserToPeakArena
	// maxCount    = 128
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, logicRedisClient *redis.Redis) {
	if goxml.GetData().ServerInfoM.GM <= 0 {
		l4g.Errorf("online forbidden! 线上禁止使用测脚本!")
		return
	}
	partID, _ := cmd.Flags().GetInt("part")
	partitionID = uint32(partID)
	sid, _ := cmd.Flags().GetInt("sid")
	serverID = uint64(sid)
	count, _ := cmd.Flags().GetInt("count")
	memCount := uint32(count)
	worldBossID, _ := cmd.Flags().GetInt("worldBossID")
	dataKey := uint32(worldBossID)
	isReset, _ := cmd.Flags().GetBool("reset")
	fmt.Printf("partitionID:%d, serverID:%d, count:%d, worldBossID:%d, reset:%t \n",
		partitionID, serverID, count, dataKey, isReset)

	//清除当前报名数据
	if isReset {
		cleanData(redisClient, dataKey)
	}

	//计算设置当前报名数据
	calcCurRankData(redisClient)

	//构建新的报名数据
	datas := makeInitDatas(logicRedisClient, memCount, dataKey)
	for k, data := range datas {
		l4g.Debugf("k: %d, data count: %d", k, len(data.Users))
	}

	//执行保存
	doSet(redisClient, datas, dataKey)
	l4g.Info("\n")
	if failedCount == 0 {
		fmt.Printf("[SUCCESS] Success!\n")
	} else {
		fmt.Printf("[FAILED] Failed, need check error log! failedCount:%d\n", failedCount)
	}

	l4g.Info("finish repair")
}

// 获取当前用于巅峰报名的排行数据
func calcCurRankData(r *redis.Redis) {
	r.RopCr.GetAllWorldBossUserToPeakArenaMCallSK(partitionID)
	data, err := r.RopCr.GetSomeWorldBossUserToPeakArenaByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get current rank data error: %d %s", partitionID, err)
		ctx.Stop()
	}

	ret := make([]*cr.WorldBossUserToPeakArena, 0, 1)
	for _, v := range data {
		ret = append(ret, v)
	}
	curRankData = ret
}

// 通过世界boss的id获取当前报名数据
func getRankDataByID(worldID, memCount uint32) (*cr.WorldBossUserToPeakArena, uint32) {
	var maxRank uint32
	for _, v := range curRankData {
		if v.WorldId == worldID {
			for _, user := range v.Users {
				if user.PartitionRank > maxRank {
					maxRank = user.PartitionRank
				}
			}
			return v, maxRank
		}
	}

	return &cr.WorldBossUserToPeakArena{
		WorldId: worldID,
		Users:   make([]*cr.WorldBossUserToPeakArenaData, 0, memCount),
	}, 0
}

// 使用战力榜构造数据
func makeInitDatas(redisClient *redis.Redis, memCount, worldBossID uint32) []*cr.WorldBossUserToPeakArena {
	redisClient.RopDB.GetAllCommonRankMCallSK(uint64(goxml.PowerRankId))
	datas, err := redisClient.RopDB.GetSomeCommonRankByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("getPowerRank failed: %s", err.Error())
		return nil
	}

	if len(datas) < int(memCount) {
		l4g.Errorf("found datas len than required. count:%d, requied:%d, details:%+v",
			len(datas), memCount, datas)
		return nil
	}

	ret := make([]*cr.WorldBossUserToPeakArena, 0, 1)
	v, maxRank := getRankDataByID(worldBossID, memCount)
	makeData(datas, v, memCount, maxRank)
	ret = append(ret, v)
	return ret
}

// 构造报名数据
// @param ranks map[uint64]*db.CommonRank 排行榜数据
// @param data *cr.WorldBossUserToPeakArena 巅峰报名数据
// @param memCount uint32 内存中排行榜的数量
// @param maxRank uint32 最大排名
func makeData(ranks map[uint64]*db.CommonRank, data *cr.WorldBossUserToPeakArena, memCount, maxRank uint32) {
	sortData := make([]*rank.UserPower, 0, memCount)
	for _, v := range ranks {
		rankData := &rank.UserPower{}
		rankData.Load(v.Data)

		sortData = append(sortData, rankData)
	}

	sort.Slice(sortData, func(i, j int) bool {
		return sortData[i].Power > sortData[j].Power
	})

	var k uint32 = 1
	for _, v := range sortData {
		maxRank++
		data.Users = append(data.Users, &cr.WorldBossUserToPeakArenaData{
			Uid:           v.Uid,
			Sid:           serverID,
			PartitionRank: maxRank,
		})

		k++
		if k > memCount {
			break
		}
	}
}

// 清除数据
func cleanData(r *redis.Redis, worldBossID uint32) {
	r.RopCr.RemSomeWorldBossUserToPeakArenaMCallSK(uint32(partitionID), []uint32{worldBossID})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("cleanData.DelAllFightersBakMCall error: %s", reply.Err)
		failedCount++
		return
	}
	//清除巅峰的数据
	if err := r.RopCr.DelPeakSK(uint32(partitionID)); err != nil {
		l4g.Errorf("cleanData.DelPeakSk error: %s", err)
		failedCount++
		return
	}
}

// 执行更新数据
func doSet(r *redis.Redis, datas []*cr.WorldBossUserToPeakArena, worldBossID uint32) {
	//清除数据
	cleanData(r, worldBossID)

	//设置数据
	r.RopCr.SetSomeWorldBossUserToPeakArenaMCallSK(uint32(partitionID), datas)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("doSet.SetSomeWorldBossUserToPeakArenaMCallSK failed: data: %+v err: %s", datas, reply.Err)
		failedCount++
		return
	}

	l4g.Info("setWorldBossToPeakInit success")
}
