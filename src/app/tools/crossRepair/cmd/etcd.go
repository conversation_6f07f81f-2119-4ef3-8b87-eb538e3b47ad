package cmd

import (
	"app/goxml"
	"app/protos/in/config"
	"fmt"
	"strings"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"

	"context"

	"go.etcd.io/etcd/clientv3"
)

func getConfigFromEtcd(cfg goxml.EtcdConfig, logicID string) (*config.Cross, error) {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   strings.Split(cfg.Servers, ","),
		DialTimeout: time.Duration(cfg.DialTimeout) * time.Second,
	})
	if err != nil {
		return nil, err
	}
	defer cli.Close()

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(cfg.RequestTimeout)*time.Second)
	resp, err := cli.Get(ctx, cfg.CrossActivity+logicID)
	cancel()
	if err != nil {
		return nil, err
	}
	if len(resp.Kvs) != 1 {
		return nil, fmt.Errorf("%s no found value in etcd, kv size: %d", cfg.CrossActivity+logicID, len(resp.Kvs))
	}
	ev := resp.Kvs[0]
	ret := new(config.Cross)
	err = proto.Unmarshal(ev.Value, ret)
	if err != nil {
		return ret, err
	}
	l4g.Infof("service config in etcd: %+v", ret)
	if logicID != ret.CrossAddr {
		return nil, fmt.Errorf("etcd config logicID (%s <> %s) error", logicID, ret.CrossAddr)
	}
	return ret, nil
}
