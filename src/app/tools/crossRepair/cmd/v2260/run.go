package v2260

import (
	"app/protos/in/cr"
	"app/protos/out/cl"
	"app/tools/crossRepair/cmd/redis"
	"fmt"
	"slices"
	"strings"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var fixGstFailedCount uint32

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	fixGVG(redisClient)
	if fixGstFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGVG Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGVG Failed, need check error log! failedCount:%d\n", fixGstFailedCount)
	}
}

func fixGVG(r *redis.Redis) {
	fixGuildUser(r)
	fixGuild(r)
}

func fixGuildUser(r *redis.Redis) {
	ret := r.Client.Cmd("redislv_keys", "GST_GuildUser:*")
	keys, err := ret.List()
	if err != nil {
		l4g.Errorf("get keys GST_GuildUser error:%s", err)
		fixGstFailedCount++
		return
	}
	for _, key := range keys {
		ss := strings.Split(key, ":")
		if len(ss) != 2 {
			l4g.Errorf("split key error:%s", key)
			fixGstFailedCount++
			continue
		}

		userM, err := r.RopCr.GetAllGSTGuildUserManagerSKs(ss[1])
		if err != nil {
			l4g.Errorf("get user manager key:%s SKs error:%s", ss[1], err)
			fixGstFailedCount++
			continue
		}
		manager := make([]*cr.GSTGuildUserManager, 0)
		for _, u := range userM {
			if u.User != nil && u.User.Ore != nil {
				u.User.Ore.OreDatas = slices.DeleteFunc(u.User.Ore.OreDatas, func(ore *cl.GSTGuildUserOreData) bool {
					return ore.IsExclusive
				})
				u.User.Ore.FightTimes.CanTimes += 50
				manager = append(manager, u)
			}
		}
		if len(manager) > 0 {
			err = r.RopCr.SetSomeGSTGuildUserManagerSKs(ss[1], manager)
			if err != nil {
				l4g.Errorf("set user manager key:%s SKs error:%s", ss[1], err)
				fixGstFailedCount++
				continue
			}
		}
	}
}

func fixGuild(r *redis.Redis) {
	ret := r.Client.Cmd("redislv_keys", "GST_Guild:*")
	keys, err := ret.List()
	if err != nil {
		l4g.Errorf("get keys GST_Guild error:%s", err)
		fixGstFailedCount++
		return
	}
	for _, key := range keys {
		ss := strings.Split(key, ":")
		if len(ss) != 2 {
			l4g.Errorf("split key error:%s", key)
			fixGstFailedCount++
			continue
		}

		guildM, err := r.RopCr.GetAllGSTGuildManagerSKs(ss[1])
		if err != nil {
			l4g.Errorf("get guild manager key:%s SKs error:%s", ss[1], err)
			fixGstFailedCount++
			continue
		}
		manager := make([]*cr.GSTGuildManager, 0)
		for _, guild := range guildM {
			if guild.Guild != nil && guild.Guild.Tech != nil {
				if guild.Guild.Tech.SignId != 0 {
					guild.Guild.Tech.SignId = 0
					manager = append(manager, guild)
				}
			}
		}
		if len(manager) > 0 {
			err = r.RopCr.SetSomeGSTGuildManagerSKs(ss[1], manager)
			if err != nil {
				l4g.Errorf("set guild manager key:%s SKs error:%s", ss[1], err)
				fixGstFailedCount++
				continue
			}
		}
	}
}
