package worldBossRegister

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath         = flag.String("worldBossRegisterConfigData", "../data/", "service data path")
	worldBossFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, crossRedisClient, logicRedisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	worldBossRegister(crossRedisClient, logicRedisClient)

	l4g.Info("\n")
	if worldBossFailedCount == 0 {
		fmt.Printf("[SUCCESS] worldBoss Success!\n")
	} else {
		fmt.Printf("[FAILED] worldBoss Failed, need check error log! failedCount:%d\n", worldBossFailedCount)
	}

	l4g.Info("finish repair")
}

func worldBossRegister(crossRedisClient, logicRedisClient *redis.Redis) {
	logicRedisClient.RopDB.GetAllGlobalRankMCall()
	datas, err := logicRedisClient.RopDB.GetSomeGlobalRankByReply(logicRedisClient.Client.GetReply())
	if err != nil {
		worldBossFailedCount++
		l4g.Errorf("load global rank data is failed.")
		return
	}
	l4g.Info("global rank player num: %d", len(datas))
	// 5: 世界 boss 的玩法 id
	//moduleID := uint64(5)
	//actDatas, err := crossRedisClient.RopCr.GetAllActPartitionSK(moduleID)
	//if err != nil {
	//	worldBossFailedCount++
	//	l4g.Errorf("load actPartition data is failed.")
	//	return
	//}

	curPartition := uint32(1)
	//for _, v := range actDatas {
	//	curPartition = v.NowPart
	//	break
	//}

	targetNum := uint32(128)
	changes := make([]*cr.WorldBossUser, 0, targetNum)
	var num uint32
	for _, data := range datas {
		num++
		if num > targetNum {
			break
		}

		changes = append(changes, &cr.WorldBossUser{
			Uid:                   data.Id,
			Level:                 1,
			AccumulativeHurt:      10000,
			AccumulativeHurtScore: 100,
			MaxHurt:               10000,
			MaxHurtScore:          100,
			HurtCreateTime:        time.Now().Unix(),
		})
	}
	l4g.Info("curPartition: %d", curPartition)
	crossRedisClient.RopCr.SetSomeWorldBossUserMCallSK(curPartition, changes)
	if reply := crossRedisClient.Client.GetReply(); reply.Err != nil {
		worldBossFailedCount++
		l4g.Errorf("worldboss rigister error: %v", reply.Err)
		return
	}
	l4g.Info("worldBoss register success")
}
