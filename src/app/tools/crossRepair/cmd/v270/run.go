package v270

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath             = flag.String("cV270configData", "../data/", "service data path")
	fixGuildMedalFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)

	fixGuildMedal(redisClient)

	l4g.Info("\n")
	if fixGuildMedalFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildMedal Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildMedal Failed, need check error log! failedCount:%d\n", fixGuildMedalFailedCount)
	}
	l4g.Info("finish repair")
}

func fixGuildMedal(r *redis.Redis) {
	r.RopCr.GetAllGuildMCall()

	guilds, err := r.RopCr.GetSomeGuildByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf(" fixGuildMedal: get guilds error:%s", err)
		fixGuildMedalFailedCount++
		return
	}

	changes := make([]*cr.Guild, 0, len(guilds))

	for _, guild := range guilds {
		if guild.Medal != nil {
			guild.Medal = nil
			changes = append(changes, guild)
		}
	}

	if len(changes) > 0 {
		err = r.RopCr.SetSomeGuild(changes)
		if err != nil {
			fixGuildMedalFailedCount++
			l4g.Errorf("fixGuildMedal: save changed guild error:%s", err)
			return
		}
	}

	l4g.Info("fixGuildMedal success")
}
