package v250tw

import (
	"app/tools/crossRepair/cmd/redis"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	fixPeakFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	fixPeak(redisClient)

	l4g.Info("\n")
	if fixPeakFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixPeak Success!\n")
	} else {
		fmt.Printf("[FAILED] fixPeak Failed, need check error log! failedCount:%d\n", fixPeakFailedCount)
	}
	l4g.Info("finish repair")
}

func fixPeak(r *redis.Redis) {
	partition := uint32(1) //战区id
	r.RopCr.DelPeakMCallSK(partition)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("fixPeak.DelPeakMCallSK error: %s", reply.Err)
		fixPeakFailedCount++
		return
	}

	r.RopCr.DelAllPeakFighterMCallSK(partition)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("fixPeak.DelAllPeakFighterMCallSK error: %s", reply.Err)
		fixPeakFailedCount++
		return
	}

	r.RopCr.DelAllPeakMatchMCallSK(partition)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("fixPeak.DelAllPeakMatchMCallSK error: %s", reply.Err)
		fixPeakFailedCount++
		return
	}

	l4g.Info("fixPeak success")
}
