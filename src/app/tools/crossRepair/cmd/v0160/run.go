package v0160

import (
	"app/cross/db"
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath            = flag.String("V0160configData", "../data/", "service data path")
	fixGuildNameFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	fixGuildName(redisClient)

	l4g.Info("\n")
	if fixGuildNameFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildName Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildName Failed, need check error log! failedCount:%d\n", fixGuildNameFailedCount)
	}

	l4g.Info("finish repair")
}

func fixGuildName(r *redis.Redis) {

	guilds, err := r.RopCr.GetAllGuild()
	if err != nil {
		l4g.Errorf("fixGuildName: get guild error. err:%s", err)
		fixGuildNameFailedCount++
		return
	}

	nameToId := make(map[string]uint64)

	changeGuilds := make([]*cr.Guild, 0, len(guilds)/4)
	changeGids := make([]uint64, 0, len(guilds)/4)

	for gid, guild := range guilds {
		if _, exist := nameToId[guild.Name]; exist {
			changeGids = append(changeGids, gid)
			guild.Name = fmt.Sprintf("%d", gid)
			l4g.Infof("changeNameGuild: gid:%d sid:%d leader:%d", gid, guild.Sid, guild.Leader)
			changeGuilds = append(changeGuilds, guild)
		}
		nameToId[guild.Name] = gid
	}

	dungeons, err := r.RopCr.GetSomeGuildDungeon(changeGids)
	if err != nil {
		l4g.Errorf("fixGuildName: get dungeon error. err:%s", err)
		fixGuildNameFailedCount++
		return
	}

	changeDungeons := make([]*cr.GuildDungeon, 0, len(changeGuilds))

	for _, guild := range changeGuilds {
		dungeon := dungeons[guild.Id]
		if dungeon == nil {
			l4g.Errorf("fixGuildName: get dungeon error. err:%s", err)
			continue
		}
		dungeon.Name = guild.Name
		changeDungeons = append(changeDungeons, dungeon)
	}

	calls := 0
	r.RopCr.SetSomeGuildMCall(changeGuilds)
	calls++
	r.RopCr.SetSomeGuildDungeonMCall(changeDungeons)
	calls++

	for name, gid := range nameToId {
		gidStr := fmt.Sprintf("%d", gid)
		r.Client.Append("HSETNX", db.RedisHashGuildName, name, gidStr)
		calls++
	}

	haveError := false
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Error("save guild error: %s %s", reply.Err)
			haveError = true
		}
	}

	if haveError {
		l4g.Errorf("fixGuildName: save error. err:%s", err)
		fixGuildNameFailedCount++
		return
	}

	l4g.Info("fixGuildName success")
}
