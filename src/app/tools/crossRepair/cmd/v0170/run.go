package v0170

import (
	"app/goxml"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath          = flag.String("V0170configData", "../data/", "service data path")
	delWrestleFailedCount uint32 //删除跨服竞技场公共数据
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	delWrestle(redisClient)

	l4g.Info("\n")
	if delWrestleFailedCount == 0 {
		fmt.Printf("[SUCCESS] delWrestle Success!\n")
	} else {
		fmt.Printf("[FAILED] delWrestle Failed, need check error log! failedCount:%d\n", delWrestleFailedCount)
	}

	l4g.Info("finish repair")
}

// 删除跨服竞技场公共数据
func delWrestle(r *redis.Redis) {
	for i := uint64(1); i <= 15; i++ {
		key := strconv.FormatUint(i, 10)
		r.RopCr.DelActBaseMCallSKs(key)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("delWrestle.DelActBaseMCallSKs error: %s, key: %s", reply.Err, key)
			delWrestleFailedCount++
			return
		}

		r.RopCr.DelAllFighterMCallSKs(key)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("delWrestle.DelAllFighterMCallSKs error: %s, key: %s", reply.Err, key)
			delWrestleFailedCount++
			return
		}

		r.RopCr.DelMatcherMCallSKs(key)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("delWrestle.DelMatcherMCallSKs error: %s, key: %s", reply.Err, key)
			delWrestleFailedCount++
			return
		}
	}

	r.RopCr.DelAllFightersBakMCall()
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delWrestle.DelAllFightersBakMCall error: %s", reply.Err)
		delWrestleFailedCount++
		return
	}

	r.RopCr.DelAllWrestleLastSeasonTopMCall()
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delWrestle.DelAllWrestleLastSeasonTopMCall error: %s", reply.Err)
		delWrestleFailedCount++
		return
	}
	l4g.Info("delWrestle success")
}
