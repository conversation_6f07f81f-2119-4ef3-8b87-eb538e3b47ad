package v290

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath                = flag.String("cV290configData", "../data/", "service data path")
	fixGuildDivisionFailedCount uint32

	needFixDivisionGuildIds = []uint64{
		154811237320200083,
		155374187274212900,
		159877786900390771,
		145804038038002060,
		137359788635515912,
		68116944365309691,
		67553994411568141,
		54606145562319571,
		62487444830660501,
		73746444388999165,
		61361544923410262,
		68116944364498050,
		65865144551505168,
		67553994411002866,
		77687094065093418,
		57983845203942018,
		52917297523001857,
		67553994412022647,
		67553994899322351,
		60798594969997978,
		52917297520754480,
		70931694620947883,
		54606145558853382,
		58546795156023606,
		86131343361746945,
		92323792614426648,
		88946093128947229,
		99079192052878129,
		104145741632830161,
		104708691587211263,
		88383143175659888,
		88946093380227366,
		83316593111690858,
		90071993036487652,
		99642142008201556,
		165507287057001179,
		169447936731394473,
		175640385469789744,
		179581035769868024,
		109212291441430606,
		121034239991684888,
		113152941007449200,
		113715890597669196,
		128915539546634161,
		109212290966613083,
		112027040734095414,
		110901140828227117,
		110901140828365686,
		109775240920579544,
		108649341013308097,
		118219490222011208,
		38280596836089809,
		24769797957943899,
		41658296557530049,
		24206847998767038,
		32088147347747056,
		29273397581904334,
		25895697861883643,
		44473046326005917,
		30399297488088808,
		40532396652212415,
		36028797026131177,
		41658296563010874,
		33776997209288550,
		25332747906006234,
		37717646883102349,
		39406496744147738,
		28710447627650934,
		36591746975596452,
		32651097304774027,
		32088147348990012,
		47287796091932570,
		24769797953582461,
		43347146422273467,
		24206847999947459,
		34902897115794792,
		35465847070608650,
		30962247442153582,
		28147497677301966,
		27021597767116929,
		27584547722538720,
		31525197395296958,
		48413695998807117,
		25332747907480470,
		24206848002094267,
		29273397582778383,
		24769797959870945,
		28710447629350858,
		33214048435992427,
		31525197393811633,
		34339947164606982,
		47850746045421689,
		21392098278836843,
		10696049181671119,
		17451448644195350,
		562949953476436,
		23080948124579510,
		20829148279142944,
		3377699721265164,
	}
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)

	fixGuildDivision(redisClient)

	l4g.Info("\n")
	if fixGuildDivisionFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildDivision Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildDivision Failed, need check error log! failedCount:%d\n", fixGuildDivisionFailedCount)
	}
	l4g.Info("finish repair")
}

func fixGuildDivision(r *redis.Redis) {

	dungeonData, err := r.RopCr.GetSomeGuildDungeon(needFixDivisionGuildIds)
	if err != nil {
		l4g.Errorf(" fixGuildDivision: get dungeon error:%s", err)
		fixGuildDivisionFailedCount++
		return
	}

	changes := make([]*cr.GuildDungeon, 0, len(dungeonData))

	for _, dungeon := range dungeonData {
		division, star := goxml.GetData().GuildDungeonDivisionInoM.CalcResetDivisionAndPoint(dungeon.Star)
		if division == 0 || star == 0 {
			continue
		}
		dungeon.Division = division
		dungeon.Star = star
		if dungeon.SeasonTopDivision > dungeon.Division {
			dungeon.SeasonTopDivision = dungeon.Division
		}
		changes = append(changes, dungeon)
	}

	if len(changes) > 0 {
		err = r.RopCr.SetSomeGuildDungeon(changes)
		if err != nil {
			fixGuildDivisionFailedCount++
			l4g.Errorf("fixGuildDivision: save changed guildDungeon error:%s", err)
			return
		}
	}

	l4g.Info("fixGuildDivision success")
}
