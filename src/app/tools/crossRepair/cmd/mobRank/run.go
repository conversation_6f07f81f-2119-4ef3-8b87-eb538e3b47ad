package mobRank

import (
	"app/cross/activity"
	"app/cross/activity/gst"
	"app/goxml"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"
	"strings"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath = flag.String("mobRankConfigData", "../data/", "service data path")
)

func Run(cmd *cobra.Command, args []string, gstRedisClient, guildRedisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	getRank(gstRedisClient, guildRedisClient)
}

func getRank(gstRedisClient, guildRedisClient *redis.Redis) {
	ret := gstRedisClient.Client.Cmd("redislv_keys", "GST_Mob_Rank:*")
	keys, err := ret.List()
	if err != nil {
		panic(fmt.Sprintf("get keys GST_Mob_Rank error:%s", err))
	}
	for _, key := range keys {
		ss := strings.Split(key, ":")
		if len(ss) != 2 {
			panic(fmt.Sprintf("split key error:%s", key))
		}
		mobRankM, err := gstRedisClient.RopCr.GetAllGSTMobRankManagerSKs(ss[1])
		if err != nil {
			panic(fmt.Sprintf("GetAllGSTMobRankManagerSKs key:%s SKs error:%s", ss[1], err))
		}
		mobManager := gst.NewGuildMobRankManager(nil)

		for _, data := range mobRankM {
			mobManager.UpdateRank(data.Info)
		}
		rankList := mobManager.GetAllRankData()
		for _, v := range rankList {
			guild, err := guildRedisClient.RopCr.GetGuild(v.Id)
			if err != nil {
				panic(fmt.Sprintf("GetGuild key:%d error:%s", v.Id, err))
			}
			if guild == nil {
				l4g.Errorf("GetGuild key:%d is nil", v.Id)
				continue
			}
			for _, member := range guild.Members {
				l4g.Infof("玩家:%d,服务器:%d,公会:%d,公会排名:%d,会长奖励:%t",
					member.Id, member.Sid, v.Id, v.Param1,
					member.Grade == activity.GuildGradeLeader || member.Grade == activity.GuildGradeDeputy)
			}
		}
	}
}
