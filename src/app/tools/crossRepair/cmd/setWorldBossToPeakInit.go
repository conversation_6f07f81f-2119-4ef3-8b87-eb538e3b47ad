package cmd

import (
	"app/tools/crossRepair/cmd/setWorldBossToPeakInit"

	"github.com/spf13/cobra"
)

var vSetWorldBossToPeakInit = &cobra.Command{
	Use:   "setWorldBossToPeakInit",
	Short: "设置用于巅峰竞技场报名的世界boss数据",
	Run: func(cmd *cobra.Command, args []string) {
		setWorldBossToPeakInit.Run(cmd, args, redisClient, logicRedisClient)
	},
}

func init() {
	vSetWorldBossToPeakInit.Flags().Int("part", 1, "分区id")
	vSetWorldBossToPeakInit.Flags().Int("sid", 1000100002, "服务器id")
	vSetWorldBossToPeakInit.Flags().Int("count", 128, "报名人数")
	vSetWorldBossToPeakInit.Flags().Int("worldBossID", 5, "世界boss量表id")
	vSetWorldBossToPeakInit.Flags().Bool("reset", false, "是否重置报名数据")
	rootCmd.AddCommand(vSetWorldBossToPeakInit)
}
