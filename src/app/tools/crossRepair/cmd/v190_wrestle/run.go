package v190_wrestle

import (
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	fixWrestleFailedCount uint32 //修改神树争霸结算时间
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	fixWrestle(redisClient)
	if fixWrestleFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixWrestle Success!\n")
	} else {
		fmt.Printf("[FAILED] fixWrestle Failed, need check error log! failedCount:%d\n", fixWrestleFailedCount)
	}

	l4g.Infof("finish repair")
}

const FixSecs int64 = 3600
const WrestleID = 2

// 修改神树争霸结算时间
func fixWrestle(r *redis.Redis) {
	//cw_m
	for i := uint32(1); i <= 36; i++ {
		matcher, err := r.RopCr.GetMatcherSK(i)
		if err != nil {
			l4g.Errorf("fixWrestle.GetMatcherSK error: %v", err)
			fixWrestleFailedCount++
			return
		}

		if matcher != nil {
			if matcher.DailyResetTime > 0 {
				matcher.DailyResetTime -= FixSecs
			}
			if matcher.PartitionResetTime > 0 {
				matcher.PartitionResetTime -= FixSecs
			}
			if matcher.SeasonResetTime > 0 {
				matcher.SeasonResetTime -= FixSecs
			}

			r.RopCr.SetMatcherMCallSK(i, matcher)
			if reply := r.Client.GetReply(); reply.Err != nil {
				l4g.Errorf("fixWrestle.SetMatcherMCallSK error:%v i:%d msg:%v", reply.Err, i, matcher)
				fixWrestleFailedCount++
				return
			}
		}
	}

	//cw_fb
	bakData, err := r.RopCr.GetAllFightersBak()
	if err != nil {
		l4g.Errorf("fixWrestle.GetAllFightersBak error: %v", err)
		fixWrestleFailedCount++
		return
	}
	for _, data := range bakData {
		if data.Time > 0 {
			newData := data.Clone()
			newData.Time -= FixSecs
			r.RopCr.SetSomeFightersBakMCall([]*cr.FightersBak{newData})
			if reply := r.Client.GetReply(); reply.Err != nil {
				l4g.Errorf("fixWrestle.SetSomeFightersBakMCall error:%v sid:%d msg:%v", reply.Err, data.Sid, newData)
				fixWrestleFailedCount++
				return
			}
		}
	}

	//actb
	actBase, err := r.RopCr.GetActBaseSK(WrestleID)
	if err != nil {
		l4g.Errorf("fixWrestle.GetActBaseSK error: %v", err)
		fixWrestleFailedCount++
		return
	}
	if actBase.ResetTime > 0 {
		actBase.ResetTime -= uint64(FixSecs)
		r.RopCr.SetActBaseMCallSK(WrestleID, actBase)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("fixWrestle.SetActBaseMCallSK error:%v msg:%v", reply.Err, actBase)
			fixWrestleFailedCount++
			return
		}
	}

	l4g.Info("fixWrestle success")
}
