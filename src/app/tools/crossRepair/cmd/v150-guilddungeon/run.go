package v150_guilddungeon

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath                    = flag.String("cV150GuildDungeonConfigData", "../data/", "service data path")
	fixGuildDungeonResetFailedCount uint32
	// lastWeekResetTime               int64 = 1699227000 // UTC+0 的 2023-11-05 23:30:00
	// lastSeasonResetTime             int64 = 1690759800 // UTC+0 的 2023-07-30 23:30:00
)

// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	fixGuildDungeonReset(redisClient)

	l4g.Info("\n")
	if fixGuildDungeonResetFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildDungeonReset Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildDungeonReset Failed, need check error log! failedCount:%d\n", fixGuildDungeonResetFailedCount)
	}

	l4g.Info("finish repair")
}

func fixGuildDungeonReset(r *redis.Redis) {

	lastWeekResetTime := goxml.GetGuildDungeonWeeklyResetTime(time.Now().Unix()) - 2*util.WeekSecs
	lastSeasonResetTime := goxml.GetGuildDungeonSeasonResetTime(goxml.GetData(), time.Now().Unix()) -
		2*int64(goxml.GetData().GuildConfigInfoM.GetGuildDungeonSeasonRound())*util.WeekSecs

	resetData, err := r.RopCr.GetAllGuildDungeonReset()

	if err != nil {
		l4g.Errorf("fixGuildDungeonReset: get guildDungeonReset error. err:%s", err)
		fixGuildDungeonResetFailedCount++
		return
	}

	change := make([]*cr.GuildDungeonReset, 0, len(resetData))
	for _, data := range resetData {
		data.LastWeeklyResetTime = lastWeekResetTime
		data.LastSeasonResetTime = lastSeasonResetTime
		change = append(change, data)
	}

	calls := 0
	r.RopCr.SetSomeGuildDungeonResetMCall(change)
	calls++
	//r.RopCr.Append("hset", "guild_dungeon_season_id", "id", 2)
	//calls++

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Error("save guildDungeonReset error: %s %s", reply.Err)
			fixGuildDungeonResetFailedCount++
			return
		}
	}

	l4g.Info("fixGuildDungeonReset success")
}
