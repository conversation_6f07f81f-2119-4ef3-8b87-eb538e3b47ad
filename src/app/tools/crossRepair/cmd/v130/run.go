package v130

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath                    = flag.String("cV130configData", "../data/", "service data path")
	fixGuildDungeonResetFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	fixGuildDungeonReset(redisClient)

	l4g.Info("\n")
	if fixGuildDungeonResetFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildDungeonReset Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildDungeonReset Failed, need check error log! failedCount:%d\n", fixGuildDungeonResetFailedCount)
	}

	l4g.Info("finish repair")
}

func fixGuildDungeonReset(r *redis.Redis) {

	resetData, err := r.RopCr.GetAllGuildDungeonReset()

	if err != nil {
		l4g.Errorf("fixGuildDungeonReset: get guildDungeonReset error. err:%s", err)
		fixGuildDungeonResetFailedCount++
		return
	}

	change := make([]*cr.GuildDungeonReset, 0, len(resetData))
	for _, data := range resetData {
		if data.SeasonId != 2 {
			data.SeasonId = 2
			change = append(change, data)
		}
	}

	calls := 0
	r.RopCr.SetSomeGuildDungeonResetMCall(change)
	calls++
	r.RopCr.Append("hset", "guild_dungeon_season_id", "id", 2)
	calls++

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Error("save guildDungeonReset error: %s %s", reply.Err)
			fixGuildDungeonResetFailedCount++
			return
		}
	}

	l4g.Info("fixGuildDungeonReset success")
}
