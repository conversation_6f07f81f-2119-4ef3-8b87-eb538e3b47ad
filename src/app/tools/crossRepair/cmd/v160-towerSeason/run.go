package v160_towerSeason

import (
	"app/cross/activity/rank"
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"encoding/json"
	"flag"
	"fmt"
	"os"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath               = flag.String("V160configData", "../data/", "service data path")
	towerSeasonRankFile        = flag.String("towerSeasonRankFilePath", "./towerRankData", "rank file path")
	getTowerSeasonRankFailed   uint32
	umMarshalServerPartsFailed uint32
)

type userTowerSeasonRank struct {
	Rank  uint32 `json:"Rank"`
	Uid   uint64 `json:"Uid"`
	Sid   uint64 `json:"Sid"`
	Name  string `json:"Name"`
	Floor uint64 `json:"Floor"`
	Tm    uint64 `json:"Tm"`
	Part  uint32 `json:"Part"`
}

type serverPartDatas struct {
	Code int `json:"code"`
	Data struct {
		List []struct {
			Id       uint64 `json:"id"`
			Name     string `json:"name"`
			LastPart int    `json:"last_part"`
			NowPart  uint32 `json:"now_part"`
			NextPart int    `json:"next_part"`
		} `json:"list"`
		Status int `json:"status"`
		Total  int `json:"total"`
	} `json:"data"`
	Msg string `json:"msg"`
}

var serverPartString string = "{\n    \"code\": 200,\n    \"data\": {\n        \"list\": [\n            {\n                \"id\": 1000300001,\n                \"name\": \"T1\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300002,\n                \"name\": \"T2\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300003,\n                \"name\": \"T3\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300004,\n                \"name\": \"T4\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300005,\n                \"name\": \"T5\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300006,\n                \"name\": \"T6\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300007,\n                \"name\": \"T7\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300008,\n                \"name\": \"T8\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300009,\n                \"name\": \"T9\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300010,\n                \"name\": \"T10\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300011,\n                \"name\": \"T11\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300012,\n                \"name\": \"T12\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300013,\n                \"name\": \"T13\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300014,\n                \"name\": \"T14\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300015,\n                \"name\": \"T15\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300016,\n                \"name\": \"T16\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300017,\n                \"name\": \"T17\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300018,\n                \"name\": \"T18\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300019,\n                \"name\": \"T19\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300020,\n                \"name\": \"T20\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300021,\n                \"name\": \"T21\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300022,\n                \"name\": \"T22\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300023,\n                \"name\": \"T23\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300024,\n                \"name\": \"T24\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300025,\n                \"name\": \"T25\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300026,\n                \"name\": \"T26\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300027,\n                \"name\": \"T27\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300028,\n                \"name\": \"T28\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300029,\n                \"name\": \"T29\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300030,\n                \"name\": \"T30\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300031,\n                \"name\": \"T31\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300032,\n                \"name\": \"T32\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300033,\n                \"name\": \"T33\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300034,\n                \"name\": \"T34\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300035,\n                \"name\": \"T35\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300036,\n                \"name\": \"T36\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300037,\n                \"name\": \"T37\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300038,\n                \"name\": \"T38\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300039,\n                \"name\": \"T39\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300040,\n                \"name\": \"T40\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300041,\n                \"name\": \"T41\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300042,\n                \"name\": \"T42\",\n                \"last_part\": 0,\n                \"now_part\": 1,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300043,\n                \"name\": \"S1\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300044,\n                \"name\": \"S2\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300045,\n                \"name\": \"S3\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300046,\n                \"name\": \"S4\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300047,\n                \"name\": \"S5\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300048,\n                \"name\": \"S6\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300049,\n                \"name\": \"S7\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300050,\n                \"name\": \"S8\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300051,\n                \"name\": \"S9\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300052,\n                \"name\": \"S10\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300053,\n                \"name\": \"S11\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300054,\n                \"name\": \"S12\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300055,\n                \"name\": \"S13\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300056,\n                \"name\": \"S14\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300057,\n                \"name\": \"S15\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300058,\n                \"name\": \"S16\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300059,\n                \"name\": \"S17\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300060,\n                \"name\": \"S18\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300061,\n                \"name\": \"S19\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300062,\n                \"name\": \"S20\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300063,\n                \"name\": \"S21\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300064,\n                \"name\": \"S22\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300065,\n                \"name\": \"S23\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300066,\n                \"name\": \"S24\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300067,\n                \"name\": \"S25\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300068,\n                \"name\": \"S26\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300069,\n                \"name\": \"S27\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300070,\n                \"name\": \"S28\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300071,\n                \"name\": \"S29\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300072,\n                \"name\": \"S30\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300073,\n                \"name\": \"S31\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300074,\n                \"name\": \"S32\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300075,\n                \"name\": \"S33\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300076,\n                \"name\": \"S34\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300077,\n                \"name\": \"S35\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300078,\n                \"name\": \"S36\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300079,\n                \"name\": \"S37\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300080,\n                \"name\": \"S38\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300081,\n                \"name\": \"S39\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300082,\n                \"name\": \"S40\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300083,\n                \"name\": \"S41\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300084,\n                \"name\": \"S42\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300085,\n                \"name\": \"S43\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300086,\n                \"name\": \"S44\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300087,\n                \"name\": \"S45\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300088,\n                \"name\": \"S46\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300089,\n                \"name\": \"S47\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300090,\n                \"name\": \"S48\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300091,\n                \"name\": \"S49\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300092,\n                \"name\": \"S50\",\n                \"last_part\": 0,\n                \"now_part\": 2,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300093,\n                \"name\": \"S51\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300094,\n                \"name\": \"S52\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300095,\n                \"name\": \"S53\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300096,\n                \"name\": \"S54\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300097,\n                \"name\": \"S55\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300098,\n                \"name\": \"S56\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300099,\n                \"name\": \"S57\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300100,\n                \"name\": \"S58\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300101,\n                \"name\": \"S59\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300102,\n                \"name\": \"S60\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300103,\n                \"name\": \"S61\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300104,\n                \"name\": \"S62\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300105,\n                \"name\": \"S63\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300106,\n                \"name\": \"S64\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300107,\n                \"name\": \"S65\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300108,\n                \"name\": \"S66\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300109,\n                \"name\": \"S67\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300110,\n                \"name\": \"S68\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300111,\n                \"name\": \"S69\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300112,\n                \"name\": \"S70\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300113,\n                \"name\": \"S71\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300114,\n                \"name\": \"S72\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300115,\n                \"name\": \"S73\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300116,\n                \"name\": \"S74\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300117,\n                \"name\": \"S75\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300118,\n                \"name\": \"S76\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300119,\n                \"name\": \"S77\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300120,\n                \"name\": \"S78\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300121,\n                \"name\": \"S79\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300122,\n                \"name\": \"S80\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300123,\n                \"name\": \"S81\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300124,\n                \"name\": \"S82\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300125,\n                \"name\": \"S83\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300126,\n                \"name\": \"S84\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300127,\n                \"name\": \"S85\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300128,\n                \"name\": \"S86\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300129,\n                \"name\": \"S87\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300130,\n                \"name\": \"S88\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300131,\n                \"name\": \"S89\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300132,\n                \"name\": \"S90\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300133,\n                \"name\": \"S91\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300134,\n                \"name\": \"S92\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300135,\n                \"name\": \"S93\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300136,\n                \"name\": \"S94\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300137,\n                \"name\": \"S95\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300138,\n                \"name\": \"S96\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300139,\n                \"name\": \"S97\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300140,\n                \"name\": \"S98\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300141,\n                \"name\": \"S99\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300142,\n                \"name\": \"S100\",\n                \"last_part\": 0,\n                \"now_part\": 3,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300143,\n                \"name\": \"S101\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300144,\n                \"name\": \"S102\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300145,\n                \"name\": \"S103\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300146,\n                \"name\": \"S104\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300147,\n                \"name\": \"S105\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300148,\n                \"name\": \"S106\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300149,\n                \"name\": \"S107\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300150,\n                \"name\": \"S108\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300151,\n                \"name\": \"S109\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300152,\n                \"name\": \"S110\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300153,\n                \"name\": \"S111\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300154,\n                \"name\": \"S112\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300155,\n                \"name\": \"S113\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300156,\n                \"name\": \"S114\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300157,\n                \"name\": \"S115\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300158,\n                \"name\": \"S116\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300159,\n                \"name\": \"S117\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300160,\n                \"name\": \"S118\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300161,\n                \"name\": \"S119\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300162,\n                \"name\": \"S120\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300163,\n                \"name\": \"S121\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300164,\n                \"name\": \"S122\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300165,\n                \"name\": \"S123\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300166,\n                \"name\": \"S124\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300167,\n                \"name\": \"S125\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300168,\n                \"name\": \"S126\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300169,\n                \"name\": \"S127\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300170,\n                \"name\": \"S128\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300171,\n                \"name\": \"S129\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300172,\n                \"name\": \"S130\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300173,\n                \"name\": \"S131\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300174,\n                \"name\": \"S132\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300175,\n                \"name\": \"S133\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300176,\n                \"name\": \"S134\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300177,\n                \"name\": \"S135\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300178,\n                \"name\": \"S136\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300179,\n                \"name\": \"S137\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300180,\n                \"name\": \"S138\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300181,\n                \"name\": \"S139\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300182,\n                \"name\": \"S140\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300183,\n                \"name\": \"S141\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300184,\n                \"name\": \"S142\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300185,\n                \"name\": \"S143\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300186,\n                \"name\": \"S144\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300187,\n                \"name\": \"S145\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300188,\n                \"name\": \"S146\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300189,\n                \"name\": \"S147\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300190,\n                \"name\": \"S148\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300191,\n                \"name\": \"S149\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300192,\n                \"name\": \"S150\",\n                \"last_part\": 0,\n                \"now_part\": 4,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300193,\n                \"name\": \"S151\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300194,\n                \"name\": \"S152\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300195,\n                \"name\": \"S153\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300196,\n                \"name\": \"S154\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300197,\n                \"name\": \"S155\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300198,\n                \"name\": \"S156\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300199,\n                \"name\": \"S157\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300200,\n                \"name\": \"S158\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300201,\n                \"name\": \"S159\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300202,\n                \"name\": \"S160\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300203,\n                \"name\": \"S161\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300204,\n                \"name\": \"S162\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300205,\n                \"name\": \"S163\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300206,\n                \"name\": \"S164\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300207,\n                \"name\": \"S165\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300208,\n                \"name\": \"S166\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300209,\n                \"name\": \"S167\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300210,\n                \"name\": \"S168\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300211,\n                \"name\": \"S169\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300212,\n                \"name\": \"S170\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300213,\n                \"name\": \"S171\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300214,\n                \"name\": \"S172\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300215,\n                \"name\": \"S173\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300216,\n                \"name\": \"S174\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300217,\n                \"name\": \"S175\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300218,\n                \"name\": \"S176\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300219,\n                \"name\": \"S177\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300220,\n                \"name\": \"S178\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300221,\n                \"name\": \"S179\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300222,\n                \"name\": \"S180\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300223,\n                \"name\": \"S181\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300224,\n                \"name\": \"S182\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300225,\n                \"name\": \"S183\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300226,\n                \"name\": \"S184\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300227,\n                \"name\": \"S185\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300228,\n                \"name\": \"S186\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300229,\n                \"name\": \"S187\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300230,\n                \"name\": \"S188\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300231,\n                \"name\": \"S189\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300232,\n                \"name\": \"S190\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300233,\n                \"name\": \"S191\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300234,\n                \"name\": \"S192\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300235,\n                \"name\": \"S193\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300236,\n                \"name\": \"S194\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300237,\n                \"name\": \"S195\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300238,\n                \"name\": \"S196\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300239,\n                \"name\": \"S197\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300240,\n                \"name\": \"S198\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300241,\n                \"name\": \"S199\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300242,\n                \"name\": \"S200\",\n                \"last_part\": 0,\n                \"now_part\": 5,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300243,\n                \"name\": \"S201\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300244,\n                \"name\": \"S202\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300245,\n                \"name\": \"S203\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300246,\n                \"name\": \"S204\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300247,\n                \"name\": \"S205\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300248,\n                \"name\": \"S206\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300249,\n                \"name\": \"S207\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300250,\n                \"name\": \"S208\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300251,\n                \"name\": \"S209\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300252,\n                \"name\": \"S210\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300253,\n                \"name\": \"S211\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300254,\n                \"name\": \"S212\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300255,\n                \"name\": \"S213\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300256,\n                \"name\": \"S214\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300257,\n                \"name\": \"S215\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300258,\n                \"name\": \"S216\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300259,\n                \"name\": \"S217\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300260,\n                \"name\": \"S218\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300261,\n                \"name\": \"S219\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300262,\n                \"name\": \"S220\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300263,\n                \"name\": \"S221\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300264,\n                \"name\": \"S222\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300265,\n                \"name\": \"S223\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300266,\n                \"name\": \"S224\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300267,\n                \"name\": \"S225\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300268,\n                \"name\": \"S226\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300269,\n                \"name\": \"S227\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300270,\n                \"name\": \"S228\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300271,\n                \"name\": \"S229\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300272,\n                \"name\": \"S230\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300273,\n                \"name\": \"S231\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300274,\n                \"name\": \"S232\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300275,\n                \"name\": \"S233\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300276,\n                \"name\": \"S234\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300277,\n                \"name\": \"S235\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300278,\n                \"name\": \"S236\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300279,\n                \"name\": \"S237\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300280,\n                \"name\": \"S238\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300281,\n                \"name\": \"S239\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300282,\n                \"name\": \"S240\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300283,\n                \"name\": \"S241\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300284,\n                \"name\": \"S242\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300285,\n                \"name\": \"S243\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300286,\n                \"name\": \"S244\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300287,\n                \"name\": \"S245\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300288,\n                \"name\": \"S246\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300289,\n                \"name\": \"S247\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300290,\n                \"name\": \"S248\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300291,\n                \"name\": \"S249\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300292,\n                \"name\": \"S250\",\n                \"last_part\": 0,\n                \"now_part\": 6,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300293,\n                \"name\": \"S251\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300294,\n                \"name\": \"S252\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300295,\n                \"name\": \"S253\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300296,\n                \"name\": \"S254\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300297,\n                \"name\": \"S255\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300298,\n                \"name\": \"S256\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300299,\n                \"name\": \"S257\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300300,\n                \"name\": \"S258\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300301,\n                \"name\": \"S259\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300302,\n                \"name\": \"S260\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300303,\n                \"name\": \"S261\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300304,\n                \"name\": \"S262\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300305,\n                \"name\": \"S263\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300306,\n                \"name\": \"S264\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300307,\n                \"name\": \"S265\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300308,\n                \"name\": \"S266\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300309,\n                \"name\": \"S267\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300310,\n                \"name\": \"S268\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300311,\n                \"name\": \"S269\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            },\n            {\n                \"id\": 1000300312,\n                \"name\": \"S270\",\n                \"last_part\": 0,\n                \"now_part\": 7,\n                \"next_part\": 0\n            }\n        ],\n        \"status\": 0,\n        \"total\": 312\n    },\n    \"msg\": \"success\"\n}\n"

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)
	serverInfo := convert2Map()
	getTowerSeasonRank(redisClient, serverInfo)
	if getTowerSeasonRankFailed > 0 {
		l4g.Errorf("getTowerSeasonRankFailed count:%d", getTowerSeasonRankFailed)
		return
	}
	l4g.Infof("get tower season rank success")
}

func convert2Map() map[uint64]uint32 {
	serverParts := &serverPartDatas{}
	err := json.Unmarshal([]byte(serverPartString), serverParts)
	if err != nil {
		panic(fmt.Sprintf("[failed] umMarshalServerParts failed\n"))
	}
	if serverParts.Data.Total != len(serverParts.Data.List) {
		panic(fmt.Sprintf("[failed] serverPartString len or data is error\n"))
	}
	ret := make(map[uint64]uint32)
	for _, v := range serverParts.Data.List {
		ret[v.Id] = v.NowPart
	}
	return ret
}

func getTowerSeasonRank(r *redis.Redis, serverInfo map[uint64]uint32) {
	rankResetDatas, err := r.RopCr.GetAllRankResetSK(goxml.TowerSeasonRankID)
	if err != nil {
		l4g.Errorf("getTowerSeasonRank: get towerSeasonReset error. err:%s", err)
		getTowerSeasonRankFailed++
		return
	}

	calls := 0
	for _, rankResetData := range rankResetDatas {
		if rankResetData == nil {
			continue
		}
		strKeyName := fmt.Sprintf("%d:%d", goxml.TowerSeasonRankID, rankResetData.Sid)
		r.RopCr.GetAllRankDataMCallSKs(strKeyName)
		calls++
	}

	rankDatas := make([]map[uint64]*cr.RankData, 0, calls)

	for i := 0; i < calls; i++ {
		rankData, err := r.RopCr.GetSomeRankDataByReply(r.RopCr.GetReply())
		if err != nil {
			l4g.Errorf("rank get data error:%v", err)
			getTowerSeasonRankFailed++
			return
		}
		rankDatas = append(rankDatas, rankData)
	}

	file, err := os.OpenFile(*towerSeasonRankFile, os.O_WRONLY|os.O_CREATE, 0644)
	if err != nil {
		fmt.Println("can't create rank file:", err)
		return
	}
	defer file.Close()

	towerSeasonRankInfo := goxml.GetData().RankingInfoM.Index(goxml.TowerSeasonRankID)
	if towerSeasonRankInfo == nil {
		panic(fmt.Sprintf("rankInfo not exist. rankId:%d", goxml.TowerSeasonRankID))
	}
	partTowerSeasonRankInfos := make(map[uint32]*rank.CommonRank) //战区ID 排行榜
	for _, uRankData := range rankDatas {
		for _, data := range uRankData {
			towerSeasonRank := &rank.TowerSeasonFloor{}
			value := towerSeasonRank.NewElement()
			err = value.Load(data.GetData())
			if err != nil {
				l4g.Errorf("[rank] commonrank load data error %v", err)
				getTowerSeasonRankFailed++
				return
			}
			partId, exist := serverInfo[data.Sid]
			if !exist {
				panic(fmt.Sprintf("uid:%d rank Info serverid:%d is not in server part Info", data.Uid, data.Sid))
			}
			partTowerSeasonRank, exist := partTowerSeasonRankInfos[partId]
			if !exist {
				partTowerSeasonRankInfos[partId] = rank.NewCommonRank(&rank.TowerSeasonFloor{}, towerSeasonRankInfo.RankNum)
				partTowerSeasonRank = partTowerSeasonRankInfos[partId]
			}

			partTowerSeasonRank.Insert(value)
		}
	}

	dataSlice := make([]*userTowerSeasonRank, 0, 600)
	for _, partRank := range partTowerSeasonRankInfos {
		partList := partRank.GetRangeByRank(1, goxml.GetData().TowerSeasonRankRewardInfoM.GetLastRewardRank())
		l4g.Infof("list len:%d", len(partList))
		for index, v := range partList {
			data := v.(rank.CommonRankValuer).ShowValue()
			l4g.Infof("data:%v", data)
			partId, exist := serverInfo[data.Sid]
			if !exist {
				l4g.Errorf(fmt.Sprintf("uid:%d rank Info serverid:%d is not in server part Info", data.Id, data.Sid))
			}
			temp := &userTowerSeasonRank{
				Rank:  uint32(index) + 1,
				Uid:   data.User.Id,
				Sid:   data.Sid,
				Name:  data.User.Name,
				Floor: data.Value,
				Tm:    data.Param1,
				Part:  partId,
			}
			dataSlice = append(dataSlice, temp)
			l4g.Infof("user:%d rank:%d", data.User.Id, index+1)
		}
	}

	l4g.Info("partTowerSeasonRankS  lens:%d", len(dataSlice))

	content, err := json.Marshal(dataSlice)
	if err != nil {
		fmt.Println("json Marshal failed:", err)
		return
	}
	// 写入文件
	_, err = file.WriteString(string(content))
	if err != nil {
		fmt.Println("can't write file:", err)
		return
	}
}
