package cmd

import (
	v150GuildDungeon "app/tools/crossRepair/cmd/v150-guilddungeon"

	"github.com/spf13/cobra"
)

// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
var v150GuildDungeonCmd = &cobra.Command{
	Use:   "v150-guildDungeon",
	Short: "1.5.0 - 1.处理公会副本提前结算",
	Run: func(cmd *cobra.Command, args []string) {
		v150GuildDungeon.Run(cmd, args, redisClient)
	},
}

func init() {
	rootCmd.AddCommand(v150GuildDungeonCmd)
}
