package v2130

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/crossRepair/cmd/redis"
	"flag"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath                   = flag.String("cV2130configData", "../data/", "service data path")
	fixSeasonArenaUserRankInFailed uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, partID uint32) {
	goxml.Load(*flagDataPath, false, false)

	fixSeasonArenaUserRankIn(redisClient, partID)

	l4g.Info("\n")
	if fixSeasonArenaUserRankInFailed == 0 {
		fmt.Printf("[SUCCESS] fixSeasonArenaUserRankIn Success!\n")
	} else {
		fmt.Printf("[FAILED] fixSeasonArenaUserRankIn Failed, need check error log! failedCount:%d\n", fixSeasonArenaUserRankInFailed)
	}
	l4g.Info("finish repair")
}

func fixSeasonArenaUserRankIn(r *redis.Redis, partID uint32) {
	users, err := r.RopCr.GetAllCrossSeasonArenaUserSK(partID)
	if err != nil {
		l4g.Errorf("fixSeasonArenaUserRankIn. get users error: %v, partition:%d", err, partID)
		fixSeasonArenaUserRankInFailed++
		return
	}
	changeUser := make([]*cr.CrossSeasonArenaUser, 0, len(users))
	for _, user := range users {
		if user == nil {
			continue
		}
		user.SeasonRankIn = true
		changeUser = append(changeUser, user)
	}

	call := 0
	r.RopCr.SetSomeCrossSeasonArenaUserMCallSK(partID, changeUser)
	call++

	for i := 0; i < call; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("fixSeasonArenaUserRankIn error:%v", reply.Err)
			fixSeasonArenaUserRankInFailed++
		}
	}
}
