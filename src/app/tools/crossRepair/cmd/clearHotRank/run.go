package clearHotRank

import (
	"app/tools/crossRepair/cmd/redis"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var clearFailedCount uint32

func Run(cmd *cobra.Command, args []string, crossRedisClient *redis.Redis) {
	clearGVG(crossRedisClient)
	if clearFailedCount == 0 {
		fmt.Printf("[SUCCESS] clear hot rank Success!\n")
	} else {
		fmt.Printf("[FAILED] clear hot rank Failed, need check error log! failedCount:%d\n", clearFailedCount)
	}
}

func clearGVG(r *redis.Redis) {
	r.RopCr.Append("redislv_keys", "cross_hot*")
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("get cross_hot* error: %s", reply.Err.Error())
		clearFailedCount++
		return
	} else {
		gvgKeys, err := reply.List()
		if err != nil {
			l4g.Errorf("get cross_hot* error: %s", err.Error())
			clearFailedCount++
			return
		}
		calls := 0
		for _, gvgKey := range gvgKeys {
			r.RopDB.Append("del", gvgKey)
			l4g.Infof("删除hot_rank: %s", gvgKey)
			calls++
		}
		for i := 0; i < calls; i++ {
			if reply := r.Client.GetReply(); reply.Err != nil {
				l4g.Errorf("del cross_hot error: %s", reply.Err.Error())
				clearFailedCount++
				continue
			}
		}
	}
}
