package clearSeasonCompliance

import (
	"app/tools/crossRepair/cmd/redis"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var clearFailedCount uint32

func Run(cmd *cobra.Command, args []string, crossRedisClient *redis.Redis) {
	clearSeasonCompliance(crossRedisClient)
	if clearFailedCount == 0 {
		fmt.Printf("[SUCCESS] clear season compliance Success!\n")
	} else {
		fmt.Printf("[FAILED] clear season compliance Failed, need check error log! failedCount:%d\n", clearFailedCount)
	}
}

func clearSeasonCompliance(r *redis.Redis) {
	r.RopCr.Append("redislv_keys", "season_compliance_*")
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("get season_compliance_* error: %s", reply.Err.Error())
		clearFailedCount++
		return
	} else {
		scKeys, err := reply.List()
		if err != nil {
			l4g.<PERSON><PERSON><PERSON>("get season_compliance_* error: %s", err.<PERSON>rror())
			clearFailedCount++
			return
		}
		calls := 0
		for _, gvgKey := range scKeys {
			r.RopDB.Append("del", gvgKey)
			l4g.Infof("删除season_compliance_: %s", gvgKey)
			calls++
		}
		for i := 0; i < calls; i++ {
			if reply := r.Client.GetReply(); reply.Err != nil {
				l4g.Errorf("del season_compliance_ error: %s", reply.Err.Error())
				clearFailedCount++
				continue
			}
		}
	}
}
