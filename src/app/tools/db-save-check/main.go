package main

import (
	"bytes"
	"flag"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/token"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"slices"
	"strings"
)

var gDBFilePath = flag.String("dbpath", "./src/app/logic/character/", "要检查的文件路径")

var allNoCheckCommentFile []string
var allNeedCheckStruct []*CheckStruct
var allAlertFunc []*AlertFunc

var allIgnoreFuncsName = []string{"load", "save", "init"}

// 需要对待的调用函数
var allNeedCheckCallFuncsName = []string{"TaskTypeOnEvent"}

// 保存需要检查的方法名
const saveMethodName = "save"

const MultiSaveChangeField = "changes"
const MultiSaveDeleteField = "deletes"

const MultiSaveChangeFuncName = "setchange"
const MultiSaveDeleteFuncName = "delete"

var allMultiSaveFuncName = []string{MultiSaveChangeFuncName, MultiSaveDeleteFuncName}

type CheckStruct struct {
	StructName string
	FieldName  string
	FieldType  string
}

// 存储赋值操作的位置信息
type AlertFunc struct {
	File     string
	FuncName string
}

func main() {
	fmt.Println("不要直接修改db结构体里的字段,为了方便检查不遗漏,请使用Set或者SetChange方法!")
	err := filepath.Walk(*gDBFilePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".go") {
			checkFile(path)
		}
		return nil
	})

	if err != nil {
		fmt.Println("Error walking the directory:", err)
	}

	//showErroFile()
	//showCheckStruct()
	showErrorFunc()
}

func showErrorFile() {
	for _, v := range allNoCheckCommentFile {
		fmt.Printf("警告！文件%s, 没有save检查标记，如果不需要检查,就在文件的最开始注释//no-db-save-check \n", v)
	}
}

func showCheckStruct() {
	for _, v := range allNeedCheckStruct {
		fmt.Printf("需要检查的对象%s和字段%s, 类型:%s \n", v.StructName, v.FieldName, v.FieldType)
	}
}

func showErrorFunc() {
	for _, v := range allAlertFunc {
		fmt.Printf("警告！文件%s, 函数:%s 没有save \n", v.File, v.FuncName)
	}
}

func checkFile(filePath string) {
	//fmt.Printf("begin checkfile %s \n", filePath)
	// 收集所有的赋值操作
	fset := token.NewFileSet()
	f, err := parser.ParseFile(fset, filePath, nil, parser.ParseComments)
	if err != nil {
		fmt.Println("Error parsing file:", err)
		return
	}

	if f.Doc != nil {
		for _, v := range f.Doc.List {
			//fmt.Printf("====>ast.GenDecl comment is %s \n", v.Text)
			if strings.Contains(v.Text, "no-db-save-check") {
				return
			}
		}
	}

	// 检查文件中的所有函数
	hasCheckComment := false
	hasMultiSave := false
	ast.Inspect(f, func(n ast.Node) bool {
		switch node := n.(type) {
		case *ast.FuncDecl:
			hasSet := checkFunction(node, fset, filePath)
			if hasSet {
				allAlertFunc = append(allAlertFunc, &AlertFunc{
					File:     filePath,
					FuncName: node.Name.Name,
				})
			}
			checkMultiSaveFunction(node, fset, filePath, hasMultiSave)
		case *ast.GenDecl:
			needCheck := checkGen(node, fset, filePath)
			if needCheck {
				hasCheckComment = true
			}

			needCheckMulti := checkMultiSaveGen(node, fset, filePath)
			if needCheckMulti {
				hasMultiSave = true
			}
		}
		return true
	})

	if err != nil {
		log.Fatalf("遍历目录错误: %v", err)
	}
	if !hasCheckComment {
		allNoCheckCommentFile = append(allNoCheckCommentFile, filePath)
	}
}

func checkGen(genDecl *ast.GenDecl, fset *token.FileSet, filePath string) bool {
	//fmt.Printf("ast.GenDecl is %+v \n", genDecl)
	if genDecl.Doc != nil {
		for _, v := range genDecl.Doc.List {
			//fmt.Printf("====>ast.GenDecl comment is %s \n", v.Text)
			if strings.Contains(v.Text, "no-db-save-check") {
				return true
			}
		}
	}

	if genDecl.Tok != token.TYPE {
		return false
	}
	needCheck := false

	/*
		fmt.Printf("ast.GenDecl is %+v \n", genDecl)
		if genDecl.Doc != nil {
			for _, v := range genDecl.Doc.List {
				fmt.Printf("====>ast.GenDecl comment is %s \n", v.Text)
			}
		}
	*/
	for _, spec := range genDecl.Specs {
		typeSpec, ok := spec.(*ast.TypeSpec)
		if !ok {
			continue
		}

		structType, ok := typeSpec.Type.(*ast.StructType)
		if !ok {
			continue
		}

		//fmt.Printf("===>ast.StructSpec is %+v \n", structType)

		name := typeSpec.Name.Name
		list := structType.Fields.List

		for _, field := range list {
			if len(field.Names) > 0 {
				fieldName := field.Names[0].Name
				fieldType := getTypeString(fset, field.Type)
				fieldComment := getComment(field.Comment)
				//fmt.Printf("======>ast.StructSpec field:%s, field_type:%s field_comment:%s \n", fieldName, fieldType, fieldComment)
				if needCheckType(fieldComment) {
					allNeedCheckStruct = append(allNeedCheckStruct, &CheckStruct{
						StructName: name,
						FieldName:  fieldName,
						FieldType:  fieldType,
					})
					needCheck = true
				}
			}
		}
	}
	return needCheck
}

func checkMultiSaveGen(genDecl *ast.GenDecl, fset *token.FileSet, filePath string) bool {
	//fmt.Printf("ast.GenDecl is:%s %+v \n", filePath, genDecl)
	if genDecl.Tok != token.TYPE {
		return false
	}
	needCheck := false
	for _, spec := range genDecl.Specs {
		typeSpec, ok := spec.(*ast.TypeSpec)
		if !ok {
			continue
		}

		structType, ok := typeSpec.Type.(*ast.StructType)
		if !ok {
			continue
		}

		//fmt.Printf("===>ast.StructSpec is %+v \n", structType)

		list := structType.Fields.List

		hasChange := false
		hasDelete := false
		for _, field := range list {
			if len(field.Names) > 0 {
				fieldName := field.Names[0].Name
				fieldComment := getComment(field.Comment)
				if strings.Contains(fieldComment, "no-db-save-check") {
					continue
				}
				if strings.ToLower(fieldName) == MultiSaveChangeField {
					hasChange = true
				}
				if strings.ToLower(fieldName) == MultiSaveDeleteField {
					hasDelete = true
				}

			}
		}
		if hasChange && hasDelete {
			needCheck = true
		}
	}
	return needCheck
}

func needCheckType(comment string) bool {
	if comment != "" && strings.Contains(comment, "db-save-check") {
		return true
	}
	return false
}

func getComment(comment *ast.CommentGroup) string {
	if comment == nil {
		return ""
	}
	for _, v := range comment.List {
		return v.Text
	}
	return ""
}

func getReceiveType(funcDecl *ast.FuncDecl, fset *token.FileSet) (string, string) {
	if funcDecl.Recv != nil {
		//name 有可能为0, 没有写name
		if len(funcDecl.Recv.List[0].Names) > 0 {
			recvShortName := funcDecl.Recv.List[0].Names[0].Name
			recvType := funcDecl.Recv.List[0].Type
			recvTypeName := getTypeString(fset, recvType)
			//fmt.Printf("func type recv name:%s type name :%s \n", recvShortName, recvTypeName)
			return recvShortName, recvTypeName
		}
	}

	/*
		if funcDecl.Type != nil {
			for _, v := range funcDecl.Type.Params.List {
				paramShortName := v.Names[0].Name
				paramType := v.Type
				paramTypeName := getTypeString(fset, paramType)
				//fmt.Printf("==>func type param  name:%s type name :%s \n", paramShortName, paramTypeName)
			}
		}
	*/
	return "", ""
}

// TODO for 循环里的代码不会被执行到
func checkFunction(funcDecl *ast.FuncDecl, fset *token.FileSet, filePath string) bool {
	funcName := strings.ToLower(getFuncName(funcDecl))
	if slices.Contains(allIgnoreFuncsName, funcName) {
		return false
	}
	find := false
	checkString := ""
	receiveName, receiveType := getReceiveType(funcDecl, fset)
	if receiveName != "" && receiveType != "" {
		for _, v := range allNeedCheckStruct {
			if strings.Trim(receiveType, "*") == v.StructName {
				find = true
				checkString = fmt.Sprintf("%s.%s", receiveName, v.FieldName)
				break
			}
		}
	}
	if find {
		//fmt.Printf("need check func:%s receviename:%s receivetype:%s, checkString:%s \n", funcDecl.Name.Name, receiveName, receiveType, checkString)
	} else {
		return false
	}
	return checkBody(fset, funcDecl.Body, checkString)

}

func checkMultiSaveFunction(funcDecl *ast.FuncDecl, fset *token.FileSet, filePath string, needCheck bool) {
	if !needCheck {
		return
	}
	funcName := strings.ToLower(getFuncName(funcDecl))
	if !slices.Contains(allMultiSaveFuncName, funcName) {
		return
	}

	isMatch := false
	ast.Inspect(funcDecl.Body, func(n ast.Node) bool {
		switch node := n.(type) {
		case *ast.ExprStmt:
			expr := getTypeString(fset, node.X)
			if funcName == MultiSaveChangeFuncName && strings.Contains(expr, MultiSaveDeleteField) {
				isMatch = true
			}
			if funcName == MultiSaveDeleteFuncName && strings.Contains(expr, MultiSaveChangeField) {
				isMatch = true
			}
			//fmt.Printf("checkMultiSaveFunction file:%s line:%d expr:%s \n", filePath, fset.Position(n.Pos()).Line, expr)
		}
		return true
	})
	if !isMatch {
		if funcName == MultiSaveChangeFuncName {
			fmt.Printf("警告! 文件%s 函数%s 缺少 delete(a.deletes,id) \n", filePath, funcName)
		} else {
			fmt.Printf("警告! 文件%s 函数%s 缺少 delete(a.changes, id) \n", filePath, funcName)
		}
	}
}

func checkBody(fset *token.FileSet, body ast.Node, checkString string) bool {
	hasSet := false
	ast.Inspect(body, func(n ast.Node) bool {
		switch node := n.(type) {
		case *ast.AssignStmt:
			// 检查赋值语句
			for _, lhs := range node.Lhs {
				expr := getTypeString(fset, lhs)
				if strings.Contains(expr, checkString) {
					hasSet = true
					break
				}
			}
		case *ast.IfStmt:
			expr := getTypeString(fset, node.Cond)
			condStr := fmt.Sprintf("%s%s == %s", checkString, ".*", "nil")
			re := regexp.MustCompile(condStr)

			condStr2 := fmt.Sprintf(`len\(%s%s\) == 0`, checkString, ".*")
			re2 := regexp.MustCompile(condStr2)
			//fmt.Printf("if stmt expr:%s, condStr:%s condStr2:%s \n", expr, condStr, condStr2)
			if re.MatchString(expr) || re2.MatchString(expr) {
				//fmt.Printf("if stmt expr regexp match success \n")
				return false
			}
		case *ast.CallExpr:
			// 检查是否调用了 Save 方法
			if sel, ok := node.Fun.(*ast.SelectorExpr); ok {
				if strings.ToLower(sel.Sel.Name) == saveMethodName || strings.ToLower(sel.Sel.Name) == MultiSaveChangeFuncName {
					// 找到了 Save 调用，清除最后的赋值记录
					if hasSet {
						hasSet = false
						return false //直接退出
					}
				}
			}
			//检查是否把参数传递出去
			callFuncName := getCallFuncName(fset, node)
			//fmt.Printf("call func name is %+v name:%s, realName:%s \n", node.Fun, getTypeString(fset, node.Fun), callFuncName)
			if slices.Contains(allNeedCheckCallFuncsName, callFuncName) {
				for _, v := range node.Args {
					expr := getTypeString(fset, v)
					if strings.Contains(expr, checkString) {
						hasSet = true
						break
					}
				}
			}

		}
		return true
	})

	return hasSet
}

func getTypeString(fset *token.FileSet, expr ast.Expr) string {
	var buf bytes.Buffer
	if err := format.Node(&buf, fset, expr); err != nil {
		panic(err)
	}
	return buf.String()
}

func getFuncName(funcDecl *ast.FuncDecl) string {
	return funcDecl.Name.Name
}

func getCallFuncName(fset *token.FileSet, node *ast.CallExpr) string {
	name := getTypeString(fset, node.Fun)
	nameArr := strings.Split(name, ".")
	return nameArr[len(nameArr)-1]
}
