package mongo

import (
	"app/tools/log-consumer/service/config"
	"context"

	l4g "github.com/ivanabc/log4go"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func NewMongoClient(mongoConfig *config.MongoConfig) *mongo.Client {
	if len(mongoConfig.Server) == 0 {
		l4g.Error("mongo no server")
		return nil
	}

	clientOptions := options.Client().ApplyURI(mongoConfig.Server)

	// 连接数据库
	client, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		panic(err)
	}

	// 验证连接
	if err := client.Ping(context.TODO(), nil); err != nil {
		panic(err)
	}
	return client
}
