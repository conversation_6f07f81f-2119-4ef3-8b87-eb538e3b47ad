package db_schema

import (
	"app/tools/log-consumer/service/config"
	"bytes"
	"context"
	"encoding/json"
	"strings"

	l4g "github.com/ivanabc/log4go"

	esC "app/tools/log-consumer/service/client/es"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
)

func initEsSchema() {
	esConfig := config.NewESConfig()
	client := esC.NewESClient(esConfig)
	if client == nil {
		return
	}
	checkESIndex(esConfig, client)
	l4g.Infof("EsSchema successfully")
}

func checkESIndex(esCfg *config.ESConfig, client *elasticsearch.Client) {
	for _, indexInfo := range esCfg.Indexs {
		index := strings.ToLower(indexInfo.Index.String())
		existIndexReq := esapi.IndicesExistsRequest{
			Index: []string{index},
		}
		existIndexRsp, existIndexErr := existIndexReq.Do(context.Background(), client)
		if existIndexErr != nil {
			panic(existIndexErr)
		}
		if existIndexRsp.IsError() {
			l4g.Debugf("Creating index %s", index)
			var buf bytes.Buffer
			if err := json.NewEncoder(&buf).Encode(indexInfo.Mappings); err != nil {
				panic(err)
			}
			createIndexReq := esapi.IndicesCreateRequest{
				Index: index,
				Body:  &buf,
			}
			createIndexRsp, createIndexErr := createIndexReq.Do(context.Background(), client)
			if createIndexErr != nil {
				panic(createIndexErr)
			}
			if createIndexRsp.IsError() {
				panic(createIndexRsp.String())
			}
		}
	}
}
