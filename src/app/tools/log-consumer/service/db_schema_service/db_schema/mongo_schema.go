package db_schema

import (
	"app/tools/log-consumer/service/config"
	"context"
	"strings"

	mongoC "app/tools/log-consumer/service/client/mongo"

	l4g "github.com/ivanabc/log4go"
	"go.mongodb.org/mongo-driver/mongo"
)

func initMongoSchema() {
	mongoConfig := config.NewMongoConfig()
	db := connectMongo(mongoConfig)
	if db == nil {
		return
	}
	checkMongoIndex(mongoConfig, db)
	l4g.Infof("MongoSchema successfully")
}

func connectMongo(mongoConfig *config.MongoConfig) *mongo.Database {
	client := mongoC.NewMongoClient(mongoConfig)
	if client == nil {
		return nil
	}
	return client.Database(mongoConfig.Database)
}

func checkMongoIndex(mongoConfig *config.MongoConfig, db *mongo.Database) {
	for _, collection := range mongoConfig.Collections {
		l4g.Debugf("Checking MongoDB collection: %+v", collection)
		collName := strings.ToLower(collection.CollName.String())
		coll := db.Collection(collName)
		var models []mongo.IndexModel
		for _, index := range collection.Indexes {
			models = append(models, *index)
		}
		_, err := coll.Indexes().CreateMany(context.Background(), models)
		if err != nil {
			panic(err)
		}
	}
}
