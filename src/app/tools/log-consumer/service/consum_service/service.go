package consum_service

import (
	"app/protos/in/log"
	"app/tools/log-consumer/service/config"
	"app/tools/log-consumer/service/consum_service/kafka_consumer"
	"net/http"

	"gitlab.qdream.com/kit/library/net/http/ginutil"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/micro/restful"
	"go.mongodb.org/mongo-driver/mongo"

	esC "app/tools/log-consumer/service/client/es"
	mongoC "app/tools/log-consumer/service/client/mongo"
	esQuery "app/tools/log-consumer/service/consum_service/query/es"
	mongoQuery "app/tools/log-consumer/service/consum_service/query/mongo"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/gin-gonic/gin"
)

type Service struct {
	group       *ctx.Group
	consumerM   *kafka_consumer.Manager
	esConfig    *config.ESConfig
	mongoConfig *config.MongoConfig
	kafkaConfig *config.KafkaConfig
	esClient    *elasticsearch.Client
	mongoClient *mongo.Client
}

func New(group *ctx.Group) restful.Servicer {
	return &Service{
		group: group,
	}
}

func (s *Service) Register(r *gin.Engine) {
	//支持内部运行查询语句
	r.POST("/search", s.Search)
}

func (s *Service) Run() {
	s.initConfig()
	s.initKafka()
	s.initClients()
}

func (s *Service) Close() {
	s.closeKafka()
}

func (s *Service) initConfig() {
	s.esConfig = config.NewESConfig()
	s.mongoConfig = config.NewMongoConfig()
	s.kafkaConfig = config.NewKafkaConfig()
}

func (s *Service) initKafka() {
	s.consumerM = kafka_consumer.NewManager(s.group.CreateChild(), s)
	s.consumerM.Init()
	s.consumerM.Run()
}

func (s *Service) initClients() {
	s.esClient = esC.NewESClient(s.GetEsConfig())
	s.mongoClient = mongoC.NewMongoClient(s.GetMongoConfig())
}

func (s *Service) GetESClient() *elasticsearch.Client {
	return s.esClient
}

func (s *Service) GetMongoClient() *mongo.Client {
	return s.mongoClient
}

func (s *Service) closeKafka() {
	s.consumerM.Close()
}

func (s *Service) GetEsConfig() *config.ESConfig {
	return s.esConfig
}
func (s *Service) GetMongoConfig() *config.MongoConfig {
	return s.mongoConfig
}
func (s *Service) GetKafkaConfig() *config.KafkaConfig {
	return s.kafkaConfig
}
func (s *Service) GetCronConfig() *config.CronConfig {
	return nil
}

func (s *Service) Search(c *gin.Context) {
	var req log.LogQueryReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	rsp := &log.LogQueryRsp{}
	if req.Type == log.LOG_QUERY_TYPE_LOG_QUERY_TYPE_ES {
		datas, err := esQuery.Search(req.EsQuery, s.GetESClient())
		if err != nil {
			ginutil.Fail(c, err.Error())
			return
		}
		rsp.Datas = datas
	} else if req.Type == log.LOG_QUERY_TYPE_LOG_QUERY_TYPE_MONGO {
		datas, err := mongoQuery.Search(req.MongoQuery, s.GetMongoConfig(), s.GetMongoClient())
		if err != nil {
			ginutil.Fail(c, err.Error())
			return
		}
		rsp.Datas = datas
	}

	ginutil.Success(c, *rsp)
}
