package mongo

import (
	"app/protos/out/cl"
	"app/tools/log-consumer/service/config"
	"context"
	"encoding/json"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func Search(req *cl.MongoQueryReq, mongoConfig *config.MongoConfig, mongoClient *mongo.Client) ([]string, error) {
	if req == nil {
		return nil, fmt.Errorf("MongoQueryReq is nil")
	}
	dataBase := mongoClient.Database(mongoConfig.Database)
	col := dataBase.Collection(req.CollName)
	if col == nil {
		return nil, fmt.Errorf("get col is nil. col:%s", req.CollName)
	}
	var filter bson.D
	if err := bson.Unmarshal(req.Filter, &filter); err != nil {
		return nil, fmt.Errorf("解析过滤条件失败: %v", err)
	}
	l4g.Debugf("过滤条件 %+v", filter)
	findOptions := buildQueryOptions(req.Options)
	cursor, err := col.Find(context.TODO(), filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询失败: %v", err)
	}
	defer cursor.Close(context.TODO())
	// 收集结果
	var results []bson.M
	for cursor.Next(context.TODO()) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("解码结果失败: %v", err)
		}
		results = append(results, result)
	}
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("游标错误: %v", err)
	}
	var datas []string
	for _, result := range results {
		jsonResult, err := json.Marshal(result)
		if err != nil {
			return nil, fmt.Errorf("转换结果为JSON失败: %v", err)
		}
		datas = append(datas, string(jsonResult))
	}

	return datas, nil
}

func buildQueryOptions(queryOptions *cl.QueryOptions) *options.FindOptions {
	option := NewQueryOptions()
	if queryOptions == nil {
		return option.Build()
	}
	for _, sort := range queryOptions.Sort {
		option.Sort(sort.Field, sort.Ascending)
	}
	if queryOptions.Page != nil {
		if queryOptions.Page.Skip != 0 {
			option.Skip(queryOptions.Page.Skip)
		}
		if queryOptions.Page.PageNum != 0 {
			option.Limit(queryOptions.Page.PageNum)
		}
	}
	return option.Build()
}

type QueryOptions struct {
	sort  bson.D
	limit int64
	skip  int64
}

// NewQueryOptions 创建新的查询选项
func NewQueryOptions() *QueryOptions {
	return &QueryOptions{
		sort:  bson.D{},
		limit: 0,
		skip:  0,
	}
}

// Sort 添加排序条件
func (qo *QueryOptions) Sort(field string, ascending bool) *QueryOptions {
	order := 1
	if !ascending {
		order = -1
	}
	qo.sort = append(qo.sort, bson.E{Key: field, Value: order})
	return qo
}

// Limit 设置查询数量限制
func (qo *QueryOptions) Limit(limit int64) *QueryOptions {
	qo.limit = limit
	return qo
}

// Skip 设置跳过的记录数
func (qo *QueryOptions) Skip(skip int64) *QueryOptions {
	qo.skip = skip
	return qo
}

// Build 构建查询选项
func (qo *QueryOptions) Build() *options.FindOptions {
	opts := options.Find()
	if len(qo.sort) > 0 {
		opts.SetSort(qo.sort)
	}
	if qo.limit > 0 {
		opts.SetLimit(qo.limit)
	}
	if qo.skip > 0 {
		opts.SetSkip(qo.skip)
	}
	return opts
}
