package es

import (
	"app/protos/out/cl"
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	l4g "github.com/ivanabc/log4go"
)

func Search(req *cl.EsQueryReq, esClient *elasticsearch.Client) ([]string, error) {
	if req == nil {
		return nil, fmt.Errorf("EsQueryReq is nil")
	}
	searchReq, err := buildRequest(req)
	if err != nil {
		l4g.Errorf("BuildRequest error :%s", err.Error())
		return nil, err
	}
	searchRsp, err := searchReq.Do(context.Background(), esClient)
	if err != nil {
		l4g.Errorf("search error :%s", err.Error())
		return nil, err
	}
	l4g.Debugf("Elastic searchRsp: %s", searchRsp.String())
	defer searchRsp.Body.Close()
	// 解析响应数据
	var response map[string]interface{}
	if err := json.NewDecoder(searchRsp.Body).Decode(&response); err != nil {
		l4g.Errorf("Decode error :%s", err.Error())
		return nil, err
	}

	return getDatas(response), nil
}

func getDatas(response map[string]interface{}) []string {
	l4g.Debugf("获取的数据: %+v", response)
	var datas []string
	// 提取 hits 数据
	hits, ok := response["hits"].(map[string]interface{})
	if !ok {
		l4g.Debug("hits not found")
		return datas
	}
	// 提取文档数组
	hitsData, ok := hits["hits"].([]interface{})
	if !ok {
		l4g.Debug(" hits array not found")
		return datas
	}
	for _, hit := range hitsData {
		hitData, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}
		sourceData, ok := hitData["_source"].(map[string]interface{})
		if !ok {
			continue
		}
		jsonData, err := json.Marshal(sourceData)
		if err != nil {
			l4g.Errorf("Marshal error :%s", err.Error())
			continue
		}
		datas = append(datas, string(jsonData))
	}
	return datas
}

func buildRequest(req *cl.EsQueryReq) (*esapi.SearchRequest, error) {
	dsl, err := toDSL(req.Query)
	if err != nil {
		return nil, err
	}
	dslJSON, err := json.Marshal(dsl)
	if err != nil {
		return nil, err
	}
	l4g.Debugf("生成的 DSL: %s", dslJSON)

	return &esapi.SearchRequest{
		Index: []string{req.Index},
		Body:  bytes.NewReader(dslJSON),
	}, nil
}

func toDSL(query *cl.EsCommonQuery) (map[string]interface{}, error) {
	if query == nil {
		return nil, fmt.Errorf("EsCommonQuery is nil")
	}
	dsl := make(map[string]interface{})
	// 构建bool查询
	boolQuerys := make(map[string]interface{})
	for boolType, conditions := range query.BoolQuerys {
		var boolTypeS string
		switch boolType {
		case int32(cl.ES_QUERY_BOOL_TYPE_ES_QUERY_BOOL_TYPE_MUST):
			boolTypeS = "must"
		case int32(cl.ES_QUERY_BOOL_TYPE_ES_QUERY_BOOL_TYPE_MUST_NOT):
			boolTypeS = "must_not"
		case int32(cl.ES_QUERY_BOOL_TYPE_ES_QUERY_BOOL_TYPE_SHOULD):
			boolTypeS = "should"
			boolQuerys["minimum_should_match"] = conditions.ShouldMatch
		default:
			return nil, fmt.Errorf("cant find bool type %d", boolType)
		}
		conditions, err := conditionsToDSL(conditions.Conditions)
		if err != nil {
			return nil, err
		}
		boolQuerys[boolTypeS] = conditions
	}

	dsl["query"] = map[string]interface{}{"bool": boolQuerys}

	// 处理排序
	if len(query.Sorts) > 0 {
		sorts := make([]map[string]interface{}, 0, len(query.Sorts))
		for _, sort := range query.Sorts {
			sortDir := "asc"
			if !sort.Ascending {
				sortDir = "desc"
			}
			sorts = append(sorts, map[string]interface{}{
				sort.Field: map[string]string{"order": sortDir},
			})
		}
		dsl["sort"] = sorts
	}

	// 处理分页
	if query.Page != nil {
		if query.Page.Skip > 0 {
			dsl["from"] = query.Page.Skip
		}
		if query.Page.PageNum > 0 {
			dsl["size"] = query.Page.PageNum
		}
	}

	return dsl, nil
}

func conditionsToDSL(conditions []*cl.EsQueryCondition) ([]map[string]interface{}, error) {
	dslConditions := make([]map[string]interface{}, 0, len(conditions))
	for _, cond := range conditions {
		condDSL, err := conditionToDSL(cond)
		if err != nil {
			return nil, err
		}
		dslConditions = append(dslConditions, condDSL)
	}
	return dslConditions, nil
}

func conditionToDSL(cond *cl.EsQueryCondition) (map[string]interface{}, error) {
	switch cond.ConditionType {
	case cl.ES_QUERY_CONDITION_TYPE_ES_QUERY_CONDITION_TYPE_TERM:
		// term 查询：{"term": {"field": {"value": ...}}}
		return map[string]interface{}{
			"term": map[string]interface{}{
				cond.Field: map[string]interface{}{"value": cond.Param},
			},
		}, nil

	case cl.ES_QUERY_CONDITION_TYPE_ES_QUERY_CONDITION_TYPE_RANGE:
		// range 查询：{"range": {"field": {"gt": ..., "lt": ...}}}
		rangeParams := make(map[string]interface{})
		params := &cl.EsQueryRanges{}
		err := json.Unmarshal([]byte(cond.Param), params)
		if err != nil {
			return nil, err
		}
		for _, rangeParam := range params.Ranges {
			var rangeType string
			switch rangeParam.RangeType {
			case cl.ES_QUERY_RANGE_TYPE_ES_QUERY_RANGE_TYPE_GT:
				rangeType = "gt"
			case cl.ES_QUERY_RANGE_TYPE_ES_QUERY_RANGE_TYPE_GTE:
				rangeType = "gte"
			case cl.ES_QUERY_RANGE_TYPE_ES_QUERY_RANGE_TYPE_LT:
				rangeType = "lt"
			case cl.ES_QUERY_RANGE_TYPE_ES_QUERY_RANGE_TYPE_LTE:
				rangeType = "lte"
			default:
				return nil, fmt.Errorf("range 条件不支持参数%d", rangeParam.RangeType)
			}
			rangeParams[rangeType] = rangeParam.Value
		}
		if len(rangeParams) == 0 {
			return nil, fmt.Errorf("range 条件至少需要一个参数gt/gte/lt/lte")
		}
		return map[string]interface{}{
			"range": map[string]interface{}{
				cond.Field: rangeParams,
			},
		}, nil

	case cl.ES_QUERY_CONDITION_TYPE_ES_QUERY_CONDITION_TYPE_WILDCARD:
		// wildcard 查询：{"wildcard": {"field": {"value": "xxx*"}}}
		return map[string]interface{}{
			"wildcard": map[string]interface{}{
				cond.Field: map[string]string{"value": cond.Param},
			},
		}, nil

	case cl.ES_QUERY_CONDITION_TYPE_ES_QUERY_CONDITION_TYPE_MATCH:
		// match 查询：{"match": {"field": "xxx"}}
		return map[string]interface{}{
			"match": map[string]interface{}{
				cond.Field: cond.Param,
			},
		}, nil

	default:
		return nil, fmt.Errorf("不支持的条件类型: %d", cond.ConditionType)
	}
}
