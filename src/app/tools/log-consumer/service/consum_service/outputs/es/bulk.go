package es

import (
	"app/tools/log-consumer/service/config"
	"context"

	esC "app/tools/log-consumer/service/client/es"

	"github.com/elastic/go-elasticsearch/v7/esutil"
)

type ESBulk struct {
	bulk esutil.BulkIndexer
}

func NewESBulk(esConfig *config.ESConfig) *ESBulk {
	esBulk := &ESBulk{}
	esClient := esC.NewESClient(esConfig)
	bulk, err := esutil.NewBulkIndexer(esutil.BulkIndexerConfig{
		Client:     esClient,
		NumWorkers: esConfig.Workers,
	})
	if err != nil {
		panic(err)
	}
	esBulk.bulk = bulk
	return esBulk
}

func (e *ESBulk) Add(item *esutil.BulkIndexerItem) {
	e.bulk.Add(context.TODO(), *item)
}

func (e *ESBulk) Close() {
	e.bulk.Close(context.TODO())
}
