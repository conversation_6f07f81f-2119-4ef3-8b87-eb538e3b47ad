log: ../log/log-consumer.log

restful:
 addr: :31003
 mode: release

mongo:
  server: mongodb://127.0.0.1:27018
  # username:
  # password:
  database: log # 数据库名称
  workers: 0 # 默认runtime.NumCPU()
  collections:
    - collname: 1            # 集合名称 log.MONGO_LOG_COLL的小写
      indexes:                   # 集合的索引
        - keys: [name:1, server_id:1]
          options:
            unique: true

es:
  hosts: [http://************:9200]
  workers: 0 # 默认runtime.NumCPU()
  indexs:
    - index: 1 # log.ES_LOG_INDEX 的小写
      mappings:
        properties:
          name: 
            type: "keyword"
          server_id: 
            type: "long"
          uid:
            type: "long"
    - index: 2 # log.ES_LOG_INDEX 的小写
      mappings:
        properties:
          name: 
            type: "keyword"
          server_id: 
            type: "long"
          guild_id:
            type: "long"
          

kafkas:
  - version: "2.4.0"
    brokers: [************:9092]
    topics: [1] # log.KAFKA_TOPIC 要消费的topics
    handler_type: 1 # log.KAFKA_HANDLER_TYPE
  - version: "2.4.0"
    brokers: [************:9092]
    topics: [2] # log.KAFKA_TOPIC
    handler_type: 2 # log.KAFKA_HANDLER_TYPE

cron:
  tasks:
    - name: "定时删除大地图日志"
      spec: "*/5 * * * * *" # cron表达式
      function: "deleteSeasonMapRecord" # 任务处理函数
      enabled: true # 是否启用

