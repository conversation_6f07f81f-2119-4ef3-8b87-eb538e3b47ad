package main

import (
	"flag"
	"fmt"
	"strings"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/redis"
	"gitlab.qdream.com/kit/sea/util"
)

var sourceIp = flag.String("sip", "127.0.0.1", "源地址")
var sourcePort = flag.Int("sport", 6379, "源端口")
var sourceIndex = flag.Int("sindex", 0, "源db")
var sourceAuth = flag.String("sauth", "ngame", "源密码")

var targetIp = flag.String("tip", "127.0.0.1", "目标地址")
var targetPort = flag.Int("tport", 6379, "目标端口")
var targetIndex = flag.Int("tindex", 0, "目标db")
var targetAuth = flag.String("tauth", "ngame", "目标密码")

var keyParttern = flag.String("key", "", "要迁移的key")

func main() {
	flag.Parse()

	logFilename := "./redis-copy.log"
	cfg := util.NewL4GConfig("ERROR", logFilename)
	cfg = strings.ReplaceAll(cfg, "10M", "50M") //日志文件大一点再生成新文件
	l4g.Global.LoadConfiguration("", []byte(cfg))
	defer l4g.Close()

	sourceAddr := fmt.Sprintf("%s:%d", *sourceIp, *sourcePort)
	sClient, err := redis.Dial("tcp", sourceAddr, *sourceAuth, uint32(*sourceIndex), false)
	if err != nil {
		fmt.Printf("connect redis:%s fail.\n", sourceAddr)
		return
	}

	targetAddr := fmt.Sprintf("%s:%d", *targetIp, *targetPort)
	tClient, err := redis.Dial("tcp", targetAddr, *targetAuth, uint32(*targetIndex), false)
	if err != nil {
		fmt.Printf("connect redis:%s fail.\n", targetAddr)
		return
	}

	//fmt.Printf("tClient:%s \n", tClient)

	//typeCmd := fmt.Sprintf("%s %s", "Type", key)

	//keysCmd := fmt.Sprintf("%s %s", "redislv_keys", *keyParttern)
	keysResult, err := sClient.Cmd("redislv_keys", *keyParttern).List()
	if err != nil {
		fmt.Printf("match key fail:%s\n", err)
		return
	}

	//fmt.Printf("keysResult:%+v \n", keysResult)

	for _, key := range keysResult {
		typeResult := sClient.Cmd("TYPE", key).String()
		//fmt.Printf("key:%s type is:%s \n", key, typeResult)
		if typeResult == "" {
			fmt.Printf("key:%s type is empty error \n", key)
			return
		}
		switch typeResult {
		case "string":
			tClient.Cmd("DEL", key)
			keyValue, err := sClient.Cmd("GET", key).Str()
			if err != nil {
				fmt.Printf("string:%s GET error:%s \n", key, err)
				return
			}
			r, err := tClient.Cmd("SET", key, keyValue).Str()
			if err != nil {
				fmt.Printf("string:%s SET:%s error:%s \n", key, keyValue, err)
				return
			}
			fmt.Printf("%-50s(string) %s\n", key, r)
		case "hash":
			tClient.Cmd("DEL", key)
			hashResult, err := sClient.Cmd("HGETALL", key).List()
			if err != nil {
				fmt.Printf("hash:%s cmd error:%s \n", key, err)
				return
			}
			if len(hashResult) > 0 {
				params := make([]any, 0, len(hashResult))
				for i := 0; i < len(hashResult); i++ {
					params = append(params, hashResult[i])
				}
				r, err := tClient.Cmd("HMSET", key, params).Str()
				if err != nil {
					fmt.Printf("HASH:%s HMSET:%+v error:%s \n", key, params, err)
					return
				}
				fmt.Printf("%-50s(hash) %s\n", key, r)
			}
		case "set":
			tClient.Cmd("DEL", key)
			setResult, err := sClient.Cmd("SMEMBERS", key).List()
			if err != nil {
				fmt.Printf("hash:%s cmd error:%s \n", key, err)
				return
			}
			if len(setResult) > 0 {
				params := make([]any, 0, len(setResult))
				for i := 0; i < len(setResult); i++ {
					params = append(params, setResult[i])
				}
				r, err := tClient.Cmd("SADD", key, params).Int()
				if err != nil {
					fmt.Printf("SET:%s SADD:%+v error:%s \n", key, params, err)
					return
				}
				fmt.Printf("%-50s(set) %d\n", key, r)
			}
		case "zset":
			tClient.Cmd("DEL", key)
			zsetResult, err := sClient.Cmd("ZRANGE", key, 0, -1, "WITHSCORES").List()
			if err != nil {
				fmt.Printf("zset:%s cmd error:%s \n", key, err)
				return
			}
			if len(zsetResult) > 0 {
				params := make([]any, 0, len(zsetResult))
				for i := 0; i < len(zsetResult); i++ {
					params = append(params, zsetResult[i+1])
					params = append(params, zsetResult[i])
					i++
				}
				r, err := tClient.Cmd("ZADD", key, params).Int()
				if err != nil {
					fmt.Printf("zSET:%s zADD:%+v error:%s \n", key, params, err)
					return
				}
				fmt.Printf("%-50s(zset) %d\n", key, r)
			}

		default:
			fmt.Printf("key:%s type is %s not support \n", key, typeResult)
			return

		}
	}
}
