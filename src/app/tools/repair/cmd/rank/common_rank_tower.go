package rank

import (
	"app/protos/in/db"
	"app/protos/out/cl"

	"github.com/gogo/protobuf/proto"
)

type UserTower struct {
	db.RankTower
}

func NewUserTower(uid uint64, floorID uint32, tm int64, power uint64) *UserTower {
	return &UserTower{
		db.RankTower{
			Uid:     uid,
			FloorId: floorID,
			Tm:      tm,
			Power:   power,
		},
	}
}

func (u *UserTower) Key() uint64 {
	return u.Uid
}

func (u *UserTower) ShowValue() *cl.RankValue {
	return &cl.RankValue{
		Id:     u.Uid,
		Value:  uint64(u.FloorId),
		Param1: uint64(u.Tm),
		Param2: u.Power,
	}
}

// 从大到小的顺序排列, floor相同的情况tm小的排前面
func (u *UserTower) CmpScore(v1, v2 interface{}) int {
	uv1 := v1.(*UserTower)
	uv2 := v2.(*UserTower)
	if uv1.FloorId == uv2.FloorId && uv1.Tm == uv2.Tm {
		return 0
	}
	if uv1.FloorId > uv2.FloorId ||
		(uv1.FloorId == uv2.FloorId && uv1.Tm < uv2.Tm) {
		return -1
	}
	return 1
}

// 值相同的情况下 uid大的排前面
func (u *UserTower) CmpKey(v1, v2 interface{}) int {
	uv1 := v1.(*UserTower)
	uv2 := v2.(*UserTower)
	if uv1.Uid == uv2.Uid {
		return 0
	}
	if uv1.Uid > uv2.Uid {
		return -1
	}
	return 1
}

// 初始化一个新的UserTower出来，配合load使用
func (u *UserTower) NewElement() CommonRankValuer {
	return &UserTower{
		db.RankTower{},
	}
}

func (u *UserTower) Load(buf []byte) error {
	return proto.Unmarshal(buf, &u.RankTower)
}

func (u *UserTower) Save() []byte {
	buf, _ := proto.Marshal(&u.RankTower)
	return buf
}
