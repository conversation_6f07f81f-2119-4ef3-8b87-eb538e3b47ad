package rank

import (
	"app/protos/in/db"
	"app/protos/out/cl"

	"github.com/gogo/protobuf/proto"
	//l4g "github.com/ivanabc/log4go"
)

type UserDungeon struct {
	db.RankDungeon
}

func NewUserDungeon(uid uint64, dungeonID uint32, tm int64, power uint64) *UserDungeon {
	return &UserDungeon{
		db.RankDungeon{
			Uid:       uid,
			DungeonId: dungeonID,
			Tm:        tm,
			Power:     power,
		},
	}
}

func (u *UserDungeon) Key() uint64 {
	return u.Uid
}

func (u *UserDungeon) ShowValue() *cl.RankValue {
	return &cl.RankValue{
		Id:     u.Uid,
		Value:  uint64(u.DungeonId),
		Param1: u.Power,
	}
}

// 从大到小的顺序排列, dungeon相同的情况tm小的排前面
func (u *UserDungeon) CmpScore(v1, v2 interface{}) int {
	uv1 := v1.(*UserDungeon)
	uv2 := v2.(*UserDungeon)
	if uv1.DungeonId == uv2.DungeonId && uv1.Tm == uv2.Tm {
		return 0
	}
	if uv1.DungeonId > uv2.DungeonId ||
		(uv1.DungeonId == uv2.DungeonId && uv1.Tm < uv2.Tm) {
		return -1
	}
	return 1
}

// 值相同的情况下 uid大的排前面
func (u *UserDungeon) CmpKey(v1, v2 interface{}) int {
	uv1 := v1.(*UserDungeon)
	uv2 := v2.(*UserDungeon)
	if uv1.Uid == uv2.Uid {
		return 0
	}
	if uv1.Uid > uv2.Uid {
		return -1
	}
	return 1
}

// 初始化一个新的UserDungeon出来，配合load使用
func (u *UserDungeon) NewElement() CommonRankValuer {
	return &UserDungeon{
		db.RankDungeon{},
	}
}

func (u *UserDungeon) Load(buf []byte) error {
	return proto.Unmarshal(buf, &u.RankDungeon)
}

func (u *UserDungeon) Save() []byte {
	buf, _ := proto.Marshal(&u.RankDungeon)
	return buf
}
