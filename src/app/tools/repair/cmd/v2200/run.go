package v2200

import (
	"app/goxml"
	"app/logic/helper/log"
	"app/protos/in/db"
	logP "app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"strconv"

	jsoniter "github.com/json-iterator/go"
	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/platform/proto/da"

	"gitlab.qdream.com/kit/sea/ctx"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath                     = flag.String("V2200configData", "../data/", "service data path")
	fixGstFormationFailedCount       uint32
	fixHeroEmblemSkillLogFailedCount uint32 //
	param9                           = ""
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	l4g.Info("\n")

	dcLogPath, _ := cmd.Flags().GetString("V2200dcLog")

	_, err := os.Stat(dcLogPath)
	if os.IsNotExist(err) {
		panic(fmt.Sprintf("dcLogPath not exist: path:%s", dcLogPath))
	}

	zone, offset := time.Now().Zone()
	zoneOffset := offset / 3600
	param9 = zone + "|" + strconv.FormatInt(int64(zoneOffset), 10)

	collect := log.NewCollect(ctx.Background().CreateChild(), 2048, filepath.Join(dcLogPath, "dc.log"), 0)

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixGstFormation(uid, redisClient)
		fixHeroEmblemSkillLog(collect, uid, redisClient)
	})

	if fixGstFormationFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGstFormation Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGstFormation Failed, need check error log! failedCount:%d\n", fixGstFormationFailedCount)
	}
	if fixHeroEmblemSkillLogFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixHeroEmblemSkillLog Success!\n")
	} else {
		fmt.Printf("[FAILED] fixHeroEmblemSkillLog Failed, need check error log! failedCount:%d\n", fixHeroEmblemSkillLogFailedCount)
	}

	collect.Close()
	ctx.Stop()
	ctx.Wait()
	l4g.Info("finish repair")
}

func fixGstFormation(uid string, r *redis.Redis) {

	dragonFormationIds := make([]uint32, 0, 6)
	for _, info := range goxml.GetData().FormationInfoM.GetFuncFormationInfos(uint32(common.FUNCID_MODULE_GST_DRAGON)) {
		if info == nil {
			continue
		}
		dragonFormationIds = append(dragonFormationIds, info.Id)
	}
	r.RopCl.GetSomeFormationMCallSKs(uid, dragonFormationIds)
	gstDragonFormations, err := r.RopCl.GetSomeFormationByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get formations error: %s %s", uid, err)
		fixGstFormationFailedCount++
		return
	}

	changeFormation := make([]*cl.Formation, 0, 6)

	for _, formation := range gstDragonFormations {
		if len(formation.Teams) <= 4 {
			continue
		}
		newFormation := formation.Clone()
		newFormation.Teams = newFormation.Teams[0:4] // 龙战的队伍，保留4队
		changeFormation = append(changeFormation, newFormation)
	}

	r.RopCl.GetSomeFormationMCallSKs(uid, []uint32{uint32(common.FORMATION_ID_FI_GST)})
	gstFormations, err := r.RopCl.GetSomeFormationByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get formations error: %s %s", uid, err)
		fixGstFormationFailedCount++
		return
	}
	for _, formation := range gstFormations {
		if len(formation.Teams) <= 6 {
			continue
		}
		newFormation := formation.Clone()
		newFormation.Teams = newFormation.Teams[0:6] // 公会战的队伍，保留6队
		changeFormation = append(changeFormation, newFormation)
	}

	if len(changeFormation) > 0 {
		r.RopCl.SetSomeFormationMCallSKs(uid, changeFormation)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixGstFormationFailedCount++
			l4g.Errorf("user %s save error: %s %s", uid, changeFormation, reply.Err)
			return
		}
	}
	l4g.Info("user %s save fixGstFormation success", uid)
}

func fixHeroEmblemSkillLog(collect *log.Collect, uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		fixHeroEmblemSkillLogFailedCount++
		return
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		fixHeroEmblemSkillLogFailedCount++
		return
	}

	if util.DaysBetweenTimes(userData.Base.OfflineTime, time.Now().Unix()) > 7 {
		// 离线超过七天的不处理
		return
	}

	heroData, err := r.RopCl.GetAllHeroBodySKs(uid)
	if err != nil {
		l4g.Error("get heroes error: %s %s", uid, err)
		fixHeroEmblemSkillLogFailedCount++
		return
	}

	emblemData, err := r.RopCl.GetAllEmblemInfoSKs(uid)
	if err != nil {
		l4g.Error("get emblem info error: %s %s", uid, err)
		fixHeroEmblemSkillLogFailedCount++
		return
	}

	for hid, heroBody := range heroData {
		if len(heroBody.Emblem) < 4 {
			continue
		}
		var exEmblemRedNum, exEmblemOrangeNum uint32
		heroInfo := goxml.GetData().HeroInfoM.Index(heroBody.SysId)
		minRare := uint32(math.MaxUint32)
		for _, emblemId := range heroBody.Emblem {
			emblem := emblemData[emblemId]
			if emblem == nil {
				break
			}
			info := goxml.GetData().EmblemInfoM.Index(emblem.SysId)
			if info == nil {
				l4g.Errorf("emblem info not exist. uid:%s, emblem sys id:%d", uid, emblem.SysId)
				break
			}
			if heroInfo.Id == emblem.AdditiveHero {
				if info.Rare >= goxml.EmblemRareRed {
					exEmblemRedNum++
				} else if info.Rare >= goxml.EmblemRareOrange {
					exEmblemOrangeNum++
				}
			}
			//羁绊技能加成最小判断
			if minRare > info.Rare {
				minRare = info.Rare
			}
		}

		exclusiveLv := uint32(0)
		//专属技能判断
		exclusiveId := getEmblemExclusiveSkillId(exEmblemRedNum, exEmblemOrangeNum, heroInfo)
		if exclusiveId > 0 {
			if exclusiveId == heroInfo.EmblemExclusive3 {
				exclusiveLv = 3
			} else if exclusiveId == heroInfo.EmblemExclusive2 {
				exclusiveLv = 2
			} else if exclusiveId == heroInfo.EmblemExclusive1 {
				exclusiveLv = 1
			}
		}
		counterSkillActivated := false
		if minRare >= goxml.GetData().EmblemConfigInfoM.GetHeroSkillUnlockRare1() && heroInfo.EmblemSkill1 > 0 {
			counterSkillActivated = true
		}
		if counterSkillActivated || exclusiveLv > 0 {
			generateLog(collect, userData, hid, heroBody.SysId, counterSkillActivated, exclusiveLv)
		}
	}
	l4g.Info("user %s fixHeroEmblemSkillLog success", uid)
}

func generateLog(collect *log.Collect, user *db.User, hid uint64, sysId uint32, counterSkillActivated bool, exclusiveLv uint32) {
	power := user.Base.Power
	if user.Base.CrystalPower > 0 {
		power = user.Base.CrystalPower
	}
	now := time.Now().Unix()
	msg := collect.NewMessage()
	msg.Topic = "log-ngame"
	// msg.EventID = srv.CreateLogID()
	msg.EventTime = uint64(now)
	// msg.ContextID = u.ContextID()
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC)
	msg.LogSubType = 0
	msg.AppID = uint32(1)
	msg.OpGroup = uint32(3)
	// msg.OpID = u.OpID()
	// msg.Channel = u.Channel()
	msg.UUID = user.Uuid
	msg.ServerID = user.ServerId
	msg.UserID = user.Id
	msg.CreateTime = user.Base.CreateTime
	msg.Name = user.Name
	msg.Level = user.Base.Level
	msg.VipLevel = user.Base.Vip
	msg.Power = uint64(power)
	msg.PayDiamond = user.Base.RechargeDiamond
	msg.GiftDiamond = user.Base.PresentDiamond
	msg.Gold = user.Base.Gold
	msg.TotalCash = user.Base.RechargeAmount
	msg.Paid = uint32(user.Base.TotalRechargeDiamond)
	// msg.ClientIP = u.NetAddr()
	// msg.DeviceID = u.DeviceID()
	// msg.OpenDay = uint32(srv.ServerDay(now))
	msg.LoginDay = user.Base.LoginDay
	msg.SeasonID = goxml.GetLogFormatSeasonId(now)
	msg.SeasonLevel = user.Base.SeasonLv
	msg.SeasonPower = uint64(user.Base.SeasonPower)
	// msg.GuildID = srv.GetGuildID(u.ID())
	msg.AccountTag = user.Base.AccountTag
	// msg.BattleZone = u.GetArea()
	msg.Pid = user.Base.Pid
	msg.Gid = user.Base.Gid
	// msg.Param8 = u.AdapterExtra()
	msg.Param9 = param9

	msg.TypeName = "英雄-符文技能改变"
	msg.LogSubType = uint32(logP.SUB_TYPE_ID_HERO_EMBLEM_SKILL)
	info := &logP.LogHeroEmblemSkill{
		Hid:          hid,
		SysId:        sysId,
		CounterSkill: counterSkillActivated,
		ExclusiveLv:  exclusiveLv,
	}
	msg.Param1, _ = JSONMarshal(info) //
	collect.Write(msg)
}

var json = jsoniter.ConfigCompatibleWithStandardLibrary

func JSONMarshal(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err == nil {
		return util.String(bytes), nil
	}
	l4g.Errorf("json marshal error:%v %v", err, v)
	return "", err
}

func getEmblemExclusiveSkillId(exEmblemRedNum, exEmblemOrangeNum uint32, heroInfo *goxml.HeroInfoExt) uint32 {
	//专属技能判断
	if exEmblemRedNum == goxml.EmblemExclusiveNumFour && heroInfo.EmblemExclusive3 > 0 {
		return heroInfo.EmblemExclusive3
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumThree {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumOne && heroInfo.EmblemExclusive2 > 0 {
			return heroInfo.EmblemExclusive2
		}
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumTwo {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumTwo && heroInfo.EmblemExclusive2 > 0 {
			return heroInfo.EmblemExclusive2
		}
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumOne {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumThree && heroInfo.EmblemExclusive1 > 0 {
			return heroInfo.EmblemExclusive1
		}
	} else if exEmblemOrangeNum == goxml.EmblemExclusiveNumFour && heroInfo.EmblemExclusive1 > 0 {
		return heroInfo.EmblemExclusive1
	}
	return 0
}
