package v093

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/base"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/utils"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"time"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
	"gitlab.qdream.com/kit/sea/util"
)

var (
	fixEmblemFailedCount          uint32
	fixCrystalCount               uint32
	fixCrystalFailedCount         uint32
	fixPowerFailedCount           uint32
	fixPowerCount                 uint32
	fixGlobalRankFailedCount      uint32
	flagDataPath                  = flag.String("V093configData", "../data/", "service data path")
	fixGuildFailedCount           uint32
	fixGuildCount                 uint32
	fixMirageCount                uint32
	fixMirageFailedCount          uint32
	fixMirageRankCount            uint32
	fixMirageRankFailedCount      uint32
	fixGemCount                   uint32
	fixGemFailedCount             uint32
	fixMazeMapCount               uint32
	fixMazeMapFailedCount         uint32
	fixEmblemRetResFailedCount    uint32
	fixDelOldEmblemFailedCount    uint32
	fixTowerCount                 uint32
	fixTowerFailedCount           uint32
	fixGoddessResourceCount       uint32
	fixGoddessResourceFailedCount uint32
	fixGoddessContractCount       uint32
	fixGoddessContractFailedCount uint32
	fixPowerRankFailedCount       uint32
	globalRank                    map[uint64]*db.GlobalRank
	commonPowerRank               map[uint64]*db.CommonRank
)

// 纹章-初始养成等级
const (
	EmblemInitialStrengthLevel uint32 = 1 // 纹章初始强化等级
	EmblemInitialBlessingLevel uint32 = 0 // 纹章初始祝福等级
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	getPowerRank(redisClient)
	getGlobalRank(redisClient)

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixEmblem(uid, redisClient, idFactory)
		fixGem(uid, redisClient)
		fixCrystalEmblemAndGem(uid, redisClient)
		fixGoddessContract(uid, redisClient)
		fixPower(uid, redisClient) // 在所有养成的后面执行
		fixMirage(uid, redisClient)
		fixTower(uid, redisClient, idFactory)
		fixGoddessResource(uid, redisClient)
	})
	fixMirageRank(redisClient)
	fixMazeMap(redisClient)
	savePowerRank(redisClient)
	saveGlobalRank(redisClient)
	l4g.Info("\n")
	if fixEmblemFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixEmblem Success!")
	} else {
		l4g.Infof("[FAILED] fixEmblem Failed, need check error log! failedCount:%d", fixEmblemFailedCount)
	}

	// l4g.Infof("have crystal length:%d, uids:%v", len(haveCrystal), haveCrystal)
	if fixCrystalFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixCrystalEmblemAndGem Success! count:%d", fixCrystalCount)
	} else {
		l4g.Infof("[FAILED] fixCrystalEmblemAndGem Failed, need check error log! failedCount:%d", fixCrystalFailedCount)
	}

	if fixPowerFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPower Success!")
	} else {
		l4g.Infof("[FAILED] fixPower Failed, need check error log! failedCount:%d", fixPowerFailedCount)
	}

	if fixGuildFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixGuild Success! count:%d", fixGuildCount)
	} else {
		l4g.Infof("[FAILED] fixGuild Failed, need check error log! failedCount:%d", fixGuildFailedCount)
	}

	if fixMirageFailedCount == 0 {
		l4g.Info("[SUCCESS] fixMirage Success!")
	} else {
		l4g.Infof("[FAILED] fixMirage Failed, need check error log! failedCount:%d", fixMirageFailedCount)
	}

	if fixMirageRankFailedCount == 0 {
		l4g.Info("[SUCCESS] fixMirageRank Success!")
	} else {
		l4g.Infof("[FAILED] fixMirageRank Failed, need check error log! failedCount:%d", fixMirageRankFailedCount)
	}

	if fixGemFailedCount == 0 {
		l4g.Info("[SUCCESS] fixGem Success!")
	} else {
		l4g.Infof("[FAILED] fixGem Failed, need check error log! failedCount:%d", fixGemFailedCount)
	}

	if fixMazeMapFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixMazeMap Success!")
	} else {
		l4g.Infof("[FAILED] fixMazeMap Failed, need check error log! failedCount:%d", fixMazeMapFailedCount)
	}

	if fixMirageFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixMirage Success!")
	} else {
		l4g.Infof("[FAILED] fixMirage Failed, need check error log! failedCount:%d", fixMirageFailedCount)
	}

	if fixMirageRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixMirageRank Success!")
	} else {
		l4g.Infof("[FAILED] fixMirageRank Failed, need check error log! failedCount:%d", fixMirageRankFailedCount)
	}

	if fixTowerFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixTower Success! count:%d", fixTowerCount)
	} else {
		l4g.Infof("[FAILED] fixTower Failed, need check error log! failedCount:%d", fixTowerFailedCount)
	}

	if fixEmblemRetResFailedCount == 0 {
		l4g.Info("[SUCCESS] fixEmblemRetRes Success!\n")
	} else {
		l4g.Info("[FAILED] fixEmblemRetRes Failed, need check error log! failedCount:%d\n", fixEmblemRetResFailedCount)
	}

	if fixDelOldEmblemFailedCount == 0 {
		l4g.Info("[SUCCESS] fixDelOldEmblem Success!\n")
	} else {
		l4g.Info("[FAILED] fixDelOldEmblem Failed, need check error log! failedCount:%d\n", fixDelOldEmblemFailedCount)
	}

	if fixGoddessResourceFailedCount == 0 {
		l4g.Info("[SUCCESS] fixGoddessResource Success!\n")
	} else {
		l4g.Info("[FAILED] fixGoddessResource Failed, need check error log! failedCount:%d\n", fixGoddessResourceFailedCount)
	}

	if fixGoddessContractFailedCount == 0 {
		l4g.Info("[SUCCESS] fixGoddessContract Success!\n")
	} else {
		l4g.Info("[FAILED] fixGoddessContractFailed Failed, need check error log! failedCount:%d\n", fixGoddessContractFailedCount)
	}

	l4g.Info("finish repair")
}

func fixEmblem(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopCl.GetAllHeroBodyMCallSKs(uid)
	heroDatas, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
		return
	}
	//emblem
	r.RopCl.GetAllEmblemInfoMCallSKs(uid)
	emblemDatas, err := r.RopCl.GetSomeEmblemInfoByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
		return
	}

	var (
		deletes   []uint64
		newHeroes []*cl.HeroBody
		//newEmblems     []*cl.EmblemInfo
		returnResource []*cl.Resource
	)

	stageLevelPool := make(map[uint32]uint32)

	for _, emblem := range emblemDatas {
		if emblem == nil {
			continue
		}
		heroId := emblem.Hid
		returnResource = append(returnResource, returnSource(emblem, stageLevelPool)...)
		deletes = append(deletes, emblem.Id)

		if hero, exist := heroDatas[heroId]; exist {
			hero.Emblem = make(map[uint32]uint64)
			newHeroes = append(newHeroes, hero)
		}
	}

	l4g.Infof("user: %s newHeroes: %+v", uid, newHeroes)
	l4g.Infof("user: %s returnResource: %+v", uid, returnResource)
	if len(newHeroes) > 0 {
		r.RopCl.SetSomeHeroBodyMCallSKs(uid, newHeroes)

		/************************REPLY*******************************/
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixEmblemFailedCount++
			l4g.Errorf("user %s save user error: %+v", uid, reply.Err)
			return
		}

		l4g.Infof("update hero data success!")
	}

	if len(deletes) > 0 {
		r.RopCl.RemSomeEmblemInfoMCallSKs(uid, deletes)

		/************************REPLY*******************************/
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixEmblemFailedCount++
			l4g.Errorf("user %s save user error: %+v", uid, reply.Err)
			return
		}

		l4g.Infof("update emblem data success!")
	}

	if len(returnResource) > 0 {
		if !utils.Award(uid, r, returnResource) {
			l4g.Errorf("user: %s revive emblem failed. ", uid)
			fixEmblemFailedCount++
		} else {
			l4g.Infof("user: %s return resource: %+v", uid, returnResource)
		}
	}

	// 处理邮件中的已删掉的符文
	if !handleMail(uid, r) {
		fixDelOldEmblemFailedCount++
		l4g.Errorf("user: %s handle mail's emblem failed. ", uid)
		return
	}

	emblemCompensateMail(uid, r, idFactory, stageLevelPool)
}

func returnSource(emblem *cl.EmblemInfo, stageLevelPool map[uint32]uint32) []*cl.Resource {
	info := xmlcfg.V090Emblem.Index(emblem.SysId)
	if info == nil {
		return nil
	}
	var capNeed uint32
	if emblem.Level > EmblemInitialStrengthLevel {
		capNeed += (emblem.Level - EmblemInitialStrengthLevel) * goxml.EmblemLevelupResCostCount
	}
	//if emblem.Blessing.Level >= EmblemInitialBlessingLevel {
	//	capNeed += (emblem.Level - EmblemInitialBlessingLevel + 1) * goxml.EmblemLevelupResCostCount
	//}
	retRes := make([]*cl.Resource, 0, capNeed)

	for i := EmblemInitialStrengthLevel; i < emblem.Level; i++ {
		levelCfg := xmlcfg.V090EmblemLevel.Index(info.LevelIndex, i)
		if levelCfg != nil {
			retRes = append(retRes, levelCfg.Costs...)
		}
	}

	// 之前等级的祝福资源
	//for i := EmblemInitialBlessingLevel; i < emblem.Blessing.Level; i++ {
	//	blessingCfg := xmlcfg.V090EmblemBless.Index(info.BlessingIndex, i)
	//	if blessingCfg != nil {
	//		retRes = append(retRes, blessingCfg.TotalCosts...)
	//	}
	//}
	//// 当前等级的祝福资源
	//if curCount := CurBlessingCount(emblem); curCount > 0 {
	//	blessingCfg := xmlcfg.V090EmblemBless.Index(info.BlessingIndex, emblem.Blessing.Level)
	//	if blessingCfg != nil {
	//		for _, cost := range blessingCfg.Costs {
	//			retRes = append(retRes, &cl.Resource{
	//				Type:  cost.Type,
	//				Value: cost.Value,
	//				Count: cost.Count * curCount,
	//			})
	//		}
	//	}
	//}
	stageLevelPool[info.Stage]++

	return retRes
}

// 当前等级的祝福次数
func CurBlessingCount(emblem *cl.EmblemInfo) uint32 {
	info := xmlcfg.V090Emblem.Index(emblem.SysId)
	if info == nil {
		return 0
	}
	//blessingCfg := xmlcfg.V090EmblemBless.Index(info.BlessingIndex, emblem.Blessing.Level)
	//if blessingCfg == nil {
	//	return 0
	//}
	//lastLimit := make(map[uint32]uint32)
	//if emblem.Blessing.Level > EmblemInitialBlessingLevel {
	//	lastCfg := xmlcfg.V090EmblemBless.Index(info.BlessingIndex, emblem.Blessing.Level-1)
	//	if lastCfg != nil {
	//		for attr, limit := range lastCfg.AttrLimit {
	//			lastLimit[attr] = limit.GrewLimit
	//		}
	//	}
	//}
	var retCount uint32
	//for k, v := range emblem.Blessing.AttrProgress {
	//	curLimit := blessingCfg.AttrLimit[k]
	//	if curLimit == nil {
	//		continue
	//	}
	//	if v > lastLimit[k] {
	//		retCount += (v - lastLimit[k]) / curLimit.GrewAdd
	//	}
	//}
	return retCount
}

func calcScore(userID string, emblem *cl.EmblemInfo) int64 {
	var score int64
	attrs := make(map[uint32]int64)
	info := goxml.GetData().EmblemInfoM.Index(emblem.SysId)
	if info == nil {
		l4g.Errorf("user:%s calc emblem score  info error. ", userID)
		return score
	}

	for k, v := range info.Attr {
		attrs[k] = v
	}

	strengthAttr := goxml.GetData().EmblemLevelInfoM.GetAttr(info.LevelIndex, emblem.Level)
	for attrId, value := range strengthAttr {
		isZero, attrValue := util.DivFloor(goxml.BaseInt64, int64(info.StrengthCoe), value)
		if isZero {
			l4g.Errorf("user:%s sysid:%d calc emblem score failed attrId:%d attrNum:%d",
				userID, emblem.SysId, attrId, value)
			continue
		}
		attrs[attrId] += attrValue
	}

	//for attrId, value := range emblem.Blessing.AttrProgress {
	//	attrs[attrId] += int64(value)
	//}
	//
	//// 祝福高阶属性
	//blessingAttr :=  goxml.GetData().EmblemBlessingInfoM.GetBlessingAttr(info.BlessingIndex, emblem.Blessing.Level)

	//for attrId, value := range blessingAttr {
	//	attrs[attrId] += value
	//}

	for attrId, attrNum := range attrs {
		success, attrScore := goxml.GetData().AttributeInfoM.CalcScore(attrId, attrNum)
		if !success {
			l4g.Errorf("user:%s emblem sysid:%d calc score failed attrId:%d attrNum:%d",
				userID, emblem.SysId, attrId, attrNum)
			continue
		}
		score += attrScore
	}

	emblem.Score = score
	return score
}

var haveCrystal []string

func checkHaveCrystal(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData != nil && userData.Crystal != nil {
		haveCrystal = append(haveCrystal, uid)
	}
}

func delCrystalRedisKeys(uid string, r *redis.Redis) {
	strKeyName := "u:" + uid
	ptReply := r.Client.Cmd("HDEL", strKeyName, "crystal")
	if ptReply.Err != nil {
		l4g.Error("delRedisKeys Error %v", ptReply.Err)
		return
	}
	l4g.Infof("user %s delRedisKeys success", uid)
}

func fixCrystalEmblemAndGem(uid string, r *redis.Redis) {
	// user
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		fixCrystalFailedCount++
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Crystal == nil || userData.Crystal.Crystal == nil || userData.Crystal.Crystal.Attr == nil {
		return
	}

	if userData.Crystal.Crystal.Attr.Emblem != nil || userData.Crystal.Crystal.Attr.Gem != nil ||
		userData.Crystal.Crystal.Attr.Emblems != nil || userData.Crystal.Crystal.Attr.Gems != nil {
		if userData.Crystal.Crystal.Attr.Emblem != nil {
			l4g.Infof("user: %s crystal emblemAttr: %+v", uid, userData.Crystal.Crystal.Attr.Emblem)
			userData.Crystal.Crystal.Attr.Emblem = nil
		}
		if userData.Crystal.Crystal.Attr.Gem != nil {
			l4g.Infof("user: %s crystal gemRare: %+v", uid, userData.Crystal.Crystal.Attr.Gem)
			userData.Crystal.Crystal.Attr.Gem = nil
		}
		if userData.Crystal.Crystal.Attr.Emblems != nil {
			l4g.Infof("user: %s crystal emblemsAttr: %+v", uid, userData.Crystal.Crystal.Attr.Emblems)
			userData.Crystal.Crystal.Attr.Emblems = nil
		}
		if userData.Crystal.Crystal.Attr.Gems != nil {
			l4g.Infof("user: %s crystal gemsRare: %+v", uid, userData.Crystal.Crystal.Attr.Gems)
			userData.Crystal.Crystal.Attr.Gems = nil
		}

		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixCrystalFailedCount++
			l4g.Errorf("user %s save crystal error: %s %s", uid, userData, reply.Err)
			return
		}
		fixCrystalCount++
	}
}

func fixPower(uid string, redisClient *redis.Redis) {
	u := base.LoadUser(uid, redisClient)
	fixArenaPowerAndForestPower(uid, u, redisClient)
	fixTop5Power(uid, u, redisClient)
}

func fixArenaPowerAndForestPower(uid string, u *character.User, redisClient *redis.Redis) {
	// heroM := u.HeroManager()
	// formationM := u.FormationManager()
	// arenaDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_ARENA_DEFENSE))
	// if arenaDefenseFormation != nil {
	// 	var power int64
	// 	for _, info := range arenaDefenseFormation.Info {
	// 		hero := heroM.Get(info.Hid)
	// 		if hero != nil {
	// 			power += int64(hero.GetHeroPower(u))
	// 		}
	// 	}
	// 	u.SetArenaPower(power)
	// 	l4g.Infof("newUserData:%s arenaPower%d", uid, power)
	// }
	// forestDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_FOREST_DEFENSE))
	// if forestDefenseFormation != nil {
	// 	var power int64
	// 	for _, info := range forestDefenseFormation.Info {
	// 		hero := heroM.Get(info.Hid)
	// 		if hero != nil {
	// 			power += int64(hero.GetHeroPower(u))
	// 		}
	// 	}
	// 	u.SetForestPower(power)
	// 	l4g.Infof("newUserData:%s forestPower%d", uid, power)
	// }
	// redisClient.RopDB.SetUserMCallSKs(uid, u.GetDBUser())
	// if reply := redisClient.Client.GetReply(); reply.Err != nil {
	// 	fixPowerFailedCount++
	// 	l4g.Errorf("[fixArenaPowerAndForestPower] user %s save user error: %s", uid, reply.Err)
	// } else {
	// 	fixPowerCount++
	// }

}

func fixTop5Power(uid string, u *character.User, redisClient *redis.Redis) {
	userData := u.GetDBUser()
	heroM := u.HeroManager()
	// heroM.ReCalTop5()
	heroHeap := heroM.GetTop5Heros()
	l4g.Infof("oldUserData:%s top5%v", uid, userData.Base.Top5Heros)
	userData.Base.Top5Heros = make([]uint64, character.Top5HeroNum)
	var totalPower uint64
	for index, hero := range heroHeap {
		userData.Base.Top5Heros[index] = hero.GetData().Id
		totalPower += hero.GetHeroPower(u)
	}
	userData.Base.Power = int64(totalPower)
	userData.Base.PowerTm = time.Now().Unix()
	userData.Base.MaxPower = int64(totalPower)

	//更新通用战力榜战力
	if userRank, exist := commonPowerRank[userData.Id]; exist {
		userPower := &rank.UserPower{}
		userPower.Load(userRank.Data)
		userPower.Power = uint64(userData.Base.Power)
		userPower.Tm = userData.Base.PowerTm
		userRank.Data = userPower.Save()
		l4g.Infof("update common power rank: %d power:%d", userPower.Uid, userPower.Power)
	}
	//更新全局战力
	if userRank, exist := globalRank[userData.Id]; exist {
		userRank.Power = userData.Base.Power
		userRank.PowerTm = userData.Base.PowerTm
		l4g.Infof("update global rank: %d power:%d", userRank.Id, userRank.Power)
	}

	redisClient.RopDB.SetUserMCallSKs(uid, userData)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixPowerFailedCount++
		l4g.Errorf("[top5] user %s save user error: %s", uid, reply.Err)
	} else {
		fixPowerCount++
	}

	l4g.Infof("newUserData:%s top5%v", uid, userData.Base.Top5Heros)
}

func fixMirage(uid string, redisClient *redis.Redis) {
	// 修改成就奖励领取状态
	{
		redisClient.RopDB.GetUserMCallSKs(uid)
		userData, err := redisClient.RopDB.GetUserByReply(redisClient.Client.GetReply())
		if err != nil {
			l4g.Error("get user error: %s %s", uid, err)
			//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
		}

		if userData == nil {
			fixMirageFailedCount++
			l4g.Error("user %s data corrupt", uid)
			return
		}

		if userData.Module1 == nil || userData.Module1.RankAchieveAwards == nil {
			return
		}

		rankIDs := make(map[uint32]struct{})
		for _, rankID := range goxml.MirageCopyID2RankId {
			rankIDs[rankID] = struct{}{}
		}

		isChange := false
		achieveAwards := userData.Module1.RankAchieveAwards
		for i := 0; i < len(achieveAwards.Id); i++ {
			goalID := achieveAwards.Id[i]
			info := goxml.GetData().RankingAchievementInfoM.Index(goalID)
			if info == nil {
				l4g.Errorf("RankingAchievementInfo not exist: %s %d", uid, goalID)
				continue
			}
			if _, exist := rankIDs[info.RankId]; exist {
				achieveAwards.Id = append(achieveAwards.Id[:i], achieveAwards.Id[i+1:]...)
				i--
				isChange = true
			}
		}

		if isChange {
			redisClient.RopDB.SetUserMCallSKs(uid, userData)
			if reply := redisClient.Client.GetReply(); reply.Err != nil {
				fixMirageFailedCount++
				l4g.Errorf("user %s save user error: %s %s", uid, userData, reply.Err)
				return
			}
		}
	}

	redisClient.RopCl.GetAllMirageMCallSKs(uid)
	mirageDatas, err := redisClient.RopCl.GetSomeMirageByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("get mirage data error: %s %s", uid, err)
		fixMirageFailedCount++
	}
	if len(mirageDatas) == 0 {
		return
	}

	deletes := make([]uint32, 0, len(mirageDatas))
	for hurdleID := range mirageDatas {
		deletes = append(deletes, hurdleID)
	}

	redisClient.RopCl.RemSomeMirageMCallSKs(uid, deletes)

	/************************REPLY*******************************/
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save delete mirage data error: %v", uid, reply.Err)
		fixMirageFailedCount++
		return
	}

	fixMirageCount++
}

func fixMirageRank(redisClient *redis.Redis) {
	calls := 0
	achieveRankIDs := make([]uint32, 0, len(goxml.MirageCopyID2RankId))
	// 删除排行榜数据
	for _, rankID := range goxml.MirageCopyID2RankId {
		redisClient.RopDB.DelAllCommonRankMCallSK(uint64(rankID))
		calls++
		achieveRankIDs = append(achieveRankIDs, rankID)
	}
	// 删除成就数据
	redisClient.RopDB.RemSomeRankAchievesMCall(achieveRankIDs)
	calls++

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save delete mirage rank data error: %s", reply.Err)
			fixMirageRankFailedCount++
			return
		}
	}

	fixMirageRankCount++
}

func fixGem(uid string, r *redis.Redis) {
	r.RopCl.GetAllHeroBodyMCallSKs(uid)
	heroDatas, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
		return
	}
	//gem
	r.RopCl.GetAllGemInfoMCallSKs(uid)
	gemDatas, err := r.RopCl.GetSomeGemInfoByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
		return
	}

	var (
		newHeroes []*cl.HeroBody
		newGem    []*cl.GemInfo
		change    bool
	)

	for _, hero := range heroDatas {
		for slot, gemId := range hero.Gem {
			if gemId != 0 {
				change = true
				delete(hero.Gem, slot)
				gem, exist := gemDatas[gemId]
				if exist {
					gem.HeroId = 0
					newGem = append(newGem, gem)
				}
			}
		}
		if change {
			newHeroes = append(newHeroes, hero)
			change = false
		}
	}

	l4g.Infof("user: %s newHeroes: %+v", uid, newHeroes)
	l4g.Infof("user: %s newGem: %+v", uid, newGem)
	if len(newHeroes) > 0 {
		r.RopCl.SetSomeHeroBodyMCallSKs(uid, newHeroes)

		/************************REPLY*******************************/
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixGemFailedCount++
			l4g.Errorf("user %s save user error: %v", uid, reply.Err)
			return
		}

		l4g.Infof("update hero data success!")
	}

	if len(newGem) > 0 {
		r.RopCl.SetSomeGemInfoMCallSKs(uid, newGem)

		/************************REPLY*******************************/
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixGemFailedCount++
			l4g.Errorf("user %s save user error: %v", uid, reply.Err)
			return
		}

		l4g.Infof("update gem data success!")
	}
}

func fixMazeMap(r *redis.Redis) {
	r.RopCl.GetAllMazeMapMCall()
	data, err := r.RopCl.GetSomeMazeMapByReply(r.Client.GetReply())
	if err != nil {
		fixMazeMapFailedCount++
		l4g.Errorf("get maze basic map data error: %+v", err)
	}

	deletes := make([]uint32, 0, len(data))
	for mapID := range data {
		deletes = append(deletes, mapID)
	}

	if len(deletes) > 0 {
		r.RopCl.RemSomeMazeMapMCall(deletes)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixMazeMapFailedCount++
			l4g.Errorf("delete mazeMap error: %s", reply.Err)
			return
		}
		fixMazeMapCount++
	}
}

func fixTower(uid string, redisClient *redis.Redis, idFactory *id.Factory) {
	u := base.LoadUser(uid, redisClient)
	tower := u.Towers().Get(uint32(common.TOWER_TYPE_TT_COMMON))
	if tower == nil {
		return
	}

	res := make([]*cl.Resource, 0, 2)
	if tower.Floor >= 100 {
		res = append(res, &cl.Resource{
			Type:  4,
			Value: 10005,
			Count: ((tower.Floor-100)/200 + 1) * 7,
		})
		if tower.Floor >= 200 {
			res = append(res, &cl.Resource{
				Type:  5,
				Value: 50019,
				Count: (tower.Floor / 200) * 50,
			})
		}
	}

	if len(res) > 0 {
		l4g.Infof("user: %s tower award: %+v", uid, res)
		params := make([]string, 0, 2)
		params = append(params, "Compensation for Palace of Penrose")
		params = append(params, "We have made adjustments to the rewards for the Palace of Penrose. Here we compensate the stage rewards. Please claim it on time.")

		m := mail.NewMail(character.MailIDGM, idfactory.CreateID(idFactory), params, res, mail.TypeNormal, 0, 0)
		redisClient.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s send mail error: %s", uid, reply.Err)
			fixTowerFailedCount++
			return
		}
		fixTowerCount++
	}
}

func emblemCompensateMail(uid string, r *redis.Redis, idFactory *id.Factory, stageLevelPool map[uint32]uint32) {
	awards := make([]*cl.Resource, 0, len(stageLevelPool))
	logSendAwards := make([]cl.Resource, 0, len(stageLevelPool))
	l4g.Infof("uid: %s emblemCompensateMail stageLevelPool: %+v", uid, stageLevelPool)
	for stageLevel, count := range stageLevelPool {
		var sysID uint32
		switch stageLevel {
		case 1:
			sysID = 30033
		case 2:
			sysID = 30034
		case 3:
			sysID = 30035
		case 4:
			sysID = 30036
		case 5:
			sysID = 30037
		case 6:
			sysID = 30038
		case 7:
			sysID = 30039
		case 8:
			sysID = 30040
		case 9:
			sysID = 30041
		case 10:
			sysID = 30042
		case 11:
			sysID = 30043
		case 12:
			sysID = 30044
		case 13:
			sysID = 30045
		case 14:
			sysID = 30046
		case 15:
			sysID = 30047
		}

		if sysID > 0 {
			res := genResource(uint32(common.RESOURCE_ITEM), sysID, count)
			awards = append(awards, res)
			logSendAwards = append(logSendAwards, *res)
		}
	}

	if len(awards) > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Rune Upgrade Materials Returned")
		params = append(params, "We have made adjustments to the Rune Evolution function. Please accept the return of all corresponding upgrade materials.")
		if !sendMail(uid, r, idFactory, character.MailIDGM, awards, params) {
			fixEmblemRetResFailedCount++
		}

		l4g.Infof("user: %s send resource mail, res: %+v", uid, logSendAwards)
	}
}

func genResource(typ, value, count uint32) *cl.Resource {
	return &cl.Resource{
		Type:  typ,
		Value: value,
		Count: count,
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user error: %s", uid, reply.Err)
		return false
	}

	return true
}

func handleMail(uid string, r *redis.Redis) bool {
	r.RopCl.GetAllMailMCallSKs(uid)
	mailsData, err := r.RopCl.GetSomeMailByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user mails error: %s %s", uid, err)
	}

	var (
		changes     []*cl.Mail
		mailDeletes []uint64
		logEmblems  []cl.Resource
	)

	for _, mail := range mailsData {
		isChange := false
		for i := 0; i < len(mail.Awards); i++ {
			res := mail.Awards[i]
			if res.Type == uint32(common.RESOURCE_EMBLEM) {
				if emblemInfo := goxml.GetData().OldEmblemInfoM.Index(res.Value); emblemInfo != nil {
					logEmblems = append(logEmblems, *res.Clone())
					isChange = true
					mail.Awards = append(mail.Awards[:i], mail.Awards[i+1:]...)
					i--
				}
			}
		}

		if !isChange {
			continue
		}

		if len(mail.Awards) == 0 {
			mailDeletes = append(mailDeletes, mail.Id)
		} else {
			changes = append(changes, mail)
		}
	}

	if len(logEmblems) > 0 {
		l4g.Infof("user: %s from mail to delete emblem num: %d, res: %+v", uid, len(logEmblems), logEmblems)
	}

	calls := 0
	if len(changes) > 0 {
		r.RopCl.SetSomeMailMCallSKs(uid, changes)
		calls++
	}
	if len(mailDeletes) > 0 {
		r.RopCl.RemSomeMailMCallSKs(uid, mailDeletes)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s", uid, reply.Err)
			return false
		}
	}

	return true
}

func fixGoddessResource(uid string, r *redis.Redis) {
	u := base.LoadUser(uid, r)

	dungeonID := u.Dungeon().GetDungeonID()

	var goddessResource []*cl.Resource

	dungeonTaskInfo := goxml.GetData().TaskInfoM.IndexEvent(2801000)
	for _, Info := range dungeonTaskInfo {
		if Info == nil {
			continue
		}
		if dungeonID < uint32(Info.Progress) {
			continue
		}
		if u.Achieve().IsAwarded(Info.Id) {
			for _, award := range Info.Awards {
				if award.Value == 81002 || award.Value == 81001 {
					goddessResource = append(goddessResource, award.Clone())
				}
			}
		}
	}

	for dungeonID != 0 {
		dungeonConfigInfo := goxml.GetData().DungeonConfigInfoM.Index(dungeonID)
		if dungeonConfigInfo == nil {
			l4g.Errorf("user:%s get dungeon:%d ConfigInfo failed", uid, dungeonID)
			return
		}
		firstDropInfo := goxml.GetData().DungeonFirstDropInfoM.Index(dungeonConfigInfo.FirstDrop)
		if firstDropInfo == nil {
			l4g.Errorf("user:%s get dungeon:%d DropInfo failed", uid, dungeonConfigInfo.FirstDrop)
			return
		}
		for _, award := range firstDropInfo.Awards {
			if award.Value == 81002 || award.Value == 81001 {
				goddessResource = append(goddessResource, award.Clone())
			}
		}
		dungeonID = dungeonConfigInfo.Dungeon
	}

	if len(goddessResource) > 0 {
		if !utils.Award(uid, r, goddessResource) {
			l4g.Errorf("user: %s revive goddess Resource failed. ", uid)
			fixGoddessResourceFailedCount++
		} else {
			l4g.Infof("user: %s revive goddess Resource %+v", uid, goddessResource)
			fixGoddessResourceCount++
		}
	}
}

func getGlobalRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllGlobalRankMCall()
	if data, err := redisClient.RopDB.GetSomeGlobalRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get global rank data error: %s", err.Error())
		fixGlobalRankFailedCount++
		return
	} else {
		globalRank = data
	}
}

func saveGlobalRank(redisClient *redis.Redis) {
	globalRanks := make([]*db.GlobalRank, 0, len(globalRank))
	for _, rank := range globalRank {
		globalRanks = append(globalRanks, rank)
	}
	redisClient.RopDB.SetSomeGlobalRankMCall(globalRanks)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save global rank data error: %s", reply.Err)
		fixGlobalRankFailedCount++
		return
	}
}

func getPowerRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllCommonRankMCallSK(uint64(goxml.PowerRankId))
	if data, err := redisClient.RopDB.GetSomeCommonRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get common power rank data error: %s", err.Error())
		fixPowerRankFailedCount++
		return
	} else {
		commonPowerRank = data
	}
}

func savePowerRank(redisClient *redis.Redis) {
	commonPowerRanks := make([]*db.CommonRank, 0, len(commonPowerRank))
	for _, rank := range commonPowerRank {
		commonPowerRanks = append(commonPowerRanks, rank)
	}
	redisClient.RopDB.SetSomeCommonRankMCallSK(uint64(goxml.PowerRankId), commonPowerRanks)

	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save common power rank data error: %s", reply.Err)
		fixPowerRankFailedCount++
		return
	}
}

func fixGoddessContract(uid string, r *redis.Redis) {
	/*// user
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		fixGoddessContractFailedCount++
		l4g.Error("user %s data corrupt", uid)
		return
	}

	userData.ModuleGlobalAttr.GoddessContractInfo = nil

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGoddessContractFailedCount++
		l4g.Errorf("user %s save goddessContract error: %s %s", uid, userData, reply.Err)
		return
	}
	fixGoddessContractCount++*/
}
