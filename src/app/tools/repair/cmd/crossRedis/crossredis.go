package crossRedis

import (
	cRedisOp "app/cross/db/redisop"
	"app/goxml"

	l4g "github.com/ivanabc/log4go"
	cRedis "gitlab.qdream.com/kit/redis"
)

type CrossRedis struct {
	Client *cRedis.Client
	RopCr  *cRedisOp.CrClient
}

func OpenCrossRedis(redisAddr string, redisIndex uint32) *CrossRedis {
	goxml.GetData().ServerInfoM.Load("../data", false)
	client, _ := cRedis.Dial("tcp", redisAddr, goxml.GetData().ServerInfoM.RedisPassword, redisIndex, false)
	return &CrossRedis{
		Client: client,
		RopCr:  &cRedisOp.CrClient{Client: client},
	}
}

func GetServerPartition(activityId uint64, sid uint64, crossMasterRedisClient *CrossRedis) uint32 {
	actPartition, err := crossMasterRedisClient.RopCr.GetActPartitionSK(activityId, sid)
	if err != nil {
		l4g.Errorf("GetServerPartition error:%s activityId:%d sid:%d", err, activityId, sid)
		return 0
	}
	if actPartition == nil {
		return 0
	}
	return actPartition.NowPart
}
