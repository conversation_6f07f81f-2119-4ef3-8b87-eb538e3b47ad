package resetGst

import (
	"app/goxml"
	ldb "app/logic/db"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath = flag.String("gsttDconfigData", "../data/", "service data path")

	fixGstDataFailed uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, redisActor *ldb.RedisActor) {
	goxml.Load(*flagDataPath, false, false)

	delGstReset(redisClient)

	l4g.Info("\n")

	if fixGstDataFailed == 0 {
		fmt.Printf("[SUCCESS] fixGstDataFailed Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGstDataFailed Failed, need check error log! failedCount:%d\n", fixGstDataFailed)
	}

	l4g.Info("finish repair")
}

func delGstReset(r *redis.Redis) {
	err := r.RopDB.DelGstLastAward()
	if err != nil {
		l4g.Errorf("DelGstLastAward error:%s", err)
		fixGstDataFailed++
		return
	}

	err = r.RopDB.DelGstLastFirstCenterAward()
	if err != nil {
		l4g.Errorf("DelGstLastFirstCenterAward error:%s", err)
		fixGstDataFailed++
		return
	}

	err = r.RopDB.DelGstDragonLastAward()
	if err != nil {
		l4g.Errorf("DelGstDragonLastAward error:%s", err)
		fixGstDataFailed++
		return
	}

	err = r.RopDB.DelGstMobLastAward()
	if err != nil {
		l4g.Errorf("DelGstMobLastAward error:%s", err)
		fixGstDataFailed++
		return
	}

	err = r.RopDB.DelAllGstLogicUser()
	if err != nil {
		l4g.Errorf("DelAllGstLogicUser error:%s", err)
		fixGstDataFailed++
		return
	}

	err = r.RopDB.DelGstChallengeLastAward()
	if err != nil {
		l4g.Errorf("DelGstChallengeLastAward error:%s", err)
		fixGstDataFailed++
		return
	}
}
