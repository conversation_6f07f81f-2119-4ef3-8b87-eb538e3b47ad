package v210

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath                                     = flag.String("V210configData", "../data/", "service data path")
	fixMedalFailedCount                              uint32
	fixStoryReviewCurrentActivityProgressFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	l4g.Info("\n")

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixMedal(uid, redisClient)
		fixStoryReviewCurrentActivityProgress(uid, redisClient, idFactory)
	})

	if fixMedalFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMedal Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMedal Failed, need check error log! failedCount:%d\n", fixMedalFailedCount)
	}

	if fixStoryReviewCurrentActivityProgressFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixStoryReviewCurrentActivityProgress Success!\n")
	} else {
		fmt.Printf("[FAILED] fixStoryReviewCurrentActivityProgress Failed, need check error log! failedCount:%d\n", fixStoryReviewCurrentActivityProgressFailedCount)
	}

	l4g.Info("finish repair")
}

func fixMedal(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module1 == nil || userData.Module1.Medal == nil {
		return
	}

	if userData.Bag == nil {
		userData.Bag = &db.Bags{}
	}
	if userData.Bag.Items == nil {
		userData.Bag.Items = make(map[uint32]uint32)
	}

	medal := userData.Module1.Medal
	if medal.Level == 27 {
		userData.Bag.Items[810027] += 1
	} else if medal.Level == 28 {
		userData.Bag.Items[810027] += 2
	} else if medal.Level >= 29 {
		userData.Bag.Items[810027] += 3
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixMedalFailedCount++
		l4g.Errorf("user %s save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save fixMedal success", uid)
}

func fixStoryReviewCurrentActivityProgress(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module5 == nil {
		userData.Module5 = &db.ModuleAttr5{}
	}

	// 修改数据，由于先停服刷脚本再起服务器，所以storyReview一定为空
	storyReview := &cl.StoryReviewData{
		SubplotProgress: make(map[uint32]uint32),
	}

	// 修复旧活动:s0赛季主线和潮汐之歌、圣诞活动主线进度
	userCreateTime := userData.Base.CreateTime // 获取玩家的创角时间
	fixOldProgress(storyReview, 2001, userCreateTime)
	fixOldProgress(storyReview, 2002, userCreateTime)
	fixOldProgress(storyReview, 3001, userCreateTime)
	// 修复当前活动:s1赛季主线进度
	fixCurProgress(storyReview, userData, 3002)

	// 修改数据
	if len(storyReview.SubplotProgress) == 0 {
		return
	}
	userData.Module5.StoryReview = storyReview
	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixStoryReviewCurrentActivityProgressFailedCount++
		l4g.Errorf("user %s fixStoryReviewCurrentActivityProgress error: %s %s \n", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s fixStoryReviewCurrentActivityProgress success", uid)
}

func fixOldProgress(storyReview *cl.StoryReviewData, storyId uint32, userCreateTime int64) {
	info := goxml.GetData().StoryReviewGroupInfoM.GetRecordById(storyId)
	if info == nil {
		l4g.Errorf("fixOldProgress: invalid storyId %d \n", storyId)
		return
	}
	if userCreateTime >= info.EndDay.Unix() {
		return
	}
	storyReview.SubplotProgress[info.Id] = info.ConditionCount
}

func fixCurProgress(storyReview *cl.StoryReviewData, userData *db.User, storyId uint32) {
	info := goxml.GetData().StoryReviewGroupInfoM.GetRecordById(storyId)
	if info == nil {
		l4g.Errorf("fixCurProgress: invalid storyId %d \n", storyId)
		return
	}

	seasonId := userData.Base.SeasonId
	if seasonId != info.ConditionValue {
		return
	}
	seasonDungeon := userData.Module5.SeasonDungeon
	if seasonDungeon == nil {
		return
	}
	if seasonDungeon.DungeonId == 0 {
		return
	}
	storyReview.SubplotProgress[info.Id] = seasonDungeon.DungeonId
}
