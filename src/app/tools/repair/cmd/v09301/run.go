package v093

import (
	"app/goxml"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath             = flag.String("V09301configData", "../data/", "service data path")
	fixMirageCount           uint32
	fixMirageFailedCount     uint32
	fixMirageRankCount       uint32
	fixMirageRankFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixMirage(uid, redisClient)
	})
	fixMirageRank(redisClient)

	l4g.Info("\n")

	if fixMirageFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixMirage Success!")
	} else {
		l4g.Infof("[FAILED] fixMirage Failed, need check error log! failedCount:%d", fixMirageFailedCount)
	}

	if fixMirageRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixMirageRank Success!")
	} else {
		l4g.Infof("[FAILED] fixMirageRank Failed, need check error log! failedCount:%d", fixMirageRankFailedCount)
	}

	l4g.Info("finish repair")
}

func fixMirage(uid string, redisClient *redis.Redis) {
	// 修改成就奖励领取状态
	{
		redisClient.RopDB.GetUserMCallSKs(uid)
		userData, err := redisClient.RopDB.GetUserByReply(redisClient.Client.GetReply())
		if err != nil {
			l4g.Error("get user error: %s %s", uid, err)
			//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
		}

		if userData == nil {
			fixMirageFailedCount++
			l4g.Error("user %s data corrupt", uid)
			return
		}

		if userData.Module1 == nil || userData.Module1.RankAchieveAwards == nil {
			return
		}

		rankIDs := make(map[uint32]struct{})
		for _, rankID := range goxml.MirageCopyID2RankId {
			rankIDs[rankID] = struct{}{}
		}

		isChange := false
		achieveAwards := userData.Module1.RankAchieveAwards
		for i := 0; i < len(achieveAwards.Id); i++ {
			goalID := achieveAwards.Id[i]
			info := goxml.GetData().RankingAchievementInfoM.Index(goalID)
			if info == nil {
				l4g.Errorf("RankingAchievementInfo not exist: %s %d", uid, goalID)
				continue
			}
			if _, exist := rankIDs[info.RankId]; exist {
				achieveAwards.Id = append(achieveAwards.Id[:i], achieveAwards.Id[i+1:]...)
				i--
				isChange = true
			}
		}

		if isChange {
			redisClient.RopDB.SetUserMCallSKs(uid, userData)
			if reply := redisClient.Client.GetReply(); reply.Err != nil {
				fixMirageFailedCount++
				l4g.Errorf("user %s save user error: %s %s", uid, userData, reply.Err)
				return
			}
		}
	}

	redisClient.RopCl.GetAllMirageMCallSKs(uid)
	mirageDatas, err := redisClient.RopCl.GetSomeMirageByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("get mirage data error: %s %s", uid, err)
		fixMirageFailedCount++
	}
	if len(mirageDatas) == 0 {
		return
	}

	deletes := make([]uint32, 0, len(mirageDatas))
	for hurdleID := range mirageDatas {
		deletes = append(deletes, hurdleID)
	}

	redisClient.RopCl.RemSomeMirageMCallSKs(uid, deletes)

	/************************REPLY*******************************/
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save delete mirage data error: %+v", uid, reply.Err)
		fixMirageFailedCount++
		return
	}

	fixMirageCount++
}

func fixMirageRank(redisClient *redis.Redis) {
	calls := 0
	achieveRankIDs := make([]uint32, 0, len(goxml.MirageCopyID2RankId))
	// 删除排行榜数据
	for _, rankID := range goxml.MirageCopyID2RankId {
		redisClient.RopDB.DelAllCommonRankMCallSK(uint64(rankID))
		calls++
		achieveRankIDs = append(achieveRankIDs, rankID)
	}
	// 删除成就数据
	redisClient.RopDB.RemSomeRankAchievesMCall(achieveRankIDs)
	calls++

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save delete mirage rank data error: %s", reply.Err)
			fixMirageRankFailedCount++
			return
		}
	}

	fixMirageRankCount++
}
