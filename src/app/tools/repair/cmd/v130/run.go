package v130

import (
	"app/goxml"
	"app/protos/in/db"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath = flag.String("V130configData", "../data/", "service data path")

	fixMedalFailedCount           uint32
	delFlowerSnatchLogFailedCount uint32 //删除抢花日志
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixMedal(uid, redisClient)
		delFlowerSnatchLog(uid, redisClient) //删除抢花日志
	})
	l4g.Info("\n")

	if fixMedalFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMedal Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMedal Failed, need check error log! failedCount:%d\n", fixMedalFailedCount)
	}

	if delFlowerSnatchLogFailedCount == 0 {
		fmt.Printf("[SUCCESS] delFlowerSnatchLog Success!\n")
	} else {
		fmt.Printf("[FAILED] delFlowerSnatchLog Failed, need check error log! failedCount:%d\n", delFlowerSnatchLogFailedCount)
	}

	l4g.Info("finish repair")
}

func fixMedal(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module1 == nil || userData.Module1.Medal == nil {
		return
	}

	if userData.Bag == nil {
		userData.Bag = &db.Bags{}
	}
	if userData.Bag.Items == nil {
		userData.Bag.Items = make(map[uint32]uint32)
	}

	medal := userData.Module1.Medal
	if medal.Level == 24 {
		userData.Bag.Items[810026] += 1
	} else if medal.Level == 25 {
		userData.Bag.Items[810026] += 2
	} else if medal.Level >= 26 {
		userData.Bag.Items[810026] += 3
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixMedalFailedCount++
		l4g.Errorf("user %s save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save fixMedal success", uid)
}

// 删除抢花日志
func delFlowerSnatchLog(uidStr string, r *redis.Redis) {
	r.RopCl.DelAllFlowerSnatchLogMCallSKs(uidStr)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delete flower snatch log error: %s", reply.Err)
		delFlowerSnatchLogFailedCount++
		return
	}
	l4g.Info("[delFlowerSnatchLog] user %s delete flower snatch log success", uidStr)
}
