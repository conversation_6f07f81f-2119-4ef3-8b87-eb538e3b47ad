package vTiYan

import (
	"app/goxml"
	"app/protos/in/db"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath = flag.String("VtiYanConfigData", "../data/", "service data path")

	fixMazeMapFailedCount   uint32
	fixWishListFailedCount  uint32
	fixGuildUserFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixWishList(uid, redisClient)
	})
	fixMazeMap(redisClient) // 删除迷宫模板

	redis.IterateAllGuildUsers(redisClient, func(guildUserData *db.GuildUser) {
		fixGuildUser(guildUserData, redisClient)
	})

	fixWrestleReset(redisClient)

	if fixWishListFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixWishList Success!\n")
	} else {
		fmt.Printf("[FAILED] fixWishList Failed, need check error log! failedCount:%d\n", fixWishListFailedCount)
	}

	if fixGuildUserFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildUser Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildUser Failed, need check error log! failedCount:%d\n", fixGuildUserFailedCount)
	}

	if fixMazeMapFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMazeMap Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMazeMap Failed, need check error log! failedCount:%d\n", fixMazeMapFailedCount)
	}

	l4g.Info("finish repair")
}

func fixWishList(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		fixWishListFailedCount++
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module == nil || userData.Module.Summon == nil {
		return
	}

	advancedData := userData.Module.Summon[goxml.AdvancedSummon]

	if advancedData == nil {
		return
	}

	if advancedData.WishListMsg == nil {
		return
	}

	if advancedData.SummonCount < 100 {
		return
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixWishListFailedCount++
		l4g.Errorf("[fixWishList] user %s save  error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save wishList success", uid)
}

func fixGuildUser(guildUser *db.GuildUser, r *redis.Redis) {
	if guildUser == nil {
		return
	}

	guildUser.UserChapter = 0
	guildUser.DailyDungeonDamage = 0
	guildUser.TotalFightCount = 0
	guildUser.AwardBox = nil
	guildUser.HistoryMaxDungeonDamage = 0
	guildUser.BuyCount = 0
	guildUser.ChallengeTimes = 0
	guildUser.LastRecoverTm = 0
	guildUser.RecvChapterTask = nil
	guildUser.RecvChapterBoss = nil
	guildUser.BossAwardReceivedMaxChapter = 0
	guildUser.DropAwardIndex = 0
	guildUser.TodayFightBestDamage = nil
	guildUser.SeasonFightBestDamage = nil
	guildUser.SeasonMaxDungeonDamage = 0
	guildUser.MaxDamagePower = 0

	r.RopDB.SetSomeGuildUserMCall([]*db.GuildUser{guildUser})
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGuildUserFailedCount++
		l4g.Errorf("[GuildUser] guildUser %d save  error: %s", guildUser.Id, reply.Err)
	}

	l4g.Infof("[GuildUser] id:%d ", guildUser.Id)
}

func fixWrestleReset(redisClient *redis.Redis) {

	err := redisClient.RopDB.DelAllWrestleUser()

	if err != nil {
		l4g.Errorf("fix fixWrestleReset data error: %s", err)
		return
	}

	err = redisClient.RopDB.DelWrestle()
	if err != nil {
		l4g.Errorf("fix fixWrestleReset data error: %s", err)
		return
	}

	l4g.Info("[fixWrestleReset] success")
}

func fixMazeMap(r *redis.Redis) {
	r.RopCl.GetAllMazeMapMCall()
	data, err := r.RopCl.GetSomeMazeMapByReply(r.Client.GetReply())
	if err != nil {
		fixMazeMapFailedCount++
		l4g.Errorf("get maze basic map data error: %+v", err)
	}

	deletes := make([]uint32, 0, len(data))
	for mapID := range data {
		deletes = append(deletes, mapID)
	}

	if len(deletes) > 0 {
		r.RopCl.RemSomeMazeMapMCall(deletes)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixMazeMapFailedCount++
			l4g.Errorf("delete mazeMap error: %s", reply.Err)
			return
		}
	}
}
