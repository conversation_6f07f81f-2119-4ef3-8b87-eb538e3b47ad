package v2400

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/redis"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	fixComplianceTaskFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, serverType string, serverDay uint32) {
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixComplianceTask(uid, redisClient, serverDay)
	})

	if fixComplianceTaskFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixComplianceTask Success!")
	} else {
		l4g.Infof("[FAILED] fixComplianceTask Failed, need check error log! failedCount:%d", fixComplianceTaskFailedCount)
	}
	l4g.Infof("finish repair")
}

type progressFunc func(user *db.User, r *redis.Redis, uid string) bool

var buildProgress = map[uint32]progressFunc{
	uint32(common.FUNCID_MODULE_ACTIVITY_TOWER): func(user *db.User, r *redis.Redis, uid string) bool {
		if user.Module == nil || user.Module.Towers == nil || user.Module.Towers[1] == nil {
			return false
		}
		towerData := user.Module.Towers[1]
		user.Module8.ComplianceTasks.Tasks[uint32(common.FUNCID_MODULE_ACTIVITY_TOWER)].Progress[4400000] = &cl.TaskTypeProgress{
			TaskTypeId: 4400000,
			Progress:   uint64(towerData.Floor),
		}
		return true
	},
	uint32(common.FUNCID_MODULE_ACTIVITY_MIRAGE): func(user *db.User, r *redis.Redis, uid string) bool {
		r.RopCl.GetAllMirageMCallSKs(uid)
		mirageDatas, err := r.RopCl.GetSomeMirageByReply(r.Client.GetReply())
		if err != nil {
			l4g.Error("[FAILED] fixComplianceTask get user mirage error: %s %s", uid, err)
			fixComplianceTaskFailedCount++
			return false
		}
		var totalFloor uint64
		typeTopFloor := make(map[uint32]uint32)
		for _, mirageData := range mirageDatas {
			if mirageData == nil {
				continue
			}
			hurdleInfo := goxml.GetData().MirageHurdleInfoM.Index(mirageData.SysId)
			if hurdleInfo == nil {
				continue
			}
			if hurdleInfo.Floor > typeTopFloor[hurdleInfo.Id] {
				typeTopFloor[hurdleInfo.Id] = hurdleInfo.Floor
			}
		}
		for _, topFloor := range typeTopFloor {
			totalFloor += uint64(topFloor)
		}
		user.Module8.ComplianceTasks.Tasks[uint32(common.FUNCID_MODULE_ACTIVITY_MIRAGE)].Progress[4801010] = &cl.TaskTypeProgress{
			TaskTypeId: 4801010,
			Progress:   totalFloor,
		}
		return true
	},
}

func fixComplianceTask(uid string, r *redis.Redis, serverDay uint32) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] fixComplianceTask get user error: %s %s", uid, err)
		return
	}

	openModule := make(map[uint32]struct{})
	activityRankInfo := goxml.GetData().RankingInfoM.Index(goxml.ActivityMirageRankID)
	if activityRankInfo != nil && userData.Base.Level >= activityRankInfo.Level && goxml.GetData().ConfigInfoM.ActivityMirageOpen(serverDay, userData.Base.Level) {
		openModule[uint32(common.FUNCID_MODULE_ACTIVITY_MIRAGE)] = struct{}{}
	}

	activityRankInfo = goxml.GetData().RankingInfoM.Index(goxml.ActivityTowerRankID)
	if activityRankInfo != nil && userData.Base.Level >= activityRankInfo.Level && goxml.GetData().ConfigInfoM.ActivityTowerOpen(serverDay, userData.Base.Level) {
		openModule[uint32(common.FUNCID_MODULE_ACTIVITY_TOWER)] = struct{}{}
	}

	var change bool
	for moduleID := range openModule {
		if moduleID == 0 {
			continue
		}

		if userData.Module8 == nil {
			userData.Module8 = &db.ModuleAttr8{}
		}

		if userData.Module8.ComplianceTasks == nil {
			userData.Module8.ComplianceTasks = &cl.ComplianceTasks{}
		}

		if userData.Module8.ComplianceTasks.Tasks == nil {
			userData.Module8.ComplianceTasks.Tasks = make(map[uint32]*cl.ComplianceTask)
		}

		userData.Module8.ComplianceTasks.Tasks[moduleID] = &cl.ComplianceTask{
			Progress: make(map[uint32]*cl.TaskTypeProgress),
			Awarded:  make(map[uint32]bool),
		}

		if buildProgress[moduleID](userData, r, uid) {
			change = true
		}
	}

	if change {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save fixComplianceTask error: %s %s", uid, userData, reply.Err)
			fixComplianceTaskFailedCount++
		}
	}
}
