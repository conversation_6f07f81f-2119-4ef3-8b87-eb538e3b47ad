package v01102

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/base"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"time"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath          = flag.String("V01102configData", "../data/", "service data path")
	fixGoddessFailedCount uint32

	fixPowerFailedCount      uint32
	fixPowerRankFailedCount  uint32
	fixGlobalRankFailedCount uint32
	globalRank               map[uint64]*db.GlobalRank
	commonPowerRank          map[uint64]*db.CommonRank
)

var curId = map[uint32]struct{}{
	2010: {},
	2011: {},
	3008: {},
	3009: {},
	5009: {},
	5010: {},
}

var goddessRepair = map[uint32]map[uint32]uint32{}

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	goddessRepair = make(map[uint32]map[uint32]uint32)
	for i := uint32(1); i <= 3; i++ {
		goddessRepair[i] = make(map[uint32]uint32)
		switch i {
		case 3:
			goddessRepair[i][2004] = 2010
			goddessRepair[i][2005] = 2011
		case 1:
			goddessRepair[i][3004] = 3008
			goddessRepair[i][3005] = 3009
		case 2:
			goddessRepair[i][5004] = 5009
			goddessRepair[i][5005] = 5010
		}
	}
	getPowerRank(redisClient)
	getGlobalRank(redisClient)
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixGoddess(uid, redisClient, idFactory)
		fixPower(uid, redisClient)
	})
	l4g.Info("\n")
	savePowerRank(redisClient)
	saveGlobalRank(redisClient)

	if fixGoddessFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixGoddess Success!")
	} else {
		l4g.Infof("[FAILED] fixGoddess Failed, need check error log! failedCount:%d", fixGoddessFailedCount)
	}

	if fixPowerFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPower Success!")
	} else {
		l4g.Infof("[FAILED] fixPower Failed, need check error log! failedCount:%d", fixPowerFailedCount)
	}

	if fixPowerRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPowerRank Success!")
	} else {
		l4g.Infof("[FAILED] fixPowerRank Failed, need check error log! failedCount:%d", fixPowerRankFailedCount)
	}

	if fixGlobalRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixGlobalRank Success!")
	} else {
		l4g.Infof("[FAILED] fixGlobalRank Failed, need check error log! failedCount:%d", fixGlobalRankFailedCount)
	}

	l4g.Info("finish repair")
}

func fixGoddess(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Module3 == nil || userData.Module3.GoddessContractInfo == nil || userData.Module3.GoddessContractInfo.Goddess == nil {
		l4g.Errorf("user %s data GoddessContractInfo failed", uid)
		return
	}

	var expItemCount uint32
	goddess, exist := userData.Module3.GoddessContractInfo.Goddess[4]
	if exist {
		if goddess == nil {
			delete(userData.Module3.GoddessContractInfo.Goddess, 4)
		} else {
			expItemCount = goddess.Exp / 20
			surplus := goddess.Exp % 20
			if surplus > 0 {
				expItemCount++
			}
			userData.ModuleGlobalAttr.GoddessContractExp -= goddess.Exp
			if len(goddess.SuitIds) > 0 {
				for _, SuitId := range goddess.SuitIds {
					for i, globalSuitId := range userData.ModuleGlobalAttr.GoddessSuits {
						if SuitId == globalSuitId {
							userData.ModuleGlobalAttr.GoddessSuits = append(userData.ModuleGlobalAttr.GoddessSuits[:i], userData.ModuleGlobalAttr.GoddessSuits[i+1:]...)
						}
					}
				}
			}
			delete(userData.Module3.GoddessContractInfo.Goddess, 4)
		}
	}

	for id, v := range userData.Module3.GoddessContractInfo.Goddess {
		//删新的
		if v == nil {
			continue
		}
		for index, storyId := range v.GoddessStory {
			_, exist = curId[storyId]
			if exist {
				userData.ModuleGlobalAttr.GoddessSuits = append(userData.ModuleGlobalAttr.GoddessSuits[:index], userData.ModuleGlobalAttr.GoddessSuits[index+1:]...)
			}
		}
		//改老的
		storyM, exist := goddessRepair[id]
		if !exist {
			continue
		}
		for index, storyId := range v.GoddessStory {
			newStory, exist := storyM[storyId]
			if exist {
				v.GoddessStory[index] = newStory
			}
		}
	}

	if expItemCount > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Valkyrie Manor Adjustments")
		params = append(params, "We have replaced the hero Ellie with Iris in the Valkyrie Manor, players can now experience the new stories and outfits. Ellie's favor has been replaced with the corresponding Favor Gift Chests, please make sure to claim it.")
		award := &cl.Resource{
			Type:  uint32(common.RESOURCE_ITEM),
			Value: 10057,
			Count: expItemCount,
		}
		sendMail(uid, r, idFactory, character.MailIDGM, []*cl.Resource{award}, params)
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGoddessFailedCount++
		l4g.Errorf("user %s save goddess error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save goddess success", uid)
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}

	return true
}

func getGlobalRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllGlobalRankMCall()
	if data, err := redisClient.RopDB.GetSomeGlobalRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get global rank data error: %s", err.Error())
		fixGlobalRankFailedCount++
		return
	} else {
		globalRank = data
	}
}

func getPowerRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllCommonRankMCallSK(uint64(goxml.PowerRankId))
	if data, err := redisClient.RopDB.GetSomeCommonRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get common power rank data error: %s", err.Error())
		fixPowerRankFailedCount++
		return
	} else {
		commonPowerRank = data
	}
}

func saveGlobalRank(redisClient *redis.Redis) {
	globalRanks := make([]*db.GlobalRank, 0, len(globalRank))
	for _, rank := range globalRank {
		globalRanks = append(globalRanks, rank)
	}
	redisClient.RopDB.SetSomeGlobalRankMCall(globalRanks)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save global rank data error: %s", reply.Err)
		fixGlobalRankFailedCount++
		return
	}
}

func savePowerRank(redisClient *redis.Redis) {
	commonPowerRanks := make([]*db.CommonRank, 0, len(commonPowerRank))
	for _, rank := range commonPowerRank {
		commonPowerRanks = append(commonPowerRanks, rank)
	}
	redisClient.RopDB.SetSomeCommonRankMCallSK(uint64(goxml.PowerRankId), commonPowerRanks)

	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save common power rank data error: %s", reply.Err)
		fixPowerRankFailedCount++
		return
	}
}

func fixPower(uid string, redisClient *redis.Redis) {
	u := base.LoadUser(uid, redisClient)
	fixDefensePower(uid, u, redisClient)
	fixTop5AndCrystalPower(uid, u, redisClient)
}

func fixDefensePower(uid string, u *character.User, redisClient *redis.Redis) {
	heroM := u.HeroManager()
	formationM := u.FormationManager()
	arenaDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_ARENA_DEFENSE))
	if arenaDefenseFormation != nil {
		var power int64
		for _, teamInfo := range arenaDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetArenaPower(power)
		l4g.Infof("newUserData:%s arenaPower: %d", uid, power)
	}

	wrestleDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE))
	if wrestleDefenseFormation != nil {
		var power int64
		for _, teamInfo := range wrestleDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetDefensePower(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE), power)
		l4g.Infof("newUserData:%s wrestlePower: %d", uid, power)
	}

	flowerDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE))
	if flowerDefenseFormation != nil {
		var power int64
		for _, teamInfo := range flowerDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetDefensePower(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE), power)
		l4g.Infof("newUserData:%s flowerPower: %d", uid, power)
	}
	redisClient.RopDB.SetUserMCallSKs(uid, u.GetDBUser())
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixPowerFailedCount++
		l4g.Errorf("[fixDefensePower] user %s save user error: %s", uid, reply.Err)
	}

}

func fixTop5AndCrystalPower(uid string, u *character.User, redisClient *redis.Redis) {
	userData := u.GetDBUser()
	heroM := u.HeroManager()
	// heroM.ReCalTop5()
	heroHeap := heroM.GetTop5Heros()
	l4g.Infof("oldUserData:%s top5%v", uid, userData.Base.Top5Heros)
	userData.Base.Top5Heros = make([]uint64, character.Top5HeroNum)
	var top5Power uint64
	for index, hero := range heroHeap {
		userData.Base.Top5Heros[index] = hero.GetData().Id
		top5Power += hero.GetHeroPower(u)
	}
	userData.Base.Power = int64(top5Power)
	userData.Base.PowerTm = time.Now().Unix()
	userData.Base.MaxPower = int64(top5Power)

	crystal := u.Crystal()
	if crystal.IsCrystalOpened() {
		userData.Base.CrystalPower = int64(crystal.CalCrystalPower())
	}

	//更新通用战力榜战力
	if userRank, exist := commonPowerRank[userData.Id]; exist {
		userPower := &rank.UserPower{}
		userPower.Load(userRank.Data)
		if crystal.IsCrystalOpened() {
			userPower.Power = uint64(userData.Base.CrystalPower)
		} else {
			userPower.Power = uint64(userData.Base.Power)
		}

		userPower.Tm = userData.Base.PowerTm
		userRank.Data = userPower.Save()
		l4g.Infof("update common power rank: %d power:%d", userPower.Uid, userPower.Power)
	}
	//更新全局战力
	if userRank, exist := globalRank[userData.Id]; exist {
		userRank.Power = userData.Base.Power
		userRank.PowerTm = userData.Base.PowerTm
		l4g.Infof("update global rank: %d power:%d", userRank.Id, userRank.Power)
	}

	redisClient.RopDB.SetUserMCallSKs(uid, userData)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixPowerFailedCount++
		l4g.Errorf("[top5] user %s save user error: %s", uid, reply.Err)
	}
	l4g.Infof("newUserData:%s top5%v top5Power:%d crystalPower:%d",
		uid, userData.Base.Top5Heros, userData.Base.Power, userData.Base.CrystalPower)
}
