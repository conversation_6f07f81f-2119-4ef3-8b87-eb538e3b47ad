/*
Copyright © 2019 NAME HERE <EMAIL ADDRESS>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"app/goxml"
	"app/logic/db"
	"app/protos/in/config"
	appsrv "app/service"
	"fmt"
	"os"
	"strings"

	l4g "github.com/ivanabc/log4go"
	snet "gitlab.qdream.com/kit/sea/net"

	"gitlab.qdream.com/kit/sea/id"

	"app/tools/repair/cmd/redis"

	"app/tools/repair/cmd/idfactory"

	homedir "github.com/mitchellh/go-homedir"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var cfgFile string

// var redisAddr string
// var redisIndex int
var xmlDataPath string
var redisClient *redis.Redis
var serviceConfig string
var debugAddr string
var cfgFromEtcd *config.Logic
var idFactory *id.Factory
var needCheckVersion bool
var version string
var redisActor *db.RedisActor
var crossRedisAddr string
var crossRedisIndex string
var crossMasterRedisAddr string
var crossMasterRedisIndex string

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "repair",
	Short: "修复玩家数据",
	// Uncomment the following line if your bare application
	// has an action associated with it:
	//	Run: func(cmd *cobra.Command, args []string) { },
	PersistentPostRun: recordVersion,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {

	rootCmd.PersistentFlags().StringVarP(&xmlDataPath, "configData", "d", "../data", "xml data path")
	cobra.OnInitialize(initConfig)
	cobra.OnInitialize(initEtcdConfig)
	cobra.OnInitialize(initXml)
	cobra.OnInitialize(initRedis)
	cobra.OnInitialize(initIDFactory)
	cobra.OnInitialize(initVersion)
	cobra.OnInitialize(checkVersion)

	// Here you will define your flags and configuration settings.
	// Cobra supports persistent flags, which, if defined here,
	// will be global for your application.

	//rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is $HOME/.repair.yaml)")
	//rootCmd.PersistentFlags().StringVarP(&redisAddr, "redis", "r", "0.0.0.0:9898", "redis server address")
	//rootCmd.PersistentFlags().IntVarP(&redisIndex, "num", "n", 0, "redis database number")
	rootCmd.PersistentFlags().StringVarP(&serviceConfig, "config", "c", "../config/logic.xml", "service config path")
	rootCmd.PersistentFlags().StringVarP(&debugAddr, "http_addr", "a", ":10010", "HTTP server listen address")
	rootCmd.PersistentFlags().BoolVarP(&needCheckVersion, "needCheckVersion", "k", true, "是否需要检查版本号")
	rootCmd.PersistentFlags().StringVarP(&crossRedisAddr, "crossRedisAddr", "r", "127.0.0.1:9898", "跨服的redis地址")
	rootCmd.PersistentFlags().StringVarP(&crossRedisIndex, "crossRedisIndex", "i", "0", "跨服的redis下标")
	rootCmd.PersistentFlags().StringVarP(&crossMasterRedisAddr, "crossMasterRedisAddr", "p", "127.0.0.1:9898", "跨服Master的redis地址")
	rootCmd.PersistentFlags().StringVarP(&crossMasterRedisIndex, "crossMasterRedisIndex", "q", "0", "跨服Master的redis下标")
	// Cobra also supports local flags, which will only run
	// when this action is called directly.
	//rootCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}

// initConfig reads in config file and ENV variables if set.
func initConfig() {
	if cfgFile != "" {
		// Use config file from the flag.
		viper.SetConfigFile(cfgFile)
	} else {
		// Find home directory.
		home, err := homedir.Dir()
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}

		// Search config in home directory with name ".repair" (without extension).
		viper.AddConfigPath(home)
		viper.SetConfigName(".mirror")
	}

	viper.AutomaticEnv() // read in environment variables that match

	// If a config file is found, read it in.
	if err := viper.ReadInConfig(); err == nil {
		fmt.Println("Using config file:", viper.ConfigFileUsed())
	}
}

func initXml() {
	goxml.Load(xmlDataPath, false, false)
}

func initRedis() {
	redisClient = redis.OpenRedis(cfgFromEtcd.Redis, cfgFromEtcd.RedisIndex)
	if redisClient == nil {
		println("open redis failed", cfgFromEtcd.Redis, cfgFromEtcd.RedisIndex)
	}
	redisActor = &db.RedisActor{
		Client: redisClient.Client,
		RopDb:  redisClient.RopDB,
		RopCl:  redisClient.RopCl,
	}
}

func initIDFactory() {
	idFactory = idfactory.OpenIDFactory(cfgFromEtcd, redisClient)
	if idFactory == nil {
		println("open idFactory failed", serviceConfig, debugAddr)
	}
}

func initEtcdConfig() {
	logicCfg := new(goxml.LogicServiceConfig)
	logicCfg.Load(serviceConfig, false)
	serviceID := getServiceID(debugAddr)
	etcdCfg, err := getConfigFromEtcd(logicCfg.Etcd, serviceID)
	if err != nil {
		l4g.Errorf("get config from etcd failed: %s", err)
		return
	}

	l4g.Infof("etcd config: %+v", etcdCfg)
	cfgFromEtcd = etcdCfg
}

func getServiceID(debugAddr string) string {
	port := strings.Split(debugAddr, ":")[1]
	var prefix string
	if *appsrv.IPV4 {
		ips, _ := snet.GetHostIPv4()
		prefix = ips[0]
	} else {
		prefix, _ = os.Hostname()
	}
	serviceID := prefix + ":" + port

	return serviceID
}

func initVersion() {
	if len(os.Args[1]) > 1 {
		version = os.Args[1]
	}
}

func checkVersion() {
	if !needCheckVersion {
		return
	}
	redisClient.RopDB.GetRepairRecordMCall()
	versions, err := redisClient.RopDB.GetRepairRecordByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("get RepairRecord error: %s", err)
		return
	}

	for _, v := range versions {
		if v == version {
			fmt.Println()
			fmt.Println("[ERROR] version:", version, " has been Executed")
			os.Exit(1)
		}
	}
}

func recordVersion(cmd *cobra.Command, args []string) {
	redisClient.RopDB.AddSomeRepairRecordMCall([]string{version})
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("recordVersion failed %v, version: %s", reply.Err, version)
		return
	}
}
