package v160_towerSeason

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"encoding/json"
	"flag"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
	"gitlab.qdream.com/kit/sea/time"
	"os"
	"strconv"
)

var (
	flagDataPath                    = flag.String("V160configData", "../data/", "service data path")
	towerSeasonRankFile             = flag.String("towerSeasonRankFilePath", "./towerRankData", "rank file path")
	awardTowerSeasonFailed          uint32
	awardTowerSeasonUserFailed      uint32
	awardTowerSeasonAwardTimeFailed uint32
	awardTowerSeasonTaskAwardFailed uint32
	userTowerSeasonDataFailed       uint32
)

type userTowerSeasonRank struct {
	Rank  uint32 `json:"Rank"`
	Uid   uint64 `json:"Uid"`
	Sid   uint64 `json:"Sid"`
	Name  string `json:"Name"`
	Floor uint64 `json:"Floor"`
	Tm    uint64 `json:"Tm"`
	Part  uint32 `json:"Part"`
}

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	rankData := AwardTowerSeasonRank()
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		uid64, err := strconv.ParseUint(uid, 10, 64)
		if err != nil {
			l4g.Errorf("uid:%s atoi failed", uid)
			return
		}
		rankInfo, exist := rankData[uid64]
		if exist {
			AwardTowerSeasonUser(uid, redisClient, rankInfo, idFactory)
		}
		TowerSeasonTaskReward(uid, redisClient, idFactory)
	})

	if awardTowerSeasonFailed == 0 && awardTowerSeasonUserFailed == 0 {
		DelTowerSeasonAwardTime(redisClient)
	}

	if awardTowerSeasonFailed == 0 {
		l4g.Infof("[SUCCESS] awardTowerSeason Success!")
	} else {
		l4g.Infof("[FAILED] awardTowerSeasonFailed Failed, need check error log! failedCount:%d", awardTowerSeasonFailed)
	}

	if awardTowerSeasonUserFailed == 0 {
		l4g.Infof("[SUCCESS] awardTowerSeasonUser Success!")
	} else {
		l4g.Infof("[FAILED] awardTowerSeasonUserFailed Failed, need check error log! failedCount:%d", awardTowerSeasonUserFailed)
	}

	if awardTowerSeasonAwardTimeFailed == 0 {
		l4g.Infof("[SUCCESS] awardTowerSeasonAwardTime Success!")
	} else {
		l4g.Infof("[FAILED] awardTowerSeasonAwardTime Failed, need check error log! failedCount:%d", awardTowerSeasonAwardTimeFailed)
	}

	if awardTowerSeasonTaskAwardFailed == 0 {
		l4g.Infof("[SUCCESS] awardTowerSeasonTaskAward Success!")
	} else {
		l4g.Infof("[FAILED] awardTowerSeasonTaskAward Failed, need check error log! failedCount:%d", awardTowerSeasonTaskAwardFailed)
	}

	if userTowerSeasonDataFailed == 0 {
		l4g.Infof("[SUCCESS] userTowerSeasonData Success!")
	} else {
		l4g.Infof("[FAILED] userTowerSeasonData Failed, need check error log! failedCount:%d", userTowerSeasonDataFailed)
	}
}

func AwardTowerSeasonRank() map[uint64]*userTowerSeasonRank {
	userRankInfo := make(map[uint64]*userTowerSeasonRank)
	dataSlice := make([]*userTowerSeasonRank, 0, 600)
	contentByte, err := os.ReadFile(*towerSeasonRankFile)
	if err != nil {
		awardTowerSeasonFailed++
		panic(fmt.Sprintf("awardTowerSeasonFailed open file err:%s", err))
	}

	l4g.Infof("contentByte:%s", string(contentByte))
	err = json.Unmarshal(contentByte, &dataSlice)
	if err != nil {
		awardTowerSeasonFailed++
		panic(fmt.Sprintf("awardTowerSeasonFailed open file err:%s", err))
	}

	for _, rankData := range dataSlice {
		if rankData == nil {
			continue
		}
		_, exist := userRankInfo[rankData.Uid]
		if exist {
			panic(fmt.Sprintf("rank data Info user:%d is repeated", rankData.Uid))
		}
		userRankInfo[rankData.Uid] = rankData
	}
	l4g.Infof("userRankInfo len:%d", len(userRankInfo))

	return userRankInfo
}

func AwardTowerSeasonUser(uid string, r *redis.Redis, seasonRank *userTowerSeasonRank, idFactory *id.Factory) {
	if seasonRank == nil {
		return
	}
	info := goxml.GetData().TowerSeasonRankRewardInfoM.GetInfoByRank(uint32(seasonRank.Rank))
	if info == nil || len(info.ClRes) == 0 {
		l4g.Errorf("C2LRankGetForResetCommand: handleTowerSeasonReset rankInfo not exist. rank:%d", seasonRank.Rank)
		awardTowerSeasonUserFailed++
		return
	}

	if len(info.ClRes) > 0 {
		awards := updateAvatarExpireTime(info.ClRes)
		params := make([]string, 0, 2)
		params = append(params, "Expedition Zone Reward")
		params = append(params, fmt.Sprintf("You've challenged the Northern Expedition 3 and ranked no. <color=#c2510d>%d</color> in Zone Rankings. Here are your result rewards.", seasonRank.Rank))
		ret := sendMail(uid, r, idFactory, character.MailIDGM, awards, params)
		if !ret {
			awardTowerSeasonUserFailed++
			l4g.Errorf("user: %s AwardTowerSeasonUser: send mail failed.", uid)
		}
		l4g.Info("user %s save AwardTowerSeasonUser.sendMail. success", uid)
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}
	return true
}

func updateAvatarExpireTime(awards []*cl.Resource) []*cl.Resource {
	newAwards := make([]*cl.Resource, 0, len(awards))
	for _, award := range awards {
		newA := award.Clone()
		newAwards = append(newAwards, newA)
		if newA.Type != uint32(common.RESOURCE_AVATAR) {
			continue
		}
		info := goxml.GetData().AvatarInfoM.Index(newA.Value)
		if info != nil && info.DurationType == goxml.AvatarTimeTypeEndTime {
			endTm := time.Now().Unix() + int64(info.Duration*60)
			newA.Attrs = []*cl.Attr{
				{
					Value: endTm,
				},
			}
		}
	}
	return newAwards
}

func DelTowerSeasonAwardTime(r *redis.Redis) {
	err := r.RopDB.DelTowerSeasonReset()
	if err != nil {
		l4g.Errorf("DelTowerSeasonReset failed")
		awardTowerSeasonAwardTimeFailed++
	}
}

func TowerSeasonTaskReward(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	dbUser := userData
	if dbUser == nil || dbUser.Module4 == nil || dbUser.Module4.TowerSeason == nil || len(dbUser.Module4.TowerSeason.TaskProgress) == 0 {
		return
	}
	towerSeason := dbUser.Module4.TowerSeason

	seasonTasks := xmlcfg.V015TowerSeasonTaskInfo.SeasonTasks(towerSeason.Season)
	totalAward := make([]*cl.Resource, 0)
	if towerSeason.TaskProgress != nil {
		if towerSeason.TaskReceived == nil {
			towerSeason.TaskReceived = make(map[uint32]bool)
		}
		for _, taskInfo := range seasonTasks {
			if taskInfo == nil {
				continue
			}
			isRecv := towerSeason.TaskReceived[taskInfo.Id]
			if isRecv {
				continue
			}

			pro, exist := towerSeason.TaskProgress[5900000]
			if !exist {
				continue
			}

			if pro.Progress >= taskInfo.Progress {
				totalAward = append(totalAward, taskInfo.Awards...)
			}
		}
	}

	if len(totalAward) > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Northern Expedition Reward Reissue")
		params = append(params, fmt.Sprintf("Northern Expedition's last month season has ended. Here are your unclaimed quest rewards. This mail will expire in 30 days, so please claim these items before then."))
		ret := sendMail(uid, r, idFactory, character.MailIDGM, totalAward, params)
		if !ret {
			awardTowerSeasonTaskAwardFailed++
			l4g.Errorf("user: %s awardTowerSeasonTaskAward: send mail failed.", uid)
		}
		l4g.Info("user %s save awardTowerSeasonTaskAward.sendMail. success", uid)
	}

	dbUser.Module4.TowerSeason = nil
	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		userTowerSeasonDataFailed++
		l4g.Errorf("user %s save towerSeason error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save towerSeason success", uid)
}
