package summon

import (
	"app/tools/repair/cmd/redis"

	"github.com/spf13/cobra"
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	loadGoxml()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixUser(uid, redisClient)
	})
}

func loadGoxml() {
	//dir := "../data"
	//show := false
	// goxml.GetData().ConfigInfoM.Load(goxml.GetData(),dir, show)
}

func fixUser(uid string, redisClient *redis.Redis) {
	/*r := redisClient
	// base
	r.RopDB.GetUserMCallSKs(uid)

	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module1 == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	wishList := userData.Module.Weekly.SummonWishList[3]

	if wishList != nil && userData.Module.Summon[3] != nil {
		if userData.Module.Summon[3].WishListMsg == nil {
			userData.Module.Summon[3].WishListMsg = &db.WishList{
				List: make(map[uint32]uint32),
			}
		}
		for sort, id := range userData.Module.Summon[3].WishList {
			userData.Module.Summon[3].WishListMsg.List[sort] = id
		}
		userData.Module.Summon[3].WishListMsg.WishGuaranteeCount = wishList.WishGuaranteeCount
		userData.Module.Summon[3].WishListMsg.LockedSlot = wishList.LockedSlot
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d save user error: %s", uid, reply.Err)
			return
		}
		l4g.Infof("success user's uid:%s uuid:%s ", uid, userData.Uuid)
	}*/
}
