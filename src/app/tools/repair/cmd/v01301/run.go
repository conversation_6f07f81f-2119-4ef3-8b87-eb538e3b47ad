package v01301

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/tools/repair/cmd/rank"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath        = flag.String("V01301configData", "../data/", "service data path")
	fixRankAchieveCount uint32
	achieveData         map[uint32]*db.RankAchieves
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()

	rankM := rank.NewCommonRankManager()
	initRankData(redisClient, rankM)
	loadRankAchieves(redisClient)
	fixRankAchieve(redisClient, rankM)
	l4g.Info("\n")

	if fixRankAchieveCount == 0 {
		l4g.Infof("[SUCCESS] fixRankAchieve Success!")
	} else {
		l4g.Infof("[FAILED] fixRankAchieve Failed, need check error log! failedCount:%d", fixRankAchieveCount)
	}
	l4g.Info("finish repair")
}

var gRankMethod = map[uint32]rank.CommonRankValuer{
	goxml.DungeonRankId:       &rank.UserDungeon{},
	goxml.TowerRankId:         &rank.UserTower{},
	goxml.MirageEmpireRankId:  &rank.UserMirage{},
	goxml.MirageForestRankId:  &rank.UserMirage{},
	goxml.MirageMoonRankId:    &rank.UserMirage{},
	goxml.MirageProtossRankId: &rank.UserMirage{},
	goxml.MirageDemonRankId:   &rank.UserMirage{},
	goxml.MirageSixRankId:     &rank.UserMirage{},
}

var startIDList = map[uint32]uint32{
	goxml.DungeonRankId:       15026,
	goxml.TowerRankId:         20034,
	goxml.MirageEmpireRankId:  30019,
	goxml.MirageForestRankId:  31019,
	goxml.MirageMoonRankId:    32019,
	goxml.MirageProtossRankId: 33019,
	goxml.MirageDemonRankId:   34019,
	goxml.MirageSixRankId:     35019,
}

func initRankData(redisClient *redis.Redis, rankM *rank.CommonRankManager) {
	for rankID, commonRankValuer := range gRankMethod {
		redisClient.RopDB.GetAllCommonRankMCallSK(uint64(rankID))
		if data, err := redisClient.RopDB.GetSomeCommonRankByReply(redisClient.Client.GetReply()); err != nil {
			l4g.Errorf("get some rank data error: %s", err)
			panic("initRankData: GetAllCommonRankMCallSK failed")
		} else {
			commonRank := rankM.AddRankList(rankID, commonRankValuer, 1000)
			if commonRank == nil {
				l4g.Errorf("commonrank load data error no find rank id %d", rankID)
				panic("initRankData: AddRankList failed")
			}

			for _, v1 := range data {
				value := commonRank.NewElement()
				err := value.Load(v1.GetData())
				if err != nil {
					l4g.Errorf("commonrank load data error %d %v", rankID, err)
					panic("commonrank manager load data panic")
				}

				rankM.InsertFromRedis(rankID, value)
			}
		}
	}
}

func loadRankAchieves(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllRankAchievesMCall()
	if data, err := redisClient.RopDB.GetSomeRankAchievesByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get rank achievements data error: %s", err)
		panic("loadRankAchieves panic")
	} else {
		achieveData = data
	}
}

func fixRankAchieve(redisClient *redis.Redis, rankM *rank.CommonRankManager) {
	result := make([]*db.RankAchieves, 0, len(gRankMethod))
	for rankID := range gRankMethod {
		item := rankM.GetElementByRank(rankID, 1)
		if item != nil {
			item := item.(rank.CommonRankValuer).ShowValue()
			if item == nil {
				l4g.Errorf("fixRankAchieve: uid data error: %d", rankID)
				fixRankAchieveCount++
				continue
			}
			l4g.Infof("fixRankAchieve. rankID:%d, item: %+v", rankID, item)

			startID := startIDList[rankID]
			targetID := goxml.GetData().RankingAchievementInfoM.GetIdByValue(rankID, int64(item.Value))
			finishedIds := goxml.GetData().RankingAchievementInfoM.GetIdsByRange(rankID, startID, targetID)
			if len(finishedIds) > 0 {
				tmp := &db.RankAchieves{
					Id:       rankID,
					Achieves: make([]*cl.RankAchieve, 0, len(finishedIds)),
				}

				oldData := achieveData[rankID]
				if oldData != nil {
					for _, v := range oldData.Achieves {
						if v.Id <= startID {
							tmp.Achieves = append(tmp.Achieves, v)
							l4g.Infof("fixRankAchieve. achieveData rankID:%d, item: %+v", rankID, v)
						}
					}
				}

				uid := item.Id
				for _, id := range finishedIds {
					achieve := &cl.RankAchieve{
						Id:  id,
						Uid: uid,
					}
					tmp.Achieves = append(tmp.Achieves, achieve)
				}
				result = append(result, tmp)
			}
		}
	}

	for _, data := range result {
		l4g.Infof("fixRankAchieve. rankID: %d, achieveLen: %d", data.Id, len(data.Achieves))
		for _, v := range data.Achieves {
			l4g.Infof("fixRankAchieve. achieve: %+v", v)
		}
	}

	if len(result) > 0 {
		if err := redisClient.RopDB.SetSomeRankAchieves(result); err != nil {
			l4g.Errorf("fixRankAchieve failed. error:%s", err)
			fixRankAchieveCount++
		}
	}
}
