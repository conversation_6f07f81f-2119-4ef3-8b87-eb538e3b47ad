package v180

import (
	"app/goxml"
	"app/logic/character"
	ldb "app/logic/db"
	"app/logic/mail"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/tools/repair/cmd/base"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"

	"gitlab.qdream.com/kit/sea/math/rand"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
	// "gitlab.qdream.com/kit/sea/math/rand"
)

var (
	flagDataPath               = flag.String("V180configData", "../data/", "service data path")
	fixGodPresentFailedCount   uint32
	fixSeasonReturnFailedCount uint32
	fixTrialFailedCount        uint32
	fixGoddessSuitFailedCount  uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, redisActor *ldb.RedisActor) {
	goxml.Load(*flagDataPath, false, false)
	l4g.Info("\n")

	now := time.Now().Unix()
	makeTrialCfg()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixGodPresent(uid, redisClient, idFactory)
		fixSeasonReturn(uid, redisClient, idFactory)
		fixTrial(uid, redisClient, idFactory, now, redisActor)
		fixGoddessSuit(uid, redisClient)
	})

	if fixGodPresentFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGodPresent Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGodPresent Failed, need check error log! failedCount:%d\n", fixGodPresentFailedCount)
	}
	if fixSeasonReturnFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixSeasonReturn Success!\n")
	} else {
		fmt.Printf("[FAILED] fixSeasonReturn Failed, need check error log! failedCount:%d\n", fixSeasonReturnFailedCount)
	}
	if fixTrialFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixTrial Success!\n")
	} else {
		fmt.Printf("[FAILED] fixTrial Failed, need check error log! failedCount:%d\n", fixTrialFailedCount)
	}
	if fixGoddessSuitFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGoddessSuit Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGoddessSuit Failed, need check error log! failedCount:%d\n", fixGoddessSuitFailedCount)
	}

	l4g.Info("finish repair")
}

func fixGodPresent(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module4 == nil || userData.Module4.GodPresentsNew == nil {
		return
	}

	data := userData.Module4.GodPresentsNew
	if data.LoginTime > 0 {
		data.LoginTime = 0
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGodPresentFailedCount++
		l4g.Errorf("user %s save fixGodPresent  error: %s %s", uid, userData, reply.Err)
		return
	}
}

func fixSeasonReturn(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module5 == nil || userData.Module5.SeasonDungeon == nil {
		return
	}

	//有打过赛季主线的玩家，设置加入赛季时间为当前时间
	if userData.Module5.SeasonDungeon.DungeonId > 0 {
		if userData.Module5.SeasonReturn == nil {
			userData.Module5.SeasonReturn = &cl.SeasonReturn{}
		}
		userData.Module5.SeasonReturn.JoinSeasonTime = time.Now().Unix()
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixSeasonReturnFailedCount++
		l4g.Errorf("user %s save fixSeasonReturn  error: %s %s", uid, userData, reply.Err)
		return
	}
}

var trialCfg map[uint32]map[uint32]*cl.Resource

// 生成材料本要补偿的通关奖励数据
func makeTrialCfg() {
	trialCfg = make(map[uint32]map[uint32]*cl.Resource)
	for typ := uint32(45); typ <= 48; typ++ {
		var count uint32
		for level := uint32(51); level <= 100; level++ {
			info := goxml.GetData().TrialInfoM.Index(typ, level)
			if info == nil {
				panic(fmt.Sprintf("makeTrialCfg. no trial info, type:%d, level:%d", typ, level))
			}

			if _, exist := trialCfg[typ]; !exist {
				trialCfg[typ] = make(map[uint32]*cl.Resource)
			}

			count += info.PassRewardCount2
			trialCfg[typ][level] = &cl.Resource{
				Type:  info.PassRewardType2,
				Value: info.PassRewardValue2,
				Count: count,
			}
		}
	}
}

func fixTrial(uid string, r *redis.Redis, idFactory *id.Factory, now int64, redisActor *ldb.RedisActor) {
	u := base.LoadUserNew(uid, redisActor)
	if u == nil {
		l4g.Error("get user error: %s ", uid)
		return
	}

	dbUser := u.GetDBUser()
	if dbUser == nil || dbUser.Module == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if dbUser.Module.Trials == nil {
		return
	}

	playerLevelInfo := goxml.GetData().PlayerLevelInfoM.Index(u.Level())
	if playerLevelInfo == nil {
		fixTrialFailedCount++
		l4g.Errorf("user:%s fixTrial get user level:%d, no info", uid, u.Level())
		return
	}
	maxTime := playerLevelInfo.TrialCumulativeDuration * util.HourSecs

	rd := rand.New(time.Now().UnixNano())
	var change bool
	mailAwards := make([]*cl.Resource, 0, 1)
	for typ, trial := range dbUser.Module.Trials {
		if trial == nil {
			continue
		}

		if trial.Level <= 50 {
			continue
		}

		trialInfo := goxml.GetData().TrialInfoM.Index(trial.Type, trial.Level)
		if trialInfo == nil {
			l4g.Errorf("user:%s fixTrial type:%d level:%d info not exist", uid, trial.Type, trial.Level)
			continue
		}

		if now <= trial.OnHook.LastCalcTm {
			l4g.Errorf("user:%s fixTrial now:%d,  LastCalcTm:%d", uid, now, trial.OnHook.LastCalcTm)
			continue
		}

		calcBaseAward(u, rd, trial, trialInfo, now, maxTime)
		calcRandomDropAward(u, rd, trial, trialInfo, now, maxTime)
		trial.OnHook.Awards = u.MergeResources(trial.OnHook.Awards)
		change = true

		mailAwards = append(mailAwards, trialCfg[typ][trial.Level])
	}

	if change {
		r.RopDB.SetUserMCallSKs(uid, dbUser)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixTrialFailedCount++
			l4g.Errorf("user %s save fixTrial error: %s %s", uid, dbUser, reply.Err)
			return
		}
		l4g.Info("user %s save fixTrial success", uid)
	}

	if len(mailAwards) > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Forgotten Land Rewards")
		params = append(params, "The rewards for Floors 51-100 in Forgotten Land have been adjusted, we have reissued you the one-time rewards for these floors.")
		ret := sendMail(uid, r, idFactory, character.MailIDGM, mailAwards, params)
		if !ret {
			fixTrialFailedCount++
			l4g.Errorf("user: %s fixTrial: send mail failed.", uid)
		}
		l4g.Info("user %s save fixTrial.sendMail success", uid)
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}
	return true
}

func calcBaseAward(u *character.User, rd *rand.Rand, trial *cl.TrialInfo, trialInfo *goxml.TrialInfoExt, now, maxTime int64) {
	//已结算奖励的时间大于等于最大时间返回
	if trial.OnHook.CalcRewardDuration >= maxTime {
		trial.OnHook.LastCalcTm = now
		return
	}
	//这次结算时间的间隔 = 当前时间 - 上次结算时间
	duration := now - trial.OnHook.LastCalcTm
	// 这次时间间隔 + 本次有效奖励时间 > 最大时间
	// 这次时间间隔 = 最大有效时间 - 本次挂机有效的奖励时间
	if duration+trial.OnHook.CalcRewardDuration > maxTime {
		duration = maxTime - trial.OnHook.CalcRewardDuration
	}

	awards := calcOnhookAwards(u, rd, duration, int64(1), trialInfo, false)
	trial.OnHook.Awards = append(trial.OnHook.Awards, awards...)
	trial.OnHook.LastCalcTm = now
	trial.OnHook.CalcRewardDuration += duration
}

func calcRandomDropAward(u *character.User, rd *rand.Rand, trial *cl.TrialInfo, trialInfo *goxml.TrialInfoExt, now, maxTime int64) {
	//已结算奖励的时间大于等于最大时间返回
	if trial.OnHook.CalcRandRewardDuration >= maxTime {
		trial.OnHook.LastRandomDropTm = now
		return
	}
	//这次结算时间的间隔 = 当前时间 - 上次结算时间
	duration := now - trial.OnHook.LastRandomDropTm
	// 这次时间间隔 + 本次有效奖励时间 > 最大时间
	// 这次时间间隔 = 最大有效时间 - 本次挂机有效的奖励时间
	if duration+trial.OnHook.CalcRandRewardDuration > maxTime {
		duration = maxTime - trial.OnHook.CalcRandRewardDuration
	}
	// 累加上一次未结算的时间
	duration += trial.OnHook.SaveRandomDuration
	freeTime := goxml.GetData().TrialConfigInfoM.GetRandomFreeDropGroupTime()
	isZero, count := util.DivFloor(util.HourSecs*int64(freeTime), duration)
	if !isZero {
		for i := 0; i < int(count); i++ {
			dropAwards, _ := u.Drop().DoDrop(rd, trialInfo.RandomFreeDropGroup1)
			trial.OnHook.Awards = append(trial.OnHook.Awards, dropAwards...)
		}
	}
	//不足一次保存至下一次
	surplus := duration % (util.HourSecs * int64(freeTime))
	trial.OnHook.LastRandomDropTm = now
	trial.OnHook.SaveRandomDuration = surplus
	trial.OnHook.CalcRandRewardDuration += duration
}

func calcOnhookAwards(u *character.User, rd *rand.Rand, duration, recvCount int64,
	info *goxml.TrialInfoExt, isGuide bool) []*cl.Resource {
	products := goxml.GetData().VipPrivilegeInfoM.TrailOnhookEfficiency(u.Vip(), []int64{info.OnhookEfficiency[0]})

	awards := make([]*cl.Resource, 0, 1)
	baseParam := util.HourSecs * goxml.BaseInt64 * goxml.BaseInt64
	for index, product := range products {
		if product == 0 {
			continue
		}

		_, count := util.DivFloor(baseParam, duration, product, recvCount)
		if !isGuide {
			surplus := (duration * product) % baseParam
			randomCount := rd.Int63n(baseParam)
			if randomCount < surplus {
				count++
			}
		}

		if count > 0 {
			awards = append(awards, &cl.Resource{
				Type:  info.OnhookAwards[index].Type,
				Value: info.OnhookAwards[index].Value,
				Count: uint32(count),
			})
		}
	}
	return awards
}

var goddessSuit uint32 = 4005

func fixGoddessSuit(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module3 == nil || userData.Module3.GoddessContractInfo == nil ||
		userData.Module3.GoddessContractInfo.Goddess == nil {
		return
	}

	suitInfo := goxml.GetData().GoddessContractSkinInfoM.Index(goddessSuit)
	if suitInfo == nil {
		fixGoddessSuitFailedCount++
		l4g.Errorf("user %s get goddess suit:%d failed ", uid, goddessSuit)
		return
	}
	goddess, exist := userData.Module3.GoddessContractInfo.Goddess[suitInfo.Goddess]
	if !exist {
		return
	}

	if goddess.Level < suitInfo.UnlockLevel {
		return
	}

	for _, suitId := range goddess.SuitIds {
		if suitId == suitInfo.GoddessSkinId {
			return
		}
	}

	goddess.SuitIds = append(goddess.SuitIds, suitInfo.GoddessSkinId)
	if userData.ModuleGlobalAttr == nil {
		userData.ModuleGlobalAttr = &db.ModuleGlobalAttr{}
	}

	userData.ModuleGlobalAttr.GoddessSuits = append(userData.ModuleGlobalAttr.GoddessSuits, suitInfo.GoddessSkinId)

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGodPresentFailedCount++
		l4g.Errorf("user %s save fixGodPresent  error: %s %s", uid, userData, reply.Err)
		return
	}
}
