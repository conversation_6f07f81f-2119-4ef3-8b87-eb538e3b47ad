package v2150

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/protos/out/cl"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath       = flag.String("V2150configData", "../data/", "service data path")
	fixPassFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixPass(uid, redisClient, idFactory)
	})

	if fixPassFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPass Success!")
	} else {
		l4g.Infof("[FAILED] fixPass Failed, need check error log! failedCount:%d", fixPassFailedCount)
	}

	l4g.Info("finish repair")
}

func fixPass(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixPassFailedCount++
		return
	}
	if userData == nil || userData.Module2 == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}
	if len(userData.Module2.Passes) == 0 {
		return
	}
	passID := uint32(14) //先知战令
	rewards := make([]*cl.Resource, 0)
	if data, exist := userData.Module2.Passes[passID]; exist {
		if !data.Buy {
			return
		}
		for _, info := range data.Receive {
			if info.Charge {
				passTaskInfo := goxml.GetData().PassTaskInfoM.Index(info.Id)
				if passTaskInfo == nil {
					l4g.Errorf("[FAILED] user:%s PassTaskInfoM: id error:%d", uid, info.Id)
					fixPassFailedCount++
					return
				}
				if len(passTaskInfo.AwardClRes) >= 2 {
					rewards = append(rewards, passTaskInfo.AwardClRes[1].Clone())
				}
			}
		}
	}

	if len(rewards) > 0 {
		rewards = character.MergeResources(rewards)
		ret := sendMail(uid, r, idFactory, 184, rewards, nil)
		if !ret {
			fixPassFailedCount++
			l4g.Errorf("user: %s fixPass: send mail failed.", uid)
			return
		}
		l4g.Info("user %s save fixPass.sendMail. success", uid)
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("[FAILED] user %s save user mail error: %s", uid, reply.Err)
		return false
	}
	return true
}
