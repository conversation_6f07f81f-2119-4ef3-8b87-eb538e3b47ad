package redis

import (
	"app/goxml"
	"app/logic/db"
	"app/logic/db/redisop"
	dbProto "app/protos/in/db"
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/redis"
)

type Redis struct {
	Client *redis.Client
	RopDB  *redisop.DbClient
	RopCl  *redisop.ClClient
}

func OpenRedis(redisAddr string, redisIndex uint32) *Redis {
	goxml.GetData().ServerInfoM.Load("../data", false)
	client, _ := redis.Dial("tcp", redisAddr, goxml.GetData().ServerInfoM.RedisPassword, redisIndex, false)
	return &Redis{
		Client: client,
		RopDB:  &redisop.DbClient{Client: client},
		RopCl:  &redisop.ClClient{Client: client},
	}
}

func IterateAllUsers(redisClient *Redis, fn func(uid string)) {
	r := redisClient
	r.Client.Append("HGETALL", db.RedisHashAccount)
	if data, err := r.Client.GetReply().Hash(); err != nil {
		println("get account size error:", err)
	} else {
		for _, uid := range data {
			fn(uid)
		}
	}
}

func IterateAllGuildUsers(redisClient *Redis, fn func(guildUserData *dbProto.GuildUser)) {
	r := redisClient

	r.Client.Append("HVALS", "guilduser")
	if data, err := r.RopDB.GetSomeGuildUserByReply(r.Client.GetReply()); err != nil {
		println("get guildUser data error: %s", err)
	} else {
		for _, guildUserData := range data {
			fn(guildUserData)
		}
	}
}

func IterateOneUser(redisClient *Redis, fn func(uid string)) {
	uid := "****************"
	fn(uid)
}

func IterateAllForestUsers(redisClient *Redis, fn func(Forest *cl.Forest)) {
	r := redisClient
	r.Client.Append("HVALS", "forest")
	if data, err := r.RopCl.GetSomeForestByReply(r.Client.GetReply()); err != nil {
		println("get forest data error: %s", err)
	} else {
		for _, Forest := range data {
			fn(Forest)
		}
	}
}

func IterateAllFlowerUsers(redisClient *Redis, fn func(flower *cl.Flower)) {
	r := redisClient
	r.Client.Append("HVALS", "flower")
	if data, err := r.RopCl.GetSomeFlowerByReply(r.Client.GetReply()); err != nil {
		println("get flower data error: %s", err)
	} else {
		for _, flower := range data {
			fn(flower)
		}
	}
}
