package v09802

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath         = flag.String("V09802configData", "../data/", "service data path")
	fixFlowerFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	redis.IterateAllFlowerUsers(redisClient, func(flower *cl.Flower) {
		fixFlower(flower, redisClient)
	})
	l4g.Info("\n")

	if fixFlowerFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixFlower Success!\n")
	} else {
		fmt.Printf("[FAILED] fixFlower Failed, need check error log! failedCount:%d\n", fixFlowerFailedCount)
	}

	l4g.Info("finish repair")
}

// 修复老玩家密林防守阵容数据
func fixFlower(flower *cl.Flower, r *redis.Redis) {
	if flower == nil {
		return
	}
	uid := flower.Id
	flowerGid := flower.GuildId //密林中缓存的公会id

	var realGid uint64 //玩家当前实际公会id
	guildUser, _ := r.RopDB.GetGuildUser(uid)
	if guildUser != nil && guildUser.GuildId > 0 {
		realGid = guildUser.GuildId
	}

	if flowerGid == realGid {
		return
	}

	flower.GuildId = realGid
	l4g.Info("fixFlower. uid: %d, flowerGid: %d, realGid: %d", uid, flowerGid, realGid)

	//修改密林数据
	r.RopCl.SetSomeFlowerMCall([]*cl.Flower{flower})
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixFlowerFailedCount++
		l4g.Errorf("fixFlower. user: %d  save Flower error: %+v", uid, reply.Err)
		return
	}
}
