package v2320

import (
	"app/logic/character"
	"app/tools/repair/cmd/redis"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	fixFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	redis.IterateAllUsers(redisClient, func(uid string) {
		fix(uid, redisClient, idFactory)
	})

	if fixFailedCount == 0 {
		l4g.Infof("[SUCCESS] fix Success!")
	} else {
		l4g.Infof("[FAILED] fix Failed, need check error log! failedCount:%d", fixFailedCount)
	}
	l4g.Infof("finish repair")
}

func fix(uid string, r *redis.Redis, _ *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixFailedCount++
		return
	}
	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}
	if userData.Module7 == nil || userData.Module7.SeasonDoor == nil ||
		len(userData.Module7.SeasonDoor.Logs) == 0 {
		return
	}
	for _, log := range userData.Module7.SeasonDoor.Logs {
		if log.Result != nil && len(log.Result.Rewards) > 0 {
			log.Result.Rewards = character.MergeResources(log.Result.Rewards)
		}
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixFailedCount++
		l4g.Errorf("user %s save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save success", uid)
}
