package cmd

import (
	"app/logic/helper"
	v2250 "app/tools/repair/cmd/v2250"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/time"
)

var v2250Cmd = &cobra.Command{
	Use:   "v2250",
	Short: "2.25.0 - 1.战令补偿",
	Run: func(cmd *cobra.Command, args []string) {
		now := time.Now().Unix()
		serverDay := helper.DaysBetweenTimes(now, cfgFromEtcd.StartTime) + 1
		if serverDay < 28 {
			return
		}
		v2250.Run(cmd, args, redisClient, idFactory, serverDay)
	},
}

func init() {
	rootCmd.AddCommand(v2250Cmd)
}
