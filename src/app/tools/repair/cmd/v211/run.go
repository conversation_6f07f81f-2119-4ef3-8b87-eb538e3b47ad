package v211

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/tools/repair/cmd/crossRedis"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath               = flag.String("V211configData", "../data/", "service data path")
	fixPyramidFailedCount      uint32
	sendInfoToGuildFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, crossRedisClient *crossRedis.CrossRedis, partitionId uint32) {
	goxml.Load(*flagDataPath, false, false)
	l4g.Info("\n")

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixPyramid(uid, redisClient)
		sendInfoToGuild(uid, redisClient, idFactory, crossRedisClient, partitionId)
	})

	if fixPyramidFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixPyramid Success!\n")
	} else {
		fmt.Printf("[FAILED] fixPyramid Failed, need check error log! failedCount:%d\n", fixPyramidFailedCount)
	}

	if sendInfoToGuildFailedCount == 0 {
		fmt.Printf("[SUCCESS] sendInfoToGuild Success!\n")
	} else {
		fmt.Printf("[FAILED] sendInfoToGuild Failed, need check error log! failedCount:%d\n", sendInfoToGuildFailedCount)
	}

	l4g.Info("finish repair")
}

func fixPyramid(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("fixPyramid error:%s", err)
		fixPyramidFailedCount++
		return
	}

	if userData == nil {
		l4g.Errorf("fixPyramid error:%s", "userData is nil")
		fixPyramidFailedCount++
		return
	}

	if userData.Module6 == nil {
		return
	}

	if userData.Module6.Pyramid == nil {
		return
	}

	userData.Module6.Pyramid = nil

	if userData.Module2 != nil && userData.Module2.ActivityRecharge != nil {
		tmpData := userData.Module2.ActivityRecharge.Shops
		delete(tmpData, 704)
		userData.Module2.ActivityRecharge.Shops = tmpData
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixPyramidFailedCount++
		l4g.Errorf("user %s fixPyramid save userData failed  error: %s %s", uid, userData, reply.Err)
		return
	}
}

func sendInfoToGuild(uid string, r *redis.Redis, idFactory *id.Factory, crossRedisClient *crossRedis.CrossRedis, partitionId uint32) {
	user, err := r.RopDB.GetUserSKs(uid)
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
	}

	guildUser, err := r.RopDB.GetGuildUser(user.GetId())
	if err != nil {
		l4g.Errorf("user: %d sendInfoToGuild: getGuildUser error. err:%s", user.GetId(), err)
		sendInfoToGuildFailedCount++
		return
	}

	if guildUser == nil {
		return
	}

	// 当前无公会的不需要刷数据
	if guildUser.GetGuildId() == 0 {
		return
	}

	if guildUser.LastLeaveTm == 0 && len(guildUser.GuildLevelCnt) == 0 {
		return
	}

	guild, err := crossRedisClient.RopCr.GetGuild(guildUser.GetGuildId())
	if err != nil {
		l4g.Errorf("user: %d sendInfoToGuild: getGuild error. err:%s gid:%d", user.GetId(), err, guildUser.GetGuildId())
		sendInfoToGuildFailedCount++
		return
	}

	if guild == nil {
		l4g.Errorf("user: %d sendInfoToGuild: getGuild nil. gid:%d", user.GetId(), guildUser.GetGuildId())
		sendInfoToGuildFailedCount++
		return
	}

	if guild.Members == nil {
		l4g.Info("user %s sendInfoToGuild: guild members is 0. gid:%d", uid, guildUser.GetGuildId())
		return
	}

	self := guild.Members[user.GetId()]
	if self == nil {
		l4g.Info("user %s sendInfoToGuild: self members not exist. gid:%d", uid, guildUser.GetGuildId())
		return
	}

	self.LastLeaveTm = guildUser.LastLeaveTm
	self.GuildLeaveCnt = guildUser.GuildLevelCnt
	guild.Members[user.GetId()] = self

	err = crossRedisClient.RopCr.SetSomeGuild([]*cr.Guild{guild})
	if err != nil {
		l4g.Errorf("user: %d sendInfoToGuild: setGuild error. err:%s gid:%d", user.GetId(), err, guildUser.GetGuildId())
		sendInfoToGuildFailedCount++
		return
	}
	l4g.Info("user %s sendInfoToGuild success", uid)
}
