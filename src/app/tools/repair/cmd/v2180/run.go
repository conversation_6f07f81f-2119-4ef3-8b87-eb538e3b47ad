package v2180

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath        = flag.String("V2180configData", "../data/", "service data path")
	fixDivineDemonCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixDivineDemon(uid, redisClient, idFactory)
	})

	if fixDivineDemonCount == 0 {
		l4g.Infof("[SUCCESS] fixDivineDemon Success!")
	} else {
		l4g.Infof("[FAILED] fixDivineDemon Failed, need check error log! failedCount:%d", fixDivineDemonCount)
	}

	l4g.Info("finish repair")
}

func fixDivineDemon(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixDivineDemonCount++
		return
	}
	if userData == nil || userData.Module3 == nil || userData.Module3.DivineDemon == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	info := goxml.GetData().DivineDemonConfigInfoM
	divineDemon := userData.Module3.DivineDemon
	if divineDemon.Summon == nil {
		return
	}

	if divineDemon.Id == 3 && divineDemon.SysId == 10001 {
		divineDemon.SummonV2 = &cl.DivineDemonSummonV2{}
		divineDemon.SummonV2.NotUpHeroCount = info.UpHeroGuarantee - 1
		divineDemon.SummonV2.ColorGuarantee = make([]uint32, goxml.DivineDemonMaxGuaranteeLen)
		if divineDemon.Summon.SummonTotalCount < info.FirstDisplayWishHeroMaxSummonCost {
			divineDemon.SummonV2.ColorGuarantee[goxml.DivineDemonRedGuaranteeIndex] = divineDemon.Summon.SummonTotalCount
		}
		if divineDemon.Summon != nil {
			divineDemon.SummonV2.DiamondSummonCount = divineDemon.Summon.DiamondSummonCount
		}

		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDivineDemonCount++
			l4g.Errorf("user %s fixDivineDemonCount save userData failed  error: %s %s", uid, userData, reply.Err)
			return
		}
	}

}
