package v2240

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath                = flag.String("V2240configData", "../data/", "service data path")
	fixDuelFormationFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, serverId uint64) {
	goxml.Load(*flagDataPath, false, false)
	redis.IterateAllUsers(redisClient, func(uid string) { // 处理所有玩家的切磋阵容
		fixDuelFormation(uid, redisClient)
	})

	if fixDuelFormationFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixDuelFormation Success!\n")
	} else {
		fmt.Printf("[FAILED] fixDuelFormation Failed, need check error log! failedCount:%d\n", fixDuelFormationFailedCount)
	}

	l4g.Info("finish repair")
}

func fixDuelFormation(uid string, r *redis.Redis) {
	var sourceFids = []uint32{uint32(common.FORMATION_ID_FI_ARENA_DEFENSE), uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE), uint32(common.FORMATION_ID_FI_SEASON_ARENA_FIVE_DEFENSE)}
	var targetFids = []uint32{uint32(common.FORMATION_ID_FI_DUEL_1), uint32(common.FORMATION_ID_FI_DUEL_2), uint32(common.FORMATION_ID_FI_DUEL_3)}
	if len(sourceFids) != len(targetFids) {
		l4g.Error("fixDuelFormation: uid %s sourceFids %+v not match targetFids %+v. ", uid, sourceFids, targetFids)
		return
	}

	saveFormations := make([]*cl.Formation, 0, len(sourceFids))
	/************************CALL*******************************/
	r.RopCl.GetSomeFormationMCallSKs(uid, sourceFids)
	/************************REPLY*******************************/
	rsp, err := r.RopCl.GetSomeFormationByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("fixDuelFormation: uid %s get formations error. formations %+v err %s", uid, sourceFids, err)
		fixDuelFormationFailedCount++
		return
	}
	for index, sourceFid := range sourceFids {
		if formation, exist := rsp[sourceFid]; exist {
			newFormation := formation.Clone()
			newFormation.Id = targetFids[index]
			saveFormations = append(saveFormations, newFormation)
		}
	}

	// 统一保存阵容
	if len(saveFormations) > 0 {
		/************************CALL*******************************/
		r.RopCl.SetSomeFormationMCallSKs(uid, saveFormations)
		/************************REPLY*******************************/
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDuelFormationFailedCount++
			l4g.Errorf("fixDuelFormation: user %s save formations error. formations %+v err %s", uid, saveFormations, reply.Err)
			return
		}
	}

	l4g.Infof("uid: %s fixDuelFormation success!", uid)
}
