package cmd

import (
	v0110 "app/tools/repair/cmd/v0110"

	"github.com/spf13/cobra"
)

var v0110Cmd = &cobra.Command{
	Use:   "v0110",
	Short: "0.11.0 - 1.英雄分解 2.水晶成就数据转移 3.修复主线关卡 4.修复引导 5.修复战令 6.删除跨服战个人数据 7.删除跨服战公共数据 8.悬赏任务删除修复 9.英雄转换道具补偿 10.删除不存在的商品ID 11.初始化材料本的挂机时间 12.契约之所女武修复 13.功能预告任务修复",
	Run: func(cmd *cobra.Command, args []string) {
		v0110.Run(cmd, args, redisClient, idFactory)
	},
}

func init() {
	rootCmd.AddCommand(v0110Cmd)
}
