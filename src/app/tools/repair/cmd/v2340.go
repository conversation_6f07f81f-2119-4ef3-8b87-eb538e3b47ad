package cmd

import (
	v2340 "app/tools/repair/cmd/v2340"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

var v2340Cmd = &cobra.Command{
	Use:   "v2340",
	Short: "v2340 - 1.赛季装备修复空词条 2.新增等级基金修复 3.国服-登录送红将进度补充 4.国服-竞赛任务脚本",
	Run: func(cmd *cobra.Command, args []string) {
		serverType := cfgFromEtcd.Platform.ServerType
		serverDay := uint32(0)
		now := time.Now().Unix()
		if now > cfgFromEtcd.StartTime {
			serverDay = util.DaysBetweenTimes(now, cfgFromEtcd.StartTime) + 1
		}
		v2340.Run(cmd, args, redisClient, idFactory, serverType, serverDay)
	},
}

func init() {
	rootCmd.AddCommand(v2340Cmd)
}
