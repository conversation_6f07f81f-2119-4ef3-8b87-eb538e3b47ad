package towerstar

import (
	"app/tools/repair/cmd/redis"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	redis.IterateAllUsers(redisClient, func(uid string) {
		fix(uid, redisClient)
	})
}

func fix(uid string, redisClient *redis.Redis) {
	r := redisClient
	// base
	r.RopDB.GetUserMCallSKs(uid)

	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module2 == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	userData.Module2.Towerstar = nil

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user error: %+v", uid, reply.Err)
		return
	}
	l4g.Infof("userData:%s infos+%v", uid, userData)
}
