package cmd

import (
	"app/protos/in/l2c"
	"app/tools/repair/cmd/crossRedis"
	v211 "app/tools/repair/cmd/v211"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

var v211Cmd = &cobra.Command{
	Use:   "v211",
	Short: "2.11.0 - 1.金字塔GM迁移 2.公会合并logic迁移玩家离会cd到cross",
	Run: func(cmd *cobra.Command, args []string) {
		indexUint, err := strconv.Atoi(crossRedisIndex)
		if err != nil {
			panic(fmt.Sprintf("%s", err))
		}
		crossRedisClient := crossRedis.OpenCrossRedis(crossRedisAddr, uint32(indexUint))
		if crossRedisClient == nil {
			panic(fmt.Sprintf("open cross redis failed crossRedisAddr:%s crossRedisIndex:%d", crossRedisAddr, indexUint))
		}

		cmIndex, err := strconv.Atoi(crossMasterRedisIndex)
		if err != nil {
			panic(fmt.Sprintf("%s", err))
		}
		crossMasterRedisClient := crossRedis.OpenCrossRedis(crossMasterRedisAddr, uint32(cmIndex))
		if crossMasterRedisClient == nil {
			panic(fmt.Sprintf("open cross redis failed crossMasterRedisAddr:%s crossMasterRedisIndex:%d", crossMasterRedisAddr, cmIndex))
		}
		partitionId := crossRedis.GetServerPartition(0, cfgFromEtcd.ServerId, crossMasterRedisClient)
		if partitionId == 0 {
			panic(fmt.Sprintf("partitionId is 0. activityId:%d serverId:%d", uint64(l2c.ACTIVITYID_GUILD), cfgFromEtcd.ServerId))
		}
		v211.Run(cmd, args, redisClient, idFactory, crossRedisClient, partitionId)
	},
}

func init() {
	rootCmd.AddCommand(v211Cmd)
}
