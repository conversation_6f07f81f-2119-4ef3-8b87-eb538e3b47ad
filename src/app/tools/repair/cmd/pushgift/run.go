package pushgift

import (
	"app/tools/repair/cmd/redis"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	redis.IterateAllUsers(redisClient, func(uid string) {
		delPushGiftData(uid, redisClient)
	})
}

func delPushGiftData(uid string, redisClient *redis.Redis) {
	r := redisClient
	// base
	r.RopDB.GetUserMCallSKs(uid)
	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s ", uid, err)
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module2 != nil {
		pushGiftData := userData.Module2.PushGiftInfo
		if pushGiftData != nil {
			for _, pushGifts := range pushGiftData.PushGifts {
				if pushGifts != nil && len(pushGifts.PushGift) > 0 {
					pushGifts.PushGift = pushGifts.PushGift[:0]
				}
			}
		}
		userData.Module2.PushGiftInfo = pushGiftData
		r.RopDB.SetUserMCallSKs(uid, userData)
		/************************REPLY*******************************/
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %+v", uid, reply.Err)
			return
		}
	}

	l4g.Infof("uid: %s delete push gift data ok!", uid)
}
