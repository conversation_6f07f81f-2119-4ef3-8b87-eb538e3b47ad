package clearhotrank

import (
	"app/goxml"
	"app/tools/repair/cmd/redis"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	fixHotRankFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	delHotRankData(redisClient)

	if fixHotRankFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixHotRankFailed Success!\n")
	} else {
		fmt.Printf("[FAILED] fixHotRankFailed Failed, need check error log! failedCount:%d\n", fixHotRankFailedCount)
	}
}

func delHotRankData(redisClient *redis.Redis) {
	for formationId := range goxml.GetData().HotRankInfoM.Formation {
		var calls int
		var key uint64
		for i := 0; i < 10; i++ {
			key = uint64(formationId)<<32 | uint64(i)
			redisClient.RopDB.DelAllLogicHotRankUserFormationMCallSK(key)
			calls++
		}
		for i := 0; i < 10; i++ {
			if reply := redisClient.RopDB.GetReply(); reply.Err != nil {
				l4g.Errorf("del hot rank user formation key:%d error: %s ", key, reply.Err)
				fixHotRankFailedCount++
				continue
			}
		}
	}

	err := redisClient.RopDB.DelAllLogicHotRankCollectionLog()
	if err != nil {
		l4g.Errorf("del hot rank collection log error: %s", err.Error())
		fixHotRankFailedCount++
	}
}
