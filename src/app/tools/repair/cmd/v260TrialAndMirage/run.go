package v260TrialAndMirage

import (
	"app/goxml"
	"app/protos/in/db"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"
	"strconv"

	"github.com/gogo/protobuf/proto"

	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"

	l4g "github.com/ivanabc/log4go"
)

var (
	flagDataPath = flag.String("v260TrialAndMirageConfigData", "../data/", "service data path")

	fixTrialFailedCount      uint32
	fixMirageFailedCount     uint32
	fixMirageRankFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixTrial(uid, redisClient)
		fixMirage(uid, redisClient)
	})
	fixMirageRank(redisClient)
	l4g.Info("\n")

	if fixTrialFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixTrial Success!\n")
	} else {
		fmt.Printf("[FAILED] fixTrial Failed, need check error log! failedCount:%d\n", fixTrialFailedCount)
	}

	if fixMirageFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMirage Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMirage Failed, need check error log! failedCount:%d\n", fixMirageFailedCount)
	}

	if fixMirageRankFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMirageRank Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMirageRank Failed, need check error log! failedCount:%d\n", fixMirageRankFailedCount)
	}

	l4g.Info("finish repair")
}

func fixTrial(uid string, redisClient *redis.Redis) {
	// uid 不存在表里，直接跳过
	if !xmlcfg.FixTrialPlayerListM.IsExist(uid) {
		return
	}

	redisClient.RopDB.GetUserMCallSKs(uid)
	userData, err := redisClient.RopDB.GetUserByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Module == nil || userData.Module.Trials == nil {
		return
	}

	var change bool
	for typ, trial := range userData.Module.Trials {
		level := xmlcfg.FixTrialPlayerListM.Index(uid, typ)
		if level > 0 {
			l4g.Infof("fixTrial, uid:%s, typ:%d, oldLevel:%d, newLevel:%d, trial:%+v", uid, typ, trial.Level, level, trial)
			trial.Level = level
			change = true
		}
	}

	if change {
		redisClient.RopDB.SetUserMCallSKs(uid, userData)
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			fixTrialFailedCount++
			l4g.Errorf("user %s save trial error: %s %s", uid, userData, reply.Err)
			return
		}
		l4g.Info("user %s save trial success", uid)
	}
}

func fixMirage(uid string, redisClient *redis.Redis) {
	// uid 不存在表里，直接跳过
	if !xmlcfg.FixMiragePlayerListM.IsExist(uid) {
		return
	}

	redisClient.RopCl.GetAllMirageMCallSKs(uid)
	mirageDatas, err := redisClient.RopCl.GetSomeMirageByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("get mirage data error: %s %s", uid, err)
		fixMirageFailedCount++
	}
	if len(mirageDatas) == 0 {
		return
	}

	deletes := make([]uint32, 0, len(mirageDatas))
	for hurdleID, mirage := range mirageDatas {
		mirageType := hurdleID / 100
		if hurdleID%100 == 0 {
			mirageType -= 1
		}

		pCfg := xmlcfg.FixMiragePlayerListM.Index(uid, mirageType)
		if pCfg == nil {
			continue
		}

		l4g.Infof("fixMirage, uid:%s, hurdleID:%d, mirageType:%d, mirage:%+v", uid, hurdleID, mirageType, mirage)
		if mirageType == pCfg.MirageType && hurdleID > pCfg.MinId {
			deletes = append(deletes, hurdleID)
		}
	}

	var call int
	if len(deletes) > 0 {
		redisClient.RopCl.RemSomeMirageMCallSKs(uid, deletes)
		call++
		l4g.Infof("fixMirage, uid:%s, deletes:%+v", uid, deletes)
	}

	/************************REPLY*******************************/
	for i := 0; i < call; i++ {
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save delete mirage data error: %s", uid, reply.Err)
			fixMirageFailedCount++
			return
		}
	}
}

var rankIDs = map[uint32]struct{}{
	goxml.MirageEmpireRankId:  {},
	goxml.MirageForestRankId:  {},
	goxml.MirageMoonRankId:    {},
	goxml.MirageProtossRankId: {},
	goxml.MirageDemonRankId:   {},
	goxml.MirageSixRankId:     {},
}

func fixMirageRank(redisClient *redis.Redis) {
	rankPool := make(map[uint32]map[uint64]*db.RankMirage)
	for rankID := range rankIDs {
		redisClient.RopDB.GetAllCommonRankMCallSK(uint64(rankID))
		rankData, err := redisClient.RopDB.GetSomeCommonRankByReply(redisClient.Client.GetReply())
		if err != nil {
			l4g.Errorf("get rank data error: %s, rankID: %d", err, rankID)
			fixMirageRankFailedCount++
		}
		if len(rankData) == 0 {
			return
		}

		if _, exist := rankPool[rankID]; !exist {
			rankPool[rankID] = make(map[uint64]*db.RankMirage)
		}

		for uid, rankValue := range rankData {
			rankMirage := &db.RankMirage{}
			err = proto.Unmarshal(rankValue.Data, rankMirage)
			if err != nil {
				l4g.Errorf("get rank data error: %s, rankID: %d, uid: %d", err, rankID, uid)
				continue
			}

			rankPool[rankID][uid] = rankMirage
		}
	}

	deletes := make(map[uint32][]uint64, 5)

	for uid, v := range xmlcfg.FixMiragePlayerListM.Datas {
		userID, err := strconv.ParseUint(uid, 10, 64)
		if err != nil {
			l4g.Errorf("fixMirageRank parse uid error: %s, uid: %s", err, uid)
			fixMirageRankFailedCount++
			continue
		}
		for mirageType, cfg := range v {
			rankID := goxml.MirageCopyID2RankId[mirageType]

			rankMirage := rankPool[rankID][userID]
			if rankMirage == nil {
				continue
			}
			if rankMirage.Floor > cfg.Floor {
				deletes[rankID] = append(deletes[rankID], userID)
			}
		}
	}
	var call int
	for rankID, v := range deletes {
		redisClient.RopDB.RemSomeCommonRankMCallSK(uint64(rankID), v)
		call++
		l4g.Infof("fixMirageRank, rankID:%d, v:%+v", rankID, v)
	}

	/************************REPLY*******************************/
	for i := 0; i < call; i++ {
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save delete mirage rank data error: %s", reply.Err)
			fixMirageRankFailedCount++
			return
		}
	}
}
