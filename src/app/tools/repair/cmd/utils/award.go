package utils

import (
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/redis"
	l4g "github.com/ivanabc/log4go"
)

func Award(uid string, r *redis.Redis, retRes []*cl.Resource) bool {
	// user
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return false
	}

	if userData.Bag == nil {
		userData.Bag = &db.Bags{
			Items: make(map[uint32]uint32),
		}
	}

	if userData.Bag.Items == nil {
		userData.Bag.Items = make(map[uint32]uint32)
	}

	isChange := false
	for _, res := range retRes {
		if res.Type == uint32(common.RESOURCE_ITEM) {
			userData.Bag.Items[res.Value] += res.Count
			isChange = true
		}
		if res.Type == uint32(common.RESOURCE_GOLD) {
			userData.Base.Gold += uint64(res.Count)
			isChange = true
		}
	}

	if isChange {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s %s", uid, userData, reply.Err)
			return false
		}
	}

	return true
}

// 删除玩家拥有的所有目标资源
func ConsumeAllResource(uid string, r *redis.Redis, retRes []*cl.Resource) bool {
	// user
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return false
	}

	if userData.Bag == nil {
		return false
	}

	if userData.Bag.Items == nil {
		return false
	}

	isChange := false
	for _, res := range retRes {
		if res.Type == uint32(common.RESOURCE_ITEM) {
			num := userData.Bag.Items[res.Value]
			if num > 0 {
				delete(userData.Bag.Items, res.Value)
			}
			isChange = true
		}
	}

	if isChange {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s %s", uid, userData, reply.Err)
			return false
		}
	}

	return true
}
