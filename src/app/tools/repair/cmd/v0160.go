package cmd

import (
	v0160 "app/tools/repair/cmd/v0160"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

var v0160Cmd = &cobra.Command{
	Use:   "v0160",
	Short: "0.16.0 - 1.迁移公会到跨服 2.清楚爬塔阵容",
	Run: func(cmd *cobra.Command, args []string) {
		indexUint, err := strconv.Atoi(crossRedisIndex)
		if err != nil {
			panic(fmt.Sprintf("%s", err))
		}
		v0160.Run(cmd, args, redisClient, idFactory, redisActor, crossRedisAddr, uint32(indexUint))
	},
}

func init() {
	rootCmd.AddCommand(v0160Cmd)
}
