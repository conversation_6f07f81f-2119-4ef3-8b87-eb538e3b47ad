package resetSeasonMap

import (
	"app/goxml"
	ldb "app/logic/db"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath = flag.String("seasonMapConfigData", "../data/", "service data path")

	resetSeasonMapFailed uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, redisActor *ldb.RedisActor) {
	goxml.Load(*flagDataPath, false, false)
	redis.IterateAllUsers(redisClient, func(uid string) {
		seasonMapReset(uid, redisClient)
	})

	l4g.Info("\n")
	if resetSeasonMapFailed == 0 {
		fmt.Printf("[SUCCESS] resetSeasonMap Success!\n")
	} else {
		fmt.Printf("[FAILED] resetSeasonMap Failed, need check error log! failedCount:%d\n", resetSeasonMapFailed)
	}

	l4g.Info("finish repair")
}

func seasonMapReset(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module8 == nil {
		return
	}

	userData.Module8.SeasonMap = nil

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		resetSeasonMapFailed++
		l4g.Errorf("user %s save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s seasonMapReset success", uid)
}
