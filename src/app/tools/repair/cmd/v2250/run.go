package v2250

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/protos/out/cl"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
	"gitlab.qdream.com/kit/sea/time"
)

var (
	flagDataPath       = flag.String("V2250configData", "../data/", "service data path")
	fixPassFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, serverDay uint32) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	now := time.Now().Unix()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixPass(uid, redisClient, idFactory, serverDay, now)
	})

	if fixPassFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPass Success!")
	} else {
		l4g.Infof("[FAILED] fixPass Failed, need check error log! failedCount:%d", fixPassFailedCount)
	}

	l4g.Info("finish repair")
}

func fixPass(uid string, r *redis.Redis, idFactory *id.Factory, serverDay uint32, now int64) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("[FAILED] get user error: %s %s", uid, err)
		fixPassFailedCount++
		return
	}
	if userData == nil || userData.Module2 == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}
	if len(userData.Module2.Passes) == 0 {
		return
	}
	passIDs := []uint32{15, 17}
	mailID := uint32(184)
	rewards := make([]*cl.Resource, 0)
	var isFind bool
	for _, passID := range passIDs {
		if data, exist := userData.Module2.Passes[passID]; exist {
			if isFind {
				delete(userData.Module2.Passes, passID)
				continue
			}
			buy := data.Buy
			allRecv := false
			allTask := xmlcfg.V2240PassTaskInfoM.GetTaskDatasByPassID(passID)
			if len(data.Receive) == 0 {
				allRecv = true
			}
			for _, v := range allTask {
				if v == nil {
					continue
				}
				if allRecv {
					rewards = append(rewards, v.FreeClRes...)
					if buy {
						rewards = append(rewards, v.AwardClRes...)
					}
				} else {
					recvState, exist := data.Receive[v.Id]
					if !exist {
						rewards = append(rewards, v.FreeClRes...)
						if buy {
							rewards = append(rewards, v.AwardClRes...)
						}
					} else {
						if !recvState.Free {
							rewards = append(rewards, v.FreeClRes...)
						}
						if !recvState.Charge && buy {
							rewards = append(rewards, v.AwardClRes...)
						}
					}
				}
			}
			isFind = true
			passInfo := xmlcfg.V2240PassInfoM.Index(passID)
			if passInfo != nil && passInfo.MailId > 0 {
				mailID = passInfo.MailId
			}
		}
		delete(userData.Module2.Passes, passID)
	}

	passInfo := goxml.GetData().PassInfoM.Index(18)
	if passInfo != nil && serverDay >= passInfo.UnlockDay {
		var reparation uint32
		realDay := serverDay - passInfo.UnlockDay
		round := realDay/passInfo.Cycle + 1
		endDay := passInfo.UnlockDay + round*passInfo.Cycle
		if endDay >= serverDay {
			reparation = 20 - (endDay - serverDay)
		}
		if reparation > 15 {
			reparation = 15
		}
		if reparation > 0 {
			task := &cl.TaskTypeProgress{
				TaskTypeId: 5308000,
				Progress:   uint64(reparation * 100),
			}
			userData.Module2.Passes[18] = &cl.PassData{
				SysId:        18,
				TaskProgress: make(map[uint32]*cl.TaskTypeProgress),
			}
			userData.Module2.Passes[18].TaskProgress[5308000] = task
			userData.Module2.Passes[18].ActivePass = &cl.PassActive{
				ActiveTime: now,
			}
		}
	} else {
		if passInfo == nil {
			l4g.Error("[FAILED] user:%s get passInfo:18", uid)
			fixPassFailedCount++
		}
	}

	if len(rewards) > 0 {
		rewards = character.MergeResources(rewards)
		ret := sendMail(uid, r, idFactory, mailID, rewards, nil)
		if !ret {
			fixPassFailedCount++
			l4g.Errorf("user: %s fixPass: send mail failed.", uid)
			return
		}
		l4g.Info("user %s save fixPass.sendMail. success", uid)
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixPassFailedCount++
		l4g.Errorf("user %s save fixPass  error: %s %s", uid, userData, reply.Err)
		return
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("[FAILED] user %s save user mail error: %s", uid, reply.Err)
		return false
	}
	return true
}
