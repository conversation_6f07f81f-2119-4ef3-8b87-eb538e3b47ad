package v110

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath = flag.String("dailyattendanceConfigData", "../data/", "service data path")

	fixDailyAttendanceFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()

	redis.IterateAllUsers(redisClient, func(uid string) {
		fixDailyAttendance(uid, redisClient)
	})
	l4g.Info("\n")

	if fixDailyAttendanceFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixDailyattendance Success!\n")
	} else {
		fmt.Printf("[FAILED] fixDailyattendance Failed, need check error log! failedCount:%d\n", fixDailyAttendanceFailedCount)
	}

	l4g.Info("finish repair")
}

func fixDailyAttendance(uid string, r *redis.Redis) {
	if uid != strconv.FormatUint(579860609402566298, 10) && uid != strconv.FormatUint(579860635646666548, 10) {
		return
	}

	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module4 == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}
	if userData.Module4.DailyAttendance == nil {
		userData.Module4.DailyAttendance = &cl.DailyAttendance{}
	}

	userData.Module4.DailyAttendance.LoginCount = 90
	if userData.Module4.DailyAttendance.Datas == nil {
		userData.Module4.DailyAttendance.Datas = make(map[uint32]*cl.DailyAttendanceData)
	}
	// 轮次
	for i := uint32(1); i < 4; i++ {
		data, exist := userData.Module4.DailyAttendance.Datas[i]
		if !exist {
			data = &cl.DailyAttendanceData{}
			userData.Module4.DailyAttendance.Datas[i] = data
		}

		data.FirstLoginTime = time.Now().Unix()
		data.LastLoginTime = time.Now().Unix()
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixDailyAttendanceFailedCount++
		l4g.Errorf("user %s save error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save fixWorldBoss success", uid)
}
