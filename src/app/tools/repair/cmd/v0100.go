package cmd

import (
	v0100 "app/tools/repair/cmd/v0100"

	"github.com/spf13/cobra"
)

var v0100Cmd = &cobra.Command{
	Use:   "v0100",
	Short: "0.10.0 - 1.修复阵容为多队结构 2.初始化老号的全民无双任务 3.删除密林战斗日志 4.删除老密林战斗日志 5.删除老密林数据 6.删除玩家战斗日志 7.删除历史战报 8.商店数据清理 9.水晶成就清理 10.刷新战力 11.契约之所修复 12.推送礼包删除",
	// Short: "0.10.0 - 1.修复阵容为多队结构 2.初始化老号的全民无双任务 3.删除密林战斗日志 4.删除老密林战斗日志 5.删除老密林数据 6.删除玩家战斗日志 7.删除历史战报 8.修复心愿单数据 9.商店数据清理 10.水晶成就清理",
	Run: func(cmd *cobra.Command, args []string) {
		v0100.Run(cmd, args, redisClient, idFactory)
	},
}

func init() {
	rootCmd.AddCommand(v0100Cmd)
}
