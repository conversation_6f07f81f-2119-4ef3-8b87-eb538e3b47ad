package idfactory

import (
	"app/logic/db"
	"app/logic/helper"
	"app/protos/in/config"
	appsrv "app/service"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"

	"app/tools/repair/cmd/redis"

	snet "gitlab.qdream.com/kit/sea/net"

	"context"

	"gitlab.qdream.com/kit/sea/id"
)

func OpenIDFactory(cfgFromEtcd *config.Logic, redisClient *redis.Redis) *id.Factory {
	idStore := new(IdStore)
	idStore.Init(cfgFromEtcd, redisClient)

	maxID := helper.MaxIDOfService(idStore.cfgFromEtcd.NodeId)
	idFactory := id.NewFactory("logic", 10, 15, 100000, maxID, idStore)
	idFactory.Init()

	return idFactory
}

type IdStore struct {
	redisClient *redis.Redis
	cfgFromEtcd *config.Logic
}

func (i *IdStore) Init(cfgFromEtcd *config.Logic, redisClient *redis.Redis) {
	i.cfgFromEtcd = cfgFromEtcd
	i.redisClient = redisClient
}

func (i *IdStore) Load(name string) id.Backup {
	reply := i.redisClient.Client.Cmd("HGET", db.RedisHashID, name)
	if reply.Err != nil {
		l4g.Errorf("find id failed: %v", reply.Err)
		return id.Backup{}
	}
	if reply.Type == 3 {
		return i.updateBackup(0, time.Now().Unix(), name)
	}

	str, err := reply.Str()
	if err != nil {
		l4g.Errorf("find id failed: %s", err)
		return id.Backup{}
	}
	nums := util.StringToUint64Slice(str, "-")
	if len(nums) != 2 {
		l4g.Errorf("parse id failed: %d", len(nums))
		return id.Backup{}
	}

	return i.updateBackup(nums[0], int64(nums[1]), name)
}

func (i *IdStore) updateBackup(uniqID uint64, ts int64, name string) id.Backup {
	var backup id.Backup
	if uniqID == 0 {
		//全区全服唯一ID生成方式
		//高15位：区服ID
		//低49位：自增数
		firstID := i.cfgFromEtcd.NodeId << 49
		backup.ID = firstID
		backup.Time = ts
		backup.Name = name
		firstID >>= 49
		if firstID != i.cfgFromEtcd.NodeId {
			panic(fmt.Sprintf("服务器ID设置异常: %d", i.cfgFromEtcd.NodeId))
		}
	} else {
		backup.ID = uniqID
		backup.Time = ts
		backup.Name = name
	}

	return backup
}

func (i *IdStore) Save(backup id.Backup) {
	var nums [2]uint64
	nums[0] = backup.ID
	nums[1] = uint64(backup.Time)
	str := util.Uint64SliceToString(nums[:], "-")
	if _, err := i.redisClient.Client.Cmd("HSET", db.RedisHashID, backup.Name, str).Int(); err != nil {
		l4g.Errorf("hset id data error: %s %s", backup, err)
	} else {
		l4g.Infof("save id data success: %s", backup)
	}
	sign, ok := id.BackupResults.Load(backup.Name)
	if ok {
		sign.(chan id.Backup) <- id.Backup{
			Name: backup.Name,
			ID:   backup.ID,
			Time: backup.Time,
		}
		l4g.Debugf("send id data finish: %s", backup)
	} else {
		l4g.Errorf("on found id: %s", backup.Name)
	}
}

func getServiceID(debugAddr string) string {
	port := strings.Split(debugAddr, ":")[1]
	var prefix string
	if *appsrv.IPV4 {
		ips, _ := snet.GetHostIPv4()
		prefix = ips[0]
	} else {
		prefix, _ = os.Hostname()
	}
	serviceID := prefix + ":" + port

	return serviceID
}

func CreateID(idfactory *id.Factory) uint64 {
	var id uint64
	for {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Millisecond)
		defer cancel()
		id = idfactory.Create(ctx)
		if id != 0 {
			break
		} else {
			l4g.Errorf("wait %s backup id too long...", idfactory.Name())
		}
	}
	return id
}
