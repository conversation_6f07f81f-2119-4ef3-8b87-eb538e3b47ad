package xmlcfg

import (
	"app/protos/out/cl"
	"encoding/xml"
	"fmt"
)

type _ = cl.Resource

type TaskInfo struct {
	Id          uint32 `xml:"id,attr"`           //int:唯一ID
	Module      uint32 `xml:"module,attr"`       //int:所属模块
	TypeId      uint32 `xml:"type_id,attr"`      //int:任务类型ID
	Value       uint64 `xml:"value,attr"`        //int:完成条件
	GetPoints   uint32 `xml:"get_points,attr"`   //int:奖励积分
	ResetType   uint32 `xml:"reset_type,attr"`   //int:重置类型
	Type1       uint32 `xml:"type1,attr"`        //int:奖励类型1
	Value1      uint32 `xml:"value1,attr"`       //int:值1
	Count1      uint32 `xml:"count1,attr"`       //int:数量1
	Type2       uint32 `xml:"type2,attr"`        //int:类型2
	Value2      uint32 `xml:"value2,attr"`       //int:值2
	Count2      uint32 `xml:"count2,attr"`       //int:数量2
	Type3       uint32 `xml:"type3,attr"`        //int:类型3
	Value3      uint32 `xml:"value3,attr"`       //int:值3
	Count3      uint32 `xml:"count3,attr"`       //int:数量3
	Type4       uint32 `xml:"type4,attr"`        //int:类型4
	Value4      uint32 `xml:"value4,attr"`       //int:值4
	Count4      uint32 `xml:"count4,attr"`       //int:数量4
	OpenLevel   uint32 `xml:"open_level,attr"`   //int:开放等级
	OpenDungeon uint32 `xml:"open_dungeon,attr"` //int:开放关卡
	OpenDay     uint32 `xml:"open_day,attr"`     //int:开放天数

	// type1 value1 count1
	// type2 value2 count2
	// type3 value3 count3
	// type4 value4 count4
	ClRes []*cl.Resource `xml:"-"` //非xml原始字段
}

type TaskInfos struct {
	Datas []*TaskInfo `xml:"data"`
}

type TaskInfoExt struct {
	Id uint32 //
	//FuncId    uint32         //
	OpenLevel   uint32         //int:开放等级
	OpenDungeon uint32         //int:开服关卡
	TypeId      uint32         //int:任务类型
	Progress    uint64         //int:任务类型值
	ResetType   uint32         //1 成就 2 日常 3 周长
	Points      uint32         //奖励积分
	Awards      []*cl.Resource //奖励
	Module      uint32         //所属模块
	Value       uint64         // 任务目标值
}

type TaskInfoManager struct {
	evts map[uint32][]*TaskInfoExt
}

func (m *TaskInfoManager) Load(datas []byte) error {
	tmp := &TaskInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic("v260_task_info.xml Unmarshal failed")
	}

	m.evts = make(map[uint32][]*TaskInfoExt)
	for _, data := range tmp.Datas {
		dataExt := &TaskInfoExt{}
		var awards []*cl.Resource
		if data.Type1 > 0 && data.Count1 > 0 {
			awards = append(awards, GenSimpleResource(data.Type1, data.Value1, data.Count1))
		}
		if data.Type2 > 0 && data.Count2 > 0 {
			awards = append(awards, GenSimpleResource(data.Type2, data.Value2, data.Count2))
		}
		if data.Type3 > 0 && data.Count3 > 0 {
			awards = append(awards, GenSimpleResource(data.Type3, data.Value3, data.Count3))
		}
		if data.Type4 > 0 && data.Count4 > 0 {
			awards = append(awards, GenSimpleResource(data.Type4, data.Value4, data.Count4))
		}
		if awards == nil {
			panic(fmt.Sprintf("gen awards error %s", fileName))
		}
		dataExt.Id = data.Id
		dataExt.OpenLevel = data.OpenLevel
		dataExt.OpenDungeon = data.OpenDungeon
		dataExt.TypeId = data.TypeId
		dataExt.Progress = data.Value
		dataExt.ResetType = data.ResetType
		dataExt.Points = data.GetPoints
		dataExt.Awards = awards
		dataExt.Module = data.Module
		dataExt.Value = data.Value
		m.evts[dataExt.TypeId] = append(m.evts[dataExt.TypeId], dataExt)
	}
	return nil
}

func (m *TaskInfoManager) IndexEvent(key uint32) []*TaskInfoExt {
	return m.evts[key]
}
