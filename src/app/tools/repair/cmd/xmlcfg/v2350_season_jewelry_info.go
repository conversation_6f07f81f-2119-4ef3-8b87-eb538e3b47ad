package xmlcfg

//import "fmt"
import (
	"app/protos/out/cl"
	"encoding/xml"
)

// Reference imports to suppress errors if they are not otherwise used.
type _ = cl.Resource

type SeasonJewelryInfo struct {
	Id              uint32 `xml:"id,attr"`                //装备id
	Level           uint32 `xml:"level,attr"`             //装等
	Rare            uint32 `xml:"rare,attr"`              //品质
	Pos             uint32 `xml:"pos,attr"`               //位置
	Link            uint32 `xml:"link,attr"`              //羁绊
	Suit            uint32 `xml:"suit,attr"`              //套装
	Power           uint32 `xml:"power,attr"`             //战力
	AttrType1       uint32 `xml:"attr_type_1,attr"`       //属性1类型
	AttrValue1      uint64 `xml:"attr_value_1,attr"`      //属性1值
	AttrType2       uint32 `xml:"attr_type_2,attr"`       //属性2类型
	AttrValue2      uint64 `xml:"attr_value_2,attr"`      //属性2值
	AttrType3       uint32 `xml:"attr_type_3,attr"`       //属性3类型
	AttrValue3      uint64 `xml:"attr_value_3,attr"`      //属性3值
	SkillGroup1     uint32 `xml:"skill_group_1,attr"`     //词条1随机池
	SkillGroup2     uint32 `xml:"skill_group_2,attr"`     //词条2随机池
	SkillGroup3     uint32 `xml:"skill_group_3,attr"`     //词条3随机池
	IsDevelop       uint32 `xml:"is_develop,attr"`        //是否可养成
	IsLevelUp       uint32 `xml:"is_level_up,attr"`       //是否可强化
	IsChange        uint32 `xml:"is_change,attr"`         //是否可洗炼
	IsClassUp       uint32 `xml:"is_class_up,attr"`       //是否可进阶
	CostType        uint32 `xml:"cost_type,attr"`         //每次强化消耗类型
	CostValue       uint32 `xml:"cost_value,attr"`        //每次强化消耗值
	CostCount       uint32 `xml:"cost_count,attr"`        //每次强化消耗数量
	CostChangeType  uint32 `xml:"cost_change_type,attr"`  //一键洗炼消耗类型
	CostChangeValue uint32 `xml:"cost_change_value,attr"` //一键洗炼消耗值
	CostChangeCount uint32 `xml:"cost_change_count,attr"` //一键洗炼消耗数量
	RecType         uint32 `xml:"rec_type,attr"`          //分解获得资源类型
	RecValue        uint32 `xml:"rec_value,attr"`         //分解获得资源值
	RecCount        uint32 `xml:"rec_count,attr"`         //分解获得资源数量
	SeasonPoint     uint32 `xml:"season_point,attr"`      //回收评分
}

func (m *SeasonJewelryInfo) prepare() {}

func (m *SeasonJewelryInfo) Check() error {
	return nil
}

type SeasonJewelryInfos struct {
	Datas []*SeasonJewelryInfo `xml:"data"`
}

type V2350SeasonJewelryInfoManager struct {
	Datas map[uint32]*SeasonJewelryInfo
}

func (m *V2350SeasonJewelryInfoManager) Load(datas []byte) {
	tmp := &SeasonJewelryInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic("v2350_season_jewelry_info.xml Unmarshal failed")
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*SeasonJewelryInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
}

func (m *V2350SeasonJewelryInfoManager) Index(key uint32) *SeasonJewelryInfo {
	return m.Datas[key]
}
