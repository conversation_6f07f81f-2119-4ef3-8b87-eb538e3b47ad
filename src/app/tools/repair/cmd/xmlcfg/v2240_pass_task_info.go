package xmlcfg

import (
	"app/protos/out/cl"
	"encoding/xml"
)

type _ = cl.Resource

type V2240PassTaskInfo struct {
	Id           uint32 `xml:"id,attr"`             //int:id
	PassId       uint32 `xml:"pass_id,attr"`        //int:战令id
	TypeId       uint32 `xml:"type_id,attr"`        //int:任务类型
	Value        uint32 `xml:"value,attr"`          //int:任务参数
	FreeType     uint32 `xml:"free_type,attr"`      //int:免费奖励类型
	FreeValue    uint32 `xml:"free_value,attr"`     //int:值
	FreeCount    uint32 `xml:"free_count,attr"`     //int:数量
	AwardType1   uint32 `xml:"award_type_1,attr"`   //int:高级奖励类型1
	AwardValue1  uint32 `xml:"award_value_1,attr"`  //int:值1
	AwardCount1  uint32 `xml:"award_count_1,attr"`  //int:数量1
	AwardType2   uint32 `xml:"award_type_2,attr"`   //int:高级奖励类型2
	AwardValue2  uint32 `xml:"award_value_2,attr"`  //int:值2
	AwardCount2  uint32 `xml:"award_count_2,attr"`  //int:数量2
	Award2Type1  uint32 `xml:"award2_type_1,attr"`  //int:高级奖励2类型1
	Award2Value1 uint32 `xml:"award2_value_1,attr"` //int:值1
	Award2Count1 uint32 `xml:"award2_count_1,attr"` //int:数量1

	// free_type free_value free_count
	FreeClRes []*cl.Resource `xml:"-"` //非xml原始字段

	// award_type_1 award_value_1 award_count_1
	// award_type_2 award_value_2 award_count_2
	AwardClRes []*cl.Resource `xml:"-"` //非xml原始字段

	// award2_type_1 award2_value_1 award2_count_1
	Award2ClRes []*cl.Resource `xml:"-"` //非xml原始字段
}

func (m *V2240PassTaskInfo) prepare() {
	// 资源
	if m.FreeType > 0 && m.FreeCount > 0 {
		m.FreeClRes = append(m.FreeClRes, &cl.Resource{
			Type:  m.FreeType,
			Value: m.FreeValue,
			Count: m.FreeCount,
		})
	}
	if m.AwardType1 > 0 && m.AwardCount1 > 0 {
		m.AwardClRes = append(m.AwardClRes, &cl.Resource{
			Type:  m.AwardType1,
			Value: m.AwardValue1,
			Count: m.AwardCount1,
		})
	}
	if m.AwardType2 > 0 && m.AwardCount2 > 0 {
		m.AwardClRes = append(m.AwardClRes, &cl.Resource{
			Type:  m.AwardType2,
			Value: m.AwardValue2,
			Count: m.AwardCount2,
		})
	}
	if m.Award2Type1 > 0 && m.Award2Count1 > 0 {
		m.Award2ClRes = append(m.Award2ClRes, &cl.Resource{
			Type:  m.Award2Type1,
			Value: m.Award2Value1,
			Count: m.Award2Count1,
		})
	}
}

type V2240PassTaskInfos struct {
	Datas []*PassTaskInfo `xml:"data"`
}

type V2240PassTaskInfoManager struct {
	Datas  map[uint32]*PassTaskInfo
	PassID map[uint32][]*PassTaskInfo
}

func (m *V2240PassTaskInfoManager) Load(datas []byte) {
	tmp := &V2240PassTaskInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic("v2240_pass_task_info.xml Unmarshal failed")
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*PassTaskInfo, len(tmp.Datas))
	}
	m.PassID = make(map[uint32][]*PassTaskInfo, len(tmp.Datas)/10)
	for _, data := range tmp.Datas {
		data.prepare()
		_, exist := m.PassID[data.Id]
		if !exist {
			m.PassID[data.Id] = []*PassTaskInfo{}
		}
		m.PassID[data.PassId] = append(m.PassID[data.PassId], data)
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
}

func (m *V2240PassTaskInfoManager) Index(key uint32) *PassTaskInfo {
	return m.Datas[key]
}

func (m *V2240PassTaskInfoManager) GetTaskDatasByPassID(key uint32) []*PassTaskInfo {
	return m.PassID[key]
}
