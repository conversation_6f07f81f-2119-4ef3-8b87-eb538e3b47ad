package xmlcfg

import (
	"app/goxml"
	"encoding/xml"
	"fmt"

	"app/protos/out/cl"
)

type _ = cl.Resource

type V093HeroStageInfo struct {
	Stage  uint32 `xml:"stage,attr"`   //
	NeedLv uint32 `xml:"need_lv,attr"` //int:需求英雄等级
	Type1  uint32 `xml:"type_1,attr"`  //int:消耗道具类型
	Value1 uint32 `xml:"value_1,attr"` //int:消耗道具ID
	Count1 uint32 `xml:"count_1,attr"` //int:消耗道具数量
	Type2  uint32 `xml:"type_2,attr"`  //int:消耗道具类型
	Value2 uint32 `xml:"value_2,attr"` //int:消耗道具ID
	Count2 uint32 `xml:"count_2,attr"` //int:消耗道具数量

	//type_1 value_1 count_1
	//type_2 value_2 count_2
	ClRes []*cl.Resource `xml:"-"` //非xml原始字段
}

func (h *V093HeroStageInfo) prepare() {
	//资源
	if h.Type1 > 0 && h.Count1 > 0 {
		h.ClRes = append(h.ClRes, &cl.Resource{
			Type:  h.Type1,
			Value: h.Value1,
			Count: h.Count1,
		})
	}
	if h.Type2 > 0 && h.Count2 > 0 {
		h.ClRes = append(h.ClRes, &cl.Resource{
			Type:  h.Type2,
			Value: h.Value2,
			Count: h.Count2,
		})
	}
}

type HeroStageInfos struct {
	Datas []*V093HeroStageInfo `xml:"data"`
}

type V093HeroStageInfoManager struct {
	Datas              map[uint32]*V093HeroStageInfoExt
	minStage, maxStage uint32
}

type V093HeroStageInfoExt struct {
	Stage  uint32 //int:当前突破等级
	NeedLv uint32 //int:需求等级
	Costs  []*cl.Resource
}

func (h *V093HeroStageInfoManager) Load(datas []byte) {
	fileName := "v093_hero_stage_info.xml"
	tmp := &HeroStageInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic(fmt.Sprintf("v093_hero_stage_info.xml Unmarshal failed"))
	}
	if h.Datas == nil {
		h.Datas = make(map[uint32]*V093HeroStageInfoExt, len(tmp.Datas))
	}
	h.maxStage = 0

	dataCount := len(tmp.Datas)
	if dataCount == 0 {
		panic(fmt.Sprintf("load config %s, no config data", fileName))
	}
	h.minStage = tmp.Datas[0].Stage
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Stage > h.minStage {
			if h.maxStage+1 != data.Stage {
				panic(fmt.Sprintf("config data discontinuity: %s, stage:%d", fileName, data.Stage))
			}
		}
		h.maxStage = data.Stage

		dataExt := &V093HeroStageInfoExt{
			Stage:  data.Stage,
			NeedLv: data.NeedLv,
			Costs:  make([]*cl.Resource, 0, goxml.HeroStageUpCostNum),
		}

		// 升级消耗
		dataExt.Costs = data.ClRes
		if dataCount-1 > int(data.Stage) {
			if len(dataExt.Costs) == 0 {
				panic(fmt.Sprintf("load config %s fail: %d no costs", fileName, data.Stage))
			}
		}

		if ptr, exist := h.Datas[data.Stage]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			h.Datas[data.Stage] = dataExt
		}
	}
}

func (h *V093HeroStageInfoManager) Index(stage uint32) *V093HeroStageInfoExt {
	return h.Datas[stage]
}

func (h *V093HeroStageInfoManager) GetDefaultStage() uint32 {
	return h.minStage
}

func (h *V093HeroStageInfoManager) GetMaxStage() uint32 {
	return h.maxStage
}
