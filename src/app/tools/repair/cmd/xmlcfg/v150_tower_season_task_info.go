package xmlcfg

import (
	"app/protos/out/cl"
	"encoding/xml"
	"fmt"
)

type _ = cl.Resource

type TowerSeasonTaskInfo struct {
	Id        uint32 `xml:"id,attr"`         //int:唯一ID
	Season    uint32 `xml:"season,attr"`     //int:赛季
	Module    uint32 `xml:"module,attr"`     //int:所属模块
	TypeId    uint32 `xml:"type_id,attr"`    //int:任务类型ID
	Value     uint32 `xml:"value,attr"`      //int:完成条件
	ResetType uint32 `xml:"reset_type,attr"` //int:重置类型
	Type1     uint32 `xml:"type1,attr"`      //int:奖励类型1
	Value1    uint32 `xml:"value1,attr"`     //int:值1
	Count1    uint32 `xml:"count1,attr"`     //int:数量1
	Type2     uint32 `xml:"type2,attr"`      //int:类型2
	Value2    uint32 `xml:"value2,attr"`     //int:值2
	Count2    uint32 `xml:"count2,attr"`     //int:数量2
	Type3     uint32 `xml:"type3,attr"`      //int:类型3
	Value3    uint32 `xml:"value3,attr"`     //int:值3
	Count3    uint32 `xml:"count3,attr"`     //int:数量3
	Type4     uint32 `xml:"type4,attr"`      //int:类型4
	Value4    uint32 `xml:"value4,attr"`     //int:值4
	Count4    uint32 `xml:"count4,attr"`     //int:数量4

	// type1 value1 count1
	// type2 value2 count2
	// type3 value3 count3
	// type4 value4 count4
	ClRes []*cl.Resource `xml:"-"` //非xml原始字段
}

func (t *TowerSeasonTaskInfo) prepare() {
	// 资源
	if t.Type1 > 0 && t.Count1 > 0 {
		t.ClRes = append(t.ClRes, &cl.Resource{
			Type:  t.Type1,
			Value: t.Value1,
			Count: t.Count1,
		})
	}
	if t.Type2 > 0 && t.Count2 > 0 {
		t.ClRes = append(t.ClRes, &cl.Resource{
			Type:  t.Type2,
			Value: t.Value2,
			Count: t.Count2,
		})
	}
	if t.Type3 > 0 && t.Count3 > 0 {
		t.ClRes = append(t.ClRes, &cl.Resource{
			Type:  t.Type3,
			Value: t.Value3,
			Count: t.Count3,
		})
	}
	if t.Type4 > 0 && t.Count4 > 0 {
		t.ClRes = append(t.ClRes, &cl.Resource{
			Type:  t.Type4,
			Value: t.Value4,
			Count: t.Count4,
		})
	}
}

/*
func (t *TowerSeasonTaskInfo) Check() error {
return nil
}
*/
type TowerSeasonTaskInfos struct {
	Datas []*TowerSeasonTaskInfo `xml:"data"`
}

type TowerSeasonTaskInfoExt struct {
	Id       uint32         //
	Season   uint32         //赛季
	TypeId   uint32         //int:任务类型
	Progress uint64         //int:任务类型值
	Awards   []*cl.Resource //奖励
	Module   uint32         //所属模块
}

type V150TowerSeasonTaskInfoManager struct {
	Datas  map[uint32]*TowerSeasonTaskInfoExt
	evts   map[uint32][]*TowerSeasonTaskInfoExt
	season map[uint32][]*TowerSeasonTaskInfoExt //season Id
}

func (t *V150TowerSeasonTaskInfoManager) Load(datas []byte) {
	tmp := &TowerSeasonTaskInfos{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic(fmt.Sprintf("v150_tower_season_task_info.xml Unmarshal failed"))
	}
	if t.Datas == nil {
		t.Datas = make(map[uint32]*TowerSeasonTaskInfoExt, len(tmp.Datas))
	}

	t.evts = make(map[uint32][]*TowerSeasonTaskInfoExt)
	t.season = make(map[uint32][]*TowerSeasonTaskInfoExt)

	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &TowerSeasonTaskInfoExt{}
		if len(data.ClRes) == 0 {
			panic(fmt.Sprintf("gen awards error %s", fileName))
		}
		dataExt.Id = data.Id
		dataExt.TypeId = data.TypeId
		dataExt.Progress = uint64(data.Value)
		dataExt.Module = data.Module
		dataExt.Awards = data.ClRes
		dataExt.Season = data.Season
		if ptr, exist := t.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			t.Datas[data.Id] = dataExt
		}
		t.evts[dataExt.TypeId] = append(t.evts[dataExt.TypeId], dataExt)
		_, exist := t.season[dataExt.Season]
		if !exist {
			t.season[dataExt.Season] = make([]*TowerSeasonTaskInfoExt, 0, 1)
		}
		t.season[dataExt.Season] = append(t.season[dataExt.Season], dataExt)
	}
}

func (t *V150TowerSeasonTaskInfoManager) Index(key uint32) *TowerSeasonTaskInfoExt {
	return t.Datas[key]
}

func (t *V150TowerSeasonTaskInfoManager) IndexEvent(key uint32) []*TowerSeasonTaskInfoExt {
	return t.evts[key]
}

func (t *V150TowerSeasonTaskInfoManager) SeasonTasks(season uint32) []*TowerSeasonTaskInfoExt {
	return t.season[season]
}
