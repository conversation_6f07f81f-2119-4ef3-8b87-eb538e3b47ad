package xmlcfg

import (
	"encoding/xml"
	"fmt"
)

type FixTrialPlayerList struct {
	PlayerId string `xml:"player_id,attr"`
	Type     uint32 `xml:"type,attr"`
	Level    uint32 `xml:"level,attr"`
}

type FixTrialPlayerLists struct {
	Datas []*FixTrialPlayerList `xml:"data"`
}

type FixTrialPlayerListManager struct {
	Datas map[string]map[uint32]uint32 // uid -> type -> different
}

func (t *FixTrialPlayerListManager) Load(datas []byte) {
	tmp := &FixTrialPlayerLists{}
	if err := xml.Unmarshal(datas, tmp); err != nil {
		panic("fix_trial_player_list.xml Unmarshal failed")
	}
	if t.Datas == nil {
		t.Datas = make(map[string]map[uint32]uint32)
	}
	for _, data := range tmp.Datas {
		if data.Level <= 0 {
			panic(fmt.Sprintf("fix_trial_player_list.xml level illegal, uid:%s", data.PlayerId))
		}

		if _, exist := t.Datas[data.PlayerId]; !exist {
			t.Datas[data.PlayerId] = make(map[uint32]uint32)
		}
		t.Datas[data.PlayerId][data.Type] = data.Level
	}
}

func (t *FixTrialPlayerListManager) IsExist(uid string) bool {
	if _, exist := t.Datas[uid]; exist {
		return true
	}
	return false
}

func (t *FixTrialPlayerListManager) Index(uid string, typ uint32) uint32 {
	return t.Datas[uid][typ]
}
