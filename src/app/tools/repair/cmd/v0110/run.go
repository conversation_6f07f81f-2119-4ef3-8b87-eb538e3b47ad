package v0110

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/base"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"
	"strconv"
	"time"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath                  = flag.String("V0110configData", "../data/", "service data path")
	fixHeroDecomposeFailedCount   uint32
	fixCrystalAchieveFailedCount  uint32
	fixDungeonFailedCount         uint32
	fixGuidanceFailedCount        uint32
	fixPassFailedCount            uint32
	delWrestlePersonalFailedCount uint32 //删除跨服竞技场个人数据
	delWrestleFailedCount         uint32 //删除跨服竞技场公共数据
	fixDispatchTaskFailedCount    uint32
	fixShopFailedCount            uint32
	fixItemFailedCount            uint32
	fixTrialFailedCount           uint32
	fixGoddessFailedCount         uint32
	fixGuidanceAddResFailedCount  uint32
	fixWishListFailedCount        uint32
	fixArtifactPointFailedCount   uint32
	fixForecastFailedCount        uint32

	fixPowerFailedCount      uint32
	fixPowerRankFailedCount  uint32
	fixGlobalRankFailedCount uint32
	globalRank               map[uint64]*db.GlobalRank
	commonPowerRank          map[uint64]*db.CommonRank
)

var (
	deleteDispatch = map[uint32]uint32{
		60:   500,
		68:   1000,
		1068: 300,
		1085: 300,
		1102: 300,
		1108: 300,
	}

	deleteGoods = map[uint32]struct{}{
		10105: {},
		20133: {},
	}
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	now := time.Now().Unix()
	getPowerRank(redisClient)
	getGlobalRank(redisClient)
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixHeroDecompose(uid, redisClient, idFactory)
		fixCrystalAchieve(uid, redisClient)
		fixGuidance(uid, redisClient)
		//fixGuidanceAddRes(uid, redisClient) // 必须在 fixDungeon 前面
		//fixDungeon(uid, redisClient)
		//fixPass(uid, redisClient)
		fixDispatch(uid, redisClient, idFactory)
		fixShop(uid, redisClient)
		fixItem(uid, redisClient, idFactory)
		fixTrial(uid, redisClient, now)
		delWrestlePersonal(uid, redisClient)
		fixGoddess(uid, redisClient, idFactory)
		fixWishList(uid, redisClient)
		fixArtifactPoint(uid, redisClient, idFactory)
		fixForecast(uid, redisClient)
		fixPower(uid, redisClient)
	})

	savePowerRank(redisClient)
	saveGlobalRank(redisClient)
	delWrestle(redisClient)
	l4g.Info("\n")

	if fixHeroDecomposeFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixHeroDecompose Success!\n")
	} else {
		fmt.Printf("[FAILED] fixHeroDecompose Failed, need check error log! failedCount:%d\n", fixHeroDecomposeFailedCount)
	}

	if fixCrystalAchieveFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixCrystalAchieve Success!\n")
	} else {
		fmt.Printf("[FAILED] fixCrystalAchieve Failed, need check error log! failedCount:%d\n", fixCrystalAchieveFailedCount)
	}

	//if fixDungeonFailedCount == 0 {
	//	fmt.Printf("[SUCCESS] fixDungeon Success!\n")
	//} else {
	//	fmt.Printf("[FAILED] fixDungeon Failed, need check error log! failedCount:%d\n", fixDungeonFailedCount)
	//}
	//
	if fixGuidanceFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuidance Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuidance Failed, need check error log! failedCount:%d\n", fixGuidanceFailedCount)
	}
	//
	//if fixPassFailedCount == 0 {
	//	fmt.Printf("[SUCCESS] fixPass Success!\n")
	//} else {
	//	fmt.Printf("[FAILED] fixPass Failed, need check error log! failedCount:%d\n", fixPassFailedCount)
	//}

	if delWrestlePersonalFailedCount == 0 {
		fmt.Printf("[SUCCESS] delWrestlePersonal Success!\n")
	} else {
		fmt.Printf("[FAILED] delWrestlePersonal Failed, need check error log! failedCount:%d\n", delWrestlePersonalFailedCount)
	}

	if delWrestleFailedCount == 0 {
		fmt.Printf("[SUCCESS] delWrestle Success!\n")
	} else {
		fmt.Printf("[FAILED] delWrestle Failed, need check error log! failedCount:%d\n", delWrestleFailedCount)
	}

	if fixDispatchTaskFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixDispatchTask Success!")
	} else {
		l4g.Infof("[FAILED] fixDispatchTask Failed, need check error log! failedCount:%d", fixDispatchTaskFailedCount)
	}

	if fixShopFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixShop Success!")
	} else {
		l4g.Infof("[FAILED] fixShop Failed, need check error log! failedCount:%d", fixShopFailedCount)
	}

	if fixItemFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixItem Success!")
	} else {
		l4g.Infof("[FAILED] fixItem Failed, need check error log! failedCount:%d", fixItemFailedCount)
	}

	if fixTrialFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixTrial Success!")
	} else {
		l4g.Infof("[FAILED] fixTrial Failed, need check error log! failedCount:%d", fixTrialFailedCount)
	}

	if fixGoddessFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixGoddess Success!")
	} else {
		l4g.Infof("[FAILED] fixGoddess Failed, need check error log! failedCount:%d", fixGoddessFailedCount)
	}

	//if fixGuidanceAddResFailedCount == 0 {
	//	l4g.Infof("[SUCCESS] fixGuidanceAddRes Success!")
	//} else {
	//	l4g.Infof("[FAILED] fixGuidanceAddRes Failed, need check error log! failedCount:%d", fixGuidanceAddResFailedCount)
	//}
	if fixWishListFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixWishList Success!")
	} else {
		l4g.Infof("[FAILED] fixWishList Failed, need check error log! failedCount:%d", fixWishListFailedCount)
	}

	if fixArtifactPointFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixArtifactPoint Success!")
	} else {
		l4g.Infof("[FAILED] fixArtifactPoint Failed, need check error log! failedCount:%d", fixArtifactPointFailedCount)
	}

	if fixPowerFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPower Success!")
	} else {
		l4g.Infof("[FAILED] fixPower Failed, need check error log! failedCount:%d", fixPowerFailedCount)
	}

	if fixPowerRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPowerRank Success!")
	} else {
		l4g.Infof("[FAILED] fixPowerRank Failed, need check error log! failedCount:%d", fixPowerRankFailedCount)
	}

	if fixGlobalRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixGlobalRank Success!")
	} else {
		l4g.Infof("[FAILED] fixGlobalRank Failed, need check error log! failedCount:%d", fixGlobalRankFailedCount)
	}

	if fixForecastFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixForecast Success!")
	} else {
		l4g.Infof("[FAILED] fixForecast Failed , need check error log! failedCount:%d", fixForecastFailedCount)
	}

	l4g.Info("finish repair")
}

var heroFragment = []uint32{513999, 522999, 534999}
var hero2StarExchange = map[uint32]uint32{
	13999: 11997,
	22999: 24998,
	34999: 33998,
}
var avatars = []uint32{112, 214, 313}

// 将上阵的2星英雄替换成对应的3星英雄
// 分解非上阵2星英雄以及碎片，返还资源
// 开启自动分解2星英雄的设置
func fixHeroDecompose(uid string, redisClient *redis.Redis, idFactory *id.Factory) {
	u := base.LoadUser(uid, redisClient)

	//处理自动分解的设置
	var updateUser bool
	if !u.IsSummonAutoDecompose() {
		u.SetSummonAutoDecompose(true)
		updateUser = true
		l4g.Infof("fixHeroDecompose. SetSummonAutoDecompose, uid:%s", uid)
	}

	//处理avatar
	baseID := u.BaseID()
	if baseID == 65536112 || baseID == 65536214 || baseID == 65536313 {
		u.UpQuality(65536001)
		updateUser = true
		l4g.Infof("fixHeroDecompose. UpQuality, uid:%s", uid)
	}

	dbUser := u.GetDBUser()
	if dbUser.Module != nil && len(dbUser.Module.Avatars) > 0 {
		for _, id := range avatars {
			delete(dbUser.Module.Avatars, id)
			updateUser = true
		}
	}

	heroM := u.HeroManager()
	equipM := u.EquipManager()
	emblemM := u.EmblemManager()

	heroes := heroM.GetAllCommonHero()
	updateHeroes := make([]*cl.HeroBody, 0, 1)
	delHids := make([]uint64, 0, 5)
	equipIDs := make([]uint64, 0, 5)
	emblemIDs := make([]uint64, 0, 5)
	for _, hero := range heroes {
		info := goxml.GetData().HeroInfoM.Index(hero.GetHeroSysID())
		if info == nil {
			l4g.Errorf("fixHeroDecompose. hero info not exist, uid:%s, heroSysID:%d", uid, hero.GetHeroSysID())
			continue
		}

		if newSysID, exist := hero2StarExchange[hero.GetHeroSysID()]; exist {
			if isInFormation(u, hero.GetHid()) {
				hero.SetHeroSysID(newSysID)
				hero.SetStar(3)
				updateHeroes = append(updateHeroes, hero.GetData().Clone())
				l4g.Infof("fixHeroDecompose. uid:%s, hid:%d", uid, hero.GetHid())
			} else {
				delHids = append(delHids, hero.GetHid())

				for _, equipID := range hero.GetAllEquipment() {
					if equipID > 0 {
						equipIDs = append(equipIDs, equipID)
					}
				}

				for _, emblemID := range hero.GetAllEmblem() {
					if emblemID > 0 {
						emblemIDs = append(emblemIDs, emblemID)
					}
				}

				l4g.Infof("fixHeroDecompose. uid:%s, delHid:%v, equipIDs:%v, emblemIDs:%v",
					uid, delHids, equipIDs, emblemIDs)
			}
		}
	}

	//处理上阵英雄
	uidInt, _ := strconv.ParseInt(uid, 10, 64)
	uidUint64 := uint64(uidInt)
	if len(updateHeroes) > 0 {
		redisClient.RopCl.SetSomeHeroBodyMCallSK(uidUint64, updateHeroes)
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			fixHeroDecomposeFailedCount++
			l4g.Errorf("fixHeroDecompose user %s update hero error: %s", uid, reply.Err)
			return
		}
	}

	//一个英雄换20个英雄经验
	fragmentCount := uint64(len(delHids) * 20)

	//处理碎片
	if dbUser.Bag != nil && len(dbUser.Bag.Fragments) > 0 {
		for _, id := range heroFragment {
			fragmentCount += uint64(dbUser.Bag.Fragments[id])
			delete(dbUser.Bag.Fragments, id)
		}
	}

	if updateUser {
		redisClient.RopDB.SetUserMCallSKs(uid, u.GetDBUser())
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			fixHeroDecomposeFailedCount++
			l4g.Errorf("fixHeroDecompose user %s save user error: %s", uid, reply.Err)
			return
		}
	}

	//返还养成资源
	retRes := make([]*cl.Resource, 0, 3)
	if len(delHids) > 0 {
		retRes = heroM.CalcHeroesDecomposeReturnRes(delHids)

		//删除2星英雄
		redisClient.RopCl.RemSomeHeroBodyMCallSK(uidUint64, delHids)
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			fixHeroDecomposeFailedCount++
			l4g.Errorf("fixHeroDecompose user %s del hero error: %s", uid, reply.Err)
			return
		}

		//更新装备数据
		if len(equipIDs) > 0 {
			equipDatas := make([]*cl.Equipment, 0, len(equipIDs))
			for _, id := range equipIDs {
				equip := equipM.Get(id)
				if equip == nil {
					continue
				}
				equip.SetHid(0)
				equipDatas = append(equipDatas, equip.Data.Clone())
				l4g.Infof("fixHeroDecompose. uid:%s, equip:%v", uid, equip.Data)
			}

			redisClient.RopCl.SetSomeEquipmentMCallSK(uidUint64, equipDatas)
			if reply := redisClient.Client.GetReply(); reply.Err != nil {
				fixHeroDecomposeFailedCount++
				l4g.Errorf("fixHeroDecompose user %s update equip error: %s", uid, reply.Err)
				return
			}
		}

		//更新符文数据
		if len(emblemIDs) > 0 {
			emblemDatas := make([]*cl.EmblemInfo, 0, len(emblemIDs))
			for _, id := range emblemIDs {
				emblem := emblemM.Get(id)
				if emblem == nil {
					continue
				}
				emblem.SetEmblemHid(0)
				emblemDatas = append(emblemDatas, emblem.Data.Clone())
				l4g.Infof("fixHeroDecompose. uid:%s, emblem:%v", uid, emblem.Data)
			}

			redisClient.RopCl.SetSomeEmblemInfoMCallSK(uidUint64, emblemDatas)
			if reply := redisClient.Client.GetReply(); reply.Err != nil {
				fixHeroDecomposeFailedCount++
				l4g.Errorf("fixHeroDecompose user %s update emblem error: %s", uid, reply.Err)
				return
			}
		}

	}

	//补发奖励
	if fragmentCount > 0 {
		retRes = append(retRes, &cl.Resource{
			Type:  uint32(common.RESOURCE_ITEM),
			Value: 10001,
			Count: uint32(fragmentCount),
		})
	}
	if len(retRes) > 0 {
		retRes = character.MergeResources(retRes)
		params := make([]string, 0, 2)
		params = append(params, "2-star Hero Compensation")
		params = append(params, "We have removed all the 2-star heroes from the game, the heroes that haven't been dismantled will be automatically turned into Hero EXP, here is the Hero EXP according to the dismantled heroes.")
		ret := sendMail(uid, redisClient, idFactory, character.MailIDGM, retRes, params)
		if !ret {
			fixHeroDecomposeFailedCount++
			l4g.Errorf("user: %s fixHeroDecompose: send mail failed.", uid)
		}
	}

	l4g.Infof("user: %s fixHeroDecompose success, updateUser:%v, updateHeroCount:%d, delHeroCount:%d",
		uid, updateUser, len(updateHeroes), len(delHids))
}

// 英雄是否上阵
func isInFormation(u *character.User, hid uint64) bool {
	for _, formation := range u.FormationManager().GetAll() {
		if formation == nil {
			continue
		}

		for _, team := range formation.Teams {
			for _, v := range team.Info {
				if v.Hid == hid {
					return true
				}
			}
		}
	}
	return false
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}

	return true
}

// 水晶成就数据转移
func fixCrystalAchieve(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	//重置水晶成就
	var changed bool
	if userData.Module2 != nil && userData.Module2.CrystalAchieve != nil {
		// l4g.Info("user %s oldAchieve:%+v", uid, userData.Module2.CrystalAchieve)
		if userData.Crystal == nil || userData.Crystal.Crystal == nil {
			userData.Crystal = &db.Crystal{
				Crystal: &cl.Crystal{},
			}
		}

		if userData.Crystal != nil && userData.Crystal.Crystal != nil {
			userData.Crystal.Crystal.Achieve = &cl.CrystalBlessingAchieve{
				ReceiveAwarded: userData.Module2.CrystalAchieve.ReceiveAwarded,
				TaskProgress:   userData.Module2.CrystalAchieve.TaskProgress,
			}

			userData.Module2.CrystalAchieve = nil
			changed = true
			// l4g.Info("user %s oldAchieve:%+v, newAchieve:%+v",
			// 	uid, userData.Module2.CrystalAchieve, userData.Crystal.Crystal.Achieve)
		}
	}

	if changed {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixCrystalAchieveFailedCount++
			l4g.Errorf("user %s save crystal error: %s %s", uid, userData, reply.Err)
			return
		}
		l4g.Info("user %s save fixCrystalAchieve success", uid)
	}
}

// 删除跨服竞技场个人数据
func delWrestlePersonal(uid string, r *redis.Redis) {
	//删除跨服竞技场日志
	r.RopCl.DelAllWrestleLogMCallSKs(uid)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delete Wrestle personal log error: %s", reply.Err)
		delWrestlePersonalFailedCount++
		return
	}

	//删除阵容数据
	r.RopCl.RemSomeFormationMCallSKs(uid, []uint32{199, 200})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delete Wrestle personal formation error: %s", reply.Err)
		delWrestlePersonalFailedCount++
		return
	}

	l4g.Info("user %s save delWrestlePersonal success", uid)
}

// 删除跨服竞技场公共数据
func delWrestle(r *redis.Redis) {
	r.RopDB.DelWrestleMCall()
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delWrestle.DelWrestleMCall error: %s", reply.Err)
		delWrestleFailedCount++
		return
	}

	r.RopDB.DelAllWrestleUserMCall()
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delWrestle.DelAllWrestleUserMCall error: %s", reply.Err)
		delWrestleFailedCount++
		return
	}
	l4g.Info("delWrestle success")
}

//	func fixDungeon(uid string, r *redis.Redis) {
//		r.RopDB.GetUserMCallSKs(uid)
//		userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
//		if err != nil {
//			l4g.Error("get user error: %s %s", uid, err)
//			//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
//		}
//
//		if userData == nil || userData.Base == nil {
//			fixDungeonFailedCount++
//			l4g.Error("user %s data corrupt", uid)
//			return
//		}
//
//		if userData.Module == nil || userData.Module.Dungeon == nil {
//			return
//		}
//
//		// 主线章节1-7
//		curDungeonID := userData.Module.Dungeon.SysId
//		if curDungeonID > 10107 {
//			return
//		}
//
//		if curDungeonID == 10107 {
//			userData.Module.Dungeon.SysId = 10106
//		}
//
//		r.RopDB.SetUserMCallSKs(uid, userData)
//		if reply := r.Client.GetReply(); reply.Err != nil {
//			fixDungeonFailedCount++
//			l4g.Errorf("user %s save pass error: %s %s", uid, userData, reply.Err)
//			return
//		}
//	}
var delGuidance = map[uint32]struct{}{
	610: {},
}

//var closeGuidance = map[uint32]struct{}{
//	30:  {},
//	40:  {},
//	50:  {},
//	60:  {},
//	70:  {},
//	80:  {},
//	81:  {},
//	90:  {},
//	100: {},
//	120: {},
//	130: {},
//	131: {},
//}

func fixGuidance(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Base == nil {
		fixGuidanceFailedCount++
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module == nil || userData.Module1 == nil || userData.Module.Dungeon == nil {
		return
	}

	if userData.Module1.Guidance == nil {
		userData.Module1.Guidance = &cl.Guidance{}
	}

	// 删除不使用了的groupID
	for i := 0; i < len(userData.Module1.Guidance.Finished); i++ {
		if _, exist := delGuidance[userData.Module1.Guidance.Finished[i]]; !exist {
			continue
		}
		userData.Module1.Guidance.Finished = append(userData.Module1.Guidance.Finished[:i], userData.Module1.Guidance.Finished[i+1:]...)
		i--
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGuidanceFailedCount++
		l4g.Errorf("user %s save pass error: %s %s", uid, userData, reply.Err)
		return
	}
}

//func fixPass(uid string, r *redis.Redis) {
//	r.RopDB.GetUserMCallSKs(uid)
//	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
//	if err != nil {
//		l4g.Error("get user error: %s %s", uid, err)
//		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
//	}
//
//	if userData == nil {
//		fixPassFailedCount++
//		l4g.Error("user %s data corrupt", uid)
//		return
//	}
//
//	if userData.Module2 == nil || userData.Module2.Passes == nil {
//		return
//	}
//
//	isChange := false
//	for _, task := range userData.Module2.Passes {
//		if progress, exist := task.TaskProgress[2801000]; exist {
//			if progress.Progress == 10107 {
//				progress.Progress = 10106
//				isChange = true
//			}
//		}
//	}
//
//	if isChange {
//		r.RopDB.SetUserMCallSKs(uid, userData)
//		if reply := r.Client.GetReply(); reply.Err != nil {
//			fixPassFailedCount++
//			l4g.Errorf("user %s save pass error: %s %s", uid, userData, reply.Err)
//			return
//		}
//	}
//}

func fixDispatch(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Module == nil || userData.Module.Dispatch == nil {
		l4g.Errorf("user %s data module dispatch corrupt", uid)
		return
	}

	var diamondCount uint32
	if len(userData.Module.Dispatch.Tasks) > 0 {
		for i := 0; i < len(userData.Module.Dispatch.Tasks); i++ {
			if count, exist := deleteDispatch[userData.Module.Dispatch.Tasks[i].SysId]; exist {
				diamondCount += count
				userData.Module.Dispatch.Tasks = append(userData.Module.Dispatch.Tasks[:i], userData.Module.Dispatch.Tasks[i+1:]...)
				i--
			}
		}
	}

	retRes := make([]*cl.Resource, 0, 1)
	if diamondCount > 0 {
		diamond := &cl.Resource{
			Type:  uint32(common.RESOURCE_DIAMOND),
			Count: diamondCount,
		}
		retRes = append(retRes, diamond)
	}

	if len(retRes) > 0 {
		retRes = character.MergeResources(retRes)
		params := make([]string, 0, 2)
		params = append(params, "Hero Convert-Bounty")
		params = append(params, "In-game hero conversion function has been removed, unearned Transformation Hourglass in Bounty Quests will be replaced with corresponding diamonds, here are the diamonds corresponding to your Transformation Hourglass.\n")
		ret := sendMail(uid, r, idFactory, character.MailIDGM, retRes, params)
		if !ret {
			fixDispatchTaskFailedCount++
			l4g.Errorf("user: %s fixDispatchTask: send mail failed.", uid)
		}
	}
	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixDispatchTaskFailedCount++
		l4g.Errorf("user %s fix dispatch error: %s %s", uid, userData, reply.Err)
		return
	}
}

func fixShop(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Module == nil || userData.Module.Shops == nil {
		l4g.Errorf("user %s shops not exist", uid)
		return
	}

	var change bool
	for _, v := range userData.Module.Shops {
		if v == nil {
			continue
		}
		for goodsId := range v.GoodsList {
			_, exist := deleteGoods[goodsId]
			if exist {
				delete(v.GoodsList, goodsId)
				change = true
			}
		}
		if v.SysId == 101 { // 优惠商店
			for goodsId := range v.GoodsList {
				if goodsId == 10102 {
					delete(v.GoodsList, 10102)
					change = true
				}
			}
		}
	}

	if change {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixShopFailedCount++
			l4g.Errorf("user %s save shop error: %s %s", uid, userData, reply.Err)
			return
		}
		l4g.Info("user %s save shop success", uid)
	}
}

func fixItem(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Bag == nil || userData.Bag.Items == nil {
		l4g.Errorf("user %s bag data corrupt", uid)
		return
	}

	var diamondCount uint32
	itemCount := userData.Bag.Items[10029]
	if itemCount > 0 {
		diamondCount = uint32(itemCount * 10)
		delete(userData.Bag.Items, 10029)
	}

	retRes := make([]*cl.Resource, 0, 1)
	if diamondCount > 0 {
		diamond := &cl.Resource{
			Type:  uint32(common.RESOURCE_DIAMOND),
			Count: diamondCount,
		}
		retRes = append(retRes, diamond)
	}

	if len(retRes) > 0 {
		retRes = character.MergeResources(retRes)
		params := make([]string, 0, 2)
		params = append(params, "Hero Convert-Inventory")
		params = append(params, "In-game hero conversion function has been removed, unused Transformation Hourglass in the players' inventory will be replaced with corresponding diamonds, here are the diamonds corresponding to your Transformation Hourglass.")
		ret := sendMail(uid, r, idFactory, character.MailIDGM, retRes, params)
		if !ret {
			fixItemFailedCount++
			l4g.Errorf("user: %s fixItem: send mail failed.", uid)
		}
	}
	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixItemFailedCount++
		l4g.Errorf("user %s fixItem error: %s %s", uid, userData, reply.Err)
		return
	}
}

func fixTrial(uid string, r *redis.Redis, now int64) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	var change bool
	if userData.Module != nil || userData.Module.Trials != nil {
		for _, v := range userData.Module.Trials {
			if v == nil {
				continue
			}
			v.OnHook = &cl.TrialOnHook{
				OnHookTm:          now,
				LastCalcTm:        now,
				LastRandomDropTm:  now,
				RandomDropStartTm: now,
			}
			change = true
		}
	}
	if change {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixTrialFailedCount++
			l4g.Errorf("user %s save trial error: %s %s", uid, userData, reply.Err)
			return
		}
		l4g.Info("user %s save trial success", uid)
	}
}

func fixGoddess(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Module3 == nil || userData.Module3.GoddessContractInfo == nil || userData.Module3.GoddessContractInfo.Goddess == nil {
		l4g.Errorf("user %s data GoddessContractInfo failed", uid)
		return
	}

	goddess, exist := userData.Module3.GoddessContractInfo.Goddess[3]
	if !exist {
		l4g.Info("user %s don't need repair goddess", uid)
		return
	}

	levelInfo := goxml.GetData().GoddessContractTrustInfoM.GetLevel(goddess.Exp)
	if levelInfo != nil {
		for i := uint32(1); i <= levelInfo.LevelId; i++ {
			suitInfo := goxml.GetData().GoddessContractSkinInfoM.GetSuit(goddess.Id, i)
			if suitInfo != nil {
				goddess.SuitIds = append(goddess.SuitIds, suitInfo.GoddessSkinId)
				userData.ModuleGlobalAttr.GoddessSuits = append(userData.ModuleGlobalAttr.GoddessSuits, suitInfo.GoddessSkinId)
				if suitInfo.AutoWear == 1 {
					goddess.EquipSuit = suitInfo.GoddessSkinId
				}
			}
		}
	}

	if goddess.RecoveryCount >= 1 {
		goddess.RecoveryCount = goxml.GetData().GoddessContractTreatInfoM.GoddessMaxTimes[3]
		goddess.RecoveryTime = 0
	}
	userData.Module3.GoddessContractInfo.Goddess[3] = goddess

	var expItemCount uint32
	goddess, exist = userData.Module3.GoddessContractInfo.Goddess[4]
	if exist {
		expItemCount = goddess.Exp / 20
		surplus := goddess.Exp % 20
		if surplus > 0 {
			expItemCount++
		}
		userData.ModuleGlobalAttr.GoddessContractExp -= goddess.Exp
		if len(goddess.SuitIds) > 0 {
			for _, SuitId := range goddess.SuitIds {
				for i, globalSuitId := range userData.ModuleGlobalAttr.GoddessSuits {
					if SuitId == globalSuitId {
						userData.ModuleGlobalAttr.GoddessSuits = append(userData.ModuleGlobalAttr.GoddessSuits[:i], userData.ModuleGlobalAttr.GoddessSuits[i+1:]...)
					}
				}
			}
		}
		userData.Module3.GoddessContractInfo.Goddess[4] = nil
	}

	if expItemCount > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Valkyrie Manor Adjustments")
		params = append(params, "We have replaced the hero Ellie with Iris in the Valkyrie Manor, players can now experience the new stories and outfits. Ellie's favor has been replaced with the corresponding Favor Gift Chests, please make sure to claim it.")
		award := &cl.Resource{
			Type:  uint32(common.RESOURCE_ITEM),
			Value: 10057,
			Count: expItemCount,
		}
		sendMail(uid, r, idFactory, character.MailIDGM, []*cl.Resource{award}, params)
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGoddessFailedCount++
		l4g.Errorf("user %s save goddess error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save goddess success", uid)
}

//func fixGuidanceAddRes(uid string, r *redis.Redis) {
//	//hero
//	r.RopCl.GetAllHeroBodyMCallSKs(uid)
//	//英雄
//	heroesData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
//	if err != nil {
//		l4g.Errorf("user: %s, get heroes error: %s", uid, err)
//	}
//
//	if len(heroesData) == 0 {
//		l4g.Errorf("user %s heroesData corrupt", uid)
//		fixGuidanceAddResFailedCount++
//		return
//	}
//
//	r.RopDB.GetUserMCallSKs(uid)
//	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
//	if err != nil {
//		l4g.Error("get user error: %s %s", uid, err)
//		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
//	}
//
//	if userData == nil || userData.Base == nil {
//		fixGuidanceAddResFailedCount++
//		l4g.Error("user %s data corrupt", uid)
//		return
//	}
//
//	if userData.Module == nil || userData.Module1 == nil || userData.Module.Dungeon == nil {
//		return
//	}
//
//	curDungeonID := userData.Module.Dungeon.SysId
//	if curDungeonID >= 10107 {
//		return
//	}
//
//	// 加经验
//	userData.Base.Exp += 1000
//
//	if userData.Bag == nil {
//		userData.Bag = &db.Bags{}
//	}
//	if userData.Bag.Items == nil {
//		userData.Bag.Items = make(map[uint32]uint32)
//	}
//
//	if userData.Bag.Fragments == nil {
//		userData.Bag.Fragments = make(map[uint32]uint32)
//	}
//
//	// 补发10张抽卡卷
//	userData.Bag.Items[10005] += 10
//	// 英雄经验
//	userData.Bag.Items[10001] += 1000
//
//	isExistXiaoDunNv := false
//	isExistXiaoJiSi := false
//	for _, hero := range heroesData {
//		if hero.SysId == 11992 {
//			isExistXiaoDunNv = true
//		} else if hero.SysId == 24992 {
//			isExistXiaoJiSi = true
//		}
//	}
//
//	if !isExistXiaoDunNv {
//		userData.Bag.Fragments[511992] += 30
//	}
//	if !isExistXiaoJiSi {
//		userData.Bag.Fragments[524992] += 30
//	}
//
//	r.RopDB.SetUserMCallSKs(uid, userData)
//	if reply := r.Client.GetReply(); reply.Err != nil {
//		fixGuidanceAddResFailedCount++
//		l4g.Errorf("user %s save pass error: %s %s", uid, userData, reply.Err)
//		return
//	}
//}

func fixWishList(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Module == nil || userData.Module.Summon == nil {
		l4g.Info("user %s don't need repair wishList", uid)
		return
	}

	summonData, exist := userData.Module.Summon[goxml.AdvancedSummon]
	if !exist || summonData.WishListMsg == nil {
		l4g.Info("user %s don't need repair wishList", uid)
		return
	}

	summonData.WishListMsg = nil

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixWishListFailedCount++
		l4g.Errorf("user %s save wishList error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save wishList success", uid)
}

func fixArtifactPoint(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Bag == nil || userData.Bag.Tokens == nil {
		l4g.Info("user %s don't need repair fixArtifactPoint", uid)
		return
	}

	num := userData.Bag.Tokens[9009]

	if num <= 0 {
		l4g.Info("user %s don't need repair fixArtifactPoint", uid)
		return
	}

	needNum := uint32(float32(num) / float32(800) * 60)

	if needNum > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Relic Points Adjustment")
		params = append(params, "After this update we'll remove the Relic Points, for players who already have relic points, we will exchange them for Epic Selectable Relic Shard Chests according to the corresponding proportion, please make sure to claim them!")
		award := &cl.Resource{
			Type:  uint32(common.RESOURCE_ITEM),
			Value: 30012,
			Count: needNum,
		}
		sendMail(uid, r, idFactory, character.MailIDGM, []*cl.Resource{award}, params)
	}

	delete(userData.Bag.Tokens, 9009)

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixArtifactPointFailedCount++
		l4g.Errorf("user %s save fixArtifactPoint error: %s %s", uid, userData, reply.Err)
		return
	}
	l4g.Info("user %s save fixArtifactPoint success", uid)
}

func fixPower(uid string, redisClient *redis.Redis) {
	u := base.LoadUser(uid, redisClient)
	fixDefensePower(uid, u, redisClient)
	fixTop5AndCrystalPower(uid, u, redisClient)
}

func fixDefensePower(uid string, u *character.User, redisClient *redis.Redis) {
	heroM := u.HeroManager()
	formationM := u.FormationManager()
	arenaDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_ARENA_DEFENSE))
	if arenaDefenseFormation != nil {
		var power int64
		for _, teamInfo := range arenaDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetArenaPower(power)
		l4g.Infof("newUserData:%s arenaPower: %d", uid, power)
	}

	wrestleDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE))
	if wrestleDefenseFormation != nil {
		var power int64
		for _, teamInfo := range wrestleDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetDefensePower(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE), power)
		l4g.Infof("newUserData:%s wrestlePower: %d", uid, power)
	}

	flowerDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE))
	if flowerDefenseFormation != nil {
		var power int64
		for _, teamInfo := range flowerDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetDefensePower(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE), power)
		l4g.Infof("newUserData:%s flowerPower: %d", uid, power)
	}
	redisClient.RopDB.SetUserMCallSKs(uid, u.GetDBUser())
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixPowerFailedCount++
		l4g.Errorf("[fixDefensePower] user %s save user error: %s", uid, reply.Err)
	}

}

func fixTop5AndCrystalPower(uid string, u *character.User, redisClient *redis.Redis) {
	userData := u.GetDBUser()
	heroM := u.HeroManager()
	// heroM.ReCalTop5()
	heroHeap := heroM.GetTop5Heros()
	l4g.Infof("oldUserData:%s top5%v", uid, userData.Base.Top5Heros)
	userData.Base.Top5Heros = make([]uint64, character.Top5HeroNum)
	var top5Power uint64
	for index, hero := range heroHeap {
		userData.Base.Top5Heros[index] = hero.GetData().Id
		top5Power += hero.GetHeroPower(u)
	}
	userData.Base.Power = int64(top5Power)
	userData.Base.PowerTm = time.Now().Unix()
	userData.Base.MaxPower = int64(top5Power)

	crystal := u.Crystal()
	if crystal.IsCrystalOpened() {
		userData.Base.CrystalPower = int64(crystal.CalCrystalPower())
	}

	//更新通用战力榜战力
	if userRank, exist := commonPowerRank[userData.Id]; exist {
		userPower := &rank.UserPower{}
		userPower.Load(userRank.Data)
		if crystal.IsCrystalOpened() {
			userPower.Power = uint64(userData.Base.CrystalPower)
		} else {
			userPower.Power = uint64(userData.Base.Power)
		}

		userPower.Tm = userData.Base.PowerTm
		userRank.Data = userPower.Save()
		l4g.Infof("update common power rank: %d power:%d", userPower.Uid, userPower.Power)
	}
	//更新全局战力
	if userRank, exist := globalRank[userData.Id]; exist {
		userRank.Power = userData.Base.Power
		userRank.PowerTm = userData.Base.PowerTm
		l4g.Infof("update global rank: %d power:%d", userRank.Id, userRank.Power)
	}

	redisClient.RopDB.SetUserMCallSKs(uid, userData)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixPowerFailedCount++
		l4g.Errorf("[top5] user %s save user error: %s", uid, reply.Err)
	}
	l4g.Infof("newUserData:%s top5%v top5Power:%d crystalPower:%d",
		uid, userData.Base.Top5Heros, userData.Base.Power, userData.Base.CrystalPower)
}

func getGlobalRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllGlobalRankMCall()
	if data, err := redisClient.RopDB.GetSomeGlobalRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get global rank data error: %s", err.Error())
		fixGlobalRankFailedCount++
		return
	} else {
		globalRank = data
	}
}

func getPowerRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllCommonRankMCallSK(uint64(goxml.PowerRankId))
	if data, err := redisClient.RopDB.GetSomeCommonRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get common power rank data error: %s", err.Error())
		fixPowerRankFailedCount++
		return
	} else {
		commonPowerRank = data
	}
}

func saveGlobalRank(redisClient *redis.Redis) {
	globalRanks := make([]*db.GlobalRank, 0, len(globalRank))
	for _, rank := range globalRank {
		globalRanks = append(globalRanks, rank)
	}
	redisClient.RopDB.SetSomeGlobalRankMCall(globalRanks)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save global rank data error: %s", reply.Err)
		fixGlobalRankFailedCount++
		return
	}
}

func savePowerRank(redisClient *redis.Redis) {
	commonPowerRanks := make([]*db.CommonRank, 0, len(commonPowerRank))
	for _, rank := range commonPowerRank {
		commonPowerRanks = append(commonPowerRanks, rank)
	}
	redisClient.RopDB.SetSomeCommonRankMCallSK(uint64(goxml.PowerRankId), commonPowerRanks)

	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save common power rank data error: %s", reply.Err)
		fixPowerRankFailedCount++
		return
	}
}

func fixForecast(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Info("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData.Module2 == nil || userData.Module2.Forecast == nil || userData.Module2.Forecast.TaskProgress == nil {
		l4g.Infof("%s get user don't need repair Forecast", uid)
		return
	}

	progress, exist := userData.Module2.Forecast.TaskProgress[1003000]
	if !exist {
		l4g.Infof("%s get user don't need repair Forecast", uid)
		return
	}

	if progress.Progress < 1 {
		l4g.Infof("%s get user don't need repair Forecast", uid)
		return
	}

	userData.Module2.Forecast.TaskProgress[2170001] = &cl.TaskTypeProgress{
		TaskTypeId: 2170001,
		Progress:   progress.Progress,
		Values:     progress.Values,
	}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixForecastFailedCount++
		l4g.Errorf("[fixForecast] user %s save user error: %+v", uid, reply.Err)
	}
}
