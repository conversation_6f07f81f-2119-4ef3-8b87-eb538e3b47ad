package cmd

import (
	"app/tools/repair/cmd/crossRedis"
	"fmt"
	"github.com/spf13/cobra"
	"strconv"

	v2170 "app/tools/repair/cmd/v2170"
)

var v2170Cmd = &cobra.Command{
	Use:   "v2170",
	Short: "1. 2.17.0初始化热度榜数据",
	Run: func(cmd *cobra.Command, args []string) {
		indexUint, err := strconv.Atoi(crossRedisIndex)
		if err != nil {
			panic(fmt.Sprintf("%s", err))
		}
		crossRedisClient := crossRedis.OpenCrossRedis(crossRedisAddr, uint32(indexUint))
		if crossRedisClient == nil {
			panic(fmt.Sprintf("open cross redis failed crossRedisAddr:%s crossRedisIndex:%d", crossRedisAddr, indexUint))
		}

		cmIndex, err := strconv.Atoi(crossMasterRedisIndex)
		if err != nil {
			panic(fmt.Sprintf("%s", err))
		}
		crossMasterRedisClient := crossRedis.OpenCrossRedis(crossMasterRedisAddr, uint32(cmIndex))
		if crossMasterRedisClient == nil {
			panic(fmt.Sprintf("open cross redis failed crossMasterRedisAddr:%s crossMasterRedisIndex:%d", crossMasterRedisAddr, cmIndex))
		}
		v2170.Run(cmd, args, redisClient, idFactory, crossRedisClient, cfgFromEtcd.ServerId)
	},
}

func init() {
	rootCmd.AddCommand(v2170Cmd)
}
