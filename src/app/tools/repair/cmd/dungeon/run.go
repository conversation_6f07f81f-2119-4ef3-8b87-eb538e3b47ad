package dungeon

import (
	"app/goxml"
	"app/tools/repair/cmd/redis"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	loadGoxml()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixUser(uid, redisClient)
	})
}

func loadGoxml() {
	dir := "../data"
	show := false
	goxml.GetData().ConfigInfoM.Load(dir, show)
}

func fixUser(uid string, redisClient *redis.Redis) {
	r := redisClient
	// base
	r.RopDB.GetUserMCallSKs(uid)

	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module1 == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	l4g.Infof("userData:%s infos+%v", uid, userData)
}
