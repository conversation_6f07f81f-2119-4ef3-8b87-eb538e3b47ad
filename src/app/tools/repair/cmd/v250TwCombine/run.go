package v250TwCombine

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/protos/in/cr"
	"app/protos/out/cl"
	"app/tools/repair/cmd/crossRedis"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"cmp"
	"flag"
	"slices"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath       = flag.String("V250TwCombineConfigData", "../data/", "service data path")
	fixPeakFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory,
	crossRedisClient *crossRedis.CrossRedis, partitionId uint32, sid uint64) {
	goxml.Load(*flagDataPath, false, false)
	l4g.Info("\n")

	peakSeasonRankList := makePeakSeasonRankList(crossRedisClient, partitionId, sid)
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixPeak(uid, redisClient, idFactory, peakSeasonRankList)
	})

	if fixPeakFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPeak Success!")
	} else {
		l4g.Infof("[FAILED] fixPeak Failed, need check error log! failedCount:%d", fixPeakFailedCount)
	}

	l4g.Info("finish repair")
}

type PeakSeasonRankList struct {
	Uid  string
	Rank uint32
}

// 巅峰赛季奖励，最后一名的名次
var LastPeakSeasonRank uint32 = 50

func makePeakSeasonRankList(crossRedisClient *crossRedis.CrossRedis, partition uint32, sid uint64) []*PeakSeasonRankList {
	fighters, err := crossRedisClient.RopCr.GetAllPeakFighterSK(partition)
	if err != nil {
		l4g.Errorf("makePeakSeasonRankList failed. error: %v, partition:%d", err, partition)
		fixPeakFailedCount++
		return nil
	}

	sortTmp := make([]*cr.PeakFighter, 0, 1)
	for _, v := range fighters {
		sortTmp = append(sortTmp, v)
	}

	list := make([]*PeakSeasonRankList, 0, 1)
	if len(sortTmp) > 0 {
		slices.SortFunc(sortTmp, func(i, j *cr.PeakFighter) int {
			if n := cmp.Compare(j.GetSeasonScore(), i.GetSeasonScore()); n != 0 {
				return n
			}
			if n := cmp.Compare(i.GetTime(), j.GetTime()); n != 0 {
				return n
			}
			return cmp.Compare(i.GetPos(), j.GetPos())
		})

		for i, fighter := range sortTmp {
			rank := uint32(i + 1)
			l4g.Infof("makePeakSeasonRankList all, rank:%d, sid:%d, uid:%d", rank, fighter.GetSid(), fighter.GetUid())
			if rank > LastPeakSeasonRank {
				break
			}

			if fighter.GetSid() != sid {
				continue
			}

			list = append(list, &PeakSeasonRankList{
				Uid:  strconv.FormatUint(fighter.GetUid(), 10),
				Rank: rank,
			})
			l4g.Infof("makePeakSeasonRankList current server, rank:%d, uid:%d", rank, fighter.GetUid())
		}
	}
	return list
}

func fixPeak(uid string, r *redis.Redis, idFactory *id.Factory, rankList []*PeakSeasonRankList) {
	var rank uint32
	for _, v := range rankList {
		if v.Uid == uid {
			rank = v.Rank
			break
		}
	}

	// 玩家不在发奖列表中
	if rank == 0 {
		return
	}

	_, err := r.RopDB.GetUserSKs(uid)
	if err != nil {
		l4g.Errorf("fixPeak: get user error, %s %s", uid, err)
		fixPeakFailedCount++
		return
	}

	awardData := goxml.GetData().PeakRankRewardInfoM.GetReward(goxml.PeakRewardTypeSeason, rank, 0)
	if len(awardData) == 0 {
		l4g.Errorf("fixPeak: no awardData, rank:%d, uid:%s", rank, uid)
		fixPeakFailedCount++
		return
	}

	params := []string{strconv.Itoa(int(rank))}
	ret := sendMail(uid, r, idFactory, character.MailIDPeakSeason, awardData, params)
	if !ret {
		fixPeakFailedCount++
		l4g.Errorf("fixPeak: send mail failed, uid:%s", uid)
		return
	}
	l4g.Info("user %s fixPeak success", uid)
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}
	return true
}
