package cmd

import (
	v260TrialAndMirage "app/tools/repair/cmd/v260TrialAndMirage"

	"github.com/spf13/cobra"
)

var v260TrialAndMirageCmd = &cobra.Command{
	Use:   "v260TrialAndMirage",
	Short: "2.6.0TrialAndMirage - 1.材料本关卡数据修复 2.个人boss关卡数据修复 3.个人boss清除指定玩家排行数据",
	Run: func(cmd *cobra.Command, args []string) {
		v260TrialAndMirage.Run(cmd, args, redisClient, idFactory)
	},
}

func init() {
	rootCmd.AddCommand(v260TrialAndMirageCmd)
}
