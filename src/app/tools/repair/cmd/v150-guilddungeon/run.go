package v150_guilddungeon

import (
	"app/goxml"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath = flag.String("V150GuildDungeonConfigData", "../data/", "service data path")

	fixGuildDungeonResetFailedCount uint32
	// lastWeekResetTime               int64 = 1699227000 // UTC+0 的 2023-11-05 23:30:00
	// lastSeasonResetTime             int64 = 1690759800 // UTC+0 的 2023-07-30 23:30:00
)

// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
// 注意：此脚本是专门给公会副本提前结算用的，1.5.0的其他脚本不要往这里添加 ！！！
func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()

	fixGuildDungeonReset(redisClient)

	l4g.Info("\n")
	if fixGuildDungeonResetFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGuildDungeonReset Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGuildDungeonReset Failed, need check error log! failedCount:%d\n", fixGuildDungeonResetFailedCount)
	}

	l4g.Info("finish repair")
}

func fixGuildDungeonReset(r *redis.Redis) {

	lastWeekResetTime := goxml.GetGuildDungeonWeeklyResetTime(time.Now().Unix()) - 2*util.WeekSecs
	lastSeasonResetTime := goxml.GetGuildDungeonSeasonResetTime(goxml.GetData(), time.Now().Unix()) -
		2*int64(goxml.GetData().GuildConfigInfoM.GetGuildDungeonSeasonRound())*util.WeekSecs

	resetData, err := r.RopDB.GetLogicGuildDungeonRefresh()

	if err != nil {
		l4g.Errorf("fixGuildDungeonReset: get guildDungeonReset error. err:%s", err)
		fixGuildDungeonResetFailedCount++
		return
	}

	resetData.LastWeeklyAwardTm = lastWeekResetTime
	resetData.LastSeasonAwardTm = lastSeasonResetTime

	calls := 0
	r.RopDB.SetLogicGuildDungeonRefreshMCall(resetData)
	calls++
	//r.RopCr.Append("hset", "guild_dungeon_season_id", "id", 2)
	//calls++

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Error("save guildDungeonReset error: %s %s", reply.Err)
			fixGuildDungeonResetFailedCount++
			return
		}
	}

	l4g.Info("fixGuildDungeonReset success")
}
