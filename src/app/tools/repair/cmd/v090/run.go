package v090

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/base"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"
	"fmt"
	"math"

	"gitlab.qdream.com/kit/sea/id"

	"github.com/spf13/cobra"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"

	"gitlab.qdream.com/kit/sea/math/rand"
)

// 新增水晶祝福任务类型
var newCrystalEventType uint32 = 4100

// 新增任务最小检查星级
var minCheckStar uint32 = 10

// 水晶任务类型id
var configEvents map[uint32]struct{}

var flagDataPath = flag.String("configData", "../data/", "service data path")

var (
	heroID2TaskTypeID            map[uint32]uint32
	fixCrystalFailedCount        uint32
	fixCrystalCount              uint32
	fixForestCount               uint32
	fixForestFailedCount         uint32
	fixGemCount                  uint32
	fixGemFailedCount            uint32
	fixHandbookFetterCount       uint32
	fixHandbookFetterFailedCount uint32
	fixArtifactCount             uint32
	fixArtifactFailedCount       uint32
	fixEmblemAndHeroCount        uint32
	fixEmblemAndHeroFailedCount  uint32
	fixTop5Count                 uint32
	fixTop5FailedCount           uint32
	fixMazeMapCount              uint32
	fixMazeMapFailedCount        uint32
	fixPassCount                 uint32
	fixPassFailedCount           uint32
	fixStarUpCostCount           uint32
	fixStarUpCostFailedCount     uint32
	fixDelOldEmblemCount         uint32
	fixDelOldEmblemFailedCount   uint32
	fixDelPushGiftCount          uint32
	fixDelPushGiftFailedCount    uint32
	fixGemRetResFailedCount      uint32
	fixEmblemRetResFailedCount   uint32
	fixMirageCount               uint32
	fixMirageFailedCount         uint32
	fixMirageRankCount           uint32
	fixMirageRankFailedCount     uint32
)

// 数据库中的密林数据
var forestData map[uint64]*cl.Forest

// 升星补偿，种族与道具的对应关系
var race2item = map[uint32]uint32{
	1: 10033, //帝国
	2: 10034, //森林
	3: 10035, //月影
	4: 10036, //神使
	5: 10037, //魔裔
}

// 升星补偿，星级与补偿道具数量的对应关系
var heroStar2ItemCount = map[uint32]uint32{
	6:  3,
	7:  3,
	8:  9,
	9:  14,
	10: 30,
	11: 42,
	12: 54,
	13: 84,
	14: 142,
}

// 神器铸造石
var artifForgeItem uint32 = 10009

// 神器突破石
var artifStrengthItem uint32 = 10010

// 神器抽卡券
var artifSummonItem uint32 = 10011

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	loadGoxml()
	prepareConfigData(redisClient)
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixCrystal(uid, redisClient)
		fixForest(uid, redisClient)
		fixGemData(uid, redisClient, idFactory)
		fixHandbookFetter(uid, redisClient)
		fixArtifactData(uid, redisClient, idFactory)
		fixEmblemAndHeroData(uid, redisClient)
		fixDelOldEmblem(uid, redisClient, idFactory) // 必须在 fixEmblemAndHeroData 之后执行
		fixPass(uid, redisClient)
		fixTop5(uid, redisClient) // 在所有养成的后面执行
		fixHeroStarUpCosts(uid, redisClient, idFactory)
		fixDelPushGift(uid, redisClient)
		fixMirage(uid, redisClient)
	})
	fixMazeMap(redisClient) // 删除迷宫模板
	fixMirageRank(redisClient)

	fmt.Printf("fixCrystal user count:%d\n", fixCrystalCount)
	fmt.Printf("fixForest user count:%d\n", fixForestCount)
	fmt.Printf("fixGem user count:%d\n", fixGemCount)
	fmt.Printf("fixHandbookFetterCount user count:%d\n", fixHandbookFetterCount)
	fmt.Printf("fixArtifactCount user count:%d\n", fixArtifactCount)
	fmt.Printf("fixEmblemAndHero user count:%d\n", fixEmblemAndHeroCount)
	fmt.Printf("fixTop5 user count:%d\n", fixTop5Count)
	fmt.Printf("fixMazeMap user count:%d\n", fixMazeMapCount)
	fmt.Printf("fixPass user count:%d\n", fixPassCount)
	fmt.Printf("fixHeroStarUpCosts user count:%d\n", fixStarUpCostCount)
	fmt.Printf("fixDelOldEmblem user count:%d\n", fixDelOldEmblemCount)
	fmt.Printf("fixDelPushGift user connt:%d\n", fixDelPushGiftCount)
	fmt.Printf("fixMirage user connt:%d\n", fixMirageCount)
	fmt.Printf("fixMirageRank user connt:%d\n", fixMirageRankCount)
	fmt.Printf("\n")
	if fixCrystalFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixCrystal Success!\n")
	} else {
		fmt.Printf("[FAILED] fixCrystal Failed, need check error log! failedCount:%d\n", fixCrystalFailedCount)
	}

	if fixForestFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixForest Success!\n")
	} else {
		fmt.Printf("[FAILED] fixForest Failed, need check error log! failedCount:%d\n", fixForestFailedCount)
	}

	if fixGemFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGem Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGem Failed, need check error log! failedCount:%d\n", fixGemFailedCount)
	}

	if fixHandbookFetterFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixHandbookFetter Success!\n")
	} else {
		fmt.Printf("[FAILED] fixHandbookFetter Failed, need check error log! failedCount:%d\n", fixHandbookFetterFailedCount)
	}

	if fixArtifactFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixArtifact Success!\n")
	} else {
		fmt.Printf("[FAILED] fixArtifact Failed, need check error log! failedCount:%d\n", fixArtifactFailedCount)
	}

	if fixEmblemAndHeroFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixEmblemAndHero Success!\n")
	} else {
		fmt.Printf("[FAILED] fixEmblemAndHero Failed, need check error log! failedCount:%d\n", fixEmblemAndHeroFailedCount)
	}

	if fixTop5FailedCount == 0 {
		fmt.Printf("[SUCCESS] fixTop5 Success!\n")
	} else {
		fmt.Printf("[FAILED] fixTop5 Failed, need check error log! failedCount:%d\n", fixTop5FailedCount)
	}

	if fixMazeMapFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMazeMap Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMazeMap Failed, need check error log! failedCount:%d\n", fixMazeMapFailedCount)
	}

	if fixPassFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixPass Success!\n")
	} else {
		fmt.Printf("[FAILED] fixPass Failed, need check error log! failedCount:%d\n", fixPassFailedCount)
	}

	if fixStarUpCostFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixHeroStarUpCosts Success!\n")
	} else {
		fmt.Printf("[FAILED] fixHeroStarUpCosts Failed, need check error log! failedCount:%d\n", fixStarUpCostFailedCount)
	}

	if fixDelOldEmblemFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixDelOldEmblem Success!\n")
	} else {
		fmt.Printf("[FAILED] fixDelOldEmblem Failed, need check error log! failedCount:%d\n", fixDelOldEmblemFailedCount)
	}

	if fixDelPushGiftFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixDelPushGift Success!\n")
	} else {
		fmt.Printf("[FAILED] fixDelPushGift Failed, need check error log! failedCount:%d\n", fixDelPushGiftFailedCount)
	}

	if fixGemRetResFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGemRetRes Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGemRetRes Failed, need check error log! failedCount:%d\n", fixGemRetResFailedCount)
	}

	if fixEmblemRetResFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixEmblemRetRes Success!\n")
	} else {
		fmt.Printf("[FAILED] fixEmblemRetRes Failed, need check error log! failedCount:%d\n", fixEmblemRetResFailedCount)
	}

	if fixMirageFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMirage Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMirage Failed, need check error log! failedCount:%d\n", fixMirageFailedCount)
	}

	if fixMirageRankFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixMirageRank Success!\n")
	} else {
		fmt.Printf("[FAILED] fixMirageRank Failed, need check error log! failedCount:%d\n", fixMirageRankFailedCount)
	}

	fmt.Printf("\nfinish repair\n")
}

func loadGoxml() {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
}

func prepareConfigData(r *redis.Redis) {
	heroID2TaskTypeID = make(map[uint32]uint32)
	events := goxml.GetData().TaskTypeInfoM.IndexEvent(newCrystalEventType)
	for _, v := range events {
		info := goxml.GetData().TaskTypeInfoM.Index(v.Id)
		if info == nil {
			l4g.Errorf("prepareConfigData. taskTypeInfo not exist, taskTypeId:%d", v.Id)
			return
		}

		heroID := info.Parameter1
		heroInfo := goxml.GetData().HeroInfoM.Index(heroID)
		if heroInfo == nil {
			l4g.Errorf("prepareConfigData. heroInfo not exist, heroID:%d", heroID)
			return
		}

		heroID2TaskTypeID[heroID] = v.Id
	}

	configEvents = make(map[uint32]struct{})
	for _, data := range goxml.GetData().CrystalAchieveInfoM.Datas {
		configEvents[data.Type] = struct{}{}
	}
	fmt.Printf("prepareConfigData. heroID2TaskTypeID:%v, configEvents:%v\n", heroID2TaskTypeID, configEvents)

	//加载密林数据
	r.RopCl.GetAllForestMCall()
	var err error
	forestData, err = r.RopCl.GetSomeForestByReply(r.Client.GetReply())
	if err != nil {
		// l4g.Errorf("prepareConfigData. load forest error: %s", err)
		panic(fmt.Sprintf("prepareConfigData. load forest error: %s", err))
	}
}

func fixCrystal(uid string, redisClient *redis.Redis) {
	r := redisClient
	//base
	r.RopDB.GetUserMCallSKs(uid)

	//base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("fixCrystal. get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("fixCrystal. user %s data corrupt", uid)
		fixCrystalFailedCount++
		return
	}

	if userData.Module2 == nil || userData.Module2.CrystalAchieve == nil || userData.Crystal == nil || userData.Crystal.Crystal == nil {
		return
	}

	//hero
	r.RopCl.GetAllHeroBodyMCallSKs(uid)

	//hero
	heroesData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("fixCrystal. get heroes error: %s %s", uid, err)
	}

	if len(heroesData) == 0 {
		l4g.Errorf("fixCrystal. user %s no heroesData", uid)
		fixCrystalFailedCount++
		return
	}

	//最终任务进度数据
	taskProcess := &cl.CrystalBlessingAchieve{
		ReceiveAwarded: make(map[uint32]bool),
		TaskProgress:   make(map[uint32]*cl.TaskTypeProgress),
	}

	//老任务数据处理
	oldProcessData := userData.Module2.CrystalAchieve.TaskProgress
	for id, data := range oldProcessData {
		if _, exist := configEvents[id]; exist {
			taskProcess.TaskProgress[id] = &cl.TaskTypeProgress{
				TaskTypeId: id,
				Progress:   data.Progress,
			}
		}
	}
	fmt.Printf("fixCrystal. user:%s oldProcessData:%v taskProcess:%v\n", uid, oldProcessData, taskProcess)

	//新增任务处理
	taskMaxStar := computeTaskMaxStar(heroesData)
	for id, star := range taskMaxStar {
		taskProcess.TaskProgress[id] = &cl.TaskTypeProgress{
			TaskTypeId: id,
			Progress:   uint64(star),
		}
	}
	fmt.Printf("fixCrystal. user:%s taskMaxStar:%v taskProcess:%v\n", uid, taskMaxStar, taskProcess)

	//更新水晶祝福任务进度数据
	userData.Module2.CrystalAchieve = taskProcess

	//重置水晶祝福养成数据
	// userData.Crystal.Crystal.Bless = &cl.CrystalBlessing{}

	r.RopDB.SetUserMCallSKs(uid, userData)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("fixCrystal. user:%s save user error: %s", uid, reply.Err)
		fixCrystalFailedCount++
		return
	}
	// fmt.Printf("fixCrystal. user:%s CrystalAchieve:%v Bless:%v\n", uid,
	// 	userData.Module2.CrystalAchieve, userData.Crystal.Crystal.Bless)
	fmt.Printf("fixCrystal. user:%s CrystalAchieve:%v \n", uid, userData.Module2.CrystalAchieve)

	fixCrystalCount++
}

func computeTaskMaxStar(heroesData map[uint64]*cl.HeroBody) map[uint32]uint32 {
	taskMaxStar := make(map[uint32]uint32)
	for _, hero := range heroesData {
		taskID, exist := heroID2TaskTypeID[hero.SysId]
		if !exist {
			continue
		}

		if hero.Star < minCheckStar {
			continue
		}

		if hero.Star > taskMaxStar[taskID] {
			taskMaxStar[taskID] = hero.Star
		}
	}
	return taskMaxStar
}

// 修复密林数据 - 将玩家最强英雄放置到采集位中
func fixForest(uidStr string, redisClient *redis.Redis) {
	// r := redisClient
	// uidInt, err := strconv.ParseInt(uidStr, 10, 64)
	// if err != nil {
	// 	l4g.Errorf("fixForest. user %s err: %s", uidStr, err)
	// 	fixForestFailedCount++
	// 	return
	// }

	// forest := forestData[uint64(uidInt)]
	// if forest == nil {
	// 	return
	// }

	// info :=  goxml.GetData().ForestLvInfoM.Index(forest.Level)
	// if info == nil {
	// 	l4g.Errorf("fixForest. user %s forestLvInfo not exist, level: %d", uidStr, forest.Level)
	// 	fixForestFailedCount++
	// 	return
	// }

	// slotNum := info.SlotNum
	// if slotNum == 0 {
	// 	return
	// }

	// //hero
	// r.RopCl.GetAllHeroBodyMCallSKs(uidStr)
	// //英雄
	// heroesData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	// if err != nil {
	// 	l4g.Errorf("fixForest. user: %s, get heroes error: %s", uidStr, err)
	// 	fixForestFailedCount++
	// 	return
	// }

	// collectHids, totalStar := getTopHidsAndTotalStar(heroesData, slotNum, uidStr)

	// //更新数据
	// forest.CollectHids = collectHids
	// if forest.PeacefulTree != nil && forest.PeacefulTree.Stage == 2 {
	// 	forest.PeacefulTree.CollectTotalStar = totalStar
	// }
	// if forest.PvpTree != nil && forest.PvpTree.Stage == 2 {
	// 	forest.PvpTree.CollectTotalStar = totalStar
	// }
	// fmt.Printf("fixForest. user:%s totalStar:%d forest:%v\n", uidStr, totalStar, forest)

	// r.RopCl.SetSomeForestMCall([]*cl.Forest{forest})
	// if reply := r.Client.GetReply(); reply.Err != nil {
	// 	l4g.Errorf("user: %d save forest error: %s", uidStr, reply.Err)
	// 	fixForestFailedCount++
	// 	return
	// }

	// fixForestCount++
}

func getTopHidsAndTotalStar(heroes map[uint64]*cl.HeroBody, slotNum uint32, uid string) ([]uint64, uint32) {
	return nil, 0
	// if len(heroes) == 0 {
	// 	return nil, 0
	// }

	// compareData := make([]*cl.HeroBody, 0, len(heroes))
	// minRequireRare :=  goxml.GetData().ForestConfigInfoM.GetCollectHeroMinRare()
	// for _, hero := range heroes {
	// 	info :=  goxml.GetData().HeroInfoM.Index(hero.SysId)
	// 	if info == nil {
	// 		l4g.Errorf("user: %d hero config not exist, sysID:%d", uid, hero.SysId)
	// 		continue
	// 	}

	// 	if info.Special == goxml.HeroSpecial {
	// 		continue
	// 	}

	// 	if info.Rare < minRequireRare {
	// 		continue
	// 	}

	// 	compareData = append(compareData, hero)
	// }

	// sortHeroes(compareData)
	// list := make([]uint64, 0, slotNum)
	// var count, totalStar uint32
	// for _, hero := range compareData {
	// 	if count >= slotNum {
	// 		break
	// 	}

	// 	list = append(list, hero.Id)
	// 	totalStar += hero.Star
	// 	count++
	// }

	// return list, totalStar
}

func isNewHeroBetter(newHero, oldHero *cl.HeroBody) bool {
	if newHero.Star > oldHero.Star {
		return true
	} else if newHero.Star == oldHero.Star {
		if newHero.SysId < oldHero.SysId {
			return true
		}
	}
	return false
}

func sortHeroes(heroes []*cl.HeroBody) {
	for i := 0; i < len(heroes)-1; i++ {
		for j := i + 1; j < len(heroes); j++ {
			if isNewHeroBetter(heroes[j], heroes[i]) {
				heroes[i], heroes[j] = heroes[j], heroes[i]
			}
		}
	}
}

func fixGemData(uid string, redisClient *redis.Redis, idFactory *id.Factory) {
	//gem
	redisClient.RopCl.GetAllGemInfoMCallSKs(uid)
	//宝石
	gemDatas, err := redisClient.RopCl.GetSomeGemInfoByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("get gem data error: %s %s", uid, err)
		fixGemFailedCount++
	}
	rd := rand.New(time.Now().UnixNano())
	var changes []*cl.GemInfo
	var (
		orangePlusGemCount uint32
		orangeGemCount     uint32
		redGemCount        uint32
		purpleGemCount     uint32
	)

	for _, gem := range gemDatas {
		gemInfo := goxml.GetData().GemInfoM.Index(gem.SysId)
		if gemInfo == nil {
			l4g.Errorf("gem data is nil. sysID: %d", gem.SysId)
			continue
		}
		if gem.TmpGemAttr != nil {
			gem.TmpGemAttr.AttrId = gem.TmpGemAttr.AttrId[:0]
		}
		if gem.GemAttr == nil {
			gem.GemAttr = &cl.GemAttr{}
		} else {
			gem.GemAttr.AttrId = gem.GemAttr.AttrId[:0]
		}

		// 宝石的基础属性
		attrs := goxml.GetData().GemAttrInfoM.GenBasicAttrs(rd, gemInfo.BasicAttrGroup)
		gem.GemAttr.AttrId = append(gem.GemAttr.AttrId, attrs...)
		if gemInfo.AdvanceAttrNum > 0 {
			attrs = goxml.GetData().GemAttrInfoM.GenAdvancedAttr(rd, gemInfo.AdvanceAttrGroup, gemInfo.AdvanceAttrNum, nil)
			gem.GemAttr.AttrId = append(gem.GemAttr.AttrId, attrs...)
		}

		changes = append(changes, gem)
		if gem.SysId == 8005 {
			orangePlusGemCount++
		} else if gem.SysId == 8006 {
			redGemCount++
		} else if gem.SysId == 8004 {
			orangeGemCount++
		} else if gem.SysId == 8003 {
			purpleGemCount++
		}
	}
	l4g.Infof("user: %s orangePlusGemCount: %d redGemCount: %d, orangeGemCount: %d, purpleGemCount: %d", uid, orangePlusGemCount, redGemCount, orangeGemCount, purpleGemCount)
	if len(changes) > 0 {
		redisClient.RopCl.SetSomeGemInfoMCallSKs(uid, changes)

		/************************REPLY*******************************/
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save gem error: %v", uid, reply.Err)
			fixGemFailedCount++
			return
		}
	}
	gemRes := genGemRes(orangePlusGemCount, redGemCount, uid)
	itemRes := genItemRes(orangeGemCount, redGemCount, orangePlusGemCount, purpleGemCount, uid)
	if !gemCompensateMail(uid, redisClient, idFactory, gemRes, itemRes) {
		fixGemRetResFailedCount++
	}

}

func genItemRes(orangeCount, redCount, orangePlusCount, purpleCount uint32, uid string) []*cl.Resource {
	awards := make([]*cl.Resource, 0, 2)
	itemNum1 := 2500*purpleCount + 6000*orangeCount + 9000*orangePlusCount + 18000*redCount
	itemNum2 := 40*purpleCount + 100*orangeCount + 150*orangePlusCount + 200*redCount
	if itemNum1 > 0 {
		awards = append(awards, genResource(uint32(common.RESOURCE_ITEM), 10008, itemNum1))
		l4g.Infof("user: %s item ID: %d count: %d", uid, 10008, itemNum1)
	}
	if itemNum2 > 0 {
		awards = append(awards, genResource(uint32(common.RESOURCE_ITEM), 10032, itemNum2))
		l4g.Infof("user: %s item ID: %d count: %d", uid, 10032, itemNum2)
	}

	return awards
}

func genGemRes(orangePlusGemCount, redCount uint32, uid string) []*cl.Resource {
	awards := make([]*cl.Resource, 0, 2)
	if orangePlusGemCount > 0 {
		awards = append(awards, genResource(uint32(common.RESOURCE_GEM), 8004, orangePlusGemCount*2))
		l4g.Infof("user: %s gem sysID: %d count: %d", uid, 8004, orangePlusGemCount*2)
	}
	if redCount > 0 {
		awards = append(awards, genResource(uint32(common.RESOURCE_GEM), 8005, redCount*2))
		l4g.Infof("user: %s gem sysID: %d count: %d", uid, 8005, redCount*2)
	}

	return awards
}

func genResource(typ, value, count uint32) *cl.Resource {
	return &cl.Resource{
		Type:  typ,
		Value: value,
		Count: count,
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user error: %s", uid, reply.Err)
		return false
	}

	return true
}

func gemCompensateMail(uid string, r *redis.Redis, idFactory *id.Factory, gemRes, itemRes []*cl.Resource) bool {
	if len(gemRes) > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Gem Upgrade Materials Returned 1")
		params = append(params, "We have made adjustments to the Gem Crafting function. Please accept the return of all corresponding upgrade materials.")
		ret := sendMail(uid, r, idFactory, character.MailIDGM, gemRes, params)
		if !ret {
			l4g.Errorf("user: %s gemCompensateMail: send gem resource failed.", uid)
			return ret
		}
	}

	if len(itemRes) > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Gem Upgrade Materials Returned 2")
		params = append(params, "We have made adjustments to the Gem Crafting function. Please accept the return of all corresponding upgrade materials.")
		ret := sendMail(uid, r, idFactory, character.MailIDGM, itemRes, params)
		if !ret {
			l4g.Errorf("user: %s gemCompensateMail: send item resource failed.", uid)
			return ret
		}
	}

	return true
}

func fixEmblemAndHeroData(uid string, redisClient *redis.Redis) {
	//hero
	redisClient.RopCl.GetAllHeroBodyMCallSKs(uid)
	//heroDatas, err := redisClient.RopCl.GetSomeHeroBodyByReply(redisClient.Client.GetReply())
	//if err != nil {
	//	l4g.Errorf("get user heroes error: %s %s", uid, err)
	//	fixEmblemAndHeroFailedCount++
	//}
	//
	////emblem
	//redisClient.RopCl.GetAllEmblemInfoMCallSKs(uid)
	//emblemDatas, err := redisClient.RopCl.GetSomeEmblemInfoByReply(redisClient.Client.GetReply())
	//if err != nil {
	//	l4g.Errorf("get user heroes error: %s %s", uid, err)
	//	fixEmblemAndHeroFailedCount++
	//}

	var (
		newHeroes  []*cl.HeroBody
		newEmblems []*cl.EmblemInfo
	)
	//for _, hero := range heroDatas {
	//	for slot, emblemID := range hero.Emblem {
	//		if emblem, exist := emblemDatas[emblemID]; exist {
	//			feature :=  goxml.GetData().EmblemInfoM.GetFeature(emblem.SysId)
	//			job :=  goxml.GetData().HeroInfoM.GetJob(hero.SysId)
	//			if (job == 0 && job == feature) || job != feature {
	//				delete(hero.Emblem, slot)
	//				newHeroes = append(newHeroes, hero)
	//				emblem.Hid = 0
	//				newEmblems = append(newEmblems, emblem)
	//			}
	//		}
	//	}
	//}

	if len(newHeroes) > 0 {
		redisClient.RopCl.SetSomeHeroBodyMCallSKs(uid, newHeroes)

		/************************REPLY*******************************/
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s", uid, reply.Err)
			return
		}

		//l4g.Infof("update hero data success!")
	}

	if len(newEmblems) > 0 {
		redisClient.RopCl.SetSomeEmblemInfoMCallSKs(uid, newEmblems)

		/************************REPLY*******************************/
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s", uid, reply.Err)
			return
		}

		//l4g.Infof("update emblem data success!")
	}

	fixEmblemAndHeroCount++
}

func fixTop5(uid string, redisClient *redis.Redis) {
	u := base.LoadUser(uid, redisClient)
	userData := u.GetDBUser()
	heroM := u.HeroManager()
	// heroM.ReCalTop5()
	heroHeap := heroM.GetTop5Heros()
	fmt.Printf("oldUserData:%s top5%v", uid, userData.Base.Top5Heros)
	userData.Base.Top5Heros = make([]uint64, character.Top5HeroNum)
	var totalPower uint64
	for index, hero := range heroHeap {
		userData.Base.Top5Heros[index] = hero.GetData().Id
		totalPower += hero.GetHeroPower(u)
	}
	//userData.Base.Power = int64(totalPower)
	userData.Base.MaxPower = int64(totalPower)

	redisClient.RopDB.SetUserMCallSKs(uid, userData)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixTop5FailedCount++
		l4g.Errorf("[top5] user %s save user error: %s", uid, reply.Err)
	} else {
		fixTop5Count++
	}

	fmt.Printf("newUserData:%s top5%v", uid, userData.Base.Top5Heros)
}

func fixMazeMap(r *redis.Redis) {
	// mazePlayer
	r.RopCl.GetAllMazeMapMCall()
	data, err := r.RopCl.GetSomeMazeMapByReply(r.Client.GetReply())
	if err != nil {
		fixMazeMapFailedCount++
		l4g.Errorf("get maze basic map data error: %+v", err)
	}

	deletes := make([]uint32, 0, len(data))
	for id := range data {
		deletes = append(deletes, id)
	}

	if len(deletes) > 0 {
		r.RopCl.RemSomeMazeMapMCall(deletes)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixMazeMapFailedCount++
			l4g.Errorf("delete mazeMap error: %s", reply.Err)
			return
		}

		fixMazeMapCount++
	}
}

// 战令去掉的任务id (pass_task_info.xml表的id)
var passTaskIDs = []uint32{10002, 10004, 10006, 10008, 10010, 10012, 10014, 10016, 10018, 10020, 20009, 20014, 20016, 20018, 20019}

func fixPass(uid string, r *redis.Redis) {
	// user
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		fixPassFailedCount++
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module2 == nil {
		fixPassCount++
		return
	}

	isChange := false
	for _, pass := range userData.Module2.Passes {
		for _, id := range passTaskIDs {
			if _, exist := pass.Receive[id]; exist {
				isChange = true
				delete(pass.Receive, id)
			}
			if pass.ActivePass == nil {
				continue
			}
			if _, exist := pass.ActivePass.ReceivePoint[id]; exist {
				isChange = true
				delete(pass.ActivePass.ReceivePoint, id)
			}
		}
	}

	if isChange {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixPassFailedCount++
			l4g.Errorf("user %s save pass error: %s %s", uid, userData, reply.Err)
			return
		}
	}
	fixPassCount++
}

// 添加升星回退数据
func fixHeroStarUpCosts(uid string, redisClient *redis.Redis, idFactory *id.Factory) {
	l4g.Infof("user: %s, start fixHeroStarUpCosts", uid)
	//hero
	redisClient.RopCl.GetAllHeroBodyMCallSKs(uid)
	//英雄
	heroesData, err := redisClient.RopCl.GetSomeHeroBodyByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("user: %s, get heroes error: %s", uid, err)
	}

	if len(heroesData) == 0 {
		l4g.Errorf("user %s heroesData corrupt", uid)
		fixStarUpCostFailedCount++
		return
	}

	var changes []*cl.HeroStarUpCosts
	compensate := make(map[uint32]uint32)
	for _, hero := range heroesData {
		info := goxml.GetData().HeroInfoM.Index(hero.SysId)
		if info == nil {
			l4g.Errorf("user: %s, hero data is nil. sysID: %d", uid, hero.SysId)
			fixStarUpCostFailedCount++
			continue
		}

		if hero.Star <= info.Star {
			continue
		}

		if info.Star < goxml.HeroCanBackMinInitStar || info.Back != goxml.HeroCanBack {
			continue
		}

		l4g.Infof("user: %s, start calcCostHeroSysIDs hid:%d sysID:%d star:%d", uid, hero.Id, hero.SysId, hero.Star)
		var heroInfo *goxml.HeroInfoExt
		starUpCosts := &cl.HeroStarUpCosts{
			Id:             hero.Id,
			Star5Costs:     make(map[uint32]uint32, 4),
			OtherStarCosts: make([]*cl.StarUpCosts, 0, 3),
		}

		for i := hero.Star - 1; i >= info.Star; i-- {
			sInfo := goxml.GetData().HeroStarInfoM.Index(i, info.Rare, info.Race)
			if sInfo == nil {
				l4g.Errorf("user: %s, starInfo not exist. sysID:%d star:%d", uid, hero.SysId, i)
				fixStarUpCostFailedCount++
				return
			}

			for _, v := range sInfo.HeroCosts {
				if v.Type == goxml.HeroStarUpCostTypeSelf {
					heroInfo = info
				} else if v.Type == goxml.HeroStarUpCostTypeRace {
					heroInfo = goxml.GetData().HeroInfoM.GetSpecialHeroByRaceAndStar(info.Race, v.Star)
				} else if v.Type == goxml.HeroStarUpCostTypeAny {
					heroInfo = goxml.GetData().HeroInfoM.GetSpecialHeroByRaceAndStar(goxml.RaceNone, goxml.HeroNoRaceDefaultStar)
				} else {
					l4g.Errorf("user: %s, heroCost type illegal. sysID:%d type:%d", uid, hero.SysId, v.Type)
					fixStarUpCostFailedCount++
					return
				}

				if heroInfo.Star == 5 {
					starUpCosts.Star5Costs[heroInfo.Id] += v.Count
				} else {
					starUpCosts.OtherStarCosts = append(starUpCosts.OtherStarCosts, &cl.StarUpCosts{
						SysId: heroInfo.Id,
						Star:  heroInfo.Star,
					})
				}
			}
		}
		changes = append(changes, starUpCosts)

		//处理升星补偿数据
		itemID := race2item[info.Race]
		itemCount := heroStar2ItemCount[hero.Star]
		if itemID == 0 || itemCount == 0 {
			l4g.Errorf("user: %s, compensate err. race:%d star:%d", uid, info.Race, hero.Star)
			fixStarUpCostFailedCount++
			return
		}
		compensate[itemID] += itemCount
	}

	if len(changes) > 0 {
		redisClient.RopCl.SetSomeHeroStarUpCostsMCallSKs(uid, changes)
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user: %s save heroStarUpCosts error: %v", uid, reply.Err)
			fixStarUpCostFailedCount++
			return
		}
	}

	if len(compensate) > 0 {
		awards := make([]*cl.Resource, 0, len(compensate))
		for itemID, itemCount := range compensate {
			awards = append(awards, genResource(uint32(common.RESOURCE_ITEM), itemID, itemCount))
		}
		params := make([]string, 0, 2)
		params = append(params, "Hero Upgrade Materials Returned")
		params = append(params, "We have made adjustments to the Hero Advancement function. Please accept the return of all corresponding upgrade materials.")
		if !sendMail(uid, redisClient, idFactory, character.MailIDGM, awards, params) {
			fixStarUpCostFailedCount++
			return
		}
	}

	fmt.Printf("fixHeroStarUpCosts user: %s, finish fixHeroStarUpCosts. changes:%v compensate:%v \n",
		uid, changes, compensate)
	fixStarUpCostCount++
}

func fixDelOldEmblem(uid string, r *redis.Redis, idFactory *id.Factory) {
	//emblem
	r.RopCl.GetAllEmblemInfoMCallSKs(uid)
	emblemDatas, err := r.RopCl.GetSomeEmblemInfoByReply(r.Client.GetReply())
	if err != nil {
		fixDelOldEmblemFailedCount++
		l4g.Errorf("get user heroes error: %s %s", uid, err)
	}

	var (
		deletes    []uint64
		retRes     []*cl.Resource
		logEmblems []cl.EmblemInfo
		logRetRes  []cl.Resource
	)
	stageLevelPool := make(map[uint32]uint32)
	for _, emblemData := range emblemDatas {
		if emblemInfo := xmlcfg.OldEmblem.Index(emblemData.SysId); emblemInfo != nil {
			// 符文有养成的，先重生，把养成资源返回给玩家，再删除
			emblem := &character.Emblem{
				Data: emblemData,
			}
			retRes = append(retRes, emblemDevelopRes(emblem, emblemInfo)...)
			deletes = append(deletes, emblemData.Id)
			logEmblems = append(logEmblems, *emblemData)

			stageLevelPool[emblemInfo.Stage] += 1
		}
	}
	l4g.Infof("user: %s delete emblem num: %d, emblems: %+v", uid, len(logEmblems), logEmblems)
	if len(retRes) > 0 {
		for _, res := range retRes {
			logRetRes = append(logRetRes, *res)
		}
		// 把返还的资源加到玩家身上
		if !award(uid, r, retRes) {
			l4g.Errorf("user: %s revive emblem failed. ", uid)
			fixDelOldEmblemFailedCount++
		} else {
			l4g.Infof("user: %s return resource: %+v", uid, logRetRes)
		}
	}

	if len(deletes) > 0 {
		r.RopCl.RemSomeEmblemInfoMCallSKs(uid, deletes)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixDelOldEmblemFailedCount++
			l4g.Errorf("user %s save user error: %+v %v", uid, deletes, reply.Err)
			return
		}
	}

	// 处理邮件中的已删掉的符文
	if !handleMail(uid, r) {
		fixDelOldEmblemFailedCount++
		l4g.Errorf("user: %s handle mail's emblem failed. ", uid)
		return
	}

	fixDelOldEmblemCount++

	emblemCompensateMail(uid, r, idFactory, stageLevelPool)
}

func emblemDevelopRes(e *character.Emblem, emblemInfo *xmlcfg.OldEmblemInfo) []*cl.Resource {
	var capNeed uint32
	if e.Data.Level > character.EmblemInitialStrengthLevel {
		capNeed += (e.Data.Level - character.EmblemInitialStrengthLevel) * goxml.EmblemLevelupResCostCount
	}
	//if e.Data.Blessing.Level > character.EmblemInitialBlessingLevel {
	//	capNeed += (e.Data.Blessing.Level - character.EmblemInitialBlessingLevel + 1) * goxml.EmblemLevelupResCostCount
	//}
	retRes := make([]*cl.Resource, 0, capNeed)
	retRes = append(retRes, retStrengthRes(emblemInfo.LevelIndex, e)...)

	// 之前等级的祝福资源
	//for i := character.EmblemInitialBlessingLevel; i < e.Data.Blessing.Level; i++ {
	//	blessingCfg := xmlcfg.OldEmblemBless.Index(emblemInfo.BlessingIndex, i)
	//	if blessingCfg != nil {
	//		retRes = append(retRes, blessingCfg.TotalCosts...)
	//	}
	//}
	// 当前等级的祝福资源
	//if curCount := curBlessingCount(e, emblemInfo); curCount > 0 {
	//	blessingCfg := xmlcfg.OldEmblemBless.Index(emblemInfo.BlessingIndex, e.Data.Blessing.Level)
	//	if blessingCfg != nil {
	//		for _, cost := range blessingCfg.Costs {
	//			retRes = append(retRes, &cl.Resource{
	//				Type:  cost.Type,
	//				Value: cost.Value,
	//				Count: cost.Count * curCount,
	//			})
	//		}
	//	}
	//}
	return retRes
}

func curBlessingCount(e *character.Emblem, emblemInfo *xmlcfg.OldEmblemInfo) uint32 {
	//blessingCfg := xmlcfg.OldEmblemBless.Index(emblemInfo.BlessingIndex, e.Data.Blessing.Level)
	//if blessingCfg == nil {
	//	return 0
	//}
	//lastLimit := make(map[uint32]uint32)
	//if e.Data.Blessing.Level > character.EmblemInitialBlessingLevel {
	//	lastCfg := xmlcfg.OldEmblemBless.Index(emblemInfo.BlessingIndex, e.Data.Blessing.Level-1)
	//	if lastCfg != nil {
	//		for attr, limit := range lastCfg.AttrLimit {
	//			lastLimit[attr] = limit.GrewLimit
	//		}
	//	}
	//}
	var retCount uint32
	//for k, v := range e.Data.Blessing.AttrProgress {
	//	curLimit := blessingCfg.AttrLimit[k]
	//	if curLimit == nil {
	//		continue
	//	}
	//	if v > lastLimit[k] {
	//		retCount += (v - lastLimit[k]) / curLimit.GrewAdd
	//	}
	//}
	return retCount
}

func retStrengthRes(levelIndex uint32, e *character.Emblem) []*cl.Resource {
	var retRes []*cl.Resource

	for i := character.EmblemInitialStrengthLevel; i < e.Data.Level; i++ {
		levelCfg := xmlcfg.OldEmblemLevel.Index(levelIndex, i)
		if levelCfg != nil {
			retRes = append(retRes, levelCfg.Costs...)
		}
	}

	return retRes
}

func emblemCompensateMail(uid string, r *redis.Redis, idFactory *id.Factory, stageLevelPool map[uint32]uint32) {
	awards := make([]*cl.Resource, 0, len(stageLevelPool))
	logSendAwards := make([]cl.Resource, 0, len(stageLevelPool))
	l4g.Infof("uid: %s emblemCompensateMail stageLevelPool: %+v", uid, stageLevelPool)
	for stageLevel, count := range stageLevelPool {
		var sysID uint32
		switch stageLevel {
		case 1:
			sysID = 30033
		case 2:
			sysID = 30034
		case 3:
			sysID = 30035
		case 4:
			sysID = 30036
		case 5:
			sysID = 30037
		case 6:
			sysID = 30038
		case 7:
			sysID = 30039
		case 8:
			sysID = 30040
		case 9:
			sysID = 30041
		case 10:
			sysID = 30042
		case 11:
			sysID = 30043
		case 12:
			sysID = 30044
		case 13:
			sysID = 30045
		case 14:
			sysID = 30046
		case 15:
			sysID = 30047
		}

		if sysID > 0 {
			res := genResource(uint32(common.RESOURCE_ITEM), sysID, count)
			awards = append(awards, res)
			logSendAwards = append(logSendAwards, *res)
		}
	}

	if len(awards) > 0 {
		params := make([]string, 0, 2)
		params = append(params, "Rune Upgrade Materials Returned")
		params = append(params, "We have made adjustments to the Rune Evolution function. Please accept the return of all corresponding upgrade materials.")
		if !sendMail(uid, r, idFactory, character.MailIDGM, awards, params) {
			fixEmblemRetResFailedCount++
		}

		l4g.Infof("user: %s send resource mail, res: %+v", uid, logSendAwards)
	}
}

func handleMail(uid string, r *redis.Redis) bool {
	r.RopCl.GetAllMailMCallSKs(uid)
	mailsData, err := r.RopCl.GetSomeMailByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user mails error: %s %s", uid, err)
	}

	var (
		changes     []*cl.Mail
		mailDeletes []uint64
		logEmblems  []cl.Resource
	)

	for _, mail := range mailsData {
		isChange := false
		for i := 0; i < len(mail.Awards); i++ {
			res := mail.Awards[i]
			if res.Type == uint32(common.RESOURCE_EMBLEM) {
				if emblemInfo := goxml.GetData().OldEmblemInfoM.Index(res.Value); emblemInfo != nil {
					logEmblems = append(logEmblems, *res.Clone())
					isChange = true
					mail.Awards = append(mail.Awards[:i], mail.Awards[i+1:]...)
					i--
				}
			}
		}

		if !isChange {
			continue
		}

		if len(mail.Awards) == 0 {
			mailDeletes = append(mailDeletes, mail.Id)
		} else {
			changes = append(changes, mail)
		}
	}

	if len(logEmblems) > 0 {
		l4g.Infof("user: %s from mail to delete emblem num: %d, res: %+v", uid, len(logEmblems), logEmblems)
	}

	calls := 0
	if len(changes) > 0 {
		r.RopCl.SetSomeMailMCallSKs(uid, changes)
		calls++
	}
	if len(mailDeletes) > 0 {
		r.RopCl.RemSomeMailMCallSKs(uid, mailDeletes)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s", uid, reply.Err)
			return false
		}
	}

	return true
}

func award(uid string, r *redis.Redis, retRes []*cl.Resource) bool {
	// user
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Bag == nil {
		l4g.Error("user %s data corrupt", uid)
		return false
	}

	if userData.Bag.Items == nil {
		userData.Bag.Items = make(map[uint32]uint32)
	}

	isChange := false
	for _, res := range retRes {
		if res.Type != uint32(common.RESOURCE_ITEM) {
			continue
		}
		userData.Bag.Items[res.Value] += res.Count
		isChange = true
	}

	if isChange {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s %s", uid, userData, reply.Err)
			return false
		}
	}

	return true
}

func fixHandbookFetter(uid string, redisClient *redis.Redis) {
	r := redisClient
	// base
	r.RopDB.GetUserMCallSKs(uid)

	//1. 修改图鉴(羁绊全部删除)

	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s ", uid, err)
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		fixHandbookFetterFailedCount++
		return
	}

	globalAttr := userData.ModuleGlobalAttr

	if globalAttr != nil && globalAttr.Handbooks != nil {
		if heroHandbook := globalAttr.Handbooks[uint32(common.HANDBOOK_HT_HERO)]; heroHandbook != nil {
			for i := 0; i < len(heroHandbook.Handbooks); i++ {
				if heroHandbook.Handbooks[i] == nil {
					continue
				}
				if heroHandbook.Handbooks[i].SysId == 41003 {
					heroHandbook.Handbooks = append(heroHandbook.Handbooks[:i], heroHandbook.Handbooks[i+1:]...)
					i--
					continue
				}
				if heroHandbook.Handbooks[i].SysId == 51003 {
					heroHandbook.Handbooks = append(heroHandbook.Handbooks[:i], heroHandbook.Handbooks[i+1:]...)
					i--
				}
			}
			// 如果玩家头像是这两个英雄的头像，换为默认头像
			if userData.Base.BaseId == 403 || userData.Base.BaseId == 503 {
				userData.Base.BaseId = 1
			}
		}

		if globalAttr.Handbooks[uint32(common.HANDBOOK_HT_FETTER)] != nil {
			delete(globalAttr.Handbooks, uint32(common.HANDBOOK_HT_FETTER))
		}
		r.RopDB.SetUserMCallSKs(uid, userData)
		/************************REPLY*******************************/
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %v", uid, reply.Err)
			return
		}
		fixHandbookFetterCount++
		l4g.Infof("uid: %s delete fetter handbook data ok!", uid)
	}
}

func fixArtifactData(uid string, redisClient *redis.Redis, idFactory *id.Factory) {

	r := redisClient

	haveError := false

	r.RopCl.GetAllArtifactMCallSKs(uid)

	artifactDatas, err := r.RopCl.GetSomeArtifactByReply(r.Client.GetReply())
	if err != nil {
		fixArtifactFailedCount++
		l4g.Errorf("get user artifacts error: %s %s", uid, err)
	}
	var changes []*cl.Artifact
	needDelItems := make(map[uint32]uint32)
	for _, artifactData := range artifactDatas {
		if artifactData.ForgeLv == 0 {
			continue
		}
		rare := goxml.GetData().ArtifactInfoM.GetRare(artifactData.SysId)
		if rare != 0 {
			res := goxml.GetData().ArtifactForgeInfoM.GetReviveRes(rare, artifactData.ForgeLv)
			for _, award := range res {
				if award.Type != uint32(common.RESOURCE_ITEM) {
					continue
				}
				if award.Value == artifForgeItem { // 铸造石
					needDelItems[artifForgeItem] += award.Count
				}
				if award.Value == artifStrengthItem { // 突破石
					needDelItems[artifStrengthItem] += award.Count
				}
			}
		}
		artifactData.ForgeLv = 0
		changes = append(changes, artifactData)
	}
	l4g.Infof("user: %s changed artifacts:%+v", uid, changes)
	if len(changes) > 0 {
		redisClient.RopCl.SetSomeArtifactMCallSKs(uid, changes)

		/************************REPLY*******************************/
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save artifact error: %v", uid, reply.Err)
			haveError = true
		}
	}

	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		haveError = true
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		haveError = true
	}

	if userData.Bag != nil && userData.Bag.Items != nil {
		item09 := userData.Bag.Items[artifForgeItem]
		item10 := userData.Bag.Items[artifStrengthItem]
		if item09 > 0 || item10 > 0 {
			l4g.Infof("user:%s  bag item10009 num: %d item10010: %d", uid, item09, item10)
			needDelItems[artifForgeItem] += item09
			needDelItems[artifStrengthItem] += item10
			delete(userData.Bag.Items, artifForgeItem)
			delete(userData.Bag.Items, artifStrengthItem)
			r.RopDB.SetUserMCallSKs(uid, userData)
			/************************REPLY*******************************/
			if reply := redisClient.Client.GetReply(); reply.Err != nil {
				l4g.Errorf("user %s save user error: %s", uid, reply.Err)
				haveError = true
			}
		}
	}

	if !handleArtifactMail(uid, r, needDelItems) {
		l4g.Errorf("user: %s handle artifactForge item Mail error", uid)
		haveError = true
	}

	allCount := needDelItems[artifForgeItem]*2 + needDelItems[artifStrengthItem]*15
	if allCount > 480000 {
		allCount = 480000
	}
	awardCount := math.Ceil(float64(allCount) / float64(600))
	if awardCount > 0 {
		mailAwards := make([]*cl.Resource, 0, 1)
		mailAwards = append(mailAwards, &cl.Resource{
			Type:  uint32(common.RESOURCE_ITEM),
			Value: artifSummonItem,
			Count: uint32(awardCount),
		})
		l4g.Infof("uid: %s mailAwards item11 num: %d", uid, uint32(awardCount))
		params := make([]string, 0, 2)
		params = append(params, "Relic Upgrade Materials Returned")
		params = append(params, "We have made adjustments to the Relic Ascension function. Please accept the return of all corresponding upgrade materials.")
		if !sendMail(uid, r, idFactory, character.MailIDGM, mailAwards, params) {
			l4g.Errorf("user: %s fix Artifact sendMail error", uid)
			haveError = true
		}
	}
	if haveError {
		fixArtifactFailedCount++
	} else {
		fixArtifactCount++
		l4g.Infof("uid: %s fixArtifact data ok!", uid)
	}
}

func handleArtifactMail(uid string, r *redis.Redis, needDelItems map[uint32]uint32) bool {
	r.RopCl.GetAllMailMCallSKs(uid)
	mailsData, err := r.RopCl.GetSomeMailByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user mails error: %s %s", uid, err)
		return false
	}

	var (
		changes     []*cl.Mail
		mailDeletes []uint64
		logArtifact []cl.Resource
	)

	for _, mailData := range mailsData {
		if mailData == nil {
			continue
		}
		isChange := false
		for i := 0; i < len(mailData.Awards); i++ {
			res := mailData.Awards[i]
			if res.Type == uint32(common.RESOURCE_ITEM) {
				if res.Value == artifForgeItem || res.Value == artifStrengthItem {
					needDelItems[res.Value] += res.Count
					logArtifact = append(logArtifact, *res.Clone())
					isChange = true
					mailData.Awards = append(mailData.Awards[:i], mailData.Awards[i+1:]...)
					i--
				}
			}
		}

		if !isChange {
			continue
		}

		if len(mailData.Awards) == 0 {
			mailDeletes = append(mailDeletes, mailData.Id)
		} else {
			changes = append(changes, mailData)
		}
	}

	if len(logArtifact) > 0 {
		l4g.Infof("user: %s from mail to delete artifactForge item num: %d, res: %+v",
			uid, len(logArtifact), logArtifact)
	}

	calls := 0
	if len(changes) > 0 {
		r.RopCl.SetSomeMailMCallSKs(uid, changes)
		calls++
	}
	if len(mailDeletes) > 0 {
		r.RopCl.RemSomeMailMCallSKs(uid, mailDeletes)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %s", uid, reply.Err)
			return false
		}
	}

	return true
}

func fixDelPushGift(uid string, redisClient *redis.Redis) {
	fmt.Printf("fixDelPushGift insert uid:%s", uid)
	r := redisClient
	// base
	r.RopDB.GetUserMCallSKs(uid)
	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s ", uid, err)
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		fixDelPushGiftFailedCount++
		return
	}

	if userData.Module2 != nil {
		pushGiftData := userData.Module2.PushGiftInfo
		if pushGiftData != nil {
			for _, pushGifts := range pushGiftData.PushGifts {
				if pushGifts != nil && len(pushGifts.PushGift) > 0 {
					pushGifts.PushGift = pushGifts.PushGift[:0]
				}
			}
		}
		userData.Module2.PushGiftInfo = pushGiftData
		r.RopDB.SetUserMCallSKs(uid, userData)
		/************************REPLY*******************************/
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s save user error: %v", uid, reply.Err)
			fixDelPushGiftFailedCount++
			return
		}
	}

	fixDelPushGiftCount++
	l4g.Infof("uid: %s delete push gift data ok!", uid)
}

func fixMirage(uid string, redisClient *redis.Redis) {
	// 修改成就奖励领取状态
	{
		redisClient.RopDB.GetUserMCallSKs(uid)
		userData, err := redisClient.RopDB.GetUserByReply(redisClient.Client.GetReply())
		if err != nil {
			l4g.Error("get user error: %s %s", uid, err)
			//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
		}

		if userData == nil {
			fixMirageFailedCount++
			l4g.Error("user %s data corrupt", uid)
			return
		}

		if userData.Module1 == nil || userData.Module1.RankAchieveAwards == nil {
			return
		}

		rankIDs := make(map[uint32]struct{})
		for _, rankID := range goxml.MirageCopyID2RankId {
			rankIDs[rankID] = struct{}{}
		}

		isChange := false
		achieveAwards := userData.Module1.RankAchieveAwards
		for i := 0; i < len(achieveAwards.Id); i++ {
			goalID := achieveAwards.Id[i]
			info := goxml.GetData().RankingAchievementInfoM.Index(goalID)
			if info == nil {
				l4g.Errorf("RankingAchievementInfo not exist: %s %d", uid, goalID)
				continue
			}
			if _, exist := rankIDs[info.RankId]; exist {
				achieveAwards.Id = append(achieveAwards.Id[:i], achieveAwards.Id[i+1:]...)
				i--
				isChange = true
			}
		}

		if isChange {
			redisClient.RopDB.SetUserMCallSKs(uid, userData)
			if reply := redisClient.Client.GetReply(); reply.Err != nil {
				fixMirageFailedCount++
				l4g.Errorf("user %s save user error: %s %s", uid, userData, reply.Err)
				return
			}
		}
	}

	redisClient.RopCl.GetAllMirageMCallSKs(uid)
	mirageDatas, err := redisClient.RopCl.GetSomeMirageByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("get mirage data error: %s %s", uid, err)
		fixMirageFailedCount++
	}
	if len(mirageDatas) == 0 {
		return
	}

	deletes := make([]uint32, 0, len(mirageDatas))
	for hurdleID := range mirageDatas {
		deletes = append(deletes, hurdleID)
	}

	redisClient.RopCl.RemSomeMirageMCallSKs(uid, deletes)

	/************************REPLY*******************************/
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save delete mirage data error: %v", uid, reply.Err)
		fixMirageFailedCount++
		return
	}

	fixMirageCount++
}

func fixMirageRank(redisClient *redis.Redis) {
	calls := 0
	achieveRankIDs := make([]uint32, 0, len(goxml.MirageCopyID2RankId))
	// 删除排行榜数据
	for _, rankID := range goxml.MirageCopyID2RankId {
		redisClient.RopDB.DelAllCommonRankMCallSK(uint64(rankID))
		calls++
		achieveRankIDs = append(achieveRankIDs, rankID)
	}
	// 删除成就数据
	redisClient.RopDB.RemSomeRankAchievesMCall(achieveRankIDs)
	calls++

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save delete mirage rank data error: %s", reply.Err)
			fixMirageRankFailedCount++
			return
		}
	}

	fixMirageRankCount++
}
