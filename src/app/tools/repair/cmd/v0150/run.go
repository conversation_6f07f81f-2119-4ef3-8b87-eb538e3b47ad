package v0150

import (
	"app/goxml"
	"app/logic/character"
	ldb "app/logic/db"
	"app/logic/mail"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/base"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"flag"
	"fmt"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath              = flag.String("V0150configData", "../data/", "service data path")
	fixGoddessFailedCount     uint32
	fixGoddessItemFailedCount uint32
	fixMedalFailedCount       uint32
	delArenaLogFailedCount    uint32
	delWrestleLogFailedCount  uint32
	globalRank                map[uint64]*db.GlobalRank
	commonPowerRank           map[uint64]*db.CommonRank
	fixPowerFailedCount       uint32
	fixPowerRankFailedCount   uint32
	fixGlobalRankFailedCount  uint32
	fixMedalAwardFailedCount  uint32
)

var deleteGoddess = map[uint32]struct{}{
	2: {},
	6: {},
	7: {},
	8: {},
}

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory, redisActor *ldb.RedisActor) {
	goxml.Load(*flagDataPath, false, false)
	getPowerRank(redisClient)
	getGlobalRank(redisClient)
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixGoddess(uid, redisClient, idFactory, redisActor)
		fixMedal(uid, redisClient)
		fixMedalAward(uid, redisClient)
		delArenaLog(uid, redisClient)   //删除竞技场战斗日志
		delWrestleLog(uid, redisClient) //删除神树争霸战斗日志
		fixPower(uid, redisClient)
	})
	savePowerRank(redisClient)
	saveGlobalRank(redisClient)
	l4g.Info("\n")

	if fixGoddessFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixGoddess Success!\n")
	} else {
		fmt.Printf("[FAILED] fixGoddess Failed, need check error log! failedCount:%d\n", fixGoddessFailedCount)
	}

	if fixGoddessItemFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixGoddessItem Success!")
	} else {
		l4g.Infof("[FAILED] fixGoddessItem Failed, need check error log! failedCount:%d", fixGoddessItemFailedCount)
	}

	if fixMedalFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixMedal Success!")
	} else {
		l4g.Infof("[FAILED] fixMedal Failed, need check error log! failedCount:%d", fixMedalFailedCount)
	}

	if delArenaLogFailedCount == 0 {
		fmt.Printf("[SUCCESS] delArenaLog Success!\n")
	} else {
		fmt.Printf("[FAILED] delArenaLog Failed, need check error log! failedCount:%d\n", delArenaLogFailedCount)
	}

	if delWrestleLogFailedCount == 0 {
		fmt.Printf("[SUCCESS] delWrestleLog Success!\n")
	} else {
		fmt.Printf("[FAILED] delWrestleLog Failed, need check error log! failedCount:%d\n", delWrestleLogFailedCount)
	}

	if fixPowerFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPower Success!")
	} else {
		l4g.Infof("[FAILED] fixPower Failed, need check error log! failedCount:%d", fixPowerFailedCount)
	}

	if fixGlobalRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixGlobalRank Success!")
	} else {
		l4g.Infof("[FAILED] fixGlobalRank Failed, need check error log! failedCount:%d", fixGlobalRankFailedCount)
	}

	if fixPowerRankFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixPowerRank Success!")
	} else {
		l4g.Infof("[FAILED] fixPowerRank Failed, need check error log! failedCount:%d", fixPowerRankFailedCount)
	}

	if fixMedalAwardFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixMedalAward Success!")
	} else {
		l4g.Infof("[FAILED] fixMedalAward Failed, need check error log! failedCount:%d", fixMedalAwardFailedCount)
	}

	l4g.Info("finish repair")
}

func fixGoddess(uid string, r *redis.Redis, idFactory *id.Factory, redisActor *ldb.RedisActor) {
	u := base.LoadUserNew(uid, redisActor)
	if u == nil {
		l4g.Error("get user error: %s ", uid)
		return
	}

	dbUser := u.GetDBUser()
	if dbUser == nil || dbUser.Module3 == nil || dbUser.Module3.GoddessContractInfo == nil || len(dbUser.Module3.GoddessContractInfo.Goddess) == 0 {
		l4g.Error("user %s data corrupt", uid)
		return
	}
	var dungeonId uint32
	if dbUser != nil && dbUser.Module != nil && dbUser.Module.Dungeon != nil {
		dungeonId = dbUser.Module.Dungeon.SysId
	}

	goddess := dbUser.Module3.GoddessContractInfo.Goddess

	var itemCount uint32
	var totalExp uint32
	unlockSuit := []uint32{}
	for index, v := range goddess {
		var needClear bool
		if v == nil {
			delete(goddess, index)
			continue
		}
		_, exist := deleteGoddess[v.Id]
		if !exist {
			goddessContractInfo := goxml.GetData().GoddessContractInfoM.Index(v.Id)
			if goddessContractInfo == nil || goddessContractInfo.UnlockDungeon > dungeonId {
				needClear = true
			} else {
				if v.Id == 1 && v.RecoveryCount >= 2 {
					if dbUser != nil && dbUser.Module1 != nil && dbUser.Module1.Guidance != nil {
						needAppend := true
						finishId := uint32(233)
						for _, finish := range dbUser.Module1.Guidance.Finished {
							if finish == finishId {
								needAppend = false
							}
						}
						if needAppend {
							dbUser.Module1.Guidance.Finished = append(dbUser.Module1.Guidance.Finished, finishId)
						}
					}
					v.RecoveryCount = 4
				}
				if goxml.GetData().GoddessContractTreatInfoM.IsRecoveryFinish(v.Id, v.RecoveryCount) {
					v.SuitIds = make([]uint32, 0, 2)
					v.EquipSuit = 0
					totalExp += v.Exp
					for i := uint32(1); i <= v.Level; i++ {
						info := goxml.GetData().GoddessContractSkinInfoM.GetSuit(v.Id, i)
						if info == nil {
							continue
						}
						v.EquipSuit = info.GoddessSkinId
						v.SuitIds = append(v.SuitIds, info.GoddessSkinId)
						unlockSuit = append(unlockSuit, info.GoddessSkinId)
					}
				} else {
					needClear = true
				}
			}
		} else {
			needClear = true
		}
		if needClear {
			itemCount += v.Exp / 20
			if v.Exp%20 != 0 {
				itemCount++
			}

			delete(goddess, index)
		}
	}

	blessInfo := goxml.GetData().GoddessContractBlessInfoM.GetInfoByExp(totalExp)
	if blessInfo == nil {
		l4g.Errorf("user %s fix total exp:%d find level info failed", uid, totalExp)
		fixGoddessFailedCount++
		return
	}
	// totalLevel：家园总等级
	totalLevel := blessInfo.Level

	collections := make(map[uint32]uint32)
	for level, data := range goxml.GetData().GoddessCollectionInfoM.AllCollectionInfos() {
		if totalLevel >= level {
			if collections[data.CollectionId] < data.UnlockLevel {
				collections[data.CollectionId] = data.UnlockLevel
			}
		}
	}
	dbUser.ModuleGlobalAttr.GoddessContractExp = totalExp
	dbUser.ModuleGlobalAttr.GoddessSuits = unlockSuit
	dbUser.Module3.GoddessContractInfo.Goddess = goddess
	dbUser.Module3.GoddessContractInfo.Collections = collections

	if itemCount > 0 {
		totalReward := []*cl.Resource{}
		totalReward = append(totalReward, &cl.Resource{
			Type:  uint32(common.RESOURCE_ITEM),
			Value: 10057,
			Count: itemCount,
		})
		params := make([]string, 0, 2)
		params = append(params, "Valkyrie Manor Adjustment")
		params = append(params, "Some of the Valkyries will be unavailable in the Valkyrie Manor. The Favor they have earned will be returned to you in the form of a Favor Gift Chest. Some of the items in the Collection Room will be unlocked after receiving the gift. Please make sure to claim the items.")
		ret := sendMail(uid, r, idFactory, character.MailIDGM, totalReward, params)
		if !ret {
			fixGoddessItemFailedCount++
			l4g.Errorf("user: %s fixGoddessItem: send mail failed.", uid)
		}
	}

	r.RopDB.SetUserMCallSKs(uid, dbUser)
	if reply := r.Client.GetReply(); reply.Err != nil {
		fixGoddessFailedCount++
		l4g.Errorf("user %s save fixGoddess error: %s %s", uid, dbUser, reply.Err)
		return
	}
	l4g.Info("user %s save fixGoddess success", uid)
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}
	return true
}

func fixMedal(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module1 == nil || userData.Module3 == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module3.GoddessContractInfo == nil {
		return
	}

	if userData.Module3.GoddessContractInfo.Goddess == nil {
		return
	}

	if userData.Module1.Medal == nil {
		userData.Module1.Medal = new(cl.Medal)
	}
	if userData.Module1.Medal.GoddessCure == nil {
		userData.Module1.Medal.GoddessCure = make(map[uint32]bool)
	}

	isChange := false
	for _, v := range userData.Module3.GoddessContractInfo.Goddess {
		if len(v.GoddessStory) == 0 {
			continue
		}

		for i := uint32(1); i <= v.RecoveryCount; i++ {
			recoveryInfo := goxml.GetData().GoddessContractTreatInfoM.GetGoddessTimes(v.Id, i)
			if recoveryInfo != nil && recoveryInfo.TreatElite > 0 {
				userData.Module1.Medal.GoddessCure[recoveryInfo.TreatElite] = true
				isChange = true
			}
		}
	}

	if isChange {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixMedalFailedCount++
			l4g.Errorf("user %s save pass error: %s %s", uid, userData, reply.Err)
			return
		}
		l4g.Info("user %s save fixMedal success", uid)
	}
}

func delArenaLog(uidStr string, r *redis.Redis) {
	r.RopCl.DelAllArenaLogMCallSKs(uidStr)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delete Arena log error: %s", reply.Err)
		delArenaLogFailedCount++
		return
	}
	l4g.Info("[delArenaLog] user %s success", uidStr)
}

func delWrestleLog(uidStr string, r *redis.Redis) {
	r.RopCl.DelAllWrestleLogMCallSKs(uidStr)
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("delete Wrestle log error: %s", reply.Err)
		delWrestleLogFailedCount++
		return
	}
	l4g.Info("[delWrestleLog] user %s success", uidStr)
}

func getPowerRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllCommonRankMCallSK(uint64(goxml.PowerRankId))
	if data, err := redisClient.RopDB.GetSomeCommonRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get common power rank data error: %s", err.Error())
		fixPowerRankFailedCount++
		return
	} else {
		commonPowerRank = data
	}
}

func savePowerRank(redisClient *redis.Redis) {
	commonPowerRanks := make([]*db.CommonRank, 0, len(commonPowerRank))
	for _, rank := range commonPowerRank {
		commonPowerRanks = append(commonPowerRanks, rank)
	}
	redisClient.RopDB.SetSomeCommonRankMCallSK(uint64(goxml.PowerRankId), commonPowerRanks)

	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save common power rank data error: %s", reply.Err)
		fixPowerRankFailedCount++
		return
	}
}

func getGlobalRank(redisClient *redis.Redis) {
	redisClient.RopDB.GetAllGlobalRankMCall()
	if data, err := redisClient.RopDB.GetSomeGlobalRankByReply(redisClient.Client.GetReply()); err != nil {
		l4g.Errorf("get global rank data error: %s", err.Error())
		fixGlobalRankFailedCount++
		return
	} else {
		globalRank = data
	}
}

func saveGlobalRank(redisClient *redis.Redis) {
	globalRanks := make([]*db.GlobalRank, 0, len(globalRank))
	for _, rank := range globalRank {
		globalRanks = append(globalRanks, rank)
	}
	redisClient.RopDB.SetSomeGlobalRankMCall(globalRanks)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("save global rank data error: %s", reply.Err)
		fixGlobalRankFailedCount++
		return
	}
}

func fixPower(uid string, redisClient *redis.Redis) {
	u := base.LoadUser(uid, redisClient)
	fixDefensePower(uid, u, redisClient)
	fixTop5AndCrystalPower(uid, u, redisClient)
}

func fixDefensePower(uid string, u *character.User, redisClient *redis.Redis) {
	heroM := u.HeroManager()
	formationM := u.FormationManager()
	arenaDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_ARENA_DEFENSE))
	if arenaDefenseFormation != nil {
		var power int64
		for _, teamInfo := range arenaDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetArenaPower(power)
		l4g.Infof("newUserData:%s arenaPower: %d", uid, power)
	}

	wrestleDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE))
	if wrestleDefenseFormation != nil {
		var power int64
		for _, teamInfo := range wrestleDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetDefensePower(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE), power)
		l4g.Infof("newUserData:%s wrestlePower: %d", uid, power)
	}

	flowerDefenseFormation := formationM.Get(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE))
	if flowerDefenseFormation != nil {
		var power int64
		for _, teamInfo := range flowerDefenseFormation.Teams {
			for _, info := range teamInfo.Info {
				hero := heroM.Get(info.Hid)
				if hero != nil {
					power += int64(hero.GetHeroPower(u))
				}
			}
		}
		u.SetDefensePower(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE), power)
		l4g.Infof("newUserData:%s flowerPower: %d", uid, power)
	}
	redisClient.RopDB.SetUserMCallSKs(uid, u.GetDBUser())
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixPowerFailedCount++
		l4g.Errorf("[fixDefensePower] user %s save user error: %s", uid, reply.Err)
	}

}

func fixTop5AndCrystalPower(uid string, u *character.User, redisClient *redis.Redis) {
	userData := u.GetDBUser()
	heroM := u.HeroManager()
	// heroM.ReCalTop5()
	heroHeap := heroM.GetTop5Heros()
	l4g.Infof("oldUserData:%s top5%v", uid, userData.Base.Top5Heros)
	userData.Base.Top5Heros = make([]uint64, character.Top5HeroNum)
	var top5Power uint64
	for index, hero := range heroHeap {
		userData.Base.Top5Heros[index] = hero.GetData().Id
		top5Power += hero.GetHeroPower(u)
	}
	userData.Base.Power = int64(top5Power)
	userData.Base.PowerTm = time.Now().Unix()
	userData.Base.MaxPower = int64(top5Power)

	crystal := u.Crystal()
	if crystal.IsCrystalOpened() {
		userData.Base.CrystalPower = int64(crystal.CalCrystalPower())
	}

	//更新通用战力榜战力
	if userRank, exist := commonPowerRank[userData.Id]; exist {
		userPower := &rank.UserPower{}
		userPower.Load(userRank.Data)
		if crystal.IsCrystalOpened() {
			userPower.Power = uint64(userData.Base.CrystalPower)
		} else {
			userPower.Power = uint64(userData.Base.Power)
		}

		userPower.Tm = userData.Base.PowerTm
		userRank.Data = userPower.Save()
		l4g.Infof("update common power rank: %d power:%d", userPower.Uid, userPower.Power)
	}
	//更新全局战力
	if userRank, exist := globalRank[userData.Id]; exist {
		userRank.Power = userData.Base.Power
		userRank.PowerTm = userData.Base.PowerTm
		l4g.Infof("update global rank: %d power:%d", userRank.Id, userRank.Power)
	}

	redisClient.RopDB.SetUserMCallSKs(uid, userData)
	if reply := redisClient.Client.GetReply(); reply.Err != nil {
		fixPowerFailedCount++
		l4g.Errorf("[top5] user %s save user error: %s", uid, reply.Err)
	}
	l4g.Infof("newUserData:%s top5%v top5Power:%d crystalPower:%d",
		uid, userData.Base.Top5Heros, userData.Base.Power, userData.Base.CrystalPower)
}

func fixMedalAward(uid string, r *redis.Redis) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module1 == nil || userData.Module3 == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Module1.Medal == nil {
		userData.Module1.Medal = new(cl.Medal)
	}

	isChange := false
	if userData.Bag == nil {
		userData.Bag = &db.Bags{}
	}

	if userData.Bag.Items == nil {
		userData.Bag.Items = make(map[uint32]uint32)
	}

	for _, data := range goxml.GetData().MedalInfoM.Datas {
		if data.Level > userData.Module1.Medal.Level {
			continue
		}

		if data.Value5 > 0 {
			userData.Bag.Items[data.Value5] += data.Count5
			isChange = true
		}
	}

	if userData.Module != nil && userData.Module.Dungeon != nil && userData.Module.Dungeon.SysId >= 10212 {
		userData.Bag.Items[810024] += 1
		isChange = true
	}

	if isChange {
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixMedalAwardFailedCount++
			l4g.Errorf("user %s save pass error: %s %s", uid, userData, reply.Err)
			return
		}
		l4g.Info("user %s save fixMedalAward success", uid)
	}
}
