package cmd

import (
	v098 "app/tools/repair/cmd/v098"

	"github.com/spf13/cobra"
)

var v098Cmd = &cobra.Command{
	Use: "v098",
	Short: "0.9.8 - 1.缔约之石宝石和纹章 2.战力 3.更新通用战力榜战力 4.更新全局战力 5.宝石重置 6.契约之所修复 7.神器修复 8.密林等级 9.纹章重置 10.纹章Token转换 " +
		"11.图鉴羁绊删除, 12.推送礼包删除 13.装备强化随机种子 14.修复公会副本数据 15.修复玩家公会数据 16.清空公会副本日志 17.清空公会副本章节排行榜 18.删除战报 19.删除竞技场战斗日志",
	Run: func(cmd *cobra.Command, args []string) {
		v098.Run(cmd, args, redisClient, idFactory)
	},
}

func init() {
	rootCmd.AddCommand(v098Cmd)
}
