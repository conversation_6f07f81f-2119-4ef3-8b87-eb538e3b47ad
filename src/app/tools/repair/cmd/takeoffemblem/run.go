package takeoffemblem

import (
	"app/protos/out/cl"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var takeoffEmblemFailed uint64

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		takeoffEmblem(uid, redisClient)
	})
	if takeoffEmblemFailed == 0 {
		fmt.Printf("[SUCCESS] takeoffEmblem Success!\n")
	} else {
		fmt.Printf("[FAILED] takeoffEmblem Failed, need check error log! failedCount:%d\n", takeoffEmblemFailed)
	}
}

func takeoffEmblem(uid string, redisClient *redis.Redis) {
	r := redisClient

	r.RopCl.GetAllHeroBodyMCallSKs(uid)

	//英雄
	heroesData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
		return
	}

	//emblem
	r.RopCl.GetAllEmblemInfoMCallSKs(uid)

	//纹章
	emblemData, err := r.RopCl.GetSomeEmblemInfoByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user emblem error: %s %s", uid, err)
		return
	}

	var changeHero []*cl.HeroBody
	var changeEmblem []*cl.EmblemInfo
	calls := 0
	for _, hero := range heroesData {
		if hero == nil {
			continue
		}

		if hero.SysId != 34006 {
			continue
		}

		if len(hero.Emblem) == 0 {
			continue
		}

		for _, Id := range hero.Emblem {
			em := emblemData[Id]
			if em == nil {
				continue
			}
			em.Hid = 0
			changeEmblem = append(changeEmblem, em)
		}
		hero.Emblem = make(map[uint32]uint64)

		changeHero = append(changeHero, hero)
	}

	if len(changeEmblem) > 0 {
		redisClient.RopCl.SetSomeEmblemInfoMCallSKs(uid, changeEmblem)
		calls++
	}

	if len(changeHero) > 0 {
		redisClient.RopCl.SetSomeHeroBodyMCallSKs(uid, changeHero)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %s takeoff emblem error: %s", uid, reply.Err)
			takeoffEmblemFailed++
			return
		}
	}

	l4g.Infof("uid: %s take off emblem data ok!", uid)
}
