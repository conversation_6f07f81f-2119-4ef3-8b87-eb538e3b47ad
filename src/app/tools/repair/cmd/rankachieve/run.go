package rankachieve

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/tools/repair/cmd/redis"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

const (
	ArenaTaskTypeID       uint32 = 4206000
	MirageTaskTypeIDRace1 uint32 = 4801001
	MirageTaskTypeIDRace2 uint32 = 4801002
	MirageTaskTypeIDRace3 uint32 = 4801003
	MirageTaskTypeIDRace4 uint32 = 4801004
)

var achieveIDs = []uint32{
	ArenaTaskTypeID,
	MirageTaskTypeIDRace1,
	MirageTaskTypeIDRace2,
	MirageTaskTypeIDRace3,
	MirageTaskTypeIDRace4,
}

var TaskTypeID2RankID = map[uint32]uint32{
	ArenaTaskTypeID:       4,
	MirageTaskTypeIDRace1: 5,
	MirageTaskTypeIDRace2: 6,
	MirageTaskTypeIDRace3: 7,
	MirageTaskTypeIDRace4: 8,
}

type achieve struct {
	uid       string
	process   uint64
	power     int64
	creatTime int64
	vip       uint32
}

var (
	maxAchieve               map[uint32]*achieve
	fixHeroSuccess           bool   = true
	fixWishListSuccess       bool   = true
	updateRankAchieveSuccess bool   = true
	fixTop5Success           bool   = true
	fixKey                   string = "0.8.8_Fix"
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	// if hasRepaired(redisClient) {
	// 	fmt.Printf("[FAILED] %s hasRepaired!\n", fixKey)
	// 	return
	// }

	loadGoxml()
	initMaxAchieve()
	redis.IterateAllUsers(redisClient, func(uid string) {
		calcMaxAchieve(uid, redisClient)
		fixHeroStarUpCosts(uid, redisClient)
		fixWishList(uid, redisClient)
		fixTop5(uid, redisClient)
	})
	updateRankAchieve(redisClient)

	fmt.Printf("\n")
	if fixHeroSuccess {
		fmt.Printf("[SUCCESS] fixHero Success!\n")
	} else {
		fmt.Printf("[FAILED] fixHero Failed!\n")
	}

	if fixWishListSuccess {
		fmt.Printf("[SUCCESS] fixWishList Success!\n")
	} else {
		fmt.Printf("[FAILED] fixWishList Failed!\n")
	}

	if fixTop5Success {
		fmt.Printf("[SUCCESS] fixTop5Success Success!\n")
	} else {
		fmt.Printf("[FAILED] fixTop5Success Failed!\n")
	}

	if updateRankAchieveSuccess {
		fmt.Printf("[SUCCESS] updateRankAchieve Success!\n")
	} else {
		fmt.Printf("[FAILED] updateRankAchieve Failed!\n")
	}

	// loadGoxml()
	// redis.IterateOneUser(redisClient, func(uid string) {
	// 	fixHeroStarUpCosts(uid, redisClient)
	// })
	//setRepaired(redisClient)
	fmt.Printf("\nfinish repair\n")
}

// 检查是否已修复过数据 - 有数据即代表被修复过
func hasRepaired(redisClient *redis.Redis) bool {
	reply := redisClient.Client.Cmd("GET", fixKey)
	_, err := reply.Str()
	return err == nil
}

func setRepaired(redisClient *redis.Redis) {
	reply := redisClient.Client.Cmd("SET", fixKey, fixKey)
	if reply.Err != nil {
		l4g.Errorf("SET %s %s failed: %v", fixKey, fixKey, reply.Err)
	}
}

func loadGoxml() {
	dir := "../data"
	show := false
	goxml.GetData().RankingAchievementInfoM.Load(dir, show)

	goxml.GetData().HeroInfoM.Load(dir, show)
	goxml.GetData().HeroStarInfoM.Load(dir, show)
}

func initMaxAchieve() {
	maxAchieve = make(map[uint32]*achieve, len(achieveIDs))
	for _, taskTypeID := range achieveIDs {
		maxAchieve[taskTypeID] = &achieve{}
	}
}

func calcMaxAchieve(uid string, redisClient *redis.Redis) {
	// base
	redisClient.RopDB.GetUserMCallSKs(uid)

	// base
	userData, err := redisClient.RopDB.GetUserByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
		updateRankAchieveSuccess = false
	}

	if userData == nil || userData.Module == nil || userData.Base == nil {
		l4g.Errorf("user %s data corrupt", uid)
		updateRankAchieveSuccess = false
		return
	}

	if userData.Module.Achieve != nil {
		for taskTypeID, v := range userData.Module.Achieve.TaskTypeProgress {
			if isInCheckList(taskTypeID) {
				checkUpdateMaxAchieve(uid, taskTypeID, v.Progress, userData.Base)
			}
		}
	}
}

func isInCheckList(taskTypeID uint32) bool {
	for _, id := range achieveIDs {
		if id == taskTypeID {
			return true
		}
	}
	return false
}

func checkUpdateMaxAchieve(uid string, taskTypeID uint32, process uint64, base *db.BaseAttr) {
	data := maxAchieve[taskTypeID]
	if data == nil {
		panic(fmt.Sprintf("checkUpdateMaxAchieve. maxAchieve not exist, taskTypeID: %d", taskTypeID))
	}

	var needUpdate bool
	if process > data.process {
		needUpdate = true
	} else if process == data.process {
		if base.Power > data.power {
			needUpdate = true
		} else if base.Power == data.power {
			if base.Vip > data.vip {
				needUpdate = true
			} else if base.Vip == data.vip {
				if base.CreateTime < data.creatTime {
					needUpdate = true
				}
			}
		}
	}

	if needUpdate {
		data.uid = uid
		data.process = process
		data.power = base.Power
		data.vip = base.Vip
		data.creatTime = base.CreateTime
	}
}

// 更新排行成就数据
func updateRankAchieve(redisClient *redis.Redis) {
	fmt.Printf("start updateRankAchieve, baseData:%+v\n", maxAchieve)
	result := make([]*db.RankAchieves, 0, len(maxAchieve))
	for t, v := range maxAchieve {
		fmt.Printf("updateRankAchieve. maxAchieve: %d --- %v\n", t, v)
		if v.uid == "" {
			continue
		}

		rankID := TaskTypeID2RankID[t]
		if rankID == 0 {
			panic(fmt.Sprintf("updateRankAchieve. rankID config not exist, taskTypeID: %d", t))
		}

		targetID := goxml.GetData().RankingAchievementInfoM.GetIdByValue(rankID, int64(v.process))
		finishedIds := goxml.GetData().RankingAchievementInfoM.GetIdsByRange(rankID, 0, targetID)
		if len(finishedIds) > 0 {
			tmp := &db.RankAchieves{
				Id:       rankID,
				Achieves: make([]*cl.RankAchieve, 0, len(finishedIds)),
			}

			uid, err := strconv.Atoi(v.uid)
			if err != nil {
				panic(fmt.Sprintf("updateRankAchieve. strconv.Atoi failed, taskTypeID: %d uid: %s", t, v.uid))
			}

			for _, id := range finishedIds {
				achieve := &cl.RankAchieve{
					Id:  id,
					Uid: uint64(uid),
				}
				tmp.Achieves = append(tmp.Achieves, achieve)

				fmt.Printf("updateRankAchieve. rankID:%d, achieveID:%d, achieve:%+v\n", rankID, id, achieve)
			}
			result = append(result, tmp)
		}
	}

	fmt.Printf("end updateRankAchieve, result:%+v\n", result)

	if len(result) > 0 {
		if err := redisClient.RopDB.SetSomeRankAchieves(result); err != nil {
			l4g.Errorf("updateRankAchieve failed. error:%s", err)
			updateRankAchieveSuccess = false
		}
	}
}

// 添加升星回退数据
func fixHeroStarUpCosts(uid string, redisClient *redis.Redis) {
	l4g.Infof("user: %s, start fixHeroStarUpCosts", uid)
	//hero
	redisClient.RopCl.GetAllHeroBodyMCallSKs(uid)
	//英雄
	heroesData, err := redisClient.RopCl.GetSomeHeroBodyByReply(redisClient.Client.GetReply())
	if err != nil {
		l4g.Errorf("user: %s, get heroes error: %s", uid, err)
		fixHeroSuccess = false
	}

	if len(heroesData) == 0 {
		l4g.Errorf("user %s heroesData corrupt", uid)
		return
	}

	var changes []*cl.HeroStarUpCosts
	for _, hero := range heroesData {
		info := goxml.GetData().HeroInfoM.Index(hero.SysId)
		if info == nil {
			l4g.Errorf("user: %s, hero data is nil. sysID: %d", uid, hero.SysId)
			fixHeroSuccess = false
			continue
		}

		if hero.Star <= info.Star {
			continue
		}

		if info.Star < goxml.HeroCanBackMinInitStar || info.Back != goxml.HeroCanBack {
			continue
		}

		l4g.Infof("user: %s, start calcCostHeroSysIDs hid:%d sysID:%d star:%d", uid, hero.Id, hero.SysId, hero.Star)
		var heroInfo *goxml.HeroInfoExt
		starUpCosts := &cl.HeroStarUpCosts{
			Id:             hero.Id,
			Star5Costs:     make(map[uint32]uint32, 4),
			OtherStarCosts: make([]*cl.StarUpCosts, 0, 3),
		}
		for i := hero.Star - 1; i >= info.Star; i-- {
			sInfo := goxml.GetData().HeroStarInfoM.Index(i, info.Rare, info.Race)
			if sInfo == nil {
				l4g.Errorf("user: %s, starInfo not exist. sysID:%d star:%d", uid, hero.SysId, i)
				fixHeroSuccess = false
				return
			}

			for _, v := range sInfo.HeroCosts {
				if v.Type == goxml.HeroStarUpCostTypeSelf {
					heroInfo = info
				} else if v.Type == goxml.HeroStarUpCostTypeRace {
					heroInfo = goxml.GetData().HeroInfoM.GetSpecialHeroByRaceAndStar(info.Race, v.Star)
				} else if v.Type == goxml.HeroStarUpCostTypeAny {
					heroInfo = goxml.GetData().HeroInfoM.GetSpecialHeroByRaceAndStar(goxml.RaceNone, goxml.HeroNoRaceDefaultStar)
				} else {
					l4g.Errorf("user: %s, heroCost type illegal. sysID:%d type:%d", uid, hero.SysId, v.Type)
					fixHeroSuccess = false
					return
				}

				if heroInfo.Star == 5 {
					starUpCosts.Star5Costs[heroInfo.Id] += v.Count
				} else {
					starUpCosts.OtherStarCosts = append(starUpCosts.OtherStarCosts, &cl.StarUpCosts{
						SysId: heroInfo.Id,
						Star:  heroInfo.Star,
					})
				}
			}
		}

		changes = append(changes, starUpCosts)
	}

	if len(changes) > 0 {
		redisClient.RopCl.SetSomeHeroStarUpCostsMCallSKs(uid, changes)
		if reply := redisClient.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user: %s save heroStarUpCosts error: %+v", uid, reply.Err)
			fixHeroSuccess = false
			return
		}
	}
	fmt.Printf("[SUCCESS] user: %s, finish fixHeroStarUpCosts. changes:%v \n", uid, changes)
}

// 修复心愿单数据
func fixWishList(uid string, redisClient *redis.Redis) {
	/*r := redisClient
	// base
	r.RopDB.GetUserMCallSKs(uid)

	// base
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil || userData.Module1 == nil {
		l4g.Errorf("user %s data corrupt", uid)
		fixWishListSuccess = false
		return
	}

	wishList := userData.Module.Weekly.SummonWishList[3]

	if wishList != nil && userData.Module.Summon[3] != nil {
		if userData.Module.Summon[3].WishListMsg == nil {
			userData.Module.Summon[3].WishListMsg = &db.WishList{
				List: make(map[uint32]uint32),
			}
		}
		for sort, id := range userData.Module.Summon[3].WishList {
			userData.Module.Summon[3].WishListMsg.List[sort] = id
		}
		userData.Module.Summon[3].WishListMsg.WishGuaranteeCount = wishList.WishGuaranteeCount
		userData.Module.Summon[3].WishListMsg.LockedSlot = wishList.LockedSlot
		r.RopDB.SetUserMCallSKs(uid, userData)
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d save user error: %s", uid, reply.Err)
			fixWishListSuccess = false
			return
		}
		l4g.Infof("success user's uid:%s uuid:%s ", uid, userData.Uuid)
	}*/
}

// 修复top5数据
func fixTop5(uid string, redisClient *redis.Redis) {
}
