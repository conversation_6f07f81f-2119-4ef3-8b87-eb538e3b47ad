package cmd

import (
	v095 "app/tools/repair/cmd/v095"

	"github.com/spf13/cobra"
)

var v095Cmd = &cobra.Command{
	Use: "v095",
	Short: "0.9.5 - 1.修复战力 2.修复排行榜（战力与全局）3.删除迷宫模板 4.迷宫成就奖励补发 5.修复公会技能数据发奖励, 6.装备精炼资源返还, 7.英雄阶级资源返还, 8.符文等级资源返还 " +
		"9.新娘脱装备及符文补偿箱",
	Run: func(cmd *cobra.Command, args []string) {
		v095.Run(cmd, args, redisClient, idFactory)
	},
}

func init() {
	rootCmd.AddCommand(v095Cmd)
}
