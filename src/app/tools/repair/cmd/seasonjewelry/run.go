package seasonjewelry

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/utils"
	"flag"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
)

var (
	flagDataPath                = flag.String("seasonjewelryconfigData", "../data/", "service data path")
	fixSeasonJewelryFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis) {
	goxml.Load(*flagDataPath, false, false)

	redis.IterateAllUsers(redisClient, func(uid string) { // 清除所有赛季装备数据
		fixSeasonJewelry(uid, redisClient)
	})

	if fixSeasonJewelryFailedCount == 0 {
		fmt.Printf("[SUCCESS] fixSeasonJewelry Success!\n")
	} else {
		fmt.Printf("[FAILED] fixSeasonJewelry Failed, need check error log! failedCount:%d\n", fixSeasonJewelryFailedCount)
	}

	l4g.Info("finish repair")
}

func fixSeasonJewelry(uid string, r *redis.Redis) {
	calls := 0

	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		return
	}

	if userData == nil {
		l4g.Error("user %s data corrupt", uid)
		return
	}

	if userData.Base == nil {
		return
	}

	// 筛选需要被修复的玩家（1.已进入新赛季 2.还在上个赛季，但已经完成结算）
	var needFix bool
	if userData.Base.SeasonId == 111 || (userData.Base.SeasonId == 110 && userData.Base.SeasonEnd) {
		needFix = true
	}
	if !needFix {
		return
	}

	// 删除赛季装备自选箱和部分随机箱
	delResources := getDelResource()
	utils.ConsumeAllResource(uid, r, delResources)

	// 根据筛选条件清除赛季装备数据
	r.RopCl.GetAllSeasonJewelryMCallSKs(uid)
	seasonJewelryData, err := r.RopCl.GetSomeSeasonJewelryByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get season jewelry error: %s %s", uid, err)
		fixSeasonJewelryFailedCount++
		return
	}
	deletes := make([]uint64, 0, len(seasonJewelryData))
	for jid, data := range seasonJewelryData {
		info := goxml.GetData().SeasonJewelryInfoM.GetRecordById(data.SysId)
		if info == nil {
			continue
		}
		// 大于T8的装备会被删除
		if info.Level <= 8 {
			continue
		}
		deletes = append(deletes, jid)
	}
	if len(deletes) > 0 {
		r.RopCl.RemSomeSeasonJewelryMCallSKs(uid, deletes)
		calls++
	}

	// 修改英雄数据，删除身上的赛季装备数据
	r.RopCl.GetAllHeroBodyMCallSKs(uid)
	heroesData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
	}
	var changes []*cl.HeroBody
	for _, hero := range heroesData {
		if len(hero.SeasonJewelry) > 0 {
			hero.SeasonJewelry = make(map[uint32]uint64)
			changes = append(changes, hero)
		}
	}
	if len(changes) > 0 {
		r.RopCl.SetSomeHeroBodyMCallSKs(uid, changes)
		calls++
	}

	/************************REPLY*******************************/
	//mcall
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			fixSeasonJewelryFailedCount++
			l4g.Errorf("user %s save fixSeasonJewelry error: %s", uid, reply.Err)
			return
		}
	}

	l4g.Infof("uid: %s fixSeasonJewelry success!", uid)
}

func getDelResource() []*cl.Resource {
	res := make([]*cl.Resource, 0)
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110001,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110002,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110003,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110004,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110005,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110006,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110007,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110008,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110009,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110010,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110011,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110012,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110013,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110014,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110015,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110016,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110017,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110018,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110019,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110020,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110027,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110028,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110029,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110030,
	})
	res = append(res, &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: 110031,
	})
	return res
}
