package v01121

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/mail"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/tools/repair/cmd/idfactory"
	"app/tools/repair/cmd/redis"
	"app/tools/repair/cmd/xmlcfg"
	"flag"

	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/cobra"
	"gitlab.qdream.com/kit/sea/id"
)

var (
	flagDataPath                = flag.String("V01121configData", "../data/", "service data path")
	fixRoundActivityFailedCount uint32
)

func Run(cmd *cobra.Command, args []string, redisClient *redis.Redis, idFactory *id.Factory) {
	goxml.Load(*flagDataPath, false, false)
	xmlcfg.Load()
	redis.IterateAllUsers(redisClient, func(uid string) {
		fixRoundActivity(uid, redisClient, idFactory)
	})
	l4g.Info("\n")

	if fixRoundActivityFailedCount == 0 {
		l4g.Infof("[SUCCESS] fixRoundActivity Success!")
	} else {
		l4g.Infof("[FAILED] fixRoundActivity Failed, need check error log! failedCount:%d", fixRoundActivityFailedCount)
	}
	l4g.Info("finish repair")
}

var taskList = []uint32{14101, 14102, 14103, 14104, 14201, 14202, 14203, 14204, 14301, 14302, 14303, 14304, 14401, 14402, 14403, 14404, 14501, 14502, 14503, 14504, 24101, 24102, 24103, 24104, 24201, 24202, 24203, 24204, 24301, 24302, 24303, 24304, 24401, 24402, 24403, 24404, 24501, 24502, 24503, 24504}

func fixRoundActivity(uid string, r *redis.Redis, idFactory *id.Factory) {
	r.RopDB.GetUserMCallSKs(uid)
	userData, err := r.RopDB.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get user error: %s %s", uid, err)
		//这里用的MultiCall，所以没有return，等下一个GetReply把数据读完
	}

	if userData == nil {
		l4g.Errorf("user %s data corrupt", uid)
		return
	}

	if userData.Module3 == nil {
		l4g.Errorf("user %s Module3 not exist", uid)
		return
	}

	if len(userData.Module3.RoundActivity) == 0 {
		return
	}

	var count uint32
	for _, data := range userData.Module3.RoundActivity {
		if data.Category != 4 {
			continue
		}

		if data.TaskReceived == nil {
			continue
		}

		for id := range data.TaskReceived {
			for _, taskID := range taskList {
				if taskID == id {
					count++
				}
			}
		}
	}

	if count > 0 {
		retRes := []*cl.Resource{{
			Type:  uint32(common.RESOURCE_FRAGMENT),
			Value: 50011,
			Count: count * 25,
		},
		}
		params := make([]string, 0, 2)
		params = append(params, "Synergy Summons Rewards")
		params = append(params, "We have added a new batch of summoning rewards, the following are the reissued rewards for your completed quests, please make sure to claim them.")
		ret := sendMail(uid, r, idFactory, character.MailIDGM, retRes, params)
		if !ret {
			fixRoundActivityFailedCount++
			l4g.Errorf("user: %s fixRoundActivity: send mail failed.", uid)
		}
		l4g.Info("user %s fixRoundActivity success, count:%d", uid, count)
	}
}

func sendMail(uid string, r *redis.Redis, idFactory *id.Factory, mailID uint32, awards []*cl.Resource, params []string) bool {
	m := mail.NewMail(mailID, idfactory.CreateID(idFactory), params, awards, mail.TypeNormal, 0, 0)
	r.RopCl.SetSomeMailMCallSKs(uid, []*cl.Mail{m})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %s save user mail error: %s", uid, reply.Err)
		return false
	}

	return true
}
