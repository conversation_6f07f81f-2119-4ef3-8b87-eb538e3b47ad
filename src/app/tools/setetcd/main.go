package main

import (
	"app/logic/helper"
	"app/protos/in/config"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"strings"
	"time"

	"go.etcd.io/etcd/clientv3"
	//	"go.etcd.io/etcd/mvcc/mvccpb"
)

var NodeId = flag.Int("node_id", 1, "服务器id")
var ServerIp = flag.String("ip", "*************", "服务器ip")
var StartTime = flag.String("start_time", "2021-01-01 10:00:00", "开服时间")
var ServerName = flag.String("server_name", "xxx服务器", "服务器时间")
var EtcdServers = flag.String("etcd_servers", "*************:2379", "etcd 服务器地址")
var Method = flag.String("method", "get", "查询方法")

func main() {
	flag.Parse()
	appId := uint64(1)
	opGroup := uint64(0)
	nodeId := uint64(*NodeId)
	if nodeId <= 0 || nodeId >= 10 {
		fmt.Printf("node id must in 1-10 %d \n", nodeId)
		return
	}
	serviceId := uint32(10000) + uint32(nodeId)
	logicId := fmt.Sprintf("%s:%d", *ServerIp, serviceId)
	redisIndex := uint32(nodeId)
	redis := "127.0.0.1:9898"
	startTime, err := time.Parse("2006-01-02 15:04:05", *StartTime)
	if err != nil {
		fmt.Printf("start time parse error:%s \n", err)
		return
	}
	startTimeUnix := startTime.Unix()
	serverId, _ := helper.CreateServerID(appId, opGroup, nodeId)
	gateway := &config.GatewayCluster{
		EtcdServers: *EtcdServers,
		Target:      "/game/x/gateway/node/",
		Guard:       "*************:39422",
	}
	logic := &config.LogicService{
		AccountSize:        1000000,
		OnlineSize:         6000,
		QueuePopSize:       10,
		MaxCacheEntries:    5000,
		UserSaveInterval:   50,
		QueueSize:          2000,
		EnableTraceLog:     false,
		IpAccounts:         0,
		DeviceIdAccounts:   0,
		IpAccountsHw:       0,
		DeviceIdAccountsHw: 0,
		EnableCrypto:       false,
		UserCacheInterval:  600, //缓存过期时间 一般10分钟
	}
	conf := config.Logic{
		ServerId:         serverId,
		AppId:            appId,
		OpGroup:          opGroup,
		NodeId:           nodeId,
		RedisIndex:       redisIndex,
		Redis:            redis,
		StartTime:        startTimeUnix,
		EnableLoginLimit: false,
		ServerName:       *ServerName,
		GatewayCluster:   gateway,
		Logic:            logic,
	}
	fmt.Printf("etcd config %+v \n", conf)
	node := "/game/x/logic/node/"
	//target := fmt.Sprintf("/game/x/logic/node/%s", logicId)
	if *Method == "get" {
		value, err := getConfigFromEtcd(*EtcdServers, node, logicId)
		if err != nil {
			fmt.Printf("getConfigFromEtcd error %s \n", err)
			return
		}
		fmt.Printf("get config from etcd value:%+v \n", value)
	} else if *Method == "put" {
		value, err := json.Marshal(conf)
		if err != nil {
			fmt.Printf("put marshal conf error %s \n", err)
			return
		}
		target := fmt.Sprintf("%s%s", node, logicId)
		SetEtcd(*EtcdServers, target, string(value))
	} else {
		fmt.Printf("method must get or put %s \n", *Method)
	}
}

func SetEtcd(servers string, target, value string) {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   strings.Split(servers, ","),
		DialTimeout: time.Duration(1) * time.Second,
	})
	if err != nil {
		fmt.Printf("[discovery] new client error: %s \n", err)
		return
	}

	defer cli.Close() // make sure to close the client
	response, err := set(cli, target, value)
	if err != nil {
		fmt.Printf("[discovery] set etcd error: %s \n", err)
		return
	}
	fmt.Printf("set etcd response:%+v \n", response)

}

func set(cli *clientv3.Client, target, value string) (*clientv3.PutResponse, error) {
	getCtx, cancel := context.WithTimeout(context.Background(), time.Duration(5)*time.Second)
	var resp *clientv3.PutResponse
	var err error
	resp, err = cli.Put(getCtx, target, value)
	cancel()
	return resp, err
}

func getConfigFromEtcd(servers string, node string, logicID string) (*config.Logic, error) {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   strings.Split(servers, ","),
		DialTimeout: time.Duration(5) * time.Second,
	})
	if err != nil {
		return nil, err
	}
	defer cli.Close()

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(5)*time.Second)
	resp, err := cli.Get(ctx, node+logicID)
	cancel()
	if err != nil {
		return nil, err
	}

	if len(resp.Kvs) != 1 {
		return nil, fmt.Errorf("%s no found value in etcd, kv size: %d", node+logicID, len(resp.Kvs))
	}
	ev := resp.Kvs[0]
	fmt.Printf("get config from etcd string:%s \n", string(ev.Value))
	ret := new(config.Logic)
	err = json.Unmarshal(ev.Value, ret)
	if err != nil {
		return ret, err
	}
	return ret, nil
}
