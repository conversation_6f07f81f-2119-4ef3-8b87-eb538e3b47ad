package helper

import (
	"adapter/service/proto/out/cl"
	"fmt"
	"strconv"
	"strings"
	"time"

	sub "adapter/service/proto/log"
	"adapter/service/proto/out/common"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func FormatBool(num uint64) bool {
	return num == 1
}

func FormatTime(tm int64, zoneData string) string {
	if tm <= 0 {
		//l4g.Error("FormatTime error. tm:%d, zoneData:%s", tm, zoneData)
		return ""
	}
	t, _ := GetLocalTimeInstance(tm, zoneData)
	return t.Format("2006-01-02 15:04:05")
}

func IsCurrency(resourceType uint32) bool {
	return common.RESOURCE(resourceType) == common.RESOURCE_DIAMOND ||
		common.RESOURCE(resourceType) == common.RESOURCE_GOLD ||
		common.RESOURCE(resourceType) == common.RESOURCE_TOKEN
}

func GetTimeAndOffset(tm int64, zoneData string) (string, int64) {
	if tm <= 0 {
		l4g.Error("GetTimeOffset error. tm:%d, zoneData:%s", tm, zoneData)
		return "", 0
	}

	t, offset := GetLocalTimeInstance(tm, zoneData)
	return t.Format("2006-01-02 15:04:05"), offset
}

func GetLocalTimeInstance(tm int64, zoneData string) (time.Time, int64) {
	/*
		zone, offset := formatZoneData(zoneData)
		if zone == "" {
			zone = "Local"
		}
		t := time.Unix(tm, 0)
		local, err := time.LoadLocation(zone)
		if err != nil {
			l4g.Error("GetLocalTimeInstance failed. tm:%d, zone:%s, err:%s",
				tm, zone, err)
			zone = "Local"
			local, _ = time.LoadLocation(zone)
		}
		return t.In(local), offset
	*/
	_, offset := formatZoneData(zoneData)
	local := time.FixedZone("serverZone", int(offset*3600))
	t := time.Unix(tm, 0)
	return t.In(local), offset
}

// 返回时区代码和偏移量
func formatZoneData(zoneData string) (string, int64) {
	zoneVals := strings.Split(zoneData, "|")
	if len(zoneVals) != 2 {
		l4g.Error("formatZoneData failed. zoneData:%s", zoneData)
		return "Local", 0
	}
	offset, _ := strconv.ParseInt(zoneVals[1], 10, 64)
	return zoneVals[0], offset
}

func GetChannel(log *da.Log) string {
	return strconv.FormatUint(uint64(log.OpID), 10)
}

// tracks: 6
func (mr *MsgRecorder) GetMinimalMessage(log *da.Log) {
	uidString := strconv.FormatUint(log.UserID, 10)
	mr.SetUserID(strconv.FormatUint(log.ServerID, 10) + "_" + uidString)
	extra := NewExtra(log)
	if extra != nil {
		mr.SetDistinctID(extra.TaDistinctID)
		mr.SetTrack("omini_uuid", extra.OmniUuid)
	}
	mr.SetTrack("user_id", uidString)
	mr.SetTrack("account_id", log.UUID)
	mr.SetTrack("device_id", log.DeviceID)
	mr.SetTrack("channel", GetChannel(log))
	mr.SetTrack("server_id", strconv.FormatUint(log.ServerID, 10))
	mr.SetTrack("context_id_str", strconv.FormatUint(log.ContextID, 10))
	mr.SetTrack("op_group", strconv.FormatUint(uint64(log.OpGroup), 10))
	mr.SetTrack("op_id", strconv.FormatUint(uint64(log.OpID), 10))
	mr.SetTrack("open_day", log.OpenDay)
	mr.SetTrack("login_day", log.LoginDay)
	mr.SetTrack("season_id", log.SeasonID)
	mr.SetTrack("season_level", log.SeasonLevel)
	mr.SetTrack("season_power_str", strconv.FormatUint(log.SeasonPower, 10))
	mr.SetTrack("guild_id", strconv.FormatUint(log.GuildID, 10))
	mr.SetTrack("account_tag", log.AccountTag)
	if log.CreateTime > 0 {
		mr.SetTrack("life_cycle", (int64(log.EventTime)-log.CreateTime)/86400+1)
	}
	mr.SetTrack("battle_zone", log.BattleZone)

	createTime := FormatTime(log.CreateTime, log.Param9)
	if log.Pid > 0 {
		createTimeInt, _ := strconv.Atoi(createTime)
		mr.SetTrack("create_time_int", createTimeInt)
		mr.SetTrack("pid", strconv.FormatUint(uint64(log.Pid), 10))
		mr.SetTrack("gid", strconv.FormatUint(uint64(log.Gid), 10))
	} else {
		mr.SetTrack("create_time", createTime)
	}
}

// tracks: 9
func (mr *MsgRecorder) GetBaseMessage(log *da.Log) {
	mr.GetMinimalMessage(log)
	mr.SetTrack("vip_level", log.VipLevel)
	mr.SetTrack("level", log.Level)
	mr.SetTrack("power_value_str", strconv.FormatUint(log.Power, 10))
}

// int32 slice 转 string slice
func Int32SliceToStringSlice(param string) []string {
	var int32Array []uint32
	err := Json.Unmarshal([]byte(param), &int32Array)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil
	}
	strArray := make([]string, 0, len(int32Array))
	for _, id := range int32Array {
		strArray = append(strArray, strconv.FormatUint(uint64(id), 10))
	}
	return strArray
}

// int64 slice 转 string slice
func Int64SliceToStringSlice(param string) []string {
	var int64Array []uint64
	err := Json.Unmarshal([]byte(param), &int64Array)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil
	}
	strArray := make([]string, 0, len(int64Array))
	for _, id := range int64Array {
		strArray = append(strArray, strconv.FormatUint(id, 10))
	}
	return strArray
}

func (mr *MsgRecorder) SetFormationMessage(formationStr string) bool {
	var formation *sub.Formation
	err := Json.Unmarshal([]byte(formationStr), &formation)
	if err != nil {
		l4g.Errorf("[SetFormationMessage] json unmarshal error: %v, formationStr: %s", err, formationStr)
		return false
	}

	if formation == nil {
		l4g.Errorf("[SetFormationMessage] formation is nil")
		return false
	}

	for index, team := range formation.Teams {
		if index == 0 {
			mr.SetTrack("formation_heroes_detail_str", team.Heroes)
			mr.SetTrack("formation_heroes_addition_detail", team.Addition)
			mr.SetTrack("formation_emblems_detail_str", team.Emblems)
			mr.SetTrack("formation_artifacts_detail", team.Artifacts)
			mr.SetTrack("formation_links_detail", team.Links)
			if team.Rite != nil {
				mr.SetTrack("formation_mark_detail", team.Rite.Grids)
				team.Rite.Grids = nil
				mr.SetTrack("formation_rite_detail", team.Rite)
			}
			mr.SetTrack("formation_remains_detail", team.Remains)
		} else {
			index := fmt.Sprintf("%d", index)
			mr.SetTrack("formation_heroes_detail_str_"+index, team.Heroes)
			mr.SetTrack("formation_heroes_addition_detail_"+index, team.Addition)
			mr.SetTrack("formation_emblems_detail_str_"+index, team.Emblems)
			mr.SetTrack("formation_artifacts_detail_"+index, team.Artifacts)
			mr.SetTrack("formation_links_detail_"+index, team.Links)
			if team.Rite != nil {
				mr.SetTrack("formation_mark_detail_"+index, team.Rite.Grids)
				team.Rite.Grids = nil
				mr.SetTrack("formation_rite_detail_"+index, team.Rite)
			}
			mr.SetTrack("formation_remains_detail_"+index, team.Remains)
		}
	}
	return true
}

func (mr *MsgRecorder) SetFormationMessageByIndex(formationStr string, teamIndex uint32) bool {
	var formation *sub.Formation
	err := Json.Unmarshal([]byte(formationStr), &formation)
	if err != nil {
		l4g.Errorf("[SetFormationMessageByIndex] json unmarshal error: %v, formationStr: %s", err, formationStr)
		return false
	}

	if formation == nil {
		l4g.Errorf("[SetFormationMessageByIndex] formation is nil")
		return false
	}

	for index, team := range formation.Teams {
		if uint32(index) == teamIndex {
			mr.SetTrack("formation_heroes_detail_str", team.Heroes)
			mr.SetTrack("formation_heroes_addition_detail", team.Addition)
			mr.SetTrack("formation_emblems_detail_str", team.Emblems)
			mr.SetTrack("formation_artifacts_detail", team.Artifacts)
			mr.SetTrack("formation_links_detail", team.Links)
			if team.Rite != nil {
				mr.SetTrack("formation_mark_detail", team.Rite.Grids)
				team.Rite.Grids = nil
				mr.SetTrack("formation_rite_detail", team.Rite)
			}
			mr.SetTrack("formation_links_detail", team.Links)
		}
	}
	return true
}

// 记录阵容信息
func (mr *MsgRecorder) SetFormationDetailsMessage(formationStr string) bool {
	var formation *sub.Formation
	err := Json.Unmarshal([]byte(formationStr), &formation)
	if err != nil {
		l4g.Errorf("[SetFormationDetailsMessage] json unmarshal error: %v, formationStr: %s", err, formationStr)
		return false
	}

	if formation == nil {
		l4g.Errorf("[SetFormationDetailsMessage] formation is nil")
		return false
	}
	var suffix string
	for index, team := range formation.Teams {
		if index == 0 {
			suffix = "_attack"
		} else {
			suffix = "_defense"
		}
		mr.SetTrack("power"+suffix, team.Power)
		mr.SetTrack("power"+suffix+"_str", strconv.FormatUint(uint64(team.Power), 10))
		mr.SetTrack("heroes"+suffix, team.Heroes)
		mr.SetTrack("heroes_addition"+suffix, team.Addition)
		mr.SetTrack("emblems"+suffix, team.Emblems)
		mr.SetTrack("artifacts"+suffix, team.Artifacts)
		mr.SetTrack("links"+suffix, team.Links)
		if team.Rite != nil {
			mr.SetTrack("mark"+suffix, team.Rite.Grids)
			team.Rite.Grids = nil
			mr.SetTrack("rite"+suffix, team.Rite)
		}
		mr.SetTrack("remains"+suffix, team.Remains)
	}
	return true
}

// 记录资源详情
// @param colName 属性名
// @param awardStr 奖励字符串
func (mr *MsgRecorder) SetResourceContentMessage(colName string, awardStr string) {
	var resources []*cl.Resource
	err := Json.Unmarshal([]byte(awardStr), &resources)
	if err != nil {
		l4g.Error("[SetSummonAwardsMessage] evt:%s json unmarshal error: %v, awardStr: %s", mr.evt, err, awardStr)
		return
	}
	size := len(resources)
	if size == 0 {
		l4g.Error("[SetSummonAwardsMessage] evt:%s no resource, awardStr: %s", mr.evt, awardStr)
		return
	}

	//类型转换
	newResource := make([]*sub.ResourceStr, 0, size)
	for _, v := range resources {
		res := &sub.ResourceStr{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count,
		}
		if v.Id > 0 {
			res.Id = strconv.FormatUint(uint64(v.Id), 10)
		}
		if v.TotalCount > 0 {
			res.TotalCount = strconv.FormatUint(uint64(v.TotalCount), 10)
		}
		if len(v.Attrs) > 0 {
			res.Attrs = make([]*sub.AttrStr, 0, len(v.Attrs))
			for _, vv := range v.Attrs {
				res.Attrs = append(res.Attrs, &sub.AttrStr{
					Type:  vv.Type,
					Value: strconv.FormatUint(uint64(vv.Value), 10),
				})
			}
		}

		newResource = append(newResource, res)
	}
	mr.SetTrack(colName, newResource)
}

func (mr *MsgRecorder) SetSeasonLinkRuneMessage(runeStr string) bool {
	var sRune *sub.SeasonLinkRune
	err := Json.Unmarshal([]byte(runeStr), &sRune)
	if err != nil {
		l4g.Errorf("[SetSeasonLinkRuneMessage] json unmarshal error: %v, runeStr: %s", err, runeStr)
		return false
	}

	if sRune == nil {
		l4g.Errorf("[SetSeasonLinkRuneMessage] rune is nil")
		return false
	}

	mr.SetTrack("rune_detail", sRune)
	return true
}
