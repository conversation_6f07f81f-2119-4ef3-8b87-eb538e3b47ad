package config

import (
	"time"

	"github.com/spf13/viper"
)

const AREA_CN = "cn"

var GArea string

type Kafka struct {
	Brokers []string
	Topics  []string
	//StartPartition int32
	//EndPartition   int32
	ClientID        string
	ConsumerGroup   string
	Offset          string
	BalanceStrategy string
	ConsumerTicker  time.Duration
	//ConsumerFetchSize      int32
	//ConsumerFetchMinSize   int32
	//ConsumerNetReadTimeout int32
	//Target                 string //目标名字
	Version string
}

func NewKafka(consumer string) *Kafka {
	kafka := viper.Sub("kafka")
	consumerCfg := kafka.Sub(consumer)
	return &Kafka{
		Brokers:         kafka.GetStringSlice("brokers"),
		Offset:          kafka.GetString("offset"),
		BalanceStrategy: kafka.GetString("balance_strategy"),
		ConsumerTicker:  kafka.GetDuration("consumer_ticker"),
		Version:         kafka.GetString("version"),
		Topics:          consumerCfg.GetStringSlice("topics"),
		ClientID:        consumerCfg.GetString("client_id"),
		ConsumerGroup:   consumerCfg.GetString("consumer_group"),
		//StartPartition:         kafka.GetInt32("start_partition"),
		//EndPartition:           kafka.GetInt32("end_partition"),
		//ConsumerFetchSize:      kafka.GetInt32("consumer_fetch_size"),
		//ConsumerFetchMinSize:   kafka.GetInt32("consumer_fetch_min_size"),
		//ConsumerNetReadTimeout: kafka.GetInt32("consumer_net_read_timeout"),
		//Target:                 consumer,
	}
}

type ThinkingData struct {
	LogDir    string
	BatchSize uint32
}

func LoadArea() {
	GArea = viper.GetString("area")
}

func NewThinkingData() *ThinkingData {
	ta := viper.Sub("thinkingdata")
	return &ThinkingData{
		LogDir:    ta.GetString("log_dir"),
		BatchSize: ta.GetUint32("batch_size"),
	}
}

type WmData struct {
	LogDir string
}

func NewWmData() *WmData {
	ta := viper.Sub("wmdata")
	return &WmData{
		LogDir: ta.GetString("log_dir"),
	}
}

type LevelDB struct {
	Dir             string
	CacheSize       int
	BlockSize       int
	WriteBufferSize int
	MaxOpenFiles    int
}

func NewLevelDB() *LevelDB {
	db := viper.Sub("leveldb")
	return &LevelDB{
		Dir:             db.GetString("dir"),
		CacheSize:       db.GetInt("cache_size"),
		BlockSize:       db.GetInt("block_size"),
		WriteBufferSize: db.GetInt("write_buffer_size"),
		MaxOpenFiles:    db.GetInt("max_open_size"),
	}
}

type QmConfig struct {
	AppID    uint32
	GameID   uint32
	Key      string
	Url      string
	LogLimit uint32
	LogDir   string
}

func NewQmConfig() *QmConfig {
	qm := viper.Sub("qm")
	return &QmConfig{
		AppID:    qm.GetUint32("app_id"),
		GameID:   qm.GetUint32("game_id"),
		Key:      qm.GetString("key"),
		Url:      qm.GetString("url"),
		LogLimit: qm.GetUint32("log_limit"),
		LogDir:   qm.GetString("log_dir"),
	}
}

type GravityConfig struct {
	Url          string
	AccessTokens map[string]string
	LogDir       string
	Debug        bool
}

func NewGravityConfig() *GravityConfig {
	gravity := viper.Sub("gravity")
	return &GravityConfig{
		Url:          gravity.GetString("url"),
		AccessTokens: gravity.GetStringMapString("access_tokens"),
		Debug:        gravity.GetBool("debug"),
		LogDir:       gravity.GetString("log_dir"),
	}
}
