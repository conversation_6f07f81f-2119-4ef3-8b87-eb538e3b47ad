package gravitylogger

import (
	"adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func (l *Logger) RegisterMessages(msgMgr *MessageM) {
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_REGISTER), 0, &RegisterMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LOGIN), uint32(log.SUB_TYPE_ID_LOGIN_SUCCESS), &LoginMessage{})
	// msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LOGOUT), 0, &LogoutMessage{})
	// msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LEVEL_UP), 0, &LevelUpMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_ORDER_PROCESS), &AddCashMessage{})
}
