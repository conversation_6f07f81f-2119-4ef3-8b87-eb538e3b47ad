package logger

import (
	"fmt"

	"adapter/service/config"
	"adapter/service/helper"
	"adapter/service/kafka_consumer"

	"github.com/Shopify/sarama"
	"github.com/ThinkingDataAnalytics/go-sdk/thinkingdata"
	"gitlab.qdream.com/kit/library/sea/ctx"
	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

type Logger struct {
	topic     string
	partition int32
	batchSize uint32

	cfg *config.ThinkingData
	ta  thinkingdata.TDAnalytics

	msgMgr *helper.MessageM
}

func NewLogger() *Logger {
	logger := &Logger{cfg: config.NewThinkingData()}
	logger.msgMgr = helper.NewMessageM(logger)
	return logger
}

func (l *Logger) Init(topic string, partition int32, group *ctx.Group) {
	l.topic = topic
	l.partition = partition

	//dir := fmt.Sprintf("%s/%d/", l.cfg.LogDir, partition)

	consumer, err := thinkingdata.NewLogConsumer(l.cfg.LogDir, thinkingdata.ROTATE_HOURLY)
	if err != nil {
		panic(fmt.Sprintf("[Logger] new log consumer error:%v", err))
	}

	l.ta = thinkingdata.New(consumer)
	l.RegisterMessages()
}

func (l *Logger) Close() {
	l.ta.Close()
	l4g.Info("Logger topic:%s, partition:%d closed", l.topic, l.partition)
}

func (l *Logger) Write(msg *sarama.ConsumerMessage) kafka_consumer.Status {
	log := &da.Log{}
	if err := helper.Json.Unmarshal(msg.Value, log); err != nil {
		l4g.Error("[Logger] json unmarshal error:%v", err)
		return kafka_consumer.Success
	}
	if !l.msgMgr.Dispatcher(log) {
		l4g.Error("[Logger] dispatch error, topic: %s, partition: %d, info: %+v", l.topic, l.partition, log)
		return kafka_consumer.Fail
	}
	//l4g.Debugf("[Logger] topic:%s, partition:%d info:%+v", l.topic, l.partition, log)
	//msgs := l.getMsgsBufferFromPool()
	//for _, msg := range msgs.getMessages(log) {
	//	if !l.OnWrite(msg) {
	//		return kafka_consumer.Fail
	//	}
	//}

	if l.batchSize >= l.cfg.BatchSize {
		return l.Sync()
	}

	return kafka_consumer.Success
}

func (l *Logger) Sync() kafka_consumer.Status {
	defer func() {
		l.batchSize = 0
	}()

	l.ta.Flush()
	l4g.Debug("[Logger] log file flush success, batch size:%d topic:%s partition:%d",
		l.batchSize, l.topic, l.partition)
	return kafka_consumer.Success
}

func (l *Logger) SyncQianMu() kafka_consumer.Status {
	return kafka_consumer.Success
}

func (l *Logger) OnWrite(msg kafka_consumer.MsgRecorderI) bool {
	uid := msg.GetUserID()
	distinctID := msg.GetDistinctID()
	evt := msg.GetEvent()
	//行为日志记录
	if err := l.ta.Track(uid, distinctID, evt, msg.GetTracks()); err != nil {
		l4g.Error("[Logger] write %s track error: %v, tracks: %+v", evt, err, msg.GetTracks())
		return false
	}
	l.batchSize++
	//用户属性更新
	if inits := msg.GetInits(); len(inits) > 0 {
		if err := l.ta.UserSetOnce(uid, distinctID, inits); err != nil {
			l4g.Error("[Logger] write %s user set once error: %v, inits: %+v", evt, err, inits)
			return false
		}
		l.batchSize++
	}
	if afters := msg.GetAfters(); len(afters) > 0 {
		if err := l.ta.UserSet(uid, distinctID, afters); err != nil {
			l4g.Error("[Logger] write %s user set error: %v, afters: %+v", evt, err, afters)
			return false
		}
		l.batchSize++
	}
	if adds := msg.GetAdds(); len(adds) > 0 {
		if err := l.ta.UserAdd(uid, distinctID, adds); err != nil {
			l4g.Error("[Logger] write %s user add error: %v, adds: %+v", evt, err, adds)
			return false
		}
		l.batchSize++
	}
	return true
}
