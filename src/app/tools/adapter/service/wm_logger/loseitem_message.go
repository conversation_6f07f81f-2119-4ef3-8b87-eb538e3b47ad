package logger

import (
	"adapter/service/helper"
	"adapter/service/proto/out/cl"
	"adapter/service/proto/out/common"
	"fmt"

	l4g "github.com/ivanabc/log4go"

	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

type LoseItemMessage struct {
	msg []Messager
}

type LoseItemMessageWm struct {
	BaseMessage
	Level    uint32 `json:"lev"`
	VipLevel uint32 `json:"viplev"`
	ItemType string `json:"itemtype"`
	ItemId   string `json:"itemid"`
	//ItemUid   uint64 `json:"itemuid"`
	ItemCount uint32 `json:"itemcount"`
	ItemLeft  uint64 `json:"itemleft"`
	Quality   string `json:"quality"`
	ItemPath  string `json:"itempath"`
	SubPath   string `json:"subpath"`
	Scene     string `json:"scene"`
}

func (m *LoseItemMessage) Execute(log *da.Log) ([]Messager, error) {
	var resources []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param1), &resources)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}
	var items []*cl.Resource
	for _, resource := range resources {
		if resource.Type == uint32(common.RESOURCE_ITEM) ||
			resource.Type == uint32(common.RESOURCE_FRAGMENT) ||
			resource.Type == uint32(common.RESOURCE_EQUIP) ||
			resource.Type == uint32(common.RESOURCE_GEM) ||
			resource.Type == uint32(common.RESOURCE_ARTIFACT_FRAGMENT) ||
			resource.Type == uint32(common.RESOURCE_EMBLEM) ||
			resource.Type == uint32(common.RESOURCE_ARTIFACT) {
			items = append(items, resource)
		}
	}

	if len(items) == 0 {
		return nil, nil
	}

	if m.msg == nil {
		m.msg = make([]Messager, 0, len(items))
	}
	index := 0
	for _, item := range items {
		var message *LoseItemMessageWm
		if len(m.msg) >= index+1 {
			message = m.msg[index].(*LoseItemMessageWm)
		} else {
			message = &LoseItemMessageWm{}
			m.msg = append(m.msg, message)
		}
		baseMessage := GetBaseFromLog(log, "loseitem", index, MessageLoseItem)
		message.BaseMessage = baseMessage
		message.FileName = "ads.format.log"
		message.Level = log.Level
		message.VipLevel = log.VipLevel
		message.ItemType = strconv.Itoa(int(item.Type))
		message.ItemId = fmt.Sprintf("%d-%d", item.Type, item.Value)
		//message.ItemUid = item.Id
		message.ItemCount = item.Count
		if item.Type == uint32(common.RESOURCE_TOKEN) || item.Type == uint32(common.RESOURCE_ITEM) ||
			item.Type == uint32(common.RESOURCE_FRAGMENT) || item.Type == uint32(common.RESOURCE_ARTIFACT_FRAGMENT) {
			message.ItemLeft = item.TotalCount
		} else { // 其余的资源都是不可叠加的,left都为0
			message.ItemLeft = 0
		}
		//message.Quality
		message.ItemPath = strconv.Itoa(int(log.LogSubType))
		//message.SubPath =
		message.Scene = ""
		l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
			message.Key, message.Logname, message.RoleID, message.Logtime)
		index++
	}
	return m.msg[0:index], nil
}

func (m *LoseItemMessage) Clear() {

}
