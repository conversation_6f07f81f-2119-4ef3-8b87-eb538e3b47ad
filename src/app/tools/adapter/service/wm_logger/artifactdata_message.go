package logger

import (
	"adapter/service/helper"
	logP "adapter/service/proto/log"
	"adapter/service/proto/out/common"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type ArtifactDataMessage struct {
	msg *ArtifactDataMessageWm
}

type ArtifactDataMessageWm struct {
	BaseMessage
	Level      uint32  `json:"lev"`
	Exp        uint64  `json:"exp"`
	TotalCash  float32 `json:"totalcash"`
	VipLevel   uint32  `json:"viplev"`
	Id         string  `json:"artifactid"`
	StrengthLv uint32  `json:"artifact_lev"`
	ForgeLv    uint32  `json:"artifact_stage"`
	Star       uint32  `json:"artifact_star"`
	Score      int64   `json:"artifact_score"`
}

func (m *ArtifactDataMessage) Execute(log *da.Log) ([]Messager, error) {

	var artifactSnapshot *logP.ArtifactSnapshotForWm
	err := helper.Json.Unmarshal([]byte(log.Param1), &artifactSnapshot)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}
	if m.msg == nil {
		m.msg = &ArtifactDataMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "artifactdata", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "artifactdata.snapshot"
	m.msg.Level = log.Level
	m.msg.Exp = log.Param10
	m.msg.VipLevel = log.VipLevel
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	m.msg.Id = fmt.Sprintf("%d-%d", uint32(common.RESOURCE_ARTIFACT), artifactSnapshot.Id)
	m.msg.StrengthLv = artifactSnapshot.Level
	m.msg.ForgeLv = artifactSnapshot.Stage
	m.msg.Star = artifactSnapshot.Star
	m.msg.Score = artifactSnapshot.Score

	return []Messager{m.msg}, nil
}

func (m *ArtifactDataMessage) Clear() {

}
