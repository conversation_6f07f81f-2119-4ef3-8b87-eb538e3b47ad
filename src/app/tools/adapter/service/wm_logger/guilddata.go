package logger

import (
	"adapter/service/helper"
	logP "adapter/service/proto/log"
	"fmt"
	"strconv"
	"strings"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type GuildDataMessage struct {
	msg *GuildSnapshotMessage
}

type GuildSnapshotMessage struct {
	BaseMessage
	CreateTm                int64  `json:"guild_create_time"`
	GuildName               string `json:"guild_name"`
	GuildId                 uint64 `json:"guild_id"`
	GuildLevel              uint32 `json:"guild_level"`
	GuildMemberNumber       uint32 `json:"guild_member_number"`
	GuildDungeonChapter     uint32 `json:"guild_dungeon_chapter"`
	GuildDungeonBossId      string `json:"guild_dungeon_boss_id"`
	GuildDungeonBossHp      string `json:"guild_dungeon_boss_hp"`
	GuildDungeonSeason      uint32 `json:"guild_dungeon_season"`
	GuildDungeonRound       uint32 `json:"guild_dungeon_round"`
	GuildDungeonZone        uint32 `json:"guild_dungeon_zone"`
	GuildDungeonGrade       uint32 `json:"guild_dungeon_grade"`
	GuildDungeonScore       uint32 `json:"guild_dungeon_score"`
	GuildActivityPoint      uint32 `json:"guild_activity_point"`
	GuildLeaderId           string `json:"guild_leader_id"`
	GuildLeaderName         string `json:"guild_leader_name"`
	GuildDonatePoint        uint32 `json:"guild_donate_point"`
	GuildMemberList         string `json:"guild_member_list"`
	GuildLanguage           string `json:"guild_language"`
	GuildJoinType           uint32 `json:"guild_join_type"`
	GuildTag                uint32 `json:"guild_tag"`
	GuildLevelLimit         uint32 `json:"guild_level_limit"`
	GuildPowerLimit         int64  `json:"guild_power_limit"`
	GuildBadgeScence        uint32 `json:"guild_badge_scence"`
	GuildBadgePicture       uint32 `json:"guild_badge_picture"`
	GuildNotice             string `json:"guild_notice"`
	GuildDeclaration        string `json:"guild_declaration"`
	GuildSettlementDivision uint32 `json:"guild_settlement_division"`
	GuildMedalTask          string `json:"guild_medal_task"`
}

func (m *GuildDataMessage) Execute(log *da.Log) ([]Messager, error) {

	if m.msg == nil {
		m.msg = &GuildSnapshotMessage{}
	}
	baseMessage := GetBaseFromLog(log, "guilddata", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "guilddata.snapshot"
	var guildSnapshot *logP.GuildSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &guildSnapshot)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}
	m.msg.CreateTm = log.CreateTime
	m.msg.GuildId = guildSnapshot.Id
	m.msg.GuildName = guildSnapshot.Name
	m.msg.GuildLevel = guildSnapshot.Level
	m.msg.GuildMemberNumber = guildSnapshot.MemberCnt
	m.msg.GuildDungeonChapter = guildSnapshot.Chapter
	m.msg.GuildDungeonSeason = guildSnapshot.SeasonId
	m.msg.GuildDungeonRound = guildSnapshot.Round
	m.msg.GuildDungeonZone = guildSnapshot.Arena
	m.msg.GuildDungeonGrade = guildSnapshot.Division
	m.msg.GuildDungeonScore = guildSnapshot.Star

	if len(guildSnapshot.BossIds) > 0 {
		m.msg.GuildDungeonBossId = util.Uint32SliceToString(guildSnapshot.BossIds, ",")
	}

	if len(guildSnapshot.BossHpPct) > 0 {
		bossHpS := make([]string, 0, len(guildSnapshot.BossHpPct))
		for _, bossHpPct := range guildSnapshot.BossHpPct {
			bossHpS = append(bossHpS, fmt.Sprintf("%.2f%%", float32(bossHpPct)/100))
		}
		m.msg.GuildDungeonBossHp = strings.Join(bossHpS, ",")
	}

	m.msg.GuildActivityPoint = guildSnapshot.ActivityPoints

	m.msg.GuildLeaderId = strconv.FormatUint(guildSnapshot.LeaderId, 10)
	m.msg.GuildLeaderName = guildSnapshot.LeaderName
	m.msg.GuildDonatePoint = guildSnapshot.DonatePoint
	m.msg.GuildMemberList = util.Uint64SliceToString(guildSnapshot.MemberIds, ",")

	m.msg.GuildLanguage = guildSnapshot.Language
	m.msg.GuildJoinType = guildSnapshot.JoinType
	m.msg.GuildTag = guildSnapshot.Label
	m.msg.GuildLevelLimit = guildSnapshot.LevelLimit
	m.msg.GuildPowerLimit = guildSnapshot.PowerLimit
	m.msg.GuildBadgeScence = guildSnapshot.BadgeBackground
	m.msg.GuildBadgePicture = guildSnapshot.BadgeIcon
	m.msg.GuildNotice = guildSnapshot.Notice
	m.msg.GuildDeclaration = guildSnapshot.Declaration
	m.msg.GuildSettlementDivision = guildSnapshot.SettlementDivision
	m.msg.GuildMedalTask, err = helper.Json.MarshalToString(guildSnapshot.Medals)
	if err != nil {
		l4g.Errorf("generateGuildSnapshotLog: medals to string error. gid:%d err:%s logGuildMedals:%+v",
			guildSnapshot.Id, err, guildSnapshot.Medals)
	}
	return []Messager{m.msg}, nil
}

func (m *GuildDataMessage) Clear() {

}

//func changeSnapshotDate(t time.Time) string {
//	t.Add(-time.Hour * 24)
//	return t.Format("2006-01-02")
//}
