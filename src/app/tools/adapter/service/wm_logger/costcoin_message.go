package logger

import (
	"adapter/service/helper"
	"adapter/service/proto/out/cl"
	"adapter/service/proto/out/common"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

type CostCoinMessage struct {
	msg []Messager
}

type CostCoinMessageWm struct {
	BaseMessage
	Level    uint32 `json:"lev"`
	VipLevel uint32 `json:"viplev"`
	Scene    string `json:"scene"`    //
	CoinType string `json:"cointype"` //
	//CoinId    string `json:"coinid"`
	CoinPath  string `json:"coinpath"`
	Coin      uint32 `json:"coin"`
	TotalCoin uint64 `json:"totalcoin"` // 累计消耗只有金币和钻石有记录
	CoinLeft  uint64 `json:"coinleft"`
	//SubPath   string `json:"subpath"`
}

func (m *CostCoinMessage) Execute(log *da.Log) ([]Messager, error) {

	var resources []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param1), &resources)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}
	var coins []*cl.Resource
	for _, resource := range resources {
		if resource.Type == uint32(common.RESOURCE_TOKEN) ||
			resource.Type == uint32(common.RESOURCE_GOLD) {
			coins = append(coins, resource)
		}
	}

	if len(coins) == 0 {
		return nil, nil
	}

	if m.msg == nil {
		m.msg = make([]Messager, 0, len(coins))
	}
	index := 0
	for _, coin := range coins {
		var message *CostCoinMessageWm
		if len(m.msg) >= index+1 {
			message = m.msg[index].(*CostCoinMessageWm)
		} else {
			message = &CostCoinMessageWm{}
			m.msg = append(m.msg, message)
		}
		baseMessage := GetBaseFromLog(log, "costcoin", index, MessageCostCoin)
		message.BaseMessage = baseMessage
		message.FileName = "ads.format.log"
		message.Level = log.Level
		message.VipLevel = log.VipLevel
		message.Scene = ""
		//message.CoinType = coin.Type
		if coin.Type == uint32(common.RESOURCE_GOLD) {
			message.CoinType = strconv.Itoa(int(coin.Type))
		} else {
			message.CoinType = fmt.Sprintf("%d-%d", coin.Type, coin.Value)
		}
		message.CoinPath = strconv.Itoa(int(log.LogSubType))
		message.Coin = coin.Count
		if coin.Type == uint32(common.RESOURCE_GOLD) {
			message.TotalCoin = log.Param11
			message.CoinLeft = log.Gold
		} else {
			message.TotalCoin = 0
			message.CoinLeft = coin.TotalCount
		}
		l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
			message.Key, message.Logname, message.RoleID, message.Logtime)
		//message.SubPath = log.Param10
		index++
	}
	return m.msg[0:index], nil
}

func (m *CostCoinMessage) Clear() {

}
