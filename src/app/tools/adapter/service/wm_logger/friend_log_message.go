package logger

import (
	"adapter/service/helper"
	"strconv"
	"strings"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type FriendLogMessage struct {
	msg *FriendLogMessageWm
}

type FriendLogMessageWm struct {
	BaseMessage
	Level       uint32  `json:"lev"`
	VipLevel    uint32  `json:"viplev"`
	TotalCash   float32 `json:"totalcash"`
	Friend      string  `json:"friend"`
	TotalFriend uint32  `json:"totalfriend"`
	Custom1_    string  `json:"custom_1"`
	Custom2_    string  `json:"custom_2"`
	Custom3_    string  `json:"custom_3"`
	Custom4_    string  `json:"custom_4"`
	Custom5_    string  `json:"custom_5"`
}

func (m *FriendLogMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &FriendLogMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "friend_log", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "friend.snapshot"
	m.msg.Logname = baseMessage.Logname
	m.msg.Level = log.Level
	m.msg.VipLevel = log.VipLevel
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	m.msg.Friend = ""
	m.msg.TotalFriend = 0
	var friendIds []uint64
	if log.Param1 != "" {
		err := helper.Json.Unmarshal([]byte(log.Param1), &friendIds)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return []Messager{m.msg}, nil
		}
	}
	var fids []string
	for _, id := range friendIds {
		fids = append(fids, strconv.Itoa(int(id)))
	}
	m.msg.Friend = strings.Join(fids, ",")
	m.msg.TotalFriend = uint32(len(friendIds))
	return []Messager{m.msg}, nil
}

func (m *FriendLogMessage) Clear() {

}
