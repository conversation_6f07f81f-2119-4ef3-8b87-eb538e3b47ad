package logger

import (
	"gitlab.qdream.com/platform/proto/da"
)

const (
	SnapshotFileNum int = 5
)

var snapFileName = [SnapshotFileNum]string{
	"chardata.snapshot",     // 角色快照
	"fighterdata.snapshot",  //伙伴快照
	"chardatabag.snapshot",  // 背包快照
	"friend.snapshot",       // 好友快照
	"artifactdata.snapshot", // 神器快照
}

type SnapshotEndMessage struct {
	msg []Messager
}

type SnapshotEndMessageWm struct {
	FileName string `json:"-"`
	Date     string `json:"-"`
	ServerID uint64 `json:"-"`
}

func (m *SnapshotEndMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = make([]Messager, 0, SnapshotFileNum)
	}
	index := 0
	for ; index < SnapshotFileNum; index++ {
		var message *SnapshotEndMessageWm
		if len(m.msg) >= index+1 {
			message = m.msg[index].(*SnapshotEndMessageWm)
		} else {
			message = &SnapshotEndMessageWm{}
			m.msg = append(m.msg, message)
		}
		baseMessage := GetBaseFromLog(log, "", index, MessageNone)
		message.FileName = snapFileName[index]
		message.Date = baseMessage.Date
		message.ServerID = baseMessage.ServerID
	}
	return m.msg[0:index], nil

}

func (msg *SnapshotEndMessageWm) GetServerID() uint64 {
	return msg.ServerID
}

func (msg *SnapshotEndMessageWm) GetDate() string {
	return msg.Date
}

func (msg *SnapshotEndMessageWm) GetFilename() string {
	return msg.FileName
}

func (msg *SnapshotEndMessageWm) GetMessageOfString() string {
	return "END$$$"
}

func (m *SnapshotEndMessage) Clear() {

}
