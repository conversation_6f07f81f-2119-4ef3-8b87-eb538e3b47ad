package logger

import (
	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

type RoleGuidMessage struct {
	msg *RoleGuidMessageWm
}

type RoleGuidMessageWm struct {
	BaseMessage
	Level     uint32  `json:"lev"`
	TotalCash float32 `json:"totalcash"`
	VipLevel  uint32  `json:"viplev"`
	TaskId    string  `json:"task_id"`
	PreTaskId string  `json:"pre_task_id"`
	StepId    string  `json:"step_id"`
	PreStepId string  `json:"pre_step_id"`
	Status    uint32  `json:"status"` // 引导完成状态；任务的状态，1:到达 2:完成 3:失败 4:其他
	Custom1   string  `json:"custom_1"`
	Custom2   string  `json:"custom_2"`
	Custom3   string  `json:"custom_3"`
	Custom4   string  `json:"custom_4"`
	Custom5   string  `json:"custom_5"`
}

func (m *RoleGuidMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &RoleGuidMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "roleguid", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	m.msg.VipLevel = log.VipLevel
	m.msg.TaskId = strconv.FormatUint(log.Param10, 10)
	m.msg.PreTaskId = ""
	m.msg.StepId = strconv.FormatUint(log.Param11, 10)
	m.msg.PreStepId = ""
	//if m.msg.StepId == 0 {
	//	m.msg.Status = 2
	//} else {
	m.msg.Status = 1
	//}
	return []Messager{m.msg}, nil
}

func (m *RoleGuidMessage) Clear() {

}
