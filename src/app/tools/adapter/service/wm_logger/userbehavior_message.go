package logger

import (
	logProto "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

type UserBehaviorMessage struct {
	msg *UserBehaviorMessageWm
}

type UserBehaviorMessageWm struct {
	BaseMessage
	Level         uint32  `json:"lev"`
	TotalCash     float32 `json:"totalcash"`      //角色累计充值金额	以美元为单位，例如19.99
	VipLevel      uint32  `json:"viplev"`         //角色VIP等级
	BehaviorType  uint32  `json:"behaviortype"`   // 行为类型   如： 商店刷新
	BehaviorType2 uint32  `json:"behaviortype_2"` // 子类型     如： 商店刷新时设置商店id
	Custom1       uint32  `json:"custom_1"`       // 自定义参数  如： 商店刷新时是否免费
}

func (m *UserBehaviorMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &UserBehaviorMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "userbehavior", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.BehaviorType = log.LogSubType
	switch log.LogSubType {
	case uint32(logProto.SUB_TYPE_ID_SHOP_REFRESH):
		m.msg.BehaviorType2 = uint32(log.Param10)
		m.msg.Custom1 = uint32(log.Param11) //1 免费  2 付费
	default:
		m.msg.BehaviorType2 = 0
		m.msg.Custom1 = 0
	}
	return []Messager{m.msg}, nil
}

func (u *UserBehaviorMessage) Clear() {

}
