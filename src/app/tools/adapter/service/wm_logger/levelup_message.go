package logger

import (
	"gitlab.qdream.com/platform/proto/da"
)

type LevelUpMessage struct {
	msg *LevelUpMessageWm
}

type LevelUpMessageWm struct {
	BaseMessage
	Level     uint32  `json:"lev"`
	TotalCash float32 `json:"totalcash"`
}

func (m *LevelUpMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &LevelUpMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "levelup", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	return []Messager{m.msg}, nil
}

func (m *LevelUpMessage) Clear() {

}
