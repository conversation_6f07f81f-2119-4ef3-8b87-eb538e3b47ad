package logger

import (
	"adapter/service/helper"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/platform/proto/da"
)

type LogoutMessage struct {
	msg *LogoutMessageWm
}

type LogoutMessageWm struct {
	BaseMessage
	Type string `json:"type"` //日志输出类型 auto:跨天0点自动输出；user:用户正常登录产生 示例:user
	//Ip              string  `json:"ip"`              //
	SourceServerID  uint64  `json:"source_serverid"` //本服，玩家归属服务器	示例:5556
	Occupation      uint32  `json:"occupation"`      //角色职业，研发确定后提供ID-名称对照表	如有填写对应ID，没有则不输出该字段
	Time            uint64  `json:"time"`            //角色本次在线时长，单位为秒 3423
	Level           uint32  `json:"lev"`
	Exp             uint64  `json:"exp"`
	TotalCash       float32 `json:"totalcash"`       //角色累计充值金额 以美元为单位，例如19.99
	VipLevel        uint32  `json:"viplev"`          //角色VIP等级
	Silver          uint32  `json:"silvercoinleft"`  //  角色当前银币数量 天神填写0
	Gold            uint64  `json:"goldcoinleft"`    //  角色当前金币数量 天神填写金币当前数量
	Diamond         uint64  `json:"yuanbaoleft"`     // 角色当前基础货币剩余 天神填写钻石当前数量
	BindYuanBaoLeft uint32  `json:"bindyuanbaoleft"` // 角色当前绑定游戏内付费的基础货币 天神填写’0‘
}

func (m *LogoutMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &LogoutMessageWm{}
	}
	m.msg.newLogoutMessageWm(log, log.Param20, 0, "user")
	//dailyZero := uint64(util.DailyZeroByTime(int64(log.Param11))) // 按登出时间计算当日0点
	//if dailyZero > log.Param10 {                                  // 跨天
	//	m.msg.Time = util.SafeSubUint64(log.Param11, dailyZero)
	//	message := handle(log)
	//	message = append(message, m.msg)
	//	return message, nil
	//}
	l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
		m.msg.Key, m.msg.Logname, m.msg.RoleID, m.msg.Logtime)
	return []Messager{m.msg}, nil
}

func (m *LogoutMessage) Clear() {

}

func (l *LogoutMessageWm) newLogoutMessageWm(log *da.Log, onlineTime uint64, index int, recordType string) {
	baseMessage := GetBaseFromLog(log, "rolelogout", index, MessageNone)
	l.BaseMessage = baseMessage
	//l.Ip = log.ClientIP
	l.FileName = "ads.format.log"
	l.Type = recordType
	l.SourceServerID = baseMessage.ServerID
	l.Time = onlineTime
	l.Level = log.Level
	l.Exp = log.Param12
	l.TotalCash = dollars2Cents(log.TotalCash)
	l.VipLevel = log.VipLevel
	l.Silver = 0
	l.Gold = log.Gold
	l.Diamond = log.PayDiamond + log.GiftDiamond
	l.BindYuanBaoLeft = 0
}

func handle(log *da.Log) []Messager {
	mes := make([]Messager, 0, 2)
	// 针对跨天的情况开始记录额外事件
	dailyZero := uint64(util.DailyZeroByTime(int64(log.Param11))) // 按登出时间计算当日0点
	onlineTime := log.Param20
	todayOnlineTime := util.SafeSubUint64(log.Param11, dailyZero)
	if todayOnlineTime > onlineTime {
		todayOnlineTime = onlineTime
	}

	//if onlineTime > todayOnlineTime {
	//	logoutMessage(r, log, extra, log.Param11, todayOnlineTime)
	//} else {
	//	logoutMessage(r, log, extra, log.Param11, onlineTime)
	//}

	// 记录跨天的上线和离线日志
	// 需要注意的是跨天之后假设的情况是会不会跨x天以上
	// 因此在最后会修改todayOnlineTime为86400，来检查是否可能出现跨天情况
	var realOnlineTm uint64
	for onlineTime > todayOnlineTime {
		onlineTime -= todayOnlineTime
		loginMessageWm := &LoginMessageWm{}
		loginMessageWm.newLoginMessageWm(log, 1, "auto")
		t, _ := helper.GetLocalTimeInstance(int64(dailyZero), log.Param9)
		loginMessageWm.Logtime = t.Format(TimeLayout)
		mes = append(mes, loginMessageWm)
		realOnlineTm = onlineTime
		if realOnlineTm > util.DaySecs {
			realOnlineTm = util.DaySecs
		}
		logoutMessageWm := &LogoutMessageWm{}
		logoutMessageWm.newLogoutMessageWm(log, realOnlineTm, 2, "auto")
		logoutT, _ := helper.GetLocalTimeInstance(int64(dailyZero-1), log.Param9)
		loginMessageWm.Logtime = logoutT.Format(TimeLayout)
		mes = append(mes, logoutMessageWm)
		dailyZero -= util.DaySecs
		todayOnlineTime = util.DaySecs
	}
	return mes
}
