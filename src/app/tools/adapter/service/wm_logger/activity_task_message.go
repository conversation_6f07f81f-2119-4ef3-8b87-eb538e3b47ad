package logger

import (
	"adapter/service/helper"
	protoLog "adapter/service/proto/log"
	"adapter/service/proto/out/common"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

const (
	TaskTypeAchieve       = 101
	TaskTypeDaily         = 102
	TaskTypeWeekly        = 103
	TaskTypeCarnival      = 104
	TaskTypeMedal         = 105
	TaskTypeSevenDayLogin = 106
)

type ActivityTaskMessage struct {
	msg []Messager
}

type ActivityTaskMessageWm struct {
	BaseMessage
	VipLevel      uint32  `json:"viplev"`
	Level         uint32  `json:"lev"`
	TotalCash     float32 `json:"totalcash"`
	Occupation    string  `json:"occupation"`
	Gender        string  `json:"gender"`
	Power         uint64  `json:"fight"`
	Exp           uint64  `json:"exp"`
	ActivityName  string  `json:"activityname"`
	BatchID       string  `json:"batchid"`
	TaskID        string  `json:"taskid"`
	TaskTypeID    string  `json:"tasktypeid"`
	Operation     string  `json:"operation"`
	ActivityLevel uint32  `json:"activityLev"`
}

func (m *ActivityTaskMessage) Execute(log *da.Log) ([]Messager, error) {
	if log.LogSubType == uint32(protoLog.SUB_TYPE_ID_SEVENDAY_LOGIN_TAKEAWARD) {
		return m.handleSevenDay(log)
	}
	var ids []uint32
	err := helper.Json.Unmarshal([]byte(log.Param1), &ids)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, fmt.Errorf("json unmarshakl error %v", err)
	}
	if len(ids) == 0 {
		return nil, nil
	}
	if m.msg == nil {
		m.msg = make([]Messager, 0, len(ids))
	}
	taskType := uint32(0)
	switch log.LogSubType {
	case uint32(protoLog.SUB_TYPE_ID_TASK_RECEIVE_AWARD):
		if log.Param10 == uint64(common.TASK_RESET_TYPE_RET_ACHIEVE) {
			taskType = TaskTypeAchieve
		} else if log.Param10 == uint64(common.TASK_RESET_TYPE_RET_DAILY) {
			taskType = TaskTypeDaily
		} else if log.Param10 == uint64(common.TASK_RESET_TYPE_RET_WEEKLY) {
			taskType = TaskTypeWeekly
		} else {
			l4g.Error("[ActivityTaskMessage]: error task type:%d", log.Param10)
			return nil, nil
		}
	case uint32(protoLog.SUB_TYPE_ID_CARNIVAL_RECEIVE_AWARD):
		taskType = TaskTypeCarnival
	case uint32(protoLog.SUB_TYPE_ID_MEDAL_RECEIVE_AWARD):
		taskType = TaskTypeMedal
	default:
		l4g.Error("[ActivityTaskMessage]: error task type:%d", log.LogSubType)
		return nil, nil
	}
	index := 0
	for _, v := range ids {
		var message *ActivityTaskMessageWm
		if len(m.msg) >= index+1 {
			message = m.msg[index].(*ActivityTaskMessageWm)
		} else {
			message = &ActivityTaskMessageWm{}
			m.msg = append(m.msg, message)
		}
		baseMessage := GetBaseFromLog(log, "activity_task", index, MessageNone)
		message.BaseMessage = baseMessage
		message.FileName = "ads.format.log"
		message.VipLevel = log.VipLevel
		message.Level = log.Level
		message.TotalCash = dollars2Cents(log.TotalCash)
		message.Power = log.Power
		message.TaskID = strconv.Itoa(int(v))
		message.TaskTypeID = strconv.Itoa(int(taskType))
		message.ActivityLevel = 0
		if taskType != TaskTypeCarnival {
			message.ActivityLevel = uint32(log.Param11)
		}
		l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
			message.Key, message.Logname, message.RoleID, message.Logtime)
		index++
	}
	return m.msg[0:index], nil
}

func (m *ActivityTaskMessage) handleSevenDay(log *da.Log) ([]Messager, error) {
	index := 0
	var message *ActivityTaskMessageWm
	if len(m.msg) >= index+1 {
		message = m.msg[index].(*ActivityTaskMessageWm)
	} else {
		message = &ActivityTaskMessageWm{}
		m.msg = append(m.msg, message)
	}
	baseMessage := GetBaseFromLog(log, "activity_task", index, MessageNone)
	message.BaseMessage = baseMessage
	message.FileName = "ads.format.log"
	message.VipLevel = log.VipLevel
	message.Level = log.Level
	message.TotalCash = dollars2Cents(log.TotalCash)
	message.Power = log.Power
	message.TaskID = strconv.Itoa(int(log.Param10))
	message.TaskTypeID = strconv.Itoa(int(TaskTypeSevenDayLogin))
	message.ActivityLevel = 0
	l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
		message.Key, message.Logname, message.RoleID, message.Logtime)
	index++
	return m.msg[0:index], nil
}

func (m *ActivityTaskMessage) Clear() {

}
