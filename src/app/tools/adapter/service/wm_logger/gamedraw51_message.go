package logger

import (
	"adapter/service/helper"
	"adapter/service/proto/out/cl"
	"adapter/service/proto/out/common"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

type GameDraw51Message struct {
	msg *GameDraw51MessageWm
}

type GameDraw51MessageWm struct {
	BaseMessage
	Level         uint32  `json:"lev"`       //日志输出类型 auto:跨天0点自动输出；user:用户正常登录产生 示例:user
	VipLevel      uint32  `json:"viplev"`    //
	TotalCash     float32 `json:"totalcash"` //
	ActivityId    string  `json:"activityid"`
	ActivityName  string  `json:"activityname"`  //
	LotteryResult uint32  `json:"lotteryresult"` //
	//CostYuanBaoType string `json:"costyuanbaotype"` //
	//CostYuanBaoNum  string `json:"costyuanbaonum"`  //
	//YuanBaoLeft     uint64 `json:"yuanbaoleft"`     //
	CostCoinType string `json:"costcointype"` //
	//CostCoinId     string `json:"costcoinid"`
	CostCoinNum   string `json:"costcoinnum"`    //
	CoinLeft      uint64 `json:"coinleft"`       //
	CostCoinType2 string `json:"costcointype_2"` //  todo 预留字段，之后一次抽卡会消耗多种资源的话使用
	CostCoinNum2  string `json:"costcoinnum_2"`  //
	CoinLeft2     uint64 `json:"coinleft_2"`     //
	CostCoinType3 string `json:"costcointype_3"` //  todo 预留字段，之后一次抽卡会消耗多种资源的话使用
	CostCoinNum3  string `json:"costcoinnum_3"`  //
	CoinLeft3     uint64 `json:"coinleft_3"`     //
	//CostItemType   string `json:"costitemtype"` //
	//CostItemId     string `json:"costitemid"`
	//CostItemNum    string `json:"costitemnum"`     //
	//ItemLeft       uint64 `json:"itemleft"`        //
	AwardItemId    string `json:"awarditemid"`     //
	AwardItemName  string `json:"awarditemname"`   //
	AwardItemNum   string `json:"awarditemnum"`    //
	AwardItemId2   string `json:"awarditemid_2"`   //
	AwardItemNum2  string `json:"awarditemnum_2"`  //
	AwardItemId3   string `json:"awarditemid_3"`   //
	AwardItemNum3  string `json:"awarditemnum_3"`  //
	AwardItemId4   string `json:"awarditemid_4"`   //
	AwardItemNum4  string `json:"awarditemnum_4"`  //
	AwardItemId5   string `json:"awarditemid_5"`   //
	AwardItemNum5  string `json:"awarditemnum_5"`  //
	AwardItemId6   string `json:"awarditemid_6"`   //
	AwardItemNum6  string `json:"awarditemnum_6"`  //
	AwardItemId7   string `json:"awarditemid_7"`   //
	AwardItemNum7  string `json:"awarditemnum_7"`  //
	AwardItemId8   string `json:"awarditemid_8"`   //
	AwardItemNum8  string `json:"awarditemnum_8"`  //
	AwardItemId9   string `json:"awarditemid_9"`   //
	AwardItemNum9  string `json:"awarditemnum_9"`  //
	AwardItemId10  string `json:"awarditemid_10"`  //
	AwardItemNum10 string `json:"awarditemnum_10"` //
	AwardItemId11  string `json:"awarditemid_11"`  //
	AwardItemNum11 string `json:"awarditemnum_11"` //
	DrawNum        uint32 `json:"drawnum"`         //
}

func (m *GameDraw51Message) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &GameDraw51MessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "gamedraw51", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"

	m.msg.Level = log.Level
	m.msg.VipLevel = log.VipLevel
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	m.msg.ActivityId = strconv.FormatUint(log.Param10, 10) // 抽卡ID
	m.msg.ActivityName = ""
	m.msg.LotteryResult = uint32(1) // 完成才会记录日志，所以写死1完成
	//	m.msg.CostYuanBaoType = "2"
	//	m.msg.YuanBaoLeft = log.PayDiamond + log.GiftDiamond
	//	m.msg.CoinLeft = log.Param16
	//	m.msg.ItemLeft = log.Param17
	var costs, awards []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param1), &costs)
	if err == nil && len(costs) != 0 {
		for index, cost := range costs {
			if index >= 1 {
				break
			}
			if cost.Type == uint32(common.RESOURCE_ITEM) {
				m.msg.CostCoinType = fmt.Sprintf("%d-%d", cost.Type, cost.Value)
				m.msg.CostCoinNum = strconv.Itoa(int(cost.Count))
				m.msg.CoinLeft = log.Param16
			} else if cost.Type == uint32(common.RESOURCE_TOKEN) {
				m.msg.CostCoinType = fmt.Sprintf("%d-%d", cost.Type, cost.Value)
				m.msg.CostCoinNum = strconv.Itoa(int(cost.Count))
				m.msg.CoinLeft = log.Param17
			} else if cost.Type == uint32(common.RESOURCE_DIAMOND) {
				m.msg.CostCoinType = strconv.Itoa(int(cost.Type))
				m.msg.CostCoinNum = strconv.Itoa(int(cost.Count))
				m.msg.CoinLeft = log.PayDiamond + log.GiftDiamond
			} else if cost.Type == uint32(common.RESOURCE_GOLD) {
				m.msg.CostCoinType = strconv.Itoa(int(cost.Type))
				m.msg.CostCoinNum = strconv.Itoa(int(cost.Count))
				m.msg.CoinLeft = log.Gold
			}
		}
	}

	err = helper.Json.Unmarshal([]byte(log.Param2), &awards)
	if err == nil && len(awards) != 0 {

		for index := 0; index < 11; index++ {
			awardTyp, awardValue := uint32(0), uint32(0)
			awardCount := "0"
			if index+1 <= len(awards) {
				awardTyp = awards[index].Type
				awardValue = awards[index].Value
				awardCount = strconv.Itoa(int(awards[index].Count))
			}
			if index+1 == 1 {
				m.msg.AwardItemId = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum = awardCount
			} else if index+1 == 2 {
				m.msg.AwardItemId2 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum2 = awardCount
			} else if index+1 == 3 {
				m.msg.AwardItemId3 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum3 = awardCount
			} else if index+1 == 4 {
				m.msg.AwardItemId4 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum4 = awardCount
			} else if index+1 == 5 {
				m.msg.AwardItemId5 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum5 = awardCount
			} else if index+1 == 6 {
				m.msg.AwardItemId6 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum6 = awardCount
			} else if index+1 == 7 {
				m.msg.AwardItemId7 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum7 = awardCount
			} else if index+1 == 8 {
				m.msg.AwardItemId8 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum8 = awardCount
			} else if index+1 == 9 {
				m.msg.AwardItemId9 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum9 = awardCount
			} else if index+1 == 10 {
				m.msg.AwardItemId10 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum10 = awardCount
			} else if index+1 == 11 {
				m.msg.AwardItemId11 = fmt.Sprintf("%d-%d", awardTyp, awardValue)
				m.msg.AwardItemNum11 = awardCount
			} else {
				break
			}
		}
	}
	m.msg.DrawNum = uint32(log.Param11)
	l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
		m.msg.Key, m.msg.Logname, m.msg.RoleID, m.msg.Logtime)
	return []Messager{m.msg}, nil
}

func (m *GameDraw51Message) Clear() {

}
