package logger

import (
	"adapter/service/helper"
	logP "adapter/service/proto/log"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type GstGuildDataMessage struct {
	msg *GstGuildSnapshotMessage
}

type GstGuildSnapshotMessage struct {
	BaseMessage
	GuildName           string `json:"guild_name"`            // 公会名称
	GuildId             uint64 `json:"guild_id"`              // 公会Id
	GuildSandTableZone  uint32 `json:"guild_sandtable_zone"`  // 战区
	GuildSandTableRound uint32 `json:"guild_sandtable_round"` // 轮次
	GuildSandTableTurn  uint32 `json:"guild_sandtable_turn"`  // 回合
	GuildSandTableRoom  uint64 `json:"guild_sandtable_room"`  // 房间
	GuildSandTableScore uint32 `json:"guild_sandtable_score"` // 地块积分
	MapId               uint32 `json:"map_id"`
	DivisionScore       uint32 `json:"guild_sandtable_point"`
	GroundNum           uint32 `json:"guild_space_count"`
	GuildRank           uint32 `json:"guild_rank"`
	BlessInfo           string `json:"bless_times"`
	GroundTypeCount     string `json:"space_ids"`
	UsersScore          string `json:"guild_user_score"`
	GuildBuildLevel     string `json:"guild_build_level"`
	GuildSpaceTaken     string `json:"guild_space_taken"`
	ArenaUserInfo       string `json:"guild_sandtable_arena_userinfo"`
	ArenaRound          uint32 `json:"guild_sandtable_arena_round"`
	ArenaRank           uint32 `json:"guild_sandtable_arena_rank"`
	ArenaWins           uint32 `json:"guild_sandtable_arena_wins"`
	GSTDivision         uint32 `json:"guild_sandtable_grade"`
}

func (m *GstGuildDataMessage) Execute(log *da.Log) ([]Messager, error) {

	if m.msg == nil {
		m.msg = &GstGuildSnapshotMessage{}
	}
	baseMessage := GetBaseFromLog(log, "gvgdata", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "gvgdata.snapshot"
	var gstGuildSnapshot *logP.GstGuildSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &gstGuildSnapshot)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}

	m.msg.GuildName = gstGuildSnapshot.GetName()
	m.msg.GuildId = gstGuildSnapshot.GetId()
	m.msg.GuildSandTableZone = gstGuildSnapshot.GetArena()
	m.msg.GuildSandTableRound = gstGuildSnapshot.GetRound()
	m.msg.GuildSandTableTurn = gstGuildSnapshot.GetLRound()
	m.msg.GuildSandTableRoom = gstGuildSnapshot.GetRoom()
	m.msg.GuildSandTableScore = gstGuildSnapshot.GetScore()
	m.msg.MapId = gstGuildSnapshot.GetMapId()
	m.msg.DivisionScore = gstGuildSnapshot.GetDivisionScore()
	m.msg.GroundNum = gstGuildSnapshot.GetGroundNum()
	m.msg.GuildRank = gstGuildSnapshot.GetGuildRank()
	m.msg.BlessInfo = log.Param2
	m.msg.GroundTypeCount = log.Param3
	m.msg.UsersScore = log.Param4
	m.msg.GuildBuildLevel = log.Param5
	m.msg.GuildSpaceTaken = log.Param6
	m.msg.ArenaUserInfo = log.Param7
	m.msg.ArenaRound = gstGuildSnapshot.GetArenaRound()
	m.msg.ArenaRank = gstGuildSnapshot.GetArenaRank()
	m.msg.ArenaWins = gstGuildSnapshot.GetArenaWins()
	m.msg.GSTDivision = gstGuildSnapshot.GetGstDivision()
	return []Messager{m.msg}, nil
}

func (m *GstGuildDataMessage) Clear() {

}

//func changeSnapshotDate(t time.Time) string {
//	t.Add(-time.Hour * 24)
//	return t.Format("2006-01-02")
//}
