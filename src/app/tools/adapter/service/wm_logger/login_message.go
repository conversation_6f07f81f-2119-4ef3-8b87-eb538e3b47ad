package logger

import (
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type LoginMessage struct {
	msg *LoginMessageWm
}

type LoginMessageWm struct {
	BaseMessage
	Type string `json:"type"` //日志输出类型 auto:跨天0点自动输出；user:用户正常登录产生 示例:user
	//Ip              string  `json:"ip"`              //
	DeviceModel     string  `json:"device_model"`    //设备类型，可通过SDK获取	示例:Iphone7
	DeviceSys       string  `json:"device_sys"`      //设备系统，可通过SDK获取	示例:ios 10.1.1
	DeviceRam       int     `json:"device_ram"`      //设备内存，可通过SDK获取	示例:2005
	SourceServerID  uint64  `json:"source_serverid"` //本服，玩家归属服务器	示例:5556
	Occupation      uint32  `json:"occupation"`      //角色职业，研发确定后提供ID-名称对照表	如有填写对应ID，没有则不输出该字段
	Level           uint32  `json:"lev"`
	Exp             uint64  `json:"exp"`
	TotalCash       float32 `json:"totalcash"`       //角色累计充值金额	以美元为单位，例如19.99   todo 确认是美元为单位
	VipLevel        uint32  `json:"viplev"`          //角色VIP等级
	Silver          uint32  `json:"silvercoinleft"`  //  角色当前银币数量 天神填写英雄经验当前数量
	Gold            uint64  `json:"goldcoinleft"`    //  角色当前金币数量	天神填写金币当前数量
	Diamond         uint64  `json:"yuanbaoleft"`     // 角色当前基础货币剩余	天神填写钻石当前数量
	BindYuanBaoLeft uint32  `json:"bindyuanbaoleft"` // 角色当前绑定游戏内付费的基础货币 天神填写’0‘
}

func (m *LoginMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &LoginMessageWm{}
	}
	recordType := "user"
	if log.Param15 == 1 {
		recordType = "auto"
	}
	m.msg.newLoginMessageWm(log, 0, recordType)
	l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
		m.msg.Key, m.msg.Logname, m.msg.RoleID, m.msg.Logtime)
	return []Messager{m.msg}, nil
}

func (m *LoginMessage) Clear() {

}

func (l *LoginMessageWm) newLoginMessageWm(log *da.Log, index int, recordType string) {
	baseMessage := GetBaseFromLog(log, "rolelogin", index, MessageNone)
	l.BaseMessage = baseMessage
	l.Ip = log.ClientIP
	l.FileName = "ads.format.log"
	l.DeviceModel = baseMessage.DeviceModel
	l.DeviceSys = baseMessage.DeviceSys
	l.DeviceRam = baseMessage.DeviceRam
	l.Type = recordType
	l.SourceServerID = baseMessage.ServerID
	l.Level = log.Level
	l.Exp = log.Param12
	l.TotalCash = dollars2Cents(log.TotalCash)
	l.VipLevel = log.VipLevel
	l.Silver = 0
	l.Gold = log.Gold
	l.Diamond = log.PayDiamond + log.GiftDiamond
	l.BindYuanBaoLeft = 0
}
