package logger

import (
	"adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func (l *Logger) RegisterMessages(msgMgr *MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_REGISTER), 0, &RegisterMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LOGIN), uint32(log.SUB_TYPE_ID_LOGIN_SUCCESS), &LoginMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LOGOUT), 0, &LogoutMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LEVEL_UP), 0, &LevelUpMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_WM_ONLINE), &OnlineUserMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SHOP_BUY), &ShopTradeMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_GUIDANCE_FINISH_NODE), &RoleGuidMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_GUIDANCE_FINISH_GROUP), &RoleGuidMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_ADD_DIAMOND), &AddYuanBaoMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_CONSUME_DIAMOND), &CostYuanBaoMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SUMMON), &GameDraw51Message{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_READ_MAIL), &ReceiveMailMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_DRAW_MAILS), &ReceiveMailMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_DELETE_MAILS), &ReceiveMailMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_RESOURCE_ADD), 0, &AddCoinMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_RESOURCE_DEC), 0, &CostCoinMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_RESOURCE_ADD), 0, &GainItemMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_RESOURCE_DEC), 0, &LoseItemMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_RESOURCE_ADD), 0, &GetFighterMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_USER_WM), &CharDataMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_HERO_WM), &FighterDataMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_BAG_WM), &CharDataBagMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_FRIEND_WM), &FriendLogMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TASK_RECEIVE_AWARD), &ActivityTaskMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_CARNIVAL_RECEIVE_AWARD), &ActivityTaskMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_MEDAL_RECEIVE_AWARD), &ActivityTaskMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SEVENDAY_LOGIN_TAKEAWARD), &ActivityTaskMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_DUNGEON_FIGHT), &DungeonFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TOWER_FIGHT), &TowerFightMessage{})
	// msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TOWER_SWEEP), &TowerSweepMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TRIAL_FIGHT), &TrialFightMessage{})
	// msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TRIAL_SWEEP), &TrialSweepMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_FIGHT), &GuildDungeonFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_MAZE_FIGHT), &MazeFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_MIRAGE_FIGHT), &MirageFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TALES_CHAPTER_FIGHT), &TalesChapterFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TALES_ELITE_FIGHT), &TalesEliteFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_TALES_ELITE_WIPE), &TalesEliteSweepMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_ARENA_FIGHT), &ArenaFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_ARENA_BE_FIGHT), &ArenaBeFightMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_END_WM), &SnapshotEndMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_ARTIFACT_WM), &ArtifactDataMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_FOREST_LOOT), &ForestLootMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_FOREST_BE_LOOT), &ForestBeLootMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SHOP_REFRESH), &UserBehaviorMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_DISPATCH_REFRESH_TASK), &UserBehaviorMessage{})
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_GOLD_BUY_GET_GOLD), &UserBehaviorMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_ORDER_PROCESS), &AddCashMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_FOREST_WM), &ForestDataMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_FORMATION_WM), &FormationDataMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_GUILD_WM), &GuildDataMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_WORLD_BOSS_RANK_WM), &WorldBossRankDataMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_PEAK_SEASON_RANK_WM), &PeakRankDataMessage{})
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(log.SUB_TYPE_ID_SNAPSHOT_GST_GUILD_WM), &GstGuildDataMessage{})
}

// 日志Message对应的唯一id，默认为0
const (
	MessageNone = iota
	MessageAddCoin
	MessageGainItem
	MessageGetFighter
	MessageCostCoin
	MessageLoseItem
)
