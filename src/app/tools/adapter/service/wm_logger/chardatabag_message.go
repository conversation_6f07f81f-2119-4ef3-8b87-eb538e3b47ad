package logger

import (
	"adapter/service/helper"
	logP "adapter/service/proto/log"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type CharDataBagMessage struct {
	msg []Messager
}

type CharDataBagMessageWm struct {
	BaseMessage
	Level     uint32  `json:"lev"`
	Exp       uint64  `json:"exp"`
	TotalCash float32 `json:"totalcash"`
	VipLevel  uint32  `json:"viplev"`
	ItemType  string  `json:"itemtype"`
	ItemId    string  `json:"itemid"`
	ItemCount uint32  `json:"itemcount"`
	ItemLev   uint32  `json:"itemlev"`
	ItemRaity string  `json:"itemraity"`
}

func (m *CharDataBagMessage) Execute(log *da.Log) ([]Messager, error) {

	var bagSnapshot *logP.BagSnapshotForWm
	err := helper.Json.Unmarshal([]byte(log.Param1), &bagSnapshot)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}
	if m.msg == nil {
		m.msg = make([]Messager, 0, len(bagSnapshot.Items)+len(bagSnapshot.Fragments)+len(bagSnapshot.ArtifactFragments))
	}
	index := 0
	for _, charDataBag := range bagSnapshot.Items {
		m.generateCharDataBagMessageWm(log, charDataBag, index)
		index++
	}
	for _, charDataBag := range bagSnapshot.Fragments {
		m.generateCharDataBagMessageWm(log, charDataBag, index)
		index++
	}
	for _, charDataBag := range bagSnapshot.ArtifactFragments {
		m.generateCharDataBagMessageWm(log, charDataBag, index)
		index++
	}
	for _, charDataBag := range bagSnapshot.RemainFragments {
		m.generateCharDataBagMessageWm(log, charDataBag, index)
		index++
	}

	for _, charDataBag := range bagSnapshot.Emblem {
		m.generateCharDataBagMessageWm(log, charDataBag, index)
		index++
	}

	//for _, charDataBag := range bagSnapshot.Equip {
	//	m.msg = append(m.msg, m.generateCharDataBagMessageWm(log, charDataBag, index))
	//	index++
	//}
	//for _, charDataBag := range bagSnapshot.Emblem {
	//	m.msg = append(m.msg, m.generateCharDataBagMessageWm(log, charDataBag, index))
	//	index++
	//}
	//for _, charDataBag := range bagSnapshot.Gem {
	//	m.msg = append(m.msg, m.generateCharDataBagMessageWm(log, charDataBag, index))
	//	index++
	//}

	return m.msg[0:index], nil
}

func (m *CharDataBagMessage) generateCharDataBagMessageWm(log *da.Log, charDataBag *logP.CharDataBag, index int) *CharDataBagMessageWm {
	var message *CharDataBagMessageWm
	if len(m.msg) >= index+1 {
		message = m.msg[index].(*CharDataBagMessageWm)
	} else {
		message = &CharDataBagMessageWm{}
		m.msg = append(m.msg, message)
	}

	baseMessage := GetBaseFromLog(log, "chardatabag", index, MessageNone)
	message.BaseMessage = baseMessage
	message.FileName = "chardatabag.snapshot"
	message.Logname = baseMessage.Logname
	message.Level = log.Level
	message.Exp = log.Param10
	message.TotalCash = dollars2Cents(log.TotalCash)
	message.VipLevel = log.VipLevel
	message.ItemType = strconv.Itoa(int(charDataBag.Type))
	message.ItemId = fmt.Sprintf("%d-%d", charDataBag.Type, charDataBag.Id)
	message.ItemCount = charDataBag.Count
	message.ItemLev = charDataBag.Lev
	message.ItemRaity = strconv.Itoa(int(charDataBag.Raity))
	return message
}

func (m *CharDataBagMessage) Clear() {

}
