package logger

import (
	"adapter/service/proto/out/common"
	"strconv"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

type CostYuanBaoMessage struct {
	msg *CostYuanBaoMessageWm
}

type CostYuanBaoMessageWm struct {
	BaseMessage
	Level        uint32  `json:"lev"`
	TotalCash    float32 `json:"totalcash"`
	VipLevel     uint32  `json:"viplev"`
	Scene        string  `json:"scene"`
	YuanBaoType  string  `json:"yuanbaotype"`
	YuanBaoPath  string  `json:"yuanbaopath"`
	YuanBao      uint64  `json:"yuanbao"`
	YuanBaoTotal uint64  `json:"yuanbaototal"`
	YuanBaoLeft  uint64  `json:"yuanbaoleft"`
	SubPath      string  `json:"subpath"`
}

func (m *CostYuanBaoMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &CostYuanBaoMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "costyuanbao", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	m.msg.VipLevel = log.VipLevel
	m.msg.Scene = ""
	m.msg.YuanBaoType = strconv.Itoa(int(common.RESOURCE_DIAMOND))
	m.msg.YuanBaoPath = strconv.FormatUint(log.Param10, 10)
	m.msg.YuanBao = log.Param11
	m.msg.YuanBaoTotal = log.Param12
	m.msg.YuanBaoLeft = log.PayDiamond + log.GiftDiamond
	l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
		m.msg.Key, m.msg.Logname, m.msg.RoleID, m.msg.Logtime)
	//m.msg.SubPath = log.Param13
	return []Messager{m.msg}, nil
}

func (m *CostYuanBaoMessage) Clear() {

}
