package logger

import (
	"adapter/service/helper"
	"adapter/service/proto/db"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type AddCashMessage struct {
	msg *AddCashMessageWm
}

type AddCashMessageWm struct {
	BaseMessage
	Level         uint32  `json:"lev"`
	TotalCash     float32 `json:"totalcash"`
	VipLevel      uint32  `json:"viplev"`
	Cash          uint32  `json:"cash"` //
	GameOrder     string  `json:"gameorder"`
	PlatformOrder string  `json:"platformorder"`
	ProductId     string  `json:"productid"`
	AddYuanBao    uint32  `json:"addyuanbao"` //
	IapType       string  `json:"iaptype"`
	PayCurrency   string  `json:"paycurrency"`
	PayAmount     float64 `json:"payamount"`
	YuanBaoLeft   uint64  `json:"yuanbaoleft"`
	PayType       string  `json:"paytype"`
}

func (m *AddCashMessage) Execute(log *da.Log) ([]Messager, error) {
	order := &db.Order{}
	err := helper.Json.Unmarshal([]byte(log.Param1), &order)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, fmt.Errorf("json unmarshakl error %v", err)
	}

	if m.msg == nil {
		m.msg = &AddCashMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "addcash", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	m.msg.VipLevel = log.VipLevel
	m.msg.YuanBaoLeft = log.PayDiamond + log.GiftDiamond
	m.msg.Cash = order.Amount
	m.msg.GameOrder = ""
	m.msg.PlatformOrder = order.OrderId
	m.msg.ProductId = order.ProductId
	m.msg.AddYuanBao = 0
	m.msg.IapType = ""
	m.msg.PayCurrency = order.PayCurrency
	m.msg.PayAmount = float64(order.PayAmount)
	l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
		m.msg.Key, m.msg.Logname, m.msg.RoleID, m.msg.Logtime)
	return []Messager{m.msg}, nil
}

func (m *AddCashMessage) Clear() {

}
