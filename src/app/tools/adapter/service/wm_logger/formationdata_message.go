package logger

import (
	"adapter/service/helper"
	logP "adapter/service/proto/log"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/platform/proto/da"
)

type FormationDataMessage struct {
	msg *FormationDataMessageWm
}

type FormationDataMessageWm struct {
	BaseMessage
	FormationID string `json:"formation_id"` // 阵容ID
	Team1Hids   string `json:"team_1_hids"`  // 队伍1的英雄
	Team1Aids   string `json:"team_1_aids"`  // 队伍1的神器
	Team2Hids   string `json:"team_2_hids"`  // 队伍2的英雄
	Team2Aids   string `json:"team_2_aids"`  // 队伍2的神器
	Team3Hids   string `json:"team_3_hids"`  // 队伍3的英雄
	Team3Aids   string `json:"team_3_aids"`  // 队伍3的神器
}

func (m *FormationDataMessage) Execute(log *da.Log) ([]Messager, error) {
	var formationSnapshot *logP.FormationSnapshotForWm
	err := helper.Json.Unmarshal([]byte(log.Param1), &formationSnapshot)
	if err != nil {
		l4g.Errorf("[Logger] json unmarshal error: %v", err)
		return nil, err
	}
	if formationSnapshot == nil || formationSnapshot.Formation == nil {
		return nil, nil
	}
	if m.msg == nil {
		m.msg = &FormationDataMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "formation_log", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "formation.snapshot"

	m.msg.resetCustom()

	m.msg.FormationID = fmt.Sprintf("%d", formationSnapshot.Formation.Id)

	for index, team := range formationSnapshot.Formation.Teams {
		hids := make([]uint64, 5)
		aids := make([]uint32, 3)
		for _, info := range team.Info {
			if info.Pos < 1 || info.Pos > 5 {
				continue
			}
			hids[info.Pos-1] = info.Hid
		}
		for _, artifact := range team.Artifacts {
			if artifact.Pos < 1 || artifact.Pos > 3 {
				continue
			}
			aids[artifact.Pos-1] = artifact.Aid
		}
		if index == 0 {
			m.msg.Team1Hids = util.Uint64SliceToString(hids, ",")
			m.msg.Team1Aids = util.Uint32SliceToString(aids, ",")
		} else if index == 1 {
			m.msg.Team2Hids = util.Uint64SliceToString(hids, ",")
			m.msg.Team2Aids = util.Uint32SliceToString(aids, ",")
		} else if index == 2 {
			m.msg.Team3Hids = util.Uint64SliceToString(hids, ",")
			m.msg.Team3Aids = util.Uint32SliceToString(aids, ",")
		} else {
			l4g.Errorf("FormationDataMessage: team num large 3")
		}
	}

	return []Messager{m.msg}, nil
}

func (m *FormationDataMessage) Clear() {

}

func (f *FormationDataMessageWm) resetCustom() {
	f.FormationID = ""
	f.Team1Hids = ""
	f.Team1Aids = ""
	f.Team2Hids = ""
	f.Team2Aids = ""
	f.Team3Hids = ""
	f.Team3Aids = ""
}
