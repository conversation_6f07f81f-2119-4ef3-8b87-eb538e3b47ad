package logger

const (
	BattleTypeDungeon      = "101" //主线
	BattleTypeTower        = "102" //爬塔
	BattleTypeTrial        = "103" //材料本
	BattleTypeMaze         = "104" //迷宫
	BattleTypeGuildDungeon = "105" //公会副本
	BattleTypeMirage       = "106" //个人boss
	BattleTypeTalesChapter = "107" //列传章节战斗
	BattleTypeTalesElite   = "108" //列传精英战斗
	BattleTypeTowerStar    = "109" //条件爬塔（失落之城）
)

const (
	BattleOpTypeFight uint32 = 1 //战斗
	BattleOpTypeSweep uint32 = 2 //扫荡
	BattleOpTypeCrush uint32 = 3 //碾压/跳关
)

type PveMessageWm struct {
	BaseMessage
	VipLevel   uint32  `json:"viplev"`
	Level      uint32  `json:"lev"`
	TotalCash  float32 `json:"totalcash"`
	Occupation string  `json:"occupation"`
	Gender     string  `json:"gender"`
	//Power        uint64  `json:"fight"`
	Exp          uint64 `json:"exp"`
	GuildID      string `json:"guildid"`
	BattleType   string `json:"battletype"`
	StageType    string `json:"stagetype"`
	OperateType  string `json:"operate_type"`
	ActivityName string `json:"activityname"`
	BatchID      string `json:"batchid"`
	BattleID     string `json:"battleid"`
	StageID      string `json:"stageid"`
	Fid          string `json:"fid"`
	FighterLevel string `json:"fighter_lev"`
	FidRarity    string `json:"fid_rarity"`
	Damage       string `json:"damage"`
	Result       string `json:"result"`
	Score        string `json:"score"`
	TotalScore   string `json:"totalscore"`
	EndTime      string `json:"endtime"`
	Custom1      string `json:"custom_1"`
	Custom2      string `json:"custom_2"`
	Custom3      string `json:"custom_3"`
	Custom4      string `json:"custom_4"`
	Custom5      string `json:"custom_5"`
	//ArtifactId   string `json:"artifactid"`
}

/*type DungeonFightMessage struct {
	msg *PveMessageWm
}

func (m *DungeonFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param1), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeDungeon //主线副本
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param10))
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(log.Param12)) //是否胜利
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *DungeonFightMessage) Clear() {

}

//爬塔
type TowerFightMessage struct {
	msg *PveMessageWm
}

func (m *TowerFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if log.Param13 == uint64(BattleOpTypeSweep) {
		return m.ExecuteSweep(log)
	}
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param1), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTower               //爬塔
	m.msg.StageType = strconv.Itoa(int(log.Param10)) //塔的类型
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param11)) //第几层
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(log.Param12)) //是否胜利
	m.msg.EndTime = baseMessage.Logtime
	m.msg.Custom1 = ""
	return []Messager{m.msg}, nil
}

func (m *TowerFightMessage) Clear() {

}

//爬塔扫荡
//type TowerSweepMessage struct {
//	msg *PveMessageWm
//}

func (m *TowerFightMessage) ExecuteSweep(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTower               //爬塔
	m.msg.StageType = strconv.Itoa(int(log.Param10)) //塔的类型
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeSweep))
	m.msg.BattleID = ""
	m.msg.Fid = ""
	m.msg.FighterLevel = ""
	m.msg.Damage = ""
	m.msg.StageID = strconv.Itoa(int(log.Param11)) //第几层
	m.msg.EndTime = baseMessage.Logtime
	m.msg.Result = "1"  //扫荡结果，固定记录1
	m.msg.Custom1 = "1" //扫荡几次
	return []Messager{m.msg}, nil
}

//公会副本
type GuildDungeonFightMessage struct {
	msg *PveMessageWm
}

func (m *GuildDungeonFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param2), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeGuildDungeon        //公会副本
	m.msg.StageType = strconv.Itoa(int(log.Param10)) //第几章
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param11)) //第几个怪
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = "1" //是否胜利
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *GuildDungeonFightMessage) Clear() {

}

//材料副本
type TrialFightMessage struct {
	msg *PveMessageWm
}

func (m *TrialFightMessage) Execute(log *da.Log) ([]Messager, error) {

	if log.Param14 == uint64(BattleOpTypeSweep) {
		return m.ExecuteSweep(log)
	}

	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param2), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTrial               //材料副本
	m.msg.StageType = strconv.Itoa(int(log.Param10)) //材料副本的类型
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param11)) //第几层
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(log.Param13)) //星数
	m.msg.EndTime = baseMessage.Logtime
	m.msg.Custom1 = ""
	return []Messager{m.msg}, nil
}

func (m *TrialFightMessage) Clear() {

}

//材料副本扫荡
//type TrialSweepMessage struct {
//	msg *PveMessageWm
//}
//材料副本扫荡
func (m *TrialFightMessage) ExecuteSweep(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTrial               //材料副本
	m.msg.StageType = strconv.Itoa(int(log.Param10)) //副本的类型
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeSweep))
	m.msg.StageID = strconv.Itoa(int(log.Param11)) //第几层
	m.msg.BattleID = ""
	m.msg.Fid = ""
	m.msg.FighterLevel = ""
	m.msg.Damage = ""
	m.msg.EndTime = baseMessage.Logtime
	m.msg.Result = "3"                             //材料本三星才能扫荡，固定记录3
	m.msg.Custom1 = strconv.Itoa(int(log.Param15)) //扫荡几次
	return []Messager{m.msg}, nil
}

//迷宫战斗
type MazeFightMessage struct {
	msg *PveMessageWm
}

func (m *MazeFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param1), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeMaze                //迷宫
	m.msg.StageType = strconv.Itoa(int(log.Param10)) //迷宫的地图id
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param11)) //迷宫的事件类型
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(log.Param12)) //是否胜利
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *MazeFightMessage) Clear() {

}

//个人boss战斗
type MirageFightMessage struct {
	msg *PveMessageWm
}

func (m *MirageFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param1), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeMirage //个人boss
	m.msg.StageType = ""
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param10)) //id
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(log.Param12))  //是否胜利
	m.msg.Custom2 = strconv.Itoa(int(log.Param11)) //选择的星数
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *MirageFightMessage) Clear() {

}

//列传章节战斗
type TalesChapterFightMessage struct {
	msg *PveMessageWm
}

func (m *TalesChapterFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param1), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTalesChapter        //列传章节
	m.msg.StageType = strconv.Itoa(int(log.Param10)) //列传 id
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param11)) //章节id
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(log.Param12)) //是否胜利
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *TalesChapterFightMessage) Clear() {

}

//列传强敌战斗
type TalesEliteFightMessage struct {
	msg *PveMessageWm
}

func (m *TalesEliteFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param1), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTalesElite //列传章节
	m.msg.StageType = ""
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param10)) //强敌id
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(log.Param12)) //是否胜利
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *TalesEliteFightMessage) Clear() {

}

//列传强敌扫荡
type TalesEliteSweepMessage struct {
	msg *PveMessageWm
}

func (m *TalesEliteSweepMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTalesElite //列传章节
	m.msg.StageType = ""
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeSweep))
	m.msg.Result = "1"                             //扫荡结果，固定记录1
	m.msg.StageID = strconv.Itoa(int(log.Param10)) //强敌id
	m.msg.Custom1 = "1"                            //扫荡几次
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *TalesEliteSweepMessage) Clear() {

}

//条件爬塔战斗
type TowerStarFightMessage struct {
	msg *PveMessageWm
}

func (m *TowerStarFightMessage) Execute(log *da.Log) ([]Messager, error) {
	if m.msg == nil {
		m.msg = &PveMessageWm{}
	}
	clReport := &bt.Report{}
	err := helper.Json.Unmarshal([]byte(log.Param1), clReport)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, nil
	}
	var fids, aids []string
	var flevel []string
	for _, v := range clReport.Attackers {
		fids = append(fids, fmt.Sprintf("%d-%d", common.RESOURCE_HERO, v.SysId))
		flevel = append(flevel, strconv.Itoa(int(v.Level)))
	}
	if clReport.AttackArtifact != nil {
		for _, v := range clReport.AttackArtifact.Infos {
			aids = append(aids, fmt.Sprintf("%d-%d", common.RESOURCE_ARTIFACT, v.Artifact.SysId))
		}
	}
	baseMessage := GetBaseFromLog(log, "pve", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "ads.format.log"
	m.msg.VipLevel = log.VipLevel
	m.msg.Level = log.Level
	m.msg.TotalCash = dollars2Cents(log.TotalCash)
	//m.msg.Power = log.Power
	m.msg.GuildID = strconv.Itoa(int(log.Param20))
	m.msg.BattleType = BattleTypeTowerStar //条件爬塔
	m.msg.StageType = ""
	m.msg.OperateType = strconv.Itoa(int(BattleOpTypeFight))
	m.msg.BattleID = strconv.Itoa(int(clReport.Id))
	m.msg.StageID = strconv.Itoa(int(log.Param10)) //dungeon id
	m.msg.Fid = strings.Join(fids, ",")
	//m.msg.ArtifactId = strings.Join(aids, ",")
	m.msg.FighterLevel = strings.Join(flevel, ",")
	m.msg.Damage = strconv.Itoa(int(clReport.GetTotalHurtShow()))
	m.msg.Result = strconv.Itoa(int(util.If(clReport.Win, 1, 0))) //是否胜利
	m.msg.EndTime = baseMessage.Logtime
	return []Messager{m.msg}, nil
}

func (m *TowerStarFightMessage) Clear() {

}*/
