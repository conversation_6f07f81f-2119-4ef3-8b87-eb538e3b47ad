package logger

import (
	"adapter/service/helper"
	"adapter/service/proto/out/cl"
	"adapter/service/proto/out/common"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

type GetFighterMessage struct {
	msg []Messager
}

type GetFighterMessageWm struct {
	BaseMessage
	Level     uint32  `json:"lev"`
	TotalCash float32 `json:"totalcash"`
	VipLevel  uint32  `json:"viplev"`
	HeroSysID string  `json:"fid"`
	Rare      string  `json:"rarity"` //稀有度
	Reason    string  `json:"path"`
	SubReason string  `json:"subpath"`
}

func (m *GetFighterMessage) Execute(log *da.Log) ([]Messager, error) {
	/*
		if m.msg != nil {
			m.msg = m.msg[0:0]
		}
	*/
	var resources []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param1), &resources)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}
	var items []*cl.Resource
	for _, resource := range resources {
		if resource.Type == uint32(common.RESOURCE_HERO) {
			items = append(items, resource)
		}
	}

	if len(items) == 0 {
		return nil, nil
	}

	if m.msg == nil {
		m.msg = make([]Messager, 0, len(items))
	}
	index := 0
	for _, item := range items {
		var message *GetFighterMessageWm
		if len(m.msg) >= index+1 {
			message = m.msg[index].(*GetFighterMessageWm)
		} else {
			message = &GetFighterMessageWm{}
			m.msg = append(m.msg, message)
		}
		baseMessage := GetBaseFromLog(log, "getfighter", index, MessageGetFighter)
		message.BaseMessage = baseMessage
		message.FileName = "ads.format.log"
		message.Level = log.Level
		message.VipLevel = log.VipLevel
		message.TotalCash = dollars2Cents(log.TotalCash)
		message.HeroSysID = fmt.Sprintf("%d-%d", item.Type, item.Value)
		message.Reason = strconv.Itoa(int(log.LogSubType))
		message.SubReason = strconv.Itoa(int(log.Param10))
		l4g.Debugf("[test] execute key:%s logName:%s userid:%s logTime:%s",
			message.Key, message.Logname, message.RoleID, message.Logtime)
		index++
	}
	return m.msg[0:index], nil
}

func (m *GetFighterMessage) Clear() {

}
