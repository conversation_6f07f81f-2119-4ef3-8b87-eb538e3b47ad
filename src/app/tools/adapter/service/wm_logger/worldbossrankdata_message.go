package logger

import (
	"adapter/service/helper"
	logP "adapter/service/proto/log"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type WorldBossRankDataMessage struct {
	msg *WorldBossRankMessage
}

type WorldBossRankMessage struct {
	BaseMessage
	RankType uint32 `json:"rank_type"`
	Rank     uint32 `json:"rank"`
	Damage   uint64 `json:"damage"`
	Term     uint32 `json:"term"`
	Mode     uint32 `json:"mode"`
	Zone     uint32 `json:"zone"`
}

func (m *WorldBossRankDataMessage) Execute(log *da.Log) ([]Messager, error) {

	if m.msg == nil {
		m.msg = &WorldBossRankMessage{}
	}
	baseMessage := GetBaseFromLog(log, "worldbossrankdata", 0, MessageNone)
	m.msg.BaseMessage = baseMessage
	m.msg.FileName = "worldbossrankdata.snapshot"
	var snapshot *logP.WorldBossRankSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &snapshot)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil, err
	}

	m.msg.Rank = snapshot.Rank
	m.msg.RankType = snapshot.RankType
	m.msg.Damage = snapshot.Damage
	m.msg.Mode = snapshot.Mode
	m.msg.Term = snapshot.Term
	m.msg.Zone = snapshot.Arena

	return []Messager{m.msg}, nil
}

func (m *WorldBossRankDataMessage) Clear() {

}

//func changeSnapshotDate(t time.Time) string {
//	t.Add(-time.Hour * 24)
//	return t.Format("2006-01-02")
//}
