package tower

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TOWER_FIGHT),
		&TowerFightMessage{}, "tower_fight", 16, 0, 0, 1)
}

type TowerFightMessage struct {
	helper.BaseMessage
}

func (m *TowerFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	win := helper.FormatBool(log.Param12)
	quick := helper.FormatBool(log.Param14)
	r.SetTrack("tower_type", log.Param10)
	r.SetTrack("tower_floor", log.Param11)
	r.SetTrack("battle_op_type", log.Param13)
	r.SetTrack("win", win)
	r.SetTrack("quick_fight", quick)
	if log.Param13 == helper.BattleOpTypeFight {
		r.SetFormationMessage(log.Param2)
		r.SetTrack("report_id", log.Param3)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
