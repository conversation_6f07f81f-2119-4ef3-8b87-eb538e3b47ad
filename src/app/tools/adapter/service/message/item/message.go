package item

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"

	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ITEM_USE),
		&ItemUseMessage{}, "item_use", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ITEM_SELL),
		&ItemSellMessage{}, "item_sell", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ITEM_SELECT),
		&ItemSelectMessage{}, "item_select", 12, 0, 0, 0)
}

type ItemUseMessage struct {
	helper.BaseMessage
}

func (m *ItemUseMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("item_id", log.Param10)
	r.SetTrack("item_count", log.Param11)
	r.SetResourceContentMessage("item_content", log.Param1) // 使用道具获得的资源
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type ItemSellMessage struct {
	helper.BaseMessage
}

func (m *ItemSellMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("item_id", log.Param10)
	r.SetTrack("item_count", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type ItemSelectMessage struct {
	helper.BaseMessage
}

func (m *ItemSelectMessage) Execute(log *da.Log) bool {
	var selectItems []*cl.SubItem
	err := helper.Json.Unmarshal([]byte(log.Param1), &selectItems)
	if err != nil {
		l4g.Errorf("[Logger] json unmarshal error: %v", err)
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("item_id", log.Param10)
	r.SetTrack("item_count", log.Param11)
	r.SetTrack("select_items", selectItems)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
