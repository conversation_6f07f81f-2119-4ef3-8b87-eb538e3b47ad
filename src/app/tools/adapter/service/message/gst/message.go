package gst

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"
	"encoding/json"
	"strconv"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_MODIFY_NOTICE),
		&GuildSandTableModifyNoticeMessage{}, "guild_sand_table_modify_notice", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_TASK),
		&GuildSandTableTaskMessage{}, "guild_sand_table_task", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS),
		&GuildSandTableBlessMessage{}, "guild_sand_table_bless", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_HOST),
		&GuildSandTableHostMessage{}, "guild_sand_table_host", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_DISPATCH),
		&GuildSandTableDispatchMessage{}, "guild_sand_table_dispatch", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_REORDER),
		&GuildSandTableReorderMessage{}, "guild_sand_table_reorder", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_FIGHT),
		&GuildSandTableFightMessage{}, "guild_sandtable_fight", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_TASK),
		&GuildSandTableBuildTaskMessage{}, "guild_sand_table_build_task", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DONATE),
		&GuildSandTableBuildDonateMessage{}, "guild_sand_table_build_donate", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_BOX_RECV),
		&LogGuildSandTableBoxRecvMessage{}, "guild_sand_table_box_recv", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DISPATCH),
		&LogGuildSandTableBuildDispatchMessage{}, "guild_sand_table_build_dispatch", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_VOTE),
		&LogGuildSandTableVoteMessage{}, "guild_sand_table_vote", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GST_BOSS_FIGHT),
		&LogGSTBossFightMessage{}, "gst_boss_fight", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GST_BOSS_AWARD),
		&LogGSTBossAwardMessage{}, "gst_boss_award", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GST_BOSS_BUY_CHALLENGE),
		&LogGSTBossBuyChallengeMessage{}, "gst_boss_buy_challenge", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GST_DRAGON_FIGHT),
		&LogGSTDragonFightMessage{}, "gst_dragon_fight", 20, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GST_DRAGON_SETTLEMENT),
		&LogGSTDragonSettlementMessage{}, "gst_dragon_settlement", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GST_DRAGON_CULTIVATION),
		&LogGSTDragonCultivationMessage{}, "gst_dragon_cultivation", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GST_DRAGON_TASK_AWARD),
		&LogGSTDragonTaskAwardMessage{}, "gst_dragon_task_award", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_FIGHT),
		&LogGSTOreFightMessage{}, "guild_sandtable_ore_battle", 20, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_RESOURCE_CHANGE),
		&LogOreResourceChangeMessage{}, "guild_sandtable_ore_resource", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_TASK_AWARD),
		&LogGSTTechTaskAwardMessage{}, "guild_sandtable_ore_task_award", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_LEVEL_UP),
		&LogGSTTechLevelUpMessage{}, "guild_sandtable_ore_tech_level", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_STREAK),
		&LogGSTStreakMessage{}, "guild_sandtable_streak", 20, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_TASK_AWARD),
		&LogGSTChallengeTaskAwardMessage{}, "guild_sand_table_challenge_task", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_FIGHT),
		&LogGSTChallengeFightMessage{}, "guild_sand_table_challenge_fight", 20, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_STREAK),
		&LogGSTChallengeStreakMessage{}, "guild_sand_table_challenge_streak", 20, 0, 0, 0)
}

// 更改公告
type GuildSandTableModifyNoticeMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableModifyNoticeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("gst_guild_id", strconv.FormatUint(log.GuildID, 10)) // 公会ID
	r.SetTrack("gst_notice", log.Param1)                            // 公告
	r.SetTrack("gst_turn", log.Param10)                             // 回合
	r.SetTrack("gst_round", log.Param11)                            // 轮次
	r.SetTrack("gst_battle_zone", log.Param12)                      // 战区
	r.SetTrack("gst_room", strconv.FormatUint(log.Param13, 10))     // 房间ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 领取任务奖励
type GuildSandTableTaskMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableTaskMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	taskIds := helper.Int32SliceToStringSlice(log.Param1)
	r.SetTrack("gst_guild_id", strconv.FormatUint(log.GuildID, 10)) // 公会ID
	r.SetTrack("gst_task_ids", taskIds)                             // 任务IDs
	r.SetTrack("gst_turn", log.Param10)                             // 回合
	r.SetTrack("gst_round", log.Param11)                            // 轮次
	r.SetTrack("gst_battle_zone", log.Param12)                      // 战区
	r.SetTrack("gst_room", strconv.FormatUint(log.Param13, 10))     // 房间ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 祝福
type GuildSandTableBlessMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableBlessMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var donateDetail []*cl.GSTClientDonate
	err := helper.Json.Unmarshal([]byte(log.Param1), &donateDetail)
	if err != nil {
		l4g.Errorf("[GuildSandTableBlessMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}

	r.SetTrack("gst_guild_id", strconv.FormatUint(log.GuildID, 10)) // 公会ID
	r.SetTrack("gst_guild_table_bless_detail", donateDetail)        // 捐赠详情
	r.SetTrack("gst_turn", log.Param10)                             // 回合
	r.SetTrack("gst_round", log.Param11)                            // 轮次
	r.SetTrack("gst_battle_zone", log.Param12)                      // 战区
	r.SetTrack("gst_room", strconv.FormatUint(log.Param13, 10))     // 房间ID
	r.SetTrack("gst_bless_id", log.Param14)                         // 祝福对象
	r.SetTrack("gst_donate_point", log.Param15)                     // 个人贡献积分
	r.SetTrack("gst_this_donate_point", log.Param16)                // 本次贡献积分
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 队伍托管
type GuildSandTableHostMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableHostMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var teams []uint32
	err := helper.Json.Unmarshal([]byte(log.Param1), &teams)
	if err != nil {
		l4g.Errorf("[GuildSandTableHostMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}
	isHost := helper.FormatBool(log.Param14)
	r.SetTrack("gst_guild_id", strconv.FormatUint(log.GuildID, 10)) // 公会ID
	r.SetTrack("gst_operate_teams", teams)                          // 操作队伍
	r.SetTrack("gst_turn", log.Param10)                             // 回合
	r.SetTrack("gst_round", log.Param11)                            // 轮次
	r.SetTrack("gst_battle_zone", log.Param12)                      // 战区
	r.SetTrack("gst_room", strconv.FormatUint(log.Param13, 10))     // 房间ID
	r.SetTrack("gst_is_host", isHost)                               // 是否托管
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 队伍派遣
type GuildSandTableDispatchMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableDispatchMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var teams []*sub.GSTDispatchTeam
	err := helper.Json.Unmarshal([]byte(log.Param1), &teams)
	if err != nil {
		l4g.Errorf("[GuildSandTableDispatchMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}
	isAuto := helper.FormatBool(log.Param14)
	r.SetTrack("gst_guild_id", strconv.FormatUint(log.GuildID, 10)) // 公会ID
	r.SetTrack("gst_team_order", teams)                             // 队伍顺序
	r.SetTrack("gst_turn", log.Param10)                             // 回合
	r.SetTrack("gst_round", log.Param11)                            // 轮次
	r.SetTrack("gst_battle_zone", log.Param12)                      // 战区
	r.SetTrack("gst_room", strconv.FormatUint(log.Param13, 10))     // 房间ID
	r.SetTrack("gst_is_auto", isAuto)                               // 是否一键派遣
	r.SetTrack("gst_room_quality", log.Param15)                     // 房间品质
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 队伍派遣
type GuildSandTableReorderMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableReorderMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var teams []*sub.GSTReorderTeam
	err := helper.Json.Unmarshal([]byte(log.Param2), &teams)
	if err != nil {
		l4g.Errorf("[GuildSandTableReorderMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}
	r.SetTrack("gst_guild_id", strconv.FormatUint(log.GuildID, 10)) // 公会ID
	r.SetTrack("gst_team_order_new", teams)                         // 派遣队伍
	r.SetTrack("gst_turn", log.Param10)                             // 回合
	r.SetTrack("gst_round", log.Param11)                            // 轮次
	r.SetTrack("gst_battle_zone", log.Param12)                      // 战区
	r.SetTrack("gst_room", strconv.FormatUint(log.Param13, 10))     // 房间ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildSandTableFightMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var attackTeam *sub.Formation
	err := helper.Json.Unmarshal([]byte(log.Param2), &attackTeam)
	if err != nil {
		l4g.Errorf("[GuildSandTableFightMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}
	var fightExt *sub.GSTFightExt
	err = helper.Json.Unmarshal([]byte(log.Extra), &fightExt)
	if err != nil {
		l4g.Errorf("[GuildSandTableFightMessage] json unmarshal GSTFightExt  error: %v data: %s", err, log.Param5)
		return false
	}
	r.SetTrack("gst_room_quality", fightExt.RoomQuality) // 房间品质
	r.SetTrack("gst_arena_round", fightExt.ArenaRound)   // 擂台轮次
	isBot := helper.FormatBool(log.Param19)
	if !isBot && log.Param4 != "" {
		var defenseLog *sub.GstDefenseInfo
		err := helper.Json.Unmarshal([]byte(log.Param4), &defenseLog)
		if err != nil {
			l4g.Errorf("[GuildSandTableFightMessage] json unmarshal defenseLog  error: %v data: %s", err, log.Param4)
			return false
		}
		r.SetTrack("defense_uid", strconv.FormatUint(defenseLog.Uid, 10))
		r.SetTrack("defense_server_id", strconv.FormatUint(defenseLog.ServerId, 10))
		r.SetTrack("defense_guild_id", strconv.FormatUint(defenseLog.GuildId, 10))
		if log.Param6 != "" {
			var defenseLogGstBattleSub *sub.LogGstBattleSub
			err := helper.Json.Unmarshal([]byte(log.Param6), &defenseLogGstBattleSub)
			if err != nil {
				l4g.Errorf("[GuildSandTableFightMessage] json unmarshal defenseLogGstBattleSub  error: %v data: %s", err, log.Param6)
				return false
			}
			r.SetTrack("defense_guild_build_id", defenseLogGstBattleSub.SpaceId)
			r.SetTrack("defense_morale", defenseLogGstBattleSub.Morale)
			r.SetTrack("defense_popularity", defenseLogGstBattleSub.Popular)
		}
	}
	r.SetTrack("gst_guild_id", strconv.FormatUint(log.GuildID, 10)) // 公会ID
	r.SetTrack("gst_turn", log.Param10)                             // 回合
	r.SetTrack("gst_round", log.Param11)                            // 轮次
	r.SetTrack("gst_battle_zone", log.Param12)                      // 战区
	r.SetTrack("gst_room", strconv.FormatUint(log.Param13, 10))     // 房间ID
	r.SetTrack("gst_fight_is_win", helper.FormatBool(log.Param14))  //是否胜利
	r.SetTrack("gst_fight_monster_id", log.Param15)                 // 怪物组ID
	r.SetTrack("gst_attack_donate_point", log.Param16)              // 进攻方贡献值
	r.SetTrack("gst_space_id", log.Param17)                         // 地块ID
	r.SetTrack("gst_space_type", log.Param18)                       //地块类型
	r.SetTrack("gst_is_bot", helper.FormatBool(log.Param19))        //是否是人机
	r.SetFormationMessageByIndex(log.Param2, uint32(log.Param20))
	r.SetTrack("gst_battle_report_id", log.Param1) //战报ID
	if log.Param5 != "" {
		var LogGstBattleSub *sub.LogGstBattleSub
		err := helper.Json.Unmarshal([]byte(log.Param5), &LogGstBattleSub)
		if err != nil {
			l4g.Errorf("[GuildSandTableFightMessage] json unmarshal LogGstBattleSub  error: %v data: %s", err, log.Param5)
			return false
		}
		r.SetTrack("gst_build_id", LogGstBattleSub.SpaceId)
		r.SetTrack("gst_morale", LogGstBattleSub.Morale)
		r.SetTrack("attack_popularity", LogGstBattleSub.Popular)
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 领取任务奖励
type GuildSandTableBuildTaskMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableBuildTaskMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	taskIds := helper.Int32SliceToStringSlice(log.Param1)
	r.SetTrack("gst_build_task_ids", taskIds) // 任务IDs
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 捐赠建筑
type GuildSandTableBuildDonateMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableBuildDonateMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetResourceContentMessage("build_cur_resource", log.Param1)
	r.SetResourceContentMessage("build_old_resource", log.Param2)
	r.SetResourceContentMessage("donate_resource", log.Param3)

	r.SetTrack("gst_build_build_id", log.Param10)  // 建筑ID
	r.SetTrack("gst_build_old_level", log.Param11) // 老等级
	r.SetTrack("gst_build_new_level", log.Param12) // 新等级

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 捐赠建筑
type LogGuildSandTableBoxRecvMessage struct {
	helper.BaseMessage
}

func (m *LogGuildSandTableBoxRecvMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	if len(log.Param1) > 0 {
		r.SetResourceContentMessage("build_donate_award", log.Param1)
	}

	if len(log.Param2) > 0 {
		r.SetResourceContentMessage("dispatch_award", log.Param2)
	}

	if len(log.Param3) > 0 {
		r.SetResourceContentMessage("battle_win_award", log.Param3)
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 派遣建筑队伍
type LogGuildSandTableBuildDispatchMessage struct {
	helper.BaseMessage
}

func (m *LogGuildSandTableBuildDispatchMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var buildDispatchHeroes map[uint32]*cl.GSTBuildUserDispatchHeroes
	err := json.Unmarshal([]byte(log.Param1), &buildDispatchHeroes)
	if err != nil {
		l4g.Error("[LogGuildSandTableBuildDispatchMessage] json unmarshal error: %v, awardStr: %s", err)
		return false
	}

	logs := make([]*sub.LogBuildDispatchHero, 0, 5)
	for buildType, buildHeroes := range buildDispatchHeroes {
		for _, hero := range buildHeroes.BuildDispatchHero {
			if hero == nil {
				continue
			}
			logs = append(logs, &sub.LogBuildDispatchHero{
				Id:      strconv.FormatUint(uint64(hero.HeroId), 10),
				SysId:   hero.SysId,
				Star:    hero.Star,
				BuildId: buildType,
			})
		}
	}

	r.SetTrack("building_appoint_detail", logs)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会战助威
type LogGuildSandTableVoteMessage struct {
	helper.BaseMessage
}

func (m *LogGuildSandTableVoteMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var logData *sub.LogGSTVote
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGuildSandTableVoteMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("gst_guild_id", strconv.FormatUint(logData.Base.GuildId, 10))
	r.SetTrack("gst_turn", logData.Base.Turn)
	r.SetTrack("gst_round", logData.Base.Round)
	r.SetTrack("gst_battle_zone", logData.Partition)
	r.SetTrack("gst_room", strconv.FormatUint(logData.Base.Room, 10))
	r.SetTrack("gst_arena_round", logData.ArenaRound)
	r.SetTrack("gst_room_quality", logData.Base.RoomQuality)
	r.SetTrack("vote_detail", logData.VoteDetail)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTBossFightMessage struct {
	helper.BaseMessage
}

func (m *LogGSTBossFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	isWin := helper.FormatBool(log.Param15)
	isFirstKill := helper.FormatBool(log.Param19)
	isSweep := helper.FormatBool(log.Param14)

	r.SetTrack("gst_battle_report_id", log.Param1) // 战报id
	r.SetTrack("left_hp", log.Param2)              // 每个boss剩余血量
	r.SetTrack("gst_room", log.Param4)             // 房间
	r.SetTrack("gst_room_quality", log.Param5)     // 房间品质

	r.SetTrack("gst_build_id", strconv.FormatUint(log.GuildID, 10))    // 公会ID
	r.SetTrack("gst_turn", strconv.FormatUint(log.Param10, 10))        // 回合
	r.SetTrack("gst_round", strconv.FormatUint(log.Param11, 10))       // 轮次
	r.SetTrack("gst_battle_zone", strconv.FormatUint(log.Param12, 10)) // 战区
	r.SetTrack("damage", strconv.FormatUint(log.Param13, 10))          // 战力
	r.SetTrack("is_sweep", isSweep)                                    // 是否扫荡
	r.SetTrack("gst_fight_is_win", isWin)                              // 是否胜利
	r.SetTrack("boss_group", strconv.FormatUint(log.Param16, 10))      // boss组
	r.SetTrack("boss_index", strconv.FormatUint(log.Param17, 10))      // boss下标
	r.SetTrack("boss_id", strconv.FormatUint(log.Param18, 10))         // boss id
	r.SetTrack("is_first_kill", isFirstKill)                           // 是否boss首杀

	r.SetFormationMessageByIndex(log.Param3, uint32(log.Param20))

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTBossAwardMessage struct {
	helper.BaseMessage
}

func (m *LogGSTBossAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("award_index", log.Param1)             //奖励下标
	r.SetResourceContentMessage("awards", log.Param2) //奖励
	r.SetTrack("monster_group", log.Param10)          // 怪物组
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTBossBuyChallengeMessage struct {
	helper.BaseMessage
}

func (m *LogGSTBossBuyChallengeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("buy_type", log.Param10)     // 购买类型
	r.SetTrack("buy_count", log.Param11)    // 购买数量
	r.SetTrack("recover_time", log.Param12) // 恢复时间
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTDragonFightMessage struct {
	helper.BaseMessage
}

func (m *LogGSTDragonFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var ownGuild *sub.LogGSTDragonGuild
	err := json.Unmarshal([]byte(log.Param1), &ownGuild)
	if err != nil {
		l4g.Errorf("[LogGSTDragonFightMessage] json unmarshal error: %s", err.Error())
		return false
	}
	var opGuild *sub.LogGSTDragonGuild
	err = json.Unmarshal([]byte(log.Param1), &opGuild)
	if err != nil {
		l4g.Errorf("[LogGSTDragonFightMessage] json unmarshal error: %s", err.Error())
		return false
	}
	var battleData *sub.LogGSTDragonFight
	err = json.Unmarshal([]byte(log.Param4), &battleData)
	if err != nil {
		l4g.Errorf("[LogGSTDragonFightMessage] json unmarshal error: %s", err.Error())
		return false
	}
	for i := uint32(0); i < battleData.TeamNum; i++ {
		r.SetFormationMessageByIndex(log.Param3, i)
	}

	r.SetTrack("season", log.Param10)
	r.SetTrack("dragon_round", log.Param11)
	r.SetTrack("battle_op_type", log.Param12)
	r.SetTrack("guild_id", strconv.FormatUint(ownGuild.GuildId, 10))
	r.SetTrack("guild_grade", ownGuild.GuildGrade)
	r.SetTrack("guild_partition", ownGuild.GuildPartition)

	r.SetTrack("defense_is_bot", opGuild.IsBot)
	r.SetTrack("defense_guild_id", strconv.FormatUint(opGuild.GuildId, 10))
	r.SetTrack("defense_guild_grade", opGuild.GuildGrade)
	r.SetTrack("defense_guild_partition", opGuild.GuildPartition)

	r.SetTrack("defense_dragon_id", battleData.DragonId)
	r.SetTrack("defense_dragon_level", battleData.DragonLevel)
	r.SetTrack("defense_dragon_reduce_hp", battleData.DragonReduceHp)
	r.SetTrack("battle_report_id", battleData.BattleReportId)
	r.SetTrack("total_damage", battleData.TotalDamage)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 龙战胜负结算
type LogGSTDragonSettlementMessage struct {
	helper.BaseMessage
}

func (m *LogGSTDragonSettlementMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var winData *sub.LogGSTDragonSettlement
	err := json.Unmarshal([]byte(log.Param1), &winData)
	if err != nil {
		l4g.Errorf("[LogGSTDragonSettlementMessage] json unmarshal error: %s", err.Error())
		return false
	}
	var loseData *sub.LogGSTDragonSettlement
	err = json.Unmarshal([]byte(log.Param2), &loseData)
	if err != nil {
		l4g.Errorf("[LogGSTDragonSettlementMessage] json unmarshal error: %s", err.Error())
		return false
	}

	r.SetTrack("gst_season", log.Param10)
	r.SetTrack("gst_dragon_round", log.Param11)

	r.SetTrack("win_guild_id", strconv.FormatUint(winData.GuildId, 10))
	r.SetTrack("win_is_bot", winData.IsBot)
	r.SetTrack("win_guild_grade", winData.GuildGrade)
	r.SetTrack("win_progress", winData.DragonProgress)
	r.SetTrack("win_partition", winData.GuildPartition)

	r.SetTrack("lose_guild_id", strconv.FormatUint(loseData.GuildId, 10))
	r.SetTrack("lose_is_bot", loseData.IsBot)
	r.SetTrack("lose_guild_grade", loseData.GuildGrade)
	r.SetTrack("lose_progress", loseData.DragonProgress)
	r.SetTrack("lose_partition", loseData.GuildPartition)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 龙战龙养成
type LogGSTDragonCultivationMessage struct {
	helper.BaseMessage
}

func (m *LogGSTDragonCultivationMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("guild_id", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("dragon_init_id", log.Param11)
	r.SetTrack("dragon_pos", log.Param12)
	r.SetTrack("dragon_level", log.Param13)
	r.SetTrack("dragon_id_before", log.Param14)
	r.SetTrack("dragon_id_after", log.Param15)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTDragonTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *LogGSTDragonTaskAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("dragon_round", log.Param10) // 龙战场次
	if log.Param1 != "" {
		taskIds := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("task_id", taskIds)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTOreFightMessage struct {
	helper.BaseMessage
}

func (m *LogGSTOreFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogGSTOreFight
	err := json.Unmarshal([]byte(log.Param2), &logData)
	if err != nil {
		l4g.Errorf("[LogGSTOreFight] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetFormationMessageByIndex(log.Param1, 0)
	r.SetTrack("battle_op_type", logData.BattleOpType)
	r.SetTrack("monster_id", logData.MonsterId)
	r.SetTrack("turn", logData.Turn)
	r.SetTrack("round", logData.Round)
	r.SetTrack("ore_level", logData.OreLevel)
	r.SetTrack("ore_id", logData.OreId)
	r.SetTrack("assist_uid", logData.AssistUid)
	r.SetTrack("type", logData.Type)
	r.SetTrack("progress", logData.Progress)
	r.SetTrack("battle_report_id", logData.BattleReportId)
	r.SetTrack("single_progress", logData.SingleProgress)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogOreResourceChangeMessage struct {
	helper.BaseMessage
}

func (m *LogOreResourceChangeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogOreResourceChange
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogOreResourceChange] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("type", logData.Type)
	r.SetTrack("count", logData.Count)
	r.SetTrack("uid_str", logData.UidStr)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTTechTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *LogGSTTechTaskAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogGSTTechTaskAward
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGSTTechTaskAward] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("task_id", logData.TaskId)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTTechLevelUpMessage struct {
	helper.BaseMessage
}

func (m *LogGSTTechLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogGSTTechLevelUp
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGSTTechLevelUp] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("node_id", logData.NodeId)
	r.SetTrack("node_level_before", logData.NodeLevelBefore)
	r.SetTrack("node_level_after", logData.NodeLevelAfter)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTStreakMessage struct {
	helper.BaseMessage
}

func (m *LogGSTStreakMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogGSTStreak
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGSTStreak] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("gst_guild_id", logData.GstGuildId)
	r.SetTrack("gst_turn", logData.GstTurn)
	r.SetTrack("gst_team_index", logData.GstTeamIndex)
	r.SetTrack("gst_team_streak", logData.GstTeamStreak)
	r.SetTrack("gst_round", logData.GstRound)
	r.SetTrack("gst_battle_zone", logData.GstBattleZone)
	r.SetTrack("gst_room", logData.GstRoom)
	r.SetTrack("gst_room_quality", logData.GstRoomQuality)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTChallengeTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *LogGSTChallengeTaskAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogGSTChallengeTaskAward
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGSTChallengeTaskAward] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("task_id", logData.TaskId)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTChallengeFightMessage struct {
	helper.BaseMessage
}

func (m *LogGSTChallengeFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogGSTChallengeFight
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGSTChallengeFight] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("battle_report_id", logData.BattleReportId)
	r.SetTrack("battle_zone", logData.BattleZone)
	r.SetTrack("number", logData.Round)
	r.SetTrack("match", logData.ChallengeRound)
	r.SetTrack("round", logData.MatchRound)
	r.SetTrack("team_index", logData.TeamIndex)
	r.SetTrack("room", logData.Room)
	r.SetTrack("room_quality", logData.RoomQuality)
	r.SetTrack("win", logData.Win)
	r.SetTrack("score", logData.AddScore)
	r.SetTrack("is_bot", logData.IsBot)
	r.SetTrack("defense_uid", logData.DefenseUid)
	r.SetTrack("defense_server_id", logData.DefenseServerId)
	r.SetTrack("defense_guild_id", logData.DefenseGuildId)
	r.SetFormationMessageByIndex(log.Param2, logData.TeamIndex)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGSTChallengeStreakMessage struct {
	helper.BaseMessage
}

func (m *LogGSTChallengeStreakMessage) Execute(log *da.Log) bool {

	var logData *sub.LogGSTChallengeStreak
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGSTChallengeStreak] json unmarshal error: %s", err.Error())
		return false
	}
	for _, team := range logData.Teams {
		r := m.GetRecorder()
		r.GetBaseMessage(log)
		r.SetTrack("guild_id", logData.GuildId)
		r.SetTrack("number", logData.Round)
		r.SetTrack("match", logData.ChallengeRound)
		r.SetTrack("team_index", team.TeamIndex)
		r.SetTrack("team_streak", team.TeamStreak)
		r.SetTrack("team_total_win", team.TeamTotalWin)
		r.SetTrack("all_team_win", logData.AllTeamWin)
		r.SetTrack("battle_zone", logData.BattleZone)
		r.SetTrack("room", logData.Room)
		r.SetTrack("room_quality", logData.RoomQuality)
		r.Write(log)
		m.PutRecorder(r)
	}

	return true
}
