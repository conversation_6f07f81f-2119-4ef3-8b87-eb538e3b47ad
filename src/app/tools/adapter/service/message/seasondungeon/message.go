package seasondungeon

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_DUNGEON_FIGHT),
		&SeasonDungeonFightMessage{}, "season_dungeon_fight", 15, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_DUNGEON_RECV),
		&SeasonDungeonRecvMessage{}, "season_dungeon_recv_award", 15, 0, 0, 1)
}

type SeasonDungeonFightMessage struct {
	helper.BaseMessage
}

func (m *SeasonDungeonFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("report_id", log.Param1)
	r.SetTrack("season_dungeon_id", log.Param10)         // 关卡id
	r.SetTrack("season_dungeon_mode", log.Param11)       // 模式 0-战斗  1-剧情
	r.SetTrack("season_dungeon_win", log.Param12)        // 输赢情况
	r.SetTrack("season_dungeon_difficulty", log.Param13) // 关卡难度
	r.SetFormationMessage(log.Param2)                    // 阵容信息
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonDungeonRecvMessage struct {
	helper.BaseMessage
}

func (m *SeasonDungeonRecvMessage) Execute(log *da.Log) bool {
	var ids []uint32
	err := helper.Json.Unmarshal([]byte(log.Param1), &ids)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("season_dungeon_recv_ids", ids)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
