package user

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ADD_PURCHASE_NUM),
		&UserAddPurchaseNum{}, "user_add_purchase_num", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FORMATION),
		&UserFormation{}, "user_formation", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SET_NAME),
		&UserSetName{}, "user_set_name", 10, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DUEL),
		&UserDuel{}, "user_duel", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ACCUSATION),
		&UserAccusation{}, "user_accusation", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECV_SHARE_AWARD),
		&UserRecvShareAward{}, "recv_share_award", 9, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GIFT_CODE),
		&UserRecvGiftCodeAward{}, "recv_gift_code_award", 9, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_VIP_UP),
		&VipUpMessage{}, "vip_up", 13, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_QUESTIONNAIRE_FINISH),
		&QuestionnaireFinishMessage{}, "questionnaire_finish", 9, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECV_H5_DESKTOP_REWARD),
		&RecvH5DesktopReward{}, "recv_h5_desktop_reward", 9, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GM_DELETE_RESOURCES),
		&GmDeleteResources{}, "gm_delete_resources", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MUTE_ACCOUNT),
		&MuteAccountMessage{}, "mute_account", 13, 0, 0, 1)
}

type UserAddPurchaseNum struct {
	helper.BaseMessage
}

func (m *UserAddPurchaseNum) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("number_type", log.Param10)
	r.SetTrack("purchase_num", log.Param11)
	r.SetTrack("buy_type", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type UserFormation struct {
	helper.BaseMessage
}

func (m *UserFormation) Execute(log *da.Log) bool {
	var formation *cl.Formation
	err := helper.Json.Unmarshal([]byte(log.Param1), &formation)
	if err != nil {
		l4g.Errorf("[Logger] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}
	if formation.Id == 0 {
		l4g.Errorf("[Logger] formation id is 0")
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("formation_id", formation.Id)
	for index, team := range formation.Teams {
		formationStr := make([]*sub.FormationInfoStr, 0, len(team.Info))
		for _, v := range team.Info {
			f := &sub.FormationInfoStr{
				Pos: v.Pos,
				Hid: strconv.FormatUint(v.Hid, 10),
			}
			if v.Uid > 0 {
				f.Uid = strconv.FormatUint(v.Uid, 10)
			}
			formationStr = append(formationStr, f)
		}
		if index == 0 {
			r.SetTrack("formation_heroes_array_str", formationStr)
			r.SetTrack("formation_artifacts_array", team.Artifacts)
		} else {
			index := fmt.Sprintf("%d", index)
			r.SetTrack("formation_heroes_array_str_"+index, formationStr)
			r.SetTrack("formation_artifacts_array_"+index, team.Artifacts)
		}
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type UserSetName struct {
	helper.BaseMessage
}

func (m *UserSetName) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("user_old_name", log.Param1)
	r.SetTrack("user_name", log.Param2)
	r.SetAfter("role_name", log.Param2)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type UserDuel struct {
	helper.BaseMessage
}

func (m *UserDuel) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("duel_enemy_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("win", helper.FormatBool(log.Param11))
	r.SetTrack("ctx_id_str", strconv.FormatUint(log.Param12, 10))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type UserAccusation struct {
	helper.BaseMessage
}

func (m *UserAccusation) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("accused_uid_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("accused_reason", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type UserRecvShareAward struct {
	helper.BaseMessage
}

func (m *UserRecvShareAward) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("recv_share_award_count", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type UserRecvGiftCodeAward struct {
	helper.BaseMessage
}

func (m *UserRecvGiftCodeAward) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("gift_code", log.Param1)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type VipUpMessage struct {
	helper.BaseMessage
}

func (m *VipUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("old_vip", log.Param10)
	r.SetTrack("old_vip_exp", log.Param11)
	r.SetTrack("vip_exp", log.Param12)
	r.SetTrack("vip_level", log.VipLevel)
	r.SetAfter("vip_level", log.VipLevel)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type QuestionnaireFinishMessage struct {
	helper.BaseMessage
}

func (m *QuestionnaireFinishMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("questionnaire_id", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type RecvH5DesktopReward struct {
	helper.BaseMessage
}

func (m *RecvH5DesktopReward) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GmDeleteResources struct {
	helper.BaseMessage
}

func (m *GmDeleteResources) Execute(log *da.Log) bool {
	var resources []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param1), &resources)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}

	size := len(resources)
	if size == 0 {
		l4g.Error("[Logger] resource nil in log %+v", log)
		return true
	}

	//类型转换
	newResource := make([]*sub.ResourceStr, 0, size)
	for _, v := range resources {
		res := &sub.ResourceStr{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count,
		}
		if v.Id > 0 {
			res.Id = strconv.FormatUint(uint64(v.Id), 10)
		}
		if v.TotalCount > 0 {
			res.TotalCount = strconv.FormatUint(uint64(v.TotalCount), 10)
		}
		if len(v.Attrs) > 0 {
			res.Attrs = make([]*sub.AttrStr, 0, len(v.Attrs))
			for _, vv := range v.Attrs {
				res.Attrs = append(res.Attrs, &sub.AttrStr{
					Type:  vv.Type,
					Value: strconv.FormatUint(uint64(vv.Value), 10),
				})
			}
		}

		newResource = append(newResource, res)
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("delete_resources", newResource)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type MuteAccountMessage struct {
	helper.BaseMessage
}

func (m *MuteAccountMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("operate_uid", log.Param10)
	r.SetTrack("mute_uid", log.Param11)
	r.SetTrack("mute_start_time", log.Param12)
	r.SetTrack("mute_end_time", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
