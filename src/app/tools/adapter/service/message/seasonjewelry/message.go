package seasonjewelry

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"
	"encoding/json"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
	"strconv"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_GET),
		&SeasonJewelryGetMessage{}, "season_jewelry_get", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_WEAR),
		&SeasonJewelryWearMessage{}, "season_jewelry_wear", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_LEVEL_UP),
		&SeasonJewelrySkillLevelUpMessage{}, "season_jewelry_skill_level_up", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CLASS_UP),
		&SeasonJewelrySkillClassUpMessage{}, "season_jewelry_skill_class_up", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE),
		&SeasonJewelrySkillChangeMessage{}, "season_jewelry_skill_change", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE_CONFIRM),
		&SeasonJewelrySkillChangeConfirmMessage{}, "season_jewelry_skill_change_confirm", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_DECOMPOSE),
		&SeasonJewelryDecomposeMessage{}, "season_jewelry_decompose", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_JEWELRY_RECYCLE),
		&SeasonJewelryRecycleMessage{}, "season_jewelry_recycle", 13, 0, 0, 0)
}

type SeasonJewelryGetMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelryGetMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var data *sub.LogSeasonJewelryData
	err := json.Unmarshal([]byte(log.Param1), &data)
	if err != nil {
		l4g.Errorf("[SeasonJewelryGetMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("jewelry", data)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonJewelryWearMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelryWearMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var data *sub.LogSeasonJewelryData
	err := json.Unmarshal([]byte(log.Param1), &data)
	if err != nil {
		l4g.Errorf("[SeasonJewelryWearMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("jewelry_after_wear", data)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonJewelrySkillLevelUpMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelrySkillLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var data1, data2 *sub.LogSeasonJewelryData
	err := json.Unmarshal([]byte(log.Param1), &data1)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillLevelUpMessage] json unmarshal error: %s", err.Error())
		return false
	}
	err = json.Unmarshal([]byte(log.Param2), &data2)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillLevelUpMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("jewelry_before_level_up", data1)
	r.SetTrack("jewelry_after_level_up", data2)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonJewelrySkillClassUpMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelrySkillClassUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var data1, data2 *sub.LogSeasonJewelryData
	err := json.Unmarshal([]byte(log.Param1), &data1)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillClassUpMessage] json unmarshal error: %s", err.Error())
		return false
	}
	err = json.Unmarshal([]byte(log.Param2), &data2)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillClassUpMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("jewelry_before_class_up", data1)
	r.SetTrack("jewelry_after_class_up", data2)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonJewelrySkillChangeMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelrySkillChangeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var data1, data2 *sub.LogSeasonJewelryData
	err := json.Unmarshal([]byte(log.Param1), &data1)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillChangeMessage] json unmarshal error: %s", err.Error())
		return false
	}
	err = json.Unmarshal([]byte(log.Param2), &data2)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillChangeMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("jewelry_before_skill_change", data1)
	r.SetTrack("jewelry_after_skill_change", data2)
	r.SetTrack("jewelry_skill_pos", log.Param10)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonJewelrySkillChangeConfirmMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelrySkillChangeConfirmMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var data1, data2 *sub.LogSeasonJewelryData
	err := json.Unmarshal([]byte(log.Param1), &data1)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillChangeConfirmMessage] json unmarshal error: %s", err.Error())
		return false
	}
	err = json.Unmarshal([]byte(log.Param2), &data2)
	if err != nil {
		l4g.Errorf("[SeasonJewelrySkillChangeConfirmMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("jewelry_before_skill_change_confirm", data1)
	r.SetTrack("jewelry_after_skill_change_confirm", data2)
	r.SetTrack("jewelry_skill_pos", log.Param10)
	r.SetTrack("op_type", log.Param11)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonJewelryDecomposeMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelryDecomposeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var data *sub.LogSeasonJewelryData
	err := json.Unmarshal([]byte(log.Param1), &data)
	if err != nil {
		l4g.Errorf("[SeasonJewelryDecomposeMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("jewelry", data)

	if log.Param2 != "" {
		var resources []*cl.Resource
		err := helper.Json.Unmarshal([]byte(log.Param2), &resources)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
		size := len(resources)
		if size > 0 {
			//类型转换
			newResource := make([]*sub.ResourceStr, 0, size)
			for _, v := range resources {
				res := &sub.ResourceStr{
					Type:  v.Type,
					Value: v.Value,
					Count: v.Count,
				}
				if v.Id > 0 {
					res.Id = strconv.FormatUint(uint64(v.Id), 10)
				}
				if v.TotalCount > 0 {
					res.TotalCount = strconv.FormatUint(uint64(v.TotalCount), 10)
				}
				if len(v.Attrs) > 0 {
					res.Attrs = make([]*sub.AttrStr, 0, len(v.Attrs))
					for _, vv := range v.Attrs {
						res.Attrs = append(res.Attrs, &sub.AttrStr{
							Type:  vv.Type,
							Value: strconv.FormatUint(uint64(vv.Value), 10),
						})
					}
				}

				newResource = append(newResource, res)
			}
			r.SetTrack("decompose_rewards", newResource)
		}
	}

	if log.Param3 != "" {
		var resources []*cl.Resource
		err := helper.Json.Unmarshal([]byte(log.Param3), &resources)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
		size := len(resources)
		if size > 0 {
			//类型转换
			newResource := make([]*sub.ResourceStr, 0, size)
			for _, v := range resources {
				res := &sub.ResourceStr{
					Type:  v.Type,
					Value: v.Value,
					Count: v.Count,
				}
				if v.Id > 0 {
					res.Id = strconv.FormatUint(uint64(v.Id), 10)
				}
				if v.TotalCount > 0 {
					res.TotalCount = strconv.FormatUint(uint64(v.TotalCount), 10)
				}
				if len(v.Attrs) > 0 {
					res.Attrs = make([]*sub.AttrStr, 0, len(v.Attrs))
					for _, vv := range v.Attrs {
						res.Attrs = append(res.Attrs, &sub.AttrStr{
							Type:  vv.Type,
							Value: strconv.FormatUint(uint64(vv.Value), 10),
						})
					}
				}

				newResource = append(newResource, res)
			}
			r.SetTrack("return_rewards", newResource)
		}
	}

	r.SetTrack("op_type", log.Param10)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonJewelryRecycleMessage struct {
	helper.BaseMessage
}

func (m *SeasonJewelryRecycleMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	if log.Param1 != "" {
		var resources []*cl.Resource
		err := helper.Json.Unmarshal([]byte(log.Param1), &resources)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
		size := len(resources)
		if size > 0 {
			//类型转换
			newResource := make([]*sub.ResourceStr, 0, size)
			for _, v := range resources {
				res := &sub.ResourceStr{
					Type:  v.Type,
					Value: v.Value,
					Count: v.Count,
				}
				if v.Id > 0 {
					res.Id = strconv.FormatUint(uint64(v.Id), 10)
				}
				if v.TotalCount > 0 {
					res.TotalCount = strconv.FormatUint(uint64(v.TotalCount), 10)
				}
				if len(v.Attrs) > 0 {
					res.Attrs = make([]*sub.AttrStr, 0, len(v.Attrs))
					for _, vv := range v.Attrs {
						res.Attrs = append(res.Attrs, &sub.AttrStr{
							Type:  vv.Type,
							Value: strconv.FormatUint(uint64(vv.Value), 10),
						})
					}
				}

				newResource = append(newResource, res)
			}
			r.SetTrack("recycle_rewards", newResource)
		}
	}
	r.SetTrack("recycle_points", log.Param10)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
