package seasonmap

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"encoding/json"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_MAP_TASK_AWARD),
		&SeasonMapTaskMessage{}, "season_map_task", 15, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_MAP_FIGHT),
		&SeasonMapFightMessage{}, "season_map_fight", 15, 0, 0, 1)
}

type SeasonMapTaskMessage struct {
	helper.BaseMessage
}

func (m *SeasonMapTaskMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	if log.Param1 != "" {
		taskIds := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("task_id", taskIds)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonMapFightMessage struct {
	helper.BaseMessage
}

func (m *SeasonMapFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogSeasonMapFight
	err := json.Unmarshal([]byte(log.Param2), &logData)
	if err != nil {
		l4g.Errorf("[LogSeasonDoorFight] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetFormationMessage(log.Param1)
	r.SetTrack("monster_id", logData.MonsterId)
	r.SetTrack("battle_report_id", logData.BattleReportId)
	r.SetTrack("space_id", logData.Position)
	r.SetTrack("is_sweep", logData.IsSweep)
	r.SetTrack("is_kill", logData.Kill)
	r.SetTrack("damages", logData.TeamDamages)
	r.SetTrack("progress", logData.TeamProgress)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
