package hero

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"encoding/json"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_LEVEL_UP),
		&HeroLevelUpMessage{}, "hero_level_up", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_STAGE_UP),
		&HeroStageUpMessage{}, "hero_stage_up", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_STAR_UP),
		&HeroStarUpMessage{}, "hero_star_up", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_BUY_SLOT),
		&HeroBuySlotMessage{}, "hero_buy_slot", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_UPDATE_LOCK_STATUS),
		&HeroUpdateLockStatusMessage{}, "hero_update_lock_status", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_DECOMPOSE),
		&HeroDecomposeMessage{}, "hero_decompose", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_BACK),
		&HeroBackMessage{}, "hero_back", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_REVIVE),
		&HeroReviveMessage{}, "hero_revive", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_CHANGE_RANDOM),
		&HeroChangeRandomMessage{}, "hero_change_random", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_CHANGE_SAVE),
		&HeroChangeSaveMessage{}, "hero_change_save", 17, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_CONVERSION),
		&HeroConversionMessage{}, "hero_conversion", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_GEM_LEVEL_UP),
		&HeroGemLevelUpMessage{}, "hero_gem_level_up", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_EXCHANGE),
		&HeroExchangeMessage{}, "hero_exchange", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_AWAKEN),
		&HeroAwakenMessage{}, "hero_awaken", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_CONVERT),
		&HeroConvertMessage{}, "hero_convert", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_EMBLEM_SKILL),
		&HeroEmblemSkillMessage{}, "hero_emblem_skill", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_HERO_TAG_UPDATE),
		&HeroTagUpdateMessage{}, "hero_tag_update", 25, 0, 0, 0)
}

type HeroLevelUpMessage struct {
	helper.BaseMessage
}

func (m *HeroLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_level", log.Param11)
	r.SetTrack("hero_add_level", log.Param12)
	r.SetTrack("hero_sys_id", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroStageUpMessage struct {
	helper.BaseMessage
}

func (m *HeroStageUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_stage", log.Param11)
	r.SetTrack("hero_sys_id", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroStarUpMessage struct {
	helper.BaseMessage
}

func (m *HeroStarUpMessage) Execute(log *da.Log) bool {
	var hids []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &hids)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	hidsArray := make([]string, 0, len(hids))
	for _, hid := range hids {
		hidsArray = append(hidsArray, strconv.FormatUint(hid, 10))
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_star", log.Param11)
	r.SetTrack("hero_sys_id", log.Param12)
	r.SetTrack("cost_hids", hidsArray)
	r.SetTrack("star_up_type", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroBuySlotMessage struct {
	helper.BaseMessage
}

func (m *HeroBuySlotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_slot_num", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroUpdateLockStatusMessage struct {
	helper.BaseMessage
}

func (m *HeroUpdateLockStatusMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	lock := helper.FormatBool(log.Param11)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("lock_hero", lock)
	r.SetTrack("hero_sys_id", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroDecomposeMessage struct {
	helper.BaseMessage
}

func (m *HeroDecomposeMessage) Execute(log *da.Log) bool {
	var heroes []*sub.LogHeroSelfData
	err := helper.Json.Unmarshal([]byte(log.Param1), &heroes)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	size := len(heroes)
	if size == 0 {
		l4g.Error("[Logger] hero nil in log %+v", log)
		return false
	}
	for _, heroData := range heroes {
		if !m.decomposeMessage(log, heroData) {
			return false
		}
	}
	return true
}

func (m *HeroDecomposeMessage) decomposeMessage(log *da.Log, data *sub.LogHeroSelfData) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(data.Id, 10))
	r.SetTrack("hero_sys_id", data.SysId)
	r.SetTrack("hero_level", data.Level)
	r.SetTrack("hero_stage", data.Stage)
	r.SetTrack("hero_star", data.Star)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroBackMessage struct {
	helper.BaseMessage
}

func (m *HeroBackMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_old_star", log.Param11)
	r.SetTrack("hero_star", log.Param12)
	r.SetTrack("hero_sys_id", log.Param13)
	r.SetTrack("hero_old_level", log.Param14)
	r.SetTrack("hero_old_stage", log.Param15)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroReviveMessage struct {
	helper.BaseMessage
}

func (m *HeroReviveMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_old_level", log.Param11)
	r.SetTrack("hero_old_stage", log.Param12)
	r.SetTrack("hero_sys_id", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroChangeRandomMessage struct {
	helper.BaseMessage
}

func (m *HeroChangeRandomMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_old_sys_id", log.Param11)
	r.SetTrack("hero_new_sys_id", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroChangeSaveMessage struct {
	helper.BaseMessage
}

func (m *HeroChangeSaveMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	save := helper.FormatBool(log.Param12)
	r.SetTrack("old_hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("old_hero_sys_id", log.Param11)
	r.SetTrack("hero_save_change", save)
	if save {
		var data *sub.LogHeroSelfData
		err := helper.Json.Unmarshal([]byte(log.Param1), &data)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
		r.SetTrack("hero_id_str", strconv.FormatUint(data.Id, 10))
		r.SetTrack("hero_sys_id", data.SysId)
		r.SetTrack("hero_level", data.Level)
		r.SetTrack("hero_stage", data.Stage)
		r.SetTrack("hero_star", data.Star)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroConversionMessage struct {
	helper.BaseMessage
}

func (m *HeroConversionMessage) Execute(log *da.Log) bool {
	var heroes []*sub.LogHeroSelfData
	err := helper.Json.Unmarshal([]byte(log.Param1), &heroes)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	size := len(heroes)
	if size == 0 {
		l4g.Error("[Logger] hero nil in log %+v", log)
		return false
	}
	for _, heroData := range heroes {
		if !m.decomposeMessage(log, heroData) {
			return false
		}
	}
	return true
}

func (m *HeroConversionMessage) decomposeMessage(log *da.Log, data *sub.LogHeroSelfData) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(data.Id, 10))
	r.SetTrack("hero_sys_id", data.SysId)
	r.SetTrack("hero_level", data.Level)
	r.SetTrack("hero_stage", data.Stage)
	r.SetTrack("hero_star", data.Star)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroGemLevelUpMessage struct {
	helper.BaseMessage
}

func (h *HeroGemLevelUpMessage) Execute(log *da.Log) bool {
	r := h.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_gem_slot", log.Param11)
	r.SetTrack("hero_gem_level", log.Param12)
	r.SetTrack("hero_gem_add_level", log.Param13)
	r.SetTrack("hero_sys_id", log.Param14)
	ret := r.Write(log)
	h.PutRecorder(r)
	return ret
}

type HeroExchangeMessage struct {
	helper.BaseMessage
}

func (m *HeroExchangeMessage) Execute(log *da.Log) bool {
	var heroes []*sub.LogHeroSelfDataStr
	err := helper.Json.Unmarshal([]byte(log.Param1), &heroes)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	size := len(heroes)
	if size == 0 {
		l4g.Error("[Logger] hero nil in log %+v", log)
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_exchange_cost", heroes)
	r.SetTrack("hero_sys_id", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroAwakenMessage struct {
	helper.BaseMessage
}

func (m *HeroAwakenMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("hero_sys_id", log.Param11)
	r.SetTrack("awaken_level", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroConvertMessage struct {
	helper.BaseMessage
}

func (m *HeroConvertMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetResourceContentMessage("hero_convert_cost", log.Param1)  // 消耗英雄
	r.SetResourceContentMessage("hero_convert_award", log.Param2) // 转换奖励

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroEmblemSkillMessage struct {
	helper.BaseMessage
}

func (m *HeroEmblemSkillMessage) Execute(log *da.Log) bool {
	if log.Param1 == "" {
		return true
	}
	var logData *sub.LogHeroEmblemSkill
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[HeroEmblemSkillMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_id_str", strconv.FormatUint(logData.Hid, 10))
	r.SetTrack("hero_sys_id", logData.SysId)
	r.SetTrack("counter_skill_active", logData.CounterSkill)
	r.SetTrack("exclusive_lv", logData.ExclusiveLv)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type HeroTagUpdateMessage struct {
	helper.BaseMessage
}

func (m *HeroTagUpdateMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("blessed_hero_id", strconv.FormatUint(log.Param10, 10))  // 原赐福英雄id
	r.SetTrack("contract_hero_id", strconv.FormatUint(log.Param11, 10)) // 原缔约英雄id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
