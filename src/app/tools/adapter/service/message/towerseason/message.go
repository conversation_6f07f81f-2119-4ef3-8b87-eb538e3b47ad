package tower

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"strconv"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TOWER_SEASON_FIGHT),
		&TowerSeasonFightMessage{}, "tower_season_fight", 17, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TOWER_SEASON_RECV_TASK_AWARD),
		&TowerSeasonRecvTaskAwardMessage{}, "tower_season_recv_task_award", 16, 0, 0, 1)
}

type TowerSeasonFightMessage struct {
	helper.BaseMessage
}

func (m *TowerSeasonFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tower_season_floor", log.Param10)
	r.SetTrack("mode", log.Param11)
	quick := helper.FormatBool(log.Param12)
	win := helper.FormatBool(log.Param13)
	r.SetTrack("season", log.Param14)
	r.SetTrack("win", win)
	r.SetTrack("quick", quick)
	r.SetFormationMessage(log.Param2)
	r.SetTrack("report_id", log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type TowerSeasonRecvTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *TowerSeasonRecvTaskAwardMessage) Execute(log *da.Log) bool {
	var taskIDs []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &taskIDs)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	idList := make([]string, 0, len(taskIDs))
	for _, id := range taskIDs {
		idList = append(idList, strconv.FormatUint(id, 10))
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tower_season_from", log.Param10) //领奖操作来源 0-玩家领奖 1-系统补发
	r.SetTrack("tower_season_task_ids", idList)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
