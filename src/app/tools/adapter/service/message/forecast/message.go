package forecast

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FORECAST_RECEIVE_AWARD),
		&ForecastReceiveAwardMessage{}, "forecast_receive_award", 10, 0, 0, 0)
}

// 新功能预告-领取奖励
type ForecastReceiveAwardMessage struct {
	helper.BaseMessage
}

func (m *ForecastReceiveAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("forecast_id", log.Param10) // 功能id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
