package rite

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RITE_MARK_COLLECTION),
		&RiteMarkCollectionMessage{}, "rite_collection", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RITE_RECYCLE),
		&RiteRecycleMessage{}, "rite_crecycle", 11, 0, 0, 0)
}

// 印记收集
type RiteMarkCollectionMessage struct {
	helper.BaseMessage
}

func (m *RiteMarkCollectionMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("item_id", log.Param13)      //印记ID
	r.SetTrack("item_quality", log.Param14) //印记品质
	r.SetTrack("item_count", log.Param15)   //数量
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 阵法回收
type RiteRecycleMessage struct {
	helper.BaseMessage
}

func (m *RiteRecycleMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("recycle_point", log.Param11) //回收分数
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
