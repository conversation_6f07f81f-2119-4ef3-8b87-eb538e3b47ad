package flower

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"strconv"

	"gitlab.qdream.com/kit/library/log/l4g"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_OCCUPY_ATTACK),
		&FlowerOccupyAttackMessage{}, "flower_occupy_attack", 16, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_OCCUPY_BE_ATTACK),
		&FlowerOccupyBeAttackMessage{}, "flower_occupy_be_attack", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_REVENGE),
		&FlowerRevengeMessage{}, "flower_revenge", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_EXTEND_OCCUPY_TIME),
		&FlowerExtendOccupyTimeMessage{}, "flower_extend_occupy_time", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_BUY_OCCUPY_ATTACK_NUM),
		&FlowerBuyOccupyAttackNumMessage{}, "flower_buy_occupy_attack_num", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_RECV_OCCUPY_AWARD),
		&FlowerRecvOccupyAwardMessage{}, "flower_recv_occupy_award", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_START_FEED),
		&FlowerStartFeedMessage{}, "flower_start_feed", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_FEED_GOBLIN),
		&FlowerFeedGoblinMessage{}, "flower_feed_goblin", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_CHANGE_GOBLIN),
		&FlowerChangeGoblinMessage{}, "flower_change_goblin", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_FEED_SPECIAL),
		&FlowerFeedSpecialMessage{}, "flower_feed_special", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_START_PLANT),
		&FlowerStartPlantMessage{}, "flower_start_plant", 16, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_SPEED_GROW),
		&FlowerSpeedGrowMessage{}, "flower_speed_grow", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_HARVEST),
		&FlowerHarvestMessage{}, "flower_harvest", 13, 0, 0, 2)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_SEARCH),
		&FlowerSearchMessage{}, "flower_search", 16, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_SNATCH),
		&FlowerSnatchMessage{}, "flower_snatch", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_BE_SNATCH),
		&FlowerBeSnatchMessage{}, "flower_be_snatch", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_LEVEL_UP),
		&FlowerLevelUpMessage{}, "flower_level_up", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_ATTACK_LEVEL_GUARD),
		&FlowerAttackLevelGuardMessage{}, "flower_attack_level_guard", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_RECV_PREVIEW_OCCUPY_AWARD),
		&FlowerRecvPreviewOccupyAwardMessage{}, "flower_recv_preview_occupy_award", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_SEND_LIKE),
		&FlowerSendLikeMessage{}, "flower_send_like", 16, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FLOWER_RECV_LIKE),
		&FlowerRecvLikeMessage{}, "flower_recv_like", 16, 0, 0, 0)
}

type FlowerOccupyAttackMessage struct {
	helper.BaseMessage
}

func (m *FlowerOccupyAttackMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_enemy_id", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("win", helper.FormatBool(log.Param11))
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param13, 10))
	r.SetTrack("is_bot", helper.FormatBool(log.Param14))
	r.SetFormationMessage(log.Param2)
	r.SetTrack("report_id", log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerOccupyBeAttackMessage struct {
	helper.BaseMessage
}

func (m *FlowerOccupyBeAttackMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_enemy_id", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("win", helper.FormatBool(log.Param11))
	r.SetFormationMessage(log.Param2)
	r.SetTrack("report_id", log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerRevengeMessage struct {
	helper.BaseMessage
}

func (m *FlowerRevengeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_log_id", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("flower_enemy_id", strconv.FormatUint(log.Param11, 10))
	r.SetTrack("win", helper.FormatBool(log.Param12))
	r.SetTrack("revenge_type", log.Param13)
	r.SetTrack("report_id", log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerExtendOccupyTimeMessage struct {
	helper.BaseMessage
}

func (m *FlowerExtendOccupyTimeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("jungle", log.Param10)
	r.SetTrack("flowerbed_pos", log.Param11)
	r.SetTrack("end_time", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerBuyOccupyAttackNumMessage struct {
	helper.BaseMessage
}

func (m *FlowerBuyOccupyAttackNumMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("buy_attack_num", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerRecvOccupyAwardMessage struct {
	helper.BaseMessage
}

func (m *FlowerRecvOccupyAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("add_flower_exp", log.Param10)
	r.SetTrack("flower_exp", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerStartFeedMessage struct {
	helper.BaseMessage
}

func (m *FlowerStartFeedMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("current_goblin", log.Param10)
	r.SetTrack("next_goblin", log.Param11)
	r.SetTrack("tree_type", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerFeedGoblinMessage struct {
	helper.BaseMessage
}

func (m *FlowerFeedGoblinMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("current_goblin", log.Param10)
	r.SetTrack("next_goblin", log.Param11)
	r.SetTrack("current_tree_score", log.Param12)
	r.SetTrack("add_score", log.Param13)
	r.SetTrack("tree_type", log.Param14)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerChangeGoblinMessage struct {
	helper.BaseMessage
}

func (m *FlowerChangeGoblinMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("old_next_goblin", log.Param10)
	r.SetTrack("new_next_goblin", log.Param11)
	r.SetTrack("tree_type", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerFeedSpecialMessage struct {
	helper.BaseMessage
}

func (m *FlowerFeedSpecialMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("old_tree_score", log.Param10)
	r.SetTrack("tree_type", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerStartPlantMessage struct {
	helper.BaseMessage
}

func (m *FlowerStartPlantMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tree_score", log.Param10)
	r.SetTrack("flower_plant_start_time", log.Param11)
	r.SetTrack("flower_plant_end_time", log.Param12)
	r.SetTrack("tree_type", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerSpeedGrowMessage struct {
	helper.BaseMessage
}

func (m *FlowerSpeedGrowMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tree_type", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerHarvestMessage struct {
	helper.BaseMessage
}

func (m *FlowerHarvestMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_level", log.Param10)
	r.SetTrack("flower_exp", log.Param11)
	r.SetTrack("flower_is_crit", helper.FormatBool(log.Param12))
	r.SetTrack("tree_type", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerSearchMessage struct {
	helper.BaseMessage
}

func (m *FlowerSearchMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tree_score", log.Param10)
	r.SetTrack("ctx_id_str", strconv.FormatUint(log.Param11, 10))
	r.SetTrack("flower_enemy_id", strconv.FormatUint(log.Param12, 10))
	r.SetTrack("is_bot", helper.FormatBool(log.Param13))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerSnatchMessage struct {
	helper.BaseMessage
}

func (m *FlowerSnatchMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_enemy_id", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("win", helper.FormatBool(log.Param11))
	r.SetTrack("is_bot", helper.FormatBool(log.Param14))
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param13, 10))
	r.SetFormationMessage(log.Param2)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerBeSnatchMessage struct {
	helper.BaseMessage
}

func (m *FlowerBeSnatchMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_enemy_id", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("win", helper.FormatBool(log.Param11))
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param13, 10))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerLevelUpMessage struct {
	helper.BaseMessage
}

func (m *FlowerLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_level", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("flower_old_level", strconv.FormatUint(log.Param11, 10))
	r.SetTrack("flower_exp", strconv.FormatUint(log.Param12, 10))
	ret := r.Write(log)

	return ret
}

type FlowerAttackLevelGuardMessage struct {
	helper.BaseMessage
}

func (m *FlowerAttackLevelGuardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("flower_monster_group", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("win", helper.FormatBool(log.Param11))

	ret := r.Write(log)
	return ret
}

type FlowerRecvPreviewOccupyAwardMessage struct {
	helper.BaseMessage
}

func (m *FlowerRecvPreviewOccupyAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("add_flower_exp", log.Param10)
	r.SetTrack("flower_exp", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerSendLikeMessage struct {
	helper.BaseMessage
}

func (m *FlowerSendLikeMessage) Execute(log *da.Log) bool {
	var logIds []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &logIds)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	logIdList := make([]string, 0, len(logIds))
	for _, id := range logIds {
		logIdList = append(logIdList, strconv.FormatUint(id, 10))
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("log_list", logIdList)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FlowerRecvLikeMessage struct {
	helper.BaseMessage
}

func (m *FlowerRecvLikeMessage) Execute(log *da.Log) bool {
	var logIds []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &logIds)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	logIdList := make([]string, 0, len(logIds))
	for _, id := range logIds {
		logIdList = append(logIdList, strconv.FormatUint(id, 10))
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("log_list", logIdList)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
