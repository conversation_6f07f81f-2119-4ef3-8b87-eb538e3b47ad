package chat

import (
	"adapter/service/helper"
)

func Init(msgMgr *helper.MessageM) {
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_CHAT),
	//	&ChatMessage{}, "chat_chat", 13, 0, 0, 0)
	//msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_CHAT_LIKE),
	//	&ChatLikeMessage{}, "chat_like", 11, 0, 0, 0)
}

//// 聊天-聊天
//type ChatMessage struct {
//	helper.BaseMessage
//}
//
//func (m *ChatMessage) Execute(log *da.Log) bool {
//	r := m.GetRecorder()
//	r.GetBaseMessage(log)
//	r.SetTrack("chat_sender_str", strconv.FormatUint(log.Param10, 10))   // 发送者
//	r.SetTrack("chat_receiver_str", strconv.FormatUint(log.Param11, 10)) // 接收者
//	r.SetTrack("chat_channel", log.Param12)                              // 聊天频道
//	r.SetTrack("chat_content", log.Param1)                               // 发送的内容
//	ret := r.Write(log)
//	m.PutRecorder(r)
//	return ret
//}
//
//// 聊天-点赞
//type ChatLikeMessage struct {
//	helper.BaseMessage
//}
//
//func (m *ChatLikeMessage) Execute(log *da.Log) bool {
//	r := m.GetRecorder()
//	r.GetBaseMessage(log)
//	r.SetTrack("chat_msg_id_str", strconv.FormatUint(log.Param10, 10)) // 消息id
//	r.SetTrack("chat_msg_type", log.Param11)                           // 消息type
//	ret := r.Write(log)
//	m.PutRecorder(r)
//	return ret
//}
