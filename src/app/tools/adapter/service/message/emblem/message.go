package emblem

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"
	"bytes"
	"fmt"

	"strconv"

	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_LEVELUP),
		&EmblemLevelUpMessage{}, "emblem_level_up", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_STAGEUP),
		&EmblemStageUpMessage{}, "emblem_stage_up", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_BLESSING),
		&EmblemBlessingMessage{}, "emblem_blessing", 17, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_DECOMPOSE),
		&EmblemDecomposeMessage{}, "emblem_decompose", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_COMPOSE),
		&EmblemComposeMessage{}, "emblem_compose", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_REVIVE),
		&EmblemReviveMessage{}, "emblem_revive", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_EXCLUSIVE),
		&EmblemExclusiveMessage{}, "emblem_exclusive", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_SET_AUTO_DECOMPOSE),
		&EmblemSetAutoDecomposeMessage{}, "emblem_set_auto_decompose", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_BUY_SLOT),
		&EmblemBuySlotMessage{}, "emblem_buy_slot", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_UPGRADE),
		&EmblemUpgradeMessage{}, "emblem_upgrade", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_SUCCINCT),
		&EmblemSuccinctMessage{}, "emblem_succinct", 21, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_SUCCINCT_LOCK_OR_SAVE),
		&EmblemSuccinctLockOrSaveMessage{}, "emblem_succinct_lock_or_save", 22, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_EMBLEM_SUCCINCT_ITEM_CONFLATE),
		&EmblemSuccinctItemConflateMessage{}, "emblem_succinct_item_conflate", 10, 0, 0, 0)
}

// 纹章升级
type EmblemLevelUpMessage struct {
	helper.BaseMessage
}

func (m *EmblemLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("emblem_id_str", strconv.FormatUint(log.Param10, 10)) // 纹章uuid
	r.SetTrack("emblem_sys_id", log.Param11)                         // 纹章sysid
	r.SetTrack("emblem_old_level", log.Param12)                      // 升级前的等级
	r.SetTrack("emblem_new_level", log.Param13)                      // 升级后的等级
	r.SetTrack("hero_sys_id", log.Param14)                           // 英雄量表id
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param15, 10))   // 英雄唯一id
	r.SetTrack("emblem_old_score", log.Param16)                      // 操作前评分
	r.SetTrack("emblem_new_score", log.Param17)                      // 操作后评分
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 纹章突破
type EmblemStageUpMessage struct {
	helper.BaseMessage
}

func (m *EmblemStageUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("emblem_id_str", strconv.FormatUint(log.Param10, 10)) // 纹章uuid
	r.SetTrack("emblem_new_sys_id", log.Param11)                     // 纹章新的sysid
	r.SetTrack("emblem_old_sys_id", log.Param12)                     // 纹章旧的sysid
	r.SetTrack("hero_sys_id", log.Param13)                           // 英雄量表id
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param14, 10))   // 英雄唯一id
	r.SetTrack("emblem_old_score", log.Param15)                      // 操作前评分
	r.SetTrack("emblem_new_score", log.Param16)                      // 操作后评分
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 纹章祝福
type EmblemBlessingMessage struct {
	helper.BaseMessage
}

func (m *EmblemBlessingMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("emblem_id_str", strconv.FormatUint(log.Param10, 10)) // 纹章uuid
	r.SetTrack("emblem_sys_id", log.Param11)                         // 纹章sysid
	r.SetTrack("blessing_level", log.Param12)                        // 纹章祝福等级
	r.SetTrack("hero_sys_id", log.Param13)                           // 英雄量表id
	r.SetTrack("hero_id_str", strconv.FormatUint(log.Param14, 10))   // 英雄唯一id
	r.SetTrack("emblem_old_score", log.Param15)                      // 操作前评分
	r.SetTrack("emblem_new_score", log.Param16)                      // 操作后评分
	//r.SetTrack("blessing_attr_progress", log.Param1) 	  // 纹章祝福后的属性条 - 废弃
	attrMap := make(map[uint32]uint32)
	err := helper.Json.Unmarshal([]byte(log.Param1), &attrMap)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}

	for attrType, value := range attrMap {
		var buffer bytes.Buffer
		buffer.WriteString("blessing_attr_")
		buffer.WriteString(strconv.FormatUint(uint64(attrType), 10))
		r.SetTrack(buffer.String(), value) // 属性type -> 变化的值
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 纹章分解
type EmblemDecomposeMessage struct {
	helper.BaseMessage
}

func (m *EmblemDecomposeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	var emblemInfo []*sub.CultivateScoreStr
	r.GetBaseMessage(log)
	//r.SetTrack("decompose_emblems", log.Param1) //分解的纹章 - 废弃
	if log.Param1 != "" {
		err := helper.Json.Unmarshal([]byte(log.Param1), &emblemInfo)
		if err != nil {
			l4g.Error("[EmblemDecomposeMessage] json unmarshal error: %v data: %s", err, log.Param1)
		}
		r.SetTrack("decompose_emblem_info_str", emblemInfo) //分解的纹章信息
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 纹章合成
type EmblemComposeMessage struct {
	helper.BaseMessage
}

func (m *EmblemComposeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	var emblemInfo []*sub.CultivateScoreStr
	r.GetBaseMessage(log)
	r.SetTrack("emblem_sys_id", log.Param10) // 纹章sysid
	r.SetTrack("emblem_count", log.Param11)  // 纹章数量
	//r.SetTrack("compose_cost_emblems", log.Param1) // 消耗的纹章 - 废弃
	if log.Param1 != "" {
		emblemIDs := helper.Int64SliceToStringSlice(log.Param1)
		r.SetTrack("compose_cost_emblem_ids", emblemIDs) // 消耗的纹章
	}
	if log.Param2 != "" {
		err := helper.Json.Unmarshal([]byte(log.Param2), &emblemInfo)
		if err != nil {
			l4g.Error("[EmblemComposeMessage] json unmarshal error: %v data: %s", err, log.Param2)
		}
		r.SetTrack("compose_emblem_info_str", emblemInfo) //合成后纹章信息
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 纹章重生
type EmblemReviveMessage struct {
	helper.BaseMessage
}

func (m *EmblemReviveMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	var emblemInfo []*sub.CultivateScoreStr
	r.GetBaseMessage(log)
	//r.SetTrack("revive_emblems", log.Param1) //重生的纹章 - 废弃
	if log.Param1 != "" {
		err := helper.Json.Unmarshal([]byte(log.Param1), &emblemInfo)
		if err != nil {
			l4g.Error("[EmblemReviveMessage] json unmarshal error: %v data: %s", err, log.Param1)
		}
		r.SetTrack("revive_emblem_info_str", emblemInfo) //重生后纹章信息
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 获得专属纹章
type EmblemExclusiveMessage struct {
	helper.BaseMessage
}

func (m *EmblemExclusiveMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("emblem_id_str", strconv.FormatUint(log.Param10, 10)) // 纹章uuid
	r.SetTrack("emblem_sys_id", log.Param11)                         // 纹章sysid
	r.SetTrack("hero_sys_id", log.Param12)                           // 专属英雄sysid
	r.SetTrack("emblem_exclusive_skill_id", log.Param13)             // 专属技能id
	r.SetTrack("reason", log.Param14)                                // 产出来源
	r.SetTrack("emblem_suit_id", log.Param15)                        // 套装ID
	r.SetTrack("emblem_suit_type", log.Param16)                      // 套装type
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 设置装备自动分解品质
type EmblemSetAutoDecomposeMessage struct {
	helper.BaseMessage
}

func (m *EmblemSetAutoDecomposeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	if log.Param1 != "" {
		autoRare := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("emblem_auto_rare", autoRare) // 自动分解的品质
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 购买符文背包栏位
type EmblemBuySlotMessage struct {
	helper.BaseMessage
}

func (m *EmblemBuySlotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("emblem_buy_slot_times", log.Param10) // 符文背包栏位购买次数
	r.SetTrack("emblem_slot_count", log.Param11)     // 最新符文背包栏位数量
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type EmblemUpgradeMessage struct {
	helper.BaseMessage
}

func (m *EmblemUpgradeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("emblem_upgrade_pre_emblems", log.Param1)   // 升阶前纹章的信息
	r.SetTrack("emblem_upgrade_after_emblems", log.Param2) // 升阶后纹章的信息
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type EmblemSuccinctMessage struct {
	helper.BaseMessage
}

func (m *EmblemSuccinctMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var preSuccinctLog *sub.EmblemSuccinctLog
	if log.Param2 != "" {
		err := helper.Json.Unmarshal([]byte(log.Param2), &preSuccinctLog)
		if err != nil {
			l4g.Error("[EmblemSuccinctMessage] json unmarshal error: %v data: %s", err, log.Param1)
			return true
		}
	}
	var succinctLog *sub.EmblemSuccinctLog
	if log.Param3 != "" {
		err := helper.Json.Unmarshal([]byte(log.Param3), &succinctLog)
		if err != nil {
			l4g.Error("[EmblemSuccinctMessage] json unmarshal error: %v data: %s", err, log.Param1)
			return true
		}
	}
	r.SetTrack("emblem_sys_id", succinctLog.SysId)
	r.SetTrack("emblem_id_str", strconv.FormatUint(succinctLog.Id, 10)) //
	r.SetTrack("hero_sys_id", succinctLog.HeroSysId)                    //
	r.SetTrack("is_lock", succinctLog.LockId)

	if preSuccinctLog != nil {
		r.SetTrack("skill_id_before", preSuccinctLog.TmpSkillId)
		r.SetTrack("suit_id_before", preSuccinctLog.TmpSuitId)
		r.SetTrack("emblem_affix_id_before", preSuccinctLog.TmpAffixId)
		r.SetTrack("emblem_affix_type_before", preSuccinctLog.AffixType)
	}

	r.SetTrack("skill_id_after", succinctLog.TmpSkillId)
	r.SetTrack("suit_id_after", succinctLog.TmpSuitId)
	r.SetTrack("emblem_affix_id_after", succinctLog.TmpAffixId)
	r.SetTrack("emblem_affix_type_after", succinctLog.AffixType)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type EmblemSuccinctLockOrSaveMessage struct {
	helper.BaseMessage
}

func (m *EmblemSuccinctLockOrSaveMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var emblem *cl.EmblemInfo
	if log.Param1 != "" {
		err := helper.Json.Unmarshal([]byte(log.Param1), &emblem)
		if err != nil {
			l4g.Error("[EmblemSuccinctMessage] json unmarshal error: %v data: %s", err, log.Param1)
			return true
		}
	}
	r.SetTrack("emblem_sys_id", emblem.SysId)
	r.SetTrack("emblem_id_str", strconv.FormatUint(emblem.Id, 10)) //
	r.SetTrack("hero_sys_id", emblem.AdditiveHero)                 //
	r.SetTrack("emblem_succinct_los_op_type", log.Param15)         // 操作类型，1：锁定   2：保存
	r.SetTrack("skill_id_before", log.Param10)                     // 操作前纹章的信息
	r.SetTrack("skill_id_after", emblem.SkillId)
	r.SetTrack("suit_id_before", log.Param11)
	r.SetTrack("suit_id_after", log.Param16)
	r.SetTrack("emblem_affix_id_before", log.Param12)
	r.SetTrack("emblem_lock_id_before", log.Param13) // 操作后纹章的信息
	if emblem.Succinct != nil {
		r.SetTrack("emblem_affix_id_after", emblem.Succinct.AffixId)
		r.SetTrack("emblem_lock_id_after", emblem.Succinct.LockId)
	}
	r.SetTrack("emblem_sys_id_before", log.Param14)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type EmblemSuccinctItemConflateMessage struct {
	helper.BaseMessage
}

func (m *EmblemSuccinctItemConflateMessage) Execute(log *da.Log) bool {
	var targetResources []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param1), &targetResources)
	if err != nil {
		l4g.Error("[Logger] EmblemSuccinctItemConflateMessage: json unmarshal error: %v", err)
		return true
	}
	var awardsString string
	for _, award := range targetResources {
		awardsString += fmt.Sprintf("type:%d,value:%d,count:%d;", award.Type, award.Value, award.Count)
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("emblem_succinct_item_conflate_target", awardsString) // 合成的目标道具
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
