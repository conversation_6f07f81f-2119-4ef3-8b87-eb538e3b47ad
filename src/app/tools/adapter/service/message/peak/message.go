package peak

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_PEAK_RECV_INVITE_REWARD),
		&PeakRecvInviteRewardMessage{}, "peak_recv_invite_reward", 17, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_PEAK_FIGHT),
		&PeakFightMessage{}, "peak_fight", 21, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_PEAK_DO_GUESS),
		&PeakDoGuessMessage{}, "peak_do_guess", 19, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_PEAK_WORSHIP),
		&PeakWorshipMessage{}, "peak_worship", 15, 0, 0, 0)
}

type PeakRecvInviteRewardMessage struct {
	helper.BaseMessage
}

func (m *PeakRecvInviteRewardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type PeakFightMessage struct {
	helper.BaseMessage
}

func (m *PeakFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("report_id", log.Param1)
	r.SetTrack("peak_left_score", log.Param10)
	r.SetTrack("peak_right_score", log.Param11)
	r.SetTrack("win", log.Param12)
	r.SetTrack("peak_match_id", log.Param16)
	r.SetTrack("right_uid", strconv.FormatUint(log.Param17, 10))
	r.SetTrack("peak_phase", log.Param18)
	r.SetFormationMessage(log.Param2)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type PeakDoGuessMessage struct {
	helper.BaseMessage
}

func (m *PeakDoGuessMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("peak_match_id", log.Param10)
	r.SetTrack("peak_group", log.Param11)
	r.SetTrack("peak_winner_uid", log.Param12)
	r.SetTrack("peak_guess_count", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type PeakWorshipMessage struct {
	helper.BaseMessage
}

func (m *PeakWorshipMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
