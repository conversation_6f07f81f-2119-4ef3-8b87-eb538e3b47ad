package snapshot

import (
	"adapter/service/config"
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"fmt"
	"strconv"
	"strings"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	if config.GArea == "" || config.GArea == "en" {
		return
	}
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_USER_WM),
		&UserSnapshotMessage{}, "chardata_snapshot", 54, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_HERO_WM),
		&FighterSnapshotMessage{}, "fighterdata_snapshot", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_BAG_WM),
		&ChardataBagSnapshotMessage{}, "chardatabag_snapshot", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_ARTIFACT_WM),
		&ArtifactSnapshotMessage{}, "artifactdata_snapshot", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_GUILD_WM),
		&GuildSnapshotMessage{}, "guilddata_snapshot", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_WORLD_BOSS_RANK_WM),
		&WorldBossSnapshotMessage{}, "worldbossrankdata_snapshot", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_PEAK_SEASON_RANK_WM),
		&PeakRankSnapshotMessage{}, "peakrankdata_snapshot", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_GST_GUILD_WM),
		&GstSnapshotMessage{}, "gvgdata_snapshot", 12, 0, 0, 0)
}

type UserSnapshotMessage struct {
	helper.BaseMessage
}

func (m *UserSnapshotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	// r.SetTrack("skin_id", log.Param10)
	// r.SetTrack("hero_sys_id", log.Param11)
	var userSnapshot *sub.UserSnapshotForWm
	err := helper.Json.Unmarshal([]byte(log.Param1), &userSnapshot)
	if err != nil {
		l4g.Error("[UserSnapshotMessage] json unmarshal error: %v", err)
		return false
	}
	r.SetTrack("lev", log.Level)
	r.SetTrack("exp", userSnapshot.Exp)
	// r.SetTrack("occupation")
	r.SetTrack("totalcash", log.TotalCash) // log.TotalCash 人民币单位是元 美元的单位是美分
	r.SetTrack("viplev", log.VipLevel)
	// r.SetTrack("silvercoinleft", 0)
	r.SetTrack("goldcoinleft", log.Gold)
	r.SetTrack("yuanbaoleft", log.PayDiamond+log.GiftDiamond)
	// r.SetTrack("bindyuanbaoleft", 0)
	// r.SetTrack("familyid", )
	//r.SetTrack("mateid", )
	// r.SetTrack("petid", )
	r.SetTrack("guildid", strconv.FormatUint(userSnapshot.GuildId, 10))
	mirageIds := make(map[uint32]uint32)
	err = helper.Json.Unmarshal([]byte(userSnapshot.MirageIds), &mirageIds)
	if err == nil {
		l4g.Errorf("[UserSnapshotMessage] userSnapshot.MirageIds json unmarshal error: %v", err)
	}
	if len(mirageIds) > 0 {
		mirageSnapshot := &sub.MirageSnapshot{
			Id: mirageIds,
		}
		r.SetTrack("current_mirage_ids", mirageSnapshot)
	}

	trialLevels := make(map[uint32]uint32)
	err = helper.Json.Unmarshal([]byte(userSnapshot.TrialLevel), &trialLevels)
	if err == nil {
		l4g.Errorf("[UserSnapshotMessage] userSnapshot.TrialLevel json unmarshal error: %v", err)
	}
	if len(trialLevels) > 0 {
		trialSnapshot := &sub.TrialSnapshot{
			Level: trialLevels,
		}
		r.SetTrack("current_trial_level", trialSnapshot)
	}

	r.SetTrack("tower_star_id", userSnapshot.TowerStarId)
	r.SetTrack("wrestle_level", userSnapshot.WrestleLevel)
	r.SetTrack("tower_season_floor", userSnapshot.TowerSeasonFloor)
	r.SetTrack("left_coupon", userSnapshot.LeftCoupon)
	r.SetTrack("total_recharge_coupon", userSnapshot.TotalRechargeCoupon)
	r.SetTrack("medal_level", userSnapshot.MedalLevel)

	if userSnapshot.GuildTalentLevel != "" {
		talentLv := make(map[uint32]uint32)
		guildTalents := strings.Split(userSnapshot.GuildTalentLevel, ",")
		for _, talentStr := range guildTalents {
			talents := strings.Split(talentStr, "_")
			if len(talents) != 2 {
				continue
			}
			job, err := strconv.Atoi(talents[0])
			if err != nil {
				l4g.Errorf("[UserSnapshotMessage] userSnapshot.GuildTalentLevel:%s talent atoi error: %v ", userSnapshot.GuildTalentLevel, err)
				continue
			}
			level, err := strconv.Atoi(talents[1])
			if level == 0 {
				l4g.Errorf("[UserSnapshotMessage] userSnapshot.GuildTalentLevel:%s talent atoi error: %v ", userSnapshot.GuildTalentLevel, err)
				continue
			}
			talentLv[uint32(job)] = uint32(level)
		}
		if len(talentLv) > 0 {
			talentSnapshot := &sub.GuildTalentSnapshot{
				Level: talentLv,
			}
			r.SetTrack("current_guild_talent_level", talentSnapshot)
		}
	}

	r.SetTrack("talentenergyleft", userSnapshot.TalentEnergy)
	r.SetTrack("contributionsleft", userSnapshot.Contributions)
	r.SetTrack("celestialbadgeleft", userSnapshot.CelestialBadge)
	r.SetTrack("demonicpetalleft", userSnapshot.Demonicpetal)
	r.SetTrack("oraclecoinleft", userSnapshot.OracleCoin)
	r.SetTrack("soulcrystalleft", userSnapshot.SoulCrystal)
	r.SetTrack("arenapt", userSnapshot.ArenaPt)
	r.SetTrack("highest_main", userSnapshot.HighestMain)
	r.SetTrack("highest_guild", userSnapshot.HighestGuild)
	r.SetTrack("highest_maze", userSnapshot.HighestMaze)
	r.SetTrack("highest_tower", userSnapshot.HighestTower)
	r.SetTrack("daily_activitylev", userSnapshot.DailyActivityLev)
	r.SetTrack("weekly_activitylev", userSnapshot.WeeklyActivityLev)
	r.SetTrack("arena_rank", userSnapshot.ArenaRank)
	// r.SetTrack("arenaid", userSnapshot.Are)
	r.SetTrack("tower_rank", userSnapshot.TowerRank)
	// r.SetTrack("towerid", userSnapshot.Tow)
	r.SetTrack("dispatch_lev", userSnapshot.DispatchLev)
	r.SetTrack("dispatch_lev_task", userSnapshot.DispatchLevTask)
	r.SetTrack("crystal_share_attr", userSnapshot.CrystalShareAttr)
	r.SetTrack("contract_heroes", userSnapshot.ContractHeroes)
	r.SetTrack("resonance_heroes", userSnapshot.ResonanceHeroes)
	r.SetTrack("guild_member_donate", userSnapshot.GuildDonateCnt)
	r.SetTrack("guild_member_activity", userSnapshot.GuildActivity)
	r.SetTrack("guild_member_position", userSnapshot.GuildGrade)
	r.SetTrack("season_dungeon_id", userSnapshot.SeasonDungeonId)

	disorderLPassMaxLevel := make(map[uint32]uint32)
	err = helper.Json.Unmarshal([]byte(userSnapshot.DisorderLPassMaxLevel), &disorderLPassMaxLevel)
	if err == nil {
		l4g.Errorf("[UserSnapshotMessage] userSnapshot.DisorderLPassMaxLevel json unmarshal error: %v", err)
	}
	if len(disorderLPassMaxLevel) > 0 {
		disorderLandSnapshot := &sub.DisorderLandSnapshot{
			MaxLevel: disorderLPassMaxLevel,
		}
		r.SetTrack("disorder_land_pass_max_level", disorderLandSnapshot)
	}

	var runeInfo *sub.UserMonumentRuneSnapshot
	err = helper.Json.Unmarshal([]byte(userSnapshot.RuneInfo), &runeInfo)
	if err == nil {
		l4g.Errorf("[UserSnapshotMessage] userSnapshot.RuneInfo json unmarshal error: %v", err)
	}
	r.SetTrack("rune_info_snapshot", runeInfo)

	r.SetTrack("base_id", userSnapshot.BaseId)

	seasonLinkActive := make(map[uint32][]uint32)
	err = helper.Json.Unmarshal([]byte(userSnapshot.SeasonLinkActive), &seasonLinkActive)
	if err == nil {
		l4g.Errorf("[UserSnapshotMessage] userSnapshot.SeasonLinkActive json unmarshal error: %v", err)
	}
	if len(seasonLinkActive) > 0 {
		actives := make([]*sub.SeasonLinkActive, 0, len(seasonLinkActive))
		for linkId, heroIds := range seasonLinkActive {
			actives = append(actives, &sub.SeasonLinkActive{
				LinkId:  linkId,
				HeroIds: heroIds,
			})
		}
		r.SetTrack("current_season_link_actives", actives)
	}

	var gstResources []*sub.TokenResourceStr
	err = helper.Json.Unmarshal([]byte(userSnapshot.GuildSandTableResource), &gstResources)
	if err == nil {
		l4g.Errorf("[UserSnapshotMessage] userSnapshot.GuildSandTableResource json unmarshal error: %v", err)
	}
	r.SetTrack("gst_resource", gstResources)

	var seasonArena *sub.LogSeasonArena
	err = helper.Json.Unmarshal([]byte(userSnapshot.SeasonArenaInfo), &seasonArena)
	if err == nil {
		l4g.Errorf("[UserSnapshotMessage] userSnapshot.SeasonArenaInfo json unmarshal error: %v", err)
	}
	r.SetTrack("season_arena", seasonArena)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FighterSnapshotMessage struct {
	helper.BaseMessage
}

func (m *FighterSnapshotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	if log.Param2 != "" {
		var heroSnapshot *sub.HeroSnapshot
		err := helper.Json.Unmarshal([]byte(log.Param2), &heroSnapshot)
		if err != nil {
			l4g.Error("[FighterSnapshotMessage] json unmarshal error: %v", err)
			return false
		}
		r.SetTrack("hero_snapshot", heroSnapshot)
	}
	if log.Param3 != "" {
		var equipSnapshot []*sub.EquipSnapshot
		err := helper.Json.Unmarshal([]byte(log.Param3), &equipSnapshot)
		if err != nil {
			l4g.Error("[FighterSnapshotMessage] json unmarshal error: %v", err)
			return false
		}
		r.SetTrack("equip_snapshot", equipSnapshot)
	}
	if log.Param4 != "" {
		var skillSnapshot []*sub.HeroSkillSnapshot
		err := helper.Json.Unmarshal([]byte(log.Param4), &skillSnapshot)
		if err != nil {
			l4g.Error("[FighterSnapshotMessage] json unmarshal error: %v", err)
			return false
		}
		r.SetTrack("skill_snapshot", skillSnapshot)
	}
	if log.Param5 != "" {
		var emblemSnapshot []*sub.EmblemSnapshot
		err := helper.Json.Unmarshal([]byte(log.Param5), &emblemSnapshot)
		if err != nil {
			l4g.Error("[FighterSnapshotMessage] json unmarshal error: %v", err)
			return false
		}
		r.SetTrack("emblem_snapshot", emblemSnapshot)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type ChardataBagSnapshotMessage struct {
	helper.BaseMessage
}

func (m *ChardataBagSnapshotMessage) Execute(log *da.Log) bool {
	var bagSnapshot *sub.BagSnapshotForWm
	err := helper.Json.Unmarshal([]byte(log.Param1), &bagSnapshot)
	if err != nil {
		l4g.Error("[ChardataBagSnapshotMessage] json unmarshal error: %v", err)
		return false
	}

	for _, item := range bagSnapshot.Items {
		m.handle(log, item)
	}

	for _, charDataBag := range bagSnapshot.Fragments {
		m.handle(log, charDataBag)
	}

	for _, charDataBag := range bagSnapshot.ArtifactFragments {
		m.handle(log, charDataBag)
	}

	for _, charDataBag := range bagSnapshot.Emblem {
		m.handle(log, charDataBag)
	}
	return true
}

func (m *ChardataBagSnapshotMessage) handle(log *da.Log, charDataBag *sub.CharDataBag) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("itemtype", charDataBag.Type)
	r.SetTrack("itemid", charDataBag.Id)
	r.SetTrack("itemcount", charDataBag.Count)
	r.SetTrack("itemlev", charDataBag.Lev)
	r.SetTrack("itemraity", charDataBag.Raity)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type ArtifactSnapshotMessage struct {
	helper.BaseMessage
}

func (m *ArtifactSnapshotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var artifactSnapshot *sub.ArtifactSnapshotForWm
	err := helper.Json.Unmarshal([]byte(log.Param1), &artifactSnapshot)
	if err != nil {
		l4g.Error("[ArtifactSnapshotMessage] json unmarshal error: %v", err)
		return false
	}

	r.SetTrack("artifactid", artifactSnapshot.GetId())
	r.SetTrack("score", artifactSnapshot.GetScore())
	r.SetTrack("artifact_lev", artifactSnapshot.GetLevel())
	r.SetTrack("artifact_stage", artifactSnapshot.GetStage())
	r.SetTrack("artifact_star", artifactSnapshot.GetStar())

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildSnapshotMessage struct {
	helper.BaseMessage
}

func (m *GuildSnapshotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var guildSnapshot *sub.GuildSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &guildSnapshot)
	if err != nil {
		l4g.Error("[GuildSnapshotMessage] json unmarshal error: %v", err)
		return false
	}
	r.SetTrack("guild_create_time", log.CreateTime)
	r.SetTrack("guild_name", guildSnapshot.GetName())
	r.SetTrack("guild_id", strconv.FormatUint(guildSnapshot.GetId(), 10))
	r.SetTrack("guild_level", guildSnapshot.GetLevel())
	r.SetTrack("guild_activity_count", guildSnapshot.GetActivityPoints())
	r.SetTrack("guild_member_number", guildSnapshot.GetMemberCnt())
	r.SetTrack("guild_dungeon_chapter", guildSnapshot.GetChapter())

	// r.SetTrack("guild_dungeon_boss_id", util.Uint32SliceToString(guildSnapshot.BossIds, ","))
	// bossHpS := make([]string, 0, len(guildSnapshot.BossHpPct))
	// for _, bossHpPct := range guildSnapshot.BossHpPct {
	// bossHpS = append(bossHpS, fmt.Sprintf("%.2f%%", float32(bossHpPct)/100))
	// }
	// r.SetTrack("guild_dungeon_boss_hp", strings.Join(bossHpS, ","))

	if len(guildSnapshot.BossIds) == len(guildSnapshot.BossHpPct) {
		dungeonBoss := &sub.GuildDungeonBossSnapshot{
			BossHp: make(map[uint32]string),
		}
		for i, id := range guildSnapshot.BossIds {
			hpPct := guildSnapshot.BossHpPct[i]
			dungeonBoss.BossHp[id] = fmt.Sprintf("%.2f%%", float32(hpPct)/100)
		}
		if len(dungeonBoss.BossHp) > 0 {
			r.SetTrack("guild_dungeon_boss", dungeonBoss)
		}
	}
	r.SetTrack("guild_dungeon_season", guildSnapshot.GetSeasonId())
	r.SetTrack("guild_dungeon_round", guildSnapshot.GetRound())
	r.SetTrack("guild_dungeon_zone", guildSnapshot.GetArena())
	r.SetTrack("guild_dungeon_grade", guildSnapshot.GetDivision())
	r.SetTrack("guild_dungeon_score", guildSnapshot.GetStar())
	r.SetTrack("guild_leader_id", strconv.FormatUint(guildSnapshot.LeaderId, 10))
	r.SetTrack("guild_leader_name", guildSnapshot.GetLeaderName())
	r.SetTrack("guild_donate_point", guildSnapshot.GetDonatePoint())
	r.SetTrack("guild_member_list", util.Uint64SliceToString(guildSnapshot.MemberIds, ","))
	r.SetTrack("guild_language", guildSnapshot.GetLanguage())
	r.SetTrack("guild_join_type", guildSnapshot.GetJoinType())
	r.SetTrack("guild_tag", guildSnapshot.GetLabel())
	r.SetTrack("guild_level_limit", guildSnapshot.GetLevelLimit())
	r.SetTrack("guild_power_limit", guildSnapshot.GetPowerLimit())
	r.SetTrack("guild_badge_scence", guildSnapshot.GetBadgeBackground())
	r.SetTrack("guild_badge_picture", guildSnapshot.GetBadgeIcon())
	r.SetTrack("guild_notice", guildSnapshot.GetNotice())
	r.SetTrack("guild_declaration", guildSnapshot.GetDeclaration())
	r.SetTrack("guild_settlement_division", guildSnapshot.GetSettlementDivision())
	r.SetTrack("guild_medal_task", guildSnapshot.Medals)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type WorldBossSnapshotMessage struct {
	helper.BaseMessage
}

func (m *WorldBossSnapshotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var snapshot *sub.WorldBossRankSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &snapshot)
	if err != nil {
		l4g.Error("[WorldBossSnapshotMessage] json unmarshal error: %v", err)
		return false
	}
	r.SetTrack("zone", snapshot.Arena)
	r.SetTrack("rank_type", snapshot.RankType)
	r.SetTrack("rank", snapshot.Rank)
	r.SetTrack("damage", strconv.FormatUint(snapshot.Damage, 10))
	r.SetTrack("term", snapshot.Term)
	r.SetTrack("mode", snapshot.Mode)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type PeakRankSnapshotMessage struct {
	helper.BaseMessage
}

func (m *PeakRankSnapshotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var snapshot *sub.PeakRankSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &snapshot)
	if err != nil {
		l4g.Error("[PeakRankSnapshotMessage] json unmarshal error: %v", err)
		return false
	}

	r.SetTrack("zone", snapshot.Arena)
	r.SetTrack("rank", snapshot.Phase)
	r.SetTrack("phase", snapshot.Phase)
	r.SetTrack("score", snapshot.Score)
	r.SetTrack("current_rank", snapshot.CurrentRank)
	r.SetTrack("current_score", snapshot.CurrentScore)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GstSnapshotMessage struct {
	helper.BaseMessage
}

func (m *GstSnapshotMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var gstGuildSnapshot *sub.GstGuildSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &gstGuildSnapshot)
	if err != nil {
		l4g.Error("[GstSnapshotMessage] json unmarshal error: %v", err)
		return false
	}

	r.SetTrack("guild_name", gstGuildSnapshot.GetName())
	r.SetTrack("guild_id", strconv.FormatUint(gstGuildSnapshot.GetId(), 10))
	r.SetTrack("guild_sandtable_zone", gstGuildSnapshot.GetArena())
	r.SetTrack("guild_sandtable_round", gstGuildSnapshot.GetRound())
	r.SetTrack("guild_sandtable_turn", gstGuildSnapshot.GetLRound())
	r.SetTrack("guild_sandtable_room", gstGuildSnapshot.GetRoom())
	r.SetTrack("guild_sandtable_score", gstGuildSnapshot.GetScore())
	r.SetTrack("map_id", gstGuildSnapshot.GetMapId())
	r.SetTrack("guild_sandtable_point", gstGuildSnapshot.GetDivisionScore())
	r.SetTrack("guild_space_count", gstGuildSnapshot.GetGroundNum())
	r.SetTrack("guild_rank", gstGuildSnapshot.GetGuildRank())
	r.SetTrack("bless_times", gstGuildSnapshot.BlessInfo)
	r.SetTrack("space_ids", gstGuildSnapshot.GetGroundInfo())
	r.SetTrack("guild_user_score", gstGuildSnapshot.GetUsersScore())
	r.SetTrack("guild_build_level", gstGuildSnapshot.GetBuild())
	r.SetTrack("guild_sandtable_arena_round", gstGuildSnapshot.GetArenaRound())
	r.SetTrack("guild_sandtable_arena_rank", gstGuildSnapshot.GetArenaRank())
	r.SetTrack("guild_sandtable_arena_wins", gstGuildSnapshot.GetArenaWins())
	r.SetTrack("guild_sandtable_arena_userinfo", gstGuildSnapshot.GetAreaRanks())
	r.SetTrack("guild_space_taken", gstGuildSnapshot.GetFirstOccupy())
	r.SetTrack("guild_sandtable_grade", gstGuildSnapshot.GetGstDivision())

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
