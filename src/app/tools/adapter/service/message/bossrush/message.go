package bossrush

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"encoding/json"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_BOSS_RUSH_FIGHT),
		&BossRushFightMessage{}, "boss_rush_fight", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_BOSS_RUSH_TASK_AWARD),
		&BossRushTaskAwardMessage{}, "boss_rush_task_award", 15, 0, 0, 0)
}

// Boss挑战-战斗
type BossRushFightMessage struct {
	helper.BaseMessage
}

func (m *BossRushFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var battleData *sub.LogBossRushFight
	err := json.Unmarshal([]byte(log.Param2), &battleData)
	if err != nil {
		l4g.Errorf("[BossRushFightMessage] json unmarshal error: %s", err.Error())
		return false
	}
	for i := uint32(0); i < battleData.TeamNum; i++ {
		r.SetFormationMessageByIndex(log.Param1, i)
	}

	var battleType uint32
	if !battleData.IsSweep {
		battleType = uint32(1)
	} else {
		battleType = uint32(2)
	}
	r.SetTrack("battle_op_type", battleType)
	r.SetTrack("season", log.Param10)
	r.SetTrack("boss_id", battleData.BossId)
	r.SetTrack("battle_report_id", battleData.BattleReportId)
	r.SetTrack("is_kill", battleData.KillBoss)
	if len(battleData.TeamDamages) > 0 {
		r.SetTrack("damage_1", battleData.TeamDamages[0])
	}
	if len(battleData.TeamDamages) > 1 {
		r.SetTrack("damage_2", battleData.TeamDamages[1])
	}
	if len(battleData.TeamDamages) > 2 {
		r.SetTrack("damage_3", battleData.TeamDamages[2])
	}
	if len(battleData.TeamProgress) > 0 {
		r.SetTrack("progress_1", battleData.TeamProgress[0])
	}
	if len(battleData.TeamProgress) > 1 {
		r.SetTrack("progress_2", battleData.TeamProgress[1])
	}
	if len(battleData.TeamProgress) > 2 {
		r.SetTrack("progress_3", battleData.TeamProgress[2])
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type BossRushTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *BossRushTaskAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	if log.Param1 != "" {
		taskIds := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("task_id", taskIds)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
