package seasoncompliance

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_COMPLIANCE_SCORE_CHANGE),
		&SeasonComplianceScoreChangeMessage{}, "season_compliance_score_update", 13, 0, 0, 0)
}

type SeasonComplianceScoreChangeMessage struct {
	helper.BaseMessage
}

func (m *SeasonComplianceScoreChangeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("phase", log.Param10)       //期数
	r.SetTrack("stage_index", log.Param11) //阶段下标
	r.SetTrack("score_type", log.Param12)  //积分类型
	r.SetTrack("add_sore", log.Param13)    //加的分数
	r.SetTrack("stage_score", log.Param14) //阶段总分

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
