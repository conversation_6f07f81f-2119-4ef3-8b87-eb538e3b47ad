package roundactivity

import (
	"strconv"

	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ROUND_ACTIVITY_RECV_TASK_AWARD),
		&RoundActivityRecvTaskAwardMessage{}, "round_activity_recv_task_award", 13, 0, 0, 0)
}

type RoundActivityRecvTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *RoundActivityRecvTaskAwardMessage) Execute(log *da.Log) bool {
	var taskIDs []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &taskIDs)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	idList := make([]string, 0, len(taskIDs))
	for _, id := range taskIDs {
		idList = append(idList, strconv.FormatUint(id, 10))
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("round_activity_from", log.Param10) //领奖操作来源 0-玩家领奖 1-系统补发
	r.SetTrack("round_activity_category", log.Param11)
	r.SetTrack("round_activity_stage", log.Param12)
	r.SetTrack("round_activity_task_ids", idList)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
