package guilddungeon

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"fmt"
	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_AWARD_RECEIVE),
		&GuildDungeonReceiveAwardMessage{}, "guild_dungeon_receive_award", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_FIGHT),
		&GuildDungeonFightMessage{}, "guild_dungeon_fight", 20, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_RECV_CHAPTER_TASK_AWARD),
		&GuildDungeonRecvChapterTaskAwardMessage{}, "guild_dungeon_recv_chapter_task_award", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_RECV_BOSS_BOX_AWARD),
		&GuildDungeonRecvBossBoxAwardMessage{}, "guild_dungeon_recv_boss_box_award", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_BUY_CHALLENGE_TIMES),
		&GuildDungeonBuyChallengeTimesMessage{}, "guild_dungeon_buy_challenge_times", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_CHAPTER_RANK_LIKE),
		&GuildDungeonChapterRankLikeMessage{}, "guild_dungeon_chapter_rank_like", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_RECV_SEASON_TOP_DIVISION_AWARD),
		&GuildDungeonRecvSeasonTopDivisonMessage{}, "guild_dungeon_recv_season_top_division_award", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_NEW_FIGHT),
		&GuildDungeonNewFightMessage{}, "guild_dungeon_new_fight", 20, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DUNGEON_USE_STRATEGY),
		&GuildDungeonUseStrategyMessage{}, "guild_strategy_use", 17, 0, 0, 0)
}

// 公会副本-领取章节进度奖励
type GuildDungeonReceiveAwardMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonReceiveAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_dungeon_req_type", log.Param10)    // 客户端请求领取的类型
	r.SetTrack("guild_dungeon_box", log.Param11)         // 客户端请求的箱子
	r.SetTrack("guild_dungeon_old_chapter", log.Param12) // 领奖之前玩家所处章节
	r.SetTrack("guild_dungeon_new_chapter", log.Param13) // 领奖之后玩家所处章节
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会副本-战斗
type GuildDungeonFightMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_dungeon_chapter", log.Param10)                                        // 章节
	r.SetTrack("guild_dungeon_monster", log.Param11)                                        // 怪物id
	r.SetTrack("guild_dungeon_old_daily_damage_str", strconv.FormatUint(log.Param13, 10))   // 战斗前玩家今日总伤害
	r.SetTrack("guild_dungeon_new_daily_damage_str", strconv.FormatUint(log.Param14, 10))   // 战斗后玩家今日总伤害
	r.SetTrack("guild_dungeon_old_chapter_damage_str", strconv.FormatUint(log.Param15, 10)) // 战斗前玩家章节总伤害
	r.SetTrack("guild_dungeon_new_chapter_damage_str", strconv.FormatUint(log.Param16, 10)) // 战斗后玩家章节总伤害
	r.SetTrack("guild_dungeon_old_chapter", log.Param17)                                    // 战斗前玩家所处章节
	r.SetTrack("guild_dungeon_new_chapter", log.Param18)                                    // 战斗后玩家所处章节
	r.SetTrack("guild_dungeon_round", log.Param19)                                          // 轮次
	r.SetTrack("guild_old_level", log.Param5)                                               //老等级
	r.SetTrack("guild_old_exp", log.Param6)                                                 //老经验
	r.SetTrack("guild_new_level", log.Param1)                                               //新等级
	r.SetTrack("guild_new_exp", log.Param2)                                                 //新经验
	r.SetTrack("guild_user_activity_point", log.Param4)                                     // 公会用户活跃值
	r.SetTrack("guild_id", log.Param7)                                                      // 公会ID
	r.SetFormationMessage(log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会副本领取章节任务奖励
type GuildDungeonRecvChapterTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonRecvChapterTaskAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	ids := helper.Int32SliceToStringSlice(log.Param1)
	r.SetTrack("guild_dungeon_recv_task_ids", ids) //
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会副本领取boss宝箱奖励
type GuildDungeonRecvBossBoxAwardMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonRecvBossBoxAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_dungeon_round", log.Param10) //
	r.SetTrack("guild_dungeon_chapter", log.Param11)
	r.SetTrack("guild_dungeon_box", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会副本购买挑战次数
type GuildDungeonBuyChallengeTimesMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonBuyChallengeTimesMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_dungeon_old_challenge_times", log.Param10) //
	r.SetTrack("guild_dungeon_old_buy_count", log.Param11)
	r.SetTrack("guild_dungeon_new_buy_count", log.Param12)
	r.SetTrack("guild_dungeon_new_challenge_times", log.Param13)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildDungeonChapterRankLikeMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonChapterRankLikeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param10, 10)) // 公会id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildDungeonRecvSeasonTopDivisonMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonRecvSeasonTopDivisonMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	ids := helper.Int32SliceToStringSlice(log.Param1)
	r.SetTrack("guild_dungeon_recv_top_season_division", ids) //
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildDungeonNewFightMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonNewFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("season", log.Param11)                             //赛季
	r.SetTrack("round", log.Param12)                              //轮次
	r.SetTrack("battlezone", log.Param13)                         //战区
	r.SetTrack("chapter", log.Param14)                            //章节
	r.SetTrack("bossid", log.Param15)                             //bossID
	r.SetTrack("damage_str", strconv.FormatUint(log.Param16, 10)) //伤害
	r.SetTrack("guild_id", strconv.FormatUint(log.Param17, 10))   //公会ID
	r.SetTrack("is_change_process", log.Param18 == 1)             //是否可改变当前进度
	r.SetTrack("report_id", log.Param1)                           //战报ID
	r.SetFormationMessage(log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildDungeonUseStrategyMessage struct {
	helper.BaseMessage
}

func (m *GuildDungeonUseStrategyMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id_str", fmt.Sprintf("%d", log.Param10)) // 使用秘技的公会ID
	r.SetTrack("strategy_id", log.Param11)                     // 秘技ID
	r.SetTrack("guild_op_id", fmt.Sprintf("%d", log.Param12))  // 被使用秘技的公会ID
	r.SetTrack("guild_op_chapter", log.Param13)                // 被使用秘技公会章节
	r.SetTrack("strategy_use_count", log.Param14)              // 使用次数
	r.SetTrack("strategy_type", log.Param15)                   // 秘技类型
	r.SetTrack("guild_grade", log.Param16)                     // 公会段位
	r.SetTrack("guild_op_grade", log.Param17)                  // 目标公会段位
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
