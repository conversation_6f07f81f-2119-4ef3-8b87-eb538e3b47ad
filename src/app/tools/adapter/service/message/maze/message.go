package maze

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_GET_MAP),
		&MazeGetMapMessage{}, "maze_get_map", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_TRIGGER_EVENT),
		&MazeTriggerEventMessage{}, "maze_trigger_event", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_RECOVERY_HERO),
		&MazeRecoveryHeroMessage{}, "maze_recovery_hero", 9, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_GET_GRID),
		&MazeGetGridMessage{}, "maze_get_grid", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_BUY_REVIVE),
		&MazeBuyReviveMessage{}, "maze_buy_revive", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_USE_ITEM),
		&MazeUseItemMessage{}, "maze_use_item", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_TASK_RECEIVE_AWARD),
		&MazeTaskReceiveAwardMessage{}, "maze_task_receive_award", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_FIGHT),
		&MazeFightMessage{}, "maze_fight", 16, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_SELECT_BUFF),
		&MazeSelectBuffMessage{}, "maze_select_buff", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MAZE_SWEEP),
		&MazeSweepMessage{}, "maze_sweep", 10, 0, 0, 0)
}

// 玩家第一次进入迷宫初始化的数据
type MazeGetMapMessage struct {
	helper.BaseMessage
}

func (m *MazeGetMapMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	//r.SetTrack("maze_player", log.Param1) // 玩家在迷宫缓存的数据 - 废弃
	r.SetTrack("maze_map_id", log.Param10)                                     // 地图id
	r.SetTrack("maze_cur_grid_id", log.Param11)                                // 当前格子id
	r.SetTrack("maze_user_max_power_str", strconv.FormatUint(log.Param12, 10)) // 玩家历史最高战力
	r.SetTrack("maze_map_level", log.Param13)                                  // 地图level
	// 待数数2021-12月份支持复杂数据结构再开启
	//r.SetTrack("map_grid_info", log.Param1) // 地图格子信息
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 触发事件
type MazeTriggerEventMessage struct {
	helper.BaseMessage
}

func (m *MazeTriggerEventMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	//r.SetTrack("maze_hero_ids", log.Param1) - 废弃
	if log.Param1 != "" {
		hidsArray := helper.Int64SliceToStringSlice(log.Param1)
		r.SetTrack("maze_hero_id", hidsArray) // 诅咒地块，前端传过来的血量最高的N个英雄
	}
	// 灵魂祭坛事件时param为选中的buff id
	// 选择事件时param为选中的answer id
	// 生命之泉选择治愈或复活 param = 1:复活，param = 2:治愈
	r.SetTrack("maze_param", log.Param10)
	r.SetTrack("maze_event_type", log.Param11)        // 事件类型
	r.SetTrack("win", helper.FormatBool(log.Param12)) // 战斗结果
	r.SetTrack("maze_grid_id", log.Param13)           // 地图格子id
	r.SetTrack("maze_map_level", log.Param14)         // 地图等级
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 迷宫复活英雄
type MazeRecoveryHeroMessage struct {
	helper.BaseMessage
}

func (m *MazeRecoveryHeroMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("maze_map_level", log.Param10) // 地图等级

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 获得迷宫格子信息
type MazeGetGridMessage struct {
	helper.BaseMessage
}

func (m *MazeGetGridMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	//r.SetTrack("maze_grid", log.Param1)        	// 迷宫格子信息 - 废弃
	r.SetTrack("maze_event_type", log.Param10) // 事件类型
	r.SetTrack("maze_grid_id", log.Param11)    // 地图格子id
	if log.Param12 != 0 {
		r.SetTrack("maze_question", log.Param12) // 选择事件的问题
	}
	if log.Param13 != 0 {
		r.SetTrack("maze_enemy_str", strconv.FormatUint(log.Param13, 10)) // 匹配的敌人
	}
	if log.Param14 != 0 {
		r.SetTrack("maze_box_id", log.Param14) // 魔盒id
	}
	if log.Param1 != "" {
		buffsArray := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("maze_soul_buffs", buffsArray) // 灵魂祭坛事件的buffs
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 迷宫-购买复生神像
type MazeBuyReviveMessage struct {
	helper.BaseMessage
}

func (m *MazeBuyReviveMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("maze_buy_revive_count", log.Param10) // 购买复生神像次数
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 迷宫-使用道具
type MazeUseItemMessage struct {
	helper.BaseMessage
}

func (m *MazeUseItemMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("maze_use_item_id", log.Param10)    // 使用道具id
	r.SetTrack("maze_use_item_count", log.Param11) // 使用道具数量
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 迷宫-领取任务奖励
type MazeTaskReceiveAwardMessage struct {
	helper.BaseMessage
}

func (m *MazeTaskReceiveAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	//r.SetTrack("maze_task_id", log.Param10)    // 任务id
	r.SetTrack("maze_task_level", log.Param11) // 成绩level
	r.SetTrack("maze_task_score", log.Param12) // 成绩score
	if log.Param1 != "" {
		buffsArray := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("maze_task_ids", buffsArray) // 任务id
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 迷宫-战斗
type MazeFightMessage struct {
	helper.BaseMessage
}

func (m *MazeFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("maze_map_id", log.Param10)            // 地图id
	r.SetTrack("maze_event_type", log.Param11)        // 事件类型
	r.SetTrack("win", helper.FormatBool(log.Param12)) // 是否胜利
	r.SetTrack("maze_map_level", log.Param13)         // 地图等级
	r.SetFormationMessage(log.Param2)
	r.SetTrack("report_id", log.Param3) // 战报 id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 迷宫-选择buff
type MazeSelectBuffMessage struct {
	helper.BaseMessage
}

func (m *MazeSelectBuffMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("maze_grid_id", log.Param10)    // grid id
	r.SetTrack("maze_event_type", log.Param11) // 事件type
	r.SetTrack("maze_buff_id", log.Param12)    // buff id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 迷宫-扫荡
type MazeSweepMessage struct {
	helper.BaseMessage
}

func (m *MazeSweepMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("maze_level", log.Param10) // level
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
