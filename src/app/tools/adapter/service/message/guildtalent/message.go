package guildtalent

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_TALENT_RESET),
		&GuildTalentResetMessage{}, "guild_talent_reset", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_TALENT_LEVEL_UP),
		&GuildTalentLevelUpMessage{}, "guild_talent_level_up", 12, 0, 0, 0)
}

// 公会天赋重置
type GuildTalentResetMessage struct {
	helper.BaseMessage
}

func (m *GuildTalentResetMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	// 废弃
	//r.SetTrack("hero_job", log.Param10)               // 英雄职业
	//r.SetTrack("guild_talent_old_level", log.Param11) // 重置前 level
	//r.SetTrack("guild_talent_new_level", log.Param12) // 重置后 level
	r.SetTrack("guild_talent_old_talent", log.Param1) // 重置前 talent
	r.SetTrack("guild_talent_new_talent", log.Param2) // 重置后 talent
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会天赋升级
type GuildTalentLevelUpMessage struct {
	helper.BaseMessage
}

func (m *GuildTalentLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("hero_job", log.Param10)               // 英雄职业
	r.SetTrack("guild_talent_old_level", log.Param11) // 升级前 level
	r.SetTrack("guild_talent_new_level", log.Param12) // 升级后 level
	r.SetTrack("guild_talent_score", log.Param13)     // score
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
