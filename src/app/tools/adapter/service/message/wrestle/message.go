package wrestle

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WRESTLE_FIGHT),
		&WrestleFightMessage{}, "wrestle_fight", 18, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WRESTLE_BE_FIGHT),
		&WrestleBeFightMessage{}, "wrestle_be_fight", 17, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WRESTLE_LIKE),
		&WrestleLikeMessage{}, "wrestle_like", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WRESTLE_RECV_LEVEL_AWARD),
		&WrestleRecvLevelAwardMessage{}, "wrestle_recv_level_award", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WRESTLE_CHANGE_ROOM),
		&WrestleChangeRoomMessage{}, "wrestle_change_room", 10, 0, 0, 0)
}

type WrestleFightMessage struct {
	helper.BaseMessage
}

func (m *WrestleFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("wrestle_op_uid", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("wrestle_level", log.Param11)
	r.SetTrack("win", helper.FormatBool(log.Param12))
	r.SetTrack("wrestle_op_sid", strconv.FormatUint(log.Param13, 10))
	r.SetTrack("wrestle_self_change", log.Param1)
	r.SetTrack("wrestle_op_change", log.Param2)
	r.SetTrack("wrestle_promoted", helper.FormatBool(log.Param14))
	r.SetFormationMessage(log.Param3)
	r.SetTrack("report_id", log.Param4)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type WrestleBeFightMessage struct {
	helper.BaseMessage
}

func (m *WrestleBeFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("wrestle_op_uid", strconv.FormatUint(uint64(log.Param10), 10))
	r.SetTrack("wrestle_rank", log.Param11)
	r.SetTrack("win", helper.FormatBool(log.Param12))
	r.SetTrack("wrestle_old_rank", log.Param13)
	r.SetFormationMessage(log.Param1)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type WrestleLikeMessage struct {
	helper.BaseMessage
}

func (m *WrestleLikeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("wrestle_like_uid", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("wrestle_like_rank", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type WrestleRecvLevelAwardMessage struct {
	helper.BaseMessage
}

func (m *WrestleRecvLevelAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("wrestle_award_ids", log.Param1)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type WrestleChangeRoomMessage struct {
	helper.BaseMessage
}

func (m *WrestleChangeRoomMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("wrestle_next_change_room_tm", log.Param10)
	r.SetTrack("wrestle_change_room_success", helper.FormatBool(log.Param11))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
