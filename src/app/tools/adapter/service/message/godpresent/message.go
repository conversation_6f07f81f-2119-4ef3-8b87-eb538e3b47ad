package godpresent

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GOD_PRESENT_RECV_ITEM),
		&GodPresentRecvItemMessage{}, "god_present_recv_item", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GOD_PRESENT_SUMMON),
		&GodPresentSummonMessage{}, "god_present_summon", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GOD_PRESENT_RECV_AWARDS),
		&GodPresentRecvAwardMessage{}, "god_present_recv_awards", 13, 0, 0, 0)
}

type GodPresentRecvItemMessage struct {
	helper.BaseMessage
}

func (m *GodPresentRecvItemMessage) Execute(log *da.Log) bool {
	var groups []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &groups)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	groupsArray := make([]string, 0, len(groups))
	for _, id := range groups {
		groupsArray = append(groupsArray, strconv.FormatUint(uint64(id), 10))
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("god_present_id", log.Param10)
	r.SetTrack("god_present_group_order", groupsArray)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GodPresentSummonMessage struct {
	helper.BaseMessage
}

func (m *GodPresentSummonMessage) Execute(log *da.Log) bool {
	var hids []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &hids)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	hidsArray := make([]string, 0, len(hids))
	for _, id := range hids {
		hidsArray = append(hidsArray, strconv.FormatUint(uint64(id), 10))
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("god_present_id", log.Param10)
	r.SetTrack("god_present_summon_count", log.Param11)
	r.SetTrack("god_present_summon_hero_sys_ids", hidsArray)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GodPresentRecvAwardMessage struct {
	helper.BaseMessage
}

func (m *GodPresentRecvAwardMessage) Execute(log *da.Log) bool {
	var hids []uint64
	err := helper.Json.Unmarshal([]byte(log.Param1), &hids)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	hidsArray := make([]string, 0, len(hids))
	for _, id := range hids {
		hidsArray = append(hidsArray, strconv.FormatUint(uint64(id), 10))
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("god_present_id", log.Param10)
	r.SetTrack("god_present_group_key", log.Param11)
	r.SetTrack("god_present_summon_hero_sys_ids", hidsArray)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
