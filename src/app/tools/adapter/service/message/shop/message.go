package shop

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"

	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SHOP_BUY),
		&ShopBuyMessage{}, "shop_buy", 17, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SHOP_REFRESH),
		&ShopRefreshMessage{}, "shop_refresh", 11, 0, 0, 0)
}

type ShopBuyMessage struct {
	helper.BaseMessage
}

func (m *ShopBuyMessage) Execute(log *da.Log) bool {
	if log.Param1 == "" || log.Param2 == "" {
		l4g.Error("[Logger] shop trade data err")
		return false
	}
	var costs, awards []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param1), &costs)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	err = helper.Json.Unmarshal([]byte(log.Param2), &awards)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}

	if len(awards) == 0 {
		l4g.Error("[Logger] no awards")
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("shop_id", log.Param10)
	r.SetTrack("shop_goods_id", log.Param11)
	r.SetTrack("buy_count", log.Param12)
	r.SetTrack("shop_type", log.Param15)
	if len(costs) > 0 {
		r.SetTrack("shopping_cost_type", costs[0].Type)
		r.SetTrack("shopping_cost_value", costs[0].Value)
		r.SetTrack("shopping_cost_count", costs[0].Count)
	}
	r.SetTrack("shopping_award_type", awards[0].Type)
	r.SetTrack("shopping_award_value", awards[0].Value)
	r.SetTrack("shopping_award_count", awards[0].Count)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type ShopRefreshMessage struct {
	helper.BaseMessage
}

func (m *ShopRefreshMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("shop_id", log.Param10)
	r.SetTrack("shop_refresh_type", log.Param11)
	r.SetTrack("is_auto", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
