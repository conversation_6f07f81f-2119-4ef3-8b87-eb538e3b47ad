package seasonreturn

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_RETURN_ADD_AWARDS),
		&SeasonReturnAddAwardsMessage{}, "season_return_add_awards", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_RETURN_TAKE_AWARDS),
		&SeasonReturnTakeAwardsMessage{}, "season_return_take_awards", 12, 0, 0, 0)
}

// 添加奖励
type SeasonReturnAddAwardsMessage struct {
	helper.BaseMessage
}

func (m *SeasonReturnAddAwardsMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("award_id", log.Param10)  //奖励ID
	r.SetTrack("return_id", log.Param11) //回流ID
	r.SetTrack("day_count", log.Param12) //回流天数
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 领取奖励
type SeasonReturnTakeAwardsMessage struct {
	helper.BaseMessage
}

func (m *SeasonReturnTakeAwardsMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("award_id", log.Param10)  //奖励ID
	r.SetTrack("return_id", log.Param11) //回流ID
	r.SetTrack("day_count", log.Param12) //回流天数
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
