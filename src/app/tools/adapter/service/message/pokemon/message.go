package pokemon

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"
	"strconv"

	"gitlab.qdream.com/kit/library/log/l4g"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_POKEMON_STAR_UP),
		&PokemonStarUpMessage{}, "pokemon_star_up", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_POKEMON_POTENTIAL_LEVEL_UP),
		&PokemonPotentialLevelUpMessage{}, "pokemon_potential_level_up", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_POKEMON_MASTER_REWARD),
		&PokemonMasterRewardMessage{}, "pokemon_master_reward", 15, 0, 0, 0)
}

type PokemonStarUpMessage struct {
	helper.BaseMessage
}

func (m *PokemonStarUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("pokemon_id", log.Param10)
	r.SetTrack("pokemon_star_before", log.Param11)
	r.SetTrack("pokemon_star_after", log.Param12)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type PokemonPotentialLevelUpMessage struct {
	helper.BaseMessage
}

func (m *PokemonPotentialLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("pokemon_potential_before", log.Param11)
	r.SetTrack("pokemon_potential_after", log.Param12)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type PokemonMasterRewardMessage struct {
	helper.BaseMessage
}

func (m *PokemonMasterRewardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	masterRewards := GenNewResource(log.Param1)
	if len(masterRewards) > 0 {
		r.SetTrack("pokemon_master_rewards", masterRewards)
	}

	if log.Param2 != "" {
		masterLevels := helper.Int32SliceToStringSlice(log.Param2)
		r.SetTrack("pokemon_master_reward_levels", masterLevels)
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

func GenNewResource(resourceStr string) []*sub.ResourceStr {
	var resources []*cl.Resource
	err := helper.Json.Unmarshal([]byte(resourceStr), &resources)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil
	}
	size := len(resources)
	if size == 0 {
		l4g.Error("[Logger] resource nil: %+v", resourceStr)
		return nil
	}

	//类型转换
	newResource := make([]*sub.ResourceStr, 0, size)
	for _, v := range resources {
		res := &sub.ResourceStr{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count,
		}
		if v.Id > 0 {
			res.Id = strconv.FormatUint(v.Id, 10)
		}
		if v.TotalCount > 0 {
			res.TotalCount = strconv.FormatUint(v.TotalCount, 10)
		}
		if len(v.Attrs) > 0 {
			res.Attrs = make([]*sub.AttrStr, 0, len(v.Attrs))
			for _, vv := range v.Attrs {
				res.Attrs = append(res.Attrs, &sub.AttrStr{
					Type:  vv.Type,
					Value: strconv.FormatUint(uint64(vv.Value), 10),
				})
			}
		}

		newResource = append(newResource, res)
	}
	return newResource
}
