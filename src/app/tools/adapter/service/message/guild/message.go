package guild

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"encoding/json"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_CREATE),
		&GuildCreateMessage{}, "guild_create", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MANAGER_MEMBER),
		&GuildManagerMemberMessage{}, "guild_manager_member", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MODIFY_INFO),
		&GuildModifyInfoMessage{}, "guild_modify_info", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SET_NAME),
		&GuildSetNameMessage{}, "guild_set_name", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MODIFY_NOTICE),
		&GuildModifyNoticeMessage{}, "guild_modify_notice", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_APPLY_RATIFY),
		&GuildApplyRatifyMessage{}, "guild_apply_ratify", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_USER_APPLY),
		&GuildUserApplyMessage{}, "guild_user_apply", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SIGN_IN),
		&GuildSignInMessage{}, "guild_sign_in", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_QUIT),
		&GuildQuitMessage{}, "guild_quit", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DISBAND),
		&GuildDisbandMessage{}, "guild_disband", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_RECRUIT),
		&GuildRecruitMessage{}, "guild_recruit", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DONATE),
		&GuildDonateMessage{}, "guild_donate", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_DONATE_RECEIVE),
		&GuildDonateReceiveMessage{}, "guild_donate_receive", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MODIFY_TEXT_INFO),
		&GuildTextSetMessage{}, "new_guild_set", 21, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_USER_JOIN_OR_QUIT),
		&GuildUserJoinOrQuitMessage{}, "new_guild_apply", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MANAGE),
		&GuildManageMessage{}, "new_guild_manage", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_POSITION_CHANGE),
		&GuildPositionChangeMessage{}, "new_guild_position", 16, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MOB_ACCEPT_TASK),
		&LogGuildMobAcceptTaskMessage{}, "guild_mobilization_accept_task", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MOB_GET_SCORE),
		&LogGuildMobScoreMessage{}, "guild_mobilization_score", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_MOB_BUY_TIMES),
		&LogGuildMobBuyTimesMessage{}, "guild_mobilization_purchase_num", 12, 0, 0, 0)
}

// 公会-创建
type GuildCreateMessage struct {
	helper.BaseMessage
}

func (m *GuildCreateMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_badge", log.Param10)                          // 公会徽章
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param11, 10)) // 公会id
	r.SetTrack("guild_name", log.Param2)                            // 公会名称
	r.SetAfter("guild_id", strconv.FormatUint(log.Param11, 10))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-管理公会成员
type GuildManagerMemberMessage struct {
	helper.BaseMessage
}

func (m *GuildManagerMemberMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_member_id_str", strconv.FormatUint(log.Param10, 10))     // 公会成员id
	r.SetTrack("guild_op_type", log.Param11)                                   // 操作类型：0-设为会长 1-更改副会长(设为副会长、若已经是副会长则撤销) 2-踢出公会
	r.SetTrack("guild_member_old_grade", log.Param12)                          // 成员旧的职位
	r.SetTrack("guild_member_new_grade", log.Param13)                          // 成员新的职位
	r.SetTrack("guild_member_old_id_str", strconv.FormatUint(log.Param14, 10)) // 成员旧的公会id
	r.SetTrack("guild_member_new_id_str", strconv.FormatUint(log.Param15, 10)) // 成员新的公会id
	ret := r.Write(log)
	m.PutRecorder(r)

	//if log.Param11 == 2 {
	//	r1 := m.GetRecorder()
	//	r1.GetBaseMessage(log)
	//	uidString := strconv.FormatUint(log.UserID, 10)
	//	r1.SetUserID(uidString)
	//	r1.SetAfter("guild_id", log.Param15)
	//	r1.SetAfter("guild_name", "")
	//	r1.Write(log)
	//	m.PutRecorder(r1)
	//}

	return ret
}

// 公会-设置公会基本信息
type GuildModifyInfoMessage struct {
	helper.BaseMessage
}

func (m *GuildModifyInfoMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_new_declaration", log.Param2) // 公会新的宣言
	r.SetTrack("guild_new_badge", log.Param11)      // 公会新的徽章
	isNeedApply := helper.FormatBool(log.Param13)
	r.SetTrack("guild_apply_set", isNeedApply) // 公会的审核设置
	r.SetTrack("guild_join_type", log.Param14)
	r.SetTrack("guild_label", log.Param15)
	r.SetTrack("guild_lv_limit", log.Param16)
	r.SetTrack("guild_power_limit", fmt.Sprintf("%d", log.Param17))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-设置公会名
type GuildSetNameMessage struct {
	helper.BaseMessage
}

func (m *GuildSetNameMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_name", log.Param2) // 公会新的名字
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-修改公会公告
type GuildModifyNoticeMessage struct {
	helper.BaseMessage
}

func (m *GuildModifyNoticeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_notice", log.Param2) // 公会公告
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-管理申请
type GuildApplyRatifyMessage struct {
	helper.BaseMessage
}

func (m *GuildApplyRatifyMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	//r.SetTrack("guild_apply_ids", log.Param1)          // 申请公会id列表 - 废弃
	if log.Param1 != "" {
		ids := helper.Int64SliceToStringSlice(log.Param1)
		r.SetTrack("guild_ratify_apply_ids", ids) // 申请公会id列表
	}
	//r.SetTrack("guild_old_user_apply_ids", log.Param2) // 管理前公会的申请列表 - 废弃
	if log.Param2 != "" {
		oldIDs := helper.Int64SliceToStringSlice(log.Param2)
		r.SetTrack("guild_old_apply_ids", oldIDs) // 管理前公会的申请列表
	}
	//r.SetTrack("guild_new_user_apply_ids", log.Param3) // 管理后公会的申请列表 -废弃
	if log.Param3 != "" {
		newIDs := helper.Int64SliceToStringSlice(log.Param3)
		r.SetTrack("guild_new_apply_ids", newIDs) // 管理后公会的申请列表
	}
	isAccept := helper.FormatBool(log.Param10)
	r.SetTrack("guild_is_accept", isAccept) // 是否同意

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-玩家加入或者申请公会
type GuildUserApplyMessage struct {
	helper.BaseMessage
}

func (m *GuildUserApplyMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_op_type", log.Param10)                        // 操作类型：0-加入公会 1-申请公会 2-取消申请 3-快速加入
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param11, 10)) // 公会id
	//r.SetTrack("guild_old_user_apply_ids", log.Param1) // 管理前公会的申请列表 - 废弃
	if log.Param1 != "" {
		oldIDs := helper.Int64SliceToStringSlice(log.Param1)
		r.SetTrack("guild_old_apply_ids", oldIDs) // 管理前公会的申请列表
	}
	//r.SetTrack("guild_new_user_apply_ids", log.Param2) // 管理后公会的申请列表 - 废弃
	if log.Param2 != "" {
		newIDs := helper.Int64SliceToStringSlice(log.Param2)
		r.SetTrack("guild_new_apply_ids", newIDs) // 管理后公会的申请列表
	}

	if log.Param10 == 0 || log.Param10 == 3 || log.Param10 == 4 {
		r.SetAfter("guild_id", strconv.FormatUint(log.Param11, 10))
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-签到
type GuildSignInMessage struct {
	helper.BaseMessage
}

func (m *GuildSignInMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_old_level", log.Param10)                      // 签到前的公会等级
	r.SetTrack("guild_old_exp", log.Param11)                        // 签到前的公会经验
	r.SetTrack("guild_new_level", log.Param12)                      // 签到后的公会等级
	r.SetTrack("guild_new_exp", log.Param13)                        // 签到后的公会经验
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param14, 10)) // 公会ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-退出公会
type GuildQuitMessage struct {
	helper.BaseMessage
}

func (m *GuildQuitMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param10, 10)) // 公会id
	r.SetAfter("guild_id", "0")
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-解散公会
type GuildDisbandMessage struct {
	helper.BaseMessage
}

func (m *GuildDisbandMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param10, 10)) // 公会id
	r.SetAfter("guild_id", "0")
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-解散公会
type GuildRecruitMessage struct {
	helper.BaseMessage
}

func (m *GuildRecruitMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id_str", strconv.FormatUint(log.Param10, 10)) // 公会id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 公会-公会捐赠
type GuildDonateMessage struct {
	helper.BaseMessage
}

func (m *GuildDonateMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id", strconv.FormatUint(log.Param15, 10)) // 公会id
	r.SetTrack("guild_old_level", strconv.FormatUint(log.Param10, 10))
	r.SetTrack("guild_old_exp", strconv.FormatUint(log.Param11, 10))
	r.SetTrack("guild_new_level", strconv.FormatUint(log.Param12, 10))
	r.SetTrack("guild_new_exp", strconv.FormatUint(log.Param13, 10))
	r.SetTrack("guild_donate_num", strconv.FormatUint(log.Param14, 10))
	r.SetTrack("guild_user_activity_point", strconv.FormatUint(log.Param16, 10))
	r.SetTrack("donate_id", strconv.FormatUint(log.Param17, 10))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildDonateReceiveMessage struct {
	helper.BaseMessage
}

func (m *GuildDonateReceiveMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_donate_receive_ids", log.Param10) //已领取的奖励ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildTextSetMessage struct {
	helper.BaseMessage
}

func (m *GuildTextSetMessage) Execute(log *da.Log) bool {
	var logInfo *sub.GuildLogText
	err := helper.Json.Unmarshal([]byte(log.Param1), &logInfo)
	if err != nil {
		l4g.Error("[GuildTextSetMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id", strconv.FormatUint(logInfo.Gid, 10))                     //
	r.SetTrack("guild_name", logInfo.Name)                                          //
	r.SetTrack("guild_createtime", helper.FormatTime(logInfo.CreateTm, log.Param9)) //
	r.SetTrack("guild_language", logInfo.Language)                                  //
	r.SetTrack("guild_join_type", logInfo.JoinType)                                 //
	r.SetTrack("guild_tag", logInfo.Label)                                          //
	r.SetTrack("guild_level_limit", logInfo.LvLimit)                                //
	r.SetTrack("guild_power_limit", logInfo.PowerLimit)                             //
	r.SetTrack("type", logInfo.Type)
	r.SetTrack("content", logInfo.Content)
	r.SetTrack("guild_badge_scence", logInfo.Background)
	r.SetTrack("guild_badge_picture", logInfo.Icon)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildUserJoinOrQuitMessage struct {
	helper.BaseMessage
}

func (m *GuildUserJoinOrQuitMessage) Execute(log *da.Log) bool {
	var logInfo *sub.GuildUserJoinOrQuit
	err := helper.Json.Unmarshal([]byte(log.Param1), &logInfo)
	if err != nil {
		l4g.Errorf("[GuildUserJoinOrQuitMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id", strconv.FormatUint(logInfo.Gid, 10))                     //
	r.SetTrack("guild_name", logInfo.Name)                                          //
	r.SetTrack("guild_createtime", helper.FormatTime(logInfo.CreateTm, log.Param9)) //
	r.SetTrack("guild_old_id", logInfo.OldGid)                                      //
	r.SetTrack("type", logInfo.Type)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildManageMessage struct {
	helper.BaseMessage
}

func (m *GuildManageMessage) Execute(log *da.Log) bool {
	var logInfo *sub.GuildManage
	err := helper.Json.Unmarshal([]byte(log.Param1), &logInfo)
	if err != nil {
		l4g.Errorf("[GuildManageMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id", strconv.FormatUint(logInfo.Gid, 10))                     //
	r.SetTrack("guild_name", logInfo.Name)                                          //
	r.SetTrack("guild_createtime", helper.FormatTime(logInfo.CreateTm, log.Param9)) //
	r.SetTrack("type", logInfo.Type)
	memberIds := make([]string, 0, len(logInfo.MemberId))
	for _, id := range logInfo.MemberId {
		memberIds = append(memberIds, fmt.Sprintf("%d", id))
	}
	r.SetTrack("guild_member_id", memberIds)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuildPositionChangeMessage struct {
	helper.BaseMessage
}

func (m *GuildPositionChangeMessage) Execute(log *da.Log) bool {
	var logInfo *sub.GuildPosition
	err := helper.Json.Unmarshal([]byte(log.Param1), &logInfo)
	if err != nil {
		l4g.Errorf("[GuildPositionChangeMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guild_id", strconv.FormatUint(logInfo.Gid, 10))                     //
	r.SetTrack("guild_name", logInfo.Name)                                          //
	r.SetTrack("guild_createtime", helper.FormatTime(logInfo.CreateTm, log.Param9)) //

	r.SetTrack("guild_manager_id", strconv.FormatUint(logInfo.ManagerId, 10))
	r.SetTrack("guild_manager_position", logInfo.ManagerPosition)
	r.SetTrack("guild_member_old_position", logInfo.MemberOldPosition)
	r.SetTrack("guild_member_new_position", logInfo.MemberNewPosition)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGuildMobScoreMessage struct {
	helper.BaseMessage
}

func (m *LogGuildMobScoreMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var logData *sub.LogGuildMobScore
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogGuildMobScoreMessage] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("guild_id", strconv.FormatUint(logData.GuildId, 10))
	r.SetTrack("score", logData.Score)
	r.SetTrack("current_score", logData.CurrentScore)
	r.SetTrack("guild_level", logData.GuildLevel)
	r.SetTrack("battle_zone", logData.Partition)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGuildMobAcceptTaskMessage struct {
	helper.BaseMessage
}

func (m *LogGuildMobAcceptTaskMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("task_id", log.Param10)
	r.SetTrack("task_quality", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogGuildMobBuyTimesMessage struct {
	helper.BaseMessage
}

func (m *LogGuildMobBuyTimesMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("number_type", log.Param10)
	r.SetTrack("purchase_num", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
