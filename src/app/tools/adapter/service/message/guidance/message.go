package guidance

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUIDANCE_FINISH_NODE),
		&GuidanceFinishNodeMessage{}, "guide_finish_node", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUIDANCE_FINISH_GROUP),
		&GuidanceFinishGroupMessage{}, "guide_finish_group", 9, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUIDANCE_SELECT_SKIP_TAG),
		&GuidanceSelectSkipTagMessage{}, "guide_select_skip_tag", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUIDANCE_SKIP),
		&GuidanceSkipMessage{}, "guide_skip", 10, 0, 0, 0)
}

type GuidanceFinishNodeMessage struct {
	helper.BaseMessage
}

func (m *GuidanceFinishNodeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guide_group", log.Param10)
	r.SetTrack("guide_step", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuidanceFinishGroupMessage struct {
	helper.BaseMessage
}

func (m *GuidanceFinishGroupMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guide_group", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuidanceSelectSkipTagMessage struct {
	helper.BaseMessage
}

func (m *GuidanceSelectSkipTagMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guide_select_skip_tag", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type GuidanceSkipMessage struct {
	helper.BaseMessage
}

func (m *GuidanceSkipMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("guide_skip", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
