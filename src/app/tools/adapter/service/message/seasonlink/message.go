package seasonlink

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_LINK_RUNE_COLLECTION),
		&SeasonLinkRuneCollectionMessage{}, "seasonlink_rune_collection", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_LINK_MONUMENT_CULTIVATION),
		&SeasonLinkMonumentCultivationMessage{}, "seasonlink_monument_cultivation", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_LINK_RECYCLE),
		&SeasonLinkRecycleMessage{}, "seasonlink_recycle", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_LINK_MONUMENT_RECV_RARE_AWARDS),
		&SeasonLinkMonumentRecvRareAwardsMessage{}, "seasonlink_monument_recv_rare_awards", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_LINK_ACTIVATION),
		&SeasonLinkActivationMessage{}, "seasonlink_active", 13, 0, 0, 0)
}

type SeasonLinkRuneCollectionMessage struct {
	helper.BaseMessage
}

func (m *SeasonLinkRuneCollectionMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("rune_id", log.Param10)
	r.SetTrack("rune_unique_level", log.Param11)
	r.SetTrack("rune_count", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonLinkMonumentCultivationMessage struct {
	helper.BaseMessage
}

func (m *SeasonLinkMonumentCultivationMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetSeasonLinkRuneMessage(log.Param1)
	r.SetTrack("monument_id", log.Param10)
	r.SetTrack("monument_unique_level", log.Param11)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonLinkRecycleMessage struct {
	helper.BaseMessage
}

func (m *SeasonLinkRecycleMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("recycle_point", log.Param11)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonLinkMonumentRecvRareAwardsMessage struct {
	helper.BaseMessage
}

func (m *SeasonLinkMonumentRecvRareAwardsMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	if log.Param1 != "" {
		awardIds := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("seasonlink_monument_rare_award_ids", awardIds) // 领奖Id
	}
	r.SetTrack("monument_id", log.Param10) // 丰碑id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonLinkActivationMessage struct {
	helper.BaseMessage
}

func (m *SeasonLinkActivationMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("seasonlink_id", log.Param10) // 羁绊id
	r.SetTrack("hero_sys_id", log.Param11)   // 英雄量表id

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
