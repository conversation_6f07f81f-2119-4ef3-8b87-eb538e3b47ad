package recharge

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SET_ACCOUNT_TAG),
		&SetAccountTagMessage{}, "set_account_tag", 10, 0, 0, 0)
}

type SetAccountTagMessage struct {
	helper.BaseMessage
}

func (m *SetAccountTagMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("account_tag", log.Param10) // 账号标识
	r.SetTrack("mute_value", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
