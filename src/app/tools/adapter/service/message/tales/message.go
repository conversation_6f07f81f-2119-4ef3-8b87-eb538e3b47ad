package tales

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALES_CHAPTER_FIGHT),
		&TalesFightMessage{}, "tales_fight", 15, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALES_CHAPTER_FINISH),
		&TalesFinishMessage{}, "tales_finish", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALES_CHAPTER_TAKE_REWARD),
		&TalesTakeAwardMessage{}, "tales_take_award", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALES_ELITE_FIGHT),
		&TalesEliteFightMessage{}, "tales_elite_fight", 15, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALES_ELITE_WIPE),
		&TalesEliteWipeMessage{}, "tales_elite_wipe", 11, 0, 0, 0)
}

// 章节战斗
type TalesFightMessage struct {
	helper.BaseMessage
}

func (m *TalesFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tales_id", log.Param10)               // 列传ID
	r.SetTrack("tales_chapter_id", log.Param11)       // 章节ID
	r.SetTrack("win", helper.FormatBool(log.Param12)) // 是否胜利
	r.SetFormationMessage(log.Param2)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 章节完成
type TalesFinishMessage struct {
	helper.BaseMessage
}

func (m *TalesFinishMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tales_id", log.Param10)         // 列传ID
	r.SetTrack("tales_chapter_id", log.Param11) // 章节ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 章节领取奖励
type TalesTakeAwardMessage struct {
	helper.BaseMessage
}

func (m *TalesTakeAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tales_id", log.Param10) // 列传ID
	//r.SetTrack("tales_chapter_id", log.Param11)   // 章节ID
	r.SetTrack("tales_award_status", log.Param12) // 奖励状态
	r.SetTrack("tales_chapter_ids", log.Param1)   // 领取的章节ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 强敌挑战
type TalesEliteFightMessage struct {
	helper.BaseMessage
}

func (m *TalesEliteFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tales_elite_id", log.Param10)         // 强敌id
	r.SetTrack("tales_fight_count", log.Param11)      // 今日已挑战此强敌的次数
	r.SetTrack("win", helper.FormatBool(log.Param12)) // 是否胜利
	r.SetFormationMessage(log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 强敌扫荡
type TalesEliteWipeMessage struct {
	helper.BaseMessage
}

func (m *TalesEliteWipeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("tales_elite_id", log.Param10)    // 强敌id
	r.SetTrack("tales_fight_count", log.Param11) // 今日已挑战此强敌的次数
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
