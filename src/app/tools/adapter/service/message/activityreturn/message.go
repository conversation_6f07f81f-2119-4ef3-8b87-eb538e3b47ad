package activityreturn

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS),
		&ReturnTakeLoginAwardsMessage{}, "return_take_login_awards", 13, 0, 0, 0)
}

// 领取奖励
type ReturnTakeLoginAwardsMessage struct {
	helper.BaseMessage
}

func (m *ReturnTakeLoginAwardsMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("return_id", log.Param10)                  //回流ID
	r.SetTrack("return_login_day_index", log.Param11)     //第几天领取
	r.SetTrack("return_login_day_count", log.Param12)     //累计登录天数
	r.SetTrack("return_login_reward_status", log.Param13) //领取后奖励领取状态
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
