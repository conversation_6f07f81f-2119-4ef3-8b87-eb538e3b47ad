package memory

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MEMORY_CHIP_UNLOCK),
		&MemoryUnlockMessage{}, "memory_unlock", 10, 0, 0, 0)
}

// 境界解锁
type MemoryUnlockMessage struct {
	helper.BaseMessage
}

func (m *MemoryUnlockMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("memory_chip_id", log.Param10) // 回忆点id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
