package avatar

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_AVATAR_ADD),
		&AvatarAddMessage{}, "avatar_add", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_AVATAR_TIME_EXPAND),
		&AvatarAddMessage{}, "avatar_add", 12, 0, 0, 0)
	/*msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_AVATAR_SET_NAME),
	&AvatarAddMessage{}, "avatar_set_name", 8, 0, 0, 3)*/
}

type AvatarAddMessage struct {
	helper.BaseMessage
}

func (m *AvatarAddMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("avatar_id", log.Param10)
	r.SetTrack("start_time", helper.FormatTime(int64(log.Param11), log.Param9))
	r.SetTrack("end_time", helper.FormatTime(int64(log.Param12), log.Param9))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type AvatarSetNameMessage struct {
	helper.BaseMessage
}

func (m *AvatarSetNameMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("old_name", log.Param1)
	r.SetTrack("new_name", log.Param2)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type AvatarTimeExpandMessage struct {
	helper.BaseMessage
}

func (m *AvatarTimeExpandMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("avatar_id", log.Param10)
	r.SetTrack("start_time", helper.FormatTime(int64(log.Param11), log.Param9))
	r.SetTrack("end_time", helper.FormatTime(int64(log.Param12), log.Param9))
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
