package base

import (
	"strconv"

	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_REGISTER), 0,
		&RegisterMessage{}, "register", 7, 10, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_REGISTER), 0,
		&AccountFirstRegisterMessage{}, "account_first_register", 8, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_REGISTER), 0,
		&DeviceFirstRegisterMessage{}, "device_first_register", 8, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LOGIN), uint32(sub.SUB_TYPE_ID_LOGIN_SUCCESS),
		&LoginMessage{}, "login", 16, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LOGOUT), 0,
		&LogoutMessage{}, "logout", 25, 0, 0, 3) //logout实际after为1，因为可能穿插login所以为3
	// msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_RECHARGE), 0,
	// 	&RechargeMessage{}, "recharge", 26, 2, 2, 8)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_ONLINE_NUM), 0,
		&OnlineNumMessage{}, "online_num", 3, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_LEVEL_UP), 0,
		&LevelUpMessage{}, "level_up", 11, 0, 0, 1)
}

// tracks: 7
func registerMessage(r *helper.MsgRecorder, log *da.Log, extra *helper.Extra) {
	r.GetMinimalMessage(log)
	r.GetDeviceMessage(extra)
}

// tracks: 10
// afters: 1
func loginMessage(r *helper.MsgRecorder, log *da.Log, extra *helper.Extra,
	loginTime, dungeonID uint64) {
	log.EventTime = loginTime
	r.SetEvent("login")
	r.GetMinimalMessage(log)
	r.GetDeviceMessage(extra)
	r.SetTrack("power_value_str", strconv.FormatUint(log.Power, 10))
	r.SetTrack("vip_level", log.VipLevel)
	r.SetTrack("level", log.Level)
	r.SetTrack("dungeon_id", dungeonID)
	r.SetTrack("role_name", log.Name)
	r.SetAfter("power_value", strconv.FormatUint(log.Power, 10))
	r.SetAfter("lastlogin_time", helper.FormatTime(int64(loginTime), log.Param9))
	r.SetAfter("op_group", strconv.FormatUint(uint64(log.OpGroup), 10))
	r.SetAfter("op_id", strconv.FormatUint(uint64(log.OpID), 10))
	r.SetAfter("open_day", log.OpenDay)
	r.SetAfter("login_day", log.LoginDay)
	r.SetAfter("season_id", log.SeasonID)
	r.SetAfter("season_level", log.SeasonLevel)
	r.SetAfter("season_power", strconv.FormatUint(log.SeasonPower, 10))
	r.SetAfter("guild_id", strconv.FormatUint(log.GuildID, 10))
	r.SetAfter("account_tag", log.AccountTag)
	if log.CreateTime > 0 {
		r.SetAfter("life_cycle", (int64(log.EventTime)-log.CreateTime)/86400+1)
	}
	r.SetAfter("battle_zone", log.BattleZone)
}

// tracks: 20
// afters: 1
func logoutMessage(r *helper.MsgRecorder, log *da.Log, extra *helper.Extra, logoutTime, onlineTime uint64) {
	log.EventTime = logoutTime
	r.SetEvent("logout")
	r.GetBaseMessage(log)
	r.GetDeviceMessage(extra)
	r.SetTrack("logout_reason", log.LogSubType)
	r.SetTrack("online_time", onlineTime)
	r.SetAfter("lastlogout_time", helper.FormatTime(int64(logoutTime), log.Param9))
}

type RegisterMessage struct {
	helper.BaseMessage
}

// inits: 3
func (m *RegisterMessage) ExtraInits(r *helper.MsgRecorder) {
	// r.SetInit("tower_floor", 0)
	// r.SetInit("dungeon_id", 0)
	// r.SetInit("forest_level", 0)
}

func (m *RegisterMessage) Execute(log *da.Log) bool {
	extra := helper.NewExtra(log)
	if extra == nil {
		return false
	}
	r := m.GetRecorder()
	registerMessage(r, log, extra)
	//由于开服清档前，内部账号的数据会被记录
	//玩家数据的主键是 serverID_uid
	//为避免玩家数据与内部测试账号重叠，现将以下原SetInit改为SetAfter（可重写）
	//由业务约束，LOG_TYPE_DA_ID_REGISTER只能被调用一次
	r.SetAfter("create_time", helper.FormatTime(int64(log.EventTime), log.Param9))
	r.SetAfter("role_id", strconv.FormatUint(log.UserID, 10))
	r.SetAfter("user_id", log.UUID)
	r.SetAfter("device_id", log.DeviceID)
	r.SetAfter("channel", helper.GetChannel(log))
	r.SetAfter("server_id", strconv.FormatUint(log.ServerID, 10))
	r.SetAfter("register_ip", log.ClientIP)
	// m.ExtraInits(r)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type AccountFirstRegisterMessage struct {
	helper.BaseMessage
}

func (m *AccountFirstRegisterMessage) Execute(log *da.Log) bool {
	extra := helper.NewExtra(log)
	if extra == nil {
		return false
	}
	r := m.GetRecorder()
	registerMessage(r, log, extra)
	r.SetTrack("#first_check_id", log.UUID)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type DeviceFirstRegisterMessage struct {
	helper.BaseMessage
}

func (m *DeviceFirstRegisterMessage) Execute(log *da.Log) bool {
	extra := helper.NewExtra(log)
	if extra == nil {
		return false
	}
	r := m.GetRecorder()
	registerMessage(r, log, extra)
	r.SetTrack("#first_check_id", log.DeviceID)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LoginMessage struct {
	helper.BaseMessage
}

func (m *LoginMessage) Execute(log *da.Log) bool {
	extra := helper.NewExtra(log)
	if extra == nil {
		return false
	}
	r := m.GetRecorder()
	loginMessage(r, log, extra, log.Param10, log.Param16)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LogoutMessage struct {
	helper.BaseMessage
}

func (m *LogoutMessage) Execute(log *da.Log) bool {
	extra := helper.NewExtra(log)
	if extra == nil {
		return false
	}
	// 针对跨天的情况开始记录额外事件
	dailyZero := uint64(util.DailyZeroByTime(int64(log.Param11))) // 按登出时间计算当日0点
	onlineTime := log.Param20
	todayOnlineTime := util.SafeSubUint64(log.Param11, dailyZero)
	if todayOnlineTime > onlineTime {
		todayOnlineTime = onlineTime
	}
	// 先记录今天的离线日志
	r := m.GetRecorder()
	if onlineTime > todayOnlineTime {
		logoutMessage(r, log, extra, log.Param11, todayOnlineTime)
	} else {
		logoutMessage(r, log, extra, log.Param11, onlineTime)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	if !ret {
		return false
	}
	// 记录跨天的上线和离线日志
	// 需要注意的是跨天之后假设的情况是会不会跨x天以上
	// 因此在最后会修改todayOnlineTime为86400，来检查是否可能出现跨天情况
	var realOnlineTm uint64
	for onlineTime > todayOnlineTime {
		onlineTime -= todayOnlineTime
		r = m.GetRecorder()
		loginMessage(r, log, extra, dailyZero, log.Param13)
		ret = r.Write(log)
		m.PutRecorder(r)
		if !ret {
			return false
		}

		r = m.GetRecorder()
		realOnlineTm = onlineTime
		if realOnlineTm > util.DaySecs {
			realOnlineTm = util.DaySecs
		}
		logoutMessage(r, log, extra, dailyZero-1, realOnlineTm)
		ret = r.Write(log)
		m.PutRecorder(r)
		if !ret {
			return false
		}
		dailyZero -= util.DaySecs
		todayOnlineTime = util.DaySecs
	}
	return ret
}

type RechargeMessage struct {
	helper.BaseMessage
}

func (m *RechargeMessage) Execute(log *da.Log) bool {
	extra := helper.NewExtra(log)
	if extra == nil {
		return false
	}
	evtTime := helper.FormatTime(int64(log.EventTime), log.Param9)
	fMoney := float64(log.Param10)
	r := m.GetRecorder()
	if log.Param11 == 2 {
		//使用充值额度
		r.SetEvent("token_consume")
	} else {
		r.SetTrack("SDK_order_id", log.Param2)
		r.SetTrack("pay_order_id", log.Param3)
		r.SetTrack("charging_money", fMoney)
		r.SetTrack("charging_money_type", log.Param4)
		// r.SetInit("firstrecharge_money", fMoney)
		// r.SetInit("firstrecharge_time", evtTime)
		// r.SetAdd("totalrecharge_money", fMoney)
		// r.SetAdd("totalrecharge_times", 1)
		r.SetInit("first_recharge_time", evtTime)
		r.SetAfter("last_recharge_time", evtTime)
		// r.SetAfter("lastrecharge_money", fMoney)
	}
	r.GetMinimalMessage(log)
	r.GetDeviceMessage(extra)
	r.SetTrack("vip_level", log.VipLevel)
	r.SetTrack("level", log.Level)
	r.SetTrack("recharge_function_id", strconv.FormatUint(log.Param13, 10))
	r.SetTrack("game_order_id", log.Param1)
	r.SetTrack("money", fMoney)
	r.SetTrack("money_type", log.Param4)
	r.SetTrack("payway", 1)
	productID := log.Param6
	if productID == "" {
		productID = log.Param5
	}
	r.SetTrack("purchase_id", productID)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type OnlineNumMessage struct {
	helper.BaseMessage
}

func (m *OnlineNumMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.SetUserID("1000000001")
	r.SetDistinctID("1000000001")
	r.SetTrack("channel", helper.GetChannel(log))
	r.SetTrack("server_id", strconv.FormatUint(log.ServerID, 10))
	r.SetTrack("online_user", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type LevelUpMessage struct {
	helper.BaseMessage
}

func (m *LevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("old_exp", log.Param10)
	r.SetTrack("exp", log.Param11)
	r.SetTrack("old_level", log.Param12)
	r.SetAfter("level", log.Level)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
