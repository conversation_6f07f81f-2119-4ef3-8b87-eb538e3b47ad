package goddesscontract

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_LEVEL_UP),
		&GoddessLevelUpMessage{}, "goddess_level_up", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_CONTRACT_LEVEL_UP),
		&GoddessContractLevelUpMessage{}, "goddess_contract_level_up", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_INIT),
		&GoddessInitMessage{}, "goddess_init", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_FEED),
		&GoddessFeedMessage{}, "goddess_feed", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_CHAPTER_FIGHT),
		&GoddessChapterFightMessage{}, "goddess_chapter_fight", 16, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_CHAPTER_FINISH),
		&GoddessChapterFinishMessage{}, "goddess_chapter_finish", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_CHAPTER_TAKE_REWARD),
		&GoddessChapterTakeAwardMessage{}, "goddess_chapter_take_award", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_UNLOCK_CHAPTER),
		&GoddessUnlockChapterMessage{}, "goddess_unlock_chapter", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_TOUCH),
		&GoddessTouchMessage{}, "goddess_touch", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_STORY_REWARD),
		&GoddessStoryAwardMessage{}, "goddess_story_award", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GODDESS_COLLECTION),
		&GoddessCollectionMessage{}, "goddess_collection", 10, 0, 0, 0)
}

// 女神升级
type GoddessLevelUpMessage struct {
	helper.BaseMessage
}

func (m *GoddessLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("goddess_id", log.Param11)
	r.SetTrack("goddess_level", log.Param12)
	if log.Param13 > 0 {
		r.SetTrack("goddess_skill_level", log.Param13)
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 契约之所升级
type GoddessContractLevelUpMessage struct {
	helper.BaseMessage
}

func (m *GoddessContractLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("goddess_contract_old_level", log.Param11)
	r.SetTrack("goddess_contract_new_level", log.Param12)
	r.SetTrack("goddess_contract_old_score", log.Param13)
	r.SetTrack("goddess_contract_new_score", log.Param14)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 女神初始化
type GoddessInitMessage struct {
	helper.BaseMessage
}

func (m *GoddessInitMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("goddess_id", log.Param11)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 女神喂养
type GoddessFeedMessage struct {
	helper.BaseMessage
}

func (m *GoddessFeedMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("goddess_id", log.Param11)
	r.SetTrack("goddess_exp", log.Param12)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 章节战斗
type GoddessChapterFightMessage struct {
	helper.BaseMessage
}

func (m *GoddessChapterFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("goddess_id", log.Param10)             // 女武神ID
	r.SetTrack("goddess_chapter_id", log.Param11)     // 章节ID
	r.SetTrack("win", helper.FormatBool(log.Param12)) // 是否胜利
	r.SetFormationMessage(log.Param2)
	r.SetTrack("report_id", log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 章节完成
type GoddessChapterFinishMessage struct {
	helper.BaseMessage
}

func (m *GoddessChapterFinishMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("goddess_id", log.Param10)         // 女武神ID
	r.SetTrack("goddess_chapter_id", log.Param11) // 章节ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 章节领取奖励
type GoddessChapterTakeAwardMessage struct {
	helper.BaseMessage
}

func (m *GoddessChapterTakeAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("goddess_id", log.Param10) // 女武神ID
	//r.SetTrack("tales_chapter_id", log.Param11)   // 章节ID
	r.SetTrack("goddess_award_status", log.Param12) // 奖励状态
	r.SetTrack("goddess_chapter_ids", log.Param1)   // 领取的章节ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 章节完成
type GoddessUnlockChapterMessage struct {
	helper.BaseMessage
}

func (m *GoddessUnlockChapterMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("goddess_id", log.Param10)                // 女武神ID
	r.SetTrack("goddess_unlock_chapter_id", log.Param11) // 章节ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 女神喂养
type GoddessTouchMessage struct {
	helper.BaseMessage
}

func (m *GoddessTouchMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("goddess_id", log.Param11)
	r.SetTrack("goddess_exp", log.Param12)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 章节完成
type GoddessStoryAwardMessage struct {
	helper.BaseMessage
}

func (m *GoddessStoryAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("goddess_id", log.Param10)             // 女武神ID
	r.SetTrack("goddess_award_story_id", log.Param11) // 章节ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// GoddessCollectionMessage 收集藏品
type GoddessCollectionMessage struct {
	helper.BaseMessage
}

func (m *GoddessCollectionMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("goddess_collections", log.Param1) // 藏品

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
