package dungeon

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DUNGEON_FIGHT),
		&DungeonFightMessage{}, "dungeon_fight", 16, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DUNGEON_RECV_AWARD),
		&DungeonRecvAwardMessage{}, "dungeon_recv_award", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DUNGEON_SPEED_RECV_AWARD),
		&DungeonSpeedRecvAwardMessage{}, "dungeon_speed_recv_award", 10, 0, 0, 0)
}

type DungeonFightMessage struct {
	helper.BaseMessage
}

func (m *DungeonFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	win := helper.FormatBool(log.Param12)
	r.SetTrack("dungeon_id", log.Param10)
	if log.Param11 > 0 {
		r.SetTrack("onhook_start_time", helper.FormatTime(int64(log.Param11), log.Param9))
	}
	r.SetTrack("win", win)
	quick := helper.FormatBool(log.Param13)
	r.SetTrack("quick_fight", quick)
	r.SetFormationMessage(log.Param2)
	r.SetTrack("report_id", log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type DungeonRecvAwardMessage struct {
	helper.BaseMessage
}

func (m *DungeonRecvAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("dungeon_id", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type DungeonSpeedRecvAwardMessage struct {
	helper.BaseMessage
}

func (m *DungeonSpeedRecvAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("dungeon_id", log.Param10)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
