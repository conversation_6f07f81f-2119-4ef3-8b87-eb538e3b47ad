package vip

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_VIP_BUY_GIFT),
		&BuyGiftMessage{}, "buy_vip_gift", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_VIP_RECHARGE_GIFT),
		&RechargeGiftMessage{}, "recharge_vip_gift", 11, 0, 0, 0)
}

type BuyGiftMessage struct {
	helper.BaseMessage
}

func (m *BuyGiftMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("gift_lv", log.Param10)
	r.SetTrack("gift_buy_status", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type RechargeGiftMessage struct {
	helper.BaseMessage
}

func (m *RechargeGiftMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("vip", log.Param10) // vip
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
