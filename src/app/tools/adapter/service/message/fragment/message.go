package fragment

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FRAGMENT_COMPOSE),
		&FragmentCompose{}, "fragment_compose", 12, 0, 0, 0)
}

// 碎片-合成
type FragmentCompose struct {
	helper.BaseMessage
}

func (m *FragmentCompose) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("fragment_id", log.Param10)
	r.SetTrack("fragment_num", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
