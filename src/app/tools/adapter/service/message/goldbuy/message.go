package goldbuy

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GOLD_BUY_GET_GOLD),
		&GoldBuyGetGoldMessage{}, "gold_buy_get_gold", 12, 0, 0, 0)
}

// 获得金币
type GoldBuyGetGoldMessage struct {
	helper.BaseMessage
}

func (m *GoldBuyGetGoldMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("box_id", log.Param10)     // 金币宝箱id
	r.SetTrack("use_count", log.Param11)  // 使用次数
	r.SetTrack("dungeon_id", log.Param12) // 主线id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
