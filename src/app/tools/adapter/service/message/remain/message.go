package remain

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_REMAIN_STAR_UP),
		&RemainStarUpMessage{}, "remain_star_up", 15, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_REMAIN_BOOK_RECV_EXP),
		&RemainBookRecvExpMessage{}, "remain_book_recv_exp", 15, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_REMAIN_BOOK_LEVEL_UP),
		&RemainBookLevelUpMessage{}, "remain_book_level_up", 15, 0, 0, 1)
}

type RemainStarUpMessage struct {
	helper.BaseMessage
}

func (m *RemainStarUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("remain_id", log.Param10)       // 遗物ID
	r.SetTrack("remain_old_star", log.Param11) // 老遗物星级
	r.SetTrack("remain_new_star", log.Param12) // 新遗物星级
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type RemainBookRecvExpMessage struct {
	helper.BaseMessage
}

func (m *RemainBookRecvExpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("remain_book_level", log.Param10)
	r.SetTrack("remain_book_total_exp", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type RemainBookLevelUpMessage struct {
	helper.BaseMessage
}

func (m *RemainBookLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("remain_book_old_level", log.Param10) // 旧等级
	r.SetTrack("remain_book_new_level", log.Param11) // 新等级
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
