package preseason

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_PRE_SEASON_RECV),
		&PreSeasonRecvMessage{}, "pre_season_recv_award", 15, 0, 0, 1)
}

type PreSeasonRecvMessage struct {
	helper.BaseMessage
}

func (m *PreSeasonRecvMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("pre_dungeon_recv_id", log.Param11) //赛季前领奖ID
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
