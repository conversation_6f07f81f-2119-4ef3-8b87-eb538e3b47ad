package seasonarena

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
	"strconv"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_ARENA_RECV_DIVISION_AWARD),
		&SeasonArenaRecvDivisionAwardMessage{}, "season_arena_recv_division_award", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_ARENA_RECV_TASK_AWARD),
		&SeasonArenaRecvTaskAwardMessage{}, "season_arena_recv_task_award", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_ARENA_DIVISION_CHANGE),
		&SeasonArenaDivisonChangeMessage{}, "season_arena_division_change", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_ARENA_FIGHT),
		&SeasonArenaDivisonFightMessage{}, "season_arena_fight", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_ARENA_BE_FIGHT),
		&SeasonArenaDivisonBeFightMessage{}, "season_arena_be_fight", 13, 0, 0, 0)
}

type SeasonArenaRecvDivisionAwardMessage struct {
	helper.BaseMessage
}

func (m *SeasonArenaRecvDivisionAwardMessage) Execute(log *da.Log) bool {
	var ids []uint32
	err := helper.Json.Unmarshal([]byte(log.Param1), &ids)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("season_arena_division_ids", ids)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonArenaRecvTaskAwardMessage struct {
	helper.BaseMessage
}

func (m *SeasonArenaRecvTaskAwardMessage) Execute(log *da.Log) bool {
	var taskIDs []uint32
	err := helper.Json.Unmarshal([]byte(log.Param1), &taskIDs)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("season_arena_task_ids", taskIDs)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonArenaDivisonChangeMessage struct {
	helper.BaseMessage
}

func (m *SeasonArenaDivisonChangeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("old_division", log.Param10)
	r.SetTrack("new_division", log.Param11)
	r.SetTrack("round", log.Param12)
	r.SetTrack("zone", log.Param13)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonArenaDivisonFightMessage struct {
	helper.BaseMessage
}

func (m *SeasonArenaDivisonFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var myScoreAndRank *cl.SeasonArenaUserScoreAndRank
	var opponentScoreAndRank *cl.SeasonArenaUserScoreAndRank

	if len(log.Param1) > 0 {
		err := helper.Json.Unmarshal([]byte(log.Param1), &myScoreAndRank)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
	}

	if len(log.Param2) > 0 {
		err := helper.Json.Unmarshal([]byte(log.Param2), &opponentScoreAndRank)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
	}

	if myScoreAndRank != nil {
		r.SetTrack("current_score", myScoreAndRank.NewScore)
		r.SetTrack("current_rank", myScoreAndRank.NewRank)
		r.SetTrack("current_grade", log.Param15)
		score := int(myScoreAndRank.NewScore) - int(myScoreAndRank.OldScore)
		r.SetTrack("score", score)
	}

	if opponentScoreAndRank != nil {
		r.SetTrack("opponent_uid", strconv.FormatUint(log.Param10, 10))
		r.SetTrack("opponent_sid", strconv.FormatUint(log.Param13, 10))
		r.SetTrack("opponent_current_score", opponentScoreAndRank.NewScore)
		r.SetTrack("opponent_current_rank", opponentScoreAndRank.NewRank)
		r.SetTrack("opponent_current_grade", log.Param16)
		score := int(opponentScoreAndRank.NewScore) - int(opponentScoreAndRank.OldScore)
		r.SetTrack("opponent_score", score)
	}

	r.SetTrack("round", log.Param11)
	r.SetTrack("win", helper.FormatBool(log.Param12))
	r.SetTrack("is_bot", helper.FormatBool(log.Param14))
	r.SetTrack("report_id", log.Param4)
	r.SetTrack("zone", log.Param19)
	r.SetTrack("opponent_zone", log.Param17)
	r.SetTrack("opponent_guild_id", strconv.FormatUint(log.Param18, 10))
	r.SetFormationMessage(log.Param3)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonArenaDivisonBeFightMessage struct {
	helper.BaseMessage
}

func (m *SeasonArenaDivisonBeFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	var myScoreAndRank *cl.SeasonArenaUserScoreAndRank
	var opponentScoreAndRank *cl.SeasonArenaUserScoreAndRank

	if len(log.Param1) > 0 {
		err := helper.Json.Unmarshal([]byte(log.Param1), &myScoreAndRank)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
	}

	if len(log.Param2) > 0 {
		err := helper.Json.Unmarshal([]byte(log.Param2), &opponentScoreAndRank)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
	}

	if myScoreAndRank != nil {
		r.SetTrack("current_score", myScoreAndRank.NewScore)
		r.SetTrack("current_rank", myScoreAndRank.NewRank)
		r.SetTrack("current_grade", log.Param15)
		score := int(myScoreAndRank.NewScore) - int(myScoreAndRank.OldScore)
		r.SetTrack("score", score)
	}

	if opponentScoreAndRank != nil {
		r.SetTrack("opponent_uid", strconv.FormatUint(log.Param10, 10))
		r.SetTrack("opponent_sid", strconv.FormatUint(log.Param13, 10))
		r.SetTrack("opponent_current_score", opponentScoreAndRank.NewScore)
		r.SetTrack("opponent_current_rank", opponentScoreAndRank.NewRank)
		r.SetTrack("opponent_current_grade", log.Param16)
		score := int(opponentScoreAndRank.NewScore) - int(opponentScoreAndRank.OldScore)
		r.SetTrack("opponent_score", score)
	}

	r.SetTrack("round", log.Param11)
	r.SetTrack("win", helper.FormatBool(log.Param12))
	r.SetTrack("is_bot", helper.FormatBool(log.Param14))
	r.SetTrack("report_id", log.Param4)
	r.SetTrack("zone", log.Param19)
	r.SetTrack("opponent_zone", log.Param17)
	r.SetTrack("opponent_guild_id", strconv.FormatUint(log.Param18, 10))

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
