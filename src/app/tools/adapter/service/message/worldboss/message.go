package worldboss

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WORLD_BOSS_SELECT_LEVEL),
		&WorldBossSelectLevel{}, "world_boss_select_level", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WORLD_BOSS_TASK_RECV_AWARD),
		&WorldBossTaskRecvAward{}, "world_boss_task_recv_award", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_WORLD_BOSS_FIGHT),
		&WorldBossTaskFight{}, "world_boss_fight", 31, 0, 0, 0)
}

type WorldBossSelectLevel struct {
	helper.BaseMessage
}

func (m *WorldBossSelectLevel) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("world_boss_data", log.Param1)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type WorldBossTaskRecvAward struct {
	helper.BaseMessage
}

func (m *WorldBossTaskRecvAward) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("world_boss_term", log.Param10)   // 期数
	r.SetTrack("world_boss_task_id", log.Param1) // 领奖的任务id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type WorldBossTaskFight struct {
	helper.BaseMessage
}

func (m *WorldBossTaskFight) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("world_boss_term", log.Param10)           // 期数
	r.SetTrack("world_boss_battle_op_type", log.Param11) // 战斗类型
	r.SetTrack("world_boss_room_id", log.Param12)
	r.SetTrack("world_boss_type", log.Param13)   // 挑战消耗类型
	r.SetTrack("world_boss_damage", log.Param14) // 伤害
	r.SetTrack("world_boss_mode", log.Param15)   // 难度
	r.SetFormationMessage(log.Param1)
	if log.Param2 != "" {
		r.SetTrack("report_id", log.Param2)
	}

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
