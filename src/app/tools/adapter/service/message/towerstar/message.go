package towerstar

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TOWERSTAR_FIGHT),
		&TowerstarFightMessage{}, "tower_star_dungeon", 16, 0, 0, 0)
}

type TowerstarFightMessage struct {
	helper.BaseMessage
}

func (m *TowerstarFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("star_info", log.Param11)
	r.SetTrack("dungeon_id", log.Param10)
	r.SetTrack("fighting_info", log.Param1)
	quick := helper.FormatBool(log.Param12)
	r.SetTrack("quick_fight", quick)
	r.SetFormationMessage(log.Param2)
	r.SetTrack("report_id", log.Param3)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
