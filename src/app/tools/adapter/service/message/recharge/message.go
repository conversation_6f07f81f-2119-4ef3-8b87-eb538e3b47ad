package recharge

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"adapter/service/proto/out/cl"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
	"strconv"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECHARGE_NORMAL),
		&RechargeNormalMessage{}, "normal_recharge", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECHARGE_FIRST_GIFT),
		&FirstGiftMessage{}, "first_recharge_gift", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECHARGE_WEB_DIAMOND),
		&RechargeWebDiamondMessage{}, "web_recharge_diamond", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECHARGE_WEB_GIFT),
		&RechargeWebGiftMessage{}, "web_recharge_gift", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL),
		&RechargeWebLargeMessage{}, "web_large_recharge", 14, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_RECHARGE_COUPON),
		&RechargeCouponMessage{}, "coupon_recharge", 11, 0, 0, 0)
}

type RechargeNormalMessage struct {
	helper.BaseMessage
}

func (m *RechargeNormalMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("order_id", log.Param1)  // 订单id
	r.SetTrack("goods_id", log.Param10) // 充值商品id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type FirstGiftMessage struct {
	helper.BaseMessage
}

func (m *FirstGiftMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("order_id", log.Param1)           // 订单id
	r.SetTrack("first_recharge_id", log.Param10) // 首充id
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type RechargeWebDiamondMessage struct {
	helper.BaseMessage
}

func (m *RechargeWebDiamondMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("order_id", log.Param1)  // 订单id
	r.SetTrack("goods_id", log.Param10) // 充值商品id
	r.SetTrack("amount", log.Param11)   // 金额
	r.SetTrack("pay_time", log.Param12) // 支付时间
	isSandbox := log.Param13 == 1
	r.SetTrack("is_sandbox", isSandbox)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type RechargeWebGiftMessage struct {
	helper.BaseMessage
}

func (m *RechargeWebGiftMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("order_id", log.Param1)  // 订单id
	r.SetTrack("goods_id", log.Param10) // 充值商品id
	r.SetTrack("amount", log.Param11)   // 金额
	r.SetTrack("pay_time", log.Param12) // 支付时间
	isSandbox := log.Param13 == 1
	r.SetTrack("is_sandbox", isSandbox) // 是否沙盒
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 大额充值
type RechargeWebLargeMessage struct {
	helper.BaseMessage
}

func (m *RechargeWebLargeMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("order_id", log.Param1) // 订单id
	r.SetTrack("amount", log.Param10)  // 订单金额
	r.SetTrack("op_id", log.Param11)
	r.SetTrack("pay_time", int64(log.Param12))
	r.SetTrack("mail_id_str", strconv.FormatUint(log.Param13, 10))
	isSandbox := log.Param14 == 1
	r.SetTrack("is_sandbox", isSandbox) // 是否沙盒
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 代金券充值
type RechargeCouponMessage struct {
	helper.BaseMessage
}

func (m *RechargeCouponMessage) Execute(log *da.Log) bool {
	var awards []*cl.Resource
	err := helper.Json.Unmarshal([]byte(log.Param2), &awards)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}

	var awardsString string
	for _, award := range awards {
		awardsString += fmt.Sprintf("type:%d,value:%d,count:%d;", award.Type, award.Value, award.Count)
	}

	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("order_id", log.Param1)              // 订单id
	r.SetTrack("awards", awardsString)              // 奖励
	r.SetTrack("coupon_recharge_type", log.Param10) // 充值类型
	r.SetTrack("coupon_award_type", log.Param11)    // 奖励类型
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
