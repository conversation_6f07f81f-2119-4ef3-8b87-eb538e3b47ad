package skin

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SKIN_USE),
		&SkinUseMessage{}, "skin_use", 12, 0, 0, 0)
}

type SkinUseMessage struct {
	helper.BaseMessage
}

func (m *SkinUseMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("skin_id", log.Param10)
	r.SetTrack("hero_sys_id", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
