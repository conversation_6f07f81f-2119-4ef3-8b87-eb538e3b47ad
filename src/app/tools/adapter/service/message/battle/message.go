package battle

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FIGHT_REPORT),
		&BattleReportMessage{}, "battle_report", 13, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_FIGHT_REPORT_DETAILS),
		&BattleReportDetailsMessage{}, "battle_round", 13, 0, 0, 0)
}

// 嘉年华-领取奖励
type BattleReportMessage struct {
	helper.BaseMessage
}

func (m *BattleReportMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("report_id", log.Param1)
	r.SetTrack("win", log.Param10)
	r.SetTrack("func_id", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type BattleReportDetailsMessage struct {
	helper.BaseMessage
}

func (m *BattleReportDetailsMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("report_id", log.Param1)
	r.SetTrack("win", helper.FormatBool(log.Param10))
	r.SetTrack("index", log.Param11)
	r.SetTrack("battle_mode", log.Param3)
	r.SetFormationDetailsMessage(log.Param2)

	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
