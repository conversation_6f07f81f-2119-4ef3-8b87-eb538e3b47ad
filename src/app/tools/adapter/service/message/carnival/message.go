package carnival

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_CARNIVAL_RECEIVE_AWARD),
		&CarnivalReceiveAwardMessage{}, "carnival_receive_award", 13, 0, 0, 0)
}

// 嘉年华-领取奖励
type CarnivalReceiveAwardMessage struct {
	helper.BaseMessage
}

func (m *CarnivalReceiveAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("carnival_task_ids", log.Param1)                  // 嘉年华任务id
	r.SetTrack("carnival_id", log.Param10)                       // 嘉年华类型 7日/14日
	r.SetTrack("carnival_add_points", log.Param11)               // 嘉年华增加积分
	r.SetTrack("carnival_total_points", log.Param11+log.Param12) // 嘉年华类型，对应的总积分
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
