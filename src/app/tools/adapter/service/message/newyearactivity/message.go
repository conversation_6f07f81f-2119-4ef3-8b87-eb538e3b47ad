package newyearactivity

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_NEW_YEAR_ACTIVITY_STORY_LOGIN_AWARD),
		&NewYearActivityLoginAwardMessage{}, "new_year_activity_login_award", 14, 0, 0, 0)
}

type NewYearActivityLoginAwardMessage struct {
	helper.BaseMessage
}

func (m *NewYearActivityLoginAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("new_year_activity_act_id", log.Param10) // 活动ID
	if log.Param1 != "" {
		loginIds := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("new_year_activity_login_id", loginIds)
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
