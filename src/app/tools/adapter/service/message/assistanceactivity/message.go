package assistanceactivity

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_ACTIVITY_STORY_LOGIN_AWARD),
		&AssistanceActivityRecvAward{}, "assistance_activity_recv_award", 14, 0, 0, 0)
}

type AssistanceActivityRecvAward struct {
	helper.BaseMessage
}

func (m *AssistanceActivityRecvAward) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("assistance_activity_act_id", log.Param10) // 活动ID
	if log.Param1 != "" {
		recvIds := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("assistance_activity_recv_id", recvIds) // 领奖ID
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
