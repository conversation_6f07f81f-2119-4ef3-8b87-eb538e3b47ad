package dispatch

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"strconv"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DISPATCH_RECEIVE_TASK),
		&DispatchReceiveTaskMessage{}, "dispatch_receive_task", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DISPATCH_RECEIVE_AWARD),
		&DispatchReceiveAwardMessage{}, "dispatch_receive_award", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DISPATCH_REFRESH_TASK),
		&DispatchRefreshTaskMessage{}, "dispatch_refresh_task", 10, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DISPATCH_LEVEL_UP),
		&DispatchLevelUpMessage{}, "dispatch_level_update", 13, 0, 0, 0)
}

// 接取任务
type DispatchReceiveTaskMessage struct {
	helper.BaseMessage
}

func (m *DispatchReceiveTaskMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	//r.SetTrack("receive_hero_ids", log.Param1)     // 接取任务需要的英雄列表 - 废弃
	if log.Param1 != "" {
		heroIDs := helper.Int64SliceToStringSlice(log.Param1)
		r.SetTrack("receive_task_hero_ids", heroIDs) // 接取任务需要的英雄列表
	}
	r.SetTrack("receive_dispatch_id_str", strconv.FormatUint(log.Param10, 10)) // 接取的任务id
	r.SetTrack("receive_dispatch_level", log.Param11)                          // 接取任务的悬赏等级
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 领取奖励
type DispatchReceiveAwardMessage struct {
	helper.BaseMessage
}

func (m *DispatchReceiveAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	taskIDs := helper.Int64SliceToStringSlice(log.Param1)
	r.SetTrack("receive_award_task_ids", taskIDs) // 领取奖励的任务ID
	sysIDs := helper.Int64SliceToStringSlice(log.Param2)
	r.SetTrack("dispatch_sys_ids", sysIDs)                         // 派遣任务sysID
	r.SetTrack("is_speed", helper.FormatBool(log.Param10))         // 是否钻石加速
	r.SetTrack("dispatch_one_key", helper.FormatBool(log.Param11)) // 是否一键
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

// 刷新任务
type DispatchRefreshTaskMessage struct {
	helper.BaseMessage
}

func (m *DispatchRefreshTaskMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	//r.SetTrack("dispatch_tasks", log.Param1) // 刷新后的任务列表 - 废弃
	if log.Param1 != "" {
		taskIDs := helper.Int64SliceToStringSlice(log.Param1)
		r.SetTrack("dispatch_refresh_tasks", taskIDs) // 刷新后的任务ID
	}
	r.SetTrack("dispatch_refresh_task_level", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type DispatchLevelUpMessage struct {
	helper.BaseMessage
}

func (m *DispatchLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	r.SetTrack("dispatch_refresh_level", log.Param11)

	ret := r.Write(log)
	return ret
}
