package talenttree

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALENT_TREE_LEVEL_UP),
		&TalentTreeLevelUpMessage{}, "talent_tree_level_up", 12, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALENT_TREE_RESET),
		&TalentTreeResetMessage{}, "talent_tree_reset", 11, 0, 0, 0)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALENT_TREE_RECEIVE_TASK_AWARDS),
		&TalentTreeReceiveTaskAwardsMessage{}, "talent_tree_receive_task_awards", 10, 0, 0, 0)
}

type TalentTreeLevelUpMessage struct {
	helper.BaseMessage
}

func (m *TalentTreeLevelUpMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("node_id", log.Param10)
	r.SetTrack("node_level_before", log.Param11)
	r.SetTrack("node_level_after", log.Param12)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type TalentTreeResetMessage struct {
	helper.BaseMessage
}

func (m *TalentTreeResetMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("talent_tree_reset_times_before", log.Param10)
	r.SetTrack("talent_tree_reset_times_after", log.Param11)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type TalentTreeReceiveTaskAwardsMessage struct {
	helper.BaseMessage
}

func (m *TalentTreeReceiveTaskAwardsMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("talent_tree_receive_task_ids", log.Param1)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
