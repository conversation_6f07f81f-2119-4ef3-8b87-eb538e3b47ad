package seasondoor

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"
	"encoding/json"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_DOOR_TASK_REWARD),
		&SeasonDoorTaskMessage{}, "season_door_task", 15, 0, 0, 1)
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_DOOR_FIGHT),
		&SeasonDoorFightMessage{}, "season_door_fight", 15, 0, 0, 1)
}

type SeasonDoorTaskMessage struct {
	helper.BaseMessage
}

func (m *SeasonDoorTaskMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogSeasonDoorTaskReward
	err := json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[LogSeasonDoorTaskReward] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetTrack("task_id", logData.TaskId)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}

type SeasonDoorFightMessage struct {
	helper.BaseMessage
}

func (m *SeasonDoorFightMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)

	var logData *sub.LogSeasonDoorFight
	err := json.Unmarshal([]byte(log.Param2), &logData)
	if err != nil {
		l4g.Errorf("[LogSeasonDoorFight] json unmarshal error: %s", err.Error())
		return false
	}
	r.SetFormationMessageByIndex(log.Param1, 0)
	r.SetTrack("monster_id", logData.MonsterId)
	r.SetTrack("battle_report_id", logData.BattleReportId)
	r.SetTrack("door_type", logData.DoorType)
	r.SetTrack("door_level", logData.DoorLevel)
	r.SetTrack("door_num", logData.DoorNum)
	r.SetTrack("win", logData.Win)
	r.SetTrack("fight_time", logData.FightTime)
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
