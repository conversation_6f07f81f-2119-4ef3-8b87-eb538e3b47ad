package medal

import (
	"adapter/service/helper"
	sub "adapter/service/proto/log"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_MEDAL_RECEIVE_AWARD),
		&MedalReceiveAwardMessage{}, "medal_receive_award", 12, 0, 0, 0)
}

// 功勋-领取奖励
type MedalReceiveAwardMessage struct {
	helper.BaseMessage
}

func (m *MedalReceiveAwardMessage) Execute(log *da.Log) bool {
	r := m.GetRecorder()
	r.GetBaseMessage(log)
	r.SetTrack("medal_op_type", log.Param10) // 领奖类型
	r.SetTrack("medal_level", log.Param11)   // 功勋等级
	if log.Param1 != "" {
		taskIDs := helper.Int32SliceToStringSlice(log.Param1)
		r.SetTrack("medal_receive_task_ids", taskIDs) // 任务id
	}
	ret := r.Write(log)
	m.PutRecorder(r)
	return ret
}
