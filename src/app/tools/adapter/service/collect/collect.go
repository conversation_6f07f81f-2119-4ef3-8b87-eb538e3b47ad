package collect

import (
	"bytes"
	"errors"
	"fmt"
	"os"
	"sync"

	"gitlab.qdream.com/kit/sea/time"

	"gitlab.qdream.com/kit/library/sea/ctx"

	l4g "github.com/ivanabc/log4go"
)

// 对方传给我的消息
type Message struct {
	ServerID uint64
	Date     string
	Filename string
	Data     []byte
}

type Collect struct {
	group   *ctx.Group
	ch      chan *Message
	pool    sync.Pool
	logPath string

	buf *bytes.Buffer

	//wServerID uint64
	wDate     string
	wFilename string

	openFiles map[string]*OpenFile
}

type OpenFile struct {
	time int64
	file *os.File
}

func (of *OpenFile) Write(p []byte) (int, error) {
	of.time = time.Now().Unix()
	return of.file.Write(p)
}

const maxBufferSize = 8192

func NewCollect(group *ctx.Group, chanSize int, logPath string, partitionIndex int32) *Collect {
	lc := &Collect{
		group:     group,
		ch:        make(chan *Message, chanSize),
		buf:       bytes.NewBuffer(make([]byte, 0, maxBufferSize)),
		openFiles: make(map[string]*OpenFile),
		logPath:   logPath,
	}
	lc.pool.New = func() interface{} {
		return &Message{}
	}
	go lc.run(group.CreateChild(), partitionIndex)
	return lc
}

func (l *Collect) NewMessage() *Message {
	return l.pool.Get().(*Message)
}

func (l *Collect) Write(msg *Message) bool {
	l.ch <- msg
	return true
}

func (l *Collect) WriteAll(datas []*Message) bool {
	for _, v := range datas {
		l4g.Debugf("[test] writeAll.v serverID:%d date:%s fileName:%s data:%s",
			v.ServerID, v.Date, v.Filename, string(v.Data))
		l.ch <- v
	}
	return true
}

func (l *Collect) run(group *ctx.Group, partitionIndex int32) {
	defer group.Finish()

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	closeFileTicker := time.NewTicker(10 * time.Minute)
	defer closeFileTicker.Stop()

	for {
		select {
		case msg := <-l.ch:
			err := l.write(msg)
			if err != nil {
				l4g.Errorf("(%+v) write msg failed, error: %s", msg, err)
				ctx.Stop()
				return
			}
			l4g.Debugf("[test] write success msg serverID:%d date:%s fileName:%s data:%s",
				msg.ServerID, msg.Date, msg.Filename, string(msg.Data))
			l.recycle(msg)
		case <-ticker.C:
			err := l.flush()
			if err != nil {
				l4g.Errorf("flush buffer failed, error: %s", err)
				ctx.Stop()
				return
			}
		case <-closeFileTicker.C:
			l.CheckCloseFile()
		case <-group.Done():
			return
		}
	}
}

func (l *Collect) write(msg *Message) error {
	//if msg.ServerID != l.wServerID || msg.Date != l.wDate || msg.Filename != l.wFilename {
	if msg.Date != l.wDate || msg.Filename != l.wFilename {
		err := l.flush()
		if err != nil {
			l4g.Errorf("(%+v) flush msg failed, error: %s", msg, err)
			return err
		}
		//l.wServerID = msg.ServerID
		l.wDate = msg.Date
		l.wFilename = msg.Filename
	}

	_, err := l.buf.Write(msg.Data)
	if err != nil {
		l4g.Errorf("(%+v) write msg failed, error: %s", msg, err)
		return err
	}

	if l.buf.Len() >= maxBufferSize {
		err := l.flush()
		if err != nil {
			l4g.Errorf("(%+v) flush msg failed, error: %s", msg, err)
			return err
		}
	}

	return nil
}

func (l *Collect) flush() error {
	if l.buf.Len() > 0 {
		dir := fmt.Sprintf("%s/%s", l.logPath, l.wDate)
		file, err := l.OpenFile(l.wFilename, dir)
		if err != nil {
			return err
		}
		_, err = l.buf.WriteTo(file)
		if err != nil {
			return err
		}
	}
	return nil
}

func (l *Collect) Close() {
	l.group.Stop()
	l.group.Wait()

	for more := true; more; {
		select {
		case msg := <-l.ch:
			err := l.write(msg)
			if err != nil {
				l4g.Errorf("(%+v) json marshal failed, error: %s", msg, err)
			}
		default:
			more = false
		}
	}
	err := l.flush()
	if err != nil {
		l4g.Errorf("json flush failed, error: %s", err)
	}

	for _, v := range l.openFiles {
		v.file.Close()
	}

	l.group.Finish()
	l4g.Infof("log collect close...")
}

func (l *Collect) clearMessage(msg *Message) {
	msg.ServerID = 0
	msg.Date = ""
	msg.Filename = ""
	msg.Data = nil
}

func (l *Collect) recycle(msg *Message) {
	l.clearMessage(msg)
	l.pool.Put(msg)
}

func (l *Collect) OpenFile(filename, dir string) (*OpenFile, error) {
	fname := fmt.Sprintf("%s/%s", filename, dir)
	if f, exist := l.openFiles[fname]; exist {
		return f, nil
	}
	fh, err := mustOpen(filename, dir)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("failed to open file %s: %s", fname, err))
	}
	of := &OpenFile{
		time: time.Now().Unix(),
		file: fh,
	}
	l.openFiles[fname] = of
	return of, nil
}

func (l *Collect) CheckCloseFile() {
	now := time.Now().Unix()
	for k, v := range l.openFiles {
		if now-v.time >= int64(time.Minute)*10 {
			v.file.Close()
			delete(l.openFiles, k)
		}
	}
}

// 判断文件是否存在。只判断文件，目录不算，一般用来检查某个默认配置文件是否存在
func fileExists(filename string) bool {
	info, err := os.Stat(filename)
	return err == nil && !info.IsDir()
}

// 打开文件。如果文件不存在就创建
func mustOpen(fileName, dir string) (*os.File, error) {
	perm := checkPermission(dir)
	if perm == true {
		return nil, fmt.Errorf("permission denied dir: %s", dir)
	}

	err := isNotExistMkDir(dir)
	if err != nil {
		return nil, fmt.Errorf("error during make dir %s, err: %s", dir, err)
	}

	f, err := os.OpenFile(dir+string(os.PathSeparator)+fileName, os.O_APPEND|os.O_CREATE|os.O_RDWR, 0644)
	if err != nil {
		return nil, fmt.Errorf("fail to open file, err: %s", err)
	}

	return f, nil
}

func checkNotExist(src string) bool {
	_, err := os.Stat(src)
	return os.IsNotExist(err)
}

func checkPermission(src string) bool {
	_, err := os.Stat(src)
	return os.IsPermission(err)
}

func isNotExistMkDir(src string) error {
	if notExist := checkNotExist(src); notExist == true {
		if err := mkDir(src); err != nil {
			return err
		}
	}
	return nil
}

func mkDir(src string) error {
	err := os.MkdirAll(src, os.ModePerm)
	if err != nil {
		return err
	}

	return nil
}
