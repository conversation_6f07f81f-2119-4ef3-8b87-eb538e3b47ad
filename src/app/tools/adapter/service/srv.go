package service

import (
	"flag"

	"github.com/spf13/viper"

	"adapter/service/config"
	gravitylogger "adapter/service/gravity_logger"
	"adapter/service/kafka_consumer"
	tlogger "adapter/service/logger"
	qmlogger "adapter/service/qm_logger"
	wlogger "adapter/service/wm_logger"

	"github.com/gin-gonic/gin"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/library/micro/restful"
	"gitlab.qdream.com/kit/library/sea/ctx"
)

var exec = flag.String("exec", "ta_log_adapter", "数数日志转换")

type Service struct {
	group    *ctx.Group
	consumer *kafka_consumer.Manager
}

func New(group *ctx.Group) restful.Servicer {
	return &Service{
		group:    group,
		consumer: kafka_consumer.NewManager(group.CreateChild(), config.NewKafka(*exec)),
	}
}

func (s *Service) Register(r *gin.Engine) {
}

func (s *Service) Run() {
	config.LoadArea()
	l4g.Info("area is %s", config.GArea)
	err := s.consumer.Init()
	if err != nil {
		l4g.Error("kafka consumer init failed: %s", err)
		return
	}

	if *exec == "ta_log_adapter" {
		s.consumer.Run(tlogger.GetLogger(viper.GetString("mode")))
	} else if *exec == "wm_log_adapter" {
		s.consumer.Run(wlogger.GetLogger(viper.GetString("mode")))
	} else if *exec == "qm_log_adapter" {
		s.consumer.Run(qmlogger.GetLogger(viper.GetString("mode")))
	} else if *exec == "gravity_log_adapter" {
		s.consumer.Run(gravitylogger.GetLogger(viper.GetString("mode")))
	} else {
		l4g.Error("kafka consumer run failed, err target:%s", *exec)
		return
	}
}

func (s *Service) Close() {
	s.group.Stop()
	s.consumer.Close()

	s.group.Wait()
	l4g.Info("service close...")
}
