package qmlogger

import (
	"adapter/service/config"
	"adapter/service/helper"
	"adapter/service/kafka_consumer"

	"github.com/Shopify/sarama"
	"gitlab.qdream.com/kit/library/sea/ctx"
	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

type Logger struct {
	topic     string
	partition int32
	group     *ctx.Group

	cfg *config.QmConfig

	msgMgr *MessageM

	log *da.Log
}

func NewLogger() *Logger {
	logger := &Logger{cfg: config.NewQmConfig()}
	logger.log = &da.Log{}
	return logger
}

func (l *Logger) Init(topic string, partition int32, group *ctx.Group) {
	l.topic = topic
	l.partition = partition
	l.group = group

	//dir := fmt.Sprintf("%s/%d/", l.cfg.LogDir, partition)

	l.msgMgr = NewMessageM(l.group.CreateChild(), l.cfg, l.partition)
	l.RegisterMessages(l.msgMgr)
}

func (l *Logger) Close() {
	l.group.Stop()
	l.CheckReport(true)
	l.msgMgr.Close()

	l.group.Wait()
	l.group.Finish()
	l4g.Info("Logger topic:%s, partition:%d closed", l.topic, l.partition)
}

func clearLog(msg *da.Log) {
	msg.Param1 = ""
	msg.Param2 = ""
	msg.Param3 = ""
	msg.Param4 = ""
	msg.Param5 = ""
	msg.Param6 = ""
	msg.Param7 = ""
	msg.Param8 = ""
	msg.Param9 = ""
	msg.Param10 = 0
	msg.Param11 = 0
	msg.Param12 = 0
	msg.Param13 = 0
	msg.Param14 = 0
	msg.Param15 = 0
	msg.Param16 = 0
	msg.Param17 = 0
	msg.Param18 = 0
	msg.Param19 = 0
	msg.Param20 = 0
	msg.Extra = ""

}

func (l *Logger) Write(msg *sarama.ConsumerMessage) kafka_consumer.Status {
	defer func() {
		clearLog(l.log)
	}()
	if err := helper.Json.Unmarshal(msg.Value, l.log); err != nil {
		l4g.Error("[Logger] json unmarshal error:%v", err)
		return kafka_consumer.Success
	}
	if !l.msgMgr.Dispatcher(l) {
		l4g.Errorf("[Logger] write msg to collect error:%v", l)
		return kafka_consumer.Fail
	}

	return kafka_consumer.Success
}

func (l *Logger) Sync() kafka_consumer.Status {
	l.CheckReport(true)
	return kafka_consumer.Success
}

func (l *Logger) SyncQianMu() kafka_consumer.Status {
	l.CheckReport(true)
	return kafka_consumer.Success
}

// force： 是否强制执行上报
func (l *Logger) CheckReport(force bool) {
	needReport := false
	if force {
		if len(l.msgMgr.datas) > 0 {
			needReport = true
		}
	} else {
		if len(l.msgMgr.datas) >= int(l.cfg.LogLimit) {
			needReport = true
		}
	}

	if needReport {
		err := GameReport(l.cfg, l.msgMgr.datas)
		if err != nil {
			for _, data := range l.msgMgr.datas {
				collectMsg, err := l.msgMgr.GetMessage(data)
				if err != nil {
					l4g.Errorf("CheckReport: %v", err)
					continue
				}
				l.msgMgr.collect.Write(collectMsg)
			}
		}
		l.msgMgr.datas = l.msgMgr.datas[0:0]
	}
}

func (l *Logger) OnWrite(msg kafka_consumer.MsgRecorderI) bool {
	return true
}
