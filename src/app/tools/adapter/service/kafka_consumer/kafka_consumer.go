package kafka_consumer

import (
	"adapter/service/config"
	"errors"
	"strconv"
	"time"

	"github.com/Shopify/sarama"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/library/metrics"
	"gitlab.qdream.com/kit/library/sea/ctx"
)

type Status int

const (
	None      Status = 0
	Fail      Status = 1
	InProcess Status = 2
	Success   Status = 3
)

type MsgRecorderI interface {
	GetUserID() string
	GetDistinctID() string
	GetEvent() string
	GetTracks() map[string]interface{}
	GetInits() map[string]interface{}
	GetAdds() map[string]interface{}
	GetAfters() map[string]interface{}
}

type MessageHandler interface {
	Write(*sarama.ConsumerMessage) Status
	OnWrite(MsgRecorderI) bool
	Sync() Status
	SyncQianMu() Status // 给千目adapter用来定时上报日志
	Init(string, int32, *ctx.Group)
	Close()
}

type MessageHandlerCreator func() MessageHandler

type consumerGroupHandler struct {
	creator MessageHandlerCreator
	cfg     *config.Kafka
	group   *ctx.Group
}

func newConsumerGroupHandler(creator MessageHandlerCreator, cfg *config.Kafka, group *ctx.Group) *consumerGroupHandler {
	l4g.Debug("new consumer group handler")
	return &consumerGroupHandler{
		creator: creator,
		cfg:     cfg,
		group:   group,
	}
}

func (c *consumerGroupHandler) Setup(ses sarama.ConsumerGroupSession) error {
	l4g.Info("setup consumer group: %+v", ses.Claims())
	return nil
}

func (c *consumerGroupHandler) Cleanup(ses sarama.ConsumerGroupSession) error {
	l4g.Info("cleanup consumer group: %+v", ses.Claims())
	return nil
}

func (c *consumerGroupHandler) ConsumeClaim(ses sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	var (
		lastMessage    *sarama.ConsumerMessage
		status         Status
		messageHandler = c.creator()
		ticker         = time.NewTicker(c.cfg.ConsumerTicker)
		serviceName    = metrics.GetName()
		topic          = claim.Topic()
		partition      = strconv.Itoa(int(claim.Partition()))
	)

	messageHandler.Init(claim.Topic(), claim.Partition(), c.group.CreateChild())

	defer func() {
		if status == InProcess && lastMessage != nil {
			if status = messageHandler.Sync(); status == Success {
				ses.MarkMessage(lastMessage, "")
			}
		}
		ticker.Stop()
		messageHandler.Close()
		l4g.Info("consume claim (%s:%d) finish...", claim.Topic(), claim.Partition())
	}()

	for {
		select {
		case msg := <-claim.Messages():
			if msg == nil {
				return nil
			}
			//l4g.Debug("recv msg, topic: %s partition: %d offset: %d", msg.Topic, msg.Partition, msg.Offset)
			status = messageHandler.Write(msg)
			switch status {
			case Success:
				ses.MarkMessage(msg, "")
				lastMessage = nil
			case InProcess:
				lastMessage = msg
			}

			metrics.GetCounter("adapter_msg_count",
				metrics.MetricVariableLabels("name", "topic", "partition")).
				With(serviceName, topic, partition).Inc()
			metrics.GetGauge("adapter_msg_offset",
				metrics.MetricVariableLabels("name", "topic", "partition")).
				With(serviceName, topic, partition).Set(float64(msg.Offset))
		case <-ticker.C:
			if lastMessage != nil {
				if status = messageHandler.Sync(); status == Success {
					ses.MarkMessage(lastMessage, "")
				}
				lastMessage = nil
			}

			messageHandler.SyncQianMu()
		}
		if status == Fail {
			ctx.Stop()
			return errors.New("write msg fail")
		}
	}
}
