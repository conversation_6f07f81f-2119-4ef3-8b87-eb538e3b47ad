package kafka_consumer

import (
	"adapter/service/config"
	"context"
	"fmt"
	"strings"

	"github.com/Shopify/sarama"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/library/sea/ctx"
)

//var gldb *db.LevelDB

type IConsumer interface {
	Init()
	Process()
	Close()
}

type Manager struct {
	//partitionConsumers []IConsumer
	group *ctx.Group
	cfg   *config.Kafka

	consumerGroup sarama.ConsumerGroup
	cancel        context.CancelFunc
}

func NewManager(group *ctx.Group, cfg *config.Kafka) *Manager {
	return &Manager{
		group: group,
		cfg:   cfg,
	}
}

func (m *Manager) Init() error {
	//ldb, err := db.NewLevelDB(config.NewLevelDB())
	//if err != nil {
	//	l4g.Error("manager init leveldb error %s", err)
	//	return err
	//}
	//gldb = ldb

	config := sarama.NewConfig()
	config.Consumer.Return.Errors = true

	// Kafka version 0.10.2.0 is required for consumer groups.
	config.Version = sarama.V0_10_2_0

	if m.cfg.Version != "" {
		version, err := sarama.ParseKafkaVersion(m.cfg.Version)
		if err != nil {
			return err
		}

		config.Version = version
	}

	config.ClientID = m.cfg.ClientID
	switch strings.ToLower(m.cfg.Offset) {
	case "oldest", "":
		config.Consumer.Offsets.Initial = sarama.OffsetOldest
	case "newest":
		config.Consumer.Offsets.Initial = sarama.OffsetNewest
	default:
		return fmt.Errorf("invalid offset %q", m.cfg.Offset)
	}

	switch strings.ToLower(m.cfg.BalanceStrategy) {
	case "range", "":
		config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRange
	case "roundrobin":
		config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
	case "sticky":
		config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategySticky
	default:
		return fmt.Errorf("invalid balance strategy %q", m.cfg.BalanceStrategy)
	}
	var err error
	l4g.Debug("kafka brokers: %+v", m.cfg)
	m.consumerGroup, err = sarama.NewConsumerGroup(m.cfg.Brokers, m.cfg.ConsumerGroup, config)
	return err
}

func (m *Manager) Run(creator MessageHandlerCreator) {
	//index, maxPartition := m.cfg.StartPartition, m.cfg.EndPartition
	//for index <= maxPartition {
	//	for _, topic := range m.cfg.Topics {
	//		consumer := NewPartitionConsumer(m.group.CreateChild(), m.cfg, index, creator, topic)
	//		if !consumer.Init() {
	//			m.Close()
	//			return
	//		}
	//		go func() {
	//			defer func() {
	//				consumer.Close() //肯定只会执行一次
	//				ctx.Stop()
	//			}()
	//			consumer.Process()
	//		}()
	//	}
	//	index++
	//}

	bgCtx, cancel := context.WithCancel(context.Background())
	m.cancel = cancel

	c1 := m.group.CreateChild()
	go func() {
		defer func() {
			ctx.Stop()
			c1.Finish()
			l4g.Info("consume goruntime finish...")
		}()

		l4g.Info("consume start...")
		for bgCtx.Err() == nil {
			handler := newConsumerGroupHandler(creator, m.cfg, c1)
			err := m.consumerGroup.Consume(bgCtx, m.cfg.Topics, handler)
			if err != nil {
				l4g.Error("consume topics(%+v) failed: %s", m.cfg.Topics, err)
				break
			}
		}

		if err := m.consumerGroup.Close(); err != nil {
			l4g.Error("consume close failed: %s", err)
		}
	}()

	c2 := m.group.CreateChild()
	go func() {
		defer func() {
			c2.Finish()
			l4g.Info("consume errors goruntime finish...")
		}()
		for err := range m.consumerGroup.Errors() {
			l4g.Error("consume failed: %s", err)
		}
	}()
}

// 阻塞操作
// 本身这个管理器是非阻塞的，那么需要提供这样一个Close的关闭资源的服务
// 他的所有的子服务是阻塞的还是非阻塞的（也就是是不是死循环）。
// 是死循环的话不需要额外的close，不是死循环的需要额外的close
// 保证close只会执行一次 这个只会在main里面执行
func (m *Manager) Close() {
	l4g.Info("kafka consumer manager closing")
	m.cancel()
	m.group.Stop() //这个执行了就会执行所有的close
	m.group.Wait()
	m.group.Finish()
	//gldb.Close()
	l4g.Info("kafka consumer manager close finish")
}

type PcConfig struct {
	topic string
}

type PartitionConsumer struct {
	group          *ctx.Group
	topic          string
	target         string
	partitionIndex int32 //分区编号
	offset         int64 //当前已经消费完的编号

	leveldbKey []byte

	cfg *config.Kafka

	consumerClient    sarama.Client
	consumer          sarama.Consumer
	partitionConsumer sarama.PartitionConsumer

	creator MessageHandlerCreator
}

/*func NewPartitionConsumer(group *ctx.Group, cfg *config.Kafka, partitionIndex int32, creator MessageHandlerCreator, topic string) *PartitionConsumer {
	return &PartitionConsumer{
		group:          group,
		partitionIndex: partitionIndex,
		topic:          topic,
		target:         cfg.Target,
		cfg:            cfg,
		creator:        creator,
	}
}

func (pc *PartitionConsumer) Init() bool {
	pc.leveldbKey = []byte(fmt.Sprintf("%s_%s_%d", pc.target, pc.topic, pc.partitionIndex))

	config := sarama.NewConfig()
	config.Consumer.Fetch.Default = pc.cfg.ConsumerFetchSize
	config.Consumer.Fetch.Min = pc.cfg.ConsumerFetchMinSize
	config.Net.ReadTimeout = time.Duration(pc.cfg.ConsumerNetReadTimeout) * time.Minute
	config.Consumer.MaxProcessingTime = time.Second
	config.Consumer.Return.Errors = true
	consumerClient, cErr := sarama.NewClient(pc.cfg.Brokers, config)
	if cErr != nil {
		l4g.Error("new client error: %s %d %v %s", pc.topic, pc.partitionIndex, pc.cfg.Brokers, cErr.Error())
		return false
	}
	pc.consumerClient = consumerClient

	consumer, err := sarama.NewConsumerFromClient(consumerClient)
	if err != nil {
		l4g.Error("new consumer %s %d error: %s", pc.topic, pc.partitionIndex, err.Error())
		return false
	}
	pc.consumer = consumer

	//get right consume offset
	offset, err := pc.GetLevelDBOffset()
	if err != nil {
		l4g.Error("get leveldb key error: %s %s", pc.leveldbKey, err.Error())
		return false
	}

	if offset != sarama.OffsetNewest {
		offset += 1
	}
	l4g.Info("consume offset: %s %s %d %d", pc.topic, pc.leveldbKey, pc.partitionIndex, offset)

	//init partition consumer
	partitionConsumer, err := consumer.ConsumePartition(pc.topic, pc.partitionIndex, offset)
	// partitionConsumer, err := consumer.ConsumePartition(pc.topic, pc.partitionIndex, sarama.OffsetOldest)
	if err != nil {
		l4g.Error("new consume partition (%s %d) error: %s", pc.topic, pc.partitionIndex, err.Error())
		return false
	}
	pc.partitionConsumer = partitionConsumer
	return true
}

func (pc *PartitionConsumer) GetLevelDBOffset() (int64, error) {
	ret, err := gldb.Get(pc.leveldbKey)
	if err != nil {
		return 0, err
	}

	var offset int64
	if len(ret) > 0 {
		offset, _ = strconv.ParseInt(string(ret), 10, 64)
	} else {
		offset = sarama.OffsetNewest
	}
	return offset, nil
}

func (pc *PartitionConsumer) Process() {
	defer pc.group.Finish()
	//pc.AddCollect(pc.config.LogType)

	var (
		lastMessage *sarama.ConsumerMessage
		status      Status
	)

	messageHandler := pc.creator()
	messageHandler.Init(pc.topic, pc.partitionIndex, pc.group.CreateChild())

	ticker := time.NewTicker(time.Minute)
	offset, _ := pc.GetLevelDBOffset()
	lastOffset, newOffset := offset, offset

	defer func() {
		ticker.Stop()
		messageHandler.Close()
	}()

	offset1, offset2 := pc.GetKafkaOffset()
	l4g.Info("partition consumer(%s %d), db offset:%d oldestoffset %d, newest offset %d", pc.topic, pc.partitionIndex, offset, offset1, offset2)

	l4g.Info("log partion:%d start", pc.partitionIndex)

	for {
		select {
		case <-pc.group.Done():
			l4g.Info("manager kafka close %d", pc.partitionIndex)
			return
		case <-ticker.C:
			l4g.Info("consume offset: topic:%s partition:%d newOffset:%d counsumerCount:%d", pc.topic, pc.partitionIndex, newOffset, newOffset-lastOffset)
			lastOffset = newOffset
			if lastMessage != nil {
				if status = messageHandler.Sync(); status == Success {
					newOffset = lastMessage.Offset
					pc.SetOffset(lastMessage.Offset)
				}
				lastMessage = nil
			}
		case err := <-pc.partitionConsumer.Errors():
			if kerr, ok := err.Err.(sarama.KError); ok && kerr == sarama.ErrOffsetOutOfRange {
				oldestOffset, _ := pc.GetKafkaOffset()
				l4g.Error("partition consumer (%s %d) return error: %s %d %d", pc.topic, pc.partitionIndex, err.Error(), newOffset, oldestOffset)
			} else {
				l4g.Error("partition consumer (%s %d) return error: %s", pc.topic, pc.partitionIndex, err.Error())
			}
			return
		case msg := <-pc.partitionConsumer.Messages():
			l4g.Debug("get msg from kafka is topic:%s, partion:%d, offset:%d key:%s value:%s", msg.Topic, msg.Partition, msg.Offset, msg.Key, msg.Value)
			status = messageHandler.Write(msg)
			switch status {
			case Success:
				newOffset = msg.Offset
				pc.SetOffset(msg.Offset)
				lastMessage = nil
			case InProcess:
				lastMessage = msg
			}
		}
		if status == Fail {
			l4g.Error("write msg fail")
			return
		}
	}

}

func (pc *PartitionConsumer) Close() {
	if pc.partitionConsumer != nil {
		if errs := pc.partitionConsumer.Close(); errs != nil {
			for _, err := range errs.(sarama.ConsumerErrors) {
				l4g.Error("close partition consumer(%s %d) error: %s", pc.topic, pc.partitionIndex, err.Error())
			}
		}
		l4g.Info("partition consumer close: %s %d", pc.topic, pc.partitionIndex)
	}

	if pc.consumer != nil {
		if err := pc.consumer.Close(); err != nil {
			l4g.Error("close consumer (%s %d) error: %s", pc.topic, pc.partitionIndex, err.Error())
		}
		l4g.Info("consumer close: %s %d", pc.topic, pc.partitionIndex)
	}

	if pc.consumerClient != nil {
		if err := pc.consumerClient.Close(); err != nil {
			l4g.Error("close client error: %s %d %s", pc.topic, pc.partitionIndex, err.Error())
			l4g.Info("consumer client close: %s %d", pc.topic, pc.partitionIndex)
		}
	}
}

func (pc *PartitionConsumer) GetKafkaOffset() (int64, int64) {
	oldestOffset, _ := pc.consumerClient.GetOffset(pc.topic, pc.partitionIndex, sarama.OffsetOldest)
	newestOffset, _ := pc.consumerClient.GetOffset(pc.topic, pc.partitionIndex, sarama.OffsetNewest)
	return oldestOffset, newestOffset
}

func (pc *PartitionConsumer) SetOffset(offset int64) {
	pc.offset = offset
	l4g.Info("synclog set Offset %d %d", pc.partitionIndex, offset)
	if err := gldb.Put(pc.leveldbKey, []byte(strconv.FormatInt(offset, 10))); err != nil {
		l4g.Error("leveldb put %s %d error: %s", pc.leveldbKey, offset, err.Error())
		ctx.Stop()
	}
}*/
