#!/bin/bash

ulimit -c unlimited

USER_NAME=$(whoami)

if [ $# -ge 2 ]; then
  vars=($*)
  CMD="./$2"
  name=$2
  for var in ${vars[@]:2}; do
    CMD=$CMD" "
    CMD=$CMD$var
  done
else
  echo "para error $# $@"
  exit 1
fi

NORMAL_COLOR="\033[44;37m"
SUCCESS_COLOR="\033[42;37m"
FAILED_COLOR="\033[41;37m"
WARN_COLOR="\033[43;37m"
END_COLOR="\033[0m"
RESULT_COLOR="\033[47;31m"

echo -e "$NORMAL_COLOR $* $END_COLOR" 

if [ ! -d "./logs" ]; then
  mkdir ./logs
fi

ERR="logs/$2_error.log"
CUR_DATE=$(date +%Y%m%d%H%M)
svr_pids=()

check_service_exist() {
  svr_pids=()
  pid=`ps -ef | grep "$USER_NAME" | grep "$CMD" | grep -v grep | grep -v $0 | awk '{print $2}'`
  if [ "$pid" != "" ]; then
    svr_pids[$name]=$pid
  fi
}

stop_service() {
  check_service_exist
  processid=${svr_pids[$name]}
  if [ ${#svr_pids[@]} -gt 0 ]; then
    echo "stop \"${CMD}\" begin...process id: ${svr_pids[@]} $(date)%$(date +%s)" >>${ERR}
    kill ${svr_pids[@]}
  fi

  sleep 2

  check_service_exist
  while [ ${#svr_pids[@]} -gt 0 ]; do
    echo "stop \"${CMD}\" wait...process id: ${svr_pids[@]} $(date)%$(date +%s)" >>${ERR}
    check_service_exist
    sleep 1
  done

  info="stop \"${CMD}\" success...$(date)%$(date +%s)"
  echo ${info} >>${ERR}
  echo -e "\033[32m stop \"${CMD}\" success... process id: ${processid} \033[0m"
}

start_service() {
  check_service_exist
  if [ ${#svr_pids[@]} -lt 1 ]; then
	if [ -f ${ERR} ]; then
		mv ${ERR} "${ERR}_`date "+%Y-%m-%d_%H:%M:%S"`"
	fi
    #eval "nohup $CMD -exec ta_log_adapter -level DEBUG > ${ERR} 2>&1 &"
    eval "nohup $CMD -exec wm_log_adapter >>${ERR} 2>>${ERR} & > /dev/null"
  else
    info="\"${CMD}\" has exist id: ${svr_pids[@]}"
    echo ${info} >>${ERR}
    echo -e "\033[31m ${info} \033[0m"
    exit 1
  fi

  check_service_exist
  if [ ${#svr_pids[@]} -gt 0 ]; then
    echo "executing \"${CMD}\"...process id: ${svr_pids[@]} $(date)%$(date +%s)" >>${ERR}
  else
    echo "executing \"${CMD}\"...fail $(date)%$(date +%s)" >>${ERR}
    echo -e "$FAILED_COLOR executing \"${CMD}\"...$END_COLOR$RESULT_COLOR fail $END_COLOR"
    exit 1
  fi
  echo -e "\033[32m start \"${CMD}\" success... process id: ${svr_pids[@]} \033[0m"
}

check_service() {
  check_service_exist
  if [ ${#svr_pids[@]} -lt 1 ]; then
    exit 1
  fi
  exit 0
}

case $1 in
start)
  start_service
  ;;
stop)
  stop_service
  ;;
restart)
  stop_service
  start_service
  ;;
check)
  check_service
  ;;
esac
