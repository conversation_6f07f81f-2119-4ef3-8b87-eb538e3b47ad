all: protos app
protos:
	mkdir -p ./service/proto
	mkdir -p ./service/proto/out
	cp -r ../../protos/in/db ./service/proto/
	cp -r ../../protos/in/log ./service/proto/
	cp -r ../../protos/out/bt ./service/proto/out/
	cp -r ../../protos/out/cl ./service/proto/out/
	cp -r ../../protos/out/common ./service/proto/out/
	find ./service/proto . -iname "*.go" |xargs sed -i 's/\"app\/protos/\"adapter\/service\/proto/g'
app:
	gofmt -w .
	GOOS=linux GOARCH=amd64 CGO_CFLAGS="-I$(PWD)/deps/include" CGO_LDFLAGS="-L$(PWD)/deps/libs" go build -gcflags "-N -l"
	GOOS=linux GOARCH=amd64 CGO_CFLAGS="-I$(PWD)/deps/include" CGO_LDFLAGS="-L$(PWD)/deps/libs" go build -gcflags "-N -l" -o wm_adapter
