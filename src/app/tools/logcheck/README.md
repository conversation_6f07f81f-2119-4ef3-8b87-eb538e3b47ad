# logcheck介绍

### 用途

- 通过输入待检索的协议起止id，检查提示日志漏记情况

### 检索范围

- src/app/logic/command/
- src/app/tools/adapter/service/message/
- src/app/logic/character/log.go
- protos/in/log.proto

### 使用方法

- 使用示例，在项目根目录的tools目录下执行：

  ```shell
  ./logcheck -start=12400 -end=12414
  ```

- 参数解释：
  - start: cl.proto中，要检查的第一个协议id。默认值 `10000`

  - end: cl.proto中，要检查的最后一个协议id。默认值 `99999`

- 添加免检标签。有些协议是不需要检测的，在其C2L协议id后，添加关键字`NOLOG`即可

  - 以竞技场相关协议为例，添加方法如下：

    ```protobuf
    //竞技场
    MSG_MIN_Arena = 12400;
    MSG_C2L_ArenaInfo = 12401;  //竞技场信息 NOLOG
    MSG_L2C_ArenaInfo = 12402;
    MSG_C2L_ArenaRefresh = 12403;  //刷新对手
    MSG_L2C_ArenaRefresh = 12404;
    MSG_C2L_ArenaFight = 12405;  //战斗
    MSG_L2C_ArenaFight = 12406;
    MSG_C2L_ArenaLogList = 12407;  //战报信息 NOLOG
    MSG_L2C_ArenaLogList = 12408;
    MSG_C2L_ArenaLike = 12409;  //竞技场点赞
    MSG_L2C_ArenaLike = 12410;
    MSG_C2L_ArenaRank = 12411;  //竞技场排行榜 NOLOG
    MSG_L2C_ArenaRank = 12412;
    MSG_C2L_ArenaRecvAward = 12413;  //领取任务奖励
    MSG_L2C_ArenaRecvAward = 12414;
    ```
  
  - 编译logcheck，在项目根目录下执行：
  
    ```shell
    make logcheck
    ```
  
  - 再执行tools目录下的logcheck即可

### 设计思路简介

1. 根据用户输入的协议起止id，在cl.proto中找到对应的MSG_信息
2. 通过MSG_MIN_xxx，确认模块名packageName
   - 可以找到command文件：`src/app/logic/command/packageName/command.go`
   - 可以找到adapter文件：`src/app/tools/adapter/service/message/packageName/message.go`
3. 通过MSG_C2L_yyy，确认方法名funcName
4. 检查funcName，在command文件中是否存在
5. 将funcName转为FUNC_NAME，检查在相关文件中是否存在
   - protos/in/log.proto
   - src/app/logic/character/log.go
   - adapter messge.go文件
6. 把以上检查中，提示信息汇总输出

### 不足之处

- `src/app/logic/character/log.go`中的处理方法，与其所使用的LogSubType是否吻合，尚未做检查