package main

import (
	"flag"
	"fmt"
	"io/ioutil"
)

var (
	startMessageID = flag.Int("start", 10000, "检索的起始MSG编号，从MSG_MIN_XXX开始")
	endMessageID   = flag.Int("end", 99999, "检索的截止MSG编号")
	gProtoFile     = flag.String("proto", "../protos/out/cl.proto", "proto file")
	commandPath    = flag.String("cp", "../src/app/logic/command/", "command path")
	adapterPath    = flag.String("ap", "../src/app/tools/adapter/service/message/", "adapter path")
	logHandlerFile = flag.String("lhf", "../src/app/logic/character/log.go", "log handler file")
	logIDFile      = flag.String("lif", "../protos/in/log.proto", "log id file")
)

func showParams() {
	fmt.Printf("startMessageID: %d\n", *startMessageID)
	fmt.Printf("endMessageID: %d\n", *endMessageID)
	fmt.Printf("gProtoFile: %s\n", *gProtoFile)
	fmt.Printf("commandPath: %s\n", *commandPath)
	fmt.Printf("adapterPath: %s\n", *adapterPath)
	fmt.Printf("logHandlerFile: %s\n", *logHandlerFile)
	fmt.Printf("logIDFile: %s\n", *logIDFile)
}

func main() {
	flag.Parse()
	if *endMessageID < *startMessageID {
		panic(fmt.Sprintf("param end less than start, start:%d, end:%d", *startMessageID, *startMessageID))
	}
	// showParams()

	checkCount := *endMessageID - *startMessageID + 1
	tips := make([]string, 0, checkCount*2)
	var (
		errorTip string
		hasError bool
	)

	messages := ParseMessagesInFile(*gProtoFile)
	logHandlerFileContents := ReadFile(*logHandlerFile)
	logIDFileContents := ReadFile(*logIDFile)
	if len(logHandlerFileContents) == 0 || len(logIDFileContents) == 0 {
		panic(fmt.Sprintf("log.go:%s or log.proto:%s not exist", *logHandlerFile, *logIDFile))
	}

	for i := *startMessageID; i <= *endMessageID; i++ {
		message := messages[i]
		if message == nil {
			continue
		}

		//避免无用循环
		if i >= MaxMessageID {
			break
		}

		tips = append(tips, fmt.Sprintf("\x1b[32mchecking message: C2L_%s\x1b[0m", message.FuncName))
		//检索command文件
		commandDir := *commandPath + message.PackageName + "/"
		commandFiles, err := ioutil.ReadDir(commandDir)
		if err != nil {
			tips = append(tips, "\x1b[31mnot found commandDir: "+commandDir+" \x1b[0m")
			hasError = true
		} else {
			var (
				foundInCommand bool
				errors         []string
			)
			for _, file := range commandFiles {
				commandFile := *commandPath + message.PackageName + "/" + file.Name()
				commandFileContents := ReadFile(commandFile)
				errorTip = SearchFuncNameInContents(commandFileContents, commandFile, message.LogFuncName)
				if errorTip == "" {
					foundInCommand = true
					break
				}

				errors = append(errors, errorTip)
			}
			if !foundInCommand {
				tips = append(tips, errors...)
				hasError = true
			}
		}

		//检索 character/log.go
		errorTip = SearchFuncNameInContents(logHandlerFileContents, *logHandlerFile, message.LogIDName)
		if errorTip != "" {
			tips = append(tips, errorTip)
			hasError = true
		}

		//检索 protos/in/log.proto
		errorTip = SearchFuncNameInContents(logIDFileContents, *logIDFile, message.LogIDName)
		if errorTip != "" {
			tips = append(tips, errorTip)
			hasError = true
		}

		//检索adapter message文件
		adapterFile := *adapterPath + message.PackageName + "/" + "message.go"
		adapterFileContents := ReadFile(adapterFile)
		if len(adapterFileContents) == 0 {
			tips = append(tips, fmt.Sprintf("\x1b[41;36madapter message file not exist: %s\x1b[0m", adapterFile))
			hasError = true
		} else {
			errorTip = SearchFuncNameInContents(adapterFileContents, adapterFile, message.LogIDName)
			if errorTip != "" {
				tips = append(tips, errorTip)
				hasError = true
			}
		}
	}

	if !hasError {
		fmt.Printf("\x1b[32m检查通过，无异常情况 \x1b[0m\n")
		return
	}

	for _, tip := range tips {
		fmt.Println(tip)
	}
}
