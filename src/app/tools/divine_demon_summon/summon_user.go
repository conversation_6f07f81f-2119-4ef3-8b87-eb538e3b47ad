package main

/*
type Server struct {
	character.Servicer
	rd *rand.Rand
}

func (s *Server) Rand() *rand.Rand {
	return s.rd
}

func (s *Server) ServerDay(now int64) uint32 {
	return 8
}

func (s *Server) StartServiceTm() int64 {
	return time.Now().Unix() - 10
}

type UserDivineSummon struct {
	id     uint64
	data   *cl.DivineDemonSummon
	counts []uint32 //第n个卡用了多少次抽将次数
}

func NewUserDivineSummon(u *character.User) *UserDivineSummon {
	r := &UserDivineSummon{
		id:   u.ID(),
		data: &cl.DivineDemonSummon{},
	}
	return r
}

func (ds *UserDivineSummon) GetSummon() *character.DivineDemonPluralSummon {
	return (*character.DivineDemonPluralSummon)(ds.data)
}

func (ds *UserDivineSummon) GetSummonTotalCount() uint32 {
	return (*cl.DivineDemonSummon)(ds.data).GetSummonTotalCount()
}

type Activity struct {
	id uint32
}

func GetOpenActivity(sysID uint32) *gmxml.DivineDemonInfo {
	return &gmxml.DivineDemonInfo{
		Id:    0,
		SysID: sysID,
	}
}

func SummonByUser() {
	activityID := uint32(*gActID)
	curHero := uint32(*gActWishHeroID)

	userCount := *gUserCount
	if userCount > 100000 {
		userCount = 100000 //最多10W
	}
	maxCount := 20                                         //总共需要抽到的个数
	extraRandAwardBySummonCount := []uint32{120, 300, 500} //多少抽在随机一次
	extraAwardBySummonCount := []uint32{200, 400, 600}     //多少抽固定奖励一次

	fmt.Println(fmt.Sprintf("开始模拟活动:%d, 选中英雄:%d, 玩家数量:%d \n", activityID, curHero, userCount))

	srv := &Server{
		rd: rand.New(time.Now().UnixNano()),
	}
	for i := 1; i < userCount; i++ {
		user := character.NewUser(&session.Client{}, uint64(i), fmt.Sprintf("uuid:%d", i), 1, "")
		recv := &r2l.R2L_Login{
			User: &db.User{
				Id:   uint64(i),
				Base: &db.BaseAttr{},
				Module: &db.ModuleAttr{
					Achieve: &cl.AchieveInfo{},
				},
				Module1: &db.ModuleAttr1{},
			},
		}
		user.LoadLoginUser(recv, srv)
		summon := NewUserDivineSummon(user)
		for {
			ok := summonByUser(srv, user, summon, activityID, curHero)
			if !ok {
				l4g.Errorf("user: %d summon failed. ret: %d", user.ID())
				return
			}
			totalCount := summon.GetSummonTotalCount()
			for _, v := range extraRandAwardBySummonCount {
				if totalCount == v {
					if srv.Rand().SelectByTenTh(1667) {
						summon.counts = append(summon.counts, totalCount)
					}
					break
				}
			}

			for _, v := range extraAwardBySummonCount {
				if totalCount == v {
					summon.counts = append(summon.counts, totalCount)
					break
				}
			}
			if len(summon.counts) >= maxCount {
				logStr := fmt.Sprintf("user:%5d", user.ID())
				for _, v := range summon.counts {
					logStr += fmt.Sprintf(" %4d ", v)
				}

				l4g.Info("%s", logStr)
				fmt.Println(logStr)
				break
			}
		}
	}
}

func summonByUser(srv character.Servicer, user *character.User, userSummon *UserDivineSummon, sysID, curHero uint32) bool {
	summon := userSummon.GetSummon()
	openActivity := GetOpenActivity(sysID)
	demonInfo := goxml.GetData().DivineDemonInfoM.Index(openActivity.SysID)
	costType := uint32(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_DIAMOND)
	ret, awards, _, wishRecordCopy, useDiamondSummonCount := summon.RetSummonAward(srv, user, openActivity, demonInfo, curHero, costType)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: summon failed. ret: %d", user.ID(), ret)
		return false
	}

	nextScType, retCode := summon.GetNextScType(srv, user.ID())
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DivineDemonSummon: get next scType failed. ret: %d", user.ID(), retCode)
		return false
	}

	redHeroCount, wishHeroCount := gerRedCardCount(awards, curHero)
	summon.UpdateSummon(redHeroCount, wishHeroCount, nextScType, useDiamondSummonCount, wishRecordCopy)

	if wishHeroCount > 0 {
		for i := 0; i < int(wishHeroCount); i++ {
			userSummon.counts = append(userSummon.counts, userSummon.GetSummonTotalCount())
		}
	}
	return true
}

func gerRedCardCount(awards []*cl.Resource, curHero uint32) (uint32, uint32) {
	redCardCount := uint32(0)
	upRedCardCount := uint32(0)
	for _, award := range awards {
		if award.Type == uint32(common.RESOURCE_HERO) {
			heroInfo := goxml.GetData().HeroInfoM.Index(award.Value)
			if heroInfo == nil {
				l4g.Errorf("hero sysID not exist. sysID: %d", award.Value)
				continue
			}
			if heroInfo.Rare >= goxml.HeroRareTrueFive {
				redCardCount++
				if award.Value == curHero {
					upRedCardCount++
				}
			}
		}
	}
	return redCardCount, upRedCardCount
}
*/
