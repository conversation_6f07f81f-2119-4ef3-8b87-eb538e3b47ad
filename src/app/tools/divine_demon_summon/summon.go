package main

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"flag"
	"fmt"
	"strings"
	"time"

	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

var (
	xmlDir = "../data/"
)

type Data struct {
	loopNum       uint32
	redHeroCount  uint32
	wishHeroCount uint32
	summonInfos   []*SummonInfo
}

type SummonInfo struct {
	HeroID         uint32 //红卡ID
	GuaranteeCount uint32 //保底次数
	TotalCount     uint32 //抽卡总次数
	IsWish         bool   // 是否是心愿
}

const (
	LoopNum uint32 = 100000
	//ActivityType = load_xml.DivineDemonActivityTypeNewServer
)

var activityTypePool = map[uint32]string{
	1: "新服",
	2: "通服",
}

var oldHeroPool = []uint32{42001, 43002, 41003, 44004, 43005, 43006, 53001, 52002, 51003, 54004, 52005, 53006}

var newHeroPool = []uint32{43007, 42008, 44009, 43010, 53007, 51008, 52009, 53011, 53010, 53014, 41011, 43012, 53012, 52013, 43013}

var (
	gActID         = flag.Int("act_id", 10015, "活动Id")
	gActType       = flag.Int("act_type", 2, "活动类型.1新服活动2通服活动")
	gActWishHeroID = flag.Int("act_wish_hero", 43013, "心愿将id")
	gFunc          = flag.String("func", "", "模拟功能")
	gUserCount     = flag.Int("user_count", 10000, "user模拟的玩家数量")
	flagLogDir     = flag.String("divine_log_dir", "./", "service log dir")
	flagLogLevel   = flag.String("divine_log_level", "Info", "log level")
)

func main() {
	flag.Parse()

	logFilename := *flagLogDir + "divine.log"
	cfg := util.NewL4GConfig(*flagLogLevel, logFilename)
	cfg = strings.ReplaceAll(cfg, "10M", "50M") //日志文件大一点再生成新文件
	l4g.Global.LoadConfiguration("", []byte(cfg))
	defer l4g.Close()

	goxml.Load(xmlDir, false, false)

	if *gFunc == "" {
		SummonByTotal()
	} else {
		//SummonByUser()
	}
}

func SummonByTotal() {
	// 1. 创建 rand
	rd := rand.New(time.Now().UnixNano())

	loopNum := LoopNum
	activityID := uint32(*gActID)
	activityType := uint32(*gActType)
	curHero := uint32(*gActWishHeroID)

	data := &Data{
		loopNum:       loopNum,
		redHeroCount:  0,
		wishHeroCount: 0,
		summonInfos:   make([]*SummonInfo, 0),
	}

	demonInfo := goxml.GetData().DivineDemonInfoM.Index(activityID)
	if demonInfo == nil {
		l4g.Errorf("demonInfo not exist. activityID: %d", *gActID)
		return
	}

	v2Clone := &cl.DivineDemonSummonV2{
		ColorGuarantee: make([]uint32, goxml.DivineDemonMaxGuaranteeLen),
		NotUpHeroCount: goxml.GetData().DivineDemonConfigInfoM.UpHeroGuarantee - 1,
	}

	for i := uint32(0); i < loopNum; i++ {
		var summonResource []*cl.Resource
		for index := range v2Clone.ColorGuarantee {
			v2Clone.ColorGuarantee[index]++
		}
		groupId, _, red, gdGroupClass := goxml.GetData().DivineDemonGroupInfoM.RandomGroup(rd, v2Clone.ColorGuarantee, demonInfo.Id)

		if red {
			v2Clone.TotalRedCard++
		}

		v2Clone.NotUpHeroCount, summonResource, _ = goxml.GetData().SummonGroupInfoM.RandomClassDivineSummon(rd, groupId, gdGroupClass, v2Clone.NotUpHeroCount, red, curHero, v2Clone.TotalRedCard)

		addGuaranteeAfterSummon(v2Clone.ColorGuarantee, summonResource, data, i, curHero)
	}

	fmt.Println(fmt.Sprintf("活动 id: %d, 活动类型：%s, 卡池 id: %d", activityID, activityTypePool[activityType], curHero))
	fmt.Println("red_hero count: ", data.redHeroCount)
	fmt.Println(fmt.Sprintf("wish hero id: %d count: %d", curHero, data.wishHeroCount))
	for _, v := range data.summonInfos {
		l4g.Info("SummonInfo%+v", v)
	}
}

func addGuaranteeAfterSummon(copyGuarantee []uint32, resources []*cl.Resource, data *Data, i uint32, wishHeroID uint32) {
	for index, resource := range resources {
		if resource == nil {
			l4g.Errorf("addGuaranteeAfterSummon index:%d is nil", index)
			continue
		}
		if resource.Type == uint32(common.RESOURCE_HERO) {
			heroInfo := goxml.GetData().HeroInfoM.Index(resource.Value)
			if heroInfo == nil {
				l4g.Errorf("addGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
				continue
			}

			if goxml.ReadRare(heroInfo.Rare) {
				isWish := heroInfo.Id == wishHeroID
				if isWish {
					data.wishHeroCount++
				}
				data.redHeroCount++
				data.summonInfos = append(data.summonInfos, &SummonInfo{
					HeroID:         heroInfo.Id,
					GuaranteeCount: copyGuarantee[goxml.DivineDemonRedGuaranteeIndex],
					TotalCount:     i,
					IsWish:         isWish,
				})
				copyGuarantee[goxml.DivineDemonRedGuaranteeIndex] = 0
				continue
			}
			if goxml.OrangeRare(heroInfo.Rare) {
				copyGuarantee[goxml.DivineDemonOrangeGuaranteeIndex] = 0
				continue
			}
			if goxml.ReadRare(heroInfo.Rare) {
				copyGuarantee[goxml.DivineDemonPurpleGuaranteeIndex] = 0
				continue
			}
		} else if resource.Type == uint32(common.RESOURCE_FRAGMENT) {
			fragmentInfo := goxml.GetData().FragmentInfoM.Index(resource.Value)
			if fragmentInfo == nil {
				l4g.Errorf("addGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
				continue
			}
			switch fragmentInfo.Rare {
			case uint32(common.QUALITY_ORANGE):
				copyGuarantee[goxml.DivineDemonOrangeFragmentGuaranteeIndex] = 0
			}
		}
	}
}
