package main

import (
	"encoding/csv"
	"fmt"
	"os"
	"sfb/service/db"
	"sfb/service/helper"
	"sfb/service/kafka_consumer"
	"sfb/service/logger"
	"sfb/service/proto/log"
	"strconv"
	"testing"

	"gitlab.qdream.com/kit/sea/math/rand"

	"gitlab.qdream.com/kit/library/sea/time"

	"github.com/Shopify/sarama"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/viper"
	"gitlab.qdream.com/kit/library/db/orm"
	"gitlab.qdream.com/kit/library/sea/ctx"
	"gitlab.qdream.com/platform/proto/da"
)

func getDbM(t *testing.T) *db.Manager {

	vipe := viper.New()

	//加载配置
	vipe.SetConfigFile("config")
	vipe.SetConfigType("yaml")
	vipe.AddConfigPath("./")
	err := vipe.ReadInConfig()
	if err != nil {
		panic(err.Error())
	}
	ormC := &orm.Config{}
	err = vipe.Sub("mysql").Unmarshal(ormC)
	if err != nil {
		l4g.Errorf("restful config err:%v", err)
		panic(fmt.Sprintf("err:%s", err))
	}
	ormC.Debug = true
	dbM := db.NewManager(ormC)
	dbM.Init()
	return dbM
}

func getMessageHandler(t *testing.T) *logger.Logger {
	dbM := getDbM(t)
	if dbM == nil {
		t.Error("getDbM nil")
		return nil
	}
	group := ctx.Background()

	messageHandler := logger.NewLogger(dbM)
	messageHandler.Init("test", 1, group.CreateChild())
	return messageHandler
}

var rd *rand.Rand

func init() {
	rd = rand.New(time.Now().UnixNano())
}

func TestUserBase(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}
	logs := make([]*da.Log, 0, 10)

	daLogEnter := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_SEASON_ENTER),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		Param10:    uint64(time.Now().Unix()),
	}
	logs = append(logs, daLogEnter)

	daLogSetName := &da.Log{
		LogType:     uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType:  uint32(log.SUB_TYPE_ID_SET_NAME),
		Name:        "Num4",
		UserID:      1,
		OpGroup:     1,
		SeasonID:    10101,
		SeasonLevel: 120,
	}
	logs = append(logs, daLogSetName)

	daLogSnapshot := &da.Log{
		LogType:     uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType:  uint32(log.SUB_TYPE_ID_SNAPSHOT_USER_WM),
		Name:        "Num4",
		UserID:      1,
		OpGroup:     1,
		SeasonID:    10101,
		SeasonLevel: 120,
	}
	userSnapshot := &log.UserSnapshotForWm{
		BaseId: 1023,
	}
	daLogSnapshot.Param1, _ = helper.Json.MarshalToString(userSnapshot)
	logs = append(logs, daLogSnapshot)

	daLogSeasonLinkCul := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_SEASON_LINK_MONUMENT_CULTIVATION),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		Param12:    15,
		Param13:    30,
	}
	logs = append(logs, daLogSeasonLinkCul)

	daLogSeasonLinkActivation := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_SEASON_LINK_ACTIVATION),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		Param12:    15,
	}
	logs = append(logs, daLogSeasonLinkActivation)

	daLogSeasonLvUp := &da.Log{
		LogType:     uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType:  uint32(log.SUB_TYPE_ID_SEASON_LEVEL_UP),
		Name:        "Num3",
		UserID:      1,
		OpGroup:     1,
		SeasonID:    10101,
		SeasonLevel: 120,
	}
	logs = append(logs, daLogSeasonLvUp)

	daLogSeasonTask := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_SEASON_LEVEL_RECV_TASK_AWARDS),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		Param10:    20,
	}
	logs = append(logs, daLogSeasonTask)
	//for i := 1; i <= 1000000; i++ {
	//for _, logData := range logs {
	// logData.UserID = uint64(i)
	msg := &sarama.ConsumerMessage{}
	var err error
	msg.Value, err = helper.Json.Marshal(logs[0])
	if err != nil {
		t.Error(err)
		return
	}
	if messageHandler.Write(msg) != kafka_consumer.Success {
		t.Error("message handle fail.")
		return
	}
	//}
	//}
}

func TestTowerSeason(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}

	daTowerSeason := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_TOWER_SEASON_FIGHT),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		Param10:    3,
		Param11:    51,
	}

	for i := 1; i <= 1000000; i++ {
		daTowerSeason.UserID = uint64(i)
		daTowerSeason.Param10 = uint64(rd.RandBetween(1, 5))
		daTowerSeason.Param11 = uint64(rd.RandBetween(1, 100))
		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daTowerSeason)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}
}

func TestDisorderLand(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}

	daDisorderLand := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_DISORDER_LAND_FIGHT),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		Param15:    17,
	}

	for i := 1; i <= 1000000; i++ {
		daDisorderLand.UserID = uint64(i)
		daDisorderLand.Param15 = uint64(rd.RandBetween(1, 64))
		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daDisorderLand)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}
}

func TestSeasonDungeon(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}

	daSeasonDungeon := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_SEASON_DUNGEON_FIGHT),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		EventTime:  uint64(time.Now().Unix()),
		Param10:    21013,
		Param12:    1, // win
		Param14:    0,
	}

	for i := 1; i <= 1000000; i++ {
		daSeasonDungeon.UserID = uint64(i)

		daSeasonDungeon.Param14 = uint64(rd.RandBetween(0, 1))

		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daSeasonDungeon)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}
}

func TestPeak(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}

	daPeak := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_PEAK_FIGHTER_RANK_CHANGE),
		Name:       "Num3",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		EventTime:  uint64(time.Now().Unix()),
	}

	p1 := &log.PeakFighterRank{
		Uid: 1,
		Snapshot: &log.PeakRankSnapshot{
			Rank:         1,
			Phase:        1,
			Score:        200,
			CurrentRank:  1,
			CurrentScore: 200,
		},
	}

	for i := 1; i <= 1000000; i++ {
		daPeak.UserID = uint64(i)

		p1.Snapshot.Rank = uint32(rd.RandBetween(1, 480))
		p1.Snapshot.Score = uint32(rd.RandBetween(1, 10000))
		p1.Snapshot.Phase = uint32(rd.RandBetween(1, 4))
		p1.Snapshot.CurrentRank = uint32(rd.RandBetween(1, 120))
		p1.Snapshot.CurrentScore = uint32(rd.RandBetween(1, 4000))
		daPeak.Param1, _ = helper.Json.MarshalToString(p1)
		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daPeak)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}
}

func TestGuild(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}

	daGuild := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_GUILD_SEASON_FINAL_DIVISION),
		Name:       "Guild",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		EventTime:  uint64(time.Now().Unix()),
	}

	p1 := &log.GuildSeasonFinalDivision{
		Gid:             90000001,
		Name:            "Guild",
		BadgeIcon:       2001,
		BadgeBackground: 3001,
		Division:        13,
		// Members:         []uint64{1, 2, 3, 4, 5, 6, 7},
	}

	for i := 1; i <= 33333; i++ {
		p1.Gid = uint64(i)
		p1.Division = uint32(rd.RandBetween(1, 21))
		p1.Members = make([]uint64, 0, 30)
		for m := 1; m <= 30; m++ {
			p1.Members = append(p1.Members, uint64((i-1)*30+m))
		}
		daGuild.Param1, _ = helper.Json.MarshalToString(p1)
		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daGuild)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}
}

func TestGst(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}

	daGstBless := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS),
		Name:       "Num1",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		EventTime:  uint64(time.Now().Unix()),
	}
	for i := 1; i <= 1000000; i++ {
		daGstBless.UserID = uint64(i)
		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daGstBless)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}

	daGstDispatch := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_DISPATCH),
		Name:       "Num1",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		EventTime:  uint64(time.Now().Unix()),
	}

	for i := 1; i <= 1000000; i++ {
		daGstDispatch.UserID = uint64(i)
		p1 := []*log.GSTDispatchTeam{
			{
				OwnerId: fmt.Sprintf("%d", daGstDispatch.UserID),
			},
		}

		if rd.RandBetween(0, 1) == 1 {
			randParam := i / 30
			p1 = append(p1, &log.GSTDispatchTeam{
				OwnerId: fmt.Sprintf("%d", rd.RandBetween(randParam*30+1, randParam*30+30)),
			})
		}

		daGstDispatch.Param1, _ = helper.Json.MarshalToString(p1)
		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daGstDispatch)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}
}

func TestGstSnapshot(t *testing.T) {
	messageHandler := getMessageHandler(t)
	if messageHandler == nil {
		t.Error("getMessageHandler nil")
		return
	}

	daGuild := &da.Log{
		LogType:    uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC),
		LogSubType: uint32(log.SUB_TYPE_ID_SNAPSHOT_GST_GUILD_WM),
		Name:       "Guild",
		UserID:     1,
		OpGroup:    1,
		SeasonID:   10101,
		EventTime:  uint64(time.Now().Unix()),
	}

	p1 := &log.GstGuildSnapshot{
		LRound: 26,
	}

	for i := 1; i <= 33333; i++ {
		p1.Id = uint64(i)
		p4 := &log.GSTGuildUsersScore{}
		p4.UsersScore = append(p4.UsersScore, &log.GSTGuildUserScore{
			Uid:   uint64(rd.RandBetween((i-1)*30+1, i*30)),
			Score: uint32(rd.RandBetween(1, 500)),
		})
		p4.UsersScore = append(p4.UsersScore, &log.GSTGuildUserScore{
			Uid:   uint64(rd.RandBetween((i-1)*30+1, i*30)),
			Score: uint32(rd.RandBetween(1, 500)),
		})
		p4.UsersScore = append(p4.UsersScore, &log.GSTGuildUserScore{
			Uid:   uint64(rd.RandBetween((i-1)*30+1, i*30)),
			Score: uint32(rd.RandBetween(1, 500)),
		})
		p4.UsersScore = append(p4.UsersScore, &log.GSTGuildUserScore{
			Uid:   uint64(rd.RandBetween((i-1)*30+1, i*30)),
			Score: uint32(rd.RandBetween(1, 500)),
		})
		p4.UsersScore = append(p4.UsersScore, &log.GSTGuildUserScore{
			Uid:   uint64(rd.RandBetween((i-1)*30+1, i*30)),
			Score: uint32(rd.RandBetween(1, 500)),
		})

		daGuild.Param1, _ = helper.Json.MarshalToString(p1)
		daGuild.Param4, _ = helper.Json.MarshalToString(p4)
		msg := &sarama.ConsumerMessage{}
		var err error
		msg.Value, err = helper.Json.Marshal(daGuild)
		if err != nil {
			t.Error(err)
			return
		}
		if messageHandler.Write(msg) != kafka_consumer.Success {
			t.Error("message handle fail.")
			return
		}
	}
}

func TestCsv(t *testing.T) {
	// 打开 CSV 文件
	csvFile, err := os.Open("/Users/<USER>/Downloads/dm_oh_season_achievement20240802.csv")
	if err != nil {
		fmt.Printf("llllllllllllllllllllllllllll  err:%s \n", err)
		return
	}
	defer csvFile.Close()

	// 创建 CSV 读取器
	csvReader := csv.NewReader(csvFile)

	// 读取 CSV 文件并插入 MySQL 数据库
	records, err := csvReader.ReadAll()
	if err != nil {
		fmt.Printf("csvReadAll err:%s \n", err)
		return
	}

	datas := make(map[uint32][]*db.FlushBack)

	for index, record := range records {
		if index == 0 {
			continue
		}
		uid, err := strconv.ParseUint(record[0], 10, 64)
		if err != nil {
			fmt.Printf("uid err:%s param:%s \n", err, record[0])
			return
		}
		data := record[1]

		seasonId, err := strconv.ParseUint(record[2], 10, 32)
		if err != nil {
			fmt.Printf("seasonId err:%s \n", err)
			return
		}

		datas[uint32(seasonId)] = append(datas[uint32(seasonId)], &db.FlushBack{
			SeasonId: uint32(seasonId),
			Uid:      uid,
			OpGroup:  3,
			Data:     data,
		})
	}

	dbM := getDbM(t)

	for seasonId, data := range datas {
		start := time.AccurateNow()
		mDB := orm.Dynamic(dbM.Db, &db.FlushBack{SeasonId: seasonId, OpGroup: 3}, true)

		mDB.AutoMigrate(&db.FlushBack{SeasonId: seasonId, OpGroup: 3})

		// 批量插入，一次最多5000条
		if tx := mDB.CreateInBatches(data, 5000); tx.Error != nil {
			fmt.Printf("saveFlashBack: CreateInBatches error. opGroup:%d seasonId:%d err:%s \n", 3, seasonId, tx.Error)
			return
		}
		useTime := time.Since(start)
		l4g.Infof("saveFlashBack useTime:%v", useTime)
	}

	fmt.Println("Data imported successfully!")
}
