package snapshot

import (
	"sfb/service/db"
	"sfb/service/helper"
	sub "sfb/service/proto/log"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/library/db/orm"
	"gitlab.qdream.com/kit/sea/util"
	"gorm.io/gorm"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_GST_GUILD_WM),
		&GSTSnapshotMessage{}, "gvgdata_snapshot")
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_USER_WM),
		&UserSnapshotMessage{}, "chardata_snapshot")
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SNAPSHOT_GUILD_WM),
		&GuildSnapshotMessage{}, "guilddata_snapshot")
}

type GSTSnapshotMessage struct {
	helper.BaseMessage
}

func (g *GSTSnapshotMessage) Execute(log *da.Log) bool {

	var gstGuildSnapshot *sub.GstGuildSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &gstGuildSnapshot)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return false
	}
	database := g.GetDb()
	if gstGuildSnapshot.LRound == 26 {
		var usersScore *sub.GSTGuildUsersScore
		err = helper.Json.Unmarshal([]byte(log.Param4), &usersScore)
		if err != nil {
			l4g.Error("[Logger] json unmarshal error: %v", err)
			return false
		}
		for _, userScore := range usersScore.UsersScore {
			model := &db.GuildUser{
				Uid:      userScore.Uid,
				OpGroup:  log.OpGroup,
				SeasonId: log.SeasonID,
				GstScore: userScore.Score,
			}
			mDB := orm.Dynamic(database, model, true)
			tx := mDB.Model(model).Where("uid = ?", userScore.Uid).UpdateColumn("gst_score", gorm.Expr("gst_score + ?", userScore.Score))
			if tx.Error != nil {
				l4g.Errorf("GSTSnapshotMessage: update error. log:%+v error:%s", log, tx.Error)
				return false
			}
			err = helper.CheckCreateAfterUpdates(tx, mDB, model)
			if err != nil {
				l4g.Errorf("GSTSnapshotMessage: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
				return false
			}
		}
	}

	return true
}

type UserSnapshotMessage struct {
	helper.BaseMessage
}

func (m *UserSnapshotMessage) Execute(log *da.Log) bool {
	if log.SeasonLevel == 0 { // 还没开启赛季，直接返回
		return true
	}

	var userSnapshot *sub.UserSnapshotForWm
	err := helper.Json.Unmarshal([]byte(log.Param1), &userSnapshot)
	if err != nil {
		l4g.Error("[UserSnapshotMessage] json unmarshal error: %v", err)
		return false
	}

	database := m.GetDb()

	opGroup := log.OpGroup
	uid := log.UserID

	userBaseModel := &db.UserBase{
		Uid:      uid,
		OpGroup:  opGroup,
		SeasonId: log.SeasonID,
		Avatar:   userSnapshot.BaseId,
	}
	mDB := orm.Dynamic(database, userBaseModel, true)
	tx := mDB.Where("uid = ?", uid).Updates(userBaseModel) // 更新avatar
	if tx.Error != nil {
		l4g.Errorf("UserSnapshotMessage: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	return true
}

type GuildSnapshotMessage struct {
	helper.BaseMessage
}

func (m *GuildSnapshotMessage) Execute(log *da.Log) bool {
	database := m.GetDb()

	var guildSnapshot *sub.GuildSnapshot
	err := helper.Json.Unmarshal([]byte(log.Param1), &guildSnapshot)
	if err != nil {
		l4g.Error("[GuildSnapshotMessage] json unmarshal error: %v", err)
		return false
	}

	opGroup := log.OpGroup
	gid := guildSnapshot.Id

	model := &db.Guild{
		Gid:        gid,
		OpGroup:    opGroup,
		SeasonId:   log.SeasonID,
		Name:       guildSnapshot.Name,
		Icon:       guildSnapshot.BadgeIcon,
		Background: guildSnapshot.BadgeBackground,
		Division:   guildSnapshot.Division,
	}
	if guildSnapshot.SettlementDivision != 0 {
		model.Division = guildSnapshot.SettlementDivision
	}

	mDB := orm.Dynamic(database, model, true)

	model.Members = util.Uint64SliceToString(guildSnapshot.MemberIds, ",")

	tx := mDB.Where("gid = ?", gid).Updates(model)
	if tx.Error != nil {
		l4g.Errorf("GuildSnapshot: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	err = helper.CheckCreateAfterUpdates(tx, mDB, model)
	if err != nil {
		l4g.Errorf("GuildSnapshot: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
		return false
	}
	return true
}
