package peak

import (
	"errors"
	"sfb/service/db"
	"sfb/service/helper"
	sub "sfb/service/proto/log"

	"gorm.io/gorm"

	"gitlab.qdream.com/kit/library/db/orm"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_PEAK_FIGHTER_RANK_CHANGE),
		&FighterRankChangeMessage{}, "peak_fighter_rank_change")
}

// 排名变化
type FighterRankChangeMessage struct {
	helper.BaseMessage
}

func (m *FighterRankChangeMessage) Execute(log *da.Log) bool {
	database := m.GetDb()
	var logData *sub.PeakFighterRank
	err := helper.Json.Unmarshal([]byte(log.Param1), &logData)
	if err != nil {
		l4g.Errorf("[FighterRankChangeMessage] json unmarshal error: %s", err.Error())
		return false
	}
	opGroup := log.OpGroup
	uid := log.UserID
	model := &db.Peak{
		Uid:      uid,
		OpGroup:  opGroup,
		SeasonId: log.SeasonID,
	}
	mDB := orm.Dynamic(database, model, true)
	dbData := &db.Peak{}
	if err := mDB.Where("uid = ?", uid).First(dbData).Error; errors.Is(err, gorm.ErrRecordNotFound) {
		// 没找到，插入新值，返回

		model.PeakMaxRank = logData.Snapshot.CurrentRank
		model.PeakMaxRankNum = 1
		model.PeakPlayNum = 1
		model.Phase = logData.Snapshot.Phase
		tx := mDB.Create(model)
		if tx.Error != nil {
			l4g.Errorf("PeakFighterRankChange: update error. log:%+v error:%s", log, tx.Error)
			return false
		}
		return true
	} else if err != nil {
		l4g.Errorf("PeakFighterRankChange: find error. log:%+v error:%s", log, err)
		return false
	}

	change := false
	if dbData.PeakMaxRank == 0 || logData.Snapshot.CurrentRank < dbData.PeakMaxRank { // 更新最好名次
		model.PeakMaxRank = logData.Snapshot.CurrentRank
		model.PeakMaxRankNum = 1
		model.MaxRankPhase = logData.Snapshot.Phase
		change = true
	} else if logData.Snapshot.CurrentRank == dbData.PeakMaxRank {
		if logData.Snapshot.Phase != dbData.MaxRankPhase { // 存在同一阶段，同一名次发多次的情况，所以这里判断必须不是同一阶段才加
			model.PeakMaxRankNum = dbData.PeakMaxRankNum + 1
			model.MaxRankPhase = logData.Snapshot.Phase
			change = true
		}
	}

	if dbData.Phase != logData.Snapshot.Phase {
		model.Phase = logData.Snapshot.Phase
		model.PeakPlayNum = dbData.PeakPlayNum + 1 // 参与次数只算参与几期
		change = true
	}
	if !change {
		return true
	}
	if err := mDB.Where("uid = ?", uid).Updates(model).Error; err != nil {
		l4g.Errorf("PeakFighterRankChange: update error. log:%+v, err:%v", log, err)
		return false
	}

	return true
}
