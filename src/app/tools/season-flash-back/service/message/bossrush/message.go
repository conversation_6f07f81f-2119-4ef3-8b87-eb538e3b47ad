package bossrush

import (
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/library/db/orm"
	"gitlab.qdream.com/platform/proto/da"
	"sfb/service/db"
	"sfb/service/helper"
	sub "sfb/service/proto/log"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_BOSS_RUSH_FIGHT),
		&BossRushFightMessage{}, "boss_rush_fight")
}

type BossRushFightMessage struct {
	helper.BaseMessage
}

func (m *BossRushFightMessage) Execute(log *da.Log) bool {
	if log.Param11 == 0 { // 未击杀Boss，不更新最大等级
		return true
	}

	database := m.GetDb()
	uid := log.UserID
	maxLevel := uint32(log.Param12)
	model := &db.BossRush{
		Uid:      uid,
		OpGroup:  log.OpGroup,
		SeasonId: log.SeasonID,
		MaxLevel: maxLevel,
	}
	mDB := orm.Dynamic(database, model, true)

	tx := mDB.Where("uid = ? and max_level < ?", uid, maxLevel).Updates(db.BossRush{MaxLevel: maxLevel})
	if tx.Error != nil {
		l4g.Errorf("BossRushFightMessage: update error. log:%+v error:%s", log, tx.Error)
		return false
	}

	err := helper.CheckCreateAfterUpdates(tx, mDB, model)
	if err != nil {
		l4g.Errorf("BossRushFightMessage: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
		return false
	}

	return true
}
