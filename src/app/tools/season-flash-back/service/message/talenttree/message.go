package talenttree

import (
	"sfb/service/db"
	"sfb/service/helper"
	sub "sfb/service/proto/log"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/library/db/orm"
	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALENT_TREE_LEVEL_UP),
		&TalentTreeLevelUpMessage{}, "talent_tree_level_up")
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_TALENT_TREE_RANK_UPDATE),
		&TalentTreeRankUpdateMessage{}, "talent_tree_rank_update")
}

type TalentTreeLevelUpMessage struct {
	helper.BaseMessage
}

func (m *TalentTreeLevelUpMessage) Execute(log *da.Log) bool {
	if log.Param13 != 1 { // 不是根节点
		return true
	}
	nodeLevel := log.Param12
	database := m.GetDb()

	opGroup := log.OpGroup
	uid := log.UserID

	model := &db.UserBase{
		Uid:             uid,
		OpGroup:         opGroup,
		SeasonId:        log.SeasonID,
		TalentTreeLevel: uint32(nodeLevel),
	}
	mDB := orm.Dynamic(database, model, true)
	tx := mDB.Where("uid = ?", uid).Updates(model)
	if tx.Error != nil {
		l4g.Errorf("TalentTree: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	err := helper.CheckCreateAfterUpdates(tx, mDB, model)
	if err != nil {
		l4g.Errorf("TalentTree: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
		return false
	}
	return true
}

type TalentTreeRankUpdateMessage struct {
	helper.BaseMessage
}

func (m *TalentTreeRankUpdateMessage) Execute(log *da.Log) bool {
	if log.Param10 == 0 {
		return true
	}
	database := m.GetDb()
	opGroup := log.OpGroup
	uid := log.UserID
	model := &db.UserBase{
		Uid:                uid,
		OpGroup:            opGroup,
		SeasonId:           log.SeasonID,
		TalentTreeBestRank: uint32(log.Param10),
	}
	mDB := orm.Dynamic(database, model, true)

	tx := mDB.Where("uid = ? and (talent_tree_best_rank > ? or talent_tree_best_rank = 0 or talent_tree_best_rank is null)", uid, log.Param10).Updates(db.UserBase{TalentTreeBestRank: uint32(log.Param10)})
	if tx.Error != nil {
		l4g.Errorf("TalentTree: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	err := helper.CheckCreateAfterUpdates(tx, mDB, model)
	if err != nil {
		l4g.Errorf("TalentTree: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
		return false
	}
	return true
}
