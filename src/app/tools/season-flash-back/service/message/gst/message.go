package gst

import (
	"sfb/service/db"
	"sfb/service/helper"
	sub "sfb/service/proto/log"
	"strconv"

	"gitlab.qdream.com/kit/library/db/orm"

	l4g "github.com/ivanabc/log4go"
	"gorm.io/gorm"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS),
		&GuildSandTableBlessMessage{}, "guild_sand_table_bless")
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_GUILD_SAND_TABLE_DISPATCH),
		&GuildSandTableDispatchMessage{}, "guild_sand_table_dispatch")
}

// 祝福
type GuildSandTableBlessMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableBlessMessage) Execute(log *da.Log) bool {
	database := m.GetDb()

	opGroup := log.OpGroup
	uid := log.UserID

	model := &db.GuildUser{
		Uid:         uid,
		OpGroup:     opGroup,
		SeasonId:    log.SeasonID,
		GstBlessNum: 1,
	}
	mDB := orm.Dynamic(database, model, true)

	tx := mDB.Where("uid = ?", uid).UpdateColumn("gst_bless_num", gorm.Expr("gst_bless_num + ?", 1))
	if tx.Error != nil {
		l4g.Errorf("GuildSandTableBless: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	err := helper.CheckCreateAfterUpdates(tx, mDB, model)
	if err != nil {
		l4g.Errorf("GuildSandTableBless: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
		return false
	}
	return true
}

// 队伍派遣
type GuildSandTableDispatchMessage struct {
	helper.BaseMessage
}

func (m *GuildSandTableDispatchMessage) Execute(log *da.Log) bool {
	database := m.GetDb()

	var teams []*sub.GSTDispatchTeam
	err := helper.Json.Unmarshal([]byte(log.Param1), &teams)
	if err != nil {
		l4g.Errorf("[GuildSandTableDispatchMessage] json unmarshal error: %v data: %s", err, log.Param1)
		return false
	}

	userTeams := make(map[uint64]uint32)

	for _, team := range teams {
		if team == nil {
			continue
		}
		ownerId, err := strconv.ParseUint(team.OwnerId, 10, 64)
		if err != nil {
			continue
		}
		userTeams[ownerId] += 1
	}

	opGroup := log.OpGroup
	var opHangUpTeamNum uint32
	for uid, num := range userTeams {
		if uid != log.UserID {
			opHangUpTeamNum += num
		}
		model := &db.GuildUser{
			Uid:            uid,
			OpGroup:        opGroup,
			SeasonId:       log.SeasonID,
			GstDispatchNum: num,
		}
		mDB := orm.Dynamic(database, model, true)
		tx := mDB.Where("uid = ?", uid).UpdateColumn("gst_dispatch_num", gorm.Expr("gst_dispatch_num + ?", num))
		if tx.Error != nil {
			l4g.Errorf("GuildSandTableDispatch: update error. log:%+v error:%s", log, tx.Error)
			return false
		}
		err := helper.CheckCreateAfterUpdates(tx, mDB, model)
		if err != nil {
			l4g.Errorf("GuildSandTableDispatch: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
			return false
		}
	}

	if opHangUpTeamNum > 0 {
		model := &db.GuildUser{
			Uid:                log.UserID,
			OpGroup:            opGroup,
			SeasonId:           log.SeasonID,
			GstOpHangUpTeamNum: opHangUpTeamNum,
		}
		mDB := orm.Dynamic(database, model, true)

		tx := mDB.Where("uid = ?", log.UserID).UpdateColumn("gst_op_hang_up_team_num", gorm.Expr("gst_op_hang_up_team_num + ?", opHangUpTeamNum))
		if tx.Error != nil {
			l4g.Errorf("GuildSandTableDispatch: update error. log:%+v error:%s", log, tx.Error)
			return false
		}
		err := helper.CheckCreateAfterUpdates(tx, mDB, model)
		if err != nil {
			l4g.Errorf("GuildSandTableDispatch: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
			return false
		}
	}
	return true

}
