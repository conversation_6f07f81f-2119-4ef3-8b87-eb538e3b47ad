package disorderland

import (
	"sfb/service/db"
	"sfb/service/helper"
	sub "sfb/service/proto/log"

	"gitlab.qdream.com/kit/library/db/orm"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_DISORDER_LAND_FIGHT),
		&Fight{}, "disorder_land_fight")
}

type Fight struct {
	helper.BaseMessage
}

func (m *Fight) Execute(log *da.Log) bool {
	if win := log.Param14 == 1; !win { // 没赢，不做记录
		return true
	}

	database := m.GetDb()
	opGroup := log.OpGroup
	uid := log.UserID
	model := &db.DisorderLand{
		Uid:      uid,
		OpGroup:  opGroup,
		SeasonId: log.SeasonID,
		MaxLevel: uint32(log.Param15),
	}
	mDB := orm.Dynamic(database, model, true)

	tx := mDB.Where("uid = ? and max_level < ?", uid, log.Param15).Updates(db.DisorderLand{MaxLevel: uint32(log.Param15)})
	if tx.Error != nil {
		l4g.Errorf("DisorderLandFight: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	err := helper.CheckCreateAfterUpdates(tx, mDB, model)
	if err != nil {
		l4g.Errorf("DisorderLandFight: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
		return false
	}
	return true
}
