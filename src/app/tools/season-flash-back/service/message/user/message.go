package user

import (
	"sfb/service/db"
	"sfb/service/helper"
	sub "sfb/service/proto/log"

	"gitlab.qdream.com/kit/library/db/orm"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/platform/proto/da"
)

func Init(msgMgr *helper.MessageM) {
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SEASON_ENTER),
		&EnterSeason{}, "enter_season")
	msgMgr.Register(uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC), uint32(sub.SUB_TYPE_ID_SET_NAME),
		&SetName{}, "user_set_name")
}

type EnterSeason struct {
	helper.BaseMessage
}

func (e *EnterSeason) Execute(log *da.Log) bool {
	database := e.GetDb()

	opGroup := log.OpGroup
	uid := log.UserID

	model := &db.UserBase{
		Uid:      uid,
		OpGroup:  opGroup,
		SeasonId: log.SeasonID,
		Name:     log.Name,
		EnterTm:  int64(log.Param10),
		Avatar:   uint32(log.Param11),
	}
	mDB := orm.Dynamic(database, model, true)

	tx := mDB.Where("uid = ?", uid).Updates(model)
	if tx.Error != nil {
		l4g.Errorf("EnterSeason: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	err := helper.CheckCreateAfterUpdates(tx, mDB, model)
	if err != nil {
		l4g.Errorf("EnterSeason: CheckCreateAfterUpdates error. log:%+v error:%s", log, err)
		return false
	}
	return true
}

type SetName struct {
	helper.BaseMessage
}

func (m *SetName) Execute(log *da.Log) bool {
	if log.SeasonLevel == 0 { // 还没开启赛季，直接返回
		return true
	}

	database := m.GetDb()

	opGroup := log.OpGroup
	uid := log.UserID

	userBaseModel := &db.UserBase{
		Uid:      uid,
		OpGroup:  opGroup,
		SeasonId: log.SeasonID,
		Name:     log.Name,
	}
	mDB := orm.Dynamic(database, userBaseModel, true)
	tx := mDB.Where("uid = ?", uid).Updates(userBaseModel) // 这里只是为了更新userBase的Name
	if tx.Error != nil {
		l4g.Errorf("SetName: update error. log:%+v error:%s", log, tx.Error)
		return false
	}
	return true
}
