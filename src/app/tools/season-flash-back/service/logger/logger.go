package logger

import (
	"sfb/service/db"
	"sfb/service/helper"
	"sfb/service/kafka_consumer"

	"github.com/Shopify/sarama"
	"gitlab.qdream.com/kit/library/sea/ctx"
	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

type Logger struct {
	topic     string
	partition int32
	batchSize uint32

	dbM *db.Manager

	msgMgr *helper.MessageM
}

func NewLogger(dbM *db.Manager) *Logger {

	logger := &Logger{}
	logger.msgMgr = helper.NewMessageM(logger)
	logger.dbM = dbM
	return logger
}

func (l *Logger) Init(topic string, partition int32, group *ctx.Group) {
	l.topic = topic
	l.partition = partition

	l.RegisterMessages()
}

func (l *Logger) Close() {
	l4g.Info("Logger topic:%s, partition:%d closed", l.topic, l.partition)
}

func (l *Logger) GetDbManager() *db.Manager {
	return l.dbM
}

func (l *Logger) Write(msg *sarama.ConsumerMessage) kafka_consumer.Status {
	log := &da.Log{}
	if err := helper.Json.Unmarshal(msg.Value, log); err != nil {
		l4g.Error("[Logger] json unmarshal error:%v", err)
		return kafka_consumer.Success
	}
	if !l.msgMgr.Dispatcher(log) {
		l4g.Error("[Logger] dispatch error, topic: %s, partition: %d, info: %+v", l.topic, l.partition, log)
		return kafka_consumer.Fail
	}
	//l4g.Debugf("[Logger] topic:%s, partition:%d info:%+v", l.topic, l.partition, log)
	//msgs := l.getMsgsBufferFromPool()
	//for _, msg := range msgs.getMessages(log) {
	//	if !l.OnWrite(msg) {
	//		return kafka_consumer.Fail
	//	}
	//}

	/*
		if l.batchSize >= l.cfg.BatchSize {
			return l.Sync()
		}*/

	return kafka_consumer.Success
}

func (l *Logger) Sync() kafka_consumer.Status {
	defer func() {
		l.batchSize = 0
	}()

	l4g.Debug("[Logger] log file flush success, batch size:%d topic:%s partition:%d",
		l.batchSize, l.topic, l.partition)
	return kafka_consumer.Success
}

func (l *Logger) OnWrite(msg kafka_consumer.MsgRecorderI) bool {
	// 	uid := msg.GetUserID()
	//distinctID := msg.GetDistinctID()
	//evt := msg.GetEvent()
	return true
}
