package service

import (
	"flag"
	"fmt"
	"sfb/service/config"
	"sfb/service/db"
	"sfb/service/kafka_consumer"
	"sfb/service/logger"

	"gitlab.qdream.com/kit/library/db/orm"

	"github.com/gin-gonic/gin"
	l4g "github.com/ivanabc/log4go"
	"github.com/spf13/viper"
	"gitlab.qdream.com/kit/library/micro/restful"
	"gitlab.qdream.com/kit/library/sea/ctx"
)

var exec = flag.String("exec", "sfb_log_adapter", "赛季回顾日志转换")

type Service struct {
	group    *ctx.Group
	consumer *kafka_consumer.Manager
	dbM      *db.Manager
}

func New(group *ctx.Group) restful.Servicer {
	return &Service{
		group:    group,
		consumer: kafka_consumer.NewManager(group.CreateChild(), config.NewKafka(*exec)),
	}
}

func AddService(m *db.Manager) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("dbm", m)
		c.Next()
	}
}

func (s *Service) Register(c *gin.Engine) {
	ormC := &orm.Config{}
	err := viper.Sub("mysql").Unmarshal(ormC)
	if err != nil {
		l4g.Errorf("restful config err:%v", err)
		panic(fmt.Sprintf("err:%s", err))
	}

	s.dbM = db.NewManager(ormC)
	s.dbM.Init()
	c.Use(AddService(s.dbM))
	c.Handle("GET", "cache", Cache)
	c.Handle("GET", "generate", Generate) // 执行脚本生成赛季回顾的最终数据
	c.Handle("GET", "find", Find)         // 查询赛季回顾数据
}

func (s *Service) Run() {
	err := s.consumer.Init()
	if err != nil {
		panic(fmt.Sprintf("kafka consumer init failed:%s", err))
		return
	}

	s.consumer.Run(logger.GetLogger(s.dbM))
	return
}

func (s *Service) Close() {
	s.group.Stop()
	s.consumer.Close()

	s.group.Wait()
	l4g.Info("service close...")
}
