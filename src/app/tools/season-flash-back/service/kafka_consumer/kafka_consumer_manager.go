package kafka_consumer

import (
	"context"
	"fmt"
	"sfb/service/config"
	"strings"

	"github.com/Shopify/sarama"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/library/sea/ctx"
)

type IConsumer interface {
	Init()
	Process()
	Close()
}

type Manager struct {
	group *ctx.Group
	cfg   *config.Kafka

	consumerGroup sarama.ConsumerGroup
	cancel        context.CancelFunc
}

func NewManager(group *ctx.Group, cfg *config.Kafka) *Manager {
	return &Manager{
		group: group,
		cfg:   cfg,
	}
}

func (m *Manager) Init() error {
	config := sarama.NewConfig()
	config.Consumer.Return.Errors = true

	// Kafka version 0.10.2.0 is required for consumer groups.
	config.Version = sarama.V0_10_2_0

	if m.cfg.Version != "" {
		version, err := sarama.ParseKafkaVersion(m.cfg.Version)
		if err != nil {
			return err
		}

		config.Version = version
	}

	config.ClientID = m.cfg.ClientID
	switch strings.ToLower(m.cfg.Offset) {
	case "oldest", "":
		config.Consumer.Offsets.Initial = sarama.OffsetOldest
	case "newest":
		config.Consumer.Offsets.Initial = sarama.OffsetNewest
	default:
		return fmt.Errorf("invalid offset %q", m.cfg.Offset)
	}

	switch strings.ToLower(m.cfg.BalanceStrategy) {
	case "range", "":
		config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRange
	case "roundrobin":
		config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
	case "sticky":
		config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategySticky
	default:
		return fmt.Errorf("invalid balance strategy %q", m.cfg.BalanceStrategy)
	}
	var err error
	l4g.Debug("kafka brokers: %+v", m.cfg)
	m.consumerGroup, err = sarama.NewConsumerGroup(m.cfg.Brokers, m.cfg.ConsumerGroup, config)
	return err
}

func (m *Manager) Run(creator MessageHandlerCreator) {
	bgCtx, cancel := context.WithCancel(context.Background())
	m.cancel = cancel

	c1 := m.group.CreateChild()
	go func() {
		defer func() {
			ctx.Stop()
			c1.Finish()
			l4g.Info("consume goroutine finish...")
		}()

		l4g.Info("consume start...")
		for bgCtx.Err() == nil {
			handler := newConsumerGroupHandler(creator, m.cfg, c1)
			err := m.consumerGroup.Consume(bgCtx, m.cfg.Topics, handler)
			if err != nil {
				l4g.Error("consume topics(%+v) failed: %s", m.cfg.Topics, err)
				break
			}
		}

		if err := m.consumerGroup.Close(); err != nil {
			l4g.Error("consume close failed: %s", err)
		}
	}()

	c2 := m.group.CreateChild()
	go func() {
		defer func() {
			c2.Finish()
			l4g.Info("consume errors goroutine finish...")
		}()
		for err := range m.consumerGroup.Errors() {
			l4g.Error("consume failed: %s", err)
		}
	}()
}

// Close 阻塞操作
// 本身这个管理器是非阻塞的，那么需要提供这样一个Close的关闭资源的服务
// 他的所有的子服务是阻塞的还是非阻塞的（也就是是不是死循环）。
// 是死循环的话不需要额外的close，不是死循环的需要额外的close
// 保证close只会执行一次 这个只会在main里面执行
func (m *Manager) Close() {
	l4g.Info("kafka consumer manager closing")
	m.cancel()
	m.group.Stop() //这个执行了就会执行所有的close
	m.group.Wait()
	m.group.Finish()
	l4g.Info("kafka consumer manager close finish")
}
