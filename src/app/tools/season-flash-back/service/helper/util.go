package helper

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.qdream.com/kit/library/db/orm"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"

	"sfb/service/proto/out/common"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

func FormatBool(num uint64) bool {
	return num == 1
}

func FormatTime(tm int64, zoneData string) string {
	if tm <= 0 {
		//l4g.Error("FormatTime error. tm:%d, zoneData:%s", tm, zoneData)
		return ""
	}
	t, _ := GetLocalTimeInstance(tm, zoneData)
	return t.Format("2006-01-02 15:04:05")
}

func IsCurrency(resourceType uint32) bool {
	return common.RESOURCE(resourceType) == common.RESOURCE_DIAMOND ||
		common.RESOURCE(resourceType) == common.RESOURCE_GOLD ||
		common.RESOURCE(resourceType) == common.RESOURCE_TOKEN
}

func GetTimeAndOffset(tm int64, zoneData string) (string, int64) {
	if tm <= 0 {
		l4g.Error("GetTimeOffset error. tm:%d, zoneData:%s", tm, zoneData)
		return "", 0
	}

	t, offset := GetLocalTimeInstance(tm, zoneData)
	return t.Format("2006-01-02 15:04:05"), offset
}

func GetLocalTimeInstance(tm int64, zoneData string) (time.Time, int64) {
	/*
		zone, offset := formatZoneData(zoneData)
		if zone == "" {
			zone = "Local"
		}
		t := time.Unix(tm, 0)
		local, err := time.LoadLocation(zone)
		if err != nil {
			l4g.Error("GetLocalTimeInstance failed. tm:%d, zone:%s, err:%s",
				tm, zone, err)
			zone = "Local"
			local, _ = time.LoadLocation(zone)
		}
		return t.In(local), offset
	*/
	_, offset := formatZoneData(zoneData)
	local := time.FixedZone("serverZone", int(offset*3600))
	t := time.Unix(tm, 0)
	return t.In(local), offset
}

// 返回时区代码和偏移量
func formatZoneData(zoneData string) (string, int64) {
	zoneVals := strings.Split(zoneData, "|")
	if len(zoneVals) != 2 {
		l4g.Error("formatZoneData failed. zoneData:%s", zoneData)
		return "Local", 0
	}
	offset, _ := strconv.ParseInt(zoneVals[1], 10, 64)
	return zoneVals[0], offset
}

func GetChannel(log *da.Log) string {
	return strconv.FormatUint(uint64(log.OpID), 10)
}

// int32 slice 转 string slice
func Int32SliceToStringSlice(param string) []string {
	var int32Array []uint32
	err := Json.Unmarshal([]byte(param), &int32Array)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil
	}
	strArray := make([]string, 0, len(int32Array))
	for _, id := range int32Array {
		strArray = append(strArray, strconv.FormatUint(uint64(id), 10))
	}
	return strArray
}

// int64 slice 转 string slice
func Int64SliceToStringSlice(param string) []string {
	var int64Array []uint64
	err := Json.Unmarshal([]byte(param), &int64Array)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return nil
	}
	strArray := make([]string, 0, len(int64Array))
	for _, id := range int64Array {
		strArray = append(strArray, strconv.FormatUint(id, 10))
	}
	return strArray
}

// logSeasonIdRemoveWeek
// @Description:  da.Log的赛季ID的后两位代表的是第几周，sfb不需要。这里把它去掉，方便后面的使用
// @param id
// @return uint32
func logSeasonIdRemoveWeek(log *da.Log) {
	log.SeasonID = log.SeasonID / 100
}

// CheckCreateAfterUpdates
// @Description:  根据数据库updates的结果，检查执行create
// @param updatesTX  updates之后返回tx
// @param db     要修改的表，最好是orm.Dynamic生成的带有表名的db
// @param model  要修改的数据，需要包含主键
// @return error
func CheckCreateAfterUpdates(updatesTX *gorm.DB, db *gorm.DB, model schema.Tabler) error {
	if updatesTX == nil || db == nil || model == nil {
		return errors.New(fmt.Sprintf("CheckCreateAfterUpdates: param is nil. updatesTX:%t db:%t model:%+v",
			updatesTX == nil, db == nil, model))
	}
	if updatesTX.Error == nil && updatesTX.RowsAffected == 0 && !updatesTX.DryRun {
		ndb := db.Create(model)
		err := ndb.Error
		if err != nil {
			if orm.AssertErrorCode(orm.MysqlErrorDuplicate, err) {
				stmt, ok := ndb.Statement.ConnPool.(*gorm.PreparedStmtDB)
				if ok {
					stmt.Mux.Lock()
					stmt.PreparedSQL = make([]string, 0, 100) // orm.MysqlErrorDuplicate 错误会 将Insert语句插入到PreparedSQL,这里手动清除下
					stmt.Mux.Unlock()
				} else {
					return errors.New(fmt.Sprintf("stmt assert failt"))
				}
			} else if err.Error() == "sql: statement is closed" {
				stmt, ok := ndb.Statement.ConnPool.(*gorm.PreparedStmtDB)
				if ok {
					stmt.Mux.Lock()
					sql := ndb.Statement.SQL.String()
					l4g.Errorf("statement closed Stmts sql:%s", sql)
					sqlToStmt, exist := stmt.Stmts[sql]
					if exist {
						l4g.Errorf("statement closed Stmts stmt.stmt:%+v", sqlToStmt.Stmt)
						delete(stmt.Stmts, sql)
					}
					stmt.Mux.Unlock()
				} else {
					l4g.Errorf(fmt.Sprintf("stmt assert failt"))
				}
				l4g.Errorf("stmt close statement.  model:%+v", model)
				return nil
			} else {
				l4g.Errorf("CheckCreateAfterUpdates: Create error. model:%+v err:%s", model, err)
				return err
			}
		}
	}
	return nil
}
