package helper

import (
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/platform/proto/da"
)

type Extra struct {
	Os           int    `json:"os"`
	Platform     string `json:"platform"` // TODO 因为目前还没有接充值，暂时输出一个默认值吧。等充值接入后，返回sdk的paytype字段值。
	MediaID      string `json:"mediaid"`
	Language     string `json:"language"`
	UDID         string `json:"udid"`
	Ver          string `json:"ver"`
	DeviceModel  string `json:"device_model"` //设备类型，可通过SDK获取	示例:Iphone7
	DeviceSys    string `json:"device_sys"`   //设备系统，可通过SDK获取	示例:ios 10.1.1
	DeviceRam    int    `json:"device_ram"`   //设备内存，可通过SDK获取	示例:2005
	TaDistinctID string `json:"cache_uuid"`   //数数 - 访客id
	OmniUuid     string `json:"omni_uuid"`    //omni发行唯一id
}

func NewExtra(log *da.Log) *Extra {
	if log.Param8 == "" {
		return &Extra{}
	}
	var stats *Extra
	err := Json.Unmarshal([]byte(log.Param8), &stats)
	if err != nil {
		l4g.Error("[Logger] json unmarshal error: %v", err)
		return &Extra{}
	}
	l4g.Debug("[Logger] param8: %s, stats: %+v", log.Param8, stats)
	if stats == nil {
		l4g.Error("[Logger] device info unmarshal nil")
		return &Extra{}
	}
	return stats
}
