package helper

import (
	"reflect"
	sub "sfb/service/proto/log"
	"sync"

	"gorm.io/gorm"

	"sfb/service/kafka_consumer"

	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
)

type MsgRecorder struct {
	logger     kafka_consumer.MessageHandler
	uid        string
	distinctID string
	evt        string
}

func newRecorder(logger kafka_consumer.MessageHandler, event string) *MsgRecorder {
	recorder := &MsgRecorder{
		logger: logger,
		evt:    event,
	}
	return recorder
}

// 从pool里拿出来的MsgRecorder会有上一次记录的信息
// 对于并非所有字段都有的情况来说，不清空会导致之前同一日志的字段还存在
// 进而导致收集的用户数据出现错误
func (mr *MsgRecorder) clear() *MsgRecorder {
	return mr
}

func (mr *MsgRecorder) GetUserID() string {
	return mr.uid
}

func (mr *MsgRecorder) SetUserID(uid string) {
	mr.uid = uid
}

func (mr *MsgRecorder) GetDistinctID() string {
	return mr.distinctID
}

func (mr *MsgRecorder) SetDistinctID(distinctID string) {
	mr.distinctID = distinctID
}

func (mr *MsgRecorder) GetEvent() string {
	return mr.evt
}

func (mr *MsgRecorder) SetEvent(evt string) {
	mr.evt = evt
}

func (mr *MsgRecorder) Write(log *da.Log) bool {
	// eventTime, zoneOffset := GetTimeAndOffset(int64(log.EventTime), log.Param9)
	return mr.logger.OnWrite(mr)
}

type MessageI interface {
	init(kafka_consumer.MessageHandler, string)
	ExecuteBefore(log *da.Log) bool
	Execute(*da.Log) bool
	GetDb() *gorm.DB
}

type BaseMessage struct {
	logger kafka_consumer.MessageHandler
	event  string
	pool   *sync.Pool
}

func (m *BaseMessage) init(logger kafka_consumer.MessageHandler, event string) {
	m.logger = logger
	m.event = event

	m.pool = &sync.Pool{
		New: func() interface{} {
			b := newRecorder(logger, event)
			return &b
		},
	}
}

func (m *BaseMessage) GetDb() *gorm.DB {
	return m.logger.GetDbManager().Db
}

type MessageM struct {
	logger kafka_consumer.MessageHandler
	// LogType<<32 | LogSubType -> []MessageI
	// 这样可以使一类日志接入多个handler，生成多条日志
	msgm map[uint64][]MessageI
}

func NewMessageM(logger kafka_consumer.MessageHandler) *MessageM {
	return &MessageM{
		logger: logger,
		msgm:   make(map[uint64][]MessageI),
	}
}

func (mm *MessageM) Register(id, subID uint32, msgi MessageI, event string) {
	var key uint64 = uint64(id)<<32 | uint64(subID)
	//避免重复添加同类型msgi
	for _, msg := range mm.msgm[key] {
		if reflect.TypeOf(msg) == reflect.TypeOf(msgi) {
			return
		}
	}
	mm.msgm[key] = append(mm.msgm[key], msgi)
	msgi.init(mm.logger, event)
	l4g.Info("[LogDispatcher] LogType %d SubType %d, event %s, handler %+v", id, subID, event, msgi)
}

func (mm *MessageM) Dispatcher(log *da.Log) bool {
	subTypeID := log.LogSubType
	switch da.LOG_TYPE(log.LogType) {
	case da.LOG_TYPE_DA_ID_LOGOUT, //登出日志SubType代表离线原因
		da.LOG_TYPE_DA_ID_RESOURCE_ADD,
		da.LOG_TYPE_DA_ID_RESOURCE_DEC:
		//资源日志逻辑特殊，SubType代表模块
		subTypeID = uint32(0)
	}
	var key uint64 = uint64(log.LogType)<<32 | uint64(subTypeID)
	for _, msg := range mm.msgm[key] {
		if !msg.ExecuteBefore(log) {
			return false
		}
		if !msg.Execute(log) {
			return false
		}
	}
	return true
}

func (m *BaseMessage) ExecuteBefore(log *da.Log) bool {
	if log.OpGroup == 0 || log.SeasonID < 10000 { // da.Log的正常SeasonID应该后两位是第几周，前面的是赛季ID，赛季ID起码三位
		l4g.Errorf("ExecuteBefore: opGroup or seasonId is 0. log:%+v", log)
		return false
	}

	logSeasonIdRemoveWeek(log) // 这里把赛季ID，修正回来，不带第几周。方便后面使用

	dbM := m.logger.GetDbManager()
	err := dbM.CheckGenerateNewTables(log)
	if err != nil {
		l4g.Errorf("ExecuteBefore: CheckGenerateNewTables error. log:%+v, err:%s", log, err)
		return false
	}

	if log.LogSubType == uint32(sub.SUB_TYPE_ID_SEASON_ENTER) {
		dbM.CheckUpdateSeasonEndTm(log.SeasonID, int64(log.Param12))
	}
	return true
}
