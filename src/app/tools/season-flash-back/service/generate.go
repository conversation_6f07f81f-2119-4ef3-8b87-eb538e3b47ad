package service

import (
	"errors"
	"fmt"
	"sfb/service/db"
	"sfb/service/helper"

	"gitlab.qdream.com/kit/library/net/http/ginutil"

	"gorm.io/gorm"

	"gitlab.qdream.com/kit/sea/time"

	"gitlab.qdream.com/kit/library/db/orm"

	"gitlab.qdream.com/kit/sea/util"

	"github.com/gin-gonic/gin"
	l4g "github.com/ivanabc/log4go"
)

type GenerateReq struct {
	SeasonId uint32 `form:"season_id"`
}

func Generate(c *gin.Context) {

	req := &GenerateReq{}
	if err := c.ShouldBindQuery(req); err != nil {
		l4g.Errorf("[Generate] bind req error. error:%s ", err)
		ginutil.Fail(c, "req params error. err:%s", err)
		return
	}

	dbM := c.MustGet("dbm").(*db.Manager)

	err := seasonFlashBlackGenerate(dbM, req.SeasonId)
	if err != nil {
		ginutil.Fail(c, "seasonFlashBlackGenerate failed. err:%s", err)
		return
	}

	ginutil.Success(c, nil)
	return
}

// 赛季回顾数据ID
const (
	SfbDays                                 = "10010"
	SfbTaskFinishNum                        = "10020"
	SfbFinialSeasonLv                       = "10030"
	SfbLinkMonumentRace                     = "10040"
	SfbLinkMonumentRaceNum                  = "10041"
	SfbActiveLinkHeroNum                    = "10050"
	SfbTowerSeason                          = "10090"
	SfbTowerSeasonRank                      = "10190"
	SfbTowerSeasonRankBeyond                = "10191"
	SfbDisorderLandMaxLevel                 = "10070"
	SfbDisorderLandRank                     = "10100"
	SfbDisorderLandRankBeyond               = "10101"
	SfbSeasonDungeonId                      = "10080"
	SfbSeasonDungeonMinThroughChapterTm     = "10110"
	SfbSeasonDungeonMinThroughChapterBeyond = "10111"
	SfbPeakUserPlayNum                      = "10130"
	SfbPeakMaxRankNum                       = "10131"
	SfbPeakMaxRank                          = "10132"
	SfbGuildBadgeIcon                       = "10140"
	SfbGuildBadgeBackground                 = "10141"
	SfbGuildName                            = "10142"
	SfbGuildDivision                        = "10143"
	SfbGstDispatchNum                       = "10150"
	SfbGstBlessNum                          = "10160"
	SfbGstGuildScoreMaxMemberAvatar         = "10170"
	SfbGstGuildScoreMaxMemberName           = "10171"
	SfbGstOpHangUpMaxMemberAvatar           = "10180"
	SfbGstOpHangUpMaxMemberName             = "10181"
	SfbBossRushMaxLevel                     = "10192"
	SfbTalentTreeLevel                      = "10200"
	SfbTalentTreeBestRank                   = "10201"
	SfbSeasonJewelryMaxLevel                = "10210"
	SfbSeasonJewelryMaxLevelNum             = "10211"
)

// FlashBackRaw 回顾明细
type FlashBackRaw struct {
	Uid    uint64
	Data   map[string]string
	Name   string
	Avatar uint32
}

func seasonFlashBlackGenerate(dbM *db.Manager, seasonId uint32) error {

	season := &db.Season{Id: seasonId}
	if tx := dbM.Db.Model(&db.Season{}).Find(season); tx.Error != nil { // 从数据库获取，方便判断是否正在生成赛季回顾数据
		return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: find season error. seasonId:%d err:%s", seasonId, tx.Error))
	}

	if season.Generating {
		return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: season Generating. seasonId:%d ", seasonId))
	}

	defer func() {
		season.Generating = false
		// 更新正在生成赛季回顾数据为false
		if tx := dbM.Db.Model(&db.Season{}).Where("id = ?", seasonId).Select("generating").Updates(&db.Season{Generating: false}); tx.Error != nil {
			l4g.Errorf("seasonFlashBlackGenerate: season set Generating false error. seasonId:%d err:%s", seasonId, tx.Error)
		}
	}()

	// 更新正在生成赛季回顾数据为true
	season.Generating = true
	if tx := dbM.Db.Model(&db.Season{}).Where("id = ?", seasonId).Updates(season); tx.Error != nil {
		return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: season set Generating true error. seasonId:%d err:%s", seasonId, tx.Error))
	}

	for _, opGroupId := range dbM.GetOpGroupIds() {
		flashBackRaws := generateBaseRaw(dbM.Db, opGroupId, seasonId, season.EndTm)
		if flashBackRaws == nil {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: flashBackRaws is nil. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		ok := generateTowerSeasonRaw(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: generateTowerSeasonRaw fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		/*ok = generateDisorderLandRaw(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: generateDisorderLandRaw fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}*/
		ok = generateBossRushRaw(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: generateBossRushRaw fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		ok = generateSeasonDungeonRaw(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: generateSeasonDungeonRaw fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		ok = generatePeakRaw(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: generatePeakRaw fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		ok = generateGuildRaw(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: generateGuildRaw fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		ok = generateSeasonJewelryRaw(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: generateSeasonJewelryRaw fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		ok = saveFlashBack(dbM.Db, opGroupId, seasonId, flashBackRaws)
		if !ok {
			return errors.New(fmt.Sprintf("seasonFlashBlackGenerate: saveFlashBack fail. opGroup:%d, seasonId:%d", opGroupId, seasonId))
		}
		l4g.Infof("seasonFlashBlackGenerate: opGroup:%d generate ok!", opGroupId)
	}
	return nil
}

func getAllUserBase(database *gorm.DB, seasonId, opGroup uint32) (error, []db.UserBase) {
	u := &db.UserBase{
		SeasonId: seasonId,
		OpGroup:  opGroup,
	}
	var users []db.UserBase
	mDB := orm.Dynamic(database, u, true)
	findStart := time.AccurateNow()
	result := mDB.Find(&users)
	useTime := time.Since(findStart)
	l4g.Infof("find Users useTime:%v", useTime)
	if result.Error != nil {
		l4g.Errorf("Find users error. err:%v", result.Error)
		return result.Error, nil
	}
	return nil, users
}

func generateBaseRaw(database *gorm.DB, opGroupId, seasonId uint32, seasonEnd int64) map[uint64]*FlashBackRaw {
	start := time.AccurateNow()
	err, users := getAllUserBase(database, seasonId, opGroupId)
	if err != nil {
		l4g.Errorf("generateBaseRaw: getAllUserBase error:%s. opGroup:%d seasonId:%d", err, opGroupId, seasonId)
		return nil
	}
	flashBackRaws := make(map[uint64]*FlashBackRaw, len(users)) // 赛季回顾明细
	for _, user := range users {
		flashBackRaw := &FlashBackRaw{
			Uid:    user.Uid,
			Data:   make(map[string]string),
			Name:   user.Name,
			Avatar: user.Avatar,
		}
		flashBackRaws[user.Uid] = flashBackRaw
		var days uint32
		if user.EnterTm == 0 {
			days = util.DaysBetweenTimes(user.CreatedAt.Unix(), seasonEnd) + 1
		} else {
			days = util.DaysBetweenTimes(user.EnterTm, seasonEnd) + 1
		}
		flashBackRaw.Data[SfbDays] = fmt.Sprintf("%d", days)
		flashBackRaw.Data[SfbTaskFinishNum] = fmt.Sprintf("%d", user.TaskFinishNum) // 赛季任务完成数量
		flashBackRaw.Data[SfbFinialSeasonLv] = fmt.Sprintf("%d", user.SeasonLv)     // 赛季最终等级
		if user.LinkMonumentRace > 0 {
			flashBackRaw.Data[SfbLinkMonumentRace] = fmt.Sprintf("%d", user.LinkMonumentRace)       // 羁绊丰碑最高品质
			flashBackRaw.Data[SfbLinkMonumentRaceNum] = fmt.Sprintf("%d", user.LinkMonumentRaceNum) // 羁绊丰碑最高品质的数量
		}
		if user.ActiveLinkHeroNum > 0 {
			flashBackRaw.Data[SfbActiveLinkHeroNum] = fmt.Sprintf("%d", user.ActiveLinkHeroNum) // 激活英雄羁绊的英雄数量
		}
		if user.TalentTreeLevel > 0 {
			flashBackRaw.Data[SfbTalentTreeLevel] = fmt.Sprintf("%d", user.TalentTreeLevel) // 赛季灵树最大等级
		}
		if user.TalentTreeBestRank > 0 {
			flashBackRaw.Data[SfbTalentTreeBestRank] = fmt.Sprintf("%d", user.TalentTreeBestRank) // 赛季灵树在排行榜的最小值
		}
	}
	useTime := time.Since(start)
	l4g.Infof("generateBaseRaw useTime:%v", useTime)
	return flashBackRaws
}

func generateTowerSeasonRaw(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	t := &db.TowerSeason{
		SeasonId: uint32(seasonId),
		OpGroup:  opGroupId,
	}
	var tss []db.TowerSeason
	mDB := orm.Dynamic(database, t, true)
	result := mDB.Order("mode DESC, max_floor DESC, updated_at ASC").Find(&tss)
	if result.Error != nil {
		l4g.Errorf("Find TowerSeason error. err:%v", result.Error)
		return false
	}
	totalNum := len(tss)
	for index, towerSeason := range tss {
		flashBackRaw := flashBackRaws[towerSeason.Uid]
		if flashBackRaw == nil {
			continue
		}
		flashBackRaw.Data[SfbTowerSeason] = fmt.Sprintf("%d-%d", towerSeason.Mode, towerSeason.MaxFloor)   // 百塔最高进度
		flashBackRaw.Data[SfbTowerSeasonRank] = fmt.Sprintf("%d", index+1)                                 // 破碎远征（百塔）排名
		flashBackRaw.Data[SfbTowerSeasonRankBeyond] = fmt.Sprintf("%d", (totalNum-index-1)*10000/totalNum) // 破碎远程超越多少人（万分比）
	}
	useTime := time.Since(start)
	l4g.Infof("generateTowerSeasonRaw useTime:%v", useTime)
	return true
}

func generateDisorderLandRaw(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	disorderLand := &db.DisorderLand{
		SeasonId: uint32(seasonId),
		OpGroup:  opGroupId,
	}
	var dls []db.DisorderLand
	mDB := orm.Dynamic(database, disorderLand, true)
	result := mDB.Order("max_level DESC, updated_at ASC").Find(&dls)
	if result.Error != nil {
		l4g.Errorf("Find disorderLand error. err:%v", result.Error)
		return false
	}
	totalNum := len(dls)
	for index, disorderLand := range dls {
		flashBackRaw := flashBackRaws[disorderLand.Uid]
		if flashBackRaw == nil {
			continue
		}
		flashBackRaw.Data[SfbDisorderLandMaxLevel] = fmt.Sprintf("%d", disorderLand.MaxLevel)               // 失序空间最高进度
		flashBackRaw.Data[SfbDisorderLandRank] = fmt.Sprintf("%d", index+1)                                 // 失序空间排名
		flashBackRaw.Data[SfbDisorderLandRankBeyond] = fmt.Sprintf("%d", (totalNum-index-1)*10000/totalNum) // 失序空间超越多少人(万分比)
	}
	useTime := time.Since(start)
	l4g.Infof("generateDisorderLandRaw useTime:%v", useTime)
	return true
}

func generateBossRushRaw(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	disorderLand := &db.BossRush{
		SeasonId: seasonId,
		OpGroup:  opGroupId,
	}
	var brs []db.BossRush
	mDB := orm.Dynamic(database, disorderLand, true)
	result := mDB.Find(&brs)
	if result.Error != nil {
		l4g.Errorf("Find BossRush error. err:%v", result.Error)
		return false
	}
	for _, bossRush := range brs {
		flashBackRaw := flashBackRaws[disorderLand.Uid]
		if flashBackRaw == nil {
			continue
		}
		flashBackRaw.Data[SfbBossRushMaxLevel] = fmt.Sprintf("%d", bossRush.MaxLevel) // 击杀Boss的最高等级
	}
	useTime := time.Since(start)
	l4g.Infof("generateBossRushRaw useTime:%v", useTime)
	return true
}

func generateSeasonDungeonRaw(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	seasonD := &db.SeasonDungeon{
		SeasonId: uint32(seasonId),
		OpGroup:  opGroupId,
	}
	var sds []db.SeasonDungeon
	mDB := orm.Dynamic(database, seasonD, true)
	result := mDB.Order("min_through_chapter_tm ASC").Find(&sds)
	if result.Error != nil {
		l4g.Errorf("Find SeasonDungeon error. err:%v", result.Error)
		return false
	}
	totalNum := len(sds)
	for index, seasonDungeon := range sds {
		flashBackRaw := flashBackRaws[seasonDungeon.Uid]
		if flashBackRaw == nil {
			continue
		}
		flashBackRaw.Data[SfbSeasonDungeonId] = fmt.Sprintf("%d", seasonDungeon.DungeonId) // 伊特恩之书最高进度
		if seasonDungeon.MinThroughChapterTm > 0 {
			flashBackRaw.Data[SfbSeasonDungeonMinThroughChapterTm] = fmt.Sprintf("%d", seasonDungeon.MinThroughChapterTm)     // 最快通关章节时间
			flashBackRaw.Data[SfbSeasonDungeonMinThroughChapterBeyond] = fmt.Sprintf("%d", (totalNum-index-1)*10000/totalNum) // 伊特恩之书最快通关章节时间戳差值超越多少人
		}
	}
	useTime := time.Since(start)
	l4g.Infof("generateSeasonDungeonRaw useTime:%v", useTime)
	return true
}

func generatePeakRaw(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	p := &db.Peak{
		SeasonId: uint32(seasonId),
		OpGroup:  opGroupId,
	}
	var peaks []db.Peak
	mDB := orm.Dynamic(database, p, true)
	result := mDB.Find(&peaks)
	if result.Error != nil {
		l4g.Errorf("Find Peak error. err:%v", result.Error)
		return false
	}
	for _, peak := range peaks {
		flashBackRaw := flashBackRaws[peak.Uid]
		if flashBackRaw == nil {
			continue
		}
		flashBackRaw.Data[SfbPeakUserPlayNum] = fmt.Sprintf("%d", peak.PeakPlayNum)
		flashBackRaw.Data[SfbPeakMaxRank] = fmt.Sprintf("%d", peak.PeakMaxRank)
		flashBackRaw.Data[SfbPeakMaxRankNum] = fmt.Sprintf("%d", peak.PeakMaxRankNum)
	}
	useTime := time.Since(start)
	l4g.Infof("generatePeakRaw useTime:%v", useTime)
	return true
}

func generateGuildRaw(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	g := &db.Guild{
		SeasonId: uint32(seasonId),
		OpGroup:  opGroupId,
	}
	var guilds []db.Guild
	mDB := orm.Dynamic(database, g, true)
	result := mDB.Find(&guilds)
	if result.Error != nil {
		l4g.Errorf("Find guilds error. err:%v", result.Error)
		return false
	}
	for _, guild := range guilds {
		var memberIds []uint64
		memberIds = util.StringToUint64Slice(guild.Members, ",")

		gu := &db.GuildUser{
			SeasonId: seasonId,
			OpGroup:  opGroupId,
		}
		mDB = orm.Dynamic(database, gu, true)
		var memberGuildUsers []db.GuildUser
		result := mDB.Where("uid IN (?)", memberIds).Find(&memberGuildUsers)
		if result.Error != nil {
			l4g.Errorf("Find GuildUser error. err:%v", result.Error)
			return false
		}

		var mostGstScore, mostOpHangUp db.GuildUser
		for _, member := range memberGuildUsers {
			flashBackRaw := flashBackRaws[member.Uid]
			if flashBackRaw == nil {
				continue
			}
			flashBackRaw.Data[SfbGstDispatchNum] = fmt.Sprintf("%d", member.GstDispatchNum)
			flashBackRaw.Data[SfbGstBlessNum] = fmt.Sprintf("%d", member.GstBlessNum)

			if member.GstScore > 0 && member.GstScore > mostGstScore.GstScore {
				mostGstScore = member
			}
			if member.GstOpHangUpTeamNum > 0 && member.GstOpHangUpTeamNum > mostOpHangUp.GstOpHangUpTeamNum {
				mostOpHangUp = member
			}
		}
		for _, id := range memberIds { // 这个用memberIds遍历。防止成员在公会中没有做任何操作，GuildUser不存在。
			flashBackRaw := flashBackRaws[id]
			if flashBackRaw == nil {
				continue
			}
			flashBackRaw.Data[SfbGuildBadgeIcon] = fmt.Sprintf("%d", guild.Icon)
			flashBackRaw.Data[SfbGuildBadgeBackground] = fmt.Sprintf("%d", guild.Background)
			flashBackRaw.Data[SfbGuildName] = guild.Name
			flashBackRaw.Data[SfbGuildDivision] = fmt.Sprintf("%d", guild.Division)
			mostGstScoreRaw := flashBackRaws[mostGstScore.Uid]
			if mostGstScoreRaw != nil {
				flashBackRaw.Data[SfbGstGuildScoreMaxMemberName] = mostGstScoreRaw.Name
				flashBackRaw.Data[SfbGstGuildScoreMaxMemberAvatar] = fmt.Sprintf("%d", mostGstScoreRaw.Avatar)
			}
			mostOpHangUpRaw := flashBackRaws[mostOpHangUp.Uid]
			if mostOpHangUpRaw != nil {
				flashBackRaw.Data[SfbGstOpHangUpMaxMemberName] = mostOpHangUpRaw.Name
				flashBackRaw.Data[SfbGstOpHangUpMaxMemberAvatar] = fmt.Sprintf("%d", mostOpHangUpRaw.Avatar)
			}
		}
	}
	useTime := time.Since(start)
	l4g.Infof("generateGuildRaw useTime:%v", useTime)
	return true
}

func generateSeasonJewelryRaw(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	model := &db.SeasonJewelry{
		SeasonId: seasonId,
		OpGroup:  opGroupId,
	}
	var sjs []db.SeasonJewelry
	mDB := orm.Dynamic(database, model, true)
	result := mDB.Find(&sjs)
	if result.Error != nil {
		l4g.Errorf("Find SeasonJewelry error. err:%v", result.Error)
		return false
	}
	for _, sj := range sjs {
		flashBackRaw := flashBackRaws[sj.Uid]
		if flashBackRaw == nil {
			continue
		}
		flashBackRaw.Data[SfbSeasonJewelryMaxLevel] = fmt.Sprintf("%d", sj.JewelryMaxLevel) // 获得赛季装备的最高等级
		flashBackRaw.Data[SfbSeasonJewelryMaxLevelNum] = fmt.Sprintf("%d", sj.MaxLevelNum)  // 最高等级装备的数量
	}
	useTime := time.Since(start)
	l4g.Infof("generateSeasonJewelryRaw useTime:%v", useTime)
	return true
}

func saveFlashBack(database *gorm.DB, opGroupId, seasonId uint32, flashBackRaws map[uint64]*FlashBackRaw) bool {
	start := time.AccurateNow()
	model := &db.FlushBack{SeasonId: seasonId, OpGroup: opGroupId}
	if database.Migrator().HasTable(model.TableName()) {
		err := database.Migrator().DropTable(model.TableName())
		if err != nil {
			l4g.Errorf("saveFlashBack: dropTable error. tableName:%s, err:%s", model.TableName(), err)
			return false
		}
	}
	mDB := orm.Dynamic(database, model, true)
	err := mDB.AutoMigrate(model)
	if err != nil {
		l4g.Errorf("saveFlashBack: AutoMigrate error. tableName:%s, err:%s", model.TableName(), err)
		return false
	}

	saveBatch := make([]*db.FlushBack, 0, len(flashBackRaws))
	// 数据处理完成，保存数据库
	for uid, raw := range flashBackRaws {
		if raw == nil {
			continue
		}
		f := &db.FlushBack{
			Uid:      uid,
			SeasonId: seasonId,
			OpGroup:  opGroupId,
		}
		f.Data, err = helper.Json.MarshalToString(raw.Data)
		if err != nil {
			l4g.Errorf("")
			continue
		}

		saveBatch = append(saveBatch, f)
	}

	// 批量插入，一次最多5000条
	if tx := mDB.CreateInBatches(saveBatch, 5000); tx.Error != nil {
		l4g.Errorf("saveFlashBack: CreateInBatches error. opGroup:%d seasonId:%d err:%s", opGroupId, seasonId, tx.Error)
		return false
	}
	useTime := time.Since(start)
	l4g.Infof("saveFlashBack useTime:%v", useTime)
	return true
}
