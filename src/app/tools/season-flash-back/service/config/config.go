package config

import (
	"time"

	"github.com/spf13/viper"
)

type Kafka struct {
	Brokers []string
	Topics  []string
	//StartPartition int32
	//EndPartition   int32
	ClientID        string
	ConsumerGroup   string
	Offset          string
	BalanceStrategy string
	ConsumerTicker  time.Duration
	//ConsumerFetchSize      int32
	//ConsumerFetchMinSize   int32
	//ConsumerNetReadTimeout int32
	//Target                 string //目标名字
	Version string
}

func NewKafka(consumer string) *Kafka {
	kafka := viper.Sub("kafka")
	consumerCfg := kafka.Sub(consumer)
	return &Kafka{
		Brokers:         kafka.GetStringSlice("brokers"),
		Offset:          kafka.GetString("offset"),
		BalanceStrategy: kafka.GetString("balance_strategy"),
		ConsumerTicker:  kafka.GetDuration("consumer_ticker"),
		Version:         kafka.GetString("version"),
		Topics:          consumerCfg.GetStringSlice("topics"),
		ClientID:        consumerCfg.GetString("client_id"),
		ConsumerGroup:   consumerCfg.GetString("consumer_group"),
		//StartPartition:         kafka.GetInt32("start_partition"),
		//EndPartition:           kafka.GetInt32("end_partition"),
		//ConsumerFetchSize:      kafka.GetInt32("consumer_fetch_size"),
		//ConsumerFetchMinSize:   kafka.GetInt32("consumer_fetch_min_size"),
		//ConsumerNetReadTimeout: kafka.GetInt32("consumer_net_read_timeout"),
		//Target:                 consumer,
	}
}

type ThinkingData struct {
	LogDir    string
	BatchSize uint32
}

func NewThinkingData() *ThinkingData {
	ta := viper.Sub("thinkingdata")
	return &ThinkingData{
		LogDir:    ta.GetString("log_dir"),
		BatchSize: ta.GetUint32("batch_size"),
	}
}
