package feishurobot

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	l4g "github.com/ivanabc/log4go"
)

var (
	secret = "4nNfK4WPEYKP7Jj1NRtuCf"
	url    = "https://open.feishu.cn/open-apis/bot/v2/hook/27f992d9-2934-4998-a83e-3898c8fea336"
)

type Message struct {
	Timestamp int64   `json:"timestamp"`
	Sign      string  `json:"sign"`
	MsgType   string  `json:"msg_type"`
	Content   Content `json:"content"`
}

type Content struct {
	Text string `json:"text"`
}

// GenSign ：生成签名
func GenSign(secret string, timestamp int64) (string, error) {
	//timestamp + key 做sha256, 再进行base64 encode
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret
	var data []byte
	h := hmac.New(sha256.New, []byte(stringToSign))
	_, err := h.Write(data)
	if err != nil {
		return "", err
	}

	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}

func newHttpClient() *http.Client {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	return &http.Client{Transport: tr}
}

func SendMsg(log string) {
	timestamp := time.Now().Unix()
	sign, _ := GenSign(secret, timestamp)

	msg := &Message{
		Timestamp: timestamp,
		Sign:      sign,
		MsgType:   "text",
		Content: Content{
			Text: log,
		},
	}
	msgJson, err := json.Marshal(msg)
	if err != nil {
		l4g.Errorf("feishu_robot: json marshal error: %s", err.Error())
		return
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(msgJson))
	if err != nil {
		l4g.Errorf("feishu_robot: new request error: %s", err.Error())
		return
	}
	req.Header.Set("Content-Type", "application/json")

	httpClient := newHttpClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		l4g.Errorf("feishu_robot: send post request error: %s", err.Error())
		return
	}

	defer resp.Body.Close()
}
