package hooktime

import (
	"fmt"
	"os"
	"strings"
	"sync/atomic"
	"time"

	"github.com/brahma-adshonor/gohook"
)

var (
	unixNano    int64
	hookStarted bool
	timeFile    string
)

const (
	TimeLayout = "2006-01-02 15:04:05"
)

const (
	HookTimeIntvalDuration   = 300 * time.Millisecond
	HookTimeSyncFileDuration = 3 * time.Second
	HookTimeFileMode         = 0644
)

func Init(tf string) {
	timeFile = tf
	buf, err := os.ReadFile(timeFile)
	if err != nil {
		return
	}
	timeStr := string(buf)
	timeStr = strings.Replace(timeStr, "\r\n", "", -1)
	timeStr = strings.Replace(timeStr, "\n", "", -1)
	t, err := time.ParseInLocation(TimeLayout, timeStr, time.Local)
	if err != nil {
		return
	}

	if err := Set(t); err != nil {
		//便于观察日志
		fmt.Println(err)
		panic(err)
	}

	startHook()
}

// 格式 "2006-01-02 15:04:05"
func Set(t time.Time) error {
	atomic.StoreInt64(&unixNano, t.UnixNano())
	startHook()
	return nil
}

// 只用调用1次
func startHook() {
	if hookStarted {
		return
	}
	hookStarted = true

	//替换最底层的time调用
	err := gohook.Hook(time.Now, getNow, nil)
	if err != nil {
		panic(err)
	}

	//定时300ms增长时间
	go func() {
		for {
			time.Sleep(HookTimeIntvalDuration)
			atomic.AddInt64(&unixNano, int64(HookTimeIntvalDuration))
		}
	}()

	//定时把时间写回文件: 重启服务器时，可以从该时间顺延
	go func() {
		for {
			time.Sleep(HookTimeSyncFileDuration)
			str := getNow().Format(TimeLayout)
			_ = os.WriteFile(timeFile, []byte(str), HookTimeFileMode)
		}
	}()
}

//go:noinline
func getNow() time.Time {
	now := time.Unix(0, atomic.LoadInt64(&unixNano))
	return now
}

func HookStarted() bool {
	return hookStarted
}
