//nolint:all
package main

import (
	"fmt"
	"time"

	"app/service/hooktime"

	ktime "gitlab.qdream.com/kit/sea/time"
)

func main() {
	for i := 0; i < 30; i++ {
		time.Sleep(time.Millisecond * 200)
		goTime, kitTime := time.Now(), ktime.Now()
		fmt.Println("go time:", goTime)
		fmt.Println("kit time:", kitTime)
		fmt.Println()
		if kitTime.Sub(goTime) > time.Second || kitTime.Sub(goTime) < -time.Second {
			panic("time diff long")
		}

		if i == 10 {
			_ = hooktime.Set(goTime.Add(time.Hour))
		}
	}
}
