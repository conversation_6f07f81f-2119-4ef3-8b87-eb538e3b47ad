package main

import (
	"expvar"
	"flag"
	"fmt"
	"net"
	"os"
	"os/signal"
	"path/filepath"

	"runtime/debug"
	"strings"
	"syscall"

	"app/goxml"
	"app/patch"
	"app/service/hooktime"
	version "app/version"

	crosssrv "app/cross/service"
	crossmastersrv "app/crossmaster/service"
	logicsrv "app/logic/service"
	"app/postman"
	appsrv "app/service"

	"gitlab.qdream.com/kit/sea/ctx"
	snet "gitlab.qdream.com/kit/sea/net"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"google.golang.org/grpc"
)

const (
	serviceLogic       string = "logic"
	serviceCross       string = "cross"
	serviceCrossMaster string = "crossmaster"
)

type Servicer interface {
	LoadConfig() bool
	ReloadConfig()
	Init(string) bool
	Run(*ctx.Group)
	Close()
}

func getService(name string) (srv Servicer, err error) {
	switch name {
	case serviceLogic:
		srv = logicsrv.NewService()
	case serviceCross:
		srv = crosssrv.NewService()
	case serviceCrossMaster:
		srv = crossmastersrv.NewService()
	default:
		err = fmt.Errorf("[main] no found service: %s", *appsrv.ServiceName)
	}
	return
}

func startService(group *ctx.Group, serviceID string) {
	l4g.Info("service address: %s", serviceID)

	version.ShowVersion()

	childGroup := group.CreateChild()
	defer childGroup.Finish()

	srv, err := getService(*appsrv.ServiceName)
	if err != nil {
		fmt.Println(err)
		ctx.Stop()
		return
	}
	if !srv.LoadConfig() {
		l4g.Error("[main] %s load config fail", *appsrv.ServiceName)
		ctx.Stop()
		return
	}
	if !srv.Init(serviceID) {
		l4g.Error("[main] %s init fail", *appsrv.ServiceName)
		ctx.Stop()
		return
	}

	defer func() {
		group.Stop()

		if err := recover(); err != nil {
			l4g.Error("%s service panic: %s %s\n", *appsrv.ServiceName, err, debug.Stack())
			//if *appsrv.PanicRecover {
			//	panicMsg := "serviceName: alpha" + "\n" + "\n" + string(debug.Stack())
			//	l4g.Errorf("%s panicRecover: %s %s\n", *appsrv.ServiceName, err, panicMsg)
			//	//feishurobot.SendMsg(panicMsg)
			//	return
			//} else {
			//
			//	ctx.Stop()
			//}
		}

		srv.Close()
		childGroup.Stop()
		childGroup.Wait()
		l4g.Info("service has completed finished")
		ctx.Stop() //把这个从recover里移出来,防止因为srv.Run里的context退出但是整体的ctx没有退出,进程一直不退出
	}()
	//patch.DoPatch(*appsrv.PatchPath) //先不加这个
	srv.Run(childGroup)
}

//nolint:funlen
func main() {
	flag.Parse()
	if *appsrv.VersionInfo {
		version.ShowVersion()
		return
	}
	hooktime.Init(*appsrv.TimeFile)

	ipPort := strings.Split(*appsrv.DebugAddr, ":")
	prefix := strings.TrimSpace(ipPort[0])
	port := strings.TrimSpace(ipPort[1])
	//加载日志处理
	var logFileLastName string
	if *appsrv.LogSuffix {
		logFileLastName = *appsrv.ServiceName + "-" + port + ".log"
	} else {
		logFileLastName = *appsrv.ServiceName + ".log"
	}
	logFilename := filepath.Join(*appsrv.LogDir, logFileLastName)
	cfg := util.NewL4GConfig(*appsrv.LogLevel, logFilename)
	l4g.Global.LoadConfiguration("", []byte(cfg))
	defer l4g.Close()
	if *appsrv.LogLevel != "DEBUG" && *appsrv.LogLevel != "INFO" {
		l4g.Error("错误的日志等级: %s", *appsrv.LogLevel)
		return
	}

	bg := ctx.Background()
	// HTTP Server
	//port := strings.Split(*appsrv.DebugAddr, ":")[1]
	if len(prefix) == 0 || strings.EqualFold(prefix, "0.0.0.0") {
		if *appsrv.IPV4 {
			ips, _ := snet.GetHostIPv4()
			prefix = ips[0]
			l4g.Info("ips: %v", ips)
		} else {
			prefix, _ = os.Hostname()
		}
	}
	serviceID := prefix + ":" + port

	debugSrv := snet.NewDebugServer(*appsrv.DebugAddr)
	httpCtx := bg.CreateChild()
	go func() {
		tmp := httpCtx.CreateChild()
		l4g.Info(debugSrv.ListenAndServe())
		tmp.Finish()
	}()
	//GRPC Server
	lis, err := net.Listen("tcp", *appsrv.GrpcAddr)
	if err != nil {
		l4g.Error("grpc failed to listen: %v", err)
		return
	}
	grpcCtx := bg.CreateChild()
	grpcSrv := grpc.NewServer()
	postman.RegisterServer(grpcSrv)
	go func() {
		tmp := grpcCtx.CreateChild()
		if err := grpcSrv.Serve(lis); err != nil {
			l4g.Error("grpc failed to serve: %v", err)
		} else {
			l4g.Info("grpc server close...")
		}
		tmp.Finish()
	}()
	// Game Server
	serviceCtx := bg.CreateChild()
	go startService(serviceCtx, serviceID)
	//注册信号
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGUSR1, syscall.SIGUSR2, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP, os.Interrupt)
QUIT:
	for {
		select {
		case <-bg.Done():
			l4g.Info("all group context exit...")
			break QUIT
		case sig := <-sigs:
			l4g.Info("Signal: %s", sig.String())
			if sig == syscall.SIGHUP {
				/*
					fn := func(ctx context.Context) {
						appsrv.GetService(ctx).(Servicer).ReloadConfig()
					}
					appsrv.CtrlCommand(fn)
				*/
				go func() {
					defer func() {
						if err := recover(); err != nil {
							l4g.Errorf("reload data panic:%s %s", err, debug.Stack())
						}
					}()
					goxml.Load(*appsrv.DataPath, false, false)
					//重新计算一下版本号
					version.ShowVersion()
					l4g.Infof("reload data success")
					l4g.Infof("reload data new version:%d", version.GetVersion())
				}()
			} else if sig == syscall.SIGINT {
				go func() {
					defer func() {
						if err := recover(); err != nil {
							l4g.Errorf("patch code panic:%s %s", err, debug.Stack())
						}
					}()
					err := patch.DoMultiPatch(patch.GetHotfixFile(*appsrv.PatchPath))
					if err != nil {
						l4g.Errorf("patch code fail: %s", err)
					}
				}()

			} else if sig == syscall.SIGUSR1 {
				level := l4g.GetFilterLevel("file")
				if level == l4g.INFO {
					l4g.ChangeFilterLevel("file", l4g.DEBUG)
					l4g.Info("change log level (debug)")
				} else {
					l4g.ChangeFilterLevel("file", l4g.INFO)
					l4g.Info("change log level (info)")
				}
			} else if sig == syscall.SIGUSR2 {
				//Game Server重启
				/*
					serviceCtx.Stop()
					serviceCtx.Wait()
					serviceCtx.Finish()
					serviceCtx = bg.CreateChild()
					go startService(serviceCtx, serviceID)
				*/
			} else {
				break QUIT
			}
		}
	}
	//关闭GRPC Server
	grpcSrv.Stop()
	grpcCtx.Wait()
	grpcCtx.Finish()
	//关闭Game Server
	serviceCtx.Stop()
	serviceCtx.Wait()
	serviceCtx.Finish()
	//关闭HTTP Server
	if err := debugSrv.Close(); err != nil {
		l4g.Error("debug service close error: %s", err)
	}
	httpCtx.Wait()
	httpCtx.Finish()
	//关闭ALL
	ctx.Stop()
	ctx.Wait()
	expvarDebug()
	l4g.Info("service exit......")
}

func expvarDebug() {
	var w strings.Builder //nolint:varnamelen
	fmt.Fprintf(&w, "{\n")
	first := true
	expvar.Do(func(kv expvar.KeyValue) {
		if !first {
			fmt.Fprintf(&w, ",\n")
		}
		first = false
		fmt.Fprintf(&w, "%q: %s", kv.Key, kv.Value)
	})
	fmt.Fprintf(&w, "\n}")
	l4g.Info("expvar: %s", w.String())
}

func GetCodeVersion() string {
	return version.Version
}
