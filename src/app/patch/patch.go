package patch

import (
	"fmt"

	"app/hotfix"
	"app/symbols"
)

/*
*
{"plugin_name":"service20240318162932.so","reload_data":true,"main_code_version":"a2c8d33e","replace_functions":["user.(*C2LTestCommand).Execute"]}
*/
type PatchInfo struct {
	MainCodeVersion  string   `json:"main_code_version"` //要求的主包的代码git版本的前8位,不一致不会更新
	ReloadData       bool     `json:"reload_data"`       //是否需要重新加载表
	PluginName       string   `json:"plugin_name"`       //要加载的补丁包名字
	ReplaceFunctions []string `json:"replace_functions"` //要替换的函数名字 类似于 user.(*C2LTestCommand).Execute
}

func GetHotfixFile(path string) string {
	return fmt.Sprintf("%s%s", path, "patch.go")
}

func DoMultiPatch(filePath string) error {
	// 加载补丁函数foo.GetPatch()
	// 保存代码
	err := hotfix.ApplyMultiFunc(filePath, symbols.Symbols)
	if err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

/*
func getFuncs(path string) ([]string, error) {
	// 打开文件
	file, err := os.Open(path)
	if err != nil {
		fmt.Println("无法打开文件:", err)
		return nil, err
	}
	defer file.Close()

	// 创建一个新的 Scanner 对象
	scanner := bufio.NewScanner(file)
	matchKey := "//hotfixfuncs:"
	// 逐行扫描文件
	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, matchKey) {
			funcStr := line[len(matchKey):]
			return strings.Split(",", funcStr), nil
		}
	}
	return nil, nil
}
*/
