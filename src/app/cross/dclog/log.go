package dclog

import (
	"app/cross/activity"
	"app/protos/in/log"
	"app/protos/out/cl"
	"strconv"
	"strings"

	l4g "github.com/ivanabc/log4go"
	jsoniter "github.com/json-iterator/go"
	"gitlab.qdream.com/kit/sea/util"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

func JSONMarshal(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err == nil {
		return util.String(bytes), nil
	}
	l4g.Errorf("json marshal error:%v %v", err, v)
	return "", err
}

func LogGSTVote(srv activity.Servicer, opGroup uint32, logData *log.LogGSTVote) {
	msg := srv.NewDCLogMessage()
	msg.OpGroup = opGroup
	msg.TypeName = "公会战助威"
	msg.UserID = logData.Uid
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_VOTE)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func LogPeakFighterRank(srv activity.Servicer, opGroup uint32, logData *log.PeakFighterRank) {
	msg := srv.NewDCLogMessage()
	msg.TypeName = "巅峰竞技场-战斗"
	msg.UserID = logData.Uid
	msg.ServerID = logData.Sid
	msg.OpGroup = opGroup

	msg.LogSubType = uint32(log.SUB_TYPE_ID_PEAK_FIGHTER_RANK_CHANGE)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func LogGuildSeasonFinalDivision(srv activity.Servicer, opGroup uint32, logData *log.GuildSeasonFinalDivision) {
	msg := srv.NewDCLogMessage()
	msg.TypeName = "公会-赛季最终段位"
	msg.UserID = logData.Gid
	msg.Name = logData.Name
	msg.OpGroup = opGroup

	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SEASON_FINAL_DIVISION)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func LogGSTDragonSettlement(srv activity.Servicer, opGroup uint32, seasonId, dragonRound, ownProgress, opProgress uint32, ownIsBot, opIsBot bool, ownGuild, opGuild *cl.GSTSimpleGuild, isWin bool) {
	msg := srv.NewDCLogMessage()
	msg.OpGroup = opGroup
	msg.TypeName = "GVG龙战-胜败结算"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GST_DRAGON_SETTLEMENT)

	ownData := &log.LogGSTDragonSettlement{
		IsBot:          ownIsBot,
		DragonProgress: ownProgress,
	}
	if ownGuild != nil {
		ownData.GuildId = ownGuild.GuildId
		ownData.GuildPartition = ownGuild.Partition
		ownData.GuildGrade = ownGuild.Division
	}

	opData := &log.LogGSTDragonSettlement{
		IsBot:          opIsBot,
		DragonProgress: opProgress,
	}
	if opGuild != nil {
		opData.GuildId = opGuild.GuildId
		opData.GuildPartition = opGuild.Partition
		opData.GuildGrade = opGuild.Division
	}

	if isWin {
		msg.Param1, _ = JSONMarshal(ownData)
		msg.Param2, _ = JSONMarshal(opData)
	} else {
		msg.Param1, _ = JSONMarshal(opData)
		msg.Param2, _ = JSONMarshal(ownData)
	}

	msg.Param10 = uint64(seasonId)
	msg.Param11 = uint64(dragonRound)

	srv.WriteLogMessage(msg)
}

func LogGSTDragonCultivation(srv activity.Servicer, opGroup uint32, guildId uint64, dragonInitId, dragonPos, dragonLevel, dragonIdBefore, dragonIdAfter uint32) {
	msg := srv.NewDCLogMessage()
	msg.OpGroup = opGroup
	msg.TypeName = "GVG龙战-养龙"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GST_DRAGON_CULTIVATION)

	msg.Param10 = guildId
	msg.Param11 = uint64(dragonInitId)
	msg.Param12 = uint64(dragonPos)
	msg.Param13 = uint64(dragonLevel)
	msg.Param14 = uint64(dragonIdBefore)
	msg.Param15 = uint64(dragonIdAfter)

	srv.WriteLogMessage(msg)
}

func LogGuildMobScore(srv activity.Servicer, logData *log.LogGuildMobScore) {
	msg := srv.NewDCLogMessage()
	msg.TypeName = "公会竞赛积分"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MOB_GET_SCORE)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func LogOreResourceChange(srv activity.Servicer, logData *log.LogOreResourceChange) {
	msg := srv.NewDCLogMessage()
	msg.TypeName = "公会战矿资源的变化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_RESOURCE_CHANGE)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func LogGSTSkill(srv activity.Servicer, logData *log.LogGSTSkill) {
	msg := srv.NewDCLogMessage()
	msg.TypeName = "公会-使用秘技"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_USE_STRATEGY)
	msg.Param10 = logData.GuildId
	msg.Param14 = logData.UseCount
	msg.Param15 = logData.Type
	msg.Param16 = uint64(logData.GuildDivision)
	srv.WriteLogMessage(msg)
}

func LogGSTStreak(srv activity.Servicer, logData *log.LogGSTStreak) {
	msg := srv.NewDCLogMessage()
	msg.TypeName = "公会地块连胜场次"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_STREAK)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func LogGuildInfo(srv activity.Servicer, logData *log.ESGuildInfo) {
	logDataS, err := JSONMarshal(logData)
	if err != nil {
		l4g.Errorf("json marshal error:%v %v", err, logData)
		return
	}
	data := srv.NewLog(log.LOG_TYPE_KAFKA_LOG).(*log.KafkaLogHandlerData)
	data.Topic = log.KAFKA_TOPIC_KAFKA_TOPIC_ES.String()
	data.EsData = &log.ESLogHandlerData{
		Index: strings.ToLower(log.ES_LOG_INDEX_ES_LOG_INDEX_GUILD_INFO.String()),
		Id:    strconv.FormatUint(logData.GuildId, 10),
		Data:  logDataS,
	}
	srv.WriteLog(log.LOG_TYPE_KAFKA_LOG, data)
}

func LogGSTChallengeTeam(srv activity.Servicer, opGroup uint32, uid, sid uint64, logData *log.LogGSTChallengeStreak) {
	msg := srv.NewDCLogMessage()
	msg.OpGroup = opGroup
	msg.TypeName = "GVG擂台赛-队伍胜场"
	msg.ServerID = uid
	msg.UserID = sid
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_STREAK)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func LogBalanceArenaFight(srv activity.Servicer, opGroup uint32, logData *log.LogBalanceArenaFight) {
	msg := srv.NewDCLogMessage()
	msg.OpGroup = opGroup
	msg.TypeName = "公平竞技场-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_BALANCE_ARENA_FIGHT)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}
