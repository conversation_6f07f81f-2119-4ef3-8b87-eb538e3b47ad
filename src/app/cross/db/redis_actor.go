package db

import (
	"context"
	"fmt"
	"strconv"

	"app/cross/db/redisop"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/r2c"
	"app/protos/in/r2l"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/redis"
	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

type RedisRequest struct {
	parse.Message
	Data       interface{}
	RedisActor *RedisActor
}

func (r *RedisRequest) init(cmd r2c.ID, uid uint64, data interface{}) {
	r.Cmd = uint32(cmd)
	r.UID = uid
	r.Data = data
}

func (r *RedisRequest) Process(param actor.Receiver) error {
	ractor := param.(*RedisActor) //这里就是redis的actor.run的时候传进来的
	r.RedisActor = ractor
	ractor.handle(r)
	return nil
}

type Response struct {
	parse.Message
	Data interface{}
}

type RedisActor struct {
	*actor.Actor

	ctx    *ctx.Group
	srvCtx context.Context

	client *redis.Client
	ropCr  *redisop.CrClient
	cmds   *parse.CommandM
}

func NewRedisActor(srvCtx context.Context, ctx *ctx.Group, redisAddr string, dbIndex int, cfg *actor.Config) (*RedisActor, error) {
	password := goxml.GetData().ServerInfoM.RedisPassword
	timeout := redisTimeout
	if goxml.GetData().ServerInfoM.IsGM() {
		timeout = time.Duration(goxml.LongTimeout) * time.Second
	}
	client, err := redis.DialTimeout("tcp", redisAddr, password, uint32(dbIndex), timeout, true)
	if err != nil {
		l4g.Error("connect redis serve error: %s %s", redisAddr, err)
		return nil, fmt.Errorf("connect redis serve error: %s %w", redisAddr, err)
		//panic(err)
	}
	l4g.Info("connect redis(%s-%d) success", redisAddr, dbIndex)

	act := actor.NewActor(ctx.CreateChild(), cfg)
	rActor := &RedisActor{
		Actor:  act,
		ctx:    ctx,
		srvCtx: srvCtx,
		client: client,
		ropCr:  &redisop.CrClient{Client: client},
		cmds:   parse.NewCommandM(uint32(r2c.ID_MSG_MIN), uint32(r2c.ID_MSG_MAX), commandMaxExecuteTime),
	}
	rActor.AddTimer(&TenSecondTimer{act: rActor}, time.Now().Unix()+10, 10) //nolint:mnd
	return rActor, nil
}

func (r *RedisActor) GetCommandM() *parse.CommandM { return r.cmds }
func (r *RedisActor) GetClient() *redis.Client     { return r.client }

func (r *RedisActor) Close() {
	r.ctx.Stop()
	r.ctx.Wait()
	l4g.Info("redis client close...: %s", r.client.Conn.LocalAddr())
	if err := r.client.Close(); err != nil {
		l4g.Error("redis client %s close error: %s", r.client.Conn.LocalAddr(), err)
	}
	r.ctx.Finish()
}

// 向redisActor，添加请求消息
// @param activity uint32 玩法id
// @param partition uint32 跨服分区id
// @param cmd r2c.ID 消息id
// @param uid uint64 玩家id
// @param data 协议结构类型
func (r *RedisActor) AddMessage(activity, partition uint32, cmd r2c.ID, uid uint64, data interface{}) {
	rr := new(RedisRequest)
	rr.init(cmd, uid, data)
	rr.CSeq = activity
	rr.SSeq = partition
	r.Actor.AddMessage(rr)
}

func (r *RedisActor) handle(msg *RedisRequest) {
	if err, _ := r.cmds.Dispatcher(r.srvCtx, msg); err != nil {
		l4g.Error("[RedisActor] id:%d process error: %s", msg.Cmd, err)
	}
}

func (r *RedisActor) ping() {
	if ret, err := r.client.Cmd("ping").Str(); err != nil || ret != "PONG" {
		l4g.Error("ping redis error: %s", err)
		ctx.Stop()
	}
}

func (r *RedisActor) Load(recv *r2c.C2R_Load) *r2c.R2C_Load {
	retData := &r2c.R2C_Load{
		Ret: uint32(r2c.RET_OK),
	}
	return retData
}

func (r *RedisActor) LoadActInfo(recv *r2c.C2R_LoadActInfo) *r2c.R2C_LoadActInfo {
	startTime := time.AccurateNow()
	retData := &r2c.R2C_LoadActInfo{
		Ret: uint32(r2c.RET_OK),
		Id:  recv.GetId(),
	}

	actParts, err := r.ropCr.GetAllActPartitionSK(uint64(recv.GetId()))
	if err != nil {
		l4g.Errorf("[RedisActor] get all partition error: %v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	actBase, err := r.ropCr.GetActBaseSK(uint64(recv.GetId()))
	if err != nil {
		l4g.Errorf("[RedisActor] get act base error: %v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.Parts = actParts
	retData.Base = actBase

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.LoadActInfo recv:%+v, retData.Parts:%+v, retData.Base:%+v, useTime:%v",
		recv, actParts, actBase, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) SaveActInfo(recv *r2c.C2R_SaveActInfo) {
	startTime := time.AccurateNow()
	i := 0
	if len(recv.Parts) > 0 {
		r.ropCr.SetSomeActPartitionMCallSK(uint64(recv.GetId()), recv.Parts)
		i++
	}
	if recv.Base != nil {
		r.ropCr.SetActBaseMCallSK(uint64(recv.GetId()), recv.Base)
		i++
	}
	for j := 0; j < i; j++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("[RedisActor] save act partition :%d error: %v", j, reply.Err)
			ctx.Stop()
			return
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.SaveActInfo recv:%+v, useTime:%v", recv, endTime.Sub(startTime))
}

type TenSecondTimer struct {
	act *RedisActor
}

func (t *TenSecondTimer) TimeOut(int64) {
	t.act.ping()
}

func (r *RedisActor) LoadWrestle(recv *r2c.C2R_WrestleLoad) *r2c.R2C_WrestleLoad {
	startTime := time.AccurateNow()
	retData := &r2c.R2C_WrestleLoad{
		Ret:       uint32(r2c.RET_OK),
		Partition: recv.GetPartition(),
		Sids:      recv.GetSids(),
	}

	if len(retData.Sids) == 0 {
		l4g.Errorf("LoadWrestle. sids is empty, recv:%+v", recv)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	matcher, err := r.ropCr.GetMatcherSK(retData.Partition)
	if err != nil {
		l4g.Errorf("LoadWrestle. get all matcher error: %v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	top, err := r.ropCr.GetWrestleLastSeasonTop(retData.Partition)
	if err != nil {
		l4g.Errorf("LoadWrestle. get all season top error: %v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	fighters, err := r.ropCr.GetAllFighterSK(retData.Partition)
	if err != nil {
		l4g.Errorf("LoadWrestle. get all fighters error: %v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	datas, err := r.ropCr.GetSomeFightersBak(retData.Sids)
	if err != nil {
		l4g.Errorf("LoadWrestle. get all fighters bak error: %v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	baks := make([]*cr.Fighter, 0, LoadBakFighterInitCap)
	for _, sid := range retData.Sids {
		if data, exist := datas[sid]; !exist {
			l4g.Infof("LoadWrestle. no data in sid: %d", sid)
		} else {
			//TODO 如果这里出错，是否该继续？
			if matcher != nil && data.Time != matcher.SeasonResetTime {
				l4g.Errorf("LoadWrestle. SeasonResetTime not match, sid:%d, time:%d, recv:%+v",
					sid, data.Time, recv)
				continue
			}

			baks = append(baks, data.GetFighters()...)
		}
	}

	retData.Wrestle = &cr.Wrestle{
		Matcher:  matcher,
		Fighters: fighters,
		Top:      top,
		Baks:     baks,
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.LoadWrestle recv:%+v, matcher:%+v, fighterCount:%d, bakFighterCount:%d, useTime:%v",
		recv, matcher, len(fighters), len(baks), endTime.Sub(startTime))
	return retData
}

const LoadBakFighterInitCap = 100

// 新赛季开始前，动态分组，获取指定服务器的fighter数据
// TODO 如果db中数据的落地时间，不符合要求，该继续吗？
func (r *RedisActor) LoadFighters(recv *r2c.C2R_WrestleLoadFighters) *r2c.R2C_WrestleLoadFighters {
	startTime := time.AccurateNow()
	retData := &r2c.R2C_WrestleLoadFighters{
		Ret: uint32(r2c.RET_OK),
	}
	l4g.Debugf("LoadFighters. recv:%+v", recv)

	if len(recv.Sids) == 0 {
		l4g.Errorf("LoadFighters. sids is empty, recv:%+v", recv)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	datas, err := r.ropCr.GetSomeFightersBak(recv.Sids)
	if err != nil {
		l4g.Errorf("LoadFighters. get all fighters bak error: %v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	retData.Fighters = make([]*cr.Fighter, 0, LoadBakFighterInitCap)
	for _, sid := range recv.Sids {
		if data, exist := datas[sid]; !exist {
			l4g.Infof("LoadFighters. no data in sid: %d", sid)
		} else {
			//TODO 如果这里出错，是否该继续？
			if data.Time != recv.SeasonResetTime {
				l4g.Errorf("LoadFighters. SeasonResetTime not match, sid:%d, time:%d, recv:%+v",
					sid, data.Time, recv)
				continue
			}

			retData.Fighters = append(retData.Fighters, data.GetFighters()...)
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.LoadFighters recv:%+v, fighterCount:%d, useTime:%v", recv, len(retData.Fighters),
		endTime.Sub(startTime))
	return retData
}

const (
	GuildAliveInternal = 2592000 // 30天
	LoadGuildCap       = 2000
)

// LoadGuild
// @Description: 跨服启动时加载公会
// @receiver r
// @param recv
// @return *r2c.R2C_GuildLoad
//
//nolint:funlen
func (r *RedisActor) LoadGuild(recv *r2c.C2R_GuildLoad) *r2c.R2C_GuildLoad {
	startTime := time.AccurateNow()
	retData := &r2c.R2C_GuildLoad{
		Ret:      uint32(r2c.RET_OK),
		Guilds:   make(map[uint64]*cr.Guild),
		Dungeons: make(map[uint64]*cr.GuildDungeon),
	}
	l4g.Debugf("LoadGuild. recv:%+v", recv)

	if len(recv.Sids) == 0 {
		l4g.Errorf("LoadGuild. sids is empty, recv:%+v", recv)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	now := time.Now().Unix()
	sidAndGids, err := r.ropCr.GetSomeSidAndGidForGuildLoad(recv.Sids)
	if err != nil {
		l4g.Errorf("LoadGuild. getSidToGid error, err:%+v", err)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	retData.SidAndGids = sidAndGids
	guildCount := make([]uint32, 0, len(sidAndGids))
	for _, sidAndGid := range sidAndGids {

		// 组合需要加载的公会ID和副本ID。保证活跃公会和参加了公会副本玩法的公会，公会数据和副本数据都加载
		loadGids := make([]uint64, 0, len(sidAndGid.Gids))
		gidJoinDungeon := make([]uint64, 0, len(sidAndGid.Gids))
		for gid, lastAccessTm := range sidAndGid.Gids {
			if now-lastAccessTm <= GuildAliveInternal {
				loadGids = append(loadGids, gid)
				if !sidAndGid.GidForDungeon[gid] { // 没参加副本玩法，活跃公会也加载副本数据
					gidJoinDungeon = append(gidJoinDungeon, gid)
				}

				continue
			}
			if sidAndGid.GidForDungeon == nil {
				continue
			}
			if sidAndGid.GidForDungeon[gid] { // 参加公会副本了，照样加载
				loadGids = append(loadGids, gid)
			}
		}

		for gid, join := range sidAndGid.GidForDungeon {
			if !join {
				// 这里可以continue，未参加副本玩法的活跃公会ID，已在上面放到列表中了
				continue
			}
			gidJoinDungeon = append(gidJoinDungeon, gid)
		}

		flag := uint32(0)
		if len(loadGids) > 0 {
			r.ropCr.GetSomeGuildMCall(loadGids)
			flag += 1
		}

		if len(gidJoinDungeon) > 0 {
			r.ropCr.GetSomeGuildDungeonMCall(gidJoinDungeon)
			flag += 1
		}

		guildCount = append(guildCount, flag)
	}

	r.ropCr.GetSomeGuildDungeonResetMCall([]uint32{recv.ArenaId})

	r.client.Append("hget", RedisHashGuildDungeonSeason, "id")

	r.ropCr.GetSomeGuildPeakPhaseUserBakMCall([]uint32{recv.ArenaId})
	r.ropCr.GetSomeGuildWorldBossUserBakMCall([]uint32{recv.ArenaId})
	r.ropCr.GetAllGuildCrossUserMCallSK(recv.ArenaId)
	r.ropCr.GetAllGuildSeasonArenaBakMCallSK(recv.ArenaId)

	for i := 0; i < len(guildCount); i++ {
		if guildCount[i] == 0 {
			continue
		}
		if guildCount[i] >= 2 {
			guildData, err := r.ropCr.GetSomeGuildByReply(r.client.GetReply())
			if err != nil {
				l4g.Errorf("LoadGuild: getGuildByReply error. err:%s", err)
				ctx.Stop() // todo 确认下这里直接stop的影响
			}
			for gid, guild := range guildData {
				retData.Guilds[gid] = guild
			}
		}

		dungeonData, err := r.ropCr.GetSomeGuildDungeonByReply(r.client.GetReply())
		if err != nil {
			l4g.Errorf("LoadGuild: getGuildDungeonByReply error. err:%s", err)
			ctx.Stop() // todo 确认下这里直接stop的影响
		}
		for gid, dungeon := range dungeonData {
			retData.Dungeons[gid] = dungeon
		}
	}

	dungeonResets, err := r.ropCr.GetSomeGuildDungeonResetByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get guildDungeon reset data error: %s", err)
		retData.Ret = uint32(r2c.RET_ERROR)
		return retData
	}

	if dungeonResets != nil {
		retData.DungeonReset = dungeonResets[recv.ArenaId]
	}

	seasonIdS, err := r.client.GetReply().Str()
	if err != nil {
		l4g.Errorf("get dungeon seasonId error. error:%s", err)
	} else {
		dungeonSeasonId, err := strconv.Atoi(seasonIdS)
		if err != nil {
			l4g.Errorf("get dungeon seasonId error. error:%s", err)
		} else {
			retData.DungeonSeasonId = uint32(dungeonSeasonId)
		}
	}

	guildPeakBak, err := r.ropCr.GetSomeGuildPeakPhaseUserBakByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("[FATAL] get GuildPeakPhaseUserBak data error: %s, partition:%d", err, recv.ArenaId)
	}
	if guildPeakBak != nil {
		retData.PeakBak = guildPeakBak[recv.ArenaId]
	}

	guildWorldBossBak, err := r.ropCr.GetSomeGuildWorldBossUserBakByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("[FATAL] get GuildWorldBossUserBak data error: %s, partition:%d", err, recv.ArenaId)
	}
	if guildWorldBossBak != nil {
		retData.WorldBossBak = guildWorldBossBak[recv.ArenaId]
	}

	guildCrossUsers, err := r.ropCr.GetSomeGuildCrossUserByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("[FATAL] get GuildCrossUser data error: %s, partition:%d", err, recv.ArenaId)
	}
	retData.Users = guildCrossUsers

	seasonArenaBak, err := r.ropCr.GetSomeGuildSeasonArenaBakByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("[FATAL] get GuildWorldBossUserBak data error: %s, partition:%d", err, recv.ArenaId)
	}
	if seasonArenaBak != nil {
		retData.SeasonArenaBak = seasonArenaBak
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.LoadGuild recv:%+v, aliveGuildCount:%d, useTime:%v",
		recv, len(retData.Guilds), endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildCreate(recv *r2c.C2R_GuildCreate) *r2c.R2C_GuildCreate {
	startTime := time.AccurateNow()
	retData := &r2c.R2C_GuildCreate{
		Ret:   uint32(r2c.RET_OK),
		Guild: recv.Guild,
	}
	l4g.Debugf("GuildCreate. recv:%+v", recv)

	if recv.Guild == nil {
		l4g.Errorf("GuildCreate. guild is empty, recv:%+v", recv)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	if recv.Guild.Name == "" {
		l4g.Errorf("GuildCreate: name is nil. msg:%s", recv)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	guild := recv.Guild

	gidStr := strconv.FormatUint(guild.Id, 10)

	// 设置name
	r.client.Append("HSETNX", RedisHashGuildName, guild.Name, gidStr)
	// 获取设置name结果
	if result, err := r.client.GetReply().Int(); err != nil {
		l4g.Errorf("setName error, set newName reply msg:%s err:%s", recv, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		return retData
	} else {
		if result == 0 {
			retData.Ret = uint32(ret.RET_GUILD_NAME_USED)
			return retData
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.CreateGuild recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildSetName(recv *r2c.C2R_GuildSetName) *r2c.R2C_GuildSetName {
	startTime := time.AccurateNow()
	retData := &r2c.R2C_GuildSetName{
		Ret:         uint32(r2c.RET_OK),
		Id:          recv.Id,
		OldName:     recv.OldName,
		NewName:     recv.NewName,
		ClientMsgId: recv.ClientMsgId,
	}
	l4g.Debugf("GuildSetName. recv:%+v", recv)

	if recv.NewName == "" {
		l4g.Errorf("setName error msg:%s", recv)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	gidStr := strconv.FormatUint(recv.Id, 10)

	// 设置name
	r.client.Append("HSETNX", RedisHashGuildName, recv.NewName, gidStr)
	// 获取设置name结果
	if result, err := r.client.GetReply().Int(); err != nil {
		l4g.Errorf("setName error, set newName reply msg:%s err:%s", recv, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		return retData
	} else {
		if result == 0 {
			retData.Ret = uint32(ret.RET_GUILD_NAME_USED)
			return retData
		}
	}
	if recv.OldName != "" {
		r.client.Append("HDel", RedisHashGuildName, recv.OldName)
		// 获取删除oldName结果
		if _, err := r.client.GetReply().Int(); err != nil {
			l4g.Errorf("setName error, del oldName reply msg:%s err:%s", recv, err)
			retData.Ret = uint32(r2c.RET_ERROR)
			return retData
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.CreateGuild recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildDelName(recv *r2c.C2R_GuildDeleteName) bool {
	startTime := time.AccurateNow()

	l4g.Debugf("GuildDelName. recv:%+v", recv)

	if recv.DelName == "" {
		l4g.Errorf("delName error msg:%s", recv)
		return false
	}

	r.client.Append("HDel", RedisHashGuildName, recv.DelName)
	// 获取删除oldName结果
	if _, err := r.client.GetReply().Int(); err != nil {
		l4g.Errorf("delName error, del oldName reply msg:%s err:%s", recv, err)
		return false
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildDelName recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return true
}

func (r *RedisActor) GuildSearch(recv *r2c.C2R_GuildSearch) *r2c.R2C_GuildSearch {
	startTime := time.AccurateNow()

	retData := &r2c.R2C_GuildSearch{
		Ret:  uint32(r2c.RET_OK),
		Id:   recv.Id,
		Name: recv.Name,
		Uid:  recv.Uid,
		Sid:  recv.Sid,
	}

	l4g.Debugf("GuildSearch. recv:%+v", recv)

	if recv.Id > 0 {
		guild, err := r.ropCr.GetGuild(recv.Id)
		if err != nil {
			l4g.Errorf("GuildSearch error msg:%s", recv)
			retData.Ret = uint32(ret.RET_ERROR)
			return retData
		}
		retData.Guild = guild
		guildDungeon, err := r.ropCr.GetGuildDungeon(recv.Id)
		if err != nil {
			l4g.Errorf("GuildSearch error msg:%s", recv)
			retData.Ret = uint32(ret.RET_ERROR)
			return retData
		}
		retData.Dungeon = guildDungeon
	} else if recv.Name != "" {
		r.client.Append("HGet", RedisHashGuildName, recv.Name)
		//
		result, err := r.client.GetReply().Str()
		if err != nil {
			l4g.Errorf("GuildSearch error, get id by name err. msg:%s err:%s", recv, err)
			retData.Ret = uint32(ret.RET_ERROR)
			return retData
		}
		gid, err := strconv.Atoi(result)
		if err != nil {
			l4g.Errorf("GuildSearch error, gitStr to uint64 error. msg:%s gidStr:%s err:%s", recv, result, err)
			retData.Ret = uint32(ret.RET_ERROR)
			return retData
		}
		guild, err := r.ropCr.GetGuild(uint64(gid))
		if err != nil {
			l4g.Errorf("GuildSearch error msg:%s", recv)
			retData.Ret = uint32(ret.RET_ERROR)
			return retData
		}
		retData.Guild = guild
		guildDungeon, err := r.ropCr.GetGuildDungeon(uint64(gid))
		if err != nil {
			l4g.Errorf("GuildSearch error msg:%s", recv)
			retData.Ret = uint32(ret.RET_ERROR)
			return retData
		}
		retData.Dungeon = guildDungeon
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildSearch recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildLogList(recv *r2c.C2R_GuildLogList) *r2c.R2C_GuildLogList {
	startTime := time.AccurateNow()

	retData := &r2c.R2C_GuildLogList{
		Ret: uint32(r2c.RET_OK),
		Uid: recv.Uid,
		Sid: recv.Sid,
	}

	logs, err := r.ropCr.GetAllGuildLogInfoSK(recv.Gid)
	if err != nil {
		l4g.Errorf("L2R_GetGuildLog. get guild log error. gid:%d err:%s",
			recv.Gid, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildLogList recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildDonateLogList(recv *r2c.C2R_GuildDonateLogList) *r2c.R2C_GuildDonateLogList {
	startTime := time.AccurateNow()

	retData := &r2c.R2C_GuildDonateLogList{
		Ret: uint32(r2c.RET_OK),
		Uid: recv.Uid,
		Sid: recv.Sid,
	}

	logs, err := r.ropCr.GetAllGuildDonateLogInfoSK(recv.Gid)
	if err != nil {
		l4g.Errorf("L2R_GetGuildDonateLog. get guild log error. gid:%d err:%s",
			recv.Gid, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildDonateLogList recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildLoadOne(recv *r2c.C2R_GuildLoadOne) *r2c.R2C_GuildLoadOne {
	startTime := time.AccurateNow()

	retData := &r2c.R2C_GuildLoadOne{
		Ret:       uint32(r2c.RET_OK),
		Gid:       recv.Gid,
		TargetSid: recv.TargetSid,
		TargetUid: recv.TargetUid,
	}

	r.ropCr.GetSomeGuildMCall([]uint64{recv.Gid})
	r.ropCr.GetSomeGuildDungeonMCall([]uint64{recv.Gid})

	guilds, err := r.ropCr.GetSomeGuildByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("L2R_GuildLoadOne. get guild error. gid:%d err:%s",
			recv.Gid, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		return retData
	}

	retData.Guild = guilds[recv.Gid]

	dungeons, err := r.ropCr.GetSomeGuildDungeonByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("L2R_GuildLoadOne. get guildDungeon error. gid:%d err:%s",
			recv.Gid, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		return retData
	}

	retData.Dungeon = dungeons[recv.Gid]

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildLoadOne recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildSave(recv *r2c.C2R_GuildSave) {
	startTime := time.AccurateNow()

	l4g.Debug("save guild %s", recv)
	calls := 0
	if len(recv.Changes) > 0 {
		r.ropCr.SetSomeGuildMCall(recv.Changes)
		calls++
	}

	if len(recv.GuildChangeLogs) > 0 {
		for gid, datas := range recv.GuildChangeLogs {
			r.ropCr.SetSomeGuildLogInfoMCallSK(gid, datas.Logs)
			calls++
		}
	}
	if len(recv.GuildDeleteLogs) > 0 {
		for gid, datas := range recv.GuildDeleteLogs {
			r.ropCr.RemSomeGuildLogInfoMCallSK(gid, datas.Ids)
			calls++
		}
	}

	if len(recv.GuildDonateLogs) > 0 {
		for gid, datas := range recv.GuildDonateLogs {
			r.ropCr.SetSomeGuildDonateLogInfoMCallSK(gid, datas.Logs)
			calls++
		}
	}
	if len(recv.GuildDonateDeleteLogs) > 0 {
		for gid, datas := range recv.GuildDonateDeleteLogs {
			r.ropCr.RemSomeGuildLogInfoMCallSK(gid, datas.Ids)
			calls++
		}
	}

	if len(recv.Deletes) > 0 {
		r.ropCr.RemSomeGuildMCall(recv.Deletes)
		calls++
		for _, id := range recv.Deletes {
			r.ropCr.DelAllGuildLogInfoMCallSK(id)
			calls++
			//r.ropCr.DelAllGuildDungeonLogMCallSK(gid)
			//calls++
			//r.ropCr.DelAllGuildDungeonMessageBoardLogMCallSK(gid)
			//calls++
			r.ropCr.DelAllGuildDonateLogInfoMCallSK(id)
			calls++
		}
	}

	if len(recv.SidAndGids) > 0 {
		r.ropCr.SetSomeSidAndGidForGuildLoadMCall(recv.SidAndGids)
		calls++
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Error("save guild error: %s %s", recv, reply.Err)
			ctx.Stop()
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildSave guildChangesCount:%d guildDelete:%+v, useTime:%v",
		len(recv.Changes), recv.Deletes, endTime.Sub(startTime))
}

func (r *RedisActor) GuildSavePeakBakData(recv *r2c.C2R_GuildSavePeakBakData) {
	startTime := time.AccurateNow()

	l4g.Debug("guild save peak bak data %s", recv)

	err := r.ropCr.SetSomeGuildPeakPhaseUserBak([]*cr.GuildPeakPhaseUserBak{recv.Data})
	if err != nil {
		l4g.Errorf("[FATAL] save GuildPeakPhaseUserBak error:%s, recv:%s ", err, recv)
		return
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildSavePeakBakData useTime:%v", endTime.Sub(startTime))
}

func (r *RedisActor) GuildSaveWorldBossBakData(recv *r2c.C2R_GuildSaveWorldBossBakData) {
	startTime := time.AccurateNow()

	l4g.Debug("guild save worldBoss bak data %s", recv)

	err := r.ropCr.SetSomeGuildWorldBossUserBak([]*cr.GuildWorldBossUserBak{recv.Data})
	if err != nil {
		l4g.Errorf("[FATAL] save GuildWorldBossUserBak error:%s, recv:%s ", err, recv)
		return
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildSaveWorldBossBakData: useTime:%v", endTime.Sub(startTime))
}

func (r *RedisActor) GuildUserSave(recv *r2c.C2R_GuildUserSave) {
	startTime := time.AccurateNow()

	l4g.Debug("guild user save %s", recv)

	err := r.ropCr.SetSomeGuildCrossUserSK(recv.AreaId, recv.Changes)
	if err != nil {
		l4g.Errorf("[FATAL] save SetSomeGuildCrossUserSK error:%s, recv:%s ", err, recv)
		return
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildUserSave: useTime:%v", endTime.Sub(startTime))
}

func (r *RedisActor) GuildSaveSeasonArenaBakData(recv *r2c.C2R_GuildSaveSeasonArenaBakData) {
	startTime := time.AccurateNow()

	l4g.Debug("guild save seasonArena bak data %s", recv)

	err := r.ropCr.SetSomeGuildSeasonArenaBakSK(recv.AreaId, []*cr.GuildSeasonArenaBak{recv.Data})
	if err != nil {
		l4g.Errorf("[FATAL] save GuildSaveSeasonArenaBakData error:%s, recv:%s ", err, recv)
		return
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildSaveSeasonArenaBakData: useTime:%v", endTime.Sub(startTime))
}

func (r *RedisActor) GuildDungeonLogList(recv *r2c.C2R_GuildDungeonLogList) *r2c.R2C_GuildDungeonLogList {
	startTime := time.AccurateNow()

	retData := &r2c.R2C_GuildDungeonLogList{
		Ret: uint32(r2c.RET_OK),
		Uid: recv.Uid,
		Sid: recv.Sid,
	}

	logs, err := r.ropCr.GetAllGuildDungeonLogSK(recv.Gid)
	if err != nil {
		l4g.Errorf("L2R_GetGuildDungeonLog. get dungeon log error. gid:%d err:%s",
			recv.Gid, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildDungeonLogList recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GuildDungeonSave(recv *r2c.C2R_GuildDungeonSave) {
	startTime := time.AccurateNow()

	l4g.Debug("save guild dungeon %s", recv)
	calls := 0
	if len(recv.Changes) > 0 {
		r.ropCr.SetSomeGuildDungeonMCall(recv.Changes)
		calls++
	}

	if len(recv.Deletes) > 0 {
		r.ropCr.RemSomeGuildDungeonMCall(recv.Deletes)
		calls++
		for _, id := range recv.Deletes {
			r.ropCr.DelAllGuildDungeonLogMCallSK(id)
			calls++
			//r.ropCr.DelAllGuildDungeonMessageBoardLogMCallSK(gid)
			//calls++
		}
	}

	if len(recv.DungeonChangeLogs) > 0 {
		for gid, datas := range recv.DungeonChangeLogs {
			r.ropCr.SetSomeGuildDungeonLogMCallSK(gid, datas.Logs)
			calls++
		}
	}
	if len(recv.DungeonDeleteLogs) > 0 {
		for gid, datas := range recv.DungeonDeleteLogs {
			r.ropCr.RemSomeGuildDungeonLogMCallSK(gid, datas.Ids)
			calls++
		}
	}

	if recv.ResetData != nil {
		r.ropCr.SetSomeGuildDungeonResetMCall([]*cr.GuildDungeonReset{recv.ResetData})
		calls++
		r.client.Append("hset", RedisHashGuildDungeonSeason, "id", recv.DungeonSeasonId)
		calls++
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Error("save guild dungeon error: %s %s", recv, reply.Err)
			ctx.Stop()
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildDungeonSave dungeonChangesCount:%d dungeonDelete:%+v, useTime:%v",
		len(recv.Changes), recv.Deletes, endTime.Sub(startTime))
}

// TODO redis-pb-cli现在不支持这种双冒号的形式展示
func (r *RedisActor) RankGet(recv *r2c.C2R_RankGet) *r2c.R2C_RankGet {
	startTime := time.AccurateNow()

	//先查询所有的服务器的id

	rankIds := recv.GetRankIds()
	servers := recv.GetServers()

	retData := &r2c.R2C_RankGet{
		Ret:     uint32(r2c.RET_OK),
		RankIds: rankIds,
		Servers: recv.GetServers(),
	}

	//查询重置时间
	calls := 0
	for _, rankID := range rankIds {
		r.ropCr.GetSomeRankResetMCallSK(rankID, recv.GetServers())
		calls++
	}

	for i := 0; i < calls; i++ {
		resetData, err := r.ropCr.GetSomeRankResetByReply(r.ropCr.GetReply())
		if err != nil {
			l4g.Error("rank get data error: %v %s", recv, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
			return retData
		}
		rankReset := &r2c.RankReset{
			RankId: rankIds[i],
			Data:   resetData,
		}
		retData.Resets = append(retData.Resets, rankReset)
	}

	calls = 0
	for _, rankID := range recv.GetRankIds() {
		for _, sid := range recv.GetServers() {
			strKeyName := fmt.Sprintf("%d:%d", rankID, sid)
			r.ropCr.GetAllRankDataMCallSKs(strKeyName)
			calls++
		}
	}
	for i := 0; i < calls; i++ {
		rankData, err := r.ropCr.GetSomeRankDataByReply(r.ropCr.GetReply())
		if err != nil {
			l4g.Error("rank get data error: %v %s", recv, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
			return retData
		}
		ri := i / len(servers)
		rs := i % len(servers)
		info := &r2c.RankInfo{
			RankId: rankIds[ri],
			Sid:    servers[rs],
			Data:   rankData,
		}
		retData.Infos = append(retData.Infos, info)
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.RankGet recv:%+v, useTime:%v", recv, endTime.Sub(startTime))
	return retData
}

// TODO redis-pb-cli现在不支持这种双冒号的形式展示
func (r *RedisActor) RankGetLast(recv *r2c.C2R_RankGetLast) *r2c.R2C_RankGetLast {
	startTime := time.AccurateNow()

	//先查询所有的服务器的id

	rankId := recv.GetRankId()
	servers := recv.GetServers()

	retData := &r2c.R2C_RankGetLast{
		Ret:     uint32(r2c.RET_OK),
		RankId:  rankId,
		Servers: recv.GetServers(),
	}

	calls := 0
	for _, sid := range recv.GetServers() {
		strKeyName := fmt.Sprintf("%s:%d:%d", "old", rankId, sid)
		r.ropCr.GetAllRankDataMCallSKs(strKeyName)
		calls++
	}
	for i := 0; i < calls; i++ {
		rankData, err := r.ropCr.GetSomeRankDataByReply(r.ropCr.GetReply())
		if err != nil {
			l4g.Error("rank get data error: %v %s", recv, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
			return retData
		}
		info := &r2c.RankInfo{
			RankId: rankId,
			Sid:    servers[i],
			Data:   rankData,
		}
		retData.Infos = append(retData.Infos, info)
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.RankGetLast recv:%+v, useTime:%v", recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) RankReset(recv *r2c.C2R_RankReset) (retData *r2c.R2C_RankReset) {
	startTime := time.AccurateNow()

	//先查询所有的服务器的id

	retData = &r2c.R2C_RankReset{
		Ret:       uint32(r2c.RET_OK),
		RankId:    recv.GetRankId(),
		Sid:       recv.GetSid(),
		ResetTime: recv.GetResetTime(),
	}

	//删除老的数据
	oldKey := fmt.Sprintf("%s:%d:%d", "old", recv.GetRankId(), recv.GetSid())
	if err := r.ropCr.DelAllRankDataSKs(oldKey); err != nil {
		l4g.Errorf("RankReset: rank_id:%d sid:%d %s", recv.GetRankId(), recv.GetSid(), err)
		ctx.Stop()
		return
	}

	//获取新的数据
	nowKey := fmt.Sprintf("%d:%d", recv.GetRankId(), recv.GetSid())
	if datas, err := r.ropCr.GetAllRankDataSKs(nowKey); err != nil {
		l4g.Errorf("RankReset: rank_id:%d sid:%d %s", recv.GetRankId(), recv.GetSid(), err)
		ctx.Stop()
		return
	} else {
		//把数据复制到新的数据里面
		if len(datas) > 0 {
			insertDatas := make([]*cr.RankData, 0, len(datas))
			for _, v := range datas {
				insertDatas = append(insertDatas, v)
			}
			if err := r.ropCr.SetSomeRankDataSKs(oldKey, insertDatas); err != nil {
				l4g.Errorf("RankReset: rank_id:%d sid:%d %s", recv.GetRankId(), recv.GetSid(), err)
				ctx.Stop()
				return
			}
		}

		//删除新的数据并且写入新的数据
		call := 0
		r.ropCr.DelAllRankDataMCallSKs(nowKey)
		call++
		newReset := &cr.RankReset{
			RankId:        recv.GetRankId(),
			Sid:           recv.GetSid(),
			LastResetTime: recv.GetResetTime(),
		}
		r.ropCr.SetSomeRankResetMCallSK(recv.GetRankId(), []*cr.RankReset{newReset})
		call++
		for i := 0; i < call; i++ {
			if reply := r.ropCr.GetReply(); reply.Err != nil {
				l4g.Errorf("RankReset: rank_id:%d sid:%d %s", recv.GetRankId(), recv.GetSid(), reply.Err)
				ctx.Stop()
				return
			}
		}
	}
	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.RankReset recv:%+v, useTime:%v", recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) RankSave(recv *r2c.C2R_RankSave) bool {
	startTime := time.AccurateNow()

	//先查询所有的服务器的id

	calls := 0
	counts := 0
	if len(recv.GetDatas()) > 0 {
		for _, data := range recv.GetDatas() {
			if len(data.GetValues()) > 0 {
				counts += len(data.GetValues())
				var nowKey string
				nowKey = fmt.Sprintf("%d:%d", data.GetRankId(), data.GetSid())
				if recv.Last {
					nowKey = fmt.Sprintf("old:%d:%d", data.GetRankId(), data.GetSid())
				}

				r.ropCr.SetSomeRankDataMCallSKs(nowKey, data.GetValues())
				calls++
			}
			if len(data.GetDeletes()) > 0 {
				counts += len(data.GetDeletes())
				var nowKey string
				nowKey = fmt.Sprintf("%d:%d", data.GetRankId(), data.GetSid())
				if recv.Last {
					nowKey = fmt.Sprintf("old:%d:%d", data.GetRankId(), data.GetSid())
				}
				r.ropCr.RemSomeRankDataMCallSKs(nowKey, data.GetDeletes())
				calls++
			}
		}
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.ropCr.GetReply(); reply.Err != nil {
			l4g.Error("save common rank error: %+v %s", recv, reply.Err)
			ctx.Stop()
		}
	}

	endTime := time.AccurateNow()
	l4g.Infof("redisActor.RankSave counts:%d, useTime:%v", counts, endTime.Sub(startTime))
	return true
}

func (r *RedisActor) WorldBossLoad(bossLoad *r2c.C2R_WorldBossLoad) *r2c.R2C_WorldBossLoad {
	retData := &r2c.R2C_WorldBossLoad{
		Ret:         uint32(r2c.RET_OK),
		PartitionId: bossLoad.PartitionId,
		TypeUsers:   make(map[uint32]*cr.WorldBossTypeUsersBack),
	}

	// base data
	r.ropCr.GetWorldBossMCallSK(bossLoad.PartitionId)
	// rooms
	r.ropCr.GetAllWorldBossRoomMCallSK(bossLoad.PartitionId)
	// type user
	for _, worldBossType := range bossLoad.BossType {
		key := uint64(bossLoad.PartitionId)<<32 | uint64(worldBossType)
		r.ropCr.GetAllWorldBossUserBackMCallSK(key)
	}
	// users
	r.ropCr.GetAllWorldBossUserMCallSK(bossLoad.PartitionId)
	// user_rank_bak
	r.ropCr.GetAllWorldBossUserRankBakMCallSK(bossLoad.PartitionId)
	// user to peak arena
	r.ropCr.GetAllWorldBossUserToPeakArenaMCallSK(bossLoad.PartitionId)
	/************************REPLY*******************************/
	// base data
	baseData, err := r.ropCr.GetWorldBossByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get worldBoss base data error: %d %s", bossLoad.PartitionId, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.BaseData = baseData

	// rooms
	roomDatas, err := r.ropCr.GetSomeWorldBossRoomByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get worldBoss room data error: %d %s", bossLoad.PartitionId, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Rooms = roomDatas

	for _, worldBossType := range bossLoad.BossType {
		// users
		userDatas, err := r.ropCr.GetSomeWorldBossUserBackByReply(r.client.GetReply())
		if err != nil {
			l4g.Errorf("get worldBoss user data error: %d %s", bossLoad.PartitionId, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		retData.TypeUsers[worldBossType] = &cr.WorldBossTypeUsersBack{
			Users: userDatas,
		}
	}

	// users
	userDatas, err := r.ropCr.GetSomeWorldBossUserByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get worldBoss user data error: %d %s", bossLoad.PartitionId, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Users = userDatas

	// users
	userRankBakDatas, err := r.ropCr.GetSomeWorldBossUserRankBakByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get worldBoss user rank bak data error: %d %s", bossLoad.PartitionId, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.UserRankBak = userRankBakDatas

	// user to top arena
	userToPeakArenas, err := r.ropCr.GetSomeWorldBossUserToPeakArenaByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get worldBoss user to peak arena data error: %d %s", bossLoad.PartitionId, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.UserToPeakArena = userToPeakArenas

	return retData
}

func (r *RedisActor) WorldBossSaveUser(data *r2c.C2R_WorldBossSaveUser) {
	if data.PartitionId == 0 {
		l4g.Errorf("save worldBoss user data failed: partitionID: %d", data.PartitionId)
		return
	}

	call := 0
	if len(data.Changes) > 0 {
		r.ropCr.SetSomeWorldBossUserMCallSK(data.PartitionId, data.Changes)
		call++
	}
	if len(data.Deletes) > 0 {
		r.ropCr.RemSomeWorldBossUserMCallSK(data.PartitionId, data.Deletes)
		call++
	}
	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("save worldBoss user data failed: data: %+v err: %s", data, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) WorldBossSaveRoom(data *r2c.C2R_WorldBossSaveRoom) {
	if data.PartitionId == 0 {
		l4g.Errorf("save worldBoss room data failed: partitionID: %d", data.PartitionId)
		return
	}

	call := 0
	if len(data.Changes) > 0 {
		r.ropCr.SetSomeWorldBossRoomMCallSK(data.PartitionId, data.Changes)
		call++
	}
	if len(data.Deletes) > 0 {
		r.ropCr.RemSomeWorldBossRoomMCallSK(data.PartitionId, data.Deletes)
		call++
	}
	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("save worldBoss room data failed: data: %+v err: %s", data, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) WorldBossSaveBaseData(data *r2c.C2R_WorldBossSaveBaseData) {
	if data.PartitionId == 0 {
		l4g.Errorf("WorldBossSaveBaseData failed: partitionID is 0")
		return
	}

	r.ropCr.SetWorldBossMCallSK(data.PartitionId, data.BaseData)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("del worldBoss base data failed: partitionID: %d err: %s", data.PartitionId, reply.Err)
		ctx.Stop()
	}
}

func (r *RedisActor) WorldBossSaveUserRankBak(data *r2c.C2R_WorldBossSaveUserRankBak) {
	if data.PartitionId == 0 {
		l4g.Errorf("save worldBoss user rank bak data failed: partitionID: %d", data.PartitionId)
		return
	}

	call := 0
	if len(data.Changes) > 0 {
		r.ropCr.SetSomeWorldBossUserRankBakMCallSK(data.PartitionId, data.Changes)
		call++
	}
	if len(data.Deletes) > 0 {
		r.ropCr.RemSomeWorldBossUserRankBakMCallSK(data.PartitionId, data.Deletes)
		call++
	}
	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("save worldBoss user rank bak data failed: data: %+v err: %s", data, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) WorldBossUserCleanAll(partitionID uint32) {
	if partitionID == 0 {
		l4g.Errorf("WorldBossUserCleanAll failed: partitionID is 0")
		return
	}
	r.ropCr.DelAllWorldBossUserMCallSK(partitionID)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("del worldBoss user data failed: partitionID: %d err: %s", partitionID, reply.Err)
		ctx.Stop()
	}
}

func (r *RedisActor) WorldBossRoomCleanAll(partitionID uint32) {
	if partitionID == 0 {
		l4g.Errorf("WorldBossRoomCleanAll failed: partitionID is 0")
		return
	}
	r.ropCr.DelAllWorldBossRoomMCallSK(partitionID)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("del worldBoss room data failed: partitionID: %d err: %s", partitionID, reply.Err)
		ctx.Stop()
	}
}

func (r *RedisActor) WorldBossUserRankBakCleanAll(partitionID uint32) {
	if partitionID == 0 {
		l4g.Errorf("WorldBossUserRankBakCleanAll failed: partitionID is 0")
		return
	}
	r.ropCr.DelAllWorldBossUserRankBakMCallSK(partitionID)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("del worldBoss user rank data failed: partitionID: %d err: %s", partitionID, reply.Err)
		ctx.Stop()
	}
}

func (r *RedisActor) WorldBossSaveUserToPeakArena(data *r2c.C2R_WorldBossSaveUserToPeakArena) {
	if data.PartitionId == 0 {
		l4g.Errorf("save worldBoss user to peak arena failed: partitionID: %d", data.PartitionId)
	}
	call := 0
	if len(data.Changes) > 0 {
		r.ropCr.SetSomeWorldBossUserToPeakArenaMCallSK(data.PartitionId, data.Changes)
		call++
	}
	if len(data.Deletes) > 0 {
		r.ropCr.RemSomeWorldBossUserToPeakArenaMCallSK(data.PartitionId, data.Deletes)
		call++
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("save worldBoss user to peak arena failed: data: %+v err: %s", data, reply.Err)
			ctx.Stop()
		}
	}

}

func (r *RedisActor) DisorderLandLoad(partitionID uint32) *r2c.R2C_DisorderLandLoad {
	retData := &r2c.R2C_DisorderLandLoad{
		Ret:         uint32(r2c.RET_OK),
		PartitionId: partitionID,
	}

	// base data
	r.ropCr.GetDisorderLandMCallSK(partitionID)
	// users
	r.ropCr.GetAllDisorderLandUserMCallSK(partitionID)

	/************************REPLY*******************************/
	// base data
	baseData, err := r.ropCr.GetDisorderLandByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get disorderLand base data error: %d %s", partitionID, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.BaseData = baseData

	// users
	userDatas, err := r.ropCr.GetSomeDisorderLandUserByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("get disorderLand user data error: %d %s", partitionID, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Users = userDatas

	return retData
}

func (r *RedisActor) DisorderLandSaveUser(data *r2c.C2R_DisorderLandSaveUser) {
	if data.PartitionId == 0 {
		l4g.Errorf("save disorderLand user data failed: partitionID: %d", data.PartitionId)
		return
	}

	call := 0
	if len(data.Changes) > 0 {
		r.ropCr.SetSomeDisorderLandUserMCallSK(data.PartitionId, data.Changes)
		call++
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("save disorderLand user data failed: data: %+v err: %s", data, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) DisorderLandSaveBaseData(data *r2c.C2R_DisorderLandSaveBaseData) {
	if data.PartitionId == 0 {
		l4g.Errorf("DisorderLandSaveBaseData failed: partitionID is 0")
		return
	}

	r.ropCr.SetDisorderLandMCallSK(data.PartitionId, data.BaseData)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("del disorderLand base data failed: partitionID: %d err: %s", data.PartitionId, reply.Err)
		ctx.Stop()
	}
}

func (r *RedisActor) DisorderLandUserCleanAll(partitionID uint32) {
	if partitionID == 0 {
		l4g.Errorf("DisorderLandUserCleanAll failed: partitionID is 0")
		return
	}
	r.ropCr.DelAllDisorderLandUserMCallSK(partitionID)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("del disorderLand user data failed: partitionID: %d err: %s", partitionID, reply.Err)
		ctx.Stop()
	}
}

func (r *RedisActor) PeakLoad(partition uint32) *r2c.R2C_PeakLoad {
	startTime := time.AccurateNow()
	retData := &r2c.R2C_PeakLoad{
		Ret:       uint32(r2c.RET_OK),
		Partition: partition,
	}

	if partition == 0 {
		l4g.Errorf("LoadPeak. param err, no partition")
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	peak, err := r.ropCr.GetPeakSK(partition)
	if err != nil {
		l4g.Errorf("LoadPeak. get peak error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.Peak = peak

	matches, err := r.ropCr.GetAllPeakMatchSK(partition)
	if err != nil {
		l4g.Errorf("LoadPeak. get matches top error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.Matches = matches

	fighters, err := r.ropCr.GetAllPeakFighterSK(partition)
	if err != nil {
		l4g.Errorf("LoadPeak. get fighters error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.Fighters = fighters

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.PeakLoad partition:%d, peak:%+v, matchCount:%d, fighterCount:%d, useTime:%v",
		partition, peak, len(matches), len(fighters), endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) PeakSave(msg *r2c.C2R_PeakSave) {
	l4g.Debugf("redisActor.PeakSave partition:%d, data:%+v", msg.Partition, msg)
	startTime := time.AccurateNow()

	if msg.Partition == 0 {
		l4g.Errorf("PeakSave. param err, no partition")
		return
	}

	call := 0
	if msg.Peak != nil {
		r.ropCr.SetPeakMCallSK(msg.Partition, msg.Peak)
		call++
	}

	if len(msg.Matches) > 0 {
		r.ropCr.SetSomePeakMatchMCallSK(msg.Partition, msg.Matches)
		call++
	}

	if len(msg.Fighters) > 0 {
		r.ropCr.SetSomePeakFighterMCallSK(msg.Partition, msg.Fighters)
		call++
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("PeakSave error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	if msg.Peak != nil {
		l4g.Infof("redisActor.PeakSave partition:%d, peak:%+v, matchCount:%d, fighterCount:%d, useTime:%v",
			msg.Partition, msg.Peak, len(msg.Matches), len(msg.Fighters), endTime.Sub(startTime))
	}
}

func (r *RedisActor) PeakClearSeason(msg *r2c.C2R_PeakClearSeason) {
	l4g.Debugf("redisActor.PeakClearSeason partition:%d, data:%+v", msg.Partition, msg)
	startTime := time.AccurateNow()

	if msg.Partition == 0 {
		l4g.Errorf("PeakInit. param err, no partition")
		return
	}

	call := 0

	r.ropCr.DelAllPeakMatchMCallSK(msg.Partition)
	call++

	r.ropCr.DelAllPeakFighterMCallSK(msg.Partition)
	call++

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("PeakClearSeason error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.PeakClearSeason partition:%d useTime:%v",
		msg.Partition, endTime.Sub(startTime))
}

func (r *RedisActor) PeakClearPhase(msg *r2c.C2R_PeakClearPhase) {
	l4g.Debugf("redisActor.PeakClearPhase partition:%d, data:%+v", msg.Partition, msg)
	startTime := time.AccurateNow()

	if msg.Partition == 0 {
		l4g.Errorf("PeakInit. param err, no partition")
		return
	}

	call := 0

	r.ropCr.DelAllPeakMatchMCallSK(msg.Partition)
	call++

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("PeakClearPhase error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.PeakClearPhase partition:%d useTime:%v",
		msg.Partition, endTime.Sub(startTime))
}

//nolint:funlen
func (r *RedisActor) GSTLoad(partition uint32) *r2c.R2C_GSTLoad {
	retData := &r2c.R2C_GSTLoad{
		Ret: uint32(r2c.RET_OK),
	}

	if partition == 0 {
		l4g.Errorf("GSTLoad. param err, no partition")
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}

	gstM, err := r.ropCr.GetGSTManagerSK(partition)
	if err != nil {
		l4g.Errorf("GSTLoad. get GST error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.Mgr = gstM
	if gstM == nil || gstM.Sta == nil || gstM.Sta.State == nil || gstM.Sta.State.SeasonId == 0 {
		return retData
	}

	seasonId := gstM.Sta.State.SeasonId
	key := uint64(seasonId)<<32 | uint64(partition) //nolint:mnd
	strKey := strconv.FormatUint(key, 10)

	seasonGuilds, err := r.ropCr.GetAllGSTGuildManagerSKs(strKey)
	if err != nil {
		l4g.Errorf("GSTLoad. get seasonGuilds error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.GuildMgr = seasonGuilds

	seasonGuildUsers, err := r.ropCr.GetAllGSTGuildUserManagerSKs(strKey)
	if err != nil {
		l4g.Errorf("GSTLoad. get seasonGuildUsers error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.GuildUserMgr = seasonGuildUsers

	seasonGroups, err := r.ropCr.GetAllGSTGroupManagerSKs(strKey)
	if err != nil {
		l4g.Errorf("GSTLoad. get seasonGroups error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.GroupMgr = seasonGroups

	seasonLogs, err := r.ropCr.GetAllGSTGuildLogManagerSK(partition)
	if err != nil {
		l4g.Errorf("GSTLoad. get seasonLogs error: %v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.GuildLogMgr = seasonLogs

	rankBak, err := r.ropCr.GetAllGSTGuildRankBakSK(partition)
	if err != nil {
		l4g.Errorf("GstLoad. get guild rank bak err:%v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.GuildRankBak = rankBak

	dragonGlobal, err := r.ropCr.GetGSTDragonGlobalSK(key)
	if err != nil {
		l4g.Errorf("GSTLoad. get dragonGlobal error: %v, seasonId:%d partition %d", err, seasonId, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.DragonGlobal = dragonGlobal

	dragonGuilds, err := r.ropCr.GetAllGSTDragonManagerSKs(strKey)
	if err != nil {
		l4g.Errorf("GSTLoad. get dragonGuilds error: %v, seasonId:%d, partition:%d", err, seasonId, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.DragonGuild = dragonGuilds

	commonBak, err := r.ropCr.GetAllGSTCommonBakSK(partition)
	if err != nil {
		l4g.Errorf("GstLoad. get common bak err:%v, partition:%d", err, partition)
		retData.Ret = uint32(ret.RET_ERROR)
		return retData
	}
	retData.CommonBak = commonBak

	if gstM.Sta.MobRound != 0 {
		mobKey := uint64(gstM.Sta.MobRound)<<32 | uint64(partition) //nolint:mnd
		strMobKey := strconv.FormatUint(mobKey, 10)
		mobRanks, err := r.ropCr.GetAllGSTMobRankManagerSKs(strMobKey)
		if err != nil {
			l4g.Errorf("GSTLoad. GetAllGSTMobManagerSKs error: %v,sta: %+v", err, gstM.Sta)
			retData.Ret = uint32(ret.RET_ERROR)
			return retData
		}
		retData.GuildMobRank = mobRanks
	}

	return retData
}

func (r *RedisActor) GSTSave(msg *r2c.C2R_GSTSave) {
	l4g.Debugf("redisActor.GSTSave data:%+v", msg)
	if msg.Partition == 0 || msg.SeasonId == 0 {
		l4g.Errorf("GSTSave. param err")
		return
	}

	call := 0
	if msg.Mgr != nil {
		r.ropCr.SetGSTManagerMCallSK(msg.Partition, msg.Mgr)
		call++
	}

	key := uint64(msg.SeasonId)<<32 | uint64(msg.Partition) //nolint:mnd
	strKey := strconv.FormatUint(key, 10)

	if len(msg.GuildMgr) > 0 {
		r.ropCr.SetSomeGSTGuildManagerMCallSKs(strKey, msg.GuildMgr)
		call++
	}

	if len(msg.GuildUserMgr) > 0 {
		r.ropCr.SetSomeGSTGuildUserManagerMCallSKs(strKey, msg.GuildUserMgr)
		call++
	}

	if len(msg.GroupMgr) > 0 {
		r.ropCr.SetSomeGSTGroupManagerMCallSKs(strKey, msg.GroupMgr)
		call++
	}

	if len(msg.GuildRankBak) > 0 {
		r.ropCr.SetSomeGSTGuildRankBakMCallSK(msg.Partition, msg.GuildRankBak)
		call++
	}

	if msg.DragonGlobal != nil {
		r.ropCr.SetGSTDragonGlobalMCallSK(key, msg.DragonGlobal)
		call++
	}

	if len(msg.DragonGuild) > 0 {
		r.ropCr.SetSomeGSTDragonManagerMCallSKs(strKey, msg.DragonGuild)
		call++
	}

	if len(msg.CommonBak) > 0 {
		r.ropCr.SetSomeGSTCommonBakMCallSK(msg.Partition, msg.CommonBak)
		call++
	}

	if len(msg.GuildMobRank) > 0 {
		mobKey := uint64(msg.GuildMobRound)<<32 | uint64(msg.Partition)
		strMobKey := strconv.FormatUint(mobKey, 10)
		r.ropCr.SetSomeGSTMobRankManagerMCallSKs(strMobKey, msg.GuildMobRank)
		call++
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("GSTSave error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GSTClearGuild(msg *r2c.C2R_GSTClearGuild) {
	l4g.Debugf("redisActor.GSTClearGuild data:%+v", msg)
	if msg.Partition == 0 || msg.SeasonId == 0 || msg.GuildId == 0 {
		l4g.Errorf("GSTClearGuild. param err")
		return
	}
	key := uint64(msg.SeasonId)<<32 | uint64(msg.Partition)
	strKey := strconv.FormatUint(key, 10)

	r.ropCr.RemSomeGSTGuildManagerMCallSKs(strKey, []uint64{msg.GuildId})
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("GSTClearGuild error:%v msg:%v", reply.Err, msg)
		ctx.Stop()
	}
}

func (r *RedisActor) GSTResetLRound(msg *r2c.C2R_GSTResetLRound) {
	l4g.Debugf("redisActor.GSTResetLRound data:%+v", msg)
	if msg.Partition == 0 || msg.SeasonId == 0 {
		l4g.Errorf("GSTResetLRound. param err")
		return
	}
}

func (r *RedisActor) GSTResetRound(msg *r2c.C2R_GSTResetRound) {
	l4g.Debugf("redisActor.GSTResetRound data:%+v", msg)
	if msg.Partition == 0 || msg.SeasonId == 0 {
		l4g.Errorf("GSTResetRound. param err")
		return
	}
	call := 0
	key := uint64(msg.SeasonId)<<32 | uint64(msg.Partition)
	strKey := strconv.FormatUint(key, 10)
	r.ropCr.DelAllGSTGroupManagerMCallSKs(strKey)
	call++
	r.ropCr.DelAllGSTGuildLogManagerMCallSK(msg.Partition)
	call++
	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("GSTResetSeason error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GSTResetSeason(msg *r2c.C2R_GSTResetSeason) {
	l4g.Debugf("redisActor.GSTResetSeason data:%+v", msg)
	if msg.Partition == 0 || msg.SeasonId == 0 {
		l4g.Errorf("GSTResetSeason. param err")
		return
	}
	call := 0
	r.ropCr.DelGSTManagerMCallSK(msg.Partition)
	call++
	key := uint64(msg.SeasonId)<<32 | uint64(msg.Partition)
	strKey := strconv.FormatUint(key, 10)
	r.ropCr.DelAllGSTGuildManagerMCallSKs(strKey)
	call++
	r.ropCr.DelAllGSTGuildUserManagerMCallSKs(strKey)
	call++
	r.ropCr.DelAllGSTGroupManagerMCallSKs(strKey)
	call++
	r.ropCr.DelAllGSTGuildLogManagerMCallSK(msg.Partition)
	call++
	r.ropCr.DelAllGSTDragonManagerMCallSKs(strKey)
	call++
	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("GSTResetSeason error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GuildLoadSome(recv *r2c.C2R_GuildLoadSome) *r2c.R2C_GuildLoadSome {
	startTime := time.AccurateNow()

	retData := &r2c.R2C_GuildLoadSome{
		Ret:         uint32(r2c.RET_OK),
		Gids:        recv.Gids,
		ClientMsgId: recv.ClientMsgId,
	}

	r.ropCr.GetSomeGuildMCall(recv.Gids)
	r.ropCr.GetSomeGuildDungeonMCall(recv.Gids)

	guilds, err := r.ropCr.GetSomeGuildByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("L2R_GuildLoadOne. get guild error. gids:%+v err:%s",
			recv.Gids, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		return retData
	}

	retData.Guilds = guilds

	dungeons, err := r.ropCr.GetSomeGuildDungeonByReply(r.client.GetReply())
	if err != nil {
		l4g.Errorf("L2R_GuildLoadOne. get guildDungeon error. gids:%+v err:%s",
			recv.Gids, err)
		retData.Ret = uint32(r2c.RET_ERROR)
		return retData
	}

	retData.Dungeons = dungeons

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("redisActor.GuildLoadOne recv:%+v, useTime:%v",
		recv, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) GSTResetGuildRankBak(msg *r2c.C2R_GSTResetGuildRankBak) {
	l4g.Debugf("redisActor.GSTResetGuildRankBak data:%+v", msg)
	if msg.Partition == 0 {
		l4g.Errorf("GSTResetGuildRankBak. param err")
		return
	}
	call := 0
	r.ropCr.DelAllGSTGuildRankBakMCallSK(msg.Partition)
	call++
	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("GSTResetGuildRankBak error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

// 重置通用备份数据
func (r *RedisActor) GSTResetCommonBak(msg *r2c.C2R_GSTResetCommonBak) {
	l4g.Debugf("redisActor.GSTResetCommonBak data:%+v", msg)
	if msg.Partition == 0 {
		l4g.Errorf("GSTResetCommonBak. param err")
		return
	}
	data, err := r.ropCr.GetAllGSTCommonBakSK(msg.Partition)
	if err != nil {
		l4g.Errorf("GSTResetCommonBak error:%v msg:%v", err, msg)
		ctx.Stop()
	}
	changeData := make([]*cr.GSTCommonBak, 0, len(data))
	for _, bak := range data {
		change := msg.ResetDragon || msg.ResetChallenge
		if msg.ResetDragon {
			bak.DragonBak = nil
		}
		if msg.ResetChallenge {
			bak.ChallengeBak = nil
		}
		if change {
			changeData = append(changeData, bak)
		}
	}
	if len(changeData) > 0 {
		err = r.ropCr.SetSomeGSTCommonBakSK(msg.Partition, changeData)
		if err != nil {
			l4g.Errorf("GSTResetCommonBak error:%v msg:%v", err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GSTResetDragonRound(msg *r2c.C2R_GSTResetDragonRound) {
	l4g.Debugf("redisActor.GSTResetDragonRound data:%+v", msg)
	if msg.Partition == 0 || msg.SeasonId == 0 {
		l4g.Errorf("GSTResetDragonRound. param err")
		return
	}
	call := 0
	key := uint64(msg.SeasonId)<<32 | uint64(msg.Partition)
	strKey := strconv.FormatUint(key, 10)
	r.ropCr.DelAllGSTDragonManagerMCallSKs(strKey)
	call++

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("GSTResetDragonRound error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GSTSaveLog(msg *r2c.C2R_GSTSaveLog) {
	l4g.Debugf("redisActor.GSTSaveLog data:%+v", msg)
	if msg.Partition == 0 {
		l4g.Errorf("GSTSaveLog. param err")
		return
	}

	call := 0
	if len(msg.GuildLogMgrUpdate) > 0 {
		r.ropCr.SetSomeGSTGuildLogManagerMCallSK(msg.Partition, msg.GuildLogMgrUpdate)
		call++
	}

	if len(msg.GuildLogMgrDel) > 0 {
		r.ropCr.RemSomeGSTGuildLogManagerMCallSK(msg.Partition, msg.GuildLogMgrDel)
		call++
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("GSTSaveLog error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SeasonArenaLoad(partitionID uint32) *r2c.R2C_SeasonArenaLoad {
	if partitionID == 0 {
		l4g.Errorf("SeasonArenaSave. param err")
		return nil
	}

	ret := &r2c.R2C_SeasonArenaLoad{
		Ret: uint32(ret.RET_OK),
	}

	sta, err := r.ropCr.GetCrossSeasonArenaStaSK(partitionID)
	if err != nil {
		l4g.Errorf("SeasonArenaLoad. get sta error: %v, partition:%d", err, partitionID)
		ret.Ret = uint32(r2c.RET_ERROR)
	}
	ret.SeasonArena = sta

	users, err := r.ropCr.GetAllCrossSeasonArenaUserSK(partitionID)
	if err != nil {
		l4g.Errorf("SeasonArenaLoad. get users error: %v, partition:%d", err, partitionID)
		ret.Ret = uint32(r2c.RET_ERROR)
	}
	ret.Users = users

	fame, err := r.ropCr.GetCrossSeasonArenaFameSK(partitionID)
	if err != nil {
		l4g.Errorf("SeasonArenaLoad. get fame error: %v, partition:%d", err, partitionID)
		ret.Ret = uint32(r2c.RET_ERROR)
	}
	ret.Fame = fame

	bak, err := r.ropCr.GetAllCrossSeasonArenaAwardServerBakSK(partitionID)
	if err != nil {
		l4g.Errorf("SeasonArenaLoad. get bak error: %v, partition:%d", err, partitionID)
		ret.Ret = uint32(r2c.RET_ERROR)
	}
	ret.ServerBack = bak

	return ret
}

func (r *RedisActor) SeasonArenaSave(msg *r2c.C2R_SeasonArenaSave) {
	l4g.Debugf("redisActor.SeasonArenaSave data:%+v", msg)
	if msg.Partition == 0 {
		l4g.Errorf("SeasonArenaSave. param err")
		return
	}

	call := 0
	if msg.SeasonArena != nil {
		r.ropCr.SetCrossSeasonArenaStaMCallSK(msg.Partition, msg.SeasonArena)
		call++
	}

	if len(msg.Users) > 0 {
		r.ropCr.SetSomeCrossSeasonArenaUserMCallSK(msg.Partition, msg.Users)
		call++
	}

	if len(msg.UserDel) > 0 {
		r.ropCr.RemSomeCrossSeasonArenaUserMCallSK(msg.Partition, msg.UserDel)
		call++
	}

	if msg.Fame != nil {
		r.ropCr.SetCrossSeasonArenaFameMCallSK(msg.Partition, msg.Fame)
		call++
	}

	if msg.ClearServerBak {
		r.ropCr.DelAllCrossSeasonArenaAwardServerBakMCallSK(msg.Partition)
		call++
	}

	if msg.ServerBack != nil {
		r.ropCr.SetSomeCrossSeasonArenaAwardServerBakMCallSK(msg.Partition, msg.ServerBack)
		call++
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("SeasonArenaSaveLog error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) HotRankLoad(msg *r2c.C2R_HotRankLoad) *r2c.R2C_HotRankLoad {
	retMsg := &r2c.R2C_HotRankLoad{
		Ret: uint32(ret.RET_OK),
	}

	l4g.Debugf("redisActor.HotRankLoad data:%+v", msg)
	if msg.Partition == 0 {
		l4g.Errorf("C2R_HotRankLoad Load partition:%d err", msg.Partition)
		retMsg.Ret = uint32(r2c.RET_ERROR)
		return retMsg
	}

	var err error
	retMsg.CollectionLog, err = r.ropCr.GetAllCrossHotRankCollectionLog()
	if err != nil {
		l4g.Errorf("HotRankLoad. get rank collection error: %v", err)
		retMsg.Ret = uint32(r2c.RET_ERROR)
		return retMsg
	}

	retMsg.CrossHotRank, err = r.ropCr.GetAllCrossHotRank()
	if err != nil {
		l4g.Errorf("HotRankLoad. get cross hot rank err:%v", err)
		retMsg.Ret = uint32(r2c.RET_ERROR)
		return retMsg
	}

	finish, err := r.ropCr.GetCrossHotRankCollectionLogFinishSid()
	if err != nil {
		l4g.Errorf("HotRankLoad get cross hot rank finish sid failed err:%v", err)
		retMsg.Ret = uint32(r2c.RET_ERROR)
		return retMsg
	}

	retMsg.FinishSid = finish

	retMsg.TalentTreeHot, err = r.ropCr.GetAllTalentTreeHot()
	if err != nil {
		l4g.Errorf("HotRankLoad. get talentTreeHot hot rank err:%v", err)
		retMsg.Ret = uint32(r2c.RET_ERROR)
		return retMsg
	}

	return retMsg
}

func (r *RedisActor) HotRankSave(msg *r2c.C2R_HotRankSave) {
	l4g.Debugf("redisActor.HotRankSave data:%+v", msg)
	if msg.Partition == 0 {
		l4g.Errorf("HotRankSave. param err")
		return
	}

	var calls int
	if msg.FinishSid != nil {
		r.ropCr.SetCrossHotRankCollectionLogFinishSidMCall(msg.FinishSid)
		calls++
	}

	if msg.ClearCollectionLog {
		r.ropCr.DelAllCrossHotRankCollectionLogMCall()
		calls++
	}

	if len(msg.SaveCollectionLog) > 0 {
		r.ropCr.SetSomeCrossHotRankCollectionLogMCall(msg.SaveCollectionLog)
		calls++
	}

	if len(msg.CrossHotRank) > 0 {
		r.ropCr.SetSomeCrossHotRankMCall(msg.CrossHotRank)
		calls++
	}

	if len(msg.TalentTree) > 0 {
		r.ropCr.SetSomeTalentTreeHotMCall(msg.TalentTree)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("HotRankSave error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GSTResetMobRank(msg *r2c.C2R_GSTResetMobRank) {
	l4g.Debugf("redisActor.GSTResetMobRank data:%+v", msg)
	if msg.Partition == 0 || msg.MobRound == 0 {
		l4g.Errorf("GSTResetMobRank. param err")
		return
	}
	key := uint64(msg.MobRound)<<32 | uint64(msg.Partition)
	strKey := strconv.FormatUint(key, 10)
	err := r.ropCr.DelAllGSTMobRankManagerSKs(strKey)
	if err != nil {
		l4g.Errorf("GSTResetMobRank error:%v msg:%v", err, msg)
		ctx.Stop()
	}
}

func (r *RedisActor) SeasonComplianceLoad(msg *r2c.C2R_SeasonComplianceLoad) *r2c.R2C_SeasonComplianceLoad {
	startTime := time.AccurateNow()

	l4g.Debugf("redisActor.SeasonComplianceLoad data:%+v", msg)
	if len(msg.RankIds) == 0 || msg.Partition == 0 {
		l4g.Errorf("SeasonComplianceLoad. param err")
		return nil
	}

	retData := &r2c.R2C_SeasonComplianceLoad{
		Ret:       uint32(ret.RET_OK),
		Partition: msg.Partition,
		RankIds:   msg.RankIds,
	}

	calls := 0

	r.ropCr.GetSeasonComplianceRankResetMCallSK(msg.Partition)
	calls++

	for i := 0; i < calls; i++ {
		resetData, err := r.ropCr.GetSeasonComplianceRankResetByReply(r.ropCr.GetReply())
		if err != nil {
			l4g.Error("season compliance get data error: %v %s", retData, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
			return retData
		}
		retData.RankReset = resetData
	}

	calls = 0
	for _, rankID := range msg.GetRankIds() {
		strKeyName := fmt.Sprintf("%d:%d", msg.Partition, rankID)
		r.ropCr.GetAllSeasonComplianceRankDataMCallSKs(strKeyName)
		calls++
	}

	for i := 0; i < calls; i++ {
		rankData, err := r.ropCr.GetSomeSeasonComplianceRankDataByReply(r.ropCr.GetReply())
		if err != nil {
			l4g.Error("rank get data error: %v %s", retData, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
			return retData
		}
		retData.Ranks = append(retData.Ranks, &r2c.SeasonComplianceTotalRankData{
			RankData: rankData,
		})
	}

	endTime := time.AccurateNow()
	l4g.Infof("redisActor.RankGet msg:%+v, useTime:%v", msg, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) SeasonComplianceSave(msg *r2c.C2R_SeasonComplianceSave) {
	var call int
	l4g.Infof("SeasonComplianceSave msg:%+v", msg)
	if msg.Partition == 0 {
		l4g.Errorf("SeasonComplianceSave. msg partition is zero err")
		return
	}

	if len(msg.ChangeData) > 0 {
		changeDb := make(map[uint32][]*cr.SeasonComplianceRankData)
		for _, data := range msg.ChangeData {
			if data == nil {
				continue
			}
			_, exist := changeDb[data.RankId]
			if !exist {
				changeDb[data.RankId] = make([]*cr.SeasonComplianceRankData, 0)
			}
			changeDb[data.RankId] = append(changeDb[data.RankId], data)
		}

		for rankID, datas := range changeDb {
			strKeyName := fmt.Sprintf("%d:%d", msg.Partition, rankID)
			r.ropCr.SetSomeSeasonComplianceRankDataMCallSKs(strKeyName, datas)
			call++
		}
	}

	if len(msg.DeleteUsers) > 0 {
		for rankID, deleteUsers := range msg.DeleteUsers {
			trKeyName := fmt.Sprintf("%d:%d", msg.Partition, rankID)
			r.ropCr.RemSomeSeasonComplianceRankDataMCallSKs(trKeyName, deleteUsers.Users)
			call++
		}
	}

	if msg.Stage != nil {
		r.ropCr.SetSeasonComplianceRankResetMCallSK(msg.Partition, msg.Stage)
		call++
	}

	if len(msg.ClearRank) > 0 {
		for _, rankID := range msg.ClearRank {
			trKeyName := fmt.Sprintf("%d:%d", msg.Partition, rankID)
			r.ropCr.DelAllSeasonComplianceRankDataMCallSKs(trKeyName)
			call++
		}
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("SeasonComplianceSaveLog error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SeasonMapLoad(msg *r2c.C2R_SeasonMapLoad) *r2c.R2C_SeasonMapLoad {
	if msg.Partition == 0 {
		l4g.Errorf("SeasonMapLoad. msg partition is zero err")
		return nil
	}
	retData := &r2c.R2C_SeasonMapLoad{
		Ret: uint32(ret.RET_OK),
	}
	startTime := time.AccurateNow()

	l4g.Debugf("redisActor.SeasonMapLoad data:%+v", msg)

	calls := 0
	r.ropCr.GetSeasonMapManagerMCallSK(msg.Partition)
	calls++

	for i := 0; i < calls; i++ {
		data, err := r.ropCr.GetSeasonMapManagerByReply(r.ropCr.GetReply())
		if err != nil {
			l4g.Error("SeasonMapLoad get data error: %v %s", retData, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
			return retData
		}
		retData.Mgr = data
	}

	endTime := time.AccurateNow()
	l4g.Debugf("redisActor.SeasonMapLoad msg:%+v, useTime:%v", msg, endTime.Sub(startTime))
	return retData
}

func (r *RedisActor) SeasonMapSave(msg *r2c.C2R_SeasonMapSave) {
	if msg.Partition == 0 {
		l4g.Errorf("SeasonMapSave. msg partition is zero err")
		return
	}
	l4g.Debugf("SeasonMapSave msg:%+v", msg)
	var call int
	if msg.Mgr != nil {
		r.ropCr.SetSeasonMapManagerMCallSK(msg.Partition, msg.Mgr)
		call++
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("SeasonMapSave error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) WorldBossClearUserBack(msg *r2c.C2R_WorldBossClearUserBack) {
	if msg.PartitionId == 0 {
		l4g.Errorf("WorldBossClearUserBack. msg partition is zero err")
		return
	}
	var call int
	call++
	var key uint64
	key = uint64(msg.PartitionId)<<32 | uint64(msg.BossType)
	r.ropCr.DelAllWorldBossUserBackMCallSK(key)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("WorldBossClearUserBack data failed: partitionID: %d bossType:%d err: %s", msg.PartitionId, msg.BossType, reply.Err)
		ctx.Stop()
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("WorldBossClearUserBack error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) WorldBossSaveUserBack(msg *r2c.C2R_WorldBossSaveUserBack) {
	if msg.PartitionId == 0 {
		l4g.Errorf("WorldBossSaveUserBack. msg partition is zero err")
		return
	}
	var call int
	call++
	var key uint64
	key = uint64(msg.PartitionId)<<32 | uint64(msg.BossType)
	r.ropCr.SetSomeWorldBossUserBackMCallSK(key, msg.UserBacks)
	if reply := r.client.GetReply(); reply.Err != nil {
		l4g.Errorf("WorldBossSaveUserBack data failed: partitionID: %d bossType:%d err: %s", msg.PartitionId, msg.BossType, reply.Err)
		ctx.Stop()
	}

	for i := 0; i < call; i++ {
		if reply := r.client.GetReply(); reply.Err != nil {
			l4g.Errorf("WorldBossSaveUserBack error:%v msg:%v", reply.Err, msg)
			ctx.Stop()
		}
	}
}
