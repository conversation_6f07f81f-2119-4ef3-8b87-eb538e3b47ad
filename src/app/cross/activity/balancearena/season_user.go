package balancearena

import (
	"app/protos/in/cr"
	"app/protos/out/cl"
)

type SeasonUser struct {
	m    *Manager
	data *cr.BalanceArenaSeasonUser
}

func NewSeasonUser(data *cr.BalanceArenaSeasonUser, m *Manager) *SeasonUser {
	user := &SeasonUser{
		m:    m,
		data: data,
	}
	return user
}

func (u *SeasonUser) GetUid() uint64 {
	return u.GetData().Uid
}

func (u *SeasonUser) GetData() *cr.BalanceArenaSeasonUser {
	return u.data
}

func (u *SeasonUser) SetChange() {
	u.m.GetUserManager().SetSeasonChange(u)
}

func (u *SeasonUser) GetSnapshot() *cl.UserSnapshot {
	return u.GetData().Snapshot
}

func (u *SeasonUser) GetRobotId() uint32 {
	return u.GetData().RobotId
}

func (u *SeasonUser) AddSeasonScore(score uint32) {
	data := u.GetData()
	data.SeasonScore += score
	u.SetChange()
	u.m.GetRanksManager().UpdateSeasonRank(u.GetUid(), data.SeasonScore, data.Snapshot.Power)
}

func (u *SeasonUser) GetSeasonScore() uint32 {
	return u.GetData().SeasonScore
}

func (u *SeasonUser) UpdateSnapShot(snapShot *cl.UserSnapshot) {
	// 战力不更新(由于参与了排行榜排名,战力只取报名时战力)
	oldPower := u.GetSnapshot().Power
	u.GetData().Snapshot = snapShot.Clone()
	u.GetSnapshot().Power = oldPower
	u.SetChange()
}
