package balancearena

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"math"

	l4g "github.com/ivanabc/log4go"
)

type MatchManager struct {
	mgr *Manager

	matches map[uint32]*cr.BalanceArenaMatch // 所有比赛
	changes map[uint32]*cr.BalanceArenaMatch

	matchIndex uint32 //match的自增id

	elimiMatch *ElimiMatch

	waitSendMatches []*cr.BalanceArenaMatch          // 待处理的比赛
	fightingMatches map[uint32]*cr.BalanceArenaMatch //已经发送出去但是还没有处理完成的比较,给重发用的
	sendMatchNum    int                              // 每次需要处理的比赛数
	startSendTime   int64                            // 开始发送比赛的时间
}

func newMatchManager(manager *Manager) *MatchManager {
	return &MatchManager{
		mgr:             manager,
		matches:         make(map[uint32]*cr.BalanceArenaMatch),
		changes:         make(map[uint32]*cr.BalanceArenaMatch),
		fightingMatches: make(map[uint32]*cr.BalanceArenaMatch),
		elimiMatch:      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(manager),
	}
}

func (mm *MatchManager) Load(matches map[uint32]*cr.BalanceArenaMatch) {
	mm.matches = matches
	for _, match := range matches {
		if !match.Finish {
			mm.waitSendMatches = append(mm.waitSendMatches, match)
		}
		if match.Stage >= uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_1) {
			mm.elimiMatch.LoadMatch(match)
		}
		if match.Id > mm.matchIndex {
			mm.matchIndex = match.Id
		}
	}
}

func (mm *MatchManager) GetMatch(id uint32) *cr.BalanceArenaMatch {
	return mm.matches[id]
}

func (mm *MatchManager) newMatch(leftUser, rightUser *User, state, battleNum, battleGroup, area, grouOrder uint32) *cr.BalanceArenaMatch {
	mm.matchIndex++
	match := &cr.BalanceArenaMatch{
		Id:                  mm.matchIndex,
		Stage:               state,
		BattleNum:           battleNum,
		BattleGroup:         battleGroup,
		BattleArea:          area,
		BattleGroupSubIndex: grouOrder,
	}
	match.LeftUser = &cl.BalanceArenaFighter{
		UserSnapshot: leftUser.GetData().GetSnapshot().Clone(),
		IsRobot:      leftUser.GetRobotId() > 0,
	}
	match.RightUser = &cl.BalanceArenaFighter{
		UserSnapshot: rightUser.GetData().GetSnapshot().Clone(),
		IsRobot:      rightUser.GetRobotId() > 0,
	}

	mm.matches[match.Id] = match

	mm.waitSendMatches = append(mm.waitSendMatches, match)
	mm.Save(match)

	// 添加双方玩家的大组赛id,小组赛id和淘汰赛id
	if state == uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH) {
		leftUser.AddBigGroupMatch(mm.matchIndex)
		leftUser.SetChange()
		rightUser.AddBigGroupMatch(mm.matchIndex)
		rightUser.SetChange()
	} else if state == uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH) {
		leftUser.AddSmallGroupMatch(mm.matchIndex)
		leftUser.SetChange()
		rightUser.AddSmallGroupMatch(mm.matchIndex)
		rightUser.SetChange()
	} else if state == uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_1) ||
		state == uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_2) ||
		state == uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_3) {
		leftUser.AddEliminationMatch(mm.matchIndex)
		leftUser.SetChange()
		rightUser.AddEliminationMatch(mm.matchIndex)
		rightUser.SetChange()
	}

	return match
}

func (mm *MatchManager) Save(match *cr.BalanceArenaMatch) {
	mm.changes[match.Id] = match
}

func (mm *MatchManager) DbSave() []*cr.BalanceArenaMatch {
	results := make([]*cr.BalanceArenaMatch, 0, len(mm.changes))
	for _, result := range mm.changes {
		if result == nil {
			continue
		}
		results = append(results, result.Clone())
	}
	mm.changes = make(map[uint32]*cr.BalanceArenaMatch)
	return results
}

func (mm *MatchManager) RoundClear() {
	mm.matches = make(map[uint32]*cr.BalanceArenaMatch)
	mm.changes = make(map[uint32]*cr.BalanceArenaMatch)
	mm.matchIndex = 0
	mm.elimiMatch = NewElimiMatch(mm.mgr)
	mm.waitSendMatches = make([]*cr.BalanceArenaMatch, 0)
	mm.fightingMatches = make(map[uint32]*cr.BalanceArenaMatch)
	mm.sendMatchNum = 0
	mm.startSendTime = 0
}

func (mm *MatchManager) Fight(now, endTime int64) bool {
	if now < mm.startSendTime {
		l4g.Debugf("%d公平竞技场未到开始战斗时间 %d", mm.GetManager().baseModule.Partition(), mm.startSendTime)
		return false
	}
	waitTotal := len(mm.waitSendMatches)
	fightingTotal := len(mm.fightingMatches)
	matchTotal := waitTotal + fightingTotal
	if matchTotal == 0 {
		l4g.Infof("%d公平竞技场本轮战斗结束", mm.GetManager().baseModule.Partition())
		return true
	}
	if mm.sendMatchNum == 0 {
		mm.InitsendMatchNum(now, endTime)
		l4g.Infof("%d公平竞技场每帧需要处理的战斗 %d now %d endTime %d", mm.GetManager().baseModule.Partition(), mm.sendMatchNum, now, endTime)
	}
	sendMatchNum := mm.sendMatchNum
	if sendMatchNum > len(mm.waitSendMatches) {
		sendMatchNum = len(mm.waitSendMatches)
	}
	sendMatches := mm.waitSendMatches[:sendMatchNum]
	for _, match := range sendMatches {
		if mm.mgr.userM.IsAllRobotMatch(match) {
			mm.UpdateFightResult(match.GetId(), true, "", goxml.GetData().BalanceArenaConfigInfoM.PointWin3, goxml.GetData().BalanceArenaConfigInfoM.PointWin0)
			continue
		} else {
			mm.SendMatchToLogic(match, now)
		}
	}
	mm.waitSendMatches = mm.waitSendMatches[sendMatchNum:]
	l4g.Infof("%d公平竞技场发出的战斗数量 %d", mm.GetManager().baseModule.Partition(), len(sendMatches))
	l4g.Infof("%d公平竞技场剩余的战斗数量 %d", mm.GetManager().baseModule.Partition(), len(mm.waitSendMatches))
	//检测超时的战斗
	for _, match := range mm.fightingMatches {
		if match.SendFightTime == 0 {
			continue
		}
		if now-match.SendFightTime > FightNeedResendTime {
			if match.SendTimes-1 >= FightMaxReSendTimes {
				//异常情况下攻击方赢
				l4g.Errorf("[FATAL] partition %d balance arena fight resend too much %+v", mm.GetManager().baseModule.Partition(), match)
				mm.UpdateFightResult(match.GetId(), true, "", goxml.GetData().BalanceArenaConfigInfoM.PointWin3, goxml.GetData().BalanceArenaConfigInfoM.PointWin0)
			} else {
				mm.waitSendMatches = append(mm.waitSendMatches, match)
				//mm.SendMatchToLogic(match, now)
				l4g.Errorf("[FATAL] partition %d balance arena fight resend %+v", mm.GetManager().baseModule.Partition(), match)
			}
		}
	}
	return false
}

func (mm *MatchManager) GetManager() *Manager {
	return mm.mgr
}

func (mm *MatchManager) InitsendMatchNum(now, endTime int64) {
	fightNum := len(mm.matches)
	l4g.Infof("%d公平竞技场需要处理的战斗总数 %d", mm.GetManager().baseModule.Partition(), fightNum)
	//根据战区,开始时间做偏移
	mm.startSendTime = now + int64(mm.GetManager().baseModule.Partition()%10)*UpdateRate*10
	leftTime := endTime - mm.startSendTime - int64(FightMaxReSendTimes)*FightNeedResendTime
	if leftTime <= 0 {
		mm.sendMatchNum = MaxSendFightNum
		return
	}
	rateTimes := leftTime / UpdateRate
	mm.sendMatchNum = int(math.Ceil(float64(fightNum) / float64(rateTimes)))
	if mm.sendMatchNum < MinSendFightNum {
		l4g.Infof("%d修正公平竞技场每秒需要处理的战斗%d %d", mm.GetManager().baseModule.Partition(), mm.sendMatchNum, MinSendFightNum)
		mm.sendMatchNum = MinSendFightNum
	} else if mm.sendMatchNum > MaxSendFightNum {
		l4g.Infof("%d修正公平竞技场每秒需要处理的战斗%d %d", mm.GetManager().baseModule.Partition(), mm.sendMatchNum, MaxSendFightNum)
		mm.sendMatchNum = MaxSendFightNum
	}
}

func (mm *MatchManager) SendMatchToLogic(match *cr.BalanceArenaMatch, now int64) {
	l4g.Debugf("balanceArena.Fight match:%+v", match)

	msg := &l2c.CS2L_BalanceArenaFight{
		MatchId:      match.GetId(),
		AttackUser:   match.GetLeftUser().GetUserSnapshot(),
		AttackTeams:  mm.GetManager().GetUserManager().GetUser(match.GetLeftUser().GetUserSnapshot().GetId()).GetTeams(),
		DefenseUser:  match.GetRightUser().GetUserSnapshot(),
		DefenseTeams: mm.GetManager().GetUserManager().GetUser(match.GetRightUser().GetUserSnapshot().GetId()).GetTeams(),
	}
	match.SendFightTime = now
	match.SendTimes++
	mm.fightingMatches[match.GetId()] = match
	mm.Save(match)

	// 发给攻击方和防守方中非机器人的那一方
	if !match.GetLeftUser().IsRobot {
		mm.GetManager().baseModule.SendCmdToLogic(msg.AttackUser.Sid, msg.AttackUser.Id, l2c.ID_MSG_CS2L_BalanceArenaFight, msg)
	} else {
		mm.GetManager().baseModule.SendCmdToLogic(msg.DefenseUser.Sid, msg.DefenseUser.Id, l2c.ID_MSG_CS2L_BalanceArenaFight, msg)
	}
}

func (mm *MatchManager) IsFightFinish() bool {
	l4g.Debugf("balanceArena.IsFightFinish unfinished match num:%d", len(mm.waitSendMatches))
	return len(mm.waitSendMatches) == 0
}

func (mm *MatchManager) UpdateFightResult(id uint32, win bool, reportId string, attackScore, defenseScore uint32) {
	delete(mm.fightingMatches, id)
	match := mm.GetMatch(id)
	if match == nil {
		l4g.Errorf("balanceArena.UpdateFightResult, partiton:%d no found id:%d", mm.GetManager().baseModule.Partition(), id)
		return
	}
	state := mm.mgr.GetStateManager().GetSta()
	if match.Stage != state.GetPhase() {
		l4g.Errorf("[FATAL] balanceArena.UpdateFightResult, partiton:%d id:%d state not match", mm.GetManager().baseModule.Partition(), id, match.Stage, state.GetPhase())
		return
	}
	var winUser *User
	var loseUser *User
	if win {
		winUser = mm.mgr.userM.GetUser(match.LeftUser.UserSnapshot.Id)
		loseUser = mm.mgr.userM.GetUser(match.RightUser.UserSnapshot.Id)
	} else {
		winUser = mm.mgr.userM.GetUser(match.RightUser.UserSnapshot.Id)
		loseUser = mm.mgr.userM.GetUser(match.RightUser.UserSnapshot.Id)
	}
	match.Win = win
	match.ReportId = reportId
	match.Finish = true
	match.LeftScore = attackScore
	match.RightScore = defenseScore

	mm.Save(match)
	if match.Stage == uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH) {
		mm.mgr.GetData().GetBigGroupData().UpdateFightResult(match)
	}

	if match.Stage == uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH) {
		mm.mgr.GetData().GetSmallGroupData().UpdateFightResult(match)
	}

	if match.Stage >= uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_1) && match.Stage <= uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_3) {
		mm.elimiMatch.processBattleResult(winUser, loseUser)
	}
}

func (mm *MatchManager) ConvertMatch(match *cr.BalanceArenaMatch) *cl.BalanceArenaMatch {
	res := &cl.BalanceArenaMatch{
		MatchType:    match.GetStage(),
		MatchId:      match.GetId(),
		LeftFighter:  match.GetLeftUser(),
		RightFighter: match.GetRightUser(),
		ReportId:     match.GetReportId(),
	}
	info := goxml.GetData().BalanceArenaPhaseInfoM.GetRecordByPhaseTypeBattleNumPhaseStage(match.GetStage(), match.GetBattleNum(), uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT))
	if info != nil {
		res.StageId = info.Id
	}
	if match.Finish {
		if match.Win {
			res.WinnerUid = match.GetLeftUser().GetUserSnapshot().GetId()
		} else {
			res.WinnerUid = match.GetRightUser().GetUserSnapshot().GetId()
		}
		res.Result = goxml.GetData().BalanceArenaConfigInfoM.CalcFightResult(match.GetLeftScore())
	}
	if match.Stage >= 5 { // 淘汰赛数据
		res.EliminationMatchData = &cl.BalanceArenaEliminationMatch{
			Round:    match.BattleNum,
			Group:    match.BattleGroup,
			Area:     match.BattleArea,
			GroupSub: match.BattleGroupSubIndex,
		}
	}
	return res
}

func (mm *MatchManager) FlushMatchesByIds(matchIds []uint32) []*cl.BalanceArenaMatch {
	matches := make([]*cl.BalanceArenaMatch, 0, len(matchIds))
	for _, matchId := range matchIds {
		match := mm.GetMatch(matchId)
		if match == nil {
			continue
		}
		matches = append(matches, mm.ConvertMatch(match))
	}
	return matches
}

func (mm *MatchManager) FlushAllEliminationMatches(stage uint32) []*cl.BalanceArenaMatch {
	matches := make([]*cl.BalanceArenaMatch, 0)
	battleRounds := mm.elimiMatch.getBattleRoundByStage(stage)
	for _, battleRound := range battleRounds {
		round := int(battleRound)
		for _, v := range mm.elimiMatch.matches[round] {
			//if (v.BattleGroup != 0 && v.BattleGroup == group) || (v.BattleArea != 0 && v.BattleArea == group) {
			matches = append(matches, mm.ConvertMatch(v))
			//}
		}
	}
	return matches
}
