package balancearena

import (
	"app/protos/in/cr"
	l4g "github.com/ivanabc/log4go"
)

type MatchManager struct {
	mgr *Manager

	matches    []*Match // 比赛序号从1开始
	matchIndex uint32   // 比赛序号
	changes    map[uint32]*cr.BalanceArenaMatch

	bigGroupMatchExtra    *BigGroupMatchExtra    // 大组赛额外数据
	smallGroupMatchExtra  *SmallGroupMatchExtra  // 小组赛额外数据
	eliminationMatchExtra *EliminationMatchExtra // 淘汰赛额外数据
}

type BigGroupMatchExtra struct {
	bigGroup [64][]*User // 大组赛分组
}
type SmallGroupMatchExtra struct {
	smallGroup [64][]*User // 小组赛分组
}

type EliminationMatchExtra struct {
	// TODO
}

func newMatch<PERSON>anager(manager *Manager) *MatchManager {
	return &MatchManager{
		mgr:     manager,
		changes: make(map[uint32]*cr.BalanceArenaMatch),
	}
}

func (mm *MatchManager) Load(matches map[uint32]*cr.BalanceArenaMatch) {
	maxMatchIndex := uint32(0)
	for _, data := range matches {
		match := newMatch(data)
		mm.addMatch(match)
		if match.getID() > maxMatchIndex {
			maxMatchIndex = match.getID()
		}
	}
	mm.matchIndex = maxMatchIndex
}

func (mm *MatchManager) GetMatch(id uint32) *Match {
	return mm.matches[id]
}

func (mm *MatchManager) addMatch(match *Match) {
	id := match.getID()
	mm.matches[id] = match
}

func (mm *MatchManager) addNewMatch(match *Match) {
	l4g.Debugf("balanceArena.addNewMatch: data :%+v", match.data)
	mm.addMatch(match)
	mm.SetChange(match)
}

func (mm *MatchManager) SetChange(match *Match) {
	mm.changes[match.getID()] = match.getData()
}

/*
// TODO 问题：玩家大组赛的每场战斗不能匹配之前已经匹配过的玩家，所以要么通过userMatches获取玩家大组赛战斗过的对手, 要么在玩家数据中专门记录一个战斗类型对应的战斗过的对手
// 这个已战斗过的对手的生命周期是整个大组赛，所以不需要放到User上，大组赛结束就可以清掉
// 随机选取两名玩家进行战斗，注意如果战斗进行到一半停服，重启后可以继续进行战斗，这依赖于？实现
// 比赛场次吗？进入战斗开始状态后，可以根据分组数量计算战斗场数 = SL/2
// 记录大组赛的战斗场次，如果<SL/2，则表示有战斗未结束，继续进行战斗
// 但是问题在于，如何重新开始呢？从哪里开始？获取大组赛中未进行战斗的玩家，继续进行战斗 => 需要一个标记.
// 还是说每次从数组中随机两个选手后，就删除这两个元素
// 总结 => 随机两个，用完删除：随机的行为在分组完成后进行一次重新随机rand.Shuffle()，后续只要每次取切片的最后两个即可，没取完说明还要继续战斗
// 报名结束 => 补机器人 => 重新随机 => 每次取最后两个直到取完
// TODO 大组赛每组的前64名选手晋级小组赛: 需要根据积分进行排序，取前64名 => slices.SortFunc()，并取前64个
type BigGroupMatchExtra struct {
	// 大组赛
	bigGroup           [8][]*User          // 大组赛分组（人数不满64或者奇数需要补机器人），切片长度SL 为>=64的偶数
	stageBigGroup      [8][]*User          // 大组赛每个阶段（stage）的匹配情况，每场战斗完成后删除两个元素，阶段重置
	userFightOpponents map[uint64][]uint64 // 玩家已战斗过的敌人，大组赛结束后清空
}
*/
