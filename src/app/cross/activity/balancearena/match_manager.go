package balancearena

import (
	"app/protos/in/cr"
)

type MatchManager struct {
	mgr *Manager

	matches     map[uint32][]*Match // match_type => matches
	userMatches map[uint64][]*Match // uid => matches

	// 是否需要分别抽象出来三种赛制
	bigGroup           [8][]*BigGroupUser // 大组赛分组（人数不满64或者奇数需要补机器人），切片长度SL 为>=64的偶数
	bigGroupMatchIndex uint32             // 大组赛当前比赛场次
	// 随机选取两名玩家进行战斗，注意如果战斗进行到一半停服，重启后可以继续进行战斗，这依赖于？实现
	// 比赛场次吗？进入战斗开始状态后，可以根据分组数量计算战斗场数 = SL/2
	// 记录大组赛的战斗场次，如果<SL/2，则表示有战斗未结束，继续进行战斗
	// 但是问题在于，如何重新开始呢？从哪里开始？获取大组赛中未进行战斗的玩家，继续进行战斗 => 需要一个标记.
	// 还是说每次从数组中随机两个选手后，就删除这两个元素
	// 总结 => 随机两个，用完删除

	changes map[uint32]*cr.BalanceArenaMatch
}

// 这个结构仅用于匹配，不需要使用cr.BalanceArenaUser
type BigGroupUser struct {
	uid      uint64
	sid      uint64
	isRobot  bool // 是否机器人
	finished bool // 战斗结束
}
