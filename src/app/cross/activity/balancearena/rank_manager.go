package balancearena

import (
	"app/goxml"
	"app/logic/srank"
	"app/protos/out/cl"
)

type RankManager struct {
	rank srank.RankList[*cl.RankValue]
}

func NewRankManager() *RankManager {
	return &RankManager{
		rank: srank.NewRankList[*cl.RankValue]([]int{1}, 0),
	}
}

func (m *RankManager) UpdateScore(uid uint64, score uint32, power int64) {
	rankv := m.getRankItem(uid)
	rankv.Value = uint64(score)
	m.GetRank().Update(rankv.User.Id, rankv, uint64(rankv.Value))
}

// 获取展示数据
func (m *RankManager) GetShowValue(manager *Manager, uid uint64, rankType uint32) *cl.RankValue {
	rank, node := m.GetRank().GetRankAndItem(uid)
	return m.getShow(manager, rank, node, rankType)
}

func (m *RankManager) getShow(manager *Manager, rank uint32, node *srank.RankItem[*cl.RankValue], rankType uint32) *cl.RankValue {
	showValue := node.Extra.Clone()
	uid := node.Extra.User.Id
	showValue.Param1 = uint64(rank)
	seasonUser := manager.GetUserManager().GetSeasonUser(uid)
	if seasonUser != nil {
		showValue.User = seasonUser.GetSnapshot().Clone()
	}
	user := manager.GetUserManager().GetUser(uid)
	switch rankType {
	case 1:
		if user != nil {
			showValue.Param2 = uint64(user.GetBigGroupData().WinTimes)
			showValue.Param3 = uint64(user.GetBigGroupData().LoseTimes)
			showValue.Param4 = goxml.GetData().HeroBalanceInfoM.CalcBalancePowerByTeams(user.GetTeams())
		}
	case 2:
		if user != nil {
			showValue.Param2 = uint64(user.GetSmallGroupData().WinTimes)
			showValue.Param3 = uint64(user.GetSmallGroupData().LoseTimes)
			showValue.Param4 = goxml.GetData().HeroBalanceInfoM.CalcBalancePowerByTeams(user.GetTeams())
		}
		/*
			case 4:
				showValue.Param2 = uint64(user.GetEliminationData().WinTimes)
				showValue.Param3 = uint64(user.GetEliminationData().LoseTimes)
		*/
	}

	// 是否机器人
	if seasonUser != nil && seasonUser.GetRobotId() > 0 {
		showValue.Param5 = 1
	}
	return showValue
}

func (m *RankManager) GetShowValueByRange(manager *Manager, start, end, rankType uint32) []*cl.RankValue {
	var showValues []*cl.RankValue
	rankM := m.GetRank()
	rankItems := rankM.GetByRange(start, end)
	for index, rankItem := range rankItems {
		rank := uint32(index) + start
		// 原始数据只有uid 展示数据要额外赋值
		showValue := m.getShow(manager, rank, rankItem, rankType)
		showValues = append(showValues, showValue)
	}
	return showValues
}

func (m *RankManager) getRankItem(uid uint64) *cl.RankValue {
	_, node := m.GetRank().GetRankAndItem(uid)
	if node == nil {
		return &cl.RankValue{
			User: &cl.UserSnapshot{Id: uid},
		}
	}
	return node.Extra
}

func (m *RankManager) GetRank() srank.RankList[*cl.RankValue] {
	return m.rank
}

func (m *RankManager) GetRankUsersByRange(start, end uint32) []uint64 {
	var rankUids []uint64
	rankM := m.GetRank()
	rankItems := rankM.GetByRange(start, end)
	for _, rankItem := range rankItems {
		uid := rankItem.Extra.User.Id
		rankUids = append(rankUids, uid)
	}
	return rankUids
}
