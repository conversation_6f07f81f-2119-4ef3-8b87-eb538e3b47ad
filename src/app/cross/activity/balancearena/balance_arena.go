package balancearena

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
)

type BalanceArena struct {
	mgr    *Manager
	data   *cr.BalanceArena
	change bool

	bigGroupData    *BigGroupData    // 大组赛数据
	smallGroupData  *SmallGroupData  // 小组赛数据
	eliminationData *EliminationData // 淘汰赛数据
	settleTime      int64            // 结算时间
}

func newBalanceArena(manager *Manager) *BalanceArena {
	return &BalanceArena{mgr: manager}
}

func (ba *BalanceArena) load(data *cr.BalanceArena) {
	ba.data = data
	if ba.data == nil {
		ba.data = &cr.BalanceArena{}
	}
	if ba.data.PublicData == nil {
		ba.data.PublicData = &cr.PublicData{
			CardData: make(map[uint32]*cl.BalanceArenaCardData),
		}
	}
	if ba.data.PublicData.CardData == nil {
		ba.data.PublicData.CardData = make(map[uint32]*cl.BalanceArenaCardData)
	}
	if ba.data.PublicData.RewardData == nil {
		ba.data.PublicData.RewardData = &cr.BalanceArenaRewardData{}
	}
	if ba.data.PublicData.RewardData.RewardServices == nil {
		ba.data.PublicData.RewardData.RewardServices = make(map[uint64]*cr.BalanceArenaRewardService)
	}

	bigGroupData := NewBigGroupData(ba.mgr)
	bigGroupData.Load(data)
	ba.bigGroupData = bigGroupData

	smallGroupData := NewSmallGroupData(ba.mgr)
	smallGroupData.Load(data)
	ba.smallGroupData = smallGroupData

	eliminationData := NewEliminationData(ba.mgr)
	eliminationData.Load(data)
	ba.eliminationData = eliminationData
}

func (ba *BalanceArena) GetData() *cr.BalanceArena {
	return ba.data
}

func (ba *BalanceArena) GetPublicData() *cr.PublicData {
	return ba.GetData().PublicData
}

func (ba *BalanceArena) GetBigGroupData() *BigGroupData {
	return ba.bigGroupData
}

func (ba *BalanceArena) GetSmallGroupData() *SmallGroupData {
	return ba.smallGroupData
}

func (ba *BalanceArena) GetEliminationData() *EliminationData {
	return ba.eliminationData
}

func (ba *BalanceArena) GetMatchIncrId() uint32 {
	return ba.GetData().GetPublicData().MatchIncrId
}

func (ba *BalanceArena) AddMatchIncrId() {
	ba.GetData().GetPublicData().MatchIncrId++
	ba.SetChange()
}

func (ba *BalanceArena) GetPublicHeroes() []uint32 {
	return ba.GetPublicData().GetPublicHeroes()
}

func (ba *BalanceArena) SetPublicHeroes(heroes []uint32) {
	ba.GetPublicData().PublicHeroes = heroes
	ba.SetChange()
}

// 赛期结束清除数据
func (ba *BalanceArena) RoundClear() {
	data := &cr.BalanceArena{}
	ba.load(data)
	ba.SetChange()
}

func (ba *BalanceArena) SetChange() {
	ba.change = true
}

func (ba *BalanceArena) SaveBigGroup(data *cr.BalanceArenaGroupManager) {
	ba.GetData().BigGroup = data
	ba.SetChange()
}

func (ba *BalanceArena) SaveSmallGroup(data *cr.BalanceArenaGroupManager) {
	ba.GetData().SmallGroup = data
	ba.SetChange()
}

func (ba *BalanceArena) SaveElimination(data *cr.BalanceArenaEliminationManager) {
	ba.GetData().Elimination = data
	ba.SetChange()
}

func (ba *BalanceArena) isChange() bool {
	return ba.change
}

func (ba *BalanceArena) DbSave() *cr.BalanceArena {
	if !ba.change {
		return nil
	}
	ba.change = false

	return ba.GetData().Clone()
}

func (ba *BalanceArena) Flush() *cr.BalanceArena {
	return ba.GetData()
}

func (ba *BalanceArena) RandomPublicHeroes() {
	// 获取随机的数量
	randomNum := goxml.GetData().BalanceArenaConfigInfoM.PublicInitialRandomHero
	heroes := goxml.GetData().HeroInfoM.RandomHeroesByNum(ba.mgr.rand, randomNum, nil)
	// 记录随机出来的英雄
	ba.SetPublicHeroes(heroes)
}

func (ba *BalanceArena) settleReward() {
	rewardData := &cr.BalanceArenaRewardData{
		RewardServices: make(map[uint64]*cr.BalanceArenaRewardService),
		State:          cr.BalanceArenaRewardE_BAR_Settle,
	}

	rankType := uint32(4)
	maxRank := goxml.GetData().BalanceArenaRankRewardInfoM.GetMaxRankMinRecordByRankType(rankType)
	start, end := uint32(1), maxRank.RankMin
	seasonRankM := ba.mgr.GetRanksManager().GetSeasonRank().GetRank()
	rankItems := seasonRankM.GetByRange(start, end)
	userM := ba.mgr.GetUserManager()
	for index, rankItem := range rankItems {
		rank := uint32(index) + start
		user := userM.GetSeasonUser(rankItem.Extra.User.Id)
		if user.GetRobotId() > 0 {
			// 排除机器人
			continue
		}
		sid := user.GetData().GetSnapshot().Sid
		serviceReward := rewardData.RewardServices[sid]
		if serviceReward == nil {
			serviceReward = &cr.BalanceArenaRewardService{
				Sid: sid,
			}
			rewardData.RewardServices[sid] = serviceReward
		}
		serviceReward.Users = append(serviceReward.Users, &cr.BalanceArenaRewardUser{
			Uid:  user.GetUid(),
			Rank: rank,
		})
	}
	ba.GetPublicData().RewardData = rewardData
	ba.SetChange()
}

func (ba *BalanceArena) getSettleState() cr.BalanceArenaRewardE {
	return ba.GetPublicData().RewardData.State
}

func (ba *BalanceArena) CheckSettleFinish(now int64) bool {
	if ba.getSettleState() == cr.BalanceArenaRewardE_BAR_None {
		ba.settleReward()
	}
	if ba.getSettleState() == cr.BalanceArenaRewardE_BAR_Finish {
		return true
	}
	rewardData := ba.GetPublicData().RewardData
	if now-ba.settleTime < SettleCheckTime {
		return false
	}
	ba.settleTime = now
	finish := true
	state := ba.mgr.GetStateManager().GetSta()
	for sid, rewardService := range rewardData.RewardServices {
		if rewardService.Rewarded {
			continue
		}
		finish = false
		msg := &l2c.CS2L_BalanceArenaRankSettle{
			State:    state.Clone(),
			Reward:   rewardService.Clone(),
			RankType: 4,
		}
		ba.mgr.baseModule.SendCmdToLogic(sid, 0, l2c.ID_MSG_CS2L_BalanceArenaRankSettle, msg)
	}
	if finish {
		rewardData.State = cr.BalanceArenaRewardE_BAR_Finish
		ba.SetChange()
	}

	return finish
}

func (ba *BalanceArena) FinishSettle(sid uint64) {
	if rewardService, ok := ba.GetPublicData().RewardData.RewardServices[sid]; ok {
		rewardService.Rewarded = true
		ba.SetChange()
	}
}

func (ba *BalanceArena) CreateRobotUserId() uint64 {
	newRobotId := ba.GetData().GetPublicData().RobotIncrId + 1
	ba.GetData().GetPublicData().RobotIncrId = newRobotId
	ba.SetChange()
	return newRobotId
}

// 记录抽牌结果
func (ba *BalanceArena) UpdateDrawNum(sysIds []uint32) {
	for _, sysId := range sysIds {
		_, exist := ba.GetData().GetPublicData().CardData[sysId]
		if !exist {
			ba.GetData().GetPublicData().CardData[sysId] = &cl.BalanceArenaCardData{
				CardId:       sysId,
				TotalDrawNum: 1,
			}
		} else {
			ba.GetData().GetPublicData().CardData[sysId].TotalDrawNum++
		}
		ba.SetChange()
	}
}

// 记录选牌结果
func (ba *BalanceArena) UpdateChooseNum(sysIds []uint32) {
	for _, sysId := range sysIds {
		_, exist := ba.GetData().GetPublicData().CardData[sysId]
		if !exist {
			ba.GetData().GetPublicData().CardData[sysId] = &cl.BalanceArenaCardData{
				CardId:         sysId,
				TotalDrawNum:   1,
				TotalChooseNum: 1,
			}
		} else {
			ba.GetData().GetPublicData().CardData[sysId].TotalChooseNum++
		}
		ba.SetChange()
	}
}

func (ba *BalanceArena) GenerateDrawResult(sysIds []uint32) []*cl.BalanceArenaCardData {
	results := make([]*cl.BalanceArenaCardData, 0, len(sysIds))
	for _, sysId := range sysIds {
		result, exist := ba.data.PublicData.CardData[sysId]
		if !exist {
			result = &cl.BalanceArenaCardData{
				CardId: sysId,
			}
		}
		results = append(results, result.Clone())
	}
	return results
}
