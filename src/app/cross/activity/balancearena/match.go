package balancearena

import (
	"app/protos/in/cr"
)

type Match struct {
	data *cr.BalanceArenaMatch
}

func newMatch(match *cr.BalanceArenaMatch) *Match {
	return &Match{
		data: match,
	}
}

func (m *Match) getData() *cr.BalanceArenaMatch {
	return m.data
}

func (m *Match) getID() uint32 {
	return m.data.Id
}

func (m *Match) getWinnerUID() uint64 {
	return m.data.WinnerUid
}

func (m *Match) IsFinished() bool {
	return m.getWinnerUID() > 0
}
