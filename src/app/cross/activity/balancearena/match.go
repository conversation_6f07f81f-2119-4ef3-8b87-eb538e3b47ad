package balancearena

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type Match interface {
	UpdateFightResult(*cr.BalanceArenaMatch)
}

func InitGroupData() *cr.BalanceArenaGroupManager {
	return &cr.BalanceArenaGroupManager{
		RewardData: &cr.BalanceArenaRewardData{
			RewardServices: make(map[uint64]*cr.BalanceArenaRewardService),
		},
		BattleNumGroupFinished: make(map[uint32]bool),
	}
}

type BigGroupData struct {
	mgr        *Manager
	data       *cr.BalanceArenaGroupManager
	settleTime int64 // 结算时间
}

func NewBigGroupData(mgr *Manager) *BigGroupData {
	return &BigGroupData{
		mgr: mgr,
	}
}

func (b *BigGroupData) Load(data *cr.BalanceArena) {
	if data == nil {
		b.data = InitGroupData()
		return
	}
	b.data = data.BigGroup
	if b.data == nil {
		b.data = &cr.BalanceArenaGroupManager{}
	}
	if b.data.RewardData == nil {
		b.data.RewardData = &cr.BalanceArenaRewardData{}
	}
	if b.data.RewardData.RewardServices == nil {
		b.data.RewardData.RewardServices = make(map[uint64]*cr.BalanceArenaRewardService)
	}
	if b.data.BattleNumGroupFinished == nil {
		b.data.BattleNumGroupFinished = make(map[uint32]bool)
	}
}

func (b *BigGroupData) GetData() *cr.BalanceArenaGroupManager {
	return b.data
}

func (b *BigGroupData) SetChange() {
	b.mgr.data.SaveBigGroup(b.GetData())
}

func (b *BigGroupData) stateRunning(now int64, state *cr.BalanceArenaState) {
	battleNum := state.BattleNum // 第几场比赛
	stateM := b.mgr.stateM
	//userM := b.mgr.userM
	matchM := b.mgr.matchM
	if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT_MATCH) { // 匹配阶段
		//是否匹配完成
		if !b.IsBattleNumMatchFinished(battleNum) {
			// 检查分组
			if cRet := b.CheckGroup(); cRet != uint32(ret.RET_OK) {
				l4g.Errorf("balanceArena.StageRunning: CheckGroup err. %d", cRet)
				return
			}

			// 检查匹配
			if mRet := b.DoMatch(state.Phase, battleNum); mRet != uint32(ret.RET_OK) {
				l4g.Errorf("balanceArena.StageRunning: DoMatch err. %d", mRet)
				return
			}
			return
		}
	} else if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT) {
		if !matchM.IsFightFinish() {
			end := stateM.GetStateEnd()
			matchM.Fight(now, end)
			return
		}
	} else if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT_REST) {
		if !b.CheckSettleFinish(now) {
			l4g.Errorf("balanceArena.StageRunning: BigGroup CheckSettleFinish err.")
			return
		}
	} else {
		//休息阶段
		//什么都不做
	}

	if !stateM.CheckStateEnd(now) {
		return
	}
	stateM.SetNextState(state.PhaseConfigId)
}

// 分组
func (b *BigGroupData) GroupAddUser(uid uint64) (uint32, uint32) {
	groupIndex := b.data.GroupIndex
	if groupIndex == 0 {
		groupIndex = goxml.BalanceArenaInitialGroupIndex
	}
	oldNum := b.AddUser(groupIndex, uid)
	b.SetChange()
	b.GroupIndexUpdate()
	return groupIndex, oldNum
}

func (b *BigGroupData) AddUser(groupId uint32, uid uint64) uint32 {
	oldNum := uint32(len(b.mgr.userM.bigGroup[groupId]))
	b.mgr.userM.bigGroup[groupId] = append(b.mgr.userM.bigGroup[groupId], uid)
	return oldNum
}

func (b *BigGroupData) GroupIndexUpdate() {
	// GroupIndex从1-8循环
	b.data.GroupIndex++
	if b.data.GroupIndex > goxml.BalanceArenaBigGroupNum {
		b.data.GroupIndex = goxml.BalanceArenaInitialGroupIndex
	}
	b.SetChange()
}

func (b *BigGroupData) CheckGroup() uint32 {
	for groupId := goxml.BalanceArenaInitialGroupIndex; groupId <= goxml.BalanceArenaBigGroupNum; groupId++ {
		userNum := uint32(len(b.mgr.userM.bigGroup[groupId]))
		if userNum >= goxml.BalanceArenaBigGroupUserMinNum && userNum%2 == 0 {
			continue
		}

		// 添加机器人
		var robotUsers []*cr.BalanceArenaUser
		if userNum < goxml.BalanceArenaBigGroupUserMinNum { // 1.如果少于64人，补充到64人
			needNum := goxml.BalanceArenaBigGroupUserMinNum - userNum
			robotUsers = append(robotUsers, b.createRobotUsersByNum(groupId, needNum)...)
		} else if userNum%2 != 0 { // 2.如果不是偶数，补充为偶数
			robotUsers = append(robotUsers, b.createRobotUsersByNum(groupId, 1)...)
		}

		for _, rUser := range robotUsers {
			// 机器人玩家加入分组
			b.AddUser(groupId, rUser.Uid)
			// 机器人玩家数据
			b.mgr.userM.AddUser(NewUser(rUser, b.mgr))
		}
	}
	b.SetChange()

	if !b.IsBigGroupFinish() {
		return uint32(ret.RET_BALANCE_ARENA_GROUP_NOT_FINISH)
	}
	return uint32(ret.RET_OK)
}

func (b *BigGroupData) IsBigGroupFinish() bool {
	bigGroup := b.mgr.userM.bigGroup
	if uint32(len(bigGroup)) != goxml.BalanceArenaBigGroupNum {
		return false
	}
	// 检查每个大组的人数是否满足要求
	for _, groupUsers := range bigGroup {
		userNum := uint32(len(groupUsers))
		if userNum < goxml.BalanceArenaBigGroupUserMinNum {
			return false
		}
		if userNum%2 != 0 {
			return false
		}
	}
	return true
}

func (b *BigGroupData) createRobotUsersByNum(groupId, num uint32) []*cr.BalanceArenaUser {
	robotUsers := make([]*cr.BalanceArenaUser, 0, num)
	rd := b.mgr.rand
	groupUserNum := b.mgr.userM.bigGroup[groupId]
	for i := uint32(0); i < num; i++ {
		robotUser := &cr.BalanceArenaUser{}
		robotInfo := goxml.GetData().BalanceArenaBotInfoM.RandomRobotId(rd)
		if robotInfo == nil {
			l4g.Errorf("balanceArena.createRobotUsersByNum: get robot info failed.")
			continue
		}

		teams := make([]*cl.BalanceArenaTeam, 0, len(robotInfo.MonsterGroup))
		for _, monsterGroup := range robotInfo.MonsterGroup {
			monsterGroupInfo := goxml.GetData().MonsterGroupInfoM.GetRecordByMonsterGroup(monsterGroup)
			if monsterGroupInfo == nil {
				l4g.Errorf("balanceArena.createRobotUsersByNum: get monster group info failed.")
				continue
			}
			heroSysIds := make([]uint32, 0, len(monsterGroupInfo.Monsters))
			artifactSysIds := make([]uint32, 0, len(monsterGroupInfo.Artifacts))
			for _, monsterId := range monsterGroupInfo.Monsters {
				monsterInfo := goxml.GetData().MonsterInfoM.Index(monsterId)
				if monsterInfo == nil {
					l4g.Errorf("balanceArena.createRobotUsersByNum: get monster info failed.")
					continue
				}
				heroInfo := goxml.GetData().HeroInfoM.Index(monsterInfo.HeroId)
				if heroInfo == nil {
					l4g.Errorf("balanceArena.createRobotUsersByNum: get hero info failed.")
					continue
				}
				heroSysIds = append(heroSysIds, heroInfo.Id)
			}

			for _, artifact := range monsterGroupInfo.Artifacts {
				artifactSysIds = append(artifactSysIds, artifact.SysId)
			}

			teams = append(teams, b.createRobotUserTeam(heroSysIds, artifactSysIds))
		}

		robotUser.Teams = teams
		robotUser.Uid = b.mgr.GetData().CreateRobotUserId()
		robotUser.RobotId = robotInfo.Id
		robotUser.Snapshot = &cl.UserSnapshot{
			Id:     robotUser.Uid,
			Level:  goxml.GetData().BotInfoM.RandomBotLevel(rd, 100),
			Name:   goxml.GetData().BotInfoM.RandomBotName(rd),
			BaseId: goxml.GetData().BotHeadInfoM.GenerateAvatar(rd),
		}
		robotUser.BigGroup = &cr.BalanceArenaGroupData{
			GroupId:  groupId,
			GroupPos: uint32(len(groupUserNum)) + i,
		}
		robotUsers = append(robotUsers, robotUser)
	}
	return robotUsers
}

func (b *BigGroupData) createRobotUserTeam(heroSysIds []uint32, artifactSysIds []uint32) *cl.BalanceArenaTeam {
	team := &cl.BalanceArenaTeam{}
	// 英雄
	heroes := make([]*cl.BalanceArenaHero, 0, len(heroSysIds))
	for _, heroSysId := range heroSysIds {
		heroes = append(heroes, &cl.BalanceArenaHero{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_DRAW),
			SysId:    heroSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.PublicInitialHeroStar,
			Emblems:  goxml.GetData().EmblemInfoM.GetDefaultSimpleEmblems(heroSysId),
		})
	}
	team.Heroes = heroes
	// 神器
	artifacts := make([]*cl.BalanceArenaArtifact, 0, len(artifactSysIds))
	for _, artifactSysId := range artifactSysIds {
		artifacts = append(artifacts, &cl.BalanceArenaArtifact{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_DRAW),
			SysId:    artifactSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.PublicInitialArtifactStar,
		})
	}
	return team
}

// 匹配
func (b *BigGroupData) DoMatch(phase, battleNum uint32) uint32 {
	matchM := b.mgr.GetMatchManager()
	userM := b.mgr.GetUserManager()
	for groupId := goxml.BalanceArenaInitialGroupIndex; groupId <= goxml.BalanceArenaBigGroupNum; groupId++ {
		// 组内的玩家
		userSlice := userM.bigGroup[groupId]
		userNum := len(userSlice)
		if userNum%2 != 0 { // 偶数
			l4g.Errorf("balanceArena.BigGroupData.DoGroup: userSlice must be even. userNum:%v", userNum)
			return uint32(ret.RET_ERROR)
		}
		if uint32(userNum) < goxml.BalanceArenaBigGroupUserMinNum {
			l4g.Errorf("balanceArena.BigGroupData.DoGroup: userSlice too small. userNum:%v", userNum)
			return uint32(ret.RET_ERROR)
		}

		// 生成每个大组第 battleNum 场比赛的匹配结果
		groupMatches := GenerateMatches(matchM, userM, userSlice, phase, goxml.BalanceArenaBigGroupMatchNum, battleNum, groupId)
		// 偶数个玩家生成一半数量的比赛，分组时已经保证玩家数量必须为偶数
		if len(groupMatches) != userNum/2 {
			l4g.Errorf("balanceArena.BigGroupData.DoGroup: groupMatches generate error. resultsNum :%d != userNum/2 %d", len(groupMatches), len(userSlice)/2)
			return uint32(ret.RET_ERROR)
		}
		l4g.Debugf("balanceArena.BigGroupData.DoGroup: groupId %d matchNum %d ", groupId, len(groupMatches))
	}
	b.mgr.GetData().SetChange()

	// 记录已完成匹配状态
	b.mgr.GetData().GetBigGroupData().data.BattleNumGroupFinished[battleNum] = true
	b.mgr.GetData().GetBigGroupData().SetChange()
	return uint32(ret.RET_OK)
}

func (b *BigGroupData) IsBattleNumMatchFinished(battleNum uint32) bool {
	return b.GetData().BattleNumGroupFinished[battleNum]
}

func (b *BigGroupData) UpdateFightResult(result *cr.BalanceArenaMatch) {
	leftUser := b.mgr.GetUserManager().GetUser(result.LeftUser.UserSnapshot.Id)
	if leftUser == nil {
		l4g.Errorf("userM cant find %d. %+v", result.LeftUser.UserSnapshot.Id, result)
		return
	}
	leftUser.AddBigGroupScore(goxml.GetData().BalanceArenaConfigInfoM.GetScoreByWinTimes(result.LeftScore), result.Win)
	b.mgr.GetRanksManager().UpdateBigGroupRank(leftUser.GetBigGroupData().GroupId, result.LeftUser.UserSnapshot.Id, leftUser.GetBigGroupData().Score, leftUser.GetData().Snapshot.Power)

	rightUser := b.mgr.GetUserManager().GetUser(result.RightUser.UserSnapshot.Id)
	if rightUser == nil {
		l4g.Errorf("userM cant find %d. %+v", result.RightUser.UserSnapshot.Id, result)
		return
	}
	rightUser.AddBigGroupScore(goxml.GetData().BalanceArenaConfigInfoM.GetScoreByWinTimes(result.RightScore), result.Win)
	b.mgr.GetRanksManager().UpdateBigGroupRank(rightUser.GetBigGroupData().GroupId, result.RightUser.UserSnapshot.Id, rightUser.GetBigGroupData().Score, rightUser.GetData().Snapshot.Power)
}

func (b *BigGroupData) settleReward() {
	rewardData := &cr.BalanceArenaRewardData{
		RewardServices: make(map[uint64]*cr.BalanceArenaRewardService),
		State:          cr.BalanceArenaRewardE_BAR_Settle,
	}

	rankType := uint32(1)
	maxRank := goxml.GetData().BalanceArenaRankRewardInfoM.GetMaxRankMinRecordByRankType(rankType)
	start, end := uint32(1), maxRank.RankMin
	for _, groupRankM := range b.mgr.GetRanksManager().GetBigGroupRanks() {
		rankM := groupRankM.GetRank()
		rankItems := rankM.GetByRange(start, end)
		for index, rankItem := range rankItems {
			rankUser := b.mgr.GetUserManager().GetUser(rankItem.Extra.User.Id)
			if rankUser == nil {
				l4g.Errorf("userM cant find %d", rankItem.Extra.User.Id)
				continue
			}
			if rankUser.GetRobotId() > 0 {
				continue
			}
			sid := rankUser.GetData().GetSnapshot().Sid
			rank := uint32(index) + start
			rankInfo := goxml.GetData().BalanceArenaRankRewardInfoM.GetRecordByRankTypeRankMinMaxLe(rankType, rank)
			if rankInfo != nil && rankInfo.Score > 0 {
				rankUser.AddTotalScore(rankInfo.Score)
				b.mgr.GetRanksManager().UpdateSeasonRank(rankUser.GetUid(), rankUser.GetData().SeasonScore, rankUser.GetData().Snapshot.Power)
			}
			serviceReward := rewardData.RewardServices[sid]
			if serviceReward == nil {
				serviceReward = &cr.BalanceArenaRewardService{
					Sid: sid,
				}
				rewardData.RewardServices[sid] = serviceReward
			}
			serviceReward.Users = append(serviceReward.Users, &cr.BalanceArenaRewardUser{
				Uid:  rankUser.GetUid(),
				Rank: rank,
			})
		}
	}
	b.GetData().RewardData = rewardData
	b.SetChange()
}

func (b *BigGroupData) getSettleState() cr.BalanceArenaRewardE {
	return b.GetData().RewardData.State
}

func (b *BigGroupData) CheckSettleFinish(now int64) bool {
	if b.getSettleState() == cr.BalanceArenaRewardE_BAR_None {
		b.settleReward()
	}
	if b.getSettleState() == cr.BalanceArenaRewardE_BAR_Finish {
		return true
	}
	rewardData := b.GetData().RewardData
	if now-b.settleTime < SettleCheckTime {
		return false
	}
	b.settleTime = now
	finish := true
	state := b.mgr.GetStateManager().GetSta()
	for sid, rewardService := range rewardData.RewardServices {
		if rewardService.Rewarded {
			continue
		}
		finish = false
		msg := &l2c.CS2L_BalanceArenaRankSettle{
			State:    state.Clone(),
			Reward:   rewardService.Clone(),
			RankType: 1,
		}
		b.mgr.baseModule.SendCmdToLogic(sid, 0, l2c.ID_MSG_CS2L_BalanceArenaRankSettle, msg)
	}
	if finish {
		rewardData.State = cr.BalanceArenaRewardE_BAR_Finish
		b.SetChange()
	}
	return finish
}

func (b *BigGroupData) FinishSettle(sid uint64) {
	if rewardData, ok := b.GetData().RewardData.RewardServices[sid]; ok {
		rewardData.Rewarded = true
		b.SetChange()
	}
}

type SmallGroupData struct {
	mgr        *Manager
	data       *cr.BalanceArenaGroupManager
	settleTime int64 // 结算时间
}

func NewSmallGroupData(mgr *Manager) *SmallGroupData {
	return &SmallGroupData{
		mgr: mgr,
	}
}

func (s *SmallGroupData) Load(data *cr.BalanceArena) {
	if data == nil {
		s.data = InitGroupData()
		return
	}
	s.data = data.SmallGroup
	if s.data == nil {
		s.data = &cr.BalanceArenaGroupManager{}
	}
	if s.data.RewardData == nil {
		s.data.RewardData = &cr.BalanceArenaRewardData{}
	}
	if s.data.RewardData.RewardServices == nil {
		s.data.RewardData.RewardServices = make(map[uint64]*cr.BalanceArenaRewardService)
	}
	if s.data.BattleNumGroupFinished == nil {
		s.data.BattleNumGroupFinished = make(map[uint32]bool)
	}
}

func (s *SmallGroupData) GetData() *cr.BalanceArenaGroupManager {
	return s.data
}

func (s *SmallGroupData) SetChange() {
	s.mgr.GetData().SaveSmallGroup(s.GetData())
}

/*
func (s *SmallGroupData) stateRunning(now int64, state *cr.BalanceArenaState) {
	battleNum := state.BattleNum // 第几场比赛
	stateM := s.mgr.stateM
	matchM := s.mgr.matchM

	if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT_MATCH) { // 匹配阶段
		//是否匹配完成
		if !s.IsBattleNumMatchFinished(battleNum) {
			// 检查分组
			if cRet := s.CheckGroup(); cRet != uint32(ret.RET_OK) {
				l4g.Errorf("balanceArena.StageRunning: CheckGroup err. %d", cRet)
				return
			}

			// 检查匹配
			if mRet := s.DoMatch(state.Phase, battleNum); mRet != uint32(ret.RET_OK) {
				l4g.Errorf("balanceArena.StageRunning: DoMatch err. %d", mRet)
				return
			}
			return
		}
	} else if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT) {
		if !matchM.IsFightFinish() {
			end := stateM.GetStateEnd()
			matchM.Fight(now, end)
			return
		}
	} else {
		//休息阶段
		//什么都不做
	}

	if !stateM.CheckStateEnd(now) {
		return
	}
	stateM.SetNextState(state.PhaseConfigId)
}
*/

func (s *SmallGroupData) IsBattleNumMatchFinished(battleNum uint32) bool {
	return s.GetData().BattleNumGroupFinished[battleNum]
}

func (s *SmallGroupData) GetBigGroupUpgradeUids() []uint64 {
	rankM := s.mgr.ranksM
	totalUids := make([]uint64, 0, goxml.BalanceArenaSmallGroupUserNum*goxml.BalanceArenaSmallGroupNum)
	for _, rank := range rankM.GetBigGroupRanks() {
		groupUids := rank.GetRankUsersByRange(1, goxml.BalanceArenaSmallGroupNum)
		totalUids = append(totalUids, groupUids...)
	}
	return totalUids
}

func (s *SmallGroupData) IsGroupFinish() bool {
	for groupId := goxml.BalanceArenaInitialGroupIndex; groupId <= goxml.BalanceArenaSmallGroupNum; groupId++ {
		userNum := uint32(len(s.mgr.userM.smallGroup[groupId]))
		if userNum < goxml.BalanceArenaSmallGroupUserNum {
			return false
		}
	}
	return true
}

func (s *SmallGroupData) CheckGroup() uint32 {
	if s.IsGroupFinish() {
		return uint32(ret.RET_OK)
	}
	// 大组赛每组前64名 * 8组 = 512名 玩家晋级
	upgradeUids := s.GetBigGroupUpgradeUids()
	if uint32(len(upgradeUids)) != goxml.BalanceArenaSmallGroupNum*goxml.BalanceArenaSmallGroupUserNum {
		l4g.Errorf("balanceArena.SmallGroupData.CheckGroup: upgradeUids num error. num:%d", len(upgradeUids))
		return uint32(ret.RET_ERROR)
	}

	s.group(upgradeUids)
	return uint32(ret.RET_OK)
}

func (s *SmallGroupData) group(uids []uint64) {
	userM := s.mgr.userM
	s.mgr.rand.Shuffle(len(uids), func(i, j int) {
		uids[i], uids[j] = uids[j], uids[i]
	})

	groupUids := splitIntoParts(uids, goxml.BalanceArenaSmallGroupNum, goxml.BalanceArenaSmallGroupUserNum)
	for groupId, ids := range groupUids {
		for pos, id := range ids {
			user := userM.GetUser(id)
			user.GetData().SmallGroup = newGroupData(groupId, uint32(pos), 0, 0)
			userM.SetChange(user)
			userM.smallGroup[groupId] = append(userM.smallGroup[groupId], id)
		}
	}
}

func splitIntoParts(players []uint64, partCount, elementsPerPart uint32) map[uint32][]uint64 {
	result := make(map[uint32][]uint64, partCount)

	for i := uint32(0); i < partCount; i++ {
		start := i * elementsPerPart
		end := start + elementsPerPart
		result[i+1] = players[start:end]
	}

	return result
}

func (s *SmallGroupData) DoMatch(phase, battleNum uint32) uint32 {
	matchM := s.mgr.GetMatchManager()
	userM := s.mgr.GetUserManager()
	for groupId := goxml.BalanceArenaInitialGroupIndex; groupId <= goxml.BalanceArenaSmallGroupNum; groupId++ {
		// 组内的玩家
		userSlice := userM.smallGroup[groupId]
		userNum := len(userSlice)
		if uint32(userNum) != goxml.BalanceArenaSmallGroupUserNum {
			l4g.Errorf("balanceArena.SmallGroupData.DoMatch: userSlice too small. userNum:%v", userNum)
			return uint32(ret.RET_ERROR)
		}

		// 生成每个小组第 battleNum 场比赛的匹配结果
		groupMatches := GenerateMatches(matchM, userM, userSlice, phase, goxml.BalanceArenaSmallGroupMatchNum, battleNum, groupId)
		// 检查比赛数量
		if len(groupMatches) != userNum/2 {
			l4g.Errorf("balanceArena.SmallGroupData.DoMatch: groupMatches generate error. resultsNum :%d != userNum/2 %d", len(groupMatches), len(userSlice)/2)
			return uint32(ret.RET_ERROR)
		}
		l4g.Debugf("balanceArena.SmallGroupData.DoMatch: groupId %d matchNum %d ", groupId, len(groupMatches))
	}
	s.mgr.GetData().SetChange()

	// 记录已完成匹配状态
	s.mgr.GetData().GetSmallGroupData().data.BattleNumGroupFinished[battleNum] = true
	s.mgr.GetData().GetSmallGroupData().SetChange()
	return uint32(ret.RET_OK)
}

func (s *SmallGroupData) stateRunning(now int64, state *cr.BalanceArenaState) {
	battleNum := state.BattleNum // 第几场比赛
	stateM := s.mgr.stateM
	matchM := s.mgr.matchM
	if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT_MATCH) { // 匹配阶段
		//是否匹配完成
		if !s.IsBattleNumMatchFinished(battleNum) {
			// 检查分组
			if cRet := s.CheckGroup(); cRet != uint32(ret.RET_OK) {
				l4g.Errorf("balanceArena.StageRunning: CheckGroup err. %d", cRet)
				return
			}

			// 检查匹配
			if mRet := s.DoMatch(state.Phase, battleNum); mRet != uint32(ret.RET_OK) {
				l4g.Errorf("balanceArena.StageRunning: DoMatch err. %d", mRet)
				return
			}
			return
		}
	} else if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT) {
		if !matchM.IsFightFinish() {
			end := stateM.GetStateEnd()
			matchM.Fight(now, end)
			return
		}
	} else if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT_REST) {
		if !s.CheckSettleFinish(now) {
			l4g.Errorf("balanceArena.StageRunning: SmallGroup CheckSettleFinish err.")
			return
		}
		// 玩家晋级
		s.UserUpgrade()
	}

	if !stateM.CheckStateEnd(now) {
		return
	}
	stateM.SetNextState(state.PhaseConfigId)
}

func (s *SmallGroupData) UpdateFightResult(result *cr.BalanceArenaMatch) {
	leftUser := s.mgr.GetUserManager().GetUser(result.LeftUser.UserSnapshot.Id)
	if leftUser == nil {
		l4g.Errorf("userM cant find %d. %+v", result.LeftUser.UserSnapshot.Id, result)
		return
	}
	leftUser.AddSmallGroupScore(goxml.GetData().BalanceArenaConfigInfoM.GetScoreByWinTimes(result.LeftScore), result.Win)
	s.mgr.GetRanksManager().UpdateSmallGroupRank(leftUser.GetSmallGroupData().GroupId, result.LeftUser.UserSnapshot.Id, leftUser.GetSmallGroupData().Score, leftUser.GetData().Snapshot.Power)
	rightUser := s.mgr.GetUserManager().GetUser(result.RightUser.UserSnapshot.Id)
	if rightUser == nil {
		l4g.Errorf("userM cant find %d. %+v", result.RightUser.UserSnapshot.Id, result)
		return
	}
	rightUser.AddSmallGroupScore(goxml.GetData().BalanceArenaConfigInfoM.GetScoreByWinTimes(result.RightScore), result.Win)
	s.mgr.GetRanksManager().UpdateSmallGroupRank(rightUser.GetSmallGroupData().GroupId, result.RightUser.UserSnapshot.Id, rightUser.GetSmallGroupData().Score, rightUser.GetData().Snapshot.Power)
}

func (s *SmallGroupData) settleReward() {
	rewardData := &cr.BalanceArenaRewardData{
		RewardServices: make(map[uint64]*cr.BalanceArenaRewardService),
		State:          cr.BalanceArenaRewardE_BAR_Settle,
	}

	rankType := uint32(2)
	maxRank := goxml.GetData().BalanceArenaRankRewardInfoM.GetMaxRankMinRecordByRankType(rankType)
	start, end := uint32(1), maxRank.RankMin
	for _, groupRankM := range s.mgr.GetRanksManager().GetSmallGroupRanks() {
		rankM := groupRankM.GetRank()
		rankItems := rankM.GetByRange(start, end)
		for index, rankItem := range rankItems {
			rankUser := s.mgr.GetUserManager().GetUser(rankItem.Extra.User.Id)
			if rankUser == nil {
				l4g.Errorf("userM cant find %d", rankItem.Extra.User.Id)
				continue
			}
			if rankUser.GetRobotId() > 0 {
				continue
			}
			sid := rankUser.GetData().GetSnapshot().Sid
			rank := uint32(index) + start
			rankInfo := goxml.GetData().BalanceArenaRankRewardInfoM.GetRecordByRankTypeRankMinMaxLe(rankType, rank)
			if rankInfo != nil && rankInfo.Score > 0 {
				rankUser.AddTotalScore(rankInfo.Score)
				s.mgr.GetRanksManager().UpdateSeasonRank(rankUser.GetUid(), rankUser.GetData().SeasonScore, rankUser.GetData().Snapshot.Power)
			}
			serviceReward := rewardData.RewardServices[sid]
			if serviceReward == nil {
				serviceReward = &cr.BalanceArenaRewardService{
					Sid:      sid,
					Rewarded: false,
				}
				rewardData.RewardServices[sid] = serviceReward
			}
			serviceReward.Users = append(serviceReward.Users, &cr.BalanceArenaRewardUser{
				Uid:  rankUser.GetUid(),
				Rank: rank,
			})
		}
	}
	s.GetData().RewardData = rewardData
	s.SetChange()
}

func (s *SmallGroupData) getSettleState() cr.BalanceArenaRewardE {
	return s.GetData().RewardData.State
}

func (s *SmallGroupData) CheckSettleFinish(now int64) bool {
	if s.getSettleState() == cr.BalanceArenaRewardE_BAR_None {
		s.settleReward()
	}
	if s.getSettleState() == cr.BalanceArenaRewardE_BAR_Finish {
		return true
	}
	rewardData := s.GetData().RewardData
	if now-s.settleTime < SettleCheckTime {
		return false
	}
	s.settleTime = now
	finish := true
	state := s.mgr.GetStateManager().GetSta()
	for sid, rewardService := range rewardData.RewardServices {
		if rewardService.Rewarded {
			continue
		}
		finish = false
		msg := &l2c.CS2L_BalanceArenaRankSettle{
			State:    state.Clone(),
			Reward:   rewardService.Clone(),
			RankType: 2,
		}
		s.mgr.baseModule.SendCmdToLogic(sid, 0, l2c.ID_MSG_CS2L_BalanceArenaRankSettle, msg)
	}
	if finish {
		rewardData.State = cr.BalanceArenaRewardE_BAR_Finish
		s.SetChange()
	}
	return finish
}

func (s *SmallGroupData) FinishSettle(sid uint64) {
	if rewardData, ok := s.GetData().RewardData.RewardServices[sid]; ok {
		rewardData.Rewarded = true
		s.SetChange()
	}
}

// 获取小组赛淘汰玩家
func (s *SmallGroupData) GetFinishUids() []uint64 {
	rankM := s.mgr.ranksM
	finishUids := make([]uint64, 0, (goxml.BalanceArenaSmallGroupUserNum-goxml.BalanceArenaSmallGroupUpgradeUserNum)*goxml.BalanceArenaSmallGroupNum)
	for _, rank := range rankM.GetSmallGroupRanks() {
		groupUids := rank.GetRankUsersByRange(goxml.BalanceArenaSmallGroupUpgradeUserNum+1, goxml.BalanceArenaSmallGroupUserNum)
		finishUids = append(finishUids, groupUids...)
	}
	return finishUids
}

// 玩家晋级
func (s *SmallGroupData) UserUpgrade() {
	// 记录被淘汰的玩家，其余玩家视为晋级
	finishUids := s.GetFinishUids()
	for _, uid := range finishUids {
		user := s.mgr.GetUserManager().GetUser(uid)
		if user == nil {
			l4g.Errorf("balanceArena.SmallGroupData.UserUpgrade: cant find user %d", uid)
			continue
		}
		user.SetFinish()
	}
}

// 淘汰赛
type EliminationData struct {
	mgr        *Manager
	data       *cr.BalanceArenaEliminationManager
	settleTime int64 // 结算时间
}

func NewEliminationData(mgr *Manager) *EliminationData {
	return &EliminationData{
		mgr: mgr,
	}
}

func (e *EliminationData) Load(data *cr.BalanceArena) {
	if data != nil {
		e.data = data.Elimination
	}
	if e.data == nil {
		e.data = &cr.BalanceArenaEliminationManager{
			RewardData: &cr.BalanceArenaRewardData{
				RewardServices: make(map[uint64]*cr.BalanceArenaRewardService),
			},
			UserIds: make(map[uint64]bool),
		}
	}
	if e.data.UserIds == nil {
		e.data.UserIds = make(map[uint64]bool)
	}
	if e.data.RewardData == nil {
		e.data.RewardData = &cr.BalanceArenaRewardData{
			RewardServices: make(map[uint64]*cr.BalanceArenaRewardService),
		}
	}
}

func (e *EliminationData) GetData() *cr.BalanceArenaEliminationManager {
	return e.data
}

func (e *EliminationData) SetChange() {
	e.mgr.GetData().SaveElimination(e.data)
}

func (e *EliminationData) settleReward() {
	rewardData := &cr.BalanceArenaRewardData{
		RewardServices: make(map[uint64]*cr.BalanceArenaRewardService),
		State:          cr.BalanceArenaRewardE_BAR_Settle,
	}

	for _, rankUser := range e.mgr.GetUserManager().elimiUser.users {
		rank := rankUser.GetEliminationData().Rank
		rankInfo := goxml.GetData().BalanceArenaRankRewardInfoM.GetRecordByRankTypeRankMinMaxLe(3, rank)
		if rankInfo != nil && rankInfo.Score > 0 {
			rankUser.AddTotalScore(rankInfo.Score)
			e.mgr.GetRanksManager().UpdateSeasonRank(rankUser.GetUid(), rankUser.GetData().SeasonScore, rankUser.GetData().Snapshot.Power)
		}
		if rankUser.GetRobotId() > 0 {
			continue
		}
		sid := rankUser.GetData().GetSnapshot().GetSid()
		serviceReward := rewardData.RewardServices[sid]
		if serviceReward == nil {
			serviceReward = &cr.BalanceArenaRewardService{
				Sid:      sid,
				Rewarded: false,
			}
			rewardData.RewardServices[sid] = serviceReward
		}
		serviceReward.Users = append(serviceReward.Users, &cr.BalanceArenaRewardUser{
			Uid:  rankUser.GetUid(),
			Rank: rank,
		})
	}
	e.GetData().RewardData = rewardData
	e.SetChange()
}

func (e *EliminationData) getSettleState() cr.BalanceArenaRewardE {
	return e.GetData().RewardData.State
}

func (e *EliminationData) CheckSettleFinish(now int64) bool {
	if e.getSettleState() == cr.BalanceArenaRewardE_BAR_None {
		e.settleReward()
	}
	if e.getSettleState() == cr.BalanceArenaRewardE_BAR_Finish {
		return true
	}
	rewardData := e.GetData().RewardData
	if now-e.settleTime < SettleCheckTime {
		return false
	}
	e.settleTime = now
	finish := true
	state := e.mgr.GetStateManager().GetSta()
	for sid, rewardService := range rewardData.RewardServices {
		if rewardService.Rewarded {
			continue
		}
		finish = false
		msg := &l2c.CS2L_BalanceArenaRankSettle{
			State:    state.Clone(),
			Reward:   rewardService.Clone(),
			RankType: 3,
		}
		e.mgr.baseModule.SendCmdToLogic(sid, 0, l2c.ID_MSG_CS2L_BalanceArenaRankSettle, msg)
	}
	if finish {
		rewardData.State = cr.BalanceArenaRewardE_BAR_Finish
		e.SetChange()
	}
	return finish
}

func (e *EliminationData) FinishSettle(sid uint64) {
	if rewardData, ok := e.GetData().RewardData.RewardServices[sid]; ok {
		rewardData.Rewarded = true
		e.SetChange()
	}
}

type SimpleMatch struct {
	leftUid  uint64
	RightUid uint64
}

// GenerateMatches 生成X个偶数玩家的Y轮不重复对战
func GenerateMatches(matchM *MatchManager, userM *UserManager, players []uint64, phase, roundNum, battleNum, groupId uint32) []*cr.BalanceArenaMatch {
	userNum := uint32(len(players))
	if userNum%2 != 0 {
		l4g.Errorf("balanceArena.GenerateMatches: userNum must be even. userNum:%d", userNum)
		return nil
	}

	var allMatches [][]*SimpleMatch
	// 固定第一个玩家，旋转其他玩家生成对战
	fixedPlayer := players[0]
	rotatingPlayers := players[1:] // 其余N-1个玩家

	for r := uint32(0); r < roundNum; r++ {
		var roundMatches []*SimpleMatch

		// 固定玩家与旋转列表中间的玩家对战
		mid := len(rotatingPlayers) / 2
		roundMatches = append(roundMatches, &SimpleMatch{
			leftUid:  fixedPlayer,
			RightUid: rotatingPlayers[mid],
		})

		// 其他玩家对称配对（第i个与倒数第i个）
		for i := 0; i < mid; i++ {
			opponentIdx := len(rotatingPlayers) - 1 - i
			roundMatches = append(roundMatches, &SimpleMatch{
				leftUid:  rotatingPlayers[i],
				RightUid: rotatingPlayers[opponentIdx],
			})
		}

		allMatches = append(allMatches, roundMatches)

		// 旋转列表（最后一个元素移到最前面），为下一轮准备
		if len(rotatingPlayers) > 0 {
			last := rotatingPlayers[len(rotatingPlayers)-1]
			rotatingPlayers = append([]uint64{last}, rotatingPlayers[:len(rotatingPlayers)-1]...)
		}
	}

	if uint32(len(allMatches)) != roundNum {
		l4g.Errorf("balanceArena.GenerateMatches: allMatches generate error. resultsNum :%d != roundNum %d", uint32(len(allMatches)), roundNum)
		return nil
	}

	matches := allMatches[battleNum-1]
	generatedMatches := make([]*cr.BalanceArenaMatch, 0, len(matches))
	for _, v := range matches {
		match := matchM.newMatch(userM.GetUser(v.leftUid), userM.GetUser(v.RightUid), phase, battleNum, roundNum, 0, 0)
		generatedMatches = append(generatedMatches, match)
	}

	return generatedMatches
}

func (e *EliminationData) Reward(uid uint64) uint32 {
	rewarded := e.GetData().UserIds[uid]
	if rewarded {
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}
	e.GetData().UserIds[uid] = true
	e.SetChange()
	return uint32(ret.RET_OK)
}

func (e *EliminationData) getBattleRound(phase, battleNum uint32) int {
	return int(battleNum)
}

func (e *EliminationData) stateRunning(now int64, state *cr.BalanceArenaState) {
	battleRound := e.getBattleRound(state.PhaseStage, state.BattleNum)
	stateM := e.mgr.stateM
	userM := e.mgr.userM.elimiUser
	matchM := e.mgr.matchM.elimiMatch
	if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT_MATCH) { // 匹配阶段
		//是否匹配完成
		if !matchM.checkMatchFinish(battleRound) {
			userM.initUser(battleRound) // 第一场比赛时分好组，后续不再改变
			users := userM.getUsersByBattleRound(battleRound)
			matchM.doMatch(state.Phase, battleRound, users)
			return
		}
	} else if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT) {
		//是否战斗完成
		if !matchM.checkFightFinish(battleRound) {
			end := stateM.GetStateEnd()
			e.mgr.matchM.Fight(now, end)
			return
		}
	} else if state.PhaseStage == uint32(common.BALANCE_ARENA_SUB_STAGE_BASS_FIGHT_REST) {
		if !e.CheckSettleFinish(now) {
			l4g.Errorf("balanceArena.StageRunning: Elimination CheckSettleFinish err.")
			return
		}
	}

	if !stateM.CheckStateEnd(now) {
		return
	}
	stateM.SetNextState(state.PhaseConfigId)
}
