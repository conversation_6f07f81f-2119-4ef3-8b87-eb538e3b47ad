package balancearena

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
	"slices"
)

type User struct {
	m    *Manager
	data *cr.BalanceArenaUser
}

func NewUser(data *cr.BalanceArenaUser, m *Manager) *User {
	return &User{
		m:    m,
		data: data,
	}
}

func (u *User) SetChange() {
	u.m.GetUserManager().SetChange(u)
}

func (u *User) GetData() *cr.BalanceArenaUser {
	return u.data
}

func (u *User) GetUid() uint64 {
	if u.data == nil {
		return 0
	}
	return u.data.Uid
}

func (u *User) GetTeams() []*cl.BalanceArenaTeam {
	return u.data.Teams
}

func (u *User) SetTeams(teams []*cl.BalanceArenaTeam) {
	u.data.Teams = teams
}

func (u *User) GetRobotId() uint32 {
	if u.data == nil {
		return 0
	}
	return u.data.RobotId
}

func (u *User) GetBigGroupData() *cr.BalanceArenaGroupData {
	data := u.GetData()
	if data.BigGroup == nil {
		data.BigGroup = &cr.BalanceArenaGroupData{}
	}
	return data.BigGroup
}

func (u *User) SetBigGroupData(data *cr.BalanceArenaGroupData) {
	u.GetData().BigGroup = data
	u.SetChange()
}

func (u *User) GetSmallGroupData() *cr.BalanceArenaGroupData {
	data := u.GetData()
	if data.SmallGroup == nil {
		data.SmallGroup = &cr.BalanceArenaGroupData{}
	}
	return data.SmallGroup
}

func (u *User) SetSmallGroupData(data *cr.BalanceArenaGroupData) {
	u.GetData().SmallGroup = data
	u.SetChange()
}

func (u *User) GetEliminationData() *cr.BalanceArenaGroupData {
	data := u.GetData()
	if data.Elimination == nil {
		data.Elimination = &cr.BalanceArenaGroupData{}
	}
	return data.Elimination
}

func (u *User) SetEliminationData(data *cr.BalanceArenaGroupData) {
	u.GetData().Elimination = data
	u.SetChange()
}

func (u *User) AddBigGroupMatch(matchId uint32) {
	u.data.BigGroup.GroupFights = append(u.data.BigGroup.GroupFights, matchId)
}

func (u *User) AddSmallGroupMatch(matchId uint32) {
	u.data.SmallGroup.GroupFights = append(u.data.SmallGroup.GroupFights, matchId)
}

func (u *User) AddEliminationMatch(matchId uint32) {
	u.data.Elimination.GroupFights = append(u.data.Elimination.GroupFights, matchId)
}

func (u *User) AddBigGroupScore(score uint32, win bool) {
	data := u.GetBigGroupData()
	data.Score += score
	if win {
		data.WinTimes++
	} else {
		data.LoseTimes++
	}
	u.SetChange()
}

func (u *User) AddSmallGroupScore(score uint32, win bool) {
	data := u.GetSmallGroupData()
	data.Score += score
	if win {
		data.WinTimes++
	} else {
		data.LoseTimes++
	}
	u.SetChange()
}

func (u *User) AddTotalScore(score uint32) {
	data := u.GetData()
	data.SeasonScore += score
	u.SetChange()
}

func (u *User) SetFinish() {
	u.GetData().GetSmallGroup().Finish = true
	u.SetChange()
}

func (u *User) FlushUser2Client() *cl.BalanceArenaUser {
	data := u.GetData()
	if data == nil {
		return nil
	}
	clientUser := &cl.BalanceArenaUser{
		Id:                 data.GetUid(),
		Teams:              data.GetTeams(),
		DrawRound:          data.GetDrawRound(),
		AvailableHeroes:    data.GetAvailableHeroes(),
		AvailableArtifacts: data.GetAvailableArtifacts(),
		DrawHeroGroups:     data.GetDrawHeroGroups(),
		DrawArtifactGroups: data.GetDrawArtifactGroups(),
	}
	return clientUser
}

func (u *User) Init() {
	u.GeneratePublicHeroes()
	u.GenerateInitialHeroes()
	u.GenerateInitialArtifacts()
}

// 生成公共英雄
func (u *User) GeneratePublicHeroes() {
	heroes := getGeneratedPublicHeroes(u.m.GetData().GetData())
	u.data.AvailableHeroes = append(u.data.AvailableHeroes, heroes...)
}

// 获取生成的公共英雄
func getGeneratedPublicHeroes(crossData *cr.BalanceArena) []*cl.BalanceArenaHero {
	if crossData == nil || crossData.PublicData == nil {
		l4g.Errorf("balanceArena.GetGeneratedPublicHeroes: crossData is nil. data %+v", crossData)
		return nil
	}
	heroes := make([]*cl.BalanceArenaHero, 0, len(crossData.PublicData.PublicHeroes))
	for _, heroSysId := range crossData.PublicData.PublicHeroes {
		heroes = append(heroes, &cl.BalanceArenaHero{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_PUBLIC),
			SysId:    heroSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.PublicInitialHeroStar,
			Emblems:  goxml.GetData().EmblemInfoM.GetDefaultSimpleEmblems(heroSysId),
		})
	}
	return heroes
}

// 生成初始英雄
func (u *User) GenerateInitialHeroes() {
	num := goxml.GetData().BalanceArenaConfigInfoM.PrivateInitialRandomHero
	heroes := make([]*cl.BalanceArenaHero, 0, num)

	// 从剩余的英雄中随机X个，前提是排除仓库中已有的
	excludeHeroes := make(map[uint32]struct{})
	for _, v := range u.data.AvailableHeroes {
		excludeHeroes[v.SysId] = struct{}{}
	}
	randomHeroes := goxml.GetData().HeroInfoM.RandomHeroesByNum(num, excludeHeroes)
	for _, heroSysId := range randomHeroes {
		heroes = append(heroes, &cl.BalanceArenaHero{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_INITIAL),
			SysId:    heroSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.PublicInitialHeroStar,
			Emblems:  goxml.GetData().EmblemInfoM.GetDefaultSimpleEmblems(heroSysId),
		})
	}
	u.data.AvailableHeroes = append(u.data.AvailableHeroes, heroes...)
}

// 生成初始神器
func (u *User) GenerateInitialArtifacts() {
	num := goxml.GetData().BalanceArenaConfigInfoM.InitialRandomArtifact
	artifacts := make([]*cl.BalanceArenaArtifact, 0, num)

	// 从剩余的神器中随机X个，前提是排除仓库中已有的
	excludeArtifacts := make(map[uint32]struct{})
	for _, v := range u.data.AvailableArtifacts {
		excludeArtifacts[v.SysId] = struct{}{}
	}
	for _, artifactSysId := range goxml.GetData().ArtifactInfoM.RandomArtifactsByNum(num, excludeArtifacts) {
		artifacts = append(artifacts, &cl.BalanceArenaArtifact{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_INITIAL),
			SysId:    artifactSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.PublicInitialArtifactStar,
		})
	}

	u.data.AvailableArtifacts = append(u.data.AvailableArtifacts, artifacts...)
}

// 随机抽选英雄
func (u *User) RandomDrawHeroes() ([]uint32, uint32) {
	if len(u.GetData().DrawHeroGroups) > 0 {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}
	// 在排除英雄仓库中英雄的情况下进行抽卡
	num := goxml.BalanceArenaDrawRoundNum

	// 从剩余的英雄中随机X个，前提是排除仓库中已有的
	excludeHeroes := make(map[uint32]struct{})
	for _, v := range u.data.AvailableHeroes {
		excludeHeroes[v.SysId] = struct{}{}
	}

	heroes := goxml.GetData().HeroInfoM.RandomHeroesByNum(num, excludeHeroes)
	if uint32(len(heroes)) != num {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}

	return heroes, uint32(cret.RET_OK)
}

// 添加抽选英雄到可选池
func (u *User) AddRandomDrawHeroes2Pool(heroes []uint32) {
	u.data.DrawHeroGroups = append(u.data.DrawHeroGroups, heroes...)
}

// 选择抽选英雄
func (u *User) ChooseDrawHeroResult(chooseGroup []uint32) ([]uint32, uint32) {
	drawGroup := u.GetData().DrawHeroGroups
	if uint32(len(drawGroup)) != goxml.BalanceArenaDrawRoundNum {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_ERROR)
	}
	// 12个英雄，分为3组，一组4个 1-[0:4] 2-[4:8] 3-[8:12]
	var heroes []uint32
	drawGroup1 := drawGroup[0:goxml.BalanceArenaDrawGroupNum]
	if slices.Equal(drawGroup1, chooseGroup) {
		heroes = drawGroup1
	}
	drawGroup2 := drawGroup[goxml.BalanceArenaDrawGroupNum : goxml.BalanceArenaDrawGroupNum*2]
	if slices.Equal(drawGroup2, chooseGroup) {
		heroes = drawGroup2
	}
	drawGroup3 := drawGroup[goxml.BalanceArenaDrawGroupNum*2 : goxml.BalanceArenaDrawGroupNum*3]
	if slices.Equal(drawGroup3, chooseGroup) {
		heroes = drawGroup3
	}
	if len(heroes) == 0 {
		return nil, uint32(cret.RET_BALANCE_ARENA_CHOOSE_CARD_ERROR)
	}

	return heroes, uint32(cret.RET_OK)
}

// 将抽选英雄添加到仓库
func (u *User) GenerateDrawHeroes(heroSysIds []uint32) {
	heroes := make([]*cl.BalanceArenaHero, 0, len(heroSysIds))
	for _, heroSysId := range heroSysIds {
		heroes = append(heroes, &cl.BalanceArenaHero{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_DRAW),
			SysId:    heroSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.PublicInitialHeroStar,
			Emblems:  goxml.GetData().EmblemInfoM.GetDefaultSimpleEmblems(heroSysId),
		})
	}
	u.data.AvailableHeroes = append(u.data.AvailableHeroes, heroes...)
	u.data.DrawHeroGroups = make([]uint32, 0)
}

// 随机抽选神器
func (u *User) RandomDrawArtifacts() ([]uint32, uint32) {
	if len(u.GetData().DrawArtifactGroups) > 0 {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}
	// 在排除神器仓库中神器的情况下进行抽卡
	num := goxml.BalanceArenaDrawRoundNum

	// 从剩余的神器中随机X个，前提是排除仓库中已有的
	excludeArtifacts := make(map[uint32]struct{})
	for _, v := range u.data.AvailableArtifacts {
		excludeArtifacts[v.SysId] = struct{}{}
	}

	artifacts := goxml.GetData().ArtifactInfoM.RandomArtifactsByNum(num, excludeArtifacts)
	if uint32(len(artifacts)) != num {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}

	return artifacts, uint32(cret.RET_OK)
}

// 添加抽选神器到可选池
func (u *User) AddRandomDrawArtifacts2Pool(artifacts []uint32) {
	u.data.DrawArtifactGroups = append(u.data.DrawArtifactGroups, artifacts...)
}

// 选择抽选神器
func (u *User) ChooseDrawArtifactResult(chooseGroup []uint32) ([]uint32, uint32) {
	drawGroup := u.GetData().DrawArtifactGroups
	if uint32(len(drawGroup)) != goxml.BalanceArenaDrawRoundNum {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_ERROR)
	}
	// 12个神器，分为3组，一组4个 1-[0:4] 2-[4:8] 3-[8:12]
	var artifacts []uint32
	drawGroup1 := drawGroup[0:goxml.BalanceArenaDrawGroupNum]
	if slices.Equal(drawGroup1, chooseGroup) {
		artifacts = drawGroup1
	}
	drawGroup2 := drawGroup[goxml.BalanceArenaDrawGroupNum : goxml.BalanceArenaDrawGroupNum*2]
	if slices.Equal(drawGroup2, chooseGroup) {
		artifacts = drawGroup2
	}
	drawGroup3 := drawGroup[goxml.BalanceArenaDrawGroupNum*2 : goxml.BalanceArenaDrawGroupNum*3]
	if slices.Equal(drawGroup3, chooseGroup) {
		artifacts = drawGroup3
	}
	if len(artifacts) == 0 {
		return nil, uint32(cret.RET_BALANCE_ARENA_CHOOSE_CARD_ERROR)
	}

	return artifacts, uint32(cret.RET_OK)
}

// 将抽选神器添加到仓库
func (u *User) GenerateDrawArtifacts(artifactSysIds []uint32) {
	artifacts := make([]*cl.BalanceArenaArtifact, 0, len(artifactSysIds))
	for _, artifactSysId := range artifactSysIds {
		artifacts = append(artifacts, &cl.BalanceArenaArtifact{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_DRAW),
			SysId:    artifactSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.PublicInitialArtifactStar,
		})
	}
	u.data.AvailableArtifacts = append(u.data.AvailableArtifacts, artifacts...)
	u.data.DrawArtifactGroups = make([]uint32, 0)
}

// 是否能自选英雄
func (u *User) CanCustomizeHero(heroSysId uint32) uint32 {
	// 自选英雄是否已经在英雄仓库中
	for _, v := range u.GetData().AvailableHeroes {
		if v.SysId == heroSysId {
			return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_ERR)
		}
	}

	// 自选英雄不可在未选择的抽卡列表中
	for _, drawHeroSysId := range u.GetData().DrawHeroGroups {
		if drawHeroSysId == heroSysId {
			return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_ERR)
		}
	}

	// 自选英雄必须是红色
	heroInfo := goxml.GetData().HeroInfoM.Index(heroSysId)
	if heroInfo == nil {
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}
	if heroInfo.Rare != uint32(common.QUALITY_RED) {
		return uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	return uint32(cret.RET_OK)
}

// 生成自选英雄
func (u *User) GenerateCustomizeHeroes(heroSysId, star uint32, emblems []*cl.SimpleEmblemInfo) {
	// 英雄的星级
	if minStar := goxml.GetData().BalanceArenaConfigInfoM.PublicInitialHeroStar; star < minStar {
		star = minStar
	}

	// 符文
	newHero := &cl.BalanceArenaHero{
		CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE),
		SysId:    heroSysId,
		Star:     star,
		Emblems:  emblems,
	}

	for pos, v := range u.data.AvailableHeroes {
		if v.CardType == uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE) {
			u.data.AvailableHeroes[pos] = newHero
			return
		}
	}

	u.data.AvailableHeroes = append(u.data.AvailableHeroes, newHero)
}

// 是否能自选神器
func (u *User) CanCustomizeArtifact(artifactSysId uint32) uint32 {
	// 自选神器是否已经在神器仓库中
	for _, v := range u.data.AvailableArtifacts {
		if v.SysId == artifactSysId {
			return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_NUM_LIMIT)
		}
	}

	// 自选神器不可在未选择的抽卡列表中
	for _, drawArtifactSysId := range u.GetData().DrawArtifactGroups {
		if drawArtifactSysId == artifactSysId {
			return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_ERR)
		}
	}

	// 自选神器必须是红色
	artifactInfo := goxml.GetData().ArtifactInfoM.Index(artifactSysId)
	if artifactInfo == nil {
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}
	if artifactInfo.Rare != uint32(common.QUALITY_RED) {
		return uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	return uint32(cret.RET_OK)
}

// 生成自选神器
func (u *User) GenerateCustomizeArtifacts(artifactSysId, star uint32) {
	// 神器的星级
	if minStar := goxml.GetData().BalanceArenaConfigInfoM.PublicInitialArtifactStar; star < minStar {
		star = minStar
	}

	newArtifact := &cl.BalanceArenaArtifact{
		CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE),
		SysId:    artifactSysId,
		Star:     star,
	}

	for pos, v := range u.data.AvailableArtifacts {
		if v.CardType == uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE) {
			u.data.AvailableArtifacts[pos] = newArtifact
			return
		}
	}

	u.data.AvailableArtifacts = append(u.data.AvailableArtifacts, newArtifact)
}

// 检查组队
func (u *User) CheckTeams(teams []*cl.BalanceArenaTeam) uint32 {
	// 检查队伍数量
	if uint32(len(teams)) != goxml.BalanceArenaTeamNum {
		return uint32(cret.RET_BALANCE_ARENA_TEAM_NUM_ERROR)
	}
	for _, team := range teams {
		// 检查单个队伍
		if ret := u.checkTeam(team); ret != uint32(cret.RET_OK) {
			return ret
		}
	}
	return uint32(cret.RET_OK)
}

func (u *User) checkTeam(team *cl.BalanceArenaTeam) uint32 {
	// 1.每个队伍的英雄必须>1
	if len(team.Heroes) == 0 {
		return uint32(cret.RET_BALANCE_ARENA_TEAM_HERO_NUM_ERROR)
	}

	// 2.所选的英雄和神器必须在仓库中
	for _, hero := range team.Heroes {
		if !u.isHeroAvailable(hero.SysId) {
			return uint32(cret.RET_BALANCE_ARENA_TEAM_NOT_AVAILABLE)
		}
	}

	for _, artifact := range team.Artifacts {
		if !u.isArtifactAvailable(artifact.SysId) {
			return uint32(cret.RET_BALANCE_ARENA_TEAM_NOT_AVAILABLE)
		}
	}

	return uint32(cret.RET_OK)
}

func (u *User) isHeroAvailable(sysId uint32) bool {
	for _, v := range u.data.AvailableHeroes {
		if v.SysId == sysId {
			return true
		}
	}
	return false
}

func (u *User) isArtifactAvailable(sysId uint32) bool {
	for _, v := range u.data.AvailableArtifacts {
		if v.SysId == sysId {
			return true
		}
	}
	return false
}

func (u *User) SetDrawRound(drawRound uint32) {
	u.data.DrawRound = drawRound
}
