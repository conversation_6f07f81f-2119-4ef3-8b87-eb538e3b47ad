package balancearena

import (
	"app/cross/activity"
	"app/goxml"
	"app/logic/srank"
	"app/protos/in/cr"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/skiplist"
)

type User struct {
	userManager *UserManager
	data        *cr.BalanceArenaUser

	user                 []uint64 //上一轮匹配的真人对手和本轮待生成的对手
	bot                  []uint64 //上一轮匹配到的机器人和本轮待生成的机器人
	findUserPowerNoMatch map[*srank.RankItem[*RankItem]]struct{}
}
