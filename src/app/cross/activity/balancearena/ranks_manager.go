package balancearena

type RanksManager struct {
	m              *Manager
	seasonRank     *RankManager
	bigGroupRank   map[uint32]*RankManager
	smallGroupRank map[uint32]*RankManager
}

func NewRanksManager(m *Manager) *RanksManager {
	return &RanksManager{
		m:              m,
		seasonRank:     NewRankManager(),
		bigGroupRank:   make(map[uint32]*RankManager),
		smallGroupRank: make(map[uint32]*RankManager),
	}
}

func (ranksM *RanksManager) GetManager() *Manager {
	return ranksM.m
}

func (ranksM *RanksManager) loadUser(user *User) {
	power := user.GetSeasonUser().GetSnapshot().GetPower()
	if user.GetBigGroupData().WinTimes > 0 || user.GetBigGroupData().LoseTimes > 0 {
		ranksM.UpdateBigGroupRank(user.GetBigGroupData().GroupId, user.GetUid(), user.GetBigGroupData().Score, power)
	}
	if user.GetSmallGroupData().WinTimes > 0 || user.GetSmallGroupData().LoseTimes > 0 {
		ranksM.UpdateSmallGroupRank(user.GetSmallGroupData().GroupId, user.GetUid(), user.GetSmallGroupData().Score, power)
	}
}

func (ranksM *RanksManager) loadSeasonUser(seasonUser *SeasonUser) {
	if seasonUser.GetSeasonScore() > 0 {
		ranksM.UpdateSeasonRank(seasonUser.GetUid(), seasonUser.GetSeasonScore(), seasonUser.GetSnapshot().GetPower())
	}
}

func (ranksM *RanksManager) RoundClear() {
	ranksM.bigGroupRank = make(map[uint32]*RankManager)
	ranksM.smallGroupRank = make(map[uint32]*RankManager)
}

func (ranksM *RanksManager) SeasonClear() {
	ranksM.seasonRank = NewRankManager()
}

func (ranksM *RanksManager) UpdateBigGroupRank(group uint32, uid uint64, score uint32, power int64) {
	rankM := ranksM.GetBigGroupRankM(group)
	rankM.UpdateScore(uid, score, power)
}

func (ranksM *RanksManager) GetBigGroupRanks() map[uint32]*RankManager {
	return ranksM.bigGroupRank
}

func (ranksM *RanksManager) GetSmallGroupRanks() map[uint32]*RankManager {
	return ranksM.smallGroupRank
}

func (ranksM *RanksManager) GetBigGroupRankM(group uint32) *RankManager {
	rankM := ranksM.GetBigGroupRanks()[group]
	if rankM == nil {
		rankM = NewRankManager()
		ranksM.GetBigGroupRanks()[group] = rankM
	}
	return rankM
}

func (ranksM *RanksManager) UpdateSmallGroupRank(group uint32, uid uint64, score uint32, power int64) {
	rankM := ranksM.GetSmallGroupRankM(group)
	rankM.UpdateScore(uid, score, power)
}

func (ranksM *RanksManager) GetSmallGroupRankM(group uint32) *RankManager {
	rankM := ranksM.GetSmallGroupRanks()[group]
	if rankM == nil {
		rankM = NewRankManager()
		ranksM.GetSmallGroupRanks()[group] = rankM
	}
	return rankM
}

func (ranksM *RanksManager) UpdateSeasonRank(uid uint64, score uint32, power int64) {
	ranksM.GetSeasonRank().UpdateScore(uid, score, power)
}

func (ranksM *RanksManager) GetSeasonRank() *RankManager {
	return ranksM.seasonRank
}
