package balancearena

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type StateManager struct {
	mgr *Manager

	sta    *cr.BalanceArenaState // 公平竞技场状态
	change bool
}

func newStateManager(manager *Manager) *StateManager {
	return &StateManager{
		mgr: manager,
		sta: &cr.BalanceArenaState{},
	}
}

func (m *StateManager) Load(data *cr.BalanceArenaState) {
	if data == nil {
		return
	}
	m.sta = data
}

func (m *StateManager) SetChange(change bool) {
	m.change = change
}

func (m *StateManager) IsChange() bool {
	return m.change
}

func (m *StateManager) Flush() *cr.BalanceArenaState {
	return m.sta.Clone()
}

func (m *StateManager) GetState() *cr.BalanceArenaState {
	return m.sta
}

func (m *StateManager) GetSta2Client() *cl.BalanceArenaState {
	state := m.sta.Clone()
	return &cl.BalanceArenaState{
		SeasonId: state.SeasonId,
		Round:    state.Round,
		Stage:    state.PhaseConfigId,
	}
}

func (m *StateManager) SetState(sta *cr.BalanceArenaState) {
	m.sta = sta
	m.SetChange(true)
	m.PushState()
}

func (m *StateManager) SetNextState(id uint32) bool {
	id++
	info := goxml.GetData().BalanceArenaPhaseInfoM.GetRecordById(id)
	if info != nil {
		sta := &cr.BalanceArenaState{
			SeasonId:      m.sta.SeasonId,
			Round:         m.sta.Round,
			Phase:         info.PhaseType,
			PhaseStage:    info.PhaseStage,
			BattleNum:     info.BattleNum,
			PhaseConfigId: info.Id,
		}
		m.SetState(sta)
		return true
	}
	return false
}

func (m *StateManager) GetSta() *cr.BalanceArenaState {
	return m.sta
}

func (m *StateManager) SetSeasonID(seasonID uint32) {
	m.sta.SeasonId = seasonID
	m.SetChange(true)
}

func (m *StateManager) SetRound(round uint32) {
	m.sta.Round = round
	m.SetChange(true)
}

func (m *StateManager) SetPhase(phase uint32) {
	m.sta.Phase = phase
	m.SetChange(true)
}

func (m *StateManager) SetPhaseStage(stage uint32) {
	m.sta.PhaseStage = stage
	m.SetChange(true)
}

func (m *StateManager) SetBattleNum(battleNum uint32) {
	m.sta.BattleNum = battleNum
	m.SetChange(true)
}

// 向logic推送状态
func (m *StateManager) PushState() {
	msg := &l2c.CS2L_BalanceArenaGetSta{
		Ret: uint32(ret.RET_OK),
		Sta: m.GetSta2Client(),
	}
	for _, sid := range m.mgr.baseModule.GetServerIDList() {
		m.mgr.baseModule.SendCmdToLogic(sid, 0, l2c.ID_MSG_CS2L_BalanceArenaGetSta, msg)
	}
}

func (m *StateManager) DbSave() *cr.BalanceArenaState {
	if !m.change {
		return nil
	}
	defer m.SetChange(false)

	return m.sta.Clone()
}

func (m *StateManager) ConvertStage2StageConfigId(state *cr.BalanceArenaState) uint32 {
	if state == nil {
		return 0
	}
	info := goxml.GetData().BalanceArenaPhaseInfoM.GetRecordByPhaseTypeBattleNumPhaseStage(state.Phase, state.BattleNum, state.PhaseStage)
	if info == nil {
		return 0
	}
	return info.Id
}

func (m *StateManager) SaveState(state *cr.BalanceArenaState) {
	state.PhaseConfigId = m.ConvertStage2StageConfigId(state)
	m.SetState(state)
	m.PushState()
}

func (m *StateManager) GetTimeConfigByState() (int64, int64) {
	state := m.sta
	roundInfo := goxml.GetData().BalanceArenaInfoM.GetRecordById(state.Round)
	if roundInfo == nil {
		l4g.Errorf("balanceArena.GetTimeConfigByState: get roundInfo failed. round:%d", state.Round)
		return 0, 0
	}
	phaseInfo := goxml.GetData().BalanceArenaPhaseInfoM.GetRecordByPhaseTypeBattleNumPhaseStage(state.Phase, state.BattleNum, state.PhaseStage)
	if phaseInfo == nil {
		l4g.Errorf("balanceArena.GetTimeConfigByState: get phaseInfo failed. phase:%d battleNum:%d phaseStage:%d", state.Phase, state.BattleNum, state.PhaseStage)
		return 0, 0
	}
	l4g.Debugf("balanceArena.GetTimeConfigByState: state %+v, start %d, end %d", state, roundInfo.OpenTime+int64(phaseInfo.PhaseOpen), roundInfo.OpenTime+int64(phaseInfo.PhaseClose))
	return roundInfo.OpenTime + int64(phaseInfo.PhaseOpen), roundInfo.OpenTime + int64(phaseInfo.PhaseClose)
}

func (m *StateManager) GetStateEnd() int64 {
	_, end := m.GetTimeConfigByState()
	return end
}

func (m *StateManager) CheckStateEnd(now int64) bool {
	end := m.GetStateEnd()
	if now > end {
		return true
	}
	return false
}
