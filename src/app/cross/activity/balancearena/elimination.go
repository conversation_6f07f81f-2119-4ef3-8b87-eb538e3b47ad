package balancearena

import (
	"app/goxml"
	"app/protos/in/cr"
	"slices"

	l4g "github.com/ivanabc/log4go"
)

// 决赛

//第一阶段128
//第几轮 1-3
//分组 a-p 16个
//组内排序 1-8
//是否淘汰

//第二阶段 16
//第几轮 4-6
//分组2个
//组内排序1-8

//第三阶段 4
//冠军赛
//季军赛

var battleRound2Rank = map[int]uint32{
	1: 128,
	2: 64,
	3: 32,
	4: 16,
	5: 8,
	6: 4,
	7: 2,
	8: 1,
}

var stage2BattleRound = map[uint32][]uint32{
	5: {0, 1, 2},
	6: {3, 4, 5},
	7: {6},
}

type ElimiUser struct {
	mgr   *Manager
	users []*User
}

func NewElimiUser(mgr *Manager) *ElimiUser {
	return &ElimiUser{
		mgr: mgr,
	}
}

func (e *ElimiUser) LoadUser(user *User) {
	e.users = append(e.users, user)
}

func (e *ElimiUser) SortUsers() {
	if len(e.users) == 0 {
		return
	}
	slices.SortFunc(e.users, func(a, b *User) int {
		au := a.GetData().Elimination
		bu := b.GetData().Elimination
		if au.GroupId < bu.GroupId {
			return -1
		} else if au.GroupId > bu.GroupId {
			return 1
		} else {
			if au.GroupPos < bu.GroupPos {
				return -1
			} else if au.GroupPos > bu.GroupPos {
				return 1
			} else {
				return 0
			}
		}
	})
}

func (e *ElimiUser) initUser(battleRound int) {
	// 第一场战斗才需要初始化
	if battleRound != 1 {
		return
	}

	// 人数满足要求，无须初始化
	if len(e.users) == EliminationUsersCount {
		return
	}
	//重新初始化
	e.users = make([]*User, 0, EliminationUsersCount)
	userM := e.mgr.userM
	users := userM.getEliminationUsers()
	if len(users) != EliminationUsersCount {
		l4g.Errorf("[FATAL] elimiUser init user count is error:%d", len(users))
		return
	}
	e.users = append(e.users, users...)
	//分组
	e.group()
	//排序
	e.SortUsers()
}

func (e *ElimiUser) group() {
	userM := e.mgr.userM
	e.mgr.rand.Shuffle(len(e.users), func(i, j int) {
		e.users[i], e.users[j] = e.users[j], e.users[i]
	})

	groupId := goxml.BalanceArenaInitialGroupIndex
	groupPos := goxml.BalanceArenaInitialGroupPos
	for _, v := range e.users {
		v.GetData().Elimination = newGroupData(groupId, groupPos, 1, uint32(EliminationUsersCount))
		userM.SetChange(v)
		groupPos++
		if groupPos >= uint32(EliminationPerGroupNum) {
			groupId++
			groupPos = 0
		}
	}
}

func newGroupData(groupId, groupPos, battleRound, rank uint32) *cr.BalanceArenaGroupData {
	return &cr.BalanceArenaGroupData{
		GroupId:     groupId,
		GroupPos:    groupPos,
		BattleRound: battleRound,
		Rank:        rank,
	}
}

func (e *ElimiUser) getUsersByBattleRound(battleRound int) []*User {
	users := make([]*User, 0, len(e.users))
	if battleRound == EliminationMaxBattleRound { // 特殊处理：第7轮比配时，上一轮输的也要打
		for _, v := range e.users {
			groupData := v.data.Elimination
			if groupData.BattleRound == uint32(battleRound) && groupData.Finish {
				users = append(users, v)
			}
		}
	}
	for _, v := range e.users {
		groupData := v.data.Elimination
		if groupData.BattleRound == uint32(battleRound) && !groupData.Finish {
			users = append(users, v)
		}
	}
	return users
}

type ElimiMatch struct {
	mgr     *Manager
	matches [EliminationMaxBattleRound][]*cr.BalanceArenaMatch //所有轮次的比赛
}

func NewElimiMatch(mgr *Manager) *ElimiMatch {
	return &ElimiMatch{
		mgr: mgr,
	}
}

func (e *ElimiMatch) LoadMatch(match *cr.BalanceArenaMatch) {
	if match.BattleNum < 1 || match.BattleNum > uint32(EliminationMaxBattleRound) {
		l4g.Errorf("[FATAL] ElimiMatch load match error:%+v", match)
		return
	}
	battleRound := int(match.BattleNum)
	e.matches[battleRound-1] = append(e.matches[battleRound-1], match)
}

// 检查匹配是否完成
func (e *ElimiMatch) checkMatchFinish(battleRound int) bool {
	if len(e.matches) < battleRound || len(e.matches[battleRound-1]) == 0 {
		return false
	}
	return true
}

// 检查战斗是否完成
func (e *ElimiMatch) checkFightFinish(battleRound int) bool {
	for _, v := range e.matches[battleRound-1] {
		if !v.Finish {
			return false
		}
	}
	return true
}

func (e *ElimiMatch) doMatch(phase uint32, battleRound int, users []*User) {
	group := uint32(0)
	area := uint32(0)
	roundGroupOrder := uint32(0)
	for i := 0; i < len(users); i++ {
		j := i + 1
		if battleRound >= 1 && battleRound <= 3 {
			//前3轮分组等于玩家的小组
			userGroup := users[i].GetData().Elimination.GroupId
			if userGroup != group {
				roundGroupOrder = 0 //重新开始
			}
			group = userGroup
			roundGroupOrder++
		} else if battleRound >= 4 && battleRound <= 6 {
			userArea := uint32(0)
			userGroup := users[i].GetData().Elimination.GroupId
			if userGroup <= 8 {
				//上半场
				userArea = 1
			} else {
				userArea = 2
			}
			if userArea != area {
				roundGroupOrder = 0 //重新开始
			}
			area = userArea
			roundGroupOrder++
		} else {
			area++              //1和2
			roundGroupOrder = 1 //都为1
		}
		match := e.mgr.matchM.newMatch(users[i], users[j], phase, uint32(battleRound), group, area, roundGroupOrder)
		e.matches[battleRound-1] = append(e.matches[battleRound-1], match)
		i++
	}
}

func (e *ElimiMatch) processBattleResult(winUser, loseUser *User) {
	e.win(winUser)
	e.lose(loseUser)
}

func (e *ElimiMatch) win(user *User) {
	if user == nil {
		l4g.Errorf("[FATAL] elimiMatch win user is nil")
		return
	}
	if user.GetData().Elimination.BattleRound == uint32(EliminationMaxBattleRound) {
		if user.GetData().Elimination.Finish {
			user.GetData().Elimination.Rank = 3 //已经输了的赢了是第三名
		} else {
			user.GetData().Elimination.Rank = 1
		}
	} else {
		user.GetData().Elimination.BattleRound++
		newBattleRound := int(user.GetData().Elimination.BattleRound)
		user.GetData().Elimination.Rank = e.getRankByBattleRound(newBattleRound)
	}
	user.GetData().Elimination.WinTimes++
	e.mgr.userM.SetChange(user)
}

func (e *ElimiMatch) lose(user *User) {
	if user == nil {
		l4g.Errorf("[FATAL] elimiMatch lose user is nil")
		return
	}
	if user.GetData().Elimination.BattleRound == uint32(EliminationMaxBattleRound-1) {
		//第6轮的输了也会进入到第7轮
		user.GetData().Elimination.BattleRound++ //第七轮特殊处理
	}
	user.GetData().Elimination.Finish = true
	user.GetData().Elimination.LoseTimes++
	e.mgr.userM.SetChange(user)
}

func (e *ElimiMatch) getRankByBattleRound(battleRound int) uint32 {
	return battleRound2Rank[battleRound]
}

func (e *ElimiMatch) getBattleRoundByStage(stage uint32) []uint32 {
	return stage2BattleRound[stage]
}
