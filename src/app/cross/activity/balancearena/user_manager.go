package balancearena

import "app/protos/in/cr"

type UserManager struct {
	mgr     *Manager
	users   map[uint64]*User // 报名参加玩家
	changes map[uint64]*cr.BalanceArenaUser
}

func newUserManager(manager *Manager) *UserManager {
	return &UserManager{
		mgr:     manager,
		users:   make(map[uint64]*User),
		changes: make(map[uint64]*cr.BalanceArenaUser),
	}
}

func (u *UserManager) Load(users map[uint64]*cr.BalanceArenaUser) {
	for _, data := range users {
		user := newUser(data)
		u.addUser(user)
	}
}

func (u *UserManager) GetUser(uid uint64) *User {
	return u.users[uid]
}

func (u *UserManager) addUser(user *User) {
	u.users[user.GetUid()] = user
}

func (u *UserManager) SetChange(user *User) {
	u.changes[user.GetUid()] = user.GetData()
}
