package balancearena

import (
	"app/protos/in/cr"
	"cmp"
	"slices"

	l4g "github.com/ivanabc/log4go"
)

type UserManager struct {
	mgr        *Manager
	users      map[uint64]*User // 报名参加玩家
	changes    map[uint64]*cr.BalanceArenaUser
	bigGroup   map[uint32][]uint64 // 大组赛分组 bigGroupId => users
	smallGroup map[uint32][]uint64 // 小组赛分组 smallGroupId => users

	seasonUsers   map[uint64]*SeasonUser
	seasonChanges map[uint64]*cr.BalanceArenaSeasonUser

	elimiUser *ElimiUser
}

func newUserManager(manager *Manager) *UserManager {
	return &UserManager{
		mgr:           manager,
		users:         make(map[uint64]*User),
		changes:       make(map[uint64]*cr.BalanceArenaUser),
		bigGroup:      make(map[uint32][]uint64),
		smallGroup:    make(map[uint32][]uint64),
		elimiUser:     NewElimi<PERSON><PERSON>(manager),
		seasonUsers:   make(map[uint64]*SeasonUser),
		seasonChanges: make(map[uint64]*cr.BalanceArenaSeasonUser),
	}
}

func (u *UserManager) LoadUsers(users map[uint64]*cr.BalanceArenaUser) {
	bigGroupUsers := make(map[uint32][]*User)
	smallGroupUsers := make(map[uint32][]*User)
	for _, data := range users {
		if data == nil {
			continue
		}
		seasonUser := u.GetSeasonUser(data.GetUid())
		if seasonUser == nil {
			l4g.Errorf("[FATAL] LoadUsers cant find seasonUser %d", data.GetUid())
			continue
		}
		user := NewUser(data, seasonUser, u.mgr)
		u.LoadUser(user)
		if data.BigGroup != nil && data.BigGroup.GroupId != 0 {
			bigGroupUsers[data.BigGroup.GroupId] = append(bigGroupUsers[data.BigGroup.GroupId], user)
		}
		if data.SmallGroup != nil && data.SmallGroup.GroupId != 0 {
			smallGroupUsers[data.SmallGroup.GroupId] = append(smallGroupUsers[data.SmallGroup.GroupId], user)
		}
		if data.Elimination != nil && data.Elimination.GroupId != 0 {
			u.elimiUser.LoadUser(user)
		}
		u.mgr.GetRanksManager().loadUser(user)
	}
	// 每个Group根据User的GroupPos进行排序
	for groupId, groupUsers := range bigGroupUsers {
		slices.SortFunc(groupUsers, func(a, b *User) int {
			return cmp.Compare(a.GetData().GetBigGroup().GroupPos, b.GetData().GetBigGroup().GroupPos)
		})
		uids := make([]uint64, 0, len(groupUsers))
		for _, v := range groupUsers {
			uids = append(uids, v.GetUid())
		}
		u.bigGroup[groupId] = uids
	}
	for groupId, groupUsers := range smallGroupUsers {
		slices.SortFunc(groupUsers, func(a, b *User) int {
			return cmp.Compare(a.GetData().GetSmallGroup().GroupPos, b.GetData().GetSmallGroup().GroupPos)
		})
		uids := make([]uint64, 0, len(groupUsers))
		for _, v := range groupUsers {
			uids = append(uids, v.GetUid())
		}
		u.smallGroup[groupId] = uids
	}

	if u.elimiUser == nil {
		u.elimiUser = NewElimiUser(u.mgr)
	}
	u.elimiUser.SortUsers()
}

func (u *UserManager) LoadSeasonUsers(users map[uint64]*cr.BalanceArenaSeasonUser) {
	for _, data := range users {
		user := NewSeasonUser(data, u.mgr)
		u.LoadSeasonUser(user)
		u.mgr.GetRanksManager().loadSeasonUser(user)
	}
}

func (u *UserManager) GetUser(uid uint64) *User {
	return u.users[uid]
}

func (u *UserManager) AddUser(user *User) {
	u.users[user.GetUid()] = user
	u.SetChange(user)
}

func (u *UserManager) LoadUser(user *User) {
	u.users[user.GetUid()] = user
}

func (u *UserManager) SetChange(user *User) {
	u.changes[user.GetUid()] = user.GetData()
}

func (u *UserManager) DbSave() ([]*cr.BalanceArenaUser, []*cr.BalanceArenaSeasonUser) {
	users := make([]*cr.BalanceArenaUser, 0, len(u.changes))
	for _, user := range u.changes {
		users = append(users, user.Clone())
	}
	u.changes = make(map[uint64]*cr.BalanceArenaUser)

	seasonUsers := make([]*cr.BalanceArenaSeasonUser, 0, len(u.seasonChanges))
	for _, seasonUser := range u.seasonChanges {
		seasonUsers = append(seasonUsers, seasonUser.Clone())
	}
	u.seasonChanges = make(map[uint64]*cr.BalanceArenaSeasonUser)
	return users, seasonUsers
}

func (u *UserManager) RoundClear() {
	u.users = make(map[uint64]*User)
	u.changes = make(map[uint64]*cr.BalanceArenaUser)
	u.smallGroup = make(map[uint32][]uint64)
	u.bigGroup = make(map[uint32][]uint64)
	u.elimiUser = NewElimiUser(u.mgr)
}

func (u *UserManager) SeasonClear() {
	u.seasonUsers = make(map[uint64]*SeasonUser)
	u.seasonChanges = make(map[uint64]*cr.BalanceArenaSeasonUser)
}

func (u *UserManager) IsAllRobotMatch(match *cr.BalanceArenaMatch) bool {
	leftUser := u.GetSeasonUser(match.LeftUser.UserSnapshot.Id)
	rightUser := u.GetSeasonUser(match.RightUser.UserSnapshot.Id)
	return leftUser.GetRobotId() > 0 && rightUser.GetRobotId() > 0
}

func (u *UserManager) GetUsers() map[uint64]*User {
	return u.users
}

func (u *UserManager) getEliminationUsers() []*User {
	users := make([]*User, 0, EliminationUsersCount)
	for _, v := range u.users {
		if v.GetData().SmallGroup != nil && v.GetData().SmallGroup.GroupId != 0 && !v.GetData().SmallGroup.Finish {
			users = append(users, v)
		}
	}
	return users
}

func (u *UserManager) GetSeasonUser(uid uint64) *SeasonUser {
	return u.seasonUsers[uid]
}

func (u *UserManager) SetSeasonChange(user *SeasonUser) {
	u.seasonChanges[user.GetUid()] = user.GetData()
}

func (u *UserManager) AddSeasonUser(user *SeasonUser) {
	u.seasonUsers[user.GetUid()] = user
	u.SetSeasonChange(user)
}

func (u *UserManager) LoadSeasonUser(user *SeasonUser) {
	u.seasonUsers[user.GetUid()] = user
}
