package balancearena

import (
	"app/protos/in/cr"
	"cmp"
	l4g "github.com/ivanabc/log4go"
	"slices"
)

type UserManager struct {
	mgr        *Manager
	users      map[uint64]*User // 报名参加玩家
	changes    map[uint64]*cr.BalanceArenaUser
	bigGroup   map[uint32][]uint64 // 大组赛分组 bigGroupId => users
	smallGroup map[uint32][]uint64 // 小组赛分组 smallGroupId => users

	elimiUser *ElimiUser
	// TODO(yta) 注意参赛选手的信息如果变化需要同步
}

func newUserManager(manager *Manager) *UserManager {
	return &UserManager{
		mgr:        manager,
		users:      make(map[uint64]*User),
		changes:    make(map[uint64]*cr.BalanceArenaUser),
		bigGroup:   make(map[uint32][]uint64),
		smallGroup: make(map[uint32][]uint64),
		elimiUser:  NewElimiUser(manager),
	}
}

func (u *UserManager) Load(users map[uint64]*cr.BalanceArenaUser) {
	bigGroupUsers := make(map[uint32][]*User)
	smallGroupUsers := make(map[uint32][]*User)
	for _, data := range users {
		if data == nil {
			continue
		}
		user := NewUser(data, u.mgr)
		u.AddUser(user)
		if data.BigGroup != nil && data.BigGroup.GroupId != 0 {
			bigGroupUsers[data.BigGroup.GroupId] = append(bigGroupUsers[data.BigGroup.GroupId], user)
		}
		if data.SmallGroup != nil && data.SmallGroup.GroupId != 0 {
			smallGroupUsers[data.SmallGroup.GroupId] = append(smallGroupUsers[data.SmallGroup.GroupId], user)
		}
		if data.Elimination != nil && data.Elimination.GroupId != 0 {
			u.elimiUser.LoadUser(user)
		}
		u.mgr.GetRanksManager().load(user)
	}
	// 每个Group根据User的GroupPos进行排序
	for groupId, groupUsers := range bigGroupUsers {
		slices.SortFunc(groupUsers, func(a, b *User) int {
			return cmp.Compare(a.GetData().GetBigGroup().GroupPos, b.GetData().GetBigGroup().GroupPos)
		})
		uids := make([]uint64, 0, len(groupUsers))
		for _, v := range groupUsers {
			uids = append(uids, v.GetUid())
		}
		u.bigGroup[groupId] = uids
	}
	for groupId, groupUsers := range smallGroupUsers {
		slices.SortFunc(groupUsers, func(a, b *User) int {
			return cmp.Compare(a.GetData().GetSmallGroup().GroupPos, b.GetData().GetSmallGroup().GroupPos)
		})
		uids := make([]uint64, 0, len(groupUsers))
		for _, v := range groupUsers {
			uids = append(uids, v.GetUid())
		}
		u.smallGroup[groupId] = uids
	}
	//排序
	u.elimiUser.SortUsers()
}

func (u *UserManager) GetUser(uid uint64) *User {
	if u.users[uid] == nil {
		l4g.Debugf("yta test GetUser err %d", uid)
	}
	return u.users[uid]
}

func (u *UserManager) AddUser(user *User) {
	u.users[user.GetUid()] = user
	u.SetChange(user)
}

func (u *UserManager) SetChange(user *User) {
	u.changes[user.GetUid()] = user.GetData()
}

func (u *UserManager) DbSave() []*cr.BalanceArenaUser {
	users := make([]*cr.BalanceArenaUser, 0, len(u.changes))
	for _, user := range u.changes {
		users = append(users, user.Clone())
	}
	u.changes = make(map[uint64]*cr.BalanceArenaUser)
	return users
}

func (u *UserManager) RoundClear() {
	// 保留玩家快照和赛季积分
	for _, user := range u.users {
		data := user.GetData()
		user = NewUser(&cr.BalanceArenaUser{
			Uid:         data.GetUid(),
			Snapshot:    data.GetSnapshot(),
			SeasonScore: data.GetSeasonScore(),
		}, u.mgr)
		u.SetChange(user)
	}
	u.smallGroup = make(map[uint32][]uint64)
	u.bigGroup = make(map[uint32][]uint64)
	u.elimiUser = NewElimiUser(u.mgr)
}

func (u *UserManager) SeasonClear() {
	u.users = make(map[uint64]*User)
	u.changes = make(map[uint64]*cr.BalanceArenaUser)
	u.smallGroup = make(map[uint32][]uint64)
	u.bigGroup = make(map[uint32][]uint64)
	u.elimiUser = NewElimiUser(u.mgr)
}

func (u *UserManager) IsAllRobotMatch(match *cr.BalanceArenaMatch) bool {
	leftUser := u.GetUser(match.LeftUser.UserSnapshot.Id)
	rightUser := u.GetUser(match.RightUser.UserSnapshot.Id)
	return leftUser.GetRobotId() > 0 && rightUser.GetRobotId() > 0
}

func (u *UserManager) GetUsers() map[uint64]*User {
	return u.users
}

func (u *UserManager) getEliminationUsers() []*User {
	users := make([]*User, 0, EliminationUsersCount)
	for _, v := range u.users {
		if v.GetData().SmallGroup != nil && !v.GetData().SmallGroup.Finish {
			users = append(users, v)
		}
	}
	return users
}
