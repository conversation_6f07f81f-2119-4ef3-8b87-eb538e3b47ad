package balancearena

import (
	"app/cross/activity"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/r2c"
	"app/protos/out/common"
	"app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
	"sync/atomic"
)

const (
	stateInit int32 = iota
	stateFinishLoad
	stateRunning
)

type Manager struct {
	baseModule    activity.BaseModuler
	actID, partID uint32
	functionState int32 // 功能状态（数据加载与运行）

	stateM *StateManager // 赛程状态管理器
	userM  *UserManager  // 玩家管理器
	matchM *MatchManager // 比赛管理器

	publicHeroes []uint32 // 公共英雄（赛期重置）TODO 看放到哪里合适

	rand       *rand.Rand
	incr, freq int64
}

func NewManager() *Manager {
	m := &Manager{}
	m.rand = rand.New(time.Now().Unix())
	m.stateM = newStateManager(m)
	m.userM = newUserManager(m)
	m.matchM = newMatchManager(m)
	m.freq = 50
	// TODO
	return m
}

func (m *Manager) Load(data *r2c.R2C_BalanceArenaLoad) {
	if data == nil {
		l4g.Errorf("balanceArena.Load: load data failed. data is nil")
		return
	}
	if data.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.Load: load data failed. ret:%d", data.Ret)
		return
	}

	m.GetStateManager().Load(data.State)
	m.GetUserManager().Load(data.Users)
	m.GetMatchManager().Load(data.Matches)

	m.SetFunctionState(stateFinishLoad)
	l4g.Infof("balanceArena.Load: success, actID:%d, partID:%d data:%+v", m.actID, m.partID, data)
}

func (m *Manager) Save(baseModule activity.BaseModuler) {
	msg := &r2c.C2R_BalanceArenaSave{
		Partition: m.partID,
	}

	var needSave bool
	// TODO(yta)

	if needSave {
		baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaSave, msg)
	}
}

func (m *Manager) GetFunctionState() int32 {
	return atomic.LoadInt32(&m.functionState)
}

func (m *Manager) SetFunctionState(functionState int32) {
	atomic.StoreInt32(&m.functionState, functionState)
}

func (m *Manager) GetStateManager() *StateManager {
	return m.stateM
}

func (m *Manager) GetUserManager() *UserManager {
	return m.userM
}

func (m *Manager) GetMatchManager() *MatchManager {
	return m.matchM
}

/********** 实现 activity.Moduler **********/
func (m *Manager) Init(baseModule activity.BaseModuler) bool {
	m.baseModule = baseModule
	m.actID = baseModule.GetActivityID()
	m.partID = baseModule.Partition()
	l4g.Infof("balanceArena.Init: start load data. actID:%d, partID:%d, sids:%v", m.actID, m.partID, baseModule.GetServerIDList())
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaLoad, &r2c.C2R_BalanceArenaLoad{Partition: m.partID})
	return true
}

func (m *Manager) CheckRunning() bool {
	return m.functionState == stateRunning
}

func (m *Manager) Update(moduler activity.BaseModuler, now int64) {
	if m.GetFunctionState() < stateFinishLoad {
		l4g.Errorf("balanceArena.Update: not finish load. actID:%d, partID:%d", m.actID, m.partID)
		return
	}
	// TODO(yta) peak在这里有一个commonSave
	m.run(now)
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(moduler)
	}

	m.SetFunctionState(stateRunning) //确保完成一次心跳update后，才让logic请求进来
}

func (m *Manager) Close(moduler activity.BaseModuler) {
	m.Save(moduler)
}

func (m *Manager) DeletePartition(moduler activity.BaseModuler) {
}

func (m *Manager) CheckCanResetPart(i int64) bool {
	return false
}

func (m *Manager) ProcessGrpcRequest(request *activity.GrpcRequest) error {
	return nil
}

func (m *Manager) TransformMsg(moduler activity.BaseModuler, msg *activity.TransformCtrlMsg) {
}

/********** 实现 activity.Moduler **********/

/********** 状态变化 **********/
// TODO(yta) 状态变化如何落地
func (m *Manager) run(now int64) {
	l4g.Debugf("yta test run.")
	state := m.stateM.GetSta()
	if state == nil {
		l4g.Errorf("balanceArena.stateRun: state is nil")
		return
	}

	switch state.GetRoundState() {
	case uint32(common.BALANCE_ARENA_ROUND_STATE_BARS_INIT): // 赛期初始化
		m.RoundInit(now)
	case uint32(common.BALANCE_ARENA_ROUND_STATE_BARS_RUNNING): // 赛期进行中
		m.RoundRunning(now, state)
	case uint32(common.BALANCE_ARENA_ROUND_STATE_BARS_END): // 赛期结束
		m.RoundEnd(now)
	default:
		l4g.Errorf("balanceArena.stateRun: round state err. state:%d", state.GetRoundState())
		return
	}
}

func (m *Manager) RoundInit(now int64) {
	// 根据当前时间获取新的赛期
	round := goxml.GetData().BalanceArenaInfoM.GetCurrentRound(now)
	if round == nil {
		return
	}

	// 构造新赛期的状态
	state := &cr.BalanceArenaState{
		SeasonId:   round.SeasonId,
		Round:      round.Id,
		RoundState: uint32(common.BALANCE_ARENA_ROUND_STATE_BARS_RUNNING), // TODO(yta) peak为什么要添加一个init状态
	}

	// TODO(yta) 清除上个赛期的数据
	// 设置本赛期数据
	m.stateM.SetSta(state)
	m.stateM.PushSta()

	l4g.Infof("balanceArena.RoundInit: now:%d state:%v", now, state)
}

func (m *Manager) RoundRunning(now int64, state *cr.BalanceArenaState) {
	// TODO 判断当前阶段的工作是否已经完成
	if !m.StageCheckFinish(now, state) {
		// 未完成当前阶段的工作，继续完成
		m.StageRunning(now, state)
		return
	}

	// TODO 判断当前阶段的时间是否已经结束
	if !m.StageCheckTime(now, state) {
		l4g.Debugf("balanceArena.RoundRunning: stage time unfinished. now:%d state:%v", now, state)
		return
	}

	// 进入下一个阶段
	m.stateM.SetSta(state)
	m.stateM.PushSta()

	l4g.Infof("balanceArena.RoundRunning: next stage. now:%d state:%v", now, state)
}

func (m *Manager) RoundEnd(now int64) {
	// TODO 结束当前赛期
}

func (m *Manager) StageCheckFinish(now int64, state *cr.BalanceArenaState) bool {
	// 不同阶段类型有不同的任务
	var phaseType uint32 // TODO(yta) 使用 info.PhaseType
	switch phaseType {
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_PREPARE): // 准备阶段
		if len(m.GetPublicHeroes()) == 0 {
			l4g.Debugf("balanceArena.StageCheckFinish: public heroes not ready. now:%d state:%v", now, state)
			return false
		}
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_SIGN): // 报名阶段
		// TODO
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_BIG_GROUP_MATCH_PREPARE): // 大组赛准备阶段
		// TODO 检查是否完成分组和构造所有的比赛
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_BIG_GROUP_MATCH): // 大组赛战斗阶段
		// TODO 检查当前阶段的战斗是否已完成
		//if m.GetMatchManager().checkAllFinished() {
		//	m.roundFightFinish(baseModule)
		//}
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_SMALL_GROUP_MATCH_PREPARE): // 小组赛准备阶段
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_SMALL_GROUP_MATCH): // 小组赛战斗阶段
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_ELIMINATION_MATCH): // 淘汰赛阶段
	default:
		l4g.Errorf("balanceArena.StageInit: phase type err. type:%d", phaseType)
		return false
	}
	return true
}

func (m *Manager) StageCheckTime(now int64, state *cr.BalanceArenaState) bool {
	return false
}

func (m *Manager) StageRunning(now int64, state *cr.BalanceArenaState) {
	// 不同阶段类型有不同的任务
	var phaseType uint32 // TODO(yta) 使用 info.PhaseType
	switch phaseType {
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_PREPARE): // 准备阶段
		m.RandomPublicHeroes()
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_SIGN): // 报名阶段
		// TODO
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_BIG_GROUP_MATCH_PREPARE): // 大组赛准备阶段
		// TODO
		// 1. 分组
		// 2. 构造所有需要打的比赛
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_BIG_GROUP_MATCH): // 大组赛战斗阶段
		// TODO 进行战斗
		/*
			if nowRound == 0 {
				nowRound = goxml.PeakRound1
			}
			endTime := goxml.GetData().PeakConfigInfoM.GetRoundFightEndTime(nowSeason, nowPhase, nowRound)
			if now >= endTime {
				m.requestFightSpecial(baseModule, nowRound, now)
			} else {
				if m.canRetryRequestFight() {
					m.requestFightNormal(baseModule, nowRound, now)
				}
			}
		*/
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_SMALL_GROUP_MATCH_PREPARE): // 小组赛准备阶段
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_SMALL_GROUP_MATCH): // 小组赛战斗阶段
	case uint32(common.BALANCE_ARENA_STAGE_TYPE_BAST_ELIMINATION_MATCH): // 淘汰赛阶段
	default:
		l4g.Errorf("balanceArena.StageInit: phase type err. type:%d", phaseType)
		return
	}
}

/********** 状态变化 **********/

func (m *Manager) GetPublicHeroes() []uint32 {
	return m.publicHeroes
}

// TODO 随机公共英雄
func (m *Manager) RandomPublicHeroes() []uint32 {
	return nil
}
