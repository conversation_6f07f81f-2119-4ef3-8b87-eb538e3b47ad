package balancearena

import (
	"app/cross/activity"
	"gitlab.qdream.com/kit/sea/math/rand"
)

const (
	stateInit int32 = iota
	stateFinishLoad
	stateRunning
)

type Manager struct {
	baseModule    activity.BaseModuler
	actID, partID uint32

	functionState  int32         // 功能状态（数据加载与运行）
	scheduleStateM *StateManager // 赛程状态管理器
	userM          *UserManager  // 玩家管理器

	rand       *rand.Rand
	incr, freq int64
}
