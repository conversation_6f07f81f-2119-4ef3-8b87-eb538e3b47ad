package balancearena

import (
	"app/cross/activity"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/r2c"
	"app/protos/out/common"
	"app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
	"sync/atomic"
)

const (
	stateInit int32 = iota
	stateFinishLoad
	stateRunning
)

type Manager struct {
	baseModule    activity.BaseModuler
	actID, partID uint32
	functionState int32 // 功能状态（数据加载与运行）

	stateM *StateManager // 赛程状态管理器
	userM  *UserManager  // 玩家管理器
	matchM *MatchManager // 比赛管理器

	publicHeroes []uint32 // 公共英雄（赛期重置）TODO 看放到哪里合适

	rand       *rand.Rand
	incr, freq int64
}

func NewManager() *Manager {
	m := &Manager{}
	m.rand = rand.New(time.Now().Unix())
	m.stateM = newStateManager(m)
	m.userM = newUserManager(m)
	m.matchM = newMatchManager(m)
	m.freq = 50
	// TODO
	return m
}

func (m *Manager) Load(data *r2c.R2C_BalanceArenaLoad) {
	if data == nil {
		l4g.Errorf("balanceArena.Load: load data failed. data is nil")
		return
	}
	if data.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.Load: load data failed. ret:%d", data.Ret)
		return
	}

	m.GetStateManager().Load(data.State)
	m.GetUserManager().Load(data.Users)
	m.GetMatchManager().Load(data.Matches)

	m.SetFunctionState(stateFinishLoad)
	l4g.Infof("balanceArena.Load: success, actID:%d, partID:%d data:%+v", m.actID, m.partID, data)
}

func (m *Manager) Save(baseModule activity.BaseModuler) {
	msg := &r2c.C2R_BalanceArenaSave{
		Partition: m.partID,
	}

	// TODO 战斗结算阶段不能保存太多频繁

	var needSave bool
	state := m.GetStateManager().DbSave()
	if state != nil {
		msg.State = state
		needSave = true
	}

	// TODO user.Save

	// TODO match.Save

	if needSave {
		baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaSave, msg)
	}
}

func (m *Manager) GetFunctionState() int32 {
	return atomic.LoadInt32(&m.functionState)
}

func (m *Manager) SetFunctionState(functionState int32) {
	atomic.StoreInt32(&m.functionState, functionState)
}

func (m *Manager) GetStateManager() *StateManager {
	return m.stateM
}

func (m *Manager) GetUserManager() *UserManager {
	return m.userM
}

func (m *Manager) GetMatchManager() *MatchManager {
	return m.matchM
}

/********** 实现 activity.Moduler **********/
func (m *Manager) Init(baseModule activity.BaseModuler) bool {
	m.baseModule = baseModule
	m.actID = baseModule.GetActivityID()
	m.partID = baseModule.Partition()
	l4g.Infof("balanceArena.Init: start load data. actID:%d, partID:%d, sids:%v", m.actID, m.partID, baseModule.GetServerIDList())
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaLoad, &r2c.C2R_BalanceArenaLoad{Partition: m.partID})
	return true
}

func (m *Manager) CheckRunning() bool {
	return m.functionState == stateRunning
}

func (m *Manager) Update(moduler activity.BaseModuler, now int64) {
	if m.GetFunctionState() < stateFinishLoad {
		l4g.Errorf("balanceArena.Update: not finish load. actID:%d, partID:%d", m.actID, m.partID)
		return
	}
	m.stateChange(now)
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(moduler)
	}

	m.SetFunctionState(stateRunning) //确保完成一次心跳update后，才让logic请求进来
}

func (m *Manager) Close(moduler activity.BaseModuler) {
	m.Save(moduler)
}

func (m *Manager) DeletePartition(moduler activity.BaseModuler) {
}

func (m *Manager) CheckCanResetPart(i int64) bool {
	return false
}

func (m *Manager) ProcessGrpcRequest(request *activity.GrpcRequest) error {
	return nil
}

func (m *Manager) TransformMsg(moduler activity.BaseModuler, msg *activity.TransformCtrlMsg) {
}

/********** 实现 activity.Moduler **********/

/********** 状态变化 **********/
func (m *Manager) stateChange(now int64) {
	state := m.stateM.GetSta()
	if state == nil {
		l4g.Errorf("balanceArena.stateChange: state is nil")
		return
	}

	// 根据当前时间获取新的赛期
	curRoundInfo := goxml.GetData().BalanceArenaInfoM.GetCurrentRound(now)
	if curRoundInfo == nil {
		l4g.Errorf("balanceArena.stateChange: get current round failed. now:%d", now)
		return
	}
	curRound := curRoundInfo.Id

	// 赛期变化
	// 1. round != curRound && round = 0 : Round1
	// 2. round != curRound && stage = Show : Round1 -> Round2
	round, stage := state.Round, state.Stage
	if round != curRound && (round == 0 || stage == uint32(common.BALANCE_ARENA_STAGE_BAS_SHOW)) {
		m.RoundInit(now, curRoundInfo) // 新赛期
	} else {
		m.RoundRunning(now, state) // 当前赛期继续运行
	}
}

func (m *Manager) RoundInit(now int64, roundInfo *goxml.BalanceArenaInfo) {
	// 清除上个赛期的数据
	m.RoundClearData()
	m.RoundClearDB(m.baseModule)

	// 设置本赛期状态
	state := &cr.BalanceArenaState{
		SeasonId: roundInfo.SeasonId,
		Round:    roundInfo.Id,
		Stage:    uint32(common.BALANCE_ARENA_STAGE_BAS_INIT),
	}
	m.stateM.SetSta(state)
	m.stateM.PushSta()

	l4g.Infof("balanceArena.RoundInit: now:%d state:%v", now, state)
}

func (m *Manager) RoundRunning(now int64, state *cr.BalanceArenaState) {
	// TODO 判断当前阶段的工作是否已经完成
	if !m.StageCheckFinish(now, state) {
		// 未完成当前阶段的工作，继续完成
		m.StageRunning(now, state)
		return
	}

	// TODO 判断当前阶段的时间是否已经结束
	if !m.StageCheckTime(now, state) {
		l4g.Debugf("balanceArena.RoundRunning: stage time unfinished. now:%d state:%v", now, state)
		return
	}

	// 进入下一个阶段（展示阶段没有下一个阶段）
	nextStage := m.stateM.GetNextStage(state)
	if nextStage != nil {
		m.stateM.SetSta(nextStage)
		m.stateM.PushSta()
	}

	l4g.Infof("balanceArena.RoundRunning: next stage. now:%d state %+v nextStage:%v", now, state, nextStage)
}

func (m *Manager) RoundClearData() {
	m.GetUserManager().RoundClear()
	m.GetMatchManager().RoundClear()
}

func (m *Manager) RoundClearDB(baseModule activity.BaseModuler) {
	msg := &r2c.C2R_BalanceArenaClearRound{
		Partition: m.partID,
	}
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaClearRound, msg)
}

func (m *Manager) StageCheckFinish(now int64, state *cr.BalanceArenaState) bool {
	// 不同阶段有不同的任务
	stage := state.Stage
	switch stage {
	case uint32(common.BALANCE_ARENA_STAGE_BAS_INIT): // 初始化阶段
		// TODO 随机公共英雄
		if len(m.GetPublicHeroes()) == 0 {
			l4g.Debugf("balanceArena.StageCheckFinish: public heroes not ready. now:%d state:%v", now, state)
			return false
		}
	case uint32(common.BALANCE_ARENA_STAGE_BAS_PREPARE): // 准备阶段
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SIGN): // 报名阶段
		// TODO
	case uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH): // 大组赛阶段
		// 检查大组赛的比赛是否 最后一轮 & 完成
		bigGroupMatch := m.GetMatchManager().bigGroupMatchExtra
		if bigGroupMatch == nil {
			l4g.Debugf("balanceArena.StageCheckFinish: big group match not ready. now:%d state:%v", now, state)
			return false
		}
		matchRound := bigGroupMatch.matchRound
		var matchType uint32 // TODO 比赛类型
		if !matchRound.IsLastMatchRound(matchType) || !matchRound.CheckTaskFinish() {
			return false
		}
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH): // 小组赛阶段
		// TODO 类似大组赛
	case uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH): // 淘汰赛阶段
	case uint32(common.BALANCE_ARENA_STAGE_BAS_FINAL_MATCH): // 决赛阶段
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SHOW): // 展示阶段
	default:
		l4g.Errorf("balanceArena.StageCheckFinish: stage err. type:%d", stage)
		return false
	}
	return true
}

// 判断当前阶段的时间是否已经结束
func (m *Manager) StageCheckTime(now int64, state *cr.BalanceArenaState) bool {
	// TODO 根据阶段获取持续时间

	return false
}

func (m *Manager) StageRunning(now int64, state *cr.BalanceArenaState) {
	stage := state.Stage
	switch stage {
	case uint32(common.BALANCE_ARENA_STAGE_BAS_INIT): // 初始化阶段
		m.RandomPublicHeroes()
	case uint32(common.BALANCE_ARENA_STAGE_BAS_PREPARE): // 准备阶段
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SIGN): // 报名阶段
		// TODO
	case uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH): // 大组赛阶段
		// 1. 未完成分组需要进行分组
		bigGroupMatch := m.GetMatchManager().bigGroupMatchExtra
		if !bigGroupMatch.IsAssignFinish() {
			bigGroupMatch.Assign()
		}

		// 2. 已完成分组且满足战斗开始时间可以进行战斗
		// TODO 对于超时的战斗是否需要和巅峰一样特殊处理 requestFightSpecial
		if bigGroupMatch.CanFightStart() {
			m.GetMatchManager().Fight(m.baseModule, stage, now)
		}
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH): // 小组赛战斗阶段
	case uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH): // 淘汰赛阶段
	case uint32(common.BALANCE_ARENA_STAGE_BAS_FINAL_MATCH): // 决赛阶段
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SHOW): // 展示阶段
	default:
		l4g.Errorf("balanceArena.StageRunning: stage err. type:%d", stage)
		return
	}
}

/********** 状态变化 **********/

func (m *Manager) GetPublicHeroes() []uint32 {
	return m.publicHeroes
}

// TODO 随机公共英雄
func (m *Manager) RandomPublicHeroes() []uint32 {
	// TODO 获取随机的数量
	goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("")

	return nil
}
