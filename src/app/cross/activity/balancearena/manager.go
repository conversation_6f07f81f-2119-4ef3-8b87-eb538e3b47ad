package balancearena

import (
	"app/cross/activity"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/in/r2c"
	"app/protos/out/common"
	"app/protos/out/ret"
	"sync/atomic"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
)

const (
	stateInit int32 = iota
	stateFinishLoad
	stateRunning
)

type Manager struct {
	baseModule    activity.BaseModuler
	actID, partID uint32
	functionState int32 // 功能状态（数据加载与运行）

	data       *BalanceArena
	stateM     *StateManager // 赛程状态管理器
	userM      *UserManager  // 玩家管理器
	matchM     *MatchManager // 比赛管理器
	ranksM     *RanksManager
	rand       *rand.Rand
	incr, freq int64
}

func NewManager() *Manager {
	m := &Manager{}
	m.rand = rand.New(time.Now().Unix())
	m.data = newBalanceArena(m)
	m.stateM = newStateManager(m)
	m.userM = newUserManager(m)
	m.matchM = newMatchManager(m)
	m.ranksM = NewRanksManager(m)
	m.freq = 50
	// TODO
	return m
}

func (m *Manager) Load(data *r2c.R2C_BalanceArenaLoad) {
	l4g.Infof("balanceArena.Load: actID:%d, partID:%d data:%+v", m.actID, m.partID, data)
	if data == nil {
		l4g.Errorf("balanceArena.Load: load data failed. data is nil")
		return
	}
	if data.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.Load: load data failed. ret:%d", data.Ret)
		return
	}

	m.GetData().load(data.BalanceArena)
	m.GetStateManager().Load(data.State)
	m.GetUserManager().Load(data.Users)
	m.GetMatchManager().Load(data.Matches)
	m.SetFunctionState(stateFinishLoad)
	l4g.Infof("balanceArena.Load: success, actID:%d, partID:%d data:%+v", m.actID, m.partID, data)
}

func (m *Manager) Save(baseModule activity.BaseModuler) {
	msg := &r2c.C2R_BalanceArenaSave{
		Partition: m.partID,
	}

	// TODO 战斗结算阶段不能保存太多频繁

	var needSave bool
	data := m.data.DbSave()
	if data != nil {
		msg.BalanceArena = data
		needSave = true
	}

	state := m.GetStateManager().DbSave()
	if state != nil {
		msg.State = state
		needSave = true
	}

	users := m.GetUserManager().DbSave()
	if len(users) > 0 {
		msg.Users = users
		needSave = true
	}

	matches := m.GetMatchManager().DbSave()
	if len(matches) > 0 {
		msg.Matches = matches
		needSave = true
	}

	if needSave {
		baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaSave, msg)
	}
}

func (m *Manager) GetFunctionState() int32 {
	return atomic.LoadInt32(&m.functionState)
}

func (m *Manager) SetFunctionState(functionState int32) {
	atomic.StoreInt32(&m.functionState, functionState)
}

func (m *Manager) GetData() *BalanceArena {
	return m.data
}

func (m *Manager) GetStateManager() *StateManager {
	return m.stateM
}

func (m *Manager) GetUserManager() *UserManager {
	return m.userM
}

func (m *Manager) GetMatchManager() *MatchManager {
	return m.matchM
}

func (m *Manager) GetRanksManager() *RanksManager {
	return m.ranksM
}

/********** 实现 activity.Moduler **********/
func (m *Manager) Init(baseModule activity.BaseModuler) bool {
	m.baseModule = baseModule
	m.actID = baseModule.GetActivityID()
	m.partID = baseModule.Partition()
	l4g.Infof("balanceArena.Init: start load data. actID:%d, partID:%d, sids:%v", m.actID, m.partID, baseModule.GetServerIDList())
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaLoad, &r2c.C2R_BalanceArenaLoad{Partition: m.partID})
	return true
}

func (m *Manager) CheckRunning() bool {
	return m.functionState == stateRunning
}

func (m *Manager) Update(moduler activity.BaseModuler, now int64) {
	if m.GetFunctionState() < stateFinishLoad {
		l4g.Errorf("balanceArena.Update: not finish load. actID:%d, partID:%d", m.actID, m.partID)
		return
	}
	m.stateUpdate(now)
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(moduler)
	}

	m.SetFunctionState(stateRunning) //确保完成一次心跳update后，才让logic请求进来
}

func (m *Manager) Close(moduler activity.BaseModuler) {
	m.Save(moduler)
}

func (m *Manager) DeletePartition(moduler activity.BaseModuler) {
}

func (m *Manager) CheckCanResetPart(i int64) bool {
	return false
}

func (m *Manager) ProcessGrpcRequest(request *activity.GrpcRequest) error {
	return nil
}

func (m *Manager) TransformMsg(moduler activity.BaseModuler, msg *activity.TransformCtrlMsg) {
}

/********** 实现 activity.Moduler **********/

/********** 状态变化 **********/
func (m *Manager) stateUpdate(now int64) {
	state := m.stateM.GetState()
	if state == nil || state.Round == 0 {
		curRoundInfo := goxml.GetData().BalanceArenaInfoM.GetCurrentRound(now)
		if curRoundInfo == nil {
			l4g.Errorf("balanceArena.stateUpdate: get current round failed. now:%d", now)
			return
		}
		m.NewRound(now, curRoundInfo) // 新赛期
	} else {
		m.StateRunning(now, state) // 当前赛期继续运行
	}
}

func (m *Manager) NewRound(now int64, roundInfo *goxml.BalanceArenaInfo) {
	// 清除上个赛期的数据
	m.RoundClearData()
	m.RoundClearDB(m.baseModule)

	// 设置本赛期状态
	state := &cr.BalanceArenaState{
		SeasonId: roundInfo.SeasonId,
		Round:    roundInfo.Id,
		Phase:    uint32(common.BALANCE_ARENA_STAGE_BAS_INIT),
	}
	m.stateM.SetState(state)

	l4g.Infof("balanceArena.NewRound: now:%d state:%v", now, state)
}

func (m *Manager) RoundClearData() {
	m.GetData().RoundClear()
	m.GetUserManager().RoundClear()
	m.GetMatchManager().RoundClear()
}

// TODO 数据库数据的处理
func (m *Manager) RoundClearDB(baseModule activity.BaseModuler) {
	msg := &r2c.C2R_BalanceArenaClearRound{
		Partition: m.partID,
	}
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_BalanceArenaClearRound, msg)
}

func (m *Manager) StateRunning(now int64, state *cr.BalanceArenaState) {
	switch state.Phase {
	case uint32(common.BALANCE_ARENA_STAGE_BAS_INIT): // 初始化阶段（无持续时间）
		m.data.RandomPublicHeroes()
		m.stateM.SetNextState(0)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_PREPARE): // 准备阶段
		if !m.stateM.CheckStateEnd(now) {
			return
		}
		m.stateM.SetNextState(state.PhaseConfigId)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SIGN): // 报名阶段
		if !m.stateM.CheckStateEnd(now) {
			return
		}
		m.stateM.SetNextState(state.PhaseConfigId)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH): // 大组赛阶段
		bigGroup := m.GetData().GetBigGroupData()
		bigGroup.stateRunning(now, state)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH): // 小组赛阶段
		smallGroup := m.GetData().GetSmallGroupData()
		smallGroup.stateRunning(now, state)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_1),
		uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_2),
		uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_3): // 淘汰赛阶段
		elimi := m.GetData().GetEliminationData()
		elimi.stateRunning(now, state)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SHOW): // 展示阶段
		maxInfo := goxml.GetData().BalanceArenaInfoM.GetMaxGameNumRecordBySeasonId(state.SeasonId)
		if maxInfo == nil {
			l4g.Errorf("balanceArena.stateUpdate: get max game num record failed. seasonId:%d", state.SeasonId)
			return
		}
		if m.GetData() == nil {
			l4g.Errorf("balanceArena.stateUpdate: balanceArena Data nil. ")
			return
		}
		if state.Round == maxInfo.Id && !m.GetData().CheckSettleFinish(now) {
			// 最后一轮,需要结算赛季排行榜奖励
			l4g.Infof("公平竞技场赛季奖励结算中 round:%d", state.Round)
			return
		}
		curRoundInfo := goxml.GetData().BalanceArenaInfoM.GetCurrentRound(now)
		if curRoundInfo == nil {
			l4g.Errorf("balanceArena.stateUpdate: get current round failed. now:%d", now)
			return
		}
		if curRoundInfo.Id != state.Round {
			if curRoundInfo.SeasonId != state.SeasonId {
				// TODO 等景风的提交
				// m.NewSeason() // 新赛季
			} else {
				m.NewRound(now, curRoundInfo) // 新赛期
			}
		}
	default:
		l4g.Errorf("balanceArena.StageRunning: phase err. type:%d", state.Phase)
		return
	}
}

func (m *Manager) FinishSettle(sid uint64, state *cr.BalanceArenaState) {
	switch state.Phase {
	case uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH):
		m.GetData().GetBigGroupData().FinishSettle(sid)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH):
		m.GetData().GetSmallGroupData().FinishSettle(sid)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_3):
		m.GetData().GetEliminationData().FinishSettle(sid)
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SHOW):
		m.GetData().FinishSettle(sid)
	default:
		l4g.Errorf("balanceArena.FinishSettle: phase err:%d", state.Phase)
		return
	}

}

func (m *Manager) GetRankList(uid uint64, req *l2c.L2CS_BalanceArenaGetRankList, rsp *l2c.CS2L_BalanceArenaGetRankList) {
	var rankM *RankManager
	rankingInfo := goxml.GetData().RankingInfoM.Index(req.Req.RankId)
	if rankingInfo == nil {
		l4g.Errorf("RankingInfoM cant find %d", req.Req.RankId)
		rsp.Rsp.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return
	}
	start, end, rankType := uint32(1), rankingInfo.ShowCount, uint32(0)
	switch req.Req.RankId {
	case goxml.BalanceArenaSeasonRank: // 赛季排行
		rankType = goxml.BalanceArenaRankTypeSeason
		rankM = m.GetRanksManager().GetSeasonRank()
	case goxml.BalanceArenaSmallGroupRank: // 小组排行
		rankType = goxml.BalanceArenaRankTypeSmallGroup
		rankM = m.GetRanksManager().GetSmallGroupRankM(req.Req.Group)
	case goxml.BalanceArenaBigGroupRank: // 大组排行
		rankType = goxml.BalanceArenaRankTypeBigGroup
		rankM = m.GetRanksManager().GetBigGroupRankM(req.Req.Group)
	default:
		l4g.Errorf("balanceArena.GetRankList: rankType err:%d", req.Req.RankId)
		return
	}
	rsp.Rsp.List = rankM.GetShowValueByRange(m, start, end, rankType)
	rsp.Rsp.SelfValue = rankM.GetShowValue(m, uid, rankType)
}

func (m *Manager) Reward(uid uint64) uint32 {
	return m.GetData().GetEliminationData().Reward(uid)
}

/********** 状态变化 **********/
