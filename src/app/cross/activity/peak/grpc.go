package peak

import (
	"app/cross/activity"
	"app/goxml"
	"app/protos/in/l2c"
	"errors"

	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func (m *Manager) ProcessGrpcRequest(msg *activity.GrpcRequest) error {
	l4g.Errorf("peakM. no found cmd:%d", msg.GetCmd())
	return errors.New("no found cmd")
}

func (m *Manager) TransformMsg(baseModule activity.BaseModuler, msg *activity.TransformCtrlMsg) {
	l4g.Debugf("peakM.TransformMsg: partition:%d get transformmsg:%+v", m.partID, msg)
	if msg.ProtoId == uint32(l2c.ID_MSG_L2C_PeakInitData) {
		recvMsg := &l2c.L2C_PeakInitData{}
		if err := proto.Unmarshal(msg.Data, recvMsg); err != nil {
			l4g.Errorf("[FATAL] L2C_PeakInitData: unmarshal err:%v", err)
			return
		}
		l4g.Debugf("peakM.TransformMsg: partition:%d get recvMsg:%+v", m.partID, recvMsg)

		//报名数据未准备好，需要重试
		if recvMsg.GetRet() == uint32(cret.RET_CROSS_PEAK_INIT_DATA_PREPARING) {
			l4g.Debugf("L2C_PeakInitData: data not ready, retryCount:%d", m.requestInitRetryCount)
			return
		}

		//巅峰竞技场报名人数不足
		if recvMsg.GetRet() == uint32(cret.RET_CROSS_PEAK_INIT_DATA_NOT_ENOUGH) {
			m.doInitData(baseModule, false, nil)
			return
		}

		if recvMsg.GetRet() != uint32(cret.RET_OK) {
			l4g.Errorf("[FATAL] L2C_PeakInitData: ret err: %d", recvMsg.GetRet())
			return
		}

		nowSeason := m.GetPeak().GetSeason()
		nowPhase := m.GetPeak().GetPhase()
		phaseInfo := goxml.GetData().PeakInfoM.GetPeakPhaseByPhase(nowSeason, nowPhase)
		if phaseInfo == nil {
			l4g.Errorf("[FATAL] L2C_PeakInitData: get phase config error, now season:%d, now phase:%d", nowSeason, nowPhase)
			return
		}
		worldBossID := recvMsg.GetId()
		if worldBossID != phaseInfo.WorldBossID {
			l4g.Errorf("[FATAL] L2C_PeakInitData: data not match, paramID:%d, needID:%d",
				worldBossID, phaseInfo.WorldBossID)
			return
		}

		fighters := recvMsg.GetPlayers()
		if len(fighters) != int(goxml.PeakPlayerCount) {
			l4g.Errorf("[FATAL] L2C_PeakInitData: fighter count err, fighterCount:%d", len(fighters))
			return
		}

		m.doInitData(baseModule, true, fighters)
	} else if msg.ProtoId == uint32(l2c.ID_MSG_Guild2P_SyncBakData) {
		recvMsg := &l2c.Guild2P_SyncBakData{}
		if err := proto.Unmarshal(msg.Data, recvMsg); err != nil {
			l4g.Errorf("[FATAL] L2C_PeakPushDataToGuild: unmarshal err:%v", err)
			return
		}
		l4g.Debugf("peakM.TransformMsg: partition:%d get recvMsg:%+v", m.partID, recvMsg)

		if recvMsg.GetRet() == uint32(cret.RET_OK) {
			m.GetPeak().setPushGuild(true)
			return
		} else {
			l4g.Debugf("peakM.L2C_PeakPushDataToGuild: partition:%d get recvMsg error:%d", m.partID, recvMsg.GetRet())
		}

	}
}
