package peak

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

type MatchManager struct {
	mgr     *Manager
	matches []*Match //id从1开始.0号位是空的

	userMatches  map[uint64][]*Match //玩家uid对应的比赛
	groupMatches map[uint32][]*Match //小组id对应的比赛
	guessMatches map[uint32][]*Match //round=>竞猜的比赛

	changes map[uint32]*cr.PeakMatch

	matchIndex uint32
}

func newMatchManager(manager *Manager) *MatchManager {
	return &MatchManager{
		mgr:          manager,
		matches:      make([]*Match, goxml.PeakPlayerCount),
		userMatches:  make(map[uint64][]*Match, 1),
		groupMatches: make(map[uint32][]*Match, goxml.PeakGroupCount),
		guessMatches: make(map[uint32][]*Match, len(goxml.PeakRounds)),
		changes:      make(map[uint32]*cr.PeakMatch),
	}
}

func (mm *MatchManager) load(matches map[uint32]*cr.PeakMatch) {
	id := uint32(0)
	for _, data := range matches {
		match := loadMatch(data)
		mm.add(match)
		if match.getID() > id {
			id = match.getID()
		}
		if match.getGuessStartTm() > 0 {
			if len(mm.guessMatches[match.GetRound()]) == 0 {
				mm.guessMatches[match.GetRound()] = make([]*Match, 0, goxml.PeakGuessMatchCount)
			}
			mm.guessMatches[match.GetRound()] = append(mm.guessMatches[match.GetRound()], match)
		}
	}
	mm.matchIndex = id
}

func (mm *MatchManager) add(match *Match) {
	id := match.getID()
	mm.matches[id] = match

	for _, uid := range match.GetFighterIDs() {
		if _, exist := mm.userMatches[uid]; !exist {
			mm.userMatches[uid] = make([]*Match, 0, 1)
		}
		mm.userMatches[uid] = append(mm.userMatches[uid], match)
	}

	if _, exist := mm.groupMatches[match.getGroup()]; !exist {
		mm.groupMatches[match.getGroup()] = make([]*Match, 0, goxml.PeakGroupRound1MatchCount)
	}
	mm.groupMatches[match.getGroup()] = append(mm.groupMatches[match.getGroup()], match)
}

func (mm *MatchManager) addMatch(match *Match) {
	l4g.Debugf("Cross peak add new match :%+v", match.data)
	mm.add(match)
	mm.SetChange(match)
}

func (mm *MatchManager) GetMatch(id uint32) *Match {
	return mm.matches[id]
}

func (mm *MatchManager) getAllMatch() []*Match {
	ret := make([]*Match, 0, len(mm.matches))
	for _, v := range mm.matches {
		if v != nil {
			ret = append(ret, v)
		}
	}
	return ret
}

func (mm *MatchManager) getMatchesByRound(round uint32) []*Match {
	ret := make([]*Match, 0, goxml.CalcPeakMatchCount(round))
	for _, match := range mm.getAllMatch() {
		if match.GetRound() == round {
			ret = append(ret, match)
		}
	}
	return ret
}

// 获取当前轮，不同半区的比赛数据
// 两个数据的数量是相同的
// 最后一轮时，上下半区数据相同
func (mm *MatchManager) getAreaMatchesByRound(round uint32) ([]*Match, []*Match) {
	matchCount := goxml.CalcPeakMatchCount(round)
	cap := matchCount >> 1
	if cap == 0 {
		cap = 1
	}
	upperMatches := make([]*Match, 0, cap)
	lowerMatches := make([]*Match, 0, cap)

	for _, match := range mm.getAllMatch() {
		if match.GetRound() == round {
			if match.getArea() == goxml.PeakAreaUpper {
				upperMatches = append(upperMatches, match)
			} else if match.getArea() == goxml.PeakAreaLower {
				lowerMatches = append(lowerMatches, match)
			} else {
				upperMatches = append(upperMatches, match)
				lowerMatches = append(lowerMatches, match)
			}
		}
	}
	return upperMatches, lowerMatches
}

func (mm *MatchManager) seasonClear() {
	mm.matches = make([]*Match, goxml.PeakPlayerCount)
	mm.userMatches = make(map[uint64][]*Match, 1)
	mm.groupMatches = make(map[uint32][]*Match, goxml.PeakGroupCount)
	mm.matchIndex = 0
	mm.initChanges()
}

func (mm *MatchManager) phaseClear() {
	mm.matches = make([]*Match, goxml.PeakPlayerCount)
	mm.userMatches = make(map[uint64][]*Match, 1)
	mm.groupMatches = make(map[uint32][]*Match, goxml.PeakGroupCount)
	mm.matchIndex = 0
	mm.initChanges()
}

func (mm *MatchManager) initChanges() {
	mm.changes = make(map[uint32]*cr.PeakMatch)
}

func (mm *MatchManager) isChange() bool {
	return mm.changeCount() > 0
}

func (mm *MatchManager) changeCount() int {
	return len(mm.changes)
}

func (mm *MatchManager) flushAllChanges() []*cr.PeakMatch {
	matches := make([]*cr.PeakMatch, 0, len(mm.changes))
	for _, v := range mm.changes {
		matches = append(matches, v.Clone())
	}
	return matches
}

func (mm *MatchManager) SetChange(match *Match) {
	mm.changes[match.getID()] = match.getData()
}

// 获取玩家比赛数据
// @param uint64 uid 玩家id
// @param bool onlyFinish 是否只要完赛数据
// @return []*cl.PeakMatch
func (mm *MatchManager) FlushUserMatches(uid uint64, onlyFinish bool) []*cl.PeakMatch {
	ret := make([]*cl.PeakMatch, 0, 1)
	for _, v := range mm.userMatches[uid] {
		if onlyFinish && !v.IsFinished() {
			continue
		}

		ret = append(ret, v.transMatch(mm.mgr.fighterM))
	}
	return ret
}

func (mm *MatchManager) FlushGuessMatches(round uint32) []*cl.PeakMatch {
	ret := make([]*cl.PeakMatch, 0, goxml.PeakGuessMatchCount)
	for _, match := range mm.guessMatches[round] {
		ret = append(ret, match.transMatch(mm.mgr.fighterM))
	}
	return ret
}

func (mm *MatchManager) FlushOneGuessMatch(round, matchID uint32) *cl.PeakMatch {
	for _, match := range mm.guessMatches[round] {
		if matchID == 0 || match.getID() == matchID {
			return match.transMatch(mm.mgr.fighterM)
		}
	}
	return nil
}

func (mm *MatchManager) GetGuessMatchesResult(round uint32) []*cl.PeakUserGuess {
	ret := make([]*cl.PeakUserGuess, 0, goxml.PeakGuessMatchCount)
	for _, match := range mm.guessMatches[round] {
		ret = append(ret, &cl.PeakUserGuess{
			MatchId:   match.getID(),
			Group:     match.getGroup(),
			WinnerUid: match.getWinnerUID(),
		})
	}
	return ret
}

func (mm *MatchManager) updateGuessMatches(matches []*Match, round uint32, tm int64) {
	mm.guessMatches[round] = matches
	for _, match := range matches {
		match.setGuessStartTm(tm)
		mm.SetChange(match)
	}
}

func (mm *MatchManager) checkAllFinished() bool {
	for _, match := range mm.getAllMatch() {
		if !match.IsFinished() {
			return false
		}
	}
	return true
}

func (mm *MatchManager) FlushMatches(params []*cl.PeakMatchParam) []*cl.PeakMatch {
	ret := make([]*cl.PeakMatch, 0, goxml.PeakRound1TotalMatchCount)
	for _, v := range params {
		for _, match := range mm.getMatchesByRound(v.Round) {
			if match.getGroup() == v.Group && match.getArea() == v.Area {
				ret = append(ret, match.transMatch(mm.mgr.fighterM))
			}
		}
	}
	return ret
}

func (mm *MatchManager) newMatch(leftFighter, rightFighter *Fighter, round, area, group, group_sub uint32) {
	id := mm.getMatchIndex()
	newMatch := &Match{
		data: &cr.PeakMatch{
			Id:           id,
			LeftFighter:  leftFighter.getPeakMatchFighter(),
			RightFighter: rightFighter.getPeakMatchFighter(),
			Round:        round,
			Area:         area,
			Group:        group,
			GroupSub:     group_sub,
		},
	}
	mm.addMatch(newMatch)
}

func (mm *MatchManager) getMatchIndex() uint32 {
	mm.matchIndex++
	return mm.matchIndex
}
