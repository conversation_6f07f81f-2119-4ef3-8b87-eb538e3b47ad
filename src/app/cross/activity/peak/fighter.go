package peak

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

type Fighter struct {
	data *cr.PeakFighter
}

func loadFighter(fighter *cr.PeakFighter) *Fighter {
	return &Fighter{
		data: fighter,
	}
}

// 初始化参数选手
// @param *l2c.PeakPlayer data 报名数据
// @param int tm 时间
// @param int phase 小周期id
// @param bool isDoubleScore 是否双倍积分
// @return *Fighter
func newFighter(data *l2c.PeakPlayer, tm int64, phase uint32, isDoubleScore bool) *Fighter {
	f := &Fighter{
		data: &cr.PeakFighter{},
	}
	f.commonReset(data, tm, phase, isDoubleScore)
	return f
}

// 新周期重置选手数据
// @param *l2c.PeakPlayer data 报名数据
// @param int tm 时间
// @param int phase 小周期id
// @param bool isDoubleScore 是否双倍积分
func (f *Fighter) resetFighter(data *l2c.PeakPlayer, tm int64, phase uint32, isDoubleScore bool) {
	f.commonReset(data, tm, phase, isDoubleScore)
}

// 通用重置逻辑
// @param *l2c.PeakPlayer data 报名数据
// @param int tm 时间
// @param int phase 小周期id
// @param bool isDoubleScore 是否双倍积分
func (f *Fighter) commonReset(data *l2c.PeakPlayer, tm int64, phase uint32, isDoubleScore bool) {
	initScore := goxml.GetData().PeakRankRewardInfoM.GetInitScore()
	if isDoubleScore {
		initScore *= 2
	}

	f.data.Uid = data.Uid
	f.data.Sid = data.Sid
	f.data.Pos = data.Rank
	f.data.PhaseScore = initScore
	f.data.SeasonScore += initScore
	f.data.Time = tm
	f.data.Rank = goxml.PeakPlayerCount
	f.data.Phase = phase
	f.data.Round = goxml.PeakRound1
	f.data.Group = getGroupByRank(data.Rank)
	f.data.GroupSub = data.Rank
}

func (f *Fighter) getData() *cr.PeakFighter {
	return f.data
}

func (f *Fighter) getUID() uint64 {
	return f.data.Uid
}

func (f *Fighter) getSID() uint64 {
	return f.data.Sid
}

func (f *Fighter) getPos() uint32 {
	return f.data.Pos
}

func (f *Fighter) getRound() uint32 {
	return f.data.Round
}

func (f *Fighter) getGroup() uint32 {
	return f.data.Group
}

func (f *Fighter) getGroupSub() uint32 {
	return f.data.GroupSub
}

func (f *Fighter) GetPhaseScore() uint32 {
	return f.data.PhaseScore
}

func (f *Fighter) GetSeasonScore() uint32 {
	return f.data.SeasonScore
}

func (f *Fighter) getTime() int64 {
	return f.data.Time
}

func (f *Fighter) getRank() uint32 {
	return f.data.Rank
}

func (f *Fighter) getPhase() uint32 {
	return f.data.Phase
}

func (f *Fighter) updateSnapshot(data *cl.UserSnapshot) {
	f.data.Snapshot = data
}

func (f *Fighter) getSnapshot() *cl.UserSnapshot {
	return f.data.Snapshot
}

func (f *Fighter) flushSnapshot() *cl.UserSnapshot {
	return f.getSnapshot().Clone()
}

func (f *Fighter) GetLastBattleReport() string {
	return f.data.LastReportId
}

func (f *Fighter) GetIsAttacker() bool {
	return f.data.IsAttacker
}

// 更新选手成绩
// @param int64 tm 比赛结算时间
// @param uint32 subGroup 子组
// @param bool isWin 是否战胜
// @param bool isDoubleScore 是否双倍积分
func (f *Fighter) UpdateResult(tm int64, subGroup uint32, isWin, isDoubleScore, isAttacker bool, lastReportId string) {
	f.data.Rank = f.calcRankAfterFight(isWin)
	f.data.Time = tm
	f.data.IsEnd = !isWin
	f.data.LastReportId = lastReportId
	f.data.IsAttacker = isAttacker
	if isWin {
		info := goxml.GetData().PeakRankRewardInfoM.GetRankInfo(goxml.PeakRewardTypePhase, f.data.Rank)
		if info == nil {
			l4g.Errorf("Fighter.UpdateResult: no info, fighter:%+v", f.getRank())
			return
		}
		f.data.Round++
		f.data.GroupSub = subGroup

		//每个巅峰赛季的最后一期，双倍积分
		addScore := info.Score
		if isDoubleScore {
			addScore *= 2
		}

		f.data.PhaseScore += addScore
		f.data.SeasonScore += addScore
	}
}

// 计算战斗后的排名
// @param bool 是否战胜
// @return uint32 新的排名
func (f *Fighter) calcRankAfterFight(isWin bool) uint32 {
	oldRank := f.getRank()
	if !isWin {
		return oldRank
	}

	newRank := oldRank >> 1
	if newRank <= 0 {
		newRank = 1
		l4g.Errorf("Fighter.calcRankAfterFight: illegal, fighter:%+v", f.getData())
	}
	return newRank
}

func (f *Fighter) IsInCurrentPhase(phase uint32) bool {
	if phase == 0 {
		return false
	}
	return f.getPhase() == phase
}

func (f *Fighter) getPeakMatchFighter() *cl.PeakMatchFighter {
	return &cl.PeakMatchFighter{
		Uid:         f.getUID(),
		Sid:         f.getSID(),
		SeasonScore: f.GetSeasonScore(),
		Pos:         f.getPos(),
		Snapshot:    f.flushSnapshot(),
	}
}

func getGroupByRank(rank uint32) uint32 {
	return goxml.PeakRank2Pos[rank-1]
}

func getAreaByGroup(group uint32) uint32 {
	if group <= goxml.PeakGroupCount/2 {
		return goxml.PeakAreas[0]
	} else {
		return goxml.PeakAreas[1]
	}
}
