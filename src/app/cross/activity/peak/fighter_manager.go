package peak

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"sort"

	l4g "github.com/ivanabc/log4go"
)

type FighterManager struct {
	mgr      *Manager
	fighters map[uint64]*Fighter
	changes  map[uint64]*cr.PeakFighter

	phaseRankList  []*Fighter //小周期选手积分排行数据
	seasonRankList []*Fighter //赛季选手积分排行数据
}

func new<PERSON>ighter<PERSON>anager(manager *Manager) *FighterManager {
	return &FighterManager{
		mgr:            manager,
		fighters:       make(map[uint64]*Fighter, goxml.PeakPlayerCount),
		changes:        make(map[uint64]*cr.PeakFighter),
		phaseRankList:  make([]*Fighter, 0, goxml.PeakPlayerCount),
		seasonRankList: make([]*Fighter, 0, goxml.PeakPlayerCount),
	}
}

func (fm *FighterManager) load(fighters map[uint64]*cr.<PERSON><PERSON>ighter, phase uint32) {
	for _, data := range fighters {
		fighter := loadFighter(data)
		fm.add(fighter)
		fm.initPhaseRankList(fighter, phase)
		fm.initSeasonRankList(fighter)
	}
	fm.sortSeasonRankList()
}

func (fm *FighterManager) add(fighter *Fighter) {
	fm.fighters[fighter.getUID()] = fighter
}

// 初始化小周期排行数据
func (fm *FighterManager) initPhaseRankList(fighter *Fighter, phase uint32) {
	if fighter.IsInCurrentPhase(phase) {
		fm.phaseRankList = append(fm.phaseRankList, fighter)
	}
}

// 初始化赛季排行数据
func (fm *FighterManager) initSeasonRankList(fighter *Fighter) {
	fm.seasonRankList = append(fm.seasonRankList, fighter)
}

func (fm *FighterManager) GetFighter(uid uint64) *Fighter {
	return fm.fighters[uid]
}

func (fm *FighterManager) seasonClear() {
	fm.fighters = make(map[uint64]*Fighter, goxml.PeakPlayerCount)
	fm.seasonRankList = fm.seasonRankList[:0]
	fm.phaseRankList = fm.phaseRankList[:0]
	fm.initChanges()
}

func (fm *FighterManager) phaseClear() {
	fm.phaseRankList = fm.phaseRankList[:0]
}

// 添加参数选手
// 均需判断过往是否有数据，需要继承score
// @param *l2c.PeakPlayer player 报名数据
// @param int tm 时间
// @param int phase 小周期id
func (fm *FighterManager) addFighter(player *l2c.PeakPlayer, tm int64, phase uint32) {
	isDoubleScore := fm.mgr.isDoubleScore()

	fighter := fm.GetFighter(player.Uid)
	if fighter != nil {
		fighter.resetFighter(player, tm, phase, isDoubleScore)
	} else {
		fighter = newFighter(player, tm, phase, isDoubleScore)
		fm.add(fighter)
		fm.initSeasonRankList(fighter)
	}
	fm.initPhaseRankList(fighter, phase)
	fm.setChange(fighter)
}

func (fm *FighterManager) initChanges() {
	fm.changes = make(map[uint64]*cr.PeakFighter)
}

func (fm *FighterManager) changeCount() int {
	return len(fm.changes)
}

func (fm *FighterManager) isChange() bool {
	return fm.changeCount() > 0
}

func (fm *FighterManager) flushAllChanges() []*cr.PeakFighter {
	fighters := make([]*cr.PeakFighter, 0, len(fm.changes))
	for _, v := range fm.changes {
		fighters = append(fighters, v.Clone())
	}
	return fighters
}

func (fm *FighterManager) setChange(fighter *Fighter) {
	fm.changes[fighter.getUID()] = fighter.getData()
}

func (fm *FighterManager) FlushSelfRank(uid uint64) *cl.PeakResult {
	fighter := fm.GetFighter(uid)
	if fighter == nil {
		return nil
	}

	var ret *cl.PeakResult
	for i, fighter := range fm.seasonRankList {
		if fighter.getUID() == uid {
			ret = &cl.PeakResult{
				Uid:      fighter.getUID(),
				Sid:      fighter.getSID(),
				Rank:     fighter.getRank(),
				Score:    fighter.GetSeasonScore(),
				RealRank: uint32(i + 1),
			}
			break
		}
	}
	return ret
}

func (fm *FighterManager) GetSeasonRankListCount() uint32 {
	return uint32(len(fm.seasonRankList))
}

func (fm *FighterManager) FlushSeasonRankList(startRank, endRank uint32) []*cl.PeakResult {
	ret := make([]*cl.PeakResult, 0, endRank-startRank+1)
	startKey := startRank - 1
	for i, fighter := range fm.seasonRankList[startKey:endRank] {
		ret = append(ret, &cl.PeakResult{
			Uid:      fighter.getUID(),
			Sid:      fighter.getSID(),
			Rank:     fighter.getRank(),
			Score:    fighter.GetSeasonScore(),
			RealRank: startKey + uint32(i+1),
		})
	}
	return ret
}

func (fm *FighterManager) sortSeasonRankList() {
	sort.Slice(fm.seasonRankList, func(i, j int) bool {
		if fm.seasonRankList[i].GetSeasonScore() != fm.seasonRankList[j].GetSeasonScore() {
			return fm.seasonRankList[i].GetSeasonScore() > fm.seasonRankList[j].GetSeasonScore()
		}
		if fm.seasonRankList[i].getTime() != fm.seasonRankList[j].getTime() {
			return fm.seasonRankList[i].getTime() < fm.seasonRankList[j].getTime()
		}
		return fm.seasonRankList[i].getPos() < fm.seasonRankList[j].getPos()
	})
}

// 构造当前游戏服玩家的赛季排行数据
// @param sid 服务器id
// @return []*cl.PeakResult
func (fm *FighterManager) MakeSeasonRankListBySid(sid uint64) []*cl.PeakResult {
	fm.sortSeasonRankList()
	ret := make([]*cl.PeakResult, 0, 1)
	for i, fighter := range fm.seasonRankList {
		if fighter.getSID() != sid {
			continue
		}

		ret = append(ret, &cl.PeakResult{
			Uid:      fighter.getUID(),
			Sid:      fighter.getSID(),
			Rank:     fighter.getRank(),
			Score:    fighter.GetSeasonScore(),
			RealRank: uint32(i + 1),
		})
	}
	return ret
}

// 获取赛季排行榜的第一名数据
// @return *cl.PeakResult
func (fm *FighterManager) GetSeasonTop1() *cl.PeakResult {
	fm.sortSeasonRankList()
	if len(fm.seasonRankList) == 0 {
		l4g.Errorf("FighterM.getSeasonTop1: no fighter")
		return nil
	}

	fighter := fm.seasonRankList[0]
	return &cl.PeakResult{
		Uid:      fighter.getUID(),
		Sid:      fighter.getSID(),
		Rank:     fighter.getRank(),
		Score:    fighter.GetSeasonScore(),
		RealRank: 1,
	}
}

func (fm *FighterManager) GetPhaseRankList() []*Fighter {
	return fm.phaseRankList
}

func (fm *FighterManager) sortPhaseRankList() {
	sort.Slice(fm.phaseRankList, func(i, j int) bool {
		if fm.phaseRankList[i].GetPhaseScore() != fm.phaseRankList[j].GetPhaseScore() {
			return fm.phaseRankList[i].GetPhaseScore() > fm.phaseRankList[j].GetPhaseScore()
		}
		if fm.phaseRankList[i].getTime() != fm.phaseRankList[j].getTime() {
			return fm.phaseRankList[i].getTime() < fm.phaseRankList[j].getTime()
		}
		return fm.phaseRankList[i].getPos() < fm.phaseRankList[j].getPos()
	})
}

// 生产当前小周期top8数据
// @param uint32 当前小周期数
// @return []*cl.PeakResult
func (fm *FighterManager) makePhaseTop8(phase uint32) []*cl.PeakResult {
	fm.sortPhaseRankList()
	fighters := fm.GetPhaseRankList()
	if len(fighters) < int(goxml.PeakTop8Count) {
		l4g.Errorf("[FATAL] FighterM.getTop8: fighter not enough, count:%d", len(fighters))
		return nil
	}

	top8 := make([]*cl.PeakResult, 0, goxml.PeakTop8Count)
	for _, fighter := range fighters[:goxml.PeakTop8Count] {
		top8 = append(top8, &cl.PeakResult{
			Uid:   fighter.getUID(),
			Sid:   fighter.getSID(),
			Rank:  fighter.getRank(),
			Score: fighter.GetPhaseScore(),
			Phase: phase,
		})
	}
	return top8
}

func (fm *FighterManager) makePhaseTop128ForGuild(phase uint32) []*cl.PeakResult {
	fm.sortPhaseRankList()
	fm.sortSeasonRankList()
	fighters := fm.GetPhaseRankList()
	if len(fighters) < int(goxml.PeakPlayerCount) {
		l4g.Errorf("[FATAL] FighterM.getTop128: fighter not enough, count:%d", len(fighters))
		return nil
	}

	seasonRank := make(map[uint64]int, 256) //nolint:mnd
	for k, v := range fm.seasonRankList {
		seasonRank[v.data.Uid] = k + 1
	}

	top128 := make([]*cl.PeakResult, 0, goxml.PeakPlayerCount)
	for _, fighter := range fighters[:goxml.PeakPlayerCount] {
		srank := seasonRank[fighter.getUID()]
		top128 = append(top128, &cl.PeakResult{
			Uid:      fighter.getUID(),
			Sid:      fighter.getSID(),
			Rank:     fighter.getRank(),
			Score:    fighter.GetPhaseScore(),
			Phase:    phase,
			RealRank: uint32(srank),
		})
	}
	return top128
}

// 获取小周期第一名数据
// @return *cl.PeakResult
func (fm *FighterManager) GetPhaseTop1() *cl.PeakResult {
	fm.sortPhaseRankList()
	fighters := fm.GetPhaseRankList()
	if len(fighters) == 0 {
		l4g.Errorf("FighterM.getPhaseTop1: no fighter")
		return nil
	}

	fighter := fighters[0]
	return &cl.PeakResult{
		Uid:   fighter.getUID(),
		Sid:   fighter.getSID(),
		Rank:  fighter.getRank(),
		Score: fighter.GetPhaseScore(),
	}
}

func (fm *FighterManager) FlushSeasonFighterRankScore() map[uint64]*cl.PeakRankScore {
	ret := make(map[uint64]*cl.PeakRankScore, len(fm.fighters))
	for i, fighter := range fm.seasonRankList {
		ret[fighter.getUID()] = &cl.PeakRankScore{
			Uid:   fighter.getUID(),
			Rank:  uint32(i + 1),
			Score: fighter.GetSeasonScore(),
		}
	}
	return ret
}

// 根据sid获取当前小周期玩家id列表
func (fm *FighterManager) GetPhasePlayerUidsBySid(sid uint64) []uint64 {
	ret := make([]uint64, 0, len(fm.fighters))
	for _, fighter := range fm.phaseRankList {
		if fighter.getSID() == sid {
			ret = append(ret, fighter.getUID())
		}
	}
	return ret
}

// 获取当前周期的某一轮的所有玩家, 按顺序从小到达
func (fm *FighterManager) GetNextMatchPlayer(phase, round, group uint32) []*Fighter {
	result := make([]*Fighter, 0, goxml.PeakPlayerCount)
	for _, fighter := range fm.fighters {
		if fighter.getPhase() == phase && fighter.getRound() == round && fighter.getGroup() == group {
			result = append(result, fighter)
		}
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].getGroupSub() < result[j].getGroupSub()
	})

	return result
}

// 获取当前周期的某一轮的所有玩家, 按顺序从小到达
func (fm *FighterManager) GetNextMatchPlayerByArea(phase, round, area uint32) []*Fighter {
	result := make([]*Fighter, 0, goxml.PeakPlayerCount)
	for _, fighter := range fm.fighters {
		a := getAreaByGroup(fighter.getGroup())
		if fighter.getPhase() == phase && fighter.getRound() == round && a == area {
			result = append(result, fighter)
		}
	}

	//按照group排序
	if round == goxml.PeakRound4 {
		//决赛第一轮是按照分组来的,后面都是比赛顺序来的
		sort.Slice(result, func(i, j int) bool {
			return result[i].getGroup() < result[j].getGroup()
		})
	} else {
		sort.Slice(result, func(i, j int) bool {
			return result[i].getGroupSub() < result[j].getGroupSub()
		})
	}

	return result
}

func (fm *FighterManager) GetNextMatchPlayerByRound(phase, round uint32) []*Fighter {
	result := make([]*Fighter, 0, goxml.PeakPlayerCount)
	for _, fighter := range fm.fighters {
		if fighter.getPhase() == phase && fighter.getRound() == round {
			result = append(result, fighter)
		}
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].getGroup() < result[j].getGroup()
	})

	return result
}

func (fm *FighterManager) getSnapshot(uid uint64) *cl.UserSnapshot {
	fighter := fm.GetFighter(uid)
	if fighter == nil {
		return nil
	}
	return fighter.getSnapshot()
}

// 更新选手快照数据
func (fm *FighterManager) UpdateSnapshot(fighter *Fighter, snapshot *cl.UserSnapshot) {
	fighter.updateSnapshot(snapshot)
	fm.setChange(fighter)
}

func (fm *FighterManager) GetFighterSeasonRank(id uint64) uint32 {
	for index, fighter := range fm.seasonRankList {
		if fighter == nil {
			continue
		}
		if fighter.getUID() == id {
			return uint32(index + 1)
		}
	}
	return 0
}
