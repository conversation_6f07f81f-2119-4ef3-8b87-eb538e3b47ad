package peak

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/out/cl"
)

type Peak struct {
	data   *cr.Peak
	change bool
}

func newPeak() *Peak {
	return &Peak{}
}

func (p *Peak) load(data *cr.Peak) {
	p.data = data

	if p.data == nil {
		p.data = &cr.Peak{}
	}
	if p.data.State == nil {
		p.data.State = &cr.PeakState{State: &cl.PeakState{}}
	}

	if p.data.Top8 == nil {
		p.data.Top8 = &cr.PeakResults{
			Datas: make([]*cl.PeakResult, 0, goxml.PeakTop8Count),
		}
	}

	if p.data.Top128 == nil {
		p.data.Top128 = &cr.PeakResults{
			Datas: make([]*cl.PeakResult, 0, 1),
		}
	}
}

func (p *Peak) GetState() *cl.PeakState {
	return p.data.State.State
}

func (p *Peak) addOpenTimes() {
	p.data.OpenTimes += 1
}

func (p *Peak) seasonClear() {
	p.data.Top8.Datas = p.data.Top8.Datas[:0]
	//p.data.Top128.Datas = p.data.Top128.Datas[:0] 这里的数据暂时也不清除,只有新的时候才清除
}

func (p *Peak) FlushTop8() []*cl.PeakResult {
	ret := make([]*cl.PeakResult, 0, len(p.data.Top8.Datas))
	for _, data := range p.data.Top8.Datas {
		ret = append(ret, data.Clone())
	}
	return ret
}

func (p *Peak) updateTop8(top8 []*cl.PeakResult) {
	p.data.Top8.Datas = append(p.data.Top8.Datas, top8...)
}

func (p *Peak) updateTop128ForGuild(top128 []*cl.PeakResult) {
	if len(top128) == 0 {
		p.data.Top128.Datas = p.data.Top128.Datas[:0] //防止传递nil无法保存数据
	} else {
		p.data.Top128.Datas = top128
	}
	p.setPushGuild(false)
}

func (p *Peak) GetTop128() []*cl.PeakResult {
	return p.data.Top128.Datas
}

func (p *Peak) setPushGuild(s bool) {
	if s {
		p.data.PushGuild = 1
	} else {
		p.data.PushGuild = 0
	}
	p.setChange()
}

func (p *Peak) GetPushGuild() bool {
	return p.data.PushGuild == 1
}

func (p *Peak) Flush() *cr.Peak {
	return p.data.Clone()
}

func (p *Peak) initChange() {
	p.change = false
}

func (p *Peak) isChange() bool {
	return p.change
}

func (p *Peak) setChange() {
	p.change = true
}

func (p *Peak) FlushState() *cl.PeakState {
	return p.data.State.State.Clone()
}
