package guild

import (
	"app/cross/activity"
	crank "app/cross/activity/rank"
	"app/cross/dclog"
	"app/goxml"
	"app/logic/helper"
	"app/protos/in/cr"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/r2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	cret "app/protos/out/ret"
	"sync/atomic"

	"github.com/gogo/protobuf/proto"

	"gitlab.qdream.com/kit/sea/math/rand"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

const (
	stateInit int32 = iota
	stateFinishLoad
	stateRunning
)

type Manager struct {
	state  int32
	actID  uint32
	partID uint32

	baseModule activity.BaseModuler

	guilds      map[uint64]*Guild
	userToGuild map[uint64]*Guild

	nameToGuild map[string]*Guild

	logicGuilds map[uint64]map[uint64]*db.LogicGuild // key：服务器ID value：(key: 公会ID value：logic的公会数据)

	recommendS *recommendList

	screen *Screen

	divisionRank *DivisionRank

	activityRank *ActivityRank

	yesterdayNumRank *YesterdayNumRank

	dungeonM *DungeonM

	sidAndGidM *SidAndGidManager

	rd *rand.Rand

	changes map[uint64]*cr.Guild
	deletes map[uint64]struct{}

	incr         int64
	freq         int64 // 保存频率
	lastSaveNano int64 //上次存储时间戳

	logM       *activity.LogM // 公会日志
	donateLogM *activity.LogM // 公会捐赠日志

	resourceLogCollect *ResourceLogCollect

	peakBakM        *PeakBakManager
	worldBossBakM   *WorldBossBakManager
	seasonArenaBakM *SeasonArenaBakManager
	userM           *UserManager // 公会跨服保存的玩家信息(注意:这些user有可能在公会也有可能不在公会)

	transferM    *transferListManager
	combineListM *combineListManager

	autoLeaderTransferM *autoLeaderTransferManager
}

func NewManager() *Manager {
	newManager := &Manager{
		guilds:      make(map[uint64]*Guild),
		nameToGuild: make(map[string]*Guild),
		userToGuild: make(map[uint64]*Guild),
		recommendS:  newRecommendList(),
		screen:      NewScreen(),
		logicGuilds: make(map[uint64]map[uint64]*db.LogicGuild),
		freq:        120, //nolint:mnd
		logM:        activity.NewLogM(activity.GuildLogLimit),
		donateLogM:  activity.NewLogM(activity.GuildDonateLogLimit),

		sidAndGidM: newSidAndGidManager(),

		rd: rand.New(time.Now().UnixNano()),

		changes: make(map[uint64]*cr.Guild),
		deletes: make(map[uint64]struct{}),

		divisionRank:     newDivisionRank(&DivisionRankValueCmp{}),
		activityRank:     newActivityRank(&ActivityRankValueCmp{}),
		yesterdayNumRank: newYesterdayNumRank(&YesterdayNumRankValueCmp{}),
	}
	newManager.dungeonM = newDungeonManager(newManager)
	newManager.peakBakM = newPeakBakManager()
	newManager.worldBossBakM = newWorldBossBakManager()
	newManager.userM = newUserManager()
	newManager.seasonArenaBakM = newSeasonArenaBakManager()
	newManager.transferM = newTransferList()
	newManager.combineListM = newCombineListManager()
	return newManager
}

func (m *Manager) Init(baseModule activity.BaseModuler) bool {
	m.baseModule = baseModule

	//加载数据
	m.actID = baseModule.GetActivityID()
	m.partID = baseModule.Partition()

	m.dungeonM.setPartID(m.partID)

	l4g.Infof("Manager.Init: start load data. actID:%d, partID:%d, sids:%v",
		m.actID, m.partID, baseModule.GetServerIDList())

	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GuildLoad, &r2c.C2R_GuildLoad{
		Sids:    baseModule.GetServerIDList(),
		ArenaId: baseModule.Partition(),
	})

	m.peakBakM.init(m)
	m.worldBossBakM.init(m)
	m.userM.init(m)
	m.seasonArenaBakM.init(m)
	m.resourceLogCollect = newResourceLogCollect(goxml.GetData().ServerInfoM.ResourceLogTime)
	m.autoLeaderTransferM = newAutoLeaderTransferManager(m)
	return true
}

func (m *Manager) Close(baseModule activity.BaseModuler) {
	m.save(baseModule)
	m.dungeonM.save(baseModule)
	m.userM.save(baseModule)
}

func (m *Manager) DeletePartition(baseModule activity.BaseModuler) {
}

func (m *Manager) Update(baseModule activity.BaseModuler, now int64) {
	if m.GetState() < stateFinishLoad {
		return
	}

	m.incr++
	if m.incr%m.freq == 0 {
		m.save(baseModule)
		m.dungeonM.save(baseModule)
		m.userM.save(baseModule)
	} else {
		m.checkSave(baseModule)
		m.dungeonM.checkSave(baseModule)
	}

	m.dungeonM.checkReset(baseModule, m, now)

	// 每天22：00检查自动转让会长
	//if now == int64(util.DailyZeroByTime(now))+79200 {
	//   m.autoTransferLeader(baseModule, now)
	// }

	m.autoLeaderTransferM.checkHandle(now)

	m.resourceLogCollect.checkLogHandle(baseModule, now, m)

	//if now <= goxml.GetGuildDungeonWeeklyCloseTime(now) && !m.dungeonM.dungeonReset.SendMsg {
	//	m.sendNewSeasonMsg(baseModule)
	//}

	// TODO 检查赛季开始时记录公会副本段位

	//确保完成一次心跳update后，才让logic请求进来
	m.SetState(stateRunning)
}

// lastResetTime 上次的重置分区的时间
func (m *Manager) CheckCanResetPart(lastResetTime int64) bool {
	return false //m.mm.canSeasonReset(lastResetTime)
}

func (m *Manager) CheckRunning() bool {
	return m.GetState() == stateRunning
}

func (m *Manager) GetState() int32 {
	return atomic.LoadInt32(&m.state)
}

func (m *Manager) SetState(st int32) {
	atomic.StoreInt32(&m.state, st)
}

// 检查保存
func (m *Manager) checkSave(baseModule activity.BaseModuler) {
	if len(m.changes) >= activity.GuildSaveNum {
		nowNano := time.Now().UnixNano()
		if nowNano-m.lastSaveNano >= activity.GuildSaveInterval {
			l4g.Infof("check save:%d %d", len(m.changes), nowNano-m.lastSaveNano)
			m.save(baseModule)
		}
	}
}

// LoadData
// @Description: 服务启动时加载公会相关数据
// @receiver m
// @param guilds
func (m *Manager) LoadData(data *r2c.R2C_GuildLoad) {

	m.peakBakM.load(data.PeakBak)
	m.worldBossBakM.load(data.WorldBossBak)
	m.seasonArenaBakM.load(data.SeasonArenaBak)
	m.userM.load(data.Users)

	m.recommendS = newRecommendList()

	now := time.Now().Unix()

	m.dungeonM.load(m.partID, data.Dungeons, data.DungeonReset, data.DungeonSeasonId)

	for _, guildData := range data.Guilds {
		newGuild := m.newGuild(guildData)
		if newGuild == nil {
			l4g.Errorf("LoadData: newGuild is nil. gid:%d", guildData.Id)
			continue
		}

		dungeon := m.dungeonM.GetDungeonByID(guildData.GetId())
		if dungeon == nil {
			l4g.Errorf("LoadData: newGuild dungeon is nil. gid:%d", guildData.Id)
			continue
		}

		for _, member := range guildData.Members {
			m.userToGuild[member.Id] = newGuild
			m.logicGuildAddMember(member, newGuild)
		}
		if newGuild.timerResetDaily(int64(util.DailyZeroByTime(now))) {
			dungeon.resetDaily()
			m.dungeonM.SetChange(dungeon)
			m.SetChange(newGuild)
		}
		if util.DaysBetweenTimes(guildData.LastAccessTime, now) <= 7 { //nolint:mnd
			m.recommendS.updateGuild(newGuild)
			m.screen.addGuild(newGuild, dungeon.Division())
		}
		if dungeon.RoomID() != 0 { // 本赛季参加了副本玩法,进入段位榜
			m.divisionRank.Update(dungeon)
		}
		if newGuild.ActivityPoints > 0 { // 有活跃度，进入活跃度榜
			m.activityRank.Update(newGuild)
		}
		m.transferM.updateRank(newGuild)
		m.yesterdayNumRank.Update(newGuild)
		m.combineListM.updateRank(newGuild)
	}

	m.sidAndGidM.load(data.SidAndGids)
	m.SetState(stateFinishLoad)
}

func (m *Manager) LoadOneGuild(data *cr.Guild, dungeonData *cr.GuildDungeon) *Guild {
	now := time.Now().Unix()

	guild := m.newGuild(data)
	dungeon := m.dungeonM.LoadOne(dungeonData)

	// 加载单个公会的时候，检查下重置
	if guild.timerResetDaily(int64(util.DailyZeroByTime(now))) {
		if dungeon != nil {
			dungeon.resetDaily()
			m.dungeonM.SetChange(dungeon)
		}
	}
	division := uint32(0)
	if dungeon != nil {
		division = dungeon.Division()
	}

	for _, member := range data.Members {
		m.userToGuild[member.Id] = guild
		m.logicGuildAddMember(member, guild)
	}

	m.SetLastAccessTm(guild, now)

	m.userToGuild[guild.GetLeader()] = guild
	if guild.ActivityPoints > 0 { // 有活跃度，进入活跃度榜
		m.activityRank.Update(guild)
	}
	if guild.YesterdayNum > 0 {
		m.UpdateYesterdayNum(guild)
	}
	m.yesterdayNumRank.Update(guild)

	m.screen.addGuild(guild, division)
	m.transferM.updateRank(guild)

	m.combineListM.updateRank(guild)

	m.changes[data.Id] = data
	return guild
}

func (m *Manager) logicGuildAddMember(member *cr.GuildMember, guild *Guild) {

	sidLogicGuilds := m.logicGuilds[member.Sid]
	if sidLogicGuilds == nil {
		sidLogicGuilds = make(map[uint64]*db.LogicGuild)
		m.logicGuilds[member.Sid] = sidLogicGuilds
	}
	logicGuild := sidLogicGuilds[guild.GetID()]
	if logicGuild == nil {
		logicGuild = &db.LogicGuild{
			Id:            guild.GetID(),
			Name:          guild.GetName(),
			Level:         guild.GetLevel(),
			HaveApplicant: guild.ApplyListCnt() > 0,
		}
		dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
		if dungeon != nil {
			logicGuild.SeasonTopDivision = dungeon.GetSeasonTopDivision()
		}
		sidLogicGuilds[guild.GetID()] = logicGuild
	}
	logicGuild.Members = append(logicGuild.Members, member.Id)
}

func (m *Manager) logicGuildDelMember(member *cr.GuildMember, guild *Guild) {

	logicGuilds := m.logicGuilds[member.Sid]
	if logicGuilds == nil {
		return
	}
	logicGuild := logicGuilds[guild.GetID()]
	if logicGuild == nil {
		return
	}
	for i := 0; i < len(logicGuild.Members); i++ {
		if logicGuild.Members[i] != member.Id {
			continue
		}
		logicGuild.Members = append(logicGuild.Members[:i], logicGuild.Members[i+1:]...)

		if len(logicGuild.Members) == 0 {
			delete(m.logicGuilds[member.Sid], guild.GetID())
		}
		return
	}
}

func (m *Manager) save(baseModule activity.BaseModuler) {
	success := false
	msg := &r2c.C2R_GuildSave{}
	if len(m.changes) > 0 {
		msg.Changes = make([]*cr.Guild, 0, len(m.changes))
		for _, guild := range m.changes {
			msg.Changes = append(msg.Changes, guild.Clone())
		}
		success = true
		m.changes = make(map[uint64]*cr.Guild)
	}
	deletes := len(m.deletes)
	if deletes > 0 {
		for gid := range m.deletes {
			msg.Deletes = append(msg.Deletes, gid)
		}
		success = true
		m.deletes = make(map[uint64]struct{})
	}

	guildLogChanges, _ := m.logM.Save() // 公会的日志只有公会解散的时候会删，所以删除不做处理
	if len(guildLogChanges) > 0 {
		for gid, logs := range guildLogChanges {
			if msg.GuildChangeLogs == nil {
				msg.GuildChangeLogs = make(map[uint64]*cr.GuildLogs)
			}
			logChangeMsg := &cr.GuildLogs{}
			for _, v := range logs {
				logChangeMsg.Logs = append(logChangeMsg.Logs, v.(*Log).clone())
			}
			msg.GuildChangeLogs[gid] = logChangeMsg
		}
		if len(msg.GuildChangeLogs) > 0 {
			success = true
		}
	}

	guildDonateLog, _ := m.donateLogM.Save()
	if len(guildDonateLog) > 0 {
		for gid, logs := range guildDonateLog {
			if msg.GuildDonateLogs == nil {
				msg.GuildDonateLogs = make(map[uint64]*cr.GuildDonateLogs)
			}
			donateLogChangeMsg := &cr.GuildDonateLogs{}
			for _, v := range logs {
				donateLogChangeMsg.Logs = append(donateLogChangeMsg.Logs, v.(*DonateLog).clone())
			}
			msg.GuildDonateLogs[gid] = donateLogChangeMsg
		}
		if len(msg.GuildDonateLogs) > 0 {
			success = true
		}
	}

	if len(m.sidAndGidM.change) > 0 {
		msg.SidAndGids = make([]*cr.SidAndGidForGuildLoad, 0, len(m.sidAndGidM.change))
		for _, changeData := range m.sidAndGidM.change {
			msg.SidAndGids = append(msg.SidAndGids, changeData.Clone())
		}
		success = true
		m.sidAndGidM.change = make(map[uint64]*cr.SidAndGidForGuildLoad)
	}

	if success {
		m.lastSaveNano = time.Now().UnixNano()
		baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GuildSave, msg)
	}
}

func (m *Manager) GetLogicGuilds(sids []uint64) map[uint64]*db.LogicGuild {
	guildsForLogic := make(map[uint64]*db.LogicGuild)

	for _, sid := range sids {
		for gid, logicGuild := range m.logicGuilds[sid] {
			guildForLogic := guildsForLogic[gid]
			if guildForLogic == nil {
				m.UpdateLogicGuild(logicGuild, gid)
				guildsForLogic[gid] = logicGuild.Clone()
				continue
			}

			guildForLogic.Members = append(guildForLogic.Members, logicGuild.Members...)
		}
	}
	return guildsForLogic
}

// UpdateLogicGuild
// @Description: 更新除成员外的信息
// @receiver m
// @param logicGuild
// @param gid
func (m *Manager) UpdateLogicGuild(logicGuild *db.LogicGuild, gid uint64) {
	if logicGuild == nil {
		return
	}
	guild := m.GetGuildByID(gid)
	if guild == nil {
		return
	}
	logicGuild.Name = guild.GetName()
	logicGuild.Level = guild.GetLevel()
	logicGuild.HaveApplicant = guild.ApplyListCnt() > 0
	dungeon := m.dungeonM.GetDungeonByID(gid)
	if dungeon != nil {
		logicGuild.SeasonTopDivision = dungeon.Division()
	}
	logicGuild.Medal = guild.flushLogicGuildMedal()
	logicGuild.LastCombineApplyTime = guild.GetLastTargetCombineApplyTime()
}

func (m *Manager) newGuild(data *cr.Guild) *Guild {
	newGuild := &Guild{
		data: data,
		m:    m,
	}
	newGuild.init()
	m.guilds[newGuild.GetID()] = newGuild
	m.nameToGuild[newGuild.GetName()] = newGuild
	return newGuild
}

func (m *Manager) GetGuildByID(id uint64) *Guild {
	guild := m.guilds[id]
	if guild == nil {
		return nil
	}
	if guild.timerResetDaily(int64(util.DailyZeroByTime(time.Now().Unix()))) {
		dungeon := m.dungeonM.GetDungeonByID(id)
		if dungeon != nil {
			dungeon.resetDaily()
		}
		m.activityRank.Update(guild)
		m.yesterdayNumRank.Update(guild)
		m.UpdateYesterdayNum(guild)
		m.SetChange(guild)
	}
	return guild
}

func (m *Manager) GetGuildByName(name string) *Guild {
	guild := m.nameToGuild[name]
	if guild == nil {
		return nil
	}
	if guild.timerResetDaily(int64(util.DailyZeroByTime(time.Now().Unix()))) {
		dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
		if dungeon != nil {
			dungeon.resetDaily()
		}
		m.activityRank.Update(guild)
		m.yesterdayNumRank.Update(guild)
		m.UpdateYesterdayNum(guild)
		m.SetChange(guild)
	}
	return guild
}

func (m *Manager) GetGuildByUser(uid uint64) *Guild {
	guild := m.userToGuild[uid]
	if guild == nil {
		return nil
	}
	if guild.timerResetDaily(int64(util.DailyZeroByTime(time.Now().Unix()))) {
		dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
		if dungeon != nil {
			dungeon.resetDaily()
		}
		m.activityRank.Update(guild)
		m.yesterdayNumRank.Update(guild)
		m.UpdateYesterdayNum(guild)
		m.SetChange(guild)
	}
	return guild
}

func (m *Manager) CreateGuild(data *cr.Guild) *Guild {
	guild := m.newGuild(data)

	dungeon := m.dungeonM.NewDungeon(guild)

	for _, member := range guild.data.Members {
		m.userToGuild[member.Id] = guild
		m.logicGuildAddMember(member, guild)
	}

	m.userToGuild[guild.GetLeader()] = guild

	m.recommendS.updateGuild(guild)
	m.screen.addGuild(guild, dungeon.Division())

	m.transferM.updateRank(guild)

	m.combineListM.updateRank(guild)

	m.sidAndGidM.addNewGid(data.Sid, data.Id)

	m.changes[data.Id] = data
	logData := &log.ESGuildInfo{
		GuildId:  guild.GetID(),
		Name:     guild.GetName(),
		ServerId: guild.GetSid(),
	}
	dclog.LogGuildInfo(m.baseModule.GetSrv(), logData)
	return guild
}

func (m *Manager) GetGuildList(msg *l2c.L2C_GuildList) (uint32, []*cl.GuildSnapshot) {
	totalCount := m.recommendS.getTotalCount()
	if msg.Screen { // 筛选
		list := m.screen.getGuildListByScreen(&screenParams{
			Page:      msg.Page,
			Language:  msg.Language,
			JoinType:  msg.JoinType,
			Label:     msg.Label,
			LvMin:     msg.LvMin,
			LvMax:     msg.LvMax,
			MemberMin: msg.MemberMin,
			MemberMax: msg.MemberMax,
			Division:  msg.Division,
		})

		l4g.Debugf("GetGuildList: list:%+v", list) //nolint:asasalint

		snapshotList := make([]*cl.GuildSnapshot, 0, len(list))
		for i := len(list) - 1; i >= 0; i-- {
			lv := list[i].(*ScreenRankValue)
			id := lv.Key()
			guild := m.GetGuildByID(id)
			if guild != nil {
				snapshotList = append(snapshotList, m.GenerateSnapshot(guild))
			}
		}

		return totalCount, snapshotList
	}
	// 不筛选
	return m.recommendS.getList(msg.Page, msg.Language, m)
}

func (m *Manager) GetTransferGuildList(msg *l2c.L2C_GuildList, currentGid uint64) (uint32, []*cl.GuildSnapshot) {
	if msg.Screen {
		return m.transferM.getScreenList(msg, m, currentGid)
	}
	// 不筛选
	return m.transferM.getRecommendList(msg, m, currentGid)
}

func (m *Manager) GetCombineGuildList(msg *l2c.L2C_GuildList, sourceGid uint64) (uint32, []*cl.GuildSnapshot, []*cl.GuildCombineStatus) {
	// 获取发起公会
	sourceGuild := m.GetGuildByID(sourceGid)
	if sourceGuild == nil {
		l4g.Errorf("GetCombineGuildList: source guild is nil, guildId %d", sourceGid)
		return 0, nil, nil
	}
	sourceLevelInfo := goxml.GetData().GuildLevelInfoM.Index(sourceGuild.GetLevel())
	if sourceLevelInfo == nil {
		l4g.Errorf("GetCombineGuildList: guild levelInfo is nil, level %d", sourceGuild.GetLevel())
		return 0, nil, nil
	}

	recommendList := m.combineListM.getRecommendList(msg, m, sourceGuild, sourceLevelInfo)
	if len(recommendList) == 0 {
		l4g.Debugf("GetCombineGuildList: no recommendList. sourceGid %d", sourceGid)
		return 0, nil, nil
	}
	combineStatus := make([]*cl.GuildCombineStatus, 0, len(recommendList))

	// 处理数据
	for _, guildInList := range recommendList {
		// 获取目标公会
		targetGuild := m.GetGuildByID(guildInList.Id)
		if targetGuild == nil {
			l4g.Errorf("GetCombineGuildList: guild not exist. gid %d", guildInList.Id)
			continue
		}
		targetLevelInfo := goxml.GetData().GuildLevelInfoM.Index(targetGuild.GetLevel())
		if targetLevelInfo == nil {
			l4g.Errorf("GetCombineGuildList: guild levelInfo is nil, level %d", targetGuild.GetLevel())
			continue
		}

		status := m.GetTargetCombineStatusForSource(sourceGuild, targetGuild, sourceLevelInfo, targetLevelInfo)
		combineStatus = append(combineStatus, status)
	}
	return uint32(len(recommendList)), recommendList, combineStatus
}

func (m *Manager) UpdateRank(baseModule activity.BaseModuler, g *Guild, rankId uint32) {
	actId := uint32(l2c.ACTIVITYID_RANK)
	partition := baseModule.Partition()
	protoId := uint32(l2c.ID_MSG_L2C_RankUpdate)
	now := time.Now().Unix()
	var (
		data    []byte
		err     error
		resetTm int64
	)
	snapshot := m.GenerateSnapshot(g)
	if rankId == goxml.GuildLevelRankID {
		guildLevel := crank.NewGuildLevel(g.GetID(), g.GetSid(), g.GetLevel(), g.GetExp(), now, snapshot)
		data, err = proto.Marshal(guildLevel)
		resetTm = 0
	} else if rankId == goxml.GuildDungeonChapterRankId {
		if goxml.GuildDungeonResetting(goxml.GetData(), now) { // 结算期，不更新章节排行榜
			l4g.Debugf("guildM.UpdateRank: dungeon is resetting. gid:%d", g.GetID())
			return
		}
		dungeon := m.dungeonM.GetDungeonByID(g.GetID())
		if dungeon == nil {
			l4g.Errorf("guildM.UpdateRank: dungeon is nil. gid:%d", g.GetID())
			return
		}
		guildDungeonChapter := crank.NewGuildDungeon(g.GetID(), g.GetSid(), dungeon.GetChapter(),
			dungeon.GetChapterRate(), dungeon.GetChapterTm(), snapshot)
		data, err = proto.Marshal(guildDungeonChapter)
		resetTm = goxml.GetGuildDungeonWeeklyResetTime(now) - util.WeekSecs
	} else {
		l4g.Errorf("guildM.UpdateRank: rankId error. rankId:%d", rankId)
		return
	}
	if err != nil {
		l4g.Errorf("guildM.UpdateRank: marshal error :%s", err)
		return
	}
	cmsg := &l2c.L2C_RankUpdate{
		RankId:    rankId,
		ResetTime: uint64(resetTm),
		Data:      data,
	}

	sendData, err2 := proto.Marshal(cmsg)
	if err2 != nil {
		l4g.Errorf("guildM.UpdateRank marshal error :%s", err2)
		return
	}
	l4g.Debug("guildM.UpdateRank: SendTransformMsgToNode actId:%d prtition:%d protoId:%d msg:%+v",
		actId, partition, protoId, cmsg)
	baseModule.SendTransformMsgToNode(actId, partition, protoId, sendData)
}

func (m *Manager) DeleteRank(baseModule activity.BaseModuler, g *Guild, rankId uint32) {
	actId := uint32(l2c.ACTIVITYID_RANK)
	partition := baseModule.Partition()
	protoId := uint32(l2c.ID_MSG_L2C_RankDelete)
	now := time.Now().Unix()
	var (
		resetTm int64
	)
	if rankId == goxml.GuildLevelRankID {
		resetTm = 0
	} else if rankId == goxml.GuildDungeonChapterRankId {
		resetTm = goxml.GetGuildDungeonWeeklyResetTime(now) - util.WeekSecs
	} else {
		l4g.Errorf("guildM.UpdateRank: rankId error. rankId:%d", rankId)
		return
	}

	cmsg := &l2c.L2C_RankDelete{
		RankId:    rankId,
		ResetTime: uint64(resetTm),
		DelKey:    g.GetID(),
	}

	sendData, err2 := proto.Marshal(cmsg)
	if err2 != nil {
		l4g.Errorf("guildM.UpdateRank marshal error :%s", err2)
		return
	}
	l4g.Debug("guildM.DeleteRank: SendTransformMsgToNode actId:%d prtition:%d protoId:%d msg:%+v",
		actId, partition, protoId, cmsg)
	baseModule.SendTransformMsgToNode(actId, partition, protoId, sendData)
}

func (m *Manager) GetGuildLogM() *activity.LogM {
	return m.logM
}

func (m *Manager) SetChange(guild *Guild) {
	m.changes[guild.GetID()] = guild.data
}

// 退出成员
func (m *Manager) QuitGuild(baseModule activity.BaseModuler, g *Guild, member *cr.GuildMember) {
	g.MemberQuit(member)
	m.logicGuildDelMember(member, g)
	delete(m.userToGuild, member.Id)

	dungeon := m.GetDungeonM().GetDungeonByID(g.GetID())
	if dungeon != nil {
		m.screen.Update(g, dungeon.Division())
		dungeon.UpdateBossAndBoxName(member)
		m.dungeonM.SetChange(dungeon)
	}

	m.recommendS.updateGuild(g)
	m.transferM.updateRank(g)
	m.combineListM.updateRank(g)
	m.activityRank.Update(g)
	m.yesterdayNumRank.Update(g)
	m.UpdateRank(baseModule, g, goxml.GuildLevelRankID)
	m.UpdateRank(baseModule, g, goxml.GuildDungeonChapterRankId)
	m.SyncGuildToGST(baseModule, g, false)
}

// 批准申请
// return:  []uint64 批准成功，可以加入公会的  []uint64 需要从申请列表中去掉的
func (m *Manager) Approve(ids []uint64, guild *Guild) ([]*cr.GuildApplyInfo, []uint64) {
	var sucUser []*cr.GuildApplyInfo
	delIds := make([]uint64, 0, len(ids))
	for _, id := range ids {
		if _, exist := m.userToGuild[id]; !exist {

			applyInfo := guild.GetApplyInfo(id)

			if applyInfo != nil {
				sucUser = append(sucUser, applyInfo)
			}
		}
		delIds = append(delIds, id)
	}
	return sucUser, delIds
}

// 拒绝申请
func (m *Manager) RefuseApply(ids []uint64, guild *Guild) []*cr.GuildApplyInfo {
	refuseApplyInfos := make([]*cr.GuildApplyInfo, 0, len(ids))
	for _, id := range ids {
		refuseApply := guild.Refuse(id)
		if refuseApply == nil {
			continue
		}
		refuseApplyInfos = append(refuseApplyInfos, refuseApply)
	}
	return refuseApplyInfos
}

// 拒绝公会合并请求，返回拒绝成功的公会会长信息
func (m *Manager) RefuseCombineApply(sourceGids []uint64, targetGuild *Guild) []*cr.GuildMember {
	refusedGuildLeaders := make([]*cr.GuildMember, 0, len(sourceGids))
	for _, sourceGid := range sourceGids {
		sourceGuild := m.GetGuildByID(sourceGid)
		if sourceGuild == nil {
			l4g.Errorf("guildM.RefuseCombineApply: sourceGuild not exist. gid %d", sourceGid)
			continue
		}
		leaderId := sourceGuild.GetLeader()
		if leaderId == 0 {
			l4g.Errorf("guildM.RefuseCombineApply: sourceGuild no leader. gid %d", sourceGid)
			continue
		}
		leader := sourceGuild.GetMember(leaderId)
		if leader == nil {
			l4g.Errorf("guildM.RefuseCombineApply: sourceGuild %d no leader member data . leaderId %d", sourceGid, leaderId)
			continue
		}
		refusedGuildLeaders = append(refusedGuildLeaders, leader)

		// 修改目标公会的申请列表
		targetGuild.DelTargetCombineApply(sourceGid)

		// 修改发起公会的申请列表
		sourceGuild.DelSourceCombineApply(targetGuild.GetID())
		m.SetChange(sourceGuild)
	}
	return refusedGuildLeaders
}

// 同意公会合并请求
func (m *Manager) AgreeWithCombineApply(sourceGuild, targetGuild *Guild) {
	// 修改目标公会的申请列表
	targetGuild.DelTargetCombineApply(sourceGuild.GetID())

	// 修改发起公会的申请列表
	sourceGuild.DelSourceCombineApply(targetGuild.GetID())
	m.SetChange(sourceGuild)
}

// 公会合并请求通用检查
func (m *Manager) CheckCombineApplyValid(sourceGuild, targetGuild *Guild, applyType uint32, isSource bool) uint32 {
	// 无法与自己公会进行合并
	if sourceGuild.GetID() == targetGuild.GetID() {
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	// 1.双方公会都开启公会合并功能 根据目标方的不同修改错误码
	if !sourceGuild.IsCombineOpen() {
		l4g.Errorf("CheckCombineApplyValid. sourceGuild combine not open, gid %d", sourceGuild.GetID())
		return uint32(ret.RET_GUILD_COMBINE_NOT_OPEN)
	}
	if !targetGuild.IsCombineOpen() {
		l4g.Errorf("CheckCombineApplyValid. targetGuild combine not open, gid %d", targetGuild.GetID())
		return uint32(ret.RET_GUILD_COMBINE_NOT_OPEN)
	}

	// 2. 双方公会的人数是否满足要求
	// 3. 公会会长是否加入，还要考虑cd信息
	// 根据发出请求和接收请求状态提供不同的错误码
	targetLevelInfo := goxml.GetData().GuildLevelInfoM.Index(targetGuild.GetLevel())
	if targetLevelInfo == nil {
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}
	sourceLevelInfo := goxml.GetData().GuildLevelInfoM.Index(sourceGuild.GetLevel())
	if sourceLevelInfo == nil {
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	// 需要判断主副公会
	if applyType == uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_REQUEST) {
		//mainGuild : targetGuild viceGuild : sourceGuild
		_, sourceActiveNum, isSourceLeaderActive := sourceGuild.GetActiveInfo(targetGuild, true)
		_, targetActiveNum, _ := targetGuild.GetActiveInfo(sourceGuild, false)
		if sourceActiveNum+targetActiveNum > targetLevelInfo.Member {
			if isSource {
				return uint32(ret.RET_GUILD_COMBINE_REQUEST_MEMBER_LIMIT)
			} else {
				return uint32(ret.RET_GUILD_COMBINE_INVITE_MEMBER_LIMIT)
			}
		}
		if !isSourceLeaderActive {
			if isSource {
				return uint32(ret.RET_GUILD_COMBINE_REQUEST_LEADER_NOT_JOIN)
			} else {
				return uint32(ret.RET_GUILD_COMBINE_INVITE_LEADER_NOT_JOIN)
			}
		}
	} else if applyType == uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_INVITE) {
		//mainGuild : sourceGuild viceGuild : targetGuild
		_, sourceActiveNum, _ := sourceGuild.GetActiveInfo(targetGuild, false)
		_, targetActiveNum, isTargetLeaderActive := targetGuild.GetActiveInfo(sourceGuild, true)
		if sourceActiveNum+targetActiveNum > sourceLevelInfo.Member {
			if isSource {
				return uint32(ret.RET_GUILD_COMBINE_INVITE_MEMBER_LIMIT)
			} else {
				return uint32(ret.RET_GUILD_COMBINE_REQUEST_MEMBER_LIMIT)
			}
		}
		if !isTargetLeaderActive {
			if isSource {
				return uint32(ret.RET_GUILD_COMBINE_INVITE_LEADER_NOT_JOIN)
			} else {
				return uint32(ret.RET_GUILD_COMBINE_REQUEST_LEADER_NOT_JOIN)
			}
		}
	}

	return uint32(ret.RET_OK)
}

// 发起公会获取目标公会的状态（可操作，已操作...）
func (m *Manager) GetTargetCombineStatusForSource(sourceGuild, targetGuild *Guild, sourceLevelInfo, targetLevelInfo *goxml.GuildLevelInfo) *cl.GuildCombineStatus {
	combineStatus := &cl.GuildCombineStatus{
		Gid:           targetGuild.GetID(),
		RequestStatus: uint32(common.GUILD_COMBINE_STATUS_GCS_CAN_OPERATE),
		InviteStatus:  uint32(common.GUILD_COMBINE_STATUS_GCS_CAN_OPERATE),
	}

	// 已操作
	if apply := sourceGuild.GetSourceValidCombineApply(targetGuild.GetID()); apply != nil {
		if apply.Type == uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_REQUEST) {
			combineStatus.RequestStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_HAS_OPERATED)
		} else if apply.Type == uint32(common.GUILD_COMBINE_APPLY_TYPE_GCAT_INVITE) {
			combineStatus.InviteStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_HAS_OPERATED)
		}
		return combineStatus
	}

	// 优先级1：双方公会是否开启合并功能
	if !sourceGuild.IsCombineOpen() || !targetGuild.IsCombineOpen() {
		combineStatus.RequestStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_FUNC_LOCKED) // 不可申请原因：公会合并未开启
		combineStatus.InviteStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_FUNC_LOCKED)  // 不可申请原因：公会合并未开启
		return combineStatus
	}

	// 优先级2：合并人数限制
	// 优先级3：会长是否加入
	// 申请状态
	//mainGuild : targetGuild viceGuild : sourceGuild
	_, sourceActiveNumRequest, isSourceLeaderActiveRequest := sourceGuild.GetActiveInfo(targetGuild, true)
	_, targetActiveNumRequest, _ := targetGuild.GetActiveInfo(sourceGuild, false)
	if sourceActiveNumRequest+targetActiveNumRequest > targetLevelInfo.Member {
		combineStatus.RequestStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_MEMBER_NUM_LIMIT)
	}
	if !isSourceLeaderActiveRequest {
		combineStatus.RequestStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_LEADER_NOT_JOIN)
	}

	// 邀请状态
	//mainGuild : sourceGuild viceGuild : targetGuild
	_, sourceActiveNumInvite, _ := sourceGuild.GetActiveInfo(targetGuild, false)
	_, targetActiveNumInvite, isTargetLeaderActiveInvite := targetGuild.GetActiveInfo(sourceGuild, true)
	if sourceActiveNumInvite+targetActiveNumInvite > sourceLevelInfo.Member { // 不可申请原因：合并人数限制
		combineStatus.InviteStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_MEMBER_NUM_LIMIT)
	} else if !isTargetLeaderActiveInvite { // 不可申请原因：公会会长不可加入；还需要考虑cd问题
		combineStatus.InviteStatus = uint32(common.GUILD_COMBINE_STATUS_GCS_LEADER_NOT_JOIN)
	}

	return combineStatus.Clone()
}

// 加入公会
func (m *Manager) JoinIn(baseModule activity.BaseModuler, users []*cr.GuildApplyInfo, guild *Guild) {
	logsM := m.logM.GetOwnerLogs(guild)

	for _, user := range users {
		m.userToGuild[user.Uid] = guild

		member := &cr.GuildMember{
			Id:    user.Uid,
			Sid:   user.Sid,
			Name:  user.Name,
			Grade: activity.GuildGradeMember,
		}

		guild.addMember(member)
		logsM.AddLog(NewGuildLog(activity.GuildLogJoinIn, user.Name, ""))

		m.logicGuildAddMember(member, guild)
	}
	m.logM.SetChange(logsM)

	dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
	if dungeon != nil {
		m.screen.Update(guild, dungeon.Division())
	}

	m.recommendS.updateGuild(guild)
	m.transferM.updateRank(guild)
	m.combineListM.updateRank(guild)
	m.UpdateRank(baseModule, guild, goxml.GuildLevelRankID)
	m.UpdateRank(baseModule, guild, goxml.GuildDungeonChapterRankId)
	m.SyncGuildToGST(baseModule, guild, false)
}

func (m *Manager) QuickJoinIn(baseModule activity.BaseModuler, applyInfo *cr.GuildApplyInfo,
	msg *l2c.L2C_GuildUserApply) (uint32, *Guild) {
	var guild *Guild

	now := time.Now().Unix()

	total := m.yesterdayNumRank.sl.Length()
	for i := uint32(1); i <= total; i++ {
		node := m.yesterdayNumRank.sl.GetNodeByRank(i)

		if node == nil {
			l4g.Errorf("QuickJoinIn: activityRank GetNodeByRank is nil. rank:%d", i)
			break
		}

		value, ok := node.Value().(*YesterdayNumRankValue)
		if !ok {
			l4g.Errorf("QuickJoinIn: activityRank node to ActivityRankValue error.")
			break
		}

		gid := value.Key()
		oneGuild := m.GetGuildByID(gid)
		if oneGuild == nil {
			continue
		}
		createDays := util.DaysBetweenTimes(oneGuild.GetCreateTm(), now) + 1
		if createDays > goxml.GetData().GuildConfigInfoM.GetRecNewGuildDay() &&
			value.Score() < uint64(goxml.GetData().GuildConfigInfoM.GetQuickJoinYesterdayNumLimit()) {
			continue
		}
		if oneGuild.GetJoinType() != goxml.GuildJoinOpen { // 不能直接加入
			continue
		}
		if msg.Level < oneGuild.GetLvLimit() || msg.Power < oneGuild.GetPowerLimit() {
			continue
		}

		if goxml.GetData().GuildQuitCdInfoM.IsInQuitCd(msg.LeaveCnt, gid, msg.LeaveTm, now) {
			continue
		}

		levelInfo := goxml.GetData().GuildLevelInfoM.Index(oneGuild.GetLevel())
		if levelInfo == nil {
			continue
		}

		if oneGuild.IsMemberFull() {
			continue
		}

		guild = oneGuild
		break
	}

	if guild == nil {
		return uint32(cret.RET_NO_GUILD_CAN_JOIN), nil
	}

	m.userToGuild[applyInfo.Uid] = guild

	member := guild.newMember(applyInfo.Uid, applyInfo.Sid, applyInfo.Name, activity.GuildGradeMember, msg.LeaveTm, msg.LeaveCnt)
	guild.addMember(member)

	dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
	if dungeon != nil {
		m.screen.Update(guild, dungeon.Division())
	}

	m.logicGuildAddMember(member, guild)

	logsM := m.logM.GetOwnerLogs(guild)
	logsM.AddLog(NewGuildLog(activity.GuildLogJoinIn, applyInfo.Name, ""))
	m.logM.SetChange(logsM)
	m.SetChange(guild)

	m.transferM.updateRank(guild)
	m.combineListM.updateRank(guild)
	m.recommendS.updateGuild(guild)
	m.UpdateRank(baseModule, guild, goxml.GuildLevelRankID)
	m.UpdateRank(baseModule, guild, goxml.GuildDungeonChapterRankId)
	return uint32(cret.RET_OK), guild
}

// 由于合并而加入公会
func (m *Manager) CombineJoinIn(baseModule activity.BaseModuler, users []*cr.GuildMember, guild *Guild) {
	logsM := m.logM.GetOwnerLogs(guild)

	for _, user := range users {
		m.userToGuild[user.Id] = guild
		member := &cr.GuildMember{
			Id:    user.Id,
			Sid:   user.Sid,
			Name:  user.Name,
			Grade: activity.GuildGradeMember,
		}

		guild.addMember(member)
		logsM.AddLog(NewGuildLog(activity.GuildLogJoinIn, user.Name, ""))

		m.logicGuildAddMember(member, guild)
	}
	m.logM.SetChange(logsM)

	dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
	if dungeon != nil {
		m.screen.Update(guild, dungeon.Division())
	}

	m.recommendS.updateGuild(guild)
	m.transferM.updateRank(guild)
	m.combineListM.updateRank(guild)
	m.UpdateRank(baseModule, guild, goxml.GuildLevelRankID)
	m.UpdateRank(baseModule, guild, goxml.GuildDungeonChapterRankId)
	m.SyncGuildToGST(baseModule, guild, false)
}

// 退出公会
/*
func (m *Manager) QuitGuild(baseModule activity.BaseModuler, guildMember *cr.GuildMember, guild *Guild) {
	oldMemberFull := guild.IsMemberFull()

	guild.MemberQuit(guildMember)

	m.logicGuildDelMember(guildMember, guild)

	if oldMemberFull {
		m.recommendS.updateByMemberBeNotFull(guild)
	}

	dungeon := m.GetDungeonM().GetDungeonByID(guild.GetID())
	if dungeon != nil {
		m.screen.Update(guild, dungeon.Division())
		dungeon.UpdateBossAndBoxName(guildMember)
		m.dungeonM.SetChange(dungeon)
	}
	delete(m.userToGuild, guildMember.Id)
	m.UpdateRank(baseModule, guild, goxml.GuildLevelRankID)
	m.UpdateRank(baseModule, guild, goxml.GuildDungeonChapterRankId)
	m.transferM.updateRank(guild)
	m.SyncGuildToGST(baseModule, guild, false)
}
*/

// 解散公会
func (m *Manager) DisbandGuild(baseModule activity.BaseModuler, guildMember *cr.GuildMember, guild *Guild) {
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GuildDeleteName, &r2c.C2R_GuildDeleteName{
		DelName: guild.GetName(),
	})
	m.ActivityRank().Del(guild)
	m.YesterdayRank().Del(guild)
	m.DeleteRank(baseModule, guild, goxml.GuildDungeonChapterRankId)
	m.DeleteRank(baseModule, guild, goxml.GuildLevelRankID)

	m.recommendS.removeGuild(guild)
	m.transferM.removeGuild(guild)
	m.combineListM.removeGuild(guild)

	// 解散公会需要处理合并请求
	m.CancelAllSourceCombineApply(guild)
	m.CancelAllTargetCombineApply(guild)

	dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
	if dungeon != nil {
		m.DivisionRank().Del(dungeon)
		m.screen.del(guild, dungeon.Division())
		m.divisionRank.Del(dungeon)
		if !dungeon.IsSignUp() { // 没有报名
			delete(m.dungeonM.dungeons, guild.GetID())
			m.dungeonM.deletes[guild.GetID()] = struct{}{}
			m.sidAndGidM.delGidForDungeon(guild.GetID())
		}
	}

	logicGuilds := m.logicGuilds[guildMember.Sid]
	if logicGuilds != nil {
		delete(logicGuilds, guild.GetID())
	}

	m.SyncGuildToGST(baseModule, guild, true)

	delete(m.userToGuild, guildMember.Id)
	delete(m.guilds, guild.GetID())
	delete(m.nameToGuild, guild.GetName())
	m.sidAndGidM.delGid(guild.GetSid(), guild.GetID())
	m.deletes[guild.GetID()] = struct{}{}
}

// 增加公会经验
func (m *Manager) AddExp(baseModule activity.BaseModuler, guild *Guild, add uint32) {
	levelUp := guild.addExp(add)

	if levelUp {
		dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
		if dungeon != nil {
			m.screen.Update(guild, dungeon.Division())
		}

		m.recommendS.updateGuild(guild)
	}

	m.transferM.updateRank(guild)
	m.combineListM.updateRank(guild)
	//更新公会等级排行榜
	m.UpdateRank(baseModule, guild, goxml.GuildLevelRankID)
}

func (m *Manager) GetGuildDonateLogM() *activity.LogM {
	return m.donateLogM
}

func (m *Manager) GetApplyList(guild *Guild) []*cr.GuildApplyInfo {
	applyInfos := make([]*cr.GuildApplyInfo, 0, len(guild.data.ApplyInfos))
	change := false
	deleteIds := make([]uint64, 0, goxml.GetData().GuildConfigInfoM.ApplyingReceiveMax)
	for _, info := range guild.data.ApplyInfos {
		if _, exist := m.userToGuild[info.Uid]; exist {
			deleteIds = append(deleteIds, info.Uid)
			change = true
			continue
		}
		applyInfos = append(applyInfos, info)
	}

	for _, id := range deleteIds {
		guild.DeleteApplyID(id)
	}

	if change {
		m.SetChange(guild)
	}
	return applyInfos
}

func (m *Manager) ModifyInfo(msg *l2c.L2C_GuildModifyInfo, guild *Guild) {
	languageChange := msg.Language != guild.GetLanguage()

	// 因为推荐列表和筛选列表都是根据语言做区分的，所以先用改变前的语言删掉
	if languageChange {
		m.recommendS.removeGuild(guild)
		m.transferM.removeGuild(guild)
		m.combineListM.removeGuild(guild)
	}

	guild.ModifyInfo(msg)

	division := uint32(0)
	dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
	if dungeon != nil {
		division = dungeon.Division()
	}

	if languageChange { // 使用改变后的语言插入
		m.recommendS.updateGuild(guild)
	}

	m.transferM.updateRank(guild)
	m.combineListM.updateRank(guild)
	m.screen.Update(guild, division)
	m.SetChange(guild)
}

func (m *Manager) GetDungeonM() *DungeonM {
	return m.dungeonM
}

func (m *Manager) SetGidJoinDungeon(sid, id uint64) {
	m.sidAndGidM.setGidJoinDungeon(sid, id)
}

func (m *Manager) SetLastAccessTm(guild *Guild, tm int64) {
	guild.setLastAccessTm(tm)
	m.sidAndGidM.setLastAccessTm(guild.GetSid(), guild.GetID(), tm)
}

func (m *Manager) GetGidsBySids(sids []uint64) []uint64 {
	return m.sidAndGidM.getGidsBySids(sids)
}

func (m *Manager) GetRand() *rand.Rand {
	return m.rd
}

func (m *Manager) GetSidLogicGuild(g *Guild, sids []uint64) *db.LogicGuild {

	var logicGuild *db.LogicGuild

	for _, sid := range sids {
		logicGuilds := m.logicGuilds[sid]
		if logicGuilds == nil {
			continue
		}

		if logicGuilds[g.GetID()] == nil {
			continue
		}

		if logicGuild == nil {
			logicGuild = logicGuilds[g.GetID()]
			m.UpdateLogicGuild(logicGuild, g.GetID())
			logicGuild = logicGuild.Clone()
		} else {
			logicGuild.Members = append(logicGuild.Members, logicGuilds[g.GetID()].Members...)
		}
	}

	return logicGuild
}

/*func (m *Manager) autoTransferLeader(baseModule activity.BaseModuler, tm int64) {

	for _, guild := range m.guilds {
		if guild.data.LeaderLoginTm+goxml.GetData().GuildConfigInfoM.PresidentCheckActiveDays >
			time.Now().Unix() {
			continue
		}
		if guild.CandidateLeader == nil || guild.GetLeader() == guild.CandidateLeader.ID {
			continue
		}
		if guild.CandidateLeader.LoginTm+goxml.GetData().GuildConfigInfoM.MemberCheckActiveDays < time.Now().Unix() {
			continue
		}
		oldID, newID := guild.GetLeader(), guild.CandidateLeader.ID
		newLeader := guild.GetMember(newID)
		oldLeader := guild.GetMember(oldID)
		if newLeader == nil || oldLeader == nil {
			l4g.Debugf("guildMember not exist oldLeaderId:%d newLeaderId:%d", oldID, newID)
			continue
		}
		guild.Transfer(m, oldLeader, newLeader)

		logsM := m.logM.GetOwnerLogs(guild)
		guild.CandidateLeader = nil // 候选人置空，等下一位候选人
		logsM.AddLog(NewGuildLog(activity.GuildLogAutoTransfer, oldLeader.Name, newLeader.Name))
		baseModule.SendCmdToLogic(oldLeader.Sid, 0, l2c.ID_MSG_C2L_GuildMail, &l2c.C2L_GuildMail{
			Ret:    uint32(cret.RET_OK),
			MailId: activity.MailIDGuildLeaderAutoTransferForOldLeader,
			Params: []string{newLeader.Name},
			Awards: nil,
			Users:  []uint64{oldLeader.Id},
		})
		baseModule.SendCmdToLogic(oldLeader.Sid, 0, l2c.ID_MSG_C2L_GuildNotify, &l2c.C2L_GuildNotify{
			Ret:      uint32(cret.RET_OK),
			Id:       guild.GetID(),
			Recall:   true,
			Uids:     []uint64{oldLeader.Id},
			CreateTm: guild.GetCreateTm(),
		})
		baseModule.SendCmdToLogic(newLeader.Sid, 0, l2c.ID_MSG_C2L_GuildMail, &l2c.C2L_GuildMail{
			Ret:    uint32(cret.RET_OK),
			MailId: activity.MailIDGuildLeaderAutoTransferForNewLeader,
			Params: nil,
			Awards: nil,
			Users:  []uint64{newLeader.Id},
		})
		baseModule.SendCmdToLogic(newLeader.Sid, 0, l2c.ID_MSG_C2L_GuildNotify, &l2c.C2L_GuildNotify{
			Ret:      uint32(cret.RET_OK),
			Id:       guild.GetID(),
			BeLeader: true,
			Uids:     []uint64{newLeader.Id},
			CreateTm: guild.GetCreateTm(),
		})
		for sid, uids := range guild.GetMailMembersWithExcludeId([]uint64{oldLeader.Id, newLeader.Id}) {
			if len(uids) <= 0 {
				continue
			}
			baseModule.SendCmdToLogic(sid, 0, l2c.ID_MSG_C2L_GuildMail, &l2c.C2L_GuildMail{
				Ret:    uint32(cret.RET_OK),
				MailId: activity.MailIDGuildLeaderAutoTransfer,
				Params: []string{oldLeader.Name, newLeader.Name},
				Awards: nil,
				Users:  uids,
			})
		}

		m.logM.SetChange(logsM)
		m.changes[guild.GetID()] = guild.data
	}
}*/

func (m *Manager) FlushClGuild(g *Guild) *cl.GuildInfo {
	if g.data == nil {
		return nil
	}
	dungeon := m.dungeonM.GetDungeonByID(g.GetID())

	clGuild := &cl.GuildInfo{
		Id:              g.data.Id,
		Name:            g.data.Name,
		Badge:           g.data.Badge,
		Level:           g.data.Level,
		Exp:             g.data.Exp,
		Notice:          g.data.Notice,
		MemberCount:     g.MemberCnt(),
		JoinType:        g.data.JoinType,
		UpdateNameTm:    g.data.UpdateNameTm,
		DonatePointNum:  g.data.DonatePointNum,
		DonatePersonNum: uint32(len(g.data.DonatePersonIds)),
		NoticeId:        g.data.NoticeId,
		Language:        g.GetLanguage(),
		Label:           g.GetLabel(),
		LvLimit:         g.GetLvLimit(),
		PowerLimit:      g.GetPowerLimit(),
		ActivityPoint:   g.ActivityPoints,
		ExpireTime:      g.getCurrentBadgeExpireTm(),
		KickCount:       g.GetKickCount(),
		GuildSid:        g.GetSid(),
		CreateTm:        g.GetCreateTm(),
	}
	if dungeon != nil {
		clGuild.DungeonSignUp = dungeon.IsSignUp()
		clGuild.DungeonStar = dungeon.Star()
		clGuild.Division = dungeon.Division()
	}

	clGuild.Medals = g.FlushActiveMedals()

	return clGuild
}

func (m *Manager) GenerateSnapshot(g *Guild) *cl.GuildSnapshot {
	newSnapshot := &cl.GuildSnapshot{
		Id:                g.GetID(),
		Name:              g.GetName(),
		Level:             g.GetLevel(),
		Badge:             g.GetBadge(),
		MemberCount:       g.MemberCnt(),
		Language:          g.data.Language,
		ActivityPoint:     g.ActivityPoints,
		JoinType:          g.data.JoinType,
		Label:             g.data.Label,
		LvLimit:           g.GetLvLimit(),
		PowerLimit:        g.GetPowerLimit(),
		ExpireTime:        g.getCurrentBadgeExpireTm(),
		ActivityMemberCnt: g.ActivityNum,
	}
	dungeon := m.dungeonM.GetDungeonByID(g.GetID())
	if dungeon != nil {
		newSnapshot.DungeonStar = dungeon.Star()
	}
	newSnapshot.Medals = g.FlushActiveMedals()
	return newSnapshot
}

func (m *Manager) DivisionRank() *DivisionRank {
	return m.divisionRank
}

func (m *Manager) ActivityRank() *ActivityRank {
	return m.activityRank
}

func (m *Manager) YesterdayRank() *YesterdayNumRank {
	return m.yesterdayNumRank
}
func (m *Manager) IsGidInDelete(gid uint64) bool {
	_, exist := m.deletes[gid]
	return exist
}

func (m *Manager) SetGuildName(oldName, newName string, g *Guild, now int64) {
	g.SetName(newName, now)
	delete(m.nameToGuild, oldName)
	m.nameToGuild[newName] = g
	m.SetChange(g)
}

// 增加活跃度
func (m *Manager) AddGuildWeeklyActivityPoint(activityPoint uint32, g *Guild) {
	g.addWeeklyActivityPoint(activityPoint)
	m.ActivityRank().Update(g)
	m.transferM.updateRank(g)
	m.combineListM.updateRank(g)
}

//nolint:varnamelen
func (m *Manager) UpdateYesterdayNum(g *Guild) {
	//这里改成依据昨日活跃人数，塞入推荐队列
	now := time.Now().Unix()
	createDays := util.DaysBetweenTimes(g.GetCreateTm(), now) + 1
	if createDays <= goxml.GetData().GuildConfigInfoM.GetRecNewGuildDay() {
		m.recommendS.updateGuild(g)
	} else {
		if g.YesterdayNum >= goxml.GetData().GuildConfigInfoM.GetGuildRecPassYesterdayNum() {
			m.recommendS.updateGuild(g)
		} else {
			//移除原有队列
			m.recommendS.removeGuild(g)
		}
	}
}

func (m *Manager) IsGuildSameArena(baseModule activity.BaseModuler, guildSid uint64) bool {
	for _, sid := range baseModule.GetServerIDList() {
		if sid == guildSid {
			return true
		}
	}
	return false
}

func (m *Manager) GenerateGSTSyncGuild(gid uint64) *l2c.GSTSyncGuild {
	guild := m.GetGuildByID(gid)
	if guild == nil {
		return nil
	}
	sg := &l2c.GSTSyncGuild{
		GuildId:    guild.GetID(),
		GuildBadge: guild.GetBadge(),
		GuildName:  guild.GetName(),
		UserIds:    make([]uint64, 0, guild.MemberCnt()),
		Leaders:    guild.GetLeaders(),
		ExpireTime: guild.getCurrentBadgeExpireTm(),
		GuildSid:   guild.GetSid(),
		Partition:  m.partID,
		Level:      guild.GetLevel(),
	}
	dungeon := m.dungeonM.GetDungeonByID(guild.GetID())
	if dungeon != nil {
		sg.Division = dungeon.Division()
		sg.DivisionScore = dungeon.Star()
	}

	for uid := range guild.GetMembers() {
		sg.UserIds = append(sg.UserIds, uid)
	}

	mobData := guild.getMobilizationData()
	if mobData != nil {
		sg.MobRound = mobData.Round
		sg.MobScore = mobData.Score
		sg.MobTime = mobData.ScoreChangeTime
	}
	return sg
}

// GuildIsExist
// @Description: 判断公会ID对应的公会是否存在，SidAndGidManager.Data中有本战区的所有公会，所以从Data中检查有没有
// @receiver s
// @param gid
// @return bool
func (m *Manager) GuildIsExist(gid uint64) bool {
	for _, sidData := range m.sidAndGidM.data {
		if _, exist := sidData.Gids[gid]; exist {
			return true
		}
	}
	return false
}

func (m *Manager) SyncGuildToGST(baseModule activity.BaseModuler, g *Guild, disband bool) {
	actId := uint32(l2c.ACTIVITYID_GST)
	partition := baseModule.Partition()
	protoId := uint32(l2c.ID_MSG_GUILD2GST_GSTSyncGuilds)
	sg := m.GenerateGSTSyncGuild(g.GetID())
	sg.Dissolve = disband
	cmsg := &l2c.GUILD2GST_GSTSyncGuilds{
		Ret:    uint32(cret.RET_OK),
		Guilds: []*l2c.GSTSyncGuild{sg},
	}
	sendData, err := proto.Marshal(cmsg)
	if err != nil {
		l4g.Errorf("guildM.SyncGuildToGST marshal error :%s", err)
		return
	}
	l4g.Debug("guildM.SyncGuildToGST: SendTransformMsgToNode actId:%d prtition:%d protoId:%d msg:%+v",
		actId, partition, protoId, cmsg)
	baseModule.SendTransformMsgToNode(actId, baseModule.GetSrv().GetBigAreaIDByAreaID(actId, baseModule.Partition()), protoId, sendData)
}

func (m *Manager) GetUserManager() *UserManager {
	return m.userM
}

func (m *Manager) GetSeasonArenaBakManage() *SeasonArenaBakManager {
	return m.seasonArenaBakM
}

func (m *Manager) IsHaveTransferGuild(msg *l2c.L2C_GuildInfo, guild *Guild) bool {
	if guild == nil {
		return false
	}
	if helper.DaysBetweenTimes(time.Now().Unix(), guild.GetCreateTm()) <= goxml.GetData().GuildConfigInfoM.GetTransferOpenCreateDayLimit() {
		return false
	}

	if guild.ActivityNum > goxml.GetData().GuildConfigInfoM.GetTransferOpenMemberLimit() {
		return false
	}
	return m.transferM.isHaveTransferGuild(msg, m, guild.GetID())
}

func (m *Manager) IsHaveCombineGuild(msg *l2c.L2C_GuildInfo, guild *Guild) bool {
	if guild == nil {
		return false
	}
	if !guild.IsCombineOpen() {
		return false
	}

	return m.combineListM.isHaveCombineGuild(msg, m, guild.GetID())
}

// 取消作为发起公会发起的请求
func (m *Manager) CancelAllSourceCombineApply(sourceGuild *Guild) {
	// 删除所有以被发起公会请求的目标公会的请求列表
	for _, apply := range sourceGuild.FlushCombineApplyList(m) {
		if apply == nil || apply.Guild == nil {
			continue
		}
		targetGuild := m.GetGuildByID(apply.Guild.Id)
		if targetGuild == nil {
			continue
		}
		targetGuild.DelTargetCombineApply(apply.SourceGid)
		m.SetChange(targetGuild)
	}

	// 清空发起的所有请求
	sourceGuild.data.SourceCombineApplyInfos = nil
	m.SetChange(sourceGuild)
}

// 取消作为目标公会收到的请求
func (m *Manager) CancelAllTargetCombineApply(targetGuild *Guild) {
	for _, apply := range targetGuild.FlushCombineAppliedList(m) {
		if apply == nil {
			continue
		}
		sourceGuild := m.GetGuildByID(apply.SourceGid)
		if sourceGuild == nil {
			continue
		}
		sourceGuild.DelSourceCombineApply(targetGuild.GetID())
		m.SetChange(sourceGuild)
	}
	// 清空收到的所有请求
	targetGuild.data.TargetCombineApplyInfos = nil
	m.SetChange(targetGuild)
}

// TODO 获取存在转会cd情况下公会a合并到公会b的活跃成员与非活跃成员
func (m *Manager) GetActiveMembersWithCD(sourceGuild, targetGuild *Guild) {

}

func (m *Manager) GenerateGSTSyncGuildMob(g *Guild) *cl.GuildMobilizationGuildRank {
	retData := &cl.GuildMobilizationGuildRank{
		Id:        g.GetID(),
		Name:      g.GetName(),
		Level:     g.GetLevel(),
		Badge:     g.GetBadge(),
		Sid:       g.GetSid(),
		Partition: m.baseModule.Partition(),
	}
	mobData := g.getMobilizationData()
	if mobData != nil {
		retData.Score = mobData.Score
		retData.ScoreChangeTime = mobData.ScoreChangeTime
	}
	return retData
}

func (m *Manager) SyncGuildMobToGST(g *Guild) {
	actId := uint32(l2c.ACTIVITYID_GST)
	partition := m.baseModule.Partition()
	protoId := uint32(l2c.ID_MSG_GUILD2GST_GSTSyncGuildMob)
	cmsg := &l2c.GUILD2GST_GSTSyncGuildMob{
		Data: m.GenerateGSTSyncGuildMob(g),
	}
	sendData, err := proto.Marshal(cmsg)
	if err != nil {
		l4g.Errorf("guildM.SyncGuildMobToGST marshal error :%s", err)
		return
	}
	l4g.Debug("guildM.SyncGuildMobToGST: SendTransformMsgToNode actId:%d prtition:%d protoId:%d msg:%+v",
		actId, partition, protoId, cmsg)
	m.baseModule.SendTransformMsgToNode(actId, m.baseModule.GetSrv().GetBigAreaIDByAreaID(actId, partition),
		protoId, sendData)
}

func (m *Manager) MobSettleBadgeAward(settleAwards map[uint64]uint32) {
	for gid, rewardId := range settleAwards {
		rewardInfo := goxml.GetData().GuildMobilizationRankRewardInfoM.Index(rewardId)
		if rewardInfo == nil {
			l4g.Errorf("guildM.MobSettleBadgeAward: no rewardInfo. rewardId:%d", rewardId)
			continue
		}
		guild := m.GetGuildByID(gid)
		if guild == nil {
			l4g.Errorf("guildM.MobSettleBadgeAward: no guild. gid %d", gid)
			continue
		}
		guild.addBadge(rewardInfo.GuildLogo)
	}
}

func (m *Manager) MobSettleDivisionScoreAward(settleAwards map[uint64]uint32) {
	for gid, rewardId := range settleAwards {
		rewardInfo := goxml.GetData().GuildMobilizationRankRewardInfoM.Index(rewardId)
		if rewardInfo == nil {
			l4g.Errorf("guildM.MobSettleDivisionScoreAward: no rewardInfo. rewardId:%d", rewardId)
			continue
		}
		retCode := m.dungeonM.AddStarByGst(gid, rewardInfo.DivisionScore, m)
		if retCode != uint32(cret.RET_OK) {
			l4g.Errorf("guildM.MobSettleDivisionScoreAward. addStar error. gid:%d addScore:%d retCode:%d",
				gid, rewardInfo.DivisionScore, retCode)
			continue
		}
	}
}
