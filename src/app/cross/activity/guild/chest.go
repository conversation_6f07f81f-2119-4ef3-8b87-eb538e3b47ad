package guild

import (
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

type CrGuildChest cr.GuildChest

func NewGuildChest(info *goxml.GuildChestInfo, name string, level, avatar, createFlower, recvChestMaxLimit, taskId uint32, itemId, uid uint64, now int64) *cr.GuildChest {
	expireTime := now + (int64(info.SendPeriodOfValidity) * util.DaySecs)
	return &cr.GuildChest{
		ChestId:    info.Id,
		Uid:        uid,
		Id:         itemId,
		ExpireTime: expireTime,
		SurplusResource: &cl.Resource{
			Type:  info.Type,
			Value: info.Value,
			Count: info.Count,
		},
		Name:              name,
		Level:             level,
		AvatarId:          avatar,
		CreateFlower:      createFlower,
		RecvChestMaxLimit: recvChestMaxLimit,
		TaskId:            taskId,
	}
}

func (g *CrGuildChest) Convert2Data(uid uint64) *cl.GuildChestData {
	if g == nil {
		return nil
	}
	detail := g.getSlotDetail(uid)
	data := &cl.GuildChestData{
		Id:                 g.Id,
		ExpireTime:         g.ExpireTime,
		LikeCount:          g.RecvLike,
		Name:               g.Name,
		Level:              g.Level,
		AvatarId:           g.AvatarId,
		ChestId:            g.ChestId,
		Uid:                g.Uid,
		ThisChestRecvCount: uint32(len(g.RecvUsers)),
		CreateFlower:       g.CreateFlower,
		RecvChestMaxLimit:  g.RecvChestMaxLimit,
		TaskId:             g.TaskId,
	}
	if detail != nil {
		data.LikeType = detail.LikeLevel
		data.Resource = detail.Resource
	}
	return data
}

func (g *CrGuildChest) getSlotDetail(uid uint64) *cl.GuildChestSlotDetail {
	return g.RecvUsers[uid]
}

func (g *CrGuildChest) Convert2Cl() *cl.GuildChest {
	data := &cl.GuildChest{
		ChestId:           g.ChestId,
		Uid:               g.Uid,
		Id:                g.Id,
		ExpireTime:        g.ExpireTime,
		SurplusResource:   g.SurplusResource.Clone(),
		RecvLike:          g.RecvLike,
		Name:              g.Name,
		Level:             g.Level,
		AvatarId:          g.AvatarId,
		CreateFlower:      g.CreateFlower,
		RecvChestMaxLimit: g.RecvChestMaxLimit,
		TaskId:            g.TaskId,
	}
	data.RecvUsers = make([]*cl.GuildChestSlotDetail, 0, len(g.RecvUsers))
	for _, v := range g.RecvUsers {
		if v == nil {
			continue
		}
		data.RecvUsers = append(data.RecvUsers, v.Clone())
	}
	return data
}

func (g *CrGuildChest) CheckAndSetLike(uid uint64, likeType uint32) uint32 {
	detail, exist := g.RecvUsers[uid]
	if !exist {
		l4g.Errorf("user:%d CrGuildChest CheckAndSetLike must recv before like", uid)
		return uint32(cret.RET_GUILD_CHEST_RECV_BEFORE_LIKE)
	}
	if detail.LikeLevel != uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_NONE) {
		l4g.Errorf("user:%d CrGuildChest CheckAndSetLike repeated like", uid)
		return uint32(cret.RET_GUILD_CHEST_LIKE_REPEATED)
	}
	detail.LikeLevel = likeType
	g.RecvLike += goxml.GetData().GuildConfigInfoM.GetLikeTypeFlowerCount(likeType)
	return uint32(cret.RET_OK)
}

func (g *CrGuildChest) CheckRecv(uid uint64) uint32 {
	if g.SurplusResource.Count <= 0 || uint32(len(g.RecvUsers)) >= g.RecvChestMaxLimit {
		l4g.Errorf("user:%d CrGuildChest CheckRecv guildChest:%+v no surPlusResource or recv count is zero", uid, *g)
		return uint32(cret.RET_GUILD_CHEST_CHEST_RECV_FIN)
	}

	_, exist := g.RecvUsers[uid]
	if exist {
		l4g.Errorf("user:%d CrGuildChest CheckRecv guildChest:%+v recv repeated", uid, *g)
		return uint32(cret.RET_GUILD_CHEST_RECV_REPEATED)
	}
	return uint32(cret.RET_OK)
}

func (g *CrGuildChest) CalcRecvCount(rd *rand.Rand) (*cl.Resource, uint32) {
	//剩一次把最后个给他
	ret := g.SurplusResource.Clone()
	surplusRecvCount := g.RecvChestMaxLimit - uint32(len(g.RecvUsers))
	if surplusRecvCount == 1 {
		return ret, uint32(cret.RET_OK)
	}
	surplusResourceCount := ret.Count
	chestInfo := goxml.GetData().GuildChestInfoM.Index(g.ChestId)
	if chestInfo == nil {
		l4g.Errorf("CrGuildChest CalcRecvCount chestInfo is nil. chestId:%d", g.ChestId)
		return nil, uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	if surplusRecvCount*chestInfo.MinToken > ret.Count {
		l4g.Errorf("CrGuildChest CalcRecvCount surplusRecvCount:%d minToken:%d ret.count:%d", surplusRecvCount, chestInfo.MinToken, ret.Count)
		return nil, uint32(cret.RET_GUILD_CHEST_CHEST_RECV_FIN)
	}
	maxCount := (surplusResourceCount / surplusRecvCount) * goxml.GetData().GuildConfigInfoM.GetGuildChestReceiveMultiple()
	minTokenMaxCount := surplusResourceCount - (chestInfo.MinToken * surplusRecvCount) + chestInfo.MinToken
	if maxCount > minTokenMaxCount {
		maxCount = minTokenMaxCount
	}
	count := uint32(rd.RandBetween(int(chestInfo.MinToken), int(maxCount)))
	if count > surplusResourceCount {
		l4g.Errorf("CrGuildChest CalcRecvCount rand recv count:%d surplusResourceCount:%d", count, surplusResourceCount)
		return nil, uint32(cret.RET_GUILD_CHEST_CHEST_RECV_FIN)
	}
	ret.Count = uint32(count)
	return ret, uint32(cret.RET_OK)
}

func (g *CrGuildChest) SetRecvState(uid uint64, name string, level, avatar uint32, now int64, resource *cl.Resource) {
	if g.RecvUsers == nil {
		g.RecvUsers = make(map[uint64]*cl.GuildChestSlotDetail)
	}
	g.RecvUsers[uid] = &cl.GuildChestSlotDetail{
		Uid:      uid,
		Resource: resource,
		RecvTime: now,
		Name:     name,
		Level:    level,
		AvatarId: avatar,
	}
	// 减法判断
	g.SurplusResource.Count -= resource.Count
}

func (g *CrGuildChest) Convert2Expired() *l2c.GuildChestExpired {
	ret := &l2c.GuildChestExpired{
		Uid:      g.Uid,
		Id:       g.Id,
		ChestId:  g.ChestId,
		RecvLike: g.RecvLike,
	}
	var count int
	var recvCount uint32
	for _, recvUser := range g.RecvUsers {
		if recvUser == nil {
			continue
		}
		if recvUser.LikeLevel > uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_NONE) {
			recvCount++
			if count < 3 { //nolint:mnd
				count++
				ret.RecvNames = append(ret.RecvNames, recvUser.Name)
			}
		}
	}
	ret.RecvCount = recvCount
	return ret
}
