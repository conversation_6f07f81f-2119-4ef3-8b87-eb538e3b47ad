package guild

import (
	"app/cross/activity"
	"app/cross/dclog"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/r2c"
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/math/rand"
)

type DungeonM struct {
	m *Manager

	dungeons map[uint64]*Dungeon

	dungeonLogM *activity.LogM // 副本日志

	rm *RoomManager //房间数据管理
	Rd *rand.Rand

	dungeonReset       *cr.GuildDungeonReset
	dungeonResetChange bool

	changes map[uint64]*cr.GuildDungeon
	deletes map[uint64]struct{}

	lastSaveNano int64 //上次存储时间戳
}

func newDungeonManager(m *Manager) *DungeonM {
	return &DungeonM{
		m:        m,
		dungeons: make(map[uint64]*Dungeon),

		rm:          newRoomManager(),
		Rd:          m.rd,
		dungeonLogM: activity.NewLogM(activity.GuildDungeonLogLimit),

		changes: make(map[uint64]*cr.GuildDungeon),
		deletes: make(map[uint64]struct{}),
	}
}

func (dm *DungeonM) load(areaId uint32, data map[uint64]*cr.GuildDungeon, resetData *cr.GuildDungeonReset, _ uint32) {
	for gid, dungeonData := range data {
		newDungeon := &Dungeon{
			data: dungeonData,
		}
		dm.dungeons[gid] = newDungeon
		dm.rm.load(newDungeon)
	}
	dm.dungeonReset = resetData
	if dm.dungeonReset == nil {
		dm.dungeonReset = &cr.GuildDungeonReset{
			Id:         areaId,
			HallOfFame: make(map[uint32]*cr.GuildDungeonSeasonHallOfFame),
			FirstWeek:  true,
		}
	}
	//dm.realSeasonId = seasonId
	if dm.dungeonReset.LastWeeklyResetTime == 0 {
		dm.initResetTime()
		dm.dungeonResetChange = true
	}
}

func (dm *DungeonM) initResetTime() {
	now := time.Now().Unix()
	dm.dungeonReset.LastWeeklyResetTime = goxml.GetGuildDungeonWeeklyResetTime(now) - util.WeekSecs
	dm.dungeonReset.LastSeasonResetTime = dm.dungeonReset.LastWeeklyResetTime // 最后一轮赛季结算时间和周结算时间是一样的
	//dm.dungeonReset.SeasonId = dm.realSeasonId
	//if dm.dungeonReset.SeasonId == 0 {
	//	dm.dungeonReset.SeasonId = 1
	//	dm.realSeasonId = 1
	//}
}

func (dm *DungeonM) checkSetNoFirstWeek() {
	if dm.dungeonReset.FirstWeek {
		dm.dungeonReset.FirstWeek = false
		dm.dungeonResetChange = true
	}
}

func (dm *DungeonM) changeResetTm(seasonReset bool, resetTm int64) {
	dm.dungeonReset.LastWeeklyResetTime = resetTm
	if seasonReset {
		dm.dungeonReset.LastSeasonResetTime = resetTm
	}
	dm.dungeonResetChange = true
}

func (dm *DungeonM) LoadOne(data *cr.GuildDungeon) *Dungeon {
	if data == nil {
		return nil
	}

	newDungeon := &Dungeon{
		data: data,
	}
	dm.rm.load(newDungeon)
	dm.dungeons[data.Id] = newDungeon
	return newDungeon
}

// 检查保存
func (dm *DungeonM) checkSave(baseModule activity.BaseModuler) {
	if len(dm.changes) >= activity.GuildSaveNum {
		nowNano := time.Now().UnixNano()
		if nowNano-dm.lastSaveNano >= activity.GuildSaveInterval {
			l4g.Infof("guild dungeon manager check save:%d %d", len(dm.changes), nowNano-dm.lastSaveNano)
			dm.save(baseModule)
		}
	}
}

func (dm *DungeonM) save(baseModule activity.BaseModuler) {
	success := false
	msg := &r2c.C2R_GuildDungeonSave{
		ArenaId: baseModule.Partition(),
	}
	if len(dm.changes) > 0 {
		msg.Changes = make([]*cr.GuildDungeon, 0, len(dm.changes))
		for _, dungeon := range dm.changes {
			msg.Changes = append(msg.Changes, dungeon.Clone())
		}
		success = true
		dm.changes = make(map[uint64]*cr.GuildDungeon)
	}
	deletes := len(dm.deletes)
	if deletes > 0 {
		for gid := range dm.deletes {
			msg.Deletes = append(msg.Deletes, gid)
		}
		success = true
		dm.deletes = make(map[uint64]struct{})
	}

	dungeonLogChanges, _ := dm.dungeonLogM.Save() // 副本的日志只有公会解散的时候会删，所以删除不做处理
	if len(dungeonLogChanges) > 0 {
		for gid, logs := range dungeonLogChanges {
			if msg.DungeonChangeLogs == nil {
				msg.DungeonChangeLogs = make(map[uint64]*cr.GuildDungeonLogs)
			}
			logChangeMsg := &cr.GuildDungeonLogs{}
			for _, v := range logs {
				logChangeMsg.Logs = append(logChangeMsg.Logs, v.(*DungeonLog).clone())
			}
			msg.DungeonChangeLogs[gid] = logChangeMsg
		}
		if len(msg.DungeonChangeLogs) > 0 {
			success = true
		}
	}

	if dm.dungeonResetChange {
		success = true
		dm.dungeonResetChange = false
		msg.ResetData = dm.dungeonReset.Clone()
		//msg.DungeonSeasonId = dm.realSeasonId
	}

	if success {
		dm.lastSaveNano = time.Now().UnixNano()
		baseModule.SendCmdToDB(r2c.ID_MSG_C2R_GuildDungeonSave, msg)
	}
}

func (dm *DungeonM) GetDungeonByID(id uint64) *Dungeon {
	return dm.dungeons[id]
}

func (dm *DungeonM) NewDungeon(guild *Guild) *Dungeon {
	dungeon := &Dungeon{
		data: &cr.GuildDungeon{
			Id:      guild.GetID(),
			Name:    guild.GetName(),
			Badge:   guild.GetBadge(),
			Chapter: activity.GuildDungeonInitialChapter,
		},
	}
	// 设置初始段位
	dungeon.UpdateDivision(goxml.GetData().GuildDungeonDivisionInoM.GetInitialDivision())
	dungeon.SetStar(goxml.GetData().GuildDungeonDivisionInoM.GetInitialStar())
	dm.dungeons[dungeon.ID()] = dungeon
	dm.SetChange(dungeon)
	return dungeon
}

func (dm *DungeonM) SignUp(dungeon *Dungeon) {
	if dungeon.Division() == 0 {
		// 设置初始段位
		dungeon.UpdateDivision(goxml.GetData().GuildDungeonDivisionInoM.GetInitialDivision())
		dungeon.SetStar(goxml.GetData().GuildDungeonDivisionInoM.GetInitialStar())
	}
	dm.rm.joinRoom(dungeon)
	dm.SetChange(dungeon)
}

func (dm *DungeonM) SetChange(dungeon *Dungeon) {
	dm.changes[dungeon.ID()] = dungeon.data
}

func (dm *DungeonM) FlushRoomMember(roomID uint32, m *Manager) []*cl.GuildDungeonRoomRankInfo {
	members := dm.rm.GetMembers(roomID)
	rankInfo := make([]*cl.GuildDungeonRoomRankInfo, 0, len(members))
	for _, member := range members {
		newRankData := &cl.GuildDungeonRoomRankInfo{
			Gid:         member.ID(),
			Name:        member.Name(),
			Chapter:     member.GetChapter(),
			ChapterRate: member.GetChapterRate(),
			Badge:       member.GetBadge(),
			Score:       member.Star(),
		}
		newRankData.DeadBossNum, newRankData.LiveBossNum = member.GetChapterDeadAndLiveBossNum(newRankData.Chapter)
		guild := m.GetGuildByID(member.ID())
		if guild != nil {
			newRankData.ExpireTime = guild.getCurrentBadgeExpireTm()
		}

		rankInfo = append(rankInfo, newRankData)
	}
	return rankInfo
}

func (dm *DungeonM) GetMemberRank(roomID uint32) map[uint64]uint32 {
	members := dm.rm.GetMembers(roomID)
	rank := make(map[uint64]uint32)
	for i, member := range members {
		rank[member.ID()] = uint32(i + 1)
	}
	return rank
}

func (dm *DungeonM) Rand() *rand.Rand {
	return dm.Rd
}

func (dm *DungeonM) GetDungeonLogM() *activity.LogM {
	return dm.dungeonLogM
}

func (dm *DungeonM) calcResetType(now int64) (int, int64) {
	var resetTime int64
	resetTime = goxml.GetGuildDungeonSeasonResetTime(goxml.GetData(), now)
	if dm.dungeonReset.LastSeasonResetTime >= resetTime {
		return activity.GuildDungeonResetTypeNone, 0
	}
	if now >= resetTime {
		resetType := activity.GuildDungeonResetTypeSeason
		return resetType, resetTime
	} else {
		if resetTime-dm.dungeonReset.LastSeasonResetTime >
			int64(goxml.GetData().GuildConfigInfoM.GetGuildDungeonSeasonRound())*util.WeekSecs {
			/*resetTime = goxml.GetLastGuildDungeonSeasonResetTime(goxml.GetData()) // 补结算，修改结算时间
			if fixTime := dm.fixResetTimeForEarlySeasonReset(resetTime); fixTime != 0 {
				// 属于赛季提前结算，返回修正后的时间
				return activity.GuildDungeonResetTypeSeason, fixTime
			}*/
			return activity.GuildDungeonResetTypeSeason, goxml.GetLastGuildDungeonSeasonResetTime(goxml.GetData())
		}
	}

	resetTime = goxml.GetGuildDungeonWeeklyResetTime(now)
	if dm.dungeonReset.LastWeeklyResetTime >= resetTime {
		return activity.GuildDungeonResetTypeNone, 0
	}
	if now >= resetTime {
		resetType := activity.GuildDungeonResetTypeWeekly
		return resetType, resetTime
	} else {
		if resetTime-dm.dungeonReset.LastWeeklyResetTime > util.WeekSecs {
			return activity.GuildDungeonResetTypeWeekly, resetTime - util.WeekSecs
		}
	}
	return activity.GuildDungeonResetTypeNone, 0
}

// 检查重置
func (dm *DungeonM) checkReset(baseModule activity.BaseModuler, m *Manager, now int64) {

	// 2023.11.17 赛季开始前，修赛期，不判断结算
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(now) {
		return
	}

	resetType, resetTime := dm.calcResetType(now)
	if resetType == activity.GuildDungeonResetTypeNone {
		return
	}

	l4g.Infof("resetTime: resetType:%d resetTime:%d", resetType, resetTime)

	//重置前，先检查是否有数据保存
	dm.save(baseModule)
	dm.doReset(baseModule, resetType, resetTime, m)
}

// 执行重置
// XXX: 流程顺序不可随意改动
// @param activity.BaseModuler baseModule
// @param uint32 resetType 重置类型 1-每周 2-赛季
// @param int64 resetTime 重置时间
func (dm *DungeonM) doReset(baseModule activity.BaseModuler, resetType int, resetTime int64, m *Manager) {
	startTime := time.AccurateNow()

	//是否是赛季结算，仅两种情况，不是赛季结算就是每周结算
	isSeasonReset := resetType == activity.GuildDungeonResetTypeSeason

	//计算结果
	divisionDungeons := dm.computeDivisionAndStar(m, resetTime)

	//清空排行榜
	//m.commonRankM.ClearRank(baseModule, goxml.GuildDungeonChapterRankId)
	//m.commonRankM.ClearRank(baseModule, goxml.GuildDungeonWeeklyDamageRankId)

	// 清理房间
	dm.rm.clearRooms()

	if isSeasonReset {
		dm.seasonReset(m, divisionDungeons, resetTime)
		//m.divisionRank.clearRank() // 赛季结算，清空段位榜
		for _, dungeons := range divisionDungeons {
			for _, dungeon := range dungeons {
				m.divisionRank.Update(dungeon) // 赛季结算，重置后的段位更新排行榜
			}
		}
	} else {
		dm.rm.autoJoinRoom(divisionDungeons, dm)
	}

	//推送奖励信息
	dm.sendAwardToLogic(divisionDungeons, baseModule, isSeasonReset, resetTime, m)

	if len(divisionDungeons) > 0 {
		dm.checkSetNoFirstWeek()
	}

	dm.changeResetTm(isSeasonReset, resetTime)

	//统计执行时间
	endTime := time.AccurateNow()
	l4g.Infof("GuildDungeon.doReset partID:%d, resetType:%d, resetTime:%d, useTime:%v",
		m.partID, resetType, resetTime, endTime.Sub(startTime))
}

// computeDivisionAndStar
// @Description: 积分和段位处理
// @receiver dm
// @param isSeasonReset
// @param time
// @return map[uint32][]*Dungeon   key:段位   value:该段位的公会
func (dm *DungeonM) computeDivisionAndStar(m *Manager, resetTm int64) map[uint32][]*Dungeon {

	divisionDungeons := make(map[uint32][]*Dungeon, len(dm.dungeons))

	delDungeons := make([]uint64, 0, 20) //nolint:mnd

	for _, room := range dm.rm.datas {
		dm.rm.sortRoomMembers(room.ID)
		for i, dungeon := range room.getMembers() {
			guild := m.GetGuildByID(dungeon.ID())
			if guild == nil { // 公会已经解散，去掉dungeon数据
				delDungeons = append(delDungeons, dungeon.ID())
				continue
			}
			beforeDivision := dungeon.Division()
			if dungeon.Division() == goxml.GetData().GuildDungeonDivisionInoM.GetInitialDivision() && dungeon.GetWeeklyDamage() == 0 { // 最低段位，且本轮没有参加
				// 将该公会退出玩法，数据初始化
				dungeon.noPlayReset()
				m.sidAndGidM.setGidNotJoinDungeon(guild.GetSid(), guild.GetID())
				dm.SetChange(dungeon)
				continue
			}
			rank := uint32(i + 1)
			pointInfo := goxml.GetData().GuildDungeonPointInfoM.Index(rank)
			if pointInfo == nil {
				l4g.Errorf("dungeonM.computeDivisionAndStar: get starInfo error. star:%d", rank)
				continue
			}

			resetAddStar := dm.getResetAddStar(dungeon, pointInfo)
			dungeon.addStar(resetAddStar)
			if dungeon.Division() > goxml.GetData().GuildConfigInfoM.GetGuildDungeonDivisionLimit() { // 不掉段的Id
				dungeon.delStar(pointInfo.DeletePoint)
			}
			divisionInfo := goxml.GetData().GuildDungeonDivisionInoM.GetDivisionByStar(dungeon.Star())
			if divisionInfo == nil {
				l4g.Errorf("dungeonM.computeDivisionAndStar: get divisionInfo error. star:%d", dungeon.Star())
				continue
			}
			dungeon.UpdateDivision(divisionInfo.Id)

			m.divisionRank.Update(dungeon)

			dungeon.data.BakData = &cr.GuildDungeonResetBakData{
				Division:            dungeon.Division(),
				Star:                dungeon.Star(),
				MemberDamageRank:    dungeon.data.DamageRank,
				SeasonTopDivision:   dungeon.GetSeasonTopDivision(),
				DivisionBeforeReset: beforeDivision,
				RoomRank:            rank,
				BakTm:               resetTm,
				ResetAddStar:        resetAddStar,
			}

			if dungeon.UpdateLargeDivisions(divisionInfo.BigNode) {
				dungeon.data.BakData.FirstLargeDivision = divisionInfo.BigNode
			}
			dungeon.CheckAddStrategyCountByReset(pointInfo)
			dungeon.weeklyReset()
			divisionDungeons[divisionInfo.Id] = append(divisionDungeons[divisionInfo.Id], dungeon)
			dm.SetChange(dungeon)
		}
	}

	// 去掉已经不存在的公会的副本信息
	for _, id := range delDungeons {
		delete(dm.dungeons, id)
		dm.deletes[id] = struct{}{}
		m.sidAndGidM.delGidForDungeon(id)
	}

	return divisionDungeons
}

func (dm *DungeonM) AddStarByGst(gid uint64, star uint32, m *Manager) uint32 {
	guild := m.GetGuildByID(gid)
	if guild == nil {
		return uint32(ret.RET_GUILD_NOT_EXIST)
	}

	dungeon := m.GetDungeonM().GetDungeonByID(gid)
	if dungeon == nil {
		return uint32(ret.RET_GUILD_NOT_EXIST)
	}
	dungeon.addStar(star)

	divisionInfo := goxml.GetData().GuildDungeonDivisionInoM.GetDivisionByStar(dungeon.Star())
	if divisionInfo == nil {
		l4g.Errorf("dungeonM.computeDivisionAndStar: get divisionInfo error. star:%d", dungeon.Star())
		dm.SetChange(dungeon)
		return uint32(ret.RET_OK)
	}
	dungeon.UpdateDivision(divisionInfo.Id)
	m.divisionRank.Update(dungeon)

	dm.SetChange(dungeon)
	return uint32(ret.RET_OK)
}

// getResetAddStar
// @Description: 获取结算要加的积分
// @receiver dm
// @param dungeon
// @param starInfo
// @return uint32
func (dm *DungeonM) getResetAddStar(dungeon *Dungeon, starInfo *goxml.GuildDungeonPointInfo) uint32 {
	if starInfo == nil {
		return 0
	}

	chapterAdd := dungeon.getChapterAddStar(dm.GetIsFirstWeek())

	if dm.GetIsFirstWeek() {
		return chapterAdd
	}
	return starInfo.AddPoint + chapterAdd
}

func (dm *DungeonM) seasonReset(m *Manager, _ map[uint32][]*Dungeon, resetTime int64) {

	m.sidAndGidM.setAllGidNotJoinDungeon()

	seasonHallOfFame := dm.generateSeasonHallOfFame()
	if dm.dungeonReset.HallOfFame == nil {
		dm.dungeonReset.HallOfFame = make(map[uint32]*cr.GuildDungeonSeasonHallOfFame)
	}
	dm.dungeonReset.HallOfFame[goxml.GetCurrentSeasonID(goxml.GetData(), resetTime)] = seasonHallOfFame

	for _, dungeon := range dm.dungeons {
		if dungeon == nil {
			continue
		}
		divisionInfo := goxml.GetData().GuildDungeonDivisionInoM.Index(dungeon.Division())
		if divisionInfo == nil {
			l4g.Errorf("divisionInfo not exist. id:%d", dungeon.Division())
			continue
		}

		resetDivision, resetPoint := goxml.GetData().GuildDungeonDivisionInoM.CalcResetDivisionAndPoint(dungeon.Star())
		if resetDivision == 0 || resetPoint == 0 {
			l4g.Errorf("seasonReset: reset division is 0. points:%d", dungeon.Star())
			continue
		}

		dm.logSeasonFinalDivision(dungeon) // 重置前记录下段位

		dungeon.SetDivision(resetDivision)
		dungeon.SetStar(resetPoint)

		if dungeon.data.BakData != nil && dungeon.data.BakData.BakTm == resetTime { // 参与公会副本
			dungeon.setBakDivisionRank(m.divisionRank.GetRankById(dungeon.ID()))
			dungeon.seasonReset()
			dm.guildAward(dungeon, divisionInfo)
		}
		dm.SetChange(dungeon)
	}

	dm.dungeonResetChange = true
}

func (dm *DungeonM) logSeasonFinalDivision(dungeon *Dungeon) {
	guild := dm.m.GetGuildByID(dungeon.ID())
	if guild == nil {
		l4g.Errorf("dm.logSeasonFinalDivision: guild not exist. gid:%d", dungeon.ID())
		return
	}
	logFinalDivision := &log.GuildSeasonFinalDivision{
		Gid:             guild.GetID(),
		Name:            guild.GetName(),
		BadgeIcon:       guild.getBadgeIcon(),
		BadgeBackground: guild.getBadgeBackground(),
		Division:        dungeon.Division(),
	}
	for uid := range guild.GetMembers() {
		logFinalDivision.Members = append(logFinalDivision.Members, uid)
	}
	dclog.LogGuildSeasonFinalDivision(dm.m.baseModule.GetSrv(), dm.m.baseModule.OpGroup(), logFinalDivision)
}

func (dm *DungeonM) generateSeasonHallOfFame() *cr.GuildDungeonSeasonHallOfFame {
	list := dm.m.divisionRank.GetRangeByRank(1, 3) //nolint:mnd

	seasonHallOfFame := &cr.GuildDungeonSeasonHallOfFame{}
	for _, v := range list {
		value := v.(*DivisionRankValue)
		gid := value.Key()
		guild := dm.m.GetGuildByID(gid)
		if guild == nil {
			continue
		}
		dungeon := dm.GetDungeonByID(gid)
		if dungeon == nil {
			continue
		}
		fameInfo := &cl.GuildDungeonHallOfFameInfo{
			Gid:      value.Key(),
			Name:     guild.GetName(),
			Badge:    guild.GetBadge(),
			Division: dungeon.Division(),
			Star:     dungeon.Star(),
			Level:    guild.GetLevel(),
		}
		seasonHallOfFame.List = append(seasonHallOfFame.List, fameInfo)
	}
	return seasonHallOfFame
}

func (dm *DungeonM) guildAward(dungeon *Dungeon, divisionInfo *goxml.GuildDungeonDivisionInfo) {
	guild := dm.m.GetGuildByID(dungeon.ID())
	if guild == nil {
		return
	}
	seasonReward := goxml.GetData().GuildDungeonSeasonRewardInfoM.Index(divisionInfo.SeasonRankRewardId)
	if seasonReward == nil {
		l4g.Errorf("dm.seasonReset: get seasonRewardInfo error. id:%d", divisionInfo.SeasonRankRewardId)
		return
	}
	if seasonReward.GuildValue1 > 0 {
		guild.addBadge(seasonReward.GuildValue1)
	}
	if seasonReward.GuildValue2 > 0 {
		guild.addBadge(seasonReward.GuildValue2)
	}
	rankRewardInfo := goxml.GetData().GuildDungeonRankRewardInfoM.GetInfoByRank(dungeon.GetBakDataSeasonDivisionRank())
	if rankRewardInfo != nil {
		if rankRewardInfo.GuildLogo > 0 {
			guild.addBadge(rankRewardInfo.GuildLogo)
		}
	}
	dm.m.SetChange(guild)
}

func (dm *DungeonM) sendAwardToLogic(divisionDungeons map[uint32][]*Dungeon,
	baseModule activity.BaseModuler, seasonReset bool, time int64, m *Manager) {

	l4g.Infof("DungeonM.sendAwardToLogic: dungeonCount:%d, time:%d",
		len(dm.dungeons), time)

	result := make(map[uint64]map[uint64]*l2c.GuildDungeonAwardBakData)

	for _, dungeons := range divisionDungeons {
		for _, dungeon := range dungeons {
			guild := m.GetGuildByID(dungeon.ID())
			if guild == nil {
				l4g.Errorf("sendAwardToLogic: guild not exist. gid:%d", dungeon.ID())
				continue
			}

			bakData := dungeon.data.BakData

			if bakData == nil {
				l4g.Errorf("sendAwardToLogic: bakData not exist. gid:%d", dungeon.ID())
				continue
			}

			for _, member := range guild.GetMembers() {
				realSid := member.Sid
				// todo 这里对于合服玩家的真实SID待处理
				//realSid := baseModule.GetRealServerID(member.Sid)

				guildsBakData := result[realSid]
				if guildsBakData == nil {
					guildsBakData = make(map[uint64]*l2c.GuildDungeonAwardBakData)
					result[realSid] = guildsBakData
				}

				dungeonBak := guildsBakData[guild.GetID()]
				if dungeonBak == nil {
					dungeonBak = dm.generateC2LAwardBakData(dungeon, guild)
					guildsBakData[guild.GetID()] = dungeonBak
				}
				if rank, exist := bakData.MemberDamageRank[member.Id]; exist {
					dungeonBak.MemberDamageRank[member.Id] = rank
				}
			}
		}
	}

	//推送数据到游戏服
	for sid, data := range result {
		msg := &l2c.C2L_GuildDungeonAward{
			Ret:      uint32(ret.RET_OK),
			Sid:      sid,
			Time:     time,
			IsSeason: seasonReset,
		}
		msg.LastSeasonTop_3, msg.LastSeasonId = dm.GetLastSeasonHallOfFame()
		for _, dungeonBakData := range data {
			msg.Ranks = append(msg.Ranks, dungeonBakData)
		}
		baseModule.SendCmdToLogic(sid, 0, l2c.ID_MSG_C2L_GuildDungeonAward, msg)
	}
}

// FlushDungeonsLastAwardData
// @Description: 根据合服服务器id列表，获取上一次结算数据. 根据前一天的结算时间，确认前一天是每日结算还是赛季结算
// @receiver dm
// @param cmsg
// @param m
// @param gids
func (dm *DungeonM) FlushDungeonsLastAwardData(cmsg *l2c.C2L_GuildDungeonAward, m *Manager, sids []uint64) {

	for gid, logicGuild := range m.GetLogicGuilds(sids) {
		guild := m.GetGuildByID(gid)
		dungeon := dm.GetDungeonByID(gid)
		if dungeon == nil || guild == nil || dungeon.data.BakData == nil {
			continue
		}

		if dungeon.data.BakData.BakTm != 0 { // 等于0的是老的数据，兼容老的数据
			if time.Now().Unix()-dungeon.data.BakData.BakTm >= util.WeekSecs {
				// 备份数据的时间大于一周的，不是最近一次结算的数据，continue
				continue
			}
		}
		awardBakData := dm.generateC2LAwardBakData(dungeon, guild)

		bakMemberDamageRank := dungeon.data.BakData.MemberDamageRank

		if bakMemberDamageRank != nil {
			for _, memberId := range logicGuild.Members {
				if rank, exist := bakMemberDamageRank[memberId]; exist {
					awardBakData.MemberDamageRank[memberId] = rank
				}
			}
		}

		cmsg.Ranks = append(cmsg.Ranks, awardBakData)
	}
	cmsg.Time = dm.dungeonReset.LastWeeklyResetTime
	cmsg.IsSeason = dm.dungeonReset.LastSeasonResetTime == dm.dungeonReset.LastWeeklyResetTime

}

// generateC2LAwardBakData
// @Description: 获取发奖的BakData，注意MemberDamageRank只初始化了
// @receiver dm
// @param dungeon
// @param guild
// @return *l2c.GuildDungeonAwardBakData
func (dm *DungeonM) generateC2LAwardBakData(dungeon *Dungeon, guild *Guild) *l2c.GuildDungeonAwardBakData {
	bakData := &l2c.GuildDungeonAwardBakData{
		Gid:                guild.GetID(),
		Division:           dungeon.data.BakData.Division,
		MemberDamageRank:   make(map[uint64]uint32),
		Leader:             guild.GetLeader(),
		SeasonTopDivision:  dungeon.data.BakData.SeasonTopDivision,
		FirstLargeDivision: dungeon.data.BakData.FirstLargeDivision,
		SeasonDivisionRank: dungeon.data.BakData.SeasonDivisionRank,
	}
	bakData.Deputy = make([]uint64, 0, len(guild.GetDeputy()))
	bakData.Deputy = append(bakData.Deputy, guild.GetDeputy()...)
	return bakData
}

func (dm *DungeonM) setPartID(partID uint32) {
	dm.rm.partID = partID
}

func (dm *DungeonM) GetHallOfFame(seasonID uint32) []*cl.GuildDungeonHallOfFameInfo {
	if dm.dungeonReset == nil || dm.dungeonReset.HallOfFame == nil {
		return nil
	}
	if dm.dungeonReset.HallOfFame[seasonID] == nil {
		return nil
	}

	return dm.dungeonReset.HallOfFame[seasonID].List
}

func (dm *DungeonM) GetHallOfFameSeasonIds() []uint32 {
	if dm.dungeonReset == nil || dm.dungeonReset.HallOfFame == nil {
		return nil
	}
	list := make([]uint32, 0, len(dm.dungeonReset.HallOfFame))
	for id := range dm.dungeonReset.HallOfFame {
		list = append(list, id)
	}
	return list
}

// GetLastSeasonHallOfFame
// @Description: 获取上赛季荣誉殿堂数据, 传给客户端,用来缓存
//
//	注意：此方法会优先获取本赛季数据，如果有，说明本赛季已经结算完了，只是处于结算期，会返回本赛季的，如果没有则返回上赛季的！！！
func (dm *DungeonM) GetLastSeasonHallOfFame() ([]*cl.GuildDungeonHallOfFameInfo, uint32) {

	if dm.dungeonReset == nil || dm.dungeonReset.HallOfFame == nil {
		return nil, 0
	}
	currentSeasonId := goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix())
	if currentSeasonId == 0 {
		return nil, 0
	}

	list := dm.GetHallOfFame(currentSeasonId)
	if list != nil {
		return list, currentSeasonId
	}
	return dm.GetHallOfFame(currentSeasonId - 1), currentSeasonId - 1
}

func (dm *DungeonM) CheckInitStrategy(dungeon *Dungeon) {

	now := time.Now().Unix()
	seasonId := goxml.GetCurrentSeasonID(goxml.GetData(), now)
	round := goxml.GetGuildDungeonCurrentRound(goxml.GetData(), now)

	var change bool
	if !dungeon.SeasonStrategyExist(seasonId) {
		dungeon.SeasonStrategyInit(seasonId)
		change = true
	}

	if !dungeon.WeekStrategyExist(seasonId, round) {
		dungeon.WeekStrategyInit(seasonId, round)
		change = true
	}

	if change {
		dm.SetChange(dungeon)
	}
}

// GenerateChatRoomId
// @Description:
// @receiver dm
// @param season 赛季ID
// @param round  轮次ID
// @param roomId 房间ID
// @return uint64
//
//nolint:mnd
func (dm *DungeonM) GenerateChatRoomId(season, round, roomId uint32) uint64 {
	return uint64(season)*uint64(100000*activity.GuildDungeonRoomIdPartParam) + // 乘100000,round留范围 0~99
		uint64(round)*uint64(1000*activity.GuildDungeonRoomIdPartParam) + // 乘1000,战区ID留范围0~999
		uint64(roomId)
}

// 旧的生成chatRoomId方法
/*func (dm *DungeonM) OldGenerateChatRoomId(arena, season, round, roomId uint32) uint64 {
	return uint64(arena)*uint64(1000000*activity.GuildDungeonRoomIdParam) + uint64(season)*uint64(10000*activity.GuildDungeonRoomIdParam) +
		uint64(round)*uint64(100*activity.GuildDungeonRoomIdParam) + uint64(roomId)
}*/

func (dm *DungeonM) GetIsFirstWeek() bool {
	return dm.dungeonReset.FirstWeek
}

func (dm *DungeonM) GetRoomManager() *RoomManager {
	return dm.rm
}
