package seasonarena

import (
	"app/cross/activity"
	"app/goxml"
	"app/logic/srank"
	"app/protos/in/cr"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/skiplist"
)

type User struct {
	userManager *UserManager
	data        *cr.CrossSeasonArenaUser

	user                 []uint64 //上一轮匹配的真人对手和本轮待生成的对手
	bot                  []uint64 //上一轮匹配到的机器人和本轮待生成的机器人
	findUserPowerNoMatch map[*srank.RankItem[*RankItem]]struct{}
}

func newUser(data *cr.CrossSeasonArenaUser, userManager *UserManager) *User {
	return &User{
		userManager: userManager,
		data:        data,
	}
}

func (u *User) GetScore() uint32 {
	return u.data.Score
}

func (u *User) SetScore(score uint32) {
	u.data.Score = score
	u.userManager.SetUserChange(u)
}

func (u *User) Search() uint32 {
	matchInfo := goxml.GetData().SeasonArenaMatchInfoM.GetMatchData(u.data.Score)
	if matchInfo == nil {
		l4g.Errorf("uid:%d season arena match info not found, score:%d", u.data.Uid, u.data.Score)
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	opponents := make([]*cr.CrossSeasonArenaOpponent, 0, goxml.SeasonArenaOpponentNum)
	u.buildSearchCache()
	defer u.clearSearchCache()
	divisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(u.GetScore(), u.GetRank())
	if divisionInfo == nil {
		l4g.Errorf("uid:%d SeasonArenaDivisionInfoM get division failed score:%d rank:%d ", u.data.Uid, u.data.Score, u.GetRank())
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}
	maxSearchScore, rankReq := goxml.GetData().SeasonArenaDivisionInfoM.GetDivisionTopScore(divisionInfo.TeamNum)
	l4g.Debugf("divisionInfo.TeamNum:%d rankReq:%d maxSearchScore:%d ", divisionInfo.TeamNum, rankReq, maxSearchScore)
	if rankReq > 0 {
		rankValue := u.userManager.Ranks[0].GetItemByRank(rankReq)
		l4g.Debugf("rankValue:%v", rankValue)
		if rankValue != nil && uint32(rankValue.Value) > maxSearchScore {
			maxSearchScore = uint32(rankValue.Value)
		}
	}

	for i := 0; i < int(goxml.SeasonArenaOpponentNum); i++ {
		ret, option := u.GetSearchOption(matchInfo, i)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("uid:%d season arena match get search match info:%v option error, ret:%d", u.data.Uid, matchInfo, ret)
			return ret
		}
		var opponent *cr.CrossSeasonArenaOpponent
		switch option {
		case goxml.SeasonArenaOptionPlayer:
			//积分找
			opponent = u.SearchPlayer(matchInfo, i, maxSearchScore)
			if opponent != nil {
				l4g.Debugf("Search player oppoent:%v", opponent)
				opponents = append(opponents, opponent)
			} else {
				//排名找
				opponent = u.SearchPlayerByRank(maxSearchScore)
				if opponent != nil {
					l4g.Debugf("Search player by rank oppoent:%v", opponent)
					opponents = append(opponents, opponent)
				} else {
					//机器人
					opponent = u.SearchRobot(matchInfo, i, divisionInfo.Id)
					if opponents == nil {
						l4g.Errorf("uid:%d search player robot failed ", u.data.Uid)
						return uint32(cret.RET_SEASON_ARENA_SEARCH_OPPONENTS_FAILED)
					}
					l4g.Debugf("Search player by robot oppoent:%v", opponent)
					opponents = append(opponents, opponent)
				}
			}
		case goxml.SeasonArenaOptionRobot:
			opponent = u.SearchRobot(matchInfo, i, divisionInfo.Id)
			if opponents == nil {
				l4g.Errorf("uid:%d search robot failed ", u.data.Uid)
				return uint32(cret.RET_SEASON_ARENA_SEARCH_OPPONENTS_FAILED)
			}
			l4g.Debugf("Search robot oppoent:%v", opponent)
			opponents = append(opponents, opponent)
		}
		if opponent != nil {
			u.addSearchCache(opponent)
		}
	}
	if len(opponents) == int(goxml.SeasonArenaOpponentNum) {
		l4g.Info("Search opponent success, uid:%d, opponents:%v", u.data.Uid, opponents)
		u.setOpponents(opponents)
	} else {
		l4g.Errorf("uid:%d search opponent failed, opponents:%v", u.data.Uid, opponents)
		return uint32(cret.RET_SEASON_ARENA_SEARCH_OPPONENTS_FAILED)
	}
	return uint32(cret.RET_OK)
}

func (u *User) GetSearchOption(matchInfo *goxml.SeasonArenaMatchInfo, stage int) (uint32, uint32) {
	switch stage {
	case activity.SeasonArenaMatchStageHigh:
		return uint32(cret.RET_OK), matchInfo.HighRobot
	case activity.SeasonArenaMatchStageMid:
		return uint32(cret.RET_OK), matchInfo.MidRobot
	case activity.SeasonArenaMatchStageLow:
		return uint32(cret.RET_OK), matchInfo.LowRobot
	}
	return uint32(cret.RET_SYSTEM_DATA_ERROR), 0
}

func (u *User) SearchPlayer(matchInfo *goxml.SeasonArenaMatchInfo, stage int, searchMaxScore uint32) *cr.CrossSeasonArenaOpponent {
	minP, maxP := u.GetMatchPower(matchInfo, u.data.Snapshot.GetSeasonTopPower(), stage)
	uid := u.data.Uid
	score := u.data.GetScore()
	var opUID uint64
	var opServerID uint64
	checkFunc := func(value interface{}) bool {
		rankItem := value.(*srank.RankItem[*RankItem])
		if rankItem.Extra == nil {
			return false
		}
		l4g.Debugf("oppoent user snapshot:%v", rankItem.Extra.user)
		user := rankItem.Extra.user
		//验证本次匹配中，搜到的对手id是否重复
		if u.IsOpUIDRepeat(uid, user.Uid) {
			l4g.Debugf("season arenaM.matchPlayer: isOpUIDRepeat, opUID:%d, uid:%d",
				user.Uid, uid)
			return false
		}

		SeasonTopPower := user.GetSnapshot().GetSeasonTopPower()
		if SeasonTopPower < minP || SeasonTopPower > maxP {
			u.findUserPowerNoMatch[rankItem] = struct{}{}
			l4g.Debugf("season arenaM.matchPlayer: SeasonTopPower not match, opUID:%d, uid:%d, SeasonTopPower:%d, minP:%d, maxP:%d",
				user.Uid, uid, SeasonTopPower, minP, maxP)
			return false
		}

		return true
	}

	for round := 1; round <= activity.SeasonArenaSearchExpandCount; round++ {
		minScore, maxScore := u.GetScoreRange(matchInfo, stage, round, score, searchMaxScore)
		list := u.GetSearchRank().rank.GetRangeByScoreRandom(u.userManager.Manager.rand, &skiplist.RangeSpec{
			Reverse:     true,
			Min:         minScore,
			Max:         maxScore,
			Num:         int(activity.SeasonArenaMatchPlayerMaxCount),
			MaxCheckNum: int(activity.SeasonArenaMatchPlayerMaxCount),
			CheckF:      checkFunc,
		})
		listLen := len(list)
		var randomIndex int
		if listLen > 0 {
			randomIndex = u.userManager.Manager.rand.RandBetween(0, listLen-1)
		}

		l4g.Debugf("season arenaM.matchPlayer: search, uid:%d, stage:%d, round:%d, minScore:%d, maxScore:%d, minP:%d, maxP:%d, list:%+v randomIndx:%d",
			uid, stage, round, minScore, maxScore, minP, maxP, list, randomIndex)
		if listLen > 0 && list[randomIndex] != nil {
			opUID = list[randomIndex].Key()
			opServerID = list[randomIndex].Extra.user.Sid
			break
		}

		if len(u.findUserPowerNoMatch) > 0 {
			tmp := make([]*srank.RankItem[*RankItem], 0, len(u.findUserPowerNoMatch))
			for key := range u.findUserPowerNoMatch {
				if u.IsOpUIDRepeat(uid, key.Extra.user.Uid) {
					l4g.Debugf("findUserPowerNoMatch: isOpUIDRepeat, opUID:%d, uid:%d",
						key.Extra.user.Uid, uid)
					continue
				}
				tmp = append(tmp, key)
			}

			tmpLen := len(tmp)
			if tmpLen > 0 {
				randomIndex = u.userManager.Manager.rand.RandBetween(0, tmpLen-1)
			}

			if tmpLen > 0 && tmp[randomIndex] != nil {
				opUID = tmp[randomIndex].Key()
				opServerID = tmp[randomIndex].Extra.user.Sid
				break
			}
		}
	}

	if opUID == 0 || opServerID == 0 {
		return nil
	}
	return &cr.CrossSeasonArenaOpponent{
		Id:       opUID,
		ServerId: opServerID,
	}
}

func (u *User) SearchPlayerByRank(maxSearchScore uint32) *cr.CrossSeasonArenaOpponent {
	rank, _ := u.GetSearchRank().GetRankAndNode(u.data.Uid)
	start := uint32(1)
	if rank > activity.SeasonArenaRankSearchUpStep {
		start = rank - activity.SeasonArenaRankSearchUpStep
	}
	end := rank + activity.SeasonArenaRankSearchDownStep
	list := u.GetSearchRank().rank.GetByRange(start, end)
	var rankItem *RankItem
	lens := len(list)
	if len(list) > 0 {
		for i := 0; i < lens; i++ {
			if list[i] == nil || u.IsOpUIDRepeat(u.data.Uid, list[i].Id) || list[i].Score() >= uint64(maxSearchScore) {
				list = append(list[:i], list[i+1:]...)
				lens--
				i--
			}
		}
	}

	if len(list) == 0 {
		return nil
	}

	randIndex := u.userManager.Manager.rand.RandBetween(0, lens-1)
	rankItem = list[randIndex].Extra
	if rankItem != nil {
		return &cr.CrossSeasonArenaOpponent{
			Id:       rankItem.user.Uid,
			ServerId: rankItem.user.Sid,
		}
	} else {
		return nil
	}
}

func (u *User) SearchRobot(matchInfo *goxml.SeasonArenaMatchInfo, stage int, division uint32) *cr.CrossSeasonArenaOpponent {
	score := u.getBotScore(matchInfo, stage)
	botInfo := goxml.GetData().SeasonArenaBotInfoM.Generate(u.userManager.Manager.rand, division, u.bot)
	if botInfo == nil {
		l4g.Errorf("season arena SearchRobot: generate bot id error, botLv:%d, uid:%d", division, u.data.Uid)
		return nil
	}
	var serverID uint64
	if u.GetScore() < goxml.GetData().SeasonArenaConfigInfoM.GetMatchAllServerScore() {
		serverID = u.data.Sid
	} else {
		serverIDs := u.userManager.Manager.baseModule.GetServerIDList()
		if len(serverIDs) == 0 {
			l4g.Errorf("uid:%d SearchRobot season arena get ServerIDList len is zero", u.data.Uid)
			return nil
		}
		serverIDsClone := make([]uint64, 0, len(serverIDs))
		serverIDsClone = append(serverIDsClone, serverIDs...)
		index := u.userManager.Manager.rand.Intn(len(serverIDsClone))
		serverID = serverIDsClone[index]
	}

	l4g.Debugf("season arenaM.matchRobot: generate, uid:%d, botLv:%d, id:%d, score:%d serverID:%d",
		u.data.Uid, botInfo.ShowLevel, botInfo.ID, score, serverID)

	return &cr.CrossSeasonArenaOpponent{
		Id:       uint64(botInfo.ID),
		Level:    botInfo.ShowLevel,
		Name:     u.botName(),
		BaseId:   u.botBaseID(),
		Score:    score,
		Bot:      true,
		ServerId: serverID,
	}
}

func (u *User) botName() string {
	first := u.userManager.Manager.rand.RandBetween(1, goxml.GetData().ConfigInfoM.BotFirstNameMax)
	second := u.userManager.Manager.rand.RandBetween(1, goxml.GetData().ConfigInfoM.BotSecondNameMax)
	return strconv.Itoa(first) + "|" + strconv.Itoa(second)
}

func (u *User) botBaseID() uint32 {
	return goxml.GetData().BotHeadInfoM.GenerateAvatar(u.userManager.Manager.rand)
}

func (u *User) getBotScore(info *goxml.SeasonArenaMatchInfo, stage int) uint32 {
	switch stage {
	case activity.SeasonArenaMatchStageHigh:
		return info.BotHighScore
	case activity.SeasonArenaMatchStageMid:
		return info.BotMidScore
	case activity.SeasonArenaMatchStageLow:
		return info.BotLowScore
	}
	return 0
}

func (u *User) IsOpUIDRepeat(uid, opID uint64) bool {
	if opID == uid {
		return true
	}

	for _, Opponent := range u.data.Opponents {
		if Opponent == nil {
			continue
		}
		if Opponent.Id == opID {
			return true
		}
	}

	for _, opponentID := range u.user {
		if opponentID == 0 {
			continue
		}
		if opponentID == opID {
			return true
		}
	}

	return false
}

func (u *User) GetMatchPower(info *goxml.SeasonArenaMatchInfo, power int64, stage int) (int64, int64) {
	var maxPower int64
	var minPower int64
	switch stage {
	case activity.SeasonArenaMatchStageHigh:
		maxPower = int64(float64(power) / float64(goxml.BaseInt64) * float64(info.HighForceMax))
		minPower = int64(float64(power) / float64(goxml.BaseInt64) * float64(info.HighForceMin))
	case activity.SeasonArenaMatchStageMid:
		maxPower = int64(float64(power) / float64(goxml.BaseInt64) * float64(info.MidForceMax))
		minPower = int64(float64(power) / float64(goxml.BaseInt64) * float64(info.MidForceMin))
	case activity.SeasonArenaMatchStageLow:
		maxPower = int64(float64(power) / float64(goxml.BaseInt64) * float64(info.LowForceMax))
		minPower = int64(float64(power) / float64(goxml.BaseInt64) * float64(info.LowForceMin))
	}
	if minPower > maxPower {
		minPower, maxPower = maxPower, minPower
	}
	return minPower, maxPower
}

// 获取真实玩家积分匹配范围
func (u *User) GetScoreRange(info *goxml.SeasonArenaMatchInfo, stage, round int,
	score, searchMaxScore uint32) (uint64, uint64) {
	baseScore := int64(score)
	var minScore, maxScore uint64
	switch stage {
	case activity.SeasonArenaMatchStageHigh:
		minScore = u.calcScore(info.HighMinAdd, baseScore, int64(round))
		maxScore = u.calcScore(info.HighMaxAdd, baseScore, int64(round))
	case activity.SeasonArenaMatchStageMid:
		minScore = u.calcScore(info.MidMinAdd, baseScore, int64(round))
		maxScore = u.calcScore(info.MidMaxAdd, baseScore, int64(round))
	case activity.SeasonArenaMatchStageLow:
		minScore = u.calcScore(info.LowMinAdd, baseScore, int64(round))
		maxScore = u.calcScore(info.LowMaxAdd, baseScore, int64(round))
	}
	if maxScore > uint64(searchMaxScore) {
		maxScore = uint64(searchMaxScore)
	}
	return minScore, maxScore
}

func (u *User) calcScore(add, baseScore, round int64) uint64 {
	var uScore uint64
	scoreChange := add * round
	score := baseScore + scoreChange
	if score < 0 {
		uScore = 0
	} else {
		uScore = uint64(score)
	}
	return uScore
}

func (u *User) buildSearchCache() {
	u.user = make([]uint64, 0, goxml.SeasonArenaOpponentNum)
	u.bot = make([]uint64, 0, goxml.SeasonArenaOpponentNum)
	u.findUserPowerNoMatch = make(map[*srank.RankItem[*RankItem]]struct{})
	for _, v := range u.data.Opponents {
		if v == nil {
			continue
		}
		if v.Bot {
			u.bot = append(u.bot, v.Id)
		} else {
			u.user = append(u.user, v.Id)
		}
	}
}

func (u *User) clearSearchCache() {
	u.user = nil
	u.bot = nil
	u.findUserPowerNoMatch = nil
}

func (u *User) addSearchCache(Opponent *cr.CrossSeasonArenaOpponent) {
	if Opponent == nil {
		return
	}
	if Opponent.Bot {
		u.bot = append(u.bot, Opponent.Id)
	} else {
		u.user = append(u.user, Opponent.Id)
	}
}

func (u *User) setOpponents(opponent []*cr.CrossSeasonArenaOpponent) {
	u.data.Opponents = opponent
	u.userManager.SetUserChange(u)
}

func (u *User) FlushOpponent2Logic() []*cl.SeasonArenaOpponentClient {
	ret := make([]*cl.SeasonArenaOpponentClient, 0, len(u.data.Opponents))
	for _, v := range u.data.Opponents { //nolint:varnamelen
		if v == nil {
			continue
		}
		if v.Bot {
			ret = append(ret, &cl.SeasonArenaOpponentClient{
				Snapshot: &cl.UserSnapshot{
					Id:       v.Id,
					SeasonLv: v.Level,
					Name:     v.Name,
					BaseId:   v.BaseId,
					Sid:      v.ServerId,
				},
				Score: v.Score,
				Bot:   true,
			})
		} else {
			opAsUser := u.userManager.GetUser(v.Id)
			if opAsUser == nil {
				l4g.Errorf("uid:%d season arena opponent user not found, opUID:%d", u.data.Uid, v.Id)
				continue
			}
			rank, _ := u.GetTotalRank().GetRankAndNode(v.Id)
			ret = append(ret, &cl.SeasonArenaOpponentClient{
				Score:    opAsUser.GetScore(),
				Snapshot: opAsUser.data.Snapshot.Clone(),
				Rank:     rank,
			})
		}
	}
	return ret
}

func (u *User) GetSearchRank() *Rank {
	if u.GetScore() >= goxml.GetData().SeasonArenaConfigInfoM.GetMatchAllServerScore() {
		return u.userManager.AllServerSearchRank
	} else {
		return u.userManager.Ranks[u.GetAreaId()]
	}
}

func (u *User) GetTotalRank() *Rank {
	rank, exist := u.userManager.Ranks[0]
	if !exist {
		rank = newRank(activity.SeasonArenaRankItemNum)
		u.userManager.Ranks[0] = rank
	}
	return rank
}

func (u *User) GetAllServerSearchRank() *Rank {
	var rank *Rank
	rank = u.userManager.AllServerSearchRank
	if rank == nil {
		rank = newRank(activity.SeasonArenaRankItemNum)
		u.userManager.AllServerSearchRank = rank
	}
	return rank
}

func (u *User) GetArenaSearchRank() *Rank {
	rank, exist := u.userManager.Ranks[u.GetAreaId()]
	if !exist {
		rank = newRank(activity.SeasonArenaRankItemNum)
		u.userManager.Ranks[u.GetAreaId()] = rank
	}
	return rank
}

func (u *User) GetOpponent(uid uint64, serviceId uint64) (*cr.CrossSeasonArenaOpponent, uint32) {
	for index, v := range u.data.Opponents {
		if v == nil {
			continue
		}
		if v.Id == uid && v.ServerId == serviceId {
			return v, uint32(index)
		}
	}
	return nil, 0
}

func (u *User) GetDivision() *goxml.SeasonArenaDivisionInfo {
	rank, _ := u.GetTotalRank().GetRankAndNode(u.data.Uid)
	divisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(u.GetScore(), rank)
	return divisionInfo
}

func (u *User) AddScore(addScore uint32) uint32 {
	u.data.Score += addScore
	u.userManager.SetUserChange(u)
	return u.data.Score
}

func (u *User) ReduceScore(reduceScore uint32) uint32 {
	if u.data.Score < reduceScore {
		u.data.Score = 0
	} else {
		u.data.Score -= reduceScore
	}
	u.userManager.SetUserChange(u)
	return u.data.Score
}

func (u *User) GetRank() uint32 {
	rank, _ := u.GetTotalRank().GetRankAndNode(u.data.Uid)
	return rank
}

func (u *User) GetUserLastRank() uint32 {
	return u.data.LastRoundRank
}

// 更新排行榜自动更换搜索排行榜
func (u *User) UpdateRank(now int64) {
	u.data.Time = now
	if !u.data.SeasonRankIn {
		u.data.SeasonRankIn = true
	}
	u.GetTotalRank().rank.Update(u.data.Uid, &RankItem{user: u.data.Clone()}, uint64(u.data.Score), uint64(u.data.Time))
	if u.GetScore() >= goxml.GetData().SeasonArenaConfigInfoM.GetMatchAllServerScore() {
		u.GetArenaSearchRank().rank.Delete(u.data.Uid)
		u.GetAllServerSearchRank().rank.Update(u.data.Uid, &RankItem{user: u.data.Clone()}, uint64(u.data.Score), uint64(u.data.Time))
	} else {
		u.GetAllServerSearchRank().rank.Delete(u.data.Uid)
		u.GetArenaSearchRank().rank.Update(u.data.Uid, &RankItem{user: u.data.Clone()}, uint64(u.data.Score), uint64(u.data.Time))
	}
	u.userManager.SetUserChange(u)
}

func (u *User) CheckUpdateSnapshot(newSnapshot *cl.UserSnapshot) {
	if u.isNeedUpdateSnapshot(newSnapshot) {
		oldSnapshot := u.data.Snapshot
		u.data.Snapshot = newSnapshot
		if oldSnapshot.AreaId > 0 {
			u.data.Snapshot.AreaId = oldSnapshot.AreaId
		}
		u.userManager.SetUserChange(u)
	}
}

func (u *User) CheckUpdateDefPower(power int64) {
	u.GetUserSnapshot().DefensePower = power
	u.userManager.SetUserChange(u)
}

func (u *User) CheckUpdateNextSeasonCrystalTop35Power(newSnapshot *cl.UserSnapshot, sta *cr.CrossSeasonArenaSta) {
	openInfo := goxml.GetData().SeasonArenaInfoM.GetNextSeasonArenaOpenInfo(sta.SeasonId, sta.Round)
	if openInfo != nil && openInfo.SeasonID != sta.SeasonId && sta.State == uint32(common.SEASON_ARENA_SA_ROUND_BREAK) {
		u.data.SeasonCrystalTop35Power = newSnapshot.SeasonCrystalTop35Power
		u.userManager.SetUserChange(u)
	}
}

func (u *User) isNeedUpdateSnapshot(newSnapshot *cl.UserSnapshot) bool {
	if newSnapshot == nil {
		return false
	}
	/*注释掉，现在在被打传上来的都是0
	if newSnapshot.ArenaId == 0 {
		return false
	}*/
	oldSnapshot := u.data.Snapshot
	if oldSnapshot.AreaId == 0 && newSnapshot.AreaId != oldSnapshot.AreaId {
		return true
	}
	if u.isPowerChanged(newSnapshot) {
		return true
	}
	if newSnapshot.BaseId != oldSnapshot.BaseId {
		return true
	}
	if newSnapshot.GuildId != oldSnapshot.GuildId {
		return true
	}
	if newSnapshot.Name != oldSnapshot.Name {
		return true
	}
	if newSnapshot.Image != oldSnapshot.Image {
		return true
	}
	if newSnapshot.SeasonCrystalTop35Power != oldSnapshot.SeasonCrystalTop35Power {
		return true
	}
	if newSnapshot.Title != oldSnapshot.Title {
		return true
	}
	if newSnapshot.PokemonImage != oldSnapshot.PokemonImage {
		return true
	}
	return false
}

func (u *User) isPowerChanged(newSnapshot *cl.UserSnapshot) bool {
	oldSnapshot := u.data.Snapshot
	return newSnapshot.DefensePower != oldSnapshot.DefensePower
}

func (u *User) GetMinSnapshot() *cl.MiniUserSnapshot {
	snapshot := u.data.Snapshot
	return &cl.MiniUserSnapshot{
		Id:     snapshot.Id,
		Name:   snapshot.Name,
		Level:  snapshot.Level,
		BaseId: snapshot.BaseId,
		Power:  snapshot.Power,
		Sid:    snapshot.Sid,
	}
}

func (u *User) Flush2Client() *cl.SeasonArenaOpponentClient {
	client := &cl.SeasonArenaOpponentClient{
		Score:    u.data.Score,
		Rank:     u.GetRank(),
		Snapshot: u.data.Snapshot.Clone(),
	}

	return client
}

func (u *User) IsCurRound(sta *cr.CrossSeasonArenaSta) bool {
	if sta == nil {
		return false
	}
	return u.data.Season == sta.SeasonId && u.data.Round == sta.Round
}

func (u *User) IsCurRoundAndSign(sta *cr.CrossSeasonArenaSta) bool {
	return u.data.Season == sta.SeasonId && u.data.Round == sta.Round && u.data.IsSign
}

func (u *User) Convert2AwardBak(rank uint32) *cr.CrossSeasonArenaServerUserBak {
	ret := &cr.CrossSeasonArenaServerUserBak{
		Uid:   u.data.Uid,
		Rank:  rank,
		Score: u.GetScore(),
	}
	divisionInfo := u.GetDivision()
	if divisionInfo != nil {
		ret.Division = divisionInfo.Id
	}

	return ret
}

func (u *User) GetSid() uint64 {
	return u.data.Sid
}

func (u *User) ResetInNewRound(now int64, bigAreaServerIDM map[uint64]struct{}, openInfo *goxml.SeasonArenOpenInfo) {
	//没有快照的玩家视为异常直接删掉 每轮重置检查当前玩家的服务器是否还在本大战区下
	_, exist := bigAreaServerIDM[u.GetSid()]
	if u.GetUserSnapshot() == nil || !exist {
		u.userManager.DeleteUser(u.data.Uid)
		return
	}
	//小战区修正
	areaID := u.userManager.Manager.baseModule.GetNormalArea(u.GetSid())
	if areaID != 0 && areaID != u.GetAreaId() {
		u.SetAreaId(areaID)
	}

	divisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(u.GetScore(), u.GetUserLastRank())
	if divisionInfo != nil {
		u.data.Score = divisionInfo.ScoreReset
	}
	u.data.Round = openInfo.Round
	// 赛季更换清理战力相关内容
	if openInfo.SeasonID != u.data.Season {
		u.data.Snapshot.DefensePower = 0
		u.data.Snapshot.SeasonCrystalTop35Power = u.data.SeasonCrystalTop35Power
		u.data.SeasonCrystalTop35Power = 0
		u.data.SeasonRankIn = false
	}
	u.data.Season = openInfo.SeasonID
	u.data.Time = now
	u.data.IsSign = false
	u.data.Opponents = nil
	if u.GetUserSnapshot() != nil {
		u.userManager.buildRank(u)
	}
	u.userManager.SetUserChange(u)
}

func (u *User) GetUserSnapshot() *cl.UserSnapshot {
	return u.data.Snapshot
}

func (u *User) Sign() {
	if u.data.IsSign {
		return
	}
	u.data.IsSign = true
	u.data.Time = time.Now().Unix()
	u.userManager.SetUserChange(u)
}

func (u *User) BuildCrossUserBase() *cr.CrossSeasonArenaUserBase {
	return &cr.CrossSeasonArenaUserBase{
		Score:          u.data.Score,
		Rank:           u.GetRank(),
		LastRoundScore: u.data.LastRoundScore,
		LastRoundRank:  u.data.LastRoundRank,
	}
}

func (u *User) UserSnapshotClone() *cl.UserSnapshot {
	return u.data.Snapshot.Clone()
}

func (u *User) SetLastRoundRank(rank uint32) {
	u.data.LastRoundRank = rank
	u.userManager.SetUserChange(u)
}

func (u *User) SetLastRoundScore(score uint32) {
	u.data.LastRoundScore = score
	u.userManager.SetUserChange(u)
}

func (u *User) GetAreaId() uint32 {
	return u.data.AreaId
}

func (u *User) SetAreaId(arenID uint32) {
	u.data.AreaId = arenID
	u.userManager.SetUserChange(u)
}

func (u *User) CheckArea(arenID uint32) bool {
	return u.data.AreaId == arenID
}

func (u *User) SetSeasonRankInTrue() {
	u.data.SeasonRankIn = true
	u.userManager.SetUserChange(u)
}

func (u *User) SeasonRankIn() bool {
	return u.data.SeasonRankIn
}
