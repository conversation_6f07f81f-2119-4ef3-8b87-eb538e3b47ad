package worldboss

import (
	"app/cross/activity"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/r2c"

	"gitlab.qdream.com/kit/sea/time"
)

type UserBackManager struct {
	mgr       *Manager
	users     map[uint32]map[uint64]*User  // key: bossType key: uid
	roomIndex map[uint32]map[uint64]uint32 // key: uid key:bossType roomID
	// 排行榜
	partitionRanks map[uint32]map[uint32]*AccumulativeHurtRank // 战区累计伤害排行榜；key:bossType key：难度level
	roomRanks      map[uint32]map[uint32]*AccumulativeHurtRank // 房间累计伤害排行榜；key:bossType key：房间 uniqueID

	// 定时保存相关标识: incr: 递增数字，freq: 保存频率 lastSaveNano: 上次存储时间戳
	incr, freq, lastSaveNano int64
	changes                  map[uint32]map[uint64]*cr.WorldBossUserBack // key:bossType
}

func NewUserBackManager(mgr *Manager) *UserBackManager {
	u := &UserBackManager{
		mgr:            mgr,
		users:          make(map[uint32]map[uint64]*User),
		partitionRanks: make(map[uint32]map[uint32]*AccumulativeHurtRank, 3), //nolint:mnd
		roomRanks:      make(map[uint32]map[uint32]*AccumulativeHurtRank),
		changes:        make(map[uint32]map[uint64]*cr.WorldBossUserBack),
		roomIndex:      make(map[uint32]map[uint64]uint32),
		freq:           50, //nolint:mnd // 50秒保存一次
	}
	mgr.addModule(u)

	return u
}

func (u *UserBackManager) ConvertUser(userData *cr.WorldBossUserBack) *cr.WorldBossUser {
	return &cr.WorldBossUser{
		Uid:                   userData.Uid,
		Level:                 userData.Level,
		RoomUniqueId:          userData.RoomUniqueId,
		Sid:                   userData.Sid,
		AccumulativeHurtScore: userData.AccumulativeHurtScore,
		MaxHurt:               userData.MaxHurt,
		HurtCreateTime:        userData.HurtCreateTime,
		MaxHurtScore:          userData.MaxHurtScore,
		ReportId:              userData.ReportId,
		AccumulativeHurt:      userData.AccumulativeHurt,
		ServerOpenTime:        userData.ServerOpenTime,
	}
}

func (u *UserBackManager) load(load *r2c.R2C_WorldBossLoad) {
	for bossType, users := range load.TypeUsers {
		for uid, user := range users.Users {
			_, exist := u.users[bossType]
			if !exist {
				u.users[bossType] = make(map[uint64]*User)
			}
			memUser := u.ConvertUser(user)
			u.users[bossType][uid] = NewUser(memUser)
			u.roomIndex[bossType][uid] = memUser.RoomUniqueId
			u.buildPartitionRank(memUser, bossType)
			u.buildRoomRank(memUser, bossType)
		}
	}
}

func (u *UserBackManager) reset(baseModule activity.BaseModuler) {
	sysID := u.mgr.worldBoss.GetSysID()
	nextInfo := goxml.GetData().WorldbossInfoM.GetNextBossInfo(sysID)
	if nextInfo == nil {
		return
	}
	msg := &r2c.C2R_WorldBossClearUserBack{
		PartitionId: baseModule.Partition(),
		BossType:    nextInfo.BossType,
	}
	baseModule.SendCmdToDB(r2c.ID_MSG_C2R_WorldBossClearUserBack, msg)
}

func (u *UserBackManager) update(moduler activity.BaseModuler) {
	u.incr++
	if u.incr%u.freq == 0 {
		u.save(moduler)
	} else {
		u.checkSave(moduler)
	}
}

func (u *UserBackManager) checkSave(baseModule activity.BaseModuler) {
	if len(u.changes) >= activity.ActivitySaveNum {
		nowNano := time.Now().UnixNano()
		if nowNano-u.lastSaveNano >= activity.WorldBossSaveInterval {
			u.save(baseModule)
		}
	}
}

func (u *UserBackManager) save(baseModule activity.BaseModuler) {
	isSave := false
	msgs := make([]*r2c.C2R_WorldBossSaveUserBack, 0, 3)
	if len(u.changes) > 0 {
		for bossTypeID, users := range u.changes {
			isSave = true
			tmp := &r2c.C2R_WorldBossSaveUserBack{
				PartitionId: baseModule.Partition(),
				BossType:    bossTypeID,
			}
			for _, user := range users {
				tmp.UserBacks = append(tmp.UserBacks, user.Clone())
			}
		}
	}

	if isSave {
		u.lastSaveNano = time.Now().UnixNano()
		for _, msg := range msgs {
			baseModule.SendCmdToDB(r2c.ID_MSG_C2R_WorldBossSaveUserBack, msg)
		}
	}
}

func (u *UserBackManager) setChange(changeUser *User, bossType uint32) {
	_, exist := u.changes[bossType]
	if !exist {
		u.changes[bossType] = make(map[uint64]*cr.WorldBossUserBack)
	}
	u.changes[bossType][changeUser.data.Uid] = changeUser.GetBackUser()
}

func (u *UserBackManager) settle(baseModule activity.BaseModuler) {
	userM := u.mgr.userM
	worldBoss := u.mgr.worldBoss
	bossType := worldBoss.GetBossType()
	u.users[bossType] = make(map[uint64]*User)
	u.roomRanks[bossType] = make(map[uint32]*AccumulativeHurtRank)
	u.partitionRanks[bossType] = make(map[uint32]*AccumulativeHurtRank)
	u.users[bossType] = make(map[uint64]*User)
	for uid, memUser := range userM.users {
		_, exist := u.users[bossType]
		if !exist {
			u.users[bossType] = make(map[uint64]*User)
		}
		memUserClone := memUser.Clone()
		u.users[bossType][uid] = memUserClone
		_, exist = u.roomIndex[bossType]
		if !exist {
			u.roomIndex[bossType] = make(map[uint64]uint32)
		}
		u.roomIndex[bossType][uid] = memUserClone.GetUserData().RoomUniqueId
		u.buildPartitionRank(memUserClone.GetUserData(), bossType)
		u.buildRoomRank(memUserClone.GetUserData(), bossType)
		u.setChange(memUserClone, bossType)
	}

	u.save(baseModule)
}

func (u *UserBackManager) buildPartitionRank(user *cr.WorldBossUser, bossType uint32) {
	// 最高伤害积分为0，不入战区榜
	if user.MaxHurtScore == 0 {
		return
	}

	_, exist := u.partitionRanks[bossType]
	if !exist {
		u.partitionRanks[bossType] = make(map[uint32]*AccumulativeHurtRank)
	}

	rankIns, exist := u.partitionRanks[bossType][user.Level]
	if !exist {
		rankIns = newAccumulativeHurtRank(activity.WorldBossPartitionRankSaveNum)
		u.partitionRanks[bossType][user.Level] = rankIns
	}

	rankIns.Insert(user)
}

func (u *UserBackManager) buildRoomRank(user *cr.WorldBossUser, bossType uint32) {
	_, exist := u.roomRanks[bossType]
	if !exist {
		u.roomRanks[bossType] = make(map[uint32]*AccumulativeHurtRank)
	}
	rankIns, exist := u.roomRanks[bossType][user.RoomUniqueId]
	if !exist {
		rankIns = newAccumulativeHurtRank(activity.WorldBossRoomRankSaveNum)
		u.roomRanks[bossType][user.RoomUniqueId] = rankIns
	}
	rankIns.Insert(user)
}

func (u *UserBackManager) GetPartitionRankIns(level uint32, bossType uint32) *AccumulativeHurtRank {
	_, exist := u.partitionRanks[bossType]
	if !exist {
		u.partitionRanks[bossType] = make(map[uint32]*AccumulativeHurtRank)
	}
	_, exist = u.partitionRanks[bossType][level]
	if !exist {
		u.partitionRanks[bossType][level] = newAccumulativeHurtRank(activity.WorldBossPartitionRankSaveNum)
	}

	return u.partitionRanks[bossType][level]
}

func (u *UserBackManager) GetUser(uid uint64, bossTpe uint32) *User {
	return u.users[bossTpe][uid]
}

func (u *UserBackManager) GetRoomID(uid uint64, bossTpe uint32) uint32 {
	return u.roomIndex[bossTpe][uid]
}

func (u *UserBackManager) GetRoomRankIns(roomUniqueID uint32, bossType uint32) *AccumulativeHurtRank {
	_, exist := u.roomRanks[bossType]
	if !exist {
		u.roomRanks[bossType] = make(map[uint32]*AccumulativeHurtRank)
	}
	_, exist = u.roomRanks[bossType][roomUniqueID]
	if !exist {
		u.roomRanks[roomUniqueID][roomUniqueID] = newAccumulativeHurtRank(activity.WorldBossRoomRankSaveNum)
	}

	return u.roomRanks[bossType][roomUniqueID]
}

func (u *UserBackManager) GetAllPartitionRankIns(bossType uint32) map[uint32]*AccumulativeHurtRank {
	return u.partitionRanks[bossType]
}

func (u *UserBackManager) GetBossTypeLevel(uid uint64) map[uint32]uint32 {
	ret := make(map[uint32]uint32)
	for bossType, partitionRank := range u.partitionRanks {
		for level, rank := range partitionRank {
			_, item := rank.GetRankAndNode(uid)
			if item != nil {
				ret[bossType] = level
			}
		}
	}
	return ret
}
