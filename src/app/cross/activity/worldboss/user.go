package worldboss

import (
	"app/protos/in/cr"

	"gitlab.qdream.com/kit/sea/time"
)

type User struct {
	data *cr.WorldBossUser
}

func NewUser(userData *cr.WorldBossUser) *User {
	return &User{
		data: userData,
	}
}

func (u *User) GetUserData() *cr.WorldBossUser {
	return u.data
}

func (u *User) GetRoomUniqueID() uint32 {
	if u.data == nil {
		return 0
	}

	return u.data.RoomUniqueId
}

func (u *User) GetLevel() uint32 {
	if u.data == nil {
		return 0
	}

	return u.data.Level
}

func (u *User) UpdateMaxHurtAndScore(maxHurt, maxHurtScore uint64) bool {
	if u.data == nil {
		return false
	}
	isChange := false
	if maxHurtScore > u.data.MaxHurtScore || maxHurt > u.data.MaxHurt {
		u.data.MaxHurt = maxHurt
		u.data.MaxHurtScore = maxHurtScore
		u.data.HurtCreateTime = time.Now().Unix()
		isChange = true
	}

	return isChange
}

func (u *User) UpdateAccumulativeHurtAndScore(accumulativeHurt, accumulatedHurtScore uint64) bool {
	if u.data == nil {
		return false
	}
	isChange := false
	if accumulativeHurt > u.data.AccumulativeHurt || accumulatedHurtScore > u.data.AccumulativeHurtScore {
		u.data.AccumulativeHurt = accumulativeHurt
		u.data.AccumulativeHurtScore = accumulatedHurtScore
		isChange = true
	}

	return isChange
}

func (u *User) UpdateBattleReportID(reportID string) {
	if u.data == nil {
		return
	}
	u.data.ReportId = reportID
}

func (u *User) GetMaxHurtScore() uint64 {
	if u.data == nil {
		return 0
	}

	return u.data.MaxHurtScore
}

func (u *User) Clone() *User {
	if u.data == nil {
		return nil
	}
	return &User{
		data: u.data.Clone(),
	}
}

func (u *User) GetBackUser() *cr.WorldBossUserBack {
	return &cr.WorldBossUserBack{
		Uid:                   u.data.Uid,
		Level:                 u.data.Level,
		RoomUniqueId:          u.data.RoomUniqueId,
		Sid:                   u.data.Sid,
		AccumulativeHurt:      u.data.AccumulativeHurt,
		MaxHurt:               u.data.MaxHurt,
		HurtCreateTime:        u.data.HurtCreateTime,
		ReportId:              u.data.ReportId,
		AccumulativeHurtScore: u.data.AccumulativeHurtScore,
		ServerOpenTime:        u.data.ServerOpenTime,
		PokemonTowerFloor:     u.data.PokemonTowerFloor,
	}
}

func (u *User) UpdatePokemonFloor(pokemonFloor uint32) bool {
	if u.data.PokemonTowerFloor != pokemonFloor {
		u.data.PokemonTowerFloor = pokemonFloor
		return true
	}
	return false
}
