package worldboss

import (
	"app/protos/in/cr"
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/skiplist"
)

type (
	AccumulativeHurtRank struct {
		sl  *skiplist.Set
		max uint32 // 最大数量
		cmp skiplist.Comparatorer
	}
	AccumulativeHurtRankValue struct {
		Sid, Uid, AccumulativeHurt, AccumulativeHurtScore uint64
		HurtCreateTime, ServerOpenTime                    int64
		Level                                             uint32
		ReportID                                          string
		PeakTag                                           uint32 // 0: 正常玩家 1：巅峰选手 2：巅峰选手(开服时间不满足)
		MaxHurtScore                                      uint64 // 新增最大伤害积分，仅用于展示
		PokemonTowerFloor                                 uint32
	}
)

func newAccumulativeHurtRank(num uint32) *AccumulativeHurtRank {
	cmp := &AccumulativeHurtRankValueCmp{}
	return &AccumulativeHurtRank{
		sl:  skiplist.NewSet(cmp),
		max: num,
		cmp: cmp,
	}
}

func (a *AccumulativeHurtRank) Insert(u *cr.WorldBossUser) {
	a.update(newAccumulativeHurtRankValue(u))
}

//nolint:varnamelen
func (a *AccumulativeHurtRank) update(v skiplist.Valuer) {
	if old := a.sl.GetElement(v.Key()); old != nil {
		a.sl.Delete(old)
	} else {
		if a.sl.Length() >= a.max && a.sl.Tail() != nil && a.cmp.CmpScore(a.sl.Tail().Value(), v) < 0 {
			return
		}
	}
	a.sl.Insert(v)

	if a.sl.Length() > a.max {
		delNode := a.sl.Tail().Value()
		if a.cmp.CmpKey(v, delNode) == 0 {
			a.sl.Delete(v)
			return
		}
		del, ok := delNode.(*AccumulativeHurtRankValue)
		if ok {
			a.sl.Delete(del)
		}
	}
}

func (a *AccumulativeHurtRank) GetElementByRank(rank uint32) *cl.RankValue {
	node := a.sl.GetNodeByRank(rank)
	if node != nil {
		v, ok := node.Value().(*AccumulativeHurtRankValue)
		if !ok {
			return nil
		}

		return a.newRankValue(v)
	}

	return nil
}

func (a *AccumulativeHurtRank) getRangeByRank(start, end uint32) []interface{} {
	return a.sl.GetRangeByRank(start, end)
}

func (a *AccumulativeHurtRank) GetRankById(key uint64) uint32 {
	return a.sl.GetRank(key)
}

func (a *AccumulativeHurtRank) GetRankAndNode(key uint64) (uint32, *cl.RankValue) {
	rank, node := a.sl.GetRankAndNode(key)
	if node == nil {
		return rank, nil
	}

	v, ok := node.Value().(*AccumulativeHurtRankValue)
	if !ok {
		return rank, nil
	}

	return rank, a.newRankValue(v)
}

func (a *AccumulativeHurtRank) First() *cl.RankValue {
	node := a.sl.First()
	if node == nil {
		return nil
	}

	v, ok := node.Value().(*AccumulativeHurtRankValue)
	if !ok {
		return nil
	}

	return a.newRankValue(v)
}

func (a *AccumulativeHurtRank) Tail() *cl.RankValue {
	node := a.sl.Tail()
	if node == nil {
		return nil
	}
	v, ok := node.Value().(*AccumulativeHurtRankValue)
	if !ok {
		return nil
	}

	return a.newRankValue(v)
}

func (a *AccumulativeHurtRank) GetLength() uint32 {
	return a.sl.Length()
}

func (a *AccumulativeHurtRank) GetRangeByRank(start, end uint32) []*cl.RankValue {
	list := a.getRangeByRank(start, end)
	retList := make([]*cl.RankValue, 0, len(list))
	for _, v := range list {
		value, ok := v.(*AccumulativeHurtRankValue)
		if !ok {
			continue
		}

		retList = append(retList, a.newRankValue(value))
	}

	return retList
}

func (a *AccumulativeHurtRank) GetRange(start, end uint32) []*AccumulativeHurtRankValue {
	list := a.getRangeByRank(start, end)
	retList := make([]*AccumulativeHurtRankValue, 0, len(list))
	for _, v := range list {
		value, ok := v.(*AccumulativeHurtRankValue)
		if !ok {
			continue
		}

		retList = append(retList, value)
	}

	return retList
}

func (a *AccumulativeHurtRank) newRankValue(value *AccumulativeHurtRankValue) *cl.RankValue {
	return &cl.RankValue{
		Id:       value.Key(),
		Sid:      value.Sid,
		Value:    value.Score(),                      // 累计伤害积分
		ReportId: value.ReportID,                     // 战报id
		Param1:   value.AccumulativeHurt,             // 累计伤害
		Param2:   uint64(value.Level),                // 难度等级
		Param3:   uint64(a.GetRankById(value.Key())), // 排名
		Param4:   uint64(value.PeakTag),              // 玩家处于巅峰竞技场标识：0: 正常玩家 1：巅峰选手 2：巅峰选手(开服时间不满足)
		Param5:   value.MaxHurtScore,                 // 新增最大伤害积分，仅用于展示
		Param6:   uint64(value.PokemonTowerFloor),
	}
}

func (a *AccumulativeHurtRank) clearRank() {
	a.sl = skiplist.NewSet(&AccumulativeHurtRankValueCmp{})
}

func newAccumulativeHurtRankValue(u *cr.WorldBossUser) *AccumulativeHurtRankValue {
	return &AccumulativeHurtRankValue{
		Sid:                   u.Sid,
		Uid:                   u.Uid,
		HurtCreateTime:        u.HurtCreateTime,
		AccumulativeHurt:      u.AccumulativeHurt,
		AccumulativeHurtScore: u.AccumulativeHurtScore,
		Level:                 u.Level,
		ReportID:              u.ReportId,
		ServerOpenTime:        u.ServerOpenTime,
		MaxHurtScore:          u.MaxHurtScore,
		PokemonTowerFloor:     u.PokemonTowerFloor,
	}
}

func (a *AccumulativeHurtRankValue) Key() uint64 {
	return a.Uid
}

func (a *AccumulativeHurtRankValue) Score() uint64 {
	return a.AccumulativeHurtScore
}

type AccumulativeHurtRankValueCmp struct {
}

func (a *AccumulativeHurtRankValueCmp) CmpKey(v1 interface{}, v2 interface{}) int {
	s1 := v1.(*AccumulativeHurtRankValue).Key()
	s2 := v2.(*AccumulativeHurtRankValue).Key()

	if s1 < s2 {
		return -1
	} else if s1 == s2 {
		return 0
	} else {
		return 1
	}
}

// CmpScore 从大到小的顺序排列
// AccumulativeHurt 相同时，按照产生的时间先后顺序排名
func (a *AccumulativeHurtRankValueCmp) CmpScore(v1 interface{}, v2 interface{}) int {
	s1 := v1.(*AccumulativeHurtRankValue)
	s2 := v2.(*AccumulativeHurtRankValue)

	if s1.AccumulativeHurtScore == s2.AccumulativeHurtScore && s1.HurtCreateTime == s2.HurtCreateTime {
		return 0
	}
	if s1.AccumulativeHurtScore > s2.AccumulativeHurtScore || (s1.AccumulativeHurtScore == s2.AccumulativeHurtScore && s1.HurtCreateTime < s2.HurtCreateTime) {
		return -1
	}

	return 1
}
