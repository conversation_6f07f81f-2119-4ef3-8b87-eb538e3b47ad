package balancearena

import (
	"app/cross/activity/balancearena"
	"app/cross/command/redis/base"
	"app/protos/in/r2c"
	"context"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(r2c.ID_MSG_R2C_BalanceArenaLoad), &R2CBalanceArenaLoadCommand{}, state)
}

type R2CBalanceArenaLoadCommand struct {
	base.Command
}

func (r *R2CBalanceArenaLoadCommand) Execute(ctx context.Context) bool {
	l4g.Debugf("balanceArena.R2CBalanceArenaLoadCommand: %+v", r.Msg)
	BalanceArena := r.Mo<PERSON>le.(*balancearena.Manager)
	BalanceArena.Load(r.Msg.Data.(*r2c.R2C_BalanceArenaLoad))
	return true
}
