package db

import (
	"app/cross/command/db/balancearena"
	"app/cross/command/db/disorderland"
	"app/cross/command/db/gst"
	"app/cross/command/db/guild"
	"app/cross/command/db/hotrank"
	"app/cross/command/db/peak"
	"app/cross/command/db/seasonarena"
	"app/cross/command/db/seasoncompliance"
	"app/cross/command/db/seasonmap"
	"app/cross/command/db/worldboss"
	"context"

	"app/cross/activity"
	"app/cross/command/db/base"
	"app/cross/db"
	"app/protos/in/r2c"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(r2c.ID_MSG_C2R_Finish), &C2RFinishCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_WrestleSave), &C2RWrestleSaveCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_WrestleFighterBak), &C2RWrestleFighterBakCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_WrestleLoad), &C2RWrestleLoadCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_LoadActInfo), &C2RLoadActInfoCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_SaveActInfo), &C2RSaveActInfoCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_WrestleLoadFighters), &C2RWrestleLoadFightersCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_WrestleClean), &C2RWrestleCleanCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_SaveCommonRank), &C2RSaveCommonRankCommand{}, state)
	guild.Init(cmds, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_RankGet), &C2RRankGetCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_RankReset), &C2RRankResetCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_RankSave), &C2RRankSaveCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_RankGetLast), &C2RRankGetLastCommand{}, state)
	worldboss.Init(cmds, state)
	disorderland.Init(cmds, state)
	peak.Init(cmds, state)
	gst.Init(cmds, state)
	seasonarena.Init(cmds, state)
	hotrank.Init(cmds, state)
	seasoncompliance.Init(cmds, state)
	seasonmap.Init(cmds, state)
	balancearena.Init(cmds, state)
}

type C2RFinishCommand struct {
	base.Command
}

func (c *C2RFinishCommand) Execute(ctx context.Context) bool {
	l4g.Debugf("C2RFinishCommand redis actoc finish, activity:%d", c.Activity)
	data, ok := c.Msg.Data.(*db.SyncMsg)
	if !ok {
		l4g.Errorf("C2RFinishCommand parse msg error, msg:%v", c.Msg.Data)
		return false
	}

	data.RetData <- true
	l4g.Debugf("C2RFinishCommand send to cross")
	return true
}

type C2RWrestleSaveCommand struct {
	base.Command
}

func (c *C2RWrestleSaveCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*r2c.C2R_WrestleSave)
	if !ok {
		l4g.Errorf("C2R_WrestleSave parse msg error, msg:%v", c.Msg.Data)
		return false
	}
	l4g.Debugf("C2R_WrestleSave recv:%+v", data)

	c.RedisActor.SaveWrestle(data)
	return true
}

type C2RWrestleFighterBakCommand struct {
	base.Command
}

func (c *C2RWrestleFighterBakCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*activity.SyncMsg)
	if !ok {
		l4g.Errorf("C2R_WrestleFighterBak parse msg error, msg:%v", c.Msg.Data)
		return false
	}
	l4g.Debugf("C2R_WrestleFighterBak recv:%+v", data)

	c.RedisActor.BakWrestleFighter(data.SendData.(*r2c.C2R_WrestleFighterBak))
	data.RetData.(chan *r2c.R2C_WrestleFighterBak) <- &r2c.R2C_WrestleFighterBak{
		Ret: uint32(r2c.RET_OK),
	}
	return true
}

type C2RLoadActInfoCommand struct {
	base.Command
}

func (c *C2RLoadActInfoCommand) Execute(ctx context.Context) bool {
	data := c.RedisActor.LoadActInfo(c.Msg.Data.(*r2c.C2R_LoadActInfo))
	c.SendCmdToCross(r2c.ID_MSG_R2C_LoadActInfo, 0, data)
	l4g.Debugf("C2RLoadCommand send to cross: %+v", data)
	return true
}

type C2RSaveActInfoCommand struct {
	base.Command
}

func (c *C2RSaveActInfoCommand) Execute(ctx context.Context) bool {
	c.RedisActor.SaveActInfo(c.Msg.Data.(*r2c.C2R_SaveActInfo))
	return true
}

type C2RWrestleLoadCommand struct {
	base.Command
}

func (c *C2RWrestleLoadCommand) Execute(ctx context.Context) bool {
	data := c.RedisActor.LoadWrestle(c.Msg.Data.(*r2c.C2R_WrestleLoad))
	c.SendCmdToCross(r2c.ID_MSG_R2C_WrestleLoad, 0, data)
	l4g.Debugf("C2RWrestleLoadCommand send to cross: %+v", data)
	return true
}

type C2RWrestleLoadFightersCommand struct {
	base.Command
}

func (c *C2RWrestleLoadFightersCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*activity.SyncMsg)
	if !ok {
		l4g.Errorf("C2R_WrestleLoadFighters parse msg error, msg:%v", c.Msg.Data)
		return false
	}

	retData := c.RedisActor.LoadFighters(data.SendData.(*r2c.C2R_WrestleLoadFighters))
	data.RetData.(chan *r2c.R2C_WrestleLoadFighters) <- retData
	return true
}

type C2RWrestleCleanCommand struct {
	base.Command
}

func (c *C2RWrestleCleanCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*r2c.C2R_WrestleClean)
	if !ok {
		l4g.Errorf("C2R_WrestleClean parse msg error, msg:%v", c.Msg.Data)
		return false
	}
	l4g.Debugf("C2R_WrestleClean recv:%+v", data)

	// TOMO 这里的redisActor怎么去掉？
	c.RedisActor.CleanWrestle(data)
	return true
}

type C2RSaveCommonRankCommand struct {
	base.Command
}

func (c *C2RSaveCommonRankCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*r2c.C2R_SaveCommonRank)
	if !ok {
		l4g.Errorf("C2R_SaveCommonRank parse msg error, msg:%v", c.Msg.Data)
		return false
	}

	c.RedisActor.SaveCommonRank(data)
	return true
}

type C2RRankGetCommand struct {
	base.Command
}

func (c *C2RRankGetCommand) Execute(ctx context.Context) bool {
	data := c.RedisActor.RankGet(c.Msg.Data.(*r2c.C2R_RankGet))
	c.SendCmdToCross(r2c.ID_MSG_R2C_RankGet, 0, data)
	l4g.Debugf("C2RRankGet send to cross: %+v", data)
	return true
}

type C2RRankResetCommand struct {
	base.Command
}

func (c *C2RRankResetCommand) Execute(ctx context.Context) bool {
	data := c.RedisActor.RankReset(c.Msg.Data.(*r2c.C2R_RankReset))
	c.SendCmdToCross(r2c.ID_MSG_R2C_RankReset, 0, data)
	l4g.Debugf("C2RRankReset send to cross: %+v", data)
	return true
}

type C2RRankSaveCommand struct {
	base.Command
}

func (c *C2RRankSaveCommand) Execute(ctx context.Context) bool {
	c.RedisActor.RankSave(c.Msg.Data.(*r2c.C2R_RankSave))
	return true
}

type C2RRankGetLastCommand struct {
	base.Command
}

func (c *C2RRankGetLastCommand) Execute(ctx context.Context) bool {
	data := c.RedisActor.RankGetLast(c.Msg.Data.(*r2c.C2R_RankGetLast))
	c.SendCmdToCross(r2c.ID_MSG_R2C_RankGetLast, 0, data)
	l4g.Debugf("C2RRankGetLast send to cross: %+v", data)
	return true
}
