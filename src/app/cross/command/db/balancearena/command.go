package balancearena

import (
	"app/cross/command/db/base"
	"app/protos/in/r2c"
	"context"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(r2c.ID_MSG_C2R_BalanceArenaLoad), &C2RBalanceArenaLoadCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_BalanceArenaSave), &C2RBalanceArenaSaveCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_BalanceArenaClearRound), &C2RBalanceArenaClearRoundCommand{}, state)
	cmds.Register(uint32(r2c.ID_MSG_C2R_BalanceArenaClearSeason), &C2RBalanceArenaClearSeasonCommand{}, state)
}

type C2RBalanceArenaLoadCommand struct {
	base.Command
}

func (c *C2RBalanceArenaLoadCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*r2c.C2R_BalanceArenaLoad)
	if !ok {
		l4g.Errorf("C2R_BalanceArenaLoad parse msg error, msg:%v", c.Msg.Data)
		return false
	}

	retData := c.RedisActor.BalanceArenaLoad(data.Partition)
	l4g.Debugf("balanceArena.C2R_BalanceArenaLoad send to cross: %+v", retData)
	c.SendCmdToCross(r2c.ID_MSG_R2C_BalanceArenaLoad, 0, retData)
	return true
}

type C2RBalanceArenaSaveCommand struct {
	base.Command
}

func (c *C2RBalanceArenaSaveCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*r2c.C2R_BalanceArenaSave)
	if !ok {
		l4g.Errorf("C2R_BalanceArenaSave parse msg error, msg:%v", c.Msg.Data)
		return false
	}

	c.RedisActor.BalanceArenaSave(data)
	return true
}

type C2RBalanceArenaClearRoundCommand struct {
	base.Command
}

func (c *C2RBalanceArenaClearRoundCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*r2c.C2R_BalanceArenaClearRound)
	if !ok {
		l4g.Errorf("C2R_BalanceArenaClearRound parse msg error, msg:%v", c.Msg.Data)
		return false
	}

	c.RedisActor.BalanceArenaClearRound(data)
	return true
}

type C2RBalanceArenaClearSeasonCommand struct {
	base.Command
}

func (c *C2RBalanceArenaClearSeasonCommand) Execute(ctx context.Context) bool {
	data, ok := c.Msg.Data.(*r2c.C2R_BalanceArenaClearSeason)
	if !ok {
		l4g.Errorf("C2R_BalanceArenaClearSeason parse msg error, msg:%v", c.Msg.Data)
		return false
	}

	c.RedisActor.BalanceArenaClearSeason(data)
	return true
}
