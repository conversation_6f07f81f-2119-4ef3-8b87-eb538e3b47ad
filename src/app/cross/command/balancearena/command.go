package balancearena

import (
	"app/cross/activity/balancearena"
	"app/cross/command/base"
	"app/goxml"
	"app/protos/in/cr"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaGetData), &L2CSBalanceArenaGetDataCommand{}, state)               //获取公平竞技场信息
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaGetSta), &L2CSBalanceArenaGetStaCommand{}, state)                 //获取公平竞技场状态
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaGetMatches), &L2CSBalanceArenaGetMatchesCommand{}, state)         // 获取比赛数据
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaCardChoose), &L2CSBalanceArenaCardChooseCommand{}, state)         //抽牌
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaCardCustomize), &L2CSBalanceArenaCardCustomizeCommand{}, state)   //自选
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaTeamUp), &L2CSBalanceArenaTeamUpCommand{}, state)                 //组队
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaFight), &L2CSBalanceArenaFightCommand{}, state)                   // 战斗结果
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaRankSettle), &L2CSBalanceArenaRankSettleCommand{}, state)         // 排行结算
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaGetRankList), &L2CSBalanceArenaGetRankListCommand{}, state)       // 获取排行榜
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaReward), &L2CSBalanceArenaRewardCommand{}, state)                 // 发奖
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaGetUserMatches), &L2CSBalanceArenaGetUserMatchesCommand{}, state) // 获取其他玩家的淘汰赛数据
	cmds.Register(uint32(l2c.ID_MSG_L2CS_BalanceArenaTestSign), &L2CSBalanceArenaTestSignCommand{}, state)             //组队
}

type L2CSBalanceArenaGetDataCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaGetDataCommand) Error(msg *l2c.CS2L_BalanceArenaGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetData, msg)
	return false
}

func (c *L2CSBalanceArenaGetDataCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaGetData{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaGetData unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaGetData: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaGetData{
		Ret: uint32(ret.RET_OK),
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_MODULE_NOT_REGISTER))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaGetData. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	smsg.State = manager.GetStateManager().GetSta2Client()

	userM := manager.GetUserManager()
	seasonUser := userM.GetSeasonUser(c.Msg.UID)
	if seasonUser == nil { // 初始化
		newSeasonUser := &cr.BalanceArenaSeasonUser{
			Uid:      c.Msg.UID,
			Snapshot: msg.GetUserSnapshot(),
		}
		seasonUser = balancearena.NewSeasonUser(newSeasonUser, manager)
		userM.AddSeasonUser(seasonUser)
	}
	user := userM.GetUser(c.Msg.UID)
	if user == nil {
		newUser := &cr.BalanceArenaUser{
			Uid: c.Msg.UID,
		}
		user = balancearena.NewUser(newUser, seasonUser, manager)
		user.Init()
		userM.AddUser(user)
	}

	rankData := &cl.BalanceArenaRank{}
	smsg.User = user.FlushUser2Client()
	if user.GetBigGroupData() != nil {
		groupId := user.GetBigGroupData().GroupId
		smsg.BigGroup = groupId

		rankM := manager.GetRanksManager().GetBigGroupRankM(groupId)
		selfValue := rankM.GetShowValue(manager, user.GetUid(), goxml.BalanceArenaRankTypeBigGroup)
		if selfValue != nil {
			rankData.BigGroupRank = uint32(selfValue.GetParam1())
		}
	}
	if user.GetSmallGroupData() != nil {
		groupId := user.GetSmallGroupData().GroupId
		smsg.SmallGroup = groupId
		rankM := manager.GetRanksManager().GetSmallGroupRankM(groupId)
		selfValue := rankM.GetShowValue(manager, user.GetUid(), goxml.BalanceArenaRankTypeSmallGroup)
		if selfValue != nil {
			rankData.SmallGroupRank = uint32(selfValue.GetParam1())
		}
	}
	if user.GetEliminationData() != nil {
		smsg.EliminationGroup = user.GetEliminationData().GroupId
	}
	smsg.RankData = rankData.Clone()

	balanceArena := manager.GetData()
	if balanceArena != nil {
		if balanceArena.GetEliminationData() != nil && balanceArena.GetEliminationData().GetData() != nil {
			userIds := balanceArena.GetEliminationData().GetData().GetUserIds()
			if userIds != nil {
				smsg.EliminationReward = userIds[c.Msg.UID]
			}
		}
	}

	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetData, smsg)
	return true
}

type L2CSBalanceArenaGetMatchesCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaGetMatchesCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaGetMatchesCommand) Error(msg *l2c.CS2L_BalanceArenaGetMatches, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetMatches, msg)
	return false
}

func (c *L2CSBalanceArenaGetMatchesCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaGetMatches{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaGetMatches unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaGetMatches: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaGetMatches{
		Ret:         uint32(ret.RET_OK),
		PhaseType:   msg.PhaseType,
		BattleGroup: msg.BattleGroup,
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_MODULE_NOT_REGISTER))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaGetMatches. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	// 状态判断：未到比赛阶段直接返回
	stateM := manager.GetStateManager()
	if stateM.GetSta().Phase < uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH) {
		c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetMatches, smsg)
		return true
	}

	user := manager.GetUserManager().GetUser(c.Msg.UID)
	matchM := manager.GetMatchManager()
	switch msg.PhaseType {
	case uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH): // 大组赛
		if user != nil {
			bigMatches := matchM.FlushMatchesByIds(user.GetBigGroupData().GroupFights)
			smsg.Matches = append(smsg.Matches, bigMatches...)
		}
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH): // 小组赛
		if user != nil {
			smallMatches := matchM.FlushMatchesByIds(user.GetSmallGroupData().GroupFights)
			smsg.Matches = append(smsg.Matches, smallMatches...)
		}
	case uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_1),
		uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_2),
		uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_3): // 淘汰赛
		// TODO 根据淘汰赛分组获取比赛：msg.BattleGroup
		eliminationMatches := matchM.FlushAllEliminationMatches(msg.PhaseType)
		smsg.Matches = append(smsg.Matches, eliminationMatches...)
	}

	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetMatches, smsg)
	return true
}

type L2CSBalanceArenaGetStaCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaGetStaCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaGetStaCommand) Error(msg *l2c.CS2L_BalanceArenaGetSta, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetSta, msg)
	return false
}

func (c *L2CSBalanceArenaGetStaCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaGetSta{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaGetSta unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaGetSta: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaGetSta{
		Ret: uint32(ret.RET_OK),
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule balance arena manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_MODULE_NOT_REGISTER))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaGetSta. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	smsg.Sta = manager.GetStateManager().GetSta2Client()

	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetSta, smsg)
	return true
}

type L2CSBalanceArenaCardChooseCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaCardChooseCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaCardChooseCommand) Error(msg *l2c.CS2L_BalanceArenaCardChoose, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaCardChoose, msg)
	return false
}

func (c *L2CSBalanceArenaCardChooseCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaCardChoose{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaCardChoose unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaCardChoose: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaCardChoose{
		Ret:     uint32(ret.RET_OK),
		OpType:  msg.OpType,
		CardIds: msg.CardIds,
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_MODULE_NOT_REGISTER))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaCardChoose. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	// 状态判断：报名阶段才能选卡
	stateM := manager.GetStateManager()
	if stateM.GetSta().Phase != uint32(common.BALANCE_ARENA_STAGE_BAS_SIGN) {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaCardChoose. invalid state %d", c.Msg.UID, stateM.GetSta().Phase)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	userM := manager.GetUserManager()
	user := userM.GetUser(c.Msg.UID)
	if user == nil {
		l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: user not exist", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	// 获取上一次的抽卡轮次 1-7，7表示抽卡已结束
	lastDrawRound := user.GetData().DrawRound
	// 抽卡已结束
	if lastDrawRound >= goxml.BalanceArenaDrawRoundMax {
		l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: draw round max. drawRound:%d", c.Msg.UID, lastDrawRound)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	curDrawRound := lastDrawRound + 1

	// 根据抽卡轮次和仓库数据进行抽卡
	switch msg.OpType {
	case uint32(common.BALANCE_ARENA_CARD_OP_BACO_DRAW): // 第一次操作：直接抽英雄并返回
		if lastDrawRound > 0 {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: draw round max. drawRound:%d", c.Msg.UID, lastDrawRound)
			return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		randomHeroes, rRet := user.RandomDrawHeroes()
		if rRet != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: random draw heroes failed. ret:%d", c.Msg.UID, rRet)
			return c.Error(smsg, rRet)
		}
		// 在自己的抽牌前获取该卡的抽选结果
		nextHeroGroups := user.GetManager().GetData().GenerateDrawResult(randomHeroes)
		// 添加新的抽牌结果到牌池
		user.AddRandomDrawHeroes2Pool(randomHeroes)
		smsg.NextHeroGroups = nextHeroGroups

		smsg.AvailableHeroes = user.FlushAvailableHeroes()
		smsg.AvailableArtifacts = user.FlushAvailableArtifacts()
		smsg.DrawRound = curDrawRound

		user.SetDrawRound(curDrawRound)
		userM.SetChange(user)

		c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaCardChoose, smsg)
		return true
	case uint32(common.BALANCE_ARENA_CARD_OP_BACO_CHOOSE_AND_DRAW): // 后面操作都是选牌+下一次抽牌
		// 无选牌操作
		if uint32(len(msg.CardIds)) != goxml.BalanceArenaDrawGroupNum {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: cardGroup is 0. opType:%d", c.Msg.UID, msg.OpType)
			return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
	default:
		l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: opType error. opType:%d", c.Msg.UID, msg.OpType)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	// 选牌
	switch curDrawRound {
	case goxml.BalanceArenaDrawRoundHero1, goxml.BalanceArenaDrawRoundHero2, goxml.BalanceArenaDrawRoundHero3, goxml.BalanceArenaDrawRoundHero4: // 英雄轮
		chooseHeroes, dRet := user.ChooseDrawHeroResult(msg.CardIds)
		if dRet != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: choose draw hero result failed. ret:%d", c.Msg.UID, dRet)
			return c.Error(smsg, dRet)
		}
		if len(chooseHeroes) > 0 {
			user.GenerateDrawHeroes(chooseHeroes)
		}
	case goxml.BalanceArenaDrawRoundArtifact1, goxml.BalanceArenaDrawRoundArtifact2: // 神器轮
		if msg.OpType == uint32(common.BALANCE_ARENA_CARD_OP_BACO_CHOOSE_AND_DRAW) {
			chooseArtifacts, dRet := user.ChooseDrawArtifactResult(msg.CardIds)
			if dRet != uint32(ret.RET_OK) {
				l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: choose draw artifact result failed. ret:%d", c.Msg.UID, dRet)
				return c.Error(smsg, dRet)
			}
			if len(chooseArtifacts) > 0 {
				user.GenerateDrawArtifacts(chooseArtifacts)
			}
		}
	}

	// 下一轮抽牌
	switch curDrawRound {
	case goxml.BalanceArenaDrawRoundHero1, goxml.BalanceArenaDrawRoundArtifact1, goxml.BalanceArenaDrawRoundHero3: // 抽英雄
		randomHeroes, dRet := user.RandomDrawHeroes()
		if dRet != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: random draw heroes failed. ret:%d", c.Msg.UID, dRet)
			return c.Error(smsg, dRet)
		}
		// 在自己的抽牌前获取该卡的抽选结果
		nextHeroGroups := user.GetManager().GetData().GenerateDrawResult(randomHeroes)
		smsg.NextHeroGroups = nextHeroGroups
		// 添加新的抽牌结果到牌池
		user.AddRandomDrawHeroes2Pool(randomHeroes)
	case goxml.BalanceArenaDrawRoundHero2, goxml.BalanceArenaDrawRoundHero4: // 抽神器
		randomArtifacts, dRet := user.RandomDrawArtifacts()
		if dRet != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardChoose: random draw artifacts failed. ret:%d", c.Msg.UID, dRet)
			return c.Error(smsg, dRet)
		}
		// 在自己的抽牌前获取该卡的抽选结果
		nextArtifactGroups := user.GetManager().GetData().GenerateDrawResult(randomArtifacts)
		smsg.NextArtifactGroups = nextArtifactGroups
		// 添加新的抽牌结果到牌池
		user.AddRandomDrawArtifacts2Pool(randomArtifacts)
	}

	user.SetDrawRound(curDrawRound)
	userM.SetChange(user)

	smsg.DrawRound = curDrawRound
	smsg.AvailableHeroes = user.FlushAvailableHeroes()
	smsg.AvailableArtifacts = user.FlushAvailableArtifacts()

	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaCardChoose, smsg)
	return true
}

type L2CSBalanceArenaCardCustomizeCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaCardCustomizeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaCardCustomizeCommand) Error(msg *l2c.CS2L_BalanceArenaCardCustomize, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaCardCustomize, msg)
	return false
}

func (c *L2CSBalanceArenaCardCustomizeCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaCardCustomize{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaCardCustomize unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaCardCustomize: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaCardCustomize{
		Ret:      uint32(ret.RET_OK),
		CardType: msg.CardType,
		SysId:    msg.SysId,
		Hid:      msg.Hid,
	}

	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_MODULE_NOT_REGISTER))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaCardCustomize. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	// 状态判断：报名阶段才能自选
	stateM := manager.GetStateManager()
	if stateM.GetSta().Phase != uint32(common.BALANCE_ARENA_STAGE_BAS_SIGN) {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaCardCustomize. invalid state %d", c.Msg.UID, stateM.GetSta().Phase)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	userM := manager.GetUserManager()
	user := userM.GetUser(c.Msg.UID)
	if user == nil {
		l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardCustomize: user not exist", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	switch msg.CardType {
	case goxml.BalanceArenaCardTypeHero:
		// 是否能自选
		if cRet := user.CanCustomizeHero(msg.SysId); cRet != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardCustomize: cannot cutomize hero", c.Msg.UID)
			return c.Error(smsg, cRet)
		}
		// 自选英雄
		newCustomizeHero := msg.SysId
		user.GenerateCustomizeHeroes(newCustomizeHero, msg.Star, msg.Emblems)
		//user.DelCardInTeams(oldCustomizeHero, goxml.BalanceArenaCardTypeHero) // 如果队伍中有旧的自选英雄要删除
	case goxml.BalanceArenaCardTypeArtifact:
		// 是否能自选
		if cRet := user.CanCustomizeArtifact(msg.SysId); cRet != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardCustomize: cannot cutomize artifact", c.Msg.UID)
			return c.Error(smsg, cRet)
		}
		// 自选神器
		newCustomizeArtifact := msg.SysId
		user.GenerateCustomizeArtifacts(newCustomizeArtifact, msg.Star)
		//user.DelCardInTeams(oldCustomizeArtifact, goxml.BalanceArenaCardTypeArtifact) // 如果队伍中有旧的自选神器要删除
	default:
		l4g.Errorf("user: %d balanceArena.L2CS_BalanceArenaCardCustomize: cardType error. cardType:%d", c.Msg.UID, msg.CardType)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	userM.SetChange(user)

	smsg.AvailableHeroes = user.FlushAvailableHeroes()
	smsg.AvailableArtifacts = user.FlushAvailableArtifacts()

	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaCardCustomize, smsg)
	return true
}

type L2CSBalanceArenaTeamUpCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaTeamUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaTeamUpCommand) Error(msg *l2c.CS2L_BalanceArenaTeamUp, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaTeamUp, msg)
	return false
}

func (c *L2CSBalanceArenaTeamUpCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaTeamUp{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaTeamUp unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaTeamUp: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaTeamUp{
		Ret:    uint32(ret.RET_OK),
		OpType: msg.OpType,
		Teams:  msg.Teams,
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_MODULE_NOT_REGISTER))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CSBalanceArenaTeamUpCommand. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}
	// 状态判断：获取状态对应的是否可以组队状态
	stateM := manager.GetStateManager()
	state := stateM.GetSta()
	info := goxml.GetData().BalanceArenaPhaseInfoM.GetRecordById(state.GetPhaseConfigId())
	if info == nil {
		l4g.Errorf("user:%d balanceArena.L2CSBalanceArenaTeamUpCommand. info is nil. phaseConfigId %d", c.Msg.UID, state.GetPhaseConfigId())
		return c.Error(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	if info.IfSetFormation == 0 {
		l4g.Errorf("user:%d balanceArena.L2CSBalanceArenaTeamUpCommand. invalid state %+v", c.Msg.UID, state)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	userM := manager.GetUserManager()
	user := userM.GetUser(c.Msg.UID)
	if user == nil {
		l4g.Error("user:%d balanceArena.L2CSBalanceArenaTeamUpCommand: user is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	// 检查组队是否符合要求
	if cRet := user.CheckTeams(msg.Teams); cRet != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d balanceArena.L2CSBalanceArenaTeamUpCommand: check teams failed. ret:%d", c.Msg.UID, cRet)
		return c.Error(smsg, cRet)
	}

	userBigGroupData := user.GetBigGroupData()
	if userBigGroupData == nil || userBigGroupData.GroupId == 0 {
		groupId, groupPos := manager.GetData().GetBigGroupData().GroupAddUser(c.Msg.UID)
		l4g.Infof("balanceArena.L2CSBalanceArenaTeamUpCommand: GroupAddUser. uid:%d groupId:%d groupPos:%d", c.Msg.UID, groupId, groupPos)
		user.SetBigGroupData(&cr.BalanceArenaGroupData{
			GroupId:  groupId,
			GroupPos: groupPos,
		})
	}

	user.SetTeams(msg.Teams)
	user.SetSign()
	userM.SetChange(user)
	// 修改队伍时更新玩家的快照
	user.GetSeasonUser().UpdateSnapShot(msg.GetUserSnapshot())

	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaTeamUp, smsg)
	return true
}

type L2CSBalanceArenaFightCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaFightCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaFight{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("L2CS_BalanceArenaFight unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("L2CS_BalanceArenaFight: %+v", msg)
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return false
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaFight. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return false
	}
	// 状态判断： 战斗不加已确保战斗顺利完成
	manager.GetMatchManager().UpdateFightResult(msg.GetMatchId(), msg.GetAttackWin(), msg.GetReportId(), msg.GetAttackScore(), msg.GetDefenseScore())
	return true
}

type L2CSBalanceArenaRankSettleCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaRankSettleCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaRankSettle{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("L2CS_BalanceArenaRankSettle unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Infof("L2CS_BalanceArenaRankSettle: %+v", msg)
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d GetModule gst manager is nil", c.Msg.UID)
		return false
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaRankSettle. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return false
	}
	manager.FinishSettle(c.ServerID(), msg.State)
	return true
}

type L2CSBalanceArenaGetRankListCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaGetRankListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaGetRankListCommand) Error(msg *l2c.CS2L_BalanceArenaGetRankList, retCode uint32) bool {
	msg.Rsp.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetRankList, msg)
	return false
}

func (c *L2CSBalanceArenaGetRankListCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaGetRankList{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaGetRankList unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaGetRankList: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaGetRankList{
		Rsp: &cl.L2C_BalanceArenaGetRankList{
			Ret:    uint32(ret.RET_OK),
			RankId: msg.Req.RankId,
			Group:  msg.Req.Group,
		},
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	manager.GetRankList(c.Msg.UID, msg, smsg)
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetRankList, smsg)
	return true
}

type L2CSBalanceArenaRewardCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaRewardCommand) Error(msg *l2c.CS2L_BalanceArenaReward, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaReward, msg)
	return false
}

func (c *L2CSBalanceArenaRewardCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaReward{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaReward unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaReward: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaReward{}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaReward. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	smsg.Ret = manager.Reward(c.Msg.UID)
	if smsg.Ret == uint32(ret.RET_OK) {
		roundInfo := goxml.GetData().BalanceArenaInfoM.GetRecordById(manager.GetStateManager().GetSta().Round)
		if roundInfo == nil {
			l4g.Errorf("L2CSBalanceArenaRewardCommand: get roundInfo failed. round:%d", manager.GetStateManager().GetSta().Round)
			return c.Error(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		smsg.RewardId = roundInfo.InviteRewardGroupId
	}
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaReward, smsg)
	return true
}

type L2CSBalanceArenaGetUserMatchesCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaGetUserMatchesCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaGetUserMatchesCommand) Error(msg *l2c.CS2L_BalanceArenaGetUserMatches, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetUserMatches, msg)
	return false
}

func (c *L2CSBalanceArenaGetUserMatchesCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaGetUserMatches{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaGetUserMatches unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaGetUserMatches: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaGetUserMatches{
		Ret: uint32(ret.RET_OK),
		Uid: msg.GetUid(),
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaGetUserMatches. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	user := manager.GetUserManager().GetUser(msg.GetUid())
	if user == nil {
		l4g.Error("user:%d balanceArena.L2CS_BalanceArenaGetUserMatches: no target user. uid", msg.GetUid())
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	eliminationData := user.GetEliminationData()
	if eliminationData == nil {
		l4g.Error("user:%d balanceArena.L2CS_BalanceArenaGetUserMatches: eliminationData is nil. uid", msg.GetUid())
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	eliminationFights := eliminationData.GetGroupFights()

	matchM := manager.GetMatchManager()
	matches := matchM.FlushMatchesByIds(eliminationFights)
	smsg.Matches = append(smsg.Matches, matches...)

	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaGetUserMatches, smsg)
	return true
}

type L2CSBalanceArenaTestSignCommand struct {
	base.Command
}

func (c *L2CSBalanceArenaTestSignCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *L2CSBalanceArenaTestSignCommand) Error(msg *l2c.CS2L_BalanceArenaTestSign, retCode uint32) bool {
	msg.Ret = retCode
	c.ReturnCmdToLogic(l2c.ID_MSG_CS2L_BalanceArenaTestSign, msg)
	return false
}

func (c *L2CSBalanceArenaTestSignCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2CS_BalanceArenaTestSign{}
	if err := proto.Unmarshal(c.ProtoData, msg); err != nil {
		l4g.Errorf("balanceArena.L2CS_BalanceArenaTestSign unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("balanceArena.L2CS_BalanceArenaTestSign: %+v", msg)
	smsg := &l2c.CS2L_BalanceArenaTestSign{
		Ret: uint32(ret.RET_OK),
	}
	manager, ok := c.GetModule().(*balancearena.Manager)
	if !ok {
		l4g.Error("user:%d balanceArena.GetModule: manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	if !manager.CheckRunning() {
		l4g.Errorf("user:%d balanceArena.L2CS_BalanceArenaTestSign. manager not running, partition:%d, logicSid:%d msg:%+v", c.Msg.UID, c.Partition(), c.ServerID(), msg)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	userM := manager.GetUserManager()
	for _, signUser := range msg.SignUsers {
		if signUser == nil {
			continue
		}
		uid := signUser.GetId()
		balanceUser := userM.GetUser(uid)
		if balanceUser != nil {
			continue
		}
		seasonUser := userM.GetSeasonUser(uid)
		if seasonUser == nil { // 初始化
			newSeasonUser := &cr.BalanceArenaSeasonUser{
				Uid:      uid,
				Snapshot: signUser,
			}
			seasonUser = balancearena.NewSeasonUser(newSeasonUser, manager)
			userM.AddSeasonUser(seasonUser)
		}
		user := userM.GetUser(uid)
		if user == nil {
			newUser := &cr.BalanceArenaUser{
				Uid: uid,
			}
			user = balancearena.NewUser(newUser, seasonUser, manager)
			user.Init()
			userM.AddUser(user)
		}

		// 分组
		groupId, groupPos := manager.GetData().GetBigGroupData().GroupAddUser(uid)
		l4g.Infof("balanceArena.L2CSBalanceArenaTestSignCommand: GroupAddUser. uid:%d groupId:%d groupPos:%d", uid, groupId, groupPos)
		user.SetBigGroupData(&cr.BalanceArenaGroupData{
			GroupId:  groupId,
			GroupPos: groupPos,
		})

		// 设置初始阵容并报名
		teams := make([]*cl.BalanceArenaTeam, 0)
		availableHeroes := user.GetData().GetAvailableHeroes()
		for i := 0; i < 3; i++ {
			if i < len(availableHeroes) {
				heroes := availableHeroes[i]
				teams = append(teams, &cl.BalanceArenaTeam{
					Heroes: []*cl.BalanceArenaHero{heroes},
				})
			}
		}
		user.SetTeams(teams)
		user.SetSign()
		userM.SetChange(user)

		// 修改队伍时更新玩家的快照
		user.GetSeasonUser().UpdateSnapShot(signUser)
	}
	return true
}
