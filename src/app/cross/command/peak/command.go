package peak

import (
	ap "app/cross/activity/peak"
	"app/cross/command/base"
	"app/goxml"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakBaseData), &L2CPeakBaseDataCommand{}, state)                       //基本信息
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakGetMatch), &L2CPeakGetMatchCommand{}, state)                       //获取比赛数据
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakGuessList), &L2CPeakGuessListCommand{}, state)                     //获取竞猜列表数据
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakAddGuessCount), &L2CPeakAddGuessCountCommand{}, state)             //增加竞猜下注次数
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakResult), &L2CPeakResultCommand{}, state)                           //结算发奖
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakRankList), &L2CPeakRankListCommand{}, state)                       //排行榜
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakFight), &L2CPeakFightCommand{}, state)                             //战斗
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakFighterDetail), &L2CPeakFighterDetailCommand{}, state)             //选手完赛详情数据
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakPushState), &L2CPeakPushStateCommand{}, state)                     //推送状态更新
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakUpdatePlayer), &L2CPeakUpdatePlayerCommand{}, state)               //新周期开始，请求更新分数和参赛选手数据
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakGetSnapshot), &L2CPeakGetSnapshotCommand{}, state)                 //获取快照
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakRankFirst), &L2CPeakRankFirstCommand{}, state)                     //获取排行榜第一名
	cmds.Register(uint32(l2c.ID_MSG_L2C_PeakGetLastBattleReport), &L2CPeakGetLastBattleReportCommand{}, state) //获取上一场战报
}

type L2CPeakBaseDataCommand struct {
	base.Command
}

func (l *L2CPeakBaseDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakBaseDataCommand) Error(msg *l2c.C2L_PeakBaseData, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakBaseData, msg)
	return false
}

func (l *L2CPeakBaseDataCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakBaseData{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakBaseData unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakBaseData. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakBaseData{
		Ret: uint32(cret.RET_OK),
		Uid: msg.Uid,
	}

	if msg.Uid == 0 {
		l4g.Errorf("user:%d L2CPeakBaseData. param uid is 0, partition:%d, logicSid:%d",
			l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakBaseData. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakBaseData. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakBaseData. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	matchM := manager.GetMatchManager()
	if matchM == nil {
		l4g.Errorf("user:%d L2CPeakBaseData. no matchM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	peak := manager.GetPeak()
	if peak == nil {
		l4g.Errorf("user:%d L2CPeakBaseData. no peak, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	cmsg.Peak = peak.Flush()
	cmsg.MyMatches = matchM.FlushUserMatches(msg.Uid, false)
	cmsg.GuessMatch = matchM.FlushOneGuessMatch(msg.Round, msg.GuessMatchId)
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakBaseData, cmsg)
	l4g.Debugf("user:%d L2CPeakBaseData. send to logic: %+v", l.Msg.UID, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakGetMatchCommand struct {
	base.Command
}

func (l *L2CPeakGetMatchCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakGetMatchCommand) Error(msg *l2c.C2L_PeakGetMatch, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakGetMatch, msg)
	return false
}

func (l *L2CPeakGetMatchCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakGetMatch{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakGetMatch unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakGetMatch. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakGetMatch{
		Ret:    uint32(cret.RET_OK),
		Params: msg.Params,
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakGetMatch. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakGetMatch. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	matchM := manager.GetMatchManager()
	if matchM == nil {
		l4g.Errorf("user:%d L2CPeakGetMatch. no matchM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	cmsg.Matches = matchM.FlushMatches(cmsg.Params)
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakGetMatch, cmsg)
	l4g.Debugf("user:%d L2CPeakGetMatch. send to logic: %+v", l.Msg.UID, cmsg)

	//保存快照数据
	if msg.Snapshot != nil {
		fighterM := manager.GetFighterManager()
		if fighterM == nil {
			l4g.Errorf("user:%d L2CPeakGetMatch. no playerM, partition:%d, msg:%+v",
				l.Msg.UID, l.Partition(), msg)
		} else {
			updatePlayerSnapshot(fighterM, msg.Snapshot)
		}
	}
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakGuessListCommand struct {
	base.Command
}

func (l *L2CPeakGuessListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakGuessListCommand) Error(msg *l2c.C2L_PeakGuessList, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakGuessList, msg)
	return false
}

func (l *L2CPeakGuessListCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakGuessList{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakGuessList unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakGuessList. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakGuessList{
		Ret:    uint32(cret.RET_OK),
		Rounds: msg.Rounds,
	}

	roundCount := len(msg.Rounds)
	if roundCount == 0 || roundCount > len(goxml.PeakRounds) {
		l4g.Errorf("user:%d L2CPeakGuessList. param roundCount err, partition:%d, logicSid:%d, roundCount:%d",
			l.Msg.UID, l.Partition(), l.ServerID(), roundCount)
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	uniq := make(map[uint32]util.None)
	for _, round := range msg.Rounds {
		if !util.InUint32s(goxml.PeakRounds, round) {
			l4g.Errorf("user:%d L2CPeakGuessList. param roundCount err, partition:%d, logicSid:%d, rounds:%v",
				l.Msg.UID, l.Partition(), l.ServerID(), msg.Rounds)
			return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if _, exist := uniq[round]; exist {
			l4g.Errorf("user:%d L2CPeakGuessList. param round repeated, partition:%d, logicSid:%d, rounds:%v",
				l.Msg.UID, l.Partition(), l.ServerID(), msg.Rounds)
			return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		uniq[round] = util.None{}
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakGuessList. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakGuessList. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	matchM := manager.GetMatchManager()
	if matchM == nil {
		l4g.Errorf("user:%d L2CPeakGuessList. no matchM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	for _, round := range msg.Rounds {
		cmsg.Matches = append(cmsg.Matches, matchM.FlushGuessMatches(round)...)
	}
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakGuessList, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakAddGuessCountCommand struct {
	base.Command
}

func (l *L2CPeakAddGuessCountCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakAddGuessCountCommand) Error(msg *l2c.C2L_PeakAddGuessCount, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakAddGuessCount, msg)
	return false
}

func (l *L2CPeakAddGuessCountCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakAddGuessCount{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakAddGuessCount unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakAddGuessCount. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakAddGuessCount{
		Ret:     uint32(cret.RET_OK),
		MatchId: msg.MatchId,
		Count:   msg.Count,
		Uid:     msg.Uid,
	}

	if msg.MatchId == 0 || msg.Count == 0 {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. param err, partition:%d, logicSid:%d, MatchId:%d, Count:%d",
			l.Msg.UID, l.Partition(), l.ServerID(), msg.MatchId, msg.Count)
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !goxml.IsInPeakPhase(goxml.GetData()) {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. not open, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_PEAK_NOT_IN_PHASE))
	}

	peak := manager.GetPeak()
	if peak == nil {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. no peak, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	matchM := manager.GetMatchManager()
	if matchM == nil {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. no matchM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	match := matchM.GetMatch(msg.MatchId)
	if match == nil {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. no match, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_PEAK_MATCH_NOT_EXIST))
	}

	if match.GetRound() != peak.GetRound() {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. round err, partition:%d, msg:%+v, curRound:%d",
			l.Msg.UID, l.Partition(), msg, peak.GetRound())
		return l.Error(cmsg, uint32(cret.RET_PEAK_MATCH_ROUND_ERR))
	}

	var matchFighter *cl.PeakMatchFighter
	leftFighter := match.GetLeftFighter()
	rightFighter := match.GetRightFighter()
	if msg.Uid == leftFighter.Uid {
		matchFighter = leftFighter
	} else if msg.Uid == rightFighter.Uid {
		matchFighter = rightFighter
	}
	if matchFighter == nil {
		l4g.Errorf("user:%d L2CPeakAddGuessCount. no matchFighter, partition:%d, msg:%+v, leftUID:%d, rightUID:%d",
			l.Msg.UID, l.Partition(), msg, leftFighter.Uid, rightFighter.Uid)
		return l.Error(cmsg, uint32(cret.RET_PEAK_GURSS_UID_NOT_IN_MATCH))
	}

	matchFighter.GuessCount += cmsg.Count
	matchM.SetChange(match)

	cmsg.Group = match.GetGroup()
	cmsg.Round = match.GetRound()
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakAddGuessCount, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakResultCommand struct {
	base.Command
}

func (l *L2CPeakResultCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakResultCommand) Error(msg *l2c.C2L_PeakResult, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakResult, msg)
	return false
}

func (l *L2CPeakResultCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakResult{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakResult unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakResult. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakResult{
		Ret:   uint32(cret.RET_OK),
		Round: msg.Round,
	}

	if msg.Round == 0 || msg.Round > goxml.GetPeakLastRound() {
		l4g.Errorf("user:%d L2CPeakResult. param err, partition:%d, logicSid:%d, Round:%d",
			l.Msg.UID, l.Partition(), l.ServerID(), msg.Round)
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakResult. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakResult. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	peak := manager.GetPeak()
	if peak == nil {
		l4g.Errorf("user:%d L2CPeakResult. no peak, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	if msg.Round > peak.GetRound() {
		l4g.Errorf("user:%d L2CPeakResult. param round err, partition:%d, logicSid:%d, round:%d, curRound:%d",
			l.Msg.UID, l.Partition(), l.ServerID(), msg.Round, peak.GetRound())
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	matchM := manager.GetMatchManager()
	if matchM == nil {
		l4g.Errorf("user:%d L2CPeakResult. no matchM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakResult. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	info := goxml.GetData().PeakInfoM.GetBySeason(peak.GetSeason())
	if info == nil {
		l4g.Errorf("user:%d L2CPeakResult. no info, season:%d",
			l.Msg.UID, peak.GetSeason())
		return l.Error(cmsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	seasonTop1 := fighterM.GetSeasonTop1()
	cmsg.NewTop1 = seasonTop1

	//赛季最后一轮，等价于赛季结束，需要获取赛季排行数据
	if goxml.IsLastRoundToSeason(peak.GetPhase(), msg.Round) {
		cmsg.SeasonRanks = fighterM.MakeSeasonRankListBySid(l.ServerID())
		cmsg.SeasonTop1 = seasonTop1
	}

	if msg.Round == goxml.GetPeakLastRound() {
		cmsg.PhaseTop1 = fighterM.GetPhaseTop1()
	}

	cmsg.RoundRanks = manager.MakeRoundFinishListBySid(msg.Round, l.ServerID())
	cmsg.GuessMatches = matchM.GetGuessMatchesResult(msg.Round)
	cmsg.Time = goxml.GetData().PeakConfigInfoM.GetRoundStartTime(peak.GetSeason(), peak.GetPhase(), cmsg.Round)
	cmsg.Scores = fighterM.FlushSeasonFighterRankScore()
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakResult, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakRankListCommand struct {
	base.Command
}

func (l *L2CPeakRankListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakRankListCommand) Error(msg *l2c.C2L_PeakRankList, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakRankList, msg)
	return false
}

func (l *L2CPeakRankListCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakRankList{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakRankList unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakRankList. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakRankList{
		Ret:       uint32(cret.RET_OK),
		Type:      msg.Type,
		StartRank: msg.StartRank,
		EndRank:   msg.EndRank,
	}

	if !goxml.IsLegalPeakRankType(msg.Type) {
		l4g.Errorf("user:%d L2CPeakRankList. param err, partition:%d, logicSid:%d, Type:%d",
			l.Msg.UID, l.Partition(), l.ServerID(), msg.Type)
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakRankList. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakRankList. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakRankList. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	peak := manager.GetPeak()
	if peak == nil {
		l4g.Errorf("user:%d L2CPeakRankList. no peak, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	if msg.Type == uint32(common.PEAK_RANK_TYPE_PRT_PHASE_TOP8) {
		cmsg.List = peak.FlushTop8()
	} else {
		var startRank, endRank uint32
		if msg.Type == uint32(common.PEAK_RANK_TYPE_PRT_SEASON_TOP3) {
			startRank = 1
			endRank = 3
		} else {
			startRank = msg.StartRank
			endRank = msg.EndRank
		}

		totalCount := fighterM.GetSeasonRankListCount()
		if totalCount == 0 || startRank > totalCount {
			l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakRankList, cmsg)
			return l.ResultOK(cmsg.Ret)
		}

		//修正数据
		if endRank > totalCount {
			endRank = totalCount
		}
		if endRank < startRank {
			endRank = startRank
		}
		cmsg.List = fighterM.FlushSeasonRankList(startRank, endRank)
	}

	if msg.Type == uint32(common.PEAK_RANK_TYPE_PRT_SEASON_ALL) {
		cmsg.Self = fighterM.FlushSelfRank(l.Msg.UID)
	}
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakRankList, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakFightCommand struct {
	base.Command
}

func (l *L2CPeakFightCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakFight{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakFight unmarshal error: uid:%d, err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakFight. recv from logic, msg:%+v", l.Msg.UID, msg)

	//跨服返回错误
	if msg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d L2CPeakFight: recv logic info error, ret:%d", l.Msg.UID, msg.Ret)
		return false
	}

	if msg.LeftSnapshot == nil || msg.RightSnapshot == nil || msg.ReportId == "" {
		l4g.Errorf("user:%d L2CPeakFight. param error, msg:%+v", l.Msg.UID, msg)
		return false
	}

	if msg.LeftWin && msg.LeftScore <= msg.RightScore {
		l4g.Errorf("user:%d L2CPeakFight. param score error, msg:%+v", l.Msg.UID, msg)
		return false
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakFight. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakFight. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakFight. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	leftFighter := fighterM.GetFighter(msg.LeftSnapshot.Id)
	if leftFighter == nil {
		l4g.Errorf("user:%d L2CPeakFight. no leftFighter, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}
	rightFighter := fighterM.GetFighter(msg.RightSnapshot.Id)
	if rightFighter == nil {
		l4g.Errorf("user:%d L2CPeakFight. no rightFighter, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	matchM := manager.GetMatchManager()
	if matchM == nil {
		l4g.Errorf("user:%d L2CPeakFight. no matchM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	match := matchM.GetMatch(msg.MatchId)
	if match == nil {
		l4g.Errorf("user:%d L2CPeakFight. no match, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	//正常报错，防止数据被重复处理
	if match.IsFinished() {
		l4g.Errorf("user:%d L2CPeakFight. already finish, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	uids := match.GetFighterIDs()
	if !util.InUint64s(uids, msg.LeftSnapshot.Id) {
		l4g.Errorf("user:%d L2CPeakFight. left uid not in match, uids:%v, msg:%+v",
			l.Msg.UID, uids, msg)
		return false
	}
	if !util.InUint64s(uids, msg.RightSnapshot.Id) {
		l4g.Errorf("user:%d L2CPeakFight. right uid not in match, uids:%v, msg:%+v",
			l.Msg.UID, uids, msg)
		return false
	}

	//更新数据
	manager.UpdateFightResult(msg)
	return true
}

type L2CPeakFighterDetailCommand struct {
	base.Command
}

func (l *L2CPeakFighterDetailCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakFighterDetailCommand) Error(msg *l2c.C2L_PeakFighterDetail, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakFighterDetail, msg)
	return false
}

func (l *L2CPeakFighterDetailCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakFighterDetail{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakFighterDetail unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakFighterDetail. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakFighterDetail{
		Ret: uint32(cret.RET_OK),
		Uid: msg.Uid,
	}

	if msg.Uid == 0 {
		l4g.Errorf("user:%d L2CPeakFighterDetail. param uid is 0, partition:%d, logicSid:%d",
			l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakFighterDetail. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakFighterDetail. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakFighterDetail. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighter := fighterM.GetFighter(msg.Uid)
	if fighter == nil {
		l4g.Errorf("user:%d L2CPeakFighterDetail. no fighter, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_PEAK_NO_FIGHTER))
	}

	matchM := manager.GetMatchManager()
	if matchM == nil {
		l4g.Errorf("user:%d L2CPeakFighterDetail. no matchM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	peak := manager.GetPeak()
	if peak == nil {
		l4g.Errorf("user:%d L2CPeakFighterDetail. no peak, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	if !fighter.IsInCurrentPhase(peak.GetPhase()) {
		l4g.Errorf("user:%d L2CPeakFighterDetail. fighter not in current phase, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_PEAK_NO_FIGHTER))
	}

	cmsg.Matches = matchM.FlushUserMatches(cmsg.Uid, true)
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakFighterDetail, cmsg)
	l4g.Debugf("user:%d L2CPeakFighterDetail. send to logic: %+v", l.Msg.UID, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakPushStateCommand struct {
	base.Command
}

func (l *L2CPeakPushStateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakPushStateCommand) Error(msg *l2c.C2L_PeakPushState, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakPushState, msg)
	return false
}

func (l *L2CPeakPushStateCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakPushState{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakPushState unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakPushState. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakPushState{
		Ret: uint32(cret.RET_OK),
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakPushState. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakPushState. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	peak := manager.GetPeak()
	if peak == nil {
		l4g.Errorf("user:%d L2CPeakPushState. no peak, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakPushState. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	cmsg.State = peak.FlushState()
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakPushState, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakUpdatePlayerCommand struct {
	base.Command
}

func (l *L2CPeakUpdatePlayerCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakUpdatePlayerCommand) Error(msg *l2c.C2L_PeakUpdatePlayer, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakUpdatePlayer, msg)
	return false
}

func (l *L2CPeakUpdatePlayerCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakUpdatePlayer{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakUpdatePlayer unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakUpdatePlayer. recv from logic: logicSid:%d, msg:%+v", l.Msg.UID, l.ServerID(), msg)

	cmsg := &l2c.C2L_PeakUpdatePlayer{
		Ret: uint32(cret.RET_OK),
	}

	if msg.Phase == 0 {
		l4g.Errorf("user:%d L2CPeakUpdatePlayer. param err, partition:%d, logicSid:%d, param:%+v",
			l.Msg.UID, l.Partition(), l.ServerID(), msg)
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakUpdatePlayer. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakUpdatePlayer. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	peak := manager.GetPeak()
	if peak == nil {
		l4g.Errorf("user:%d L2CPeakUpdatePlayer. no peak, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	if msg.Phase != peak.GetPhase() {
		l4g.Errorf("user:%d L2CPeakUpdatePlayer. param phase err, partition:%d, logicSid:%d, phase:%d, curPhase:%d",
			l.Msg.UID, l.Partition(), l.ServerID(), msg.Phase, peak.GetPhase())
		return l.Error(cmsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakUpdatePlayer. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	cmsg.State = peak.FlushState()
	cmsg.Scores = fighterM.FlushSeasonFighterRankScore()
	cmsg.Uids = fighterM.GetPhasePlayerUidsBySid(l.ServerID())
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakUpdatePlayer, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakGetSnapshotCommand struct {
	base.Command
}

func (l *L2CPeakGetSnapshotCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakGetSnapshot{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakGetSnapshot unmarshal error: uid:%d, err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakGetSnapshot. recv from logic, msg:%+v", l.Msg.UID, msg)

	//跨服返回错误
	if msg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d L2CPeakGetSnapshot: recv logic info error, ret:%d", l.Msg.UID, msg.Ret)
		return false
	}

	if len(msg.Snapshots) == 0 {
		l4g.Errorf("user:%d L2CPeakGetSnapshot. no snapshot", l.Msg.UID)
		return false
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakGetSnapshot. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakGetSnapshot. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakGetSnapshot. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return false
	}

	//更新数据
	for _, v := range msg.Snapshots {
		updatePlayerSnapshot(fighterM, v)
	}
	return true
}

// 更新选手快照
func updatePlayerSnapshot(fighterM *ap.FighterManager, snapshot *cl.UserSnapshot) {
	fighter := fighterM.GetFighter(snapshot.Id)
	if fighter != nil {
		fighterM.UpdateSnapshot(fighter, snapshot)
	}
}

type L2CPeakRankFirstCommand struct {
	base.Command
}

func (l *L2CPeakRankFirstCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakRankFirstCommand) Error(msg *l2c.C2L_PeakRankFirst, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakRankFirst, msg)
	return false
}

func (l *L2CPeakRankFirstCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakRankFirst{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakRankFirst unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakRankFirst. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakRankFirst{
		Ret:         uint32(cret.RET_OK),
		ClientMsgId: msg.ClientMsgId,
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakRankFirst. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakRankFirst. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakRankList. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_ERROR))
	}

	totalCount := fighterM.GetSeasonRankListCount()
	if totalCount >= 1 {
		list := fighterM.FlushSeasonRankList(1, 1)
		if len(list) > 0 {
			cmsg.Value = list[0]
		}
	}
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakRankFirst, cmsg)
	return l.ResultOK(cmsg.Ret)
}

type L2CPeakGetLastBattleReportCommand struct {
	base.Command
}

func (l *L2CPeakGetLastBattleReportCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CPeakGetLastBattleReportCommand) Error(msg *l2c.C2L_PeakGetLastBattleReport, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakGetLastBattleReport, msg)
	return false
}

func (l *L2CPeakGetLastBattleReportCommand) Execute(ctx context.Context) bool {
	msg := &l2c.L2C_PeakGetLastBattleReport{}
	if err := proto.Unmarshal(l.ProtoData, msg); err != nil {
		l4g.Errorf("L2CPeakGetLastBattleReport unmarshal error: uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2CPeakGetLastBattleReport. recv from logic:%+v", l.Msg.UID, msg)

	cmsg := &l2c.C2L_PeakGetLastBattleReport{
		Ret: uint32(cret.RET_OK),
		Uid: msg.Uid,
	}

	manager, ok := l.GetModule().(*ap.Manager)
	if !ok {
		l4g.Errorf("user:%d L2CPeakGetLastBattleReport. get manager error, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	if !manager.CheckRunning() {
		l4g.Errorf("user:%d L2CPeakGetLastBattleReport. not running, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighterM := manager.GetFighterManager()
	if fighterM == nil {
		l4g.Errorf("user:%d L2CPeakGetLastBattleReport. no fighterM, partition:%d, msg:%+v",
			l.Msg.UID, l.Partition(), msg)
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	fighter := fighterM.GetFighter(msg.Uid)
	if fighter == nil {
		l4g.Errorf("user:%d L2CPeakGetLastBattleReport. no fighter, partition:%d",
			l.Msg.UID, l.Partition())
		return l.Error(cmsg, uint32(cret.RET_PEAK_NO_FIGHTER))
	}

	lastBattleReportId := fighter.GetLastBattleReport()
	if lastBattleReportId == "" {
		l4g.Infof("user:%d L2CPeakGetLastBattleReport. no last battle report, partition:%d",
			l.Msg.UID, l.Partition())
		return l.Error(cmsg, uint32(cret.RET_FORMATION_NOT_EXIST))
	}

	cmsg.ReportId = lastBattleReportId
	cmsg.IsAttacker = fighter.GetIsAttacker()

	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_PeakGetLastBattleReport, cmsg)
	return l.ResultOK(cmsg.Ret)
}
