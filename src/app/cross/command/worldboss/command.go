package worldboss

import (
	"app/cross/activity"
	w "app/cross/activity/worldboss"
	"app/cross/command/base"
	"app/goxml"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_L2C_WorldBossMatchRoom), &L2CWorldBossMatchRoomCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_L2C_WorldBossRoomInfo), &L2CWorldBossRoomInfoCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_L2C_WorldBossGetRoomLog), &L2CWorldBossGetRoomLogCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_L2C_WorldBossRank), &L2CWorldBossRankCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_L2C_WorldBossSyncFightData), &L2CWorldBossSyncFightDataCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_L2C_WorldBossSettle), &L2CWorldBossSettleCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_L2C_WorldBossGetData), &L2CWorldBossGetDataCommand{}, state)
}

type L2CWorldBossMatchRoomCommand struct {
	base.Command
}

func (l *L2CWorldBossMatchRoomCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CWorldBossMatchRoomCommand) Error(msg *l2c.C2L_WorldBossMatchRoom, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossMatchRoom, msg)
	return false
}

func (l *L2CWorldBossMatchRoomCommand) Execute(ctx context.Context) bool {
	lmsg := &l2c.L2C_WorldBossMatchRoom{}
	if err := proto.Unmarshal(l.ProtoData, lmsg); err != nil {
		l4g.Errorf("L2C_WorldBossMatchRoom unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2C_WorldBossMatchRoom: recv from logic:%+v", l.Msg.UID, lmsg)

	cmsg := &l2c.C2L_WorldBossMatchRoom{
		Ret:    uint32(cret.RET_OK),
		WSysId: lmsg.SysId,
		Level:  lmsg.Level,
	}

	manager, ok := l.GetModule().(*w.Manager)
	if !ok {
		l4g.Errorf("user:%d L2C_WorldBossMatchRoom: get worldBoss err, partitionID:%d, sid:%d", l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	worldBoss, userM, roomM := manager.GetWorldBoss(), manager.GetUserManager(), manager.GetRoomManager()

	isOpen := goxml.GetData().WorldbossInfoM.IsOpen(worldBoss.GetSysID())
	if !isOpen || worldBoss.GetSysID() != lmsg.SysId {
		l4g.Errorf("user:%d L2C_WorldBossMatchRoom: worldBoss not open. curSysID: %d clientID: %d", l.Msg.UID, worldBoss.GetSysID(), lmsg.SysId)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_OPEN))
	}
	difficultyInfo := goxml.GetData().WorldbossDifficultyInfoM.Index(lmsg.Level)
	if difficultyInfo == nil {
		l4g.Errorf("user:%d L2C_WorldBossMatchRoom: difficultyInfo is nil. level: %d", l.Msg.UID, lmsg.Level)
		return l.Error(cmsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 可能存在cross创建成功，但同步游戏服失败的情况，所以user不为空时直接返回成功
	user := userM.GetUser(l.Msg.UID)
	if user != nil {
		cmsg.WUniqueId = worldBoss.GetUniqueID()
		cmsg.RoomUniqueId = user.GetRoomUniqueID()
		room := roomM.GetRoom(user.GetRoomUniqueID())
		if room != nil {
			cmsg.RoomSysId = room.GetSysID()
		}
		l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossMatchRoom, cmsg)

		return l.ResultOK(cmsg.Ret)
	}

	worldBoss.UpdateWorldBoss(lmsg.Level, difficultyInfo.RoomId)

	// 房间相关操作
	roomUniqueID := worldBoss.CalcRoomUniqueID(lmsg.Level, l.Partition())
	roomSysID := worldBoss.GetCurRoomIndex(lmsg.Level)
	room := roomM.GetRoom(roomUniqueID)
	log := &cl.WorldBossRoomLog{
		Uid:        l.Msg.UID,
		LogType:    uint32(common.WORLD_BOSS_LOG_TYPE_WBLT_JOIN_ROOM),
		CreateTime: time.Now().Unix(),
		Params:     []string{lmsg.PlayerName},
	}

	if room == nil {
		room = roomM.AddRoom(lmsg.Level, roomUniqueID, roomSysID, log)
	} else {
		room.AddLog(log)
	}

	userM.AddUser(room.GetUniqueID(), lmsg.Level, l.Msg.UID, l.ServerID(), lmsg.ServerOpenTime)

	cmsg.WUniqueId = worldBoss.GetUniqueID()
	cmsg.RoomUniqueId = roomUniqueID
	cmsg.RoomSysId = roomSysID
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossMatchRoom, cmsg)

	return l.ResultOK(cmsg.Ret)
}

type L2CWorldBossRoomInfoCommand struct {
	base.Command
}

func (l *L2CWorldBossRoomInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CWorldBossRoomInfoCommand) Error(msg *l2c.C2L_WorldBossRoomInfo, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossRoomInfo, msg)
	return false
}

func (l *L2CWorldBossRoomInfoCommand) Execute(ctx context.Context) bool {
	lmsg := &l2c.L2C_WorldBossRoomInfo{}
	if err := proto.Unmarshal(l.ProtoData, lmsg); err != nil {
		l4g.Errorf("L2C_WorldBossRoomInfo unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2C_WorldBossRoomInfo. recv from logic:%+v", l.Msg.UID, lmsg)

	cmsg := &l2c.C2L_WorldBossRoomInfo{
		Ret: uint32(cret.RET_OK),
	}

	manager, ok := l.GetModule().(*w.Manager)
	if !ok {
		l4g.Errorf("user:%d L2C_WorldBossRoomInfo: get worldBoss err, partitionID:%d, sid:%d", l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}
	worldBoss, userM, roomM := manager.GetWorldBoss(), manager.GetUserManager(), manager.GetRoomManager()

	isOpen := goxml.GetData().WorldbossInfoM.IsOpen(worldBoss.GetSysID())
	if !isOpen || worldBoss.GetSysID() != lmsg.ActivityId {
		l4g.Errorf("user:%d L2C_WorldBossRoomInfo: worldBoss not open. sysID: %d", l.Msg.UID, lmsg.ActivityId)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_OPEN))
	}

	user := userM.GetUser(l.Msg.UID)
	if user == nil {
		l4g.Errorf("user:%d L2C_WorldBossRoomInfo: user not exist, not match room.", l.Msg.UID)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
	}

	uniqueID := user.GetRoomUniqueID()
	room := roomM.GetRoom(uniqueID)
	if room == nil {
		l4g.Errorf("user:%d L2C_WorldBossRoomInfo: not match room. uniqueID: %d", l.Msg.UID, uniqueID)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
	}
	pRankIns := userM.GetPartitionRankIns(user.GetLevel())
	cmsg.PartitionRank = pRankIns.GetRankById(l.Msg.UID)
	rRankIns := userM.GetRoomRankIns(uniqueID)

	userData := user.GetUserData()
	// 预防战斗同步数据失败，导致排行榜中的伤害和玩家身上的不一致
	if user.UpdateMaxHurtAndScore(lmsg.MaxHurt, lmsg.MaxHurtScore) {
		userM.SetChange(userData)
	}
	if user.UpdateAccumulativeHurtAndScore(lmsg.AccumulativeHurt, lmsg.AccumulativeHurtScore) {
		rRankIns.Insert(userData)
		pRankIns.Insert(userData)
		userM.SetChange(userData)
	}

	cmsg.Logs = room.GetLogs(activity.WorldBossRoomLogRetClientDefaultNum)
	cmsg.Users = room.GetUsersFromRank(l.Msg.UID, uniqueID, manager)
	cmsg.List, cmsg.RoomTop = retRank(l.Msg.UID, uniqueID, userM)
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossRoomInfo, cmsg)

	return l.ResultOK(cmsg.Ret)
}

func retRank(uid uint64, roomUniqueID uint32, userM *w.UserManager) (list []*cl.RankValue, roomTop *cl.RankValue) {
	rankIns := userM.GetRoomRankIns(roomUniqueID)
	// 房间榜
	rank, node := rankIns.GetRankAndNode(uid)
	// 未上榜，获取榜单最后一名
	if rank == 0 {
		v := rankIns.Tail()
		if v != nil {
			list = append(list, v)
		}
	} else if rank == 1 {
		if node != nil {
			list = append(list, node)
		}
		v := rankIns.GetElementByRank(rank + 1)
		if v != nil {
			list = append(list, v)
		}
	} else {
		list = append(list, rankIns.GetRangeByRank(rank-1, rank+1)...)
	}

	// rank top
	roomTop = rankIns.First()

	return
}

type L2CWorldBossRankCommand struct {
	base.Command
}

func (l *L2CWorldBossRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CWorldBossRankCommand) Error(msg *l2c.C2L_WorldBossRank, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossRank, msg)
	return false
}

func (l *L2CWorldBossRankCommand) Execute(ctx context.Context) bool {
	lmsg := &l2c.L2C_WorldBossRank{}
	if err := proto.Unmarshal(l.ProtoData, lmsg); err != nil {
		l4g.Errorf("L2C_WorldBossRank unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2C_WorldBossRank. recv from logic:%+v", l.Msg.UID, lmsg)

	cmsg := &l2c.C2L_WorldBossRank{
		Ret:       uint32(cret.RET_OK),
		Type:      lmsg.Type,
		Level:     lmsg.Level,
		StartRank: lmsg.StartRank,
		EndRank:   lmsg.EndRank,
		BossType:  lmsg.BossType,
	}

	manager, ok := l.GetModule().(*w.Manager)
	if !ok {
		l4g.Errorf("user:%d L2C_WorldBossRank: get worldBoss err, partitionID:%d, sid:%d", l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}
	userM, roomM, worldBoss, userBackM := manager.GetUserManager(), manager.GetRoomManager(), manager.GetWorldBoss(), manager.GetUserBackManager()
	bossType := worldBoss.GetBossType()
	if bossType == lmsg.BossType {
		var uniqueID uint32
		user := userM.GetUser(l.Msg.UID)
		if user != nil {
			uniqueID = user.GetRoomUniqueID()
		}
		room := roomM.GetRoom(uniqueID)
		if cmsg.Type == uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_ROOM) && room == nil {
			l4g.Errorf("user:%d L2C_WorldBossRank: not match room. uniqueID: %d", l.Msg.UID, uniqueID)
			return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
		}
		l.getRankUsers(cmsg, userM, worldBoss, uniqueID)
	} else {
		l.getOldRank(cmsg, userBackM, worldBoss, bossType)
	}

	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossRank, cmsg)

	return l.ResultOK(cmsg.Ret)
}

func (l *L2CWorldBossRankCommand) getRankUsers(cmsg *l2c.C2L_WorldBossRank, userM *w.UserManager, worldBoss *w.WorldBoss, roomUniqueID uint32) {
	switch cmsg.Type {
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_PARTITION):
		if !worldBoss.IsSettle(worldBoss.GetSysID()) {
			userM.ResetPeakTag()
		}
		rankIns := userM.GetPartitionRankIns(cmsg.Level)
		cmsg.List = rankIns.GetRangeByRank(cmsg.StartRank, cmsg.EndRank)
		cmsg.SelfRank, cmsg.SelfValue = rankIns.GetRankAndNode(l.Msg.UID)
		if cmsg.SelfRank == 0 && cmsg.SelfValue == nil {
			user := userM.GetUser(l.Msg.UID)
			if user != nil && user.GetUserData() != nil {
				data := user.GetUserData()
				cmsg.SelfValue = &cl.RankValue{
					Id:     data.Uid,
					Sid:    data.Sid,
					Value:  data.AccumulativeHurtScore, // 累计伤害积分
					Param1: data.AccumulativeHurt,      // 累计伤害
					Param2: uint64(data.Level),         // 难度等级
					Param5: data.MaxHurtScore,          // 最大伤害积分
				}
			}

		}
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_ROOM):
		rankIns := userM.GetRoomRankIns(roomUniqueID)
		cmsg.List = rankIns.GetRangeByRank(1, activity.WorldBossRoomRankGetNum)
		cmsg.SelfRank, cmsg.SelfValue = rankIns.GetRankAndNode(l.Msg.UID)
		if cmsg.SelfRank > activity.WorldBossRoomRankGetNum {
			cmsg.SelfRank = 0
		}
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_FIGHT_STAR):
		if !worldBoss.IsSettle(worldBoss.GetSysID()) {
			userM.ResetPeakTag()
		}
		allRankIns := userM.GetAllPartitionRankIns()
		for _, rankIns := range allRankIns {
			node := rankIns.First()
			if node != nil {
				user := userM.GetUser(node.Id)
				if user != nil {
					node.Param5 = user.GetMaxHurtScore()
				}
				cmsg.List = append(cmsg.List, node)
			}
		}
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_SELF):
		// 结算后才会调用，userM.ResetPeakTag()在结算处会调用一次，这里不需要重复调用
		rankIns := userM.GetPartitionRankIns(cmsg.Level)
		cmsg.SelfRank, cmsg.SelfValue = rankIns.GetRankAndNode(l.Msg.UID)
	}
}

func (l *L2CWorldBossRankCommand) getOldRank(cmsg *l2c.C2L_WorldBossRank, userM *w.UserBackManager, worldBoss *w.WorldBoss, bossType uint32) {
	switch cmsg.Type {
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_PARTITION):
		rankIns := userM.GetPartitionRankIns(cmsg.Level, bossType)
		cmsg.List = rankIns.GetRangeByRank(cmsg.StartRank, cmsg.EndRank)
		cmsg.SelfRank, cmsg.SelfValue = rankIns.GetRankAndNode(l.Msg.UID)
		if cmsg.SelfRank == 0 && cmsg.SelfValue == nil {
			user := userM.GetUser(l.Msg.UID, bossType)
			if user != nil && user.GetUserData() != nil {
				data := user.GetUserData()
				cmsg.SelfValue = &cl.RankValue{
					Id:     data.Uid,
					Sid:    data.Sid,
					Value:  data.AccumulativeHurtScore, // 累计伤害积分
					Param1: data.AccumulativeHurt,      // 累计伤害
					Param2: uint64(data.Level),         // 难度等级
					Param5: data.MaxHurtScore,          // 最大伤害积分
				}
			}

		}
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_ROOM):
		roomID := userM.GetRoomID(l.Msg.UID, bossType)
		rankIns := userM.GetRoomRankIns(roomID, bossType)
		cmsg.List = rankIns.GetRangeByRank(1, activity.WorldBossRoomRankGetNum)
		cmsg.SelfRank, cmsg.SelfValue = rankIns.GetRankAndNode(l.Msg.UID)
		if cmsg.SelfRank > activity.WorldBossRoomRankGetNum {
			cmsg.SelfRank = 0
		}
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_FIGHT_STAR):
		allRankIns := userM.GetAllPartitionRankIns(bossType)
		for _, rankIns := range allRankIns {
			node := rankIns.First()
			if node != nil {
				user := userM.GetUser(node.Id, bossType)
				if user != nil {
					node.Param5 = user.GetMaxHurtScore()
				}
				cmsg.List = append(cmsg.List, node)
			}
		}
	case uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_SELF):
		// 结算后才会调用，userM.ResetPeakTag()在结算处会调用一次，这里不需要重复调用
		rankIns := userM.GetPartitionRankIns(cmsg.Level, bossType)
		cmsg.SelfRank, cmsg.SelfValue = rankIns.GetRankAndNode(l.Msg.UID)
	}
}

type L2CWorldBossGetRoomLogCommand struct {
	base.Command
}

func (l *L2CWorldBossGetRoomLogCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CWorldBossGetRoomLogCommand) Error(msg *l2c.C2L_WorldBossGetRoomLog, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossGetRoomLog, msg)
	return false
}

func (l *L2CWorldBossGetRoomLogCommand) Execute(ctx context.Context) bool {
	lmsg := &l2c.L2C_WorldBossGetRoomLog{}
	if err := proto.Unmarshal(l.ProtoData, lmsg); err != nil {
		l4g.Errorf("L2C_WorldBossGetRoomLog unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2C_WorldBossGetRoomLog. recv from logic:%+v", l.Msg.UID, lmsg)

	cmsg := &l2c.C2L_WorldBossGetRoomLog{
		Ret: uint32(cret.RET_OK),
		Num: lmsg.Num,
	}

	manager, ok := l.GetModule().(*w.Manager)
	if !ok {
		l4g.Errorf("user:%d L2C_WorldBossGetRoomLog: get worldBoss err, partitionID:%d, sid:%d", l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}
	worldBoss, userM, roomM := manager.GetWorldBoss(), manager.GetUserManager(), manager.GetRoomManager()

	isOpen := goxml.GetData().WorldbossInfoM.IsOpen(worldBoss.GetSysID())
	if !isOpen {
		l4g.Errorf("user:%d L2C_WorldBossGetRoomLog: worldBoss not open. sysID: %d", l.Msg.UID, worldBoss.GetSysID())
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_OPEN))
	}

	user := userM.GetUser(l.Msg.UID)
	if user == nil {
		l4g.Errorf("user:%d L2C_WorldBossGetRoomLog: user not exist, not match room.", l.Msg.UID)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
	}

	uniqueID := user.GetRoomUniqueID()
	room := roomM.GetRoom(uniqueID)
	if room == nil {
		l4g.Errorf("user:%d L2C_WorldBossGetRoomLog: not match room. uniqueID: %d", l.Msg.UID, uniqueID)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
	}
	cmsg.Logs = room.GetLogs(lmsg.Num)
	cmsg.List, cmsg.RoomTop = retRank(l.Msg.UID, uniqueID, userM)
	pRankIns := userM.GetPartitionRankIns(user.GetLevel())
	cmsg.PartitionRank = pRankIns.GetRankById(l.Msg.UID)
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossGetRoomLog, cmsg)

	return l.ResultOK(cmsg.Ret)
}

type L2CWorldBossSettleCommand struct {
	base.Command
}

func (l *L2CWorldBossSettleCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CWorldBossSettleCommand) Error(msg *l2c.C2L_WorldBossSettle, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossSettle, msg)
	return false
}

func (l *L2CWorldBossSettleCommand) Execute(ctx context.Context) bool {
	lmsg := &l2c.L2C_WorldBossSettle{}
	if err := proto.Unmarshal(l.ProtoData, lmsg); err != nil {
		l4g.Errorf("L2C_WorldBossSettle unmarshal error. sid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("sid:%d L2C_WorldBossSettle. recv from logic:%+v", l.Msg.UID, lmsg)

	cmsg := &l2c.C2L_WorldBossSettle{
		Ret: uint32(cret.RET_OK),
		Id:  lmsg.Id,
	}

	manager, ok := l.GetModule().(*w.Manager)
	if !ok {
		l4g.Errorf("L2C_WorldBossSettle: get worldBoss err, partitionID:%d, sid:%d", l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}
	worldBoss := manager.GetWorldBoss()
	if !worldBoss.IsSettle(lmsg.Id) {
		l4g.Errorf("L2C_WorldBossSettle: get worldBoss settle data err, partitionID:%d, sid:%d, wid:%d", l.Partition(), l.ServerID(), lmsg.Id)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_CROSS_NOT_EXIST_SETTLE_DATA))
	}

	userRankBak := manager.GetUserRankBak()
	cmsg.List, cmsg.PartitionFirst = userRankBak.GetSettleData(l.ServerID())
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossSettle, cmsg)

	return l.ResultOK(cmsg.Ret)
}

type L2CWorldBossSyncFightDataCommand struct {
	base.Command
}

func (l *L2CWorldBossSyncFightDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CWorldBossSyncFightDataCommand) Error(msg *l2c.C2L_WorldBossSyncFightData, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossSyncFightData, msg)
	return false
}

func (l *L2CWorldBossSyncFightDataCommand) Execute(ctx context.Context) bool {
	lmsg := &l2c.L2C_WorldBossSyncFightData{}
	if err := proto.Unmarshal(l.ProtoData, lmsg); err != nil {
		l4g.Errorf("L2C_WorldBossSyncFightData unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2C_WorldBossSyncFightData. recv from logic:%+v", l.Msg.UID, lmsg)

	cmsg := &l2c.C2L_WorldBossSyncFightData{
		Ret:                   uint32(cret.RET_OK),
		Type:                  lmsg.Type,
		ReportId:              lmsg.ReportId,
		Hurt:                  lmsg.Hurt,
		HurtScore:             lmsg.HurtScore,
		MaxHurt:               lmsg.MaxHurt,
		MaxHurtScore:          lmsg.MaxHurtScore,
		AccumulativeHurt:      lmsg.AccumulativeHurt,
		AccumulativeHurtScore: lmsg.AccumulativeHurtScore,
	}

	manager, ok := l.GetModule().(*w.Manager)
	if !ok {
		l4g.Errorf("user:%d L2C_WorldBossSyncFightData: get worldBoss err, partitionID:%d, sid:%d", l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	worldBoss, userM, roomM := manager.GetWorldBoss(), manager.GetUserManager(), manager.GetRoomManager()

	isOpen := goxml.GetData().WorldbossInfoM.IsOpen(worldBoss.GetSysID())
	if !isOpen {
		l4g.Errorf("user:%d L2C_WorldBossSyncFightData: worldBoss not open. sysID: %d", l.Msg.UID, worldBoss.GetSysID())
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_OPEN))
	}

	user := userM.GetUser(l.Msg.UID)
	if user == nil {
		l4g.Errorf("user:%d L2C_WorldBossSyncFightData: user not exist, not match room.", l.Msg.UID)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
	}

	uniqueID := user.GetRoomUniqueID()
	room := roomM.GetRoom(uniqueID)
	if room == nil {
		l4g.Errorf("user:%d L2C_WorldBossSyncFightData: not match room. uniqueID: %d", l.Msg.UID, uniqueID)
		return l.Error(cmsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
	}
	// 战区榜：old first
	partitionRankIns := userM.GetPartitionRankIns(user.GetLevel())
	oldPartitionRankFirst := partitionRankIns.First()
	if oldPartitionRankFirst == nil {
		cmsg.IsSendSystemMsg = true
	}

	// 房间榜: oldFirst、oldSelfRank
	roomRankIns := userM.GetRoomRankIns(uniqueID)
	oldRoomRankFirst, oldSelfRoomRank := roomRankIns.First(), roomRankIns.GetRankById(l.Msg.UID)

	if user.UpdatePokemonFloor(lmsg.PokemonFloor) {
		userM.SetChange(user.GetUserData())
	}

	// 更新 hurt
	if user.UpdateMaxHurtAndScore(lmsg.MaxHurt, lmsg.MaxHurtScore) {
		user.UpdateBattleReportID(lmsg.ReportId)
		userM.SetChange(user.GetUserData())
		// 更新战区单次最高伤害，并返回是否命中，需要记录历史阵容
		cmsg.IsSetRecommendFormation = userM.GetRecommend().CheckUpdateMaxHurtRank(user.GetUserData())
	}
	if user.UpdateAccumulativeHurtAndScore(lmsg.AccumulativeHurt, lmsg.AccumulativeHurtScore) {
		userM.SetChange(user.GetUserData())
		roomRankIns.Insert(user.GetUserData())
		partitionRankIns.Insert(user.GetUserData())
		cmsg.IsSetPeakFormation = IsSetPeakFormation(userM, user.GetLevel(), l.Msg.UID)
	}

	// 跑马灯: 战区第一有变化
	newPartitionRankFirst := partitionRankIns.First()
	if newPartitionRankFirst != nil && oldPartitionRankFirst != nil && oldPartitionRankFirst.Id != newPartitionRankFirst.Id {
		cmsg.IsSendSystemMsg = true
	}
	// 更新日志：
	l.updateLog(room, userM, lmsg, oldSelfRoomRank, oldRoomRankFirst)
	roomM.SetChange(room.GetData())
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossSyncFightData, cmsg)

	return l.ResultOK(cmsg.Ret)
}

func IsSetPeakFormation(userM *w.UserManager, level uint32, uid uint64) bool {
	rankIns := userM.GetPartitionRankIns(level)
	rank := rankIns.GetRankById(uid)
	return rank > 0
}

func (l *L2CWorldBossSyncFightDataCommand) updateLog(room *w.Room, userM *w.UserManager, lmsg *l2c.L2C_WorldBossSyncFightData, oldRank uint32, oldRankFirst *cl.RankValue) {
	now := time.Now().Unix()
	// 任意伤害
	room.AddLog(&cl.WorldBossRoomLog{
		Uid:        l.Msg.UID,
		LogType:    uint32(common.WORLD_BOSS_LOG_TYPE_WBLT_HURT),
		CreateTime: now,
		Params:     []string{lmsg.PlayerName, strconv.FormatUint(lmsg.HurtScore, 10)},
	})
	// 任意玩家排名第一
	roomIns := userM.GetRoomRankIns(room.GetUniqueID())
	newRankFirst := roomIns.First()
	if newRankFirst != nil && oldRankFirst != nil && newRankFirst.Id != oldRankFirst.Id {
		room.AddLog(&cl.WorldBossRoomLog{
			Uid:        l.Msg.UID,
			LogType:    uint32(common.WORLD_BOSS_LOG_TYPE_WBLT_RANK_FIRST),
			CreateTime: now,
			Params:     []string{lmsg.PlayerName},
		})
	}
	// 任意玩家排名变化
	newRank := roomIns.GetRankById(l.Msg.UID)
	if newRank != oldRank {
		room.AddLog(&cl.WorldBossRoomLog{
			Uid:        l.Msg.UID,
			LogType:    uint32(common.WORLD_BOSS_LOG_TYPE_WBLT_RANK_CHANGE),
			CreateTime: now,
			Params:     []string{strconv.FormatUint(uint64(oldRank), 10), strconv.FormatUint(uint64(newRank), 10)},
		})
	}
}

type L2CWorldBossGetDataCommand struct {
	base.Command
}

func (l *L2CWorldBossGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (l *L2CWorldBossGetDataCommand) Error(msg *l2c.C2L_WorldBossGetData, retCode uint32) bool {
	msg.Ret = retCode
	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossGetData, msg)
	return false
}

func (l *L2CWorldBossGetDataCommand) Execute(ctx context.Context) bool {
	lmsg := &l2c.L2C_WorldBossGetData{}
	if err := proto.Unmarshal(l.ProtoData, lmsg); err != nil {
		l4g.Errorf("L2C_WorldBossGetData unmarshal error. uid:%d err:%v", l.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d L2C_WorldBossGetData. recv from logic:%+v", l.Msg.UID, lmsg)

	cmsg := &l2c.C2L_WorldBossGetData{
		Ret: uint32(cret.RET_OK),
	}

	manager, ok := l.GetModule().(*w.Manager)
	if !ok {
		l4g.Errorf("user:%d L2C_WorldBossGetData: get worldBoss err, partitionID:%d, sid:%d", l.Msg.UID, l.Partition(), l.ServerID())
		return l.Error(cmsg, uint32(cret.RET_MODULE_NOT_REGISTER))
	}

	cmsg.BossTypeLevel = manager.GetUserBackManager().GetBossTypeLevel(l.Msg.UID)

	l.ReturnCmdToLogic(l2c.ID_MSG_C2L_WorldBossGetData, cmsg)
	return l.ResultOK(cmsg.Ret)
}
