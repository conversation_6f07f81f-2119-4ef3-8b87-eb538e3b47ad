linters:
  disable-all: true
  enable:
    - errcheck 
    - gosimple 
    - govet 
    - ineffassign
    - staticcheck 
    - unused 
#开始其他的
    - asasalint 
    - asciicheck 
    - bidichk 
    - bodyclose
    - durationcheck 
    - exhaustive 
    - iface 
    - makezero 
    - mnd
    - nilerr 
    #- prealloc var a []int 这种方式都不行
    - protogetter 
    - reassign 
    - recvcheck 
    - unparam 
    - funlen 
    - varnamelen 
    - wastedassign 
    #- exhaustruct proto里的xxx_ss肯定会报错，没办法处理
run:
  timeout: 5m
  tests: false
  concurrency: 4
issues:
  exclude-dirs:
    - logic/db/redisop
    - cross/db/redisop
    - logic/battle/cmd 
  exclude-files:
    - logic/character/ai_tool.go
linters-settings:
  funlen:
    lines: 100
    statements: -1
    ignore-comments: true
  varnamelen:
    max-distance: 10
    min-name-length: 2 #小于2个字符才算min,比如id这类就不算
    ignore-names:
      - m
      - i
      - k
      - u
      - g
  mnd:
    ignored-numbers:
      - '2'
      - '4'
      - '8'
      - '16'
      - '32'
      - '64'
      - '128'
      - '256'
      - '512'
      - '1024'
      - '2048'
      - '4096'
      - '10'
      - '100'
      - '1000'
      - '10000'
      - '1000000'
      - '10000.0'
      - '60'
      - '3600'
      - '84600'
      - '0x1'
      - '0xFFFF'
      - '0600'
