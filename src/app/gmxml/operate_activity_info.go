package gmxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/util"
)

type OperateActivityInfo struct {
	Id                uint32 `xml:"id,attr"`                  //
	PageId            uint32 `xml:"page_id,attr"`             //int:所属页签
	Type              uint32 `xml:"type,attr"`                //int:活动类型
	Value             uint32 `xml:"value,attr"`               //int:活动值
	Name              string `xml:"name,attr"`                //string:活动名
	Icon              uint32 `xml:"icon,attr"`                //int:活动图标
	Flag              string `xml:"flag,attr"`                //string:活动角标
	Description       uint32 `xml:"description,attr"`         //string:活动详细说明
	Intro             string `xml:"intro,attr"`               //string:活动简易说明
	Picture           string `xml:"picture,attr"`             //int:活动背景图
	Npc               uint32 `xml:"npc,attr"`                 //int:活动看板
	TitleFont         string `xml:"tile_font,attr"`           //int:活动标题艺术字
	Level             uint32 `xml:"level,attr"`               //int:开启等级
	VipLevel          uint32 `xml:"vip_level,attr"`           //int:vip开启等级
	ServerLimit       uint32 `xml:"server_limit,attr"`        //int:开服限制
	TimeType          uint32 `xml:"time_type,attr"`           //int:时间类型
	ViewTime          uint32 `xml:"view_time,attr"`           //int:预览时间
	OpenTime          uint32 `xml:"open_time,attr"`           //int:开启时间
	EndTime           uint32 `xml:"end_time,attr"`            //int:结束时间
	ForceEnd          uint32 `xml:"force_end,attr"`           //int:强制结束
	FunctionPic       uint32 `xml:"function_pic,attr"`        //int:功能按钮图标
	FunctionTxt       string `xml:"function_txt,attr"`        //string:功能按钮文本
	FunctionBtn1      uint32 `xml:"function_btn1,attr"`       //int:功能按钮
	FunctionBtn2      uint32 `xml:"function_btn2,attr"`       //int:功能按钮
	FunctionBtn3      uint32 `xml:"function_btn3,attr"`       //int:功能按钮
	FunctionBtn4      uint32 `xml:"function_btn4,attr"`       //int:功能按钮
	FunctionBtn5      uint32 `xml:"function_btn5,attr"`       //int:功能按钮
	ShowType1         uint32 `xml:"show_type_1,attr"`         //int:显示资源类型1
	ShowValue1        uint32 `xml:"show_value_1,attr"`        //int:显示资源类型值1
	ShowType2         uint32 `xml:"show_type_2,attr"`         //int:显示资源类型2
	ShowValue2        uint32 `xml:"show_value_2,attr"`        //int:显示资源类型值2
	ShowType3         uint32 `xml:"show_type_3,attr"`         //int:显示资源类型3
	ShowValue3        uint32 `xml:"show_value_3,attr"`        //int:显示资源类型值3
	ShowType4         uint32 `xml:"show_type_4,attr"`         //int:显示资源类型4
	ShowValue4        uint32 `xml:"show_value_4,attr"`        //int:显示资源类型值4
	ShowType5         uint32 `xml:"show_type_5,attr"`         //int:显示资源类型5
	ShowValue5        uint32 `xml:"show_value_5,attr"`        //int:显示资源类型值5
	DungeonId         uint32 `xml:"dungeon_id,attr"`          //int:主线关卡ID
	LoginPut          uint32 `xml:"login_put,attr"`           //int:客户端推送方式
	CountDown         uint32 `xml:"count_down,attr"`          //int:客户端显示倒计时
	NoviceProtection  uint32 `xml:"novice_protection,attr"`   //int:新手保护天数
	ResetType         uint32 `xml:"reset_type,attr"`          //int:重置类型
	ClientTemplate    uint32 `xml:"client_template,attr"`     //int:前端模板
	PageButtonPicture string `xml:"page_button_picture,attr"` //int:切页按钮图片
	PageButtonSort    uint32 `xml:"page_button_sort,attr"`    //切页按钮排序
	PageButtonName    string `xml:"page_button_name,attr"`    //切页按钮名称
	PageButtonID      uint32 `xml:"page_button_id,attr"`      //切页按钮ID
	ResCoin           uint32 `xml:"res_coin,attr"`            //资源栏
	GiftModule        uint32 `xml:"gift_module,attr"`         //礼物模板ID
	GiftIconPath      string `xml:"gift_icon_path,attr"`      //礼物入口Icon路径
	GiftPic           string `xml:"gift_pic,attr"`            //礼物界面图片
	GiftName          string `xml:"gift_name,attr"`           //礼物入口按钮名称
	IsRound           uint32 `xml:"is_round,attr"`            //是否是轮次活动
	RoundType         uint32 `xml:"round_type,attr"`          //轮次活动类型
	LevelMax          uint32 `xml:"level_max,attr"`           //
	PgpIdStr          string `xml:"pgp_ids,attr"`
	RechargeMin       uint32 `xml:"recharge_min,attr"`
	RechargeMax       uint32 `xml:"recharge_max,attr"`
	Hero              uint32 `xml:"hero,attr"`
	Artifact          uint32 `xml:"artifact,attr"`
	ShowAd1           string `xml:"show_ad1,attr"`
	ShowAd2           string `xml:"show_ad2,attr"`
	PgpIds            []uint32
}

func (o *OperateActivityInfo) prepare() {
}

/*
func (o *OperateActivityInfo) Check() error {
				return nil
}
*/

type OperateActivityInfos struct {
	Datas []*OperateActivityInfo `xml:"data"`
}

// 是否需要同步到客户端
func (o *OperateActivityInfo) canFlush(now []uint32) bool {
	if o.isForceEnd(now) {
		return false
	}
	//结束了很久的，尚未到预览时间的等等
	return o.timeCondition(now)
}

// 是否已经强制结束了
func (o *OperateActivityInfo) isForceEnd(now []uint32) bool {
	return o.ForceEnd > 0 && now[common.TIME_TYPE_NORMAL_DATE] > o.ForceEnd
}

func (o *OperateActivityInfo) IsOpening(condition *FlushCondition) bool {
	//强制结束了
	if condition == nil {
		return false
	}

	if o.isForceEnd(condition.Now) {
		return false
	}

	if o.IsNoviceProtection(condition.ServerOpenDay) {
		return false
	}

	if o.Type == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_DISCOUNT_GIFT) || o.Type == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT) ||
		o.Type == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_CHAIN_GIFT) {
		// 新推送礼包，IsOpening 只判断时间
		return o.timeCondition(condition.Now)
	}

	dungeonC := true
	levelC := true
	vipLevelC := true

	if o.DungeonId > 0 && condition.DungeonID < o.DungeonId {
		dungeonC = false
	}

	if o.Level > 0 && condition.Level < o.Level {
		levelC = false
	}

	if o.VipLevel > 0 && condition.VipLevel < o.VipLevel {
		vipLevelC = false
	}

	if dungeonC && levelC && vipLevelC {
		return o.timeCondition(condition.Now)
	}

	return false
}

func (o *OperateActivityInfo) timeCondition(now []uint32) bool {
	if o.TimeType > 0 && o.TimeType < uint32(common.TIME_TYPE_MAX_SIZE) {
		return o.OpenTime <= now[o.TimeType] && now[o.TimeType] <= o.EndTime
	}
	return false
}

// 判断活动是否已结束
// return bool - 是否已结束
// return uint32 - 结束了多少秒了
func (o *OperateActivityInfo) IsFinished(now []uint32) (bool, uint32) {
	//强制结束了
	if o.isForceEnd(now) {
		return true, util.SafeSubUint32(now[common.TIME_TYPE_NORMAL_DATE], o.ForceEnd)
	}
	if o.TimeType > 0 && o.TimeType < uint32(common.TIME_TYPE_MAX_SIZE) {
		if now[o.TimeType] > o.EndTime {
			return true, util.SafeSubUint32(now[o.TimeType], o.EndTime)
		}
	}

	return false, 0
}

func (o *OperateActivityInfo) CanDelete(end bool, time uint32) (bool, bool) {
	if end {
		if time >= uint32(OperateActivityDeleteDelay) {
			return end, true
		}
	}
	return end, false
}

func (o *OperateActivityInfo) SrvDeleteSrvDeleteCheckCheck(now int64) bool {
	switch o.TimeType {
	case uint32(common.TIME_TYPE_NORMAL_DATE):
		return now > int64(o.EndTime) && util.DaysBetweenTimes(now, int64(o.EndTime))+1 >= uint32(OperateActivityDeleteDay)
	default:
		return false
	}
}

func newOperateActivityInfo(data *cl.OperateActivityInfo) *OperateActivityInfo {
	info := &OperateActivityInfo{
		Id:                data.Id,
		PageId:            data.PageId,
		Type:              data.Type,
		Value:             data.Value,
		Name:              data.Name,
		Icon:              data.Icon,
		Flag:              data.Flag,
		Description:       data.Description,
		Intro:             data.Intro,
		Picture:           data.Picture,
		Npc:               data.Npc,
		TitleFont:         data.TitleFont,
		Level:             data.Level,
		VipLevel:          data.VipLevel,
		ServerLimit:       data.ServerLimit,
		TimeType:          data.TimeType,
		ViewTime:          data.ViewTime,
		OpenTime:          data.OpenTime,
		EndTime:           data.EndTime,
		ForceEnd:          data.ForceEnd,
		FunctionTxt:       data.FunctionTxt,
		FunctionPic:       data.FunctionPic,
		DungeonId:         data.DungeonId,
		LoginPut:          data.LoginPut,
		CountDown:         data.CountDown,
		NoviceProtection:  data.NoviceProtection,
		ResetType:         data.ResetType,
		ClientTemplate:    data.ClientTemplate,
		PageButtonPicture: data.PageButtonPicture,
		PageButtonSort:    data.PageButtonSort,
		PageButtonName:    data.PageButtonName,
		PageButtonID:      data.PageButtonId,

		ResCoin:      data.ResCoin,
		GiftModule:   data.GiftModule,
		GiftIconPath: data.GiftIconPath,
		GiftPic:      data.GiftPic,
		IsRound:      data.IsRound,
		RoundType:    data.RoundType,
		LevelMax:     data.LevelMax,
		PgpIdStr:     util.Uint32SliceToString(data.PgpIds, ","),
		RechargeMin:  data.RechargeMin,
		RechargeMax:  data.RechargeMax,
		PgpIds:       data.PgpIds,

		Hero:     data.Hero,
		Artifact: data.Artifact,
		ShowAd1:  data.ShowAd1,
		ShowAd2:  data.ShowAd2,
	}
	size := len(data.FunctionBtns)
	if size > 0 {
		info.FunctionBtn1 = data.FunctionBtns[0]
	}
	if size > 1 {
		info.FunctionBtn2 = data.FunctionBtns[1]
	}
	if size > 2 {
		info.FunctionBtn3 = data.FunctionBtns[2]
	}
	if size > 3 {
		info.FunctionBtn4 = data.FunctionBtns[3]
	}
	if size > 4 {
		info.FunctionBtn5 = data.FunctionBtns[4]
	}

	size = len(data.Shows)
	if size > 0 {
		info.ShowType1 = data.Shows[0].Type
		info.ShowValue1 = data.Shows[0].Value
	}
	if size > 1 {
		info.ShowType2 = data.Shows[1].Type
		info.ShowValue2 = data.Shows[1].Value
	}
	if size > 2 {
		info.ShowType3 = data.Shows[2].Type
		info.ShowValue3 = data.Shows[2].Value
	}
	if size > 3 {
		info.ShowType4 = data.Shows[3].Type
		info.ShowValue4 = data.Shows[3].Value
	}
	if size > 4 {
		info.ShowType5 = data.Shows[4].Type
		info.ShowValue5 = data.Shows[4].Value
	}
	info.prepare()
	return info
}

func (o *OperateActivityInfo) Flush() *cl.OperateActivityInfo {
	newInfo := &cl.OperateActivityInfo{
		Id:                o.Id,
		PageId:            o.PageId,
		Type:              o.Type,
		Value:             o.Value,
		Name:              o.Name,
		Icon:              o.Icon,
		Flag:              o.Flag,
		Description:       o.Description,
		Intro:             o.Intro,
		Picture:           o.Picture,
		Npc:               o.Npc,
		TitleFont:         o.TitleFont,
		Level:             o.Level,
		ServerLimit:       o.ServerLimit,
		TimeType:          o.TimeType,
		ViewTime:          o.ViewTime,
		OpenTime:          o.OpenTime,
		EndTime:           o.EndTime,
		ForceEnd:          o.ForceEnd,
		FunctionPic:       o.FunctionPic,
		FunctionTxt:       o.FunctionTxt,
		FunctionBtns:      o.FunctionBtns(),
		Shows:             o.shows(),
		VipLevel:          o.VipLevel,
		DungeonId:         o.DungeonId,
		LoginPut:          o.LoginPut,
		CountDown:         o.CountDown,
		NoviceProtection:  o.NoviceProtection,
		ResetType:         o.ResetType,
		ClientTemplate:    o.ClientTemplate,
		PageButtonPicture: o.PageButtonPicture,
		PageButtonSort:    o.PageButtonSort,
		PageButtonName:    o.PageButtonName,
		PageButtonId:      o.PageButtonID,
		ResCoin:           o.ResCoin,
		GiftModule:        o.GiftModule,
		GiftIconPath:      o.GiftIconPath,
		GiftPic:           o.GiftPic,
		GiftName:          o.GiftName,
		IsRound:           o.IsRound,
		RoundType:         o.RoundType,
		LevelMax:          o.LevelMax,
		RechargeMin:       o.RechargeMin,
		RechargeMax:       o.RechargeMax,
		Hero:              o.Hero,
		Artifact:          o.Artifact,
		ShowAd1:           o.ShowAd1,
		ShowAd2:           o.ShowAd2,
	}
	newInfo.PgpIds = make([]uint32, 0, len(o.PgpIds))
	copy(newInfo.PgpIds, o.PgpIds)
	return newInfo
}

func (o *OperateActivityInfo) shows() []*cl.Resource {
	var shows []*cl.Resource
	if o.ShowType1 > 0 {
		shows = append(shows, &cl.Resource{
			Type:  o.ShowType1,
			Value: o.ShowValue1,
		})
	}
	if o.ShowType2 > 0 {
		shows = append(shows, &cl.Resource{
			Type:  o.ShowType2,
			Value: o.ShowValue2,
		})
	}
	if o.ShowType3 > 0 {
		shows = append(shows, &cl.Resource{
			Type:  o.ShowType3,
			Value: o.ShowValue3,
		})
	}
	if o.ShowType4 > 0 {
		shows = append(shows, &cl.Resource{
			Type:  o.ShowType4,
			Value: o.ShowValue4,
		})
	}
	if o.ShowType5 > 0 {
		shows = append(shows, &cl.Resource{
			Type:  o.ShowType5,
			Value: o.ShowValue5,
		})
	}
	return shows
}

func (o *OperateActivityInfo) clone() *OperateActivityInfo {
	return &OperateActivityInfo{
		Id:                o.Id,
		PageId:            o.PageId,
		Type:              o.Type,
		Value:             o.Value,
		Name:              o.Name,
		Icon:              o.Icon,
		Flag:              o.Flag,
		Description:       o.Description,
		Intro:             o.Intro,
		Picture:           o.Picture,
		Npc:               o.Npc,
		TitleFont:         o.TitleFont,
		Level:             o.Level,
		VipLevel:          o.VipLevel,
		ServerLimit:       o.ServerLimit,
		TimeType:          o.TimeType,
		ViewTime:          o.ViewTime,
		OpenTime:          o.OpenTime,
		EndTime:           o.EndTime,
		ForceEnd:          o.ForceEnd,
		FunctionTxt:       o.FunctionTxt,
		FunctionPic:       o.FunctionPic,
		FunctionBtn1:      o.FunctionBtn1,
		FunctionBtn2:      o.FunctionBtn2,
		FunctionBtn3:      o.FunctionBtn3,
		FunctionBtn4:      o.FunctionBtn4,
		FunctionBtn5:      o.FunctionBtn5,
		ShowType1:         o.ShowType1,
		ShowValue1:        o.ShowValue1,
		ShowType2:         o.ShowType2,
		ShowValue2:        o.ShowValue2,
		ShowType3:         o.ShowType3,
		ShowValue3:        o.ShowValue3,
		ShowType4:         o.ShowType4,
		ShowValue4:        o.ShowValue4,
		ShowType5:         o.ShowType5,
		ShowValue5:        o.ShowValue5,
		DungeonId:         o.DungeonId,
		LoginPut:          o.LoginPut,
		CountDown:         o.CountDown,
		NoviceProtection:  o.NoviceProtection,
		ResetType:         o.ResetType,
		ClientTemplate:    o.ClientTemplate,
		PageButtonPicture: o.PageButtonPicture,
		PageButtonSort:    o.PageButtonSort,
		PageButtonName:    o.PageButtonName,
		PageButtonID:      o.PageButtonID,
		ResCoin:           o.ResCoin,
		GiftModule:        o.GiftModule,
		GiftIconPath:      o.GiftIconPath,
		GiftPic:           o.GiftPic,
		GiftName:          o.GiftName,
		IsRound:           o.IsRound,
		RoundType:         o.RoundType,
		LevelMax:          o.LevelMax,
		PgpIds:            o.PgpIds,
		RechargeMin:       o.RechargeMin,
		RechargeMax:       o.RechargeMax,
		Hero:              o.Hero,
		Artifact:          o.Artifact,
		ShowAd1:           o.ShowAd1,
		ShowAd2:           o.ShowAd2,
	}
}

func (o *OperateActivityInfo) FunctionBtns() []uint32 {
	btns := make([]uint32, 5)
	btns[0] = o.FunctionBtn1
	btns[1] = o.FunctionBtn2
	btns[2] = o.FunctionBtn3
	btns[3] = o.FunctionBtn4
	btns[4] = o.FunctionBtn5
	return btns
}

func (o *OperateActivityInfo) IsNoviceProtection(serverDay uint32) bool {
	if o.NoviceProtection != 0 {
		return serverDay <= o.NoviceProtection
	}

	return false
}

func (o *OperateActivityInfo) IsPgpIdsExist(pgpIds []uint32) bool {

	for _, uPgpId := range pgpIds {
		for _, aPgpId := range o.PgpIds {
			if uPgpId == aPgpId {
				return true
			}
		}
	}
	return false
}
