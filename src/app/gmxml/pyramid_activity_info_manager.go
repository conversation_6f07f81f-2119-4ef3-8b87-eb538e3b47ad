package gmxml

import (
	"app/goxml"
	"app/protos/out/cl"
	"fmt"
	"os"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type PyramidActivityInfos struct {
	Datas []*PyramidActivityInfo `xml:"data"`
}

type PyramidActivityInfoManager struct {
	fileName string
	datas    []*PyramidActivityInfoExt
}

func (pm *PyramidActivityInfoManager) Load(air string, show bool) {
	pm.fileName = filepath.Join(air, "pyramid_activity_info.xml")
	if _, err := os.Stat(pm.fileName); err != nil && os.IsNotExist(err) {
		l4g.Infof("pyramid activity config(%s) not exist", pm.fileName)
		return
	}

	tmp := &PyramidActivityInfos{}
	if err := util.LoadConfig(pm.fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", pm.fileName, err))
	} else {
		if show {
			for _, data := range tmp.Datas {
				l4g.Debug("config(%s): %+v", pm.fileName, data)
			}
		}
	}

	//初始化数据&清理过期数据
	pm.initDatas(tmp.Datas)

	for _, data := range pm.datas {
		//检查时间重叠，避免xml文件被手动误修改，造成的错误
		if data.OpStatus == OpStatusRelease && !pm.checkTimeOverlap(data.Id, data.StartTime, data.EndTime) {
			panic(fmt.Sprintf("load config %s fail: checkTimeOverlap failed", pm.fileName))
		}
		l4g.Debugf("PyramidActivityInfoM.load: data:%+v", data)
	}
}

// 初始化数据&清理过期数据
func (pm *PyramidActivityInfoManager) initDatas(datas []*PyramidActivityInfo) {
	pm.datas = make([]*PyramidActivityInfoExt, 0, len(datas))
	//now := time.Now().Unix()
	var needSave bool
	for _, data := range datas {
		// if data.EndTime+PyramidActivityDeleteDelay < now {
		// 	needSave = true
		// 	l4g.Infof("PyramidActivityInfoM.initDatas: data:%+v", data)
		// 	continue
		// }

		dataExt := &PyramidActivityInfoExt{
			Id:         data.Id,
			ActivityId: data.ActivityId,
			StartTime:  data.StartTime,
			EndTime:    data.EndTime,
			OpStatus:   data.OpStatus,
			//ProtectDay: data.ProtectDay,
		}
		pm.datas = append(pm.datas, dataExt)
	}

	if needSave {
		if !pm.save() {
			panic("PyramidActivityInfoM.initDatas: save failed")
		}
	}
}

// 检查与已有的活动是否存在时间重叠
func (pm *PyramidActivityInfoManager) checkTimeOverlap(id uint32, startTime, endTime int64) bool {
	if endTime <= startTime {
		l4g.Errorf("PyramidActivityInfoM.checkTimeOverlap: time illegal, startTime:%d, endTime:%d",
			startTime, endTime)
		return false
	}

	now := time.Now().Unix()
	for _, v := range pm.datas {
		//不对自身做检查
		if v.Id == id {
			continue
		}

		//加载时，过滤掉过期和下线的活动
		if v.IsDisable(now) {
			continue
		}

		//验证时间区间
		if startTime > v.EndTime || endTime < v.StartTime {
			continue
		}

		l4g.Errorf("PyramidActivityInfoM.checkTimeOverlap: failed, startTime:%d, endTime:%d, data:%+v",
			startTime, endTime, v)
		return false
	}
	return true
}

func (pm *PyramidActivityInfoManager) saveUpdate(oldActivity *PyramidActivityInfoExt) bool {
	success := pm.save()
	if !success {
		pm.replace(oldActivity)
		l4g.Errorf("PyramidActivityInfoM.saveUpdate failed, id: %d", oldActivity.Id)
	}

	return success
}

func (pm *PyramidActivityInfoManager) replace(data *PyramidActivityInfoExt) {
	for k, v := range pm.datas {
		if v.Id == data.Id {
			pm.datas[k] = data
			break
		}
	}
}

func (pm *PyramidActivityInfoManager) saveAdd(gmData *cl.PyramidActivityBase) bool {
	pm.datas = append(pm.datas, (*PyramidActivityInfoExt)(gmData))
	success := pm.save()
	if !success {
		pm.delete(gmData.Id)
		l4g.Errorf("PyramidActivityInfoM.saveAdd failed, id: %d", gmData.Id)
	}
	return success
}

func (pm *PyramidActivityInfoManager) delete(id uint32) {
	for k, v := range pm.datas {
		if v.Id == id {
			pm.datas = append(pm.datas[:k], pm.datas[k+1:]...)
			break
		}
	}
}

func (pm *PyramidActivityInfoManager) save() bool {
	saveDatas, isPass := pm.clone()
	if !isPass {
		l4g.Errorf("PyramidActivityInfoM.save: clone failed")
		return false
	}

	if err := util.SaveConfig(pm.fileName, saveDatas); err != nil {
		l4g.Errorf("PyramidActivityInfoM.save: config %s fail: %s", pm.fileName, err)
		return false
	}
	return true
}

func (pm *PyramidActivityInfoManager) clone() (*PyramidActivityInfos, bool) {
	tmp := &PyramidActivityInfos{
		Datas: make([]*PyramidActivityInfo, 0, len(pm.datas)),
	}
	for _, data := range pm.datas {
		v := data.clone()
		if v == nil {
			return nil, false
		}
		tmp.Datas = append(tmp.Datas, v)
	}
	return tmp, true
}

func (pm *PyramidActivityInfoManager) Get(id uint32) *PyramidActivityInfoExt {
	for _, v := range pm.datas {
		if id == v.Id {
			return v
		}
	}
	return nil
}

// 同步活动数据
func (pm *PyramidActivityInfoManager) SyncActivity(data *cl.PyramidActivityBase, now int64) bool {
	activity := pm.Get(data.Id)
	if activity == nil {
		//新活动，必须是发布状态
		if data.OpStatus == OpStatusOff {
			l4g.Errorf("PyramidActivityInfoM.SyncActivity: new activity OpStatus illegal, data:%+v", data)
			return false
		}
		return pm.addActivity(data)
	}

	return pm.updateActivity(data, activity, now)
}

// 添加新活动
func (pm *PyramidActivityInfoManager) addActivity(data *cl.PyramidActivityBase) bool {
	if !pm.checkTimeOverlap(data.Id, data.StartTime, data.EndTime) {
		l4g.Errorf("PyramidActivityInfoM.addActivity: checkTimeOverlap failed, data:%+v", data)
		return false
	}

	return pm.saveAdd(data)
}

// 更新已有活动
func (pm *PyramidActivityInfoManager) updateActivity(data *cl.PyramidActivityBase,
	activity *PyramidActivityInfoExt, now int64) bool {
	//不允许修改已过期或已下架的活动
	if activity.IsDisable(now) {
		l4g.Errorf("PyramidActivityInfoM.updateActivity: activity has disabled, data:%+v", data)
		return false
	}

	oldActivityData := pm.GetRawActivityByUniqID(data.Id)
	if oldActivityData == nil {
		l4g.Errorf("PyramidActivityInfoM.updateActivity: activity %d not exist.", data.Id)
		return false
	}

	if activity.IsActive(now) { //正在运行中的活动，仅可修改结束时间和下架
		if data.OpStatus == OpStatusOff {
			//下架
			activity.SetOff()
		} else {
			//修改结束时间
			if !pm.checkTimeOverlap(activity.Id, activity.StartTime, data.EndTime) {
				l4g.Errorf("PyramidActivityInfoM.updateActivity: checkTimeOverlap failed, data:%+v", data)
				return false
			}
			activity.UpdateEndTime(data.EndTime)
		}
	} else { //未开始的活动
		if !pm.checkTimeOverlap(activity.Id, data.StartTime, data.EndTime) {
			l4g.Errorf("PyramidActivityInfoM.updateActivity: checkTimeOverlap failed, data:%+v", data)
			return false
		}
		activity.UpdateBeforeRelease(data)
	}
	return pm.saveUpdate(oldActivityData)
}

// 获取全部有效的活动基础数据
// 修正处于新服保护期内的活动开始时间
// @param int64 serverOpenTm 服务器开服时间
// @return []*cl.PyramidActivityBase
func (pm *PyramidActivityInfoManager) Flush(serverOpenTm int64) []*cl.PyramidActivityBase {
	now := time.Now().Unix()
	//serverOpenZero := int64(util.DailyZeroByTime(serverOpenTm))
	ret := make([]*cl.PyramidActivityBase, 0, len(pm.datas))
	for _, v := range pm.datas {
		if v.IsDisable(now) {
			continue
		}

		/*
			fixedData := fixPyramidByProtectDay(v, serverOpenZero)
			if fixedData == nil {
				continue
			}
		*/

		ret = append(ret, v.trans2PyramidActivityBase())
		if len(ret) >= PyramidActivityFlushLimit {
			break
		}
	}
	return ret
}

// 根据时间，获取当前活动
// tips: 同一时段，只会有一个活动
// @retrun *cl.PyramidActivityBase
func (pm *PyramidActivityInfoManager) GetCurrentActivity(serverDay uint32, serverStartTime int64) *cl.PyramidActivityBase {
	now := time.Now().Unix()
	var base *cl.PyramidActivityBase
	if goxml.GetData().ActivityPyramidOpenInfoM.IsNewServer(serverDay) {
		base = goxml.GetData().ActivityPyramidOpenInfoM.GetCurActivity(serverDay, serverStartTime)
		return base
	} else {
		for _, v := range pm.datas {
			if v.IsActive(now) {
				base = v.trans2PyramidActivityBase()
				return base
			}
		}
	}

	return nil
}

// 根据新服保护期，修正活动开始时间
// @param *PyramidActivityInfoExt data 活动原始数据
// @param int64 serverOpenZero 服务器开服日的零点
// @return *cl.PyramidActivityBase
/*
func fixPyramidByProtectDay(data *PyramidActivityInfoExt, serverOpenZero int64) *cl.PyramidActivityBase {
	if data.ProtectDay == 0 {
		return data.trans2PyramidActivityBase()
	}
	// 新服保护结束时间 yyyy-mm-dd 23:59:59
	protectEndTm := serverOpenZero + int64(data.ProtectDay*util.DaySecs) - 1

	if data.EndTime <= protectEndTm {
		return nil
	}

	// 根据新服保护期，修正活动开始时间
	if data.StartTime <= protectEndTm {
		fixedStartTm := protectEndTm + 1
		if fixedStartTm >= data.EndTime {
			return nil
		}
		data.StartTime = fixedStartTm
	}

	return data.trans2PyramidActivityBase()
}
*/

// 根据唯一id，获取活动
func (pm *PyramidActivityInfoManager) GetActivityByUniqID(id uint32, serverType uint32, serverStartTime int64) *cl.PyramidActivityBase {
	if goxml.ActivityPyramidNewServer == serverType {
		info := goxml.GetData().ActivityPyramidOpenInfoM.GetRecordById(id)
		if info == nil {
			return nil
		}
		return info.ConvertBase(serverStartTime)
	} else {
		for _, v := range pm.datas {
			if v.Id == id {
				return v.trans2PyramidActivityBase()
			}
		}
	}

	return nil
}

// 根据唯一id，获取活动
func (pm *PyramidActivityInfoManager) GetRawActivityByUniqID(id uint32) *PyramidActivityInfoExt {
	for _, v := range pm.datas {
		if v.Id == id {
			return v.cloneRaw()
		}
	}
	return nil
}
