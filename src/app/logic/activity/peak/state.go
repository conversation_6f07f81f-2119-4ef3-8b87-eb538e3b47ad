package peak

import (
	"app/goxml"
	"app/logic/activity"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

func (m *Manager) GetRound() uint32 {
	return m.peak.State.Data.Round
}

func (m *Manager) GetRoundState() uint32 {
	return m.peak.State.Data.RoundState
}

// 验证当前轮次是否处于初始状态
// @param round uint32 轮次
// @return bool
func (m *Manager) IsCurRoundInInitState(round uint32) bool {
	return m.GetRound() == round && m.GetRoundState() == uint32(common.PEAK_ROUND_STATE_PRS_INIT)
}

// 验证当前轮次是否与参数一致且已完成
// @param round uint32 轮次
// @return bool 是否完成
func (m *Manager) IsCurRoundFinish(round uint32) bool {
	return m.GetRound() == round && m.GetRoundState() == uint32(common.PEAK_ROUND_STATE_PRS_END)
}

// 验证当前轮次是否需要更新为下一轮初始状态
// @param round uint32 轮次
// @param roundState uint32 轮次状态
func (m *Manager) IsNeedUpdateRoundToNextInitState(round, roundState uint32) bool {
	if m.GetRound() == 0 {
		return true
	}
	return m.IsCurRoundFinish(round-1) && roundState == uint32(common.PEAK_ROUND_STATE_PRS_INIT)
}

// 每轮结算后，更新状态数据
// @param round uint32 当前轮次
func (m *Manager) UpdateRoundFinish(round uint32) {
	m.peak.State.Data.Round = round
	m.peak.State.Data.RoundState = uint32(common.PEAK_ROUND_STATE_PRS_END)
}

// 校验当前结算轮次是否正确
// @param round uint32 目标发奖轮次
// @return bool
func (m *Manager) IsRightRound(round uint32) bool {
	if round < m.GetRound() {
		return false
	} else if round == m.GetRound() {
		return m.GetRoundState() != uint32(common.PEAK_ROUND_STATE_PRS_END)
	} else {
		return (round == m.GetRound()+1) && (m.GetRoundState() == uint32(common.PEAK_ROUND_STATE_PRS_END))
	}
}

func (m *Manager) GetPhase() uint32 {
	return m.peak.State.Data.Phase
}

func (m *Manager) GetPhaseState() uint32 {
	return m.peak.State.Data.PhaseState
}

func (m *Manager) GetSeason() uint32 {
	return m.peak.State.Data.Season
}

func (m *Manager) getSeasonState() uint32 {
	return m.peak.State.Data.SeasonState
}

func (m *Manager) IsSeasonFinish(season uint32) bool {
	return m.GetSeason() == season && m.peak.State.Data.SeasonState == uint32(common.PEAK_SEASON_STATE_PSS_END)
}

func (m *Manager) IsOpen() bool {
	return m.peak.State.Data.Open
}

// 设置赛季状态
// @param *cl.PeakState state 状态数据
func (m *Manager) setSeasonState(state *cl.PeakState) {
	m.peak.State.Data.Season = state.Season
	m.peak.State.Data.SeasonState = state.SeasonState

	m.peak.State.Data.Phase = 0
	m.peak.State.Data.PhaseState = uint32(common.PEAK_PHASE_STATE_PPS_INIT)
}

// 设置小周期状态
// @param *cl.PeakState state 状态数据
func (m *Manager) setPhaseState(state *cl.PeakState) {
	m.peak.State.Data.Phase = state.Phase
	m.peak.State.Data.PhaseState = state.PhaseState

	m.peak.State.Data.Round = goxml.PeakRound1
	m.peak.State.Data.RoundState = uint32(common.PEAK_ROUND_STATE_PRS_INIT)

	m.peak.State.Data.Open = state.Open
}

// 初始化轮次状态
// @param uint32 round 轮次
func (m *Manager) initRoundState(round uint32) {
	m.peak.State.Data.Round = round
	m.peak.State.Data.RoundState = uint32(common.PEAK_ROUND_STATE_PRS_INIT)
}

func (m *Manager) FlushState() *cl.PeakState {
	return m.peak.State.Data.Clone()
}

// 功能是否开启
func (m *Manager) isFunctionOpen(srv activity.Servicer, now int64) bool {
	info := goxml.GetData().FunctionInfoM.Index(uint32(common.FUNCID_MODULE_PEAK))
	if info == nil {
		l4g.Errorf("peakM.isFunctionOpen, no function info, id:%d", common.FUNCID_MODULE_PEAK)
		return false
	}

	return srv.ServerOpenDay(now) >= info.ServerDay
}

// 向跨服请求状态数据
func (m *Manager) requestState(srv activity.Servicer) {
	if !m.isFunctionOpen(srv, time.Now().Unix()) {
		return
	}

	srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakPushState, srv.ServerID(), &l2c.L2C_PeakPushState{})
	l4g.Infof("peakM.requestState, season:%d-%d, phase:%d-%d, round:%d-%d,",
		m.GetSeason(), m.getSeasonState(), m.GetPhase(), m.GetPhaseState(),
		m.GetRound(), m.GetRoundState())
}

func (m *Manager) GetFidAndRaisePSs() (uint32, []uint64) {
	fid := uint32(common.FORMATION_ID_FI_PEAK_1)
	var raisePSs []uint64
	phaseInfo := goxml.GetData().PeakInfoM.GetPeakPhaseByPhase(m.GetSeason(), m.GetPhase())
	if phaseInfo == nil {
		l4g.Errorf("[FATAL] peakM.GetFidAndRaisePSs: no phaseInfo, season:%d, phase:%d",
			m.GetSeason(), m.GetPhase())
	} else {
		fid = phaseInfo.Fid
		raisePSs = phaseInfo.RaisePSs
	}
	return fid, raisePSs
}
