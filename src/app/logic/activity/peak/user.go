package peak

import (
	"app/goxml"
	"app/protos/out/cl"
)

// 玩家跨服竞技场数据
type User struct {
	data *cl.PeakUser
}

func loadUser(data *cl.PeakUser) *User {
	u := &User{
		data: data,
	}
	if len(u.data.AllGuess) == 0 {
		u.data.AllGuess = make([]*cl.PeakUserGuess, 0, goxml.PeakGuessMatchCount)
	}
	return u
}

func newUser(uid uint64) *User {
	return &User{
		data: &cl.PeakUser{
			Uid:      uid,
			AllGuess: make([]*cl.PeakUserGuess, 0, goxml.PeakGuessMatchCount),
		},
	}
}

func (u *User) GetData() *cl.PeakUser {
	return u.data
}

func (u *User) Flush() *cl.PeakUser {
	return u.data.Clone()
}

func (u *User) getUID() uint64 {
	return u.data.Uid
}

func (u *User) getAllGuess() []*cl.PeakUserGuess {
	return u.data.AllGuess
}

func (u *User) addGuess(guess *cl.PeakUserGuess) {
	u.data.AllGuess = append(u.data.AllGuess, guess)
}

func (u *User) resetAllGuess() {
	u.data.AllGuess = u.data.AllGuess[:0]
}

// 根据round获取竞猜数据
func (u *User) getGuessByRound(round uint32) []*cl.PeakUserGuess {
	ret := make([]*cl.PeakUserGuess, 0, goxml.PeakGuessMatchCount)
	for _, v := range u.getAllGuess() {
		if v.Round == round {
			ret = append(ret, v)
		}
	}
	return ret
}

// 判断是否能对当前选手下注（单场比赛只能对一个选手下注）
// @param uint32 round 轮次
// @param uint32 matchID 比赛ID
// @param uint64 playerID 选手ID
// @return bool 是否能对当前选手下注
func (u *User) CanGuessThisPlayer(round, matchID uint32, playerID uint64) bool {
	for _, v := range u.getAllGuess() {
		if v.Round == round {
			if v.MatchId == matchID && v.WinnerUid != playerID {
				return false
			}
		}
	}
	return true
}

// 根据rounds获取竞猜数据
func (u *User) FlushGuessByRounds(rounds []uint32) []*cl.PeakUserGuess {
	ret := make([]*cl.PeakUserGuess, 0, goxml.PeakGuessMatchCount)
	for _, round := range rounds {
		ret = append(ret, u.getGuessByRound(round)...)
	}
	return ret
}

// 获取当前轮次，竞猜比赛id
func (u *User) GetFirstGuessMatchIDByRound(round uint32) uint32 {
	datas := u.getGuessByRound(round)
	if len(datas) == 0 {
		return 0
	}

	return datas[0].MatchId
}

// 根据round获取相关轮次的竞猜数量
// 当前轮次和前一轮次的竞猜数量
// @param round 轮次
// @return 轮次=>竞猜数量
func (u *User) CalcInvolvedGuessCountByRound(round uint32) map[uint32]uint32 {
	ret := make(map[uint32]uint32)
	for i := round; i >= goxml.PeakRound1; i-- {
		count := u.CalcUsedGuessCount(i)
		ret[i] = count

		if i != round && count > 0 {
			break
		}
	}
	return ret
}

// 统计已用竞猜次数
// @param uint32 round 轮次
// @return uint32
func (u *User) CalcUsedGuessCount(round uint32) uint32 {
	var count uint32
	for _, v := range u.getGuessByRound(round) {
		count += v.Count
	}
	return count
}

// 计算竞猜奖励
// @param uint32 round 轮次
// @param []*cl.PeakUserGuess matches 竞猜结果数据
// @return []*cl.Resource
func (u *User) calcGuessWinAward(round uint32, matches []*cl.PeakUserGuess) []*cl.Resource {
	var count uint32
	for _, v := range u.getGuessByRound(round) {
		for _, match := range matches {
			if v.MatchId == match.MatchId && v.WinnerUid == match.WinnerUid {
				count += v.Count
			}
		}
	}

	if count == 0 {
		return nil
	}
	return goxml.GetData().PeakConfigInfoM.GetGuessAward(count)
}
