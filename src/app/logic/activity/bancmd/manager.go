package bancmd

import (
	"app/logic/activity"
	"app/protos/in/gm"
	"app/protos/in/r2l"

	"gitlab.qdream.com/kit/sea/util"
)

// 协议封禁
type Manager struct {
	data map[uint32]struct{}
}

func NewManager() *Manager {
	return &Manager{
		data: make(map[uint32]struct{}),
	}
}

func (m *Manager) Init(srv activity.Servicer) {}

func (m *Manager) Load(recv *r2l.R2L_Load) {
	for _, cmd := range recv.BanCmds {
		m.data[cmd] = util.None{}
	}
}

func (m *Manager) Save(srv activity.Servicer)               {}
func (m *Manager) OnTimer(srv activity.Servicer, now int64) {}

func (m *Manager) Exist(cmd uint32) bool {
	if _, exist := m.data[cmd]; exist {
		return true
	}
	return false
}

func (m *Manager) Update(srv activity.Servicer, ban *gm.BanCmd) {
	msg := &r2l.L2R_OpBanCmds{}
	if n := len(ban.AddCmds); n > 0 {
		for _, cmd := range ban.AddCmds {
			m.data[cmd] = struct{}{}
		}
		msg.AddCmds = make([]uint32, n)
		copy(msg.AddCmds, ban.AddCmds)
	}
	if n := len(ban.DelCmds); n > 0 {
		for _, cmd := range ban.DelCmds {
			delete(m.data, cmd)
		}
		msg.DelCmds = make([]uint32, n)
		copy(msg.DelCmds, ban.DelCmds)
	}
	if msg.AddCmds != nil || msg.DelCmds != nil {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_OpBanCmds), 0, msg)
	}
}
