package ban

import (
	"app/logic/activity"
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

// 封禁
type Manager struct {
	datas map[uint64]*db.UserBan

	//数据库操作相关
	incr    int64
	freq    int64 //保存频率，单位是秒
	changes map[uint64]*db.UserBan
	deletes map[uint64]util.None
}

func NewManager() *Manager {
	return &Manager{
		freq:    60 + int64(activity.BanAccount), //nolint:mnd //60秒保存一次
		datas:   make(map[uint64]*db.UserBan),
		changes: make(map[uint64]*db.UserBan),
		deletes: make(map[uint64]util.None),
	}
}

func (m *Manager) clean(now int64) {
	for id, data := range m.datas {
		for typ, ban := range data.Ban {
			if ban.End < now {
				delete(data.Ban, typ)
			}
		}
		if len(data.Ban) == 0 {
			delete(m.datas, id)
			delete(m.changes, id)
			m.deletes[id] = util.None{}
		}
	}
}

func (m *Manager) Init(srv activity.Servicer) {
}

func (m *Manager) Load(recv *r2l.R2L_Load) {
	if len(recv.UserBans) > 0 {
		m.datas = recv.UserBans
		recv.UserBans = nil
	}
}

// 根据变动量，触发存储
func (m *Manager) checkSave(srv activity.Servicer) {
	if len(m.changes)+len(m.deletes) >= 50 { //nolint:mnd
		m.Save(srv)
	}
}

func (m *Manager) Save(srv activity.Servicer) {
	changes := len(m.changes)
	deletes := len(m.deletes)
	if changes > 0 || deletes > 0 {
		msg := &r2l.L2R_SaveUserBan{}
		if changes > 0 {
			msg.Changes = make([]*db.UserBan, 0, changes)
			for _, change := range m.changes {
				msg.Changes = append(msg.Changes, change)
				delete(m.changes, change.Id)
			}
		}
		if deletes > 0 {
			msg.Deletes = make([]uint64, 0, deletes)
			for id := range m.deletes {
				msg.Deletes = append(msg.Deletes, id)
				delete(m.deletes, id)
			}
		}
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveUserBan), 0, msg)
	}
}

func (m *Manager) get(id uint64) *db.UserBan {
	return m.datas[id]
}

func (m *Manager) Insert(id uint64, ban *db.GMBan) bool {
	data := m.get(id)
	if data == nil {
		data = &db.UserBan{
			Id:  id,
			Ban: make(map[uint32]*db.GMBan),
		}
		m.datas[data.Id] = data
	}
	data.Ban[ban.Type] = ban
	m.changes[data.Id] = data
	return true
}

func (m *Manager) GetUserBan(id uint64) *db.UserBan {
	return m.get(id)
}

func (m *Manager) Delete(id uint64, typ uint32) bool {
	if data := m.get(id); data != nil {
		delete(data.Ban, typ)
		if len(data.Ban) == 0 {
			delete(m.datas, id)
			m.deletes[id] = util.None{}
		} else {
			m.changes[id] = data
		}
		return true
	}
	return false
}

// 是否封禁期间
func DuringBanTime(ban *db.GMBan, now int64) bool {
	return ban != nil && ban.Start <= now && now <= ban.End
}

// 检查封禁
// @param id - 玩家id
// @param typ - 封禁类型 1:发言 2:登录 3:自言自语
// @return bool - 是否封禁
func (m *Manager) Check(id uint64, typ uint32) bool {
	if data := m.get(id); data != nil {
		return DuringBanTime(data.Ban[typ], time.Now().Unix())
	}
	return false
}

func (m *Manager) OnTimer(srv activity.Servicer, now int64) {
	m.incr++
	if m.incr%m.freq == 0 {
		m.clean(now)
		//定时保存
		m.Save(srv)
	} else {
		//根据变动量，触发存储
		m.checkSave(srv)
	}
}

// 获取正被禁言的id列表
// @return map[uint32]*cl.BanUsers - 禁言id列表
func (m *Manager) BanChatIds() map[uint32]*cl.BanUsers {
	now := time.Now().Unix()
	ret := make(map[uint32]*cl.BanUsers, 2) //nolint:mnd
	for _, data := range m.datas {
		for _, typ := range []uint32{
			uint32(common.BAN_TYPE_CHAT)} {
			banInfo := data.Ban[typ]
			if DuringBanTime(banInfo, now) {
				if ret[typ] == nil {
					ret[typ] = &cl.BanUsers{Users: make([]*cl.BanInfo, 0, len(m.datas))}
				}
				ret[typ].Users = append(ret[typ].Users, &cl.BanInfo{
					Id:    data.Id,
					Start: banInfo.Start,
					End:   banInfo.End,
				})
			}
		}
	}
	return ret
}
