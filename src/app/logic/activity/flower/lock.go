package flower

import (
	"gitlab.qdream.com/kit/sea/time"
)

type TimeLock struct {
	interval    int64
	snatchLocks map[uint64]int64
	occupyLocks map[uint64]int64
}

func newTimeLock(interval int64) *TimeLock {
	return &TimeLock{
		interval:    interval,
		snatchLocks: make(map[uint64]int64),
		occupyLocks: make(map[uint64]int64),
	}
}

// 加锁
// @param - 唯一id
// @return bool - true: 成功 false: 失败
func (t *TimeLock) TryLock(id uint64) bool {
	now := time.Now().Unix()
	tm := t.snatchLocks[id]
	if tm >= now {
		return false
	}
	t.snatchLocks[id] = t.interval + now
	return true
}

// 解锁
func (t *TimeLock) UnLock(id uint64) {
	delete(t.snatchLocks, id)
}

// 据点模式加锁
// @param - 唯一id
// @return bool - true: 成功 false: 失败
func (t *TimeLock) TryLockOccupy(id uint64) bool {
	now := time.Now().Unix()
	tm := t.occupyLocks[id]
	if tm >= now {
		return false
	}
	t.occupyLocks[id] = t.interval + now
	return true
}

// 据点模式解锁
func (t *TimeLock) UnLockOccupy(id uint64) {
	delete(t.occupyLocks, id)
}
