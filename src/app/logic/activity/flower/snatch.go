package flower

import (
	"app/logic/activity"
	"app/protos/out/cl"
	"gitlab.qdream.com/kit/sea/time"
)

//掠夺模式 - 抢夺相关逻辑

func newSnatch() *cl.FlowerSnatch {
	return &cl.FlowerSnatch{
		Ids:  make([]uint64, 0, activity.FlowerAssistMaxCount),
		Bots: make([]*cl.FlowerSnatchEnemy, 0, activity.FlowerAssistMaxCount),
	}
}

func (f *Flower) GetAssist() *cl.FlowerSnatch { return f.data.Snatch }

func (f *Flower) SetAssist(data *cl.FlowerSnatch) {
	f.data.Snatch = data
}

// 获取已搜到玩家id
func (f *Flower) IsInExistPool(uid uint64) bool {
	for _, v := range f.GetAssist().Ids {
		if v == uid {
			return true
		}
	}
	return false
}

func (f *Flower) UpdateExistPool(players, bots []uint64) {
	f.GetAssist().Ids = f.GetAssist().Ids[:0]
	if len(players) > 0 {
		f.GetAssist().Ids = append(f.GetAssist().Ids, players...)
	}
	if len(bots) > 0 {
		f.GetAssist().Ids = append(f.GetAssist().Ids, bots...)
	}
}

func (f *Flower) UpdateBotsSnapShot(bots []*cl.FlowerSnatchEnemy) {
	f.GetAssist().Bots = f.GetAssist().Bots[:0]
	f.GetAssist().Bots = append(f.GetAssist().Bots, bots...)
}

func (f *Flower) ClearBotsSnapShot() {
	f.GetAssist().Bots = f.GetAssist().Bots[:0]
}

func (f *Flower) UpdateSearchTime() {
	f.GetAssist().Tm = time.Now().Unix()
}

func (f *Flower) GetBotsSnapShot(uid uint64) *cl.FlowerSnatchEnemy {
	for _, SnapShot := range f.GetAssist().Bots {
		if SnapShot.Snapshot.Id == uid {
			return SnapShot
		}
	}
	return nil
}

func (f *Flower) BotHasBeAssist(uid uint64) bool {
	for _, SnapShot := range f.GetAssist().Bots {
		if SnapShot.Snapshot.Id == uid {
			return SnapShot.BeAssist
		}
	}
	return true
}

func (f *Flower) SetBotBeAssist(uid uint64) {
	for _, SnapShot := range f.GetAssist().Bots {
		if SnapShot.Snapshot.Id == uid {
			SnapShot.BeAssist = true
		}
	}
}
