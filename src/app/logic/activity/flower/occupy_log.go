package flower

import (
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type OccupyLogManager struct {
	UID  uint64
	data *cl.FlowerNewLogTip
}

func loadOccupyLogManager(uid uint64, logTip *cl.FlowerNewLogTip) *OccupyLogManager {
	olm := &OccupyLogManager{
		UID:  uid,
		data: logTip,
	}
	return olm
}

// 实现LogOwner接口
func (lm *OccupyLogManager) GetUniqID() uint64   { return lm.UID }
func (lm *OccupyLogManager) GetLogIndex() uint32 { return lm.data.LogIndexId }
func (lm *OccupyLogManager) SetLogIndex(index uint32) {
	lm.data.LogIndexId = index
}

func (lm *OccupyLogManager) GetNewLogTip() bool { return lm.data.NewLogTip }

func (lm *OccupyLogManager) SetNewLogTip(flag bool) {
	lm.data.NewLogTip = flag
}

type OccupyLog struct {
	data *cl.FlowerOccupyLog
}

func (l *OccupyLog) GetID() uint32 {
	return l.data.Id
}

func (l *OccupyLog) SetID(id uint32) {
	l.data.Id = id
	l4g.Debugf("OccupyLog.SetID: id:%d ", id)
}

func (l *OccupyLog) GetUniqID() uint64 {
	return l.data.LogId
}

func (l *OccupyLog) GetLogType() uint32 {
	return l.data.Detail.Type
}

func (l *OccupyLog) GetJungle() uint32 {
	return l.data.Detail.JungleId
}

func (l *OccupyLog) GetFlowerbedPos() uint32 {
	return l.data.Detail.FlowerbedPos
}

func (l *OccupyLog) IsRevenge() bool {
	return l.data.Revenge
}

func (l *OccupyLog) GetEnemyID() uint64 {
	return l.data.Enemy.Id
}

func (l *OccupyLog) Revenge() {
	l.data.Revenge = true
}

func (l *OccupyLog) GetCreatTm() int64 {
	return l.data.Tm
}

func (l *OccupyLog) Flush() *cl.FlowerOccupyLog {
	return l.data.Clone()
}

func NewFlowerOccupyLogDetail(logType common.FLOWER_LOG, jungle, pos uint32) *cl.FlowerLogDetail {
	return &cl.FlowerLogDetail{
		Type:         uint32(logType),
		JungleId:     jungle,
		FlowerbedPos: pos,
	}
}

func NewFlowerOccupyLog(enemy *cl.MiniUserSnapshot, id uint64, detail *cl.FlowerLogDetail) *OccupyLog {
	return &OccupyLog{
		data: &cl.FlowerOccupyLog{
			Enemy:  enemy,
			Tm:     time.Now().Unix(),
			LogId:  id,
			Detail: detail,
		},
	}
}

func NewOccupyLogFromDB(data *cl.FlowerOccupyLog) *OccupyLog {
	return &OccupyLog{
		data: data,
	}
}
