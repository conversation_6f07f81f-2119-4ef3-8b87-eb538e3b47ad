package flower

import (
	"gitlab.qdream.com/kit/sea/skiplist"
)

type Rank struct {
	*skiplist.Set
}

func NewFlowerRank() *Rank {
	return &Rank{
		Set: skiplist.NewSet(&ScoreCmp{}),
	}
}

func (r *Rank) Update(v skiplist.Valuer) {
	if ele := r.GetElement(v.Key()); ele != nil {
		r.Delete(ele)
	}
	r.Insert(v)
}

type ScoreValue struct {
	ID    uint64
	Power int64
}

func (s *ScoreValue) Key() uint64   { return s.ID }
func (s *ScoreValue) Score() uint64 { return uint64(s.Power) }

type ScoreCmp struct {
}

func (s *ScoreCmp) CmpKey(v1 interface{}, v2 interface{}) int {
	s1 := v1.(*ScoreValue).Key()
	s2 := v2.(*ScoreValue).Key()
	switch {
	case s1 < s2:
		return -1
	case s1 == s2:
		return 0
	default:
		return 1
	}
}

func (s *ScoreCmp) CmpScore(v1 interface{}, v2 interface{}) int {
	lv1 := v1.(*ScoreValue)
	lv2 := v2.(*ScoreValue)
	switch {
	case lv1.Score() < lv2.Score():
		return 1
	case lv1.Score() == lv2.Score():
		if lv1.ID < lv2.ID {
			return -1
		} else if lv1.ID > lv2.ID {
			return 1
		}
		return 0
	default:
		return -1
	}
}
