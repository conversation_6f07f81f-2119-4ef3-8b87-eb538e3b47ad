package activity

import "gitlab.qdream.com/kit/sea/util"

type ID int

const (
	Friend     ID = 0 //好友
	Arena      ID = 1 //竞技场
	BanAccount ID = 2 //封禁
	Guild      ID = 4 //公会
	MiragePass ID = 5 //个人boss过关统计
	// Forest        ID = 6  //矿战
	Report ID = 7 //战报
	//GuildChat     ID = 8  //公会聊天
	//WorldChat     ID = 9  //世界聊天
	BanCmd             ID = 10 //封/解禁协议
	SystemChat         ID = 11 //系统聊天
	ChatLikeList       ID = 12 //聊天点赞列表
	Wrestle            ID = 13 //神树争霸
	Flower             ID = 14 //密林 - 0.9.8
	MonthTasksMsg      ID = 15 //全民无双
	WorldBoss          ID = 16 // 世界boss
	TowerSeason        ID = 17 // 百塔
	DisorderLand       ID = 18 // 失序空间
	Peak               ID = 19 // 巅峰竞技场
	GST                ID = 20 // 公会战
	PeopleGroupPackage ID = 21
	SeasonArena        ID = 22 // 赛季竞技场
	HotRank            ID = 23 // 英雄热度榜
	ActivityTower      ID = 24 // 冲榜活动
	SeasonCompliance   ID = 25 // 赛季冲榜
	SeasonMap          ID = 26 // 赛季地图
	TowerPokemon       ID = 27
	BalanceArena       ID = 28
	MAX                ID = 29 // 最大
)

// 好友关系
const (
	RelationStranger = 0 //非好友
	RelationFriend   = 1 //好友
	RelationBlack    = 2 //黑名单
)

const (
	FriendRequestsMaxNum = 5      //好友申请条数
	SevenDaySeconds      = 604800 //7天秒
	ArenaPosTen          = 10
	ArenaPosTwenty       = 20
	FriendSaveInterval   = 500 * 1e6 //触发好友存储间隔毫秒
	FriendSaveNum        = 100       //触发好友存储数量
)

// 好友点赞状态
const (
	FriendLikeNone = 0 //无
	FriendLikeSnd  = 1 //点赞或被点赞
	FriendLikeRcv  = 2 //已领取点赞
)

// 竞技场
const (
	FirstSeason              uint32 = 1         //首个赛季
	ArenaSaveInterval               = 500 * 1e6 //触发普通竞技场存储间隔毫秒
	ArenaSaveNum                    = 100       //触发普通竞技场存储数量
	ArenaMatchStageHigh      int    = 0         //匹配所处阶段 - 高区
	ArenaMatchStageMid       int    = 1         //匹配所处阶段 - 中区
	ArenaMatchStageLow       int    = 2         //匹配所处阶段 - 低区
	ArenaFightLockSec        int64  = 10        //战斗锁定时间
	ArenaPowerUpdateRate     int    = 500       //战力变化超过5%才更新 - 万分比
	ArenaMatchPlayerMaxCount int    = 200       //单次匹配玩家个数上限
	ArenaSearchExistNum      int    = 5         //搜索暂存数量，用于去重计算
	ArenaSeasonTopNum        int    = 3         //保存赛季前x名玩家数据
	ArenaSeasonTopDefaultKey uint32 = 1         //保存赛季前x名玩家数据的默认key
)

// 竞技场任务奖励类型
const (
	ArenaTaskAwardTypeNone   uint32 = iota
	ArenaTaskAwardTypeAll           //全部
	ArenaTaskAwardTypeSeason        //赛季
	ArenaTaskAwardTypeLife          //成就
)

// 竞技场匹配规则
const (
	ArenaMatchRuleNormal uint32 = iota //常规
	ArenaMatchRuleSeason               //赛季前x次
	ArenaMatchRuleDefeat               //连败超过x次
)

// 公会
const (
	GuildSaveInterval          = 500 * 1e6 //触发公会存储间隔毫秒
	GuildSaveNum               = 100       //触发公会存储数量
	GuildDungeonInitialChapter = 1         // 公会副本初始章节
	GuildDungeonWeeklyReset    = 1         // 公会副本周重置
	GuildDungeonMonthlyReset   = 2         // 公会副本月重置
	GuildDailyDonateReset      = 3         // 公会每日捐献重置
	GuildActivityPointLen      = 7         // 公会每周活动值长度
)

var GuildDungeonRestType = map[uint32]struct{}{
	GuildDungeonWeeklyReset:  {},
	GuildDungeonMonthlyReset: {},
}

var GuildResetType = map[uint32]struct{}{
	GuildDailyDonateReset: {},
}

// 个人boss
const (
	MiragePassSaveNum      = 100       //触发个人boss过关统计存储数量
	MiragePassSaveInterval = 500 * 1e6 //触发个人boss过关统计存储间隔毫秒
)

// 战报
const (
	ReportSaveNum             = 50        //触发批量存储的数量
	ReporttSaveInterval       = 500 * 1e6 //触发战报存储间隔毫秒
	ReportExpireTm      int64 = 604800    //战报过期时间7天
)

const (
	SaveCommandMaxExecuteTime = 5000000 //save的耗时统计
)

// 世界聊天
const (
	WorldMsgSaveNum      = 50        // 触发世界聊天消息存储数量
	WorldMsgSaveInterval = 500 * 1e6 // 触发世界聊天消息存储间隔毫秒
)

// 公会聊天
const (
	GuildMsgSaveNum      = 50        // 触发公会聊天存储数量
	GuildMsgSaveInterval = 500 * 1e6 // 触发公会聊天消息存储间隔毫秒
	GuildMsgLockTm       = 2         // 公会聊天上锁的时间 - 2秒（用于异步操作加锁）
)

// 系统聊天
const (
	SystemMsgSaveNum      = 50        // 触发系统聊天消息存储数量
	SystemMsgSaveInterval = 500 * 1e6 // 触发系统聊天消息存储间隔毫秒
)

// 聊天点赞
const (
	ChatLikeListSaveNum      = 50        // 触发聊天点赞存储数量
	ChatLikeListSaveInterval = 500 * 1e6 // 触发聊天点赞存储间隔毫秒
)

// 神树争霸
const (
	WrestleSaveInterval                   = 500 * 1e6 //触发普通竞技场存储间隔毫秒
	WrestleSaveNum                        = 100       //触发普通竞技场存储数量
	WrestleUpdateNewTopUserInterval       = 300       // 获取头部玩家间隔秒
	WrestleFightLockSec             int64 = 5         //战斗锁定时间
)

// 密林 - 0.9.8
const (
	FlowerPowerUpdateInterval      int64 = 1800 //战力更新时间间隔
	FlowerPowerUpdateIncreaseLimit int   = 500  //战力增长超过5%才更新 - 万分比

	FlowerSearchUpChangePct     int = 1000 //向上搜索范围，万分比，战力加50%
	FlowerSearchDownChangePct   int = 2000 //向下搜索范围，万分比，战力减20%
	FlowerSearchExpandNum       int = 5    //搜索范围，扩大次数
	FlowerSearchUpExpandNum     int = 2    //搜索范围，向上扩大次数
	FlowerSearchExistNum        int = 5    //搜索暂存数量，用于去重计算
	FlowerSearchNum             int = 100  //单次搜索数量上限
	FlowerSearchLoopNumPerRange int = 2    //每个搜索范围的搜索次数
	FlowerMaxPowerExpandRound   int = 3    //最大战力开始上涨的回合

	FlowerLockSeconds    = 30 //战斗锁定时间
	FlowerAssistMaxCount = 4  //协助模式，可搜到玩家数量
	FlowerOccupyAwardCap = 3  //据点模式，奖励类型初始数量

	FlowerSaveNum                     = 300       //触发批量存储的数量
	FlowerSaveInterval                = 500 * 1e6 //触发密林存储间隔毫秒
	FlowerFightLockSec         int64  = 10        //战斗锁定时间
	FlowerSearchPlayerMaxCount uint32 = 11        //单次匹配玩家个数上限

	FlowerOccupyAwardCheckFreq int64 = 7200              //据点模式奖励过期检查频率
	FlowerOccupyAwardKeepSec   int64 = util.DaySecs * 30 //据点模式奖励保留时间

	FlowerRequestJungleLimit = 10 //单次请求丛林数量上限
)

// 全民无双消息
const (
	MonthTasksMsgSaveNum      = 50        // 触发全民无双消息存储数量
	MonthTasksMsgSaveInterval = 500 * 1e6 // 触发公会聊天消息存储间隔毫秒
	MonthTasksMsgNum          = 10        // 全民无双消息保存数量
)

// 世界boss
const (
	WorldBossFuncStatusOpen  uint32 = 0 // 功能打开
	WorldBossFuncStatusClose uint32 = 1 // 功能关闭
)

// 巅峰竞技场
const (
	PeakSaveInterval = 500 * 1e6 //触发普通竞技场存储间隔毫秒
	PeakSaveNum      = 100       //触发普通竞技场存储数量
)

// 通用
const (
	CommonSaveFreq = 60 //定时器检查保存，频率基数
)

const (
	GSTSaveInterval = 500 * 1e6 //触发公会战存储间隔毫秒
	GSTSaveUserNum  = 100
)

// 人群包
const (
	PeopleGroupPackageSaveNum      = 50        // 触发人群包存储数量
	PeopleGroupPackageSaveInterval = 500 * 1e6 // 触发人群包存储间隔毫秒
)

// 竞技场
const (
	SeasonArenaSaveInterval       = 500 * 1e6 //触发普通竞技场存储间隔毫秒
	SeasonArenaSaveNum            = 100       //触发普通竞技场存储数量
	SeasonArenaFightLockSec int64 = 5         //战斗锁定时间
)

// 公平竞技场
const (
	BalanceArenaSaveInterval = 500 * 1e6 //触发公平竞技场存储间隔毫秒
	BalanceArenaSaveNum      = 100       //触发公平竞技场存储数量
)
