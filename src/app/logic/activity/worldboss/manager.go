package worldboss

import (
	"app/goxml"
	"app/logic/activity"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/r2l"

	l4g "github.com/ivanabc/log4go"
)

type Manager struct {
	data *db.WorldBossSettle
}

func NewManager() *Manager {
	return &Manager{
		data: &db.WorldBossSettle{
			SettleData: &db.WorldBossSettleDataMap{
				Data: make(map[uint32]*db.WorldBossSettleData),
			},
		},
	}
}

func (m *Manager) Load(data *r2l.R2L_Load) {
	if data != nil && data.WorldBoss != nil && data.WorldBoss.SettleData != nil && data.WorldBoss.SettleData.Data != nil {
		m.data = data.WorldBoss
	}
}

func (m *Manager) Init(srv activity.Servicer) {}

func (m *Manager) Save(srv activity.Servicer) {
	data := &r2l.L2R_SaveWorldBossSettle{
		SettleData: m.data.Clone(),
	}

	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveWorldBossSettle), 0, data)
}

// OnTimer
//
//	  结算策略：
//	         由跨服开启结算行为，待结算完成后，由各游戏服主动请求去获取结算数据
//			 游戏服获取结算数据成功，进行发奖，并记录完成的标识
func (m *Manager) OnTimer(srv activity.Servicer, now int64) {
	lastExpireCfg := goxml.GetData().WorldbossInfoM.GetLastExpireWorldBoss(now)
	if lastExpireCfg == nil {
		return
	}
	// 本服玩家没有参与上期worldBoss，不用结算
	settleData, exist := m.data.SettleData.Data[lastExpireCfg.Id]
	if !exist || !settleData.IsJoin {
		return
	}
	m.handleSettleReq(srv, settleData, lastExpireCfg.Id)
}

func (m *Manager) handleSettleReq(srv activity.Servicer, settleData *db.WorldBossSettleData, sysID uint32) {
	// 已成功获取结算数据
	if settleData.IsSettle {
		return
	}
	l4g.Infof("send worldBoss settle request")
	msg := &l2c.L2C_WorldBossSettle{
		Id: sysID,
	}
	if !srv.SendCmdToCross(l2c.ID_MSG_L2C_WorldBossSettle, srv.ServerID(), msg) {
		l4g.Errorf("L2C_WorldBossSettle: cross maintain")
	}
}

func (m *Manager) UpdateSettleStatus(srv activity.Servicer, id uint32) {
	if m.data.SettleData == nil {
		return
	}

	settleData := m.data.SettleData.Data[id]
	if settleData == nil {
		return
	}

	settleData.IsSettle = true
	m.Save(srv)
}

func (m *Manager) GetSettleStatus(id uint32) bool {
	if m.data.SettleData == nil {
		return false
	}
	settleData := m.data.SettleData.Data[id]
	if settleData == nil {
		return false
	}

	return settleData.IsSettle
}

// Join
// IsJoin：玩家参与标识
//
//	如果玩家没有参与世界boss玩法，在玩法结束后，不向跨服发起结算请求
func (m *Manager) Join(srv activity.Servicer, id uint32) {
	isSave := false
	settleData := m.data.SettleData.Data[id]
	if settleData == nil {
		settleData = &db.WorldBossSettleData{}
		m.data.SettleData.Data[id] = settleData
		isSave = true
	}

	if !settleData.IsJoin {
		settleData.IsJoin = true
		isSave = true
	}

	if isSave {
		m.Save(srv)
	}
}

func (m *Manager) GetJoinStatus(sysID uint32) bool {
	settleData := m.data.SettleData.Data[sysID]
	if settleData == nil {
		return false
	}

	return settleData.IsJoin
}
