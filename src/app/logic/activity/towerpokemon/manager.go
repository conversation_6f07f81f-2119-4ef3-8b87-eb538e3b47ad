package towerpokemon

import (
	"app/goxml"
	"app/logic/activity"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/r2l"

	l4g "github.com/ivanabc/log4go"
)

type Manager struct {
	data *db.TowerPokemonReset
}

func NewManager() *Manager {
	m := &Manager{}
	return m
}

func (m *Manager) Init(activity.Servicer) {
}

func (m *Manager) Load(msg *r2l.R2L_Load) {
	m.data = msg.TowerPokemonReset
	if m.data == nil {
		m.data = &db.TowerPokemonReset{}
	}
}

func (m *Manager) Save(srv activity.Servicer) {
	data := &r2l.L2R_SaveTowerPokemonReset{
		Data: m.data.Clone(),
	}

	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveTowerPokemonReset), 0, data)
}

func (m *Manager) OnTimer(srv activity.Servicer, now int64) {
	seasonInfo := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(now)
	if seasonInfo == nil {
		return
	}
	//检查奖励结算
	if m.isNeedAward(now, seasonInfo) {
		m.award(srv, seasonInfo)
	}
}

// 是否需要补发奖励
func (m *Manager) isNeedAward(now int64, seasonInfo *goxml.SeasonInfoExt) bool {
	if seasonInfo.Id == m.data.RewardedSeason {
		return false
	}
	if now >= seasonInfo.SettlementTm {
		return true
	}
	return false
}

func (m *Manager) SetRewardedSeason(srv activity.Servicer, season uint32) {
	m.data.RewardedSeason = season
	m.Save(srv)
}

// 奖励
func (m *Manager) award(srv activity.Servicer, seasonInfo *goxml.SeasonInfoExt) {
	msg := &l2c.L2C_RankGetForReset{
		RankId:    goxml.TowerPokemonRank,
		Last:      true,
		BeginRank: 1,
		EndRank:   goxml.GetData().TowerPokemonRankInfoM.GetLastRewardRank(),
	}

	msg.ResetTime = uint64(seasonInfo.SettlementTm)

	srv.SendCmdToCross(l2c.ID_MSG_L2C_RankGetForReset, srv.ServerID(), msg)
	l4g.Infof("[TowerPokemon] request cross rank ")
}

func (m *Manager) GetRewardedSeason() uint32 {
	return m.data.RewardedSeason
}
