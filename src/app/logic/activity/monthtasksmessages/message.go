package monthtasksmessages

import (
	"app/logic/activity"
	"app/protos/out/cl"
)

type PointMessage cl.MonthTasksMsg

func (p *PointMessage) addMessage(msg *cl.Message) {
	if len(p.Messages) < activity.MonthTasksMsgNum {
		p.Messages = append(p.Messages, msg)
	} else {
		p.delFirstMsg() // 消息顺序保存，index = 0 的消息是最早的消息，需要删掉
		p.Messages[len(p.Messages)-1] = msg
	}
}

// 删除超过保存限制的消息
func (p *PointMessage) delFirstMsg() {
	if len(p.Messages) > 0 {
		copy(p.Messages[0:], p.Messages[1:])
	}
}
