package monthtasksmessages

import (
	"app/logic/activity"
	"app/protos/in/r2l"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type Manager struct {
	msgMap       map[uint32]*cl.MonthTasksMsg
	incr         int64 // 递增id
	freq         int64 // 保存频率，单位：秒
	lastSaveNano int64 // 上次存储时间戳，单位：纳秒

	changes map[uint32]struct{}
	deletes map[uint32]struct{}
}

func NewManager() *Manager {
	return &Manager{
		msgMap:  make(map[uint32]*cl.MonthTasksMsg),
		freq:    60 + int64(activity.MonthTasksMsg),
		changes: make(map[uint32]struct{}),
		deletes: make(map[uint32]struct{}),
	}
}

func (m *Manager) Load(msg *r2l.R2L_Load) {
	if msg.MonthTasksMsg != nil {
		m.msgMap = msg.MonthTasksMsg
	}
}

func (m *Manager) Init(srv activity.Servicer) {
	m.lastSaveNano = time.Now().UnixNano()
}

func (m *Manager) Save(srv activity.Servicer) {
	var success bool
	msg := &r2l.L2R_SaveMonthTasksMsg{
		Changes: make([]*cl.MonthTasksMsg, 0, len(m.changes)),
	}
	start := time.AccurateNow()
	if len(m.changes) > 0 {
		for id := range m.changes {
			monthTasksMsg := m.msgMap[id]
			if monthTasksMsg == nil {
				continue
			}
			msg.Changes = append(msg.Changes, monthTasksMsg.Clone())
		}
		success = true
		m.changes = make(map[uint32]struct{})
	}

	if len(m.deletes) > 0 {
		msg.Deletes = make([]uint32, 0, len(m.deletes))
		for id := range m.deletes {
			msg.Deletes = append(msg.Deletes, id)
		}
		success = true
		m.deletes = make(map[uint32]struct{})
	}

	if success {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveMonthTasksMsg), 0, msg)
		m.lastSaveNano = time.Now().UnixNano()
		delay := time.Since(start)
		if int64(delay) > activity.SaveCommandMaxExecuteTime {
			l4g.Errorf("activity monthTasksMsg cmd: save execute time: %v", delay)
		}
	}
}

func (m *Manager) OnTimer(srv activity.Servicer, now int64) {
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(srv)
	} else {
		m.checkSave(srv)
	}
}

// 检查是否需要save
func (m *Manager) checkSave(srv activity.Servicer) {
	if len(m.changes) >= activity.MonthTasksMsgSaveNum {
		nowNano := time.Now().UnixNano()
		if nowNano-m.lastSaveNano >= activity.MonthTasksMsgSaveInterval {
			m.Save(srv)
		}
	}
}

func (m *Manager) GetMonthTasksMsg(id uint32) *PointMessage {
	if msg, exist := m.msgMap[id]; exist {
		return (*PointMessage)(msg)
	}

	return nil
}

func (m *Manager) SetChange(id uint32) {
	m.changes[id] = struct{}{}
}

func (m *Manager) SetDelete(id uint32) {
	delete(m.msgMap, id)
	m.deletes[id] = struct{}{}
}

func (m *Manager) AddMessage(id uint32, message *cl.Message) {
	pointMessage := m.GetMonthTasksMsg(id)
	if pointMessage == nil {
		pointMessage = m.newMonthTasksMsg(id)
	}
	pointMessage.addMessage(message)
	m.SetChange(id)
}

func (m *Manager) FlushAllMessages(ids []uint32) []*cl.MonthTasksMsg {
	returnData := make([]*cl.MonthTasksMsg, 0, len(ids))
	for _, id := range ids {
		msg := m.msgMap[id]
		if msg == nil {
			continue
		}
		returnData = append(returnData, msg.Clone())
	}
	return returnData
}

func (m *Manager) newMonthTasksMsg(id uint32) *PointMessage {
	monthTasksMsg := &cl.MonthTasksMsg{
		Id:       id,
		Messages: make([]*cl.Message, 0, activity.MonthTasksMsgNum),
	}
	m.msgMap[id] = monthTasksMsg
	return (*PointMessage)(monthTasksMsg)
}
