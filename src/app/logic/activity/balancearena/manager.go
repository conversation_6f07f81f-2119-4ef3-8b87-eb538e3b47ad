package balancearena

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/actm"
	"app/logic/character"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"encoding/json"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type Manager struct {
	//缓存数据
	CrossState     *cl.BalanceArenaState //玩法状态
	crossConnected bool

	//落地数据
	balanceArena *db.BalanceArena

	//users map[uint64]*BalanceArenaUser //玩家数据
	//changes      map[uint64]*cl.BalanceArenaUser
	//deletes      map[uint64]struct{}

	//数据库操作相关
	incr         int64
	freq         int64 //保存频率，单位是秒
	lastSaveNano int64 //上次存储时间戳

	change bool
}

func NewManager() *Manager {
	return &Manager{
		//users:   make(map[uint64]*BalanceArenaUser),
		//changes: make(map[uint64]*cl.BalanceArenaUser),
		//deletes: make(map[uint64]struct{}),
		freq: 60 + int64(activity.BalanceArena), //nolint:mnd
	}
}

//********* 实现logic.Activity *********

func (m *Manager) Load(data *r2l.R2L_Load) {
	//for _, user := range data.BalanceArenaUser {
	//	if user == nil {
	//		continue
	//	}
	//	m.users[user.Id] = newUser(user)
	//}
	m.balanceArena = data.BalanceArena
	if m.balanceArena == nil {
		m.balanceArena = &db.BalanceArena{}
	}
}

func (m *Manager) Init(servicer activity.Servicer) {
	m.lastSaveNano = time.Now().UnixNano()
}

func (m *Manager) OnTimer(servicer activity.Servicer, i int64) {
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(servicer)
	} else {
		m.checkSave(servicer)
	}
}

func (m *Manager) Save(servicer activity.Servicer) {
	var needSave bool
	msg := &r2l.L2R_SaveBalanceArena{}
	if m.change {
		msg.BalanceArena = m.balanceArena.Clone()
		needSave = true
		m.change = false
	}

	if needSave {
		m.lastSaveNano = time.Now().UnixNano()
		servicer.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveBalanceArena), 0, msg)
	}
}

func (m *Manager) checkSave(srv activity.Servicer) {
	if m.change {
		nowNano := time.Now().UnixNano()
		if nowNano-m.lastSaveNano >= activity.BalanceArenaSaveInterval {
			l4g.Infof("balanceArena.checkSave.interval:%d", nowNano-m.lastSaveNano)
			m.Save(srv)
		}
	}
}

//********* 实现logic.Activity *********

//********* 玩法状态 *********

func (m *Manager) CrossClosed(servicer activity.Servicer) {
	l4g.Info("balanceArena.CrossClosed. ")
	m.crossConnected = false
}

func (m *Manager) IsCrossConnected() bool {
	return m.crossConnected
}

func (m *Manager) CrossConnected(servicer activity.Servicer) {
	l4g.Infof("balanceArena.CrossConnected")
	m.crossConnected = true

	m.PullState(servicer)
}

func (m *Manager) PullState(srv activity.Servicer) {
	if !m.isFunctionOpen(srv, time.Now().Unix()) {
		l4g.Debugf("balanceArena.PullState: function not open.")
		return
	}

	srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetSta, srv.ServerID(), &l2c.L2CS_BalanceArenaGetSta{})
	l4g.Infof("balanceArena.PullState serverId:%d ", srv.ServerID())
}

func (m *Manager) SetCrossState(sta *cl.BalanceArenaState) {
	m.CrossState = sta
}

func (m *Manager) GetCrossState() *cl.BalanceArenaState {
	if m.CrossState == nil {
		return &cl.BalanceArenaState{}
	}
	return m.CrossState.Clone()
}

//********* 玩法状态 *********

//********* 玩家数据 *********
/*
func (m *Manager) AddUser(user *BalanceArenaUser) *BalanceArenaUser {
	if user == nil || user.GetData() == nil {
		return nil
	}
	_, exist := m.users[user.GetData().GetId()]
	if exist {
		return nil
	}
	m.users[user.GetData().GetId()] = user
	return user
}

// 获取游戏服玩家数据时，如果不存在，构造一个
func (m *Manager) GetUser(uid uint64, crossData *cr.BalanceArena) *BalanceArenaUser {
	user, exist := m.users[uid]
	if !exist {
		if crossData == nil {
			return nil
		}
		bUser := m.AddUser(newUser(&cl.BalanceArenaUser{
			Id: uid,
		}))
		bUser.Init(crossData)
		m.SetChangeUser(bUser)
		return bUser
	}
	return user
}

func (m *Manager) SetChangeUser(user *BalanceArenaUser) {
	m.users[user.data.Id] = user
	m.changes[user.data.Id] = user.data
}

func (m *Manager) DeleteUser(user *BalanceArenaUser) {
	uid := user.data.Id
	m.deletes[uid] = struct{}{}
	delete(m.changes, uid)
	delete(m.users, uid)
}

*/
//********* 玩家数据 *********

// 功能是否开启
func (m *Manager) isFunctionOpen(srv activity.Servicer, now int64) bool {
	info := goxml.GetData().FunctionInfoM.Index(uint32(common.FUNCID_MODULE_BALANCE_ARENA))
	if info == nil {
		l4g.Errorf("balanceArena.isFunctionOpen, no function info, id:%d", common.FUNCID_MODULE_BALANCE_ARENA)
		return false
	}

	return srv.ServerOpenDay(now) >= info.ServerDay
}

func (m *Manager) GetRewardSta() *db.BalanceArenaReward {
	if m.balanceArena.Reward == nil {
		m.balanceArena.Reward = &db.BalanceArenaReward{}
	}
	if m.balanceArena.Reward.State == nil {
		m.balanceArena.Reward.State = &cl.BalanceArenaState{}
	}
	return m.balanceArena.Reward
}

func (m *Manager) SettleReward(srv activity.Servicer, req *l2c.CS2L_BalanceArenaRankSettle) {
	rewardSta := m.GetRewardSta()
	rewarded := false
	if rewardSta.State.SeasonId == req.State.SeasonId {
		switch req.State.Phase {
		case uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH):
			if rewardSta.BigGroupRewarded && rewardSta.State.Round == req.State.Round {
				rewarded = true
			}
		case uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH):
			if rewardSta.SmallGroupRewarded && rewardSta.State.Round == req.State.Round {
				rewarded = true
			}
		case uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_3):
			if rewardSta.EliminationRewarded && rewardSta.State.Round == req.State.Round {
				rewarded = true
			}
		case uint32(common.BALANCE_ARENA_STAGE_BAS_SHOW):
			if rewardSta.SeasonEndRewarded {
				rewarded = true
			}
		default:
			l4g.Errorf("SettleReward stage err %d", req.State.Phase)
			return
		}
	}

	if rewarded {
		l4g.Errorf("balanceArena.SettleReward: hadSettle. seasonId:%d stage:%d",
			req.State.SeasonId, req.State.Phase)
		return
	}
	m.sendRewardMail(srv, req)
}

func (m *Manager) sendRewardMail(srv activity.Servicer, req *l2c.CS2L_BalanceArenaRankSettle) {
	seasonInfo := goxml.GetData().SeasonInfoM.Index(req.State.SeasonId)
	if seasonInfo == nil {
		return
	}
	for _, rankUser := range req.Reward.Users {
		rankInfo := goxml.GetData().BalanceArenaRankRewardInfoM.GetRecordByRankTypeRankMinMinGe(req.RankType, rankUser.Rank)
		if rankInfo == nil {
			l4g.Errorf("BalanceArenaRankRewardInfoM cant find. uid:%d rank:%d",
				rankUser.Uid, rankUser.Rank)
			continue
		}
		if rankInfo.RewardGroupId == 0 {
			continue
		}
		rankRewardInfo := goxml.GetData().BalanceArenaRewardInfoM.GetRecordById(rankInfo.RewardGroupId)
		if rankRewardInfo == nil {
			l4g.Errorf("BalanceArenaRankRewardM cant find. uid:%d rank:%d",
				rankUser.Uid, rankUser.Rank)
			continue
		}
		var rewards []*cl.Resource
		for _, reward := range rankRewardInfo.Rewards {
			rewards = append(rewards, reward.Clone())
		}
		character.BalanceArenaRankMail(srv, rankInfo.MailId, []string{fmt.Sprintf("%d", rankUser.Rank)},
			rewards, rankUser.Uid, seasonInfo)
	}

	m.setSettled(req)
	l4g.Infof("[sendRewardMail] finish.")
}

func (m *Manager) setSettled(req *l2c.CS2L_BalanceArenaRankSettle) {
	rewardSta := m.GetRewardSta()
	oldRound := rewardSta.State.Round
	if oldRound != req.State.Round {
		//期数不一致 需要重置数据 赛季数据可以不重置 因为赛季id一定会不一样
		rewardSta.BigGroupRewarded = false
		rewardSta.SmallGroupRewarded = false
		rewardSta.EliminationRewarded = false
	}
	rewardSta.State = &cl.BalanceArenaState{
		SeasonId: req.State.SeasonId,
		Round:    req.State.Round,
		Stage:    req.State.Phase,
	}
	switch req.State.Phase {
	case uint32(common.BALANCE_ARENA_STAGE_BAS_BIG_GROUP_MATCH):
		rewardSta.BigGroupRewarded = true
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SMALL_GROUP_MATCH):
		rewardSta.SmallGroupRewarded = true
	case uint32(common.BALANCE_ARENA_STAGE_BAS_ELIMINATION_MATCH_3):
		rewardSta.EliminationRewarded = true
	case uint32(common.BALANCE_ARENA_STAGE_BAS_SHOW):
		rewardSta.SeasonEndRewarded = true
	}
	m.SetChange()
}

func (m *Manager) SetChange() {
	m.change = true
}

func (m *Manager) QaSet(srv actm.Servicer, content string) uint32 {
	type BalanceArenaQaSetReq struct {
		Num uint32 `json:"num"`
		Sid uint64 `json:"sid"`
	}
	req := &BalanceArenaQaSetReq{}
	err := json.Unmarshal([]byte(content), req)
	if err != nil {
		l4g.Error("BalanceArena QaSet unmarshal error:%s, content:%s", err, content)
		return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
	}
	if req.Num == 0 {
		l4g.Error("BalanceArena QaSet num is 0:%d, content:%s", req.Num, content)
		return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
	}
	if req.Sid == 0 {
		l4g.Error("BalanceArena QaSet sid is 0:%d, content:%s", req.Sid, content)
		return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
	}
	//从排行榜里找出n个人放进来
	rk := srv.GetGlobalRank(rank.POWER)
	users := rk.GetRangeByRank(1, req.Num)
	userSnapshots := make([]*cl.UserSnapshot, 0, len(users))
	for _, user := range users {
		v := user.(*rank.PowerValue)
		userSnapshots = append(userSnapshots, &cl.UserSnapshot{
			Id:   v.ID,
			Sid:  req.Sid,
			Name: strconv.Itoa(int(v.ID)),
		})
	}

	if !m.IsCrossConnected() {
		return uint32(ret.RET_CROSS_REQ_TIMEOUT)
	}

	msg := &l2c.L2CS_BalanceArenaTestSign{
		SignUsers: userSnapshots,
	}
	if !srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaTestSign, userSnapshots[0].Id, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_TestSign: cross maintain", userSnapshots[0].Id)
		return uint32(ret.RET_CROSS_REQ_TIMEOUT)
	}

	return uint32(ret.RET_OK)
}
