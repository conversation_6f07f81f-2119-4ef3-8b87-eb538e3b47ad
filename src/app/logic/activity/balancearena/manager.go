package balancearena

import (
	"app/goxml"
	"app/logic/activity"
	"app/protos/in/l2c"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type Manager struct {
	//缓存数据
	CrossState     *cl.BalanceArenaState //玩法状态
	crossConnected bool

	//落地数据
	users map[uint64]*BalanceArenaUser //玩家数据

	//数据库操作相关
	incr         int64
	freq         int64 //保存频率，单位是秒
	changes      map[uint64]*cl.BalanceArenaUser
	deletes      map[uint64]struct{}
	lastSaveNano int64 //上次存储时间戳
}

func NewManager() *Manager {
	return &Manager{
		users:   make(map[uint64]*BalanceArenaUser),
		freq:    60 + int64(activity.BalanceArena), //nolint:mnd
		changes: make(map[uint64]*cl.BalanceArenaUser),
		deletes: make(map[uint64]struct{}),
	}
}

//********* 实现logic.Activity *********

func (m *Manager) Load(data *r2l.R2L_Load) {
	for _, user := range data.BalanceArenaUser {
		if user == nil {
			continue
		}
		m.users[user.Id] = newUser(user)
	}
}

func (m *Manager) Init(servicer activity.Servicer) {
	m.lastSaveNano = time.Now().UnixNano()
}

func (m *Manager) OnTimer(servicer activity.Servicer, i int64) {
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(servicer)
	} else {
		m.checkSave(servicer)
	}
}

func (m *Manager) Save(servicer activity.Servicer) {
	var isChange bool
	changeNum := len(m.changes)
	msg := &r2l.L2R_SaveBalanceArena{}

	if changeNum > 0 {
		msg.ChangeUsers = make([]*cl.BalanceArenaUser, 0, changeNum)
		for _, u := range m.changes {
			msg.ChangeUsers = append(msg.ChangeUsers, u.Clone())
		}

		m.changes = make(map[uint64]*cl.BalanceArenaUser)
		isChange = true
	}

	deletes := len(m.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint64, 0, deletes)
		for id := range m.deletes {
			msg.Deletes = append(msg.Deletes, id)
		}

		m.deletes = make(map[uint64]struct{})
		isChange = true
	}

	if isChange {
		m.lastSaveNano = time.Now().UnixNano()
		servicer.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveBalanceArena), 0, msg)
	}
}

func (m *Manager) checkSave(srv activity.Servicer) {
	if len(m.changes) >= activity.BalanceArenaSaveNum {
		nowNano := time.Now().UnixNano()
		if nowNano-m.lastSaveNano >= activity.BalanceArenaSaveInterval {
			l4g.Infof("balanceArena.checkSave. changes:%d interval:%d", len(m.changes), nowNano-m.lastSaveNano)
			m.Save(srv)
		}
	}
}

//********* 实现logic.Activity *********

//********* 玩法状态 *********

func (m *Manager) CrossClosed(servicer activity.Servicer) {
	l4g.Info("balanceArena.CrossClosed. ")
	m.crossConnected = false
}

func (m *Manager) IsCrossConnected() bool {
	return m.crossConnected
}

func (m *Manager) CrossConnected(servicer activity.Servicer) {
	l4g.Infof("balanceArena.CrossConnected")
	m.crossConnected = true

	m.requestState(servicer)
}

func (m *Manager) requestState(srv activity.Servicer) {
	if !m.isFunctionOpen(srv, time.Now().Unix()) {
		l4g.Debugf("balanceArena.requestState: function not open.")
		return
	}

	srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetSta, srv.ServerID(), &l2c.L2CS_BalanceArenaGetSta{})
	l4g.Infof("balanceArena.requestState serverId:%d ", srv.ServerID())
}

func (m *Manager) SetCrossState(sta *cl.BalanceArenaState) {
	m.CrossState = sta
}

func (m *Manager) GetCrossState() *cl.BalanceArenaState {
	if m.CrossState == nil {
		return &cl.BalanceArenaState{}
	}
	return m.CrossState.Clone()
}

//********* 玩法状态 *********

//********* 玩家数据 *********

func (m *Manager) GetUser(uid uint64) *BalanceArenaUser {
	user, exist := m.users[uid]
	if !exist {
		return nil
	}
	return user
}

func (m *Manager) SetChangeUser(user *BalanceArenaUser) {
	m.users[user.data.Id] = user
	m.changes[user.data.Id] = user.data
}

func (m *Manager) DeleteUser(user *BalanceArenaUser) {
	uid := user.data.Id
	m.deletes[uid] = struct{}{}
	delete(m.changes, uid)
	delete(m.users, uid)
}

// TODO 玩家数据什么时候重置

// TODO 跨服报名成功后通知游戏服这个状态

//********* 玩家数据 *********

// 功能是否开启
func (m *Manager) isFunctionOpen(srv activity.Servicer, now int64) bool {
	info := goxml.GetData().FunctionInfoM.Index(uint32(common.FUNCID_MODULE_BALANCE_ARENA))
	if info == nil {
		l4g.Errorf("balanceArena.isFunctionOpen, no function info, id:%d", common.FUNCID_MODULE_BALANCE_ARENA)
		return false
	}

	return srv.ServerOpenDay(now) >= info.ServerDay
}
