package balancearena

import (
	"app/goxml"
	"app/logic/character"
	"app/protos/in/cr"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
)

type BalanceArenaUser struct {
	data *cl.BalanceArenaUser
}

func newUser(data *cl.BalanceArenaUser) *BalanceArenaUser {
	return &BalanceArenaUser{
		data: data,
	}
}

func (u *BalanceArenaUser) GetData() *cl.BalanceArenaUser {
	return u.data
}

func (u *BalanceArenaUser) Init(crossData *cr.BalanceArena) {
	u.GeneratePublicHeroes(crossData)
	u.GenerateInitialHeroes()
	u.GenerateInitialArtifacts()
}

// 生成公共英雄
// 添加公共英雄的时机：初始化完成后，玩家主动触发GetData时（不能直接发给所有玩家)
func (u *BalanceArenaUser) GeneratePublicHeroes(crossData *cr.BalanceArena) {
	heroes := getGeneratedPublicHeroes(crossData)
	u.data.AvailableHeroes = append(u.data.AvailableHeroes, heroes...)
}

// 获取跨服生成的公共英雄
func getGeneratedPublicHeroes(crossData *cr.BalanceArena) []*cl.BalanceArenaHero {
	if crossData == nil || crossData.PublicHero == nil {
		l4g.Errorf("balanceArena.GetGeneratedPublicHeroes: crossData is nil. data %+v", crossData)
		return nil
	}
	heroes := make([]*cl.BalanceArenaHero, 0, len(crossData.PublicHero.HeroIds))
	for _, heroSysId := range crossData.PublicHero.HeroIds {
		heroes = append(heroes, &cl.BalanceArenaHero{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_PUBLIC),
			SysId:    heroSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("public_initial_hero_star").Count,
			Emblems:  getDefaultSimpleEmblems(heroSysId),
		})
	}
	return heroes
}

// 生成初始英雄
func (u *BalanceArenaUser) GenerateInitialHeroes() {
	num := goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("private_initial_random_hero").Count
	heroes := make([]*cl.BalanceArenaHero, 0, num)

	// 从剩余的英雄中随机X个，前提是排除仓库中已有的
	excludeHeroes := make(map[uint32]struct{})
	for _, v := range u.data.AvailableHeroes {
		excludeHeroes[v.SysId] = struct{}{}
	}
	for _, heroSysId := range goxml.GetData().HeroInfoM.RandomHeroesByNum(num, excludeHeroes) {
		heroes = append(heroes, &cl.BalanceArenaHero{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_INITIAL),
			SysId:    heroSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("public_initial_hero_star").Count,
			Emblems:  getDefaultSimpleEmblems(heroSysId),
		})
	}
	u.data.AvailableHeroes = append(u.data.AvailableHeroes, heroes...)
}

// 生成初始神器
func (u *BalanceArenaUser) GenerateInitialArtifacts() {
	num := goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("private_initial_random_artifact").Count
	artifacts := make([]*cl.BalanceArenaArtifact, 0, num)

	// 从剩余的神器中随机X个，前提是排除仓库中已有的
	excludeArtifacts := make(map[uint32]struct{})
	for _, v := range u.data.AvailableArtifacts {
		excludeArtifacts[v.SysId] = struct{}{}
	}
	for _, artifactSysId := range goxml.GetData().ArtifactInfoM.RandomArtifactsByNum(num, excludeArtifacts) {
		artifacts = append(artifacts, &cl.BalanceArenaArtifact{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_INITIAL),
			SysId:    artifactSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("public_initial_artifact_star").Count,
		})
	}

	u.data.AvailableArtifacts = append(u.data.AvailableArtifacts, artifacts...)
}

// 随机抽选英雄
func (u *BalanceArenaUser) RandomDrawHeroes() ([]uint32, uint32) {
	if len(u.GetData().DrawHeroGroups) > 0 {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}
	// 在排除英雄仓库中英雄的情况下进行抽卡
	num := goxml.BalanceArenaDrawRoundNum

	// 从剩余的英雄中随机X个，前提是排除仓库中已有的
	excludeHeroes := make(map[uint32]struct{})
	for _, v := range u.data.AvailableHeroes {
		excludeHeroes[v.SysId] = struct{}{}
	}

	heroes := goxml.GetData().HeroInfoM.RandomHeroesByNum(num, excludeHeroes)
	if uint32(len(heroes)) != num {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}

	return heroes, uint32(cret.RET_OK)
}

// 添加抽选英雄到可选池
func (u *BalanceArenaUser) AddRandomDrawHeroes2Pool(heroes []uint32) {
	u.data.DrawHeroGroups = append(u.data.DrawHeroGroups, heroes...)
}

// 选择抽选英雄
func (u *BalanceArenaUser) ChooseDrawHeroResult(chooseGroup []uint32) ([]uint32, uint32) {
	drawGroup := u.GetData().DrawHeroGroups
	if uint32(len(drawGroup)) != goxml.BalanceArenaDrawRoundNum {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_ERROR)
	}
	// 12个英雄，分为3组，一组4个 1-[0:4] 2-[4:8] 3-[8:12]
	var sameGroup uint32
	drawGroup1 := drawGroup[0:goxml.BalanceArenaDrawGroupNum]
	drawGroup2 := drawGroup[goxml.BalanceArenaDrawGroupNum : goxml.BalanceArenaDrawGroupNum*2]
	drawGroup3 := drawGroup[goxml.BalanceArenaDrawGroupNum*2 : goxml.BalanceArenaDrawGroupNum*3]

	// TODO 前端选择的英雄组是否和某一个和组对应

	heroes := drawGroup[(cardGroup-1)*goxml.BalanceArenaDrawGroupNum : cardGroup*goxml.BalanceArenaDrawGroupNum]
	return heroes, uint32(cret.RET_OK)
}

// 将抽选英雄添加到仓库
func (u *BalanceArenaUser) GenerateDrawHeroes(heroSysIds []uint32) {
	heroes := make([]*cl.BalanceArenaHero, 0, len(heroSysIds))
	for _, heroSysId := range heroSysIds {
		heroes = append(heroes, &cl.BalanceArenaHero{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_DRAW),
			SysId:    heroSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("public_initial_hero_star").Count,
			Emblems:  getDefaultSimpleEmblems(heroSysId),
		})
	}
	u.data.AvailableHeroes = append(u.data.AvailableHeroes, heroes...)
	u.data.DrawHeroGroups = make([]uint32, 0)
}

// 随机抽选神器
func (u *BalanceArenaUser) RandomDrawArtifacts() ([]uint32, uint32) {
	if len(u.GetData().DrawArtifactGroups) > 0 {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}
	// 在排除神器仓库中神器的情况下进行抽卡
	num := goxml.BalanceArenaDrawRoundNum

	// 从剩余的神器中随机X个，前提是排除仓库中已有的
	excludeArtifacts := make(map[uint32]struct{})
	for _, v := range u.data.AvailableArtifacts {
		excludeArtifacts[v.SysId] = struct{}{}
	}

	artifacts := goxml.GetData().ArtifactInfoM.RandomArtifactsByNum(num, excludeArtifacts)
	if uint32(len(artifacts)) != num {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_RESULT_EXIST)
	}

	return artifacts, uint32(cret.RET_OK)
}

// 添加抽选神器到可选池
func (u *BalanceArenaUser) AddRandomDrawArtifacts2Pool(artifacts []uint32) {
	u.data.DrawArtifactGroups = append(u.data.DrawArtifactGroups, artifacts...)
}

// 选择抽选神器
func (u *BalanceArenaUser) ChooseDrawArtifactResult(cardGroup uint32) ([]uint32, uint32) {
	drawGroup := u.GetData().DrawArtifactGroups
	if uint32(len(drawGroup)) != goxml.BalanceArenaDrawGroupNum {
		return nil, uint32(cret.RET_BALANCE_ARENA_DRAW_ERROR)
	}
	// 12个神器，分为3组，一组4个 1-[0:4] 2-[4:8] 3-[8:12]
	artifacts := drawGroup[(cardGroup-1)*goxml.BalanceArenaDrawGroupNum : cardGroup*goxml.BalanceArenaDrawGroupNum]
	return artifacts, uint32(cret.RET_OK)
}

// 将抽选神器添加到仓库
func (u *BalanceArenaUser) GenerateDrawArtifacts(artifactSysIds []uint32) {
	artifacts := make([]*cl.BalanceArenaArtifact, 0, len(artifactSysIds))
	for _, artifactSysId := range artifactSysIds {
		artifacts = append(artifacts, &cl.BalanceArenaArtifact{
			CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_DRAW),
			SysId:    artifactSysId,
			Star:     goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("public_initial_artifact_star").Count,
		})
	}
	u.data.AvailableArtifacts = append(u.data.AvailableArtifacts, artifacts...)
	u.data.DrawArtifactGroups = make([]uint32, 0)
}

// 是否能自选英雄
func (u *BalanceArenaUser) CanCustomizeHero(heroSysId uint32) uint32 {
	maxCustomizeNum := goxml.BalanceArenaCustomizeNum
	customizeNum := uint32(0)
	for _, v := range u.data.AvailableHeroes {
		// 自选英雄是否已经在英雄仓库中
		if v.SysId == heroSysId {
			return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_NUM_LIMIT)
		}
		if v.CardType != uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE) {
			continue
		}
		customizeNum++
	}
	// 自选数量是否满足
	if customizeNum >= maxCustomizeNum {
		return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_EXIST)
	}
	return uint32(cret.RET_OK)
}

// 生成自选英雄
func (u *BalanceArenaUser) GenerateCustomizeHeroes(user *character.User, hero *character.Hero) {
	// 英雄的星级
	star := hero.GetStar()
	if minStar := goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("public_initial_hero_star").Count; star < minStar {
		star = minStar
	}

	// 符文
	emblems := u.GetEmblemsByHero(user, hero.GetHid())

	u.data.AvailableHeroes = append(u.data.AvailableHeroes, &cl.BalanceArenaHero{
		CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE),
		SysId:    hero.GetHeroSysID(),
		Star:     star,
		Emblems:  emblems,
	})
}

// 是否能自选神器
func (u *BalanceArenaUser) CanCustomizeArtifact(artifactSysId uint32) uint32 {
	maxCustomizeNum := goxml.BalanceArenaCustomizeNum
	customizeNum := uint32(0)
	for _, v := range u.data.AvailableArtifacts {
		// 自选神器是否已经在神器仓库中
		if v.SysId == artifactSysId {
			return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_NUM_LIMIT)
		}
		if v.CardType != uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE) {
			continue
		}
		customizeNum++
	}
	// 自选数量是否满足
	if customizeNum >= maxCustomizeNum {
		return uint32(cret.RET_BALANCE_ARENA_CUSTOMIZE_EXIST)
	}
	return uint32(cret.RET_OK)
}

// 生成自选神器
func (u *BalanceArenaUser) GenerateCustomizeArtifacts(artifact *character.Artifact) {
	// 神器的星级
	star := artifact.GetStar()
	if minStar := goxml.GetData().BalanceArenaConfigInfoM.GetRecordByKey("public_initial_artifact_star").Count; star < minStar {
		star = minStar
	}

	u.data.AvailableArtifacts = append(u.data.AvailableArtifacts, &cl.BalanceArenaArtifact{
		CardType: uint32(common.BALANCE_ARENA_CARD_TYPE_BACT_CHOOSE),
		SysId:    artifact.GetSysID(),
		Star:     artifact.GetStar(),
	})
}

// 优先使用英雄符文，不符合要求再使用默认符文
func (u *BalanceArenaUser) GetEmblemsByHero(user *character.User, heroId uint64) []*cl.SimpleEmblemInfo {
	hero := user.HeroManager().Get(heroId)
	if hero == nil {
		l4g.Errorf("balanceArena.GetEmblemsByHero: hero not exist. heroId:%d", heroId)
		return nil
	}
	_, level := hero.CalcHeroEmblemSkill(user)
	if level == 0 {
		return getDefaultSimpleEmblems(hero.GetHeroSysID()) // 英雄符文不符合要求，使用默认符文
	}
	// 使用英雄符文的条件：持有橙色专属技能->专属技能等级>0
	// 四橙/三橙一红 level = 1 二橙二红/一橙三红 level = 2  四红 level = 3
	emblems := hero.GetDisplayEmblems(user)
	return convertEmblems2SimpleEmblems(emblems, hero.GetHeroSysID())
}

// TODO 使用默认符文 需求等策划确认
func getDefaultSimpleEmblems(heroSysId uint32) []*cl.SimpleEmblemInfo {
	simpleEmblems := make([]*cl.SimpleEmblemInfo, 0, goxml.EmblemExclusiveNumFour)

	// 阵营：同英雄阵营

	// 品质：橙色
	//goxml.EmblemRareOrange

	// 类型：暴伤 闪避 反弹 治疗

	// 专属

	return simpleEmblems
}

func convertEmblems2SimpleEmblems(emblems []*character.Emblem, heroSysId uint32) []*cl.SimpleEmblemInfo {
	simpleEmblems := make([]*cl.SimpleEmblemInfo, 0, len(emblems))
	for _, emblem := range emblems {
		simpleEmblems = append(simpleEmblems, &cl.SimpleEmblemInfo{
			SysId:        emblem.Data.SysId,
			AdditiveHero: heroSysId,
		})
	}
	return simpleEmblems
}

// 检查组队
func (u *BalanceArenaUser) CheckTeams(teams []*cl.BalanceArenaTeam) uint32 {
	// 检查队伍数量
	if uint32(len(teams)) != goxml.BalanceArenaTeamNum {
		return uint32(cret.RET_BALANCE_ARENA_TEAM_NUM_ERROR)
	}
	for _, team := range teams {
		// 检查单个队伍
		if ret := u.checkTeam(team); ret != uint32(cret.RET_OK) {
			return ret
		}
	}
	return uint32(cret.RET_OK)
}

func (u *BalanceArenaUser) checkTeam(team *cl.BalanceArenaTeam) uint32 {
	// 1.每个队伍的英雄必须>1
	if len(team.Heroes) == 0 {
		return uint32(cret.RET_BALANCE_ARENA_TEAM_HERO_NUM_ERROR)
	}

	// 2.所选的英雄和神器必须在仓库中
	for _, hero := range team.Heroes {
		if !u.isHeroAvailable(hero.SysId) {
			return uint32(cret.RET_BALANCE_ARENA_TEAM_NOT_AVAILABLE)
		}
	}

	for _, artifact := range team.Artifacts {
		if !u.isArtifactAvailable(artifact.SysId) {
			return uint32(cret.RET_BALANCE_ARENA_TEAM_NOT_AVAILABLE)
		}
	}

	return 0
}

func (u *BalanceArenaUser) isHeroAvailable(sysId uint32) bool {
	for _, v := range u.data.AvailableHeroes {
		if v.SysId == sysId {
			return true
		}
	}
	return false
}

func (u *BalanceArenaUser) isArtifactAvailable(sysId uint32) bool {
	for _, v := range u.data.AvailableArtifacts {
		if v.SysId == sysId {
			return true
		}
	}
	return false
}
