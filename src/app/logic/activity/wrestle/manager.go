package wrestle

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/base"
	"app/protos/in/cr"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"

	//l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

// 神树争霸系统
type Manager struct {
	data     *db.Wrestle
	userData map[uint64]*User //user_id->普通竞技场数据

	//数据库操作相关
	incr    int64
	freq    int64 //保存频率，单位是秒
	changes map[uint64]*db.WrestleUser
	deletes map[uint64]struct{}

	wrestleChange bool

	lastSaveNano int64 //上次存储时间戳

	//日志管理器
	logM *base.LogM

	topUsers          map[uint32][]*cl.UserSnapshot // key:赛场level value:top玩家snapshot
	topUserUpdateTime int64
	topDataUpdating   bool

	crossConnected bool
}

func NewManager() *Manager {
	m := &Manager{
		userData: make(map[uint64]*User),
		freq:     60 + int64(activity.Wrestle), //nolint:mnd //60秒保存一次
		changes:  make(map[uint64]*db.WrestleUser),
		deletes:  make(map[uint64]struct{}),
		logM:     base.NewLogM(goxml.WrestleLogMaxNum),
	}
	return m
}

func (m *Manager) Init(activity.Servicer) {
	m.lastSaveNano = time.Now().UnixNano()
}

func (m *Manager) Load(msg *r2l.R2L_Load) {
	for _, data := range msg.WrestleUser {
		//m.insert(data)
		user := newWrestleUser(data)
		//nolint:mnd
		if util.DaysBetweenTimes(user.Get().EnterTm, time.Now().Unix()) > 30 {

			m.deletes[user.data.Uid] = struct{}{}
			continue
		}
		m.userData[data.Uid] = user
	}
	m.data = msg.Wrestle
	if m.data == nil {
		m.data = &db.Wrestle{}
	}
}

func (m *Manager) checkSave(srv activity.Servicer) {
	if len(m.changes) > activity.WrestleSaveNum || m.logM.GetChanges() >= activity.WrestleSaveNum {
		nowNano := time.Now().UnixNano()
		if nowNano-m.lastSaveNano >= activity.WrestleSaveInterval {
			l4g.Infof("wrestleM -> checkSave. changes:%d interval:%d", len(m.changes), nowNano-m.lastSaveNano)
			m.Save(srv)
		}
	}
}

func (m *Manager) Save(srv activity.Servicer) {
	success := false
	msg := &r2l.L2R_SaveWrestle{}
	if size := len(m.changes); size > 0 {
		msg.Changes = make([]*db.WrestleUser, 0, size)
		for _, change := range m.changes {
			msg.Changes = append(msg.Changes, change.Clone())
		}
		l4g.Infof("process save changes wrestle length:%d", size)
		m.changes = make(map[uint64]*db.WrestleUser)
		success = true
	}

	if size := len(m.deletes); size > 0 {
		msg.Deletes = make([]uint64, 0, size)
		for id := range m.deletes {
			msg.Deletes = append(msg.Deletes, id)
		}
		success = true
		m.deletes = make(map[uint64]struct{})
		l4g.Infof("process save deletes wrestleUser length:%d", size)
	}

	logChanges, logDeletes := m.logM.Save()
	if len(logChanges) > 0 {
		for uid, logs := range logChanges {
			if msg.ChangeLogs == nil {
				msg.ChangeLogs = make(map[uint64]*cl.WrestleLogs)
			}
			logChangeMsg := &cl.WrestleLogs{}
			for _, v := range logs {
				logChangeMsg.Logs = append(logChangeMsg.Logs, v.(*Log).Flush())
			}
			msg.ChangeLogs[uid] = logChangeMsg
		}
		if len(msg.ChangeLogs) > 0 {
			success = true
		}
		l4g.Debugf("process save change wrestle log datas:%+v", logChanges)
	}
	if len(logDeletes) > 0 {
		for uid, deleteIds := range logDeletes {
			if msg.DeleteLogs == nil {
				msg.DeleteLogs = make(map[uint64]*cl.WrestleLogDeletes)
			}
			logDeleteMsg := &cl.WrestleLogDeletes{Ids: deleteIds}
			msg.DeleteLogs[uid] = logDeleteMsg
		}
		if len(msg.DeleteLogs) > 0 {
			success = true
		}
		l4g.Debugf("process save delete wrestle log datas:%+v", logChanges)
	}

	if m.wrestleChange {
		msg.Wrestle = m.data.Clone()
		success = true
		m.wrestleChange = false
	}

	if success {
		m.lastSaveNano = time.Now().UnixNano()
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveWrestle), 0, msg)
	}
}

func (m *Manager) OnTimer(srv activity.Servicer, now int64) {
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(srv)
		//检查功能开启
		if m.isOpen(srv, now) {
			//检查奖励结算
			if m.isNeedAward(srv, now) {
				m.award(srv)
			}
		}
	} else {
		m.checkSave(srv)
	}
}

func (m *Manager) CrossClosed(srv activity.Servicer) {
	l4g.Info("Wrestle get cross closed")
	m.crossConnected = false
}

func (m *Manager) CrossConnected(srv activity.Servicer) {
	l4g.Info("Wrestle get cross connected")
	m.crossConnected = true
	now := time.Now().Unix()
	if m.isOpen(srv, now) {
		if m.isNeedAward(srv, now) {
			m.award(srv)
		}
	}
}

// 功能是否开启
func (m *Manager) isOpen(srv activity.Servicer, now int64) bool {
	return goxml.IsWrestleOpen(goxml.GetData(), srv.ServerOpenDay(now))
}

func (m *Manager) setChange(u *User) {
	m.changes[u.GetUID()] = u.Get()
}

func (m *Manager) SetWrestleChange(srv activity.Servicer) {
	m.wrestleChange = true
	m.Save(srv)
}

func (m *Manager) Get(uid uint64) *User {
	if user := m.userData[uid]; user != nil {
		if user.GetData() != nil {
			return m.userData[uid]
		}
	}
	return nil
}

// 是否需要补发奖励
func (m *Manager) isNeedAward(srv activity.Servicer, now int64) bool {
	awardTime := goxml.GetWrestleDailyResetTime(now)
	lastTime := m.GetLastDailyAwardTime()

	// 玩法开启后，还没有进行过每日结算
	if lastTime == 0 {
		day := srv.ServerOpenDay(now)
		if !goxml.IsWrestleOpen(goxml.GetData(), day) {
			return false
		}
		m.InitAwardTime(srv, now)
		lastTime = m.GetLastDailyAwardTime()
	}

	// 还没到今天的发奖时间，使用昨天的发奖时间，检查是否需要补发昨天的
	if now < awardTime {
		awardTime = awardTime - util.DaySecs
	}

	if lastTime < awardTime {
		return true
	}
	return false
}

func (m *Manager) IsNeedAward(srv activity.Servicer, now int64) bool {
	return m.isNeedAward(srv, now)
}

// 检查补发奖励
func (m *Manager) award(srv activity.Servicer) {
	msg := &l2c.L2C_WrestleAward{
		Sid: srv.ServerID(),
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleAward, srv.ServerID(), msg)
	l4g.Infof("[Wrestle] request cross rank award")
}

// 获取当前赛季状态
// @param day 开服第几天
// @param time 当前时间
func (m *Manager) GetSeasonStatus(srv activity.Servicer, time int64) uint32 {

	if goxml.IsWrestleDailyResetting(time) {
		return uint32(common.WRESTLE_STATUS_WS_RESETTING) // 结算中
	}

	if goxml.GetWrestleDailyResetEndTime(time) < time {
		return uint32(common.WRESTLE_STATUS_WS_PREPARING) // 准备中
	}

	return uint32(common.WRESTLE_STATUS_WS_NONE) // 正常状态
}

func (m *Manager) GetMapTopThree() map[uint32]*cl.MultipleMiniUserSnapshot {
	topThree := make(map[uint32]*cl.MultipleMiniUserSnapshot)
	for level, data := range m.topUsers {
		multipleSnapshot := &cl.MultipleMiniUserSnapshot{
			//nolint:mnd
			MiniInfo: make([]*cl.MiniUserSnapshot, 0, 3),
		}
		for index, snapshot := range data {
			//nolint:mnd
			if index == 3 {
				break
			}
			multipleSnapshot.MiniInfo = append(multipleSnapshot.MiniInfo, &cl.MiniUserSnapshot{
				Id:     snapshot.Id,
				Name:   snapshot.Name,
				Level:  snapshot.Level,
				BaseId: snapshot.BaseId,
				Power:  snapshot.Power,
				Sid:    snapshot.Sid,
			})
		}
		topThree[level] = multipleSnapshot
	}
	return topThree
}

func (m *Manager) IsNeedNewTopData(now int64) bool {
	if m.topUserUpdateTime > now {
		return true
	}

	if m.topDataUpdating {
		// 正在更新，不需要重复更新

		return now-m.topUserUpdateTime > 2*activity.WrestleUpdateNewTopUserInterval
		// 防止更新请求没有回复，这里判断超过2个时间间隔，还是请求更新
	}

	return now-m.topUserUpdateTime > activity.WrestleUpdateNewTopUserInterval // 大于5分钟请求新的
}

func (m *Manager) SetTopDataUpdating() {
	m.topDataUpdating = true
}

func (m *Manager) SetTopDataUpdateFinish() {
	m.topDataUpdating = false
}

func (m *Manager) GetTopUserByLevel(level uint32) []*cl.UserSnapshot {
	if m.topUsers != nil {
		return m.topUsers[level]
	}
	return nil
}

func (m *Manager) UpdateTopUsers(level uint32, data []*cl.UserSnapshot) {
	if m.topUsers == nil {
		//nolint:mnd
		m.topUsers = make(map[uint32][]*cl.UserSnapshot, 5)
	}
	m.topUsers[level] = data
	m.topUserUpdateTime = time.Now().Unix()
}

func (m *Manager) IsNeedUpdateHallOfFame(now int64) bool {
	// 当前所处赛季的更新时间
	nowReasonResetTm := goxml.GetWrestleSeasonResetTime(goxml.GetData(), now)
	hallOfFameUpdateTm := m.GetHallOfFameUpdateTm()
	if hallOfFameUpdateTm == 0 {
		return true
	}

	// 荣誉殿堂更新时间所在的赛季重置时间
	resetTm := goxml.GetWrestleSeasonResetTime(goxml.GetData(), hallOfFameUpdateTm)

	dayBetween := util.DaysBetweenTimes(resetTm, nowReasonResetTm)
	switch dayBetween {
	case uint32(goxml.WrestleSeasonDuration): // 重置时间正好隔一个赛季
		if now > nowReasonResetTm {
			return true
		}
		if hallOfFameUpdateTm < resetTm {
			return true
		}
	case 0: // 重置时间在同一天
		if nowReasonResetTm < now && hallOfFameUpdateTm <= nowReasonResetTm {
			return true
		}
	default: // 大于一周
		return true
	}
	return false
}

func (m *Manager) UpdateHallOfFame(srv activity.Servicer, data []*cr.FighterRecord) {
	m.data.HallOfFame = &db.WrestleHallOfFame{
		Record: make([]*db.WrestleFighterRecord, 0, len(data)),
	}
	for _, fighterRecord := range data {
		m.data.HallOfFame.Record = append(m.data.HallOfFame.Record,
			&db.WrestleFighterRecord{
				Level:      fighterRecord.Level,
				Rank:       fighterRecord.Rank,
				Score:      fighterRecord.Score,
				LikedCount: fighterRecord.LikedCount,
				Snapshot:   fighterRecord.Snapshot.Clone(),
			})
	}
	m.SetHallOfFameUpdateTm(time.Now().Unix())
	m.SetWrestleChange(srv)
}

func (m *Manager) GetHallOfFameTop3() ([]*cl.UserSnapshot, []uint32) {
	//nolint:mnd
	top3 := make([]*cl.UserSnapshot, 0, 3)
	//nolint:mnd
	likeNum := make([]uint32, 0, 3)
	if m.data.HallOfFame == nil {
		return top3, likeNum
	}
	for index, data := range m.data.HallOfFame.Record {
		//nolint:mnd
		if index == 3 {
			break
		}
		if data == nil {
			continue
		}
		top3 = append(top3, data.Snapshot)
		likeNum = append(likeNum, data.LikedCount)
	}
	return top3, likeNum
}

func (m *Manager) UpdateHallOfFameTop3LikeCount(srv activity.Servicer, likeCount []uint32) {
	hallOfFame := m.data.HallOfFame
	if hallOfFame == nil {
		return
	}
	for index, count := range likeCount {
		//nolint:mnd
		if index == 3 {
			break
		}
		if index >= len(m.data.HallOfFame.Record) {
			break
		}
		record := m.data.HallOfFame.Record[index]
		if record == nil {
			continue
		}
		record.LikedCount = count
	}
	m.SetWrestleChange(srv)
}

func (m *Manager) GetHallOfFame() []*cl.UserSnapshot {
	if m.data.HallOfFame == nil {
		return nil
	}
	top := make([]*cl.UserSnapshot, 0, len(m.data.HallOfFame.Record))
	for _, data := range m.data.HallOfFame.Record {
		top = append(top, data.Snapshot)
	}
	return top
}

func (m *Manager) GetRankOfHallOfFame() []*cl.RankValue {
	if m.data.HallOfFame == nil {
		return nil
	}
	top := make([]*cl.RankValue, 0, len(m.data.HallOfFame.Record))
	for _, data := range m.data.HallOfFame.Record {
		top = append(top, &cl.RankValue{
			Value:  uint64(data.Level),
			User:   data.Snapshot,
			Param1: uint64(data.Score),
		})
	}
	return top
}

func (m *Manager) GetLogM() *base.LogM {
	return m.logM
}

func (m *Manager) SetChange(srv activity.Servicer, u *User) {
	m.setChange(u)
	m.checkSave(srv)
}

func (m *Manager) GetLikeRank(id uint64) uint32 {
	if m.data.HallOfFame == nil {
		return 0
	}
	for index, data := range m.data.HallOfFame.Record {
		if data.Snapshot.Id == id {
			return uint32(index + 1)
		}
	}
	return 0
}

func (m *Manager) GetLastSeasonAwardTime() int64 {
	return m.data.SeasonAwardTm
}

func (m *Manager) SetDailyAwardTime(time int64) {
	m.data.DailyAwardTm = time
}

func (m *Manager) GetLastDailyAwardTime() int64 {
	return m.data.DailyAwardTm
}

func (m *Manager) SetSeasonAwardTime(time int64) {
	m.data.SeasonAwardTm = time
}

func (m *Manager) SetHallOfFameUpdateTm(time int64) {
	m.data.HallOfFameUpdateTm = time
}

func (m *Manager) GetHallOfFameUpdateTm() int64 {
	return m.data.HallOfFameUpdateTm
}

func (m *Manager) IsCrossConnected() bool {
	return m.crossConnected
}

func (m *Manager) InitAwardTime(srv activity.Servicer, now int64) {
	initLastDailyTime := goxml.GetWrestleDailyResetTime(now) - util.DaySecs
	initLastSeasonTime := goxml.GetWrestlePartitionPrevResetTime(goxml.GetData(), now)
	m.SetDailyAwardTime(initLastDailyTime)
	m.SetSeasonAwardTime(initLastSeasonTime)
	m.SetWrestleChange(srv)
}
