package arena

import (
	"app/goxml"
	"app/logic/activity"
	"app/protos/in/db"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/skiplist"
	"gitlab.qdream.com/kit/sea/time"
)

// 竞技场匹配对手
// @param activity.Servicer srv
// @param *arena.User user
// @param uint32 level 玩家等级
// @param int64 maxPower 玩家最大主线战力
// @return []*db.ArenaOpponent
func (m *Manager) GenerateOpponents(srv activity.Servicer, user *User,
	level uint32, maxPower int64) []*db.ArenaOpponent {
	start := time.AccurateNow()
	var opponents []*db.ArenaOpponent
	m.resetRepeatList()

	score := user.GetScore()
	rule := user.GetMatchRule()
	if rule == activity.ArenaMatchRuleSeason {
		opponents = m.doGenerateSeasonRule(srv, level, score)
	} else if rule == activity.ArenaMatchRuleDefeat {
		opponents = m.doGenerateDefeatRule(srv, level, user, maxPower)
	} else {
		opponents = m.doGenerateNormalRule(srv, level, user, maxPower)
	}

	end := time.AccurateNow()
	l4g.Debugf("user: %d, GenerateOpponents. score:%d, execute time:%v, ops:%+v, rule:%d",
		user.GetUID(), score, end.Sub(start), opponents, rule)

	//添加赛季刷新次数
	user.AddSeasonRefreshCount()

	//检查重置连败纪录
	if rule == activity.ArenaMatchRuleDefeat {
		user.ResetDefeatCount()
	}

	return opponents
}

// 竞技场匹配对手 - 赛季前x场
// @param activity.Servicer srv
// @param uint32 level 玩家等级
// @param uint32 score 玩家竞技场积分
// @return []*db.ArenaOpponent
func (m *Manager) doGenerateSeasonRule(srv activity.Servicer, level, score uint32) []*db.ArenaOpponent {
	opponents := make([]*db.ArenaOpponent, 0, goxml.ArenaOpponentNum)
	info := goxml.GetData().ArenaMatchInfoM.Match(score)
	for stage := 0; stage < int(goxml.ArenaOpponentNum); stage++ {
		opponent := m.matchBot(srv, level, info, stage)
		opponents = append(opponents, opponent)
	}
	return opponents
}

// 竞技场匹配对手 - 连败x场
// @param activity.Servicer srv
// @param uint32 level 玩家等级
// @param *arena.User user
// @param int64 power 玩家最大主线战力
// @return []*db.ArenaOpponent
func (m *Manager) doGenerateDefeatRule(srv activity.Servicer, level uint32,
	user *User, power int64) []*db.ArenaOpponent {
	opponents := make([]*db.ArenaOpponent, 0, goxml.ArenaOpponentNum)
	info := goxml.GetData().ArenaMatchInfoM.Match(user.GetScore())
	for stage := 0; stage < int(goxml.ArenaOpponentNum); stage++ {
		var opponent *db.ArenaOpponent
		if stage == activity.ArenaMatchStageLow {
			opponent = m.matchBot(srv, level, info, stage)
		} else {
			opponent = m.generateOpponent(srv, info, stage, user, power, level)
		}
		opponents = append(opponents, opponent)
	}
	return opponents
}

// 竞技场匹配对手 - 正常规则
// @param activity.Servicer srv
// @param uint32 level 玩家等级
// @param *arena.User user
// @param int64 power 玩家最大主线战力
// @return []*db.ArenaOpponent
func (m *Manager) doGenerateNormalRule(srv activity.Servicer, level uint32,
	user *User, power int64) []*db.ArenaOpponent {
	opponents := make([]*db.ArenaOpponent, 0, goxml.ArenaOpponentNum)
	info := goxml.GetData().ArenaMatchInfoM.Match(user.GetScore())
	for stage := 0; stage < int(goxml.ArenaOpponentNum); stage++ {
		opponent := m.generateOpponent(srv, info, stage, user, power, level)
		opponents = append(opponents, opponent)
	}
	return opponents
}

// 生成对手
// @param activity.Servicer srv
// @param *goxml.ArenaMatchInfo info
// @param int stage 生成对手的区间（低中高）
// @param *User user
// @param int64 power 玩家最大主线战力
// @param uint32 level 玩家等级
// @return *db.ArenaOpponent
// tips: 当已搜不到真实玩家，且已搜列表有数据时，清空已搜列表数据，再搜一次真实玩家
func (m *Manager) generateOpponent(srv activity.Servicer, info *goxml.ArenaMatchInfo,
	stage int, user *User, power int64, level uint32) *db.ArenaOpponent {
	opponent := m.matchPlayer(srv, info, stage, user, power)
	if opponent == nil {
		//当已搜不到真实玩家，且已搜列表有数据时，清空已搜列表数据，再搜一次真实玩家
		isCleaned := user.CheckCleanSearchList(stage)
		if isCleaned {
			opponent = m.matchPlayer(srv, info, stage, user, power)
		}

		if opponent == nil {
			opponent = m.extMatchPlayer(srv, info, stage, user)
		}

		if opponent == nil {
			opponent = m.matchBot(srv, level, info, stage)
		}
	}
	return opponent
}

// 匹配真人玩家
// @param activity.Servicer srv
// @param *goxml.ArenaMatchInfo info
// @param int stage 生成对手的区间（低中高）
// @param *User user
// @param int64 power 玩家最大主线战力
// @return *db.ArenaOpponent
func (m *Manager) matchPlayer(srv activity.Servicer, info *goxml.ArenaMatchInfo,
	stage int, user *User, power int64) *db.ArenaOpponent {
	start := time.AccurateNow()
	maxPower := m.getMatchPower(info, stage, power)
	var opUID uint64
	uid := user.GetUID()
	score := user.GetScore()
	checkFunc := func(value interface{}) bool {
		v := value.(*ScoreValue) //nolint:varnamelen
		//验证本次匹配中，搜到的对手id是否重复
		if m.isOpUIDRepeat(uid, v.ID) {
			l4g.Debugf("arenaM.matchPlayer: isOpUIDRepeat, opUID:%d, uid:%d",
				v.ID, uid)
			return false
		}

		//验证当前匹配区间，对手id是否被搜到过
		if user.IsInSearchList(stage, v.ID) {
			l4g.Debugf("arenaM.matchPlayer: IsInSearchList, opUID:%d, stage:%d",
				v.ID, stage)
			return false
		}

		//验证战力是否符合要求
		opUser := m.Get(v.ID)
		if opUser == nil {
			l4g.Errorf("arenaM.matchPlayer: opUser data err, opUID:%d", v.ID)
			return false
		}
		if opUser.GetPower() > maxPower {
			l4g.Debugf("arenaM.matchPlayer: power not match, opUID:%d, opPower:%d, power:%d, maxPower:%d",
				v.ID, opUser.GetPower(), power, maxPower)
			return false
		}
		return true
	}

	for round := 1; round <= int(goxml.GetData().ArenaConfigInfoM.MatchExpandCount); round++ {
		minScore, maxScore := m.getScoreRange(info, stage, round, score)
		list := m.Rank.GetRangeByScoreRandom(srv.Rand(), &skiplist.RangeSpec{
			Reverse:     true,
			Min:         minScore,
			Max:         maxScore,
			Num:         1,
			MaxCheckNum: activity.ArenaMatchPlayerMaxCount,
			CheckF:      checkFunc,
		})

		l4g.Debugf("user: %d, matchPlayer. round:%d, userScore:%d, minScore:%d, maxScore:%d",
			uid, round, score, minScore, maxScore)

		if len(list) > 0 {
			opUID = list[0].(*ScoreValue).Key()
			break
		}
	}

	end := time.AccurateNow()
	l4g.Debugf("user: %d, matchPlayer. infoID:%d, stage:%d, opUID:%d, costTm:%v",
		uid, info.Id, stage, opUID, end.Sub(start))

	if opUID == 0 {
		return nil
	}
	m.existPlayers = append(m.existPlayers, opUID)
	user.UpdateSearchList(stage, opUID)
	return &db.ArenaOpponent{
		Id: opUID,
	}
}

// 获取真实玩家积分匹配范围
func (m *Manager) getScoreRange(info *goxml.ArenaMatchInfo, stage, round int,
	score uint32) (uint64, uint64) {
	baseScore := int64(score)
	var minScore, maxScore uint64
	switch stage {
	case activity.ArenaMatchStageHigh:
		minScore = uint64(baseScore + info.HighMinAdd*int64(round))
		maxScore = uint64(baseScore + info.HighMaxAdd*int64(round))
	case activity.ArenaMatchStageMid:
		minScore = uint64(baseScore + info.MidMinAdd*int64(round))
		maxScore = uint64(baseScore + info.MidMaxAdd*int64(round))
	case activity.ArenaMatchStageLow:
		minScore = uint64(baseScore + info.LowMinAdd*int64(round))
		maxScore = uint64(baseScore + info.LowMaxAdd*int64(round))
	}
	return minScore, maxScore
}

// 获取战力匹配范围
func (m *Manager) getMatchPower(info *goxml.ArenaMatchInfo, stage int, userPower int64) int64 {
	var maxPower int64
	switch stage {
	case activity.ArenaMatchStageHigh:
		maxPower = userPower * int64(info.HighForceMax) / goxml.BaseInt64
	case activity.ArenaMatchStageMid:
		maxPower = userPower * int64(info.MidForceMax) / goxml.BaseInt64
	case activity.ArenaMatchStageLow:
		maxPower = userPower * int64(info.LowForceMax) / goxml.BaseInt64
	}
	return maxPower
}

// 扩展模式匹配真人玩家
// @param activity.Servicer srv
// @param *goxml.ArenaMatchInfo info
// @param int stage 生成对手的区间（低中高）
// @param *User user
// @return *db.ArenaOpponent
// @tips 模式1-先向上找，如果没有再向下找  模式2-向下找
func (m *Manager) extMatchPlayer(srv activity.Servicer, info *goxml.ArenaMatchInfo, stage int, user *User) *db.ArenaOpponent {
	extMode, limitScore := m.getExtModeData(info, stage)
	if extMode == goxml.ArenaMatchExtModeNone {
		return nil
	}

	_, node := m.Rank.GetRankAndNode(user.GetUID())
	if node == nil {
		l4g.Errorf("user: %d, extMatchPlayer node not exist", user.GetUID())
		return nil
	}

	//扩展模式1的第一阶段
	var opList []*ScoreValue
	if extMode == goxml.ArenaMatchExtModeUpThenDown {
		for i := uint32(0); i < goxml.ArenaMatchExtMaxNum; i++ {
			node = node.Prev()
			if node == nil {
				break
			}

			value := node.Value().(*ScoreValue)
			if m.isExtMatchOpAvailable(user, value, limitScore) {
				opList = append(opList, value)
			}

			l4g.Debugf("user: %d, extMatchPlayer type 1. infoID:%d, stage:%d, value:%+v",
				user.GetUID(), info.Id, stage, value)
		}
	}

	//扩展模式1的第二阶段，或模式2
	if len(opList) == 0 {
		_, node := m.Rank.GetRankAndNode(user.GetUID())
		for i := uint32(0); i < goxml.ArenaMatchExtMaxNum; i++ {
			node = node.Next(0)
			if node == nil {
				break
			}

			value := node.Value().(*ScoreValue)
			if m.isExtMatchOpAvailable(user, value, limitScore) {
				opList = append(opList, value)
			}

			l4g.Debugf("user: %d, extMatchPlayer type 1 or 2. infoID:%d, stage:%d, value:%+v",
				user.GetUID(), info.Id, stage, value)
		}
	}

	opListLen := len(opList)
	l4g.Debugf("user: %d, extMatchPlayer. infoID:%d, stage:%d, opListLen:%d, extMode:%d",
		user.GetUID(), info.Id, stage, opListLen, extMode)

	if opListLen == 0 {
		return nil
	}

	//随机一个对手
	k := srv.Rand().Intn(opListLen)
	opUID := opList[k].Key()
	m.existPlayers = append(m.existPlayers, opUID)
	user.UpdateSearchList(stage, opUID)
	return &db.ArenaOpponent{
		Id: opUID,
	}
}

// 获取扩展模式相关数据
// @param *goxml.ArenaMatchInfo info
// @param int stage 生成对手的区间（低中高）
// @return uint32 模式
// @return uint32 限制分数
func (m *Manager) getExtModeData(info *goxml.ArenaMatchInfo, stage int) (uint32, uint32) {
	switch stage {
	case activity.ArenaMatchStageHigh:
		return info.ExtModeHigh, info.DownLimitHigh
	case activity.ArenaMatchStageMid:
		return info.ExtModeMid, info.DownLimitMid
	default:
		return info.ExtModeLow, info.DownLimitLow
	}
}

// 检查扩展匹配，根据排名搜到的玩家是否可用
// @param *User user 玩家信息
// @param *ScoreValue opData 对手信息
// @param uint32 limitScore 差值限制分数
// @return bool
func (m *Manager) isExtMatchOpAvailable(user *User, opData *ScoreValue, limitScore uint32) bool {
	if m.isOpUIDRepeat(user.GetUID(), opData.Key()) {
		l4g.Debugf("user: %d, isExtMatchOpAvailable. OpUidRepeat opUID:%d",
			user.GetUID(), opData.Key())
		return false
	}
	return m.checkExtMatchScoreLimit(user.GetScore(), uint32(opData.Score()), limitScore)
}

// 检查扩展匹配，分数显示是否通过
// @param uint32 userScore 玩家分数
// @param uint32 opScore 对数分数
// @param uint32 limit 差值限制分数
// @return bool
func (m *Manager) checkExtMatchScoreLimit(userScore, opScore, limit uint32) bool {
	l4g.Debugf("checkExtMatchScoreLimit. userScore:%d, opScore:%d, limit:%d",
		userScore, opScore, limit)
	if opScore >= userScore {
		return true
	}
	return userScore-opScore <= limit
}

// 匹配机器人
// @param activity.Servicer srv
// @param uint32 level 玩家等级
// @param *goxml.ArenaMatchInfo info
// @param int stage 生成对手的区间（低中高）
// @return *db.ArenaOpponent
func (m *Manager) matchBot(srv activity.Servicer, level uint32,
	info *goxml.ArenaMatchInfo, stage int) *db.ArenaOpponent {
	score := m.getBotScore(info, stage)
	botLv := m.botLevel(srv, level)
	id := goxml.GetData().BotInfoM.Generate(srv.Rand(), botLv, m.existBots)
	if id == 0 {
		l4g.Errorf("Generate bot failed: %d, %d, %+v", level, botLv, m.existBots)
		return nil
	}

	l4g.Debugf("matchBot. level:%d, infoID:%d, stage:%d, score:%d, botID:%d",
		level, info.Id, stage, score, id)

	m.existBots = append(m.existBots, id)
	return &db.ArenaOpponent{
		Id:     id,
		Level:  botLv,
		Name:   m.botName(srv),
		BaseId: m.botBaseID(srv),
		Score:  score,
		Bot:    true,
	}
}

// 根据对手档位获取机器人积分
func (m *Manager) getBotScore(info *goxml.ArenaMatchInfo, stage int) uint32 {
	var score uint32
	switch stage {
	case activity.ArenaMatchStageHigh:
		score = info.BotHigh
	case activity.ArenaMatchStageMid:
		score = info.BotMid
	case activity.ArenaMatchStageLow:
		score = info.BotLow
	}
	return score
}

func (m *Manager) botLevel(srv activity.Servicer, level uint32) uint32 {
	max := int(level + goxml.GetData().ConfigInfoM.BotLvAddNum)
	var min int = 1
	if level > goxml.GetData().ConfigInfoM.BotLvSubNum {
		min = int(level - goxml.GetData().ConfigInfoM.BotLvSubNum)
	}
	return uint32(srv.Rand().RandBetween(min, max))
}

func (m *Manager) botName(srv activity.Servicer) string {
	first := srv.Rand().RandBetween(1, goxml.GetData().ConfigInfoM.BotFirstNameMax)
	second := srv.Rand().RandBetween(1, goxml.GetData().ConfigInfoM.BotSecondNameMax)
	return strconv.Itoa(first) + "|" + strconv.Itoa(second)
}

func (m *Manager) botBaseID(srv activity.Servicer) uint32 {
	return goxml.GetData().BotHeadInfoM.GenerateAvatar(srv.Rand())
}

func (m *Manager) resetRepeatList() {
	m.existBots = make([]uint64, 0, goxml.ArenaOpponentNum)
	m.existPlayers = make([]uint64, 0, goxml.ArenaOpponentNum)
}

// 验证单次匹配过程中，被搜到的uid是否重复
func (m *Manager) isOpUIDRepeat(uid, opID uint64) bool {
	if opID == uid {
		return true
	}
	if len(m.existPlayers) == 0 {
		return false
	}
	for _, id := range m.existPlayers {
		if id == opID {
			return true
		}
	}
	return false
}
