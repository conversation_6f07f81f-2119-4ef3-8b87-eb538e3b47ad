package arena

import (
	"app/protos/out/cl"
)

type Log struct {
	data *cl.ArenaLog
}

func (l *Log) GetID() uint32 {
	return l.data.Id
}

func (l *Log) SetID(id uint32) {
	l.data.Id = id
}

func (l *Log) GetUniqID() uint64 {
	return l.data.LogId
}

func (l *Log) Flush() *cl.ArenaLog {
	return l.data.Clone()
}

func NewArenaLog(logID uint64, reportID string, scoreChange int32, win,
	bot bool, attacker, defender *cl.MiniUserSnapshot, tm int64) *Log {
	data := &cl.ArenaLog{
		LogId:       logID,
		Attacker:    attacker,
		Defender:    defender,
		ReportId:    reportID,
		Tm:          tm,
		ScoreChange: scoreChange,
		Win:         win,
		Bot:         bot,
	}
	return &Log{
		data: data,
	}
}

func NewLogFromDB(data *cl.ArenaLog) *Log {
	return &Log{
		data: data,
	}
}
