package arena

import (
	"app/logic/activity"
	"app/protos/in/db"
	"app/protos/in/r2l"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

// 竞技场赛季
type Season struct {
	data *db.ArenaSeason
}

func NewArenaSeason() *Season {
	return &Season{
		data: &db.ArenaSeason{},
	}
}

func (s *Season) Load(season *db.ArenaSeason) {
	s.data = season
}

func (s *Season) Get() *db.ArenaSeason {
	return s.data
}

func (s *Season) GetSeasonID() uint32 {
	return s.data.Id
}

func (s *Season) Flush() *db.ArenaSeason {
	return s.data.Clone()
}

func (s *Season) Init(srv activity.Servicer) *Season {
	s.data = &db.ArenaSeason{
		Id: activity.FirstSeason,
	}
	s.Save(srv)
	return s
}

func (s *Season) Save(srv activity.Servicer) {
	msg := &r2l.L2R_SaveArenaSeason{
		Season: s.data.Clone(),
	}
	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveArenaSeason), 0, msg)
	l4g.Infof("process save arena season: %+v", s.data)
}

func (s *Season) GetNextDailyAwardTm() int64 {
	if s.data.DailyTm == 0 {
		return GetDailyAwardTm()
	}
	return s.data.DailyTm + util.DaySecs
}

func (s *Season) UpdateDailyTm(srv activity.Servicer, nextDailyTm int64) {
	s.data.DailyTm = nextDailyTm
	s.Save(srv)
}

func (s *Season) GetNextSeasonResetTm() int64 {
	if s.data.SeasonTm == 0 {
		return GetSeasonResetTm()
	}
	return s.data.SeasonTm + util.WeekSecs
}

func (s *Season) UpdateSeason(srv activity.Servicer, nextSeasonTm int64) {
	s.data.Id++
	s.data.SeasonTm = nextSeasonTm
	s.Save(srv)
}
