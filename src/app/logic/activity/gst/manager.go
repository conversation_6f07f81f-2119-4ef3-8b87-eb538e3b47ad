package gst

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/base"
	"app/logic/activity/guild"
	"app/logic/command"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/r2l"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type Manager struct {
	gstLastAward         *db.GstLastAward //Logic的状态
	lastFirstCenterAward *db.GstLastFirstCenterAward
	dragonLastAward      *db.GstDragonLastAward
	mobLastAward         *db.GstMobLastAward
	challengeLastAward   *db.GstChallengeLastAward

	crossGst *cl.GSTSta //跨服的状态
	users    map[uint64]*db.GstLogicUser

	incr, freq, lastSaveNano int64 //定时保存相关
	crossConnected           bool  //跨服连接状态标识

	changes            map[uint64]*db.GstLogicUser
	deletes            map[uint64]struct{}
	gstChange          bool
	gstLastFirstChange bool
	gstDragonChange    bool
	gstMobChange       bool
	gstChallengeChange bool

	srv command.Servicer

	//日志管理器
	logM *base.LogM
}

func NewManager(srv command.Servicer) *Manager {
	return &Manager{
		users:   make(map[uint64]*db.GstLogicUser),
		changes: make(map[uint64]*db.GstLogicUser),
		deletes: make(map[uint64]struct{}),
		freq:    activity.CommonSaveFreq + int64(activity.GST),
		srv:     srv,
		logM:    base.NewLogM(goxml.ChallengeLogMaxNum),
	}
}

func (m *Manager) Load(msg *r2l.R2L_Load) {
	l4g.Infof("manager.load: gst %+v", msg.GstLastAward)
	l4g.Infof("manager.load: gst_guild_user %+v", msg.GstLogicUser)
	l4g.Infof("manager.load: gst_dragon %+v", msg.GstDragonLastAward)
	l4g.Infof("manager.load: gst_mob %+v", msg.GstMobLastAward)
	m.users = msg.GstLogicUser
	if m.users == nil {
		m.users = make(map[uint64]*db.GstLogicUser)
	}
	m.gstLastAward = msg.GstLastAward
	if m.gstLastAward == nil {
		m.gstLastAward = &db.GstLastAward{}
	}
	m.lastFirstCenterAward = msg.GstLastFirstCenter
	if m.lastFirstCenterAward == nil {
		m.lastFirstCenterAward = &db.GstLastFirstCenterAward{}
	}
	m.dragonLastAward = msg.GstDragonLastAward
	if m.dragonLastAward == nil {
		m.dragonLastAward = &db.GstDragonLastAward{}
	}
	m.mobLastAward = msg.GstMobLastAward
	if m.mobLastAward == nil {
		m.mobLastAward = &db.GstMobLastAward{}
	}
	m.challengeLastAward = msg.GstChallengeLastAward
	if m.challengeLastAward == nil {
		m.challengeLastAward = &db.GstChallengeLastAward{}
	}
}

func (m *Manager) Init(servicer activity.Servicer) {
	m.lastSaveNano = time.Now().UnixNano()
}

func (m *Manager) OnTimer(servicer activity.Servicer, i int64) {
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(servicer)
	} else {
		m.checkSave(servicer)
	}
}

func (m *Manager) Save(servicer activity.Servicer) {
	needSave := false
	msg := &r2l.L2R_SaveGST{}
	if size := len(m.changes); size > 0 {
		msg.User = make([]*db.GstLogicUser, 0, size)
		for _, change := range m.changes {
			msg.User = append(msg.User, change.Clone())
		}

		m.changes = make(map[uint64]*db.GstLogicUser)
		needSave = true
	}

	if size := len(m.deletes); size > 0 {
		msg.DeleteUsers = make([]uint64, 0, size)
		for deleteID := range m.deletes {
			msg.DeleteUsers = append(msg.DeleteUsers, deleteID)
		}

		m.deletes = make(map[uint64]struct{})
		needSave = true
	}

	if m.gstChange {
		msg.GstLastAward = m.gstLastAward.Clone()
		needSave = true
		m.gstChange = false
	}

	if m.gstLastFirstChange {
		msg.GstLastFirstCenterAward = m.lastFirstCenterAward.Clone()
		needSave = true
		m.gstLastFirstChange = false
	}

	if m.gstDragonChange {
		msg.GstDragonLastAward = m.dragonLastAward.Clone()
		needSave = true
		m.gstDragonChange = false
	}

	if m.gstMobChange {
		msg.GstMobLastAward = m.mobLastAward.Clone()
		needSave = true
		m.gstMobChange = false
	}

	if m.gstChallengeChange {
		msg.GstChallengeLastAward = m.challengeLastAward.Clone()
		needSave = true
		m.gstChallengeChange = false
	}

	logChanges, logDeletes := m.logM.Save()
	if len(logChanges) > 0 {
		for uid, logs := range logChanges {
			if msg.ChangeLogs == nil {
				msg.ChangeLogs = make(map[uint64]*cl.GSTChallengeFightLogs)
			}
			logChangeMsg := &cl.GSTChallengeFightLogs{}
			for _, v := range logs {
				logChangeMsg.Logs = append(logChangeMsg.Logs, v.(*Log).Flush())
			}
			msg.ChangeLogs[uid] = logChangeMsg
		}
		if len(msg.ChangeLogs) > 0 {
			needSave = true
		}
		l4g.Debugf("process save change gstChallenge log datas:%+v", logChanges)
	}
	if len(logDeletes) > 0 {
		for uid, deleteIds := range logDeletes {
			if msg.DeleteLogs == nil {
				msg.DeleteLogs = make(map[uint64]*cl.GSTChallengeFightLogDeletes)
			}
			logDeleteMsg := &cl.GSTChallengeFightLogDeletes{Ids: deleteIds}
			msg.DeleteLogs[uid] = logDeleteMsg
		}
		if len(msg.DeleteLogs) > 0 {
			needSave = true
		}
		l4g.Debugf("process save delete gstChallenge log datas:%+v", logChanges)
	}

	if needSave {
		m.lastSaveNano = time.Now().UnixNano()
		servicer.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveGST), 0, msg)
	}
}

func (m *Manager) CrossClosed(servicer activity.Servicer) {
	l4g.Info("Gst get cross closed")
	m.crossConnected = false
}

func (m *Manager) CrossConnected(servicer activity.Servicer) {
	l4g.Infof("Gst get cross connected")
	m.crossConnected = true

	m.requestState(servicer)
}

func (m *Manager) IsCrossConnected() bool {
	return m.crossConnected
}

func (m *Manager) requestState(srv activity.Servicer) {
	srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTPushSta, srv.ServerID(), &l2c.L2CS_GSTPushSta{})
	l4g.Infof("GSTM.requestState serverId:%d ", srv.ServerID())
}

func (m *Manager) GetUsers() map[uint64]*db.GstLogicUser {
	return m.users
}

func (m *Manager) checkSave(srv activity.Servicer) {
	if len(m.changes) < activity.GSTSaveUserNum && m.logM.GetChanges() < activity.GSTSaveUserNum {
		return
	}

	if time.Now().UnixNano()-m.lastSaveNano >= activity.GSTSaveInterval {
		m.Save(srv)
	}
}

func (m *Manager) GetUser(uid uint64) *GstUser {
	ret := (*GstUser)(m.users[uid])
	guildM, ok := m.srv.GetActivity(activity.Guild).(*guild.Manager)
	if ok && ret != nil {
		guildUser := guildM.GetGuildUser(uid)
		if guildUser != nil {
			if ret.GuildId != guildUser.GetData().GetGuildId() {
				ret.ChatRoomId = ""
				ret.Sign = false
				ret.GuildId = guildUser.GetData().GetGuildId()
				m.SetChange(ret)
			}
		}
	}
	return ret
}

func (m *Manager) SetChange(gstUser *GstUser) {
	m.changes[gstUser.Id] = (*db.GstLogicUser)(gstUser).Clone()
	delete(m.deletes, gstUser.Id)
}

func (m *Manager) NeedAward() (bool, uint32, uint32) {
	// 正常结算
	l4g.Infof("crossGst :%v logic Last award:%v", m.crossGst, m.gstLastAward)
	if m.crossGst.Stage == cl.GST_STAGE_RoundFightState_Reward ||
		(m.crossGst.Stage == cl.GST_STAGE_RoundFightState_Finish && m.crossGst.LRound >= goxml.GetData().GuildSandTableConfigInfoM.MaxLRound) {
		if m.gstLastAward.SeasonId < m.crossGst.SeasonId {
			return true, m.crossGst.SeasonId, m.crossGst.Round
		}
		if m.gstLastAward.SeasonId == m.crossGst.SeasonId && m.gstLastAward.Round < m.crossGst.Round {
			return true, m.crossGst.SeasonId, m.crossGst.Round
		}
	}
	// 补发奖励
	if m.crossGst.Stage < cl.GST_STAGE_RoundFightState_Fight && (m.crossGst.LRound <= 1) {
		if m.gstLastAward.SeasonId+1 < m.crossGst.SeasonId {
			if m.crossGst.Round <= 1 {
				return true, m.crossGst.SeasonId - 1, goxml.GetData().GuildSandTableConfigInfoM.MaxRound
			} else {
				return true, m.crossGst.SeasonId, m.crossGst.Round - 1
			}
		} else if m.gstLastAward.SeasonId+1 == m.crossGst.SeasonId {
			if m.gstLastAward.Round == goxml.GetData().GuildSandTableConfigInfoM.MaxRound {
				return false, 0, 0
			}
			if m.crossGst.Round <= 1 {
				return true, m.crossGst.SeasonId - 1, goxml.GetData().GuildSandTableConfigInfoM.MaxRound
			} else {
				return true, m.crossGst.SeasonId, m.crossGst.Round - 1
			}
		}

		if m.gstLastAward.SeasonId == m.crossGst.SeasonId && m.gstLastAward.Round < m.crossGst.Round-1 {
			return true, m.crossGst.SeasonId, m.crossGst.Round - 1
		}
	}
	return false, 0, 0
}

func (m *Manager) CanAward(season, round uint32) bool {
	if m.gstLastAward.SeasonId < season {
		return true
	}
	if m.gstLastAward.SeasonId == season && m.gstLastAward.Round < round {
		return true
	}
	return false
}

func (m *Manager) SetAwardStage(season, round uint32) {
	m.gstLastAward.SeasonId = season
	m.gstLastAward.Round = round
	m.gstChange = true
}

func (m *Manager) NeedFirstAward() (bool, uint32, uint32, uint32) {
	l4g.Infof("crossGst :%v logic Last first center award:%v", m.crossGst, m.lastFirstCenterAward)
	if m.crossGst.Stage == cl.GST_STAGE_RoundFightState_Finish {
		if m.lastFirstCenterAward.SeasonId < m.crossGst.SeasonId {
			return true, m.crossGst.SeasonId, m.crossGst.Round, m.crossGst.LRound
		}
		if m.lastFirstCenterAward.SeasonId == m.crossGst.SeasonId && m.lastFirstCenterAward.Round < m.crossGst.Round {
			return true, m.crossGst.SeasonId, m.crossGst.Round, m.crossGst.LRound
		}
		if m.lastFirstCenterAward.SeasonId == m.crossGst.SeasonId && m.lastFirstCenterAward.Round < m.crossGst.Round && m.gstLastAward.Lround < m.crossGst.LRound {
			return true, m.crossGst.SeasonId, m.crossGst.Round, m.crossGst.LRound
		}
	}
	if m.crossGst.Stage < cl.GST_STAGE_RoundFightState_Fight {
		ret := m.GetLastLRoundState(m.crossGst)
		return true, ret.SeasonId, ret.Round, ret.LRound
	}
	return false, 0, 0, 0
}

func (m *Manager) CanFirstCenterAward(season, round, lround uint32) bool {
	if m.gstLastAward.SeasonId < season {
		return true
	}
	if m.gstLastAward.SeasonId == season && m.gstLastAward.Round < round {
		return true
	}
	if m.gstLastAward.SeasonId == season && m.gstLastAward.Round == round && m.gstLastAward.Lround < lround {
		return true
	}
	return false
}

func (m *Manager) SetFirstCenterAward(season, round, lround uint32) {
	m.lastFirstCenterAward.SeasonId = season
	m.lastFirstCenterAward.Round = round
	m.lastFirstCenterAward.Lround = lround
	m.gstLastFirstChange = true
}

// 判断当前时点是否需要发奖
func (m *Manager) NeedDragonAward() (bool, uint32, uint32) {
	crossDragonRound := goxml.GetData().GuildSandTableConfigInfoM.CalcDragonRound(m.crossGst.Round, m.crossGst.LRound, false)
	l4g.Infof("[gvg] NeedDragonAward: crossGst %+v dragonRound %d dragonLastAward %+v", m.crossGst, crossDragonRound, m.dragonLastAward)

	// 正常结算：每场龙战的结算期开始发奖
	if m.crossGst.Stage == cl.GST_STAGE_RoundFightState_Fight && m.crossGst.LRound%goxml.GetData().GuildSandTableConfigInfoM.GetDragonFightLRound() == 0 {
		// 本赛季未发奖
		if m.dragonLastAward.SeasonId < m.crossGst.SeasonId {
			return true, m.crossGst.SeasonId, crossDragonRound
		}
		// 上次领奖的龙战轮次小于当前龙战轮次
		if m.dragonLastAward.SeasonId == m.crossGst.SeasonId && m.dragonLastAward.DragonRound < crossDragonRound {
			return true, m.crossGst.SeasonId, crossDragonRound
		}
	}

	// 特殊结算：补发上一场的龙战奖励（游戏服宕机错过结算期）
	if m.crossGst.Stage < cl.GST_STAGE_RoundFightState_Fight && crossDragonRound > 0 { // crossDragonRound = 0 备战期不补发
		// 补发上一场龙战奖励
		if m.dragonLastAward.SeasonId == m.crossGst.SeasonId && m.dragonLastAward.DragonRound < crossDragonRound-1 {
			return true, m.crossGst.SeasonId, crossDragonRound - 1
		}
	}

	return false, 0, 0
}

// 龙战能否发奖
func (m *Manager) CanDragonAward(season, dragonRound uint32) bool {
	// 本赛季未发奖
	if m.dragonLastAward.SeasonId < season {
		return true
	}
	// 上次领奖的龙战轮次小于当前龙战轮次
	if m.dragonLastAward.SeasonId == season && m.dragonLastAward.DragonRound < dragonRound {
		return true
	}
	return false
}

func (m *Manager) SetDragonAwardStage(season, dragonRound uint32) {
	m.dragonLastAward.SeasonId = season
	m.dragonLastAward.DragonRound = dragonRound
	m.gstDragonChange = true
}

func (m *Manager) SetCrossStageAndClearExpireUser(sta *cl.GSTSta) {
	var clear bool
	if m.crossGst == nil {
		if sta.SeasonId != 0 && sta.Round != 0 {
			clear = true
		}
	} else {
		if sta.SeasonId != 0 && m.crossGst.SeasonId != sta.SeasonId {
			clear = true
		}
		if sta.Round != 0 && m.crossGst.Round != sta.Round {
			clear = true
		}
	}
	m.crossGst = sta.Clone()
	if clear {
		for _, gstLogicUser := range m.users {
			if gstLogicUser == nil {
				continue
			}
			if gstLogicUser.Season != sta.SeasonId {
				m.DelGstLogicUser(gstLogicUser.Id)
			}
			if gstLogicUser.Round != sta.Round {
				m.DelGstLogicUser(gstLogicUser.Id)
			}
		}
	}
}

func (m *Manager) NeedSyncUserBaseInfo(uid uint64, force bool) bool {
	gstLogicUser := m.GetUser(uid)
	if gstLogicUser == nil || !gstLogicUser.isSign() {
		return false
	}

	if force || !gstLogicUser.BaseInfoSync {
		gstLogicUser.SetBaseInfoSyncFalse()
		m.SetChange(gstLogicUser)
		return true
	}

	return false
}

func (m *Manager) SetSyncSuccess(uid uint64) {
	gstLogicUser := m.GetUser(uid)
	if gstLogicUser != nil {
		gstLogicUser.SetBasInfoSyncTrue()
		m.SetChange(gstLogicUser)
	}
}

func (m *Manager) DelGstLogicUser(uid uint64) {
	user := m.GetUser(uid)
	if user != nil {
		myLogM := m.GetChallengeLogM().GetOwnerLogs(user)
		myLogM.DelAllLog(m.GetChallengeLogM())
	}
	delete(m.users, uid)
	delete(m.changes, uid)
	m.deletes[uid] = struct{}{}
}

func (m *Manager) NeedSyncUserTeamInfo(uid uint64, force bool) bool {
	gstLogicUser := m.GetUser(uid)
	if gstLogicUser == nil || !gstLogicUser.isSign() {
		return false
	}

	if force || !gstLogicUser.TeamInfoSync {
		gstLogicUser.SetTeamInfoSyncFalse()
		m.SetChange(gstLogicUser)
		return true
	}

	return false
}

func (m *Manager) SetSyncTeamSuccess(uid uint64) {
	gstLogicUser := m.GetUser(uid)
	if gstLogicUser != nil {
		gstLogicUser.SetTeamInfoSyncTrue()
		m.SetChange(gstLogicUser)
	}
}

func (m *Manager) NeedSyncUserBuildDispatchInfo(uid uint64, force bool) bool {
	gstLogicUser := m.GetUser(uid)
	if gstLogicUser == nil || !gstLogicUser.isSign() {
		return false
	}

	if force || !gstLogicUser.BuildDispatchSync {
		gstLogicUser.SetBuildTeamSyncFalse()
		m.SetChange(gstLogicUser)
		return true
	}

	return false
}

func (m *Manager) SetSyncBuildDispatchSuccess(uid uint64) {
	gstLogicUser := m.GetUser(uid)
	if gstLogicUser != nil {
		gstLogicUser.SetBuildTeamSyncTrue()
		m.SetChange(gstLogicUser)
	}
}

func (m *Manager) IsSameSeasonRound(user *GstUser) bool {
	if user == nil || m.crossGst == nil {
		return false
	}
	return user.Season == m.crossGst.SeasonId && user.Round == m.crossGst.Round
}

func (m *Manager) NeedSign(uid uint64) bool {
	if m.crossGst == nil {
		return false
	}
	gstLogicUser := m.GetUser(uid)
	if gstLogicUser == nil {
		return true
	}
	if gstLogicUser.Season != m.crossGst.SeasonId {
		return true
	}
	return !gstLogicUser.Sign
}

func (m *Manager) SignUser(uid uint64) *GstUser {
	user := m.GetUser(uid)
	if user == nil {
		user = newUser(uid, m.crossGst.SeasonId, m.crossGst.Round, m)
		user.Sign = true
		m.users[uid] = (*db.GstLogicUser)(user)
		m.SetChange(user)
	} else {
		if !user.Sign {
			if m.crossGst.SeasonId == user.Season && m.crossGst.Round == user.Round {
				user.Sign = true
				m.SetChange(user)
			} else {
				user = newUser(uid, m.crossGst.SeasonId, m.crossGst.Round, m)
				user.Sign = true
				m.users[uid] = (*db.GstLogicUser)(user)
				m.SetChange(user)
			}
		} else {
			if !(m.crossGst.SeasonId == user.Season && m.crossGst.Round == user.Round) {
				user = newUser(uid, m.crossGst.SeasonId, m.crossGst.Round, m)
				user.Sign = true
				m.users[uid] = (*db.GstLogicUser)(user)
				m.SetChange(user)
			}
		}
	}
	return user
}

func (m *Manager) GetCrossGst() *cl.GSTSta {
	return m.crossGst
}

func (m *Manager) GetLastLRoundState(sta *cl.GSTSta) *cl.GSTSta {
	ret := sta.Clone()
	if ret == nil {
		return nil
	}
	ret.LRound--
	if ret.LRound < 1 {
		ret.Round--
		ret.LRound = goxml.GetData().GuildSandTableConfigInfoM.MaxLRound
		if ret.Round < 1 {
			ret.SeasonId--
			ret.Round = goxml.GetData().GuildSandTableConfigInfoM.MaxRound
		}
	}
	return ret
}

// 公会竞赛能否发奖
func (m *Manager) CanMobRankSettle(mobRound uint32) bool {
	return m.mobLastAward.MobRound < mobRound
}

func (m *Manager) SetMobRankSettle(mobRound uint32) {
	m.mobLastAward.MobRound = mobRound
	m.gstMobChange = true
}

func (m *Manager) CrossStateNil() bool {
	return m.crossGst == nil
}

func (m *Manager) GetChallengeLogM() *base.LogM {
	return m.logM
}

func (m *Manager) CheckChallengeNeedReward() {
	maxLround := goxml.GetData().GuildSandTableChallengeInfoM.GetCurrentGSTRoundMaxEndLRound(m.crossGst.Round)
	if maxLround == 0 {
		l4g.Errorf("ChallengeNeedReward：maxLround is 0. round:%d ", m.crossGst.Round)
		return
	}

	rewardSeasonId, rewardRound := uint32(0), uint32(0)
	if m.crossGst.LRound < maxLround {
		if m.crossGst.Round == 1 {
			// 检查补发上个赛季最后一轮的奖励
			if m.challengeLastAward.SeasonId+1 == m.crossGst.SeasonId &&
				m.challengeLastAward.Round < goxml.GetData().GuildSandTableConfigInfoM.MaxRound {
				rewardSeasonId, rewardRound = m.challengeLastAward.SeasonId, goxml.GetData().GuildSandTableConfigInfoM.MaxRound
			}
		}
		if m.crossGst.Round > 1 {
			// 检查补发上一轮的奖励
			if m.challengeLastAward.SeasonId != m.crossGst.SeasonId || m.challengeLastAward.Round+1 != m.crossGst.Round {
				rewardSeasonId, rewardRound = m.crossGst.SeasonId, m.crossGst.Round-1
			}
		}
	}

	if m.crossGst.LRound == maxLround {
		if m.crossGst.Stage == cl.GST_STAGE_RoundFightState_Fight {
			if m.challengeLastAward.SeasonId < m.crossGst.SeasonId || m.challengeLastAward.Round < m.crossGst.Round {
				rewardSeasonId, rewardRound = m.crossGst.SeasonId, m.crossGst.Round
			}
		}
	}
	if m.crossGst.LRound > maxLround {
		if m.challengeLastAward.SeasonId < m.crossGst.SeasonId || m.challengeLastAward.Round < m.crossGst.Round {
			rewardSeasonId, rewardRound = m.crossGst.SeasonId, m.crossGst.Round
		}
	}

	if rewardSeasonId > 0 && rewardRound > 0 {
		l4g.Info(" time:%d send to L2CS_GSTChallengeReward season:%d round:%d",
			time.Now().Unix(), rewardSeasonId, rewardRound)
		m.srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTChallengeAward, 0, &l2c.L2CS_GSTChallengeAward{
			SeasonId: rewardSeasonId,
			Round:    rewardRound,
		})
	}
}

func (m *Manager) CanChallengeAward(season, round uint32) bool {
	// 本赛季未发奖
	if m.challengeLastAward.SeasonId < season {
		return true
	}

	if m.challengeLastAward.Round < round {
		return true
	}
	return false
}

func (m *Manager) SetChallengeAwardStage(season, round uint32) {
	m.challengeLastAward.SeasonId = season
	m.challengeLastAward.Round = round
	m.gstChallengeChange = true
}
