package gst

import (
	"app/protos/out/cl"
)

type Log struct {
	data *cl.GSTChallengeFightLog
}

func (l *Log) GetID() uint32 {
	return l.data.Id
}

func (l *Log) SetID(id uint32) {
	l.data.Id = id
}

func (l *Log) GetUniqID() uint64 {
	return l.data.LogId
}

func (l *Log) GetCreatTm() int64 {
	return l.data.Tm
}

func (l *Log) Flush() *cl.GSTChallengeFightLog {
	return l.data.Clone()
}

func NewGstChallengeLog(fightLog *cl.GSTChallengeFightLog) *Log {
	return &Log{
		data: fightLog,
	}
}

func NewLogFromDB(data *cl.GSTChallengeFightLog) *Log {
	return &Log{
		data: data,
	}
}
