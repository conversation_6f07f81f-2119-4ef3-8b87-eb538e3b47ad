package gst

import (
	"app/logic/activity"
	"app/logic/activity/guild"
	"app/logic/character"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/ret"
)

type GstUser db.GstLogicUser

func newUser(uid uint64, season uint32, round uint32, m *Manager) *GstUser {
	var guildId uint64
	guildM, ok := m.srv.GetActivity(activity.Guild).(*guild.Manager)
	if ok {
		guildUser := guildM.GetGuildUser(uid)
		if guildUser != nil {
			guildId = guildUser.GetData().GetGuildId()
		}
	}
	return (*GstUser)(&db.GstLogicUser{
		Id:      uid,
		Season:  season,
		Round:   round,
		GuildId: guildId,
	})
}

func (g *GstUser) TaskIsRecv(taskID uint32) bool {
	if g.TaskAwarded == nil {
		return false
	}
	return g.TaskAwarded[taskID]
}

func (g *GstUser) SetTaskRecv(taskIDs []uint32) {
	if g.TaskAwarded == nil {
		g.TaskAwarded = make(map[uint32]bool)
	}
	for _, taskId := range taskIDs {
		g.TaskAwarded[taskId] = true
	}
}

func (g *GstUser) GetTaskRecv() map[uint32]bool {
	ret := make(map[uint32]bool)
	if g.TaskAwarded == nil {
		return ret
	}
	for taskId, recv := range g.TaskAwarded {
		ret[taskId] = recv
	}
	return ret
}

func (g *GstUser) AddPoint(addPoint uint32) {
	g.GstTaskPoint += addPoint
}

func (g *GstUser) GetGstTaskPointProgress() *cl.L2C_GSTTaskTypeUpdate {
	return &cl.L2C_GSTTaskTypeUpdate{
		Ret: uint32(ret.RET_OK),
		//nolint:mnd
		TaskTypeProgress: map[uint32]*cl.TaskTypeProgress{
			2301001: {
				TaskTypeId: 2301001,
				Progress:   uint64(g.GstTaskPoint),
			},
		},
	}
}

func (g *GstUser) InsertTaskPoint(taskInfo *cl.GSTTaskInfo) *cl.GSTTaskInfo {
	if taskInfo == nil {
		taskInfo = &cl.GSTTaskInfo{}
	}
	if taskInfo.TaskTypeProgress == nil {
		taskInfo.TaskTypeProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	//nolint:mnd
	taskInfo.TaskTypeProgress[2301001] = &cl.TaskTypeProgress{
		TaskTypeId: 2301001,
		Progress:   uint64(g.GstTaskPoint),
	}
	return taskInfo
}

func (g *GstUser) isSign() bool {
	if g == nil {
		return false
	}
	if !g.Sign {
		return false
	}

	return true
}

func (g *GstUser) SetBaseInfoSyncFalse() {
	g.BaseInfoSync = false
}

func (g *GstUser) SetBasInfoSyncTrue() {
	g.BaseInfoSync = true
}

func (g *GstUser) SetTeamInfoSyncFalse() {
	g.TeamInfoSync = false
}

func (g *GstUser) SetTeamInfoSyncTrue() {
	g.TeamInfoSync = true
}

func (g *GstUser) SetBuildTeamSyncFalse() {
	g.BuildDispatchSync = false
}

func (g *GstUser) SetBuildTeamSyncTrue() {
	g.BuildDispatchSync = true
}

func (g *GstUser) SetChatRoomId(id string) {
	g.ChatRoomId = id
}

func (g *GstUser) CheckDispatchBuildHeroUpdate(hero *character.Hero, isRemove bool) bool {
	if hero == nil {
		return false
	}
	for _, heroes := range g.BuildDispatchHero {
		if heroes == nil {
			continue
		}
		for i := 0; i < len(heroes.BuildDispatchHero); i++ {
			if heroes.BuildDispatchHero[i] == nil {
				continue
			}
			if heroes.BuildDispatchHero[i].HeroId == hero.GetData().GetId() {
				if isRemove {
					heroes.BuildDispatchHero = append(heroes.BuildDispatchHero[:i], heroes.BuildDispatchHero[i+1:]...)
				} else {
					heroes.BuildDispatchHero[i].Star = hero.GetData().GetStar()
				}
				return true
			}
		}
	}
	return false
}

func (g *GstUser) SyncDispatchBuildByHeroDel(hid uint64) bool {
	if hid == 0 {
		return false
	}
	for _, heroes := range g.BuildDispatchHero {
		if heroes == nil {
			continue
		}
		for i := 0; i < len(heroes.BuildDispatchHero); i++ {
			if heroes.BuildDispatchHero[i] == nil {
				continue
			}
			if heroes.BuildDispatchHero[i].HeroId == hid {
				heroes.BuildDispatchHero = append(heroes.BuildDispatchHero[:i], heroes.BuildDispatchHero[i+1:]...)
				return true
			}
		}
	}
	return false
}

func (g *GstUser) ExchangeDispatchBuildHero(buildType uint32, dispatchHero []*cl.GSTBuildDispatchHero) map[uint32]*cl.GSTBuildUserDispatchHeroes {
	clone := make(map[uint32]*cl.GSTBuildUserDispatchHeroes)
	for index, v := range g.BuildDispatchHero {
		clone[index] = v.Clone()
	}

	for userBuildType, heroes := range clone {
		if userBuildType == buildType {
			continue
		}
		if heroes == nil {
			continue
		}
		// 把其他建筑的派遣英雄下掉
		for _, clientDispatchHero := range dispatchHero {
			length := len(heroes.BuildDispatchHero)
			for i := 0; i < len(heroes.BuildDispatchHero); i++ {
				if heroes.BuildDispatchHero[i] == nil {
					continue
				}
				if heroes.BuildDispatchHero[i].HeroId == clientDispatchHero.HeroId {
					heroes.BuildDispatchHero = append(heroes.BuildDispatchHero[:i], heroes.BuildDispatchHero[i+1:]...)
					length--
				}
			}
		}
	}
	clone[buildType] = &cl.GSTBuildUserDispatchHeroes{
		BuildType:         buildType,
		BuildDispatchHero: dispatchHero,
	}

	return clone
}

func (g *GstUser) SetDispatchBuildHero(dispatchHeroes map[uint32]*cl.GSTBuildUserDispatchHeroes) {
	g.BuildDispatchHero = dispatchHeroes
}

func (g *GstUser) CheckDispatchHero(formation *cl.Formation) bool {
	heroId := make([]uint64, 0, 5) //nolint:mnd
	for _, team := range formation.Teams {
		if team == nil {
			continue
		}
		for _, hero := range team.Info {
			if hero == nil {
				continue
			}
			heroId = append(heroId, hero.Hid)
		}
	}
	uidsM := make(map[uint64]struct{})
	for _, uid := range heroId {
		uidsM[uid] = struct{}{}
	}
	for _, dispatchHeroes := range g.BuildDispatchHero {
		if dispatchHeroes == nil {
			continue
		}
		for _, hero := range dispatchHeroes.BuildDispatchHero {
			if hero == nil {
				continue
			}
			_, exist := uidsM[hero.HeroId]
			if exist {
				return false
			}
		}
	}
	return true
}

func (g *GstUser) GetUniqID() uint64 {
	return g.Id
}

func (g *GstUser) GetLogIndex() uint32 {
	return g.LogIndexId
}

func (g *GstUser) SetLogIndex(index uint32) {
	g.LogIndexId = index
}

func (g *GstUser) SetChallengeFormationLimit(status bool) {
	g.ChallengeFormationLimit = status
}

func (g *GstUser) GetChallengeFormationLimit() bool {
	return g.ChallengeFormationLimit
}
