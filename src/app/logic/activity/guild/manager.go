package guild

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/character"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"strconv"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type Manager struct {
	userToGuild map[uint64]*Guild
	idToGuild   map[uint64]*Guild

	guildUsers map[uint64]*User // 玩家单独保存的公会信息

	changes map[uint64]*db.LogicGuild
	deletes map[uint64]struct{}

	changeGuildUsers map[uint64]*User

	guildDungeonRefresh       *db.LogicGuildDungeonRefresh
	guildDungeonRefreshChange bool

	incr int64
	freq int64 // 保存频率, 单位是秒

	lastSaveNano int64 //上次存储时间戳

	crossConnected bool // 跨服是否连接

	sync    bool // 是否已和跨服同步数据
	syncing bool // 正在同步

	lastSeasonTop3 []*cl.GuildDungeonHallOfFameInfo // 本服缓存
	lastSeasonId   uint32
}

func NewManager() *Manager {
	m := &Manager{
		userToGuild: make(map[uint64]*Guild),
		idToGuild:   make(map[uint64]*Guild),

		guildUsers: make(map[uint64]*User),

		freq:                60 + int64(activity.Guild), //nolint:mnd
		changes:             make(map[uint64]*db.LogicGuild),
		deletes:             make(map[uint64]struct{}),
		changeGuildUsers:    make(map[uint64]*User),
		guildDungeonRefresh: &db.LogicGuildDungeonRefresh{},
	}
	return m
}

func (m *Manager) Load(msg *r2l.R2L_Load) {
	for _, guild := range msg.LogicGuilds {
		m.newGuild(guild)
	}
	if msg.LogicGuildDungeonRefresh != nil {
		m.guildDungeonRefresh = msg.LogicGuildDungeonRefresh
	}
}

func (m *Manager) Init(srv activity.Servicer) {
	m.lastSaveNano = time.Now().UnixNano()
}

func (m *Manager) Save(srv activity.Servicer) {
	success := false
	msg := &r2l.L2R_SaveGuild{}
	if len(m.changes) > 0 {
		msg.Changes = make([]*db.LogicGuild, 0, len(m.changes))
		for _, guild := range m.changes {
			msg.Changes = append(msg.Changes, guild.Clone())
		}
		success = true
		m.changes = make(map[uint64]*db.LogicGuild)
	}
	deletes := len(m.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint64, 0, deletes)
		for id := range m.deletes {
			msg.Deletes = append(msg.Deletes, id)
		}
		success = true
		m.deletes = make(map[uint64]struct{})
	}

	if len(m.changeGuildUsers) > 0 {
		msg.UserChanges = make([]*db.GuildUser, 0, len(m.changeGuildUsers))
		for _, guildUser := range m.changeGuildUsers {
			msg.UserChanges = append(msg.UserChanges, guildUser.data.Clone())
		}
		success = true
		m.changeGuildUsers = make(map[uint64]*User)
	}

	if m.guildDungeonRefreshChange {
		msg.DungeonRefresh = m.guildDungeonRefresh.Clone()
		success = true
		m.guildDungeonRefreshChange = false
	}

	if success {
		m.lastSaveNano = time.Now().UnixNano()
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveGuild), 0, msg)
	}
}

func (m *Manager) OnTimer(srv activity.Servicer, now int64) {
	m.incr++
	if m.incr%m.freq == 0 {
		m.Save(srv)
		if m.isNeedAward(srv, now) {
			m.award(srv)
		}
	} else {
		m.checkSave(srv)
	}
}

// 检查是否需要save
func (m *Manager) checkSave(srv activity.Servicer) {
	if len(m.changes) >= activity.GuildSaveNum || len(m.changeGuildUsers) >= activity.GuildSaveNum {
		nowNano := time.Now().UnixNano()
		if nowNano-m.lastSaveNano >= activity.GuildSaveInterval {
			l4g.Infof("check save:%d %d", len(m.changes), nowNano-m.lastSaveNano)
			m.Save(srv)
		}
	}
}

func (m *Manager) CrossClosed(srv activity.Servicer) {
	l4g.Info("guild get cross closed")
	m.crossConnected = false
}

func (m *Manager) CrossConnected(srv activity.Servicer) {
	l4g.Info("guild get cross connected")
	m.crossConnected = true
	if !m.sync && !m.syncing { // 没有和跨服同步，并且没有正在同步中
		srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildGetLogicGuilds, srv.ServerID(), &l2c.L2C_GuildGetLogicGuilds{Sid: srv.ServerID()})
		m.syncing = true
	}
	now := time.Now().Unix()
	if m.isNeedAward(srv, now) {
		m.award(srv)
	}
}

func (m *Manager) IsCrossConnected() bool {
	return m.crossConnected
}

//nolint:unparam
func (m *Manager) newGuild(data *db.LogicGuild) *db.LogicGuild {
	newGuild := &Guild{
		data:                    data,
		newMemberOnDungeonReset: make(map[uint64]struct{}),
	}

	m.idToGuild[data.Id] = newGuild

	for _, id := range newGuild.data.Members {
		m.userToGuild[id] = newGuild
	}

	return newGuild.data
}

func (m *Manager) UpdateLogicGuild(lGuild *db.LogicGuild) {
	if lGuild == nil {
		return
	}
	guild := m.GetGuildByID(lGuild.GetId())
	if guild == nil {
		return
	}
	guild.data = lGuild
	m.changes[lGuild.Id] = lGuild
}

func (m *Manager) SaveGuildUser(guildUser *User) {
	m.changeGuildUsers[guildUser.ID()] = guildUser
}

// LoadMyGuildUser
// @Description:自己上线，加载的guildUser
// @receiver m
// @param data
func (m *Manager) LoadMyGuildUser(data *db.GuildUser) {
	user := m.LoadGuildUser(data)
	if user.GuildID() == 0 { // 容错处理
		guild := m.userToGuild[user.ID()]
		if guild != nil {
			user.SetGuildID(guild.ID())
			m.SaveGuildUser(user)
		}
	}
}

// LoadGuildUser
// @Description: 加载GuildUser可能是自己触发的，也可能是别人触发的。
// @receiver m
// @param data
// @return *User
func (m *Manager) LoadGuildUser(data *db.GuildUser) *User {
	guildUser := m.GetGuildUser(data.Id)
	if guildUser == nil { // 公会中有成员上线时，会加载所有成员的信息，自己上线时可能数据已经加载了
		return m.newGuildUser(data)
	}
	return guildUser
}

func (m *Manager) newGuildUser(data *db.GuildUser) *User {
	gUser := newGuildUser(data, m)
	m.guildUsers[gUser.ID()] = gUser
	return gUser
}

func (m *Manager) GetGuildUser(id uint64) *User {
	guildUser := m.guildUsers[id]
	if guildUser != nil {
		if guildUser.timerResetDaily(int64(util.DailyZero()), m.guildDungeonRefresh) {
			m.SaveGuildUser(guildUser)
		}
	}
	return guildUser
}

func (m *Manager) NewGuildUser(uid uint64, name string) *User {
	guildUser := m.GetGuildUser(uid)
	if guildUser == nil {
		data := &db.GuildUser{
			Id:   uid,
			Name: name,
			//	UserChapter: activity.GuildDungeonInitialChapter,
		}
		guildUser = m.newGuildUser(data)
		m.SaveGuildUser(guildUser)
	}

	return guildUser
}

// AddGuild
// @Description: 本服有成员加入此公会，将改公会加入到本服数据中
// @receiver m
// @param logicGuild
// @return *Guild
func (m *Manager) AddGuild(logicGuild *db.LogicGuild) *Guild {
	m.newGuild(logicGuild)
	m.changes[logicGuild.Id] = logicGuild

	now := time.Now().Unix()
	if goxml.GuildDungeonResetting(goxml.GetData(), now) {
		// 结算期加进来的，缓存中添加，为了发奖时过滤掉
		guild := m.GetGuildByID(logicGuild.GetId())
		if guild != nil {
			for _, uid := range guild.GetMembers() {
				guild.newMemberOnDungeonReset[uid] = struct{}{}
			}
		}
	}

	return m.idToGuild[logicGuild.Id]
}

func (m *Manager) CreateGuild(logicGuild *db.LogicGuild) {
	m.newGuild(logicGuild)
	m.changes[logicGuild.Id] = logicGuild
	guild := m.idToGuild[logicGuild.Id]
	guild.SetLoadFinish()
}

// UpdateGuilds
// @Description: cross返回后，更新logic的Guild数据
// @receiver m
// @param srv
// @param data
func (m *Manager) UpdateGuilds(srv activity.Servicer, data map[uint64]*db.LogicGuild) {
	for _, logicGuild := range data {
		m.newGuild(logicGuild)
		m.changes[logicGuild.Id] = logicGuild
	}
	m.sync = true
	m.syncing = false
	m.Save(srv)
}

/*
func (m *Manager) delGuildForCrossUpdate(guild *Guild) {
	for _, id := range guild.GetMembers() {
		delete(m.userToGuild, id)
	}

	delete(m.idToGuild, guild.ID())
	m.deletes[guild.ID()] = struct{}{}
}
*/

func (m *Manager) GetGuildByUser(uid uint64) *Guild {
	return m.userToGuild[uid]
}

func (m *Manager) GetGuildByID(gid uint64) *Guild {
	return m.idToGuild[gid]
}

func (m *Manager) UpdateGuild(logicGuild *db.LogicGuild) {
	if logicGuild == nil {
		return
	}
	guild := m.GetGuildByID(logicGuild.GetId())
	if guild == nil {
		return
	}
	guild.data.Id = logicGuild.GetId()
	guild.data.Name = logicGuild.GetName()
	guild.data.Level = logicGuild.GetLevel()
	guild.data.Members = logicGuild.Members
	guild.data.Medal = logicGuild.Medal
	// 不在结算期，把记录的结算期加进来的MemberID清空
	if !goxml.GuildDungeonResetting(goxml.GetData(), time.Now().Unix()) && len(guild.newMemberOnDungeonReset) > 0 {
		guild.ClearMemberOnReset()
	}
	m.changes[guild.data.Id] = guild.data
}

func (m *Manager) IsInGuild(uid uint64) bool {
	return m.GetGuildByUser(uid) != nil
}

func (m *Manager) SetChange(guild *Guild) {
	m.changes[guild.ID()] = guild.data
}

func (m *Manager) Quit(uid uint64, guild *Guild) {
	delete(m.userToGuild, uid)
	guild.DeleteUser(uid)
	if len(guild.data.Members) == 0 {
		delete(m.idToGuild, guild.ID())
		m.deletes[guild.ID()] = struct{}{}
	}
}

func (m *Manager) Disband(uid uint64, guild *Guild) {
	delete(m.userToGuild, uid)
	delete(m.idToGuild, guild.ID())
	m.deletes[guild.ID()] = struct{}{}
}

// 踢成员
func (m *Manager) KickMember(guild *Guild, member *User) {
	if guild != nil {
		delete(m.userToGuild, member.ID())
		guild.MemberQuit(member.ID())
		m.SetChange(guild)
	}
}

// 加入公会
func (m *Manager) JoinIn(users []*User, guild *Guild) {
	for _, guildUser := range users {
		m.userToGuild[guildUser.ID()] = guild

		guild.AddMember(guildUser.ID())

		guildUser.JoinGuild(guild.ID(), uint32(character.GuildGradeMember))

		m.changeGuildUsers[guildUser.ID()] = guildUser
	}
}

func (m *Manager) GuildDataLoaded(gid uint64) {
	guild := m.idToGuild[gid]
	if guild != nil && !guild.loaded {
		guild.loaded = true
		guild.loading = false
	}
}

func (m *Manager) InitAwardTime(srv activity.Servicer, now int64) {
	initLastWeeklyTime := goxml.GetGuildDungeonWeeklyResetTime(now - util.WeekSecs)
	initLastSeasonTime := goxml.GetLastGuildDungeonSeasonResetTime(goxml.GetData())
	m.SetGuildDungeonRefreshChange(srv, initLastWeeklyTime, initLastSeasonTime)
	m.Save(srv)
}

func (m *Manager) SetLastWeeklyAwardTime(time int64) {
	m.guildDungeonRefresh.LastWeeklyAwardTm = time
}

func (m *Manager) GetLastWeeklyAwardTime() int64 {
	return m.guildDungeonRefresh.GetLastWeeklyAwardTm()
}

func (m *Manager) SetLastSeasonAwardTime(time int64) {
	m.guildDungeonRefresh.LastSeasonAwardTm = time
}

func (m *Manager) GetLastSeasonAwardTime() int64 {
	return m.guildDungeonRefresh.LastSeasonAwardTm
}

// 是否需要补发奖励
func (m *Manager) isNeedAward(srv activity.Servicer, now int64) bool {

	if !m.crossConnected {
		return false
	}

	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(now) {
		return false
	}

	awardTime := goxml.GetGuildDungeonWeeklyResetTime(now)
	lastTime := m.GetLastWeeklyAwardTime()

	// 玩法开启后，还没有进行过每周
	if lastTime == 0 {
		m.InitAwardTime(srv, now)
		lastTime = m.GetLastWeeklyAwardTime()
	}

	// 还没到本周的发奖时间，使用上周的发奖时间，检查是否需要补发上周的
	if now < awardTime {
		awardTime = awardTime - util.WeekSecs
	}

	if lastTime < awardTime {
		return true
	}
	return false
}

func (m *Manager) award(srv activity.Servicer) {
	msg := &l2c.L2C_GuildDungeonAward{
		Sid: srv.ServerID(),
	}
	srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonAward, srv.ServerID(), msg)
	l4g.Infof("[guildDungeon] request cross rank award")
}

func (m *Manager) CheckTopDivisionReparation(divisionUser map[uint32][]*db.GuildUser, topDivisionReparationM map[uint32]*TopDivisionReparation) map[uint32]*TopDivisionReparation {
	if topDivisionReparationM == nil {
		topDivisionReparationM = make(map[uint32]*TopDivisionReparation) //未领段位MASK 补偿的奖励
	}
	for topDivision, Users := range divisionUser {
		var topDivisionMask uint32
		for i := 1; i <= int(topDivision); i++ {
			topDivisionMask |= 1 << i
		}
		for _, u := range Users {
			rewardDivisionKey := m.CalcDivisionReparation(topDivision, topDivisionMask, u)
			if rewardDivisionKey == 0 {
				continue
			}
			_, exist := topDivisionReparationM[rewardDivisionKey]
			if !exist {
				tmp := &TopDivisionReparation{}
				for _, rewardDivision := range goxml.GetUint32BitPositions(rewardDivisionKey) {
					LevelUpReward := goxml.GetData().GuildDungeonLevelUpRewardInfoM.Index(rewardDivision)
					if LevelUpReward == nil {
						l4g.Errorf("CheckTopDivisionReparation get division:%d failed", rewardDivision)
						continue
					}
					tmp.Reparation = append(tmp.Reparation, LevelUpReward.ClRes...)
					//nolint:mnd
					tmp.UIDs = make([]uint64, 0, 4)
				}
				topDivisionReparationM[rewardDivisionKey] = tmp
			}
			topDivisionReparationM[rewardDivisionKey].UIDs = append(topDivisionReparationM[rewardDivisionKey].UIDs, u.Id)
		}
	}
	return topDivisionReparationM
}

func (m *Manager) CalcDivisionReparation(guildTopDivision, topDivisionMask uint32, guildUser *db.GuildUser) uint32 {
	var uMask uint32
	for _, d := range guildUser.SeasonRecvFirstDivision {
		if guildTopDivision < d {
			continue
		}
		uMask |= 1 << d
	}
	reparationKey := topDivisionMask ^ uMask
	return reparationKey
}

func (m *Manager) SendTopDivisionReparation(srv activity.Servicer, TopDivisionReparationM map[uint32]*TopDivisionReparation) {
	seasonStr := strconv.Itoa(int(m.lastSeasonId))
	for _, topDivisionReparation := range TopDivisionReparationM {
		if topDivisionReparation == nil {
			continue
		}
		if len(topDivisionReparation.UIDs) == 0 || len(topDivisionReparation.Reparation) == 0 {
			continue
		}
		character.GuildDungeonMail(srv, character.MailIDSeasonTopDivision, []string{seasonStr}, character.MergeResources(topDivisionReparation.Reparation), topDivisionReparation.UIDs)
	}
}

type TopDivisionReparation struct {
	UIDs       []uint64
	Reparation []*cl.Resource
}

func (m *Manager) SetGuildDungeonRefreshChange(srv activity.Servicer, weeklyTime int64, seasonTime int64) {
	m.guildDungeonRefreshChange = true
	m.SetLastWeeklyAwardTime(weeklyTime)
	if seasonTime > 0 {
		m.SetLastSeasonAwardTime(seasonTime)
	}
	m.Save(srv)
}

func (m *Manager) SetLastSeasonTop3(hallOfFame []*cl.GuildDungeonHallOfFameInfo, lastSeasonId uint32) {
	m.lastSeasonTop3 = hallOfFame
	m.lastSeasonId = lastSeasonId
}

func (m *Manager) GetLastSeasonTop3() ([]*cl.GuildDungeonHallOfFameInfo, uint32) {
	return m.lastSeasonTop3, m.lastSeasonId
}
