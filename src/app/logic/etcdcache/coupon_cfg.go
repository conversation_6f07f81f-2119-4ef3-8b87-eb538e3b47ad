package etcdcache

import (
	"app/protos/out/cl"
	"encoding/json"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"go.etcd.io/etcd/clientv3"
	"strconv"
	"strings"
)

type CouponCfgManager struct {
	periods []*cl.CouponCfg
	EtcdCacheBase
}

func NewCouponCfgManager() EtcdCache {
	m := &CouponCfgManager{
		periods: make([]*cl.CouponCfg, 0),
	}
	m.ProtoName = CouponCfg
	m.Prefix = false
	return m
}

func (c *CouponCfgManager) Init(resp *clientv3.GetResponse) error {
	var err error
	if len(resp.Kvs) >= 1 {
		err, _ = c.UpdateByRaw(string(resp.Kvs[0].Key), resp.Kvs[0].Value, Modify)
	}

	return err
}

func (c *CouponCfgManager) UpdateByRaw(key string, raw []byte, mType EtcdMessageType) (error, bool) {
	ret := &[]*cl.CouponCfg{}
	err := json.Unmarshal(raw, ret)
	l4g.Info("CouponCfgManager UpdateByRaw json unmarshal Data:%s", string(raw))
	if err != nil {
		return err, false
	}
	c.Update(*ret)
	return nil, false
}

func (c *CouponCfgManager) Update(newPeriods []*cl.CouponCfg) {
	// 直接根据运营配置修改，获取奖励时做了限制
	l4g.Infof("Coupon Cfg updated from etcd: %+v", newPeriods)
	//注意这里不能直接return 需要数据设置成Nil
	c.periods = newPeriods
}

func (c *CouponCfgManager) GetExtraAwards(opId uint32, couponAmount int32) []*cl.Resource {
	var totalAwards []*cl.Resource
	now := uint64(time.Now().Unix())
	for _, cfg := range c.periods {
		if cfg == nil {
			continue
		}

		// 判断运营商id
		opIds := ParseOpIds(cfg.OpIds)
		if len(opIds) == 0 {
			l4g.Errorf("[CouponCfgManager] GetExtraAwards: no opIds, cfg id %d", cfg.Id)
			continue
		}
		if !util.InUint32s(opIds, opId) {
			continue
		}

		// 判断活动时间和金额区间
		if now >= cfg.GetStartTime() && now <= cfg.GetEndTime() { //双闭区间
			for _, content := range cfg.GetRuleContent() {
				if content == nil {
					continue
				}
				condition := content.GetCondition()
				if condition == nil {
					continue
				}
				if couponAmount > condition.StartCoin && couponAmount <= condition.EndCoin { //左开右闭区间
					totalAwards = append(totalAwards, content.Awards...)
					break //同一金额只能获取一份奖励
				}
			}
			break //同一时间内只能参与一个代金券活动
		}
	}

	return totalAwards
}

func ParseOpIds(opIdStr string) []uint32 {
	nums := strings.Split(opIdStr, ",")
	opIds := make([]uint32, 0, len(nums))
	for _, numStr := range nums {
		num, _ := strconv.Atoi(numStr)
		opIds = append(opIds, uint32(num))
	}
	return opIds
}
