package etcdcache

import (
	"app/logic/helper"
	"app/protos/out/cl"
	"encoding/json"

	"go.etcd.io/etcd/clientv3"

	l4g "github.com/ivanabc/log4go"
)

type QuestionnaireManager struct {
	questionnaires     map[uint32]*cl.Questionnaire // key: questionnaireId   value: questionnaire
	questionnairesList []*cl.Questionnaire
	contents           map[uint32]*ContentCache // key: questionnaireId
	EtcdCacheBase
}

type ContentCache struct {
	List map[string]QuestionnaireContent
}

type QuestionnaireContent struct {
	Content string `json:"content"`
}

func NewQuestionnaireManager() EtcdCache {
	m := &QuestionnaireManager{
		questionnaires: make(map[uint32]*cl.Questionnaire),
		contents:       make(map[uint32]*ContentCache),
	}
	m.ProtoName = Questionnaire
	m.Prefix = false
	return m
}

func (q *QuestionnaireManager) Init(resp *clientv3.GetResponse) error {
	var err error
	if len(resp.Kvs) >= 1 {
		err, _ = q.UpdateByRaw(string(resp.Kvs[0].Key), resp.Kvs[0].Value, Modify)
	}

	return err
}

func (q *QuestionnaireManager) UpdateByRaw(key string, raw []byte, mType EtcdMessageType) (error, bool) {
	ret := make(map[string]*cl.Questionnaire)
	err := json.Unmarshal(raw, &ret)
	if err != nil {
		return err, false
	}
	l4g.Info("QuestionnaireManager UpdateByRaw json unmarshal Data:%s", string(raw))
	q.Update(ret)

	return nil, true
}

func (q *QuestionnaireManager) SendToClient(service Service) {
	msg := &cl.L2C_QuestionnaireUpdate{}
	service.BroadcastCmdToClient(cl.ID_MSG_L2C_QuestionnaireUpdate, msg)
}

func (qm *QuestionnaireManager) Update(newQuestionnaires map[string]*cl.Questionnaire) {
	qm.questionnaires = make(map[uint32]*cl.Questionnaire, len(newQuestionnaires))
	qm.questionnairesList = make([]*cl.Questionnaire, 0, len(newQuestionnaires))
	qm.contents = make(map[uint32]*ContentCache, len(newQuestionnaires))
	l4g.Info("etcd init QuestionnaireManager Data:%+v", newQuestionnaires)
	if newQuestionnaires == nil {
		return
	}
	for _, v := range newQuestionnaires {
		qm.questionnaires[v.Id] = v

		qm.questionnairesList = append(qm.questionnairesList, v)
		if v.Content != "" {
			contentCache := qm.unmarshal(v.Content)
			qm.contents[v.Id] = contentCache
		}
	}
}

func (qm *QuestionnaireManager) IsQuestionnaireExist(id uint32) bool {
	return qm.questionnaires[id] != nil
}

func (qm *QuestionnaireManager) unmarshal(dataContent string) *ContentCache {
	newCache := &ContentCache{
		List: make(map[string]QuestionnaireContent),
	}
	err := json.Unmarshal([]byte(dataContent), &newCache.List)
	if err != nil {
		l4g.Errorf("QuestionnaireManager: dataContent json unmarshal error. err:%+v", err)
	}
	return newCache
}

type CheckQuestionnaireMsg struct {
	NowTime        uint32
	Level          uint32
	Vip            uint32
	ServerDay      uint32
	OpID           uint32
	Channel        uint32
	CreateRoleDay  uint32
	RechargeAmount uint32
	FinishIDs      map[uint32]bool
}

//nolint:varnamelen
func (qm *QuestionnaireManager) QuestionnairesFlush(checkMsg *CheckQuestionnaireMsg, lang string) []*cl.Questionnaire {
	results := make([]*cl.Questionnaire, 0, len(qm.questionnaires))
	l4g.Debugf("questionnaire get param:%+v", checkMsg)
	for _, q := range qm.questionnairesList {
		l4g.Debugf("question log Data %+v", q)
		if checkMsg.FinishIDs[q.Id] {
			continue
		}
		if qm.check(q, checkMsg) {
			newQuestionnaire := q.Clone()
			newQuestionnaire.Content = ""
			results = append(results, newQuestionnaire)
			contentCache := qm.contents[q.Id]
			if contentCache == nil {
				continue
			}
			isExist := false
			if cache, exist := contentCache.List[lang]; exist {
				isExist = true
				newQuestionnaire.Content = cache.Content
			}

			if !isExist {
				if cache, exist := contentCache.List[q.DefaultLang]; exist {
					newQuestionnaire.Content = cache.Content
				}
			}
		}
	}
	return results
}

// End字段==0即为未打开该限制，因此判断为0后直接跳过该条件
func (qm *QuestionnaireManager) checkBasics(q *cl.Questionnaire, now, level, vip, serverDay, createRoleDay, rechargeAmount uint32) bool {
	//区间的判断只判断上限。方便前端根据玩家的条件变化做显示。
	return (q.EndTm == 0 || now < q.EndTm) &&
		//(q.EndLvl == 0 || q.StartLvl <= level && level < q.EndLvl) &&
		(q.EndLvl == 0 || level < q.EndLvl) &&
		//(q.EndVip == 0 || q.StartVip <= vip && vip < q.EndVip) &&
		(q.EndVip == 0 || vip < q.EndVip) &&
		(q.SrvDay == 0 || q.SrvDay <= serverDay) &&
		(q.CreateRoleDay == 0 || q.CreateRoleDay <= createRoleDay) &&
		//(q.EndRechargeAmount == 0 || q.StartRechargeAmount <= rechargeAmount && rechargeAmount < q.EndRechargeAmount)
		(q.EndRechargeAmount == 0 || rechargeAmount < q.EndRechargeAmount)
}

func (qm *QuestionnaireManager) checkOpIDAndChannel(q *cl.Questionnaire, OpID, Channel uint32) bool {
	return helper.CheckOpAndChannel(OpID, Channel, q.OpId, q.Channel)
}

func (qm *QuestionnaireManager) check(q *cl.Questionnaire, checkMsg *CheckQuestionnaireMsg) bool {
	return qm.checkBasics(q, checkMsg.NowTime, checkMsg.Level, checkMsg.Vip,
		checkMsg.ServerDay, checkMsg.CreateRoleDay, checkMsg.RechargeAmount) &&
		qm.checkOpIDAndChannel(q, checkMsg.OpID, checkMsg.Channel)
}

func (qm *QuestionnaireManager) GetQuestionnaire(id uint32) *cl.Questionnaire {
	return qm.questionnaires[id]
}
