package etcdcache

import (
	"app/goxml"
	"app/protos/out/common"
	"encoding/json"
	"fmt"
	"go.etcd.io/etcd/clientv3"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/logics/oss"
)

type OssConfig struct {
	OssList []*oss.ClientConfig   `json:"osslist"`
	OssMap  map[common.FUNCID]int // funcID => index
	EtcdCacheBase
}

func NewOssConfig() EtcdCache {
	m := &OssConfig{
		OssMap:  make(map[common.FUNCID]int),
		OssList: make([]*oss.ClientConfig, 0),
	}
	m.ProtoName = OssList
	m.Prefix = false
	return m
}

func (o *OssConfig) Init(resp *clientv3.GetResponse) error {
	var err error
	if len(resp.Kvs) >= 1 {
		err, _ = o.UpdateByRaw(string(resp.Kvs[0].Key), resp.Kvs[0].Value, Modify)
	}

	return err
}

func (o *OssConfig) UpdateByRaw(key string, raw []byte, mType EtcdMessageType) (error, bool) {
	ossConfig := new(OssConfig)
	err := json.Unmarshal(raw, ossConfig)
	if err != nil {
		return err, false
	}
	l4g.Info("OssConfig UpdateByRaw json unmarshal Data:%s", string(raw))
	o.Update(ossConfig)

	return nil, false
}

func (o *OssConfig) Update(ossList *OssConfig) {
	l4g.Info("etcd init OssConfig Data:%+v", ossList)
	if ossList == nil {
		return
	}
	if len(ossList.OssList) != int(OssType_Max) {
		panic(fmt.Sprintf("ossList len Error. %d", len(ossList.OssList)))
	}
	OssMap := make(map[common.FUNCID]OSSTYPE)
	for _, info := range goxml.GetData().FormationInfoM.Datas {
		reportType := goxml.GetData().FunctionInfoM.GetReportType(info.FunctionId)
		if reportType != 0 {
			OssMap[common.FUNCID(info.FunctionId)] = OSSTYPE(reportType)
		}
	}
	o.OssList = ossList.OssList
	for funcId, funcOssType := range OssMap {
		for index, ossConfig := range o.OssList {
			listOssType, err := strconv.Atoi(ossConfig.Bucket)
			if err != nil {
				panic(fmt.Sprintf("parse ossType Error. %s", ossConfig.Bucket))
			}
			if OSSTYPE(listOssType) == funcOssType {
				o.OssMap[funcId] = index
			}
		}
		if _, exist := o.OssMap[funcId]; !exist {
			panic(fmt.Sprintf("cant find func ossType. %s", funcId.String()))
		}
	}
}

type OSSTYPE int32

const (
	OssType_None    OSSTYPE = 0
	OssType_Forever OSSTYPE = 1
	OssType_Days_30 OSSTYPE = 2
	OssType_Max     OSSTYPE = OssType_Days_30
)
