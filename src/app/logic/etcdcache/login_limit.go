package etcdcache

import (
	"app/protos/in/config"
	"encoding/json"
	"net"
	"strings"

	"go.etcd.io/etcd/clientv3"

	l4g "github.com/ivanabc/log4go"
)

type Manager struct {
	whiteLists map[string]struct{}
	devices    map[string]struct{}
	uuids      map[string]struct{}
	EtcdCacheBase
}

func NewManager() EtcdCache {
	m := &Manager{
		whiteLists: make(map[string]struct{}),
		devices:    make(map[string]struct{}),
		uuids:      make(map[string]struct{}),
	}
	m.ProtoName = WhiteList
	m.Prefix = false
	return m
}

func (m *Manager) Init(resp *clientv3.GetResponse) error {
	var err error
	if len(resp.Kvs) >= 1 {
		err, _ = m.UpdateByRaw(string(resp.Kvs[0].Key), resp.Kvs[0].Value, Modify)
	}

	return err
}

func (m *Manager) UpdateByRaw(key string, raw []byte, mType EtcdMessageType) (error, bool) {
	ret := new(config.WhiteList)
	err := json.Unmarshal(raw, ret)
	if err != nil {
		return err, false
	}
	l4g.Info("LoginLimitManager UpdateByRaw json unmarshal Data:%s", string(raw))
	m.UpdateLoginLimit(ret)
	return nil, false
}

func (m *Manager) UpdateLoginLimit(whiteList *config.WhiteList) {
	l4g.Info("etcd UpdateLoginLimit Data:%+v", whiteList)
	if whiteList == nil {
		return
	}
	m.devices = make(map[string]struct{})
	m.whiteLists = make(map[string]struct{})
	m.uuids = make(map[string]struct{})
	if whiteList.Ips != "" {
		splitWhiteList := strings.Split(whiteList.Ips, ",")
		for _, sip := range splitWhiteList {
			ip := net.ParseIP(sip)
			if ip == nil {
				continue
			}
			m.whiteLists[sip] = struct{}{}
		}
	}
	if whiteList.Uuids != "" {
		splitUuids := strings.Split(whiteList.Uuids, ",")
		for _, suuid := range splitUuids {
			m.uuids[suuid] = struct{}{}
		}
	}
	if whiteList.DeviceIds != "" {
		splitDeviceIds := strings.Split(whiteList.DeviceIds, ",")
		for _, sdeviceId := range splitDeviceIds {
			m.devices[sdeviceId] = struct{}{}
		}
	}
}

func (m *Manager) CheckLimit(ip, deviceId, uuid string) bool {
	_, exist := m.whiteLists[ip]
	if exist {
		return true
	}
	_, exist = m.devices[deviceId]
	if exist {
		return true
	}
	_, exist = m.uuids[uuid]
	return exist
}
