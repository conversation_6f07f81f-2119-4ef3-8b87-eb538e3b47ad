package etcdcache

// 神器首发
const (
	GiftCode               = "GiftCode"
	ActivityMix            = "ActivityMix"
	CouponCfg              = "CouponCfg"
	LinkSetting            = "LinkSetting"
	LinkSummon             = "LinkSummon"
	RefundEmail            = "RefundEmail"
	Questionnaire          = "Questionnaire"
	WhiteList              = "WhiteList"
	FunctionStatus         = "FunctionStatus"
	OssList                = "OssList"
	ActivityScheduleTarget = "ActivityScheduleTarget"
)

type newCacheManager func() EtcdCache

var NewManagerFunc = map[string]newCacheManager{
	GiftCode:               NewGiftCodeAwardsManager,
	ActivityMix:            NewActivityMixManager,
	CouponCfg:              NewCouponCfgManager,
	LinkSetting:            NewLinkSettingManager,
	LinkSummon:             NewLinkSummonTimeManager,
	RefundEmail:            NewRefundMailTemplateManager,
	Questionnaire:          NewQuestionnaireManager,
	WhiteList:              NewManager,
	FunctionStatus:         NewFunctionStatusManager,
	OssList:                NewOssConfig,
	ActivityScheduleTarget: NewActivitySchedule,
}
