package db

import (
	"app/logic/helper"
	"app/protos/in/r2l"
	"strconv"

	"gitlab.qdream.com/kit/redis"

	l4g "github.com/ivanabc/log4go"
)

// 全部是MCALL形式，请求与结果一一对应
func (r *RedisActor) initLoad() {
	//加载DCID
	r.loadFuncs = append(r.loadFuncs, r.loadDCID)
	r.loadResults = append(r.loadResults, r.loadDCIDResult)

	//加载邮件
	r.loadFuncs = append(r.loadFuncs, r.loadMail)
	r.loadResults = append(r.loadResults, r.loadMailResult)
	//加载账号
	r.loadFuncs = append(r.loadFuncs, r.loadAccount)
	r.loadResults = append(r.loadResults, r.loadAccountResult)
	//加载名字
	r.loadFuncs = append(r.loadFuncs, r.loadName)
	r.loadResults = append(r.loadResults, r.loadNameResult)
	//加载全局排行榜数据
	r.loadFuncs = append(r.loadFuncs, r.loadGlobalRank)
	r.loadResults = append(r.loadResults, r.loadGlobalRankResult)
	//小排行榜
	r.loadFuncs = append(r.loadFuncs, r.loadSmallRank)
	r.loadResults = append(r.loadResults, r.loadSmallRankResult)
	//好友
	r.loadFuncs = append(r.loadFuncs, r.loadFriend)
	r.loadResults = append(r.loadResults, r.loadFriendResult)
	//通用排行榜
	r.loadFuncs = append(r.loadFuncs, r.loadCommonRank)
	r.loadResults = append(r.loadResults, r.loadCommonRankResult)
	//加载ip统计
	r.loadFuncs = append(r.loadFuncs, r.loadIPAccounts)
	r.loadResults = append(r.loadResults, r.loadIPAccountsResult)
	//加载设备统计
	r.loadFuncs = append(r.loadFuncs, r.loadDeviceIDAccounts)
	r.loadResults = append(r.loadResults, r.loadDeviceIDAccountsResult)
	//竞技场赛季
	r.loadFuncs = append(r.loadFuncs, r.loadArenaSeason)
	r.loadResults = append(r.loadResults, r.loadArenaSeasonResult)
	//普通竞技场
	r.loadFuncs = append(r.loadFuncs, r.loadArena)
	r.loadResults = append(r.loadResults, r.loadArenaResult)
	//封禁
	r.loadFuncs = append(r.loadFuncs, r.loadUserBans)
	r.loadResults = append(r.loadResults, r.loadUserBansResult)
	//排行成就
	r.loadFuncs = append(r.loadFuncs, r.loadRankAchieves)
	r.loadResults = append(r.loadResults, r.loadRankAchievesResult)
	//迷宫地图模板
	r.loadFuncs = append(r.loadFuncs, r.loadMazeMap)
	r.loadResults = append(r.loadResults, r.loadMazeMapResult)
	//个人boss统计数据
	r.loadFuncs = append(r.loadFuncs, r.loadMiragePass)
	r.loadResults = append(r.loadResults, r.loadMiragePassResult)
	//公会
	r.loadFuncs = append(r.loadFuncs, r.loadLogicGuild)
	r.loadResults = append(r.loadResults, r.loadLogicGuildResult)
	//公会副本
	//r.loadFuncs = append(r.loadFuncs, r.loadGuildDungeon)
	//r.loadResults = append(r.loadResults, r.loadGuildDungeonResult)
	//公会重置信息
	r.loadFuncs = append(r.loadFuncs, r.loadGuildDungeonReset)
	r.loadResults = append(r.loadResults, r.loadGuildDungeonResetResult)
	//多语言缓存
	r.loadFuncs = append(r.loadFuncs, r.loadMultiLang)
	r.loadResults = append(r.loadResults, r.loadMultiLangResult)
	//已禁用协议
	r.loadFuncs = append(r.loadFuncs, r.loadBanCmd)
	r.loadResults = append(r.loadResults, r.loadBanCmdResult)
	//竞技场上赛季前三名数据
	r.loadFuncs = append(r.loadFuncs, r.loadArenaLastSeasonTop)
	r.loadResults = append(r.loadResults, r.loadArenaLastSeasonTopResult)
	//神树争霸
	r.loadFuncs = append(r.loadFuncs, r.loadWrestleUser)
	r.loadResults = append(r.loadResults, r.loadWrestleUserResult)
	r.loadFuncs = append(r.loadFuncs, r.loadWrestle)
	r.loadResults = append(r.loadResults, r.loadWrestleResult)
	//密林 - 0.9.8
	r.loadFuncs = append(r.loadFuncs, r.loadFlower)
	r.loadResults = append(r.loadResults, r.loadFlowerResult)
	//全民无双消息
	r.loadFuncs = append(r.loadFuncs, r.loadMonthTasksMsg)
	r.loadResults = append(r.loadResults, r.loadMonthTasksMsgResult)
	// 世界boss
	r.loadFuncs = append(r.loadFuncs, r.loadWorldBoss)
	r.loadResults = append(r.loadResults, r.loadWorldBossResult)
	// 百塔赛季重置
	r.loadFuncs = append(r.loadFuncs, r.loadTowerSeasonReset)
	r.loadResults = append(r.loadResults, r.loadTowerSeasonResetResult)
	//巅峰竞技场
	r.loadFuncs = append(r.loadFuncs, r.loadPeak)
	r.loadResults = append(r.loadResults, r.loadPeakResult)
	r.loadFuncs = append(r.loadFuncs, r.loadPeakUser)
	r.loadResults = append(r.loadResults, r.loadPeakUserResult)
	//公会战
	r.loadFuncs = append(r.loadFuncs, r.loadGSTLogicUser)
	r.loadResults = append(r.loadResults, r.loadGstLogicUserResult)
	r.loadFuncs = append(r.loadFuncs, r.loadGstLastAward)
	r.loadResults = append(r.loadResults, r.loadGstLastAwardResult)
	r.loadFuncs = append(r.loadFuncs, r.loadGstLastFirstCenter)
	r.loadResults = append(r.loadResults, r.loadGstLastFirstCenterResult)
	r.loadFuncs = append(r.loadFuncs, r.loadGstDragonLastAward)
	r.loadResults = append(r.loadResults, r.loadGstDragonLastAwardResult)
	r.loadFuncs = append(r.loadFuncs, r.loadGstMobLastAward)
	r.loadResults = append(r.loadResults, r.loadGstMobLastAwardResult)
	r.loadFuncs = append(r.loadFuncs, r.loadGstChallengeLastAward)
	r.loadResults = append(r.loadResults, r.loadGstChallengeLastAwardResult)
	// 人群包
	r.loadFuncs = append(r.loadFuncs, r.loadPeopleGroupPackage)
	r.loadResults = append(r.loadResults, r.loadPeopleGroupPackageResult)
	//赛季竞技场
	r.loadFuncs = append(r.loadFuncs, r.loadSeasonArenaUser)
	r.loadResults = append(r.loadResults, r.loadSeasonArenaUserResult)
	r.loadFuncs = append(r.loadFuncs, r.loadSeasonArenaLastAward)
	r.loadResults = append(r.loadResults, r.loadSeasonArenaLastAwardResult)
	r.loadFuncs = append(r.loadFuncs, r.loadSeasonArenaTopUser)
	r.loadResults = append(r.loadResults, r.loadSeasonArenaTopUserResult)
	//热度榜
	r.loadFuncs = append(r.loadFuncs, r.loadHotRankCollectionLog)
	r.loadResults = append(r.loadResults, r.loadHotRankCollectionLogResult)
	// 天赋树养成热度
	r.loadFuncs = append(r.loadFuncs, r.loadLogicTalentTreeHot)
	r.loadResults = append(r.loadResults, r.loadLogicTalentTreeHotResult)
	//离线资源
	r.loadFuncs = append(r.loadFuncs, r.loadOfflineResource)
	r.loadResults = append(r.loadResults, r.loadOfflineResourceResult)
	//地宫冲榜
	r.loadFuncs = append(r.loadFuncs, r.loadActivityTower)
	r.loadResults = append(r.loadResults, r.loadActivityTowerResult)
	//赛季冲榜
	r.loadFuncs = append(r.loadFuncs, r.loadSeasonComplianceAward)
	r.loadResults = append(r.loadResults, r.loadSeasonComplianceAwardResult)
	//热修复代码
	r.loadFuncs = append(r.loadFuncs, r.loadHotfixCode)
	r.loadResults = append(r.loadResults, r.loadHotfixCodeResult)
	//女神夺宝
	r.loadFuncs = append(r.loadFuncs, r.loadGoddessPrizeDraw)
	r.loadResults = append(r.loadResults, r.loadGoddessPrizeDrawResult)
	//公平竞技场
	//r.loadFuncs = append(r.loadFuncs, r.loadBalanceArenaUser)
	//r.loadResults = append(r.loadResults, r.loadBalanceArenaUserResult)
	r.loadFuncs = append(r.loadFuncs, r.loadBalanceArena)
	r.loadResults = append(r.loadResults, r.loadBalanceArenaResult)
}

// DC ID
func (r *RedisActor) loadDCID(msg *r2l.L2R_Load) {
	r.Client.Append("HGET", RedisHashDCID, msg.Sid)
	r.dcID = uint32(helper.CreateDCID(msg.Sid))
}

func (r *RedisActor) loadDCIDResult(msg *r2l.R2L_Load) {
	reply := r.Client.GetReply()
	if reply.Err != nil {
		l4g.Errorf("get dcid failed: %s", reply.Err)
		msg.Ret = uint32(r2l.RET_ERROR)
		return
	}
	if reply.Type != redis.NilReply {
		dcID, err := reply.Int64()
		if err != nil {
			l4g.Errorf("get dcid failed: %s", err)
			msg.Ret = uint32(r2l.RET_ERROR)
			return
		}
		r.dcID = uint32(dcID)
	}
	l4g.Infof("dc id: %d", r.dcID)
}

// 邮件
func (r *RedisActor) loadMail(msg *r2l.L2R_Load) {
	r.RopCl.GetAllMailMCallSK(msg.Sid)
}

func (r *RedisActor) loadMailResult(msg *r2l.R2L_Load) {
	if data, err := r.RopCl.GetSomeMailByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get service mails error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.Mails = data
	}
}

// 账号
func (r *RedisActor) loadAccount(msg *r2l.L2R_Load) {
	r.Client.Append("HGETALL", RedisHashAccount)
}

func (r *RedisActor) loadAccountResult(msg *r2l.R2L_Load) {
	if data, err := r.Client.GetReply().Hash(); err != nil {
		l4g.Errorf("get account size error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		r.accounts = data
		l4g.Infof("account size: %d", len(r.accounts))
	}
}

// 名字
func (r *RedisActor) loadName(msg *r2l.L2R_Load) {
	r.Client.Append("HGETALL", RedisHashName)
}

func (r *RedisActor) loadNameResult(msg *r2l.R2L_Load) {
	if data, err := r.Client.GetReply().Hash(); err != nil {
		l4g.Errorf("get name size error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		r.names = data
		l4g.Infof("name size: %d", len(r.names))
	}
}

// 全服排行榜
func (r *RedisActor) loadGlobalRank(msg *r2l.L2R_Load) {
	r.RopDb.GetAllGlobalRankMCall()
}

func (r *RedisActor) loadGlobalRankResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeGlobalRankByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get rank data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.RankData = data
	}
}

func (r *RedisActor) loadSmallRank(msg *r2l.L2R_Load) {
	r.RopDb.GetAllSmallRankMCall()
}

func (r *RedisActor) loadSmallRankResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeSmallRankByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get small rank data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.SmallRank = data
	}
}

func (r *RedisActor) loadFriend(msg *r2l.L2R_Load) {
	r.RopDb.GetAllFriendsMCall()
}

func (r *RedisActor) loadFriendResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeFriendsByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get friend data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.Friends = data
	}
}

func (r *RedisActor) loadCommonRank(msg *r2l.L2R_Load) {
	for _, v := range msg.RankIds {
		r.RopDb.GetAllCommonRankMCallSK(uint64(v))
	}
}

func (r *RedisActor) loadCommonRankResult(msg *r2l.R2L_Load) {
	msg.CommonRank = make(map[uint32]*r2l.CommonRankData)
	for _, v := range msg.RankIds {
		if data, err := r.RopDb.GetSomeCommonRankByReply(r.Client.GetReply()); err != nil {
			l4g.Errorf("get some rank data error: %d %s", msg.Sid, err)
			msg.Ret = uint32(r2l.RET_ERROR)
		} else {
			returnData := &r2l.CommonRankData{Data: data}
			msg.CommonRank[v] = returnData
		}
	}
}

func (r *RedisActor) loadIPAccounts(msg *r2l.L2R_Load) {
	r.Client.Append("ZRANGEBYSCORE", RedisZSetIP, r.limitCfg.IPAccountsHW, "+inf", "WITHSCORES")
}

func (r *RedisActor) loadIPAccountsResult(msg *r2l.R2L_Load) {
	if data, err := r.Client.GetReply().Hash(); err != nil {
		l4g.Errorf("get ip accounts error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		for key, value := range data {
			if num, err := strconv.Atoi(value); err != nil {
				l4g.Errorf("get ip accounts error: %d %s", msg.Sid, err)
				msg.Ret = uint32(r2l.RET_ERROR)
			} else {
				r.ipSet[key] = num
			}
		}
		l4g.Infof("ip accounts: %d", len(r.ipSet))
	}
}

func (r *RedisActor) loadDeviceIDAccounts(msg *r2l.L2R_Load) {
	r.Client.Append("ZRANGEBYSCORE", RedisZSetDeviceID, r.limitCfg.DeviceIDAccountsHW, "+inf", "WITHSCORES")
}

func (r *RedisActor) loadDeviceIDAccountsResult(msg *r2l.R2L_Load) {
	if data, err := r.Client.GetReply().Hash(); err != nil {
		l4g.Errorf("get device id accounts error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		for key, value := range data {
			if num, err := strconv.Atoi(value); err != nil {
				l4g.Errorf("get device id accounts error: %d %s", msg.Sid, err)
				msg.Ret = uint32(r2l.RET_ERROR)
			} else {
				r.deviceIDSet[key] = num
			}
		}
		l4g.Infof("device id accounts: %d", len(r.deviceIDSet))
	}
}

func (r *RedisActor) loadArenaSeason(msg *r2l.L2R_Load) {
	r.RopDb.GetArenaSeasonMCall()
}

func (r *RedisActor) loadArenaSeasonResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetArenaSeasonByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get common arena season data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.ArenaSeason = data
	}
}

func (r *RedisActor) loadArena(msg *r2l.L2R_Load) {
	r.RopCl.GetAllArenaMCall()
}

func (r *RedisActor) loadArenaResult(msg *r2l.R2L_Load) {
	if data, err := r.RopCl.GetSomeArenaByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get common arena data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.Arena = data
	}
}

func (r *RedisActor) loadUserBans(msg *r2l.L2R_Load) {
	r.RopDb.GetAllUserBanMCall()
}

func (r *RedisActor) loadUserBansResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeUserBanByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get gm bans data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.UserBans = data
	}
}

func (r *RedisActor) loadRankAchieves(msg *r2l.L2R_Load) {
	r.RopDb.GetAllRankAchievesMCall()
}

func (r *RedisActor) loadRankAchievesResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeRankAchievesByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get rank achievements data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.RankAchieves = data
	}
}

// 加载迷宫地图模板
func (r *RedisActor) loadMazeMap(msg *r2l.L2R_Load) {
	r.RopCl.GetAllMazeMapMCall()
}

func (r *RedisActor) loadMazeMapResult(msg *r2l.R2L_Load) {
	if data, err := r.RopCl.GetSomeMazeMapByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get maze basic map data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.MazeMap = data
	}
}

func (r *RedisActor) loadMiragePass(msg *r2l.L2R_Load) {
	r.RopDb.GetAllMiragePassMCall()
}

func (r *RedisActor) loadMiragePassResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeMiragePassByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get mirage pass data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.MiragePass = data
	}
}

// 加载公会
func (r *RedisActor) loadLogicGuild(msg *r2l.L2R_Load) {
	r.RopDb.GetAllLogicGuildMCall()
}

func (r *RedisActor) loadLogicGuildResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeLogicGuildByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get guild data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.LogicGuilds = data
	}
}

// 加载公会副本重置信息
func (r *RedisActor) loadGuildDungeonReset(msg *r2l.L2R_Load) {
	r.RopDb.GetLogicGuildDungeonRefreshMCall()
}

func (r *RedisActor) loadGuildDungeonResetResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetLogicGuildDungeonRefreshByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get logicGuildDungeonReset data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.LogicGuildDungeonRefresh = data
	}
}

// 加载多语言缓存
func (r *RedisActor) loadMultiLang(msg *r2l.L2R_Load) {
	r.RopDb.GetAllMultiLangMCall()
}

func (r *RedisActor) loadMultiLangResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeMultiLangByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get MultiLang data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.MultiLangs = data
	}
}

// 封/解禁协议
func (r *RedisActor) loadBanCmd(msg *r2l.L2R_Load) {
	r.RopDb.GetBanCmdMCall()
}

func (r *RedisActor) loadBanCmdResult(msg *r2l.R2L_Load) {
	if cmds, err := r.RopDb.GetBanCmdByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get module closed data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.BanCmds = cmds
	}
}

// 竞技场上赛季前三名数据
func (r *RedisActor) loadArenaLastSeasonTop(msg *r2l.L2R_Load) {
	r.RopCl.GetAllArenaLastSeasonTopMCall()
}

func (r *RedisActor) loadArenaLastSeasonTopResult(msg *r2l.R2L_Load) {
	if data, err := r.RopCl.GetSomeArenaLastSeasonTopByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get arena last season top data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.ArenaLastSeasonTop = data
	}
}

func (r *RedisActor) loadWrestle(msg *r2l.L2R_Load) {
	r.RopDb.GetWrestleMCall()
}

func (r *RedisActor) loadWrestleResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetWrestleByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get wrestle data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.Wrestle = data
	}
}

func (r *RedisActor) loadWrestleUser(msg *r2l.L2R_Load) {
	r.RopDb.GetAllWrestleUserMCall()
}

func (r *RedisActor) loadWrestleUserResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeWrestleUserByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get wrestleUser data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.WrestleUser = data
	}
}

// 加载密林 - 0.9.8
func (r *RedisActor) loadFlower(msg *r2l.L2R_Load) {
	r.RopCl.GetAllFlowerMCall()
}

func (r *RedisActor) loadFlowerResult(msg *r2l.R2L_Load) {
	if data, err := r.RopCl.GetSomeFlowerByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get flower data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.Flowers = data
	}
}

// 加载全民无双消息
func (r *RedisActor) loadMonthTasksMsg(msg *r2l.L2R_Load) {
	r.RopCl.GetAllMonthTasksMsgMCall()
}

func (r *RedisActor) loadMonthTasksMsgResult(msg *r2l.R2L_Load) {
	if data, err := r.RopCl.GetSomeMonthTasksMsgByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get monthTasksMsg data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.MonthTasksMsg = data
	}
}

// 加载公会副本重置信息
func (r *RedisActor) loadWorldBoss(msg *r2l.L2R_Load) {
	r.RopDb.GetWorldBossSettleMCall()
}

func (r *RedisActor) loadWorldBossResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetWorldBossSettleByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get loadWorldBossResult data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.WorldBoss = data
	}
}

// 加载百塔重置信息
func (r *RedisActor) loadTowerSeasonReset(msg *r2l.L2R_Load) {
	r.RopDb.GetTowerSeasonResetMCall()
}

func (r *RedisActor) loadTowerSeasonResetResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetTowerSeasonResetByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get loadTowerSeasonResetResult data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.TowerSeasonReset = data
	}
}

// 加载巅峰竞技场 - peak
func (r *RedisActor) loadPeak(msg *r2l.L2R_Load) {
	r.RopDb.GetPeakMCall()
}

func (r *RedisActor) loadPeakResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetPeakByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get peak data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.Peak = data
	}
}

// 加载巅峰竞技场 - peakUser
func (r *RedisActor) loadPeakUser(msg *r2l.L2R_Load) {
	r.RopCl.GetAllPeakUserMCall()
}

func (r *RedisActor) loadPeakUserResult(msg *r2l.R2L_Load) {
	if data, err := r.RopCl.GetSomePeakUserByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get peakUser data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.PeakUser = data
	}
}

// 加公会战玩家 - peakUser
func (r *RedisActor) loadGSTLogicUser(msg *r2l.L2R_Load) {
	r.RopDb.GetAllGstLogicUserMCall()
}

func (r *RedisActor) loadGstLogicUserResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeGstLogicUserByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get GstLogicUser data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.GstLogicUser = data
	}
}

func (r *RedisActor) loadGstLastAward(msg *r2l.L2R_Load) {
	r.RopDb.GetGstLastAwardMCall()
}

func (r *RedisActor) loadGstLastAwardResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetGstLastAwardByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get gst last award result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.GstLastAward = data
	}
}

func (r *RedisActor) loadGstLastFirstCenter(msg *r2l.L2R_Load) {
	r.RopDb.GetGstLastFirstCenterAwardMCall()
}

func (r *RedisActor) loadGstLastFirstCenterResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetGstLastFirstCenterAwardByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get last first center result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.GstLastFirstCenter = data
	}
}

func (r *RedisActor) loadGstDragonLastAward(msg *r2l.L2R_Load) {
	r.RopDb.GetGstDragonLastAwardMCall()
}

func (r *RedisActor) loadGstDragonLastAwardResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetGstDragonLastAwardByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get gst dragon last award result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.GstDragonLastAward = data
	}
}

func (r *RedisActor) loadGstMobLastAward(_ *r2l.L2R_Load) {
	r.RopDb.GetGstMobLastAwardMCall()
}

func (r *RedisActor) loadGstMobLastAwardResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetGstMobLastAwardByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get gst Mob last award result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.GstMobLastAward = data
	}
}

func (r *RedisActor) loadGstChallengeLastAward(_ *r2l.L2R_Load) {
	r.RopDb.GetGstChallengeLastAwardMCall()
}

func (r *RedisActor) loadGstChallengeLastAwardResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetGstChallengeLastAwardByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get gst challenge last award result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.GstChallengeLastAward = data
	}
}

// 人群包数据
func (r *RedisActor) loadPeopleGroupPackage(msg *r2l.L2R_Load) {
	r.RopDb.GetAllPeopleGroupPackageMCall()
}

func (r *RedisActor) loadPeopleGroupPackageResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomePeopleGroupPackageByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get PeopleGroupPackage data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.PeopleGroupPackage = data
	}
}

// 参加赛季竞技场的玩家
func (r *RedisActor) loadSeasonArenaUser(msg *r2l.L2R_Load) {
	r.RopDb.GetAllSeasonArenaUserMCall()
}

func (r *RedisActor) loadSeasonArenaUserResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeSeasonArenaUserByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get seasonArena user data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.SeasonArenaUser = data
	}
}

// 加载上一次发奖状态
func (r *RedisActor) loadSeasonArenaLastAward(msg *r2l.L2R_Load) {
	r.RopDb.GetSeasonArenaLastAwardMCall()
}

func (r *RedisActor) loadSeasonArenaLastAwardResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSeasonArenaLastAwardByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get seasonArena award result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.SeasonArenaLastAward = data
	}
}

func (r *RedisActor) loadSeasonArenaTopUser(msg *r2l.L2R_Load) {
	r.RopDb.GetSeasonArenaTopUserMCall()
}

func (r *RedisActor) loadSeasonArenaTopUserResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSeasonArenaTopUserByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get seasonArena top user result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.SeasonArenaTopUser = data
	}
}

func (r *RedisActor) loadHotRankCollectionLog(msg *r2l.L2R_Load) {
	r.RopDb.GetAllLogicHotRankCollectionLogMCall()
}

func (r *RedisActor) loadHotRankCollectionLogResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeLogicHotRankCollectionLogByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get hot rank collection log  result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.LogicHotRankCollectionLog = data
	}
}

func (r *RedisActor) loadLogicTalentTreeHot(msg *r2l.L2R_Load) {
	r.RopDb.GetLogicTalentTreeHotMCall()
}

func (r *RedisActor) loadLogicTalentTreeHotResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetLogicTalentTreeHotByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get logicTalentTreeHot result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.TalentTreeHot = data
	}
}

// 离线修改资源
func (r *RedisActor) loadOfflineResource(msg *r2l.L2R_Load) {
	r.RopDb.GetAllOfflineResourceMCall()
}

func (r *RedisActor) loadOfflineResourceResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeOfflineResourceByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get service mails error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.OfflineResource = data
	}
}

func (r *RedisActor) loadActivityTower(msg *r2l.L2R_Load) {
	r.RopDb.GetActivityTowerMCall()
}

func (r *RedisActor) loadActivityTowerResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetActivityTowerByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get activity tower result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.ActivityTower = data
	}
}

func (r *RedisActor) loadSeasonComplianceAward(_ *r2l.L2R_Load) {
	r.RopDb.GetSeasonComplianceAwardMCall()
}

func (r *RedisActor) loadSeasonComplianceAwardResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSeasonComplianceAwardByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get season compliance award result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.SeasonComplianceAward = data
	}
}

func (r *RedisActor) loadHotfixCode(_ *r2l.L2R_Load) {
	r.RopDb.GetAllHotfixCodeMCall()
}

func (r *RedisActor) loadHotfixCodeResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetSomeHotfixCodeByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get hotfixCode data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.HotfixCodes = data
	}
}

func (r *RedisActor) loadGoddessPrizeDraw(_ *r2l.L2R_Load) {
	r.RopDb.GetGoddessPrizeDrawMCall()
}

func (r *RedisActor) loadGoddessPrizeDrawResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetGoddessPrizeDrawByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get season compliance award result data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.GoddessPrizeDraw = data
	}
}

func (r *RedisActor) loadBalanceArena(msg *r2l.L2R_Load) {
	r.RopDb.GetBalanceArenaMCall()
}

func (r *RedisActor) loadBalanceArenaResult(msg *r2l.R2L_Load) {
	if data, err := r.RopDb.GetBalanceArenaByReply(r.Client.GetReply()); err != nil {
		l4g.Errorf("get balanceArena data error: %d %s", msg.Sid, err)
		msg.Ret = uint32(r2l.RET_ERROR)
	} else {
		msg.BalanceArena = data
	}
}
