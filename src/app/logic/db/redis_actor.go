package db

import (
	"app/goxml"
	"app/protos/out/common"
	"container/list"
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync/atomic"

	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	// "app/goxml"
	"app/logic/db/redisop"
	"app/logic/helper"
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/redis"
	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"gitlab.qdream.com/kit/sea/lru"

	l4g "github.com/ivanabc/log4go"
)

const commandMaxExecuteTime = 10000000

//const redisTimeout = 5 * time.Second

const redisTimeout = 10 * time.Second //修改为10s.线上出现过redislv写消耗6s的情况

const maxPrivateMessageIDCacheSize = 1024

type RedisRequest struct {
	parse.Message
	Data interface{}
}

func (r *RedisRequest) init(cmd uint32, uid uint64, data interface{}) {
	r.Cmd = cmd
	r.UID = uid
	r.Data = data
}

func (r *RedisRequest) Process(param actor.Receiver) error {
	ractor := param.(*RedisActor)
	ractor.handle(r)
	atomic.AddUint64(&ractor.DBMsgsCount, 1)
	return nil
}

type RedisRespone struct {
	parse.Message
	Data interface{}
}

func (r *RedisRespone) init(cmd uint32, uid uint64, data interface{}) {
	r.Cmd = cmd
	r.UID = uid
	r.Data = data
}

type LimitConfig struct {
	MaxAccounts        int
	IPAccounts         int
	DeviceIDAccounts   int
	IPAccountsHW       int
	DeviceIDAccountsHW int
	BanRegister        bool
	Status             int
}

type RedisActor struct {
	*actor.Actor
	Client                *redis.Client
	MsgQ                  chan *RedisRespone
	queue                 *list.List
	ctx                   *ctx.Group
	serviceCtx            context.Context
	cmds                  *parse.CommandM
	RopDb                 *redisop.DbClient
	RopCl                 *redisop.ClClient
	accounts              map[string]string //全服玩家账号
	names                 map[string]string //全服玩家名字
	ipSet                 map[string]int
	deviceIDSet           map[string]int
	DBMsgsCount           uint64 //redis actor 自己处理的db消息数量
	dcID                  uint32
	privateMessageIDCache *lru.Cache // 缓存的私聊消息ID  key: uid   value：消息ID列表

	limitCfg *LimitConfig
	rd       *rand.Rand

	loadFuncs   []func(*r2l.L2R_Load)
	loadResults []func(*r2l.R2L_Load)
}

func NewSimpleRedisActor(client *redis.Client) *RedisActor {
	return &RedisActor{
		Client: client,
		RopDb:  &redisop.DbClient{Client: client},
		RopCl:  &redisop.ClClient{Client: client},
		limitCfg: &LimitConfig{
			MaxAccounts:        20000, //nolint:mnd
			IPAccounts:         100,   //nolint:mnd
			DeviceIDAccounts:   100,   //nolint:mnd
			IPAccountsHW:       10,    //nolint:mnd
			DeviceIDAccountsHW: 10,    //nolint:mnd
		},
	}
}

func NewRedisActor(serviceCtx context.Context, ctx *ctx.Group,
	redisAddr, password string, dbIndex uint32,
	cfg *actor.Config, limitCfg *LimitConfig) *RedisActor {
	timeout := redisTimeout
	if goxml.GetData().ServerInfoM.IsGM() {
		timeout = time.Duration(goxml.LongTimeout) * time.Second
	}
	client, err := redis.DialTimeout("tcp", redisAddr, password, dbIndex, timeout, true)
	if err != nil {
		l4g.Errorf("connect redis serve error: %s %s", redisAddr, err)
		panic(err)
	}

	l4g.Infof("connect redis(%s-%d) success", redisAddr, dbIndex)

	act := actor.NewActor(ctx.CreateChild(), cfg)
	rActor := &RedisActor{
		Actor:                 act,
		Client:                client,
		MsgQ:                  make(chan *RedisRespone, 1024), //nolint:mnd
		queue:                 list.New(),
		ctx:                   ctx,
		serviceCtx:            serviceCtx,
		cmds:                  parse.NewCommandM(uint32(r2l.ID_MSG_MIN), uint32(r2l.ID_MSG_MAX), commandMaxExecuteTime),
		RopDb:                 &redisop.DbClient{Client: client},
		RopCl:                 &redisop.ClClient{Client: client},
		limitCfg:              limitCfg,
		ipSet:                 make(map[string]int),
		deviceIDSet:           make(map[string]int),
		privateMessageIDCache: lru.New(maxPrivateMessageIDCacheSize),
		rd:                    rand.New(time.Now().UnixNano()),
	}
	rActor.OpenRecoverPanic()
	rActor.initLoad()
	act.TickerCB = func(int64) {
		rActor.moveToChan()
		qLen := rActor.queue.Len()
		if qLen > queueHighWaterMark {
			l4g.Errorf("redis msg queue in highwater : %d", qLen)
		}
	}
	act.AddTimer(&TenSecondTimer{act: rActor}, time.Now().Unix()+10, 10) //nolint:mnd
	return rActor
}

func (r *RedisActor) getServiceContext() context.Context { return r.serviceCtx }
func (r *RedisActor) GetCommandM() *parse.CommandM       { return r.cmds }
func (r *RedisActor) GetClient() *redis.Client           { return r.Client }

func (r *RedisActor) Close() {
	r.ctx.Stop()
	r.ctx.Wait()
	r.Client.Close()
	l4g.Infof("redis client close...")
	r.ctx.Finish()
}

func (r *RedisActor) OpenRecoverPanic() {
	r.cmds.OpenRecoverPanic()
}

func (r *RedisActor) AddMessage(cmd uint32, uid uint64, data interface{}) {
	rr := new(RedisRequest)
	rr.init(cmd, uid, data)
	r.Actor.AddMessage(rr)
}

const queueHighWaterMark = 5000

func (r *RedisActor) ReturnMessage(cmd uint32, uid uint64, data interface{}) {
	rr := new(RedisRespone)
	rr.init(cmd, uid, data)

	qLen := r.queue.Len()
	if qLen > 0 {
		r.queue.PushBack(rr)
		r.moveToChan()
		return
	}
	select {
	case r.MsgQ <- rr:
	default:
		r.queue.PushBack(rr)
	}
}

func (r *RedisActor) moveToChan() {
	for e := r.queue.Front(); e != nil; {
		select {
		case r.MsgQ <- e.Value.(*RedisRespone):
			tmp := e
			e = e.Next()
			r.queue.Remove(tmp)
		default:
			return
		}
	}
}

func (r *RedisActor) handle(msg *RedisRequest) {
	if err, _ := r.cmds.Dispatcher(r.getServiceContext(), msg); err != nil {
		l4g.Errorf("[RedisActor] msg(%+v) process error: %s", msg.PackHead, err)
	}
	dbMetrics.msgsCount.WithLabelValues("db").Inc()
}

// 加载全局信息
func (r *RedisActor) Load(ph *parse.PackHead, msg *r2l.L2R_Load) (retData *r2l.R2L_Load) {
	retData = &r2l.R2L_Load{
		Ret:     uint32(r2l.RET_OK),
		Sid:     msg.Sid,
		RankIds: msg.RankIds,
	}

	for _, fn := range r.loadFuncs {
		fn(msg)
	}
	for _, fn := range r.loadResults {
		fn(retData)
	}

	return
}

// 玩家登陆加载数据
func (r *RedisActor) Login(ph *parse.PackHead, msg *r2l.L2R_Login) (retData *r2l.R2L_Login) {
	retData = &r2l.R2L_Login{
		Ret: uint32(r2l.RET_OK),
	}

	//判断账号是否存在
	uuidKey := fmt.Sprintf("%s:%d", msg.Uuid, msg.ServerId)
	uid, exist := r.accounts[uuidKey]
	if !exist {
		//检查账号总数限制（登录时检查，不需要等到创角时候再检查）
		allAccounts := len(r.accounts)
		if allAccounts >= r.limitCfg.MaxAccounts || allAccounts >= helper.MaxNumOfAccounts() {
			l4g.Errorf("[FATAL] account size %d limit, %s", allAccounts, uuidKey)
			retData.Ret = uint32(ret.RET_ACCOUNT_SIZE_OUT_OF_LIMIT)
		} else {
			l4g.Infof("no found user account when login, %s", uuidKey)
			retData.Ret = uint32(ret.RET_USER_NOT_EXIST)
		}
		return
	}
	r.LoginByUID(uid, retData)
	return retData
}

//nolint:funlen
func (r *RedisActor) LoginByUID(uid string, retData *r2l.R2L_Login) {
	// base
	r.RopDb.GetUserMCallSKs(uid)
	//mail
	r.RopCl.GetAllMailMCallSKs(uid)
	//hero
	r.RopCl.GetAllHeroBodyMCallSKs(uid)
	//equip
	r.RopCl.GetAllEquipmentMCallSKs(uid)
	//formation
	r.RopCl.GetAllFormationMCallSKs(uid)
	//artifact
	r.RopCl.GetAllArtifactMCallSKs(uid)
	//gem
	r.RopCl.GetAllGemInfoMCallSKs(uid)
	//emblem
	r.RopCl.GetAllEmblemInfoMCallSKs(uid)
	//mirage
	r.RopCl.GetAllMirageMCallSKs(uid)
	// mazePlayer
	r.RopCl.GetAllMazePlayerMCallSKs(uid)
	iUID, _ := strconv.ParseUint(uid, 10, 64)
	//guilduser
	r.RopDb.GetSomeGuildUserMCall([]uint64{iUID})
	//充值订单
	r.RopDb.GetAllOrderMCallSKs(uid)
	//待处理的订单
	r.RopDb.GetWaitProcessOrderMCallSK(iUID)
	//配置活动
	r.RopCl.GetAllOperateActivityMCallSKs(uid)
	//已完成问卷
	r.RopDb.GetQuestionnaireMCallSK(iUID)
	//heroStarUpCosts
	r.RopCl.GetAllHeroStarUpCostsMCallSKs(uid)
	//永恒仪式
	r.RopCl.GetAllRiteMCallSKs(uid)
	//ban
	r.RopDb.GetSomeUserBanMCall([]uint64{iUID})
	//皮肤
	r.RopCl.GetAllSkinMCallSKs(uid)
	//赛季埋点日志
	// r.RopCl.GetAllSeasonUserLogMCallSKs(uid)
	//待退款订单
	r.RopDb.GetAllWaitRefundOrderMCallSKs(uid)
	//已退款订单
	r.RopDb.GetAllRefundedOrderMCallSKs(uid)
	//赛季羁绊激活数据
	r.RopDb.GetSeasonLinkActivationMCallSK(iUID)
	//赛季羁绊丰碑
	r.RopCl.GetAllSeasonLinkMonumentMCallSKs(uid)
	//遗物系统
	r.RopCl.GetAllRemainMCallSKs(uid)
	//活动合集
	r.RopCl.GetAllActivitySumMCallSKs(uid)
	//切磋
	r.RopCl.GetDuelMCallSKs(uid)
	//赛季装备
	r.RopCl.GetAllSeasonJewelryMCallSKs(uid)
	//称号
	r.RopCl.GetAllTitleMCallSKs(uid)
	// 宠物
	r.RopCl.GetAllPokemonMCallSKs(uid)
	/************************REPLY*******************************/
	// base
	userData, err := r.RopDb.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.User = userData
	//邮件
	mailsData, err := r.RopCl.GetSomeMailByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user mails error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Mails = mailsData
	//英雄
	heroesData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Heroes = heroesData
	//装备
	equipsData, err := r.RopCl.GetSomeEquipmentByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user equips error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Equips = equipsData
	//阵容
	formationsData, err := r.RopCl.GetSomeFormationByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user formation error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Formations = formationsData
	//神器
	artifactData, err := r.RopCl.GetSomeArtifactByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user artifact error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Artifacts = artifactData

	//宝石
	gemData, err := r.RopCl.GetSomeGemInfoByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user gem error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Gems = gemData

	//纹章
	emblemData, err := r.RopCl.GetSomeEmblemInfoByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user emblem error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Emblems = emblemData

	//个人boss
	miragesData, err := r.RopCl.GetSomeMirageByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user mirage error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Mirages = miragesData

	// mazePlayer
	mazePlayerData, err := r.RopCl.GetSomeMazePlayerByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get mazePlayer error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.MazePlayer = mazePlayerData

	//guildUser
	guildUserData, err := r.RopDb.GetSomeGuildUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Error("get guildUser error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.GuildUser = guildUserData[iUID]
	//充值订单
	orders, err := r.RopDb.GetSomeOrderByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get orders error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Orders = orders
	//待处理的订单
	waitProcessOrders, err := r.RopDb.GetWaitProcessOrderByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get wait process orders error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.WaitProcessOrders = waitProcessOrders

	activities, err := r.RopCl.GetSomeOperateActivityByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get operate activiy error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Activities = activities

	//已完成问卷
	questionnaires, err := r.RopDb.GetQuestionnaireByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get questionnaire error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Questionnaires = questionnaires

	//英雄升星消耗
	heroesStarUpCosts, err := r.RopCl.GetSomeHeroStarUpCostsByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user heroes star up costs error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.HeroesStarUpCosts = heroesStarUpCosts

	//永恒仪式
	rites, err := r.RopCl.GetSomeRiteByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user rites error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Rites = rites

	userBan, err := r.RopDb.GetSomeUserBanByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user ban error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.UserBan = userBan[iUID]

	//皮肤
	skinsData, err := r.RopCl.GetSomeSkinByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user skins error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Skins = skinsData

	// 赛季埋点日志
	/*seasonUserLogData, err := r.RopCl.GetSomeSeasonUserLogByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get season user log error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.SeasonUserLogs = seasonUserLogData*/

	//待退款的订单
	waitRefundOrders, err := r.RopDb.GetSomeWaitRefundOrderByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get wait refund orders error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.WaitRefundOrders = waitRefundOrders

	//已退款的订单
	refundedOrders, err := r.RopDb.GetSomeRefundedOrderByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get refunded orders error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.RefundedOrders = refundedOrders

	//赛季羁绊激活数据
	seasonlinkActivation, err := r.RopDb.GetSeasonLinkActivationByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get seasonlink activation error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.SeasonlinkActivation = seasonlinkActivation

	//赛季羁绊丰碑
	seasonlinkMonuments, err := r.RopCl.GetSomeSeasonLinkMonumentByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get seasonlink monuments error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.SeasonlinkMonuments = seasonlinkMonuments

	//遗物系统
	remains, err := r.RopCl.GetSomeRemainByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get remains error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Remains = remains

	activitySum, err := r.RopCl.GetSomeActivitySumByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get activity sum error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.ActivitySum = activitySum

	duel, err := r.RopCl.GetDuelByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get duel error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Duel = duel

	seasonJewelry, err := r.RopCl.GetSomeSeasonJewelryByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get season jewelry error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.SeasonJewelry = seasonJewelry

	//称号
	titlesData, err := r.RopCl.GetSomeTitleByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user titles error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Titles = titlesData

	//宠物
	pokemonData, err := r.RopCl.GetSomePokemonByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user pokemon error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}
	retData.Pokemons = pokemonData
}

func (r *RedisActor) createCheck(uuidKey string, msg *r2l.L2R_Create, smsg *r2l.R2L_Create) {
	//检查禁止注册开关
	if r.limitCfg.BanRegister {
		l4g.Errorf("server is not allowed to create user")
		smsg.Ret = uint32(ret.RET_SERVER_CREATE_USER_CLOSED)
		return
	}
	//检查账号总数限制
	allAccounts := len(r.accounts)
	if allAccounts >= helper.MaxNumOfAccounts() {
		l4g.Errorf("account size %d limit: %s", allAccounts, msg)
		smsg.Ret = uint32(ret.RET_ACCOUNT_SIZE_OUT_OF_LIMIT)
		return
	}

	//如果限制参数都为0， 那么就不做限制
	if r.limitCfg.MaxAccounts > 0 && allAccounts >= r.limitCfg.MaxAccounts {
		l4g.Errorf("account size limit: %s %d %d", msg, allAccounts, r.limitCfg.MaxAccounts)
		smsg.Ret = uint32(ret.RET_ACCOUNT_SIZE_OUT_OF_LIMIT)
		return
	}

	ipLimit := r.ipSet[msg.Ip]
	if r.limitCfg.IPAccounts > 0 && ipLimit >= r.limitCfg.IPAccounts {
		l4g.Errorf("[FATAL] ip size limit: %s %d %d", msg, ipLimit, r.limitCfg.IPAccounts)
		smsg.Ret = uint32(ret.RET_IP_SIZE_OUT_OF_LIMIT)
		return
	} else if r.limitCfg.IPAccountsHW > 0 && ipLimit >= r.limitCfg.IPAccountsHW {
		l4g.Errorf("account:(%s) with ip: %s is suspicious", msg, msg.Ip)
	}

	deviceIDLimit := r.deviceIDSet[msg.DeviceId]
	if r.limitCfg.DeviceIDAccounts > 0 && deviceIDLimit >= r.limitCfg.DeviceIDAccounts {
		l4g.Errorf("[FATAL] device id size limit: %s %d %d", msg, deviceIDLimit, r.limitCfg.DeviceIDAccounts)
		smsg.Ret = uint32(ret.RET_DEVICE_ID_SIZE_OUT_OF_LIMIT)
		return
	} else if r.limitCfg.DeviceIDAccountsHW > 0 && deviceIDLimit >= r.limitCfg.DeviceIDAccountsHW {
		l4g.Errorf("account: (%s) with device: %s is suspicious", msg, msg.DeviceId)
	}

	//检查账号是否已经创角
	if _, exist := r.accounts[uuidKey]; exist {
		l4g.Errorf("create user msg:%s account (%s) exist error", msg, uuidKey)
		smsg.Ret = uint32(r2l.RET_ERROR)
		return
	}

	//检查名字是否重复
	/*
		res, err = r.client.GetReply().Int64()
		if err != nil {
			l4g.Errorf("create user error: %s %s", msg, err)
			smsg.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		if res == 1 {
			l4g.Errorf("create user name (%s) exist error: %s", msg.Name, msg)
			smsg.Ret = uint32(ret.RET_USER_NAME_REPEAT)
		}
	*/
}

func (r *RedisActor) Create(ph *parse.PackHead, msg *r2l.L2R_Create) (retData *r2l.R2L_Create) {
	retData = &r2l.R2L_Create{
		Ret:      uint32(r2l.RET_OK),
		Ip:       msg.Ip,
		DeviceId: msg.DeviceId,
	}
	uuidKey := fmt.Sprintf("%s:%d", msg.Uuid, msg.ServerId)
	//创角检查
	r.createCheck(uuidKey, msg, retData)
	if retData.Ret != uint32(ret.RET_OK) {
		return
	}

	// 入库
	now := time.Now().Unix()
	base := &db.User{
		Uuid:     msg.Uuid,
		ServerId: msg.ServerId,
		Id:       msg.Id,
		Name:     msg.Name,
		Base: &db.BaseAttr{
			Level:      msg.Level,
			LevelTm:    now,
			Career:     msg.Career,
			BaseId:     msg.BaseId,
			CreateTime: now,
			Face:       msg.Face,
			DcId:       r.dcID,
			RegisterIp: msg.Ip,
			Channel:    msg.Channel,
			OpId:       msg.OpId,
			Pid:        msg.Pid,
			Gid:        msg.Gid,
		},
		Module: &db.ModuleAttr{},
	}

	// 设置account
	r.Client.Append("HSET", RedisHashAccount, uuidKey, msg.Id)
	// 设置name
	//r.client.Append("HSET", RedisHashName, msg.Name, msg.Id)
	// 设置user
	r.RopDb.SetUserMCallSK(msg.Id, base)
	//增加ip创角数量
	r.RopDb.Append("ZINCRBY", RedisZSetIP, 1, msg.Ip)
	//增加设备创角数量
	r.RopDb.Append("ZINCRBY", RedisZSetDeviceID, 1, msg.DeviceId)
	//自增dc id
	r.Client.Append("HSET", RedisHashDCID, helper.GetNodeIDByServerID(msg.ServerId), r.dcID)
	//增加索引
	r.Client.Append("HSET", RedisHashDCIDIndex, r.dcID, msg.Id)

	/************************REPLY*******************************/
	// 获取设置account结果
	if _, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("create user op db error: %s %s", msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
	}
	// 获取设置name结果
	/*
		if _, err := r.client.GetReply().Int(); err != nil {
			l4g.Errorf("create user op db error: %s %s", msg, err)
			retData.Ret = uint32(r2l.RET_ERROR)
		}
	*/
	// 获取设置user结果
	if _, err := r.Client.GetReply().Str(); err != nil {
		l4g.Errorf("create user op db error: %s %s", msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
	}

	//ip创角数
	if num, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("create user op db error: %s %s", msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
	} else {
		r.ipSet[msg.Ip] = num
		l4g.Debugf("ip: %s accounts size: %d", msg.Ip, num)
	}
	//设备创角数
	if num, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("create user op db error: %s %s", msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
	} else {
		r.deviceIDSet[msg.DeviceId] = num
		l4g.Debugf("device id: %s accounts size: %d", msg.DeviceId, num)
	}

	// 获取设置dc id自增结果
	if _, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("hset dc id %d error: %s %s", r.dcID, msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
	}
	// 获取设置dc id 索引结果
	if _, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("hset dcid %d index error: %s %s", r.dcID, msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
	}

	if retData.Ret != uint32(r2l.RET_OK) {
		return
	}
	// 创角成功
	retData.User = base
	r.accounts[uuidKey] = strconv.FormatUint(msg.Id, 10)
	l4g.Infof("create user success. account size: %d ip: %s device id: %s", len(r.accounts), msg.Ip, msg.DeviceId)
	return retData
}

//nolint:funlen
func (r *RedisActor) SaveUser(ph *parse.PackHead, msg *r2l.L2R_SaveUser) {
	uid := ph.UID
	l4g.Debugf("save user: %d %s", uid, msg)

	calls := 0
	r.Client.Append("SADD", RedisMapDailyActive+time.Now().Format("********"), uid)
	calls++
	// set user
	r.RopDb.SetUserMCallSK(uid, msg.User)
	calls++
	//set mail
	if len(msg.OpMails.Changes) > 0 {
		r.RopCl.SetSomeMailMCallSK(uid, msg.OpMails.Changes)
		calls++
	}
	if len(msg.OpMails.Deletes) > 0 {
		r.RopCl.RemSomeMailMCallSK(uid, msg.OpMails.Deletes)
		calls++
	}
	//set hero
	if len(msg.OpHeroes.Changes) > 0 {
		r.RopCl.SetSomeHeroBodyMCallSK(uid, msg.OpHeroes.Changes)
		calls++
	}
	if len(msg.OpHeroes.Deletes) > 0 {
		r.RopCl.RemSomeHeroBodyMCallSK(uid, msg.OpHeroes.Deletes)
		calls++
	}
	//set equip
	if len(msg.OpEquips.Changes) > 0 {
		r.RopCl.SetSomeEquipmentMCallSK(uid, msg.OpEquips.Changes)
		calls++
	}
	if len(msg.OpEquips.Deletes) > 0 {
		r.RopCl.RemSomeEquipmentMCallSK(uid, msg.OpEquips.Deletes)
		calls++
	}
	//set formation
	if len(msg.OpFormations.Changes) > 0 {
		r.RopCl.SetSomeFormationMCallSK(uid, msg.OpFormations.Changes)
		calls++
	}
	if len(msg.OpFormations.Deletes) > 0 {
		r.RopCl.RemSomeFormationMCallSK(uid, msg.OpFormations.Deletes)
		calls++
	}
	//set artifact
	if len(msg.OpArtifacts.Changes) > 0 {
		r.RopCl.SetSomeArtifactMCallSK(uid, msg.OpArtifacts.Changes)
		calls++
	}
	if len(msg.OpArtifacts.Deletes) > 0 {
		r.RopCl.RemSomeArtifactMCallSK(uid, msg.OpArtifacts.Deletes)
		calls++
	}

	// set gem
	if len(msg.OpGems.Changes) > 0 {
		r.RopCl.SetSomeGemInfoMCallSK(uid, msg.OpGems.Changes)
		calls++
	}
	if len(msg.OpGems.Deletes) > 0 {
		r.RopCl.RemSomeGemInfoMCallSK(uid, msg.OpGems.Deletes)
		calls++
	}

	// set emblem
	if len(msg.OpEmblems.Changes) > 0 {
		r.RopCl.SetSomeEmblemInfoMCallSK(uid, msg.OpEmblems.Changes)
		calls++
	}
	if len(msg.OpEmblems.Deletes) > 0 {
		r.RopCl.RemSomeEmblemInfoMCallSK(uid, msg.OpEmblems.Deletes)
		calls++
	}

	//set mirage
	if len(msg.OpMirages.Changes) > 0 {
		r.RopCl.SetSomeMirageMCallSK(uid, msg.OpMirages.Changes)
		calls++
	}

	// set mazePlayer
	if len(msg.OpMazePlayer.Changes) > 0 {
		r.RopCl.SetSomeMazePlayerMCallSK(uid, msg.OpMazePlayer.Changes)
		calls++
	}

	if len(msg.OpActivities.Changes) > 0 {
		r.RopCl.SetSomeOperateActivityMCallSK(uid, msg.OpActivities.Changes)
		calls++
	}
	if len(msg.OpActivities.Deletes) > 0 {
		r.RopCl.RemSomeOperateActivityMCallSK(uid, msg.OpActivities.Deletes)
		calls++
	}

	//set heroStarUpCosts
	if len(msg.OpHeroesStarUpCosts.Changes) > 0 {
		r.RopCl.SetSomeHeroStarUpCostsMCallSK(uid, msg.OpHeroesStarUpCosts.Changes)
		calls++
	}
	if len(msg.OpHeroesStarUpCosts.Deletes) > 0 {
		r.RopCl.RemSomeHeroStarUpCostsMCallSK(uid, msg.OpHeroesStarUpCosts.Deletes)
		calls++
	}

	//set rites
	if len(msg.OpRites.Changes) > 0 {
		r.RopCl.SetSomeRiteMCallSK(uid, msg.OpRites.Changes)
		calls++
	}
	if len(msg.OpRites.Deletes) > 0 {
		r.RopCl.RemSomeRiteMCallSK(uid, msg.OpRites.Deletes)
		calls++
	}

	//set skin
	if len(msg.OpSkins.Changes) > 0 {
		r.RopCl.SetSomeSkinMCallSK(uid, msg.OpSkins.Changes)
		calls++
	}
	if len(msg.OpSkins.Deletes) > 0 {
		r.RopCl.RemSomeSkinMCallSK(uid, msg.OpSkins.Deletes)
		calls++
	}

	//set rank data
	r.RopDb.SetSomeGlobalRankMCall([]*db.GlobalRank{msg.RankData})
	calls++

	//set SeasonLogs
	/*if len(msg.OpSeasonLogs.Changes) > 0 {
		r.RopCl.SetSomeSeasonUserLogMCallSK(uid, msg.OpSeasonLogs.Changes)
		calls++
	}
	if len(msg.OpSeasonLogs.Deletes) > 0 {
		r.RopCl.RemSomeSeasonUserLogMCallSK(uid, msg.OpSeasonLogs.Deletes)
		calls++
	}*/

	//set seasonLink Monuments
	if len(msg.OpSeasonLinkMonuments.Changes) > 0 {
		r.RopCl.SetSomeSeasonLinkMonumentMCallSK(uid, msg.OpSeasonLinkMonuments.Changes)
		calls++
	}
	if len(msg.OpSeasonLinkMonuments.Deletes) > 0 {
		r.RopCl.RemSomeSeasonLinkMonumentMCallSK(uid, msg.OpSeasonLinkMonuments.Deletes)
		calls++
	}

	//set remain
	if len(msg.OpRemains.Changes) > 0 {
		r.RopCl.SetSomeRemainMCallSK(uid, msg.OpRemains.Changes)
		calls++
	}
	if len(msg.OpRemains.Deletes) > 0 {
		r.RopCl.RemSomeRemainMCallSK(uid, msg.OpRemains.Deletes)
		calls++
	}

	if len(msg.OpActivitySum.Changes) > 0 {
		r.RopCl.SetSomeActivitySumMCallSK(uid, msg.OpActivitySum.Changes)
		calls++
	}
	if len(msg.OpActivitySum.Deletes) > 0 {
		r.RopCl.RemSomeActivitySumMCallSK(uid, msg.OpActivitySum.Deletes)
		calls++
	}

	// set duel
	if msg.OpDuel.Data != nil {
		r.RopCl.SetDuelMCallSK(uid, msg.OpDuel.Data)
		calls++
	}

	//set seasonJewelry
	if len(msg.OpSeasonJewelry.Changes) > 0 {
		r.RopCl.SetSomeSeasonJewelryMCallSK(uid, msg.OpSeasonJewelry.Changes)
		calls++
	}
	if len(msg.OpSeasonJewelry.Deletes) > 0 {
		r.RopCl.RemSomeSeasonJewelryMCallSK(uid, msg.OpSeasonJewelry.Deletes)
		calls++
	}

	//set title
	if len(msg.OpTitles.Changes) > 0 {
		r.RopCl.SetSomeTitleMCallSK(uid, msg.OpTitles.Changes)
		calls++
	}
	if len(msg.OpTitles.Deletes) > 0 {
		r.RopCl.RemSomeTitleMCallSK(uid, msg.OpTitles.Deletes)
		calls++
	}

	//set pokemon
	if len(msg.OpPokemons.Changes) > 0 {
		r.RopCl.SetSomePokemonMCallSK(uid, msg.OpPokemons.Changes)
		calls++
	}
	if len(msg.OpPokemons.Deletes) > 0 {
		r.RopCl.RemSomePokemonMCallSK(uid, msg.OpPokemons.Deletes)
		calls++
	}
	/************************REPLY*******************************/
	//mcall
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d save user error: %s %s", uid, msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GroupMail(ph *parse.PackHead, msg *r2l.L2R_GroupMail) {
	l4g.Debugf("GroupMail %s", msg)
	calls := 0
	for _, id := range msg.Users {
		r.RopCl.SetSomeMailMCallSK(id, []*cl.Mail{msg.Data})
		calls++
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d group mail error: %s %s", ph.UID, msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) OpMails(ph *parse.PackHead,
	msg *r2l.L2R_OpMails) {
	l4g.Debugf("user %d OpMails %s", ph.UID, msg)
	calls := 0
	if len(msg.Data.Changes) > 0 {
		r.RopCl.SetSomeMailMCallSK(ph.UID, msg.Data.Changes)
		calls++
	}
	if len(msg.Data.Deletes) > 0 {
		r.RopCl.RemSomeMailMCallSK(ph.UID, msg.Data.Deletes)
		calls++
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d op mails error: %s %s", ph.UID, msg, reply.Err)
			ctx.Stop()
		}
	}
}

// 根据名字获取玩家id，如果不存在则id为0
func (r *RedisActor) GetUserIDByName(names []string) []uint64 {
	userIDs := make([]uint64, 0, len(names))
	for _, name := range names {
		if val, exist := r.names[name]; exist {
			uid, _ := strconv.ParseUint(val, 10, 64)
			userIDs = append(userIDs, uid)
		}
	}
	return userIDs
}

func (r *RedisActor) GetUserIDByUUID(uuids []string) []uint64 {
	userIDs := make([]uint64, 0, len(uuids))
	for _, uuid := range uuids {
		if val, exist := r.accounts[uuid]; exist {
			uid, _ := strconv.ParseUint(val, 10, 64)
			userIDs = append(userIDs, uid)
		}
	}
	return userIDs
}

func (r *RedisActor) GetUUIDByUserID(ids []uint64) []string {
	uuIDs := make([]string, 0, len(ids))
	for _, id := range ids {
		data, err := r.RopDb.GetUserSK(id)
		if err != nil {
			continue
		}
		uuIDs = append(uuIDs, data.Uuid)
	}
	return uuIDs
}

func (r *RedisActor) GetUserSnapshot(ph *parse.PackHead,
	msg *r2l.L2R_GetUserSnapshot) (retData *r2l.R2L_GetUserSnapshot) {
	l4g.Debugf("user %d GetUserSnapshot %s", ph.UID, msg)
	retData = &r2l.R2L_GetUserSnapshot{
		Ret:         uint32(r2l.RET_OK),
		ClientMsgId: msg.ClientMsgId,
		FormationId: msg.FormationId,
	}
	var userIDs []uint64
	calls := 0
	if len(msg.Names) != 0 {
		userIDs = r.GetUserIDByName(msg.Names)
	} else {
		userIDs = msg.Users
	}
	for _, id := range userIDs {
		if id != 0 {
			r.RopDb.GetOfflineUserMCallSK(id)
			calls++
		}
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		userData, err := r.RopDb.GetOfflineUserByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get offline user %d error: %s", userIDs[i], err)
			retData.Ret = uint32(r2l.RET_ERROR)
		}
		if userData != nil && userData.Id != 0 {
			if userData.Base == nil {
				l4g.Errorf("get offline user %d error: %s", userIDs[i], err)
				retData.Ret = uint32(r2l.RET_ERROR)
			} else {
				retData.Users = append(retData.Users, userData)
			}
		}
	}
	return retData
}

//nolint:funlen
func (r *RedisActor) GetUserBattleData(ph *parse.PackHead,
	msg *r2l.L2R_GetUserBattleData) (retData *r2l.R2L_GetUserBattleData) {
	l4g.Debugf("user %d GetUserBattleData %s", ph.UID, msg)
	retData = &r2l.R2L_GetUserBattleData{
		Ret:         uint32(r2l.RET_OK),
		FormationId: msg.FormationId,
		ClientMsgId: msg.ClientMsgId,
		OnlyPower:   msg.OnlyPower,
	}
	calls := 0
	userIDs := msg.Users                          //原始数据，后面除了遍历就不要使用了。因为中间可能有空缺
	userIDStrs := make([]string, 0, len(userIDs)) //实际要查的总数量
	for _, id := range userIDs {
		if id != 0 {
			r.RopDb.GetBattleUserMCallSK(id)
			userIDStrs = append(userIDStrs, strconv.FormatUint(id, 10))
			if msg.FormationId != 0 {
				r.RopCl.GetSomeFormationMCallSK(id, []uint32{msg.FormationId})
			}
			r.RopCl.GetAllArtifactMCallSK(id)
			r.RopDb.GetSeasonLinkActivationMCallSK(id)
			r.RopCl.GetAllSeasonLinkMonumentMCallSK(id)
			r.RopCl.GetAllRemainMCallSK(id)
			r.RopCl.GetDuelMCallSK(id)
			r.RopCl.GetAllTitleMCallSK(id)
			r.RopCl.GetAllPokemonMCallSK(id)
			calls++
		}
	}

	battleData := make([]*db.UserBattleData, len(userIDStrs))
	heroes := make([][]uint64, len(userIDStrs))            //要查找得英雄id
	equips := make([][]uint64, len(userIDStrs))            //要查找的装备id
	emblems := make([][]uint64, len(userIDStrs))           //要查找的纹章id
	rites := make([][]uint32, len(userIDStrs))             //要查找的仪式id
	seasonJewelryList := make([][]uint64, len(userIDStrs)) //要查找的赛季装备id

	/************************REPLY*******************************/
	for order := 0; order < calls; order++ {
		//user
		userData, err := r.RopDb.GetBattleUserByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get offline user %s error: %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		if userData != nil && userData.Id != 0 {
			if userData.Base == nil {
				l4g.Errorf("get offline user %s error userBaseData is nil", userIDStrs[order])
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			battleData[order] = &db.UserBattleData{}
			battleData[order].User = userData
			battleData[order].FormationId = msg.FormationId
		}
		if msg.FormationId == 0 {
			if battleData[order] != nil {
				for _, heroID := range battleData[order].User.Base.Top5Heros {
					if heroID == 0 {
						break
					}
					heroes[order] = append(heroes[order], heroID)
				}
			}
		} else {
			//formation
			formationsData, err := r.RopCl.GetSomeFormationByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("get formation %s error: %s", userIDStrs[order], err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			//这里要确保user存在，否则可能panic
			if battleData[order] != nil && formationsData != nil {
				battleData[order].Formations = formationsData
				for _, v := range formationsData {
					for _, team := range v.Teams {
						for _, info := range team.Info {
							heroes[order] = append(heroes[order], info.Hid)
						}
						if team.RiteInfo != nil && team.RiteInfo.RiteId > 0 {
							rites[order] = append(rites[order], team.RiteInfo.RiteId)
						}
					}
				}
			}
		}

		//artifact
		artifactData, err := r.RopCl.GetSomeArtifactByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user artifact error: %s %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}

		if battleData[order] != nil && artifactData != nil {
			battleData[order].Artifacts = artifactData
		}

		//seasonlink activation
		activation, err := r.RopDb.GetSeasonLinkActivationByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user seasonlink activation error: %s %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}

		if battleData[order] != nil && activation != nil {
			battleData[order].SeasonLinkActivation = activation
		}

		// seasonlink monuments
		monuments, err := r.RopCl.GetSomeSeasonLinkMonumentByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user seasonlink monuments error: %s %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		if battleData[order] != nil && monuments != nil {
			battleData[order].Monuments = monuments
		}

		// remain
		remainData, err := r.RopCl.GetSomeRemainByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user remain error: %s %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		if battleData[order] != nil && remainData != nil {
			battleData[order].Remains = remainData
		}

		//duel
		duel, err := r.RopCl.GetDuelByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user duel error: %s %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		if battleData[order] != nil && duel != nil {
			battleData[order].Duel = duel
		}

		//titles
		titles, err := r.RopCl.GetSomeTitleByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user titles error: %s %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		if battleData[order] != nil && titles != nil {
			battleData[order].Titles = titles
		}

		//pokemon
		pokemonData, err := r.RopCl.GetSomePokemonByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user pokemon error: %s %s", userIDStrs[order], err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}

		if battleData[order] != nil && pokemonData != nil {
			battleData[order].Pokemons = pokemonData
		}
	}

	//开始查找阵容
	for k, id := range userIDStrs {
		if heroes[k] != nil {
			r.RopCl.GetSomeHeroBodyMCallSKs(id, heroes[k])
		}
	}

	/************************REPLY*******************************/
	skins := make([][]uint32, len(userIDStrs)) //要查找的皮肤id
	for k, id := range userIDStrs {
		if heroes[k] != nil {
			heroData, err := r.RopCl.GetSomeHeroBodyByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("get offline user %s formation heroes error: %s", id, err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			battleData[k].Heroes = heroData
			for _, v := range heroData {
				for _, eid := range v.Equipment { // 装备
					equips[k] = append(equips[k], eid)
				}

				for _, emblemID := range v.Emblem { // 纹章
					emblems[k] = append(emblems[k], emblemID)
				}

				skinList := goxml.GetData().SkinInfoM.GetSkins(v.SysId) // 皮肤
				if len(skinList) > 0 {
					skins[k] = append(skins[k], skinList...)
				}

				for _, jid := range v.SeasonJewelry { // 赛季装备
					seasonJewelryList[k] = append(seasonJewelryList[k], jid)
				}
			}
		}
	}

	//开始查找仪式
	for k, id := range userIDStrs {
		if rites[k] != nil {
			r.RopCl.GetSomeRiteMCallSKs(id, rites[k])
		}
	}

	/************************REPLY*******************************/
	for k, id := range userIDStrs {
		if rites[k] != nil {
			riteMap, err := r.RopCl.GetSomeRiteByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("get offline user %s formation rites error: %s", id, err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			battleData[k].Rites = riteMap
		}
	}

	for k, id := range userIDStrs {
		if equips[k] != nil {
			r.RopCl.GetSomeEquipmentMCallSKs(id, equips[k])
		}
		if emblems[k] != nil {
			r.RopCl.GetSomeEmblemInfoMCallSKs(id, emblems[k])
		}
		if skins[k] != nil {
			r.RopCl.GetSomeSkinMCallSKs(id, skins[k])
		}
		if seasonJewelryList[k] != nil {
			r.RopCl.GetSomeSeasonJewelryMCallSKs(id, seasonJewelryList[k])
		}
	}

	/************************REPLY*******************************/
	for k := range userIDStrs {
		if equips[k] != nil {
			equipData, err := r.RopCl.GetSomeEquipmentByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("get offline user %s equips error: %s", userIDStrs[k], err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			battleData[k].Equips = equipData
		}
		//纹章
		if emblems[k] != nil {
			emblemData, err := r.RopCl.GetSomeEmblemInfoByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("get offline user %s emblems error: %s", userIDStrs[k], err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			battleData[k].Emblems = emblemData
		}
		//皮肤
		if skins[k] != nil {
			skinData, err := r.RopCl.GetSomeSkinByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("get offline user %s skins error: %s", userIDStrs[k], err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			battleData[k].Skins = skinData
		}
		// 赛季装备
		if seasonJewelryList[k] != nil {
			jewelryData, err := r.RopCl.GetSomeSeasonJewelryByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("get offline user %s jewelryData error: %s", userIDStrs[k], err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			battleData[k].SeasonJewelry = jewelryData
		}
	}

	retData.Users = battleData

	return retData
}

func (r *RedisActor) SaveSmallRank(datas []*db.SmallRank) {
	if len(datas) != 0 {
		if err := r.RopDb.SetSomeSmallRank(datas); err != nil {
			l4g.Errorf("save small rank error:%s", err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveFriend(datas []*db.Friends) {
	if len(datas) != 0 {
		if err := r.RopDb.SetSomeFriends(datas); err != nil {
			l4g.Errorf("save small rank error:%s", err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveCommonRank(msg *r2l.L2R_SaveCommonRank) {
	l4g.Debugf("SaveCommonRank %s", msg)

	calls := 0
	if len(msg.GetDatas()) > 0 {
		for _, v := range msg.GetDatas() {
			if len(v.GetValues()) > 0 {
				r.RopDb.SetSomeCommonRankMCallSK(uint64(v.GetId()), v.GetValues())
				calls++
			}
			if len(v.GetDeletes()) > 0 {
				r.RopDb.RemSomeCommonRankMCallSK(uint64(v.GetId()), v.GetDeletes())
				calls++
			}
		}
	}

	if len(msg.Deletes) > 0 {
		for _, v := range msg.Deletes {
			r.RopDb.DelAllCommonRankMCallSK(uint64(v))
			calls++
		}
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save common rank error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) ChangeConfig(msg *r2l.L2R_ChangeConfig) {
	r.limitCfg.MaxAccounts = int(msg.AccountSize)
	r.limitCfg.IPAccounts = int(msg.IpAccounts)
	r.limitCfg.DeviceIDAccounts = int(msg.DeviceIdAccounts)
	r.limitCfg.IPAccountsHW = int(msg.IpAccountsHw)
	r.limitCfg.DeviceIDAccountsHW = int(msg.DeviceIdAccountsHw)
	r.limitCfg.BanRegister = msg.BanRegister
	r.limitCfg.Status = int(msg.Status)
}

func (r *RedisActor) SaveArena(msg *r2l.L2R_SaveArena) {
	l4g.Debugf("SaveArena: %s", msg)
	calls := 0
	if len(msg.Changes) > 0 {
		r.RopCl.SetSomeArenaMCall(msg.Changes)
		calls++
	}
	if len(msg.Deletes) > 0 {
		r.RopCl.RemSomeArenaMCall(msg.Deletes)
		calls++
	}
	if len(msg.ChangeLogs) > 0 {
		for uid, datas := range msg.ChangeLogs {
			r.RopCl.SetSomeArenaLogMCallSK(uid, datas.Logs)
			calls++
		}
	}
	if len(msg.DeleteLogs) > 0 {
		for uid, datas := range msg.DeleteLogs {
			r.RopCl.RemSomeArenaLogMCallSK(uid, datas.Ids)
			calls++
		}
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save arena error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveArenaSeason(season *db.ArenaSeason) {
	if season != nil {
		if err := r.RopDb.SetArenaSeason(season); err != nil {
			l4g.Errorf("save common arena season error:%s", err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GetArenaLog(ph *parse.PackHead,
	msg *r2l.L2R_GetArenaLog) (retData *r2l.R2L_GetArenaLog) {
	l4g.Debugf("user %d GetArenaLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetArenaLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}
	logs, err := r.RopCl.GetAllArenaLogSK(msg.Id)
	if err != nil {
		l4g.Errorf("L2R_GetArenaLog. get arena log error. uid:%d err:%s",
			msg.Id, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs
	return
}

func (r *RedisActor) SetName(ph *parse.PackHead, msg *r2l.L2R_SetName) (retData *r2l.R2L_SetName) {
	retData = &r2l.R2L_SetName{
		Ret:     uint32(r2l.RET_OK),
		Id:      msg.Id,
		OldName: msg.OldName,
		NewName: msg.NewName,
	}
	if msg.NewName == "" {
		l4g.Errorf("Setname error msg:%s", msg)
		retData.Ret = uint32(ret.RET_ERROR)
		return
	}
	uidStr := strconv.FormatUint(msg.Id, 10)
	//检查名字是否重复
	if _, exist := r.names[msg.NewName]; exist {
		l4g.Errorf("Setname error msg:%s", msg)
		retData.Ret = uint32(ret.RET_USER_NAME_REPEAT)
		return
	}
	if msg.OldName != "" {
		if id, exist := r.names[msg.OldName]; exist {
			if id != uidStr {
				l4g.Errorf("Setname error msg:%s, old id:%s", msg, id)
				retData.Ret = uint32(ret.RET_ERROR)
				return
			}
		} else {
			l4g.Errorf("Setname error msg:%s, old name not exist", msg)
			retData.Ret = uint32(ret.RET_ERROR)
			return
		}
	}

	// 设置name
	r.Client.Append("HSet", RedisHashName, msg.NewName, uidStr)
	if msg.OldName != "" {
		r.Client.Append("HDel", RedisHashName, msg.OldName)
	}
	sufKey := redisop.RedisOpKeyHashDbUser + uidStr
	r.Client.Append("HSet", sufKey, "name", msg.NewName)
	// 获取设置name结果
	if _, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("SetName error, set newname reply msg:%s err:%s", msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		return
	}
	if msg.OldName != "" {
		if _, err := r.Client.GetReply().Int(); err != nil {
			l4g.Errorf("SetName error: del old name reply msg:%s err:%s", msg, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			return
		}
	}
	if _, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("SetName error: set user replay msg:%s err:%s", msg, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		return
	}
	delete(r.names, msg.OldName)
	r.names[msg.NewName] = uidStr
	return
}

func (r *RedisActor) ping() {
	if ret, err := r.Client.Cmd("ping").Str(); err != nil || ret != "PONG" {
		l4g.Errorf("ping redis error: %s %v", err, ret)
		ctx.Stop()
	}
}

type TenSecondTimer struct {
	act *RedisActor
}

func (t *TenSecondTimer) TimeOut(int64) {
	t.act.ping()
}

func (r *RedisActor) SaveUserBan(msg *r2l.L2R_SaveUserBan) {
	l4g.Debugf("save gm bans %s", msg)
	calls := 0
	if len(msg.Changes) > 0 {
		r.RopDb.SetSomeUserBanMCall(msg.Changes)
		calls++
	}
	if len(msg.Deletes) > 0 {
		r.RopDb.RemSomeUserBanMCall(msg.Deletes)
		calls++
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save userban error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) ChangeBags(msg *r2l.L2R_GRPC_ChangeBags) error {
	l4g.Debugf("gm change bags: %v", msg)
	data, err := r.RopDb.GetUserSK(msg.UserId)
	if err != nil {
		return err
	}
	data.BagOp = db.Op_Dirty
	changeItems := func(items, target map[uint32]uint32) (map[uint32]uint32, error) {
		for k, v := range target {
			curNum := items[k]
			if curNum < v {
				return nil, fmt.Errorf("gm ChangeBags error, item:%d number:%d < target:%d", k, curNum, v)
			} else if curNum == v {
				delete(items, k)
			} else {
				items[k] = curNum - v
			}
		}
		return items, nil
	}
	if data.Bag.Items, err = changeItems(data.Bag.Items, msg.Bag.Items); err != nil {
		return err
	}
	if data.Bag.ArtifactFragments, err = changeItems(data.Bag.ArtifactFragments, msg.Bag.ArtifactFragments); err != nil {
		return err
	}
	if data.Bag.Fragments, err = changeItems(data.Bag.Fragments, msg.Bag.Fragments); err != nil {
		return err
	}
	if data.Bag.PokemonFragments, err = changeItems(data.Bag.PokemonFragments, msg.Bag.PokemonFragments); err != nil {
		return err
	}
	if len(msg.Bag.Tokens) != 0 {
		for k, v := range msg.Bag.Tokens {
			curNum := data.Bag.Tokens[k]
			if curNum == v {
				delete(data.Bag.Tokens, k)
			} else if curNum > v {
				data.Bag.Tokens[k] = curNum - v
			} else {
				return fmt.Errorf("gm ChangeBags error, item:%d number:%d < target:%d", k, curNum, v)
			}
		}
	}
	return r.RopDb.SetUserSK(msg.UserId, data)
}

func (r *RedisActor) SaveRankAchieve(msg *r2l.L2R_SaveRankAchieve) {
	if len(msg.Changes) > 0 {
		if err := r.RopDb.SetSomeRankAchieves(msg.Changes); err != nil {
			l4g.Errorf("save rank achievement changes error:%s", err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveMazeMap(msg *r2l.L2R_SaveMazeMap) {
	if len(msg.Changes) > 0 {
		if err := r.RopCl.SetSomeMazeMap(msg.Changes); err != nil {
			l4g.Errorf("save maze map changes error:%s", err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveMiragePass(msg *r2l.L2R_SaveMiragePass) {
	if len(msg.Changes) > 0 {
		if err := r.RopDb.SetSomeMiragePass(msg.Changes); err != nil {
			l4g.Errorf("save mirage pass num changes error:%s", err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveUserBattleSnapshot(ph *parse.PackHead, msg *r2l.L2R_SaveUserBattleSnapshot) (
	retData *r2l.R2L_SaveUserBattleSnapshot) {
	uid := ph.UID
	l4g.Debugf("user %d SaveUserBattleSnapshot %s", ph.UID, msg)
	retData = &r2l.R2L_SaveUserBattleSnapshot{
		Ret:         uint32(r2l.RET_OK),
		FormationId: msg.FormationId,
		ClientMsgId: msg.ClientMsgId,
	}

	r.RopDb.SetSomeUserBattleSnapshotMCallSK(uid, msg.Users)

	/************************REPLY*******************************/
	//mcall
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %d save userBattleSnapshot error: %s %s", uid, msg, reply.Err)
		ctx.Stop()
	}
	for i := 0; i < len(msg.Users); i++ {
		retData.NewIds = append(retData.NewIds, msg.Users[i].Id)
	}
	return retData
}

func (r *RedisActor) GetUserBattleSnapshot(ph *parse.PackHead, msg *r2l.L2R_GetUserBattleSnapshot) (
	retData *r2l.R2L_GetUserBattleSnapshot) {
	l4g.Debugf("user %d GetUserBattleSnapshot %s", ph.UID, msg)
	retData = &r2l.R2L_GetUserBattleSnapshot{
		Ret:         uint32(r2l.RET_OK),
		ClientMsgId: msg.ClientMsgId,
	}
	if len(msg.Ids) > 0 {
		r.RopDb.GetSomeUserBattleSnapshotMCallSKs(strconv.FormatUint(ph.UID, 10), msg.Ids)
	} else {
		l4g.Errorf("get userBattleSnapshot error ids count id zero user:%d", ph.UID)
		retData.Ret = uint32(r2l.RET_ERROR)
		return retData
	}

	/************************REPLY*******************************/
	datas, err := r.RopDb.GetSomeUserBattleSnapshotByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get userBattleSnapshot error user %d error: %s", ph.UID, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		return retData
	}
	for id, data := range datas {
		if data != nil && data.Id != 0 {
			if data.Info == nil {
				l4g.Errorf("get  userBattleSnapshot user %d Id: %d error: %s", ph.UID, id, err)
				retData.Ret = uint32(r2l.RET_ERROR)
				return retData
			}
			retData.Infos = append(retData.Infos, datas[id])
		}
	}
	return retData
}

func (r *RedisActor) DeleteUserBattleSnapshot(ph *parse.PackHead, msg *r2l.L2R_DeleteUserBattleSnapshot) {
	err := r.RopDb.RemSomeUserBattleSnapshotSKs(strconv.FormatUint(ph.UID, 10), msg.Ids)
	if err != nil {
		l4g.Errorf("del userBattleSnapshot error user %d ids %+v error: %s", ph.UID, msg.Ids, err)
		ctx.Stop()
	}
}

func (r *RedisActor) SaveGuild(msg *r2l.L2R_SaveGuild) {
	l4g.Debug("save guild %s", msg)
	calls := 0
	if len(msg.Changes) > 0 {
		r.RopDb.SetSomeLogicGuildMCall(msg.Changes)
		calls++
	}

	if len(msg.Deletes) > 0 {
		r.RopDb.RemSomeLogicGuildMCall(msg.Deletes)
		calls++
		//r.ropDb.RemSomeGuildDungeonMCall(msg.Deletes)
		//calls++
		//for _, gid := range msg.Deletes {
		//	r.ropCl.DelAllGuildLogInfoMCallSK(gid)
		//	calls++
		//	r.ropCl.DelAllGuildDungeonLogMCallSK(gid)
		//	calls++
		//	r.ropCl.DelAllGuildDungeonMessageBoardLogMCallSK(gid)
		//	calls++
		//	r.ropCl.DelAllGuildDonateLogInfoMCallSK(gid)
		//	calls++
		//}
	}

	//if len(msg.GuildChangeLogs) > 0 {
	//	for gid, datas := range msg.GuildChangeLogs {
	//		r.ropCl.SetSomeGuildLogInfoMCallSK(gid, datas.Logs)
	//		calls++
	//	}
	//}
	//if len(msg.GuildDeleteLogs) > 0 {
	//	for gid, datas := range msg.GuildDeleteLogs {
	//		r.ropCl.RemSomeGuildLogInfoMCallSK(gid, datas.Ids)
	//		calls++
	//	}
	//}

	if len(msg.UserChanges) > 0 {
		r.RopDb.SetSomeGuildUserMCall(msg.UserChanges)
		calls++
	}

	if msg.DungeonRefresh != nil {
		r.RopDb.SetLogicGuildDungeonRefreshMCall(msg.DungeonRefresh)
		calls++
	}

	//if len(msg.GuildDonateLogs) > 0 {
	//	for gid, datas := range msg.GuildDonateLogs {
	//		r.ropCl.SetSomeGuildDonateLogInfoMCallSK(gid, datas.Logs)
	//		calls++
	//	}
	//}
	//if len(msg.GuildDonateDeleteLogs) > 0 {
	//	for gid, datas := range msg.GuildDonateDeleteLogs {
	//		r.ropCl.RemSomeGuildLogInfoMCallSK(gid, datas.Ids)
	//		calls++
	//	}
	//}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Error("save guild error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) LoadGuild(ph *parse.PackHead, msg *r2l.L2R_LoadGuild) *r2l.R2L_LoadGuild {
	l4g.Debugf("user %d LoadGuild %s", ph.UID, msg)
	retData := &r2l.R2L_LoadGuild{
		Ret:   uint32(r2l.RET_OK),
		Guild: msg.Id,
	}

	if len(msg.Uids) > 0 {
		r.RopDb.GetSomeGuildUserMCall(msg.Uids)
	}

	if len(msg.Uids) > 0 {
		users, err := r.RopDb.GetSomeGuildUserByReply(r.Client.GetReply())
		if err != nil {
			l4g.Error("get guild user error: %v %s", msg, err)
			retData.Ret = uint32(r2l.RET_ERROR)
			ctx.Stop()
		}
		for _, u := range users {
			retData.Users = append(retData.Users, u)
		}
	}
	return retData
}

func (r *RedisActor) GetGuildDungeonLog(ph *parse.PackHead,
	msg *r2l.L2R_GetGuildDungeonLog) (retData *r2l.R2L_GetGuildDungeonLog) {
	l4g.Debugf("user %d GetGuildDungeonLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetGuildDungeonLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}

	//logs, err := r.RopCl.GetAllGuildDungeonLogSK(msg.Id)
	//if err != nil {
	//	l4g.Errorf("L2R_GetGuildDungeonLog. get guildDungeon log error. uid:%d err:%s",
	//		msg.Id, err)
	//	retData.Ret = uint32(r2l.RET_ERROR)
	//	ctx.Stop()
	//}

	//retData.Logs = logs
	return
}

func (r *RedisActor) GuildDungeonMessageBoardLog(ph *parse.PackHead,
	msg *r2l.L2R_GetGuildDungeonMessageBoardLog) (retData *r2l.R2L_GetGuildDungeonMessageBoardLog) {
	l4g.Debugf("user %d GetGuildDungeonMessageBoardLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetGuildDungeonMessageBoardLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}

	logs, err := r.RopCl.GetAllGuildDungeonMessageBoardLogSK(msg.Id)
	if err != nil {
		l4g.Errorf("L2R_GetGuildDungeonMessageBoardLog. get guildDungeon message board log error. uid:%d err:%s",
			msg.Id, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs
	return
}

func (r *RedisActor) GetGuildLog(ph *parse.PackHead,
	msg *r2l.L2R_GetGuildLog) (retData *r2l.R2L_GetGuildLog) {
	l4g.Debugf("user %d GetGuildLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetGuildLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}

	//logs, err := r.ropCl.GetAllGuildLogInfoSK(msg.Id)
	//if err != nil {
	//	l4g.Errorf("L2R_GetGuildLog. get guild log error. uid:%d err:%s",
	//		msg.Id, err)
	//	retData.Ret = uint32(r2l.RET_ERROR)
	//	ctx.Stop()
	//}
	//
	//retData.Logs = logs
	return
}

// 添加订单
func (r *RedisActor) AddOrder(data *db.Order) uint32 {
	//添加的订单，必然是首次出现的订单
	if order, err := r.RopDb.GetOrderSK(data.UserId, data.OrderId); err != nil {
		l4g.Errorf("user %d get order error:%s %+v", data.UserId, err, data)
		ctx.Stop()
		return uint32(ret.RET_ERROR)
	} else if order != nil {
		l4g.Errorf("user %d repeat order:%+v", data.UserId, data)
		return uint32(ret.RET_ADD_ORDER_REPEAT)
	}

	calls := 0
	temp := data.Clone() //在未入库成功前，不修改原订单状态
	temp.Status = uint32(common.ORDER_STATUS_OS_SAVE)
	//添加新订单
	r.RopDb.SetSomeOrderMCallSK(data.UserId, []*db.Order{temp})
	calls++
	//添加到待处理的订单集合
	r.RopDb.AddSomeWaitProcessOrderMCallSK(data.UserId, []string{data.OrderId})
	calls++
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d add order error: %s order:%v", data.UserId, reply.Err, temp)
			ctx.Stop()
		}
	}
	//修改订单状态设置为已入库，order对象会返回到logic处理，进程内通信
	data.Status = uint32(common.ORDER_STATUS_OS_SAVE) //已入库
	return uint32(ret.RET_OK)
}

// 更新订单
func (r *RedisActor) SaveOrder(userID uint64, data []*db.Order) {
	//筛选出来未处理的和已处理结束的订单
	//订单流程 add order -> process order -> save order
	//理论上，save order的都是处理完成的订单，
	//保险起见，这里也检查下未处理的订单
	calls := 0
	for _, order := range data {
		if order.Status == uint32(common.ORDER_STATUS_OS_SAVE) {
			//理论上未处理的订单，走add order流程
			l4g.Errorf("user %d save unprocessed order :%+v", userID, order)
			r.RopDb.AddSomeWaitProcessOrderMCallSK(order.UserId, []string{order.OrderId})
			calls++
		} else {
			r.RopDb.RemSomeWaitProcessOrderMCallSK(order.UserId, []string{order.OrderId})
			calls++
		}
	}
	r.RopDb.SetSomeOrderMCallSK(userID, data)
	calls++

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d save order error: %s order:%v", userID, reply.Err, data)
			ctx.Stop()
		}
	}
}

// 保存订单退款状态
func (r *RedisActor) SaveRefundOrder(userId uint64, delWaitRefundOrders []*db.WaitRefundOrder, addRefundedOrders []*db.RefundedOrder) {
	calls := 0

	//Del waitRefundOrders
	for _, order := range delWaitRefundOrders {
		r.RopDb.RemSomeWaitRefundOrderMCallSK(userId, []string{order.OrderId})
		calls++
	}

	//Add refundedOrders
	if len(addRefundedOrders) > 0 {
		r.RopDb.SetSomeRefundedOrderMCallSK(userId, addRefundedOrders)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d SaveRefundOrder error: %s delWaitRefundOrders:%+v refundedOrders %+v", userId, reply.Err, delWaitRefundOrders, addRefundedOrders)
			ctx.Stop()
		}
	}
}

// 添加待退款订单
func (r *RedisActor) AddWaitRefundOrder(data *db.Order) uint32 {
	//检查待退款订单是否重复
	if order, err := r.RopDb.GetWaitRefundOrderSK(data.UserId, data.OrderId); err != nil {
		l4g.Errorf("user %d get wait refund order error:%s %+v", data.UserId, err, data)
		ctx.Stop()
		return uint32(ret.RET_ERROR)
	} else if order != nil {
		l4g.Errorf("user %d repeat wait refund order:%+v", data.UserId, data)
		return uint32(ret.RET_ADD_ORDER_REPEAT)
	}

	waitRefundOrder := &db.WaitRefundOrder{
		OrderId:   data.GetOrderId(),
		OrderData: data.Clone(),
	}

	r.RopDb.SetSomeWaitRefundOrderMCallSK(data.UserId, []*db.WaitRefundOrder{waitRefundOrder})
	if reply := r.Client.GetReply(); reply.Err != nil {
		l4g.Errorf("user %d add wait refund order error: %s order:%v", data.UserId, reply.Err, data)
		ctx.Stop()
	}

	return uint32(ret.RET_OK)
}

func (r *RedisActor) SaveMultiLang(msg *r2l.L2R_SaveMultiLang) {
	calls := 0
	if len(msg.MultiChanges) > 0 {
		r.RopDb.SetSomeMultiLangMCall(msg.MultiChanges)
		calls++
	}
	if len(msg.MultiDeletes) > 0 {
		r.RopDb.RemSomeMultiLangMCall(msg.MultiDeletes)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save lang error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) DelInvalidQuestionnaireID(uid uint64, msg *r2l.L2R_DelInvalidQuestionnaireID) {
	calls := 0
	if len(msg.InvalidIds) > 0 {
		r.RopDb.RemSomeQuestionnaireMCallSK(uid, msg.InvalidIds)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save lang error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SetQuestionnaireFinish(uid uint64, msg *r2l.L2R_GRPC_SetQuestionnaireFinish) uint32 {
	questionnaireIDs, err := r.RopDb.GetQuestionnaireSK(uid)
	if err != nil {
		l4g.Errorf("user %d get questionnaireId error: %s id:%v",
			uid, err, msg.QuestionnaireID)
		return uint32(ret.RET_ERROR)
	}

	if util.InUint32s(questionnaireIDs, msg.QuestionnaireID) {
		l4g.Errorf("user %d had questionnaireId :%v", uid, msg.QuestionnaireID)
		return uint32(ret.RET_ERROR)
	}

	calls := 0
	//添加到已完成订单集合
	r.RopDb.AddSomeQuestionnaireMCallSK(uid, []uint32{msg.QuestionnaireID})
	calls++
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d add questionnaireId error: %s id:%v",
				uid, reply.Err, msg.QuestionnaireID)
			ctx.Stop()
		}
	}

	return uint32(ret.RET_OK)
}

func (r *RedisActor) OpBanCmds(msg *r2l.L2R_OpBanCmds) {
	calls := 0
	if len(msg.AddCmds) > 0 {
		r.RopDb.AddSomeBanCmdMCall(msg.AddCmds)
		calls++
	}
	if len(msg.DelCmds) > 0 {
		r.RopDb.RemSomeBanCmdMCall(msg.DelCmds)
		calls++
	}
	/************************REPLY*******************************/
	//mcall
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("op module closed error: %v", reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GetUserBaseByUID(uid string, retData *r2l.R2L_UserBase) error {
	// base
	r.RopDb.GetUserMCallSKs(uid)

	/************************REPLY*******************************/
	// base
	userData, err := r.RopDb.GetUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get user error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		return err
	}
	retData.User = userData
	return nil
}

func (r *RedisActor) GetRechargeList(uid string, starTm, endTm int64, retData *r2l.R2L_GetRechargeList) error {
	r.RopDb.GetAllOrderMCallSKs(uid)

	/************************REPLY*******************************/

	orderData, err := r.RopDb.GetSomeOrderByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get orders error: %s %s", uid, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		return err
	}
	for _, order := range orderData {
		if order.PayTime >= starTm && order.PayTime <= endTm {
			retData.Orders = append(retData.Orders, order)
		}
	}

	return nil
}

func (r *RedisActor) SaveArenaLastSeasonTop(msg *r2l.L2R_SaveArenaLastSeasonTop) *r2l.R2L_SaveArenaLastSeasonTop {
	if msg == nil || len(msg.Data) == 0 {
		l4g.Errorf("SaveArenaLastSeasonTop. no data")
		return nil
	}

	ret := &r2l.R2L_SaveArenaLastSeasonTop{
		Ret: uint32(ret.RET_OK),
	}

	data := msg.Data[0]
	if len(data.Users) > 0 {
		for k, v := range data.Users {
			uid := strconv.FormatUint(v.User.Id, 10)
			r.RopDb.GetUserMCallSKs(uid)
			userData, err := r.RopDb.GetUserByReply(r.Client.GetReply())
			if err != nil {
				l4g.Errorf("SaveArenaLastSeasonTop. get user error: %s %s", uid, err)
				return nil
			}

			data.Users[k].User.Name = userData.Name
			data.Users[k].User.Level = userData.Base.Level
			data.Users[k].User.BaseId = userData.Base.BaseId
			data.Users[k].User.Power = userData.Base.Power
			data.Users[k].User.CrystalPower = userData.Base.CrystalPower

			l4g.Debugf("SaveArenaLastSeasonTop. data.Users[%d]:%+v", k, data.Users[k])
		}
	}

	l4g.Debugf("SaveArenaLastSeasonTop. data:%+v", data)
	if err := r.RopCl.SetSomeArenaLastSeasonTop(msg.Data); err != nil {
		l4g.Errorf("SaveArenaLastSeasonTop. save error:%s", err)
		ctx.Stop()
		return nil
	}

	ret.Data = msg.Data[0].Clone()
	return ret
}

func (r *RedisActor) CloseGuidance(msg *r2l.L2R_GRPC_CloseGuidance) error {
	data, err := r.RopDb.GetUserSK(msg.ID)
	if err != nil {
		return err
	}

	if data.Module1 == nil {
		data.Module1 = &db.ModuleAttr1{}
	}
	if data.Module1.Guidance == nil {
		data.Module1.Guidance = &cl.Guidance{}
	}
	exist := false
	for _, group := range data.Module1.Guidance.Finished {
		if group == msg.Value {
			exist = true
			break
		}
	}
	if !exist {
		data.Module1.Guidance.Finished = append(data.Module1.Guidance.Finished, msg.Value)
		return r.RopDb.SetUserSK(msg.ID, data)
	}

	return nil
}

func (r *RedisActor) SetAccountTag(msg *r2l.L2R_GRPC_SetAccountTag) error {
	data, err := r.RopDb.GetUserSK(msg.ID)
	if err != nil {
		return err
	}

	if data == nil || data.Base == nil {
		return errors.New("user not exist")
	}
	data.Base.AccountTag = msg.Value
	data.Base.CanMute = msg.MuteValue
	msg.Data = data
	return r.RopDb.SetUserSK(msg.ID, data)
}

//nolint:funlen
func (r *RedisActor) ImportUser(msg *r2l.L2R_GRPC_ImportUser) error {
	user := msg.Data.User
	uid := user.Id
	l4g.Debugf("ImportUser: %d %v", uid, msg)

	uuidKey := fmt.Sprintf("%s:%d", user.Uuid, user.ServerId)
	uidStr := strconv.FormatUint(uid, 10)
	r.accounts[uuidKey] = uidStr
	calls := 0
	if _, exist := r.names[user.Name]; exist {
		user.Name = fmt.Sprintf("%simport", user.Name)
		r.names[user.Name] = uidStr
	}
	r.Client.Append("HSet", RedisHashName, user.Name, uidStr)
	calls++
	sufKey := redisop.RedisOpKeyHashDbUser + uidStr
	r.Client.Append("HSet", sufKey, "name", user.Name)
	calls++
	r.Client.Append("HSET", RedisHashAccount, uuidKey, user.Id)
	calls++
	// set user
	r.RopDb.SetUserMCallSK(uid, msg.Data.User)
	calls++
	//set mail
	if msg.Data.Mails != nil {
		mails := make([]*cl.Mail, 0, len(msg.Data.Mails))
		for _, mail := range msg.Data.Mails {
			mails = append(mails, mail)
		}
		r.RopCl.SetSomeMailMCallSK(uid, mails)
		calls++
	}

	//set hero
	if msg.Data.Heroes != nil {
		r.RopCl.DelAllHeroBodyMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		heroes := make([]*cl.HeroBody, 0, len(msg.Data.Heroes))
		for _, hero := range msg.Data.Heroes {
			heroes = append(heroes, hero)
		}
		r.RopCl.SetSomeHeroBodyMCallSK(uid, heroes)
		calls++
	}

	//set equip
	if msg.Data.Equips != nil {
		r.RopCl.DelAllEquipmentMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		equips := make([]*cl.Equipment, 0, len(msg.Data.Equips))
		for _, equip := range msg.Data.Equips {
			equips = append(equips, equip)
		}
		r.RopCl.SetSomeEquipmentMCallSK(uid, equips)
		calls++
	}

	//set formation
	if msg.Data.Formations != nil {
		r.RopCl.DelAllFormationMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		formations := make([]*cl.Formation, 0, len(msg.Data.Formations))
		for _, formation := range msg.Data.Formations {
			formations = append(formations, formation)
		}
		r.RopCl.SetSomeFormationMCallSK(uid, formations)
		calls++
	}

	//set artifact
	if msg.Data.Artifacts != nil {
		r.RopCl.DelAllArtifactMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		artifacts := make([]*cl.Artifact, 0, len(msg.Data.Artifacts))
		for _, artifact := range msg.Data.Artifacts {
			artifacts = append(artifacts, artifact)
		}
		r.RopCl.SetSomeArtifactMCallSK(uid, artifacts)
		calls++
	}

	// set gem
	if msg.Data.Gems != nil {
		r.RopCl.DelAllGemInfoMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		gems := make([]*cl.GemInfo, 0, len(msg.Data.Gems))
		for _, gem := range msg.Data.Gems {
			gems = append(gems, gem)
		}
		r.RopCl.SetSomeGemInfoMCallSK(uid, gems)
		calls++
	}

	// set emblem
	if msg.Data.Emblems != nil {
		r.RopCl.DelAllEmblemInfoMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		emblems := make([]*cl.EmblemInfo, 0, len(msg.Data.Emblems))
		for _, emblem := range msg.Data.Emblems {
			emblems = append(emblems, emblem)
		}
		r.RopCl.SetSomeEmblemInfoMCallSK(uid, emblems)
		calls++
	}

	//set mirage
	if msg.Data.Mirages != nil {
		r.RopCl.DelAllMirageMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		mirages := make([]*cl.Mirage, 0, len(msg.Data.Mirages))
		for _, mirage := range msg.Data.Mirages {
			mirages = append(mirages, mirage)
		}
		r.RopCl.SetSomeMirageMCallSK(uid, mirages)
		calls++
	}

	// set mazePlayer
	if msg.Data.MazePlayer != nil {
		r.RopCl.DelAllMazePlayerMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		mazePlayers := make([]*cl.MazePlayer, 0, len(msg.Data.MazePlayer))
		for _, mazePlayer := range msg.Data.MazePlayer {
			mazePlayers = append(mazePlayers, mazePlayer)
		}
		r.RopCl.SetSomeMazePlayerMCallSK(uid, mazePlayers)
		calls++
	}

	if msg.Data.Activities != nil {
		r.RopCl.DelAllOperateActivityMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		activities := make([]*cl.OperateActivity, 0, len(msg.Data.Activities))
		for _, activity := range msg.Data.Activities {
			activities = append(activities, activity)
		}
		r.RopCl.SetSomeOperateActivityMCallSK(uid, activities)
		calls++
	}

	if msg.Data.GuildUser != nil {
		r.RopDb.SetSomeGuildUserMCall([]*db.GuildUser{msg.Data.GuildUser})
		calls++
	}

	if len(msg.Data.Questionnaires) > 0 {
		r.RopDb.AddSomeQuestionnaireMCallSK(uid, msg.Data.Questionnaires)
		calls++
	}

	//set heroStarUpCosts
	if msg.Data.HeroesStarUpCosts != nil {
		r.RopCl.DelAllHeroStarUpCostsMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		costs := make([]*cl.HeroStarUpCosts, 0, len(msg.Data.HeroesStarUpCosts))
		for _, c := range msg.Data.HeroesStarUpCosts {
			costs = append(costs, c)
		}
		r.RopCl.SetSomeHeroStarUpCostsMCallSK(uid, costs)
		calls++
	}

	//set rites
	if msg.Data.Rites != nil {
		r.RopCl.DelAllRiteMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		rites := make([]*cl.Rite, 0, len(msg.Data.Rites))
		for _, rite := range msg.Data.Rites {
			rites = append(rites, rite)
		}
		r.RopCl.SetSomeRiteMCallSK(uid, rites)
		calls++
	}

	//set seasonlink monuments
	if msg.Data.SeasonlinkMonuments != nil {
		r.RopCl.DelAllSeasonLinkMonumentMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		monuments := make([]*cl.SeasonLinkMonument, 0, len(msg.Data.SeasonlinkMonuments))
		for _, monument := range msg.Data.SeasonlinkMonuments {
			monuments = append(monuments, monument)
		}
		r.RopCl.SetSomeSeasonLinkMonumentMCallSK(uid, monuments)
		calls++
	}

	// set seasonlink activation
	if msg.Data.SeasonlinkActivation != nil {
		r.RopDb.DelAllSeasonLinkActivationMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		r.RopDb.AddSomeSeasonLinkActivationMCallSK(uid, msg.Data.SeasonlinkActivation)
		calls++
	}

	// set remain
	if msg.Data.Remains != nil {
		r.RopCl.DelAllRemainMCallSK(uid)
		calls++
		remains := make([]*cl.Remain, 0, len(msg.Data.Remains))
		for _, remain := range msg.Data.Remains {
			remains = append(remains, remain)
		}
		r.RopCl.SetSomeRemainMCallSK(uid, remains)
		calls++
	}

	// set skin
	if msg.Data.Skins != nil {
		r.RopCl.DelAllSkinMCallSK(uid)
		calls++
		skins := make([]*cl.Skin, 0, len(msg.Data.Skins))
		for _, skin := range msg.Data.Skins {
			skins = append(skins, skin.Clone())
		}
		r.RopCl.SetSomeSkinMCallSK(uid, skins)
		calls++
	}

	//set seasonJewelry
	if msg.Data.SeasonJewelry != nil {
		r.RopCl.DelAllSeasonJewelryMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		jewelryList := make([]*cl.SeasonJewelry, 0, len(msg.Data.SeasonJewelry))
		for _, jewelry := range msg.Data.SeasonJewelry {
			jewelryList = append(jewelryList, jewelry)
		}
		r.RopCl.SetSomeSeasonJewelryMCallSK(uid, jewelryList)
		calls++
	}

	//set rank data
	/*	r.ropDb.SetSomeGlobalRankMCall([]*db.GlobalRank{msg.RankData})
		calls++*/

	//set pokemon
	if msg.Data.Pokemons != nil {
		r.RopCl.DelAllPokemonMCallSK(uid) // 删掉老的，保证重复导入的时候没有多余的数据
		calls++
		pokemons := make([]*cl.Pokemon, 0, len(msg.Data.Pokemons))
		for _, pokemon := range msg.Data.Pokemons {
			pokemons = append(pokemons, pokemon)
		}
		r.RopCl.SetSomePokemonMCallSK(uid, pokemons)
		calls++
	}

	/************************REPLY*******************************/
	//mcall
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d save user error: %v %s", uid, msg, reply.Err)
			ctx.Stop()
		}
	}
	return nil
}

func (r *RedisActor) GetDBUser(ph *parse.PackHead, msg *r2l.L2R_GetDBUser) (retData *r2l.R2L_GetDBUser) {
	l4g.Debugf("user %d GetPrivateMsg %s", ph.UID, msg)
	retData = &r2l.R2L_GetDBUser{
		Ret:         uint32(r2l.RET_OK),
		Uids:        msg.Uids,
		ClientMsgId: msg.ClientMsgId,
	}

	for _, uid := range msg.Uids {
		r.RopDb.GetUserMCallSKs(strconv.FormatUint(uid, 10))
		userData, err := r.RopDb.GetUserByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get user error: %d %s", uid, err)
			continue
		}
		if userData != nil {
			retData.Users = append(retData.Users, userData)
		}
	}

	return
}

func (r *RedisActor) GetWrestleLog(ph *parse.PackHead,
	msg *r2l.L2R_GetWrestleLog) (retData *r2l.R2L_GetWrestleLog) {
	l4g.Debugf("user %d GetWrestleLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetWrestleLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}
	logs, err := r.RopCl.GetAllWrestleLogSK(msg.Id)
	if err != nil {
		l4g.Errorf("L2R_GetWrestleLog. get arena log error. uid:%d err:%s",
			msg.Id, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs
	return
}

func (r *RedisActor) SaveWrestle(msg *r2l.L2R_SaveWrestle) {
	l4g.Debugf("SaveWrestle: %s", msg)
	calls := 0
	if len(msg.Changes) > 0 {
		r.RopDb.SetSomeWrestleUserMCall(msg.Changes)
		calls++
	}
	if len(msg.Deletes) > 0 {
		r.RopDb.RemSomeWrestleUserMCall(msg.Deletes)
		calls++
	}
	if len(msg.ChangeLogs) > 0 {
		for uid, datas := range msg.ChangeLogs {
			r.RopCl.SetSomeWrestleLogMCallSK(uid, datas.Logs)
			calls++
		}
	}
	if len(msg.DeleteLogs) > 0 {
		for uid, datas := range msg.DeleteLogs {
			r.RopCl.RemSomeWrestleLogMCallSK(uid, datas.Ids)
			calls++
		}
	}

	if msg.Wrestle != nil {
		r.RopDb.SetWrestleMCall(msg.Wrestle)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save wrestle error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) LoadGuildUserForReset(ph *parse.PackHead, msg *r2l.L2R_LoadGuildUserForReset) *r2l.R2L_LoadGuildUserForReset {
	l4g.Debugf("user %d LoadGuildUserForReset %s", ph.UID, msg)
	retData := &r2l.R2L_LoadGuildUserForReset{
		Ret:       uint32(r2l.RET_OK),
		ResetType: msg.ResetType,
	}

	for _, data := range msg.Data {
		if data == nil {
			continue
		}
		guildUserForReset := &r2l.GuildUserForReset{
			Gid:  data.Gid,
			Uids: data.Uids,
		}
		if len(data.Uids) > 0 {
			r.RopDb.GetSomeGuildUserMCall(data.Uids)
		}
		if len(data.Uids) > 0 {
			users, err := r.RopDb.GetSomeGuildUserByReply(r.Client.GetReply())
			if err != nil {
				l4g.Error("get guild user error: %v %s", msg, err)
				retData.Ret = uint32(r2l.RET_ERROR)
				ctx.Stop()
			}
			for _, u := range users {
				guildUserForReset.Users = append(guildUserForReset.Users, u)
			}
		}
		retData.Data = append(retData.Data, guildUserForReset)
	}

	return retData
}

func (r *RedisActor) SaveFlower(msg *r2l.L2R_SaveFlower) {
	calls := 0
	if len(msg.Changes) > 0 {
		r.RopCl.SetSomeFlowerMCall(msg.Changes)
		calls++
	}
	if len(msg.Deletes) > 0 {
		r.RopCl.RemSomeFlowerMCall(msg.Deletes)
		calls++
	}

	if len(msg.ChangeSnatchLogs) > 0 {
		for uid, datas := range msg.ChangeSnatchLogs {
			r.RopCl.SetSomeFlowerSnatchLogMCallSK(uid, datas.Logs)
			calls++
		}
	}
	if len(msg.DeleteSnatchLogs) > 0 {
		for uid, datas := range msg.DeleteSnatchLogs {
			r.RopCl.RemSomeFlowerSnatchLogMCallSK(uid, datas.Ids)
			calls++
		}
	}

	if len(msg.ChangeOccupyLogs) > 0 {
		for uid, datas := range msg.ChangeOccupyLogs {
			r.RopCl.SetSomeFlowerOccupyLogMCallSK(uid, datas.Logs)
			calls++
		}
	}
	if len(msg.DeleteOccupyLogs) > 0 {
		for uid, datas := range msg.DeleteOccupyLogs {
			r.RopCl.RemSomeFlowerOccupyLogMCallSK(uid, datas.Ids)
			calls++
		}
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save flower error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GetFlowerSnatchLog(ph *parse.PackHead,
	msg *r2l.L2R_GetFlowerSnatchLog) (retData *r2l.R2L_GetFlowerSnatchLog) {
	l4g.Debugf("user %d GetFlowerSnatchLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetFlowerSnatchLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}

	logs, err := r.RopCl.GetAllFlowerSnatchLogSK(msg.Id)
	if err != nil {
		l4g.Errorf("L2R_GetFlowerSnatchLog. get flower snatch log error. uid:%d err:%s",
			msg.Id, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs
	return
}

func (r *RedisActor) GetFlowerOccupyLog(ph *parse.PackHead,
	msg *r2l.L2R_GetFlowerOccupyLog) (retData *r2l.R2L_GetFlowerOccupyLog) {
	l4g.Debugf("user %d GetFlowerOccupyLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetFlowerOccupyLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}

	logs, err := r.RopCl.GetAllFlowerOccupyLogSK(msg.Id)
	if err != nil {
		l4g.Errorf("L2R_GetFlowerOccupyLog. get flower occupy log error. uid:%d err:%s",
			msg.Id, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs
	return
}

func (r *RedisActor) SaveMonthTasksMsg(msg *r2l.L2R_SaveMonthTasksMsg) {
	calls := 0
	if len(msg.Changes) > 0 {
		r.RopCl.SetSomeMonthTasksMsgMCall(msg.Changes)
		calls++
	}

	if len(msg.Deletes) > 0 {
		r.RopCl.RemSomeMonthTasksMsgMCall(msg.Deletes)
		calls++
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Error("save monthTasksMsg error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GetGuildDonateLog(ph *parse.PackHead,
	msg *r2l.L2R_GetGuildDonateLog) (retData *r2l.R2L_GetGuildDonateLog) {
	l4g.Debugf("user %d GetGuildDonateLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetGuildDonateLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}

	//logs, err := r.ropCl.GetAllGuildDonateLogInfoSK(msg.Id)
	//if err != nil {
	//	l4g.Errorf("L2R_GetGuildDonateLog. get guild donate log error. uid:%d err:%s",
	//		msg.Id, err)
	//	retData.Ret = uint32(r2l.RET_ERROR)
	//	ctx.Stop()
	//}
	//
	//retData.Logs = logs
	return
}

func (r *RedisActor) GetGuildMemberInfo(ph *parse.PackHead,
	msg *r2l.L2R_GetGuildMemberInfo) (retData *r2l.R2L_GetGuildMemberInfo) {
	l4g.Debugf("user %d GetGuildMemberInfo %s", ph.UID, msg)
	retData = &r2l.R2L_GetGuildMemberInfo{
		Ret:           uint32(r2l.RET_OK),
		ClientMsgId:   msg.ClientMsgId,
		GuildNeedLoad: msg.GuildNeedLoad,
		Gid:           msg.Gid,
	}
	var userIDs []uint64
	calls := 0
	if len(msg.Names) != 0 {
		userIDs = r.GetUserIDByName(msg.Names)
	} else {
		userIDs = msg.Users
	}
	for _, id := range userIDs {
		if id != 0 {
			r.RopDb.GetOfflineUserMCallSK(id)
			calls++
		}
	}

	if msg.GuildNeedLoad {
		r.RopDb.GetSomeGuildUserMCall(userIDs)
	}

	/************************REPLY*******************************/
	for i := 0; i < calls; i++ {
		userData, err := r.RopDb.GetOfflineUserByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get offline user %d error: %s", userIDs[i], err)
			retData.Ret = uint32(r2l.RET_ERROR)
		}
		if userData != nil && userData.Id != 0 {
			if userData.Base == nil {
				l4g.Errorf("get offline user %d error: %s", userIDs[i], err)
				retData.Ret = uint32(r2l.RET_ERROR)
			} else {
				retData.Users = append(retData.Users, userData)
			}
		}
	}

	if msg.GuildNeedLoad {
		guildUserData, err := r.RopDb.GetSomeGuildUserByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("get guildUser error: %s", err)
			retData.Ret = uint32(r2l.RET_ERROR)
		}
		retData.GuildUser = guildUserData
	}

	return retData
}

func (r *RedisActor) GetGuildUser(ph *parse.PackHead, msg *r2l.L2R_GetGuildUsers) *r2l.R2L_GetGuildUsers {
	l4g.Debugf("user %d GetGuildUser %s", ph.UID, msg)
	retData := &r2l.R2L_GetGuildUsers{
		Ret:         uint32(r2l.RET_OK),
		ClientMsgId: msg.ClientMsgId,
	}

	if len(msg.Users) == 0 {
		retData.Ret = uint32(r2l.RET_ERROR)
		return retData
	}

	r.RopDb.GetSomeGuildUserMCall(msg.Users)

	/************************REPLY*******************************/
	guildUserData, err := r.RopDb.GetSomeGuildUserByReply(r.Client.GetReply())
	if err != nil {
		l4g.Errorf("get guildUser users:%+v error: %s", msg.Users, err)
		retData.Ret = uint32(r2l.RET_ERROR)
	}
	retData.Users = guildUserData
	return retData
}

func (r *RedisActor) DelChatGroupTag(msg *r2l.L2R_GRPC_DelChatGroupTag) error {
	data, err := r.RopDb.GetUserSK(msg.ID)
	if err != nil {
		return err
	}

	if data.Module4 != nil {
		if data.Module4.ChatGroupTag != nil {
			data.Module4.ChatGroupTag.ChatGroupTags = make(map[string]bool)
			data.Module4.ChatGroupTag.ChatGroupArea = 0
		}
	}

	return r.RopDb.SetUserSK(msg.ID, data)
}

func (r *RedisActor) SaveWorldBossSettle(msg *r2l.L2R_SaveWorldBossSettle) {
	if err := r.RopDb.SetWorldBossSettle(msg.SettleData); err != nil {
		l4g.Errorf("save worldBoss settleData error:%s", err)
		ctx.Stop()
	}
}

func (r *RedisActor) SaveTowerSeasonReset(msg *r2l.L2R_SaveTowerSeasonReset) {
	if err := r.RopDb.SetTowerSeasonReset(msg.Data); err != nil {
		l4g.Errorf("save towerSeason error:%s", err)
		ctx.Stop()
	}
}

func (r *RedisActor) SaveActivityStoryRankReset(msg *r2l.L2R_SaveActivityStoryRankRest) {
	if err := r.RopDb.AddSomeActivityStoryRankRest(msg.Data.Ids); err != nil {
		l4g.Errorf("save activity story error:%s", err)
		ctx.Stop()
	}
}

func (r *RedisActor) SavePeak(msg *r2l.L2R_SavePeak) {
	l4g.Debugf("SavePeak: %s", msg)
	calls := 0
	if msg.IsSeasonReset {
		r.RopCl.DelAllPeakUserMCall()
		calls++
	}

	if msg.Peak != nil {
		r.RopDb.SetPeakMCall(msg.Peak)
		calls++
	}

	if len(msg.Changes) > 0 {
		r.RopCl.SetSomePeakUserMCall(msg.Changes)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save peak error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) DeleteCurrencies(msg *r2l.L2R_GRPC_DeleteCurrencies) uint32 {
	l4g.Debugf("gm delete currencies: %v", msg)
	data, err := r.RopDb.GetUserSK(msg.ID)
	if err != nil {
		l4g.Errorf("user:%d DeleteCurrencies: GetUserSK error. err:%s", msg.ID, err)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	//修改货币数量
	data.BaseOp = db.Op_Dirty

	for _, delCurrency := range msg.DelCurrencies {
		switch delCurrency.Type {
		case uint32(common.RESOURCE_DIAMOND):
			if data.Base.RechargeDiamond+data.Base.PresentDiamond < uint64(delCurrency.Count) {
				l4g.Errorf("user:%d DeleteCurrencies: delCount error. rechargeDiamond:%d presentDiamond:%d delCount:%d",
					msg.ID, data.Base.RechargeDiamond, data.Base.PresentDiamond, delCurrency.Count)
				return uint32(ret.RET_CLIENT_REQUEST_ERROR)
			}
			if data.Base.RechargeDiamond >= uint64(delCurrency.Count) {
				data.Base.RechargeDiamond -= uint64(delCurrency.Count)
				continue
			}
			tmp := uint64(delCurrency.Count) - data.Base.RechargeDiamond
			data.Base.RechargeDiamond = 0
			if tmp >= data.Base.PresentDiamond {
				data.Base.PresentDiamond = 0
			} else {
				data.Base.PresentDiamond -= tmp
			}
		case uint32(common.RESOURCE_GOLD):
			if data.Base.Gold >= uint64(delCurrency.Count) {
				data.Base.Gold -= uint64(delCurrency.Count)
			} else {
				l4g.Errorf("user:%d DeleteCurrencies: delCount error. gold:%d delCount:%d",
					msg.ID, data.Base.Gold, delCurrency.Count)
				return uint32(ret.RET_CLIENT_REQUEST_ERROR)
			}
		case uint32(common.RESOURCE_COUPON):
			data.Base.Coupon -= int64(delCurrency.Count)
		default:
			l4g.Errorf("user:%d DeleteCurrencies: delType error. delType:%d", msg.ID, delCurrency.Type)
			return uint32(ret.RET_CLIENT_REQUEST_ERROR)
		}
	}

	if err := r.RopDb.SetUserSK(msg.ID, data); err != nil {
		l4g.Errorf("user:%d DeleteCurrencies: db error:%s", msg.ID, err)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	return uint32(ret.RET_OK)
}

func (r *RedisActor) DeleteUserGuildMails(msg *r2l.L2R_DeleteUserGuildMails) {
	call := 0
	for _, uid := range msg.Uids {
		r.RopCl.GetAllMailMCallSK(uid)
		call++
	}

	delIds := make(map[uint64][]uint64)
	for i := 0; i < call; i++ {
		if i >= len(msg.Uids) {
			break
		}
		uid := msg.Uids[i]
		mails, err := r.RopCl.GetSomeMailByReply(r.Client.GetReply())
		if err != nil {
			l4g.Errorf("user:%d DeleteUserGuildMails: get Mails error:%+v", uid, err)
			continue
		}
		for mailId, mail := range mails {
			if mail == nil {
				continue
			}
			if mail.BaseId == msg.MailBaseId {
				delIds[uid] = append(delIds[uid], mailId)
			}
		}
	}

	delCall := 0
	for uid, mailIds := range delIds {
		r.RopCl.RemSomeMailMCallSK(uid, mailIds)
		delCall++
	}
	for i := 0; i < delCall; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf(" del user guild mail error: %s", reply.Err)
		}
	}
}

func (r *RedisActor) SaveGst(msg *r2l.L2R_SaveGST) {
	l4g.Debugf("SaveGst: %s", msg)
	calls := 0

	if msg.GetGstLastAward() != nil {
		r.RopDb.SetGstLastAwardMCall(msg.GstLastAward)
		calls++
	}

	if len(msg.User) > 0 {
		r.RopDb.SetSomeGstLogicUserMCall(msg.User)
		calls++
	}

	if len(msg.DeleteUsers) > 0 {
		r.RopDb.RemSomeGstLogicUserMCall(msg.DeleteUsers)
		calls++
	}

	if msg.GetGstLastFirstCenterAward() != nil {
		r.RopDb.SetGstLastFirstCenterAwardMCall(msg.GstLastFirstCenterAward)
		calls++
	}

	if msg.GetGstDragonLastAward() != nil {
		r.RopDb.SetGstDragonLastAwardMCall(msg.GstDragonLastAward)
		calls++
	}

	if msg.GetGstMobLastAward() != nil {
		r.RopDb.SetGstMobLastAwardMCall(msg.GstMobLastAward)
		calls++
	}

	if msg.GetGstChallengeLastAward() != nil {
		r.RopDb.SetGstChallengeLastAwardMCall(msg.GstChallengeLastAward)
		calls++
	}

	if len(msg.ChangeLogs) > 0 {
		for uid, data := range msg.ChangeLogs {
			r.RopCl.SetSomeGSTChallengeFightLogMCallSK(uid, data.Logs)
			calls++
		}
	}
	if len(msg.DeleteLogs) > 0 {
		for uid, data := range msg.DeleteLogs {
			r.RopCl.RemSomeGSTChallengeFightLogMCallSK(uid, data.Ids)
			calls++
		}
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save logic gst error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

// 保存赛季羁绊激活信息
func (r *RedisActor) SaveSeasonLinkActivation(userId uint64, activation uint32) {
	calls := 0
	r.RopDb.AddSomeSeasonLinkActivationMCallSK(userId, []uint32{activation})
	calls++
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d SaveSeasonLinkActivation error: %s activation %+v", userId, reply.Err, activation)
			ctx.Stop()
		}
	}
}

// 删除赛季羁绊激活信息
func (r *RedisActor) DelSeasonLinkActivation(userId uint64) {
	calls := 0
	r.RopDb.DelAllSeasonLinkActivationMCallSK(userId)
	calls++
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d DelSeasonLinkActivation error: %s", userId, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SavePeopleGroupPackage(msg *r2l.L2R_SavePeopleGroupPackage) {
	l4g.Debugf("SavePeopleGroupPackage: %s", msg)
	calls := 0

	if len(msg.Changes) > 0 {
		r.RopDb.SetSomePeopleGroupPackageMCall(msg.Changes)
		calls++
	}

	if len(msg.Deletes) > 0 {
		r.RopDb.RemSomePeopleGroupPackageMCall(msg.Deletes)
		calls++
	}
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save peopleGroupPackage error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) ChangeUserName(msg *r2l.L2R_GRPC_ChangeUserName) *r2l.R2L_GRPC_ChangeUserName {
	resp := &r2l.R2L_GRPC_ChangeUserName{
		Ret:      uint32(r2l.RET_ERROR),
		Callback: msg.Callback,
	}
	uidStr := strconv.FormatUint(msg.UserId, 10)
	data, err := r.RopDb.GetUserSK(msg.UserId)
	if err != nil {
		l4g.Errorf("cant find user data:%d", msg.UserId)
		return resp
	}
	oldName := data.Name
	if oldName != "" {
		if id, exist := r.names[oldName]; exist {
			if id != uidStr {
				l4g.Errorf("Setname error msg:%v, old id:%s", msg, id)
				return resp
			}
		}
		r.Client.Append("HDel", RedisHashName, oldName)
		if _, err := r.Client.GetReply().Int(); err != nil {
			l4g.Errorf("SetName error: del old name reply msg:%v err:%s", msg, err)
			return resp
		}
	}

	var newName string
	for {
		newName = r.randString(15) //nolint:mnd // 生成长度为 15 的随机字符串
		if _, ok := r.names[newName]; !ok {
			break
		}
	}
	r.Client.Append("HSet", RedisHashName, newName, uidStr)
	if _, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("SetName error, set newname reply msg:%v err:%s", msg, err)
		return resp
	}

	sufKey := redisop.RedisOpKeyHashDbUser + uidStr
	r.Client.Append("HSet", sufKey, "name", newName)
	if _, err := r.Client.GetReply().Int(); err != nil {
		l4g.Errorf("SetName error: set user replay msg:%v err:%s", msg, err)
		return resp
	}

	delete(r.names, oldName)
	r.names[newName] = uidStr
	resp.Ret = uint32(r2l.RET_OK)
	return resp
}

var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

func (r *RedisActor) randString(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[r.rd.Intn(len(letterRunes))]
	}
	return string(b)
}

func (r *RedisActor) SaveSeasonArena(msg *r2l.L2R_SaveSeasonArena) {
	l4g.Debugf("SaveSeasonArena: %s", msg)
	calls := 0
	if len(msg.ChangeUsers) > 0 {
		r.RopDb.SetSomeSeasonArenaUserMCall(msg.ChangeUsers)
		calls++
	}
	if len(msg.Deletes) > 0 {
		r.RopDb.RemSomeSeasonArenaUserMCall(msg.Deletes)
		calls++
	}

	if msg.TopUser != nil {
		r.RopDb.SetSeasonArenaTopUserMCall(msg.TopUser)
		calls++
	}

	if msg.SeasonAwardLastAward != nil {
		r.RopDb.SetSeasonArenaLastAwardMCall(msg.SeasonAwardLastAward)
		calls++
	}

	if len(msg.ChangeLogs) > 0 {
		for uid, datas := range msg.ChangeLogs {
			r.RopCl.SetSomeSeasonArenaLogMCallSK(uid, datas.Logs)
			calls++
		}
	}
	if len(msg.DeleteLogs) > 0 {
		for uid, datas := range msg.DeleteLogs {
			r.RopCl.RemSomeSeasonArenaLogMCallSK(uid, datas.Ids)
			calls++
		}
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save season arena error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GetSeasonArenaLog(ph *parse.PackHead,
	msg *r2l.L2R_GetSeasonArenaLog) (retData *r2l.R2L_GetSeasonArenaLog) {
	l4g.Debugf("user %d GetSeasonArenaLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetSeasonArenaLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}
	logs, err := r.RopCl.GetAllSeasonArenaLogSK(msg.Id)
	if err != nil {
		l4g.Errorf("L2R_GetSeasonArenaLog. get arena log error. uid:%d err:%s",
			msg.Id, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs
	return
}

func (r *RedisActor) SaveHotRankUserFormation(
	msg *r2l.L2R_SaveHotRankUserFormation) {
	calls := 0
	if len(msg.Changes) > 0 {
		saveMap := make(map[uint64][]*db.LogicHotRankUserFormation)
		for _, data := range msg.Changes {
			key := uint64(uint64(data.FormationId)<<32 | uint64(data.Uid)%10)
			saveMap[key] = append(saveMap[key], data)
		}
		for key, datas := range saveMap {
			r.RopDb.SetSomeLogicHotRankUserFormationMCallSK(key, datas)
			calls++
		}
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save hot rank user formation error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveLogicHotRankCollectionUpdateFlag(
	msg *r2l.L2R_SaveLogicHotRankCollectionUpdateFlag) {
	calls := 0
	if msg == nil {
		return
	}

	if msg.Log != nil {
		r.RopDb.SetSomeLogicHotRankCollectionLogMCall(msg.Log)
		calls++
	}

	if msg.Delete {
		r.RopDb.DelAllLogicHotRankCollectionLogMCall()
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save logic hot rank collection update flage error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveTalentTreeHot(
	msg *r2l.L2R_SaveLogicTalentTreeHot) {
	if msg == nil {
		return
	}

	err := r.RopDb.SetLogicTalentTreeHot(msg.Data)
	if err != nil {
		l4g.Errorf("save logic talentTreeHot error: %s %s", msg, err)
		ctx.Stop()
	}
}

func (r *RedisActor) SaveOfflineResource(msg *r2l.L2R_SaveOfflineResource) {
	l4g.Debugf("SaveOfflineResource: %s", msg)
	calls := 0

	if len(msg.Changes) > 0 {
		r.RopDb.SetSomeOfflineResourceMCall(msg.Changes)
		calls++
	}

	if len(msg.Deletes) > 0 {
		r.RopDb.RemSomeOfflineResourceMCall(msg.Deletes)
		calls++
	}
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save OfflineResource error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveActivityTower(msg *r2l.L2R_SaveActivityTower) {
	l4g.Debugf("SaveActivityTower: %s", msg)
	calls := 0

	if msg.Data != nil {
		r.RopDb.SetActivityTowerMCall(msg.Data)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save activityTower error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) GetGstChallengeLog(ph *parse.PackHead,
	msg *r2l.L2R_GetGstChallengeLog) (retData *r2l.R2L_GetGstChallengeLog) {
	l4g.Debugf("user %d GetGstChallengeLog %s", ph.UID, msg)
	retData = &r2l.R2L_GetGstChallengeLog{
		Ret:         uint32(r2l.RET_OK),
		Id:          msg.Id,
		ClientMsgId: msg.ClientMsgId,
	}
	logs, err := r.RopCl.GetAllGSTChallengeFightLogSK(msg.Id)
	if err != nil {
		l4g.Errorf("L2R_GetGstChallengeLog. get arena log error. uid:%d err:%s",
			msg.Id, err)
		retData.Ret = uint32(r2l.RET_ERROR)
		ctx.Stop()
	}

	retData.Logs = logs
	return
}

func (r *RedisActor) SaveSeasonComplianceReward(msg *r2l.L2R_SaveSeasonComplianceReward) {
	l4g.Debugf("SaveSeasonComplianceReward: %s", msg)
	calls := 0

	if msg.Data != nil {
		r.RopDb.SetSeasonComplianceAwardMCall(msg.Data)
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save season compliance reward error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveHotfixCode(msg *r2l.L2R_SaveHotfixCode) {
	calls := 0
	if msg.Code != nil {
		r.RopDb.SetSomeHotfixCodeMCall([]*db.HotfixCode{msg.Code})
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("save hotfix code error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}

func (r *RedisActor) SaveTowerPokemonReset(msg *r2l.L2R_SaveTowerPokemonReset) {
	if err := r.RopDb.SetTowerPokemonReset(msg.Data); err != nil {
		l4g.Errorf("save towerpokemon error:%s", err)
		ctx.Stop()
	}
}

func (r *RedisActor) SetOfflineVipAward(uid uint64, msg *r2l.L2R_GRPC_SetOfflineVipAward) uint32 {
	awards, err := r.RopCl.GetAllOfflineVipAwardSK(uid)
	if err != nil {
		l4g.Errorf("user %d get offlineVipAwardId error: %s id:%v",
			uid, err, msg.OfflineVipAwardID)
		return uint32(ret.RET_ERROR)
	}

	if _, exist := awards[msg.OfflineVipAwardID]; exist {
		l4g.Errorf("user %d had offlineVipAwardId :%v", uid, msg.OfflineVipAwardID)
		return uint32(ret.RET_REPEATED_RECEIVE_AWARD)
	}

	calls := 0
	newMsg := &cl.OfflineVipAward{
		Id:   msg.OfflineVipAwardID,
		Time: uint64(time.Now().Unix()),
	}
	r.RopCl.SetSomeOfflineVipAwardMCallSK(uid, []*cl.OfflineVipAward{newMsg})
	calls++
	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("user %d add offlineVipAwardId error: %s id:%v",
				uid, reply.Err, msg.OfflineVipAwardID)
			ctx.Stop()
		}
	}

	return uint32(ret.RET_OK)
}

func (r *RedisActor) SaveGoddessPrizeDraw(msg *r2l.L2R_SaveGoddessPrizeDraw) {
	if err := r.RopDb.SetGoddessPrizeDraw(msg.Data); err != nil {
		l4g.Errorf("save towerpokemon error:%s", err)
		ctx.Stop()
	}
}

func (r *RedisActor) SaveBalanceArena(msg *r2l.L2R_SaveBalanceArena) {
	l4g.Debugf("balanceArena.SaveBalanceArena: %s", msg)
	calls := 0
	if msg.GetBalanceArena() != nil {
		r.RopDb.SetBalanceArena(msg.GetBalanceArena())
		calls++
	}

	for i := 0; i < calls; i++ {
		if reply := r.Client.GetReply(); reply.Err != nil {
			l4g.Errorf("balanceArena.SaveBalanceArena: error: %s %s", msg, reply.Err)
			ctx.Stop()
		}
	}
}
