package session

import (
	"strconv"
	"strings"
	"sync"

	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/discovery"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"
	"gitlab.qdream.com/kit/sea/zebra/tcp"

	l4g "github.com/ivanabc/log4go"
)

type Config struct {
	Name              string
	ReadQSize         int
	ReconnectInterval int64
	Discovery         *discovery.Config
	Zebra             *tcp.Config
	Actor             *actor.Config

	//gateway专用
	Guard      string //etcd中配置的保底gateways
	PortOffset int    //gateway监听端口相对于client端口偏移,当前仅针对gateway使用
}

// Manager Watch所有 Node(不仅仅是Alive Node)
type Manager struct {
	ReadQ chan *Message

	mtx            sync.Mutex
	netAddr2Client map[uint64]*Client //全部的客户端（与node地址一一对应）
	clients        map[uint64]*Client //连接正常的客户端

	tmpClients []*Client //访问优化

	cfg       *Config
	sessionID uint64
	ctx       *ctx.Group
	closed    bool
}

func NewManager(ctx *ctx.Group, cfg *Config) *Manager {
	return &Manager{
		cfg:            cfg,
		ReadQ:          make(chan *Message, cfg.ReadQSize),
		clients:        make(map[uint64]*Client),
		tmpClients:     make([]*Client, 0, 10), //nolint:mnd
		netAddr2Client: make(map[uint64]*Client),
		ctx:            ctx,
	}
}

func (m *Manager) Name() string { return m.cfg.Name }

func (m *Manager) Done() <-chan struct{} {
	return m.ctx.Done()
}

func (m *Manager) Run() {
	m.onLoad()
	go func() {
		discovery.Watch(m.ctx.CreateChild(), m.cfg.Discovery, m)
		m.ctx.Stop()
		l4g.Infof("%s discovery goruntine closed", m.Name())
	}()
}

func (m *Manager) onLoad() {
	//配置文件更新时重新加载
	if m.cfg.Guard != "" {
		addrs := strings.Split(m.cfg.Guard, ",")
		for _, addr := range addrs {
			m.create(addr)
		}
	}
}

func (m *Manager) Close() {
	m.ForceWrite()
	//关闭watch
	m.ctx.Stop()
	m.tmpClients = m.tmpClients[0:0]

	m.mtx.Lock()
	for _, ses := range m.netAddr2Client {
		m.tmpClients = append(m.tmpClients, ses)
		delete(m.netAddr2Client, ses.NetAddr())
	}
	m.closed = true
	m.mtx.Unlock()

	for _, ses := range m.tmpClients {
		ses.stop()
		ses.wait()
	}

	l4g.Infof("[%s] manager close ...(%d-%d)", m.Name(), len(m.clients), len(m.netAddr2Client))
	m.ctx.Wait()
	m.ctx.Finish()
	l4g.Infof("[%s] manager closed...", m.Name())
}

func (m *Manager) parseNetAddress(key string) string {
	ss := strings.Split(key, "/")
	addr := ss[len(ss)-1]
	ss = strings.Split(addr, ":")
	net, port := ss[0], ss[1]
	portNum, _ := strconv.Atoi(port)
	//统一机制: 网关服务器端监听的端口 = 网关客户端监听端口 + logicServicePortOffset
	addr = net + ":" + strconv.Itoa(portNum+m.cfg.PortOffset)
	return addr
}

func (m *Manager) Set(key, value string) {
	l4g.Infof("[%s] manager set %s->%s", m.Name(), key, value)
	m.Create(key, value)
}

func (m *Manager) Modify(key, value string) {
	l4g.Debugf("[%s] manager modify %s->%s", m.Name(), key, value)
}

func (m *Manager) Create(key, value string) {
	addr := m.parseNetAddress(key)
	l4g.Infof("manager create %s->%s : %s addr: %s", key, value, m.Name(), addr)
	m.create(addr)
}

func (m *Manager) create(addr string) {
	serverID := util.NetAddrToUint64(addr)

	var client *Client
	m.mtx.Lock()
	if !m.closed {
		if ses, ok := m.netAddr2Client[serverID]; !ok {
			m.sessionID++
			cfg := &Config{
				ReadQSize:         m.cfg.ReadQSize,
				ReconnectInterval: m.cfg.ReconnectInterval,
				Discovery:         m.cfg.Discovery,
				Zebra: &tcp.Config{
					Address:           addr,
					MaxReadMsgSize:    m.cfg.Zebra.MaxReadMsgSize,
					ReadMsgQueueSize:  m.cfg.Zebra.ReadMsgQueueSize,
					ReadTimeOut:       m.cfg.Zebra.ReadTimeOut,
					MaxWriteMsgSize:   m.cfg.Zebra.MaxWriteMsgSize,
					WriteMsgQueueSize: m.cfg.Zebra.WriteMsgQueueSize,
					WriteTimeOut:      m.cfg.Zebra.WriteTimeOut,
				},
				Actor: m.cfg.Actor,
			}
			client = newClient(m.ctx.CreateChild(), m, cfg, m.sessionID)
			m.netAddr2Client[serverID] = client
		} else {
			l4g.Infof("[%s] exist same address, (%d - %d)", m.Name(), ses.ID(), ses.NetAddr())
		}
	} else {
		l4g.Infof("[%s] manager had closed", m.Name())
	}
	m.mtx.Unlock()
	if client != nil {
		go client.run()
	}
}

func (m *Manager) Delete(key string) {
	addr := m.parseNetAddress(key)
	l4g.Infof("[%s] manager delete %s: addr: %s", m.Name(), key, addr)
	serverID := util.NetAddrToUint64(addr)

	var client *Client
	m.mtx.Lock()
	if tmp, ok := m.netAddr2Client[serverID]; ok {
		client = tmp
		delete(m.netAddr2Client, serverID)
	}
	m.mtx.Unlock()
	if client != nil {
		client.stop()
		go client.wait()
	}
}

func (m *Manager) GetClientByID(id uint64) *Client {
	var client *Client
	m.mtx.Lock()
	client = m.clients[id]
	m.mtx.Unlock()
	return client
}

func (m *Manager) GetClientByAddr(addr uint64) *Client {
	var client *Client
	m.mtx.Lock()
	client = m.netAddr2Client[addr]
	if client != nil {
		if _, exist := m.clients[client.ID()]; !exist {
			client = nil
		}
	}
	m.mtx.Unlock()
	return client
}

func (m *Manager) GetNetAddrClientByAddr(addr uint64) *Client {
	var client *Client
	m.mtx.Lock()
	client = m.netAddr2Client[addr]
	m.mtx.Unlock()
	return client
}

func (m *Manager) addClient(client *Client) {
	m.mtx.Lock()
	m.clients[client.ID()] = client
	m.mtx.Unlock()
}

func (m *Manager) deleteClient(client *Client) {
	m.mtx.Lock()
	delete(m.clients, client.ID())
	m.mtx.Unlock()
}

func (m *Manager) BroadcastMessage(ph *parse.PackHead, data interface{}) {
	m.tmpClients = m.tmpClients[0:0]
	m.mtx.Lock()
	for _, ses := range m.clients {
		m.tmpClients = append(m.tmpClients, ses)
	}
	m.mtx.Unlock()
	for _, ses := range m.tmpClients {
		ses.Write(ph, data)
	}
}

func (m *Manager) ForceWrite() {
	m.tmpClients = m.tmpClients[0:0]
	m.mtx.Lock()
	for _, ses := range m.clients {
		m.tmpClients = append(m.tmpClients, ses)
	}
	m.mtx.Unlock()
	for _, ses := range m.tmpClients {
		ses.ForceWrite()
	}
}
