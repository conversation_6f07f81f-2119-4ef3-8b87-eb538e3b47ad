package evententity

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/gst"
	"app/logic/activity/guild"
	"app/logic/activity/hotrank"
	"app/logic/activity/seasoncompliance"
	"app/logic/character"
	aevent "app/logic/event"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/p2l"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"slices"

	// l4g "github.com/ivanabc/log4go"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/event"
	"gitlab.qdream.com/kit/sea/time"
)

//nolint:funlen
func InitEventWatcher(eventm *event.Manager) {
	//事件日志
	eventm.Watch(aevent.IeCreate, &doCreateEvent)
	eventm.Watch(aevent.IeOnline, &doOnlineEvent)
	eventm.Watch(aevent.IeSave, &doSaveEvent)
	eventm.Watch(aevent.IeLevelToX, &doLevelUpEvent)
	eventm.Watch(aevent.IeSetName, &doSetNameEvent)
	eventm.Watch(aevent.IeSetIcon, &doSetIconEvent)
	eventm.Watch(aevent.IeNewHero, &doNewHeroEvent)
	eventm.Watch(aevent.IeReceiveDispatchTask, &doReceiveDispatchTask)
	eventm.Watch(aevent.IeHeroUpdateMaxLv, &doHeroUpdateMaxLvEvent)
	eventm.Watch(aevent.IeHeroStarUp, &doHeroStarUpEvent)
	//eventm.Watch(aevent.IeTriggerScrollEvent, &doTriggerScrollEvent)
	eventm.Watch(aevent.IeTowerNewFloor, &doTowerNewFloorEvent)
	eventm.Watch(aevent.IeTowerSweep, &doTowerSweepEvent)
	eventm.Watch(aevent.IeDungeonToX, &doDungeonFightWinEvent)
	eventm.Watch(aevent.IeDungeonSpeedOnhook, &doDungeonSpeedOnhookEvent)
	eventm.Watch(aevent.IeNewEmblem, &doNewEmblemEvent)
	//eventm.Watch(aevent.IeEmblemStageUp, &doEmblemStageUpEvent)
	//eventm.Watch(aevent.IeEmblemBlessingLevelUp, &doEmblemBlessingLevelUp)
	eventm.Watch(aevent.IeRefreshMysteryShop, &doRefreshMysteryShopEvent)
	eventm.Watch(aevent.IeBuyGoodsFromMysteryShop, &doBuyGoodsFromMysteryShopEvent)
	eventm.Watch(aevent.IeArenaFight, &doArenaFightEvent)
	eventm.Watch(aevent.IeMirageFight, &doMirageFightEvent)
	eventm.Watch(aevent.IeTrialFightWinEvent, &doTrialFightWinEvent)
	eventm.Watch(aevent.IeTrialSweepEvent, &doTrialSweepEvent)
	eventm.Watch(aevent.IeNewGem, &doNewGemEvent)
	eventm.Watch(aevent.IeNewMemory, &doNewMemoryEvent)
	eventm.Watch(aevent.IeSummon, &doSummonEvent)
	eventm.Watch(aevent.IeGetNewArtifact, &doGetNewArtifactEvent)
	eventm.Watch(aevent.IeArtifactStarUp, &doArtifactStarUpEvent)
	eventm.Watch(aevent.IeArtifactStrength, &doArtifactStrengthEvent)
	eventm.Watch(aevent.IeArtifactForge, &doArtifactForgeEvent)
	//eventm.Watch(aevent.IeGetNewEquip, &doGetNewEquipEvent)
	eventm.Watch(aevent.IeEquipEvolution, &doEquipEvolutionEvent)
	eventm.Watch(aevent.IeEquipRefine, &doEquipRefineEvent)
	eventm.Watch(aevent.IeEquipEnchant, &doEquipEnchantEvent)
	//eventm.Watch(aevent.IeEquipWear, &doEquipWearEvent)
	eventm.Watch(aevent.IeEquipStrength, &doEquipStrengthEvent)
	eventm.Watch(aevent.IeGuildDungeonFight, &doGuildDungeonFightEvent)
	eventm.Watch(aevent.IeGuildDungeonChapterChange, &doGuildDungeonChapterChangeEvent)
	eventm.Watch(aevent.IeGoldBuy, &doGoldBuyGetGold)
	eventm.Watch(aevent.IeMazeEnemyBattle, &doMazeEnemyBattle)
	eventm.Watch(aevent.IeGemConvert, &doGemConvert)
	eventm.Watch(aevent.IeGemCompose, &doGemCompose)
	eventm.Watch(aevent.IeTalesEliteFight, &doTalesEliteFight)
	eventm.Watch(aevent.IeTalesEliteWipe, &doTalesEliteWipe)

	eventm.Watch(aevent.IeBuyGoodsFromFixedShop, &doBuyGoodsFromFixedShopEvent)
	eventm.Watch(aevent.IeActiveHandbook, &doActiveHandbookEvent)
	eventm.Watch(aevent.IeBuyGoodsFromAnyShop, &doBuyGoodsFromAnyShopEvent)
	eventm.Watch(aevent.IeDecomposeHero, &doDecomposeHeroEvent)
	eventm.Watch(aevent.IeDecomposeEquip, &doDecomposeEquipEvent)
	eventm.Watch(aevent.IeArenaLike, &doArenaLikeEvent)
	eventm.Watch(aevent.IeRankAchieveReceiveAward, &doRankAchieveReceiveAwardEvent)
	eventm.Watch(aevent.IeMedalLevel, &doMedalLevelEvent)
	eventm.Watch(aevent.IeMedalDailyAward, &doMedalDailyAwardEvent)
	eventm.Watch(aevent.IeReceiveDailyAward, &doReceiveDailyAwardEvent)
	eventm.Watch(aevent.IeReceiveDailyAwardBox, &doReceiveDailyAwardBoxEvent)
	eventm.Watch(aevent.IeDispatchRefresh, &doDispatchRefreshEvent)
	eventm.Watch(aevent.IeHeroStage, &doHeroStageEvent)
	eventm.Watch(aevent.IeGuildSignIn, &doGuildSignInEvent)
	eventm.Watch(aevent.IeGuildDungeonRecvBox, &doGuildDungeonRecvBoxEvent)
	//eventm.Watch(aevent.IeGuildTalentLevelUp, &doGuildTalentLevelUpEvent)
	eventm.Watch(aevent.IeTrialReceiveStarAward, &doTrialReceiveStarAwardEvent)
	eventm.Watch(aevent.IeHeroChange, &doHeroChangeEvent)
	eventm.Watch(aevent.IeMazeReceiveBoxAward, &doMazeReceiveAwardEvent)
	eventm.Watch(aevent.IeTalesFinish, &doTalesFinishEvent)
	//eventm.Watch(aevent.IeMiragePass, &doMiragePassEvent)
	eventm.Watch(aevent.IeFlowerLoot, &doFlowerLootEvent)
	//eventm.Watch(aevent.IeForestUpdateSlot, &doForesUpdateSlotEvent)
	eventm.Watch(aevent.IeEmblemBlessing, &doEmblemBlessingEvent)
	eventm.Watch(aevent.IeMazeUseLifeStream, &doMazeUseLifeStreamEvent)
	eventm.Watch(aevent.IeMazeUseHeroRevive, &doMazeUseHeroReviveEvent)
	eventm.Watch(aevent.IeMazeUseRuleSword, &doMazeUseRuleSwordEvent)
	eventm.Watch(aevent.IeMazeBossLevel, &doMazeBossLevelEvent)
	eventm.Watch(aevent.IeFlowerLevel, &doFlowerLevelEvent)
	eventm.Watch(aevent.IeFirstStarTowerToX, &FirstStarTowerEvent)
	eventm.Watch(aevent.IeCrystalContractUpdate, &doCrystalContractUpdateEvent)
	// eventm.Watch(aevent.IeCrystalUnlockSlot, &doCrystalUnlockSlotEvent)
	// eventm.Watch(aevent.IeCrystalBlessingLevelUp, &doCrystalBlessingLevelUpEvent)
	// eventm.Watch(aevent.IePassPoint, &PassPointEvent)
	eventm.Watch(aevent.IeRechargeAfter, &RechargeAfterEvent)
	eventm.Watch(aevent.IeCostDiamond, &CostDiamondEvent)
	eventm.Watch(aevent.IeSummonTrueFiveHero, &SummonTrueFiveHero)
	eventm.Watch(aevent.IeCompleteDispatchTask, &CompleteDispatchTask)
	eventm.Watch(aevent.IeTowerStartFight, &TowerStartFight)
	eventm.Watch(aevent.IeTowerStarStartFight, &TowerStarStartFight)
	eventm.Watch(aevent.IeBuyNumberTypeCount, &BuyNumberTypeCount)
	eventm.Watch(aevent.IeShopBuyItem, &ShopBuyItem)
	eventm.Watch(aevent.IeShopBuyAny, &ShopBuyAny)
	eventm.Watch(aevent.IeArenaShopBuyEquip, &ArenaShopBuyEquip)
	eventm.Watch(aevent.IeDecomposeRareEquip, &DecomposeRareEquip)
	eventm.Watch(aevent.IeChat, &ChatEvent)
	eventm.Watch(aevent.IeDivineDemonSummon, &DivineDemonSummonEvent)
	eventm.Watch(aevent.IeWrestleFight, &WrestleFight)
	eventm.Watch(aevent.IeGoddessContractLevelUp, &GoddessContactLevelUpEvent)
	eventm.Watch(aevent.IeArtifactSummon, &ArtifactSummonEvent)
	eventm.Watch(aevent.IeArtifactPointSummon, &ArtifactSummonPointEvent)
	eventm.Watch(aevent.IeDungeonSpeedOnHookCost, &doDungeonSpeedOnhookCostEvent)
	eventm.Watch(aevent.IeEquipmentCultivate, &EquipmentCultivateEvent)
	eventm.Watch(aevent.IeEmblemCultivate, &EmblemCultivateEvent)
	eventm.Watch(aevent.IeGuildTalentLevelUp, &GuildTalentLevelUpEvent)
	eventm.Watch(aevent.IeGuildDungeonBuyChallengeTimesToX, &GuildDungeonBuyChallengeTimesEvent)
	eventm.Watch(aevent.IeHeroGemLevelUp, &doHeroGemLevelUpEvent)
	eventm.Watch(aevent.IeGoddessTouch, &GoddessTouchEvent)
	eventm.Watch(aevent.IeGoddessFeed, &GoddessFeedEvent)
	eventm.Watch(aevent.IeGoddessTouchOrFeed, &GoddessTouchOrFeedEvent)
	eventm.Watch(aevent.IeGoddessUnlock, &GoddessUnlockEvent)
	eventm.Watch(aevent.IeGoddessStoryFinish, &GoddessStoryFinishEvent)
	eventm.Watch(aevent.IeGoddessLevelUp, &GoddessLevelUpEvent)
	eventm.Watch(aevent.IeFlowerPlant, &doFlowerPlantEvent)
	eventm.Watch(aevent.IeArtifactDebutSummon, &doArtifactDebutSummonEvent)
	eventm.Watch(aevent.IeLinkSummon, &doLinkSummonEvent)
	eventm.Watch(aevent.IeGuildDonate, &doGuildDonateEvent)
	eventm.Watch(aevent.IeTowerSeasonWin, &doTowerSeasonWinEvent)
	eventm.Watch(aevent.IeDailyAttendanceExtraAwardAfter, &DailyAttendanceExtraAwardAfterEvent)
	eventm.Watch(aevent.IeBattleFailed, &BattleFailedEvent)
	eventm.Watch(aevent.IeGoddessRecovery, &goddessRecoveryEvent)
	eventm.Watch(aevent.IeWorldBossFight, &worldBossFightEvent)
	eventm.Watch(aevent.IeWorldBossWorship, &worldBossWorshipEvent)
	eventm.Watch(aevent.IeWorldBossShopBuy, &worldBossShopBuyEvent)
	eventm.Watch(aevent.IeWrestleLikeCount, &wrestleLikeEvent)
	eventm.Watch(aevent.IeTowerSeasonPass, &towerSeasonPassEvent)
	eventm.Watch(aevent.IeNewSkin, &doNewSkinEvent)
	eventm.Watch(aevent.IeActivityStoryDungeon, &doActivityStoryDungeonFightWinEvent)
	eventm.Watch(aevent.IeActivityStorySweep, &doActivityStoryDungeonSweepEvent)
	eventm.Watch(aevent.IeTowerSeasonFight, &doTowerSeasonFightEvent)
	eventm.Watch(aevent.IeSeasonLevelToX, &doSeasonLevelUpEvent)
	eventm.Watch(aevent.IeDisorderLandFight, &doDisorderLandFightEvent)
	eventm.Watch(aevent.IeRiteUpToX, &doRiteUpEvent)
	eventm.Watch(aevent.IeRiteMarkGet, &doRiteMarkGetEvent)
	eventm.Watch(aevent.IeGetMonumentToX, &doGetMonumentEvent)
	eventm.Watch(aevent.IeSeasonDungeonToX, &doSeasonDungeonFightWinEvent)
	eventm.Watch(aevent.IePyramidDrawNumToX, &doPyramidDrawEvent)
	eventm.Watch(aevent.IePyramidDrawNum, &doPyramidDrawNumEvent)
	eventm.Watch(aevent.IeSeasonArenaFight, &doSeasonArenaFightEvent)
	eventm.Watch(aevent.IeActivityTurnTableSummon, &doActivityTurnTableSummonEvent)
	eventm.Watch(aevent.IeHotRankUpdate, &doHotRankUpdateEvent)
	eventm.Watch(aevent.IeGstDonate, &doGstDonateEvent)
	eventm.Watch(aevent.IeBossRushFight, &doBossRushFightEvent)
	eventm.Watch(aevent.IeBossRushBuyTimes, &doBossRushBuyTimesEvent)
	eventm.Watch(aevent.IeTalentTreeLevelUp, &doTalentTreeLevelUpEvent)
	eventm.Watch(aevent.IeRemainGet, &doRemainGetEvent)
	eventm.Watch(aevent.IeRemainStarUp, &doRemainStarUpEvent)
	eventm.Watch(aevent.IeDragonFightCount, &doDragonFightCountEvent)
	eventm.Watch(aevent.IeArtifactFragment, &doArtifactFragmentEvent)
	eventm.Watch(aevent.IeComplianceNewEmblem, &doIeComplianceNewEmblemEvent)
	eventm.Watch(aevent.IeSeasonJewelryGet, &doSeasonJewelryGetEvent)
	eventm.Watch(aevent.IeSeasonJewelrySkillLevelUp, &doSeasonJewelrySkillLevelUpEvent)
	eventm.Watch(aevent.IeCNWebRecharge, &doCNWebRechargeEvent)
	eventm.Watch(aevent.IeSeasonComplianceEmblemStageInit, &doSeasonComplianceEmblemStageInitEvent)
	eventm.Watch(aevent.IeSeasonMapFightX, &doIeSeasonMapFightXEvent)
	eventm.Watch(aevent.IePokemonGetNew, &doIeNewPokemonEvent)
	eventm.Watch(aevent.IePokemonGetFragment, &doIePokemonFragmentEvent)
	eventm.Watch(aevent.IePokemonSummon, &doIePokemonSummonEvent)

	//成就和任务
	for _, evt := range goxml.GetData().TaskInfoM.AchieveEvents() {
		eventm.Watch(evt, &achieveHandlerOnEvent)
	}
	for _, evt := range goxml.GetData().TaskInfoM.DailyEvents() {
		eventm.Watch(evt, &dailyHandlerOnEvent)
	}
	for _, evt := range goxml.GetData().TaskInfoM.WeeklyEvents() {
		eventm.Watch(evt, &weeklyHandlerOnEvent)
	}
	for _, evt := range goxml.GetData().TaskInfoM.MonthlyEvents() {
		eventm.Watch(evt, &monthlyHandlerOnEvent)
	}

	for _, evt := range goxml.GetData().TaskInfoM.LineEvents() {
		eventm.Watch(evt, &lineHandlerOnEvent)
	}

	// 嘉年华任务
	for _, evt := range goxml.GetData().CarnivalTaskInfoM.CarnivalEvents() {
		eventm.Watch(evt, &carnivalHandlerOnEvent)
	}
	// 功勋任务
	for evt := range goxml.GetData().MedalTaskInfoM.MedalEvents() {
		eventm.Watch(evt, &medalHandlerOnEvent)
	}
	// 新功能预告任务
	for evt := range goxml.GetData().ForecastTaskInfoM.ForecastEvents() {
		eventm.Watch(evt, &forecastHandlerOnEvent)
	}
	//水晶祝福成就
	// for evt := range goxml.GetData().CrystalAchieveInfoM.Events() {
	// 	eventm.Watch(evt, &crystalAchieveHandlerOnEvent)
	// }
	// 迷宫任务
	for evt := range goxml.GetData().MazeTaskInfoM.MazeEvents() {
		eventm.Watch(evt, &mazeHandlerOnEvent)
	}

	// 战令任务
	for evt := range goxml.GetData().PassTaskInfoM.PassEvents() {
		eventm.Watch(evt, &passHandlerOnMainEvent)
	}
	// 活跃战令任务
	for evt := range goxml.GetData().PassCycleTaskInfoM.PassActiveEvents() {
		eventm.Watch(evt, &passHandlerOnCycleEvent)
	}
	// 推送礼包
	for _, evt := range goxml.GetData().ActivityPushGiftInfoM.AllEvents() {
		eventm.Watch(evt, &pushGiftHandlerOnEvent)
	}
	// 配置任务
	for _, evt := range goxml.GetData().ActivityTaskTypeInfoM.AllEvent() {
		eventm.Watch(evt, &operateTaskOnEvent)
	}
	// 悬赏等级升级
	for _, evt := range goxml.GetData().DispatchLevelInfoM.AllEvent() {
		eventm.Watch(evt, &dispatchHandlerOnEvent)
	}
	// 契约之所激活
	/*for _, evt := range  goxml.GetData().GoddessContractInfoM.AllEvent() {
		eventm.Watch(evt, &goddessContractHandlerOnEvent)
	}*/
	// 神魔抽卡 - 升星任务
	for evt := range goxml.GetData().DivineDemonTaskStarInfoM.Events() {
		eventm.Watch(evt, &divineDemonStarHandlerOnEvent)
	}

	// 神魔抽卡 - 累抽任务
	for evt := range goxml.GetData().DivineDemonTaskSummonInfoM.Events() {
		eventm.Watch(evt, &divineDemonSummonHandlerOnEvent)
	}
	//// 神魔抽卡 - 活跃任务
	//for evt := range  goxml.GetData().DivineDemonTaskActiveInfoM.Events() {
	//	eventm.Watch(evt, &divineDemonActiveHandlerOnEvent)
	//}

	// 全民无双
	for evt := range goxml.GetData().ActivityMonthTasksInfoM.GetAllEvents() {
		eventm.Watch(evt, &monthTasksHandlerOnEvent)
	}

	//神器首发 - 成就任务
	for _, evt := range goxml.GetData().ArtifactDebutTaskInfoM.AchieveEvents() {
		eventm.Watch(evt, &artifactDebutHandlerOnAchieveEvent)
	}

	//神器首发 - 每日任务
	for _, evt := range goxml.GetData().ArtifactDebutTaskInfoM.DailyEvents() {
		eventm.Watch(evt, &artifactDebutHandlerOnDailyEvent)
	}

	// 调整number 次数上限
	for evt := range goxml.GetData().NumberMaxAddTaskInfoM.Events() {
		eventm.Watch(evt, &numberAddMaxHandlerOnEvent)
	}

	//新服轮次活动
	for _, evt := range goxml.GetData().RoundActivityTaskInfoM.Events() {
		eventm.Watch(evt, &roundActivityHandlerOnEvent)
	}

	//百塔任务
	for _, evt := range goxml.GetData().TowerSeasonTaskInfoM.AllEvents() {
		eventm.Watch(evt, &TowerSeasonHandlerOnEvent)
	}

	// 功勋女武神治疗任务
	for evt := range goxml.GetData().MedalSkinInfoM.MedalGoddessEvents() {
		eventm.Watch(evt, &medalGoddessHandlerOnEvent)
	}

	// 世界boss任务
	for evt := range goxml.GetData().WorldbossTaskInfoM.Events() {
		eventm.Watch(evt, &worldBossHandlerOnEvent)
	}

	// 赛季等级任务
	for evt := range goxml.GetData().SeasonLevelTaskInfoM.Events() {
		eventm.Watch(evt, &seasonLevelHandlerOnEvent)
	}

	// 赛季主线任务
	for evt := range goxml.GetData().SeasonDungeonRewardInfoM.AllEvent() {
		eventm.Watch(evt, &SeasonDungeonEvent)
	}

	// 金字塔活动任务
	for evt := range goxml.GetData().ActivityPyramidTaskInfoM.Events() {
		eventm.Watch(evt, &pyramidHandlerOnEvent)
	}

	//赛季竞技场任务
	for evt := range goxml.GetData().SeasonArenaTaskInfoM.Events() {
		eventm.Watch(evt, &SeasonArenaOnEvent)
	}

	for _, evt := range goxml.GetData().ActivityTurntableTaskInfoM.AllEvents() {
		eventm.Watch(evt, &activityTurnTableHandlerOnEvent)
	}

	for evt := range goxml.GetData().ActivityLifelongGiftInfoM.GetAllEvent() {
		eventm.Watch(evt, &activityLifeLongGiftsOnEvent)
	}

	activitySumEvent := make(map[uint32]struct{})
	if character.Event2ActivityType == nil {
		character.Event2ActivityType = make(map[uint32][]uint32)
	}
	for actType, v := range character.ActivitySumFuncManager {
		if v == nil || v.WatchEvent == nil {
			continue
		}
		events := v.WatchEvent()
		if len(events) > 0 {
			for _, evt := range events {
				activitySumEvent[evt] = struct{}{}
				if !slices.Contains(character.Event2ActivityType[evt], actType) {
					character.Event2ActivityType[evt] = append(character.Event2ActivityType[evt], actType)
				}
			}
		}
	}
	for evt := range activitySumEvent {
		eventm.Watch(evt, &ActivitySumEvent)
	}

	for evt := range goxml.GetData().ActivityCouponTaskInfoM.GetAllEvent() {
		eventm.Watch(evt, &ActivityXiao7CouponEvent)
	}

	//更新全局排行榜
	eventm.Watch(aevent.IeLevelToX, &updateLevelRank)
	eventm.Watch(aevent.IePowerToX, &onPowerChange)

	//阵容战力变化
	eventm.Watch(aevent.IeDefensePowerChange, &onDefensePowerChange)
	eventm.Watch(aevent.IeSeasonPowerChange, &onSeasonPowerChange)

	//永恒仪式
	// eventm.Watch(aevent.IeRiteRareUp, &onRiteRareUp)
	eventm.Watch(aevent.IeAllRitesCleared, &onAllRitesCleared)

	// 公会竞赛任务
	for _, evt := range goxml.GetData().GuildMobilizationTaskInfoM.AllEvents() {
		eventm.Watch(evt, &guildMobOnEvent)
	}

	// Boss挑战任务
	for evt := range goxml.GetData().BossRushTaskInfoM.GetAllEvent() {
		eventm.Watch(evt, &bossRushHandlerOnEvent)
	}

	// 天赋树任务
	for evt := range goxml.GetData().SeasonTalentTreeTaskAwardInfoM.GetAllEvent() {
		eventm.Watch(evt, &seasonTalentTreeHandlerOnEvent)
	}

	// 赛季开门任务
	for evt := range goxml.GetData().SeasonDoorTaskInfoM.AllEvent() {
		eventm.Watch(evt, &SeasonDoorEvent)
	}

	// 冲榜任务领奖
	for _, evt := range goxml.GetData().ActivityRankTimelimitInfoM.GetAllEvent() {
		eventm.Watch(evt, &ComplianceTasksEvent)
	}

	// 赛季地图任务
	for evt := range goxml.GetData().SeasonMapTaskInfoM.AllEvent() {
		eventm.Watch(evt, &SeasonMapEvent)
	}

	//宠物爬塔
	for _, evt := range goxml.GetData().TowerPokemonTaskInfoM.AllEvents() {
		eventm.Watch(evt, &TowerPokemonHandlerOnEvent)
	}
}

// 成就和每日,每周任务的Event处理函数
var achieveHandlerOnEvent = func(evt event.Eventer, _ interface{}) {
	commonEvent := evt.(aevent.CommonEvent)

	user := commonEvent.User.(*character.User)
	user.Achieve().OnAchieveEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var dailyHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.DailyTask().OnDailyEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var weeklyHandlerOnEvent = func(evt event.Eventer, _ interface{}) {
	commonEvent := evt.(aevent.CommonEvent)

	user := commonEvent.User.(*character.User)
	user.WeeklyTask().OnWeeklyEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var monthlyHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.MonthlyTask().OnMonthlyEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var lineHandlerOnEvent = func(evt event.Eventer, _ interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	user.LineTask().OnEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var carnivalHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	//srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.Carnival().OnCarnivalEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var medalHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	//srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.Medal().OnMedalEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var forecastHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	//srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.Forecast().OnForecastEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

// var crystalAchieveHandlerOnEvent = func(evt event.Eventer, param interface{}) {
// 	commonEvent := evt.(aevent.CommonEvent)
// 	user := commonEvent.User.(*character.User)
// 	user.Crystal().OnCrystalAchieveEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
// }

var mazeHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	//srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.MazeM().OnMazeEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var passHandlerOnMainEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.Pass().OnPassMainEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var passHandlerOnCycleEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.Pass().OnPassCycleEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var pushGiftHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.PushGift().OnPushGiftEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var operateTaskOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.OperateActivityM().OnOperateTaskEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var dispatchHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.Dispatch().OnDispatchEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

/*var goddessContractHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.GoddessContract().DungeonIDWatch(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}*/

var divineDemonStarHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.DivineDemon().OnStarEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var divineDemonSummonHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.DivineDemon().OnSummonEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

//var divineDemonActiveHandlerOnEvent = func(evt event.Eventer, param interface{}) {
//	commonEvent := evt.(aevent.CommonEvent)
//	srv := param.(character.Servicer)
//	user := commonEvent.User.(*character.User)
//
//	user.DivineDemon().onActiveEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
//}

var monthTasksHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.MonthTasks().OnEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var artifactDebutHandlerOnAchieveEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.ArtifactDebutM().OnEvent(commonEvent.Event, goxml.ArtifactDebutTaskTypeAchieve, commonEvent.Value, commonEvent.Values, srv)
}

var artifactDebutHandlerOnDailyEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.ArtifactDebutM().OnEvent(commonEvent.Event, goxml.ArtifactDebutTaskTypeDaily, commonEvent.Value, commonEvent.Values, srv)
}

var numberAddMaxHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	//srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)

	user.OnNumberAddMaxEvent(commonEvent.Event, commonEvent.Value)
}

var roundActivityHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)

	user.RoundActivityM().OnEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var TowerSeasonHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.TowerSeason().OnTowerSeasonEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var activityTurnTableHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.ActivityTurnTable().OnActivityTurnEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var activityLifeLongGiftsOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.ActivityLifelongGifts().OnActivityLifeLongGiftsEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var seasonLevelHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.SeasonLevel().OnSeasonLevelEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var pyramidHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.Pyramid().OnPyramidEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var SeasonDungeonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.SeasonDungeon().OnSeasonDungeonEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var SeasonArenaOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.SeasonArenaTask().OnSeasonArenaTask(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var ActivitySumEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.ActivitySumM().OnActivityTurnEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var ActivityXiao7CouponEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.ActivityXiao7Coupon().OnActivityTask(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

// 操作cache数据时候使用
var doSaveEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)

	user := commonEvent.User.(*character.User)
	user.Save(srv)
}

var doCreateEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//账号创建时，初始资源
	info := goxml.GetData().GameInitialInfoM.Datas
	awards := make([]*cl.Resource, 0, len(info))
	for _, v := range info {
		tmp := &cl.Resource{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count,
		}
		awards = append(awards, tmp)
	}
	if len(awards) > 0 {
		user.Award(srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_REG_ADD_RESOURCE), 0)
	}
}

var doLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	oldLevel := commonEvent.Value
	if oldLevel > 0 {
		awards := goxml.GetData().PlayerLevelInfoM.GetLevelUpAwards(user.ID(), uint32(oldLevel), user.Level())
		if len(awards) == 0 {
			l4g.Errorf("user %d doLevelUpEvent: no awards, oldLevel:%d newLevel:%d",
				user.ID(), oldLevel, user.Level())
			return
		}

		user.Award(srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_PLAYER_LEVELUP), 0)
	}

	srv.UserM().ChangeOnlineLevelUser(user.ID(), uint32(oldLevel), user.Level())
	user.PutRoleToAccount(srv, uint32(log.SUB_TYPE_ID_ACCOUNT_LEVEL))
}
var updateLevelRank = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	rk := srv.GetGlobalRank(rank.LEVEL)
	rk.Update(&rank.LevelValue{
		ID:    user.ID(),
		Level: user.Level(),
		Tm:    user.LevelTm(),
	})
}

var onPowerChange = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	power := commonEvent.Value

	now := time.Now().Unix()
	rk := srv.GetGlobalRank(rank.POWER)
	rk.Update(&rank.PowerValue{
		ID:    user.ID(),
		Power: int64(power),
		Tm:    now,
	})

	//战力排行榜
	rankInfo := goxml.GetData().RankingInfoM.Index(goxml.PowerRankId)
	if rankInfo != nil && user.Level() >= rankInfo.Level {
		srv.CommonRankM().Insert(goxml.PowerRankId,
			rank.NewUserPower(user.ID(), power, time.Now().Unix()))
		//检查更新排行成就
		srv.RankAchieveM().CheckAndUpdate(srv, goxml.PowerRankId, user.ID(), int64(power))
	}
	//战力任务
	user.FireCommonEvent(srv.EventM(), aevent.AePowerToX, power)
}

var doNewHeroEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//账号创建时，初始资源
	sysID := commonEvent.Values[0]
	star := commonEvent.Values[1]
	reason := commonEvent.Values[2] //新英雄获取途径
	hid := commonEvent.Value

	heroManager := user.HeroManager()
	hero := heroManager.Get(hid)
	if hero != nil {
		heroManager.NewHeroUpdateTag(srv, hero)
		user.UpdateAllPower(srv, character.PowerUpdateByNormalRaise)
	}

	//更新任务
	if reason != uint32(log.RESOURCE_CHANGE_REASON_HERO_BACK) {
		user.FireCommonEvent(srv.EventM(), aevent.AeStarHeroToX, 1, star)
	}

	info := goxml.GetData().HeroInfoM.Index(sysID)
	if info == nil {
		l4g.Errorf("doNewHeroEvent: heroInfo not exist. uid:%d sysID:%d", user.ID(), sysID)
		return
	}

	// 同名英雄算一次，利用图鉴处理
	if user.IsFirstGetHero(sysID) {
		if info.Link1ID > 0 {
			user.FireCommonEvent(srv.EventM(), aevent.AeGetSomeLinkHeroToX, 1, info.Link1ID)
		}

		//avatar
		avatarRes := goxml.GetData().AvatarInfoM.GetAvatarResourceByNewHero(sysID)
		if len(avatarRes) > 0 {
			user.Award(srv, avatarRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_NEW_HANDBOOK_HERO), 0)
		}

		if info.ShowManual {
			user.HandbookManager().HandleNewHandbook(uint32(common.HANDBOOK_HT_HERO), sysID)
		}
	}

	if info.Special == goxml.HeroCommon {
		//处理水晶相关任务
		user.HeroManager().CheckHeroesForCrystalTask(srv, star, info.Race, info.Id)
	}

	if reason != uint32(log.RESOURCE_CHANGE_REASON_HERO_BACK) {
		seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
			{
				StageID: goxml.DivineHeroStageID,
				param:   hero,
			}, {
				StageID: goxml.DivineSeasonHeroStageID,
				param:   hero,
			}, {
				StageID: goxml.NormalSeasonHeroStageID,
				param:   hero,
			},
		})
	}
}

// 接取悬赏任务
var doReceiveDispatchTask = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	// 任务品质
	taskQuality := commonEvent.Values[0]
	// 任意品质的任务
	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeDispatchQualityX), commonEvent.Value, taskQuality)
	// 紫色及以上的任务
	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeDispatchVioletX), commonEvent.Value, taskQuality)
}

var doHeroUpdateMaxLvEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeHeroLevelToX, commonEvent.Value)
}

var doHeroStarUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	targetStar := commonEvent.Values[0]
	race := commonEvent.Values[1]
	sysID := commonEvent.Values[2]

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeUpgradeStarTimes, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeStarHeroToX, commonEvent.Value, targetStar)

	//处理水晶相关任务
	user.HeroManager().CheckHeroesForCrystalTask(srv, targetStar, race, sysID)

	// 处理英雄图鉴的升星属性
	user.HandbookManager().GetHeroHandbookM().UpdateStarAttr(srv, sysID, targetStar)
}

var doNewEmblemEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	// 任务品质
	rare := commonEvent.Values[0]
	infos := goxml.GetData().TaskTypeInfoM.IndexEvent(aevent.AeHaveFixedRareEmblemToX)
	for _, info := range infos {
		if info.Parameter1 <= rare {
			user.FireCommonEvent(srv.EventM(), uint32(aevent.AeHaveFixedRareEmblemToX), commonEvent.Value, info.Parameter1)
		}
	}
}

//// 触发迷宫的古文残卷事件
//var doTriggerScrollEvent = func(evt event.Eventer, param interface{}) {
//	commonEvent := evt.(aevent.CommonEvent)
//	user := commonEvent.User.(*character.User)
//	srv := param.(character.Servicer)
//
//	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeTriggerScrollEventX), commonEvent.Value)
//}

// 爬塔通过新层
var doTowerNewFloorEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	winNum := uint64(commonEvent.Values[0])

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeTowerFloorToX, commonEvent.Value, uint32(winNum))
	user.FireCommonEvent(srv.EventM(), aevent.AeTowerFightOrSweepToX, winNum)
}

// 爬塔扫荡
var doTowerSweepEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeTowerFightOrSweepToX, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeTowerOnlySweepToX, commonEvent.Value)
}

// 主线 - 战斗胜利
var doDungeonFightWinEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//功能开启前提前初始化迷宫战力
	if uint32(commonEvent.Value) == goxml.MazeInitPowerDungeonID {
		user.SetFirstMazePower()
	}

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeDungeonToX, commonEvent.Value)
	if uint32(commonEvent.Value) == goxml.GetData().ConfigInfoM.GetSocietyMailPushLevel() {
		character.SocietyMail(srv, user)
	}
	now := time.Now().Unix()
	if user.RebaseCheck(now, srv) {
		pmsg := &p2l.L2P_RebaseGet{
			Uuid: user.UUID(),
			Id:   user.ID(),
		}
		srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_RebaseGet), user.ID(), pmsg)
	}
	funcInfo := goxml.GetData().FunctionInfoM.Index(uint32(common.FUNCID_MODULE_DAILY_ATTENDANCE_HERO))
	if funcInfo != nil && funcInfo.Dungeon == uint32(commonEvent.Value) && user.DailyAttendanceHero().LoginCountZero() {
		user.DailyAttendanceHero().AddLogin()
		user.SendCmdToGateway(cl.ID_MSG_L2C_DailyAttendanceGetData, &cl.L2C_DailyAttendanceHeroGetData{
			Ret:        uint32(ret.RET_OK),
			Attendance: user.DailyAttendanceHero().Flush(),
		})
	}
}

// 主线 - 加速挂机
var doDungeonSpeedOnhookEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeSpeedOnhook, commonEvent.Value)
}

// 刷新神秘商店
var doRefreshMysteryShopEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeRefreshMysteryShopToX, commonEvent.Value)
}

// 在神秘商店购买商品
var doBuyGoodsFromMysteryShopEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeBuyGoodsFromMysteryShopToX, commonEvent.Value)
}

// 竞技场战斗
var doArenaFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	isWin := commonEvent.Values[0]
	score := commonEvent.Values[1]
	rank := commonEvent.Values[2]

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeArenaFightToX, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeArenaSeasonFightToX, commonEvent.Value)
	if isWin == 1 {
		user.FireCommonEvent(srv.EventM(), aevent.AeArenaFightWinToX, 1)

		//段位任务
		info := goxml.GetData().ArenaDivisionInfoM.GetInfoByScoreAndRank(score, rank)
		if info != nil {
			user.FireCommonEvent(srv.EventM(), aevent.AeArenaDivisionToX, uint64(info.ID))
		}
	}
	user.FireCommonEvent(srv.EventM(), aevent.AeArenaScoreToX, uint64(score))
	if rank > 0 {
		user.FireCommonEvent(srv.EventM(), aevent.AeArenaRankToX, uint64(rank))
	}
}

// 个人boss战斗
var doMirageFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	isPass := commonEvent.Values[0]
	isSweep := commonEvent.Values[1]
	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeMirageWinToX, commonEvent.Value)
	if isPass == 1 {
		copyID, floor := commonEvent.Values[2], commonEvent.Values[3]
		user.FireCommonEvent(srv.EventM(), aevent.AeMiragePassFixedRaceFloorToX, uint64(floor), copyID)
		user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMiragePassToX), commonEvent.Value)
	}
	if isSweep == 1 {
		user.FireCommonEvent(srv.EventM(), aevent.AeMirageSweepToX, commonEvent.Value)
	}
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.MirageFightStageID,
			param:   uint32(commonEvent.Value),
		},
	})
}

// 密林种树
var doFlowerPlantEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	rare := commonEvent.Values[0]
	treeType := commonEvent.Values[1]

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeFlowerPlantToX, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeFlowerPlantYToX, 1, rare)
	user.FireCommonEvent(srv.EventM(), aevent.AeFlowerPlantTypeYToX, 1, treeType)
}

// 材料本战斗胜利
var doTrialFightWinEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	num := uint64(commonEvent.Values[0])
	trialType := commonEvent.Values[1]
	stars := uint64(commonEvent.Values[2])
	oldLevel := commonEvent.Values[3]
	newLevel := commonEvent.Values[4]
	if oldLevel != newLevel {
		user.Trials().ChangeLevelEvent(srv, trialType, oldLevel, newLevel)
	}
	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeAllTrialTotalStarToX, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeOneTrialStarToX, stars, trialType)
	user.FireCommonEvent(srv.EventM(), aevent.AeTrialFightOrSweepToX, num)
	user.FireCommonEvent(srv.EventM(), aevent.AeOneTrialPassToX, num, trialType)
}

// 材料本扫荡
var doTrialSweepEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	trialType := commonEvent.Values[0]
	//更新任务

	user.FireCommonEvent(srv.EventM(), aevent.AeTrialFightOrSweepToX, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeOneTrialPassToX, commonEvent.Value, trialType)
}

// 境界-开启了X个完整的回忆
var doNewMemoryEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	num := uint64(commonEvent.Values[0])

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeMemoryUnlockToX, num)
}

var doSummonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	group := commonEvent.Values[0]

	//更新任务
	switch group {
	case goxml.AdvancedSummon:
		user.FireCommonEvent(srv.EventM(), aevent.AeSummonCountToX, commonEvent.Value, goxml.AdvancedSummon) // 高级招募任务
		user.FireCommonEvent(srv.EventM(), aevent.AeSummonDailyAdvanceCount, commonEvent.Value)              //当日累计招募高级抽卡
	case goxml.BasicSummon:
		user.FireCommonEvent(srv.EventM(), aevent.AeSummonCountToX, commonEvent.Value, goxml.BasicSummon) // 普通招募任务
	case goxml.FriendshipSummon:
		user.FireCommonEvent(srv.EventM(), aevent.AeSummonCountToX, commonEvent.Value, 0)
	case goxml.ArtifactSummon:
		user.FireCommonEvent(srv.EventM(), aevent.AeArtifactSummonCountToX, commonEvent.Value, 0) // 神器召唤
	case goxml.AssocSummonEmpire, goxml.AssocSummonWoodland, goxml.AssocSummonEclipse, goxml.AssocSummonDemon:
		user.FireCommonEvent(srv.EventM(), aevent.AeAssocSummonCountToX, commonEvent.Value, 0) // 先知召唤
	}
}

var doGetNewArtifactEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	rare := commonEvent.Values[0] // 品质
	artifactId := commonEvent.Values[1]

	// 更新全局属性
	user.ArtifactManager().UpdateArtifactGlobalAttr()
	user.ArtifactManager().SendArtifactGlobalAttrToClient()
	// 更新英雄属性是否重新计算的标识
	user.HeroManager().SetHeroAttrChange()
	// 更新阵容战力
	user.UpdateAllPower(srv, character.PowerUpdateByNormalRaise)

	user.FireCommonEvent(srv.EventM(), aevent.AeHaveFixedRareArtifactToX, commonEvent.Value, rare) // 拥有多少rare品质的神器
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.RedArtifactStageID,
			param:   artifactId,
		}, {
			StageID: goxml.RedSeasonArtifactStageID,
			param:   artifactId,
		},
	})
}

var doArtifactStarUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) < 2 {
		l4g.Errorf("user:%d doArtifactStarUpEvent: values must be at least 2", user.ID())
		return
	}

	newStar := commonEvent.Values[0]
	artifactId := commonEvent.Values[1]

	user.FireCommonEvent(srv.EventM(), aevent.AeHaveFixedStarArtifactToX, commonEvent.Value, newStar)
	user.HandleSeasonAddChange(srv, goxml.SeasonAddHandbookArtifactStar, artifactId, newStar)
}

var doArtifactStrengthEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeArtifactStrengthMaxLevelToX, commonEvent.Value) //神器的历史最高强化等级
}

var doArtifactForgeEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeArtifactForgeNumToX, commonEvent.Value) // 神器累计突破次数
}

var doEquipEvolutionEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeEquipEvolutionNumToX, commonEvent.Value) // 装备累计进阶次数
}

var doEquipRefineEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeEquipRefineNumToX, commonEvent.Value) // 装备累计精炼次数
}

var doEquipEnchantEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeEquipEnchantNumToX, commonEvent.Value) // 装备累计附魔次数

}

var doEquipStrengthEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeEquipLevelToX, commonEvent.Value)

}

var doGuildDungeonFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)

	if len(commonEvent.Values) <= 0 {
		l4g.Errorf("doGuildDungeonFightEvent user:%d commonEvent.Values is error.", user.ID())
	}

	count := commonEvent.Values[0]

	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeGuildDungeonFight, uint64(count))
	user.FireCommonEvent(srv.EventM(), aevent.AeGuildDungeonOnceFightDamageToX, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeGuildDungeonTotalHurtToX, commonEvent.Value)

	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.GuildDungeonStageID,
			param:   uint32(count),
		},
	})
}

var doGuildDungeonChapterChangeEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	//user.FireCommonEvent(srv.EventM(), aevent.AeGuildDungeonChapterToX, commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), aevent.AeGuildDungeonMaxChapterToX, commonEvent.Value)
}

// 点金-累计购买金币的次数
var doGoldBuyGetGold = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeGoldBuyGetGoldToX, commonEvent.Value)
}

// 迷宫-累计击败敌人X次
var doMazeEnemyBattle = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	eventType := commonEvent.Values[0]
	user.FireCommonEvent(srv.EventM(), aevent.AeMazeEnemyBattleToX, commonEvent.Value)
	switch eventType {
	case goxml.MazeLittleMonster:
		user.FireCommonEvent(srv.EventM(), aevent.AeMazePassLittleMonsterTox, commonEvent.Value)
	case goxml.MazeCommonMonster:
		user.FireCommonEvent(srv.EventM(), aevent.AeMazePassCommonMonsterTox, commonEvent.Value)
	case goxml.MazeEliteMonster:
		user.FireCommonEvent(srv.EventM(), aevent.AeMazePassEliteMonsterTox, commonEvent.Value)
	case goxml.MazeDifficultMonster:
		user.FireCommonEvent(srv.EventM(), aevent.AeMazePassDifficultMonsterTox, commonEvent.Value)
	case goxml.MazeTreasuryGuard:
		user.FireCommonEvent(srv.EventM(), aevent.AeMazeTreasuryPassGuardTox, commonEvent.Value)
	}
}

var doNewGemEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	rare := commonEvent.Values[0]
	// 宝石品质
	infos := goxml.GetData().TaskTypeInfoM.IndexEvent(aevent.AeHaveFixedRareGemToX)
	for _, info := range infos {
		if info.Parameter1 <= rare {
			user.FireCommonEvent(srv.EventM(), uint32(aevent.AeHaveFixedRareGemToX), commonEvent.Value, info.Parameter1)
		}
	}
}

// 宝石-累计置换X次
var doGemConvert = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeGemConvertToX, commonEvent.Value)
}

// 宝石-累计合成X次
var doGemCompose = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeGemComposeToX, commonEvent.Value)
}

// 列传-强敌挑战
var doTalesEliteFight = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	win := commonEvent.Values[0]
	user.FireCommonEvent(srv.EventM(), aevent.AeTalesEliteFightOrWipeX, commonEvent.Value)
	if win == 1 {
		eliteID := commonEvent.Values[1]
		user.FireCommonEvent(srv.EventM(), aevent.AeTalesEliteToX, uint64(eliteID))
	}
}

// 列传-强敌扫荡
var doTalesEliteWipe = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeTalesEliteFightOrWipeX, commonEvent.Value)
}

// 在固定商店使用友情点购买商品
var doBuyGoodsFromFixedShopEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeUseFriendshipPointBuyGoodsFromFixedShopToX, commonEvent.Value)
}

// 激活X次图鉴
var doActiveHandbookEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	handbookType := commonEvent.Values[0]

	//更新任务
	if handbookType == uint32(common.HANDBOOK_HT_FETTER) {
		user.FireCommonEvent(srv.EventM(), aevent.AeActiveHeroFetterToX, commonEvent.Value)
	}
	//else if handbookType == uint32(common.HANDBOOK_HT_HERO) {
	//user.FireCommonEvent(srv.EventM(), aevent.AeActiveHandbookOfHeroToX, commonEvent.Value)
	//}
}

// 任意商店购买X次商品
var doBuyGoodsFromAnyShopEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeBuyGoodsFromAnyShopToX, commonEvent.Value)
}

// 分解X品质的英雄X次
var doDecomposeHeroEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeDecomposeHeroToX), commonEvent.Value)
}

// 分解X品质的装备X次
var doDecomposeEquipEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeDecomposeEquipToX), commonEvent.Value)
}

// 竞技场点赞X次
var doArenaLikeEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeArenaLikeToX), commonEvent.Value)
}

// 领取成就排行榜奖励X次
var doRankAchieveReceiveAwardEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeRankAchieveReceiveAwardToX), commonEvent.Value)
}

// 功勋等级达到X级
var doMedalLevelEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMedalLevelToX), commonEvent.Value)
}

// 领取功勋每日奖励X次
var doMedalDailyAwardEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMedalDailyAwardToX), commonEvent.Value)
}

var doReceiveDailyAwardEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeReceiveDailyAwardToX), commonEvent.Value)
}

var doReceiveDailyAwardBoxEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeReceiveDailyAwardBoxToX), commonEvent.Value)
}

var doDispatchRefreshEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeDispatchRefreshToX), commonEvent.Value)
}

var doHeroStageEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeHeroStageToX), commonEvent.Value)
}

var doGuildSignInEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeGuildSignInToX), commonEvent.Value)
}

var doGuildDungeonRecvBoxEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//user.FireCommonEvent(srv.EventM(), uint32(aevent.AeGuildDungeonRecvBoxToX), commonEvent.Value)
	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeGuildDungeonOpenBossBoxToX), commonEvent.Value)
}

//var doGuildTalentLevelUpEvent = func(evt event.Eventer, param interface{}) {
//	commonEvent := evt.(aevent.CommonEvent)
//	user := commonEvent.User.(*character.User)
//	srv := param.(character.Servicer)
//	job := commonEvent.Values[0] // 职业
//
//	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeGuildTalentLevelUpToX), commonEvent.Value)
//	if job == goxml.HeroJobAll { // 全职业线
//		user.FireCommonEvent(srv.EventM(), uint32(aevent.AeGuildTalentAllJobToX), commonEvent.Value)
//	} else { // 其他职业线
//		user.FireCommonEvent(srv.EventM(), uint32(aevent.AeGuildTalentAnyJobToX), commonEvent.Value)
//	}
//}

var doTrialReceiveStarAwardEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeTrialReceiveStarAwardToX), commonEvent.Value)
}

var doHeroChangeEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeHeroChangeToX), commonEvent.Value)
}

var doMazeReceiveAwardEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMazeReceiveAwardToX), commonEvent.Value)
}

var doTalesFinishEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeTalesFinishToX), commonEvent.Value)
}

//var doMiragePassEvent = func(evt event.Eventer, param interface{}) {
//	commonEvent := evt.(aevent.CommonEvent)
//	user := commonEvent.User.(*character.User)
//	srv := param.(character.Servicer)
//
//	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMiragePassToX), commonEvent.Value)
//}

var doFlowerLootEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeFlowerLootToX), commonEvent.Value)
}

/*var doForesUpdateSlotEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeForestUpdateSlotToX), commonEvent.Value)
}*/

var doEmblemBlessingEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeEmblemBlessingToX), commonEvent.Value)
}

var doMazeUseLifeStreamEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMazeUseLifeStreamToX), commonEvent.Value)
}

var doMazeUseHeroReviveEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMazeUseHeroReviveToX), commonEvent.Value)
}

var doMazeUseRuleSwordEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeMazeUseRuleSwordToX), commonEvent.Value)
}

var doMazeBossLevelEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeMazeBossFightWinTox, 1)
	user.FireCommonEvent(srv.EventM(), aevent.AeMazePassBossLevelToX, commonEvent.Value)
}

var doFlowerLevelEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeFlowerLevelToX, commonEvent.Value, commonEvent.Values[0])
}

var FirstStarTowerEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeFirstStarTowerToX, commonEvent.Value)
}

var doSetNameEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.PutRoleToAccount(srv, uint32(log.SUB_TYPE_ID_SET_NAME))
	if user.IsFunctionOpen(uint32(common.FUNCID_MODULE_CHAT), srv) {
		character.ChatSyncUserToPlatform(srv, user)
	}

}

var doSetIconEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.PutRoleToAccount(srv, uint32(log.SUB_TYPE_ID_SET_ICON))
	if user.IsFunctionOpen(uint32(common.FUNCID_MODULE_CHAT), srv) {
		character.ChatSyncUserToPlatform(srv, user)
	}
}

// 水晶 - 缔约更新
var doCrystalContractUpdateEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeCrystalContractHeroLevelToX), commonEvent.Value)
}

// 水晶 - 解锁共鸣格位
// var doCrystalUnlockSlotEvent = func(evt event.Eventer, param interface{}) {
// 	commonEvent := evt.(aevent.CommonEvent)
// 	user := commonEvent.User.(*character.User)
// 	srv := param.(character.Servicer)

// 	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeCrystalUnlockSlotToX), commonEvent.Value)
// }

// 水晶 - 祝福升级
// var doCrystalBlessingLevelUpEvent = func(evt event.Eventer, param interface{}) {
// 	commonEvent := evt.(aevent.CommonEvent)
// 	user := commonEvent.User.(*character.User)
// 	srv := param.(character.Servicer)

// 	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeCrystalBlessingLevelToX), commonEvent.Value)
// }

/*var PassPointEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	passID := commonEvent.Values[0] // 战令ID

	event := goxml.GetData().PassTaskInfoM.GetEventsByPassID(passID)
	if event > 0 {
		user.FireCommonEvent(srv.EventM(), event, commonEvent.Value, passID)
	}
}*/

var RechargeAfterEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	orderAmount := commonEvent.Values[0]

	user.DailyAttendance().UpdateSecondAwardTag()
	user.DailyWishM().CalcSummonCount(srv, orderAmount)
	user.OperateActivityM().RechargeInitGift(srv)
	user.FireCommonEvent(srv.EventM(), aevent.AeRecharge, uint64(orderAmount))
}

var CostDiamondEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeCostDiamondToX, commonEvent.Value)
}

var SummonTrueFiveHero = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeSummonTrueFiveHero, commonEvent.Value)
}

var CompleteDispatchTask = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	// 任务品质
	taskQuality := commonEvent.Values[0]

	user.FireCommonEvent(srv.EventM(), aevent.AeCompleteDispatchToX, commonEvent.Value, taskQuality)
}

var TowerStartFight = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeTowerStartFightToX, commonEvent.Value)
}

var TowerStarStartFight = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeTowerStarStartFightToX, commonEvent.Value)
}

var BuyNumberTypeCount = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	numberType := commonEvent.Values[0]
	// 竞技场挑战次数
	switch numberType {
	case uint32(common.PURCHASEID_ARENA_FIGHT_COUNT):
		user.FireCommonEvent(srv.EventM(), aevent.AeBuyNumberTypeCountToX, commonEvent.Value, numberType)
	case uint32(common.PURCHASEID_TRIAL_GOLD_COUNT), uint32(common.PURCHASEID_TRIAL_EXP_COUNT),
		uint32(common.PURCHASEID_TRIAL_HERO_COUNT), uint32(common.PURCHASEID_TRIAL_EQUIP_COUNT),
		uint32(common.PURCHASEID_TRIAL_EMBLEM_COUNT):
		user.FireCommonEvent(srv.EventM(), aevent.AeTrialBuyBattleCountToX, commonEvent.Value)             // 任意材料本
		user.FireCommonEvent(srv.EventM(), aevent.AeTrialBuyBattleCountToX, commonEvent.Value, numberType) // 指定材料本
	}
}

var ChatEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeWorldChatToX, commonEvent.Value)
}

var ShopBuyItem = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	if len(commonEvent.Values) > 1 {
		shopID := commonEvent.Values[0]
		itemID := commonEvent.Values[1]

		switch shopID {
		case uint32(common.SHOPID_GENERAL_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeGeneralShopBuyToX, commonEvent.Value, itemID)
		case uint32(common.SHOPID_ARENA_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeArenaShopBuyToX, commonEvent.Value, itemID)
		case uint32(common.SHOPID_GUILD_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeGuildShopBuyToX, commonEvent.Value, itemID)
		case uint32(common.SHOPID_PROPHET_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeProphetShopBuyToX, commonEvent.Value, itemID)
		case uint32(common.SHOPID_ARTIFACT_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeArtifactShopBuyToX, commonEvent.Value, itemID)
		case uint32(common.SHOPID_GEM_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeFlowerShopBuyToX, commonEvent.Value, itemID)
		case uint32(common.SHOPID_EMBLEM_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeMirageShopBuyToX, commonEvent.Value, itemID)
		case uint32(common.SHOPID_SEASON_MAP_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeSeasonMapShopBuyItemX, commonEvent.Value, itemID)
		}
	}
}

var ShopBuyAny = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) > 0 {
		shopID := commonEvent.Values[0]
		// 任意商店购买
		user.FireCommonEvent(srv.EventM(), aevent.AeAnyShopBuyToX, commonEvent.Value)

		switch shopID {
		case uint32(common.SHOPID_GENERAL_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeGeneralShopBuyAny, commonEvent.Value)
		case uint32(common.SHOPID_ARENA_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeArenaShopBuyToX, commonEvent.Value)
		case uint32(common.SHOPID_GUILD_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeGuildShopBuyToX, commonEvent.Value)
		case uint32(common.SHOPID_PROPHET_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeProphetShopBuyToX, commonEvent.Value)
		case uint32(common.SHOPID_ARTIFACT_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeArtifactShopBuyToX, commonEvent.Value)
		case uint32(common.SHOPID_GEM_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeFlowerShopBuyToX, commonEvent.Value)
		case uint32(common.SHOPID_EMBLEM_SHOP):
			user.FireCommonEvent(srv.EventM(), aevent.AeMirageShopBuyToX, commonEvent.Value)
		}
	}
}

var DivineDemonSummonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	user.FireCommonEvent(srv.EventM(), aevent.AeDivineDemonSummonToX, commonEvent.Value)
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.DivineSummonStageID,
			param:   uint32(commonEvent.Value),
		},
	})
}

var ArenaShopBuyEquip = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) > 0 {
		equipID := commonEvent.Values[0]

		equipInfo := goxml.GetData().EquipInfoM.Index(equipID)
		if equipInfo == nil {
			return
		}

		user.FireCommonEvent(srv.EventM(), aevent.AeArenaShopBuyEquipToX, commonEvent.Value, equipInfo.Rare)

	}
}

var DecomposeRareEquip = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) > 0 {
		equipRare := commonEvent.Values[0]
		user.FireCommonEvent(srv.EventM(), aevent.AeDecomposeRareEquipToX, commonEvent.Value, equipRare)
	}
}

var WrestleFight = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeWrestleFightCountToX, commonEvent.Value)
	if len(commonEvent.Values) >= 2 {
		user.FireCommonEvent(srv.EventM(), aevent.AeWrestleInAppointLevelGetScoreToX, uint64(commonEvent.Values[0]), commonEvent.Values[1])
	} else {
		l4g.Errorf("user: %d event wrestleFight: commonEvent.Values num error. num: %d",
			user.ID(), len(commonEvent.Values))
	}
}

var GoddessContactLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessContractLevelUpTox, commonEvent.Value)
}

var GoddessTouchEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessTouchToX, commonEvent.Value)
}

var GoddessFeedEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessFeedToX, commonEvent.Value)
}

var GoddessTouchOrFeedEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessTouchOrFeedToX, commonEvent.Value)
}

var GoddessUnlockEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessUnlockTox, commonEvent.Value)
}

var GoddessLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessLevelUpToX, commonEvent.Value)
}

var GoddessStoryFinishEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessStoryFinishToX, commonEvent.Value)
}

// 主线 - 钻石加速挂机
var doDungeonSpeedOnhookCostEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeSpeedOnhookCost, commonEvent.Value)
}

var ArtifactSummonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	//
	Rare := commonEvent.Values[0]

	user.FireCommonEvent(srv.EventM(), aevent.AeArtifactSummon, commonEvent.Value, Rare)
}

var ArtifactSummonPointEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	//
	Rare := commonEvent.Values[0]

	user.FireCommonEvent(srv.EventM(), aevent.AeArtifactSummonPoint, commonEvent.Value, Rare)
}

var EquipmentCultivateEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeEquipmentCultivate, commonEvent.Value)
}

var EmblemCultivateEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeEmblemCultivate, commonEvent.Value)
}

var GuildTalentLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	if len(commonEvent.Values) == 2 {
		floor := commonEvent.Values[0]
		kind := commonEvent.Values[1]
		if floor == goxml.GuildTalentFloorFirst || floor == goxml.GuildTalentFloorSecond {
			user.FireCommonEvent(srv.EventM(), aevent.AeGuildTalentLevelToX, commonEvent.Value, kind)
			user.FireCommonEvent(srv.EventM(), aevent.AeGuildTalentLevelUpToX, 1)
		}
	}
}

var GuildDungeonBuyChallengeTimesEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGuildDungeonBuyChallengeTimesToX, commonEvent.Value)
}

var doHeroGemLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	newLevel := uint32(commonEvent.Value)
	slot := commonEvent.Values[0]
	oldLevel := commonEvent.Values[1]
	if oldLevel == 0 && newLevel > 0 {
		user.FireCommonEvent(srv.EventM(), aevent.AeHeroGemActivationToX, 1)
	}
	gemUpgradeInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(slot, newLevel)
	if gemUpgradeInfo != nil {
		user.FireCommonEvent(srv.EventM(), aevent.AeHeroGemFixedRareToX, 1, gemUpgradeInfo.Rare)
	}
	if newLevel > oldLevel {
		user.FireCommonEvent(srv.EventM(), aevent.AeHeroGemLevelUpCountToX, uint64(newLevel-oldLevel))
	}
}

var doArtifactDebutSummonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeArtifactDebutSummon, commonEvent.Value)
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.ArtifactDebut,
			param:   uint32(commonEvent.Value),
		},
	})
}

var doLinkSummonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeLinkSummon, commonEvent.Value)
}

var doGuildDonateEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGuildDonateToX, commonEvent.Value)
}

var doTowerSeasonWinEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeTowerSeasonToX, commonEvent.Value)
}

var medalGoddessHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	//srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.Medal().OnMedalGoddessEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var goddessRecoveryEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeGoddessRecoveryToX, commonEvent.Value)
}

var DailyAttendanceExtraAwardAfterEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if commonEvent.Value > 0 {
		user.FireCommonEvent(srv.EventM(), aevent.AeDailyAttendanceExtraAward, commonEvent.Value)
	} else {
		user.FireCommonEvent(srv.EventM(), aevent.AeDailyAttendanceCircleAward, 1)
	}
}

var BattleFailedEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeBattleFailedToX, commonEvent.Value)
}

var worldBossHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.WorldBoss().OnTaskEvent(srv, commonEvent.Event, commonEvent.Value, commonEvent.Values)
}

var worldBossFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeWorldBossFightToX, commonEvent.Value)
}

var worldBossWorshipEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeWorldBossWorshipToX, commonEvent.Value)
}

var worldBossShopBuyEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	shopType := commonEvent.Values[0]

	switch common.SHOPID(shopType) {
	case common.SHOPID_WORLD_BOSS_SHOP:
		user.FireCommonEvent(srv.EventM(), aevent.AeWorldBossShopBuyToX, commonEvent.Value)
	case common.SHOPID_ARTIFACT_WORLD_BOSS_SHOP:
		user.FireCommonEvent(srv.EventM(), aevent.AeArtifactWorldBossShopBuyToX, commonEvent.Value)
	case common.SHOPID_POKEMON_WORLD_BOSS_SHOP:
		user.FireCommonEvent(srv.EventM(), aevent.AePokemonWorldBossShopBuyToX, commonEvent.Value)
	}

}

var wrestleLikeEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeWrestleLikeCountToX, commonEvent.Value)
}

var towerSeasonPassEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	if len(commonEvent.Values) > 0 {
		enemyType := commonEvent.Values[0]
		// 0：普通小怪
		if enemyType == 0 {
			user.FireCommonEvent(srv.EventM(), aevent.AeTowerSeasonCommonPassToX, commonEvent.Value)
		} else if enemyType == 2 { // 2: boss怪
			user.FireCommonEvent(srv.EventM(), aevent.AeTowerSeasonBossPassToX, commonEvent.Value)
		}
	}
}

var doNewSkinEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	skinID := commonEvent.Values[0] //皮肤ID
	duration := commonEvent.Value   //皮肤有效时间

	//仅永久皮肤时，才添加avatar
	if duration == 0 {
		avatarRes := goxml.GetData().AvatarInfoM.GetSkinAvatarRes(skinID)
		if len(avatarRes) > 0 {
			user.Award(srv, avatarRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_NEW_SKIN_AVATAR), 0)
		}
	}
}

// 活动故事主线 - 战斗胜利
var doActivityStoryDungeonFightWinEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) < 1 {
		l4g.Errorf("doActivityStoryDungeonFightWinEvent values error. user:%d", user.ID())
		return
	}

	progress := commonEvent.Value
	activityId := commonEvent.Values[0]
	//更新剧情回忆录进度
	user.StoryReview().UpdateStoryProgress(character.StoryTypeActivity, activityId, uint32(progress))
	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AEActivityStoryDungeonToX, progress)
}

// 活动故事扫荡
var doActivityStoryDungeonSweepEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AEActivityStorySweepX, commonEvent.Value)
}

// 百塔 - 挑战X次
var doTowerSeasonFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeTowerSeasonFightToX, commonEvent.Value)
}

// var onRiteRareUp = func(evt event.Eventer, param interface{}) {
// 	commonEvent := evt.(aevent.CommonEvent)
// 	user := commonEvent.User.(*character.User)
// 	srv := param.(character.Servicer)

// 	user.FireCommonEvent(srv.EventM(), aevent.AeRiteUpToX, uint64(1), uint32(commonEvent.Value))
// }

var onAllRitesCleared = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	//srv := param.(character.Servicer)
	user := commonEvent.User.(*character.User)
	user.FormationManager().OnAllRiteCleard()
}

var doSeasonLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.FireCommonEvent(srv.EventM(), aevent.AeSeasonLevelToX, uint64(user.GetSeasonLevel()))
	user.FireCommonEvent(srv.EventM(), aevent.AeSeasonLevelCountToX, uint64(user.GetSeasonLevel())-commonEvent.Value)
}

var doDisorderLandFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	num := commonEvent.Value // 战斗次数

	if len(commonEvent.Values) < 3 { //nolint:mnd
		l4g.Errorf("DisorderLandFightEvent values error. user:%d", user.ID())
		return
	}

	win := commonEvent.Values[0] == 1
	sweep := commonEvent.Values[1] == 1
	firstWin := commonEvent.Values[2] == 1

	if (win || sweep) && !firstWin {
		user.FireCommonEvent(srv.EventM(), aevent.AeDisorderLandFightNumToX, num)
	}
	if win && !sweep && firstWin {
		user.FireCommonEvent(srv.EventM(), aevent.AeDisorderLandFightWinToX, num)
	}
}

var doRiteUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) < 3 { //nolint:mnd
		l4g.Errorf("doRiteUpEvent values error. user:%d", user.ID())
		return
	}

	riteId := commonEvent.Values[0]
	nowRare := commonEvent.Values[1]
	oldRare := commonEvent.Values[2]

	infos := goxml.GetData().RiteRareInfoM.GetRiteRareInfos(riteId)

	if len(infos) == 0 {
		l4g.Errorf("doRiteUpEvent riteRareInfos not exist. user:%d riteId:%d", user.ID(), riteId)
		return
	}

	for rare := range infos {
		if rare <= oldRare {
			continue
		}

		if rare > nowRare {
			continue
		}

		user.FireCommonEvent(srv.EventM(), aevent.AeRiteUpToX, commonEvent.Value, rare)
	}
}

var doRiteMarkGetEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) < 2 {
		l4g.Errorf("doRiteMarkGetEvent values error. user:%d", user.ID())
		return
	}

	rare := commonEvent.Values[0]
	powerId := commonEvent.Values[1]

	user.FireCommonEvent(srv.EventM(), aevent.AeRiteMarkGetToX, commonEvent.Value, rare)
	if powerId > 0 {
		user.FireCommonEvent(srv.EventM(), aevent.AeRiteMarkWithPowerGetToX, commonEvent.Value)
	}
}

var doGetMonumentEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) < 2 {
		l4g.Errorf("doGetMonumentEvent values error. user:%d", user.ID())
		return
	}

	oldLevelUnique := commonEvent.Values[0]
	newLevelUnique := commonEvent.Values[1]

	for i := oldLevelUnique + 1; i <= newLevelUnique; i++ {
		user.FireCommonEvent(srv.EventM(), aevent.AeGetMonumentToX, commonEvent.Value, i)
	}
}

// 赛季主线 - 战斗胜利
var doSeasonDungeonFightWinEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	if len(commonEvent.Values) < 1 {
		l4g.Errorf("doSeasonDungeonFightWinEvent values error. user:%d", user.ID())
		return
	}
	progress := commonEvent.Value
	seasonId := commonEvent.Values[0]

	//更新剧情回忆录进度
	user.StoryReview().UpdateStoryProgress(character.StoryTypeSeason, seasonId, uint32(progress))
	//更新任务
	user.FireCommonEvent(srv.EventM(), aevent.AeSeasonDungeonToX, progress)
}

// 金字塔活动 - 抽奖
var doPyramidDrawEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	progress := commonEvent.Value

	//更新当期活动抽奖次数
	user.FireCommonEvent(srv.EventM(), aevent.AePyramidDrawNumToX, progress)
}

// 金字塔活动 - 单条协议抽奖次数
var doPyramidDrawNumEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.PyramidDrawNumStageID,
			param:   uint64(commonEvent.Value),
		},
	})
}

var doSeasonArenaFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	progress := commonEvent.Value
	user.FireCommonEvent(srv.EventM(), aevent.AeSeasonArenaFightToX, progress)
	if len(commonEvent.Values) > 0 && commonEvent.Values[0] == 1 {
		user.FireCommonEvent(srv.EventM(), aevent.AeSeasonArenaFightWinToX, progress)
	}
}

var doActivityTurnTableSummonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	progress := commonEvent.Value
	user.FireCommonEvent(srv.EventM(), aevent.AeActivityTurnTableSummonCount, progress)
}

var doHotRankUpdateEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	progress := commonEvent.Value
	if len(commonEvent.Values) == 0 {
		return
	}
	formationID := commonEvent.Values[0]

	//阵容ID
	insertRankInfo := goxml.GetData().HotRankInfoM.GetInsertRank(formationID, uint32(progress))
	if insertRankInfo == nil {
		return
	}

	r2lData := user.BuildHotRankUserFormation(formationID, uint32(progress))
	if r2lData == nil {
		return
	}

	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveHotRankUserFormation), user.ID(), &r2l.L2R_SaveHotRankUserFormation{Changes: []*db.LogicHotRankUserFormation{r2lData}})
}

// 玩家上线之后触发此事件，玩家模块数据在此初始化
var doOnlineEvent = func(evt event.Eventer, param interface{}) {
	event := evt.(aevent.CommonEvent)
	srv := param.(character.Servicer)

	fromCache := event.Value == 1 //判断是否是从缓存登录的
	l4g.Debugf("online event form cache %v", fromCache)

	user := event.User.(*character.User)
	user.SetContextID(srv.CreateLogID())
	//online三步骤
	//1. 初始化信息
	user.ChangeStateToOnline(srv)
	//2. 更新user modules信息
	user.ModulesOnline(srv, fromCache)
	//3. 更新系统信息
	user.OnlineInService(srv)
	//增加上线日志
	user.LogLogin(srv, 0)
	user.PutRoleToAccount(srv, uint32(log.SUB_TYPE_ID_ACCOUNT_ONLINE))
	user.SyncCrossAreaToChat(srv)
	//l4g.Debugf("online event:%s, %v", user.LogString(), event.Value)
	//触发任务类事件
	user.FireCommonEvent(srv.EventM(), aevent.AeOnline, 1)

	// 去掉推送频率限制
	srv.FlowerPushFrequencyLimitDelete(user.ID())
}

var guildMobOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(GetActivityService)
	guildM, ok := srv.GetActivity(activity.Guild).(*guild.Manager)
	if !ok {
		l4g.Errorf("user: %d guildManager not exist", user.ID())
		return
	}
	guildUser := guildM.GetGuildUser(user.ID())
	if guildUser != nil {
		guildUser.OnEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, param.(activity.Servicer), user)
	}
}

var doGstDonateEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	progress := commonEvent.Value
	user.FireCommonEvent(srv.EventM(), aevent.AeGstDonateToX, progress)
}

var bossRushHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.BossRush().OnEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var doBossRushFightEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	progress := commonEvent.Value
	user.FireCommonEvent(srv.EventM(), aevent.AeBossRushFightToX, progress)

	//赛季冲榜加积分
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.BossRushStageID,
			param:   uint32(commonEvent.Value),
		},
	})

	if len(commonEvent.Values) == 0 {
		return
	}

	bossGroup, bossLevel := commonEvent.Values[0], commonEvent.Values[1]
	if bossLevel == goxml.GetData().BossRushInfoM.GetInfiniteLevel() {
		l4g.Debugf("doBossRushFightEvent: bossLevel is infinite")
		return
	}

	// 推图到最高X级
	user.FireCommonEvent(srv.EventM(), aevent.AeBossRushPassToMaxLevelX, uint64(bossLevel))

	// 某个指定Boss最高到X级
	user.FireCommonEvent(srv.EventM(), aevent.AeBossRushBossDungeonToX, uint64(bossLevel), bossGroup)
}

var doBossRushBuyTimesEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	progress := commonEvent.Value
	user.FireCommonEvent(srv.EventM(), aevent.AeBossRushBuyTimesToX, progress)
}

var seasonTalentTreeHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.TalentTree().OnSeasonTalentTreeEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var doTalentTreeLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	newLevel := commonEvent.Value
	if len(commonEvent.Values) == 0 {
		return
	}
	oldLevel := uint64(commonEvent.Values[0])
	treeId := commonEvent.Values[1]

	baseInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(user.GetSeasonID(), treeId)
	if baseInfo == nil {
		return
	}

	if newLevel > oldLevel {
		user.FireCommonEvent(srv.EventM(), aevent.AeTalentTreeLevelUpTimesToX, newLevel-oldLevel)
	}

	if treeId == goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(user.GetSeasonID()) { // 根节点
		user.UpdateTalentTreeRootLevelRank(srv, uint32(newLevel))
		user.FireCommonEvent(srv.EventM(), aevent.AeTalentTreeRootLevelToX, newLevel)
	}

	talentTreeUpdateHot(treeId, uint32(oldLevel), uint32(newLevel), param, user)
	talentTreeSyncGst(treeId, user, param)
}

func talentTreeUpdateHot(nodeId, oldLevel, newLevel uint32, param interface{}, user *character.User) {
	srv, ok := param.(GetActivityService)
	if !ok {
		l4g.Errorf("user:%d talentTreeUpdateHot: param to GetActivityService error.", user.ID())
		return
	}

	if nodeId == goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(user.GetSeasonID()) {
		return
	}
	hotRankM, ok := srv.GetActivity(activity.HotRank).(*hotrank.Manager)
	if !ok {
		l4g.Errorf("user:%d TalentTreeLevelUp.UpdateHot: HotRank activity error.", user.ID())
		return
	}
	hotRankM.GetTalentTreeHotM().UpdateTalentTreeNodeLevel(nodeId, oldLevel, newLevel)
}

func talentTreeSyncGst(nodeId uint32, user *character.User, param interface{}) {
	baseInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(user.GetSeasonID(), nodeId)
	if baseInfo == nil {
		l4g.Errorf("user:%d talentTreeSyncGst.SyncGst: baseInfo is nil. seasonId:%d nodeId:%d",
			user.ID(), user.GetSeasonID(), nodeId)
		return
	}

	if baseInfo.NodeType != goxml.TalentTreeRoot { // 只有根节点变化需要同步公会战
		return
	}

	aSrv := param.(GetActivityService)
	srv := param.(character.Servicer)
	gstM, ok := aSrv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user:%d TalentTreeLevelUp.SyncGst: gst activity error.", user.ID())
		return
	}
	needSync := gstM.NeedSyncUserBaseInfo(user.ID(), true)
	if needSync {
		if gstM.IsCrossConnected() {
			syncInfo := user.CreateGstUserBaseInfo()
			if !srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTSYNCGuildUserInfo, user.ID(), syncInfo) {
				l4g.Errorf("user: %d TalentTreeLevelUp.ID_MSG_L2CS_GSTSYNCGuildUserInfo: cross maintain", user.ID())
			}
		}
	}
}

var onDefensePowerChange = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	//power := commonEvent.Value
	formationId := uint32(0)
	if len(commonEvent.Values) > 0 {
		formationId = commonEvent.Values[0]
	}

	//now := time.Now().Unix()

	if formationId == uint32(common.FORMATION_ID_FI_GST) {
		//gst布阵
		srv.SyncGstFormation(user)
	}
}

var onSeasonPowerChange = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	//power := commonEvent.Value

	//now := time.Now().Unix()

	//赛季竞技场
	srv.SyncSeasonArenaSnapshot(user)
	//同步工会战
	srv.SyncGstUserInfo(user)
}

var doRemainGetEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	progress := commonEvent.Value
	user.FireCommonEvent(srv.EventM(), aevent.AeRemainCollectToX, progress)
}

var doRemainStarUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	// 多少个遗物升星到X
	newStar := commonEvent.Values[0]
	infos := goxml.GetData().TaskTypeInfoM.IndexEvent(aevent.AeRemainNumStarUpToX)
	for _, info := range infos {
		if info.Parameter1 <= newStar {
			user.FireCommonEvent(srv.EventM(), uint32(aevent.AeRemainNumStarUpToX), commonEvent.Value, info.Parameter1)
		}
	}
}

var doDragonFightCountEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	fightCount := uint32(commonEvent.Value)

	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.DragonFightStageID,
			param:   fightCount,
		},
	})
}

var doArtifactFragmentEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	var count uint32
	fragmentID := uint32(commonEvent.Value)
	count = uint32(commonEvent.Values[0])
	if fragmentID > 0 && count > 0 {
		seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
			{
				StageID: goxml.RedArtifactFragmentStageID,
				param:   []uint32{fragmentID, count},
			}, {
				StageID: goxml.RedSeasonArtifactFragmentStageID,
				param:   []uint32{fragmentID, count},
			},
		})
	}
}

var doIeComplianceNewEmblemEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	emblemID := uint64(commonEvent.Value)
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.CommonThreeOrangeEmblemStageID,
			param:   uint64(emblemID),
		}, {
			StageID: goxml.ProtossDemonOrangeEmblemStageID,
			param:   uint64(emblemID),
		}, {
			StageID: goxml.CommonThreeRedEmblemStageID,
			param:   uint64(emblemID),
		}, {
			StageID: goxml.ProtossDemonRedEmblemStageID,
			param:   uint64(emblemID),
		}, {
			StageID: goxml.HaveMoreThanOrangeEmblemStageID,
			param:   uint64(emblemID),
		}, {
			StageID: goxml.HaveMoreThanRedEmblemStageID,
			param:   uint64(emblemID),
		},
	})

}

var doIeNewPokemonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.PokemonManager().UpdateAttrAndPower(srv)

	if len(commonEvent.Values) > 0 {
		pokemonID := uint64(commonEvent.Values[0])
		seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
			{
				StageID: goxml.PokemonRedStageID,
				param:   uint32(pokemonID),
			}, {
				StageID: goxml.PokemonSeasonRedStageID,
				param:   uint32(pokemonID),
			},
		})
	}

}

var doIePokemonFragmentEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	var count uint32
	fragmentID := uint32(commonEvent.Value)
	count = uint32(commonEvent.Values[0])
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.PokemonRedFragmentStageID,
			param:   []uint32{fragmentID, count},
		}, {
			StageID: goxml.PokemonSeasonRedFragmentStageID,
			param:   []uint32{fragmentID, count},
		},
	})
}

var doIePokemonSummonEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	summonCount := uint64(commonEvent.Value)
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.PokemonSummonStageID,
			param:   uint32(summonCount),
		},
	})
}

type CalcScoreFunc func(user *character.User, stageIDParam *StageIDCheckParam, stage uint32) *cl.ComplianceSourcePoint

func SeasonComplianceParamUint32Stage(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	fightCount, ok := stageIDParam.param.(uint32)
	if !ok {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score * uint32(fightCount),
	}
}

func SeasonComplianceParamDivineHero(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	hero, ok := stageIDParam.param.(*character.Hero)
	if !ok || hero == nil {
		l4g.Errorf("user:%d SeasonComplianceParamDivineHero convert hero:%v failed", user.ID(), hero)
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(hero.GetHeroSysID())
	if heroInfo != nil && (heroInfo.Race == goxml.RaceProtoss || heroInfo.Race == goxml.RaceDemon) && heroInfo.Rare >= goxml.RareRed {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamNormalSeasonHero(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	hero, ok := stageIDParam.param.(*character.Hero)
	if !ok || hero == nil {
		return nil
	}

	if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookHeroStar, hero.GetHeroSysID()) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(hero.GetHeroSysID())
	if heroInfo == nil {
		return nil
	}

	if (heroInfo.Race == goxml.RaceMoon || heroInfo.Race == goxml.RaceForest || heroInfo.Race == goxml.RaceEmpire) && heroInfo.Rare >= goxml.RareRed {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
			Expand:   heroInfo.Id,
		}
	}

	return nil
}

func SeasonComplianceParamDivineSeasonAddHero(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	hero, ok := stageIDParam.param.(*character.Hero)
	if !ok || hero == nil {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(hero.GetHeroSysID())
	if heroInfo == nil {
		return nil
	}

	if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookHeroStar, hero.GetHeroSysID()) {
		return nil
	}

	if (heroInfo.Race == goxml.RaceProtoss || heroInfo.Race == goxml.RaceDemon) && heroInfo.Rare >= goxml.RareRed {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
			Expand:   hero.GetHeroSysID(),
		}
	}

	return nil
}

func SeasonComplianceParamRedArtifactStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	artifactID, ok := stageIDParam.param.(uint32)
	if !ok {
		return nil
	}

	artifactInfo := goxml.GetData().ArtifactInfoM.Index(artifactID)
	if artifactInfo == nil {
		return nil
	}

	if artifactInfo.Rare < goxml.RareRed {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score,
	}
}

func SeasonComplianceParamRedSeasonArtifactStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	artifactID, ok := stageIDParam.param.(uint32)
	if !ok {
		return nil
	}

	artifactInfo := goxml.GetData().ArtifactInfoM.Index(artifactID)
	if artifactInfo == nil {
		return nil
	}

	if artifactInfo.Rare < goxml.RareRed {
		return nil
	}

	if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookArtifactStar, artifactID) {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score,
		Expand:   artifactID,
	}
}

func SeasonComplianceParamRedArtifactFragmentStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	param, ok := stageIDParam.param.([]uint32)
	if !ok {
		return nil
	}

	if len(param) != 2 {
		return nil
	}

	fragmentId := param[0]
	count := param[1]

	fragmentInfo := goxml.GetData().ArtifactFragmentInfoM.Index(fragmentId)
	if fragmentInfo == nil {
		return nil
	}

	if fragmentInfo.Rare < goxml.RareRed {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score * count,
	}
}

func SeasonComplianceParamRedSeasonArtifactFragmentStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	param, ok := stageIDParam.param.([]uint32)
	if !ok {
		return nil
	}

	if len(param) != 2 {
		return nil
	}

	fragmentId := param[0]
	count := param[1]

	fragmentInfo := goxml.GetData().ArtifactFragmentInfoM.Index(fragmentId)
	if fragmentInfo == nil {
		return nil
	}

	if fragmentInfo.Rare < goxml.RareRed {
		return nil
	}

	artifactInfo := goxml.GetData().ArtifactInfoM.GetFragmentIDArtifact(fragmentId)
	if artifactInfo == nil {
		return nil
	}

	if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookArtifactStar, artifactInfo.Id) {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score * count,
		Expand:   fragmentId,
	}
}

func SeasonComplianceParamCommonThreeOrangeEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareOrange) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceMoon || heroInfo.Race == goxml.RaceForest || heroInfo.Race == goxml.RaceEmpire {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamCommonSeasonThreeOrangeEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareOrange) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceMoon || heroInfo.Race == goxml.RaceForest || heroInfo.Race == goxml.RaceEmpire {
		if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookHeroEmblemExclusiveLv, emblem.Data.AdditiveHero) {
			return nil
		}
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamProtossDemonOrangeEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareOrange) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceProtoss || heroInfo.Race == goxml.RaceDemon {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamProtossDemonSeasonOrangeEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareOrange) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceProtoss || heroInfo.Race == goxml.RaceDemon {
		if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookHeroEmblemExclusiveLv, emblem.Data.AdditiveHero) {
			return nil
		}
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamCommonThreeRedEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareRed) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceMoon || heroInfo.Race == goxml.RaceForest || heroInfo.Race == goxml.RaceEmpire {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamCommonThreeSeasonRedEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareRed) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceMoon || heroInfo.Race == goxml.RaceForest || heroInfo.Race == goxml.RaceEmpire {
		if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookHeroEmblemExclusiveLv, emblem.Data.AdditiveHero) {
			return nil
		}

		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamProtossDemonRedEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareRed) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceProtoss || heroInfo.Race == goxml.RaceDemon {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamProtossDemonSeasonRedEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare != uint32(goxml.RareRed) {
		return nil
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		return nil
	}

	if heroInfo.Race == goxml.RaceProtoss || heroInfo.Race == goxml.RaceDemon {
		if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookHeroEmblemExclusiveLv, emblem.Data.AdditiveHero) {
			return nil
		}

		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Point:    stageInfo.Score,
		}
	}

	return nil
}

func SeasonComplianceParamHaveMoreThanOrangeEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil || emblem.Data == nil || emblem.Data.AdditiveHero == 0 {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare < uint32(goxml.RareOrange) {
		return nil
	}

	rareScore := goxml.GetData().SeasonComplianceEmblemInfoM.GetScore(stageIDParam.sc.GetPhase(), emblem.Data.AdditiveHero, emblemInfo.Rare)

	if rareScore[goxml.RareOrange] > 0 {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Expand:   emblem.Data.AdditiveHero,
			Point:    rareScore[goxml.RareOrange],
			EmblemPos: map[uint32]bool{
				emblemInfo.Pos: true,
			},
		}
	}

	return nil
}

func SeasonComplianceParamHaveMoreThanRedEmblemStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	emblemID := stageIDParam.param.(uint64)

	emblem := user.EmblemManager().Get(emblemID)
	if emblem == nil || emblem.Data == nil || emblem.Data.AdditiveHero == 0 {
		return nil
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil || emblemInfo.Rare < uint32(goxml.RareRed) {
		return nil
	}

	rareScore := goxml.GetData().SeasonComplianceEmblemInfoM.GetScore(stageIDParam.sc.GetPhase(), emblem.Data.AdditiveHero, emblemInfo.Rare)

	if rareScore[goxml.RareRed] > 0 {
		return &cl.ComplianceSourcePoint{
			SourceId: stageIDParam.StageID,
			Expand:   emblem.Data.AdditiveHero,
			Point:    rareScore[goxml.RareRed],
			EmblemPos: map[uint32]bool{
				emblemInfo.Pos: true,
			},
		}
	}

	return nil
}

func SeasonComplianceParamPyramidDrawNumStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	drawCount := stageIDParam.param.(uint64)

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score * uint32(drawCount),
	}
}

func SeasonComplianceParamRedPokemonStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	pokemonID, ok := stageIDParam.param.(uint32)
	if !ok {
		return nil
	}

	pokemonInfo := goxml.GetData().PokemonInfoM.GetRecordByPokemonId(pokemonID)
	if pokemonInfo == nil {
		return nil
	}

	if pokemonInfo.Rare < goxml.RareRed {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score,
	}
}

func SeasonComplianceParamRedSeasonPokemonStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	pokemonID, ok := stageIDParam.param.(uint32)
	if !ok {
		return nil
	}

	pokemonInfo := goxml.GetData().PokemonInfoM.GetRecordByPokemonId(pokemonID)
	if pokemonInfo == nil {
		return nil
	}

	if pokemonInfo.Rare < goxml.RareRed {
		return nil
	}

	if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookPokemonStar, pokemonInfo.PokemonId) {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score,
		Expand:   pokemonID,
	}
}

func SeasonComplianceParamRedFragmentPokemonStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	param, ok := stageIDParam.param.([]uint32)
	if !ok {
		return nil
	}

	if len(param) != 2 {
		return nil
	}

	fragmentId := param[0]
	count := param[1]

	fragmentInfo := goxml.GetData().PokemonFragmentInfoM.GetRecordByFragmentId(fragmentId)
	if fragmentInfo == nil {
		return nil
	}

	if fragmentInfo.Rare < goxml.RareRed {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score * count,
	}
}

func SeasonComplianceParamRedSeasonFragmentPokemonStageID(user *character.User, stageIDParam *StageIDCheckParam, stageType uint32) *cl.ComplianceSourcePoint {
	stageInfo := goxml.GetData().SeasonComplianceStageInfoM.GetScoreTypeStageType(stageIDParam.StageID, stageType)
	if stageInfo == nil {
		return nil
	}

	param, ok := stageIDParam.param.([]uint32)
	if !ok {
		return nil
	}

	if len(param) != 2 {
		return nil
	}

	fragmentId := param[0]
	count := param[1]

	fragmentInfo := goxml.GetData().PokemonFragmentInfoM.GetRecordByFragmentId(fragmentId)
	if fragmentInfo == nil {
		return nil
	}

	if fragmentInfo.Rare < goxml.RareRed {
		return nil
	}

	pokemonInfo := goxml.GetData().PokemonInfoM.GetRecordByFragmentValue(fragmentId)
	if pokemonInfo == nil {
		return nil
	}

	if !goxml.GetData().SeasonAddInfoM.IsComplianceAdd(user.GetSeasonID(), goxml.SeasonAddHandbookPokemonStar, pokemonInfo.PokemonId) {
		return nil
	}

	return &cl.ComplianceSourcePoint{
		SourceId: stageIDParam.StageID,
		Point:    stageInfo.Score * count,
		Expand:   fragmentId,
	}
}

var seasonComplianceScoreCalcFunc = map[uint32]CalcScoreFunc{
	goxml.DivineSummonStageID:              SeasonComplianceParamUint32Stage,
	goxml.DragonFightStageID:               SeasonComplianceParamUint32Stage,
	goxml.BossRushStageID:                  SeasonComplianceParamUint32Stage,
	goxml.GuildDungeonStageID:              SeasonComplianceParamUint32Stage,
	goxml.DivineHeroStageID:                SeasonComplianceParamDivineHero,
	goxml.DivineSeasonHeroStageID:          SeasonComplianceParamDivineSeasonAddHero,
	goxml.NormalSeasonHeroStageID:          SeasonComplianceParamNormalSeasonHero,
	goxml.ArtifactDebut:                    SeasonComplianceParamUint32Stage,
	goxml.RedArtifactStageID:               SeasonComplianceParamRedArtifactStageID,
	goxml.RedSeasonArtifactStageID:         SeasonComplianceParamRedSeasonArtifactStageID,
	goxml.RedArtifactFragmentStageID:       SeasonComplianceParamRedArtifactFragmentStageID,
	goxml.RedSeasonArtifactFragmentStageID: SeasonComplianceParamRedSeasonArtifactFragmentStageID,
	goxml.MirageFightStageID:               SeasonComplianceParamUint32Stage,
	goxml.CommonThreeOrangeEmblemStageID:   SeasonComplianceParamCommonThreeOrangeEmblemStageID,
	goxml.ProtossDemonOrangeEmblemStageID:  SeasonComplianceParamProtossDemonOrangeEmblemStageID,
	goxml.CommonThreeRedEmblemStageID:      SeasonComplianceParamCommonThreeRedEmblemStageID,
	goxml.ProtossDemonRedEmblemStageID:     SeasonComplianceParamProtossDemonRedEmblemStageID,
	goxml.HaveMoreThanOrangeEmblemStageID:  SeasonComplianceParamHaveMoreThanOrangeEmblemStageID,
	goxml.HaveMoreThanRedEmblemStageID:     SeasonComplianceParamHaveMoreThanRedEmblemStageID,
	goxml.PyramidDrawNumStageID:            SeasonComplianceParamPyramidDrawNumStageID,
	goxml.SeasonMapFightStageID:            SeasonComplianceParamUint32Stage,
	goxml.PokemonSummonStageID:             SeasonComplianceParamUint32Stage,
	goxml.PokemonRedStageID:                SeasonComplianceParamRedPokemonStageID,
	goxml.PokemonRedFragmentStageID:        SeasonComplianceParamRedFragmentPokemonStageID,
	goxml.PokemonSeasonRedStageID:          SeasonComplianceParamRedSeasonPokemonStageID,
	goxml.PokemonSeasonRedFragmentStageID:  SeasonComplianceParamRedSeasonFragmentPokemonStageID,
}

type StageIDCheckParam struct {
	StageID uint32
	param   any
	sc      *character.SeasonCompliance
}

func seasonComplianceUpdateScore(user *character.User, param interface{}, StageIDCheckParams []*StageIDCheckParam) {
	//赛季冲榜加积分
	aSrv := param.(GetActivityService)
	srv := param.(character.Servicer)
	if !user.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_COMPLIANCE), srv) {
		return
	}
	seasonComplianceM, ok := aSrv.GetActivity(activity.SeasonCompliance).(*seasoncompliance.Manager)
	if !ok {
		l4g.Errorf("user: %d seasonCompliance not exist", user.ID())
		return
	}
	phase, stageType, lastEnd := seasonComplianceM.GetPhaseAndStageType()
	if phase == 0 || stageType == 0 || !seasonComplianceM.LegalState() || lastEnd {
		return
	}
	compliance := user.SeasonComplianceM().GetSeasonCompliance(phase, stageType, srv, true)
	var totalAddScore uint32
	var sourcePoints []*cl.ComplianceSourcePoint
	for _, stageParam := range StageIDCheckParams {
		stageParam.sc = compliance
		calcFunc, exist := seasonComplianceScoreCalcFunc[stageParam.StageID]
		if !exist {
			continue
		}
		sourcePoint := calcFunc(user, stageParam, stageType)
		if sourcePoint != nil {
			l4g.Infof("user:%d seasonComplianceUpdateScore sourcePoint:%+v stageParam stageID:%d param:%+v stageType:%d", user.ID(), sourcePoint, stageParam.StageID, stageParam.param, stageType)
			sourcePoints = append(sourcePoints, sourcePoint)
		}
	}

	if len(sourcePoints) > 0 {
		curStage := seasonComplianceM.GetCurStage()
		if curStage == nil {
			return
		}
		sTotalScore := compliance.GetTotalScore()
		for _, sourcePoint := range sourcePoints {
			if !compliance.SetSource(sourcePoint) {
				continue
			}
			sTotalScore += sourcePoint.Point
			user.LogSeasonComplianceScoreChange(srv, curStage.Phase, curStage.Stage, sourcePoint.SourceId, sourcePoint.Point, sTotalScore)
			totalAddScore += sourcePoint.Point
		}

		user.SeasonComplianceM().Save()
		srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonComplianceRankUpdate, user.ID(), &l2c.L2CS_SeasonComplianceRankUpdate{
			Stage:      curStage,
			CommonData: compliance.BuildSeasonComplianceRank(user, srv.ServerID()),
			TotalData:  user.SeasonComplianceM().BuildPhaseSeasonComplianceRank(curStage.Phase, srv.ServerID()),
		})
		user.SendCmdToGateway(cl.ID_MSG_L2C_SeasonComplianceScoreChange, &cl.L2C_SeasonComplianceScoreChange{
			Ret:        uint32(ret.RET_OK),
			Stage:      stageType,
			AddScore:   totalAddScore,
			TotalScore: compliance.GetTotalScore(),
		})
	}
}

var SeasonDoorEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.SeasonDoor().OnSeasonDoorEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var doSeasonJewelryGetEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	// X级以上的赛季装备收集Y件
	jLevel := commonEvent.Values[0]                                                               // 获取当前获得赛季装备的等级
	infos := goxml.GetData().TaskTypeInfoM.IndexEvent(aevent.AeSeasonDoorLevelXJewelryCollectToY) // 获取所有该类型任务
	for _, info := range infos {
		if jLevel < info.Parameter1 { // 大于等于目标等级计算进度
			continue
		}
		user.FireCommonEvent(srv.EventM(), uint32(aevent.AeSeasonDoorLevelXJewelryCollectToY), commonEvent.Value, info.Parameter1) // 更新所有任务进度
	}
}

var doSeasonJewelrySkillLevelUpEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	// 赛季装备词条升级X次
	user.FireCommonEvent(srv.EventM(), uint32(aevent.AeSeasonDoorJewelrySkillLevelUpTimesToX), commonEvent.Value)
}

var doCNWebRechargeEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	rechargeCount := uint32(commonEvent.Value)

	user.ActivityWeb().Recharge(rechargeCount, srv)
}

var doIeSeasonMapFightXEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)
	fightCount := uint32(commonEvent.Value)

	user.FireCommonEvent(srv.EventM(), aevent.AeSeasonMapFightX, uint64(fightCount))
	seasonComplianceUpdateScore(user, srv, []*StageIDCheckParam{
		{
			StageID: goxml.SeasonMapFightStageID,
			param:   fightCount,
		},
	})
}

var ComplianceTasksEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.ComplianceTasks().OnComplianceTasksEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var doSeasonComplianceEmblemStageInitEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	//赛季冲榜加积分
	aSrv := param.(GetActivityService)
	if !user.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_COMPLIANCE), srv) {
		return
	}
	seasonComplianceM, ok := aSrv.GetActivity(activity.SeasonCompliance).(*seasoncompliance.Manager)
	if !ok {
		l4g.Errorf("user: %d seasonCompliance not exist", user.ID())
		return
	}
	phase, stageType, lastEnd := seasonComplianceM.GetPhaseAndStageType()
	l4g.Info("seasonComplianceUpdateScore now:%d phase:%d stageType:%d", time.Now().Unix(), phase, stageType)
	if phase == 0 || stageType == 0 || !seasonComplianceM.LegalState() || lastEnd {
		return
	}
	compliance := user.SeasonComplianceM().GetSeasonCompliance(phase, stageType, srv, true)
	var totalAddScore uint32

	curStage := seasonComplianceM.GetCurStage()
	if curStage == nil {
		return
	}
	var sTotalScore uint32
	for _, sourcePoint := range compliance.GetComplianceSourcePoint() {
		sTotalScore += sourcePoint.Point
		user.LogSeasonComplianceScoreChange(srv, curStage.Phase, curStage.Stage, sourcePoint.SourceId, sourcePoint.Point, sTotalScore)
		totalAddScore += sourcePoint.Point
	}

	user.SeasonComplianceM().Save()
	if sTotalScore > 0 {
		srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonComplianceRankUpdate, user.ID(), &l2c.L2CS_SeasonComplianceRankUpdate{
			Stage:      curStage,
			CommonData: compliance.BuildSeasonComplianceRank(user, srv.ServerID()),
			TotalData:  user.SeasonComplianceM().BuildPhaseSeasonComplianceRank(curStage.Phase, srv.ServerID()),
		})
		user.SendCmdToGateway(cl.ID_MSG_L2C_SeasonComplianceScoreChange, &cl.L2C_SeasonComplianceScoreChange{
			Ret:        uint32(ret.RET_OK),
			Stage:      stageType,
			AddScore:   totalAddScore,
			TotalScore: compliance.GetTotalScore(),
		})
	}
}

var SeasonMapEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.SeasonMap().OnTaskEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}

var TowerPokemonHandlerOnEvent = func(evt event.Eventer, param interface{}) {
	commonEvent := evt.(aevent.CommonEvent)
	user := commonEvent.User.(*character.User)
	srv := param.(character.Servicer)

	user.TowerPokemon().OnTowerPokemonEvent(commonEvent.Event, commonEvent.Value, commonEvent.Values, srv)
}
