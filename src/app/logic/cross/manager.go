package cross

import (

	//"encoding/json"

	"app/cross/tool"
	"app/logic/session"
	"app/protos/in/l2c"
	"app/protos/in/l2cm"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

type Servicer interface {
	NewCrossActAddOnConnectedNode(uint32)
	NewCrossActAddOnUnconnectedNode(uint32)
}

type CrossNode struct {
	id       uint32
	httpAddr string
	addr     string //实际访问的地址
	netAddr  uint64
}

type Manager struct {
	srv Servicer
	*session.Manager

	connected map[string]bool
	parts     map[uint32]*l2cm.CrossActPart //活动id=>分区信息.为0代表战区配置
	nodes     map[uint32]*CrossNode         //后台配置node_id=>信息

	normalPartSids []uint64 // 同战区的服务器ID列表
}

func New(srv Servicer, ctx *ctx.Group, cfg *session.Config) *Manager {
	sm := session.NewManager(ctx, cfg)
	return &Manager{
		Manager:   sm,
		srv:       srv,
		connected: make(map[string]bool),
		parts:     make(map[uint32]*l2cm.CrossActPart),
		nodes:     make(map[uint32]*CrossNode),
	}
}

func getActivityIDByCmd(cmd l2c.ID) uint32 {
	var id uint32
	switch {
	case cmd > l2c.ID_MSG_ROUTER_MIN && cmd < l2c.ID_MSG_ROUTER_MAX:
		// TODO. 这里可以随机一个id给出去.为了固定期间，可以按照服务器id来取模
		id = uint32(l2c.ACTIVITYID_ROUTER)
	case cmd > l2c.ID_MSG_WRESTLE_MIN && cmd < l2c.ID_MSG_WRESTLE_MAX:
		id = uint32(l2c.ACTIVITYID_WRESTLE)
	case cmd > l2c.ID_MSG_GUILD_MIN && cmd < l2c.ID_MSG_GUILD_MAX:
		id = uint32(l2c.ACTIVITYID_GUILD)
	case cmd > l2c.ID_MSG_RANK_MIN && cmd < l2c.ID_MSG_RANK_MAX:
		id = uint32(l2c.ACTIVITYID_RANK)
	case cmd > l2c.ID_MSG_GUILD_CHEST_MIN && cmd < l2c.ID_MSG_GUILD_CHEST_MAX:
		id = uint32(l2c.ACTIVITYID_GUILD)
	case cmd > l2c.ID_MSG_WorldBoss_MIN && cmd < l2c.ID_MSG_WorldBoss_MAX:
		id = uint32(l2c.ACTIVITYID_WORLDBOSS)
	case cmd > l2c.ID_MSG_DisorderLand_MIN && cmd < l2c.ID_MSG_DisorderLand_MAX:
		id = uint32(l2c.ACTIVITYID_DISORDER_LAND)
	case cmd > l2c.ID_MSG_PEAK_MIN && cmd < l2c.ID_MSG_PEAK_MAX:
		id = uint32(l2c.ACTIVITYID_PEAK)
	case cmd > l2c.ID_MSG_MIN_GST && cmd < l2c.ID_MSG_MAX_GST,
		cmd > l2c.ID_MSG_MIN_GST_DRAGON && cmd < l2c.ID_MSG_MAX_GST_DRAGON,
		cmd > l2c.ID_MSG_MIN_GSTOre && cmd < l2c.ID_MSG_MAX_GSTOre,
		cmd > l2c.ID_MSG_MIN_GSTTech && cmd < l2c.ID_MSG_MAX_GSTTech,
		cmd > l2c.ID_MSG_MIN_GSTChallenge && cmd < l2c.ID_MSG_MAX_GSTChallenge:
		id = uint32(l2c.ACTIVITYID_GST)
	case cmd > l2c.ID_MSG_MIN_SEASON_ARENA && cmd < l2c.ID_MSG_MAX_SEASON_ARENA:
		id = uint32(l2c.ACTIVITYID_SEASON_ARENA)
	case cmd > l2c.ID_MSG_MIN_HOT_RANK && cmd < l2c.ID_MSG_MAX_HOT_RANK:
		id = uint32(l2c.ACTIVITYID_HOT_RANK)
	case cmd > l2c.ID_MSG_MIN_Guild_Mobilization && cmd < l2c.ID_MSG_MAX_Guild_Mobilization:
		id = uint32(l2c.ACTIVITYID_GUILD)
	case cmd > l2c.ID_MSG_MIN_SeasonComplianceRank && cmd < l2c.ID_MSG_MAX_SeasonComplianceRank:
		id = uint32(l2c.ACTIVITYID_SEASON_COMPLIANCE)
	case cmd > l2c.ID_MSG_MIN_SeasonMap && cmd < l2c.ID_MSG_MAX_SeasonMap:
		id = uint32(l2c.ACTIVITYID_SEASON_MAP)
	}

	return id
}

// 根据Node地址获取对应的所有的活动
func (m *Manager) GetActivityByNetAddr(netAddr uint64) []uint32 {
	acts := make([]uint32, 0, 4) //nolint:mnd
	for _, v := range m.nodes {
		if v.netAddr == netAddr {
			for _, p := range m.parts {
				//有可能是战区part node为nil
				if p.Node != nil && p.Node.Id == v.id {
					acts = append(acts, p.ActId)
				}
			}
			break
		}
	}
	return acts
}

func (m *Manager) Write(ph *parse.PackHead, msg interface{}) bool {
	id := getActivityIDByCmd(l2c.ID(ph.Cmd))
	part := m.parts[id]
	if part == nil {
		l4g.Errorf("user: %d no found activity: %d partition, cmd: %d", ph.UID, id, ph.Cmd)
		return false
	}
	l4g.Debugf("crossManager. crossAct: %+v, ph: %+v", part, ph)
	if part.ActId != id {
		l4g.Errorf("cross manager found part act id ni not same.local:%d, remote:%d", id, part.ActId)
		return false
	}
	node := m.nodes[part.Node.Id]
	if node == nil {
		l4g.Errorf("cross manager no found node :%d", part.Node.Id)
		return false
	}
	client := m.GetClientByAddr(node.netAddr)
	if client == nil {
		l4g.Errorf("cross manager no found client :%d", node.netAddr)
		return false
	}
	if client != nil {
		//复用cseq
		ph.CSeq = id
		ph.SSeq = part.Partition
		ph.Flags = part.Session //session的标记
		client.Write(ph, msg)
		return true
	}
	return false
}

func (m *Manager) UpdatePartition(msg *l2cm.CM2L_GetServerPartition) bool {
	if msg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CrossMasterManager UpdatePartition msg error:%d", msg.Ret)
		return false
	}
	m.parts = make(map[uint32]*l2cm.CrossActPart)
	m.nodes = make(map[uint32]*CrossNode)
	for _, part := range msg.Parts {
		m.updatePartition(part)
	}
	m.SetNormalPartSids(msg.NormalPartSids)
	m.FlushPart()
	return true
}

func (m *Manager) SetNormalPartSids(sids []uint64) {
	m.normalPartSids = sids
}

func (m *Manager) UpdateOnePartition(msg *l2cm.CM2L_UpdateServerPartition) bool {
	if msg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CrossMasterManager UpdatePartition msg error:%d", msg.Ret)
		return false
	}
	m.updatePartition(msg.Part)
	if msg.Part.ActId == uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA) {
		m.SetNormalPartSids(msg.NormalPartSids)
	}
	m.FlushPart()
	return true
}

func (m *Manager) updatePartition(part *l2cm.CrossActPart) {
	if part.ActId == uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA) ||
		part.ActId == uint32(l2c.CROSS_ACT_AREA_ID_SEASON_AREA) ||
		part.ActId == uint32(l2c.CROSS_ACT_AREA_ID_GST_AREA) ||
		part.ActId == uint32(l2c.CROSS_ACT_AREA_ID_SEASON_COMPLIANCE_AREA) {
		m.parts[part.ActId] = part
		return
	}
	//如果跨服传递过来的分区id为0,代表是删除这个分区的配置信息
	if part.Partition == 0 {
		l4g.Infof("logic UpdateServerPartition del part act_id:%d, part:%+v", part.ActId, part)
		delete(m.parts, part.ActId)
		return
	}
	if part.Node == nil {
		l4g.Errorf("Cross Manager UpdatePartition part node is nil:%+v", part)
		return
	}
	m.parts[part.ActId] = part
	httpAddr := part.Node.HttpAddr
	logicAddr := tool.GetLogicTcpAddr(httpAddr)
	node := &CrossNode{
		id:       part.Node.Id,
		httpAddr: httpAddr,
		addr:     logicAddr,
		netAddr:  util.NetAddrToUint64(logicAddr),
	}
	m.nodes[part.Node.Id] = node
	if !m.connected[logicAddr] {
		//配置的活动是一个新的节点。原来可能连的是老的节点。那么这个时候需要把原来的状态设置为关闭
		l4g.Info("logic UpdateServerPartition new act:%d part:%d run on node addr:%s %s netadd:%d, is new node", part.ActId, part.Partition, node.httpAddr, node.addr, node.netAddr)
		m.srv.NewCrossActAddOnUnconnectedNode(part.ActId)
		m.Create(logicAddr, logicAddr)
		m.connected[logicAddr] = true
	} else {
		//活动被配置到了一个已经链接上的节点。那么这个活动要重新触发一下链接上的事件
		l4g.Info("logic UpdateServerPartition new act:%d part:%d run on node addr:%s %s netadd:%d", part.ActId, part.Partition, node.httpAddr, node.addr, node.netAddr)
		client := m.GetClientByAddr(node.netAddr)
		if client != nil {
			l4g.Info("logic UpdateServerPartition new act:%d part:%d run on node addr:%s %s netadd:%d, client is connected", part.ActId, part.Partition, node.httpAddr, node.addr, node.netAddr)
			//新的活动需要更新下状态
			m.srv.NewCrossActAddOnConnectedNode(part.ActId)
		}
	}
}

func (m *Manager) GetCrossArea(arenaType uint32) uint32 {
	if v, exist := m.parts[arenaType]; exist {
		return v.Partition
	} else {
		return uint32(0)
	}
}

func (m *Manager) GetNormalPartSids() []uint64 {
	return m.normalPartSids
}

func (m *Manager) FlushPart() {
	for k, v := range m.parts {
		l4g.Infof("========>Flush cross part, partID:%d info:%+v, node:%+v", k, v, v.Node)
	}
	for k, v := range m.nodes {
		l4g.Infof("========>Flush node, nodeID:%d  node:%+v", k, v)
	}
	for k, v := range m.connected {
		l4g.Infof("========>Flush connected, addr:%s  connect:%+v", k, v)
	}
}
