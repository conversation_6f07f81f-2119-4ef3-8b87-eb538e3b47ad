package cross

import (
	"app/csession"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

type CrossMasterManager struct {
	*csession.Manager
	group   *ctx.Group
	addr    string
	netAddr uint64

	//parts     map[uint32]*l2cm.CrossActPart
	//nodes     map[uint32]*config.CrossNode
	//connected bool
}

func NewCrossMasterManager(group *ctx.Group, cfg *csession.Config, addr string) *CrossMasterManager {
	return &CrossMasterManager{
		Manager: csession.NewManager(group.CreateChild(), cfg),
		group:   group,
		addr:    addr,
		netAddr: util.NetAddrToUint64(addr),
		//partitions: make(map[uint32]*l2cm.CrossActPart),
		//nodes:      make(map[uint32]*config.CrossNode),
	}
}

// 对外接口.开启一个g来执行
func (m *CrossMasterManager) Run() {
	m.Create(m.addr)
}

func (m *CrossMasterManager) Close() {
	m.Manager.Close()

	m.group.Stop()
	m.group.Wait()
	m.group.Finish()
	l4g.Info("CrossMasterManager close...")
}

func (m *CrossMasterManager) Write(ph *parse.PackHead, msg interface{}) bool {
	client := m.GetClientByAddr(m.netAddr)
	if client != nil {
		client.Write(ph, msg)
		client.ForceWrite()
		return true
	}
	l4g.Errorf("CrossMasterManager no found server:%s", m.addr)
	return false
}

/*
func (m *CrossMasterManager) UpdatePartition(msg *l2cm.CM2L_GetServerPartition) bool {
	if msg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CrossMasterManager UpdatePartition msg error:%d", msg.Ret)
		return false
	}
	m.parts = make(map[uint32]*l2cm.CrossActPart)
	m.nodes = make(map[uint32]*config.CrossNode)
	for _, v := range msg.Parts {
		m.parts[v.ActId] = v
		m.nodes[v.Node.Id] = v.Node
	}
}
*/
