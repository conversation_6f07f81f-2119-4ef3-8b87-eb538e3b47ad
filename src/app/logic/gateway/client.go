package gateway

import (
	"sync"

	"gitlab.qdream.com/kit/sea/time"

	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"
	"gitlab.qdream.com/kit/sea/zebra/tcp"

	l4g "github.com/ivanabc/log4go"
)

const connectTimeout = 10

type Client struct {
	act *actor.Actor

	broker  *tcp.Broker
	id      uint64
	netAddr uint64
	manager *Manager
	cfg     *Config
	ctx     *ctx.Group

	msgs *tcp.MessageBatch
}

func newClient(ctx *ctx.Group, ma *Manager, cfg *Config, id uint64) *Client {
	client := &Client{
		manager: ma,
		id:      id,
		netAddr: util.NetAddrToUint64(cfg.Zebra.Address),
		cfg:     cfg,
		ctx:     ctx,
	}
	client.act = actor.NewActor(client.ctx.CreateChild(), cfg.Actor)
	return client
}

func (c *Client) run() {
	c.connect()
	c.act.Run(c)
}

func (c *Client) ID() uint64 {
	return c.id
}

func (c *Client) NetAddr() uint64 {
	return c.netAddr
}

func (c *Client) Init(broker *tcp.Broker) {
	//连接正常
	c.broker = broker
	c.msgs = parse.NewMessageBatch()
	c.manager.addClient(c)
	l4g.Infof("[gateway] Client init: %d %s %s", c.id, c.broker.LocalAddr(), c.broker.RemoteAddr())
}

func (c *Client) Close() {
	closeMsg := &Message{
		GateID:      c.id,
		GateNetAddr: c.netAddr,
	}
	closeMsg.Cmd = CloseSessionCmd
	c.manager.ReadQ <- closeMsg
	c.manager.deleteClient(c)
	c.act.AddMessage(&reconnectMessage{})
	l4g.Infof("[gateway] connect close: %d %s(%d)", c.id, c.cfg.Zebra.Address, c.netAddr)
}

func (c *Client) Write(ph *parse.PackHead, msg interface{}) {
	sm := singleMessagePool.Get().(*singleMessage)
	sm.PH, sm.Data = *ph, msg
	c.act.AddMessage(sm)
}

func (c *Client) ForceWrite() {
	c.act.AddMessage(&syncMessage{})
}

func (c *Client) Process(buf []byte) {
	msg := &Message{
		GateID:      c.id,
		GateNetAddr: c.netAddr,
	}
	parse.DecodePackHead(buf, &msg.PackHead)
	msg.Data = buf

	l4g.Debugf("[gateway] client %d recv msg: %d", c.id, msg.PackHead.Cmd)
	c.manager.ReadQ <- msg
}

func (c *Client) stop() {
	c.ctx.Stop()
}

func (c *Client) wait() {
	c.ctx.Wait()
	c.ctx.Finish()
	//l4g.Infof("[gateway] Client wait finish: %d %s %s", c.id, c.broker.LocalAddr(), c.broker.RemoteAddr())
}

func (c *Client) connect() {
	if !tcp.ClientServe(c.ctx.CreateChild(), c, c.cfg.Zebra, connectTimeout*time.Second) {
		l4g.Errorf("[gateway] connect server error: %d %+v", c.id, c.cfg.Zebra)
		c.act.AddTimer(&reconnectTimer{c}, c.cfg.ReconnectInterval+time.Now().Unix(), 0)
	} else {
		l4g.Infof("[gateway] connect server success: %d %s(%d)", c.id, c.cfg.Zebra.Address, c.netAddr)
	}
}

func (c *Client) addReconnectTimer() {
	c.act.AddTimer(&reconnectTimer{c}, c.cfg.ReconnectInterval+time.Now().Unix(), 0)
}

type reconnectTimer struct {
	owner *Client
}

func (r *reconnectTimer) TimeOut(now int64) {
	r.owner.connect()
}

type reconnectMessage struct {
}

func (r *reconnectMessage) Process(param actor.Receiver) error {
	param.(*Client).addReconnectTimer()
	return nil
}

var singleMessagePool = &sync.Pool{
	New: func() interface{} {
		return new(singleMessage)
	},
}

type singleMessage struct {
	PH   parse.PackHead
	Data interface{}
}

func (s *singleMessage) Process(param actor.Receiver) error {
	//消息合并发送
	c := param.(*Client)
	parse.AppendMessageBatch(c.msgs, &s.PH, s.Data)
	if c.msgs.IsFull() {
		c.broker.WriteMessages(c.msgs)
		c.msgs = parse.NewMessageBatch()
		l4g.Debugf("gate client %d msg queue is full", c.ID())
	}

	//回收
	s.Data = nil
	singleMessagePool.Put(s)
	return nil
}

type syncMessage struct {
}

func (s *syncMessage) Process(param actor.Receiver) error {
	//强制同步消息
	c := param.(*Client)
	if !c.msgs.IsEmpty() {
		c.broker.WriteMessages(c.msgs)
		c.msgs = parse.NewMessageBatch()
		l4g.Debugf("gate client %d ForceWrite", c.ID())
	}
	return nil
}
