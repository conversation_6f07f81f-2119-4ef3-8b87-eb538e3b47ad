//nolint:mnd
package battle

import (
	"app/goxml"
	"app/protos/out/bt"
	"fmt"
	"math"
	"slices"

	"gitlab.qdream.com/kit/sea/util"
)

type Team struct {
	IPoolObj
	index                uint32
	autoID               uint32 //自增id(当前队伍成员最大id)，从最大指定位置CallPos+1开始(12)
	m                    *Manager
	alive                int
	baseInfo             *TeamBase
	artifactM            *ArtifactManager
	publicHp             map[uint32]*PublicHpGroup //共用血量组
	effects              *TeamEffect
	tUniter              *TeamUniter
	activedLinks         []*goxml.LinkSkillInfoExt         //激活的联结
	heroLinkInfo         map[uint32]map[uint32]util.None   //heroId => linkid
	DeathTimes           int                               //死亡次数
	ResurrectionTimes    int                               //复活次数
	initMembers          []*Member                         //初始的成员
	voidMembers          []*VoidMember                     //初始的虚拟单位
	members              []*Member                         //member 在位置上战斗的 死亡的也在这里
	activedArtifactLinks []*goxml.ArtifactLinkSkillInfoExt //激活的神器联结
	artifactLinkInfo     map[uint32]map[uint32]util.None   //artifactId => linkid
	altRaisePS           map[uint32][]uint64
	totalAttr            [goxml.AttrMaxNum]int64 //队伍的总属性
	shareDamageMembers   map[uint32][]Uniter     //施加者 => 绑定的人

	energy            int64 //赛季羁绊，能量值
	maxEnergy         int64 //赛季羁绊，能量上限值
	roundAddEnergy    int64 //赛季羁绊，当前回合添加的能量值
	roundAddNegEnergy int64 //赛季羁绊，当前回合添加的能量值(负能量)

	bigSkillCount map[uint32]uint32 //回合数 => 主动释放大招的英雄数量
	linkTypeCount map[uint32]uint32 //激活的不同类型羁绊数量 羁绊类型=>激活数量
	callCount     uint32            //召唤次数（召唤个数）

	accumulateHurt uint64 //累计伤害，s4神器buff

	// 自适应羁绊
	changeTypeLinkNum   map[uint32]uint32   // linkChangeType => num
	heroChangeTypeLinks map[uint32][]uint32 // heroSysID => 可变羁绊类型列表

	// 共享cd计数
	psShareCD map[uint32]uint32 // 被动技能id=>生效回合数

	haloPsSkill [goxml.Event_Max][]*PsSkill //光环被动

	pokemons []*Pokemon
}

type TeamBase struct {
	IsCalculateLink bool   //是否计算羁绊
	Power           int64  //战力
	Name            string //名字
	BaseId          uint32
	SeasonLv        uint32
	TeamIndex       uint32 //队伍编号，从0开始
	TitleId         uint32
}

type TeamEffect struct {
	buffTypeEffects  map[uint32]*BuffEffect //bufftype=>
	singleBuffs      map[uint32]Uniter      //buffid=>加过buff的对象
	linkInfo         map[uint32]uint32      //拥有的联结
	artifactLinkInfo map[uint32]uint32      //拥有的神器联结
}

type BuffEffect struct {
	addLayer   uint32
	multiLayer uint32 // 万分比
	fixTurns   uint32
}

func (e *BuffEffect) GetMaxLayer(buffTypeInfo *goxml.BuffTypeInfo) uint32 {
	return uint32(math.Floor(float64(buffTypeInfo.MaxAdd+e.addLayer) * float64(e.multiLayer) / goxml.BaseFloat))
}

func NewTeam(members []*Member, artifacts []*bt.ArtifactInfo,
	voidMembers []*VoidMember, baseInfo *TeamBase, pokemons []*Pokemon) *Team {
	t := gPool.GetTeam()
	t.fillData(members, artifacts, voidMembers, baseInfo, pokemons)
	return t
}

func (t *Team) GetHaloPsSkills(event int) []*PsSkill {
	return t.haloPsSkill[event]
}

func (t *Team) AddHaloPsSkill(event int, psSkill *PsSkill) {
	t.haloPsSkill[event] = append(t.haloPsSkill[event], psSkill)
}

func (t *Team) GetTeamUniter() *TeamUniter {
	return t.tUniter
}

func (t *Team) GetBaseInfo() *TeamBase {
	return t.baseInfo
}

func (t *Team) calPower() {
	t.baseInfo.Power = 0
	for _, mem := range t.initMembers {
		t.baseInfo.Power += int64(CalPower(mem.attrM.baseAttr, true))
	}
}

func (t *Team) fillData(members []*Member, artifacts []*bt.ArtifactInfo,
	voidMembers []*VoidMember, baseInfo *TeamBase, pokemons []*Pokemon) {
	t.members = make([]*Member, 0, len(members))
	t.initMembers = members
	t.baseInfo = baseInfo
	t.voidMembers = voidMembers
	t.artifactM = gPool.GetArtifactManager()
	t.artifactM.fillData(artifacts)
	t.effects = &TeamEffect{
		buffTypeEffects: make(map[uint32]*BuffEffect),
	}
	t.tUniter = gPool.GetTeamUniter()
	t.energy = para(7002)
	t.bigSkillCount = make(map[uint32]uint32)
	t.linkTypeCount = make(map[uint32]uint32)

	// 因s6科技树的影响，后续仅在初始时设置个默认的能量上限
	// 7001需根据各赛季不同情况，策划来调整数值
	t.setMaxEnergy(7001)

	t.psShareCD = make(map[uint32]uint32)
	t.pokemons = pokemons
}

func (t *Team) GetActivedLinks() []*goxml.LinkSkillInfoExt {
	return t.activedLinks
}

func (t *Team) GetActivedArtifactLinks() []*goxml.ArtifactLinkSkillInfoExt {
	return t.activedArtifactLinks
}

func (t *Team) ForEachMember(fn func(*Member)) {
	for _, mem := range t.members {
		fn(mem)
	}
}

func (t *Team) GetAllMembers() []*Member {
	mems := make([]*Member, len(t.members))
	i := 0
	for _, v := range t.members {
		mems[i] = v
		i++
	}
	return mems
}

func (t *Team) Reset() {
	*t = Team{}
}

func (t *Team) HasArtifact() bool {
	return t.artifactM.HasArtifact()
}

func (t *Team) GetArtifactAttr(typ int) int64 {
	return t.artifactM.attrM.GetAttr(typ)
}

func (t *Team) getInitMemberByPos(pos uint32) *Member {
	for _, m := range t.initMembers {
		if m.Pos() == pos {
			return m
		}
	}
	return nil
}

func (t *Team) getUniterByPos(pos uint32) Uniter {
	for _, m := range t.members {
		if m.Pos() == pos {
			return m
		}
	}
	return nil
}

func (t *Team) GetUniterByPos(pos uint32) Uniter {
	m := t.getUniterByPos(pos)
	if m != nil {
		return m
	}

	for _, m := range t.voidMembers {
		if m.Pos() == pos {
			return m
		}
	}
	return nil
}

func (t *Team) SetIndex(index uint32) {
	t.index = index
}

func (t *Team) SetName(name string) {
	t.baseInfo.Name = name
}

func (t *Team) SetSeasonLv(lv uint32) {
	t.baseInfo.SeasonLv = lv
}

func (t *Team) IsAttackTeam() bool {
	return t.index == AttackTeam
}
func (t *Team) IsDefenseTeam() bool {
	return t.index == DefenseTeam
}

func (t *Team) IsCalculateLink() bool {
	return t.baseInfo.IsCalculateLink
}

func (t *Team) ChangeAliveNum(num int, mem *Member, triggerEvent bool) {
	t.alive += num
	if triggerEvent {
		t.m.TriggerBuffEvent(Buff_Event_Alive_Change, mem)
	}
}

// @param bool fixMaxHP 是否修改最大血量 - 修改最大血量=当前血量
func (t *Team) AddMember(mem *Member, altAttr map[uint32]map[int]int64, altHp map[uint64]int64,
	altPosHp map[uint32]int64, isCall, fixMaxHP bool) {
	mem.initBase(t, isCall)
	mem.initAttr(altAttr, altHp, altPosHp, fixMaxHP)
	if mem.GetStatus() == UniterDead {
		return
	}
	if mem.NeedKill() {
		t.ChangeAliveNum(1, mem, true)
	}
	if mem.Base.PublicHp != 0 {
		t.addPublicHp(mem)
	}
	if m := t.getUniterByPos(mem.Base.Pos); m != nil {
		t.members = slices.DeleteFunc(t.members, func(member *Member) bool {
			return member.ID() == m.ID()
		})
		t.m.RemoveActMember(m.ID())
	}
	t.members = append(t.members, mem)
	t.m.AddActMember(mem)

	t.m.unites = append(t.m.unites, mem)
}

func (t *Team) addPublicHp(mem *Member) {
	if t.publicHp == nil {
		t.publicHp = make(map[uint32]*PublicHpGroup)
	}
	hpGroup := t.publicHp[mem.Base.PublicHp]
	if hpGroup == nil {
		hpGroup = NewPublicHpGroup(t.m)
		t.publicHp[mem.Base.PublicHp] = hpGroup
	}
	hpGroup.AddUniter(mem)
}

// @param bool fixMaxHP 是否修改最大血量 - 自动战斗的存血玩法，需剔除已死亡的初始成员
func (t *Team) initBase(m *Manager, teamIndex uint32, altAttr map[uint32]map[int]int64,
	altHp map[uint64]int64, altRaisePS map[uint32][]uint64, altPosHp map[uint32]int64, fixMaxHP bool) {
	t.m = m
	t.index = teamIndex
	t.altRaisePS = altRaisePS
	for _, mem := range t.initMembers {
		t.AddMember(mem, altAttr, altHp, altPosHp, false, fixMaxHP)
	}

	// 移除已经死亡的初始成员
	if fixMaxHP {
		aliveMembers := make([]*Member, 0, len(t.initMembers))
		for _, mem := range t.initMembers {
			if mem.GetStatus() != UniterDead {
				aliveMembers = append(aliveMembers, mem)
			}
		}
		t.initMembers = aliveMembers
	}

	for _, mem := range t.voidMembers {
		t.AddVoidMember(mem)
	}
	t.initAttr()
	t.InitArtifactM(altAttr)
	t.InitGlobalUniter()
	t.initPokemons()
}

func (t *Team) initPokemons() {
	for _, pokemon := range t.pokemons {
		pokemon.Init(t.m, t)
		t.m.unites = append(t.m.unites, pokemon)
	}
}

func (t *Team) initAttr() {
	//目前只有总暴击率和总反弹率
	for _, m := range t.members {
		t.totalAttr[goxml.AttrCritRate] += m.GetAttr(goxml.AttrCritRate)
		t.totalAttr[goxml.AttrDamReflectRate] += m.GetAttr(goxml.AttrDamReflectRate)
	}
}
func (t *Team) initSkills() {
	for _, mem := range t.initMembers {
		mem.initRaisePs(t.altRaisePS)
		mem.initSkills(false)
	}
	for _, mem := range t.voidMembers {
		mem.initRaisePs(t.altRaisePS)
		mem.InitSkills()
	}
	t.initArtifactSKills()
	t.tUniter.initRaisePs(t.altRaisePS)
	t.altRaisePS = nil
	t.tUniter.addLinkPassiveSkills()
	for _, pokemon := range t.pokemons {
		pokemon.InitSkills()
	}
}

func (t *Team) initArtifactSKills() {
	for _, artifact := range t.artifactM.artifacts {
		starInfo := goxml.GetData().ArtifactStarInfoM.Index(artifact.artifactInfo.SysId, artifact.artifactInfo.Star)
		if starInfo != nil {
			artifact.raisePSs = append(artifact.raisePSs, starInfo.RaisePsSkills...)
			artifact.calRaisePsAttr()
		}
	}
	t.artifactM.initRaisePs(t.altRaisePS)
	t.artifactM.InitSkills()
}

func (t *Team) GetPublicHpGroup(id uint32) *PublicHpGroup {
	return t.publicHp[id]
}

func (t *Team) AddVoidMember(voidMember *VoidMember) {
	voidMember.Init(t.m, t)
	t.m.unites = append(t.m.unites, voidMember)
}

func (t *Team) InitArtifactM(altAttr map[uint32]map[int]int64) {
	t.artifactM.Init(t.m, t, ArtifactBattlePos)
	t.artifactM.initAttr(altAttr)
	t.artifactM.initArtifact(t)
	t.m.unites = append(t.m.unites, t.artifactM)
}

func (t *Team) InitGlobalUniter() {
	t.tUniter.Init(t.m, t)
	t.m.unites = append(t.m.unites, t.tUniter)
}

func (t *Team) AddArtifactEnergy(energy int, attack, defense Uniter) {
	t.artifactM.AddEnergy(energy, attack, defense, nil)
}

func (t *Team) SettleBuff(stage uint32, iexe IExecution) {
	// 神器身上的buff，也要扣回合数
	if t.artifactM != nil {
		SettleBuff(t.artifactM, stage, iexe)
	}

	for _, mem := range t.members {
		SettleBuff(mem, stage, iexe)
	}
}

func (t *Team) GetAliveNum() uint32 {
	var aliveNum uint32 = 0
	for _, mem := range t.members {
		if !(mem.GetStatus() == UniterDead) {
			aliveNum++
		}
	}
	return aliveNum
}

func (t *Team) TriggerUpdateAliveNum() {
	for _, mem := range t.members {
		mem.attrM.UpdateAliveNum(t.GetAliveNum())
	}
}

func (t *Team) Dead(mem *Member, triggerEvent bool) {
	t.TriggerUpdateAliveNum()
	if mem.NeedKill() {
		t.ChangeAliveNum(-1, mem, triggerEvent)
	}
}

func (t *Team) Alive(mem *Member, triggerEvent bool) {
	t.TriggerUpdateAliveNum()
	if mem.NeedKill() {
		t.ChangeAliveNum(1, mem, triggerEvent)
	}
}

func (t *Team) IsFinish() bool {
	return t.alive <= 0
}

// /=========开始目标选择================//
func (t *Team) GetAll(targets *Uniters) {
	for _, v := range t.members {
		if v.CanBeChoose(TargetNeedAlive) {
			targets.AddUniter(v)
		}
	}
	for _, v := range t.voidMembers {
		if v.CanBeChoose(TargetNeedAlive) {
			targets.AddUniter(v)
		}
	}
}

func (t *Team) GetAllIncludeExile(targets *Uniters) {
	for _, v := range t.members {
		if v.CanBeChooseIncludeExile(TargetNeedAlive) {
			targets.AddUniter(v)
		}
	}
	for _, v := range t.voidMembers {
		if v.CanBeChooseIncludeExile(TargetNeedAlive) {
			targets.AddUniter(v)
		}
	}
}

// 全体英雄生命值上限之和
func (t *Team) GetAllHeroHpSum() int64 {
	var hpSum int64
	for _, mem := range t.members {
		if mem.CanBeChoose(TargetNeedAlive) {
			hp := mem.GetAttr(goxml.AttrHp)
			hpSum += hp
		}
	}

	return hpSum
}

func (t *Team) GetDeadAll(targets *Uniters) {
	for _, v := range t.members {
		if v.GetStatus() == UniterDead {
			targets.AddUniter(v)
		}
	}
}

// 所有活着的member
func (t *Team) GetAliveAll(targets *Uniters) {
	for _, v := range t.members {
		if v.CanBeChoose(TargetNeedAlive) {
			targets.AddUniter(v)
		}
	}
}

func (t *Team) GetJob(job uint32, targets *Uniters) {
	for _, v := range t.members {
		if v.CanBeChoose(TargetNeedAlive) && v.GetJob() == job {
			targets.AddUniter(v)
		}
	}
}

func (t *Team) GetRace(race uint32, targets *Uniters) {
	for _, v := range t.members {
		if v.CanBeChoose(TargetNeedAlive) && v.GetRace() == race {
			targets.AddUniter(v)
		}
	}
}

type CmpUniter func(Uniter, Uniter) bool

func sortTargetByFn(ts *Uniters, fn CmpUniter) {
	for i := 1; i < ts.GetNum(); i++ {
		for j := i; j > 0; j-- {
			if fn(ts.GetUniters()[j-1], ts.GetUniters()[j]) {
				break
			}
			ts.GetUniters()[j-1], ts.GetUniters()[j] = ts.GetUniters()[j], ts.GetUniters()[j-1]
		}
	}
}

func (t *Team) BuffTypeEffect(buffType uint32, effectType int, effectValue uint32) {
	buffEffect := t.effects.buffTypeEffects[buffType]
	if buffEffect == nil {
		buffEffect = &BuffEffect{
			multiLayer: goxml.BaseUInt32,
		}
		t.effects.buffTypeEffects[buffType] = buffEffect
	}
	switch effectType {
	case BuffTypeEffectMaxLayerAdd:
		buffEffect.addLayer += effectValue
	case BuffTypeEffectMaxLayerMulti:
		buffEffect.multiLayer += effectValue
	case BuffTypeEffectRoundAdd:
		buffEffect.fixTurns += effectValue
	}
}

func (t *Team) GetBuffTypeEffectMaxLayer(buffType uint32) uint32 {
	buffTypeInfo := goxml.GetData().BuffTypeInfoM.Index(buffType)
	if buffTypeInfo == nil {
		panic(fmt.Sprintf("battle no find buff type :%d", buffType))
	}
	if buffEffect, exist := t.effects.buffTypeEffects[buffType]; exist {
		return buffEffect.GetMaxLayer(buffTypeInfo)
	}
	return buffTypeInfo.MaxAdd
}

func (t *Team) GetBuffTypeEffectFixTurns(buffType uint32) uint32 {
	if buffEffect, exist := t.effects.buffTypeEffects[buffType]; exist {
		return buffEffect.fixTurns
	}
	return 0
}

func (t *Team) AddSingleBuff(buffType uint32, owner Uniter, iexe IExecution, attack Uniter) {
	if t.effects.singleBuffs == nil {
		t.effects.singleBuffs = make(map[uint32]Uniter)
	} else {
		//删除队伍中的老buff
		if oldOwner, exist := t.effects.singleBuffs[buffType]; exist {
			oldOwner.GetBuffM().removeBuffByType(buffType, iexe, attack)
		}
	}
	t.effects.singleBuffs[buffType] = owner
}

func (t *Team) EffectLinkNum(linkID uint32, num uint32, mem *Member) {
	sysID := mem.GetHeroSysID()
	isChangeLink, changeType := goxml.GetData().LinkInfoM.IsLinkTypeChange(linkID)
	fixLink := mem.GetOmniLinkByType(changeType)
	// 当前羁绊属可变羁绊，且当前全能英雄选择自动时，认定为自动激活可变羁绊
	if isChangeLink && fixLink == 0 {
		t.changeTypeLinkNum[changeType] += num

		if _, exist := t.heroChangeTypeLinks[sysID]; !exist {
			t.heroChangeTypeLinks[sysID] = make([]uint32, 0, 1)
		}
		t.heroChangeTypeLinks[sysID] = append(t.heroChangeTypeLinks[sysID], changeType)
	} else {
		// 全能英雄自选羁绊有值时，认定为激活自选羁绊
		if fixLink > 0 {
			// 验证所选羁绊是否可用
			if !goxml.GetData().LinkInfoM.IsAvailableOmniLink(fixLink, t.m.battleParams.GetSeasonID()) {
				return
			}
			linkID = fixLink
		}
		t.effects.linkInfo[linkID] += num

		if _, exist := t.heroLinkInfo[sysID]; !exist {
			t.heroLinkInfo[sysID] = make(map[uint32]util.None)
		}
		t.heroLinkInfo[sysID][linkID] = util.None{}
	}
}

func (t *Team) addAllLink() {
	if t.effects.linkInfo == nil {
		t.effects.linkInfo = make(map[uint32]uint32)
	}
	if t.heroLinkInfo == nil {
		t.heroLinkInfo = make(map[uint32]map[uint32]util.None)
	}
	if t.changeTypeLinkNum == nil {
		t.changeTypeLinkNum = make(map[uint32]uint32)
	}
	if t.heroChangeTypeLinks == nil {
		t.heroChangeTypeLinks = make(map[uint32][]uint32)
	}
	for _, mem := range t.initMembers {
		for linkId, num := range mem.Base.Links {
			t.EffectLinkNum(linkId, num, mem)
		}
	}
}

// 计算自适应羁绊
func (t *Team) calcChangeLink() {
	if len(t.changeTypeLinkNum) == 0 {
		return
	}

	// 统计不同类型羁绊，激活数最大的那个羁绊
	topLinks := goxml.GetData().LinkInfoM.CalcTopLinks(t.effects.linkInfo)

	// 确认自适应羁绊最终羁绊效果，更新羁绊激活数
	finalLinks := make(map[uint32]uint32, len(t.changeTypeLinkNum))
	for typ, count := range t.changeTypeLinkNum {
		id := topLinks[typ]
		if id == 0 {
			continue
		}

		t.effects.linkInfo[id] += count
		finalLinks[typ] = id
	}

	// 更新英雄激活羁绊数据
	for sysID, types := range t.heroChangeTypeLinks {
		for typ, linkID := range finalLinks {
			if util.InUint32s(types, typ) {
				if _, exist := t.heroLinkInfo[sysID]; !exist {
					t.heroLinkInfo[sysID] = make(map[uint32]util.None)
				}
				t.heroLinkInfo[sysID][linkID] = util.None{}
			}
		}
	}
}

func (t *Team) activedLink() {
	//添加羁绊类型统计
	for linkID := range t.effects.linkInfo {
		info := goxml.GetData().LinkInfoM.GetRecordByLinkId(linkID)
		if info == nil {
			panic(fmt.Sprintf("activedLink: no link info, linkID:%d", linkID))
		}
		//每个linkID只统计一次
		t.addLinkTypeCount(info.LinkSubtype)
	}

	numReduceLinkIds := t.getLinkActiveNumReduceLinkIds()

	//添加激活的羁绊技能
	t.activedLinks = goxml.GetData().LinkSkillInfoM.GetActiveLinks(t.effects.linkInfo,
		t.m.battleParams.GetAttackFID(), isCheckSeason(), numReduceLinkIds)
	for _, linkSkill := range t.activedLinks {
		for _, psSkill := range linkSkill.PassiveSkills {
			t.tUniter.AddPsSkill(psSkill)
		}

		//设置羁绊能量上限值 --- 因s6科技树的影响，后续仅在初始时设置个默认的能量上限，此处屏蔽
		// if t.m.IsCurrentSeasonLink(linkSkill.LinkId) {
		// 	//XXX: battle_para_info表中id，必须与linkId一致，否则会出错
		// 	t.setMaxEnergy(linkSkill.LinkId)
		// }
	}
}

// getActiveNumReduceLinkIds
// @Description: 获取羁绊激活数量减少的羁绊ID列表
// @receiver t
// @return []uint32
func (t *Team) getLinkActiveNumReduceLinkIds() []uint32 {
	var talentTreeNodeLevels map[uint32]uint32
	if t.IsAttackTeam() {
		if t.m.battleParams.attackTalentTree != nil {
			talentTreeNodeLevels = t.m.battleParams.attackTalentTree.Levels
		}
	} else if t.IsDefenseTeam() {
		if t.m.battleParams.defenseTalentTree != nil {
			talentTreeNodeLevels = t.m.battleParams.defenseTalentTree.Levels
		}
	}
	if talentTreeNodeLevels == nil {
		return nil
	}
	nodeIds := goxml.GetData().SeasonTalentTreeBaseInfoM.GetAdditionTypeNodes(t.m.battleParams.GetSeasonID(), goxml.TalentTreeNodeLevelAdditionLinkHeroNumReduce)
	if len(nodeIds) == 0 {
		return nil
	}
	linkIds := make([]uint32, 0, len(nodeIds))
	for _, id := range nodeIds {
		baseInfo := goxml.GetData().SeasonTalentTreeBaseInfoM.GetRecordBySeasonIdId(t.m.battleParams.GetSeasonID(), id)
		if baseInfo.SeasonLink == 0 {
			continue
		}
		level := talentTreeNodeLevels[id]
		lvInfo := goxml.GetData().SeasonTalentTreeLevelInfoM.GetRecordByIdLevel(id, level)
		if lvInfo == nil {
			continue
		}
		if len(lvInfo.PassiveParams) > 0 && lvInfo.PassiveParams[0] == 1 {
			linkIds = append(linkIds, baseInfo.SeasonLink)
		}
	}
	return linkIds
}

func (t *Team) GetHeroLinkInfo() map[uint32]map[uint32]util.None {
	return t.heroLinkInfo
}

func (t *Team) GetLinkInfo() map[uint32]uint32 {
	return t.effects.linkInfo
}

func (t *Team) GetArtifactLinkInfo() map[uint32]map[uint32]util.None {
	return t.artifactLinkInfo
}

// 召唤物死亡(直接清除,不管下回合能不能复活)
func (t *Team) OnCallDead(callID uint32) {
	for index, mem := range t.members {
		if callID == mem.ID() {
			if initMem := t.getInitMemberByPos(mem.Pos()); initMem != nil {
				t.m.AddActMember(initMem)
				t.members[index] = initMem
			} else {
				t.members = append(t.members[:index], t.members[index+1:]...)
			}
			break
		}
	}
	t.m.RemoveActMember(callID)
}

func (t *Team) EffectArtifactLinkNum(linkID uint32, num uint32, uniter Uniter) {
	if t.effects.artifactLinkInfo == nil {
		t.effects.artifactLinkInfo = make(map[uint32]uint32)
	}
	if t.artifactLinkInfo == nil {
		t.artifactLinkInfo = make(map[uint32]map[uint32]util.None)
	}
	t.effects.artifactLinkInfo[linkID] += num
	if uniter.GetUniterType() == UniterTypeArtifact {
		artifact := uniter.(*Artifact)
		sysID := artifact.GetSysID()
		if linkMap, exist := t.artifactLinkInfo[sysID]; exist {
			linkMap[linkID] = util.None{}
		} else {
			linkMap = make(map[uint32]util.None)
			linkMap[linkID] = util.None{}
			t.artifactLinkInfo[sysID] = linkMap
		}
	}
}

func (t *Team) activedArtifactLink() {
	t.activedArtifactLinks = goxml.GetData().ArtifactLinkSkillInfoM.GetActivedLinks(t.effects.artifactLinkInfo)
	for _, linkSkill := range t.activedArtifactLinks {
		for _, psSkill := range linkSkill.PassiveSkills {
			t.tUniter.AddPsSkill(psSkill)
		}
	}
}

func (t *Team) AddShareDamage(b *Buff) {
	if t.shareDamageMembers == nil {
		t.shareDamageMembers = make(map[uint32][]Uniter)
	}
	t.shareDamageMembers[b.attack.ID()] = append(t.shareDamageMembers[b.attack.ID()], b.owner)
}
func (t *Team) RemoveShareDamage(b *Buff) {
	for index, member := range t.shareDamageMembers[b.attack.ID()] {
		if member.ID() == b.owner.ID() {
			t.shareDamageMembers[b.attack.ID()] = append(t.shareDamageMembers[b.attack.ID()][:index], t.shareDamageMembers[b.attack.ID()][index+1:]...)
		}
	}
}
func (t *Team) GetShareDamageUniters(b *Buff) []Uniter {
	return t.shareDamageMembers[b.attack.ID()]
}

func (t *Team) GetLeftHpPct() float64 {
	var leftHpPct float64
	for _, mem := range t.initMembers {
		leftHpPct += float64(mem.hp) / float64(mem.showMaxHp)
	}
	return leftHpPct
}
func (t *Team) GetArtifactM() *ArtifactManager {
	return t.artifactM
}

// 设置赛季羁绊能量上限值
func (t *Team) setMaxEnergy(linkID uint32) {
	t.maxEnergy = para(linkID)
}

// 初始化当前回合添加的能量值(正负)
func (t *Team) initRoundAddEnergy() {
	t.roundAddEnergy = 0
	t.roundAddNegEnergy = 0
}

func (t *Team) getRoundAddEnergy(isNegative bool) int64 {
	if isNegative {
		return t.roundAddNegEnergy
	} else {
		return t.roundAddEnergy
	}
}

func (t *Team) setRoundAddEnergy(energy int64, isNegative bool) {
	if isNegative {
		t.roundAddNegEnergy = energy
	} else {
		t.roundAddEnergy = energy
	}
}

// 增加赛季羁绊信仰点
func (t *Team) AddSeasonLinkEnergy(energy int64, attack, defense Uniter, iexe IExecution, isNegative bool) {
	// pvp玩法处理单回合能量上限逻辑 - 正能量的增加
	if !isNegative && t.m.battleParams.IsPVP() {
		energy = t.seasonLinkEnergyRoundAddLimit(energy, isNegative)
		if energy == 0 {
			return
		}
	}

	t.energy = t.energy + energy
	if t.energy > t.maxEnergy {
		energy = energy - (t.energy - t.maxEnergy)
		t.energy = t.maxEnergy
	}
	t.m.report.NewAddSeasonLinkEnergy(iexe, energy, attack, defense)
	t.seasonLinkEnergyTrigger()
}

// 减少赛季羁绊信仰点
func (t *Team) DecSeasonLinkEnergy(energy int64, attack, defense Uniter, iexe IExecution, isNegative bool) {
	// pvp玩法处理单回合能量上限逻辑 - 负能量的增加
	if isNegative && t.m.battleParams.IsPVP() {
		energy = t.seasonLinkEnergyRoundAddLimit(energy, isNegative)
		if energy == 0 {
			return
		}
	}

	t.energy -= energy
	if t.energy < 0 {
		negMaxEnergy := 0 - t.maxEnergy
		if t.energy < negMaxEnergy {
			energy = t.energy + energy + t.maxEnergy
			t.energy = negMaxEnergy
		}
	}
	t.m.report.NewDecSeasonLinkEnergy(iexe, energy, attack, defense)
	t.seasonLinkEnergyTrigger()
}

// 赛季羁绊单回合能量上限修正
// @param int64 energy 要加的能量值(正负)
// @param bool isNegative 是否是负能量
// @return int64 修正后，要加的能量值
func (t *Team) seasonLinkEnergyRoundAddLimit(energy int64, isNegative bool) int64 {
	roundEnergyLimit := para(7004)
	roundAddEnergy := t.getRoundAddEnergy(isNegative)
	if energy+roundAddEnergy > roundEnergyLimit {
		energy = roundEnergyLimit - roundAddEnergy
	}
	if energy > 0 {
		t.setRoundAddEnergy(roundAddEnergy+energy, isNegative)
	}
	return energy
}

// 赛季羁绊相关事件触发
func (t *Team) seasonLinkEnergyTrigger() {
	if t.energy == 0 {
		return
	}

	event := goxml.Event_SeasonLinkEnergyReach
	if t.energy < 0 {
		event = goxml.Event_SeasonLinkNegEnergyReach
	}
	absEnergy := uint32(math.Abs(float64(t.energy)))
	for _, mem := range t.members {
		t.m.TriggerPsSkill(mem, event, absEnergy)
	}
	t.m.TriggerPsSkill(t.tUniter, event, absEnergy)
}

// 获取赛季羁绊信仰点
func (t *Team) GetEnergy() int64 {
	return t.energy
}

func (t *Team) AddBigSkillCount(round uint32) {
	t.bigSkillCount[round]++
}

func (t *Team) GetBigSkillCount(round uint32) uint32 {
	return t.bigSkillCount[round]
}

func (t *Team) addLinkTypeCount(linkType uint32) {
	t.linkTypeCount[linkType] += 1
}

func (t *Team) GetLinkTypeCount(linkType uint32) uint32 {
	return t.linkTypeCount[linkType]
}

func (t *Team) addCallCount() {
	t.callCount++
}

func (t *Team) GetCallCount() uint32 {
	return t.callCount
}

func (t *Team) AddAccumulateHurt(hurt uint64, attack, defense Uniter, iexe IExecution) {
	t.accumulateHurt += hurt
	t.m.report.NewAddAccumulateHurt(iexe, int64(hurt), attack, defense)
}

func (t *Team) DecAccumulateHurt(hurt uint64, attack, defense Uniter, iexe IExecution) {
	if hurt > t.accumulateHurt {
		hurt = t.accumulateHurt
	}
	t.accumulateHurt -= hurt
	t.m.report.NewDecAccumulateHurt(iexe, int64(hurt), attack, defense)
}

func (t *Team) GetAccumulateHurt() uint64 {
	return t.accumulateHurt
}

func (t *Team) setAutoID(id uint32) {
	t.autoID = id
}

// 计算成员id
// 非召唤物，直接用pos做id
// 召唤物，统一从FirstCallID开始自增
// @param uint32 pos 位置
// @param bool isCall 是否是召唤物
// @return uint32
func (t *Team) calcMemID(pos uint32, isCall bool) uint32 {
	if !isCall {
		return pos
	}

	if t.autoID < FirstCallID {
		return FirstCallID
	} else {
		return t.autoID + 1
	}
}

// 生成成员id
// @param uint32 pos 位置
// @param bool isCall 是否是召唤物
// @return uint32
func (t *Team) NewID(pos uint32, isCall bool) uint32 {
	id := t.calcMemID(pos, isCall)
	if id > t.autoID {
		t.setAutoID(id)
	}
	return t.index*100 + id
}

func (t *Team) GetPsShareCD(psSkillID uint32) uint32 {
	return t.psShareCD[psSkillID]
}

func (t *Team) SetPsShareCD(psSkillID uint32, round uint32) {
	t.psShareCD[psSkillID] = round
}
