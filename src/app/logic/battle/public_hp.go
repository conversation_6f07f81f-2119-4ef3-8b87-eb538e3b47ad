package battle

import (
	"app/goxml"
	"app/protos/out/bt"
	"fmt"
)

type PublicHpGroup struct {
	eliteType uint32
	mgr       *Manager
	uniters   *Uniters
	hp        int64
	maxHp     int64
}

func NewPublicHpGroup(mgr *Manager) *PublicHpGroup {
	g := &PublicHpGroup{
		mgr:     mgr,
		uniters: gPool.GetUniters(),
	}
	return g
}

func (g *PublicHpGroup) AddUniter(mem *Member) {
	g.hp += mem.hp
	g.maxHp += mem.maxHp
	if mem.Base.EliteType > 0 { // 这里不能用mem.EliteType()方法，这个时候mem.EliteType()返回的肯定是0
		g.eliteType = 1
	}
	g.uniters.AddUniter(mem)
}

func (g *PublicHpGroup) GetHp() int64 {
	return g.hp
}

func (g *PublicHpGroup) GetMaxHp() int64 {
	return g.maxHp
}

func (g *PublicHpGroup) IsDead() bool {
	return g.hp == 0
}

func (g *PublicHpGroup) AddHp(value int64) (int64, int64) {
	g.hp += value
	var over int64
	if g.hp > g.maxHp {
		over = g.hp - g.maxHp
		g.hp = g.maxHp
	}
	value = value - over
	totalNum := g.uniters.GetNum()
	uniterArray := g.uniters.GetUniters()
	for i := 0; i < totalNum; i++ {
		uniter := uniterArray[i]
		uniter.GetAttrM().ChangeHpAttr()
	}
	return value, over
}

func (g *PublicHpGroup) DecHp(value int64) (int64, int64) {
	var overflow int64
	if g.hp >= value {
		g.hp -= value
	} else {
		overflow = value - g.hp
		value = g.hp
		g.hp = 0
	}
	totalNum := g.uniters.GetNum()
	uniterArray := g.uniters.GetUniters()
	for i := 0; i < totalNum; i++ {
		uniterArray[i].AfterDecHp(value)
	}
	return value, overflow
}

func (g *PublicHpGroup) CheckDead(attack Uniter, hurtType bt.SpecialType, hurtEffect *bt.HurtEffect) bool {
	if g.hp == 0 && (hurtType == bt.SpecialType_HurtTypeDoKill || hurtType == bt.SpecialType_HurtTypeDevour ||
		hurtType == bt.SpecialType_HurtTypeKillNotDead || hurtType == bt.SpecialType_HurtTypeKillIgnoreLockBlood) {
		totalNum := g.uniters.GetNum()
		uniterArray := g.uniters.GetUniters()
		for i := 0; i < totalNum; i++ {
			uniter := uniterArray[i]
			if uniter.IsNoDead3() {
				g.hp = 1
				return false
			}
		}

		//斩杀情况下直接死亡
		g.Dead(hurtEffect)
		return true
	}
	if g.hp == 0 {
		totalNum := g.uniters.GetNum()
		uniterArray := g.uniters.GetUniters()
		for i := 0; i < totalNum; i++ {
			uniter := uniterArray[i]
			g.mgr.TriggerPsSkill(uniter, goxml.Event_DeadHurt, attack)
			if g.hp != 0 {
				return false
			}
			if uniter.IsNoDead() {
				if g.hp == 0 {
					g.hp = 1
				}
				return false
			}
		}
		if g.hp == 0 {
			g.Dead(hurtEffect)
			return true
		}
	}
	return false
}

// 死亡
func (g *PublicHpGroup) Dead(hurtEffect *bt.HurtEffect) {
	totalNum := g.uniters.GetNum()
	uniterArray := g.uniters.GetUniters()
	for i := 0; i < totalNum; i++ {
		uniterArray[i].Dead(hurtEffect)
	}
}

func (g *PublicHpGroup) EliteType() uint32 {
	return g.eliteType
}

func (g *PublicHpGroup) UpdateHp() {
	g.hp = 0
	g.maxHp = 0
	totalNum := g.uniters.GetNum()
	uniterArray := g.uniters.GetUniters()
	for i := 0; i < totalNum; i++ {
		uniter, ok := uniterArray[i].(*Member)
		if !ok {
			panic(fmt.Sprintf("PublicHpGroup.UpdateHp, uniter not member, index:%d id:%d", uniter.Index(), uniter.ID()))
		}
		g.hp += uniter.GetSelfHp()
		g.maxHp += uniter.GetSelfMaxHp()
	}
}
