package battle

import (
	"app/goxml"
	"app/protos/out/bt"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

//nolint:funlen
func NewMonsterGroupTeam(groupID, riteRestrictState uint32, teamIndex int) (*Team, map[uint32][]uint64) {
	monsterGroup := goxml.GetData().MonsterGroupInfoM.Index(groupID)
	if monsterGroup == nil {
		l4g.Errorf("monster group no find id: %d", groupID)
		return nil, nil
	}
	members := make([]*Member, 0, TeamMaxPos)
	voidMembers := make([]*VoidMember, 0, 1)
	voidIds := make(map[uint32]uint32)
	isCalculateLink := monsterGroup.Link == 1
	canAddLinkMap := make(map[uint32]util.None)
	if isCalculateLink {
		sameHeroStar := make(map[uint32][]uint32) //heroId => [id,star]
		//排除重复的英雄,最高星级的那个才有羁绊
		for i := uint32(1); i <= uint32(TeamMaxPos); i++ {
			if id, exist := monsterGroup.Monsters[i]; exist {
				monster := goxml.GetData().MonsterInfoM.Index(id)
				if monster == nil || monster.EtherBind != 0 {
					continue
				}
				if values, exist := sameHeroStar[monster.HeroId]; exist {
					if monster.Star > values[1] {
						values[0] = monster.Id
						values[1] = monster.Star
					}
				} else {
					sameHeroStar[monster.HeroId] = append(sameHeroStar[monster.HeroId], monster.Id, monster.Star)
				}
			}
		}
		for _, values := range sameHeroStar {
			canAddLinkMap[values[0]] = util.None{}
		}
	}

	for i := uint32(1); i <= uint32(TeamMaxPos); i++ {
		if id, exist := monsterGroup.Monsters[i]; exist {
			monster := goxml.GetData().MonsterInfoM.Index(id)
			if monster == nil {
				continue
			}
			if monster.EtherBind == 0 {
				var activeLink bool
				if _, exist := canAddLinkMap[id]; exist {
					activeLink = true
				}

				member, _ := NewMonster(&MemberBase{
					UniqID: uint64(id), Pos: i,
				}, activeLink)
				if member == nil {
					return nil, nil
				}
				members = append(members, member)
			} else {
				voidIds[i] = id
			}
		}
	}
	GetMember := func(sysID uint32) *Member {
		for _, m := range members {
			if m.GetSysID() == sysID {
				return m
			}
		}
		return nil
	}
	for pos, id := range voidIds {
		monster := goxml.GetData().MonsterInfoM.Index(id)
		if monster == nil {
			continue
		}
		boss := GetMember(monster.EtherBind)
		if boss == nil {
			l4g.Errorf("NewMonsterGroupTeam error: VoidMember has not etherbind boss, groupID:%d, id:%d", groupID, id)
			return nil, nil
		}
		voidMember := NewVoid(id, pos, boss)
		if voidMember == nil {
			return nil, nil
		}
		voidMembers = append(voidMembers, voidMember)
	}

	artifacts := make([]*bt.ArtifactInfo, 0, len(monsterGroup.Artifacts))
	for i, info := range monsterGroup.Artifacts {
		artifacts = append(artifacts, &bt.ArtifactInfo{
			Pos:   uint32(i + 1),
			SysId: info.SysId,
			Star:  info.Star,
		})
	}

	var raisePSsMap map[uint32][]uint64
	riteMonsterGroupInfo := goxml.GetData().RiteMonsterGroupInfoM.Index(groupID)
	if riteMonsterGroupInfo != nil {
		raisePSsMap = GetRiteRaisePSsMap(riteMonsterGroupInfo.RiteId, riteMonsterGroupInfo.Rare, riteRestrictState)
	}

	if len(members) > 0 {
		team := NewTeam(members, artifacts, voidMembers, &TeamBase{
			IsCalculateLink: isCalculateLink,
			Power:           monsterGroup.Power,
			TeamIndex:       uint32(teamIndex),
		}, nil)
		return team, raisePSsMap
	}
	return nil, nil
}

func NewVoid(id uint32, pos uint32, boss *Member) *VoidMember {
	if id == 0 {
		return nil
	}
	monster := goxml.GetData().MonsterInfoM.Index(id)
	if monster == nil {
		l4g.Errorf("NewVoid error no find monster pos:%d id:%d", pos, id)
		return nil
	}
	member := NewVoidMember(uint64(monster.Id), pos, monster.HeroId, boss)
	if member == nil {
		l4g.Errorf("NewVoid error monster nil pos:%d id:%d", pos, id)
		return nil
	}
	member.SetSysID(id)

	for _, s := range monster.PassiveSkills {
		member.AddNewSysPassiveSkill(s)
	}
	return member
}

func NewMonster(base *MemberBase, isCalculateLink bool) (*Member, *goxml.MonsterInfoEx) {
	if base.UniqID == 0 {
		return nil, nil
	}
	monster := goxml.GetData().MonsterInfoM.Index(uint32(base.UniqID))
	if monster == nil {
		l4g.Errorf("Monster new member error no find monster pos:%d id:%d", base.Pos, base.UniqID)
		return nil, nil
	}
	attr := monster.GetAttr(goxml.GetData())
	if len(attr) == 0 {
		l4g.Errorf("monster GetAttr nil: %d", monster.Id)
		return nil, nil
	}
	base.SysID = monster.HeroId
	base.Stage = monster.Stage
	base.Star = monster.Star
	base.Level = monster.Level
	base.SkillLvl = monster.SkillLevel
	base.EliteType = monster.IsElite
	base.MonsterType = monster.Type
	base.PublicHp = monster.PublicHp
	base.NeedNotKill = monster.NeedNotKill == 1
	if isCalculateLink {
		base.Links = monster.LinkInfo
	}
	member := NewMember(base, attr)
	if member == nil {
		l4g.Errorf("Monster new member error monster nil pos:%d id:%d", base.Pos, base.SysID)
		return nil, nil
	}
	for _, s := range monster.PassiveSkills {
		member.AddNewSysPassiveSkill(s)
	}
	if len(base.Emblems) > 0 {
		raisePs := getEmblemRaisePs(base.SysID, base.Emblems)
		if raisePs != 0 {
			member.Base.RaisePSs = append(member.Base.RaisePSs, raisePs)
		}
	}
	return member, monster
}

func getEmblemRaisePs(heroID uint32, emblems []*cl.SimpleEmblemInfo) uint64 {
	heroInfo := goxml.GetData().HeroInfoM.Index(heroID)
	if heroInfo == nil {
		l4g.Errorf("heroInfo not exist. hero id:%d", heroID)
		return 0
	}
	var activeSkill, exEmblemRedNum, exEmblemOrangeNum uint32
	for _, emblem := range emblems {
		if emblem == nil || emblem.SysId == 0 {
			continue
		}
		info := goxml.GetData().EmblemInfoM.Index(emblem.SysId)
		if info == nil {
			l4g.Errorf("EmblemInfoM not exist. emblem sys id:%d", emblem.SysId)
			continue
		}
		if heroID == emblem.AdditiveHero {
			if info.Rare >= goxml.EmblemRareRed {
				exEmblemRedNum++
			} else if info.Rare >= goxml.EmblemRareOrange {
				exEmblemOrangeNum++
			}
		}
	}

	if exEmblemRedNum == goxml.EmblemExclusiveNumFour && heroInfo.EmblemExclusive3 > 0 {
		activeSkill = heroInfo.EmblemExclusive3
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumThree {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumOne && heroInfo.EmblemExclusive2 > 0 {
			activeSkill = heroInfo.EmblemExclusive2
		}
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumTwo {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumTwo && heroInfo.EmblemExclusive2 > 0 {
			activeSkill = heroInfo.EmblemExclusive2
		}
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumOne {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumThree && heroInfo.EmblemExclusive1 > 0 {
			activeSkill = heroInfo.EmblemExclusive1
		}
	} else if exEmblemOrangeNum == goxml.EmblemExclusiveNumFour && heroInfo.EmblemExclusive1 > 0 {
		activeSkill = heroInfo.EmblemExclusive1
	}
	if activeSkill != 0 {
		raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(activeSkill, 1)
		if raisePSInfo == nil {
			l4g.Errorf("hero:%d emblem raise passive skillId:%d count: 1 is nil",
				heroID, activeSkill)
			return 0
		}
		return raisePSInfo.ID
	}
	return 0
}
