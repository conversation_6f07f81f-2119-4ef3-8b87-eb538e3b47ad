package battle

import (
	"app/goxml"
	"app/protos/out/bt"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

//nolint:funlen
func NewMonsterGroupTeam(groupID, riteRestrictState uint32, teamIndex int) (*Team, map[uint32][]uint64) {
	monsterGroup := goxml.GetData().MonsterGroupInfoM.Index(groupID)
	if monsterGroup == nil {
		l4g.Errorf("monster group no find id: %d", groupID)
		return nil, nil
	}
	members := make([]*Member, 0, TeamMaxPos)
	voidMembers := make([]*VoidMember, 0, 1)
	voidIds := make(map[uint32]uint32)
	isCalculateLink := monsterGroup.Link == 1
	canAddLinkMap := make(map[uint32]util.None)
	if isCalculateLink {
		sameHeroStar := make(map[uint32][]uint32) //heroId => [id,star]
		//排除重复的英雄,最高星级的那个才有羁绊
		for i := uint32(1); i <= uint32(TeamMaxPos); i++ {
			if id, exist := monsterGroup.Monsters[i]; exist {
				monster := goxml.GetData().MonsterInfoM.Index(id)
				if monster == nil || monster.EtherBind != 0 {
					continue
				}
				if values, exist := sameHeroStar[monster.HeroId]; exist {
					if monster.Star > values[1] {
						values[0] = monster.Id
						values[1] = monster.Star
					}
				} else {
					sameHeroStar[monster.HeroId] = append(sameHeroStar[monster.HeroId], monster.Id, monster.Star)
				}
			}
		}
		for _, values := range sameHeroStar {
			canAddLinkMap[values[0]] = util.None{}
		}
	}

	for i := uint32(1); i <= uint32(TeamMaxPos); i++ {
		if id, exist := monsterGroup.Monsters[i]; exist {
			monster := goxml.GetData().MonsterInfoM.Index(id)
			if monster == nil {
				continue
			}
			if monster.EtherBind == 0 {
				var activeLink bool
				if _, exist := canAddLinkMap[id]; exist {
					activeLink = true
				}

				member, _ := NewMonster(id, i, activeLink)
				if member == nil {
					return nil, nil
				}
				members = append(members, member)
			} else {
				voidIds[i] = id
			}
		}
	}
	GetMember := func(sysID uint32) *Member {
		for _, m := range members {
			if m.GetSysID() == sysID {
				return m
			}
		}
		return nil
	}
	for pos, id := range voidIds {
		monster := goxml.GetData().MonsterInfoM.Index(id)
		if monster == nil {
			continue
		}
		boss := GetMember(monster.EtherBind)
		if boss == nil {
			l4g.Errorf("NewMonsterGroupTeam error: VoidMember has not etherbind boss, groupID:%d, id:%d", groupID, id)
			return nil, nil
		}
		voidMember := NewVoid(id, pos, boss)
		if voidMember == nil {
			return nil, nil
		}
		voidMembers = append(voidMembers, voidMember)
	}

	artifacts := make([]*bt.ArtifactInfo, 0, len(monsterGroup.Artifacts))
	for i, info := range monsterGroup.Artifacts {
		artifacts = append(artifacts, &bt.ArtifactInfo{
			Pos:   uint32(i + 1),
			SysId: info.SysId,
			Star:  info.Star,
		})
	}

	var raisePSsMap map[uint32][]uint64
	riteMonsterGroupInfo := goxml.GetData().RiteMonsterGroupInfoM.Index(groupID)
	if riteMonsterGroupInfo != nil {
		raisePSsMap = GetRiteRaisePSsMap(riteMonsterGroupInfo.RiteId, riteMonsterGroupInfo.Rare, riteRestrictState)
	}

	if len(members) > 0 {
		team := NewTeam(members, artifacts, voidMembers, &TeamBase{
			IsCalculateLink: isCalculateLink,
			Power:           monsterGroup.Power,
			TeamIndex:       uint32(teamIndex),
		}, nil)
		return team, raisePSsMap
	}
	return nil, nil
}

func NewVoid(id uint32, pos uint32, boss *Member) *VoidMember {
	if id == 0 {
		return nil
	}
	monster := goxml.GetData().MonsterInfoM.Index(id)
	if monster == nil {
		l4g.Errorf("NewVoid error no find monster pos:%d id:%d", pos, id)
		return nil
	}
	member := NewVoidMember(uint64(monster.Id), pos, monster.HeroId, boss)
	if member == nil {
		l4g.Errorf("NewVoid error monster nil pos:%d id:%d", pos, id)
		return nil
	}
	member.SetSysID(id)

	for _, s := range monster.Skills {
		member.AddNewSysPassiveSkill(s)
	}
	return member
}

func NewMonster(id uint32, pos uint32, isCalculateLink bool) (*Member, *goxml.MonsterInfoEx) {
	if id == 0 {
		return nil, nil
	}
	monster := goxml.GetData().MonsterInfoM.Index(id)
	if monster == nil {
		l4g.Errorf("Monster new member error no find monster pos:%d id:%d", pos, id)
		return nil, nil
	}
	attr := monster.GetAttr(goxml.GetData())
	if len(attr) == 0 {
		l4g.Errorf("monster GetAttr nil: %d", monster.Id)
		return nil, nil
	}
	base := &MemberBase{
		UniqID: uint64(monster.Id), Pos: pos, SysID: id, Stage: monster.Stage,
		Star: monster.Star, Level: monster.Level, SkillLvl: monster.SkillLevel,
		EliteType: monster.IsElite, MonsterType: monster.Type, PublicHp: monster.PublicHp,
		NeedNotKill: monster.NeedNotKill == 1,
	}
	if isCalculateLink {
		base.Links = monster.LinkInfo
	}
	member := NewMember(base, attr)
	if member == nil {
		l4g.Errorf("Monster new member error monster nil pos:%d id:%d", pos, id)
		return nil, nil
	}
	for _, s := range monster.Skills {
		member.AddNewSysPassiveSkill(s)
	}
	return member, monster
}
