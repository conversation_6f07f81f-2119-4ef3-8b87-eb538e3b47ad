//nolint:mnd,varnamelen,wastedassign
package battle

import (
	"app/goxml"
	"fmt"
	"math"

	"gitlab.qdream.com/kit/sea/util"
)

type ConditionFunc func(*Manager, uint32, uint32, uint32, uint32, uint32, *PsSkillExe) bool

var gBattleConditions = [goxml.CondMax]ConditionFunc{
	goxml.CondOwnerHpLower:                      CondOwnerHpLower,
	goxml.CondDefenseHpLess:                     CondDefenseHpLess,
	goxml.CondDefenseHpMore:                     CondDefenseHpMore,
	goxml.CondDefenseJob:                        CondDefenseJob,
	goxml.CondAttackJob:                         CondAttackJob,
	goxml.CondDefenseBuffState:                  CondDefenseBuffState,
	goxml.CondDefenseNoBuffState:                CondDefenseNoBuffState,
	goxml.CondAttackBuffState:                   CondAttackBuffState,
	goxml.CondOwnerBuffState:                    CondOwnerBuffState,
	goxml.CondAttackNoBuffState:                 CondAttackNoBuffState,
	goxml.CondAttackIsControled:                 CondAttackIsControled,
	goxml.CondDefenseIsControled:                CondDefenseIsControled,
	goxml.CondDefenseBuffStateOrControled:       CondDefenseBuffStateOrControled,
	goxml.CondAttackBuffStateOrDefenseControled: CondAttackBuffStateOrDefenseControled,
	goxml.CondAttackHasCrit:                     CondAttackHasCrit,
	goxml.CondAttackHasOverCure:                 CondAttackHasOverCure,
	goxml.CondAttackHasNoCritAndOnlyOne:         CondAttackHasNoCritAndOnlyOne,
	goxml.CondAttackHasKill:                     CondAttackHasKill,
	goxml.CondAttackTargetNumLess:               CondAttackTargetNumLess,
	goxml.CondAttackHpMore:                      CondAttackHpMore,
	goxml.CondAttackHpLess:                      CondAttackHpLess,
	goxml.CondDefenseAttrLessOwner:              CondDefenseAttrLessOwner,
	goxml.CondDefenseAttrMoreOwner:              CondDefenseAttrMoreOwner,
	goxml.CondAttackHasNoCrit:                   CondAttackHasNoCrit,
	goxml.CondControlNumMore:                    CondControlNumMore,
	goxml.CondOwnerCanResurr:                    CondOwnerCanResurr,
	goxml.CondRoundLess:                         CondRoundLess,
	goxml.CondRoundGE:                           CondRoundGE,
	goxml.CondSkillGroupEqual:                   CondSkillGroupEqual,
	goxml.CondDefenseInOwnerTarget:              CondDefenseInOwnerTarget,
	goxml.CondDefenseCorruptionCountMore:        CondDefenseCorruptionCountMore,
	goxml.CondTriggerBuffIdCountMoreThan1:       CondTriggerBuffIDCountMoreThan1,
	goxml.CondAttackHurtOrCureLessDefenseHp:     CondAttackHurtOrCureLessDefenseHp,
	goxml.CondAttackHurtOrCureMoreDefenseHp:     CondAttackHurtOrCureMoreDefenseHp,
	goxml.CondAttackInOwnerTarget:               CondAttackInOwnerTarget,
	goxml.CondAttackBenefitBuffCount:            CondAttackBenefitBuffCount,
	goxml.CondAttackNoBenefitBuffCount:          CondAttackNoBenefitBuffCount,
	goxml.CondDefenseBenefitBuffCount:           CondDefenseBenefitBuffCount,
	goxml.CondDefenseNoBenefitBuffCount:         CondDefenseNoBenefitBuffCount,
	goxml.CondDefenseDefCompare:                 CondDefenseDefCompare,
	goxml.CondAttackRace:                        CondAttackRace,
	goxml.CondOwnerIsBenefitBuffCount:           CondOwnerIsBenefitBuffCount,
	goxml.CondOwnerHpHigher:                     CondOwnerHpHigher,
	goxml.CondDefenseRace:                       CondDefenseRace,
	goxml.CondOneSkillAttackHasKill:             CondOneSkillAttackHasKill,
	goxml.CondAttackTeamBenefitBuffCount:        CondAttackTeamBenefitBuffCount,
	goxml.CondResurrection:                      CondResurrection,
	goxml.CondAttackTeamLinkNum:                 CondAttackTeamLinkNum,
	goxml.CondDefenseTeamLinkNum:                CondDefenseTeamLinkNum,
	goxml.CondTeamAttrMoreEqual:                 CondTeamAttrMoreEqual,
	goxml.CondTeamResurrectionMoreEqual:         CondTeamResurrectionMoreEqual,
	goxml.CondTeamDeathNumMoreEqual:             CondTeamDeathNumMoreEqual,
	goxml.CondBuffLayerCountMoreEqual:           CondBuffLayerCountMoreEqual,
	goxml.CondBuffTypeCountMoreEqual:            CondBuffTypeCountMoreEqual,
	goxml.CondOwnerTeamLinkNum:                  CondOwnerTeamLinkNum,
	goxml.CondSameActionAddedBuffCount:          CondSameActionAddedBuffCount,
	goxml.CondHasPsAttr:                         CondHasPsAttr,
	goxml.CondOwnerDeathState:                   CondOwnerDeathState,
	goxml.CondOwnerNoBuffState:                  CondOwnerNoBuffState,
	goxml.CondLink:                              CondLink,
	goxml.CondSeasonEnergy:                      CondSeasonEnergy,
	goxml.CondIsTargetExist:                     CondIsTargetExist,
	goxml.CondCurRoundBigSkillCount:             CondCurRoundBigSkillCount,
	goxml.CondSeasonEnergyExtra:                 CondSeasonEnergyExtra,
	goxml.CondHeroStar:                          CondHeroStar,
	goxml.CondAttr:                              CondAttr,
	goxml.CondLinkTypeCount:                     CondLinkTypeCount,
	goxml.CondHeroSex:                           CondHeroSex,
	goxml.CondHavePsID:                          CondHavePsID,
	goxml.CondOwnerPos:                          CondOwnerPos,
	goxml.CondLeftMemAndNoMonsterCallHero:       CondLeftMemAndNoMonsterCallHero,
	goxml.CondHaveEmptyPosOrMonsterCall:         CondHaveEmptyPosOrMonsterCall,
	goxml.CondAliveNum:                          CondAliveNum,
	goxml.CondNoBuffID:                          CondNoBuffID,
	goxml.CondHeroID:                            CondHeroID,
	goxml.CondBuffLayerCountLess:                CondBuffLayerCountLess,
	goxml.CondBuffLayerRecord:                   CondBuffLayerRecord,
	goxml.CondBuffActiveSkillHurtEffective:      CondBuffActiveSkillHurtEffective,
	goxml.CondOwnerOpTeamLinkNum:                CondOpTeamLinkNum,
	goxml.CondAccumulateBeHurt:                  CondAccumulateBeHurt,
	goxml.CondCurrentRoundIsOddOrEven:           CondCurrentRoundIsOddOrEven,
	goxml.CondArtifactStar:                      CondArtifactStar,
}

// owner的血量低于xx%
func CondOwnerHpLower(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	hp := owner.GetHp()
	maxHp := owner.GetMaxHp()
	p := uint32(float64(hp) / float64(maxHp) * BaseFloat)
	b := p < conditionParam1
	if de() {
		desc := fmt.Sprintf("owner的血量(%d)/(%d)低于%d", hp, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseBuffState(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	count := defense.GetBuffM().GetBuffLayerByType(conditionParam1)
	b := count >= conditionParam2
	pse.condValue = int64(count)
	if de() {
		desc := fmt.Sprintf("defense的buff_type(%d)的层数(%d)>=(%d)", conditionParam1, count, conditionParam2)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseNoBuffState(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	b := !defense.HasBuff(conditionParam1)
	if de() {
		desc := fmt.Sprintf("defense没有buff type=(%d)状态", conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackBuffState(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	count := attack.GetBuffM().GetBuffLayerByType(conditionParam1)
	b := count >= conditionParam2
	pse.condValue = int64(count)
	if de() {
		desc := fmt.Sprintf("attack的buff_type(%d)的层数(%d)>=(%d)", conditionParam1, count, conditionParam2)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondOwnerBuffState(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	count := owner.GetBuffM().GetBuffLayerByType(conditionParam1)
	b := count > 0
	pse.condValue = int64(count)
	if de() {
		desc := fmt.Sprintf("owner处于buff type=(%d)状态", conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackNoBuffState(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	b := !attack.HasBuff(conditionParam1)
	if de() {
		desc := fmt.Sprintf("attack没有处于buff type=(%d)状态", conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseHpLess(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	hp := defense.GetHp()
	maxHp := defense.GetMaxHp()
	p := uint32(float64(hp) / float64(maxHp) * BaseFloat)
	pse.condValue = int64(p)
	b := p < conditionParam1
	if de() {
		desc := fmt.Sprintf("defense(%d)的血量(%d)/(%d)低于(%d)", defense.ID(), hp, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseHpMore(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	hp := defense.GetHp()
	maxHp := defense.GetMaxHp()
	p := uint32(float64(hp) / float64(maxHp) * BaseFloat)
	pse.condValue = int64(p)
	b := p >= conditionParam1
	if de() {
		desc := fmt.Sprintf("defense的血量(%d)/(%d)高于(%d)", hp, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func checkJob(owner Uniter, job, ConditionParam1 uint32) bool {
	b := false
	if job == 0 {
		return b
	}
	if ConditionParam1 < 100 {
		b = job == ConditionParam1
	} else if ConditionParam1 == 200 {
		//和自己职业职业
		myJob := owner.GetJob()
		b = (myJob == job)
	} else if ConditionParam1 == 201 {
		myJob := owner.GetJob()
		b = (myJob != job)
	} else if ConditionParam1 == 103 {
		b = (job == MemberJobTank || job == MemberJobWarrior)
	} else if ConditionParam1 == 104 {
		b = (job == MemberJobTank || job == MemberJobAssist)
	} else if ConditionParam1 == 203 {
		b = (job == MemberJobMagic || job == MemberJobWarrior)
	} else if ConditionParam1 == 204 {
		b = (job == MemberJobMagic || job == MemberJobAssist)
	}
	return b
}

func CondDefenseJob(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	defense := pse.GetDefense()
	job := defense.GetJob()
	b := checkJob(owner, job, conditionParam1)
	if de() {
		desc := fmt.Sprintf("defense的职业(%d)==(%d)", job, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackJob(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	attack := pse.GetAttack()
	job := attack.GetJob()
	b := checkJob(owner, job, conditionParam1)
	if de() {
		desc := fmt.Sprintf("attack的职业(%d)==(%d)", job, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackIsControled(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	b := false
	id := uint32(0)
	if attack.GetBuffM().GetHasControl() {
		b = true
	}
	if de() {
		desc := fmt.Sprintf("attack是否被控制,buffid(%d)", id)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseIsControled(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	b := false
	id := uint32(0)
	if defense.GetBuffM().GetHasControl() {
		b = true
	}
	if de() {
		desc := fmt.Sprintf("defense是否被控制,buffid(%d)", id)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseBuffStateOrControled(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	fn := func(b *Buff) bool {
		return b.IsControl() || b.info.Type == conditionParam1
	}
	b := defense.GetBuffM().CheckHasSomeBuff(fn)
	if de() {
		desc := fmt.Sprintf("defense是否被控制或者处于(%d)状态", conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackBuffStateOrDefenseControled(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	b := CondAttackBuffState(m, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4, pse) ||
		CondDefenseIsControled(m, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4, pse)
	if de() {
		desc := fmt.Sprintf("attack处于(%d)状态或者目标是否被控制", conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackHasCrit(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	iArgs, ok := pse.args.(IHasCrit)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasCrit", pse.debug()))
	}
	b := iArgs.GetHasCrit()
	if de() {
		desc := "attack是否暴击"
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackHasNoCrit(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	iArgs, ok := pse.args.(IHasCrit)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasCrit", pse.debug()))
	}
	b := !iArgs.GetHasCrit()
	if de() {
		desc := "attack是否没有暴击"
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackTeamOnlyOne(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	team := m.GetTeam(attack)
	num := team.alive
	b := num == 1
	if de() {
		desc := fmt.Sprintf("攻击方存活人数(%d)是否为1", num)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackHasNoCritAndOnlyOne(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	b := CondAttackHasNoCrit(m, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4, pse) &&
		CondAttackTeamOnlyOne(m, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4, pse)
	if de() {
		desc := "attack是否没有暴击cirt,攻击方仅仅存活1人"
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 目前只支持每次攻击，不支持其他的
func CondAttackHasOverCure(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	iArgs, ok := pse.args.(*OneSkillAttack)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasOverCure", pse.debug()))
	}
	b := iArgs.GetOverCure() > 0
	if de() {
		desc := "attack是否溢出治疗"
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackHasKill(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	iArgs, ok := pse.args.(IHasKill)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasKill", pse.debug()))
	}
	b := iArgs.GetHasKill()
	if de() {
		desc := "判断attack是否有击杀"
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackTargetNumLess(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	iArgs, ok := pse.args.(IHasTarget)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasTarget", pse.debug()))
	}
	num := iArgs.GetTargets().GetNum()
	b := num < int(conditionParam1)
	pse.condValue = int64(num)
	if de() {
		desc := fmt.Sprintf("当前能目标数量(%d)是否少于(%d)", num, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackHpMore(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	hp := attack.GetHp()
	maxHp := attack.GetMaxHp()
	p := uint32(float64(hp) / float64(maxHp) * BaseFloat)
	b := p >= conditionParam1
	pse.condValue = int64(p)
	if de() {
		desc := fmt.Sprintf("attack的血量(%d)/(%d)高于(%d)", hp, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackHpLess(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	hp := attack.GetHp()
	maxHp := attack.GetMaxHp()
	p := uint32(float64(hp) / float64(maxHp) * BaseFloat)
	pse.condValue = int64(p)
	b := p < conditionParam1
	if de() {
		desc := fmt.Sprintf("attack的血量(%d)/(%d)低于(%d)", hp, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseAttrLessOwner(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	defense := pse.GetDefense()
	pDef := int64(0)
	pAtk := int64(0)
	if conditionParam1 == 301 {
		pDef = defense.GetHp()
		pAtk = owner.GetHp()
	} else if conditionParam1 == 302 {
		pDef = int64(float64(defense.GetHp()) / float64(defense.GetMaxHp()) * BaseFloat)
		pAtk = int64(float64(owner.GetHp()) / float64(owner.GetMaxHp()) * BaseFloat)
	} else {
		pDef = defense.GetAttr(int(conditionParam1))
		pAtk = owner.GetAttr(int(conditionParam1))
	}
	b := pDef < pAtk
	pse.condValue = pDef
	if de() {
		desc := fmt.Sprintf("defense的属性(%d)的值(%d)是否低于owner的对应的属性值(%d)", conditionParam1, pDef, pAtk)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseAttrMoreOwner(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	defense := pse.GetDefense()
	pDef := int64(0)
	pAtk := int64(0)
	if conditionParam1 == 301 {
		pDef = defense.GetHp()
		pAtk = owner.GetHp()
	} else if conditionParam1 == 302 {
		pDef = int64(float64(defense.GetHp()) / float64(defense.GetMaxHp()) * BaseFloat)
		pAtk = int64(float64(owner.GetHp()) / float64(owner.GetMaxHp()) * BaseFloat)
	} else {
		pDef = defense.GetAttr(int(conditionParam1))
		pAtk = owner.GetAttr(int(conditionParam1))
	}
	b := pDef >= pAtk
	pse.condValue = pDef
	if de() {
		desc := fmt.Sprintf("defense的属性(%d)的值(%d)是否高于owner的对应的属性值(%d)", conditionParam1, pDef, pAtk)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondControlNumMore(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	attack := pse.GetAttack()

	var team *Team
	if conditionParam2 == 1 {
		team = m.GetTeam(attack)
	} else {
		team = m.GetOpTeam(attack)
	}

	p := uint32(0)
	for _, v := range team.members {
		if v.GetBuffM().GetHasControl() {
			p++
		}
	}
	b := p >= conditionParam1
	pse.condValue = int64(p)
	if de() {
		desc := fmt.Sprintf("cond26, (%d)方成员中被控制的数量(%d)大于(%d)", conditionParam2, p, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondOwnerCanResurr(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	b := !owner.IsNoAlive()
	if de() {
		desc := fmt.Sprintf("天赋拥有者是否能够复活(%v)", b)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondRoundLess(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	r := pse.GetRound()
	pse.SetCondValue(int64(r))
	b := r < conditionParam1
	if de() {
		desc := fmt.Sprintf("当前回合(%d)<指定回合(%d)", r, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}
func CondRoundGE(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	r := pse.GetRound()
	pse.SetCondValue(int64(r))
	b := r >= conditionParam1
	if de() {
		desc := fmt.Sprintf("当前回合(%d)>=指定回合(%d)", r, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}
func CondSkillGroupEqual(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	b := false
	group := uint32(0)
	if m.nowSkill != nil {
		group = m.nowSkill.info.SkillGroup
	}
	b = group == conditionParam1
	if de() {
		desc := fmt.Sprintf("skillGroup(%d)为指定值(%d)", group, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 队友或自己闪避攻击后，若受击方站位符合天赋拥有者【触发参数】，对【目标】用【公式】造成伤害
func CondDefenseInOwnerTarget(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	defense := pse.GetDefense()
	b := false
	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	m.GetTarget(owner, nil, conditionParam1, pse, targets)
	totalNum := targets.GetNum()
	for i := 0; i < totalNum; i++ {
		if targets.GetUniters()[i].ID() == defense.ID() {
			b = true
			break
		}
	}
	if de() {
		desc := fmt.Sprintf("defense(%d)在owner的目标(%d)的目标范围内(%d)", defense.ID(), owner.ID(), conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

//nolint:varnamelen
func CondDefenseCorruptionCountMore(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	b := false
	count := 0
	team := m.GetOpTeam(attack)
	for _, v := range team.members {
		if v.GetStatus() == UniterAlive && v.HasBuff(BuffTypeCorruption) {
			count++
		}
	}
	b = count >= int(conditionParam1)
	pse.condValue = int64(count)
	if de() {
		desc := fmt.Sprintf("对手有腐蚀的数量(%d)>目标目标数量(%d)", count, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondTriggerBuffIDCountMoreThan1(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	defense := pse.GetTriggerUniter()
	b := false
	fn := func(b *Buff) bool {
		return b.info.Id == conditionParam1
	}
	count := defense.GetBuffM().GetBuffLayer(fn)
	b = count > 1
	pse.condValue = int64(count)
	if de() {
		desc := fmt.Sprintf("触发者有指定的buff id(%d)的数量(%d)>1", conditionParam1, count)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 造成的总伤害/治疗低于受击方最大生命的百分比
func CondAttackHurtOrCureLessDefenseHp(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	p := pse.GetOneAttackHurtOrCure()
	defense := pse.GetDefense()
	maxHp := defense.GetMaxHp()
	baseHurt := int64(float64(maxHp) * (float64(conditionParam1) / BaseFloat))
	b := p < baseHurt
	if de() {
		desc := fmt.Sprintf("造成的总伤害/治疗(%d)低于受击方最大生命(%d)的百分比(%d)", p, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 造成的总伤害/治疗高于受击方最大生命的百分比
func CondAttackHurtOrCureMoreDefenseHp(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	p := pse.GetOneAttackHurtOrCure()
	defense := pse.GetDefense()
	maxHp := defense.GetMaxHp()
	baseHurt := int64(float64(maxHp) * (float64(conditionParam1) / BaseFloat))
	b := p > baseHurt
	if de() {
		desc := fmt.Sprintf("造成的总伤害/治疗(%d)高于受击方最大生命(%d)的百分比(%d)", p, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 若攻击方站位符合天赋拥有者【触发参数】，对【目标】用【公式】造成伤害
func CondAttackInOwnerTarget(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	attack := pse.GetAttack()
	b := false
	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	m.GetTarget(owner, nil, conditionParam1, pse, targets)
	totalNum := targets.GetNum()
	for i := 0; i < totalNum; i++ {
		if targets.GetUniters()[i].ID() == attack.ID() {
			b = true
			break
		}
	}
	if de() {
		desc := fmt.Sprintf("attack(%d)在owner的目标(%d)的目标范围内(%d)", attack.ID(), owner.ID(), conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackBenefitBuffCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	b := false
	fn := func(b *Buff) bool {
		return IsBenefitBuff(b.typeInfo)
	}
	count := attack.GetBuffM().GetBuffLayer(fn)
	pse.condValue = int64(count)
	if conditionParam2 > 0 {
		b = count < conditionParam1
		if de() {
			desc := fmt.Sprintf("attack增益buff数量(%d)是否小于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	} else {
		b = count >= conditionParam1
		if de() {
			desc := fmt.Sprintf("attack增益buff数量(%d)是否大于等于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	}
	return b
}

func CondAttackNoBenefitBuffCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	b := false
	fn := func(b *Buff) bool {
		return IsHarmfulBuff(b.typeInfo)
	}
	count := attack.GetBuffM().GetBuffLayer(fn)
	pse.condValue = int64(count)
	if conditionParam2 > 0 {
		b = count < conditionParam1
		if de() {
			desc := fmt.Sprintf("attack减益buff数量(%d)是否小于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	} else {
		b = count >= conditionParam1
		if de() {
			desc := fmt.Sprintf("attack减益buff数量(%d)是否大于等于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	}
	return b
}

//nolint:varnamelen
func CondDefenseBenefitBuffCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	b := false
	fn := func(b *Buff) bool {
		return IsBenefitBuff(b.typeInfo)
	}
	count := defense.GetBuffM().GetBuffLayer(fn)
	pse.condValue = int64(count)
	if conditionParam2 > 0 {
		b = count < conditionParam1
		if de() {
			desc := fmt.Sprintf("denfese增益buff数量(%d)是否小于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	} else {
		b = count >= conditionParam1
		if de() {
			desc := fmt.Sprintf("denfese增益buff数量(%d)是否大于等于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	}
	return b
}

func CondDefenseNoBenefitBuffCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	b := false
	fn := func(b *Buff) bool {
		return IsHarmfulBuff(b.typeInfo)
	}
	count := defense.GetBuffM().GetBuffLayer(fn)
	pse.condValue = int64(count)
	if conditionParam2 > 0 {
		b = count < conditionParam1
		if de() {
			desc := fmt.Sprintf("denfese减益buff数量(%d)是否小于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	} else {
		b = count >= conditionParam1
		if de() {
			desc := fmt.Sprintf("denfese减益buff数量(%d)是否大于等于参数(%d)", count, conditionParam1)
			dEvent := GetCondCheckDebug(condition, desc, b)
			m.GetRep().AddDebugEvent(dEvent)
		}
	}
	return b
}

func CondDefenseDefCompare(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	physicalDef := defense.GetAttr(goxml.AttrPhyDef)
	magicDef := defense.GetAttr(goxml.AttrMagDef)
	var b bool
	if conditionParam1 == 0 {
		b = (physicalDef == magicDef)
	} else if conditionParam1 == 1 {
		b = (physicalDef > magicDef)
	} else if conditionParam1 == 2 {
		b = (physicalDef < magicDef)
	}
	if de() {
		desc := fmt.Sprintf("defense的物防(%d)和魔防(%d)按ConditionParam1(%d)比较", physicalDef, magicDef, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackRace(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	b := attack.GetRace() == conditionParam1 || attack.GetRace() == conditionParam2 || attack.GetRace() == conditionParam3 || attack.GetRace() == conditionParam4
	if de() {
		desc := fmt.Sprintf("attack的种族(%d)==(%d)||(%d)||(%d)||(%d)", attack.GetRace(), conditionParam1, conditionParam2, conditionParam3, conditionParam4)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondOwnerIsBenefitBuffCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	owner := pse.GetOwner()

	fn := func(b *Buff) bool {
		if conditionParam2 == 1 {
			return IsBenefitBuff(b.typeInfo)
		} else if conditionParam2 == 2 {
			return IsHarmfulBuff(b.typeInfo)
		}
		return false
	}
	count := owner.GetBuffM().GetBuffLayer(fn)

	b := false
	if conditionParam3 > 0 {
		b = count < conditionParam1
	} else {
		b = count >= conditionParam1
	}

	pse.condValue = int64(count)
	if de() {
		desc := fmt.Sprintf("owner buff BuffTypeInfo.IsBenefit == (%d)的数量(%d)是否((%d):默认大于等于, 1为小于)参数(%d)", conditionParam2, count, conditionParam3, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// owner的血量高于xx%
func CondOwnerHpHigher(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	hp := owner.GetHp()
	maxHp := owner.GetMaxHp()
	p := uint32(float64(hp) / float64(maxHp) * BaseFloat)
	b := p > conditionParam1
	if de() {
		desc := fmt.Sprintf("owner的血量(%d)/(%d)高于%d", hp, maxHp, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseRace(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	defense := pse.GetDefense()
	b := defense.GetRace() == conditionParam1 || defense.GetRace() == conditionParam2 || defense.GetRace() == conditionParam3 || defense.GetRace() == conditionParam4
	if de() {
		desc := fmt.Sprintf("defense的种族(%d)==(%d)||(%d)||(%d)||(%d)", defense.GetRace(), conditionParam1, conditionParam2, conditionParam3, conditionParam4)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondOneSkillAttackHasKill(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	triggerUniter := pse.GetTriggerUniter()
	b := triggerUniter.IsAtSkillKilled()
	if de() {
		desc := "判断triggerUniter技能中是否有击杀"
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackTeamBenefitBuffCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	attack := pse.GetAttack()
	team := m.GetTeam(attack)

	b := false
	fn := func(b *Buff) bool {
		return IsBenefitBuff(b.typeInfo)
	}
	var totalCount uint32
	for _, mem := range team.members {
		totalCount += mem.GetBuffM().GetBuffLayer(fn)
	}
	b = totalCount >= conditionParam1

	if de() {
		desc := fmt.Sprintf("attack队伍增益buff数量(%d)是否大于等于参数(%d)", totalCount, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondResurrection(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	isResurrectioned := false
	var target *Member
	if conditionParam1 == 1 {
		attack := pse.GetAttack()
		if !(attack.GetUniterType() == UniterTypeMember) {
			panic(fmt.Sprintf("attack(%d) is not Member", attack.ID()))
		}
		target = attack.(*Member)
	} else {
		defense := pse.GetDefense()
		if !(defense.GetUniterType() == UniterTypeMember) {
			panic(fmt.Sprintf("defense(%d) is not Member", defense.ID()))
		}
		target = defense.(*Member)
	}

	if target.HasSta(StaResurrectioned) {
		isResurrectioned = true
	}

	b := isResurrectioned
	if conditionParam2 == 1 {
		b = !isResurrectioned
	}

	if de() {
		desc := fmt.Sprintf("目标(%d)是否复活过(%t)", target.ID(), isResurrectioned)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondAttackTeamLinkNum(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var num1, num2, num3 uint32
	attack := pse.GetAttack()
	activedLinks := m.GetTeam(attack).GetActivedLinks()
	for _, activedLink := range activedLinks {
		if activedLink.LinkId == conditionParam2 {
			num1 = activedLink.Num
		} else if activedLink.LinkId == conditionParam3 {
			num2 = activedLink.Num
		} else if activedLink.LinkId == conditionParam4 {
			num3 = activedLink.Num
		}
	}
	b := num1+num2+num3 >= conditionParam1
	if de() {
		desc := fmt.Sprintf("结果=(%t):  attack(%d)的队伍已激活的联结 (%d)数量(%d) || (%d)数量(%d) || (%d)数量(%d)是否达到(%d)",
			b, attack.ID(), conditionParam2, num1, conditionParam3, num2, conditionParam4, num3, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondDefenseTeamLinkNum(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var num1, num2, num3 uint32
	defense := pse.GetDefense()
	activedLinks := m.GetTeam(defense).GetActivedLinks()
	for _, activedLink := range activedLinks {
		if activedLink.LinkId == conditionParam2 {
			num1 = activedLink.Num
		} else if activedLink.LinkId == conditionParam3 {
			num2 = activedLink.Num
		} else if activedLink.LinkId == conditionParam4 {
			num3 = activedLink.Num
		}
	}
	b := num1+num2+num3 >= conditionParam1
	if de() {
		desc := fmt.Sprintf("结果=(%t):  defense(%d)的队伍已激活的联结 (%d)数量(%d) || (%d)数量(%d) || (%d)数量(%d)是否达到(%d)",
			b, defense.ID(), conditionParam2, num1, conditionParam3, num2, conditionParam4, num3, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondTeamAttrMoreEqual(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var result bool
	var totalAttr int64
	var team *Team
	if conditionParam3 == 1 {
		team = m.GetOpTeam(pse.GetOwner())
	} else {
		team = m.GetTeam(pse.GetOwner())
	}
	for _, member := range team.initMembers {
		totalAttr += member.GetAttr(int(conditionParam1))
	}
	if totalAttr >= int64(conditionParam2) {
		result = true
	}

	if de() {
		desc := fmt.Sprintf("全队指定属性条件检查:/n 结果 = [%t]:  拥有者[%d]的队伍[%d]属性类型[%d]的值[%d]是否达到[%d]",
			result, pse.GetOwner().ID(), team.index, conditionParam1, totalAttr, conditionParam2)
		m.GetRep().AddDebugEvent(desc)
	}
	return result
}

func CondTeamResurrectionMoreEqual(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var resurrectionTimes int
	if conditionParam1 == 1 { //敌方
		resurrectionTimes = m.GetOpTeam(pse.GetOwner()).ResurrectionTimes
	} else if conditionParam1 == 2 { //我方
		resurrectionTimes = m.GetTeam(pse.GetOwner()).ResurrectionTimes
	}
	result := resurrectionTimes >= int(conditionParam2)
	if de() {
		desc := fmt.Sprintf("全队复活次数检查: 结果 = (%t) 复活次数(%d)是否达到[%d]",
			result, resurrectionTimes, conditionParam2)
		m.GetRep().AddDebugEvent(desc)
	}
	return result
}

func CondTeamDeathNumMoreEqual(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var deathTimes int
	if conditionParam1 == 1 { //敌方
		deathTimes = m.GetOpTeam(pse.GetOwner()).DeathTimes
	} else if conditionParam1 == 2 { //我方
		deathTimes = m.GetTeam(pse.GetOwner()).DeathTimes
	}
	result := deathTimes >= int(conditionParam2)
	if de() {
		desc := fmt.Sprintf("全队死亡次数检查: 结果 = (%t) 死亡次数(%d)是否达到[%d]",
			result, deathTimes, conditionParam2)
		m.GetRep().AddDebugEvent(desc)
	}
	return result
}

func CondBuffLayerCountMoreEqual(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	members := gPool.GetUniters()
	defer gPool.FreeUniters(members)
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		owner := pse.psSkill.owner
		if conditionParam1 == 1 {
			team = owner.GetManager().GetOpTeam(owner)
		} else {
			team = owner.GetManager().GetTeam(owner)
		}
		team.GetAliveAll(members)
	} else if conditionParam1 == 3 {
		uniter := pse.GetAttack()
		members.AddUniter(uniter)
	} else if conditionParam1 == 4 {
		uniter := pse.GetDefense()
		members.AddUniter(uniter)
	}

	var fn func(b *Buff) bool
	if conditionParam2 == 1 { //根据buffType
		fn = func(b *Buff) bool {
			return b.typeInfo.Id == conditionParam3
		}
	} else if conditionParam2 == 2 { //buff 增减益
		fn = func(b *Buff) bool {
			if conditionParam3 == 1 {
				return IsBenefitBuff(b.typeInfo)
			} else if conditionParam3 == 2 {
				return IsHarmfulBuff(b.typeInfo)
			}
			return false
		}
	} else if conditionParam2 == 3 { //指定buffID
		fn = func(b *Buff) bool {
			return b.info.Id == conditionParam3
		}
	}

	membersNum := members.GetNum()
	membersArray := members.GetUniters()
	var totalLayer uint32
	for i := 0; i < membersNum; i++ {
		totalLayer += membersArray[i].GetBuffM().GetBuffLayer(fn)
	}

	b := totalLayer >= conditionParam4
	if de() {
		desc := fmt.Sprintf("结果=(%t) buff层数数量(%d)>=目标数量(%d)", b, totalLayer, conditionParam4)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondBuffTypeCountMoreEqual(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	members := gPool.GetUniters()
	defer gPool.FreeUniters(members)
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		owner := pse.psSkill.owner
		if conditionParam1 == 1 {
			team = owner.GetManager().GetOpTeam(owner)
		} else {
			team = owner.GetManager().GetTeam(owner)
		}
		team.GetAliveAll(members)
	} else if conditionParam1 == 3 {
		uniter := pse.GetAttack()
		members.AddUniter(uniter)
	} else if conditionParam1 == 4 {
		uniter := pse.GetDefense()
		members.AddUniter(uniter)
	}

	var fn func(b *Buff) bool
	if conditionParam2 == 2 { //buff 增减益
		fn = func(b *Buff) bool {
			if conditionParam3 == 1 {
				return IsBenefitBuff(b.typeInfo)
			} else if conditionParam3 == 2 {
				return IsHarmfulBuff(b.typeInfo)
			}
			return false
		}
	}

	membersNum := members.GetNum()
	membersArray := members.GetUniters()
	buffTypeMap := make(map[uint32]util.None, 0)
	for i := 0; i < membersNum; i++ {
		for _, buffs := range membersArray[i].GetBuffM().buffs {
			for _, buff := range buffs {
				if CheckBuffInUse(buff) && buff.IsActive() && fn(buff) {
					buffTypeMap[buff.typeInfo.Id] = util.None{}
				}
			}
		}
	}
	totalType := len(buffTypeMap)
	b := uint32(totalType) >= conditionParam4
	pse.condValue = int64(totalType)
	if de() {
		desc := fmt.Sprintf("结果=(%t) buff类型数量(%d)>=目标数量(%d)", b, totalType, conditionParam4)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondOwnerTeamLinkNum(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	return condTeamLinkNum(false, m, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4, pse)
}

func condTeamLinkNum(op bool, m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var num1, num2, num3 uint32
	owner := pse.GetOwner()
	team := m.GetTeam(owner)
	if op {
		team = m.GetOpTeam(owner)
	}
	activedLinks := team.GetActivedLinks()
	for _, activedLink := range activedLinks {
		if activedLink.LinkId == conditionParam2 {
			num1 = activedLink.Num
		} else if activedLink.LinkId == conditionParam3 {
			num2 = activedLink.Num
		} else if activedLink.LinkId == conditionParam4 {
			num3 = activedLink.Num
		}
	}
	b := num1+num2+num3 >= conditionParam1

	deParam := "己方"
	if op {
		deParam = "敌方"
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t):  owner(%d)的%s队伍已激活的联结 (%d)数量(%d) || (%d)数量(%d) || (%d)数量(%d)是否达到(%d)",
			b, owner.ID(), deParam, conditionParam2, num1, conditionParam3, num2, conditionParam4, num3, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondOpTeamLinkNum(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	return condTeamLinkNum(true, m, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4, pse)
}

func CondSameActionAddedBuffCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	members := gPool.GetUniters()
	defer gPool.FreeUniters(members)
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		owner := pse.psSkill.owner
		if conditionParam1 == 1 {
			team = owner.GetManager().GetOpTeam(owner)
		} else {
			team = owner.GetManager().GetTeam(owner)
		}
		team.GetAliveAll(members)
	} else if conditionParam1 == 3 {
		uniter := pse.GetAttack()
		members.AddUniter(uniter)
	} else if conditionParam1 == 4 {
		uniter := pse.GetDefense()
		members.AddUniter(uniter)
	}

	var fn func(b *Buff) bool
	nowAction := m.GetActionIndex()
	if conditionParam2 == 1 { //根据buffType
		fn = func(b *Buff) bool {
			if b.addAction != nowAction {
				return false
			}
			return b.typeInfo.Id == conditionParam3
		}
	} else if conditionParam2 == 2 { //buff 增减益
		fn = func(b *Buff) bool {
			if b.addAction != nowAction {
				return false
			}
			if conditionParam3 == 1 {
				return IsBenefitBuff(b.typeInfo)
			} else if conditionParam3 == 2 {
				return IsHarmfulBuff(b.typeInfo)
			}
			return false
		}
	} else if conditionParam2 == 3 { //指定buffID
		fn = func(b *Buff) bool {
			if b.addAction != nowAction {
				return false
			}
			return b.info.Id == conditionParam3
		}
	}

	membersNum := members.GetNum()
	membersArray := members.GetUniters()
	var total uint32
	for i := 0; i < membersNum; i++ {
		if membersArray[i].GetBuffM().HadBuff(fn) {
			total++
		}
	}

	b := total >= conditionParam4
	if de() {
		desc := fmt.Sprintf("结果=(%t) 拥有buff人数(%d)>=目标数量(%d)", b, total, conditionParam4)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondHasPsAttr(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var result bool
	switch conditionParam1 {
	case 5: //目前只支持拥有者自己
		result = pse.psSkill.owner.GetPSEffectValue(int(conditionParam2), 0) != 0
		if de() {
			desc := fmt.Sprintf("结果=(%t):  owner(%d)是否有特殊属性(%d)",
				result, pse.psSkill.owner.ID(), conditionParam2)
			dEvent := GetCondCheckDebug(condition, desc, result)
			m.GetRep().AddDebugEvent(dEvent)
		}
	}

	return result
}

func CondOwnerDeathState(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	b := owner.GetStatus() == int(conditionParam1)
	if de() {
		desc := fmt.Sprintf("结果=(%t):  owner(%d)的死亡状态是否为(%d)",
			b, owner.ID(), conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondOwnerNoBuffState(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	count := owner.GetBuffM().GetBuffLayerByType(conditionParam1)
	b := count <= 0
	pse.condValue = int64(count)
	if de() {
		desc := fmt.Sprintf("owner没有处于buff type=(%d)状态", conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondLink(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var own bool
	var memID uint32
	mem, ok := pse.GetAttack().(*Member)
	if ok {
		heroID := mem.GetHeroSysID()
		if linkData, exist := m.GetTeam(mem).GetHeroLinkInfo()[heroID]; exist {
			if _, exist := linkData[conditionParam1]; exist {
				own = true
			}
		}
		memID = mem.ID()
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t): attack(%d)的队伍是否已激活联结(%d) attak是member(%t)", own, memID, conditionParam1, ok)
		dEvent := GetCondCheckDebug(condition, desc, own)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return own
}

func CondSeasonEnergy(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	isNegative := isSeasonLinkNegativeEnergy(int64(conditionParam2))
	b := false
	if isNegative {
		b = uint32(math.Abs(float64(m.GetTeam(owner).GetEnergy()))) >= conditionParam1
	} else {
		b = m.GetTeam(owner).GetEnergy() >= int64(conditionParam1)
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t):  attack(%d)的队伍赛季能量点是否达到(%d),是负能量(%t)", b, owner.ID(), conditionParam1, isNegative)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

func CondIsTargetExist(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	m.GetTarget(owner, nil, conditionParam1, pse, targets)
	b := targets.GetNum() > 0

	if de() {
		desc := fmt.Sprintf("结果=(%t): 目标(%d) 是否存在", b, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 当前回合第x个释放大招
func CondCurRoundBigSkillCount(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	if conditionParam2 <= 0 {
		panic(fmt.Sprintf("%d conditionParam2 illegal", conditionParam2))
	}

	owner := pse.GetOwner()
	bigSkillCount := uint32(0)
	if conditionParam1 == 0 { // 进攻方
		bigSkillCount = m.GetTeam(owner).GetBigSkillCount(m.GetRound()) + 1
	} else if conditionParam1 == 1 { // 防守方
		bigSkillCount = m.GetOpTeam(owner).GetBigSkillCount(m.GetRound()) + 1
	} else if conditionParam1 == 2 { // 全场
		team := m.GetTeam(owner)
		opTeam := m.GetOpTeam(owner)
		bigSkillCount = team.GetBigSkillCount(m.GetRound()) + opTeam.GetBigSkillCount(m.GetRound()) + 1
	}

	b := conditionParam2 == bigSkillCount
	if de() {
		desc := fmt.Sprintf("结果=(%t): 进攻方还是防守方(%d)，第(%d)回合，第(%d)个释放大招的英雄",
			b, conditionParam1, m.GetRound(), conditionParam2)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 赛季能量点条件(62)的补充条件
func CondSeasonEnergyExtra(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	owner := pse.GetOwner()
	isNegative := isSeasonLinkNegativeEnergy(int64(conditionParam2))
	b := false
	if isNegative {
		b = uint32(math.Abs(float64(m.GetTeam(owner).GetEnergy()))) <= conditionParam1
	} else {
		b = m.GetTeam(owner).GetEnergy() <= int64(conditionParam1)
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t):  attack(%d)的队伍赛季能量点<=(%d),是负能量(%t)", b, owner.ID(), conditionParam1, isNegative)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 判断英雄星级
func CondHeroStar(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var own bool
	var heroStar, memID uint32
	mem, ok := pse.GetTriggerUniter().(*Member)
	if ok {
		if conditionParam1 >= 1 && conditionParam1 <= 3 {
			heroStar = mem.GetStar()
			if conditionParam1 == 1 {
				if heroStar >= conditionParam2 {
					own = true
				}
			} else if conditionParam1 == 2 {
				if heroStar == conditionParam2 {
					own = true
				}
			} else {
				if heroStar <= conditionParam2 {
					own = true
				}
			}
			memID = mem.ID()
		}
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t): defense(%d)的星级(%d),符合条件(%d)的要求(%d)",
			own, memID, heroStar, conditionParam1, conditionParam2)
		dEvent := GetCondCheckDebug(condition, desc, own)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return own
}

// 判断属性值
func CondAttr(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var own bool
	var attrVal int64
	var memID uint32
	mem, ok := pse.GetTriggerUniter().(*Member)
	if ok {
		if conditionParam2 >= 1 && conditionParam2 <= 3 {
			attrVal = mem.GetAttr(int(conditionParam1))
			if conditionParam2 == 1 {
				if attrVal >= int64(conditionParam3) {
					own = true
				}
			} else if conditionParam2 == 2 {
				if attrVal == int64(conditionParam3) {
					own = true
				}
			} else {
				if attrVal <= int64(conditionParam3) {
					own = true
				}
			}
			memID = mem.ID()
		}
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t): defense(%d)的属性(%d => %d),符合条件(%d)的要求(%d)",
			own, memID, conditionParam1, attrVal, conditionParam2, conditionParam3)
		dEvent := GetCondCheckDebug(condition, desc, own)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return own
}

// 判断某类型羁绊激活的数量
func CondLinkTypeCount(m *Manager, condition, conditionParam1, conditionParam2,
	conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	if conditionParam2 < 1 {
		panic(fmt.Sprintf("CondLinkTypeCount, conditionParam2:%d illegal", conditionParam2))
	}

	owner := pse.GetOwner()
	team := m.GetTeam(owner)
	ownCount := team.GetLinkTypeCount(conditionParam1)
	b := ownCount >= conditionParam2
	if de() {
		desc := fmt.Sprintf("结果=(%t): 激活羁绊类型(%d)数量(%d)，要求数量(%d)",
			b, conditionParam1, ownCount, conditionParam2)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

type AddRateConditionFunc func(*goxml.PassiveSkillProbabilityInfo, *PsSkillExe) uint32

var gBattleAddRateConditions = [goxml.AddRateCondMax]AddRateConditionFunc{
	goxml.AddRateCondOwnerTeamLinkNum: AddRateCondOwnerTeamLinkNum,
}

func AddRateCondOwnerTeamLinkNum(info *goxml.PassiveSkillProbabilityInfo, pse *PsSkillExe) uint32 {
	owner := pse.GetOwner()
	linkInfo := pse.GetManager().GetTeam(owner).GetLinkInfo()
	var linkNum1, linkNum2, result uint32
	condition1 := true
	condition2 := true

	if info.EffParam1 != 0 {
		if linkNum, exist := linkInfo[info.EffParam1]; exist {
			linkNum1 = linkNum
			if linkNum < info.EffParam2 {
				condition1 = false
			}
		} else {
			condition1 = false
		}
	}
	if info.EffParam3 != 0 {
		if linkNum, exist := linkInfo[info.EffParam3]; exist {
			linkNum2 = linkNum
			if linkNum < info.EffParam4 {
				condition2 = false
			}
		} else {
			condition2 = false
		}
	}
	if condition1 && condition2 {
		result = info.EffParam5
	}

	if de() {
		desc := fmt.Sprintf("被动增加概率条件检查:/n 结果=(%d):  owner(%d)的队伍激活的联结 (%d)数量(%d)是否达到(%d) && (%d)数量(%d)是否达到(%d)",
			result, owner.ID(), info.EffParam1, linkNum1, info.EffParam2, info.EffParam3, linkNum2, info.EffParam4)
		pse.GetManager().GetRep().AddDebugEvent(desc)
	}
	return result
}

// 判断英雄性别
func CondHeroSex(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var own bool
	var heroSysID, memID uint32
	mem, ok := pse.GetDefense().(*Member)
	if ok {
		heroSysID = mem.GetHeroSysID()
		memID = mem.ID()
		info := goxml.GetData().HeroInfoM.Index(heroSysID)
		if info == nil {
			panic(fmt.Sprintf("CondHeroSex, no hero info. id:%d, %s", heroSysID, pse.debug()))
		}
		own = conditionParam1 == info.Sex
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t): defense(%d)的英雄性别是(%d),符合条件要求的(%d)",
			own, memID, heroSysID, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, own)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return own
}

// 判断某时机下，是否拥有某被动id
func CondHavePsID(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var own bool
	uniterID := pse.GetTriggerUniter().ID()
	for _, psSkills := range m.psm.psSkills {
		for _, psSkill := range psSkills[int(uniterID)] {
			if psSkill.info.Info.Id == conditionParam1 || psSkill.info.Info.Id == conditionParam2 ||
				psSkill.info.Info.Id == conditionParam3 {
				own = true
				break
			}
		}
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t): defense(%d)，是否拥有被动ID(%d或%d或%d)",
			own, uniterID, conditionParam1, conditionParam2, conditionParam3)
		dEvent := GetCondCheckDebug(condition, desc, own)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return own
}

func CondOwnerPos(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	pos := pse.GetOwner().Pos()
	var b bool
	if pos == conditionParam1 || pos == conditionParam2 || pos == conditionParam3 {
		b = true
	}
	if de() {
		desc := fmt.Sprintf("owner的站位(%d)，是否在(%d-%d-%d)之中", pos, conditionParam1, conditionParam2, conditionParam3)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 敌方剩余人数(排除召唤物)大于1且我方没有英雄类召唤物
func CondLeftMemAndNoMonsterCallHero(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	uniter := pse.GetTriggerUniter()
	team := m.GetTeam(uniter)
	opTeam := m.GetOpTeam(uniter)

	// 排除召唤物，剩余存活数量
	var opAliveCount uint32
	for _, uniter := range opTeam.members {
		if uniter.IsCall() {
			continue
		}

		if uniter.HasSta(StaHide) || uniter.HasSta(StaExile) {
			continue
		}

		if uniter.GetStatus() == UniterAlive {
			opAliveCount++
		}
	}
	numCheck := opAliveCount > 1

	var b bool
	if numCheck {
		b = true
		for _, v := range team.members {
			if v.GetMonsterType() == MonsterTypeCallHero {
				b = false
				break
			}
		}
	}

	if de() {
		desc := fmt.Sprintf("cond72: 结果:%t, 触发者(%d)，敌方剩余人数(%d)", b, uniter.ID(), opAliveCount)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 有空位或者有普通召唤物
func CondHaveEmptyPosOrMonsterCall(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	uniter := pse.GetTriggerUniter()
	team := m.GetTeam(uniter)

	var hasEmptyPos uint32
	var hasMonsterCall bool
	for i := 1; i <= TeamMaxPosWithCallPos; i++ {
		uniter := team.GetUniterByPos(getRealPos(i))
		if uniter != nil && uniter.GetStatus() != UniterDead {
			if uniter.GetMonsterType() == MonsterTypeCall {
				hasMonsterCall = true
			}
			continue
		}

		hasEmptyPos++
		break
	}

	b := hasEmptyPos > 0 || hasMonsterCall
	if de() {
		desc := fmt.Sprintf("cond73: 结果:%t, 触发者(%d), 剩余空位数(%d), 是否有普通召唤物(%t)",
			b, uniter.ID(), hasEmptyPos, hasMonsterCall)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 队伍存活单位数量
// 参数1 敌方/我方/攻方/守方
// 参数2 数值
func CondAliveNum(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var aliveNum uint32
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		if conditionParam1 == 1 {
			team = m.GetOpTeam(pse.GetOwner())
		} else {
			team = m.GetTeam(pse.GetOwner())
		}

		for _, uniter := range team.members {
			if uniter.HasSta(StaHide) || uniter.HasSta(StaExile) {
				continue
			}

			if uniter.GetStatus() == UniterAlive {
				aliveNum++
			}
		}

	} else if conditionParam1 == 3 || conditionParam1 == 4 {
		var uniter Uniter
		if conditionParam1 == 3 {
			uniter = pse.GetAttack()
		} else {
			uniter = pse.GetDefense()
		}

		if !uniter.HasSta(StaHide) && !uniter.HasSta(StaExile) && uniter.GetStatus() == UniterAlive {
			aliveNum++
		}
	} else {
		panic(fmt.Sprintf("CondAliveNum, conditionParam1:%d illegal, %s", conditionParam1, pse.debug()))
	}

	b := aliveNum > conditionParam2
	if de() {
		desc := fmt.Sprintf("cond74: 结果:%t, 触发者(%d), owner(%d), param1:%d, param2:%d, 剩余人数(%d)",
			b, pse.GetTriggerUniter().ID(), pse.GetOwner().ID(), conditionParam1, conditionParam2, aliveNum)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 某方没有指定buffID
// 参数1 敌方/我方/攻方/守方
// 参数2 buffID
func CondNoBuffID(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	fn := func(b *Buff) bool {
		return b.info.Id == conditionParam2
	}

	var haveBuff bool
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		if conditionParam1 == 1 {
			team = m.GetOpTeam(pse.GetOwner())
		} else {
			team = m.GetTeam(pse.GetOwner())
		}

		for _, uniter := range team.members {
			if uniter.GetStatus() == UniterAlive {
				if uniter.GetBuffM().HadBuff(fn) {
					haveBuff = true
					break
				}
			}
		}

	} else if conditionParam1 == 3 || conditionParam1 == 4 {
		var uniter Uniter
		if conditionParam1 == 3 {
			uniter = pse.GetAttack()
		} else {
			uniter = pse.GetDefense()
		}

		if uniter.GetStatus() == UniterAlive {
			if uniter.GetBuffM().HadBuff(fn) {
				haveBuff = true
			}
		}
	} else {
		panic(fmt.Sprintf("CondNoBuffID, conditionParam1:%d illegal, %s", conditionParam1, pse.debug()))
	}

	b := !haveBuff
	if de() {
		desc := fmt.Sprintf("cond75-没有指定buffID: 结果:%t, 触发者(%d), param1:%d, param2:%d",
			b, pse.GetTriggerUniter().ID(), conditionParam1, conditionParam2)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 判断某方有无指定英雄
func CondHeroID(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var isHave bool
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		if conditionParam1 == 1 {
			team = m.GetOpTeam(pse.GetOwner())
		} else {
			team = m.GetTeam(pse.GetOwner())
		}

		for _, uniter := range team.members {
			if !uniter.HasSta(StaHide) && !uniter.HasSta(StaExile) && uniter.GetStatus() == UniterAlive {
				if uniter.GetHeroSysID() == conditionParam3 {
					isHave = true
					break
				}
			}
		}

	} else if conditionParam1 == 3 || conditionParam1 == 4 {
		var uniter Uniter
		if conditionParam1 == 3 {
			uniter = pse.GetAttack()
		} else {
			uniter = pse.GetDefense()
		}

		member, ok := uniter.(*Member)
		if !ok {
			panic(fmt.Sprintf("CondNoBuffID, conditionParam1:%d uniter not member, %s", conditionParam1, pse.debug()))
		}

		if !uniter.HasSta(StaHide) && !uniter.HasSta(StaExile) && uniter.GetStatus() == UniterAlive {
			if member.GetHeroSysID() == conditionParam3 {
				isHave = true
			}
		}
	} else {
		panic(fmt.Sprintf("CondNoBuffID, conditionParam1:%d illegal, %s", conditionParam1, pse.debug()))
	}

	needHave := conditionParam2 == 1
	ret := isHave == needHave
	if de() {
		desc := fmt.Sprintf("cond76-判断某方有无指定英雄，结果=(%t): 目标(%d)是否拥有英雄(%d)，是否要求拥有(%t)",
			ret, conditionParam1, conditionParam3, needHave)
		dEvent := GetCondCheckDebug(condition, desc, ret)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return ret
}

// 指定buff总层数小于某个值
func CondBuffLayerCountLess(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32,
	pse *PsSkillExe) bool {
	members := gPool.GetUniters()
	defer gPool.FreeUniters(members)
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		owner := pse.psSkill.owner
		if conditionParam1 == 1 {
			team = owner.GetManager().GetOpTeam(owner)
		} else {
			team = owner.GetManager().GetTeam(owner)
		}
		team.GetAliveAll(members)
	} else if conditionParam1 == 3 {
		uniter := pse.GetAttack()
		members.AddUniter(uniter)
	} else if conditionParam1 == 4 {
		uniter := pse.GetDefense()
		members.AddUniter(uniter)
	}

	var fn func(b *Buff) bool
	if conditionParam2 == 1 { //根据buffType
		fn = func(b *Buff) bool {
			return b.typeInfo.Id == conditionParam3
		}
	} else if conditionParam2 == 2 { //buff 增减益
		fn = func(b *Buff) bool {
			if conditionParam3 == 1 {
				return IsBenefitBuff(b.typeInfo)
			} else if conditionParam3 == 2 {
				return IsHarmfulBuff(b.typeInfo)
			}
			return false
		}
	} else if conditionParam2 == 3 { //指定buffID
		fn = func(b *Buff) bool {
			return b.info.Id == conditionParam3
		}
	}

	membersNum := members.GetNum()
	membersArray := members.GetUniters()
	var totalLayer uint32
	for i := 0; i < membersNum; i++ {
		totalLayer += membersArray[i].GetBuffM().GetBuffLayer(fn)
	}

	b := totalLayer < conditionParam4
	if de() {
		desc := fmt.Sprintf("cond77-指定buff总层数小于某个值，结果=(%t) buff层数数量(%d)<目标数量(%d)", b, totalLayer, conditionParam4)
		dEvent := GetCondCheckDebug(condition, desc, b)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return b
}

// 判断施加buff_type的累积数量，达到N层后触发
func CondBuffLayerRecord(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var totalLayer uint32
	if conditionParam1 == 1 || conditionParam1 == 2 {
		var team *Team
		if conditionParam1 == 1 {
			team = m.GetOpTeam(pse.GetOwner())
		} else {
			team = m.GetTeam(pse.GetOwner())
		}

		for _, uniter := range team.members {
			totalLayer += uniter.GetBuffLayerRecord(conditionParam2)
		}
	} else if conditionParam1 == 3 || conditionParam1 == 4 {
		var uniter Uniter
		if conditionParam1 == 3 {
			uniter = pse.GetAttack()
		} else {
			uniter = pse.GetDefense()
		}
		totalLayer += uniter.GetBuffLayerRecord(conditionParam2)
	} else {
		panic(fmt.Sprintf("CondBuffLayerRecord, conditionParam1:%d illegal, %s", conditionParam1, pse.debug()))
	}

	ret := totalLayer >= conditionParam3
	if de() {
		desc := fmt.Sprintf("cond78-判断施加buff_type的累积数量，结果=(%t): 目标(%d)添加buff总层数(%d)，指定buffType(%d)是否达到(%d)层",
			ret, conditionParam1, totalLayer, conditionParam2, conditionParam3)
		dEvent := GetCondCheckDebug(condition, desc, ret)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return ret
}

// 判断主动技能造成的伤害有效（大于指定值）
func CondBuffActiveSkillHurtEffective(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	data := pse.GetArgs().(*OneSkillAttack)
	ret := data.GetHurt() > int64(conditionParam1)
	if de() {
		desc := fmt.Sprintf("cond79-判断主动技能造成的伤害大于指定值，结果=(%t): 目标(%d)",
			ret, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, ret)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return ret
}

// 累计受到X点伤害
func CondAccumulateBeHurt(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	data := pse.GetArgs().(*OneSkillAttack)
	value := m.report.GetNRoundSta(data.defense.ID(), m.GetRound())

	param := uint64(float64(conditionParam1) * math.Pow(float64(10), float64(conditionParam2)))
	ret := value.GetBeHurt() >= param
	if de() {
		desc := fmt.Sprintf("cond81-累计受到X点伤害，结果=(%t): 目标(%d)",
			ret, param)
		dEvent := GetCondCheckDebug(condition, desc, ret)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return ret
}

// 判断当前回合是单数还是双数
func CondCurrentRoundIsOddOrEven(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	currentRound := m.GetRound()

	even := currentRound%2 == 0

	var ret bool
	if conditionParam1 == 1 { // 期望是单数回合
		ret = !even
	} else if conditionParam1 == 2 { // 期望是双数回合
		ret = even
	} else {
		panic(fmt.Sprintf("CondCurrentRoundIsOddOrEven: conditionParam1:%d error.", conditionParam1))
	}

	if de() {
		desc := fmt.Sprintf("cond82-判断当前回合是单数还是双数，结果=(%t): 目标(%d)",
			ret, conditionParam1)
		dEvent := GetCondCheckDebug(condition, desc, ret)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return ret
}

func CondArtifactStar(m *Manager, condition, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, pse *PsSkillExe) bool {
	var own bool
	var star uint32
	mem, ok := pse.GetTriggerUniter().(*Member)
	if ok {
		team := m.GetTeam(mem)
		for _, artifact := range team.GetArtifactM().artifacts {
			if artifact.GetSysID() == conditionParam1 {
				star = artifact.artifactInfo.Star
				if conditionParam2 == 1 {
					if star >= conditionParam3 {
						own = true
					}
				} else if conditionParam2 == 2 {
					if star == conditionParam3 {
						own = true
					}
				} else if conditionParam2 == 3 {
					if star <= conditionParam3 {
						own = true
					}
				}
				break
			}
		}
	}

	if de() {
		desc := fmt.Sprintf("结果=(%t): defense(%d)队伍的神器(%d)的星级(%d),符合条件(%d)的要求(%d)",
			own, mem.ID(), conditionParam1, star, conditionParam2, conditionParam3)
		dEvent := GetCondCheckDebug(condition, desc, own)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return own
}
