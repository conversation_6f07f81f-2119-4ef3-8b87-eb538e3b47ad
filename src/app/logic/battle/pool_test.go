package battle

import (
	"fmt"
	"runtime/debug"
	"testing"
)

const poolSize int = 100000

type OneRoundContainer struct {
	objs        []IPoolObj
	updateIndex int
}

func newOneRoundIPoolObj() IPoolObj {
	return &OneRound{}
}

func (p *OneRoundContainer) GetPoolObj() IPoolObj {
	var value IPoolObj
	if p.updateIndex == len(p.objs) {
		value = newOneRoundIPoolObj()
		p.objs = append(p.objs, nil)
	} else {
		value = p.objs[p.updateIndex]
	}
	if StrictPool() {
		debug.Stack()
	}
	p.updateIndex++
	return value
}
func (p *OneRoundContainer) PutPoolObj(value IPoolObj) {
	if value == nil {
		panic(fmt.Sprintf("battle pool PutPoolObj value nil :  %s", debug.Stack()))
	}
	if StrictPool() {
		debug.Stack()
	}
	p.updateIndex--
	p.objs[p.updateIndex] = value
	value.Reset()
}

var oneRoundContainer *OneRoundContainer = &OneRoundContainer{objs: make([]IPoolObj, 0)}

var newPoolValues [poolSize]*OneRound

func Benchmark_NewPool(b *testing.B) {
	for i := 0; i < b.N; i++ {
		for i := 0; i < poolSize; i++ {
			newPoolValues[i] = oneRoundContainer.GetPoolObj().(*OneRound)
		}
		for i := 0; i < poolSize; i++ {
			oneRoundContainer.PutPoolObj(newPoolValues[i])
		}
	}
}

// var oldPoolValues [poolSize]*OneRound

// func Benchmark_OldPool(b *testing.B) {
// 	for i := 0; i < b.N; i++ {
// 		for i := 0; i < poolSize; i++ {
// 			oldPoolValues[i] = gPool.NewOneRound()
// 		}
// 		for i := 0; i < poolSize; i++ {
// 			gPool.PutOneRound(oldPoolValues[i])
// 			//oldPoolValues[i] = nil
// 		}
// 	}
// }

// var oneRound *OneRound

// func oneRoundIPoolObjReset(obj IPoolObj) {
// 	obj.Reset()
// }
// func newOneRoundIPoolObj() IPoolObj {
// 	return &OneRound{}
// }
// func Benchmark_OnlyInterface(b *testing.B) {
// 	for i := 0; i < b.N; i++ {
// 		oneRound = newOneRoundIPoolObj().(*OneRound)
// 		oneRoundIPoolObjReset(oneRound)
// 	}
// }

// func newOneRound() *OneRound {
// 	return &OneRound{}
// }
// func oneRoundReset(oneRound *OneRound) {
// 	oneRound.Reset()
// }
// func Benchmark_NewOneRound(b *testing.B) {
// 	for i := 0; i < b.N; i++ {
// 		oneRound = newOneRound()
// 		oneRoundReset(oneRound)
// 	}
// }
