package battle

import (
	"app/goxml"
	"app/protos/out/bt"
)

type Pokemon struct {
	Uniter
	id uint32

	pos   uint32
	sysID uint32
	star  uint32
	index uint32

	passiveSkills []uint32
	mgr           *Manager
	skillM        *SkillManager
}

func NewPokemon(pos, sysID, star uint32) *Pokemon {
	m := gPool.GetPokemon()
	m.pos = pos
	m.sysID = sysID
	m.star = star
	m.skillM = NewSkillManager(m)
	return m
}

func (p *Pokemon) Reset() {
	*p = Pokemon{}
}

func (p *Pokemon) InitSkills() {
	for _, psSkill := range p.passiveSkills {
		p.skillM.AddPsSkill(psSkill)
	}
}

func (p *Pokemon) AddNewSysPassiveSkill(id uint32) {
	p.passiveSkills = append(p.passiveSkills, id)
}
func (p *Pokemon) Init(mgr *Manager, t *Team) {
	p.id = t.NewID(PokemonBasePos+p.pos, false)
	p.index = t.index
	p.mgr = mgr
}
func (p *Pokemon) SetSysID(id uint32) {
	p.sysID = id
}
func (p *Pokemon) ID() uint32 {
	return p.id
}

func (p *Pokemon) Index() uint32 {
	return p.index
}
func (p *Pokemon) Pos() uint32 {
	return p.pos
}

func (p *Pokemon) AddPsSkill(registerPsSkill uint32) *PsSkill {
	return p.skillM.AddPsSkill(registerPsSkill)
}

func (p *Pokemon) GetManager() *Manager {
	return p.mgr
}

func (p *Pokemon) GetSkillM() ISkillManager {
	return p.skillM
}

func (p *Pokemon) Flush() *bt.Pokemon {
	Pokemon := &bt.Pokemon{
		Id:    p.id,
		SysId: p.sysID,
		Star:  p.star,
		Pos:   p.pos,
	}

	return Pokemon
}

func (p *Pokemon) FlushDebugInfo(debugSkill bool) *bt.DebugInfo {
	debugInfo := &bt.DebugInfo{
		Id:  p.id,
		Pos: p.pos,
	}
	if debugSkill {
		debugInfo.Skills = p.skillM.GetOwnerSkills()
		debugInfo.OtherPsSkills = append(debugInfo.OtherPsSkills, p.passiveSkills...)
	}
	return debugInfo
}

func (p *Pokemon) GetUniterType() int {
	return UniterTypePokemon
}

func (p *Pokemon) GetStatus() int {
	return UniterAlive
}

func (p *Pokemon) GetPSEffectValue(name int, base int64) int64 {
	return base
}

func (p *Pokemon) GetAttr(attr int) int64 {
	return goxml.GetData().TeamInitDataInfoM.GetAttr(attr)
}

func (p *Pokemon) HasSta(sta int) bool {
	return false
}

func (p *Pokemon) IsCall() bool {
	return false
}
