package battle

const (
	BattleAdd uint32 = 0 //增加
	BattleDec uint32 = 1 //减少
)

const (
	AttackTeam  uint32 = 0
	DefenseTeam uint32 = 1
)

const (
	ActifactEnergyAction                  = 150 // 英雄能量获取公式(英雄行动)
	ActifactEnergyKill                    = 151 // 英雄能量获取公式(击杀)
	FormulaChangeValue             uint32 = 511 //转换传入值
	FormulaChangeValueMaxAttackAtk uint32 = 261 //转换传入值(最大值为攻击方攻击力)
	FormulaHurt517                 uint32 = 517 // 新增伤害公式
	FormulaChangeSeasonLinkEnergy  uint32 = 518 // 赛季羁绊能量点(信仰点)转为属性
)

// 最大回合数，不能大于这个回合
const (
	MaxRoundNum uint32 = 15
)

// 战斗阶段
// 可以buff的结算标记, 也作为被动技能的触发时机
const (
	StageOneRoundPrepare  uint32 = 1
	StageOneActionPrepare uint32 = 2 //行动前
	StageOneSkillPrepare  uint32 = 3 //选定目标攻击前
	StageOneSkillEnd      uint32 = 4 //选定目标攻击时
	StageOneActionEnd     uint32 = 5 //行动之后
	StageOneRoundEnd      uint32 = 6
)

// 被动技能触发的条件
const (
	CondAttackCrit uint32 = 1 //攻击出现了暴击
	CondHpLower    uint32 = 2 //血量低于%x多少
)

// buff类型
const (
	BuffTypeCorruption                         uint32 = 101    //腐蚀
	BuffTypeFire                               uint32 = 102    //燃烧
	BuffTypePoison                             uint32 = 103    //中毒
	BuffTypeReflect                            uint32 = 104    //反弹
	BuffTypeBurn                               uint32 = 105    //灼烧
	BuffTypeMaxLayerHurt3                      uint32 = 121    //叠满3层造成伤害
	BuffTypeMaxLayerHurt4                      uint32 = 122    //叠满4层造成伤害
	BuffTypeMaxLayerHurt5                      uint32 = 123    //叠满5层造成伤害
	BuffTypeHot                                uint32 = 151    //持续治疗
	BuffTypeHot2                               uint32 = 152    //持续治疗,回合结束时
	BuffTypeStun                               uint32 = 201    //眩晕
	BuffTypeTaunt                              uint32 = 202    //嘲讽
	BuffTypeSilence                            uint32 = 203    //沉默
	BuffTypeEntang                             uint32 = 204    //缠绕
	BuffTypeNoAlive                            uint32 = 205    //禁魂 无法复活
	BuffTypeSeal                               uint32 = 206    //封印 无法暴击，被动技能失效
	BuffTypeChaos                              uint32 = 207    //混乱，普攻攻击 敌我双方
	BuffTypeMabi                               uint32 = 208    //麻痹 50%概率无法行动
	BuffTypeCharm                              uint32 = 209    //魅惑
	BuffTypeReset                              uint32 = 210    //休息，强制休息一回合，非控制类buff
	BuffTypeCastTime                           uint32 = 211    //吟唱 替换技能 并且添加控制后 会被删除掉
	BuffTypeHide                               uint32 = 212    //隱藏 玩家不会行动(只结算buff)，且不会被当作选定目标
	BuffTypeStealCure                          uint32 = 214    //存在buff时,被治疗时给buff的施加者治疗
	BuffTypeJess                               uint32 = 215    //枷锁,效果同眩晕
	BuffTypeCure2Hurt                          uint32 = 216    //治疗逆转为伤害
	BuffTypeExile                              uint32 = 217    //放逐效果；同隐藏，无法行动且无法被选中，需要新特效(敌人全部放逐时，敌方失败)
	BuffTypeStun2                              uint32 = 219    //眩晕，累计回合的眩晕
	BuffTypeShudder                            uint32 = 220    //战栗(效果同眩晕)
	BuffTypeSkillNoCritHurt2Cure               uint32 = 221    //主动技能造成的非暴击伤害转治疗
	BuffTypeShield                             uint32 = 301    //护盾
	BuffTypeNoDead                             uint32 = 302    //不屈
	BuffTypeHunting                            uint32 = 303    //狩猎，只是一个状态
	BuffTypeImmuneBadNoControlBuff             uint32 = 304    //免疫负面非控制类buff，并且是次数控制，只能免疫可以被净化的BUFF
	BuffTypeGuard                              uint32 = 306    //保护
	BuffTypeImmuneControlBuff                  uint32 = 307    //免疫控制
	BuffTypeImmunePositiveBuff                 uint32 = 308    // 禁强：免疫多数增益BUFF，只能免疫可以被驱散的BUFF
	BuffTypeImmuneNoAlive                      uint32 = 309    //免疫禁魂
	BuffTypeResurrect                          uint32 = 310    //死后可以复活
	BuffTypeCut                                uint32 = 5001   //割裂buff， 属性类buff
	BuffTypeStormGather                        uint32 = 439810 //风暴聚集
	BuffTypeSign                               uint32 = 312    //标记
	BuffTypeInvincibleShield                   uint32 = 313    //无敌盾
	BuffTypeArtifactEnergy                     uint32 = 404    //影响神器释放能量值
	BuffTypeIgnoreShield                       uint32 = 311    //伤害无视血量护盾
	BuffTypeLimitSelfHurt                      uint32 = 316    //存在buff时,只能被自己伤害
	BuffTypeDeathKeepBenefitBuff               uint32 = 405    //存在buff时,死亡不清除增益buff且能延长回合数
	BuffTypeShareDamageByHp                    uint32 = 406    //根据剩余血量分摊伤害
	BuffTypeBlessNoHurted                      uint32 = 317    //庇佑免疫伤害
	BuffTypeBlessNoDebuff                      uint32 = 318    //庇佑免疫负面buff
	BuffTypeNoHurted                           uint32 = 319    //无敌类型的免疫伤害
	BuffTypeNoDebuff                           uint32 = 320    //无敌类型的免疫负面buff
	BuffTypeImmune                             uint32 = 321    //免疫某buffType类型
	BuffTypeForbidAddBuff                      uint32 = 322    //无法施加buff或debuff
	BuffTypeAccumulateHurt                     uint32 = 323    //积攒伤害（s4神器）
	BuffTypeAccumulateHurtShare                uint32 = 324    //积攒伤害分摊（s4神器）
	BuffTypeOnlyMagicHurt                      uint32 = 325    //龙战boss - 只受魔法伤害（破盾）
	BuffTypeOnlyPhysicalHurt                   uint32 = 326    //龙战boss - 只受物理伤害（破盾）
	BuffTypeOnlyArtifactSingleHurt             uint32 = 327    //龙战boss - 只受神器单体主动伤害（破盾）
	BuffTypeOnlyArtifactGroupHurt              uint32 = 328    //龙战boss - 只受神器群体主动伤害（破盾）
	BuffTypeLimitSelfHurt2                     uint32 = 329    //无敌，仅能够被伤害类型16,17(effect60)斩杀和自己伤害，英雄用
	BuffTypeReflectDebuff                      uint32 = 330    //反弹debuff
	BuffTypeImmuneStealAndEat                  uint32 = 331    //免疫被偷取和被吃
	BuffTypeNoHurtedBySkillType                uint32 = 332    //根据技能类型免伤(仅受某类技能伤害)
	BuffTypeCountLayer                         uint32 = 333    //扣层数的计数buff
	BuffTypeNoDead2                            uint32 = 334    //不屈2(怪物用)
	BuffTypeOnlyHurtBySelfOrCallDepartedSpirit uint32 = 335    //只受自残伤害或者英灵召唤物的伤害
	BuffTypeNoDead3                            uint32 = 336    //不屈3（根据参数1的配置，概率生效
	BuffTypeOnlyOnePosAndOneSex                uint32 = 337    //地图boss - 只受单一位置和单一性别造成的伤害
)

// buff逻辑的大类
const (
	BuffLogicTypeAttr   uint32 = 1 //修改属性类
	BuffLogicTypeHurt   uint32 = 2 //伤害类
	BuffLogicTypeCurl   uint32 = 3 //治疗类
	BuffLogicTypeShield uint32 = 4 //护盾类
)

// buff逻辑子类
const (
	BuffSubTypeSettleHurt           uint32 = 101 //结算伤害类
	BuffSubTypeTrrigerHurt          uint32 = 102 //特殊触发伤害类
	BuffSubTypeSettleAccumulateHurt uint32 = 103 //积攒伤害分摊（s4神器）
	BuffSubTypeSettleCurl           uint32 = 201 //结算治疗类
)

const (
	BuffAttrStartType uint32 = 1000 //1000以后的都是属性类的buff
)

// buff结算类型
const (
	BuffTakeRoundPrepare  uint32 = 1 //大回合开始的时候扣回合数
	BuffTakeActionPrepare uint32 = 2 //行动开始前的时候扣回合数
	BuffTakeActionEnd     uint32 = 5 //行动结束的时候扣回合数
)

// buff清除类型
const (
	BuffRemoveTypeHelp                uint32 = 1 //清除增益
	BuffRemoveTypeHarm                uint32 = 2 //清除减益
	BuffRemoveTypeControl             uint32 = 3 //清除控制
	BuffRemoveTypeHarmfulAndNoControl uint32 = 4 //清除非控制类的减益
	BuffRemoveTypeBuffEffectType      uint32 = 5 //根据effectType清楚buff

	BuffRemoveTypeStatus         uint32 = 4 //清除分组
	BuffRemoveTypeID             uint32 = 5 //清除buffid, 并且不考虑这个buff是否能被清楚
	BuffRemoveTypeStatusOneLayer uint32 = 7 //随机1层BUFFtype
	BuffRemoveTypeIDOneLayer     uint32 = 8 //随机1层BUFFinfo
)

// buff拷贝传递类型
const (
	BuffCopyTypeHelp                uint32 = 1 //传递增益
	BuffCopyTypeHarm                uint32 = 2 //传递减益
	BuffCopyTypeHarmfulAndNoControl uint32 = 3 //传递非控制类的减益
)

const (
	BuffAddTypeNone              uint32 = 0 //不限制
	BuffAddTypeOverlay           uint32 = 1 //可叠加, 有层数上限的
	BuffAddTypeCover             uint32 = 2 //覆盖
	BuffAddTypePriorityCover     uint32 = 3 //有优先级的, 高的覆盖第的
	BuffAddTypeUpdate            uint32 = 4 //添加的时候累计层数并且刷新持续时间，层数到达上限之后不在添加
	BuffAddTypePriority          uint32 = 5 //有优先级的, 低的不被覆盖，但是只有高的生效
	BuffAddTypeTeamSingle        uint32 = 6 //全队唯一存在的buff
	BuffAddTypeTypeLayer         uint32 = 7 //共存叠加 ，max_add为同一buff_type的所有buff的layer之和，满的情况下，不加
	BuffAddTypeCoverInheritLayer uint32 = 8 //覆盖，needAddLayer有值时，用needAddLayer。无值时，继承层数
	BuffAddTypeUpdateRound       uint32 = 9 //添加的时候累计回合数
)

// 需统计添加层数的buffType列表
var RecordBuffLayer = []uint32{BuffTypeCorruption}

// 伤害类型
const (
	HurtTypeNone             uint32 = 0  //默认的
	HurtTypeActiveSkillSmall uint32 = 1  //主动技能-小招伤害
	HurtTypeNormalSkill      uint32 = 2  //普通技能伤害
	HurtTypePassiveSkill     uint32 = 3  //普通被动技能伤害
	HurtTypeBuff             uint32 = 4  //buff伤害
	HurtTypeSuck             uint32 = 5  //吸血
	HurtTypeReflect          uint32 = 6  //反弹
	HurtTypeSkillAttackDead  uint32 = 7  //技能攻击者死亡伤害
	HurtTypeDoKill           uint32 = 8  //斩杀
	HurtTypeGuard            uint32 = 9  //保护伤害
	HurtTypeHurtSelf         uint32 = 10 //自残
	HurtTypeResurrect        uint32 = 11 //复活
	HurtTypeActiveSkillBig   uint32 = 12 //主动技能-大招伤害
	CureTypeSteal            uint32 = 13 //偷来的治疗
)

// 技能类型
const (
	SkillTypeNormal      uint32 = 1 //普通技能
	SkillTypeActiveSmall uint32 = 2 //主动技能-小招
	SkillTypePassive     uint32 = 3 //被动技能
	SkillTypeAttr        uint32 = 4 //只加面板属性的技能
	SkillTypeActiveBig   uint32 = 5 //主动技能-大招
)

// 技能整体的攻击类型
const (
	SkillAttackTypeHurt uint32 = 1 //伤害类技能
	SkillAttackTypeCure uint32 = 2 //治疗类技能
	SkillAttackTypeSpec uint32 = 3 //特殊类技能

)

// 每段技能效果类型
const (
	SkillEffectTypeHurt                 uint32 = 1 //伤害
	SkillEffectTypeCure                 uint32 = 2 //治疗
	SkillEffectTypeAddBuff              uint32 = 3 //加buff
	SkillEffectTypeRemoveBuff           uint32 = 4 //删除 buff
	SkillEffectTypeResurrectionAndCured uint32 = 5 //复活
	SkillEffectTypeHurtSelf             uint32 = 6 //伤害自己
	SkillEffectTypeEffectBuffAttr       uint32 = 7 //影响buff的属性
	SkillEffectTypeCall                 uint32 = 8 //召唤
)

const (
	BaseFloat    float64 = 10000.0
	BaseFloatInt int64   = 10000
)

// 被嘲讽了默认使用的技能
const (
	SkillTauntSkill uint32 = 1
)

var gSortByAttackPositon = map[uint32][][]uint32{
	0: { //正对优先
		{},
		{1, 3, 4, 2, 5, 11},
		{2, 4, 11, 5, 1, 3},
		{3, 4, 5, 1, 2, 11},
		{4, 3, 5, 1, 2, 11},
		{5, 4, 3, 11, 2, 1},
		{1, 3, 4, 2, 5, 11},
		{1, 3, 4, 2, 5, 11},
		{1, 3, 4, 2, 5, 11},
		{1, 3, 4, 2, 5, 11},
		{1, 3, 4, 2, 5, 11},
		{11, 5, 2, 4, 1, 3},
	},
	1: { //前排优先
		{},
		{1, 2, 11, 3, 4, 5},
		{2, 11, 1, 4, 5, 3},
		{1, 2, 11, 3, 4, 5},
		{1, 2, 11, 4, 3, 5},
		{11, 2, 1, 5, 4, 3},
		{1, 2, 11, 3, 4, 5},
		{1, 2, 11, 3, 4, 5},
		{1, 2, 11, 3, 4, 5},
		{1, 2, 11, 3, 4, 5},
		{1, 2, 11, 3, 4, 5},
		{11, 2, 1, 5, 4, 3},
	},
	2: { //后排优先
		{},
		{4, 3, 5, 1, 2, 11},
		{4, 5, 3, 2, 11, 1},
		{3, 4, 5, 1, 2, 11},
		{4, 5, 3, 11, 2, 1},
		{5, 4, 3, 11, 2, 1},
		{4, 3, 5, 1, 2, 11},
		{4, 3, 5, 1, 2, 11},
		{4, 3, 5, 1, 2, 11},
		{4, 3, 5, 1, 2, 11},
		{4, 3, 5, 1, 2, 11},
		{5, 4, 3, 11, 2, 1},
	},
}

var gReviseSameRow = [][]uint32{
	{},
	{1, 2, 11},
	{2, 11, 1},
	{3, 4, 5},
	{4, 3, 5},
	{5, 4, 3},
	{},
	{},
	{},
	{},
	{},
	{11, 1, 2},
}
var gReviseSameRowNoSelf = [][]uint32{
	{},
	{2, 11},
	{1, 11},
	{4, 5},
	{3, 5},
	{4, 3},
	{},
	{},
	{},
	{},
	{},
	{1, 2},
}
var gReviseNotSameRow = [][]uint32{
	{},
	{3, 4, 5},
	{4, 5, 3},
	{1, 2, 11},
	{1, 2, 11},
	{11, 2, 1},
	{},
	{},
	{},
	{},
	{},
	{5, 3, 4},
}
var gReviseAround = [][]uint32{
	{},
	{1, 3, 4, 2},
	{2, 4, 5, 1, 11},
	{3, 1, 4},
	{4, 1, 2, 3, 5},
	{5, 2, 4, 11},
	{},
	{},
	{},
	{},
	{},
	{11, 5, 4, 2},
}
var gReviseAroundNoSelf = [][]uint32{
	{},
	{3, 4, 2},
	{4, 5, 1, 11},
	{1, 4},
	{1, 2, 3, 5},
	{2, 11, 4},
	{},
	{},
	{},
	{},
	{},
	{5, 4, 2},
}
var gReviseNotAround = [][]uint32{
	{},
	{5},
	{3},
	{5, 2, 11},
	{11},
	{3, 1},
	{},
	{},
	{},
	{},
	{},
	{1, 3, 4},
}
var gReviseBack = [][]uint32{
	{},
	{3, 4},
	{4, 5},
	{},
	{},
	{},
	{},
	{},
	{},
	{},
	{},
	{5},
}
var gReviseFront = [][]uint32{
	{},
	{},
	{},
	{1},
	{1, 2},
	{2, 11},
	{},
	{},
	{},
	{},
	{},
	{},
}

const (
	MemberJobTank    uint32 = 1 //坦克
	MemberJobMagic   uint32 = 2 //法师
	MemberJobWarrior uint32 = 3 //战士
	MemberJobAssist  uint32 = 4 //辅助
)

const (
	AttackTypeNone     uint32 = 0 //
	AttackTypePhysical uint32 = 1 //物理攻击
	AttackTypeMagic    uint32 = 2 //魔法攻击
)

const (
	PSEffectOneBattle = iota
	PSEffectOneAction
	PSEffectOneSkill
	PSEffectOneAttack
)

const (
	BuffBenefitNormal uint32 = 0 //既不是增益也不是减益
	BuffBenefit       uint32 = 1
	BuffHarmful       uint32 = 2
	BuffBenefitSp     uint32 = 3
	BuffHarmfulSp     uint32 = 4
)

const (
	BuffClearNormal uint32 = 0 //既不驱散也不净化
	BuffPurify      uint32 = 1 //净化
	BuffDispel      uint32 = 2 //驱散
)

// 混乱buff的技能目标
const (
	BuffChaosTarget           uint32 = 806
	BuffCharmTarget           uint32 = 534
	BuffMabiRate              int64  = 5000
	BuffCharmActiveRate       int64  = 5000
	BuffCharmTargetCasterRate int64  = 5000
)

const (
	BeGuardHurtFormula uint32 = 116
	GuardHurtFormula   uint32 = 117
	SuckBloodFormula   uint32 = 118
	HurtReflectFormula uint32 = 119
)

// 战斗位置
const (
	TeamMaxPos            int = 5
	TeamMaxPosWithCallPos int = TeamMaxPos + 1 // v2.8添加召唤位后的最大位置数量

	TargetPos1          uint32 = 1  // 英雄位置1
	TargetPos2          uint32 = 2  // 英雄位置2
	TargetPos3          uint32 = 3  // 英雄位置3
	TargetPos4          uint32 = 4  // 英雄位置4
	TargetPos5          uint32 = 5  // 英雄位置5
	ArtifactBattlePos   uint32 = 6  // 神器manager位置
	TeamUniterBattlePos uint32 = 7  // 全局对象位置
	CallPos             uint32 = 11 // 召唤位位置
	PokemonBasePos      uint32 = 11 // 宠物基础位置
	ArtifactMaxNum      int    = 3  //战斗中最大的神器数量

	FirstCallID = CallPos // 首个召唤物id
)

const (
	ChangeAttrTimeOneAttack uint32 = iota
	ChangeAttrTimeOneEffect
	ChangeAttrTimeOneSkill
	ChangeAttrTimeForeve //永久的
	ChangeAttrTimeBuff
	ChangeAttrTimeMax
)

const ( //相同的时机保持和ChangeAttrTime对齐
	ChangePsAttrTimeOneAttack int = iota
	ChangePsAttrTimeOneEffect
	ChangePsAttrTimeOneSkill
	ChangePsAttrTimeHalo
	ChangePsAttrTimeForeve
	ChangePsAttrTimeNextSkill //下一个技能
	ChangePsAttrTimeMax
)

const (
	SortGetMin bool = false //从小到大的
	SortGetMax bool = true  //从大到小的
)

const (
	NextSkillNoModify int32 = 0 //技能连击无修正
)

const (
	TargetCanEmpty = true  //目标选不到的时候是否可以为空 可以为空
	TargetNoEmpty  = false //目标选不到的时候是否可以为空 不可以为空， 接着选
)

const (
	TargetNeedAlive   = iota //选择目标的时候需要目标存活
	TargetNoNeedAlive        //选择目标的时候是否需要目标存活 否
	TargetNeedDead           //选择目标的时候需要目标死亡
)

const (
	MonsterTypeNone               = 0
	MonsterTypeNormal             = 1
	MonsterTypeElite              = 2
	MonsterTypeCall               = 3 //召唤物
	MonsterTypeCallResurrection   = 4 //可复活的召唤物
	MonsterTypeCallHero           = 5 //英雄类召唤物
	MonsterTypeCallDepartedSpirit = 6 //亡灵召唤物
	MonsterTypeBoss               = 10
)

const (
	AddBuffRetNone    = iota - 1 //-1代表既不算成功也不算失败
	AddBuffRetSuccess            // 0 代表成功， 下面的都是失败
	AddBuffRetMianyi
	AddBuffRetDikang
	AddBuffRetReflect //反弹debuff
)

const (
	BattleStaHurt   uint32 = 0
	BattleStaBeHurt uint32 = 1
	BattleStaCure   uint32 = 2
)

const (
	UniterAlive = iota
	UniterDead
)

// 主动技能多段上限
const SkillMaxStage = 4

// 被动技能多段上限
const PsSkillMaxStage = 4

const (
	BuffTypeEffectDefault       = iota
	BuffTypeEffectMaxLayerAdd   //修改buff_type层数上限 加法
	BuffTypeEffectMaxLayerMulti //修改buff_type层数上限 乘法
	BuffTypeEffectRoundAdd      //修改buff_type默认回合数 加法
)

const (
	StaNoSkillAttack  = iota
	StaSkillAttacking //攻击中
	StaSkillAttackEnd //攻击后
)

const ( //64位状态
	StaResurrectioned = iota //复活过
	StaHide                  //隐藏状态
	StaBlessNoHurted         //庇佑抵挡伤害
	StaBlessNoDebuff         //庇佑抵挡buff
	StaNoHurted              //无敌抵挡伤害
	StaNoDebuff              //无敌抵挡buff
	StaIgnoreShield          //伤害无视护盾
	StaSealPsSkill           //被动封印
	StaResurrect             //能够复活
	StaExile                 //放逐状态
	StaForbidAddBuff         //禁止添加buff
	StaAccumulateHurt        //积攒伤害
	StaMax            = 64
)

const (
	NextSkillTypeNone = iota //不是连击类技能
	NextSkillTypeCommon
	NextSkillTypeCombo     //连击
	NextSkillTypeUnion     //联动
	NextSkillTypeFightBack //回击
)

const PsMaxTriggerCount uint32 = 10000

const (
	UniterTypeNone = iota
	UniterTypeMember
	UniterTypeVoidMember
	UniterTypeArtifactM
	UniterTypeArtifact
	UniterTypeTeamUniter
	UniterTypePos
	UniterTypePokemon
)

const (
	PassiveTypeNone uint32 = iota
	PassiveType1           //庇护,无敌类被动
	PassiveType2
	PassiveType3
	PassiveType4
	PassiveType5
)

func GetAllSameRowPos(pos uint32) []uint32 {
	if pos == TargetPos1 || pos == TargetPos2 || pos == CallPos {
		return []uint32{TargetPos1, TargetPos2, CallPos}
	} else {
		return []uint32{TargetPos3, TargetPos4, TargetPos5}
	}
}

func GetAllSameRowPosNoSelfNoCall(pos uint32) []uint32 {
	list := make([]uint32, 0, 1)
	for _, v := range GetAllSameRowPos(pos) {
		if v == pos || v == CallPos {
			continue
		}
		list = append(list, v)
	}
	return list
}

// 是否是赛季羁绊负能量
// @param int64 param 1:正能量 2:负能量
func isSeasonLinkNegativeEnergy(param int64) bool {
	return param == 2
}

const (
	Buff_Event_None         = iota // 无效
	Buff_Event_Alive_Change        // 存活数量改变
	Buff_Event_Round_End           // 回合结束
	Buff_Event_Max                 //最大的情况
)

const (
	BuffSliceLengthMax  = 10000 // buff 切片的最大值，超过这个值就是异常，报panic
	RoundActionTimesMax = 200   // 单轮次，action执行次数的最大值
)
