package battle

import (
	"app/goxml"
	"fmt"
)

var gBuffConditions = [goxml.BuffCondMax]BuffConditionFunc{
	goxml.BuffCondAlive:   BuffCondAlive,
	goxml.BuffCondOnEvent: BuffCondOnEvent,
}

type BuffConditionFunc func(*Manager, uint32, uint32, uint32, uint32, *Buff) bool

// 判断某些目标存活
func BuffCondAlive(m *Manager, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, buff *Buff) bool {
	if len(buff.buffRegisterEvents) == 0 { //注册关心的事件
		buff.RegisterEvent(Buff_Event_Alive_Change)
	}
	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	buff.getTarget(conditionParam1, targets)
	totalNum := targets.GetNum()
	var aliveNum uint32
	for i := 0; i < totalNum; i++ {
		uniter := targets.GetUniters()[i]
		if uniter.GetStatus() == UniterAlive {
			aliveNum++
		}
	}
	result := aliveNum >= conditionParam2
	if df() {
		dEvent := fmt.Sprintf("结果(%t):存活目标数(%d)大于等于%d", result, aliveNum, conditionParam2)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return result
}

func BuffCondOnEvent(m *Manager, conditionParam1, conditionParam2, conditionParam3, conditionParam4 uint32, buff *Buff) bool {
	if len(buff.buffRegisterEvents) != 0 { //注册关心的事件
		if conditionParam1 != 0 {
			buff.RegisterEvent(int(conditionParam1))
		}
	}
	result := true
	if df() {
		dEvent := fmt.Sprintf("结果(%t):监听关心的事件 %d", result, conditionParam1)
		m.GetRep().AddDebugEvent(dEvent)
	}
	return result
}
