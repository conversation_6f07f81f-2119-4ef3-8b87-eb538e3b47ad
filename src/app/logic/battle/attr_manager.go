//nolint:mnd
package battle

import (
	"app/goxml"

	l4g "github.com/ivanabc/log4go"
)

type IAttrM interface {
	GetAttr(int) int64
	GetBaseAttr(int) int64
	ChangeAttr(uint32, uint32, int, int64)
	ChangeHpAttr()
	ChangeBuff(bool, uint32)
	ClearAttr(uint32)
	AttrChanged(int)
	GetInitAttr(int) int64
}

type PsAttrChange struct {
	owner Uniter
	attr  int
	value int64
}

func NewPsAttrChange(owner Uniter, attr int, value int64) PsAttrChange {
	return PsAttrChange{
		owner: owner,
		attr:  attr,
		value: value,
	}
}

const (
	HpAttrCondHpLower  = iota //血量低于百分比
	HpAttrCondHpHigher        //血量高于于百分比
)

type HpAttrManager struct {
	Attrs []*HpAttr
}

func NewHpAttrManager() *HpAttrManager {
	return &HpAttrManager{}
}

func (hm *HpAttrManager) Add(attr int, value int64, cond, condValue int) {
	if hm.Attrs == nil {
		hm.Attrs = make([]*HpAttr, 0, 1)
	}
	hm.Attrs = append(hm.Attrs, &HpAttr{attr, value, cond, condValue})
}

type HpAttr struct {
	Attr      int   //什么属性
	Value     int64 //加多少
	Cond      int
	CondValue int
}

/*
func (hm *HpAttrManager) GetAttr(id int, member Uniter) int64 {
	if len(hm.Attrs) > 0 {
		value := int64(0)
		for _, v := range hm.Attrs {
			if v.Attr == id {
				if v.Cond == HpAttrCondHpLower {
					if int(float64(member.GetHp())/float64(member.GetMaxHp())*BaseFloat) < v.CondValue {
						value += v.Value
					}
				} else if v.Cond == HpAttrCondHpHigher {
					if int(float64(member.GetHp())/float64(member.GetMaxHp())*BaseFloat) >= v.CondValue {
						value += v.Value
					}
				}
			}
		}
		return value
	} else {
		return 0
	}
}
*/

//nolint:varnamelen
func (hm *HpAttrManager) SetHpAttr(m *AttrManager, member Uniter, attrs *[goxml.AttrMaxNum]int64) {
	if len(hm.Attrs) > 0 {
		for k := range attrs {
			attrs[k] = 0
		}
		for _, v := range hm.Attrs {
			if v.Value == 0 {
				continue
			}
			if v.Cond == HpAttrCondHpLower {
				if int(float64(member.GetHp())/float64(member.GetMaxHp())*BaseFloat) < v.CondValue {
					m.changeAttrWithID(attrs, v.Attr, v.Value, BattleAdd)
				} else {
					m.AttrChanged(v.Attr)
				}
			} else if v.Cond == HpAttrCondHpHigher {
				if int(float64(member.GetHp())/float64(member.GetMaxHp())*BaseFloat) >= v.CondValue {
					m.changeAttrWithID(attrs, v.Attr, v.Value, BattleAdd)
				} else {
					m.AttrChanged(v.Attr)
				}
			}
		}
	}
}

type HadBuffAttr struct {
	Active   bool   //是否激活
	BuffType uint32 //需要的buff类型
	Attr     int    //属性类型
	Value    int64  //改变值
}

type HadBuffAttrManager struct {
	Attrs map[uint32]*HadBuffAttr //SkillId =>
}

func NewHadBuffAttrManager() *HadBuffAttrManager {
	return &HadBuffAttrManager{Attrs: make(map[uint32]*HadBuffAttr)}
}

func (hadBuffAttrManager *HadBuffAttrManager) Add(skillID, buffType uint32, attr int, value int64) {
	hadBuffAttrManager.Attrs[skillID] = &HadBuffAttr{false, buffType, attr, value}
}

type AliveNumAttr struct {
	Active   bool   //是否激活
	AliveNum uint32 //需要的存活数量
	Attr     int    //属性类型
	Value    int64  //改变值
}

type AliveNumAttrManager struct {
	Attrs map[uint32]*AliveNumAttr
}

func NewAliveNumAttrManager() *AliveNumAttrManager {
	return &AliveNumAttrManager{Attrs: make(map[uint32]*AliveNumAttr)}
}

func (aliveNumAttrManager *AliveNumAttrManager) Add(skillID, aliveNum uint32, attr int, value int64) {
	aliveNumAttrManager.Attrs[skillID] = &AliveNumAttr{false, aliveNum, attr, value}
}

type ChangeFlagManager struct {
	m [goxml.AttrChangeFlagGroupCount]BitSet64
}

func newChangeFlagManager() *ChangeFlagManager {
	return &ChangeFlagManager{m: [goxml.AttrChangeFlagGroupCount]BitSet64{}}
}

func (cfm *ChangeFlagManager) Clear(realID int) {
	group, id := goxml.GetAttrChangeFlagGroupAndID(realID)
	cfm.m[group].Clear(id)
}

func (cfm *ChangeFlagManager) IsSet(realID int) bool {
	group, id := goxml.GetAttrChangeFlagGroupAndID(realID)
	return cfm.m[group].IsSet(id)
}

func (cfm *ChangeFlagManager) Set(realID int) {
	group, id := goxml.GetAttrChangeFlagGroupAndID(realID)
	cfm.m[group].Set(id)
}

type AttrManager struct {
	IPoolObj
	IAttrM
	owner Uniter

	baseAttr      []int64                 //基础属性，人物面板的属性
	initAttr      [goxml.AttrMaxNum]int64 //初始化属性(在回合0之后的属性)
	totalAttr     [goxml.AttrMaxNum]int64 //缓存的值
	changedAttrs  [ChangeAttrTimeMax][goxml.AttrMaxNum]int64
	hpAttr        [goxml.AttrMaxNum]int64 //血量变化引起的属性改变, 每次血量变化实时更新
	hadBuffAttr   [goxml.AttrMaxNum]int64 //拥有指定buff类型引起的属性变化,实时更新
	aliveNumAttr  [goxml.AttrMaxNum]int64 //队伍存活人数变化引起的属性变化,实时更新
	hpAttrM       *HpAttrManager          //血量变化引起的属性改变规则
	hadBuffAttrM  *HadBuffAttrManager     //拥有指定buff类型引起的属性变化
	aliveNumAttrM *AliveNumAttrManager    //队伍存活人数变化引起的属性变化
	changeFlagM   *ChangeFlagManager      //变化标志位管理器
}

func NewAttrManager(owner Uniter) *AttrManager {
	m := gPool.GetAttrManager()
	m.init(owner)
	return m
}

func (m *AttrManager) init(owner Uniter) {
	if m.baseAttr == nil {
		m.baseAttr = make([]int64, goxml.AttrMaxNum)
	}
	if m.hpAttrM == nil {
		m.hpAttrM = NewHpAttrManager()
	}
	if m.hadBuffAttrM == nil {
		m.hadBuffAttrM = NewHadBuffAttrManager()
	}
	if m.aliveNumAttrM == nil {
		m.aliveNumAttrM = NewAliveNumAttrManager()
	}
	if m.changeFlagM == nil {
		m.changeFlagM = newChangeFlagManager()
	}
	m.owner = owner
}

func (m *AttrManager) Reset() {
	*m = AttrManager{
		baseAttr: m.baseAttr,
	}
	for i := 0; i < goxml.AttrMaxNum; i++ {
		m.baseAttr[i] = 0
	}
}

// 注意这里一定不能直接赋值
func (m *AttrManager) SetBaseAttr(attr []int64) {
	copy(m.baseAttr, attr)
}

func (m *AttrManager) InitBaseAttr() {
	goxml.FixAttr(m.baseAttr)
	copy(m.totalAttr[:], m.baseAttr)
}

func (m *AttrManager) InitInitAttr() {
	copy(m.initAttr[:], m.totalAttr[:])
}
func (m *AttrManager) GetInitAttr(id int) int64 {
	return m.initAttr[id]
}

func (m *AttrManager) setAttrAndAttrPctChanged(attrType int) {
	m.changeFlagM.Set(attrType)
	for _, v := range goxml.GetAttrPctType(attrType) {
		m.setAttrAndAttrPctChanged(v)
	}

	//绝对值属性对应的属性
	for _, v := range goxml.GetAttrAbsType(attrType) {
		m.setAttrAndAttrPctChanged(v)
	}
}

func (m *AttrManager) AttrChanged(attrType int) {
	m.setAttrAndAttrPctChanged(attrType)
	if mAttr := goxml.GetAttrMerged(attrType); len(mAttr) > 0 {
		for _, v := range mAttr {
			m.setAttrAndAttrPctChanged(v)
		}
	}
}

func (m *AttrManager) changeAttrWithID(attrs *[goxml.AttrMaxNum]int64, attr int, value int64, add uint32) {
	changeFunc := func(attrType int) {
		if add == BattleAdd {
			attrs[attrType] += value
		} else {
			attrs[attrType] -= value
		}
		m.AttrChanged(attrType)
	}
	if mAttr := goxml.GetAttrMerged(attr); len(mAttr) > 0 {
		for _, v := range mAttr {
			changeFunc(v)
		}
	} else {
		changeFunc(attr)
	}
}

func (m *AttrManager) ChangeAttr(addTime, add uint32, attr int, value int64) {
	if value == 0 {
		return
	}
	m.changeAttrWithID(&m.changedAttrs[addTime], attr, value, add)
}

func (m *AttrManager) ChangeHpAttr() {
	if len(m.hpAttrM.Attrs) == 0 {
		return
	}
	m.hpAttrM.SetHpAttr(m, m.owner, &m.hpAttr)
}

func (m *AttrManager) AddHpAttrEffect(attr int, value int64, condType, condValue int) {
	m.hpAttrM.Add(attr, value, condType, condValue)
	//每次有这个天赋之后都重新计算一次
	m.ChangeHpAttr()
}

func (m *AttrManager) ClearAttr(addTime uint32) {
	for i := 0; i < goxml.AttrMaxNum; i++ {
		if m.changedAttrs[addTime][i] != 0 {
			m.changedAttrs[addTime][i] = 0
			m.AttrChanged(i)
		}
	}
}

func (m *AttrManager) AddHadBuffAttrEffect(skillID, buffType uint32, attr int, value int64) {
	m.hadBuffAttrM.Add(skillID, buffType, attr, value)
	m.initHadBuffAttr()
}

func (m *AttrManager) initHadBuffAttr() {
	for _, v := range m.hadBuffAttrM.Attrs {
		if v.Active {
			continue
		}
		if m.owner.HasBuff(v.BuffType) {
			m.changeAttrWithID(&m.hadBuffAttr, v.Attr, v.Value, BattleAdd)
			v.Active = true
		}
	}
}

func (m *AttrManager) ChangeBuff(isAdd bool, buffType uint32) {
	if isAdd {
		for _, v := range m.hadBuffAttrM.Attrs {
			if v.Active {
				continue
			}
			if v.BuffType == buffType {
				m.changeAttrWithID(&m.hadBuffAttr, v.Attr, v.Value, BattleAdd)
				v.Active = true
			}
		}
	} else {
		if m.owner.HasBuff(buffType) {
			return
		}
		for _, v := range m.hadBuffAttrM.Attrs {
			if !v.Active {
				continue
			}
			if v.BuffType == buffType {
				m.changeAttrWithID(&m.hadBuffAttr, v.Attr, v.Value, BattleDec)
				v.Active = false
			}
		}
	}
}

func (m *AttrManager) AddAliveNumAttrEffect(skillID, aliveNum uint32, attr int, value int64) {
	m.aliveNumAttrM.Add(skillID, aliveNum, attr, value)
	m.initAliveNumAttr()
}

func (m *AttrManager) initAliveNumAttr() {
	aliveNum := m.owner.GetManager().GetTeam(m.owner).GetAliveNum()
	for _, v := range m.aliveNumAttrM.Attrs {
		if v.Active {
			continue
		}
		if aliveNum >= v.AliveNum {
			m.changeAttrWithID(&m.aliveNumAttr, v.Attr, v.Value, BattleAdd)
			v.Active = true
		}
	}
}

func (m *AttrManager) UpdateAliveNum(aliveNum uint32) {
	for _, v := range m.aliveNumAttrM.Attrs {
		if aliveNum >= v.AliveNum {
			if !v.Active {
				m.changeAttrWithID(&m.aliveNumAttr, v.Attr, v.Value, BattleAdd)
				v.Active = true
			}
		} else {
			if v.Active {
				m.changeAttrWithID(&m.aliveNumAttr, v.Attr, v.Value, BattleDec)
				v.Active = false
			}
		}
	}
}

// IMP 这个就是策划眼中的基础属性
func (m *AttrManager) GetBaseAttr(id int) int64 {
	return m.baseAttr[id]
}

// 获取绝对值属性
func (m *AttrManager) GetBattleAbsAttr(id int) int64 {
	absAttr := m.getBattleAttr(id)
	return alt(absAttr, goxml.GetData().BattleParaInfoM.AttrMin[id], goxml.GetData().BattleParaInfoM.AttrMax[id])
}

func (m *AttrManager) GetAttr(id int) int64 {
	if !m.changeFlagM.IsSet(id) {
		return m.getTotalAttr(id)
	}
	base := m.GetBaseAttr(id)
	base += m.getBattleAttr(id)
	if base > 0 {
		pcts := goxml.GetAttrTypeToPct(id)
		for _, pct := range pcts {
			pctAttr := m.GetAttr(pct)
			base = int64(float64(base) * (float64(pctAttr) / BaseFloat))
		}
	}

	// 绝对值攻击力/防御力
	absAttrID := goxml.GetAttrTypeToAbsAttrID(id)
	if absAttrID > 0 {
		base += m.GetAttr(absAttrID)
	}

	// 独立百分比
	if base > 0 {
		pcts := goxml.GetAttrTypeToIndependentPct(id)
		for _, pct := range pcts {
			pctAttr := m.GetAttr(pct)
			base = int64(float64(base) * (float64(pctAttr) / BaseFloat))
		}
	}

	m.totalAttr[id] = base
	m.changeFlagM.Clear(id)
	return m.getTotalAttr(id)
}

func (m *AttrManager) getTotalAttr(id int) int64 {
	base := m.totalAttr[id]
	if goxml.IsPctTypeAttr(id) {
		base += BaseFloatInt
	}
	altBase := alt(base, goxml.GetData().BattleParaInfoM.AttrMin[id], goxml.GetData().BattleParaInfoM.AttrMax[id])
	return altBase
}

func (m *AttrManager) getBattleAttr(id int) int64 {
	return m.changedAttrs[ChangeAttrTimeForeve][id] + m.changedAttrs[ChangeAttrTimeOneEffect][id] +
		m.changedAttrs[ChangeAttrTimeOneAttack][id] + m.changedAttrs[ChangeAttrTimeOneSkill][id] +
		m.changedAttrs[ChangeAttrTimeBuff][id] + m.hpAttr[id] + m.hadBuffAttr[id] + m.aliveNumAttr[id] +
		m.owner.GetManager().GetTeam(m.owner).tUniter.GetHaloAttr(id)
}

type VoidAttrManager struct {
	IAttrM
	owner         *VoidMember
	oneAttackAttr []int64 //每次有目标的攻击的属行
}

// 虚拟单位属性
func NewVoidAttrManager(owner *VoidMember) *VoidAttrManager {
	return &VoidAttrManager{
		owner:         owner,
		oneAttackAttr: make([]int64, goxml.AttrMaxNum),
	}
}

func (m *VoidAttrManager) ChangeAttr(addTime, add uint32, attr int, value int64) {
	attrVal := m.getAttrByTime(addTime)
	if attrVal == nil {
		return
	}
	if add == BattleAdd {
		attrVal[attr] += value
	} else {
		attrVal[attr] -= value
	}
}

func (m *VoidAttrManager) getAttrByTime(addTime uint32) []int64 {
	switch addTime {
	case ChangeAttrTimeOneAttack:
		return m.oneAttackAttr
	}
	return nil
}

func (m *VoidAttrManager) GetBossAttrM() *AttrManager {
	return m.owner.boss.attrM
}

func (m *VoidAttrManager) GetBaseAttr(id int) int64 {
	attrM := m.GetBossAttrM()
	if attrM != nil {
		return attrM.GetBaseAttr(id)
	}
	return 0
}
func (m *VoidAttrManager) GetInitAttr(id int) int64 {
	attrM := m.GetBossAttrM()
	if attrM != nil {
		return attrM.GetInitAttr(id)
	}
	return 0
}

func (m *VoidAttrManager) ClearAttr(addTime uint32) {
	attrVal := m.getAttrByTime(addTime)
	for k := range attrVal {
		attrVal[k] = 0
	}
}

func (m *VoidAttrManager) GetAttr(id int) int64 {
	base := m.GetBaseAttr(id)
	base += m.getBattleAttr(id)
	if base > 0 {
		pcts := goxml.GetAttrTypeToPct(id)
		for _, pct := range pcts {
			pctAttr := m.GetBattlePctAttr(pct)
			base = int64(float64(base) * (float64(pctAttr) / BaseFloat))
		}
	}
	base = alt(base, goxml.GetData().BattleParaInfoM.AttrMin[id], goxml.GetData().BattleParaInfoM.AttrMax[id])
	return base
}

func (m *VoidAttrManager) GetBattlePctAttr(id int) int64 {
	pctAttr := m.getBattleAttr(id)
	pctAttr = alt(pctAttr, goxml.GetData().BattleParaInfoM.AttrMin[id], goxml.GetData().BattleParaInfoM.AttrMax[id])
	pctAttr += BaseFloatInt
	return pctAttr
}

func (m *VoidAttrManager) getBattleAttr(id int) int64 {
	return m.GetBossAttrM().getBattleAttr(id) + m.oneAttackAttr[id]
}

func (m *VoidAttrManager) ChangeBuff(isAdd bool, buffType uint32) {

}

func (m *VoidAttrManager) AttrChanged(attrType int) {
	m.GetBossAttrM().AttrChanged(attrType)
}

//战斗中需要缓存的效果

type PsAttrManager struct {
	IPoolObj
	owner Uniter

	attrs [ChangePsAttrTimeMax][PsAttrTotal]int64
}

func NewPsAttrManager(owner Uniter) *PsAttrManager {
	m := gPool.GetPsAttrManager()
	m.init(owner)
	return m
}

func (m *PsAttrManager) init(owner Uniter) {
	m.owner = owner
}

func (m *PsAttrManager) Reset() {
	*m = PsAttrManager{}
}

func (m *PsAttrManager) GetHurtedMax() int64 {
	var hurtMax int64

	var hurtMax1 int64
	hpPctMin := m.GetAttr(PsAttrHurtedHpPctMax)
	if hpPctMin > 0 {
		hurtMax1 = m.owner.GetMaxHp() * hpPctMin / BaseFloatInt
	}

	// 最大伤害修正，目前仅作用与非共血对象
	var hurtMax2 int64
	if m.owner.GetUniterType() == UniterTypeMember {
		mem := m.owner.(*Member)
		hpPctMin2 := m.GetAttr(PsAttrHurtedHpPctMax2)
		if hpPctMin2 > 0 {
			hurtMax2 = mem.GetMaxHpWithoutFixPct() * (hpPctMin2 / BaseFloatInt)
		}
	}
	if hurtMax1 > 0 || hurtMax2 > 0 {
		if hurtMax1 > 0 && hurtMax2 > 0 {
			hurtMax = min(hurtMax1, hurtMax2)
		} else if hurtMax1 > 0 {
			hurtMax = hurtMax1
		} else if hurtMax2 > 0 {
			hurtMax = hurtMax2
		}
	}

	hurtMin := m.GetAttr(PsAttrDefHurtedMax)
	if hurtMin > 0 && hurtMin < hurtMax {
		hurtMax = hurtMin
	}
	return hurtMax
}

func (m *PsAttrManager) GetHurtedHpMax() int64 {
	return m.GetAttr(PsAttrDefHurtedHpMax)
}

func (m *PsAttrManager) GetHurtedMaxByAttackAtk(attack Uniter) int64 {
	psAttr := m.GetAttr(PsAttrDefHurtedMaxByAttackAtk)
	if psAttr > 0 {
		return int64(float64(attack.GetAttr(goxml.AttrAttack)) * (float64(psAttr) / BaseFloat))
	}
	return 0
}

func (m *PsAttrManager) GetAttr(name int) int64 {
	var value int64 = 0
	haloAttr, exist := m.owner.GetManager().GetTeam(m.owner).tUniter.GetHaloPsAttr(name)
	if exist {
		value = haloAttr
	}
	addType := gPsAttrAddType[name]
	if addType == PsAttrAddTypeAdd {
		for i := 0; i < ChangePsAttrTimeMax; i++ {
			if m.attrs[i][name] != 0 {
				value += m.attrs[i][name]
			}
		}
	} else if addType == PsAttrAddTypeMin {
		for i := 0; i < ChangePsAttrTimeMax; i++ {
			if m.attrs[i][name] != 0 && (value == 0 || m.attrs[i][name] < value) {
				value = m.attrs[i][name]
			}
		}
	} else if addType == PsAttrAddTypeMax {
		for i := 0; i < ChangePsAttrTimeMax; i++ {
			if m.attrs[i][name] != 0 && (value == 0 || m.attrs[i][name] > value) {
				value = m.attrs[i][name]
			}
		}
	}
	return value
}

func (m *PsAttrManager) ChangeAttr(addTime int, add uint32, attr int, value int64) {
	//只有增加,没有减少的情况
	addType := gPsAttrAddType[attr]
	if addType == PsAttrAddTypeAdd {
		m.attrs[addTime][attr] += value
	} else if addType == PsAttrAddTypeMin {
		if m.attrs[addTime][attr] == 0 {
			m.attrs[addTime][attr] = value
		} else if value < m.attrs[addTime][attr] {
			m.attrs[addTime][attr] = value
		}
	} else if addType == PsAttrAddTypeMax {
		if value > m.attrs[addTime][attr] {
			m.attrs[addTime][attr] = value
		}
	}

	if attr == 35 {
		l4g.Debugf("ChangeAttr, defenseID:%d, addTime:%d, add:%d, value:%d, result:%d",
			m.owner.ID(), addTime, add, value, m.attrs[addTime][attr])
	}
}

func (m *PsAttrManager) ClearAttr(addTime int) {
	for i := 0; i < PsAttrTotal; i++ {
		m.attrs[addTime][i] = 0
	}
}

func (m *PsAttrManager) ChangePsAttrTimeNextSkill() bool {
	changed := false
	for i := 0; i < PsAttrTotal; i++ {
		if m.attrs[ChangePsAttrTimeNextSkill][i] != 0 {
			m.ChangeAttr(ChangePsAttrTimeOneSkill, BattleAdd, i, m.attrs[ChangePsAttrTimeNextSkill][i])
			m.attrs[ChangePsAttrTimeNextSkill][i] = 0
			changed = true
		}
	}
	return changed
}
