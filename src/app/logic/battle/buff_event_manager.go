package battle

import (
	"app/goxml"
)

type BuffEventManager struct {
	IPoolObj
	mgr        *Manager
	eventBuffs [goxml.Event_Max][]*Buff //事件触发时需要更新buff
}

func NewBuffEventManager(m *Manager) *BuffEventManager {
	bem := gPool.GetBuffEventManager()
	bem.Init(m)
	return bem
}

func (bem *BuffEventManager) Init(m *Manager) {
	bem.mgr = m
}

func (bem *BuffEventManager) Reset() {
	*bem = BuffEventManager{}
}

func (bem *BuffEventManager) TriggerEvent(m *Manager, event int, args interface{}) {
	// 避免执行RemoveEvents时，bem.eventBuffs被改变，导致遍历时产生bug
	tmpBuffs := make([]*Buff, 0, 1)
	tmpBuffs = append(tmpBuffs, bem.eventBuffs[event]...)
	for _, buff := range tmpBuffs {
		buff.OnEvent(m, event, args)
	}
}

func (bem *BuffEventManager) RegisterEvent(event int, buff *Buff) {
	bem.eventBuffs[event] = append(bem.eventBuffs[event], buff)
}

func (bem *BuffEventManager) RemoveEvents(buff *Buff) {
	for _, event := range buff.buffRegisterEvents {
		for index, b := range bem.eventBuffs[event] {
			if b.ID() == buff.ID() {
				bem.eventBuffs[event] = append(bem.eventBuffs[event][:index], bem.eventBuffs[event][index+1:]...)
				break
			}
		}
	}
}
