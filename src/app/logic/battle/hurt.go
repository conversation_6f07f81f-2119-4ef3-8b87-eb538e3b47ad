package battle

import (
	"app/goxml"
	"app/protos/out/bt"
)

type HurtResult struct {
	InitValue    int64 //本次伤害的初始值
	InitAltValue int64 //本次伤害的伤害减免的初始值
	Overflow     int64 //伤害溢出的值
	RealValue    int64 //实际造成的伤害,实际扣除的血量
	ShowValue    int64 //实际显示的值，一般用AfterShieldValue
	Factor       bt.FactorType
	HurtType     bt.SpecialType
	Dead         bool   //是否死亡
	defense      Uniter //受到伤害的目标
}

func (h *HurtResult) Reset() {
	*h = HurtResult{}
}

type CureResult struct {
	InitValue int64
	Overflow  int64
	RealValue int64 //实际添加的值
	ShowValue int64 //传递给前端的值, 用的是InitValue
	Factor    bt.FactorType
}

func (c *CureResult) Reset() {
	*c = CureResult{}
}

type AddBuffResult struct {
	Ret         int //-1算成功，也不算失败, 0 成功， 1,免疫，2抵抗
	SysBuff     *goxml.BuffInfo
	SysBuffType *goxml.BuffTypeInfo
}

type RemoveBuffResult struct {
	Ret int
}
