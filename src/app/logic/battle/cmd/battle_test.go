package main

import (
	"app/goxml"
	"app/logic/battle"
	"app/logic/character"
	"app/protos/out/bt"
	"app/protos/out/ret"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"testing"
	"time"

	l4g "github.com/ivanabc/log4go"
	//"google.golang.org/protobuf/proto"
	"github.com/gogo/protobuf/proto"
)

var br = `
[{"title":"A","num":1,"id":41003,"skills":[4100300,4100313,4100323,4100333,4100343,100111,100121,100131,100141],"attrs":{"1":1458371,"2":10796850,"5":725462,"6":645220,"4":274,"18":10600,"19":2600,"20":1300,"21":3500,"26":5750,"27":6900,"34":15000},"passive_skills":[]},{"title":"S","num":2,"id":24005,"skills":[2400500,2400513,2400523,2400533,2400543,100411,100421,100431,100441],"attrs":{"1":1408577,"2":11360970,"5":572439,"6":638147,"4":267,"18":12100,"19":1100,"20":1300,"21":3500,"24":2000,"26":5750,"27":6900,"34":15000}},{"title":"z","num":3,"id":13001,"skills":[1300100,1300113,1300123,1300133,1300143,100311,100321,100331,100341],"attrs":{"1":1790785,"2":9418184,"5":610361,"6":555459,"4":245,"18":13000,"19":2600,"20":4300,"21":500,"26":5750,"27":6900,"34":15000}},{"title":"x","num":4,"id":23002,"skills":[2300200,2300213,2300223,2300233,2300243,100311,100321,100331,100341],"attrs":{"1":1754935,"2":9512677,"5":622402,"6":561967,"4":243,"18":10600,"19":2600,"20":4300,"21":500,"26":5750,"27":6900,"28":2000,"34":15000}},{"title":"c","num":5,"id":44004,"skills":[4400400,4400413,4400423,4400433,4400443,100411,100421,100431,100441],"attrs":{"1":1469985,"2":11671668,"5":585214,"6":650432,"4":255,"18":12100,"19":1100,"20":1300,"21":3500,"23":2400,"24":2000,"26":5750,"27":6900,"34":15000}},{"title":"a","num":6,"id":41003,"skills":[4100300,4100313,4100323,4100333,4100343,100111,100121,100131,100141],"attrs":{"1":1458371,"2":10796850,"5":725462,"6":645220,"4":274,"18":10600,"19":2600,"20":1300,"21":3500,"26":6050,"27":7200,"34":15000},"passive_skills":[]},{"title":"s","num":7,"id":24005,"skills":[2400500,2400513,2400523,2400533,2400543,100411,100421,100431,100441],"attrs":{"1":1408577,"2":11360970,"5":572439,"6":638147,"4":267,"18":12100,"19":1100,"20":1300,"21":3500,"24":2000,"26":6050,"27":7200,"34":15000}},{"title":"z","num":8,"id":52005,"skills":[5200500,5200513,5200523,5200533,5200543,100211,100221,100231,100241],"attrs":{"1":1881736,"2":10211416,"5":509432,"6":572640,"4":263,"18":14900,"19":1100,"20":4300,"21":500,"26":6050,"27":7200,"34":15000}},{"title":"x","num":9,"id":54004,"skills":[5400400,5400413,5400423,5400433,5400443,100411,100421,100431,100441],"attrs":{"1":1484738,"2":11601664,"5":584236,"6":656452,"4":290,"18":12100,"19":1100,"20":1300,"21":3500,"26":6050,"27":7200,"31":1000,"34":15000}},{"title":"c","num":10,"id":22001,"skills":[2200100,2200113,2200123,2200133,2200143,100211,100221,100231,100241],"attrs":{"1":1803020,"2":10109936,"5":508984,"6":566444,"4":242,"18":12100,"19":1100,"20":5700,"21":500,"26":6050,"27":7200,"30":1400,"34":15000}},{"title":"1","num":101,"id":71506,"skills":[7150605,7150505,7150105],"attrs":{}},{"title":"1","num":104,"id":71506,"skills":[7150605,7150505,7150105],"attrs":{}}]
`

var round int
var reportByte int
var reportByte2 int

func Test_DoBattle1(t *testing.T) {
	l4g.ChangeFilterLevel("stdout", l4g.INFO)
	defer l4g.Close()
	dataPath := "../../../../../data/"
	goxml.Load(dataPath, false, false)

	members := GenBattleData()
	count := 1000
	for i := 0; i < count; i++ {
		DoBattle(members, true)
	}
	t.Logf("\n average o-report:%d, report:%d round:%d \n", reportByte/count, reportByte2/count, round/count)
	battle.DebugPool()
	time.Sleep(1 * time.Second)

}

func Benchmark_DoBattle(b *testing.B) {
	l4g.ChangeFilterLevel("stdout", l4g.INFO)
	defer l4g.Close()
	dataPath := "../../../../../data/"
	goxml.Load(dataPath, false, false)
	members := GenBattleData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		DoBattle(members, false)
	}
}

func GenBattleData() []*Member {
	//data := BattleData{}
	var members []*Member
	if err := json.Unmarshal([]byte(br), &members); err != nil {
		panic(fmt.Sprintf("DoOneBattle error:%s", err))
	}
	return members
}

func DoBattle(members []*Member, show bool) {
	//data := BattleData{}

	t1 := time.Now()
	attackMembers := make([]*battle.Member, 0, battle.TeamMaxPos)
	defenseMembers := make([]*battle.Member, 0, battle.TeamMaxPos)
	attackArtifactInfos := make([]*bt.ArtifactInfo, 0, 3)
	defenseArtifactInfos := make([]*bt.ArtifactInfo, 0, 3)

	altAttr := &battle.AltAttr{}
	var attack, defense *battle.Team

	attackAttr := make(map[uint32]map[int]int64)
	defenseAttr := make(map[uint32]map[int]int64)
	attackRaisePSs := make(map[uint32][]uint64)
	defenseRaisePSs := make(map[uint32][]uint64)
	for _, v := range members {
		if v.Num <= SplitPos {
			if v.ID > 0 {
				mem := battle.NewMemberAndNewSkills(uint64(v.ID), v.GetPos(), uint32(v.ID), v.GetSkills(),
					v.GetPassiveSkills(), v.GetAttr())
				if mem == nil {
					panic(fmt.Sprintf("DoOneBattle mem is nil:%d \n", v.ID))
				}
				if v.Num <= 5 {
					attackMembers = append(attackMembers, mem)
					mem.SetLinks(getLinks(mem, attackRaisePSs))
				} else {
					defenseMembers = append(defenseMembers, mem)
					mem.SetLinks(getLinks(mem, defenseRaisePSs))
				}
			}
		} else {
			pos := v.GetArtifactPos()
			ArtifactInfo := &bt.ArtifactInfo{
				Pos:        pos,
				SysId:      uint32(v.ID),
				Star:       0,
				StrengthLv: 1,
				ForgeLv:    1,
			}
			if len(v.GetArtifactSkill()) > 0 {
				ArtifactInfo.Skill = v.GetArtifactSkill()[0]
			}
			if v.Num <= SplitPos+3 {
				attackArtifactInfos = append(attackArtifactInfos, ArtifactInfo)
				if len(v.Attrs) > 0 {
					if _, ok := attackAttr[battle.ArtifactBattlePos]; !ok {
						attackAttr[battle.ArtifactBattlePos] = make(map[int]int64)
					}
					for attrK, attrV := range v.Attrs {
						attackAttr[battle.ArtifactBattlePos][attrK] += attrV
					}
				}
			} else {
				defenseArtifactInfos = append(defenseArtifactInfos, ArtifactInfo)
				if len(v.Attrs) > 0 {
					if _, ok := defenseAttr[battle.ArtifactBattlePos]; !ok {
						defenseAttr[battle.ArtifactBattlePos] = make(map[int]int64)
					}
					for attrK, attrV := range v.Attrs {
						defenseAttr[battle.ArtifactBattlePos][attrK] += attrV
					}
				}
			}
		}
	}

	altAttr.SetAttack(attackAttr)
	altAttr.SetDefense(defenseAttr)
	attack = battle.NewTeam(attackMembers, attackArtifactInfos, nil, &battle.TeamBase{
		IsCalculateLink: true,
	}, nil)
	defense = battle.NewTeam(defenseMembers, defenseArtifactInfos, nil, &battle.TeamBase{
		IsCalculateLink: true,
	}, nil)
	altRaisePS := battle.NewAltRaisePS()
	altRaisePS.AltAttack(attackRaisePSs)
	altRaisePS.AltDefense(defenseRaisePSs)
	params := battle.NewManagerParams(0, 0, 0, 0, altAttr, 0)
	params.SetSeasonID(0)
	params.SetAltRaisePS(altRaisePS)
	b := battle.NewManager(attack, defense, params)
	b.Run()
	if b.GetRet() != ret.RET_OK {
		panic("dobattle panic \n")
	}
	report := b.GetReport()
	if show {
		fmt.Printf("size:%d: time:%v, round:%d \n", report.Size(), time.Since(t1), len(report.GetRounds()))
	}
	clReport, err := proto.Marshal(report)
	if err != nil {
		panic(fmt.Sprintf("dobattle panic error %s \n", err))
	}
	reportByte += report.Size()
	reportByte2 += len(clReport)
	round += len(report.GetRounds())

	b.Free()
}

func Benchmark_DoBattleFromReport(b *testing.B) {
	l4g.ChangeFilterLevel("stdout", l4g.INFO)
	defer l4g.Close()
	dataPath := "../../../../../data/"
	goxml.Load(dataPath, false, false)
	file, err := os.Open("battleReport")
	if err != nil {
		b.Logf("get battleReport error %s \n", err)
		return
	}
	data, err := io.ReadAll(file)
	if err != nil {
		b.Logf("read battleReport error %s \n", err)
		return
	}
	battleReport := &bt.MultipleTeamsReport{}
	battleReport.ReportVersion = character.ReportVersion
	err = proto.Unmarshal(data, battleReport)
	if err != nil {
		b.Logf("parse battleReport error %s \n", err)
		return
	}
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		battleReportFight(battleReport)
	}
}
