package main

import (
	"io"
	"strings"

	//"app/protos/out/ret"
	//"app/logic/battle"

	//"encoding/xml"
	//"encoding/json"
	"flag"
	"fmt"
	"html/template"
	"net/http"
	"os"
	"path/filepath"

	"github.com/julienschmidt/httprouter"

	l4g "github.com/ivanabc/log4go"
)

var gAddr = flag.String("addr", ":12121", "端口地址")
var gDir = flag.String("onlineData", "../data/", "xml表配置地址")
var gTmpDir = flag.String("tmpData", "../tmpdata/", "xml表配置地址")
var runDir = ""

//var gConfig = flag.String("config", "../config/", "config地址")

func main() {
	flag.Parse()
	fmt.Printf("beigin battle test server \n")
	runDir = *gTmpDir + "data"
	ClearTmpData()
	sysData()
	loadData()

	router := httprouter.New()
	router.GET("/battle", DoOneBattle)
	router.POST("/battle", DoOneBattle)
	router.GET("/upload", Upload)
	router.POST("/upload", Upload)
	router.GET("/show", Show)
	router.GET("/clear", Clear)
	router.GET("/battleReport", BattleReport)
	router.POST("/battleReportToJson", BattleReportToJson)
	router.POST("/battleReportFight", BattleReportFight)
	router.GET("/battlePVPBatch", GetBattlePVPBatch)
	router.POST("/battlePVPBatch", PostBattlePVPBatch)
	router.GET("/ListResultFiles", ListResultFiles)
	router.GET("/download", downloadFileHandler)
	router.GET("/GetPVPBatchState", getPVPBatchState)
	l4g.Infof("Starting tool server on addr %s...", *gAddr)
	err := http.ListenAndServe(*gAddr, router)
	if err != nil {
		l4g.Errorf("begin server error %s", err)
		fmt.Printf("begin server error %s \n", err)
	}

}

// 上传文件
func Upload(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	t, err := template.New("battle").Parse(HTMLPage)
	if err != nil {
		if err = t.Execute(w, fmt.Sprintf("get html page error %s", err)); err != nil {
			l4g.Errorf("upload error:%s", err)
		}
		return
	}
	if r.Method == "GET" {
		if err = t.Execute(w, ""); err != nil {
			l4g.Errorf("upload error:%s", err)
		}
		return
	}
	r.ParseMultipartForm(0)
	for _, files := range r.MultipartForm.File {
		for _, fileHeader := range files {
			//拷贝到临时目录
			file, err := fileHeader.Open()
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			defer file.Close()
			path := filepath.Join(*gTmpDir, fileHeader.Filename)
			dst, err := os.Create(path)
			if err != nil {
				if err = t.Execute(w, fmt.Sprintf("create file error %s", err)); err != nil {
					l4g.Errorf("upload error:%s", err)
				}
				return
			}
			_, err = io.Copy(dst, file)
			if err != nil {
				if err = t.Execute(w, fmt.Sprintf("copy file error %s", err)); err != nil {
					l4g.Errorf("upload error:%s", err)
				}
				return
			}
			//拷贝到运行目录
			file1, err := fileHeader.Open()
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			defer file1.Close()
			path = filepath.Join(runDir, fileHeader.Filename)
			dst1, err := os.Create(path)
			if err != nil {
				if err = t.Execute(w, fmt.Sprintf("create file error %s", err)); err != nil {
					l4g.Errorf("upload error:%s", err)
				}
				return
			}
			_, err = io.Copy(dst1, file1)
			if err != nil {
				if err = t.Execute(w, fmt.Sprintf("copy file error %s", err)); err != nil {
					l4g.Errorf("upload error:%s", err)
				}
				return
			}
		}
	}
	t.Execute(w, "upload file success")
	//重新加载量表
	loadData()
}

func Clear(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	err := ClearTmpData()
	if err == nil {
		_, err = w.Write([]byte("clear tmp data success"))
	} else {
		_, err = w.Write([]byte(fmt.Sprintf("clear tmp data error %s", err)))
	}
	if err != nil {
		l4g.Errorf("clear error :%s", err)
	}
}

type XMLData struct {
	Table string
	XML   template.HTML
	Error string
}

// 查看文件
func Show(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	tmpl := template.New("ShowUploadFiles")
	t, err := tmpl.Parse(ListResultFilesPage)
	if err != nil {
		t.Execute(w, fmt.Sprintf("get html page error %s", err))
		return
	}
	fileDir := *gTmpDir
	filePaths := []string{}
	err = filepath.Walk(fileDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			relPath, err := filepath.Rel(fileDir, path)
			if err != nil {
				l4g.Errorf("计算相对路径时发生错误: %v\n", err)
				return nil
			}

			// 判断文件的深度是否为1
			parts := strings.Split(relPath, string(filepath.Separator))
			if len(parts) == 1 {
				// 只处理一级文件
				filePaths = append(filePaths, path)
			}
		}
		return nil
	})

	if err != nil {
		t.Execute(w, fmt.Sprintf("Failed to list files: %s", err.Error()))
		return
	}

	err = tmpl.Execute(w, filePaths)
	if err != nil {
		t.Execute(w, fmt.Sprintf("Failed to execute template: %s", err.Error()))
		return
	}
}
