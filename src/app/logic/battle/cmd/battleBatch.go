package main

import (
	"app/logic/battle"
	"app/logic/character"
	"app/protos/out/bt"
	"app/protos/out/common"
	"app/protos/out/ret"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	l4g "github.com/ivanabc/log4go"
	"github.com/julienschmidt/httprouter"
	"github.com/xuri/excelize/v2"
)

type BattlePVPBatchData struct {
	TemplateID       int
	FightTimes       int
	SeasonID         int
	ActiveSeasonLink bool
	Error            template.HTML
	Result           template.HTML
}

type BattlePVPBatchResult struct {
	Error   string
	results map[uint64][]*BatchFightReport
}

var (
	resultDir       = "../log/batchResult.log"
	resultDir2      = ""
	battleMutex     sync.Mutex
	totalResult     int
	processedResult int
	canRun          atomic.Bool
	startTime       time.Time
	result          *BattlePVPBatchResult
)

func BattlePVPBatch(fightTimes int, seasonID uint32, fid common.FORMATION_ID, checkSeason bool) {
	defer battleMutex.Unlock()
	BattlePVPBatchReset()
	defer func() {
		if err := recover(); err != nil {
			battle.ResetPool()
			l4g.Error(err)
			stackInfo := fmt.Sprintf("StackInfo: %s", debug.Stack())
			l4g.Error(stackInfo)
			result.Error = fmt.Sprintf("ERROR: %s StackInfo: %s", err, stackInfo)
		}
	}()
	attackers := character.AiAttackPool
	defensers := character.AiDefensePool
	totalResult = len(attackers) * len(defensers) * fightTimes
	for _, attack := range attackers {
		for _, defense := range defensers {
			pvpBatchFight(attack, defense, fightTimes, seasonID, fid, checkSeason)
		}
	}
	buildBatchFightResult()
}

func BattlePVPBatchReset() {
	startTime = time.Now()
	canRun.Store(true)
	processedResult = 0
	result = &BattlePVPBatchResult{
		results: make(map[uint64][]*BatchFightReport),
	}
	resultDir2 = resultDir + *gAddr
}

func pvpBatchFight(attack *character.User, defense *character.User, fightTimes int,
	seasonID uint32, fid common.FORMATION_ID, checkSeason bool) {
	batchFightReport := &BatchFightReport{
		AttackersFinal: make(map[uint32]*BattleMember),
		DefensersFinal: make(map[uint32]*BattleMember),
		Attack:         attack.ID(),
		Defense:        defense.ID(),
	}
	for i := 0; i < fightTimes; i++ {
		if !canRun.Load() {
			panic("force stoped")
		}
		if !checkSeason {
			battle.SetNotCheckSeason()
		}
		bat := getPvpSingleFightManager(attack, defense, seasonID, fid, checkSeason)
		bat.Run()
		if bat.GetRet() != ret.RET_OK {
			panic(bat.GetError())
		}
		report := bat.GetReport()
		mergeStaticInfo(report, batchFightReport)
		if fightTimes == 1 {
			genReportLog(report, attack.ID())
		}
		calBatchFightReport(int64(fightTimes), report, batchFightReport)
		bat.Free()
		battle.DebugPool()
		processedResult++
	}
	result.results[batchFightReport.Attack] = append(result.results[batchFightReport.Attack], batchFightReport)
}

func genReportLog(report *bt.Report, id uint64) {
	reportBytes, err := json.Marshal(report)
	if err != nil {
		panic(fmt.Sprintf("battle marshal error:%s", err))
	}
	fileName := "../log/" + time.Now().Format("2006-01-02-15-04-05") + "---" + strconv.FormatUint(id, 10) + ".log"
	file, err := os.Create(fileName)
	if err != nil {
		panic(fmt.Sprintf("Failed to create file: %s\n", err))
	}
	defer file.Close()
	_, err = file.Write(reportBytes)
	if err != nil {
		panic(err)
	}
}

func getPvpSingleFightManager(attack *character.User, defense *character.User,
	seasonID uint32, fid common.FORMATION_ID, checkSeason bool) *battle.Manager {
	battleParams := battle.NewManagerParams(fid, fid, 0, 0, nil, 0)
	battleParams.SetSeasonID(seasonID)

	//处理阵法相关战斗参数
	riteFlag := attack.SetRiteBattleParams(character.BattlePVP, battleParams, defense)
	if !riteFlag {
		panic(fmt.Sprintf("getPvpSingleFightManager.SetRiteBattleParams failed: %s", attack.UUID()))
	}

	// aSeasonTrainID, aSeasonTrainLevel,
	// 	aSeasonTrainRaisePSMap := attack.AiGetMonumentForBattle(seasonID, battleParams.GetAttackFID(),
	// 	battleParams.GetAttackTeamIndex(), checkSeason)
	// battleParams.SetAttackSeasonTrain(aSeasonTrainID, aSeasonTrainLevel)
	// if len(aSeasonTrainRaisePSMap) > 0 {
	// 	altRaisePS.AltAttack(aSeasonTrainRaisePSMap)
	// }
	// aSeasonTrainAttrsMap := attack.AiGetMonumentRuneAttrForBattle(seasonID)

	// dSeasonTrainID, dSeasonTrainLevel,
	// 	dSeasonTrainRaisePSMap := defense.AiGetMonumentForBattle(seasonID, battleParams.GetAttackFID(),
	// 	battleParams.GetAttackTeamIndex(), checkSeason)
	// battleParams.SetDefenseSeasonTrain(dSeasonTrainID, dSeasonTrainLevel)
	// if len(dSeasonTrainRaisePSMap) > 0 {
	// 	altRaisePS.AltDefense(dSeasonTrainRaisePSMap)
	// }
	// dSeasonTrainAttrsMap := defense.AiGetMonumentRuneAttrForBattle(seasonID)

	attackTeam, attackArtifactPSMap := attack.NewUserTeam(battleParams.GetAttackFID(), battleParams, true)
	if attackTeam == nil {
		panic(fmt.Sprintf("attack team error: %s", attack.UUID()))
	}

	defenseTeam, defenseArtifactPSMap := defense.NewUserTeam(battleParams.GetDefenseFID(), battleParams, false)
	if defenseTeam == nil {
		panic(fmt.Sprintf("defense team error: %s", defense.UUID()))
	}

	attack.AddRaisePSAndAttr(battleParams, true, attackArtifactPSMap, true)
	defense.AddRaisePSAndAttr(battleParams, false, defenseArtifactPSMap, true)
	return battle.NewManager(attackTeam, defenseTeam, battleParams)
}

func buildBatchFightResult() {
	err := os.MkdirAll(resultDir2, os.ModePerm)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to create directory: %s", err.Error())
		return
	}
	f := excelize.NewFile()
	defer f.Close()
	fileName := "/battleBatch" + startTime.Format("2006-01-02-15-04") + ".xlsx"
	winRateSheet := "WinRate"
	roundSheet := "Round"
	sourceData := "Source"
	minBigLettle := 65
	cellNum := 1
	f.SetSheetName("Sheet1", winRateSheet)
	f.NewSheet(roundSheet)
	f.NewSheet(sourceData)
	setDefenseName := false
	for attack, results := range result.results {
		cellNum++
		attackCell := fmt.Sprintf("%s%d", getCellString(minBigLettle), cellNum)
		f.SetCellValue(winRateSheet, attackCell, attack)
		f.SetCellValue(roundSheet, attackCell, attack)
		f.SetCellValue(sourceData, attackCell, attack)
		for index, sourceResult := range results {
			if !setDefenseName {
				DefenseNameCell := fmt.Sprintf("%s1", getCellString(minBigLettle+index+1))
				f.SetCellValue(winRateSheet, DefenseNameCell, sourceResult.Defense)
				f.SetCellValue(roundSheet, DefenseNameCell, sourceResult.Defense)
				f.SetCellValue(sourceData, DefenseNameCell, sourceResult.Defense)
			}

			defenseCell := fmt.Sprintf("%s%d", getCellString(minBigLettle+index+1), cellNum)
			f.SetCellValue(winRateSheet, defenseCell, sourceResult.Win)
			f.SetCellValue(roundSheet, defenseCell, sourceResult.Round)
			data, err := json.Marshal(sourceResult)
			if err != nil {
				result.Error = err.Error()
				return
			}
			f.SetCellValue(sourceData, defenseCell, data)
		}
		setDefenseName = true
	}

	if err := f.SaveAs(resultDir2 + fileName); err != nil {
		result.Error = fmt.Sprintf("Failed to buildBatchFightResult: %s", err.Error())
		return
	}
}

func getCellString(value int) string {
	minBigLettle := 65
	if value < minBigLettle {
		return ""
	}
	maxBigLettle := minBigLettle + 26
	if value >= minBigLettle && value < maxBigLettle {
		return fmt.Sprintf("%c", value)
	} else if value >= maxBigLettle {
		return fmt.Sprintf("%c", value/minBigLettle-1+minBigLettle) + fmt.Sprintf("%c", (value-maxBigLettle)%26+minBigLettle)
	}

	return ""
}

func ListResultFiles(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	tmpl := template.New("ListResultFiles")
	t, err := tmpl.Parse(ListResultFilesPage)
	if err != nil {
		t.Execute(w, fmt.Sprintf("get html page error %s", err))
		return
	}
	dir, err := os.Getwd()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	fileDir := dir + "/" + resultDir2
	filePaths := []string{}
	err = filepath.Walk(fileDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			filePaths = append(filePaths, path)
		}
		return nil
	})

	if err != nil {
		t.Execute(w, fmt.Sprintf("Failed to list files: %s", err.Error()))
		return
	}

	err = tmpl.Execute(w, filePaths)
	if err != nil {
		t.Execute(w, fmt.Sprintf("Failed to execute template: %s", err.Error()))
		return
	}
}

func GetBattlePVPBatch(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	data := &BattlePVPBatchData{}
	t, err := template.New("BattlePVPBatch").Parse(BattleBatchPage)
	if err != nil {
		data.Error = template.HTML(fmt.Sprintf("get html page error %s", err))
	}
	t.Execute(w, data)
}

func PostBattlePVPBatch(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	data := &BattlePVPBatchData{}
	t, err := template.New("BattlePVPBatch").Parse(BattleBatchPage)
	defer t.Execute(w, data)
	if err != nil {
		data.Error = template.HTML(fmt.Sprintf("get html page error %s", err))
		return
	}
	templateID, err := strconv.ParseInt(r.FormValue("TemplateID"), 10, 64)
	if err != nil {
		data.Error = template.HTML(fmt.Sprintf("parse TemplateID %s", err))
		return
	}
	fightTimes, err := strconv.ParseInt(r.FormValue("FightTimes"), 10, 64)
	if err != nil {
		data.Error = template.HTML(fmt.Sprintf("parse FightTimes %s", err))
		return
	}
	seasonID, err := strconv.ParseInt(r.FormValue("SeasonID"), 10, 64)
	if err != nil {
		data.Error = template.HTML(fmt.Sprintf("parse seasonID %s", err))
		return
	}

	fid := common.FORMATION_ID_FI_ARENA_ATTACK //非赛季玩法（单服竞技场）
	if r.FormValue("activeSeasonLink") == "1" {
		data.ActiveSeasonLink = true
		fid = common.FORMATION_ID_FI_SEASON_ARENA_THREE_ATTACK //赛季玩法
	}
	retAi := character.CreateAiTool(uint32(templateID), uint32(seasonID), runDir, !data.ActiveSeasonLink, uint32(fid))
	if retAi != uint32(ret.RET_OK) {
		data.Error = template.HTML(fmt.Sprintf("Gen AITools EROR: %d", retAi))
		return
	}
	if !battleMutex.TryLock() {
		data.Error = template.HTML("已经有战斗在执行中!")
		return
	} else {
		go BattlePVPBatch(int(fightTimes), uint32(seasonID), fid, !data.ActiveSeasonLink)
	}
	data.Result = template.HTML("成功执行战斗!")
}

func downloadFileHandler(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	fileName := r.URL.Query().Get("file")
	if fileName == "" {
		http.Error(w, "Missing 'file' parameter", http.StatusBadRequest)
		return
	}
	file, err := os.Open(fileName)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileInfo.Name()))
	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	_, err = io.Copy(w, file)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
}

func getPVPBatchState(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	if result != nil && len(result.Error) > 0 {
		http.Error(w, result.Error, http.StatusInternalServerError)
		return
	}
	for processedResult < totalResult && canRun.Load() {
		fmt.Fprintf(w, "%d / %d (完成/总任务量) \n", processedResult, totalResult)
		w.(http.Flusher).Flush() // 实时刷新响应
		time.Sleep(time.Second)
	}
	fmt.Fprintf(w, "%d / %d (完成/总任务量)", processedResult, totalResult)
}
