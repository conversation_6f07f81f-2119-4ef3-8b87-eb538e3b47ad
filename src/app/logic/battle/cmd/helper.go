package main

import (
	"os"
	"strings"
)

func FileExist(path string) bool {
	_, err := os.Stat(path) //os.Stat获取文件信息
	if err != nil {
		return os.IsExist(err)
	}
	return true
}

// 判断所给路径是否为文件夹
func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

func CreateDir(path string) bool {
	if !IsDir(path) {
		if err := os.Mkdir(path, os.ModePerm); err != nil {
			return false
		}
		return true
	}
	return true
}

func JSON2HTML(str string) string {
	newStr := make([]byte, 0, 100)
	index := 0
	preFirst := true
	for _, v := range str {
		if v == '[' {
			index++
			newStr = append(newStr, byte(v))
			preFirst = true
		} else if v == '{' {
			newStr = append(newStr, []byte("</br>")...)
			for i := 0; i < index*4; i++ {
				newStr = append(newStr, []byte("&nbsp")...)
			}
			newStr = append(newStr, byte(v))
			preFirst = true
			index++
		} else if v == ',' {
			newStr = append(newStr, byte(v))
			preFirst = true
		} else if v == ']' {
			index--
			newStr = append(newStr, []byte("</br>")...)
			for i := 0; i < index*4; i++ {
				newStr = append(newStr, []byte("&nbsp")...)
			}
			newStr = append(newStr, byte(v))
			//newStr = append(newStr, []byte("</br>")...)
			preFirst = true
		} else if v == '}' {
			index--
			newStr = append(newStr, []byte("</br>")...)
			for i := 0; i < index*4; i++ {
				newStr = append(newStr, []byte("&nbsp")...)
			}
			newStr = append(newStr, byte(v))
			preFirst = true
		} else {
			if !preFirst {
				newStr = append(newStr, []byte(string(v))...)
			} else {
				newStr = append(newStr, []byte("</br>")...)
				for i := 0; i < index*4; i++ {
					newStr = append(newStr, []byte("&nbsp")...)
				}
				newStr = append(newStr, []byte(string(v))...)
				preFirst = false
			}
		}
	}
	return string(newStr)
}

func XML2Html(content string) string {
	s := strings.Replace(content, "<", "&lt;", -1)
	s = strings.Replace(s, ">", "&gt;", -1)
	s = strings.Replace(s, "\n", "</br>", -1)
	return s

}
