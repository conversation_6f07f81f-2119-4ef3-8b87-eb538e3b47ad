package main

import (
	"app/logic/battle"
	"app/logic/character"
	"app/protos/out/bt"
	"app/protos/out/common"
	"app/protos/out/ret"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"net/http"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"github.com/julienschmidt/httprouter"
)

type BattleResult struct {
	Ret    ret.RET
	Error  string
	Report template.HTML
}

func BattleReport(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	t, err := template.New("battleReport").Parse(BattleReportPage)
	if err != nil {
		if err = t.Execute(w, fmt.Sprintf("get html page error %s", err)); err != nil {
			l4g.Errorf("upload error:%s", err)
		}
		return
	}
	data := &BattleData{}
	err = t.Execute(w, data)
	if err != nil {
		l4g.Errorf("BattleReport error:%s", err)
	}
}

func BattleReportToJson(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	file, _, err := r.FormFile("battleReport")
	if err != nil {
		w.Write([]byte(fmt.Sprintf("get battleReport error %s \n", err)))
		return
	}
	data, err := io.ReadAll(file)
	if err != nil {
		w.Write([]byte(fmt.Sprintf("read battleReport error %s \n", err)))
		return
	}
	battleReport := &bt.MultipleTeamsReport{}
	battleReport.ReportVersion = character.ReportVersion
	err = proto.Unmarshal(data, battleReport)
	if err != nil {
		w.Write([]byte(fmt.Sprintf("parse battleReport error %s \n", err)))
		return
	}
	jsonData, err := json.Marshal(battleReport)
	if err != nil {
		w.Write([]byte(fmt.Sprintf("Marshal battleReport to json error %s \n", err)))
		return
	}

	w.Write(jsonData)
}

func BattleReportFight(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	t, err := template.New("BattleReportFight").Parse(BattleReportFightPage)
	if err != nil {
		if err = t.Execute(w, fmt.Sprintf("get html page error %s", err)); err != nil {
			l4g.Errorf("BattleReportFight error:%s", err)
		}
		return
	}

	file, _, err := r.FormFile("battleReport")
	if err != nil {
		w.Write([]byte(fmt.Sprintf("get battleReport error %s \n", err)))
		return
	}
	data, err := io.ReadAll(file)
	if err != nil {
		w.Write([]byte(fmt.Sprintf("read battleReport error %s \n", err)))
		return
	}
	battleReport := &bt.MultipleTeamsReport{}
	battleReport.ReportVersion = character.ReportVersion
	err = proto.Unmarshal(data, battleReport)
	if err != nil {
		w.Write([]byte(fmt.Sprintf("parse battleReport error %s \n", err)))
		return
	}
	battleResult := doBattle(battleReport)
	err = t.Execute(w, battleResult)
	if err != nil {
		l4g.Errorf("BattleReportFight error:%s", err)
	}
}

func doBattle(battleReport *bt.MultipleTeamsReport) *BattleResult {
	prepareBattle(battleReport)
	return battleReportFight(battleReport)
}

func prepareBattle(battleReport *bt.MultipleTeamsReport) {
	battle.OpenDebugFormula()
	battle.OpenDebugEvent()
	battle.OpenDebugAttr()
}

func battleReportFight(battleReport *bt.MultipleTeamsReport) *BattleResult {
	result := &BattleResult{}
	reportData := &bt.MultipleTeamsReport{
		Id:            battleReport.Id,
		ClientData:    battleReport.ClientData,
		Pvp:           battleReport.Pvp,
		ExtraId:       battleReport.ExtraId,
		Win:           battleReport.Win,
		FormationId:   battleReport.FormationId,
		ReportVersion: character.ReportVersion,
	}

	battleParams := battle.NewManagerParams(common.FORMATION_ID(battleReport.FormationId), 0,
		0, 0, nil, 0)
	battleParams.SetSeasonID(0)
	for _, report := range battleReport.Reports {
		b, err := reportToBattleManager(report, battleParams)
		if err != nil {
			result.Error = err.Error()
			return result
		}
		defer b.Free()
		b.Run()
		if b.GetRet() != ret.RET_OK {
			result.Error = b.GetError()
			return result
		}
		reReport := b.GetReport()
		reportData.Reports = append(reportData.Reports, reReport)
	}
	jsonData, err := json.Marshal(reportData)
	if err != nil {
		result.Error = fmt.Sprintf("Marshal battleReport to json error %s \n", err)
		return result
	}
	result.Ret = ret.RET_OK
	result.Report = template.HTML(JSON2HTML(string(jsonData)))
	return result
}

func reportToBattleManager(battleReport *bt.Report, battleParams *battle.ManagerParams) (*battle.Manager, error) {
	// battleParams.SetMaxRound(battleReport.ReportInfo.MaxRound)
	// battleParams.SetNotFinish(battleReport.ReportInfo.NotFinish)

	// var attack, defense *battle.Team
	// var artifacts []*bt.ArtifactInfo
	// attackMembers := make([]*battle.Member, 0, battle.TeamMaxPos)
	// for _, member := range battleReport.Attackers {
	// 	isSeasonAddHero := character.IsSeasonAddHero(battleParams.GetSeasonID(), battleParams.GetAttackFID(), member.Info.SysId)
	// 	attackMember := battle.NewMember(
	// 		member.Info.UniqId, member.Info.Pos, member.Info.SysId, member.Info.Stage, member.Info.Star,
	// 		member.Info.Level, 0, member.Info.Dress, member.Info.AwakenLevel, 0, member.Attr, member.Info.RaisePs, 0, nil, nil, isSeasonAddHero, nil)
	// 	attackMember.SetHp(member.NowHp)
	// 	attackMembers = append(attackMembers, attackMember)
	// }
	// if battleReport.AttackArtifact != nil {
	// 	artifacts = battleReport.AttackArtifact.Infos
	// }
	// attack = battle.NewTeam(attackMembers, artifacts, nil, &battle.TeamBase{
	// 	IsCalculateLink: true,
	// 	Power:           battleReport.ReportInfo.AttackPower,
	// 	Name:            battleReport.ReportInfo.AttackName,
	// }, nil)
	// artifacts = nil
	// if len(battleReport.AttackTeamMember.RaisePs) > 0 {
	// 	attack.GetTeamUniter().SetRaisePSs(battleReport.AttackTeamMember.RaisePs)
	// }

	// if battleReport.ReportInfo.MonsterGroupId != 0 {
	// 	defense, _ = battle.NewMonsterGroupTeam(battleReport.ReportInfo.MonsterGroupId, uint32(common.RITE_RESTRICT_STATE_RRS_NONE), 0)
	// 	if defense == nil {
	// 		return nil, fmt.Errorf("monster gen error : %d", battleReport.ReportInfo.MonsterGroupId)
	// 	}
	// } else {
	// 	defenseMembers := make([]*battle.Member, 0, battle.TeamMaxPos)
	// 	for _, member := range battleReport.Defensers {
	// 		isSeasonAddHero := character.IsSeasonAddHero(battleParams.GetSeasonID(), battleParams.GetAttackFID(), member.Info.SysId)
	// 		defenseMember := battle.NewMember(
	// 			member.Info.UniqId, member.Info.Pos, member.Info.SysId, member.Info.Stage, member.Info.Star,
	// 			member.Info.Level, 0, member.Info.Dress, member.Info.AwakenLevel, 0, member.Attr, member.Info.RaisePs, 0, nil, nil, isSeasonAddHero, nil)
	// 		defenseMember.SetHp(member.NowHp)
	// 		defenseMembers = append(defenseMembers, defenseMember)
	// 	}
	// 	if battleReport.DefenseArtifact != nil {
	// 		artifacts = battleReport.DefenseArtifact.Infos
	// 	}
	// 	defense = battle.NewTeam(defenseMembers, artifacts, nil, &battle.TeamBase{
	// 		IsCalculateLink: true,
	// 		Power:           battleReport.ReportInfo.DefensePower,
	// 		Name:            battleReport.ReportInfo.DefenseName,
	// 	}, nil)
	// 	if len(battleReport.DefenseTeamMember.RaisePs) > 0 {
	// 		defense.GetTeamUniter().SetRaisePSs(battleReport.DefenseTeamMember.RaisePs)
	// 	}
	// }

	// return battle.NewManager(attack, defense, battleParams), nil
	return nil, nil
}
