package main

import (
	"app/goxml"
	"app/logic/battle"
	"app/logic/character"
	"app/protos/out/bt"
	"app/protos/out/common"
	"app/protos/out/ret"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"net/http"
	"runtime/debug"
	"strconv"
	"sync"

	l4g "github.com/ivanabc/log4go"
	"github.com/shopspring/decimal"

	"github.com/julienschmidt/httprouter"
	//l4g "github.com/ivanabc/log4go"
)

const (
	SplitPos  int    = 100
	GetMethod string = "GET"
)

type BatchFightReport struct {
	Attackers       []*bt.Member             `json:"attackers"`
	Defensers       []*bt.Member             `json:"defensers"`
	AttackArtifact  *bt.Artifact             `json:"attack_artifact"`
	DefenseArtifact *bt.Artifact             `json:"defense_artifact"`
	Win             decimal.Decimal          `json:"win"`
	Round           decimal.Decimal          `json:"round"`
	TotalHurtShow   decimal.Decimal          `json:"total_hurt_show"`
	Stas            []*BattleSta             `json:"stas"`
	AttackersFinal  map[uint32]*BattleMember `json:"attackers_final"`
	DefensersFinal  map[uint32]*BattleMember `json:"defensers_final"`
	Attack          uint64                   `json:"attack"`
	Defense         uint64                   `json:"defense"`
}

type BattleSta struct {
	Mid    uint32          `json:"mid"`
	Hurt   decimal.Decimal `json:"hurt"`
	Cure   decimal.Decimal `json:"cure"`
	BeHurt decimal.Decimal `json:"be_hurt"`
}

type BattleMember struct {
	ID    uint32          `json:"id"`
	SysID uint32          `json:"sys_id"`
	Pos   uint32          `json:"pos"`
	NowHp decimal.Decimal `json:"now_hp"`
	MaxHp decimal.Decimal `json:"max_hp"`
}

type Member struct {
	Title         string        `json:"title"`
	Num           int           `json:"num"`
	ID            int64         `json:"id"`
	Dress         uint32        `json:"dress"`
	Star          uint32        `json:"star"`
	AwakenLevel   uint32        `json:"awaken_level"`
	Skills        []uint32      `json:"skills"`
	PassiveSkills []uint32      `json:"passive_skills"`
	Attrs         map[int]int64 `json:"attrs"`
	NeedNotKill   uint32        `json:"need_not_kill"`
	IsVoid        bool          //是否是虚拟单位
	BossPos       uint32        //boss的位置
	IsMonster     bool          //是否是怪物 如果是怪物id对应的就是monster_info的id
	//Attrs    []*Attr
	//MapAttrs map[string]*Attr
}

func GetNumByIndexAndPos(index int, pos int) int {
	if index == 0 {
		if pos <= 5 {
			return pos
		}
		return pos - 5 + SplitPos
	} else {
		if pos <= 5 {
			return pos + 5
		}
		return pos - 5 + SplitPos + 3
	}
}

func (m *Member) GetPos() uint32 {
	if m.Num <= 5 {
		return uint32(m.Num)
	}
	return uint32(m.Num - 5)
}

func (m *Member) GetArtifactPos() uint32 {
	num := m.Num - SplitPos
	if num <= 3 {
		return uint32(num)
	}
	return uint32(num - 3)
}

/*
func (m *Member) GetStage() uint32 {
	return m.Stage
}

func (m *Member) GetStar() uint32 {
	return m.Star
}
*/

func (m *Member) GetAttr() []int64 {
	attr := make([]int64, goxml.AttrMaxNum)
	for k, v := range m.Attrs {
		attr[k] = v
	}
	return attr
}

func (m *Member) GetSkills() []uint32 {
	return m.Skills
}

func (m *Member) GetArtifactSkill() []uint32 {
	return m.Skills
}

func (m *Member) GetPassiveSkills() []uint32 {
	return m.PassiveSkills
}

type BattleData struct {
	ShowDebugFormula          bool
	ShowDebugEvent            bool
	ShowDebugAttr             bool
	ShowDebugBuff             bool
	StrictPool                bool
	ShowTargetInfo            bool
	SetDefaultMaxSeasonEnergy bool
	FormationID               uint32
	MonsterGroup              uint32
	MaxRound                  uint32
	MemberInfo                string
	Error                     template.HTML
	MaxTimes                  int64
	Report                    template.HTML
	SeasonID                  uint32
}

func NewMember(i int) *Member {
	attrs := make(map[int]int64, 3)
	attrs[1] = 100
	attrs[2] = 1000
	attrs[3] = 10
	title := ""
	if i <= 5 {
		title = fmt.Sprintf("攻击方%d号位", i)
	} else {
		title = fmt.Sprintf("防守方%d号位", i-5)
	}
	m := &Member{
		Num:           i,
		Title:         title,
		Attrs:         attrs,
		Skills:        []uint32{},
		PassiveSkills: []uint32{},
	}
	return m
}

func doTestBattle(w io.Writer) error {
	tmpl := template.New("battle")
	//tmpl.Funcs(template.FuncMap{"Json2Html": JSON2HTML})
	t, err := tmpl.Parse(BattlePage)
	if err != nil {
		return t.Execute(w, fmt.Sprintf("get html page error %s", err))
	}

	data := BattleData{}
	//data.Titles = gTitle
	members := make([]*Member, 0, 10)
	members = append(members, NewMember(1))
	members = append(members, NewMember(2))
	members = append(members, NewMember(6))
	content, err := json.MarshalIndent(members, "", "\t")
	if err != nil {
		data.Error = template.HTML(fmt.Sprintf("marshal memberinfo error:%s", err))
		return t.Execute(w, data)
	}
	data.MemberInfo = string(content)
	data.FormationID = 0
	data.MonsterGroup = 0
	data.MaxRound = 0
	data.MaxTimes = 1
	data.Report = "Battle report"
	data.SeasonID = 0
	return t.Execute(w, data)
}

func prepareBattleData(r *http.Request, data *BattleData) {
	data.MemberInfo = r.FormValue("memberinfos")
	FormationID, _ := strconv.ParseInt(r.FormValue("FormationID"), 10, 64)
	data.FormationID = uint32(FormationID)
	monsterGroup, _ := strconv.ParseInt(r.FormValue("monsterGroup"), 10, 64)
	data.MonsterGroup = uint32(monsterGroup)
	seasonID, _ := strconv.ParseInt(r.FormValue("seasonID"), 10, 64)
	data.SeasonID = uint32(seasonID)
	if r.FormValue("showDebugFormula") == "1" {
		data.ShowDebugFormula = true
	}
	if r.FormValue("showDebugEvent") == "1" {
		data.ShowDebugEvent = true
	}
	if r.FormValue("showDebugAttr") == "1" {
		data.ShowDebugAttr = true
	}
	if r.FormValue("showDebugBuff") == "1" {
		data.ShowDebugBuff = true
	}
	if r.FormValue("strictPool") == "1" {
		data.StrictPool = true
	}
	if r.FormValue("showTargetInfo") == "1" {
		data.ShowTargetInfo = true
	}
	if r.FormValue("setDefaultMaxSeasonEnergy") == "1" {
		data.SetDefaultMaxSeasonEnergy = true
	}
	maxRound, _ := strconv.ParseInt(r.FormValue("maxRound"), 10, 64)
	data.MaxRound = uint32(maxRound)
	data.MaxTimes, _ = strconv.ParseInt(r.FormValue("maxTimes"), 10, 64)
	battle.SetStrictPool(data.StrictPool)
	if data.MaxTimes == 1 {
		l4g.ChangeFilterLevel("file", l4g.DEBUG)
		l4g.ChangeFilterLevel("stdout", l4g.DEBUG)

		if data.ShowDebugFormula {
			battle.OpenDebugFormula()
		}
		if data.ShowDebugEvent {
			battle.OpenDebugEvent()
		}
		if data.ShowDebugAttr {
			battle.OpenDebugAttr()
		}
		if data.ShowDebugBuff {
			battle.OpenDebugBuff()
		}
		if data.SetDefaultMaxSeasonEnergy {
			battle.SetDefaultMaxSeasonEnergy()
		}
	} else {
		l4g.ChangeFilterLevel("file", l4g.INFO)
		l4g.ChangeFilterLevel("stdout", l4g.INFO)
	}
}

var mutex sync.Mutex

func DoOneBattle(w http.ResponseWriter, r *http.Request, p httprouter.Params) {
	if r.Method == GetMethod {
		if err := doTestBattle(w); err != nil {
			l4g.Errorf("DoOneBattle error:%s", err)
			_, _ = w.Write([]byte(err.Error()))
		}
		return
	}

	mutex.Lock()
	defer mutex.Unlock()

	tmpl := template.New("battle")
	t, err := tmpl.Parse(BattlePage)
	if err != nil {
		if err = t.Execute(w, fmt.Sprintf("get html page error %s", err)); err != nil {
			l4g.Errorf("DoOneBattle error:%s", err)
		}
		return
	}

	data := &BattleData{}
	defer func() {
		if err := recover(); err != nil {
			battle.ResetPool()
			l4g.Error(err)
			stackInfo := fmt.Sprintf("StackInfo: %s", debug.Stack())
			l4g.Error(stackInfo)
			data.Error = template.HTML(fmt.Sprintf("ERROR: %s", err))
			_ = t.Execute(w, data)
		}
	}()
	prepareBattleData(r, data)
	fight(data)
	err = t.Execute(w, data)
	if err != nil {
		l4g.Errorf("DoOneBattle error:%s", err)
	}
}

func fight(data *BattleData) {
	var batchFightReport *BatchFightReport
	if data.MaxTimes > 1 {
		batchFightReport = &BatchFightReport{
			AttackersFinal: make(map[uint32]*BattleMember),
			DefensersFinal: make(map[uint32]*BattleMember),
		}
	}
	for i := 0; i < int(data.MaxTimes); i++ {
		b := newBattleManager(data)
		b.Run()
		if b.GetRet() != ret.RET_OK {
			data.Error = template.HTML(b.GetError())
			return
		}
		report := b.GetReport()
		if data.MaxTimes > 1 {
			mergeStaticInfo(report, batchFightReport)
			calBatchFightReport(data.MaxTimes, report, batchFightReport)
		} else {
			reportData := &bt.MultipleTeamsReport{
				Win:           report.ReportInfo.Win,
				FormationId:   data.FormationID,
				ReportVersion: character.ReportVersion,
			}
			reportData.Reports = append(reportData.Reports, report)
			reportBytes, err := json.Marshal(reportData)
			if err != nil {
				panic(fmt.Sprintf("battle marshal error:%s", err))
			}
			data.Report = template.HTML(JSON2HTML(string(reportBytes)))
		}
		b.Free()
		battle.DebugPool()
	}

	if data.MaxTimes > 1 {
		report, err := json.Marshal(batchFightReport)
		if err != nil {
			panic(fmt.Sprintf("battle marshal error:%s", err))
		}
		data.Report = template.HTML(JSON2HTML(string(report)))
	}
}

func altAttrByFunctionID(attack, _ *battle.Team, attr *battle.AltAttr, functionID uint32) {
	switch functionID {
	case uint32(common.FORMATION_ID_FI_GUILD_DUNGEON):
		attackAttr := make(map[uint32]map[int]int64)
		for _, v := range attack.GetAllMembers() {
			if v.GetStar() > 0 {
				attr := make(map[int]int64)
				attr[int(goxml.GetData().GuildConfigInfoM.DungeonStarBuffType)] =
					int64(v.GetStar() * goxml.GetData().GuildConfigInfoM.DungeonStarBuffValue)
				attackAttr[v.Pos()] = attr
			}
		}
		attr.SetAttack(attackAttr)
	default:
		break
	}
}

func calBatchFightReport(times int64, report *bt.Report, batchFightReport *BatchFightReport) {
	value := decimal.NewFromInt(1).Div(decimal.NewFromInt(times))
	base := decimal.NewFromInt(battle.BaseFloatInt)
	if report.ReportInfo.Win {
		batchFightReport.Win = batchFightReport.Win.Add(value.Mul(base))
	}
	batchFightReport.Round =
		batchFightReport.Round.Add(decimal.NewFromInt(int64(len(report.Rounds) - 1)).Mul(value))
	batchFightReport.TotalHurtShow =
		batchFightReport.TotalHurtShow.Add(decimal.NewFromInt(int64(report.ReportInfo.TotalHurtShow)).Mul(value))
	for _, reportStat := range report.Stas {
		isFind := false
		for _, batchStat := range batchFightReport.Stas {
			if reportStat.Mid == batchStat.Mid {
				isFind = true
				batchStat.Hurt =
					batchStat.Hurt.Add(decimal.NewFromInt(int64(reportStat.Hurt)).Mul(value))
				batchStat.Cure =
					batchStat.Cure.Add(decimal.NewFromInt(int64(reportStat.Cure)).Mul(value))
				batchStat.BeHurt =
					batchStat.BeHurt.Add(decimal.NewFromInt(int64(reportStat.BeHurt)).Mul(value))
			}
		}
		if !isFind {
			batchStat := &BattleSta{
				Mid:    reportStat.Mid,
				Hurt:   decimal.NewFromInt(int64(reportStat.Hurt)).Mul(value),
				Cure:   decimal.NewFromInt(int64(reportStat.Cure)).Mul(value),
				BeHurt: decimal.NewFromInt(int64(reportStat.BeHurt)).Mul(value),
			}
			batchFightReport.Stas = append(batchFightReport.Stas, batchStat)
		}
	}
	for _, reportFinal := range report.AttackersFinal {
		if batchFinal, exist := batchFightReport.AttackersFinal[reportFinal.Id]; exist {
			batchFinal.NowHp =
				batchFinal.NowHp.Add(decimal.NewFromInt(reportFinal.NowHp).Mul(value))
		} else {
			batchFightReport.AttackersFinal[reportFinal.Id] = &BattleMember{
				ID:    reportFinal.Id,
				SysID: reportFinal.Info.SysId,
				Pos:   reportFinal.Info.Pos,
				MaxHp: decimal.NewFromInt(reportFinal.MaxHp),
			}
		}
	}
	for _, reportFinal := range report.DefensersFinal {
		if batchFinal, exist := batchFightReport.DefensersFinal[reportFinal.Id]; exist {
			batchFinal.NowHp =
				batchFinal.NowHp.Add(decimal.NewFromInt(reportFinal.NowHp).Mul(value))
		} else {
			batchFightReport.DefensersFinal[reportFinal.Id] = &BattleMember{
				ID:    reportFinal.Id,
				SysID: reportFinal.Info.SysId,
				Pos:   reportFinal.Info.Pos,
				NowHp: decimal.NewFromInt(reportFinal.NowHp).Mul(value),
				MaxHp: decimal.NewFromInt(reportFinal.MaxHp),
			}
		}
	}
}

func mergeStaticInfo(report *bt.Report, batchFightReport *BatchFightReport) {
	if len(batchFightReport.Attackers) != 0 {
		return
	}
	for _, mem := range report.Attackers {
		batchFightReport.Attackers = append(batchFightReport.Attackers, mem.Clone())
	}
	for _, mem := range report.Defensers {
		batchFightReport.Defensers = append(batchFightReport.Defensers, mem.Clone())
	}

	batchFightReport.AttackArtifact = report.AttackArtifact.Clone()
	batchFightReport.DefenseArtifact = report.DefenseArtifact.Clone()
}

func getFormationAttr(battleMems map[uint32]*battle.Member) map[int]int64 {
	raceInfo := make(map[uint32]uint32)
	for _, mem := range battleMems {
		sysInfo := goxml.GetData().HeroInfoM.Index(mem.GetSysID())
		raceInfo[sysInfo.Race]++
	}

	formationAttr := battle.GetFormationAltAttr(raceInfo)
	return formationAttr
}

func newBattleManager(data *BattleData) *battle.Manager {
	var members []*Member
	if err := json.Unmarshal([]byte(data.MemberInfo), &members); err != nil {
		panic(fmt.Sprintf("unmarshal member info error:%s", err))
	}
	altAttr := &battle.AltAttr{}
	var attack, defense *battle.Team
	attackMembers := make([]*battle.Member, 0, battle.TeamMaxPos)
	defenseMembers := make([]*battle.Member, 0, battle.TeamMaxPos)
	attackArtifactInfos := make([]*bt.ArtifactInfo, 0, 3)
	defenseArtifactInfos := make([]*bt.ArtifactInfo, 0, 3)

	attackAttr := make(map[uint32]map[int]int64)
	defenseAttr := make(map[uint32]map[int]int64)
	attackRaisePSs := make(map[uint32][]uint64)
	defenseRaisePSs := make(map[uint32][]uint64)
	voids := make([]*Member, 0, 2)

	for _, v := range members {
		if v.Num <= SplitPos {
			if v.ID > 0 {
				var mem *battle.Member
				if v.IsVoid {
					//虚拟怪
					voids = append(voids, v)
					continue
				} else {
					if v.IsMonster {
						monster := goxml.GetData().MonsterInfoM.Index(uint32(v.ID))
						if monster == nil {
							panic(fmt.Sprintf("battle no find monster error:pos:%d id:%d", v.GetPos(), v.ID))
						}

						mem = battle.NewMemberAndNewSkills(uint64(v.ID), v.GetPos(), monster.HeroId, v.GetSkills(),
							v.GetPassiveSkills(), v.GetAttr())

						mem.SetSysID(uint32(v.ID))
						mem.SetElite(monster.IsElite)
						mem.SetMonsterType(monster.Type)
						mem.SetPublicHp(monster.PublicHp)
						mem.SetStar(monster.Star)
					} else {
						mem = battle.NewMemberAndNewSkills(uint64(v.ID), v.GetPos(), uint32(v.ID), v.GetSkills(),
							v.GetPassiveSkills(), v.GetAttr())
						mem.SetStar(v.Star)
						mem.SetDress(v.Dress)
						heroInfo := goxml.GetData().HeroInfoM.Index(uint32(v.ID))
						if heroInfo != nil && heroInfo.IsAwakenable() && goxml.GetData().ConfigInfoM.IsHeroAwakenStarUnlocked(v.Star) {
							mem.SetAwakenLevel(v.AwakenLevel)
						}
					}
				}
				if mem == nil {
					panic(fmt.Sprintf("battle no find hero error:pos:%d id:%d", v.GetPos(), v.ID))
				}
				if v.NeedNotKill == 1 {
					mem.SetNeedNotKill()
				}
				if v.Num <= 5 {
					attackMembers = append(attackMembers, mem)
					mem.SetLinks(getLinks(mem, attackRaisePSs))
				} else {
					defenseMembers = append(defenseMembers, mem)
					mem.SetLinks(getLinks(mem, defenseRaisePSs))
				}
			}
		} else {
			pos := v.GetArtifactPos()
			ArtifactInfo := &bt.ArtifactInfo{
				Pos:        pos,
				SysId:      uint32(v.ID),
				Star:       v.Star,
				StrengthLv: 1,
				ForgeLv:    1,
			}
			if len(v.GetArtifactSkill()) > 0 {
				ArtifactInfo.Skill = v.GetArtifactSkill()[0]
			}
			if v.Num <= SplitPos+3 {
				attackArtifactInfos = append(attackArtifactInfos, ArtifactInfo)
				if len(v.Attrs) > 0 {
					if _, ok := attackAttr[battle.ArtifactBattlePos]; !ok {
						attackAttr[battle.ArtifactBattlePos] = make(map[int]int64)
					}
					for attrK, attrV := range v.Attrs {
						attackAttr[battle.ArtifactBattlePos][attrK] += attrV
					}
				}
			} else {
				defenseArtifactInfos = append(defenseArtifactInfos, ArtifactInfo)
				if len(v.Attrs) > 0 {
					if _, ok := defenseAttr[battle.ArtifactBattlePos]; !ok {
						defenseAttr[battle.ArtifactBattlePos] = make(map[int]int64)
					}
					for attrK, attrV := range v.Attrs {
						defenseAttr[battle.ArtifactBattlePos][attrK] += attrV
					}
				}
			}
		}
	}

	voidMembers := make([]*battle.VoidMember, 0, 1)
	for _, v := range voids {
		bossPos := v.BossPos
		var boss *battle.Member
		for _, m := range defenseMembers {
			if m.Pos() == bossPos {
				boss = m
				break
			}
		}
		if boss == nil {
			panic(fmt.Sprintf("battle no find boss:pos:%d id:%d", bossPos, v.ID))
		}
		pos := v.GetPos()
		voidMember := battle.NewVoid(uint32(v.ID), pos, boss)
		for _, ps := range v.PassiveSkills {
			voidMember.AddNewSysPassiveSkill(ps)
		}
		voidMembers = append(voidMembers, voidMember)
	}

	altAttr.SetAttack(attackAttr)
	altAttr.SetDefense(defenseAttr)
	attack = battle.NewTeam(attackMembers, attackArtifactInfos, nil, &battle.TeamBase{
		IsCalculateLink: true,
	}, nil)
	if data.MonsterGroup > 0 {
		defense, defenseRaisePSs = battle.NewMonsterGroupTeam(data.MonsterGroup, uint32(common.RITE_RESTRICT_STATE_RRS_NONE), 0)
		for _, m := range members {
			if m.Num <= 5 {
				continue
			}
			if len(m.PassiveSkills) == 0 {
				continue
			}
			u := defense.GetUniterByPos(m.GetPos())
			if u == nil {
				continue
			}
			if u.GetUniterType() == battle.UniterTypeVoidMember {
				mem, ok := u.(*battle.VoidMember)
				if ok && mem != nil {
					for _, ps := range m.PassiveSkills {
						mem.AddNewSysPassiveSkill(ps)
					}
				}
			} else if u.GetUniterType() == battle.UniterTypeMember {
				mem, ok := u.(*battle.Member)
				if ok && mem != nil {
					for _, ps := range m.PassiveSkills {
						mem.AddNewSysPassiveSkill(ps)
					}
				}
			}
		}
	} else {
		defense = battle.NewTeam(defenseMembers, defenseArtifactInfos, voidMembers, &battle.TeamBase{
			IsCalculateLink: true,
		}, nil)
	}
	altAttrByFunctionID(attack, defense, altAttr, data.FormationID)
	battleParams := battle.NewManagerParams(common.FORMATION_ID(data.FormationID), 0,
		0, 0, altAttr, 0)
	battleParams.SetSeasonID(data.SeasonID)
	if data.MaxRound > 0 {
		battleParams.SetMaxRound(data.MaxRound)
		battleParams.SetNotFinish(true)
	}
	altRaisePS := battle.NewAltRaisePS()
	altRaisePS.AltAttack(attackRaisePSs)
	altRaisePS.AltDefense(defenseRaisePSs)
	battleParams.SetAltRaisePS(altRaisePS)
	return battle.NewManager(attack, defense, battleParams)
}

func getLinks(member *battle.Member, raisePss map[uint32][]uint64) map[uint32]uint32 {
	links := make(map[uint32]uint32)

	heroInfo := goxml.GetData().HeroInfoM.Index(member.GetSysID())
	if heroInfo == nil {
		panic(fmt.Sprintf("HeroInfo cant find heroID:%d", member.GetSysID()))
	}

	// 羁绊1
	if member.GetStar() >= goxml.GetData().ConfigInfoM.Link1UnlockHeroStar && heroInfo.Link1ID > 0 {
		links[heroInfo.Link1ID] += 1
	}
	// 羁绊2
	if member.GetStar() >= goxml.GetData().ConfigInfoM.Link2UnlockHeroStar && heroInfo.Link2ID > 0 {
		links[heroInfo.Link2ID] += 1
	}
	// 羁绊3
	if member.GetStar() >= goxml.GetData().ConfigInfoM.Link3UnlockHeroStar && heroInfo.Link3ID > 0 {
		links[heroInfo.Link3ID] += 1
	}
	// 羁绊4
	if member.GetStar() >= goxml.GetData().ConfigInfoM.Link4UnlockHeroStar && heroInfo.Link4ID > 0 {
		links[heroInfo.Link4ID] += 1
	}
	// 羁绊5
	if member.GetAwakenLevel() > 0 {
		raisePSs := heroInfo.GetAwakenPassiveSkillRaisePSs(member.GetAwakenLevel())
		if len(raisePSs) > 0 {
			raisePss[member.Base.Pos] = append(raisePss[member.Base.Pos], raisePSs...)
		}
		if member.GetAwakenLevel() == goxml.HeroAwakenLinkLevel && heroInfo.Link5ID > 0 {
			links[heroInfo.Link5ID] += 1
		}
	}

	return links
}
