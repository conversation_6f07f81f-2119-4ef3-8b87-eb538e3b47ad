//nolint:mnd
package battle

import (
	"app/goxml"
	"app/protos/out/bt"
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

// TODO uniqID 如果玩家上的也是系统英雄会不会出现重复的情况
// TODO 现在的id反而会出现重复的情况, 同一个位置召唤了一个新的人的话
type Member struct {
	IPoolObj
	mgr  *Manager
	Base *MemberBase

	skillM            *SkillManager
	buffM             *BuffManager
	attrM             *AttrManager
	psAttrM           *PsAttrManager
	hasNoAliveBuff    bool   //死亡的时候是否有禁魂效果
	atSkillKilled     bool   //是否主动技能期间造成击杀
	resurrCount       uint32 //复活次数
	staSkillAttack    int    //攻击状态
	status            int
	id                uint32 //战斗唯一标记, 战斗里面用这个
	maxHp             int64  // 实际最大血量
	showMaxHp         int64  // 显示最大血量
	hp                int64
	bitStatus         uint64            //状态集
	canActionRound    uint32            //可行动的回合
	lastPos           uint32            //上次位置
	swapCount         uint32            //换位次数
	buffTypeLayer     map[uint32]uint32 //统计指定buffType添加层数 buffType=>layer
	templateMonsterId uint32            // 亡灵召唤物对应的monsterID
	callerID          uint32            // 召唤者ID
}

type MemberBase struct {
	UniqID          uint64 //外面的唯一id, 对于玩家就是英雄的唯一id 对于怪物就是系统id, 同一个group里的怪物id不相同
	Pos             uint32
	Index           uint32
	SysID           uint32 //系统id
	Stage           uint32 //突破
	Star            uint32 //星数
	Job             uint32 //职业
	Dress           uint32 //皮肤
	AwakenLevel     uint32 //觉醒等级
	Race            uint32 //种族
	Level           uint32 //等级
	Rare            uint32 // 品质
	AttackType      uint32
	MonsterType     uint32
	OmniLinks       map[uint32]uint32 // 全能英雄自选羁绊 羁绊类型=>羁绊id
	Links           map[uint32]uint32 // 拥有的羁绊点数
	EliteType       uint32
	NeedNotKill     bool                      //是否不需要被击杀，默认false，都是需要被击杀的。只要需要击杀的怪被击杀了那么战斗就胜利，不需要击杀的有没有存活都没关系
	Skills          [goxml.SkillPosMax]uint32 //战斗外面初始化的所有技能
	PassiveSkills   []uint32                  //战斗外面初始化的所有被动技能
	RaisePSs        []uint64                  //战斗外的养成被动技能(或被玩法修正的新增的特效类被动技能 eg: +10%攻击力)
	GemInfo         []*cl.Gem                 //宝石信息
	EmblemID        []uint32                  //穿戴的纹章
	Power           uint64                    //战力
	LogTag          uint32
	IsSeasonAddHero bool
	SkillLvl        uint32
	PublicHp        uint32 // 共用血量组
}

// @param raisePsAttr 养成上套的养成被动，主要是未了为对应raise_passive_skill_info
func NewMember(base *MemberBase, attr []int64) *Member {
	m := newMember(base, attr)
	if m == nil {
		l4g.Errorf("NewMember get hero error %d", base.SysID)
		return nil
	}

	hero := goxml.GetData().HeroInfoM.Index(base.SysID)
	if hero == nil {
		l4g.Errorf("NewMember get hero error %d", base.SysID)
		return nil
	}
	skillLevelInfo := goxml.GetData().SkillLevelInfoM.Index(hero.SkillLevelID, base.Star)
	for i := goxml.SkillPosNormal; i < goxml.SkillPosMax; i++ {
		if i == goxml.SkillPosSeasonAddHero && !base.IsSeasonAddHero {
			continue
		}

		addLevel := base.SkillLvl
		if i == goxml.SkillPosNormal {
			addLevel = 1
		}
		if addLevel == 0 {
			if skillLevelInfo == nil {
				l4g.Errorf("NewMember get skill level info error %d %d %d %d", hero.SkillLevelID, m.Base.SysID, m.Base.Stage, m.Base.Star)
				return nil
			}
			addLevel = skillLevelInfo.SkillLevel[i]
		}
		if !m.AddNewSkillByGroupAndLevel(hero.Skills[i], addLevel, i) {
			return nil
		}
	}
	return m
}

func NewMemberAndNewSkills(uniqID uint64, pos, sysID uint32, skills []uint32, passiveSkills []uint32,
	attr []int64) *Member {
	m := newMember(&MemberBase{
		UniqID: uniqID, Pos: pos, SysID: sysID,
	}, attr)
	if m == nil {
		l4g.Errorf("NewMember get hero error %d", sysID)
		return nil
	}
	for k, v := range skills {
		m.addNewSysSkill(v, k)
	}
	for _, v := range passiveSkills {
		m.AddNewSysPassiveSkill(v)
	}
	return m
}

func newMember(base *MemberBase, attr []int64) *Member {
	m := gPool.GetMember()
	m.fillData(base, attr)
	return m
}

func (m *Member) fillData(base *MemberBase, attr []int64) {
	hero := goxml.GetData().HeroInfoM.Index(base.SysID)
	if hero == nil {
		l4g.Errorf("NewMember get hero error %d", base.SysID)
		return
	}
	m.Base = base
	m.Base.AttackType = hero.AttackType
	m.Base.Job = hero.Job
	m.Base.Race = hero.Race
	m.attrM = NewAttrManager(m)
	m.psAttrM = NewPsAttrManager(m)
	m.skillM = NewSkillManager(m)
	m.buffM = NewBuffManager(m)
	m.attrM.SetBaseAttr(attr)
	m.buffTypeLayer = make(map[uint32]uint32)
}

func (m *Member) Reset() {
	*m = Member{}
}

func (m *Member) ID() uint32 {
	return m.id
}

func (m *Member) EliteType() uint32 {
	if publicHp := m.GetPublicHpGroup(); publicHp != nil {
		return publicHp.EliteType()
	}
	return m.Base.EliteType
}

func (m *Member) Index() uint32 {
	return m.Base.Index
}

func (m *Member) Pos() uint32 {
	return m.Base.Pos
}
func (m *Member) GetPublicHpGroup() *PublicHpGroup {
	if m.Base.PublicHp == 0 {
		return nil
	}
	return m.mgr.GetTeam(m).GetPublicHpGroup(m.Base.PublicHp)
}

func (m *Member) GetHp() int64 {
	if publicHp := m.GetPublicHpGroup(); publicHp != nil {
		return publicHp.GetHp()
	}
	return m.GetSelfHp()
}

func (m *Member) SetHp(hp int64) {
	m.hp = hp
	if hp == 0 {
		m.SetStatus(UniterDead)
	}
}

func (m *Member) GetMaxHp() int64 {
	if publicHp := m.GetPublicHpGroup(); publicHp != nil {
		return publicHp.GetMaxHp()
	}
	return m.GetSelfMaxHp()
}

func (m *Member) GetSelfHp() int64 {
	return m.hp
}

func (m *Member) GetSelfMaxHp() int64 {
	return m.maxHp
}

func (m *Member) GetShowMaxHp() int64 {
	return m.showMaxHp
}

// 获取最大血量，不受固定百分比影响(属性47, 60)
func (m *Member) GetMaxHpWithoutFixPct() int64 {
	maxHpPct := m.attrM.GetAttr(goxml.AttrFixMaxHpPct)
	maxHpPct2 := m.attrM.GetAttr(goxml.AttrFixMaxHpPct2)

	return int64(float64(m.maxHp) / (float64(maxHpPct) * float64(maxHpPct2)) * BaseFloat * BaseFloat)
}

func (m *Member) GetManager() *Manager {
	return m.mgr
}

func (m *Member) GetAttrManager() *AttrManager {
	return m.attrM
}

func (m *Member) GetAttrM() IAttrM {
	return m.attrM
}
func (m *Member) GetBuffM() *BuffManager {
	return m.buffM
}
func (m *Member) GetPsAttrM() *PsAttrManager {
	return m.psAttrM
}

func (m *Member) GetSkillM() ISkillManager {
	return m.skillM
}

func (m *Member) GetJob() uint32 {
	return m.Base.Job
}

func (m *Member) GetRace() uint32 {
	return m.Base.Race
}

// 返回是否需要被击杀
func (m *Member) NeedKill() bool {
	return !m.Base.NeedNotKill
}

func (m *Member) SetNeedNotKill() {
	m.Base.NeedNotKill = true
}

func (m *Member) IsNoAlive() bool {
	return m.hasNoAliveBuff
}

func (m *Member) AddNewSkill(id uint32, pos int, args interface{}) *Skill {
	return m.skillM.AddNewSkill(id, pos, args, false, false)
}

func (m *Member) AddPsSkill(registerPsSkill uint32) *PsSkill {
	return m.skillM.AddPsSkill(registerPsSkill)
}

/*
受到伤害
attack 攻击方，如果是buff，就是buff的施放者
value 伤害的原始值
*/
//nolint:funlen
func (m *Member) Hurted(attack Uniter, value int64, hurtType bt.SpecialType, factor bt.FactorType, iexe IExecution) bool {
	if m.GetStatus() == UniterDead {
		return true
	}

	// s5龙战机制: 根据攻击类型和仅受xx伤害buff，来检查免伤
	if m.checkNoHurtByOnlyHurtBuff(attack, hurtType, factor, iexe) {
		return false
	}

	if m.HasBuff(BuffTypeAccumulateHurt) && hurtType != bt.SpecialType_HurtTypeDevour &&
		hurtType != bt.SpecialType_HurtTypeKillNotDead && hurtType != bt.SpecialType_HurtTypeKillIgnoreLockBlood {
		m.mgr.GetTeam(m).AddAccumulateHurt(uint64(value), attack, m, iexe)
		return true
	}

	manger := m.GetManager()
	res := gPool.GetHurtResult()
	defer gPool.FreeHurtResult(res)
	res.defense = m
	res.Factor = factor
	res.HurtType = hurtType
	if iexe != nil {
		defer iexe.UpdateHurtResult(res)
		if iexe.GetExecutionType() == bt.SourceType_SOURCE_ACTIVE && hurtType != bt.SpecialType_HurtTypeTransfer {
			m.checkBeStuneRate(attack)
		}
	}

	if m.checkNoHurt(attack, hurtType, factor, iexe) {
		return false
	}

	// 根据伤害类型和无敌buff329来判断是否免伤
	if m.checkNoHurtByHurtType(attack, hurtType, factor, iexe) {
		return false
	}

	if hurtType != bt.SpecialType_HurtTypeHurtSelf && hurtType != bt.SpecialType_HurtTypeTransfer &&
		hurtType != bt.SpecialType_HurtTypeFull && hurtType != bt.SpecialType_HurtTypeDoKill &&
		hurtType != bt.SpecialType_HurtTypeDevour && hurtType != bt.SpecialType_HurtTypeKillNotDead &&
		hurtType != bt.SpecialType_HurtTypeKillIgnoreLockBlood && hurtType != bt.SpecialType_HurtTypeHurtSelfDamageToTrigger {
		m.beforeHurtFix(attack, factor, iexe)
		//伤害修正
		value, _ = manger.CalFormula(attack, m, 1, value, int64(factor), 0, nil)
	}

	if param := m.GetPSEffectValue(PsAttrHurtReduceByDivided, 0); param > 0 {
		value = int64(float64(value) * (BaseFloat / float64(param)))
		if df() {
			gDebugFormulaStrs = append(gDebugFormulaStrs,
				fmt.Sprintf("特殊属性%d:%d  结果(%d)=伤害*10000/特殊属性值", PsAttrHurtReduceByDivided, param, value))
			GenDebugFormula(0, attack.ID(), m.ID(), param, 0, 0, value, m.mgr)
		}
	}

	if m.hurt2Cure(factor, value, iexe) {
		clHurt := m.mgr.report.NewHurt(0, factor, hurtType, attack, m, iexe)
		clHurt.Hp = m.GetHp()
		return true
	}

	res.InitValue = value
	clHurt := m.mgr.report.NewHurt(value, factor, hurtType, attack, m, iexe)
	res.InitAltValue = value
	if value != 0 && hurtType != bt.SpecialType_HurtTypeTransfer &&
		hurtType != bt.SpecialType_HurtTypeDoKill &&
		hurtType != bt.SpecialType_HurtTypeDevour &&
		hurtType != bt.SpecialType_HurtTypeKillNotDead &&
		hurtType != bt.SpecialType_HurtTypeKillIgnoreLockBlood {
		if m.HurtFixByGuard(attack, &value, hurtType) {
			res.Dead = true
		}
	}
	if hurtType == bt.SpecialType_HurtTypeHurtSelf ||
		hurtType == bt.SpecialType_HurtTypeDoKill ||
		hurtType == bt.SpecialType_HurtTypeDevour ||
		hurtType == bt.SpecialType_HurtTypeKillNotDead ||
		hurtType == bt.SpecialType_HurtTypeKillIgnoreLockBlood ||
		hurtType == bt.SpecialType_HurtTypeHurtSelfDamageToTrigger {
		//自残和斩杀不计算护盾类
		// res.InitAltValue = value
	} else {
		if value != 0 && m.UseInvincibleShield(iexe, attack) {
			if res.InitAltValue >= value {
				res.InitAltValue -= value
			}
			value = 0
		}

		// 更新计数buff层数
		m.buffM.UpdateCountLayerBuff(iexe, attack)

		if value != 0 {
			if attack.GetUniterType() == UniterTypeTeamUniter || !attack.HasSta(StaIgnoreShield) {
				hurtShieldRate := 1 + float64(attack.GetAttr(goxml.AttrShieldDamAddRate)+attack.GetAttr(goxml.AttrSeasonShieldDamAddRate))/BaseFloat
				needShieldValue := int64(hurtShieldRate * float64(value))

				//护盾
				_, shield := m.UseShield(needShieldValue, iexe, attack)
				clHurt.Shield = uint64(shield)

				value -= int64(float64(shield) / hurtShieldRate)
			}
		}
		m.HurtFixByHurtedMax(&value, attack)
	}
	if value != 0 && hurtType != bt.SpecialType_HurtTypeTransfer &&
		hurtType != bt.SpecialType_HurtTypeDoKill &&
		hurtType != bt.SpecialType_HurtTypeDevour &&
		hurtType != bt.SpecialType_HurtTypeKillNotDead &&
		hurtType != bt.SpecialType_HurtTypeKillIgnoreLockBlood {
		if m.HurtFixByShareDamage(attack, &value, hurtType, iexe) {
			res.Dead = true
		}
	}

	clHurt.Hurt = uint64(value)
	showValue := value
	res.ShowValue = showValue
	if m.GetHp() > 0 {
		if hurtType != bt.SpecialType_HurtTypeKillNotDead &&
			hurtType != bt.SpecialType_HurtTypeKillIgnoreLockBlood {
			m.HurtFixByLockBlood(&value)
		}
		realValue, overflow := m.DecHp(value)
		clHurt.RealHurt = uint64(realValue)
		res.Overflow = overflow
		res.RealValue = realValue
		if publicHp := m.GetPublicHpGroup(); publicHp != nil {
			if publicHp.CheckDead(attack, hurtType, clHurt) {
				res.Dead = true
			}
		} else {
			if m.CheckDead(attack, hurtType, clHurt) {
				res.Dead = true
			}
		}
	}
	clHurt.Hp = m.GetHp()

	if m.Index() != attack.Index() {
		// 吞噬、积攒伤害分摊、effect60斩杀，不计入伤害统计
		if hurtType != bt.SpecialType_HurtTypeDevour &&
			hurtType != bt.SpecialType_HurtTypeBuffShare &&
			hurtType != bt.SpecialType_HurtTypeKillNotDead &&
			hurtType != bt.SpecialType_HurtTypeKillIgnoreLockBlood {
			m.mgr.AddResultRoundHurt(attack, m, res.RealValue, res.ShowValue, iexe, hurtType)
		}
		attack.UpdateAttackResult(res)
	}

	if hurtType == bt.SpecialType_HurtTypeBuffShare {
		m.mgr.GetTeam(m).DecAccumulateHurt(uint64(value), attack, m, iexe)
	}

	if res.InitValue > 0 && hurtType != bt.SpecialType_HurtTypeTransfer {
		manger.TriggerPsSkill(attack, goxml.Event_DoHurt, res)
	}
	return res.Dead
}

func (m *Member) beforeHurtFix(attack Uniter, factor bt.FactorType, iexe IExecution) {
	if iexe == nil {
		return
	}
	if iexe.GetExecutionType() != bt.SourceType_SOURCE_ACTIVE {
		return
	}

	oneSkillAttack, ok := iexe.(*OneSkillAttack)
	if !ok {
		l4g.Errorf("beforeHurtFix: iexe not OneSkillAttack")
		return
	}

	args := NewArgsBeforeHurtValueFix(factor == bt.FactorType_FactorCrit, oneSkillAttack)
	defer gPool.FreeArgsBeforeHurtValueFix(args)
	m.GetManager().TriggerPsSkill(m, goxml.Event_ActiveHurtedBeforeHurtValueFix, args)
}

func (m *Member) checkBeStuneRate(attack Uniter) {
	rate := attack.GetPSEffectValue(PsAttrStuneRate, 0)
	if rate == 0 {
		return
	}
	if m.mgr.CheckRate(rate) {
		sysBuff := goxml.GetData().BuffInfoM.Index(20100)
		if sysBuff == nil {
			panic(fmt.Sprintf("battle no find buff:%d", 20100))
		}
		sysBuffType := goxml.GetData().BuffTypeInfoM.Index(sysBuff.Type)
		if sysBuffType == nil {
			panic(fmt.Sprintf("battle no find buff type :%d %d", 20100, sysBuff.Type))
		}

		ret := m.GetBuffM().checkAddBuffImmune(sysBuff, sysBuffType, nil, attack)
		if ret == AddBuffRetMianyi || ret == AddBuffRetDikang {
			m.GetBuffM().owner.GetManager().report.NewAddBuffRet(ret, sysBuff.Id, attack, m, nil)
			return
		}
		if ret != AddBuffRetSuccess {
			return
		}

		m.GetBuffM().successAddBuff(attack, m, nil, sysBuff, sysBuffType, 0)
	}
}

// 根据攻击类型和仅受xx伤害buff，来检查免伤
// @return bool 是否免伤
func (m *Member) checkNoHurtByOnlyHurtBuff(attack Uniter, hurtType bt.SpecialType, factor bt.FactorType, iexe IExecution) bool {
	isNohurt := true
	defer func() {
		if isNohurt {
			clHurt := m.mgr.report.NewHurt(0, factor, hurtType, attack, m, iexe)
			clHurt.Hp = m.GetHp()
		}
	}()

	// 4中buff, 单个member不会同时拥有多个
	if m.HasBuff(BuffTypeOnlyPhysicalHurt) {
		if attack.GetUniterType() == UniterTypeMember && attack.IsPhysical() {
			isNohurt = false
		}
	} else if m.HasBuff(BuffTypeOnlyMagicHurt) {
		if attack.GetUniterType() == UniterTypeMember && !attack.IsPhysical() {
			isNohurt = false
		}
	} else if m.HasBuff(BuffTypeOnlyArtifactSingleHurt) {
		if sa, ok := iexe.(*OneSkillAttack); ok {
			if attack.GetUniterType() == UniterTypeArtifact && goxml.IsSingleTargetAttack(sa.GetNowSkill().info.Type2) {
				isNohurt = false
			}
		}
	} else if m.HasBuff(BuffTypeOnlyArtifactGroupHurt) {
		if sa, ok := iexe.(*OneSkillAttack); ok {
			if attack.GetUniterType() == UniterTypeArtifact && !goxml.IsSingleTargetAttack(sa.GetNowSkill().info.Type2) {
				isNohurt = false
			}
		}
	} else if m.HasBuff(BuffTypeOnlyOnePosAndOneSex) {
		if attack.GetUniterType() == UniterTypeMember {
			attackMem, ok := attack.(*Member)
			if ok {
				heroId := attackMem.GetHeroSysID()
				info := goxml.GetData().HeroInfoM.Index(heroId)
				buff := m.GetBuffM().GetOneBuff(BuffTypeOnlyOnePosAndOneSex)
				if buff != nil && info != nil {
					if attackMem.Pos() == buff.GetBuffInfo().BuffEffect1 && info.Sex == buff.GetBuffInfo().BuffEffect2 {
						isNohurt = false
					}
				}
			}
		}
	} else {
		isNohurt = false
	}
	return isNohurt
}

// 判断这次伤害是否无效
func (m *Member) checkNoHurt(attack Uniter, hurtType bt.SpecialType, factor bt.FactorType, iexe IExecution) bool {
	isNohurt := false
	defer func() {
		if isNohurt {
			clHurt := m.mgr.report.NewHurt(0, factor, hurtType, attack, m, iexe)
			clHurt.Hp = m.GetHp()
		}
	}()
	if factor == bt.FactorType_FactorDodge {
		isNohurt = true
		return isNohurt
	}
	if factor == bt.FactorType_FactorDodge2 {
		isNohurt = true
		return isNohurt
	}
	if m.HasBuff(BuffTypeLimitSelfHurt) && attack.ID() != m.ID() {
		isNohurt = true
		return isNohurt
	}
	if sa, ok := iexe.(*OneSkillAttack); ok {
		buff := m.buffM.GetOneBuff(BuffTypeNoHurtedBySkillType)
		if buff != nil {
			if uint32(buff.info.Eff1Param1) != sa.GetNowSkill().info.Type {
				isNohurt = true
				return isNohurt
			}
		}
	}
	if m.HasSta(StaBlessNoHurted) {
		notConsumeRate := m.GetPSEffectValue(PsAttrNotConsume, 0)
		if !m.mgr.CheckRate(notConsumeRate) {
			m.buffM.removeBuffByType(BuffTypeBlessNoHurted, iexe, attack)
		}
		isNohurt = true
		return isNohurt
	}

	if m.HasSta(StaNoHurted) {
		m.buffM.removeBuffByType(BuffTypeNoHurted, iexe, attack)
		isNohurt = true
		return isNohurt
	}
	return isNohurt
}

// 根据伤害类型和无敌buff329来判断是否免伤
// 仅能够被伤害类型16,17(来自effect60)斩杀和自己伤害
func (m *Member) checkNoHurtByHurtType(attack Uniter, hurtType bt.SpecialType, factor bt.FactorType, iexe IExecution) bool {
	isNohurt := false
	defer func() {
		if isNohurt {
			clHurt := m.mgr.report.NewHurt(0, factor, hurtType, attack, m, iexe)
			clHurt.Hp = m.GetHp()
		}
	}()
	if m.HasBuff(BuffTypeLimitSelfHurt2) && attack.ID() != m.ID() {
		if hurtType != bt.SpecialType_HurtTypeKillNotDead && hurtType != bt.SpecialType_HurtTypeKillIgnoreLockBlood {
			isNohurt = true
		}
	}
	if m.HasBuff(BuffTypeOnlyHurtBySelfOrCallDepartedSpirit) {
		if hurtType != bt.SpecialType_HurtTypeHurtSelfDamageToTrigger && attack.GetMonsterType() != MonsterTypeCallDepartedSpirit {
			isNohurt = true
		}
	}
	return isNohurt
}

func (m *Member) HurtFixByGuard(attack Uniter, hurt *int64, hurtType bt.SpecialType) bool {
	guardDead := false
	bucket := getBuffBucket(BuffTypeGuard)
	for _, buff := range m.GetBuffM().buffs[bucket] {
		if CheckBuffInUse(buff) && buff.IsActive() && buff.Type() == BuffTypeGuard &&
			buff.attack.GetStatus() == UniterAlive && buff.attack.ID() != m.ID() {
			if buff.attack.HasBuff(BuffTypeHide) || buff.attack.HasBuff(BuffTypeExile) {
				continue
			}

			guardP := m.GetManager().SimpleZeroFormula(attack, buff.attack, buff.info.Eff1Formula, buff.info.Eff1Param1, buff.info.Eff1Param2, 0, nil)
			guardV := int64(float64(*hurt) * (float64(guardP) / BaseFloat))
			if buff.attack.Hurted(attack, guardV, bt.SpecialType_HurtTypeTransfer, 0, nil) {
				guardDead = true
			}
			*hurt -= guardV
		}
	}
	return guardDead
}

// 如果有锁血属性,那么就需要修正伤害
func (m *Member) HurtFixByLockBlood(hurt *int64) {
	lockBlood := m.GetAttr(goxml.AttrLockBlood)
	if lockBlood != 0 {
		lockV := int64(float64(m.GetMaxHp()) * (float64(lockBlood) / BaseFloat))
		if m.GetHp() > lockV {
			if m.GetHp()-*hurt < lockV {
				*hurt = m.GetHp() - lockV
			}
		} else {
			*hurt = 0
		}
	}
}

func (m *Member) HurtFixByShareDamage(attack Uniter, hurt *int64, hurtType bt.SpecialType, iexe IExecution) bool {
	shareDead := false
	shareMember := make(map[uint32]Uniter)
	bucket := getBuffBucket(BuffTypeShareDamageByHp)
	for _, buff := range m.GetBuffM().buffs[bucket] {
		if CheckBuffInUse(buff) && buff.IsActive() && buff.Type() == BuffTypeShareDamageByHp {
			for _, uniter := range m.mgr.GetTeam(m).GetShareDamageUniters(buff) {
				if uniter.GetStatus() == UniterDead || uniter.HasBuff(BuffTypeHide) || uniter.HasBuff(BuffTypeExile) {
					continue
				}
				shareMember[uniter.ID()] = uniter
			}
		}
	}
	if len(shareMember) == 0 {
		return false
	}
	var totalHp float64
	for _, uniter := range shareMember {
		totalHp += float64(uniter.GetHp())
	}
	hurtF := float64(*hurt)
	//超过伤害了就不分摊
	if hurtF >= totalHp {
		return false
	}
	for id, uniter := range shareMember {
		if id == m.ID() {
			continue
		}
		hurtV := int64(float64(uniter.GetHp()) / totalHp * hurtF)
		if uniter.Hurted(attack, hurtV, bt.SpecialType_HurtTypeTransfer, 0, iexe) {
			shareDead = true
		}
	}
	*hurt = int64(float64(m.GetHp()) / totalHp * hurtF)
	return shareDead
}

/*
受到治疗
attack 攻击方，如果是buff，就是buff的施放者
value 伤害的原始值
*/
func (m *Member) Cured(attack Uniter, value int64, cureType bt.SpecialType, factor bt.FactorType, iexe IExecution) {
	value, _ = m.GetManager().CalFormula(attack, m, 3, value, int64(cureType), int64(factor), nil)
	res := gPool.GetCureResult()
	res.Factor = factor
	res.InitValue = value
	defer gPool.FreeCureResult(res)
	if iexe != nil {
		defer iexe.UpdateCureResult(res)
	}
	if m.Cure2Hurt(value) {
		return
	}
	if cureType != bt.SpecialType_CureTypeSteal {
		value = m.BeStealCure(value, factor, iexe)
		if value == 0 {
			//不用处理复活的情况,死亡这类buff会被清掉
			return
		}
	}

	cure := m.mgr.report.NewCure(value, factor, cureType, attack, m, iexe)
	res.ShowValue = value
	realValue, overflow := m.AddHp(value)
	res.Overflow = overflow
	res.RealValue = realValue
	cure.Hp = m.GetHp()

	if m.Index() == attack.Index() {
		m.mgr.report.AddBattleSta(attack, BattleStaCure, realValue, 0)
	}

	attack.GetManager().TriggerPsSkill(m, goxml.Event_BeCured, res)
	if cureType == bt.SpecialType_CureTypeSuck {
		attack.GetManager().TriggerPsSkill(m, goxml.Event_BloodSuck, res)
	}
	if res.Overflow != 0 {
		attack.GetManager().TriggerPsSkill(m, goxml.Event_OverCure, res)
	}
}

// 偷治疗
// @param int64 value 原治疗数值
// @return int64 被偷后剩余的数值
func (m *Member) BeStealCure(value int64, factor bt.FactorType, iexe IExecution) int64 {
	stealCure := m.GetBuffM().GetOneBuff(BuffTypeStealCure)
	if stealCure != nil && m.ID() != stealCure.attack.ID() {
		stealValue := value
		if stealCure.info.Eff1Param1 > 0 {
			if stealCure.info.Eff1Param1 > BaseFloatInt {
				panic(fmt.Sprintf("BeStealCure, param error %d", stealCure.info.Eff1Param1))
			}
			stealValue = int64(float64(value) * float64(stealCure.info.Eff1Param1) / BaseFloat)
		}

		stealCure.attack.Cured(m, stealValue, bt.SpecialType_CureTypeSteal, factor, stealCure)
		return value - stealValue
	}
	return value
}

func (m *Member) hurt2Cure(factor bt.FactorType, hurtV int64, iexe IExecution) bool {
	if iexe == nil {
		return false
	}
	if iexe.GetExecutionType() != bt.SourceType_SOURCE_ACTIVE {
		return false
	}
	if factor == bt.FactorType_FactorCrit {
		return false
	}
	hurt2Cure := m.GetBuffM().GetOneBuff(BuffTypeSkillNoCritHurt2Cure)
	if hurt2Cure != nil {
		cureV, _ := m.mgr.CalFormula(hurt2Cure.attack, hurt2Cure.owner, hurt2Cure.info.Eff1Formula, hurtV, hurt2Cure.info.Eff1Param2, hurt2Cure.info.Eff1Param3, hurt2Cure)
		hurt2Cure.owner.Cured(hurt2Cure.attack, cureV, bt.SpecialType_SpecialTypeNone, bt.FactorType_FactorNone, hurt2Cure)
		return true
	}
	return false
}

func (m *Member) Cure2Hurt(cureV int64) bool {
	cure2hurt := m.GetBuffM().GetOneBuff(BuffTypeCure2Hurt)
	if cure2hurt != nil {
		hurtV, _ := m.mgr.CalFormula(cure2hurt.attack, cure2hurt.owner, cure2hurt.info.Eff1Formula, cureV, cure2hurt.info.Eff1Param2, cure2hurt.info.Eff1Param3, cure2hurt)
		cure2hurt.owner.Hurted(cure2hurt.attack, hurtV, bt.SpecialType_HurtTypeFull, bt.FactorType_FactorNone, cure2hurt)
		return true
	}
	return false
}
func (m *Member) SetID(id uint32) {
	m.id = id
}

func (m *Member) SetSysID(id uint32) {
	m.Base.SysID = id
}

func (m *Member) GetSysID() uint32 {
	return m.Base.SysID
}

func (m *Member) SetElite(eliteType uint32) {
	m.Base.EliteType = eliteType
}

func (m *Member) SetIndex(index uint32) {
	m.Base.Index = index
}

func (m *Member) SetPos(pos uint32) {
	m.Base.Pos = pos
}

func (m *Member) SetStage(stage uint32) {
	m.Base.Stage = stage
}
func (m *Member) GetStage() uint32 {
	return m.Base.Stage
}

func (m *Member) SetStar(star uint32) {
	m.Base.Star = star
}

func (m *Member) SetDress(dress uint32) {
	m.Base.Dress = dress
}
func (m *Member) GetDress() uint32 {
	return m.Base.Dress
}

func (m *Member) SetAwakenLevel(awakenLevel uint32) {
	m.Base.AwakenLevel = awakenLevel
}
func (m *Member) GetAwakenLevel() uint32 {
	return m.Base.AwakenLevel
}

func (m *Member) GetStar() uint32 {
	return m.Base.Star
}

func (m *Member) SetLevel(level uint32) {
	m.Base.Level = level
}
func (m *Member) GetLevel() uint32 {
	return m.Base.Level
}

func (m *Member) SetMonsterType(t uint32) {
	m.Base.MonsterType = t
}

func (m *Member) GetMonsterType() uint32 {
	return m.Base.MonsterType
}

// 是否是召唤物
func (m *Member) IsCall() bool {
	return m.GetMonsterType() == MonsterTypeCall ||
		m.GetMonsterType() == MonsterTypeCallResurrection ||
		m.GetMonsterType() == MonsterTypeCallHero || m.GetMonsterType() == MonsterTypeCallDepartedSpirit
}

func (m *Member) IsPublicHP() bool {
	return m.Base.PublicHp > 0
}

func (m *Member) SetPublicHp(publicHp uint32) {
	m.Base.PublicHp = publicHp
}

func (m *Member) SetManager(mgr *Manager) {
	m.mgr = mgr
	m.buffM.SetManager(mgr)
}

func (m *Member) SetRaisePsAttr(raisePSs []uint64) {
	m.Base.RaisePSs = raisePSs
}

func (m *Member) SetOmniLinks(links map[uint32]uint32) {
	m.Base.OmniLinks = links
}

func (m *Member) SetLinks(links map[uint32]uint32) {
	m.Base.Links = links
}

func (m *Member) GetLinks() map[uint32]uint32 {
	return m.Base.Links
}

func (m *Member) GetOmniLinkByType(linkType uint32) uint32 {
	return m.Base.OmniLinks[linkType]
}
func (m *Member) initBase(t *Team, isCall bool) {
	m.SetManager(t.m)
	m.SetIndex(t.index)
	m.SetID(t.NewID(m.Base.Pos, isCall))
}

func (m *Member) initAttr(altAttr map[uint32]map[int]int64, altHp map[uint64]int64,
	altPosHp map[uint32]int64, fixMaxHP bool) {
	if altAttr != nil {
		for k, v := range altAttr[0] {
			m.attrM.baseAttr[k] += v
		}
		for k, v := range altAttr[m.Base.Pos] {
			m.attrM.baseAttr[k] += v
		}
	}
	m.attrM.InitBaseAttr()

	m.maxHp = m.GetAttr(goxml.AttrHp)
	m.showMaxHp = m.maxHp
	m.hp = m.maxHp
	if altHp != nil {
		if altHpPct, exist := altHp[m.Base.UniqID]; exist {
			if altHpPct < BaseFloatInt {
				m.hp = int64(float64(m.maxHp) * (float64(altHpPct) / BaseFloat))
			}
		}
	}

	if altPosHp != nil {
		if altHpPct, exist := altPosHp[m.Base.Pos]; exist {
			if altHpPct < BaseFloatInt {
				m.hp = int64(float64(m.maxHp) * (float64(altHpPct) / BaseFloat))
			}
		}
	}

	if fixMaxHP {
		m.maxHp = m.hp
	}

	if m.hp <= 0 {
		m.SetStatus(UniterDead)
	}
}

func (m *Member) initRaisePs(altRaisePS map[uint32][]uint64) {
	if altRaisePS != nil {
		m.Base.RaisePSs = append(m.Base.RaisePSs, altRaisePS[0]...)
		m.Base.RaisePSs = append(m.Base.RaisePSs, altRaisePS[m.Base.Pos]...)
	}
	m.calRaisePsAttr()
}

func (m *Member) initSkills(needRoundFix bool) {
	for pos, id := range m.Base.Skills {
		if id != 0 {
			m.skillM.AddNewSkill(id, pos, nil, needRoundFix, false)
		}
	}
	for _, id := range m.Base.PassiveSkills {
		m.skillM.AddPsSkill(id)
	}
}

func SettleBuff(owner Uniter, stage uint32, iexe IExecution) {
	for _, buffs := range owner.GetBuffM().buffs {
		for _, buff := range buffs {
			if !CheckBuffInUse(buff) {
				continue
			}
			if buff.CheckStage(stage) {
				if buff.NeedSettle(iexe) {
					owner.GetManager().SettleBuff(buff, iexe)
					if owner.GetStatus() != UniterDead {
						owner.GetManager().TriggerPsSkill(owner, goxml.Event_BuffSettle, buff)
					}
					if owner.GetStatus() == UniterDead {
						return
					}
					if buff.IsReset() {
						//这个buff可能整个结算行为中被清除了
						continue
					}
				}
				if buff.UseRound() {
					owner.GetBuffM().removeBuff(buff, iexe, buff.attack)
				}
			}
		}
	}
}

/*
@return int64 实际扣除的血量
@return int64 溢出的伤害
*/
func (m *Member) DecHp(value int64) (int64, int64) {
	if publicHp := m.GetPublicHpGroup(); publicHp != nil {
		return publicHp.DecHp(value)
	}
	overflow := int64(0)
	if m.hp >= value {
		m.hp -= value
	} else {
		overflow = value - m.hp
		value = m.hp
		m.hp = 0
	}
	m.AfterDecHp(value)
	return value, overflow
}

func (m *Member) AfterDecHp(value int64) {
	m.attrM.ChangeHpAttr()
	if value > 0 {
		args := NewArgsDecHp(value)
		defer gPool.FreeArgsDecHp(args)
		m.mgr.TriggerPsSkill(m, goxml.Event_DecHp, args)
	}
}

/*
@return bool 是否死亡
*/
func (m *Member) CheckDead(attack Uniter, hurtType bt.SpecialType, hurtEffect *bt.HurtEffect) bool {
	if m.hp == 0 && (hurtType == bt.SpecialType_HurtTypeDoKill ||
		hurtType == bt.SpecialType_HurtTypeDevour || hurtType == bt.SpecialType_HurtTypeKillNotDead ||
		hurtType == bt.SpecialType_HurtTypeKillIgnoreLockBlood) {
		if m.IsNoDead3() { // 不屈类型3，在斩杀等伤害下，也可以不死
			m.hp = 1
			return false
		}
		m.Dead(hurtEffect)
		return true
	}
	if m.hp == 0 {
		m.mgr.TriggerPsSkill(m, goxml.Event_DeadHurt, attack)
		if m.hp != 0 {
			return false
		}
		if m.IsNoDead() {
			if m.hp == 0 {
				m.hp = 1 //只有血量为0，才修正，血量有可能恢复了
			}
			return false
		}
		if m.hp == 0 {
			m.Dead(hurtEffect)
			return true
		}
	}
	return false
}

// 死亡
func (m *Member) Dead(hurtEffect *bt.HurtEffect) {
	if m.GetStatus() == UniterDead {
		return
	}
	m.SetStatus(UniterDead)
	deadInfo := gPool.GetBattleMemberDeadInfo()
	hurtEffect.DeadInfo = append(hurtEffect.DeadInfo, deadInfo)
	deadInfo.Id = m.ID()
	if m.HasBuff(BuffTypeNoAlive) {
		m.hasNoAliveBuff = true
	}
	m.AddOpEnergy()
	if m.mgr.NeedMoreResult() {
		team := m.mgr.GetTeam(m)
		if team.IsDefenseTeam() {
			m.mgr.report.AddResultKillCount()
		}
	}
	m.ClearBuffOnDead()
	m.hp = 0

	var addKillNum bool
	if hurtEffect.HurtType != bt.SpecialType_HurtTypeKillNotDead {
		addKillNum = true
	}

	m.mgr.Dead(m, addKillNum, true)
	if hurtEffect.HurtType != bt.SpecialType_HurtTypeKillNotDead {
		fixedCount := 1 + int(m.GetPSEffectValue(PsAttrDeadEventAddTriggerCount, 0))
		if m.IsCall() {
			fixedCount += int(m.GetPSEffectValue(PsAttrCallDeadEventAddTriggerCount, 0))
		}
		for i := 0; i < fixedCount; i++ {
			m.mgr.TriggerAllPsSkillSpeedFirst(goxml.Event_Dead, m)
		}
	}

	if m.GetStatus() == UniterDead &&
		(m.GetMonsterType() == MonsterTypeCall || m.GetMonsterType() == MonsterTypeCallHero ||
			m.GetMonsterType() == MonsterTypeCallDepartedSpirit) {
		m.mgr.GetTeam(m).OnCallDead(m.ID())
	}
}

func (m *Member) AddOpEnergy() uint32 {
	var value uint32
	opTeam := m.mgr.GetOpTeam(m)
	if opTeam.HasArtifact() {
		addEnergy, _ := m.GetManager().CalFormula(opTeam.artifactM, nil, ActifactEnergyKill, 0, 0, 0, nil)
		value = uint32(addEnergy)
		opTeam.AddArtifactEnergy(int(addEnergy), m, opTeam.artifactM)
	}
	return value
}

/*
@return int64 实际加血
@return int64 过量治疗
*/
func (m *Member) AddHp(value int64) (int64, int64) {
	if publicHp := m.GetPublicHpGroup(); publicHp != nil {
		return publicHp.AddHp(value)
	}
	m.hp += value
	var over int64
	if m.hp > m.maxHp {
		over = m.hp - m.maxHp
		value = value - over
		overRate := m.GetPSEffectValue(PsAttrOverCureRate, BaseFloatInt)
		if overRate > BaseFloatInt {
			over = int64(float64(over) / BaseFloat * float64(overRate))
		}
		m.hp = m.maxHp
	}
	m.attrM.ChangeHpAttr()
	return value, over
}

func (m *Member) ActionPrepare(a *OneAction) bool {
	//提前获取要使用的技能
	return m.ChooseNowSkill(a)
}

func (m *Member) OneSkillPrepare(s *OneSkill) {
	if m.psAttrM.ChangePsAttrTimeNextSkill() {
		s.AddPsAttrChange(m)
	}
}

func (m *Member) OneActionEnd(a *OneAction) {
	team := m.mgr.GetTeam(m)
	if team.HasArtifact() {
		addEnergy, _ := m.GetManager().CalFormula(team.artifactM, nil, ActifactEnergyAction,
			0, 0, 0, nil)
		team.AddArtifactEnergy(int(addEnergy), m, team.artifactM)
	}
}

func (m *Member) AddActionRound(round uint32) {
	m.canActionRound = m.mgr.GetRound() + round
}

// 是否可以行动
func (m *Member) IsStuned() (bool, IExecution) {
	if m.canActionRound > m.mgr.GetRound() {
		return true, nil
	}
	buff := m.GetBuffM().GetOneBuff(BuffTypeReset)
	if buff != nil {
		m.AddActionRound(1)
		return true, buff
	}
	buff = m.GetBuffM().GetOneBuff(BuffTypeStun)
	if buff != nil {
		m.AddActionRound(1)
		return true, buff
	}
	buff = m.GetBuffM().GetOneBuff(BuffTypeStun2)
	if buff != nil {
		m.AddActionRound(1)
		return true, buff
	}
	buff = m.GetBuffM().GetOneBuff(BuffTypeShudder)
	if buff != nil {
		m.AddActionRound(1)
		return true, buff
	}
	buff = m.GetBuffM().GetOneBuff(BuffTypeJess)
	if buff != nil {
		m.AddActionRound(1)
		return true, buff
	}
	buff = m.GetBuffM().GetOneBuff(BuffTypeEntang)
	if buff != nil {
		m.AddActionRound(1)
		return true, buff
	}
	buff = m.GetBuffM().GetOneBuff(BuffTypeMabi)
	if buff != nil && m.mgr.CheckRate(BuffMabiRate) {
		m.AddActionRound(1)
		return true, buff
	}
	return false, nil
}

func (m *Member) CanBeChoose(needAlive int) bool {
	if m.HasSta(StaHide) || m.HasSta(StaExile) {
		return false
	}
	if needAlive == TargetNeedAlive && !(m.GetStatus() == UniterAlive) {
		return false
	}
	if needAlive == TargetNeedDead && !(m.GetStatus() == UniterDead) {
		return false
	}
	return true
}

func (m *Member) CanBeChooseIncludeExile(needAlive int) bool {
	if m.HasSta(StaHide) {
		return false
	}
	if needAlive == TargetNeedAlive && !(m.GetStatus() == UniterAlive) {
		return false
	}
	if needAlive == TargetNeedDead && !(m.GetStatus() == UniterDead) {
		return false
	}
	return true
}

func (m *Member) IsNoDead() bool {
	if m.HasBuff(BuffTypeNoDead) || m.HasBuff(BuffTypeNoDead2) {
		return true
	}
	if m.IsNoDead3() {
		return true
	}
	return false
}

// 是否满足不屈类型3的不死情况
func (m *Member) IsNoDead3() bool {
	if m.HasBuff(BuffTypeNoDead3) {
		buff := m.buffM.GetOneBuff(BuffTypeNoDead3)
		if m.mgr.CheckRate(buff.info.Eff1Param1) {
			return true
		}
	}
	return false
}

// 获取成员的属性
func (m *Member) GetAttr(id int) int64 {
	return m.attrM.GetAttr(id)
}

// 获取行动排序需要的属性
func (m *Member) GetActAttr(id int) int64 {
	mgr := m.GetManager()
	replaceMem := mgr.getReplaceActMember(m)
	if replaceMem != nil {
		return replaceMem.GetAttr(id)
	}
	return m.GetAttr(id)
}

func (m *Member) GetTarget(target uint32, iexe IExecution, targets *Uniters) {
	m.mgr.GetTarget(m, nil, target, iexe, targets)
}

/*
*
@param id buff系统id
@param param 基础概率
@return *Buff 添加后生成的buff信息
@return *Buff 添加后被删除的buff信息
*/
func (m *Member) DoAddBuff(attack Uniter, formula, param1, param2 uint32, iexe IExecution) {
	m.buffM.DoAddBuff(attack, formula, param1, param2, iexe)
}

func (m *Member) DoRemoveBuff(attack Uniter, formula, param1, param2, param3, param4 uint32, iexe IExecution) RemoveBuffResult {
	return m.buffM.DoRemoveBuff(attack, formula, param1, param2, param3, param4, iexe)
}

func (m *Member) EffectBuffAttr(attack Uniter, formula, param1, param2, param3 uint32, iexe IExecution) {
	m.buffM.EffectBuffAttr(attack, formula, param1, param2, param3, iexe)
}

func (m *Member) ChangeBuffAttr(add uint32, buff *Buff) {
	for k, v := range buff.attr {
		if v != 0 {
			m.ChangeAttr(ChangeAttrTimeBuff, add, k, v*int64(buff.layer))
		}
	}
}

func (m *Member) AddCastBuff(buff *Buff) {
	m.buffM.AddCastBuff(buff)
}

func (m *Member) DelCastBuff(buff *Buff) {
	m.buffM.DelCastBuff(buff)
}

func (m *Member) ClearCastBuff() {
	for _, v := range m.buffM.castBuffs {
		if v != nil && v.owner != nil {
			if v.owner.GetStatus() == UniterAlive {
				v.owner.GetBuffM().removeBuff(v, nil, nil)
			}
		}
	}
	m.buffM.castBuffs = m.buffM.castBuffs[:0]
}

func (m *Member) GetTauntBuffTarget(targets *Uniters) {
	for _, buffs := range m.buffM.buffs {
		for _, v := range buffs {
			if CheckBuffInUse(v) && v.Type() == BuffTypeTaunt && v.attack.GetStatus() == UniterAlive {
				targets.AddUniter(v.attack)
				return
			}
		}
	}
}

func (m *Member) GetChaosBuffTarget(targets *Uniters) {
	_getNoSelfTarget(m.GetManager(), m, 1, targets)
}

func (m *Member) GetCharmBuffTarget() bool {
	bucket := getBuffBucket(BuffTypeCharm)
	for _, buff := range m.buffM.buffs[bucket] {
		if !CheckBuffInUse(buff) || !buff.IsActive() || buff.Type() != BuffTypeCharm {
			continue
		}
		if !m.mgr.CheckRate(int64(buff.info.BuffEffect1)) {
			continue
		}
		targets := gPool.GetUniters()
		if m.mgr.CheckRate(int64(buff.info.BuffEffect2)) {
			_getSelfNoSelfTarget(m.GetManager(), m, 1, targets)
		}
		if targets.GetNum() == 0 {
			targets.AddUniter(buff.attack)
		}

		m.mgr.nowSkill = m.skillM.GetNormalSkill()
		m.mgr.SetNormalSkillBindTargets(targets)
		return true
	}
	return false
}

func (m *Member) IsPhysical() bool {
	return m.Base.AttackType == AttackTypePhysical
}

func (m *Member) AttackType() uint32 {
	return m.Base.AttackType
}

func (m *Member) ChangeAttr(addTime, add uint32, attr int, value int64) {
	m.attrM.ChangeAttr(addTime, add, attr, value)
	if goxml.IsEffectMaxHp(attr) {
		m.MaxHpChanged()
	}
}

func (m *Member) MaxHpChanged() {
	beforeMaxHp := m.maxHp
	initHp := m.GetAttr(goxml.AttrHp)

	maxHpPct := m.attrM.GetAttr(goxml.AttrFixMaxHpPct)
	m.maxHp = int64(float64(initHp) * (float64(maxHpPct) / BaseFloat))
	maxHpPct2 := m.attrM.GetAttr(goxml.AttrFixMaxHpPct2)
	m.maxHp = int64(float64(m.maxHp) * (float64(maxHpPct2) / BaseFloat))
	maxHpPct3 := m.attrM.GetAttr(goxml.AttrFixMaxHpPct3)
	m.maxHp = int64(float64(m.maxHp) * (float64(maxHpPct3) / BaseFloat))
	maxHpPct4 := m.attrM.GetAttr(goxml.AttrHpPctForPokemon)
	m.maxHp = int64(float64(m.maxHp) * (float64(maxHpPct4) / BaseFloat))
	m.maxHp += m.GetAttr(goxml.AttrAbsoluteMaxHp)

	if m.maxHp > beforeMaxHp && m.GetStatus() != UniterDead { //减生命上限,不处理血量
		addHp := m.maxHp - beforeMaxHp
		m.hp += addHp
	}
	if m.hp > m.maxHp {
		m.SetHp(m.maxHp)
	}
	publicHp := m.GetPublicHpGroup()
	if publicHp != nil {
		publicHp.UpdateHp()
	}
	m.mgr.GetRep().NewChangeMaxHp(m.maxHp, m.hp, nil, m, nil)
}

func (m *Member) ChangePsAttr(addTime int, add uint32, attr int, value int64) {
	m.psAttrM.ChangeAttr(addTime, add, attr, value)
}

func (m *Member) FlushDebugInfo(debugSkill bool) *bt.DebugInfo {
	debugInfo := &bt.DebugInfo{
		Id:    m.ID(),
		Attrs: make(map[uint32]int64),
	}
	for i := goxml.AttrAttack; i < int(goxml.AttrMaxNum); i++ {
		value := m.attrM.GetAttr(i)
		if value != 0 {
			debugInfo.Attrs[uint32(i)] = m.attrM.GetAttr(i)
		}
	}

	// 仅战斗测试网站使用
	if isDebugBuff() {
		canAddBuffSet := make(map[uint32]*bt.BuffDebugInfo) //可叠加层数的buff
		for _, buffs := range m.buffM.buffs {
			for _, v := range buffs {
				if CheckBuffInUse(v) {
					if buffIsOverly(v.typeInfo.Add) {
						if value, exist := canAddBuffSet[v.typeInfo.Id]; exist {
							value.Layer += v.layer
						} else {
							canAddBuffSet[v.typeInfo.Id] = v.FlushDebugInfo()
						}
					} else {
						debugInfo.Buffs = append(debugInfo.Buffs, v.FlushDebugInfo())
					}
				}
			}
		}
		for _, value := range canAddBuffSet {
			debugInfo.Buffs = append(debugInfo.Buffs, value)
		}
	}

	// if debugSkill {
	// 	debugInfo.Skills = m.skillM.GetOwnerSkills()
	// 	debugInfo.OtherPsSkills = append(debugInfo.OtherPsSkills, m.passiveSkills...)
	// }
	debugInfo.Pos = m.Pos()
	return debugInfo
}

func (m *Member) Flush(isInit bool) *bt.Member {
	battleMember := gPool.GetBattleMember()
	battleMember.Id = m.id
	battleMember.Info = &bt.MemberInfo{
		UniqId:     m.Base.UniqID,
		SysId:      m.Base.SysID,
		Pos:        m.Base.Pos,
		Stage:      m.Base.Stage,
		Star:       m.Base.Star,
		Level:      m.Base.Level,
		IsMonster:  m.Base.MonsterType != MonsterTypeNone,
		Power:      m.Base.Power,
		Dress:      m.Base.Dress,
		TemplateId: m.GetTemplateID(),
	}
	battleMember.MaxHp = m.GetMaxHp()
	battleMember.NowHp = m.GetHp()
	battleMember.ShowMaxHp = m.GetShowMaxHp()
	battleMember.Info.Gem = make([]uint32, common.GEM_CONFIG_GEM_RIGHT_SLOT)
	for _, gemInfo := range m.Base.GemInfo {
		battleMember.Info.Gem[gemInfo.Slot-1] = gemInfo.Level
	}
	battleMember.Info.EmblemId = m.Base.EmblemID
	if m.Base.OmniLinks != nil {
		battleMember.Info.SelectedLink = make(map[uint32]uint32, len(m.Base.OmniLinks))
		for linkType, linkID := range m.Base.OmniLinks {
			battleMember.Info.SelectedLink[linkType] = linkID
		}
	}
	if isInit {
		if len(m.Base.RaisePSs) > 0 {
			battleMember.Info.RaisePs = make([]uint64, len(m.Base.RaisePSs))
			copy(battleMember.Info.RaisePs, m.Base.RaisePSs)
		}

		battleMember.Attr = make([]int64, goxml.AttrMaxNum)
		copy(battleMember.Attr, m.attrM.baseAttr)
	}
	if da() {
		battleMember.DebugInfo = m.FlushDebugInfo(true)
	}
	return battleMember
}

func (m *Member) HasBuff(buffType uint32) bool {
	return m.buffM.HasBuff(buffType)
}

func (m *Member) HasBuffWithSubType(buffType, buffSubType uint32) bool {
	return m.buffM.HasBuffWithSubType(buffType, buffSubType)
}

// 复活
func (m *Member) DoResurrection(attack Uniter, value int64, iexe IExecution) {
	if value <= 0 {
		skillAttack, ok := iexe.(*OneSkillAttack)
		if ok {
			l4g.Errorf("%d DoResurrection value %d. skill args %+v", m.GetSysID(), value, *skillAttack.GetNowSkill().info)
		}
		pse, ok := iexe.(*PsSkillExe)
		if ok {
			l4g.Errorf("%d DoResurrection value %d. ps args %+v", m.GetSysID(), value, *pse.psSkill.info.Info)
		}
		return
	}
	m.Resurrectioned(attack, value, iexe)
}

// m被复活，value复活后的血量
func (m *Member) Resurrectioned(attack Uniter, value int64, iexe IExecution) {
	m.SetStatus(UniterAlive)
	m.resurrCount++
	m.SetSta(StaResurrectioned, true)

	m.Cured(attack, value, bt.SpecialType_CureTypeResurrect, bt.FactorType_FactorNone, iexe)

	m.mgr.Resurrection(m, true)
	m.mgr.TriggerPsSkill(m, goxml.Event_Revive, attack)
}

func (m *Member) ClearBuffOnDead() {
	keepBuff := m.GetBuffM().GetOneBuff(BuffTypeDeathKeepBenefitBuff)
	for _, buffs := range m.buffM.buffs {
		for _, v := range buffs {
			if CheckBuffInUse(v) && v.info.DeathDispel != 0 {
				if keepBuff != nil && IsBenefitBuff(v.typeInfo) && m.mgr.CheckRate(keepBuff.info.Eff1Param2) {
					if keepBuff.info.Eff1Param1 > 0 {
						v.round += uint32(keepBuff.info.Eff1Param1)
						v.BeUpdate(m.GetBuffM(), nil, nil)
					}
				} else {
					m.buffM.removeBuff(v, nil, nil)
				}
			}
		}
	}
	m.ClearCastBuff() //清除添加过的嘲讽buff
}

// 受到伤害的上限效果
func (m *Member) UseHurtedMax(value *int64) {
	param := m.psAttrM.GetHurtedMax()
	if param > 0 {
		if *value > param {
			*value = param
		}
	}
}

// 受到伤害的上限效果
func (m *Member) UseHurtedHpMax(value *int64) {
	param := m.psAttrM.GetHurtedHpMax()
	if param > 0 {
		if *value > param {
			*value = param
		}
	}
}

// 受到伤害的上限效果
func (m *Member) UseHurtedMaxByAttackAtk(value *int64, attack Uniter) {
	param := m.psAttrM.GetHurtedMaxByAttackAtk(attack)
	if param > 0 {
		if *value > param {
			*value = param
		}
	}
}

func (m *Member) HurtFixByHurtedMax(value *int64, attack Uniter) {
	if *value == 0 {
		return
	}
	m.UseHurtedMax(value)
	m.UseHurtedHpMax(value)
	m.UseHurtedMaxByAttackAtk(value, attack)
}

func (m *Member) UseShield(value int64, iexe IExecution, attack Uniter) (int64, int64) {
	return m.buffM.UseShield(value, iexe, attack)
}

func (m *Member) AddNewSkillByGroupAndLevel(skillGroupID uint32, level uint32, pos int) bool {
	if skillGroupID == 0 || level == 0 {
		return true
	}
	skill := goxml.GetData().SkillInfoM.GroupLevel(skillGroupID, level)
	if skill == nil {
		l4g.Errorf("member add skill error no find groupid %d level:%d", skillGroupID, level)
		return false
	}
	m.addNewSysSkill(skill.Id, pos)
	return true
}

func (m *Member) addNewSysSkill(id uint32, pos int) {
	m.Base.Skills[pos] = id
}

func (m *Member) AddNewSysPassiveSkill(id uint32) {
	m.Base.PassiveSkills = append(m.Base.PassiveSkills, id)
}

/*
func (m *Member) ClearAtOneAttackEnd() {
	m.ClearAttr(ChangeAttrTimeOneAttack)
	m.ClearPsAttr(ChangeAttrTimeOneAttack)
}

func (m *Member) ClearAtOneEffectEnd() {
	m.ClearAttr(ChangeAttrTimeOneEffect)
	m.ClearPsAttr(ChangeAttrTimeOneEffect)
}

func (m *Member) ClearAtOneSkillEnd() {
	m.ClearAttr(ChangeAttrTimeOneSkill)
	m.ClearPsAttr(ChangeAttrTimeOneSkill)
}
*/
//nolint:varnamelen
func (m *Member) ChooseNowSkill(a *OneAction) bool {
	if m.mgr.normalSkillBindTargets != nil {
		gPool.FreeUniters(m.mgr.normalSkillBindTargets)
		m.mgr.normalSkillBindTargets = nil
	}

	nowSkill := m.skillM.GetRoundSkill(m.GetManager().GetRound())
	m.mgr.nowSkill = nowSkill

	m.mgr.TriggerPsSkill(m, goxml.Event_ChooseSkill, a)

	//在判断吟唱，防止放在前面被其他的给替换了
	if m.HasBuff(BuffTypeCastTime) {
		buff := m.buffM.GetOneBuff(BuffTypeCastTime)
		skillID := uint32(buff.info.Eff1Param1)
		m.mgr.ResetNowSkill(skillID)
	}

	oneSkill := NewOneSkill(a, a.GetM().nowSkill, NextSkillNoModify, NextSkillTypeNone, nil)
	defer gPool.FreeOneSkill(oneSkill)
	//判断是否有解除buff的机制 或者替换当前可执行的技能
	m.mgr.TriggerPsSkill(m, goxml.Event_ActionPrepare, oneSkill)

	if m.GetStatus() == UniterDead {
		return false
	}

	m.ChangeSkillByControlBuff(a)
	return true
}

func (m *Member) ChangeSkillByControlBuff(a *OneAction) {
	//优先级先魅惑，再嘲讽，在混乱，在沉默
	if m.GetCharmBuffTarget() {
		return
	}
	if m.HasBuff(BuffTypeTaunt) {
		members := gPool.GetUniters()
		m.GetTauntBuffTarget(members)
		m.mgr.SetNormalSkillBindTargets(members)
		m.mgr.nowSkill = m.skillM.GetNormalSkill() //用原始的普通技能
		return
	}
	if m.HasBuff(BuffTypeChaos) {
		members := gPool.GetUniters()
		m.GetChaosBuffTarget(members)
		m.mgr.SetNormalSkillBindTargets(members)
		m.mgr.nowSkill = m.skillM.GetNormalSkill() //用原始的普通技能
		return
	}
	if m.HasBuff(BuffTypeSilence) { // 沉默的时候, 都是无脑替换
		m.mgr.nowSkill = m.skillM.GetNormalSkill()
		return
	}
}

func (m *Member) GetPSEffectValue(name int, base int64) int64 {
	return m.psAttrM.GetAttr(name) + base
}

func (m *Member) SetAtSkillAttacking() {
	m.staSkillAttack = StaSkillAttacking
}

func (m *Member) SetAtSkillAttackEnd() {
	m.staSkillAttack = StaSkillAttackEnd
}

func (m *Member) ClearAtSkillAttack() {
	m.staSkillAttack = StaNoSkillAttack
}

func (m *Member) GetAtSkillAttack() int {
	return m.staSkillAttack
}

func (m *Member) GetStatus() int {
	return m.status
}

func (m *Member) SetStatus(status int) {
	m.status = status
}

func (m *Member) UpdateAttackResult(hurt *HurtResult) {
	if m.staSkillAttack != StaNoSkillAttack && hurt.Dead {
		m.atSkillKilled = true
	}
}

func (m *Member) IsAtSkillKilled() bool {
	return m.atSkillKilled
}

func (m *Member) ClearAttackResult() {
	m.atSkillKilled = false
}

func (m *Member) GetHeroSysID() uint32 {
	if m.Base.MonsterType != MonsterTypeNone && m.Base.MonsterType != MonsterTypeCallHero &&
		m.Base.MonsterType != MonsterTypeCallDepartedSpirit {
		monster := goxml.GetData().MonsterInfoM.Index(m.Base.SysID)
		if monster == nil {
			l4g.Errorf("MonsterInfoM cant find ID %d", m.Base.SysID)
			return 0
		}
		return monster.HeroId
	}
	return m.Base.SysID
}

func (m *Member) calRaisePsAttr() {
	m.mgr.calRaisePsAttr(m)
}

func (m *Member) GetRaisePSs() []uint64 {
	return m.Base.RaisePSs
}

func (m *Member) ClearRaisePSs() {
	m.Base.RaisePSs = nil
}

func (m *Member) SetSta(sta int, isTrue bool) {
	if isTrue {
		util.BitSetTrueUint64Ptr(&m.bitStatus, sta)
	} else {
		util.BitSetFalseUint64Ptr(&m.bitStatus, sta)
	}
}

func (m *Member) HasSta(sta int) bool {
	return util.BitAndUint64(m.bitStatus, sta)
}

func (m *Member) RecordBuffLayer(buffType, layer uint32) {
	m.buffTypeLayer[buffType] += layer
}

func (m *Member) GetBuffLayerRecord(buffType uint32) uint32 {
	return m.buffTypeLayer[buffType]
}

func (m *Member) UseInvincibleShield(iexe IExecution, attack Uniter) bool {
	return m.buffM.UseInvincibleShield(iexe, attack)
}

// 属性只会继承攻防血，覆盖
func (m *Member) InheritAttr(attack Uniter, inherit uint32) {
	percent := float64(inherit) / BaseFloat
	m.attrM.baseAttr[goxml.AttrAttack] = int64(float64(attack.GetAttr(goxml.AttrAttack)) * percent)
	m.attrM.baseAttr[goxml.AttrHp] = int64(float64(attack.GetAttr(goxml.AttrHp)) * percent)
	m.attrM.baseAttr[goxml.AttrDef] = int64(float64(attack.GetAttr(goxml.AttrDef)) * percent)
	m.attrM.baseAttr[goxml.AttrPhyDef] = int64(float64(attack.GetAttr(goxml.AttrPhyDef)) * percent)
	m.attrM.baseAttr[goxml.AttrMagDef] = int64(float64(attack.GetAttr(goxml.AttrMagDef)) * percent)
}

func (m *Member) GetUniterType() int {
	return UniterTypeMember
}

func (m *Member) GetUniqID() uint64 {
	return m.Base.UniqID
}
func (m *Member) GetPower() uint64 {
	return m.Base.Power
}
func (m *Member) GetLogTag() uint32 {
	return m.Base.LogTag
}

func NewSoulMember(pos uint32, defense *Member) *Member {
	m := newMember(&MemberBase{
		UniqID: defense.Base.UniqID, Pos: pos, SysID: defense.GetHeroSysID(), Stage: defense.Base.Stage,
		Star: defense.Base.Star, Level: defense.Base.Level, Dress: defense.Base.Dress, Power: defense.GetPower(),
		MonsterType: MonsterTypeCall,
	}, defense.attrM.baseAttr)
	copy(m.Base.Skills[:], defense.Base.Skills[:])
	copy(m.Base.PassiveSkills, defense.Base.PassiveSkills)
	return m
}

func (m *Member) LastPos() uint32 {
	return m.lastPos
}

func (m *Member) SetLastPos(pos uint32) {
	m.lastPos = pos
	if pos > 0 {
		m.swapCount++
	}
}

// 获取换位次数
func (m *Member) GetSwapCount() uint32 {
	return m.swapCount
}

func (m *Member) SetTemplateID(sysID uint32) {
	m.templateMonsterId = sysID
}

func (m *Member) GetTemplateID() uint32 {
	return m.templateMonsterId
}

func (m *Member) SetCallerID(id uint32) {
	m.callerID = id
}

func (m *Member) GetCallerID() uint32 {
	return m.callerID
}
