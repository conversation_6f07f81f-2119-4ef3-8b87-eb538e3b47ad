//nolint:mnd
package battle

import (
	"app/goxml"
	"app/protos/out/bt"
	"fmt"
)

func fCheckSubTriggerTimeAttackHurtType(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IHasAttack)
	getV := iArgs.GetAttack().AttackType()
	r := getV == v
	return r
}

func fCheckSubTriggerTimeSkillType(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IInOneSkill)
	getV := iArgs.GetNowSkill().info.Type
	r := getV == v
	return r
}

func fCheckSubTriggerTimeSkillAttackType(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IInOneSkill)
	getV := iArgs.GetNowSkill().info.AttackType
	r := getV == v
	return r
}

func fCheckSubTriggerTimeSkillEffect(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IInOneSkillEffect)
	getV := iArgs.GetSkillEffect().Type
	r := getV == v
	return r
}

func fCheckSubTriggerTimeSkillAttackFactor(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IInOneSkillAttackEnd)
	getV := iArgs.GetIsDodge()
	r := false
	if v == 1 && !getV {
		//必须命中
		r = true
	} else if v == 2 && getV {
		//必须未命中
		r = true
	}
	return r
}

//nolint:varnamelen
func fCheckSubTriggerTimeBuffType(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs, ok := args.(IHasBuffInfo)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasBuffInfo, args:%+v", ps.debug(), args))
	}

	buffInfo := iArgs.GetBuffInfo()
	if buffInfo == nil {
		panic(fmt.Sprintf("%s fCheckSubTriggerTimeBuffType buffInfo is nil, args:%+v", ps.debug(), args))
	}

	getV := buffInfo.Type
	r := getV == v
	return r
}

func fCheckSubTriggerTimeBuffBenefit(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IHasBuffInfo)
	getV := iArgs.GetBuffTypeInfo().IsBenefit == v
	r := getV
	return r
}

func fCheckSubTriggerTimeBuffControl(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IHasBuffInfo)
	r := false
	getV := iArgs.GetBuffTypeInfo().IsControl == 1
	if v == 1 && getV {
		r = true
	} else if v == 2 && !getV {
		r = true
	}
	return r
}

func fCheckSubTriggerTimeHurtType(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IHurtInfo)
	getV := iArgs.GetHurtType()
	r := getV == bt.SpecialType(v)
	return r
}

func fCheckSubTriggerTimeBuffEffectType(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IHasBuffInfo)
	getV := iArgs.GetBuffTypeInfo().BuffEffectType
	r := getV == v
	return r
}

func fCheckSubTriggerTimeMemberRelation(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(*Member)
	var r bool //nolint:varnamelen
	switch v {
	case 1: //自己
		if iArgs.ID() == ps.owner.ID() {
			r = true
		}
	case 2: //我方(包含自己)
		if ps.owner.Index() == iArgs.Index() {
			r = true
		}
	case 3: //我方(不包含自己)
		if ps.owner.Index() == iArgs.Index() && iArgs.ID() != ps.owner.ID() {
			r = true
		}
	case 4: //敌方
		if ps.owner.Index() != iArgs.Index() {
			r = true
		}
	case 5: //敌我双方(场上全体)
		r = true
	case 6: //敌我双方(场上全体，不包含召唤物)
		if !iArgs.IsCall() {
			r = true
		}
	}

	return r
}

func fCheckSubTriggerTimeSource(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IExecution)
	getV := uint32(iArgs.GetExecutionType())
	r := getV == v
	return r
}

func fCheckSubTriggerTimeSkillType2(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IInOneSkill)
	getV := iArgs.GetNowSkill().info.Type2
	r := getV == v
	return r
}

// 赛季羁绊能量达到多少
func fCheckSubTriggerTimeSeasonLinkEnergyValue(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(uint32)
	return iArgs >= v
}

// 是否是净化或驱散
func fCheckSubTriggerTimePurifyOrDispel(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(uint32)
	if v == 0 {
		return true
	}
	return iArgs == v
}

// buff是否存在（未重置）
func fCheckSubTriggerTimeBuffExist(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(*Buff)
	if !iArgs.IsReset() {
		return v == 1
	} else {
		return v == 0
	}
}

//nolint:varnamelen
func fCheckSubTriggerTimeBuffID(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs, ok := args.(IHasBuffInfo)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasBuffInfo, args:%+v", ps.debug(), args))
	}

	buffInfo := iArgs.GetBuffInfo()
	if buffInfo == nil {
		panic(fmt.Sprintf("%s fCheckSubTriggerTimeBuffID buffInfo is nil, args:%+v", ps.debug(), args))
	}

	getV := buffInfo.Id
	r := getV == v
	return r
}

func fCheckSubTriggerTimeAttackNotElite(id int, v uint32, args interface{}, ps *PsSkill) bool {
	if v == 0 { // 配置是0，不需要检查，直接返回true
		return true
	}
	iArgs, ok := args.(IHasAttack)
	if !ok {
		panic(fmt.Sprintf("%s is not IHasAttack, args:%+v", ps.debug(), args))
	}
	notElite := iArgs.GetAttack().EliteType() != 1
	if de() {
		desc := fmt.Sprintf("TriggerCondition19-攻击方不是精英怪，结果=(%t)", notElite)
		dEvent := GetCondCheckDebug(goxml.TriggerCondition_AttackIsElite, desc, notElite)
		ps.owner.GetManager().GetRep().AddDebugEvent(dEvent)
	}
	return notElite
}

func fCheckSubTriggerTimeSkillIsFollow(id int, v uint32, args interface{}, ps *PsSkill) bool {
	iArgs := args.(IInOneSkill)
	if v == 1 {
		return iArgs.GetIsInFollow()
	}
	if v == 2 {
		return !iArgs.GetIsInFollow()
	}
	return false
}

type CheckPsEventArgsFunc func(int, uint32, interface{}, *PsSkill) bool

var gPsEventArgsCondition = [goxml.TriggerCondition_Max]CheckPsEventArgsFunc{
	goxml.TriggerCondition_AttackHurtType:        fCheckSubTriggerTimeAttackHurtType,
	goxml.TriggerCondition_SkillType:             fCheckSubTriggerTimeSkillType,
	goxml.TriggerCondition_SkillAttackType:       fCheckSubTriggerTimeSkillAttackType,
	goxml.TriggerCondition_SkillEffect:           fCheckSubTriggerTimeSkillEffect,
	goxml.TriggerCondition_SkillAttackFactor:     fCheckSubTriggerTimeSkillAttackFactor,
	goxml.TriggerCondition_BuffType:              fCheckSubTriggerTimeBuffType,
	goxml.TriggerCondition_BuffBenefit:           fCheckSubTriggerTimeBuffBenefit,
	goxml.TriggerCondition_BuffIsControl:         fCheckSubTriggerTimeBuffControl,
	goxml.TriggerCondition_HurtType:              fCheckSubTriggerTimeHurtType,
	goxml.TriggerCondition_BuffEffectType:        fCheckSubTriggerTimeBuffEffectType,
	goxml.TriggerCondition_MemberRelation:        fCheckSubTriggerTimeMemberRelation,
	goxml.TriggerCondition_Source:                fCheckSubTriggerTimeSource,
	goxml.TriggerCondition_SkillType2:            fCheckSubTriggerTimeSkillType2,
	goxml.TriggerCondition_SeasonLinkEnergyValue: fCheckSubTriggerTimeSeasonLinkEnergyValue,
	goxml.TriggerCondition_PurifyOrDispel:        fCheckSubTriggerTimePurifyOrDispel,
	goxml.TriggerCondition_BuffExist:             fCheckSubTriggerTimeBuffExist,
	goxml.TriggerCondition_BuffID:                fCheckSubTriggerTimeBuffID,
	goxml.TriggerCondition_AttackIsElite:         fCheckSubTriggerTimeAttackNotElite,
	goxml.TriggerCondition_SkillIsFollow:         fCheckSubTriggerTimeSkillIsFollow,
}
