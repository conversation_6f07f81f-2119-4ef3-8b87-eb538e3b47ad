package battle

import (
	"app/goxml"
)

type OneRound struct {
	IPoolObj
	IOneBattle
}

func NewOneRound(m *Manager, round uint32) *OneRound {
	m.NewRound(round)
	data := gPool.GetOneRound()
	data.Init(m)
	return data
}

func (r *OneRound) Init(m *Manager) {
	r.IOneBattle = m
}

func (r *OneRound) Reset() {
	*r = OneRound{}
}

func (r *OneRound) DoRun() {
	if !r.prepare() {
		return
	}
	r.run()
	r.end()
}

func (r *OneRound) prepare() bool {
	r.GetM().TriggerAllPsSkillSpeedFirst(goxml.Event_OneRoundPrepare, r)

	r.Settle<PERSON>uff(StageOneRoundPrepare)

	r.GetM().TriggerAllPsSkillSpeedFirst(goxml.Event_OneRoundPrepareEnd, r)

	// 重置单回合添加的能量点记录
	if r.GetM().battleParams.IsPVP() {
		r.GetM().attack.initRoundAddEnergy()
		r.GetM().defense.initRoundAddEnergy()
	}
	return true
}

func (r *OneRound) run() {
	var lastMem Uniter
	for i := 0; ; i++ {
		if i >= RoundActionTimesMax {
			panic("OneRound run action times error.")
		}

		if r.GetM().IsFinish() {
			return
		}
		mem := r.ChooseMember(lastMem)
		if mem == nil {
			break
		}
		if mem.GetUniterType() == UniterTypeMember {
			lastMem = mem
		}
		oneAction := NewOneAction(r, mem)
		oneAction.DoRun()
		gPool.FreeOneAction(oneAction)
	}
}

func (r *OneRound) end() {
	r.GetM().TriggerAllPsSkillSpeedFirst(goxml.Event_OneRoundPreEnd, r)
	r.GetM().TriggerBuffEvent(Buff_Event_Round_End, nil)
	r.GetM().ClearRoundAddNextSkills()
}

// =========self============////
func (r *OneRound) SettleBuff(stage uint32) {
	r.GetM().attack.SettleBuff(stage, nil)
	r.GetM().defense.SettleBuff(stage, nil)
}

func (r *OneRound) chooseArtifact(lastMem Uniter) *Artifact {
	var artifact *Artifact
	if lastMem != nil {
		artifact = r.GetM().GetTeam(lastMem).artifactM.chooseArtifact()
		if artifact != nil {
			return artifact
		}
		artifact = r.GetM().GetOpTeam(lastMem).artifactM.chooseArtifact()
		if artifact != nil {
			return artifact
		}
	} else {
		artifact = r.GetM().attack.artifactM.chooseArtifact()
		if artifact != nil {
			return artifact
		}
		artifact = r.GetM().defense.artifactM.chooseArtifact()
		if artifact != nil {
			return artifact
		}
	}
	return artifact
}

func (r *OneRound) ChooseMember(lastMem Uniter) Uniter {
	if u := r.chooseArtifact(lastMem); u != nil {
		return u
	}
	if u := r.GetM().GetActMember(); u != nil {
		return u
	}
	return nil
}
