//nolint:mnd
package battle

import (
	"app/goxml"
)

type Skill struct {
	IPoolObj
	hadUsed    bool //是否使用过
	owner      Uniter
	info       *goxml.SkillInfoExt
	pos        int    // 0 普通技能 1主动1 2主动2, 临时被添加的技能也有位置
	round      uint32 //可执行的回合
	psSkill    []*PsSkill
	tmpPsSkill []*PsSkill
	preAttack  Uniter //继承的attack
	preDefense Uniter //继承的defense
	needEnergy int    //如果需要能量的话,需要减能量
	bindTarget Uniter //可能设置了绑定的目标(反击)
	count      int64  // 技能执行次数
}

//nolint:varnamelen
func NewSkill(owner Uniter, info *goxml.SkillInfoExt, pos int, args interface{},
	needRoundFix, needAddSeasonPs bool) *Skill {
	s := gPool.GetSkill()
	s.owner = owner
	s.info = info
	s.pos = pos
	if s.psSkill == nil {
		s.psSkill = make([]*PsSkill, 0, 3)
	}
	if s.tmpPsSkill == nil {
		s.tmpPsSkill = make([]*PsSkill, 0, 2)
	}
	if args != nil {
		hasAttack, ok := args.(IHasAttack)
		if ok {
			s.preAttack = hasAttack.GetAttack()
		}
		hasDefense, ok := args.(IHasDefense)
		if ok {
			s.preDefense = hasDefense.GetDefense()
		}
	}
	s.Init(needRoundFix, needAddSeasonPs)
	return s
}

func (s *Skill) Init(needRoundFix, needAddSeasonPs bool) {
	m := s.owner
	if s.info.Type != SkillTypeAttr {
		if s.info.PassiveSkill1 > 0 {
			effect := m.AddPsSkill(s.info.PassiveSkill1)
			if effect != nil {
				s.psSkill = append(s.psSkill, effect)
			}
		}
		if s.info.PassiveSkill2 > 0 {
			effect := m.AddPsSkill(s.info.PassiveSkill2)
			if effect != nil {
				s.psSkill = append(s.psSkill, effect)
			}
		}
		if s.info.PassiveSkill3 > 0 {
			effect := m.AddPsSkill(s.info.PassiveSkill3)
			if effect != nil {
				s.psSkill = append(s.psSkill, effect)
			}
		}
		if s.info.PassiveSkill4 > 0 {
			effect := m.AddPsSkill(s.info.PassiveSkill4)
			if effect != nil {
				s.psSkill = append(s.psSkill, effect)
			}
		}
		if s.info.PassiveSkill5 > 0 {
			effect := m.AddPsSkill(s.info.PassiveSkill5)
			if effect != nil {
				s.psSkill = append(s.psSkill, effect)
			}
		}
		for _, tmpPsSkill := range s.info.EffectPassiveSkills {
			effect := m.AddPsSkill(tmpPsSkill)
			if effect != nil {
				effect.setActive(false)
				s.tmpPsSkill = append(s.tmpPsSkill, effect)
			}
		}
		//添加赛季技能(神器)
		if needAddSeasonPs {
			if s.info.SeasonPassiveSkill1 > 0 {
				effect := m.AddPsSkill(s.info.SeasonPassiveSkill1)
				if effect != nil {
					s.psSkill = append(s.psSkill, effect)
				}
			}
			if s.info.SeasonPassiveSkill2 > 0 {
				effect := m.AddPsSkill(s.info.SeasonPassiveSkill2)
				if effect != nil {
					s.psSkill = append(s.psSkill, effect)
				}
			}
			if s.info.SeasonPassiveSkill3 > 0 {
				effect := m.AddPsSkill(s.info.SeasonPassiveSkill3)
				if effect != nil {
					s.psSkill = append(s.psSkill, effect)
				}
			}
		}
	}
	s.round = s.info.FirstCd + 1
	if needRoundFix {
		s.round += s.owner.GetManager().GetRound()
	}
	s.count = 1
}

func (s *Skill) Reset() {
	*s = Skill{}
}

func (s *Skill) GetPos() int {
	return s.pos
}

func (s *Skill) SetPos(pos int) {
	s.pos = pos
}

func (s *Skill) AddTmpPsSkill() {
	for _, v := range s.tmpPsSkill {
		v.setActive(true)
	}
}

func (s *Skill) RemoveTmpPsSkill() {
	for _, v := range s.tmpPsSkill {
		v.setActive(false)
	}

}

func (s *Skill) turnCD(currentRound uint32) {
	s.hadUsed = true
	s.round = currentRound + s.info.CdRound + 1
}

func (s *Skill) resetCD() {
	s.round = s.owner.GetManager().GetRound()
}

func (s *Skill) setBindTarget(target Uniter) {
	s.bindTarget = target
}

func (s *Skill) GetCount() int64 {
	return s.count
}

func (s *Skill) SetCount(count int64) {
	s.count = count
}

// 下一次追击或者连击的技能效果
type NextSkill struct {
	skill     *Skill
	param     int32  //修正系数, 0 无修正
	skillType int    //技能类型
	tUniter   Uniter //添加这个技能的触发者
}

func NewNextSkill(skill *Skill, param int32, skillType int, tUniter Uniter) *NextSkill {
	return &NextSkill{
		skill:     skill,
		param:     param,
		skillType: skillType,
		tUniter:   tUniter,
	}
}
