//nolint:mnd
package battle

import (
	"app/goxml"
	"app/protos/out/bt"
)

// 虚拟单位
// 绑定唯一主体boss
type VoidMember struct {
	Uniter
	pos    uint32
	sysID  uint32 //系统id
	index  uint32
	id     uint32
	uniqID uint64 //外面的唯一id, 对于玩家就是英雄的唯一id 对于怪物就是系统id, 同一个group里的怪物id不相同

	boss    *Member // 主体boss
	mgr     *Manager
	skillM  *SkillManager
	attrM   *VoidAttrManager
	psAttrM *PsAttrManager

	passiveSkills []uint32 //战斗外面初始化的所有被动技能
	raisePSs      []uint64 //战斗外的被动技能
}

func NewVoidMember(uniqID uint64, pos uint32, sysID uint32, boss *Member) *VoidMember {
	m := gPool.GetVoidMember()
	m.uniqID = uniqID
	m.pos = pos
	m.sysID = sysID
	m.passiveSkills = make([]uint32, 0, 5)
	m.boss = boss
	m.attrM = NewVoidAttrManager(m)
	m.psAttrM = NewPsAttrManager(m)
	m.skillM = NewSkillManager(m)
	return m
}

func (v *VoidMember) Reset() {
	*v = VoidMember{}
}

func (v *VoidMember) InitSkills() {
	for _, psSkill := range v.passiveSkills {
		v.skillM.AddPsSkill(psSkill)
	}
}
func (v *VoidMember) EliteType() uint32 {
	return v.boss.EliteType()
}

func (v *VoidMember) GetMonsterType() uint32 {
	return v.boss.Base.MonsterType
}

func (v *VoidMember) IsCall() bool {
	return false
}

func (v *VoidMember) GetCallerID() uint32 {
	return 0
}

func (v *VoidMember) IsPublicHP() bool {
	return false
}

func (v *VoidMember) AddNewSysPassiveSkill(id uint32) {
	v.passiveSkills = append(v.passiveSkills, id)
}
func (v *VoidMember) Init(mgr *Manager, t *Team) {
	v.id = t.NewID(v.pos, false)
	v.index = t.index
	v.mgr = mgr
}
func (v *VoidMember) SetSysID(id uint32) {
	v.sysID = id
}
func (v *VoidMember) ID() uint32 {
	return v.id
}

func (v *VoidMember) Index() uint32 {
	return v.index
}
func (v *VoidMember) Pos() uint32 {
	return v.pos
}

func (v *VoidMember) IsPhysical() bool {
	return v.boss.IsPhysical()
}

func (v *VoidMember) GetAttr(attr int) int64 {
	return v.attrM.GetAttr(attr)
}

func (v *VoidMember) GetHp() int64 {
	return v.boss.GetHp()
}

func (v *VoidMember) GetMaxHp() int64 {
	return v.boss.GetMaxHp()
}
func (v *VoidMember) GetJob() uint32 {
	return v.boss.GetJob()
}
func (v *VoidMember) GetRace() uint32 {
	return v.boss.GetRace()
}

func (v *VoidMember) IsNoAlive() bool {
	return v.boss.IsNoAlive()
}

func (v *VoidMember) GetPSEffectValue(name int, base int64) int64 {
	return v.psAttrM.GetAttr(name) + base
}

func (v *VoidMember) AddNewSkill(id uint32, pos int, args interface{}) *Skill {
	return nil
}

func (v *VoidMember) AddPsSkill(registerPsSkill uint32) *PsSkill {
	return v.skillM.AddPsSkill(registerPsSkill)
}

func (v *VoidMember) Hurted(
	attack Uniter, value int64, hurtType bt.SpecialType, factor bt.FactorType, iexe IExecution) bool {
	return v.boss.Hurted(attack, value, hurtType, factor, iexe)
}

func (v *VoidMember) Cured(attack Uniter, value int64, hurtType bt.SpecialType, factor bt.FactorType, iexe IExecution) {
	v.boss.Cured(attack, value, hurtType, factor, iexe)
}

func (v *VoidMember) DoAddBuff(attack Uniter, formula, id uint32, baseRate uint32, iexe IExecution) {
	v.boss.DoAddBuff(attack, formula, id, baseRate, iexe)
}

func (v *VoidMember) DoRemoveBuff(
	attack Uniter, formula, id uint32, baseRate uint32, param3, param4 uint32, iexe IExecution) RemoveBuffResult {
	return v.boss.DoRemoveBuff(attack, formula, id, baseRate, param3, param4, iexe)
}

func (v *VoidMember) EffectBuffAttr(
	attack Uniter, formula, param1, param2, param3 uint32, iexe IExecution) {
	v.boss.EffectBuffAttr(attack, formula, param1, param2, param3, iexe)
}

func (v *VoidMember) DoResurrection(attack Uniter, value int64, iexe IExecution) {
	// 治疗
	v.boss.DoResurrection(attack, value, iexe)
}

func (v *VoidMember) HasBuff(buffType uint32) bool {
	return false
}

func (v *VoidMember) HasBuffWithSubType(buffType, buffSubType uint32) bool {
	return false
}

func (v *VoidMember) IsStuned() (bool, IExecution) {
	return v.boss.IsStuned()
}

func (v *VoidMember) CanBeChoose(needAlive int) bool {
	if v.HasSta(StaHide) || v.HasSta(StaExile) {
		return false
	}
	if needAlive == TargetNeedAlive && !(v.GetStatus() == UniterAlive) {
		return false
	}
	if needAlive == TargetNeedDead && !(v.GetStatus() == UniterDead) {
		return false
	}
	return true
}

func (v *VoidMember) CanBeChooseIncludeExile(needAlive int) bool {
	if v.HasSta(StaHide) {
		return false
	}
	if needAlive == TargetNeedAlive && !(v.GetStatus() == UniterAlive) {
		return false
	}
	if needAlive == TargetNeedDead && !(v.GetStatus() == UniterDead) {
		return false
	}
	return true
}

func (v *VoidMember) ChangeAttr(addTime, add uint32, attr int, value int64) {
	v.attrM.ChangeAttr(addTime, add, attr, value)
}

func (v *VoidMember) ChangePsAttr(addTime int, add uint32, attr int, value int64) {
	v.psAttrM.ChangeAttr(addTime, add, attr, value)
}

func (v *VoidMember) GetManager() *Manager {
	return v.mgr
}

func (v *VoidMember) GetAttrM() IAttrM {
	return v.attrM
}
func (v *VoidMember) GetBuffM() *BuffManager {
	return v.boss.buffM
}
func (v *VoidMember) GetPsAttrM() *PsAttrManager {
	return v.psAttrM
}

func (v *VoidMember) GetSkillM() ISkillManager {
	return v.skillM
}

func (v *VoidMember) AttackType() uint32 {
	return v.boss.AttackType()
}

func (v *VoidMember) Flush() *bt.Member {
	battleMember := gPool.GetBattleMember()
	battleMember.Id = v.id
	battleMember.Info = &bt.MemberInfo{
		UniqId:    v.uniqID,
		SysId:     v.sysID,
		Pos:       v.pos,
		Stage:     v.boss.Base.Stage,
		Star:      v.boss.Base.Star,
		Level:     v.boss.Base.Level,
		IsMonster: true,
	}
	battleMember.MaxHp = v.GetMaxHp()
	battleMember.NowHp = v.GetHp()
	if da() {
		battleMember.DebugInfo = v.FlushDebugInfo(true)
	}
	return battleMember
}

func (v *VoidMember) FlushDebugInfo(debugSkill bool) *bt.DebugInfo {
	debugInfo := &bt.DebugInfo{
		Attrs:   make(map[uint32]int64),
		IsVoid:  true,
		BossPos: v.boss.Pos(),
	}
	for i := goxml.AttrAttack; i < int(goxml.AttrMaxNum); i++ {
		value := v.attrM.GetAttr(i)
		if value != 0 {
			debugInfo.Attrs[uint32(i)] = value
		}
	}
	if debugSkill {
		debugInfo.Skills = v.skillM.GetOwnerSkills()
		debugInfo.OtherPsSkills = append(debugInfo.OtherPsSkills, v.passiveSkills...)
	}
	debugInfo.Pos = v.Pos()
	return debugInfo
}

func (v *VoidMember) GetStatus() int {
	return v.boss.GetStatus()
}

func (v *VoidMember) SetStatus(status int) {
	v.boss.status = status
}

func (v *VoidMember) SetSta(sta int, isTrue bool) {
	v.boss.SetSta(sta, isTrue)
}

func (v *VoidMember) HasSta(sta int) bool {
	return v.boss.HasSta(sta)
}
func (v *VoidMember) RecordBuffLayer(buffType, layer uint32) {
	v.boss.RecordBuffLayer(buffType, layer)
}
func (v *VoidMember) GetBuffLayerRecord(buffType uint32) uint32 {
	return v.boss.GetBuffLayerRecord(buffType)
}

func (v *VoidMember) UpdateAttackResult(hurt *HurtResult) {
}

func (v *VoidMember) IsAtSkillKilled() bool {
	return false
}

func (v *VoidMember) ClearAttackResult() {
}

func (v *VoidMember) SetAtSkillAttacking() {
}

func (v *VoidMember) SetAtSkillAttackEnd() {
}

func (v *VoidMember) ClearAtSkillAttack() {

}

func (v *VoidMember) calRaisePsAttr() {
	v.mgr.calRaisePsAttr(v)
}

func (v *VoidMember) GetRaisePSs() []uint64 {
	return v.raisePSs
}

func (v *VoidMember) ClearRaisePSs() {
	v.raisePSs = nil
}

func (v *VoidMember) initRaisePs(altRaisePS map[uint32][]uint64) {
	if altRaisePS != nil {
		v.raisePSs = append(v.raisePSs, altRaisePS[0]...)
		v.raisePSs = append(v.raisePSs, altRaisePS[v.pos]...)
	}
	v.calRaisePsAttr()
}

func (v *VoidMember) GetUniterType() int {
	return UniterTypeVoidMember
}
func (v *VoidMember) LastPos() uint32      { return 0 }
func (v *VoidMember) SetLastPos(uint32)    {}
func (v *VoidMember) GetSwapCount() uint32 { return 0 }
