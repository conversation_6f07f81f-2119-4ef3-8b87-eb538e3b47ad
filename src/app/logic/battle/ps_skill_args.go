package battle

import (
	"app/goxml"
	"app/protos/out/bt"
)

type ArgsBuffBeAdd struct {
	sysBuff     *goxml.BuffInfo
	sysBuffType *goxml.BuffTypeInfo
	layer       uint32
	autoID      uint32 //唯一id
	defense     Uniter //buff拥有者
	value       int64  //buff效果值，目前仅用于护盾值 effect51
}

func NewArgsBuffBeAdd(buff *Buff) *ArgsBuffBeAdd {
	arg := gPool.GetArgsBuffBeAdd()
	arg.sysBuff = buff.info
	arg.sysBuffType = buff.typeInfo
	arg.layer = buff.layer
	arg.autoID = buff.id
	arg.defense = buff.owner
	arg.value = buff.value
	return arg
}

func (arg *ArgsBuffBeAdd) Reset() {
	*arg = ArgsBuffBeAdd{}
}

func (arg *ArgsBuffBeAdd) GetBuffInfo() *goxml.BuffInfo {
	return arg.sysBuff
}

func (arg *ArgsBuffBeAdd) GetBuffTypeInfo() *goxml.BuffTypeInfo {
	return arg.sysBuffType
}

func (arg *ArgsBuffBeAdd) GetBuffLayer() uint32 {
	return arg.layer
}

func (arg *ArgsBuffBeAdd) GetBuffAutoID() uint32 {
	return arg.autoID
}

func (arg *ArgsBuffBeAdd) GetDefense() Uniter {
	return arg.defense
}

func (arg *ArgsBuffBeAdd) GetValue() int64 {
	return arg.value
}

type ArgsBuffBeRemove struct {
	sysBuff     *goxml.BuffInfo
	sysBuffType *goxml.BuffTypeInfo
}

// 针对不同的叠加方式处理逻辑是不一样的
func NewArgsBuffBeRemove(buff *Buff) *ArgsBuffBeRemove {
	arg := gPool.GetArgsBuffBeRemove()
	arg.sysBuff = buff.info
	arg.sysBuffType = buff.typeInfo
	return arg
}

func (arg *ArgsBuffBeRemove) Reset() {
	*arg = ArgsBuffBeRemove{}
}

func (arg *ArgsBuffBeRemove) GetBuffInfo() *goxml.BuffInfo {
	return arg.sysBuff
}

func (arg *ArgsBuffBeRemove) GetBuffTypeInfo() *goxml.BuffTypeInfo {
	return arg.sysBuffType
}

// ================//
type ArgsDecHp struct {
	value int64
}

func NewArgsDecHp(value int64) *ArgsDecHp {
	args := gPool.GetArgsDecHp()
	args.value = value
	return args
}

func (arg *ArgsDecHp) Reset() {
	*arg = ArgsDecHp{}
}

func (arg *ArgsDecHp) GetLoseHp() int64 {
	return arg.value
}

/*
//致命伤害
type ArgsDeadHurt struct {
}

	func NewArgsDeadHurt() *ArgsDeadHurt {
		return &ArgsDeadHurt{}
	}

//死亡
type ArgsDead struct {
}

	func NewArgsDead() *ArgsDead {
		return &ArgsDead{}
	}

//复活
type ArgsResurrectioned struct {
}

	func NewArgsResurrectioned() *ArgsResurrectioned {
		return &ArgsResurrectioned{}
	}
*/
func (arg *HurtResult) GetRealHurt() int64 {
	return arg.RealValue
}
func (arg *HurtResult) GetHurtType() bt.SpecialType {
	return arg.HurtType
}

func (arg *HurtResult) GetDefense() Uniter {
	return arg.defense
}

func (arg *CureResult) GetRealCure() int64 {
	return arg.RealValue
}

func (arg *CureResult) GetInitValue() int64 {
	return arg.InitValue
}

func (arg *CureResult) GetOverflow() int64 {
	return arg.Overflow
}

type ArgsBuffBeMianYi struct {
	sysBuff     *goxml.BuffInfo
	sysBuffType *goxml.BuffTypeInfo
}

func NewArgsBuffBeMianYi(buffInfo *goxml.BuffInfo, typeInfo *goxml.BuffTypeInfo) *ArgsBuffBeMianYi {
	arg := gPool.GetArgsBuffBeMianYi()
	arg.sysBuff = buffInfo
	arg.sysBuffType = typeInfo
	return arg
}

func (arg *ArgsBuffBeMianYi) Reset() {
	*arg = ArgsBuffBeMianYi{}
}

func (arg *ArgsBuffBeMianYi) GetBuffInfo() *goxml.BuffInfo {
	return arg.sysBuff
}

func (arg *ArgsBuffBeMianYi) GetBuffTypeInfo() *goxml.BuffTypeInfo {
	return arg.sysBuffType
}

type ArgsBeforeHurtValueFix struct {
	crit bool
	IHasChangeAttrTime
}

func NewArgsBeforeHurtValueFix(crit bool, iHasChangeAttrTime IHasChangeAttrTime) *ArgsBeforeHurtValueFix {
	arg := gPool.GetArgsBeforeHurtValueFix()
	arg.crit = crit
	arg.IHasChangeAttrTime = iHasChangeAttrTime
	return arg
}

func (arg *ArgsBeforeHurtValueFix) Reset() {
	*arg = ArgsBeforeHurtValueFix{}
}

func (arg *ArgsBeforeHurtValueFix) GetHasCrit() bool {
	return arg.crit
}
