//nolint:mnd
package battle

import (
	"app/goxml"
	"fmt"
	"sort"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

/**
1. 所有对防守者的效果都可以立即触发，这个就相当于这次攻击的附加效果，他不会中断这次技能，不会有额外影响
攻击后斩杀，被攻击后吸血。被攻击后免死等效果
所以对非防守者产生的效果，都需要延后执行.

2. 把所有的主动效果也改成被动的机制来制作
3. 所有的触发效果会构建一个结构体，里面会包含一个当前的主动技能的阶段效果
	减少重复的数据构建
	这里因为一个相同的主动技能阶段的不同时机数据内容是不一样的，所以不能直接继承
	因为后面我们还有可能会修改到这个里面的数据

4. 优化
	1. 某个点不在需要触发事件了还是会一直触发，可以不优化。


*/

// 被动技能管理
type PsSkillMgr struct {
	IPoolObj
	IHasSkillStageTarget
	m                *Manager                               //战斗管理器
	psSkills         [goxml.Event_Max]map[uint32][]*PsSkill //所有的被动技能
	delayPsSkillExes []*PsSkillExe                          //延迟执行的被动技能
	poolPsSkills     []*PsSkill
}

func NewPsSkillMgr(m *Manager) *PsSkillMgr {
	psm := gPool.GetPsSkillMgr()
	psm.Init(m)
	return psm
}

func (psm *PsSkillMgr) Init(m *Manager) {
	psm.m = m
	for i := 0; i < goxml.Event_Max; i++ {
		psm.psSkills[i] = make(map[uint32][]*PsSkill)
	}
}

func (psm *PsSkillMgr) Reset() {
	*psm = PsSkillMgr{}
}

//触发事件
/**
@param tUniter 触发者 必须不能为nil
@param triggerTime 触发时机。定义在goxml.TriggerXxx
@param args 触发的时候代入的参数.不同的triggerTime,对应不同的对象
*/
func (psm *PsSkillMgr) TriggerPsSkill(tUniter Uniter, triggerTime int, args interface{}) {
	for _, psSkill := range psm.psSkills[triggerTime][tUniter.ID()] {
		psm.triggerPsSkill(psSkill, tUniter, args)
	}
	// 光环被动
	team := psm.m.GetTeam(tUniter)
	for _, psSkill := range team.GetHaloPsSkills(triggerTime) {
		psm.triggerPsSkill(psSkill, tUniter, args)
	}
}

func (psm *PsSkillMgr) triggerPsSkill(psSkill *PsSkill, tUniter Uniter, args interface{}) {
	if !psSkill.isActive(psm.m) {
		return
	}

	if !psSkill.checkOwnerDeadExecute() {
		return
	}

	if psSkill.owner.HasSta(StaHide) || psSkill.owner.HasSta(StaExile) {
		return
	}

	if !psSkill.checkEventArgs(args) {
		return
	}

	pse := NewPsSkillExe(psm.m.NewAutoID(), tUniter, psSkill, args)
	if !psSkill.delay {
		if !psm.dealConditionAndTriggerCount(pse) {
			return
		}
		psm.CastPsSkill(pse)
	} else {
		//稍后处理
		psm.delayPsSkillExes = append(psm.delayPsSkillExes, pse)
	}
}

// 处理检查条件和更新次数
func (psm *PsSkillMgr) dealConditionAndTriggerCount(pse *PsSkillExe) bool {
	if !pse.checkTriggerCondition(pse.psSkill.info.Info.MaxExecute) {
		gPool.FreePsSkillExe(pse)
		return false
	}
	if de() {
		psm.m.GetRep().AddDebugEvent(fmt.Sprintf("被动技能id:%d id:%d 被动技能信息:%s",
			pse.psSkill.info.Info.Id, pse.id, pse.psSkill.sDebug()))
	}
	pse.addTriggerCount()
	return true
}

// 延迟执行的被动技能
// @return bool 是否存在延迟执行的复活类被动
func (psm *PsSkillMgr) DelayExecute() bool {
	var runDelayResurrection bool
	if len(psm.delayPsSkillExes) == 0 {
		return runDelayResurrection
	}
	skills := make([]*PsSkillExe, len(psm.delayPsSkillExes))
	copy(skills, psm.delayPsSkillExes)
	sort.Slice(skills, func(i, j int) bool {
		return skills[i].psSkill.priority < skills[j].psSkill.priority
	})
	psm.delayPsSkillExes = psm.delayPsSkillExes[0:0]
	for len(skills) > 0 {
		for _, skill := range skills {
			if !psm.dealConditionAndTriggerCount(skill) {
				continue
			}

			if util.InUint32s(skill.psSkill.info.EffectInfo.Effects, goxml.EffectResurrection) {
				runDelayResurrection = true
			}
			psm.CastPsSkill(skill)
		}
		if len(psm.delayPsSkillExes) > 0 {
			skills = make([]*PsSkillExe, len(psm.delayPsSkillExes))
			copy(skills, psm.delayPsSkillExes)
			psm.delayPsSkillExes = psm.delayPsSkillExes[0:0]
		} else {
			break
		}
	}
	return runDelayResurrection
}

func (psm *PsSkillMgr) AddPsSkillTriggers(triggerTime int, ID uint32, ps *PsSkill) {
	beginIndex := len(psm.psSkills[triggerTime][ID])
	for index, psSkill := range psm.psSkills[triggerTime][ID] {
		if psSkill.priority > ps.priority {
			beginIndex = index
			break
		}
	}

	psm.psSkills[triggerTime][ID] = append(psm.psSkills[triggerTime][ID], nil)
	psSkills := psm.psSkills[triggerTime][ID]
	copy(psSkills[beginIndex+1:], psSkills[beginIndex:])
	psSkills[beginIndex] = ps
}

// 注册事件
// 多次注册只是激活
// //如何防止重复注册？
func (psm *PsSkillMgr) RegisterPsSkill(owner Uniter, registerPsSkill uint32) *PsSkill {
	info := goxml.GetData().PassiveSkillInfoM.Index(registerPsSkill)
	if info == nil {
		panic(fmt.Sprintf("mem:%d NewPassiveSkillEffect %v", owner.ID(), registerPsSkill))
	}
	if info.Info.FormationGroupId != 0 && !psm.m.CheckInForamation(info.Info.FormationGroupId) {
		return nil
	}
	ID := owner.ID()
	if ePs, exist := psm.psSkills[int(info.TriggerInfo.Event)][ID]; exist {
		for _, ps := range ePs {
			if ps.info.Info.Id == info.Info.Id && ps.owner.ID() == ID {
				if !ps.isActive(psm.m) {
					ps.setActive(true)
				}
				return ps
			}
		}
	}

	psSkill := NewPsSkill(psm.m.NewAutoID(), owner, info)
	psm.poolPsSkills = append(psm.poolPsSkills, psSkill)
	psm.addPsSkillTriggersByTarget(owner, psSkill)
	return psSkill
}

func (psm *PsSkillMgr) addPsSkillTriggersByTarget(owner Uniter, psSkill *PsSkill) {
	target := psSkill.info.TriggerInfo.Target
	triggerTime := int(psSkill.info.TriggerInfo.Event)
	switch target {
	case goxml.TriggerTarget_None:
		team := psm.m.GetTeam(owner)
		team.ForEachMember(func(mem *Member) {
			psm.AddPsSkillTriggers(triggerTime, mem.ID(), psSkill)
		})
		if team.artifactM != nil {
			psm.AddPsSkillTriggers(triggerTime, team.artifactM.ID(), psSkill)
		}

		team2 := psm.m.GetOpTeam(owner)
		team2.ForEachMember(func(mem *Member) {
			psm.AddPsSkillTriggers(triggerTime, mem.ID(), psSkill)
		})
		if team2.artifactM != nil {
			psm.AddPsSkillTriggers(triggerTime, team2.artifactM.ID(), psSkill)
		}
	case goxml.TriggerTarget_Self:
		psm.AddPsSkillTriggers(triggerTime, owner.ID(), psSkill)
	case goxml.TriggerTarget_TeamAll:
		team := psm.m.GetTeam(owner)
		if psSkill.info.Info.IsHalo == 1 {
			team.AddHaloPsSkill(triggerTime, psSkill)
		} else {
			team.ForEachMember(func(mem *Member) {
				psm.AddPsSkillTriggers(triggerTime, mem.ID(), psSkill)
			})
		}
	case goxml.TriggerTarget_TeamOther:
		team := psm.m.GetTeam(owner)
		team.ForEachMember(func(mem *Member) {
			if mem.ID() != owner.ID() {
				psm.AddPsSkillTriggers(triggerTime, mem.ID(), psSkill)
			}
		})
	case goxml.TriggerTarget_OpTeamAll:
		team := psm.m.GetOpTeam(owner)
		if psSkill.info.Info.IsHalo == 1 {
			team.AddHaloPsSkill(triggerTime, psSkill)
		} else {
			team.ForEachMember(func(mem *Member) {
				psm.AddPsSkillTriggers(triggerTime, mem.ID(), psSkill)
			})
		}
	case goxml.TriggerTarget_TeamAll2:
		team := psm.m.GetTeam(owner)
		team.ForEachMember(func(mem *Member) {
			psm.AddPsSkillTriggers(triggerTime, mem.ID(), psSkill)
		})

		if team.artifactM != nil {
			psm.AddPsSkillTriggers(triggerTime, team.artifactM.ID(), psSkill)
		}
	case goxml.TriggerTarget_TeamUniter:
		team := psm.m.GetTeam(owner)
		psm.AddPsSkillTriggers(triggerTime, team.tUniter.ID(), psSkill)
	case goxml.TriggerTarget_OpTeamUniter:
		team := psm.m.GetOpTeam(owner)
		psm.AddPsSkillTriggers(triggerTime, team.tUniter.ID(), psSkill)
	default:
		targets := gPool.GetUniters()
		defer gPool.FreeUniters(targets)
		psm.m.GetTarget(owner, nil, target, nil, targets)
		totalNum := targets.GetNum()
		uniterArray := targets.GetUniters()
		for i := 0; i < totalNum; i++ {
			psm.AddPsSkillTriggers(triggerTime, uniterArray[i].ID(), psSkill)
		}
	}
}

func (psm *PsSkillMgr) CastPsSkill(pse *PsSkillExe) {
	cfg := pse.psSkill.info
	if IsBattleDebug() {
		l4g.Debug("执行技能: %d %s", pse.psSkill.info.Info.Id, pse.psSkill.sDebug())
	}
	defer gPool.FreePsSkillExe(pse)
	var lastPse *PsSkillExe
	for stage, effectData := range cfg.EffectDatas {
		if effectData == nil {
			panic(fmt.Sprintf("CastPsSkill: no effectData, id:%d info:%+v", pse.executor.ID(), cfg.Info))
		}
		pse.updateExecutor(effectData.Executor)

		if !cfg.CanDeadExecute(pse.executor.GetStatus() == UniterDead) {
			if stage == 0 { // 第一段效果直接返回
				return
			}
			if pse.executor.GetStatus() == UniterDead && cfg.Info.EffDeadExecute == 0 { // 后续的效果，EffDeadExecute不等于0的时候，死亡也要执行
				return
			}
		}

		if pse.executor.IsCall() && util.InUint32s(goxml.EffectCallList, effectData.Effect) {
			// 召唤物不执行召唤类效果
			return
		}

		if cfg.Info.Show == 1 {
			psm.m.GetRep().NewTriggerPassive(pse.executor, nil, pse)
		}

		if lastPse != nil {
			pse.updateLastArgs(lastPse)
		}
		targets := gPool.GetUniters()
		defer gPool.FreeUniters(targets)
		psm.getTarget(pse, effectData.Target, targets, nil)

		var redirectTargets *Uniters
		if effectData.PassiveTarget > 0 {
			redirectTargets = gPool.GetUniters()
			defer gPool.FreeUniters(redirectTargets)
			psm.getTarget(pse, effectData.PassiveTarget, redirectTargets, nil)
		}

		totalNum := targets.GetNum()
		for i := 0; i < totalNum; i++ {
			psm.m.DoPassiveSkillEffect(pse.executor, targets.GetUniters()[i], pse, effectData, redirectTargets)
		}

		if redirectTargets != nil {
			pse.SetSkillTargets(stage, redirectTargets)
		} else {
			pse.SetSkillTargets(stage, targets)
		}

		// 为了继承多段的args，暂存上一个pse
		lastPse = pse
	}
}

func (psm *PsSkillMgr) getTarget(pse *PsSkillExe, target uint32, targets *Uniters, defenseUniter Uniter) {
	switch target {
	case 2: //触发天赋时候的攻击者
		attack := pse.GetAttack()
		if attack == nil {
			return
		}
		if attack.CanBeChoose(TargetNeedAlive) {
			targets.AddUniter(attack)
		}
		return
	case 3:
		defense := pse.GetDefense()
		if defense == nil {
			return
		}
		if defense.CanBeChoose(TargetNeedAlive) {
			targets.AddUniter(defense)
		}
		return
	//TODO 4为什么不需要触发呢?
	case 4: ////谁触发了天赋，就选谁做目标, 这个也不需要判断是否存活和1一样
		tUniter := pse.GetTriggerUniter()
		if tUniter.CanBeChoose(TargetNoNeedAlive) {
			targets.AddUniter(tUniter)
		}
		return
	case 5: //连击类技能的触发者,和当前被动技能的触发者
		oneSkill := pse.args.(*OneSkill)
		if oneSkill.skillType == NextSkillTypeNone {
			panic("skill is not NextSkill")
		}
		if oneSkill.tUniter.CanBeChoose(TargetNoNeedAlive) {
			targets.AddUniter(oneSkill.tUniter)
		}
		if pse.GetTriggerUniter().CanBeChoose(TargetNoNeedAlive) {
			targets.AddUniter(pse.GetTriggerUniter())
		}
	default:
		psm.m.GetTarget(pse.executor, defenseUniter, target, pse, targets)
	}
}

func (psm *PsSkillMgr) HadPsSkill(ID uint32, psSkill *PsSkill) bool {
	if hadPSs, exist := psm.psSkills[int(psSkill.info.TriggerInfo.Event)][ID]; exist {
		for _, hadPS := range hadPSs {
			if hadPS.info.Info.Id == psSkill.info.Info.Id {
				return true
			}
		}
	}
	return false
}
