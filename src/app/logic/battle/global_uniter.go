package battle

import (
	"app/goxml"
	"app/protos/out/bt"
)

// 目前只作为一个被动技能的载体
type TeamUniter struct {
	Uniter
	mgr             *Manager
	team            *Team //所属的team
	id              uint32
	raisePSs        []uint64 //战斗外的被动技能
	haloAttr        map[uint32]map[int]int64
	totalHaloAttr   map[int]int64
	haloPsAttr      map[uint32]map[int]int64
	totalHaloPsAttr map[int]int64
}

func (tUniter *TeamUniter) Reset() {
	*tUniter = TeamUniter{}
}

func (tUniter *TeamUniter) Init(mgr *Manager, team *Team) {
	tUniter.team = team
	tUniter.mgr = mgr
	tUniter.id = tUniter.team.NewID(TeamUniterBattlePos, false)
	tUniter.haloAttr = make(map[uint32]map[int]int64)
	tUniter.totalHaloAttr = make(map[int]int64)
	tUniter.haloPsAttr = make(map[uint32]map[int]int64)
	tUniter.totalHaloPsAttr = make(map[int]int64)
}

func (tUniter *TeamUniter) addLinkPassiveSkills() {
	if !tUniter.team.IsCalculateLink() {
		return
	}
	tUniter.team.addAllLink()
	tUniter.team.calcChangeLink()
	tUniter.team.activedLink()
	tUniter.team.activedArtifactLink()
	tUniter.mgr.report.addInitLink(tUniter.team)
}

func (tUniter *TeamUniter) Index() uint32 {
	return tUniter.team.index
}

func (tUniter *TeamUniter) Pos() uint32 {
	return TeamUniterBattlePos
}

func (tUniter *TeamUniter) SetPos(pos uint32) {
}

func (tUniter *TeamUniter) GetManager() *Manager {
	return tUniter.mgr
}

func (tUniter *TeamUniter) GetStatus() int {
	return UniterAlive
}

func (tUniter *TeamUniter) UpdateAttackResult(hurt *HurtResult) {
}

func (tUniter *TeamUniter) AddPsSkill(registerPsSkill uint32) *PsSkill {
	return tUniter.GetManager().psm.RegisterPsSkill(tUniter, registerPsSkill)
}

func (tUniter *TeamUniter) Flush() *bt.TeamMember {
	battleTeamMember := &bt.TeamMember{
		Id: tUniter.id,
	}
	if len(tUniter.raisePSs) > 0 {
		battleTeamMember.RaisePs = make([]uint64, len(tUniter.raisePSs))
		copy(battleTeamMember.RaisePs, tUniter.raisePSs)
	}

	if da() {
		battleTeamMember.DebugInfo = tUniter.FlushDebugInfo(true)
	}
	return battleTeamMember
}

func (tUniter *TeamUniter) FlushDebugInfo(debugSkill bool) *bt.DebugInfo {
	debugInfo := &bt.DebugInfo{
		Id: tUniter.ID(),
	}
	debugInfo.Pos = tUniter.Pos()
	return debugInfo
}

func (tUniter *TeamUniter) ID() uint32 {
	return tUniter.id
}

func (tUniter *TeamUniter) GetPSEffectValue(name int, base int64) int64 {
	return base
}

func (tUniter *TeamUniter) calRaisePsAttr() {
	tUniter.mgr.calRaisePsAttr(tUniter)
}

func (tUniter *TeamUniter) GetRaisePSs() []uint64 {
	return tUniter.raisePSs
}

func (tUniter *TeamUniter) ClearRaisePSs() {
	tUniter.raisePSs = nil
}

func (tUniter *TeamUniter) SetRaisePSs(raisePS []uint64) {
	tUniter.raisePSs = raisePS
}

func (tUniter *TeamUniter) GetAttr(attr int) int64 {
	return goxml.GetData().TeamInitDataInfoM.GetAttr(attr)
}

func (tUniter *TeamUniter) initRaisePs(altRaisePS map[uint32][]uint64) {
	if altRaisePS != nil {
		tUniter.raisePSs = append(tUniter.raisePSs, altRaisePS[TeamUniterBattlePos]...)
	}
	tUniter.calRaisePsAttr()
}

func (tUniter *TeamUniter) GetUniterType() int {
	return UniterTypeTeamUniter
}

// 添加光环属性
func (tUniter *TeamUniter) AddHaloAttr(mem Uniter, attrType int, value int64) {
	if value == 0 {
		return
	}
	attrs := tUniter.haloAttr[mem.ID()]
	if attrs == nil {
		attrs = make(map[int]int64)
		tUniter.haloAttr[mem.ID()] = attrs
	}
	attrs[attrType] += value
	tUniter.totalHaloAttr[attrType] += value

	//队伍所有的人需要设置属性的修改
	for _, mem := range tUniter.team.members {
		mem.GetAttrM().AttrChanged(attrType)
	}
}
func (tUniter *TeamUniter) AddHaloPsAttr(mem Uniter, attrType int, value int64) {
	if value == 0 {
		return
	}
	addType := gPsAttrAddType[attrType]
	attrs := tUniter.haloPsAttr[mem.ID()]
	if attrs == nil {
		attrs = make(map[int]int64)
		tUniter.haloPsAttr[mem.ID()] = attrs
	}

	if addType == PsAttrAddTypeAdd {
		attrs[attrType] += value
		tUniter.totalHaloPsAttr[attrType] += value
	} else if addType == PsAttrAddTypeMin {
		if oldValue, exist := attrs[attrType]; exist {
			if value < oldValue {
				attrs[attrType] = value
			}
		} else {
			attrs[attrType] = value
		}
		if oldValue, exist := tUniter.totalHaloPsAttr[attrType]; exist {
			if value < oldValue {
				tUniter.totalHaloPsAttr[attrType] = value
			}
		} else {
			tUniter.totalHaloPsAttr[attrType] = value
		}
	} else if addType == PsAttrAddTypeMax {
		if value > attrs[attrType] {
			attrs[attrType] = value
		}
		if value > tUniter.totalHaloPsAttr[attrType] {
			tUniter.totalHaloPsAttr[attrType] = value
		}
	}
}
func (tUniter *TeamUniter) UniterDead(mem Uniter) {
	for attrType, value := range tUniter.haloAttr[mem.ID()] {
		tUniter.totalHaloAttr[attrType] -= value
	}

	for attrType, value := range tUniter.haloPsAttr[mem.ID()] {
		addType := gPsAttrAddType[attrType]
		if addType == PsAttrAddTypeAdd {
			tUniter.totalHaloPsAttr[attrType] -= value
		} else {
			if value == tUniter.totalHaloPsAttr[attrType] {
				tUniter.ResetHaloPsAttr(attrType, addType)
			}
		}
	}
}
func (tUniter *TeamUniter) ResetHaloPsAttr(attrType, addType int) {
	hasInit := false
	var value int64
	for _, attrs := range tUniter.haloPsAttr {
		if hasValue, exist := attrs[attrType]; exist {
			if !hasInit {
				value = hasValue
				continue
			}
			if (addType == PsAttrAddTypeMin && hasValue < value) ||
				(addType == PsAttrAddTypeMax && hasValue > value) {
				value = hasValue
			}
		}
	}
	tUniter.totalHaloAttr[attrType] = value
}
func (tUniter *TeamUniter) UniterAlive(mem Uniter) {
	for attrType, value := range tUniter.haloAttr[mem.ID()] {
		tUniter.totalHaloAttr[attrType] += value
	}

	for attrType, value := range tUniter.haloPsAttr[mem.ID()] {
		addType := gPsAttrAddType[attrType]
		if addType == PsAttrAddTypeAdd {
			tUniter.totalHaloPsAttr[attrType] += value
		} else if addType == PsAttrAddTypeMin {
			if value < tUniter.totalHaloPsAttr[attrType] {
				tUniter.totalHaloPsAttr[attrType] = value
			}
		} else if addType == PsAttrAddTypeMax {
			if value > tUniter.totalHaloPsAttr[attrType] {
				tUniter.totalHaloPsAttr[attrType] = value
			}
		}
	}
}

func (tUniter *TeamUniter) GetHaloAttr(attrType int) int64 {
	return tUniter.totalHaloAttr[attrType]
}
func (tUniter *TeamUniter) GetHaloPsAttr(attrType int) (int64, bool) {
	if value, exist := tUniter.totalHaloPsAttr[attrType]; exist {
		return value, exist
	}
	return 0, false
}
func (tUniter *TeamUniter) HasSta(sta int) bool {
	return false
}
func (tUniter *TeamUniter) SetSta(sta int, isTrue bool) {
}
func (tUniter *TeamUniter) RecordBuffLayer(buffType, layer uint32)    {}
func (tUniter *TeamUniter) GetBuffLayerRecord(buffType uint32) uint32 { return 0 }

func (tUniter *TeamUniter) GetMonsterType() uint32 {
	return MonsterTypeNone
}
func (tUniter *TeamUniter) LastPos() uint32      { return 0 }
func (tUniter *TeamUniter) SetLastPos(uint32)    {}
func (tUniter *TeamUniter) GetSwapCount() uint32 { return 0 }
func (tUniter *TeamUniter) IsCall() bool         { return false }
func (tUniter *TeamUniter) GetCallerID() uint32  { return 0 }
func (tUniter *TeamUniter) IsPublicHP() bool     { return false }
