package battle

import (
	appsrv "app/service"
	"container/heap"
	"encoding/json"
	"maps"
	"math"
	"slices"

	"fmt"
	"runtime/debug"
	"sort"
	"strings"

	"app/goxml"
	"app/protos/out/bt"
	"app/protos/out/common"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"github.com/shopspring/decimal"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
)

type Manager struct {
	IPoolObj
	attack  *Team
	defense *Team

	psm *PsSkillMgr
	bem *BuffEventManager

	unites []Uniter //战斗中所有产生的uniter,对象池中的uniter都在这里回收
	rd     *rand.Rand

	nowSkill               *Skill       //当前执行的技能
	nextSkills             []*NextSkill //等待执行的技能
	roundAddNextSkills     []*Skill     //当前回合添加过的技能
	normalSkillBindTargets *Uniters     //当由于控制类buff的影响 需要执行普通技能时,绑定的目标

	report *Report

	battleGlobalValue *ManagerGlobalValue //正常战斗全局的数据

	battleParams     *ManagerParams    //战斗参数
	battleStatus     ManagerStatus     //战斗状态
	members          []*Member         //排序的所有可行动的member(给被动触发用)
	waitMembers      MemberHeap        // 等待行动的member
	actMemberReplace map[uint32]uint32 // 等待行动的member的替换。轮到key执行，就改为value执行
}

type BattleLimitValue struct {
	//被动各种限制
	addAttrValue    map[int]int64 //attrType => value
	reduceAttrValue map[int]int64
	owner           Uniter
}

func (b *BattleLimitValue) AddAttr(attrType int, addValue int64) {
	if addValue > 0 {
		b.addAttrV(attrType, addValue, b.addAttrValue)
	} else {
		b.addAttrV(attrType, addValue, b.reduceAttrValue)
	}
}
func (b *BattleLimitValue) addAttrV(attrType int, value int64, attrValue map[int]int64) {
	attrValue[attrType] += value
}

func (b *BattleLimitValue) GetLimitedAttr(attrType int, addValue int64, limitedV int64) int64 {
	var hadValue int64
	fixLimit := b.owner.GetPSEffectValue(PsAttrAttrLimitFix, 0)
	if fixLimit != 0 {
		limitedV = int64(math.Floor(float64(limitedV) * float64(goxml.BaseInt64+fixLimit) / goxml.BaseFloat))
	}
	if addValue > 0 {
		hadValue = b.addAttrValue[attrType]
		if limitedV <= hadValue {
			return 0
		}
		canAddValue := limitedV - hadValue
		if canAddValue >= addValue {
			return addValue
		}
		return canAddValue
	} else {
		hadValue = b.reduceAttrValue[attrType]
		if limitedV <= -hadValue {
			return 0
		}
		canReduceValue := limitedV + hadValue
		if canReduceValue+addValue >= 0 {
			return addValue
		}
		return -canReduceValue
	}
}

//nolint:recvcheck
type MemberHeap []*Member

func (h MemberHeap) Len() int { return len(h) }
func (h MemberHeap) Less(i, j int) bool {
	s1 := h[i].GetActAttr(goxml.AttrSpeed)
	s2 := h[j].GetActAttr(goxml.AttrSpeed)
	if s1 == s2 {
		if h[i].Index() == h[j].Index() {
			return h[i].Pos() < h[j].Pos()
		}
		return h[i].Index() < h[j].Index()
	}
	return s1 > s2
}
func (h MemberHeap) Swap(i, j int) { h[i], h[j] = h[j], h[i] }

func (h *MemberHeap) Push(x interface{}) {
	*h = append(*h, x.(*Member))
}

func (h *MemberHeap) Pop() interface{} {
	old := *h
	n := len(old)
	x := old[n-1]
	*h = old[0 : n-1]
	return x
}

// 战斗外的修正属性
type AltAttr struct {
	attack          map[uint32]map[int]int64 //pos=>attrId=>attrValue, pos=0代表全部位置都生效(不包括神器，神器用位置ArtifactBattlePos)
	attackIDHpPct   map[uint64]int64         //攻击方按照id修正血量百分比, 不能传0
	defense         map[uint32]map[int]int64
	defenseIDHpPct  map[uint64]int64 //防守方按照血量id修正血量百分比
	attackPosHpPct  map[uint32]int64 // 攻击方根据pos修正血量万分比
	defensePosHpPct map[uint32]int64 // 防守方根据pos修正血量万分比
	fixMaxHP        bool             // 是否修改最大血量
}

func NewAltAttr() *AltAttr {
	return &AltAttr{}
}

// 修正攻击方的属性
func (a *AltAttr) SetAttack(attrs map[uint32]map[int]int64) {
	if a.attack == nil {
		a.attack = make(map[uint32]map[int]int64)
	}

	for pos, attrMap := range attrs {
		if a.attack[pos] == nil {
			a.attack[pos] = make(map[int]int64)
		}
		for attrID, attrValue := range attrMap {
			a.attack[pos][attrID] += attrValue
		}
	}
}

// 修正防守方的属性
func (a *AltAttr) SetDefense(attrs map[uint32]map[int]int64) {
	if a.defense == nil {
		a.defense = make(map[uint32]map[int]int64)
	}
	for pos, attrMap := range attrs {
		if a.defense[pos] == nil {
			a.defense[pos] = make(map[int]int64)
		}

		for attrID, attrValue := range attrMap {
			a.defense[pos][attrID] += attrValue
		}
	}
}

// 修正攻击方的血量
func (a *AltAttr) SetAttackIDHpPct(hp map[uint64]int64) {
	a.attackIDHpPct = hp
}

func (a *AltAttr) GetAttackIDHpPct() map[uint64]int64 {
	return a.attackIDHpPct
}

// 修正防守方的血量
func (a *AltAttr) SetDefenseIDHpPct(hp map[uint64]int64) {
	a.defenseIDHpPct = hp
}

func (a *AltAttr) GetDefenseIDHpPct() map[uint64]int64 {
	return a.defenseIDHpPct
}

func (a *AltAttr) SetAttackPosHpPct(hp map[uint32]int64) {
	a.attackPosHpPct = hp
}

func (a *AltAttr) SetDefensePosHpPct(hp map[uint32]int64) {
	a.defensePosHpPct = hp
}

func (a *AltAttr) SetFixMaxHP() {
	a.fixMaxHP = true
}

func (a *AltAttr) EffectBattleParams(battleParams *ManagerParams) {
	if a.attack != nil {
		//迷宫不修正己方的战力
		if battleParams.GetAttackFID() != uint32(common.FORMATION_ID_FI_MAZE) {
			battleParams.SetCalAttackPower()
		}
	}
	if a.defense != nil {
		battleParams.SetCalDefensePower()
	}
}

func NewManager(attack, defense *Team, battleParams *ManagerParams) *Manager {
	m := gPool.GetManager()
	m.init(attack, defense, battleParams)
	return m
}

func (m *Manager) init(attack, defense *Team, battleParams *ManagerParams) {
	m.attack = attack
	m.defense = defense
	m.battleParams = battleParams
	//这个可能复用
	if m.rd == nil {
		m.rd = rand.New(time.Now().UnixNano())
	}
	m.battleStatus.ret = ret.RET_OK
	m.psm = NewPsSkillMgr(m)
	m.bem = NewBuffEventManager(m)
	m.report = NewReport(m)
	m.battleGlobalValue = &ManagerGlobalValue{}
	m.battleGlobalValue.Init()
	m.members = make([]*Member, 0, len(m.attack.members)+len(m.defense.members))
	m.initTeam()
}

func (m *Manager) Reset() {
	*m = Manager{
		rd: m.rd,
	}
}

func (m *Manager) GetTeamByIndex(teamIndex uint32) *Team {
	if teamIndex == AttackTeam {
		return m.attack
	}
	return m.defense
}

// 开始战斗
func (m *Manager) Run() {
	defer func() {
		CloseDebugAttr()
		CloseDebugEvent()
		CloseDebugFormula()
		CleanDefaultMaxSeasonEnergy()
		ResetDefaultCheckSeason()
		CloseDebugBuff()
		CloseBattleDebug()
		if err := recover(); err != nil {
			var b strings.Builder
			b.WriteString(fmt.Sprintf("battle panic:\n\nserviceName: %s\nport: %s\n\n\n\n", *appsrv.ServiceName, *appsrv.DebugAddr))
			b.WriteString(fmt.Sprintf("battle panic:errInfo:%s stackInfo:%s\n", err, debug.Stack()))
			errStr := b.String()

			l4g.Error(errStr)
			/*
				if *appsrv.PanicRecover {
					feishurobot.SendMsg(errStr)
				}
			*/
			gPool.reset()
			m.battleStatus.ret = ret.RET_ERROR
			m.battleStatus.err = strings.ReplaceAll(strings.ReplaceAll(errStr, " ", "&nbsp;"), "\n", "<br/>")
		}
	}()
	if m.IsFinish() {
		m.report.AddMemberInfoOnErr(m)
		reportInfo, err := json.Marshal(m.GetReport())
		if err != nil {
			l4g.Errorf("%s", err.Error())
			return
		}
		errInfo := fmt.Sprintf("battle panic: finish at begin. formationID:%d reportInfo:%s", m.battleParams.attackFID, string(reportInfo))
		l4g.Errorf("%s", errInfo)
		//对业务层暴露错误，方便查找问题
		m.battleStatus.ret = ret.RET_ERROR
		m.battleStatus.err = errInfo
		return
	}
	m.Prepare()
	m.DoRound()
	m.Finish()
}

func (m *Manager) GetRet() ret.RET {
	return m.battleStatus.ret
}

func (m *Manager) GetError() string {
	return m.battleStatus.err
}

// 战斗准备阶段，属性修正，数据 整合
func (m *Manager) Prepare() {
	m.initSkills()
	//重新计算战力
	m.calPower()
	//战力碾压
	m.initTeamPowerSuppress()
	//初始化阶段的战报(初始状态用于战报重新战斗)
	m.report.InitPrepare(m)
}

func (m *Manager) initSkills() {
	m.attack.initSkills()
	m.defense.initSkills()
}

func (m *Manager) calPower() {
	//赛季玩法不做战力修正
	formationConfigInfo := goxml.GetData().FormationInfoM.Index(m.battleParams.GetAttackFID())
	if formationConfigInfo == nil {
		panic(fmt.Sprintf("cant find formationID %d", m.battleParams.GetAttackFID()))
	}
	if goxml.IsSeasonFunctionOpen(goxml.GetData(), time.Now().Unix(), formationConfigInfo.FunctionId) {
		return
	}
	if m.battleParams.IsCalAttackPower() {
		m.attack.calPower()
	}
	if m.battleParams.IsCalDefensePower() {
		m.defense.calPower()
	}
}

func (m *Manager) printAltPassiveSkills(altRaisePS *AltRaisePS) {
	if *appsrv.LogLevel == "DEBUG" {
		for pos, ids := range altRaisePS.attack {
			psSkills := make([]uint32, 0, len(ids))
			for _, id := range ids {
				info := goxml.GetData().RaisePassiveSkillInfoM.Index(id)
				if info == nil {
					l4g.Debugf("外部传入被动技能(attak)配置不存在, id:%d", id)
					continue
				}

				if !m.checkFormationGroups(info.FormationGroups) {
					l4g.Debugf("外部传入被动技能(attak)当前阵容不生效, pos:%d, info:%+v", pos, info)
					continue
				}

				psSkills = append(psSkills, info.EffectParam1)
				if info.EffectParam2 > 0 {
					psSkills = append(psSkills, info.EffectParam2)
				}
			}
			l4g.Debugf("外部传入被动技能(attak), pos:%d, psSkills:%v", pos, psSkills)
		}

		for pos, ids := range altRaisePS.defense {
			psSkills := make([]uint32, 0, len(ids))
			for _, id := range ids {
				info := goxml.GetData().RaisePassiveSkillInfoM.Index(id)
				if info == nil {
					l4g.Debugf("外部传入被动技能(defense)配置不存在, id:%d", id)
					continue
				}

				if !m.checkFormationGroups(info.FormationGroups) {
					l4g.Debugf("外部传入被动技能(defense)当前阵容不生效, pos:%d, info:%+v", pos, info)
					continue
				}

				psSkills = append(psSkills, info.EffectParam1)
				if info.EffectParam2 > 0 {
					psSkills = append(psSkills, info.EffectParam2)
				}
			}
			l4g.Debugf("外部传入被动技能(defense), pos:%d, psSkills:%v", pos, psSkills)
		}
	}
}

func (m *Manager) initTeam() {
	attr := m.battleParams.AltAttr
	altRaisePS := m.battleParams.AltRaisePS
	l4g.Debugf("修正属性%+v", attr)
	l4g.Debugf("修正被动%+v", altRaisePS)
	m.printAltPassiveSkills(altRaisePS)

	var attackAltAttr, defenseAltAttr map[uint32]map[int]int64
	var attackAltHp, defenseAltHp map[uint64]int64
	var attackAltRaisePS, defenseAltRaisePS map[uint32][]uint64
	var attackAltPosHp, defenseAltPosHp map[uint32]int64
	var fixMaxHP bool
	if attr != nil {
		attackAltAttr = attr.attack
		defenseAltAttr = attr.defense
		attackAltHp = attr.attackIDHpPct
		defenseAltHp = attr.defenseIDHpPct
		attackAltPosHp = attr.attackPosHpPct
		defenseAltPosHp = attr.defensePosHpPct
		fixMaxHP = attr.fixMaxHP
	}
	if altRaisePS != nil {
		attackAltRaisePS = altRaisePS.attack
		defenseAltRaisePS = altRaisePS.defense
	}

	m.attack.initBase(m, AttackTeam, attackAltAttr, attackAltHp, attackAltRaisePS, attackAltPosHp, fixMaxHP)
	m.defense.initBase(m, DefenseTeam, defenseAltAttr, defenseAltHp, defenseAltRaisePS, defenseAltPosHp, fixMaxHP)
}

func (m *Manager) initTeamPowerSuppress() {
	formationConfigInfo := goxml.GetData().FormationInfoM.Index(m.battleParams.GetAttackFID())
	if formationConfigInfo == nil {
		l4g.Errorf("cant find formationID %d", m.battleParams.GetAttackFID())
		return
	}
	m.GetBattleGlobalValue().powerSuppress =
		goxml.GetData().BattleSuppressInfoM.GetEffectDmgInfo(formationConfigInfo.FunctionId, m.attack.baseInfo.Power, m.defense.baseInfo.Power)
}

func (m *Manager) GetTeamPowerSuppressHurtRate(attack, defense Uniter) int64 {
	if m.GetBattleGlobalValue().powerSuppress != nil {
		attackTeam := m.GetTeam(attack)
		defenseTeam := m.GetTeam(defense)
		if attackTeam.index == defenseTeam.index {
			return BaseFloatInt
		}
		if attackTeam.index == AttackTeam {
			return m.GetBattleGlobalValue().powerSuppress.DmgRate1
		}
		if attackTeam.index == DefenseTeam {
			return m.GetBattleGlobalValue().powerSuppress.DmgRate2
		}
	}
	return BaseFloatInt
}

func (m *Manager) GetReport() *bt.Report {
	if m.report == nil {
		return nil
	}
	return m.report.report
}

// 开始每一回合,从第0回合开始
func (m *Manager) DoRound() {
	m.DoRound0()
	if m.IsFinish() {
		return
	}
	for _, member := range m.members {
		member.attrM.InitInitAttr()
	}
	for i := uint32(1); i <= m.battleParams.GetMaxRound(); i++ {
		m.InitRoundMembers()
		oneRound := NewOneRound(m, i)
		oneRound.DoRun()
		gPool.FreeOneRound(oneRound)
		if m.IsFinish() {
			return
		}
	}
	if m.battleParams.IsPVP() {
		m.PVPJuegeWin()
	}
}

func (m *Manager) PVPJuegeWin() {
	if decimal.NewFromFloat(m.attack.GetLeftHpPct()).Cmp(decimal.NewFromFloat(m.defense.GetLeftHpPct())) >= 0 {
		m.report.SetWin(true)
	}
}

func (m *Manager) SetRound(round uint32) {
	m.battleStatus.round = round
	if m.battleParams.IsPVP() {
		if int64(round) == goxml.GetData().BattleParaInfoM.PVPFixStartRound {
			m.battleGlobalValue.battleCureRate = goxml.GetData().BattleParaInfoM.PVPFixStartCure
			m.battleGlobalValue.battleHurtRate = goxml.GetData().BattleParaInfoM.PVPFixStartHurt
		} else if int64(round) > goxml.GetData().BattleParaInfoM.PVPFixStartRound {
			m.battleGlobalValue.battleCureRate = m.battleGlobalValue.battleCureRate + goxml.GetData().BattleParaInfoM.PVPFixCure
			if m.battleGlobalValue.battleCureRate < 0 {
				m.battleGlobalValue.battleCureRate = 0
			}
			m.battleGlobalValue.battleHurtRate = m.battleGlobalValue.battleHurtRate + goxml.GetData().BattleParaInfoM.PVPFixHurt*(int64(round)-goxml.GetData().BattleParaInfoM.PVPFixStartRound)
			if m.battleGlobalValue.battleHurtRate < 0 {
				m.battleGlobalValue.battleHurtRate = 0
			}
		}
	}
}

func (m *Manager) GetBattleGlobalValue() *ManagerGlobalValue {
	return m.battleGlobalValue
}

func (m *Manager) SetDefenseName(name string) {
	m.defense.SetName(name)
}

func (m *Manager) SetSeasonLevel(lv uint32) {
	m.defense.SetSeasonLv(lv)
}

func (m *Manager) NeedMoreResult() bool {
	return m.battleParams.needMoreResult
}

func (m *Manager) GetMember(id uint32) *Member {
	for _, uniter := range m.unites {
		if uniter.ID() == id {
			return uniter.(*Member)
		}
	}
	return nil
}

func (m *Manager) GetUniter(id uint32) Uniter {
	for _, uniter := range m.unites {
		if uniter.ID() == id {
			return uniter
		}
	}
	return nil
}

// 结束的操作
func (m *Manager) Finish() {
	m.report.AddFinalMember(m)
	if m.NeedMoreResult() {
		m.report.CalResultFinal(m)
	}
	m.report.StaEnd()
	// 最后对胜利方血量进行修正
	m.report.checkFixWinnerFinalHp(m)
}

// 过滤虚拟单位
// 先添加所有非虚拟单位, 如果其中不包含主体boss, 再添加虚拟单位其中一个
func (m *Manager) FilterVoidTargets(oldTargets *Uniters, targets *Uniters, effectType uint32) {
	if effectType != SkillEffectTypeRemoveBuff &&
		effectType != SkillEffectTypeAddBuff &&
		effectType != SkillEffectTypeEffectBuffAttr {
		targets.AddAll(oldTargets)
		return
	}
	var voidMember *VoidMember
	totalNum := oldTargets.GetNum()
	uniterArray := oldTargets.GetUniters()
	for i := 0; i < totalNum; i++ {
		if uniterArray[i].GetUniterType() == UniterTypeVoidMember {
			if voidMember == nil {
				voidMember = uniterArray[i].(*VoidMember)
			}
		} else {
			targets.AddUniter(uniterArray[i])
		}
	}
	if voidMember == nil {
		return
	}
	totalNum = targets.GetNum()
	uniterArray = targets.GetUniters()
	for i := 0; i < totalNum; i++ {
		if uniterArray[i].ID() == voidMember.boss.ID() {
			return
		}
	}
	targets.AddUniter(voidMember)
}

var gDebugFormulaStrs []string

func (m *Manager) CalFormula(attack, defense Uniter, formula uint32, param1, param2, param3 int64,
	iexe IExecution) (int64, bt.FactorType) {
	f := gBattleFormulas[formula]
	if f == nil {
		info := &bt.BaseEffectInfo{}
		iexe.GetExecutionParams(info)
		panic(fmt.Sprintf("battle CalFormula no find formula :%d info:%+v", formula, info))
	}
	result1, result2 := f(m, formula, attack, defense, param1, param2, param3, iexe)
	if df() {
		var attackID, defenseID uint32
		if attack != nil {
			attackID = attack.ID()
		}
		if defense != nil {
			defenseID = defense.ID()
		}
		GenDebugFormula(formula, attackID, defenseID, param1, param2, param3, result1, m)
	}
	return result1, result2
}

func (m *Manager) EffectRemoveBuff(
	attack, defense Uniter, effect uint32, param1, param2, param3, param4 int64, iexe IExecution) (int64, uint32) {

	f := gBattleEffectRemoveBuff[effect]
	if f == nil {
		panic(fmt.Sprintf("battle EffectRemoveBuff no find effect :%d", effect))
	}
	result1, result2 := f(m, effect, attack, defense, param1, param2, param3, param4, iexe)
	if df() {
		var attackID, defenseID uint32
		if attack != nil {
			attackID = attack.ID()
		}
		if defense != nil {
			defenseID = defense.ID()
		}
		GenDebugFormula(effect, attackID, defenseID, param1, param2, param3, result1, m)
	}
	return result1, result2
}

func (m *Manager) EffectBuffAttr(
	attack, defense Uniter, effect uint32, param1, param2, param3 int64, iexe IExecution) (int64, uint32) {

	f := gBattleEffectBuffAttr[effect]
	if f == nil {
		panic(fmt.Sprintf("battle EffectBuffAttr no find effect :%d", effect))
	}
	result1, result2 := f(m, effect, attack, defense, param1, param2, param3, 0, iexe)
	if df() {
		var attackID, defenseID uint32
		if attack != nil {
			attackID = attack.ID()
		}
		if defense != nil {
			defenseID = defense.ID()
		}
		GenDebugFormula(effect, attackID, defenseID, param1, param2, param3, result1, m)
	}
	return result1, result2
}

func (m *Manager) NewAutoID() uint32 {
	m.battleStatus.autoID++
	return m.battleStatus.autoID
}

func (m *Manager) GetRound() uint32 {
	return m.battleStatus.round
}

func (m *Manager) GetActionIndex() uint32 {
	return m.battleStatus.actionIndex
}

func (m *Manager) AddActionIndex() {
	m.battleStatus.actionIndex++
}

func (m *Manager) CheckHit(attack, defense Uniter, iexe IExecution) bool {
	//nolint:mnd
	rate, _ := Formula101(m, 101, attack, defense, 0, 0, 0, iexe)
	return m.CheckRate(rate)
}

// 命中率2: 检查命中率2和闪避率2
func (m *Manager) CheckHit2(attack, defense Uniter, iexe IExecution) bool {
	//nolint:mnd
	rate, _ := Formula120(m, 120, attack, defense, 0, 0, 0, iexe)
	return m.CheckRate(rate)
}

func (m *Manager) CheckCrit(attack, defense Uniter, iexe IExecution) bool {
	//nolint:mnd
	rate, _ := Formula102(m, 102, attack, defense, 0, 0, 0, iexe)
	return m.CheckRate(rate)
}

func (m *Manager) CheckArtifactCrit(attack, defense Uniter, iexe IExecution) bool {
	//nolint:mnd
	rate, _ := Formula154(m, 154, attack, defense, 0, 0, 0, iexe)
	return m.CheckRate(rate)
}

func (m *Manager) CheckArtifactHealCrit(attack, defense Uniter, iexe IExecution) bool {
	//nolint:mnd
	rate, _ := Formula155(m, 155, attack, defense, 0, 0, 0, iexe)
	return m.CheckRate(rate)
}

func (m *Manager) CheckSkillRate(rate int64) bool {
	b := m.CheckRate(rate)
	return b
}

func (m *Manager) CheckPassiveSkillRate(rate int64) bool {
	b := m.CheckRate(rate)
	return b
}

func (m *Manager) CheckRate(rate int64) bool {
	if rate >= BaseFloatInt {
		return true
	}
	return m.rd.SelectByTenTh(uint32(rate))
}

func (m *Manager) Rand(min, max int64) int64 {
	return int64(m.rd.RandBetween(int(min), int(max)))
}

func (m *Manager) NewBuff(attack, defense Uniter, sysBuff *goxml.BuffInfo, sysBuffType *goxml.BuffTypeInfo,
	iexe IExecution, fixValue int64) *Buff {
	newID := m.NewAutoID()
	buff := NewBuff(attack, defense, newID, sysBuff, sysBuffType)
	if fixValue == 0 {
		buff.CalValue(m, iexe)
	} else {
		buff.SetValue(fixValue)
	}
	return buff
}

func (m *Manager) CheckSkillMax(attack, defense Uniter, value, maxDamage int64, defHp, attAtk uint32) int64 {
	if defHp > 0 {
		val := int64(float64(defense.GetAttr(goxml.AttrHp)) * (float64(defHp) / BaseFloat))
		if value > val {
			value = val
		}
	}
	if attAtk > 0 {
		val := int64(float64(attack.GetAttr(goxml.AttrAttack)) * (float64(attAtk) / BaseFloat))
		if value > val {
			value = val
		}
	}
	if maxDamage > 0 && value > maxDamage {
		value = maxDamage
	}
	return value
}

// 死亡
// @param addKillNum bool 是否统计死亡人数
func (m *Manager) Dead(mem *Member, addKillNum bool, triggerEvent bool) {
	if mem.Base.Index == AttackTeam {
		m.attack.Dead(mem, triggerEvent)
		if addKillNum {
			m.attack.DeathTimes++
		}
	} else {
		if addKillNum {
			m.battleStatus.killNum++
			m.defense.DeathTimes++
		}
		m.defense.Dead(mem, triggerEvent)
	}
	m.attack.tUniter.UniterDead(mem)
	m.defense.tUniter.UniterDead(mem)
}

func (m *Manager) Resurrection(mem *Member, triggerEvent bool) {
	if mem.Base.Index == AttackTeam {
		m.attack.Alive(mem, triggerEvent)
		m.attack.ResurrectionTimes++
	} else {
		m.defense.Alive(mem, triggerEvent)
		m.defense.ResurrectionTimes++
	}
	m.attack.tUniter.UniterAlive(mem)
	m.defense.tUniter.UniterAlive(mem)
}

func (m *Manager) IsFinish() bool {
	if m.battleStatus.isFinish {
		return true
	}

	if m.psm.DelayExecute() {
		return false
	}

	if (m.battleParams.GetMaxKill() != 0 && m.battleStatus.killNum >= m.battleParams.GetMaxKill()) ||
		(!m.battleParams.IsNotFinish() && m.defense.IsFinish()) {
		m.report.SetWin(true)
		m.battleStatus.isFinish = true
	} else if m.attack.IsFinish() {
		m.battleStatus.isFinish = true
	}
	return m.battleStatus.isFinish
}

// 第0回合的事情
func (m *Manager) DoRound0() {
	m.NewRound(0)
	//排序触发天赋(被动技能)

	m.TriggerAllPsSkillSpeedFirst(goxml.Event_BattleBeforeInit, m)
	m.TriggerAllPsSkillSpeedFirst(goxml.Event_BattleInit, m)
	m.TriggerAllPsSkillAttackFirst(goxml.Event_BattleBeforePrepare, m)
	m.TriggerAllPsSkillSpeedFirst(goxml.Event_BattlePrepare, m)
}

func (m *Manager) GetOpTeam(mem Uniter) *Team {
	team := m.defense
	if mem.Index() == DefenseTeam {
		team = m.attack
	}
	return team
}

func (m *Manager) GetTeam(mem Uniter) *Team {
	team := m.attack
	if mem.Index() == DefenseTeam {
		team = m.defense
	}
	return team
}

func (m *Manager) GetOpIndex(u Uniter) uint32 {
	index := DefenseTeam
	if u.Index() == DefenseTeam {
		index = AttackTeam
	}
	return index
}

func (m *Manager) GetIndex(mem *Member) uint32 {
	return mem.Index()
}

func GetFormationAltAttr(race map[uint32]uint32) map[int]int64 {
	attr := make(map[int]int64)
	if len(race) == TeamMaxPos {
		race[0] = 1
	}
	for k, v := range race {
		info := goxml.GetData().RaceBuffInfoM.Race(k, v)
		if info != nil {
			if info.Buff1 > 0 {
				attr[int(info.Buff1)] += info.BuffValue1
			}
			if info.Buff2 > 0 {
				attr[int(info.Buff2)] += info.BuffValue2
			}
			if info.Buff3 > 0 {
				attr[int(info.Buff3)] += info.BuffValue3
			}
		}
	}
	return attr
}

func (m *Manager) GetNextSkill() *NextSkill {
	if len(m.nextSkills) > 0 {
		skill := m.nextSkills[0]
		m.nextSkills = m.nextSkills[1:]
		return skill
	}
	return nil
}

// 替换当前的技能
func (m *Manager) ResetNowSkill(id uint32) {
	if m.nowSkill == nil {
		return
	}
	newSkill := m.nowSkill.owner.AddNewSkill(id, goxml.SkillPosOther, nil)
	newSkill.SetPos(m.nowSkill.GetPos())
	m.nowSkill = newSkill
}

func (m *Manager) AddNextSkill(mem Uniter, pse *PsSkillExe, args interface{}, tUniter Uniter) *Skill {
	skillInfo := pse.psSkill.info
	var skill *Skill
	if skillInfo.Info.EffParam4 > 0 {
		skill = mem.GetSkillM().GetSkillByType(skillInfo.Info.EffParam4)
	} else {
		skillID := uint32(skillInfo.Info.EffParam1)
		skill = mem.AddNewSkill(skillID, goxml.SkillPosOther, args)
	}

	if skill == nil {
		l4g.Errorf("psSkill %d NextSkill Erorr", skillInfo.Info.Id)
		return nil
	}

	rate := int32(skillInfo.Info.EffParam2)
	if skillInfo.Info.EffParam3 != 0 {
		rate = rate + int32(m.GetRoundSameNextSkillNum(skill.info.Id)*int(skillInfo.Info.EffParam3))
	}

	m.AddComboSkill(mem, skill, rate, tUniter)
	m.roundAddNextSkills = append(m.roundAddNextSkills, skill)
	return skill
}

func (m *Manager) AddComboSkill(mem Uniter, skill *Skill, nextParam int32, tUniter Uniter) {
	nextSkill := NewNextSkill(skill, nextParam, NextSkillTypeCombo, tUniter)
	m.nextSkills = append(m.nextSkills, nextSkill)
}

func (m *Manager) AddComboNormalUnion(mem Uniter, skill *Skill, nextParam int32, tUniter Uniter) {
	nextSkill := NewNextSkill(skill, nextParam, NextSkillTypeUnion, tUniter)
	m.nextSkills = append(m.nextSkills, nextSkill)
	members := gPool.GetUniters()
	members.AddUniter(mem)
	m.SetNormalSkillBindTargets(members)
}

func (m *Manager) AddFightBack(mem Uniter, skill *Skill, nextParam int32, tUniter Uniter) {
	skill.setBindTarget(mem)
	nextSkill := NewNextSkill(skill, nextParam, NextSkillTypeFightBack, tUniter)
	m.nextSkills = append(m.nextSkills, nextSkill)
}

func (m *Manager) SetNormalSkillBindTargets(targets *Uniters) {
	if m.normalSkillBindTargets != nil {
		gPool.FreeUniters(m.normalSkillBindTargets)
	}
	m.normalSkillBindTargets = targets
}

func (m *Manager) AddResultRoundHurt(attack Uniter, mem Uniter, realValue, showValue int64, iexe IExecution, hurtType bt.SpecialType) {
	if hurtType == bt.SpecialType_HurtTypeHurtSelfDamageToTrigger {
		// 特殊自残，伤害计算到触发者身上
		psE, ok := iexe.(*PsSkillExe)
		if ok {
			m.report.AddBattleSta(psE.GetTriggerUniter(), BattleStaHurt, realValue, 0)
		} else {
			panic(fmt.Sprintf("AddResultRoundHurt: iexe assertion to PsSkillExe error."))
		}
	} else {
		var buffHurtType uint32
		if iexe != nil && iexe.GetExecutionType() == bt.SourceType_SOURCE_BUFF {
			buffType := iexe.GetArgs().(uint32)
			if buffType == BuffTypeCorruption || buffType == BuffTypeReflect {
				buffHurtType = buffType
			}
		}
		m.report.AddBattleSta(attack, BattleStaHurt, realValue, buffHurtType)
	}
	m.report.AddBattleSta(mem, BattleStaBeHurt, realValue, 0)
	if attackTeam := m.GetTeam(attack); attackTeam != nil && attackTeam.IsAttackTeam() {
		m.report.AddHurtShowValue(showValue)
	}

	if m.NeedMoreResult() {
		//材料本特殊需求
		team := m.GetTeam(mem)
		if team.IsDefenseTeam() {
			//防守方被打
			m.report.AddResultRoundHurt(m.GetRound(), realValue)
		}
	}
}

func (m *Manager) Free() {
	gPool.FreeManager(m)
}

// func (m *Manager) FlushDebugInfo() []*bt.DebugInfo {
// 	ret := make([]*bt.DebugInfo, len(m.unites))
// 	i := 0
// 	for _, u := range m.unites {
// 		ret[i] = u.FlushDebugInfo(true)
// 		i++
// 	}
// 	return ret
// }

func (m *Manager) TriggerPsSkill(tUniter Uniter, triggerTime int, args interface{}) {
	if tUniter.GetUniterType() == UniterTypeArtifact {
		//所有行动神器事件的触发,由神器主体目标来触发
		artifact := tUniter.(*Artifact)
		tUniter = artifact.owner
	}

	if tUniter.HasSta(StaHide) || tUniter.HasSta(StaExile) {
		return
	}
	if !tUniter.HasSta(StaSealPsSkill) {
		m.psm.TriggerPsSkill(tUniter, triggerTime, args)
	}
}

func (m *Manager) TriggerBuffEvent(triggerTime int, args interface{}) {
	m.bem.TriggerEvent(m, triggerTime, args)
}

// import 策划说神器没有上阵 也可以给这个位置挂被动技能
func (m *Manager) TriggerAllPsSkillSpeedFirst(triggerTime int, args interface{}) {
	if m.attack.tUniter != nil {
		m.TriggerPsSkill(m.attack.tUniter, triggerTime, args)
	}
	if m.defense.tUniter != nil {
		m.TriggerPsSkill(m.defense.tUniter, triggerTime, args)
	}

	cacheMembers := make([]*Member, 0, len(m.members))
	cacheMembers = append(cacheMembers, m.members...)
	for _, mem := range cacheMembers {
		if mem == nil {
			continue
		}
		m.TriggerPsSkill(mem, triggerTime, args)
	}

	if m.attack.artifactM != nil {
		m.TriggerPsSkill(m.attack.artifactM, triggerTime, args)
	}
	if m.defense.artifactM != nil {
		m.TriggerPsSkill(m.defense.artifactM, triggerTime, args)
	}

	for _, pokemon := range m.attack.pokemons {
		m.TriggerPsSkill(pokemon, triggerTime, args)
	}

	for _, pokemon := range m.defense.pokemons {
		m.TriggerPsSkill(pokemon, triggerTime, args)
	}
}

func (m *Manager) TriggerAllPsSkillAttackFirst(triggerTime int, args interface{}) {
	if m.attack.tUniter != nil {
		m.TriggerPsSkill(m.attack.tUniter, triggerTime, args)
	}
	if m.defense.tUniter != nil {
		m.TriggerPsSkill(m.defense.tUniter, triggerTime, args)
	}

	for _, mem := range m.attack.members {
		m.TriggerPsSkill(mem, triggerTime, args)
	}

	for _, mem := range m.defense.members {
		m.TriggerPsSkill(mem, triggerTime, args)
	}

	if m.attack.artifactM != nil {
		m.TriggerPsSkill(m.attack.artifactM, triggerTime, args)
	}
	if m.defense.artifactM != nil {
		m.TriggerPsSkill(m.defense.artifactM, triggerTime, args)
	}

	for _, pokemon := range m.attack.pokemons {
		m.TriggerPsSkill(pokemon, triggerTime, args)
	}

	for _, pokemon := range m.defense.pokemons {
		m.TriggerPsSkill(pokemon, triggerTime, args)
	}
}

func isSameTeam(attack, defense Uniter) bool {
	return attack.Index() == defense.Index()
}

func DebugPool() {
	gPool.debug()
}

//nolint:varnamelen
func (m *Manager) GetTarget(u, defenseUniter Uniter, target uint32, iexe IExecution, finalTargets *Uniters) {
	switch target {
	case 1: //拥有者
		finalTargets.AddUniter(u)
		return
	default:
	}
	targetConfig := goxml.GetData().SkillTargetInfoM.Index(target)
	if targetConfig == nil {
		panic(fmt.Sprintf("SkillTargetInfo no find target :%d", target))
	}

	baseFilterTargets := gPool.GetUniters()
	firstStepTargets := gPool.GetUniters()
	conditionTargets := gPool.GetUniters()
	defer gPool.FreeUniters(baseFilterTargets)
	defer gPool.FreeUniters(firstStepTargets)
	defer gPool.FreeUniters(conditionTargets)

	gBaseFilter[targetConfig.TargetType](m, u, defenseUniter, iexe, baseFilterTargets)
	gBaseFilterInherit[targetConfig.Inherit](m, u, iexe, firstStepTargets, baseFilterTargets, targetConfig.InheritParam)
	gBaseChooseConditionFunc[targetConfig.Condition](m, u, iexe, targetConfig, conditionTargets, firstStepTargets)
	gBaseChooseSortFunc[targetConfig.Sort](m, u, iexe, conditionTargets, firstStepTargets, targetConfig.SortParam)
	if targetConfig.Sort1 != 0 {
		gBaseChooseSortFunc[targetConfig.Sort1](m, u, iexe, conditionTargets, firstStepTargets, targetConfig.Sort1Param)
	}
	m.GetfinalTargets(targetConfig, conditionTargets, finalTargets)
	gReviseFunc[targetConfig.Added](m, u, iexe, targetConfig, conditionTargets, baseFilterTargets, finalTargets)
}

func (m *Manager) GetfinalTargets(config *goxml.SkillTargetInfo, conditionTargets, finalTargets *Uniters) {
	if config.Count > 0 {
		conditionTargets.CutTo(int(config.Count))
	}
	finalTargets.AddAll(conditionTargets)
}

func (m *Manager) ShufferUniters(uniters *Uniters) {
	m.rd.Shuffle(uniters.GetNum(), func(i, j int) {
		uniters.GetUniters()[i], uniters.GetUniters()[j] = uniters.GetUniters()[j], uniters.GetUniters()[i]
	})
}

func (m *Manager) CheckInForamation(formationGroupId uint32) bool {
	for _, v := range goxml.GetData().FormationGroupInfoM.Index(formationGroupId).FormationIds {
		if v == m.battleParams.attackFID {
			return true
		}
	}
	return false
}

// 战斗外的修正被动技能
// 没有特殊情况 一律挂在队伍的全局对象上
// 没有特殊情况 优先使用attack实现
type AltRaisePS struct {
	attack  map[uint32][]uint64 //pos=>raise passive skill ids
	defense map[uint32][]uint64 //pos=>raise passive skill ids
}

func NewAltRaisePS() *AltRaisePS {
	return &AltRaisePS{}
}

// 修正攻击方的技能
func (a *AltRaisePS) AltAttack(raisePSMap map[uint32][]uint64) {
	if a.attack == nil {
		a.attack = make(map[uint32][]uint64)
	}

	for pos, raisePSs := range raisePSMap {
		for _, psSkill := range raisePSs {
			if psSkill == 0 {
				panic("raisePS has 0")
			}
		}
		a.attack[pos] = append(a.attack[pos], raisePSs...)
	}
}

// 修正攻击方的技能 - 移除技能
// @param uint32 pos 站位
// @param uint64 id 要移除的raisePassviceSkillID
func (a *AltRaisePS) AltAttackRm(pos uint32, id uint64) {
	if id == 0 || a.attack == nil {
		return
	}

	if raisePSs, exist := a.attack[pos]; exist {
		for index, raisePS := range raisePSs {
			if id == raisePS {
				a.attack[pos] = append(a.attack[pos][:index], a.attack[pos][index+1:]...)
				break
			}
		}
	}
}

// 修正防守的技能
func (a *AltRaisePS) AltDefense(raisePSMap map[uint32][]uint64) {
	if a.defense == nil {
		a.defense = make(map[uint32][]uint64)
	}

	for pos, raisePSs := range raisePSMap {
		for _, psSkill := range raisePSs {
			if psSkill == 0 {
				panic("raisePS has 0")
			}
		}
		a.defense[pos] = append(a.defense[pos], raisePSs...)
	}
}

// 修正防守的技能 - 移除技能
// @param uint32 pos 站位
// @param uint64 id 要移除的raisePassviceSkillID
func (a *AltRaisePS) AltDefenseRm(pos uint32, id uint64) {
	if id == 0 || a.defense == nil {
		return
	}

	if raisePSs, exist := a.defense[pos]; exist {
		for index, raisePS := range raisePSs {
			if id == raisePS {
				a.defense[pos] = append(a.defense[pos][:index], a.defense[pos][index+1:]...)
				break
			}
		}
	}
}

func (a *AltRaisePS) Clone() *AltRaisePS {
	clone := &AltRaisePS{
		attack:  make(map[uint32][]uint64, len(a.attack)),
		defense: make(map[uint32][]uint64, len(a.defense)),
	}
	maps.Copy(clone.attack, a.attack)
	maps.Copy(clone.defense, a.defense)
	return clone
}

func (m *Manager) checkFormationGroups(groups []uint32) bool {
	if len(groups) == 0 {
		return true
	}

	var pass bool
	for _, groupID := range groups {
		if m.CheckInForamation(groupID) {
			pass = true
			break
		}
	}
	return pass
}

func (m *Manager) calRaisePsAttr(uniter Uniter) {
	raisePSs := uniter.GetRaisePSs()
	for _, id := range raisePSs {
		sysInfo := goxml.GetData().RaisePassiveSkillInfoM.Index(id)
		if sysInfo == nil {
			panic(fmt.Sprintf("%d raisePSs : %+v \n battle no find raise ps attr:%d", uniter.ID(), raisePSs, id))
		}
		if !m.checkFormationGroups(sysInfo.FormationGroups) {
			continue
		}

		if sysInfo.EffectType == goxml.RaisePsAttrTypePs {
			uniter.AddPsSkill(sysInfo.EffectParam1)
			if sysInfo.EffectParam2 > 0 {
				uniter.AddPsSkill(sysInfo.EffectParam2)
			}
		} else if sysInfo.EffectType == goxml.RaisePsAttrTypeArtifactLink {
			m.GetTeam(uniter).EffectArtifactLinkNum(sysInfo.EffectParam1, sysInfo.EffectParam2, uniter)
		}
	}
	uniter.ClearRaisePSs()
}

func (m *Manager) SettleBuff(buff *Buff, iexe IExecution) {
	value := buff.value * int64(buff.layer)
	targets := gPool.GetUniters()
	defer gPool.FreeUniters(targets)
	m.GetTarget(buff.owner, nil, buff.info.EffectTarget, iexe, targets)
	totalNum := targets.GetNum()
	for i := 0; i < totalNum; i++ {
		uniter := targets.GetUniters()[i]
		if !uniter.CanBeChoose(TargetNeedAlive) {
			continue
		}
		if buff.IsSettleHurt() {
			uniter.Hurted(buff.attack, value, bt.SpecialType_HurtTypeBuff, bt.FactorType_FactorNone, buff)
		} else if buff.IsSettleCure() {
			uniter.Cured(buff.attack, value, bt.SpecialType_HurtTypeBuff, bt.FactorType_FactorNone, buff)
		} else if buff.IsSettleAccumulateHurt() {
			if buff.round <= 0 {
				continue
			}
			value, _ = m.CalFormula(buff.attack, uniter, buff.info.Eff1Formula, int64(buff.round), int64(totalNum), 0, iexe)
			if value > 0 {
				uniter.Hurted(buff.attack, value, bt.SpecialType_HurtTypeBuffShare, bt.FactorType_FactorNone, buff)
			}
		}
	}
}

// 召唤
// @param param2 uint32 角色属性继承万分比（不填则取怪物id属性）
// @param param3 uint32 怪物继承队伍平均攻击力万分比（不填则取怪物id属性）
// @tip param2和param3不能同时存在，否则取param2
func (m *Manager) Call(attack Uniter, pos Uniter, monsterID uint32, param2, param3 uint32, iexe IExecution) {
	member, _ := NewMonster(monsterID, pos.Pos(), false)
	if !member.IsCall() {
		panic(fmt.Sprintf("monster %d cant call", monsterID))
	}
	if param2 > 0 {
		member.InheritAttr(attack, param2)
	} else if param3 > 0 {
		t := m.GetTeam(attack)
		var totalAttack int64
		for _, m := range t.members {
			totalAttack += m.GetAttr(goxml.AttrAttack)
		}
		attrVal := totalAttack / BaseFloatInt * int64(param3)
		member.attrM.baseAttr[goxml.AttrAttack] = attrVal
	}
	team := m.GetTeamByIndex(pos.Index())
	team.AddMember(member, nil, nil, nil, true, false)
	team.addCallCount()
	member.initSkills(true)
	member.SetCallerID(attack.ID())
	m.report.NewCall(iexe, attack, member)
}

// 拘魂
func (m *Manager) StealSoul(attack, defense, callPos Uniter, pse *PsSkillExe) {
	defenseMem, ok := defense.(*Member)
	if !ok {
		panic(fmt.Sprintf("defense %d not a member", defense.ID()))
	}
	member := NewSoulMember(callPos.Pos(), defenseMem)

	// 偷人
	info := pse.psSkill.info.Info
	isStealHero := info.EffParam2 > 0 && info.EffParam3 > 0
	if isStealHero {
		member.attrM.baseAttr[goxml.AttrHp] = int64(float64(member.attrM.baseAttr[goxml.AttrHp]) / BaseFloat * float64(info.EffParam2))
		member.attrM.baseAttr[goxml.AttrAttack] = int64(float64(member.attrM.baseAttr[goxml.AttrAttack]) / BaseFloat * float64(info.EffParam3))
		member.Base.MonsterType = MonsterTypeCallHero
	}

	team := m.GetTeam(callPos)
	team.AddMember(member, nil, nil, nil, true, false)
	member.initSkills(true)
	member.SetCallerID(attack.ID())
	if isStealHero {
		m.TriggerPsSkill(attack, goxml.Event_BeforeStealHero, pse)
	}

	buffID := uint32(info.EffParam1)
	sysBuff := goxml.GetData().BuffInfoM.Index(buffID)
	if sysBuff == nil {
		panic(fmt.Sprintf("battle no find buff:%d", buffID))
	}
	sysBuffType := goxml.GetData().BuffTypeInfoM.Index(sysBuff.Type)
	if sysBuffType == nil {
		panic(fmt.Sprintf("battle no find buff type :%d %d", buffID, sysBuff.Type))
	}
	defense.GetBuffM().successAddBuff(member, defense, pse, sysBuff, sysBuffType, 0)
	m.report.NewCall(pse, attack, member)

	if isStealHero {
		m.TriggerPsSkill(attack, goxml.Event_StealHero, pse)
	}
}

func (m *Manager) InitRoundMembers() {
	m.waitMembers = make([]*Member, len(m.members))
	copy(m.waitMembers, m.members)
	heap.Init(&m.waitMembers)
}

func (m *Manager) GetActMember() *Member {
	for m.waitMembers.Len() > 0 {
		member := heap.Pop(&m.waitMembers).(*Member)
		if member.GetStatus() == UniterDead {
			continue
		}
		return member
	}
	return nil
}

func (m *Manager) getReplaceActMember(member *Member) *Member {
	replaceID, exist := m.actMemberReplace[member.ID()]
	if exist {
		return m.GetMember(replaceID)
	}
	return nil
}

func (m *Manager) setReplaceActMember(srcMemberID, replaceMem uint32) {
	if m.actMemberReplace == nil {
		m.actMemberReplace = make(map[uint32]uint32)
	}
	m.actMemberReplace[srcMemberID] = replaceMem
}

func (m *Manager) AddActMember(addMem *Member) {
	heap.Push(&m.waitMembers, addMem)
	m.members = append(m.members, addMem)
	sort.Slice(m.members, func(i, j int) bool {
		return m.members[i].GetAttr(goxml.AttrSpeed) > m.members[j].GetAttr(goxml.AttrSpeed)
	})
}

func (m *Manager) RemoveActMember(removeMemID uint32) {
	m.members = slices.DeleteFunc(m.members, func(mem *Member) bool {
		return mem.ID() == removeMemID
	})

	for index, mem := range m.waitMembers {
		if mem.ID() == removeMemID {
			heap.Remove(&m.waitMembers, index)
			break
		}
	}
}

func (m *Manager) ClearRoundAddNextSkills() {
	m.roundAddNextSkills = nil
}

func (m *Manager) GetRoundSameNextSkillNum(skillID uint32) int {
	var res int
	for _, skill := range m.roundAddNextSkills {
		if skill.info.Id == skillID {
			res++
		}
	}
	return res
}

// 是否是当前赛季的赛季羁绊
func (m *Manager) IsCurrentSeasonLink(linkId uint32) bool {
	if linkId == 0 {
		return false
	}

	seasonID := m.battleParams.GetSeasonID()
	if seasonID == 0 {
		return false
	}

	datas := goxml.GetData().SeasonLinkMonumentInfoM.GetRecordsBySeasonId(seasonID)
	for _, v := range datas {
		if v.Link == linkId {
			return true
		}
	}
	return false
}
