package platform

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"net/url"
	"sort"
	"strconv"
	"strings"

	"github.com/google/go-querystring/query"
)

func BuildFormSignature(uuid string, timestamp int64, secretKey string) string {
	values := url.Values{}
	values.Add("uuid", uuid)
	values.Add("timestamp", strconv.FormatUint(uint64(timestamp), 10))
	strParams := values.Encode()
	h := hmac.New(sha256.New, []byte(secretKey))
	_, err := h.Write([]byte(strParams))
	if err != nil {
		//l4g.Errorf("write signature failed:%v", err)
		return ""
	}

	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func UrlValues(request interface{}) url.Values {
	if v, err := query.Values(request); err == nil {
		return v
	}
	return nil
}

func HmacMD5(key, data string) string {
	h := hmac.New(md5.New, []byte(key))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

func MD5(data string) string {
	d := []byte(data)
	m := md5.New()
	m.Write(d)
	return hex.EncodeToString(m.Sum(nil))
}

func GetSignStr(values url.Values) string {
	var build strings.Builder
	keys := make([]string, 0, len(values))
	for k, v := range values {
		if len(v) > 0 && v[0] == "" {
			continue
		}
		keys = append(keys, k)
	}

	sort.Strings(keys)
	for _, key := range keys {
		if key != "sign" {
			build.WriteString(key)
			build.WriteString("=")
			build.WriteString(values.Get(key))
			build.WriteString("&")
		}
	}
	return strings.TrimRight(build.String(), "&")
}
