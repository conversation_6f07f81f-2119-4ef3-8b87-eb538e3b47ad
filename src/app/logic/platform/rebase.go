package platform

import (
	"app/goxml"
	"app/protos/in/p2l"
	cret "app/protos/out/ret"
	"bytes"
	"encoding/json"
	l4g "github.com/ivanabc/log4go"
	"io"
)

const (
	RebaseRetOK       = 200
	RebaseServerError = 1001
)

type RebaseGetReq struct {
	Uuid      string `json:"uuid"`
	Id        uint64 `json:"id"`
	OpGroupId uint64 `json:"op_group_id"`
	ServerID  uint64 `json:"server_id"`
}

type RebaseGetData struct {
	Uuid         string `json:"uuid"`
	Id           uint64 `json:"id"`
	OpGroupId    uint64 `json:"op_group_id"`
	ServerID     uint64 `json:"server_id"`
	RechargeNum  uint32 `json:"recharge_num"`
	GameServerID uint64 `json:"game_server_id"`
	GameRecvID   uint64 `json:"game_recv_id"`
	Operated     uint32 `json:"operated"`
}

type RebaseGetResp struct {
	Code       int32          `json:"code"`
	Message    string         `json:"message"`
	Data       *RebaseGetData `json:"data"`
	ServerTime string         `json:"server_time"`
}

func (h *HTTPActor) RebaseGet(recv *p2l.L2P_RebaseGet) *p2l.P2L_RebaseGet {
	ret := &p2l.P2L_RebaseGet{}
	if h.ServerType() != goxml.SERVER_TYPE_CN {
		l4g.Errorf("uuid:%d uid:%d [RebaseGet] not cn server", recv.Uuid, recv.Id)
		ret.Ret = uint32(cret.RET_ERROR)
		return ret
	}

	c := h.m.httpClient()

	rebaseUrl := h.RebaseUrl()

	req := &RebaseGetReq{
		Uuid:      recv.Uuid,
		Id:        recv.Id,
		OpGroupId: h.OpGroup(),
		ServerID:  h.ServerID(),
	}

	targetUrl := rebaseUrl

	byteData, err := json.Marshal(req)
	if err != nil {
		l4g.Errorf("[RebaseGet] req:%+v err:%s", req, err)
		ret.Ret = uint32(cret.RET_REBASE_JASON_MARSHAL_FAIL)
		return ret
	}

	l4g.Debugf("[RebaseGet] targetUrl:%s", targetUrl)
	raw, err := c.Post(targetUrl, "application/json", bytes.NewReader(byteData))
	if err != nil {
		l4g.Errorf("[RebaseGet] user:%s post platform error:%v", recv.Uuid, err)
		ret.Ret = uint32(cret.RET_REBASE_SERVER_ERROR)
		return ret
	}
	defer raw.Body.Close()

	respBytes, err := io.ReadAll(raw.Body)
	if err != nil {
		l4g.Errorf("[RebaseGet] user:%s read from body error:%v", recv.Uuid, err)
		ret.Ret = uint32(cret.RET_REBASE_SERVER_ERROR)
		return ret
	}

	resp := &RebaseGetResp{}
	l4g.Debugf("[RebaseGet] user:%s resp:%+v", recv.Uuid, string(respBytes))
	if err := json.Unmarshal(respBytes, resp); err != nil {
		l4g.Errorf("[RebaseGet] user:%s unmarshal bytes(%s) error:%v", recv.Uuid, string(respBytes), err)
		ret.Ret = uint32(cret.RET_REBASE_SERVER_ERROR)
		return ret
	}
	l4g.Debugf("RebaseGetData resp:%+v", resp)

	switch resp.Code {
	case RebaseRetOK:
		if resp.Data != nil {
			ret.Uuid = resp.Data.Uuid
			ret.Id = resp.Data.Id
			ret.RechargeNum = resp.Data.RechargeNum
			ret.GameRecvId = resp.Data.GameRecvID
			ret.GameServer = resp.Data.GameServerID
			ret.Operated = resp.Data.Operated
		}
		ret.Ret = uint32(cret.RET_OK)
	case RebaseServerError:
		ret.Ret = uint32(cret.RET_REBASE_SERVER_ERROR)
	}

	return ret
}
