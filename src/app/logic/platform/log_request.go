package platform

import (
	"app/protos/out/cl"
	"bytes"
	"io"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func (h *HTTPActor) SearchLogData(req *cl.LogQueryReq) *cl.LogQueryRsp {
	rsp := &cl.LogQueryRsp{}
	c := h.m.httpClient()

	reqBytes, err := proto.Marshal(req)
	if err != nil {
		rsp.Err = err.Error()
		return rsp
	}
	reader := bytes.NewReader(reqBytes)
	targetUrl := h.SearchLogURL()
	resp, err := c.Post(targetUrl, "application/octet-stream", reader)
	if err != nil {
		rsp.Err = err.Error()
		return rsp
	}
	defer resp.Body.Close()

	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		rsp.Err = err.Error()
		return rsp
	}
	l4g.Debugf("[GetLogData] resp:%+v", string(respBytes))

	err = proto.Unmarshal(respBytes, rsp)
	if err != nil {
		return rsp
	}
	return rsp
}
