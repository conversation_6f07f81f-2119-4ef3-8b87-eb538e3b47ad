package platform

import (
	"app/goxml"
	"app/protos/in/p2l"
	"app/protos/out/ret"
	"bytes"
	"encoding/json"
	"io"

	l4g "github.com/ivanabc/log4go"

	"strconv"
)

const (
	RetOK             = 200  //ok
	RetServiceError   = 2005 //礼品码服务器暂不可用 请稍后再试
	RetNoFind         = 2006 //您输入的礼品码不正确
	RetClosed         = 2007 //您输入的礼品码已失效
	RetTimeError      = 2008 //您输入的礼品码已过期
	RetUsed           = 2009 //您已使用过该类型礼品码
	RetCodeUsed       = 2010 //您输入的礼品码已被使用
	RetFreqError      = 2011 //您的请求过于频繁 请稍后再试
	RetKeyError       = 2012 //秘钥异常
	RetGameIDError    = 2013 //该礼品码非对应游戏异常
	RetOpIDError      = 2014 //该运营商不可使用此礼包码
	RetChannelIDError = 2015 //该二级运营商不可使用此礼包码
	RetServerIdError  = 2016 //该服务器不可使用此礼品码
)

const (
	PlatRetUnKnowErr  = -1   //未知错误
	PlatRetOK         = 0    //ok
	PlatRetCodeNil    = 1001 //礼包码为空
	PlatRetUerNil     = 1002 //用户信息为空
	PlatRetServerNil  = 1003 //serverId为空
	PlatRetOpGroupNil = 1004 //参数 op_group 为空
	// PlatRetUsualCodeHasUse             = 2001 //通用兑奖码已经被自己使用
	PlatRetUsualCodeOverUseCount       = 2002 //通用兑奖码已经超出使用限制
	PlatRetUsualCodeExpireOrNotExist   = 2003 //通用兑奖码不存在或者活动已经过期
	PlatRetUsualCodeClosed             = 2004 //通用兑奖码已经作废
	PlatRetSpecialCodeFormatError      = 3001 //专用兑奖码格式不正确，无法从中获取到batch_id
	PlatRetSpecialCodeHasUse           = 3002 //专用兑奖码已经被使用
	PlatRetSpecialCodeOverUseCount     = 3003 //该用户的专用兑奖码使用次数上限已经达到
	PlatRetSpecialCodeExpireOrNotExist = 3004 //专用兑奖码不存在或者活动已经过期
	PlatRetSpecialCodeNotExist         = 3005 //专用礼包码不存在
	PlatRetSpecialCodeClosed           = 3006 //专用礼包码已作废
	PlatRetSpecialCodeExpire           = 3010 //专用礼包码已经过期
	PlatRetOpGroupError                = 4001 //运营组不符合
)

type GiftCodeData struct {
	ID string `json:"act_id"` // 欧美老的接口返回
	Id uint32 `json:"reward_id"`
}

type GiftCodeResp struct {
	Code       int32         `json:"code"`
	Message    string        `json:"message"`
	Data       *GiftCodeData `json:"data"`
	ServerTime string        `json:"server_time"`
}

type giftCodeServerReq struct {
	Code     string `json:"code"`
	ServerId uint64 `json:"server"`
	Uuid     string `json:"uuid"`
	Op       uint32 `json:"op"`
	Channel  uint32 `json:"channel"`
}

//nolint:varnamelen
func (h *HTTPActor) UseGiftCode(recv *p2l.L2P_UseGiftCode) (uint32, uint32) {
	c := h.m.httpClient()

	info := make(map[string]string)
	info["server"] = strconv.FormatUint(recv.ServerId, 10)
	info["code"] = recv.Code
	info["user"] = strconv.FormatUint(recv.Uid, 10)
	info["op"] = strconv.FormatUint(uint64(recv.OpId), 10)
	info["op_group"] = strconv.FormatUint(recv.OpGroup, 10)

	byteData, err := json.Marshal(info)
	if err != nil {
		l4g.Errorf("[GiftCode] user:%s json marshal error:%v", recv.Uuid, err)
		return uint32(ret.RET_GIFT_CODE_SERVICE_ERROR), 0
	}

	codeUrl := h.GiftCodeURL()
	proxyUrl := h.WebProxyUrl()

	var targetUrl string
	if len(proxyUrl) == 0 {
		targetUrl = codeUrl
	} else {
		targetUrl = proxyUrl + "?targetUrl=" + codeUrl
	}

	if h.ServerType() == goxml.SERVER_TYPE_CN {
		req := &giftCodeServerReq{
			Code:     recv.Code,
			ServerId: recv.ServerId,
			Uuid:     recv.Uuid,
			Op:       recv.OpId,
			Channel:  recv.Channel,
		}
		byteData, err = json.Marshal(req)
		if err != nil {
			l4g.Errorf("[GiftCode] user:%s json marshal error:%v", recv.Uuid, err)
			return uint32(ret.RET_GIFT_CODE_SERVICE_ERROR), 0
		}
		targetUrl = codeUrl
	}

	raw, err := c.Post(targetUrl, "application/json", bytes.NewReader(byteData))
	if err != nil {
		l4g.Errorf("[GiftCode] user:%s post platform error:%v", recv.Uuid, err)
		return uint32(ret.RET_GIFT_CODE_SERVICE_ERROR), 0
	}
	defer raw.Body.Close()

	bytes, err := io.ReadAll(raw.Body)
	if err != nil {
		l4g.Errorf("[GiftCode] user:%s read from body error:%v", recv.Uuid, err)
		return uint32(ret.RET_GIFT_CODE_SERVICE_ERROR), 0
	}

	resp := &GiftCodeResp{}
	l4g.Debugf("[GiftCode] user:%s resp:%+v", recv.Uuid, string(bytes))
	if err := json.Unmarshal(bytes, resp); err != nil {
		l4g.Errorf("[GiftCode] user:%s unmarshal bytes(%s) error:%v", recv.Uuid, string(bytes), err)
		return uint32(ret.RET_GIFT_CODE_SERVICE_ERROR), 0
	}

	if resp.Code != PlatRetOK && resp.Code != RetOK {
		l4g.Errorf("[GiftCode] user:%s code:%s platform return error:%d", recv.Uuid, recv.Code, resp.Code)
		var retCode uint32
		if h.ServerType() == goxml.SERVER_TYPE_CN {
			retCode = uint32(convertCode(resp.Code))
		} else {
			retCode = uint32(platCode2LogicCode(resp.Code))
		}
		return retCode, 0
	}

	if resp.Data == nil {
		l4g.Error("[GiftCode] user:%s resp.Data is nil. bytes(%s)", recv.Uuid, string(bytes))
		return uint32(ret.RET_GIFT_CODE_SERVICE_ERROR), 0
	}

	var awardID int64
	if h.ServerType() == goxml.SERVER_TYPE_CN {
		awardID = int64(resp.Data.Id)
	} else {
		awardID, err = strconv.ParseInt(resp.Data.ID, 0, 32)
		if err != nil {
			l4g.Errorf("[GiftCode] user:%s code:%s platform return award id error:%s", recv.Uuid, recv.Code, err)
			return uint32(ret.RET_GIFT_CODE_SERVICE_ERROR), 0
		}
	}
	return uint32(ret.RET_OK), uint32(awardID)
}

func convertCode(code int32) ret.RET {
	// 运营要求先只使用这个错误提示，后续客诉多再修改
	return ret.RET_GIFT_CODE_USE_ERROR
	/*
		switch code {
		case RetServiceError:
			return ret.RET_GIFT_CODE_SERVICE_ERROR
		case RetNoFind, RetGameIDError:
			return ret.RET_GIFT_CODE_ILLEGAL
		case RetClosed:
			return ret.RET_GIFT_CODE_CLOSED
		case RetTimeError:
			return ret.RET_GIFT_CODE_EXPIRE
		case RetUsed:
			return ret.RET_GIFT_CODE_USED_SELF
		case RetCodeUsed:
			return ret.RET_GIFT_CODE_USED_OTHERS
		case RetFreqError:
			return ret.RET_GIFT_CODE_FREQUENT
		case RetKeyError:
			return ret.RET_GIFT_CODE_SERVICE_ERROR
		case RetOpIDError:
			return ret.RET_GIFT_CODE_ILLEGAL
		case RetChannelIDError:
			return ret.RET_GIFT_CODE_ILLEGAL
		}
		return ret.RET_ERROR
	*/
}

func platCode2LogicCode(code int32) ret.RET {
	switch code {
	case PlatRetUnKnowErr:
		return ret.RET_GIFT_CODE_SERVICE_ERROR
	case PlatRetCodeNil, PlatRetUerNil, PlatRetServerNil, PlatRetOpGroupNil:
		return ret.RET_GIFT_CODE_ILLEGAL
	case PlatRetUsualCodeOverUseCount:
		return ret.RET_GIFT_CODE_USED_SELF
	case PlatRetUsualCodeExpireOrNotExist, PlatRetSpecialCodeFormatError:
		return ret.RET_GIFT_CODE_ILLEGAL
	case PlatRetSpecialCodeHasUse:
		return ret.RET_GIFT_CODE_USED_OTHERS
	case PlatRetSpecialCodeOverUseCount:
		return ret.RET_GIFT_CODE_USED_SELF
	case PlatRetSpecialCodeExpireOrNotExist:
		return ret.RET_GIFT_CODE_ILLEGAL
	case PlatRetSpecialCodeNotExist, PlatRetSpecialCodeExpire:
		return ret.RET_GIFT_CODE_ILLEGAL
	case PlatRetOpGroupError:
		return ret.RET_GIFT_CODE_ILLEGAL
	case PlatRetUsualCodeClosed, PlatRetSpecialCodeClosed:
		return ret.RET_GIFT_CODE_CLOSED
	}
	return ret.RET_ERROR
}
