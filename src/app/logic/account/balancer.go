//nolint:staticcheck //这里需要改成v2 api
package account

import (
	"context"
	"sync"

	l4g "github.com/ivanabc/log4go"
	"google.golang.org/grpc/balancer"
	"google.golang.org/grpc/balancer/base"
	"google.golang.org/grpc/resolver"
)

// Name is the name of weighted round robin balancer.
const BalancerName = "pick_by_service_id"

// newBuilder creates a new weighted round robin balancer builder.
func newBuilder(serverID uint64) balancer.Builder {
	return base.NewBalancerBuilder(BalancerName, &logicPickerBuilder{serverID})
}

type logicPickerBuilder struct {
	serverID uint64
}

func (p *logicPickerBuilder) Build(readySCs map[resolver.Address]balancer.SubConn) balancer.Picker {
	if len(readySCs) == 0 {
		return base.NewErrPicker(balancer.ErrNoSubConnAvailable)
	}

	var scs []balancer.SubConn
	for _, subConn := range readySCs {
		scs = append(scs, subConn)
	}

	index := int((p.serverID) % uint64(len(scs)))
	l4g.Info("logicPickerBuilder serverid:%d scs:%d sckInfo:%+v index:%+v", p.serverID, len(scs), scs, index)

	return &logicPicker{
		subConns: scs,
		next:     index,
	}
}

type logicPicker struct {
	// subConns is the snapshot of the roundrobin balancer when this picker was
	// created. The slice is immutable. Each Get() will do a round robin
	// selection from it and return the selected SubConn.
	subConns []balancer.SubConn

	//其实这里也可以不用所 因为不会修改next
	mu   sync.RWMutex
	next int
}

func (p *logicPicker) Pick(ctx context.Context, opts balancer.PickOptions) (balancer.SubConn, func(balancer.DoneInfo), error) {
	p.mu.RLock()
	sc := p.subConns[p.next]
	p.mu.RUnlock()
	//l4g.Debug("logicPickerBuilder next:%d, sc:%+v", p.next, sc)
	return sc, nil, nil

}
