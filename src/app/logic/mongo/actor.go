package mongo

import (
	"context"

	kv "app/protos/in/l2m"

	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

const (
	grpcTimeout        = 3 * time.Second
	queueHighWaterMark = 1000
)

type Actor struct {
	*actor.Actor
	ctx    *ctx.Group
	srvCtx context.Context

	client kv.MongoClient

	m *Manager
}

func NewActor(srvCtx context.Context, ctx *ctx.Group, cfg *actor.Config, m *Manager) *Actor {
	act := actor.NewActor(ctx.CreateChild(), cfg)
	accActor := &Actor{
		Actor:  act,
		ctx:    ctx,
		srvCtx: srvCtx,
		m:      m,
	}
	accActor.client = kv.NewMongoClient(m.conn)
	act.TickerCB = func(int64) {
		if qLen := act.MsgQSize(); qLen > queueHighWaterMark {
			l4g.Errorf("account msg queue in highwater : %d", qLen)
		}
	}
	return accActor
}

func (a *Actor) Client() kv.MongoClient { return a.client }

func (a *Actor) Close() {
	a.ctx.Stop()
	a.ctx.Wait()
	a.ctx.Finish()
	l4g.Infof("account actor close...")
}
