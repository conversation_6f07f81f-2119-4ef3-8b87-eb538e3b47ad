package character

import (
	"app/goxml"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
)

type GST struct {
	owner *User

	//data *cl.GSTBossUser
}

func newGST(user *User) *GST {
	return &GST{
		owner: user,
	}
}

func (g *GST) Owner() *User {
	return g.owner
}

//func (g *GST) load(data *cl.GSTBossUser) {
//	g.data = data
//	if g.data == nil {
//		g.data = &cl.GSTBossUser{}
//	}
//}

//func (g *GST) Save() {
//	g.Owner().dbUser.Module6.BossUser = g.data
//	g.Owner().setSaveTag(saveTagModule6)
//}

//func (g *GST) Flush() *cl.GSTBossUser {
//	return g.data.Clone()
//}

//func (g *GST) GetRefreshTime() uint32 {
//	return g.data.ChallengeRecoverTime
//}

//func (g *GST) SetRefreshTime(recoverTime uint32) {
//	g.data.ChallengeRecoverTime = recoverTime
//	g.Save()
//}

//func (g *GST) getChallengeNum() uint32 {
//	challengeItem := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeItem
//	return uint32(g.owner.tokensBag()[challengeItem.Value])
//}

// 挑战令恢复
//func (g *GST) RecoverChallengeItem(srv servicer) uint32 {
//	refreshTime := g.GetRefreshTime()
//	if refreshTime <= 0 {
//		return uint32(cret.RET_GST_BOSS_USER_NOT_SIGN)
//	}
//	//判断token余量
//	maxNum := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeMaximum
//	curNum := g.getChallengeNum()
//	freeNum := uint32(0)
//	if maxNum > curNum {
//		freeNum = maxNum - curNum
//	}
//	now := uint32(time.Now().Unix())
//	if freeNum <= 0 {
//		g.SetRefreshTime(now)
//		return uint32(cret.RET_OK)
//	}
//	//按时间恢复
//	recoverTime := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeRecoverTime
//	if refreshTime+recoverTime >= now {
//		return uint32(cret.RET_OK)
//	}
//	recoverNum := (now - refreshTime) / recoverTime
//	refreshTime += recoverTime * recoverNum
//	//修正恢复数量
//	if recoverNum > freeNum {
//		recoverNum = freeNum
//	}
//	//发放道具
//	challengeItem := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeItem
//	resource := goxml.GenSimpleResource(challengeItem.Type, challengeItem.Value, recoverNum)
//	ret, _ := g.Owner().Award(srv, []*cl.Resource{resource}, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GST_BOSS_CHALLENGE_RECOVER), 0)
//	if ret != uint32(cret.RET_OK) {
//		l4g.Errorf("user: %d BoxExchange: gst boss revover challenge error. recoverNum:%d, refreshTime:%d", g.Owner().ID(), recoverNum, refreshTime)
//		return ret
//	}
//	g.SetRefreshTime(refreshTime)
//
//	return uint32(cret.RET_OK)
//}

//func (g *GST) SignUser(srv servicer) {
//	if g.GetRefreshTime() > 0 {
//		return
//	}
//	//发放初始挑战令
//	challengeItem := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeItem
//	initNumber := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeInitialNumber
//	award := goxml.GenSimpleResource(challengeItem.Type, challengeItem.Value, initNumber)
//	ret, _ := g.Owner().Award(srv, []*cl.Resource{award}, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GST_BOSS_INIT_CHALLENGE), 0)
//	if ret != uint32(cret.RET_OK) {
//		l4g.Errorf("user: %d initial challenge fail. award:%s", g.Owner().ID(), award)
//		return
//	}
//
//	g.SetRefreshTime(uint32(time.Now().Unix()))
//}

func (g *GST) OnSeasonEnd(srv servicer, needSettleAward bool) {
	g.clearBuild(srv)
	g.clearOre(srv, uint32(log.RESOURCE_CHANGE_REASON_GST_ORE_SEASON_RESET))
	g.clearFormation()
}

func (g *GST) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	g.initBuild(srv)
	g.initOre(srv)
}

func (g *GST) clearBuild(srv servicer) {
	buildResource := goxml.GetData().GuildSandTableConfigInfoM.SeasonGstBuildResource()
	costResource := make([]*cl.Resource, 0)
	for tokenID := range buildResource {
		userCount := g.owner.GetTokenCount(tokenID, srv)
		if userCount > 0 {
			costResource = append(costResource, &cl.Resource{
				Type:  uint32(common.RESOURCE_TOKEN),
				Value: tokenID,
				Count: uint32(userCount),
			})
		}
	}
	if len(costResource) > 0 {
		g.owner.Consume(srv, costResource, uint32(log.RESOURCE_CHANGE_REASON_GST_BUILD_SEASON_RESET), 0)
	}
}

func (g *GST) clearOre(srv servicer, reason uint32) {
	oreResource := goxml.GetData().GuildSandTableConfigInfoM.OreSeasonInitItem
	userCount := g.owner.GetTokenCount(oreResource.Value, srv)
	if userCount > 0 {
		costResource := make([]*cl.Resource, 0)
		costResource = append(costResource, &cl.Resource{
			Type:  uint32(common.RESOURCE_TOKEN),
			Value: oreResource.Value,
			Count: uint32(userCount),
		})
		g.owner.Consume(srv, costResource, reason, 0)
	}
}

func (g *GST) initBuild(srv servicer) {
	buildResource := goxml.GetData().GuildSandTableConfigInfoM.SeasonGstBuildResource()
	addResource := make([]*cl.Resource, 0)
	for tokenID, count := range buildResource {
		userCount := g.owner.GetTokenCount(tokenID, srv)
		if userCount < count {
			addResource = append(addResource, &cl.Resource{
				Type:  uint32(common.RESOURCE_TOKEN),
				Value: tokenID,
				Count: uint32(count),
			})
		}
	}
	if len(addResource) > 0 {
		g.owner.Award(srv, addResource, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GST_BUILD_SEASON_RESET), 0)
	}
}

func (g *GST) initOre(srv servicer) {
	addResource := make([]*cl.Resource, 0)
	addResource = append(addResource, goxml.GetData().GuildSandTableConfigInfoM.OreSeasonInitItem.Clone())
	g.owner.Award(srv, addResource, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GST_ORE_SEASON_RESET), 0)
}

func (g *GST) cleanOreItemOnGuildQuit(srv servicer) {
	g.clearOre(srv, uint32(log.SUB_TYPE_ID_GUILD_QUIT))
}

func (g *GST) clearFormation() {
	formationM := g.owner.FormationManager()
	for id := uint32(common.FORMATION_ID_FI_GST_BOSS_1); id <= uint32(common.FORMATION_ID_FI_GST_BOSS_8); id++ {
		formationM.Delete(id)
	}
	for id := uint32(common.FORMATION_ID_FI_GST_DRAGON_1); id <= uint32(common.FORMATION_ID_FI_GST_DRAGON_5); id++ {
		formationM.Delete(id)
	}
}
