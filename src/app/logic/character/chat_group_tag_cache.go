package character

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
)

type ChatGroupTag struct {
	owner *User
	data  *db.ChatGroupTag
}

func newChatGroupTag(user *User) *ChatGroupTag {
	return &ChatGroupTag{
		owner: user,
	}
}

func (c *ChatGroupTag) load(data *db.ChatGroupTag) {
	c.data = data
}

func (c *ChatGroupTag) getArea() uint32 {
	if c.data == nil {
		return 0
	}

	return c.data.ChatGroupArea
}

func (c *ChatGroupTag) setArea(area uint32) {
	if c.data == nil {
		c.data = &db.ChatGroupTag{}
	}
	c.data.ChatGroupArea = area
	c.save()
}

func (c *ChatGroupTag) save() {
	c.owner.dbUser.Module4.ChatGroupTag = c.data
	c.owner.setSaveTag(saveTagModule4)
}

func (c *ChatGroupTag) isUpdateCrossArea(srv servicer) bool {
	if !c.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_CHAT_PARTITION), srv) {
		return false
	}

	if srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)) == 0 || c.getArea() == srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)) {
		return false
	}

	return true
}

func (c *ChatGroupTag) UpdateCrossArea(crossArea uint32) bool {
	if crossArea == 0 || c.getArea() == crossArea {
		return false
	}
	c.setArea(crossArea)

	return true
}

func (c *ChatGroupTag) getGroupTags() map[string]bool {
	if c.data == nil {
		return nil
	}

	return c.data.ChatGroupTags
}

func (c *ChatGroupTag) isExistNoSyncGroupTag(guildID uint64) bool {
	tags := goxml.GetData().ChatInfoM.GetAllGroupTag()
	for _, tag := range tags {
		if _, ok := c.getGroupTags()[tag]; !ok {
			if tag == goxml.GetData().ChatInfoM.ChatGuildGroupTag {
				if guildID == 0 {
					continue
				} else {
					return true
				}
			}

			return true
		}
	}

	return false
}

func (c *ChatGroupTag) AddGroupTag(tags []string) {
	if len(tags) == 0 {
		return
	}
	if c.data == nil {
		c.data = &db.ChatGroupTag{}
	}
	if c.data.ChatGroupTags == nil {
		c.data.ChatGroupTags = make(map[string]bool)
	}

	for _, tag := range tags {
		c.data.ChatGroupTags[tag] = true
	}

	c.save()
}

func (c *ChatGroupTag) DelGroupTag(tags []string) {
	if len(tags) == 0 || c.data == nil {
		return
	}

	for _, tag := range tags {
		delete(c.data.ChatGroupTags, tag)
	}

	c.save()
}

func (c *ChatGroupTag) isChangeLangMaxPeople() bool {
	if c.data == nil || c.data.MaxPeoples == nil {
		return true
	}
	lang := c.owner.GetLang()
	if maxPeople, exist := c.data.MaxPeoples[lang]; exist {
		langInfo := goxml.GetData().ChatLanguageInfoM.GetLanguageInfo(lang)
		if langInfo == nil {
			l4g.Errorf("user: %d isChangeLangMaxPeople: chatLangInfo is nil. lang: %s", c.owner.ID(), lang)
			return false
		}
		if langInfo.PeopleMax == maxPeople {
			return false
		}
	}

	return true
}

func (c *ChatGroupTag) UpdateLangMaxPeople(lang string) {
	if lang == "" {
		return
	}

	langInfo := goxml.GetData().ChatLanguageInfoM.GetLanguageInfo(lang)
	if langInfo == nil {
		l4g.Errorf("user: %d UpdateLangMaxPeople: chatLangInfo is nil. lang: %s", c.owner.ID(), lang)
		return
	}

	if maxPeople, exist := c.data.MaxPeoples[lang]; exist {
		if maxPeople == langInfo.PeopleMax {
			return
		}
	}
	if c.data.MaxPeoples == nil {
		c.data.MaxPeoples = make(map[string]uint32)
	}
	c.data.MaxPeoples[lang] = langInfo.PeopleMax
	c.save()
}

func (c *ChatGroupTag) checkUpdateGuildGroupTag(guildID uint64) {
	if c.data == nil {
		return
	}

	// 需要同步公会聊天组，所以需要把已存在的 guild groupTag给删除
	if guildID == 0 || guildID != c.data.GuildId {
		c.DelGroupTag([]string{goxml.GetData().ChatInfoM.ChatGuildGroupTag})
	}
}

func (c *ChatGroupTag) isChangeGuildID(guildID uint64) bool {
	if c.data == nil {
		return true
	}

	if guildID == 0 {
		return false
	}

	if guildID != c.data.GuildId {
		return true
	}

	return false
}

func (c *ChatGroupTag) UpdateGuildID(guildID uint64) {
	if c.data == nil {
		c.data = &db.ChatGroupTag{}
	}
	c.data.GuildId = guildID
	c.save()
}

func (c *ChatGroupTag) GetCacheGuildID() uint64 {
	if c.data == nil {
		return 0
	}

	return c.data.GuildId
}
