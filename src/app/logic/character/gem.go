package character

import (
	"app/goxml"
	"app/protos/out/cl"
)

type Gem struct {
	data *cl.GemInfo // 宝石相关数据
}

func newGem(srv servicer, res *cl.Resource, gemInfo *goxml.GemInfoExt) *Gem {
	gem := &cl.GemInfo{
		Id:    res.Id,
		SysId: res.Value,
		GemAttr: &cl.GemAttr{
			AttrId: make([]uint32, 0, gemInfo.AdvanceAttrNum*2),
		},
	}
	// 继承的属性
	for _, attr := range res.Attrs {
		gem.GemAttr.AttrId = append(gem.GemAttr.AttrId, uint32(attr.Value))
	}
	// 当继承的属性条数＜宝石的随机高级属性条数时，除了继承属性外的其余高级属性进行随机生成
	attrs := genAdvancedAttr(srv, res.Value, gem.GemAttr.AttrId, gemInfo)
	gem.GemAttr.AttrId = append(gem.GemAttr.AttrId, attrs...)
	// 宝石的基础属性
	attrs = goxml.GetData().GemAttrInfoM.GenBasicAttrs(srv.Rand(), gemInfo.BasicAttrGroup)
	gem.GemAttr.AttrId = append(gem.GemAttr.AttrId, attrs...)

	return &Gem{data: gem}
}

func (g *Gem) GetData() *cl.GemInfo {
	return g.data
}

// 获取单个宝石
func (g *Gem) flush() *cl.GemInfo {
	if g != nil && g.data != nil {
		return g.data.Clone()
	}

	return nil
}

// SetGemHeroID : 设置宝石身上的hero id
func (g *Gem) SetGemHeroID(heroID uint64) {
	g.data.HeroId = heroID
}

// SetGemTmpAttr : 设置宝石身上的临时属性
// param@ changeAttrs: key: attrID  value: 原属性切片需要被重新随机属性的index
func (g *Gem) SetGemTmpAttr(changeAttrs map[int]uint32) {
	if g.data.TmpGemAttr == nil {
		g.data.TmpGemAttr = &cl.GemAttr{
			AttrId: make([]uint32, 0, len(g.data.GetGemAttr().AttrId)),
		}
	} else {
		g.data.TmpGemAttr.AttrId = g.data.TmpGemAttr.AttrId[:0]
	}

	g.data.TmpGemAttr.AttrId = append(g.data.TmpGemAttr.AttrId, g.data.GemAttr.AttrId...)

	for index, attr := range changeAttrs {
		if len(g.data.TmpGemAttr.AttrId) > index {
			g.data.TmpGemAttr.AttrId[index] = attr
		}
	}
}

// SetGemAttr : 设置宝石身上的属性: 把宝石的临时属性变成永久属性
func (g *Gem) SetGemAttr() {
	*g.data.GemAttr = *g.data.TmpGemAttr
	g.data.TmpGemAttr = nil
}

// MergeGemAttr : 合并宝石的属性: L2C_OpResources用
func (g *Gem) MergeGemAttr() []*cl.Attr {
	attrs := make([]*cl.Attr, 0, len(g.data.GemAttr.AttrId))
	for _, attrID := range g.data.GemAttr.AttrId {
		attrs = append(attrs, &cl.Attr{
			Value: int64(attrID),
		})
	}

	return attrs
}

// ConvertAdvancedAttr : 置换宝石高级属性
func (g *Gem) ConvertAdvancedAttr(srv servicer, lockAttrs []uint32, srcAdvancedAttrs map[uint32]int) map[int]uint32 {
	gemInfo := goxml.GetData().GemInfoM.Index(g.data.SysId)
	if gemInfo != nil {
		// 需要保证锁住属性的原位置，所有获取需要改变属性的位置，并赋值
		changeIndex := getNeedChangeAttrIndex(lockAttrs, srcAdvancedAttrs)
		newAttrs := genAdvancedAttr(srv, g.GetSysID(), lockAttrs, gemInfo)
		if len(newAttrs) == 0 {
			return nil
		}
		setChangeAttr(newAttrs, changeIndex)

		return changeIndex
	}

	return nil
}

func (g *Gem) GetSysID() uint32 {
	return g.data.SysId
}

func (g *Gem) GetAdvancedAttr() []uint32 {
	attrs := make([]uint32, 0, len(g.data.GemAttr.GetAttrId()))
	for _, id := range g.data.GemAttr.GetAttrId() {
		gemAttr := goxml.GetData().GemAttrInfoM.Index(id)
		if gemAttr != nil && gemAttr.AttrClass == goxml.GemAttrTypeAdvanced {
			attrs = append(attrs, id)
		}
	}

	return attrs
}

// GetAdvancedAttrAndIndex : 获取高级属性及其index
func (g *Gem) GetAdvancedAttrAndIndex() map[uint32]int {
	aAttrs := make(map[uint32]int, 2)
	for index, id := range g.data.GemAttr.GetAttrId() {
		if gemAttr := goxml.GetData().GemAttrInfoM.Index(id); gemAttr != nil {
			if gemAttr.AttrClass == goxml.GemAttrTypeAdvanced {
				aAttrs[id] = index
			}
		}
	}

	return aAttrs
}

func (g *Gem) ResetTmpAttr() {
	if g.data.GetTmpGemAttr() != nil {
		g.data.TmpGemAttr.AttrId = g.data.TmpGemAttr.AttrId[:0]
	}
}

func (g *Gem) CalcGemAttr(user *User) {
	user.ClearAttrCache()
	for _, id := range g.data.GemAttr.GetAttrId() {
		goxml.GetData().GemAttrInfoM.GenGemAttr(id, user.GetAttrCache())
	}
}

func (g *Gem) CalcGemScore(user *User) int64 {
	return user.CalcCultivateScore()
}

func (g *Gem) GetScore() int64 {
	return g.data.Score
}

func (g *Gem) SetScore(score int64) {
	g.data.Score = score
}

func calcDiffValue(v1, v2 uint32) uint32 {
	if v2 > v1 {
		return v2 - v1
	}

	return 0
}

func genAdvancedAttr(srv servicer, sysID uint32, attrs []uint32, gemInfo *goxml.GemInfoExt) []uint32 {
	diff := calcDiffValue(uint32(len(attrs)), goxml.GetData().GemInfoM.GetAdvanceAttrNum(sysID))
	if diff > 0 {
		return goxml.GetData().GemAttrInfoM.GenAdvancedAttr(srv.Rand(), gemInfo.AdvanceAttrGroup, diff, attrs)
	}

	return nil
}

func getNeedChangeAttrIndex(lockAttrs []uint32, srcAdvancedAttrs map[uint32]int) map[int]uint32 {
	changeAttrs := make(map[int]uint32)
	for attr, index := range srcAdvancedAttrs {
		isExist := false
		for _, lockAttr := range lockAttrs {
			if attr == lockAttr {
				isExist = true
			}
		}
		if !isExist {
			changeAttrs[index] = 0
		}
	}

	return changeAttrs
}

// param@ newAttrs: 生成的新属性
// param@ changeIndex: 需要改变属性的位置
func setChangeAttr(newAttrs []uint32, changeIndex map[int]uint32) {
	for _, attr := range newAttrs {
		for index := range changeIndex {
			if changeIndex[index] == 0 {
				changeIndex[index] = attr
				break
			}
		}
	}
}
