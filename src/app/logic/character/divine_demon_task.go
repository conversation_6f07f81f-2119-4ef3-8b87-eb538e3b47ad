package character

import (
	"app/goxml"
	"app/protos/out/cl"
)

type DivineDemonTask interface {
	isEventExist(activityID, event uint32) bool
	IsReceiveAwarded(taskID uint32) bool
	GetTaskData(activityID, taskID uint32) (uint32, uint64, *cl.TaskTypeProgress)
	GetTaskAwards(activityID uint32, taskIDs []uint32) (awards []*cl.Resource)
	GetTaskProgress() map[uint32]*cl.TaskTypeProgress
	HandlerReceive(taskIDs []uint32)
}

type ReturnAward interface {
	returnCanReceiveAwards(u *User, activityID uint32, progress *cl.TaskTypeProgress) []*cl.Resource
	GetTaskProgress() map[uint32]*cl.TaskTypeProgress
}

type DivineDemonSummonTask cl.DivineDemonTask
type DivineDemonActiveTask cl.DivineDemonTask
type DivineDemonStarTask cl.DivineDemonTask

func (d *DivineDemonSummonTask) IsReceiveAwarded(taskID uint32) bool {
	if _, exist := d.ReceiveAwarded[taskID]; exist {
		return true
	}

	return false
}

func (d *DivineDemonSummonTask) GetTaskData(activityID, taskID uint32) (uint32, uint64, *cl.TaskTypeProgress) {
	typeID, value := goxml.GetData().DivineDemonTaskSummonInfoM.GetTypeIDAndValue(activityID, taskID)
	return typeID, value, d.TaskProgress[typeID]
}

func (d *DivineDemonSummonTask) GetTaskAwards(activityID uint32, taskIDs []uint32) (awards []*cl.Resource) {
	for _, id := range taskIDs {
		awards = append(awards, goxml.GetData().DivineDemonTaskSummonInfoM.GetAwards(activityID, id)...)
	}

	return
}

func (d *DivineDemonSummonTask) GetTaskProgress() map[uint32]*cl.TaskTypeProgress {
	if d.TaskProgress == nil {
		d.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	return d.TaskProgress
}

func (d *DivineDemonSummonTask) HandlerReceive(taskIDs []uint32) {
	if d.ReceiveAwarded == nil {
		d.ReceiveAwarded = make(map[uint32]bool)
	}
	for _, id := range taskIDs {
		d.ReceiveAwarded[id] = true
	}
}

func (d *DivineDemonSummonTask) isEventExist(activityID, event uint32) bool {
	return goxml.GetData().DivineDemonTaskSummonInfoM.IsExistTaskType(event)
}

func (d *DivineDemonSummonTask) returnCanReceiveAwards(u *User, activityID uint32, progress *cl.TaskTypeProgress) []*cl.Resource {
	awards := make([]*cl.Resource, 0, 10) //nolint:mnd
	for _, info := range goxml.GetData().DivineDemonTaskSummonInfoM.GetTypeInfos(activityID, progress.TaskTypeId) {
		// 已领过的不返回
		if _, exist := d.ReceiveAwarded[info.TaskId]; exist {
			continue
		}
		// 任务进度没有完成
		if !u.CheckTaskFinish(progress, info.TypeId, info.Value) {
			continue
		}
		awards = append(awards, info.ClRes...)
	}

	return awards
}

func (d *DivineDemonActiveTask) IsReceiveAwarded(taskID uint32) bool {
	if _, exist := d.ReceiveAwarded[taskID]; exist {
		return true
	}

	return false
}

func (d *DivineDemonActiveTask) GetTaskData(activityID, taskID uint32) (uint32, uint64, *cl.TaskTypeProgress) {
	typeID, value := goxml.GetData().DivineDemonTaskActiveInfoM.GetTypeIDAndValue(activityID, taskID)
	return typeID, value, d.TaskProgress[typeID]
}

func (d *DivineDemonActiveTask) GetTaskAwards(activityID uint32, taskIDs []uint32) (awards []*cl.Resource) {
	for _, taskID := range taskIDs {
		awards = append(awards, goxml.GetData().DivineDemonTaskActiveInfoM.GetAwards(activityID, taskID)...)
	}

	return
}

func (d *DivineDemonActiveTask) GetTaskProgress() map[uint32]*cl.TaskTypeProgress {
	if d.TaskProgress == nil {
		d.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}

	return d.TaskProgress
}

func (d *DivineDemonActiveTask) HandlerReceive(taskIDs []uint32) {
	if d.ReceiveAwarded == nil {
		d.ReceiveAwarded = make(map[uint32]bool)
	}
	for _, id := range taskIDs {
		d.ReceiveAwarded[id] = true
	}
}

// 首发活动: 有活跃任务
func (d *DivineDemonActiveTask) isEventExist(activityID, event uint32) bool {
	if goxml.GetData().DivineDemonInfoM.IsDebut(activityID) {
		return goxml.GetData().DivineDemonTaskActiveInfoM.IsExistTaskType(event)
	}

	return false
}

func (d *DivineDemonActiveTask) returnCanReceiveAwards(u *User, activityID uint32, progress *cl.TaskTypeProgress) []*cl.Resource {
	if !goxml.GetData().DivineDemonInfoM.IsDebut(activityID) {
		return nil
	}

	awards := make([]*cl.Resource, 0, 10)
	for _, info := range goxml.GetData().DivineDemonTaskActiveInfoM.GetTypeInfos(activityID, progress.TaskTypeId) {
		// 已领过的不返回
		if _, exist := d.ReceiveAwarded[info.TaskId]; exist {
			continue
		}

		// 任务进度没有完成
		if !u.CheckTaskFinish(progress, info.TypeId, info.Value) {
			continue
		}
		awards = append(awards, info.ClRes...)
	}

	return awards
}

func (d *DivineDemonStarTask) IsReceiveAwarded(taskID uint32) bool {
	if _, exist := d.ReceiveAwarded[taskID]; exist {
		return true
	}

	return false
}

func (d *DivineDemonStarTask) GetTaskData(activityID, taskID uint32) (uint32, uint64, *cl.TaskTypeProgress) {
	typeID, value := goxml.GetData().DivineDemonTaskStarInfoM.GetTypeIDAndValue(activityID, taskID)
	return typeID, value, d.TaskProgress[typeID]
}

func (d *DivineDemonStarTask) GetTaskAwards(activityID uint32, taskIDs []uint32) (awards []*cl.Resource) {
	for _, taskID := range taskIDs {
		awards = append(awards, goxml.GetData().DivineDemonTaskStarInfoM.GetAwards(activityID, taskID)...)
	}

	return
}

func (d *DivineDemonStarTask) GetTaskProgress() map[uint32]*cl.TaskTypeProgress {
	if d.TaskProgress == nil {
		d.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}

	return d.TaskProgress
}

func (d *DivineDemonStarTask) HandlerReceive(taskIDs []uint32) {
	if d.ReceiveAwarded == nil {
		d.ReceiveAwarded = make(map[uint32]bool)
	}
	for _, id := range taskIDs {
		d.ReceiveAwarded[id] = true
	}
}

// 首发活动: 有升星任务
func (d *DivineDemonStarTask) isEventExist(activityID, event uint32) bool {
	if goxml.GetData().DivineDemonInfoM.IsDebut(activityID) {
		return goxml.GetData().DivineDemonTaskStarInfoM.IsExistTaskType(event)
	}

	return false
}

func (d *DivineDemonStarTask) returnCanReceiveAwards(u *User, activityID uint32, progress *cl.TaskTypeProgress) []*cl.Resource {
	if !goxml.GetData().DivineDemonInfoM.IsDebut(activityID) {
		return nil
	}

	awards := make([]*cl.Resource, 0, 10)
	for _, info := range goxml.GetData().DivineDemonTaskStarInfoM.GetTypeInfos(activityID, progress.TaskTypeId) {
		// 已领过的不返回
		if _, exist := d.ReceiveAwarded[info.TaskId]; exist {
			continue
		}
		// 任务进度没有完成
		if !u.CheckTaskFinish(progress, info.TypeId, info.Value) {
			continue
		}
		awards = append(awards, info.ClRes...)
	}

	return awards
}
