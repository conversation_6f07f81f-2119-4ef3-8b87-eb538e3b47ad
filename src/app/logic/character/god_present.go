package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
)

type GodPresent cl.GodPresentNew

// 构建最终奖励数据
// @param servicer srv
// @param uint32 groupKey 选定的第x组卡（序号从1开始）
// @return []*cl.Resource 合并后的子活动奖励
// @return map[uint32]uint32 子活动获得的红色英雄计数
// @return map[uint32]uint32 子活动获得的英雄碎片计数
func (g *GodPresent) MakeAwards(srv servicer, groupKey uint32) ([]*cl.Resource,
	map[uint32]uint32, map[uint32]uint32) {
	index := int(groupKey) - 1
	if index < 0 {
		l4g.Errorf("GodPresent.MakeAwards: groupKey illegal, groupKey:%d", groupKey)
		return nil, nil, nil
	}

	reqRare := goxml.GetData().ConfigInfoM.GetGodPresentReduceHeroRare()
	awards := make([]*cl.Resource, 0, 2*goxml.GodPresentSummonCountPerTime)
	redHeroes := make(map[uint32]uint32, 1)
	fragments := make(map[uint32]uint32, goxml.GetData().GodPresentRecoveryInfoM.FragmentTypeCount())
	for k, v := range g.IdList {
		if v == nil || len(v.Ids) == 0 {
			l4g.Errorf("GodPresent.MakeAwards: ids illegal, index:%d", index)
			return nil, nil, nil
		}

		for _, id := range v.Ids {
			info := goxml.GetData().HeroInfoM.Index(id)
			if info == nil {
				l4g.Errorf("GodPresent.MakeAwards: no hero, sysId:%d, k:%d", id, k)
				continue
			}

			if k == index { //英雄
				count := uint32(1)
				awards = append(awards, &cl.Resource{
					Type:  uint32(common.RESOURCE_HERO),
					Value: id,
					Count: count,
				})

				if info.Rare >= reqRare {
					redHeroes[id] += count
				}

			} else { //碎片
				award := goxml.GetData().GodPresentRecoveryInfoM.Hero2Fragment(srv.Rand(), info.Rare)
				if award == nil {
					l4g.Errorf("GodPresent.MakeAwards: no fragment award, rare:%d", info.Rare)
					continue
				}
				awards = append(awards, award)
				//碎片统计
				fragments[award.Value] += award.Count
			}
		}
	}

	awards = MergeResources(awards)
	return awards, redHeroes, fragments
}

// 抽卡是否已抽尽
func (g *GodPresent) IsFinishSummon(configCount uint32) bool {
	return len(g.IdList) >= int(configCount)
}

// 卡组顺序列表
func (g *GodPresent) GetCloneGroups() []uint32 {
	groups := make([]uint32, 0, len(g.Groups))
	return append(groups, g.Groups...)
}

// 已抽卡次数
func (g *GodPresent) GetSummonCount() uint32 {
	return uint32(len(g.IdList))
}

// 添加抽卡数据
func (g *GodPresent) AddSummonData(ids []uint32) {
	if len(g.IdList) == 0 {
		g.IdList = make([]*cl.GodPresentSummon, 0, 1)
	}
	g.IdList = append(g.IdList, &cl.GodPresentSummon{
		Ids: ids,
	})
}

func (g *GodPresent) FlushSummonData() []*cl.GodPresentSummon {
	ret := make([]*cl.GodPresentSummon, 0, 1)
	for _, v := range g.IdList {
		tmp := &cl.GodPresentSummon{
			Ids: make([]uint32, 0, goxml.GodPresentSummonCountPerTime),
		}
		tmp.Ids = append(tmp.Ids, v.Ids...)
		ret = append(ret, tmp)
	}
	return ret
}

func (g *GodPresent) FlushIDList(selectKey uint32) []uint32 {
	index := int(selectKey) - 1
	if index < 0 {
		l4g.Errorf("GodPresent.FlushIDList: index err, selectKey:%d", selectKey)
		return nil
	}

	ret := make([]uint32, 0, goxml.GodPresentSummonCountPerTime)
	return append(ret, g.IdList[index].Ids...)
}

func (g *GodPresent) FlushSummonAllIDList() []uint32 {
	ret := make([]uint32, 0, len(g.IdList)*goxml.GodPresentSummonCountPerTime)
	for _, v := range g.IdList {
		ret = append(ret, v.Ids...)
	}
	return ret
}

// 更新选中卡组数据
func (g *GodPresent) setSelectGroupKey(groupKey uint32) {
	g.SelectGroupKey = groupKey
}

// 是否已选中卡组
func (g *GodPresent) HasSelected() bool {
	return g.SelectGroupKey > 0
}

func (g *GodPresent) setFragments(fragments map[uint32]uint32) {
	g.Fragments = fragments
}
