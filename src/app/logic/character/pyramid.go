package character

import (
	"app/gmxml"
	"app/goxml"
	aevent "app/logic/event"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"sort"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
)

type PyramidM struct {
	owner *User
	data  *cl.Pyramid
}

func newPyramid(user *User) *PyramidM {
	return &PyramidM{
		owner: user,
	}
}

func (p *PyramidM) Owner() *User {
	return p.owner
}

func (p *PyramidM) load(data *cl.Pyramid) {
	p.data = data
	if p.data == nil {
		p.data = &cl.Pyramid{}
	}
	if p.data.Lattices == nil {
		p.data.Lattices = make(map[uint32]*cl.Lattice)
	}
	if p.data.ChooseDrawNum == nil {
		p.data.ChooseDrawNum = make(map[uint32]uint32)
	}
	if p.data.TaskProgress == nil {
		p.data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if p.data.ReceivedTasks == nil {
		p.data.ReceivedTasks = make(map[uint32]bool)
	}
}

func (p *PyramidM) Save() {
	p.Owner().dbUser.Module6.Pyramid = p.data
	p.Owner().setSaveTag(saveTagModule6)
}

func (p *PyramidM) Flush() *cl.Pyramid {
	return p.data.Clone()
}

func (p *PyramidM) GetPeriodId() uint32 {
	return p.data.PeriodId
}

func (p *PyramidM) GetPeriodDrawNum() uint32 {
	return p.data.PeriodNum
}

func (p *PyramidM) GetRoundId() uint32 {
	return p.data.Round
}

// 获取当前已抽奖次数
func (p *PyramidM) CalcRoundDrawNum() uint32 {
	var num uint32
	for _, lattice := range p.data.Lattices {
		num += lattice.DrawNum
	}
	return num
}

func (p *PyramidM) GetTaskProgressByTypeId(taskTypeId uint32) *cl.TaskTypeProgress {
	return p.data.TaskProgress[taskTypeId]
}

func (p *PyramidM) CheckReset(srv servicer) {
	// 检查玩家是否达到活动的参与条件
	if !p.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_PYRAMID), srv) {
		return
	}

	now := time.Now().Unix()
	// 根据当前时间获取活动期数
	curPyramidInfo := gmxml.PyramidActivityInfoM.GetCurrentActivity(srv.ServerOpenDay(now), srv.StartServiceTm())

	// 删除道具
	p.CheckDeleteItems(srv, curPyramidInfo)

	// 活动未开始或已结束
	if curPyramidInfo == nil {
		return
	}
	periodId := curPyramidInfo.Id
	// 活动进行中
	if periodId == p.data.PeriodId && curPyramidInfo.NewServer == p.data.NewServer {
		return
	}
	// 初始化第一期
	if p.data.PeriodId == 0 {
		p.data = p.initPyramidByPeriod(curPyramidInfo)
		p.Save()
		return
	}
	// 重置新的一期
	p.reset(srv, curPyramidInfo)
}

func (p *PyramidM) reset(srv servicer, info *cl.PyramidActivityBase) {
	// 数据重置
	p.data = p.initPyramidByPeriod(info)
	p.ResetRechargeShop(srv)
	p.Save()
}

func (p *PyramidM) GetServerType() uint32 {
	if p.data.NewServer {
		return goxml.ActivityPyramidNewServer
	} else {
		return goxml.ActivityPyramidNormalServer
	}
}

func (p *PyramidM) CheckInit(srv servicer) {
	now := time.Now().Unix()
	// 根据当前时间获取活动期数
	curPyramidInfo := gmxml.PyramidActivityInfoM.GetCurrentActivity(srv.ServerOpenDay(now), srv.StartServiceTm())
	l4g.Debugf("curPyramidInfo:%+v", curPyramidInfo)
	if curPyramidInfo == nil { // 活动未开始或已结束
		l4g.Errorf("[PyramidM] user %d CheckInit: no curPyramidInfo, now %d", p.owner.ID(), time.Now().Unix())
		return
	}
	if p.data.PeriodId > 0 { // 活动已初始化过
		return
	}
	p.data = p.initPyramidByPeriod(curPyramidInfo) // 初始化
	// 登录任务容错(ResetDaily已经触发过的情况)
	p.OnPyramidEvent(aevent.AeContinueLoginToX, 1, nil, srv)
	p.Save()
}

// 活动进入新一期时初始化数据
func (p *PyramidM) initPyramidByPeriod(info *cl.PyramidActivityBase) *cl.Pyramid {
	return &cl.Pyramid{
		PeriodId:      info.Id,
		Round:         goxml.ActivityPyramidInitialRound,
		Floor:         goxml.ActivityPyramidInitialFloor,
		Lattices:      make(map[uint32]*cl.Lattice),
		ChooseDrawNum: make(map[uint32]uint32),
		TaskProgress:  make(map[uint32]*cl.TaskTypeProgress),
		ReceivedTasks: make(map[uint32]bool),
		IsDeleteItems: false,
		NewServer:     info.NewServer,
	}
}

func (p *PyramidM) IsActivityOpen(srv servicer) bool {
	now := time.Now().Unix()
	return gmxml.PyramidActivityInfoM.GetCurrentActivity(srv.ServerOpenDay(now), srv.StartServiceTm()) != nil
}

func (p *PyramidM) GetIsDeleteItems() bool {
	return p.data.IsDeleteItems
}

func (p *PyramidM) SetIsDeleteItems(isDelete bool) {
	p.data.IsDeleteItems = isDelete
	p.Save()
}

func (p *PyramidM) deleteItems(srv servicer) uint32 {
	// 获取活动数据所对应的活动配置
	gmInfo := gmxml.PyramidActivityInfoM.GetActivityByUniqID(p.GetPeriodId(), p.GetServerType(), srv.StartServiceTm())
	if gmInfo == nil {
		l4g.Errorf("[PyramidM] user %d deleteItems: no gm activity cfg, periodId:%d", p.owner.ID(), p.GetPeriodId())
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	info := goxml.GetData().ActivityPyramidInfoM.GetRecordById(gmInfo.ActivityId)
	if info == nil {
		l4g.Errorf("[PyramidM] user %d deleteItems: no xml activity cfg, activityType:%d", p.owner.ID(), gmInfo.ActivityId)
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	currentNum := uint32(p.owner.GetResourceCount(uint32(common.RESOURCE_ITEM), info.ItemLimit, srv))
	if currentNum > 0 {
		costs := goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), info.ItemLimit, currentNum)
		ret := p.owner.Consume(srv, []*cl.Resource{costs}, uint32(log.RESOURCE_CHANGE_REASON_PYRAMID_RESET), 0)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("[PyramidM] user %d deleteItems: consume item err, costs: %+v", p.owner.ID(), costs)
			return ret
		}
	}

	return uint32(cret.RET_OK)
}

func (p *PyramidM) CheckDeleteItems(srv servicer, curPyramidInfo *cl.PyramidActivityBase) {
	// 是否已删除道具
	if p.GetIsDeleteItems() {
		l4g.Debugf("[PyramidM] CheckDeleteItems: actId %d has delete items", p.data.PeriodId)
		return
	}

	// 活动正在进行中，不能删除道具
	if curPyramidInfo != nil && curPyramidInfo.Id == p.GetPeriodId() {
		l4g.Debugf("[PyramidM] CheckDeleteItems: act is running. id %d", curPyramidInfo.Id)
		return
	}

	l4g.Debugf("[PyramidM] CheckDeleteItems: deleteItems.")
	if dRet := p.deleteItems(srv); dRet == uint32(cret.RET_OK) {
		p.SetIsDeleteItems(true)
	}
}

func (p *PyramidM) IsChooseIdValid(latticeGroup, chooseId uint32) bool {
	chooseRecords := goxml.GetData().ActivityPyramidChooseInfoM.GetIdRecordsByServerTypeGroup(p.GetServerType(), latticeGroup)
	for _, record := range chooseRecords {
		if record.Id == chooseId {
			return true
		}
	}
	return false
}

func (p *PyramidM) CanChooseAward(chooseId uint32) bool {
	chooseInfo := goxml.GetData().ActivityPyramidChooseInfoM.GetRecordById(chooseId)
	if chooseInfo == nil {
		l4g.Errorf("user: %d IsAchieveChooseRequirement: invalid ChooseId %d", p.owner.ID(), chooseId)
		return false
	}
	// 轮次限制
	if p.data.Round < chooseInfo.ChooseRound {
		l4g.Errorf("user: %d IsAchieveChooseRequirement: Round %d not enough, require round %d", p.owner.ID(), p.data.Round, chooseInfo.ChooseRound)
		return false
	}
	// 本期活动自选奖励总抽取数限制
	if drawNum, exist := p.data.ChooseDrawNum[chooseId]; exist && drawNum >= chooseInfo.RewardMax {
		l4g.Errorf("user: %d IsAchieveChooseRequirement: drawNum %d over ", p.owner.ID(), drawNum)
		return false
	}

	return true
}

func (p *PyramidM) IsLatticeCanChangeChooseAward(latticeId uint32) bool {
	data, exist := p.data.Lattices[latticeId]
	// 未选择自选奖励
	if !exist || data.ChooseId == 0 {
		return true
	}
	// 已选择自选奖励但未抽到过，可以更换自选
	if data.ChooseId > 0 && data.DrawNum == 0 {
		return true
	}
	return false
}

func (p *PyramidM) SetLatticeChooseAward(latticeId, chooseId uint32) {
	p.data.Lattices[latticeId] = &cl.Lattice{
		LatticeId: latticeId,
		ChooseId:  chooseId,
	}
	p.Save()
}

func (p *PyramidM) CheckHasChooseAward() bool {
	// 获取本层需要自选的格位
	needChooseLattices := goxml.GetData().ActivityPyramidLatticeInfoM.GetNeedChooseLatticeByFloor(p.GetServerType(), p.data.Floor)
	if len(needChooseLattices) == 0 {
		return true
	}

	// 获取所有已自选的格位
	hasChooseLattices := make(map[uint32]struct{})
	for _, lattice := range p.data.Lattices {
		if lattice.ChooseId > 0 {
			hasChooseLattices[lattice.LatticeId] = struct{}{}
		}
	}

	// 判断是否已自选
	for _, lattice := range needChooseLattices {
		_, exist := hasChooseLattices[lattice]
		if !exist {
			l4g.Errorf("user: %d CheckHasChooseAward: lattice %d not choose award ", p.owner.ID(), lattice)
			return false
		}
	}

	return true
}

// 检查是否已经不能继续抽奖
func (p *PyramidM) CheckDrawFinish(maxRound uint32) bool {
	if p.data.Round > maxRound {
		return true
	}
	if p.data.Round == maxRound && p.checkDrawBigPrizeInCurrentRound() {
		return true
	}
	return false
}

func (p *PyramidM) checkDrawBigPrizeInCurrentRound() bool {
	for _, lattice := range p.data.Lattices {
		latticeInfo := goxml.GetData().ActivityPyramidLatticeInfoM.GetRecordById(lattice.LatticeId)
		if latticeInfo.Type == goxml.ActivityPyramidLatticeBigPrize && lattice.ChooseId > 0 && lattice.DrawNum >= goxml.ActivityPyramidLatticeBigPrizeDrawMax {
			return true
		}
	}
	return false
}

func (p *PyramidM) CalculateDynamicDrawCosts(srv servicer) []*cl.Resource {
	// 获取活动配置
	gmInfo := gmxml.PyramidActivityInfoM.GetActivityByUniqID(p.GetPeriodId(), p.GetServerType(), srv.StartServiceTm())
	if gmInfo == nil {
		l4g.Errorf("[PyramidM] user %d CalculateDynamicDrawCosts: no gm activity cfg, periodId:%d", p.owner.ID(), p.GetPeriodId())
		return nil
	}

	info := goxml.GetData().ActivityPyramidInfoM.GetRecordById(gmInfo.ActivityId)
	if info == nil {
		l4g.Errorf("[PyramidM] user %d CalculateDynamicDrawCosts: no xml activity cfg, activityType:%d", p.owner.ID(), gmInfo.ActivityId)
		return nil
	}
	// 根据当前轮次抽奖次数获取配置
	roundDrawNum := p.CalcRoundDrawNum()
	ok, costNum := goxml.GetData().BuyPriceInfoM.GetPrice(info.BuyPrice, roundDrawNum, 1)
	if !ok {
		l4g.Errorf("user: %d CalculateDynamicDrawCosts: calc cost err %d", p.owner.ID(), roundDrawNum)
		return nil
	}

	// 根据永久抽卡券数量和限时抽卡券数量计算动态消耗
	permanentNum := p.owner.GetResourceCount(uint32(common.RESOURCE_ITEM), info.ItemPermanent, srv)
	limitNum := p.owner.GetResourceCount(uint32(common.RESOURCE_ITEM), info.ItemLimit, srv)
	requireNum := uint64(costNum)
	if permanentNum+limitNum < requireNum {
		l4g.Errorf("user: %d CalculateDynamicDrawCosts: totalCount not enough, permanentNum %d limitNum %d requireNum %d", p.owner.ID(), permanentNum, limitNum, requireNum)
		return nil
	}

	// 优先消耗限时抽卡券
	var costs []*cl.Resource
	if limitNum >= requireNum {
		costs = append(costs, goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), info.ItemLimit, uint32(requireNum)))
	} else {
		if limitNum > 0 {
			costs = append(costs, goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), info.ItemLimit, uint32(limitNum)))
		}
		costs = append(costs, goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), info.ItemPermanent, uint32(requireNum-limitNum)))
	}

	return costs
}

type WeightLattice struct {
	Weight  uint32
	Lattice *PyramidLattice
}

func (p *PyramidM) DrawInPool(rd *rand.Rand, pool []*PyramidLattice) *PyramidLattice {
	var totalWeight uint32
	weightLattice := make([]*WeightLattice, 0, len(pool))
	for _, lattice := range pool {
		totalWeight += lattice.Weight
		weightLattice = append(weightLattice, &WeightLattice{
			Weight:  lattice.Weight,
			Lattice: lattice,
		})
	}
	if totalWeight == 0 {
		l4g.Errorf("user: %d [Pyramid] DrawInPool: no totalWeight", p.owner.ID())
		return nil
	}

	randNum := uint32(rd.RandBetween(1, int(totalWeight)))
	var finalLattice *PyramidLattice
	for _, wl := range weightLattice {
		if randNum > 0 && randNum <= wl.Weight {
			finalLattice = wl.Lattice
			break
		}
		randNum -= wl.Weight
	}
	return finalLattice
}

type PyramidLattice struct {
	Id          uint32
	Floor       uint32
	Pos         uint32
	Type        uint32
	Weight      uint32
	RewardGroup uint32
	RewardType  uint32
	RewardValue uint32
	RewardCount uint32
}

// 获取有效奖池
func (p *PyramidM) GetDrawPool() []*PyramidLattice {
	// 获取当前的可抽取奖池
	datas := goxml.GetData().ActivityPyramidLatticeInfoM.GetRecordsByServerTypeFloor(p.GetServerType(), p.data.Floor)
	if len(datas) == 0 {
		l4g.Errorf("user: %d [Pyramid] GetDrawPool: drawPool empty, floor %d", p.owner.ID(), p.data.Floor)
		return nil
	}

	drawPool := make([]*PyramidLattice, 0, len(datas))
	for _, data := range datas {
		drawPool = append(drawPool, &PyramidLattice{
			Id:          data.Id,
			Floor:       data.Floor,
			Pos:         data.Pos,
			Type:        data.Type,
			Weight:      data.Weight,
			RewardGroup: data.RewardGroup,
			RewardType:  data.RewardType,
			RewardValue: data.RewardValue,
			RewardCount: data.RewardCount,
		})
	}
	return p.filterDrawPool(drawPool)
}

func (p *PyramidM) filterDrawPool(latticePool []*PyramidLattice) []*PyramidLattice {
	var validPool []*PyramidLattice
	for _, lattice := range latticePool {
		// 格位的抽取限制
		if !p.checkLatticeCanDraw(lattice.Id, lattice.Type) {
			continue
		}
		validPool = append(validPool, lattice)
	}
	return validPool
}

// 检查格位的抽取次数的限制
func (p *PyramidM) checkLatticeCanDraw(latticeId, latticeType uint32) bool {
	var hasDrawNum uint32 // 格位已经抽取过的次数
	lattice, exist := p.data.Lattices[latticeId]
	if exist { // 不存在说明没有抽过
		hasDrawNum = lattice.DrawNum
	}

	// 获取限制抽取数量
	limitNum := p.getLatticeDrawLimit(latticeType)

	return hasDrawNum < limitNum
}

// 检查自选奖励的抽取次数的限制
func (p *PyramidM) CheckChooseCanDraw(chooseId uint32) bool {
	// 获取自选id最大可抽取次数
	chooseInfo := goxml.GetData().ActivityPyramidChooseInfoM.GetRecordById(chooseId)
	if chooseInfo == nil {
		l4g.Errorf("user: %d [Pyramid] CheckChooseCanDraw: no chooseInfo, chooseId %d", p.owner.ID(), chooseId)
		return false
	}
	drawMax := chooseInfo.RewardMax

	// 获取当前抽取数量
	var drawNum uint32
	num, ce := p.data.ChooseDrawNum[chooseId]
	if ce { // 未抽取过说明抽取数量为0
		drawNum = num
	}

	return drawNum < drawMax
}

// 返回是否存在抽取限制及限制数量（内部规则）
func (p *PyramidM) getLatticeDrawLimit(latticeType uint32) uint32 {
	switch latticeType {
	case goxml.ActivityPyramidLatticeNormalPrize:
		return goxml.ActivityPyramidLatticeNormalPrizeDrawMax
	case goxml.ActivityPyramidLatticeRisePrizeOne:
		return goxml.ActivityPyramidLatticeRisePrizeOneDrawMax
	case goxml.ActivityPyramidLatticeChoosePrize:
		return goxml.ActivityPyramidLatticeChoosePrizeDrawMax
	case goxml.ActivityPyramidLatticeBigPrize:
		return goxml.ActivityPyramidLatticeBigPrizeDrawMax
	default:
		l4g.Errorf("user: %d [Pyramid] GetLatticeDrawLimit: invalid latticeType %d", p.owner.ID(), latticeType)
		return 0
	}
}

// 根据格位类型判断是否可以进行奖励自选
func (p *PyramidM) GetLatticeCanChooseAwardByType(latticeType uint32) uint32 {
	switch latticeType {
	case goxml.ActivityPyramidLatticeNormalPrize:
		return 0
	case goxml.ActivityPyramidLatticeRisePrizeOne:
		return 0
	case goxml.ActivityPyramidLatticeChoosePrize:
		return 1
	case goxml.ActivityPyramidLatticeBigPrize:
		return 1
	default:
		l4g.Errorf("user: %d [Pyramid] GetLatticeCanChooseAwardByType: invalid latticeType %d", p.owner.ID(), latticeType)
		return 0
	}
}

// 增加当前活动抽奖次数
func (p *PyramidM) AddPeriodDrawNum() {
	p.data.PeriodNum += 1
}

func (p *PyramidM) GetDrawAwardsByLatticeType(latticeInfo *PyramidLattice) []*cl.Resource {
	switch latticeInfo.Type {
	case goxml.ActivityPyramidLatticeNormalPrize, goxml.ActivityPyramidLatticeRisePrizeOne: //普通格位，上升格位1
		return []*cl.Resource{goxml.GenSimpleResource(latticeInfo.RewardType, latticeInfo.RewardValue, latticeInfo.RewardCount)}
	case goxml.ActivityPyramidLatticeChoosePrize, goxml.ActivityPyramidLatticeBigPrize: //自选格位，大奖格位
		return p.getChooseAwards(latticeInfo.Id)
	default:
		l4g.Errorf("user: %d [Pyramid] GetDrawAwardsByLatticeType: invalid lattice type %d", p.owner.ID(), latticeInfo.Type)
		return nil
	}
}

func (p *PyramidM) HandleWithLatticeType(latticeInfo *PyramidLattice, isCircleNumWork bool, srv servicer) {
	// 获取活动数据所对应的活动配置
	gmInfo := gmxml.PyramidActivityInfoM.GetActivityByUniqID(p.GetPeriodId(), p.GetServerType(), srv.StartServiceTm())
	if gmInfo == nil {
		l4g.Errorf("user: %d [Pyramid] HandleWithLatticeType: no gm activity cfg, periodId %d", p.owner.ID(), p.GetPeriodId())
		return
	}

	info := goxml.GetData().ActivityPyramidInfoM.GetRecordById(gmInfo.ActivityId)
	if info == nil {
		l4g.Errorf("user: %d [Pyramid] HandleWithLatticeType:  no xml activity cfg, activityType %d", p.owner.ID(), gmInfo.ActivityId)
		return
	}

	// 通用逻辑
	p.handleCommonLattice(latticeInfo.Id) // 通用格位处理
	p.AddPeriodDrawNum()

	// 特殊逻辑
	switch latticeInfo.Type {
	case goxml.ActivityPyramidLatticeNormalPrize: //普通格位
	case goxml.ActivityPyramidLatticeRisePrizeOne: //上升格位1
		p.data.Floor += 1
	case goxml.ActivityPyramidLatticeChoosePrize: //自选格位
		p.handleChooseAwardLattice(latticeInfo.Id)
	case goxml.ActivityPyramidLatticeBigPrize: //大奖格位
		p.handleChooseAwardLattice(latticeInfo.Id)
		p.handleBigPrizeLattice(isCircleNumWork, info.CircleNum)
	default:
		l4g.Errorf("user: %d [Pyramid] HandleWithLatticeType: invalid lattice type %d", p.owner.ID(), latticeInfo.Type)
		return
	}
}

// 通用格位处理(包含自选格位)
func (p *PyramidM) handleCommonLattice(latticeId uint32) {
	data, exist := p.data.Lattices[latticeId]
	if exist {
		data.DrawNum += 1
	} else {
		p.data.Lattices[latticeId] = &cl.Lattice{
			LatticeId: latticeId,
			DrawNum:   1,
		}
	}
}

// 含自选的格位的特殊处理(包括自选格位和大奖格位)
func (p *PyramidM) handleChooseAwardLattice(latticeId uint32) {
	lattice, le := p.data.Lattices[latticeId]
	if !le {
		l4g.Errorf("user: %d [Pyramid] HandleChooseAwardLattice: draw without choose award", p.owner.ID())
		return
	}

	chooseId := lattice.ChooseId
	if chooseId == 0 {
		return
	}
	_, e := p.data.ChooseDrawNum[chooseId]
	if e {
		p.data.ChooseDrawNum[chooseId] += 1
	} else {
		p.data.ChooseDrawNum[chooseId] = 1
	}
}

// 大奖格位的特殊处理：未毕业，进入下一轮次并初始化轮次抽奖数据;已毕业，不需要额外处理
func (p *PyramidM) handleBigPrizeLattice(isCircleNumWork bool, circleNum uint32) {
	nextRound := p.data.Round + 1
	if isCircleNumWork { //判断期数限制是否生效（对测试工具不生效）
		if nextRound > circleNum {
			return
		}
	}
	p.resetPyramidByRound(nextRound)
}

// 活动进入下一轮次时初始化数据
func (p *PyramidM) resetPyramidByRound(roundId uint32) {
	p.data.Round = roundId
	p.data.Floor = goxml.ActivityPyramidInitialFloor
	p.data.Lattices = make(map[uint32]*cl.Lattice)
}

// 获取格位的自选奖励
func (p *PyramidM) getChooseAwards(latticeId uint32) []*cl.Resource {
	data, exist := p.data.Lattices[latticeId]
	if !exist || data == nil {
		l4g.Errorf("user: %d HandleWithLatticeType: no lattice data, latticeId %d", p.owner.ID(), latticeId)
		return nil
	}
	chooseInfo := goxml.GetData().ActivityPyramidChooseInfoM.GetRecordById(data.ChooseId)
	if chooseInfo == nil {
		l4g.Errorf("user: %d HandleWithLatticeType: invalid chooseId %d", p.owner.ID(), data.ChooseId)
		return nil
	}
	return chooseInfo.Rewards
}

/**************** 任务模块 ****************/

func (p *PyramidM) OnPyramidEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	// 检查活动是否开启
	if !p.IsActivityOpen(srv) {
		return
	}
	// 检查玩家是否达到活动的参与条件
	if !p.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_PYRAMID), srv) {
		return
	}
	taskProgress, change := p.owner.TaskTypeOnEvent(p.data.TaskProgress, event, progress, values)
	if change {
		p.update(taskProgress)
		p.Save()
	}
}

func (p *PyramidM) update(progress map[uint32]*cl.TaskTypeProgress) {
	smsg := &cl.L2C_PyramidUpdateTask{
		Ret:      uint32(cret.RET_OK),
		Progress: progress,
	}
	p.owner.SendCmdToGateway(cl.ID_MSG_L2C_PyramidUpdateTask, smsg)
}

func (p *PyramidM) IsTaskAwardsReceived(taskId uint32) bool {
	_, exist := p.data.ReceivedTasks[taskId]
	return exist
}

func (p *PyramidM) ReceiveTaskAward(taskIds []uint32) {
	for _, id := range taskIds {
		p.data.ReceivedTasks[id] = true
	}
	p.Save()
}

/**************** 任务模块 ****************/

/**************** 商店模块 ****************/

func (p *PyramidM) ResetRechargeShop(srv servicer) {
	// 获取活动数据所对应的活动配置
	gmInfo := gmxml.PyramidActivityInfoM.GetActivityByUniqID(p.GetPeriodId(), p.GetServerType(), srv.StartServiceTm())
	if gmInfo == nil {
		l4g.Errorf("user: %d [Pyramid] IsRechargeShopOpen: no gm activity cfg, periodId %d", p.owner.ID(), p.GetPeriodId())
		return
	}

	pyramidInfo := goxml.GetData().ActivityPyramidInfoM.GetRecordById(gmInfo.ActivityId)
	if pyramidInfo == nil {
		l4g.Errorf("user: %d [Pyramid] IsRechargeShopOpen:  no xml activity cfg, activityType %d", p.owner.ID(), gmInfo.ActivityId)
		return
	}

	p.owner.ActivityRecharge().ResetByShopID(pyramidInfo.RechargeId)
}

func (p *PyramidM) IsRechargeShopOpen(srv servicer, shopId uint32) bool {
	if !p.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_PYRAMID), srv) {
		return false
	}

	now := time.Now().Unix()
	// 根据当前时间获取活动期数
	curPyramidInfo := gmxml.PyramidActivityInfoM.GetCurrentActivity(srv.ServerOpenDay(now), srv.StartServiceTm())
	// 活动未开始或已结束
	if curPyramidInfo == nil {
		return false
	}

	pyramidInfo := goxml.GetData().ActivityPyramidInfoM.GetRecordById(curPyramidInfo.ActivityId)
	if pyramidInfo == nil {
		l4g.Errorf("user: %d [PyramidM] IsRechargeShopOpen: activity config not found, activityType %d", p.owner.ID(), curPyramidInfo.ActivityId)
		return false
	}

	return pyramidInfo.RechargeId == shopId
}

/**************** 商店模块 ****************/

/**************** 测试工具 ****************/

// 抽取多轮
func (p *PyramidM) DrawMultiRound(srv servicer, drawRounds uint32, latticeWeight map[uint32]uint32) []*cl.PyramidTestResult {
	m := make(map[uint32]uint32)
	for i := uint32(0); i < drawRounds; i++ {
		drawNum := p.drawSingleRound(srv, latticeWeight)
		if drawNum == 0 {
			l4g.Errorf("user: %d DrawMultiRound: draw err, round %d", p.owner.ID(), i+1)
			break
		}
		m[drawNum]++
	}
	// 对结果进行筛选
	return p.filterDrawResults(m)
}

func (p *PyramidM) filterDrawResults(data map[uint32]uint32) []*cl.PyramidTestResult {
	result := make([]*cl.PyramidTestResult, 0, len(data))
	for k, v := range data {
		result = append(result, &cl.PyramidTestResult{DrawBigPrizeNeedNum: k, SampleNum: v})
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].SampleNum > result[j].SampleNum
	})

	if len(result) <= goxml.ActivityPyramidTestDrawResultNum {
		return result
	}

	return result[:goxml.ActivityPyramidTestDrawResultNum]
}

// 抽取单轮，返回抽中大奖所需要的次数
func (p *PyramidM) drawSingleRound(srv servicer, latticeWeight map[uint32]uint32) uint32 {
	var drawNum uint32         // 记录抽取次数
	for i := 0; i < 100; i++ { // 限制最大抽取次数,防止因策划配置导致无法抽到大奖而一直循环下去
		drawPool := p.GetDrawPool()
		if len(drawPool) == 0 {
			l4g.Errorf("user: %d DrawSingleRound: invalid drawPool", p.owner.ID())
			continue
		}
		drawLatticeInfo := p.DrawInPool(srv.Rand(), p.fixDrawPoolWeight(drawPool, latticeWeight))
		if drawLatticeInfo == nil {
			l4g.Errorf("user: %d DrawSingleRound: draw error, draw pool %+v", p.owner.ID(), drawPool)
			continue
		}
		p.HandleWithLatticeType(drawLatticeInfo, false, srv)
		drawNum++
		if drawLatticeInfo.Type == goxml.ActivityPyramidLatticeBigPrize {
			break
		}
	}
	return drawNum
}

func (p *PyramidM) fixDrawPoolWeight(latticePool []*PyramidLattice, latticeWeight map[uint32]uint32) []*PyramidLattice {
	var validPool []*PyramidLattice
	for _, lattice := range latticePool {
		fixedWeight, exist := latticeWeight[lattice.Id]
		if !exist {
			l4g.Errorf("user: %d [Pyramid] FixDrawPoolWeight: latticeId %d not in latticeWeight", p.owner.ID(), lattice.Id)
			return nil
		}
		fixedLattice := &PyramidLattice{
			Id:          lattice.Id,
			Floor:       lattice.Floor,
			Pos:         lattice.Pos,
			Type:        lattice.Type,
			Weight:      fixedWeight,
			RewardGroup: lattice.RewardGroup,
			RewardType:  lattice.RewardType,
			RewardValue: lattice.RewardValue,
			RewardCount: lattice.RewardCount,
		}
		validPool = append(validPool, fixedLattice)
	}
	return validPool
}

/**************** 测试工具 ****************/
