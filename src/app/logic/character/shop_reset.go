package character

import (
	"app/goxml"
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/time"
)

// ShopReset : 商店重置刷新接口；不同的type，重置刷新不同
type ShopReset interface {
	reset(servicer, *User, *goxml.ShopInfoExt) bool // 刷新商店数据
}

type ShopResetRandom cl.Shop // 随机商店

func (s *ShopResetRandom) reset(srv servicer, u *User, shopInfo *goxml.ShopInfoExt) bool {
	isPaid := s.paidReset(shopInfo.PaidRefreshLimitResetTm)
	isFree := s.freeReset(u, shopInfo)

	return isPaid || isFree
}

func (s *ShopResetRandom) paidReset(tm uint32) bool {
	resetTime := goxml.CalcLastDailyResetTime(tm)
	if resetTime <= s.PaidRefreshResetTm {
		return false
	}
	s.PaidRefreshResetTm = resetTime
	s.PaidRefreshNum = 0

	return true
}

func (s *ShopResetRandom) freeReset(user *User, shopInfo *goxml.ShopInfoExt) bool {
	freeRefreshNum := s.calcFreeRefreshNum(user.Vip(), shopInfo.FreeRefreshLimit)
	if freeRefreshNum == 0 {
		return false
	}

	now := time.Now().Unix()
	if s.FreeRefreshRecoverTm >= now {
		return false
	}

	addNum := s.calcAddRefreshNum(now, freeRefreshNum, shopInfo.FreeRefreshInterval)
	if addNum == 0 {
		return false
	}

	s.FreeRefreshLeftNum += addNum
	s.FreeRefreshRecoverTm += int64(addNum * shopInfo.FreeRefreshInterval)

	return true
}

func (s *ShopResetRandom) calcFreeRefreshNum(vip, numLimit uint32) uint32 {
	numLimit += goxml.GetData().VipPrivilegeInfoM.GetShopFreeRefreshAdd(s.SysId, vip)
	if s.FreeRefreshLeftNum >= numLimit {
		return 0
	}

	return numLimit - s.FreeRefreshLeftNum
}

func (s *ShopResetRandom) calcAddRefreshNum(now int64, freeRefreshNum, refreshInterval uint32) uint32 {
	num := uint32(now-s.FreeRefreshRecoverTm) / refreshInterval
	if num > freeRefreshNum {
		num = freeRefreshNum
	}

	return num
}

type ShopResetRound cl.Shop // 轮次商店

func (s *ShopResetRound) reset(srv servicer, u *User, shopInfo *goxml.ShopInfoExt) bool {
	round, resetTime := calcRoundAndResetTime(srv, int64(shopInfo.ShopResetInterval), shopInfo.Id)
	if resetTime > s.ShopResetTm {
		s.update(round, resetTime, shopInfo)
		return true
	}

	return false
}

func (s *ShopResetRound) update(round uint32, resetTime int64, shopInfo *goxml.ShopInfoExt) {
	newGoodsList := make(map[uint32]uint32)
	if shopInfo != nil && shopInfo.GoodsIndependent {
		for goodsId, buyNum := range s.GoodsList {
			goodsInfo := goxml.GetData().ShopRegularGoodsInfoM.Index(goodsId)
			if goodsInfo == nil {
				continue
			}
			switch goodsInfo.BuyLimitType {
			case uint32(goxml.ShopLimitTypeWeekly), uint32(goxml.ShopLimitTypeMonth):
				newGoodsList[goodsId] = buyNum
			}
		}
	}
	s.GoodsList = newGoodsList
	s.Round = round
	s.ShopResetTm = resetTime
}
