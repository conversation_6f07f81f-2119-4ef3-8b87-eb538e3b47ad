package character

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"
	"app/protos/out/ret"
	cret "app/protos/out/ret"
	"strconv"
	"strings"
	"sync"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

/*
	秒跑工具：构建阵容规则 - https://qdreaming.feishu.cn/sheets/shtcnlFLIzi8dKCWIn1CDN9Kotg?sheet=iHN3hX


*/

var (
	AiAttackPool   []*User
	AiDefensePool  []*User
	heroGradeInfo  *AiToolsHeroGrade
	once           sync.Once
	aiToolsStartID uint64
	aiToolsRd      *rand.Rand
)

func resetAiPool() {
	AiAttackPool = AiAttackPool[:0]
	AiDefensePool = AiDefensePool[:0]
}

func newAiToolsID() uint64 {
	aiToolsStartID++
	return aiToolsStartID
}

// @param needCheckIsSeasonLinkActivation bool - 是否检查赛季羁绊的激活状态（为false时，自动激活）
// @parma fid uint32 所用阵容id
func CreateAiTool(strategy, seasonID uint32, dataPath string, needCheckIsSeasonLinkActivation bool, fid uint32) uint32 {
	once.Do(func() {
		InitAiToolConfig(dataPath)
	})

	resetAiPool()

	heroGradeInfo = aiToolsHeroGradeM.Index(strategy)
	if heroGradeInfo == nil {
		l4g.Errorf("create ai tool formation failed. heroGradeInfo is nil id: %d", strategy)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	for _, info := range aiToolsLineupM.Datas {
		if info.Attack == 0 && info.Defense == 0 {
			continue
		}

		u := &User{
			dbUser: &db.User{
				Id:               uint64(info.Id),
				ModuleGlobalAttr: &db.ModuleGlobalAttr{},
				Base: &db.BaseAttr{
					SeasonId: seasonID,
				},
			},
		}
		u.skins = NewSkinM(u)
		u.titles = NewTitleM(u)
		preparePokemon(u)
		u.modules = make(map[uint32]UserModuler)
		u.initModule(true)

		if retCode := aiFormationOp(u, info, heroGradeInfo, fid); retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed, aiFormationOp failed. retCode: %d", retCode)
			return retCode
		}

		if retCode := aiHandbookOp(u, heroGradeInfo); retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed, aiHandbookOp failed. retCode: %d", retCode)
			return retCode
		}

		if retCode := aiGoddessContractOp(u, heroGradeInfo); retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed, aiGoddesscontractOp failed. retCode: %d", retCode)
			return retCode
		}

		if retCode := aiSeasonLinkMonumentOp(u, heroGradeInfo); retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed, aiSeasonLinkMonumentOp failed. retCode: %d", retCode)
			return retCode
		}

		if retCode := aiRemainBookOp(u, heroGradeInfo); retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed, aiRemainBookOp failed. retCode: %d", retCode)
			return retCode
		}

		if retCode := aiPokemonOp(u, info, heroGradeInfo); retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed, aiPokemonOp failed. retCode: %d", retCode)
			return retCode
		}

		if info.Attack == 1 {
			AiAttackPool = append(AiAttackPool, u)
		}
		if info.Defense == 1 {
			AiDefensePool = append(AiDefensePool, u)
		}
		l4g.Debugf("测试: heros %d", len(u.heroes.heroes))
		for _, hero := range u.heroes.heroes {
			smsg := &cl.L2C_HeroTestAttr{
				Ret: uint32(cret.RET_OK),
			}
			hero.TestHeroAttr(u, smsg, needCheckIsSeasonLinkActivation)
			data, _ := json.Marshal(smsg)

			l4g.Debugf("测试属性%d------- %s \n", hero.data.Id, data)
		}
	}

	return uint32(ret.RET_OK)
}

func addAiHero(u *User, heroID, heroGrade uint32, rare ...uint32) (uint32, *Hero) {
	if u.heroes == nil {
		u.heroes = newHeroM(u)
	}

	hero := newHero(newAiToolsID(), heroID)
	if hero == nil {
		l4g.Errorf("create ai tool formation failed. newHero is nil. heroID: %d", heroID)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}
	u.heroes.heroes[hero.data.Id] = hero

	developInfo := aiToolsDevelopM.Index(heroGrade)
	if developInfo == nil {
		l4g.Errorf("create ai tool formation failed. developInfo is nil. heroGrade: %d", heroGrade)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}

	// 英雄
	hero.data.Level = developInfo.HeroLevels
	hero.data.Star = developInfo.HeroStars
	if len(rare) > 0 && rare[0] == uint32(50) && developInfo.HeroStars > 9 {
		hero.data.Star = 9
	}
	hero.data.Stage = developInfo.HeroStages
	awakenLevel := developInfo.HeroAwakenLevels
	if awakenLevel > goxml.GetData().HeroAwakenLevelInfoM.MaxId() {
		awakenLevel = goxml.GetData().HeroAwakenLevelInfoM.MaxId()
	}
	heroInfo := goxml.GetData().HeroInfoM.Index(heroID)
	if heroInfo == nil || !heroInfo.IsAwakenable() || !goxml.GetData().ConfigInfoM.IsHeroAwakenStarUnlocked(hero.data.Star) {
		awakenLevel = 0
	}
	hero.data.AwakenLevel = awakenLevel

	// 宝石
	hero.data.Gems = append(hero.data.Gems, &cl.Gem{Slot: 1, Level: developInfo.Gemlevel}, &cl.Gem{Slot: 2, Level: developInfo.Gemlevel})

	// 装备
	if u.equips == nil {
		u.equips = NewEquipM(u)
	}
	equipIDs := strings.Split(developInfo.EquipId, ",")
	for i, id := range equipIDs {
		equipID, err := strconv.ParseUint(id, 10, 32)
		if err != nil {
			l4g.Errorf("create ai tool formation failed. get equipID failed. equipID: %s", id)
			return uint32(ret.RET_ERROR), nil
		}
		equip := &Equip{
			Data: &cl.Equipment{
				Id:            newAiToolsID(),
				SysId:         uint32(equipID),
				Hid:           hero.data.Id,
				StrengthLevel: developInfo.EquipStrength,
				RefineLevel:   developInfo.EquipRefine,
				EnchantLevel:  developInfo.EquipEnchant,
			},
		}
		u.equips.es[equip.Data.Id] = equip
		hero.data.Equipment[uint32(i)] = equip.Data.Id
	}

	exclusiveInfo := aiToolsHeroExclusiveM.Index(heroID, developInfo.EmblemGrade)
	if exclusiveInfo == nil {
		l4g.Errorf("create ai tool formation failed. exclusiveInfo is nil. heroID: %d, emblemGrade: %d", heroID, developInfo.EmblemGrade)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}

	// 纹章
	if u.emblems == nil {
		u.emblems = newEmblemM(u)
	}
	emblem1 := addAiEmblem(u, exclusiveInfo.EmblemId1, developInfo.EmblemLevel, exclusiveInfo.IsHeroGroupId, hero)
	emblem2 := addAiEmblem(u, exclusiveInfo.EmblemId2, developInfo.EmblemLevel, exclusiveInfo.IsHeroGroupId, hero)
	emblem3 := addAiEmblem(u, exclusiveInfo.EmblemId3, developInfo.EmblemLevel, exclusiveInfo.IsHeroGroupId, hero)
	emblem4 := addAiEmblem(u, exclusiveInfo.EmblemId4, developInfo.EmblemLevel, exclusiveInfo.IsHeroGroupId, hero)

	hero.data.Emblem[1] = emblem1.Data.Id
	hero.data.Emblem[2] = emblem2.Data.Id
	hero.data.Emblem[3] = emblem3.Data.Id
	hero.data.Emblem[4] = emblem4.Data.Id

	// 赛季装备
	if u.seasonJewelry == nil {
		u.seasonJewelry = NewSeasonJewelryM(u) // ai_tool不测试赛季装备，此时仅保证不报错
	}

	return uint32(ret.RET_OK), hero
}

func addAiEmblem(u *User, emblemID, emblemLevel, isHeroGroupId uint32, hero *Hero) *Emblem {
	if u.emblems == nil {
		u.emblems = newEmblemM(u)
	}

	emblem := &Emblem{
		Data: &cl.EmblemInfo{
			Id:           newAiToolsID(),
			SysId:        emblemID,
			AdditiveHero: isHeroGroupId,
		},
	}
	emblem.Data.Level = emblemLevel
	emblem.Data.Hid = hero.data.Id
	u.emblems.emblems[emblem.Data.Id] = emblem

	return emblem
}

func aiFormationOp(u *User, info *AiToolsLineup, heroGrade *AiToolsHeroGrade, fid uint32) uint32 {
	if u.formations == nil {
		u.formations = &FormationM{
			formations: make(map[uint32]*cl.Formation, 40),
		}
	}

	retCode, hero1 := addAiHero(u, info.Hero1, heroGrade.Hero1)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. addAiHero failed. hero1ID: %d, ret: %d", info.Hero1, retCode)
		return retCode
	}

	retCode, hero2 := addAiHero(u, info.Hero2, heroGrade.Hero2)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. newHero is nil. hero2ID: %d, ret: %d", info.Hero2, retCode)
		return retCode
	}

	retCode, hero3 := addAiHero(u, info.Hero3, heroGrade.Hero3)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. newHero is nil. hero3ID: %d, ret: %d", info.Hero3, retCode)
		return retCode
	}

	retCode, hero4 := addAiHero(u, info.Hero4, heroGrade.Hero4)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. newHero is nil. hero4ID: %d, ret: %d", info.Hero4, retCode)
		return retCode
	}

	retCode, hero5 := addAiHero(u, info.Hero5, heroGrade.Hero2)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. newHero is nil. hero5ID: %d, ret: %d", info.Hero5, retCode)
		return retCode
	}

	// 增加图鉴全局属性
	for _, v := range TestAiToolsHeroM.Datas {
		// 去掉布阵的英雄
		if v.HeroId == info.Hero1 || v.HeroId == info.Hero2 || v.HeroId == info.Hero3 || v.HeroId == info.Hero4 || v.HeroId == info.Hero5 {
			continue
		}
		retCode, _ = addAiHero(u, v.HeroId, heroGrade.HeroX, v.Rare)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed. newHero is nil. heroID: %d, ret: %d", v.HeroId, retCode)
			return retCode
		}
	}

	// 添加上阵神器
	retCode, artifact1 := addAiArtifact(u, info.Artifact1, heroGrade.Artifact1)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. newArtifact is nil. artifact1ID: %d, ret: %d", info.Artifact1, retCode)
		return retCode
	}
	retCode, artifact2 := addAiArtifact(u, info.Artifact2, heroGrade.Artifact2)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. newArtifact is nil. artifact2ID: %d, ret: %d", info.Artifact2, retCode)
		return retCode
	}
	retCode, artifact3 := addAiArtifact(u, info.Artifact3, heroGrade.Artifact3)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("create ai tool formation failed. newArtifact is nil. artifact3ID: %d, ret: %d", info.Artifact3, retCode)
		return retCode
	}

	// 添加未上阵的神器全局属性
	for _, v := range TestAiToolsArtifactM.Datas {
		if v.ArtifactId == info.Artifact1 || v.ArtifactId == info.Artifact2 || v.ArtifactId == info.Artifact3 {
			continue
		}
		retCode, _ = addAiArtifact(u, v.ArtifactId, heroGrade.ArtifactX)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("create ai tool formation failed. new Artifact is nil. artifactID:%d, artifactX: %d, ret: %d", v.ArtifactId, heroGrade.ArtifactX, retCode)
			return retCode
		}
	}

	formation := &cl.Formation{
		Id:    fid,
		Teams: make([]*cl.FormationTeamInfo, 0, 1),
	}

	formationTeam := &cl.FormationTeamInfo{}
	formationTeam.Info = append(formationTeam.Info, &cl.FormationInfo{
		Pos: info.Hero1Position,
		Hid: hero1.data.Id,
		Uid: u.dbUser.Id,
	})
	formationTeam.Info = append(formationTeam.Info, &cl.FormationInfo{
		Pos: info.Hero2Position,
		Hid: hero2.data.Id,
		Uid: u.dbUser.Id,
	})
	formationTeam.Info = append(formationTeam.Info, &cl.FormationInfo{
		Pos: info.Hero3Position,
		Hid: hero3.data.Id,
		Uid: u.dbUser.Id,
	})
	formationTeam.Info = append(formationTeam.Info, &cl.FormationInfo{
		Pos: info.Hero4Position,
		Hid: hero4.data.Id,
		Uid: u.dbUser.Id,
	})
	formationTeam.Info = append(formationTeam.Info, &cl.FormationInfo{
		Pos: info.Hero5Position,
		Hid: hero5.data.Id,
		Uid: u.dbUser.Id,
	})

	formationTeam.Artifacts = append(formationTeam.Artifacts, &cl.FormationArtifactInfo{
		Aid: artifact1.data.SysId,
		Pos: 1,
	})
	formationTeam.Artifacts = append(formationTeam.Artifacts, &cl.FormationArtifactInfo{
		Aid: artifact2.data.SysId,
		Pos: 2,
	})
	formationTeam.Artifacts = append(formationTeam.Artifacts, &cl.FormationArtifactInfo{
		Aid: artifact3.data.SysId,
		Pos: 3,
	})

	formation.Teams = append(formation.Teams, formationTeam)
	u.formations.formations[fid] = formation

	return uint32(ret.RET_OK)
}

func addAiArtifact(u *User, id, artifactGrade uint32) (uint32, *Artifact) {
	if u.artifacts == nil {
		u.artifacts = newArtifactM(u)
	}
	if goxml.GetData().ArtifactInfoM.Index(id) == nil {
		l4g.Errorf("create ai tool formation failed. artifactInfo is nil. artifact1ID: %d", id)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}
	developInfo := aiToolsDevelopM.Index(artifactGrade)
	if developInfo == nil {
		l4g.Errorf("create ai tool formation failed. developInfo is nil. artifactGrade: %d", artifactGrade)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}

	artifact := newArtifact(id)
	artifact.data.Star = developInfo.ArtifactStars
	artifact.data.StrengthLv = developInfo.ArtifactStrength

	if _, exist := u.artifacts.artifacts[id]; !exist {
		u.artifacts.artifacts[id] = artifact
	}

	u.artifacts.UpdateArtifactGlobalAttr()

	return uint32(ret.RET_OK), artifact
}

func preparePokemon(u *User) {
	u.pokemonM = newPokemonM(u)
	u.PokemonManager().load(nil)
	if u.PokemonManager().globalData == nil {
		u.PokemonManager().globalData = &db.PokemonGlobal{}
	}
}

func aiPokemonOp(u *User, lineUpInfo *AiToolsLineup, gradeInfo *AiToolsHeroGrade) uint32 {
	// 获取出战宠物配置，pokemonId = 0则不生效
	pokemonLineUpInfo := TestAiToolsPokemonM.Index(lineUpInfo.PokemonBall)
	if pokemonLineUpInfo == nil {
		l4g.Errorf("create ai tool formation failed. pokemonLineUpInfo is nil id: %d", lineUpInfo.PokemonBall)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	// 出战宠物根据模版星级提供全局属性加成（注意：出战宠物技能仅对赛季pvp生效，因为ai_tool的foramtion_id为固定的）
	addAiLineUpPokemons(u, pokemonLineUpInfo, gradeInfo)

	// 未出战宠物根据全局星级提供全局属性加成
	for _, v := range u.pokemonM.pokemons {
		if v.GetData() == nil {
			continue
		}
		if v.GetData().SysId == pokemonLineUpInfo.PokemonId1 || v.GetData().SysId == pokemonLineUpInfo.PokemonId2 || v.GetData().SysId == pokemonLineUpInfo.PokemonId3 {
			continue
		}
		addAiPokemon(u, v.GetData().SysId, gradeInfo.PokemonGlobalStar, 0)
	}

	// 全局潜能等级提供全局属性加成
	u.SetPokemonPotentialLevel(gradeInfo.PokemonGlobalPotantial)

	// 重新计算属性
	u.pokemonM.RecalcGlobalAttr()

	return uint32(ret.RET_OK)
}

func addAiLineUpPokemons(u *User, pokemonInfo *TestAiToolsPokemon, gradeInfo *AiToolsHeroGrade) uint32 {
	if pokemonInfo.PokemonId1 > 0 {
		retCode, _ := addAiPokemon(u, pokemonInfo.PokemonId1, gradeInfo.Pokemon1, 1)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("addAiPokemons: addAiPokemon failed. PokemonId1: %d grade: %d ret: %d", pokemonInfo.PokemonId1, gradeInfo.Pokemon1, retCode)
			return retCode
		}
	}
	if pokemonInfo.PokemonId2 > 0 {
		retCode, _ := addAiPokemon(u, pokemonInfo.PokemonId2, gradeInfo.Pokemon2, 2)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("addAiPokemons: addAiPokemon failed. PokemonId2: %d grade: %d ret: %d", pokemonInfo.PokemonId2, gradeInfo.Pokemon2, retCode)
			return retCode
		}
	}
	if pokemonInfo.PokemonId3 > 0 {
		retCode, _ := addAiPokemon(u, pokemonInfo.PokemonId3, gradeInfo.Pokemon3, 3)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("addAiPokemons: addAiPokemon failed. PokemonId3: %d grade: %d ret: %d", pokemonInfo.PokemonId3, gradeInfo.Pokemon3, retCode)
			return retCode
		}
	}
	return uint32(ret.RET_OK)
}

func addAiPokemon(u *User, pokemonId, pokemonGrade, ballPos uint32) (uint32, *Pokemon) {
	if u.pokemonM == nil {
		u.pokemonM = newPokemonM(u)
	}
	if goxml.GetData().PokemonInfoM.GetRecordByPokemonId(pokemonId) == nil {
		l4g.Errorf("create ai tool formation failed. pokemonInfo is nil. pokemonId: %d", pokemonId)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}
	developInfo := aiToolsDevelopM.Index(pokemonGrade)
	if developInfo == nil {
		l4g.Errorf("create ai tool formation failed. developInfo is nil. pokemonGrade: %d", pokemonGrade)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}

	aiPokemon := newPokemon(pokemonId)
	aiPokemon.SetStar(developInfo.PokemonStars)
	if ballPos > 0 {
		aiPokemon.SetBallPos(ballPos)
	}

	if u.pokemonM.pokemons == nil {
		u.pokemonM.pokemons = make(map[uint32]*Pokemon)
	}
	u.pokemonM.pokemons[pokemonId] = aiPokemon
	return uint32(ret.RET_OK), aiPokemon
}

func aiHandbookOp(u *User, info *AiToolsHeroGrade) uint32 {
	if u.dbUser.ModuleGlobalAttr.HandbooksData == nil {
		u.dbUser.ModuleGlobalAttr.HandbooksData = &cl.Handbooks{}
	}
	u.HandbookManager().load(u.dbUser.ModuleGlobalAttr.HandbooksData)

	situationInfo := aiToolsOverallSituationM.Index(info.OverallSituationId)
	if situationInfo == nil {
		l4g.Errorf("create ai tool formation failed. situationInfo is nil. situationId: %d", info.OverallSituationId)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	tmpHeroIDs := make(map[uint32]struct{})
	for _, v := range u.heroes.heroes {
		tmpHeroIDs[v.data.SysId] = struct{}{}
	}

	linkTaskIDs := strings.Split(situationInfo.LinkBookTaskIds, ",")
	for _, taskID := range linkTaskIDs {
		linkTaskID, err := strconv.ParseUint(taskID, 10, 32)
		if err != nil {
			l4g.Errorf("create ai tool formation failed. crystal get taskID failed.")
			return uint32(ret.RET_ERROR)
		}

		linkBookTaskInfo := goxml.GetData().LinkBookTaskInfoM.Index(uint32(linkTaskID))
		if linkBookTaskInfo == nil {
			l4g.Errorf("create ai tool formation failed. linkBookTaskInfo is nil. linkTaskID: %d", linkTaskID)
			return uint32(ret.RET_SYSTEM_DATA_ERROR)
		}

		handbookM := u.HandbookManager().GetHeroHandbookM()
		if _, exist := tmpHeroIDs[linkBookTaskInfo.HeroGroup]; exist {
			if heroHandbook := handbookM.GetOne(linkBookTaskInfo.HeroGroup); heroHandbook != nil {
				handbookM.ActivateHeroAttr(handbookM.GetOne(linkBookTaskInfo.HeroGroup), linkBookTaskInfo)
				continue
			}
			retCode := handbookM.Add(linkBookTaskInfo.HeroGroup)
			if retCode != uint32(ret.RET_OK) {
				l4g.Errorf("create ai tool formation failed. handbook add failed. heroID: %d ret: %d", linkBookTaskInfo.HeroGroup, retCode)
				return retCode
			}

			heroHandbook := handbookM.GetOne(linkBookTaskInfo.HeroGroup)
			heroHandbook.Link_1StarAttr = goxml.GetData().LinkBookTaskInfoM.GetHeroStarAttrId(linkBookTaskInfo.HeroGroup, 5)

			handbookM.ActivateHeroAttr(handbookM.GetOne(linkBookTaskInfo.HeroGroup), linkBookTaskInfo)
		}
	}

	return uint32(ret.RET_OK)
}

func aiGoddessContractOp(u *User, info *AiToolsHeroGrade) uint32 {
	situationInfo := aiToolsOverallSituationM.Index(info.OverallSituationId)
	if situationInfo == nil {
		l4g.Errorf("create ai tool formation failed. situationInfo is nil. situationId: %d", info.OverallSituationId)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	u.GoddessContract().AddTotalExp(situationInfo.GoddesscontractExp)
	u.GoddessContract().CalcTotalData(situationInfo.GoddesscontractExp, nil)

	return uint32(ret.RET_OK)
}

func aiRemainBookOp(u *User, info *AiToolsHeroGrade) uint32 {
	if u.dbUser.ModuleGlobalAttr.RemainBook == nil {
		u.dbUser.ModuleGlobalAttr.RemainBook = &cl.RemainBook{}
	}
	if u.remains == nil {
		u.remains = newRemainM(u)
	}

	situationInfo := aiToolsOverallSituationM.Index(info.OverallSituationId)
	if situationInfo == nil {
		l4g.Errorf("create ai tool formation failed. situationInfo is nil. situationId: %d", info.OverallSituationId)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	u.RemainM().loadRemainBook(u.dbUser.ModuleGlobalAttr.RemainBook)
	levelInfo := goxml.GetData().RemainBlessInfoM.Index(situationInfo.RemainBookLevel)
	if levelInfo == nil {
		l4g.Errorf("u:%d remian book level:%d is nil", u.ID(), situationInfo.RemainBookLevel)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	u.RemainM().RemainBook().SetLevelForGm(levelInfo)
	return uint32(ret.RET_OK)
}

func aiSeasonLinkMonumentOp(u *User, info *AiToolsHeroGrade) uint32 {
	developInfo := aiToolsDevelopM.Index(info.Season1LinkX)
	if developInfo == nil {
		l4g.Errorf("create ai tool seasonlink monument failed. developInfo is nil. Season1LinkX: %d", info.Season1LinkX)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}
	// 将赛季丰碑根据玩法等级进行初始化
	sl := u.SeasonLink()
	for _, v := range goxml.GetData().SeasonLinkMonumentInfoM.GetRecordsBySeasonId(developInfo.SeasonId) {
		monumentId := v.Id
		monument := newMonumentByIdAndUniqueLevel(monumentId, developInfo.Season1LinkLevel)
		sl.monuments[monumentId] = monument
		sl.SetChange(monument)
	}
	return uint32(ret.RET_OK)
}
