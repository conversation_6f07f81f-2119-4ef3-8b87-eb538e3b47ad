package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
)

type GuildTalent struct {
	owner *User
	data  *cl.GuildTalent

	// 第一、二层的节点拥有的全局属性：加入公会时有值，退出公会时没有值
	globalAttrs map[uint32]*cl.AttrInfo
	// 第三层节点：用于计算被动技能：加入公会时有值，退出公会时没有值
	thirdNodePool map[uint32]map[uint32][]uint32 // key: parentNode -> childNodeType -> 第三层的子节点kind
	score         int64
}

func newGuildTalent(user *User) *GuildTalent {
	return &GuildTalent{
		owner:         user,
		data:          newData(),
		globalAttrs:   make(map[uint32]*cl.AttrInfo),
		thirdNodePool: make(map[uint32]map[uint32][]uint32),
	}
}

func newData() *cl.GuildTalent {
	data := &cl.GuildTalent{
		Talents: make(map[uint32]uint32),
	}
	// 根节点默认激活
	data.Talents[goxml.GuildTalentRootNode] = 0

	return data
}

func (g *GuildTalent) load(data *cl.GuildTalent) {
	g.data = data
	if g.data != nil {
		g.loadAttrAndThirdNodePool()
	} else {
		g.data = newData()
	}
}

func (g *GuildTalent) loadAttrAndThirdNodePool() {
	if g.data.Talents == nil {
		g.data.Talents = make(map[uint32]uint32)
		// 根节点默认激活
		g.data.Talents[goxml.GuildTalentRootNode] = 0
	}
	for kind, level := range g.data.Talents {
		talentInfo := goxml.GetData().GuildTalentInfoM.Index(kind, level)
		if talentInfo == nil {
			continue
		}
		if talentInfo.Floor == goxml.GuildTalentFloorThird {
			g.addThirdNodePool(kind, talentInfo)
		} else {
			g.addAttrs(kind, talentInfo.Attr)
		}
	}
}

func (g *GuildTalent) Save() {
	if g.Owner().dbUser.ModuleGlobalAttr.GuildTalent == nil {
		g.Owner().dbUser.ModuleGlobalAttr.GuildTalent = g.data
	}
	g.Owner().setSaveTag(saveTagModuleGlobalAttr)
}

func (g *GuildTalent) addGlobalAttr(kind uint32) {
	delete(g.globalAttrs, kind)
	level := g.data.Talents[kind]
	if level == 0 {
		return
	}

	talentInfo := goxml.GetData().GuildTalentInfoM.Index(kind, level)
	if talentInfo == nil {
		return
	}
	g.addAttrs(kind, talentInfo.Attr)
}

func (g *GuildTalent) addAttrs(kind uint32, attrs map[uint32]int64) {
	if _, exist := g.globalAttrs[kind]; !exist {
		g.globalAttrs[kind] = &cl.AttrInfo{}
	}
	g.globalAttrs[kind].Attr = attrs
}

func (g *GuildTalent) addThirdNodePool(kind uint32, tInfo *goxml.GuildTalentInfoExt) {
	if _, exist := g.thirdNodePool[tInfo.ParentNode]; !exist {
		g.thirdNodePool[tInfo.ParentNode] = make(map[uint32][]uint32)
	}
	g.thirdNodePool[tInfo.ParentNode][tInfo.ChildNodeType] = append(g.thirdNodePool[tInfo.ParentNode][tInfo.ChildNodeType], kind)
}

func (g *GuildTalent) Owner() *User {
	return g.owner
}

func (g *GuildTalent) Flush() *cl.GuildTalent {
	return g.data.Clone()
}

func (g *GuildTalent) GetTalent() *cl.GuildTalent {
	return g.data
}

func (g *GuildTalent) GetLevel(kind uint32) uint32 {
	return g.data.Talents[kind]
}

func (g *GuildTalent) GetGuildTalentFloor(floor uint32) GuildTalentLevelUp {
	if floor == goxml.GuildTalentFloorFirst {
		return GuildTalentFirstFloor(floor)
	} else if floor == goxml.GuildTalentFloorSecond {
		return GuildTalentSecondFloor(floor)
	} else {
		return GuildTalentThirdFloor(floor)
	}
}

func (g *GuildTalent) checkPreNodes(tInfo *goxml.GuildTalentInfoExt) uint32 {
	switch tInfo.PreConnectType {
	case goxml.GuildTalentConnectTypeAnd:
		for _, preNode := range tInfo.PreNodes {
			level, exist := g.data.Talents[preNode]
			if !exist {
				return uint32(ret.RET_GUILD_TALENT_PRE_NODE_NOT_ACTIVATE)
			}
			if level < tInfo.PreLimit {
				return uint32(ret.RET_GUILD_TALENT_PRE_NODE_LEVEL_NOT_ENOUGH)
			}
		}
		return uint32(ret.RET_OK)

	case goxml.GuildTalentConnectTypeOr:
		for _, preNode := range tInfo.PreNodes {
			if level, exist := g.data.Talents[preNode]; exist && level >= tInfo.PreLimit {
				return uint32(ret.RET_OK)
			}
		}
		return uint32(ret.RET_GUILD_TALENT_PRE_NODE_NOT_ACTIVATE)

	}

	return uint32(ret.RET_GUILD_TALENT_CONNECT_TYPE_NOT_INVALID)
}

// Reset ：职业线重置
func (g *GuildTalent) Reset(kind uint32) {
	delete(g.data.Talents, kind)
	delete(g.globalAttrs, kind)
	for _, v := range goxml.GetData().GuildTalentInfoM.GetThirdNodes(kind) {
		delete(g.data.Talents, v)
	}
	delete(g.thirdNodePool, kind)
	g.updateGuildTalentGlobalAttr(kind)
	g.resetToken(kind)
}

func (g *GuildTalent) resetToken(kind uint32) {
	tokenValue := goxml.GetData().GuildTalentInfoM.SecondLevelUpAward[kind]
	count := g.owner.GetResourceCount(uint32(common.RESOURCE_TOKEN), tokenValue)
	if count == 0 {
		return
	}
	g.owner.ResetToken(goxml.GetData().GuildTalentInfoM.SecondLevelUpAward[kind])
	cost := &cl.Resource{
		Type:  uint32(common.RESOURCE_TOKEN),
		Value: tokenValue,
		Count: uint32(count),
	}
	msg := &cl.L2C_OpResources{Reduce: []*cl.Resource{cost}}
	g.owner.SendCmdToGateway(cl.ID_MSG_L2C_OpResources, msg)
}

func (g *GuildTalent) updateGuildTalentGlobalAttr(kind uint32) {
	g.addGlobalAttr(kind)
	g.SetScore(g.CalcScore())
	g.sendGlobalAttrToClient()
}

func (g *GuildTalent) calcGlobalAttr() *cl.AttrInfo {
	if len(g.globalAttrs) > 0 {
		globalAttr := &cl.AttrInfo{
			Attr: make(map[uint32]int64),
		}
		for _, attrInfo := range g.globalAttrs {
			for attr, value := range attrInfo.Attr {
				globalAttr.Attr[attr] += value
			}
		}

		return globalAttr
	}

	return nil
}

func (g *GuildTalent) FlushGuildTalentGlobalAttr() map[uint32]*cl.AttrInfo {
	newAttr := g.calcGlobalAttr()
	if newAttr == nil {
		return nil
	}

	globalAttr := make(map[uint32]*cl.AttrInfo, 1)
	// key == 0: 前端用来当做全局属性
	globalAttr[0] = newAttr

	return globalAttr
}

func (g *GuildTalent) sendGlobalAttrToClient() {
	globalAttr := &cl.GlobalAttr{
		GuildAttr: g.FlushGuildTalentGlobalAttr(),
	}

	msg := &cl.L2C_OpGlobalAttr{
		GlobalAttr: globalAttr,
	}
	g.owner.SendCmdToGateway(cl.ID_MSG_L2C_OpGlobalAttr, msg)
}

func (g *GuildTalent) SendGuildTalentToClient() {
	msg := &cl.L2C_GuildTalentList{
		Ret:  uint32(ret.RET_OK),
		List: g.Flush(),
	}
	g.owner.SendCmdToGateway(cl.ID_MSG_L2C_GuildTalentList, msg)
}

func (g *GuildTalent) UserGuildChange(user *User, srv servicer) {
	user.HeroManager().SetHeroAttrChange()
	user.UpdateAllPower(srv, PowerUpdateByNormalRaise)
}

// TestSetGuildTalentLevel : 设置公会天赋等级, 供测试逻辑使用
func (g *GuildTalent) TestSetGuildTalentLevel(kind, level uint32) {
	if g.data.Talents == nil {
		g.data.Talents = make(map[uint32]uint32)
	}

	g.data.Talents[kind] = level
	g.loadAttrAndThirdNodePool()
	g.updateGuildTalentGlobalAttr(kind)
	g.Save()
}

func (g *GuildTalent) CalcScore() int64 {
	var score int64
	for _, attrs := range g.globalAttrs {
		score += goxml.GetData().AttributeInfoM.CalcGlobalScoreMap(attrs.Attr)
	}
	return score
}

func (g *GuildTalent) SetScore(score int64) {
	g.score = score
}

func (g *GuildTalent) GetScore() int64 {
	return g.score
}

func (g *GuildTalent) GetRaisePSs() []uint64 {
	if g.owner.UserGuild().GuildID() == 0 {
		return nil
	}

	raisePSs := g.calRaisePSs()
	if len(raisePSs) == 0 {
		return nil
	}

	unrepeated := make(map[uint64]struct{}, len(raisePSs))
	for _, ps := range raisePSs {
		unrepeated[ps] = struct{}{}
	}
	raisePSs = raisePSs[:0]
	for ps := range unrepeated {
		raisePSs = append(raisePSs, ps)
	}

	return raisePSs
}

func (g *GuildTalent) calRaisePSs() []uint64 {
	raisePSs := make([]uint64, 0, len(g.thirdNodePool)*2)
	for _, v := range g.thirdNodePool {
		for _, kinds := range v {
			// 从 nodes 中找最大的
			kind := getMaxKind(kinds)
			talentInfo := goxml.GetData().GuildTalentInfoM.Index(kind, 0)
			if talentInfo == nil {
				continue
			}
			// talentInfo.ConditionTypes：在量表中已逆序
			raisePSs = append(raisePSs, g.getMatchPassSkill(talentInfo.ConditionTypes)...)
		}
	}

	return raisePSs
}

func (g *GuildTalent) getMatchPassSkill(conditionTypes []uint32) []uint64 {
	raisePSs := make([]uint64, 0, 3) //nolint:mnd
	// 逆向查找：谁满足谁退出
	for _, id := range conditionTypes {
		conditionInfo := goxml.GetData().GuildTalentConditionInfoM.Index(id)
		if conditionInfo == nil {
			continue
		}
		isExist := true
		// 配置的节点必须都满足，才算激活被动技能
		for _, node := range conditionInfo.ConditionNodes {
			if _, exist := g.data.Talents[node]; !exist {
				isExist = false
				break
			}
		}
		// 条件节点都激活，才匹配被动技能
		if isExist {
			raisePSs = append(raisePSs, conditionInfo.RaisePSs...)
			return raisePSs
		}
	}

	return raisePSs
}

func getMaxKind(nodes []uint32) uint32 {
	if len(nodes) > 0 {
		maxNode := nodes[0]
		for _, node := range nodes {
			if maxNode < node {
				maxNode = node
			}
		}

		return maxNode
	}

	return 0
}
