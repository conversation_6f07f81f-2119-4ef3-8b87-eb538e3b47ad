package character

import (
	"app/goxml"
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

// ShopInit : 商店初始化接口；不同的type，初始化方式不同
type ShopInit interface {
	init(servicer, *User, *goxml.ShopInfoExt) // 刷新商店数据
}

type ShopInitRandom cl.Shop // 随机商店

func (s *ShopInitRandom) init(srv servicer, u *User, shopInfo *goxml.ShopInfoExt) {
	list := goxml.GetData().ShopInfoM.GenerateRandomGoodsList(srv.Rand(), shopInfo.Id, u.Level())
	if list == nil {
		l4g.Errorf("InitRandomShop: GenerateRandomGoodsList err. shopID:%d level:%d", shopInfo.Id, u.Level())
		return
	}
	vipFreeRefreshAdd := uint32(0)
	if shopInfo.Id == goxml.VipPrivilegeShopId {
		vipFreeRefreshAdd += goxml.GetData().VipPrivilegeInfoM.GetShopFreeRefreshAdd(goxml.VipPrivilegeShopId, u.Vip())
	}

	s.FreeRefreshLeftNum = shopInfo.FreeRefreshLimit + vipFreeRefreshAdd
	s.FreeRefreshRecoverTm = time.Now().Unix()
	s.GoodsList = list
	s.PaidRefreshResetTm = goxml.CalcLastDailyResetTime(shopInfo.PaidRefreshLimitResetTm)
}

type ShopInitRound cl.Shop // 轮次商店

func (s *ShopInitRound) init(srv servicer, u *User, shopInfo *goxml.ShopInfoExt) {
	round, resetTime := calcRoundAndResetTime(srv, int64(shopInfo.ShopResetInterval), shopInfo.Id)
	s.update(round, resetTime)
}

func (s *ShopInitRound) update(round uint32, resetTime int64) {
	s.GoodsList = make(map[uint32]uint32)
	s.Round = round
	s.ShopResetTm = resetTime
}
