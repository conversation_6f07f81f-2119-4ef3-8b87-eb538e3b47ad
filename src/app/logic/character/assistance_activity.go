package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"gitlab.qdream.com/kit/sea/time"
)

type AssistanceActivity struct {
	owner *User
	data  *cl.AssistanceActivity
}

func newAssistanceActivity(user *User) *AssistanceActivity {
	return &AssistanceActivity{
		owner: user,
	}
}

func (a *AssistanceActivity) Owner() *User {
	return a.owner
}

func (a *AssistanceActivity) load(data *cl.AssistanceActivity) {
	a.data = data
	if a.data == nil {
		a.data = &cl.AssistanceActivity{}
	}
	if a.data.RecvAward == nil {
		a.data.RecvAward = make(map[uint32]bool)
	}
}

func (a *AssistanceActivity) Save() {
	a.Owner().dbUser.Module5.AssistanceActivity = a.data
	a.owner.setSaveTag(saveTagModule5)
}

func (a *AssistanceActivity) NeedReset() (*goxml.AssistanceActivityOpenInfoExt, bool) {
	now := time.Now().Unix()
	actInfo := goxml.GetData().AssistanceActivityOpenInfoM.GetCurrent(now)
	if actInfo == nil {
		//没有活动数据且之前没参加过活动
		if a.data.ActId == 0 {
			return actInfo, false
		} else { //没有活动数据之前参加过活动
			return actInfo, true
		}
	}

	//有活动且跟当前玩家的活动不一样
	return actInfo, actInfo.Id != a.data.ActId
}

func (a *AssistanceActivity) Reset(info *goxml.AssistanceActivityOpenInfoExt) {
	if info == nil {
		a.data = &cl.AssistanceActivity{}
	} else {
		a.data = &cl.AssistanceActivity{
			ActId:     info.Id,
			RecvAward: make(map[uint32]bool),
		}
	}
	a.Save()
}

func (a *AssistanceActivity) Flush2Client() *cl.AssistanceActivity {
	return a.data.Clone()
}

func (a *AssistanceActivity) GetActID() uint32 {
	return a.data.GetActId()
}

func (a *AssistanceActivity) IsOpen(now int64) bool {
	return goxml.GetData().AssistanceActivityOpenInfoM.IsOpen(a.data.GetActId(), now)
}

func (a *AssistanceActivity) GetOpenTimeDuration(now int64) (int64, bool) {
	sysId := a.data.GetActId()
	if sysId == 0 {
		return 0, false
	}
	openInfo := goxml.GetData().AssistanceActivityOpenInfoM.Index(sysId)
	if openInfo == nil {
		return 0, false
	}

	if openInfo.OpenTime > now {
		return 0, false
	}
	return now - openInfo.OpenTime, true
}

func (a *AssistanceActivity) IsRecvAward(awardId uint32) bool {
	recv, exist := a.data.RecvAward[awardId]
	if !exist {
		return true
	}
	return !recv
}

func (a *AssistanceActivity) SetRecvAward(awardId []uint32) {
	for _, Id := range awardId {
		a.data.RecvAward[Id] = true
	}
}
