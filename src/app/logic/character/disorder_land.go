package character

import (
	"app/goxml"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/time"
)

type DisorderLand struct {
	owner *User
	data  *cl.Disorderland
}

func newDisorderLand(user *User) *DisorderLand {
	return &DisorderLand{
		owner: user,
		data:  createDisorderLand(),
	}
}

func createDisorderLand() *cl.Disorderland {
	return &cl.Disorderland{
		Map:            make(map[uint32]*cl.DisorderlandMap, len(goxml.GetData().DisorderlandInfoM.Datas)),
		PassMaxLevel:   make(map[uint32]uint32, goxml.GetData().DisorderlandDungeonInfoM.GetMaxType()),
		GuaranteeCount: make(map[uint64]uint32),
	}
}

func (d *DisorderLand) load(data *cl.Disorderland) {
	d.data = data
	if d.data == nil {
		d.data = createDisorderLand()
	}
	if d.data.GuaranteeCount == nil {
		d.data.GuaranteeCount = make(map[uint64]uint32)
	}
}

func (d *DisorderLand) Flush() *cl.Disorderland {
	return d.data.Clone()
}

func (d *DisorderLand) Save() {
	d.owner.dbUser.Disorderland.DisorderlandData = d.data
	d.owner.setSaveTag(saveTagDisorderland)
}

func (d *DisorderLand) GetSeasonIDFromSeason() uint32 {
	return d.owner.GetSeasonID()
}

func (d *DisorderLand) GetSeasonLevelFromSeason() uint32 {
	return d.owner.GetSeasonLevel()
}

func (d *DisorderLand) GetSeasonDayFromSeason() uint32 {
	return d.owner.SeasonOpenDay(time.Now().Unix())
}

func (d *DisorderLand) GetSeasonLinkMonumentNumWithLevel(monumentLevel uint32) uint32 {
	count := uint32(0)
	monuments := d.owner.SeasonLink().getAllMonument()
	for _, m := range monuments {
		if m.GetUniqueLevel() >= monumentLevel {
			count++
		}
	}

	return count
}

// 从丰碑中根据品质和等级获取最小的一个位置的符文
func (d *DisorderLand) findMinMonumentRune(monumentID uint32) *cl.MonumentRune {
	monument := d.owner.SeasonLink().GetMonumentById(monumentID)
	if monument == nil {
		return nil
	}
	runes := monument.GetRunes()
	if len(runes) == 0 {
		return nil
	}

	var (
		min   *goxml.SeasonLinkRuneInfo
		index int
	)
	for i, r := range runes {
		rInfo := goxml.GetData().SeasonLinkRuneInfoM.GetRecordById(r.GetRuneSysId())
		if rInfo == nil {
			continue
		}
		if i == 0 {
			min = rInfo
			index = i
		} else {
			if min.UniqueLevel > rInfo.UniqueLevel {
				min = rInfo
				index = i
			}
		}
	}

	return runes[index]
}

func (d *DisorderLand) randMonumentRune(srv servicer, monumentID uint32) *cl.MonumentRune {
	monument := d.owner.SeasonLink().GetMonumentById(monumentID)
	if monument == nil {
		return nil
	}
	runes := monument.GetRunes()
	if len(runes) == 0 {
		return nil
	}
	index := srv.Rand().RandBetween(0, len(runes)-1)

	return runes[index]
}

func (d *DisorderLand) isSeasonOpen() bool {
	return d.GetSeasonIDFromSeason() > 0
}

func (d *DisorderLand) OnSeasonEnd(srv servicer, needSettleAward bool) {
	d.clearToken(srv)
	d.clearFormation()
	d.clearStaminaBuyCount()
}

func (d *DisorderLand) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	d.data = createDisorderLand()
	d.Save()
	for i := range common.DISORDER_LAND_HURDLE_TYPE_name {
		if i == int32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_NONE) || i == int32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_MAX) {
			continue
		}
		d.owner.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_DISORDER_LAND), uint32(i), 0)
	}
}

func (d *DisorderLand) clearStaminaBuyCount() {
	numberType := uint32(common.PURCHASEID_DISORDER_LAND_BUY_COUNT)
	info := goxml.GetData().NumberTypeInfoM.Index(numberType)
	if info == nil {
		l4g.Errorf("user: %d clearStaminaBuyCount: numberTypeInfo is nil. numberType: %d", d.owner.ID(), numberType)
		return
	}
	data := d.owner.getUserNumData(numberType, info.RefreshType)
	if data == nil {
		return
	}
	if data.UseNum > 0 || data.PurchaseNum > 0 {
		data.UseNum = 0
		data.PurchaseNum = 0
		d.owner.saveNum(info.RefreshType)
		d.owner.sendOpNumToClient(numberType, info.RefreshType)
	}
}

func (d *DisorderLand) clearFormation() {
	formationM := d.owner.FormationManager()
	for _, id := range goxml.DisorderLandFormationPool {
		formationM.Delete(id)
	}
}

func (d *DisorderLand) Init(srv servicer) {
	now := time.Now().Unix()
	seasonID := goxml.GetCurrentSeasonID(goxml.GetData(), now)
	funcID := uint32(common.FUNCID_MODULE_DISORDER_LAND)
	if goxml.IsFuncInSeason(goxml.GetData(), seasonID, funcID) && d.owner.IsFunctionOpen(funcID, srv) {
		if !d.data.IsFirst {
			d.initDefaultStaminaNum(srv)
			d.initStaminaRecoveryTime()
			d.initFormation()
			d.data.IsFirst = true
			d.Save()
		}
	}
}

func (d *DisorderLand) initFormation() {
	for _, id := range goxml.DisorderLandFormationPool {
		d.cloneAndSetFormationFromSeasonDungeon(id)
	}
}

func (d *DisorderLand) cloneAndSetFormationFromSeasonDungeon(formationID uint32) {
	sFormation := d.owner.GetFormation(uint32(common.FORMATION_ID_FI_SEASON_DUNGEON))
	if sFormation == nil || len(sFormation.Teams) == 0 {
		return
	}
	// clone获取赛季主线阵容
	clone := sFormation.Clone()
	clone.Id = formationID
	team := clone.Teams[goxml.FormationTeamOneIndex]
	if team.RiteInfo != nil {
		team.RiteInfo = nil
	}

	d.setFormation(formationID, clone)
}

func (d *DisorderLand) setFormation(formationID uint32, formation *cl.Formation) {
	formationM := d.owner.FormationManager()
	if formationM.formations == nil {
		formationM.formations = make(map[uint32]*cl.Formation)
	}
	formationM.formations[formationID] = formation
	formationM.changes[formationID] = formation
}

func (d *DisorderLand) clearToken(srv servicer) {
	awards := make([]*cl.Resource, 0, 2)
	for _, id := range goxml.GetData().DisorderlandConfigInfoM.DelLastSeasonTokens {
		num := d.owner.tokensBag()[id]
		if num > 0 {
			awards = append(awards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), id, uint32(num)))
		}
	}
	if len(awards) > 0 {
		retCode := d.owner.Consume(srv, awards, uint32(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_STAMINA_OR_KEY), 0)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d clearToken: cost token failed. retCode: %d", d.owner.ID(), retCode)
			return
		}
	}
}

// 设置体力的初始值
func (d *DisorderLand) initDefaultStaminaNum(srv servicer) {
	res := make([]*cl.Resource, 0, 1)
	res = append(res, goxml.GetData().DisorderlandConfigInfoM.DefaultStamina)
	d.addStamina(srv, res)
}

func (d *DisorderLand) initStaminaRecoveryTime() {
	d.data.StaminaRecoverTime = time.Now().Unix()
	d.Save()
}

// StaminaRecover 体力恢复
func (d *DisorderLand) StaminaRecover(srv servicer) uint64 {
	if d.GetStaminaRecoverTime() == 0 {
		return d.getStamina()
	}

	configM := goxml.GetData().DisorderlandConfigInfoM
	staminaID := configM.StaminaToken
	staminaNumLimit := configM.StaminaLimit
	recoverTimeSec := int64(configM.StaminaRecoverTimeSec)
	leftGrowCap := uint64(0)
	if staminaNumLimit > d.getStamina() {
		leftGrowCap = staminaNumLimit - d.getStamina()
	}

	now := time.Now().Unix()
	if leftGrowCap == 0 {
		d.setStaminaRecoverTime(now)
		return d.getStamina()
	}

	recoverNum := uint64((now - d.GetStaminaRecoverTime()) / recoverTimeSec)
	if recoverNum > 0 {
		leftTime := int64(0)
		if leftGrowCap <= recoverNum {
			recoverNum = leftGrowCap
		} else {
			leftTime = now - d.GetStaminaRecoverTime() - int64(recoverNum)*recoverTimeSec // 把未用完的时间保留下来，避免丢失时间
		}
		d.setStaminaRecoverTime(now - leftTime)
		token := goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), staminaID, uint32(recoverNum))
		d.addStamina(srv, []*cl.Resource{token})
	}

	return d.getStamina()
}

func (d *DisorderLand) GetStaminaRecoverTime() int64 {
	return d.data.StaminaRecoverTime
}

func (d *DisorderLand) setStaminaRecoverTime(time int64) {
	d.data.StaminaRecoverTime = time
	d.Save()
}

func (d *DisorderLand) getStamina() uint64 {
	return d.owner.tokensBag()[goxml.GetData().DisorderlandConfigInfoM.StaminaToken]
}

func (d *DisorderLand) addStamina(srv servicer, tokens []*cl.Resource) {
	retCode, _ := d.owner.Award(srv, tokens, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_STAMINA_OR_KEY), 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d addKeyOrStamina: send award failed. retCode: %d", d.owner.ID(), retCode)
		return
	}
}

func (d *DisorderLand) GetMapM(difficulty uint32) *DisorderLandMap {
	dMap := d.data.Map[difficulty]
	if dMap == nil {
		return d.initMap(difficulty)
	}

	return (*DisorderLandMap)(dMap)
}

func (d *DisorderLand) initMap(difficulty uint32) *DisorderLandMap {
	dMap := &cl.DisorderlandMap{}
	if d.data.Map == nil {
		d.data.Map = make(map[uint32]*cl.DisorderlandMap)
	}
	d.data.Map[difficulty] = dMap
	d.Save()

	return (*DisorderLandMap)(dMap)
}

// GetDegreeOfNodeFinish
// @Description: 获取失序空间完成度
// @receiver d
// @param difficulty 难度
// @return uint32
func (d *DisorderLand) GetDegreeOfNodeFinish(difficulty uint32) uint32 {
	mapM := d.GetMapM(difficulty)
	if mapM == nil {
		return 0
	}

	info := goxml.GetData().DisorderlandInfoM.Index(difficulty)
	if info == nil {
		return 0
	}

	totalNum := goxml.GetData().DisorderlandMapInfoM.GetNodeNum(info.MapId)
	if totalNum == 0 {
		return 0
	}
	return mapM.getCompleteNodeNum() * 10000 / totalNum // 万分比
}

func (d *DisorderLand) GetMaxHurdleLevel(hurdleType uint32) uint32 {
	return d.data.PassMaxLevel[hurdleType]
}

func (d *DisorderLand) isUpdateHurdleLevel(hurdleType, hurdleLevel uint32) bool {
	if level, exist := d.data.PassMaxLevel[hurdleType]; exist {
		// >=的目的，关卡事件可以重复打，如数据同步失败，再打一次，可以再同步一次
		// 如果只 > 的话, 最后一级同步失败的话，会导致游戏服数据和跨服排行榜数据不一致
		return hurdleLevel >= level
	}

	return true
}

func (d *DisorderLand) updateHurdleLevel(hurdleType, hurdleLevel uint32) {
	if d.data.PassMaxLevel == nil {
		d.data.PassMaxLevel = make(map[uint32]uint32)
	}
	if hurdleLevel < d.data.PassMaxLevel[hurdleType] {
		return
	}
	d.data.PassMaxLevel[hurdleType] = hurdleLevel
	d.owner.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_DISORDER_LAND), hurdleType, hurdleLevel)
}

func (d *DisorderLand) updateSweepCount() {
	d.data.SweepCount++
	if d.data.SweepCount >= goxml.GetData().DisorderlandConfigInfoM.GuaranteedSweepCount {
		d.data.SweepCount = 0
	}
}

func (d *DisorderLand) getSweepCount() uint32 {
	return d.data.SweepCount
}

func (d *DisorderLand) flushPassMaxLevel() map[uint32]uint32 {
	clone := make(map[uint32]uint32)
	for k, v := range d.data.PassMaxLevel {
		clone[k] = v
	}

	return clone
}

func (d *DisorderLand) CheckHaveNoCompleteWithRedPoint(difficulty uint32) bool {
	if !d.isSeasonOpen() {
		return false
	}
	mapM := d.GetMapM(difficulty)
	if mapM == nil {
		return true
	}
	info := goxml.GetData().DisorderlandInfoM.Index(difficulty)
	if info == nil {
		return true
	}

	if mapM.getCompleteNodeNum() >= goxml.GetData().DisorderlandMapInfoM.GetNodeNum(info.MapId) {
		return false
	}

	return true
}

func (d *DisorderLand) CheckStaminaIsFullWithRedPoint(srv servicer) bool {
	d.StaminaRecover(srv)
	return d.getStamina() >= goxml.GetData().DisorderlandConfigInfoM.StaminaLimit
}

// 测试扫荡
//
//nolint:varnamelen
func TestSweep(srv servicer, d *DisorderLand, sMsg *cl.L2C_DisorderlandTestSweep, dungeonInfo *goxml.DisorderlandDungeonInfoExt) bool {
	monumentID := dungeonInfo.MonumentId
	dropID := dungeonInfo.Drop

	mRune := d.randMonumentRune(srv, monumentID)
	if mRune == nil {
		l4g.Errorf("user: %d TestSweep: monumentRune is nil. monumentID:%d", d.owner.ID(), monumentID)
		sMsg.Ret = uint32(ret.RET_DISORDER_LAND_GET_RUNE_FAILED)
		return false
	}
	runeInfo := goxml.GetData().SeasonLinkRuneInfoM.GetRecordById(mRune.GetRuneSysId())
	if runeInfo == nil {
		l4g.Errorf("user: %d TestSweep: runeInfo is nil. runeID:%d", d.owner.ID(), mRune.GetRuneSysId())
		sMsg.Ret = uint32(ret.RET_SYSTEM_DATA_ERROR)
		return false
	}

	rLevel := runeInfo.UniqueLevel
	rPos := runeInfo.Position
	improvedRLevel := rLevel + 1

	infoM := goxml.GetData().DisorderlandDropGroupInfoM

	// 掉落判定，获取有效符石等级
	isImprove, group, realRLevel := goxml.GetData().DisorderlandDropInfoM.IsImprove(srv.Rand(), dropID, improvedRLevel)

	// 记录有效符石等级
	if _, exist := sMsg.Data[realRLevel]; !exist {
		sMsg.Data[realRLevel] = &cl.SweepTestData{TotalCount: 1}
	} else {
		sMsg.Data[realRLevel].TotalCount++
	}

	var isGuaranteeExist bool // 判断是否存在保底
	dropInfo := goxml.GetData().DisorderlandDropInfoM.GetDataByIdLevel(dropID, realRLevel)
	if dropInfo == nil {
		return false
	}
	if dropInfo.Num > 0 {
		isGuaranteeExist = true
	}

	var isGuaranteeTrigger bool // 判断触发保底
	if isGuaranteeExist {
		isGuaranteeTrigger, _ = checkGuarantee(d, dropID, realRLevel, dropInfo.Num)
		if isGuaranteeTrigger {
			isImprove = true
			sMsg.Data[realRLevel].ImproveCount++   // 记录提升次数
			sMsg.Data[realRLevel].GuaranteeCount++ // 记录保底触发次数
		}
	}

	// case1：高等级符石,重置保底计数
	if isImprove {
		sMsg.Ret, _ = infoM.GetAwardWithImprove(group, rPos, improvedRLevel)
		if sMsg.Ret != uint32(ret.RET_OK) {
			return false
		}
		if isGuaranteeExist { // 存在保底
			resetSingleGuaranteeCount(d, dropID, realRLevel)
		}
		sMsg.Data[realRLevel].ImproveCount++ // 记录提升次数
		return true
	}

	// case2：低等级符石，增加保底计数
	sMsg.Ret, _ = infoM.RandAwardWithNoImprove(srv.Rand(), group, rPos, rLevel)
	if sMsg.Ret != uint32(ret.RET_OK) {
		return false
	}
	if isGuaranteeExist { //存在保底
		addSingleGuaranteeCount(d, dropID, realRLevel)
	}
	return true
}

func (d *DisorderLand) ResetDaily(srv servicer) {
	d.data.GuildMedalAdd = false
	d.HandleGuildMedalAdd(srv)
	d.Save()
}

func (d *DisorderLand) HandleGuildMedalAdd(srv servicer) {
	if !d.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_DISORDER_LAND), srv) {
		return
	}

	if d.data.GuildMedalAdd {
		return
	}

	guildMedal := srv.GetGuildMedal(d.owner.ID())
	if guildMedal == nil {
		return
	}

	if util.DailyZeroByTime(guildMedal.SyncTm) != util.DailyZero() {
		return
	}

	addValue := uint32(0)
	for _, medal := range guildMedal.Medals {
		info := goxml.GetData().GuildMedalTaskInfoM.Index(medal.GetMedalId())
		if info == nil {
			continue
		}
		if info.AddType != goxml.GuildMedalDisorderLand {
			continue
		}
		addValue += info.AddValue
	}

	if addValue == 0 {
		return
	}

	award := goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), goxml.GetData().DisorderlandConfigInfoM.StaminaToken, addValue)
	l4g.Debugf("user:%d HandleGuildMedalAdd: award:%+v", d.owner.ID(), award)
	retCode, _ := d.owner.Award(srv, []*cl.Resource{award}, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GUILD_MEDAL_DISORDER_LAND_ADD), 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("user:%d HandleGuildMedalAdd: award error. retCode:%d", d.owner.ID(), retCode)
	}
	d.data.GuildMedalAdd = true
	d.Save()
}
