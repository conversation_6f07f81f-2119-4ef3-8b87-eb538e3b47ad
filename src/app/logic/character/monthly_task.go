package character

import (
	"app/goxml"

	//"app/goxml"
	"app/protos/out/cl"

	//"app/protos/out/common"
	"app/protos/out/ret"
	//l4g "github.com/ivanabc/log4go"
)

type MonthlyTask struct {
	owner *User
	data  *cl.MonthlyTask
}

func newMonthlyTask(user *User) *MonthlyTask {
	return &MonthlyTask{
		owner: user,
	}
}

func (d *MonthlyTask) Owner() *User {
	return d.owner
}

// 加载数据
func (d *MonthlyTask) load(data *cl.MonthlyTask) {
	d.data = data
	if d.data == nil {
		d.data = &cl.MonthlyTask{}
	}
	if d.data.TaskTypeProgress == nil {
		d.data.TaskTypeProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if d.data.Awarded == nil {
		d.data.Awarded = make(map[uint32]bool)
	}
}

// 设置保存标签, 保存数据
func (d *MonthlyTask) save() {
	if d.Owner().dbUser.Module4.MonthlyInfo.MonthlyTask == nil {
		d.Owner().dbUser.Module4.MonthlyInfo.MonthlyTask = d.data
	}
	d.Owner().setSaveTag(saveTagModule4)
}

// clone内存数据
func (d *MonthlyTask) Flush() *cl.MonthlyTask {
	return d.data.Clone()
}

func (d *MonthlyTask) GetData() *cl.MonthlyTask {
	return d.data
}

func (d *MonthlyTask) GetProgress() map[uint32]*cl.TaskTypeProgress {
	if d.data == nil {
		return nil
	}
	return d.data.TaskTypeProgress
}

func (d *MonthlyTask) GetOneTypeProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return d.owner.CalcTaskProgress(taskTypeInfo)
	}
	if d.data == nil {
		return nil
	}
	return d.data.TaskTypeProgress[taskTypeInfo.Id]
}

func (d *MonthlyTask) ReceiveAward(ids []uint32) {
	for _, id := range ids {
		d.data.Awarded[id] = true
	}
	d.save()
}

func (d *MonthlyTask) IsAwarded(id uint32) bool {
	if _, exist := d.data.Awarded[id]; exist {
		return true
	}
	return false
}

func (d *MonthlyTask) update(progress map[uint32]*cl.TaskTypeProgress) {
	smsg := &cl.L2C_TaskUpdate{
		Ret:                 uint32(ret.RET_OK),
		MonthlyTaskProgress: progress,
	}
	d.Owner().SendCmdToGateway(cl.ID_MSG_L2C_TaskUpdate, smsg)
}

// 重置日常任务，放到重置日常数据一起
func (d *MonthlyTask) Reset() {
	d.load(nil)
}

func (d *MonthlyTask) OnMonthlyEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	d.Owner().ResetMonthly(0)
	p, change := d.Owner().TaskTypeOnEvent(d.data.TaskTypeProgress, event, progress, values)
	if change {
		d.update(p)
		d.save()
	}
}
