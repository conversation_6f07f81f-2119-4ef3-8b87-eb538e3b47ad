package character

import (
	"app/goxml"
	"app/protos/in/log"
	"app/protos/out/cl"
)

type ActivitySubTurnTableS struct {
}

//nolint:varnamelen
func (a *ActivitySubTurnTableS) DestroyFunc(sum *cl.ActivitySum, user *User, s Servicer) {
	tableInfo := goxml.GetData().ActivityTurntableInfoM.Index(sum.SysId)
	if tableInfo != nil {
		ticketCount := user.GetResourceCount(tableInfo.TicketType, tableInfo.TicketValue, s)
		costs := []*cl.Resource{
			{
				Type:  tableInfo.TicketType,
				Value: tableInfo.TicketValue,
				Count: uint32(ticketCount),
			},
		}
		if ticketCount > 0 {
			totalRes := make([]*cl.Resource, 0, len(tableInfo.RecycleTicketClRes))
			for _, v := range tableInfo.RecycleTicketClRes {
				clone := v.Clone()
				clone.Count *= uint32(ticketCount)
				totalRes = append(totalRes, clone)
			}
			if len(totalRes) > 0 {
				user.Consume(s, costs, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_RECYCLE_TICKET), 0)
				ActivityTurnTableResetMail(s, user, totalRes, tableInfo.MailId)
			}
		}
		user.deleteNumber(tableInfo.BuyID, goxml.NumRefreshForbid)
	}
}

func (a *ActivitySubTurnTableS) InitSubFunc(sum *cl.ActivitySum) {
	sum.TurnTable = &cl.ActivitySubTurnTable{
		BuffIds: make(map[uint32]uint32),
	}
}

func (a *ActivitySubTurnTableS) AfterInitSubFunc(sum *cl.ActivitySum, user *User, s Servicer, resetDaily bool) {
	a.RandomBuff(sum, s)
}

func (a *ActivitySubTurnTableS) RestDaily(sum *cl.ActivitySum, refreshTime, now int64, user *User, s Servicer) {
	a.RandomBuff(sum, s)
}

func (a *ActivitySubTurnTableS) RandomBuff(sum *cl.ActivitySum, s Servicer) {
	if sum != nil && sum.TurnTable != nil {
		if len(sum.TurnTable.SelectBuffs) != 0 {
			return
		}
		buffIDS := goxml.GetData().ActivityTurntableBuffInfoM.IDSClone()
		buffLen := len(buffIDS)
		s.Rand().Shuffle(buffLen, func(i, j int) {
			buffIDS[i], buffIDS[j] = buffIDS[j], buffIDS[i]
		})
		buffCount := goxml.GetData().ConfigInfoM.GetTurnTableRandomBuffNum()
		if buffLen < int(buffCount) {
			buffCount = uint32(buffLen)
		}
		tmp := make([]uint32, buffCount)
		copy(tmp, buffIDS)
		sum.TurnTable.SelectBuffs = tmp
	}
}

func (a *ActivitySum) GetDisposableM() map[uint32]struct{} {
	ret := make(map[uint32]struct{})
	for _, id := range a.TurnTable.BigAwardId {
		ret[id] = struct{}{}
	}
	return ret
}

func (a *ActivitySum) FlushBuff() map[uint32]uint32 {
	ret := make(map[uint32]uint32)
	for id, count := range a.TurnTable.BuffIds {
		ret[id] = count
	}
	return ret
}

func (a *ActivitySum) FlushBigAward() []uint32 {
	ret := make([]uint32, 0, len(a.TurnTable.BigAwardId))
	ret = append(ret, a.TurnTable.BigAwardId...)
	return ret
}

func (a *ActivitySum) FlushSelectBuff() []uint32 {
	ret := make([]uint32, 0, len(a.TurnTable.SelectBuffs))
	ret = append(ret, a.TurnTable.SelectBuffs...)
	return ret
}

func (a *ActivitySum) IsSelectBuff(buffId uint32) bool {
	for _, id := range a.TurnTable.SelectBuffs {
		if id == buffId {
			return true
		}
	}
	return false
}

func (a *ActivitySum) AfterSelectBuff(buffId uint32) map[uint32]uint32 {
	a.TurnTable.SelectBuffs = make([]uint32, 0)
	if a.TurnTable.BuffIds == nil {
		a.TurnTable.BuffIds = make(map[uint32]uint32)
	}
	a.TurnTable.BuffIds[buffId]++
	ret := make(map[uint32]uint32)
	for id, count := range a.TurnTable.BuffIds {
		ret[id] = count
	}
	return ret
}

func (a *ActivitySum) AddGuaranteeCount(count, disposable uint32) uint32 {
	a.TurnTable.GuaranteeCount += count
	if disposable == goxml.TurnTableDisposable {
		a.TurnTable.GuaranteeCount = 0
	}
	return a.TurnTable.GuaranteeCount
}

func (a *ActivitySum) GuaranteeCount() uint32 {
	return a.TurnTable.GuaranteeCount
}

func (a *ActivitySum) AppendBigAwardID(id uint32) []uint32 {
	a.TurnTable.BigAwardId = append(a.TurnTable.BigAwardId, id)
	return a.TurnTable.BigAwardId
}

func (a *ActivitySum) ReduceBuff(buffID uint32) {
	if a.TurnTable.BuffIds == nil {
		return
	}

	count, exist := a.TurnTable.BuffIds[buffID]
	if !exist || count < 1 {
		return
	}

	a.TurnTable.BuffIds[buffID]--
	if a.TurnTable.BuffIds[buffID] == 0 {
		delete(a.TurnTable.BuffIds, buffID)
	}
}

func (a *ActivitySum) DeepCopy() {

}
