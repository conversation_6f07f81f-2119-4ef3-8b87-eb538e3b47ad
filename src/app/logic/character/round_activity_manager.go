package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type ******************** struct {
	owner *User
	datas []*cl.RoundActivity
}

func new********************(user *User) ********************* {
	return &********************{
		owner: user,
	}
}

// 加载数据
func (r *********************) load(datas []*cl.RoundActivity, srv servicer) {
	r.datas = datas
	if len(r.datas) == 0 {
		r.datas = make([]*cl.RoundActivity, 0, 1)
	}

	for _, data := range r.datas {
		if data.TaskProgress == nil {
			data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
		}
		if data.TaskReceived == nil {
			data.TaskReceived = make(map[uint32]bool)
		}
	}

	//检查初始化活动数据
	//当repair代码调用LoadLoginUser时，srv为nil
	if srv != nil {
		r.checkInit(srv)
	}
}

func (r *********************) reset(category, stage uint32) {
	//数据存在时，删除或更新
	for i := 0; i < len(r.datas); i++ {
		data := r.datas[i]
		if data.Category != category {
			continue
		}

		if stage == 0 {
			r.datas = append(r.datas[:i], r.datas[i+1:]...)
		} else {
			data.Stage = stage
			data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
			data.TaskReceived = make(map[uint32]bool)
		}
		return
	}

	//数据不存在时，添加
	if stage > 0 {
		r.datas = append(r.datas, &cl.RoundActivity{
			Category:     category,
			Stage:        stage,
			TaskProgress: make(map[uint32]*cl.TaskTypeProgress),
			TaskReceived: make(map[uint32]bool),
		})
	}
}

func (r *********************) Save() {
	r.owner.dbUser.Module3.RoundActivity = r.datas
	r.owner.setSaveTag(saveTagModule3)
}

func (r *********************) get(category uint32) *cl.RoundActivity {
	for _, data := range r.datas {
		if data.Category == category {
			return data
		}
	}
	return nil
}

func (r *********************) Flush() []*cl.RoundActivity {
	ret := make([]*cl.RoundActivity, 0, len(r.datas))
	for _, data := range r.datas {
		ret = append(ret, data.Clone())
	}
	return ret
}

// 检查初始化活动数据
// 仅当玩家数据为空时处理，非空时由CheckReset处理
func (r *********************) checkInit(srv servicer) {
	if len(r.datas) > 0 {
		return
	}

	openDay := srv.ServerDay(time.Now().Unix())
	sysActs := goxml.GetData().RoundActivityOpenInfoM.GetAllCurrentActivity(srv.StartServiceTm(), openDay)

	var needSave bool
	for _, sysAct := range sysActs {
		r.reset(sysAct.Category, sysAct.Stage)
		needSave = true
	}

	if needSave {
		r.Save()
	}
}

func (r *********************) CheckReset(srv servicer) {
	openDay := srv.ServerDay(time.Now().Unix())
	sysActs := goxml.GetData().RoundActivityOpenInfoM.GetAllCurrentActivity(srv.StartServiceTm(), openDay)
	if len(sysActs) == 0 && len(r.datas) == 0 {
		return
	}

	var needSave bool
	for _, category := range goxml.GetData().RoundActivityOpenInfoM.GetAllCategories() {
		var sysActStage uint32
		for _, sysAct := range sysActs {
			if sysAct.Category == category {
				sysActStage = sysAct.Stage
				break
			}
		}

		var userActStage uint32
		for _, userAct := range r.datas {
			if userAct.Category == category {
				userActStage = userAct.Stage
				if userAct.Category == goxml.ActivitySumTypeRecharge {
					userAct.DailyAward = false
					needSave = true
				}
				break
			}
		}

		if sysActStage == userActStage {
			continue
		}

		if userActStage > 0 {
			r.sendAward(srv, category, userActStage)
		}
		r.reset(category, sysActStage)
		needSave = true
	}

	if needSave {
		r.Save()
	}
}

// 活动数据更新时，补发上一期活动任务未领奖励
func (r *********************) sendAward(srv servicer, category, stage uint32) {
	awards, taskIDs := r.calcLeftTaskAward(category, stage)
	if len(awards) > 0 {
		info := goxml.GetData().RoundActivitySumInfoM.Index(category)
		if info == nil {
			l4g.Errorf("user: %d RoundActivityM.sendAward: no sumInfo, category:%d",
				r.owner.ID(), category)
			return
		}
		awards = MergeResources(awards)
		RoundActivityMail(srv, info.MailId, awards, r.owner)
		r.owner.LogRoundActivityRecvTaskAward(srv, taskIDs, RoundActivityRecvAwardFromSys, category, stage)
	}
}

// 统计任务未领奖励
func (r *********************) calcLeftTaskAward(category, stage uint32) ([]*cl.Resource, []uint32) {
	awards := make([]*cl.Resource, 0, 1)
	ids := make([]uint32, 0, 1)
	infos := goxml.GetData().RoundActivityTaskInfoM.GetAllTasks(category, stage)
	for _, info := range infos {
		//是否可领奖
		if !r.CanReceiveAward(info, false) {
			continue
		}

		awards = append(awards, info.ClRes...)
		ids = append(ids, info.TaskId)
	}
	return awards, ids
}

// 检查是否可领奖
func (r *********************) CanReceiveAward(info *goxml.RoundActivityTaskInfo, printErr bool) bool {
	data := r.get(info.Category)
	if data == nil {
		if printErr {
			l4g.Errorf("user: %d RoundActivityM.CanReceiveAward: no data, category:%d, taskID:%d",
				r.owner.ID(), info.Category, info.TaskId)
		}
		return false
	}

	//是否已领过奖
	if _, exist := data.TaskReceived[info.TaskId]; exist {
		if printErr {
			l4g.Errorf("user: %d RoundActivityM.CanReceiveAward: award has received, category:%d, taskID:%d",
				r.owner.ID(), info.Category, info.TaskId)
		}
		return false
	}

	//检查任务进度是否达标
	progress := data.TaskProgress[info.TypeId]
	if progress == nil {
		if printErr {
			l4g.Errorf("user: %d RoundActivityM.CanReceiveAward: no progress, category:%d, taskID:%d",
				r.owner.ID(), info.Category, info.TaskId)
		}
		return false
	}
	if !r.owner.CheckTaskFinish(progress, info.TypeId, uint64(info.Value)) {
		if printErr {
			l4g.Errorf("user:%d RoundActivityM.CanReceiveAward: task not finish, category:%d, taskID:%d",
				r.owner.ID(), info.Category, info.TaskId)
		}
		return false
	}
	return true
}

func (r *********************) ReceiveTasksAward(infos []*goxml.RoundActivityTaskInfo) {
	for _, info := range infos {
		data := r.get(info.Category)
		if data == nil {
			l4g.Errorf("user:%d RoundActivityM.ReceiveTasksAward: data not exist, category:%d, taskID:%d",
				r.owner.ID(), info.Category, info.TaskId)
			continue
		}

		data.TaskReceived[info.TaskId] = true
	}
}

func (r *********************) OnEvent(event uint32, progress uint64, values []uint32) {
	for _, data := range r.datas {
		if goxml.GetData().RoundActivityTaskInfoM.IsEventExist(data.Category, event) {
			progressMap, change := r.owner.TaskTypeOnEvent(data.TaskProgress, event, progress, values)
			if change {
				r.sendTaskProgress(progressMap)
				r.Save()
			}
		}
	}
}

func (r *********************) sendTaskProgress(progressMap map[uint32]*cl.TaskTypeProgress) {
	r.owner.SendCmdToGateway(cl.ID_MSG_L2C_RoundActivityUpdateTask, &cl.L2C_RoundActivityUpdateTask{
		Ret:      uint32(ret.RET_OK),
		Progress: progressMap,
	})
}

func (r *********************) WasRecvDailyReward() bool {
	for _, v := range r.datas {
		if v == nil {
			continue
		}

		if v.Category == goxml.ActivitySumTypeRecharge && v.Stage > 0 {
			return v.DailyAward
		}
	}
	return false
}

func (r *********************) SetRecvDailyReward() {
	for _, v := range r.datas {
		if v == nil {
			continue
		}

		if v.Category == goxml.ActivitySumTypeRecharge && v.Stage > 0 {
			v.DailyAward = true
			r.Save()
			return
		}
	}
}
