package character

import (
	"app/gmxml"
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
)

type LinkSummon struct {
	owner *User
	data  *cl.LinkHeroSummon
}

func newLinkSummon(user *User) *LinkSummon {
	return &LinkSummon{
		owner: user,
	}
}

func (l *LinkSummon) load(data *cl.LinkHeroSummon) {
	l.data = data
	if l.data == nil {
		l.data = &cl.LinkHeroSummon{}
	}
	if len(l.data.ColorGuarantee) == 0 {
		l.initGuarantee()
	}
}

func (l *LinkSummon) initGuarantee() {
	l.data.ColorGuarantee = make([]uint32, LinkSummonGuaranteeLen)
}

func (l *LinkSummon) Save() {
	if l.owner.dbUser.Module3.LinkHeroSummon == nil {
		l.owner.dbUser.Module3.LinkHeroSummon = l.data
	}
	l.owner.setSaveTag(saveTagModule3)
}

func (l *LinkSummon) GetColorGuarantee() []uint32 {
	return l.data.ColorGuarantee
}

func (l *LinkSummon) SetColorGuarantee(colorGuarantee []uint32) {
	l.data.ColorGuarantee = colorGuarantee
	l.Save()
}

func (l *LinkSummon) GetRedGuarantee() uint32 {
	return l.data.ColorGuarantee[goxml.LinkSummonRedGuaranteeIndex]
}

func (l *LinkSummon) GetActivitySummonGuarantee() *cl.ActivitySummonGuarantee {
	ret := l.data.ActivitySummonGuarantee
	if ret == nil {
		ret = &cl.ActivitySummonGuarantee{}
	}
	return ret
}

func (l *LinkSummon) SetActivitySummonGuarantee(guarantee *cl.ActivitySummonGuarantee) {
	l.data.ActivitySummonGuarantee = guarantee
}

func (l *LinkSummon) NeedResetFreeCount(actID uint32) bool {
	return l.data.Id != actID
}

func (l *LinkSummon) ResetFreeCount(actID uint32) {
	l.data.Id = actID
	l.owner.WeeklyInfo().LinkSummonFree = 0
	l.Save()
	l.owner.SaveWeekly()
}

func (l *LinkSummon) GetDropGroupGuarantee() uint32 {
	return l.data.DropGroupGuarantee
}
func (l *LinkSummon) SetDropGroupGuarantee(count uint32) {
	l.data.DropGroupGuarantee = count
}

func (l *LinkSummon) NeedRoundActivityGuarantee(srv Servicer) *cl.ActivitySummonGuarantee {
	roundType := uint32(goxml.RoundActivityTypeLink)
	now := l.owner.Now(srv)
	serverDay := now[int(common.TIME_TYPE_SERVER_OPEN)]
	copyGuarantee := l.GetActivitySummonGuarantee().Clone()
	roundActivityOpenInfo := goxml.GetData().RoundActivityOpenInfoM.GetCurrentActivityByCategory(srv.StartServiceTm(), serverDay,
		uint32(roundType))
	if roundActivityOpenInfo != nil {
		actId := roundActivityOpenInfo.Stage<<16 + roundActivityOpenInfo.Category //nolint:mnd
		if copyGuarantee.IsNewActivity != goxml.SummonActivityNew || copyGuarantee.ActivityId != actId {
			return l.activityGuaranteeReset(actId, goxml.SummonActivityNew)
		}
	} else {
		if copyGuarantee.IsNewActivity != goxml.SummonActivityNormal || copyGuarantee.ActivityId == 0 {
			roundActivity := gmxml.OperateActivityInfoM.GetRoundActivityInfo(roundType)
			for _, v := range roundActivity {
				if l.owner.OperateActivityM().IsOpening(srv, v, now) {
					return l.activityGuaranteeReset(v.Id, goxml.SummonActivityNormal)
				}
			}
		}
		if copyGuarantee.ActivityId != 0 {
			actInfo := gmxml.OperateActivityInfoM.Index(copyGuarantee.ActivityId)
			if actInfo != nil && l.owner.OperateActivityM().IsOpening(srv, actInfo, now) {
				return nil
			} else {
				roundActivity := gmxml.OperateActivityInfoM.GetRoundActivityInfo(roundType)
				for _, v := range roundActivity {
					if l.owner.OperateActivityM().IsOpening(srv, v, now) {
						return l.activityGuaranteeReset(v.Id, goxml.SummonActivityNormal)
					}
				}
			}
		}
	}
	return nil
}

func (l *LinkSummon) activityGuaranteeReset(id, isNew uint32) *cl.ActivitySummonGuarantee {
	ret := &cl.ActivitySummonGuarantee{
		ActivityId:    id,
		IsNewActivity: isNew,
	}
	return ret
}

func (l *LinkSummon) RefreshRoundActivityGuarantee(guarantee *cl.ActivitySummonGuarantee) {
	l.SetActivitySummonGuarantee(guarantee)
	l.owner.ActivityRecharge().ResetByShopID(goxml.ActivityRechargeLinkSummonShopId)
	l.Save()
	l.owner.ActivityRecharge().save()
}

func (l *LinkSummon) FirstDraw() bool {
	return l.data.FirstDraw
}

func (l *LinkSummon) SetFirstDraw(b bool) {
	l.data.FirstDraw = b
	l.Save()
}
