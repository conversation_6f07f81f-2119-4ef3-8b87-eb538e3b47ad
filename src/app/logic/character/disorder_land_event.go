package character

import (
	"app/goxml"
	aevent "app/logic/event"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

var eventPool map[uint32]func(
	*DisorderLand,
	*DisorderLandMap,
	servicer,
	*cl.L2C_DisorderlandTriggerEvent,
	*goxml.DisorderlandMapInfoExt,
	[]byte,
) bool

func init() {
	eventPool = make(map[uint32]func(
		*DisorderLand,
		*DisorderLandMap,
		servicer,
		*cl.L2C_DisorderlandTriggerEvent,
		*goxml.DisorderlandMapInfoExt,
		[]byte,
	) bool, 4) //nolint:mnd
	registerDisorderEvent()
}

func registerDisorderEvent() {
	eventPool[uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_BATTLE)] = battleEvent
	eventPool[uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_BOX)] = boxEvent
	eventPool[uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_RELIC)] = relicEvent
}

// EventHandler 事件处理器
type EventHandler func(
	*DisorderLand,
	*DisorderLandMap,
	servicer,
	*cl.L2C_DisorderlandTriggerEvent,
	*goxml.DisorderlandMapInfoExt,
	[]byte,
) bool

func GetEventHandler(eventType uint32) EventHandler {
	if e, exist := eventPool[eventType]; exist {
		return e
	}

	return nil
}

func disorderError(sMsg *cl.L2C_DisorderlandTriggerEvent, retCode uint32) bool {
	sMsg.Ret = retCode
	return false
}

// 战斗事件
//
//nolint:varnamelen
func battleEvent(
	d *DisorderLand,
	mapM *DisorderLandMap,
	srv servicer,
	sMsg *cl.L2C_DisorderlandTriggerEvent,
	mapInfo *goxml.DisorderlandMapInfoExt,
	clientData []byte,
) bool {
	if sMsg.FightType == 0 || sMsg.FightType >= uint32(common.DISORDER_LAND_BATTLE_DLB_MAX) {
		l4g.Errorf("user: %d battle: fight type is invalid. type: %d", d.owner.ID(), sMsg.FightType)
		return disorderError(sMsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	hurdleID := mapInfo.EventId
	dInfo := goxml.GetData().DisorderlandDungeonInfoM.Index(hurdleID)
	if dInfo == nil {
		l4g.Errorf("user: %d battle: dungeon is invalid. dungeonID: %d", d.owner.ID(), hurdleID)
		return disorderError(sMsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	node := mapM.GetNode(mapInfo.Id)
	if !d.isEnoughOfRes(srv) {
		l4g.Errorf("user: %d battle: battle res not enough.", d.owner.ID())
		return disorderError(sMsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	isFirstWin := !node.GetStatus()

	battleSrv := getDisorderBattleSrvI(sMsg.FightType)
	sMsg.Ret = battleSrv.Do(srv, d, mapM, node, sMsg, dInfo, mapInfo, clientData)
	if sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d battle: battle run failed. ret: %d", d.owner.ID(), sMsg.Ret)
		return disorderError(sMsg, sMsg.Ret)
	}

	d.fightTask(srv, sMsg.FightType, sMsg.Win, isFirstWin)
	sMsg.StaminaRecoverTime = d.GetStaminaRecoverTime()
	sMsg.Node = mapM.FlushNode(mapInfo.Id)
	sMsg.PassMaxLevel = d.flushPassMaxLevel()
	d.owner.LogDisorderLandFight(srv, sMsg.FightType, sMsg.Difficulty, dInfo.Type, hurdleID, dInfo.Level, sMsg.Win, sMsg.ReportId)

	return true
}

func (d *DisorderLand) fightTask(srv servicer, fightType uint32, win, firstWin bool) {
	isSweep := util.If(fightType == uint32(common.DISORDER_LAND_BATTLE_DLB_SWEEP), 1, 0)
	isWin := util.If(win, 1, 0)
	isFirstWin := util.If(firstWin, 1, 0)
	d.owner.FireCommonEvent(srv.EventM(), aevent.IeDisorderLandFight, 1, isWin, isSweep, isFirstWin)
}

func (d *DisorderLand) getBattleCosts() []*cl.Resource {
	return []*cl.Resource{goxml.GenSimpleResource(
		uint32(common.RESOURCE_TOKEN),
		goxml.GetData().DisorderlandConfigInfoM.StaminaToken,
		1)}
}

func (d *DisorderLand) isEnoughOfRes(srv servicer) bool {
	d.StaminaRecover(srv)
	return d.getStamina() > 0
}

// 宝箱事件
//
//nolint:varnamelen
func boxEvent(
	d *DisorderLand,
	mapM *DisorderLandMap,
	srv servicer,
	sMsg *cl.L2C_DisorderlandTriggerEvent,
	mapInfo *goxml.DisorderlandMapInfoExt,
	_ []byte,
) bool {
	boxInfo := goxml.GetData().DisorderlandBoxInfoM.Index(mapInfo.EventId)
	if boxInfo == nil {
		l4g.Errorf("user: %d box: boxInfo is nil. boxID: %d", d.owner.ID(), mapInfo.EventId)
		return disorderError(sMsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	sMsg.Ret, sMsg.Awards = d.owner.Award(srv, boxInfo.ClRes, AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_BOX), 0)
	if sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d box: send award failed. boxID:%d ret:%d", d.owner.ID(), mapInfo.EventId, sMsg.Ret)
		return disorderError(sMsg, sMsg.Ret)
	}

	mapM.InsertNode(mapInfo.Id, mapInfo.EventType)
	mapM.addCompleteNodeNum()

	sMsg.Node = mapM.FlushNode(mapInfo.Id)

	d.owner.FireCommonEvent(srv.EventM(), aevent.AeDisorderLandTriggerBoxToX, 1)
	return true
}

// 遗物事件
//
//nolint:varnamelen
func relicEvent(
	d *DisorderLand,
	mapM *DisorderLandMap,
	srv servicer,
	sMsg *cl.L2C_DisorderlandTriggerEvent,
	mapInfo *goxml.DisorderlandMapInfoExt,
	_ []byte,
) bool {
	buffInfo := goxml.GetData().DisorderlandBuffInfoM.Index(mapInfo.EventId)
	if buffInfo == nil {
		l4g.Errorf("user: %d relicEvent: buffInfo is nil. relicID:%d", d.owner.ID(), mapInfo.EventId)
		return disorderError(sMsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	reason := uint32(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_RELIC_AWARD)
	sMsg.Ret, sMsg.Awards = d.owner.Award(srv, buffInfo.ClRes, AwardTagMail, reason, 0)
	if sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d relicEvent: send award failed. relicID:%d ret:%d", d.owner.ID(), mapInfo.EventId, sMsg.Ret)
		return disorderError(sMsg, sMsg.Ret)
	}
	mapM.InsertNode(mapInfo.Id, mapInfo.EventType)
	mapM.addCompleteNodeNum()

	sMsg.Node = mapM.FlushNode(mapInfo.Id)
	d.owner.FireCommonEvent(srv.EventM(), aevent.AeDisorderLandTriggerRelicToX, 1)
	return true
}
