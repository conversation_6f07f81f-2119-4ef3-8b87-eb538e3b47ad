package character

import (
	"app/goxml"
	"app/logic/mongo"
	"app/protos/in/l2c"
	"app/protos/in/l2m"
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type CognitionManager struct {
	owner *User
	logs  map[uint32]*cl.CognitionLog
}

func newCognitionManager(user *User) *CognitionManager {
	return &CognitionManager{
		owner: user,
		logs:  make(map[uint32]*cl.CognitionLog),
	}
}

func (cm *CognitionManager) NewCognitionLog(srv servicer, fid uint32, params ...uint32) *cl.CognitionLog {
	u := cm.owner //nolint:varnamelen

	heroes, skins, artifacts, totalPower, passiveSkills := u.FlushFormationHeroes(fid, params...)

	riteIds := u.getFormationRiteIds(fid)
	rites := make([]*cl.Rite, 0, len(riteIds))
	for _, id := range riteIds {
		riteData := u.RiteManager().GetRite(id)
		if riteData == nil {
			continue
		}
		cloneData := riteData.Flush()
		cloneData.Grids = nil // 通关记录不需要这个信息，赋值nil，减少数据大小
		rites = append(rites, cloneData)
	}

	remainIds := u.getFormationRemainIds(fid)
	remains := make([]*cl.Remain, 0, len(remainIds))
	for _, id := range remainIds {
		remainData := u.RemainM().GetRemain(id)
		if remainData == nil {
			continue
		}
		cloneData := remainData.Flush()
		remains = append(remains, cloneData)
	}

	return &cl.CognitionLog{
		Sid:         u.ServerID(),
		Uid:         u.ID(),
		OpGroup:     uint32(srv.OpGroup()),
		FormationId: fid,
		//TargetId:    targetId,
		BaseId:    u.BaseID(),
		Name:      u.Name(),
		Power:     int64(totalPower),
		Formation: u.GetFormation(fid).Clone(),
		Heroes:    heroes,
		Artifacts: artifacts,
		Arena:     srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)),
		//Round:       round,
		//Damage:      damage,
		Skins: skins,
		Rites: rites,
		// Emblems:   emblems,
		SeasonAdd:     u.GetSeasonAddData(),
		Remains:       remains,
		PassiveSkills: passiveSkills,
		TalentTree:    u.TalentTree().GetBossRushTalentTreeCultivation(u.GetSeasonID(), fid),
	}
}

func (cm *CognitionManager) SetCognitionLog(l *cl.CognitionLog) {
	cm.logs[l.FormationId] = l
}

func (cm *CognitionManager) Del(fid uint32) {
	delete(cm.logs, fid)
}

func (cm *CognitionManager) TowerSeasonCognitionLogSend(srv servicer, targetID, round uint32) {
	cLog := cm.logs[uint32(common.FORMATION_ID_FI_TOWER_SEASON)]

	if cLog == nil {
		return
	}
	cLogClone := cLog.Clone()
	cLogClone.SeasonId = goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix())
	cLogClone.TargetId = targetID
	cLogClone.Round = round
	srv.MongoM().Send(&mongo.CognitionLogMessage{
		Source:  cLogClone,
		CogType: l2m.CognitionType_CT_TowerSeason,
	})
}

func (cm *CognitionManager) Send(srv servicer, fid, targetID, round uint32, damage uint64) {
	cLog := cm.logs[fid]

	if cLog == nil {
		return
	}

	cogType, exist := formationWithCognition[common.FORMATION_ID(fid)]
	if !exist {
		l4g.Errorf("user:%d CognitionManager.Send: fid:%d not register", cm.owner.ID(), fid)
		return
	}

	info := goxml.GetData().FormationInfoM.Index(fid)
	if info == nil {
		l4g.Errorf("user:%d CognitionManager.Send: formationInfo is nil. fid:%d", cm.owner.ID(), fid)
		return
	}

	seasonID := goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix())
	// 赛季ID使用时间获取，不用玩家身上的，玩家可能没有解锁赛季，比如:百塔
	inSeason := goxml.IsFuncInSeason(goxml.GetData(), seasonID, info.FunctionId)
	if inSeason {
		cLog.SeasonId = seasonID
	}

	cLog.TargetId = targetID
	cLog.Round = round
	cLog.Damage = damage

	srv.MongoM().Send(&mongo.CognitionLogMessage{
		Source:  cLog,
		CogType: cogType,
	})
	cm.logs[fid] = nil
}

func (cm *CognitionManager) GetCognitionType(fid common.FORMATION_ID) l2m.CognitionType {
	cogType, exist := formationWithCognition[fid]
	if !exist {
		l4g.Errorf("user:%d CognitionManager.GetCognitionType: fid:%d not register", cm.owner.ID(), fid)
		return l2m.CognitionType_CT_NONE
	}
	return cogType
}
