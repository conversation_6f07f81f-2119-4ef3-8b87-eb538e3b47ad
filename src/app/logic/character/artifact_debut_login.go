package character

import (
	"app/goxml"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

func (a *ArtifactDebutManager) newLoginAct() *cl.ArtifactDebutLoginAct {
	return &cl.ArtifactDebutLoginAct{}
}

func (a *ArtifactDebutManager) getLoginAct() *cl.ArtifactDebutLoginAct {
	if a.data.LoginAct == nil {
		a.data.LoginAct = a.newLoginAct()
	}
	return a.data.LoginAct
}

// 增加累登次数
func (a *ArtifactDebutManager) AddLoginCount(srv servicer) {
	if !a.IsInActiveTime(srv) {
		return
	}
	loginAct := a.getLoginAct()
	loginAct.Count++
	a.Save()
}

type ArtifactDebutLoginAct cl.ArtifactDebutLoginAct

// 是否可以领奖
func (a *ArtifactDebutLoginAct) CanReceiveAward(actID uint32, ids []uint32) bool {
	for _, id := range ids {
		info := goxml.GetData().ArtifactDebutLoginRewardInfoM.Index(actID, id)
		if info == nil {
			l4g.Errorf("ArtifactDebutLoginAct.CanReceiveAward: info not exist, id:%d", id)
			return false
		}

		if a.Count < info.Day {
			l4g.Errorf("ArtifactDebutLoginAct.CanReceiveAward: not finish, id:%d, requestCount:%d, count:%d",
				id, info.Day, a.Count)
			return false
		}

		if util.BitAndUint64(a.ReceivedIds, int(id)) {
			l4g.Errorf("ArtifactDebutLoginAct.CanReceiveAward: has reveived, id:%d, ReceivedIds:%d",
				id, a.ReceivedIds)
			return false
		}
	}
	return true
}

// 获取活动奖励数据
func (a *ArtifactDebutLoginAct) GetAward(actID uint32, ids []uint32) []*cl.Resource {
	return goxml.GetData().ArtifactDebutLoginRewardInfoM.GetAwardByIDs(actID, ids)
}

// 领取活动奖励
func (a *ArtifactDebutLoginAct) ReceiveAward(ids []uint32) uint64 {
	for _, id := range ids {
		util.BitSetTrueUint64Ptr(&a.ReceivedIds, int(id))
	}
	return a.ReceivedIds
}
