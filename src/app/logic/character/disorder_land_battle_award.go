package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

// 通过定义 DisorderAwardSrv, 来对外暴露奖励相关的接口，
// 定义 DisorderSweepAward 和 DisorderFightAward 来实现 DisorderAwardSrv 接口
// 奖励相关逻辑变化限制在这2个 struct 上，奖励逻辑怎么变化
// 影响的都只是这2个 struct, battle event 整体业务逻辑流程不会受到影响

type DisorderAwardSrv interface {
	calcAward(
		servicer,
		*DisorderLand,
		uint32, // 赛季丰碑 id
		uint32, // drop id
		uint32, // dungeon id
		string,
	) (uint32, []*cl.Resource)
}

func getDisorderAwardSrvI(fightType uint32) DisorderAwardSrv {
	if fightType == uint32(common.DISORDER_LAND_BATTLE_DLB_BATTLE) {
		return DisorderFightAward(uint32(common.DISORDER_LAND_BATTLE_DLB_BATTLE))
	}

	return DisorderSweepAward(uint32(common.DISORDER_LAND_BATTLE_DLB_SWEEP))
}

type DisorderSweepAward uint32

//nolint:varnamelen
func (s DisorderSweepAward) calcAward(srv servicer, d *DisorderLand, monumentID, dropID, dungeonID uint32, reportID string) (uint32, []*cl.Resource) {
	var mRune *cl.MonumentRune
	if d.getSweepCount()+1 == goxml.GetData().DisorderlandConfigInfoM.GuaranteedSweepCount {
		mRune = d.findMinMonumentRune(monumentID)
	} else {
		mRune = d.randMonumentRune(srv, monumentID)
	}
	if mRune == nil {
		l4g.Errorf("user: %d DisorderSweepAward: monumentRune is nil. monumentID:%d", d.owner.ID(), monumentID)
		return uint32(cret.RET_DISORDER_LAND_GET_RUNE_FAILED), nil
	}
	runeInfo := goxml.GetData().SeasonLinkRuneInfoM.GetRecordById(mRune.GetRuneSysId())
	if runeInfo == nil {
		l4g.Errorf("user: %d DisorderSweepAward: runeInfo is nil. runeID:%d", d.owner.ID(), mRune.GetRuneSysId())
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
	}

	return getDisorderAward(srv, d, dungeonID, dropID, runeInfo.UniqueLevel, mRune.Pos, reportID)
}

type DisorderFightAward uint32

//nolint:varnamelen
func (s DisorderFightAward) calcAward(srv servicer, d *DisorderLand, monumentID, dropID, dungeonID uint32, reportID string) (uint32, []*cl.Resource) {
	mRune := d.randMonumentRune(srv, monumentID)
	if mRune == nil {
		l4g.Errorf("user: %d DisorderFightAward: monumentRune is nil. monumentID:%d", d.owner.ID(), monumentID)
		return uint32(cret.RET_DISORDER_LAND_GET_RUNE_FAILED), nil
	}
	runeInfo := goxml.GetData().SeasonLinkRuneInfoM.GetRecordById(mRune.GetRuneSysId())
	if runeInfo == nil {
		l4g.Errorf("user: %d DisorderFightAward: runeInfo is nil. runeID:%d", d.owner.ID(), mRune.GetRuneSysId())
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
	}

	return getDisorderAward(srv, d, dungeonID, dropID, runeInfo.UniqueLevel, mRune.Pos, reportID)
}

//nolint:varnamelen
func getDisorderAward(srv servicer, d *DisorderLand, dungeonID, dropID, rLevel, rPos uint32, reportID string) (uint32, []*cl.Resource) {
	infoM := goxml.GetData().DisorderlandDropGroupInfoM
	// 目标符石等级为随机到的符石等级 + 1
	improvedRLevel := rLevel + 1

	// 判断是否提升成功，并获取有效符石等级
	isImprove, group, realRLevel := goxml.GetData().DisorderlandDropInfoM.IsImprove(srv.Rand(), dropID, improvedRLevel)

	var isGuaranteeExist bool // 判断是否存在保底
	dropInfo := goxml.GetData().DisorderlandDropInfoM.GetDataByIdLevel(dropID, realRLevel)
	if dropInfo == nil {
		l4g.Errorf("user: %d getDisorderAward: no dropInfo, dropID %d rLevel %d", d.owner.ID(), dropID, realRLevel)
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
	}
	if dropInfo.Num > 0 {
		isGuaranteeExist = true
	}

	var isGuaranteeTrigger bool  // 判断触发保底
	var guaranteeProgress uint32 // 当前保底进度
	if isGuaranteeExist {
		isGuaranteeTrigger, guaranteeProgress = checkGuarantee(d, dropID, realRLevel, dropInfo.Num)
		if isGuaranteeTrigger {
			l4g.Debugf("user: %d getDisorderAward: 触发保底，dropID %d  realRLevel %d guaranteeProgress %d", d.owner.ID(), dropID, realRLevel, guaranteeProgress)
			isImprove = true
		}
	}

	// case1：高等级符石
	if isImprove {
		ret, awards := infoM.GetAwardWithImprove(group, rPos, improvedRLevel)
		if isGuaranteeExist && ret == uint32(cret.RET_OK) { // 存在保底
			resetSingleGuaranteeCount(d, dropID, realRLevel) // 提升成功和触发保底都会重置保底计数
			if isGuaranteeTrigger {
				d.owner.LogDisorderLandTriggerGuarantee(srv, dungeonID, realRLevel, awards, reportID)
			}
		}
		return ret, awards
	}

	// case2：低等级符石
	ret, awards := infoM.RandAwardWithNoImprove(srv.Rand(), group, rPos, rLevel)
	if isGuaranteeExist && ret == uint32(cret.RET_OK) { //存在保底
		addSingleGuaranteeCount(d, dropID, realRLevel)
	}

	return ret, awards
}

// 检查是否触发保底
func checkGuarantee(d *DisorderLand, dropID, rLevel, guaranteeNum uint32) (bool, uint32) {
	//nolint:mnd
	count, exist := d.data.GuaranteeCount[(uint64(dropID)<<32)|uint64(rLevel)]
	return exist && guaranteeNum > 0 && count >= guaranteeNum, count // num > 0 才有保底
}

// 增加单个保底计数
func addSingleGuaranteeCount(d *DisorderLand, dropID, rLevel uint32) {
	//nolint:mnd
	key := (uint64(dropID) << 32) | uint64(rLevel)
	d.data.GuaranteeCount[key]++
	d.Save()
}

// 重置单个保底计数
func resetSingleGuaranteeCount(d *DisorderLand, dropID, rLevel uint32) {
	//nolint:mnd
	key := (uint64(dropID) << 32) | uint64(rLevel)
	_, exist := d.data.GuaranteeCount[key]
	if !exist {
		return
	}
	d.data.GuaranteeCount[key] = 0
	d.Save()
}
