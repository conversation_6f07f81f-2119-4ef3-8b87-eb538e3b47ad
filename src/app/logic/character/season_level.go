package character

import (
	"app/goxml"
	"app/logic/battle"
	aevent "app/logic/event"
	"app/logic/helper"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"
)

type SeasonLevel struct {
	owner *User
	data  *cl.SeasonLevel
}

func newSeasonLevel(user *User) *SeasonLevel {
	return &SeasonLevel{
		owner: user,
	}
}

func (sl *SeasonLevel) load(data *cl.SeasonLevel) {
	sl.data = data
	if sl.data == nil {
		sl.data = &cl.SeasonLevel{}
	}
	if sl.data.TaskTypeProgress == nil {
		sl.data.TaskTypeProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if sl.data.TaskAwarded == nil {
		sl.data.TaskAwarded = make(map[uint32]bool)
	}
}

func (sl *SeasonLevel) Save() {
	sl.owner.dbUser.Module5.SeasonLevel = sl.data
	sl.owner.setSaveTag(saveTagModule5)
}

func (sl *SeasonLevel) Flush() *cl.SeasonLevel {
	return sl.data.Clone()
}

func (sl *SeasonLevel) IsLevelAwardsReceived(lv uint32) bool {
	for _, level := range sl.data.ReceivedLvAwards {
		if level == lv {
			return true
		}
	}
	return false
}

func (sl *SeasonLevel) SetLevelAwardsReceived(levels []uint32) {
	sl.data.ReceivedLvAwards = append(sl.data.ReceivedLvAwards, levels...)
	sl.Save()
}

func (sl *SeasonLevel) OnSeasonLevelEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	if !sl.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON), srv) { // 是否解锁赛季
		return
	}

	dayNum := sl.owner.SeasonOpenDay(time.Now().Unix())
	if dayNum == 0 {
		return
	}

	unlock := goxml.GetData().SeasonLevelTaskInfoM.CheckEventIsCanRecord(sl.owner.GetSeasonID(), event, dayNum)

	if unlock {
		p, change := sl.owner.TaskTypeOnEvent(sl.data.TaskTypeProgress, event, progress, values)
		if change {
			sl.update(p)
			sl.Save()
		}
	}
}

func (sl *SeasonLevel) update(progress map[uint32]*cl.TaskTypeProgress) {
	smsg := &cl.L2C_SeasonLevelTaskUpdate{
		Ret:      uint32(ret.RET_OK),
		Progress: progress,
	}
	sl.owner.SendCmdToGateway(cl.ID_MSG_L2C_SeasonLevelTaskUpdate, smsg)
}

func (sl *SeasonLevel) GetOneTypeProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return sl.owner.CalcTaskProgress(taskTypeInfo)
	}
	return sl.data.TaskTypeProgress[taskTypeInfo.Id]
}

func (sl *SeasonLevel) IsAwarded(id uint32) bool {
	if _, exist := sl.data.TaskAwarded[id]; exist {
		return true
	}
	return false
}

func (sl *SeasonLevel) ReceiveAward(ids []uint32) {

	for _, id := range ids {
		sl.data.TaskAwarded[id] = true
	}
	sl.Save()
}

func (sl *SeasonLevel) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	sl.data.ReceivedLvAwards = nil
	sl.data.TaskAwarded = make(map[uint32]bool)
	sl.data.TaskTypeProgress = make(map[uint32]*cl.TaskTypeProgress)
	sl.checkInitLoginTask(srv)
	sl.Save()
}

func (sl *SeasonLevel) resetDaily() {
	if len(sl.data.TaskTypeProgress) == 0 {
		return
	}
	tasks := goxml.GetData().SeasonLevelTaskInfoM.GetDailyResetTasks(sl.owner.GetSeasonID())
	if tasks == nil {
		return
	}
	for taskType, taskIds := range tasks {
		delete(sl.data.TaskTypeProgress, taskType)
		for _, id := range taskIds {
			delete(sl.data.TaskAwarded, id)
		}
	}
	sl.Save()
}

func (sl *SeasonLevel) checkInitLoginTask(srv servicer) {
	if sl.owner.GetDBUser().Module.Daily.DailyZero ==
		helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), time.Now().Unix()) {
		// 有可能已经执行过resetDaily后才解锁赛季，这里单独执行下赛季的登录任务
		sl.OnSeasonLevelEvent(aevent.AeContinueLoginToX, 1, nil, srv)
	}
}

// AddPassSkillByNewHero  新影响加成被动技能
// * 赛季玩法内所有功能通用 *
func (sl *SeasonLevel) AddPassSkillByNewHero(formationID uint32, formationIndex int, altRaisePS *battle.AltRaisePS) {
	team := sl.owner.GetFormationTeam(formationID, formationIndex)
	if team == nil || len(team.Info) == 0 {
		return
	}
	infoM := goxml.GetData().SeasonDungeonLayerInfoM
	pos2RaisePSs := make(map[uint32][]uint64)
	totalStar := uint32(0)
	for _, info := range team.Info {
		heroM := sl.owner.HeroManager()
		hero := heroM.Get(info.Hid)
		if hero == nil {
			continue
		}
		if infoM.GetAddHero(sl.owner.GetSeasonID(), hero.GetHeroSysID()) != nil {
			totalStar += hero.GetStar()
		}
	}
	// 默认 1 层
	layer := uint32(1)
	skillID := goxml.GetData().SeasonHeroAddInfoM.GetPassSkill(totalStar)
	if skillID == 0 {
		return
	}
	raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skillID, layer)
	if raisePSInfo == nil {
		return
	}
	pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
	altRaisePS.AltAttack(pos2RaisePSs)
}

func (sl *SeasonLevel) TaskFinishNum() int {
	if sl.data == nil {
		return 0
	}
	return len(sl.data.TaskAwarded)
}
