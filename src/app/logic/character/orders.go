package character

import (
	"app/goxml"
	aevent "app/logic/event"
	"app/protos/in/db"
	"app/protos/in/gm"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

// 充值订单
type Orders struct {
	owner *User

	data              map[string]*db.Order //充值订单数据
	waitProcessOrders map[string]util.None //待处理的充值订单集合

	//由于sdk通知订单退款可能早于订单发货，故不能在data中记录退款状态
	refundedOrders   map[string]uint32    //已退款的订单集合 => 退款状态
	waitRefundOrders map[string]*db.Order //待处理的退款订单集合，延迟处理离线玩家
}

func newOrders(user *User) *Orders {
	return &Orders{
		owner:             user,
		data:              make(map[string]*db.Order),
		waitProcessOrders: make(map[string]util.None),
		refundedOrders:    make(map[string]uint32),
		waitRefundOrders:  make(map[string]*db.Order),
	}
}

func (o *Orders) Owner() *User {
	return o.owner
}

// 加载数据
func (o *Orders) load(data map[string]*db.Order, waitRefundOrders map[string]*db.WaitRefundOrder, waitProcessOrders []string, refundedOrders map[string]*db.RefundedOrder) {
	if data != nil {
		o.data = data
	}
	for _, orderID := range waitProcessOrders {
		o.waitProcessOrders[orderID] = util.None{}
	}
	if len(waitRefundOrders) > 0 {
		tmp := make(map[string]*db.Order)
		for _, v := range waitRefundOrders {
			tmp[v.OrderId] = v.OrderData
		}
		o.waitRefundOrders = tmp
	}
	if len(refundedOrders) > 0 {
		tmp := make(map[string]uint32)
		for _, v := range refundedOrders {
			tmp[v.OrderId] = v.Status
		}
		o.refundedOrders = tmp
	}
}

func (o *Orders) Get(id string) *db.Order { return o.data[id] }
func (o *Orders) Add(order *db.Order) {
	o.data[order.OrderId] = order
	//检查添加到待处理的订单集合
	//订单流程，先到add redis，然后有user缓存的话，调用user.Orders().Add
	if order.Status == uint32(common.ORDER_STATUS_OS_SAVE) {
		o.waitProcessOrders[order.OrderId] = util.None{}
	}
}

// SetRefund 设置订单已退款状态
func (o *Orders) SetRefund(order *db.Order, status uint32) {
	//增加已退款订单
	o.refundedOrders[order.OrderId] = status
	//删除待退款订单
	delete(o.waitRefundOrders, order.OrderId)
}

// AddRefundOrder 添加到待退款的订单集合
func (o *Orders) AddRefundOrder(order *db.Order) {
	o.waitRefundOrders[order.OrderId] = order
}

var IsRechargeCouponFuncM = map[uint32]IsRechargeCouponFunc{
	uint32(common.RECHARGE_TYPE_RT_COUPON):                DefaultRechargeCouponFunc,
	uint32(common.RECHARGE_TYPE_RT_WEB_COUPON):            DefaultRechargeCouponFunc,
	uint32(common.RECHARGE_TYPE_RT_ACTIVITY_COUPON):       DefaultRechargeCouponFunc,
	uint32(common.RECHARGE_TYPE_RT_ACTIVITY_COUPON_DAILY): DefaultRechargeCouponFunc,
	uint32(common.RECHARGE_TYPE_RT_ACTIVITY_RECHARGE):     ActivityRechargeCouponFunc,
}

type IsRechargeCouponFunc func(servicer, *db.Order, *User) bool

func DefaultRechargeCouponFunc(servicer, *db.Order, *User) bool {
	return true
}

func ActivityRechargeCouponFunc(srv servicer, order *db.Order, user *User) bool {
	giftID := order.Custom.Id
	giftInfo := goxml.GetData().ActivityRechargeGiftInfoM.Index(giftID)
	if giftInfo == nil {
		l4g.Errorf("user:%d account tag:%d ActivityRechargeCouponFunc no find gift:%d", user.ID(), user.GetAccountTag(), giftID)
		return false
	}

	var awards []*cl.Resource
	if giftInfo.GiftType == goxml.ActivityRechargeGiftTypeOneClick {
		if len(giftInfo.GiftIds) > 0 {
			for _, singleGiftId := range giftInfo.GiftIds {
				singleGiftInfo := goxml.GetData().ActivityRechargeGiftInfoM.Index(singleGiftId)
				if singleGiftInfo == nil {
					l4g.Errorf("user:%d ActivityRechargeCouponFunc no find gift:%v", user.ID(), singleGiftInfo)
					return false
				}
				awards = append(awards, singleGiftInfo.RewardClRes...)
			}
		}
	} else {
		awards = giftInfo.RewardClRes
	}

	for _, award := range awards {
		if award == nil {
			continue
		}
		if award.Type == uint32(common.RESOURCE_COUPON) {
			return true
		}
	}

	return false
}

// 订单处理 并进行存储
// @param online - 在线/离线订单
// @return sucs - 成功订单
// @return fails - 失败订单
func (o *Orders) Process(srv servicer, orders []*db.Order) {
	rmsg := &r2l.L2R_SaveOrder{
		Orders: make([]*db.Order, 0, len(orders)),
	}

	var lastRechargeTime int64
	for index, v := range orders { //nolint:varnamelen
		//两种特殊类型：充值代金券与代金券支付
		var isRechargeCoupon, isRechargeByCoupon bool
		fun, exist := IsRechargeCouponFuncM[v.Custom.Type]
		if exist {
			isRechargeCoupon = fun(srv, v, o.owner)
		}
		if !isRechargeCoupon && common.ORDER_TYPE(v.Type) == common.ORDER_TYPE_OT_COUPON {
			isRechargeByCoupon = true
		}

		firstOrder := o.owner.GetRecharge() == 0 && index == 0 //第一份订单
		ret := o.owner.OrderShipped(srv, v)
		if ret == ProcessOrderRetSuccess {
			//订单正常处理
			v.Status = uint32(common.ORDER_STATUS_OS_SUCCESS)

			//代金券支付时不记录，其他情况记录
			if !isRechargeByCoupon {
				o.owner.addRecharge(v.Amount)
				o.owner.SendSelfToClient()
				if v.PayTime > lastRechargeTime {
					lastRechargeTime = v.PayTime
				}
			}
			//代金券充值时不触发充值事件，其他情况触发
			if !isRechargeCoupon {
				o.owner.FireCommonEvent(srv.EventM(), aevent.IeRechargeAfter, uint64(o.owner.GetRecharge()), v.Amount)
			}

		} else {
			v.Status = uint32(common.ORDER_STATUS_OS_FAIL) //订单处理失败意味着订单有问题，Save时从等待处理列表中删除
		}

		//代金券支付时不落地，其他情况落地
		if !isRechargeByCoupon {
			rmsg.Orders = append(rmsg.Orders, v.Clone())
		}

		//从待处理订单集合中移除
		delete(o.waitProcessOrders, v.OrderId)

		o.owner.LogOrderProcess(srv, v, firstOrder)
	}

	if lastRechargeTime > o.owner.lastRechargeTime() {
		o.owner.setLastRechargeTime(lastRechargeTime)
	}

	//save时，redis actor会把db中的waitProcessOrder移除
	if len(rmsg.Orders) > 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveOrder), o.owner.ID(), rmsg)
	}
}

func (o *Orders) online(srv servicer, _ bool) {
	o.processWaitOrders(srv)
	o.processWaitRefundOrders(srv)
}

// 处理未发货订单，离线增加的订单等
func (o *Orders) processWaitOrders(srv servicer) {
	//没有待处理的订单，直接返回
	if len(o.waitProcessOrders) == 0 {
		return
	}
	orders := make([]*db.Order, 0, len(o.waitProcessOrders))
	for orderID := range o.waitProcessOrders {
		order := o.data[orderID]
		if order == nil {
			l4g.Errorf("user:%d [Orders] not found wait process order:%s", o.owner.ID(), orderID)
		} else if order.Status != uint32(common.ORDER_STATUS_OS_SAVE) {
			l4g.Errorf("user:%d [Orders] order state err:%v", o.owner.ID(), order)
		} else {
			orders = append(orders, order)
		}
	}
	//清空待处理订单
	//o.waitProcessOrders = make(map[string]util.None)  process里会删除
	if length := len(orders); length != 0 {
		o.Process(srv, orders)
		l4g.Infof("user:%d [Orders] process offline orders:%v", o.owner.ID(), orders)
	}
}

// 处理待退款订单等
func (o *Orders) processWaitRefundOrders(srv servicer) {
	//没有待处理的订单，直接返回
	if len(o.waitRefundOrders) == 0 {
		return
	}
	orders := make([]*db.Order, 0, len(o.waitRefundOrders))
	for _, order := range o.waitRefundOrders {
		orders = append(orders, order)
	}
	//Process里会清空待处理订单
	if length := len(orders); length != 0 {
		o.Refund(srv, orders)
		l4g.Infof("user:%d [Orders] process offline refund orders:%v", o.owner.ID(), orders)
	}
}

func (o *Orders) GetData() map[string]*db.Order {
	return o.data
}

// Refund 订单退款
// 删除待退款订单，增量落地已退款订单
func (o *Orders) Refund(srv servicer, orders []*db.Order) map[string]util.None {
	l4g.Infof("user:%d [Orders] Refund: orders %+v", o.owner.ID(), orders)
	var delWaitRefundOrders []*db.WaitRefundOrder
	var addRefundedOrders []*db.RefundedOrder
	successOrderIds := make(map[string]util.None)

	//修改内存中的已退款订单和待退款订单
	var status uint32
	for _, v := range orders { //nolint:varnamelen
		if !o.HasRefunded(v.OrderId) { //判断订单是否已退款
			if o.owner.OrderRefund(srv, v) { //订单退款成功
				status = uint32(common.REFUND_STATUS_RS_SUCCESS)
				o.owner.LogOrderRefund(srv, v, uint64(v.Amount), o.owner.Coupon())
				successOrderIds[v.OrderId] = util.None{}
				// 发送退款邮件
				mailTxt := srv.RefundMailTemplateM().GetRefundMailTxt(v.Amount)
				if len(mailTxt) != rechargeMailTxtLen {
					l4g.Errorf("user:%d [Orders] Refund: invalid mailTxt %+v", o.owner.ID(), mailTxt)
				} else {
					GMMail(srv, mailTxt, []uint64{o.Owner().ID()}, &gm.UsersMail{}, &gm.Result{})
				}
			} else {
				status = uint32(common.REFUND_STATUS_RS_FAIL)
			}
			o.refundedOrders[v.OrderId] = status
			addRefundedOrders = append(addRefundedOrders, &db.RefundedOrder{
				OrderId: v.OrderId,
				Status:  status,
			})
		} else {
			l4g.Errorf("user:%d [Orders] Refund: order has refunded %+v", o.owner.ID(), v)
		}
		//不管退款是否失败，都会删除待退款订单
		delete(o.waitRefundOrders, v.OrderId)
		delWaitRefundOrders = append(delWaitRefundOrders, &db.WaitRefundOrder{
			OrderId:   v.OrderId,
			OrderData: v,
		})
	}

	rmsg := &r2l.L2R_SaveRefundOrder{
		WaitRefundOrders: delWaitRefundOrders,
		RefundedOrders:   addRefundedOrders,
	}
	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveRefundOrder), o.owner.ID(), rmsg)

	return successOrderIds
}

// 是否已退款
func (o *Orders) HasRefunded(orderId string) bool {
	if _, exist := o.refundedOrders[orderId]; exist {
		return true
	}
	return false
}
