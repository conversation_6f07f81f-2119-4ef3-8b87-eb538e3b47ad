package character

import (
	"app/goxml"
	"app/logic/helper"
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/time"
)

type GodPresentManager struct {
	owner *User
	data  *cl.GodPresentsNew
}

func newGodPresentManager(user *User) *GodPresentManager {
	return &GodPresentManager{
		owner: user,
	}
}

func (gm *GodPresentManager) Owner() *User {
	return gm.owner
}

func (gm *GodPresentManager) load(data *cl.GodPresentsNew) {
	gm.data = data
	if gm.data == nil {
		gm.data = &cl.GodPresentsNew{}
	}

	if len(gm.data.Datas) == 0 {
		gm.data.Datas = make([]*cl.GodPresentNew, 0, 1)
	}

	if len(gm.data.HeroCount) == 0 {
		gm.data.HeroCount = make(map[uint32]uint32)
	}

	if gm.data.LoginTime == 0 {
		gm.data.LoginTime = int64(util.DailyZeroByTime(time.Now().Unix()))
		gm.Save()
	}

	if gm.data.IsSendMail == nil {
		gm.data.IsSendMail = make(map[uint32]bool)
	}
}

func (gm *GodPresentManager) Save() {
	gm.owner.dbUser.Module4.GodPresentsNew = gm.data
	gm.owner.setSaveTag(saveTagModule4)
}

func (gm *GodPresentManager) FlushGodPresents() []*cl.GodPresentNew {
	ret := make([]*cl.GodPresentNew, 0, len(gm.data.Datas))
	for _, v := range gm.data.Datas {
		ret = append(ret, v.Clone())
	}
	return ret
}

func (gm *GodPresentManager) GetLoginTime() int64 {
	return gm.data.LoginTime
}

// CheckSendNoticeMail 检查发送活动开启邮件
func (gm *GodPresentManager) CheckSendNoticeMail(srv servicer) {
	createDay := gm.owner.CreateDay(time.Now().Unix())
	curRound := uint32(goxml.GodPresentGroupFirst)
	firstDay := goxml.GetData().GodPresentInfoM.FirstStartDay(curRound)
	endDay := goxml.GetData().GodPresentInfoM.EndDay(curRound)
	if createDay > endDay {
		return
	}

	isSend := gm.data.IsSendMail[curRound]
	if !isSend && createDay >= firstDay {
		GodPresentMail(srv, gm.owner, gm.GetMailID(srv, curRound))
		gm.setIsSendMail(curRound)
	}
}

func (gm *GodPresentManager) setIsSendMail(group uint32) {
	if gm.data.IsSendMail == nil {
		gm.data.IsSendMail = make(map[uint32]bool)
	}

	gm.data.IsSendMail[group] = true
	gm.Save()
}

func (gm *GodPresentManager) GetGodPresent(id uint32) *GodPresent {
	for _, v := range gm.data.Datas {
		if v.Id == id {
			return (*GodPresent)(v)
		}
	}
	return nil
}

// IsLastFinish 前一个子活动是否已完成
// 1.首个活动前没有活动，即代表完成
// 2.领取过奖励，即代表完成
// @param uint32 id 子活动id
// @return bool 是否已完成
func (gm *GodPresentManager) IsLastFinish(id, group uint32) bool {
	if id == goxml.GetData().GodPresentInfoM.FirstID(group) {
		return true
	}

	lastID := id - 1
	lastAct := gm.GetGodPresent(lastID)
	if lastAct == nil {
		return false
	}

	return lastAct.HasSelected()
}

// InitGodPresent 初始化子活动数据
// TODO 这里是否要删除老的数据？因为活动期数的增加。特别老的数据会一直在里面，数据量还不小
func (gm *GodPresentManager) InitGodPresent(id, group uint32, groups []uint32) {
	gm.data.Datas = append(gm.data.Datas, &cl.GodPresentNew{
		Id:     id,
		Groups: groups,
	})
	//每3轮清除一次数据
	if id == goxml.GetData().GodPresentInfoM.FirstID(group) {
		if group%goxml.GetData().GodPresentConfigInfoM.GetClearHeroRound() == 1 {
			gm.data.HeroCount = make(map[uint32]uint32)
		}
	}
	//从第三轮开始要清除前面的数据
	if group > goxml.GodPresentGroupFirst+1 {
		lastGroup := group - 1
		stopId := goxml.GetData().GodPresentInfoM.FirstID(lastGroup)
		if stopId > 0 {
			newData := make([]*cl.GodPresentNew, 0, 2)
			hasChange := false
			for _, v := range gm.data.Datas {
				if v.GetId() >= stopId || v.GetSelectGroupKey() <= 0 {
					newData = append(newData, v)
				} else {
					l4g.Infof("user:%d InitGodPresent delete data id:%d, datas:%+v", gm.owner.ID(), v.GetId(), v)
					hasChange = true
				}
			}
			if hasChange {
				gm.data.Datas = newData
			}
		}
	}
	gm.Save()
}

func (gm *GodPresentManager) RecvAwards(g *GodPresent, groupKey uint32,
	redHeroes map[uint32]uint32, fragments map[uint32]uint32) {
	g.setSelectGroupKey(groupKey)
	g.setFragments(fragments)
	gm.addHeroCount(redHeroes)
	gm.Save()
}

func (gm *GodPresentManager) GetHeroCount() map[uint32]uint32 {
	return gm.data.HeroCount
}

func (gm *GodPresentManager) addHeroCount(heroCount map[uint32]uint32) {
	for sysID, count := range heroCount {
		gm.data.HeroCount[sysID] += count
	}
}

// IsOpenOld 活动是否开启: 除了最新一期，其他旧的期都用这个方法来判断
func (gm *GodPresentManager) IsOpenOld(id, createDay, group uint32) bool {
	info := goxml.GetData().GodPresentInfoM.Index(id)
	if info == nil {
		l4g.Errorf("user: %d GodPresentInfoM.IsOpen: info not exist, id:%d", gm.owner.ID(), id)
		return false
	}
	isOpen := createDay >= info.StartDay && createDay <= info.StopDay
	latestID := goxml.GetData().GodPresentInfoM.LatestID(group)
	if isOpen || gm.isDrawSummonAndNoSelect(latestID) {
		return true
	}

	return false
}

// 领取抽卡卷，但未选中英雄，针对策划案的补丁代码
func (gm *GodPresentManager) isDrawSummonAndNoSelect(id uint32) bool {
	for _, v := range gm.data.GetDatas() {
		if v.Id != id {
			continue
		}
		if v.SelectGroupKey == 0 && len(v.Groups) > 0 {
			return true
		}
	}

	return false
}

func (gm *GodPresentManager) isSelectHero(id uint32) bool {
	isSelect := false
	for _, v := range gm.data.GetDatas() {
		if v.Id != id {
			continue
		}
		if v.SelectGroupKey > 0 {
			isSelect = true
		}
	}

	return isSelect
}

// IsSendMail 活动开启，通知补发邮件
func (gm *GodPresentManager) IsSendMail(srv servicer) {
	gm.checkSendMail(srv)
}

func (gm *GodPresentManager) checkSendMail(srv servicer) {
	now := time.Now().Unix()
	createDay := gm.GetCreateDay(now)
	for i := goxml.GodPresentGroupFirst + 1; i <= int(goxml.GetData().GodPresentInfoM.GetMaxGroup()); i++ {
		curRound := uint32(i)
		preRound := curRound - 1
		if gm.data.IsSendMail[curRound] {
			//已经发送了,继续看下一个
			continue
		}
		endDay := goxml.GetData().GodPresentInfoM.EndDay(curRound)
		if createDay > endDay {
			continue
		}
		// 前一个活动的截止日
		endDayFirst := goxml.GetData().GodPresentInfoM.EndDay(preRound)
		latestIDFirst := goxml.GetData().GodPresentInfoM.LatestID(preRound)
		// 第一期活动的最后一个子活动选中英雄 或 创角时间大于第一期的结束日期，补发第二期抽卡邮件
		if gm.isSelectHero(latestIDFirst) || createDay > endDayFirst {
			GodPresentMail(srv, gm.owner, gm.GetMailID(srv, curRound))
			gm.setIsSendMail(curRound)
		}
		break
	}
}

func (gm *GodPresentManager) GetMailID(srv servicer, round uint32) uint32 {
	return goxml.GetData().GodPresentInfoM.GetMailID(round)
}

// 判断某一期活动是否开启
func (gm *GodPresentManager) IsOpen(now int64, info *goxml.GodPresentInfoExt) bool {
	createDay := gm.GetCreateDay(now)
	return goxml.GetData().GodPresentInfoM.IsOpen(info.ID, createDay)
}

func (gm *GodPresentManager) GetCreateDay(now int64) uint32 {
	fixedCreateTime := gm.getFixedCreateTime()
	return helper.DaysBetweenTimes(now, fixedCreateTime) + 1
}

func (gm *GodPresentManager) getFixedCreateTime() int64 {
	createTime := gm.owner.CreateTime()
	newGroup := goxml.GetData().GodPresentConfigInfoM.GetNewGroup()
	if newGroup == 0 {
		return createTime
	}
	newGroupTime := goxml.GetData().GodPresentConfigInfoM.GetNewGroupTime()
	if newGroupTime == 0 {
		return createTime
	}
	//要求的期活动的开启时间
	endDay := goxml.GetData().GodPresentInfoM.FirstStartDay(newGroup)
	fixedTime := newGroupTime - int64(endDay-1)*util.DaySecs
	if createTime < fixedTime {
		createTime = fixedTime
	}
	return createTime
}

func (gm *GodPresentManager) GetClassReplaceCount(group uint32) uint32 {
	if gm.data == nil || gm.data.ClassChangeCnt == nil {
		return 0
	}
	return gm.data.ClassChangeCnt[group]
}

func (gm *GodPresentManager) AddClassReplaceCount(group, addNum uint32) {
	if gm.data.ClassChangeCnt == nil {
		gm.data.ClassChangeCnt = make(map[uint32]uint32)
	}
	gm.data.ClassChangeCnt[group] += addNum
}
