package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
)

type TaskHandler interface {
	GetProgress() map[uint32]*cl.TaskTypeProgress
	GetOneTypeProgress(*goxml.TaskTypeInfo) *cl.TaskTypeProgress
	IsAwarded(uint32) bool
	ReceiveAward([]uint32)
}

func (u *User) CheckTaskFinish(pro *cl.TaskTypeProgress, typeID uint32, infoProgress uint64) bool {
	if typeID == 0 {
		//不需要任何进度就可以完成
		return true
	}
	taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(typeID)
	if taskTypeInfo == nil {
		return false
	}
	if taskTypeInfo.IsClient == goxml.ClientHandleTask && (taskTypeInfo.Type == event.AeCommunityTask || taskTypeInfo.Type == event.AeLoginBindAward) {
		return true
	}
	//pro := progress[typeID]
	if pro == nil {
		return false
	}
	if taskTypeInfo.FinishType == uint32(common.TASK_TYPE_FINISH_TYPE_FT_GREATER) {
		if pro.Progress >= infoProgress {
			return true
		}
	} else if taskTypeInfo.FinishType == uint32(common.TASK_TYPE_FINISH_TYPE_FT_LESS) {
		if pro.Progress <= infoProgress {
			return true
		}
	}
	return false
}

func (u *User) GetTaskProgress(progress map[uint32]*cl.TaskTypeProgress, typeID uint32) uint32 {
	pro := progress[typeID]
	if pro == nil {
		return uint32(0)
	}
	return uint32(pro.Progress)
}

// event 事件触发后，判断是否需要更新进度
func (u *User) TaskTypeOnEvent(userProgress map[uint32]*cl.TaskTypeProgress, event uint32,
	progress uint64, values []uint32) (map[uint32]*cl.TaskTypeProgress, bool) {
	if progress == 0 {
		l4g.Errorf("TaskTypeOnEvent. eventg:%d progress=0", event)
		return nil, false
	}

	infos := goxml.GetData().TaskTypeInfoM.IndexEvent(event)
	modifyPro := make(map[uint32]*cl.TaskTypeProgress)
	modify := false
	for _, info := range infos {
		switch info.CompareTypeParam {
		case goxml.CompareEqual:
			if info.Parameter1 == 0 || (len(values) > 0 && values[0] == info.Parameter1) {
				change := u.addTaskProgress(info, values, progress, userProgress, modifyPro)
				modify = modify || change
			}
		case goxml.CompareMoreThan:
			if len(values) > 0 && values[0] >= info.Parameter1 {
				change := u.addTaskProgress(info, values, progress, userProgress, modifyPro)
				modify = modify || change
			}
		}
	}
	return modifyPro, modify
}

func (u *User) addTaskProgress(info *goxml.TaskTypeInfo, values []uint32, progress uint64,
	userProgress map[uint32]*cl.TaskTypeProgress, modifyPro map[uint32]*cl.TaskTypeProgress) bool {

	pro := userProgress[info.Id]
	if pro == nil {
		pro = &cl.TaskTypeProgress{TaskTypeId: info.Id}
		userProgress[info.Id] = pro
	}

	modify := false
	//匹配到的事件
	if info.AddType == uint32(common.TASK_TYPE_PROGRESS_TYPE_PT_ADD) {
		pro.Progress += progress
		modifyPro[info.Id] = pro.Clone()
		modify = true
	} else if info.AddType == uint32(common.TASK_TYPE_PROGRESS_TYPE_PT_MAX) {
		if progress > pro.Progress {
			pro.Progress = progress
			modifyPro[info.Id] = pro.Clone()
			modify = true
		}
	} else if info.AddType == uint32(common.TASK_TYPE_PROGRESS_TYPE_PT_MIN) {
		if progress < pro.Progress || pro.Progress == 0 {
			pro.Progress = progress
			modifyPro[info.Id] = pro.Clone()
			modify = true
		}
	} else if info.AddType == uint32(common.TASK_TYPE_PROGRESS_TYPE_PT_UNIQUE) {
		if len(values) > 1 && values[1] > 0 { //复用一下values的第一2个数据，未来可能会做修改
			if _, exist := pro.Values[values[1]]; !exist {
				pro.Progress++
				pro.Values[values[1]] = true
				modifyPro[info.Id] = pro.Clone()
				modify = true
			}
		}
	}
	return modify
}

func (u *User) CalcTaskProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	pro := &cl.TaskTypeProgress{}
	switch taskTypeInfo.Type {
	case event.AeJoinInGuild:
		if u.GuildID() != 0 {
			pro.Progress = 1
		}
	//case event.AeFriendCount:
	//pro.Progress = uint64(fm.GetFriendNum(fu.ID()))
	//case event.AeHaveFixedStarHeroNumToX:
	//	pro.Progress = uint64(u.HeroManager().CalcHeroNumForStar(taskTypeInfo.Parameter1))
	//case event.AeHaveFixedStarArtifactToX:
	//	pro.Progress = uint64(u.ArtifactManager().CalcArtifactNumForStar(taskTypeInfo.Parameter1))
	case event.AeArtifactForgeLevelThanNumToX:
		pro.Progress = uint64(u.ArtifactManager().CalcArtifactNumForForgeLevel(taskTypeInfo.Parameter1))
	//case event.AeHaveFixedRareArtifactToX:
	//	pro.Progress = uint64(u.ArtifactManager().CalcArtifactNumForRare(taskTypeInfo.Parameter1))
	case event.AeHaveFixedRareEquipToX:
		pro.Progress = uint64(u.EquipManager().CalcEquipNumForEventGetNew(taskTypeInfo.Parameter1))
	case event.AeEquipEvolutionLevelThanNumToX:
		pro.Progress = uint64(u.EquipManager().CalcEquipNumForEventEvolution(taskTypeInfo.Parameter1))
	case event.AeEquipRefineLevelThanNumToX:
		pro.Progress = uint64(u.EquipManager().CalcEquipNumForEventRefine(taskTypeInfo.Parameter1))
	case event.AeEquipEnchantLevelThanNumToX:
		pro.Progress = uint64(u.EquipManager().CalcEquipNumForEventEnchant(taskTypeInfo.Parameter1))
	case event.AeHeroNumHave4EquipToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForHave4Equip(taskTypeInfo.Parameter1))
	case event.AeEquipStrengthMasterToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForEquipStrengthMaster(taskTypeInfo.Parameter1))
	case event.AeEquipRefineMasterToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForEquipRefineMaster(taskTypeInfo.Parameter1))
	case event.AeHaveFixedStageHeroToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForStage(taskTypeInfo.Parameter1))
	case event.AeHeroNumHave2EmblemToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForHave2Emblem(taskTypeInfo.Parameter1))
	case event.AeEmblemStrengthMasterToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForEmblemStrengthMaster(taskTypeInfo.Parameter1))
	case event.AeHaveFixedLevelHeroToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForLevel(taskTypeInfo.Parameter1))
	case event.AeHeroNumHave4EquipOfFixedEnchantToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForAllEquipEnchant(taskTypeInfo.Parameter1))
	case event.AeHeroNumHave4EquipOfFixedEvolutionToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForAllEquipEvolution(taskTypeInfo.Parameter1))
	case event.AeHasArtifactStrengthenToX:
		pro.Progress = uint64(u.ArtifactManager().CalcArtifactNumForStrengthLevel(taskTypeInfo.Parameter1))
	case event.AeHasHeroWearGemRareToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForWearGemRare(taskTypeInfo.Parameter1))
	case event.AeHasHeroWearEquipRefineToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForWearEquipRefine(taskTypeInfo.Parameter1))
	case event.AeHeroGemFixedRareHaveToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroGemNumForFixedRare(taskTypeInfo.Parameter1))
	case event.AeHaveActiveSomeFormationLinkHeroToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForActiveFormationLink(taskTypeInfo.Parameter1))
	case event.AeHaveActiveSomeGoddessLinkHeroToX:
		pro.Progress = uint64(u.HeroManager().CalcHeroNumForActiveGoddessLink(taskTypeInfo.Parameter1))
	case event.AeHaveFixedStarLegendHeroToX:
		pro.Progress = uint64(u.HeroManager().CalcLegendHeroNumForFixedStar(taskTypeInfo.Parameter1))
	}
	return pro
}

// 只更新提供的任务
func TaskProgressOnEvent(userProgress []*cl.TaskProgress, event uint32, progress uint64, values []uint32) []*cl.TaskProgress {
	changedProgress := make([]*cl.TaskProgress, 0)
	for _, taskTypeProgress := range userProgress {
		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskTypeProgress.TaskTypeId)
		if taskTypeInfo == nil {
			l4g.Errorf("cant find taskType %d", taskTypeProgress.TaskTypeId)
			continue
		}
		if taskTypeInfo.Type != event {
			continue
		}
		add := false
		switch taskTypeInfo.CompareTypeParam {
		case goxml.CompareEqual:
			if taskTypeInfo.Parameter1 == 0 || (len(values) > 0 && values[0] == taskTypeInfo.Parameter1) {
				add = true
			}
		case goxml.CompareMoreThan:
			if len(values) > 0 && values[0] >= taskTypeInfo.Parameter1 {
				add = true
			}
		}
		if add {
			if addTaskProgress(taskTypeProgress, taskTypeInfo, progress) {
				changedProgress = append(changedProgress, taskTypeProgress)
			}
		}
	}
	return changedProgress
}

func addTaskProgress(userProgress *cl.TaskProgress, info *goxml.TaskTypeInfo, progress uint64) bool {
	change := false
	if info.AddType == uint32(common.TASK_TYPE_PROGRESS_TYPE_PT_ADD) {
		userProgress.Progress += progress
		change = true
	} else if info.AddType == uint32(common.TASK_TYPE_PROGRESS_TYPE_PT_MAX) {
		if progress > userProgress.Progress {
			userProgress.Progress = progress
			change = true
		}
	} else if info.AddType == uint32(common.TASK_TYPE_PROGRESS_TYPE_PT_MIN) {
		if progress < userProgress.Progress || userProgress.Progress == 0 {
			userProgress.Progress = progress
			change = true
		}
	}
	return change
}

func CheckTaskFinish(pro *cl.TaskProgress, typeID uint32, infoProgress uint64) bool {
	if typeID == 0 {
		//不需要任何进度就可以完成
		return true
	}
	taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(typeID)
	if taskTypeInfo == nil {
		return false
	}
	if taskTypeInfo.IsClient == goxml.ClientHandleTask && (taskTypeInfo.Type == event.AeCommunityTask || taskTypeInfo.Type == event.AeLoginBindAward) {
		return true
	}
	if pro == nil {
		return false
	}
	if taskTypeInfo.FinishType == uint32(common.TASK_TYPE_FINISH_TYPE_FT_GREATER) {
		if pro.Progress >= infoProgress {
			return true
		}
	} else if taskTypeInfo.FinishType == uint32(common.TASK_TYPE_FINISH_TYPE_FT_LESS) {
		if pro.Progress <= infoProgress {
			return true
		}
	}
	return false
}
