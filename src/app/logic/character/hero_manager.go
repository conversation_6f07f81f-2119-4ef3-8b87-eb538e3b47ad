package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"slices"
	"sort"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
)

type HeroM struct {
	owner         *User
	heroes        map[uint64]*Hero
	specialHeroes map[uint64]*Hero

	top5Heros HeroSlice
	//创建，修改，删除英雄使用，定时保存使用
	changes map[uint64]*cl.HeroBody
	deletes map[uint64]uint64

	contractHeroes []*Hero // 共享养成 - 缔约英雄
}

type HeroSlice []*Hero

func (h HeroSlice) Len() int           { return len(h) }
func (h HeroSlice) Less(i, j int) bool { return h[i].power > h[j].power }
func (h HeroSlice) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h HeroSlice) Clone() HeroSlice {
	if h == nil {
		return nil
	}
	r := make([]*Hero, len(h))
	copy(r, h)
	return r
}

func (h HeroSlice) HasChange(old HeroSlice) bool {
	if len(h) != len(old) {
		return true
	}
	for i := 0; i < len(h); i++ {
		if h[i].data.Id != old[i].data.Id {
			return true
		}
	}
	return false
}

// 是否有相同的系统ID
func (h HeroSlice) GetSameSysHeroIndex(u *User, hero *Hero) int {
	for index, v := range h {
		if v.data.SysId == hero.data.SysId {
			return index
		}
	}
	return -1
}

func newHeroM(user *User) *HeroM {
	return &HeroM{
		owner:          user,
		heroes:         make(map[uint64]*Hero),
		specialHeroes:  make(map[uint64]*Hero),
		top5Heros:      make(HeroSlice, 0),
		changes:        make(map[uint64]*cl.HeroBody),
		deletes:        make(map[uint64]uint64),
		contractHeroes: make([]*Hero, 0, RequireHeroCountForContract),
	}
}

// Load ：加载数据
func (hm *HeroM) Load(dbHeroes map[uint64]*cl.HeroBody, isBattle bool) {
	for id, data := range dbHeroes {
		hero := initHeroFromData(data)
		info := goxml.GetData().HeroInfoM.Index(data.SysId)
		if info == nil {
			l4g.Errorf("Load heroes -> config not exist. uid:%d hid:%d sysID:%d",
				hm.owner.ID(), data.Id, data.SysId)
			continue
		}
		if info.Special == goxml.HeroCommon {
			hm.heroes[id] = hero
		} else {
			hm.specialHeroes[id] = hero
		}

		if hero.IsTagContract() {
			hm.contractHeroes = append(hm.contractHeroes, hero)
		}
	}
	if isBattle {
		return
	}
	for _, heroID := range hm.owner.GetTop5() {
		if heroID == 0 {
			break
		}
		hero := hm.Get(heroID)
		if hero == nil {
			l4g.Errorf("Load Top5 hero not exist. uid:%d hid:%d", hm.owner.ID(), heroID)
			continue
		}
		hm.top5Heros = append(hm.top5Heros, hero)
	}
}

func (hm *HeroM) GetAllCommonHero() map[uint64]*Hero {
	return hm.heroes
}

func (hm *HeroM) FlushAll() []*cl.Hero {
	heroes := make([]*cl.Hero, 0, len(hm.heroes))
	for _, h := range hm.heroes {
		heroes = append(heroes, h.Flush())
	}
	for _, h := range hm.specialHeroes {
		heroes = append(heroes, h.Flush())
	}
	return heroes
}

// GrpcClone : 给grpc接口 GetUser使用
func (hm *HeroM) GrpcClone() map[uint64]*cl.HeroBody {
	heroes := make(map[uint64]*cl.HeroBody, len(hm.heroes))
	for id, hero := range hm.heroes {
		if hero.GetData() != nil {
			heroes[id] = &cl.HeroBody{SysId: hero.GetData().SysId}
		}
	}
	return heroes
}

// GetHeroFromAll ：从全部英雄中获取一个
func (hm *HeroM) GetHeroFromAll(id uint64) *Hero {
	if hero := hm.Get(id); hero != nil {
		return hero
	}
	return hm.GetSpecial(id)
}

// Get ：获取一个常规英雄
func (hm *HeroM) Get(id uint64) *Hero {
	return hm.heroes[id]
}

// GetSpecial ：获取一个特殊英雄
func (hm *HeroM) GetSpecial(id uint64) *Hero {
	return hm.specialHeroes[id]
}

// GetCount ：全部英雄数量
func (hm *HeroM) GetCount() uint32 {
	return uint32(len(hm.heroes)) + uint32(len(hm.specialHeroes))
}

// GetCommonCount ：常规英雄数量
func (hm *HeroM) GetCommonCount() uint32 {
	return uint32(len(hm.heroes))
}

// 检查常规不重复英雄数量是否达标
// @param int target 目标数量
// @return bool
func (hm *HeroM) IsUniqHeroEnough(target int) bool {
	uniqHeroList := make(map[uint32]struct{})
	for _, hero := range hm.heroes {
		uniqHeroList[hero.GetHeroSysID()] = struct{}{}
		if len(uniqHeroList) >= target {
			return true
		}
	}
	return false
}

func (hm *HeroM) Add(srv servicer, id uint64, sysID uint32, reason uint32) *Hero {
	hero := newHero(id, sysID)
	if hero == nil {
		l4g.Errorf("user: %d new hero instance failed. hid: %d sysID: %d", hm.owner.ID(), id, sysID)
		return nil
	}

	heroData := hero.GetData()
	info := goxml.GetData().HeroInfoM.Index(heroData.SysId)
	if info == nil {
		l4g.Errorf("Add hero -> config not exist. uid:%d hid:%d sysID:%d", hm.owner.ID(), id, sysID)
		return nil
	}
	if info.Special == goxml.HeroCommon {
		hm.heroes[id] = hero
	} else {
		hm.specialHeroes[id] = hero
	}
	hm.changes[id] = heroData

	//抛出事件
	hm.owner.FireCommonEvent(srv.EventM(), event.IeNewHero, heroData.GetId(), sysID, heroData.Star, reason)

	return hero
}

// Delete ：消耗英雄
func (hm *HeroM) Delete(id uint64, srv servicer) {
	if _, ok := hm.heroes[id]; ok {
		delete(hm.heroes, id)
	} else {
		delete(hm.specialHeroes, id)
	}
	hm.deletes[id] = id
	delete(hm.changes, id)
}

// Change ：英雄转换 - 仅限常规英雄
// func (hm *HeroM) Change(oldID, newID uint64) {
// 	hm.heroes[newID] = hm.heroes[oldID]
// 	hm.Delete(oldID)
// }

func (hm *HeroM) SetChange(id uint64) {
	hm.changes[id] = hm.GetHeroFromAll(id).GetData()
	delete(hm.deletes, id)
}

// CheckHeroExist ：检查英雄是否存在
func (hm *HeroM) CheckHeroExist(id uint64) bool {
	if _, ok := hm.heroes[id]; ok {
		return true
	} else if _, ok := hm.specialHeroes[id]; ok {
		return true
	}
	return false
}

// Save ：增量保存英雄数据变化
func (hm *HeroM) Save(msg *r2l.OpHeroes) bool {
	success := false
	changes := len(hm.changes)
	if changes > 0 {
		msg.Changes = make([]*cl.HeroBody, 0, changes)
		for id, k := range hm.changes {
			msg.Changes = append(msg.Changes, k.Clone())
			delete(hm.changes, id)
		}
		success = true
	}
	deletes := len(hm.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint64, 0, deletes)
		for id := range hm.deletes {
			msg.Deletes = append(msg.Deletes, id)
			delete(hm.deletes, id)
		}
		success = true
	}
	return success
}

func (hm *HeroM) CalcLevelUpCost(current, num uint32) []*cl.Resource {
	target := current + num
	costs := make([]*cl.Resource, 0, num)
	for i := current; i < target; i++ {
		info := goxml.GetData().HeroLevelInfoM.Index(i)
		if info == nil {
			l4g.Errorf("config cost not exist. target level :%d", i)
			return nil
		}
		costs = append(costs, info.Costs...)
	}
	return costs
}

func (hm *HeroM) CheckRepeat(heroIds []uint64) bool {
	list := make(map[uint64]struct{})
	for _, v := range heroIds {
		if _, exist := list[v]; exist {
			return false
		}
		list[v] = struct{}{}
	}
	return true
}

// 计算升星被吃返还的资源
func (hm *HeroM) CalcHeroesBeEatenReturnRes(heroIds []uint64) []*cl.Resource {
	retRes := make([]*cl.Resource, 0, len(heroIds))
	for _, id := range heroIds {
		hero := hm.GetHeroFromAll(id)
		retRes = append(retRes, hero.calcReviveReturnRes(true)...)
		retRes = append(retRes, hero.calcGemReturnRes()...)
	}
	return retRes
}

// 计算重生返还的资源
func (hm *HeroM) CalcHeroesReviveReturnRes(heroIds []uint64, resetAwakenLevel bool) []*cl.Resource {
	retRes := make([]*cl.Resource, 0, len(heroIds))
	for _, id := range heroIds {
		hero := hm.GetHeroFromAll(id)
		retRes = append(retRes, hero.calcReviveReturnRes(resetAwakenLevel)...)
	}
	return retRes
}

// 计算分解返还的资源
func (hm *HeroM) CalcHeroesDecomposeReturnRes(heroIds []uint64) []*cl.Resource {
	retRes := make([]*cl.Resource, 0, len(heroIds))
	for _, id := range heroIds {
		hero := hm.GetHeroFromAll(id)
		retRes = append(retRes, hero.CalcDecomposeReturnRes()...)
		retRes = append(retRes, hero.calcGemReturnRes()...)
	}
	return retRes
}

// RemoveHeroOutfits ：英雄被消耗时，处理身上装配的全部外部物品
func (hm *HeroM) RemoveHeroOutfits(heroIds []uint64) {
	var gemIds []uint64
	var emblemIds []uint64
	var equipIDs []uint64
	for _, v := range heroIds {
		hero := hm.Get(v)
		if hero != nil {
			//获取宝石id
			gemIds = append(gemIds, hero.RemoveAllGem()...)
			//获取纹章id
			emblemIds = append(emblemIds, hero.RemoveAllEmblem()...)
			//获取装备id
			equipIDs = append(equipIDs, hero.RemoveAllEquip()...)
		}
	}

	//脱下宝石
	if len(gemIds) > 0 {
		hm.owner.GemManager().RemoveHeroID(gemIds)
	}
	//脱下纹章
	if len(emblemIds) > 0 {
		hm.owner.EmblemManager().RemoveHeroID(emblemIds)
	}
	//脱下装备
	if len(equipIDs) > 0 {
		hm.owner.EquipManager().RemoveHeroID(equipIDs)
	}
	//脱下赛季装备
	hm.owner.SeasonJewelryManager().RemoveHeroID(heroIds)
}

// SetHeroAttrChange : 给所有英雄设置属性标识，用于判断获取英雄属性时是否需要重新计算
func (hm *HeroM) SetHeroAttrChange() {
	for _, hero := range hm.heroes {
		hero.SetHeroAttrChange()
	}
}

// 设置英雄属性重新计算标识
func (hm *HeroM) SetHeroAttrChangeBySysID(sysID uint32) {
	for _, hero := range hm.heroes {
		if hero.GetHeroSysID() == sysID {
			hero.SetHeroAttrChange()
		}
	}
}

// 设置英雄属性重新计算标识
func (hm *HeroM) SetHeroAttrChangeByHeroID(heroID uint64) {
	for _, hero := range hm.heroes {
		if hero.GetHid() == heroID {
			hero.SetHeroAttrChange()
		}
	}
}

// 设置英雄属性重新计算标识
func (hm *HeroM) SetSeasonlinkHeroAttrChange() {
	for _, hero := range hm.heroes {
		if hero == nil {
			continue
		}
		if _, exist := hero.raisePSs[RaisePSTypeSeasonLink]; exist {
			hero.SetHeroAttrChange()
		}
	}
}

// 获取星级最高的英雄id
func (hm *HeroM) GetMaxStarHeroID() uint64 {
	var hid uint64
	var star uint32
	for id, h := range hm.heroes {
		if h.GetStar() > star {
			star = h.GetStar()
			hid = id
		}
	}
	return hid
}

// 获取星级最高的英雄
// @param []uint64 existIDs 已存在的英雄id列表（需排除）
// @return *Hero
//
//nolint:varnamelen
func (hm *HeroM) GetMaxStarHero(existIDs []uint64) *Hero {
	existSysIDs := make([]uint32, 0, len(existIDs))
	for _, id := range existIDs {
		h := hm.Get(id)
		if h == nil {
			l4g.Errorf("user:%d heroM.GetMaxStarHero: existIDs hero not exist, id:%d", hm.owner.ID(), id)
			continue
		}
		existSysIDs = append(existSysIDs, h.GetHeroSysID())
	}

	var hero *Hero
	for _, h := range hm.heroes {
		var hidExist bool
		for _, id := range existIDs {
			if h.GetHid() == id {
				hidExist = true
				break
			}
		}
		if hidExist {
			continue
		}

		var sysIdExist bool
		for _, sysID := range existSysIDs {
			if h.GetHeroSysID() == sysID {
				sysIdExist = true
				break
			}
		}
		if sysIdExist {
			continue
		}

		if hero == nil || h.GetStar() > hero.GetStar() {
			hero = h
		}
	}
	return hero
}

func (hm *HeroM) CalcHeroNumForHave4Equip(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Equipment) < 4 { //nolint:mnd
			continue
		}
		minRare := uint32(0)
		for _, equipID := range hero.data.Equipment {
			equipM := hm.owner.EquipManager()
			equip := equipM.Get(equipID)
			if equip == nil {
				continue
			}
			equipInfo := goxml.GetData().EquipInfoM.Index(equip.Data.SysId)
			if equipInfo == nil {
				continue
			}
			if minRare == 0 {
				minRare = equipInfo.Rare
			}
			if equipInfo.Rare < minRare {
				minRare = equipInfo.Rare
			}
		}
		if param <= minRare {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForEquipRefineMaster(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Equipment) < 4 { //nolint:mnd
			continue
		}
		minMasterLevel := uint32(0)
		for _, equipID := range hero.data.Equipment {
			equipM := hm.owner.EquipManager()
			equip := equipM.Get(equipID)
			if equip == nil {
				continue
			}
			if minMasterLevel == 0 {
				minMasterLevel = equip.Data.RefineLevel
			}
			if equip.Data.RefineLevel < minMasterLevel {
				minMasterLevel = equip.Data.RefineLevel
			}
			minMasterLevel = goxml.GetData().MasterDataInfoM.GetMasterLevel(goxml.MasterEquipRefine, minMasterLevel)
		}
		if param <= minMasterLevel {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForEquipStrengthMaster(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Equipment) < int(EquipWearMaxNum) {
			continue
		}
		minMasterLevel := uint32(0)
		for _, equipID := range hero.data.Equipment {
			equipM := hm.owner.EquipManager()
			equip := equipM.Get(equipID)
			if equip == nil {
				continue
			}
			if minMasterLevel == 0 {
				minMasterLevel = equip.Data.StrengthLevel
			}
			if equip.Data.StrengthLevel < minMasterLevel {
				minMasterLevel = equip.Data.StrengthLevel
			}
		}
		minMasterLevel = goxml.GetData().MasterDataInfoM.GetMasterLevel(goxml.MasterEquipStrength, minMasterLevel)
		if param <= minMasterLevel {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForStage(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if param <= hero.data.Stage {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForLevel(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if param <= hero.data.Level {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForHave2Emblem(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Emblem) < goxml.EmblemPosMax {
			continue
		}
		minRare := uint32(0)
		for _, emblemID := range hero.data.Emblem {
			emblemM := hm.owner.EmblemManager()
			emblem := emblemM.Get(emblemID)
			if emblem == nil {
				continue
			}
			emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
			if emblemInfo == nil {
				continue
			}
			if minRare == 0 {
				minRare = emblemInfo.Rare
			}
			if emblemInfo.Rare < minRare {
				minRare = emblemInfo.Rare
			}
		}
		if param <= minRare {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForEmblemStrengthMaster(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Emblem) < 2 { //nolint:mnd
			continue
		}
		minMasterLevel := uint32(0)
		for _, emblemID := range hero.data.Emblem {
			emblemM := hm.owner.EmblemManager()
			emblem := emblemM.Get(emblemID)
			if emblem == nil {
				continue
			}
			if minMasterLevel == 0 {
				minMasterLevel = emblem.Data.Level
			}
			if emblem.Data.Level < minMasterLevel {
				minMasterLevel = emblem.Data.Level
			}
		}
		minMasterLevel = goxml.GetData().MasterDataInfoM.GetMasterLevel(goxml.MasterEmblemStrength, minMasterLevel)
		if param <= minMasterLevel {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForAllEquipEnchant(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Equipment) < int(EquipWearMaxNum) {
			continue
		}
		minEnchantLevel := uint32(0)
		for _, equipID := range hero.data.Equipment {
			equipM := hm.owner.EquipManager()
			equip := equipM.Get(equipID)
			if equip == nil {
				continue
			}
			if minEnchantLevel == 0 {
				minEnchantLevel = equip.Data.EnchantLevel
			}
			if equip.Data.EnchantLevel < minEnchantLevel {
				minEnchantLevel = equip.Data.EnchantLevel
			}
		}
		if param <= minEnchantLevel {
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForAllEquipEvolution(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Equipment) < int(EquipWearMaxNum) {
			continue
		}
		minEvolutionLevel := uint32(0)
		for _, equipID := range hero.data.Equipment {
			equipM := hm.owner.EquipManager()
			equip := equipM.Get(equipID)
			if equip == nil {
				continue
			}
			if minEvolutionLevel == 0 {
				minEvolutionLevel = equip.Data.EvolutionLevel
			}
			if equip.Data.EvolutionLevel < minEvolutionLevel {
				minEvolutionLevel = equip.Data.EnchantLevel
			}
		}
		if param <= minEvolutionLevel {
			num++
		}
	}
	return num
}

func (hm *HeroM) RemoveChangingSysID(hid uint64) {
	hero := hm.Get(hid)
	if hero != nil {
		if hero.GetChangingSysID() == 0 {
			return
		}

		hero.SetChangingSysID(0)
		hm.SetChange(hid)
	}
}

// 英雄回退相关数据更新
func (hm *HeroM) DoRevive(srv servicer, hero *Hero, resetAwakenLevel bool, from uint32) (uint32, []*cl.Resource) {
	retStatus := uint32(cret.RET_OK)
	var awardRes []*cl.Resource

	//返还资源
	res := hm.CalcHeroesReviveReturnRes([]uint64{hero.GetHid()}, resetAwakenLevel)
	if len(res) > 0 {
		retStatus, awardRes = hm.owner.Award(srv, res, AwardTagMail, from, 0)
		if retStatus != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d DoRevive: award err. hid:%d, from:%d",
				hm.owner.ID(), hero.GetHid(), from)
			return retStatus, awardRes
		}
	}

	//处理英雄身上全部外装
	hm.RemoveHeroOutfits([]uint64{hero.GetHid()})

	//更新英雄数据
	hero.SetLevel(goxml.GetData().HeroLevelInfoM.GetDefaultLv())
	hero.SetStage(goxml.GetData().HeroStageInfoM.GetDefaultStage())
	if resetAwakenLevel {
		hero.SetAwakenLevel(0)
	}
	hm.SetChange(hero.GetHid())

	// 设置更新英雄属性的标识
	hero.SetHeroAttrChange()
	return retStatus, awardRes
}

// 检查是否满足升星限制
// @param uint32 star 要升至的星级
// @return bool
func (hm *HeroM) CheckHeroStarLimit(star uint32) bool {
	return star <= hm.owner.HeroStarLimit()
}

// 检查英雄 - 水晶成就相关
// @param servicer srv
// @param uint32 star 要升至的星级
// @param uint32 race 英雄种族
// @param uint32 sysID 英雄系统id
func (hm *HeroM) CheckHeroesForCrystalTask(srv servicer, star, race, sysID uint32) {
	//水晶成就 - 常规任务
	eventID := getEventIDByHeroRace(race)
	if eventID > 0 {
		hm.owner.FireCommonEvent(srv.EventM(), eventID, 1, star)
	}

	//更新升星限制数据
	if hm.checkStarLimitCanUpgrade(star) {
		targetStarLimitLv := hm.owner.HeroStarLimit() + 1
		hm.owner.SetHeroStarLimit(targetStarLimitLv)
		msg := &cl.L2C_HeroUpdateStarLimit{
			Ret:       uint32(cret.RET_OK),
			LimitStar: targetStarLimitLv,
		}
		hm.owner.SendCmdToGateway(cl.ID_MSG_L2C_HeroUpdateStarLimit, msg)
	}

	//拥有多少星的x英雄
	hm.owner.FireCommonEvent(srv.EventM(), uint32(event.AeOwnHeroXStar), uint64(star), sysID)
	// 将指定品质的英雄升到X星
	hm.owner.FireCommonEvent(srv.EventM(), uint32(event.AeRaceHeroUpStarToX), uint64(star), race)
}

func getEventIDByHeroRace(race uint32) uint32 {
	switch race {
	case goxml.RaceEmpire:
		return event.AeEmpireHeroXStar
	case goxml.RaceForest:
		return event.AeForestHeroXStar
	case goxml.RaceMoon:
		return event.AeMoonHeroXStar
	case goxml.RaceProtoss:
		return event.AeProtossHeroXStar
	case goxml.RaceDemon:
		return event.AeDemonHeroXStar
	default:
		return 0
	}
}

// 检查升星限制等级是否可升级
// @param uint32 star 最新升至的星级（触发此逻辑的星级）
// @return bool 是否需要更新（升级）升星限制数据
func (hm *HeroM) checkStarLimitCanUpgrade(star uint32) bool {
	if !hm.isNeedCheckForStarLimit(star) {
		return false
	}

	var starLimitHeroCount uint32
	starLimitRequireStar := hm.getNextStarLimitRequireStar()
	for _, hero := range hm.GetAllCommonHero() {
		hero := hero.GetData()
		if hero == nil {
			l4g.Errorf("user: %d checkStarLimitCanUpgrade. hero is nil", hm.owner.ID())
			return false
		}

		if hero.Star < GetMinRequireStar() {
			continue
		}

		if hero.Star >= starLimitRequireStar {
			starLimitHeroCount++
			if starLimitHeroCount >= StarLimitStarLvRequireCount {
				return true
			}
		}
	}
	return false
}

func (hm *HeroM) isNeedCheckForStarLimit(star uint32) bool {
	currentStarLimitLv := hm.owner.HeroStarLimit()
	if star < GetStarLimitMinRequireStarByStar(currentStarLimitLv) {
		return false
	}

	if currentStarLimitLv >= StarLimitMaxStarLv {
		return false
	}

	return true
}

func (hm *HeroM) getNextStarLimitRequireStar() uint32 {
	targetStarLimitLv := hm.owner.HeroStarLimit() + 1
	return GetStarLimitRequireStar(targetStarLimitLv)
}

func (hm *HeroM) TestCalPower() int64 {
	start := time.AccurateNow()
	for _, hero := range hm.heroes {
		hero.testCalPower(hm.owner)
	}
	return time.Since(start).Nanoseconds() / int64(len(hm.heroes))
}

// 英雄兑换相关数据更新
func (hm *HeroM) DoConversion(hero *Hero) ([]*cl.Resource, []*cl.Resource) {
	var costRes, awardRes []*cl.Resource

	//返还资源
	if hero.GetLevel() != goxml.GetData().HeroLevelInfoM.GetDefaultLv() ||
		hero.GetStage() != goxml.GetData().HeroStageInfoM.GetDefaultStage() ||
		hero.GetAwakenLevel() > 0 {
		awardRes = hm.CalcHeroesReviveReturnRes([]uint64{hero.GetHid()}, true)
	}

	//处理英雄身上全部外装
	hm.RemoveHeroOutfits([]uint64{hero.GetHid()})

	//更新英雄数据
	hero.SetLevel(goxml.GetData().HeroLevelInfoM.GetDefaultLv())
	hero.SetStage(goxml.GetData().HeroStageInfoM.GetDefaultStage())
	hero.SetAwakenLevel(0)

	awardRes = append(awardRes, goxml.GetData().ConversionInfoM.GetAwards()...)
	hm.SetChange(hero.GetHid())

	costRes = append(costRes, &cl.Resource{
		Id:    hero.GetHid(),
		Type:  uint32(common.RESOURCE_HERO),
		Value: hero.GetData().SysId,
		Count: 1,
	})

	// 设置更新英雄属性的标识
	hero.SetHeroAttrChange()
	return costRes, awardRes
}

// CalcHeroNumForWearGemRare X个英雄穿戴齐一套【指定品质】及以上的宝石
func (hm *HeroM) CalcHeroNumForWearGemRare(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Gem) < GemSlotNum {
			continue
		}
		minRare := uint32(0)
		for _, gemID := range hero.data.Gem {
			gem := hm.owner.GemManager().GetGem(gemID)
			if gem == nil {
				continue
			}
			gemInfo := goxml.GetData().GemInfoM.Index(gem.data.SysId)
			if gemInfo == nil {
				continue
			}
			if minRare == 0 {
				minRare = gemInfo.Rare
			}
			if gemInfo.Rare < minRare {
				minRare = gemInfo.Rare
			}
		}
		if param <= minRare {
			num++
		}
	}
	return num
}

// CalcHeroNumForWearEquipRefine X个英雄穿戴齐一套【精炼x级】及以上的装备
func (hm *HeroM) CalcHeroNumForWearEquipRefine(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		if len(hero.data.Equipment) < EquipNum {
			continue
		}
		minRefineLevel := uint32(0)
		for _, equipID := range hero.data.Equipment {
			equip := hm.owner.EquipManager().Get(equipID)
			if equip == nil {
				continue
			}
			if minRefineLevel == 0 {
				minRefineLevel = equip.Data.RefineLevel
			}
			if equip.Data.RefineLevel < minRefineLevel {
				minRefineLevel = equip.Data.RefineLevel
			}
		}
		if param <= minRefineLevel {
			num++
		}
	}
	return num
}

// 拥有X个Y品质以上宝石
func (hm *HeroM) CalcHeroGemNumForFixedRare(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		for _, gem := range hero.data.Gems {
			if gem == nil {
				continue
			}
			gemUpgradeInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(gem.Slot, gem.Level)
			if gemUpgradeInfo != nil && gemUpgradeInfo.Rare >= param {
				num++
			}
		}
	}
	return num
}

func (hm *HeroM) CalcHeroNumForActiveFormationLink(param uint32) uint32 {
	heroSysIds := make(map[uint32]struct{})

	num := uint32(0)
	for _, h := range hm.heroes { //nolint:varnamelen
		sysID := h.GetHeroSysID()
		if _, exist := heroSysIds[sysID]; exist {
			continue
		}
		heroInfo := goxml.GetData().HeroInfoM.Index(sysID)
		if heroInfo == nil {
			continue
		}
		if param != 0 && heroInfo.Link3ID != param {
			continue
		}
		leftGem := h.GetGem(uint32(common.GEM_CONFIG_GEM_LEFT_SLOT))
		rightGem := h.GetGem(uint32(common.GEM_CONFIG_GEM_RIGHT_SLOT))
		if leftGem == nil || rightGem == nil {
			continue
		}
		leftGemInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(leftGem.Slot, leftGem.Level)
		rightGemInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(rightGem.Slot, rightGem.Level)

		if leftGemInfo == nil || rightGemInfo == nil {
			continue
		}
		if h.isActiveLink3(leftGemInfo, rightGemInfo) {
			heroSysIds[sysID] = struct{}{}
			num++
		}
	}
	return num
}

//nolint:varnamelen
func (hm *HeroM) CalcHeroNumForActiveGoddessLink(param uint32) uint32 {
	heroSysIds := make(map[uint32]struct{})

	num := uint32(0)
	for _, h := range hm.heroes {
		sysID := h.GetHeroSysID()
		if _, exist := heroSysIds[sysID]; exist {
			continue
		}
		if h.data.Star < goxml.GetData().ConfigInfoM.GetLink4UnLockHeroStar() {
			continue
		}
		heroInfo := goxml.GetData().HeroInfoM.Index(sysID)
		if heroInfo == nil {
			continue
		}
		if param != 0 && heroInfo.Link4ID != param {
			continue
		}
		emblemIds := h.GetAllEmblem()
		if len(emblemIds) != goxml.EmblemPos4 {
			continue
		}
		var notOk bool
		var minRare uint32
		for _, eid := range emblemIds {
			emblem := hm.owner.EmblemManager().Get(eid)
			if emblem == nil || emblem.Data == nil {
				notOk = true
				break
			}
			emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
			if emblemInfo == nil {
				notOk = true
				break
			}
			if minRare == 0 {
				minRare = emblemInfo.Rare
			} else if emblemInfo.Rare < minRare {
				minRare = emblemInfo.Rare
			}
		}
		if notOk {
			continue
		}
		if minRare >= goxml.GetData().EmblemConfigInfoM.GetLink4UnlockCondition() {
			heroSysIds[sysID] = struct{}{}
			num++
		}
	}
	return num
}

func (hm *HeroM) CalcLegendHeroNumForFixedStar(param uint32) uint32 {
	num := uint32(0)
	for _, hero := range hm.heroes {
		heroInfo := goxml.GetData().HeroInfoM.Index(hero.GetHeroSysID())
		if heroInfo == nil || heroInfo.Rare < goxml.HeroRareTrueFive { // 需要传说英雄
			continue
		}
		if hero.GetStar() >= param {
			num++
		}
	}
	return num
}

// 当前同名英雄，是否已达满星
func (hm *HeroM) ReachedTopStar(id uint32) bool {
	for _, hero := range hm.GetAllCommonHero() {
		if id == hero.GetHeroSysID() {
			if hero.GetStar() >= StarLimitMaxStarLv {
				return true
			}
		}
	}
	return false
}

// 当前是否拥有某个英雄
func (hm *HeroM) IsHaveHero(id uint32) bool {
	for _, hero := range hm.GetAllCommonHero() {
		if id == hero.GetHeroSysID() {
			return true
		}
	}
	return false
}

type HeroIDAndPower struct {
	id    uint64
	power uint64
}

// 战斗英雄列表内，按战力排序的不同英雄id列表
// @param int 数量上限
// @return []uint64
func (hm *HeroM) CalcHeroIDListByPowerInBattleHeroes(maxCount int) []uint64 {
	//生成最大战力的不同英雄列表
	diffHeroMap := make(map[uint32]*HeroIDAndPower)
	for _, hero := range hm.GetAllCommonHero() {
		if hero.IsInBattleList() {
			if v, exist := diffHeroMap[hero.GetHeroSysID()]; !exist {
				diffHeroMap[hero.GetHeroSysID()] = &HeroIDAndPower{hero.GetHid(), hero.GetHeroPower(hm.owner)}
			} else {
				power := hero.GetHeroPower(hm.owner)
				if power > v.power {
					v.id = hero.GetHid()
					v.power = power
				}
			}
		}
	}

	//按战力排序
	tmpList := make([]*HeroIDAndPower, 0, len(diffHeroMap))
	for _, v := range diffHeroMap {
		tmpList = append(tmpList, v)
	}
	sort.Slice(tmpList, func(i, j int) bool {
		if tmpList[i].power != tmpList[j].power {
			return tmpList[i].power > tmpList[j].power
		}
		return tmpList[i].id < tmpList[j].id
	})

	//生成英雄id列表
	var count int
	hidList := make([]uint64, 0, maxCount)
	for _, v := range tmpList {
		hidList = append(hidList, v.id)
		count++
		if count >= maxCount {
			break
		}
	}
	return hidList
}

func (hm *HeroM) GmSetAwakenLevel(srv servicer, heroSysId, awakenLevel uint32) cret.RET {
	heroInfo := goxml.GetData().HeroInfoM.Index(heroSysId)
	if heroInfo == nil {
		l4g.Errorf("GmSetAwakenLevel: hero not exist in HeroInfoM, uid:%d, heroSysId:%d awakenLevel:%d", hm.owner.ID(), heroSysId, awakenLevel)
		return cret.RET_HERO_NOT_EXIST
	}
	if !heroInfo.IsAwakenable() {
		l4g.Errorf("GmSetAwakenLevel: hero not awakenable, uid:%d, heroSysId:%d awakenLevel:%d", hm.owner.ID(), heroSysId, awakenLevel)
		return cret.RET_HERO_NOT_AWAKENABLE
	}
	if awakenLevel > goxml.GetData().HeroAwakenLevelInfoM.MaxId() {
		l4g.Errorf("GmSetAwakenLevel: awaken level out of limit, uid:%d, heroSysId:%d awakenLevel:%d", hm.owner.ID(), heroSysId, awakenLevel)
		return cret.RET_HERO_AWAKEN_LEVEL_OUT_OF_LIMIT
	}
	heroIDs := make([]uint64, 0)
	for _, hero := range hm.heroes {
		if hero.GetHeroSysID() != heroSysId {
			continue
		}
		if !goxml.GetData().ConfigInfoM.IsHeroAwakenStarUnlocked(hero.GetStar()) {
			continue
		}
		hero.SetAwakenLevel(awakenLevel)
		hm.SetChange(hero.GetHid())
		heroIDs = append(heroIDs, hero.data.Id)
	}
	if len(heroIDs) == 0 {
		l4g.Errorf("GmSetAwakenLevel: no hero awaken level unlocked, uid:%d, heroSysId:%d awakenLevel:%d", hm.owner.ID(), heroSysId, awakenLevel)
		return cret.RET_NO_HERO_AWAKEN_LEVEL_UNLOCKED
	}
	l4g.Info("GmSetAwakenLevel: success, uid:%d, heroSysId:%d awakenLevel:%d heroIDs:%v", hm.owner.ID(), heroSysId, awakenLevel, heroIDs)

	//推送消息
	smsg := &cl.L2C_HeroList{
		Ret:    uint32(cret.RET_OK),
		Heroes: hm.FlushAll(),
	}
	hm.owner.SendCmdToGateway(cl.ID_MSG_L2C_HeroList, smsg)

	return cret.RET_OK
}

// 判断英雄是否处于初始状态
func (hm *HeroM) IsHeroInitial(heroId uint64) bool {
	hero := hm.GetHeroFromAll(heroId)
	if hero == nil || hero.data == nil {
		l4g.Errorf("user %d IsHeroesInitial: no heroData, hid %d", hm.owner.ID(), heroId)
		return false
	}

	// 检查等级
	if hero.data.Level > goxml.GetData().HeroLevelInfoM.GetDefaultLv() {
		l4g.Errorf("user %d IsHeroesInitial: heroLevel ", hm.owner.ID())
		return false
	}

	// 检查星级
	heroInfo := goxml.GetData().HeroInfoM.GetRecordById(hero.GetHeroSysID())
	if heroInfo == nil {
		return false
	}
	if hero.GetStar() != heroInfo.Star {
		l4g.Errorf("user %d IsHeroesInitial: heroStar %d not defaultStar %d ", hm.owner.ID(), hero.GetStar(), heroInfo.Star)
		return false
	}

	// 检查突破
	defaultStage := goxml.GetData().HeroStageInfoM.GetDefaultStage()
	if hero.data.Stage > defaultStage {
		l4g.Errorf("user %d IsHeroesInitial: heroStage %d > defaultStage %d ", hm.owner.ID(), hero.data.Stage, defaultStage)
		return false
	}

	// 检查觉醒等级
	if hero.data.AwakenLevel > 0 {
		l4g.Errorf("user %d IsHeroesInitial: awakenLevel %d > 0 ", hm.owner.ID(), hero.data.AwakenLevel)
		return false
	}

	// 检查宝石等级
	for _, gem := range hero.data.Gems {
		if gem.Level > 0 {
			l4g.Errorf("user %d IsHeroesInitial: gemLevel %d > 0 ", hm.owner.ID(), gem.Level)
			return false
		}
	}

	// 检查是否穿戴装备
	if len(hero.GetAllEquipment()) > 0 {
		l4g.Errorf("user %d IsHeroesInitial: hero has equipment", hm.owner.ID())
		return false
	}

	// 检查是否穿戴符文
	if len(hero.GetAllEmblem()) > 0 {
		l4g.Errorf("user %d IsHeroesInitial: hero has emblem", hm.owner.ID())
		return false
	}

	// 检查品质
	if hero.GetRare() != 0 {
		l4g.Errorf("user %d IsHeroesInitial: hero rare %d > 0 ", hm.owner.ID(), hero.GetRare())
		return false
	}
	return true
}

// GetAwakenToTargetLevelNeedCostItemNum
// @Description: 获取英雄觉醒到目标等级所需要消耗的觉醒道具数量
func (hm *HeroM) GetAwakenToTargetLevelNeedCostItemNum(curLevel, targetLevel uint32) uint32 {
	if curLevel >= targetLevel {
		return 0
	}
	var num uint32
	for i := curLevel + 1; i <= targetLevel; i++ {
		awakenLevelInfo := goxml.GetData().HeroAwakenLevelInfoM.GetRecordById(i)
		if awakenLevelInfo == nil {
			l4g.Errorf("GetAwakenToTargetLevelNeedCostItemNum: no awakenLevelInfo, level %d", i)
			return 0
		}
		num += awakenLevelInfo.Count
	}
	return num
}

// GetAwakeItemGap
// @Description: 获取觉醒道具缺口
func (hm *HeroM) GetAwakeItemGap(awakenLevel, bagNum uint32) uint32 {
	// 获取英雄觉醒到最高等级还需要多少道具
	maxLevel := goxml.GetData().HeroAwakenLevelInfoM.MaxId()
	leftCostNum := hm.GetAwakenToTargetLevelNeedCostItemNum(awakenLevel, maxLevel)
	// 减去背包中的同种觉醒道具数量
	if leftCostNum <= bagNum {
		return 0
	}
	return leftCostNum - bagNum
}

// GetMinGapByHeroSysId 获取英雄的最小觉醒道具缺口
func (hm *HeroM) GetMinGapByHeroSysId(heroSysId, awakenValue uint32, srv Servicer) uint32 {
	bagNum := uint32(hm.owner.GetResourceCount(uint32(common.RESOURCE_ITEM), awakenValue, srv)) // 背包中已拥有的数量

	// 获取英雄的最高觉醒等级
	var maxAwakenLevel uint32
	for _, hero := range hm.heroes {
		if hero.GetHeroSysID() != heroSysId {
			continue
		}
		awakenLevel := hero.GetAwakenLevel()
		if awakenLevel > maxAwakenLevel {
			maxAwakenLevel = awakenLevel
		}
	}

	// 根据最高觉醒等级 和 背包数量 计算当前缺口
	return hm.GetAwakeItemGap(maxAwakenLevel, bagNum)
}

// GetHeroAwakenPool 获取觉醒道具随机池
func (hm *HeroM) GetHeroAwakenPool(excludeHeroSysId uint32) []uint32 {
	// 获取所有可觉醒英雄信息
	awakenHeroInfos := goxml.GetData().HeroInfoM.GetAwakenHeroInfos()
	if len(awakenHeroInfos) == 0 {
		l4g.Errorf("user %d GetHeroAwakenGap: no awakenHeroes", hm.owner.ID())
		return nil
	}

	now := time.Now().Unix()
	pool := make([]uint32, 0, len(awakenHeroInfos)-1)
	for _, info := range awakenHeroInfos {
		if info.ShieldEndTime > now {
			l4g.Debugf("user %d GetHeroAwakenGap: heroSysId %d shielded, shieldTime %d now %d", hm.owner.ID(), info.HeroSysId, info.ShieldEndTime, now)
			continue
		}
		if info.HeroSysId == excludeHeroSysId {
			continue
		}
		pool = append(pool, info.AwakenValue)
	}

	return pool
}

// RandomAwakenPool 随机一个觉醒道具
func (hm *HeroM) RandomAwakenPool(rd *rand.Rand, heroAwakenPool []uint32) uint32 {
	if len(heroAwakenPool) == 0 {
		l4g.Errorf("user %d RandomAwakenPool: pool empty", hm.owner.ID())
		return 0
	}

	// 从池子中随机一个
	randomItemValue := heroAwakenPool[rd.Intn(len(heroAwakenPool))]
	if randomItemValue == 0 {
		l4g.Errorf("user %d RandomAwakenPool: rand failed", hm.owner.ID())
		return 0
	}

	return randomItemValue
}

// GetAwakenItemCompensation 获取觉醒道具补偿
func (hm *HeroM) GetAwakenItemCompensation(rd *rand.Rand, compensationNum, targetHeroSysId uint32) []*cl.Resource {
	// 觉醒道具池
	heroAwakenPool := hm.owner.HeroManager().GetHeroAwakenPool(targetHeroSysId)
	l4g.Debugf("user: %d GetAwakenItemCompensation: heroAwakenPool %v", hm.owner.ID(), heroAwakenPool)

	// 随机ax -b 个未觉醒满的英雄对应的觉醒道具
	var awards []*cl.Resource
	for i := uint32(0); i < compensationNum; i++ {
		randResult := hm.owner.HeroManager().RandomAwakenPool(rd, heroAwakenPool)
		l4g.Debugf("user: %d GetAwakenItemCompensation: randResult %d", hm.owner.ID(), randResult)
		if randResult == 0 {
			l4g.Errorf("user: %d GetAwakenItemCompensation: RandomAwakenPool error", hm.owner.ID())
			return nil
		}
		awards = append(awards, goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), randResult, 1))
	}

	return awards
}

func (hm *HeroM) RandomAwakenRes(rd *rand.Rand, convertRes map[uint32]uint32) (uint32, map[uint32]uint32, map[uint32]uint32) {
	heroCount := make(map[uint32]uint32)
	for _, heroData := range hm.heroes {
		if heroData == nil || heroData.data == nil {
			continue
		}
		if heroData.data.AwakenLevel == 0 {
			continue
		}

		count := goxml.GetData().HeroAwakenLevelInfoM.GetAwakenLevelCount(heroData.data.AwakenLevel)
		heroCount[heroData.GetHeroSysID()] += count
	}

	now := time.Now().Unix()
	pools := make([]*goxml.HeroAwakenItemCount, 0, len(goxml.GetData().HeroInfoM.GetAwakenHeroInfos()))
	bag := hm.owner.itemsBag()
	maxNum := goxml.GetData().HeroAwakenLevelInfoM.TotalNum()
	var totalRandomNum uint32
	for _, awakenHeroData := range goxml.GetData().HeroInfoM.GetAwakenHeroInfos() {
		if awakenHeroData == nil || now < awakenHeroData.ShieldEndTime {
			continue
		}

		totalCount := bag[awakenHeroData.AwakenValue] + heroCount[awakenHeroData.HeroSysId]

		count, exist := convertRes[awakenHeroData.AwakenValue]
		if exist {
			if totalCount < maxNum || totalCount < count || totalCount-count < maxNum {
				l4g.Errorf("user:%d buildRandomAwakenPool failed hero id:%d awaken item id:%d totalCount:%d client count:%d",
					hm.owner.ID(), awakenHeroData.HeroSysId, awakenHeroData.AwakenValue, totalCount, count)
				return uint32(cret.RET_HERO_CONVERT_AWAKEN_NOT_MORE_THAN_MAX), nil, nil
			}
			totalRandomNum += count
		} else {
			if maxNum > totalCount {
				pools = append(pools, &goxml.HeroAwakenItemCount{
					Info:      awakenHeroData,
					LeftCount: maxNum - totalCount,
				})
			}
		}
	}

	if len(pools) == 0 {
		return uint32(cret.RET_HERO_CONVERT_NO_RES_TO_CONVERT), nil, nil
	}

	var costPool []*goxml.CostPoolItem
	for itemID, count := range convertRes {
		costPool = append(costPool, &goxml.CostPoolItem{
			Id:    itemID,
			Count: count,
		})
	}

	ret := make(map[uint32]uint32)
	cost := make(map[uint32]uint32)
	var costPoolIndex int
	for i := 0; i < int(totalRandomNum); i++ {
		poolLen := len(pools)
		if poolLen == 0 {
			break
		}
		index := rd.RandBetween(0, poolLen-1)
		item := pools[index]
		if item != nil && item.LeftCount > 0 {
			item.LeftCount--
			ret[item.Info.AwakenValue]++
			costPoolIndex = costPool[costPoolIndex].Use(costPoolIndex, cost)
		}
		if item == nil || item.LeftCount == 0 {
			pools = slices.Delete(pools, index, index+1)
		}
	}
	return uint32(cret.RET_OK), cost, ret
}
