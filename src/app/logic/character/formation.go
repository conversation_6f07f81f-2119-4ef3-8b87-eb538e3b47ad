package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/out/bt"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"math"

	l4g "github.com/ivanabc/log4go"
)

func elemInSlice(elem uint32, s []uint32) bool {
	for _, e := range s {
		if elem == e {
			return true
		}
	}
	return false
}

func (u *User) NewTeamByAssistMonsters(assistMonsters []uint32, teamIndex int) *battle.Team {
	var pos uint32 = 0
	var totalPower uint64
	members := make([]*battle.Member, 0, battle.TeamMaxPos)
	for _, assistMonster := range assistMonsters {
		pos++
		if assistMonster == 0 { //0代表空位
			continue
		}
		member, monsterInfo := battle.NewMonster(assistMonster, pos, true)
		if member == nil {
			l4g.Errorf("%d NewTeam NewMonster Error, monsterId: %d", u.ID(), assistMonster)
			return nil
		}
		members = append(members, member)
		totalPower += monsterInfo.GetPower(goxml.GetData())
	}

	team := battle.NewTeam(members, nil, nil, &battle.TeamBase{
		IsCalculateLink: true,
		Power:           int64(totalPower),
		Name:            u.Name(),
		TeamIndex:       uint32(teamIndex),
	}, nil)
	return team
}

func (u *User) GetFormation(formationID uint32) *cl.Formation {
	return u.FormationManager().Get(formationID)
}

func (u *User) SendFormationToClient(formationIds []uint32) {
	smsg := &cl.L2C_GetFormation{
		Ret:          uint32(ret.RET_OK),
		FormationIds: formationIds,
		Formations:   u.FormationManager().Flush(formationIds),
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_GetFormation, smsg)
}

func (u *User) CheckHeroInFormation(hid uint64, formation uint32) bool {
	f := u.GetFormation(formation)
	if f == nil {
		return false
	}
	for _, t := range f.Teams {
		if t == nil {
			continue
		}
		for _, hero := range t.Info {
			if hero.Hid == hid {
				return true
			}
		}
	}

	return false
}

// 英雄是否可以被消耗
// @param uint64 hid 待检查英雄id
// @return bool
func (u *User) IsHeroCanConsume(hid uint64, fids []uint32) bool {
	for _, fid := range fids {
		if u.CheckHeroInFormation(hid, fid) {
			return false
		}
	}
	return true
}

func (u *User) CalFormationPower(formationID uint32, params ...uint32) int64 {
	formation := u.GetFormation(formationID)
	if formation == nil {
		l4g.Errorf("user: %d CalFormationPower: formation is nil. formationID: %d", u.ID(), formationID)
		return 0
	}
	var power int64
	for index, team := range formation.Teams {
		if team == nil {
			continue
		}

		teamPower := u.CalFormationTeamPower(formationID, team, params...)
		l4g.Debugf("user:%d CalFormationPower formation id:%d index:%d team power:%d", u.ID(), formationID, index, teamPower)

		power += teamPower
	}
	l4g.Debugf("user:%d. CalFormationPower. fid:%d, power:%d", u.ID(), formationID, power)
	if power <= 0 {
		power = 0
		l4g.Errorf("user:%d. CalFormationPower err. fid:%d, power:%d", u.ID(), formationID, power)
	}
	return power

}

func (u *User) CalFormationTeamPower(formationID uint32, team *cl.FormationTeamInfo, params ...uint32) int64 {
	var teamPower int64
	for _, f := range team.Info {
		if hero := u.HeroManager().Get(f.Hid); hero != nil {
			newHero := u.calHeroAttr(hero, formationID, params...)
			if newHero != nil {
				teamPower += int64(newHero.GetHeroPower(u))
			}
		}
	}
	return teamPower
}

func (u *User) setFormationPower(formationID uint32, power int64) {
	switch formationID {
	case uint32(common.FORMATION_ID_FI_ARENA_DEFENSE):
		u.SetArenaPower(power)
	default:
		u.SetDefensePower(formationID, power)
	}
}

func (u *User) ClearFormationAssistMonster(srv servicer, formationID uint32, assistMonster uint32) {
	if assistMonster == 0 {
		return
	}
	formation := u.GetFormation(formationID)
	if formation != nil {
		hasFind := false
		for _, team := range formation.Teams {
			if team == nil {
				continue
			}
			for k, v := range team.Info {
				if uint32(v.Hid) == assistMonster {
					team.Info = append(team.Info[:k], team.Info[k+1:]...)
					hasFind = true
					break
				}
			}
			if hasFind {
				break
			}
		}
		if hasFind {
			u.FormationManager().NewFormation(srv, formationID, formation)
			u.SendFormationToClient([]uint32{formationID})
		}
	}
}

func (u *User) IsTeamExist(formationID uint32, index int) bool {
	team := u.GetFormationTeam(formationID, index)
	if team == nil || len(team.Info) == 0 {
		return false
	}
	return true
}

func (u *User) GetFormationTeam(formationID uint32, index int) *cl.FormationTeamInfo {
	formation := u.GetFormation(formationID)
	if formation == nil {
		l4g.Errorf("user:%d GetFormationTeam: formation: %d is nil.", u.ID(), formationID)
		return nil
	}

	if len(formation.Teams) < index+1 {
		l4g.Errorf("user:%d GetFormationTeam: formation.Team count is less. teams:%d index:%d",
			u.ID(), len(formation.Teams), index)
		return nil
	}

	return formation.Teams[index]
}

// 注意：这个方法，赐福英雄的养成进度，会使用共享养成进度
// 只给通关战报使用的
func (u *User) FlushFormationHeroes(formationID uint32, params ...uint32) ([]*cl.HeroBody, []*cl.Skin, []*cl.Artifact, uint64, []*cl.CognitionHeroPassiveSkill) {
	formation := u.GetFormation(formationID)
	if formation == nil {
		l4g.Errorf("user:%d GetFormationTeam: formation is nil.", u.ID())
		return nil, nil, nil, uint64(0), nil
	}

	heroes := make([]*cl.HeroBody, 0, len(formation.Teams)*int(FormationMaxPos))
	heroPassiveSkills := make([]*cl.CognitionHeroPassiveSkill, 0, len(formation.Teams)*int(FormationMaxPos))
	power := uint64(0)
	// 皮肤
	skins := make([]*cl.Skin, 0, 1)
	// 符文
	// emblems := make([]*cl.EmblemInfo, 0, len(heroes))

	artifacts := make([]*cl.Artifact, 0, len(formation.Teams)*int(FormationArtifMaxPos))

	heroM := u.HeroManager()
	artifactM := u.ArtifactManager()
	for _, team := range formation.Teams {
		for _, info := range team.Info {
			hero := heroM.Get(info.Hid)
			if hero == nil {
				continue
			}
			data := hero.GetData()
			if data == nil {
				continue
			}
			newHero := u.calHeroAttr(hero, formationID, params...)
			if newHero == nil {
				continue
			}
			newHeroData := newHero.GetData()
			skin := u.SkinManager().GetSkinByHeroIDByBattle(newHeroData.SysId)
			if skin != nil {
				skins = append(skins, skin.Flush())
			}
			/*
				heroEmblems := newHero.GetDisplayEmblems(u)
				for _, emblem := range heroEmblems {
					if emblem == nil {
						continue
					}
					emblems = append(emblems, emblem.Data.Clone())
				}
			*/
			heroes = append(heroes, newHeroData.Clone())
			power += newHero.GetHeroPower(u)
			if _, exclusiveLv := hero.calcHeroEmblemSkill(u); exclusiveLv > 0 {
				heroPassiveSkills = append(heroPassiveSkills, &cl.CognitionHeroPassiveSkill{
					SysId:             newHeroData.SysId,
					EmblemExclusiveLv: exclusiveLv,
				})
			}
		}

		for _, a := range team.Artifacts {
			artifact := artifactM.GetArtifact(a.Aid)
			if artifact == nil {
				continue
			}
			data := artifact.GetData()
			if data == nil {
				continue
			}
			newArtifact := u.calArtifactAttr(artifact, formationID, params...)
			if newArtifact == nil {
				continue
			}
			artifacts = append(artifacts, newArtifact.data.Clone())
		}

	}

	return heroes, skins, artifacts, power, heroPassiveSkills
}

func (u *User) NewUserTeam(formationID uint32, battleParam *battle.ManagerParams, isAttack bool, extraID ...uint32) (*battle.Team, map[uint32][]uint64) {
	teamIndex := battleParam.GetAttackTeamIndex()
	pokemons := battleParam.AttackPokemons
	if !isAttack {
		teamIndex = battleParam.GetDefenseTeamIndex()
		pokemons = battleParam.DefensePokemons
	}
	formationTeam := u.GetFormationTeam(formationID, teamIndex)
	if formationTeam == nil || len(formationTeam.Info) == 0 {
		l4g.Errorf("%d NewTeam error %d:%d", u.ID(), formationID, teamIndex)
		return nil, nil
	}
	members, power := u.GetBattleMembers(formationTeam, formationID, extraID...)
	if len(members) == 0 {
		return nil, nil
	}

	artifacts, raisePSsMap := u.GetBattleArtifacts(formationTeam, formationID, extraID...)
	teamBase := u.GetBattleTeamBase(formationID, uint32(teamIndex), int64(power))
	battlePokemons := u.GetBattlePokemons(pokemons)
	team := battle.NewTeam(members, artifacts, nil, teamBase, battlePokemons)
	return team, raisePSsMap
}

func (u *User) GetBattlePokemons(pokemons []*battle.PokemonBattle) []*battle.Pokemon {
	battlePokemons := make([]*battle.Pokemon, 0)
	for _, pokemon := range pokemons {
		battlePokemon := battle.NewPokemon(pokemon.Pos, pokemon.SysId, pokemon.Star)
		for _, v := range pokemon.PassiveSkills {
			battlePokemon.AddNewSysPassiveSkill(v)
		}
		battlePokemons = append(battlePokemons, battlePokemon)
	}
	return battlePokemons
}

func (u *User) GetBattleMembers(formationTeam *cl.FormationTeamInfo, formationID uint32, extraID ...uint32) ([]*battle.Member, uint64) {
	members := make([]*battle.Member, 0, battle.TeamMaxPos)
	var power uint64 = 0
	for _, v := range formationTeam.Info {
		hid := v.Hid
		uid := v.Uid
		pos := v.Pos
		omniLinks := v.Links
		if hid > math.MaxUint32 {
			if hero := u.HeroManager().Get(hid); hero != nil {
				member := u.NewBattleMemberFromHero(hero, formationID, pos, omniLinks, extraID...)
				members = append(members, member)
				power += member.Base.Power
			}
		} else {
			if len(extraID) == 0 || !elemInSlice(uint32(hid), extraID) {
				l4g.Errorf("%d NewTeam error %d no find hid:%d %d %d", u.ID(), formationID, pos, hid, uid)
				return nil, 0
			}
			member, monsterInfo := battle.NewMonster(uint32(hid), pos, true)
			if member == nil {
				l4g.Errorf("%d NewTeam NewMonster Error, %d, hid:%d, %d, %d", u.ID(), formationID, pos, hid, uid)
				return nil, 0
			}
			members = append(members, member)
			power += monsterInfo.GetPower(goxml.GetData())
		}
	}
	return members, power
}

func (u *User) GetBattleArtifacts(formationTeam *cl.FormationTeamInfo, formationID uint32,
	extraID ...uint32) ([]*bt.ArtifactInfo, map[uint32][]uint64) {
	artifacts := make([]*bt.ArtifactInfo, 0, len(formationTeam.Artifacts))
	pos := uint32(0)
	psSkills := make(map[uint32][]uint64)
	for _, v := range formationTeam.Artifacts {
		artifact := u.ArtifactManager().GetArtifact(v.Aid)
		if artifact == nil || artifact.GetData() == nil {
			continue
		}
		info := goxml.GetData().ArtifactInfoM.Index(artifact.data.SysId)
		if info == nil {
			l4g.Errorf("artifact.calcAttr() artifactInfo not exist sysId:%d", artifact.data.SysId)
			continue
		}
		pos++
		ar := u.NewBattleArtifactInfoFromArtifact(artifact, formationID, pos, extraID...)
		if ar == nil {
			l4g.Errorf("artifact.calcAttr() NewBattleArtifactInfoFromArtifact :%d, %+v", artifact.data.SysId, extraID)
			continue
		}
		artifacts = append(artifacts, ar)
		skillInfo := goxml.GetData().ArtifactSkillInfoM.GroupLevel(info.SkillGroup1, ar.Star)
		if skillInfo != nil {
			psSkills[battle.TeamUniterBattlePos] = append(psSkills[battle.TeamUniterBattlePos], skillInfo.RaisePSs...)
		}
	}
	return artifacts, psSkills
}

func (u *User) GetBattleRiteRaisePSsAndAttrsMap(formationTeam *cl.FormationTeamInfo, riteRestrictState uint32) (map[uint32][]uint64, map[uint32]map[int]int64) {
	if formationTeam == nil {
		return nil, nil
	}
	if formationTeam.RiteInfo == nil || formationTeam.RiteInfo.RiteId == 0 {
		return nil, nil
	}
	riteId := formationTeam.RiteInfo.RiteId
	rite := u.RiteManager().GetRite(riteId)
	if rite == nil {
		l4g.Errorf("user:%d GetRite: rite is nil. riteId:%d", u.ID(), riteId)
		return nil, nil
	}
	raisePSsMap := battle.GetRiteRaisePSsMap(riteId, rite.GetRare(), riteRestrictState)
	ritePowerRaisePSsMap := battle.GetRitePowerRaisePSsMap(formationTeam.GetRiteInfo().RitePowers)
	for pos, raisePSs := range ritePowerRaisePSsMap {
		raisePSsMap[pos] = append(raisePSsMap[pos], raisePSs...)
	}
	attrsMap := battle.GetRiteAttrsMap(rite.GetEquippedMarkIds())
	return raisePSsMap, attrsMap
}

// 生成品质受限的符文
// @param limitRare uint32 品质上限值
// @param info *goxml.EmblemInfosExt 源符文配置
// @param source *Emblem 源符文
// @return *Emblem
func makeRareLimitEmblem(limitRare uint32, info *goxml.EmblemInfosExt, source *Emblem) *Emblem {
	ret := source.Clone()
	if info.Rare <= limitRare {
		return ret
	}

	targetInfo := goxml.GetData().EmblemInfoM.GetRareLimitEmblemInfo(limitRare, info.Race, info.Type, info.Pos)
	if targetInfo == nil {
		l4g.Errorf("makeRareLimitEmblem error, no targetInfo, limitRare:%d, rare:%d, type:%d, pos:%d",
			limitRare, info.Race, info.Type, info.Pos)
		return nil
	}

	ret.Data.SysId = targetInfo.Id
	if ret.Data.Level > targetInfo.LevelMax {
		ret.Data.Level = targetInfo.LevelMax
	}
	if targetInfo.IsHeroGroup == 0 {
		ret.Data.AdditiveHero = 0
		ret.Data.SkillId = 0
		ret.Data.Succinct = nil
	}
	return ret
}

func (u *User) getSeasonLevelPower() int64 {
	levelInfo := goxml.GetData().SeasonLevelInfoM.GetLevelInfo(u.GetSeasonID(), u.GetSeasonLevel())

	if levelInfo == nil {
		l4g.Errorf("user: %d levelInfo not exist. seasonID:%d level:%d",
			u.ID(), u.GetSeasonID(), u.GetSeasonLevel())
		return 0
	}

	return levelInfo.Power
}

func (u *User) getFormationRiteIds(fid uint32) []uint32 {
	formation := u.GetFormation(fid)
	if formation == nil {
		l4g.Errorf("user:%d GetFormationRiteIds: formation is nil. fid=%d", u.ID(), fid)
		return nil
	}
	ids := make([]uint32, 0, 3) //nolint:mnd
	for _, team := range formation.Teams {
		if team.RiteInfo != nil {
			ids = append(ids, team.RiteInfo.RiteId)
		}
	}
	return ids
}

func (u *User) getFormationRemainIds(fid uint32) []uint32 {
	formation := u.GetFormation(fid)
	if formation == nil {
		l4g.Errorf("user:%d GetFormationRiteIds: formation is nil. fid=%d", u.ID(), fid)
		return nil
	}
	ids := make([]uint32, 0, len(formation.Teams))
	for _, team := range formation.Teams {
		for _, info := range team.RemainInfo {
			ids = append(ids, info.Id)
		}
	}
	return ids
}

func (u *User) IsFormationWithRite(fid uint32) bool {
	formation := u.GetFormation(fid)
	if formation == nil {
		l4g.Errorf("user:%d GetFormationRiteIds: formation is nil. fid=%d", u.ID(), fid)
		return false
	}
	for _, team := range formation.Teams {
		if team.RiteInfo != nil && team.RiteInfo.RiteId > 0 {
			return true
		}
	}
	return false
}

func (u *User) Convert2GSTTeam() []*cl.GSTSetFormationTeamInfo {
	formation := u.FormationManager().Get(uint32(common.FORMATION_ID_FI_GST))
	if formation == nil {
		return nil
	}

	ret := make([]*cl.GSTSetFormationTeamInfo, 0, len(formation.Teams))
	for index, team := range formation.Teams {
		gstTeam := &cl.GSTSetFormationTeamInfo{
			Heros:     make([]*cl.GSTTeamHeroInfo, 0, len(team.Info)),
			TeamIndex: uint32(index),
		}
		gstTeam.Power = u.CalFormationTeamPower(uint32(common.FORMATION_ID_FI_GST), team)
		for _, hero := range team.Info {
			heroData := u.HeroManager().Get(hero.Hid)
			if heroData == nil {
				return nil
			}
			gstHero := &cl.GSTTeamHeroInfo{
				Pos:   hero.Pos,
				Star:  heroData.GetStar(),
				SysId: heroData.GetHeroSysID(),
			}
			gstTeam.Heros = append(gstTeam.Heros, gstHero)
		}
		ret = append(ret, gstTeam)
	}
	return ret
}

func (u *User) GstCheckBuildDispatch(heroesSysID []uint32) bool {
	formation := u.GetFormation(uint32(common.FORMATION_ID_FI_GST))
	if formation == nil {
		return true
	}

	for _, team := range formation.Teams {
		if team == nil {
			continue
		}
		for _, hero := range team.Info {
			if hero == nil {
				continue
			}
			heroData := u.HeroManager().Get(hero.Hid)
			if heroData == nil {
				continue
			}
			for _, sysID := range heroesSysID {
				if heroData.GetHeroSysID() == sysID {
					return false
				}
			}
		}
	}
	return true
}
