package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"time"
)

type NewYearActivity struct {
	owner *User
	data  *cl.NewYearActivity
}

func newNewYearActivity(user *User) *NewYearActivity {
	return &NewYearActivity{
		owner: user,
	}
}

func (n *NewYearActivity) Owner() *User {
	return n.owner
}

func (n *NewYearActivity) load(data *cl.NewYearActivity) {
	n.data = data
	if n.data == nil {
		n.data = &cl.NewYearActivity{}
	}
	if n.data.LoginAward == nil {
		n.data.LoginAward = make(map[uint32]bool)
	}
}

func (n *NewYearActivity) Save() {
	n.Owner().dbUser.Module5.NewYearActivity = n.data
	n.Owner().setSaveTag(saveTagModule5)
}

func (n *NewYearActivity) Flush() *cl.NewYearActivity {
	return n.data.Clone()
}

func (n *NewYearActivity) IsOpen(srv servicer, now int64) (*goxml.NewYearActivityInfoExt, bool) {
	sysId := n.data.GetSysId()
	openInfo := goxml.GetData().NewYearInfoM.Index(sysId)
	if openInfo == nil {
		return nil, false
	}

	if !openInfo.IsOpen(now) {
		return nil, false
	}

	funcInfo := goxml.GetData().FunctionInfoM.Index(openInfo.FunctionID)
	if funcInfo == nil {
		return nil, false
	}

	if !n.owner.isFunctionOpen(srv, funcInfo) {
		return nil, false
	}
	return openInfo, true
}

func (n *NewYearActivity) NeedReset(srv servicer) (*goxml.NewYearActivityInfoExt, bool) {
	now := time.Now().Unix()
	newYearActivityInfo := goxml.GetData().NewYearInfoM.GetCurrent(now)
	if newYearActivityInfo == nil {
		//没有活动数据且之前没参加过活动
		if n.data.SysId == 0 {
			return newYearActivityInfo, false
		} else { //没有活动数据之前参加过活动
			return newYearActivityInfo, true
		}
	}

	if srv.ServerOpenDay(newYearActivityInfo.EndTime) <= goxml.GetData().ConfigInfoM.GetNewYearActivitySheildDays() {
		return nil, false
	}

	funcInfo := goxml.GetData().FunctionInfoM.Index(newYearActivityInfo.FunctionID)
	if funcInfo == nil {
		return nil, false
	}

	if !n.owner.isFunctionOpen(srv, funcInfo) {
		return nil, false
	}

	return newYearActivityInfo, newYearActivityInfo.ID != n.data.SysId
}

func (n *NewYearActivity) Reset(srv servicer, info *goxml.NewYearActivityInfoExt) {
	if info != nil {
		data := &cl.NewYearActivity{
			SysId:            info.ID,
			ContinueLoginDay: 1,
			LoginAward:       make(map[uint32]bool),
		}
		n.data = data
	} else {
		data := &cl.NewYearActivity{
			LoginAward: make(map[uint32]bool),
		}
		n.data = data
	}
	n.Save()
}

func (n *NewYearActivity) ResetDaily(srv servicer) {
	var change bool
	info, reset := n.NeedReset(srv)
	if reset {
		n.Reset(srv, info)
		change = true
	}

	now := time.Now().Unix()
	_, isOpen := n.IsOpen(srv, now)
	if isOpen && !reset {
		n.data.ContinueLoginDay++
		change = true
	}
	if change {
		n.Save()
	}
}

func (n *NewYearActivity) GetSysID() uint32 {
	return n.data.SysId
}

func (a *NewYearActivity) CheckLoginReward(id uint32) bool {
	award, exist := a.data.LoginAward[id]
	if !exist {
		return true
	}
	return !award
}

func (a *NewYearActivity) CanReceive(day uint32) bool {
	return a.data.ContinueLoginDay >= day
}

func (a *NewYearActivity) SetLoginReward(taskIds []uint32) {
	for _, v := range taskIds {
		a.data.LoginAward[v] = true
	}
	a.Save()
}
