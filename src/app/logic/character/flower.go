package character

import (
	"app/goxml"
	"app/protos/in/p2l"
	"app/protos/out/cl"
	"strconv"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type Flower struct {
	owner *User
	data  *cl.FlowerOccupyNum
}

func newFlower(user *User) *Flower {
	return &Flower{
		owner: user,
	}
}

func (f *Flower) Owner() *User {
	return f.owner
}

func (f *Flower) load(data *cl.FlowerOccupyNum) {
	f.data = data
	if data == nil {
		f.data = &cl.FlowerOccupyNum{
			LeftNum: goxml.GetData().FlowerConfigInfoM.GetOccupyMaxFightCount(),
		}
	}
}

func (f *Flower) Init() {
	f.data = &cl.FlowerOccupyNum{
		LeftNum: goxml.GetData().FlowerConfigInfoM.GetOccupyMaxFightCount(),
	}
	f.save()
}

func (f *Flower) save() {
	if f.owner.dbUser.Module3.FlowerOccupyNum == nil {
		f.owner.dbUser.Module3.FlowerOccupyNum = f.data
	}
	f.owner.setSaveTag(saveTagModule3)
}

func (f *Flower) Clone() *cl.FlowerOccupyNum {
	return f.data.Clone()
}

func (f *Flower) getBuyNum() uint32 {
	return f.data.BuyNum
}

func (f *Flower) getBuyTime() int64 {
	return f.data.BuyTime
}

func (f *Flower) GetLeftNum() uint32 {
	f.Recover()
	return f.data.LeftNum
}

// 计算今天已购买次数
func (f *Flower) CalcTodayBoughtNum() uint32 {
	if f.getBuyTime() == 0 {
		return 0
	}

	if util.DailyZeroByTime(f.getBuyTime()) == util.DailyZeroByTime(time.Now().Unix()) {
		return f.getBuyNum()
	}
	return 0
}

// 获取可增加的次数
func (f *Flower) GetGrowSpace() uint32 {
	maxNum := goxml.GetData().FlowerConfigInfoM.GetOccupyMaxFightCount()
	if f.data.LeftNum >= maxNum {
		return 0
	}
	return maxNum - f.data.LeftNum
}

func (f *Flower) UseNum() {
	if f.data.LeftNum == 0 {
		l4g.Errorf("user: %d, flower.Use: leftNum=0", f.Owner().ID())
		return
	}
	isFullBefore := f.GetGrowSpace() == 0

	f.data.LeftNum--
	if isFullBefore {
		f.data.RecoverTm = time.Now().Unix()
	}
	f.save()
}

func (f *Flower) BuyNum(num, boughtNum uint32) bool {
	growSpace := f.GetGrowSpace()
	if growSpace == 0 {
		l4g.Errorf("user: %d, flower.Buy: no growSpace", f.Owner().ID())
		return false
	}

	if num > growSpace {
		l4g.Errorf("user: %d, flower.Use: num:%d > growSpace:%d", f.Owner().ID(),
			num, growSpace)
		return false
	}

	f.data.LeftNum += num
	f.data.BuyNum = boughtNum + num
	f.data.BuyTime = time.Now().Unix()
	f.save()
	return true
}

// 检查回复进攻次数
func (f *Flower) Recover() {
	growSpace := f.GetGrowSpace()
	if growSpace == 0 {
		return
	}

	now := time.Now().Unix()
	if f.data.RecoverTm >= now {
		l4g.Debugf("user: %d, flower.Recover: RecoverTm:%d, now:%d", f.Owner().ID(),
			f.data.RecoverTm, now)
		return
	}
	passTm := now - f.data.RecoverTm

	//检查时间差是否已达恢复时间间隔
	addNum := uint32(passTm / goxml.GetData().FlowerConfigInfoM.GetOccupyFightResumeCD())
	if addNum <= 0 {
		return
	}

	if addNum >= growSpace {
		addNum = growSpace
	}
	l4g.Debugf("user: %d, flower.Recover: addNum:%d, passTm:%d", f.Owner().ID(),
		addNum, passTm)

	f.data.LeftNum += addNum
	f.data.RecoverTm += int64(addNum) * goxml.GetData().FlowerConfigInfoM.GetOccupyFightResumeCD()
	f.save()
}

func FlowerPushMsg(srv Servicer, lang, uuid string, uid uint64) {
	if lang == "" {
		lang = "en"
	}
	mInfo := goxml.GetData().FlowerMessageInfoM.Index(lang)
	if mInfo == nil {
		l4g.Errorf("flowerMessageInfo is nil. lang: %s", lang)
		return
	}

	opUuid, err := strconv.ParseUint(uuid, 10, 64)
	if err != nil {
		l4g.Errorf("uuid trans uint64 failed. err: %s", err.Error())
		return
	}

	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_FlowerPushMsg), uid, &p2l.L2P_FlowerPushMsg{
		MsgId:   srv.CreateUserLogID(),
		Title:   mInfo.Title,
		Context: mInfo.Txt,
		Uuid:    opUuid,
	})
}
