package character

import (
	"app/goxml"
	"app/protos/out/cl"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type ActivityWeb struct {
	owner *User
	data  *cl.ActivityWebDatas
}

func newActivityDatas(user *User) *ActivityWeb {
	return &ActivityWeb{
		owner: user,
	}
}

func (a *ActivityWeb) load(data *cl.ActivityWebDatas) {
	a.data = data
	if a.data == nil {
		a.data = &cl.ActivityWebDatas{}
	}
	if a.data.Datas == nil {
		a.data.Datas = make(map[uint32]*cl.ActivityWebData)
	}
}

func (a *ActivityWeb) save() {
	a.owner.dbUser.Module7.ActivityWebDatas = a.data
	a.owner.setSaveTag(saveTagModule7)
}

func (a *ActivityWeb) initNewData(groupID, rechargeCount uint32) *cl.ActivityWebData {
	webData := &cl.ActivityWebData{
		GroupId:       groupID,
		RechargeCount: rechargeCount,
	}
	a.data.Datas[groupID] = webData
	a.save()
	return webData
}

func (a *ActivityWeb) Recharge(rechargeCount uint32, srv servicer) {
	if srv.ServerType() != goxml.SERVER_TYPE_CN {
		return
	}
	now := time.Now().Unix()
	groups := goxml.GetData().ActivityWebInfoM.GetGroupOpenEndTime(now)
	if len(groups) == 0 {
		l4g.Debugf("ActivityWeb no groups open ")
		return
	}

	for _, groupID := range groups {
		webData, exist := a.data.Datas[groupID]
		if !exist {
			webData = a.initNewData(groupID, rechargeCount)
		} else {
			webData.RechargeCount += rechargeCount
			a.save()
		}

		actInfos := goxml.GetData().ActivityWebInfoM.GetAwards(groupID, webData.RechargeCount, webData.AwardIds)
		l4g.Infof("ActivityWeb gourpID:%d actInfos:%+v", groupID, actInfos)
		if len(actInfos) > 0 {
			awards := make([]*cl.Resource, 0, len(actInfos))
			recvIDs := make([]uint32, 0, len(actInfos))
			for _, actInfo := range actInfos {
				awards = append(awards, actInfo.Awards...)
				recvIDs = append(recvIDs, actInfo.Id)
			}
			webData.AwardIds = append(webData.AwardIds, recvIDs...)
			a.save()
			l4g.Debugf("user:%d ActivityWebMail send awards:%+v", a.owner.ID(), awards)
			ActivityWebMail(srv, a.owner, awards, MailIDActivityWeb)
		}
	}
}
