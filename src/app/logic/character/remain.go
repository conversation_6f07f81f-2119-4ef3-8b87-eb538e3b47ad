package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type Remain struct {
	data *cl.Remain
}

func newRemain(u *User, sysID uint32) *Remain {
	data := &cl.Remain{
		Id:   sysID,
		Star: goxml.GetData().RemainConfigInfoM.InitialStarValue,
	}
	r := &Remain{}
	r.init(data)
	r.UpdateRemain(u)

	return r
}

func (r *Remain) init(data *cl.Remain) {
	r.data = data
}

func (r *Remain) GetData() *cl.Remain {
	return r.data
}

func (r *Remain) Flush() *cl.Remain {
	return r.data.Clone()
}

func (r *Remain) RecvStar(u *User, starRecv uint32) (uint32, uint32) {
	if starRecv > r.data.Star || starRecv <= r.data.StarRecv {
		return uint32(ret.RET_CLIENT_REQUEST_ERROR), 0
	}
	remainInfo := goxml.GetData().RemainInfoM.Index(r.data.Id)
	if remainInfo == nil {
		l4g.Errorf("u:%d StarRecv remain id:%d info is nil", u.ID(), r.data.Id)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), 0
	}

	rare := remainInfo.Rare
	var totalExp uint32
	for i := r.data.StarRecv + 1; i <= starRecv; i++ {
		remainStarInfo := goxml.GetData().RemainStarInfoM.Index(rare, i)
		if remainStarInfo == nil {
			l4g.Errorf("u:%d StarRecv get rare:%d starRecv:%d remain star info is nil", u.ID(), rare, i)
			return uint32(ret.RET_SYSTEM_DATA_ERROR), 0
		}
		totalExp += remainStarInfo.Exp
	}
	r.data.StarRecv = starRecv
	return uint32(ret.RET_OK), totalExp
}

func (r *Remain) Star() uint32 {
	return r.data.Star
}

func (r *Remain) UpdateRemain(u *User) {
	msg := &cl.L2C_RemainUpdate{
		Ret: uint32(ret.RET_OK),
		Remains: []*cl.Remain{
			r.data.Clone(),
		},
	}

	u.SendCmdToGateway(cl.ID_MSG_L2C_RemainUpdate, msg)
}

func (r *Remain) SetStar(star uint32, user *User) {
	r.data.Star = star
	r.UpdateRemain(user)
}
