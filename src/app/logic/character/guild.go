package character

import (
	"app/goxml"
)

type UserGuild struct {
	owner     *User
	guildID   uint64
	guildName string
}

func (m *UserGuild) Owner() *User {
	return m.owner
}

func newUserGuild(user *User) *UserGuild {
	return &UserGuild{
		owner: user,
	}
}

/*
func (m *UserGuild) Get() *cl.UserGuildInfo {
	return m.data
}*/

func (m *UserGuild) GuildID() uint64 {
	return m.guildID
}

func (m *UserGuild) SetGuildID(id uint64) {
	m.guildID = id
}

func (m *UserGuild) SetGuildName(name string) {
	m.guildName = name
}

func (m *UserGuild) GuildName() string {
	return m.guildName
}

func (m *UserGuild) Update(guildID uint64, guildName string) {
	m.guildID = guildID
	m.guildName = guildName
}

type GoodsGuildLevelLimit struct {
	guildLevel uint32
}

func (g *GoodsGuildLevelLimit) IsShopGoodOpen(info *goxml.ShopRegularGoodsInfoExt, _ Servicer) bool {
	guildLevel := g.guildLevel
	if info.MinExt > 0 {
		if guildLevel < info.MinExt || guildLevel > info.MaxExt {
			return false
		}
	}

	return true
}

func (m *UserGuild) IsInGuild() bool {
	return m.guildID != 0
}
