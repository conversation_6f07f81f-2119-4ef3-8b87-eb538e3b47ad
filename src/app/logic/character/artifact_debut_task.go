package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

func newArtifactDebutTask() *cl.ArtifactDebutTask {
	return &cl.ArtifactDebutTask{
		Progress: make(map[uint32]*cl.TaskTypeProgress),
		Received: make(map[uint32]bool),
	}
}

func (a *ArtifactDebutManager) getAchieveTask() *cl.ArtifactDebutTask {
	if a.data.Task == nil {
		a.data.Task = newArtifactDebutTask()
	}
	if len(a.data.Task.Progress) == 0 {
		a.data.Task.Progress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if len(a.data.Task.Received) == 0 {
		a.data.Task.Received = make(map[uint32]bool)
	}
	return a.data.Task
}

func (a *ArtifactDebutManager) getDailyTask() *cl.ArtifactDebutTask {
	if a.data.DailyTask == nil {
		a.data.DailyTask = newArtifactDebutTask()
	}
	if len(a.data.DailyTask.Progress) == 0 {
		a.data.DailyTask.Progress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if len(a.data.DailyTask.Received) == 0 {
		a.data.DailyTask.Received = make(map[uint32]bool)
	}
	return a.data.DailyTask
}

func (a *ArtifactDebutManager) getAllTask(taskType uint32) *cl.ArtifactDebutTask {
	var task *cl.ArtifactDebutTask
	if taskType == goxml.ArtifactDebutTaskTypeAchieve {
		task = a.getAchieveTask()
	} else {
		task = a.getDailyTask()
	}
	return task
}

func (a *ArtifactDebutManager) getAllTaskProgress(taskType uint32) map[uint32]*cl.TaskTypeProgress {
	task := a.getAllTask(taskType)
	return task.Progress
}

func (a *ArtifactDebutManager) CanReceiveAward(info *goxml.ArtifactDebutTaskInfo, printErr bool) bool {
	task := a.getAllTask(info.ResetType)
	//是否已领过奖
	if _, exist := task.Received[info.Id]; exist {
		if printErr {
			l4g.Errorf("user: %d ArtifactDebutM.CanReceiveAward: award has received, actID:%d, id:%d",
				a.owner.ID(), info.ActivityId, info.Id)
		}
		return false
	}

	//info.TypeId为0是登录任务，不需验证
	if info.TypeId > 0 {
		progress := task.Progress[info.TypeId]
		if progress == nil {
			if printErr {
				l4g.Errorf("user: %d ArtifactDebutM.CanReceiveAward: no progress, actID:%d, id:%d",
					a.owner.ID(), info.ActivityId, info.Id)
			}
			return false
		}

		if !a.owner.CheckTaskFinish(progress, info.TypeId, uint64(info.Value)) {
			if printErr {
				l4g.Errorf("user:%d ArtifactDebutM.CanReceiveAward: task not finish, actID:%d, id:%d",
					a.owner.ID(), info.ActivityId, info.Id)
			}
			return false
		}
	}
	return true
}

func (a *ArtifactDebutManager) ReceiveTasksAward(infos []*goxml.ArtifactDebutTaskInfo) {
	for _, info := range infos {
		task := a.getAllTask(info.ResetType)
		task.Received[info.Id] = true
	}
}

func (a *ArtifactDebutManager) OnEvent(event, taskType uint32, progress uint64, values []uint32, srv servicer) {
	if !a.IsInActiveTime(srv) {
		return
	}
	a.handleTask(event, taskType, progress, values)
}

func (a *ArtifactDebutManager) handleTask(event, taskType uint32, progress uint64, values []uint32) {
	actID := a.GetActID()
	if actID == 0 {
		l4g.Errorf("user: %d ArtifactDebutManager.handlerTask: actID=0, event:%d, progress:%d",
			a.owner.ID(), event, progress)
		return
	}

	if !goxml.GetData().ArtifactDebutTaskInfoM.IsEventExist(actID, event) {
		l4g.Errorf("user: %d ArtifactDebutManager.handlerTask: event not exist, event:%d",
			a.owner.ID(), event)
		return
	}

	progressMap, change := a.owner.TaskTypeOnEvent(a.getAllTaskProgress(taskType), event, progress, values)
	if change {
		a.sendTaskProgress(progressMap)
		a.Save()
	}
}

func (a *ArtifactDebutManager) sendTaskProgress(progressMap map[uint32]*cl.TaskTypeProgress) {
	uniqID := a.GetID()
	if uniqID <= 0 {
		l4g.Errorf("user: %d ArtifactDebutManager.sendTaskProgress: uniqID=0", a.owner.ID())
		return
	}

	a.owner.SendCmdToGateway(cl.ID_MSG_L2C_ArtifactDebutUpdateTask, &cl.L2C_ArtifactDebutUpdateTask{
		Ret:      uint32(ret.RET_OK),
		UniqId:   uniqID,
		Progress: progressMap,
	})
}

func (a *ArtifactDebutManager) ResetDailyTask(srv servicer) {
	if !a.IsInAwardTime(srv) {
		return
	}
	a.data.DailyTask = newArtifactDebutTask()
	a.Save()
}

// 统计任务未领奖励
func (a *ArtifactDebutManager) calcLeftTaskAward(actID uint32) []*cl.Resource {
	awards := make([]*cl.Resource, 0, 1)
	infos := goxml.GetData().ArtifactDebutTaskInfoM.GetDatasByActiviyID(actID)
	for _, info := range infos {
		//是否可领奖
		if !a.CanReceiveAward(info, false) {
			continue
		}

		//过滤拼图碎片
		for _, award := range info.ClRes {
			if goxml.NeedFilterArtifactDebutMailAward(award) {
				continue
			}
			awards = append(awards, award)
		}
	}
	return awards
}
