package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"math"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type Hero struct {
	data      *cl.HeroBody
	totalAttr []int64                  // 英雄属性（包括养成、全局等所有属性）
	attrTag   bool                     // 重新计算属性的标识；false：重新计算，true：不重新计算，直接用totalAttr里缓存的属性
	power     uint64                   // 英雄战力
	raisePSs  map[RaisePSType][]uint64 //养成的额外被动技能。英雄突破,纹章,装备
	links     map[uint32]uint32        //拥有的联结 linkID=> num
	logTag    uint32                   // 位标记（用来记录英雄身上宝石和符文的羁绊和技能激活状态）日志记录用
	modify    *HeroModify              //英雄的修正属性
}

// 处理一些需要修正的情况
type HeroModify struct {
	emblems []*Emblem //修正的符文数据 正常英雄没有这个数据，如果有优先从这里取符文数据 slot=>符文数据
	level   uint32    //共享养成修复的等级
	stage   uint32    //共享养成修复的stage
}

type RaisePSType int

const (
	RaisePSTypeHeroStar RaisePSType = iota //英雄星级联结技能
	RaisePSTypeHeroStage
	RaisePSTypeEquipEnchant      //装备附魔等级技能(都是属性!!)
	RaisePSTypeHeroTalent        //英雄天赋(宝石)英雄技能
	RaisePSTypeHeroTalentLink    //英雄天赋联结技能
	RaisePSTypeEmblemSelfAndSuit //纹章本身词条和套装技能
	RaisePSTypeEmblemHero        //纹章英雄技能
	RaisePSTypeEmblemLink        //纹章联结技能
	RaisePSTypeHeroAwaken        //三系英雄觉醒
	RaisePSTypeSkin              //皮肤属性
	RaisePSTypeEmblemAffix       //纹章词缀技能
	RaisePSTypeSeasonLink        //赛季羁绊属性
	RaisePSTypeHeroRareUp        // 英雄升品
)

func (h *Hero) AddRaisePSs(raiseType RaisePSType, ids []uint64) {
	h.raisePSs[raiseType] = append(h.raisePSs[raiseType], ids...)
}

func newHero(id uint64, sysID uint32) *Hero {
	heroInfo := goxml.GetData().HeroInfoM.Index(sysID)
	if heroInfo != nil {
		data := &cl.HeroBody{
			Id:        id,
			SysId:     sysID,
			Star:      heroInfo.Star,
			Level:     goxml.GetData().HeroLevelInfoM.GetDefaultLv(),
			Stage:     goxml.GetData().HeroStageInfoM.GetDefaultStage(),
			Equipment: make(map[uint32]uint64),
			Gem:       make(map[uint32]uint64),
			Emblem:    make(map[uint32]uint64),
		}
		return initHeroFromData(data)
	}
	return nil
}

func initHeroFromData(data *cl.HeroBody) *Hero {
	hero := &Hero{}
	hero.data = data
	return hero
}

func NewHeroInit() *Hero {
	return &Hero{
		totalAttr: make([]int64, goxml.AttrMaxNum),
		raisePSs:  make(map[RaisePSType][]uint64),
		links:     make(map[uint32]uint32),
	}
}

func (h *Hero) GetData() *cl.HeroBody {
	return h.data
}

// 获取可以比较的数据
func (h *Hero) GetReorderData() uint64 {
	return h.power
}

func (h *Hero) GetHid() uint64 {
	return h.data.Id
}

func (h *Hero) GetHeroSysID() uint32 {
	return h.data.SysId
}

func (h *Hero) SetHeroSysID(sysID uint32) {
	h.data.SysId = sysID
}

func (h *Hero) GetLevel() uint32 {
	return h.data.Level
}

func (h *Hero) SetLevel(level uint32) {
	h.data.Level = level
}

func (h *Hero) GetStage() uint32 {
	return h.data.Stage
}

func (h *Hero) SetStage(stage uint32) {
	h.data.Stage = stage
}

func (h *Hero) GetStar() uint32 {
	return h.data.Star
}

func (h *Hero) SetStar(star uint32) {
	h.data.Star = star
}

func (h *Hero) GetLockStatus() bool {
	return h.data.Locked
}

func (h *Hero) SetLock() {
	h.data.Locked = true
}

func (h *Hero) SetUnlock() {
	h.data.Locked = false
}

func (h *Hero) GetChangingSysID() uint32 {
	return h.data.ChangingSysId
}

func (h *Hero) SetChangingSysID(sysID uint32) {
	h.data.ChangingSysId = sysID
}

func (h *Hero) GetAwakenLevel() uint32 {
	return h.data.AwakenLevel
}

func (h *Hero) SetAwakenLevel(awakenLevel uint32) {
	h.data.AwakenLevel = awakenLevel
}

func (h *Hero) GetAllEquipment() map[uint32]uint64 {
	return h.data.Equipment
}

func (h *Hero) SetTag(tag common.HERO_TAG) {
	h.setTag(tag)
}

func (h *Hero) setTag(tag common.HERO_TAG) {
	h.data.Tag = uint32(tag)
}

func (h *Hero) getTag() common.HERO_TAG {
	return common.HERO_TAG(h.data.Tag)
}

func (h *Hero) WearEquipment(id uint64, pos uint32) {
	if h.data.Equipment == nil {
		h.data.Equipment = make(map[uint32]uint64)
	}
	h.data.Equipment[pos] = id
}

func (h *Hero) RemoveEquipment(pos []uint32) {
	if h.data.Equipment != nil {
		for _, v := range pos {
			delete(h.data.Equipment, v)
		}
	}
}

func (h *Hero) RemoveAllEquip() []uint64 {
	retEquipID := make([]uint64, 0, len(h.data.Equipment))
	for k, v := range h.data.Equipment {
		retEquipID = append(retEquipID, v)
		delete(h.data.Equipment, k)
	}

	return retEquipID
}

func (h *Hero) Flush() *cl.Hero {
	if h != nil {
		o := &cl.Hero{
			Data: h.data.Clone(),
		}
		return o
	}
	return nil
}

// CheckHeroType ：验证卡牌类型是否符合要求
func (h *Hero) CheckHeroType(sysID, needType uint32) bool {
	switch needType {
	case goxml.HeroStarUpCostTypeSelf:
		if sysID != h.data.SysId {
			return false
		}
	case goxml.HeroStarUpCostTypeRace:
		info := goxml.GetData().HeroInfoM.Index(h.data.SysId)
		costHeroInfo := goxml.GetData().HeroInfoM.Index(sysID)
		if info == nil || costHeroInfo == nil {
			return false
		}
		if costHeroInfo.Race != info.Race {
			return false
		}
	case goxml.HeroStarUpCostTypeAny:
		return true
	default:
		return false
	}
	return true
}

func (h *Hero) GetAllGem() map[uint32]uint64 {
	return h.data.Gem
}

// GetGemID : 获取穿戴的宝石id
func (h *Hero) GetGemID(slot uint32) uint64 {
	return h.data.Gem[slot]
}

// GetGemSlot : 获取穿戴宝石的slot
func (h *Hero) GetGemSlot(gemID uint64) uint32 {
	for slot, id := range h.data.Gem {
		if id == gemID {
			return slot
		}
	}
	return 0
}

// WearGem ：穿戴,替换宝石
func (h *Hero) WearGem(user *User, slot uint32, id uint64) {
	if h.data.Gem == nil {
		h.data.Gem = make(map[uint32]uint64)
	}

	_, exist := h.data.Gem[slot]
	if exist {
		h.RemoveGem(user, slot)
	}
	h.data.Gem[slot] = id
	if gem := user.GemManager().GetGem(id); gem != nil {
		gem.SetGemHeroID(h.data.Id)
		user.GemManager().SetChange(id)
		user.HeroManager().SetChange(h.data.Id)
		h.SetHeroAttrChange()
	}
}

// RemoveGem ：卸下宝石
func (h *Hero) RemoveGem(user *User, slot uint32) {
	gemID, exist := h.data.Gem[slot]
	if !exist {
		return
	}
	delete(h.data.Gem, slot)
	user.HeroManager().SetChange(h.data.Id)
	gem := user.GemManager().GetGem(gemID)
	if gem != nil {
		gem.SetGemHeroID(0)
		user.GemManager().SetChange(gemID)
	}
	h.SetHeroAttrChange()
}

// RemoveAllGem : 获取英雄身上全部宝石id
func (h *Hero) RemoveAllGem() []uint64 {
	retGemID := make([]uint64, 0, len(h.data.Gem))
	for k, gemID := range h.data.Gem {
		retGemID = append(retGemID, gemID)
		delete(h.data.Gem, k)
	}

	return retGemID
}

func (h *Hero) HasSameTypeEmblem(user *User, typeID uint32, exceptSlot uint32) bool {
	emblems := h.GetDisplayEmblems(user)
	for index, emblem := range emblems {
		if emblem == nil {
			continue
		}
		if uint32(index+1) == exceptSlot {
			continue
		}
		curInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if curInfo != nil && curInfo.Type == typeID {
			return true
		}
	}
	return false
}

func (h *Hero) GetAllEmblem() map[uint32]uint64 {
	return h.data.Emblem
}

// 获取英雄战斗的纹章情况
// 返回的列表中nil是空的纹章
func (h *Hero) GetDisplayEmblems(user *User) []*Emblem {
	if h.modify != nil && len(h.modify.emblems) > 0 {
		return h.modify.emblems
	}
	emblemM := user.EmblemManager()
	emblems := make([]*Emblem, goxml.EmblemPosMax)
	//existType := make([]uint32, 0, EmblemWearMaxNum)
	for slot, v := range h.data.Emblem {
		emblem := emblemM.Get(v)
		if emblem == nil {
			l4g.Errorf("GetDisplayEmblems error. emblem not exist. uid:%d, emblem:%d", user.ID(), v)
			continue
		}
		emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if emblemInfo == nil {
			l4g.Errorf("GetDisplayEmblems error. no find sys id. uid:%d, emblem:%d sysID:%d", user.ID(), v, emblem.Data.SysId)
			continue
		}
		emblems[slot-1] = emblem
	}
	return emblems
}

func (h *Hero) GetEmblemSlot(emblemID uint64) uint32 {
	for slot, id := range h.data.Emblem {
		if id == emblemID {
			return slot
		}
	}
	return goxml.EmblemPosNone
}

func (h *Hero) GetEmblemBySlot(slot uint32) uint64 {
	return h.data.Emblem[slot]
}

func (h *Hero) WearEmblem(user *User, slot uint32, emblem *Emblem) {
	if h.data.Emblem == nil {
		h.data.Emblem = make(map[uint32]uint64)
	}
	_, ok := h.data.Emblem[slot]
	if ok {
		h.TakeOffEmblem(user, slot)
	}
	h.data.Emblem[slot] = emblem.Data.Id
	emblem.SetEmblemHid(h.data.Id)
	user.EmblemManager().SetChange(emblem.Data.Id)
	user.HeroManager().SetChange(h.data.Id)
}

func (h *Hero) TakeOffEmblem(user *User, slot uint32) {
	emblemID, ok := h.data.Emblem[slot]
	if !ok {
		return
	}
	delete(h.data.Emblem, slot)
	user.HeroManager().SetChange(h.data.Id)
	emblem := user.EmblemManager().Get(emblemID)
	if emblem != nil && emblem.Data.Hid == h.data.Id {
		emblem.SetEmblemHid(0)
		user.EmblemManager().SetChange(emblemID)
	}
}

func (h *Hero) RemoveAllEmblem() []uint64 {
	retEmblemID := make([]uint64, 0, len(h.data.Emblem))
	for k, v := range h.data.Emblem {
		retEmblemID = append(retEmblemID, v)
		delete(h.data.Emblem, k)
	}

	return retEmblemID
}
func (h *Hero) CalcReviveReturnRes(resetAwakenLevel bool) []*cl.Resource {
	return h.calcReviveReturnRes(resetAwakenLevel)
}

// 计算重生返还资源
func (h *Hero) calcReviveReturnRes(resetAwakenLevel bool) []*cl.Resource {
	var retRes []*cl.Resource
	if h.data.Stage > goxml.GetData().HeroStageInfoM.GetDefaultStage() {
		for i := goxml.GetData().HeroStageInfoM.GetDefaultStage(); i < h.data.Stage; i++ {
			sInfo := goxml.GetData().HeroStageInfoM.Index(i)
			if len(sInfo.Costs) > 0 {
				retRes = append(retRes, sInfo.Costs...)
			}
		}
	}

	if h.data.Level > goxml.GetData().HeroLevelInfoM.GetDefaultLv() {
		for i := goxml.GetData().HeroLevelInfoM.GetDefaultLv(); i < h.data.Level; i++ {
			lInfo := goxml.GetData().HeroLevelInfoM.Index(i)
			if len(lInfo.Costs) > 0 {
				retRes = append(retRes, lInfo.Costs...)
			}
		}
	}
	if resetAwakenLevel && h.data.AwakenLevel > 0 {
		var totalCount uint32
		heroInfo := goxml.GetData().HeroInfoM.Index(h.data.SysId)
		if heroInfo != nil && heroInfo.AwakenValue > 0 {
			for i := uint32(1); i <= h.data.AwakenLevel; i++ {
				heroAwakenLevelInfo := goxml.GetData().HeroAwakenLevelInfoM.Index(i)
				if heroAwakenLevelInfo == nil {
					continue
				}
				totalCount += heroAwakenLevelInfo.Count
			}
			if totalCount > 0 {
				retRes = append(retRes, &cl.Resource{
					Type:  uint32(common.RESOURCE_ITEM),
					Value: heroInfo.AwakenValue,
					Count: totalCount,
				})
			}
		}
	}
	return retRes
}

// 计算分解返还资源
func (h *Hero) CalcDecomposeReturnRes() []*cl.Resource {
	retRes := h.calcReviveReturnRes(true)
	heroInfo := goxml.GetData().HeroInfoM.Index(h.data.SysId)
	if heroInfo != nil {
		rare := heroInfo.Rare
		if h.GetRare() != 0 {
			rare = h.GetRare()
		}
		info := goxml.GetData().HeroStarInfoM.Index(h.GetStar(), rare, heroInfo.Race)
		if info != nil {
			retRes = append(retRes, info.ResAwards...)
		}
	}

	return retRes
}

// 获取英雄战力, 默认的英雄战力
func (h *Hero) GetHeroPower(user *User) uint64 {
	h.isRecalculate(user)
	return h.power
}

func (h *Hero) GetHeroSeasonPower(user *User) int64 {
	heroBalanceInfo := goxml.GetData().HeroBalanceInfoM.Index(h.data.SysId, h.data.Star)
	if heroBalanceInfo == nil {
		l4g.Errorf("user:%d GetHeroSeasonPower: getHeroBalanceInfo nil. sysId:%d hero star:%d",
			user.ID(), h.data.SysId, h.data.Star)
		return 0
	}
	monsterInfo := goxml.GetData().MonsterInfoM.Index(heroBalanceInfo.MonsterId)
	if monsterInfo == nil {
		l4g.Errorf("user:%d GetHeroSeasonPower: get MonsterInfo nil. id:%d", user.ID(), heroBalanceInfo.MonsterId)
		return 0
	}
	power := int64(monsterInfo.GetPower(goxml.GetData()))
	power += int64(h.GetSeasonSkillPower())
	return power
}

func (h *Hero) getRealLevelAndStage(user *User) (uint32, uint32) {
	level := h.data.Level
	stage := h.data.Stage
	shareHeroAttr := user.ShareGrowth().getShareHeroAttr()
	if h.IsTagBlessed() && shareHeroAttr != nil {
		level = shareHeroAttr.Level
		stage = shareHeroAttr.Stage

		//共鸣突破等级，不能超过英雄突破等级上限
		info := goxml.GetData().HeroInfoM.Index(h.data.SysId)
		if info != nil {
			if stage > info.LimitStage {
				stage = info.LimitStage
			}
		}
	}
	return level, stage
}

// GetTotalAttr ：获取英雄属性
func (h *Hero) GetTotalAttr(user *User) []int64 {
	h.isRecalculate(user)
	return h.totalAttr
}

// GetPsAttr : 额外的养成技能
func (h *Hero) GetRaisePsAttr(user *User) []uint64 {
	h.isRecalculate(user)
	return h.GetBattleRaisePsAttrs()
}

func (h *Hero) GetBattleRaisePsAttrs() []uint64 {
	var raisePsAttr []uint64
	for _, raises := range h.raisePSs {
		raisePsAttr = append(raisePsAttr, raises...)
	}
	return raisePsAttr
}

// 根据 attrTag 判断是否重新计算
func (h *Hero) isRecalculate(user *User) {
	if !h.attrTag {
		h.resetLogTag()
		h.calcTotalAttr(user, true) // 重新计算英雄属性
		h.calcPower()               // 重新计算Power
		h.updateAttrTag()
		h.updateSnapshotPower(user)
	}
}

// 更新英雄战力 - 仅用于完美快照
func (h *Hero) updateSnapshotPower(user *User) {
	if h.data.SnapshotPower != h.power {
		h.data.SnapshotPower = h.power
		user.HeroManager().SetChange(h.data.Id)
	}
}

// 计算英雄的总属性
// @param user *User
// @param needCheckSeasonLinkActivation bool 是否需要检查激活状态 - 跑测工具可通过此参数，控制是否激活赛季羁绊
func (h *Hero) calcTotalAttr(user *User, needCheckSeasonLinkActivation bool) {
	if len(h.totalAttr) == 0 {
		h.totalAttr = make([]int64, goxml.AttrMaxNum) // goxml.AttrMaxNum: 战斗中最大属性值
	} else {
		for index := range h.totalAttr {
			h.totalAttr[index] = 0
		}
	}

	h.raisePSs = make(map[RaisePSType][]uint64)
	h.links = make(map[uint32]uint32)

	// 更新 hero的 基础属性 到 totalAttr
	if !h.addBaseAttr(user) {
		l4g.Errorf("user: %d Hero.calcTotalAttr: hero base attr is error. hid: %d, sysID: %d",
			user.ID(), h.data.Id, h.data.SysId)
		return
	}
	// 更新 hero的 技能属性 到 totalAttr
	h.addSkillAttr()
	// 更新 hero突破属性 到 totalAttr
	h.addStageAttr(user)
	// 更新 装备养成属性 到 totalAttr
	h.addEquipAttr(user)
	// 更新 宝石养成属性 到 totalAttr
	h.addGemAttr(user)
	// 更新 纹章养成属性 到 totalAttr，纹章技能战力到 skillPower, 纹章skill 到 extraSkills
	emblems := h.GetDisplayEmblems(user)
	h.addEmblemAttr(user, emblems)

	// 更新 三系觉醒属性
	h.addAwakenAttr()

	// 更新 皮肤属性
	h.addSkinAttr(user)

	// 更新 赛季羁绊属性
	h.addSeasonLinkAttr(user, needCheckSeasonLinkActivation)

	// TODO 全局属性：
	// 更新 境界的全局属性 到 totalAttr
	h.addMemoryGlobalAttr(user)
	// 更新 神器的全局属性 到 totalAttr
	h.addArtifactGlobalAttr(user)
	// 更新 公会天赋的全局属性 到 totalAttr
	if user.UserGuild().GuildID() != 0 { // 先判断有没有公会
		h.addGuildTalentGlobalAttr(user)
	}
	// 更新 图鉴的全局属性  到  totalAttr
	h.addHandbookGlobalAttr(user)

	// 更新 契约圣所的全局属性 到 totalAttr
	h.addGoddessGlobalAttr(user)

	// 更新 遗物图鉴全局属性 到 totalAttr
	h.addRemainBookGlobalAttr(user)

	// 更新 宠物的全局属性 到 totalAttr
	h.addPokemonGlobalAttr(user)

	// 更新 英雄升品
	h.addRareUpAttr()

	// 星级羁绊
	h.addStarAttr()

	goxml.FixAttr(h.totalAttr)
}

// 计算限制品质的符文属性 （当前仅世界boss使用）
// @param user *User
// @param limitEmblems []*Emblem 限制品质的符文
func (h *Hero) CalcLimitEmblem(user *User, limitEmblems []*Emblem) {
	if len(limitEmblems) == 0 {
		return
	}
	h.addEmblemAttr(user, limitEmblems)
}

// UpdatePower ：更新英雄战力
func (h *Hero) calcPower() {
	h.power = battle.CalPower(h.totalAttr, true)
}

func (h *Hero) SetHeroAttrChange() {
	h.attrTag = false
}

func (h *Hero) updateAttrTag() {
	h.attrTag = true
}

// 计算英雄基础属性
func (h *Hero) addBaseAttr(user *User) bool {
	sysId := h.data.SysId
	level, stage := h.getRealLevelAndStage(user)
	h.setModifyLevelAndStage(level, stage) //为了保证getbattlelevelandstage一致
	info := goxml.GetData().HeroInfoM.Index(sysId)
	if info == nil {
		l4g.Errorf("HeroInfoM not exist. %d", sysId)
		return false
	}

	dataInfo := goxml.GetData().HeroDataInfoM.Index(sysId, stage)
	rare := info.Rare
	if h.GetRare() != 0 {
		rare = h.GetRare()
	}
	starInfo := goxml.GetData().HeroStarInfoM.Index(h.data.Star, rare, info.Race)

	if dataInfo == nil || starInfo == nil {
		l4g.Errorf("config data not exist. %d, %d, %v, %v, %v", sysId, stage, info, dataInfo, starInfo)
		return false
	}

	jobStarAttr := goxml.GetData().HeroStarInfoM.GetJobBaseAttr(starInfo, info.Job)
	if jobStarAttr == nil {
		l4g.Errorf("jobStarAttr not exist. %d, %d, %d", sysId, h.data.Star, info.Job)
		return false
	}

	//基础属性 = 突破基础 + 升级属性*(等级-1)*(1+升星算法基础属性%）+升星基础
	h.totalAttr[goxml.AttrHp] = int64(dataInfo.Hp) +
		int64(float64(info.Hp*(level-1))*(1.0+float64(starInfo.HpPct)/goxml.BaseFloat)) + jobStarAttr.Hp
	h.totalAttr[goxml.AttrAttack] = int64(dataInfo.Attack) +
		int64(float64(info.Attack*(level-1))*(1.0+float64(starInfo.AttackPct)/goxml.BaseFloat)) + jobStarAttr.Attack
	h.totalAttr[goxml.AttrPhyDef] = int64(dataInfo.PhyDef) +
		int64(float64(info.PhyDef*(level-1))*(1.0+float64(starInfo.PhyDefPct)/goxml.BaseFloat)) + jobStarAttr.PhyDef
	h.totalAttr[goxml.AttrMagDef] = int64(dataInfo.MagDef) +
		int64(float64(info.MagDef*(level-1))*(1.0+float64(starInfo.MagDefPct)/goxml.BaseFloat)) + jobStarAttr.MagDef
	if h.totalAttr[goxml.AttrHp] < 0 || h.totalAttr[goxml.AttrAttack] < 0 ||
		h.totalAttr[goxml.AttrPhyDef] < 0 || h.totalAttr[goxml.AttrMagDef] < 0 {
		l4g.Errorf("total attr < 0. hid:%d, star:%d, stage:%d, job:%d, hp:%d, attack:%d, phyDef:%d, magDef:%d",
			sysId, h.data.Star, stage, info.Job, h.totalAttr[goxml.AttrHp],
			h.totalAttr[goxml.AttrAttack], h.totalAttr[goxml.AttrPhyDef], h.totalAttr[goxml.AttrMagDef])
		return false
	}

	h.totalAttr[goxml.AttrFixedDam] = int64(float64(dataInfo.FixedDam+info.FixedDam*(level-1)) *
		(1.0 + float64(starInfo.FixedDamPct)/goxml.BaseFloat))
	h.totalAttr[goxml.AttrFixedDamReduce] = int64(float64(dataInfo.FixedDamReduce+info.FixedDamReduce*(level-1)) *
		(1.0 + float64(starInfo.FixedDamReducePct)/goxml.BaseFloat))
	h.totalAttr[goxml.AttrSpeed] = int64(float64(dataInfo.Speed+info.Speed*(level-1)) *
		(1.0 + float64(starInfo.SpeedPct)/goxml.BaseFloat))

	h.totalAttr[goxml.AttrHitRate] = goxml.GetData().BattleParaInfoM.BaseHitP + int64(dataInfo.HitRate)    //命中率
	h.totalAttr[goxml.AttrDodgeRate] = int64(dataInfo.DodgeRate)                                           //闪避率
	h.totalAttr[goxml.AttrCritRate] = goxml.GetData().BattleParaInfoM.BaseCritP + int64(dataInfo.CritRate) //暴击率
	h.totalAttr[goxml.AttrCritReduceRate] = int64(dataInfo.CritReduceRate)                                 //抗暴率
	h.totalAttr[goxml.AttrControlRate] = int64(dataInfo.ControlRate)                                       //控制
	h.totalAttr[goxml.AttrControlReduceRate] = int64(dataInfo.ControlReduceRate)                           //免控
	h.totalAttr[goxml.AttrHealRate] = int64(dataInfo.HealRate)                                             //治疗
	h.totalAttr[goxml.AttrGetHealRate] = int64(dataInfo.GetHealRate)                                       //受疗
	h.totalAttr[goxml.AttrCritDamAddRate] = goxml.GetData().BattleParaInfoM.BaseCritDamP
	h.totalAttr[goxml.AttrPVPDamReduceRate] = int64(dataInfo.PvpDmgRedRate)
	h.totalAttr[goxml.AttrPVPDamAddRate] = int64(dataInfo.PvpDmgAddRate)
	return true
}

// 更新技能属性
func (h *Hero) addSkillAttr() {
	info := goxml.GetData().HeroInfoM.Index(h.data.SysId)
	if info == nil {
		l4g.Errorf("addSkillAttr failed: HeroInfo not exist, sysID:%d", h.data.SysId)
		return
	}

	levelInfo := goxml.GetData().SkillLevelInfoM.Index(info.SkillLevelID, h.data.Star)
	if levelInfo == nil {
		l4g.Errorf("addSkillAttr failed: SkillLevelInfo not exist, sysID:%d, SkillLevelID:%d, star:%d",
			h.data.SysId, info.SkillLevelID, h.data.Star)
		return
	}

	for i := goxml.SkillPosActive1; i < goxml.SkillPosMax; i++ {
		if i == goxml.SkillPosSeasonAddHero {
			continue
		}

		if i == goxml.SkillPosAwake {
			// 更新英雄觉醒属性
			if awakeAttrs := goxml.GetData().HeroAwakeInfoM.GetAwakeAttrs(info.Id, h.data.Star); len(awakeAttrs) != 0 {
				for _, attr := range awakeAttrs {
					l4g.Debugf("addSkillAttr.awakeAttrs hid:%d sysID:%d attr:%+v", h.data.Id, h.data.SysId, attr)
					h.totalAttr[attr.Type] += attr.Value
				}
			}
		} else {
			if skill := goxml.GetData().SkillInfoM.GroupLevel(info.Skills[i], levelInfo.SkillLevel[i]); skill != nil {
				for _, attr := range skill.Attrs {
					l4g.Debugf("addSkillAttr. hid:%d sysID:%d attr:%+v", h.data.Id, h.data.SysId, attr)
					h.totalAttr[attr.Type] += attr.Value
				}
			}
		}
	}

	// 羁绊1
	if h.GetStar() >= goxml.GetData().ConfigInfoM.Link1UnlockHeroStar && info.Link1ID > 0 {
		h.AddLink(info.Link1ID, 1)
	}
	// 羁绊2
	if h.GetStar() >= goxml.GetData().ConfigInfoM.Link2UnlockHeroStar && info.Link2ID > 0 {
		h.AddLink(info.Link2ID, 1)
	}
}

// 更新英雄突破属性
func (h *Hero) addStageAttr(user *User) {
	_, stage := h.getRealLevelAndStage(user)
	stageAttrs := goxml.GetData().HeroDataInfoM.GetStageAttrs(h.data.SysId, stage)
	h.mergeAttr(stageAttrs)
	// 英雄突破技能
	h.AddRaisePSs(RaisePSTypeHeroStage, goxml.GetData().HeroDataInfoM.GetStageSkills(h.data.SysId, stage))
}

func (h *Hero) addEquipmentAttr(user *User, equipment *cl.Equipment, minChantLevel, minStrengthLevel, minRefineLevel *uint32) {
	equipInfo := goxml.GetData().EquipInfoM.Index(equipment.SysId)
	if equipInfo == nil {
		l4g.Errorf("equipInfo not exist uid:%d sysID:%d", user.ID(), equipment.SysId)
		return
	}
	attrCoeInfo := goxml.GetData().EquipAttrCoeInfoM.Index(equipInfo.Rare)
	if attrCoeInfo == nil {
		l4g.Errorf("attrCoeInfo not exist uid:%d sysID:%d", user.ID(), equipInfo.Rare)
		return
	}
	// 基础属性
	h.totalAttr[equipInfo.AttrType] += int64(equipInfo.AttrValue)
	// 强化属性
	goxml.GetData().EquipStrengthInfoM.GetAttr(
		h.totalAttr, equipInfo.Part, equipment.StrengthLevel, attrCoeInfo.StrengthCoe)
	// 精炼属性
	goxml.GetData().EquipRefineInfoM.GetAttr(
		h.totalAttr, equipInfo.Part, equipment.RefineLevel, equipment.RefineExp, attrCoeInfo.RefineCoe)
	// 精炼技巧属性
	goxml.GetData().EquipRefineTechniqueInfoM.GetAttr(
		h.totalAttr, equipment.SysId, equipment.RefineLevel)
	// 附魔属性
	goxml.GetData().EquipEnchantInfoM.GetAttr(
		h.totalAttr, equipInfo.Part, equipment.EnchantLevel, attrCoeInfo.EnchantCoe)
	// 进阶属性
	goxml.GetData().EquipEvolutionInfoM.GetAttr(
		h.totalAttr, equipInfo.EvolutionId, equipment.EvolutionLevel)

	if equipment.StrengthLevel < *minStrengthLevel {
		*minStrengthLevel = equipment.StrengthLevel
	}
	if equipment.RefineLevel < *minRefineLevel {
		*minRefineLevel = equipment.RefineLevel
	}
	suitLevel := goxml.GetData().EquipEnchantInfoM.GetSuitLevel(equipInfo.Part, equipment.EnchantLevel)
	if suitLevel < *minChantLevel {
		*minChantLevel = suitLevel
	}
}

func (h *Hero) addEquipMasterAttr(minChantLevel, minStrengthLevel, minRefineLevel uint32) {
	goxml.GetData().EquipEnchantMasterInfoM.GetAttr(
		h.totalAttr, minChantLevel) //装备附魔大师
	goxml.GetData().MasterDataInfoM.GetAttr(
		h.totalAttr, goxml.MasterEquipStrength, minStrengthLevel) // 装备强化大师
	goxml.GetData().MasterDataInfoM.GetAttr(
		h.totalAttr, goxml.MasterEquipRefine, minRefineLevel) // 装备精炼大师
	raisePSs := goxml.GetData().EquipEnchantMasterInfoM.GetRaisePSs(minChantLevel) //装备附魔技能
	if len(raisePSs) != 0 {
		h.AddRaisePSs(RaisePSTypeEquipEnchant, raisePSs)
	}
}

// 更新装备属性
func (h *Hero) addEquipAttr(user *User) {
	var minChantLevel, minStrengthLevel, minRefineLevel uint32 = 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
	var equipNum int
	shareEquipmentAttr := user.ShareGrowth().getShareEquipmentAttr()
	if h.IsTagBlessed() && len(shareEquipmentAttr) > 0 {
		for _, share := range shareEquipmentAttr {
			equipment := &cl.Equipment{
				SysId:          share.SysId,
				StrengthLevel:  share.StrengthLevel,
				RefineExp:      share.RefineExp,
				RefineLevel:    share.RefineLevel,
				EnchantLevel:   share.EnchantLevel,
				EvolutionLevel: share.EvolutionLevel,
			}
			h.addEquipmentAttr(user, equipment, &minChantLevel, &minStrengthLevel, &minRefineLevel)
			equipNum++
		}
	} else {
		equipM := user.EquipManager()
		for _, id := range h.data.Equipment {
			equip := equipM.Get(id)
			if equip == nil {
				l4g.Errorf("equip not exist uid:%d equipId:%d", user.ID(), id)
				continue
			}
			h.addEquipmentAttr(user, equip.Data, &minChantLevel, &minStrengthLevel, &minRefineLevel)
			equipNum++
		}
	}
	if equipNum >= EquipNum {
		h.addEquipMasterAttr(minChantLevel, minStrengthLevel, minRefineLevel)
	}
}

// 添加宝石属性
func (h *Hero) addGemAttr(user *User) {
	for _, gem := range h.data.Gems {
		gemUpgradeInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(gem.Slot, gem.Level)
		if gemUpgradeInfo == nil {
			continue
		}
		if gemUpgradeInfo.AttrType1 > 0 && gemUpgradeInfo.AttrValue1 > 0 {
			h.totalAttr[gemUpgradeInfo.AttrType1] += gemUpgradeInfo.AttrValue1
		}
		if gemUpgradeInfo.AttrType2 > 0 && gemUpgradeInfo.AttrValue2 > 0 {
			h.totalAttr[gemUpgradeInfo.AttrType2] += gemUpgradeInfo.AttrValue2
		}
	}
	heroInfo := goxml.GetData().HeroInfoM.Index(h.data.SysId)
	if heroInfo == nil {
		l4g.Errorf("user:%d addGemAttr heroInfo not exist. heroId:%d", user.ID(), h.data.SysId)
		return
	}

	if heroInfo.Rare < goxml.HeroRareFakeFive {
		return
	}

	leftGem := h.GetGem(uint32(common.GEM_CONFIG_GEM_LEFT_SLOT))
	rightGem := h.GetGem(uint32(common.GEM_CONFIG_GEM_RIGHT_SLOT))
	if leftGem == nil || rightGem == nil {
		return
	}
	leftGemInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(leftGem.Slot, leftGem.Level)
	rightGemInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(rightGem.Slot, rightGem.Level)

	if leftGemInfo == nil || rightGemInfo == nil {
		return
	}

	// 羁绊
	if h.isActiveLink3(leftGemInfo, rightGemInfo) && heroInfo.Link3ID > 0 {
		h.updateLogTag(goxml.GemLink)
		h.AddLink(heroInfo.Link3ID, 1)
	}

	var talentRaisePS uint64
	// 技能
	if leftGemInfo.IsTalent1 && rightGemInfo.IsTalent1 && heroInfo.GemPassiveSkill1RaisePS != 0 {
		h.updateLogTag(goxml.GemPasSkill1)
		talentRaisePS = heroInfo.GemPassiveSkill1RaisePS
	}
	if leftGemInfo.IsTalent2 && rightGemInfo.IsTalent2 && heroInfo.GemPassiveSkill2RaisePS != 0 {
		h.updateLogTag(goxml.GemPasSkill2)
		talentRaisePS = heroInfo.GemPassiveSkill2RaisePS
	}
	if talentRaisePS != 0 {
		h.AddRaisePSs(RaisePSTypeHeroTalent, []uint64{talentRaisePS})
	}
}

// 添加纹章属性
//
//nolint:funlen
func (h *Hero) addEmblemAttr(user *User, emblems []*Emblem) {
	typeSuitSkill := make(map[uint32]goxml.Uint32Slice)
	raisePassiveSkill := make(map[uint32]uint32)
	heroInfo := goxml.GetData().HeroInfoM.Index(h.data.SysId)
	var needLink bool
	var passiveSkill1Check bool
	var exEmblemNum, exEmblemRedNum, exEmblemOrangeNum uint32
	minRare := uint32(math.MaxUint32)
	if heroInfo == nil {
		l4g.Errorf("update emblem attr error. hero info not exist. uid:%d, hero sys id:%d", user.ID(), h.data.SysId)
		return
	}
	emConfig := goxml.GetData().EmblemConfigInfoM
	//4件专属英雄的装备
	//专有技能及符文羁绊现版本需要4个符文
	if len(emblems) == goxml.EmblemPos4 {
		passiveSkill1Check = true
		if h.data.Star >= goxml.GetData().ConfigInfoM.GetLink4UnLockHeroStar() {
			needLink = true
		}
	}

	affixIdToNum := make(map[uint32]uint32, len(emblems))
	for _, emblem := range emblems {
		if emblem == nil {
			needLink = false
			passiveSkill1Check = false
			continue
		}
		info := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if info == nil {
			l4g.Errorf("update emblem attr error. emblem info not exist. uid:%d, emblem sys id:%d", user.ID(), emblem.Data.SysId)
			continue
		}

		// 基础属性
		h.mergeAttr(info.Attr)
		// 强化属性
		strengthAttr := goxml.GetData().EmblemLevelInfoM.GetAttr(info.LevelIndex, emblem.Data.Level)
		for attr, value := range strengthAttr {
			h.totalAttr[attr] += value * int64(info.StrengthCoe) / int64(goxml.BaseInt)
		}
		//符文随机词条添加
		if emblem.Data.SkillId > 0 {
			magicSkillInfo := goxml.GetData().EmblemMagicSkillInfoM.Index(emblem.Data.SkillId)
			if magicSkillInfo != nil {
				if goxml.GetData().EmblemMagicSkillInfoM.IsAdditiveSkill(emblem.Data.SkillId) {
					if goxml.GetData().EmblemHeroGroupInfoM.IsHeroAdditiveSkill(h.GetHeroSysID(), magicSkillInfo.MagicSkill) {
						raisePassiveSkill[emblem.Data.SkillId]++
					}
				} else {
					raisePassiveSkill[emblem.Data.SkillId]++
				}
			} else {
				// 洗炼出的词条走下面的判断
				magicSkillInfos := goxml.GetData().EmblemSuccinctMagicSkillInfoM.GetRecordsByHeroIdEmblemRare(h.GetHeroSysID(), info.Rare)
				for _, skillInfo := range magicSkillInfos {
					if skillInfo.Skill != emblem.Data.SkillId {
						continue
					}
					if skillInfo.Type == goxml.EmblemSkillTypeAdditive {
						if h.GetHeroSysID() == skillInfo.HeroId {
							raisePassiveSkill[emblem.Data.SkillId]++
						}
					} else {
						raisePassiveSkill[emblem.Data.SkillId]++
					}
				}
			}
		}

		// 词缀添加
		if emblem.Data.Succinct != nil && emblem.Data.Succinct.AffixId > 0 {
			affixIdToNum[emblem.Data.Succinct.AffixId]++
		}

		//套装整理对应套装ID
		if info.SuitSkill > 0 && heroInfo.Race == info.Race {
			_, exist := typeSuitSkill[info.Type]
			if !exist {
				typeSuitSkill[info.Type] = make(goxml.Uint32Slice, 0, goxml.EmblemPosMax)
			}
			typeSuitSkill[info.Type] = append(typeSuitSkill[info.Type], info.SuitSkill)
		}
		//羁绊技能加成最小判断
		if minRare > info.Rare {
			minRare = info.Rare
		}
		if heroInfo.Id == emblem.Data.AdditiveHero {
			if info.Rare >= goxml.EmblemRareRed {
				exEmblemRedNum++
			} else if info.Rare >= goxml.EmblemRareOrange {
				exEmblemOrangeNum++
			} else {
				exEmblemNum++
			}
		}
	}

	//套装属性的计算
	suitNum := goxml.GetData().EmblemInfoM.CalcSuitSkillId(user.ID(), typeSuitSkill, raisePassiveSkill)
	if suitNum > 0 {
		h.updateLogTag(suitNum)
		if suitNum == goxml.EmblemFourSuit { // 激活4件套
			// 处理词缀技能
			var suitInfo *goxml.EmblemSuitInfo
			for _, suitSkills := range typeSuitSkill {
				if len(suitSkills) == 4 { //nolint:mnd
					for _, suitSkillId := range suitSkills {
						info := goxml.GetData().EmblemSuitInfoM.Index(suitSkillId)
						if info.Affix1 != 0 {
							suitInfo = info
						}
						if info.Affix2 != 0 {
							suitInfo = info
							break
						}
					}
					break
				}
			}
			if suitInfo != nil {
				targetAffixId := uint32(0)
				if affixIdToNum[suitInfo.Affix2] >= 4 { //nolint:mnd
					targetAffixId = suitInfo.Affix2
				} else if affixIdToNum[suitInfo.Affix1]+affixIdToNum[suitInfo.Affix2] >= 4 { //nolint:mnd
					targetAffixId = suitInfo.Affix1
				}
				if targetAffixId > 0 {
					raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(targetAffixId, 1)
					if raisePSInfo == nil {
						l4g.Errorf("user:%d hero:%d emblem raise passive skillId:%d count:%d is nil",
							user.ID(), h.data.Id, targetAffixId, 1)
					} else {
						h.AddRaisePSs(RaisePSTypeEmblemAffix, []uint64{raisePSInfo.ID})
					}
				}
			}
		}
	}

	for skillId, count := range raisePassiveSkill {
		raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skillId, count)
		if raisePSInfo == nil {
			l4g.Errorf("user:%d hero:%d emblem raise passive skillId:%d count:%d is nil",
				user.ID(), h.data.Id, skillId, count)
			continue
		}
		h.AddRaisePSs(RaisePSTypeEmblemSelfAndSuit, []uint64{raisePSInfo.ID})
	}

	heroRaisePs := make(map[uint32]uint32)
	if passiveSkill1Check {
		if minRare >= emConfig.GetHeroSkillUnlockRare1() && heroInfo.EmblemSkill1 > 0 {
			h.updateLogTag(goxml.EmblemPasSkill1)
			heroRaisePs[heroInfo.EmblemSkill1]++
		}
	}

	//专属技能判断
	exclusiveId := h.getEmblemExclusiveSkillId(exEmblemRedNum, exEmblemOrangeNum, heroInfo)
	if exclusiveId > 0 {
		if exclusiveId == heroInfo.EmblemExclusive3 {
			h.updateLogTag(goxml.EmblemExclusiveSkill3)
			heroRaisePs[heroInfo.EmblemExclusive3]++
		} else if exclusiveId == heroInfo.EmblemExclusive2 {
			h.updateLogTag(goxml.EmblemExclusiveSkill2)
			heroRaisePs[heroInfo.EmblemExclusive2]++
		} else if exclusiveId == heroInfo.EmblemExclusive1 {
			h.updateLogTag(goxml.EmblemExclusiveSkill1)
			heroRaisePs[heroInfo.EmblemExclusive1]++
		}
	}

	//l4g.Info("addEmblemAttr: userId:%d heroId:%d SysId:%d exEmblemNum:%d exEmblemRedNum:%d exEmblemOrangeNum:%d heroRaisePs:%v",
	//	user.ID(), h.data.Id, h.data.SysId, exEmblemNum, exEmblemRedNum, exEmblemOrangeNum, heroRaisePs)

	for skillId, count := range heroRaisePs {
		raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skillId, count)
		if raisePSInfo == nil {
			l4g.Errorf("user:%d hero:%d emblem raise passive skillId:%d count:%d is nil",
				user.ID(), h.data.Id, skillId, count)
			continue
		}
		h.AddRaisePSs(RaisePSTypeEmblemHero, []uint64{raisePSInfo.ID})
	}

	//羁绊判断
	if needLink && minRare >= emConfig.GetLink4UnlockCondition() && heroInfo.Link4ID > 0 {
		h.updateLogTag(goxml.EmblemLink)
		h.AddLink(heroInfo.Link4ID, 1)
	}

	exNum := exEmblemNum + exEmblemRedNum + exEmblemOrangeNum
	if exNum == 1 {
		h.updateLogTag(goxml.EmblemExclusive1)
	} else if exNum == 2 { //nolint:mnd
		h.updateLogTag(goxml.EmblemExclusive2)
	} else if exNum == 3 { //nolint:mnd
		h.updateLogTag(goxml.EmblemExclusive3)
	} else if exNum == 4 { //nolint:mnd
		h.updateLogTag(goxml.EmblemExclusive4)
	}
}

// 更新三系英雄觉醒属性
func (h *Hero) addAwakenAttr() {
	heroInfo := goxml.GetData().HeroInfoM.Index(h.data.SysId)
	if heroInfo == nil {
		l4g.Errorf("addAwakenAttr failed: HeroInfo not exist, sysID:%d", h.data.SysId)
		return
	}
	raisePSs := heroInfo.GetAwakenPassiveSkillRaisePSs(h.data.AwakenLevel)
	if len(raisePSs) > 0 {
		h.raisePSs[RaisePSTypeHeroAwaken] = append(h.raisePSs[RaisePSTypeHeroAwaken], raisePSs...)
	}
	if h.data.AwakenLevel <= goxml.HeroAwakenSkillMaxLevel {
		return
	}
	if h.data.AwakenLevel != goxml.HeroAwakenLinkLevel {
		l4g.Errorf("addAwakenAttr failed: invalid awaken level, sysID:%d awakenLevel:%d", h.data.SysId, h.data.AwakenLevel)
		return
	}
	if heroInfo.Link5ID > 0 {
		h.AddLink(heroInfo.Link5ID, 1)
	}
}

// 更新皮肤属性
func (h *Hero) addSkinAttr(user *User) {
	skinM := user.SkinManager()
	attrs := skinM.GetSkinPassiveSkills(h.GetHeroSysID())
	if len(attrs) > 0 {
		h.AddRaisePSs(RaisePSTypeSkin, attrs)
	}
}

// 添加境界的全局属性
func (h *Hero) addMemoryGlobalAttr(user *User) {
	// 获取境界全局属性
	memoryGlobalAttr := user.Memory().GetMemoryGlobalAttr()
	if memoryGlobalAttr != nil {
		h.mergeAttr(memoryGlobalAttr.Attr)
	}
}

// 添加神器的全局属性
func (h *Hero) addArtifactGlobalAttr(user *User) {
	artifactGlobalAttr := user.ArtifactManager().getArtifactGlobalAttr()
	if artifactGlobalAttr != nil {
		h.mergeAttr(artifactGlobalAttr.Attr)
	}
}

// 添加公会天赋的全局属性
func (h *Hero) addGuildTalentGlobalAttr(user *User) {
	talentGlobalAttr := user.GuildTalent().calcGlobalAttr()
	if talentGlobalAttr != nil {
		h.mergeAttr(talentGlobalAttr.Attr)
	}
}

// 添加图鉴的全局属性
func (h *Hero) addHandbookGlobalAttr(user *User) {
	// 图鉴的全局属性
	baseHandbookM := user.HandbookManager().baseHandbookM
	if baseHandbookM == nil {
		return
	}
	handbookGlobalAttr := baseHandbookM.getHandbookGlobalAttr()
	if handbookGlobalAttr != nil {
		h.mergeAttr(handbookGlobalAttr.Attr)
	}
}

func (h *Hero) addGoddessGlobalAttr(user *User) {
	attrs := user.GoddessContract().GetGoddessGlobalAttrs()
	if len(attrs) > 0 {
		h.mergeAttr(attrs)
	}
}

func (h *Hero) addRemainBookGlobalAttr(user *User) {
	attrs := user.RemainM().RemainBook().GetGlobalAttrs()
	if len(attrs) > 0 {
		h.mergeAttr(attrs)
	}
}

func (h *Hero) addPokemonGlobalAttr(user *User) {
	attrs := user.PokemonManager().GetPokemonGlobalAttr()
	if attrs != nil {
		h.mergeAttr(attrs.GetAttr())
	}
}

// TestHeroAttr ：供前端调试
// @param needCheckSeasonLinkActivation bool 是否需要检查激活状态 - 跑测工具可通过此参数，控制是否激活赛季羁绊
//
//nolint:varnamelen
func (h *Hero) TestHeroAttr(user *User, smsg *cl.L2C_HeroTestAttr, needCheckSeasonLinkActivation bool) {
	l := goxml.AttrMaxNum

	init := func() {
		if len(h.totalAttr) == 0 {
			h.totalAttr = make([]int64, goxml.AttrMaxNum) // goxml.AttrMaxNum: 战斗中最大属性值
		} else {
			for index := range h.totalAttr {
				h.totalAttr[index] = 0
			}
		}
	}
	h.raisePSs = make(map[RaisePSType][]uint64)
	h.links = make(map[uint32]uint32)

	trans := func() map[int32]int64 {
		attrMap := make(map[int32]int64, l)
		for attrType, value := range h.totalAttr {
			if value > 0 {
				attrMap[int32(attrType)] = value
			}
		}
		return attrMap
	}

	// hero base attr
	init()
	h.addBaseAttr(user)
	smsg.BaseAttr = trans()

	// hero skill attr
	init()
	h.addSkillAttr()
	smsg.SkillAttr = trans()

	// hero stage attr
	init()
	h.addStageAttr(user)
	smsg.StageAttr = trans()

	// equip attr
	init()
	h.addEquipAttr(user)
	smsg.EquipAttr = trans()

	// gem attr
	init()
	h.addGemAttr(user)
	smsg.GemAttr = trans()

	// emblem attr
	init()
	emblems := h.GetDisplayEmblems(user)
	h.addEmblemAttr(user, emblems)
	smsg.EmblemAttr = trans()

	// awaken attr
	init()
	h.addAwakenAttr()

	// skin attr
	h.addSkinAttr(user)

	// season link attr
	h.addSeasonLinkAttr(user, needCheckSeasonLinkActivation)

	// master attr
	init()
	//h.addMasterAttr(emblems)
	smsg.MasterAttr = trans()

	// memory global attr
	init()
	h.addMemoryGlobalAttr(user)
	smsg.MemoryGlobalAttr = trans()

	// artifact global attr
	init()
	h.addArtifactGlobalAttr(user)
	smsg.ArtifactGlobalAttr = trans()

	// guildTalent global attr
	init()
	h.addGuildTalentGlobalAttr(user)
	smsg.GuildTalentGlobalAttr = trans()

	// handbook global attr
	init()
	h.addHandbookGlobalAttr(user)
	smsg.HandbookGlobalAttr = trans()

	// goddess global attr
	init()
	h.addGoddessGlobalAttr(user)
	smsg.GoddessContractAttr = trans()

	// remain book global attr
	init()
	h.addRemainBookGlobalAttr(user)
	smsg.RemainBook = trans()

	// pokemon global attr
	init()
	h.addPokemonGlobalAttr(user)
	smsg.PokemonGlobalAttr = trans()

	// global attr
	init()
	h.addMemoryGlobalAttr(user)
	h.addArtifactGlobalAttr(user)
	h.addGuildTalentGlobalAttr(user)
	h.addHandbookGlobalAttr(user)
	h.addGoddessGlobalAttr(user)
	h.addRemainBookGlobalAttr(user)
	h.addPokemonGlobalAttr(user)
	smsg.GlobalAttr = trans()

	// total attr
	init()
	h.calcTotalAttr(user, needCheckSeasonLinkActivation)
	smsg.TotalAttr = trans()

	// 更新计算标签
	h.updateAttrTag()
}

func (h *Hero) mergeAttr(attrs map[uint32]int64) {
	for attrType, attrValue := range attrs {
		h.totalAttr[attrType] += attrValue
	}
}

// 获取英雄展示等级（最终等级）
// @return uint32
func (h *Hero) GetDisplayLevel(user *User) uint32 {
	if h.IsTagBlessed() {
		return user.ShareGrowth().getShareHeroLevel()
	}
	return h.GetLevel()
}

func (h *Hero) GetDisplayStage(user *User) uint32 {
	if h.IsTagBlessed() {
		return user.ShareGrowth().getShareHeroStage()
	}
	return h.GetStage()
}

// 根据属性计算评分（目前只有图鉴用）
func (u *User) CalcScore(attr map[uint32]int64) uint64 {
	totalAttr := make([]int64, goxml.AttrMaxNum)
	for attrType, attrValue := range attr {
		totalAttr[attrType] += attrValue
	}
	goxml.FixAttr(totalAttr)
	return battle.CalPower(totalAttr, false)
}

func (h *Hero) testCalPower(user *User) {
	h.calcTotalAttr(user, true)
	h.calcPower()
}

func (h *Hero) GetGem(slot uint32) *cl.Gem {
	if h.data == nil {
		return nil
	}
	for _, gem := range h.data.Gems {
		if gem.Slot == slot {
			return gem
		}
	}
	return nil
}

func (h *Hero) GetGemLevel(slot uint32) uint32 {
	if h.data == nil {
		return 0
	}
	for _, gem := range h.data.Gems {
		if gem.Slot == slot {
			return gem.Level
		}
	}
	return 0
}

func (h *Hero) initGem(slot uint32) *cl.Gem {
	newGem := &cl.Gem{
		Slot: slot,
	}

	h.data.Gems = append(h.data.Gems, newGem)
	return newGem
}

func (h *Hero) AddGemLevel(slot, upNum uint32) uint32 {
	gem := h.GetGem(slot)
	if gem == nil {
		gem = h.initGem(slot)
	}
	gem.Level += upNum
	return gem.Level
}

func (h *Hero) SetGemLevel(slot, newLeve uint32) uint32 {
	gem := h.GetGem(slot)
	if gem == nil {
		gem = h.initGem(slot)
	}
	gem.Level = newLeve
	return gem.Level
}

func (h *Hero) CalcGemReturnRes() []*cl.Resource {
	return h.calcGemReturnRes()
}

func (h *Hero) calcGemReturnRes() []*cl.Resource {
	var retRes []*cl.Resource
	for _, gem := range h.data.Gems {
		for l := uint32(0); l < gem.Level; l++ {
			upgradeInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(gem.Slot, l)
			if upgradeInfo == nil {
				continue
			}
			retRes = append(retRes, upgradeInfo.CostClRes...)
		}
	}
	return retRes
}

func (h *Hero) SetGems(tmpGem []*cl.Gem) {
	h.data.Gems = tmpGem
}

func (h *Hero) CloneGems() []*cl.Gem {
	if len(h.data.Gems) == 0 {
		return nil
	}
	tmpGems := make([]*cl.Gem, 0, len(h.data.Gems))

	for _, gem := range h.data.Gems {
		tmpGems = append(tmpGems, gem.Clone())
	}
	return tmpGems
}

func (h *Hero) AddLink(link, num uint32) {
	h.links[link] += num
}

func (h *Hero) GetLinks(user *User) map[uint32]uint32 {
	h.isRecalculate(user)
	return h.links
}

// 是否激活link3（宝石）
func (h *Hero) isActiveLink3(leftGemInfo, rightGemInfo *goxml.GemLevelInfoExt) bool {
	if leftGemInfo == nil || rightGemInfo == nil {
		return false
	}
	// 羁绊
	if h.GetStar() >= goxml.GetData().ConfigInfoM.Link3UnlockHeroStar {
		if leftGemInfo.IsLink && rightGemInfo.IsLink {
			return true
		}
	}
	return false
}

func (h *Hero) IsActiveLink3() bool {
	heroInfo := goxml.GetData().HeroInfoM.Index(h.GetHeroSysID())
	if heroInfo == nil {
		l4g.Errorf("")
		return false
	}
	leftGem := h.GetGem(uint32(common.GEM_CONFIG_GEM_LEFT_SLOT))
	rightGem := h.GetGem(uint32(common.GEM_CONFIG_GEM_RIGHT_SLOT))
	if leftGem == nil || rightGem == nil {
		return false
	}
	leftGemInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(leftGem.Slot, leftGem.Level)
	rightGemInfo := goxml.GetData().GemLevelInfoM.GetUpgradeInfo(rightGem.Slot, rightGem.Level)

	if leftGemInfo == nil || rightGemInfo == nil {
		return false
	}
	return h.isActiveLink3(leftGemInfo, rightGemInfo)
}

func (h *Hero) IsActiveLink4(user *User) bool {
	if h.GetStar() < goxml.GetData().ConfigInfoM.GetLink4UnLockHeroStar() {
		return false
	}
	emblemIds := h.GetAllEmblem()
	if len(emblemIds) < goxml.EmblemPos4 {
		return false
	}
	emblems := h.GetDisplayEmblems(user)
	minRare := uint32(math.MaxUint32)
	for _, emblem := range emblems {
		if emblem == nil || emblem.Data == nil {
			return false
		}
		info := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if info == nil {
			l4g.Errorf("emblem info not exist. uid:%d, emblem sys id:%d", user.ID(), emblem.Data.SysId)
			return false
		}
		//羁绊技能加成最小判断
		if minRare > info.Rare {
			minRare = info.Rare
		}
	}
	//羁绊判断
	return minRare >= goxml.GetData().EmblemConfigInfoM.GetLink4UnlockCondition()
}

func (h *Hero) CheckHandleActiveEmblemExclusive(srv Servicer, user *User) {
	counterSkillActivated, exclusiveLv := h.calcHeroEmblemSkill(user)
	if exclusiveLv > 0 {
		user.HandbookManager().GetHeroHandbookM().UpdateHeroEmblemExclusiveLv(srv, h.GetHeroSysID(), exclusiveLv)
	}
	if counterSkillActivated || exclusiveLv > 0 {
		user.LogHeroEmblemSkill(srv, h.GetHid(), h.GetHeroSysID(), counterSkillActivated, exclusiveLv)
	}
}

// calcEmblemExclusiveSkillLv
// @Description: 英雄符文技能激活情况(包括克制技能和专属技能)
// @receiver h
// @param user
// @return bool  克制技能是否激活
// @return uint32  专属技能等级
func (h *Hero) calcHeroEmblemSkill(user *User) (bool, uint32) {
	var exEmblemRedNum, exEmblemOrangeNum uint32
	emblems := h.GetDisplayEmblems(user)
	if len(emblems) < goxml.EmblemPos4 {
		return false, 0
	}
	heroInfo := goxml.GetData().HeroInfoM.Index(h.data.SysId)
	minRare := uint32(math.MaxUint32)
	for _, emblem := range emblems {
		if emblem == nil || emblem.Data == nil {
			return false, 0
		}
		info := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if info == nil {
			l4g.Errorf("emblem info not exist. uid:%d, emblem sys id:%d", user.ID(), emblem.Data.SysId)
			return false, 0
		}
		if heroInfo.Id == emblem.Data.AdditiveHero {
			if info.Rare >= goxml.EmblemRareRed {
				exEmblemRedNum++
			} else if info.Rare >= goxml.EmblemRareOrange {
				exEmblemOrangeNum++
			}
		}
		//
		if minRare > info.Rare {
			minRare = info.Rare
		}
	}

	exclusiveLv := uint32(0)
	//专属技能判断
	exclusiveId := h.getEmblemExclusiveSkillId(exEmblemRedNum, exEmblemOrangeNum, heroInfo)
	if exclusiveId > 0 {
		if exclusiveId == heroInfo.EmblemExclusive3 {
			exclusiveLv = 3
		} else if exclusiveId == heroInfo.EmblemExclusive2 {
			exclusiveLv = 2
		} else if exclusiveId == heroInfo.EmblemExclusive1 {
			exclusiveLv = 1
		}
	}
	counterSkillActivated := false
	if minRare >= goxml.GetData().EmblemConfigInfoM.GetHeroSkillUnlockRare1() && heroInfo.EmblemSkill1 > 0 {
		counterSkillActivated = true
	}

	return counterSkillActivated, exclusiveLv
}

func (h *Hero) GetLogTag() uint32 {
	return h.logTag
}

func (h *Hero) resetLogTag() {
	h.logTag = 0
}
func (h *Hero) updateLogTag(index int) {
	util.BitSetTrueUint32Ptr(&h.logTag, index)
}

func (h *Hero) CloneBattleHero(u *User) *Hero {
	cloneHero := &Hero{
		data:    h.data.Clone(),
		attrTag: h.attrTag,
		power:   h.power,
		logTag:  h.logTag,
		modify:  nil,
	}
	cloneHero.totalAttr = u.GetCloneHeroAttr()
	copy(cloneHero.totalAttr, h.totalAttr)
	if len(h.raisePSs) > 0 {
		cloneHero.raisePSs = make(map[RaisePSType][]uint64, len(h.raisePSs))
		for k, e := range h.raisePSs {
			cloneHero.raisePSs[k] = append(cloneHero.raisePSs[k], e...)
		}
	}
	if len(h.links) > 0 {
		cloneHero.links = make(map[uint32]uint32)
		for k, v := range h.links {
			cloneHero.links[k] = v
		}
	}
	return cloneHero
}

// 需要外面传的参数是可能会被修正的数据
func (h *Hero) newMember(pos uint32, level, stage, dress uint32, emblems []uint32,
	isSeasonAddHero bool, omniLinks map[uint32]uint32) *battle.Member {
	base := &battle.MemberBase{
		UniqID: h.data.Id, Pos: pos, SysID: h.data.SysId, Stage: stage,
		Star: h.data.Star, Level: level, Dress: dress, AwakenLevel: h.data.AwakenLevel,
		LogTag: h.logTag, RaisePSs: h.GetBattleRaisePsAttrs(), Power: h.power, GemInfo: h.GetBattleGems(),
		EmblemID: emblems, IsSeasonAddHero: isSeasonAddHero, OmniLinks: omniLinks, Links: h.links,
	}
	return battle.NewMember(base, h.totalAttr)
}

func (h *Hero) GetBattleGems() []*cl.Gem {
	gemInfo := make([]*cl.Gem, 0, len(h.data.Gems))
	for _, gem := range h.data.Gems {
		gemInfo = append(gemInfo, gem.Clone())
	}
	return gemInfo
}

func (h *Hero) GetBattleEmblemSysIDs(user *User) []uint32 {
	var emblemSysIDs []uint32
	if h.modify != nil && len(h.modify.emblems) > 0 {
		//有修正数据取修正数据
		for slot := goxml.EmblemPos1; slot <= goxml.EmblemPos4; slot++ {
			if len(h.modify.emblems) >= slot && h.modify.emblems[slot-1] != nil {
				emblemSysIDs = append(emblemSysIDs, h.modify.emblems[slot-1].Data.SysId)
			} else {
				emblemSysIDs = append(emblemSysIDs, 0)
			}
		}
	} else {
		emblemM := user.EmblemManager()
		for slot := goxml.EmblemPos1; slot <= goxml.EmblemPos4; slot++ {
			emblemId := h.data.Emblem[uint32(slot)]
			if emblemId != 0 {
				emblem := emblemM.Get(emblemId)
				if emblem != nil {
					emblemSysIDs = append(emblemSysIDs, emblem.Data.SysId)
				}
			} else {
				emblemSysIDs = append(emblemSysIDs, 0)
			}
		}
	}
	return emblemSysIDs
}

func (h *Hero) GetBattleLevelAndStage(user *User) (uint32, uint32) {
	if h.modify != nil {
		return h.modify.level, h.modify.stage
	}
	return h.data.Level, h.data.Stage
}

// 英雄是否穿戴了指定品质及以上的全套符文
// @param user *User
// @param rare uint32
// @return bool
func (h *Hero) IsWearAllExclusiveEmblem(user *User, rare uint32) bool {
	if rare == 0 {
		return false
	}

	emblems := h.GetAllEmblem()
	if len(emblems) < goxml.EmblemPosMax {
		return false
	}

	emblemM := user.EmblemManager()
	for _, v := range emblems {
		emblem := emblemM.Get(v)
		if emblem == nil {
			l4g.Errorf("IsWearAllExclusiveEmblem error, emblem not exist. uid:%d, emblem:%d", user.ID(), v)
			return false
		}

		emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if emblemInfo == nil {
			l4g.Errorf("IsWearAllExclusiveEmblem error, no find sys id. uid:%d, emblem:%d sysID:%d",
				user.ID(), v, emblem.Data.SysId)
			return false
		}

		if emblemInfo.Rare < rare {
			return false
		}

		if h.GetHeroSysID() != emblem.Data.AdditiveHero {
			return false
		}
	}
	return true
}

// GetHeroEmblemAffixType
// @Description: 获取英雄激活的符文词缀类型
// @receiver h
// @return uint32
func (h *Hero) GetHeroEmblemAffixType(user *User) uint32 {
	emblems := h.GetAllEmblem()
	if len(emblems) < goxml.EmblemPos4 {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
	}
	eM := user.EmblemManager()
	suitId := uint32(0)
	affixIdToNum := make(map[uint32]uint32, 4)
	heroInfo := goxml.GetData().HeroInfoM.Index(h.GetHeroSysID())
	if heroInfo == nil {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
	}
	for _, emblemId := range emblems {
		emblem := eM.Get(emblemId)

		emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if emblemInfo == nil {
			return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
		}
		if emblemInfo.Race != heroInfo.Race {
			return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
		}
		if suitId == 0 {
			suitId = emblemInfo.SuitSkill
		}
		if suitId != emblemInfo.SuitSkill {
			return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
		}
		if emblem.GetSuccinctAffixId() == 0 {
			return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
		}
		affixIdToNum[emblem.GetSuccinctAffixId()]++
	}

	suitInfo := goxml.GetData().EmblemSuitInfoM.Index(suitId)
	if suitInfo == nil {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
	}

	if affixIdToNum[suitInfo.Affix2] >= 4 { //nolint:mnd
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_ARCHAIC)
	}
	if affixIdToNum[suitInfo.Affix1]+affixIdToNum[suitInfo.Affix2] >= 4 { //nolint:mnd
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_ANCIENT)
	}
	return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
}

// 更新赛季羁绊属性
// @param user *User
// @param needCheckActivation bool 是否需要检查激活状态 - 跑测工具可通过此参数，控制是否激活赛季羁绊
func (h *Hero) addSeasonLinkAttr(user *User, needCheckActivation bool) {
	if needCheckActivation {
		if !user.SeasonLink().IsHeroSeasonLinkActivated(h.GetHeroSysID()) {
			return
		}
	}

	seasonLinkInfo := goxml.GetData().SeasonLinkInfoM.GetRecordBySeasonIdHeroId(user.GetSeasonID(), h.GetHeroSysID())
	if seasonLinkInfo == nil {
		l4g.Errorf("user %d addSeasonLinkAttr: HeroInfo not exist, seasonId %d heroSysId %d", user.ID(), user.GetSeasonID(), h.GetHeroSysID())
		return
	}
	h.AddLink(seasonLinkInfo.LinkId1, 1)

	if seasonLinkInfo.LinkId2 > 0 {
		h.AddLink(seasonLinkInfo.LinkId2, 1)
	}
}

// 跑测工具计算赛季羁绊
// @param user *User
func (h *Hero) AiCalcSeasonLink(user *User) map[uint32]uint32 {
	seasonLinkInfo := goxml.GetData().SeasonLinkInfoM.GetRecordBySeasonIdHeroId(user.GetSeasonID(), h.GetHeroSysID())
	if seasonLinkInfo == nil {
		l4g.Errorf("user:%d AiCalcSeasonLink: info not exist, season %d heroSysID %d",
			user.ID(), user.GetSeasonID(), h.GetHeroSysID())
		return nil
	}

	seasonLink := make(map[uint32]uint32)
	raisePSInfo1 := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(seasonLinkInfo.LinkId1, 1)
	if raisePSInfo1 == nil {
		l4g.Errorf("user:%d AiCalcSeasonLink: no raisePSInfo, linkId %d", user.ID(), seasonLinkInfo.LinkId1)
		return nil
	}
	seasonLink[raisePSInfo1.EffectParam1] += raisePSInfo1.EffectParam2

	if seasonLinkInfo.LinkId2 > 0 {
		raisePSInfo2 := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(seasonLinkInfo.LinkId2, 1)
		if raisePSInfo2 == nil {
			l4g.Errorf("user:%d AiCalcSeasonLink: no raisePSInfo, linkId %d", user.ID(), seasonLinkInfo.LinkId2)
			return nil
		}
		seasonLink[raisePSInfo2.EffectParam1] += raisePSInfo2.EffectParam2
	}
	return seasonLink
}

// getExclusiveSkillId
// @Description: 获取符文专属技能ID。根据传入的参数，判断激活哪个专属技能，返回对应的技能ID。一个也没有激活就返回0
// @receiver h
// @param exEmblemRedNum  英雄穿戴红品质专属符文数量
// @param exEmblemOrangeNum 英雄穿戴橙品质专属符文数量
// @param heroInfo
// @return uint32
func (h *Hero) getEmblemExclusiveSkillId(exEmblemRedNum, exEmblemOrangeNum uint32, heroInfo *goxml.HeroInfoExt) uint32 {
	//专属技能判断
	if exEmblemRedNum == goxml.EmblemExclusiveNumFour && heroInfo.EmblemExclusive3 > 0 {
		return heroInfo.EmblemExclusive3
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumThree {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumOne && heroInfo.EmblemExclusive2 > 0 {
			return heroInfo.EmblemExclusive2
		}
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumTwo {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumTwo && heroInfo.EmblemExclusive2 > 0 {
			return heroInfo.EmblemExclusive2
		}
	} else if exEmblemRedNum == goxml.EmblemExclusiveNumOne {
		if exEmblemOrangeNum == goxml.EmblemExclusiveNumThree && heroInfo.EmblemExclusive1 > 0 {
			return heroInfo.EmblemExclusive1
		}
	} else if exEmblemOrangeNum == goxml.EmblemExclusiveNumFour && heroInfo.EmblemExclusive1 > 0 {
		return heroInfo.EmblemExclusive1
	}
	return 0
}

func (h *Hero) setModifyLevelAndStage(level, stage uint32) {
	if h.modify == nil {
		h.modify = &HeroModify{}
	}
	h.modify.level = level
	h.modify.stage = stage
}

// 是否是缔约英雄
func (h *Hero) IsTagContract() bool {
	return h.getTag() == common.HERO_TAG_HT_CONTRACT
}

// 是否是赐福英雄
func (h *Hero) IsTagBlessed() bool {
	return h.getTag() == common.HERO_TAG_HT_BLESSED
}

// 是否是材料英雄
func (h *Hero) IsTagMarterial() bool {
	return h.getTag() == common.HERO_TAG_HT_MARTERIAL
}

// 是否属于战斗列表中的英雄
func (h *Hero) IsInBattleList() bool {
	return h.IsTagContract() || h.IsTagBlessed()
}

//func (h *Hero) GetAllSeasonJewelry() map[uint32]uint64 {
//	return h.data.SeasonJewelry
//}

//func (h *Hero) GetSeasonJewelryByPos(pos uint32) uint64 {
//	if h.data.SeasonJewelry == nil {
//		return 0
//	}
//	return h.data.SeasonJewelry[pos]
//}

func (h *Hero) WearSeasonJewelry(id uint64, pos uint32) {
	if h.data.SeasonJewelry == nil {
		h.data.SeasonJewelry = make(map[uint32]uint64)
	}
	h.data.SeasonJewelry[pos] = id
}

func (h *Hero) RemoveSeasonJewelry(pos []uint32) {
	if h.data.SeasonJewelry != nil {
		for _, v := range pos {
			delete(h.data.SeasonJewelry, v)
		}
	}
}

func (h *Hero) RemoveSeasonJewelryById(jid uint64) {
	for pos, id := range h.data.SeasonJewelry {
		if jid != id {
			continue
		}
		delete(h.data.SeasonJewelry, pos)
	}
}

func (h *Hero) RemoveAllSeasonJewelry() []uint64 {
	seasonJewelryIds := make([]uint64, 0, len(h.data.SeasonJewelry))
	for k, v := range h.data.SeasonJewelry {
		seasonJewelryIds = append(seasonJewelryIds, v)
		delete(h.data.SeasonJewelry, k)
	}

	return seasonJewelryIds
}

func (h *Hero) CanWearSeasonJewelry(rare uint32) uint32 {
	// 品质限制
	if rare != uint32(common.QUALITY_RED) && rare != uint32(common.QUALITY_ORANGE) {
		return uint32(ret.RET_SEASON_JEWELRY_HERO_WEAR_RARE_ERROR)
	}

	// 非缔约英雄/赐福英雄
	if !h.IsTagContract() && !h.IsTagBlessed() {
		return uint32(ret.RET_SEASON_JEWELRY_HERO_WEAR_TAG_ERROR)
	}

	return uint32(ret.RET_OK)
}

func (h *Hero) GetActiveLinkIds(heroInfo *goxml.HeroInfoExt, user *User) []uint32 {
	activeLinkIds := make([]uint32, 0, 4)
	activeLinkIds = append(activeLinkIds, heroInfo.Link1ID)
	activeLinkIds = append(activeLinkIds, heroInfo.Link2ID)

	if h.IsActiveLink3() {
		activeLinkIds = append(activeLinkIds, heroInfo.Link3ID)
	}

	if h.IsActiveLink4(user) {
		activeLinkIds = append(activeLinkIds, heroInfo.Link4ID)
	}

	return activeLinkIds
}

func (h *Hero) GetActiveLinkIdsWithChangeLink(heroInfo *goxml.HeroInfoExt, user *User) []uint32 {
	links := h.GetActiveLinkIds(heroInfo, user)
	if len(links) == 0 {
		l4g.Errorf("CheckWearObjects: hero active links is empty. hid:%d", h.GetHeroSysID())
		return nil
	}

	// 添加可变羁绊
	var addLinks []uint32
	for _, link := range links {
		isChangeLink, changeType := goxml.GetData().LinkInfoM.IsLinkTypeChange(link)
		if !isChangeLink {
			continue
		}
		changeLinks := goxml.GetData().LinkInfoM.GetLinksByChangeType(changeType, link)
		addLinks = append(addLinks, changeLinks...)
	}

	if len(addLinks) > 0 {
		links = append(links, addLinks...)
	}

	return links
}

func (h *Hero) GetSeasonSkillPower() uint32 {
	info := goxml.GetData().HeroInfoM.Index(h.GetHeroSysID())
	if info == nil {
		return 0
	}
	now := time.Now().Unix()
	currentSeasonId := goxml.GetCurrentSeasonID(goxml.GetData(), now)
	if currentSeasonId != info.SeasonIdForSkill {
		return 0
	}

	if info.SeasonSkill1 == 0 {
		return 0
	}

	levelInfo := goxml.GetData().SkillLevelInfoM.Index(info.SkillLevelID, h.GetStar())
	if levelInfo == nil {
		return 0
	}

	skill := goxml.GetData().SkillInfoM.GroupLevel(info.SeasonSkill1, levelInfo.SeasonSkill1Level)
	if skill == nil {
		return 0
	}
	return skill.Power
}

func (h *Hero) GetRareData() *cl.HeroRare {
	if h.data.Rare == nil {
		h.data.Rare = &cl.HeroRare{}
	}
	return h.data.Rare
}

func (h *Hero) GetRare() uint32 {
	return h.GetRareData().Rare
}

func (h *Hero) addStarAttr() {
	info := goxml.GetData().HeroInfoM.Index(h.GetHeroSysID())
	if info == nil {
		l4g.Errorf("HeroInfoM not exist. %d", h.GetHeroSysID())
		return
	}
	rare := info.Rare
	if h.GetRare() != 0 {
		rare = h.GetRare()
	}
	starInfo := goxml.GetData().HeroStarInfoM.Index(h.data.Star, rare, info.Race)
	links := starInfo.GetLink(info)
	for linkID, num := range links {
		h.AddLink(linkID, num)
	}
}

func (h *Hero) addRareUpAttr() {
	rareUp := h.GetRareData()
	if rareUp.RareLevel == 0 {
		return
	}
	heroRareUpInfo := goxml.GetData().HeroRareUpInfoM.GetIndexByHeroId(h.data.SysId)
	if heroRareUpInfo == nil {
		return
	}
	raisePSs := make([]uint64, 0, 0)
	for _, levelRecord := range heroRareUpInfo.GetLevelRecords() {
		if rareUp.RareLevel < levelRecord.Level {
			continue
		}
		if levelRecord.Skill != 0 {
			raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(levelRecord.Skill, 1)
			if raisePSInfo == nil {
				l4g.Errorf("RaisePassiveSkillInfoM not exist:%d", levelRecord.Skill)
				continue
			}
			raisePSs = append(raisePSs, raisePSInfo.ID)
		}
	}

	if len(raisePSs) > 0 {
		h.raisePSs[RaisePSTypeHeroRareUp] = append(h.raisePSs[RaisePSTypeHeroRareUp], raisePSs...)
	}
}
