package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

// ShopBuy ：商店购买接口；不同type,购买方式不同
type ShopBuy interface {
	CheckGoodAndCalcRes(Servicer, *User, uint32, uint32) (uint32, []*cl.Resource, []*cl.Resource)
	Update(uint32, uint32) bool
}

type ShopBuyRandom cl.Shop // 随机商店

func (s *ShopBuyRandom) CheckGoodAndCalcRes(srv Servicer, u *User, goodID, num uint32) (uint32, []*cl.Resource, []*cl.Resource) {
	randomGoodsInfo := goxml.GetData().ShopRandomGoodsInfoM.Index(goodID)
	if randomGoodsInfo == nil {
		l4g.Errorf("user: %d C2L_ShopBuy: randomGoodsInfo not exist. goodsID:%d", u.ID(), goodID)
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil, nil
	}
	if ret := s.checkGoodNum(u.ID(), goodID, num); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ShopBuy: good num invalid. goodsID:%d", u.ID(), goodID)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil
	}

	cost, award := s.calcRes(randomGoodsInfo, num)

	return uint32(cret.RET_OK), cost, award
}

func (s *ShopBuyRandom) Update(goodID, num uint32) bool {
	s.GoodsList[goodID] += num
	return true
}

func (s *ShopBuyRandom) checkGoodNum(uid uint64, goodID, num uint32) uint32 {
	boughtNum, exist := s.GoodsList[goodID]
	if !exist {
		l4g.Errorf("user: %d C2L_ShopBuy: good not exist. goodsID:%d", uid, goodID)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}

	if boughtNum == 0 && num == 1 { //存在，未购买，限购1个
		return uint32(cret.RET_OK)
	}

	l4g.Errorf("user: %d C2L_ShopBuy: buy num invalid. goodsID:%d,boughtNum:%d", uid, goodID, boughtNum)
	return uint32(cret.RET_CHECK_GOODS_FAILED)
}

func (s *ShopBuyRandom) calcRes(rInfo *goxml.ShopRandomGoodsInfoExt, num uint32) ([]*cl.Resource, []*cl.Resource) {
	cost := make([]*cl.Resource, 0, len(rInfo.Costs))
	award := make([]*cl.Resource, 0, len(rInfo.Awards))
	for _, v := range rInfo.Costs {
		cost = append(cost, goxml.GenSimpleResource(v.Type, v.Value, v.Count*num))
	}
	for _, v := range rInfo.Awards {
		award = append(award, goxml.GenSimpleResource(v.Type, v.Value, v.Count*num))
	}

	return cost, award
}

type ShopBuyRound cl.Shop // 轮次商店

func (s *ShopBuyRound) CheckGoodAndCalcRes(srv Servicer, u *User, goodID uint32, num uint32) (uint32, []*cl.Resource, []*cl.Resource) {
	info := goxml.GetData().ShopRegularGoodsInfoM.Index(goodID)
	if info == nil {
		l4g.Errorf("user: %d C2L_ShopBuy: regularGoodsInfo not exist. goodsID:%d", u.ID(), goodID)
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil, nil
	}
	if info.ShopId != s.SysId {
		l4g.Errorf("user: %d C2L_ShopBuy: shopID is different. infoShopID:%d, shopID:%d", u.ID(), info.ShopId, s.SysId)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil
	}
	if info.Round != s.Round {
		l4g.Errorf("user: %d C2L_ShopBuy: round is different. infoRound:%d, round:%d", u.ID(), info.Round, s.Round)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil
	}

	if !s.checkGoodOpenLevel(u, info) {
		l4g.Errorf("user: %d C2L_ShopBuy: level not match. level:%d minLv:%d maxLv:%d", u.ID(), u.Level(), info.MinLv, info.MaxLv)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil
	}

	boughtNum := s.GoodsList[info.Id]
	if !s.isEnoughGoodLeftNum(u.ID(), boughtNum, num, info) {
		l4g.Errorf("user: %d C2L_ShopBuy: good not enough. boughtNum:%d", u.ID(), boughtNum)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil
	}

	if !s.isOpenGoodSpecialLimit(u, info, srv) {
		l4g.Errorf("user: %d C2L_ShopBuy: good be limit. goodID:%d", u.ID(), goodID)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil
	}
	cost, award := s.calcRes(u.ID(), num, info)

	return uint32(cret.RET_OK), cost, award
}

func (s *ShopBuyRound) Update(goodID, num uint32) bool {
	info := goxml.GetData().ShopRegularGoodsInfoM.Index(goodID)
	if info == nil {
		return false
	}

	if goxml.ShopLimitType(info.BuyLimitType) == goxml.ShopLimitTypeNo {
		return false
	}
	s.GoodsList[goodID] += num

	return true
}

func (s *ShopBuyRound) isOpenGoodSpecialLimit(u *User, info *goxml.ShopRegularGoodsInfoExt, srv servicer) bool {
	shopGoodOpenI := GetShopGoodOpenI(info.ExtType, u, srv)
	if shopGoodOpenI == nil {
		return true
	}

	return shopGoodOpenI.IsShopGoodOpen(info, srv)
}

func (s *ShopBuyRound) checkGoodOpenLevel(user *User, info *goxml.ShopRegularGoodsInfoExt) bool {
	level := user.Level()
	passLevelCheck := true
	if info.MinLv > 0 {
		if level < info.MinLv || level > info.MaxLv {
			passLevelCheck = false
		}
	}

	return passLevelCheck
}

// 验证商品剩余数量：购买记录会定时刷新（轮次商店&固定商店），所以不需考虑限购类型
func (s *ShopBuyRound) isEnoughGoodLeftNum(uid uint64, boughtNum, num uint32, info *goxml.ShopRegularGoodsInfoExt) bool {
	if goxml.ShopLimitType(info.BuyLimitType) == goxml.ShopLimitTypeNo {
		return true
	}
	if info.BuyLimitCount < boughtNum {
		l4g.Errorf("user: %d C2L_ShopBuy: buy be limit. limitCount:%d boughtNum:%d", uid, info.BuyLimitCount, boughtNum)
		return false
	}

	return info.BuyLimitCount-boughtNum >= num
}

func (s *ShopBuyRound) calcRes(uid uint64, num uint32, info *goxml.ShopRegularGoodsInfoExt) ([]*cl.Resource, []*cl.Resource) {
	boughtNum := s.GoodsList[info.Id]
	exist, price := goxml.GetData().BuyPriceInfoM.GetPrice(info.CostGroup, boughtNum, num)
	if !exist {
		l4g.Errorf("user: %d C2L_ShopBuy: calcPrice err. costGroup:%d boughtNum:%d, num:%d", uid, info.CostGroup, boughtNum, num)
		return nil, nil
	}

	awards := make([]*cl.Resource, 0, len(info.Awards))
	for _, v := range info.Awards {
		award := &cl.Resource{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count * num,
		}
		if v.Type == uint32(common.RESOURCE_AVATAR) {
			avatarInfo := goxml.GetData().AvatarInfoM.Index(v.Value)
			if avatarInfo == nil {
				continue
			}
			//if avatarInfo.DurationType == goxml.AvatarTimeTypeDuration {
			//	award.Attrs = append(award.Attrs, &cl.Attr{
			//		Value: int64(avatarInfo.Duration),
			//	})
			//}
		}
		awards = append(awards, award)
	}

	if price == 0 {
		return nil, awards
	}
	return []*cl.Resource{goxml.GenSimpleResource(info.CostType, info.CostValue, price)}, awards
}
