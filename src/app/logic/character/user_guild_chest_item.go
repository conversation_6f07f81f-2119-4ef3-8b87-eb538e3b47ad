package character

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type UserGuildChestItem struct {
	owner *User
	data  map[uint64]*cl.UserGuildChestItem
}

func newUserGuildChestItem(user *User) *UserGuildChestItem {
	return &UserGuildChestItem{
		owner: user,
	}
}

func (gc *UserGuildChestItem) Owner() *User {
	return gc.owner
}

func (gc *UserGuildChestItem) load(data map[uint64]*cl.UserGuildChestItem) {
	gc.data = data
	if gc.data == nil {
		gc.data = make(map[uint64]*cl.UserGuildChestItem)
	}
}

func (gc *UserGuildChestItem) Flush2Client() ([]*cl.UserGuildChestItem, []uint64) {
	ret := make([]*cl.UserGuildChestItem, 0, len(gc.data))
	expiredItem := make([]uint64, 0, len(gc.data)/2+1)
	now := time.Now().Unix()
	var change bool
	for id, guildChestItem := range gc.data {
		if guildChestItem == nil {
			delete(gc.data, id)
			change = true
			continue
		}
		if now >= guildChestItem.ExpireTime {
			expiredItem = append(expiredItem, id)
			delete(gc.data, id)
			change = true
		}
		ret = append(ret, guildChestItem.Clone())
	}
	if change {
		gc.Save()
	}
	return ret, expiredItem
}

func (gc *UserGuildChestItem) DelItem(id uint64) {
	_, exist := gc.data[id]
	if exist {
		delete(gc.data, id)
		gc.Save()
	}
}

func (gc *UserGuildChestItem) DelActivate(chestDatas []*cl.GuildChestData) {
	var change bool
	for _, data := range chestDatas {
		if data == nil {
			continue
		}
		_, exist := gc.data[data.Id]
		if exist {
			delete(gc.data, data.Id)
			change = true
		}
	}
	if change {
		gc.Save()
	}
}

func (gc *UserGuildChestItem) Save() {
	gc.owner.dbUser.Module4.UserGuildChestItem = gc.data
	gc.owner.setSaveTag(saveTagModule4)
}

func (gc *UserGuildChestItem) Add(srv servicer, sysID, taskID uint32, now int64) {
	l4g.Debugf("UserGuildChestItem add is in")
	chestInfo := goxml.GetData().GuildChestInfoM.Index(sysID)
	if chestInfo == nil {
		l4g.Errorf("UserGuildChestItem add uid:%d sysID:%d get xml failed", gc.owner.ID(), sysID)
		return
	}
	itemId := srv.CreateUniqueID()
	userItem := &cl.UserGuildChestItem{
		Id:         itemId,
		ChestId:    sysID,
		ExpireTime: now + int64(chestInfo.ReceivePeriodOfValidity)*int64(util.DaySecs),
		TaskId:     taskID,
	}

	gc.data[itemId] = userItem
	gc.NotifyItem([]*cl.UserGuildChestItem{userItem})
	gc.Save()
}

func (gc *UserGuildChestItem) CanUse(itemId uint64, now int64) (*cl.UserGuildChestItem, bool) {
	item, exist := gc.data[itemId]
	if !exist {
		return item, false
	}
	l4g.Debugf("UserGuildChestItem CanUse item id:%d expired:%d now:%d", itemId, item.ExpireTime, now)
	return item, item.ExpireTime > now
}

func (gc *UserGuildChestItem) NotifyItem(items []*cl.UserGuildChestItem) {
	clientItems := make([]*cl.UserGuildChestItem, 0, len(items))

	for _, v := range items {
		clientItems = append(clientItems, v.Clone())
	}
	msg := &cl.L2C_UserGuildChestItemNotify{
		Ret:   uint32(cret.RET_OK),
		Items: clientItems,
	}

	gc.Owner().SendCmdToGateway(cl.ID_MSG_L2C_UserGuildChestItemNotify, msg)
}
