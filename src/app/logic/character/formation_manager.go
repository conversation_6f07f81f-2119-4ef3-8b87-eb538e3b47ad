package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/in/l2c"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"math"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type FormationM struct {
	u *User

	formations map[uint32]*cl.Formation
	//创建，修改，删除装备使用，定时保存使用
	changes map[uint32]*cl.Formation
	deletes map[uint32]struct{}
}

type SyncGstFormationCondition struct {
	HeroId     uint64
	ArtifactId uint32
}

func NewFormationM(user *User) *FormationM {
	return &FormationM{
		u: user,
		//formations: make(map[uint32]*cl.Formation),
		changes: make(map[uint32]*cl.Formation),
		deletes: make(map[uint32]struct{}),
	}
}

// 加载数据
func (fm *FormationM) Load(dbFormations map[uint32]*cl.Formation) {
	fm.formations = dbFormations
}

func (fm *FormationM) FlushSome(funcIds []uint32) map[uint32]*cl.Formation {
	ret := make(map[uint32]*cl.Formation, len(funcIds))
	for _, v := range funcIds {
		formation := fm.Get(v)

		if formation == nil {
			continue
		}
		ret[v] = formation.Clone()
	}
	return ret
}

func (fm *FormationM) Flush(funcIds []uint32) map[uint32]*cl.Formation {
	if len(funcIds) > 0 {
		return fm.FlushSome(funcIds)
	}
	return fm.FlushAll()
}

func (fm *FormationM) FlushAll() map[uint32]*cl.Formation {
	ret := make(map[uint32]*cl.Formation, len(fm.formations))
	for k, v := range fm.formations {
		ret[k] = v.Clone()
	}
	return ret
}

func (fm *FormationM) Get(id uint32) *cl.Formation {
	return fm.formations[id]
}

func (fm *FormationM) GetAll() map[uint32]*cl.Formation {
	return fm.formations
}

func (fm *FormationM) NewFormation(srv servicer, fid uint32, formation *cl.Formation) bool {
	if fm.formations == nil {
		fm.formations = make(map[uint32]*cl.Formation)
	}
	if formation == nil {
		formation = &cl.Formation{
			Id: fid,
		}
	}
	fm.formations[fid] = formation
	fm.changes[fid] = formation
	fm.updateDefenseFormationPower(srv, formation)
	return true
}

// 根据已存在的阵容，初始化阵容(复制一个阵容)
// tips: 多队需要的最少英雄数量，需调用方验证
// @param uint32 formFid 源阵容id
// @param uint32 toFid 目标阵容id
// @param int needTeamCnt 阵容的队伍数量
// @return bool
func (fm *FormationM) InitFormationByExistOne(srv servicer, fromFid, toFid uint32, needTeamCnt int) bool {
	f := fm.Get(fromFid)
	if f == nil {
		l4g.Errorf("FormationM -> InitFormationByExistOne. no formation, uid:%d fromFid:%d",
			fm.u.ID(), fromFid)
		return false
	}

	newFormation := f.Clone()
	newFormation.Id = toFid
	curTeamCnt := len(newFormation.Teams)
	if curTeamCnt > needTeamCnt {
		newFormation.Teams = newFormation.Teams[0:needTeamCnt]
	} else if curTeamCnt < needTeamCnt {
		teams := fm.makeNewTeam(newFormation.Teams, needTeamCnt-curTeamCnt)
		newFormation.Teams = append(newFormation.Teams, teams...)
	}
	fm.formations[toFid] = newFormation
	fm.changes[toFid] = newFormation

	if goxml.GetData().FormationInfoM.IsNonemptyFormation(toFid) {
		power := fm.u.CalFormationPower(toFid)
		fm.u.setFormationPower(toFid, power)
	}
	return true
}

// 根据现有队伍信息，生成其他队伍信息（多队玩法补充阵容）
// @param []*cl.FormationTeamInfo curTeams 玩法现有队伍信息
// @param int addCount 要补充生成的队伍数量
// @return []*cl.FormationTeamInfo 生成的队伍信息
func (fm *FormationM) makeNewTeam(curTeams []*cl.FormationTeamInfo, addCount int) []*cl.FormationTeamInfo {
	existHids := make([]uint64, 0, FormationMaxPos)
	for _, v := range curTeams {
		for _, data := range v.Info {
			existHids = append(existHids, data.Hid)
		}
	}

	heroM := fm.u.HeroManager()
	teams := make([]*cl.FormationTeamInfo, 0, addCount)
	for i := 0; i < addCount; i++ {
		team := &cl.FormationTeamInfo{
			Info: make([]*cl.FormationInfo, 0, 5), //nolint:mnd
		}

		for pos := uint32(1); pos <= FormationMaxPos; pos++ {
			hero := heroM.FindOneBattleHero(existHids)
			if hero == nil {
				l4g.Errorf("FormationM.makeNewTeam. hero is nil, uid:%d existHids:%v",
					fm.u.ID(), existHids)
				continue
			}

			team.Info = append(team.Info, &cl.FormationInfo{
				Pos: pos,
				Hid: hero.GetHid(),
			})
			existHids = append(existHids, hero.GetHid())
		}

		teams = append(teams, team)
	}
	return teams
}

func (fm *FormationM) Save(msg *r2l.OpFormations) bool {
	success := false
	changes := len(fm.changes)
	if changes > 0 {
		msg.Changes = make([]*cl.Formation, 0, changes)
		for id, v := range fm.changes {
			msg.Changes = append(msg.Changes, v.Clone())
			delete(fm.changes, id)
		}
		success = true
	}
	deletes := len(fm.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint32, 0, deletes)
		for id := range fm.deletes {
			msg.Deletes = append(msg.Deletes, id)
			delete(fm.deletes, id)
		}
		success = true
	}
	return success
}

func (fm *FormationM) GetAllFormationsID() []uint32 {
	formationIds := make([]uint32, 0, len(fm.formations))
	for k := range fm.formations {
		formationIds = append(formationIds, k)
	}
	return formationIds
}

// 获取玩家全部不能为空的阵容
func (fm *FormationM) GetCannotEmptyFormations() map[uint32]*cl.Formation {
	fids := goxml.GetData().FormationInfoM.GetNonemptyFids()
	if len(fids) == 0 {
		return nil
	}

	result := make(map[uint32]*cl.Formation)
	for id, formation := range fm.GetAll() {
		for _, fid := range fids {
			if fid == id {
				result[id] = formation
				break
			}
		}
	}
	return result
}

// 移出阵容中的英雄,除了被吃掉的，还有因为不是水晶英雄了要从阵容里移除掉
func (fm *FormationM) removeHeroesFromFormation(formation *cl.Formation, leaveHids []uint64) {
	if len(leaveHids) == 0 {
		return
	}

	isChanged := false
	noHeroTeams := make([]int, 0, len(formation.Teams))

	formationLeftHero := make([]uint64, 0, FormationMaxPos) // 还在阵容上的英雄，autoFormation用

	for index, team := range formation.Teams {
		for i := 0; i < len(team.Info); i++ {
			heroInfo := team.Info[i]
			leave := false
			for _, leaveHid := range leaveHids {
				if heroInfo.Hid == leaveHid {
					leave = true
				}
			}
			if leave {
				team.Info = append(team.Info[:i], team.Info[i+1:]...)
				isChanged = true
				i--
				continue
			}
			formationLeftHero = append(formationLeftHero, heroInfo.Hid)
		}
		if len(team.Info) == 0 {
			noHeroTeams = append(noHeroTeams, index)
		}
	}

	if len(noHeroTeams) <= 0 {
		if isChanged {
			fm.changes[formation.Id] = formation
			fm.u.SendFormationToClient([]uint32{formation.Id})
		}
		return
	}

	//  ************ 以下是有队伍为空时的处理 ************

	if goxml.GetData().FormationInfoM.IsNonemptyFormation(formation.Id) {
		fm.autoFormation(formation, noHeroTeams, formationLeftHero)
	}
	fm.checkDeleteFormationNilTeams(formation)

	if len(formation.Teams) == 0 {
		delete(fm.formations, formation.Id)
		fm.deletes[formation.Id] = struct{}{}
	} else {
		fm.changes[formation.Id] = formation
	}

	//推给客户端
	fm.u.SendFormationToClient([]uint32{formation.Id})
}

// ArtifactLeaveFormations : 神器离开阵容
func (fm *FormationM) ArtifactLeaveFormations(sysID uint32) {
	for formationID, formation := range fm.formations {
		var isLeave bool // 是否离开阵容
		for _, team := range formation.Teams {
			for index, artifact := range team.Artifacts {
				if artifact.Aid != sysID {
					continue
				}
				isLeave = true
				team.Artifacts = append(team.Artifacts[:index], team.Artifacts[index+1:]...)
				fm.changes[formationID] = formation
				break
			}
			if isLeave { // 修改神器布阵的pos，pos从1开始，需要连续
				for index, artifact := range team.Artifacts {
					artifact.Pos = uint32(index) + 1
				}
			}
		}
	}
}

func (fm *FormationM) updateDefenseFormationPower(srv servicer, formation *cl.Formation) {
	if !fm.isRealTimeUpdateFormation(formation.Id) {
		return
	}

	oldPower := fm.u.GetDefensePower(formation.Id)
	power := fm.u.CalFormationPower(formation.Id)
	if power != oldPower {
		fm.u.FireCommonEvent(srv.EventM(), event.IeDefensePowerChange, uint64(power), formation.Id)
		fm.u.setFormationPower(formation.Id, power)
	}

}

func (fm *FormationM) isRealTimeUpdateFormation(formationID uint32) bool {
	for _, id := range goxml.GetData().FormationInfoM.GetRealTimeUpdateFids() {
		if id == formationID {
			return true
		}
	}
	return false
}

func (fm *FormationM) autoFormation(formation *cl.Formation, noHeroTeamsIndex []int, existHids []uint64) {
	formationInfo := goxml.GetData().FormationInfoM.Index(formation.Id)
	if formationInfo == nil {
		l4g.Errorf("user:%d autoFormation: formationInfo is nil. fid:%d", fm.u.ID(), formation.Id)
		return
	}

	heroM := fm.u.HeroManager()
	for _, teamIndex := range noHeroTeamsIndex {
		team := fm.u.GetFormationTeam(formation.Id, teamIndex)
		if team == nil {
			continue
		}

		hero := heroM.FindOneBattleHero(existHids)
		if hero == nil {
			team.Info = nil
		} else {
			team.Info = make([]*cl.FormationInfo, 1)
			team.Info[0] = &cl.FormationInfo{
				Pos: 1,
				Hid: hero.GetHid(),
			}
			existHids = append(existHids, hero.GetHid())
		}
	}
}

type FormationLinkInfo struct {
	ActivedLinks []*goxml.LinkSkillInfoExt
	HeroLinkInfo map[uint32]map[uint32]uint32 //pos => linkId => num
}

func (fm *FormationM) GetFormationLinkInfo(formationID uint32, index int) *FormationLinkInfo {
	team := fm.u.GetFormationTeam(formationID, index)
	if team == nil {
		l4g.Errorf("user: %d GetFormationLinkInfo: no team, formationID:%d, index:%d",
			fm.u.ID(), formationID, index)
		return nil
	}

	linkList := make(map[uint32]uint32) //激活羁绊列表
	changeTypeLinkNum := make(map[uint32]uint32)
	posChangeTypeNum := make(map[uint32]map[uint32]uint32) // pos changeType num
	result := &FormationLinkInfo{
		HeroLinkInfo: make(map[uint32]map[uint32]uint32),
	}
	heroM := fm.u.HeroManager()
	for _, formationInfo := range team.Info {
		hid := formationInfo.Hid
		pos := formationInfo.Pos
		var linkInfo map[uint32]uint32
		if hid > math.MaxUint32 {
			if hero := heroM.Get(hid); hero != nil {
				linkInfo = hero.GetLinks(fm.u)
			}
		} else if hid > 0 {
			monsterInfo := goxml.GetData().MonsterInfoM.Index(uint32(hid))
			if monsterInfo == nil {
				l4g.Errorf("user: %d GetFormationLinkInfo: monsterInfo nil. monsterID: %d", fm.u.ID(), hid)
				continue
			}
			linkInfo = monsterInfo.LinkInfo
		}

		if _, exist := result.HeroLinkInfo[pos]; !exist {
			result.HeroLinkInfo[pos] = make(map[uint32]uint32)
		}

		// 计算羁绊数据，找到需要自动激活的羁绊
		for linkID, num := range linkInfo {
			// 当前羁绊属可变羁绊，且当前全能英雄选择自动时，认定为自动激活可变羁绊
			isChangeLink, changeType := goxml.GetData().LinkInfoM.IsLinkTypeChange(linkID)
			fixLink := formationInfo.Links[changeType]
			if isChangeLink && fixLink == 0 {
				changeTypeLinkNum[changeType] += num

				if _, exist := posChangeTypeNum[pos]; !exist {
					posChangeTypeNum[pos] = make(map[uint32]uint32)
				}
				posChangeTypeNum[pos][changeType] += num
			} else {
				// 全能英雄自选羁绊有值时，认定为激活自选羁绊
				if fixLink > 0 {
					// 验证所选羁绊是否可用
					if !goxml.GetData().LinkInfoM.IsAvailableOmniLink(fixLink, fm.u.GetSeasonID()) {
						continue
					}
					linkID = fixLink
				}
				linkList[linkID] += num
				result.HeroLinkInfo[pos][linkID] += num
			}
		}
	}

	// 计算自动激活的可变羁绊
	if len(changeTypeLinkNum) > 0 {
		finalLinks := make(map[uint32]uint32)
		topLinks := goxml.GetData().LinkInfoM.CalcTopLinks(linkList)
		// 计算出可变羁绊变成的羁绊
		for typ, count := range changeTypeLinkNum {
			id := topLinks[typ]
			if id == 0 {
				continue
			}
			linkList[id] += count
			finalLinks[typ] = id
		}

		// 将变成的羁绊赋值给对应的位置的英雄
		for pos, changTypeNum := range posChangeTypeNum {
			for changeType := range changTypeNum {
				changeLink, exist := finalLinks[changeType]
				if !exist {
					continue
				}
				result.HeroLinkInfo[pos][changeLink] += 1
			}
		}
	}

	numReduceLinkIds := fm.u.TalentTree().getLinkActiveNumReduceLinkIds()
	result.ActivedLinks = goxml.GetData().LinkSkillInfoM.GetActiveLinks(linkList, formationID, true, numReduceLinkIds)
	l4g.Debugf("user:%d GetFormationLinkInfo, linkList:%+v, formationID:%d, numReduceLinkIds:%+v, result:%+v",
		fm.u.ID(), linkList, formationID, numReduceLinkIds, result)
	return result
}

// 获取阵容中指定羁绊激活数量
// @param formationID 阵容id
// @param linkID 羁绊id
// @param index 队伍id
// @return 羁绊激活数量
func (fm *FormationM) GetLinkActiveNum(formationID, linkID uint32, index int) uint32 {
	data := fm.GetFormationLinkInfo(formationID, index)
	if data == nil {
		l4g.Errorf("user %d GetLinkActiveNum: no formationLinkInfo, formationID:%d teamIndex:%d",
			fm.u.ID(), formationID, index)
		return 0
	}

	l4g.Debugf("user:%d GetLinkActiveNum, formationID:%d, linkID:%d, index:%d",
		fm.u.ID(), formationID, linkID, index)
	for _, v := range data.ActivedLinks {
		if v.LinkId == linkID {
			return v.Num
		}
	}
	return 0
}

func (fm *FormationM) Delete(formationID uint32) {
	if fm.Get(formationID) == nil {
		return
	}

	delete(fm.formations, formationID)
	fm.deletes[formationID] = struct{}{}
	delete(fm.changes, formationID)
}

func (fm *FormationM) OnAllRiteCleard() {
	for formationID, formation := range fm.formations {
		if formation == nil {
			continue
		}
		isChanged := false
		for _, team := range formation.Teams {
			if team == nil || team.RiteInfo == nil {
				continue
			}
			team.RiteInfo = nil
			isChanged = true
		}
		if isChanged {
			fm.changes[formationID] = formation
			//TODO:是否需要给客户端发推送
		}
	}
}

// 根据英雄战力，设置阵容队伍
// @param servicer srv
// @param uint32 fid 阵容id
// @param int teamCount 队伍数量
func (fm *FormationM) SetFormationByPower(srv servicer, fid uint32, teamCount int) {
	heroM := fm.u.HeroManager()
	maxCount := teamCount * int(FormationMaxPos)
	hidList := heroM.CalcHeroIDListByPowerInBattleHeroes(maxCount)

	tmpTeam := make(map[int][]uint64, teamCount) //map[int][]uint64 队伍数量
	for k, hid := range hidList {
		teamIndex := k % teamCount
		if _, exist := tmpTeam[teamIndex]; !exist {
			tmpTeam[teamIndex] = make([]uint64, 0, int(FormationMaxPos))
		}
		tmpTeam[teamIndex] = append(tmpTeam[teamIndex], hid)

		if k >= maxCount-1 {
			break
		}
	}

	if len(tmpTeam) < 1 {
		l4g.Errorf("user: %d SetFormationByPower: no tmpTeam, fid:%d", fm.u.ID(), fid)
		return
	}

	formation := &cl.Formation{
		Id:    fid,
		Teams: make([]*cl.FormationTeamInfo, len(tmpTeam)),
	}
	for index, hids := range tmpTeam {
		lockPosList := goxml.GetData().FormationInfoM.GetFormationTeamLockPosList(fid, index)
		cap := len(hids) - len(lockPosList)
		if cap <= 0 {
			cap = 1
		}
		formation.Teams[index] = &cl.FormationTeamInfo{
			Info: make([]*cl.FormationInfo, 0, cap),
		}

		for k, hid := range hids {
			// 跳过锁定pos
			pos := uint32(k + 1)
			if util.InUint32s(lockPosList, pos) {
				continue
			}

			tmp := &cl.FormationInfo{
				Pos: pos,
				Hid: hid,
			}
			formation.Teams[index].Info = append(formation.Teams[index].Info, tmp)
		}
	}
	fm.NewFormation(srv, fid, formation)
}

// DeleteHeroFromSeasonFuncFormation
// @Description: 赛季玩法的阵容中删除英雄
// @receiver fm
func (fm *FormationM) DeleteHeroFromSeasonFuncFormation(srv servicer, leaveHids []uint64) {
	seasonInfo := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(time.Now().Unix())
	if seasonInfo == nil {
		return
	}

	for _, id := range seasonInfo.Functions {
		for _, info := range goxml.GetData().FormationInfoM.GetFuncFormationInfos(id) {
			if info == nil {
				continue
			}
			formation := fm.u.GetFormation(info.Id)
			if formation == nil {
				l4g.Debugf("user:%d DeleteHeroFromSeasonFuncFormation: getFormation is nil. fid:%d", fm.u.ID(), info.Id)
				continue
			}
			fm.removeHeroesFromFormation(formation, leaveHids)
		}
	}
}

func (fm *FormationM) checkDeleteFormationNilTeams(formation *cl.Formation) {
	if formation == nil {
		return
	}

	nilNum := 0
	newTeams := make([]*cl.FormationTeamInfo, 0, len(formation.Teams))
	for _, team := range formation.Teams {
		if len(team.Info) == 0 {
			newTeams = append(newTeams, &cl.FormationTeamInfo{})
			nilNum++
			continue
		}
		newTeams = append(newTeams, team)
	}
	if nilNum >= len(newTeams) {
		formation.Teams = nil // 所有队伍都空了
		return
	}
	formation.Teams = newTeams
}

func (fm *FormationM) Convert2GSTTeam(teams []*cl.FormationTeamInfo) []*l2c.GSTSignTeam {
	ret := make([]*l2c.GSTSignTeam, 0, len(teams))
	for index, team := range teams {
		gstTeam := &l2c.GSTSignTeam{
			Heros:     make([]*cl.GSTTeamHeroInfo, 0, len(teams)),
			TeamIndex: uint32(index),
		}
		gstTeam.Power = fm.u.CalFormationTeamPower(uint32(common.FORMATION_ID_FI_GST), team)
		for _, hero := range team.Info {
			heroData := fm.u.HeroManager().Get(hero.Hid)
			if heroData == nil {
				l4g.Errorf("")
				return nil
			}
			gstHero := &cl.GSTTeamHeroInfo{
				Pos:   hero.Pos,
				Star:  heroData.GetStar(),
				SysId: heroData.GetHeroSysID(),
			}
			gstTeam.Heros = append(gstTeam.Heros, gstHero)
		}

		ret = append(ret, gstTeam)
	}

	return ret
}

func (fm *FormationM) NeedSyncGSTFormationToCross(cond *SyncGstFormationCondition) bool {
	var ret bool
	formation := fm.Get(uint32(common.FORMATION_ID_FI_GST))
	if formation == nil {
		return ret
	}
	for _, team := range formation.Teams {
		if team != nil {
			if cond.HeroId > 0 {
				for _, Info := range team.Info {
					if cond.HeroId == Info.Uid {
						return true
					}
				}
			}
			if cond.ArtifactId > 0 {
				for _, artifact := range team.Artifacts {
					if cond.ArtifactId == artifact.Aid {
						return true
					}
				}
			}
		}
	}

	return ret
}

func (fm *FormationM) InitSeasonArenaFormation(srv servicer, formationIds []uint32) {
	var needInit bool
	for def := range goxml.SeasonArenaDefender2Attack {
		_, exit := fm.formations[def]
		if !exit {
			needInit = true
			break
		}
	}
	if !needInit {
		return
	}

	heroM := fm.u.HeroManager()
	teamMaxCount := goxml.GetData().SeasonArenaDivisionInfoM.GetMaxTeamCount()
	maxCount := int(teamMaxCount) * int(FormationMaxPos)
	//todo  战斗英雄列表里没有7个人怎么办？？？
	hidList := heroM.CalcHeroIDListByPowerInBattleHeroes(maxCount)
	if len(hidList) < int(teamMaxCount) {
		teamMaxCount = uint32(len(hidList))
	}
	l4g.Debugf("user: %d InitSeasonArenaFormation: finish seasonArena formation, heroLen:%d hidList: %v", fm.u.ID(), len(hidList), hidList)

	tmpTeam := fm.CreateTemporaryTeam(teamMaxCount, hidList)
	if len(tmpTeam) == 0 {
		l4g.Errorf("user: %d initSeasonArenaFormation: no tmpTeam", fm.u.ID())
		return
	}

	fm.InitFormationsWithTemporaryTeam(srv, formationIds, tmpTeam)
}

func (fm *FormationM) CreateTemporaryTeam(teamMaxCount uint32, hidList []uint64) map[int][]uint64 {
	tmpTeam := make(map[int][]uint64, teamMaxCount) //队伍下标，英雄
	for i := 0; i < int(teamMaxCount); i++ {
		if _, exist := tmpTeam[i]; !exist {
			tmpTeam[i] = make([]uint64, 0, int(FormationMaxPos))
		}
		tmpTeam[i] = append(tmpTeam[i], hidList[i])
	}
	return tmpTeam
}

func (fm *FormationM) InitFormationsWithTemporaryTeam(srv servicer, formations []uint32, tmpTeam map[int][]uint64) {
	for _, formationId := range formations {
		if _, exist := fm.formations[formationId]; exist {
			continue
		}

		ok, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(formationId)
		if !ok {
			l4g.Errorf("user: %d InitFormationsWithTmpTeam: GetTeamNum failed, fid:%d", fm.u.ID(), formationId)
			continue
		}
		if len(tmpTeam) < teamNum {
			teamNum = len(tmpTeam)
		}

		formation := &cl.Formation{
			Id:    formationId,
			Teams: make([]*cl.FormationTeamInfo, teamNum),
		}

		for i := 0; i < teamNum; i++ {
			formation.Teams[i] = &cl.FormationTeamInfo{
				Info: make([]*cl.FormationInfo, 0, len(tmpTeam[i])),
			}

			for k, hid := range tmpTeam[i] {
				tmp := &cl.FormationInfo{
					Pos: uint32(k + 1),
					Hid: hid,
				}
				formation.Teams[i].Info = append(formation.Teams[i].Info, tmp)
			}
		}
		fm.NewFormation(srv, formationId, formation)
		fm.u.SendFormationToClient([]uint32{formation.Id})
	}
}

func (fm *FormationM) CalcSeasonArenaFormationPower() {
	for def := range goxml.SeasonArenaDefender2Attack {
		formation := fm.Get(def)
		if formation == nil {
			continue
		}
		defPower := fm.u.CalFormationPower(def)
		fm.u.SetDefensePower(def, defPower)
	}
}

func (fm *FormationM) ReCalcSeasonDefFormation() {
	if !fm.u.dbUser.Module6.SeasonArenaCalcFormationPower {
		fm.CalcSeasonArenaFormationPower()
		fm.u.dbUser.Module6.SeasonArenaCalcFormationPower = true
		fm.u.setSaveTag(saveTagModule6)
	}
}

func (fm *FormationM) GetFormationSysHeroes(formationID uint32) map[uint32]struct{} {
	ret := make(map[uint32]struct{})
	formation, exist := fm.formations[formationID]
	if !exist {
		return ret
	}
	for _, team := range formation.Teams {
		if team == nil {
			continue
		}
		for _, hero := range team.Info {
			if hero == nil {
				continue
			}

			heroData := fm.u.HeroManager().Get(hero.Hid)
			if heroData == nil {
				continue
			}

			ret[heroData.data.SysId] = struct{}{}
		}
	}
	return ret
}
