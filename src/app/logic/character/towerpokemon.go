package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type TowerPokemon struct {
	owner *User
	data  *cl.TowerPokemon // db-save-check
}

func newTowerPokemon(user *User) *TowerPokemon {
	return &TowerPokemon{
		owner: user,
	}
}

func (t *TowerPokemon) Owner() *User {
	return t.owner
}

func (t *TowerPokemon) load(data *cl.TowerPokemon) {
	t.data = data
	t.customInit()
}

func (t *TowerPokemon) customInit() {
	if t.data == nil {
		t.data = &cl.TowerPokemon{}
	}
	if t.data.TaskProgress == nil {
		t.data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if t.data.TaskReceived == nil {
		t.data.TaskReceived = make(map[uint32]bool)
	}
	if t.data.BuyGoods == nil {
		t.data.BuyGoods = make(map[uint32]bool)
	}
}

func (t *TowerPokemon) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	t.resetData()
	t.customInit()
}

func (t *TowerPokemon) resetData() {
	if t.data == nil {
		return
	}
	t.data = nil
	t.Save()
}

func (t *TowerPokemon) OnSeasonEnd(srv servicer, needSettleAward bool) {
	if needSettleAward {
		t.SendTaskReward(srv)
	}
	t.resetData()
	t.customInit()
}

func (t *TowerPokemon) Save() {
	if t.Owner().dbUser.Module8.TowerPokemon == nil {
		t.Owner().dbUser.Module8.TowerPokemon = t.data
	}
	t.Owner().setSaveTag(saveTagModule8)
}

func (t *TowerPokemon) Flush() *cl.TowerPokemon {
	return t.data.Clone()
}

func (t *TowerPokemon) GetDungeonId() uint32 {
	return t.data.DungeonId
}

func (t *TowerPokemon) SetDungeonId(dungeonId uint32) {
	t.data.DungeonId = dungeonId
	t.Save()
}

func (t *TowerPokemon) IsRecvAward(taskId uint32) bool {
	return t.data.TaskReceived[taskId]
}

func (t *TowerPokemon) SetRecvAward(taskIds []uint32) {
	for _, taskId := range taskIds {
		t.data.TaskReceived[taskId] = true
	}
	t.Save()
}

func (t *TowerPokemon) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo, user *User) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return user.CalcTaskProgress(taskTypeInfo)
	}
	return t.data.TaskProgress[taskTypeInfo.Id]
}

func (t *TowerPokemon) OnTowerPokemonEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	if !t.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_TOWER_POKEMON), srv) {
		return
	}
	progressMap, change := t.owner.TaskTypeOnEvent(t.data.TaskProgress, event, progress, values)
	if change {
		t.Save()
		t.sendTaskProgress(progressMap)
	}
}

// 逻辑与个人BOSS (mm *MirageM) LinkExtraSkills 相同
func (t *TowerPokemon) LinkExtraSkills(altRaisePS *battle.AltRaisePS, formationID common.FORMATION_ID, fightEffect uint32, teamIndex int) {
	formationLinkInfo := t.owner.FormationManager().GetFormationLinkInfo(uint32(formationID), teamIndex)
	if formationLinkInfo == nil {
		l4g.Errorf("u:%d towerSeason LinkExtraSkills get GetFormationLinkInfo failed formation:%d team index:%d", t.owner.ID(), formationID, teamIndex)
		return
	}

	effects := goxml.GetData().TowerPokemonFightEffectInfoM.Group(fightEffect)
	if effects == nil {
		l4g.Errorf("u:%d towerSeason LinkExtraSkills get TowerPokemonFightEffectInfoM fightEffect:%d failed", t.owner.ID(), fightEffect)
		return
	}
	matchEffects := make(map[uint32]uint32) // key : 效果id, value: num
	// 计算满足条件的效果人数
	for _, links := range formationLinkInfo.HeroLinkInfo {
		for _, effect := range effects {
			_, exist1 := links[effect.Link1]
			_, exist2 := links[effect.Link2]
			if exist1 && exist2 {
				matchEffects[effect.Id] += 1
			} else if exist1 || exist2 {
				matchEffects[effect.Id] += 1
			}
		}
	}

	pos2RaisePSs := make(map[uint32][]uint64)
	// 根据效果人数获取ps skill
	for _, effect := range effects {
		if matchEffects[effect.Id] < effect.Num {
			continue
		}
		pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], effect.Skills...)
	}

	altRaisePS.AltAttack(pos2RaisePSs)
}

func (t *TowerPokemon) sendTaskProgress(progressMap map[uint32]*cl.TaskTypeProgress) {
	t.owner.SendCmdToGateway(cl.ID_MSG_L2C_TowerPokemonUpdateTask, &cl.L2C_TowerPokemonUpdateTask{
		Ret:      uint32(ret.RET_OK),
		Progress: progressMap,
	})
}

func (t *TowerPokemon) IsBuyGoods(goodID uint32) bool {
	return t.data.BuyGoods[goodID]
}

func (t *TowerPokemon) SetBuyGoods(goodID uint32) {
	t.data.BuyGoods[goodID] = true
	t.Save()
}

func (t *TowerPokemon) CheckTaskAward() bool {
	season := t.owner.GetSeasonID()
	if season == 0 {
		return false
	}
	if len(t.data.TaskProgress) > 0 {
		for _, taskInfo := range goxml.GetData().TowerPokemonTaskInfoM.SeasonTasks(season) {
			if t.IsRecvAward(taskInfo.Id) {
				continue
			}
			taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
			if taskTypeInfo == nil {
				continue
			}
			progress := t.GetTaskProgress(taskTypeInfo, t.owner)
			if !t.owner.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
				continue
			}
			return true
		}
	}
	return false
}

func (t *TowerPokemon) CheckShopAward() bool {
	dungeonID := t.GetDungeonId()
	if dungeonID == 0 {
		return false
	}
	for _, goodsInfo := range goxml.GetData().TowerPokemonGoodsInfoM.Datas {
		if dungeonID < goodsInfo.Floor {
			continue
		}
		if t.IsBuyGoods(goodsInfo.Id) {
			continue
		}
		return true
	}

	return false
}

func (t *TowerPokemon) SendTaskReward(srv servicer) {
	season := t.owner.GetSeasonID()
	if season == 0 {
		return
	}
	totalAward := make([]*cl.Resource, 0)
	if len(t.data.TaskProgress) > 0 {
		for _, taskInfo := range goxml.GetData().TowerPokemonTaskInfoM.SeasonTasks(season) {
			if t.IsRecvAward(taskInfo.Id) {
				continue
			}
			taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
			if taskTypeInfo == nil {
				continue
			}
			progress := t.GetTaskProgress(taskTypeInfo, t.owner)
			if !t.owner.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
				continue
			}
			for _, award := range taskInfo.ClRes {
				totalAward = append(totalAward, award.Clone())
			}
		}
	}
	if len(totalAward) > 0 {
		seasonInfo := goxml.GetData().SeasonInfoM.Index(season)
		if seasonInfo == nil {
			l4g.Error("u:%d towerPokemon SendTaskReward get season info failed, seasonID:%d", t.owner.ID(), season)
			return
		}
		totalAward = MergeResources(totalAward)
		TowerPokemonTaskMail(srv, totalAward, t.owner, seasonInfo.StartTm)
	}
}
