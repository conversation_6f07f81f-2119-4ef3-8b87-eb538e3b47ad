package character

import (
	"app/cross/activity"
	"app/goxml"
	"app/logic/battle"
	"app/protos/in/db"
	"app/protos/in/gm"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/bt"
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"strconv"
	"strings"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/platform/proto/da"

	l4g "github.com/ivanabc/log4go"
	jsoniter "github.com/json-iterator/go"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

func JSONMarshal(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err == nil {
		return util.String(bytes), nil
	}
	l4g.Errorf("json marshal error:%v %v", err, v)
	return "", err
}

type Extra struct {
	Os       uint32 `json:"os"`       //完美->平台id 1-iOS越狱 2-Android 3-iOS官方 4-windows或win10 5-MacOs 6-安卓TV 7-安卓TV单机游戏 8-WP
	Platform string `json:"platform"` //完美->渠道缩写，可以从已接入的SDK日志中的Paytype字段返回值 如: GooglePlay
	Mediaid  string `json:"mediaid"`  //完美->子渠道或cps包渠道缩写 如: GooglePlay_3
	Language string `json:"language"` //完美->客户端当前语种，由客户端sdk提供
	Udid     string `json:"udid"`     //完美->由客户端sdk提供
	GmTag    string `json:"gm_tag"`   //完美->0为正常用户，1为通过gm设置的标记用户
	Ver      string `json:"ver"`      //完美->客户端版本号

}

type heroEmblem struct {
	heroSysID uint32 //英雄系统id
	emblemID  uint64 //符文唯一id
}

// 通过阵容id，获取日志所需阵容数据
//
//nolint:funlen
func (u *User) getLogFormationData(fid uint32) *log.Formation {
	formationData := u.GetFormation(fid)
	if formationData == nil || len(formationData.Teams) == 0 {
		l4g.Errorf("user: %d getLogFormationData: formationData is nil. formationID:%d", u.ID(), fid)
		return nil
	}

	ret := &log.Formation{
		Teams: make([]*log.Team, 0, len(formationData.Teams)),
	}
	for i := 0; i < len(formationData.Teams); i++ {
		team := formationData.Teams[i]
		if team == nil {
			l4g.Errorf("user: %d, getLogFormationData: no formation, fid:%d", u.ID(), fid)
			return nil
		}

		//nolint:mnd
		logTeam := &log.Team{
			Heroes:    make([]*log.Hero, 0, 5),
			Emblems:   make([]*log.Emblem, 0, 4),
			Artifacts: make([]*log.Artifact, 0, 3),
			Links:     make([]*log.Link, 0, 2),
			Remains:   make([]uint32, 0, len(team.RemainInfo)),
		}

		heroEmblems := make([]*heroEmblem, 0, len(team.Info))

		heroM := u.HeroManager()
		for _, v := range team.Info { //nolint:varnamelen
			hero := heroM.Get(v.Hid)
			if hero == nil {
				continue
			}

			heroData := hero.GetData()
			if heroData == nil {
				l4g.Errorf("user: %d, getLogFormationData: no heroData, fid:%d, hid:%d", u.ID(), fid, v.Hid)
				return nil
			}
			var skinId uint32
			skin := u.SkinManager().GetSkinByHeroIDByBattle(heroData.SysId)
			if skin != nil {
				skinId = skin.GetID()
			}

			logHero := &log.Hero{
				Id:          strconv.FormatUint(heroData.Id, 10),
				SysId:       heroData.SysId,
				Star:        heroData.Star,
				Level:       heroData.Level,
				Stage:       heroData.Stage,
				Pos:         v.Pos,
				AwakenLevel: heroData.AwakenLevel,
				SkinId:      skinId,
				Power:       strconv.FormatUint(hero.GetHeroPower(u), 10),
			}

			for _, gem := range heroData.Gems {
				if gem.Slot == 1 {
					logHero.Gem1Level = gem.Level
				} else if gem.Slot == 2 {
					logHero.Gem2Level = gem.Level
				}
			}
			logTeam.Heroes = append(logTeam.Heroes, logHero)

			for _, id := range heroData.Emblem {
				heroEmblems = append(heroEmblems, &heroEmblem{
					heroSysID: heroData.SysId,
					emblemID:  id,
				})
			}
			logTag := hero.GetLogTag()
			logAddition := &log.HeroGemAndEmblemAddition{
				Hid:              strconv.FormatUint(heroData.Id, 10),
				Pos:              v.Pos,
				GemLink:          util.BitAndUint32(logTag, goxml.GemLink),
				GemPasSkill_1:    util.BitAndUint32(logTag, goxml.GemPasSkill1),
				GemPasSkill_2:    util.BitAndUint32(logTag, goxml.GemPasSkill2),
				EmblemLink:       util.BitAndUint32(logTag, goxml.EmblemLink),
				EmblemPasSkill_1: util.BitAndUint32(logTag, goxml.EmblemPasSkill1),
				EmblemPasSkill_2: util.BitAndUint32(logTag, goxml.EmblemPasSkill2),
				EmblemPasSkill_3: util.BitAndUint32(logTag, goxml.EmblemPasSkill3),
			}
			if util.BitAndUint32(logTag, goxml.EmblemTwoSuit) {
				logAddition.EmblemSuitNum = 2
			} else if util.BitAndUint32(logTag, goxml.EmblemFourSuit) {
				logAddition.EmblemSuitNum = 4
			}
			if util.BitAndUint32(logTag, goxml.EmblemExclusive1) {
				logAddition.EmblemExclusiveNum = 1
			} else if util.BitAndUint32(logTag, goxml.EmblemExclusive2) {
				logAddition.EmblemExclusiveNum = 2
			} else if util.BitAndUint32(logTag, goxml.EmblemExclusive3) {
				logAddition.EmblemExclusiveNum = 3
			} else if util.BitAndUint32(logTag, goxml.EmblemExclusive4) {
				logAddition.EmblemExclusiveNum = 4
			}
			logTeam.Addition = append(logTeam.Addition, logAddition)
		}

		emblemM := u.EmblemManager()
		for _, v := range heroEmblems {
			emblem := emblemM.Get(v.emblemID)
			if emblem == nil {
				l4g.Errorf("user: %d, getLogFormationData: no emblem, fid:%d, id:%d", u.ID(), fid, v.emblemID)
				return nil
			}
			logTeam.Emblems = append(logTeam.Emblems, &log.Emblem{
				SysId:     emblem.Data.SysId,
				Hid:       strconv.FormatUint(emblem.Data.Hid, 10),
				HeroSysId: v.heroSysID,
			})
		}

		for _, v := range team.Artifacts {
			logTeam.Artifacts = append(logTeam.Artifacts, &log.Artifact{
				SysId: v.Aid,
				Pos:   v.Pos,
			})
		}

		//羁绊
		linkData := u.FormationManager().GetFormationLinkInfo(fid, i)
		if linkData != nil {
			for _, v := range linkData.ActivedLinks {
				logTeam.Links = append(logTeam.Links, &log.Link{
					SysId: v.LinkId,
					Num:   v.Num,
				})
			}
		}

		//仪式
		riteId, riteRare, riteGrids := u.GetRiteForBattle(fid, i)
		logTeam.Rite = &bt.Rite{
			Id:    riteId,
			Rare:  riteRare,
			Grids: riteGrids,
		}

		//遗物
		for _, v := range team.RemainInfo {
			logTeam.Remains = append(logTeam.Remains, v.Id)
		}
		ret.Teams = append(ret.Teams, logTeam)
	}

	return ret
}

// 生成新日志
// 注：user有可能没有模块数据，此方法内调用的玩家数据只能通过dbUser获得！！！
// 注：user有可能没有模块数据，此方法内调用的玩家数据只能通过dbUser获得！！！
// 注：user有可能没有模块数据，此方法内调用的玩家数据只能通过dbUser获得！！！
func (u *User) newLogMessage(srv servicer) *da.Log {
	power := u.Power()
	if u.GetCrystalPower() > 0 {
		power = u.GetCrystalPower()
	}
	now := time.Now().Unix()
	msg := srv.NewLogMessage()
	msg.Topic = srv.LogTopicDC()
	msg.EventID = srv.CreateLogID()
	msg.EventTime = uint64(now)
	msg.ContextID = u.ContextID()
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC)
	msg.LogSubType = 0
	msg.AppID = uint32(srv.AppID())
	msg.OpGroup = uint32(srv.OpGroup())
	msg.OpID = u.OpID()
	msg.Channel = u.Channel()
	msg.UUID = u.UUID()
	msg.ServerID = u.ServerID()
	msg.UserID = u.ID()
	msg.CreateTime = u.CreateTime()
	msg.Name = u.Name()
	msg.Level = u.Level()
	msg.VipLevel = u.Vip()
	msg.Power = uint64(power)
	msg.PayDiamond = u.RechargeDiamond()
	msg.GiftDiamond = u.PresentDiamond()
	msg.Gold = u.Gold()
	msg.TotalCash = u.GetRecharge()
	msg.Paid = uint32(u.TotalRechargeDiamond())
	msg.ClientIP = u.NetAddr()
	msg.DeviceID = u.DeviceID()
	msg.OpenDay = uint32(srv.ServerDay(now))
	msg.LoginDay = u.LoginDay()
	msg.SeasonID = goxml.GetLogFormatSeasonId(now)
	msg.SeasonLevel = u.GetSeasonLevel()
	msg.SeasonPower = uint64(u.GetSeasonPower())
	msg.GuildID = srv.GetGuildID(u.ID())
	msg.AccountTag = u.GetAccountTag()
	msg.BattleZone = u.GetArea()
	msg.Pid = u.Pid()
	msg.Gid = u.Gid()
	msg.Param8 = u.AdapterExtra()
	msg.Param9 = srv.GetTimeZoneAndOffest()
	//各种日志都不要用Param8和Param9！！！
	//各种日志都不要用Param8和Param9！！！
	//各种日志都不要用Param8和Param9！！！
	return msg
}

func logOnlineNum(srv servicer, num int) {
	now := time.Now().Unix()
	msg := srv.NewLogMessage()
	msg.Topic = srv.LogTopicDC()
	msg.ContextID = 0
	msg.OpID = 0
	msg.Channel = 0
	msg.UUID = ""
	msg.UserID = 0
	msg.CreateTime = 0
	msg.Name = ""
	msg.Level = 0
	msg.VipLevel = 0
	msg.Power = 0
	msg.PayDiamond = 0
	msg.GiftDiamond = 0
	msg.Gold = 0
	msg.Paid = 0
	msg.ClientIP = ""
	msg.DeviceID = ""
	msg.LogSubType = 0
	msg.EventID = srv.CreateLogID()
	msg.EventTime = uint64(now)
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_ONLINE_NUM)
	msg.TypeName = "在线人数"
	msg.OpGroup = uint32(srv.OpGroup())
	msg.AppID = uint32(srv.AppID())
	msg.ServerID = srv.ServerID()
	msg.OpenDay = uint32(srv.ServerDay(now))
	msg.Pid = 0
	msg.Gid = 0
	msg.Param9 = srv.GetTimeZoneAndOffest()
	msg.Param10 = uint64(num) //在线人数
	srv.WriteLogMessage(msg)
}

func logOsOnlineNum(srv servicer, os string, num int) {
	now := time.Now().Unix()
	msg := srv.NewLogMessage()
	msg.Topic = srv.LogTopicDC()
	msg.ContextID = 0
	msg.OpID = 0
	msg.Channel = 0
	msg.UUID = ""
	msg.UserID = 0
	msg.CreateTime = 0
	msg.Name = ""
	msg.Level = 0
	msg.VipLevel = 0
	msg.Power = 0
	msg.PayDiamond = 0
	msg.GiftDiamond = 0
	msg.Gold = 0
	msg.Paid = 0
	msg.ClientIP = ""
	msg.DeviceID = ""
	msg.LogSubType = 0
	msg.EventID = srv.CreateLogID()
	msg.EventTime = uint64(now)
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_GAME_LOGIC)
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WM_ONLINE) //完美在线
	msg.TypeName = "完美os在线人数"
	msg.OpGroup = uint32(srv.OpGroup())
	msg.AppID = uint32(srv.AppID())
	msg.ServerID = srv.ServerID()
	msg.OpenDay = uint32(srv.ServerDay(now))
	msg.Pid = 0
	msg.Gid = 0
	msg.Param9 = srv.GetTimeZoneAndOffest()
	msg.Param10 = uint64(num) //在线人数
	msg.Param1 = os
	srv.WriteLogMessage(msg)
}

func (u *User) LogRegister(srv servicer) {
	msg := u.newLogMessage(srv)
	msg.EventTime = uint64(u.CreateTime())
	msg.ContextID = srv.CreateLogID() //TODO 是否需要异步传递？
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_REGISTER)
	msg.TypeName = "注册"
	msg.Param10 = uint64(u.OnlineTime())  //上线时间
	msg.Param11 = uint64(u.OfflineTime()) //下线时间
	msg.Param14 = u.cookie                //cookie
	srv.WriteLogMessage(msg)
}

// @param: recordType 0玩家正常登录 1，玩家0-1点持续在线自动记录
func (u *User) LogLogin(srv servicer, recordType uint32) {
	msg := u.newLogMessage(srv)
	msg.EventTime = uint64(u.OnlineTime())
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_LOGIN)
	msg.TypeName = "登录"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_LOGIN_SUCCESS)
	msg.Param10 = uint64(u.OnlineTime())     //上线时间
	msg.Param11 = uint64(u.OfflineTime())    //下线时间
	msg.Param12 = u.Exp()                    //角色修为
	msg.Param13 = uint64(u.LastOnlineTime()) //上次上线时间
	msg.Param14 = u.cookie                   //cookie
	msg.Param15 = uint64(recordType)         //是否自动记录
	msg.Param16 = uint64(u.DungeonID())      //主线关卡id
	srv.WriteLogMessage(msg)
}

func (u *User) LogLogout(srv servicer, reason uint32) {
	msg := u.newLogMessage(srv)
	msg.EventTime = uint64(u.OfflineTime())
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_LOGOUT)
	msg.TypeName = "登出"
	msg.LogSubType = reason
	msg.Param10 = uint64(u.OnlineTime())                   //上线时间
	msg.Param11 = uint64(u.OfflineTime())                  //下线时间
	msg.Param12 = u.Exp()                                  //角色修为
	msg.Param13 = uint64(u.DungeonID())                    //主线关卡id
	msg.Param20 = uint64(u.OfflineTime() - u.OnlineTime()) //在线时长
	msg.Param1 = "user"                                    // todo 目前写死user，之后添加auto
	srv.WriteLogMessage(msg)
}

func (u *User) LogResourceAdd(srv servicer, awards []*cl.Resource,
	reason uint32, subReason, mailID uint64) {
	if bytes, err := json.Marshal(awards); err == nil {
		msg := u.newLogMessage(srv)
		msg.TypeName = "资源产出"
		msg.LogType = uint32(da.LOG_TYPE_DA_ID_RESOURCE_ADD)
		msg.LogSubType = reason         //资源产出原因
		msg.Param1 = util.String(bytes) //资源产出详情
		msg.Param10 = subReason         //资源产出原因2
		msg.Param11 = u.TotalAddGold()  //累计增加金币(awards包含金币时，完美日志需要)
		msg.Param12 = mailID            //背包满时，另一部分资源发送到的邮件id
		srv.WriteLogMessage(msg)
		u.IncOpArray(OpResourceAdd)
	} else {
		l4g.Errorf("LogResourceAdd json marshal error: %v", err)
	}
}

func (u *User) LogResourceDec(srv servicer, costs []*cl.Resource, reason uint32, subReason uint64) {
	if bytes, err := json.Marshal(costs); err == nil {
		msg := u.newLogMessage(srv)
		msg.TypeName = "资源消耗"
		msg.LogType = uint32(da.LOG_TYPE_DA_ID_RESOURCE_DEC)
		msg.LogSubType = reason         //资源消耗原因
		msg.Param1 = util.String(bytes) //资源的消耗详情
		msg.Param10 = subReason         //资源产出原因2
		msg.Param11 = u.TotalCostGold() //累计消耗金币(costs包含金币时，完美日志需要)
		srv.WriteLogMessage(msg)
		u.IncOpArray(OpResourceDec)
	} else {
		l4g.Errorf("LogResourceDec json marshal error: %v", err)
	}
}

func WriteTraceLog(srv servicer, msg *da.Log, cmd uint32, cmsg interface{}) {
	msg.Topic = "trace-ngame"
	msg.TypeName = "协议跟踪"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TRACE)
	if bytes, err := json.Marshal(cmsg); err == nil {
		msg.Param1 = util.String(bytes) //协议数据
	} else {
		l4g.Errorf("json marshal error: %v", err)
	}
	msg.Param10 = uint64(cmd) //协议ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogTrace(srv servicer, cmd uint32, cmsg interface{}) {
	if srv.EnableTraceLog(u.ID()) {
		msg := u.newLogMessage(srv)
		WriteTraceLog(srv, msg, cmd, cmsg)
	}
}

func (u *User) LogMonitor(srv servicer) {
	msg := u.newLogMessage(srv)
	msg.Topic = "monitor-ngame"
	msg.TypeName = "协议监控"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MONITOR)
	arr := u.CmdArray().IDs()
	failed := u.CmdArray().Failed()
	if len(arr) > 0 {
		//防止生成Null数据在日志里
		if bytes, err := json.Marshal(arr); err == nil {
			msg.Param1 = util.String(bytes) //单位时间内消息
			msg.Param10 = uint64(len(arr))
		} else {
			l4g.Errorf("json marshal error: %v", err)
		}
	}
	if len(failed) > 0 {
		if bytes, err := json.Marshal(failed); err == nil {
			msg.Param2 = util.String(bytes) //单位时间内错误消息
			msg.Param11 = uint64(len(failed))
		} else {
			l4g.Errorf("json marshal error: %v", err)
		}
	}
	if len(u.CmdArray().FailedIndex()) > 0 {
		if bytes, err := json.Marshal(u.CmdArray().FailedIndex()); err == nil {
			msg.Param3 = util.String(bytes) //单位时间内错误消息索引
		} else {
			l4g.Errorf("json marshal error: %v", err)
		}
	}
	if len(u.CmdArray().Times()) > 0 {
		if bytes, err := json.Marshal(u.CmdArray().Times()); err == nil {
			msg.Param4 = util.String(bytes) //单位时间内错误消息索引
		} else {
			l4g.Errorf("json marshal error: %v", err)
		}
	}
	ops := u.getOpArray()
	msg.Param12 = uint64(ops[OpWorldChat])
	msg.Param13 = uint64(ops[OpResourceAdd])
	msg.Param14 = uint64(ops[OpResourceDec])
	srv.WriteLogMessage(msg)
}

func (u *User) LogAddDiamond(srv servicer, count, reason uint32, subreason uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "钻石获得日志"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ADD_DIAMOND)
	msg.Param10 = uint64(reason)
	msg.Param11 = uint64(count)
	msg.Param12 = u.TotalAddDiamond() // todo  游戏内钻石累计增加总量，即含本次所有增量求和
	msg.Param13 = subreason
	srv.WriteLogMessage(msg)
}

func (u *User) LogCostDiamond(srv servicer, count, reason uint32, subreason uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "钻石消耗日志"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CONSUME_DIAMOND)
	msg.Param10 = uint64(reason)
	msg.Param11 = uint64(count)
	msg.Param12 = u.TotalCostDiamond()
	msg.Param13 = subreason
	srv.WriteLogMessage(msg)
}

func (u *User) LogLevelUp(srv servicer, oldExp, newExp uint64, oldLv uint32) {
	msg := u.newLogMessage(srv)
	msg.LogType = uint32(da.LOG_TYPE_DA_ID_LEVEL_UP)
	msg.TypeName = "玩家升级"
	msg.Param10 = oldExp
	msg.Param11 = newExp
	msg.Param12 = uint64(oldLv)
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipWear(srv servicer, reqType, heroSysID uint32, reqEquips []*cl.Equipment,
	reqHero uint64, changedequips []*Equip, heroIds []uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-穿戴"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_WEAR)
	equipIds := make([]uint64, 0, len(reqEquips))
	for _, equip := range reqEquips {
		equipIds = append(equipIds, equip.Id)
	}
	msg.Param1, _ = JSONMarshal(equipIds) //请求操作的装备
	changedEquipIds := make([]uint64, 0, len(changedequips))
	for _, equip := range changedequips {
		changedEquipIds = append(changedEquipIds, equip.Data.Id)
	}
	msg.Param2, _ = JSONMarshal(changedEquipIds) //被修改的所有装备
	msg.Param3, _ = JSONMarshal(heroIds)         //被修改的所有英雄
	msg.Param10 = uint64(reqType)                //操作类型
	msg.Param11 = reqHero                        //请求操作英雄唯一id
	msg.Param12 = uint64(heroSysID)              //请求操作英雄量表id
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipStrength(srv servicer, heroSysID uint32, oneKey bool,
	hid uint64, equips []*log.EquipStrength) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-强化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_STRENGTH)
	msg.Param10 = uint64(util.If(oneKey, 1, 0)) //一键1，否则未0
	msg.Param11 = hid                           //英雄唯一id
	msg.Param12 = uint64(heroSysID)             //英雄量表id
	msg.Param1, _ = JSONMarshal(equips)         //装备强化信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipRefine(srv servicer, equip *Equip, preLevel, preExp,
	heroSysID uint32, hid uint64, quick bool, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-精炼"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_REFINE)
	msg.Param10 = equip.Data.Id                  //装备id
	msg.Param11 = uint64(equip.Data.SysId)       //装备系统id
	msg.Param12 = uint64(preLevel)               //精炼前的精炼等级
	msg.Param13 = uint64(preExp)                 //精炼前的精炼经验
	msg.Param14 = uint64(util.If(quick, 1, 0))   //一键1，否则未0
	msg.Param15 = uint64(equip.Data.RefineLevel) //精炼后的精炼等级
	msg.Param16 = uint64(equip.Data.RefineExp)   //精炼后的精炼经验
	msg.Param17 = hid                            //英雄唯一id
	msg.Param18 = uint64(heroSysID)              //英雄量表id
	msg.Param19 = uint64(oldScore)               //操作前武器评分
	msg.Param20 = uint64(newScore)               //操作后武器评分
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipEvolution(srv servicer, equip *Equip, preLevel, heroSysID uint32,
	hid uint64, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-进阶"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_EVOLUTION)
	msg.Param10 = equip.Data.Id                     //装备id
	msg.Param11 = uint64(equip.Data.SysId)          //装备系统id
	msg.Param12 = uint64(preLevel)                  //进阶前的进阶等级
	msg.Param13 = uint64(equip.Data.EvolutionLevel) //进阶后的进阶等级
	msg.Param14 = hid                               //英雄唯一id
	msg.Param15 = uint64(heroSysID)                 //英雄量表id
	msg.Param16 = uint64(oldScore)                  //操作前武器评分
	msg.Param17 = uint64(newScore)                  //操作后武器评分
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipEnchant(srv servicer, equip *Equip, preLevel, heroSysID uint32,
	hid uint64, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-附魔"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_ENCHANT)
	msg.Param10 = equip.Data.Id                   //装备id
	msg.Param11 = uint64(equip.Data.SysId)        //装备系统id
	msg.Param12 = uint64(preLevel)                //附魔前等级
	msg.Param13 = uint64(equip.Data.EnchantLevel) //附魔到目标等级
	msg.Param14 = hid                             //英雄唯一id
	msg.Param15 = uint64(heroSysID)               //英雄量表id
	msg.Param16 = uint64(oldScore)                //武器旧评分
	msg.Param17 = uint64(newScore)                //武器新评分
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipDecompose(srv servicer, ids []uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-分解"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_DECOMPOSE)
	msg.Param1, _ = JSONMarshal(ids) // 被分解的装备id
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipRevive(srv servicer, id uint64, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-重生"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_REVIVE)
	msg.Param10 = id // 装备id
	msg.Param11 = uint64(oldScore)
	msg.Param12 = uint64(newScore)
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipGrowTransfer(srv servicer, growEquips, reviveEquips []*log.EquipmentStr) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-养成转移"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_GROW_TRANSFER)
	msg.Param2, _ = JSONMarshal(growEquips)   // 转移后待养成装备的信息
	msg.Param3, _ = JSONMarshal(reviveEquips) // 转移后待重生装备的信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogEquipSetAutoDecompose(srv servicer, autoRare []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "装备-设置自动分解的品质"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EQUIP_SET_AUTO_DECOMPOSE)
	msg.Param1, _ = JSONMarshal(autoRare) // 转移后剩余资源
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemSetAutoDecompose(srv servicer, autoRare []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "符文-设置自动分解的品质"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_SET_AUTO_DECOMPOSE)
	msg.Param1, _ = JSONMarshal(autoRare) // 自动分解等级
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemLevelUp(srv servicer, emblem *Emblem, oldLevel, heroSysID uint32, hid uint64, oldS, newS int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_LEVELUP)
	msg.Param10 = emblem.Data.Id            // 纹章uuid
	msg.Param11 = uint64(emblem.Data.SysId) // 纹章sysid
	msg.Param12 = uint64(oldLevel)          // 升级前的等级
	msg.Param13 = uint64(emblem.Data.Level) // 升级后的等级
	msg.Param14 = uint64(heroSysID)         // 英雄唯一id
	msg.Param15 = hid                       // 英雄量表id
	msg.Param16 = uint64(oldS)              // 操作前评分
	msg.Param17 = uint64(newS)              // 操作后评分
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemStageUp(srv servicer, emblem *Emblem, oldSysID, heroSysID uint32, hid uint64, oldS, newS int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章进阶"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_STAGEUP)
	msg.Param10 = emblem.Data.Id            // 纹章uuid
	msg.Param11 = uint64(emblem.Data.SysId) // 纹章新的sysid
	msg.Param12 = uint64(oldSysID)          // 纹章旧的sysid
	msg.Param13 = uint64(heroSysID)         // 英雄唯一id
	msg.Param14 = hid                       // 英雄量表id
	msg.Param15 = uint64(oldS)              // 操作前评分
	msg.Param16 = uint64(newS)              // 操作后评分
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemBlessing(srv servicer, emblem *Emblem, heroSysID uint32, hid uint64, oldS, newS int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章祝福"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_BLESSING)
	msg.Param10 = emblem.Data.Id            // 纹章uuid
	msg.Param11 = uint64(emblem.Data.SysId) // 纹章sysid
	//msg.Param12 = uint64(emblem.Data.Blessing.Level)              // 纹章祝福等级
	msg.Param13 = uint64(heroSysID) // 英雄唯一id
	msg.Param14 = hid               // 英雄量表id
	msg.Param15 = uint64(oldS)      // 操作前评分
	msg.Param16 = uint64(newS)      // 操作后评分
	//bytes, err := json.Marshal(emblem.Data.Blessing.AttrProgress) // 纹章祝福后的属性条
	//if err != nil {
	//	l4g.Errorf("logEmblemBlessing json marshal error: %v", err)
	//}
	//msg.Param1 = util.String(bytes)
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemDecompose(srv servicer, emblemIDs []*log.CultivateScoreStr) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章分解"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_DECOMPOSE)
	bytes, err := json.Marshal(emblemIDs) //分解的纹章
	if err != nil {
		l4g.Errorf("logEmblemDecompose json marshal error: %v", err)
	}
	msg.Param1 = util.String(bytes)
	srv.WriteLogMessage(msg)
}
func (u *User) LogEmblemCompose(srv servicer, sysID uint32, count uint32, costs []uint64, scoreLog []*log.CultivateScoreStr) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章合成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_COMPOSE)
	msg.Param10 = uint64(sysID)       // 纹章SysID
	msg.Param11 = uint64(count)       // 纹章数量
	bytes, err := json.Marshal(costs) // 消耗的纹章
	if err != nil {
		l4g.Errorf("logEmblemCompose json marshal error: %v", err)
	}
	msg.Param1 = util.String(bytes)

	scoreLogByte, err := json.Marshal(scoreLog)
	if err != nil {
		l4g.Errorf("logEmblemCompose scoreLog json marshal error: %v", err)
	}
	msg.Param2 = util.String(scoreLogByte)
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemRevive(srv servicer, cultivateS []*log.CultivateScoreStr) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章重生"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_REVIVE)
	bytes, err := json.Marshal(cultivateS) //重生的纹章id
	if err != nil {
		l4g.Errorf("logEmblemRevive json marshal error: %v", err)
	}
	msg.Param1 = util.String(bytes)
	srv.WriteLogMessage(msg)
}

// TODO 数数日志待添加
func (u *User) LogEmblemGrowTransfer(srv servicer, preEmblems, emblems []*cl.EmblemInfo, leftRes []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章-养成转移"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_GROW_TRANSFER)
	msg.Param1, _ = JSONMarshal(leftRes)    // 转移后剩余资源
	msg.Param2, _ = JSONMarshal(preEmblems) // 转移前两个纹章的信息
	msg.Param3, _ = JSONMarshal(emblems)    // 转移后两个纹章的信息
	srv.WriteLogMessage(msg)
}

// 获得专属符文
func (u *User) LogEmblemExclusive(srv servicer, emblem *Emblem, reason uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章-获得专属纹章"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_EXCLUSIVE)
	msg.Param10 = emblem.Data.Id                   // 纹章uuid
	msg.Param11 = uint64(emblem.Data.SysId)        // 纹章sysid
	msg.Param12 = uint64(emblem.Data.AdditiveHero) // 专属英雄sysid
	msg.Param13 = uint64(emblem.Data.SkillId)      // 专属技能id
	msg.Param14 = uint64(reason)                   // 来源
	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo != nil {
		msg.Param15 = uint64(emblemInfo.SuitSkill)
		suitInfo := goxml.GetData().EmblemSuitInfoM.Index(emblemInfo.SuitSkill)
		if suitInfo != nil {
			msg.Param16 = uint64(suitInfo.Type)
		}
	}
	srv.WriteLogMessage(msg)
}

// 购买符文背包栏位
func (u *User) LogEmblemBuySlot(srv servicer, buyTimes, newCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章-购买背包栏位"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_BUY_SLOT)
	msg.Param10 = uint64(buyTimes) //购买次数
	msg.Param11 = uint64(newCount) //购买后栏位数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemUpgrade(srv servicer, preEmblems, emblems []*cl.EmblemInfo) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章-升阶"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_UPGRADE)
	msg.Param1, _ = JSONMarshal(preEmblems) // 升阶前纹章的信息
	msg.Param2, _ = JSONMarshal(emblems)    // 升阶后两个纹章的信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemSuccinct(srv servicer, preEmblem, emblem *cl.EmblemInfo) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章-洗炼"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_SUCCINCT)
	preSuccinctLog := &log.EmblemSuccinctLog{}
	if preEmblem != nil && preEmblem.Succinct != nil {
		preSuccinctLog.TmpSkillId = preEmblem.Succinct.TmpSkillId
		preSuccinctLog.TmpAffixId = preEmblem.Succinct.TmpAffixId
		emblemInfo := goxml.GetData().EmblemInfoM.Index(preEmblem.Succinct.TmpSysId)
		if emblemInfo != nil {
			preSuccinctLog.TmpSuitId = emblemInfo.SuitSkill // 洗炼前的套装ID
			preSuccinctLog.AffixType = emblemInfo.GetEmblemAffixType(preEmblem.Succinct.TmpAffixId)
		}
	}
	msg.Param2, _ = JSONMarshal(preSuccinctLog) // 洗炼前的信息

	succinctLog := &log.EmblemSuccinctLog{}
	succinctLog.Id = emblem.Id
	succinctLog.SysId = emblem.SysId
	succinctLog.HeroSysId = emblem.AdditiveHero
	succinctLog.LockId = emblem.Succinct.LockId
	succinctLog.TmpSkillId = emblem.Succinct.TmpSkillId
	succinctLog.TmpAffixId = emblem.Succinct.TmpAffixId
	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Succinct.TmpSysId)
	if emblemInfo != nil {
		succinctLog.TmpSuitId = emblemInfo.SuitSkill // 洗炼前的套装ID
		succinctLog.AffixType = emblemInfo.GetEmblemAffixType(emblem.Succinct.TmpAffixId)
	}
	msg.Param3, _ = JSONMarshal(succinctLog) // 洗炼前的信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogEmblemSuccinctLockOrSave(srv servicer, preEmblem, emblem *cl.EmblemInfo, opType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章-洗炼保存或者锁定"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_SUCCINCT_LOCK_OR_SAVE)
	msg.Param1, _ = JSONMarshal(emblem) // 纹章信息
	if preEmblem.Succinct != nil {
		msg.Param10 = uint64(preEmblem.SkillId) //
		emblemInfo := goxml.GetData().EmblemInfoM.Index(preEmblem.SysId)
		if emblemInfo != nil {
			msg.Param11 = uint64(emblemInfo.SuitSkill) //
		}
		msg.Param12 = uint64(preEmblem.Succinct.AffixId)
		msg.Param13 = uint64(preEmblem.Succinct.LockId)
		msg.Param14 = uint64(preEmblem.SysId)
	}
	msg.Param15 = uint64(opType) // 操作类型：锁定1 或 保存2
	newEmblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.SysId)
	if newEmblemInfo != nil {
		msg.Param16 = uint64(newEmblemInfo.SuitSkill) //
	}
	srv.WriteLogMessage(msg)
}

func (u *User) EmblemSuccinctItemConflate(srv servicer, targetResources []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "纹章-合成洗炼道具"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_EMBLEM_SUCCINCT_ITEM_CONFLATE)
	msg.Param1, _ = JSONMarshal(targetResources) // 目标道具
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroLevelUp(srv servicer, hid uint64, level, num, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_LEVEL_UP)
	msg.Param10 = hid           //英雄id
	msg.Param11 = uint64(level) //当前等级
	msg.Param12 = uint64(num)   //升多少级
	msg.Param13 = uint64(sysID) //英雄量表id
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroStageUp(srv servicer, hid uint64, stage, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-突破"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_STAGE_UP)
	msg.Param10 = hid           //英雄id
	msg.Param11 = uint64(stage) //当前突破等级
	msg.Param12 = uint64(sysID) //英雄量表id
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroStarUp(srv servicer, hid uint64, star, sysID uint32, costs map[uint64]struct{}, normal bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-升星"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_STAR_UP)
	msg.Param10 = hid                           //英雄id
	msg.Param11 = uint64(star)                  //当前星级
	msg.Param12 = uint64(sysID)                 //英雄量表id
	msg.Param13 = uint64(util.If(normal, 1, 0)) //1-常规升星 0-回退材料的养成升星

	costHeroIDs := make([]uint64, 0, len(costs))
	for id := range costs {
		costHeroIDs = append(costHeroIDs, id)
	}
	msg.Param1, _ = JSONMarshal(costHeroIDs) //消耗的英雄id
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroBuySlot(srv servicer, num uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-购买格子"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_BUY_SLOT)
	msg.Param10 = uint64(num) //当前格子数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroUpdateLockStatus(srv servicer, hid uint64, lock bool, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-更新锁定状态"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_UPDATE_LOCK_STATUS)
	msg.Param10 = hid
	msg.Param11 = uint64(util.If(lock, 1, 0)) //1-锁定 0-解锁
	msg.Param12 = uint64(sysID)               //英雄量表id
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroDecompose(srv servicer, data []*log.LogHeroSelfData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-分解"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_DECOMPOSE)
	msg.Param1, _ = JSONMarshal(data) //被分解的英雄
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroBack(srv servicer, hid uint64, oldLv, oldStage, oldStar, newStar, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-回退"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_BACK)
	msg.Param10 = hid              //英雄id
	msg.Param11 = uint64(oldStar)  //原星级
	msg.Param12 = uint64(newStar)  //当前星级
	msg.Param13 = uint64(sysID)    //英雄量表id
	msg.Param14 = uint64(oldLv)    //原等级
	msg.Param15 = uint64(oldStage) //原突破等级
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroRevive(srv servicer, hid uint64, oldLv, oldStage, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-重生"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_REVIVE)
	msg.Param10 = hid              //英雄id
	msg.Param11 = uint64(oldLv)    //原等级
	msg.Param12 = uint64(oldStage) //原突破等级
	msg.Param13 = uint64(sysID)    //英雄量表id
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroChangeRandom(srv servicer, hid uint64, oldSysID, newSysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-转换随机"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_CHANGE_RANDOM)
	msg.Param10 = hid              //原英雄id
	msg.Param11 = uint64(oldSysID) //原英雄sysID
	msg.Param12 = uint64(newSysID) //随机到的英雄sysID
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroChangeSave(srv servicer, oldID uint64, oldSysID uint32, isSave bool, data *log.LogHeroSelfData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-转换保存"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_CHANGE_SAVE)
	msg.Param10 = oldID                         //原英雄id
	msg.Param11 = uint64(oldSysID)              //原英雄sysID
	msg.Param12 = uint64(util.If(isSave, 1, 0)) //1-保存 0-放弃
	if data != nil {
		msg.Param1, _ = JSONMarshal(data) //英雄属性数据
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroConversion(srv servicer, data []*log.LogHeroSelfData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-兑换"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_CONVERSION)
	msg.Param1, _ = JSONMarshal(data) //被分解的英雄
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroGemLevelUp(srv servicer, hid uint64, slot, level, num, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-宝石升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_GEM_LEVEL_UP)
	msg.Param10 = hid           //英雄id
	msg.Param11 = uint64(slot)  //宝石槽位
	msg.Param12 = uint64(level) //当前等级
	msg.Param13 = uint64(num)   //升多少级
	msg.Param14 = uint64(sysID)
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroExchange(srv servicer, costHeroes []*log.LogHeroSelfDataStr, newSysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-转换"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_EXCHANGE)
	msg.Param10 = uint64(newSysID)          //产出英雄的系统id
	msg.Param1, _ = JSONMarshal(costHeroes) //消耗的英雄
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroAwaken(srv servicer, hid uint64, sysID, awakenLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-觉醒"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_AWAKEN)
	msg.Param10 = hid                 //英雄id
	msg.Param11 = uint64(sysID)       //英雄量表id
	msg.Param12 = uint64(awakenLevel) //当前觉醒等级
	srv.WriteLogMessage(msg)
}

// LogMemoryUnlock 境界-回忆点解锁
func (u *User) LogMemoryUnlock(srv servicer, chipID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "境界-回忆点解锁"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MEMORY_CHIP_UNLOCK)
	msg.Param10 = uint64(chipID)
	srv.WriteLogMessage(msg)
}

func (u *User) LogTalesChapterFinish(srv servicer, taleID uint32, chapterID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄列传-章节完成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TALES_CHAPTER_FINISH)
	msg.Param10 = uint64(taleID)    // 列传ID
	msg.Param11 = uint64(chapterID) // 章节ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogTalesChapterTakeReward(srv servicer, taleID uint32, chapterIDs []uint32, status uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄列传-章节领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TALES_CHAPTER_TAKE_REWARD)
	msg.Param10 = uint64(taleID) // 列传ID
	//msg.Param11 = uint64(chapterIDs) // 章节ID
	msg.Param12 = uint64(status)                           // 奖励领取状态
	msg.Param1 = util.Uint32SliceToString(chapterIDs, ",") // 章节ID
	msg.Param3, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_TALES_ELITE)))
	srv.WriteLogMessage(msg)
}

func (u *User) LogSevenDayLoginTakeAward(srv servicer, id uint32, info *cl.SevenDayLogin) {
	if info == nil {
		return
	}
	msg := u.newLogMessage(srv)
	msg.TypeName = "七日登录-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEVENDAY_LOGIN_TAKEAWARD)
	msg.Param10 = uint64(id)                //领取的id
	msg.Param11 = uint64(info.Days)         //累计登录天数
	msg.Param12 = uint64(info.RewardStatus) //领取后奖励领取状态
	srv.WriteLogMessage(msg)
}

func (u *User) LogTalesEliteWipe(srv servicer, eliteID uint32, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄列传-强敌扫荡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TALES_ELITE_WIPE)
	msg.Param10 = uint64(eliteID) // 强敌id
	msg.Param11 = uint64(count)   // 今日已挑战此强敌的次数
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDungeonRecvChapterTaskAward(srv servicer, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会副本-领取周章节进度任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_RECV_CHAPTER_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(ids) // 任务id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDungeonRecvBossBoxAward(srv servicer, round, chapter, box uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会副本-领取Boss宝箱奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_RECV_BOSS_BOX_AWARD)
	msg.Param10 = uint64(round)
	msg.Param11 = uint64(chapter)
	msg.Param13 = uint64(box)
	srv.WriteLogMessage(msg)
}

func (u *User) LogicGuildDungeonRecvTopDivision(srv servicer, divisions []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会副本-领取赛季最高段位奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_RECV_SEASON_TOP_DIVISION_AWARD)
	msg.Param1, _ = JSONMarshal(divisions)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDungeonBuyChallengeTimes(srv servicer, oldChallengeTimes, oldBuyCount, newBuyCount, challengeTimes uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会副本-购买挑战次数"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_BUY_CHALLENGE_TIMES)
	msg.Param10 = uint64(oldChallengeTimes)
	msg.Param11 = uint64(oldBuyCount)
	msg.Param12 = uint64(newBuyCount)
	msg.Param13 = uint64(challengeTimes)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDungeonNewFight(srv servicer, season, round, battleZone, chapter, bossID, formationID uint32,
	damage, guildID uint64, reportID string, marked bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新公会副本-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_NEW_FIGHT)
	msg.Param3, _ = JSONMarshal(u.getLogFormationData(formationID))
	msg.Param11 = uint64(season)
	msg.Param12 = uint64(round)
	msg.Param13 = uint64(battleZone)
	msg.Param14 = uint64(chapter)
	msg.Param15 = uint64(bossID)
	msg.Param16 = uint64(damage)
	msg.Param17 = uint64(guildID)
	msg.Param18 = util.If64(marked, 1, 0)
	msg.Param1 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildUseStrategy(srv servicer, gid, opGid uint64, cmsg *l2c.C2L_GuildDungeonUseStrategy, strategyType, ownDivision, opDivision, useCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-使用秘技"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_USE_STRATEGY)
	msg.Param10 = gid                  // 使用秘技的公会ID
	msg.Param15 = uint64(strategyType) // 秘技类型 1.公会副本，2.龙攻城技能，3.龙战秘技
	if cmsg != nil {
		msg.Param11 = uint64(cmsg.StrategyId)        // 秘技ID
		msg.Param12 = cmsg.Gid                       // 被使用秘技的公会ID
		msg.Param13 = uint64(cmsg.BeUseGuildChapter) // 被使用秘技公会章节
		msg.Param14 = uint64(cmsg.UseCount)          // 使用次数
	} else {
		msg.Param12 = opGid
		msg.Param14 = uint64(useCount)    // 使用次数
		msg.Param16 = uint64(ownDivision) // 我方公会副本段位
		msg.Param17 = uint64(opDivision)  // 敌方公会副本段位
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildCreate(srv servicer, name string, badge uint32, guildID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-创建"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_CREATE)
	msg.Param10 = uint64(badge) // 客户端请求的徽章
	msg.Param11 = guildID       // 新建的公会ID
	msg.Param2 = name           // 公会名
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildManagerMember(srv servicer, memberID uint64, reqType, oldGrade,
	newGrade uint32, guildID, memberGuildID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-管理公会成员"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MANAGER_MEMBER)
	msg.Param10 = memberID         // 客户端请求的成员Id
	msg.Param11 = uint64(reqType)  // 客户端请求的type
	msg.Param12 = uint64(oldGrade) // 操作前此成员的职位
	msg.Param13 = uint64(newGrade) // 操作后此成员的职位
	msg.Param14 = guildID          // 操作前此成员的公会id
	msg.Param15 = memberGuildID    // 操作后此成员的公会id
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildModifyInfo(srv servicer, cmsg *l2c.C2L_GuildModifyInfo) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-设置公会基本信息"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MODIFY_INFO)
	// msg.Param1 = reqDeclaration                       // 前端传的宣言
	msg.Param2 = cmsg.Declaration // 操作后公会的宣言
	msg.Param3 = cmsg.Language
	// msg.Param10 = uint64(reqBadge)                    // 前端传的徽章
	msg.Param11 = uint64(cmsg.Badge) // 操作后公会的徽章
	// msg.Param12 = uint64(util.If(reqNeedApply, 1, 0)) // 前端传的审核设置 (需要审核为 1 否则为 0)
	// msg.Param13 = uint64(util.If(newNeedApply, 1, 0)) // 操作后公会的审核设置
	msg.Param14 = uint64(cmsg.JoinType) // 加入类型
	msg.Param15 = uint64(cmsg.Label)
	msg.Param16 = uint64(cmsg.LvLimit)
	msg.Param17 = uint64(cmsg.PowerLimit)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSetName(srv servicer, reqName, newName string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-设置公会名"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SET_NAME)
	msg.Param1 = reqName // 前端传的公会名
	msg.Param2 = newName // 当前的公会名
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildModifyNotice(srv servicer, reqNotice, newNotice string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-修改公会公告"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MODIFY_NOTICE)
	msg.Param1 = reqNotice // 前端传的公告
	msg.Param2 = newNotice // 当前的公会公告
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildApplyRatify(srv servicer, reqIDs []uint64, accept bool, oldApplyIds, newApplyIds []uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-管理申请"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_APPLY_RATIFY)
	msg.Param1, _ = JSONMarshal(reqIDs)         // 前端传的id
	msg.Param2, _ = JSONMarshal(oldApplyIds)    // 操作前公会的申请列表
	msg.Param3, _ = JSONMarshal(newApplyIds)    // 操作后公会的申请列表
	msg.Param10 = uint64(util.If(accept, 1, 0)) // 前端传的操作类型 (true为 1 false为 0)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildUserApply(srv servicer, reqType uint32, reqID uint64, oldApplyIds, newApplyIds []uint64) {
	msg := u.newLogMessage(srv)
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_USER_APPLY)
	msg.TypeName = "公会-玩家加入或者申请公会"
	msg.Param10 = uint64(reqType)            // 前端请求的类型
	msg.Param11 = reqID                      // 前端请求的公会id
	msg.Param1, _ = JSONMarshal(oldApplyIds) // 操作前玩家的申请列表
	msg.Param2, _ = JSONMarshal(newApplyIds) // 操作后玩家的申请列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSignIn(srv servicer, oldGuildLevel, oldGuildExp, newLevel, newExp uint32, guildID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-签到"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SIGN_IN)
	msg.Param10 = uint64(oldGuildLevel) // 签到前公会的等级
	msg.Param11 = uint64(oldGuildExp)   // 签到前公会的经验
	msg.Param12 = uint64(newLevel)      // 签到后公会的等级
	msg.Param13 = uint64(newExp)        // 签到后公会的经验
	msg.Param14 = guildID               // 公会ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildQuit(srv servicer, oldGuildID, newGuildID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-退出公会"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_QUIT)
	msg.Param10 = oldGuildID // 退出去玩家的公会id
	msg.Param11 = newGuildID // 退出后玩家的公会id
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDisband(srv servicer, guildID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-解散公会"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DISBAND)
	msg.Param10 = guildID // 解散的公会id
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildRecruit(srv servicer, guildID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-公会招募"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_RECRUIT)
	msg.Param10 = guildID // 公会id
	srv.WriteLogMessage(msg)
}

// LogGuildModifyTextInfo
// @Description: 修改公会文本类信息
// @receiver u
func (u *User) LogGuildModifyTextInfo(srv servicer, logInfo *log.GuildLogText) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-修改文本类信息"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MODIFY_TEXT_INFO)
	msg.Param1, _ = JSONMarshal(logInfo)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildUserJoinOrQuit(srv Servicer, logInfo *log.GuildUserJoinOrQuit) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-玩家加入或退出"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_USER_JOIN_OR_QUIT)
	msg.Param1, _ = JSONMarshal(logInfo)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildManage(srv Servicer, logInfo *log.GuildManage) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-管理"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MANAGE)
	msg.Param1, _ = JSONMarshal(logInfo)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildPositionChange(srv Servicer, logInfo *log.GuildPosition) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-职位变动"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_POSITION_CHANGE)
	msg.Param1, _ = JSONMarshal(logInfo)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildQuickTransfer(srv servicer, oldGuildID, newGuildID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-快速转会"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_QUIT)
	msg.Param10 = oldGuildID // 退出前玩家的公会id
	msg.Param11 = newGuildID // 退出后玩家的公会id
	srv.WriteLogMessage(msg)
}

// LogGoldBuyGetGold : 点金系统，获取金币
func (u *User) LogGoldBuyGetGold(srv servicer, boxID, useCount, DungeonID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "点金-获取金币"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GOLD_BUY_GET_GOLD)
	msg.Param10 = uint64(boxID)     // 金币宝箱id
	msg.Param11 = uint64(useCount)  // 使用次数
	msg.Param12 = uint64(DungeonID) // 主线关卡id
	srv.WriteLogMessage(msg)
}

// LogDispatchReceiveTask : 悬赏系统-接取任务
func (u *User) LogDispatchReceiveTask(srv servicer, dispatchID uint64, heroIds []uint64, dispatchLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "悬赏系统-接取任务"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISPATCH_RECEIVE_TASK)
	bytesHeroIds, err := JSONMarshal(heroIds)
	if err != nil {
		l4g.Errorf("LogDispatchReceiveTask json marshal error: %v", err)
	}
	msg.Param1 = bytesHeroIds           // 接取任务需要的英雄列表
	msg.Param10 = dispatchID            // 悬赏任务id
	msg.Param11 = uint64(dispatchLevel) //接取悬赏任务等级
	srv.WriteLogMessage(msg)
}

// LogDispatchReceiveAward : 悬赏系统-领取奖励
func (u *User) LogDispatchReceiveAward(srv servicer, dispatchIDs []uint64, isSpeed, isAssistant bool, sysIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "悬赏系统-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISPATCH_RECEIVE_AWARD)
	msg.Param1, _ = JSONMarshal(dispatchIDs)         // 悬赏任务列表
	msg.Param2, _ = JSONMarshal(sysIDs)              // 悬赏任务sysID列表
	msg.Param10 = uint64(util.If(isSpeed, 1, 0))     // 是否钻石加速 (true为 1 false为 0)
	msg.Param11 = uint64(util.If(isAssistant, 1, 0)) // 是否一键 (true为 1 false为 0)
	srv.WriteLogMessage(msg)
}

// LogDispatchRefreshTask : 悬赏系统-刷新悬赏任务
func (u *User) LogDispatchRefreshTask(srv servicer, tasks []*cl.DispatchTask, level uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "悬赏系统-刷新悬赏任务"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISPATCH_REFRESH_TASK)
	//bytesTasks, err := JSONMarshal(tasks)
	//if err != nil {
	//	l4g.Errorf("LogDispatchRefreshTask json marshal error: %v", err)
	//}
	//msg.Param1 = bytesTasks // 刷新后的悬赏任务列表
	taskIDs := make([]uint64, 0, len(tasks))
	for _, task := range tasks {
		taskIDs = append(taskIDs, task.Id)
	}
	msg.Param1, _ = JSONMarshal(taskIDs) // 刷新后的悬赏任务id
	msg.Param11 = uint64(level)          //接取任务的悬赏等级
	srv.WriteLogMessage(msg)
}

// LogGemDecompose : 宝石系统-宝石分解
func (u *User) LogGemDecompose(srv servicer, gem []*log.CultivateScoreStr) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宝石系统-宝石分解"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GEM_DECOMPOSE)

	bytes, err := JSONMarshal(gem)
	if err != nil {
		l4g.Errorf("LogGemDecompose json marshal error: %v", err)
	}
	msg.Param1 = bytes //被分解的宝石
	srv.WriteLogMessage(msg)
}

// LogGemWear : 宝石系统-宝石穿戴
func (u *User) LogGemWear(srv servicer, gemIDs, heroIDs []uint64, slot, opType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宝石系统-宝石穿戴"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GEM_WEAR)
	msg.Param10 = uint64(slot)   // 英雄身上的宝石槽位
	msg.Param11 = uint64(opType) // 宝石操作类型
	bytesGems, err := JSONMarshal(gemIDs)
	if err != nil {
		l4g.Errorf("LogGemWear json marshal error: %v", err)
	}
	msg.Param1 = bytesGems // 变化的宝石数据

	bytesHeroes, err := JSONMarshal(heroIDs)
	if err != nil {
		l4g.Errorf("LogGemWear json marshal error: %v", err)
	}
	msg.Param2 = bytesHeroes // 变化的英雄数据

	srv.WriteLogMessage(msg)
}

// LogGemCompose : 宝石系统-宝石合成
func (u *User) LogGemCompose(srv servicer, costs []*LogComposeCostGem, gem *cl.GemInfo, inheritAttrs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宝石系统-宝石合成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GEM_COMPOSE)
	bytes, err := JSONMarshal(costs)
	if err != nil {
		l4g.Errorf("LogGemCompose json marshal error: %v", err)
	}
	msg.Param1 = bytes // 消耗的宝石
	if gem != nil {
		msg.Param10 = gem.Id            // 合成的宝石id
		msg.Param11 = uint64(gem.SysId) // 合成的宝石sys_id
		attrs, err := JSONMarshal(genAttrData(gem.GemAttr.AttrId))
		if err != nil {
			l4g.Errorf("LogGemCompose json marshal error: %v", err)
		}
		msg.Param2 = attrs
		msg.Param12 = uint64(gem.Score) // 合成的宝石评分
	}
	if len(inheritAttrs) > 0 {
		attrs, err := JSONMarshal(genAttrData(inheritAttrs))
		if err != nil {
			l4g.Errorf("LogGemCompose json marshal error: %v", err)
		}
		msg.Param3 = attrs
	}
	srv.WriteLogMessage(msg)
}

func genAttrData(attrs []uint32) []*cl.Attr {
	attrDatas := make([]*cl.Attr, 0, len(attrs))
	for _, attr := range attrs {
		attrInfo := goxml.GetData().GemAttrInfoM.Index(attr)
		if attrInfo == nil {
			continue
		}
		attrDatas = append(attrDatas, &cl.Attr{
			Type:  attrInfo.AttrType1,
			Value: attrInfo.AttrValue1,
		})
		if attrInfo.AttrType2 > 0 {
			attrDatas = append(attrDatas, &cl.Attr{
				Type:  attrInfo.AttrType1,
				Value: attrInfo.AttrValue1,
			})
		}
	}

	return attrDatas
}

// LogGemConvert : 宝石系统-宝石置换
func (u *User) LogGemConvert(srv servicer, oldGem, newGem *cl.GemInfo, convertType uint32, lockAttrs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宝石系统-宝石置换"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GEM_CONVERT)
	msg.Param10 = uint64(newGem.SysId)   // 宝石系统id
	msg.Param11 = newGem.Id              // 宝石唯一id
	msg.Param12 = uint64(convertType)    // 置换类型
	msg.Param13 = uint64(len(lockAttrs)) // 锁定属性的条数
	msg.Param14 = uint64(oldGem.Score)
	msg.Param15 = uint64(newGem.Score)

	oldAttrs, err := JSONMarshal(genAttrData(oldGem.GemAttr.AttrId))
	if err != nil {
		l4g.Errorf("LogGemConvert json marshal error: %v", err)
	}
	msg.Param1 = oldAttrs // 洗炼前的宝石attr

	newAttrs, err := JSONMarshal(genAttrData(newGem.GemAttr.AttrId))
	if err != nil {
		l4g.Errorf("LogGemConvert json marshal error: %v", err)
	}
	msg.Param2 = newAttrs // 保存的宝石attr

	if len(lockAttrs) > 0 {
		lockAttrInfo, err := JSONMarshal(genAttrData(lockAttrs))
		if err != nil {
			l4g.Errorf("LogGemConvert json marshal error: %v", err)
		}
		msg.Param3 = lockAttrInfo // 锁定的属性
	}

	if newGem.TmpGemAttr != nil {
		newGenAttrs, err := JSONMarshal(genAttrData(newGem.TmpGemAttr.AttrId))
		if err != nil {
			l4g.Errorf("LogGemConvert json marshal error: %v", err)
		}
		msg.Param4 = newGenAttrs // 洗炼生成的宝石attr
	}

	srv.WriteLogMessage(msg)
}

// LogMazeGetMap : 迷宫-获取地图信息
func (u *User) LogMazeGetMap(srv servicer, mazePlayer *cl.MazePlayer) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-获取地图信息"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_GET_MAP)
	msg.Param10 = uint64(mazePlayer.SysId)           // 地图id
	msg.Param11 = uint64(mazePlayer.CurGridId)       // 当前格子id
	msg.Param12 = uint64(mazePlayer.MaxPower)        // 玩家历史最高战力
	msg.Param13 = uint64(mazePlayer.MapLevel)        // 地图等级
	bytesGrids, err := JSONMarshal(mazePlayer.Grids) // 地图格子信息
	if err != nil {
		l4g.Errorf("LogMazeGetMap json marshal error: %v", err)
	}
	msg.Param1 = bytesGrids
	srv.WriteLogMessage(msg)
}

// LogMazeTriggerEvent : 迷宫-触发格子事件
func (u *User) LogMazeTriggerEvent(srv servicer, mapLevel, param uint32, heroIds []uint64, grid *MazeGrid, battleResult bool,
	ctxID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-触发格子事件"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_TRIGGER_EVENT)
	msg.ContextID = ctxID
	if param > 0 {
		msg.Param10 = uint64(param)
		// 灵魂祭坛事件时param为选中的buff id
		// 选择事件时param为选中的answer id
		// 生命之泉选择治愈或复活 param = 1:复活，param = 2:治愈
	}
	msg.Param11 = uint64(grid.GetEventType()) // 事件类型
	switch grid.GetEventType() {
	case goxml.MazeLittleMonster, goxml.MazeCommonMonster, goxml.MazeEliteMonster, goxml.MazeBoss, goxml.MazeTreasuryGuard:
		msg.Param12 = uint64(util.If(battleResult, 1, 0)) // 战斗结果 (true为 1 false为 0)
	}
	if len(heroIds) > 0 {
		bytesHeroIds, err := JSONMarshal(heroIds)
		if err != nil {
			l4g.Errorf("LogMazeTriggerEvent json marshal error: %v", err)
		}
		msg.Param1 = bytesHeroIds // 魔盒事件: 英雄id
	}
	msg.Param13 = uint64(grid.GetGridID()) // 地图格子id
	msg.Param14 = uint64(mapLevel)         // 地图等级
	srv.WriteLogMessage(msg)
}

// LogMazeBattle : 迷宫-战斗
func (u *User) LogMazeFight(srv servicer, mazeID, eventType, mapLevel uint32, ctxID uint64, win bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_FIGHT)
	msg.ContextID = ctxID
	msg.Param10 = uint64(mazeID)             //地图id
	msg.Param11 = uint64(eventType)          // 事件类型
	msg.Param12 = uint64(util.If(win, 1, 0)) //战斗胜利
	msg.Param13 = uint64(mapLevel)           // 地图等级

	msg.Param20 = u.GuildID() //公会Id
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_MAZE)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

// LogMazeRecoveryHero : 迷宫-复活英雄
func (u *User) LogMazeRecoveryHero(srv servicer, mapLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-复活英雄"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_RECOVERY_HERO)
	msg.Param10 = uint64(mapLevel) // 地图等级

	srv.WriteLogMessage(msg)
}

// LogMazeGetGrid : 迷宫-获取格子信息
func (u *User) LogMazeGetGrid(srv servicer, grid *MazeGrid, ctxID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-获取格子信息"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_GET_GRID)
	if ctxID > 0 {
		msg.ContextID = ctxID
	}
	msg.Param10 = uint64(grid.GetEventType()) // 事件类型
	if grid != nil {
		msg.Param11 = uint64(grid.GetGridID())   // 地图格子id
		msg.Param12 = uint64(grid.GetQuestion()) // 选择事件的问题
		if grid.Enemy != nil {
			if grid.GetMonsterGroup() != 0 {
				msg.Param13 = uint64(grid.GetMonsterGroup())
			}
			if grid.GetOldEnemyID() != 0 {
				msg.Param13 = grid.GetOldEnemyID()
			}
		}
		msg.Param14 = uint64(grid.GetBoxID())
		msg.Param1, _ = JSONMarshal(grid.SoulBuffs) // 灵魂buff
	}

	srv.WriteLogMessage(msg)
}

// LogMazeBuyRevive : 迷宫-复活英雄
func (u *User) LogMazeBuyRevive(srv servicer, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-购买复生神像"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_BUY_REVIVE)
	msg.Param10 = uint64(count) // 购买复生神像次数
	srv.WriteLogMessage(msg)
}

// LogMazeUseItem : 迷宫-使用道具
func (u *User) LogMazeUseItem(srv servicer, itemID, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-使用道具"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_USE_ITEM)
	msg.Param10 = uint64(itemID) // 道具id
	msg.Param11 = uint64(count)  // 道具使用次数
	srv.WriteLogMessage(msg)
}

// LogMazeTaskReceiveAward : 迷宫-领取任务奖励
func (u *User) LogMazeTaskReceiveAward(srv servicer, taskIDs []uint32, level, score uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_TASK_RECEIVE_AWARD)
	//msg.Param10 = uint64(taskID) // 任务id
	msg.Param11 = uint64(level)          // 成绩等级
	msg.Param12 = uint64(score)          // 任务积分
	msg.Param1, _ = JSONMarshal(taskIDs) // 任务id
	srv.WriteLogMessage(msg)
}

func (u *User) LogMazeSelectBuff(srv servicer, gridID, eventType, buffID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-选择buff"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_SELECT_BUFF)
	msg.Param10 = uint64(gridID)    // 格子id
	msg.Param11 = uint64(eventType) // 事件类型
	msg.Param12 = uint64(buffID)    // buff id
	srv.WriteLogMessage(msg)
}

func (u *User) LogMazeSweep(srv servicer, level uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "迷宫-扫荡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MAZE_SWEEP)
	msg.Param10 = uint64(level) // 迷宫难度

	srv.WriteLogMessage(msg)
}

func (u *User) LogTowerFight(srv servicer, towerType, floor uint32, win, quick bool,
	battleOpType uint32, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "爬塔-战斗/扫荡/碾压"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TOWER_FIGHT)
	msg.Param10 = uint64(towerType)            //塔类型
	msg.Param11 = uint64(floor)                //层数
	msg.Param12 = uint64(util.If(win, 1, 0))   //战斗结果 1-胜 0-负
	msg.Param13 = uint64(battleOpType)         //战斗操作类型 1-战斗 2-扫荡 3-碾压
	msg.Param14 = uint64(util.If(quick, 1, 0)) //快速战斗 1-是 0-否
	msg.Param20 = u.GuildID()                  //公会Id
	if battleOpType == BattleOpTypeFight {
		msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_TOWER)))
		msg.Param3 = reportID
	}

	srv.WriteLogMessage(msg)
}

func (u *User) LogDungeonFight(srv servicer, id uint32, startTm int64, win, quick bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "主线-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DUNGEON_FIGHT)
	msg.Param10 = uint64(id)                   //关卡id
	msg.Param11 = uint64(startTm)              //挂机开始时间
	msg.Param12 = uint64(util.If(win, 1, 0))   //战斗结果 1-胜 0-负
	msg.Param13 = uint64(util.If(quick, 1, 0)) //快速战斗 1-是 0-否
	msg.Param20 = u.GuildID()                  //公会Id
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_DUNGEON)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogDungeonRecvAward(srv servicer, id uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "主线-领取挂机奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DUNGEON_RECV_AWARD)
	msg.Param10 = uint64(id) //关卡id
	srv.WriteLogMessage(msg)
}

func (u *User) LogDungeonSpeedRecvAward(srv servicer, id uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "主线-加速领取挂机奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DUNGEON_SPEED_RECV_AWARD)
	msg.Param10 = uint64(id) //关卡id
	srv.WriteLogMessage(msg)
}

func (u *User) LogArenaRefresh(srv servicer, tm int64, ctxID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "竞技场-刷新对手"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARENA_REFRESH)
	msg.Param10 = uint64(tm) //刷新时间
	msg.Param11 = ctxID
	msg.Param1, _ = JSONMarshal(u.ArenaOpponent().GetOpponentsForLog()) //对手uid
	srv.WriteLogMessage(msg)
}
func (u *User) LogArenaFight(srv servicer, opUID, ctxID uint64, seasonID uint32,
	oldRank uint32, score, rank uint32, win, bot bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "竞技场-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARENA_FIGHT)
	msg.ContextID = ctxID
	msg.Param10 = opUID                      //对手uid
	msg.Param11 = uint64(score)              //当前分数
	msg.Param12 = uint64(rank)               //当前排名
	msg.Param13 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param14 = uint64(util.If(bot, 1, 0)) //是否是机器人 1-是 0-否
	msg.Param15 = uint64(seasonID)           //赛季id
	msg.Param16 = uint64(oldRank)            //战斗前排名
	msg.Param20 = u.GuildID()                //公会id
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_ARENA_ATTACK)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogArenaBeFight(srv servicer, opUID, ctxID uint64, seasonID uint32,
	oldRank uint32, score, rank uint32, win, bot bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "竞技场-防守"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARENA_BE_FIGHT)
	msg.ContextID = ctxID
	msg.Param10 = opUID                      //对手uid
	msg.Param11 = uint64(score)              //当前分数
	msg.Param12 = uint64(rank)               //当前排名
	msg.Param13 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	// msg.Param14 = uint64(util.If(bot, 1, 0)) //是否是机器人 1-是 0-否
	msg.Param15 = uint64(seasonID) //赛季id
	msg.Param16 = uint64(oldRank)  //战斗前排名
	msg.Param20 = u.GuildID()      //公会id
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_ARENA_DEFENSE)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogArenaLike(srv servicer, opUID uint64, awardID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "竞技场-点赞"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARENA_LIKE)
	msg.Param10 = opUID           //对手uid
	msg.Param11 = uint64(awardID) //奖励id
	srv.WriteLogMessage(msg)
}

func (u *User) LogArenaRecvAward(srv servicer, taskAwardType []*log.ArenaDivisionTaskAward) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "竞技场-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARENA_RECV_AWARD)
	msg.Param1, _ = JSONMarshal(taskAwardType)
	srv.WriteLogMessage(msg)
}

func (u *User) LogFriendAdd(srv Servicer, users []*cl.ServerUser) {
	if len(users) == 0 {
		return
	}
	userIDs := make([]uint64, 0, len(users))
	for _, user := range users {
		userIDs = append(userIDs, user.Uid)
	}
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-申请"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_ADD)
	msg.Param1, _ = JSONMarshal(userIDs) //确认玩家ID列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogFriendConfirm(srv servicer, userIDs []uint64, accept bool, ctx uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-确认申请"
	msg.ContextID = ctx
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_CONFIRM)
	msg.Param10 = uint64(util.If(accept, 1, 0)) //是否接受(1:接受 0:拒绝)
	msg.Param1, _ = JSONMarshal(userIDs)        //确认玩家ID列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogFriendDelete(srv servicer, friendID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-删除好友"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_DELETE)
	msg.Param10 = friendID //删除好友id
	srv.WriteLogMessage(msg)
}

func (u *User) LogFriendBlacklist(srv servicer, friendID uint64, relation uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-拉黑"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_BLACKLIST)
	msg.Param10 = friendID         //好友ID
	msg.Param11 = uint64(relation) //拉黑前关系(0:陌生人 1:好友)
	srv.WriteLogMessage(msg)
}

func (u *User) LogFriendRemBlacklist(srv servicer, friendID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-删除黑名单"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_REMBLACKLIST)
	msg.Param10 = friendID //好友ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogFriendSendLike(srv servicer, tp uint32, friendIDs []uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-点赞"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_SEND_LIKE)
	msg.Param10 = uint64(tp)               //点赞类型(0个人,1全部)
	msg.Param1, _ = JSONMarshal(friendIDs) //好友ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogFriendRecvLike(srv servicer, tp uint32, friendIDs []uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-领取点赞"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_RECV_LIKE)
	msg.Param10 = uint64(tp)               //点赞类型(0个人,1全部)
	msg.Param1, _ = JSONMarshal(friendIDs) //好友ID
	srv.WriteLogMessage(msg)
}

// 支持离线玩家
func (u *User) LogFriendAdded(srv Servicer, userID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-被添加为好友"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_ADDED)
	msg.Param10 = userID //操作者ID
	srv.WriteLogMessage(msg)
}

// 支持离线玩家
func (u *User) LogFriendDeleted(srv Servicer, userID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-被他人删除好友"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_DELETED)
	msg.Param10 = userID //操作者ID
	srv.WriteLogMessage(msg)
}

// 支持离线玩家
func (u *User) LogFriendBlacklisted(srv Servicer, userID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-被好友拉黑"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_BLACKLISTED)
	msg.Param10 = userID //操作者ID
	srv.WriteLogMessage(msg)
}

// 支持离线玩家
func (u *User) LogFriendRemovedFromBlacklist(srv Servicer, userID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "好友-被好友移除黑名单"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRIEND_REMOVED_FROM_BLACKLIST)
	msg.Param10 = userID //操作者ID
	srv.WriteLogMessage(msg)
}

// LogArtifactActivate : 神器-激活
func (u *User) LogArtifactActivate(srv servicer, sysID uint32, score int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器-激活"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_ACTIVATE)
	msg.Param10 = uint64(sysID) //请求激活的神器系统id
	msg.Param11 = uint64(score) //神器评分
	srv.WriteLogMessage(msg)
}

// LogArtifactStarUp : 神器-升星
func (u *User) LogArtifactStarUp(srv servicer, sysID, oldStar, newStar uint32, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器-升星"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_STAR_UP)
	msg.Param10 = uint64(sysID)    // 请求升星的神器系统ID
	msg.Param11 = uint64(oldStar)  // 升星前的星级
	msg.Param12 = uint64(newStar)  // 升星后的星级
	msg.Param13 = uint64(oldScore) // 旧评分
	msg.Param14 = uint64(newScore) // 新评分
	srv.WriteLogMessage(msg)
}

// LogArtifactStrength : 神器-强化
func (u *User) LogArtifactStrength(srv servicer, sysID, addLv, oldStrength, newStrength uint32, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器-强化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_STRENGTH)
	msg.Param10 = uint64(sysID)       // 请求强化的神器系统ID
	msg.Param11 = uint64(addLv)       // 请求强化的等级
	msg.Param12 = uint64(oldStrength) // 强化前的等级
	msg.Param13 = uint64(newStrength) // 强化后的等级
	msg.Param14 = uint64(oldScore)    // 旧评分
	msg.Param15 = uint64(newScore)    // 新评分
	srv.WriteLogMessage(msg)
}

// LogArtifactForge : 神器-铸造
func (u *User) LogArtifactForge(srv servicer, sysID, addLv, oldForge, newForge uint32, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器-铸造"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_FORGE)
	msg.Param10 = uint64(sysID)    // 请求铸造的神器系统ID
	msg.Param11 = uint64(addLv)    // 请求铸造的等级
	msg.Param12 = uint64(oldForge) // 铸造前的等级
	msg.Param13 = uint64(newForge) // 铸造后的等级
	msg.Param14 = uint64(oldScore) // 旧评分
	msg.Param15 = uint64(newScore) // 新评分
	srv.WriteLogMessage(msg)
}

// LogArtifactRevive : 神器-重生
func (u *User) LogArtifactRevive(srv servicer, sysID, oldForge, oldStrength, newForge, newStrength uint32, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器-重生"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_REVIVE)
	msg.Param10 = uint64(sysID)       // 神器系统ID
	msg.Param11 = uint64(oldForge)    // 老的铸造等级
	msg.Param12 = uint64(oldStrength) // 老的强化等级
	msg.Param13 = uint64(newForge)    // 新的铸造等级
	msg.Param14 = uint64(newStrength) // 新的强化等级
	msg.Param15 = uint64(oldScore)    // 旧评分
	msg.Param16 = uint64(newScore)    // 新评分
	srv.WriteLogMessage(msg)
}

// LogGuildTalentReset : 公会天赋-职业线重置
func (u *User) LogGuildTalentReset(srv servicer, old, new *cl.GuildTalent) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会天赋-职业线重置"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_TALENT_RESET)
	msg.Param1, _ = JSONMarshal(old) // 重置前的天赋数据
	msg.Param2, _ = JSONMarshal(new) // 重置后的天赋数据

	srv.WriteLogMessage(msg)
}

// LogGuildTalentLevelUp : 公会天赋-职业线升级
func (u *User) LogGuildTalentLevelUp(srv servicer, job, oldLevel, newLevel uint32, score int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会天赋-职业线升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_TALENT_LEVEL_UP)
	msg.Param10 = uint64(job)      // 英雄职业
	msg.Param11 = uint64(oldLevel) // 老level
	msg.Param12 = uint64(newLevel) // 新level
	msg.Param13 = uint64(score)
	srv.WriteLogMessage(msg)
}

func (u *User) LogAvatarAdd(srv servicer, sysID uint32, startTime, endTime int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "设置-激活头像"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_AVATAR_ADD)
	msg.Param10 = uint64(sysID)     //avatar id
	msg.Param11 = uint64(startTime) //头像开始时间
	msg.Param12 = uint64(endTime)   //头像结束时间
	srv.WriteLogMessage(msg)
}

func (u *User) LogAvatarSetIcon(srv servicer, avatarIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "设置-修改头像"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_AVATAR_SET_ICON)
	if len(avatarIDs) != int(common.AVATAR_SET_INDEX_ASI_MAX) {
		l4g.Errorf("LogAvatarSetIcon: avatarIds error.len:%d", len(avatarIDs))
		return
	}
	msg.Param10 = uint64(avatarIDs[int(common.AVATAR_SET_INDEX_ASI_ICON)])  //头像id
	msg.Param11 = uint64(avatarIDs[int(common.AVATAR_SET_INDEX_ASI_FRAME)]) //头像框
	msg.Param12 = uint64(avatarIDs[int(common.AVATAR_SET_INDEX_ASI_IMAGE)]) //形象
	srv.WriteLogMessage(msg)
}

func (u *User) LogAvatarTimeExpand(srv servicer, sysID uint32, startTime, endTime int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "设置-延长头像时间"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_AVATAR_TIME_EXPAND)
	msg.Param10 = uint64(sysID)     //avatar id
	msg.Param11 = uint64(startTime) //头像开始时间
	msg.Param12 = uint64(endTime)   //头像结束时间
	srv.WriteLogMessage(msg)
}

// LogSummon : 抽卡（包括神器和英雄）
func (u *User) LogSummon(srv servicer, id, count uint32, oldTotalCount, oldSpecialCount,
	newTotalCount, newSpecialCount, linkSummonID, linkID uint32, guaranteeType []uint32, costs, awards, unTransform []*cl.Resource, wishHeroes []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "抽卡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SUMMON)
	msg.Param10 = uint64(id)              // 前端传的抽卡ID
	msg.Param11 = uint64(count)           // 抽卡次数
	msg.Param12 = uint64(oldTotalCount)   // 抽之前，此类型抽卡的总次数
	msg.Param13 = uint64(oldSpecialCount) // 抽之前，此类型抽卡的特殊次数记录
	msg.Param14 = uint64(newTotalCount)   // 抽之后，此类型抽卡的总次
	msg.Param15 = uint64(newSpecialCount) // 抽之后，此类型抽卡的特殊次数记录
	msg.Param16 = uint64(linkSummonID)    // 流派抽 期数ID
	msg.Param17 = uint64(linkID)          // 流派抽 羁绊ID
	if len(costs) > 1 {                   // todo 之后单次抽卡会消耗多种道具的话，增加相应处理（完美日志）后去掉。
		l4g.Errorf("user: %d log.LogSummon: cost resource type num error. num:%d", u.ID(), len(costs))
	}
	for index, cost := range costs {
		if index >= 1 {
			break
		}
		if cost.Type == uint32(common.RESOURCE_DIAMOND) { // 钻石的剩余数量不需要单独传惨
			continue
		}
		if cost.Type == uint32(common.RESOURCE_ITEM) {
			msg.Param16 = u.GetResourceCount(cost.Type, cost.Value) // 消耗道具，记录该道具剩余数量
		} else if cost.Type == uint32(common.RESOURCE_TOKEN) {
			msg.Param17 = u.GetResourceCount(cost.Type, cost.Value) // 消耗货币，记录该货币剩余数量
		} else {
			l4g.Errorf("user: %d log.LogSummon: cost resource type have no record. type:%d", u.ID(), cost.Type)
		}
	}
	msg.Param1, _ = JSONMarshal(costs)         //抽卡消耗
	msg.Param2, _ = JSONMarshal(awards)        //抽卡获得的资源
	msg.Param3, _ = JSONMarshal(guaranteeType) //抽卡对应的保底
	msg.Param4, _ = JSONMarshal(unTransform)   //未转换的的资源
	msg.Param5, _ = JSONMarshal(wishHeroes)    //是否为心愿英雄
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuidanceFinishStep(srv servicer, group uint32, node uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新手引导-完成节点"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUIDANCE_FINISH_NODE)
	msg.Param10 = uint64(group)
	msg.Param11 = uint64(node)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuidanceFinishGroup(srv servicer, group uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新手引导-完成组"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUIDANCE_FINISH_GROUP)
	msg.Param10 = uint64(group)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuidanceSelectSkipTag(srv servicer, isSkip bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新手引导-选择跳过标签"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUIDANCE_SELECT_SKIP_TAG)
	msg.Param10 = uint64(util.If(isSkip, 1, 0)) // 是否选择
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuidanceSkip(srv servicer, isSkip bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新手引导-跳过一部分引导"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUIDANCE_SKIP)
	msg.Param10 = uint64(util.If(isSkip, 1, 0)) // 是否跳过
	srv.WriteLogMessage(msg)
}

func (u *User) LogShopBuy(srv servicer, shopID, goodsID, num,
	discount uint32, costs, awards []*cl.Resource, shopType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "商店-购买商品"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SHOP_BUY)
	msg.Param10 = uint64(shopID)   //商店id
	msg.Param11 = uint64(goodsID)  //商品id
	msg.Param12 = uint64(num)      //商品数量
	msg.Param13 = uint64(discount) //todo 目前商店没有折扣，所以写死1为无折扣商品，之后有折扣按实际情况赋值
	if len(costs) != 0 && costs[0].Type != uint32(common.RESOURCE_DIAMOND) {
		msg.Param14 = u.GetResourceCount(costs[0].Type, costs[0].Value)
	}
	msg.Param15 = uint64(shopType)      //商店类型
	msg.Param1, _ = JSONMarshal(costs)  //花费的资源
	msg.Param2, _ = JSONMarshal(awards) //获得的资源
	srv.WriteLogMessage(msg)
}

func (u *User) LogShopRefresh(srv servicer, shopID uint32, refreshType uint32, isAuto bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "商店-刷新"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SHOP_REFRESH)
	msg.Param10 = uint64(shopID)                //商店id
	msg.Param11 = uint64(refreshType)           //刷新类型 1-免费 2-付费
	msg.Param12 = uint64(util.If(isAuto, 1, 0)) // 1-是自动刷新 0-否
	srv.WriteLogMessage(msg)
}

func (u *User) LogTaskReceiveAward(srv servicer, ids []uint32, taskType, newPoints uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "任务-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TASK_RECEIVE_AWARD)
	msg.Param1, _ = JSONMarshal(ids) //任务id
	msg.Param10 = uint64(taskType)   //任务重置类型
	msg.Param11 = uint64(newPoints)  //任务最新活跃度
	srv.WriteLogMessage(msg)
}

func (u *User) LogTrialFight(srv servicer, trialType, level, star uint32,
	win bool, battleOpType, sweepCount, formationID uint32, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "材料本-战斗/扫荡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TRIAL_FIGHT)
	msg.Param10 = uint64(trialType)          //材料本类型
	msg.Param11 = uint64(level)              //第几层
	msg.Param12 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param13 = uint64(star)               //本次战斗结果是几星
	msg.Param14 = uint64(battleOpType)       //战斗操作类型 1-战斗 2-扫荡
	msg.Param15 = uint64(sweepCount)         //扫荡次数
	msg.Param20 = u.GuildID()                //公会id
	if formationID > 0 {
		msg.Param3, _ = JSONMarshal(u.getLogFormationData(formationID))
	}
	msg.Param4 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogTrialOnHookReward(srv servicer, onhookUpLimit uint64, hook []*log.TrialOnHook) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "材料本-挂机奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TRIAL_ONHOOK_REWARD)
	msg.Param10 = onhookUpLimit
	msg.Param1, _ = JSONMarshal(hook)
	srv.WriteLogMessage(msg)
}

func (u *User) LogCarnivalReceiveAward(srv servicer, ids []uint32, carnivalID, addPoints uint32, oldPoints uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "嘉年华-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CARNIVAL_RECEIVE_AWARD)
	msg.Param1, _ = JSONMarshal(ids) //任务id
	msg.Param10 = uint64(carnivalID) //嘉年华类型
	msg.Param11 = uint64(addPoints)  //获得积分
	msg.Param12 = oldPoints          //本次获得前的积分
	srv.WriteLogMessage(msg)
}

func (u *User) LogReadMail(srv servicer, id, gid uint64, m *cl.Mail) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "邮件-读取"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_READ_MAIL)
	msg.Param10 = id               //邮件id
	msg.Param11 = uint64(m.BaseId) //邮件量表id
	msg.Param12 = uint64(m.Type)   //邮件类型
	// msg.Param1 =  todo 标题
	msg.Param2, _ = JSONMarshal(m.Awards)         //邮件附带的资源
	if m.BaseId == activity.MailIDGuildSendMail { // 公会邮件，记录公会ID和邮件内容
		msg.Param13 = gid
		if len(m.Params) == 3 { //nolint:mnd
			msg.Param3 = m.Params[2] ///nolint:mnd //邮件内容
		}
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogDrawMails(srv servicer, ids []uint64,
	mailMessage []*log.MailMessageForWm, awards map[uint64][]*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "邮件-领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DRAW_MAILS)
	msg.Param1, _ = JSONMarshal(ids)         //邮件ids
	msg.Param2, _ = JSONMarshal(mailMessage) //完美需要的日志信息
	msg.Param3, _ = JSONMarshal(awards)      //每个邮件对应的奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogDeleteMails(srv servicer, ids []uint64,
	mailMessage []*log.MailMessageForWm, awards map[uint64][]*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "邮件-删除"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DELETE_MAILS)
	msg.Param1, _ = JSONMarshal(ids)         //邮件ids
	msg.Param2, _ = JSONMarshal(mailMessage) //完美需要的日志信息
	msg.Param3, _ = JSONMarshal(awards)      //每个邮件对应的奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogRankAchieveRecvAward(srv servicer, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "排行成就-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RANK_ACHIEVE_RECV_AWARD)
	msg.Param1, _ = JSONMarshal(ids) //排行成就id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogMirageFight(srv servicer, id uint32, win, firstPass,
	best bool, battleOpType, fid uint32, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "个人boss-战斗/扫荡/碾压"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MIRAGE_FIGHT)
	msg.Param10 = uint64(id) //关卡id
	//msg.Param11 = uint64(star)                     //星数
	msg.Param12 = uint64(util.If(win, 1, 0))       //战斗结果 1-胜 0-负
	msg.Param13 = uint64(util.If(firstPass, 1, 0)) //是否首次过关 1-是 0-否
	msg.Param14 = uint64(util.If(best, 1, 0))      //是否最好成绩 1-是 0-否
	msg.Param15 = uint64(battleOpType)             //战斗操作类型 1-战斗 2-扫荡 3-碾压
	msg.Param20 = u.GuildID()                      //公会ID
	if fid > 0 {
		msg.Param2, _ = JSONMarshal(u.getLogFormationData(fid))
	}
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogMirageReceiveAward(srv servicer, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "个人boss-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MIRAGE_RECEIVE_AWARD)
	msg.Param1, _ = JSONMarshal(ids) //关卡id
	srv.WriteLogMessage(msg)
}

func (u *User) LogMirageSaveAffixes(srv servicer, id uint32, affixes []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "个人boss-保存词缀"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MIRAGE_SAVE_AFFIXES)
	msg.Param10 = uint64(id)             //关卡id
	msg.Param1, _ = JSONMarshal(affixes) //词缀
	srv.WriteLogMessage(msg)
}

func (u *User) LogAddPurchaseNum(srv servicer, typ, num, buyType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user模块-增加购买次数"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ADD_PURCHASE_NUM)
	msg.Param10 = uint64(typ)     //玩法次数类型
	msg.Param11 = uint64(num)     //数量
	msg.Param13 = uint64(buyType) //购买类型 1-普通购买 2-代金券购买
	srv.WriteLogMessage(msg)
}

func (u *User) LogFormation(srv servicer, data *cl.Formation) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user模块-布阵"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FORMATION)
	msg.Param1, _ = JSONMarshal(data) //阵容数据
	srv.WriteLogMessage(msg)
}

func (u *User) LogSetName(srv servicer, oldName, newName string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user模块-设置昵称"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SET_NAME)
	msg.Param1 = oldName //老名字
	msg.Param2 = newName //新名字
	srv.WriteLogMessage(msg)
}

func (u *User) LogDuel(srv servicer, enemyID, ctxID uint64, win bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user模块-切磋"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DUEL)
	msg.Param10 = enemyID                    //被切磋者-id
	msg.Param11 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param12 = ctxID
	srv.WriteLogMessage(msg)
}

func (u *User) LogAccusation(srv servicer, uid uint64, reason uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user模块-举报"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACCUSATION)
	msg.Param10 = uid            //被举报玩家id
	msg.Param11 = uint64(reason) //原因
	srv.WriteLogMessage(msg)
}

func (u *User) LogRecvH5DesktopReward(srv servicer) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user模块-领取保存h5桌面奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECV_H5_DESKTOP_REWARD)
	srv.WriteLogMessage(msg)
}

func (u *User) LogItemUse(srv servicer, id, count uint32, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "道具-使用"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ITEM_USE)
	msg.Param10 = uint64(id)            //道具id
	msg.Param11 = uint64(count)         //数量
	msg.Param1, _ = JSONMarshal(awards) //获得的资源
	srv.WriteLogMessage(msg)
}

func (u *User) LogItemSell(srv servicer, id, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "道具-出售"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ITEM_SELL)
	msg.Param10 = uint64(id)    //道具id
	msg.Param11 = uint64(count) //数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogItemSelect(srv servicer, id, count uint32, subItems []*cl.SubItem) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "道具-兑换自选礼包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ITEM_SELECT)
	msg.Param10 = uint64(id)              //道具id
	msg.Param11 = uint64(count)           //数量
	msg.Param1, _ = JSONMarshal(subItems) //所选道具信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogMedalReceiveAward(srv servicer, opType, level uint32, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "功勋-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MEDAL_RECEIVE_AWARD)
	msg.Param10 = uint64(opType) //领奖类型
	msg.Param11 = uint64(level)  //等级
	if len(taskIDs) > 0 {
		msg.Param1, _ = JSONMarshal(taskIDs) // 任务id
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogHandbooksAdd(srv servicer, typ uint32, sysID uint32, score int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "图鉴-新增"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HANDBOOKS_ADD)
	msg.Param10 = uint64(typ)   //图鉴类型
	msg.Param11 = uint64(sysID) //图鉴id
	msg.Param12 = uint64(score) //分数
	srv.WriteLogMessage(msg)
}

func (u *User) LogHandbooksActive(srv servicer, typ uint32, ids []uint32, score int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "图鉴-激活"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HANDBOOKS_ACTIVE)
	msg.Param10 = uint64(typ)        //图鉴类型
	msg.Param11 = uint64(score)      //分数
	msg.Param1, _ = JSONMarshal(ids) //id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogHandbooksReceiveAward(srv servicer, typ uint32, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "图鉴-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HANDBOOKS_RECV_AWARD)
	msg.Param10 = uint64(typ)        //图鉴类型
	msg.Param1, _ = JSONMarshal(ids) //id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogHandbooksActiveHeroAttr(srv servicer, linkBookTaskInfo *goxml.LinkBookInfoExt, heroHandbookM *HeroHandbookM) {
	if linkBookTaskInfo == nil || heroHandbookM == nil {
		return
	}
	msg := u.newLogMessage(srv)
	msg.TypeName = "图鉴-激活英雄羁绊属性"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HANDBOOKS_ACTIVE_HERO_ATTR)
	msg.Param10 = uint64(linkBookTaskInfo.HeroGroup)                              // 图鉴ID
	msg.Param11 = uint64(linkBookTaskInfo.Id)                                     // 属性ID
	msg.Param12 = uint64(linkBookTaskInfo.Grade)                                  // 增加的评分
	msg.Param13 = uint64(heroHandbookM.GetLinkScore(linkBookTaskInfo.LinkBookId)) // 羁绊组评分
	msg.Param14 = uint64(heroHandbookM.GetTotalScore())                           // 总评分
	linkBookInfo := goxml.GetData().LinkBookInfoM.Index(linkBookTaskInfo.LinkBookId)
	if linkBookInfo != nil {
		msg.Param15 = uint64(linkBookInfo.LinkType)                                 // 羁绊组类型
		msg.Param16 = uint64(heroHandbookM.GetLinkTypeScore(linkBookInfo.LinkType)) // 羁绊组类型评分
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogFragmentCompose(srv servicer, id, num uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "碎片-碎片合成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FRAGMENT_COMPOSE)
	msg.Param10 = uint64(id)  //碎片id
	msg.Param11 = uint64(num) //合成数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogChat(srv servicer, sender, receiver uint64, channel uint32, content string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "聊天-聊天"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CHAT)
	msg.Param10 = sender          //发送者
	msg.Param11 = receiver        //接收者
	msg.Param12 = uint64(channel) //聊天频道
	msg.Param1 = content          //发送的内容
	srv.WriteLogMessage(msg)
}

func (u *User) LogChatLike(srv servicer, msgID uint64, msgType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "聊天-点赞"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CHAT_LIKE)
	msg.Param10 = msgID
	msg.Param11 = uint64(msgType) //接收者
	srv.WriteLogMessage(msg)
}

func (u *User) LogCrystalAddHero(srv servicer, hid uint64, slotID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "水晶-添加共鸣英雄"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CRYSTAL_ADD_HERO)
	msg.Param10 = hid            //英雄id
	msg.Param11 = uint64(slotID) //槽位id
	srv.WriteLogMessage(msg)
}

func (u *User) LogCrystalRemoveHero(srv servicer, hid uint64, slotID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "水晶-移出共鸣英雄"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CRYSTAL_REMOVE_HERO)
	msg.Param10 = hid            //英雄id
	msg.Param11 = uint64(slotID) //槽位id
	srv.WriteLogMessage(msg)
}

func (u *User) LogCrystalUnlockSlot(srv servicer, slotID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "水晶-解锁槽位"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CRYSTAL_UNLOCK_SLOT)
	msg.Param10 = uint64(slotID) //槽位id
	srv.WriteLogMessage(msg)
}

func (u *User) LogCrystalSpeedSlotCD(srv servicer, slotID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "水晶-加速完成槽位冷却"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CRYSTAL_SPEED_SLOT_C_D)
	msg.Param10 = uint64(slotID) //槽位id
	srv.WriteLogMessage(msg)
}

func (u *User) LogCrystalInit(srv servicer) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "水晶-数据初始化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CRYSTAL_INIT)
}

func (u *User) LogCrystalActiveAchievement(srv servicer, ids, activedList []uint32, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "水晶-激活成就"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_CRYSTAL_ACTIVE_ACHIEVEMENT)
	msg.Param1, _ = JSONMarshal(ids)         //本次要激活的成就id列表
	msg.Param2, _ = JSONMarshal(activedList) //全部已激活的成就id列表
	msg.Param11 = uint64(oldScore)
	msg.Param12 = uint64(newScore)
	srv.WriteLogMessage(msg)
}

// func (u *User) LogCrystalBlessingLevelUp(srv servicer, currentLevel, currentExp uint32, oldScore, newScore int64) {
// 	msg := u.newLogMessage(srv)
// 	msg.TypeName = "水晶-祝福升级"
// 	msg.LogSubType = uint32(log.SUB_TYPE_ID_CRYSTAL_BLESSING_LEVEL_UP)
// 	msg.Param10 = uint64(currentLevel) //当前祝福等级
// 	msg.Param11 = uint64(currentExp)   //当前祝福经验
// 	msg.Param12 = uint64(oldScore)     //升级前分数
// 	msg.Param13 = uint64(newScore)     //升级后分数
// 	srv.WriteLogMessage(msg)
// }

func (u *User) LogOrderProcess(srv servicer, order *db.Order, firstOrder bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "订单-订单发货"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ORDER_PROCESS)
	msg.Param1, _ = JSONMarshal(order)
	msg.Param10 = uint64(util.If(firstOrder, 1, 0)) // 第一份订单，1-是，0-否
	srv.WriteLogMessage(msg)
}

func (u *User) LogRechargeNormal(srv servicer, order *db.Order, goodsId uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "充值-普通充值"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECHARGE_NORMAL)
	msg.Param1 = order.OrderId
	msg.Param3 = order.ProductId
	msg.Param10 = uint64(goodsId) //充值档位id
	srv.WriteLogMessage(msg)
}

func (u *User) LogRechargeFirstGift(srv servicer, order *db.Order, giftId uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "充值-首充礼包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECHARGE_FIRST_GIFT)
	msg.Param1 = order.OrderId
	msg.Param10 = uint64(giftId) //首充礼包id
	srv.WriteLogMessage(msg)
}

func (u *User) LogForecastReceiveAward(srv servicer, forecastID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新功能预告-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FORECAST_RECEIVE_AWARD)
	msg.Param10 = uint64(forecastID) //功能id
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityRechargeBuy(srv servicer, shopID, giftID uint32, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "限时礼包-购买"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_RECHARGE_BUY)
	msg.Param10 = uint64(shopID)        //商店id
	msg.Param11 = uint64(giftID)        //商品id
	msg.Param1, _ = JSONMarshal(awards) //获得的资源
	srv.WriteLogMessage(msg)
}

func (u *User) LogOrderRefund(srv servicer, order *db.Order, reduceCoupon uint64, leftCoupon int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "订单-退款"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ORDER_REFUND)
	msg.Param1, _ = JSONMarshal(order)
	msg.Param2, _ = JSONMarshal(leftCoupon) //扣除后剩余代金券数量
	msg.Param10 = reduceCoupon              //扣除代金券数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogTowerstarFight(srv servicer, dungeonID, formationID uint32, starInfo uint32, quick bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "条件爬塔-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TOWERSTAR_FIGHT)
	msg.Param10 = uint64(dungeonID)
	msg.Param11 = uint64(starInfo)
	msg.Param12 = uint64(util.If(quick, 1, 0)) //快速战斗 1-是 0-否
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(formationID))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogMonthlyCardReceiveAward(srv servicer, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "月卡-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MONTHLY_CARD_RECEIVE_AWARD)
	msg.Param10 = uint64(sysID) //月卡id
	srv.WriteLogMessage(msg)
}

func (u *User) LogMonthlyCardRecharge(srv servicer, sysID uint32, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "月卡-充值"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MONTHLY_CARD_RECHARGE)
	msg.Param10 = uint64(sysID)         // 月卡ID
	msg.Param1, _ = JSONMarshal(awards) // 奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogVipBuyGift(srv servicer, giftLv, status uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "vip-购买等级礼包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_VIP_BUY_GIFT)
	msg.Param10 = uint64(giftLv)
	msg.Param11 = uint64(status)
	srv.WriteLogMessage(msg)
}

func (u *User) LogVipBuyRechargeGift(srv servicer, vip uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "vip-购买付费礼包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_VIP_RECHARGE_GIFT)
	msg.Param10 = uint64(vip) // 月卡ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogRecvShareAward(srv servicer, num uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user模块-领取分享奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECV_SHARE_AWARD)
	msg.Param10 = uint64(num) //数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogPassRecharge(srv servicer, sysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "战令-购买"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PASS_RECHARGE)
	msg.Param10 = uint64(sysID) // 战令ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogPassReceiveAward(srv servicer, sysID uint32, receiveID []uint32, charge uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "战令-领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PASS_RECEIVE_AWARD)
	msg.Param10 = uint64(sysID) // 战令ID
	msg.Param11 = charge
	msg.Param1, _ = JSONMarshal(receiveID) //接收奖励的ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogPassLevelBuy(srv servicer, sysID uint32, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "战令-等级购买"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PASS_LEVEL_BUY)
	msg.Param10 = uint64(sysID)          // 战令ID
	msg.Param1, _ = JSONMarshal(taskIDs) //任务id
	srv.WriteLogMessage(msg)
}

func (u *User) LogPushGiftCreate(srv servicer, giftInfo *goxml.ActivityPushgiftInfo, GroupInfo *goxml.ActivityPushgiftGroupInfo,
	expireTime int64, product *goxml.RechargeProductInfo, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "推送礼包-生成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PUSH_GIFT_CREATE)
	msg.Param10 = uint64(giftInfo.Id)        // 礼包ID
	msg.Param11 = uint64(GroupInfo.Id)       // 礼包组ID
	msg.Param12 = uint64(expireTime)         //过期时间
	msg.Param13 = uint64(product.OpId)       //op_id
	msg.Param14 = uint64(product.OpGroupId)  //op_group_id
	msg.Param15 = uint64(product.InternalId) //内部ID
	msg.Param16 = uint64(product.Amount)     //商品金额
	msg.Param17 = uint64(count)              //当前数量
	msg.Param18 = uint64(giftInfo.Trigger)   //触发条件

	msg.Param1 = GroupInfo.SdkPid  //sdk_pid
	msg.Param2 = product.ProductId //product_id
	msg.Param3 = product.Currency  //商品货币
	srv.WriteLogMessage(msg)
}

func (u *User) LogPushGiftDel(srv servicer, sysID uint32, groupID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "推送礼包-删除"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PUSH_GIFT_DEL)
	msg.Param10 = uint64(sysID)   // 礼包ID
	msg.Param11 = uint64(groupID) // 礼包组ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogPushGiftRecharge(srv servicer, giftInfo *goxml.ActivityPushgiftInfo, GroupInfo *goxml.ActivityPushgiftGroupInfo,
	expireTime int64, product *goxml.RechargeProductInfo, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "推送礼包-充值"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PUSH_GIFT_RECHARGE)
	msg.Param10 = uint64(giftInfo.Id)        // 礼包ID
	msg.Param11 = uint64(GroupInfo.Id)       // 礼包组ID
	msg.Param12 = uint64(expireTime)         //过期时间
	msg.Param13 = uint64(product.OpId)       //op_id
	msg.Param14 = uint64(product.OpGroupId)  //op_group_id
	msg.Param15 = uint64(product.InternalId) //内部ID
	msg.Param16 = uint64(product.Amount)     //商品金额
	msg.Param17 = uint64(count)              //当前数量
	msg.Param18 = uint64(giftInfo.Trigger)   //触发条件

	msg.Param1 = GroupInfo.SdkPid  //sdk_pid
	msg.Param2 = product.ProductId //product_id
	msg.Param3 = product.Currency  //商品货币
	srv.WriteLogMessage(msg)
}

func (u *User) LogGetGiftCodeAward(srv servicer, code string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "领取礼包码奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GIFT_CODE)
	msg.Param1 = code //礼包码
	srv.WriteLogMessage(msg)
}

func (u *User) LogRateScore(srv servicer, score uint32, content string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "评分"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RATE_SCORE)
	msg.Param10 = uint64(score) // 分数
	msg.Param1 = content        // 评论内容
	srv.WriteLogMessage(msg)
}

func (u *User) LogVipLevelUp(srv servicer, oldVip, oldVipExp, newVipExp uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "user-vip升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_VIP_UP)
	msg.Param10 = uint64(oldVip)
	msg.Param11 = uint64(oldVipExp)
	msg.Param12 = uint64(newVipExp)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSetWishList(srv servicer, slot, wishID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "心愿单-设置"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SUMMON_SET_WISH_LIST)
	msg.Param10 = uint64(slot)
	msg.Param11 = uint64(wishID)
	srv.WriteLogMessage(msg)
}

func (u *User) LogOperateActivityGiftRecharge(srv servicer, actID, goodID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动礼包-充值"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_OPERATE_GIFT_RECHARGE)
	msg.Param10 = uint64(actID)
	msg.Param11 = uint64(goodID)
	srv.WriteLogMessage(msg)
}

func (u *User) LogOperateActivityGiftInit(srv servicer, actID uint32, amount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "配置礼包活动-初始化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_OPERATE_ACTIVITY_GIFT_INTI)
	msg.Param10 = uint64(actID)
	msg.Param11 = uint64(amount)
	srv.WriteLogMessage(msg)
}

func (u *User) LogOperateActivityTaskReceived(srv servicer, actID uint32, taskID []uint32, logTasks []*log.OperateActivityTask) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "配置活动-任务奖励领取"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_OPERATE_TASK_RECEIVED)
	msg.Param10 = uint64(actID)           //活动ID
	msg.Param1, _ = JSONMarshal(taskID)   //任务id
	msg.Param2, _ = JSONMarshal(logTasks) //配置活动任务信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogPromotionGiftInit(srv servicer, actID uint32, promotionGift *cl.PromotionGift) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新推送礼包-初始化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PROMOTION_GIFT_INIT)
	msg.Param10 = uint64(actID)                //活动ID
	msg.Param1, _ = JSONMarshal(promotionGift) //初始化信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogPromotionGiftSelectAward(srv servicer, actID, giftID, awardIndex, targetIndex uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新推送礼包-自选礼包选择奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PROMOTION_GIFT_SELECT_AWARD)
	msg.Param10 = uint64(actID)       //活动ID
	msg.Param11 = uint64(giftID)      //礼包ID
	msg.Param12 = uint64(awardIndex)  //奖励位置
	msg.Param13 = uint64(targetIndex) // 选择的物品位置
	srv.WriteLogMessage(msg)
}

func (u *User) LogPromotionGiftRecharge(srv servicer, actID, goodID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新推送礼包-充值"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PROMOTION_GIFT_RECHARGE)
	msg.Param10 = uint64(actID)
	msg.Param11 = uint64(goodID)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSetAccountTag(srv servicer, tag, muteValue uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "gm-设置账号标识"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SET_ACCOUNT_TAG)
	msg.Param10 = uint64(tag) // 账号类型
	msg.Param11 = uint64(muteValue)
	srv.WriteLogMessage(msg)
}

func (u *User) LogDispatchLevelUpdate(srv servicer, level uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "悬赏等级提升"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISPATCH_LEVEL_UP)
	msg.Param11 = uint64(level)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessLevelUp(srv servicer, goddessID, level uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "女神等级提升"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_LEVEL_UP)
	msg.Param11 = uint64(goddessID)
	msg.Param12 = uint64(level)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessContractLevelUp(srv servicer, oldLevel, newLevel uint32, oldScore, newScore int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "女神圣所等级提升"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_CONTRACT_LEVEL_UP)
	msg.Param11 = uint64(oldLevel)
	msg.Param12 = uint64(newLevel)
	msg.Param13 = uint64(oldScore)
	msg.Param14 = uint64(newScore)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessInit(srv servicer, goddessId uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "女神初始化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_INIT)
	msg.Param11 = uint64(goddessId)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessFeed(srv servicer, goddessID uint32, exp uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "女神喂养"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_FEED)
	msg.Param11 = uint64(goddessID)
	msg.Param12 = uint64(exp)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessTouch(srv servicer, goddessID uint32, exp uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "女神抚摸"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_TOUCH)
	msg.Param11 = uint64(goddessID)
	msg.Param12 = uint64(exp)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessChapterFight(
	srv servicer, goddessID uint32, chapterID uint32, win bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "契约之所女武神-章节战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_CHAPTER_FIGHT)
	msg.Param10 = uint64(goddessID)          // 女武神ID
	msg.Param11 = uint64(chapterID)          // 章节ID
	msg.Param12 = uint64(util.If(win, 1, 0)) // 是否胜利
	msg.Param20 = u.GuildID()                //公会ID
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_GODDESS_CONTRACT)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessChapterFinish(srv servicer, goddessID uint32, chapterID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "契约之所女武神-章节完成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_CHAPTER_FINISH)
	msg.Param10 = uint64(goddessID) // 女武神ID
	msg.Param11 = uint64(chapterID) // 章节ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessUnlockChapter(srv servicer, goddessID uint32, chapterID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "契约之所女武神-章节完成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_UNLOCK_CHAPTER)
	msg.Param10 = uint64(goddessID) // 女武神ID
	msg.Param11 = uint64(chapterID) // 章节ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessChapterTakeReward(srv servicer, goddessID uint32, chapterIDs []uint32, status uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "契约之所-章节领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_CHAPTER_TAKE_REWARD)
	msg.Param10 = uint64(goddessID) // 女武神ID
	//msg.Param11 = uint64(chapterIDs) // 章节ID
	msg.Param12 = uint64(status)                           // 奖励领取状态
	msg.Param1 = util.Uint32SliceToString(chapterIDs, ",") // 章节ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessStoryTakeReward(srv servicer, goddessID uint32, storyId uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "契约之所-故事领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_STORY_REWARD)
	msg.Param10 = uint64(goddessID) // 女武神ID
	msg.Param11 = uint64(storyId)   // 故事ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogWrestleFight(srv servicer, opUID, opSID, ctxID uint64, level uint32,
	myChange, opChange *cl.WrestleUserScoreAndRank, win, promoted bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神树争霸-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WRESTLE_FIGHT)
	msg.ContextID = ctxID
	msg.Param10 = opUID
	msg.Param11 = uint64(level)
	msg.Param12 = uint64(util.If(win, 1, 0))
	msg.Param13 = opSID
	msg.Param14 = uint64(util.If(promoted, 1, 0))
	msg.Param1, _ = JSONMarshal(myChange)
	msg.Param2, _ = JSONMarshal(opChange)
	msg.Param3, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_CROSS_ARENA_ATTACK)))
	msg.Param4 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGoddessCollection(srv servicer, collections map[uint32]uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "契约之所-收集藏品"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GODDESS_COLLECTION)
	msg.Param1, _ = JSONMarshal(collections)
	srv.WriteLogMessage(msg)
}

func (u *User) LogWrestleBeFight(srv servicer, opUID, ctxID uint64,
	oldRank, rank uint32, win bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神树争霸-防守"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WRESTLE_BE_FIGHT)
	msg.ContextID = ctxID
	msg.Param10 = opUID                      //对手uid
	msg.Param11 = uint64(rank)               //当前排名
	msg.Param12 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param13 = uint64(oldRank)            //战斗前排名
	msg.Param1, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)))
	srv.WriteLogMessage(msg)
}

func (u *User) LogWrestleLike(srv servicer, opUID uint64, rank uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神树争霸-点赞"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WRESTLE_LIKE)
	msg.Param10 = opUID
	msg.Param11 = uint64(rank)
	srv.WriteLogMessage(msg)
}

func (u *User) LogWrestleRecvLevelAward(srv servicer, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神树争霸-领取赛场奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WRESTLE_RECV_LEVEL_AWARD)
	msg.Param1, _ = JSONMarshal(ids)
	srv.WriteLogMessage(msg)
}

func (u *User) LogWrestleChangeRoom(srv servicer, tm int64, success bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神树争霸-更换房间"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WRESTLE_CHANGE_ROOM)
	msg.Param10 = uint64(tm)
	msg.Param11 = uint64(util.If(success, 1, 0)) //是否更换成功 1-是 0-否
	srv.WriteLogMessage(msg)
}

func (u *User) LogDivineDemonSummon(srv servicer, activityID, summonType, poolID, activityType, upRedHeroCount, redHeroCount uint32, summon *cl.DivineDemonSummonV2, awards []*cl.Resource, guaranteeWays []uint32, wishHeroes []uint32, costType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神魔抽卡-抽卡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DIVINE_DEMON_SUMMON)
	msg.Param10 = uint64(activityID) // 活动系统id
	msg.Param11 = uint64(summonType) // 抽卡类型
	if summon != nil {
		msg.Param12 = uint64(summon.TotalSummonCount) // 抽卡总次数
	}
	msg.Param14 = uint64(upRedHeroCount) // 心愿将的数量
	msg.Param15 = uint64(activityType)   // 活动类型
	msg.Param16 = uint64(poolID)         // 卡池id
	msg.Param17 = uint64(redHeroCount)   // 红卡的数量
	msg.Param18 = uint64(costType)       // 消耗类型

	msg.Param1, _ = JSONMarshal(awards)        // 抽取到的资源
	msg.Param3, _ = JSONMarshal(guaranteeWays) // 抽卡对应的保底
	msg.Param4, _ = JSONMarshal(wishHeroes)    // 心愿英雄

	srv.WriteLogMessage(msg)
}

func (u *User) LogDivineDemonReceiveTaskAward(srv servicer, id uint64, activityID, taskType uint32, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神魔抽卡-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DIVINE_DEMON_RECEIVE_TASK_AWARD)
	msg.Param10 = uint64(activityID)     // 活动id
	msg.Param11 = uint64(taskType)       // 任务类型
	msg.Param12 = id                     // 活动唯一ID
	msg.Param1, _ = JSONMarshal(taskIDs) // 任务id
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerRevengeSnatch(srv servicer, logID, opUID, ctxID uint64, win bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-掠夺复仇"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_REVENGE)
	msg.ContextID = ctxID
	msg.Param10 = logID                                 //日志id
	msg.Param11 = opUID                                 //掠夺者id
	msg.Param12 = uint64(util.If(win, 1, 0))            //战斗结果 1-胜 0-负
	msg.Param13 = uint64(common.FLOWER_LOG_FL_SNATCHED) //复仇类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerRevengeOccupy(srv servicer, logID, opUID, ctxID uint64, win bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-据点复仇"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_REVENGE)
	msg.ContextID = ctxID
	msg.Param10 = logID                                 //日志id
	msg.Param11 = opUID                                 //掠夺者id
	msg.Param12 = uint64(util.If(win, 1, 0))            //战斗结果 1-胜 0-负
	msg.Param13 = uint64(common.FLOWER_LOG_FL_OCCUPIED) //复仇类型
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerOccupyAttack(srv servicer, uid, ctxID uint64, win, isBot bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-攻打据点"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_OCCUPY_ATTACK)
	msg.ContextID = ctxID
	msg.Param10 = uid                        //被抢者id
	msg.Param11 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param13 = u.GuildID()
	msg.Param14 = uint64(util.If(isBot, 1, 0)) //是否是机器人 1-胜 0-负
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_FLOWER_ATTACK)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerOccupyBeAttack(srv servicer, opUID, ctxID uint64, win bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-据点被攻打"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_OCCUPY_BE_ATTACK)
	msg.ContextID = ctxID
	msg.Param10 = opUID                      //对手uid
	msg.Param11 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param13 = u.GuildID()                //公会id
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerExtendOccupyTime(srv servicer, occupyData *cl.FlowerOccupy) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-延长据点占领时间"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_EXTEND_OCCUPY_TIME)
	msg.Param10 = uint64(occupyData.JungleId)     //丛林id
	msg.Param11 = uint64(occupyData.FlowerbedPos) //花坛位置
	msg.Param12 = uint64(occupyData.EndTm)        //新的结束时间
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerBuyOccupyAttackNum(srv servicer, num uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-购买据点进攻次数"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_BUY_OCCUPY_ATTACK_NUM)
	msg.Param10 = uint64(num)
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerRecvOccupyAward(srv servicer, exp, currentExp uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-领取据点采集奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_RECV_OCCUPY_AWARD)
	msg.Param10 = uint64(exp)        //领取到的经验
	msg.Param11 = uint64(currentExp) //当前总经验
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerLevelUp(srv servicer, level, oldLevel, currentExp uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-密林升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_LEVEL_UP)
	msg.Param10 = uint64(level)      //领取到的经验
	msg.Param11 = uint64(oldLevel)   //之前的等级
	msg.Param12 = uint64(currentExp) //当前总经验
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerAttackLevelGuard(srv servicer, monsterGroup uint32, win bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-攻打升级守卫"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_ATTACK_LEVEL_GUARD)
	msg.Param10 = uint64(monsterGroup) //怪物ID
	msg.Param11 = util.If64(win, 1, 0) //战斗结果 1-胜 0-负
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerStartFeed(srv servicer, currentGoblin, nextGoblin, treeType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-开始献祭"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_START_FEED)
	msg.Param10 = uint64(currentGoblin) //当前哥布林
	msg.Param11 = uint64(nextGoblin)    //下个哥布林
	msg.Param12 = uint64(treeType)      //树的类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerFeedGoblin(srv servicer, currentGoblin, nextGoblin, score, addScore, treeType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-献祭哥布林"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_FEED_GOBLIN)
	msg.Param10 = uint64(currentGoblin) //当前哥布林
	msg.Param11 = uint64(nextGoblin)    //下个哥布林
	msg.Param12 = uint64(score)         //种子分数
	msg.Param13 = uint64(addScore)      //本次增加的分数
	msg.Param14 = uint64(treeType)      //树的类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerChangeGoblin(srv servicer, currentGoblin, nextGoblin, treeType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-更换哥布林"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_CHANGE_GOBLIN)
	msg.Param10 = uint64(currentGoblin) //当前哥布林
	msg.Param11 = uint64(nextGoblin)    //下个哥布林
	msg.Param12 = uint64(treeType)      //树的类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerFeedSpecial(srv servicer, oldScore, treeType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-使用道具，购买最高品质的种子"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_FEED_SPECIAL)
	msg.Param10 = uint64(oldScore) //原分数
	msg.Param11 = uint64(treeType) //树的类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerStartPlant(srv servicer, score, treeType uint32,
	startTm, endTm int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-种树"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_START_PLANT)
	msg.Param10 = uint64(score)    //种子分数
	msg.Param11 = uint64(startTm)  //树的开始时间
	msg.Param12 = uint64(endTm)    //树的成熟时间
	msg.Param13 = uint64(treeType) //树的类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerSpeedGrow(srv servicer, treeType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-加速生长"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_SPEED_GROW)
	msg.Param10 = uint64(treeType) //树的类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerHarvest(srv servicer, level, treeType uint32, exp uint64, isCrit bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-收获资源"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_HARVEST)
	msg.Param10 = uint64(level)                 //最新密林等级
	msg.Param11 = exp                           //最新密林经验
	msg.Param12 = uint64(util.If(isCrit, 1, 0)) //是否暴击 1-是 0-否
	msg.Param13 = uint64(treeType)              //树的类型
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerSearch(srv servicer, score uint32,
	enemyID, ctxID uint64, isBot bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-搜索"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_SEARCH)
	msg.Param10 = uint64(score) //种子分数
	msg.Param11 = ctxID
	msg.Param12 = enemyID
	msg.Param13 = uint64(util.If(isBot, 1, 0)) //是否是机器人 1-胜 0-负
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerSnatch(srv servicer, uid, ctxID uint64, win, isBot bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-协助"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_SNATCH)
	msg.ContextID = ctxID
	msg.Param10 = uid                        //被抢者id
	msg.Param11 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param13 = u.GuildID()
	msg.Param14 = uint64(util.If(isBot, 1, 0)) //是否是机器人 1-胜 0-负
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_FLOWER_ATTACK)))
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerBeSnatch(srv servicer, opUID, ctxID uint64, win bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-被协助"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_BE_SNATCH)
	msg.ContextID = ctxID
	msg.Param10 = opUID                      //对手uid
	msg.Param11 = uint64(util.If(win, 1, 0)) //战斗结果 1-胜 0-负
	msg.Param13 = u.GuildID()                //公会id
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerRecvPreviewOccupyAward(srv servicer, exp, currentExp uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-领取据点预览奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_RECV_PREVIEW_OCCUPY_AWARD)
	msg.Param10 = uint64(exp)        //领取到的经验
	msg.Param11 = uint64(currentExp) //当前总经验
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerAssistSendLike(srv servicer, logIds []uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-协助模式发送友情点"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_SEND_LIKE)
	msg.Param1, _ = JSONMarshal(logIds) //进行发送操作的日志列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogFlowerAssistRecvLike(srv servicer, logIds []uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "密林-协助模式收取友情点"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FLOWER_RECV_LIKE)
	msg.Param1, _ = JSONMarshal(logIds) //进行收取操作的日志列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDungeonChapterRankLike(srv servicer, gid uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会副本-章节排行点赞"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DUNGEON_CHAPTER_RANK_LIKE)
	msg.Param10 = gid
	srv.WriteLogMessage(msg)
}

func (u *User) LogMonthTasksRecvAwards(srv servicer, cmsg *cl.C2L_MonthTasksRecvAwards, points uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "全民无双-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MONTH_TASKS_RECV_AWARD)
	msg.Param10 = uint64(cmsg.Id)
	msg.Param11 = uint64(util.If(cmsg.RecvDailyAward, 1, 0)) // 是否领取了每日奖励
	msg.Param12 = uint64(points)                             // 当前拥有积分
	msg.Param1, _ = JSONMarshal(cmsg.TaskIds)                // 任务id
	srv.WriteLogMessage(msg)
}

func (u *User) LogBattleReport(srv servicer, logInfo *ReportLogInfo) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "战斗-战报"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_FIGHT_REPORT)
	msg.Param10 = uint64(util.If(logInfo.Win, 1, 0)) // 是否胜利
	msg.Param11 = uint64(logInfo.FuncID)             // 玩法id
	msg.Param1 = logInfo.ID                          // 战报id
	srv.WriteLogMessage(msg)
}

func (u *User) LogBattleReportDetails(srv servicer, index int, btMgr *battle.Manager, report *bt.MultipleTeamsReport) {
	eventConfig := goxml.GetData().FormationInfoM.Index(report.FormationId)
	if eventConfig == nil {
		l4g.Errorf("FormationInfo cant find %d", report.FormationId)
		return
	}
	fightReportMsg := u.newLogMessage(srv)
	fightReportMsg.TypeName = "战斗-战斗细节"
	fightReportMsg.LogSubType = uint32(log.SUB_TYPE_ID_FIGHT_REPORT_DETAILS)
	fightReportMsg.Param10 = uint64(util.If(report.Reports[index].ReportInfo.Win, 1, 0)) // 是否胜利
	fightReportMsg.Param11 = uint64(index)                                               //场次
	fightReportMsg.Param1 = report.Id                                                    // 战报id
	fightReportMsg.Param2, _ = JSONMarshal(u.getFightReportInfo(btMgr))
	fightReportMsg.Param3 = eventConfig.DbName
	srv.WriteLogMessage(fightReportMsg)
}

func (u *User) getFightReportInfo(btMgr *battle.Manager) *log.Formation {
	ret := &log.Formation{
		Teams: make([]*log.Team, 2),
	}

	ret.Teams[0] = u.getFightReportDetails(btMgr.GetTeamByIndex(battle.AttackTeam))
	ret.Teams[0].Rite = btMgr.GetReport().AttackRite
	ret.Teams[0].Remains = btMgr.GetReport().GetAttackRemains()
	ret.Teams[1] = u.getFightReportDetails(btMgr.GetTeamByIndex(battle.DefenseTeam))
	ret.Teams[1].Rite = btMgr.GetReport().DefenseRite
	ret.Teams[1].Remains = btMgr.GetReport().GetDefenseRemains()
	return ret
}

func (u *User) getFightReportDetails(team *battle.Team) *log.Team {
	teamDetails := &log.Team{
		Power: team.GetBaseInfo().Power,
	}

	for _, mem := range team.GetAllMembers() {
		logHero := &log.Hero{
			Id:          strconv.FormatUint(mem.GetUniqID(), 10),
			SysId:       mem.GetSysID(),
			Star:        mem.GetStar(),
			Level:       mem.GetLevel(),
			Stage:       mem.GetStage(),
			Pos:         mem.Pos(),
			Power:       strconv.FormatUint(mem.GetPower(), 10),
			AwakenLevel: mem.GetAwakenLevel(),
			SkinId:      mem.GetDress(),
		}

		for _, gem := range mem.GemInfo {
			if gem.Slot == 1 {
				logHero.Gem1Level = gem.Level
			} else if gem.Slot == 2 {
				logHero.Gem2Level = gem.Level
			}
		}
		teamDetails.Heroes = append(teamDetails.Heroes, logHero)
		for _, id := range mem.EmblemID {
			if id == 0 {
				continue
			}
			teamDetails.Emblems = append(teamDetails.Emblems, &log.Emblem{
				HeroSysId: mem.GetSysID(),
				SysId:     id,
			})
		}
		logTag := mem.GetLogTag()
		logAddition := &log.HeroGemAndEmblemAddition{
			Hid:              strconv.FormatUint(mem.GetUniqID(), 10),
			Pos:              mem.Pos(),
			GemLink:          util.BitAndUint32(logTag, goxml.GemLink),
			GemPasSkill_1:    util.BitAndUint32(logTag, goxml.GemPasSkill1),
			GemPasSkill_2:    util.BitAndUint32(logTag, goxml.GemPasSkill2),
			EmblemLink:       util.BitAndUint32(logTag, goxml.EmblemLink),
			EmblemPasSkill_1: util.BitAndUint32(logTag, goxml.EmblemPasSkill1),
			//策划废弃该技能
			//EmblemPasSkill_2:       util.BitAndUint32(logTag, goxml.EmblemPasSkill2),
			//EmblemPasSkill_3:       util.BitAndUint32(logTag, goxml.EmblemPasSkill3),
			EmblemExclusiveSkill_1: util.BitAndUint32(logTag, goxml.EmblemExclusiveSkill1),
			EmblemExclusiveSkill_2: util.BitAndUint32(logTag, goxml.EmblemExclusiveSkill2),
			EmblemExclusiveSkill_3: util.BitAndUint32(logTag, goxml.EmblemExclusiveSkill3),
		}
		if util.BitAndUint32(logTag, goxml.EmblemTwoSuit) {
			logAddition.EmblemSuitNum = 2
		} else if util.BitAndUint32(logTag, goxml.EmblemFourSuit) {
			logAddition.EmblemSuitNum = 4
		}
		if util.BitAndUint32(logTag, goxml.EmblemExclusive1) {
			logAddition.EmblemExclusiveNum = 1
		} else if util.BitAndUint32(logTag, goxml.EmblemExclusive2) {
			logAddition.EmblemExclusiveNum = 2
		} else if util.BitAndUint32(logTag, goxml.EmblemExclusive3) {
			logAddition.EmblemExclusiveNum = 3
		} else if util.BitAndUint32(logTag, goxml.EmblemExclusive4) {
			logAddition.EmblemExclusiveNum = 4
		}
		teamDetails.Addition = append(teamDetails.Addition, logAddition)
	}
	for _, v := range team.GetArtifactM().GetArtifactInfos() {
		teamDetails.Artifacts = append(teamDetails.Artifacts, &log.Artifact{
			SysId: v.SysId,
			Pos:   v.Pos,
		})
	}

	//羁绊
	for _, v := range team.GetActivedLinks() {
		teamDetails.Links = append(teamDetails.Links, &log.Link{
			SysId: v.LinkId,
			Num:   v.Num,
		})
	}

	return teamDetails
}

func (u *User) LogArtifactDebutSetWish(srv servicer, uniqID, sysActID, wishAid uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器首发-设置心愿神器"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_DEBUT_SET_WISH)
	msg.Param10 = uint64(uniqID)   //活动唯一id
	msg.Param11 = uint64(sysActID) //活动量表id
	msg.Param12 = uint64(wishAid)  //心愿神器id
	srv.WriteLogMessage(msg)
}

func (u *User) LogArtifactDebutSummon(srv servicer, uniqID, sysActID uint32,
	data *log.ArtifactDebutSummon, guaranteeTypes []uint32, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器首发-抽卡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_DEBUT_SUMMON)
	msg.Param10 = uint64(uniqID)   //活动唯一id
	msg.Param11 = uint64(sysActID) //活动量表id
	msg.Param1, _ = JSONMarshal(data)
	msg.Param2, _ = JSONMarshal(guaranteeTypes) //保底类型
	msg.Param3, _ = JSONMarshal(awards)         //奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogArtifactDebutRecvActAward(srv servicer, uniqID, sysActID, actType uint32, recvIDs uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器首发-活动领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_DEBUT_RECV_ACT_AWARD)
	msg.Param10 = uint64(uniqID)   //活动唯一id
	msg.Param11 = uint64(sysActID) //活动量表id
	msg.Param12 = uint64(actType)  //活动类型 1-累登 2-累抽 3-拼图
	msg.Param13 = recvIDs          //最新已领奖的id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogArtifactDebutRecvTaskAward(srv servicer, uniqID, sysActID uint32, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器首发-任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_DEBUT_RECV_TASK_AWARD)
	msg.Param10 = uint64(uniqID)     //活动唯一id
	msg.Param11 = uint64(sysActID)   //活动量表id
	msg.Param1, _ = JSONMarshal(ids) //任务id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogArtifactDebutOpenPuzzle(srv servicer, uniqID, sysActID uint32, openIDs uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器首发-拼图掀开格子"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_DEBUT_OPEN_PUZZLE)
	msg.Param10 = uint64(uniqID)   //活动唯一id
	msg.Param11 = uint64(sysActID) //活动量表id
	msg.Param12 = openIDs          //最新已掀开的id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDonate(srv servicer, oldGuildLevel, oldGuildExp, newLevel, newExp uint32, guildID uint64, guildDonatePoint uint32, activityPoint, id uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-公会捐赠"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DONATE)
	msg.Param10 = uint64(oldGuildLevel)    // 前公会的等级
	msg.Param11 = uint64(oldGuildExp)      // 前公会的经验
	msg.Param12 = uint64(newLevel)         // 后公会的等级
	msg.Param13 = uint64(newExp)           // 后公会的经验
	msg.Param14 = uint64(guildDonatePoint) // 公会捐赠点数
	msg.Param15 = guildID                  // 公会ID
	msg.Param16 = uint64(activityPoint)    // 公会会用户活跃度
	msg.Param17 = uint64(id)               // 公会用户捐赠ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildDonateReceive(srv servicer, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会-公会捐赠领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_DONATE_RECEIVE)
	msg.Param1, _ = JSONMarshal(ids) //悬赏id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogRoundActivityRecvTaskAward(srv servicer, ids []uint32,
	from uint64, category, stage uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新服轮次活动-任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ROUND_ACTIVITY_RECV_TASK_AWARD)
	msg.Param10 = from               //0-玩家领奖 1-补发奖励
	msg.Param11 = uint64(category)   //活动类别
	msg.Param12 = uint64(stage)      //活动轮次
	msg.Param1, _ = JSONMarshal(ids) //任务id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogWebRechargeDiamond(srv servicer, order *db.Order, goodsId, amount uint32, payTime int64, isSandbox bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "官网充值-钻石"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECHARGE_WEB_DIAMOND)
	msg.Param1 = order.OrderId
	msg.Param10 = uint64(goodsId) //商品id
	msg.Param11 = uint64(amount)  // 金额
	msg.Param12 = uint64(payTime)
	msg.Param13 = uint64(util.If(isSandbox, 1, 0))
	srv.WriteLogMessage(msg)
}

func (u *User) LogWebRechargeGift(srv servicer, order *db.Order, goodsId, amount uint32, payTime int64, isSandbox bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "官网充值-礼包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECHARGE_WEB_GIFT)
	msg.Param1 = order.OrderId
	msg.Param10 = uint64(goodsId) //商品id
	msg.Param11 = uint64(amount)  // 金额
	msg.Param12 = uint64(payTime)
	msg.Param13 = uint64(util.If(isSandbox, 1, 0))
	srv.WriteLogMessage(msg)
}

func (u *User) LogWebLargeRecharge(srv Servicer, rechargeParams *gm.WebLargeRechargeMailParams, mailId uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "官网充值-大额充值"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL)
	msg.Param10 = uint64(rechargeParams.Amount)
	msg.Param11 = uint64(rechargeParams.OpId)
	msg.Param12 = uint64(rechargeParams.PayTime)
	msg.Param13 = mailId
	msg.Param14 = uint64(util.If(rechargeParams.IsSandbox, 1, 0))
	msg.Param1 = rechargeParams.OrderId
	srv.WriteLogMessage(msg)
}

func (u *User) LogRiteMarkCollection(srv servicer, markId, markRare, markCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "永恒仪式-印记收集"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RITE_MARK_COLLECTION)
	msg.Param13 = uint64(markId)    //印记ID
	msg.Param14 = uint64(markRare)  //印记品质
	msg.Param15 = uint64(markCount) //印记数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogRiteRecycle(srv servicer, recyclePoint uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "永恒仪式-阵法回收"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RITE_RECYCLE)
	msg.Param11 = uint64(recyclePoint) //回收分数
	srv.WriteLogMessage(msg)
}

func (u *User) LogTowerSeasonFight(srv servicer, dungeonInfo *goxml.TowerSeasonDungeonInfoExt, season uint32, quick, win bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "百塔-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TOWER_SEASON_FIGHT)
	msg.Param10 = uint64(dungeonInfo.Id)       //关卡ID
	msg.Param11 = uint64(dungeonInfo.Type)     //难度
	msg.Param12 = uint64(util.If(quick, 1, 0)) //快速战斗 1-是 0-否
	msg.Param13 = uint64(util.If(win, 1, 0))   //是否胜利 1-是 0-否
	msg.Param14 = uint64(season)
	msg.Param15 = uint64(dungeonInfo.Floor)
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_TOWER_SEASON)))
	msg.Param3 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogTowerSeasonRecvTaskAward(srv servicer, ids []uint32, from uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "百塔-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TOWER_SEASON_RECV_TASK_AWARD)
	msg.Param10 = from               //0-玩家领奖 1-补发奖励
	msg.Param1, _ = JSONMarshal(ids) //任务id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogQuestionnaireFinish(srv servicer, questionnaireID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "问卷-完成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_QUESTIONNAIRE_FINISH)
	msg.Param10 = uint64(questionnaireID) //问卷ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGodPresentRecvItem(srv servicer, id uint32, groups []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "777抽活动-领取抽卡券"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GOD_PRESENT_RECV_ITEM)
	msg.Param10 = uint64(id)            //卡池ID
	msg.Param1, _ = JSONMarshal(groups) //卡组顺序列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogGodPresentSummon(srv servicer, id, summonCount uint32, hero_sys_ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "777抽活动-抽卡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GOD_PRESENT_SUMMON)
	msg.Param10 = uint64(id)                  //卡池ID
	msg.Param11 = uint64(summonCount)         //第x次抽卡
	msg.Param1, _ = JSONMarshal(hero_sys_ids) //抽中英雄id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogGodPresentRecvAwards(srv servicer, id, groupKey uint32, hero_sys_ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "777抽活动-选定卡组，领取抽卡活动奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GOD_PRESENT_RECV_AWARDS)
	msg.Param10 = uint64(id)                  //卡池ID
	msg.Param11 = uint64(groupKey)            //选中第x组抽卡结果 >=1
	msg.Param1, _ = JSONMarshal(hero_sys_ids) //选中英雄id列表
	srv.WriteLogMessage(msg)
}

func (u *User) LogDropActivityExchange(srv servicer, smsg *cl.L2C_DropActivityExchange, actID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "掉落活动-兑换"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DROP_ACTIVITY_EXCHANGE)
	msg.Param10 = uint64(smsg.Id)     //活动唯一id
	msg.Param11 = uint64(smsg.RuleId) //兑换规则序号id
	msg.Param12 = uint64(smsg.Count)  //兑换次数
	msg.Param13 = uint64(actID)       //活动量表id
	srv.WriteLogMessage(msg)
}

func (u *User) LogDailyAttendanceRecvAward(srv servicer, round uint32, extraAward bool, params []*cl.DailyAttendanceRecvParam) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "累登-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DAILY_ATTENDANCE_RECV_AWARD)
	msg.Param10 = uint64(round)                     //轮次
	msg.Param11 = uint64(util.If(extraAward, 1, 0)) //领取额外大奖 1-是 0-否
	msg.Param1, _ = JSONMarshal(params)             // 领奖的天数和第几次领奖
	srv.WriteLogMessage(msg)
}

func (u *User) LogDailySpecialRecvAward(srv servicer, shopID uint32, slots []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "每日特惠-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DAILY_SPECIAL_RECV_AWARD)
	msg.Param10 = uint64(shopID)
	msg.Param1, _ = JSONMarshal(slots) // 领奖的slot
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildChestRecvAward(srv servicer, id, guildId uint64, sysId uint32, diamond uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会红包-领取公会红包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_CHEST_RECV_AWARD)
	msg.Param10 = uint64(guildId)
	msg.Param11 = uint64(sysId)
	msg.Param12 = uint64(diamond)
	msg.Param13 = uint64(id)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildChestSetLike(srv servicer, id, guildId uint64, sysId uint32, flower uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会红包-点赞公会红包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_CHEST_SET_LIKE)
	msg.Param10 = uint64(guildId)
	msg.Param11 = uint64(sysId)
	msg.Param12 = uint64(flower)
	msg.Param13 = uint64(id)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildChestActivate(srv servicer, id, guildId uint64, sysId uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会红包-激活红包"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_CHEST_ACTIVATE)
	msg.Param10 = uint64(guildId)
	msg.Param11 = uint64(sysId)
	msg.Param12 = uint64(id)
	srv.WriteLogMessage(msg)
}

func (u *User) LogWorldBossSelectLevel(srv servicer, data *cl.WorldBossData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "世界boss-选择等级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WORLD_BOSS_SELECT_LEVEL)
	msg.Param1, _ = JSONMarshal(data)

	srv.WriteLogMessage(msg)
}

func (u *User) LogWorldBossTaskRecvAward(srv servicer, sysID uint32, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "世界boss-任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WORLD_BOSS_TASK_RECV_AWARD)
	msg.Param10 = uint64(sysID)
	msg.Param1, _ = JSONMarshal(taskIDs)

	srv.WriteLogMessage(msg)
}

func (u *User) LogWorldBossFight(srv servicer, sysID, fightType, roomID, costType, level uint32, hurt uint64, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "世界boss-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_WORLD_BOSS_FIGHT)
	msg.Param10 = uint64(sysID)
	msg.Param11 = uint64(fightType)
	msg.Param12 = uint64(roomID)
	msg.Param13 = uint64(costType)
	msg.Param14 = hurt
	msg.Param15 = uint64(level)
	msg.Param1, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_WORLD_BOSS)))
	msg.Param2 = reportID
	srv.WriteLogMessage(msg)
}

func (u *User) LogSkinUse(srv servicer, skinID, heroSysID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "皮肤-使用"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SKIN_USE)
	msg.Param10 = uint64(skinID)    //皮肤id
	msg.Param11 = uint64(heroSysID) //英雄量表id

	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityStoryLoginAward(srv servicer, actId uint32, loginIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动故事-持续登录领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_STORY_LOGIN_AWARD)
	msg.Param10 = uint64(actId)           //活动故事量表ID
	msg.Param1, _ = JSONMarshal(loginIDs) //每日登录的领奖ID

	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityStoryExchangeAward(srv servicer, id, actID, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动故事-活动兑换"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_STORY_EXCHANGE)

	msg.Param10 = uint64(id)    //活动故事兑换表ID
	msg.Param11 = uint64(actID) //活动故事量表ID
	msg.Param12 = uint64(count) //活动故事本次兑换次数
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityStoryFight(srv servicer, actID, dungeonID, fightType uint32, win bool, formation uint32, reportID string, sweepCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动故事-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_STORY_FIGHT)

	msg.Param10 = uint64(dungeonID)          //关卡ID
	msg.Param11 = uint64(actID)              //活动ID
	msg.Param12 = uint64(fightType)          //战斗类型 1.挑战 2.扫荡
	msg.Param13 = uint64(util.If(win, 1, 0)) //是否胜利 1-是 0-否
	msg.Param14 = uint64(sweepCount)         //扫荡次数
	if formation > 0 && fightType == uint32(common.ACTIVITY_STORY_FIGHT_TYPE_ASFT_FIGHT) {
		msg.Param1, _ = JSONMarshal(u.getLogFormationData(formation))
		msg.Param3 = reportID
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogAssistanceActivityRecvAward(srv servicer, actId uint32, loginIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "助力活动-领取助力奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ASSISTANCE_ACTIVITY_RECV_AWARD)
	msg.Param10 = uint64(actId)           //助力活动量表ID
	msg.Param1, _ = JSONMarshal(loginIDs) //助力活动领奖ID

	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLevelUp(srv servicer, oldLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季等级-升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LEVEL_UP)
	msg.Param13 = uint64(oldLevel)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLevelRecvLvAwards(srv servicer, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季等级-领取等级奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LEVEL_RECV_LV_AWARDS)
	msg.Param1, _ = JSONMarshal(ids) //领奖ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLevelRecvTaskAwards(srv servicer, taskIDs []uint32, finishTaskNum int) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季等级-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LEVEL_RECV_TASK_AWARDS)
	msg.Param1, _ = JSONMarshal(taskIDs) //领奖ID
	msg.Param10 = uint64(finishTaskNum)
	srv.WriteLogMessage(msg)
}

func (u *User) LogDisorderLandTriggerEvent(srv servicer, difficulty, mapID, nodeID, eventID uint32, node *cl.DisorderlandNode) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "失序空间-触发事件"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISORDER_LAND_TRIGGER_EVENT)
	msg.Param10 = uint64(difficulty)  //难度
	msg.Param11 = uint64(mapID)       //地图 id
	msg.Param12 = uint64(nodeID)      //节点 id
	msg.Param13 = uint64(eventID)     //事件 id
	msg.Param1, _ = JSONMarshal(node) // 节点信息
	srv.WriteLogMessage(msg)
}

func (u *User) LogDisorderLandFight(srv servicer, fightType, difficulty, hurdleType, hurdleID, hurdleLevel uint32, win bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "失序空间-战斗事件"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISORDER_LAND_FIGHT)
	msg.Param10 = uint64(fightType)          //战斗类型
	msg.Param11 = uint64(difficulty)         //难度
	msg.Param12 = uint64(hurdleType)         //关卡类型
	msg.Param13 = uint64(hurdleID)           //关卡id
	msg.Param14 = uint64(util.If(win, 1, 0)) //是否胜利 1-是 0-否
	msg.Param15 = uint64(hurdleLevel)        // 关卡等级
	if fightType == uint32(common.DISORDER_LAND_BATTLE_DLB_BATTLE) {
		fID := uint32(0)
		switch hurdleType {
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_A):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_A)
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_B):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_B)
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_C):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_C)
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_D):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_D)
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_E):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_E)
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_F):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_F)
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_G):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_G)
		case uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_H):
			fID = uint32(common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_H)
		}
		if fID != 0 {
			msg.Param1, _ = JSONMarshal(u.getLogFormationData(fID))
		}

		msg.Param2 = reportID
	}

	srv.WriteLogMessage(msg)
}

func (u *User) LogDisorderLandBuyStamina(srv servicer, buyType, num uint32, recoveryTime int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "失序空间-购买体力"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISORDER_LAND_BUY_STAMINA)
	msg.Param10 = uint64(buyType)      //购买类型
	msg.Param11 = uint64(num)          //购买数量
	msg.Param12 = uint64(recoveryTime) //恢复时间
	srv.WriteLogMessage(msg)
}

func (u *User) LogDisorderLandTriggerGuarantee(srv servicer, dungeonId, runeLevel uint32, awards []*cl.Resource, reportId string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "失序空间-触发保底"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DISORDER_LAND_TRIGGER_GUARANTEE)
	msg.Param1 = reportId               // 战报id
	msg.Param2, _ = JSONMarshal(awards) // 获得的奖励
	msg.Param10 = uint64(dungeonId)     // 关卡id
	msg.Param11 = uint64(runeLevel)     // 符石等级

	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityReturnTakeLoginAwards(srv servicer, returnId, dayIndex uint32, info *cl.ActivityReturnLogin) {
	if info == nil {
		return
	}
	msg := u.newLogMessage(srv)
	msg.TypeName = "回流-领取登录奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS)
	msg.Param10 = uint64(returnId)          //领取的id
	msg.Param11 = uint64(dayIndex)          //领取的第几天
	msg.Param12 = uint64(info.DayCount)     //累计登录天数
	msg.Param13 = uint64(info.RewardStatus) //领取后奖励领取状态
	srv.WriteLogMessage(msg)
}

func (u *User) LogPeakRecvInviteReward(srv servicer) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "巅峰竞技场-领取邀请奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PEAK_RECV_INVITE_REWARD)
	srv.WriteLogMessage(msg)
}

func (u *User) LogPeakFight(srv servicer, reportID string, win bool, leftScore,
	rightScore, matchID, phase, fid uint32, rightUid uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "巅峰竞技场-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PEAK_FIGHT)
	msg.Param1 = reportID
	msg.Param10 = uint64(leftScore)
	msg.Param11 = uint64(rightScore)
	msg.Param12 = uint64(util.If(win, 1, 0)) // 是否胜利
	msg.Param16 = uint64(matchID)
	msg.Param17 = rightUid
	msg.Param18 = uint64(phase)
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(fid))
	srv.WriteLogMessage(msg)
}

func (u *User) LogPeakDoGuess(srv servicer, guessData *cl.PeakUserGuess) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "巅峰竞技场-竞猜下注"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PEAK_DO_GUESS)
	msg.Param10 = uint64(guessData.MatchId)
	msg.Param11 = uint64(guessData.Group)
	msg.Param12 = uint64(guessData.WinnerUid)
	msg.Param13 = uint64(guessData.Count)
	srv.WriteLogMessage(msg)
}

func (u *User) LogPeakWorship(srv servicer) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "巅峰竞技场-膜拜"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PEAK_WORSHIP)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonDungeonFight(srv servicer, reportID string, win bool, dungeonInfo *goxml.SeasonDungeonInfoExt) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季主线-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_DUNGEON_FIGHT)
	msg.Param1 = reportID
	msg.Param10 = uint64(dungeonInfo.Id)
	msg.Param11 = uint64(dungeonInfo.DungeonType)
	msg.Param12 = uint64(util.If(win, 1, 0)) // 是否胜利
	msg.Param13 = uint64(dungeonInfo.Type)
	nextInfo := goxml.GetData().SeasonDungeonInfoM.GetNextInfo(dungeonInfo.Id, dungeonInfo.SeasonId)
	if nextInfo == nil || nextInfo.Chapter != dungeonInfo.Chapter {
		msg.Param14 = 1 // 代表本章节通关
	}
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_SEASON_DUNGEON)))
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonDungeonRecv(srv servicer, receiveID []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季主线-领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_DUNGEON_RECV)
	msg.Param1, _ = JSONMarshal(receiveID) //接收奖励的ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogPreSeasonRecv(srv servicer, recvID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季前-领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PRE_SEASON_RECV)
	msg.Param11 = uint64(recvID)
	srv.WriteLogMessage(msg)
}

func (u *User) LogDeleteUserResources(srv Servicer, delResources []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "gm-删除资源"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GM_DELETE_RESOURCES)
	msg.Param1, _ = JSONMarshal(delResources)
	srv.WriteLogMessage(msg)
}

func (u *User) LogRechargeCoupon(srv Servicer, awards []*cl.Resource, orderId string, rechargeType, awardType uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "充值-充值代金券"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_RECHARGE_COUPON)
	msg.Param1 = orderId                //订单id
	msg.Param2, _ = JSONMarshal(awards) //奖励
	msg.Param10 = rechargeType          //充值类型 10-游戏内充值代金券 11-官网充值代金券
	msg.Param11 = awardType             //奖励类型 1-一般奖励 2-额外奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonReturnAddAwards(srv servicer, awardId int64, returnId, dayCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季回流-发放奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_RETURN_ADD_AWARDS)
	msg.Param10 = uint64(awardId)  //奖励id
	msg.Param11 = uint64(returnId) //回流id
	msg.Param12 = uint64(dayCount) //回流天数
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonReturnTakeAwards(srv servicer, awardId int64, returnId, dayCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季回流-领取奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_RETURN_TAKE_AWARDS)
	msg.Param10 = uint64(awardId)  //奖励id
	msg.Param11 = uint64(returnId) //回流id
	msg.Param12 = uint64(dayCount) //回流天数
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLinkActivation(srv servicer, seasonLinkId, heroSysId uint32, activeNum int) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季养成-羁绊激活"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LINK_ACTIVATION)
	msg.Param10 = uint64(seasonLinkId) //羁绊id
	msg.Param11 = uint64(heroSysId)    //英雄sysId
	msg.Param12 = uint64(activeNum)    // 激活英雄羁绊的数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLinkRuneCollection(srv servicer, runeId, uniqueLevel, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季养成-符石收集"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LINK_RUNE_COLLECTION)
	msg.Param10 = uint64(runeId)      //符石id
	msg.Param11 = uint64(uniqueLevel) //玩法等级
	msg.Param12 = uint64(count)       //数量
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLinkMonumentCultivation(srv servicer, monumentId, runePos, runeId, runeLevel, monumentLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季养成-丰碑养成"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LINK_MONUMENT_CULTIVATION)
	msg.Param1, _ = JSONMarshal(&log.SeasonLinkRune{
		Position:    runePos,   //符石位置
		SysId:       runeId,    //符石id
		UniqueLevel: runeLevel, //符石玩法等级
	})
	msg.Param10 = uint64(monumentId)    //丰碑id
	msg.Param11 = uint64(monumentLevel) //丰碑玩法等级

	maxRare, maxRareNum := u.SeasonLink().getMaxRareAndNum()
	msg.Param12 = uint64(maxRare)
	msg.Param13 = uint64(maxRareNum)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLinkMonumentRecvRareAwards(srv servicer, monumentId uint32, uniqueLevels []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季养成-赛季养成品质奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LINK_MONUMENT_RECV_RARE_AWARDS)
	msg.Param1, _ = JSONMarshal(uniqueLevels) // 丰碑玩法等级
	msg.Param10 = uint64(monumentId)          // 丰碑id
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableFight(srv servicer, fightMsg *l2c.CS2L_GSTFight, sub []*log.LogGstBattleSub, defense *User) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_FIGHT)
	msg.GuildID = fightMsg.LogGeneral.GuildId                                         // 公会ID
	msg.Param1 = fightMsg.Fight.ReportId                                              // 战报ID
	msg.Param10 = uint64(fightMsg.LogGeneral.Turn)                                    // 回合
	msg.Param11 = uint64(fightMsg.LogGeneral.Round)                                   //	轮次
	msg.Param12 = uint64(srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA))) //	战区
	msg.Param13 = fightMsg.LogGeneral.Room                                            // 房间ID
	msg.Param14 = uint64(util.If(fightMsg.Fight.IsWin, 1, 0))
	if defense == nil {
		msg.Param15 = uint64(fightMsg.Fight.Defense.MonsterId)
		msg.Param19 = 1
	} else {
		defenseLog := &log.GstDefenseInfo{
			Uid:      defense.ID(),
			ServerId: defense.ServerID(),
			GuildId:  defense.GuildID(),
		}
		msg.Param4, _ = JSONMarshal(defenseLog)
	}
	msg.Param16 = uint64(fightMsg.LogGeneral.DonatePoint)
	msg.Param17 = uint64(fightMsg.SpaceId)
	msg.Param18 = uint64(fightMsg.SpaceType)
	msg.Param20 = uint64(fightMsg.Fight.Attack.TeamIndex)
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_GST)))
	if len(sub) > 0 {
		msg.Param5, _ = JSONMarshal(sub[0])
	}
	if len(sub) > 1 {
		msg.Param6, _ = JSONMarshal(sub[1])
	}
	fightExt := &log.GSTFightExt{
		RoomQuality: fightMsg.LogGeneral.RoomQuality,
		ArenaRound:  fightMsg.Fight.ArenaRound,
	}
	msg.Extra, _ = JSONMarshal(fightExt)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableNotice(srv servicer, notice string, guildID uint64, turn, round, battleZone uint32, room uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-修改公会战提示"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_MODIFY_NOTICE)
	msg.GuildID = guildID            // 公会ID
	msg.Param1 = notice              // 公告
	msg.Param10 = uint64(turn)       // 回合
	msg.Param11 = uint64(round)      //	轮次
	msg.Param12 = uint64(battleZone) //	战区
	msg.Param13 = room               // 房间ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableRecvTask(srv servicer, taskIds []uint32, guildID uint64, turn, round, battleZone uint32, room uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-接受任务"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_TASK)
	msg.GuildID = guildID                // 公会ID
	msg.Param1, _ = JSONMarshal(taskIds) // 接受的任务IDs
	msg.Param10 = uint64(turn)           // 回合
	msg.Param11 = uint64(round)          //	轮次
	msg.Param12 = uint64(battleZone)     //	战区
	msg.Param13 = room                   // 房间ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableBless(srv servicer, donateDetail []*cl.GSTClientDonate, guildID uint64, turn, round, battleZone uint32, room uint64,
	blessId uint32, donatePoint, thisDonatePoint uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-祝福"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BLESS)
	msg.GuildID = guildID                     // 公会ID
	msg.Param1, _ = JSONMarshal(donateDetail) // 接受的任务IDs
	msg.Param10 = uint64(turn)                // 回合
	msg.Param11 = uint64(round)               // 轮次
	msg.Param12 = uint64(battleZone)          // 战区
	msg.Param13 = room                        // 房间ID
	msg.Param14 = uint64(blessId)             // 祝福ID
	msg.Param15 = uint64(donatePoint)         // 个人贡献度
	msg.Param16 = uint64(thisDonatePoint)     // 本次个人贡献积分
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableHost(srv servicer, teamIds []uint32, guildID uint64, turn, round, battleZone uint32, room uint64, isHost bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-托管或取消托管"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_HOST)
	msg.GuildID = guildID                       // 公会ID
	msg.Param1, _ = JSONMarshal(teamIds)        //	受操作的队伍
	msg.Param10 = uint64(turn)                  // 回合
	msg.Param11 = uint64(round)                 // 轮次
	msg.Param12 = uint64(battleZone)            // 战区
	msg.Param13 = room                          // 房间ID
	msg.Param14 = uint64(util.If(isHost, 1, 0)) // true 托管 false 取消托管
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableDispatch(srv servicer, logGeneral *cl.GstLogGeneral, exchangeTeam []*log.GSTDispatchTeam, isAuto bool) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-派遣队伍"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_DISPATCH)
	msg.GuildID = logGeneral.GuildId                                                  // 公会ID
	msg.Param1, _ = JSONMarshal(exchangeTeam)                                         // 交换的队伍
	msg.Param10 = uint64(logGeneral.Turn)                                             // 回合
	msg.Param11 = uint64(logGeneral.Round)                                            // 轮次
	msg.Param12 = uint64(srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA))) // 战区
	msg.Param13 = logGeneral.Room                                                     // 房间ID
	msg.Param14 = uint64(util.If(isAuto, 1, 0))                                       // 是否一键
	msg.Param15 = uint64(logGeneral.RoomQuality)                                      // 房间品质
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableReorder(srv servicer, exchangeTeam []*log.GSTReorderTeam, guildID uint64, turn, round, battleZone uint32, room uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-交换队伍"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_REORDER)
	msg.GuildID = guildID                     // 公会ID
	msg.Param2, _ = JSONMarshal(exchangeTeam) // 交换的队伍
	msg.Param10 = uint64(turn)                // 回合
	msg.Param11 = uint64(round)               // 轮次
	msg.Param12 = uint64(battleZone)          // 战区
	msg.Param13 = room                        // 房间ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableBuildDonate(srv servicer, buildId uint32, buildCurResource, buildOldResource, donateResource []*cl.Resource, oldLevel, curLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-建筑捐赠"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DONATE)
	msg.Param1, _ = JSONMarshal(buildCurResource)
	msg.Param2, _ = JSONMarshal(buildOldResource)
	msg.Param3, _ = JSONMarshal(donateResource)
	msg.Param10 = uint64(buildId)  // 建筑ID
	msg.Param11 = uint64(oldLevel) // 老等级
	msg.Param12 = uint64(curLevel) // 当前等级
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableBuildDispatch(srv servicer, dispatchHeros map[uint32]*cl.GSTBuildUserDispatchHeroes) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-公会建筑派遣"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_DISPATCH)
	msg.Param1, _ = JSONMarshal(dispatchHeros)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableBoxRecv(srv servicer, buildDonate, dispatchTeam, battleWin []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-领取公会宝箱"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BOX_RECV)
	msg.Param1, _ = JSONMarshal(buildDonate)
	msg.Param2, _ = JSONMarshal(dispatchTeam)
	msg.Param3, _ = JSONMarshal(battleWin)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildSandTableBuildTask(srv servicer, taskIds []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战-建筑任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_BUILD_TASK)
	msg.Param1, _ = JSONMarshal(taskIds)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonLinkRecycle(srv servicer, recyclePoint uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季羁绊-回收"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_LINK_RECYCLE)
	msg.Param11 = uint64(recyclePoint) //回收分数
	srv.WriteLogMessage(msg)
}

func (u *User) LogStoryReviewUnlock(srv servicer, storyId uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "剧情回忆录-解锁剧情"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_STORY_REVIEW_UNLOCK)
	msg.Param10 = uint64(storyId) //剧情id
	srv.WriteLogMessage(msg)
}

func (u *User) LogNewYearActivityLoginAward(srv servicer, actId uint32, loginIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "新年活动-持续登录领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_NEW_YEAR_ACTIVITY_STORY_LOGIN_AWARD)
	msg.Param10 = uint64(actId)           //新年活动量表ID
	msg.Param1, _ = JSONMarshal(loginIDs) //每日登录的领奖ID

	srv.WriteLogMessage(msg)
}

func (u *User) LogPyramidChooseAward(srv servicer, latticeInfo *goxml.ActivityPyramidLatticeInfo, chooseAwards []*cl.Resource, periodId, round uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "金字塔-金字塔自选"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PYRAMID_CHOOSE_AWARD)
	bytes, err := json.Marshal(chooseAwards)
	if err != nil {
		l4g.Errorf("LogPyramidChooseAward json marshal error: %v", err)
	}
	msg.Param1 = util.String(bytes)         //自选奖励
	msg.Param10 = uint64(latticeInfo.Id)    //格子id
	msg.Param11 = uint64(latticeInfo.Type)  //格子类型
	msg.Param12 = uint64(latticeInfo.Floor) //格子层数
	msg.Param13 = uint64(latticeInfo.Pos)   //格子序号
	msg.Param14 = uint64(periodId)          //活动期数
	msg.Param15 = uint64(round)             //活动轮次
	srv.WriteLogMessage(msg)
}

func (u *User) LogPyramidDraw(srv servicer, latticeInfo *PyramidLattice, drawCosts, drawAwards []*cl.Resource, isChooseAward, periodId, round uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "金字塔-金字塔抽奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PYRAMID_DRAW)
	bytes1, err1 := json.Marshal(drawCosts)
	if err1 != nil {
		l4g.Errorf("LogPyramidDraw json marshal error: %v", err1)
	}
	msg.Param1 = util.String(bytes1) //抽奖消耗
	bytes2, err2 := json.Marshal(drawAwards)
	if err2 != nil {
		l4g.Errorf("LogPyramidDraw json marshal error: %v", err2)
	}
	msg.Param2 = util.String(bytes2)        //奖励
	msg.Param10 = uint64(latticeInfo.Id)    //格子id
	msg.Param11 = uint64(latticeInfo.Type)  //格子类型
	msg.Param12 = uint64(latticeInfo.Floor) //格子层数
	msg.Param13 = uint64(latticeInfo.Pos)   //格子序号
	msg.Param14 = uint64(isChooseAward)     //是否自选(0:否,1:是)
	msg.Param15 = uint64(periodId)          //活动期数
	msg.Param16 = uint64(round)             //活动轮次
	srv.WriteLogMessage(msg)
}

func (u *User) LogPyramidReceiveTaskAward(srv servicer, taskIds []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "金字塔-金字塔任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_PYRAMID_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(taskIds) // 领奖任务id
	srv.WriteLogMessage(msg)
}

func (u *User) LogArtifactFragmentRecycle(srv servicer, costs, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "神器-神器碎片溢出回收"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ARTIFACT_FRAGMENT_RECYCLE)
	msg.Param1, _ = JSONMarshal(costs)  // 回收的神器碎片
	msg.Param2, _ = JSONMarshal(awards) // 兑换的奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroConvert(srv servicer, costs, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-溢出英雄转换"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_CONVERT)
	msg.Param1, _ = JSONMarshal(costs)  // 消耗的英雄
	msg.Param2, _ = JSONMarshal(awards) // 转换得到的奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroTagUpdate(srv servicer, blessedHeroID, contractHeroID uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-交换英雄tag类型"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_TAG_UPDATE)
	msg.Param10 = blessedHeroID  // 原赐福英雄id
	msg.Param11 = contractHeroID // 原缔约英雄id
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonArenaRecvTaskAward(srv servicer, taskIds []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季竞技-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_ARENA_RECV_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(taskIds)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonArenaRecvDivisionAward(srv servicer, divisions []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季竞技-领取段位奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_ARENA_RECV_DIVISION_AWARD)
	msg.Param1, _ = JSONMarshal(divisions)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonArenaDivisionChange(srv Servicer, oldDivision, newDivision, round, zone uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季竞技-段位变化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_ARENA_DIVISION_CHANGE)
	msg.Param10 = uint64(oldDivision)
	msg.Param11 = uint64(newDivision)
	msg.Param12 = uint64(round)
	msg.Param13 = uint64(zone)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonArenaFight(srv servicer, opUID, opSID, defArena, defGid, ctxID uint64, formation, round uint32,
	myChange, opChange *cl.SeasonArenaUserScoreAndRank, myDivision, opponentDivision uint32, win, isBot bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季竞技-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_ARENA_FIGHT)
	msg.Param10 = opUID
	msg.Param11 = uint64(round)
	msg.Param12 = uint64(util.If(win, 1, 0))
	msg.Param13 = opSID
	msg.Param14 = uint64(util.If(isBot, 1, 0))
	msg.Param15 = uint64(myDivision)
	msg.Param16 = uint64(opponentDivision)
	msg.Param17 = uint64(defArena)
	msg.Param18 = uint64(defGid)
	msg.Param19 = uint64(u.GetArea())
	msg.Param1, _ = JSONMarshal(myChange)
	msg.Param2, _ = JSONMarshal(opChange)
	if formation > 0 {
		msg.Param3, _ = JSONMarshal(u.getLogFormationData(formation))
	}
	msg.Param4 = reportID

	msg.ContextID = ctxID
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonArenaBeFight(srv Servicer, attackID, attackSID, attackArena, attackGid uint64, round uint32,
	myChange, opChange *cl.SeasonArenaUserScoreAndRank, myDivision, opponentDivision uint32, win bool, reportID string) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季竞技-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_ARENA_BE_FIGHT)
	msg.Param10 = attackID
	msg.Param11 = uint64(round)
	msg.Param12 = uint64(util.If(win, 1, 0))
	msg.Param13 = attackSID
	msg.Param15 = uint64(myDivision)
	msg.Param16 = uint64(opponentDivision)
	msg.Param17 = uint64(attackArena)
	msg.Param18 = uint64(attackGid)
	msg.Param19 = uint64(u.GetArea())
	msg.Param1, _ = JSONMarshal(myChange)
	msg.Param2, _ = JSONMarshal(opChange)
	msg.Param4 = reportID

	srv.WriteLogMessage(msg)
}

func (u *User) LogDropActivityRecvDailyAward(srv servicer, sysID, activityID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "掉落活动-领取每日奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_DROP_ACTIVITY_RECV_DAILY_AWARD)
	msg.Param10 = uint64(sysID)      //活动后台ID
	msg.Param11 = uint64(activityID) //系统表ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTBossFight(srv servicer, cmsg *l2c.CS2L_GSTBossFight, guildId uint64, formationId uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会boss战-挑战"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GST_BOSS_FIGHT)
	msg.GuildID = guildId                                           // 公会ID
	msg.Param1 = cmsg.ReportId                                      // 战报ID
	msg.Param3, _ = JSONMarshal(u.getLogFormationData(formationId)) //阵容
	msg.Param4 = strconv.FormatUint(cmsg.GroupId, 10)               //房间id
	msg.Param5 = strconv.FormatUint(uint64(cmsg.Quality), 10)       //房间品质

	msg.Param10 = uint64(cmsg.Sta.LRound)                         // 回合
	msg.Param11 = uint64(cmsg.Sta.Round)                          // 轮次
	msg.Param12 = uint64(cmsg.Partition)                          //战区
	msg.Param13 = uint64(cmsg.Damage)                             //战力
	msg.Param14 = uint64(util.If(cmsg.Sweep, 1, 0))               //是否扫荡
	msg.Param15 = uint64(util.If(cmsg.Boss.ResidueHp <= 0, 1, 0)) //血量为0 胜利
	msg.Param16 = uint64(cmsg.BossGroup.MonsterGroup)             //boss组
	msg.Param17 = uint64(cmsg.BossGroup.MonsterIndex)             //boss 下标
	msg.Param18 = uint64(cmsg.Boss.BossId)                        //boss id
	msg.Param19 = uint64(util.If(cmsg.IsFirstKill, 1, 0))         //一键1，否则未0
	msg.Param20 = uint64(0)

	var bossLeftHp string
	for _, v := range cmsg.BossGroup.Boss {
		if bossLeftHp == "" {
			bossLeftHp = fmt.Sprintf("%d:%d", v.BossId, v.ResidueHp)
		} else {
			bossLeftHp = fmt.Sprintf("%s;%d:%d", bossLeftHp, v.BossId, v.ResidueHp)
		}
	}
	msg.Param2 = bossLeftHp
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTBossAward(srv servicer, monsterGroup uint32, awardIndex []uint32, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会boss战-领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GST_BOSS_AWARD)
	msg.Param10 = uint64(monsterGroup)      //怪物组
	msg.Param1, _ = JSONMarshal(awardIndex) //奖励下标
	msg.Param2, _ = JSONMarshal(awards)     //奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTBossBuyChallenge(srv servicer, buyType, buyCount uint32, recoverTime uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会boss战-购买挑战令"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GST_BOSS_BUY_CHALLENGE)
	msg.Param10 = uint64(buyType)     //购买类型
	msg.Param11 = uint64(buyCount)    //购买数量
	msg.Param12 = uint64(recoverTime) //恢复时间
	srv.WriteLogMessage(msg)
}

func (u *User) LogRemainStarUp(srv servicer, remainID uint32, remainOldStar uint32, remainNewStar uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "遗物-星级提升"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_REMAIN_STAR_UP)
	msg.Param10 = uint64(remainID)
	msg.Param11 = uint64(remainOldStar)
	msg.Param12 = uint64(remainNewStar)
	srv.WriteLogMessage(msg)
}

func (u *User) LogRemainBookRecvExp(srv servicer, level uint32, exp int64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "遗物图鉴-接受经验"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_REMAIN_BOOK_RECV_EXP)
	msg.Param10 = uint64(level)
	msg.Param11 = uint64(exp)
	srv.WriteLogMessage(msg)
}

func (u *User) LogRemainBookLevelUp(srv servicer, oldLevel, newLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "遗物图鉴-升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_REMAIN_BOOK_LEVEL_UP)
	msg.Param10 = uint64(oldLevel)
	msg.Param11 = uint64(newLevel)
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityTurnTableSummon(srv servicer, activityID, rewardID, buffID, guarantee, BigReward uint32, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "周年庆-转盘抽奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_TURN_TABLE_SUMMON)
	msg.Param10 = uint64(activityID)
	msg.Param11 = uint64(rewardID)
	msg.Param12 = uint64(buffID)
	msg.Param13 = uint64(guarantee)
	msg.Param14 = uint64(BigReward)
	msg.Param2, _ = JSONMarshal(awards) //奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityTurnTableLoginAward(srv servicer, actId uint32, loginIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "周年庆-持续登录领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_TURN_TABLE_LOGIN_AWARD)
	msg.Param10 = uint64(actId)           //活动量表ID
	msg.Param1, _ = JSONMarshal(loginIDs) //领奖ID

	srv.WriteLogMessage(msg)
}

func (u *User) LogActivityTurnTableTaskAward(srv servicer, actId uint32, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "周年庆-持续登录领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_TURN_TABLE_TASK_AWARD)
	msg.Param10 = uint64(actId)          //量表ID
	msg.Param1, _ = JSONMarshal(taskIDs) //任务ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonEnter(srv servicer) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季-进入"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_ENTER)
	now := time.Now().Unix()
	msg.Param10 = uint64(now)
	msg.Param11 = uint64(u.BaseID())

	seasonInfo := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(now)
	if seasonInfo != nil {
		msg.Param12 = uint64(seasonInfo.SettlementTm)
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTDragonFight(srv servicer, seasonId, dragonRound uint32, isSweep bool, ownGuild, opGuild *log.LogGSTDragonGuild, battleData *log.LogGSTDragonFight) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "GVG龙战-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GST_DRAGON_FIGHT)

	msg.Param1, _ = JSONMarshal(ownGuild) // 我方公会
	msg.Param2, _ = JSONMarshal(opGuild)  // 敌方公会
	if battleData != nil {
		msg.Param3, _ = JSONMarshal(u.getLogFormationData(battleData.FormationId)) // 阵容
	}
	msg.Param4, _ = JSONMarshal(battleData) // 战斗数据
	msg.Param10 = uint64(seasonId)
	msg.Param11 = uint64(dragonRound)
	if isSweep {
		msg.Param12 = uint64(2) // 扫荡
	} else {
		msg.Param12 = uint64(1) // 战斗
	}

	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTDragonTaskAward(srv servicer, dragonRound uint32, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "GVG龙战-任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GST_DRAGON_TASK_AWARD)
	msg.Param10 = uint64(dragonRound)    // 龙战场次
	msg.Param1, _ = JSONMarshal(taskIDs) // 任务ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildMobAcceptTask(srv servicer, taskID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "接取公会竞赛任务"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MOB_ACCEPT_TASK)
	msg.Param10 = uint64(taskID)
	taskInfo := goxml.GetData().GuildMobilizationTaskInfoM.Index(taskID)
	if taskInfo != nil {
		msg.Param11 = uint64(taskInfo.Rare)
	}
	srv.WriteLogMessage(msg)
}

func (u *User) LogGuildMobBuyTimes(srv servicer, buyType uint32, buyTimes uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会竞赛购买次数"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_MOB_BUY_TIMES)
	msg.Param10 = uint64(buyType)
	msg.Param11 = uint64(buyTimes)
	srv.WriteLogMessage(msg)
}

func (u *User) LogBossRushFight(srv servicer, seasonId uint32, battleData *log.LogBossRushFight) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季BOSS-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_BOSS_RUSH_FIGHT)

	if battleData != nil {
		msg.Param1, _ = JSONMarshal(u.getLogFormationData(battleData.FormationId)) // 阵容
	}
	msg.Param2, _ = JSONMarshal(battleData) // 战斗数据
	msg.Param10 = uint64(seasonId)
	if battleData.KillBoss {
		msg.Param11 = uint64(1)
	} else {
		msg.Param11 = uint64(0)
	}
	msg.Param12 = uint64(battleData.BossLevel)

	srv.WriteLogMessage(msg)
}

func (u *User) LogBossRushTaskAward(srv servicer, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季BOSS-任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_BOSS_RUSH_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(taskIDs) // 任务ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogTalentTreeLevelUp(srv servicer, id, oldLevel, newLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "天赋树-升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TALENT_TREE_LEVEL_UP)
	msg.Param10 = uint64(id)       // nodeId
	msg.Param11 = uint64(oldLevel) // 升级前的等级
	msg.Param12 = uint64(newLevel) // 升级后的等级
	rootId := id == goxml.GetData().SeasonTalentTreeBaseInfoM.GetRootId(u.GetSeasonID())
	msg.Param13 = uint64(util.If(rootId, 1, 0))
	srv.WriteLogMessage(msg)
}

func (u *User) LogTalentTreeReset(srv servicer, oldTimes, newTimes uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "天赋树-洗点"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TALENT_TREE_RESET)
	msg.Param10 = uint64(oldTimes)
	msg.Param11 = uint64(newTimes)
	srv.WriteLogMessage(msg)
}

func (u *User) LogTalentTreeReceiveTaskAwards(srv servicer, ids []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "天赋树-任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TALENT_TREE_RECEIVE_TASK_AWARDS)
	msg.Param1 = util.Uint32SliceToString(ids, ",") // 任务ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogTalentTreeRankUpdate(srv servicer, rank uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "天赋树-排行更新"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TALENT_TREE_RANK_UPDATE)
	msg.Param10 = uint64(rank) //
	srv.WriteLogMessage(msg)
}

func (u *User) LogHeroEmblemSkill(srv servicer, hid uint64, sysId uint32, counterSkill bool, exclusiveLv uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "英雄-符文技能改变"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_HERO_EMBLEM_SKILL)
	info := &log.LogHeroEmblemSkill{
		Hid:          hid,
		SysId:        sysId,
		CounterSkill: counterSkill,
		ExclusiveLv:  exclusiveLv,
	}
	msg.Param1, _ = JSONMarshal(info) //
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumLoginAward(srv servicer, actType, actId uint32, loginIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-持续登录领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_LOGIN_REWARD)
	msg.Param10 = uint64(actId)
	msg.Param11 = uint64(actType)
	msg.Param1, _ = JSONMarshal(loginIDs) //每日登录的领奖ID

	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumTaskAward(srv servicer, actType, actId uint32, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_TASK_REWARD)
	msg.Param10 = uint64(actId)
	msg.Param11 = uint64(actType)
	msg.Param1, _ = JSONMarshal(taskIDs)

	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumExchangeAward(srv servicer, id, actType, actID, count uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-活动兑换"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_STORY_EXCHANGE)

	msg.Param10 = uint64(id)
	msg.Param11 = uint64(actID)
	msg.Param12 = uint64(count)
	msg.Param13 = uint64(actType)
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumPuzzleCell(srv servicer, actType, actID uint32, copyID uint32, complete uint32, flipTimes uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-翻格子"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_PUZZLE_CELL)

	msg.Param10 = uint64(actID)
	msg.Param11 = uint64(actType)
	msg.Param12 = uint64(copyID)
	msg.Param13 = uint64(complete)
	msg.Param14 = uint64(flipTimes)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTOreFight(srv servicer, logData *log.LogGSTOreFight) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "公会战占矿-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_ORE_FIGHT)
	msg.Param1, _ = JSONMarshal(u.getLogFormationData(logData.FormationId)) // 阵容
	msg.Param2, _ = JSONMarshal(logData)                                    // 战斗数据
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTTechTaskAward(srv servicer, logData *log.LogGSTTechTaskAward) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "领取公会科技任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTTechLevelUp(srv servicer, logData *log.LogGSTTechLevelUp) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "升级公会战科技"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_TECH_LEVEL_UP)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumTurnTableSummon(srv servicer, actType, activityID, rewardID, buffID, guarantee, BigReward uint32, awards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-转盘抽奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_TURN_TABLE_SUMMON)
	msg.Param10 = uint64(activityID)
	msg.Param11 = uint64(rewardID)
	msg.Param12 = uint64(buffID)
	msg.Param13 = uint64(guarantee)
	msg.Param14 = uint64(BigReward)
	msg.Param15 = uint64(actType)
	msg.Param2, _ = JSONMarshal(awards) //奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumTurnTableSelectBuff(srv servicer, actType, activityID, buffID uint32, beSelectBuff []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-转盘BUFF选择"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_TURN_TABLE_SELECT_BUFF)
	msg.Param10 = uint64(activityID)
	msg.Param11 = uint64(actType)
	msg.Param12 = uint64(buffID)
	msg.Param2, _ = JSONMarshal(beSelectBuff) //奖励
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumFeed(srv servicer, actType, activityID, giftID, clientCount, addExp, feedCond, feedLevel, feedExp, feedCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-喂养"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_FEED_FEED)
	msg.Param10 = uint64(activityID)
	msg.Param11 = uint64(actType)
	msg.Param12 = uint64(giftID)
	msg.Param13 = uint64(clientCount)
	msg.Param14 = uint64(addExp)
	msg.Param15 = uint64(feedCond)
	msg.Param16 = uint64(feedLevel)
	msg.Param17 = uint64(feedExp)
	msg.Param18 = uint64(feedCount)
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumMakeGift(srv servicer, actType, activityID, giftID, makeCount uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-喂养"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_FEED_MAKE_GIFT)
	msg.Param10 = uint64(activityID)
	msg.Param11 = uint64(actType)
	msg.Param12 = uint64(giftID)
	msg.Param13 = uint64(makeCount)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSelectSummonSummon(srv servicer, activityID, summonType, wishHero uint32, summon *cl.SelectSummonSummon, awards []*cl.Resource, summonLog *SelectSummonLog) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "选择抽卡-抽卡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SELECT_SUMMON_SUMMON)
	msg.Param10 = uint64(activityID)               // 活动系统id
	msg.Param11 = uint64(summonType)               // 抽卡类型
	msg.Param12 = uint64(summon.TotalSummonCount)  // 抽卡总次数
	msg.Param14 = uint64(summonLog.UpRedHeroCount) // 心愿将的数量
	msg.Param16 = uint64(wishHero)                 // 心愿英雄
	msg.Param17 = uint64(summonLog.RedHeroCount)   // 红卡的数量

	msg.Param1, _ = JSONMarshal(awards)                  // 抽取到的资源
	msg.Param3, _ = JSONMarshal(summonLog.GuaranteeWays) // 抽卡对应的保底
	msg.Param4, _ = JSONMarshal(summonLog.WishHeroes)    // 心愿英雄

	srv.WriteLogMessage(msg)
}

func (u *User) LogUserInfo(srv servicer) {
	userInfo := &log.ESUserInfo{
		XId:      u.ID(),
		Name:     u.Name(),
		ServerId: u.ServerID(),
	}
	bytes, err := json.Marshal(userInfo)
	if err != nil {
		l4g.Errorf("json marshal error:%v %v", err, userInfo)
		return
	}
	data := srv.NewLog(log.LOG_TYPE_KAFKA_LOG).(*log.KafkaLogHandlerData)
	data.Topic = log.KAFKA_TOPIC_KAFKA_TOPIC_ES.String()
	data.EsData = &log.ESLogHandlerData{
		Index: strings.ToLower(log.ES_LOG_INDEX_ES_LOG_INDEX_USER_INFO.String()),
		Data:  bytes,
	}
	srv.WriteLog(log.LOG_TYPE_KAFKA_LOG, data)
}

func (u *User) LogGSTChallengeTaskAward(srv servicer, logData *log.LogGSTChallengeTaskAward) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "GST擂台赛-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func (u *User) LogGSTChallengeFight(srv servicer, logData *log.LogGSTChallengeFight) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "GST擂台赛-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_GUILD_SAND_TABLE_CHALLENGE_FIGHT)
	msg.Param1, _ = JSONMarshal(logData)
	msg.Param2, _ = JSONMarshal(u.getLogFormationData(uint32(common.FORMATION_ID_FI_GST_CHALLENGE)))
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonComplianceScoreChange(srv servicer, phase, stageIndex, scoreType, addScore, stageScore uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季冲榜-积分变化"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_COMPLIANCE_SCORE_CHANGE)
	msg.Param10 = uint64(phase)
	msg.Param11 = uint64(stageIndex)
	msg.Param12 = uint64(scoreType)
	msg.Param13 = uint64(addScore)
	msg.Param14 = uint64(stageScore)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonDoorTaskReward(srv servicer, logData *log.LogSeasonDoorTaskReward) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "开门玩法-领取任务奖励"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_DOOR_TASK_REWARD)
	msg.Param1, _ = JSONMarshal(logData)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonDoorFight(srv servicer, logData *log.LogSeasonDoorFight) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "开门玩法-战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_DOOR_FIGHT)
	msg.Param1, _ = JSONMarshal(u.getLogFormationData(logData.FormationId)) // 阵容
	msg.Param2, _ = JSONMarshal(logData)                                    // 战斗数据
	srv.WriteLogMessage(msg)
}

func (u *User) LogTitleAdd(srv servicer, title *cl.Title) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "称号-添加"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TITLE_ADD)
	msg.Param1, _ = JSONMarshal(title)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelryGet(srv servicer, data *log.LogSeasonJewelryData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-获得"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_GET)
	msg.Param1, _ = JSONMarshal(data)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelryWear(srv servicer, data *log.LogSeasonJewelryData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-穿戴"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_WEAR)
	msg.Param1, _ = JSONMarshal(data)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelrySkillLevelUp(srv servicer, data1, data2 *log.LogSeasonJewelryData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-词条升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_LEVEL_UP)
	msg.Param1, _ = JSONMarshal(data1)
	msg.Param2, _ = JSONMarshal(data2)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelrySkillClassUp(srv servicer, data1, data2 *log.LogSeasonJewelryData) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-词条升阶"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CLASS_UP)
	msg.Param1, _ = JSONMarshal(data1)
	msg.Param2, _ = JSONMarshal(data2)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelrySkillChange(srv servicer, data1, data2 *log.LogSeasonJewelryData, skillPos uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-词条洗练"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE)
	msg.Param1, _ = JSONMarshal(data1)
	msg.Param2, _ = JSONMarshal(data2)
	msg.Param10 = uint64(skillPos)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelrySkillChangeConfirm(srv servicer, data1, data2 *log.LogSeasonJewelryData, skillPos, opType uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-洗练结果确认"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_SKILL_CHANGE_CONFIRM)
	msg.Param1, _ = JSONMarshal(data1)
	msg.Param2, _ = JSONMarshal(data2)
	msg.Param10 = uint64(skillPos)
	msg.Param11 = uint64(opType)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelryDecompose(srv servicer, data *log.LogSeasonJewelryData, opType uint32, decomposeRewards, returnRewards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-分解"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_DECOMPOSE)
	msg.Param1, _ = JSONMarshal(data)
	msg.Param2, _ = JSONMarshal(decomposeRewards)
	msg.Param3, _ = JSONMarshal(returnRewards)
	msg.Param10 = uint64(opType)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonJewelryRecycle(srv servicer, recyclePoints uint32, recycleRewards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季装备-回收"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_JEWELRY_RECYCLE)
	msg.Param1, _ = JSONMarshal(recycleRewards)
	msg.Param10 = uint64(recyclePoints)
	srv.WriteLogMessage(msg)
}

func (u *User) LogActivitySumShoot(srv servicer, actType, activityID, shootID uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "活动集合-射击领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_ACTIVITY_SUM_SHOOT)
	msg.Param10 = uint64(activityID)
	msg.Param11 = uint64(actType)
	msg.Param12 = uint64(shootID)
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonMapTaskAward(srv servicer, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季地图 - 任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_MAP_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(taskIDs) // 任务ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogSeasonMapFight(srv servicer, logData *log.LogSeasonMapFight) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "赛季地图 - 战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_SEASON_MAP_FIGHT)
	msg.Param1, _ = JSONMarshal(u.getLogFormationData(logData.FormationId))
	msg.Param2, _ = JSONMarshal(logData)

	srv.WriteLogMessage(msg)
}

func (u *User) LogMuteAccount(srv servicer, operateUid, muteUid, muteStartTime, muteEndTime uint64) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "游戏服 - 封号"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_MUTE_ACCOUNT)
	msg.Param10 = operateUid
	msg.Param11 = muteUid
	msg.Param12 = muteStartTime
	msg.Param13 = muteEndTime

	srv.WriteLogMessage(msg)
}

func (u *User) LogTowerPokemonFight(srv servicer, logData *log.LogTowerPokemonFight) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宠物爬塔 - 战斗"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TOWER_POKEMON_FIGHT)
	msg.Param1, _ = JSONMarshal(u.getLogFormationData(logData.FormationId))
	msg.Param2, _ = JSONMarshal(logData)

	srv.WriteLogMessage(msg)
}

func (u *User) LogTowerPokemonTaskAward(srv servicer, taskIDs []uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宠物爬塔 - 任务领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_TOWER_POKEMON_TASK_AWARD)
	msg.Param1, _ = JSONMarshal(taskIDs) // 任务ID
	srv.WriteLogMessage(msg)
}

func (u *User) LogPokemonStarUp(srv servicer, pokemonSysID, oldStar, newStar uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宠物 - 升星"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_POKEMON_STAR_UP)
	msg.Param10 = uint64(pokemonSysID)
	msg.Param11 = uint64(oldStar)
	msg.Param12 = uint64(newStar)

	srv.WriteLogMessage(msg)
}

func (u *User) LogPokemonPotentialLevelUp(srv servicer, oldLevel, newLevel uint32) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宠物 - 潜能升级"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_POKEMON_POTENTIAL_LEVEL_UP)
	msg.Param11 = uint64(oldLevel)
	msg.Param12 = uint64(newLevel)

	srv.WriteLogMessage(msg)
}

func (u *User) LogPokemonMasterReward(srv servicer, masterLevels []uint32, masterRewards []*cl.Resource) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "宠物 - 大师等级领奖"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_POKEMON_MASTER_REWARD)
	msg.Param1, _ = JSONMarshal(masterRewards)
	msg.Param2, _ = JSONMarshal(masterLevels) // 领奖的大师等级

	srv.WriteLogMessage(msg)
}

func (u *User) LogPokemonSummonSummon(srv servicer, activityID, summonType, costType, wishPokemon uint32, summon *cl.PokemonSummonSummon, awards []*cl.Resource, summonLog *PokemonSummonLog) {
	msg := u.newLogMessage(srv)
	msg.TypeName = "选择抽卡-抽卡"
	msg.LogSubType = uint32(log.SUB_TYPE_ID_POKEMON_SUMMON_SUMMON)
	msg.Param10 = uint64(activityID)                // 活动系统id
	msg.Param11 = uint64(summonType)                // 抽卡类型
	msg.Param12 = uint64(summon.TotalSummonCount)   // 抽卡总次数
	msg.Param16 = uint64(wishPokemon)               // 心愿宠物
	msg.Param17 = uint64(summonLog.RedPokemonCount) // 红卡的数量
	msg.Param18 = uint64(costType)                  // 消耗类型。

	msg.Param1, _ = JSONMarshal(awards)                  // 抽取到的资源
	msg.Param3, _ = JSONMarshal(summonLog.GuaranteeWays) // 抽卡对应的保底
	msg.Param4, _ = JSONMarshal(summonLog.WishPokemon)   // 心愿英雄
	msg.Param5, _ = JSONMarshal(summonLog.GroupID)       // 卡池ID

	srv.WriteLogMessage(msg)
}
