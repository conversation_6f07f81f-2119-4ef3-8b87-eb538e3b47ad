package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

func (d *DivineDemon) Summon(summonType, summonCostType uint32, info *goxml.DivineDemonInfoExt, rd *rand.<PERSON>, heroID uint32) (uint32, []*cl.Resource, []*cl.Resource, *cl.DivineDemonSummonV2, uint32, []uint32, []uint32) {
	v2Summon := d.divineDemon.SummonV2
	if v2Summon == nil {
		d.initSummonV2()
		v2Summon = d.divineDemon.SummonV2
	}
	v2Clone := v2Summon.Clone()
	costI := getDivineDemonCostI(summonType, summonCostType)
	if costI == nil {
		l4g.Errorf("user:%d DivineDemon Summon summonType:%d summonCostType:%d get CostI failed", d.owner.ID(), summonType, summonCostType)
		return 0, nil, nil, nil, 0, nil, nil
	}
	retCode, useDiamondSummonCount, costs := costI.getSummonCost(d.owner, info, v2Clone)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d DivineDemonSingleSummon: getSummonCost failed. ret:%d", d.owner.ID(), retCode)
		return retCode, nil, nil, nil, 0, nil, nil
	}

	var summonCount int
	if summonType == uint32(common.DIVINE_DEMON_SUMMON_TYPE_DSN_SINGLE) {
		summonCount = 1
	} else {
		summonCount = int(goxml.GetData().DivineDemonConfigInfoM.SummonPlural)
	}

	guaranteeWays := make([]uint32, 0, summonCount)

	v2Clone.DiamondSummonCount += useDiamondSummonCount
	v2Clone.TotalSummonCount += uint32(summonCount)
	var awards []*cl.Resource
	var wishHeroes []uint32
	for i := 0; i < summonCount; i++ {
		var guaranteeWay SummonWay
		var summonResource []*cl.Resource
		var wishHero bool
		for index := range v2Clone.ColorGuarantee {
			v2Clone.ColorGuarantee[index]++
		}
		groupId, guarantee, red, gdGroupClass := goxml.GetData().DivineDemonGroupInfoM.RandomGroup(rd, v2Clone.ColorGuarantee, info.Id)
		if groupId == 0 {
			l4g.Errorf("user:%d divine demon Summon v2 failed", d.owner.ID())
			return uint32(ret.RET_DIVINE_DEMON_RAND_GROUP_FAILED), nil, nil, nil, 0, nil, nil
		}

		if guarantee {
			guaranteeWay = SummonColorfulGuarantee
		} else {
			guaranteeWay = SummonNormal
		}

		if red {
			v2Clone.TotalRedCard++
		}

		v2Clone.NotUpHeroCount, summonResource, wishHero = goxml.GetData().SummonGroupInfoM.RandomClassDivineSummon(rd, groupId, gdGroupClass, v2Clone.NotUpHeroCount, red, heroID, v2Clone.TotalRedCard)
		if summonResource == nil {
			l4g.Errorf("user:%d divine demon Summon v2 failed", d.owner.ID())
			return uint32(ret.RET_DIVINE_DEMON_RAND_CLASS_FAILED), nil, nil, nil, 0, nil, nil
		}

		guaranteeWays = append(guaranteeWays, uint32(guaranteeWay))
		if wishHero {
			wishHeroes = append(wishHeroes, 1)
		} else {
			wishHeroes = append(wishHeroes, 0)
		}

		addGuaranteeAfterSummon(v2Clone.ColorGuarantee, summonResource)
		awards = append(awards, summonResource...)
	}
	if summonCostType == uint32(common.DIVINE_DEMON_SUMMON_COST_TYPE_DDSCT_SUMMON_FREE) {
		v2Clone.DailyFreeSummon += uint32(summonCount)
	}

	return uint32(ret.RET_OK), awards, costs, v2Clone, uint32(summonCount), guaranteeWays, wishHeroes
}

func addGuaranteeAfterSummon(copyGuarantee []uint32, resources []*cl.Resource) {
	for index, resource := range resources {
		if resource == nil {
			l4g.Errorf("addGuaranteeAfterSummon index:%d is nil", index)
			continue
		}
		if resource.Type == uint32(common.RESOURCE_HERO) {
			heroInfo := goxml.GetData().HeroInfoM.Index(resource.Value)
			if heroInfo == nil {
				l4g.Errorf("addGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
				continue
			}
			switch heroInfo.Rare {
			case uint32(common.QUALITY_RED):
				copyGuarantee[goxml.DivineDemonRedGuaranteeIndex] = 0
			case uint32(common.QUALITY_ORANGE):
				copyGuarantee[goxml.DivineDemonOrangeGuaranteeIndex] = 0
			case uint32(common.QUALITY_PURPLE):
				copyGuarantee[goxml.DivineDemonPurpleGuaranteeIndex] = 0
			}
		} else if resource.Type == uint32(common.RESOURCE_FRAGMENT) {
			fragmentInfo := goxml.GetData().FragmentInfoM.Index(resource.Value)
			if fragmentInfo == nil {
				l4g.Errorf("addGuaranteeAfterSummon retResource hero:%d is error", resource.Value)
				continue
			}
			switch fragmentInfo.Rare {
			case uint32(common.QUALITY_ORANGE):
				copyGuarantee[goxml.DivineDemonOrangeFragmentGuaranteeIndex] = 0
			}
		}
	}
}
