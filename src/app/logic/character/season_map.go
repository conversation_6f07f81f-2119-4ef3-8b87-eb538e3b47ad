package character

import (
	"app/goxml"
	"app/logic/event"
	"app/logic/helper"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/bt"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"maps"
	"math"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type SeasonMap struct {
	owner *User
	data  *cl.SeasonMap //db-save-check
}

func newSeasonMap(user *User) *SeasonMap {
	s := &SeasonMap{
		owner: user,
	}
	return s
}

// 加载数据
func (s *SeasonMap) load(data *cl.SeasonMap) {
	s.data = data
	s.customInit()
}

func (s *SeasonMap) customInit() {
	if s.data == nil {
		s.data = &cl.SeasonMap{}
	}
	if s.data.PositionData == nil {
		s.data.PositionData = make(map[uint32]*cl.SeasonMapPosition)
	}
	if s.data.RecoverTimes == nil {
		s.data.RecoverTimes = make(map[uint32]int64)
	}
	if s.data.TaskProgress == nil {
		s.data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if s.data.ReceiveAwarded == nil {
		s.data.ReceiveAwarded = make(map[uint32]bool)
	}
}

func (s *SeasonMap) Save() {
	s.owner.dbUser.Module8.SeasonMap = s.data
	s.owner.setSaveTag(saveTagModule8)
}

func (s *SeasonMap) FlushData() *cl.SeasonMap {
	return s.data.Clone()
}

func (s *SeasonMap) SeasonInit(srv servicer) {
	if s.GetPosition() != 0 {
		return
	}
	s.SetPosition(goxml.GetData().SeasonMapConfigInfoM.InitPosition)
	positionData := &cl.SeasonMapPosition{}
	s.GetData().PositionData[s.GetPosition()] = positionData
	positionInfo := goxml.GetData().SeasonMapPositionInfoM.Index(s.GetPosition())
	if positionInfo == nil {
		l4g.Errorf("user: %d CheckInit: no positionInfo. position:%d", s.owner.ID(), s.GetPosition())
		return
	}
	for _, monster := range positionInfo.Monsters {
		monsterData := &cl.SeasonMapMonster{
			MonsterId: monster,
		}
		s.AddMonsterData(positionData, monsterData)
		monsterHpInfo := goxml.GetData().SeasonMapMonsterHpInfoM.GetRecordById(monster)
		if monsterHpInfo == nil {
			l4g.Errorf("user: %d CheckInit: no monsterHpInfo. monsterId:%d", s.owner.ID(), monster)
			return
		}
		monsterData.ReduceHp = monsterHpInfo.MaxHp
		monsterInfo := goxml.GetData().SeasonMapMonsterInfoM.Index(monster)
		if monsterInfo == nil {
			l4g.Errorf("user: %d CheckInit: no monsterInfo. monsterId:%d", s.owner.ID(), monster)
			return
		}
		s.OnMonsterDead(s.GetPosition(), positionData, monsterInfo, srv)
	}
	rewards := make([]*cl.Resource, 0)
	rewards = append(rewards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
		goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, goxml.GetData().SeasonMapConfigInfoM.InitFightStamina))
	rewards = append(rewards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
		goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, goxml.GetData().SeasonMapConfigInfoM.InitGoodsStamina))
	rewards = append(rewards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
		goxml.GetData().SeasonMapConfigInfoM.CurrencyTokenId, goxml.GetData().SeasonMapConfigInfoM.InitCurrency))
	retCode, _ := s.owner.Award(srv, rewards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_INIT), 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("CheckInit: award error. retCode %d user %d", retCode, s.owner.ID())
		return
	}
	now := time.Now().Unix()
	s.GetData().RecoverTimes[uint32(cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina)] = now
	s.GetData().RecoverTimes[uint32(cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina)] = now
	s.GetData().RecoverTimes[uint32(cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency)] = int64(helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now))
	s.Save()
}

func (s *SeasonMap) OnSeasonInit(srv servicer, currentSeasonID uint32) {
	s.clearStaminaBuyCount()
	s.resetData()
	s.customInit()
	s.SeasonInit(srv)
}

func (s *SeasonMap) clearStaminaBuyCount() {
	numberType := goxml.GetData().SeasonMapConfigInfoM.BuyFightStaminaNumType
	info := goxml.GetData().NumberTypeInfoM.Index(numberType)
	if info == nil {
		l4g.Errorf("user: %d clearStaminaBuyCount: numberTypeInfo is nil. numberType: %d", s.owner.ID(), numberType)
		return
	}
	s.owner.deleteNumber(numberType, info.RefreshType)
}

func (s *SeasonMap) resetData() {
	if s.data == nil {
		return
	}
	s.data = nil
	s.Save()
}

func (s *SeasonMap) OnSeasonEnd(srv servicer, needSettleAward bool) {
	s.clearSeasonItem(srv)
	s.resetData()
	s.customInit()
}

func (s *SeasonMap) clearSeasonItem(srv servicer) {
	clearItems := make([]*cl.Resource, 0)
	for id, num := range s.owner.SeasonMapBag().Goods {
		clearItems = append(clearItems, goxml.GenSimpleResource(uint32(common.RESOURCE_SEASON_MAP_GOODS), id, num))
	}
	count := s.owner.GetResourceCount(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, srv)
	if count != 0 {
		clearItems = append(clearItems, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
			goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, uint32(count)))
	}
	count = s.owner.GetResourceCount(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, srv)
	if count != 0 {
		clearItems = append(clearItems, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
			goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, uint32(count)))
	}
	count = s.owner.GetResourceCount(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.CurrencyTokenId, srv)
	if count != 0 {
		clearItems = append(clearItems, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
			goxml.GetData().SeasonMapConfigInfoM.CurrencyTokenId, uint32(count)))
	}

	count = s.owner.GetResourceCount(goxml.GetData().SeasonMapConfigInfoM.FightStaminaItem.Type,
		goxml.GetData().SeasonMapConfigInfoM.FightStaminaItem.Value, srv)
	if count != 0 {
		clearItems = append(clearItems, goxml.GenSimpleResource(goxml.GetData().SeasonMapConfigInfoM.FightStaminaItem.Type,
			goxml.GetData().SeasonMapConfigInfoM.FightStaminaItem.Value, uint32(count)))
	}

	count = s.owner.GetResourceCount(goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaItem.Type,
		goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaItem.Value, srv)
	if count != 0 {
		clearItems = append(clearItems, goxml.GenSimpleResource(goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaItem.Type,
			goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaItem.Value, uint32(count)))
	}

	if len(clearItems) > 0 {
		s.owner.Consume(srv, clearItems, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_SEASON_RESET), 0)
	}
}

func (s *SeasonMap) RecoverStamina(srv servicer) {
	s.RecoverFightStamina(srv)
	s.RecoverGoodsStamina(srv)
}

func (s *SeasonMap) GetData() *cl.SeasonMap {
	return s.data
}

func (s *SeasonMap) setRecoverTime(recoverType cl.SeasonMapRecoverType, time int64) {
	s.GetData().RecoverTimes[uint32(recoverType)] = time
	s.Save()
	if recoverType == cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency {
		return
	}
	msg := &cl.L2C_SeasonMapRecoverTimeUpdate{
		Ret:          uint32(ret.RET_OK),
		RecoverTimes: make(map[uint32]int64),
	}
	maps.Copy(msg.RecoverTimes, s.GetData().RecoverTimes)
	s.owner.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapRecoverTimeUpdate, msg)
}

func (s *SeasonMap) getRecoverTime(recoverType cl.SeasonMapRecoverType) int64 {
	return s.GetData().RecoverTimes[uint32(recoverType)]
}

func (s *SeasonMap) getFightStaminaCanRecoverNum(srv servicer) uint64 {
	curGoodsStamina := s.owner.GetTokenCount(goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, srv)
	staminaMax := goxml.GetData().SeasonMapConfigInfoM.FightStaminaMax
	if curGoodsStamina >= staminaMax {
		return 0
	}
	return staminaMax - curGoodsStamina
}

func (s *SeasonMap) IsMaxFightStamina(srv servicer) bool {
	return s.getFightStaminaCanRecoverNum(srv) == 0
}

func (s *SeasonMap) getFightStaminaRecoverSec() int64 {
	sec := goxml.GetData().SeasonMapConfigInfoM.FightStaminaRecoverSec
	return sec
}

func (s *SeasonMap) UseFightStamina(srv servicer, costs []*cl.Resource, reason uint32) ret.RET {
	before := s.IsMaxFightStamina(srv)
	retCode := s.owner.Consume(srv, costs, reason, 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("[SeasonMap] UseFightStamina: Consume error. retCode %d user %d", retCode, s.owner.ID())
		return ret.RET(retCode)
	}
	after := s.IsMaxFightStamina(srv)
	// 之前满了, 用之后不满 设置恢复时间为当前
	if before && !after {
		s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina, time.Now().Unix())
	}
	s.Save()
	return ret.RET_OK
}

func (s *SeasonMap) RecoverFightStamina(srv servicer) {
	lastRecoverTime := s.getRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina)
	if lastRecoverTime == 0 {
		return
	}
	now := time.Now().Unix()
	if now < lastRecoverTime {
		return
	}
	canRecoverNum := s.getFightStaminaCanRecoverNum(srv)
	if canRecoverNum == 0 {
		return
	}

	recoverSec := s.getFightStaminaRecoverSec()
	passTime := now - lastRecoverTime
	recoverNum := uint32(passTime / recoverSec)
	if recoverNum > 0 {
		if recoverNum >= uint32(canRecoverNum) {
			recoverNum = uint32(canRecoverNum)
			s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina, now)
		} else {
			leftTime := passTime - int64(recoverNum)*recoverSec
			s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina, now-leftTime)
		}
		s.AddFightStamina(srv, recoverNum, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_RECOVER))
	}
}

func (s *SeasonMap) AddFightStamina(srv servicer, count, reason uint32) ret.RET {
	reward := goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, count)
	rewards := []*cl.Resource{reward}
	retCode, _ := s.owner.Award(srv, rewards, AwardTagMail, reason, 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("[SeasonMap] AddFightStamina: award error. retCode %d user %d", retCode, s.owner.ID())
		return ret.RET(retCode)
	}
	return ret.RET_OK
}

func (s *SeasonMap) getCurrencyRecoverNum() uint32 {
	num := goxml.GetData().SeasonMapConfigInfoM.CurrencyRecoverNum
	addNumInfo := goxml.GetData().SeasonMapDailyMoneyInfoM.GetRecordByExploreValueMaxLe(s.GetData().ExploreRate)
	if addNumInfo != nil {
		num += addNumInfo.DailyMoney
	}
	return num
}

func (s *SeasonMap) RecoverCurrency(srv servicer) (ret.RET, []*cl.Resource) {
	now := time.Now().Unix()
	lastRecoverTime := s.getRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency)
	if lastRecoverTime == 0 {
		return ret.RET_OK, nil
	}
	if now < lastRecoverTime+goxml.GetData().SeasonMapConfigInfoM.CurrencyRecoverInterval {
		return ret.RET_OK, nil
	}

	recoverTimes := uint32((now - lastRecoverTime) / goxml.GetData().SeasonMapConfigInfoM.CurrencyRecoverInterval)
	recoverNum := s.getCurrencyRecoverNum() * recoverTimes
	s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency,
		int64(helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now)))
	return s.AddCurrency(srv, recoverNum, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_RECOVER))
}

func (s *SeasonMap) AddCurrency(srv servicer, count, reason uint32) (ret.RET, []*cl.Resource) {
	reward := goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.CurrencyTokenId, count)
	rewards := []*cl.Resource{reward}
	retCode, rewardsRet := s.owner.Award(srv, rewards, AwardTagMail, reason, 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("[SeasonMap] AddCurrency: award error. retCode %d user %d", retCode, s.owner.ID())
		return ret.RET(retCode), nil
	}
	return ret.RET_OK, rewardsRet
}

func (s *SeasonMap) GetPositionData(position uint32) *cl.SeasonMapPosition {
	return s.data.PositionData[position]
}

func (s *SeasonMap) GetMonsterData(positionData *cl.SeasonMapPosition, monsterId uint32) *cl.SeasonMapMonster {
	for _, monster := range positionData.Monsters {
		if monster.MonsterId == monsterId {
			return monster
		}
	}
	return nil
}

func (s *SeasonMap) GetEventData(positionData *cl.SeasonMapPosition, eventType cl.SeasonMapEventType) *cl.SeasonMapEvent {
	if positionData.EventData == nil {
		return nil
	}
	return positionData.EventData[uint32(eventType)]
}

func (s *SeasonMap) GenerateSeasonMapMonsterRecord(battleReport *bt.BattleSummary, monsterHpInfo *goxml.SeasonMapMonsterHpInfo, position uint32) *cl.SeasonMapMonsterRecord {
	record := &cl.SeasonMapMonsterRecord{
		ReportId: battleReport.Id,
	}
	for _, report := range battleReport.Reports {
		teamData := &cl.SeasonMapTeamData{
			TeamHeroes: make([]uint32, goxml.TeamMaxPos),
		}
		for _, attack := range report.Attackers {
			teamData.TeamHeroes[attack.Pos-1] = attack.SysId
		}
		teamData.TeamDamage = report.TotalHurt
		damageAdd := s.GetAltarAdd(SeasonMapBuffFightDamage)
		if damageAdd > 0 {
			teamData.TeamDamage = uint64(math.Floor(float64(teamData.TeamDamage) * (goxml.BaseFloat + float64(damageAdd)) / goxml.BaseFloat))
		}
		record.TotalDamage += teamData.TeamDamage
		teamData.TeamReduceHp = helper.CalcReduceHpByDamage(teamData.TeamDamage, monsterHpInfo.DamageRange, monsterHpInfo.HpRange)
		record.TotalReduceHp += teamData.TeamReduceHp
		record.TeamData = append(record.TeamData, teamData)
	}

	return record
}

type SeasonMapBattleResult struct {
	BeforeReduceHp, AfterReduceHp uint32
	Dead, UpdateMaxRecord         bool
}

func (s *SeasonMap) AddMonsterData(positionData *cl.SeasonMapPosition, monsterData *cl.SeasonMapMonster) {
	positionData.Monsters = append(positionData.Monsters, monsterData)
	s.Save()
}

func (s *SeasonMap) FinishFight(record *cl.SeasonMapMonsterRecord, positionData *cl.SeasonMapPosition,
	monsterData *cl.SeasonMapMonster, monsterInfo *goxml.SeasonMapMonsterInfoExt,
	monsterHpInfo *goxml.SeasonMapMonsterHpInfo) *SeasonMapBattleResult {
	result := &SeasonMapBattleResult{
		BeforeReduceHp: monsterData.ReduceHp,
	}

	if monsterData.MaxRecord == nil || monsterData.MaxRecord.TotalReduceHp < record.TotalReduceHp {
		monsterData.MaxRecord = record
		result.UpdateMaxRecord = true
	}

	if monsterInfo.Data.IsInfinite == 0 {
		if monsterData.ReduceHp+record.TotalReduceHp >= monsterHpInfo.MaxHp {
			monsterData.ReduceHp = monsterHpInfo.MaxHp
			result.Dead = true
		} else {
			monsterData.ReduceHp += record.TotalReduceHp
		}
	}

	result.AfterReduceHp = monsterData.ReduceHp
	s.Save()
	return result
}

func (s *SeasonMap) OnMonsterDead(position uint32, positionData *cl.SeasonMapPosition, monsterInfo *goxml.SeasonMapMonsterInfoExt, srv servicer) {
	if len(monsterInfo.Events) == 0 {
		return
	}
	for _, addEvent := range monsterInfo.Events {
		s.addSeasonMapEvent(position, positionData, addEvent, srv)
	}
	if monsterInfo.Data.ExploreRate != 0 {
		s.GetData().ExploreRate += monsterInfo.Data.ExploreRate
		s.owner.FireCommonEvent(srv.EventM(), event.AeSeasonMapExploreRateToX, uint64(monsterInfo.Data.ExploreRate))
		s.owner.UpdateSeasonMapRank(srv, goxml.SeasonMapExploreRate, s.GetData().ExploreRate)
	}
	s.Save()
}

func (s *SeasonMap) addSeasonMapEvent(position uint32, positionData *cl.SeasonMapPosition, eventInfo *goxml.SeasonMapEvent, srv servicer) {
	if positionData.EventData != nil {
		if _, exist := positionData.EventData[uint32(eventInfo.EventType)]; exist {
			l4g.Errorf("[SeasonMap] addSeasonMapEvent: repeated event. user %d eventType %d", s.owner.ID(), eventInfo.EventType)
			return
		}
	}
	seasonMapEvent := &cl.SeasonMapEvent{}
	switch eventInfo.EventType {
	case cl.SeasonMapEventType_EventType_Connect:
		connectInfo := goxml.GetData().SeasonMapConnectInfoM.Index(eventInfo.EventTypeId)
		if connectInfo == nil {
			l4g.Errorf("[SeasonMap] addSeasonMapEvent: connectInfo cant find. user %d EventTypeId %d", s.owner.ID(), eventInfo.EventTypeId)
			return
		}
		for _, position := range connectInfo.Connects {
			s.connectPosition(position, srv)
		}
		seasonMapEvent.ConnectEvent = &cl.SeasonMapConnectEvent{
			Finish: true,
		}
	case cl.SeasonMapEventType_EventType_Trade:
		seasonMapEvent.TradeEvent = &cl.SeasonMapTradeEvent{}
	case cl.SeasonMapEventType_EventType_Master:
		masterInfo := goxml.GetData().SeasonMapMasterInfoM.Index(eventInfo.EventTypeId)
		if masterInfo == nil {
			l4g.Errorf("[SeasonMap] addSeasonMapEvent: masterInfo cant find. user %d EventTypeId %d", s.owner.ID(), eventInfo.EventTypeId)
			return
		}
		if len(masterInfo.Tasks) == 0 {
			return
		}
		seasonMapEvent.MasterEvent = &cl.SeasonMapMasterEvent{
			TaskId: masterInfo.Tasks[0].TaskId,
		}
	case cl.SeasonMapEventType_EventType_Altar:
		seasonMapEvent.AltarEvent = &cl.SeasonMapAltarEvent{}
		seasonMapEvent.AltarEvent.Buffs = s.AltarRandBuff(seasonMapEvent.AltarEvent, eventInfo.EventTypeId, srv)
		s.owner.FireCommonEvent(srv.EventM(), event.AeSeasonMapUnlockAltarToX, uint64(1))
		s.GetData().AltarEventPositions = append(s.GetData().AltarEventPositions, position)
	case cl.SeasonMapEventType_EventType_Dialogue:
		seasonMapEvent.DialogueEvent = &cl.SeasonMapDialogueEvent{}
	case cl.SeasonMapEventType_EventType_None:
		return
	default:
		l4g.Errorf("[SeasonMap] addSeasonMapEvent: eventType error. user %d eventType %d", s.owner.ID(), eventInfo.EventType)
		return
	}
	if positionData.EventData == nil {
		positionData.EventData = make(map[uint32]*cl.SeasonMapEvent)
	}
	positionData.EventData[uint32(eventInfo.EventType)] = seasonMapEvent
	s.Save()
}

func (s *SeasonMap) connectPosition(position uint32, srv servicer) {
	positionInfo := goxml.GetData().SeasonMapPositionInfoM.Index(position)
	if positionInfo == nil {
		l4g.Errorf("[SeasonMap] connectPosition: positionInfo cant find. user %d position %d", s.owner.ID(), position)
		return
	}
	if s.GetPositionData(position) != nil {
		return
	}
	s.GetData().PositionData[position] = &cl.SeasonMapPosition{}
	s.owner.FireCommonEvent(srv.EventM(), event.AeSeasonMapConnectPositionToX, uint64(1))
	s.Save()
}

func (s *SeasonMap) AltarRandBuff(event *cl.SeasonMapAltarEvent, position uint32, srv servicer) []uint32 {
	altarBuffInfo := goxml.GetData().SeasonMapAltarBuffInfoM.PositionBuffs(position)
	if altarBuffInfo == nil {
		l4g.Errorf("[SeasonMap] AltarRandBuff: altarBuffInfo cant find. user %d position %d", s.owner.ID(), position)
		return nil
	}
	buffsInfo := altarBuffInfo.RandBuffs(srv.Rand())
	buffs := make([]uint32, 0)
	for _, buffInfo := range buffsInfo {
		buffs = append(buffs, buffInfo.BuffId)
	}
	return buffs
}

func (s *SeasonMap) GetPosition() uint32 {
	return s.GetData().Position
}

func (s *SeasonMap) SetPosition(position uint32) {
	s.GetData().Position = position
	s.Save()
}

func (s *SeasonMap) IsRecvAward(taskId uint32) bool {
	return s.GetData().ReceiveAwarded[taskId]
}

func (s *SeasonMap) SetRecvAward(taskIds []uint32) {
	for _, taskId := range taskIds {
		s.GetData().ReceiveAwarded[taskId] = true
	}
	s.Save()
}

func (s *SeasonMap) OnTaskEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	//功能未开启返回
	if !s.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), srv) {
		return
	}

	progressMap, change := s.owner.TaskTypeOnEvent(s.GetData().TaskProgress, event, progress, values)
	if change {
		s.Save()
		s.sendTaskProgress(progressMap)
	}
}

func (s *SeasonMap) sendTaskProgress(progressMap map[uint32]*cl.TaskTypeProgress) {
	s.owner.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapTaskUpdate, &cl.L2C_SeasonMapTaskUpdate{
		Ret:              uint32(ret.RET_OK),
		TaskTypeProgress: progressMap,
	})
}

func (s *SeasonMap) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return s.owner.CalcTaskProgress(taskTypeInfo)
	}
	return s.GetData().TaskProgress[taskTypeInfo.Id]
}

func (s *SeasonMap) ResetDaily(srv servicer) {
	data := s.GetData()
	for _, positionData := range data.PositionData {
		for eventType, eventData := range positionData.EventData {
			if eventType == uint32(cl.SeasonMapEventType_EventType_Altar) {
				if eventData.AltarEvent != nil {
					eventData.AltarEvent.ResetTimes = 0
					s.Save()
				}
			}
		}
	}
}

func (s *SeasonMap) getGoodsStaminaCanRecoverNum(srv servicer) uint64 {
	curGoodsStamina := s.owner.GetTokenCount(goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, srv)
	staminaMax := goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaMax
	addNum := s.owner.TalentTree().GetSeasonMapAddition(goxml.TalentTreeNodeLevelAdditionSeasonMapStaminaMax)
	staminaMax += uint64(addNum)
	if curGoodsStamina >= staminaMax {
		return 0
	}
	return staminaMax - curGoodsStamina
}

func (s *SeasonMap) IsMaxGoodsStamina(srv servicer) bool {
	return s.getGoodsStaminaCanRecoverNum(srv) == 0
}

func (s *SeasonMap) getGoodsStaminaRecoverSec() int64 {
	sec := goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaRecoverSec
	addNum := s.owner.TalentTree().GetSeasonMapAddition(goxml.TalentTreeNodeLevelAdditionSeasonMapStaminaRecover)
	if sec > int64(addNum) {
		sec -= int64(addNum)
	} else {
		l4g.Errorf("[SeasonMap]: getGoodsStaminaRecoverSec error.user %d addNum too large %d", s.owner.ID(), addNum)
		sec = 1
	}
	return sec
}

func (s *SeasonMap) BuyGoodsCost(srv servicer, costs, awards []*cl.Resource, reason uint32) (ret.RET, []*cl.Resource) {
	before := s.IsMaxGoodsStamina(srv)
	retCode, rewards := s.owner.Trade(srv, costs, awards, reason, 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("[SeasonMap] UseGoodsStamina: Consume error. retCode %d user %d", retCode, s.owner.ID())
		return ret.RET(retCode), nil
	}
	after := s.IsMaxGoodsStamina(srv)
	// 之前满了, 用之后不满 设置恢复时间为当前
	if before && !after {
		s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina, time.Now().Unix())
	}
	s.Save()
	return ret.RET_OK, rewards
}

func (s *SeasonMap) UpdateGoodsStaminaMax(srv servicer) {
	if s.IsMaxGoodsStamina(srv) {
		s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina, time.Now().Unix())
	} else {
		s.RecoverGoodsStamina(srv)
	}
}

func (s *SeasonMap) RecoverGoodsStamina(srv servicer) {
	lastRecoverTime := s.getRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina)
	if lastRecoverTime == 0 {
		return
	}
	now := time.Now().Unix()
	if now < lastRecoverTime {
		return
	}
	canRecoverNum := s.getGoodsStaminaCanRecoverNum(srv)
	if canRecoverNum == 0 {
		return
	}

	recoverSec := s.getGoodsStaminaRecoverSec()
	passTime := now - lastRecoverTime
	recoverNum := uint32(passTime / recoverSec)
	if recoverNum > 0 {
		if recoverNum >= uint32(canRecoverNum) {
			recoverNum = uint32(canRecoverNum)
			s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina, now)
		} else {
			leftTime := passTime - int64(recoverNum)*recoverSec
			s.setRecoverTime(cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina, now-leftTime)
		}
		s.AddGoodsStamina(srv, recoverNum, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_RECOVER))
	}
}

func (s *SeasonMap) AddGoodsStamina(srv servicer, count, reason uint32) ret.RET {
	reward := goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, count)
	rewards := []*cl.Resource{reward}
	retCode, _ := s.owner.Award(srv, rewards, AwardTagMail, reason, 0)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("[SeasonMap] AddGoodsStamina: award error. retCode %d user %d", retCode, s.owner.ID())
		return ret.RET(retCode)
	}
	return ret.RET_OK
}

func (s *SeasonMap) Sync2Cross(srv servicer, log *l2c.SeasonMapSyncPositionLog) {
	srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonMapSync, srv.ServerID(), &l2c.L2CS_SeasonMapSync{
		Log: log,
	})
}

func (s *SeasonMap) GetBuyGoodsPrice(position uint32, goodsInfo *goxml.SeasonMapTradeGoodsInfo) uint32 {
	addPct1 := float64(s.owner.TalentTree().GetSeasonMapAddition(goxml.TalentTreeNodeLevelAdditionSeasonMapSellPriceDown))
	if addPct1 > goxml.BaseFloat {
		addPct1 = goxml.BaseFloat
	}
	addPct2 := float64(s.GetAltarAdd(SeasonMapBuffBuyPrice))
	if addPct2 > goxml.BaseFloat {
		addPct2 = goxml.BaseFloat
	}
	price := uint32(math.Floor(float64(goodsInfo.FirstPrice) * (goxml.BaseFloat - addPct1) / goxml.BaseFloat * (goxml.BaseFloat - addPct2) / goxml.BaseFloat))
	price = goodsInfo.FixPrice(price, false)
	return price
}

func (s *SeasonMap) GetAltarAdd(addType int) uint32 {
	var addNum uint32
	for _, altarPosition := range s.GetData().AltarEventPositions {
		positionData := s.GetPositionData(altarPosition)
		if positionData == nil {
			continue
		}
		eventData := s.GetEventData(positionData, cl.SeasonMapEventType_EventType_Altar)
		if eventData == nil {
			continue
		}
		if eventData.AltarEvent == nil || eventData.AltarEvent.ChooseBuff == 0 {
			continue
		}
		altarBuffInfo := goxml.GetData().SeasonMapAltarBuffInfoM.Index(eventData.AltarEvent.ChooseBuff)
		if altarBuffInfo == nil {
			continue
		}
		if altarBuffInfo.Type == uint32(addType) {
			addNum += altarBuffInfo.Value1
		}
	}
	return addNum
}

func (s *SeasonMap) IsShopGoodOpen(info *goxml.ShopRegularGoodsInfoExt, _ Servicer) bool {
	if info.ExtType == goxml.ShopGoodsSeasonMapExploreLimit {
		return s.GetData().ExploreRate >= info.MinExt
	}
	return false
}

func (s *SeasonMap) CheckTaskAward(id common.RED_POINT) bool {
	if len(s.GetData().TaskProgress) == 0 {
		return false
	}
	if id == common.RED_POINT_SEASON_MAP_TASK1_RECV_AWARD {
		tasks1 := goxml.GetData().SeasonMapTaskInfoM.GetTasks(1)
		for _, taskInfo := range tasks1 {
			if s.IsRecvAward(taskInfo.Id) {
				continue
			}
			taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
			if taskTypeInfo == nil {
				continue
			}

			progress := s.GetTaskProgress(taskTypeInfo)
			if !s.owner.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
				continue
			}
			return true
		}
	} else if id == common.RED_POINT_SEASON_MAP_TASK2_RECV_AWARD {
		tasks2 := goxml.GetData().SeasonMapTaskInfoM.GetTasks(2)
		for _, taskInfo := range tasks2 {
			if s.IsRecvAward(taskInfo.Id) {
				continue
			}
			taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
			if taskTypeInfo == nil {
				continue
			}
			progress := s.GetTaskProgress(taskTypeInfo)
			if !s.owner.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
				// 只要有不满足的 后面的任务就不管
				break
			}
			return true
		}
	}

	return false
}

func (s *SeasonMap) GetFightAttrAdd() map[uint32]map[int]int64 {
	altAttrs := make(map[uint32]map[int]int64)
	// 0 代表全队
	attrs := make(map[int]int64)
	altAttrs[0] = attrs
	add := s.GetAltarAdd(SeasonMapBuffAttrDamAddRate)
	if add > 0 {
		attrs[goxml.AttrDamAddRate] += int64(add)
	}
	add = s.GetAltarAdd(SeasonMapBuffAttrFinalDamAddRate)
	if add > 0 {
		attrs[goxml.AttrFinalDamAddRate] += int64(add)
	}
	return altAttrs
}
