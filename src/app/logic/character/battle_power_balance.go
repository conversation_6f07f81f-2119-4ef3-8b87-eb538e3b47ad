package character

/*
import (
	"app/logic/battle"
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/util"
)

type FixTeamsAttr interface {
	GetBattleTeam(uint32, *cl.FormationTeamInfo, uint32, *User) (*battle.Team, map[uint32][]uint64, map[uint32]map[int]int64)
	GetFixPower(hero *Hero, u *User, formationID uint32) uint64
	FixHeroStatus(hero *cl.HeroBody, u *User)
	FixArtifactStatus(artifact *cl.Artifact, u *User)
}

var FixAttrFormationID = map[common.FORMATION_ID]FixTeamsAttr{
	common.FORMATION_ID_FI_TOWER_SEASON:               commonFix,
	common.FORMATION_ID_FI_GUILD_DUNGEON_1:            commonFix,
	common.FORMATION_ID_FI_GUILD_DUNGEON_2:            commonFix,
	common.FORMATION_ID_FI_GUILD_DUNGEON_3:            commonFix,
	common.FORMATION_ID_FI_GUILD_DUNGEON_4:            commonFix,
	common.FORMATION_ID_FI_GUILD_DUNGEON_5:            commonFix,
	common.FORMATION_ID_FI_GUILD_DUNGEON_6:            commonFix,
	common.FORMATION_ID_FI_WORLD_BOSS:                 worldBossFix,
	common.FORMATION_ID_FI_ACTIVITY_STORY_EQUAL:       commonFix,
	common.FORMATION_ID_FI_SEASON_DUNGEON:             commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_A:     commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_B:     commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_C:     commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_D:     commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_E:     commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_F:     commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_G:     commonFix,
	common.FORMATION_ID_FI_DISORDER_LAND_HURDLE_H:     commonFix,
	common.FORMATION_ID_FI_PEAK:                       commonFix,
	common.FORMATION_ID_FI_GST:                        commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_THREE_ATTACK:  commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_THREE_DEFENSE: commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_FIVE_ATTACK:   commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_FIVE_DEFENSE:  commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_SEVEN_ATTACK:  commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_SEVEN_DEFENSE: commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_NINE_ATTACK:   commonFix,
	common.FORMATION_ID_FI_SEASON_ARENA_NINE_DEFENSE:  commonFix,
	common.FORMATION_ID_FI_GST_BOSS_1:                 commonFix,
	common.FORMATION_ID_FI_GST_BOSS_2:                 commonFix,
	common.FORMATION_ID_FI_GST_BOSS_3:                 commonFix,
	common.FORMATION_ID_FI_GST_BOSS_4:                 commonFix,
	common.FORMATION_ID_FI_GST_BOSS_5:                 commonFix,
	common.FORMATION_ID_FI_GST_BOSS_6:                 commonFix,
	common.FORMATION_ID_FI_GST_BOSS_7:                 commonFix,
	common.FORMATION_ID_FI_GST_BOSS_8:                 commonFix,
	common.FORMATION_ID_FI_GST_DRAGON_1:               commonFix,
	common.FORMATION_ID_FI_GST_DRAGON_2:               commonFix,
	common.FORMATION_ID_FI_GST_DRAGON_3:               commonFix,
	common.FORMATION_ID_FI_GST_DRAGON_4:               commonFix,
	common.FORMATION_ID_FI_GST_DRAGON_5:               commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_1:                commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_2:                commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_3:                commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_4:                commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_5:                commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_6:                commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_7:                commonFix,
	common.FORMATION_ID_FI_BOSS_RUSH_8:                commonFix,
}

// 非赛季玩法（不需要赛季战力、属性修正）
var notSeasonFuncFIDs = []uint32{
	uint32(common.FORMATION_ID_FI_WORLD_BOSS),
	uint32(common.FORMATION_ID_FI_ACTIVITY_STORY_EQUAL),
}

// 是否需要赛季修正
// @param fid 阵容id
// @return bool true:需要 false:不需要
func isNeedSeasonFix(fid uint32) bool {
	return !util.InUint32s(notSeasonFuncFIDs, fid)
}

// 战力平衡通用修复
var commonFix = &CommonFix{}

type CommonFix struct{}

func (s *CommonFix) GetBattleTeam(formationID uint32, formationTeam *cl.FormationTeamInfo, riteRestrictState uint32,
	u *User) (*battle.Team, map[uint32][]uint64, map[uint32]map[int]int64) {
	return nil, nil, nil
}

func (s *CommonFix) GetFixPower(hero *Hero, u *User, formationID uint32) uint64 {
	return 0
}

func (s *CommonFix) FixHeroStatus(hero *cl.HeroBody, u *User) {
}

func (s *CommonFix) FixArtifactStatus(artifact *cl.Artifact, u *User) {}

var worldBossFix = &WorldBossFix{}

type WorldBossFix struct{}

func (p *WorldBossFix) GetBattleTeam(formationID uint32, formationTeam *cl.FormationTeamInfo, riteRestrictState uint32,
	u *User) (*battle.Team, map[uint32][]uint64, map[uint32]map[int]int64) {
	return nil, nil, nil
}
func (p *WorldBossFix) GetFixPower(hero *Hero, u *User, _ uint32) uint64 {
	return 0
}
func (p *WorldBossFix) FixHeroStatus(hero *cl.HeroBody, u *User) {
}
func (p *WorldBossFix) FixArtifactStatus(artifact *cl.Artifact, u *User) {
}
*/
