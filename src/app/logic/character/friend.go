package character

import (
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

func (u *User) ClearRecommendedFriend() {
	u.recommendedNum = 0
	u.recommendedF = make(map[uint64]int64)
}

func (u *User) ResetRecommendedFriend(now int64) {
	//已经推荐过的好友屏蔽一段时间，降低重复推荐的频率
	for k, v := range u.recommendedF {
		if v+RecommendedFriendInterval <= now {
			delete(u.recommendedF, k)
		}
	}
	//间隔几分钟后，重新从低等级开始推荐
	if u.recommendedTm+RecommendedFriendResetTime <= now {
		u.recommendedNum = 0
	}
	u.recommendedTm = now
	l4g.Debugf("user:%d ResetRecommendedFriend:%v %d",
		u.ID(), u.recommendedTm, u.recommendedNum)
}

// 是否是已推荐好友
func (u *User) IsRecommendedFriend(userID uint64) bool {
	return u.recommendedF[userID] != 0
}

func (u *User) AddRecommendedFriend(users []*cl.UserSnapshot, now int64) {
	l4g.Debugf("user:%d AddRecommendedFriend:%+v %d", u.ID(), users, now)
	for _, v := range users {
		u.recommendedF[v.Id] = now
	}
}

func (u *User) RecommendedNum() uint32 {
	return u.recommendedNum
}

// 只有从排行榜中推荐的时候会增加次数
func (u *User) AddRecommendedNum(num uint32) {
	u.recommendedNum += num
}

func (u *User) SetRecommendedNum(num uint32) {
	u.recommendedNum = num
}

func (u *User) CloneRecommendedFriend() map[uint64]int64 {
	clone := make(map[uint64]int64, len(u.recommendedF))
	for k, v := range u.recommendedF {
		clone[k] = v
	}
	l4g.Debugf("user:%d CloneRecommendedFriend:%v", u.ID(), clone)
	return clone
}
