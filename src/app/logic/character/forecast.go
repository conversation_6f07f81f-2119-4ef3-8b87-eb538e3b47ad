package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
)

type Forecast struct {
	owner *User
	data  *cl.Forecast
}

func newForecast(user *User) *Forecast {
	return &Forecast{
		owner: user,
	}
}

func (f *Forecast) Owner() *User {
	return f.owner
}

// 加载数据
func (f *Forecast) load(data *cl.Forecast) {
	f.data = data
}

// 设置保存标签, 保存数据
func (f *Forecast) save() {
	if f.Owner().dbUser.Module2.Forecast == nil {
		f.Owner().dbUser.Module2.Forecast = f.data
	}
	f.Owner().setSaveTag(saveTagModule2)
}

func (f *Forecast) Flush() *cl.Forecast {
	return f.data.Clone()
}

func (f *Forecast) GetForecast() *cl.Forecast {
	return f.data
}

func (f *Forecast) IsReceive(forecastID uint32) bool {
	if f.data == nil {
		return false
	}
	if _, exist := f.data.ReceiveAwarded[forecastID]; exist {
		return false
	}

	return true
}

func (f *Forecast) ReceiveAward(forecastID uint32) {
	if f.data == nil {
		return
	}
	if f.data.ReceiveAwarded == nil {
		f.data.ReceiveAwarded = make(map[uint32]bool)
	}
	f.data.ReceiveAwarded[forecastID] = true
	f.save()
}

func (f *Forecast) sendForecastUpdateToClient(progressMap map[uint32]*cl.TaskTypeProgress) {
	msg := &cl.L2C_ForecastUpdate{
		Ret:          uint32(ret.RET_OK),
		TaskProgress: progressMap,
	}
	f.Owner().SendCmdToGateway(cl.ID_MSG_L2C_ForecastUpdate, msg)
}

func (f *Forecast) OnForecastEvent(event uint32, progress uint64, values []uint32) {
	if !f.isUpdateProgress(event, values) {
		return
	}

	if f.data == nil {
		f.data = &cl.Forecast{
			TaskProgress: make(map[uint32]*cl.TaskTypeProgress),
		}
	}
	progressMap, change := f.Owner().TaskTypeOnEvent(f.data.TaskProgress, event, progress, values)
	if change {
		f.sendForecastUpdateToClient(progressMap)
		f.save()
	}
}

func (f *Forecast) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return f.owner.CalcTaskProgress(taskTypeInfo)
	}
	if f.data == nil {
		return nil
	}
	return f.data.TaskProgress[taskTypeInfo.Id]
}

// 是否更新任务进度
func (f *Forecast) isUpdateProgress(event uint32, values []uint32) bool {
	if f.data == nil {
		return true
	}

	var parameter uint32
	if len(values) > 0 {
		parameter = values[0]
	}

	if paramMap, exist := goxml.GetData().ForecastTaskInfoM.TaskTypeCache[event]; exist {
		var task *goxml.ForecastTask
		for param, forecastTask := range paramMap {
			if param == parameter || param == 0 {
				task = forecastTask
			}
		}
		if task == nil {
			return true
		}

		progress := f.data.TaskProgress[task.TaskID]
		if progress == nil {
			return true
		}

		switch task.FinishType {
		case uint32(common.TASK_TYPE_FINISH_TYPE_FT_GREATER):
			if progress.Progress < task.Value {
				return true
			}
		case uint32(common.TASK_TYPE_FINISH_TYPE_FT_LESS):
			if progress.Progress > task.Value {
				return true
			}
		}
	}

	return false
}
