package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

func (u *User) getUserNumData(numType, refreshType uint32) *cl.NumInfo {
	var datas map[uint32]*cl.NumInfo
	switch refreshType {
	case goxml.NumRefreshForbid:
		if u.dbUser.Module.NumInfo == nil {
			u.dbUser.Module.NumInfo = make(map[uint32]*cl.NumInfo)
		}
		datas = u.dbUser.Module.NumInfo
	case goxml.NumRefreshDaily:
		if u.dbUser.Module.Daily.NumInfo == nil {
			u.dbUser.Module.Daily.NumInfo = make(map[uint32]*cl.NumInfo)
		}
		datas = u.dbUser.Module.Daily.NumInfo
	case goxml.NumRefreshWeekly:
		if u.dbUser.Module.Weekly.NumInfo == nil {
			u.dbUser.Module.Weekly.NumInfo = make(map[uint32]*cl.NumInfo)
		}
		datas = u.dbUser.Module.Weekly.NumInfo
	case goxml.NumRefreshWeeklyFriday:
		if u.dbUser.Module5.WeeklyFriday.NumInfo == nil {
			u.dbUser.Module5.WeeklyFriday.NumInfo = make(map[uint32]*cl.NumInfo)
		}
		datas = u.dbUser.Module5.WeeklyFriday.NumInfo
	}

	if data, exist := datas[numType]; exist {
		return data
	}

	data := &cl.NumInfo{}
	datas[numType] = data
	return data
}

func (u *User) deleteNumber(numType, refreshType uint32) {
	var datas map[uint32]*cl.NumInfo
	switch refreshType {
	case goxml.NumRefreshForbid:
		datas = u.dbUser.Module.NumInfo
	case goxml.NumRefreshDaily:
		datas = u.dbUser.Module.Daily.NumInfo
	case goxml.NumRefreshWeekly:
		datas = u.dbUser.Module.Weekly.NumInfo
	case goxml.NumRefreshWeeklyFriday:
		datas = u.dbUser.Module5.WeeklyFriday.NumInfo
	}

	if datas == nil {
		return
	}
	_, exist := datas[numType]
	if !exist {
		return
	}

	delete(datas, numType)
	u.saveNum(refreshType)
	u.sendOpNumToClient(numType, refreshType)
}

func (u *User) saveNum(refreshType uint32) {
	switch refreshType {
	case goxml.NumRefreshForbid:
		u.setSaveTag(saveTagModule)
	case goxml.NumRefreshDaily:
		u.SaveDaily()
	case goxml.NumRefreshWeekly:
		u.SaveWeekly()
	case goxml.NumRefreshWeeklyFriday:
		u.SaveWeeklyFriday()
	}
}

func (u *User) sendOpNumToClient(numType, refreshType uint32) {
	ret := make(map[uint32]*cl.NumInfo, 1)
	switch refreshType {
	case goxml.NumRefreshForbid:
		ret[numType] = u.dbUser.Module.NumInfo[numType].Clone()
	case goxml.NumRefreshDaily:
		ret[numType] = u.dbUser.Module.Daily.NumInfo[numType].Clone()
	case goxml.NumRefreshWeekly:
		ret[numType] = u.dbUser.Module.Weekly.NumInfo[numType].Clone()
	case goxml.NumRefreshWeeklyFriday:
		ret[numType] = u.dbUser.Module5.WeeklyFriday.NumInfo[numType].Clone()
	}
	msg := &cl.L2C_OpNum{
		NumInfo: ret,
	}
	u.SendCmdToGateway(cl.ID_MSG_L2C_OpNum, msg)
}

// AddNumByType 根据玩法类型，更新次数相关统计数据
// @param uint32 numType 	玩法类型
// @param uint32 numSummary 	待添加的次数统计数据
func (u *User) AddNumByType(numType uint32, numSummary *cl.NumInfo) {
	if numSummary == nil {
		l4g.Errorf("user: %d AddNumByType: numSummary not exist. numType:%d", u.ID(), numType)
		return
	}
	cfg := goxml.GetData().NumberTypeInfoM.Index(numType)
	if cfg == nil {
		l4g.Errorf("user: %d AddNumByType: number type info not exist. numType:%d", u.ID(), numType)
		return
	}

	data := u.getUserNumData(numType, cfg.RefreshType)
	data.UseNum += numSummary.UseNum
	data.CostNum += numSummary.CostNum
	data.PurchaseNum += numSummary.PurchaseNum
	u.saveNum(cfg.RefreshType)
	u.sendOpNumToClient(numType, cfg.RefreshType)
}

func (u *User) AddPurchaseNum(numType, num uint32) uint32 {
	cfg := goxml.GetData().NumberTypeInfoM.Index(numType)
	if cfg == nil {
		l4g.Errorf("user: %d AddPurchaseNum: number type info not exist. numType:%d", u.ID(), numType)
		return 0
	}

	data := u.getUserNumData(numType, cfg.RefreshType)
	data.PurchaseNum += num
	u.saveNum(cfg.RefreshType)
	u.sendOpNumToClient(numType, cfg.RefreshType)
	return data.PurchaseNum
}

// 是否消耗道具
func (u *User) isCostItemForNum(costType uint32) bool {
	return costType > 0
}

// 是否购买即使用
func (u *User) isUseAfterPurchaseForNum(cfg *goxml.NumberTypeInfo) bool {
	if cfg.BuyType == goxml.NumBuyForbid {
		return false
	}
	return cfg.CountType == goxml.NumBuyAndUse
}

// CheckNumByType 根据玩法类型与消耗次数，检查次数或资源是否满足
// @param	uint32 numType	玩法类型
// @param	uint32 num		要使用的次数
// @return	uint32			状态码
// @return	[]*cl.Resource	检查到需要的消耗
// @return	*cl.NumInfo		需要消耗的次数统计信息（用于AddNumByType接口使用）
func (u *User) CheckNumByType(numType, num uint32) (uint32, []*cl.Resource, *cl.NumInfo) {
	cfg := goxml.GetData().NumberTypeInfoM.Index(numType)
	if cfg == nil {
		l4g.Errorf("user: %d CheckNumByType: number type info not exist. numType:%d", u.ID(), numType)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil, nil
	}

	data := u.getUserNumData(numType, cfg.RefreshType)
	if !u.isCostItemForNum(cfg.CostType) && !u.isUseAfterPurchaseForNum(cfg) {
		ret := getNumUseI(numType).checkNumEnough(num, numType, cfg.FreeNum, data, u)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d CheckNumByType: checkNumEnough failed. ret: %d", u.ID(), ret)
			return ret, nil, nil
		}
		return ret, nil, &cl.NumInfo{UseNum: num}

	} else {
		nonFreeCount := getNumUseI(numType).calcNonFreeCount(numType, cfg.FreeNum, num, data, u)
		if nonFreeCount == 0 {
			return uint32(cret.RET_OK), nil, &cl.NumInfo{UseNum: num}
		}
		// nonFreeCount是由num计算得来，已确保 num >= nonFreeCount
		usedFreeCount := num - nonFreeCount

		// 玩家拥有的道具，可以兑换的次数
		var (
			costs         []*cl.Resource
			costItemCount uint32
		)
		if u.isCostItemForNum(cfg.CostType) {
			costItemCount = u.getAmountOfItemForCostCount(cfg, nonFreeCount, data.CostNum)
			if costItemCount > 0 {
				costs = append(costs, goxml.GenSimpleResource(cfg.CostType, cfg.CostValue, cfg.CostCount*costItemCount))
			}

			if costItemCount >= nonFreeCount {
				return uint32(cret.RET_OK), costs, &cl.NumInfo{
					UseNum:  usedFreeCount,
					CostNum: nonFreeCount,
				}
			}
		}

		//需要购买的次数
		needPurchaseCount := nonFreeCount - costItemCount
		ret, purchaseCostRes := u.getPurchaseNumCostRes(numType, data.PurchaseNum, needPurchaseCount, cfg)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d CheckNumByType: getPurchaseNumCostRes failed. ret: %d", u.ID(), ret)
			return ret, nil, nil
		}

		costs = append(costs, purchaseCostRes...)
		ret, costs = u.CheckResourcesSize(costs)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d CheckNumByType: CheckResourcesSize failed. ret: %d", u.ID(), ret)
			return ret, nil, nil
		}

		return ret, costs, &cl.NumInfo{
			UseNum:      usedFreeCount + needPurchaseCount,
			CostNum:     costItemCount,
			PurchaseNum: needPurchaseCount,
		}
	}
}

// 计算通过资源（钻石）购买的次数，所需资源（钻石）量
func (u *User) getPurchaseNumCostRes(numType, purchasedNum,
	needPurchaseCount uint32, cfg *goxml.NumberTypeInfo) (uint32, []*cl.Resource) {
	if !u.isUseAfterPurchaseForNum(cfg) {
		l4g.Errorf("user: %d getPurchaseNumCostRes: number type info BuyType err. numType:%d, countType:%d",
			u.ID(), numType, cfg.CountType)
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
	}

	if !u.checkUserNumPurchaseLimit(cfg, numType, purchasedNum, needPurchaseCount) {
		return uint32(cret.RET_BUY_COUNT_LIMIT), nil
	}

	return u.calcPurchaseNumCostRes(cfg, purchasedNum, needPurchaseCount)
}

// 获取玩家拥有的道具，可以兑换的次数
func (u *User) getAmountOfItemForCostCount(cfg *goxml.NumberTypeInfo, needCount, costedNum uint32) uint32 {
	var ownedCount uint64
	if cfg.CostLimit > 0 {
		if needCount+costedNum > cfg.CostLimit {
			needCount = cfg.CostLimit - costedNum
		}
	}

	//目前只用到RESOURCE_ITEM，且应该不会用到RESOURCE_DIAMOND和RESOURCE_GOLD
	switch cfg.CostType {
	case uint32(common.RESOURCE_DIAMOND):
		ownedCount = u.Diamond() / uint64(cfg.CostCount)
	case uint32(common.RESOURCE_GOLD):
		ownedCount = u.Gold() / uint64(cfg.CostCount)
	case uint32(common.RESOURCE_ITEM):
		ownedCount = uint64(u.itemsBag()[cfg.CostValue] / cfg.CostCount)
		l4g.Debugf("getAmountOfItemForCostCount. bagItems:%+v, itemID:%d, itemCostPerNum:%d, ownedCount:%d",
			u.itemsBag(), cfg.CostValue, cfg.CostCount, ownedCount)
	case uint32(common.RESOURCE_TOKEN):
		ownedCount = u.tokensBag()[cfg.CostValue] / uint64(cfg.CostCount)
	default:
		return 0
	}
	if needCount <= uint32(ownedCount) {
		return needCount
	} else {
		return uint32(ownedCount)
	}
}

func (u *User) GetPurchaseNumCostRes(numType, num uint32, cfg *goxml.NumberTypeInfo) (uint32, []*cl.Resource) {
	purchasedNum := u.getPurchaseNum(numType)
	return u.calcPurchaseNumCostRes(cfg, purchasedNum, num)
}

// 计算购买次数所需资源（钻石）
func (u *User) calcPurchaseNumCostRes(cfg *goxml.NumberTypeInfo, purchasedNum, num uint32) (uint32, []*cl.Resource) {
	ok, price := goxml.GetData().BuyPriceInfoM.GetPrice(cfg.BuyGroup, purchasedNum, num)
	if !ok || price == 0 {
		l4g.Errorf("user: %d calcPurchaseNumCostRes: GetPrice failed. purchasedNum:%d, num:%d",
			u.ID(), purchasedNum, num)
		return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil
	}

	return uint32(cret.RET_OK), []*cl.Resource{{
		Type:  cfg.BuyCostType,
		Value: cfg.BuyCostValue,
		Count: price,
	}}
}

// CloneNumData 获取玩法次数数据
// 目前只有日刷新的次数信息 u.dbUser.Module.Daily.NumInfo
func (u *User) CloneNumData() map[uint32]*cl.NumInfo {
	nums := make(map[uint32]*cl.NumInfo, len(u.dbUser.Module.Daily.NumInfo))
	for k, v := range u.dbUser.Module.NumInfo {
		nums[k] = v.Clone()
	}
	if u.dbUser.Module.Daily != nil {
		for k, v := range u.dbUser.Module.Daily.NumInfo {
			nums[k] = v.Clone()
		}
	}
	if u.dbUser.Module.Weekly != nil {
		for k, v := range u.dbUser.Module.Weekly.NumInfo {
			nums[k] = v.Clone()
		}
	}
	if u.dbUser.Module5.WeeklyFriday != nil {
		for k, v := range u.dbUser.Module5.WeeklyFriday.NumInfo {
			nums[k] = v.Clone()
		}
	}
	return nums
}

// 检查购买次数限制
func (u *User) CheckUserNumPurchaseLimit(numType, num uint32) bool {
	cfg := goxml.GetData().NumberTypeInfoM.Index(numType)
	if cfg == nil {
		l4g.Errorf("user: %d CheckUserNumPurchaseLimit: number type info not exist. numType:%d", u.ID(), numType)
		return false
	}

	data := u.getUserNumData(numType, cfg.RefreshType)
	return u.checkUserNumPurchaseLimit(cfg, numType, data.PurchaseNum, num)
}

func (u *User) checkUserNumPurchaseLimit(cfg *goxml.NumberTypeInfo, numType, purchasedNum, num uint32) bool {

	if cfg.BuyType == goxml.NumBuyNoLimit { //不限制购买次数
		return true
	}

	//vip特权增加的购买次数
	vipBuyAdd := goxml.GetData().VipPrivilegeInfoM.GetBuyAddByType(u.Vip(), numType)

	return cfg.BuyLimit+vipBuyAdd >= purchasedNum+num
}

// 获取已购买玩法次数
// @param typ - 次数类型(关联number_type_info.xml的type)
func (u *User) getPurchaseNum(numType uint32) uint32 {
	cfg := goxml.GetData().NumberTypeInfoM.Index(numType)
	if cfg == nil {
		l4g.Errorf("user: %d GetPurchaseNum: number type info not exist. numType:%d", u.ID(), numType)
		return 0
	}

	data := u.getUserNumData(numType, cfg.RefreshType)
	return data.PurchaseNum
}

func (u *User) GetLeftFreeNumWithReaPoint(numType uint32) uint32 {
	cfg := goxml.GetData().NumberTypeInfoM.Index(numType)
	if cfg == nil {
		l4g.Errorf("user: %d GetLeftFreeNum: number type info not exist. numType:%d", u.ID(), numType)
		return 0
	}

	privilegeNum := u.calcPrivilegeNum(numType)
	data := u.getUserNumData(numType, cfg.RefreshType)
	num := cfg.FreeNum + privilegeNum
	if num > data.UseNum {
		return num - data.UseNum
	}

	return 0
}

// 计算特权额外加的次数
func (u *User) calcPrivilegeNum(numType uint32) (num uint32) {
	// vip
	num += goxml.GetData().VipPrivilegeInfoM.GetFreeAddByType(u.Vip(), numType)
	//月卡：主线挂机次数
	if numType == uint32(common.PURCHASEID_SPEED_ONHOOK) {
		num += u.MonthlyCard().GetPrivilegeValue(goxml.MonthlyCardDungeonSpeed)
	}

	return
}
