package character

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
)

type DailyAttendanceHero struct {
	owner *User
	data  *cl.DailyAttendanceHero //db-save-check
}

func newDailyAttendanceHero(user *User) *DailyAttendanceHero {
	return &DailyAttendanceHero{
		owner: user,
	}
}

func (d *DailyAttendanceHero) load(data *cl.DailyAttendanceHero) {
	d.data = data
	if d.data == nil {
		d.data = &cl.DailyAttendanceHero{}
	}
	if d.data.RecvStatus == nil {
		d.data.RecvStatus = make(map[uint32]bool)
	}
}

func (d *DailyAttendanceHero) Flush() *cl.DailyAttendanceHero {
	return d.data.Clone()
}

func (d *DailyAttendanceHero) Save() {
	if d.owner.dbUser.Module8.DailyAttendanceHero == nil {
		d.owner.dbUser.Module8.DailyAttendanceHero = d.data
	}
	d.owner.setSaveTag(saveTagModule8)
}

func (d *DailyAttendanceHero) GetData() *cl.DailyAttendanceHero {
	return d.data
}

func (d *DailyAttendanceHero) SetAward(recvID []uint32) {
	for _, v := range recvID {
		d.data.RecvStatus[v] = true
	}
	d.Save()
}

func (d *DailyAttendanceHero) ResetDaily(srv servicer) {
	if !d.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_DAILY_ATTENDANCE_HERO), srv) {
		return
	}
	d.AddLogin()
	cmsg := &cl.L2C_DailyAttendanceHeroGetData{
		Ret:        uint32(ret.RET_OK),
		Attendance: d.Flush(),
	}
	d.owner.SendCmdToGateway(cl.ID_MSG_L2C_DailyAttendanceHeroGetData, cmsg)
}

func (d *DailyAttendanceHero) LoginCountZero() bool {
	return d.data.LoginCount == 0
}

func (d *DailyAttendanceHero) AddLogin() {
	d.data.LoginCount++
	d.Save()
}

func (d *DailyAttendanceHero) SetLoginDay(day uint32) {
	d.data.LoginCount = day
	d.Save()
}
