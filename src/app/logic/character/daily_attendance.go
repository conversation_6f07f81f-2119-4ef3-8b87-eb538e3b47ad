package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

type DailyAttendance struct {
	owner *User
	data  *cl.DailyAttendance
}

func newDailyAttendance(user *User) *DailyAttendance {
	return &DailyAttendance{
		owner: user,
	}
}

func (d *DailyAttendance) load(data *cl.DailyAttendance) {
	d.data = data
}

func (d *DailyAttendance) Flush() *cl.DailyAttendance {
	return d.data.Clone()
}

func (d *DailyAttendance) save() {
	if d.owner.dbUser.Module4.DailyAttendance == nil {
		d.owner.dbUser.Module4.DailyAttendance = d.data
	}
	d.owner.setSaveTag(saveTagModule4)
}

func (d *DailyAttendance) GetDailyAttendanceData(group uint32) *cl.DailyAttendanceData {
	if d.data == nil {
		return nil
	}

	return d.data.Datas[group]
}

func (d *DailyAttendance) AddLoginCount() {
	if d.data == nil {
		d.data = &cl.DailyAttendance{}
	}

	d.data.LoginCount++
	d.updateTime()
	d.save()
	l4g.Debugf("[DailyAttendance] user %d AddLoginCount curCount %d", d.owner.ID(), d.data.LoginCount)
}

func (d *DailyAttendance) updateTime() {
	if d.data.Datas == nil {
		d.data.Datas = make(map[uint32]*cl.DailyAttendanceData)
	}

	curGroup := d.calcGroup()
	l4g.Debugf("user: %d curGroup: %d", d.owner.ID(), curGroup)
	data := d.GetDailyAttendanceData(curGroup)
	if data == nil {
		data = &cl.DailyAttendanceData{
			Round: curGroup,
		}
		d.data.Datas[curGroup] = data
	}
	curDay := d.CalcDay()
	l4g.Debugf("user: %d curDay: %d", d.owner.ID(), curDay)
	switch curDay {
	case goxml.DailyAttendanceEachGroupMinDay:
		data.FirstLoginTime = time.Now().Unix()
	case goxml.DailyAttendanceEachGroupMaxDay:
		data.LastLoginTime = time.Now().Unix()
	}
}

// 计算在哪个组
func (d *DailyAttendance) calcGroup() uint32 {
	if d.data == nil {
		return goxml.GetData().DailyAttendanceInfoM.GetMinGroup()
	}

	loginCount := d.data.LoginCount
	if loginCount <= goxml.DailyAttendanceEachGroupMaxDay {
		return goxml.GetData().DailyAttendanceInfoM.GetMinGroup()
	}

	// 取模 == 0，就是当前的组，30 / 30 = 1; 60 / 30 = 2;
	if loginCount%goxml.DailyAttendanceEachGroupMaxDay == 0 {
		return loginCount / goxml.DailyAttendanceEachGroupMaxDay
	}

	// 取模有余数，当前的组需要+1
	return loginCount/goxml.DailyAttendanceEachGroupMaxDay + 1
}

// CalcDay 计算在哪一天
func (d *DailyAttendance) CalcDay() uint32 {
	if d.data == nil {
		return goxml.DailyAttendanceEachGroupMinDay
	}
	// 取模 == 0，返回最后一天
	if d.data.LoginCount%goxml.DailyAttendanceEachGroupMaxDay == 0 {
		return goxml.DailyAttendanceEachGroupMaxDay
	}

	return d.data.LoginCount % goxml.DailyAttendanceEachGroupMaxDay
}

// 任意充值后，更新当天第二次领奖tag
func (d *DailyAttendance) UpdateSecondAwardTag() {
	if d.data == nil {
		l4g.Errorf("user: %d updateSecondReceiveTag: update second award tag failed. d.data is nil", d.owner.ID())
		return
	}
	curGroup := d.calcGroup()
	data := d.GetDailyAttendanceData(curGroup)
	if data == nil {
		l4g.Errorf("user: %d updateSecondReceiveTag: update second award tag failed. dailyAttendanceData is nil", d.owner.ID())
		return
	}

	curDay := d.CalcDay()
	if !util.BitAndUint64(data.IsSecondAward, int(curDay)) {
		util.BitSetTrueUint64Ptr(&data.IsSecondAward, int(curDay))
		d.save()
		d.owner.SendCmdToGateway(cl.ID_MSG_L2C_DailyAttendanceUpdate, &cl.L2C_DailyAttendanceUpdate{Data: data.Clone()})
	}
}

func (d *DailyAttendance) ReceiveAward(data *cl.DailyAttendanceData, param *cl.C2L_DailyAttendanceRecvAward) {
	if param.ExtraAwardReceive {
		data.ExtraAwardReceive = true
	}

	for _, v := range param.Params {
		switch v.AwardType {
		case uint32(common.DAILY_ATTENDANCE_AWARD_DAA_FIRST):
			util.BitSetTrueUint64Ptr(&data.FirstAwardReceive, int(v.Day))
		case uint32(common.DAILY_ATTENDANCE_AWARD_DAA_SECOND):
			util.BitSetTrueUint64Ptr(&data.SecondAwardReceive, int(v.Day))
		case uint32(common.DAILY_ATTENDANCE_AWARD_DAA_BOTH):
			util.BitSetTrueUint64Ptr(&data.FirstAwardReceive, int(v.Day))
			util.BitSetTrueUint64Ptr(&data.SecondAwardReceive, int(v.Day))
		}
	}

	d.save()
}

func (d *DailyAttendance) CheckExtraAwardReceive(data *cl.DailyAttendanceData, group uint32) uint32 {
	// 是否已领取过
	if data.ExtraAwardReceive {
		l4g.Errorf("user: %d CheckExtraAwardReceive: extra award is received. group: %d", d.owner.ID(), group)
		return uint32(cret.RET_DAILY_ATTENDANCE_AWARD_ALREADY_RECEIVED)
	}
	// 是不是当前group的最后一天(> 0 说明是当前group的最后一天)
	if data.LastLoginTime == 0 {
		l4g.Errorf("user: %d CheckExtraAwardReceive: day not match. group: %d", d.owner.ID(), group)
		return uint32(cret.RET_DAILY_ATTENDANCE_AWARD_ALREADY_RECEIVED)
	}

	return uint32(cret.RET_OK)
}

func (d *DailyAttendance) CheckDayAwardReceive(data *cl.DailyAttendanceData, params []*cl.DailyAttendanceRecvParam) (uint32, []*cl.Resource) {
	awards := make([]*cl.Resource, 0, len(params))
	// 需要计算此group的累登当前天数
	curDay := d.getCurDay(data)
	curGroup := data.Round
	isRepeat := make(map[uint32]struct{})
	for _, v := range params { //nolint:varnamelen
		if _, exist := isRepeat[v.Day]; exist {
			l4g.Errorf("user: %d CheckDayAwardReceive: param is repeat. group:%d param: %d", d.owner.ID(), curGroup, v.Day)
			return uint32(cret.RET_REPEATED_PARAM), nil
		}
		isRepeat[v.Day] = struct{}{}

		// 比较天数是否合法
		if v.Day > curDay {
			l4g.Errorf("user: %d CheckDayAwardReceive: day is invalid. group:%d day: %d", d.owner.ID(), curGroup, v.Day)
			return uint32(cret.RET_CLIENT_REQUEST_ERROR), nil
		}

		if ret := d.canReceiveDayAwards(data, v); ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d CheckDayAwardReceive: award already received. group:%d day: %d", d.owner.ID(), curGroup, v.Day)
			return ret, nil
		}

		validGroup := goxml.GetData().DailyAttendanceRewardInfoM.GetValidGroup(curGroup)
		rewardInfo := goxml.GetData().DailyAttendanceRewardInfoM.Index(validGroup, v.Day)
		if rewardInfo == nil {
			l4g.Errorf("user: %d CheckDayAwardReceive: rewardInfo is nil. group: %d day: %d", d.owner.ID(), validGroup, v.Day)
			return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
		}

		awards = append(awards, rewardInfo.RewardClRes...)
	}

	return uint32(cret.RET_OK), awards
}

func (d *DailyAttendance) getCurDay(data *cl.DailyAttendanceData) uint32 {
	var curDay uint32
	if data.LastLoginTime == 0 {
		curDay = d.CalcDay()
	} else {
		curDay = goxml.DailyAttendanceEachGroupMaxDay
	}

	return curDay
}

func (d *DailyAttendance) canReceiveDayAwards(data *cl.DailyAttendanceData, param *cl.DailyAttendanceRecvParam) uint32 {
	checkAwardReceives := make([]uint64, 0, 2)
	switch param.AwardType {
	case uint32(common.DAILY_ATTENDANCE_AWARD_DAA_FIRST):
		checkAwardReceives = append(checkAwardReceives, data.FirstAwardReceive)
	case uint32(common.DAILY_ATTENDANCE_AWARD_DAA_SECOND):
		checkAwardReceives = append(checkAwardReceives, data.SecondAwardReceive)
		if !util.BitAndUint64(data.IsSecondAward, int(param.Day)) {
			return uint32(cret.RET_DAILY_ATTENDANCE_NOT_RECHARGE)
		}
	case uint32(common.DAILY_ATTENDANCE_AWARD_DAA_BOTH):
		checkAwardReceives = append(checkAwardReceives, data.FirstAwardReceive, data.SecondAwardReceive)
		if !util.BitAndUint64(data.IsSecondAward, int(param.Day)) {
			return uint32(cret.RET_DAILY_ATTENDANCE_NOT_RECHARGE)
		}
	default:
		l4g.Errorf("user: %d isReceive: award type is invalid. awardOp: %d", d.owner.ID(), param.AwardType)
		return uint32(cret.RET_DAILY_ATTENDANCE_AWARD_OP_INVALID)
	}

	for _, v := range checkAwardReceives {
		if util.BitAndUint64(v, int(param.Day)) {
			l4g.Errorf("user: %d isReceive: award already received. group:%d day: %d", d.owner.ID(), data.Round, param.Day)
			return uint32(cret.RET_DAILY_ATTENDANCE_AWARD_ALREADY_RECEIVED)
		}
	}

	return uint32(cret.RET_OK)
}

// 邮件补发奖励
func (d *DailyAttendance) CheckMailReward(srv servicer) {
	if d.data == nil {
		return
	}

	if len(d.data.Datas) == 0 {
		return
	}

	curGroup := d.calcGroup()
	if curGroup < goxml.GetData().DailyAttendanceInfoM.GetMaxGroup() { // 进入循环累登之前不需要补发奖励
		return
	}

	totalAwards, pushGiftTriggers := d.CalcDailyAttendanceMailRewards(curGroup)
	if len(totalAwards) == 0 {
		return
	}

	// 邮件发奖
	DailyAttendanceMail(srv, d.owner, totalAwards, MailIDDailyAttendance)

	// 触发推送礼包
	if len(pushGiftTriggers) > 0 {
		d.TriggerPushGifts(srv, pushGiftTriggers)
	}
}

func (d *DailyAttendance) CalcDailyAttendanceMailRewards(curGroup uint32) ([]*cl.Resource, []uint32) {
	var totalAwards []*cl.Resource
	var pushGiftTriggers []uint32

	// 仅考虑补发循环轮次(group = 10的奖励)
	cycleGroup := goxml.GetData().DailyAttendanceRewardInfoM.GetValidGroup(curGroup)
	attendanceInfo := goxml.GetData().DailyAttendanceInfoM.Index(cycleGroup)
	if attendanceInfo == nil {
		l4g.Errorf("[DailyAttendance] user: %d CalcDailyAttendanceMailRewards: attendanceInfo is nil. group: %d", d.owner.ID(), cycleGroup)
		return nil, nil
	}

	var needSave bool
	for group, attendanceData := range d.data.Datas {
		if group < goxml.GetData().DailyAttendanceInfoM.GetMaxGroup() { // 旧累登保留界面不需要补发
			continue
		}
		if group == curGroup { // 不处理当前轮次
			continue
		}
		if attendanceData == nil {
			l4g.Errorf("[DailyAttendance] user %d CalcDailyAttendanceMailRewards: no attendanceData. group %d", d.owner.ID(), group)
			continue
		}
		if attendanceData.MailReward { // 已补发奖励
			continue
		}

		// 检查大奖
		if d.CheckExtraAwardReceive(attendanceData, group) == uint32(cret.RET_OK) {
			totalAwards = append(totalAwards, attendanceInfo.ExtraRewardClRes...)
			pushGiftTriggers = append(pushGiftTriggers, group)
			attendanceData.ExtraAwardReceive = true
		}

		// 检查每日奖励
		for day := goxml.DailyAttendanceEachGroupMinDay; day <= goxml.DailyAttendanceEachGroupMaxDay; day++ {
			// 获取当日奖励
			rewardInfo := goxml.GetData().DailyAttendanceRewardInfoM.Index(cycleGroup, day)
			if rewardInfo == nil || len(rewardInfo.RewardClRes) == 0 {
				l4g.Errorf("user: %d CalcDailyAttendanceMailRewards: rewardInfo is nil. group: %d day: %d", d.owner.ID(), cycleGroup, day)
				return nil, nil
			}

			// 判断当日的奖励1是否需要补发
			param1 := &cl.DailyAttendanceRecvParam{
				Day:       day,
				AwardType: uint32(common.DAILY_ATTENDANCE_AWARD_DAA_FIRST),
			}
			if ret := d.canReceiveDayAwards(attendanceData, param1); ret == uint32(cret.RET_OK) {
				totalAwards = append(totalAwards, rewardInfo.RewardClRes...)
				util.BitSetTrueUint64Ptr(&attendanceData.FirstAwardReceive, int(day))
			}

			// 判断当日的奖励2是否需要补发
			param2 := &cl.DailyAttendanceRecvParam{
				Day:       day,
				AwardType: uint32(common.DAILY_ATTENDANCE_AWARD_DAA_SECOND),
			}
			if ret := d.canReceiveDayAwards(attendanceData, param2); ret == uint32(cret.RET_OK) {
				totalAwards = append(totalAwards, rewardInfo.RewardClRes...)
				util.BitSetTrueUint64Ptr(&attendanceData.SecondAwardReceive, int(day))
			}
		}

		// 设置已补发状态
		attendanceData.MailReward = true
		needSave = true
	}

	if needSave {
		d.save()
	}

	return totalAwards, pushGiftTriggers
}

func (d *DailyAttendance) TriggerPushGifts(srv servicer, Groups []uint32) {
	for _, group := range Groups {
		// 旧累登和循环累登的推送礼包不同
		var eventValue uint32
		if group < goxml.GetData().DailyAttendanceInfoM.GetMaxGroup() { //旧累登
			eventValue = group
		}
		d.owner.FireCommonEvent(srv.EventM(), event.IeDailyAttendanceExtraAwardAfter, uint64(eventValue))
	}
}
