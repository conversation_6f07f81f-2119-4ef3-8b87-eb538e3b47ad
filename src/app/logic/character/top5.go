package character

import (
	"app/logic/helper"
)

func (hm *HeroM) GetTop5Heros() HeroSlice {
	return hm.top5Heros
}

// 更新top5
func (hm *HeroM) UpdateTop5(srv servicer) {
	oldTop5 := hm.top5Heros.Clone()

	hm.reCalTop5()

	power := hm.getTop5TotalPower()
	if power != hm.owner.Power() {
		hm.onTop5PowerChange(srv, power)
	}

	if hm.top5Heros.HasChange(oldTop5) {
		hm.setUserTop5()
		hm.owner.SendSelfToClient()
	}
}

func (hm *HeroM) clearTop5() {
	hm.top5Heros = nil
}

func (hm *HeroM) reCalTop5() {
	hm.clearTop5()
	hm.updateTop5()
}

func (hm *HeroM) ReCalTop5() {
	hm.reCalTop5()
}

func (hm *HeroM) setUserTop5() {
	userTop5 := hm.owner.GetTop5()
	if len(userTop5) != Top5HeroNum {
		userTop5 = make([]uint64, Top5HeroNum)
	}
	for index := range userTop5 {
		if len(hm.top5Heros) > index {
			userTop5[index] = hm.top5Heros[index].data.Id
		} else {
			userTop5[index] = 0
		}
	}
	if len(userTop5) >= 1 && userTop5[0] != 0 {
		hero := hm.Get(userTop5[0])
		if hero != nil && hero.GetData() != nil {
			hm.owner.setShowHero(hero.GetData().SysId)
		}
	}

	hm.owner.SetTop5(userTop5)
}

func (hm *HeroM) onTop5PowerChange(_ servicer, power int64) {
	hm.owner.SetPower(power)
	if power > hm.owner.MaxPower() {
		hm.owner.SetMaxPower(power)
	}
}

func (hm *HeroM) getTop5TotalPower() int64 {
	var uTotalPower uint64 = 0
	for _, hero := range hm.top5Heros {
		uTotalPower += hero.GetHeroPower(hm.owner)
	}
	return int64(uTotalPower)
}

func (hm *HeroM) updateTop5() {
	hm.updateByBattleListHero()
}

func (hm *HeroM) updateByBattleListHero() {
	for _, hero := range hm.heroes {
		if hero.IsInBattleList() {
			hm.updateTop5ByNewSysHero(hero)
		}
	}
}

// 根据新英雄来更新top5
func (hm *HeroM) updateTop5ByNewSysHero(hero *Hero) {
	if len(hm.top5Heros) < Top5HeroNum {
		hm.top5Heros = append(hm.top5Heros, hero)
		hero.GetHeroPower(hm.owner)
	} else {
		index := len(hm.top5Heros) - 1
		if hero.GetHeroPower(hm.owner) <= hm.top5Heros[index].GetHeroPower(hm.owner) {
			return
		}
		hm.top5Heros[index] = hero
	}
	//重新排序
	//调用之前要保证计算过战力
	helper.ReorderLastElementDesc(hm.top5Heros)
}
