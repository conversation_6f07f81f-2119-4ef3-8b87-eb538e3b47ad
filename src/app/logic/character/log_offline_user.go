package character

import (
	"app/logic/helper"
	"app/protos/in/db"
	"app/protos/in/gm"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
)

// 提取出来支持离线玩家的log
// 请一定要确保这些log不访问user的模块数据，因为没有模块数据
// 请一定要确保这些log不访问user的模块数据，因为没有模块数据
// 请一定要确保这些log不访问user的模块数据，因为没有模块数据
type MaybeOfflineUserLog interface {
	LogFriendAdded(srv Servicer, userID uint64)
	LogFriendDeleted(srv Servicer, userID uint64)
	LogFriendBlacklisted(srv Servicer, userID uint64)
	LogFriendRemovedFromBlacklist(srv Servicer, userID uint64)
	LogWebLargeRecharge(srv Servicer, params *gm.WebLargeRechargeMailParams, mailID uint64)
	LogGuildUserJoinOrQuit(srv Servicer, param *log.GuildUserJoinOrQuit)
	LogGuildPositionChange(srv Servicer, param *log.GuildPosition)
	LogGuildManage(srv Servicer, param *log.GuildManage)
	LogDeleteUserResources(srv Servicer, delCurrencies []*cl.Resource)
	LogSeasonArenaDivisionChange(srv Servicer, oldDivision, newDivision, round, zone uint32)
	LogSeasonArenaBeFight(srv Servicer, attackID, attackSID, attackArena, attackGid uint64, round uint32,
		myChange, opChange *cl.SeasonArenaUserScoreAndRank, myDivision, opponentDivision uint32, win bool, reportID string)
	/*
		LogLeagueJoin(srv Servicer, leagueID uint64, leagueName string)
		LogLeagueKicked(srv Servicer, leagueID uint64, leagueName string)
		LogLeagueTransfered(srv Servicer, leagueID uint64, leagueName string)
		LogLeagueViceAppointed(srv Servicer, leagueID uint64, leagueName string)
		LogLeagueImpeached(srv Servicer, leagueID uint64, leagueName string)
		LogLeagueBecomeElite(srv Servicer, leagueID uint64, leagueName string)
	*/
}

// 玩家行为日志不可能并发记录，所以保存全局的假user对象
// 要初始化一些必要的结构，因为部分log用到了，防止null pointer panic
// 服务器启动时，初始化全局的offlineLogUser，之后的离线玩家日志都复用这个offlineLogUser
// 离线玩家日志存在的意义：
//
//	比如全局类的玩法，仙盟，好友操作离线玩家，需要给离线玩家打个行为日志
//	重复new user object没有啥意义，这里只是需要有个user object可以调用LogXXX()
var offlineUserLog *User

func init() {
	dbUser := db.InitUser()
	//typ := reflect.TypeOf(*dbUser)
	//v := reflect.ValueOf(dbUser)
	//helper.ReflectInit(typ, v.Elem())
	offlineUserLog = &User{dbUser: dbUser}
}

// 为了记录离线玩家日志，请勿修改User对象的任何数据
func NewMaybeOfflineUserLog(srv Servicer, userID uint64) MaybeOfflineUserLog {
	if onlineUser := srv.UserM().GetUser(userID); onlineUser != nil {
		return onlineUser
	}

	offlineUserLog.dbUser.Id = userID
	offlineUserLog.dbUser.ServerId, _ = helper.CreateServerIDByUserID(srv.AppID(), srv.OpGroup(), userID)
	offlineUserLog.areaId = srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA))
	return offlineUserLog
}
