package character

import (
	"app/goxml"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/time"
)

type WorldBoss struct {
	owner *User
	data  *cl.WorldBoss
}

func newWorldBoss(user *User) *WorldBoss {
	return &WorldBoss{
		owner: user,
	}
}

func (w *WorldBoss) load(data *cl.WorldBoss) {
	w.data = data
	if w.data == nil {
		w.data = &cl.WorldBoss{}
	}
}

func (w *WorldBoss) save() {
	if w.owner.dbUser.Module4.WorldBoss == nil {
		w.owner.dbUser.Module4.WorldBoss = w.data
	}
	w.owner.setSaveTag(saveTagModule4)
}

func (w *WorldBoss) Flush() *cl.WorldBoss {
	return w.data.Clone()
}

func (w *WorldBoss) FlushData() *cl.WorldBossData {
	return w.data.GetData().Clone()
}

func (w *WorldBoss) GetOpenWorldBossCfg(srv servicer, now int64) *goxml.WorldbossInfoExt {
	serverOpenDay := srv.ServerDay(now)
	if serverOpenDay < goxml.GetData().WorldbossConfigInfoM.UnlockDay {
		return nil
	}
	return goxml.GetData().WorldbossInfoM.GetOpenWorldBoss(now)
}

func (w *WorldBoss) GetWorldBossData() *cl.WorldBossData {
	return w.data.Data
}

func (w *WorldBoss) reset(_ servicer, sysID uint32) {
	w.data.Data = new(cl.WorldBossData)
	w.data.Data.UniqueId = sysID
	w.data.Data.SysId = sysID
	w.data.Task = new(cl.WorldBossTask)
	w.save()
	w.owner.FormationManager().Delete(uint32(common.FORMATION_ID_FI_WORLD_BOSS))
}

func (w *WorldBoss) DailyReset(srv servicer) {
	if !w.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), srv) {
		return
	}
	w.CheckReset(srv)
}

func (w *WorldBoss) CheckReset(srv servicer) {
	now := time.Now().Unix()
	data := w.data.GetData()
	if data == nil {
		openCfg := w.GetOpenWorldBossCfg(srv, now)
		if openCfg == nil {
			l4g.Debugf("WorldBossCheckReset: no open world")
			return
		}
		w.data.Data = &cl.WorldBossData{
			UniqueId: openCfg.Id,
			SysId:    openCfg.Id,
		}
		w.save()
		return
	}

	openCfg := w.GetOpenWorldBossCfg(srv, now)
	if openCfg != nil && openCfg.Id != data.SysId {
		w.reset(srv, openCfg.Id)
	}
}

func (w *WorldBoss) IsMatchRoom() bool {
	room := w.data.GetData().GetRoom()
	if room != nil && room.RoomSysId > 0 {
		return true
	}

	return false
}

// IsDisplayPeriod  是否在展示期内
func (w *WorldBoss) IsDisplayPeriod(now int64) bool {
	if w.data.GetData() == nil {
		return false
	}
	info := goxml.GetData().WorldbossInfoM.Index(w.data.Data.GetSysId())
	if info == nil {
		return false
	}
	if now < (info.EndTime + goxml.GetData().WorldbossConfigInfoM.ShowTimeSec) {
		return true
	}

	return false
}

func (w *WorldBoss) InitWorldBoss(cmsg *l2c.C2L_WorldBossMatchRoom) {
	w.setUniqueId(cmsg.WUniqueId)
	w.setLevel(cmsg.Level)
	w.initRoom(cmsg.RoomUniqueId, cmsg.RoomSysId)
	w.save()
}

func (w *WorldBoss) setUniqueId(id uint32) {
	if w.data.GetData() == nil {
		return
	}
	if w.data.Data.UniqueId == 0 {
		w.data.Data.UniqueId = id
	}
}

func (w *WorldBoss) setLevel(level uint32) {
	if w.data.GetData() == nil {
		return
	}
	w.data.Data.Level = level
}

func (w *WorldBoss) initRoom(id, sysID uint32) {
	if w.data.GetData() == nil {
		return
	}
	if w.data.Data.GetRoom() == nil {
		w.data.Data.Room = new(cl.WorldBossRoom)
	}
	room := w.data.Data.Room
	room.RoomId = id
	room.RoomSysId = sysID
	room.RecoveryTimeFightCount = time.Now().Unix()
	room.FightCount = goxml.GetData().WorldbossConfigInfoM.InitialFightCount
}

func (w *WorldBoss) RecoverFightCount() uint32 {
	if w.data.GetData() == nil || w.data.Data.GetRoom() == nil {
		return 0
	}
	room := w.data.Data.GetRoom()

	now := time.Now().Unix()
	config := goxml.GetData().WorldbossConfigInfoM
	leftGrowCap := uint32(0)
	if config.FightCountLimit > room.FightCount {
		leftGrowCap = config.FightCountLimit - room.FightCount
	}
	if leftGrowCap == 0 {
		room.RecoveryTimeFightCount = now
		return room.FightCount
	}

	recoverNum := uint32((now - room.RecoveryTimeFightCount) / int64(config.EachXMinuteSec))
	if recoverNum > 0 {
		leftTime := int64(0)
		if leftGrowCap <= recoverNum {
			recoverNum = leftGrowCap
		} else {
			leftTime = now - room.RecoveryTimeFightCount - int64(recoverNum)*int64(config.EachXMinuteSec)
		}
		room.RecoveryTimeFightCount = now - leftTime
		room.FightCount += recoverNum
		w.save()
	}

	return room.FightCount
}

func (w *WorldBoss) GetAccumulativeHurt() uint64 {
	if w.data.GetData() == nil || w.data.Data.GetRoom() == nil {
		return 0
	}

	return w.data.Data.GetRoom().AccumulativeHurt
}

func (w *WorldBoss) GetAccumulativeHurtScore() uint64 {
	if w.data.GetData() == nil || w.data.Data.GetRoom() == nil {
		return 0
	}

	return w.data.Data.GetRoom().AccumulativeHurtScore
}

func (w *WorldBoss) GetMaxHurtScore() uint64 {
	if w.data.GetData() == nil || w.data.Data.GetRoom() == nil {
		return 0
	}

	return w.data.Data.GetRoom().MaxHurtScore
}

func (w *WorldBoss) GetMaxHurt() uint64 {
	if w.data.GetData() == nil || w.data.Data.GetRoom() == nil {
		return 0
	}

	return w.data.Data.GetRoom().MaxHurt
}

func (w *WorldBoss) UpdateHurtAndScore(hurt, hurtScore uint64) {
	if w.data.GetData() == nil || w.data.Data.GetRoom() == nil {
		return
	}
	w.updateMaxHurtAndScore(hurt, hurtScore)
	w.updateAccumulativeHurtAndScore(hurt, hurtScore)
	w.save()
}

func (w *WorldBoss) updateMaxHurtAndScore(hurt, hurtScore uint64) {
	room := w.data.Data.GetRoom()
	if hurt > room.MaxHurt {
		room.MaxHurt = hurt
	}
	if hurtScore > room.MaxHurtScore {
		room.MaxHurtScore = hurtScore
	}
}

func (w *WorldBoss) updateAccumulativeHurtAndScore(hurt, hurtScore uint64) {
	room := w.data.Data.GetRoom()
	room.AccumulativeHurt += hurt
	room.AccumulativeHurtScore += hurtScore
}

func (w *WorldBoss) DecFightCount() {
	if w.data.GetData() == nil || w.data.Data.GetRoom() == nil {
		return
	}
	room := w.data.Data.GetRoom()
	if room.FightCount > 0 {
		room.FightCount = room.FightCount - uint32(1)
	}

	w.save()
}

func (w *WorldBoss) GetLevel() uint32 {
	if w.data.GetData() == nil {
		return 0
	}

	return w.data.Data.Level
}

func (w *WorldBoss) GetWorldBossSysID() uint32 {
	if w.data.GetData() == nil {
		return 0
	}

	return w.data.Data.SysId
}
