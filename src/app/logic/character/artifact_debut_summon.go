package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

func (a *ArtifactDebutManager) initArtifactDebutSummon(category uint32) *cl.ArtifactDebutSummon {
	summon := &cl.ArtifactDebutSummon{
		Category: category,
	}
	a.data.Summons = append(a.data.Summons, summon)
	return summon
}

func (a *ArtifactDebutManager) getSummon(category uint32) *cl.ArtifactDebutSummon {
	for _, v := range a.data.Summons {
		if v.Category == category {
			return v
		}
	}
	return nil
}

func (a *ArtifactDebutManager) GetSummon(category uint32) *cl.ArtifactDebutSummon {
	return a.getSummon(category)
}

// 获取心愿神器id
func (a *ArtifactDebutManager) GetWishArtifactID() uint32 {
	summon := a.getSummon(uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR))
	if summon == nil {
		return 0
	}
	return summon.WishAid
}

// 设置心愿神器
func (a *ArtifactDebutManager) SetWishArtifact(aid uint32) {
	category := uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_SENIOR)
	summon := a.getSummon(category)
	if summon == nil {
		summon = a.initArtifactDebutSummon(category)
	}

	summon.WishAid = aid
	a.Save()
}

func (a *ArtifactDebutManager) SetSummon(category uint32, curSummon *cl.ArtifactDebutSummon) {
	for k, v := range a.data.Summons {
		if v.Category == category {
			a.data.Summons[k] = curSummon
			break
		}
	}
}

// 抽卡结果数据
type ArtifactDebutDrawRet struct {
	Awards         []*cl.Resource             //抽卡获得奖励
	Summon         *cl.ArtifactDebutSummon    //抽卡后最新抽卡数据
	Guarantee      *cl.ArtifactDebutGuarantee //抽卡后最新保底数据
	GuaranteeTypes []uint32                   //保底类型
}

// 抽卡
// @param servicer src
// @param uint32 category 抽卡分类 初级or高级
// @param uint32 drawCount 抽卡次数
// @return bool 是否执行成功
// @return ArtifactDebutDrawRet 抽卡结果数据
func (a *ArtifactDebutManager) DrawArtifact(srv servicer, category, drawCount uint32) (bool, *ArtifactDebutDrawRet) {
	summon := a.getSummon(category)
	if summon == nil {
		summon = a.initArtifactDebutSummon(category)
	}

	tmpSummon := summon.Clone()
	tmpGuarantee := a.getGuarantee().Clone()
	awards := make([]*cl.Resource, 0, drawCount)
	types := make([]uint32, 0, drawCount)
	for i := 0; i < int(drawCount); i++ {
		var winWishCard bool
		typ := a.calcGuarantee(tmpGuarantee)
		if typ != ArtifactDebutGuaranteeTypeNone {
			if tmpSummon.WishAid == 0 {
				l4g.Errorf("user: %d DrawArtifact.doDraw: guarantee failed, no wishAid", a.owner.ID())
				return false, nil
			}
			awards = append(awards, makeWishArtifactRes(tmpSummon.WishAid))
			winWishCard = true
		} else {
			award, group, id, isWishCard := a.doDraw(srv, tmpSummon)
			if award == nil {
				l4g.Errorf("user: %d DrawArtifact.doDraw: failed, summon:%+v", a.owner.ID(), tmpSummon)
				return false, nil
			}
			awards = append(awards, award...)
			winWishCard = isWishCard
			//更新抽卡临时数据
			a.updateSummonData(id, group, tmpSummon)
		}

		//更新临时保底数据
		a.updateGuarantee(winWishCard, tmpGuarantee)
		types = append(types, typ)
	}

	return true, &ArtifactDebutDrawRet{
		Awards:         awards,
		Summon:         tmpSummon,
		Guarantee:      tmpGuarantee,
		GuaranteeTypes: types,
	}
}

func makeWishArtifactRes(wishArtifactID uint32) *cl.Resource {
	return &cl.Resource{
		Type:  uint32(common.RESOURCE_ARTIFACT),
		Value: wishArtifactID,
		Count: 1,
	}
}

func (a *ArtifactDebutManager) isSummonIDListFull(summon *cl.ArtifactDebutSummon) bool {
	return summon.CurrentIds >= goxml.GetData().ArtifactDebutDrawGroupInfoM.MaxVal(summon.CurrentGroup)
}

func (a *ArtifactDebutManager) isSummonGroupListFull(summon *cl.ArtifactDebutSummon) bool {
	return summon.FinishGroups >= goxml.GetData().ArtifactDebutDrawCategoryInfoM.MaxVal(a.GetActID(), summon.Category)
}

// 抽卡逻辑
// @param servicer srv
// @param *cl.ArtifactDebutSummon summon
// @return []*cl.Resource 抽到的奖励
// @return uint32 使用的group
// @return uint32 使用的id
// @return bool 是否抽中心愿卡
func (a *ArtifactDebutManager) doDraw(srv servicer, summon *cl.ArtifactDebutSummon) ([]*cl.Resource, uint32, uint32, bool) {
	var winWishCard bool
	group := a.randomGroup(srv, summon) // 获取大组
	if group == 0 {
		l4g.Errorf("user: %d doDraw: randGroup failed, summon:%+v", a.owner.ID(), summon)
		return nil, 0, 0, winWishCard
	}

	id := a.randomID(srv, group, summon) // 获取小组
	if id == 0 {
		l4g.Errorf("user: %d doDraw: randomID failed, summon:%+v", a.owner.ID(), summon)
		return nil, 0, 0, winWishCard
	}

	dropGroupID := goxml.GetData().ArtifactDebutDrawGroupInfoM.GetDropGroupID(group, id)
	info := goxml.GetData().ArtifactDebutDrawDropInfoM.Random(srv.Rand(), dropGroupID)
	if info == nil {
		l4g.Errorf("user: %d doDraw: dropRandom failed, dropGroupID:%d", a.owner.ID(), dropGroupID)
		return nil, 0, 0, winWishCard
	}

	//初级抽，量表中的kind只能是0
	if summon.Category == uint32(common.ARTIFACT_DEBUT_SUMMON_CATEGORY_JUNIOR) &&
		info.Kind != goxml.ArtifactDebutDrawKindCommon {
		l4g.Errorf("user: %d doDraw: junior draw config data err, id:%d", a.owner.ID(), info.ID)
		return nil, 0, 0, winWishCard
	}

	var award []*cl.Resource
	if info.Kind == goxml.ArtifactDebutDrawKindWishArtifactFragment {
		award = []*cl.Resource{
			{
				Type:  uint32(common.RESOURCE_ARTIFACT_FRAGMENT),
				Value: goxml.GetData().ArtifactInfoM.GetArtifactFragmentID(summon.WishAid),
				Count: info.Count,
			},
		}
	} else if info.Kind == goxml.ArtifactDebutDrawKindWishArtifact { // 新版碎片抽卡不能抽到完整神器，但这里不做限制
		award = []*cl.Resource{
			{
				Type:  uint32(common.RESOURCE_ARTIFACT),
				Value: summon.WishAid,
				Count: info.Count,
			},
		}
		winWishCard = true
	} else {
		award = info.Award
	}
	return award, group, id, winWishCard
}

// 随机权重组group
func (a *ArtifactDebutManager) randomGroup(srv servicer, summon *cl.ArtifactDebutSummon) uint32 {
	if summon.CurrentGroup > 0 {
		return summon.CurrentGroup
	}

	groups := goxml.GetData().ArtifactDebutDrawCategoryInfoM.GetGroupList(a.GetActID(), summon.Category, summon.FinishGroups)
	length := len(groups)
	if length == 0 {
		l4g.Errorf("user: %d ArtifactDebutManager.randGroup: no group data", a.owner.ID())
		return 0
	}
	return groups[srv.Rand().RandBetween(0, length-1)]
}

// 随机掉落组id
func (a *ArtifactDebutManager) randomID(srv servicer, group uint32, summon *cl.ArtifactDebutSummon) uint32 {
	idList := goxml.GetData().ArtifactDebutDrawGroupInfoM.IDs(group, summon.CurrentIds)
	length := len(idList)
	if length == 0 {
		l4g.Errorf("user: %d ArtifactDebutManager.randomID: no id list data", a.owner.ID())
		return 0
	}
	return idList[srv.Rand().RandBetween(0, length-1)]
}

// 更新抽卡数据
func (a *ArtifactDebutManager) updateSummonData(id, group uint32, summon *cl.ArtifactDebutSummon) {
	summon.CurrentGroup = group
	if a.addSummonCurrentID(id, summon) {
		a.addSummonFinishGroup(group, summon)
	}
}

// 添加掉落组id，到当前已完成id列表
// @param uint32 id 掉落组id
// @param *cl.ArtifactDebutSummon summon
// @return bool 当前掉落组是否已抽尽
func (a *ArtifactDebutManager) addSummonCurrentID(id uint32, summon *cl.ArtifactDebutSummon) bool {
	util.BitSetTrueUint64Ptr(&summon.CurrentIds, int(id))
	if a.isSummonIDListFull(summon) {
		summon.CurrentIds = 0
		summon.CurrentGroup = 0
		return true
	}
	return false
}

// 添加掉落组group，到当前已完成group列表
// @param uint32 group 掉落组group
// @param *cl.ArtifactDebutSummon summon
func (a *ArtifactDebutManager) addSummonFinishGroup(group uint32, summon *cl.ArtifactDebutSummon) {
	util.BitSetTrueUint64Ptr(&summon.FinishGroups, int(group))
	if a.isSummonGroupListFull(summon) {
		summon.CurrentIds = 0
		summon.CurrentGroup = 0
		summon.FinishGroups = 0
	}
}

func newArtifactDebutSummonAct() *cl.ArtifactDebutSummonAct {
	return &cl.ArtifactDebutSummonAct{}
}

func (a *ArtifactDebutManager) getSummonAct() *cl.ArtifactDebutSummonAct {
	if a.data.SummonAct == nil {
		a.data.SummonAct = newArtifactDebutSummonAct()
	}
	return a.data.SummonAct
}

func (a *ArtifactDebutManager) AddDrawCount(addCount uint32) {
	summonAct := a.getSummonAct()
	summonAct.Count += addCount
}

func (a *ArtifactDebutManager) GetDrawCount() uint32 {
	summonAct := a.getSummonAct()
	return summonAct.Count
}

type ArtifactDebutSummonAct cl.ArtifactDebutSummonAct

// 是否可以领奖
func (a *ArtifactDebutSummonAct) CanReceiveAward(actID uint32, ids []uint32) bool {
	for _, id := range ids {
		info := goxml.GetData().ArtifactDebutDrawRewardInfoM.Index(actID, id)
		if info == nil {
			l4g.Errorf("ArtifactDebutSummonAct.CanReceiveAward: info not exist, id:%d", id)
			return false
		}

		if a.Count < info.DrawCount {
			l4g.Errorf("ArtifactDebutSummonAct.CanReceiveAward: not finish, id:%d, requestCount:%d, count:%d",
				id, info.DrawCount, a.Count)
			return false
		}

		if util.BitAndUint64(a.ReceivedIds, int(id)) {
			l4g.Errorf("ArtifactDebutSummonAct.CanReceiveAward: has reveived, id:%d, ReceivedIds:%d",
				id, a.ReceivedIds)
			return false
		}
	}
	return true
}

// 获取活动奖励数据
func (a *ArtifactDebutSummonAct) GetAward(actID uint32, ids []uint32) []*cl.Resource {
	return goxml.GetData().ArtifactDebutDrawRewardInfoM.GetAwardByIDs(actID, ids)
}

// 领取活动奖励
func (a *ArtifactDebutSummonAct) ReceiveAward(ids []uint32) uint64 {
	for _, id := range ids {
		util.BitSetTrueUint64Ptr(&a.ReceivedIds, int(id))
	}
	return a.ReceivedIds
}

// DrawArtifactFragments 新版抽卡
// @param servicer src
// @param uint32 category 抽卡分类 初级or高级
// @param uint32 drawCount 抽卡次数
// @return bool 是否执行成功
// @return ArtifactDebutDrawRet 抽卡结果数据
func (a *ArtifactDebutManager) DrawArtifactFragments(srv servicer, category, drawCount uint32) (bool, *ArtifactDebutDrawRet) {
	summon := a.getSummon(category)
	if summon == nil {
		summon = a.initArtifactDebutSummon(category)
	}

	tmpSummon := summon.Clone()
	awards := make([]*cl.Resource, 0, drawCount)
	for i := 0; i < int(drawCount); i++ {
		award, group, id, _ := a.doDraw(srv, tmpSummon)
		if award == nil {
			l4g.Errorf("user: %d DrawArtifact.doDraw: failed, summon:%+v", a.owner.ID(), tmpSummon)
			return false, nil
		}
		awards = append(awards, award...)
		//更新抽卡临时数据
		a.updateSummonData(id, group, tmpSummon)
	}

	return true, &ArtifactDebutDrawRet{
		Awards: awards,
		Summon: tmpSummon,
	}
}
