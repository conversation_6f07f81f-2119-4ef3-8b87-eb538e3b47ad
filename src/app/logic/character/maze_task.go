package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/ret"
)

func (m *MazeM) sendMazeTaskUpdateToClient(progressMap map[uint32]*cl.TaskTypeProgress) {
	msg := &cl.L2C_MazeTaskUpdate{
		Ret:          uint32(ret.RET_OK),
		TaskProgress: progressMap,
	}
	m.owner.SendCmdToGateway(cl.ID_MSG_L2C_MazeTaskUpdate, msg)
}

func (m *MazeM) OnMazeEvent(event uint32, progress uint64, values []uint32) {
	if m.data == nil || m.data.Task == nil {
		return
	}
	if m.data.Task.TaskProgress == nil {
		m.data.Task.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}

	progressMap, change := m.owner.TaskTypeOnEvent(m.data.Task.TaskProgress, event, progress, values)
	if change {
		m.sendMazeTaskUpdateToClient(progressMap)
		m.SetChange()
	}
}

func (m *MazeM) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return m.owner.CalcTaskProgress(taskTypeInfo)
	}
	if m.data == nil || m.data.Task == nil {
		return nil
	}

	return m.data.Task.TaskProgress[taskTypeInfo.Id]
}

func (m *MazeM) CanReceive(taskID uint32) bool {
	if m.data == nil || m.data.Task == nil {
		return false
	}

	if _, exist := m.data.Task.TaskAwarded[taskID]; exist {
		return false
	}

	return true
}

func (m *MazeM) ReceiveAward(taskIDs []uint32, level, score uint32) {
	if m.data == nil || m.data.Task == nil {
		return
	}
	if m.data.Task.TaskAwarded == nil {
		m.data.Task.TaskAwarded = make(map[uint32]bool)
	}
	// 更新任务领奖状态
	for _, id := range taskIDs {
		m.data.Task.TaskAwarded[id] = true
	}

	// 更新等级
	if level > m.data.Task.Level {
		m.data.Task.Level = level
	}
	// 更新积分
	m.data.Task.Score = score
}

func (m *MazeM) GetScore() uint32 {
	if m.data == nil || m.data.Task == nil {
		return 0
	}

	return m.data.Task.Score
}

func (m *MazeM) GetLevelAndAwardFromMazeTask(score, curLevel uint32) (newLevel uint32, awards []*cl.Resource) {
	newLevel = curLevel
	if score == 0 || m.data == nil || m.data.Task == nil {
		return
	}
	data := goxml.GetData().MazeTaskLevelInfoM.GetData(score)
	if data == nil || data.Level <= curLevel {
		return
	}
	newLevel = data.Level
	// 有可能跳级
	for i := curLevel + 1; i <= newLevel; i++ {
		levelInfo := goxml.GetData().MazeTaskLevelInfoM.Index(i)
		if levelInfo.RewardType == goxml.MazeTaskLevelAwardTypeItem {
			awards = append(awards, levelInfo.ClRes...)
		}
	}

	return
}

func (m *MazeM) GetMazeTaskLevel() uint32 {
	if m.data == nil || m.data.Task == nil {
		return 0
	}

	return m.data.Task.Level
}
