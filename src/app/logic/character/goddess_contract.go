package character

import (
	"app/goxml"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

type MemGoddess cl.Goddess

func newGoddess(id uint32) *cl.Goddess {
	return &cl.Goddess{Id: id}
}

func newGoddessContractData(id uint32) *cl.Goddess {
	return &cl.Goddess{
		Id:        id,
		Level:     goxml.GoddessInitLevel,
		Favourite: make([]uint32, 0, 3), //nolint:mnd
		SuitIds:   make([]uint32, 0, 1),
	}
}

func (g *MemGoddess) Clone() *cl.Goddess {
	return (*cl.Goddess)(g).Clone()
}

func (g *MemGoddess) IsStoryAwarded(story uint32) bool {
	for _, id := range g.GoddessStory {
		if id == story {
			return true
		}
	}
	return false
}

func (g *MemGoddess) SetAwardState(story uint32) {
	if len(g.GoddessStory) == 0 {
		g.GoddessStory = make([]uint32, 0, goxml.GetData().GoddessContractEliteInfoM.Len())
	}
	g.GoddessStory = append(g.GoddessStory, story)
}

func (g *MemGoddess) SetCollect(collect bool) {
	g.Collect = collect
}

func (g *MemGoddess) addExp(exp uint32) {
	g.Exp += exp
}

func (g *MemGoddess) SetFavourite(itemId uint32) {
	for _, v := range g.Favourite {
		if v == itemId {
			return
		}
	}
	g.Favourite = append(g.Favourite, itemId)
}

func (g *MemGoddess) autoLevelUp(user *User) (uint32, bool) {
	trustInfo := goxml.GetData().GoddessContractTrustInfoM.GetLevel(g.Exp)
	if trustInfo == nil {
		l4g.Errorf("user:%d GoddessContractData autoLevelUp failed exp:%d", user.ID(), g.Exp)
		return 0, false
	}
	if g.Level != trustInfo.LevelId {
		oldLevel := g.Level
		g.Level = trustInfo.LevelId
		return oldLevel, true
	}
	return 0, false
}

func (g *MemGoddess) UnlockSuit(oldLevel uint32) []*goxml.GoddessContractSkinExt {
	var unlockSuit []*goxml.GoddessContractSkinExt
	if oldLevel == g.Level {
		return unlockSuit
	}
	for i := oldLevel + 1; i <= g.Level; i++ {
		suitInfo := goxml.GetData().GoddessContractSkinInfoM.GetSuit(g.Id, i)
		if suitInfo == nil {
			continue
		}
		if suitInfo.AutoWear == 1 {
			g.EquipSuit = suitInfo.GoddessSkinId
		}
		g.SuitIds = append(g.SuitIds, suitInfo.GoddessSkinId)
		unlockSuit = append(unlockSuit, suitInfo)
	}

	return unlockSuit
}

func (g *MemGoddess) IsTopLevel() bool {
	maxLevel := goxml.GetData().GoddessContractTrustInfoM.GetMaxLevel()
	return g.Level >= maxLevel
}

func (g *MemGoddess) HasSuit(suitId uint32) bool {
	for _, s := range g.SuitIds {
		if s == suitId {
			return true
		}
	}
	return false
}

func (g *MemGoddess) SetSuit(suitId uint32) {
	g.EquipSuit = suitId
}

func (g *MemGoddess) SetRecoveryTimes() {
	g.RecoveryTime++
}

func (g *MemGoddess) CanRecovery(now int64) bool {
	return g.RecoveryTime <= now
}

func (g *MemGoddess) Recovery(count uint32, time int64) {
	g.RecoveryCount = count
	g.RecoveryTime = time
}

func (g *MemGoddess) GetActivityActive() bool {
	return g.ActivityActive
}

func (g *MemGoddess) SetActivityActive(state bool) {
	g.ActivityActive = state
}

/*func (g *MemGoddess) IsValidIndex(uid uint64, index uint32) bool {
	if g == nil {
		return false
	}
	if g.LastFinished == 0 {
		return index == 1
	}
	lastChapterCfg :=  goxml.GetData().GoddessTalesDungeonInfoM.Index(g.LastFinished)
	if lastChapterCfg == nil {
		l4g.Errorf("user: %d Tales.IsValidIndex lastFinished not found config: chapterID:%d",
			uid, g.LastFinished)
		return false
	}
	return lastChapterCfg.Index+1 == index
}

func (g *MemGoddess) SetPass() {
	g.FightPass = true
}

func (g *MemGoddess) SetFinish(chapterId uint32) {
	g.LastFinished = chapterId
	g.FightPass = false
}

func (g *MemGoddess) SetUnlockId(chapterId uint32) {
	g.UnlockId = chapterId
}

func (g *MemGoddess) GetLastFinished() uint32 {
	return g.LastFinished
}

func (g *MemGoddess) GetUnlockId() uint32 {
	return g.UnlockId
}

func (g *MemGoddess) SetChapterTakeAward(index uint32) {
	util.BitSetTrueUint32Ptr(&g.TakenStatus, int(index))
}

func (g *MemGoddess) GetTakenStatus() uint32 {
	return g.TakenStatus
} */
