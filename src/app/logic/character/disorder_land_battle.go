package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type DisorderBattleSrv interface {
	Do(
		Servicer,
		*DisorderLand,
		*DisorderLandMap,
		*DisorderLandNode,
		*cl.L2C_DisorderlandTriggerEvent,
		*goxml.DisorderlandDungeonInfoExt,
		*goxml.DisorderlandMapInfoExt,
		[]byte,
	) uint32
}

func getDisorderBattleSrvI(fightType uint32) DisorderBattleSrv {
	if fightType == uint32(common.DISORDER_LAND_BATTLE_DLB_BATTLE) {
		return DisorderBattleBattle(uint32(common.DISORDER_LAND_BATTLE_DLB_BATTLE))
	}

	return DisorderBattleSweep(uint32(common.DISORDER_LAND_BATTLE_DLB_SWEEP))
}

type DisorderBattleSweep uint32

//nolint:varnamelen
func (b DisorderBattleSweep) Do(
	srv Servicer,
	d *DisorderLand,
	_ *DisorderLandMap,
	node *DisorderLandNode,
	sMsg *cl.L2C_DisorderlandTriggerEvent,
	dInfo *goxml.DisorderlandDungeonInfoExt,
	_ *goxml.DisorderlandMapInfoExt,
	_ []byte,
) uint32 {
	if !node.GetStatus() {
		l4g.Errorf("user: %d battle: node not complete battle.", d.owner.ID())
		return uint32(cret.RET_DISORDER_LAND_NODE_NOT_COMPLETE)
	}

	awardSrv := getDisorderAwardSrvI(uint32(common.DISORDER_LAND_BATTLE_DLB_SWEEP))
	sMsg.Ret, sMsg.Awards = awardSrv.calcAward(srv, d, dInfo.MonumentId, dInfo.Drop, dInfo.DungeonId, "")
	if sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d battle: calc sweep drop award is failed. ret:%d", d.owner.ID(), sMsg.Ret)
		return sMsg.Ret
	}

	reason := uint32(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_SWEEP)
	sMsg.Ret, sMsg.Awards = d.owner.Trade(srv, d.getBattleCosts(), sMsg.Awards, reason, 0)
	if sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d battle: trade resource failed. ret:%d", d.owner.ID(), sMsg.Ret)
		return sMsg.Ret
	}
	d.updateSweepCount()

	return sMsg.Ret
}

type DisorderBattleBattle uint32

//nolint:varnamelen
func (b DisorderBattleBattle) Do(srv Servicer,
	d *DisorderLand,
	mapM *DisorderLandMap,
	node *DisorderLandNode,
	sMsg *cl.L2C_DisorderlandTriggerEvent,
	dInfo *goxml.DisorderlandDungeonInfoExt,
	mInfo *goxml.DisorderlandMapInfoExt,
	clientData []byte,
) uint32 {
	formationID := dInfo.GetFormationID()
	altRaisePS := b.extraSkill(d, dInfo, uint32(formationID))
	win, reportID, retCode := d.owner.AttackDisorderLand(srv, dInfo.MonsterGroup, formationID, nil, altRaisePS, clientData)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d battle: battle failed. ret: %d", d.owner.ID(), sMsg.Ret)
		return retCode
	}
	if win {
		sMsg.Ret = b.sendHurdLevelToCross(srv, d, dInfo.Type, dInfo.Level)
		if sMsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d battle: cross maintain", d.owner.ID())
			return sMsg.Ret
		}
		sMsg.Ret, sMsg.Awards = b.calcAwards(srv, b.isFirstPass(node.GetStatus()), d, dInfo, reportID)
		if sMsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d battle: calc fight drop award is failed. ret:%d", d.owner.ID(), sMsg.Ret)
			return sMsg.Ret
		}

		reason := uint32(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_BATTLE)
		sMsg.Ret, sMsg.Awards = d.owner.Trade(srv, d.getBattleCosts(), sMsg.Awards, reason, 0)
		if sMsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d battle: trade resource failed. ret:%d", d.owner.ID(), sMsg.Ret)
			return sMsg.Ret
		}
		if b.isFirstPass(node.GetStatus()) {
			mapM.addCompleteNodeNum()
			d.updateHurdleLevel(dInfo.Type, dInfo.Level)
			d.owner.CognitionManager().Send(srv, uint32(dInfo.GetFormationID()), mInfo.Id, 0, 0)
		}
		node.SetStatus()
	}

	sMsg.Win = win
	sMsg.ReportId = reportID

	return sMsg.Ret
}

func (b DisorderBattleBattle) extraSkill(
	d *DisorderLand,
	info *goxml.DisorderlandDungeonInfoExt,
	formationID uint32) *battle.AltRaisePS {
	altSkill := battle.NewAltRaisePS()
	b.newHeroExtraSkill(d, altSkill, formationID)
	b.monsterExtraSkill(d, altSkill, info.Level)
	b.mercenaryExtraSkill(d, altSkill, info, formationID)

	return altSkill
}

// 新英雄加技能
func (b DisorderBattleBattle) newHeroExtraSkill(d *DisorderLand, altRaisePS *battle.AltRaisePS, formationID uint32) {
	d.owner.SeasonLevel().AddPassSkillByNewHero(formationID, goxml.FormationTeamOneIndex, altRaisePS)
}

// 怪物加技能
func (b DisorderBattleBattle) monsterExtraSkill(d *DisorderLand, altSkill *battle.AltRaisePS, hurdleLevel uint32) {
	seasonDay := d.GetSeasonDayFromSeason()
	info := goxml.GetData().DisorderlandBossWeakInfoM.GetMaxBySeasonDay(hurdleLevel, seasonDay)
	if info == nil {
		return
	}
	pos2RaisePSs := make(map[uint32][]uint64)
	raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(info.Skill, 1)
	if raisePSInfo == nil {
		l4g.Errorf("user: %d monsterExtraSkill: raisePSInfo is nil. skillID: %d", d.owner.ID(), info.Skill)
		return
	}
	pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
	altSkill.AltDefense(pos2RaisePSs)
}

// 佣兵加技能
//
//nolint:varnamelen
func (b DisorderBattleBattle) mercenaryExtraSkill(
	d *DisorderLand,
	altSkill *battle.AltRaisePS,
	dInfo *goxml.DisorderlandDungeonInfoExt,
	formationID uint32,
) {
	if dInfo.CounterMonument == 0 {
		return
	}
	// 丰碑id不存在，给战斗传技能 id
	m := d.owner.SeasonLink().GetMonumentById(dInfo.CounterMonument)
	if m == nil {
		b.addMercenarySkill(d, altSkill, dInfo)
		return
	}
	mInfo := goxml.GetData().SeasonLinkMonumentRareInfoM.GetRecordByMonumentIdLevelUnique(m.GetMonumentId(), m.GetUniqueLevel())
	if mInfo == nil {
		l4g.Errorf("user: %d mercenaryExtraSkill: rareInfo is nil. monumentID:%d, level:%d", d.owner.ID(), m.GetMonumentId(), m.GetUniqueLevel())
		return
	}
	// 丰碑品质不满足，给战斗传技能 id
	if mInfo.Rare < dInfo.CounterMonumentRare {
		b.addMercenarySkill(d, altSkill, dInfo)
		return
	}
	// 丰碑 link 未激活，给战斗传技能 id
	seasonTrain := d.owner.GetMonumentForBattle(formationID, goxml.FormationTeamOneIndex)
	if seasonTrain == nil || seasonTrain.MonumentID != dInfo.CounterMonument {
		b.addMercenarySkill(d, altSkill, dInfo)
		return
	}
}

func (b DisorderBattleBattle) addMercenarySkill(d *DisorderLand, altSkill *battle.AltRaisePS, dInfo *goxml.DisorderlandDungeonInfoExt) {
	if dInfo.MercenarySkill == 0 {
		return
	}
	pos2RaisePSs := make(map[uint32][]uint64)
	raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(dInfo.MercenarySkill, 1)
	if raisePSInfo == nil {
		l4g.Errorf("user: %d mercenaryExtraSkill: raisePSInfo is nil. skillID: %d", d.owner.ID(), dInfo.MercenarySkill)
		return
	}
	pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
	altSkill.AltDefense(pos2RaisePSs)
}

// 同步数据到跨服: 等级 >= maxLevel 再同步
func (b DisorderBattleBattle) sendHurdLevelToCross(srv servicer, d *DisorderLand, hurdleType, hurdleLevel uint32) uint32 {
	isUpdate := d.isUpdateHurdleLevel(hurdleType, hurdleLevel)
	if isUpdate {
		msg := &l2c.L2C_DisorderLandSyncHurdleLevel{
			SeasonId: d.GetSeasonIDFromSeason(),
			Type:     hurdleType,
			MaxLevel: hurdleLevel,
		}
		if !srv.SendCmdToCross(l2c.ID_MSG_L2C_DisorderLandSyncHurdleLevel, d.owner.ID(), msg) {
			return uint32(cret.RET_CROSS_MAINTAIN)
		}
	}

	return uint32(cret.RET_OK)
}

func (b DisorderBattleBattle) isFirstPass(status bool) bool {
	return !status
}

func (b DisorderBattleBattle) calcAwards(
	srv servicer,
	isFirstPass bool,
	d *DisorderLand,
	dInfo *goxml.DisorderlandDungeonInfoExt,
	reportID string,
) (uint32, []*cl.Resource) {
	if isFirstPass && dInfo.FirstDrop > 0 {
		return b.firstPassAwards(dInfo.FirstDrop)
	}
	awardSrv := getDisorderAwardSrvI(uint32(common.DISORDER_LAND_BATTLE_DLB_BATTLE))
	return awardSrv.calcAward(srv, d, dInfo.MonumentId, dInfo.Drop, dInfo.DungeonId, reportID)
}

func (b DisorderBattleBattle) firstPassAwards(dropID uint32) (uint32, []*cl.Resource) {
	info := goxml.GetData().DisorderlandFirstDropInfoM.Index(dropID)
	if info == nil {
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
	}

	return uint32(cret.RET_OK), info.ClRes
}
