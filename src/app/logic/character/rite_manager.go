package character

import (
	"app/goxml"
	aevent "app/logic/event"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

const (
	PrevSeasonRiteIdBase = 100000000
)

var recycleItems = []uint32{75001, 75002, 75003, 75201, 75301, 75401, 75501, 75601, 75701}

type RiteM struct {
	owner *User
	rites map[uint32]*Rite

	//创建，修改，删除仪式使用，定时保存使用
	changes map[uint32]*cl.Rite
	deletes map[uint32]struct{}
}

func newRiteM(user *User) *RiteM {
	return &RiteM{
		owner:   user,
		rites:   make(map[uint32]*Rite),
		changes: make(map[uint32]*cl.Rite),
		deletes: make(map[uint32]struct{}),
	}
}

// Load ：加载数据
func (m *RiteM) Load(dbRites map[uint32]*cl.Rite) {
	for id, data := range dbRites {
		if id >= PrevSeasonRiteIdBase {
			l4g.Debugf("user: %d prev season rite id. id:%d rite:%v", m.owner.ID(), id, data)
			continue
		}
		if data == nil {
			continue
		}
		if id != data.RiteId {
			l4g.Errorf("user: %d rite id not match. id:%d idInData:%d", m.owner.ID(), id, data.RiteId)
			continue
		}
		rite := initRiteFromData(data)
		m.rites[id] = rite
		l4g.Debugf("user: %d init rite success. id:%d rite:%v", m.owner.ID(), id, data)
	}
}

// GetAllRites ：获取所有仪式
func (m *RiteM) GetAllRites() map[uint32]*Rite {
	if !m.isRiteOpen() {
		return nil
	}
	return m.rites
}

// clearAllRites : 清空所有仪式
func (m *RiteM) clearAllRites(srv servicer) {
	m.changes = make(map[uint32]*cl.Rite)
	for _, rite := range m.rites {
		if rite == nil {
			continue
		}
		riteId := rite.GetRiteId()
		m.deletes[riteId] = struct{}{}
		//备份处理
		// clRite := rite.Flush()
		// clRite.RiteId = PrevSeasonRiteIdBase + riteId
		// m.changes[clRite.RiteId] = clRite
	}
	l4g.Infof("user: %d all rites cleared. deletes:%v changes:%v", m.owner.ID(), m.deletes, m.changes)
	m.rites = make(map[uint32]*Rite)
	m.owner.FireCommonEvent(srv.EventM(), aevent.IeAllRitesCleared, 0)
	m.SendRitesToClient()
}

// FlushAll ：获取所有pb数据
func (m *RiteM) FlushAll() []*cl.Rite {
	if !m.isRiteOpen() {
		return nil
	}
	rites := make([]*cl.Rite, 0, len(m.rites))
	for _, rite := range m.rites {
		rites = append(rites, rite.Flush())
	}
	return rites
}

func (m *RiteM) isRiteOpen() bool {
	return goxml.IsSeasonFunctionOpen(goxml.GetData(), time.Now().Unix(), uint32(common.FUNCID_MODULE_RITE))
}

// GetRite ：获取一个仪式
func (m *RiteM) GetRite(id uint32) *Rite {
	if !m.isRiteOpen() {
		return nil
	}
	return m.getRite(id, false)
}

func (m *RiteM) GetRiteWithAutoCreate(id uint32) *Rite {
	if !m.IsRiteForUser(id) {
		l4g.Errorf("GetRite: rite is for monster. uid:%d id:%d", m.owner.ID(), id)
		return nil
	}
	return m.getRite(id, true)
}

func (m *RiteM) getRite(id uint32, autoCreate bool) *Rite {
	rite := m.rites[id]
	if rite == nil {
		if !autoCreate {
			return nil
		}
		rite = newRite(id)
		if rite == nil {
			l4g.Errorf("getRite: new rite failed. uid:%d id:%d", m.owner.ID(), id)
			return nil
		}
		m.rites[id] = rite

		m.SetChange(rite)
	}
	return rite
}

// GetCount ：全部仪式数量
func (m *RiteM) GetCount() uint32 {
	if !m.isRiteOpen() {
		return 0
	}
	return uint32(len(m.rites))
}

// SetChange ：设置变化的仪式
func (m *RiteM) SetChange(rite *Rite) {
	if rite == nil {
		return
	}
	riteId := rite.GetRiteId()
	delete(m.deletes, riteId)
	m.changes[riteId] = rite.GetData()

}

func (m *RiteM) SetChangeById(id uint32) {
	m.SetChange(m.getRite(id, false))
}

// Save ：增量保存仪式数据变化
func (m *RiteM) Save(msg *r2l.OpRites) bool {
	if msg == nil {
		return false
	}
	success := false
	changes := len(m.changes)
	if changes > 0 {
		msg.Changes = make([]*cl.Rite, 0, changes)
		for id, k := range m.changes {
			msg.Changes = append(msg.Changes, k.Clone())
			delete(m.changes, id)
		}
		success = true
	}
	deletes := len(m.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint32, 0, deletes)
		for id := range m.deletes {
			msg.Deletes = append(msg.Deletes, id)
			delete(m.deletes, id)
		}
		success = true
	}
	return success
}

// SetRare : 设置仪式品质
func (m *RiteM) GmSetRare(srv servicer, riteId, rare uint32) cret.RET {
	if !m.isRiteOpen() {
		l4g.Errorf("GmSetRare: rite not open. uid:%d riteId:%d rare:%d", m.owner.ID(), riteId, rare)
		return cret.RET_FUNCTION_NOT_OPEN
	}
	if !m.IsRiteForUser(riteId) {
		l4g.Errorf("AddMark: mark is not for user. uid:%d riteId:%d rare:%d", m.owner.ID(), riteId, rare)
		return cret.RET_RITE_NOT_FOR_USER
	}
	rite := m.getRite(riteId, true)
	if rite == nil {
		return cret.RET_SERVER_ERROR
	}
	grids := make([]*cl.RiteGrid, 0, FormationMaxPos)
	for pos := uint32(1); pos <= FormationMaxPos; pos++ {
		markInfo := goxml.GetData().RiteMarkInfoM.GetByRiteIdPositionRare(riteId, pos, rare)
		if markInfo == nil {
			return cret.RET_RITE_NOT_FOUND
		}
		grids = append(grids, &cl.RiteGrid{
			Pos:      pos,
			MarkGuid: srv.CreateUniqueID(),
			MarkId:   markInfo.Id,
		})
	}
	rite.SetGrids(grids)
	rite.SetRare(rare)
	m.SetChange(rite)
	m.SendRiteChangedToClient(rite)
	l4g.Infof("GmSetRare: success. uid:%d riteId:%d rare:%d rite:%v", m.owner.ID(), riteId, rare, rite.data)

	return cret.RET_OK
}

// AddMark ：添加指定印记
// @return increasedResource *cl.Resource 增加的资源
// @return reducedResource *cl.Resource 减少的资源
// @return showAwards []*cl.Resource 客户端显示的奖励
// @return decomposedResults []*cl.Resource 分解产物
func (m *RiteM) AddMark(srv servicer, markGuid uint64, markId uint32) (*cl.Resource, *cl.Resource, []*cl.Resource, []*cl.Resource) {
	if !m.isRiteOpen() {
		l4g.Errorf("AddMark: rite not open. uid:%d markId:%d", m.owner.ID(), markId)
		return nil, nil, nil, nil
	}
	markInfo := goxml.GetData().RiteMarkInfoM.Index(markId)
	if markInfo == nil {
		l4g.Errorf("AddMark: mark info not found. uid:%d markId:%d", m.owner.ID(), markId)
		return nil, nil, nil, nil
	}
	if !m.IsRiteForUser(markInfo.RiteId) {
		l4g.Errorf("AddMark: mark is not for user. uid:%d markId:%d", m.owner.ID(), markId)
		return nil, nil, nil, nil
	}
	rite := m.getRite(markInfo.RiteId, true)
	if rite == nil {
		return nil, nil, nil, nil
	}
	changed, increaseResource, reduceResource, showAwards, decomposedResults := rite.AddMark(markGuid, markInfo)
	if changed {
		gridsMinRare := rite.GetGridsMinRare()
		if gridsMinRare > rite.GetRare() {
			oldRare := rite.GetRare()
			rite.SetRare(gridsMinRare)
			m.owner.FireCommonEvent(srv.EventM(), aevent.IeRiteUpToX, uint64(1), rite.GetRiteId(), gridsMinRare, oldRare)
		}
		m.SetChange(rite)
		m.SendRiteChangedToClient(rite)
		l4g.Infof("AddMark: add success and rite changed. uid:%d markId:%d rite:%+v", m.owner.ID(), markId, rite.Flush())
	} else {
		l4g.Infof("AddMark: add success but rite not changed. uid:%d markId:%d", m.owner.ID(), markId)
	}
	m.owner.LogRiteMarkCollection(srv, markId, markInfo.Rare, 1)
	m.owner.FireCommonEvent(srv.EventM(), aevent.IeRiteMarkGet, uint64(1), markInfo.Rare, markInfo.PowerId)
	return increaseResource, reduceResource, showAwards, decomposedResults
}

func (m *RiteM) IsRiteForUser(riteId uint32) bool {
	riteInfo := goxml.GetData().RiteInfoM.Index(riteId)
	if riteInfo == nil {
		l4g.Errorf("IsRiteForUser: riteInfo not found. uid:%d riteId:%d", m.owner.ID(), riteId)
		return false
	}
	return riteInfo.IsForMonster == 0
}

func (m *RiteM) SendRiteChangedToClient(rite *Rite) {
	if rite == nil {
		return
	}
	msg := &cl.L2C_RiteChanged{
		Ret:  uint32(cret.RET_OK),
		Rite: rite.Flush(),
	}
	m.owner.SendCmdToGateway(cl.ID_MSG_L2C_RiteChanged, msg)
}

func (m *RiteM) SendRitesToClient() {
	smsg := &cl.L2C_RiteGetData{
		Ret:   uint32(cret.RET_OK),
		Rites: m.FlushAll(),
	}
	m.owner.SendCmdToGateway(cl.ID_MSG_L2C_RiteGetData, smsg)
}

func (m *RiteM) getRecyclePoint(seasonID uint32) uint32 {
	recyclePoint := uint32(0)
	for _, rite := range m.rites {
		recyclePoint += rite.GetRecyclePoint(seasonID)
	}
	return recyclePoint
}

func (m *RiteM) OnSeasonEnd(srv servicer, needSettleAward bool) {
	seasonID := m.owner.GetSeasonID()
	if seasonID == 0 {
		return
	}
	//物品回收
	consumes := make([]*cl.Resource, 0, len(recycleItems))
	for _, itemId := range recycleItems {
		count := m.owner.GetItemCount(itemId, srv)
		if count > 0 {
			consumes = append(consumes, &cl.Resource{
				Type:  uint32(common.RESOURCE_ITEM),
				Value: itemId,
				Count: count,
			})
		}
	}
	if len(consumes) > 0 {
		m.owner.consume(srv, consumes, uint32(log.RESOURCE_CHANGE_REASON_RITE_RECYCLE), 0)
	}

	recyclePoint := m.getRecyclePoint(seasonID)
	rewardInfo := goxml.GetData().RiteRecycleRewardInfoM.GetRecordBySeasonIdPointMaxLe(seasonID, recyclePoint)
	if rewardInfo == nil || len(rewardInfo.Rewards) <= 0 {
		m.owner.LogRiteRecycle(srv, recyclePoint)
		l4g.Infof("recycle: no reward. uid:%d recyclePoint:%d", m.owner.ID(), recyclePoint)
	} else {
		RiteRecycleMail(srv, m.owner, rewardInfo.Rewards, MailIDRiteRecycle)
		m.owner.LogRiteRecycle(srv, recyclePoint)
		l4g.Infof("recycle: success. uid:%d recyclePoint:%d rewards:%v", m.owner.ID(), recyclePoint, rewardInfo.Rewards)
	}

	m.clearAllRites(srv)
}
