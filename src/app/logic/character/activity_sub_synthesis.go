package character

import (
	"app/goxml"
	"app/protos/in/log"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
)

type ActivitySubSynthesisS struct {
	baseController
}

func (as *ActivitySubSynthesisS) InitSubFunc(sum *cl.ActivitySum) {
	sum.Synthesis = &cl.ActivitySubSynthesis{}
}

// TODO(yta) 次数改为道具
func (as *ActivitySubSynthesisS) AfterInitSubFunc(sum *cl.ActivitySum, user *User, s Servicer, resetDaily bool) {
	// 获取次数类型
	var numberType uint32
	if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("GAME_TIME"); info != nil {
		numberType = info.Count
	}
	if numberType == 0 {
		l4g.Errorf("ActivitySubSynthesisS AfterInitSubFunc: synthesis game config not exist.")
		return
	}

	// 每日次数
	var dailyNum uint32
	if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("GAME_TIME_DAILY"); info != nil {
		dailyNum = info.Count
	}

	// 最大次数
	var maxNum uint32
	if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("GAME_TIME_MAX"); info != nil {
		maxNum = info.Count
	}
	numInfo := &cl.NumInfo{
		LeftNum: min(dailyNum, maxNum),
		NumMax:  maxNum,
	}
	if user == nil || user.dbUser == nil || user.dbUser.Module == nil || user.dbUser.Module.Daily == nil {
		return
	}
	if user.dbUser.Module.Daily.NumInfo == nil {
		user.dbUser.Module.Daily.NumInfo = make(map[uint32]*cl.NumInfo)
	}
	user.dbUser.Module.Daily.NumInfo[numberType] = numInfo
	// 数据落地
	user.SaveDaily()
	// 发给前端
	retNums := make(map[uint32]*cl.NumInfo)
	retNums[numberType] = numInfo
	user.SendCmdToGateway(cl.ID_MSG_L2C_OpNum, &cl.L2C_OpNum{
		NumInfo: retNums,
	})

	// 发送道具
	initAwards := make([]*cl.Resource, 0, 2)
	if item1 := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("OBJECT_DELETE_ITEM"); item1 != nil {
		initAwards = append(initAwards, goxml.GenSimpleResource(item1.Type, item1.Value, 1))
	}
	if item2 := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("OBJECT_UP_ITEM"); item2 != nil {
		initAwards = append(initAwards, goxml.GenSimpleResource(item2.Type, item2.Value, 1))
	}
	ret, _ := user.Award(s, initAwards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_SYNTHESIS_GAME_INIT), 0)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("ActivitySubSynthesisS AfterInitSubFunc: award failed. retCode: %d", ret)
	}
}

// TODO(yta) number_type的每日重置会先把次数重置掉吗
func (as *ActivitySubSynthesisS) RestDaily(sum *cl.ActivitySum, now int64, user *User, s Servicer) {
	// 获取次数类型
	var numberType uint32
	if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("GAME_TIME"); info != nil {
		numberType = info.Count
	}
	if numberType == 0 {
		l4g.Errorf("ActivitySubSynthesisS RestDaily: synthesis game config not exist.")
		return
	}

	// 每日次数
	var dailyNum uint32
	if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("GAME_TIME_DAILY"); info != nil {
		dailyNum = info.Count
	}

	// 最大次数
	var maxNum uint32
	if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("GAME_TIME_MAX"); info != nil {
		maxNum = info.Count
	}

	if user == nil || user.dbUser == nil || user.dbUser.Module == nil || user.dbUser.Module.Daily == nil {
		return
	}
	var oldNum uint32
	data, exist := user.dbUser.Module.Daily.NumInfo[numberType]
	if exist {
		oldNum = data.LeftNum
	} else if user.dbUser.Module.Daily.NumInfo == nil {
		user.dbUser.Module.Daily.NumInfo = make(map[uint32]*cl.NumInfo)
	}

	numInfo := &cl.NumInfo{
		LeftNum: min(oldNum+dailyNum, maxNum),
		NumMax:  maxNum,
	}
	user.dbUser.Module.Daily.NumInfo[numberType] = numInfo
	// 数据落地
	user.SaveDaily()
	// 发给前端
	retNums := make(map[uint32]*cl.NumInfo)
	retNums[numberType] = numInfo
	user.SendCmdToGateway(cl.ID_MSG_L2C_OpNum, &cl.L2C_OpNum{
		NumInfo: retNums,
	})

	// 重置每日道具购买次数
	if sum != nil && sum.Synthesis != nil {
		sum.Synthesis.ItemDailyBuyNum = make(map[uint32]uint32)
	}
}

func (a *ActivitySum) SynthesisGetTotalScore() uint32 {
	if a.Synthesis == nil {
		return 0
	}
	return a.Synthesis.TotalScore
}

func (a *ActivitySum) SynthesisAddTotalScore(score uint32) {
	if score == 0 {
		return
	}
	if a.Synthesis == nil {
		return
	}
	a.Synthesis.TotalScore += score
}

func (a *ActivitySum) SynthesisGetMaxScore() uint32 {
	if a.Synthesis == nil {
		return 0
	}
	return a.Synthesis.MaxScore
}

func (a *ActivitySum) SynthesisSetMaxScore(score uint32) {
	if a.Synthesis == nil {
		return
	}
	a.Synthesis.MaxScore = score
}

func (a *ActivitySum) SynthesisGetMaxLevel() uint32 {
	if a.Synthesis == nil {
		return 0
	}
	return a.Synthesis.MaxSynthesisLevel
}

func (a *ActivitySum) SynthesisSetMaxLevel(level uint32) {
	if a.Synthesis == nil {
		return
	}
	a.Synthesis.MaxSynthesisLevel = level
}

func (a *ActivitySum) SynthesisGetGameOpen() bool {
	if a.Synthesis == nil {
		return false
	}
	return a.Synthesis.GameOpen
}

func (a *ActivitySum) SynthesisSetGameOpen(open bool) {
	if a.Synthesis == nil {
		return
	}
	a.Synthesis.GameOpen = open
}

func (a *ActivitySum) SynthesisGetBuyCount(itemType uint32) uint32 {
	if a.Synthesis == nil {
		return 0
	}
	return a.Synthesis.ItemDailyBuyNum[itemType]
}
