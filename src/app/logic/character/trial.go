package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/logic/helper"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type Trials struct {
	owner *User
	data  map[uint32]*cl.TrialInfo
}

func newTrials(user *User) *Trials {
	return &Trials{
		owner: user,
	}
}

// 加载数据
func (t *Trials) load(dbData map[uint32]*cl.TrialInfo) {
	if t.data == nil {
		t.data = make(map[uint32]*cl.TrialInfo)
	}

	for trialType, data := range dbData {
		if data == nil {
			data = &cl.TrialInfo{
				OnHook: &cl.TrialOnHook{},
			}
		}
		if data.OnHook == nil {
			data.OnHook = &cl.TrialOnHook{}
		}
		t.data[trialType] = data
	}
}

func (t *Trials) save(trialType uint32) {
	t.owner.dbUser.Module.Trials[trialType] = t.data[trialType]
	t.owner.setSaveTag(saveTagModule)
}

func (t *Trials) Flush() []*cl.TrialInfo {
	allInfo := make([]*cl.TrialInfo, 0, len(t.data))
	for _, v := range t.data {
		allInfo = append(allInfo, v.Clone())
	}
	return allInfo
}

func (t *Trials) Trial(trialType uint32) *cl.TrialInfo {
	if trialInfo, exist := t.data[trialType]; exist {
		return trialInfo
	}
	return nil
}

func (t *Trials) LevelWin(srv servicer, trialType, level, star uint32) {
	info := t.data[trialType]
	if info == nil {
		info = &cl.TrialInfo{
			OnHook: &cl.TrialOnHook{},
		}
		if info.OnHook == nil {
			info.OnHook = &cl.TrialOnHook{}
		}
		info.OpenTime = time.Now().Unix()
	}

	info.Type = trialType
	if info.Level < level { // 新level，直接保存新数据
		info.Level = level
		info.Star = star
	}

	if info.Level == level && star > info.Star { // 之前的level，并且打出新的星数
		info.Star = star
	}
	//新手引导第一个开的材料本给固定奖励
	if !t.owner.TrialGuide() {
		trialInfo := goxml.GetData().TrialInfoM.Index(trialType, level)
		if trialInfo != nil {
			duration := goxml.GetData().TrialConfigInfoM.GetInitTrialTime()
			awards, leftResCount := t.calcOnhookAwards(duration, trialInfo, true, info.OnHook.LeftResCount)
			info.OnHook.Awards = append(info.OnHook.Awards, awards...)
			info.OnHook.LeftResCount = leftResCount
		}
		t.owner.SetTrailGuideFinish()
	}

	t.data[trialType] = info
	t.save(trialType)
	t.owner.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_TRIAL), trialType, level)
}

func (t *Trials) CalcTotalStar(trialType uint32) uint32 {
	var totalStar uint32
	for _, trialInfo := range t.data {
		stars := uint32(0)
		if trialInfo.Level > 1 {
			//stars += (trialInfo.Level - 1) * 3 // 当前level之前的，都是三星
			stars += trialInfo.Level - 1 // 当前level之前的，策划要求按1星算
		}
		stars += trialInfo.Star
		if trialType != 0 && trialType == trialInfo.Type {
			return stars
		}
		totalStar += stars
	}

	return totalStar
}

func (t *Trials) AltRaisePS(trialType, level uint32, formationID uint32) (uint32, *battle.AltRaisePS) {
	team := t.owner.GetFormationTeam(formationID, goxml.FormationTeamOneIndex)
	if team == nil {
		l4g.Errorf("user:%d AltRaisePS: formation is nil.", t.owner.ID())
		return uint32(ret.RET_FORMATION_NOT_EXIST), nil
	}

	info := goxml.GetData().TrialInfoM.Index(trialType, level)
	if info == nil {
		l4g.Errorf("user:%d Trials.AltRaisePS: info is nil, type: %d level:%d", t.owner.ID(), trialType, level)
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}

	var (
		sameJobCount                   uint32
		exclusiveEmblemRareRedCount    uint32
		exclusiveEmblemRareOrangeCount uint32
	)
	reqHeroes := goxml.GetData().TrialHeroInfoM.GetHeroIDs(trialType)
	heroM := t.owner.HeroManager()
	for _, v := range team.Info {
		hero := heroM.Get(v.Hid)
		if hero == nil {
			continue
		}

		if _, exist := reqHeroes[hero.data.SysId]; exist {
			sameJobCount++

			if hero.IsWearAllExclusiveEmblem(t.owner, info.MirageSuitRace2) {
				exclusiveEmblemRareRedCount++
				exclusiveEmblemRareOrangeCount++
			} else if hero.IsWearAllExclusiveEmblem(t.owner, info.MirageSuitRace1) {
				exclusiveEmblemRareOrangeCount++
			}
		}
	}

	// 材料本中所有增益buff，均基于限定职业的英雄
	if sameJobCount == 0 {
		return uint32(ret.RET_OK), nil
	}

	skillList := make([]uint64, 0, 1)
	//相同职业增益效果
	skillID := getSkillIDByFixedLayer(info.PassiveLayerId1, sameJobCount, info.LayerMax1)
	if skillID > 0 {
		skillList = append(skillList, skillID)
	}

	//全套橙色专属符文增益效果
	if exclusiveEmblemRareOrangeCount > 0 {
		skillID = getSkillIDByFixedLayer(info.PassiveLayerId2, exclusiveEmblemRareOrangeCount, info.LayerMax2)
		if skillID > 0 {
			skillList = append(skillList, skillID)
		}
	}

	//全套红色专属符文增益效果
	if exclusiveEmblemRareRedCount > 0 {
		skillID = getSkillIDByFixedLayer(info.PassiveLayerId3, exclusiveEmblemRareRedCount, info.LayerMax3)
		if skillID > 0 {
			skillList = append(skillList, skillID)
		}
	}

	if len(skillList) == 0 {
		return uint32(ret.RET_OK), nil
	}

	raisePSMap := make(map[uint32][]uint64)
	raisePSMap[battle.TeamUniterBattlePos] = skillList
	altRaisePS := battle.NewAltRaisePS()
	altRaisePS.AltAttack(raisePSMap)
	return uint32(ret.RET_OK), altRaisePS
}

// 修正层数，获取技能ID
// @param passiveLayerID uint32 被动技能id
// @param count uint32 层数
// @param limit uint32 最大层数
// @return uint64 raise_passive_skill表中技能id
func getSkillIDByFixedLayer(passiveLayerID, count, limit uint32) uint64 {
	if count > limit {
		count = limit
	}

	info := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(passiveLayerID, count)
	if info == nil {
		l4g.Errorf("[FATAL] getSkillIDByFixedLayer: no info, passiveLayerID: %d count:%d", passiveLayerID, count)
		return 0
	}
	return info.ID
}

func (t *Trials) GetLevel(trialType uint32) uint32 {
	return t.data[trialType].GetLevel()
}

func (t *Trials) CalcPreviewAward(srv servicer, now int64) (uint32, []*cl.Resource, map[uint32]int64) {
	retAwards := make([]*cl.Resource, 0, len(t.data)*2)
	efficientTimes := make(map[uint32]int64, len(t.data))
	playerLevelInfo := goxml.GetData().PlayerLevelInfoM.Index(t.owner.Level())
	if playerLevelInfo == nil {
		l4g.Errorf("user:%d get user level:%d, no info", t.owner.ID(), t.owner.Level())
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil, nil
	}
	maxTime := playerLevelInfo.TrialCumulativeDuration * util.HourSecs
	for _, trial := range t.data {
		if trial == nil {
			continue
		}
		trialInfo := goxml.GetData().TrialInfoM.Index(trial.Type, trial.Level)
		if trialInfo == nil {
			l4g.Errorf("user:%d trial type:%d level:%d info not exist", t.owner.ID(), trial.Type, trial.Level)
			continue
		}

		if now <= trial.OnHook.LastCalcTm {
			continue
		}

		t.calcBaseAward(srv, trial, trialInfo, now, maxTime)

		t.calcRandomDropAward(srv, trial, trialInfo, now, maxTime)
		trial.OnHook.Awards = t.owner.MergeResources(trial.OnHook.Awards)
		l4g.Debugf("user:%d CalcPreviewAward: onHookAwards:%+v", t.owner.ID(), trial.OnHook.Awards)
		efficientTimes[trial.Type] = trial.OnHook.CalcRewardDuration
		t.save(trial.Type)
		retAwards = append(retAwards, trial.OnHook.Awards...)
	}
	return uint32(ret.RET_OK), retAwards, efficientTimes
}

func (t *Trials) GetReward() []*cl.Resource {
	retAwards := make([]*cl.Resource, 0, len(t.data)*2)
	for _, trial := range t.data {
		if trial == nil {
			continue
		}
		retAwards = append(retAwards, trial.OnHook.Awards...)
	}
	return retAwards
}

func (t *Trials) ResetTrialsOnHook() (map[uint32]int64, []*log.TrialOnHook) {
	startTimes := make(map[uint32]int64, len(t.data))
	logs := make([]*log.TrialOnHook, 0, len(t.data))
	for _, trial := range t.data {
		if trial == nil {
			continue
		}
		logs = append(logs, &log.TrialOnHook{
			TrialType:  trial.Type,
			TrialLevel: trial.Level,
			OnHookTime: trial.OnHook.LastCalcTm - trial.OnHook.OnHookTm,
		})
		trial.OnHook.OnHookTm = trial.OnHook.LastCalcTm
		trial.OnHook.RandomDropStartTm = trial.OnHook.LastRandomDropTm
		trial.OnHook.Awards = trial.OnHook.Awards[:0]
		startTimes[trial.Type] = trial.OnHook.OnHookTm
		trial.OnHook.CalcRewardDuration = 0
		trial.OnHook.CalcRandRewardDuration = 0
		t.save(trial.Type)
	}
	return startTimes, logs
}

func (t *Trials) CalcSpeedAwards(srv servicer, buyCount map[uint32]uint32) (uint32, []*cl.Resource, []*log.TrialOnHook) {
	retAwards := make([]*cl.Resource, 0, len(buyCount)*2)
	logs := make([]*log.TrialOnHook, 0, len(buyCount))
	for trialType, count := range buyCount {
		if count == 0 {
			l4g.Errorf("user:%d calc speed awards trialType:%d count is zero", t.owner.ID(), trialType)
			return uint32(ret.RET_CLIENT_REQUEST_ERROR), nil, nil
		}

		trial, exist := t.data[trialType]
		if !exist {
			l4g.Errorf("user:%d speed trial onHook failed type:%d not init", t.owner.ID(), trialType)
			return uint32(ret.RET_TRAIL_TYPE_NOT_INIT), nil, nil
		}

		trialInfo := goxml.GetData().TrialInfoM.Index(trial.Type, trial.Level)
		if trialInfo == nil {
			l4g.Errorf("user:%d trial type:%d level:%d info not exist", t.owner.ID(), trialType, trial.Level)
			return uint32(ret.RET_SYSTEM_DATA_ERROR), nil, nil
		}

		duration := goxml.GetData().TrialConfigInfoM.GetSpeedOnhookSeconds()
		awards := t.calcSpeedOnhookAwards(srv, duration*int64(count), trialInfo)
		retAwards = append(retAwards, awards...)

		for i := 0; i < int(count); i++ {
			dropAwards, _ := t.owner.Drop().DoDrop(srv.Rand(), trialInfo.RandomBuyDropGroup1)
			retAwards = append(retAwards, dropAwards...)
		}
		logs = append(logs, &log.TrialOnHook{
			TrialType:  trial.Type,
			Num:        count,
			TrialLevel: trial.Level,
			OnHookTime: trial.OnHook.LastCalcTm - trial.OnHook.OnHookTm,
		})
	}

	retAwards = t.owner.MergeResources(retAwards)
	l4g.Debugf("user:%d CalcSpeedAwards: totalAwards:%+v", t.owner.ID(), retAwards)
	if guildMedal := srv.GetGuildMedal(t.owner.ID()); guildMedal != nil {
		retAwards = t.guildMedalAdd(retAwards, guildMedal)
		l4g.Debugf("user:%d CalcSpeedAwards: totalAwardsAfterGuildMedalAdd:%+v", t.owner.ID(), retAwards)
	}
	return uint32(ret.RET_OK), retAwards, logs
}

func (t *Trials) ChangeLevelEvent(srv servicer, trialType, oldLevel, newLevel uint32) {
	trial, exist := t.data[trialType]
	if !exist {
		if trial == nil {
			trial = &cl.TrialInfo{
				OnHook: &cl.TrialOnHook{},
			}
		}
		t.data[trialType] = trial
	}
	trial = t.data[trialType]
	if trial.OnHook == nil {
		trial.OnHook = &cl.TrialOnHook{}
	}

	now := time.Now().Unix()
	if oldLevel == 0 {
		trial.OnHook.OnHookTm = now
		trial.OnHook.LastCalcTm = now
		trial.OnHook.RandomDropStartTm = now
		trial.OnHook.LastRandomDropTm = now
		t.data[trialType] = trial
		t.save(trialType)
		return
	}

	oldInfo := goxml.GetData().TrialInfoM.Index(trialType, oldLevel)
	if oldInfo == nil {
		return
	}
	newInfo := goxml.GetData().TrialInfoM.Index(trialType, newLevel)
	if newInfo == nil {
		return
	}

	playerLevelInfo := goxml.GetData().PlayerLevelInfoM.Index(t.owner.Level())
	if playerLevelInfo == nil {
		l4g.Errorf("user:%d ChangeLevelEvent, no info, level:%d ", t.owner.ID(), t.owner.Level())
		return
	}

	maxTime := playerLevelInfo.TrialCumulativeDuration * util.HourSecs
	needBaseSettle, needRandomSettle := isNeedSettle(oldInfo, newInfo)
	if needBaseSettle {
		t.calcBaseAward(srv, trial, oldInfo, now, maxTime)
	}
	if needRandomSettle {
		t.calcRandomDropAward(srv, trial, oldInfo, now, maxTime)
	}

	trial.OnHook.Awards = t.owner.MergeResources(trial.OnHook.Awards)
	l4g.Debugf("user:%d ChangeLevelEvent: onHookAwards:%+v", t.owner.ID(), trial.OnHook.Awards)
	t.save(trialType)
}

// 是否需要结算
// @param oldInfo *goxml.TrialInfoExt
// @param newInfo *goxml.TrialInfoExt
// @return bool 基础奖励是否需要结算
// @return bool 随机奖励是否需要结算
func isNeedSettle(oldInfo, newInfo *goxml.TrialInfoExt) (bool, bool) {
	var needBaseSettle, needRandomSettle bool
	if oldInfo.OnHookRewardType1 != newInfo.OnHookRewardType1 || oldInfo.OnHookRewardValue1 != newInfo.OnHookRewardValue1 ||
		oldInfo.OnHookRewardProductiveness1 != newInfo.OnHookRewardProductiveness1 {
		needBaseSettle = true
	}
	if oldInfo.OnHookRewardType2 != newInfo.OnHookRewardType2 || oldInfo.OnHookRewardValue2 != newInfo.OnHookRewardValue2 ||
		oldInfo.OnHookRewardProductiveness2 != newInfo.OnHookRewardProductiveness2 {
		needBaseSettle = true
	}
	if oldInfo.OnHookRewardType3 != newInfo.OnHookRewardType3 || oldInfo.OnHookRewardValue3 != newInfo.OnHookRewardValue3 ||
		oldInfo.OnHookRewardProductiveness3 != newInfo.OnHookRewardProductiveness3 {
		needBaseSettle = true
	}

	if oldInfo.RandomFreeDropGroup1 != newInfo.RandomFreeDropGroup1 {
		needRandomSettle = true
	}
	return needBaseSettle, needRandomSettle
}

func (t *Trials) calcBaseAward(srv servicer, trial *cl.TrialInfo, trialInfo *goxml.TrialInfoExt, now, maxTime int64) {
	//已结算奖励的时间大于等于最大时间返回
	if trial.OnHook.CalcRewardDuration >= maxTime {
		trial.OnHook.LastCalcTm = now
		return
	}
	//这次结算时间的间隔 = 当前时间 - 上次结算时间
	duration := now - trial.OnHook.LastCalcTm
	// 这次时间间隔 + 本次有效奖励时间 > 最大时间
	// 这次时间间隔 = 最大有效时间 - 本次挂机有效的奖励时间
	if duration+trial.OnHook.CalcRewardDuration > maxTime {
		duration = maxTime - trial.OnHook.CalcRewardDuration
	}

	awards, leftResCount := t.calcOnhookAwards(duration, trialInfo, false, trial.OnHook.LeftResCount)
	l4g.Debugf("user:%d calcBaseAward: onHookAwards:%+v baseAward:%+v", t.owner.ID(), trial.OnHook.Awards, awards)
	if guildMedal := srv.GetGuildMedal(t.owner.ID()); guildMedal != nil {
		awards = t.guildMedalAdd(awards, guildMedal)
	}
	l4g.Debugf("user:%d calcBaseAward: onHookAwards:%+v baseAward afterGuildMedalAdd:%+v", t.owner.ID(), trial.OnHook.Awards, awards)
	trial.OnHook.Awards = append(trial.OnHook.Awards, awards...)
	trial.OnHook.LeftResCount = leftResCount
	trial.OnHook.LastCalcTm = now
	trial.OnHook.CalcRewardDuration += duration
}

func (t *Trials) guildMedalAdd(awards []*cl.Resource, guildMedal *db.LogicGuildMedal) []*cl.Resource {
	addValue := uint32(0)
	for _, medal := range guildMedal.Medals {
		info := goxml.GetData().GuildMedalTaskInfoM.Index(medal.GetMedalId())
		if info == nil {
			continue
		}
		if info.AddType != goxml.GuildMedalAddTrial {
			continue
		}
		addValue += info.AddValue
	}
	if addValue == 0 {
		return awards
	}

	newAwards := make([]*cl.Resource, 0, len(awards))
	for _, award := range awards {
		cAward := award.Clone()
		cAward.Count = cAward.Count + cAward.Count*addValue/10000 //nolint:mnd
		newAwards = append(newAwards, cAward)
	}

	return newAwards
}

func (t *Trials) calcRandomDropAward(srv servicer, trial *cl.TrialInfo, trialInfo *goxml.TrialInfoExt, now, maxTime int64) {
	//已结算奖励的时间大于等于最大时间返回
	if trial.OnHook.CalcRandRewardDuration >= maxTime {
		trial.OnHook.LastRandomDropTm = now
		return
	}
	//这次结算时间的间隔 = 当前时间 - 上次结算时间
	duration := now - trial.OnHook.LastRandomDropTm
	// 这次时间间隔 + 本次有效奖励时间 > 最大时间
	// 这次时间间隔 = 最大有效时间 - 本次挂机有效的奖励时间
	if duration+trial.OnHook.CalcRandRewardDuration > maxTime {
		duration = maxTime - trial.OnHook.CalcRandRewardDuration
	}
	// 累加上一次未结算的时间
	duration += trial.OnHook.SaveRandomDuration
	freeTime := goxml.GetData().TrialConfigInfoM.GetRandomFreeDropGroupTime()
	isZero, count := util.DivFloor(util.HourSecs*int64(freeTime), duration)
	if !isZero {
		for i := 0; i < int(count); i++ {
			dropAwards, _ := t.owner.Drop().DoDrop(srv.Rand(), trialInfo.RandomFreeDropGroup1)
			l4g.Debugf("user:%d calcRandomDropAward: onHookAwards:%+v dropAward:%+v", t.owner.ID(), trial.OnHook.Awards, dropAwards)
			if guildMedal := srv.GetGuildMedal(t.owner.ID()); guildMedal != nil {
				dropAwards = t.guildMedalAdd(dropAwards, guildMedal)
			}
			l4g.Debugf("user:%d calcRandomDropAward: onHookAwards:%+v dropAwardAfterGuildMedalAdd:%+v", t.owner.ID(), trial.OnHook.Awards, dropAwards)
			trial.OnHook.Awards = append(trial.OnHook.Awards, dropAwards...)
		}
	}
	//不足一次保存至下一次
	surplus := duration % (util.HourSecs * int64(freeTime))
	trial.OnHook.LastRandomDropTm = now
	trial.OnHook.SaveRandomDuration = surplus
	trial.OnHook.CalcRandRewardDuration += duration
}

func (t *Trials) OpenCopyIDs() map[uint32]struct{} {
	copyIDMap := make(map[uint32]struct{})
	day := helper.Weekday(time.Now().Unix())
	for copyID := range goxml.GetData().TrialCopyInfoM.Group(day) {
		copyIDMap[copyID] = struct{}{}
	}

	return copyIDMap
}

// 计算挂机奖励
// @param duration int64 挂机时长（包含次数因素）
// @param info *goxml.TrialInfoExt
// @param isGuide bool 是否来自于引导
// @param leftResCount []int64 上次未结算的剩余资源数量
// @return []*cl.Resource 挂机奖励
// @return []int64 未结算的剩余资源数量
func (t *Trials) calcOnhookAwards(duration int64, info *goxml.TrialInfoExt,
	isGuide bool, leftResCount []int64) ([]*cl.Resource, []int64) {
	if len(leftResCount) == 0 {
		leftResCount = make([]int64, goxml.TrialOnhookAwardCount)
	}

	var products []int64
	if !isGuide {
		products = goxml.GetData().VipPrivilegeInfoM.TrailOnhookEfficiency(t.owner.Vip(), info.OnhookEfficiency)
	} else {
		products = fixEfficiency(info.OnhookEfficiency)
	}

	awards := make([]*cl.Resource, 0, 1)
	retLeftCount := make([]int64, goxml.TrialOnhookAwardCount)
	baseParam := util.HourSecs * goxml.BaseInt64 * goxml.BaseInt64
	for index, product := range products {
		if product == 0 {
			continue
		}

		//防止数组越界
		var tmpLeftCount int64
		if index < len(leftResCount) {
			tmpLeftCount = leftResCount[index]
		}

		tmpCount := duration*product + tmpLeftCount //被除数 = 本次挂机产出 + 之前剩余未结算数据
		_, count := util.DivFloor(baseParam, tmpCount)
		retLeftCount[index] = tmpCount % baseParam

		if count > 0 {
			awards = append(awards, &cl.Resource{
				Type:  info.OnhookAwards[index].Type,
				Value: info.OnhookAwards[index].Value,
				Count: uint32(count),
			})
		}
	}
	return awards, retLeftCount
}

// 修正挂机效率
// 为了后续统一计算逻辑，将原有数值 * 1w
func fixEfficiency(datas []int64) []int64 {
	ret := make([]int64, len(datas))
	for i := 0; i < len(datas); i++ {
		ret[i] = datas[i] * goxml.BaseInt64
	}
	return ret
}

// 计算挂机奖励
// @param srv servicer
// @param duration int64 挂机时长（包含次数因素）
// @param info *goxml.TrialInfoExt
// @return []*cl.Resource 挂机奖励
func (t *Trials) calcSpeedOnhookAwards(srv servicer, duration int64, info *goxml.TrialInfoExt) []*cl.Resource {
	products := goxml.GetData().VipPrivilegeInfoM.TrailOnhookEfficiency(t.owner.Vip(), info.OnhookEfficiency)
	awards := make([]*cl.Resource, 0, 1)
	baseParam := util.HourSecs * goxml.BaseInt64 * goxml.BaseInt64
	for index, product := range products {
		if product == 0 {
			continue
		}

		tmpCount := duration * product
		_, count := util.DivFloor(baseParam, tmpCount)
		surplus := tmpCount % baseParam
		randomCount := srv.Rand().Int63n(baseParam)
		if randomCount < surplus {
			count++
		}

		if count > 0 {
			awards = append(awards, &cl.Resource{
				Type:  info.OnhookAwards[index].Type,
				Value: info.OnhookAwards[index].Value,
				Count: uint32(count),
			})
		}
	}
	return awards
}
