package character

import (
	"app/goxml"
	"app/logic/helper"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"strconv"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"

	"app/protos/in/db"
)

type MonthlyCard struct {
	owner *User
	data  *cl.MonthlyCard
}

func newMonthlyCard(user *User) *MonthlyCard {
	return &MonthlyCard{
		owner: user,
	}
}

func newMonthlyCardData(sysID uint32, expireTime int64) *cl.MonthlyCardData {
	return &cl.MonthlyCardData{
		SysId:         sysID,
		ExpireTime:    expireTime,
		AutoSendAward: true,
	}
}

func initMonthlyCard() *cl.MonthlyCard {
	return &cl.MonthlyCard{
		CardDatas: make([]*cl.MonthlyCardData, 0, goxml.GetData().MonthlycardInfoM.Length()),
	}
}

func (m *MonthlyCard) addMonthlyCard(sysID uint32, expireTime int64) {
	if m.data == nil {
		m.data = initMonthlyCard()
	}
	m.data.CardDatas = append(m.data.CardDatas, newMonthlyCardData(sysID, expireTime))
}

func (m *MonthlyCard) Owner() *User {
	return m.owner
}

// 加载数据
func (m *MonthlyCard) load(data *cl.MonthlyCard) {
	m.data = data
}

// 设置保存标签, 保存数据
func (m *MonthlyCard) save() {
	if m.Owner().dbUser.Module2.MonthlyCard == nil {
		m.Owner().dbUser.Module2.MonthlyCard = m.data
	}
	m.Owner().setSaveTag(saveTagModule2)
}

func (m *MonthlyCard) Flush() *cl.MonthlyCard {
	return m.data.Clone()
}

func (m *MonthlyCard) GetMonthlyCard(sysID uint32) *cl.MonthlyCardData {
	if m.data == nil {
		return nil
	}

	for _, card := range m.data.CardDatas {
		if card.SysId == sysID {
			return card
		}
	}

	return nil
}

// AddRefreshCount ：月卡存在之后的刷新次数,每日重置
func (m *MonthlyCard) AddRefreshCount() {
	if m.data == nil || len(m.data.CardDatas) == 0 {
		return
	}

	isExist := false
	for _, cardData := range m.data.CardDatas {
		cardInfo := goxml.GetData().MonthlycardInfoM.Index(cardData.SysId)
		if cardInfo == nil {
			continue
		}
		if cardInfo.DispatchPurple > 0 || cardInfo.DispatchOrange > 0 {
			isExist = true
		}
	}

	if isExist {
		m.data.DispatchRefreshCount++
		m.save()
	}
}

func (m *MonthlyCard) GetRefreshCount() uint32 {
	if m.data == nil {
		return 0
	}

	return m.data.DispatchRefreshCount
}

func (m *MonthlyCard) SetAutoSendAward(sysID uint32) {
	if m.data == nil {
		return
	}

	for _, card := range m.data.CardDatas {
		if card.SysId == sysID {
			card.AutoSendAward = false
		}
	}

	m.save()
}

func (m *MonthlyCard) GetExpireTime(term uint32) int64 {
	now := time.Now().Unix()
	// 获取充值当天的零点
	return int64(term*util.DaySecs) + int64(helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now))
}

func (m *MonthlyCard) process(srv servicer, order *db.Order) bool {
	// 获取月卡配置
	monthlyCardInfo := goxml.GetData().MonthlycardInfoM.Index(order.Custom.Id)
	if monthlyCardInfo == nil {
		l4g.Errorf("user: %d account tag:%d  MonthlyCard.process: monthly card not exist. sysID: %d", m.owner.ID(), m.owner.GetAccountTag(), order.Custom.Id)
		return false
	}

	// 如果订单为非折扣券订单但背包中存在可用折扣券
	if discountItem := goxml.GetData().MonthlycardInfoM.GetDiscountItemByRawInternalId(order.Custom.InternalId); discountItem > 0 {
		if discountItemNum := m.owner.GetResourceCount(uint32(common.RESOURCE_ITEM), discountItem, srv); discountItemNum > 0 {
			l4g.Errorf("user: %d account tag:%d  MonthlyCard.process: has discountItem but not use. internalID: %d discountItemNum %d", m.owner.ID(), m.owner.GetAccountTag(), order.Custom.InternalId, discountItemNum)
			return false
		}
	}

	var internalId uint32
	var costs []*cl.Resource
	// 是否使用折扣券
	if discountItem := goxml.GetData().MonthlycardInfoM.GetDiscountItemByProcessedInternalId(order.Custom.InternalId); discountItem > 0 {
		if m.owner.GetResourceCount(uint32(common.RESOURCE_ITEM), discountItem, srv) == 0 {
			l4g.Errorf("user: %d account tag:%d  MonthlyCard.process: no enough discountItem. internalID: %d discountItem %d", m.owner.ID(), m.owner.GetAccountTag(), order.Custom.InternalId, discountItem)
			return false
		}
		internalId = monthlyCardInfo.InternalIdCoupon
		costs = append(costs, goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), discountItem, 1))
	} else {
		internalId = monthlyCardInfo.InternalId
	}

	// 检查internalId是否匹配
	if internalId != order.Custom.InternalId {
		l4g.Errorf("user: %d account tag:%d  MonthlyCard.process: internalID not match. internalID: %d", m.owner.ID(), m.owner.GetAccountTag(), order.Custom.InternalId)
		return false
	}

	var retCode uint32
	var retAwards []*cl.Resource
	if len(costs) == 0 {
		retCode, retAwards = m.owner.Award(srv, monthlyCardInfo.GoodsClRes, AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_MONTHLY_CARD_RECHARGE), goxml.AddResourcesForRecharge)
	} else {
		retCode, retAwards = m.owner.Trade(srv, costs, monthlyCardInfo.GoodsClRes,
			uint32(log.RESOURCE_CHANGE_REASON_MONTHLY_CARD_RECHARGE), goxml.AddResourcesForRecharge)
	}
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d account tag:%d  MonthlyCard.process: send award failed. retCode:%d costs %+v", m.owner.ID(), m.owner.GetAccountTag(), retCode, costs)
		return false
	}

	m.recharge(order.Custom.Id, monthlyCardInfo.Term)
	m.owner.LogMonthlyCardRecharge(srv, order.Custom.Id, retAwards)
	m.owner.SendCmdToGateway(cl.ID_MSG_L2C_MonthlyCardRechargeNotify, &cl.L2C_MonthlyCardRechargeNotify{
		Ret:         uint32(cret.RET_OK),
		Awards:      retAwards,
		MonthlyCard: m.GetMonthlyCard(order.Custom.Id).Clone(),
	})

	return true
}

func (m *MonthlyCard) recharge(sysID uint32, addDay uint32) bool {
	monthlyCard := m.GetMonthlyCard(sysID)
	if monthlyCard == nil {
		m.addMonthlyCard(sysID, m.GetExpireTime(addDay))
	} else {
		now := time.Now().Unix()
		if monthlyCard.ExpireTime > now {
			monthlyCard.ExpireTime += int64(addDay * util.DaySecs)
		} else {
			monthlyCard.ExpireTime = m.GetExpireTime(addDay)
			monthlyCard.AutoSendAward = true
		}
	}
	m.save()

	return true
}

func (m *MonthlyCard) ResetDaily(srv servicer) {
	if m.data == nil {
		return
	}

	isChange := false
	if m.data.DispatchRefreshCount != 0 {
		// 重置刷新次数
		m.data.DispatchRefreshCount = 0
		isChange = true
	}

	for i := 0; i < len(m.data.CardDatas); i++ {
		cardData := m.data.CardDatas[i]
		awardMailID, SoonExpireMailID, ExpiredMailID := m.getMailID(cardData.SysId)
		m.isSendAwardMail(srv, cardData, awardMailID)

		now := time.Now().Unix()
		if cardData.ExpireTime > now {
			if !cardData.AutoSendAward {
				cardData.AutoSendAward = true
				isChange = true
			}

			m.isSendSoonExpireMail(srv, now, cardData.ExpireTime, SoonExpireMailID)
		} else {
			// 月卡过期邮件
			MonthlyCardMail(srv, ExpiredMailID, nil, nil, []uint64{m.owner.ID()})
			m.data.CardDatas = append(m.data.CardDatas[:i], m.data.CardDatas[i+1:]...)
			i--
			isChange = true
		}
	}

	if isChange {
		m.save()
	}
}

func (m *MonthlyCard) getMailID(sysID uint32) (uint32, uint32, uint32) {
	switch sysID {
	case goxml.MonthlyCardTypeGrowUp:
		return MailIDMonthlyCardGrowUpAward, MailIDMonthlyCardGrowUpSoonExpire, MailIDMonthlyCardGrowUpExpired
	case goxml.MonthlyCardTypeSupply:
		return MailIDMonthlyCardSupplyAward, MailIDMonthlyCardSupplySoonExpire, MailIDMonthlyCardSupplyExpired
	}

	return 0, 0, 0
}

// 是否发送奖励邮件
func (m *MonthlyCard) isSendAwardMail(srv servicer, cardData *cl.MonthlyCardData, mailID uint32) {
	if cardData.AutoSendAward {
		info := goxml.GetData().MonthlycardInfoM.Index(cardData.SysId)
		if info == nil {
			l4g.Errorf("user: %d ResetDaily: system data is nil. id: %d", m.owner.ID(), cardData.SysId)
			return
		}
		MonthlyCardMail(srv, mailID, nil, info.DaliyRewardClRes, []uint64{m.owner.ID()})
	}
}

// 是否发送即将过期提醒的邮件
func (m *MonthlyCard) isSendSoonExpireMail(srv servicer, now, expireTime int64, mailID uint32) {
	intervalDay := helper.DaysBetweenTimes(now, expireTime)
	if intervalDay <= goxml.MonthlyCardMailIntervalDay {
		day := strconv.Itoa(int(intervalDay))
		MonthlyCardMail(srv, mailID, []string{day}, nil, []uint64{m.owner.ID()})
	}
}

// GetPrivilegeValue : 获取月卡赋予的特权值
func (m *MonthlyCard) GetPrivilegeValue(kind uint32) (count uint32) {
	if m.data == nil {
		return
	}

	for _, cardData := range m.data.CardDatas {
		if cardData.ExpireTime > time.Now().Unix() {
			count += goxml.GetData().MonthlycardInfoM.GetPrivilegeValue(cardData.SysId, kind)
		}
	}

	return
}

func (m *MonthlyCard) RefreshMaxQuality(count uint32) bool {
	if m.data == nil {
		return false
	}

	for _, cardData := range m.data.CardDatas {
		if cardData.ExpireTime > time.Now().Unix() {
			if goxml.GetData().MonthlycardInfoM.RefreshDispatch(cardData.SysId, count) {
				return true
			}
		}
	}
	return false
}
