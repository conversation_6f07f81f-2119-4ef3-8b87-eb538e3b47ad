package character

import (
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

type Args struct {
	UID       uint64
	CtxID     uint64
	Caller    *User
	CallCmd   uint32 //调用者协议id
	TargetSid uint64
}

func (u *User) Args(cmd uint32, params ...uint64) *Args {
	args := &Args{
		UID:     u.ID(),
		CtxID:   u.ContextID(),
		Caller:  u, //TODO 这里要考虑怎么删除掉这个数据.异步拉取的时候如果直接直接的本地,这里不会被重复赋值，所以还需要这个
		CallCmd: cmd,
	}
	if len(params) > 0 {
		args.TargetSid = params[0]
	}
	return args
}

func (um *UserManager) GetUserSnapshots(ids []uint64, formationID uint32) ([]*cl.UserSnapshot, []uint64) {
	return um.getUserSnapshots(ids, formationID)
}

func (um *UserManager) getUserSnapshots(ids []uint64, formationID uint32) ([]*cl.UserSnapshot, []uint64) {
	data := make([]*cl.UserSnapshot, 0, len(ids))
	rest := make([]uint64, 0, len(ids))
	for _, id := range ids {
		u := um.GetUser(id)
		if u == nil {
			u = um.Cache().GetUser(id)
			if u == nil {
				rest = append(rest, id)
				continue
			}
		}
		data = append(data, u.NewUserSnapshot(formationID))
	}
	return data, rest
}

func (um *UserManager) GetUserBattleData(srv servicer, formationID uint32, ids []uint64, onlyPower bool) ([]*db.UserBattleData, []uint64) {
	return um.getUserBattleData(srv, ids, formationID, onlyPower)
}

func (um *UserManager) getUserBattleData(srv servicer, ids []uint64, formationID uint32, onlyPower bool) ([]*db.UserBattleData, []uint64) {
	data := make([]*db.UserBattleData, 0, len(ids))
	rest := make([]uint64, 0, len(ids))
	for _, id := range ids {
		var battleData *db.UserBattleData
		u := um.GetUserWithCache(id)
		if u != nil {
			if onlyPower {
				battleData = NewUBDOnlyPowerFromUser(u, formationID)
			} else {
				battleData = um.GetBattleDataCache().GetBattleUser(id, formationID)
				if battleData != nil {
					l4g.Errorf("battleData not clean when user %d online", id)
					um.GetBattleDataCache().DelBattleUser(id, formationID)
				}
				battleData = GetBattleDataFromUser(u, formationID)
			}
			data = append(data, battleData)
			continue
		}

		battleData = um.GetBattleDataCache().GetBattleUser(id, formationID)
		if battleData != nil {
			if onlyPower {
				battleData = NewUBDOnlyPowerFromUBD(srv, battleData, formationID)
			}
			data = append(data, battleData)
			continue
		}

		rest = append(rest, id)
	}
	return data, rest
}

func (um *UserManager) GetOneUserBattleData(uid uint64, formationID uint32) *db.UserBattleData {
	u := um.GetUserWithCache(uid)
	if u == nil {
		return nil
	}
	return GetBattleDataFromUser(u, formationID)
}

func (um *UserManager) DeleteUserBattleSnapshots(srv servicer, uid uint64, ids []uint64) {
	if len(ids) > 0 {
		msg := &r2l.L2R_DeleteUserBattleSnapshot{
			Ids: ids,
		}
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_DeleteUserBattleSnapshot), uid, msg)
	}
}
