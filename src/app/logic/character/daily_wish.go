package character

import (
	"app/protos/out/cl"
	"app/protos/out/common"
)

type DailyWish cl.DailyWish

func newDailyWish(actId uint32, newServer bool) *cl.DailyWish {
	return &cl.DailyWish{
		Id:    actId,
		IsNew: newServer,
	}
}

func (d *DailyWish) addSummonCount(count uint32) {
	d.DailyCount += count
	d.SummonCount += count
}

func (d *DailyWish) AfterSummonAward(awardId uint32) {
	if len(d.TakeAward) == 0 {
		d.TakeAward = make([]uint32, 0, int(common.DAILY_WISH_WEIGHT_END_ID))
	}
	d.TakeAward = append(d.Take<PERSON>ward, awardId)
	if len(d.<PERSON>) == int(common.DAILY_WISH_WEIGHT_END_ID) {
		d.resetRound()
	}
	d.SummonCount--
}

/*
func (d *DailyWish) resetDaily() {
	d.DailyCount = 0
}
*/

func (d *DailyWish) resetRound() {
	d.Take<PERSON><PERSON> = make([]uint32, 0, int(common.DAILY_WISH_WEIGHT_END_ID))
}

func (d *DailyWish) GetSummonCount() uint32 {
	return d.SummonCount
}

func (d *DailyWish) Clone() *cl.DailyWish {
	return (*cl.DailyWish)(d).Clone()
}
