package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/out/bt"
	l4g "github.com/ivanabc/log4go"
)

type PowerUpdateTyp uint32

const (
	PowerUpdateByNormalRaise PowerUpdateTyp = 1 // 基础养成
	PowerUpdateBySeasonRaise PowerUpdateTyp = 2 //赛季养成
)

func (u *User) UpdateAllPower(srv servicer, typ PowerUpdateTyp) {
	if typ == PowerUpdateByNormalRaise {
		//先更战斗列表战力
		u.HeroManager().UpdateBattleListPower(srv)
		//更新赛季战力
		u.UpdateSeasonPower(srv)
		//更新防守阵容战力
		u.updateFormationPower(srv)
		//更新top5
		u.HeroManager().UpdateTop5(srv)
	} else if typ == PowerUpdateBySeasonRaise {
		//更新赛季战力
		u.UpdateSeasonPower(srv)
		//更新防守阵容战力
		u.updateFormationPower(srv)
	}
}

// 目前只更新防守阵容的
func (u *User) updateFormationPower(srv servicer) {
	fm := u.FormationManager()
	for _, formationID := range goxml.GetData().FormationInfoM.GetRealTimeUpdateFids() {
		formation := fm.Get(formationID)
		if formation != nil {
			fm.updateDefenseFormationPower(srv, formation)
		}
	}
}

func (u *User) RemoveFormationHeroes(leaveHids []uint64) {
	fm := u.FormationManager()
	for _, formation := range fm.formations {
		fm.removeHeroesFromFormation(formation, leaveHids)
	}

}

// 注意这个方法返回的hero不能传递出去,需要用的数据需要立即使用完并回收，不能把这个数据传递出去
func (u *User) calHeroAttr(hero *Hero, formationId uint32, params ...uint32) *Hero {
	calType := goxml.GetHeroAttrCalType(formationId)
	if calType == goxml.HeroAttrCalTypeByDefault {
		hero.GetHeroPower(u)
		return hero
	} else {
		//其他情况都这么算
		hero.GetHeroPower(u)                 //先计算属性
		cloneHero := hero.CloneBattleHero(u) //复制属性
		if calType == goxml.HeroAttrCalTypeByWorldBoss {
			if !u.worldBossFixHero(cloneHero, getParamsByPos(0, params...)) { //世界boss修正
				return nil
			}
		}
		heroData := cloneHero.GetData()
		heroBalanceInfo := goxml.GetData().HeroBalanceInfoM.Index(heroData.SysId, heroData.Star)
		if heroBalanceInfo == nil {
			l4g.Errorf("GetBattleMemberCommon, no info, hero sysId:%d hero star:%d", heroData.SysId, heroData.Star)
			return nil
		}
		monsterInfo := goxml.GetData().MonsterInfoM.Index(heroBalanceInfo.MonsterId)
		if monsterInfo == nil {
			l4g.Errorf("user: %d monsterInfo nil. monsterID: %d", u.ID(), heroBalanceInfo.MonsterId)
			return nil
		}
		copy(cloneHero.totalAttr, monsterInfo.GetAttr(goxml.GetData()))
		cloneHero.data.Star = heroBalanceInfo.HeroStar //星级也修正
		cloneHero.power = monsterInfo.GetPower(goxml.GetData())
		cloneHero.data.Level = monsterInfo.Level
		cloneHero.data.Stage = monsterInfo.Stage
		if calType == goxml.HeroAttrCalTypeBySeason {
			if u.GetSeasonID() > 0 { // 赛季解锁，增加赛季修正
				//增加赛季等级属性
				info := goxml.GetData().SeasonLevelInfoM.GetLevelInfo(u.GetSeasonID(), u.GetSeasonLevel())
				if info != nil {
					for _, attr := range info.Attrs {
						cloneHero.totalAttr[int(attr.Type)] += int64(attr.Value)
					}
				}
				for _, jid := range cloneHero.data.SeasonJewelry {
					jewelry := u.seasonJewelry.Get(jid)
					if jewelry == nil {
						continue
					}
					jewelryInfo := goxml.GetData().SeasonJewelryInfoM.GetRecordById(jewelry.GetSysId())
					if jewelryInfo == nil {
						continue
					}
					talentTreeAdd := u.TalentTree().GetSeasonJewelryAdd(jewelryInfo.Pos)
					for _, attr := range jewelryInfo.Attr {
						cloneHero.totalAttr[int(attr.Type)] += int64(attr.Value) * (goxml.BaseInt64 + talentTreeAdd) / goxml.BaseInt64
					}
				}
				cloneHero.power = uint64(cloneHero.GetHeroSeasonPower(u))
				cloneHero.power += uint64(u.getSeasonLevelPower())
				cloneHero.data.Level = u.GetSeasonLevel()

				//cloneHero.power += uint64(riteAddPower)
				cloneHero.power += uint64(u.SeasonLink().GetBattleSeasonLinkPower())
				cloneHero.power += uint64(u.RemainM().RemainBook().GetPower())
				cloneHero.power += uint64(u.TalentTree().getLevelPower())
				cloneHero.power += uint64(u.SeasonJewelryManager().CalcPower(cloneHero))
				cloneHero.power += uint64(u.GetSeasonAddPower())
			}
		}
		cloneHero.setModifyLevelAndStage(cloneHero.data.Level, cloneHero.data.Stage) //为了保证getbattlelevelandstage一致
		return cloneHero
	}
}

func (u *User) calArtifactAttr(artifact *Artifact, formationId uint32, params ...uint32) *Artifact {
	calType := goxml.GetHeroAttrCalType(formationId)
	if calType == goxml.HeroAttrCalTypeByWorldBoss {
		newAr := &Artifact{
			data: artifact.data.Clone(),
		}
		if !u.worldBossFixArtifact(newAr, getParamsByPos(0, params...)) {
			return nil
		}
		return newAr
	} else {
		return artifact
	}
}

func getParamsByPos(index int, params ...uint32) uint32 {
	param := uint32(0)
	if len(params) > index {
		param = params[index]
	}
	return param
}

func (u *User) worldBossFixHero(cloneHero *Hero, diffcult uint32) bool {
	worldBossDifficultyInfo := goxml.GetData().WorldbossDifficultyInfoM.Index(diffcult)
	if worldBossDifficultyInfo == nil {
		l4g.Errorf("worldBossDifficultyInfoM cant find difficult:%d", diffcult)
		return false
	}
	heroData := cloneHero.GetData()
	//修正最高星级
	if heroData.Star > worldBossDifficultyInfo.HeroStar {
		heroData.Star = worldBossDifficultyInfo.HeroStar
	}
	//修正宝石等级
	for _, gem := range heroData.Gems {
		if gem.Level > worldBossDifficultyInfo.GemLevel {
			gem.Level = worldBossDifficultyInfo.GemLevel
		}
	}
	//修正符文
	emblemM := u.EmblemManager()
	if len(heroData.Emblem) > 0 {
		if cloneHero.modify == nil {
			cloneHero.modify = &HeroModify{}
		}
		cloneHero.modify.emblems = make([]*Emblem, goxml.EmblemPosMax)
		for slot, v := range heroData.Emblem { //nolint:varnamelen
			if slot < 1 {
				l4g.Errorf("GetBattleMembersWorldBoss error. emblem slot error. uid:%d, emblem:%d, slot:%d", u.ID(), v, slot)
				continue
			}
			emblem := emblemM.Get(v)
			if emblem == nil {
				l4g.Errorf("GetBattleMembersWorldBoss error. emblem not exist. uid:%d, emblem:%d", u.ID(), v)
				continue
			}
			emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
			if emblemInfo == nil {
				l4g.Errorf("GetBattleMembersWorldBoss error. no find sys id. uid:%d, emblem:%d sysID:%d", u.ID(), v, emblem.Data.SysId)
				continue
			}

			limitEmblem := makeRareLimitEmblem(worldBossDifficultyInfo.EmblemRare, emblemInfo, emblem)
			if limitEmblem == nil {
				l4g.Errorf("GetBattleMembersWorldBoss error. no limitEmblem. uid:%d, emblem:%d sysID:%d", u.ID(), v, emblem.Data.SysId)
				continue
			}
			cloneHero.modify.emblems[slot-1] = limitEmblem
		}
	}
	cloneHero.attrTag = false //需要重新计算修正后的属性
	cloneHero.GetHeroPower(u)
	return true
}

func (u *User) NewBattleMemberFromHero(hero *Hero, formationId, pos uint32,
	omniLinks map[uint32]uint32, params ...uint32) *battle.Member {
	newHero := u.calHeroAttr(hero, formationId, params...)
	if newHero == nil {
		return nil
	}
	dress := uint32(0)
	skin := u.SkinManager().GetSkinByHeroIDByBattle(newHero.data.SysId)
	if skin != nil {
		dress = skin.GetID()
	}
	isSeasonAddHero := u.IsSeasonAddHero(formationId, hero.data.SysId)
	emblems := newHero.GetBattleEmblemSysIDs(u)
	level, stage := newHero.GetBattleLevelAndStage(u)
	return newHero.newMember(pos, level, stage, dress, emblems, isSeasonAddHero, omniLinks)
}

func (u *User) NewBattleArtifactInfoFromArtifact(artifact *Artifact, formationId, pos uint32, params ...uint32) *bt.ArtifactInfo {
	artifact = u.calArtifactAttr(artifact, formationId, params...)
	if artifact == nil {
		return nil
	}
	ar := &bt.ArtifactInfo{
		Pos:        pos,
		SysId:      artifact.GetSysID(),
		Star:       artifact.GetStar(),
		StrengthLv: artifact.GetStrengthLv(),
		ForgeLv:    artifact.GetForgeLv(),
	}
	return ar
}

func (u *User) worldBossFixArtifact(ar *Artifact, diffcult uint32) bool {
	worldBossDifficultyInfo := goxml.GetData().WorldbossDifficultyInfoM.Index(diffcult)
	if worldBossDifficultyInfo == nil {
		l4g.Errorf("worldBossDifficultyInfoM cant find difficult:%d", diffcult)
		return false
	}

	if ar.data.Star > worldBossDifficultyInfo.ArtifactStar {
		ar.data.Star = worldBossDifficultyInfo.ArtifactStar
	}

	return true
}

func (u *User) GetBattleTeamBase(formationId, teamIndex uint32, power int64) *battle.TeamBase {
	calType := goxml.GetHeroAttrCalType(formationId)
	teamBase := &battle.TeamBase{
		IsCalculateLink: true,
		Power:           power,
		Name:            u.Name(),
		BaseId:          u.BaseID(),
		TeamIndex:       teamIndex,
		TitleId:         u.GetTitle(),
	}
	//赛季玩法需要显示赛季等级
	if calType == goxml.HeroAttrCalTypeBySeason {
		teamBase.SeasonLv = u.GetSeasonLevel()
	}
	return teamBase
}
