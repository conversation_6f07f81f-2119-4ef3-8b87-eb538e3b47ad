package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/out/cl"
	"app/protos/out/ret"
	l4g "github.com/ivanabc/log4go"
	"slices"
)

type Carnival struct {
	owner *User
	data  []*cl.Carnival
}

func newCarnival(user *User) *Carnival {
	return &Carnival{
		owner: user,
	}
}

func (c *Carnival) Owner() *User {
	return c.owner
}

// 加载数据
func (c *Carnival) load(data []*cl.Carnival) {
	c.data = data
	for _, carnivalData := range c.data {
		if carnivalData.TaskTypeProgress == nil {
			carnivalData.TaskTypeProgress = make(map[uint32]*cl.TaskTypeProgress)
		}
		if carnivalData.Awarded == nil {
			carnivalData.Awarded = make(map[uint32]bool)
		}
	}
}

// 保存数据
func (c *Carnival) save() {
	c.Owner().dbUser.Module1.Carnival = c.data
	c.Owner().setSaveTag(saveTagModule1)
}

// clone内存数据
func (c *Carnival) Flush() []*cl.Carnival {
	var allData []*cl.Carnival
	for _, carnivalData := range c.data {
		allData = append(allData, carnivalData.Clone())
	}
	return allData
}

func (c *Carnival) GetCarnivalById(id uint32) *cl.Carnival {
	for _, carnivalData := range c.data {
		if carnivalData.Id == id {
			return carnivalData
		}
	}
	data := newSingleCarnival(id)
	c.data = append(c.data, data)
	return data
}

func (c *Carnival) GetTotalProgress(carnivalID uint32) map[uint32]*cl.TaskTypeProgress {
	for _, carnival := range c.data {
		if carnival.Id == carnivalID {
			return carnival.TaskTypeProgress
		}
	}
	return nil
}

func (c *Carnival) GetProgressByType(taskTypeInfo *goxml.TaskTypeInfo, carnivalID uint32) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return c.owner.CalcTaskProgress(taskTypeInfo)
	}
	for _, carnival := range c.data {
		if carnival.Id == carnivalID {
			return carnival.TaskTypeProgress[taskTypeInfo.Id]
		}
	}
	return nil
}

func (c *Carnival) ReceiveAward(carnivalID uint32, ids []uint32) {
	data := c.GetCarnivalById(carnivalID)
	for _, id := range ids {
		data.Awarded[id] = true
	}
	c.save()
}

func (c *Carnival) IsAwarded(carnivalId, id uint32) bool {
	for _, carnivalData := range c.data {
		if carnivalData.Id == carnivalId {
			if _, exist := carnivalData.Awarded[id]; exist {
				return true
			}
		}
	}
	return false
}

func (c *Carnival) updateTask(id uint32, progress map[uint32]*cl.TaskTypeProgress) {
	smsg := &cl.L2C_CarnivalUpdate{
		Ret:              uint32(ret.RET_OK),
		CarnivalId:       id,
		CarnivalProgress: progress,
	}
	c.Owner().SendCmdToGateway(cl.ID_MSG_L2C_CarnivalUpdate, smsg)
}

func (c *Carnival) AddPoints(srv Servicer, carnivalId, points uint32, isSeason bool) {
	if points == 0 {
		return
	}

	var eventType uint32
	if !isSeason {
		eventType = event.AeCarnivalPoints
	} else {
		eventType = event.AeSeasonCarnivalPoints
	}
	c.Owner().FireCommonEvent(srv.EventM(), eventType, uint64(points), carnivalId)
}

// 重置日常任务，放到重置日常数据一起
func (c *Carnival) Reset() {
	c.load(nil)
}

func (c *Carnival) ClearCarnivalById(carnivalId uint32) {
	c.data = slices.DeleteFunc(c.data, func(carnival *cl.Carnival) bool {
		return carnival.Id == carnivalId
	})
	c.save()
}

func (c *Carnival) OnCarnivalEvent(eventId uint32, progress uint64, values []uint32) {
	updateCarnivals := goxml.GetData().CarnivalTaskInfoM.GetUpdateCarnivalsByEvent(eventId, c.Owner().CreateTime(), c.Owner().GetSeasonEnterTime())
	l4g.Debugf("yta test OnCarnivalEvent3: eventId:%d, progress:%d, values:%v, updateCarnivals:%v", eventId, progress, values, updateCarnivals)
	for _, carnivalId := range updateCarnivals {
		data := c.GetCarnivalById(carnivalId)
		p, change := c.Owner().TaskTypeOnEvent(data.TaskTypeProgress, eventId, progress, values)
		if change {
			l4g.Debugf("yta test OnCarnivalEvent4: eventId:%d, progress:%d, values:%v, updateCarnivals:%v, change:%v", eventId, progress, values, updateCarnivals, change)
			c.updateTask(carnivalId, p)
			c.save()
		}
	}
}

func (c *Carnival) GetData() []*cl.Carnival {
	return c.data
}

// 嘉年华积分任务id
const (
	CarnivalSevenPointsTaskTypeID    = 5100001
	CarnivalFourteenPointsTaskTypeID = 5100002
	CarnivalSeasonPointsTaskTypeID   = 5100003
)

// 获取嘉年华积分
func (c *Carnival) GetCarnivalPointsById(carnivalID uint32) uint64 {
	carnivalInfo := goxml.GetData().CarnivalInfoM.Index(carnivalID)
	if carnivalInfo == nil {
		return 0
	}

	var taskTypeID uint32
	if carnivalInfo.SeasonId > 0 { // 赛季嘉年华
		taskTypeID = CarnivalSeasonPointsTaskTypeID
	} else {
		if carnivalID == CarnivalSeven { // 7日嘉年华
			taskTypeID = CarnivalSevenPointsTaskTypeID
		} else if carnivalID == CarnivalFourteen { // 14日嘉年华
			taskTypeID = CarnivalFourteenPointsTaskTypeID
		}
	}
	if taskTypeID == 0 {
		return 0
	}

	var points uint64
	for _, carnival := range c.data {
		if carnival.Id != carnivalID {
			continue
		}
		data, exist := carnival.TaskTypeProgress[taskTypeID]
		if !exist || data == nil {
			continue
		}
		points += data.Progress
	}

	return points
}

// 判断嘉年华是否开启（包含活动开放期和活动领奖期）
func (c *Carnival) IsCarnivalOpen(carnivalId uint32) bool {
	intervalDay := goxml.GetData().CarnivalTaskInfoM.GetIntervalDayById(carnivalId, c.Owner().CreateTime(), c.Owner().GetSeasonEnterTime())
	if intervalDay == 0 {
		l4g.Errorf("IsCarnivalOpen: intervalDay is 0. carnivalId %d", carnivalId)
		return false
	}
	return goxml.GetData().CarnivalInfoM.IsCarnivalOpen(carnivalId, intervalDay)
}

// 判断嘉年华是否活跃（仅处于活动开放期）
func (c *Carnival) IsCarnivalActive(carnivalId uint32) bool {
	intervalDay := goxml.GetData().CarnivalTaskInfoM.GetIntervalDayById(carnivalId, c.Owner().CreateTime(), c.Owner().GetSeasonEnterTime())
	if intervalDay == 0 {
		l4g.Errorf("IsCarnivalActive: intervalDay is 0. carnivalId %d", carnivalId)
		return false
	}
	return goxml.GetData().CarnivalInfoM.IsCarnivalActive(carnivalId, intervalDay)
}

// ************* 赛季嘉年华 **************** //

func (c *Carnival) OnSeasonEnd() {
	seasonId := c.owner.GetSeasonID()
	if seasonId == 0 {
		return
	}

	// 获取当前赛季的嘉年华配置
	carnivalInfo := goxml.GetData().CarnivalInfoM.GetRecordBySeasonId(seasonId)
	if carnivalInfo == nil {
		return
	}
	c.ClearCarnivalById(carnivalInfo.Id)
}

// ************* 单个嘉年华 **************** //

func newSingleCarnival(id uint32) *cl.Carnival {
	return &cl.Carnival{
		Id:               id,
		TaskTypeProgress: make(map[uint32]*cl.TaskTypeProgress),
		Awarded:          make(map[uint32]bool),
	}
}

// ************* 以下方法供 gm grpc 使用 **************** //

func (c *Carnival) Save() {
	c.save()
}
