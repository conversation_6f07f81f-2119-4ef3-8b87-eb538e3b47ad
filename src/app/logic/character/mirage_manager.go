package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/logic/helper"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type MirageM struct {
	u             *User
	datas         map[uint32]*Mirage // key: hurdleID 关卡id
	maxWinHurdles map[uint32]uint32  //各个副本已获胜最大关卡id; key: copyID
	changes       map[uint32]*cl.Mirage
}

func newMirageM(user *User) *MirageM {
	return &MirageM{
		u:             user,
		datas:         make(map[uint32]*Mirage),
		maxWinHurdles: make(map[uint32]uint32),
		changes:       make(map[uint32]*cl.Mirage),
	}
}

func (mm *MirageM) Load(dbData map[uint32]*cl.Mirage) {
	for hurdleID, data := range dbData {
		mirage := (*Mirage)(data)
		mm.datas[hurdleID] = mirage
		if data.IsFirstPass {
			mm.initPassMaxWinHurdle(hurdleID)
		}
	}
}

func (mm *MirageM) initPassMaxWinHurdle(hurdleID uint32) {
	hurdleInfo := goxml.GetData().MirageHurdleInfoM.Index(hurdleID)
	if hurdleInfo == nil {
		l4g.Errorf("user: %d mirage config not exist, copyID:%d", mm.u.ID(), hurdleID)
		return
	}

	if hurdleID > mm.maxWinHurdles[hurdleInfo.Id] {
		mm.maxWinHurdles[hurdleInfo.Id] = hurdleID
	}
}

func (mm *MirageM) GetMaxHurdleID(copyID uint32) uint32 {
	return mm.maxWinHurdles[copyID]
}

func (mm *MirageM) UpdateMaxHurdle(copyID, hurdleID uint32) {
	if hurdleID > mm.maxWinHurdles[copyID] {
		mm.maxWinHurdles[copyID] = hurdleID
		mm.u.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_MIRAGE), copyID, hurdleID)
	}
}

func (mm *MirageM) GetMirage(hurdleID uint32) *Mirage {
	return mm.datas[hurdleID]
}

func (mm *MirageM) SetChange(hurdleID uint32) {
	mirage := mm.GetMirage(hurdleID)
	if mirage != nil {
		mm.changes[hurdleID] = (*cl.Mirage)(mirage)
	}
}

func (mm *MirageM) Save(msg *r2l.OpMirages) bool {
	success := false
	changes := len(mm.changes)
	if changes > 0 {
		msg.Changes = make([]*cl.Mirage, 0, changes)
		for sysID, k := range mm.changes {
			msg.Changes = append(msg.Changes, k.Clone())
			delete(mm.changes, sysID)
		}
		success = true
	}
	return success
}

func (mm *MirageM) AddMirage(hurdleID uint32, affixes []uint32) *Mirage {
	mirage := newMirage(hurdleID, affixes)
	mm.datas[hurdleID] = mirage
	return mirage
}

// FlushCurrentFloors : 各副本最大已获胜关卡数据
func (mm *MirageM) FlushCurrentFloors() map[uint32]*cl.Mirage {
	data := make(map[uint32]*cl.Mirage, len(mm.maxWinHurdles))
	for copyID, sysID := range mm.maxWinHurdles {
		mirage := mm.GetMirage(sysID)
		if mirage == nil {
			l4g.Errorf("user: %d mirage data not exist, copyID:%d", mm.u.ID(), sysID)
			continue
		}
		data[copyID] = mirage.flush()
	}

	return data
}

// FlushAllByCopyID : 单个副本全部关卡数据(包括未打赢，仅保存词缀的关卡)
func (mm *MirageM) FlushAllByCopyID(copyID uint32) []*cl.Mirage {
	firstID := goxml.GetData().MirageHurdleInfoM.FirstHurdleId(copyID)
	if firstID == 0 {
		l4g.Errorf("user: %d mirage hurdle config not exist, copyID:%d", mm.u.ID(), copyID)
		return nil
	}

	currentID := mm.maxWinHurdles[copyID]
	if currentID == 0 { //首个关卡
		//检查首个关卡是否有数据 - 是否保存过词缀
		if mm.GetMirage(firstID) == nil {
			return nil
		}
		currentID = firstID
	} else {
		if currentID < firstID {
			l4g.Errorf("user: %d currentID less than firstID. currentID:%d firstID:%d", mm.u.ID(), currentID, firstID)
			return nil
		}
		//检查下个关卡是否有数据 - 是否保存过词缀
		nextID := goxml.GetData().MirageHurdleInfoM.GetNextHurdle(copyID, currentID)
		if nextID > 0 && mm.GetMirage(nextID) != nil {
			currentID = nextID
		}
	}

	//在初始量表数据时，已限制了关卡id必须递增的
	passHurdles := goxml.GetData().MirageHurdleInfoM.GetUserCopyCrossHurdle(copyID, currentID)
	data := make([]*cl.Mirage, 0, len(passHurdles))
	for _, hurdleId := range passHurdles {
		mirage := mm.GetMirage(hurdleId)
		if mirage == nil {
			l4g.Errorf("user: %d mirage data not exist, copyID:%d", mm.u.ID(), hurdleId)
			continue
		}
		data = append(data, mirage.flush())
	}

	return data
}

// IsUnclaimedAwardWithRedPoint :检查副本中是否有未领取的奖励
func (mm *MirageM) IsUnclaimedAwardWithRedPoint(copyID uint32, srv servicer) bool {
	//检查当前副本是否开放
	if !IsMirageOpen(mm.u, copyID, srv) {
		return false
	}

	firstID := goxml.GetData().MirageHurdleInfoM.FirstHurdleId(copyID)
	if firstID == 0 {
		l4g.Errorf("user: %d mirage hurdle config not exist, copyID:%d", mm.u.ID(), copyID)
		return false
	}

	currentID := mm.maxWinHurdles[copyID]
	if currentID == 0 {
		return false
	}

	isExist := false
	//在初始量表数据时，已限制了关卡id必须递增的
	passes := goxml.GetData().MirageHurdleInfoM.GetUserCopyCrossHurdle(copyID, currentID)
	if len(passes) == 0 {
		return false
	}
	for _, hurdle := range passes {
		mirage := mm.GetMirage(hurdle)
		if mirage == nil {
			continue
		}

		if goxml.GetData().MirageHurdleInfoM.IsExistAward(hurdle) && !mirage.IsReceivedAward() {
			isExist = true
			break
		}
	}

	return isExist
}

// IsCanFightWithRedPoint :检查副本中是否存在未通关的关卡
func (mm *MirageM) IsCanFightWithRedPoint(copyID uint32, srv servicer) bool {
	//检查当前副本是否开放
	if !IsMirageOpen(mm.u, copyID, srv) {
		return false
	}

	currentID := mm.maxWinHurdles[copyID]
	if currentID == 0 {
		return true
	}

	currentInfo := goxml.GetData().MirageHurdleInfoM.Index(currentID)
	if currentInfo == nil {
		l4g.Errorf("Mirage, current config not exist, uuid:%s, id:%d", mm.u.UUID(), currentID)
		return false
	}

	if goxml.GetData().MirageHurdleInfoM.IsMaxFloor(copyID, currentInfo.Floor) {
		return false
	}

	return true
}

// CheckAffixSettable : 检查当前关卡是否可以设置词缀
func (mm *MirageM) CheckAffixSettable(hurdleID uint32) uint32 {
	if mm.GetMirage(hurdleID) != nil {
		return uint32(ret.RET_OK)
	}

	targetInfo := goxml.GetData().MirageHurdleInfoM.Index(hurdleID)
	if targetInfo == nil {
		l4g.Errorf("user: %d target hurdle not exist, sysID:%d", mm.u.ID(), hurdleID)
		return uint32(ret.RET_SYSTEM_DATA_ERROR)
	}

	maxHurdleID := mm.maxWinHurdles[targetInfo.Id] //玩家当前副本最大关卡id
	if maxHurdleID == 0 {
		firstID := goxml.GetData().MirageHurdleInfoM.FirstHurdleId(targetInfo.Id)
		if hurdleID != firstID {
			l4g.Errorf("user: %d target hurdle id not match, targetID:%d firstID:%d", mm.u.ID(), hurdleID, firstID)
			return uint32(ret.RET_CLIENT_REQUEST_ERROR)
		}

		return uint32(ret.RET_OK)
	}

	currentInfo := goxml.GetData().MirageHurdleInfoM.Index(maxHurdleID)
	if currentInfo == nil {
		l4g.Errorf("user: %d current hurdle not exist, currentID:%d", mm.u.ID(), maxHurdleID)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	if targetInfo.Floor != currentInfo.Floor+1 {
		l4g.Errorf("user: %d floor not match, targetFloor:%d, curFloor:%d", mm.u.ID(), targetInfo.Floor, currentInfo.Floor)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	if mm.GetMirage(maxHurdleID) == nil {
		l4g.Errorf("user: %d mirage not exist, currentID:%d", mm.u.ID(), maxHurdleID)
		return uint32(ret.RET_MIRAGE_NOT_EXIST)
	}

	return uint32(ret.RET_OK)
}

// PassRate : 获取过关超越数据
func (mm *MirageM) PassRate(passNum uint32) uint32 {
	//当过关人数的超越数据不足MirageDefaultMinPassRate时，显示MirageDefaultMinPassRate
	if float64(passNum) > float64(MiragePlayerCount)*(goxml.BaseFloat-MirageDefaultMinPassRate)/goxml.BaseFloat {
		return uint32(MirageDefaultMinPassRate)
	}

	return uint32(float64(MiragePlayerCount-passNum) / float64(MiragePlayerCount) * goxml.BaseFloat)
}

// IsMirageOpen : 检查副本是否开放
// 不放mirage_copy_info_manager中的原因：会导致循环引用
func IsMirageOpen(user *User, copyID uint32, srv servicer) bool {
	if !user.IsFunctionOpen(uint32(common.FUNCID_MODULE_MIRAGE), srv) {
		return false
	}

	copyInfo := goxml.GetData().MirageCopyInfoM.Index(copyID)
	if copyInfo == nil {
		return false
	}

	day := helper.Weekday(time.Now().Unix())
	_, exist := goxml.GetData().MirageCopyInfoM.Group(day)[copyID]

	return exist
}

func (mm *MirageM) ReceiveAward(hurdleIDs []uint32) {
	for _, hurdleID := range hurdleIDs {
		mirage := mm.GetMirage(hurdleID)
		if mirage == nil {
			continue
		}
		mirage.updateReceiveAward()
		mm.changes[hurdleID] = (*cl.Mirage)(mirage)
	}
}

func (mm *MirageM) UpdateAffixes(hurdleID uint32, affixes []uint32) {
	mirage := mm.GetMirage(hurdleID)
	if mirage == nil {
		mm.AddMirage(hurdleID, affixes)
	} else {
		mirage.setAffixes(affixes)
	}
}

// ExtraSkills
// @Description: 根据玩家上阵情况增加额外技能
func (mm *MirageM) ExtraSkills(altRaisePS *battle.AltRaisePS, formationID common.FORMATION_ID, hurdleInfo *goxml.MirageHurdleInfo) {
	effects := goxml.GetData().MirageFightEffectInfoM.Group(hurdleInfo.FightEffect)
	pos2RaisePSs := make(map[uint32][]uint64)

	for _, effect := range effects {
		if effect == nil {
			continue
		}
		switch effect.Type {
		case goxml.MirageFightEffectHeroLink:
			mm.effectHeroLink(effect, uint32(formationID), pos2RaisePSs)
		case goxml.MirageFightEffectArtifactLink:
			mm.effectArtifactLink(effect, uint32(formationID), pos2RaisePSs)
		case goxml.MirageFightEffectArtifactRareStar:
			mm.effectArtifactRareStar(effect, uint32(formationID), pos2RaisePSs)
		}
	}

	altRaisePS.AltAttack(pos2RaisePSs)
}

// effectHeroLink
// @Description: 处理上阵英雄羁绊所加的额外技能。 effect.Link1或者Link2的羁绊上阵的英雄数量>=effect.Num就添加技能
func (mm *MirageM) effectHeroLink(effect *goxml.MirageFightEffectInfoExt, formationId uint32, pos2RaisePSs map[uint32][]uint64) {
	formationLinkInfo := mm.u.FormationManager().GetFormationLinkInfo(formationId, goxml.FormationTeamOneIndex)
	if formationLinkInfo == nil {
		return
	}
	matchNum := uint32(0)
	for _, links := range formationLinkInfo.HeroLinkInfo {
		linkNum := links[effect.Link1]
		matchNum += linkNum
		linkNum = links[effect.Link2]
		matchNum += linkNum
	}
	if matchNum < effect.Num {
		return
	}
	for _, skill := range effect.Skills {
		raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skill, 1)
		if raisePSInfo == nil {
			continue
		}
		pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
	}
}

// effectArtifactLink
// @Description: 处理上阵神器羁绊所加的额外技能。 effect.Link1或者Link2的羁绊上阵的神器数量>=effect.Num就添加技能
func (mm *MirageM) effectArtifactLink(effect *goxml.MirageFightEffectInfoExt, formationId uint32, pos2RaisePSs map[uint32][]uint64) {
	formation := mm.u.GetFormation(formationId)
	if formation == nil {
		return
	}
	matchNum := uint32(0)
	for _, team := range formation.Teams {
		if team == nil {
			continue
		}
		for _, artifact := range team.Artifacts {
			if artifact == nil {
				continue
			}
			artifactData := mm.u.ArtifactManager().GetArtifact(artifact.Aid)
			if artifactData == nil {
				continue
			}
			starInfo := goxml.GetData().ArtifactStarInfoM.Index(artifact.Aid, artifactData.GetStar())
			if starInfo == nil {
				continue
			}
			if effect.Link1 > 0 && (effect.Link1 == starInfo.Link1 || effect.Link1 == starInfo.Link2) {
				matchNum++
				continue
			}
			if effect.Link2 > 0 && (effect.Link2 == starInfo.Link1 || effect.Link2 == starInfo.Link2) {
				matchNum++
			}
		}
	}
	if matchNum < effect.Num {
		return
	}
	for _, skill := range effect.Skills {
		raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skill, 1)
		if raisePSInfo == nil {
			continue
		}
		pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
	}
}

// effectArtifactRareStar
// @Description: 处理上阵指定品质神器的总星数所加的额外技能。 effect.Quality的神器的总星数 / effect.Num的值大于0就加技能
func (mm *MirageM) effectArtifactRareStar(effect *goxml.MirageFightEffectInfoExt, formationId uint32, pos2RaisePSs map[uint32][]uint64) {
	formation := mm.u.GetFormation(formationId)
	if formation == nil {
		return
	}
	totalStar := uint32(0)
	for _, team := range formation.Teams {
		if team == nil {
			continue
		}
		for _, artifact := range team.Artifacts {
			if artifact == nil {
				continue
			}
			rare := goxml.GetData().ArtifactInfoM.GetRare(artifact.Aid)
			if rare >= effect.Quality {
				artifactData := mm.u.ArtifactManager().GetArtifact(artifact.Aid)
				if artifactData == nil {
					continue
				}
				totalStar += artifactData.GetStar()
			}
		}
	}
	if totalStar == 0 {
		return
	}
	layer := totalStar / effect.Num
	if layer == 0 {
		return
	}
	for _, skill := range effect.Skills {
		raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skill, layer)
		if raisePSInfo == nil {
			continue
		}
		pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
	}
}

func (mm *MirageM) GetTotalFloor() uint32 {
	user := mm.u
	rankIds := []uint32{goxml.MirageEmpireRankId, goxml.MirageForestRankId, goxml.MirageMoonRankId,
		goxml.MirageProtossRankId, goxml.MirageDemonRankId, goxml.MirageSixRankId}
	var totalFloor uint32
	for _, rankId := range rankIds {
		copyID := goxml.GetMirageCopyIDByRankID(rankId)
		hurdleID := user.MirageManager().GetMaxHurdleID(copyID)
		if hurdleID == 0 {
			continue
		}
		info := goxml.GetData().MirageHurdleInfoM.Index(hurdleID)
		if info == nil {
			l4g.Errorf("user: %d doRank: mirageHurdleInfo not exist. sysID:%d", user.ID(), hurdleID)
			continue
		}
		totalFloor += info.Floor
	}
	return totalFloor
}
