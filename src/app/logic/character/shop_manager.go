package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type ShopM struct {
	owner *User
	shops map[uint32]*cl.Shop
}

func newShopM(user *User) *ShopM {
	return &ShopM{
		owner: user,
		shops: make(map[uint32]*cl.Shop),
	}
}

func (sm *ShopM) load(dbData map[uint32]*cl.Shop) {
	for id, data := range dbData {
		if data.GoodsList == nil {
			data.GoodsList = make(map[uint32]uint32)
		}
		sm.shops[id] = data
	}
}

func (sm *ShopM) Save(shopID uint32) {
	sm.owner.dbUser.Module.Shops[shopID] = sm.shops[shopID]
	sm.owner.setSaveTag(saveTagModule)
}

func (sm *ShopM) getShopResetI(shopID, shopType uint32) ShopReset {
	shop := sm.shops[shopID]
	if shop == nil {
		return nil
	}

	switch shopType {
	case uint32(common.SHOP_TYPE_ST_RANDOM):
		return (*ShopResetRandom)(shop)
	case uint32(common.SHOP_TYPE_ST_ROUND):
		return (*ShopResetRound)(shop)
	default:
		return nil
	}
}

func (sm *ShopM) getShopInitI(shopID, shopType uint32) ShopInit {
	shop := sm.shops[shopID]
	if shop == nil {
		return nil
	}

	switch shopType {
	case uint32(common.SHOP_TYPE_ST_RANDOM):
		return (*ShopInitRandom)(shop)
	case uint32(common.SHOP_TYPE_ST_ROUND):
		return (*ShopInitRound)(shop)
	default:
		return nil
	}
}

func (sm *ShopM) GetShopBuyI(shopID, shopType uint32) ShopBuy {
	shop := sm.shops[shopID]
	if shop == nil {
		return nil
	}

	switch shopType {
	case uint32(common.SHOP_TYPE_ST_RANDOM), uint32(common.SHOP_TYPE_ST_RANDOM_BOX):
		return (*ShopBuyRandom)(shop)
	case uint32(common.SHOP_TYPE_ST_FIXED), uint32(common.SHOP_TYPE_ST_ROUND), uint32(common.SHOP_TYPE_ST_ARTIFACT_DEBUT):
		return (*ShopBuyRound)(shop)
	default:
		return nil
	}
}

func (sm *ShopM) GetShop(id uint32) *Shop {
	return (*Shop)(sm.shops[id])
}

func (sm *ShopM) FlushShops(shopIds []uint32) (shops map[uint32]*cl.Shop) {
	if len(shopIds) > 0 {
		shops = make(map[uint32]*cl.Shop, len(shopIds))
		for _, shopID := range shopIds {
			if s, exist := sm.shops[shopID]; exist {
				shops[shopID] = s.Clone()
			}
		}
	} else {
		shops = make(map[uint32]*cl.Shop, len(sm.shops))
		for shopID, s := range sm.shops {
			shops[shopID] = s.Clone()
		}
	}

	return
}

func (sm *ShopM) ResetShop(srv servicer) {
	shopInfos := goxml.GetData().ShopInfoM.All()
	//初次加载数据
	if len(sm.shops) == 0 {
		for _, info := range shopInfos {
			sm.initShops(srv, info)
		}
		return
	}

	//玩家商店数据少于配置数据
	//xxx如果shop_info.xml数据有变化，需执行清理脚本，保证数据有效性
	if len(sm.shops) < len(shopInfos) {
		for id, info := range shopInfos {
			if _, exist := sm.shops[id]; exist {
				continue
			}
			sm.initShops(srv, info)
		}
	}
	sm.reset(srv, shopInfos)
}

func (sm *ShopM) initShops(srv servicer, info *goxml.ShopInfoExt) {
	if info == nil {
		return
	}
	if sm.owner.IsFunctionOpen(info.FuncId, srv) && info.SeasonCheck(sm.owner.GetSeasonID()) {
		sm.shops[info.Id] = newShop(info)
		shopInitI := sm.getShopInitI(info.Id, info.Type)
		if shopInitI != nil {
			shopInitI.init(srv, sm.owner, info)
		}
		sm.Save(info.Id)
	}
}

// 检查更新商店信息
func (sm *ShopM) reset(srv servicer, shopInfos map[uint32]*goxml.ShopInfoExt) {
	for id := range sm.shops {
		shopInfo := shopInfos[id]
		if shopInfo == nil {
			continue
		}
		shopResetI := sm.getShopResetI(id, shopInfo.Type)
		if shopResetI != nil && shopResetI.reset(srv, sm.owner, shopInfo) {
			sm.Save(id)
		}
	}
}

// ResetBuyLimit 重置限购商品数据
func (sm *ShopM) ResetBuyLimit(limitType goxml.ShopLimitType) {
	for id, s := range sm.shops {
		info, exist := goxml.GetData().ShopInfoM.All()[id]
		if !exist || info.Type == uint32(common.SHOP_TYPE_ST_RANDOM) || info.Type == uint32(common.SHOP_TYPE_ST_RANDOM_BOX) {
			continue
		}
		if (*Shop)(s).CheckResetBuyLimit(limitType) {
			sm.Save(id)
		}
	}
}

// HandleVipUpPrivilege 处理vip变化后的商店特权; 目前商店特权只有神秘商店的免费刷新次数
func (sm *ShopM) HandleVipUpPrivilege(oldVip, newVip uint32) {
	shop := sm.GetShop(goxml.VipPrivilegeShopId)
	if shop == nil {
		return
	}

	beforeNum := goxml.GetData().VipPrivilegeInfoM.GetShopFreeRefreshAdd(goxml.VipPrivilegeShopId, oldVip)
	afterNum := goxml.GetData().VipPrivilegeInfoM.GetShopFreeRefreshAdd(goxml.VipPrivilegeShopId, newVip)
	if afterNum <= beforeNum {
		return
	}

	shop.addFreeRefreshNum(afterNum - beforeNum)
	sm.Save(goxml.VipPrivilegeShopId)

	shopIDs := []uint32{goxml.VipPrivilegeShopId}
	sm.owner.SendCmdToGateway(cl.ID_MSG_L2C_ShopList, &cl.L2C_ShopList{
		Ret:    uint32(cret.RET_OK),
		SysIds: shopIDs,
		Shops:  sm.FlushShops(shopIDs),
	})
}

// InitRandomBoxShop ：按商店格子初始化商品
// 迷宫的迷途商人事件专用
func (sm *ShopM) InitRandomBoxShop(srv servicer) {
	shopInfo := goxml.GetData().ShopInfoM.Index(goxml.MazeBlackMarketerShopID)
	if shopInfo == nil {
		l4g.Errorf("InitRandomBoxShop: no shopsInfo. uid:%d", sm.owner.ID())
		return
	}
	if !shopInfo.SeasonCheck(sm.owner.GetSeasonID()) {
		return
	}
	if !sm.owner.IsFunctionOpen(shopInfo.FuncId, srv) {
		return
	}

	var shop *Shop
	if s, exist := sm.shops[shopInfo.Id]; exist {
		shop = (*Shop)(s)
	} else {
		newS := newShop(shopInfo)
		sm.shops[shopInfo.Id] = newS
		shop = (*Shop)(newS)
	}

	shop.initRandomBoxShop(srv.Rand(), sm.owner.Level(), shopInfo.Id)
	sm.Save(shopInfo.Id)
}

func (sm *ShopM) ResetDebutShopBuyLimit(shopID uint32) {
	shop := sm.shops[shopID]
	if shop == nil {
		l4g.Errorf("user: %d ResetDebutShopBuyLimit: shopID not exist.", sm.owner.ID())
		return
	}

	s := (*Shop)(shop)
	if len(s.GoodsList) > 0 {
		s.GoodsList = make(map[uint32]uint32)
		sm.Save(shopID)
	}
}

func (sm *ShopM) ResetActivityShopBuyLimit(shopID uint32) {
	shop, exist := sm.shops[shopID]
	if !exist {
		return
	}

	s := (*Shop)(shop)
	if len(s.GoodsList) > 0 {
		s.GoodsList = make(map[uint32]uint32)
		sm.Save(shopID)
	}
}

// 根据开服时间计算当前轮次,重置时间
func calcRoundAndResetTime(srv servicer, interval int64, shopID uint32) (uint32, int64) {
	resetTm := int64(util.DailyZeroByTime(srv.StartServiceTm()))
	num, now := uint32(1), time.Now().Unix()
	if now >= (resetTm + interval) {
		roundNum := (now - resetTm) / interval
		resetTm += interval * roundNum
		num += uint32(roundNum)
	}

	return goxml.GetData().ShopRegularGoodsInfoM.CalcRoundNum(shopID, num), resetTm
}
