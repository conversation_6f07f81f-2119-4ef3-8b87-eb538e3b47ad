package character

import (
	"app/goxml"
	"app/protos/in/log"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

type ActivitySubDropS struct {
	baseController
}

//nolint:varnamelen
func (*ActivitySubDropS) DestroyFunc(sum *cl.ActivitySum, user *User, s Servicer) {
	actInfo := goxml.GetData().ActivityTurntableInfoM.Index(sum.SysId)
	if actInfo == nil {
		return
	}

	dropInfo := goxml.GetData().DropActivityInfoM.Index(actInfo.DropActivity)
	if dropInfo == nil {
		l4g.Errorf("uid:%d activitysum drop DestroyFunc: no info, ActivityId:%d, ",
			user.ID(), sum.SysId)
		return
	}

	if dropInfo.RechargeShopId > 0 {
		user.ActivityRecharge().ResetByShopID(dropInfo.RechargeShopId)
	}
	if dropInfo.RechargeShop2Id > 0 {
		user.ActivityRecharge().ResetByShopID(dropInfo.RechargeShop2Id)
	}

	if dropInfo.MailId > 0 && len(dropInfo.ItemClRes) == len(dropInfo.RecycleClRes) {
		allCosts := make([]*cl.Resource, 0, len(dropInfo.ItemClRes))
		allRes := make([]*cl.Resource, 0, len(dropInfo.RecycleClRes))
		for index, itemRes := range dropInfo.ItemClRes {
			count := user.GetResourceCount(itemRes.Type, itemRes.Value, s)
			if count == 0 {
				continue
			}
			allCosts = append(allCosts, &cl.Resource{
				Type:  itemRes.Type,
				Value: itemRes.Value,
				Count: uint32(count),
			})
			allRes = append(allRes, &cl.Resource{
				Type:  dropInfo.RecycleClRes[index].Type,
				Value: dropInfo.RecycleClRes[index].Value,
				Count: uint32(count) * dropInfo.RecycleClRes[index].Count,
			})
		}
		recycleRess := make([]*cl.Resource, 0, len(dropInfo.ItemClRes)*2)
		itemRes := goxml.GetData().DropActivityInfoM.ItemResCheck(actInfo.DropActivity)
		if itemRes != nil {
			dungeonAwardLen := len(user.Dungeon().data.Awards)
			dungeonAwardChange := false
			for i := 0; i < dungeonAwardLen; i++ {
				dungeonAward := user.Dungeon().data.Awards[i]
				recycleRes, exist := itemRes[dungeonAward.Type][dungeonAward.Value]
				if !exist {
					continue
				}
				user.Dungeon().data.Awards = append(user.Dungeon().data.Awards[:i], user.Dungeon().data.Awards[i+1:]...)
				dungeonAwardChange = true
				dungeonAwardLen--
				i--
				tmp := recycleRes.Clone()
				tmp.Count *= dungeonAward.Count
				recycleRess = append(recycleRess, tmp)
			}
			if dungeonAwardChange {
				user.Save(s)
			}
			flowerRes := s.DelDropActivityFlowerOccupy(user.ID(), itemRes)

			allRes = append(allRes, flowerRes...)
			allRes = append(allRes, recycleRess...)
		}
		allRes = MergeResources(allRes)
		if len(allRes) > 0 {
			user.Consume(s, allCosts, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_DROP_EXPIRED), 0)
			DropActivityMail(s, user, allRes, dropInfo.MailId)
		}
	}
}
