package character

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var CharacterMetrics = struct {
	StateInFlight *prometheus.GaugeVec
	MsgsCount     *prometheus.CounterVec
}{}

func init() {
	const namespace, sub = "logic", "character"

	basicLabels := []string{"type"}
	CharacterMetrics.StateInFlight = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: namespace,
		Subsystem: sub,
		Name:      "total",
		Help:      "state statistics",
	}, basicLabels)
	CharacterMetrics.MsgsCount = promauto.NewCounterVec(prometheus.CounterOpts{
		Namespace: namespace,
		Subsystem: sub,
		Name:      "msgs_total",
		Help:      "Counter of msgs",
	}, basicLabels)
}
