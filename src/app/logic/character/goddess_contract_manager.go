package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"slices"

	l4g "github.com/ivanabc/log4go"
)

type GoddessContractManager struct {
	owner *User
	data  *cl.GoddessContractInfo

	totalLevel  uint32
	totalExp    uint32
	suits       []uint32
	globalAttrs map[uint32]int64
	score       int64
}

func newGoddessContractInfo(user *User) *GoddessContractManager {
	gcm := &GoddessContractManager{
		owner:       user,
		globalAttrs: make(map[uint32]int64),
		suits:       []uint32{},
	}
	gcm.data = &cl.GoddessContractInfo{
		Goddess: make(map[uint32]*cl.Goddess),
	}
	return gcm
}

func (g *GoddessContractManager) load(goddessExp uint32, suits []uint32) {
	if g.data != nil {
		g.CalcTotalData(goddessExp, suits)
		g.SetScore(g.CalcGlobalScore())
	}
}

func (g *GoddessContractManager) loadGoddess(data *cl.GoddessContractInfo) {
	if data != nil {
		if data.Goddess != nil {
			g.data.Goddess = data.Goddess
		}
		g.data.Collections = data.Collections
		g.data.ActivityRecv = data.ActivityRecv
		g.data.ActivityState = data.ActivityState
		if g.data.ActivityRecv == nil {
			g.data.ActivityRecv = make([]uint32, 0, 2)
		}
		if g.data.ActivityState == nil {
			g.data.ActivityState = make([]uint32, 0, 2)
		}
	}
}

func (g *GoddessContractManager) SaveGoddess() {
	if g.owner.dbUser.Module3.GoddessContractInfo == nil {
		g.owner.dbUser.Module3.GoddessContractInfo = &cl.GoddessContractInfo{
			Goddess: make(map[uint32]*cl.Goddess),
		}
	}

	g.owner.dbUser.Module3.GoddessContractInfo = g.data
	g.owner.setSaveTag(saveTagModule3)
}

func (g *GoddessContractManager) SaveExp() {
	g.owner.dbUser.ModuleGlobalAttr.GoddessContractExp = g.totalExp
	g.owner.setSaveTag(saveTagModuleGlobalAttr)
}

func (g *GoddessContractManager) SaveSuit() {
	g.owner.dbUser.ModuleGlobalAttr.GoddessSuits = g.suits
	g.owner.setSaveTag(saveTagModuleGlobalAttr)
}

func (g *GoddessContractManager) AddTotalExp(exp uint32) {
	g.totalExp += exp
}

func (g *GoddessContractManager) TryLevelUp(srv servicer) (uint32, bool, map[uint32]int64) {
	oldLevel := g.totalLevel
	blessInfo := goxml.GetData().GoddessContractBlessInfoM.GetInfoByExp(g.totalExp)
	if blessInfo == nil {
		l4g.Errorf("GoddessContractInfo: TryLevelUp failed. exp:%d", g.totalExp)
		return 0, false, nil
	}
	if oldLevel != blessInfo.Level {
		blessAttr := goxml.GetData().GoddessContractBlessInfoM.GetAttr(blessInfo.Level)
		g.totalLevel = blessInfo.Level
		g.ActivateCollection(srv, g.totalLevel)
		g.owner.FireCommonEvent(srv.EventM(), event.IeGoddessContractLevelUp, uint64(blessInfo.Level))
		return oldLevel, true, blessAttr
	}
	return 0, false, nil
}

func (g *GoddessContractManager) CalcTotalData(goddessExp uint32, suits []uint32) {
	if g.data == nil {
		return
	}

	g.totalExp = goddessExp
	g.suits = suits
	blessInfo := goxml.GetData().GoddessContractBlessInfoM.GetInfoByExp(g.totalExp)
	if blessInfo == nil {
		l4g.Errorf("user:%d GoddessContractManager calc total data get blessInfo failed total exp:%d", g.owner.ID(), g.totalExp)
		return
	}
	g.totalLevel = blessInfo.Level
	g.globalAttrs = goxml.GetData().GoddessContractBlessInfoM.GetAttr(blessInfo.Level)

	for _, suit := range suits {
		skinInfo := goxml.GetData().GoddessContractSkinInfoM.Index(suit)
		if skinInfo == nil {
			l4g.Errorf("user:%d GoddessContractManager calc total get suit Id failed:%d", g.owner.ID(), suit)
			continue
		}
		for attrType, attrValue := range skinInfo.GlobalAttr {
			g.globalAttrs[attrType] += attrValue
		}
	}
}

func (g *GoddessContractManager) FlushAll() *cl.GoddessContractInfo {
	return g.data.Clone()
}

func (g *GoddessContractManager) FlushGoddessGlobalAttr() *cl.AttrInfo {
	attrInfo := &cl.AttrInfo{
		Attr: make(map[uint32]int64),
	}

	for attrId, value := range g.globalAttrs {
		attrInfo.Attr[attrId] = value
	}

	return attrInfo
}

func (g *GoddessContractManager) GetGoddessGlobalAttrs() map[uint32]int64 {
	return g.globalAttrs
}

func (g *GoddessContractManager) GetLevel() uint32 {
	return g.totalLevel
}

func (g *GoddessContractManager) GetExp() uint32 {
	return g.totalExp
}

func (g *GoddessContractManager) Get(goddessID uint32) *MemGoddess {
	if g.data == nil {
		return nil
	}
	goddess := g.data.Goddess[goddessID]
	return (*MemGoddess)(goddess)
}

func (g *GoddessContractManager) InitGoddess(goddessID uint32) *MemGoddess {
	g.data.Goddess[goddessID] = newGoddess(goddessID)
	goddess := g.data.Goddess[goddessID]
	return (*MemGoddess)(goddess)
}

func (g *GoddessContractManager) SetLevel(level uint32) {
	g.totalLevel = level
}

func (g *GoddessContractManager) SendGoddessGlobalAttrsToClient() {
	globalAttr := &cl.GlobalAttr{
		GoddessContractAttr: g.FlushGoddessGlobalAttr(),
	}

	msg := &cl.L2C_OpGlobalAttr{
		GlobalAttr: globalAttr,
	}
	g.owner.SendCmdToGateway(cl.ID_MSG_L2C_OpGlobalAttr, msg)
}

func (g *GoddessContractManager) UpdateGlobalAttrs(srv servicer) {
	g.SendGoddessGlobalAttrsToClient()
	g.owner.HeroManager().SetHeroAttrChange()
	g.owner.UpdateAllPower(srv, PowerUpdateByNormalRaise)
}

func (g *GoddessContractManager) CalcGlobalScore() int64 {
	return goxml.GetData().AttributeInfoM.CalcGlobalScoreMap(g.globalAttrs)
}

func (g *GoddessContractManager) GetScore() int64 {
	return g.score
}

func (g *GoddessContractManager) SetScore(score int64) {
	g.score = score
}

func (g *GoddessContractManager) AddGoddess(unlocks []*goxml.GoddessContractInfo) {
	if g.data == nil {
		g.data = &cl.GoddessContractInfo{
			Goddess: make(map[uint32]*cl.Goddess),
		}
	}
	if g.data.Goddess == nil {
		g.data.Goddess = make(map[uint32]*cl.Goddess)
	}

	for _, Info := range unlocks {
		goddess := newGoddessContractData(Info.GoddessId)
		g.data.Goddess[Info.GoddessId] = goddess
	}
}

func (g *GoddessContractManager) AddGoddessExp(srv servicer, goddess *MemGoddess, exp uint32) {
	//契约之所经验添加
	g.totalExp += exp
	g.SaveExp()
	//契约之所全局属性加成
	contractOldLevel, contractLevelUp, tmpAttrs := g.TryLevelUp(srv)

	//女武神加经验
	goddess.addExp(exp)
	//女武神升级检查
	oldLevel, goddessLevelUp := goddess.autoLevelUp(g.owner)

	var suitsInfo []*goxml.GoddessContractSkinExt
	if goddessLevelUp {
		//女武神皮肤解锁检查
		suitsInfo = goddess.UnlockSuit(oldLevel)
		if len(suitsInfo) > 0 {
			for _, v := range suitsInfo {
				g.suits = append(g.suits, v.GoddessSkinId)
			}
			g.SaveSuit()
		}
		g.owner.FireCommonEvent(srv.EventM(), event.IeGoddessLevelUp, uint64(goddess.Level), goddess.Id)
		g.owner.LogGoddessLevelUp(srv, goddess.Id, goddess.Level)
	}
	g.SaveGoddess()

	//契约未升级直接添加衣服属性 契约之所升级重新计算全局属性

	smsg := &cl.L2C_GoddessUpdateSuitIds{
		Ret:   uint32(ret.RET_OK),
		Suits: make([]uint32, 0, len(suitsInfo)),
	}
	if !contractLevelUp {
		//女武神皮肤属性添加
		for _, v := range suitsInfo {
			smsg.Suits = append(smsg.Suits, v.GoddessSkinId)
			for attrType, attrValue := range v.GlobalAttr {
				g.globalAttrs[attrType] += attrValue
			}
		}
	} else {
		for _, v := range suitsInfo {
			smsg.Suits = append(smsg.Suits, v.GoddessSkinId)
		}
		for _, suit := range g.suits {
			skinInfo := goxml.GetData().GoddessContractSkinInfoM.Index(suit)
			if skinInfo == nil {
				l4g.Errorf("user:%d GoddessContractManager AddGoddessExp calc total get suit Id failed:%d", g.owner.ID(), suit)
				continue
			}
			for attrType, attrValue := range skinInfo.GlobalAttr {
				tmpAttrs[attrType] += attrValue
			}
		}
		g.globalAttrs = tmpAttrs
	}

	if len(smsg.Suits) > 0 {
		smsg.Goddess = goddess.Clone()
		g.owner.SendCmdToGateway(cl.ID_MSG_L2C_GoddessUpdateSuitIds, smsg)
	}

	var oldScore int64
	var newScore int64
	if contractLevelUp || len(suitsInfo) > 0 {
		oldScore = g.score
		newScore = g.CalcGlobalScore()
		g.SetScore(newScore)
		g.UpdateGlobalAttrs(srv)
	}
	if contractLevelUp {
		g.owner.LogGoddessContractLevelUp(srv, contractOldLevel, g.totalLevel, oldScore, newScore)
	}
}

func (g *GoddessContractManager) AddSuit(srv servicer, suitInfo *goxml.GoddessContractSkinExt, goddess *MemGoddess) {
	if suitInfo == nil || goddess == nil {
		return
	}
	g.suits = append(g.suits, suitInfo.GoddessSkinId)
	if suitInfo.AutoWear == 1 {
		goddess.EquipSuit = suitInfo.GoddessSkinId
	}
	goddess.SuitIds = append(goddess.SuitIds, suitInfo.GoddessSkinId)
	g.SaveSuit()
	g.SaveGoddess()

	smsg := &cl.L2C_GoddessUpdateSuitIds{
		Ret:   uint32(ret.RET_OK),
		Suits: []uint32{suitInfo.GoddessSkinId},
	}
	smsg.Goddess = goddess.Clone()
	g.owner.SendCmdToGateway(cl.ID_MSG_L2C_GoddessUpdateSuitIds, smsg)

	//添加新增皮肤属性
	for attrType, attrValue := range suitInfo.GlobalAttr {
		g.globalAttrs[attrType] += attrValue
	}

	newScore := g.CalcGlobalScore()
	g.SetScore(newScore)
	g.UpdateGlobalAttrs(srv)
}

func (g *GoddessContractManager) GetCollections() map[uint32]uint32 {
	if g.data == nil {
		return nil
	}

	copyCollections := make(map[uint32]uint32, len(g.data.Collections))
	for k, v := range g.data.Collections {
		copyCollections[k] = v
	}

	return copyCollections
}

func (g *GoddessContractManager) ActivateCollection(srv servicer, goddessLevel uint32) {
	if g.data == nil {
		return
	}

	collectionInfo := goxml.GetData().GoddessCollectionInfoM.GetCollectionInfo(goddessLevel)
	if collectionInfo == nil {
		return
	}
	if g.data.Collections == nil {
		g.data.Collections = make(map[uint32]uint32)
	}
	g.data.Collections[collectionInfo.CollectionId] = goddessLevel
	g.owner.LogGoddessCollection(srv, g.data.Collections)
	g.SaveGoddess()
}

// ExtraAwardFromDungeon 额外增加主线挂机奖励
func (g *GoddessContractManager) ExtraAwardFromDungeon(awards []*cl.Resource) []*cl.Resource {
	return g.extraAward(goxml.GoddessCollectionTypeOnhook, 0, awards, 0)
}

// ExtraAwardFromDailyBox  额外增加日常宝箱
func (g *GoddessContractManager) ExtraAwardFromDailyBox(score uint64) []*cl.Resource {
	return g.extraAward(goxml.GoddessCollectionTypeBox, goxml.GoddessCollectionBoxDaily, nil, score)
}

// ExtraAwardFromWeeklyBox  额外增加周常宝箱
func (g *GoddessContractManager) ExtraAwardFromWeeklyBox(score uint64) []*cl.Resource {
	return g.extraAward(goxml.GoddessCollectionTypeBox, goxml.GoddessCollectionBoxWeekly, nil, score)
}

func (g *GoddessContractManager) extraAward(typ, boxType uint32, awards []*cl.Resource, score uint64) []*cl.Resource {
	if g.data == nil || len(g.data.Collections) == 0 {
		return nil
	}

	retAwards := make([]*cl.Resource, 0, len(g.data.Collections))

	if typ == goxml.GoddessCollectionTypeOnhook {
		for _, goddessLevel := range g.data.Collections {
			retAwards = append(retAwards, goxml.GetData().GoddessCollectionInfoM.RecalcAwardFromOnHook(goddessLevel, awards, g.owner.ID())...)
		}
	} else {
		for _, goddessLevel := range g.data.Collections {
			collectionInfo := goxml.GetData().GoddessCollectionInfoM.GetCollectionInfo(goddessLevel)
			if collectionInfo == nil {
				l4g.Errorf("user: %d extraAward: collectionInfo is nil. goddessLevel: %d", g.owner.ID(), goddessLevel)
				return nil
			}
			if collectionInfo.Type != goxml.GoddessCollectionTypeBox {
				continue
			}

			boxInfo := goxml.GetData().GoddessCollectionBoxInfoM.Index(collectionInfo.BoxId)
			if boxInfo == nil {
				l4g.Errorf("user: %d extraAward: boxInfo is nil. boxID: %d", g.owner.ID(), collectionInfo.BoxId)
				return nil
			}

			retAwards = append(retAwards, g.calcBoxAward(boxType, score, boxInfo)...)
		}
	}

	// 如相同的award存在多个，合并下
	if len(retAwards) > 1 {
		retAwards = g.owner.MergeResources(retAwards)
	}

	return retAwards
}

func (g *GoddessContractManager) calcBoxAward(boxType uint32, score uint64, boxInfo *goxml.GoddessCollectionBoxInfo) []*cl.Resource {
	retAwards := make([]*cl.Resource, 0, 3) //nolint:mnd

	if boxType == boxInfo.Type1 && score == boxInfo.Param1 {
		retAwards = append(retAwards, goxml.GenSimpleResource(boxInfo.ResType1, boxInfo.ResValue1, boxInfo.ResCount1))
	}
	if boxType == boxInfo.Type2 && score == boxInfo.Param2 {
		retAwards = append(retAwards, goxml.GenSimpleResource(boxInfo.ResType2, boxInfo.ResValue2, boxInfo.ResCount2))
	}
	if boxType == boxInfo.Type3 && score == boxInfo.Param3 {
		retAwards = append(retAwards, goxml.GenSimpleResource(boxInfo.ResType3, boxInfo.ResValue3, boxInfo.ResCount3))
	}

	return retAwards
}

func (g *GoddessContractManager) CheckActivityGoddessRecv(id uint32) bool {
	return slices.Contains(g.data.ActivityRecv, id)
}

func (g *GoddessContractManager) SetGoddessContractRecoveryRecv(id uint32) {
	g.data.ActivityRecv = append(g.data.ActivityRecv, id)
	g.SaveGoddess()
}

func (g *GoddessContractManager) FlushActivityGoddessRecv() []uint32 {
	return slices.Clone(g.data.ActivityRecv)
}

func (g *GoddessContractManager) GetRecoveryCount() uint32 {
	return uint32(len(g.data.ActivityState))
}

func (g *GoddessContractManager) IsActive(id uint32) bool {
	return slices.Contains(g.data.ActivityState, id)
}

func (g *GoddessContractManager) AppendActive(id uint32) {
	g.data.ActivityState = append(g.data.ActivityState, id)
	g.SaveGoddess()
}

func (g *GoddessContractManager) CopyActive() []uint32 {
	return slices.Clone(g.data.ActivityState)
}
