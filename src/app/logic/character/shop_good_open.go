package character

import (
	"app/goxml"
)

// ShopGoodOpen ：检查商品是否开启
type ShopGoodOpen interface {
	IsShopGoodOpen(*goxml.ShopRegularGoodsInfoExt, Servicer) bool
}

func GetShopGoodOpenI(extType uint32, u *User, srv servicer) ShopGoodOpen {
	switch extType {
	case goxml.ShopGoodsArtifactDebutLimit:
		return u.ArtifactDebutM()
	case goxml.ShopGoodsDungeonLimit:
		return u.Dungeon()
	case goxml.ShopGoodsMirageTopLevelLimit:
		return (*MirageTopFloor)(u.MirageManager())
	case goxml.ShopGoodsMirageEmpireTopLevelLimit,
		goxml.ShopGoodsMirageForestTopLevelLimit,
		goxml.ShopGoodsMirageMoonTopLevelLimit,
		goxml.ShopGoodsMirageProtossTopLevelLimit,
		goxml.ShopGoodsMirageDemonTopLevelLimit,
		goxml.ShopGoodsMirageSixTopLevelLimit:
		return (*MirageCopyTopFloor)(u.MirageManager())
	case goxml.ShopGoodsGuildLevelLimit:
		limit := &GoodsGuildLevelLimit{
			guildLevel: srv.GetUserGuildLevel(u.ID()),
		}
		return limit
	case goxml.ShopGoodsDisorderLandAllMapHurdleLevelLimit:
		return (*DisorderLandTopLevel)(u.DisorderLand())
	case goxml.ShopGoodsSkinNotRepeatOwnership:
		return (*SkinNoRepeatOwnership)(u.SkinManager())
	case goxml.ShopGoodsArtifactOwnershipAdvancedMazeToken:
		return (*ArtifactOwnershipAdvancedMazeToken)(u.ArtifactManager())
	case goxml.ShopGoodsSeasonDoorPassLevelLimit1, goxml.ShopGoodsSeasonDoorPassLevelLimit2:
		return u.SeasonDoor()
	case goxml.ShopGoodsSeasonMapExploreLimit:
		return u.SeasonMap()
	default:
		return nil
	}
}
