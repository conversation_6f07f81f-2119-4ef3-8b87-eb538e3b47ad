package character

import (
	"app/goxml"
	aevent "app/logic/event"
	"app/protos/out/cl"
	"app/protos/out/ret"
)

type ActivitySubTaskS struct {
	baseController
}

func (a *ActivitySubTaskS) InitSubFunc(sum *cl.ActivitySum) {
	sum.Task = &cl.ActivitySubTask{
		TaskTypeProgress: make(map[uint32]*cl.TaskTypeProgress),
		RecvTask:         make(map[uint32]bool),
	}
}

func (a *ActivitySubTaskS) AfterInitSubFunc(sum *cl.ActivitySum, user *User, s Servicer, resetDaily bool) {
	fun, exist := ActivitySumFuncManager[sum.ActivityType]
	if !exist {
		return
	}
	for _, event := range fun.WatchEvent() {
		initData, exist := InitTriggerEvent[event]
		needTrigger := false
		if exist {
			needTrigger = !(resetDaily && initData.resetDailyNotTrigger)
		}
		if needTrigger {
			(*ActivitySum)(sum).OnActivitySubTaskEvent(user, event, initData.progress, initData.values, sum.SysId, sum.ActivityType)
		}
	}
}

type InitAfterEventParam struct {
	event                uint32
	progress             uint64
	values               []uint32
	resetDailyNotTrigger bool
}

var InitTriggerEvent = map[uint32]*InitAfterEventParam{
	uint32(aevent.AeContinueLoginToX): {
		event:                aevent.AeContinueLoginToX,
		progress:             1,
		values:               []uint32{},
		resetDailyNotTrigger: true,
	},
}

func (a *ActivitySum) IsRecviveTaskAward(taskId uint32) bool {
	return a.Task.RecvTask[taskId]
}

func (a *ActivitySum) SetRecviveTaskAward(taskIds []uint32) {
	if a.Task.RecvTask == nil {
		a.Task.RecvTask = make(map[uint32]bool)
	}
	for _, taskId := range taskIds {
		a.Task.RecvTask[taskId] = true
	}
}

func (a *ActivitySum) GetRecviveTaskAward2Client() []uint32 {
	ret := make([]uint32, 0, len(a.Task.RecvTask))
	for k := range a.Task.RecvTask {
		ret = append(ret, k)
	}
	return ret
}

func (a *ActivitySum) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo, owner *User) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return owner.CalcTaskProgress(taskTypeInfo)
	}
	return a.Task.TaskTypeProgress[taskTypeInfo.Id]
}

func (a *ActivitySum) OnActivitySubTaskEvent(u *User, event uint32, progress uint64, values []uint32, actID, actType uint32) bool {
	if a != nil && a.Task != nil && a.Task.TaskTypeProgress != nil {
		progressMap, change := u.TaskTypeOnEvent(a.Task.TaskTypeProgress, event, progress, values)
		if change {
			a.sendTaskProgress(u, progressMap, actID, actType)
		}
		return change
	}
	return false
}

func (a *ActivitySum) sendTaskProgress(u *User, progressMap map[uint32]*cl.TaskTypeProgress, actID, actType uint32) {
	u.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTaskUpdate, &cl.L2C_ActivitySumTaskUpdate{
		Ret:          uint32(ret.RET_OK),
		ActivityType: actType,
		ActivityId:   actID,
		TaskProgress: progressMap,
	})
}

func (a *ActivitySum) DeepCopyProgress() map[uint32]*cl.TaskTypeProgress {
	ret := make(map[uint32]*cl.TaskTypeProgress)
	if a.Task != nil && len(a.Task.TaskTypeProgress) > 0 {
		for k, v := range a.Task.TaskTypeProgress {
			ret[k] = v.Clone()
		}
	}

	return ret
}

func (a *ActivitySum) DeepCopyRecvTask() map[uint32]bool {
	ret := make(map[uint32]bool)
	if a.Task != nil && len(a.Task.RecvTask) > 0 {
		for k, v := range a.Task.RecvTask {
			ret[k] = v
		}
	}
	return ret
}

func (a *ActivitySum) GetXmlRecvTask(id uint32) goxml.RecvTask {
	switch a.ActivityType {
	default:
		return goxml.GetData().ActivityTurntableTaskInfoM.GetRecvTaskID(id)
	}
}
