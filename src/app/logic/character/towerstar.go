package character

import (
	"app/goxml"
	aevent "app/logic/event"
	"app/logic/helper"
	"app/logic/rank"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type Towerstar struct {
	owner *User
	data  *cl.Towerstar
}

func newTowerstar(user *User) *Towerstar {
	return &Towerstar{
		owner: user,
	}
}

func (t *Towerstar) Owner() *User {
	return t.owner
}

func (t *Towerstar) load(data *cl.Towerstar) {
	t.data = data
	if t.data == nil {
		t.data = &cl.Towerstar{
			ChapterInfo: make(map[uint32]*cl.TowerstarChapterInfo),
		}
	}
	if t.data.ChapterInfo == nil {
		t.data.ChapterInfo = make(map[uint32]*cl.TowerstarChapterInfo)
	}
}

func (t *Towerstar) save() {
	if t.Owner().dbUser.Module2.Towerstar == nil {
		t.Owner().dbUser.Module2.Towerstar = t.data
	}
	t.Owner().setSaveTag(saveTagModule2)
}

func (t *Towerstar) Flush(chapter uint32) *cl.Towerstar {
	if chapter == 0 {
		return t.CloneCurChapter()
	} else {
		return t.CloneOtherChapter(chapter)
	}
}

func (t *Towerstar) CloneCurChapter() *cl.Towerstar {
	if t.data != nil {
		towerStar := &cl.Towerstar{}
		if len(t.data.ChapterInfo) > 0 {
			towerStar.ChapterInfo = make(map[uint32]*cl.TowerstarChapterInfo, len(t.data.ChapterInfo))
			curChapter := t.GetUnlockChapter()
			for k, e := range t.data.ChapterInfo {
				if k == curChapter {
					towerStar.ChapterInfo[k] = e.Clone()
				} else {
					towerStar.ChapterInfo[k] = &cl.TowerstarChapterInfo{
						TotalStar: e.TotalStar,
					}
					if len(e.RewardInfo) > 0 {
						towerStar.ChapterInfo[k].RewardInfo = make([]uint32, len(e.RewardInfo))
						copy(towerStar.ChapterInfo[k].RewardInfo, e.RewardInfo)
					}
				}
			}
		}
		towerStar.CurrentUnlockDungeon = t.data.CurrentUnlockDungeon
		towerStar.CurrentOpenChapter = t.data.CurrentOpenChapter
		towerStar.OpenChapterUpdateTime = t.data.OpenChapterUpdateTime
		towerStar.TotalStar = t.data.TotalStar
		towerStar.MaxRemovedChapter = t.data.MaxRemovedChapter
		return towerStar
	}
	return nil
}

func (t *Towerstar) CloneOtherChapter(chapter uint32) *cl.Towerstar {
	if t.data != nil {
		towerStar := &cl.Towerstar{}
		if len(t.data.ChapterInfo) > 0 {
			towerStar.ChapterInfo = make(map[uint32]*cl.TowerstarChapterInfo)
			info, exist := t.data.ChapterInfo[chapter]
			if exist {
				towerStar.ChapterInfo[chapter] = info.Clone()
			}
		}
		towerStar.CurrentUnlockDungeon = t.data.CurrentUnlockDungeon
		towerStar.CurrentOpenChapter = t.data.CurrentOpenChapter
		towerStar.OpenChapterUpdateTime = t.data.OpenChapterUpdateTime
		towerStar.TotalStar = t.data.TotalStar
		towerStar.MaxRemovedChapter = t.data.MaxRemovedChapter
		return towerStar
	}
	return nil
}

func (t *Towerstar) GetUnlockChapter() uint32 {
	dungeonInfo := goxml.GetData().TowerstarDungeonInfoM.Index(t.data.CurrentUnlockDungeon)
	if dungeonInfo == nil {
		return 0
	}
	return dungeonInfo.Chapter
}

func (t *Towerstar) Update() {
	t.updateDungeon()
}

func (t *Towerstar) updateDungeon() {
	if t.data.CurrentUnlockDungeon >= goxml.GetData().TowerstarDungeonInfoM.GetMaxDungeon() {
		return
	}
	t.unlockNextDungeon()
}

func (t *Towerstar) checkUnlockChapter(chapter uint32) bool {
	chapterConfig := goxml.GetData().TowerstarChapterInfoM.Index(chapter)
	if chapterConfig == nil {
		l4g.Errorf("chapterInfo nil. chapter:%d", chapter)
		return false
	}
	if chapterConfig.UnlockLevel > 0 &&
		t.owner.Level() < chapterConfig.UnlockLevel {
		return false
	}
	if chapterConfig.UnlockPower > 0 &&
		t.Owner().Power() < chapterConfig.UnlockPower {
		return false
	}

	if chapterConfig.UnlockChapter > 0 &&
		chapterConfig.UnlockStar > 0 &&
		chapterConfig.UnlockChapter > t.data.MaxRemovedChapter &&
		t.data.ChapterInfo[chapterConfig.UnlockChapter].TotalStar < chapterConfig.UnlockStar {
		return false
	}

	return true
}

func (t *Towerstar) unlockChapter(chapter uint32) bool {
	if !t.checkUnlockChapter(chapter) {
		return false
	}
	t.data.ChapterInfo[chapter] = &cl.TowerstarChapterInfo{
		DungeonInfo: make(map[uint32]uint32),
	}
	t.save()
	return true
}

func (t *Towerstar) unlockDungeon(dungeonID uint32) {
	dungeonConfig := goxml.GetData().TowerstarDungeonInfoM.Index(dungeonID)
	if chapterInfo, exist := t.data.ChapterInfo[dungeonConfig.Chapter]; exist {
		chapterInfo.DungeonInfo[dungeonID] = 0
	} else {
		if !t.unlockChapter(dungeonConfig.Chapter) {
			return
		}
		t.data.ChapterInfo[dungeonConfig.Chapter].DungeonInfo[dungeonID] = 0
	}
	t.data.CurrentUnlockDungeon = dungeonID
	t.save()
}

func (t *Towerstar) unlockNextDungeon() {
	if t.data.CurrentUnlockDungeon != 0 {
		//不是初始化的情况下，只要当前解锁关卡有星级，就可以做解锁下一关的判断
		dungeonConfig := goxml.GetData().TowerstarDungeonInfoM.Index(t.data.CurrentUnlockDungeon)
		if dungeonConfig == nil {
			l4g.Errorf("user: %d unlockNextDungeon: %d no dungeonConfig",
				t.owner.ID(), t.data.CurrentUnlockDungeon)
			return
		}
		if dungeonConfig.Chapter > t.data.MaxRemovedChapter &&
			t.data.ChapterInfo[dungeonConfig.Chapter].DungeonInfo[t.data.CurrentUnlockDungeon] == 0 {
			return
		}
	}
	unlockDungeon := goxml.GetData().TowerstarDungeonInfoM.GetNextDungeon(t.data.CurrentUnlockDungeon)
	//已经没有后续关卡了
	if unlockDungeon == 0 {
		return
	}
	t.unlockDungeon(unlockDungeon)
}

func (t *Towerstar) CheckGetData(srv servicer, cmsg *cl.C2L_TowerstarGetData) ret.RET {
	retCode := ret.RET_OK
	if !t.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_TOWERSTAR), srv) {
		retCode = ret.RET_FUNCTION_NOT_OPEN
		l4g.Errorf("user: %d C2L_TowerstarGetData: function not open",
			t.owner.ID())
		return retCode
	}
	return retCode
}

func (t *Towerstar) CheckFight(srv servicer, cmsg *cl.C2L_TowerstarFight) ret.RET {
	retCode := ret.RET_OK
	if !t.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_TOWERSTAR), srv) {
		retCode = ret.RET_FUNCTION_NOT_OPEN
		l4g.Errorf("user: %d C2L_TowerstarFight: function not open",
			t.owner.ID())
		return retCode
	}
	if !t.owner.CheckAndSetOperateInterval(srv, OITowerstar) {
		retCode = ret.RET_OPERATE_TOO_OFTEN
		l4g.Errorf("user: %d C2L_TowerstarFight: operate too frequently",
			t.owner.ID())
		return retCode
	}

	if !helper.CheckBytesLen(cmsg.ClientData, MaxClientDataLen) {
		retCode = ret.RET_OPERATE_TOO_OFTEN
		l4g.Errorf("user: %d C2L_TowerstarFight: ClientData too long", t.owner.ID())
		return retCode
	}

	if cmsg.DungeonId > t.data.CurrentUnlockDungeon {
		retCode = ret.RET_TOWERSTAR_DUNGEON_NOT_UNLOCK
		l4g.Errorf("user: %d C2L_TowerstarFight: dungeon not unlock. retCode:%d dungeonId:%d current:%d",
			t.owner.ID(), retCode, cmsg.DungeonId, t.data.CurrentUnlockDungeon)
		return retCode
	}
	dungeonConfig := goxml.GetData().TowerstarDungeonInfoM.Index(cmsg.DungeonId)
	if dungeonConfig == nil {
		retCode = ret.RET_TOWERSTAR_DUNGEON_ILLEGAL
		l4g.Errorf("user: %d C2L_TowerstarFight: dungeon illegal. retCode:%d dungeonId:%d",
			t.owner.ID(), retCode, cmsg.DungeonId)
		return retCode
	}
	if t.getDungeonStarInfo(dungeonConfig) == TowerstarFullStar {
		retCode = ret.RET_TOWERSTAR_DUNGEON_STAR_FULL
		l4g.Errorf("user: %d C2L_TowerstarFight: dungeon star full. retCode:%d dungeonId:%d",
			t.owner.ID(), retCode, cmsg.DungeonId)
		return retCode
	}
	return retCode
}

func (t *Towerstar) getDungeonStarInfo(dungeonConfig *goxml.TowerstarDungeonInfo) uint32 {
	if dungeonConfig.Chapter <= t.data.MaxRemovedChapter {
		return TowerstarFullStar
	}
	return t.data.ChapterInfo[dungeonConfig.Chapter].DungeonInfo[dungeonConfig.Id]
}

func (t *Towerstar) UpdateDungeonStarInfo(
	srv servicer, dungeonConfig *goxml.TowerstarDungeonInfo, newStarInfo, formationId uint32) (ret.RET, []*cl.Resource) {
	retCode := ret.RET_OK
	var awards []*cl.Resource
	if newStarInfo == 0 {
		return retCode, awards
	}

	oldStarInfo := t.getDungeonStarInfo(dungeonConfig)
	addStarNum, oldStarNum, newStarNum := t.calStarNum(oldStarInfo, newStarInfo)
	if addStarNum < 0 || oldStarInfo == newStarInfo {
		return retCode, awards
	}
	if addStarNum > 0 {
		for i := newStarNum; i > oldStarNum; i-- {
			awards = append(awards, dungeonConfig.PassRewardClRes[i-1])
		}
		var result uint32
		result, awards = t.owner.Award(srv, awards, AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_TOWERSTAR_FIRST_AWARD), 0)
		if result != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d C2L_TowerstarFight: send award failed. errorCode: %d",
				t.owner.ID(), result)
			return ret.RET(result), awards
		}
		t.Owner().FireCommonEvent(srv.EventM(), aevent.AeTowerStarTotalStarToX, uint64(addStarNum))
	}
	if oldStarInfo == 0 {
		t.Owner().FireCommonEvent(srv.EventM(), aevent.IeFirstStarTowerToX, uint64(dungeonConfig.Id))
	}

	chapterInfo := t.data.ChapterInfo[dungeonConfig.Chapter]
	chapterInfo.DungeonInfo[dungeonConfig.Id] = newStarInfo
	if addStarNum > 0 {
		t.data.TotalStar += uint32(addStarNum)
		chapterInfo.TotalStar += uint32(addStarNum)
		t.updateRank(srv, formationId)
		t.owner.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_TOWERSTAR), 0, t.data.CurrentUnlockDungeon)
		t.unlockNextDungeon()
	}
	t.save()
	return retCode, awards
}

func (t *Towerstar) GetHighestPassDungeonID() uint32 {
	dungeonConfig := goxml.GetData().TowerstarDungeonInfoM.Index(t.data.CurrentUnlockDungeon)
	if dungeonConfig == nil {
		return 0
	}
	if t.getDungeonStarInfo(dungeonConfig) == 0 {
		return dungeonConfig.Dungeon
	}
	return t.data.CurrentUnlockDungeon
}

func (t *Towerstar) GetTotalStar() uint32 {
	return t.data.TotalStar
}

func (t *Towerstar) updateRank(srv servicer, formationId uint32) {
	srv.CommonRankM().Insert(goxml.TowerstarRankId,
		rank.NewUserTowerstar(t.owner.ID(), t.GetHighestPassDungeonID(), t.GetTotalStar(), time.Now().Unix(),
			uint64(t.owner.CalFormationPower(formationId))))
}

func (t *Towerstar) calStarNum(oldStarInfo uint32, newStarInfo uint32) (int, int, int) {
	hasNum := 0
	newNum := 0
	for i := 0; i < 3; i++ {
		if util.BitAndUint32(oldStarInfo, i) {
			hasNum++
		}
		if util.BitAndUint32(newStarInfo, i) {
			newNum++
		}
	}
	return newNum - hasNum, hasNum, newNum
}

func (t *Towerstar) CheckStarAward(srv servicer, cmsg *cl.C2L_TowerstarStarRecvAward) ret.RET {
	retCode := ret.RET_OK
	if !t.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_TOWERSTAR), srv) {
		retCode = ret.RET_FUNCTION_NOT_OPEN
		l4g.Errorf("user: %d C2L_TowerstarStarReward: function not open",
			t.owner.ID())
		return retCode
	}
	if len(cmsg.Ids) == 0 || len(cmsg.Ids) > CommandMaxParamLen {
		retCode = ret.RET_TOWERSTAR_REWARD_ILLEGAL
		l4g.Errorf("user: %d C2L_TowerstarStarReward: ids empty. retCode:%d",
			t.owner.ID(), retCode)
		return retCode
	}
	set := make(map[uint32]struct{})
	for _, id := range cmsg.Ids {
		rewardConfig := goxml.GetData().TowerstarRewardInfoM.Index(id)
		if rewardConfig == nil {
			retCode = ret.RET_TOWERSTAR_REWARD_ILLEGAL
			l4g.Errorf("user: %d C2L_TowerstarStarReward: id not exist. retCode:%d id:%d",
				t.owner.ID(), retCode, id)
			return retCode
		}
		if rewardConfig.Chapter <= t.data.MaxRemovedChapter {
			retCode = ret.RET_REPEATED_RECEIVE_AWARD
			l4g.Errorf("user: %d C2L_TowerstarStarReward: star repeate receive award. retCode:%d id:%d",
				t.owner.ID(), retCode, id)
			return retCode
		}
		chapterInfo := t.data.ChapterInfo[rewardConfig.Chapter]
		if chapterInfo == nil {
			retCode = ret.RET_TOWERSTAR_CHAPTER_NOT_UNLOCK
			l4g.Errorf("user: %d C2L_TowerstarStarReward: chapter not unlock. retCode:%d chapter:%d",
				t.owner.ID(), retCode, rewardConfig.Chapter)
			return retCode
		}

		if _, exist := set[id]; exist {
			retCode = ret.RET_REPEATED_PARAM
			l4g.Errorf("user: %d C2L_TowerstarStarReward: id repeate. retCode:%d id:%d",
				t.owner.ID(), retCode, id)
			return retCode
		}
		set[id] = struct{}{}

		if rewardConfig.StarNum > chapterInfo.TotalStar {
			retCode = ret.RET_TOWERSTAR_REWARD_ILLEGAL
			l4g.Errorf("user: %d C2L_TowerstarStarReward: star over totalStar. retCode:%d id:%d totalStar:%d",
				t.owner.ID(), retCode, id, chapterInfo.TotalStar)
			return retCode
		}
		for _, v := range chapterInfo.RewardInfo {
			if v == id {
				retCode = ret.RET_REPEATED_RECEIVE_AWARD
				l4g.Errorf("user: %d C2L_TowerstarStarReward: star repeate receive award. retCode:%d id:%d rewards:%v",
					t.owner.ID(), retCode, id, chapterInfo.RewardInfo)
				return retCode
			}
		}
	}

	return retCode
}

func (t *Towerstar) checkChapterFinishAndRemove() {
	dungeonConfig := goxml.GetData().TowerstarDungeonInfoM.Index(t.data.CurrentUnlockDungeon)
	if dungeonConfig == nil {
		return
	}
	checkChapter := t.data.MaxRemovedChapter + 1
	for ; checkChapter < dungeonConfig.Chapter; checkChapter++ {
		chapterInfo := t.data.ChapterInfo[checkChapter]
		if chapterInfo == nil {
			break
		}
		if !goxml.GetData().TowerstarRewardInfoM.IsAllReward(checkChapter, chapterInfo.RewardInfo) {
			break
		}
		delete(t.data.ChapterInfo, checkChapter)
		t.data.MaxRemovedChapter = checkChapter
		t.save()
	}
}

func (t *Towerstar) RecvStarAward(srv servicer, cmsg *cl.C2L_TowerstarStarRecvAward) (ret.RET, []*cl.Resource) {
	retCode := ret.RET_OK
	awards := make([]*cl.Resource, 0)
	for _, id := range cmsg.Ids {
		rewardConfig := goxml.GetData().TowerstarRewardInfoM.Index(id)
		awards = append(awards, rewardConfig.RewardClRes...)
	}
	var result uint32
	result, _ = t.owner.Award(srv, awards, AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_TOWERSTAR_STAR_AWARD), 0)
	if result != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d C2L_TowerstarStarReward: send award failed. errorCode: %d",
			t.owner.ID(), result)
		return ret.RET(result), awards
	}
	t.receivedStarAward(cmsg.Ids)
	return retCode, awards
}

func (t *Towerstar) receivedStarAward(ids []uint32) {
	for _, id := range ids {
		rewardConfig := goxml.GetData().TowerstarRewardInfoM.Index(id)
		chapterInfo := t.data.ChapterInfo[rewardConfig.Chapter]
		chapterInfo.RewardInfo = append(chapterInfo.RewardInfo, id)
	}

	t.checkChapterFinishAndRemove()

	t.save()
}

func (t *Towerstar) CheckDailyAward(srv servicer) ret.RET {
	retCode := ret.RET_OK
	if !t.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_TOWERSTAR), srv) {
		retCode = ret.RET_FUNCTION_NOT_OPEN
		l4g.Errorf("user: %d C2L_TowerstarDailyRecvAward: function not open",
			t.owner.ID())
		return retCode
	}
	if t.GetTotalStar() == 0 {
		retCode = ret.RET_TOWERSTAR_DAILY_NO_REWARD
		l4g.Errorf("user: %d C2L_TowerstarDailyRecvAward: daily award nil.",
			t.owner.ID())
		return retCode
	}
	return retCode
}

func (t *Towerstar) RecvDailyAward(srv servicer) (ret.RET, []*cl.Resource) {
	retCode := ret.RET_OK
	var awards []*cl.Resource

	result, costs, numSummary := t.owner.CheckNumByType(uint32(common.PURCHASEID_TOWERSTAR_DAILY_AWARD_COUNT), 1)
	if result != uint32(ret.RET_OK) {
		retCode = ret.RET(result)
		l4g.Errorf("user: %d C2L_TowerstarDailyRecvAward: receive daily award failed. count not enough",
			t.owner.ID())
		return retCode, awards
	}

	dungeonID := t.GetHighestPassDungeonID()
	dungeonConfig := goxml.GetData().TowerstarDungeonInfoM.Index(dungeonID)
	awards = append(awards, dungeonConfig.DailyRewardClRes...)
	if len(costs) > 0 {
		result, awards = t.owner.Trade(srv, costs, awards,
			uint32(log.RESOURCE_CHANGE_REASON_TOWERSTAR_DAILY_AWARD), 0)
	} else {
		result, awards = t.owner.Award(srv, awards, AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_TOWERSTAR_DAILY_AWARD), 0)
	}

	if result != uint32(ret.RET_OK) {
		retCode = ret.RET(result)
		l4g.Errorf("user: %d C2L_TowerstarDailyRecvAward: send daily award failed. errorCode: %d",
			t.owner.ID(), retCode)
		return retCode, awards
	}
	t.owner.AddNumByType(uint32(common.PURCHASEID_TOWERSTAR_DAILY_AWARD_COUNT), numSummary)

	return retCode, awards
}

func (t *Towerstar) CheckCanRecvStarAward() bool {
	for chapter, chapterInfo := range t.data.ChapterInfo {
		if goxml.GetData().TowerstarRewardInfoM.IsCanRecvReward(chapter, chapterInfo.TotalStar, chapterInfo.RewardInfo) {
			return true
		}
	}
	return false
}

func (t *Towerstar) Reset() {
	t.data = &cl.Towerstar{
		ChapterInfo: make(map[uint32]*cl.TowerstarChapterInfo),
	}
	t.save()
}

func (t *Towerstar) GMSetGungeonID(dungeonID uint32) {
	t.Reset()
	var chapterInfo *cl.TowerstarChapterInfo
	for _, dungeonConfig := range goxml.GetData().TowerstarDungeonInfoM.Datas {
		if dungeonConfig.Id > dungeonID {
			continue
		}
		if value, exist := t.data.ChapterInfo[dungeonConfig.Chapter]; exist {
			chapterInfo = value
		} else {
			chapterInfo = &cl.TowerstarChapterInfo{
				DungeonInfo: make(map[uint32]uint32),
			}
			t.data.ChapterInfo[dungeonConfig.Chapter] = chapterInfo
		}
		chapterInfo.DungeonInfo[dungeonConfig.Id] = TowerstarFullStar
		chapterInfo.TotalStar += 3
		t.data.TotalStar += 3
	}
	t.data.CurrentUnlockDungeon = dungeonID
	t.save()
}

func (t *Towerstar) CheckCanFight() bool {
	t.Update()
	dungeonConfig := goxml.GetData().TowerstarDungeonInfoM.Index(t.data.CurrentUnlockDungeon)
	if dungeonConfig == nil {
		l4g.Errorf("user: %d C2L_TowerstarFight: dungeon illegal. dungeonId:%d",
			t.owner.ID(), t.data.CurrentUnlockDungeon)
		return false
	}
	if t.getDungeonStarInfo(dungeonConfig) != TowerstarFullStar {
		return true
	}
	for chapter, chapterInfo := range t.data.ChapterInfo {
		chapterConfig := goxml.GetData().TowerstarChapterInfoM.Index(chapter)
		if chapterConfig == nil {
			l4g.Errorf("chapterInfo nil. chapter:%d", chapter)
			continue
		}
		if chapterInfo.TotalStar < chapterConfig.Star {
			return true
		}
	}
	return false
}

func (t *Towerstar) CheckDynamicFormationOpen(chapter uint32) bool {
	return chapter >= goxml.GetData().ConfigInfoM.GetTowerstarDynamicFormationOpenChapter()
}
