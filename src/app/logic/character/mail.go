package character

import (
	"app/goxml"
	"app/logic/mail"
	"app/protos/in/gm"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type groupMail struct {
	*cl.Mail
	users map[uint64]struct{}
}

func newGroupMail(m *cl.Mail) *groupMail {
	return &groupMail{
		Mail:  m,
		users: make(map[uint64]struct{}),
	}
}

func (g *groupMail) addUser(id uint64) {
	g.users[id] = struct{}{}
}

func (g *groupMail) createMail(u *User) {
	if _, exist := g.users[u.ID()]; exist {
		u.MailBox().AddWithOptional(g.<PERSON>(), true)
		delete(g.users, u.ID())
	}
}

// 缓存群组邮件
type groupMailCache struct {
	ms map[uint64]*groupMail
}

func newGroupMailCache() *groupMailCache {
	return &groupMailCache{
		ms: make(map[uint64]*groupMail),
	}
}

func (g *groupMailCache) add(m *groupMail) {
	g.ms[m.Id] = m
}

func (g *groupMailCache) remove(id uint64) {
	delete(g.ms, id)
}

func (g *groupMailCache) createMails(u *User) {
	//l4g.Debug("mailtest user:%d createMails :%+v", u.ID(), g.ms)
	for _, gMail := range g.ms {
		gMail.createMail(u)
	}
	//这里不用做删除判断，因为下面会有地方把数据清除掉，等数据库落地完成，就不需要从这个里面拿数据了
}

func (um *UserManager) addGroupMail(m *groupMail) {
	um.groupMails.add(m)
}

func (um *UserManager) createGroupMails(user *User) {
	um.groupMails.createMails(user)
}

// 删除群邮件API
func (um *UserManager) RemoveGroupMail(id uint64) {
	um.groupMails.remove(id)
}

/********************************** Mail API **********************************/

// 普通邮件
func NormalMail(box *mail.Box, mailID, reason uint32, uniqueID uint64, params []string, awards []*cl.Resource, genTime int64) {
	box.Add(mail.NewMail(mailID, uniqueID, params, awards, mail.TypeNormal, reason, genTime))
}

// 群邮件
//
//nolint:varnamelen
func GroupMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource,
	users []uint64, reason uint32, rechargeParams *gm.WebLargeRechargeMailParams) error {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, reason, 0)
	if mailID == MailIDGM {
		m.MultiLang = true
		err := srv.MultiLangM().AddMailMultiLangContents(srv, m.Params, m.Id, m.ExpiredTime)
		if err != nil {
			l4g.Errorf("%s", err.Error())
			return err
		}
		m.Params = nil
	}
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	var gMail *groupMail
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			if gMail == nil {
				gMail = newGroupMail(m.Clone())
			}
			gMail.addUser(id)

			msg.Users = append(msg.Users, id)
		}
		if reason == uint32(log.SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL) {
			if u == nil {
				NewMaybeOfflineUserLog(srv, id).LogWebLargeRecharge(srv, rechargeParams, m.Id)
			} else {
				u.LogWebLargeRecharge(srv, rechargeParams, m.Id)
			}
		}
	}
	if len(msg.Users) != 0 {
		srv.UserM().addGroupMail(gMail)
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
	return nil
}

func addGlobalMail(m *cl.Mail, u *User) {
	if mail.CheckGlobalMailConds(m, u) &&
		mail.CheckGlobalMailOpAndChannel(m, u) {
		u.MailBox().Add(m.Clone())
		u.SendNewMailTipToClient(false)
	}
}

// 系统邮件(全服邮件)
func (um *UserManager) CreateGlobalMail(srv servicer, m *cl.Mail) {
	l4g.Debugf("CreateGlobalMail: m -> %+v", m)
	//全服邮件必须设置create time参数
	hasCreateTime := false
	if len(m.And) > 0 {
		for _, cond := range m.And {
			if common.MAIL_COND(cond.Type) == common.MAIL_COND_TYPE_CREATE_TIME {
				hasCreateTime = true
				break
			}
		}
		if !hasCreateTime {
			l4g.Errorf("global mail has not create time: %+v", m)
			return
		}
	}

	um.mails.Add(m)
	um.saveMailBox(srv)
	l4g.Infof("new global mail: %+v", m)
	um.Range(func(u *User) { addGlobalMail(m, u) })
	//修改离线玩家数据
	um.Cache().Range(func(u *User) { addGlobalMail(m, u) })
}

/********************************** Mail API **********************************/

// GM邮件
func GMMail(srv servicer, params []string, ids []uint64, data *gm.UsersMail, result *gm.Result) {
	if len(ids) <= 0 {
		l4g.Errorf("GMMail: user.Ids num is 0! data:%+v", data)
		result.Code = uint32(ret.RET_ERROR)
		return
	}
	reason := uint32(0)
	if data.Type == WebLargeRechargeGmMailType {
		reason = uint32(log.SUB_TYPE_ID_RECHARGE_WEB_LARGE_MAIL)
	}

	err := GroupMail(srv, MailIDGM, params, data.Ress, ids, reason, data.RechargeParams)
	if err != nil {
		l4g.Errorf("GMMail: groupMail error. error:%+v", err)
		result.Code = uint32(ret.RET_ERROR)
		return
	}
}

// GM全服邮件
func GMServerMail(srv servicer, sm *gm.ServerMail, result *gm.Result) {
	l4g.Debugf("GMServerMail: %+v", sm)
	m := mail.NewMail(MailIDGM, srv.CreateUniqueID(), sm.Content, sm.Awards, mail.TypeGlobal, 0, 0)
	m.And = sm.And
	m.Or = sm.Or
	m.Ops = sm.Ops
	m.Channels = sm.Channels
	m.GmId = sm.GmId
	if sm.ExpiredTime > 0 {
		m.ExpiredTime = sm.ExpiredTime
	}
	m.MultiLang = true
	err := srv.MultiLangM().AddMailMultiLangContents(srv, sm.Content, m.Id, m.ExpiredTime)
	if err != nil {
		result.Code = uint32(ret.RET_ERROR)
		l4g.Errorf("%s", err.Error())
		return
	}
	m.Params = nil
	srv.UserM().CreateGlobalMail(srv, m)
}

// GM删除全服邮件
func GMDeleteServerMail(srv servicer, sm *gm.DelServerMail, result *gm.Result) {
	l4g.Debugf("GMDeleteServerMail: %+v", sm)
	mailBox := srv.UserM().MailBox()
	deleteMailId := make([]uint64, 0, 1)
	for _, mail := range mailBox.GetAll() {
		if mail == nil {
			continue
		}
		if mail.GmId == sm.GmId {
			mail.GmDel = true
			deleteMailId = append(deleteMailId, mail.GetId())
			mailBox.AddGmDel(mail)
			mailBox.Change([]*cl.Mail{mail})
			break
		}
	}
	um := srv.UserM()
	um.Range(func(u *User) { u.MailBox().Delete(deleteMailId) })
	//修改离线玩家数据
	um.Cache().Range(func(u *User) { u.MailBox().Delete(deleteMailId) })
}

// 奖励邮件
func bagFullMail(srv servicer, user *User, awards []*cl.Resource, reason uint32) uint64 {
	id := srv.CreateUniqueID()
	mailSysID := GetMailIDByResourceReason(reason)
	NormalMail(user.MailBox(), mailSysID, reason, id, []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(true)
	return id
}

// 竞技场邮件
func ArenaMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 公会邮件
func GuildMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 公会副本邮件
func GuildDungeonMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// MonthlyCardMail : 月卡邮件
func MonthlyCardMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 活跃战令邮件
func PassActiveMail(srv servicer, mailID uint32, awards []*cl.Resource, user *User, genTime int64) {
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, genTime)
	user.SendNewMailTipToClient(false)
}

// 神树争霸-奖励邮件
func WrestleMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("WrestleMail: awards or users is nil. mailID:%d", mailID)
		return
	}
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// DivineDemonMail : 神魔抽卡邮件
func DivineDemonMail(srv servicer, mailID uint32, awards []*cl.Resource, user *User) {
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// 密林 - 据点模式 - 奖励邮件
func FlowerOccupyMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func DailyWishMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 神器首发，奖励补发邮件
func ArtifactDebutMail(srv servicer, awards []*cl.Resource, user *User) {
	id := srv.CreateUniqueID()
	NormalMail(user.MailBox(), MailIDArtifactDebut, 0, id, []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// 轮次活动，奖励补发邮件
func RoundActivityMail(srv servicer, mailID uint32, awards []*cl.Resource, user *User) {
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// 百塔，奖励补发邮件
func TowerSeasonMail(srv servicer, awards []*cl.Resource, user *User, genTime int64) {
	NormalMail(user.MailBox(), MailIDTowerSeason, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, genTime)
	user.SendNewMailTipToClient(false)
}

// 777抽活动开启邮件
func GodPresentMail(srv servicer, user *User, mailID uint32) {
	awards := goxml.GetData().GodPresentConfigInfoM.GetMailReward()
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// WorldBossSettleMail worldBoss结算邮件
func WorldBossSettleMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, uid uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	u := srv.UserM().GetUserWithCache(uid)
	if u != nil {
		u.MailBox().Add(m.Clone())
		u.SendNewMailTipToClient(false)
	} else {
		msg.Users = append(msg.Users, uid)
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 百塔邮件
func TowerSeasonRankMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	now := time.Now().Unix()
	seasonInfo := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(now)
	var genTime int64
	if seasonInfo != nil {
		genTime = seasonInfo.StartTm
	}
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, genTime)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 活动故事 更换活动自动兑换
func ActivityStoryMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func DropActivityMail(srv servicer, user *User, awards []*cl.Resource, mailID uint32) {
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// 巅峰竞技场-奖励邮件
func PeakMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("PeakMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 充值代金券-奖励邮件（处理官网充值代金券及代金券返利）
func CouponRechargeMail(srv servicer, mailID, reason uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("CouponRechargeMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, reason, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 仪式回收-奖励邮件
func RiteRecycleMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// 公会战排名奖励
func GSTRankMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("GSTRankMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func GSTLeaderMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("GSTLeaderMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 赛季羁绊回收-奖励邮件
func SeasonLinkRecycleMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func GSTFirstOccupyMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("GSTFirstOccupyMails: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func SeasonArenaDivisionMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("SeasonArenaDivisionMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func SeasonArenaDivisionCompensateMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("SeasonArenaDivisionCompensateMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func SeasonArenaRankMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("SeasonArenaRankMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func DailyAttendanceMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func RemainSeasonRestMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func ActivityTurnTableResetMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func ActivitySumPuzzleResetMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// 公会战龙战结算奖励
func GSTDragonSettlementMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users map[uint64]util.None) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("GSTDragonSettlementMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func GSTDragonTaskMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("GSTDragonSettlementMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 公会竞赛邮件
func GuildMobMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 天赋树 赛季回馈
func TalentTreeRecycle(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{user.Name()}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func ActivityComplianceMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("ActivityComplianceMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func ActivityComplianceReparationMail(srv servicer, user *User, awards []*cl.Resource, mailId uint32) {
	NormalMail(user.MailBox(), mailId, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func SocietyMail(srv servicer, user *User) {
	awrads := []*cl.Resource{
		{
			Type:  uint32(common.RESOURCE_DIAMOND),
			Count: goxml.GetData().ConfigInfoM.GetSocietyMailDiamondAward(),
		},
	}
	NormalMail(user.MailBox(), uint32(MailIDSociety), 0, srv.CreateUniqueID(), []string{}, awrads, 0)
	user.SendNewMailTipToClient(false)
}

// 地宫冲榜排行奖励邮件
func ActivityTowerRankAwardMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("ActivityTowerRankAwardMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 幻境冲榜排行奖励邮件
func ActivityMirageRankAwardMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("ActivityMirageRankAwardMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 百塔冲榜排行奖励邮件
func ActivityTowerSeasonRankAwardMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("ActivityTowerSeasonRankAwardMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func GSTChallengeSettlementMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("GSTChallengeSettlementMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 问卷邮件
func QuestionnaireMail(srv servicer, awards []*cl.Resource, uid uint64) {
	m := mail.NewMail(MailIDQuestionnaire, srv.CreateUniqueID(), nil, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	u := srv.UserM().GetUserWithCache(uid)
	if u != nil {
		u.MailBox().Add(m.Clone())
		u.SendNewMailTipToClient(false)
	} else {
		msg.Users = append(msg.Users, uid)
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

// 赛季冲榜
func SeasonComplianceMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, users []uint64) {
	if len(users) == 0 || len(awards) == 0 {
		l4g.Errorf("SeasonComplianceMail: param err, mailID:%d, userCount:%d, awardCount:%d",
			mailID, len(users), len(awards))
		return
	}

	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	for _, id := range users {
		u := srv.UserM().GetUserWithCache(id)
		if u != nil {
			u.MailBox().Add(m.Clone())
			u.SendNewMailTipToClient(false)
		} else {
			msg.Users = append(msg.Users, id)
		}
	}
	if len(msg.Users) != 0 {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func SeasonComplianceScoreMail(srv servicer, user *User, awards []*cl.Resource, mailID uint32) {
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

// 赛季装备回收-奖励邮件
func SeasonJewelryRecycleMail(srv servicer, uid uint64, awards []*cl.Resource, mailId uint32) {
	m := mail.NewMail(mailId, srv.CreateUniqueID(), nil, awards, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	u := srv.UserM().GetUserWithCache(uid)
	if u != nil {
		u.MailBox().Add(m.Clone())
		u.SendNewMailTipToClient(false)
	} else {
		msg.Users = append(msg.Users, uid)
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func RebaseMail(srv servicer, user *User, awards []*cl.Resource, mailID, recharge uint32) {
	var couponCount uint32
	var diamondCount uint32
	for _, res := range awards {
		if res.Type == uint32(common.RESOURCE_DIAMOND) {
			diamondCount = res.Count
		} else if res.Type == uint32(common.RESOURCE_COUPON) {
			couponCount = res.Count
		}
	}
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(),
		[]string{strconv.FormatUint(uint64(recharge), 10),
			strconv.FormatUint(uint64(couponCount), 10),
			strconv.FormatUint(uint64(diamondCount), 10)}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func ActivityWebMail(srv servicer, user *User, awards []*cl.Resource, mailID uint32) {
	NormalMail(user.MailBox(), mailID, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func ActivityMirageCompliance(srv servicer, user *User, awards []*cl.Resource) {
	NormalMail(user.MailBox(), MailIDMirageCompliance, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func ActivityTowerCompliance(srv servicer, user *User, awards []*cl.Resource) {
	NormalMail(user.MailBox(), MailIDTowerCompliance, 0, srv.CreateUniqueID(), []string{}, awards, 0)
	user.SendNewMailTipToClient(false)
}

func GuildMobilizationTaskBeCancel(srv servicer, uid uint64) {
	m := mail.NewMail(MailIDGuildMobTaskBeCancel, srv.CreateUniqueID(), nil, nil, mail.TypeNormal, 0, 0)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	u := srv.UserM().GetUserWithCache(uid)
	if u != nil {
		u.MailBox().Add(m.Clone())
		u.SendNewMailTipToClient(false)
	} else {
		msg.Users = append(msg.Users, uid)
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func TowerPokemonRankMail(srv servicer, mailID uint32, params []string, awards []*cl.Resource, uid uint64, seasonInfo *goxml.SeasonInfoExt) {
	m := mail.NewMail(mailID, srv.CreateUniqueID(), params, awards, mail.TypeNormal, 0, seasonInfo.StartTm)
	msg := &r2l.L2R_GroupMail{
		Data: m,
	}
	u := srv.UserM().GetUserWithCache(uid)
	if u != nil {
		u.MailBox().Add(m.Clone())
		u.SendNewMailTipToClient(false)
	} else {
		msg.Users = append(msg.Users, uid)
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GroupMail), 0, msg)
	}
}

func TowerPokemonTaskMail(srv servicer, awards []*cl.Resource, user *User, genTime int64) {
	NormalMail(user.MailBox(), MailIDTowerPokemonTask, 0, srv.CreateUniqueID(), []string{}, awards, genTime)
	user.SendNewMailTipToClient(false)
}
