package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/math/rand"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type Dispatch struct {
	owner *User
	data  *cl.Dispatch
	Hides map[uint64]struct{} // 缓存被派遣的英雄id，作用：便于判断英雄是否被重复派遣
}

func newDispatch(user *User) *Dispatch {
	return &Dispatch{
		owner: user,
		Hides: make(map[uint64]struct{}),
	}
}

func (d *Dispatch) Owner() *User {
	return d.owner
}

// 加载数据
func (d *Dispatch) load(data *cl.Dispatch) {
	d.data = data
}

func (d *Dispatch) InitDispatch() {
	d.data = &cl.Dispatch{
		Tasks: make([]*cl.DispatchTask, 0, 3), //nolint:mnd
	}
	d.initDefaultTask()
	d.InitDispatchLevel()
	d.InitTaskCD()
	d.Save()
}

// 加载悬赏默认的三条初始任务：默认的悬赏任务ID，配置在config_info.xml
func (d *Dispatch) initDefaultTask() {
	for _, id := range goxml.GetData().ConfigInfoM.DispatchInitTask {
		d.data.Tasks = append(d.data.Tasks, &cl.DispatchTask{
			Id:    uint64(id),
			SysId: id,
		})
	}
}

func (d *Dispatch) InitDispatchLevel() *cl.DispatchLevelInfo {
	d.data.LevelInfo = &cl.DispatchLevelInfo{
		DispatchLevel: 1,
		TaskProgress:  make(map[uint32]*cl.TaskTypeProgress),
	}
	dungeonTaskType := common.TASK_TYPE_ID_TTYPE_ID_MAIN_DUNGEON_FLOOR
	dungeonID := d.owner.Dungeon().GetDungeonID()
	if dungeonID == 0 {
		dungeonID = goxml.GetData().DungeonConfigInfoM.FirstId
	}
	progress := &cl.TaskTypeProgress{
		TaskTypeId: uint32(dungeonTaskType),
		Progress:   uint64(dungeonID),
	}
	d.data.LevelInfo.TaskProgress[uint32(dungeonTaskType)] = progress
	d.NotifyClient()
	d.Save()
	return d.data.LevelInfo
}

func (d *Dispatch) GetTaskCD() *cl.DispatchCD {
	if d.data == nil {
		return nil
	}

	return d.data.TaskCd
}

func (d *Dispatch) InitTaskCD() {
	if d.data.TaskCd == nil {
		d.data.TaskCd = &cl.DispatchCD{
			StartTime: time.Now().Unix(),
		}
	}
	if d.data.TaskCd.StartTime == 0 {
		d.data.TaskCd.StartTime = time.Now().Unix()
	}
	d.Save()
}

// 添加悬赏任务
func (d *Dispatch) addDispatchTask(srv servicer, ids []uint32) {
	if d.data != nil {
		for _, id := range ids {
			task := &cl.DispatchTask{
				Id:    srv.CreateUniqueID(),
				SysId: id,
			}
			d.data.Tasks = append(d.data.Tasks, task)
		}
		d.Save()
	}
}

// Save ：设置保存标签, 保存数据
func (d *Dispatch) Save() {
	if d.Owner().dbUser.Module.Dispatch == nil {
		d.Owner().dbUser.Module.Dispatch = d.data
	}
	d.Owner().setSaveTag(saveTagModule)
}

func (d *Dispatch) Flush() []*cl.DispatchTask {
	if d.data != nil {
		tasks := make([]*cl.DispatchTask, 0, len(d.data.Tasks))
		for _, task := range d.data.Tasks {
			tasks = append(tasks, task.Clone())
		}

		return tasks
	}

	return nil
}

func (d *Dispatch) FlushLevelInfo() *cl.DispatchLevelInfo {
	if d.data == nil {
		return nil
	}
	return d.data.LevelInfo.Clone()
}

func (d *Dispatch) FlushTaskCD() *cl.DispatchCD {
	if d.data == nil {
		return nil
	}

	return d.data.TaskCd.Clone()
}

// GetDispatch ：获取悬赏信息
func (d *Dispatch) GetDispatch() *cl.Dispatch {
	return d.data
}

// GetDispatchTask ：获取悬赏任务
func (d *Dispatch) GetDispatchTask(id uint64) *cl.DispatchTask {
	if d.data != nil {
		for _, task := range d.data.Tasks {
			if task.Id == id {
				return task
			}
		}
	}

	return nil
}

func (d *Dispatch) IsHeroRepeat(heroID []uint64) bool {
	repeatMap := make(map[uint64]struct{})
	for _, id := range heroID {
		if _, exist := repeatMap[id]; exist {
			l4g.Errorf("user: %d receive dispatch task error: hero id is repeat. heroID:%d", d.owner.ID(), id)
			return false
		}
		repeatMap[id] = struct{}{}

		if _, exist := d.Hides[id]; exist {
			return false
		}
	}

	return true
}

// Delete ：删除领取过奖励的悬赏任务
func (d *Dispatch) Delete(ids map[uint64]struct{}) {
	if d.data != nil {
		// 被删除任务里的英雄id
		var hides []uint64
		for i := 0; i < len(d.data.Tasks); i++ {
			if _, exist := ids[d.data.Tasks[i].Id]; exist {
				hides = append(hides, d.data.Tasks[i].HeroIds...)
				d.data.Tasks = append(d.data.Tasks[:i], d.data.Tasks[i+1:]...)
				i--
			}
		}
		// 删除缓存的英雄id
		for _, id := range hides {
			delete(d.Hides, id)
		}
		d.Save()
	}
}

// 删除未接取的任务
func (d *Dispatch) delNoReceiveTask() {
	for i := 0; i < len(d.data.Tasks); i++ {
		if d.data.Tasks[i].Time == 0 {
			d.data.Tasks = append(d.data.Tasks[:i], d.data.Tasks[i+1:]...)
			i--
		}
	}
}

func (d *Dispatch) Refresh(srv servicer) ([]*cl.DispatchTask, []uint32) {
	if d.data == nil {
		l4g.Errorf("user: %d Dispatch.Refresh: dispatch is not init. ", d.owner.ID())
		return nil, nil
	}
	d.delNoReceiveTask()
	taskIDs := make([]uint32, 0, goxml.GetData().ConfigInfoM.DispatchNewTaskNum)
	// 首次刷新：必出紫色任务
	// 改表后每一组不一定会有紫色悬赏任务故改为当前组的最高品质
	if !d.data.FirstRefresh {
		purpleTaskID := d.RandMaxQualityTaskWithGroup(srv)
		if purpleTaskID == 0 {
			l4g.Errorf("user: %d Dispatch.Refresh: taskID is zero. ", d.owner.ID())
			return nil, nil
		}
		taskIDs = append(taskIDs, purpleTaskID)
		d.data.FirstRefresh = true
	}
	// 月卡特权
	taskIDs = append(taskIDs, d.genTaskByMonthlyCard(srv)...)
	if goxml.GetData().ConfigInfoM.DispatchNewTaskNum < uint32(len(taskIDs)) {
		l4g.Errorf("user: %d Dispatch.Refresh: num is invalid. ", d.owner.ID())
		return nil, nil
	}

	genTaskNum := goxml.GetData().ConfigInfoM.DispatchNewTaskNum - uint32(len(taskIDs))
	// 生成悬赏任务id
	subtaskIDs, count := d.generate(srv, d.data.FloorsCount, genTaskNum)
	if len(subtaskIDs) < int(genTaskNum) {
		l4g.Errorf("user: %d Dispatch.Refresh: refresh task failed. ", d.owner.ID())
		return nil, nil
	}

	// 更新红色品质的保底累计次数
	d.data.FloorsCount = count
	taskIDs = append(taskIDs, subtaskIDs...)

	d.addDispatchTask(srv, taskIDs)
	return d.Flush(), taskIDs
}

// 优化为获取当前组最高品质的任务
func (d *Dispatch) genTaskByMonthlyCard(srv servicer) (taskIDs []uint32) {
	monthlyCard := d.owner.MonthlyCard()
	refreshCount := monthlyCard.GetRefreshCount() + 1
	if monthlyCard.RefreshMaxQuality(refreshCount) {
		taskID := d.RandMaxQualityTaskWithGroup(srv)
		if taskID == 0 {
			l4g.Errorf("user: %d genTaskByMonthlyCard: taskID is zero. ", d.owner.ID())
			return nil
		}
		taskIDs = append(taskIDs, taskID)
	}
	return
}

func (d *Dispatch) CheckHeroData(heroID []uint64, dispatchInfo *goxml.DispatchInfoExt) uint32 {
	starExist := false
	raceNum, linkNum := 0, 0
	racePool, linkPool := d.DeepCopy(dispatchInfo.RacePool), d.DeepCopy(dispatchInfo.LinkPool)

	heroM := d.owner.HeroManager()
	for _, id := range heroID {
		hero := heroM.Get(id)
		if hero == nil {
			l4g.Errorf("user: %d Dispatch.CheckHeroesStarAndRace: hero is not exist. heroID: %d ", d.owner.ID(), id)
			return uint32(cret.RET_HERO_NOT_EXIST)
		}
		// 检查英雄星数
		heroData := hero.GetData()
		if heroData.Star >= dispatchInfo.StarGrade {
			starExist = true
		}

		heroInfo := goxml.GetData().HeroInfoM.Index(heroData.SysId)
		if heroInfo == nil {
			l4g.Errorf("user: %d Dispatch: heroInfo is nil. heroID: %d ", d.owner.ID(), hero.GetHeroSysID())
			return uint32(cret.RET_SYSTEM_DATA_ERROR)
		}

		for race := range racePool {
			if heroInfo.Race == race {
				raceNum++
				delete(racePool, race) // 预防一个英雄匹配2次种族
			}
		}

		for link := range linkPool {
			if heroInfo.Link1ID == link {
				linkNum++
				delete(linkPool, link) // 预防一个英雄匹配2次羁绊
			}
		}
	}
	// 英雄星数不匹配
	if !starExist {
		return uint32(cret.RET_DISPATCH_HERO_STAR_NOT_MATCH)
	}

	// 检查种族和羁绊是或的关系
	if dispatchInfo.HeroConditionType == goxml.DispatchHeroConditionTypeRace {
		if raceNum < len(racePool) {
			return uint32(cret.RET_DISPATCH_HERO_RACE_NOT_MATCH)
		}
	} else {
		if linkNum < len(linkPool) {
			return uint32(cret.RET_DISPATCH_HERO_LINK_NOT_MATCH)
		}
	}

	return uint32(cret.RET_OK)
}

// DeepCopy ：深拷贝
func (d *Dispatch) DeepCopy(race map[uint32]struct{}) map[uint32]struct{} {
	pool := make(map[uint32]struct{}, len(race))
	for k := range race {
		pool[k] = struct{}{}
	}
	return pool
}

// SliceToMap ：slice 转 map
func (d *Dispatch) SliceToMap(ids []uint64) map[uint64]struct{} {
	idMap := make(map[uint64]struct{}, len(ids))
	for _, id := range ids {
		idMap[id] = struct{}{}
	}
	return idMap
}

// SetHidesCache ：缓存英雄id
func (d *Dispatch) SetHidesCache(ids []uint64) {
	for _, id := range ids {
		d.Hides[id] = struct{}{}
	}
	d.Save()
}

// GetTaskNum ：获取所有任务的数量
func (d *Dispatch) GetTaskNum() int {
	if d.data != nil {
		return len(d.data.Tasks)
	}
	return 0
}

// IsHaveAwardReceive : 是否有可领取奖励的任务
func (d *Dispatch) IsHaveAwardReceive() bool {
	if d.data == nil {
		return false
	}
	for _, task := range d.data.Tasks {
		dispatchInfo := goxml.GetData().DispatchInfoM.Index(task.SysId)
		if dispatchInfo == nil {
			l4g.Debugf("user: %d Dispatch.IsHaveAwardReceive: dispatchInfo is nil. task sysID: %d",
				d.owner.ID(), task.SysId)
			continue
		}
		if task.Time > 0 && task.Time+int64(dispatchInfo.Time) <= time.Now().Unix() { // 存在可以领奖的任务
			return true
		}
	}

	return false
}

// 客户端上线后通过ID_MSG_C2L_Flush获取数据
// 获取到数据后不会通过ID_MSG_C2L_DispatchTasks获取数据
// 故改为在这里初始化
func (d *Dispatch) SendDispatchToClient() {
	msg := &cl.L2C_DispatchTasks{
		Ret: uint32(cret.RET_OK),
	}

	dispatchM := d.owner.Dispatch()
	if dispatchM.GetDispatch() == nil {
		dispatchM.InitDispatch()
	}
	if dispatchM.GetDispatchLevelInfo() == nil {
		dispatchM.InitDispatchLevel()
	}
	if dispatchM.GetTaskCD() == nil {
		dispatchM.InitTaskCD()
	}

	msg.Tasks = d.Flush()
	msg.LevelInfo = d.FlushLevelInfo()
	msg.TaskCd = d.FlushTaskCD()
	d.owner.SendCmdToGateway(cl.ID_MSG_L2C_DispatchTasks, msg)
}

func (d *Dispatch) GetDispatchLevelInfo() *cl.DispatchLevelInfo {
	return d.data.LevelInfo
}

func (d *Dispatch) GetDispatchLevel() uint32 {
	if d.data.LevelInfo == nil {
		return 0
	}
	return d.data.LevelInfo.DispatchLevel
}

func (d *Dispatch) OnDispatchEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	if d.data == nil {
		d.InitDispatch()
	}

	disLevelInfo := d.GetDispatchLevelInfo()
	if disLevelInfo == nil {
		disLevelInfo = d.InitDispatchLevel()
	}

	_, change := d.owner.TaskTypeOnEvent(disLevelInfo.TaskProgress, event, progress, values)
	if !change {
		return
	}
	d.Save()

	if d.levelUpConditionCheck() && d.LevelUp() {
		d.owner.LogDispatchLevelUpdate(srv, d.GetDispatchLevel())
	}
	d.Save()

	d.NotifyClient()
}

func (d *Dispatch) levelUpConditionCheck() bool {
	levelInfo := goxml.GetData().DispatchLevelInfoM.Index(d.data.LevelInfo.DispatchLevel)
	if levelInfo == nil {
		return false
	}
	for _, v := range levelInfo.TaskInfos {
		if !d.owner.CheckTaskFinish(d.data.LevelInfo.TaskProgress[v.TaskType], v.TaskType, uint64(v.Value)) {
			return false
		}
	}

	return true
}

func (d *Dispatch) LevelUp() bool {
	nextLevel := d.data.LevelInfo.DispatchLevel + 1
	levelInfo := goxml.GetData().DispatchLevelInfoM.Index(d.data.LevelInfo.DispatchLevel)
	if levelInfo == nil {
		return false
	}
	nextLevelInfo := goxml.GetData().DispatchLevelInfoM.Index(nextLevel)
	if nextLevelInfo == nil {
		return false
	}

	newProgress := make(map[uint32]*cl.TaskTypeProgress)
	for _, v := range levelInfo.TaskInfos {
		recordType, exist := goxml.DispatchTaskRecordType[v.TaskType]
		if !exist {
			continue
		}
		if recordType == goxml.DispatchLevelUpKeep {
			newProgress[v.TaskType] = d.data.LevelInfo.TaskProgress[v.TaskType]
		}
	}
	d.data.LevelInfo.TaskProgress = newProgress
	d.data.LevelInfo.DispatchLevel = nextLevel
	return true
}

func (d *Dispatch) NotifyClient() {
	d.owner.SendCmdToGateway(cl.ID_MSG_L2C_DispatchLevelUpdate, &cl.L2C_DispatchLevelUpdate{
		Ret:       uint32(cret.RET_OK),
		LevelInfo: d.GetDispatchLevelInfo().Clone(),
	})
}

func (d *Dispatch) IncreaseAward(task *cl.DispatchTask, award []*cl.Resource) []*cl.Resource {
	retAward := make([]*cl.Resource, 0, len(award))
	var level uint32
	level = task.DispatchLevel
	if task.DispatchLevel == 0 {
		level = 1
	}

	levelInfo := goxml.GetData().DispatchLevelInfoM.Index(level)
	if levelInfo == nil {
		return award
	}

	if levelInfo.RewardUp > 0 {
		for _, v := range award {
			isZero, IncreaseCount := util.DivFloor(goxml.BaseInt64, int64(v.Count), int64(levelInfo.RewardUp))
			if isZero {
				IncreaseCount = int64(v.Count)
			}
			tmp := v.Clone()
			tmp.Count = uint32(IncreaseCount)
			retAward = append(retAward, tmp)
		}
	}
	return retAward
}

// UpdateTaskCDData : 更新任务 CD 数据
func (d *Dispatch) UpdateTaskCDData(tasks []uint32) {
	isSave := false
	for _, id := range tasks {
		info := goxml.GetData().DispatchInfoM.Index(id)
		if info == nil {
			continue
		}
		if info.Cd > 0 && info.Maximum > 0 {
			isChange, newRound := d.isChangeCDRound(info.Id, info.Cd)
			if isChange {
				d.updateTaskCDMaxCount(info.Id, true)
			} else {
				d.updateTaskCDMaxCount(info.Id, false)
			}
			d.updateTaskCDRound(info.Id, newRound)
			isSave = true
		}
	}
	if isSave {
		d.Save()
	}
}

func (d *Dispatch) isChangeCDRound(taskID, cd uint32) (bool, uint32) {
	newRound := d.calcCDRound(int64(cd))
	oldRound := d.data.TaskCd.Round[taskID]
	if newRound != oldRound {
		return true, newRound
	}

	return false, newRound
}

func (d *Dispatch) updateTaskCDRound(taskID, newRound uint32) {
	if d.data.TaskCd.Round == nil {
		d.data.TaskCd.Round = make(map[uint32]uint32)
	}
	if newRound != d.data.TaskCd.Round[taskID] {
		d.data.TaskCd.Round[taskID] = newRound
	}
}

func (d *Dispatch) updateTaskCDMaxCount(taskID uint32, isChangeRound bool) {
	if d.data.TaskCd.MaxCount == nil {
		d.data.TaskCd.MaxCount = make(map[uint32]uint32)
	}
	if isChangeRound {
		d.data.TaskCd.MaxCount[taskID] = 1
	} else {
		d.data.TaskCd.MaxCount[taskID] += 1
	}
}

func (d *Dispatch) getTaskCDStartTime() int64 {
	if d.data == nil || d.data.TaskCd == nil {
		return 0
	}
	return d.data.TaskCd.StartTime
}

func (d *Dispatch) calcCDRound(cd int64) uint32 {
	interval := time.Now().Unix() - d.getTaskCDStartTime()
	return uint32(interval / cd)
}

func (d *Dispatch) getTaskCDMaxCount(taskID, cd uint32, tmpMaxCount map[uint32]uint32) uint32 {
	isChange, _ := d.isChangeCDRound(taskID, cd)
	if isChange {
		return 0
	}

	return tmpMaxCount[taskID]
}

func (d *Dispatch) RandMaxQualityTaskWithGroup(srv servicer) uint32 {
	level := d.GetDispatchLevel()
	levelInfo := goxml.GetData().DispatchLevelInfoM.Index(level)
	if levelInfo == nil {
		l4g.Error("user:%d RandMaxQualityTaskWithGroup: levelInfo is nil. level: %d", d.owner.ID(), level)
		return 0
	}
	dInfoM := goxml.GetData().DispatchInfoM
	maxQuality, exist := dInfoM.GroupMaxQuality[levelInfo.Group]
	if !exist || maxQuality == 0 {
		l4g.Error("user:%d RandMaxQualityTaskWithGroup: maxQuality is invalid. level:%d group:%d ", d.owner.ID(), level, levelInfo.Group)
		return 0
	}

	groupQuality, exist := dInfoM.TaskGroupQualityPool[levelInfo.Group]
	if !exist {
		l4g.Error("user:%d RandMaxQualityTaskWithGroup: group quality is nil. level:%d group:%d", d.owner.ID(), level, levelInfo.Group)
		return 0
	}
	taskIDs, exist := groupQuality[maxQuality]
	if !exist || len(taskIDs) == 0 {
		l4g.Error("user:%d RandMaxQualityTaskWithGroup: group quality is nil. level:%d group:%d quality:%d ", d.owner.ID(), level, levelInfo.Group, maxQuality)
		return 0
	}
	tmpMaxCount := d.flushAllCDMaxCount()
	d.delLimitWithTaskIDs(taskIDs, tmpMaxCount)

	return d.randomID(srv.Rand(), taskIDs)
}

// 返回没有被 CD 最大次数限制的任务 id
func (d *Dispatch) delLimitWithTaskIDs(taskIDs []uint32, tmpMaxCount map[uint32]uint32) []uint32 {
	realTaskIDs := make([]uint32, 0, len(taskIDs))
	for _, id := range taskIDs {
		data := goxml.GetData().DispatchInfoM.Index(id)
		if data == nil {
			continue
		}
		if data.Cd > 0 && data.Maximum > 0 {
			newCount := d.getTaskCDMaxCount(data.Id, data.Cd, tmpMaxCount) + 1
			if newCount <= data.Maximum {
				realTaskIDs = append(realTaskIDs, data.Id)
				tmpMaxCount[data.Id] += 1
			}
		} else {
			realTaskIDs = append(realTaskIDs, data.Id)
		}
	}

	return realTaskIDs
}

func (d *Dispatch) randomID(rand *rand.Rand, taskIDs []uint32) uint32 {
	if len(taskIDs) == 0 {
		return 0
	}

	index := rand.Intn(len(taskIDs))
	return taskIDs[index]
}

// Generate : 生成悬赏任务: 根据weight，随机生成X条, 不需要去重
// param@  count: 刷新任务的累计条数，当 count == m.xmlData.ConfigInfoM.DispatchForceRedCounts时，必出红色品质的任务
// 改表后每一组不一定会有红色悬赏任务故改为当前组的最高品质
func (d *Dispatch) generate(srv servicer, count, genTaskNum uint32) ([]uint32, uint32) {
	group := d.getGroupByLevel()
	if group == uint32(0) {
		l4g.Error("user:%d generate: task group is zero. level:%d", d.owner.ID(), d.GetDispatchLevel())
		return nil, count
	}
	dIndoM := goxml.GetData().DispatchInfoM
	if _, exist := dIndoM.TotalWeight[group]; !exist || dIndoM.TotalWeight[group] == 0 {
		l4g.Error("user:%d generate: totalWeight is zero. group: %d", d.owner.ID(), group)
		return nil, count
	}
	tasks := make([]uint32, 0, genTaskNum)
	tmpMaxCount := d.flushAllCDMaxCount()
	for i := 0; i < int(genTaskNum); i++ {
		taskId := d.randomTaskID(srv.Rand(), dIndoM.TotalWeight[group], dIndoM.RandomPool[group], tmpMaxCount)
		taskInfo := dIndoM.Index(taskId)
		if taskInfo == nil {
			l4g.Error("user:%d generate: taskInfo not exist. taskID: %d", d.owner.ID(), taskId)
			return nil, 0
		}
		count++
		// 触发保底出红品质任务的机制：随机一条红品质任务
		if taskInfo.Quality != uint32(common.QUALITY_RED) && count == goxml.GetData().ConfigInfoM.DispatchForceRedCounts {
			taskId = d.RandMaxQualityTaskWithGroup(srv)
			if taskId == 0 {
				l4g.Error("user:%d generate: taskID is zero. taskID: %d", d.owner.ID(), taskId)
				return nil, 0
			}
			count = 0 // 每次随机抽取到或触发保底出红品质任务后，重新清零计算保底累积
		} else if taskInfo.Quality == uint32(common.QUALITY_RED) {
			count = 0 // 每次随机抽取到或触发保底出红品质任务后，重新清零计算保底累积
		}
		tasks = append(tasks, taskId)
	}

	return tasks, count
}

// 加权随机
func (d *Dispatch) randomTaskID(rand *rand.Rand, totalWeight uint32, pool []*goxml.DispatchInfoExt, tmpMaxCount map[uint32]uint32) (taskId uint32) {
	totalWeight, pool = d.delLimitWithTaskPool(totalWeight, pool, tmpMaxCount)
	if totalWeight == uint32(0) {
		l4g.Errorf("user: %d randomTaskID: random generate taskID failed. totalWeight is zero", d.owner.ID())
		return
	}

	randNum := rand.Intn(int(totalWeight))
	var curTotal uint32
	for _, v := range pool {
		curTotal += v.Weight
		if uint32(randNum) < curTotal {
			taskId = v.Id
			break
		}
	}

	return
}

// 返回没有被 CD 最大次数限制的任务 id
func (d *Dispatch) delLimitWithTaskPool(totalWeight uint32, pool []*goxml.DispatchInfoExt, tmpMaxCount map[uint32]uint32) (uint32, []*goxml.DispatchInfoExt) {
	var realTotalWeight uint32
	realTotalWeight = totalWeight
	realTasks := make([]*goxml.DispatchInfoExt, 0, len(pool))
	for _, v := range pool { //nolint:varnamelen
		data := goxml.GetData().DispatchInfoM.Index(v.Id)
		if data == nil {
			continue
		}
		if data.Cd > 0 && data.Maximum > 0 {
			newCount := d.getTaskCDMaxCount(data.Id, data.Cd, tmpMaxCount) + 1
			if newCount <= data.Maximum {
				realTasks = append(realTasks, v)
				tmpMaxCount[data.Id] += 1
			} else {
				realTotalWeight -= data.Weight
			}
		} else {
			realTasks = append(realTasks, v)
		}
	}

	return realTotalWeight, realTasks
}

func (d *Dispatch) getGroupByLevel() uint32 {
	level := d.GetDispatchLevel()
	info := goxml.GetData().DispatchLevelInfoM.Index(level)
	if info == nil {
		return 0
	}

	return info.Group
}

func (d *Dispatch) FlushCDMaxCount(tasks []uint32) map[uint32]uint32 {
	retMaxCount := make(map[uint32]uint32, len(tasks))
	for _, id := range tasks {
		count, exist := d.data.TaskCd.MaxCount[id]
		if exist {
			retMaxCount[id] = count
		}
	}

	return retMaxCount
}

func (d *Dispatch) flushAllCDMaxCount() map[uint32]uint32 {
	retMaxCount := make(map[uint32]uint32, len(d.data.TaskCd.MaxCount))
	for k, v := range d.data.TaskCd.MaxCount {
		retMaxCount[k] = v
	}

	return retMaxCount
}
