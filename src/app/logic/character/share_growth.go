package character

import (
	"app/goxml"
	"app/logic/event"
	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
)

type ShareGrowth struct {
	owner *User
	data  *cl.ShareGrowth
}

func newShareGrowth(user *User) *ShareGrowth {
	return &ShareGrowth{
		owner: user,
	}
}

func (s *ShareGrowth) Owner() *User {
	return s.owner
}

func (s *ShareGrowth) init() *cl.ShareGrowth {
	return &cl.ShareGrowth{}
}

// 加载数据
func (s *ShareGrowth) load(data *cl.ShareGrowth) {
	s.data = data
	if s.data == nil {
		s.data = s.init()
	}
}

func (s *ShareGrowth) getShareAttr() *cl.ShareGrowth {
	return s.data
}

func (s *ShareGrowth) SetShareAttr(attr *cl.ShareGrowth) {
	s.data = attr
}

func (s *ShareGrowth) getShareHeroAttr() *cl.ShareHero {
	attr := s.getShareAttr()
	if attr != nil {
		return attr.Hero
	}
	return nil
}

func (s *ShareGrowth) getShareHeroLevel() uint32 {
	shareHeroAttr := s.getShareHeroAttr()
	if shareHeroAttr != nil {
		return shareHeroAttr.Level
	}
	return 0
}

func (s *ShareGrowth) getShareHeroStage() uint32 {
	shareHeroAttr := s.getShareHeroAttr()
	if shareHeroAttr != nil {
		return shareHeroAttr.Stage
	}
	return 0
}

func (s *ShareGrowth) setShareHeroAttr(attr *cl.ShareHero) {
	s.data.Hero = attr
}

func (s *ShareGrowth) getShareEquipmentAttr() map[uint32]*cl.ShareEquipment {
	attr := s.getShareAttr()
	if attr != nil {
		return attr.Equipment
	}
	return nil
}

func (s *ShareGrowth) setShareEquipmentAttr(attrs map[uint32]*cl.ShareEquipment) {
	s.data.Equipment = attrs
}

func (s *ShareGrowth) save() {
	s.owner.dbUser.ModuleGlobalAttr.ShareGrowth = s.data
	s.owner.setSaveTag(saveTagModuleGlobalAttr)
}

func (s *ShareGrowth) Flush() *cl.ShareGrowth {
	return s.data.Clone()
}

// 获取共享养成数据
// @return *cl.ShareGrowth
func (s *ShareGrowth) GetShareAttr() *cl.ShareGrowth {
	return s.getShareAttr()
}

// 获取英雄共享等级
// @return uint32
func (s *ShareGrowth) GetShareHeroLevel() uint32 {
	return s.getShareHeroLevel()
}

// 获取英雄共享突破等级
// @return uint32
func (s *ShareGrowth) GetShareHeroStage() uint32 {
	return s.getShareHeroStage()
}

// 检查更新共享养成属性
// @param servicer srv
// @param common.SHARE_GROWTH_TYPE typ 养成类型
// @param uint64 hid 英雄id
func (s *ShareGrowth) CheckUpdateShareAttr(srv servicer, typ common.SHARE_GROWTH_TYPE, hid uint64) {
	hm := s.Owner().HeroManager()
	if !hm.UnderContracting() {
		return
	}

	hero := hm.Get(hid)
	if hero == nil {
		l4g.Errorf("user: %d CheckUpdateShareAttr: equipment is nil, hid:%d", s.Owner().ID(), hid)
		return
	}
	if !hero.IsTagContract() {
		return
	}

	saveFlag := s.updateShareAttr(typ, srv)
	if saveFlag {
		s.Owner().HeroManager().setBlessedHeroesAttrAndPower(srv)
	}
}

// 更新共享养成
func (s *ShareGrowth) updateShareAttr(typ common.SHARE_GROWTH_TYPE, srv servicer) bool {
	saveFlag := s.calcUpdateShareAttr(typ, srv)
	if saveFlag {
		s.save()
		s.sendShareGrowthToClient()
	}
	return saveFlag
}

// 计算更新共享养成
// 调用方要确保，当前处于缔约状态中
func (s *ShareGrowth) calcUpdateShareAttr(typ common.SHARE_GROWTH_TYPE, srv servicer) bool {
	switch typ {
	case common.SHARE_GROWTH_TYPE_SGT_ALL:
		heroFlag := s.calcUpdateShareHero(srv)
		equipFlag := s.calcUpdateShareEquipment()
		return heroFlag || equipFlag
	case common.SHARE_GROWTH_TYPE_SGT_HERO:
		return s.calcUpdateShareHero(srv)
	case common.SHARE_GROWTH_TYPE_SGT_EQUIPMENT:
		return s.calcUpdateShareEquipment()
	default:
		return false
	}
}

// 计算更新共享养成 - 英雄
// @return bool 属性是否有更新
func (s *ShareGrowth) calcUpdateShareHero(srv servicer) bool {
	heroManager := s.Owner().HeroManager()
	newAttr := &cl.ShareHero{}
	for i, hero := range heroManager.GetContractHeroes() {
		data := hero.GetData()
		if i == 0 {
			newAttr.Level = data.Level
			newAttr.Stage = data.Stage
		} else {
			if data.Level < newAttr.Level {
				newAttr.Level = data.Level
			}
			if data.Stage < newAttr.Stage {
				newAttr.Stage = data.Stage
			}
		}
	}

	var isUpdate bool
	oldAttr := s.getShareHeroAttr()
	if oldAttr == nil || newAttr.Level != oldAttr.Level || newAttr.Stage != oldAttr.Stage {
		isUpdate = true
		s.setShareHeroAttr(newAttr)
		s.owner.FireCommonEvent(srv.EventM(), event.IeCrystalContractUpdate, uint64(newAttr.Level))
	}
	return isUpdate
}

// 计算更新共享养成 - 装备
func (s *ShareGrowth) calcUpdateShareEquipment() bool {
	heroManager := s.Owner().HeroManager()
	shareAttrs := make(map[uint32][]*cl.ShareEquipment)
	for _, hero := range heroManager.GetContractHeroes() {
		datas := hero.GetAllEquipment()
		for pos := uint32(1); pos <= EquipWearMaxNum; pos++ {
			id, exist := datas[pos]
			if !exist {
				continue
			}

			if shareAttrs[pos] == nil {
				shareAttrs[pos] = make([]*cl.ShareEquipment, 0, RequireHeroCountForContract)
			}

			attr := s.getEquipmentAttrData(id)
			if attr != nil {
				shareAttrs[pos] = append(shareAttrs[pos], attr)
			}
		}
	}

	//更新前后，均无该共享养成时，不需推送数据
	s.filterShareEquipmentAttr(shareAttrs)
	if len(shareAttrs) == 0 &&
		!s.isHaveShareEquipmentAttr() {
		return false
	}

	if len(shareAttrs) == 0 {
		s.setShareEquipmentAttr(nil)
	} else {
		s.updateShareEquipmentAttr(shareAttrs)
	}
	return true
}

func (s *ShareGrowth) filterShareEquipmentAttr(shareAttrs map[uint32][]*cl.ShareEquipment) {
	for pos, attrs := range shareAttrs {
		if len(attrs) < RequireHeroCountForContract {
			delete(shareAttrs, pos)
			continue
		}
	}
}

func (s *ShareGrowth) getEquipmentAttrData(id uint64) *cl.ShareEquipment {
	manager := s.Owner().EquipManager()
	equip := manager.Get(id)
	if equip == nil {
		l4g.Errorf("user: %d getEquipmentAttrData: equipment is nil, eid:%d", s.Owner().ID(), id)
		return nil
	}

	return &cl.ShareEquipment{
		SysId:          equip.Data.SysId,
		StrengthLevel:  equip.Data.StrengthLevel,
		RefineExp:      equip.Data.RefineExp,
		RefineLevel:    equip.Data.RefineLevel,
		EnchantLevel:   equip.Data.EnchantLevel,
		EvolutionLevel: equip.Data.EvolutionLevel,
	}
}

func (s *ShareGrowth) updateShareEquipmentAttr(shareAttrs map[uint32][]*cl.ShareEquipment) {
	attrs := make(map[uint32]*cl.ShareEquipment)
	for pos, data := range shareAttrs {
		attr := &cl.ShareEquipment{}
		var minRare uint32
		for i, v := range data { //nolint:varnamelen
			if v == nil {
				continue
			}

			rare := goxml.GetData().EquipInfoM.GetEquipRare(v.SysId)
			if i == 0 {
				attr = v
				minRare = rare
			} else {
				if rare < minRare {
					minRare = rare
					attr.SysId = v.SysId
				}
				if v.StrengthLevel < attr.StrengthLevel {
					attr.StrengthLevel = v.StrengthLevel
				}
				if v.RefineLevel < attr.RefineLevel {
					attr.RefineLevel = v.RefineLevel
					attr.RefineExp = v.RefineExp
				} else if v.RefineLevel == attr.RefineLevel && v.RefineExp < attr.RefineExp {
					attr.RefineExp = v.RefineExp
				}
				if v.EnchantLevel < attr.EnchantLevel {
					attr.EnchantLevel = v.EnchantLevel
				}
				if v.EvolutionLevel < attr.EvolutionLevel {
					attr.EvolutionLevel = v.EvolutionLevel
				}
			}
		}

		if minRare == 0 {
			l4g.Errorf("user: %d updateShareEquipmentAttr: minRare err, sysID:%d", s.Owner().ID(), attr.SysId)
		} else {
			attrs[pos] = attr
		}
	}
	s.setShareEquipmentAttr(attrs)
}

// 是否已拥有装备的共享养成
func (s *ShareGrowth) isHaveShareEquipmentAttr() bool {
	return len(s.getShareEquipmentAttr()) > 0
}

// 推送共享养成数据
func (s *ShareGrowth) sendShareGrowthToClient() {
	msg := &cl.L2C_OpGlobalAttr{
		GlobalAttr: &cl.GlobalAttr{
			ShareGrowth: s.Flush(),
		},
	}
	s.owner.SendCmdToGateway(cl.ID_MSG_L2C_OpGlobalAttr, msg)
}
