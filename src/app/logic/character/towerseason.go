package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/logic/helper"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type TowerSeason struct {
	owner *User
	data  *cl.TowerSeason
}

func newTowerSeason(user *User) *TowerSeason {
	return &TowerSeason{
		owner: user,
	}
}

func (t *TowerSeason) Owner() *User {
	return t.owner
}

func (t *TowerSeason) load(data *cl.TowerSeason) {
	t.data = data
	if t.data == nil {
		t.data = &cl.TowerSeason{}
		now := time.Now().Unix()
		season, round, reset := t.NeedReset(now)
		if reset {
			t.Reset(now, season, round)
		}
	}

	if t.data.TaskProgress == nil {
		t.data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if t.data.TaskReceived == nil {
		t.data.TaskReceived = make(map[uint32]bool)
	}
}

func (t *TowerSeason) InitSeason(now time.Time) {
	t.data.LastResetTime = now.Unix()
	t.data.Floor = 0
	t.data.FloorTm = 0
	t.data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	t.data.TaskReceived = make(map[uint32]bool)
	t.data.Season = uint32(now.Month())
	t.data.MonthLikeCount = 0
}

func (t *TowerSeason) Save() {
	if t.Owner().dbUser.Module4.TowerSeason == nil {
		t.Owner().dbUser.Module4.TowerSeason = t.data
	}
	t.Owner().setSaveTag(saveTagModule4)
}

func (t *TowerSeason) Flush() *cl.TowerSeason {
	return t.data.Clone()
}

func (t *TowerSeason) GetSeason() uint32 {
	return t.data.Season
}

func (t *TowerSeason) GetRound() uint32 {
	return t.data.Round
}

func (t *TowerSeason) GetFloor() uint32 {
	return t.data.Floor
}

func (t *TowerSeason) GetFloorTm() int64 {
	return t.data.FloorTm
}

func (t *TowerSeason) SetFloor(floor uint32, tm int64) {
	t.data.Floor = floor
	t.SetFloorTm(tm)
	t.owner.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_TOWER_SEASON), 0, t.GetFloor())
}

func (t *TowerSeason) SetFloorTm(tm int64) {
	t.data.FloorTm = tm
}

func (t *TowerSeason) IsRecvAward(taskId uint32) bool {
	return t.data.TaskReceived[taskId]
}

func (t *TowerSeason) SetRecvAward(taskIds []uint32) {
	for _, taskId := range taskIds {
		t.data.TaskReceived[taskId] = true
	}
}

func (t *TowerSeason) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo, user *User) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return user.CalcTaskProgress(taskTypeInfo)
	}
	return t.data.TaskProgress[taskTypeInfo.Id]
}

func (t *TowerSeason) OnTowerSeasonEvent(event uint32, progress uint64, values []uint32, srv servicer) {
	progressMap, change := t.owner.TaskTypeOnEvent(t.data.TaskProgress, event, progress, values)
	if change {
		t.Save()
		t.sendTaskProgress(progressMap)
	}
}

// 逻辑与个人BOSS (mm *MirageM) LinkExtraSkills 相同
func (t *TowerSeason) LinkExtraSkills(altRaisePS *battle.AltRaisePS, formationID common.FORMATION_ID, fightEffect uint32, teamIndex int, linkSkillActive bool) {
	formationLinkInfo := t.owner.FormationManager().GetFormationLinkInfo(uint32(formationID), teamIndex)
	if formationLinkInfo == nil {
		l4g.Errorf("u:%d towerSeason LinkExtraSkills get GetFormationLinkInfo failed formation:%d team index:%d", t.owner.ID(), formationID, teamIndex)
		return
	}

	effects := goxml.GetData().TowerSeasonFightEffectInfoM.Group(fightEffect)
	if effects == nil {
		l4g.Errorf("u:%d towerSeason LinkExtraSkills get TowerSeasonFightEffectInfoM fightEffect:%d failed", t.owner.ID(), fightEffect)
		return
	}
	matchEffects := make(map[uint32]uint32) // key : 效果id, value: num
	// 计算满足条件的效果人数
	for _, links := range formationLinkInfo.HeroLinkInfo {
		for _, effect := range effects {
			linkNum := links[effect.Link1]
			matchEffects[effect.Id] += linkNum
			linkNum = links[effect.Link2]
			matchEffects[effect.Id] += linkNum
		}
	}

	pos2RaisePSs := make(map[uint32][]uint64)
	// 根据效果人数获取ps skill
	for _, effect := range effects {
		if !linkSkillActive && matchEffects[effect.Id] < effect.Num {
			continue
		}

		for _, skill := range effect.Skills {
			raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skill, 1)
			if raisePSInfo == nil {
				continue
			}
			pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
		}
	}

	altRaisePS.AltAttack(pos2RaisePSs)
}

func (t *TowerSeason) ResetDaily(srv servicer) {
	now := time.Now()
	if t.data.LastResetTime == 0 {
		season, round, reset := t.NeedReset(now.Unix())
		if reset {
			t.Reset(now.Unix(), season, round)
		}
		t.Save()
		return
	}

	if t.SyncRank() {
		t.SetSyncRank(false)
	}

	season, round, reset := t.NeedReset(now.Unix())
	if reset {
		seasonTasks := goxml.GetData().TowerSeasonTaskInfoM.SeasonTasks(t.data.Season, t.data.Round)
		totalAward := make([]*cl.Resource, 0)
		taskIds := make([]uint32, 0)
		if t.data.TaskProgress != nil {
			if t.data.TaskReceived == nil {
				t.data.TaskReceived = make(map[uint32]bool)
			}
			for _, taskInfo := range seasonTasks {
				if t.IsRecvAward(taskInfo.Id) {
					continue
				}

				taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
				if taskTypeInfo == nil {
					continue
				}

				progress := t.GetTaskProgress(taskTypeInfo, t.owner)
				if !t.owner.CheckTaskFinish(progress, taskInfo.TypeId, taskInfo.Progress) {
					continue
				}
				totalAward = append(totalAward, taskInfo.Awards...)
				taskIds = append(taskIds, taskInfo.Id)
			}
		}

		if len(totalAward) > 0 {
			totalAward = MergeResources(totalAward)
			TowerSeasonMail(srv, totalAward, t.owner, t.data.LastResetTime)
			t.owner.LogTowerSeasonRecvTaskAward(srv, taskIds, RecvAwardFromSys)
		}
		t.Reset(now.Unix(), season, round)
		t.Save()
		return
	}
}

func (t *TowerSeason) sendTaskProgress(progressMap map[uint32]*cl.TaskTypeProgress) {
	t.owner.SendCmdToGateway(cl.ID_MSG_L2C_TowerSeasonUpdateTask, &cl.L2C_TowerSeasonUpdateTask{
		Ret:      uint32(ret.RET_OK),
		Progress: progressMap,
	})
}

func (t *TowerSeason) GetMonthDay(now int64) uint32 {
	nowDate := time.Unix(now, 0)
	monthDay := goxml.GetFirstDateOfMonth(nowDate)
	return helper.DaysBetweenTimes(now, monthDay.Unix()) + 1
}

func (t *TowerSeason) GetRoundDay(now int64) uint32 {
	seasonInfo := goxml.GetData().SeasonInfoM.Index(t.GetSeason())
	if seasonInfo == nil {
		return 0
	}

	roundDuration := goxml.GetData().TowerSeasonConfigInfoM.CalcTowerSeasonRoundDuration(t.GetRound())
	roundStart := seasonInfo.StartTm + roundDuration
	return util.DaysBetweenTimes(now, roundStart) + 1
}

func (t *TowerSeason) GetTowerSeasonMonthlyResetting(now int64) bool {
	if now >= goxml.GetTowerSeasonMonthlyCloseTime(now) &&
		now <= goxml.GetTowerSeasonMonthlyEndTime(now) {
		return true
	}
	return false
}

func (t *TowerSeason) GetTowerSeasonRoundResetting(now int64) bool {
	if !goxml.GetData().TowerSeasonConfigInfoM.IsTowerSeasonOpen(now) {
		return true
	}
	resetStartTime, _ := goxml.GetTowerSeasonRoundResetStartTime(now)
	resetEndTime, _ := goxml.GetTowerSeasonNowRoundResetEndTime(now)
	if now >= resetStartTime && now <= resetEndTime {
		return true
	}
	return false
}

func (t *TowerSeason) CheckTaskAward() bool {
	if t.GetTowerSeasonRoundResetting(time.Now().Unix()) {
		return false
	}
	seasonTasks := goxml.GetData().TowerSeasonTaskInfoM.SeasonTasks(t.data.Season, t.data.Round)
	if len(t.data.TaskProgress) > 0 {
		if t.data.TaskReceived == nil {
			t.data.TaskReceived = make(map[uint32]bool)
		}
		for _, taskInfo := range seasonTasks {
			if t.IsRecvAward(taskInfo.Id) {
				continue
			}

			taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
			if taskTypeInfo == nil {
				continue
			}

			progress := t.GetTaskProgress(taskTypeInfo, t.owner)
			if !t.owner.CheckTaskFinish(progress, taskInfo.TypeId, taskInfo.Progress) {
				continue
			}
			return true
		}
	}
	return false
}

func (t *TowerSeason) GetMonthLikeCount() uint32 {
	return t.data.MonthLikeCount
}

func (t *TowerSeason) AddMonthLikeCount() {
	t.data.MonthLikeCount++
	t.Save()
}

func (t *TowerSeason) CheckSeasonLikeCount() bool {
	return t.GetMonthLikeCount() < goxml.GetData().TowerSeasonConfigInfoM.GetMonthLikeLimit()
}

func (t *TowerSeason) SyncRank() bool {
	return t.data.SyncRank
}

func (t *TowerSeason) SetSyncRank(param bool) {
	t.data.SyncRank = param
	t.Save()
}

func (t *TowerSeason) NeedReset(now int64) (uint32, uint32, bool) {
	seasonInfo := goxml.GetData().SeasonInfoM.GetSeasonInfoByTime(now)
	if seasonInfo == nil {
		if t.data.Season != 0 {
			return 0, 0, true
		}
	} else {
		round := goxml.GetData().TowerSeasonConfigInfoM.CalcTowerSeasonRound(now - seasonInfo.StartTm)
		if t.data.Season != seasonInfo.Id || t.data.Round != round {
			return seasonInfo.Id, round, true
		}
	}
	return 0, 0, false
}

func (t *TowerSeason) Reset(now int64, season uint32, round uint32) {
	t.data.LastResetTime = now
	t.data.Floor = 0
	t.data.FloorTm = 0
	t.data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	t.data.TaskReceived = make(map[uint32]bool)
	t.data.Season = season
	t.data.MonthLikeCount = 0
	t.data.Round = round
	t.owner.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_TOWER_SEASON), 0, t.GetFloor())
}

func (t *TowerSeason) CheckTopQuickBattleId(levelID uint32) bool {
	return levelID > t.data.TopQuickBattleId
}

func (t *TowerSeason) SetTopQuickBattleId(levelID uint32) {
	t.data.TopQuickBattleId = levelID
}

func (t *TowerSeason) TeamNum() int {
	validFloor := t.GetValidFloor()
	dungeonInfo := goxml.GetData().TowerSeasonDungeonInfoM.GetRecordById(validFloor)
	if dungeonInfo == nil {
		l4g.Errorf("user %d [TowerSeason] TeamNum: no dungeonInfo. floor %d", t.owner.ID(), validFloor)
		return 0
	}
	return len(dungeonInfo.MonsterGroups)
}

func (t *TowerSeason) GetValidFloor() uint32 {
	floor := t.GetFloor()
	floor = goxml.GetData().TowerSeasonDungeonInfoM.GetNextDungeonId(t.data.Season, t.data.Round, floor)
	return floor
}
