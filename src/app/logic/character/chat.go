package character

import (
	"app/goxml"
	"app/protos/in/l2c"
	"app/protos/in/p2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"fmt"
	"strconv"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/time"
)

type ChatGroupOp uint32

const (
	ChatGroupOpJoin ChatGroupOp = 1 // 加入
	ChatGroupOpQuit ChatGroupOp = 2 // 退出
)

const (
	ChatGroupTypeCommon = 0 // 普通群
	ChatGroupTypeBig    = 1 // 大群
)

const (
	ChatGroupVerificationNeed = 1 // 需要验证
	ChatGroupVerificationNo   = 2 // 直接进群
)

// opt - 选项，根据如下选项组合使用
//
//	      OPT_EXIT_IF_NOT_EXIST = 1 退出群组选项，若已加入群组不在列表内，则退出后重新加入
//	      OPT_AUTO_CREATE = 2       加入群组选项，若待加入的群组不存在，则创建后加入
//		  OPT_SET_JOINED_GROUP = 4  更新群组选项，若已加入群组在列表内，则更新群组信息
const (
	ChatGroupOptSeven = 7
	ChatGroupOptSix   = 6 // 全服语言群用 6
)

const (
	ChatSingleType       = 1
	ChatGroupType        = 2 // 普通群聊
	ChatSuperGroupType   = 3
	ChatNotificationType = 4
)

type Option func(msg *cl.ChatMessage)

func NewMsg(options ...Option) *cl.ChatMessage {
	msg := &cl.ChatMessage{}
	msg.Timestamp = strconv.FormatUint(uint64(time.Now().Unix()), 10)
	for _, option := range options {
		option(msg)
	}

	return msg
}

func Id(srv servicer) Option {
	return func(msg *cl.ChatMessage) {
		msg.Id = strconv.FormatUint(srv.CreateUserLogID(), 10)
	}
}

func Sender(senderID uint64) Option {
	return func(msg *cl.ChatMessage) {
		msg.Sender = strconv.FormatUint(senderID, 10)
	}
}

func Content(content string) Option {
	return func(msg *cl.ChatMessage) {
		msg.Content = content
	}
}

func Head(user *cl.ChatUserHead) Option {
	return func(msg *cl.ChatMessage) {
		msg.User = user
	}
}

func Type(msgType uint32) Option {
	return func(msg *cl.ChatMessage) {
		msg.Type = msgType
	}
}

func Params(params ...string) Option {
	return func(msg *cl.ChatMessage) {
		msg.Params = params
	}
}

func genUserID(uid uint64) string {
	return fmt.Sprintf("omniheros_%d", uid)
}

// 字段含义可参考: https://qdreaming.feishu.cn/wiki/wikcnDIR0Apdp9212La2v7pTIhe
func newChatSendMsg(uid uint64, groupID string, ex []byte, message *cl.ChatMessage) *p2l.L2P_ChatSendMsg {
	//nolint:mnd
	sendMsg := &p2l.L2P_ChatSendMsg{
		UniqueId: message.Id,
		Uid:      uid,
		SysMsg: &p2l.ChatSendMsg{
			MsgData: &p2l.ChatMsgData{
				SendID:           genUserID(uid),
				GroupID:          groupID,
				ClientMsgID:      message.Id,
				SenderPlatformID: 7,                  // 发送者平台ID, 统一填7
				SessionType:      ChatSuperGroupType, // 大群群聊
				MsgFrom:          200,                // 消息来源
				ContentType:      501,                // 系统推送文本， 聊天服定义
				Content:          ex,
			},
		},
	}
	// 指定消息需要设置为系统发送的
	if _, exist := goxml.ChatNeedChangeSendID[message.Type]; exist {
		sendMsg.SysMsg.MsgData.SendID = "omniheros_admin"
	}

	return sendMsg
}

// NewChatUser 字段含义可参考: https://qdreaming.feishu.cn/wiki/wikcnDIR0Apdp9212La2v7pTIhe
func NewChatUser(uid, serverID uint64, ex, name string, cd uint32) *p2l.ChatUserInfo {
	return &p2l.ChatUserInfo{
		UserID:    genUserID(uid),
		LexiconID: "omniheros", // 敏感词库
		FaceURL:   ex,          // 头像相关信息: json格式
		Nickname:  name,
		ServerID:  serverID,
		SendCd:    cd,
	}
}

// NewChatGroupTag 字段含义可参考: https://qdreaming.feishu.cn/wiki/wikcnDIR0Apdp9212La2v7pTIhe
func NewChatGroupTag(tag, groupID string) *p2l.ChatGroupTag {
	group := &p2l.ChatGroupTag{
		Tag:       tag,
		Opt:       ChatGroupOptSeven,
		GroupType: ChatGroupTypeBig,
	}
	groupInfo := &p2l.ChatGroupInfo{
		GroupID:          groupID,
		GroupType:        ChatGroupTypeBig,
		NeedVerification: ChatGroupVerificationNeed,
	}
	group.Groups = append(group.Groups, groupInfo)

	return group
}

// NewChatGroupTagAllWorld 字段含义可参考: https://qdreaming.feishu.cn/wiki/wikcnDIR0Apdp9212La2v7pTIhe
func NewChatGroupTagAllWorld(srv servicer, lang, groupTag, groupName string, uid uint64, opt uint32) *p2l.ChatGroupTag {
	langInfo := goxml.GetData().ChatLanguageInfoM.GetLanguageInfo(lang)
	if langInfo == nil {
		l4g.Errorf("user: %d new chatGroup failed. chatLangInfo is nil. lang: %s", uid, lang)
		return nil
	}

	group := &p2l.ChatGroupTag{
		Tag:       groupTag,
		GroupType: ChatGroupTypeBig,
		Opt:       opt,
	}
	// 固定创建一个群组
	groupInfo := &p2l.ChatGroupInfo{
		GroupID:          groupName + strconv.FormatUint(srv.OpGroup(), 10) + "_" + strconv.FormatUint(uint64(langInfo.Language), 10) + "_" + "1",
		GroupType:        ChatGroupTypeBig,
		MaxMemberNum:     langInfo.PeopleMax,
		NeedVerification: ChatGroupVerificationNo,
	}
	// 当1群满了，从2开始，聊天服自动创建群
	groupInfo1 := &p2l.ChatGroupInfo{
		GroupID:          groupName + strconv.FormatUint(srv.OpGroup(), 10) + "_" + strconv.FormatUint(uint64(langInfo.Language), 10) + "_" + "%d" + ",2-n",
		GroupType:        ChatGroupTypeBig,
		MaxMemberNum:     langInfo.PeopleMax,
		NeedVerification: ChatGroupVerificationNo,
	}

	group.Groups = append(group.Groups, groupInfo, groupInfo1)

	return group
}

func getChatGroupID(srv servicer, msgType uint32, uid uint64) string {
	adminInfo := goxml.GetData().ChatAdminInfoM.Index(msgType)
	if adminInfo == nil {
		l4g.Errorf("BroadcastWorldMsg: chatAdminInfo is nil. uid: %d, type: %d", uid, msgType)
		return ""
	}
	chatInfo := goxml.GetData().ChatInfoM.Index(adminInfo.ChannelId)
	if chatInfo == nil {
		l4g.Errorf("BroadcastWorldMsg: chatInfo is nil. uid: %d, channelID: %d", uid, adminInfo.ChannelId)
		return ""
	}
	groupID := ""
	switch adminInfo.ChannelId {
	case goxml.ChatGuild, goxml.ChatSystemGuild:
		groupID = guildGroupID(chatInfo.GroupName, srv, srv.GetGuildID(uid))
	case goxml.ChatLocalWorld:
		groupID = localWorldGroupID(chatInfo.GroupName, srv)
	case goxml.ChatPartition:
		groupID = partitionGroupID(chatInfo.GroupName, srv)
	case goxml.ChatGuildDungeon:
		roomId := srv.GetGuildDungeonChatRoomID(uid)
		if roomId == 0 {
			l4g.Errorf("getChatGroupID: dungeon room id is 0. uid: %d, channelID: %d", uid, adminInfo.ChannelId)
			return ""
		}
		groupID = guildGroupID(chatInfo.GroupName, srv, roomId)
	case goxml.ChatGST:
		roomId := srv.GetGstChatRoomID(uid)
		if roomId == "" {
			l4g.Errorf("getChatGroupID: gst room id is 0. uid: %d, channelID: %d", uid, adminInfo.ChannelId)
			return ""
		}
		groupID = gstGroupID(chatInfo.GroupName, srv, roomId)
	}

	return groupID
}

// SyncUserAndGroup isHand：true: 客户端GetChatToken, false: 系统登录时操作
func SyncUserAndGroup(srv servicer, user *User, isGetToken bool) uint32 {
	msg := NewMsg(Head(user.NewChatUserHead(srv.GetLogicGuild(user.ID()))), Id(srv))
	bytes, err := json.Marshal(msg)
	if err != nil {
		l4g.Errorf("user: %d GetChatToken: json marshal msg failed. err: %s", user.ID(), err.Error())
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}
	noSyncGroupTag := goxml.GetData().ChatInfoM.GetAllGroupTag()
	// 战区频道未开启，不同步
	if !user.IsFunctionOpen(uint32(common.FUNCID_MODULE_CHAT_PARTITION), srv) {
		for i, tag := range noSyncGroupTag {
			if tag == goxml.GetData().ChatInfoM.ChatPartitionGroupTag {
				noSyncGroupTag = append(noSyncGroupTag[:i], noSyncGroupTag[i+1:]...)
				break
			}
		}
	}

	cd := goxml.GetData().ChatConfigInfoM.GetChatCD(user.Dungeon().GetDungeonID())
	pmsg := &p2l.L2P_ChatGetToken{
		UniqueId: msg.Id,
		UserID:   strconv.FormatUint(user.ID(), 10),
		User: &p2l.ChatSyncUserAndJoinGroup{
			GetToken:  isGetToken,
			User:      NewChatUser(user.ID(), user.ServerID(), string(bytes), user.Name(), cd),
			GroupTags: make([]*p2l.ChatGroupTag, 0, len(noSyncGroupTag)),
		},
		CrossArea: srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)),
		IsHand:    isGetToken,
		Lang:      user.GetLang(),
	}

	for _, tag := range noSyncGroupTag {
		groupTag := initChatGroupTag(srv, tag, user)
		if groupTag != nil {
			pmsg.User.GroupTags = append(pmsg.User.GroupTags, groupTag)
		}
	}

	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatGetToken), user.ID(), pmsg)

	return uint32(cret.RET_OK)
}

func localWorldGroupID(groupName string, srv servicer) string {
	groupID := groupName + strconv.FormatUint(srv.OpGroup(), 10) + "_" + strconv.FormatUint(srv.ServerID(), 10)
	return groupID
}

func partitionGroupID(groupName string, srv servicer) string {
	groupID := groupName + strconv.FormatUint(srv.OpGroup(), 10) + "_" + strconv.FormatUint(uint64(srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA))), 10)
	return groupID
}

func guildGroupID(groupName string, srv servicer, id uint64) string {
	groupID := groupName + strconv.FormatUint(srv.OpGroup(), 10) + "_" + strconv.FormatUint(id, 10)
	return groupID
}

func gstGroupID(groupName string, srv servicer, id string) string {
	groupID := groupName + strconv.FormatUint(srv.OpGroup(), 10) + "_" + id
	return groupID
}

func initChatGroupTag(srv servicer, groupTag string, u *User) *p2l.ChatGroupTag {
	chatInfo := goxml.GetData().ChatInfoM.GetChatInfo(groupTag)
	if chatInfo == nil {
		l4g.Errorf("user: %d new chatGroup failed. chatInfo is nil. groupTag: %s", u.ID(), groupTag)
		return nil
	}
	switch chatInfo.Id {
	case goxml.ChatGuild, goxml.ChatSystemGuild:
		if u.GuildID() == 0 {
			return nil
		}
		return NewChatGroupTag(groupTag, guildGroupID(chatInfo.GroupName, srv, u.GuildID()))
	case goxml.ChatLocalWorld:
		return NewChatGroupTag(groupTag, localWorldGroupID(chatInfo.GroupName, srv))
	case goxml.ChatAllWorld:
		return NewChatGroupTagAllWorld(srv, u.GetLang(), groupTag, chatInfo.GroupName, u.ID(), ChatGroupOptSix)
	case goxml.ChatPartition:
		if srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)) > 0 {
			return NewChatGroupTag(groupTag, partitionGroupID(chatInfo.GroupName, srv))
		}

	case goxml.ChatGuildDungeon:
		dungeonRoomId := srv.GetGuildDungeonChatRoomID(u.ID())
		if dungeonRoomId > 0 {
			return NewChatGroupTag(groupTag, guildGroupID(chatInfo.GroupName, srv, dungeonRoomId))
		}
	case goxml.ChatGST:
		chatRoomId := srv.GetGstChatRoomID(u.ID())
		if len(chatRoomId) > 0 {
			return NewChatGroupTag(groupTag, gstGroupID(chatInfo.GroupName, srv, chatRoomId))
		}
	}

	return nil
}

func ChatSendMsgToPlatform(srv servicer, uid uint64, msg *cl.ChatMessage) {
	bytes, err := json.Marshal(msg)
	if err != nil {
		l4g.Errorf("ChatSendMsgToPlatform: json marshal failed. uid: %d", uid)
		return
	}

	groupID := getChatGroupID(srv, msg.Type, uid)
	if groupID == "" {
		l4g.Errorf("user: %d ChatSendMsgToPlatform: groupID not exist. msgType: %d", uid, msg.Type)
		return
	}
	chatMsg := newChatSendMsg(uid, groupID, bytes, msg)
	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatSendMsg), uid, chatMsg)
}

func ChatJoinGuildGroupToPlatform(srv Servicer, uid, guildID uint64, groupOp ChatGroupOp) {
	chatJoinGuild(srv, uid, guildID, groupOp)
	chatJoinSystemGuild(srv, uid, guildID, groupOp)
}

func chatJoinGuild(srv Servicer, uid, guildID uint64, groupOp ChatGroupOp) {
	info := goxml.GetData().ChatInfoM.Index(goxml.ChatGuild)
	if info == nil {
		l4g.Errorf("user: %d new chatGroup failed. chatInfo is nil. channelID: %d", uid, goxml.ChatGuild)
		return
	}

	msg := &p2l.L2P_ChatJoinGroup{
		UniqueId: strconv.FormatUint(srv.CreateUserLogID(), 10),
		Uid:      uid,
		GroupOp:  uint32(groupOp),
		GroupTag: info.GroupTag,
		JoinGroup: &p2l.ChatJoinGroup{
			UserID: genUserID(uid),
		},
	}

	// 加入的时候：ChatGroupTag 有值， 退出： ChatGroupTag 为空
	if groupOp == ChatGroupOpJoin {
		groupID := guildGroupID(info.GroupName, srv, guildID)
		subGroup := NewChatGroupTag(info.GroupTag, groupID)
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, subGroup)
	} else {
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, &p2l.ChatGroupTag{
			Tag:       info.GroupTag,
			Opt:       ChatGroupOptSeven,
			GroupType: ChatGroupTypeBig,
		})
	}
	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatJoinGroup), uid, msg)
}

func chatJoinSystemGuild(srv Servicer, uid, guildID uint64, groupOp ChatGroupOp) {
	info := goxml.GetData().ChatInfoM.Index(goxml.ChatSystemGuild)
	if info == nil {
		l4g.Errorf("user: %d new chatGroup failed. chatInfo is nil. channelID: %d", uid, goxml.ChatSystemGuild)
		return
	}

	msg := &p2l.L2P_ChatJoinGroup{
		UniqueId: strconv.FormatUint(srv.CreateUserLogID(), 10),
		Uid:      uid,
		GroupOp:  uint32(groupOp),
		GroupTag: info.GroupTag,
		JoinGroup: &p2l.ChatJoinGroup{
			UserID: genUserID(uid),
		},
	}

	// 加入的时候：ChatGroupTag 有值， 退出： ChatGroupTag 为空
	if groupOp == ChatGroupOpJoin {
		groupID := guildGroupID(info.GroupName, srv, guildID)
		subGroup := NewChatGroupTag(info.GroupTag, groupID)
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, subGroup)
	} else {
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, &p2l.ChatGroupTag{
			Tag:       info.GroupTag,
			Opt:       ChatGroupOptSeven,
			GroupType: ChatGroupTypeBig,
		})
	}
	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatJoinGroup), uid, msg)
}

func ChatJoinAllWorldGroupToPlatform(srv Servicer, uid uint64, lang string, opt uint32) {
	info := goxml.GetData().ChatInfoM.Index(goxml.ChatAllWorld)
	if info == nil {
		l4g.Errorf("user: %d new chatGroup failed. chatInfo is nil. channelID: %d", uid, goxml.ChatAllWorld)
		return
	}

	msg := &p2l.L2P_ChatJoinGroup{
		UniqueId: strconv.FormatUint(srv.CreateUserLogID(), 10),
		Uid:      uid,
		GroupOp:  uint32(ChatGroupOpJoin),
		GroupTag: info.GroupTag,
		JoinGroup: &p2l.ChatJoinGroup{
			UserID: genUserID(uid),
		},
	}

	groupTag := NewChatGroupTagAllWorld(srv, lang, info.GroupTag, info.GroupName, uid, opt)
	if groupTag == nil {
		l4g.Errorf("user: %d new chatGroupTag failed. lang: %s", uid, lang)
		return
	}
	msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, groupTag)

	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatJoinGroup), uid, msg)
}

func ChatAutoJoinGroupToPlatform(srv Servicer, user *User, isGetToken bool) {
	retCode := SyncUserAndGroup(srv, user, isGetToken)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("ChatJoinPartitionGroupToPlatform: join partition group failed. ret:%d", retCode)
	}
}

func ChatSyncUserToPlatform(srv Servicer, user *User) {
	msg := NewMsg(Head(user.NewChatUserHead(srv.GetLogicGuild(user.ID()))), Id(srv))
	bytes, err := json.Marshal(msg)
	if err != nil {
		l4g.Errorf("user: %d ChatSyncUser: json marshal msg failed. err: %s", user.ID(), err.Error())
		return
	}
	cd := goxml.GetData().ChatConfigInfoM.GetChatCD(user.Dungeon().GetDungeonID())
	pmsg := &p2l.L2P_ChatSyncUser{
		UniqueId: msg.Id,
		User: &p2l.ChatUser{
			User: NewChatUser(user.ID(), user.ServerID(), string(bytes), user.Name(), cd),
		},
	}

	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatSyncUser), user.ID(), pmsg)
}

func ChatSendMsgToPlatformForOtherGuild(srv servicer, uid uint64, gid uint64, msg *cl.ChatMessage) {
	bytes, err := json.Marshal(msg)
	if err != nil {
		l4g.Errorf("ChatSendMsgToPlatform: json marshal failed. uid: %d", uid)
		return
	}

	adminInfo := goxml.GetData().ChatAdminInfoM.Index(msg.Type)
	if adminInfo == nil {
		l4g.Errorf("BroadcastGuildMsg: chatAdminInfo is nil. uid: %d, type: %d", uid, msg.Type)
		return
	}
	chatInfo := goxml.GetData().ChatInfoM.Index(adminInfo.ChannelId)
	if chatInfo == nil {
		l4g.Errorf("BroadcastGuildMsg: chatInfo is nil. uid: %d, channelID: %d", uid, adminInfo.ChannelId)
		return
	}

	groupID := guildGroupID(chatInfo.GroupName, srv, gid)

	if groupID == "" {
		l4g.Errorf("user: %d ChatSendMsgToPlatform: groupID not exist. msgType: %d", uid, msg.Type)
		return
	}
	chatMsg := newChatSendMsg(uid, groupID, bytes, msg)
	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatSendMsg), uid, chatMsg)
}

func ChatJoinGuildDungeonGroupToPlatform(srv Servicer, uid, guildDungeonID uint64, groupOp ChatGroupOp) {
	info := goxml.GetData().ChatInfoM.Index(goxml.ChatGuildDungeon)
	if info == nil {
		l4g.Errorf("user: %d new chatGroup failed. chatInfo is nil. channelID: %d", uid, goxml.ChatGuildDungeon)
		return
	}

	msg := &p2l.L2P_ChatJoinGroup{
		UniqueId: strconv.FormatUint(srv.CreateUserLogID(), 10),
		Uid:      uid,
		GroupOp:  uint32(groupOp),
		GroupTag: info.GroupTag,
		JoinGroup: &p2l.ChatJoinGroup{
			UserID: genUserID(uid),
		},
	}

	if groupOp == ChatGroupOpJoin {
		groupID := guildGroupID(info.GroupName, srv, guildDungeonID)
		subGroup := NewChatGroupTag(info.GroupTag, groupID)
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, subGroup)
	} else {
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, &p2l.ChatGroupTag{
			Tag:       info.GroupTag,
			Opt:       ChatGroupOptSeven,
			GroupType: ChatGroupTypeBig,
		})
	}

	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatJoinGroup), uid, msg)
}

func ChatQuitGST(srv Servicer, uid uint64) {
	chatRoomId := srv.GetGstChatRoomID(uid)
	if len(chatRoomId) == 0 {
		return
	}
	ChatJoinGST(srv, uid, chatRoomId, ChatGroupOpQuit)
}

func ChatJoinGST(srv Servicer, uid uint64, gstID string, groupOp ChatGroupOp) {
	info := goxml.GetData().ChatInfoM.Index(goxml.ChatGST)
	if info == nil {
		l4g.Errorf("user: %d new chatGroup failed. chatInfo is nil. channelID: %d", uid, goxml.ChatGST)
		return
	}

	msg := &p2l.L2P_ChatJoinGroup{
		UniqueId: strconv.FormatUint(srv.CreateUserLogID(), 10),
		Uid:      uid,
		GroupOp:  uint32(groupOp),
		GroupTag: info.GroupTag,
		JoinGroup: &p2l.ChatJoinGroup{
			UserID: genUserID(uid),
		},
	}

	if groupOp == ChatGroupOpJoin {
		groupID := gstGroupID(info.GroupName, srv, gstID)
		subGroup := NewChatGroupTag(info.GroupTag, groupID)
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, subGroup)
	} else {
		msg.JoinGroup.GroupTags = append(msg.JoinGroup.GroupTags, &p2l.ChatGroupTag{
			Tag:       info.GroupTag,
			Opt:       ChatGroupOptSeven,
			GroupType: ChatGroupTypeBig,
		})
	}

	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatJoinGroup), uid, msg)
}

func ChatGstPushMessageToPlatform(srv Servicer, roomId string, guildId uint64, msg *cl.ChatMessage) {
	bytes, err := json.Marshal(msg)
	if err != nil {
		l4g.Errorf("ChatGstPushMessageToPlatform: json marshal failed.")
		return
	}
	adminInfo := goxml.GetData().ChatAdminInfoM.Index(msg.Type)
	if adminInfo == nil {
		l4g.Errorf("BroadcastGuildMsg: chatAdminInfo is nil. type: %d", msg.Type)
		return
	}
	info := goxml.GetData().ChatInfoM.Index(adminInfo.ChannelId)
	if info == nil {
		l4g.Errorf("new chatGroup failed. chatInfo is nil. channelID: %d", adminInfo.ChannelId)
		return
	}

	var groupID string
	switch adminInfo.ChannelId {
	case goxml.ChatGuild, goxml.ChatSystemGuild:
		groupID = guildGroupID(info.GroupName, srv, guildId)
	case goxml.ChatGST:
		groupID = gstGroupID(info.GroupName, srv, roomId)
	default:
		l4g.Errorf("BroadcastGuildMsg: chatAdminInfo is not guild channel. type: %d", msg.Type)
		return
	}
	if groupID == "" {
		l4g.Errorf("ChatGstPushMessageToPlatform: groupID not exist. msgType: %d", msg.Type)
		return
	}
	chatMsg := newChatSendMsg(0, groupID, bytes, msg)
	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatSendMsg), 0, chatMsg)
}
