package character

import (
	"app/logic/account"
	kv "app/protos/in/account"

	"gitlab.qdream.com/kit/sea/time"
)

// 账号数据更新
// @param eventType
func (u *User) PutRoleToAccount(srv servicer, eventType uint32) {
	msg := &account.RoleMessage{
		Source: &kv.Role{
			ServerId:    u.ServerID(),
			AppId:       uint32(srv.AppID()),
			OpId:        u.OpID(),
			UserId:      u.ID(),
			Level:       u.Level(),
			Vip:         u.Vip(),
			CreateTime:  uint64(u.CreateTime()),
			OnlineTime:  uint64(u.OnlineTime()),
			OfflineTime: uint64(u.OfflineTime()),
			CurrentTime: uint64(time.Now().Unix()),
			Uuid:        u.UUID(),
			Name:        u.Name(),
			OpGroup:     uint32(srv.OpGroup()),
			Ip:          u.NetAddr(),
			EventType:   eventType,
			BaseId:      u.BaseID(),
		},
	}
	srv.AccountM().Send(msg)
}

func (u *User) TestQueryRoleFromAccount(srv servicer) {
	msg := &account.QueryMessage{
		Source: &kv.Query{
			Uuid:    u.UUID(),
			OpGroup: uint32(srv.OpGroup()),
			AppId:   uint32(srv.AppID()),
		},
	}
	srv.AccountM().Send(msg)
}
