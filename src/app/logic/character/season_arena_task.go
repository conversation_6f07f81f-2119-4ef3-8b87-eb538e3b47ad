package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
)

type SeasonArenaTask struct {
	owner *User
	data  *cl.SeasonArenaTask
}

// 任务重置逻辑

func newSeasonArenaTask(user *User) *SeasonArenaTask {
	return &SeasonArenaTask{
		owner: user,
	}
}

func (s *SeasonArenaTask) Owner() *User {
	return s.owner
}

func (s *SeasonArenaTask) Load(data *cl.SeasonArenaTask) {
	s.data = data
	if s.data == nil {
		s.data = &cl.SeasonArenaTask{}
	}
	if s.data.TaskProgress == nil {
		s.data.TaskProgress = make(map[uint32]*cl.TaskTypeProgress)
	}
	if s.data.ReceiveAwarded == nil {
		s.data.ReceiveAwarded = make(map[uint32]bool)
	}
}

func (s *SeasonArenaTask) Save() {
	s.Owner().dbUser.Module6.SeasonArenaTask = s.data
	s.Owner().setSaveTag(saveTagModule6)
}

func (s *SeasonArenaTask) OnSeasonArenaTask(event uint32, progress uint64, values []uint32, srv servicer) {
	// 检查玩家是否达到活动的参与条件
	if !s.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_ARENA), srv) {
		return
	}

	taskProgress, change := s.owner.TaskTypeOnEvent(s.data.TaskProgress, event, progress, values)
	if change {
		s.update(taskProgress)
		s.Save()
	}
}

func (s *SeasonArenaTask) update(taskTypeProgress map[uint32]*cl.TaskTypeProgress) {
	msg := &cl.L2C_SeasonArenaTaskUpdate{
		Ret:          uint32(ret.RET_OK),
		TaskProgress: taskTypeProgress,
	}
	s.owner.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaTaskUpdate, msg)
}

func (s *SeasonArenaTask) IsRecviveTaskAward(taskId uint32) bool {
	return s.data.ReceiveAwarded[taskId]
}

func (s *SeasonArenaTask) SetRecviveTaskAward(taskIds []uint32) {
	for _, taskId := range taskIds {
		s.data.ReceiveAwarded[taskId] = true
	}
	s.Save()
}

func (s *SeasonArenaTask) GetTaskProgress(taskTypeInfo *goxml.TaskTypeInfo) *cl.TaskTypeProgress {
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		return s.owner.CalcTaskProgress(taskTypeInfo)
	}
	return s.data.TaskProgress[taskTypeInfo.Id]
}

func (s *SeasonArenaTask) GetData() *cl.SeasonArenaTask {
	return s.data.Clone()
}

func (s *SeasonArenaTask) RoundClear() {
	s.data = &cl.SeasonArenaTask{
		TaskProgress:   make(map[uint32]*cl.TaskTypeProgress),
		ReceiveAwarded: make(map[uint32]bool),
	}
	s.Save()
}
