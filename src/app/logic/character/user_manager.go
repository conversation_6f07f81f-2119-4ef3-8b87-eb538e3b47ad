package character

import (
	"fmt"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	"app/goxml"
	"app/logic/helper"
	"app/logic/mail"
	"app/logic/session"
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/util"

	"github.com/google/btree"
	l4g "github.com/ivanabc/log4go"
)

type uint64Set map[uint64]util.None

// ***************************玩家下线缓存****************/
// 设计目的：针对断线重连的玩家的一个快速访问，减少数据库操作
type UsersCache struct {
	maxEntries int
	users      map[uint64]*User
	signs      map[string]*User
	expired    *btree.BTree
}

func newUsersCache(maxEntries int) *UsersCache {
	return &UsersCache{
		maxEntries: maxEntries,
		users:      make(map[uint64]*User),
		signs:      make(map[string]*User),
		expired:    btree.New(32), //nolint:mnd
	}
}

func (uc *UsersCache) SetMaxEntries(max int) {
	uc.maxEntries = max
}

// 构建btree Item
type expiredItem struct {
	id uint64
	tm int64
}

func (e *expiredItem) Less(than btree.Item) bool {
	other := than.(*expiredItem)
	if e.tm < other.tm {
		return true
	}
	if e.tm == other.tm && e.id < other.id {
		return true
	}
	return false
}

func newExpiredItem(u *User) *expiredItem {
	return &expiredItem{
		id: u.ID(),
		tm: u.getCacheExpiredTime(),
	}
}

func (uc *UsersCache) add(user *User, interval int64) {
	if interval > 0 {
		user.SetState(StateCache)
		uc.users[user.ID()] = user
		uc.signs[user.Sign()] = user
		user.setCacheExpiredTime(time.Now().Unix() + interval)
		uc.expired.ReplaceOrInsert(newExpiredItem(user))
		l4g.Infof("move user %d to cache(size: %d) msg queue(%s)",
			user.ID(), uc.size(), user.MsgSync())
	}
}

func (uc *UsersCache) delete(user *User) {
	delete(uc.users, user.ID())
	delete(uc.signs, user.Sign())
	uc.expired.Delete(newExpiredItem(user))
	user.SetState(StateNone)
	l4g.Infof("delete user %d from cache(size: %d)", user.ID(), uc.size())
}

func (uc *UsersCache) clear(srv servicer) {
	uc.Range(func(u *User) {
		//当缓存阶段，数据发生变化，需要保存操作
		u.Save(srv)
		uc.delete(u)
	})
}

func (uc *UsersCache) size() int {
	return uc.expired.Len()
}

func (uc *UsersCache) GetUserByUUIDAndServerID(uuid string, serverID uint64) *User {
	u := uc.signs[fmt.Sprintf("%s:%d", uuid, serverID)]
	return u
}

func (uc *UsersCache) GetUser(id uint64) *User {
	return uc.users[id]
}

func (uc *UsersCache) Range(fn func(u *User)) {
	for _, u := range uc.users {
		fn(u)
	}
}

// 删除过期过量数据
func (uc *UsersCache) Evict(srv servicer, now int64, max int) {
	var items []uint64
	pivot := &expiredItem{
		tm: now + 1,
	}
	var num int
	//删除超时的数据
	uc.expired.AscendLessThan(pivot, func(i btree.Item) bool {
		items = append(items, i.(*expiredItem).id)
		num++
		return num < max
	})
	for _, id := range items {
		u := uc.GetUser(id)
		u.Save(srv)
		uc.delete(u)
	}
	if num >= max || uc.size() <= uc.maxEntries {
		return
	}
	items = items[:0]
	//删除过量的数据
	count := uc.size() - uc.maxEntries
	uc.expired.Ascend(func(i btree.Item) bool {
		items = append(items, i.(*expiredItem).id)
		count--
		num++
		return (num < max && count != 0)
	})
	for _, id := range items {
		u := uc.GetUser(id)
		//当缓存阶段，数据发生变化，需要保存操作
		u.Save(srv)
		uc.delete(u)
	}
}

// 玩家管理器
type UserManager struct {
	unauths      map[string]map[uint64]*User
	usersInGates map[uint64]uint64Set
	cookies      map[uint64]*User

	users            [UserGroup]map[uint64]*User
	lastForeachIndex int //游戏中玩家遍历操作是分批完成
	cache            *UsersCache

	mails      *mail.Box       //系统邮件，全服邮件
	groupMails *groupMailCache //群组邮件临时缓存

	onlineUsersByLevel []map[uint64]struct{} //在线玩家按照等级分组，方便推荐好友，优先从在线玩家里推选

	onlineNumByWmOs map[string]int //完美要求的按照os分类统计在线玩家数量

	wrongUsers map[string]int64

	lastLoginUserName []string //最后登录的5个玩家的名字
	lastLoginIndex    int
	lastLoginIndexMax int

	userBattleDataCache *OfflineUserBattleDataCache
	cacheExpireTime     int64

	orCache *offlineResourceCache
}

func NewUserManager(maxEntries int, cacheExpireTime int64) *UserManager {
	um := &UserManager{
		unauths:      make(map[string]map[uint64]*User),
		usersInGates: make(map[uint64]uint64Set),
		cookies:      make(map[uint64]*User),

		cache:               newUsersCache(maxEntries),
		userBattleDataCache: newBattleDataCache(maxEntries),
		groupMails:          newGroupMailCache(),
		onlineUsersByLevel:  make([]map[uint64]struct{}, int(goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel+1)),
		onlineNumByWmOs:     make(map[string]int, 5), //nolint:mnd
		wrongUsers:          make(map[string]int64),
		lastLoginUserName:   make([]string, 0, int(goxml.GetData().ConfigInfoM.GetAssistanceActivityNameNum())),
		lastLoginIndexMax:   int(goxml.GetData().ConfigInfoM.GetAssistanceActivityNameNum()),
		cacheExpireTime:     cacheExpireTime,
		orCache:             newOfflineResourceCache(),
	}
	for i := 0; i < UserGroup; i++ {
		um.users[i] = make(map[uint64]*User)
	}
	um.onlineNumByWmOs[WmOsAndroid] = 0
	return um
}

func (um *UserManager) GetCacheExpiredTime() int64 {
	return um.cacheExpireTime
}

func (um *UserManager) Cache() *UsersCache {
	return um.cache
}

func (um *UserManager) Cookies() map[uint64]*User {
	return um.cookies
}

// 当cache中没有玩家，创建一个未验证的玩家
func (um *UserManager) NewUnauthUser(
	gate *session.Client, cookie uint64, uuid string, serverID uint64, na string) *User {
	user := NewUser(gate, cookie, uuid, serverID, na)
	um.addIndex(user)
	return user
}

func (um *UserManager) GetUserFromCache(uuid string, serverID uint64) *User {
	return um.cache.GetUserByUUIDAndServerID(uuid, serverID)
}

func (um *UserManager) ReuseCacheUser(user *User, gate *session.Client, cookie uint64, na string) {
	//注意顺序
	um.cache.delete(user)

	user.onlineFromCache(gate, cookie, na)
	um.addIndex(user)
	um.addUser(user)
}

func (um *UserManager) addIndex(user *User) {
	if set, exist := um.unauths[user.UUID()]; !exist {
		set = make(map[uint64]*User)
		set[user.ServerID()] = user
		um.unauths[user.UUID()] = set
	} else {
		set[user.ServerID()] = user
	}

	um.cookies[user.Cookie()] = user

	if set, exist := um.usersInGates[user.GateID()]; exist {
		set[user.Cookie()] = util.None{}
	} else {
		set = make(map[uint64]util.None)
		set[user.Cookie()] = util.None{}
		um.usersInGates[user.GateID()] = set
	}
}

func (um *UserManager) deleteIndex(user *User) {
	if set, exist := um.unauths[user.UUID()]; exist {
		delete(set, user.ServerID())
		if len(set) == 0 {
			delete(um.unauths, user.UUID())
		}
	}

	delete(um.cookies, user.Cookie())

	if set, exist := um.usersInGates[user.GateID()]; exist {
		delete(set, user.Cookie())
	}
}

func (um *UserManager) GetUserByCookie(id uint64) *User {
	return um.cookies[id]
}

func (um *UserManager) GetUnauthUser(uuid string, serverID uint64) *User {
	if set, exist := um.unauths[uuid]; exist {
		if user, exist := set[serverID]; exist {
			return user
		}
	}
	return nil
}

func (um *UserManager) GetUser(id uint64) *User {
	if user, exist := um.users[id&userGroupOP][id]; exist {
		return user
	}
	return nil
}

func (um *UserManager) GetUserWithCache(id uint64) *User {
	u := um.GetUser(id)
	if u == nil {
		u = um.Cache().GetUser(id)
	}
	return u
}

func (um *UserManager) addUser(user *User) {
	um.users[user.ID()&userGroupOP][user.ID()] = user
	um.GetBattleDataCache().OnUserOnline(user.ID())
}

func (um *UserManager) addOnlineLevelUser(uid uint64, level uint32) {
	if level < goxml.GetData().ConfigInfoM.FriendRecommendMinLevel {
		return
	}
	if level > goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel {
		level = goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel
	}
	if um.onlineUsersByLevel[level] == nil {
		um.onlineUsersByLevel[level] = make(map[uint64]struct{})
	}
	um.onlineUsersByLevel[level][uid] = struct{}{}
}

func (um *UserManager) removeOnlineLevelUser(uid uint64, level uint32) {
	if level >= goxml.GetData().ConfigInfoM.FriendRecommendMinLevel {
		//删除老的等级
		if level >= goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel {
			level = goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel
		}
		delete(um.onlineUsersByLevel[level], uid)
	}

}

func (um *UserManager) ChangeOnlineLevelUser(uid uint64, oldLevel, newLevel uint32) {
	um.removeOnlineLevelUser(uid, oldLevel)
	um.addOnlineLevelUser(uid, newLevel)
}

func (um *UserManager) addOsOnline(os string) {
	if os != "" {
		um.onlineNumByWmOs[os]++
	}
}

func (um *UserManager) removeOsOnline(os string) {
	if os != "" && um.onlineNumByWmOs[os] > 0 {
		um.onlineNumByWmOs[os]--
	}
}

func (um *UserManager) GetOsOnline() map[string]int {
	return um.onlineNumByWmOs
}

func (um *UserManager) deleteUser(user *User) {
	delete(um.users[user.ID()&userGroupOP], user.ID())
}

func (um *UserManager) DeleteUsersByGate(id uint64, srv servicer) {
	var ret []uint64
	if set, exist := um.usersInGates[id]; exist {
		ret = make([]uint64, 0, len(set))
		for cookie := range set {
			ret = append(ret, cookie)
		}
		delete(um.usersInGates, id)
	}
	for _, cookie := range ret {
		user := um.GetUserByCookie(cookie)
		if user != nil {
			um.UserOffline(user, srv, uint32(common.OFFLINE_REASON_RESTART))
		}
	}
}

// 玩家上线, 从数据库登录上线
func (um *UserManager) UserOnline(user *User, info interface{}, srv servicer) uint32 {
	result := user.online(info, srv)
	if result == uint32(ret.RET_OK) {
		um.addUser(user)
	}
	return result
}

func (um *UserManager) UserOffline(user *User, srv servicer, reason uint32) {
	um.deleteIndex(user)
	um.deleteUser(user)

	lastState := user.State()
	user.offline(srv, reason)
	if lastState == StateOnline {
		um.removeOsOnline(user.wmos)
		um.removeOnlineLevelUser(user.ID(), user.Level()) //这个必须是已登录过的玩家下线
		user.Save(srv)
		um.cache.add(user, srv.GetUserCacheInterval())
		l4g.Infof("user offline, id: %d uuid: %s serverID: %d name: %s cookie: %d",
			user.ID(), user.UUID(), user.ServerID(), user.Name(), user.Cookie())
	} else {
		l4g.Infof("user offline, uuid: %s serverID: %d, cookie: %d state: %d",
			user.UUID(), user.ServerID(), user.Cookie(), lastState)
	}
	user.clearNetInfo()
}

// 封禁账号
func (um *UserManager) BanAccount(uid uint64, srv servicer) {
	um.KillUser(srv, uid, uint32(common.OFFLINE_REASON_GM_KICK))
}

// 踢除账号
func (um *UserManager) KickAccount(uid uint64, srv servicer) {
	um.KillUser(srv, uid, uint32(common.OFFLINE_REASON_GM_KICK))
}

func (um *UserManager) Foreach(srv servicer, now int64) {
	dailyRefreshTime := helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now)
	weekRefreshTime := helper.GetResetTime(uint32(common.RESET_TYPE_WEEKLY), now)
	monthlyRefreshTime := helper.GetResetTime(uint32(common.RESET_TYPE_MONTHLY), now)
	weekFridayRefreshTime := helper.GetResetTime(uint32(common.RESET_TYPE_WEEKLY_FRIDAY), now)

	for _, user := range um.users[um.lastForeachIndex&userGroupOP] {
		user.update(srv, now, dailyRefreshTime, weekRefreshTime, monthlyRefreshTime, weekFridayRefreshTime)
	}
	um.lastForeachIndex++
	if um.lastForeachIndex&userGroupOP == 0 {
		um.MailBox().Update(now)
		//保存邮件数据
		um.saveMailBox(srv)
		//离线修改资源落库
		um.saveOfflineResource(srv)
	}
}

func (um *UserManager) GetOnlineNum() int {
	userSize := 0
	for i := 0; i < UserGroup; i++ {
		userSize += len(um.users[i])
	}
	return userSize
}

func (um *UserManager) Debug(srv servicer) {
	userSize := um.GetOnlineNum()
	l4g.Infof("unauths: %d, cookies: %d onlines: %d, caches: %d",
		len(um.unauths), len(um.cookies), userSize, um.cache.size())
	for k, v := range um.usersInGates {
		l4g.Infof("gateway session users, id: %d count: %d", k, len(v))
	}
	logOnlineNum(srv, userSize)
	for k, v := range um.GetOsOnline() {
		logOsOnlineNum(srv, k, v)
	}
	CharacterMetrics.StateInFlight.WithLabelValues("online").Set(float64(userSize))
	CharacterMetrics.StateInFlight.WithLabelValues("unauths").Set(float64(len(um.unauths)))
	CharacterMetrics.StateInFlight.WithLabelValues("cookies").Set(float64(len(um.cookies)))
	CharacterMetrics.StateInFlight.WithLabelValues("caches").Set(float64(um.cache.size()))
}

// 外部加载完成以后，设置
func (um *UserManager) SetMailBox(mails *mail.Box) {
	um.mails = mails
}

func (um *UserManager) MailBox() *mail.Box {
	return um.mails
}

func (um *UserManager) Range(fn func(u *User)) {
	for i := 0; i < UserGroup; i++ {
		us := um.users[i]
		for _, u := range us {
			fn(u)
		}
	}
}

func (um *UserManager) RandRange(srv servicer, fn func(u *User) bool) {
	g := srv.Rand().Intn(UserGroup)
	for i := 0; i < UserGroup; i++ {
		ni := g + i
		if ni >= UserGroup {
			ni -= UserGroup
		}
		us := um.users[ni]
		for _, u := range us {
			if !fn(u) {
				return
			}
		}
	}
}

// 根据等级范围来筛选玩家
func (um *UserManager) RandRangeByLevel(srv servicer, level uint32, fn func(u *User) bool) {
	if level > goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel {
		level = goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel
	}
	//l4g.Debug("um onlineUserByLevel level:%d uid:%+v", level, um.onlineUsersByLevel[level])
	for k := range um.onlineUsersByLevel[level] {
		user := um.GetUser(k)
		if user != nil {
			if !fn(user) {
				return
			}
		}
	}
}

func (um *UserManager) saveMailBox(srv servicer) {
	data := &r2l.OpMails{}
	if um.MailBox().Save(data) {
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_OpMails), um.MailBox().Owner(), &r2l.L2R_OpMails{
			Data: data,
		})
	}
}

func (um *UserManager) Close(srv servicer) {
	um.Range(func(u *User) {
		//关服的时候需要修改下玩家的离线时间,否则赛季回流等依赖这个时间计算的玩法会有问题
		u.dbUser.Base.OfflineTime = time.Now().Unix()
		u.setSaveTag(saveTagBase)
		u.Save(srv)
	})
	um.cache.clear(srv)
}

func (um *UserManager) CookiesSize() int {
	return len(um.cookies)
}

func (um *UserManager) RecommendFriends1(user *User, num uint32) []*cl.UserSnapshot {
	list := make([]*cl.UserSnapshot, 0, num)
	for _, v := range um.cookies {
		if v.ID() != user.ID() {
			list = append(list, v.NewUserSnapshot(0))
			if uint32(len(list)) >= num {
				break
			}
		}
	}
	return list
}

/*
func (um *UserManager) TestBattleData(srv servicer, cmd uint32, userID uint64, formationID uint32) {
	u := um.GetUser(userID)
	power := u.CalFormationPower(formationID)

	smsg := &cl.L2C_TestBattleData{
		Ret: uint32(ret.RET_OK),
	}

	battleData := GetBattleDataFromUser(u, formationID)
	if battleData == nil {
		l4g.Errorf("user have not formation data , userId: %d formationId: %d", userID, formationID)
		smsg.Ret = uint32(ret.RET_ERROR)
		u.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return
	}
	newUser := GetUserFromUserBattleData(srv,battleData)

	newPower := newUser.CalFormationPower(formationID)

	if power != newPower {
		l4g.Errorf("user's  power %d not equal newUser's power %d, userId: %d", power, newPower, userID)
		smsg.Ret = uint32(ret.RET_ERROR)
		u.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return
	}

	um.DoUserBattleData(srv, []uint64{userID}, u.Args(cmd), formationID,
		func(cbsrv interface{}, cbdata interface{}, args *Args, cbret uint32) bool {
			return AsyncTestBattleData(cbsrv.(servicer), cbdata.([]*db.UserBattleData), args, formationID, power, cbret, smsg)
		}, nil)
}

func AsyncTestBattleData(srv servicer, cbBattleData []*db.UserBattleData,
	args *Args, formationID uint32, power int64, retCode uint32, smsg *cl.L2C_TestBattleData) bool {
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d L2C_TesetBattleData: get battleData error:%d",
			args.UID, retCode)
		smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return false
	}
	newCBUser := GetUserFromUserBattleData(srv,cbBattleData[0])
	if newCBUser == nil {
		l4g.Errorf("get user from UserBattleData error userId: %d, formationId: %d", args.UID, formationID)
		smsg.Ret = uint32(ret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return false
	}

	newCBPower := newCBUser.CalFormationPower(formationID)

	if power != newCBPower {
		l4g.Errorf("user's  power %d not equal dbUser's power %d, userId: %d", power, newCBPower, args.UID)
		smsg.Ret = uint32(ret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return false
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
	return true

}

*/

// 强制下线
func (um *UserManager) KillUser(srv servicer, uid uint64, reason uint32) {
	if u := um.GetUser(uid); u != nil {
		um.UserOffline(u, srv, reason)
	}
	if u := um.cache.GetUser(uid); u != nil {
		um.cache.delete(u)
	}
	l4g.Infof("[UserManager] kick user:%d reason: %d", uid, reason)
}

func (um *UserManager) AddWrongUser(uid uint64, srv servicer) {
	if uid == 0 {
		return
	}
	if u := um.GetUser(uid); u != nil {
		um.wrongUsers[u.UUID()] = time.Now().Unix()
	}

	um.KillUser(srv, uid, uint32(common.OFFLINE_REASON_WRONG))
	CharacterMetrics.MsgsCount.WithLabelValues("userWrong").Inc()
}

func (um *UserManager) IsWrong(UUID string, now int64) bool {
	lastTime, exist := um.wrongUsers[UUID]
	if !exist {
		return false
	}
	if now-lastTime > WrongUserLimitTime {
		delete(um.wrongUsers, UUID)
		return false
	}
	return true
}

func (um *UserManager) AddLastLoginUserName(name string) {
	if name == "" {
		return
	}
	for _, na := range um.lastLoginUserName {
		if na == name {
			return
		}
	}

	if len(um.lastLoginUserName) == um.lastLoginIndexMax {
		um.lastLoginUserName[um.lastLoginIndex] = name
		um.lastLoginIndex++
		if um.lastLoginIndex == um.lastLoginIndexMax {
			um.lastLoginIndex = 0
		}
	} else if len(um.lastLoginUserName) < um.lastLoginIndexMax {
		um.lastLoginUserName = append(um.lastLoginUserName, name)
	}
}

func (um *UserManager) GetLastLoginUserNameClone(userName string) []string {
	ret := make([]string, 0, len(um.lastLoginUserName))
	for _, name := range um.lastLoginUserName {
		if userName == name {
			continue
		}
		tmp := name
		ret = append(ret, tmp)
	}
	return ret
}

func (um *UserManager) GetBattleDataCache() *OfflineUserBattleDataCache {
	return um.userBattleDataCache
}

type OfflineUserBattleDataCache struct {
	cache         map[string]*db.UserBattleData
	userCacheKeys map[uint64]map[string]*BattleDataCacheKey
	expired       *btree.BTree
	maxEntries    int
}

type BattleDataCacheKey struct {
	key              string
	uid              uint64
	cacheExpiredTime int64
}

func (e *BattleDataCacheKey) Less(than btree.Item) bool {
	other := than.(*BattleDataCacheKey)
	if e.cacheExpiredTime < other.cacheExpiredTime {
		return true
	}
	if e.cacheExpiredTime == other.cacheExpiredTime && e.key < other.key {
		return true
	}
	return false
}

func newBattleDataCache(maxSize int) *OfflineUserBattleDataCache {
	cache := &OfflineUserBattleDataCache{
		cache:         make(map[string]*db.UserBattleData),
		userCacheKeys: make(map[uint64]map[string]*BattleDataCacheKey),
		expired:       btree.New(32), //nolint:mnd
		maxEntries:    maxSize,
	}
	return cache
}

func (cache *OfflineUserBattleDataCache) genKey(uid uint64, formationID uint32) string {
	return strconv.FormatUint(uid, 10) + "f" + strconv.FormatUint(uint64(formationID), 10)
}

func (cache *OfflineUserBattleDataCache) genBattleDataCacheKey(uid uint64, formationID uint32, expireTime int64) *BattleDataCacheKey {
	cacheKey := &BattleDataCacheKey{
		uid:              uid,
		key:              cache.genKey(uid, formationID),
		cacheExpiredTime: time.Now().Unix() + expireTime,
	}
	return cacheKey
}

func (cache *OfflineUserBattleDataCache) AddBattleUser(uid uint64, formationID uint32, data *db.UserBattleData, expireTime int64) {
	if expireTime > 0 {
		key := cache.genKey(uid, formationID)
		cache.cache[key] = data
		cacheKey := cache.genBattleDataCacheKey(uid, formationID, expireTime)
		cacheKeyMap := cache.userCacheKeys[uid]
		if cacheKeyMap == nil {
			cacheKeyMap = make(map[string]*BattleDataCacheKey)
			cache.userCacheKeys[uid] = cacheKeyMap
		}
		cacheKeyMap[cacheKey.key] = cacheKey
		cache.expired.ReplaceOrInsert(cacheKey)
		l4g.Infof("move user %d formation %d to BattleDataCache(size: %d)", uid, formationID, cache.size())
	}
}

func (cache *OfflineUserBattleDataCache) GetBattleUser(uid uint64, formationID uint32) *db.UserBattleData {
	key := cache.genKey(uid, formationID)
	return cache.cache[key]
}

func (cache *OfflineUserBattleDataCache) DelBattleUser(uid uint64, formationID uint32) {
	key := cache.genKey(uid, formationID)
	cache.delBattleUser(uid, key)
}

func (cache *OfflineUserBattleDataCache) delBattleUser(uid uint64, key string) {
	delete(cache.cache, key)
	cacheKeyMap := cache.userCacheKeys[uid]
	if cacheKeyMap != nil {
		cacheKey := cacheKeyMap[key]
		if cacheKey != nil {
			cache.expired.Delete(cacheKey)
			delete(cacheKeyMap, key)
		}
	}
	l4g.Infof("delete user %d key %s from BattleDataCache(size: %d)", uid, key, cache.size())
}

func (cache *OfflineUserBattleDataCache) OnUserOnline(uid uint64) {
	cacheKeyMap := cache.userCacheKeys[uid]
	for key := range cacheKeyMap {
		cache.delBattleUser(uid, key)
	}
	delete(cache.userCacheKeys, uid)
}

func (cache *OfflineUserBattleDataCache) size() int {
	return cache.expired.Len()
}

func (cache *OfflineUserBattleDataCache) Evict(srv servicer) {
	max := 100
	var items []*BattleDataCacheKey
	pivot := &BattleDataCacheKey{
		cacheExpiredTime: time.Now().Unix(),
	}
	var num int
	//删除超时的数据
	cache.expired.AscendLessThan(pivot, func(i btree.Item) bool {
		items = append(items, i.(*BattleDataCacheKey))
		num++
		return num < max
	})
	for _, item := range items {
		cache.delBattleUser(item.uid, item.key)
	}
	if num >= max || cache.size() <= cache.maxEntries {
		return
	}
	items = items[:0]
	//删除过量的数据
	count := cache.size() - cache.maxEntries
	cache.expired.Ascend(func(i btree.Item) bool {
		items = append(items, i.(*BattleDataCacheKey))
		count--
		num++
		return (num < max && count != 0)
	})
	for _, item := range items {
		cache.delBattleUser(item.uid, item.key)
	}
}

type offlineResourceCache struct {
	resources map[uint64]*db.OfflineResource

	changes map[uint64]*db.OfflineResource
	deletes map[uint64]struct{}
}

func newOfflineResourceCache() *offlineResourceCache {
	cache := &offlineResourceCache{
		resources: make(map[uint64]*db.OfflineResource),
		changes:   make(map[uint64]*db.OfflineResource),
		deletes:   make(map[uint64]struct{}),
	}
	return cache
}

// 加载数据
func (um *UserManager) LoadOfflineResource(resources map[uint64]*db.OfflineResource) {
	if len(resources) == 0 {
		return
	}
	um.orCache.resources = resources
}

func (um *UserManager) saveOfflineResource(srv servicer) {
	msg := &r2l.L2R_SaveOfflineResource{}
	hadChanged := false
	changes := len(um.orCache.changes)
	if changes > 0 {
		msg.Changes = make([]*db.OfflineResource, 0, changes)
		for uid, cResource := range um.orCache.changes {
			msg.Changes = append(msg.Changes, cResource.Clone())
			delete(um.orCache.changes, uid)
		}
		hadChanged = true
	}
	deletes := len(um.orCache.deletes)
	if deletes > 0 {
		msg.Deletes = make([]uint64, 0, deletes)
		for id := range um.orCache.deletes {
			msg.Deletes = append(msg.Deletes, id)
			delete(um.orCache.deletes, id)
		}
		hadChanged = true
	}
	if !hadChanged {
		return
	}
	l4g.Errorf("ID_MSG_L2R_SaveOfflineResource ")
	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveOfflineResource), 0, msg)
}

func (um *UserManager) addOfflineResource(uid uint64, items []*db.OfflineItem) {
	resource, exist := um.orCache.resources[uid]
	if !exist {
		resource = &db.OfflineResource{
			Id: uid,
		}
	}
	resource.Items = append(resource.Items, items...)
	um.orCache.resources[uid] = resource
}

// 加减离线资源
// count: >0:增加 <0:减少
func (um *UserManager) ChangeOfflineResource(uid uint64, item *db.OfflineItem) {
	l4g.Errorf("ChangeOfflineResource uid:%d %v", uid, item)
	um.addOfflineResource(uid, []*db.OfflineItem{item})
}

// 离线资源结算
func (um *UserManager) StatOfflineResource(srv servicer, user *User) {
	uid := user.ID()
	resource, exist := um.orCache.resources[uid]
	if !exist {
		return
	}
	for _, item := range resource.Items {
		resources := []*cl.Resource{{
			Type:  item.Type,
			Value: item.Value,
		}}
		if item.Count > 0 {
			resources[0].Count = uint32(item.Count)
			aret, _ := user.Award(srv, resources, AwardTagMail, item.Reason, item.SubReason)
			if aret != uint32(ret.RET_OK) {
				l4g.Errorf("user:%d StatOfflineResource: award error ret:%d data:%+v", uid, aret, resource)
				return
			}
		}
		if item.Count < 0 {
			resources[0].Count = uint32(0 - item.Count)
			user.ConsumeDontCheckResourcesSize(srv, resources, item.Reason, item.SubReason)
		}
	}
	delete(um.orCache.resources, uid)
	um.orCache.deletes[uid] = struct{}{}
}
