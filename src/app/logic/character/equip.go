package character

import (
	"app/goxml"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"gitlab.qdream.com/kit/sea/math/rand"

	l4g "github.com/ivanabc/log4go"
)

type Equip struct {
	Data *cl.Equipment
}

func newEquip(srv servicer, id uint64, sysID uint32) *Equip {
	data := &cl.Equipment{
		Id:             id,
		SysId:          sysID,
		StrengthLevel:  EquipInitialStrengthLevel,
		RefineExp:      EquipInitialRefineExp,
		RefineLevel:    EquipInitialRefineLevel,
		EnchantLevel:   EquipInitialEnchantLevel,
		EvolutionLevel: EquipInitialEvolutionLevel,
	}
	equip := &Equip{}
	equip.Load(data)
	equip.SetEquipEnchantSeed(equip.RandEquipEnchantSeedId(srv.Rand()))
	return equip
}

func (e *Equip) Load(data *cl.Equipment) *Equip {
	e.Data = data
	return e
}

func (e *Equip) Flush() *cl.Equipment {
	if e != nil {
		return e.Data.Clone()
	}
	return nil
}

func (e *Equip) SetHid(id uint64) {
	e.Data.Hid = id
}

func (e *Equip) AddRefineLevel(num uint32) {
	e.Data.RefineLevel += num
}

func (e *Equip) SetRefineExp(newExp uint32) {
	e.Data.RefineExp = newExp
}

func (e *Equip) SetEnchantLevel(newLevel uint32) {
	e.Data.EnchantLevel = newLevel
}

func (e *Equip) AddEvolutionLevel(num uint32) {
	e.Data.EvolutionLevel += num
}

func (e *Equip) Reborn(srv servicer) { // 注：这里会重置hid，调用时注意
	e.Data.StrengthLevel = EquipInitialStrengthLevel
	e.Data.RefineExp = EquipInitialRefineExp
	e.Data.RefineLevel = EquipInitialRefineLevel
	e.Data.EnchantLevel = EquipInitialEnchantLevel
	e.Data.EvolutionLevel = EquipInitialEvolutionLevel
	e.Data.Hid = 0
	e.SetEquipEnchantSeed(e.RandEquipEnchantSeedId(srv.Rand()))
	e.Data.EnchantCost = nil
}

func (e *Equip) MergeReturnRes(info *goxml.EquipInfo, uid uint64) ([]*cl.Resource, uint32) {
	var retRes []*cl.Resource

	for i := uint32(1); i < e.Data.StrengthLevel; i++ {
		strengthInfo := goxml.GetData().EquipStrengthInfoM.Index(info.Part, i)
		if strengthInfo == nil {
			l4g.Errorf("user:%d equip strengthInfo not exist part:%d, level:%d", uid, info.Part, i)
			return nil, uint32(cret.RET_CLIENT_REQUEST_ERROR)
		}
		retRes = append(retRes, strengthInfo.Costs...)
	}

	if e.Data.RefineLevel > 0 {
		for i := uint32(0); i < e.Data.RefineLevel; i++ {
			refineInfo := goxml.GetData().EquipRefineInfoM.Index(info.Part, i)
			if refineInfo == nil {
				l4g.Errorf("user:%d equip refineInfo not exist part:%d, level:%d", uid, info.Part, i)
				return nil, uint32(cret.RET_CLIENT_REQUEST_ERROR)
			}
			if refineInfo.OnceExp > 0 {
				num := refineInfo.NeedExp / refineInfo.OnceExp
				costs := goxml.GetData().EquipRefineInfoM.GetCosts(num, refineInfo)
				retRes = append(retRes, costs...)
			}
		}
	}
	if e.Data.RefineExp > 0 {
		refineInfo := goxml.GetData().EquipRefineInfoM.Index(info.Part, e.Data.RefineLevel)
		if refineInfo.OnceExp > 0 {
			num := e.Data.RefineExp / refineInfo.OnceExp
			costs := goxml.GetData().EquipRefineInfoM.GetCosts(num, refineInfo)
			retRes = append(retRes, costs...)
		}
	}

	if len(e.Data.EnchantCost) > 0 {
		retRes = append(retRes, e.Data.EnchantCost...)
	}

	if info.EvolutionId > 0 {
		evolutionRes, retR := e.GetEvolutionReturnRes(info, uid)
		if retR != uint32(cret.RET_OK) {
			return nil, uint32(cret.RET_CLIENT_REQUEST_ERROR)
		}
		retRes = append(retRes, evolutionRes...)
	}

	return retRes, uint32(cret.RET_OK)
}

func (e *Equip) GetEvolutionReturnRes(info *goxml.EquipInfo, uid uint64) ([]*cl.Resource, uint32) {
	var retRes []*cl.Resource
	if info.EvolutionId > 0 {
		for i := uint32(0); i < e.Data.EvolutionLevel; i++ {
			evolutionInfo := goxml.GetData().EquipEvolutionInfoM.Index(info.EvolutionId, i)
			if evolutionInfo == nil {
				l4g.Errorf("user:%d equip evolutionInfo not exist evolutionId:%d, level:%d",
					uid, info.EvolutionId, i)
				return nil, uint32(cret.RET_CLIENT_REQUEST_ERROR)
			}
			retRes = append(retRes, evolutionInfo.Costs...)
		}
	}

	return retRes, uint32(cret.RET_OK)
}

func (e *Equip) CheckEquipIsInitial() bool {
	if e.Data.StrengthLevel == EquipInitialStrengthLevel && e.Data.RefineExp == EquipInitialRefineExp &&
		e.Data.RefineLevel == EquipInitialRefineLevel && e.Data.EvolutionLevel == EquipInitialEvolutionLevel &&
		e.Data.EnchantLevel == EquipInitialEnchantLevel {
		return true
	}
	return false
}

func (e *Equip) CalcEquipAttrs(user *User) {
	user.ClearAttrCache()
	attrs := user.GetAttrCache()
	equipInfo := goxml.GetData().EquipInfoM.Index(e.Data.SysId)
	if equipInfo == nil {
		l4g.Errorf("equipInfo not exist uid:%d sysID:%d", user.ID(), e.Data.SysId)
		return
	}
	attrCoeInfo := goxml.GetData().EquipAttrCoeInfoM.Index(equipInfo.Rare)
	if attrCoeInfo == nil {
		l4g.Errorf("attrCoeInfo not exist uid:%d sysID:%d", user.ID(), equipInfo.Rare)
		return
	}
	// 基础属性
	attrs[equipInfo.AttrType] += int64(equipInfo.AttrValue)
	// 强化属性
	goxml.GetData().EquipStrengthInfoM.GetAttr(
		attrs, equipInfo.Part, e.Data.StrengthLevel, attrCoeInfo.StrengthCoe)
	// 精炼属性
	goxml.GetData().EquipRefineInfoM.GetAttr(
		attrs, equipInfo.Part, e.Data.RefineLevel, e.Data.RefineExp, attrCoeInfo.RefineCoe)
	// 精炼技巧属性
	goxml.GetData().EquipRefineTechniqueInfoM.GetAttr(
		attrs, equipInfo.Id, e.Data.RefineLevel)
	// 附魔属性
	goxml.GetData().EquipEnchantInfoM.GetAttr(
		attrs, equipInfo.Part, e.Data.EnchantLevel, attrCoeInfo.EnchantCoe)
	// 进阶属性
	goxml.GetData().EquipEvolutionInfoM.GetAttr(
		attrs, equipInfo.EvolutionId, e.Data.EvolutionLevel)
}

func (e *Equip) CalcEquipScore(user *User) int64 {
	return user.CalcCultivateScore()
}

func (e *Equip) GetEquipScore() int64 {
	return e.Data.Score
}

func (e *Equip) SetEquipScore(score int64) {
	e.Data.Score = score
}

func (e *Equip) SetEquipEnchantSeed(seed uint32) {
	e.Data.EnchantSeed = seed
}

func (e *Equip) RandEquipEnchantSeedId(rd *rand.Rand) uint32 {
	length := goxml.GetData().EquipEnchantRandInfoM.RandSeedLength()
	return uint32(rd.RandBetween(0, int(length-1)))
}

func (e *Equip) GetEquipEnchantSeed() uint32 {
	return e.Data.EnchantSeed
}
