package character

import (
	"app/goxml"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"gitlab.qdream.com/kit/sea/time"
)

type Duel struct {
	owner *User
	data  *cl.Duel

	change bool
}

func newDuel(user *User) *Duel {
	return &Duel{
		owner: user,
		data: &cl.Duel{
			DuelStatus: uint32(common.DUEL_STATUS_TYPE_DSST_OPEN),
		},
	}
}

func (d *Duel) Owner() *User {
	return d.owner
}

func (d *Duel) Load(data *cl.Duel) {
	d.data = data
	if d.data == nil {
		d.data = &cl.Duel{
			DuelStatus: uint32(common.DUEL_STATUS_TYPE_DSST_OPEN),
		}
	}
}

func (d *Duel) SetChange() {
	d.change = true
}

func (d *Duel) save(op *r2l.OpDuel) {
	if !d.change {
		return
	}
	op.Data = d.data.Clone()
	d.change = false
}

func (d *Duel) Flush() *cl.Duel {
	return d.data.Clone()
}

func (d *Duel) GetStatus() uint32 {
	return d.data.DuelStatus
}

func (d *Duel) SetStatus(status uint32) {
	d.data.DuelStatus = status
	d.SetChange()
}

func (d *Duel) CanBeDueledByOtherUser() bool {
	return d.data.DuelStatus == uint32(common.DUEL_STATUS_TYPE_DSST_OPEN)
}

func (d *Duel) GetLastDuelTime() int64 {
	return d.data.LastDuelTime
}

func (d *Duel) SetLastDuelTime(lastDuelTime int64) {
	d.data.LastDuelTime = lastDuelTime
	d.SetChange()
}

func (d *Duel) CheckAndSetOperateInterval(smsg *cl.L2C_DuelFight) bool {
	smsg.LastDuelTime = d.GetLastDuelTime()

	interval := int64(goxml.GetData().ConfigInfoM.DuelOperateInterval)
	if interval != 0 && time.Now().Unix()-d.GetLastDuelTime() < interval {
		return false
	}

	return true
}
