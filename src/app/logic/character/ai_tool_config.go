//nolint:wastedassign
package character

import (
	"app/logic/helper"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/math/rand"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

var (
	//aiToolConfigPath         = "../data/"
	aiToolsDevelopM          *AiToolsDevelopManager
	aiToolsHeroExclusiveM    *AiToolsHeroExclusiveManager
	aiToolsHeroGradeM        *AiToolsHeroGradeManager
	aiToolsLineupM           *AiToolsLineupManager
	aiToolsOverallSituationM *AiToolsOverallSituationManager
	TestAiToolsHeroM         *TestAiToolsHeroManager
	TestAiToolsArtifactM     *TestAiToolsArtifactManager
	TestAiToolsPokemonM      *TestAiToolsPokemonManager
)

func InitAiToolConfig(dataPath string) {
	aiToolsStartID = helper.CreateFirstID(1)
	aiToolsRd = rand.New(time.Now().UnixNano())

	aiToolsDevelopM = new(AiToolsDevelopManager)
	aiToolsHeroExclusiveM = new(AiToolsHeroExclusiveManager)
	aiToolsHeroGradeM = new(AiToolsHeroGradeManager)
	aiToolsLineupM = new(AiToolsLineupManager)
	aiToolsOverallSituationM = new(AiToolsOverallSituationManager)
	TestAiToolsHeroM = new(TestAiToolsHeroManager)
	TestAiToolsArtifactM = new(TestAiToolsArtifactManager)
	TestAiToolsPokemonM = new(TestAiToolsPokemonManager)

	aiToolsDevelopM.Load(dataPath)
	aiToolsHeroExclusiveM.Load(dataPath)
	aiToolsHeroGradeM.Load(dataPath)
	aiToolsLineupM.Load(dataPath)
	aiToolsOverallSituationM.Load(dataPath)
	TestAiToolsHeroM.Load(dataPath)
	TestAiToolsArtifactM.Load(dataPath)
	TestAiToolsPokemonM.Load(dataPath)
}

type AiToolsDevelop struct {
	Id               uint32 `xml:"id,attr"`                 //
	HeroLevels       uint32 `xml:"hero_levels,attr"`        //int:英雄等级
	HeroStars        uint32 `xml:"hero_stars,attr"`         //int:英雄星级
	HeroStages       uint32 `xml:"hero_stages,attr"`        //int:英雄突破等级
	HeroAwakenLevels uint32 `xml:"hero_awaken_levels,attr"` //int:英雄觉醒等级
	Gemlevel         uint32 `xml:"gemlevel,attr"`           //int:宝石等级
	EquipId          string `xml:"equip_id,attr"`           //string:装备ID（四件）
	EquipStrength    uint32 `xml:"equip_strength,attr"`     //int:装备强化
	EquipEnchant     uint32 `xml:"equip_enchant,attr"`      //int:装备附魔
	EquipRefine      uint32 `xml:"equip_refine,attr"`       //int:装备精炼
	EquipEvolution   uint32 `xml:"equip_evolution,attr"`    //int:装备进阶
	ArtifactStars    uint32 `xml:"artifact_stars,attr"`     //int:神器星级
	ArtifactStrength uint32 `xml:"artifact_strength,attr"`  //int:神器强化
	EmblemGrade      uint32 `xml:"emblem_grade,attr"`       //int:纹章品质（调取表4）
	EmblemLevel      uint32 `xml:"emblem_level,attr"`       //int:纹章强化
	SeasonId         uint32 `xml:"season_id,attr"`          //int:赛季id
	Season1LinkLevel uint32 `xml:"season1_link_level,attr"` //int:s1赛季羁绊丰碑玩法等级
	PokemonStars     uint32 `xml:"pokemon_stars,attr"`      //int:宠物星级
	Rare             uint32 `xml:"rare,attr"`               // 英雄品质
}

type AiToolsDevelops struct {
	Datas []*AiToolsDevelop `xml:"data"`
}

type AiToolsDevelopManager struct {
	Datas map[uint32]*AiToolsDevelop
}

func (a *AiToolsDevelopManager) Load(dir string) {
	tmp := &AiToolsDevelops{}
	fileName := filepath.Join(dir, "test_ai_tools_develop.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}
	if a.Datas == nil {
		a.Datas = make(map[uint32]*AiToolsDevelop, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		if ptr, exist := a.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			a.Datas[data.Id] = data
		}
	}
}

func (a *AiToolsDevelopManager) Index(id uint32) *AiToolsDevelop {
	return a.Datas[id]
}

type AiToolsHeroExclusive struct {
	Id            uint32 `xml:"id,attr"`               //
	HeroId        uint32 `xml:"hero_id,attr"`          //int:英雄ID
	EmblemGrade   uint32 `xml:"emblem_grade,attr"`     //int:纹章品质
	EmblemId1     uint32 `xml:"emblem_id1,attr"`       //int:纹章ID1
	EmblemId2     uint32 `xml:"emblem_id2,attr"`       //int:纹章ID2
	EmblemId3     uint32 `xml:"emblem_id3,attr"`       //int:纹章ID3
	EmblemId4     uint32 `xml:"emblem_id4,attr"`       //int:纹章ID4
	IsHeroGroupId uint32 `xml:"is_hero_group_id,attr"` //int:纹章专属英雄ID
}

type AiToolsHeroExclusives struct {
	Datas []*AiToolsHeroExclusive `xml:"data"`
}

type AiToolsHeroExclusiveManager struct {
	Datas map[uint32]map[uint32]*AiToolsHeroExclusive
}

func (a *AiToolsHeroExclusiveManager) Load(dir string) {
	tmp := &AiToolsHeroExclusives{}
	fileName := filepath.Join(dir, "test_ai_tools_hero_exclusive.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}
	a.Datas = make(map[uint32]map[uint32]*AiToolsHeroExclusive, len(tmp.Datas))
	for _, data := range tmp.Datas {
		if _, exist := a.Datas[data.HeroId]; !exist {
			a.Datas[data.HeroId] = make(map[uint32]*AiToolsHeroExclusive)
		}
		a.Datas[data.HeroId][data.EmblemGrade] = data
	}
}

func (a *AiToolsHeroExclusiveManager) Index(heroID, emblemGrade uint32) *AiToolsHeroExclusive {
	return a.Datas[heroID][emblemGrade]
}

type AiToolsHeroGrade struct {
	Id                     uint32 `xml:"id,attr"`                       //
	Hero1                  uint32 `xml:"hero_1,attr"`                   //int:主力1
	Hero2                  uint32 `xml:"hero_2,attr"`                   //int:主力2
	Hero3                  uint32 `xml:"hero_3,attr"`                   //int:主力3
	Hero4                  uint32 `xml:"hero_4,attr"`                   //int:主力4
	Hero5                  uint32 `xml:"hero_5,attr"`                   //int:主力5
	Artifact1              uint32 `xml:"artifact_1,attr"`               //int:神器1号位
	Artifact2              uint32 `xml:"artifact_2,attr"`               //int:神器2号位
	Artifact3              uint32 `xml:"artifact_3,attr"`               //int:神器3号位
	OverallSituationId     uint32 `xml:"overall_situation_id,attr"`     //int:全局养成
	HeroX                  uint32 `xml:"hero_x,attr"`                   //int:全局英雄
	ArtifactX              uint32 `xml:"artifact_x,attr"`               //int:全局神器
	Season1LinkX           uint32 `xml:"season1_link_x,attr"`           //int:s1赛季羁绊
	Pokemon1               uint32 `xml:"pokemon_1,attr"`                //int:宠物1号位
	Pokemon2               uint32 `xml:"pokemon_2,attr"`                //int:宠物2号位
	Pokemon3               uint32 `xml:"pokemon_3,attr"`                //int:宠物3号位
	PokemonGlobalStar      uint32 `xml:"pokemon_global_star,attr"`      //int:全局宠物星级
	PokemonGlobalPotantial uint32 `xml:"pokemon_global_potantial,attr"` //int:全局宠物潜能
}

type AiToolsHeroGrades struct {
	Datas []*AiToolsHeroGrade `xml:"data"`
}

type AiToolsHeroGradeManager struct {
	Datas map[uint32]*AiToolsHeroGrade
}

func (a *AiToolsHeroGradeManager) Load(dir string) {
	tmp := &AiToolsHeroGrades{}
	fileName := filepath.Join(dir, "test_ai_tools_hero_grade.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}
	if a.Datas == nil {
		a.Datas = make(map[uint32]*AiToolsHeroGrade, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		if ptr, exist := a.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			a.Datas[data.Id] = data
		}
	}
}

func (a *AiToolsHeroGradeManager) Index(id uint32) *AiToolsHeroGrade {
	return a.Datas[id]
}

type AiToolsLineup struct {
	Id            uint32 `xml:"id,attr"`              //
	Hero1         uint32 `xml:"hero_1,attr"`          //int:主力1
	Hero2         uint32 `xml:"hero_2,attr"`          //int:主力2
	Hero3         uint32 `xml:"hero_3,attr"`          //int:主力3
	Hero4         uint32 `xml:"hero_4,attr"`          //int:主力4
	Hero5         uint32 `xml:"hero_5,attr"`          //int:主力5
	Hero1Position uint32 `xml:"hero_1_position,attr"` //int:英雄1站位顺序
	Hero2Position uint32 `xml:"hero_2_position,attr"` //int:英雄2站位顺序
	Hero3Position uint32 `xml:"hero_3_position,attr"` //int:英雄3站位顺序
	Hero4Position uint32 `xml:"hero_4_position,attr"` //int:英雄4站位顺序
	Hero5Position uint32 `xml:"hero_5_position,attr"` //int:英雄5站位顺序
	Artifact1     uint32 `xml:"artifact_1,attr"`      //int:神器1号位
	Artifact2     uint32 `xml:"artifact_2,attr"`      //int:神器2号位
	Artifact3     uint32 `xml:"artifact_3,attr"`      //int:神器3号位
	Attack        uint32 `xml:"attack,attr"`          //int:是否设置为进攻方
	Defense       uint32 `xml:"defense,attr"`         //int:是否设置为防守方
	PokemonBall   uint32 `xml:"pokemon_ball,attr"`    //int:宠物兽栏
}

type AiToolsLineups struct {
	Datas []*AiToolsLineup `xml:"data"`
}

type AiToolsLineupManager struct {
	Datas map[uint32]*AiToolsLineup
}

func (a *AiToolsLineupManager) Load(dir string) {
	tmp := &AiToolsLineups{}
	fileName := filepath.Join(dir, "test_ai_tools_lineup.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}
	if a.Datas == nil {
		a.Datas = make(map[uint32]*AiToolsLineup, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		if ptr, exist := a.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			a.Datas[data.Id] = data
		}
	}
}

type AiToolsOverallSituation struct {
	Id                 uint32 `xml:"id,attr"`                  //
	LinkBookTaskIds    string `xml:"link_book_task_ids,attr"`  //string:图鉴属性
	GoddesscontractExp uint32 `xml:"goddesscontract_exp,attr"` //int:契约之所
	RemainBookLevel    uint32 `xml:"remain_book_level,attr"`
}

type AiToolsOverallSituations struct {
	Datas []*AiToolsOverallSituation `xml:"data"`
}

type AiToolsOverallSituationManager struct {
	Datas map[uint32]*AiToolsOverallSituation
}

func (a *AiToolsOverallSituationManager) Load(dir string) {
	tmp := &AiToolsOverallSituations{}
	fileName := filepath.Join(dir, "test_ai_tools_overall_situation.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}
	if a.Datas == nil {
		a.Datas = make(map[uint32]*AiToolsOverallSituation, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		if ptr, exist := a.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			a.Datas[data.Id] = data
		}
	}
}

func (a *AiToolsOverallSituationManager) Index(id uint32) *AiToolsOverallSituation {
	return a.Datas[id]
}

type TestAiToolsHero struct {
	Id     uint32 `xml:"id,attr"`      //
	HeroId uint32 `xml:"hero_id,attr"` //int:英雄ID
	Rare   uint32 `xml:"rare,attr"`    //int:英雄品质
}

type TestAiToolsHeros struct {
	Datas []*TestAiToolsHero `xml:"data"`
}

type TestAiToolsHeroManager struct {
	Datas map[uint32]*TestAiToolsHero
}

func (t *TestAiToolsHeroManager) Load(dir string) {
	tmp := &TestAiToolsHeros{}
	fileName := filepath.Join(dir, "test_ai_tools_hero.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}
	if t.Datas == nil {
		t.Datas = make(map[uint32]*TestAiToolsHero, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		if ptr, exist := t.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			t.Datas[data.Id] = data
		}
	}
}

type TestAiToolsArtifact struct {
	Id         uint32 `xml:"id,attr"`          //
	ArtifactId uint32 `xml:"artifact_id,attr"` //int:神器ID
}

type TestAiToolsArtifacts struct {
	Datas []*TestAiToolsArtifact `xml:"data"`
}

type TestAiToolsArtifactManager struct {
	Datas map[uint32]*TestAiToolsArtifact
}

func (t *TestAiToolsArtifactManager) Load(dir string) {
	tmp := &TestAiToolsArtifacts{}
	fileName := filepath.Join(dir, "test_ai_tools_artifact.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}

	if t.Datas == nil {
		t.Datas = make(map[uint32]*TestAiToolsArtifact, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		if ptr, exist := t.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			t.Datas[data.Id] = data
		}
	}
}

type TestAiToolsPokemon struct {
	Id         uint32 `xml:"id,attr"`           //
	PokemonId1 uint32 `xml:"pokemon_id_1,attr"` //int:宠物1号位
	PokemonId2 uint32 `xml:"pokemon_id_2,attr"` //int:宠物2号位
	PokemonId3 uint32 `xml:"pokemon_id_3,attr"` //int:宠物3号位
}

type TestAiToolsPokemons struct {
	Datas []*TestAiToolsPokemon `xml:"data"`
}

type TestAiToolsPokemonManager struct {
	Datas map[uint32]*TestAiToolsPokemon
}

func (t *TestAiToolsPokemonManager) Load(dir string) {
	tmp := &TestAiToolsPokemons{}
	fileName := filepath.Join(dir, "test_ai_tools_pokemon.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	}
	if t.Datas == nil {
		t.Datas = make(map[uint32]*TestAiToolsPokemon, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		if ptr, exist := t.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			t.Datas[data.Id] = data
		}
	}
}

func (t *TestAiToolsPokemonManager) Index(id uint32) *TestAiToolsPokemon {
	return t.Datas[id]
}
