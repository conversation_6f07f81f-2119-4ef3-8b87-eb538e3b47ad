package character

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

type Drop struct {
	owner *User
	data  *db.DropInfo
}

func newDrop(owner *User) *Drop {
	return &Drop{
		owner: owner,
	}
}

func (d *Drop) load(data *db.DropInfo) {
	d.data = data
	if d.data == nil {
		d.data = &db.DropInfo{}
	}
}

func (d *Drop) save() {
	if d.owner.dbUser.Module1.DropInfo == nil {
		d.owner.dbUser.Module1.DropInfo = d.data
	}
	d.owner.setSaveTag(saveTagModule1)
}

func (d *Drop) GetDropFailCount(groupID, dropID uint32) uint32 {
	key := dropFailKey(groupID, dropID)
	return d.data.FailCount[key]
}

func (d *Drop) AddDropFailCount(groupID, dropID, count uint32) {
	if d.data.FailCount == nil {
		d.data.FailCount = make(map[uint64]uint32)
	}
	d.data.FailCount[dropFailKey(groupID, dropID)] += count
	d.save()
}

func (d *Drop) ResetDropFailCount(groupID, dropID uint32) {
	if d.data.FailCount == nil {
		return
	}
	delete(d.data.FailCount, dropFailKey(groupID, dropID))
	d.save()
}

func dropFailKey(groupID, dropID uint32) uint64 {
	return uint64(groupID)<<32 + uint64(dropID)
}

func (d *Drop) DoDrop(rd *rand.Rand, groupID uint32) ([]*cl.Resource, bool) {
	info := goxml.GetData().DropGroupInfoM.Group(groupID)
	if info == nil {
		l4g.Error("drop group info not exist. %d", groupID)
		return nil, false
	}
	awards := make([]*cl.Resource, 0, len(info))
	for _, v := range info { //nolint:varnamelen
		for i := uint32(0); i < uint32(v.Count); i++ {
			failCount := d.GetDropFailCount(v.GroupId, v.DropId)
			if v.FixedCount > 0 && failCount+1 >= v.FixedCount { //保底必掉逻辑
				tmp := dealAwardsAndFailCount(rd, v, d)
				awards = append(awards, tmp...)
			} else {
				chance := int(v.Chance)
				if v.ChangeAdd > 0 {
					chance += int(v.ChangeAdd * float64(failCount))
				}
				if chance >= goxml.BaseInt || chance >= rd.RandBetween(1, goxml.BaseInt) {
					tmp := dealAwardsAndFailCount(rd, v, d)
					awards = append(awards, tmp...)
				} else {
					if isSpecialDrop(v) {
						d.AddDropFailCount(v.GroupId, v.DropId, 1)
					}
				}
			}
		}
	}
	return awards, true
}

// 是否属于非常规随机（概率递增或有保底必掉次数）
func isSpecialDrop(info *goxml.DropGroupInfoExt) bool {
	if info.ChangeAdd != 0 || info.FixedCount > 0 {
		return true
	}
	return false
}

// 处理奖励数据和随机失败次数
func dealAwardsAndFailCount(rd *rand.Rand, info *goxml.DropGroupInfoExt, d *Drop) []*cl.Resource {
	tmp, flag := goxml.GetRealDropAwards(goxml.GetData(), rd, info.DropId, info.DropType, uint32(1))
	awards := make([]*cl.Resource, 0, len(tmp))
	if flag {
		awards = append(awards, tmp...)
	}

	if d != nil && isSpecialDrop(info) {
		d.ResetDropFailCount(info.GroupId, info.DropId)
	}

	return awards
}

// 未挂在玩家身上的随机接口
// 仅支持概率随机，不支持保底和概率增加逻辑
// XXX 使用时，需注意其与d.DoDrop的区别
// 目前仅密林据点模式，使用此接口
func DoDrop(rd *rand.Rand, groupID uint32) ([]*cl.Resource, bool) {
	info := goxml.GetData().DropGroupInfoM.Group(groupID)
	if info == nil {
		l4g.Error("drop group info not exist. %d", groupID)
		return nil, false
	}
	awards := make([]*cl.Resource, 0, len(info))
	for _, v := range info {
		for i := uint32(0); i < uint32(v.Count); i++ {
			chance := int(v.Chance)
			if chance >= goxml.BaseInt || chance >= rd.RandBetween(1, goxml.BaseInt) {
				tmp := dealAwardsAndFailCount(rd, v, nil)
				awards = append(awards, tmp...)
			}
		}
	}
	return awards, true
}
