package character

import (
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/time"
)

type Skin struct {
	Data *cl.Skin
}

func newSkin(id, heroID uint32, endTm int64) *Skin {
	data := &cl.Skin{
		Id:      id,
		EndTime: endTm,
		HeroId:  heroID,
	}
	skin := &Skin{}
	skin.Load(data)
	return skin
}

func (s *Skin) Load(data *cl.Skin) *Skin {
	s.Data = data
	return s
}

func (s *Skin) Flush() *cl.Skin {
	if s != nil {
		return s.Data.Clone()
	}
	return nil
}

func (s *Skin) GetID() uint32 {
	return s.Data.Id
}

func (s *Skin) getEndTm() int64 {
	return s.Data.EndTime
}

func (s *Skin) GetHeroID() uint32 {
	return s.Data.HeroId
}

func (s *Skin) GetInUse() bool {
	return s.Data.InUse
}

func (s *Skin) IsForever() bool {
	return s.getEndTm() == 0
}

// 是否已过期
func (s *Skin) IsExpired() bool {
	return !s.IsForever() && time.Now().Unix() > s.getEndTm()
}

func (s *Skin) SetEndTm(tm int64) {
	s.Data.EndTime = tm
}

func (s *Skin) UpdateInUse(isUse bool) {
	s.Data.InUse = isUse
}
