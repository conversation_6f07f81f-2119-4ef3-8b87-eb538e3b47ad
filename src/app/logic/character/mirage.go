package character

import (
	"app/goxml"
	"app/logic/battle"
	"app/protos/out/cl"
)

type Mirage cl.Mirage

func newMirage(sysID uint32, affixes []uint32) *Mirage {
	return &Mirage{
		SysId:   sysID,
		Affixes: affixes,
	}
}

func (m *Mirage) flush() *cl.Mirage {
	return (*cl.Mirage)(m).Clone()
}

func (m *Mirage) GetAffixes() []uint32 {
	return m.Affixes
}

func (m *Mirage) setAffixes(affixes []uint32) {
	m.Affixes = affixes
}

// GetAltAttrsAndSkills : 获取词缀带来的战斗属性,技能,战力
func (m *Mirage) GetAltAttrsAndSkills() (*battle.AltAttr, *battle.AltRaisePS) {
	attrs := make(map[int]int64)
	raisePSMap := make(map[uint32][]uint64)
	for _, id := range m.GetAffixes() {
		info := goxml.GetData().MirageAffixInfoM.Index(id)
		if info == nil {
			continue
		}

		for k, v := range info.Attrs {
			attrs[k] += v
		}
		raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], info.RaisePSs...)
	}

	altAttr := &battle.AltAttr{}
	altSkill := battle.NewAltRaisePS()
	if len(attrs) > 0 {
		posAttrs := make(map[uint32]map[int]int64, 1)
		posAttrs[0] = attrs
		altAttr.SetDefense(posAttrs)
	}
	if len(raisePSMap) > 0 {
		altSkill.AltAttack(raisePSMap)
	}
	return altAttr, altSkill
}

// IsPassFirst : 是否首次通关
func (m *Mirage) IsPassFirst() bool {
	return m.IsFirstPass
}

func (m *Mirage) UpdatePassFirst() {
	m.IsFirstPass = true
}

func (m *Mirage) updateReceiveAward() {
	m.IsReceiveAward = true
}

func (m *Mirage) IsReceivedAward() bool {
	return m.IsReceiveAward
}
