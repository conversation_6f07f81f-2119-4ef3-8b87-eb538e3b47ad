package character

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
)

type SeasonShopM struct {
	owner *User
	data  []*db.SeasonShop //db-save-check
}

func newSeasonShopM(user *User) *SeasonShopM {
	return &SeasonShopM{
		owner: user,
	}
}

func newSingleSeasonShop(shopId uint32) *db.SeasonShop {
	return &db.SeasonShop{
		ShopId:    shopId,
		GoodsList: make(map[uint32]uint32),
	}
}

func (s *SeasonShopM) Owner() *User {
	return s.owner
}

// 加载数据
func (s *SeasonShopM) load(data []*db.SeasonShop) {
	s.data = data
	for _, shopData := range s.data {
		if shopData.GoodsList == nil {
			shopData.GoodsList = make(map[uint32]uint32)
		}
	}
}

// 保存数据
func (s *SeasonShopM) Save() {
	s.Owner().dbUser.Module7.SeasonShops = s.data
	s.Owner().setSaveTag(saveTagModule7)
}

// clone内存数据
func (s *SeasonShopM) FlushShopById(shopId uint32) *cl.SeasonShop {
	flushShop := &cl.SeasonShop{
		ShopId: shopId,
	}
	for _, seasonShopData := range s.data {
		if seasonShopData == nil {
			continue
		}
		if seasonShopData.ShopId != shopId {
			continue
		}
		shopClone := seasonShopData.Clone()
		if shopClone == nil {
			continue
		}
		flushShop.GoodsList = shopClone.GoodsList
		break
	}
	return flushShop
}

func (s *SeasonShopM) GetSeasonShopById(shopId uint32) *db.SeasonShop {
	for _, shopData := range s.data {
		if shopData.ShopId == shopId {
			return shopData
		}
	}
	data := newSingleSeasonShop(shopId)
	s.data = append(s.data, data)
	return data
}

func (s *SeasonShopM) IsSeasonShopOpen(now int64, seasonShopId uint32) bool {
	startTime, endTime, ret := goxml.SeasonShopCalcActivityTime(now, seasonShopId)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user %d [SeasonShopM] IsSeasonShopOpen: SeasonShopCalcActivityTime err. %d", s.owner.ID(), ret)
		return false
	}
	return now >= startTime && now <= endTime
}

func (s *SeasonShopM) OnSeasonEnd() {
	if len(s.data) == 0 {
		return
	}
	for _, shop := range s.data {
		if shop == nil {
			continue
		}
		shop.GoodsList = make(map[uint32]uint32)
	}
	s.Save()
}

// **************** buy ****************

func (s *SeasonShopM) CalcBuyRes(goodsInfo *goxml.ActivitySeasonShopGoodsInfo, num uint32) ([]*cl.Resource, []*cl.Resource) {
	costs := make([]*cl.Resource, 0, len(goodsInfo.Cost))
	awards := make([]*cl.Resource, 0, len(goodsInfo.Award))

	for _, v := range goodsInfo.Cost {
		if v == nil {
			continue
		}
		costs = append(costs, goxml.GenSimpleResource(v.Type, v.Value, v.Count*num))
	}
	for _, v := range goodsInfo.Award {
		if v == nil {
			continue
		}
		awards = append(awards, goxml.GenSimpleResource(v.Type, v.Value, v.Count*num))
	}

	return costs, awards
}
