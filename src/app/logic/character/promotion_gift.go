package character

import (
	"app/gmxml"
	"app/goxml"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
)

func (o *OperateActivity) InitPromotionGift(srv servicer, user *User) *cl.PromotionGift {
	if o.PromotionGift == nil {
		o.PromotionGift = &cl.PromotionGift{
			Goods:  make(map[uint32]*cl.GoodsState),
			Level:  user.Level(),
			Amount: user.GetRecharge(),
		}
	}

	activityInfo := gmxml.OperateActivityInfoM.Index(o.Id)
	if activityInfo == nil {
		l4g.Errorf("user:%d InitPromotionGift: activityInfo not exist. actId:%d", user.ID(), o.Id)
		return nil
	}

	if activityInfo.Value == uint32(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_PGP) {
		o.PromotionGift.PgpExist = activityInfo.IsPgpIdsExist(srv.GetPgpIdsByUser(user.ID()))
	}

	o.initGiftGoods(activityInfo)
	return o.PromotionGift
}

func (o *OperateActivity) initGiftGoods(activityInfo *gmxml.OperateActivityInfo) {
	now := time.Now().Unix()
	giftInfos := gmxml.PromotionGiftInfoM.Group(o.Id)
	for _, info := range giftInfos {
		_, exist := o.PromotionGift.Goods[info.Id]
		if exist {
			continue
		}
		goodState := &cl.GoodsState{}
		goodState.Id = info.Id
		goodState.Time = now
		o.PromotionGift.Goods[info.Id] = goodState
		if activityInfo.Type == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT) {
			o.initGoodsTargetAwards(info, goodState)
		}
	}
}

func (o *OperateActivity) initGoodsTargetAwards(info *gmxml.PromotionGiftInfo, goodState *cl.GoodsState) {
	if info == nil || goodState == nil {
		return
	}
	goodState.TargetAwards = make([]int32, 0, len(info.OptionalAwards))
	for _, awards := range info.OptionalAwards {
		if len(awards.Ress) == 1 {
			goodState.TargetAwards = append(goodState.TargetAwards, 0) // 只有一个奖励，直接设为0
			continue
		}
		goodState.TargetAwards = append(goodState.TargetAwards, -1) // 多个奖励设为-1，等玩家手动选择
	}
}

// initForQa
// @Description: 注意该方法是给测试号生成礼包用，其它情况不可调用
func (o *OperateActivity) initPromotionGiftForQa(user *User) *cl.PromotionGift {
	if o.PromotionGift == nil {
		o.PromotionGift = &cl.PromotionGift{
			Goods:  make(map[uint32]*cl.GoodsState),
			Level:  user.Level(),
			Amount: user.GetRecharge(),
		}
	}

	activityInfo := gmxml.OperateActivityInfoM.Index(o.Id)
	if activityInfo == nil {
		l4g.Errorf("user:%d InitPromotionGift: activityInfo not exist. actId:%d", user.ID(), o.Id)
		return nil
	}

	if activityInfo.Value == uint32(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_USER_LEVEL) {
		if o.PromotionGift.Level < activityInfo.Level || o.PromotionGift.Level >= activityInfo.LevelMax {
			o.PromotionGift.Level = activityInfo.Level
		}
	}
	if activityInfo.Value == uint32(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_RECHARGE_AMOUNT) {
		if o.PromotionGift.Amount < activityInfo.RechargeMin || o.PromotionGift.Amount >= activityInfo.RechargeMax {
			o.PromotionGift.Amount = activityInfo.RechargeMin
		}
	}
	if activityInfo.Value == uint32(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_PGP) {
		o.PromotionGift.PgpExist = true
	}

	o.initGiftGoods(activityInfo)
	return o.PromotionGift
}

func (o *OperateActivityM) promotionGiftProcess(srv servicer, order *db.Order) bool {
	if !o.u.IsFunctionOpen(uint32(common.FUNCID_MODULE_PROMOTION), srv) {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  function not open", o.u.ID(), o.u.GetAccountTag())
		return false
	}
	giftInfo := gmxml.PromotionGiftInfoM.Index(order.Custom.Id)
	if giftInfo == nil {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: gift not exist:%s %d",
			o.u.ID(), o.u.GetAccountTag(), order.OrderId, order.Custom.Id)
		return false
	}
	if giftInfo.InternalId != order.Custom.InternalId {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: recharge internal id is error custom:%d , info:%d",
			o.u.ID(), o.u.GetAccountTag(), order.Custom.InternalId, giftInfo.InternalId)
		return false
	}
	activityInfo := gmxml.OperateActivityInfoM.Index(giftInfo.ActId)
	if activityInfo == nil {
		l4g.Errorf("[OperateActivityM] user:%d activityInfo is nil. id:%d", o.u.ID(), giftInfo.Id)
		return false
	}
	if !o.promotionGiftIsOpening(srv, activityInfo, giftInfo) {
		l4g.Errorf("[OperateActivityM] user:%d promotionGiftIsOpening check error.", o.u.ID())
		return false
	}

	act := o.u.OperateActivityM().GetActivity(giftInfo.ActId)
	if act == nil {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: activity %d is not init",
			o.u.ID(), o.u.GetAccountTag(), giftInfo.ActId)
		return false
	}
	gift := act.PromotionGift
	if gift == nil {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: activity :%d gift is not init",
			o.u.ID(), o.u.GetAccountTag(), giftInfo.ActId)
		return false
	}
	if o.u.OperateActivityM().PromotionRechargeCheck(activityInfo, giftInfo) != cret.RET_OK {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift recharge check failed",
			o.u.ID(), o.u.GetAccountTag())
		return false
	}
	goods, exist := gift.Goods[giftInfo.Id]
	if !exist {
		gift.Goods[giftInfo.Id] = &cl.GoodsState{Id: giftInfo.Id, Time: time.Now().Unix()}
		goods = gift.Goods[giftInfo.Id]
	}
	count := goods.Count
	if count+1 > giftInfo.Number {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: gift num not enough: orderId %s count %d gift number %d",
			o.u.ID(), o.u.GetAccountTag(), order.OrderId, count, giftInfo.Number)
		return false
	}
	awards := giftInfo.Awards()
	if activityInfo.Type == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT) {
		awards = o.GetUserOptionalAwards(giftInfo, goods.GetTargetAwards())
	}
	if len(awards) == 0 {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: awards config error: orderId %s gift Id %d",
			o.u.ID(), o.u.GetAccountTag(), order.OrderId, giftInfo.Id)
		return false
	}

	smsg := &cl.L2C_OperateActivityRecharge{}
	smsg.Ret, awards = o.u.Award(srv, awards, AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_PROMOTION_GIFT), goxml.AddResourcesForRecharge)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: awards is error, error code :%d", o.u.ID(), o.u.GetAccountTag(), smsg.Ret)
		return false
	}
	goods.Count++
	o.SetChange(act)

	smsg.Id = giftInfo.Id
	smsg.PromotionGift = gift.Clone()
	smsg.Awards = awards

	//推送直充活动信息
	o.u.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityRecharge, smsg)
	o.u.LogPromotionGiftRecharge(srv, giftInfo.ActId, giftInfo.Id)
	l4g.Infof("[OperateActivityM] user:%d account tag:%d  processGift suc: order:%s goods:%v InternalId:%d",
		o.u.ID(), o.u.GetAccountTag(), order.OrderId, goods, giftInfo.InternalId)
	return true
}

func (o *OperateActivityM) promotionGiftIsOpening(srv servicer, activityInfo *gmxml.OperateActivityInfo, giftInfo *gmxml.PromotionGiftInfo) bool {
	/* now := o.u.Now(srv)
	if !o.IsOpening(srv, activityInfo, now) {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: activity not open: %d",
			o.u.ID(), o.u.GetAccountTag(), giftInfo.ActId)
		return false
	} */
	// 开启时间检查没有用IsOpening，下面的检查中把结束时间延长了一天
	now := o.u.Now(srv)
	var addTm uint32
	switch activityInfo.TimeType {
	case uint32(common.TIME_TYPE_NORMAL_DATE):
		addTm = util.DaySecs
	case uint32(common.TIME_TYPE_SERVER_OPEN), uint32(common.TIME_TYPE_USER_CREATE):
		addTm = 1
	default:
		l4g.Errorf("[OperateActivityM] user:%d [FATAL] timeType error. actId:%d timeType:%d", o.u.ID(), activityInfo.Id, activityInfo.TimeType)
		return false
	}
	if now[activityInfo.TimeType] < activityInfo.OpenTime || now[activityInfo.TimeType] > activityInfo.EndTime+addTm {
		l4g.Errorf("[OperateActivityM] user:%d account tag:%d  processGift: activity not open: %d",
			o.u.ID(), o.u.GetAccountTag(), giftInfo.ActId)
		return false
	}
	return true
}

func (o *OperateActivityM) PromotionRechargeCheck(activityInfo *gmxml.OperateActivityInfo, giftInfo *gmxml.PromotionGiftInfo) cret.RET {
	activity := o.GetActivity(activityInfo.Id)
	if activity == nil {
		l4g.Errorf("user: %d PromotionRechargeCheck：activity not exist. actId:%d", o.u.ID(), activityInfo.Id)
		return cret.RET_SYSTEM_DATA_ERROR
	}
	if activityInfo.Value == uint32(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_RECHARGE_AMOUNT) {
		if activity.PromotionGift.Amount < activityInfo.RechargeMin || activity.PromotionGift.Amount >= activityInfo.RechargeMax {
			l4g.Errorf("user: %d PromotionRechargeCheck：activity rechargeAmount error. actId:%d amount:%d",
				o.u.ID(), activityInfo.Id, activity.PromotionGift.Amount)
			return cret.RET_PROMOTION_GIFT_LIMIT
		}
	} else if activityInfo.Value == uint32(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_USER_LEVEL) {
		if activity.PromotionGift.Level < activityInfo.Level || activity.PromotionGift.Level >= activityInfo.LevelMax {
			l4g.Errorf("user: %d PromotionRechargeCheck：activity level error. actId:%d level:%d",
				o.u.ID(), activityInfo.Id, activity.PromotionGift.Level)
			return cret.RET_PROMOTION_GIFT_LIMIT
		}
	} else if activityInfo.Value == uint32(common.PROMOTION_GIFT_LIMIT_TYPE_PGLT_PGP) { // 是人群包限制类型的礼包，检查人群包ID
		if !activity.PromotionGift.PgpExist {
			l4g.Errorf("user: %d PromotionRechargeCheck: activityType error. actId:%d", o.u.ID(), activityInfo.Id)
			return cret.RET_PROMOTION_GIFT_LIMIT
		}
	} else {
		l4g.Errorf("user: %d PromotionRechargeCheck: activity limitType error. actId:%d", o.u.ID(), activityInfo.Id)
		return cret.RET_PROMOTION_GIFT_LIMIT
	}

	promotionGiftGood := activity.PromotionGift.Goods[giftInfo.Id]
	if promotionGiftGood == nil {
		l4g.Errorf("user: %d PromotionRechargeCheck: goods not exist. giftId:%d ", o.u.ID(), giftInfo.Id)
		return cret.RET_CLIENT_REQUEST_ERROR
	}
	if promotionGiftGood.Count >= giftInfo.Number {
		l4g.Errorf("user: %d PromotionRechargeCheck: giftID:%d is in novice protection", o.u.ID(), giftInfo.Id)
		return cret.RET_PROMOTION_BUY_NUMBER_LIMIT
	}

	switch activityInfo.Type {
	case uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_DISCOUNT_GIFT): // 折扣礼包
	case uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT): // 自选礼包
		if len(promotionGiftGood.TargetAwards) != len(giftInfo.OptionalAwards) {
			l4g.Errorf("user: %d PromotionRechargeCheck: giftID:%d award error targetAwards:%+v",
				o.u.ID(), giftInfo.Id, promotionGiftGood.TargetAwards)
			return cret.RET_SYSTEM_DATA_ERROR
		}

		for i, v := range promotionGiftGood.GetTargetAwards() {
			if v < 0 || giftInfo.OptionalAwards[i] == nil || v >= int32(len(giftInfo.OptionalAwards[i].Ress)) {
				l4g.Errorf("user: %d PromotionRechargeCheck: giftID:%d award not select", o.u.ID(), giftInfo.Id)
				return cret.RET_PROMOTION_OPTIONAL_GIFT_AWARD_NOT_CHOOSE
			}
		}
	case uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_CHAIN_GIFT): // 连购礼包
		if giftInfo.Index > 1 {
			preGift := gmxml.PromotionGiftInfoM.GetChainGiftInfoByIndex(activityInfo.Id, giftInfo.Index-1)
			if preGift == nil {
				l4g.Errorf("user: %d PromotionRechargeCheck: giftID:%d award not select", o.u.ID(), giftInfo.Id)
				return cret.RET_SYSTEM_DATA_ERROR
			}
			if activity.PromotionGift.Goods[preGift.Id] == nil || activity.PromotionGift.Goods[preGift.Id].Count < preGift.Number {
				l4g.Errorf("user: %d PromotionRechargeCheck: giftID:%d award not select", o.u.ID(), giftInfo.Id)
				return cret.RET_PROMOTION_CHAIN_GIFT_PRE_GIFT_NOT_BUY
			}
		}
	default:
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: activityType error. type:%d", o.u.ID(), activityInfo.Type)
		return cret.RET_SYSTEM_DATA_ERROR
	}

	return cret.RET_OK
}

func (o *OperateActivityM) GetUserOptionalAwards(promotionGiftInfo *gmxml.PromotionGiftInfo, choose []int32) []*cl.Resource {
	if promotionGiftInfo == nil {
		return nil
	}

	awardsNum := len(promotionGiftInfo.OptionalAwards)

	if awardsNum != len(choose) {
		l4g.Errorf("[FATAL] user: %d GetUserOptionalAwards: awardsNum not equal to chooseNum. awardsNum:%d chooseNum:%d",
			o.u.ID(), len(promotionGiftInfo.OptionalAwards), len(choose))
		// 只打印日志，发奖正常走
	}

	awards := make([]*cl.Resource, 0, awardsNum)

	for i, optionalAward := range promotionGiftInfo.OptionalAwards {
		if len(optionalAward.Ress) <= 0 {
			continue
		}
		if i >= len(choose) {
			break
		}
		chooseIndex := choose[i]
		if chooseIndex < 0 || chooseIndex >= int32(len(optionalAward.Ress)) {
			awards = append(awards, optionalAward.Ress[0].Clone())
			continue
		}
		awards = append(awards, optionalAward.Ress[chooseIndex].Clone())
	}
	return awards
}
