package character

import (
	"app/goxml"
	"app/protos/out/cl"
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/time"
)

type GoldBuy struct {
	owner *User
	data  *cl.GoldBuy
}

func newGoldBuy(user *User) *GoldBuy {
	return &GoldBuy{
		owner: user,
	}
}

func (g *GoldBuy) Owner() *User {
	return g.owner
}

// 加载数据
func (g *GoldBuy) load(data *cl.GoldBuy) {
	g.data = data
}

func (g *GoldBuy) InitGoldBuy() {
	g.data = &cl.GoldBuy{
		Chests: make([]*cl.GoldChest, 0, len(goxml.GetData().GoldBuyInfoM.Datas)),
	}

	for _, info := range goxml.GetData().GoldBuyInfoM.Datas {
		if info != nil {
			chest := &cl.GoldChest{
				SysId: info.Id,
			}
			g.data.Chests = append(g.data.Chests, chest)
		}
	}
	g.Save()
}

// Save ：设置保存标签, 保存数据
func (g *GoldBuy) Save() {
	if g.Owner().dbUser.Module1.GoldBuy == nil {
		g.Owner().dbUser.Module1.GoldBuy = g.data
	}
	g.Owner().setSaveTag(saveTagModule1)
}

func (g *GoldBuy) GetGoldBuy() *cl.GoldBuy {
	return g.data
}

func (g *GoldBuy) GetGoldChest(id uint32) *cl.GoldChest {
	if g.data != nil {
		for _, chest := range g.data.Chests {
			if chest.SysId == id {
				return chest
			}
		}
	}

	return nil
}

func (g *GoldBuy) Flush() []*cl.GoldChest {
	if g.data != nil {
		chests := make([]*cl.GoldChest, 0, len(g.data.Chests))
		for _, chest := range g.data.Chests {
			chests = append(chests, chest.Clone())
		}
		return chests
	}

	return nil
}

// 返回整点时间戳: 例如：0点、12点、20点
func (g *GoldBuy) dailyHour(hour, min, sec int) int64 {
	// util.DailyZero(): 获取当天零点时间戳
	//nolint:mnd
	return int64(util.DailyZero()) + int64((hour*3600)+(min*60)+sec)
}

func (g *GoldBuy) IsRefresh() bool {
	now := time.Now().Unix()
	for _, refresh := range goxml.GetData().GoldBuyRefreshInfoM.Datas {
		if refresh == nil {
			l4g.Errorf("user: %d GoldBuy.CheckIsRefresh: goldBuy_refresh_info.xml config error ", g.owner.ID())
			return false
		}
		if g.data.LastTime < g.dailyHour(int(refresh.RefreshHour), int(refresh.RefreshMin), int(refresh.RefreshSec)) &&
			g.dailyHour(int(refresh.RefreshHour), int(refresh.RefreshMin), int(refresh.RefreshSec)) <= now {
			return true
		}
	}
	return false
}

// Refresh ：刷新：宝箱使用次数重置为0
func (g *GoldBuy) Refresh() {
	if g.data != nil {
		// 更新宝箱可用次数
		for _, chest := range g.data.Chests {
			chest.UseCount = uint32(0)
		}

		now := time.Now().Unix()
		// 更新上次刷新时间
		g.data.LastTime = now
		g.Save()
	}
}

func (g *GoldBuy) CalcAwards(dungeonID uint32, buyInfo *goxml.GoldBuyInfo) []*cl.Resource {
	dungeon := goxml.GetData().DungeonConfigInfoM.Index(dungeonID)
	if dungeon == nil {
		l4g.Errorf("user: %d GoldBuy.GetAwards: DungeonConfigInfo is nil, cal gold failed.", g.owner.ID())
		return nil
	}

	// 获得的金币数量
	goldNum := goxml.GetData().VipPrivilegeInfoM.GoldBuyPrivilege(g.owner.Vip(), buyInfo.HangTime*dungeon.GoldNum)
	// 固定加成不经过VIP加成
	goldNum += buyInfo.ExtraReward
	return []*cl.Resource{goxml.GenSimpleResource(uint32(common.RESOURCE_GOLD), 0, goldNum)}
}

func (g *GoldBuy) HaveFreeCountWithRedPoint(srv servicer) bool {
	if !g.owner.IsFunctionOpen(uint32(common.FUNCID_MODULE_GOLD_BUY), srv) {
		return false
	}

	if g.data == nil {
		return true
	}

	for _, chest := range g.data.Chests {
		buyInfo := goxml.GetData().GoldBuyInfoM.Index(chest.SysId)
		if buyInfo == nil {
			l4g.Errorf("user: %d GoldBuy.HaveFreeCount: goldBuy info not exist. ", g.owner.ID())
			continue
		}
		if buyInfo.FreeLimit > 0 && chest.UseCount < buyInfo.FreeLimit {
			return true
		}
	}

	return false
}
