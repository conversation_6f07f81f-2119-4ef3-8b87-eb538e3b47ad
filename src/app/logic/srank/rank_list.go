package srank

import (
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/skiplist"
)

/*
* 定义排行列表项
* 说明：
* 	支持按多个分数值进行排行
 */
type RankItem[T any] struct {
	Id     uint64
	Scores []uint64
	Extra  T
}

func (ri *RankItem[T]) Key() uint64 {
	if ri == nil {
		return 0
	}
	return ri.Id
}
func (ri *RankItem[T]) Score() uint64 {
	if ri == nil || len(ri.Scores) <= 0 {
		return 0
	}
	return ri.Scores[0]
}

/*
* 定义排行比较器
* 说明：
*	1、RankItem支持多个分数值，比较器首先比较第一项，相等时再比较第二项，以此类推
*	2、若两个RankItem分数值数量不同，数量不够的自动填0进行比较
*   3、默认分数越高排行越靠前
*	4、RankBySmallerScoreIndexes可以指定哪些分数越低排行越靠前，这里指定RankItem中Scores的下标即可
 */
type RankComparer[T any] struct {
	RankBySmallerScoreIndexes []int
}

func (c *RankComparer[T]) CmpKey(v1 interface{}, v2 interface{}) int {
	k1 := v1.(*RankItem[T]).Key()
	k2 := v2.(*RankItem[T]).Key()
	switch {
	case k1 < k2:
		return -1
	case k1 == k2:
		return 0
	default:
		return 1
	}
}

func (c *RankComparer[T]) CmpScore(v1 interface{}, v2 interface{}) int {
	item1 := v1.(*RankItem[T])
	item2 := v2.(*RankItem[T])
	count := len(item1.Scores)
	if count < len(item2.Scores) {
		count = len(item2.Scores)
	}
	for idx := 0; idx < count; idx++ {
		var score1 uint64
		var score2 uint64
		if idx < len(item1.Scores) {
			score1 = item1.Scores[idx]
		}
		if idx < len(item2.Scores) {
			score2 = item2.Scores[idx]
		}
		if score1 == score2 {
			continue
		}
		isRankBySmallerScore := c.isRankBySmallerScore(idx)
		if (!isRankBySmallerScore && score1 < score2) || (isRankBySmallerScore && score1 > score2) {
			return 1
		}
		return -1
	}
	return 0
}

func (c *RankComparer[T]) isRankBySmallerScore(index int) bool {
	for _, i := range c.RankBySmallerScoreIndexes {
		if index == i {
			return true
		}
	}
	return false
}

/*
* 排行列表项创建接口
 */
type RankItemCreator[T any] interface {
	CreateRankItem() *RankItem[T]
}

/*
* 排行榜接口
 */
type RankList[T any] interface {
	//更新排行
	Update(id uint64, extra T, scores ...uint64) *RankItem[T]
	//根据排行列表项更新排行
	UpdateWithRankItem(rankItem *RankItem[T]) *RankItem[T]
	//根据创建接口更新排行
	UpdateWithCreator(creator RankItemCreator[T])
	//删除列表项
	Delete(id uint64)
	//删除指定排行范围的列表项
	DeleteByRange(start, end uint32) uint32
	//清空排行榜
	Clear()
	//获取当前排行项数量
	Length() uint32
	//获取第一名
	GetFirstItem() *RankItem[T]
	//获取最后一名
	GetLastItem() *RankItem[T]
	//获取指定ID的排行
	GetRank(id uint64) uint32
	//获取指定ID的排行和列表项
	GetRankAndItem(id uint64) (uint32, *RankItem[T])
	//获取指定排行的列表项
	GetItemByRank(rank uint32) *RankItem[T]
	//获取全部列表项
	GetAll() []*RankItem[T]
	//获取指定排行范围的列表项
	GetByRange(start, end uint32) []*RankItem[T]

	//获取全部排行附加数据列表
	GetAllExtras() []T
	//获取指定排行范围的附加数据列表
	GetExtrasByRange(start, end uint32) []T

	//获取指定分数范围的随机列表
	GetRangeByScoreRandom(rd *rand.Rand, rg *skiplist.RangeSpec) []*RankItem[T]

	//获取指定分数范围的列表
	GetRangeByScore(rg *skiplist.RangeSpec) []*RankItem[T]
}

/*
* 定义排行榜
 */
type rankList[T any] struct {
	comparer *RankComparer[T]
	sl       *skiplist.Set
	maxRank  uint32
}

// @param *RankComparer comparer 比较器
// @param uint32 maxRank 最大排行，0表示不限
// @return Rank 排行榜对象
func NewRankListWithComparer[T any](comparer *RankComparer[T], maxRank uint32) RankList[T] {
	return newRankListWithComparer[T](comparer, maxRank)
}

func newRankListWithComparer[T any](comparer *RankComparer[T], maxRank uint32) *rankList[T] {
	if comparer == nil {
		return nil
	}
	return &rankList[T]{
		comparer: comparer,
		sl:       skiplist.NewSet(comparer),
		maxRank:  maxRank,
	}
}

// @param []int rankBySmallerScoreIndexes 分数越低排行越靠前的索引列表
// @param uint32 maxRank 最大排行，0表示不限
// @return Rank 排行榜对象
func NewRankList[T any](rankBySmallerScoreIndexes []int, maxRank uint32) RankList[T] {
	return newRankList[T](rankBySmallerScoreIndexes, maxRank)
}

func newRankList[T any](rankBySmallerScoreIndexes []int, maxRank uint32) *rankList[T] {
	return newRankListWithComparer(&RankComparer[T]{
		RankBySmallerScoreIndexes: rankBySmallerScoreIndexes,
	}, maxRank)
}

func (r *rankList[T]) Update(id uint64, extra T, scores ...uint64) *RankItem[T] {
	rankItem := &RankItem[T]{
		Id:    id,
		Extra: extra,
	}
	rankItem.Scores = append(rankItem.Scores, scores...)
	return r.UpdateWithRankItem(rankItem)
}

func (r *rankList[T]) UpdateWithRankItem(rankItem *RankItem[T]) *RankItem[T] {
	if rankItem == nil {
		return nil
	}
	if ele := r.sl.GetElement(rankItem.Id); ele != nil {
		r.sl.Delete(ele)
	}
	var lastItem *RankItem[T]
	if r.maxRank > 0 && r.sl.Length() >= r.maxRank {
		last := r.GetLastItem()
		if last != nil {
			if r.comparer.CmpScore(rankItem, last) > 0 {
				return nil
			}
		}
		r.sl.DeleteRangeByRank(r.sl.Length(), r.sl.Length())
		lastItem = last
	}
	r.sl.Insert(rankItem)
	return lastItem
}

func (r *rankList[T]) UpdateWithCreator(creator RankItemCreator[T]) {
	if creator == nil {
		return
	}
	r.UpdateWithRankItem(creator.CreateRankItem())
}

func (r *rankList[T]) Delete(id uint64) {
	if ele := r.sl.GetElement(id); ele != nil {
		r.sl.Delete(ele)
	}
}

func (r *rankList[T]) DeleteByRange(start, end uint32) uint32 {
	return r.sl.DeleteRangeByRank(start, end)
}

func (r *rankList[T]) Clear() {
	r.sl = skiplist.NewSet(r.comparer)
}

func (r *rankList[T]) Length() uint32 {
	return r.sl.Length()
}

func (r *rankList[T]) GetFirstItem() *RankItem[T] {
	node := r.sl.First()
	if node == nil {
		return nil
	}
	return node.Value().(*RankItem[T])
}

func (r *rankList[T]) GetLastItem() *RankItem[T] {
	node := r.sl.Tail()
	if node == nil {
		return nil
	}
	return node.Value().(*RankItem[T])
}

func (r *rankList[T]) GetRank(id uint64) uint32 {
	return r.sl.GetRank(id)
}

func (r *rankList[T]) GetRankAndItem(id uint64) (uint32, *RankItem[T]) {
	rank, node := r.sl.GetRankAndNode(id)
	if rank == 0 {
		return 0, nil
	}
	return rank, node.Value().(*RankItem[T])
}

func (r *rankList[T]) GetItemByRank(rank uint32) *RankItem[T] {
	node := r.sl.GetNodeByRank(rank)
	if node == nil {
		return nil
	}
	return node.Value().(*RankItem[T])
}

func (r *rankList[T]) GetAll() []*RankItem[T] {
	length := r.Length()
	return r.GetByRange(1, length)
}

func (r *rankList[T]) GetByRange(start, end uint32) []*RankItem[T] {
	values := r.sl.GetRangeByRank(start, end)
	if values == nil {
		return nil
	}
	rankItems := make([]*RankItem[T], 0, len(values))
	for _, v := range values {
		rankItem := v.(*RankItem[T])
		rankItems = append(rankItems, rankItem)
	}
	return rankItems
}

func (r *rankList[T]) GetAllExtras() []T {
	rankItems := r.GetAll()
	return r.getExtras(rankItems)
}

func (r *rankList[T]) GetExtrasByRange(start, end uint32) []T {
	rankItems := r.GetByRange(start, end)
	return r.getExtras(rankItems)
}

func (r *rankList[T]) getExtras(rankItems []*RankItem[T]) []T {
	extras := make([]T, 0, len(rankItems))
	for _, rankItem := range rankItems {
		if rankItem != nil {
			extras = append(extras, rankItem.Extra)
		}
	}
	return extras
}

func (r *rankList[T]) GetRangeByScoreRandom(rd *rand.Rand, rg *skiplist.RangeSpec) []*RankItem[T] {
	values := r.sl.GetRangeByScoreRandom(rd, rg)
	if values == nil {
		return nil
	}
	rankItems := make([]*RankItem[T], 0, len(values))
	for _, v := range values {
		rankItem := v.(*RankItem[T])
		rankItems = append(rankItems, rankItem)
	}
	return rankItems
}

func (r *rankList[T]) GetRangeByScore(rg *skiplist.RangeSpec) []*RankItem[T] {
	values := r.sl.GetRangeByScore(rg)
	if values == nil {
		return nil
	}
	rankItems := make([]*RankItem[T], 0, len(values))
	for _, v := range values {
		rankItem := v.(*RankItem[T])
		rankItems = append(rankItems, rankItem)
	}
	return rankItems
}
