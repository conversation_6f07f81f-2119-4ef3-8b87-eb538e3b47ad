package service

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/activitytower"
	"app/logic/activity/arena"
	"app/logic/activity/balancearena"
	"app/logic/activity/ban"
	"app/logic/activity/bancmd"
	"app/logic/activity/flower"
	"app/logic/activity/friend"
	"app/logic/activity/gst"
	"app/logic/activity/guild"
	"app/logic/activity/hotrank"
	"app/logic/activity/miragepass"
	"app/logic/activity/monthtasksmessages"
	"app/logic/activity/peak"
	"app/logic/activity/peoplegrouppackage"
	"app/logic/activity/seasonarena"
	"app/logic/activity/seasoncompliance"
	"app/logic/activity/towerpokemon"
	"app/logic/activity/towerseason"
	"app/logic/activity/worldboss"
	"app/logic/activity/wrestle"
	"app/logic/actm"
	"app/logic/actm/goddessprizedraw"
	"app/logic/character"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"runtime/debug"

	"app/protos/in/r2l"

	l4g "github.com/ivanabc/log4go"
)

func (l *LogicService) registerActivities() {
	l.acts[activity.Friend] = friend.NewManager()
	l.acts[activity.Arena] = arena.NewManager()
	l.acts[activity.BanAccount] = ban.NewManager()
	l.acts[activity.Guild] = guild.NewManager() //guild.NewManager()
	l.acts[activity.MiragePass] = miragepass.NewManager()
	//l.acts[activity.GuildChat] = guildchat.NewManager()
	//l.acts[activity.WorldChat] = worldchat.NewManager()
	l.acts[activity.BanCmd] = bancmd.NewManager()
	//l.acts[activity.SystemChat] = systemchat.NewManager()
	//l.acts[activity.ChatLikeList] = chatlikelist.NewManager()
	l.acts[activity.Wrestle] = wrestle.NewManager()
	l.acts[activity.Flower] = flower.NewManager()
	l.acts[activity.MonthTasksMsg] = monthtasksmessages.NewManager()
	l.acts[activity.WorldBoss] = worldboss.NewManager()
	l.acts[activity.TowerSeason] = towerseason.NewManager()
	l.acts[activity.Peak] = peak.NewManager()
	l.acts[activity.GST] = gst.NewManager(l)
	l.acts[activity.PeopleGroupPackage] = peoplegrouppackage.NewManager()
	l.acts[activity.SeasonArena] = seasonarena.NewManager()
	l.acts[activity.HotRank] = hotrank.NewManager()
	l.acts[activity.ActivityTower] = activitytower.NewManager()
	l.acts[activity.SeasonCompliance] = seasoncompliance.NewManager()
	l.acts[activity.TowerPokemon] = towerpokemon.NewManager()
	l.actms[actm.GoddessPrizeDraw] = goddessprizedraw.NewManager()
	l.acts[activity.BalanceArena] = balancearena.NewManager()
}

func (l *LogicService) loadActivities(msg *r2l.R2L_Load) {
	for _, act := range l.acts {
		if act != nil {
			act.Load(msg)
		}
	}

	for _, act := range l.actms {
		if act != nil {
			act.Load(msg)
		}
	}

}

func (l *LogicService) initActivities() {
	for _, act := range l.acts {
		if act != nil {
			act.Init(l)
		}
	}
	for _, act := range l.actms {
		if act != nil {
			act.Init(l)
		}
	}
}

func (l *LogicService) GetActivity(id activity.ID) activity.Activity {
	return l.acts[id]
}

func (l *LogicService) GetActivityM(id actm.ID) actm.Activity {
	return l.actms[id]
}

func (l *LogicService) saveAllActivities() {
	for _, act := range l.acts {
		if act != nil {
			act.Save(l)
		}
	}

	for _, act := range l.actms {
		if act != nil {
			act.Save(l)
		}
	}

}

// 初始化活动tick
// interval: tick周期，毫秒
func (l *LogicService) initActivitiesTick(interval int) {
	perSecond := 1000 / uint32(interval)
	if perSecond == 0 {
		panic(fmt.Sprintf("initActivitiesTick interval:%d", interval))
	}
	l.actsGroupSize = perSecond
	l.actmsGroupSize = perSecond
}

// tick算法：
// 根据服务器的tick周期，算出来每秒tick多少次，以下以16次为例：
// 把活动按照16个，16个，16个分成一组，这里假设有18个活动
// 那么：
//
//	第1次tick：执行活动1,17
//	第2次tick: 执行活动2,18
//	第3次tick: 执行活动3,(没有19了)
//
// 每次tick的起始位置从 actsTickCount(累次tick总次数) % actsGroupSize(多少个活动分成一组)
//
// 同时这种算法还能够处理活动数量<每秒tick数的情况，比如活动12个，那么在第13,14,15,16次tick是不会执行的
//
// 要注意的是：
//
//	1：多个活动不一定是平均分散在每秒的，比如只有2个活动，每秒tick 16次，那么每秒的前2次tick才有效
//	2：多个活动的tick没有次序保证，因为是按照GroupSize跳跃式tick的
//	3: 由于UserGroupSize，interval，actsGroupSize换算的精度问题，每个活动的连续2次tick时间差不一定严格等于1000ms
//
// 但幸运的是：以上3点都不大需要关注
func (l *LogicService) onActivitiesTick(now int64) {
	for idx := l.actsTickCount % l.actsGroupSize; idx < uint32(len(l.acts)); idx += l.actsGroupSize {
		//l4g.Debugf("onActivitiesTick:%d", idx)
		if act := l.acts[idx]; act != nil {
			l.dealActOnTimer(idx, act, now)
		}
	}
	l.actsTickCount++

	for idx := l.actmsTickCount % l.actmsGroupSize; idx < uint32(len(l.actms)); idx += l.actmsGroupSize {
		//l4g.Debugf("onActivitiesTick:%d", idx)
		if act := l.actms[idx]; act != nil {
			l.dealActmOnTimer(idx, act, now)
		}
	}
	l.actmsTickCount++
}

func (l *LogicService) dealActOnTimer(id uint32, act activity.Activity, now int64) {
	defer func() {
		if err := recover(); err != nil {
			l4g.Error("logic act:%d on timer panic: %s %s\n", id, err, debug.Stack())
		}
	}()
	act.OnTimer(l, now)
}

func (l *LogicService) dealActmOnTimer(id uint32, act actm.Activity, now int64) {
	defer func() {
		if err := recover(); err != nil {
			l4g.Error("logic act:%d on timer panic: %s %s\n", id, err, debug.Stack())
		}
	}()
	act.OnTimer(l, now)
}

/*
func (l *LogicService) onTimerForAllActivities(now int64) {
	for _, act := range l.acts {
		if act != nil {
			act.OnTimer(l, now)
		}
	}
}
*/

// 好友
func (l *LogicService) FriendM() *friend.Manager {
	return l.GetActivity(activity.Friend).(*friend.Manager)
}

// 普通竞技场
func (l *LogicService) ArenaM() *arena.Manager {
	return l.GetActivity(activity.Arena).(*arena.Manager)
}

// 封禁
func (l *LogicService) BanAccountM() *ban.Manager {
	return l.GetActivity(activity.BanAccount).(*ban.Manager)
}

// 公会
func (l *LogicService) GuildM() *guild.Manager {
	return l.GetActivity(activity.Guild).(*guild.Manager)
}

// 个人boss过关统计
func (l *LogicService) MiragePassM() *miragepass.Manager {
	return l.GetActivity(activity.MiragePass).(*miragepass.Manager)
}

// 封/解禁协议
func (l *LogicService) BanCmdM() *bancmd.Manager {
	return l.GetActivity(activity.BanCmd).(*bancmd.Manager)
}

// 密林
func (l *LogicService) FlowerM() *flower.Manager {
	return l.GetActivity(activity.Flower).(*flower.Manager)
}

// 世界boss
func (l *LogicService) WorldBossM() *worldboss.Manager {
	return l.GetActivity(activity.WorldBoss).(*worldboss.Manager)
}

// 巅峰竞技场
func (l *LogicService) PeakM() *peak.Manager {
	return l.GetActivity(activity.Peak).(*peak.Manager)
}

// 公会战
func (l *LogicService) GstM() *gst.Manager {
	return l.GetActivity(activity.GST).(*gst.Manager)
}

// 冲榜活动
func (l *LogicService) ActivityTowerM() *activitytower.Manager {
	return l.GetActivity(activity.ActivityTower).(*activitytower.Manager)
}

// activity  玩法的展示成就，在这里补充给前端
func (l *LogicService) AddAchievementsShowcase(uid uint64, show *cl.AchievementsShowcase) {
	if show == nil {
		return
	}
	if show.Element == nil {
		show.Element = make(map[uint32]*cl.FunctionShowcase)
	}
	arenaM := l.ArenaM()
	arenaUser := arenaM.Get(uid)
	if arenaUser != nil {
		show.Element[uint32(common.FUNCID_MODULE_ARENA)] = &cl.FunctionShowcase{Value: arenaUser.GetSeasonBestDivision()}
	}
	wrestleM := l.GetActivity(activity.Wrestle).(*wrestle.Manager)
	wrestleUser := wrestleM.Get(uid)
	if wrestleUser != nil {
		show.Element[uint32(common.FUNCID_MODULE_WRESTLE)] = &cl.FunctionShowcase{Value: wrestleUser.GetLevel()}
	}
	guildM := l.GetActivity(activity.Guild).(*guild.Manager)
	guild := guildM.GetGuildByUser(uid)
	if guild != nil {
		topDivision := guild.GetLogicGuildSeasonTopDivision()
		show.Element[uint32(common.FUNCID_MODULE_GST)] = &cl.FunctionShowcase{Value: topDivision}
	}
	peakM := l.GetActivity(activity.Peak).(*peak.Manager)
	rank := peakM.GetRank(uid)
	if rank > 0 {
		show.Element[uint32(common.FUNCID_MODULE_PEAK)] = &cl.FunctionShowcase{Value: rank}
	}
}

func (l *LogicService) GetPgpIdsByUser(uid uint64) []uint32 {
	pgpM := l.GetActivity(activity.PeopleGroupPackage).(*peoplegrouppackage.Manager)
	return pgpM.GetPackageIdsByUser(uid)
}

func (l *LogicService) GetUserGuildLevel(uid uint64) uint32 {
	guildM := l.GuildM()
	guild := guildM.GetGuildByUser(uid)
	if guild == nil {
		return 0
	}
	return guild.GetLevel()
}

func (l *LogicService) GetGuildID(uid uint64) uint64 {
	guild := l.GuildM().GetGuildByUser(uid)
	if guild == nil {
		return 0
	}
	return guild.ID()
}

func (l *LogicService) GetLogicGuild(uid uint64) *db.LogicGuild {
	guild := l.GuildM().GetGuildByUser(uid)
	if guild == nil {
		return nil
	}
	return guild.GetData()
}

func (l *LogicService) GetGuildDungeonChatRoomID(uid uint64) uint64 {
	guildUser := l.GuildM().GetGuildUser(uid)
	if guildUser == nil {
		return 0
	}
	return guildUser.GetDungeonChatRoomId()
}

func (l *LogicService) GetGstChatRoomID(uid uint64) string {
	gstUser := l.GstM().GetUser(uid)
	if gstUser == nil {
		return ""
	}
	return gstUser.ChatRoomId
}

func (l *LogicService) DelDropActivityFlowerOccupy(uid uint64, itemRes map[uint32]map[uint32]*cl.Resource) []*cl.Resource {
	flowerM := l.FlowerM()
	flower := flowerM.Get(uid)
	recycleRess := make([]*cl.Resource, 0, 3) //nolint:mnd
	var flowerChange bool
	if flower != nil && flower.GetPreviewAward() != nil {
		flowerAwardLen := len(flower.GetPreviewAward().Res)
		for i := 0; i < flowerAwardLen; i++ {
			flowerAward := flower.GetPreviewAward().Res[i]
			recycleRes, exist := itemRes[flowerAward.Type][flowerAward.Value]
			if !exist {
				continue
			}
			flower.GetPreviewAward().Res = append(flower.GetPreviewAward().Res[:i], flower.GetPreviewAward().Res[i+1:]...)
			flowerChange = true
			flowerAwardLen--
			i--
			tmp := recycleRes.Clone()
			tmp.Count *= flowerAward.Count
			recycleRess = append(recycleRess, tmp)
		}
	}
	if flower != nil && flower.GetOccupySettleAward() != nil {
		flowerOccupyLen := len(flower.GetOccupySettleAward().Res)
		for i := 0; i < flowerOccupyLen; i++ {
			flowerAward := flower.GetOccupySettleAward().Res[i]
			recycleRes, exist := itemRes[flowerAward.Type][flowerAward.Value]
			if !exist {
				continue
			}
			flower.GetOccupySettleAward().Res = append(flower.GetOccupySettleAward().Res[:i], flower.GetOccupySettleAward().Res[i+1:]...)
			flowerChange = true
			flowerOccupyLen--
			i--
			tmp := recycleRes.Clone()
			tmp.Count *= flowerAward.Count
			recycleRess = append(recycleRess, tmp)
		}
	}
	if flowerChange {
		flowerM.SetChange(l, flower)
	}
	return recycleRess
}

func (l *LogicService) SyncGstFormation(user *character.User) {
	gstM, ok := l.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user:%d SyncGstFormation getActivity Gst failed", user.ID())
		return
	}
	needSync := gstM.NeedSyncUserTeamInfo(user.ID(), true)
	if needSync && gstM.GetCrossGst() != nil && !(gstM.GetCrossGst().Stage >= cl.GST_STAGE_RoundFightState_Fight && gstM.GetCrossGst().Stage <= cl.GST_STAGE_RoundFightState_Reward) {
		crossMsg := &l2c.L2CS_GSTSetTeam{}
		crossMsg.FormationId = uint32(common.FORMATION_ID_FI_GST)
		crossMsg.Formation = user.Convert2GSTTeam()
		if gstM.IsCrossConnected() {
			if !l.SendCmdToCross(l2c.ID_MSG_L2CS_GSTSetTeam, user.ID(), crossMsg) {
				l4g.Errorf("user: %d L2CS_GSTSetTeam: cross maintain", user.ID())
			}
		}
	}
}

func (l *LogicService) SyncGstUserInfo(user *character.User) {
	gstM, ok := l.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user:%d SyncGstUserInfo getActivity Gst failed", user.ID())
		return
	}
	needSync := gstM.NeedSyncUserBaseInfo(user.ID(), true)
	if needSync {
		syncInfo := user.CreateGstUserBaseInfo()
		if gstM.IsCrossConnected() {

			if !l.SendCmdToCross(l2c.ID_MSG_L2CS_GSTSYNCGuildUserInfo, user.ID(), syncInfo) {
				l4g.Errorf("user: %d L2CS_GSTSYNCGuildUserInfo: cross maintain", user.ID())
			}
		}
	}
}

func (l *LogicService) SyncGstBuildDispatch(user *character.User, hid uint64) {
	gstM, ok := l.GetActivity(activity.GST).(*gst.Manager)
	if ok {
		gstUser := gstM.GetUser(user.ID())
		if gstUser != nil {
			if gstUser.SyncDispatchBuildByHeroDel(hid) {
				gstM.SetChange(gstUser)
				needSync := gstM.NeedSyncUserBuildDispatchInfo(user.ID(), true)
				if needSync && gstM.GetCrossGst() != nil && !(gstM.GetCrossGst().Stage >= cl.GST_STAGE_RoundFightState_Fight && gstM.GetCrossGst().Stage <= cl.GST_STAGE_RoundFightState_Reward) && gstM.IsCrossConnected() {
					crossMsg := &l2c.L2CS_GSTGuildBuildDispatchHero{}
					crossMsg.BuildDispatchHeroes = gstUser.BuildDispatchHero
					if !l.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGuildBuildDispatchHero, user.ID(), crossMsg) {
						l4g.Errorf("user: %d SyncGstBuildDispatch: cross maintain", user.ID())
					}
				}
			}
		}
	}
}

func (l *LogicService) GetGuildMedal(uid uint64) *db.LogicGuildMedal {
	guild := l.GuildM().GetGuildByUser(uid)
	if guild == nil {
		return nil
	}

	return guild.GetGuildMedal()
}

func (l *LogicService) SyncSeasonArenaSnapshot(user *character.User) {
	saM, ok := l.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user:%d SyncSeasonArenaSnapshot getActivity seaosn arena failed", user.ID())
		return
	}
	if !saM.IsCrossConnected() || saM.CrossState == nil {
		return
	}
	//无脑同步数据由跨服同步
	saUser := saM.GetUser(user.ID())
	var formationID uint32
	if saUser != nil && saUser.IsCurSeasonRound(saM.CrossState.Season, saM.CrossState.Round) {
		divisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.Index(saUser.GetDivision())
		if divisionInfo != nil {
			formationID = divisionInfo.TeamNum
		}
	}
	crossMsg := &l2c.L2CS_SeasonArenaUpdateSnapshot{
		Snapshot: user.NewUserSnapshot(formationID),
	}

	if !l.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaUpdateSnapshot, user.ID(), crossMsg) {
		l4g.Errorf("user: %d SyncSeasonArenaSnapshot L2CS_SeasonArenaUpdateSnapshot: cross maintain", user.ID())
	}
}

func (l *LogicService) GetSeasonComplianceCurStage(user *character.User) *cl.SeasonComplianceStage {
	scM, ok := l.GetActivity(activity.SeasonCompliance).(*seasoncompliance.Manager)
	if !ok {
		l4g.Errorf("user:%d SyncSeasonArenaSnapshot getActivity seaosn arena failed", user.ID())
		return nil
	}
	return scM.GetCurStage()
}

func (l *LogicService) TowerPokemonM() *activitytower.Manager {
	return l.GetActivity(activity.TowerPokemon).(*activitytower.Manager)
}

func (l *LogicService) GoddessPrizeDrawM() *goddessprizedraw.Manager {
	return l.GetActivityM(actm.GoddessPrizeDraw).(*goddessprizedraw.Manager)
}
