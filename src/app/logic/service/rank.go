package service

import (
	"app/logic/rank"
	"app/protos/in/r2l"

	l4g "github.com/ivanabc/log4go"
)

func (l *LogicService) registerGlobalRanks() {
	l.globalRanks[rank.LEVEL] = rank.NewGlobal(&rank.LevelCmp{}, rank.ConvertToLevelValue)
	l.globalRanks[rank.POWER] = rank.NewGlobal(&rank.PowerCmp{}, rank.ConvertToPowerValue)
}

func (l *LogicService) loadGlobalRanks(msg *r2l.R2L_Load) {
	for _, data := range msg.RankData {
		for _, rk := range l.globalRanks {
			rk.Load(data)
		}
	}
	l4g.Debugf("[GlobalRank] load size:%d %d", l.globalRanks[rank.LEVEL].Length(), l.globalRanks[rank.POWER].Length())
}

func (l *LogicService) GetGlobalRank(id rank.ID) *rank.Global {
	return l.globalRanks[id]
}
