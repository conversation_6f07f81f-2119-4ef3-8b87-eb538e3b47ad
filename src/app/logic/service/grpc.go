//nolint:unparam
package service

import (
	"app/gmxml"
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/balancearena"
	"app/logic/activity/ban"
	"app/logic/activity/flower"
	afriend "app/logic/activity/friend"
	aguild "app/logic/activity/guild"
	"app/logic/activity/hotrank"
	"app/logic/activity/peoplegrouppackage"
	aseasonarena "app/logic/activity/seasonarena"
	awrestle "app/logic/activity/wrestle"
	"app/logic/actm"
	"app/logic/actm/goddessprizedraw"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/helper"
	"app/logic/mongo"
	"app/logic/rank"
	"app/patch"
	"app/protos/in/db"
	"app/protos/in/gm"
	"app/protos/in/l2c"
	"app/protos/in/l2m"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	appsrv "app/service"
	"app/service/hooktime"
	"app/symbols"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"runtime/debug"

	"github.com/gogo/protobuf/proto"
	"gitlab.qdream.com/kit/sea/skiplist"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

const (
	gmSliceMaxLength  = 1000 //后台发送slice最大长度
	effectiveMinLevel = 5    //有效玩家最低等级，>4，判断时按>=5
)

type GRPCMessage struct {
	ID   gm.ID
	Data proto.Message
	Ret  chan proto.Message
}

func NewGRPCMessage(id gm.ID, data proto.Message) *GRPCMessage {
	return &GRPCMessage{
		ID:   id,
		Data: data,
		Ret:  make(chan proto.Message, 1),
	}
}

//nolint:exhaustive,funlen
func OnGRPCMessage(ctx context.Context, msg *GRPCMessage) {
	defer func() {
		if err := recover(); err != nil {
			l4g.Error("logic grpc msg panic:%+v %s \n", msg, debug.Stack())
			msg.Ret <- nil
		}
	}()
	srv := appsrv.GetService(ctx).(*LogicService)
	var ret proto.Message
	sync := true
	switch msg.ID {
	case gm.ID_Fn_GetUser:
		ret, sync = srv.onGRPCGetUser(msg.Data.(*gm.UserIndex), msg.Ret)
	case gm.ID_Fn_ReduceResources:
		ret, sync = srv.onGRPCReduceResources(msg.Data.(*gm.Object), msg.Ret)
	case gm.ID_Fn_BanAccount:
		ret, sync = srv.onGRPCBanAccount(msg.Data.(*gm.BanAccountReq), msg.Ret)
	case gm.ID_Fn_SendUserMail:
		ret, sync = srv.onGRPCSendUserMail(msg.Data.(*gm.UsersMail), msg.Ret)
	case gm.ID_Fn_SendServerMail:
		ret, sync = srv.onGRPCSendServerMail(msg.Data.(*gm.ServerMail), msg.Ret)
	case gm.ID_Fn_ToggleUserLogTrace:
		ret, sync = srv.onGRPCToggleUserLogTrace(msg.Data.(*gm.LogTraceOp), msg.Ret)
	case gm.ID_Fn_OnlineNum:
		ret, sync = srv.onGRPCOnlineNum(msg.Data.(*gm.Cmd), msg.Ret)
	case gm.ID_Fn_SetDungeonID:
		ret, sync = srv.onGRPCSetDungeonID(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetGuidanceClose:
		ret, sync = srv.onGRPCSetGuidanceClose(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ChangeBags:
		ret, sync = srv.onGRPCChangeBags(msg.Data.(*gm.BagsOp), msg.Ret)
	case gm.ID_Fn_SendQuestionnaire:
		ret, sync = srv.onGRPCSendQuestionnaire(msg.Data.(*cl.Questionnaire), msg.Ret)
	case gm.ID_Fn_SetUserLevel:
		ret, sync = srv.onGRPCSetUserLevel(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SendTopResource:
		ret, sync = srv.onGRPCSendTopResource(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ClearUserResource:
		ret, sync = srv.onGRPCClearUserResource(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ActivityInfo:
		ret, sync = srv.onGRPCGetActivityInfo(msg.Data.(*gm.UserIndex), msg.Ret)
	case gm.ID_Fn_ResetDailyNum:
		ret, sync = srv.onGRPCResetDailyNum(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetMedalLevel:
		ret, sync = srv.onGRPCSetMedalLevel(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ResetMaze:
		ret, sync = srv.onGRPCResetMaze(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ResetGuildQuitTm:
		ret, sync = srv.onGRPCResetGuildQuitTm(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ResetTowerFloor:
		ret, sync = srv.onGRPCResetTowerFloor(msg.Data.(*gm.TowerReq), msg.Ret)
	case gm.ID_Fn_ResetTrialLevel:
		ret, sync = srv.onGRPCResetTrialLevel(msg.Data.(*gm.TrialReq), msg.Ret)
	case gm.ID_Fn_ResetArenaScore:
		ret, sync = srv.onGRPCResetArenaScore(msg.Data.(*gm.ArenaReq), msg.Ret)
	case gm.ID_Fn_ResetMirageFloor:
		ret, sync = srv.onGRPCResetMirageFloor(msg.Data.(*gm.MirageReq), msg.Ret)
	case gm.ID_Fn_ResetScore:
		ret, sync = srv.onGRPCResetScore(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ResetTaleChapterFinish:
		ret, sync = srv.onGRPCResetTaleChapterFinish(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ResetTaleElite:
		ret, sync = srv.onGRPCResetTaleElite(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetUserResource:
		ret, sync = srv.onGRPCSetUserResource(msg.Data.(*gm.UserResource), msg.Ret)
	case gm.ID_Fn_NotifyRecharge:
		ret, sync = srv.onGRPCNotifyRecharge(msg.Data.(*db.Order), msg.Ret)
	case gm.ID_Fn_SetUserVip:
		ret, sync = srv.onGRPCSetUserVip(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetTowerstarDungeonID:
		ret, sync = srv.onGRPCSetTowerstarDungeonID(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_ResetTowerstar:
		ret, sync = srv.onGRPCResetTowerstar(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetGmConfig:
		ret, sync = srv.onGRPCSetGmConfig(msg.Data.(*gm.GmConfigReq))
	case gm.ID_Fn_SendGiftCodeInfo:
		ret, sync = srv.onGRPCSendGiftCodeInfo(msg.Data.(*gm.GiftCodeInfo), msg.Ret)
	case gm.ID_Fn_UpdateMultiLangs:
		ret, sync = srv.onGRPCUpdateMultiLangs(msg.Data.(*gm.MultiLangs), msg.Ret)
	case gm.ID_Fn_QuestionnaireFinish:
		ret, sync = srv.onGRPCQuestionnaireFinish(msg.Data.(*gm.QuestionnaireFinishReq), msg.Ret)
	case gm.ID_Fn_ReleaseOperateActivity:
		ret, sync = srv.onGRPCReleaseOperateActivity(msg.Data.(*gm.OperateActivity), msg.Ret)
	case gm.ID_Fn_ReleaseOperatePageInfo:
		ret, sync = srv.onGRPCReleaseOperatePageInfo(msg.Data.(*gm.OperatePageInfos), msg.Ret)
	case gm.ID_Fn_BanProtocol:
		ret, sync = srv.onGRPCBanProtocol(msg.Data.(*gm.BanCmd), msg.Ret)
	case gm.ID_Fn_SetAllPushGift:
		ret, sync = srv.onGRPCSetAllPushGift(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_GetRechargeList:
		ret, sync = srv.onGRPCGetRechargeList(msg.Data.(*gm.RechargeListReq), msg.Ret)
	case gm.ID_Fn_CloseGuidance:
		ret, sync = srv.onGRPCCloseGuidance(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetMazeTaskLevel:
		ret, sync = srv.onGRPCSetMazeTaskLevel(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_GetUserMailList:
		ret, sync = srv.onGRPCGetUserMailList(msg.Data.(*gm.UserMailListReq), msg.Ret)
	case gm.ID_Fn_DeleteUserMail:
		ret, sync = srv.onGRPCDeleteUserMail(msg.Data.(*gm.DeleteUserMailReq), msg.Ret)
	case gm.ID_Fn_SetAccountTag:
		ret, sync = srv.onGRPCSetAccountTag(msg.Data.(*gm.AccountTagReq), msg.Ret)
	case gm.ID_Fn_SetServerTime:
		ret, sync = srv.onGRPCSetServerTime(msg.Data.(*gm.ServerTimeReq), msg.Ret)
	case gm.ID_Fn_QueryServerTime:
		ret, sync = srv.onGRPCQueryServerTime(msg.Data.(*gm.Cmd), msg.Ret)
	case gm.ID_Fn_SetSinglePushGift:
		ret, sync = srv.onGRPCSingleAllPushGift(msg.Data.(*gm.SinglePushGiftReq), msg.Ret)
	case gm.ID_Fn_ImportUser:
		ret, sync = srv.onGRPCImportUser(msg.Data.(*gm.ImportUserReq), msg.Ret)
	case gm.ID_Fn_SetDispatchLevel:
		ret, sync = srv.onGRPCSetDispatchLevel(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_UpdateDivineDemon:
		ret, sync = srv.onGRPCUpdateDivineDemon(msg.Data.(*gm.DivineDemonReq))
	case gm.ID_Fn_SetGuildDungeonCurrentChapter:
		ret, sync = srv.onGRPCSetGuildDungeonCurrentChapter(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_UpdateAnnouncement:
		ret, sync = srv.onGRPCUpdateAnnouncement(msg.Data.(*gm.AnnouncementReq))
	case gm.ID_Fn_SetGuildLevel:
		ret, sync = srv.onGRPCSetGuildLevel(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_RecoveryGuildDungeonResetTime:
		ret, sync = srv.onGRPCRecoveryGuildDungeonResetTime(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetFlowerLevel:
		ret, sync = srv.onGRPCSetFlowerLevel(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_UpdateDailyWishActivity:
		ret, sync = srv.onGRPCUpdateDailyWishActivity(msg.Data.(*gm.DailyWishInfo), msg.Ret)
	case gm.ID_Fn_UpdateArtifactDebut:
		ret, sync = srv.onGRPCUpdateArtifactDebut(msg.Data.(*gm.ArtifactDebutReq))
	case gm.ID_Fn_SetTowerSeasonFloor:
		ret, sync = srv.onGRPCSetTowerSeasonFloorID(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_DelChatGroupTag:
		ret, sync = srv.onGRPCDelChatGroupTag(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_UpdateDropActivity:
		ret, sync = srv.onGRPCUpdateDropActivity(msg.Data.(*cl.DropActivityBase))
	case gm.ID_Fn_DeleteHero:
		ret, sync = srv.onGRPCDeleteHero(msg.Data.(*gm.DeleteHeroReq), msg.Ret)
	case gm.ID_Fn_SetDailyAttendance:
		ret, sync = srv.onGRPCSetDailyAttendance(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetWrestleLevel:
		ret, sync = srv.onGRPCSetUserWrestleLevel(msg.Data.(*gm.UserSetParam), msg.Ret)
	case gm.ID_Fn_SetRiteRare:
		ret, sync = srv.onGRPCSetRiteRare(msg.Data.(*gm.UserSetRiteRare), msg.Ret)
	case gm.ID_Fn_SetSeasonLevel:
		ret, sync = srv.onGRPCSetUserSeasonLevel(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_NotifyRefund:
		ret, sync = srv.onGRPCNotifyRefund(msg.Data.(*db.Order), msg.Ret)
	case gm.ID_Fn_DeleteCurrenciesReq:
		ret, sync = srv.onGRPCDeleteCurrencies(msg.Data.(*gm.DeleteCurrenciesReq), msg.Ret)
	case gm.ID_Fn_SetHeroAwakenLevel:
		ret, sync = srv.onGRPCSetHeroAwakenLevel(msg.Data.(*gm.UserSetHeroAwakenLevel), msg.Ret)
	case gm.ID_Fn_SetDisorderLand:
		ret, sync = srv.onGRPCSetDisorderLand(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_GmDivineDemonBench:
		ret, sync = srv.onGRPCDivineDemonBench(msg.Data.(*gm.DivineDemonBenchReq), msg.Ret)
	case gm.ID_Fn_SetSeasonLinkMonuments:
		ret, sync = srv.onGRPCSetSeasonLinkMonuments(msg.Data.(*gm.SetSeasonLinkMonumentsReq), msg.Ret)
	case gm.ID_Fn_UpdatePeopleGroupPackage:
		ret, sync = srv.onGRPCUpdatePeopleGroupPackage(msg.Data.(*gm.UpdatePeopleGroupPackageReq), msg.Ret)
	case gm.ID_Fn_SendPGPMail:
		ret, sync = srv.onGRPCSendPGPMail(msg.Data.(*gm.PGPMail), msg.Ret)
	case gm.ID_Fn_KickAccount:
		ret, sync = srv.onGRPCKickAccount(msg.Data.(*gm.UserIndex), msg.Ret)
	case gm.ID_Fn_ChangeUserName:
		ret, sync = srv.onGRPCChangeUserName(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_GmSetSeasonArenaScore:
		ret, sync = srv.onGPRCSetSeasonArenaScore(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetSeasonLink:
		ret, sync = srv.onGRPCSetSeasonLink(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_UpdatePyramidActivity:
		ret, sync = srv.onGRPCUpdatePyramidActivity(msg.Data.(*cl.PyramidActivityBase))
	case gm.ID_Fn_SetTaskFinish:
		ret, sync = srv.onGRPCSetTaskFinish(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_AddGuildMobScore:
		ret, sync = srv.onGRPCAddGuildMobScore(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetTalentTree:
		ret, sync = srv.onGRPCSetTalentTree(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_SetBossRushLevel:
		ret, sync = srv.onGRPCSetBossRushLevel(msg.Data.(*gm.SetBossRushLevelReq), msg.Ret)
	case gm.ID_Fn_SetBattleHeroStar:
		ret, sync = srv.onGRPCSetBattleHeroStar(msg.Data.(*gm.BattleHeroStar), msg.Ret)
	case gm.ID_Fn_Hotfix:
		ret, sync = srv.onGRPCHotfixCode(msg.Data.(*gm.HotfixReq), msg.Ret)
	case gm.ID_Fn_Remain:
		ret, sync = srv.onGRPCSetRemain(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_UpdateSelectSummon:
		ret, sync = srv.onGRPCUpdateSelectSummon(msg.Data.(*gm.SelectSummonReq))
	case gm.ID_Fn_NewQuestionnaireFinish:
		ret, sync = srv.onGRPCNewQuestionnaireFinish(msg.Data.(*gm.QuestionnaireFinishReq), msg.Ret)
	case gm.ID_Fn_GmDailyAttendanceHero:
		ret, sync = srv.onGRPCSetDailyAttendanceHero(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_DeleteServerMail:
		ret, sync = srv.onGRPCDeleteServerMail(msg.Data.(*gm.DelServerMail), msg.Ret)
	case gm.ID_Fn_UpdateCouponActivity:
		ret, sync = srv.onGRPCUpdateCouponActivity(msg.Data.(*cl.ActivityCouponXml), msg.Ret)
	case gm.ID_Fn_UpdatePokemonSummon:
		ret, sync = srv.onGRPCUpdatePokemonSummon(msg.Data.(*gm.PokemonSummonReq))
	case gm.ID_Fn_SetTowerPokemonDungeonID:
		ret, sync = srv.onGRPCSetTowerPokemonDungeonID(msg.Data.(*gm.UserReq), msg.Ret)
	case gm.ID_Fn_OfflineVipAward:
		ret, sync = srv.onGRPCOfflineVipAward(msg.Data.(*gm.OfflineVipAwardReq), msg.Ret)
	case gm.ID_Fn_GMQaSet:
		ret, sync = srv.onGRPCQaSet(msg.Data.(*gm.QaSetReq), msg.Ret)
	default:
		l4g.Errorf("no found grpc message: %+v", msg)
	}

	if sync {
		resultOnGRPCMessage(msg.Ret, ret)
	}
}

func resultOnGRPCMessage(retCh chan proto.Message, result proto.Message) {
	select {
	case retCh <- result:
	default:
		l4g.Errorf("OnGRPCMessage return failed: %+v", result)
	}
}

type GrpcAsyncMessage struct {
	srv       *LogicService
	timeoutID uint32 //定时器id
	cb        func(uint32, interface{}) bool
}

func (ga *GrpcAsyncMessage) Work(cret uint32, data interface{}) bool {
	if ga.timeoutID > 0 {
		//存在超时id
		ga.srv.DeleteTimerID(ga.timeoutID)
	}
	return ga.cb(cret, data)
}

func (ga *GrpcAsyncMessage) AddTimeoutID(id uint32) {
	ga.timeoutID = id
}

// 检查索引数据
func (l *LogicService) checkUserIndex(data *gm.UserIndex) bool {
	if l.ServerID() == data.ServerId {
		switch data.Type {
		case uint32(gm.SEARCH_BY_ID):
			if len(data.Ids) > 0 && len(data.Values) == 0 {
				return true
			}
		case uint32(gm.SEARCH_BY_NAME), uint32(gm.SEARCH_BY_UUID):
			if len(data.Values) > 0 && len(data.Ids) == 0 {
				return true
			}
		}
	}
	return false
}

// 玩家信息查询
func (l *LogicService) onGRPCGetUser(data *gm.UserIndex, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.RetUser{Code: uint32(ret.RET_OK)}
	if !l.checkUserIndex(data) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.Type == uint32(gm.SEARCH_BY_ID) {
		// if u := l.UserM().GetUser(data.Ids[0]); u != nil {
		/* if u := l.UserM().GetUserWithCache(data.Ids[0]); u != nil {
			msg.Data = u.Clone()
			msg.Data.Equips = u.EquipManager().GrpcClone()
			msg.Data.Gems = u.GemManager().GrpcClone()
			msg.Data.Emblems = u.EmblemManager().GrpcClone()
			msg.Data.Artifacts = u.ArtifactManager().GrpcClone()
			msg.Data.Heroes = u.HeroManager().GrpcClone()
			return msg, true
		}*/
		l.getUserFromDB(data.Ids[0], retCh)
	} else {
		l.getUserID(data.ServerId, data.Type, data.Values[:1], nil, func(values []string, ids []uint64) {
			if len(ids) == 0 || ids[0] == 0 {
				resultOnGRPCMessage(retCh, &gm.RetUser{Code: uint32(ret.RET_USER_NOT_EXIST)})
			} else {
				l.getUserFromDB(ids[0], retCh)
			}
		})
	}
	return msg, false
}

// 获取玩家ID
// serverID: 玩家创角归属的serverID，考虑合服情况，user.ServerID 不一定等于 l.ServerID()
// 所以要把玩家的serverID传进来
func (l *LogicService) getUserID(serverID uint64, typ uint32, values []string, ids []uint64, cb func([]string, []uint64)) {
	if typ == uint32(gm.SEARCH_BY_UUID) {
		tmps := make([]string, 0, len(values))
		for _, value := range values {
			//这里不要去l.ServerID()
			//合服之后，l.ServerID()变了，用玩家原来的serverid
			tmps = append(tmps, fmt.Sprintf("%s:%d", value, serverID))
		}
		values = tmps
	}
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_GetUserID), 0, &r2l.L2R_GRPC_GetUserID{
		Type:   typ,
		Values: values,
		IDs:    ids,
		Callback: func(data interface{}) {
			tmp := data.(*r2l.R2L_GRPC_GetUserID)
			cb(tmp.Values, tmp.IDs)
		},
	})
}

// 加载玩家信息
func (l *LogicService) getUserFromDB(uid uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_Login), 0, &r2l.L2R_GRPC_Login{
		ID: uid,
		Callback: func(result proto.Message) {
			resultOnGRPCMessage(retCh, &gm.RetUser{
				Code: uint32(ret.RET_OK),
				Data: result.(*r2l.R2L_Login),
			})
		},
	})
}

// 玩家道具删除
func (l *LogicService) onGRPCReduceResources(data *gm.Object, _ chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if data.User.Type != uint32(gm.SEARCH_BY_ID) &&
		len(data.User.Ids) != 1 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
	} else if u := l.UserM().GetUser(data.User.Ids[0]); u == nil {
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
	} else {
		msg.Code = u.Consume(l, data.Ress, uint32(log.RESOURCE_CHANGE_REASON_GM), 0)
	}
	return msg, true
}

// 玩家封禁解禁
func (l *LogicService) onGRPCBanAccount(data *gm.BanAccountReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCBanAccount: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if data == nil {
		l4g.Errorf("[LogicService] onGRPCBanAccount: data nil")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.User == nil {
		l4g.Errorf("[LogicService] onGRPCBanAccount: data user nil")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if !l.checkUserIndex(data.User) {
		l4g.Errorf("[LogicService] onGRPCBanAccount: check user index err, type:%d, ids len:%d, values len: %d",
			data.User.Type, len(data.User.Ids), len(data.User.Values))
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if len(data.User.Values) > gmSliceMaxLength {
		l4g.Errorf("[LogicService] onGRPCBanAccount: user length abnormal, resources: %d", len(data.User.Values))
		msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
		return msg, true
	}
	if data.Ban == nil {
		l4g.Errorf("[LogicService] onGRPCBanAccount: data ban nil")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.Ban.Type == uint32(common.BAN_TYPE_NONE) || data.Ban.Type >= uint32(common.BAN_TYPE_MAX) {
		l4g.Errorf("[LogicService] onGRPCBanAccount: ban type error, type: %d", data.Ban.Type)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.User.Type == uint32(gm.SEARCH_BY_ID) {
		l.banAccount(data.User.Ids, data.Ban)
		return msg, true
	}

	l.getUserID(data.User.ServerId, data.User.Type, data.User.Values, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || len(ids) != len(values) {
			msg.Code = uint32(ret.RET_USER_NOT_FOUND)
		} else {
			l.banAccount(ids, data.Ban)
		}
		resultOnGRPCMessage(retCh, msg)
	})
	return msg, false
}

// 封禁和解禁
func (l *LogicService) banAccount(uids []uint64, data *db.GMBan) {
	now := time.Now().Unix()
	for _, uid := range uids {
		//l.banChat(uid, data)
		if data.Start == data.End && data.End == 0 {
			l.BanAccountM().Delete(uid, data.Type)
		} else {
			if (data.Type == uint32(common.BAN_TYPE_LOGIN) ||
				data.Type == uint32(common.BAN_TYPE_LOGIN_TEMPORARY)) &&
				ban.DuringBanTime(data, now) {
				l.UserM().BanAccount(uid, l)
			}
			l.BanAccountM().Insert(uid, data.Clone())
		}
	}
}

//// 聊天 - 禁言/解禁：调用聊天服的接口实现
//func (l *LogicService) banChat(uid uint64, data *db.GMBan) {
//	if data.Type != uint32(common.BAN_TYPE_CHAT) {
//		return
//	}
//
//	userID := strconv.FormatUint(uid, 10)
//	msg := &p2l.L2P_ChatBan{
//		UniqueId: strconv.FormatUint(l.CreateUserLogID(), 10),
//		Ban: &p2l.ChatBan{
//			UserID: fmt.Sprintf("omniheros_%s", userID),
//		},
//	}
//	if data.Start == data.End && data.End == 0 {
//		msg.Ban.MuteSeconds = 0
//	} else {
//		msg.Ban.MuteSeconds = data.End
//	}
//
//	l.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_ChatBan), uid, msg)
//}

// 玩家邮件
func (l *LogicService) onGRPCSendUserMail(data *gm.UsersMail, retCh chan proto.Message) (*gm.Result, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if !l.checkUserIndex(data.User) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	l4g.Debugf("grpc [LogicService] onGRPCSendUserMail: recv %s", data)
	txt := make([]string, 0, 2) //title content
	txt = append(txt, data.Title, data.Txt)
	if data.User.Type == uint32(gm.SEARCH_BY_ID) {
		character.GMMail(l, txt, data.User.Ids, data, msg)
		return msg, true
	}

	l.getUserID(data.User.ServerId, data.User.Type, data.User.Values, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || len(values) != len(ids) {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
		} else {
			character.GMMail(l, txt, ids, data, msg)
		}
		resultOnGRPCMessage(retCh, msg)
	})
	return msg, false
}

// 全服邮件
func (l *LogicService) onGRPCSendServerMail(data *gm.ServerMail, _ chan proto.Message) (*gm.Result, bool) {
	l4g.Infof("grpc [LogicService] onGRPCSendServerMail: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	ops := len(data.Ops)
	channels := len(data.Channels)
	if ops > gmSliceMaxLength || channels > gmSliceMaxLength {
		l4g.Errorf("[LogicService] onGRPCSendServerMail: ops or channels length error, ops:%d channels:%d",
			ops, channels)
		msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
	} else if ops != 1 && channels > 0 {
		l4g.Errorf("[LogicService] onGRPCSendServerMail: ops or channels error, ops:%d channels:%d", ops, channels)
		msg.Code = uint32(ret.RET_ERROR)
	} else if len(data.Awards) > gmSliceMaxLength {
		l4g.Errorf("[LogicService] onGRPCSendServerMail: awards length abnormal, resources: %d", len(data.Awards))
		msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
	} else if util.InUint32s(data.Ops, 0) || util.InUint32s(data.Channels, 0) {
		l4g.Errorf("[LogicService] onGRPCSendServerMail: ops or channels illegal, ops:%+v channels:%+v",
			data.Ops, data.Channels)
		msg.Code = uint32(ret.RET_ERROR)
	} else {
		character.GMServerMail(l, data, msg)
	}
	return msg, true
}

// 切换用户日志跟踪
func (l *LogicService) onGRPCToggleUserLogTrace(data *gm.LogTraceOp, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCToggleUserLogTrace: recv %s", data)
	msg := &gm.Result{Code: uint32(ret.RET_OK)}
	if data.User == nil || !l.checkUserIndex(data.User) {
		l4g.Errorf("[LogicService] onGRPCToggleUserLogTrace: check user index err, type:%d, ids len:%d, values len: %d",
			data.User.Type, len(data.User.Ids), len(data.User.Values))
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.User.Type == uint32(gm.SEARCH_BY_ID) {
		uid := data.User.Ids[0]
		if !base.IsValidUserID(l, uid) {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			return msg, true
		}
		if data.Trace {
			l.logTraceUsers[uid] = util.None{}
		} else {
			delete(l.logTraceUsers, uid)
		}
		return msg, true
	}
	l.getUserID(data.User.ServerId, data.User.Type, data.User.Values[:1], nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			resultOnGRPCMessage(retCh, &gm.Result{Code: uint32(ret.RET_USER_NOT_EXIST)})
		} else {
			uid := ids[0]
			if data.Trace {
				l.logTraceUsers[uid] = util.None{}
			} else {
				delete(l.logTraceUsers, uid)
			}
			resultOnGRPCMessage(retCh, &gm.Result{Code: uint32(ret.RET_OK)})
		}
	})
	return msg, false
}

// 在线人数
func (l *LogicService) onGRPCOnlineNum(data *gm.Cmd, _ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCOnlineNum: recv %s", data)
	levelR := l.GetGlobalRank(rank.LEVEL)
	_, lastRank := levelR.LastInRange(&skiplist.RangeSpec{
		Min:     effectiveMinLevel,
		Max:     uint64(goxml.GetData().PlayerLevelInfoM.MaxLevel()),
		Reverse: true,
	})
	msg := &gm.RetOnlineNum{
		Code:         uint32(ret.RET_OK),
		EffectiveNum: lastRank,
		RegisterNum:  levelR.Length(),
		OnlineNum:    uint32(l.userM.GetOnlineNum()),
	}
	return msg, true
}

// 修改背包数据
func (l *LogicService) onGRPCChangeBags(data *gm.BagsOp, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCChangeBags: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if !l.checkUserIndex(data.User) {
		l4g.Errorf("[LogicService] onGRPCChangeBags: check user index err, type:%d, ids len:%d, values len: %d",
			data.User.Type, len(data.User.Ids), len(data.User.Values))
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.User.Type == uint32(gm.SEARCH_BY_NAME) || data.User.Type == uint32(gm.SEARCH_BY_UUID) {
		l.getUserID(data.User.ServerId, data.User.Type, data.User.Values[:1], nil, func(values []string, ids []uint64) {
			if len(ids) == 0 || ids[0] == 0 {
				msg.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
			} else {
				l.changeUserBags(ids[0], data.Bag, retCh, msg)
			}
		})
	} else {
		l.changeUserBags(data.User.Ids[0], data.Bag, retCh, msg)
	}
	return msg, false
}

func (l *LogicService) changeUserBags(userID uint64, bags *db.Bags, retCh chan proto.Message, msg *gm.Result) {
	if !l.BanAccountM().Check(userID, uint32(common.BAN_TYPE_LOGIN)) &&
		!l.BanAccountM().Check(userID, uint32(common.BAN_TYPE_LOGIN_TEMPORARY)) {
		msg.Code = uint32(ret.RET_BAN_USER_BEFORE_OP)
		resultOnGRPCMessage(retCh, msg)
		return
	}
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ChangeBags), userID, &r2l.L2R_GRPC_ChangeBags{
		UserId: userID,
		Bag:    bags,
		Callback: func(retCode uint32, delResources []*cl.Resource) {
			if retCode != uint32(ret.RET_OK) {
				msg.Code = retCode
			} else {
				character.NewMaybeOfflineUserLog(l, userID).LogDeleteUserResources(l, delResources) // 日志记录
			}
			resultOnGRPCMessage(retCh, msg)
		},
	})
}

// 发布问卷
func (l *LogicService) onGRPCSendQuestionnaire(data *cl.Questionnaire, _ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCSendQuestionnaire: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	lenOpID := len(data.OpId)
	lenChannel := len(data.Channel)
	if lenOpID != 1 && lenChannel > 0 {
		l4g.Errorf("grpc [LogicService] onGRPCSendQuestionnaire, opID length %d, channel length %d", lenOpID, lenChannel)
		msg.Code = uint32(ret.RET_ERROR)
	} else if len(data.Awards) > gmSliceMaxLength {
		l4g.Errorf("[LogicService] onGRPCSendQuestionnaire: awards length abnormal, awards: %d", len(data.Awards))
		msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
	} else if gmxml.QuestionnaireInfoM.Update(data) {
		smsg := &cl.L2C_QuestionnaireUpdate{}
		l.BroadcastCmdByOpIDAndChannel(cl.ID_MSG_L2C_QuestionnaireUpdate, smsg, data.OpId, data.Channel)
	} else {
		msg.Code = uint32(ret.RET_ERROR)
	}

	return msg, true
}

// 设置主线关卡ID
func (l *LogicService) onGRPCSetDungeonID(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user != nil {
		dungeonM := user.Dungeon()
		dungeonM.SetDungeonID(data.Value)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.changeDungeonID(ids[0], data.Value, retCh)
		}
	})
	return msg, false
}

func (l *LogicService) changeDungeonID(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ChangeDungeonID), userID, &r2l.L2R_GRPC_ChangeDungeonID{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 设置新手引导状态
func (l *LogicService) onGRPCSetGuidanceClose(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetGuidanceClose. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.guidanceClose(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) guidanceClose(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_GuidanceClose), userID, &r2l.L2R_GRPC_GuidanceClose{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 修改玩家等级
func (l *LogicService) onGRPCSetUserLevel(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetUserLevel. data:%+v", data)
	if user != nil {
		user.SetLevel(data.Value)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setUserLevel(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setUserLevel(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetUserLevel), userID, &r2l.L2R_GRPC_SetUserLevel{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 发送顶级账号资源
func (l *LogicService) onGRPCSendTopResource(data *gm.UserReq, _ chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSendTopResource. data:%+v", data)
	if user == nil {
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		return msg, true
	}

	msg.Code = character.SendTopResource(l, user, data.Type, data.Value)

	return msg, true
}

// 清除玩家资源
func (l *LogicService) onGRPCClearUserResource(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.clearUserResource(ids[0], retCh)
		}
	})

	return msg, false
}

func (l *LogicService) clearUserResource(userID uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ClearUserResource), userID, &r2l.L2R_GRPC_ClearUserResource{
		ID: userID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 活动信息
func (l *LogicService) onGRPCGetActivityInfo(data *gm.UserIndex, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.ActivityInfoRsp{Code: uint32(ret.RET_OK)}
	if !l.checkUserIndex(data) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.Type == uint32(gm.SEARCH_BY_ID) {
		if u := l.UserM().GetUserWithCache(data.Ids[0]); u != nil {
			msg.Data = &r2l.GmActivityInfo{
				SevenDay: &r2l.GmSevenDayLogin{},
				Carnival: &r2l.GmCarnivalInfo{},
			}
			msg.Data.SevenDay.Days = u.SevenDayLogin().GetData().Days
			msg.Data.SevenDay.Awards = getSevenDayAwards(u.SevenDayLogin().GetData().RewardStatus)
			for _, c := range u.Carnival().GetData() {
				//msg.Data.Carnival.Tasks = append(msg.Data.Carnival.Tasks, getAwardedCarnivalTasks(l.StartServiceTm(), c)...)
				msg.Data.Carnival.Tasks = append(msg.Data.Carnival.Tasks, getAwardedCarnivalTasks(u.CreateTime(), c)...)
			}
			return msg, true
		}
		l.getActivityInfoFromDB(data.Ids[0], retCh)
	} else {
		l.getUserID(data.ServerId, data.Type, data.Values[:1], nil, func(values []string, ids []uint64) {
			if len(ids) == 0 || ids[0] == 0 {
				msg.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
			} else {
				l.getActivityInfoFromDB(ids[0], retCh)
			}
		})
	}
	return msg, false
}

func (l *LogicService) getActivityInfoFromDB(uid uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ActivityInfo), 0, &r2l.L2R_GRPC_ActivityInfo{
		ID: uid,
		Callback: func(result proto.Message) {
			activityInfo := &r2l.GmActivityInfo{}
			retData, ok := result.(*r2l.R2L_UserBase)
			if ok {
				if retData == nil || retData.User == nil || retData.User.Module1 == nil {
					return
				}
				// 七日登录
				if retData.User.Module1.SevenDayLogin != nil {
					activityInfo.SevenDay = &r2l.GmSevenDayLogin{}
					activityInfo.SevenDay.Days = retData.User.Module1.SevenDayLogin.Days
					activityInfo.SevenDay.Awards = getSevenDayAwards(retData.User.Module1.SevenDayLogin.RewardStatus)
				}
				// 嘉年华
				if retData.User.Module1.Carnival != nil {
					activityInfo.Carnival = &r2l.GmCarnivalInfo{}
					for _, carnival := range retData.User.Module1.Carnival {
						activityInfo.Carnival.Tasks = append(activityInfo.Carnival.Tasks,
							//getAwardedCarnivalTasks(l.StartServiceTm(), carnival)...)
							getAwardedCarnivalTasks(retData.User.Base.CreateTime, carnival)...)
					}
				}
			}

			resultOnGRPCMessage(retCh, &gm.ActivityInfoRsp{
				Code: uint32(ret.RET_OK),
				Data: activityInfo,
			})
		},
	})
}

func getSevenDayAwards(AwardStatus uint32) (awards []*cl.Resource) {
	maxID := goxml.GetData().SevenDayLoginM.GetMaxId()
	for i := uint32(1); i < maxID; i++ {
		if util.BitAndUint32(AwardStatus, int(i)) {
			if info := goxml.GetData().SevenDayLoginM.Index(i); info != nil {
				awards = append(awards, info.RewardClRes...)
			}
		}
	}
	return
}

func getAwardedCarnivalTasks(startTm int64, carnival *cl.Carnival) []*r2l.GmCarnivalTask {
	carnivalIDMap := goxml.GetData().CarnivalInfoM.GetCarnivalID(helper.DaysAfterStartOfService(startTm, time.Now().Unix()))
	taskInfos := goxml.GetData().CarnivalTaskInfoM.GetCompletedTasks(carnival.TaskTypeProgress)
	retTasks := make([]*r2l.GmCarnivalTask, 0, len(taskInfos))
	for _, taskInfo := range taskInfos {
		if taskInfo == nil {
			continue
		}
		if _, exist := carnivalIDMap[taskInfo.CarnivalId]; !exist {
			continue
		}
		carnivalTask := &r2l.GmCarnivalTask{
			Id:         taskInfo.Id,
			Value:      taskInfo.Value,
			CarnivalId: taskInfo.CarnivalId,
			Day:        taskInfo.DayNumber,
		}
		if _, exist := carnival.Awarded[taskInfo.Id]; exist {
			carnivalTask.Awards = taskInfo.ClRes
		}

		retTasks = append(retTasks, carnivalTask)
	}

	return retTasks
}

// 重置各玩法次数回到初始状态
func (l *LogicService) onGRPCResetDailyNum(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("uuid: %s ResetDailyNum failed, guildManager not exist", data.Uuid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetDailyNum. data:%+v", data)
	if user != nil {
		for k := range user.DailyInfo().NumInfo {
			delete(user.DailyInfo().NumInfo, k)
		}
		if user.DailyInfo().NumInfo == nil {
			user.DailyInfo().NumInfo = make(map[uint32]*cl.NumInfo)
		}

		for numberType := range goxml.SpecialNumberTypePool {
			user.DailyInfo().NumInfo[numberType] = goxml.MakeSpecialNumInitData(numberType, user.Level(), 10)
		}
		user.SaveDaily()

		shop := user.ShopM().GetShop(goxml.GrpcShopID)
		if shop != nil {
			newShop := shop.Flush()
			shopInfo := goxml.GetData().ShopInfoM.Index(goxml.GrpcShopID)
			if newShop != nil && shopInfo != nil {
				newShop.FreeRefreshLeftNum = shopInfo.FreeRefreshLimit
				newShop.FreeRefreshRecoverTm = time.Now().Unix()
				user.ShopM().Save(goxml.GrpcShopID)
			}
		}

		guildUser := guildM.GetGuildUser(user.ID())
		if guildUser != nil {
			guildUser.SetBuyCount(0)
			guildUser.SetChallengeTimes(goxml.GetData().GuildConfigInfoM.DungeonMaxChallengeTimes)
			guildM.SaveGuildUser(guildUser)
		}

		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetDailyNum(ids[0], retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetDailyNum(userID uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetDailyNum), userID, &r2l.L2R_GRPC_ResetDailyNum{
		ID: userID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 修改功勋等级
func (l *LogicService) onGRPCSetMedalLevel(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetMedalLevel. data:%+v", data)
	if user != nil {
		medal := user.Medal().GetMedal()
		if medal != nil {
			medal.Level = data.Value
			for k := range medal.TaskAwarded {
				delete(medal.TaskAwarded, k)
			}

		} else {
			user.Medal().SetData(&cl.Medal{Level: data.Value})
		}
		user.Medal().Save()
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setMedalLevel(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setMedalLevel(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetMedalLevel), userID, &r2l.L2R_GRPC_SetMedalLevel{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 重置迷宫
func (l *LogicService) onGRPCResetMaze(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetMaze. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetMaze(ids[0], retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetMaze(userID uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetMaze), userID, &r2l.L2R_GRPC_ResetMaze{
		ID: userID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 重置公会退出时间
func (l *LogicService) onGRPCResetGuildQuitTm(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetGuildQuitTm. data:%+v", data)
	if user != nil {
		guildM := l.GuildM()
		if guildM != nil {
			guildUser := guildM.GetGuildUser(user.ID())
			if guildUser != nil {
				guildUser.SetLeaveTm(0)
				guildM.SaveGuildUser(guildUser)
			}
		}

		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetGuildQuitTm(ids[0], retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetGuildQuitTm(userID uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetGuildQuitTm), userID, &r2l.L2R_GRPC_ResetGuildQuitTm{
		ID: userID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 重置爬塔层数
func (l *LogicService) onGRPCResetTowerFloor(data *gm.TowerReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetTowerFloor. data:%+v", data)
	if user != nil {
		if goxml.GetData().TowerInfoM.Index(data.TowerType, data.Value) == nil {
			msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
			return msg, true
		}
		user.Towers().Update(data.TowerType, data.Value)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetTowerFloor(ids[0], data.TowerType, data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetTowerFloor(userID uint64, towerType, floor uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetTowerFloor), userID, &r2l.L2R_GRPC_ResetTowerFloor{
		ID:    userID,
		Type:  towerType,
		Value: floor,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 重置材料本等级
func (l *LogicService) onGRPCResetTrialLevel(data *gm.TrialReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetTrialLevel. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetTrialLevel(ids[0], data.Type, data.Level, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetTrialLevel(userID uint64, trialType, level uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetTrialLevel), userID, &r2l.L2R_GRPC_ResetTrialLevel{
		ID:    userID,
		Type:  trialType,
		Level: level,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 重置竞技场积分
func (l *LogicService) onGRPCResetArenaScore(data *gm.ArenaReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetArenaScore. data:%+v", data)
	if user != nil {
		code := l.updateArenaScore(user.ID(), data.Score)
		msg.Code = uint32(code)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetArenaScore(ids[0], data.Score, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetArenaScore(userID uint64, score uint32, retCh chan proto.Message) {
	code := l.updateArenaScore(userID, score)
	resultOnGRPCMessage(retCh, &gm.Result{
		Code: uint32(code),
	})
}

func (l *LogicService) updateArenaScore(userID uint64, score uint32) ret.RET {
	arenaUser := l.ArenaM().Get(userID)
	if arenaUser == nil {
		return ret.RET_ARENA_NO_USER_DATA
	}
	l.ArenaM().GrpcUpdateScore(l, userID, int32(score))
	return ret.RET_OK
}

// 重置个人Boss floor
func (l *LogicService) onGRPCResetMirageFloor(data *gm.MirageReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetTrialLevel. data:%+v", data)
	if user != nil {
		//affixIDs :=  goxml.GetData().MirageAffixGroupInfoM.Index(data.HurdleId)
		//mirageM := user.MirageManager()
		//mirageM.AddMirage(l, data.HurdleId, affixIDs)
		//mirage := mirageM.Get(data.HurdleId)
		//if mirage != nil {
		//	hurdleInfo :=  goxml.GetData().MirageHurdleInfoM.Index(data.HurdleId)
		//	if hurdleInfo == nil {
		//		msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
		//		return msg, true
		//	}
		//	mirage.UpdateMirage(l, hurdleInfo.PassStar)
		//	mirageM.UpdateMaxWinHurdle(hurdleInfo.Type, data.HurdleId)
		//	mirage.SetAffixes(affixIDs)
		//	mirageM.SetChange(data.HurdleId)
		//}

		//msg.Code = uint32(ret.RET_OK)
		//return msg, true
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetMirageFloor(ids[0], data.HurdleId, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetMirageFloor(userID uint64, hurdleID uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetMirageFloor), userID, &r2l.L2R_GRPC_ResetMirageFloor{
		ID:       userID,
		HurdleID: hurdleID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCResetScore(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetMedalLevel. data:%+v", data)
	if user != nil {
		// 嘉年华
		for _, carnival := range user.Carnival().GetData() {
			if progress, exist := carnival.TaskTypeProgress[goxml.CarnivalTaskScore]; exist {
				progress.Progress = uint64(data.Value)
			} else {
				p := &cl.TaskTypeProgress{}
				p.TaskTypeId = goxml.CarnivalTaskScore
				p.Progress = uint64(data.Value)
				carnival.TaskTypeProgress[goxml.CarnivalTaskScore] = p
			}
		}
		user.Carnival().Save()

		// 日常
		dailyTask := user.DailyTask().GetData()
		if progress, exist := dailyTask.TaskTypeProgress[goxml.DailyTaskTypeID]; exist {
			progress.Progress = uint64(data.Value)
		} else {
			p := &cl.TaskTypeProgress{}
			p.TaskTypeId = goxml.DailyTaskTypeID
			p.Progress = uint64(data.Value)
			dailyTask.TaskTypeProgress[goxml.DailyTaskTypeID] = p
		}
		user.DailyTask().Save()

		// 周常
		weeklyTask := user.WeeklyTask().GetData()
		if progress, exist := weeklyTask.TaskTypeProgress[goxml.WeeklyTaskTypeID]; exist {
			progress.Progress = uint64(data.Value)
		} else {
			p := &cl.TaskTypeProgress{}
			p.TaskTypeId = goxml.WeeklyTaskTypeID
			p.Progress = uint64(data.Value)
			weeklyTask.TaskTypeProgress[goxml.WeeklyTaskTypeID] = p
		}
		user.WeeklyTask().Save()
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetScore(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetScore(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetScore), userID, &r2l.L2R_GRPC_ResetScore{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 重置列传章节完成
func (l *LogicService) onGRPCResetTaleChapterFinish(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetTaleChapterFinish. data:%+v", data)
	if user != nil {
		taleInfo := goxml.GetData().TalesDungeonInfoM.Index(data.Value)
		if taleInfo == nil {
			msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
			return msg, true
		}
		user.Tales().SetFinish(taleInfo.TalesId, data.Value)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetTaleChapterFinish(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetTaleChapterFinish(userID uint64, chapterID uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetTaleChapterFinish), userID, &r2l.L2R_GRPC_ResetTaleChapterFinish{
		ID:    userID,
		Value: chapterID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 重置列传强敌挑战
func (l *LogicService) onGRPCResetTaleElite(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetTaleChapterFinish. data:%+v", data)
	if user != nil {
		if goxml.GetData().TalesEliteInfoM.Index(data.Value) == nil {
			msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
			return msg, true
		}
		user.Tales().SetElitePassed(data.Value)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetTaleElite(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetTaleElite(userID uint64, chapterID uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetTaleElite), userID, &r2l.L2R_GRPC_ResetTaleElite{
		ID:    userID,
		Value: chapterID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 修改玩家资源
func (l *LogicService) onGRPCSetUserResource(data *gm.UserResource, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc onGRPCSetUserResource: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	// if user != nil {
	// 	l.UserM().KickAccount(user.ID(), l)
	// }
	if user != nil {
		eret, _ := user.Award(l, data.Res, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GM), 0)
		if eret != uint32(ret.RET_OK) {
			l4g.Errorf("grpc.onGRPCSetUserResource: award failed, uid:%d, res:%v", user.ID(), data.Res)
		} else {
			l4g.Infof("grpc.onGRPCSetUserResource: award success, uid:%d, res:%v", user.ID(), data.Res)
		}
		msg.Code = uint32(eret)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setUserResource(ids[0], data.Res, retCh)
		}
	})
	return msg, false
}

func (l *LogicService) setUserResource(userID uint64, res []*cl.Resource, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetUserResource), userID, &r2l.L2R_GRPC_SetUserResource{
		ID:        userID,
		Resources: res,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 充值订单
func (l *LogicService) onGRPCNotifyRecharge(data *db.Order, retCh chan proto.Message) (
	proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCNotifyRecharge: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if data == nil {
		l4g.Errorf("[LogicService] onGRPCNotifyRecharge: data nil")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if l.ServerID() != data.ServerId {
		l4g.Errorf("[LogicService] onGRPCNotifyRecharge: server id error, order:%+v", data)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.Amount == 0 {
		l4g.Errorf("[LogicService] onGRPCNotifyRecharge: amount error, order:%+v", data)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if !base.IsValidUserID(l, data.UserId) {
		l4g.Errorf("[LogicService] onGRPCNotifyRecharge: user id error, order:%+v", data)
		msg.Code = uint32(ret.RET_USER_NOT_EXIST)
		return msg, true
	}
	data.Custom = &cl.OrderCustomData{}
	if err := json.Unmarshal([]byte(data.CustomData), data.Custom); err != nil {
		l4g.Errorf("[LogicService] onGRPCNotifyRecharge: custom unmarshal error:%v, order:%+v", err, data)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	//db会自动把订单加入到redis的waitProcessOrder集合中
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_AddOrder), 0, &r2l.L2R_GRPC_AddOrder{
		Order: data,
		Callback: func(retCode uint32, order *db.Order) {
			l4g.Infof("[LogicService] onGRPCNotifyRecharge: add order callback:%d %+v", retCode, order)
			msg.Code = retCode
			if retCode == uint32(ret.RET_OK) {
				//在线处理订单
				user := l.UserM().GetUserWithCache(order.UserId)
				if user != nil {
					//如果是缓存User，也需要进行添加，等上线再进行发货
					user.Orders().Add(order)
					if user.IsOnline() {
						user.SetContextID(l.CreateLogID()) //为了记录日志用
						user.Orders().Process(l, []*db.Order{order})
						l4g.Infof("[LogicService] onGRPCNotifyRecharge: online order:%+v", order)
						if order.Status != uint32(common.ORDER_STATUS_OS_SUCCESS) {
							msg.Code = uint32(ret.RET_RECHARGE_ORDER_PROCESS_FAIL)
						}
					} else {
						l4g.Infof("[LogicService] onGRPCNotifyRecharge: cache order:%d %+v", retCode, order)
					}
				} else {
					l4g.Infof("[LogicService] onGRPCNotifyRecharge: offline order:%d %+v", retCode, order)
				}
			}
			resultOnGRPCMessage(retCh, msg)
		},
	})
	return msg, false
}

// 发布礼包码
func (l *LogicService) onGRPCSendGiftCodeInfo(data *gm.GiftCodeInfo, _ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCSendGiftCodeInfo: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if len(data.Awards) > gmSliceMaxLength {
		l4g.Errorf("[LogicService] onGRPCSendGiftCodeInfo: awards length abnormal, awards: %d", len(data.Awards))
		msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
	} else if !gmxml.GiftCodeInfoM.Update(*appsrv.GMDataPath, data) {
		msg.Code = uint32(ret.RET_ERROR)
	}

	return msg, true
}

// 修改玩家vip
func (l *LogicService) onGRPCSetUserVip(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetUserVip. data:%+v", data)
	vipInfo := goxml.GetData().VipInfoM.Index(data.Value)
	if vipInfo == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if user != nil {
		user.SetVip(data.Value)
		user.SetVipExp(vipInfo.Exp)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setUserVip(ids[0], data.Value, vipInfo.Exp, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setUserVip(userID uint64, vip, vipExp uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetUserVip), userID, &r2l.L2R_GRPC_SetUserVip{
		ID:     userID,
		Vip:    vip,
		VipExp: vipExp,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCSetTowerstarDungeonID(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetTowerstarDungeonID. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setTowerstarDungeonID(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setTowerstarDungeonID(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetTowerstarDungeonID), userID, &r2l.L2R_GRPC_SetTowerstarDungeonID{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCResetTowerstar(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCResetTowerstar. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.resetTowerstar(ids[0], retCh)
		}
	})

	return msg, false
}

func (l *LogicService) resetTowerstar(userID uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ResetTowerstar), userID, &r2l.L2R_GRPC_ResetTowerstar{
		ID: userID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 设置gm config
func (l *LogicService) onGRPCSetGmConfig(data *gm.GmConfigReq) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCSetRate: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("[LogicService] onGRPCSetRate: data is nil. ")
		return msg, true
	}

	if data.Status != gmxml.GmConfigOpen && data.Status != gmxml.GmConfigClose {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("[LogicService] onGRPCSetRate: status is invalid.")
		return msg, true
	}

	if retCode := l.setGmConfig(data); retCode != uint32(ret.RET_OK) {
		msg.Code = retCode
		l4g.Errorf("[LogicService] onGRPCSetRate: set rate status failed. ret: %d", retCode)
		return msg, true
	}

	return msg, true
}

func (l *LogicService) setGmConfig(data *gm.GmConfigReq) uint32 {
	if !gmxml.GmConfigInfoM.Update(data.Id, data.Status) {
		l4g.Errorf("[LogicService] setGmConfig: save file failed.")
		return uint32(ret.RET_ERROR)
	}

	return uint32(ret.RET_OK)
}

// 更新多语言
func (l *LogicService) onGRPCUpdateMultiLangs(data *gm.MultiLangs, _ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCUpdateMultiLangs: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	l.MultiLangM().UpdateMultiLangs(l, data)

	return msg, true
}

// 完成问卷
func (l *LogicService) onGRPCQuestionnaireFinish(data *gm.QuestionnaireFinishReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCQuestionnaireFinish: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if !l.checkUserIndex(data.User) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCQuestionnaireFinish: userIndex error.")
		return msg, true
	}
	if data.User.Type != uint32(gm.SEARCH_BY_ID) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCQuestionnaireFinish: userType error. type:%d", data.User.Type)
		return msg, true
	}
	userID := data.User.Ids[0]
	if !l.QuestionnaireM().IsQuestionnaireExist(data.QuestionnaireId) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCQuestionnaireFinish: questionnaireInfo not exist. id:%d", data.QuestionnaireId)
		return msg, true
	}
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetQuestionnaireFinish), userID, &r2l.L2R_GRPC_SetQuestionnaireFinish{
		ID:              userID,
		QuestionnaireID: data.QuestionnaireId,
		Callback: func(retCode uint32, userID uint64, finishID uint32) {
			l4g.Infof("[LogicService] onGRPCQuestionnaireFinish: callback:%d %+v", retCode, finishID)
			msg.Code = retCode
			if retCode != uint32(ret.RET_OK) {
				l4g.Errorf("grpc [LogicService] onGRPCQuestionnaireFinish: set finish error. id:%d", data.QuestionnaireId)
				resultOnGRPCMessage(retCh, msg)
				return
			}
			//在线处理
			user := l.UserM().GetUserWithCache(userID)
			if user == nil {
				resultOnGRPCMessage(retCh, msg)
				return
			}

			user.LogQuestionnaireFinish(l, finishID)

			//如果是缓存User，也需要进行添加
			user.Questionnaires().AddFinishID(finishID)
			if user.IsOnline() {
				smsg := &cl.L2C_QuestionnaireUpdate{}
				user.SendCmdToGateway(cl.ID_MSG_L2C_QuestionnaireUpdate, smsg)
			}
			resultOnGRPCMessage(retCh, msg)
		},
	})

	return msg, false
}

// 发布可配置活动
func (l *LogicService) onGRPCReleaseOperateActivity(data *gm.OperateActivity,
	_ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCReleaseOperateActivity: recv %s", data)
	msg := &gm.Result{Code: uint32(ret.RET_OK)}
	if data.Base != nil {
		if len(data.Base.Changes) > gmSliceMaxLength || len(data.Base.Deletes) > gmSliceMaxLength {
			l4g.Errorf("[LogicService] onGRPCReleaseOperateActivity: base datas length abnormal, changes: %d, deletes: %d",
				len(data.Base.Changes), len(data.Base.Deletes))
			msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
		}
		if data.Gift != nil {
			if len(data.Gift.Changes) > gmSliceMaxLength || len(data.Gift.Deletes) > gmSliceMaxLength {
				l4g.Errorf("[LogicService] onGRPCReleaseOperateActivity: gift datas length abnormal, changes: %d, deletes: %d",
					len(data.Gift.Changes), len(data.Gift.Deletes))
				msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
			}
		}
		if data.Task != nil {
			if len(data.Task.Changes) > gmSliceMaxLength || len(data.Task.Deletes) > gmSliceMaxLength {
				l4g.Errorf("[LogicService] onGRPCReleaseOperateActivity: task datas length abnormal, changes: %d, deletes: %d",
					len(data.Gift.Changes), len(data.Gift.Deletes))
				msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
			}
		}
		if data.PromotionGifts != nil {
			if len(data.PromotionGifts.Changes) > gmSliceMaxLength || len(data.PromotionGifts.Deletes) > gmSliceMaxLength {
				l4g.Errorf("[LogicService] onGRPCReleaseOperateActivity: promotionGifts datas length abnormal, changes: %d, deletes: %d",
					len(data.PromotionGifts.Changes), len(data.PromotionGifts.Deletes))
				msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
			}
		}
		cret := gmxml.OperateActivityInfoM.Check(data)
		if cret != uint32(ret.RET_OK) {
			l4g.Errorf("[LogicService] OperateActivityInfoM: check error err:%d", cret)
			msg.Code = cret
		}

		if data.Task != nil {
			if !gmxml.OperateTaskInfoM.Update(data.Task) {
				msg.Code = uint32(ret.RET_ERROR)
				l4g.Errorf("[LogicService] update operate task info error")
			}
		}

		if data.Gift != nil {
			if !gmxml.OperateGiftInfoM.Update(data.Gift) {
				msg.Code = uint32(ret.RET_ERROR)
				l4g.Errorf("[LogicService] update operate gift info error")
			}
		}

		if data.PromotionGifts != nil {
			if !gmxml.PromotionGiftInfoM.Update(data) {
				msg.Code = uint32(ret.RET_ERROR)
				l4g.Errorf("[LogicService] update promotion gift info error")
			}
		}

		flag, actIDs := gmxml.OperateActivityInfoM.Update(data.Base)
		if !flag {
			msg.Code = uint32(ret.RET_ERROR)
			l4g.Errorf("[LogicService] update operate activity info error")
		} else {
			smsg := &cl.L2C_OperateActivityCanXMLUpdate{
				Ret:    uint32(ret.RET_OK),
				ActIds: actIDs,
				Delete: data.Base.Deletes,
			}
			l.BroadcastCmdToClient(cl.ID_MSG_L2C_OperateActivityCanXMLUpdate, smsg)
		}
	}

	return msg, true
}

// 发布可配置页签
func (l *LogicService) onGRPCReleaseOperatePageInfo(data *gm.OperatePageInfos,
	_ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCReleaseOperatePageInfo: recv %s", data)
	msg := &gm.Result{Code: uint32(ret.RET_OK)}

	/*if data != nil {
		if len(data.Changes) > gmSliceMaxLength || len(data.Deletes) > gmSliceMaxLength {
			l4g.Errorf("[LogicService] onGRPCReleaseOperatePageInfo: datas length abnormal, changes: %d, deletes: %d",
				len(data.Changes), len(data.Deletes))
			msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
		} else if !gmxml.OperatePageInfoM.Update(data) {
			msg.Code = uint32(ret.RET_WRITE_FILE_FAIL)
			l4g.Errorf("[LogicService] update operate page info error")
		}
	}*/
	return msg, true
}

// 开启关闭协议
func (l *LogicService) onGRPCBanProtocol(data *gm.BanCmd, _ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCBanProtocol: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if len(data.AddCmds) > gmSliceMaxLength || len(data.DelCmds) > gmSliceMaxLength {
		l4g.Errorf("[LogicService] onGRPCBanProtocol: commands length abnormal, add: %d, delete: %d",
			len(data.AddCmds), len(data.DelCmds))
		msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
	} else {
		if l.ServerID() == data.ServerId {
			l.BanCmdM().Update(l, data)
		} else {
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		}
	}
	return msg, true
}

func (l *LogicService) onGRPCSetAllPushGift(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user != nil {
		user.PushGift().GmInitAllGift()
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)

	return msg, false
}

// 订单信息
func (l *LogicService) onGRPCGetRechargeList(data *gm.RechargeListReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Debug("onGRPCGetRechargeList. data:%+v", data)

	msg := &gm.RechargeListRsp{Code: uint32(ret.RET_OK)}
	if data.Uuid == "" || data.ServerId == 0 || len(data.TimeInterval) < 2 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	starTime, endTime := data.TimeInterval[0], data.TimeInterval[1]
	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user != nil {
		for _, order := range user.Orders().GetData() {
			if order.PayTime >= starTime && order.PayTime <= endTime {
				msg.Orders = append(msg.Orders, order)
			}
		}
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.getRechargeListFromDB(ids[0], starTime, endTime, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) getRechargeListFromDB(uid uint64, starTm, endTm int64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_GetRechargeList), 0, &r2l.L2R_GRPC_GetRechargeList{
		ID: uid,
		Callback: func(result proto.Message) {
			resultOnGRPCMessage(retCh, &gm.RechargeListRsp{
				Code:   uint32(ret.RET_OK),
				Orders: result.(*r2l.R2L_GetRechargeList).Orders,
			})
		},
		StartTime: starTm,
		EndTime:   endTm,
	})
}

// 设置新手引导状态
func (l *LogicService) onGRPCCloseGuidance(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if !goxml.GetData().GuidanceInfoM.IsValidGroup(data.Value) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCCloseGuidance. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.closeGuidance(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) closeGuidance(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_CloseGuidance), userID, &r2l.L2R_GRPC_CloseGuidance{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 设置迷宫任务等级
func (l *LogicService) onGRPCSetMazeTaskLevel(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetMazeTaskLevel. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setMazeTaskLevel(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setMazeTaskLevel(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetMazeTaskLevel), userID, &r2l.L2R_GRPC_SetMazeTaskLevel{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 获取玩家邮件列表
func (l *LogicService) onGRPCGetUserMailList(data *gm.UserMailListReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.UserMailListRsp{
		Result: &gm.Result{Code: uint32(ret.RET_OK)},
		Mails:  make(map[uint64]*cl.Mail),
	}
	if !l.checkUserIndex(data.User) {
		msg.Result.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.User.Type == uint32(gm.SEARCH_BY_ID) {
		user := l.UserM().GetUserWithCache(data.User.Ids[0])
		if user != nil {
			for id, mail := range user.MailBox().GetAll() {
				cloneMail := mail.Clone()
				if cloneMail.MultiLang {
					cloneMail.Params = l.MultiLangM().GetLangs(cloneMail.Id, user.GetLang())
				}
				msg.Mails[id] = cloneMail
			}
			return msg, true
		}
		l.getUserMailList(data.User.Ids[0], retCh, msg)
	} else if data.User.Type == uint32(gm.SEARCH_BY_UUID) {
		user := l.UserM().GetUnauthUser(data.User.Values[0], data.User.ServerId)
		if user != nil && user.MailBox() != nil {
			for id, mail := range user.MailBox().GetAll() {
				cloneMail := mail.Clone()
				if cloneMail.MultiLang {
					cloneMail.Params = l.MultiLangM().GetLangs(cloneMail.Id, user.GetLang())
				}
				msg.Mails[id] = cloneMail
			}
			return msg, true
		}
		l.getUserID(data.User.ServerId, data.User.Type, data.User.Values[:1], nil, func(values []string, ids []uint64) {
			if len(ids) == 0 || ids[0] == 0 {
				msg.Result.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
			} else {
				l.getUserMailList(ids[0], retCh, msg)
			}
		})
	} else if data.User.Type == uint32(gm.SEARCH_BY_NAME) {
		l.getUserID(data.User.ServerId, data.User.Type, data.User.Values[:1], nil, func(values []string, ids []uint64) {
			if len(ids) == 0 || ids[0] == 0 {
				msg.Result.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
			} else {
				user := l.UserM().GetUserWithCache(ids[0])
				if user != nil {
					for id, mail := range user.MailBox().GetAll() {
						cloneMail := mail.Clone()
						if cloneMail.MultiLang {
							cloneMail.Params = l.MultiLangM().GetLangs(cloneMail.Id, user.GetLang())
						}
						msg.Mails[id] = cloneMail
					}
					resultOnGRPCMessage(retCh, msg)
				} else {
					l.getUserMailList(ids[0], retCh, msg)
				}
			}
		})
	}
	return msg, false
}

func (l *LogicService) getUserMailList(userID uint64, retCh chan proto.Message, msg *gm.UserMailListRsp) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_GetUserMailList), userID, &r2l.L2R_GRPC_GetUserMailList{
		ID: userID,
		Callback: func(result uint32, mailFormation *r2l.MailListFormation) {
			msg.Result.Code = result
			if result == uint32(ret.RET_OK) {
				for _, mail := range mailFormation.Mails {
					if mail.MultiLang {
						mail.Params = l.MultiLangM().GetLangs(mail.Id, mailFormation.Lang)
					}
				}
			}
			msg.Mails = mailFormation.Mails
			resultOnGRPCMessage(retCh, msg)
		},
	})
}

// 玩家邮件删除
func (l *LogicService) onGRPCDeleteUserMail(data *gm.DeleteUserMailReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if !l.checkUserIndex(data.User) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if len(data.MailIds) == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.User.Type == uint32(gm.SEARCH_BY_ID) {
		user := l.UserM().GetUserWithCache(data.User.Ids[0])
		if user != nil {
			user.MailBox().Delete(data.MailIds)
			return msg, true
		} else {
			l.getUserID(data.User.ServerId, data.User.Type, nil, data.User.Ids[:1], func(values []string, ids []uint64) {
				if len(values) == 0 {
					msg.Code = uint32(ret.RET_USER_NOT_EXIST)
					resultOnGRPCMessage(retCh, msg)
				} else {
					l.deleteUserMail(ids[0], data.MailIds, retCh, msg)
				}
			})
		}
	} else if data.User.Type == uint32(gm.SEARCH_BY_UUID) {
		user := l.UserM().GetUnauthUser(data.User.Values[0], data.User.ServerId)
		if user != nil {
			if user.MailBox() != nil {
				//已经加载了
				user.MailBox().Delete(data.MailIds)
				return msg, true
			} else {
				//还在加载中
				msg.Code = uint32(ret.RET_QUEUING)
				return msg, true
			}
		} else {
			l.getUserID(data.User.ServerId, data.User.Type, data.User.Values[:1], nil, func(values []string, ids []uint64) {
				if len(ids) == 0 || ids[0] == 0 {
					msg.Code = uint32(ret.RET_USER_NOT_EXIST)
					resultOnGRPCMessage(retCh, msg)
				} else {
					l.deleteUserMail(ids[0], data.MailIds, retCh, msg)
				}
			})
		}
	} else if data.User.Type == uint32(gm.SEARCH_BY_NAME) {
		l.getUserID(data.User.ServerId, data.User.Type, data.User.Values[:1], nil, func(values []string, ids []uint64) {
			if len(ids) == 0 || ids[0] == 0 {
				msg.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
			} else {
				user := l.UserM().GetUserWithCache(ids[0])
				if user != nil {
					user.MailBox().Delete(data.MailIds)
					resultOnGRPCMessage(retCh, msg)
				} else {
					l.deleteUserMail(ids[0], data.MailIds, retCh, msg)
				}
			}
		})
	}
	return msg, false
}

func (l *LogicService) deleteUserMail(userID uint64, deletes []uint64, retCh chan proto.Message, msg *gm.Result) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_DeleteUserMail), userID, &r2l.L2R_GRPC_DeleteUserMail{
		ID:      userID,
		Deletes: deletes,
		Callback: func(ret uint32) {
			msg.Code = ret
			resultOnGRPCMessage(retCh, msg)
		},
	})
}

// 设置充值类型
func (l *LogicService) onGRPCSetAccountTag(data *gm.AccountTagReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.AccountTagRsp{
		Code: uint32(ret.RET_OK),
	}

	if data.Uid == 0 || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.Value == 0 && data.MuteValue > 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	user := l.UserM().GetUserWithCache(data.Uid)
	l4g.Debug("onGRPCSetRechargeType. data:%+v", data)
	if user != nil {
		user.SetAccountTag(data.Value)
		user.SetMute(data.MuteValue)
		user.LogSetAccountTag(l, data.Value, data.MuteValue)
		user.SendSelfToClient()
		msg.User = user.GetDBUser()
		return msg, true
	}

	l.setAccountTag(data.Uid, data.Value, data.MuteValue, retCh)

	return msg, false
}

func (l *LogicService) setAccountTag(userID uint64, value, muteValue uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetAccountTag), userID, &r2l.L2R_GRPC_SetAccountTag{
		ID:        userID,
		Value:     value,
		MuteValue: muteValue,
		Callback: func(code uint32, result *db.User) {
			resultOnGRPCMessage(retCh, &gm.AccountTagRsp{
				Code: code,
				User: result,
			})
		},
	})

	base.AsyncGetDBUser(l, userID, &AsyncAddAdapterLog{rType: value, muteValue: muteValue})
}

type AsyncAddAdapterLog struct {
	rType     uint32 // 充值类型
	muteValue uint32
}

func (a *AsyncAddAdapterLog) Resp(srv command.Servicer, args *character.Args, retCode uint32, data interface{}) bool {
	// args 为nil，因为在 AsyncGetDBUser 方法中没有赋值
	_ = args

	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("AsyncGetDBUser: get db.User data failed. retCode: %d", retCode)
		return false
	}

	users, exist := data.([]*db.User)
	if !exist {
		l4g.Errorf("AsyncGetDBUser: get db.User data failed, type trans failed.")
		return false
	}

	for _, u := range users {
		user := &character.User{}
		user.SetDBUser(u)
		user.LogSetAccountTag(srv, a.rType, a.muteValue)
		l4g.Debugf("user: %d AsyncAddAdapterLog: add about set account tag adapter log", user.ID())
	}

	return true
}

// 设置服务器时间
func (l *LogicService) onGRPCSetServerTime(data *gm.ServerTimeReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.ServerTime == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	//时间必须在2000-01-01到2500-01-01之间
	if data.ServerTime <= ********* || data.ServerTime >= *********** {
		l4g.Errorf("onGRPCSetServerTime newtime:%d  error ")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if time.Now().Unix() > int64(data.ServerTime) {
		l4g.Errorf("onGRPCSetServerTime newtime:%d now:%d error ", data.ServerTime, time.Now().Unix())
		msg.Code = uint32(ret.RET_GM_TIME_IS_LESS_THAN_LOGIC_NOW)
		return msg, true
	}

	guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("no found guildM")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("cross is not connected")
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	fn := func(cret uint32, cdata interface{}) bool {
		if cret == uint32(ret.RET_OK) {
			if time.Now().Unix() < int64(data.ServerTime) {
				//有可能跨服和游戏服是用的同一个配置文件
				hooktime.Set(time.Unix(int64(data.ServerTime), 0))
			}
			l4g.Info("logic set new time:%d", data.ServerTime)
		}
		resultOnGRPCMessage(retCh, &gm.Result{
			Code: cret,
		})
		return true
	}

	amsg := &GrpcAsyncMessage{
		srv: l,
		cb:  fn,
	}
	mid := l.CreateAsyncMessage(amsg, 5)
	cmsg := &l2c.L2C_SetTime{
		ClientMsgId: mid,
		Time:        data.ServerTime,
	}
	if !l.SendCmdToCross(l2c.ID_MSG_L2C_SetTime, 0, cmsg) {
		l4g.Errorf("SetServerTime SendCmdToCross fail")
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}
	return msg, false

}

// 查询服务器时间
func (l *LogicService) onGRPCQueryServerTime(data *gm.Cmd, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.ServerTimeRsp{
		Code: uint32(ret.RET_OK),
	}

	l4g.Debug("onGRPCQueryServerTime")

	guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("no found guildM")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("cross is not connected")
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	fn := func(cret uint32, cdata interface{}) bool {
		crossTime := uint64(0)
		if cret == uint32(ret.RET_OK) {
			crossData, ok := cdata.(*l2c.C2L_QueryTime)
			if !ok {
				cret = uint32(ret.RET_ERROR)
			} else {
				crossTime = crossData.Time
			}
		}
		resultOnGRPCMessage(retCh, &gm.ServerTimeRsp{
			Code:       cret,
			ServerTime: uint64(time.Now().Unix()),
			CrossTime:  crossTime,
		})
		return true
	}

	amsg := &GrpcAsyncMessage{
		srv: l,
		cb:  fn,
	}
	mid := l.CreateAsyncMessage(amsg, 5)
	cmsg := &l2c.L2C_QueryTime{
		ClientMsgId: mid,
	}
	if !l.SendCmdToCross(l2c.ID_MSG_L2C_QueryTime, 0, cmsg) {
		l4g.Errorf("SetServerTime SendCmdToCross fail")
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}
	return msg, false
}

func (l *LogicService) onGRPCSingleAllPushGift(data *gm.SinglePushGiftReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user != nil {
		if user.PushGift().GmInitSingleGift(data.GiftId, data.GroupId) {
			msg.Code = uint32(ret.RET_OK)
			return msg, true
		}
	}

	msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)

	return msg, true
}

func (l *LogicService) onGRPCImportUser(data *gm.ImportUserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.ImportUserRsp{
		Code: uint32(ret.RET_OK),
	}

	if data.ServerId != l.ServerID() {
		l4g.Errorf("serverID error. serverID:%d", data.ServerId)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	userData := data.Data
	if userData == nil {
		l4g.Errorf("userData is nil")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, false
	}

	if userData.User == nil {
		l4g.Errorf("userData.User is nil")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, false
	}

	userData.User.ServerId = uint64(data.ServerId)

	if userData.GuildUser != nil {
		userData.GuildUser.GuildId = 0
		userData.GuildUser.Grade = 0
	}

	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ImportUser), userData.User.Id, &r2l.L2R_GRPC_ImportUser{
		Data: userData,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.ImportUserRsp{
				Code: uint32(ret.RET_OK),
			})
		},
	})

	msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)

	return msg, false
}

func (l *LogicService) onGRPCSetDispatchLevel(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	Info := goxml.GetData().DispatchLevelInfoM.Index(data.Value)
	if Info == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetDispatchLevel. data:%+v", data)
	if user != nil {
		dispatch := user.Dispatch().GetDispatch()
		if dispatch == nil {
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			return msg, true
		}
		levelInfo := dispatch.GetLevelInfo()
		if levelInfo == nil {
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			return msg, true
		}
		levelInfo.DispatchLevel = data.Value
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setDispatchLevel(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setDispatchLevel(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetDispatchLevel), userID, &r2l.L2R_GRPC_SetDispatchLevel{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 设置gm config
func (l *LogicService) onGRPCUpdateDivineDemon(data *gm.DivineDemonReq) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCUpdateDivineDemon: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("[LogicService] onGRPCUpdateDivineDemon: data is nil. ")
		return msg, true
	}
	if retCode := l.updateDivineDemon(data); retCode != uint32(ret.RET_OK) {
		msg.Code = retCode
		l4g.Errorf("[LogicService] onGRPCUpdateDivineDemon: update divine demon summon failed. ret: %d", retCode)
		return msg, true
	}

	return msg, true
}

func (l *LogicService) updateDivineDemon(data *gm.DivineDemonReq) uint32 {
	switch gmxml.ReleaseStatus(data.OpStatus) {
	case gmxml.DivineDemonStatusOnline:
		now := time.Now().Unix()
		if data.EndTime <= data.StartTime || data.EndTime <= now || data.Id < goxml.DivineDemonUniqIDMax {
			l4g.Errorf("updateDivineDemon: divine demon time or id is invalid.")
			return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		}
		if code := gmxml.DivineDemonInfoM.Online(data); code != uint32(ret.RET_OK) {
			l4g.Errorf("updateDivineDemon: divine demon online failed. ret:%d", code)
			return code
		}
	case gmxml.DivineDemonStatusOffline:
		if code := gmxml.DivineDemonInfoM.Offline(data.Id); code != uint32(ret.RET_OK) {
			l4g.Errorf("updateDivineDemon: divine demon offline failed. ret:%d", code)
			return code
		}
	default:
		l4g.Errorf("updateDivineDemon: divine demon release status is invalid.")
		return uint32(ret.RET_DIVINE_DEMON_OP_STATUS_INVALID)
	}

	l.BroadcastCmdToClient(cl.ID_MSG_L2C_DivineDemonUpdate, &cl.L2C_DivineDemonUpdate{Id: data.Id})

	return uint32(ret.RET_OK)
}

func (l *LogicService) onGRPCSetGuildDungeonCurrentChapter(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	l4g.Debugf("onGRPCSetGuildDungeonCurrentChapter data:%+v", data)

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	maxChapter := goxml.GetData().GuildDungeonChapterInfoM.GetMaxChapter()
	if data.Value > maxChapter {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("uuid: %s SetGuildDungeonCurrentChapter failed, guildManager not exist", data.Uuid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if !guildM.IsCrossConnected() {
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetGuildDungeonCurrentChapter. data:%+v", data)
	if user != nil {
		guild := guildM.GetGuildByUser(user.ID())
		if guild == nil {
			msg.Code = uint32(ret.RET_NOT_IN_GUILD)
			return msg, true
		}

		l.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonSetChapterForGm, user.ID(), &l2c.L2C_GuildDungeonSetChapterForGm{
			Gid:     guild.ID(),
			Chapter: data.Value,
		})
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			userId := ids[0]
			guild := guildM.GetGuildByUser(userId)
			if guild == nil {
				msg.Code = uint32(ret.RET_NOT_IN_GUILD)
				resultOnGRPCMessage(retCh, msg)
				return
			}

			l.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonSetChapterForGm, userId, &l2c.L2C_GuildDungeonSetChapterForGm{
				Gid:     guild.ID(),
				Chapter: data.Value,
			})
			msg.Code = uint32(ret.RET_OK)
			resultOnGRPCMessage(retCh, msg)
		}
	})

	return msg, false
}

// 设置gm config
func (l *LogicService) onGRPCUpdateAnnouncement(data *gm.AnnouncementReq) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCUpdateAnnouncement: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("[LogicService] onGRPCUpdateAnnouncement: data is nil. ")
		return msg, true
	}

	if retCode := l.updateAnnouncement(data); retCode != uint32(ret.RET_OK) {
		msg.Code = retCode
		l4g.Errorf("[LogicService] onGRPCUpdateAnnouncement: update divinedemon activity failed. ret: %d", retCode)
		return msg, true
	}

	return msg, true
}

func (l *LogicService) updateAnnouncement(data *gm.AnnouncementReq) uint32 {
	switch data.OpType {
	case gmxml.AnnouncementOpTypeUpdate:
		if data.EndTime <= data.StartTime || data.EndTime <= time.Now().Unix() {
			l4g.Errorf("updateDebutActivity: announcement endTime is invalid.")
			return uint32(ret.RET_CLIENT_REQUEST_ERROR)
		}

		if !gmxml.AnnouncementInfoM.Update(data) {
			l4g.Errorf("updateDebutActivity: announcement update failed.")
			return uint32(ret.RET_SERVER_ERROR)
		}

	case gmxml.AnnouncementOpTypeDelete:
		if !gmxml.AnnouncementInfoM.Delete(data.Id) {
			l4g.Errorf("updateDebutActivity: announcement delete failed.")
			return uint32(ret.RET_SERVER_ERROR)
		}

	default:
		l4g.Errorf("updateDebutActivity: announcement opType is invalid. opType: %d", data.OpType)
		return uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}

	l.BroadcastCmdToClient(cl.ID_MSG_L2C_AnnouncementUpdate, &cl.L2C_AnnouncementUpdate{})

	return uint32(ret.RET_OK)
}

func (l *LogicService) onGRPCSetGuildLevel(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	l4g.Debugf("onGRPCSetGuildLevel data:%+v", data)

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	levelInfo := goxml.GetData().GuildLevelInfoM.Index(data.Value)
	if levelInfo == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	//newExp := levelInfo.Exp / 2

	guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("uuid: %s SetGuildLevel failed, guildManager not exist", data.Uuid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if !guildM.IsCrossConnected() {
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetGuildLevel. data:%+v", data)
	if user != nil {
		guild := guildM.GetGuildByUser(user.ID())
		if guild == nil {
			l4g.Errorf("uuid: %s SetGuildLevel failed, guild not exist", data.Uuid)
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			return msg, true
		}
		//guild.SetLevelAndExp(levelInfo.Level, newExp)

		if !guildM.IsCrossConnected() {
			msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
			return msg, true
		}

		l.SendCmdToCross(l2c.ID_MSG_L2C_GuildSetLevelForGM, user.ID(), &l2c.L2C_GuildSetLevelForGM{
			Gid:   guild.ID(),
			Level: levelInfo.Level,
		})

		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			guild := guildM.GetGuildByUser(ids[0])
			if guild == nil {
				l4g.Errorf("uuid: %s SetGuildLevel failed, guild not exist", data.Uuid)
				msg.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
				return
			} else {
				l.SendCmdToCross(l2c.ID_MSG_L2C_GuildSetLevelForGM, ids[0], &l2c.L2C_GuildSetLevelForGM{
					Gid:   guild.ID(),
					Level: levelInfo.Level,
				})
				msg.Code = uint32(ret.RET_OK)
				resultOnGRPCMessage(retCh, msg)
			}
		}
	})

	return msg, false
}

func (l *LogicService) onGRPCRecoveryGuildDungeonResetTime(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	l4g.Debugf("onGRPCSetGuildLevel data:%+v", data)

	if data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("uuid: %s SetGuildDivision failed, guildManager not exist", data.Uuid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if !guildM.IsCrossConnected() {
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetGuildDivision. data:%+v", data)
	//if user != nil {

	if user == nil {
		l4g.Errorf("onGRPCSetGuildDivision. data:%+v  user offline", data)
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		return msg, true
	}

	guild := guildM.GetGuildByUser(user.ID())
	if guild == nil {
		l4g.Errorf("uuid: %s SetGuildDivision failed, guild not exist", data.Uuid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	//guild.SetLevelAndExp(levelInfo.Level, newExp)

	if !guildM.IsCrossConnected() {
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	guildUser := guildM.GetGuildUser(user.ID())
	guildUser.AddWeeklyActivityPoint(data.Value)

	l.SendCmdToCross(l2c.ID_MSG_L2C_GuildSetLevelForGM, user.ID(), &l2c.L2C_GuildSetLevelForGM{
		Gid:   guild.ID(),
		Level: data.Value,
		Type:  1,
	})

	msg.Code = uint32(ret.RET_OK)
	return msg, true
}

// 重置密林等级
func (l *LogicService) onGRPCSetFlowerLevel(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetFlowerLevel. data:%+v", data)
	if user == nil {
		msg.Code = uint32(ret.RET_ERROR)
		return msg, true
	}

	flowerM, ok := l.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		msg.Code = uint32(ret.RET_ERROR)
		return msg, true
	}

	f := flowerM.Get(user.ID()) //nolint:varnamelen
	if f == nil {
		msg.Code = uint32(ret.RET_FLOWER_NOT_EXIST)
		return msg, true
	}

	if data.Value > goxml.GetData().FlowerLvInfoM.TopLv() {
		l4g.Errorf("setFlowerLevel. level must less than config topLv, userID:%d level:%d", user.ID(), data.Value)
		msg.Code = uint32(ret.RET_ERROR)
		return msg, true
	}

	f.SetLevel(data.Value)
	flowerM.SetChange(l, f)
	return msg, true
}

// 发布每日许愿
func (l *LogicService) onGRPCUpdateDailyWishActivity(data *gm.DailyWishInfo,
	_ chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCUpdateDailyWishActivity: recv %s", data)
	msg := &gm.Result{Code: uint32(ret.RET_OK)}
	startTime := l.StartTime()

	if data.Changes == nil && data.Deletes > 0 {
		msg.Code = gmxml.DailyWishActivityInfoM.Update(data, startTime)
		if msg.Code != uint32(ret.RET_OK) {
			l4g.Errorf("[LogicService] onGRPCUpdateDailyWishActivity: daily wish activity update failed code:%d",
				msg.Code)
			return msg, true
		}
	} else {
		msg.Code = gmxml.DailyWishAwardInfoM.Update(data)
		if msg.Code != uint32(ret.RET_OK) {
			l4g.Errorf("[LogicService] onGRPCUpdateDailyWishActivity: daily wish award update failed code:%d",
				msg.Code)
			return msg, true
		}
		msg.Code = gmxml.DailyWishActivityInfoM.Update(data, startTime)
		if msg.Code != uint32(ret.RET_OK) {
			l4g.Errorf("[LogicService] onGRPCUpdateDailyWishActivity: daily wish activity update failed code:%d",
				msg.Code)
			return msg, true
		}
	}

	smsg := &cl.L2C_DailyWishXmlUpdate{
		Ret:      uint32(ret.RET_OK),
		ChangeId: data.Changes.Id,
		Delete:   data.Deletes,
	}
	l.BroadcastCmdToClient(cl.ID_MSG_L2C_DailyWishXmlUpdate, smsg)

	return msg, true
}

// 更新神器首发活动
func (l *LogicService) onGRPCUpdateArtifactDebut(data *gm.ArtifactDebutReq) (proto.Message, bool) {
	l4g.Infof("grpc.onGRPCUpdateArtifactDebut: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateArtifactDebut: data is nil")
		return msg, true
	}

	if data.Id == 0 || data.ActivityId == 0 || data.StartTime == 0 ||
		data.EndTime == 0 || data.StartTime >= data.EndTime {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateArtifactDebut: data illegal")
		return msg, true
	}

	now := time.Now().Unix()
	if data.EndTime <= now {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateArtifactDebut: endTime < now")
		return msg, true
	}

	if data.Id < uint32(gm.ARTIFACT_DEBUT_AD_MIN_ID) {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateArtifactDebut: id illegal")
		return msg, true
	}

	info := goxml.GetData().ArtifactDebutActivityInfoM.Index(data.ActivityId)
	if info == nil || info.ActivityType != uint32(common.ARTIFACT_DEBUT_TYPE_ADT_COMMON) {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateArtifactDebut: activity illegal")
		return msg, true
	}

	if !gmxml.ArtifactDebutInfoM.SyncActivity(data, now) {
		msg.Code = uint32(ret.RET_GM_ACTIVITY_DATA_UPDATE_FAILED)
		l4g.Errorf("grpc.onGRPCUpdateArtifactDebut: update artifact debut failed")
		return msg, true
	}

	//如与新服活动时间有重叠，则需修正活动时间
	protectDay := goxml.GetData().ArtifactDebutConfigInfoM.GetProtectDay()
	fxied, startTm := l.FixActivityTime(now, data.StartTime, data.EndTime, protectDay)
	baseData := gmxml.ArtifactDebutInfoM.GetArtifactDebutBaseData(data.Id)
	if fxied && baseData != nil {
		baseData.StartTime = startTm
		baseData.EndTime = data.EndTime
		l.BroadcastCmdToClient(cl.ID_MSG_L2C_ArtifactDebutUpdateActivity,
			&cl.L2C_ArtifactDebutUpdateActivity{Data: baseData})
	}
	return msg, true
}

// 设置玩家百塔层数
func (l *LogicService) onGRPCSetTowerSeasonFloorID(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	now := time.Now()
	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user != nil {
		if data.Type == uint32(gm.SET_DUNGEON_TYPE_SDT_TOWER_SEASON) {
			towerSeason := user.TowerSeason()
			info := goxml.GetData().TowerSeasonDungeonInfoM.GetRecordById(data.Value)
			if info == nil {
				msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return msg, true
			}
			if info.Season != uint32(user.GetSeasonID()) {
				msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return msg, true
			}
			towerSeason.Reset(now.Unix(), info.Season, info.Round)
			towerSeason.SetFloor(data.Value, now.Unix())
			towerSeason.Save()
			smsg := &cl.L2C_TowerSeasonGetData{
				Ret: uint32(ret.RET_OK),
			}
			smsg.TowerSeason = user.TowerSeason().Flush()
			user.SendCmdToGateway(cl.ID_MSG_L2C_TowerSeasonGetData, smsg)
			msg.Code = uint32(ret.RET_OK)
		} else if data.Type == uint32(gm.SET_DUNGEON_TYPE_SDT_ACTIVITY_STORY) {
			activityStory := user.ActivityStory()
			info := goxml.GetData().ActivityStoryDungeonInfoM.Index(data.Value)
			storyInfo := goxml.GetData().ActivityStoryInfoM.GetCurrent(now.Unix())
			if info == nil || storyInfo == nil || info.DungeonId != storyInfo.DungeonID {
				msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return msg, true
			}
			activityStory.SetDungeon(data.Value)
			activityStory.Save()
			smsg := &cl.L2C_ActivityStoryGetData{
				Ret: uint32(ret.RET_OK),
			}
			smsg.Data = user.ActivityStory().Flush()
			user.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryGetData, smsg)
		} else if data.Type == uint32(gm.SET_DUNGEON_TYPE_SDT_SEASON_DUNGEON) {
			seasonDungeon := user.SeasonDungeon()
			info := goxml.GetData().SeasonDungeonInfoM.Index(data.Value)
			if info == nil || info.SeasonId != user.GetSeasonID() {
				msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				return msg, true
			}
			seasonDungeon.SetDungeon(data.Value)
			seasonDungeon.Save()
			smsg := &cl.L2C_SeasonDungeonGetData{
				Ret: uint32(ret.RET_OK),
			}
			smsg.Data = user.SeasonDungeon().Flush()
			user.SendCmdToGateway(cl.ID_MSG_L2C_SeasonDungeonGetData, smsg)
		} else {
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		}
	} else {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
	}
	return msg, true
}

// 根据新服保护期，修正通服活动时间
// @param int64 now 当前时间
// @param int64 startTm 活动开始时间
// @param int64 endTm 活动结束时间
// @param uint32 protectDay 活动的新服保护时间
// @return bool 活动时间是否有效
// @return int64 修正后的活动开始时间
// Tips: 调用时需注意: 修正后的数据，会出现开始时间大于结束时间的情况
func (l *LogicService) FixActivityTime(now, startTm, endTm int64, protectDay uint32) (bool, int64) {
	openDay := l.ServerDay(now)
	if openDay <= protectDay {
		diff := protectDay - openDay
		endSec := int64(util.DailyZeroByTime(now) + diff*util.DaySecs + util.DaySecs - 1) //新服活动结束时间 yyyy-mm-dd 23:59:59
		if endTm <= endSec {
			l4g.Debugf("grpc.FixActivityTime: activityEndTm less than newServerActivityEndTm")
			return false, startTm
		}

		if startTm <= endSec {
			startTm = endSec + 1
			l4g.Debugf("grpc.FixActivityTime: startTm:%d endTm:%d", startTm, endTm)

			if startTm > endTm {
				l4g.Errorf("grpc.FixActivityTime: startTm greater than endTm, startTm:%d endTm:%d",
					startTm, endTm)
				return false, startTm
			}
		}
	}
	return true, startTm
}

//nolint:exhaustive
func OnMongoMessage(ctx context.Context, msg *mongo.MongoGRPCMessage) {
	srv := appsrv.GetService(ctx).(*LogicService)
	if msg == nil || msg.Data == nil {
		l4g.Errorf("grpc [LogicService] onMongoMessage: msg is nil. msg:%+v", msg)
		return
	}

	l4g.Debugf("grpc [LogicService] onMongoMessage: id: %d", msg.ID)

	switch msg.ID {
	case l2m.ID_Fn_GetCognitionLog:
		srv.onGRPCGetCognitionLogs(msg)
	case l2m.ID_Fn_GetGstLog:
		srv.onGRPCGetGstLog(msg)
	default:
		l4g.Errorf("grpc [LogicService] onMongoMessage: id not exist. id:%d", msg.ID)
	}
}

func (l *LogicService) onGRPCGetCognitionLogs(msg *mongo.MongoGRPCMessage) {
	l4g.Debugf("grpc [LogicService] onGRPCGetCognitionLogs: recv %s", msg)

	rsp, ok := msg.Data.(*l2m.GetCognitionLogRsp)
	if !ok || msg == nil {
		l4g.Errorf("onMongoMessage data trans error: recv %s", msg)
		return
	}

	user := l.UserM().GetUserWithCache(rsp.Uid)
	if user != nil {
		returnMsg := &cl.L2C_GetCognitionLog{
			Ret:         uint32(ret.RET_OK),
			Logs:        rsp.Logs,
			FirstPass:   rsp.FirstPass,
			LessPower:   rsp.LessPower,
			Total:       rsp.Total,
			FormationId: rsp.FormationId,
			TargetId:    rsp.TargetId,
			Page:        rsp.Page,
		}
		//if user.GuildID() != 0 {
		// todo
		//guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
		//if ok {
		//	guild := guildM.GetGuildByUser(user.ID(), l)
		//	if guild != nil {
		//		returnMsg.GuildMembers = guild.GetMembers()
		//	}
		//}
		//}
		friendM, ok := l.GetActivity(activity.Friend).(*afriend.Manager)
		if ok {
			friendU := friendM.Get(l, rsp.Uid, false)
			if friendU != nil {
				_, returnMsg.FriendIds = friendU.Flush()
			}
		}

		if rsp.Type == l2m.CognitionType_CT_TowerSeason {
			towerSeasonMsg := &cl.L2C_TowerSeasonCognitionLogs{
				Ret:   uint32(ret.RET_OK),
				Logs:  rsp.Logs,
				Total: rsp.Total,
				Page:  rsp.Page,
			}
			user.SendCmdToGateway(cl.ID_MSG_L2C_TowerSeasonCognitionLogs, towerSeasonMsg)
		} else {
			user.SendCmdToGateway(cl.ID_MSG_L2C_GetCognitionLog, returnMsg)
		}
	}
}

// 删除聊天groupTag
func (l *LogicService) onGRPCDelChatGroupTag(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCDelChatGroupTag. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.delChatGroupTag(ids[0], retCh)
		}
	})

	return msg, false
}

func (l *LogicService) delChatGroupTag(userID uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_DelChatGroupTag), userID, &r2l.L2R_GRPC_DelChatGroupTag{
		ID: userID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

// 更新掉落活动
//
//nolint:funlen
func (l *LogicService) onGRPCUpdateDropActivity(data *cl.DropActivityBase) (proto.Message, bool) {
	l4g.Infof("grpc.onGRPCUpdateDropActivity: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateDropActivity: data is nil")
		return msg, true
	}

	if data.Id == 0 || data.ActivityId == 0 || data.StartTime == 0 ||
		data.EndTime == 0 || data.StartTime >= data.EndTime ||
		(data.OpStatus != gmxml.OpStatusRelease && data.OpStatus != gmxml.OpStatusOff) ||
		len(data.Rules) == 0 {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateDropActivity: data illegal, data:%+v", data)
		return msg, true
	}

	now := time.Now().Unix()
	if data.EndTime <= now {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateDropActivity: endTime < now, id:%d", data.Id)
		return msg, true
	}

	infos := goxml.GetData().DropActivityTypeInfoM.Index(data.ActivityId)
	if len(infos) == 0 {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateDropActivity: no activity cfg, id:%d, ActivityId:%d",
			data.Id, data.ActivityId)
		return msg, true
	}

	//检查兑换规则
	idRepeated := make(map[uint32]struct{})
	for _, rule := range data.Rules {
		if _, exist := idRepeated[rule.Id]; exist {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateDropActivity: rule id repeated, id:%d ruleID:%d", data.Id, rule.Id)
			return msg, true
		}
		idRepeated[rule.Id] = struct{}{}

		if rule.Tag != 1 && rule.Tag != 2 {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateDropActivity: rule tag illegal, id:%d tag:%d", data.Id, rule.Tag)
			return msg, true
		}

		if rule.Max == 0 {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateDropActivity: rule no max limit, id:%d ruleID:%d", data.Id, rule.Id)
			return msg, true
		}

		if len(rule.Target) == 0 {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateDropActivity: rule no target, id:%d ruleID:%d", data.Id, rule.Id)
			return msg, true
		}

		if len(rule.Condition) == 0 {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateDropActivity: rule no condition, id:%d ruleID:%d", data.Id, rule.Id)
			return msg, true
		}

		//验证待兑换的奖励数量
		for _, v := range rule.Target {
			if v.Count == 0 {
				msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
				l4g.Errorf("grpc.onGRPCUpdateDropActivity: target count is 0, id:%d ruleID:%d", data.Id, rule.Id)
				return msg, true
			}
		}

		if rule.OpenBuyTime > data.EndTime {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateDropActivity: rule id :%d open_buy_time:%d, is large than end time:%d", data.Id, rule.OpenBuyTime, data.EndTime)
			return msg, true
		}

		//验证消耗道具是否与量表吻合
		for _, v := range rule.Condition { //nolint:varnamelen
			if v.Count == 0 {
				msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
				l4g.Errorf("grpc.onGRPCUpdateDropActivity: condition count is 0, id:%d ruleID:%d", data.Id, rule.Id)
				return msg, true
			}

			//除量表配置的道具外，额外可消耗金币和钻石
			if v.Type == uint32(common.RESOURCE_GOLD) || v.Type == uint32(common.RESOURCE_DIAMOND) {
				continue
			}

			var exist bool
			for _, info := range infos {
				if v.Type == info.Type && v.Value == info.Value {
					exist = true
					break
				}
			}

			if !exist {
				msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
				l4g.Errorf("grpc.onGRPCUpdateDropActivity: rule condition item illegal, id:%d ruleID:%d",
					data.Id, rule.Id)
				return msg, true
			}
		}
	}

	if !gmxml.DropActivityInfoM.SyncActivity(data, now) {
		msg.Code = uint32(ret.RET_GM_ACTIVITY_DATA_UPDATE_FAILED)
		l4g.Errorf("grpc.onGRPCUpdateDropActivity: update drop activity failed, id:%d", data.Id)
		return msg, true
	}

	//如与新服活动时间有重叠，则需修正活动时间
	fxied, startTm := l.FixActivityTime(now, data.StartTime, data.EndTime, data.ProtectDay)
	baseData := gmxml.DropActivityInfoM.GetActivityByUniqID(data.Id)
	if fxied && baseData != nil {
		baseData.StartTime = startTm
		baseData.EndTime = data.EndTime
		l.BroadcastCmdToClient(cl.ID_MSG_L2C_DropActivityUpdateActivity,
			&cl.L2C_DropActivityUpdateActivity{Data: baseData})
	}

	return msg, true
}

// 删除玩家英雄
func (l *LogicService) onGRPCDeleteHero(data *gm.DeleteHeroReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if !l.checkUserIndex(data.User) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.Hid == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.User.Type != uint32(gm.SEARCH_BY_ID) {
		l4g.Errorf("grpc.onGRPCDeleteHero: type err")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	uid := data.User.Ids[0]
	user := l.UserM().GetUserWithCache(uid)
	if user == nil {
		l4g.Errorf("grpc.onGRPCDeleteHero: user not online")
		msg.Code = uint32(ret.RET_ERROR)
		return msg, true
	}

	heroM := user.HeroManager()
	hero := heroM.Get(data.Hid)
	if hero == nil {
		l4g.Errorf("grpc.onGRPCDeleteHero: hero not exist, uid:%d, hid:%d", uid, data.Hid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	delHeroResource := &cl.Resource{
		Id:    data.Hid,
		Type:  uint32(common.RESOURCE_HERO),
		Value: hero.GetHeroSysID(),
		Count: 1,
	}

	if heroM.GetCommonCount() <= 1 {
		l4g.Errorf("grpc.onGRPCDeleteHero: hero count err, uid:%d, hid:%d", uid, data.Hid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if len(hero.GetAllEquipment()) > 0 {
		l4g.Errorf("grpc.onGRPCDeleteHero: equip not empty, uid:%d, hid:%d", uid, data.Hid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if len(hero.GetAllEmblem()) > 0 {
		l4g.Errorf("grpc.onGRPCDeleteHero: emblem not empty, uid:%d, hid:%d", uid, data.Hid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	for _, fid := range goxml.GetData().FormationInfoM.GetAllFids() {
		if user.CheckHeroInFormation(data.Hid, fid) {
			l4g.Errorf("grpc.onGRPCDeleteHero: hero in formation, uid:%d, hid:%d, fid:%d",
				uid, data.Hid, fid)
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			return msg, true
		}
	}

	// if hero.IsInBattleList() {
	// 	l4g.Errorf("grpc.onGRPCDeleteHero: hero in crystal, uid:%d, hid:%d", uid, data.Hid)
	// 	msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
	// 	return msg, true
	// }
	sysID := hero.GetHeroSysID()

	heroM.Delete(data.Hid, l)
	heroStarUpCostsM := user.HeroStarUpCostsManager()
	heroStarUpCostsM.Delete([]uint64{data.Hid})

	user.UpdateAllPower(l, character.PowerUpdateByNormalRaise)

	pushMsg := &cl.L2C_OpResources{
		Reduce: []*cl.Resource{{
			Id:    data.Hid,
			Type:  uint32(common.RESOURCE_HERO),
			Value: sysID,
			Count: 1,
		}},
	}
	character.NewMaybeOfflineUserLog(l, user.ID()).LogDeleteUserResources(l, []*cl.Resource{delHeroResource}) // 日志记录
	user.SendCmdToGateway(cl.ID_MSG_L2C_OpResources, pushMsg)
	return msg, true
}

func (l *LogicService) onGRPCSetDailyAttendance(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetDailyAttendance. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setDailyAttendance(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setDailyAttendance(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetDailyAttendance), userID, &r2l.L2R_GRPC_SetDailyAttendance{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCSetUserWrestleLevel(data *gm.UserSetParam, retCh chan proto.Message) (proto.Message, bool) {

	l4g.Debugf("onGRPCSetUserWrestleLevel: recv data:%v", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if len(data.User.Values) == 0 || data.User.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.Param < uint32(common.WRESTLE_LEVEL_WL_FIRST) || data.Param >= uint32(common.WRESTLE_LEVEL_WL_MAX) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	wrestleM, ok := l.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("SetUserWrestleLevel failed, wrestleM not exist")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if !wrestleM.IsCrossConnected() {
		l4g.Errorf("SetUserWrestleLevel failed, cross not connect")
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	l.getUserID(data.User.ServerId, uint32(gm.SEARCH_BY_UUID), data.User.Values, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setWrestleLevel(ids, data.Param, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setWrestleLevel(ids []uint64, level uint32, retCh chan proto.Message) {

	wrestleM, ok := l.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("SetUserWrestleLevel failed, wrestleM not exist")
		resultOnGRPCMessage(retCh, &gm.Result{
			Code: uint32(ret.RET_CLIENT_REQUEST_ERROR),
		})
		return
	}

	if !wrestleM.IsCrossConnected() {
		l4g.Errorf("SetUserWrestleLevel failed, cross not connect")
		resultOnGRPCMessage(retCh, &gm.Result{
			Code: uint32(ret.RET_CLIENT_REQUEST_ERROR),
		})
		return
	}

	req := &l2c.L2C_WrestleSetFighterLevelForGm{
		Uids:  ids,
		Level: level,
	}

	l.SendCmdToCross(l2c.ID_MSG_L2C_WrestleSetFighterLevelForGm, l.ServerID(), req)
	resultOnGRPCMessage(retCh, &gm.Result{
		Code: uint32(ret.RET_OK),
	})
}

func (l *LogicService) onGRPCSetRiteRare(data *gm.UserSetRiteRare, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Debugf("onGRPCSetRiteRare: recv data:%v", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc.onGRPCSetRiteRare: invalid uuid or serverid, uuid:%s, serverId:%d", data.Uuid, data.ServerId)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetRiteRare. data:%+v", data)
	if user != nil {
		r := user.RiteManager().GmSetRare(l, data.RiteId, data.Rare)
		if r != ret.RET_OK {
			l4g.Errorf("grpc.onGRPCSetRiteRare: set rare failed, uid:%d, riteId:%d rate:%d", user.ID(), data.RiteId, data.Rare)
		} else {
			l4g.Infof("grpc.onGRPCSetRiteRare: set rare success. uid:%d, riteId:%d rate:%d", user.ID(), data.RiteId, data.Rare)
		}
		msg.Code = uint32(r)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
			l4g.Errorf("grpc.onGRPCSetRiteRare: user not exist, uuid:%s, riteId:%d rate:%d", data.Uuid, data.RiteId, data.Rare)
		} else {
			l.setRiteRare(ids[0], data.RiteId, data.Rare, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setRiteRare(userID uint64, riteId, rare uint32, retCh chan proto.Message) {
	riteInfo := goxml.GetData().RiteInfoM.Index(riteId)
	if riteInfo == nil {
		l4g.Errorf("setRiteRare: rite not found. uid:%d riteId:%d", userID, riteId)
		resultOnGRPCMessage(retCh, &gm.Result{Code: uint32(ret.RET_RITE_NOT_FOUND)})
		return
	}
	if riteInfo.IsForMonster != 0 {
		l4g.Errorf("setRiteRare: rite is not for user. uid:%d riteId:%d", userID, riteId)
		resultOnGRPCMessage(retCh, &gm.Result{Code: uint32(ret.RET_RITE_NOT_FOR_USER)})
		return
	}

	grids := make([]*cl.RiteGrid, 0, 5)     //nolint:mnd
	for pos := uint32(1); pos <= 5; pos++ { //nolint:mnd
		markInfo := goxml.GetData().RiteMarkInfoM.GetByRiteIdPositionRare(riteId, pos, rare)
		if markInfo == nil {
			l4g.Errorf("setRiteRare: rite position rare not found. uid:%d riteId:%d", userID, riteId)
			resultOnGRPCMessage(retCh, &gm.Result{Code: uint32(ret.RET_RITE_NOT_FOUND)})
			return
		}
		grids = append(grids, &cl.RiteGrid{
			Pos:      pos,
			MarkGuid: l.CreateUniqueID(),
			MarkId:   markInfo.Id,
		})
	}

	l4g.Infof("grpc.setRiteRare: send cmd to db. uid:%d, riteId:%d rate:%d", userID, riteId, rare)
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetRiteRare), userID, &r2l.L2R_GRPC_SetRiteRare{
		ID:     userID,
		RiteID: riteId,
		Rare:   rare,
		Grids:  grids,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
			l4g.Infof("grpc.setRiteRare: success. uid:%d, riteId:%d rate:%d", userID, riteId, rare)
		},
	})
}

// 修改玩家赛季等级
func (l *LogicService) onGRPCSetUserSeasonLevel(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetUserSeasonLevel. data:%+v", data)
	if user == nil {
		l4g.Errorf("onGRPCSetUserSeasonLevel: user not online. uuid:%s", data.Uuid)
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		return msg, false
	}

	if user.GetSeasonID() != goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix()) {
		l4g.Errorf("onGRPCSetUserSeasonLevel: user not in current season. uid:%d seasonID:%d", user.ID(), user.GetSeasonID())
		msg.Code = uint32(ret.RET_ERROR)
		return msg, false
	}

	if goxml.GetData().SeasonLevelInfoM.GetLevelInfo(user.GetSeasonID(), data.Value) == nil {
		l4g.Errorf("onGRPCSetUserSeasonLevel: level error. uid:%d seasonID:%d level:%d",
			user.ID(), user.GetSeasonID(), data.Value)
		msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
		return msg, false
	}

	user.SetSeasonLevel(data.Value)
	msg.Code = uint32(ret.RET_OK)
	return msg, true
}

// 订单退款
func (l *LogicService) onGRPCNotifyRefund(data *db.Order, retCh chan proto.Message) (
	proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCNotifyRefund: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if data == nil {
		l4g.Errorf("[LogicService] onGRPCNotifyRefund: data nil")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if l.ServerID() != data.ServerId {
		l4g.Errorf("[LogicService] onGRPCNotifyRefund: server id error, order:%+v", data)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.Amount == 0 {
		l4g.Errorf("[LogicService] onGRPCNotifyRefund: amount error, order:%+v", data)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if !base.IsValidUserID(l, data.UserId) {
		l4g.Errorf("[LogicService] onGRPCNotifyRefund: user id error, order:%+v", data)
		msg.Code = uint32(ret.RET_USER_NOT_EXIST)
		return msg, true
	}
	data.Custom = &cl.OrderCustomData{}
	if err := json.Unmarshal([]byte(data.CustomData), data.Custom); err != nil {
		l4g.Errorf("[LogicService] onGRPCNotifyRefund: custom unmarshal error:%v, order:%+v", err, data)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	//db会自动把订单加入到redis的waitRefundOrder集合中
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_AddWaitRefundOrder), 0, &r2l.L2R_GRPC_AddWaitRefundOrder{
		Order: data,
		Callback: func(retCode uint32, order *db.Order) {
			l4g.Infof("[LogicService] onGRPCNotifyRefund: add order callback:%d %+v", retCode, order)
			msg.Code = retCode
			if retCode == uint32(ret.RET_OK) {
				//在线处理订单
				user := l.UserM().GetUserWithCache(order.UserId)
				if user != nil {
					//如果是缓存User，也需要进行添加，等上线再进行发货
					user.Orders().AddRefundOrder(order)
					if user.IsOnline() {
						user.SetContextID(l.CreateLogID()) //为了记录日志用
						successOrders := user.Orders().Refund(l, []*db.Order{order})
						l4g.Infof("[LogicService] onGRPCNotifyRefund: online order:%+v", order)
						if _, exist := successOrders[order.OrderId]; !exist {
							msg.Code = uint32(ret.RET_REFUND_ORDER_PROCESS_FAIL)
						}
					} else {
						l4g.Infof("[LogicService] onGRPCNotifyRefund: cache order:%d %+v", retCode, order)
					}
				} else {
					l4g.Infof("[LogicService] onGRPCNotifyRefund: offline order:%d %+v", retCode, order)
				}
			}
			resultOnGRPCMessage(retCh, msg)
		},
	})
	return msg, false
}

// 删除高级货币（钻石、金币、代金券）
func (l *LogicService) onGRPCDeleteCurrencies(data *gm.DeleteCurrenciesReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCDeleteCurrencies: recv %+v", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if len(data.DelCurrencies) == 0 || len(data.DelCurrencies) > 3 {
		l4g.Errorf("[LogicService] onGRPCDeleteCurrencies: checkParam error, recv:%+v", data)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if !l.checkUserIndex(data.User) {
		l4g.Errorf("[LogicService] onGRPCDeleteCurrencies: check user index err, type:%d, ids len:%d, values len: %d",
			data.User.Type, len(data.User.Ids), len(data.User.Values))
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if data.User.Type == uint32(gm.SEARCH_BY_NAME) || data.User.Type == uint32(gm.SEARCH_BY_UUID) {
		l.getUserID(data.User.ServerId, data.User.Type, data.User.Values[:1], nil, func(values []string, ids []uint64) {
			if len(ids) == 0 || ids[0] == 0 {
				msg.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
			} else {
				l.changeUserCurrencies(ids[0], data.DelCurrencies, retCh, msg)
			}
		})
	} else {
		l.changeUserCurrencies(data.User.Ids[0], data.DelCurrencies, retCh, msg)
	}
	return msg, false
}

func (l *LogicService) changeUserCurrencies(userID uint64, delCurrencies []*cl.Resource, retCh chan proto.Message, msg *gm.Result) {
	if !l.BanAccountM().Check(userID, uint32(common.BAN_TYPE_LOGIN)) &&
		!l.BanAccountM().Check(userID, uint32(common.BAN_TYPE_LOGIN_TEMPORARY)) {
		msg.Code = uint32(ret.RET_BAN_USER_BEFORE_OP)
		resultOnGRPCMessage(retCh, msg)
		return
	}
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_DeleteCurrencies), userID, &r2l.L2R_GRPC_DeleteCurrencies{
		ID:            userID,
		DelCurrencies: delCurrencies,
		Callback: func(retCode uint32) {
			if retCode != uint32(ret.RET_OK) {
				msg.Code = retCode
			} else {
				character.NewMaybeOfflineUserLog(l, userID).LogDeleteUserResources(l, delCurrencies) // 日志记录
			}
			resultOnGRPCMessage(retCh, msg)
		},
	})
}

func (l *LogicService) onGRPCSetHeroAwakenLevel(data *gm.UserSetHeroAwakenLevel, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Debugf("onGRPCSetHeroAwakenLevel: recv data:%v", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc.onGRPCSetHeroAwakenLevel: invalid uuid or serverid, uuid:%s, serverId:%d", data.Uuid, data.ServerId)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetHeroAwakenLevel. data:%+v", data)
	if user == nil {
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		l4g.Errorf("grpc.onGRPCSetHeroAwakenLevel: user not online, uuid:%s, heroSysId:%d awakenLevel:%d", data.Uuid, data.HeroSysId, data.AwakenLevel)
		return msg, true
	}
	r := user.HeroManager().GmSetAwakenLevel(l, data.HeroSysId, data.AwakenLevel)
	if r != ret.RET_OK {
		l4g.Errorf("grpc.onGRPCSetHeroAwakenLevel: set awaken level failed, uid:%d, heroSysId:%d awakenLevel:%d", user.ID(), data.HeroSysId, data.AwakenLevel)
	} else {
		l4g.Infof("grpc.onGRPCSetHeroAwakenLevel: set awaken level success. uid:%d, heroSysId:%d awakenLevel:%d", user.ID(), data.HeroSysId, data.AwakenLevel)
	}
	msg.Code = uint32(r)
	return msg, true
}

func (l *LogicService) onGRPCSetDisorderLand(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetUserLevel. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setDisorderLand(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setDisorderLand(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetDisorderLand), userID, &r2l.L2R_GRPC_SetDisorderLand{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCDivineDemonBench(data *gm.DivineDemonBenchReq, retCh chan proto.Message) (proto.Message, bool) {
	/*wishID := data.WishHeroId
	loopNum := data.LoopNum
	activityID := data.ActivityId
	activityType := data.ActivityType
	activityTypePool := map[uint32]string{
		1: "新服",
		2: "通服",
	}

	msg := &gm.DivineDemonBenchRsp{
		Code: uint32(ret.RET_OK),
	}

	d := &DivineDemonBench{
		loopNum:        loopNum,
		activityID:     activityID,
		activityType:   activityType,
		wishID:         wishID,
		record:         &Record{},
		groupCountPool: make(map[uint32]uint32),
	}

	// sc 的几种状态
	sc0 := 0
	sc1 := 0
	sc2 := 0
	sc3 := 0

	for i := uint32(0); i < d.loopNum; i++ {
		awards, _ := d.RetSummonAward(l.rd)
		var redHeroCount uint32
		for _, award := range awards {
			if award.Type != 6 {
				continue
			}
			hero := goxml.GetData().HeroInfoM.Index(award.Value)
			if hero == nil {
				l4g.Errorf("hero not exist. heroID: %d", award.Value)
				continue
			}
			if hero.Rare >= 60 {
				redHeroCount++
				if d.wishID == award.Value {
					d.wishHeroCount++
				}

				switch d.sc {
				case 0:
					sc0 += 1
				case 1:
					sc1 += 1
				case 2:
					sc2 += 1
				case 3:
					sc3 += 1
				}
			}
		}

		// 更新sc
		nextSc := goxml.GetData().DivineDemonSummonScInfoM.RandSCInfo(l.rd, d.sc).NextScType
		d.UpdateSummon(redHeroCount, nextSc)
	}

	msg.Rets = append(msg.Rets, fmt.Sprintf("活动 id: %d, 活动类型：%s, 卡池 id: %d \n", d.activityID, activityTypePool[d.activityType], d.wishID))
	msg.Rets = append(msg.Rets, fmt.Sprintf("red_hero count: %d", d.redHeroCount))
	msg.Rets = append(msg.Rets, fmt.Sprintf("wish hero id: %d count: %d", d.wishID, d.wishHeroCount))
	msg.Rets = append(msg.Rets, fmt.Sprintf("grade = 1|2, new hero count: %d", d.newHeroCount))
	msg.Rets = append(msg.Rets, fmt.Sprintf("grade = 1|2, old hero count: %d", d.oldHeroCount))
	msg.Rets = append(msg.Rets, fmt.Sprintf("new/old hero pct: %.2f", float64(d.newHeroCount)/float64(d.oldHeroCount)))
	for i, v := range d.groupCountPool {
		msg.Rets = append(msg.Rets, fmt.Sprintf("wishListPorb group出现的次数 group：%d 对应的数量: %d", i, v))
	}

	return msg, true*/
	return nil, false
}

// 设置赛季羁绊丰碑数据
func (l *LogicService) onGRPCSetSeasonLinkMonuments(data *gm.SetSeasonLinkMonumentsReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Debugf("onGRPCSetSeasonLinkMonuments: recv data:%v", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("onGRPCSetSeasonLinkMonuments: invalid uuid or serverid, uuid:%s, serverId:%d", data.Uuid, data.ServerId)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user == nil {
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		l4g.Errorf("onGRPCSetSeasonLinkMonuments: user not online, uuid:%s ", data.Uuid)
		return msg, true
	}
	r := user.SeasonLink().GmSetSeasonLink(data.Runes, l)
	if r != ret.RET_OK {
		l4g.Errorf("grpc.onGRPCSetSeasonLinkMonuments: set monuments failed, uid:%d  data %+v", user.ID(), data.Runes)
	} else {
		l4g.Infof("grpc.onGRPCSetSeasonLinkMonuments: set monuments success. uid:%d  data %+v", user.ID(), data.Runes)
	}
	msg.Code = uint32(r)
	return msg, true
}

// 同步人群包
func (l *LogicService) onGRPCUpdatePeopleGroupPackage(data *gm.UpdatePeopleGroupPackageReq, _ chan proto.Message) (proto.Message, bool) {
	l4g.Debugf("onGRPCUpdatePeopleGroupPackage: recv data:%v", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	peopleGroupPackageM, ok := l.GetActivity(activity.PeopleGroupPackage).(*peoplegrouppackage.Manager)
	if !ok {
		l4g.Errorf("updatePeopleGroupPackage failed, peopleGroupPackageM not exist")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.PackageId == 0 {
		l4g.Errorf("updatePeopleGroupPackage failed, packageId is 0")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.Offline {
		peopleGroupPackageM.SetDelete(data.PackageId)
	} else {
		peopleGroupPackageM.UpdateById(data.PackageId, data.Users)
	}
	msg.Code = uint32(ret.RET_OK)
	return msg, true
}

// 全服邮件
func (l *LogicService) onGRPCSendPGPMail(data *gm.PGPMail, _ chan proto.Message) (*gm.Result, bool) {
	l4g.Infof("grpc [LogicService] onGRPCSendPGPMail: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if len(data.Awards) > gmSliceMaxLength {
		l4g.Errorf("[LogicService] onGRPCSendPGPMail: awards length abnormal, resources: %d", len(data.Awards))
		msg.Code = uint32(ret.RET_PARAM_LENGTH_LIMIT)
		return msg, true
	}

	peopleGroupPackageM, ok := l.GetActivity(activity.PeopleGroupPackage).(*peoplegrouppackage.Manager)
	if !ok {
		l4g.Errorf("onGRPCSendPGPMail failed, peopleGroupPackageM not exist")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	pgp := peopleGroupPackageM.Get(data.PgpId)
	if pgp == nil {
		l4g.Errorf("onGRPCSendPGPMail failed, peopleGroupPackageM not exist")
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	usersMail := &gm.UsersMail{
		User: &gm.UserIndex{
			Type: uint32(gm.SEARCH_BY_ID),
			Ids:  pgp.Uids,
		},
		Ress: data.Awards,
	}

	character.GMMail(l, data.Content, pgp.Uids, usersMail, msg)
	return msg, true
}

func (l *LogicService) onGRPCGetGstLog(msg *mongo.MongoGRPCMessage) {
	l4g.Debugf("grpc [LogicService] onGRPCGetGstLog: recv %s", msg)
	rsp, ok := msg.Data.(*l2m.GSTGetLogRsp)
	if !ok || msg == nil {
		l4g.Errorf("onMongoMessage data trans error: recv %s", msg)
		return
	}

	user := l.UserM().GetUserWithCache(rsp.ReqUid)
	if user != nil {
		user.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetLogData, rsp.Rsp)
	}
}

func (l *LogicService) onGRPCKickAccount(data *gm.UserIndex, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCKickAccount: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if data == nil || !l.checkUserIndex(data) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if data.Type == uint32(gm.SEARCH_BY_ID) {
		l.UserM().KickAccount(data.Ids[0], l)
		return msg, true
	}

	l.getUserID(data.ServerId, data.Type, data.Values, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || len(ids) != len(values) {
			msg.Code = uint32(ret.RET_USER_NOT_FOUND)
		} else {
			l.UserM().KickAccount(ids[0], l)
		}
		resultOnGRPCMessage(retCh, msg)
	})
	return msg, false
}

func (l *LogicService) onGRPCChangeUserName(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetUserLevel. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.changeUserName(ids[0], retCh)
		}
	})
	return msg, false
}

func (l *LogicService) changeUserName(userID uint64, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ChangeUserName), userID, &r2l.L2R_GRPC_ChangeUserName{
		UserId: userID,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGPRCSetSeasonArenaScore(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	l4g.Debugf("onGPRCSetSeasonArenaScore data:%+v", data)

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	seasonArenaM, ok := l.GetActivity(activity.SeasonArena).(*aseasonarena.Manager)
	if !ok {
		l4g.Errorf("uuid: %s SetSeasonArenaScore failed, seasonArenaM not exist", data.Uuid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	if !seasonArenaM.IsCrossConnected() {
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("SetSeasonArenaScore. data:%+v", data)
	if user != nil {
		seasonArenaUser := seasonArenaM.GetUser(user.ID())
		if seasonArenaUser == nil {
			l4g.Errorf("uuid: %s SetSeasonArenaScore failed, guild not exist", data.Uuid)
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			return msg, true
		}

		if !seasonArenaM.IsCrossConnected() {
			msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
			return msg, true
		}

		l.SendCmdToCross(l2c.ID_MSG_L2CS_GmSetSeasonArenaScore, user.ID(), &l2c.L2CS_GmSetSeasonArenaScore{
			Score: data.Value,
		})

		msg.Code = uint32(ret.RET_OK)
		return msg, true
	} else {
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		return msg, true
	}

	/*l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			seasonArenaUser := seasonArenaM.GetUser(user.ID())
			if seasonArenaUser == nil {
				l4g.Errorf("uuid: %s SetSeasonArenaScore failed, guild not exist", data.Uuid)
				msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
				resultOnGRPCMessage(retCh, msg)
				return
			}

			if !seasonArenaM.IsCrossConnected() {
				msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
				resultOnGRPCMessage(retCh, msg)
				return
			}

			l.SendCmdToCross(l2c.ID_MSG_L2CS_GmSetSeasonArenaScore, user.ID(), &l2c.L2CS_GmSetSeasonArenaScore{
				Score: data.Value,
			})
			msg.Code = uint32(ret.RET_OK)
			resultOnGRPCMessage(retCh, msg)
			return
		}
	})*/
}

func (l *LogicService) onGRPCSetSeasonLink(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetSeasonLink. data:%+v", data)
	if user != nil {
		l.UserM().KickAccount(user.ID(), l)
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setSeasonLink(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setSeasonLink(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetSeasonLink), userID, &r2l.L2R_GRPC_SetSeasonLink{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCUpdatePyramidActivity(data *cl.PyramidActivityBase) (proto.Message, bool) {
	l4g.Infof("grpc.onGRPCUpdatePyramidActivity: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdatePyramidActivity: data is nil")
		return msg, true
	}

	// 检查活动配置是否合法
	now := time.Now().Unix()
	if data.Id == 0 || data.ActivityId == 0 || data.StartTime == 0 ||
		data.EndTime == 0 || data.StartTime >= data.EndTime || data.EndTime <= now ||
		(data.OpStatus != gmxml.OpStatusRelease && data.OpStatus != gmxml.OpStatusOff) {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdatePyramidActivity: data illegal, data:%+v", data)
		return msg, true
	}

	// 检查配置中是否存在该活动类型
	info := goxml.GetData().ActivityPyramidInfoM.GetRecordById(data.ActivityId)
	if info == nil {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdatePyramidActivity: no activity cfg, id:%d, ActivityId:%d", data.Id, data.ActivityId)
		return msg, true
	}

	// 更新活动配置
	if !gmxml.PyramidActivityInfoM.SyncActivity(data, now) {
		msg.Code = uint32(ret.RET_GM_ACTIVITY_DATA_UPDATE_FAILED)
		l4g.Errorf("grpc.onGRPCUpdatePyramidActivity: update pyramid activity failed, id:%d", data.Id)
		return msg, true
	}

	// 同步客户端
	baseData := gmxml.PyramidActivityInfoM.GetActivityByUniqID(data.Id, goxml.ActivityPyramidNormalServer, l.StartServiceTm())
	l.BroadcastCmdToClient(cl.ID_MSG_L2C_PyramidUpdateActivity,
		&cl.L2C_PyramidUpdateActivity{
			Ret:    uint32(ret.RET_OK),
			Config: baseData,
		})

	/*
		// 如与新服活动时间有重叠，则需修正活动时间
		fixed, startTm := l.FixActivityTime(now, data.StartTime, data.EndTime, data.ProtectDay)
		baseData := gmxml.PyramidActivityInfoM.GetActivityByUniqID(data.Id)
		if fixed && baseData != nil {
			baseData.StartTime = startTm
			baseData.EndTime = data.EndTime
			l.BroadcastCmdToClient(cl.ID_MSG_L2C_PyramidUpdateActivity,
				&cl.L2C_PyramidUpdateActivity{
					Ret:    uint32(ret.RET_OK),
					Config: baseData,
				})
		}
	*/

	return msg, true
}

func (l *LogicService) onGRPCSetTaskFinish(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("onGRPCSetTaskFinish: invalid uuid or serverid, uuid:%s, serverId:%d", data.Uuid, data.ServerId)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user == nil {
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		l4g.Errorf("onGRPCSetTaskFinish: user not online, uuid:%s ", data.Uuid)
		return msg, true
	}

	taskInfo := goxml.GetData().TaskInfoM.Index(data.Value)
	if taskInfo == nil {
		msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
		l4g.Errorf("uuid:%s onGRPCSetTaskFinish: get task:%d is nil", data.Uuid, data.Value)
		return msg, true
	}

	moduleTask := goxml.GetData().TaskInfoM.GetModule(taskInfo.Module)
	if len(moduleTask) == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("uuid:%s onGRPCSetTaskFinish: get task:%d module:%d is nil", data.Uuid, data.Value, taskInfo.Module)
		return msg, true
	}

	resetSetNewFinishID := make(map[uint32][]uint32)
	for _, v := range moduleTask {
		if v == nil {
			continue
		}
		resetSetNewFinishID[v.ResetType] = append(resetSetNewFinishID[v.ResetType], v.Id)
		if v.Id == data.Value {
			break
		}
	}

	// 成就任务不清除
	for resetType, finishID := range resetSetNewFinishID {
		switch resetType {
		case uint32(common.TASK_RESET_TYPE_RET_ACHIEVE):
			user.Achieve().ReceiveAward(finishID)
		case uint32(common.TASK_RESET_TYPE_RET_DAILY):
			user.DailyTask().Reset()
			user.DailyTask().ReceiveAward(finishID)
		case uint32(common.TASK_RESET_TYPE_RET_WEEKLY):
			user.WeeklyTask().Reset()
			user.WeeklyTask().ReceiveAward(finishID)
		case uint32(common.TASK_RESET_TYPE_RET_MONTHLY):
			user.MonthlyTask().Reset()
			user.MonthlyTask().ReceiveAward(finishID)
		case uint32(common.TASK_RESET_TYPE_RET_LINE):
			user.LineTask().GMReset()
			user.LineTask().ReceiveAward(finishID)
		}
	}
	user.SendTasksToClient(0)

	return msg, true
}

func (l *LogicService) onGRPCAddGuildMobScore(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	l4g.Debugf("onGRPCAddGuildMobScore data:%+v", data)

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	guildM, ok := l.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("uuid: %s AddGuildMobScore failed, guildManager not exist", data.Uuid)
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	if !guildM.IsCrossConnected() {
		msg.Code = uint32(ret.RET_CROSS_REQ_TIMEOUT)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCAddGuildMobScore. data:%+v", data)
	if user != nil {
		guild := guildM.GetGuildByUser(user.ID())
		if guild == nil {
			l4g.Errorf("uuid: %s AddGuildMobScore failed, guild not exist", data.Uuid)
			msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			return msg, true
		}

		l.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationAddScoreForGM, user.ID(), &l2c.L2CS_GuildMobilizationAddScoreForGM{
			Score: data.Value,
		})
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			guild := guildM.GetGuildByUser(ids[0])
			if guild == nil {
				l4g.Errorf("uuid: %s AddGuildMobScore failed, guild not exist", data.Uuid)
				msg.Code = uint32(ret.RET_USER_NOT_EXIST)
				resultOnGRPCMessage(retCh, msg)
				return
			} else {
				l.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationAddScoreForGM, ids[0], &l2c.L2CS_GuildMobilizationAddScoreForGM{
					Score: data.Value,
				})
				msg.Code = uint32(ret.RET_OK)
				resultOnGRPCMessage(retCh, msg)
			}
		}
	})

	return msg, false
}

func (l *LogicService) onGRPCSetTalentTree(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("onGRPCSetTaskFinish: invalid uuid or serverid, uuid:%s, serverId:%d", data.Uuid, data.ServerId)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			user := l.UserM().GetUserWithCache(ids[0])
			if user == nil {
				msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
				resultOnGRPCMessage(retCh, msg)
				return
			}
			changeIds := user.TalentTree().GmSetLevel(data.Value)
			if len(changeIds) > 0 {
				hotRankM, ok := l.GetActivity(activity.HotRank).(*hotrank.Manager)
				if ok {
					for id, levels := range changeIds {
						if len(levels) == 2 {
							hotRankM.GetTalentTreeHotM().UpdateTalentTreeNodeLevel(id, levels[0], levels[1])
						}
					}
				} else {
					l4g.Errorf("onGRPCSetTalentTree: hotrank activity error.")
				}
			}
			resultOnGRPCMessage(retCh, msg)
		}
	})
	return msg, false
}

// 设置Boss挑战等级
func (l *LogicService) onGRPCSetBossRushLevel(data *gm.SetBossRushLevelReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Debugf("onGRPCSetBossRushLevel: recv data:%v", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("onGRPCSetBossRushLevel: invalid uuid or serverid, uuid:%s, serverId:%d", data.Uuid, data.ServerId)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user == nil {
		msg.Code = uint32(ret.RET_COMMON_USER_OFFLINE)
		l4g.Errorf("onGRPCSetBossRushLevel: user not online, uuid:%s ", data.Uuid)
		return msg, true
	}
	r := user.BossRush().GmSetBossRush(data.Data, l)
	if r != ret.RET_OK {
		l4g.Errorf("onGRPCSetBossRushLevel: operation failed, uid:%d  data %+v", user.ID(), data.Data)
	} else {
		l4g.Infof("onGRPCSetBossRushLevel: operation success. uid:%d  data %+v", user.ID(), data.Data)
	}
	msg.Code = uint32(r)
	return msg, true
}

// 设置战斗英雄星级
func (l *LogicService) onGRPCSetBattleHeroStar(data *gm.BattleHeroStar, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Debugf("onGRPCSetBattleHeroStar: recv data:%v", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 || data.Hero == 0 || data.Star == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("onGRPCSetBattleHeroStar: params invalid, data:%+v", data)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user == nil {
		msg.Code = uint32(ret.RET_ERROR)
		l4g.Errorf("onGRPCSetBattleHeroStar: user not online, uuid:%s", data.Uuid)
		return msg, true
	}

	if data.Star > goxml.GetData().HeroStarInfoM.GetMaxStar() {
		msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
		l4g.Errorf("onGRPCSetBattleHeroStar: param star illegal, data:%+v", data)
		return msg, true
	}

	info := goxml.GetData().HeroInfoM.Index(data.Hero)
	if info == nil {
		msg.Code = uint32(ret.RET_SYSTEM_DATA_ERROR)
		l4g.Errorf("onGRPCSetBattleHeroStar: param hero illegal, data:%+v", data)
		return msg, true
	}

	if data.Star > info.LimitStar {
		msg.Code = uint32(ret.RET_ERROR)
		l4g.Errorf("onGRPCSetBattleHeroStar: param hero star more than limit star, data:%+v", data)
		return msg, true
	}

	heroM := user.HeroManager()
	var hero *character.Hero
	for _, h := range heroM.GetAllCommonHero() {
		if h.IsInBattleList() && h.GetHeroSysID() == data.Hero {
			hero = h
			break
		}
	}

	if hero == nil {
		msg.Code = uint32(ret.RET_HERO_NOT_EXIST)
		l4g.Errorf("onGRPCSetBattleHeroStar: hero not exist, data:%+v", data)
		return msg, true
	}

	hero.SetStar(data.Star)
	heroM.SetChange(hero.GetHid())
	return msg, true
}

func (l *LogicService) onGRPCHotfixCode(data *gm.HotfixReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}
	if data.Type == 1 {
		//修复玩家数据的代码
		err, version := l.GetHotfixManager().AddCode(data.Code, symbols.Symbols)
		if err != nil {
			l4g.Errorf("Hotfix title:%s code:%s fail, err:%+v", data.Title, data.Code, err)
			msg.Code = uint32(ret.RET_ERROR)
			return msg, true
		}
		l4g.Infof("Hotfix title:%s code:%s success", data.Title, data.Code)
		dbmsg := &r2l.L2R_SaveHotfixCode{
			Code: &db.HotfixCode{
				Version: uint64(version),
				Type:    data.Type,
				Title:   data.Title,
				Code:    data.Code,
			},
		}
		//保存修复脚本到redis
		l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveHotfixCode), 0, dbmsg)
		return msg, true
	} else {
		// 加载补丁函数foo.GetPatch()
		// 保存代码
		hotfixFile := patch.GetHotfixFile(*appsrv.PatchPath)
		err := os.WriteFile(hotfixFile, []byte(data.Code), 0600)
		if err != nil {
			fmt.Println(err)
			msg.Code = uint32(ret.RET_ERROR)
			return msg, true
		}
		err = patch.DoMultiPatch(hotfixFile)
		if err != nil {
			l4g.Errorf("Hotfix title:%s code:%s fail, err:%+v", data.Title, data.Code, err)
			msg.Code = uint32(ret.RET_ERROR)
		} else {
			l4g.Infof("Hotfix title:%s code:%s success", data.Title, data.Code)
		}
		return msg, true
	}
}

func (l *LogicService) onGRPCSetRemain(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	targetStar := data.Value
	if user != nil {
		rareTopStar := goxml.GetData().RemainStarInfoM.RareTopStar()
		for _, remainInfo := range goxml.GetData().RemainInfoM.Datas {
			if remainInfo == nil {
				continue
			}
			var remainStar uint32
			topStar := rareTopStar[remainInfo.Rare]
			remain := user.RemainM().GetRemain(remainInfo.Id)
			if targetStar > topStar {
				remainStar = topStar
			} else {
				remainStar = targetStar
			}
			if remain == nil {
				user.RemainM().NewRemainByGM(remainInfo.Id, remainStar)
			} else {
				if remainStar != remain.Star() {
					remain.SetStar(remainStar, user)
					user.RemainM().SetChange(remain)
				}
			}
		}
		return msg, true
	}

	return msg, false
}

// 设置gm config
func (l *LogicService) onGRPCUpdateSelectSummon(data *gm.SelectSummonReq) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCUpdateSelectSummon: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("[LogicService] onGRPCUpdateSelectSummon: data is nil. ")
		return msg, true
	}
	if retCode := l.updateSelectSummon(data); retCode != uint32(ret.RET_OK) {
		msg.Code = retCode
		l4g.Errorf("[LogicService] onGRPCUpdateSelectSummon: update divine demon summon failed. ret: %d", retCode)
		return msg, true
	}

	return msg, true
}

func (l *LogicService) updateSelectSummon(data *gm.SelectSummonReq) uint32 {
	switch gmxml.SelectSummonReleaseStatus(data.OpStatus) {
	case gmxml.SelectSummonStatusOnline:
		now := time.Now().Unix()
		if data.EndTime <= data.StartTime || data.EndTime <= now || data.Id < goxml.DivineDemonUniqIDMax {
			l4g.Errorf("updateSelectSummon: divine demon time or id is invalid.")
			return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		}
		if code := gmxml.SelectSummonInfoM.Online(data); code != uint32(ret.RET_OK) {
			l4g.Errorf("updateSelectSummon: divine demon online failed. ret:%d", code)
			return code
		}
	case gmxml.SelectSummonStatusOffline:
		if code := gmxml.SelectSummonInfoM.Offline(data.Id); code != uint32(ret.RET_OK) {
			l4g.Errorf("updateSelectSummon: divine demon offline failed. ret:%d", code)
			return code
		}
	default:
		l4g.Errorf("updateSelectSummon: divine demon release status is invalid.")
		return uint32(ret.RET_DIVINE_DEMON_OP_STATUS_INVALID)
	}

	l.BroadcastCmdToClient(cl.ID_MSG_L2C_SelectSummonUpdate, &cl.L2C_SelectSummonUpdate{Id: data.Id})

	return uint32(ret.RET_OK)
}

func (l *LogicService) onGRPCNewQuestionnaireFinish(data *gm.QuestionnaireFinishReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCNewQuestionnaireFinish: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if !l.checkUserIndex(data.User) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCNewQuestionnaireFinish: userIndex error.")
		return msg, true
	}
	if data.User.Type != uint32(gm.SEARCH_BY_ID) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCNewQuestionnaireFinish: userType error. type:%d", data.User.Type)
		return msg, true
	}
	userID := data.User.Ids[0]
	questionnaire := l.QuestionnaireM().GetQuestionnaire(data.QuestionnaireId)
	if questionnaire == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCNewQuestionnaireFinish: questionnaireInfo not exist. id:%d", data.QuestionnaireId)
		return msg, true
	}
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetQuestionnaireFinish), userID, &r2l.L2R_GRPC_SetQuestionnaireFinish{
		ID:              userID,
		QuestionnaireID: data.QuestionnaireId,
		Callback: func(retCode uint32, userID uint64, finishID uint32) {
			l4g.Infof("[LogicService] onGRPCNewQuestionnaireFinish: callback:%d %+v", retCode, finishID)
			msg.Code = retCode
			if retCode != uint32(ret.RET_OK) {
				l4g.Errorf("grpc [LogicService] onGRPCNewQuestionnaireFinish: set finish error. id:%d", data.QuestionnaireId)
				resultOnGRPCMessage(retCh, msg)
				return
			}
			character.QuestionnaireMail(l, questionnaire.Awards, userID)
			//在线处理
			user := l.UserM().GetUserWithCache(userID)
			if user == nil {
				resultOnGRPCMessage(retCh, msg)
				return
			}

			user.LogQuestionnaireFinish(l, finishID)

			//如果是缓存User，也需要进行添加
			user.Questionnaires().AddFinishID(finishID)
			if user.IsOnline() {
				smsg := &cl.L2C_QuestionnaireUpdate{}
				user.SendCmdToGateway(cl.ID_MSG_L2C_QuestionnaireUpdate, smsg)
			}
			resultOnGRPCMessage(retCh, msg)
		},
	})

	return msg, false
}

// 修改玩家英雄登录天数
func (l *LogicService) onGRPCSetDailyAttendanceHero(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}

	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	l4g.Debug("onGRPCSetDailyAttendanceHero. data:%+v", data)
	if user != nil {
		user.DailyAttendanceHero().SetLoginDay(data.Value)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.setUserDailyAttendanceHeroLogin(ids[0], data.Value, retCh)
		}
	})

	return msg, false
}

func (l *LogicService) setUserDailyAttendanceHeroLogin(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetDailyAttendanceHero), userID, &r2l.L2R_GRPC_SetDailyAttendanceHero{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCDeleteServerMail(data *gm.DelServerMail, _ chan proto.Message) (*gm.Result, bool) {
	l4g.Infof("grpc [LogicService] onGRPCDeleteServerMail: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	character.GMDeleteServerMail(l, data, msg)
	return msg, true
}

func (l *LogicService) onGRPCUpdateCouponActivity(data *cl.ActivityCouponXml, _ chan proto.Message) (*gm.Result, bool) {
	l4g.Infof("grpc [LogicService] onGRPCUpdateCouponActivity: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateCouponActivity: data is nil")
		return msg, true
	}

	if data.ActId == 0 || data.OpenDay == 0 ||
		data.CloseDay == 0 || data.OpenDay >= data.CloseDay ||
		(data.State != gmxml.OpStatusRelease && data.State != gmxml.OpStatusOff) ||
		len(data.Res) == 0 {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateCouponActivity: data illegal, data:%+v", data)
		return msg, true
	}

	now := time.Now().Unix()
	if data.CloseDay <= now {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateCouponActivity: endTime < now, id:%d", data.ActId)
		return msg, true
	}

	//检查兑换规则
	check := make(map[uint32]map[uint32]*cl.ActivityCouponRes)
	for _, res := range data.Res {
		_, exist := check[res.Day]
		if !exist {
			check[res.Day] = make(map[uint32]*cl.ActivityCouponRes)
		}

		_, exist = check[res.Day][res.AwardType]
		if exist {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateCouponActivity act ID:%d res day:%d reward type:%d is error", data.ActId, res.Day, res.AwardType)
			return msg, true
		}

		check[res.Day][res.AwardType] = res
	}

	betweenDay := (data.CloseDay - data.OpenDay) / util.DaySecs

	if betweenDay == 0 {
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		l4g.Errorf("grpc.onGRPCUpdateCouponActivity: data illegal, data:%+v", data)
		return msg, true
	}

	for i := uint32(1); i <= uint32(betweenDay); i++ {
		_, exist := check[i]
		if !exist {
			msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
			l4g.Errorf("grpc.onGRPCUpdateCouponActivity ")
			return msg, true
		}

		for awardType := range goxml.ActivityCouponType {
			res, exist := check[i][awardType]
			if !exist {
				msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
				l4g.Errorf("grpc.onGRPCUpdateCouponActivity actID:%d day:%d awardtype:%d not exist", data.ActId, i, awardType)
				return msg, true

			}

			if len(res.Award) == 0 {
				msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
				l4g.Errorf("grpc.onGRPCUpdateCouponActivity actID:%d day:%d awardtype:%d award is zero", data.ActId, i, awardType)
				return msg, true
			}

			switch awardType {
			case goxml.ActivityCouponFree:
				if len(res.Cost) > 0 {
					msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
					l4g.Errorf("grpc.onGRPCUpdateCouponActivity actID:%d day:%d awardtype:%d cost is more than one", data.ActId, i, awardType)
					return msg, true
				}
			case goxml.ActivityCouponDiamond:
				if len(res.Cost) == 0 {
					msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
					l4g.Errorf("grpc.onGRPCUpdateCouponActivity actID:%d day:%d awardtype:%d cost is zero", data.ActId, i, awardType)
					return msg, true
				}
			case goxml.ActivityCouponRecharge:
				if len(res.Cost) > 0 {
					msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
					l4g.Errorf("grpc.onGRPCUpdateCouponActivity actID:%d day:%d awardtype:%d cost is more than one", data.ActId, i, awardType)
					return msg, true
				}
				if res.InternalId == 0 || res.SdkPid == "" {
					msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
					l4g.Errorf("grpc.onGRPCUpdateCouponActivity actID:%d day:%d awardtype:%d internal id or sdk_pid is error", data.ActId, i, awardType)
					return msg, true
				}
			}
		}
	}

	if !gmxml.ActivityCouponInfoM.SyncActivity(data, now) {
		msg.Code = uint32(ret.RET_GM_ACTIVITY_DATA_UPDATE_FAILED)
		l4g.Errorf("grpc.onGRPCUpdateDropActivity: update drop activity failed, id:%d", data.ActId)
		return msg, true
	}

	activityCouponInfo := gmxml.ActivityCouponInfoM.GetCurrentActivity()
	if activityCouponInfo != nil {
		l.BroadcastCmdToClient(cl.ID_MSG_L2C_ActivityCouponXmlUpdate,
			&cl.L2C_ActivityCouponXmlUpdate{
				Ret:   uint32(ret.RET_OK),
				Datas: activityCouponInfo})
	}

	return msg, true
}

// 设置gm config
func (l *LogicService) onGRPCUpdatePokemonSummon(data *gm.PokemonSummonReq) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCUpdatePokemonSummon: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("[LogicService] onGRPCUpdatePokemonSummon: data is nil. ")
		return msg, true
	}
	if retCode := l.updatePokemonSummon(data); retCode != uint32(ret.RET_OK) {
		msg.Code = retCode
		l4g.Errorf("[LogicService] onGRPCUpdatePokemonSummon: update divine demon summon failed. ret: %d", retCode)
		return msg, true
	}

	return msg, true
}

func (l *LogicService) updatePokemonSummon(data *gm.PokemonSummonReq) uint32 {
	switch gmxml.PokemonSummonReleaseStatus(data.OpStatus) {
	case gmxml.PokemonSummonStatusOnline:
		now := time.Now().Unix()
		if data.EndTime <= data.StartTime || data.EndTime <= now {
			l4g.Errorf("updatePokemonSummon: pokemon summon time or id is invalid.")
			return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
		}
		if code := gmxml.PokemonSummonInfoM.Online(data); code != uint32(ret.RET_OK) {
			l4g.Errorf("updatePokemonSummon: pokemon summon online failed. ret:%d", code)
			return code
		}
	case gmxml.PokemonSummonStatusOffline:
		if code := gmxml.PokemonSummonInfoM.Offline(data.Id); code != uint32(ret.RET_OK) {
			l4g.Errorf("updatePokemonSummon: pokemon summon offline failed. ret:%d", code)
			return code
		}
	default:
		l4g.Errorf("updatePokemonSummon: pokemon summon release status is invalid.")
		return uint32(ret.RET_DIVINE_DEMON_OP_STATUS_INVALID)
	}

	l.BroadcastCmdToClient(cl.ID_MSG_L2C_PokemonSummonUpdate, &cl.L2C_PokemonSummonUpdate{Id: data.Id})

	return uint32(ret.RET_OK)
}

func (l *LogicService) onGRPCSetTowerPokemonDungeonID(data *gm.UserReq, retCh chan proto.Message) (proto.Message, bool) {
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if data.Uuid == "" || data.ServerId == 0 || data.Value == 0 {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		return msg, true
	}
	user := l.UserM().GetUnauthUser(data.Uuid, data.ServerId)
	if user != nil {
		dungeonM := user.TowerPokemon()
		dungeonM.SetDungeonId(data.Value)
		msg.Code = uint32(ret.RET_OK)
		return msg, true
	}

	l.getUserID(data.ServerId, uint32(gm.SEARCH_BY_UUID), []string{data.Uuid}, nil, func(values []string, ids []uint64) {
		if len(ids) == 0 || ids[0] == 0 {
			msg.Code = uint32(ret.RET_USER_NOT_EXIST)
			resultOnGRPCMessage(retCh, msg)
		} else {
			l.changeTowerPokemonDungeonID(ids[0], data.Value, retCh)
		}
	})
	return msg, false
}

func (l *LogicService) changeTowerPokemonDungeonID(userID uint64, value uint32, retCh chan proto.Message) {
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_ChangeTowerPokemonDungeonID), userID, &r2l.L2R_GRPC_ChangeTowerPokemonDungeonID{
		ID:    userID,
		Value: value,
		Callback: func() {
			resultOnGRPCMessage(retCh, &gm.Result{
				Code: uint32(ret.RET_OK),
			})
		},
	})
}

func (l *LogicService) onGRPCOfflineVipAward(data *gm.OfflineVipAwardReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCOfflineVipReward: recv %s", data)

	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	if !l.checkUserIndex(data.User) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCOfflineVipReward: userIndex error.")
		return msg, true
	}
	if data.User.Type != uint32(gm.SEARCH_BY_ID) {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCOfflineVipReward: userType error. type:%d", data.User.Type)
		return msg, true
	}
	userID := data.User.Ids[0]
	info := goxml.GetData().OfflineVipAwardInfoM.GetRecordById(data.Id)
	if info == nil {
		msg.Code = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		l4g.Errorf("grpc [LogicService] onGRPCOfflineVipReward: award not exist. id:%s", data.Id)
		return msg, true
	}
	l.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_SetOfflineVipAward), userID, &r2l.L2R_GRPC_SetOfflineVipAward{
		ID:                userID,
		OfflineVipAwardID: data.Id,
		Callback: func(retCode uint32) {
			l4g.Infof("[LogicService] onGRPCOfflineVipReward: callback:%d %+v", retCode)
			msg.Code = retCode
			if retCode != uint32(ret.RET_OK) {
				l4g.Errorf("grpc [LogicService] onGRPCOfflineVipReward: set finish error. id:%d, retCode:%d", data.Id, retCode)
				if retCode == uint32(ret.RET_REPEATED_RECEIVE_AWARD) {
					msg.Code = uint32(ret.RET_OK)
				}
				resultOnGRPCMessage(retCh, msg)
				return
			}
			//mailID := goxml.GetData().ConfigInfoM.OfflineVipAwardMailId
			mailID := info.Mail
			character.OfflineVipAwardMail(l, info.Rewards, mailID, userID)
			resultOnGRPCMessage(retCh, msg)
		},
	})

	return msg, false
}

// gm后台修改玩家数据通用协议
func (l *LogicService) onGRPCQaSet(data *gm.QaSetReq, retCh chan proto.Message) (proto.Message, bool) {
	l4g.Infof("grpc [LogicService] onGRPCQaSet: recv %s", data)
	msg := &gm.Result{
		Code: uint32(ret.RET_OK),
	}

	switch data.GetName() {
	case "goddess_prize_draw:apply":
		manager := l.GetActivityM(actm.GoddessPrizeDraw).(*goddessprizedraw.Manager)
		if manager == nil {
			l4g.Errorf("onGRPCQaSet: goddessprizedraw manager is nil")
			msg.Code = uint32(ret.RET_SERVER_ERROR)
			return msg, true
		}
		msg.Code = manager.QaSet(l, data.GetContent())
	case "balance_arena:sign":
		manager := l.GetActivity(activity.BalanceArena).(*balancearena.Manager)
		if manager == nil {
			l4g.Errorf("onGRPCQaSet: BalanceArena manager is nil")
			msg.Code = uint32(ret.RET_SERVER_ERROR)
			return msg, true
		}
		msg.Code = manager.QaSet(l, data.GetContent())
	default:
		msg.Code = uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
	}

	return msg, true

}
