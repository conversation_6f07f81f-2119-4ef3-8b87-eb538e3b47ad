package service

import (
	cactivitycompliance "app/logic/command/activitycompliance"
	cactivitycoupon "app/logic/command/activitycoupon"
	cactivitymix "app/logic/command/activitymix"
	cactivityrecharge "app/logic/command/activityrecharge"
	cactivityreturn "app/logic/command/activityreturn"
	cactivitystory "app/logic/command/activitystory"
	cactivitysum "app/logic/command/activitysum"
	cactivityturntable "app/logic/command/activityturntable"
	cannouncement "app/logic/command/announcement"
	carena "app/logic/command/arena"
	cartifact "app/logic/command/artifact"
	cartifactdebut "app/logic/command/artifactdebut"
	cassistanceactivity "app/logic/command/assistanceactivity"
	cassistant "app/logic/command/assistant"
	cavatar "app/logic/command/avatar"
	cbalancearena "app/logic/command/balancearena"
	cbossrush "app/logic/command/bossrush"
	ccarnival "app/logic/command/carnival"
	cchat "app/logic/command/chat"
	ccompliancetasks "app/logic/command/compliancetasks"
	ccross "app/logic/command/cross"
	ccrossmaster "app/logic/command/crossmaster"
	ccrystal "app/logic/command/crystal"
	cdailyattendance "app/logic/command/dailyattendance"
	cdailyattendancehero "app/logic/command/dailyattendancehero"
	cdailyspecial "app/logic/command/dailyspecial"
	cdailywish "app/logic/command/dailywish"
	cdisorderland "app/logic/command/disorderland"
	cdispatch "app/logic/command/dispatch"
	cdivinedemon "app/logic/command/divinedemon"
	cdropactivity "app/logic/command/dropactivity"
	cduel "app/logic/command/duel"
	cdungeon "app/logic/command/dungeon"
	cemblem "app/logic/command/emblem"
	cequip "app/logic/command/equip"
	cflower "app/logic/command/flower"
	cforecast "app/logic/command/forecast"
	cfragment "app/logic/command/fragment"
	cfriend "app/logic/command/friend"
	cfunctionstatus "app/logic/command/functionstatus"
	cgateway "app/logic/command/gateway"
	cgem "app/logic/command/gem"
	cgoddesscontract "app/logic/command/goddesscontract"
	cgodpresent "app/logic/command/godpresent"
	cgoldbuy "app/logic/command/goldbuy"
	cgst "app/logic/command/gst"
	cguidance "app/logic/command/guidance"
	cguild "app/logic/command/guild"
	cguildchest "app/logic/command/guildchest"
	cguilddungeon "app/logic/command/guilddungeon"
	cguildmobilization "app/logic/command/guildmobilization"
	cguildtalent "app/logic/command/guildtalent"
	chandbooks "app/logic/command/handbooks"
	chero "app/logic/command/hero"
	chotrank "app/logic/command/hotrank"
	chttp "app/logic/command/http"
	citem "app/logic/command/item"
	clink "app/logic/command/link"
	clinksummon "app/logic/command/linksummon"
	cmail "app/logic/command/mail"
	cmaze "app/logic/command/maze"
	cmedal "app/logic/command/medal"
	cmemory "app/logic/command/memory"
	cmirage "app/logic/command/mirage"
	cmonthlycard "app/logic/command/monthlycard"
	cactivitytasks "app/logic/command/monthtasks"
	cnewyearactivity "app/logic/command/newyearactivity"
	coperateactivity "app/logic/command/operateactivity"
	cpass "app/logic/command/pass"
	cpeak "app/logic/command/peak"
	cpokemon "app/logic/command/pokemon"
	cpokemonsummon "app/logic/command/pokemonsummon"
	cpreseason "app/logic/command/preseason"
	cpushgift "app/logic/command/pushgift"
	cpyramid "app/logic/command/pyramid"
	crankachieve "app/logic/command/rankachieve"
	crate "app/logic/command/rate"
	crecharge "app/logic/command/recharge"
	credis "app/logic/command/redis"
	credpoint "app/logic/command/redpoint"
	cremain "app/logic/command/remain"
	crite "app/logic/command/rite"
	croundactivity "app/logic/command/roundactivity"
	cseasonArena "app/logic/command/seasonarena"
	cseasoncompliance "app/logic/command/seasoncompliance"
	"app/logic/command/seasondoor"
	cseasondungeon "app/logic/command/seasondungeon"
	cseasonjewelry "app/logic/command/seasonjewelry"
	"app/logic/command/seasonlevel"
	cseasonlink "app/logic/command/seasonlink"
	"app/logic/command/seasonmap"
	cseasonreturn "app/logic/command/seasonreturn"
	cseasonshop "app/logic/command/seasonshop"
	cseasonstart "app/logic/command/seasonstart"
	cselectsummon "app/logic/command/selectsummon"
	csevendaylogin "app/logic/command/sevendaylogin"
	cshop "app/logic/command/shop"
	cskin "app/logic/command/skin"
	cstoryreview "app/logic/command/storyreview"
	csummon "app/logic/command/summon"
	ctalenttree "app/logic/command/talenttree"
	ctales "app/logic/command/tales"
	ctask "app/logic/command/task"
	ctitle "app/logic/command/title"
	ctower "app/logic/command/tower"
	"app/logic/command/towerpokemon"
	ctowerseason "app/logic/command/towerseason"
	ctowerstar "app/logic/command/towerstar"
	ctrial "app/logic/command/trial"
	cuser "app/logic/command/user"
	cvip "app/logic/command/vip"
	cworldboss "app/logic/command/worldboss"
	cwrestle "app/logic/command/wrestle"

	l4g "github.com/ivanabc/log4go"
)

const (
	CmdsTypeGateway = 1
	CmdsTypeUser    = 2
	CmdsTypeItem    = 3 //道具
	CmdsTypeMail    = 4 //邮件
	CmdsTypeShop    = 5 //商店
	// CmdsTypeAchieve          = 6  //成就和每日任务
	CmdsTypeFriend = 7 //好友
	// CmdsTypeDailyTask        = 8  //日常任务
	CmdsTypeDungeon     = 9  //战役
	CmdsTypeHero        = 10 //英雄
	CmdsTypeSummon      = 11 //召唤
	CmdsTypeTask        = 12 //通用任务
	CmdsTypeFragment    = 13 //碎片
	CmdsTypeEquip       = 14 //装备
	CmdsTypeTower       = 15 //爬塔
	CmdsTypeArena       = 16 //普通竞技场
	CmdsTypeArtifact    = 17 //神器
	CmdsTypeGem         = 18 //宝石
	CmdsTypeAvatar      = 19 //头像
	CmdsTypeTrial       = 20 //材料本
	CmdsTypeEmblem      = 21 //纹章
	CmdsTypeGoldBuy     = 22 //点金
	CmdsTypeDispatch    = 23 //悬赏任务
	CmdsTypeRankAchieve = 24 //排行成就
	CmdsTypeMaze        = 25 //迷宫
	CmdsTypeMirage      = 26 //个人boss
	CmdsTypeGuild       = 27 //公会
	CmdsTypeTales       = 28 //英雄列传模块
	CmdsTypeMemory      = 29 //境界
	// CmdsTypeForest           = 30 //密林
	CmdsTypeGuildDungeon         = 31  //公会副本
	CmdsTypeGuildTalent          = 32  //公会天赋
	CmdsTypeRedPoint             = 33  //红点
	CmdsTypeGuidance             = 34  //新手引导
	CmdsTypeCarnival             = 35  //嘉年华
	CmdsTypeSevenDayLogin        = 36  //七日登录活动
	CmdsTypeHandbook             = 37  //图鉴
	CmdsTypeMedal                = 38  //功勋
	CmdsTypeChat                 = 39  //聊天
	CmdsTypeForecast             = 40  //新功能预告
	CmdsTypeCrystal              = 41  //水晶
	CmdsTypeRecharge             = 42  //充值
	CmdsTypeVip                  = 43  //vip模块
	CmdsTypeActivityRecharge     = 44  //限时礼包模块
	CmdsTypeTowerstar            = 45  //条件爬塔
	CmdsTypeMonthlycard          = 46  //月卡
	CmdsTypePass                 = 47  //战令
	CmdsTypePushGift             = 48  //推送礼包
	CmdsTypeRate                 = 49  //评分
	CmdsTypeOperateActivity      = 50  //配置活动
	CmdsTypeGoddesscontract      = 51  //契约之所
	CmdsTypeDivineDemon          = 52  //神魔抽卡
	CmdsTypeWrestle              = 53  //神树争霸
	CmdsTypeFunctionstatus       = 54  //神树争霸
	CmdsTypeLink                 = 55  //联结
	CmdsTypeAnnouncement         = 56  //公告
	CmdsTypeFlower               = 57  //密林
	CmdsTypeMonthTasks           = 58  //全民无双
	CmdsTypeDailywish            = 59  //每日许愿
	CmdsTypeLinksummon           = 60  //流派抽卡
	CmdsTypeArtifactDebut        = 61  //神器首发
	CmdsTypeRoundActivity        = 62  //红点
	CmdsTypeTowerSeason          = 63  //百塔
	CmdsTypeGodPresent           = 64  //神之馈赠
	CmdsTypeDropActivity         = 65  //掉落活动
	CmdsTypeDailyAttendance      = 66  //累登
	CmdsTypeDailySpecial         = 67  //每日特惠
	CmdsTypeActivityMix          = 68  //活动聚合
	CmdsTypeGuildChest           = 69  //公会红包
	CmdsTypeWorldBoss            = 70  //活动聚合
	CmdsTypeSkin                 = 71  //皮肤
	CmdsTypeActivityStory        = 72  //活动故事
	CmdsTypeAssistanceActivity   = 73  //助力活动
	CmdsTypeRite                 = 74  //永恒仪式
	CmdsTypeSeasonLevel          = 75  //赛季等级
	CmdsTypeSeasonDungeon        = 76  //赛季主线
	CmdsTypeActivityReturn       = 77  //回流活动
	CmdsTypeDisorderLand         = 78  //失序空间
	CmdsTypePeak                 = 79  //巅峰竞技场
	CmdsTypePreSeason            = 80  //赛季前奖励
	CmdsTypeSeasonReturn         = 81  //赛季回流
	CmdsTypeAssistant            = 82  //小助手
	CmdsTypeSeasonLink           = 83  //赛季羁绊
	CmdsTypeGST                  = 84  //公会沙盘战
	CmdsTypeStoryReview          = 85  //剧情回忆录
	CmdsTypeNewYearActivity      = 86  //新年活动
	CmdsTypePyramid              = 87  //金字塔活动
	CmdsTypeRemain               = 88  //遗物系统
	CmdsTypeSeasonArena          = 89  //赛季竞技场
	CmdsTypeActivityTurnTable    = 90  //周年庆活动
	CmdsTypeHotRank              = 91  //热度榜
	CmdsTypeGuildMobilization    = 92  //公会竞赛
	CmdsTypeBossRush             = 93  //Boss挑战
	CmdsTypeTalentTree           = 94  //天赋树
	CmdsTypeActivitySum          = 95  //活动聚合
	CmdsTypeActivityCompliance   = 96  //开服冲榜活动
	CmdsTypeDuel                 = 97  //切磋
	CmdsTypeActivitySelectSummon = 98  //选择抽卡
	CmdsTypeSeasonStart          = 99  //赛季启动
	CmdsTypeSeasonCompliance     = 100 //赛季冲榜
	CmdsTypeSeasonJewelry        = 101 //赛季装备
	CmdsTypeSeasonDoor           = 102 //赛季开门玩法
	CmdsTypeTitle                = 103 //称号
	CmdsTypeSeasonShop           = 104 //赛季商店
	CmdsTypeComplianceTasks      = 105 //竞赛任务
	CmdsTypeActivityCoupon       = 106 //代金券活动
	CmdsTypeDailyAttendanceHero  = 107 //每日登录英雄
	CmdsTypeSeasonMap            = 108 //赛季地图
	CmdsTypePokemon              = 109 //宠物
	CmdsTypePokemonSummon        = 110 // 宠物抽卡
	CmdsTypeTowerPokemon         = 111 // 宠物爬塔
	CmdsTypeBalanceArena         = 112 // 公平竞技场
)

//nolint:funlen
func cmdsTypeString(tp int) string {
	switch tp {
	case CmdsTypeGateway:
		return "gateway"
	case CmdsTypeUser:
		return "主角"
	case CmdsTypeItem:
		return "道具"
	case CmdsTypeMail:
		return "邮件"
	case CmdsTypeShop:
		return "商店"
	//case CmdsTypeAchieve:
	//	return "成就和每日任务"
	case CmdsTypeFriend:
		return "好友"
	//case CmdsTypeDailyTask:
	//	return "日常任务"
	case CmdsTypeDungeon:
		return "战役"
	case CmdsTypeHero:
		return "英雄"
	case CmdsTypeSummon:
		return "召唤"
	case CmdsTypeTask:
		return "通用任务"
	case CmdsTypeFragment:
		return "碎片"
	case CmdsTypeEquip:
		return "装备"
	case CmdsTypeTower:
		return "爬塔"
	case CmdsTypeArena:
		return "竞技场"
	case CmdsTypeArtifact:
		return "神器"
	case CmdsTypeGem:
		return "宝石"
	case CmdsTypeAvatar:
		return "头像"
	case CmdsTypeTrial:
		return "材料本"
	case CmdsTypeEmblem:
		return "纹章"
	case CmdsTypeGoldBuy:
		return "点金"
	case CmdsTypeDispatch:
		return "悬赏任务"
	case CmdsTypeRankAchieve:
		return "排行成就"
	case CmdsTypeMaze:
		return "迷宫"
	case CmdsTypeMirage:
		return "个人boss"
	case CmdsTypeGuild:
		return "公会"
	case CmdsTypeTales:
		return "英雄列传模块"
	case CmdsTypeMemory:
		return "境界"
	// case CmdsTypeForest:
	// 	return "密林"
	case CmdsTypeGuildDungeon:
		return "公会副本"
	case CmdsTypeGuildTalent:
		return "公会天赋"
	case CmdsTypeRedPoint:
		return "红点"
	case CmdsTypeGuidance:
		return "新手引导"
	case CmdsTypeCarnival:
		return "嘉年华"
	case CmdsTypeSevenDayLogin:
		return "七日登录活动"
	case CmdsTypeHandbook:
		return "图鉴"
	case CmdsTypeMedal:
		return "功勋"
	case CmdsTypeChat:
		return "聊天"
	case CmdsTypeForecast:
		return "新功能预告"
	case CmdsTypeCrystal:
		return "水晶"
	case CmdsTypeRecharge:
		return "充值"
	case CmdsTypeVip:
		return "vip模块"
	case CmdsTypeActivityRecharge:
		return "限时礼包模块"
	case CmdsTypeTowerstar:
		return "条件爬塔"
	case CmdsTypeMonthlycard:
		return "月卡"
	case CmdsTypePass:
		return "战令"
	case CmdsTypePushGift:
		return "推送礼包"
	case CmdsTypeRate:
		return "评分"
	case CmdsTypeOperateActivity:
		return "配置活动"
	case CmdsTypeGoddesscontract:
		return "契约之所"
	case CmdsTypeDivineDemon:
		return "神魔抽卡"
	case CmdsTypeWrestle:
		return "神树争霸"
	case CmdsTypeFunctionstatus:
		return "功能状态"
	case CmdsTypeLink:
		return "联结"
	case CmdsTypeAnnouncement:
		return "公告"
	case CmdsTypeFlower:
		return "密林"
	case CmdsTypeMonthTasks:
		return "全民无双"
	case CmdsTypeDailywish:
		return "每日许愿"
	case CmdsTypeLinksummon:
		return "流派抽卡"
	case CmdsTypeArtifactDebut:
		return "神器首发"
	case CmdsTypeRoundActivity:
		return "新服轮次活动"
	case CmdsTypeTowerSeason:
		return "百塔"
	case CmdsTypeGodPresent:
		return "神之馈赠"
	case CmdsTypeDropActivity:
		return "掉落活动"
	case CmdsTypeDailyAttendance:
		return "累登"
	case CmdsTypeDailySpecial:
		return "每日特惠"
	case CmdsTypeActivityMix:
		return "活动聚合"
	case CmdsTypeGuildChest:
		return "公会红包"
	case CmdsTypeWorldBoss:
		return "世界boss"
	case CmdsTypeRite:
		return "永恒仪式"
	case CmdsTypeSkin:
		return "皮肤"
	case CmdsTypeActivityStory:
		return "活动故事"
	case CmdsTypeAssistanceActivity:
		return "助力活动"
	case CmdsTypeSeasonLevel:
		return "赛季等级"
	case CmdsTypeSeasonDungeon:
		return "赛季主线"
	case CmdsTypeActivityReturn:
		return "回流"
	case CmdsTypeDisorderLand:
		return "失序空间"
	case CmdsTypePeak:
		return "巅峰竞技场"
	case CmdsTypePreSeason:
		return "赛季前奖励"
	case CmdsTypeSeasonReturn:
		return "赛季回流"
	case CmdsTypeAssistant:
		return "小助手"
	case CmdsTypeGST:
		return "公会沙盘战"
	case CmdsTypeSeasonLink:
		return "赛季羁绊"
	case CmdsTypeStoryReview:
		return "剧情回忆录"
	case CmdsTypeNewYearActivity:
		return "新年活动"
	case CmdsTypePyramid:
		return "金字塔活动"
	case CmdsTypeRemain:
		return "遗物系统"
	case CmdsTypeSeasonArena:
		return "赛季竞技场"
	case CmdsTypeActivityTurnTable:
		return "周年庆活动"
	case CmdsTypeHotRank:
		return "热度榜"
	case CmdsTypeGuildMobilization:
		return "公会竞赛"
	case CmdsTypeBossRush:
		return "Boss挑战"
	case CmdsTypeTalentTree:
		return "天赋树"
	case CmdsTypeActivitySum:
		return "活动聚合"
	case CmdsTypeActivityCompliance:
		return "开服冲榜活动"
	case CmdsTypeDuel:
		return "切磋"
	case CmdsTypeActivitySelectSummon:
		return "选择抽卡"
	case CmdsTypeSeasonStart:
		return "赛季启动"
	case CmdsTypeSeasonCompliance:
		return "赛季冲榜"
	case CmdsTypeSeasonJewelry:
		return "赛季装备"
	case CmdsTypeTitle:
		return "称号"
	case CmdsTypeSeasonShop:
		return "赛季商店"
	case CmdsTypeComplianceTasks:
		return "赛季任务"
	case CmdsTypeActivityCoupon:
		return "代金券活动"
	case CmdsTypeDailyAttendanceHero:
		return "每日登录英雄"
	case CmdsTypeSeasonDoor:
		return "赛季开门"
	case CmdsTypeSeasonMap:
		return "赛季地图"
	case CmdsTypePokemon:
		return "宠物"
	case CmdsTypePokemonSummon:
		return "宠物抽卡"
	case CmdsTypeTowerPokemon:
		return "宠物爬塔"
	default:
		l4g.Errorf("no found gateway cmds type: %d", tp)
	}
	return ""
}

func (l *LogicService) openCmdRecoverPanic() {
	//设置cmd捕获panic
	l.gatewayCmds.OpenRecoverPanic()
	l.crossCmds.OpenRecoverPanic()
	l.redisCmds.OpenRecoverPanic()
	l.httpCmds.OpenRecoverPanic()
}

//nolint:funlen
func (l *LogicService) registerCmds() {
	credis.Init(l.redisCmds, true)

	chttp.Init(l.httpCmds, true)
	ccross.Init(l.crossCmds, true)
	ccrossmaster.Init(l.crossMasterCmds, true)
	l.RegisterGatewayCmds(CmdsTypeGateway, true)
	l.RegisterGatewayCmds(CmdsTypeUser, true)
	l.RegisterGatewayCmds(CmdsTypeItem, true)
	l.RegisterGatewayCmds(CmdsTypeMail, true)
	l.RegisterGatewayCmds(CmdsTypeShop, true)
	// l.RegisterGatewayCmds(CmdsTypeAchieve, true)
	l.RegisterGatewayCmds(CmdsTypeFriend, true)
	// l.RegisterGatewayCmds(CmdsTypeDailyTask, true)
	l.RegisterGatewayCmds(CmdsTypeDungeon, true)
	l.RegisterGatewayCmds(CmdsTypeHero, true)
	l.RegisterGatewayCmds(CmdsTypeSummon, true)
	l.RegisterGatewayCmds(CmdsTypeTask, true)
	l.RegisterGatewayCmds(CmdsTypeFragment, true)
	l.RegisterGatewayCmds(CmdsTypeEquip, true)
	l.RegisterGatewayCmds(CmdsTypeTower, true)
	l.RegisterGatewayCmds(CmdsTypeTower, true)
	l.RegisterGatewayCmds(CmdsTypeArena, true)
	l.RegisterGatewayCmds(CmdsTypeArtifact, true)
	l.RegisterGatewayCmds(CmdsTypeGem, true)
	l.RegisterGatewayCmds(CmdsTypeAvatar, true)
	l.RegisterGatewayCmds(CmdsTypeTrial, true)
	l.RegisterGatewayCmds(CmdsTypeEmblem, true)
	l.RegisterGatewayCmds(CmdsTypeGoldBuy, true)
	l.RegisterGatewayCmds(CmdsTypeDispatch, true)
	l.RegisterGatewayCmds(CmdsTypeRankAchieve, true)
	l.RegisterGatewayCmds(CmdsTypeMaze, true)
	l.RegisterGatewayCmds(CmdsTypeMirage, true)
	l.RegisterGatewayCmds(CmdsTypeGuild, true)
	l.RegisterGatewayCmds(CmdsTypeTales, true)
	l.RegisterGatewayCmds(CmdsTypeMemory, true)
	// l.RegisterGatewayCmds(CmdsTypeForest, true)
	l.RegisterGatewayCmds(CmdsTypeGuildDungeon, true)
	l.RegisterGatewayCmds(CmdsTypeGuildTalent, true)
	l.RegisterGatewayCmds(CmdsTypeRedPoint, true)
	l.RegisterGatewayCmds(CmdsTypeGuidance, true)
	l.RegisterGatewayCmds(CmdsTypeCarnival, true)
	l.RegisterGatewayCmds(CmdsTypeSevenDayLogin, true)
	l.RegisterGatewayCmds(CmdsTypeHandbook, true)
	l.RegisterGatewayCmds(CmdsTypeMedal, true)
	l.RegisterGatewayCmds(CmdsTypeChat, true)
	l.RegisterGatewayCmds(CmdsTypeForecast, true)
	l.RegisterGatewayCmds(CmdsTypeCrystal, true)
	l.RegisterGatewayCmds(CmdsTypeRecharge, true)
	l.RegisterGatewayCmds(CmdsTypeVip, true)
	l.RegisterGatewayCmds(CmdsTypeActivityRecharge, true)
	l.RegisterGatewayCmds(CmdsTypeTowerstar, true)
	l.RegisterGatewayCmds(CmdsTypeMonthlycard, true)
	l.RegisterGatewayCmds(CmdsTypePass, true)
	l.RegisterGatewayCmds(CmdsTypePushGift, true)
	l.RegisterGatewayCmds(CmdsTypeRate, true)
	l.RegisterGatewayCmds(CmdsTypeOperateActivity, true)
	l.RegisterGatewayCmds(CmdsTypeGoddesscontract, true)
	l.RegisterGatewayCmds(CmdsTypeDivineDemon, true)
	l.RegisterGatewayCmds(CmdsTypeWrestle, true)
	l.RegisterGatewayCmds(CmdsTypeFunctionstatus, true)
	l.RegisterGatewayCmds(CmdsTypeLink, true)
	l.RegisterGatewayCmds(CmdsTypeAnnouncement, true)
	l.RegisterGatewayCmds(CmdsTypeFlower, true)
	l.RegisterGatewayCmds(CmdsTypeMonthTasks, true)
	l.RegisterGatewayCmds(CmdsTypeDailywish, true)
	l.RegisterGatewayCmds(CmdsTypeLinksummon, true)
	l.RegisterGatewayCmds(CmdsTypeArtifactDebut, true)
	l.RegisterGatewayCmds(CmdsTypeRoundActivity, true)
	l.RegisterGatewayCmds(CmdsTypeTowerSeason, true)
	l.RegisterGatewayCmds(CmdsTypeGodPresent, true)
	l.RegisterGatewayCmds(CmdsTypeDropActivity, true)
	l.RegisterGatewayCmds(CmdsTypeDailyAttendance, true)
	l.RegisterGatewayCmds(CmdsTypeDailySpecial, true)
	l.RegisterGatewayCmds(CmdsTypeActivityMix, true)
	l.RegisterGatewayCmds(CmdsTypeGuildChest, true)
	l.RegisterGatewayCmds(CmdsTypeWorldBoss, true)
	l.RegisterGatewayCmds(CmdsTypeRite, true)
	l.RegisterGatewayCmds(CmdsTypeSkin, true)
	l.RegisterGatewayCmds(CmdsTypeActivityStory, true)
	l.RegisterGatewayCmds(CmdsTypeAssistanceActivity, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonLevel, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonDungeon, true)
	l.RegisterGatewayCmds(CmdsTypeActivityReturn, true)
	l.RegisterGatewayCmds(CmdsTypeDisorderLand, true)
	l.RegisterGatewayCmds(CmdsTypePeak, true)
	l.RegisterGatewayCmds(CmdsTypePreSeason, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonReturn, true)
	l.RegisterGatewayCmds(CmdsTypeAssistant, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonLink, true)
	l.RegisterGatewayCmds(CmdsTypeGST, true)
	l.RegisterGatewayCmds(CmdsTypeStoryReview, true)
	l.RegisterGatewayCmds(CmdsTypeNewYearActivity, true)
	l.RegisterGatewayCmds(CmdsTypePyramid, true)
	l.RegisterGatewayCmds(CmdsTypeRemain, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonArena, true)
	l.RegisterGatewayCmds(CmdsTypeActivityTurnTable, true)
	l.RegisterGatewayCmds(CmdsTypeHotRank, true)
	l.RegisterGatewayCmds(CmdsTypeGuildMobilization, true)
	l.RegisterGatewayCmds(CmdsTypeBossRush, true)
	l.RegisterGatewayCmds(CmdsTypeTalentTree, true)
	l.RegisterGatewayCmds(CmdsTypeActivitySum, true)
	l.RegisterGatewayCmds(CmdsTypeActivityCompliance, true)
	l.RegisterGatewayCmds(CmdsTypeDuel, true)
	l.RegisterGatewayCmds(CmdsTypeActivitySelectSummon, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonStart, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonCompliance, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonJewelry, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonDoor, true)
	l.RegisterGatewayCmds(CmdsTypeTitle, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonShop, true)
	l.RegisterGatewayCmds(CmdsTypeComplianceTasks, true)
	l.RegisterGatewayCmds(CmdsTypeActivityCoupon, true)
	l.RegisterGatewayCmds(CmdsTypeDailyAttendanceHero, true)
	l.RegisterGatewayCmds(CmdsTypeSeasonMap, true)
	l.RegisterGatewayCmds(CmdsTypePokemon, true)
	l.RegisterGatewayCmds(CmdsTypePokemonSummon, true)
	l.RegisterGatewayCmds(CmdsTypeTowerPokemon, true)
	l.RegisterGatewayCmds(CmdsTypeBalanceArena, true)
}

//nolint:funlen
func (l *LogicService) RegisterGatewayCmds(tp int, open bool) {
	l4g.Infof("register gateway cmds type(%d-%s): %t", tp, cmdsTypeString(tp), open)
	switch tp {
	case CmdsTypeGateway:
		cgateway.Init(l.gatewayCmds, open)
	case CmdsTypeUser:
		cuser.Init(l.gatewayCmds, open)
	case CmdsTypeItem:
		citem.Init(l.gatewayCmds, open)
	case CmdsTypeMail:
		cmail.Init(l.gatewayCmds, open)
	case CmdsTypeShop:
		cshop.Init(l.gatewayCmds, open)
	case CmdsTypeFriend:
		cfriend.Init(l.gatewayCmds, open)
	case CmdsTypeDungeon:
		cdungeon.Init(l.gatewayCmds, open)
	case CmdsTypeHero:
		chero.Init(l.gatewayCmds, open)
	case CmdsTypeSummon:
		csummon.Init(l.gatewayCmds, open)
	case CmdsTypeTask:
		ctask.Init(l.gatewayCmds, open)
	case CmdsTypeFragment:
		cfragment.Init(l.gatewayCmds, open)
	case CmdsTypeEquip:
		cequip.Init(l.gatewayCmds, open)
	case CmdsTypeTower:
		ctower.Init(l.gatewayCmds, open)
	case CmdsTypeArena:
		carena.Init(l.gatewayCmds, open)
	case CmdsTypeArtifact:
		cartifact.Init(l.gatewayCmds, open)
	case CmdsTypeArtifactDebut:
		cartifactdebut.Init(l.gatewayCmds, open)
	case CmdsTypeGem:
		cgem.Init(l.gatewayCmds, open)
	case CmdsTypeAvatar:
		cavatar.Init(l.gatewayCmds, open)
	case CmdsTypeTrial:
		ctrial.Init(l.gatewayCmds, open)
	case CmdsTypeEmblem:
		cemblem.Init(l.gatewayCmds, open)
	case CmdsTypeGoldBuy:
		cgoldbuy.Init(l.gatewayCmds, open)
	case CmdsTypeDispatch:
		cdispatch.Init(l.gatewayCmds, open)
	case CmdsTypeRankAchieve:
		crankachieve.Init(l.gatewayCmds, open)
	case CmdsTypeMaze:
		cmaze.Init(l.gatewayCmds, open)
	case CmdsTypeMirage:
		cmirage.Init(l.gatewayCmds, open)
	case CmdsTypeGuild:
		cguild.Init(l.gatewayCmds, open)
	case CmdsTypeTales:
		ctales.Init(l.gatewayCmds, open)
	case CmdsTypeMemory:
		cmemory.Init(l.gatewayCmds, open)
	// case CmdsTypeForest:
	// 	cforest.Init(l.gatewayCmds, open)
	case CmdsTypeGuildDungeon:
		cguilddungeon.Init(l.gatewayCmds, open)
	case CmdsTypeGuildTalent:
		cguildtalent.Init(l.gatewayCmds, open)
	case CmdsTypeRedPoint:
		credpoint.Init(l.gatewayCmds, open)
	case CmdsTypeRoundActivity:
		croundactivity.Init(l.gatewayCmds, open)
	case CmdsTypeGuidance:
		cguidance.Init(l.gatewayCmds, open)
	case CmdsTypeCarnival:
		ccarnival.Init(l.gatewayCmds, open)
	case CmdsTypeSevenDayLogin:
		csevendaylogin.Init(l.gatewayCmds, open)
	case CmdsTypeHandbook:
		chandbooks.Init(l.gatewayCmds, open)
	case CmdsTypeMedal:
		cmedal.Init(l.gatewayCmds, open)
	case CmdsTypeChat:
		cchat.Init(l.gatewayCmds, open)
	case CmdsTypeForecast:
		cforecast.Init(l.gatewayCmds, open)
	case CmdsTypeCrystal:
		ccrystal.Init(l.gatewayCmds, open)
	case CmdsTypeRecharge:
		crecharge.Init(l.gatewayCmds, open)
	case CmdsTypeVip:
		cvip.Init(l.gatewayCmds, open)
	case CmdsTypeActivityRecharge:
		cactivityrecharge.Init(l.gatewayCmds, open)
	case CmdsTypeTowerstar:
		ctowerstar.Init(l.gatewayCmds, open)
	case CmdsTypeMonthlycard:
		cmonthlycard.Init(l.gatewayCmds, open)
	case CmdsTypePass:
		cpass.Init(l.gatewayCmds, open)
	case CmdsTypePushGift:
		cpushgift.Init(l.gatewayCmds, open)
	case CmdsTypeRate:
		crate.Init(l.gatewayCmds, open)
	case CmdsTypeOperateActivity:
		coperateactivity.Init(l.gatewayCmds, open)
	case CmdsTypeGoddesscontract:
		cgoddesscontract.Init(l.gatewayCmds, open)
	case CmdsTypeDivineDemon:
		cdivinedemon.Init(l.gatewayCmds, open)
	case CmdsTypeWrestle:
		cwrestle.Init(l.gatewayCmds, open)
	case CmdsTypeFunctionstatus:
		cfunctionstatus.Init(l.gatewayCmds, open)
	case CmdsTypeLink:
		clink.Init(l.gatewayCmds, open)
	case CmdsTypeAnnouncement:
		cannouncement.Init(l.gatewayCmds, open)
	case CmdsTypeFlower:
		cflower.Init(l.gatewayCmds, open)
	case CmdsTypeMonthTasks:
		cactivitytasks.Init(l.gatewayCmds, open)
	case CmdsTypeDailywish:
		cdailywish.Init(l.gatewayCmds, open)
	case CmdsTypeLinksummon:
		clinksummon.Init(l.gatewayCmds, open)
	case CmdsTypeTowerSeason:
		ctowerseason.Init(l.gatewayCmds, open)
	case CmdsTypeGodPresent:
		cgodpresent.Init(l.gatewayCmds, open)
	case CmdsTypeDropActivity:
		cdropactivity.Init(l.gatewayCmds, open)
	case CmdsTypeDailyAttendance:
		cdailyattendance.Init(l.gatewayCmds, open)
	case CmdsTypeDailySpecial:
		cdailyspecial.Init(l.gatewayCmds, open)
	case CmdsTypeActivityMix:
		cactivitymix.Init(l.gatewayCmds, open)
	case CmdsTypeGuildChest:
		cguildchest.Init(l.gatewayCmds, open)
	case CmdsTypeWorldBoss:
		cworldboss.Init(l.gatewayCmds, open)
	case CmdsTypeRite:
		crite.Init(l.gatewayCmds, open)
	case CmdsTypeSkin:
		cskin.Init(l.gatewayCmds, open)
	case CmdsTypeActivityStory:
		cactivitystory.Init(l.gatewayCmds, open)
	case CmdsTypeAssistanceActivity:
		cassistanceactivity.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonLevel:
		seasonlevel.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonDungeon:
		cseasondungeon.Init(l.gatewayCmds, open)
	case CmdsTypeActivityReturn:
		cactivityreturn.Init(l.gatewayCmds, open)
	case CmdsTypeDisorderLand:
		cdisorderland.Init(l.gatewayCmds, open)
	case CmdsTypePeak:
		cpeak.Init(l.gatewayCmds, open)
	case CmdsTypePreSeason:
		cpreseason.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonReturn:
		cseasonreturn.Init(l.gatewayCmds, open)
	case CmdsTypeAssistant:
		cassistant.Init(l.gatewayCmds, open)
	case CmdsTypeGST:
		cgst.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonLink:
		cseasonlink.Init(l.gatewayCmds, open)
	case CmdsTypeStoryReview:
		cstoryreview.Init(l.gatewayCmds, open)
	case CmdsTypeNewYearActivity:
		cnewyearactivity.Init(l.gatewayCmds, open)
	case CmdsTypePyramid:
		cpyramid.Init(l.gatewayCmds, open)
	case CmdsTypeRemain:
		cremain.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonArena:
		cseasonArena.Init(l.gatewayCmds, open)
	case CmdsTypeActivityTurnTable:
		cactivityturntable.Init(l.gatewayCmds, open)
	case CmdsTypeHotRank:
		chotrank.Init(l.gatewayCmds, open)
	case CmdsTypeGuildMobilization:
		cguildmobilization.Init(l.gatewayCmds, open)
	case CmdsTypeBossRush:
		cbossrush.Init(l.gatewayCmds, open)
	case CmdsTypeTalentTree:
		ctalenttree.Init(l.gatewayCmds, open)
	case CmdsTypeActivitySum:
		cactivitysum.Init(l.gatewayCmds, open)
	case CmdsTypeActivityCompliance:
		cactivitycompliance.Init(l.gatewayCmds, open)
	case CmdsTypeDuel:
		cduel.Init(l.gatewayCmds, open)
	case CmdsTypeActivitySelectSummon:
		cselectsummon.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonStart:
		cseasonstart.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonCompliance:
		cseasoncompliance.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonJewelry:
		cseasonjewelry.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonDoor:
		seasondoor.Init(l.gatewayCmds, open)
	case CmdsTypeTitle:
		ctitle.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonShop:
		cseasonshop.Init(l.gatewayCmds, open)
	case CmdsTypeComplianceTasks:
		ccompliancetasks.Init(l.gatewayCmds, open)
	case CmdsTypeActivityCoupon:
		cactivitycoupon.Init(l.gatewayCmds, open)
	case CmdsTypeDailyAttendanceHero:
		cdailyattendancehero.Init(l.gatewayCmds, open)
	case CmdsTypeSeasonMap:
		seasonmap.Init(l.gatewayCmds, open)
	case CmdsTypePokemon:
		cpokemon.Init(l.gatewayCmds, open)
	case CmdsTypePokemonSummon:
		cpokemonsummon.Init(l.gatewayCmds, open)
	case CmdsTypeTowerPokemon:
		towerpokemon.Init(l.gatewayCmds, open)
	case CmdsTypeBalanceArena:
		cbalancearena.Init(l.gatewayCmds, open)
	default:
		l4g.Errorf("no found gateway cmds type: %d", tp)
	}
}
