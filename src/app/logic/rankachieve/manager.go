package rankachieve

import (
	"app/goxml"
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/cl"

	l4g "github.com/ivanabc/log4go"
)

type Manager struct {
	data    map[uint32]*db.RankAchieves //仅保存当前已完成的成就
	changes map[uint32]*db.RankAchieves
}

func NewManager() *Manager {
	return &Manager{
		//nolint:mnd
		data:    make(map[uint32]*db.RankAchieves, 16),
		changes: make(map[uint32]*db.RankAchieves),
	}
}

func (r *Manager) Load(data map[uint32]*db.RankAchieves) {
	r.data = data
	l4g.Debugf("Manager->load: data len:%d", len(data))
}

func (r *Manager) Flush(rankID uint32) []*cl.RankAchieve {
	if r.data[rankID] != nil && len(r.data[rankID].Achieves) > 0 {
		ret := make([]*cl.RankAchieve, 0, len(r.data[rankID].Achieves))
		for _, v := range r.data[rankID].Achieves {
			ret = append(ret, v.Clone())
		}
		return ret
	}
	return nil
}

func (r *Manager) NoRepeatUserList(rankID uint32) []uint64 {
	if r.data[rankID] != nil && len(r.data[rankID].Achieves) > 0 {
		var ret []uint64
		temp := make(map[uint64]struct{})
		for _, v := range r.data[rankID].Achieves {
			if _, exist := temp[v.Uid]; exist {
				continue
			} else {
				ret = append(ret, v.Uid)
				temp[v.Uid] = struct{}{}
			}
		}
		return ret
	}
	return nil
}

// 当前成就
func (r *Manager) currentAchieve(rankID uint32) *cl.RankAchieve {
	if r.data[rankID] == nil {
		return nil
	}
	return r.data[rankID].Achieves[len(r.data[rankID].Achieves)-1] //当前最大成就
}

func (r *Manager) save(srv Servicer) {
	changes := len(r.changes)
	msg := &r2l.L2R_SaveRankAchieve{}
	if changes > 0 {
		msg.Changes = make([]*db.RankAchieves, 0, changes)
		for _, c := range r.changes {
			msg.Changes = append(msg.Changes, c)
		}

		l4g.Debugf("Manager->save: changes length:%d", changes)
		r.changes = make(map[uint32]*db.RankAchieves)
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveRankAchieve), 0, msg)
	}
}

func (r *Manager) CheckAndUpdate(srv Servicer, rankID uint32, uid uint64, value int64) {
	targetID := goxml.GetData().RankingAchievementInfoM.GetIdByValue(rankID, value)
	if targetID == 0 {
		return
	}
	l4g.Debugf("Manager -> CheckAndUpdate: rankID:%d, uid:%d, value:%d", rankID, uid, value)

	var currentID uint32
	achieve := r.currentAchieve(rankID)
	if achieve != nil {
		currentID = achieve.Id
	}

	if targetID > currentID {
		finishedIds := goxml.GetData().RankingAchievementInfoM.GetIdsByRange(rankID, currentID, targetID)
		if len(finishedIds) > 0 {
			if r.currentAchieve(rankID) == nil {
				r.newOneData(rankID)
			}
			r.update(srv, uid, rankID, finishedIds)
			r.sendNotify(srv, rankID)
		}
	}
}

func (r *Manager) newOneData(rankID uint32) {
	r.data[rankID] = &db.RankAchieves{
		Id:       rankID,
		Achieves: make([]*cl.RankAchieve, 0, goxml.RankAchieveInitCap),
	}
}

func (r *Manager) update(srv Servicer, uid uint64, rankID uint32, ids []uint32) {
	for _, id := range ids {
		achieve := &cl.RankAchieve{
			Id:  id,
			Uid: uid,
		}

		r.data[rankID].Achieves = append(r.data[rankID].Achieves, achieve)
		r.changes[rankID] = r.data[rankID].Clone()
	}
	r.save(srv)
	l4g.Debugf("Manager -> update: rankID:%d, uid:%d, ids:%v", rankID, uid, ids)
}

// 通知客户端，重新调协议
func (r *Manager) sendNotify(srv Servicer, rankID uint32) {
	smsg := &cl.L2C_RankAchieveNotify{
		Id: rankID,
	}
	srv.BroadcastCmdToClient(cl.ID_MSG_L2C_RankAchieveNotify, smsg)
}

// 检查goalID对应的成就是否已完成
// 已在逻辑中限定，当前成就id是最大的
func (r *Manager) CheckGoalFinished(rankID, goalID uint32) bool {
	achieve := r.currentAchieve(rankID)
	if achieve == nil {
		return false
	}
	return achieve.Id >= goalID
}
