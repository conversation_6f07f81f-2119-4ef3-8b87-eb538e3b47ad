package rank

import (
	"app/protos/in/db"
	"app/protos/out/cl"

	"github.com/gogo/protobuf/proto"
)

type ActivityTower struct {
	db.RankActivityTower
}

func NewActivityTower(uid uint64, floorID uint32, tm int64, power uint64) *ActivityTower {
	return &ActivityTower{
		db.RankActivityTower{
			Uid:     uid,
			FloorId: floorID,
			Tm:      tm,
			Power:   power,
		},
	}
}

func (u *ActivityTower) Key() uint64 {
	return u.Uid
}

func (u *ActivityTower) ShowValue() *cl.RankValue {
	return &cl.RankValue{
		Id:         u.Uid,
		Value:      uint64(u.FloorId),
		Param1:     uint64(u.Tm),
		Param2:     u.Power,
		LikedCount: u.LikeCount,
	}
}

// 从大到小的顺序排列, floor相同的情况tm小的排前面
func (u *ActivityTower) CmpScore(v1, v2 interface{}) int {
	uv1 := v1.(*ActivityTower)
	uv2 := v2.(*ActivityTower)
	if uv1.FloorId == uv2.FloorId && uv1.Tm == uv2.Tm {
		return 0
	}
	if uv1.FloorId > uv2.FloorId ||
		(uv1.FloorId == uv2.FloorId && uv1.Tm < uv2.Tm) {
		return -1
	}
	return 1
}

// 值相同的情况下 uid大的排前面
func (u *ActivityTower) CmpKey(v1, v2 interface{}) int {
	uv1 := v1.(*ActivityTower)
	uv2 := v2.(*ActivityTower)
	if uv1.Uid == uv2.Uid {
		return 0
	}
	if uv1.Uid > uv2.Uid {
		return -1
	}
	return 1
}

// 初始化一个新的ActivityTower出来，配合load使用
func (u *ActivityTower) NewElement() CommonRankValuer {
	return &ActivityTower{
		db.RankActivityTower{},
	}
}

func (u *ActivityTower) Load(buf []byte) error {
	return proto.Unmarshal(buf, &u.RankActivityTower)
}

func (u *ActivityTower) Save() []byte {
	buf, _ := proto.Marshal(&u.RankActivityTower)
	return buf
}

func (u *ActivityTower) Like() uint32 {
	u.LikeCount++
	return u.LikeCount
}
