package rank

import (
	"app/protos/in/db"
	"app/protos/out/cl"

	"github.com/gogo/protobuf/proto"
	//l4g "github.com/ivanabc/log4go"
)

type RankActivityCompliance struct {
	db.RankActivityCompliance
}

func NewRankActivityCompliance(uid uint64, score uint32, tm int64, power uint64) *RankActivityCompliance {
	return &RankActivityCompliance{
		db.RankActivityCompliance{
			Uid:   uid,
			Score: score,
			Tm:    tm,
			Power: power,
		},
	}
}

func (u *RankActivityCompliance) Key() uint64 {
	return u.Uid
}

func (u *RankActivityCompliance) ShowValue() *cl.RankValue {
	return &cl.RankValue{
		Id:         u.Uid,
		Value:      uint64(u.Score),
		Param1:     u.Power,
		Param2:     uint64(u.Tm),
		LikedCount: u.LikeCount,
	}
}

// 从大到小的顺序排列, dungeon相同的情况tm小的排前面
func (u *RankActivityCompliance) CmpScore(v1, v2 interface{}) int {
	uv1 := v1.(*RankActivityCompliance)
	uv2 := v2.(*RankActivityCompliance)
	if uv1.Score == uv2.Score && uv1.Tm == uv2.Tm {
		return 0
	}
	if uv1.Score > uv2.Score ||
		(uv1.Score == uv2.Score && uv1.Tm < uv2.Tm) {
		return -1
	}
	return 1
}

// 值相同的情况下 uid大的排前面
func (u *RankActivityCompliance) CmpKey(v1, v2 interface{}) int {
	uv1 := v1.(*RankActivityCompliance)
	uv2 := v2.(*RankActivityCompliance)
	if uv1.Uid == uv2.Uid {
		return 0
	}
	if uv1.Uid > uv2.Uid {
		return -1
	}
	return 1
}

// 初始化一个新的UserDungeon出来，配合load使用
func (u *RankActivityCompliance) NewElement() CommonRankValuer {
	return &RankActivityCompliance{
		db.RankActivityCompliance{},
	}
}

func (u *RankActivityCompliance) Load(buf []byte) error {
	return proto.Unmarshal(buf, &u.RankActivityCompliance)
}

func (u *RankActivityCompliance) Save() []byte {
	buf, _ := proto.Marshal(&u.RankActivityCompliance)
	return buf
}

func (u *RankActivityCompliance) Like() uint32 {
	u.LikeCount++
	return u.LikeCount
}
