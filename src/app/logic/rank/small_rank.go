package rank

import (
	"sort"

	"app/protos/in/db"
	"app/protos/in/r2l"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

// 我们暂时不使用SmallRankManager, 全部使用CommonRankManager
// 小排行榜：排名100以内，数组排序
type SmallRankManager struct {
	ranks map[uint32]*SmallRank

	changes map[uint32]util.None
}

func NewSmallRankManager() *SmallRankManager {
	return &SmallRankManager{
		ranks:   make(map[uint32]*SmallRank),
		changes: make(map[uint32]util.None),
	}
}

func (m *SmallRankManager) Load(data map[uint32]*db.SmallRank) {
	for _, sr := range data {
		if _, e := smallRankReg[sr.Id]; !e {
			l4g.Errorf("[SmallRankManager] load rank not register:%d", sr.Id)
			continue
		}
		m.ranks[sr.Id] = NewSmallRank(sr.Id)
		m.ranks[sr.Id].load(sr)
		l4g.Infof("[SmallRankManager] load rank:%d len:%d", sr.Id, len(sr.Values))
	}
}

// 每秒定时存储
func (m *SmallRankManager) Save(srv Servicer) {
	if len(m.changes) != 0 {
		msg := &r2l.L2R_SaveSmallRank{}
		for id := range m.changes {
			msg.Ranks = append(msg.Ranks, m.ranks[id].save())
		}
		srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveSmallRank), 0, msg)

		m.changes = make(map[uint32]util.None)
	}
}

// 更新排行榜数据
// 注意v数据上榜后不能被修改，减少数据拷贝
// 根据v.Id做唯一性判断 数量少目前是直接遍历
// 如果id已存在榜单直接进行替换，否则新增插入
func (m *SmallRankManager) Update(id uint32, rv *db.RankValue) {
	if smallRankReg[id] == nil {
		l4g.Errorf("[SmallRankManager] Update: not register rank id:%d", id)
		return
	}
	if m.ranks[id] == nil {
		m.ranks[id] = NewSmallRank(id)
	}
	if m.ranks[id].Update(rv) {
		m.changes[id] = util.None{}
	}
}

// 获取排行榜数据 根据注册MaxNum全量返回
func (m *SmallRankManager) GetRankList(id uint32) []*db.RankValue {
	if m.ranks[id] != nil {
		return m.ranks[id].save().Values //安全返回数据 拷贝过的
	}
	return nil
}

type SmallRank struct {
	data *db.SmallRank
}

func NewSmallRank(id uint32) *SmallRank {
	return &SmallRank{
		data: &db.SmallRank{
			Id:     id,
			Values: make([]*db.RankValue, 0, smallRankReg[id].MaxNum+1),
		},
	}
}

func (s *SmallRank) ID() uint32 { return s.data.Id }

func (s *SmallRank) load(data *db.SmallRank) {
	if data != nil {
		s.data = data
		s.sort()
	}
}

func (s *SmallRank) save() *db.SmallRank {
	return s.data.Clone()
}

func (s *SmallRank) Len() int {
	return len(s.data.Values)
}

func (s *SmallRank) Swap(i, j int) {
	s.data.Values[i], s.data.Values[j] = s.data.Values[j], s.data.Values[i]
}

func (s *SmallRank) Less(i, j int) bool {
	return smallRankReg[s.data.Id].SortFunc(s.data.Values[i], s.data.Values[j])
}

func (s *SmallRank) sort() {
	if len(s.data.Values) >= 1 {
		sort.Sort(s)
		if uint32(len(s.data.Values)) > smallRankReg[s.data.Id].MaxNum {
			s.data.Values = s.data.Values[:smallRankReg[s.data.Id].MaxNum]
		}
	}
}

// 更新排行榜 注意v数据上榜后不能被修改
func (s *SmallRank) Update(rv *db.RankValue) bool {
	t0 := time.AccurateNow()
	if rv == nil {
		return false
	}
	//检查是否可以上榜
	if uint32(len(s.data.Values)) >= smallRankReg[s.data.Id].MaxNum {
		if !smallRankReg[s.data.Id].SortFunc(rv, s.data.Values[len(s.data.Values)-1]) {
			return false
		}
	}
	//遍历检查是否已在榜上
	var exist bool
	for index, value := range s.data.Values {
		if value.Id == rv.Id {
			exist = true
			s.data.Values[index] = rv
		}
	}
	if !exist {
		s.data.Values = append(s.data.Values, rv)
	}
	s.sort()
	//l4g.Debugf("[SmallRank] %d Update:%v", s.data.Id, s.data.Values)
	if elapsed := time.Since(t0).Nanoseconds(); elapsed > 1000000 { //nolint:mnd
		l4g.Errorf("[SmallRank] %d %d Update elapsed:%d", s.data.Id, len(s.data.Values), elapsed)
	}
	return true
}
