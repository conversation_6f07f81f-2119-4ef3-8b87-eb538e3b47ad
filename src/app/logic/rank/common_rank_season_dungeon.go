package rank

import (
	"app/protos/in/db"
	"app/protos/out/cl"

	"github.com/gogo/protobuf/proto"
	//l4g "github.com/ivanabc/log4go"
)

type RankSeasonDungeon struct {
	db.RankSeasonDungeon
}

func NewRankSeasonDungeon(uid uint64, dungeonID uint32, tm int64) *RankSeasonDungeon {
	return &RankSeasonDungeon{
		db.RankSeasonDungeon{
			Uid:       uid,
			DungeonId: dungeonID,
			Tm:        tm,
		},
	}
}

func (sd *RankSeasonDungeon) Key() uint64 {
	return sd.Uid
}

func (sd *RankSeasonDungeon) ShowValue() *cl.RankValue {
	return &cl.RankValue{
		Id:     sd.Uid,
		Value:  uint64(sd.DungeonId),
		Param1: uint64(sd.Tm),
	}
}

// 从大到小的顺序排列, dungeon相同的情况tm小的排前面
func (sd *RankSeasonDungeon) CmpScore(v1, v2 interface{}) int {
	uv1 := v1.(*RankSeasonDungeon)
	uv2 := v2.(*RankSeasonDungeon)
	if uv1.DungeonId == uv2.DungeonId && uv1.Tm == uv2.Tm {
		return 0
	}
	if uv1.DungeonId > uv2.DungeonId ||
		(uv1.DungeonId == uv2.DungeonId && uv1.Tm < uv2.Tm) {
		return -1
	}
	return 1
}

// 值相同的情况下 uid大的排前面
func (sd *RankSeasonDungeon) CmpKey(v1, v2 interface{}) int {
	uv1 := v1.(*RankSeasonDungeon)
	uv2 := v2.(*RankSeasonDungeon)
	if uv1.Uid == uv2.Uid {
		return 0
	}
	if uv1.Uid > uv2.Uid {
		return -1
	}
	return 1
}

// 初始化一个新的RankSeasonDungeon出来，配合load使用
func (sd *RankSeasonDungeon) NewElement() CommonRankValuer {
	return &RankSeasonDungeon{
		db.RankSeasonDungeon{},
	}
}

func (sd *RankSeasonDungeon) Load(buf []byte) error {
	return proto.Unmarshal(buf, &sd.RankSeasonDungeon)
}

func (sd *RankSeasonDungeon) Save() []byte {
	buf, _ := proto.Marshal(&sd.RankSeasonDungeon)
	return buf
}

func (sd *RankSeasonDungeon) Like() uint32 {
	return 0
}
