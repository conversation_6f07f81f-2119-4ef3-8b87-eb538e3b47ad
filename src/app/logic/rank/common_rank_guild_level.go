package rank

import (
	"app/protos/in/db"
	"app/protos/out/cl"

	"github.com/gogo/protobuf/proto"
)

type GuildLevel struct {
	db.RankGuildLevel
}

func NewGuildLevel(gid uint64, level, exp uint32, tm int64) *GuildLevel {
	return &GuildLevel{
		db.RankGuildLevel{
			Gid:   gid,
			Level: level,
			Exp:   exp,
			Tm:    tm,
		},
	}
}

func (u *GuildLevel) Key() uint64 {
	return u.Gid
}

func (u *GuildLevel) ShowValue() *cl.RankValue {
	return &cl.RankValue{
		Id:     u.Gid,
		Value:  uint64(u.Level),
		Param1: uint64(u.Exp),
		Param2: uint64(u.Tm),
	}
}

// 从大到小的顺序排列
// level相同，按exp由大到小排
// exp相同，按时间由小到大排
func (u *GuildLevel) CmpScore(v1, v2 interface{}) int {
	uv1 := v1.(*GuildLevel)
	uv2 := v2.(*GuildLevel)
	if uv1.Level == uv2.Level && uv1.Exp == uv2.Exp && uv1.Tm == uv2.Tm {
		return 0
	}
	if uv1.Level > uv2.Level ||
		(uv1.Level == uv2.Level && uv1.Exp > uv2.Exp) ||
		(uv1.Level == uv2.Level && uv1.Exp == uv2.Exp && uv1.Tm < uv2.Tm) {
		return -1
	}
	return 1
}

// 值相同的情况下 gid大的排前面
func (u *GuildLevel) CmpKey(v1, v2 interface{}) int {
	uv1 := v1.(*GuildLevel)
	uv2 := v2.(*GuildLevel)
	if uv1.Gid == uv2.Gid {
		return 0
	}
	if uv1.Gid > uv2.Gid {
		return -1
	}
	return 1
}

// 初始化一个新的GuildLevel出来，配合load使用
func (u *GuildLevel) NewElement() CommonRankValuer {
	return &GuildLevel{
		db.RankGuildLevel{},
	}
}

func (u *GuildLevel) Load(buf []byte) error {
	return proto.Unmarshal(buf, &u.RankGuildLevel)
}

func (u *GuildLevel) Save() []byte {
	buf, _ := proto.Marshal(&u.RankGuildLevel)
	return buf
}

func (u *GuildLevel) Like() uint32 {
	return 0
}
