package sync

import "testing"

func Test_Sync(t *testing.T) {
	ms := NewMessageSync(1, 2, 10)
	err := ms.Push(2, 10, "xxxxx")
	if err != nil {
		t.Log(err)
	}
	t.Log(ms)
	err = ms.Push(2, 10, "xxxxx")
	if err != nil {
		t.Log(err)
	}
	t.Log(ms)
	//=====
	err = ms.Push(2, 10, "xxxxx10")
	if err != nil {
		t.Log(err)
	}
	err = ms.Push(2, 11, "xxxxx11")
	if err != nil {
		t.Log(err)
	}
	err = ms.Push(3, 12, "xxxxx12")
	if err != nil {
		t.Log(err)
	}
	err = ms.Push(3, 13, "xxxxx13")
	if err != nil {
		t.Log(err)
	}
	t.Log(ms)
	//======
	if ms.Reuse(1, 2, 4, 11) {
		ret := ms.Get<PERSON>ll(11)
		for _, ss := range ret {
			t.Log(ss)
		}
	} else {
		t.Log("all flush")
	}
	t.Log(ms)
}
