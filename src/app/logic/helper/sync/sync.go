//消息同步
/*
1. cseq -> sseqs
2. cookie：上线时携带的cookie和当前cookie一致
*/
package sync

import (
	"container/list"
	"errors"
	"fmt"
	"strings"
)

var (
	ErrNotFound     = errors.New("not found message")
	ErrSeqException = errors.New("sequence is zero")
)

type csPair struct {
	cseq uint32
	smsg *list.Element //cseq对应的第一个server msg
}

type message struct {
	sseq uint32
	data interface{}
}

type MessageSync struct {
	pairs  *list.List
	smsgs  *list.List
	cookie uint64
	csize  int //最大暂存客户端消息数量
	ssize  int //最大暂存服务器端消息数量

	lastCSeq uint32
	lastSSeq uint32
}

func NewMessageSync(cookie uint64, csize, ssize int) *MessageSync {
	return &MessageSync{
		pairs:  list.New(),
		smsgs:  list.New(),
		cookie: cookie,
		csize:  csize,
		ssize:  ssize,
	}
}

func (m *MessageSync) Cookie() uint64       { return m.cookie }
func (m *MessageSync) MaxClientSeq() uint32 { return m.lastCSeq }
func (m *MessageSync) MaxServerSeq() uint32 { return m.lastSSeq }

// 重连之后使用，检查cookie的一致性
// maxCSeq, maxSSeq: client 缓存中最大的cseq和sseq值
func (m *MessageSync) Reuse(oldCookie, newCookie uint64, maxCSeq, maxSSeq uint32) bool {
	tmp := m.cookie
	m.cookie = newCookie
	if tmp == oldCookie && m.verify(maxCSeq, maxSSeq) {
		return true
	}
	m.reset()
	return false
}

func (m *MessageSync) verify(maxCSeq uint32, maxSSeq uint32) bool {
	front := m.pairs.Front()
	if front == nil {
		return false
	}
	if maxCSeq < m.lastCSeq {
		return false
	}
	if maxSSeq > m.lastSSeq {
		return false
	}
	front = m.smsgs.Front()
	if front == nil {
		return false
	}
	if maxSSeq < front.Value.(*message).sseq {
		return false
	}
	return true
}

func (m *MessageSync) reset() {
	m.pairs.Init()
	m.smsgs.Init()
	m.lastCSeq = 0
	m.lastSSeq = 0
}

func (m *MessageSync) Push(cseq uint32, sseq uint32, smsg interface{}) error {
	var err error
	if cseq == 0 {
		//客户端最小的序列号必须大于零
		return ErrSeqException
	}
	if m.lastCSeq == 0 || (cseq == m.lastCSeq+1 && sseq == m.lastSSeq+1) {
		m.pairs.PushBack(&csPair{
			cseq: cseq,
			smsg: m.smsgs.PushBack(&message{
				sseq: sseq,
				data: smsg,
			}),
		})
	} else if cseq == m.lastCSeq && sseq == m.lastSSeq+1 {
		err = m.append(cseq, sseq, smsg)
	} else {
		err = fmt.Errorf("out of order: cseq(current:%d - last:%d), sseq(current:%d - last:%d)",
			cseq, m.lastCSeq, sseq, m.lastSSeq)
	}
	if err == nil {
		m.lastCSeq = cseq
		m.lastSSeq = sseq
		m.remove()
	} else {
		m.reset()
	}
	return err
}

func (m *MessageSync) append(cseq uint32, sseq uint32, smsg interface{}) error {
	last := m.pairs.Back()
	if last != nil {
		pair := last.Value.(*csPair)
		if pair.cseq == cseq {
			m.smsgs.PushBack(&message{
				sseq: sseq,
				data: smsg,
			})
			return nil
		}
		return fmt.Errorf("inconsistent data: cseq(%d-%d)", pair.cseq, cseq)
	}
	return ErrNotFound
}

func (m *MessageSync) remove() {
	if m.pairs.Len() == 1 {
		//至少保留一条客户端协议和对应的服务器端协议
		if m.smsgs.Len() <= m.ssize {
			//防止出现异常，导致内存OOM
			return
		}
	}
	for m.pairs.Len() > m.csize || m.smsgs.Len() > m.ssize {
		if m.pairs.Len() == 0 {
			m.smsgs.Init()
		} else {
			m.pairs.Remove(m.pairs.Front())
			front := m.pairs.Front()
			if front != nil {
				end := front.Value.(*csPair).smsg
				for start := m.smsgs.Front(); start != end; start = m.smsgs.Front() {
					m.smsgs.Remove(start)
				}
			} else {
				m.smsgs.Init()
			}
		}
	}
}

// Reuse成功之后使用
func (m *MessageSync) GetAll(sseq uint32) (msgs []interface{}) {
	for start := m.smsgs.Front(); start != nil; start = start.Next() {
		msg := start.Value.(*message)
		if msg.sseq >= sseq+1 {
			msgs = append(msgs, msg.data)
		}
	}
	return
}

func (m *MessageSync) String() string {
	var sb strings.Builder
	fmt.Fprintf(&sb, "cookie: %d smsg: %d", m.Cookie(), m.smsgs.Len())
	front := m.pairs.Front()
	if front != nil {
		pair := front.Value.(*csPair)
		fmt.Fprintf(&sb, " front cseq: %d sseq: %d", pair.cseq, pair.smsg.Value.(*message).sseq)
	}
	fmt.Fprintf(&sb, " last cseq: %d sseq: %d", m.MaxClientSeq(), m.MaxServerSeq())
	return sb.String()
}
