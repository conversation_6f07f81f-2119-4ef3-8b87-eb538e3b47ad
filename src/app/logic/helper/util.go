package helper

import (
	l4g "github.com/ivanabc/log4go"
	//"encoding/json"
	"regexp"
	"strings"
	"unicode/utf8"

	//"app/goxml"
	cret "app/protos/out/ret"
	//"gitlab.qdream.com/kit/sea/math/rand"
)

var RegexpNumerical = regexp.MustCompile(`^[0-9]+$`)

// 检查字符串是否是纯数字
func CheckPurelyNumerical(content string) bool {
	return RegexpNumerical.MatchString(content)
}

// 检查客户端输入的字符（汉字，表情符等）
func CheckContent(content string, maxLen uint32) uint32 {
	//对长度总字节进行限制
	if length := uint32(len(content)); length == 0 || length > maxLen {
		return uint32(cret.RET_CONTENT_LENGTH_LIMIT)
	}
	if !utf8.ValidString(content) {
		return uint32(cret.RET_CONTENT_INVALID)
	}
	return uint32(cret.RET_OK)
}

/*
func RetName(ret uint32) string { return cret.RET_name[int32(ret)] }
func RetOK(ret uint32) bool     { return ret == uint32(cret.RET_OK) }

func RandName(rd *rand.Rand) string {
	var name string
	idx := rd.Intn(len( goxml.GetData().NameFirstInfoM.Datas))
	for _, info := range  goxml.GetData().NameFirstInfoM.Datas {
		if idx <= 0 {
			name = info.Name
			break
		}
		idx--
	}
	idx = rd.Intn(len( goxml.GetData().NameSecondInfoM.Datas))
	for _, info := range  goxml.GetData().NameSecondInfoM.Datas {
		if idx <= 0 {
			name += info.Name
			break
		}
		idx--
	}
	return name
}
*/

func ConcatenateString(build *strings.Builder, args ...string) string {
	for _, value := range args {
		build.WriteString(value)
	}
	value := build.String()
	build.Reset()
	return value
}

func CheckBytesLen(data []byte, maxLen int) bool {
	return len(data) <= maxLen
}

func CalcReduceHpByDamage(value uint64, damageRange []uint64, hpRange []uint32) uint32 {
	if len(damageRange) != len(hpRange) {
		l4g.Error("CalcReduceHpByDamage: damageRange len %d and hpRange len %d not equal. ", len(damageRange), len(hpRange))
		return 0
	}

	if len(damageRange) == 0 {
		l4g.Error("CalcReduceHpByDamage: damageRange is empty. ")
		return 0
	}

	maxIndex := len(damageRange) - 1
	// [0,minDamage)
	if value < damageRange[0] {
		return hpRange[0]
	}

	// [maxDamage,+∞)
	if value >= damageRange[maxIndex] {
		return hpRange[maxIndex]
	}

	// [minDamage, maxDamage)
	index := 0
	for i, damage := range damageRange {
		if value >= damage {
			index = i
			continue
		}
		break
	}

	if index+1 > len(damageRange)-1 {
		l4g.Error("CalcReduceHpByDamage: index out of range. index %d", index)
		return 0
	}

	// 伤害所处区间 [index, index + 1]
	minDamage := damageRange[index]
	minReduceHp := hpRange[index]
	maxDamage := damageRange[index+1]
	maxReduceHp := hpRange[index+1]
	return uint32(float64(maxReduceHp-minReduceHp)*float64(value-minDamage)/float64(maxDamage-minDamage) + float64(minReduceHp))
}
