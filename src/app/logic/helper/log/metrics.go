package log

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var logMetrics = struct {
	msgsInChan *prometheus.GaugeVec
}{}

func init() {
	const namespace, sub = "logic", "collect"

	basicLabels := []string{"name"}
	logMetrics.msgsInChan = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: namespace,
		Subsystem: sub,
		Name:      "msgs_in_chan",
		Help:      "Number of msgs in chan",
	}, basicLabels)
}
