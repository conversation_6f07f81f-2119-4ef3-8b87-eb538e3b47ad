package helper

import (
	"app/protos/out/common"

	"gitlab.qdream.com/kit/sea/time"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

// 重置时间在凌晨0点
const resetTime uint32 = 0 //5 * 3600

// 根据类型获取重置时间
// @param tp - 重置类型(1:每日 2:每周一 3:每月)
func GetResetTime(tp uint32, ts int64) uint32 {
	cmpTime := ts - int64(resetTime)
	switch common.RESET_TYPE(tp) {
	case common.RESET_TYPE_RESET_NULL:
		return 0
	case common.RESET_TYPE_DAILY:
		return resetTime + util.DailyZeroByTime(cmpTime)
	case common.RESET_TYPE_WEEKLY:
		return resetTime + util.WeekdayZeroByTime(cmpTime, 1)
	case common.RESET_TYPE_MONTHLY:
		return resetTime + util.MonthlyZeroByTime(cmpTime)
	case common.RESET_TYPE_WEEKLY_FRIDAY:
		return resetTime + util.WeekLastResetTime(cmpTime, 5) //nolint:mnd
	}
	l4g.Errorf("[Helper] reset type illegal:%d", tp)
	return 0
}

// 因为重置时间要求，重写间隔天数方法
func DaysBetweenTimes(ts1, ts2 int64) uint32 {
	resetTime1 := resetTime + util.DailyZeroByTime(ts1-int64(resetTime))
	resetTime2 := resetTime + util.DailyZeroByTime(ts2-int64(resetTime))
	var sub uint32
	if resetTime1 > resetTime2 {
		sub = resetTime1 - resetTime2
	} else {
		sub = resetTime2 - resetTime1
	}
	return sub / util.DaySecs
}

func WeeksBetweenTimes(ts1, ts2 int64) uint32 {
	resetTime1 := resetTime + util.DailyZeroByTime(ts1-int64(resetTime))
	resetTime2 := resetTime + util.DailyZeroByTime(ts2-int64(resetTime))
	var sub uint32
	if resetTime1 > resetTime2 {
		sub = resetTime1 - resetTime2
	} else {
		sub = resetTime2 - resetTime1
	}
	return sub / util.WeekSecs
}

func OpenServerDay2TimeStamp(openTime int64, day uint32) int64 {
	openDailyZero := resetTime + util.DailyZeroByTime(openTime-int64(resetTime))
	if day <= 1 {
		return int64(openDailyZero)
	}
	day--
	return int64(openDailyZero + day*(util.DaySecs))
}

func InSameDay(ts1, ts2 int64) bool {
	return util.InSameDay(ts1-int64(resetTime), ts2-int64(resetTime))
}

// 获取现在是开服第几天，开服当天为第一天
func DaysAfterStartOfService(startServiceTm, ts int64) uint32 {
	if startServiceTm > ts {
		return 0
	}
	days := DaysBetweenTimes(startServiceTm, ts)

	return days + 1
}

// 获取下一分钟的第50秒的时间戳
func GetNextMinuteFiftySecond(now time.Time) int64 {
	nowSecond := now.Second()
	addSecond := int64(60 - nowSecond + 50) //nolint:mnd
	return now.Unix() + addSecond
}

// Weekday 获取指定时间当前周几(星期一作为一周的开始)
func Weekday(ts int64) uint32 {
	ts = ts - int64(resetTime)
	day := uint32(time.Unix(ts, 0).Weekday())
	if day == 0 { //星期日
		day = 7 //nolint:mnd
	}
	return day
}

func WeekdayNoTransform(ts int64) uint32 {
	ts = ts - int64(resetTime)
	day := uint32(time.Unix(ts, 0).Weekday())
	return day
}

// 间隔月数方法
func MonthsBetweenTimes(ts1, ts2 int64) uint32 {
	resetTime1 := resetTime + util.DailyZeroByTime(ts1-int64(resetTime))
	resetTime2 := resetTime + util.DailyZeroByTime(ts2-int64(resetTime))
	var sub uint32
	if resetTime1 > resetTime2 {
		sub = resetTime1 - resetTime2
	} else {
		sub = resetTime2 - resetTime1
	}
	return sub / util.DaySecs
}
