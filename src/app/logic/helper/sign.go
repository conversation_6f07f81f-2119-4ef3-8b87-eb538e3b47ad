package helper

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"net/url"

	l4g "github.com/ivanabc/log4go"
)

func BuildFormSignature(requestForm url.Values, secretKey string) string {
	strParams := requestForm.Encode()

	h := hmac.New(sha256.New, []byte(secretKey))
	_, err := h.Write([]byte(strParams))
	if err != nil {
		l4g.Errorf("write signature failed:%v", err)
		return ""
	}

	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}
