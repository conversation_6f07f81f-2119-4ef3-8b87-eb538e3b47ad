package helper

import (
	//"crypto/md5" //#nosec
	"encoding/base64"
	"encoding/json"

	//"io"
	"net/url"
	"strconv"

	"app/goxml"
	"app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"

	l4g "github.com/ivanabc/log4go"
)

type GameData struct {
	Timestamp int64    `json:"timestamp"` //平台时间戳
	RoleType  uint32   `json:"role_type"` //玩家类型0:未实名 1:已实名
	Age       uint32   `json:"age"`       //玩家年龄
	OpID      uint32   `json:"op_id"`
	AppID     uint32   `json:"app_id"`
	UUID      string   `json:"uuid"`
	DeviceID  string   `json:"device_id"`
	Sign      string   `json:"sign"`
	Channel   uint32   `json:"channel"`
	WmOs      string   `json:"wmos"`     //完美的os字段
	WmExtra   string   `json:"wm_extra"` //
	SqExtra   *SQExtra `json:"sq_extra"`
}

type SQExtra struct {
	Pid int `json:"pid"`
	Gid int `json:"gid"`
}

func ParseToken(token string) (*GameData, error) {
	buf, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return nil, err
	}
	l4g.Debugf("base64 decode: %s", buf)
	data := &GameData{}
	err = json.Unmarshal(buf, data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (g *GameData) Check(appID uint64) uint32 {
	if g.AppID != uint32(appID) {
		l4g.Errorf("[LoginCheck] user[%s] app id(%d) abnormal", g.UUID, appID)
		return uint32(ret.RET_ERROR)
	}
	if g.DeviceID == "" {
		l4g.Errorf("[LoginCheck] user[%s] device id empty", g.UUID)
		return uint32(ret.RET_ERROR)
	}

	//测试环境中不检查时间
	if goxml.GetData().ServerInfoM.GM == 0 {
		exceedTime := goxml.GetData().ServerInfoM.ExceedTime
		diff := g.Timestamp - time.Now().Unix()
		if diff < -1*exceedTime || diff > exceedTime {
			l4g.Errorf("[LoginCheck] user[%s] login timestamp: %d illegal", g.UUID, g.Timestamp)
			return uint32(ret.RET_ERROR)
		}
	}
	/*
		if g.RoleType == 0 {
			l4g.Errorf("[LoginCheck] user[%s] auth real name error", g.UUID)
			return uint32(ret.RET_ERROR)
		}
	*/

	values := url.Values{}
	values.Add("uuid", g.UUID)
	values.Add("timestamp", strconv.FormatUint(uint64(g.Timestamp), 10))
	sign := BuildFormSignature(values, goxml.GetData().ServerInfoM.AppKey)
	if g.Sign != sign {
		l4g.Errorf("[LoginCheck] user[%s] check sign failed, sign: (%s <> %s)", g.UUID, g.Sign, sign)
		return uint32(ret.RET_ERROR)
	}

	return uint32(ret.RET_OK)
}
