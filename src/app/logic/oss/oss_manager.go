package oss

import (
	"app/logic/etcdcache"
	"app/protos/out/common"
	"app/protos/out/ret"
	"crypto/md5"
	"crypto/tls"
	"encoding/binary"
	"fmt"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/logics/oss"
	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/ctx"
	"go.uber.org/atomic"
)

type OSSManager struct {
	actors    []*OssActors
	ctxG      *ctx.Group
	ossConfig *etcdcache.OssConfig
}

type OssActors struct {
	Actors      []*OSSActor
	oss         *oss.OSS
	Index       int
	FailedTimes *atomic.Int32
}

func NewOSSManager(ctxG *ctx.Group, cfg *actor.Config, ossConfig *etcdcache.OssConfig, parallel int) *OSSManager {
	if parallel == 0 {
		parallel = 8
	}
	m := &OSSManager{
		ctxG:      ctxG,
		ossConfig: ossConfig,
	}
	ossList := ossConfig.OssList
	ossNum := len(ossList)
	m.actors = make([]*OssActors, ossNum)
	httpTransport := &http.Transport{
		Proxy:           http.ProxyFromEnvironment,
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second, //拨号等待连接完成的最大时间
			KeepAlive: 30 * time.Second, //nolint:mnd //保持网络连接活跃keep-alive探测间隔时间。
		}).DialContext,
		MaxIdleConns:          parallel * ossNum,
		IdleConnTimeout:       90 * time.Second, //nolint:mnd
		MaxIdleConnsPerHost:   parallel,
		ResponseHeaderTimeout: 2 * time.Second,
	}
	for i := 0; i < ossNum; i++ {
		ossConfig := ossList[i]
		actors := &OssActors{
			Actors:      make([]*OSSActor, parallel),
			FailedTimes: atomic.NewInt32(0),
		}
		oss, err := oss.NewOSS(ossConfig, httpTransport)
		if err != nil {
			panic(err.Error())
		}
		actors.oss = oss
		for j := 0; j < parallel; j++ {
			actors.Actors[j] = NewOSSActor(ctxG.CreateChild(), cfg, ossConfig, m, actors)
		}
		m.actors[i] = actors
	}
	return m
}

func (m *OSSManager) Init() {
	for _, actors := range m.actors {
		for _, actor := range actors.Actors {
			go actor.Run(actor)
		}
	}
}

func (m *OSSManager) AddMessage(cmd uint32, uid uint64, data interface{}, funcID common.FUNCID) bool {
	if len(m.actors) == 0 {
		return false
	}
	actor := m.GetActor(funcID)
	if actor != nil {
		actor.AddMessage(cmd, uid, data)
		return true
	}
	return false
}

func (m *OSSManager) GetActor(funcID common.FUNCID) *OSSActor {
	if index, exist := m.ossConfig.OssMap[funcID]; !exist {
		return nil
	} else {
		actors := m.actors[index]
		// if actors.FailedTimes.Load() >= MaxFTimes {
		// 	return nil
		// }
		actors.Index = (actors.Index + 1) % len(actors.Actors)
		return actors.Actors[actors.Index]
	}
}
func (m *OSSManager) Close() {
	m.ctxG.Stop()
	for _, actors := range m.actors {
		for _, actor := range actors.Actors {
			actor.Close()
		}
	}
	m.ctxG.Wait()
	m.ctxG.Finish()
	l4g.Infof("oss manager closed")
}

func (m *OSSManager) GetActors() []*OssActors {
	return m.actors
}

func (m *OSSManager) GetOssKey(uniqueID uint64, funcID common.FUNCID) string {
	b := make([]byte, 8)
	binary.BigEndian.PutUint64(b, uniqueID)
	result := md5.Sum(b)
	sResult := fmt.Sprintf("%x", result)
	var build strings.Builder
	build.WriteString(time.Now().Format("20060102150405"))
	build.WriteString("/")
	build.WriteString(sResult[:4])
	build.WriteString("-")
	build.WriteString(funcID.String())
	build.WriteString("-")
	build.WriteString(strconv.FormatUint(uniqueID, 10))
	return build.String()
}

func (m *OSSManager) GetOssUrl(key string) (string, ret.RET) {
	var build strings.Builder
	beginIndex := strings.Index(key, "-")
	if beginIndex == -1 {
		return build.String(), ret.RET_ERROR
	}
	endIndex := strings.LastIndex(key, "-")
	if endIndex <= beginIndex {
		return build.String(), ret.RET_ERROR
	}
	funcID := common.FUNCID(common.FUNCID_value[key[beginIndex+1:endIndex]])
	if index, exist := m.ossConfig.OssMap[funcID]; !exist {
		return build.String(), ret.RET_ERROR
	} else {
		build.WriteString(m.ossConfig.OssList[index].Endpoint)
		build.WriteString("/")
		build.WriteString(key)
	}
	return build.String(), ret.RET_OK
}

const MaxFTimes int32 = 10
const MaxRetryTimes = 5
