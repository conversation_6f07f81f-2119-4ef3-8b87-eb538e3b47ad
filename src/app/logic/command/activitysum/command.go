package activitysum

import (
	"app/goxml"
	"app/logic/character"
	aevent "app/logic/event"
	"app/logic/helper"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	"app/logic/command/base"
	"app/protos/out/cl"

	// "app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumGetData), &C2LActivitySumGetDataCommand{}, state)                   //  MSG_C2L_ActivitySumGetData = 28201;
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumLoginReward), &C2LActivitySumLoginRewardCommand{}, state)           //  MSG_C2L_ActivitySumLoginReward = 28203;
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumTaskReward), &C2LActivitySumTaskRewardCommand{}, state)             //  MSG_C2L_ActivitySumTaskReward = 28205;
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumActivePuzzleCell), &C2LActivitySumActivePuzzleCellCommand{}, state) //  MSG_C2L_ActivitySumActivePuzzleCell = 28207;
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumTicketBuy), &C2LActivitySumTicketBuyCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumExchange), &C2LActivitySumExchangeCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumTurnTableSummon), &C2LActivitySumTurnTableSummonCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumTurnTableSelectBuff), &C2LActivitySumTurnTableSelectBuffCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumFeed), &C2LActivitySumFeedCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumMakeGift), &C2LActivitySumMakeGiftCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumShootGameFight), &C2LActivitySumShootGameFight{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumSynthesisGameStart), &C2LActivitySumSynthesisGameStart{}, state)     // 合成小游戏 - 开始游戏
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumSynthesisGameUseItem), &C2LActivitySumSynthesisGameUseItem{}, state) // 合成小游戏 - 使用道具
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumSynthesisGameUpdate), &C2LActivitySumSynthesisGameUpdate{}, state)   // 合成小游戏 - 游戏结束更新数据
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivitySumSynthesisGameBuyItem), &C2LActivitySumSynthesisGameBuyItem{}, state) // 合成小游戏 - 购买道具
}

type C2LActivitySumGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumGetDataCommand) Error(msg *cl.L2C_ActivitySumGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumGetData, msg)
	return false
}

func (c *C2LActivitySumGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumGetData{
		Ret: uint32(cret.RET_OK),
	}

	if len(cmsg.ActivityType) > len(character.ActivitySumFuncManager) {
		l4g.Errorf("user:%d C2L_ActivitySumGetData Client param is to long", c.Msg.UID)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumGetData, smsg)
		return false
	}

	checkRepeat := make(map[uint32]struct{}, len(cmsg.ActivityType))
	for _, aType := range cmsg.ActivityType {
		if _, ok := checkRepeat[aType]; ok {
			l4g.Errorf("user:%d C2L_ActivitySumGetData Client param:%d is repated", c.User.ID(), aType)
			c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumGetData, smsg)
			return false
		}
		checkRepeat[aType] = struct{}{}

		sumFunc, exist := character.ActivitySumFuncManager[aType]
		if !exist {
			continue
		}
		c.User.ActivitySumM().CheckAndReset(aType, sumFunc, c.Srv, false)
		clientData := sumFunc.Flush2Client(c.User.ActivitySumM().GetTypeActivity(aType))
		if clientData != nil {
			smsg.Data = append(smsg.Data, clientData)
		}
	}
	smsg.ActivityType = cmsg.ActivityType
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumLoginRewardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumLoginRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumLoginRewardCommand) Error(msg *cl.L2C_ActivitySumLoginReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumLoginReward, msg)
	return false
}

func (c *C2LActivitySumLoginRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumLoginReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumLoginReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumLoginReward: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ActivitySumLoginReward{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if data == nil {
		l4g.Errorf("user:%d C2L_ActivitySumLoginReward activityType:%d not init",
			c.User.ID(), cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2L_ActivitySumLoginReward activityType:%d actID:%d not match client id:%d",
			c.User.ID(), cmsg.ActivityType, data.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	//主表抽成接口
	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumLoginReward main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumLoginReward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("usaer: %d C2L_ActivitySumLoginReward activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	cLens := len(cmsg.Id)
	if cLens == 0 || cLens > len(goxml.GetData().ActivityStoryLoginInfoM.GetRound(activityInfo.LoginId)) {
		l4g.Errorf("user:%d C2L_ActivitySumLoginReward client id:%v len is error", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_PARAM_LENGTH_LIMIT))
	}

	repeatedM := make(map[uint32]struct{})
	totalReward := make([]*cl.Resource, 0, cLens*2) //nolint:mnd
	for _, loginID := range cmsg.Id {
		_, exist := repeatedM[loginID]
		if exist {
			l4g.Errorf("user: %d C2L_ActivitySumLoginReward client login:%d is repeated", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedM[loginID] = struct{}{}

		loginInfo := goxml.GetData().ActivityStoryLoginInfoM.Index(loginID)
		if loginInfo == nil {
			l4g.Errorf("user: %d C2L_ActivitySumLoginReward client login:%d get data failed", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if loginInfo.LoginId != activityInfo.LoginId {
			l4g.Errorf("user: %d C2L_ActivitySumLoginReward actId:%d login:%d is not same with client LoginInfo id:%d round:%d",
				c.Msg.UID, data.SysId, activityInfo.LoginId, loginInfo.Id, loginInfo.LoginId)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_TURN_TABLE_LOGIN_AWARD_CONDITION_FAILED))
		}

		if !data.CheckLoginReward(loginID) {
			l4g.Errorf("user: %d C2L_ActivitySumLoginReward client login:%d has recv", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}

		if !data.CanReceive(loginInfo.Days) {
			l4g.Errorf("user: %d C2L_ActivitySumLoginReward login:%d check condition failed", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_TURN_TABLE_LOGIN_AWARD_CONDITION_FAILED))
		}

		totalReward = append(totalReward, loginInfo.RewardClRes...)
	}

	smsg.Ret, smsg.Rewards = c.User.Award(c.Srv, totalReward, character.AwardTagMail, uint32(log.SUB_TYPE_ID_ACTIVITY_TURN_TABLE_LOGIN_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivitySumLoginReward award failed error code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	data.SetLoginReward(cmsg.Id)
	smsg.Id = data.GetLoginReward2Client()
	smsg.ActivityId = cmsg.ActivityId
	smsg.ActivityType = cmsg.ActivityType
	c.User.ActivitySumM().SetChange(data)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumLoginReward, smsg)
	c.User.LogActivitySumLoginAward(c.Srv, data.ActivityType, data.SysId, cmsg.Id)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumTaskRewardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumTaskRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumTaskRewardCommand) Error(msg *cl.L2C_ActivitySumTaskReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTaskReward, msg)
	return false
}

func (c *C2LActivitySumTaskRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumTaskReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumTaskReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumTaskReward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumTaskReward{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if data == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTaskReward activityType:%d not init",
			c.User.ID(), cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2L_ActivitySumTaskReward activityType:%d actID:%d not match client id:%d",
			c.User.ID(), cmsg.ActivityType, data.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	//主表抽成接口
	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTaskReward main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumTaskReward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("usaer: %d C2L_ActivitySumTaskReward activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	cLens := len(cmsg.TaskIds)
	if cLens == 0 || cLens > goxml.GetData().ActivityTurntableTaskInfoM.ModuleTaskLen(activityInfo.FunctionId) {
		l4g.Errorf("user:%d C2L_ActivitySumTaskReward client task id:%v len  xml len:%d is error ", c.Msg.UID, cmsg.TaskIds, cLens)
		return c.Error(smsg, uint32(cret.RET_PARAM_LENGTH_LIMIT))
	}

	totalAward := make([]*cl.Resource, 0, len(cmsg.TaskIds))
	repeatedM := make(map[uint32]struct{})
	for _, taskId := range cmsg.TaskIds {
		_, exist := repeatedM[taskId]
		if exist {
			l4g.Errorf("user:%d C2L_ActivitySumTaskReward: repeated task id:%d", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedM[taskId] = struct{}{}

		if data.IsRecviveTaskAward(taskId) {
			l4g.Errorf("user:%d C2L_ActivitySumTaskReward: task id:%d is recv", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		recvTaskInfo := data.GetXmlRecvTask(taskId)
		if recvTaskInfo == nil {
			l4g.Errorf("user:%d C2L_ActivitySumTaskReward task id:%d get xml failed", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if recvTaskInfo.GetModule() != activityInfo.FunctionId {
			l4g.Errorf("user:%d C2L_ActivitySumTaskReward recv task module:%d is not activity func id:%d", c.Msg.UID, recvTaskInfo.GetModule(), activityInfo.FunctionId)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_TASK_MODULE_ERROR))
		}

		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(recvTaskInfo.TypeID())
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d C2L_ActivitySumTaskReward: taskTypeInfo not exist. typeID:%d", c.Msg.UID, recvTaskInfo.TypeID())
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		progress := data.GetTaskProgress(taskTypeInfo, c.User)
		if !c.User.CheckTaskFinish(progress, recvTaskInfo.TypeID(), uint64(recvTaskInfo.TaskValue())) {
			l4g.Errorf("user:%d C2L_ActivityTurnTableRecvTask: task id:%d not finish", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_TASK_PROGRESS_ERROR))
		}

		totalAward = append(totalAward, recvTaskInfo.GetAward()...)
	}
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalAward, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_TASK_AWARD), 0)

	data.SetRecviveTaskAward(cmsg.TaskIds)
	c.User.ActivitySumM().SetChange(data)
	smsg.TaskIds = cmsg.TaskIds
	smsg.ActivityId = cmsg.ActivityId
	smsg.ActivityType = cmsg.ActivityType
	smsg.TaskIds = data.GetRecviveTaskAward2Client()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTaskReward, smsg)
	c.User.LogActivitySumTaskAward(c.Srv, data.ActivityType, data.SysId, cmsg.TaskIds)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumActivePuzzleCellCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumActivePuzzleCellCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumActivePuzzleCellCommand) Error(msg *cl.L2C_ActivitySumActivePuzzleCell, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumActivePuzzleCell, msg)
	return false
}

func (c *C2LActivitySumActivePuzzleCellCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumActivePuzzleCell{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumActivePuzzleCell Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumActivePuzzleCell: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumActivePuzzleCell{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(goxml.ActivitySumPuzzle)

	if data == nil || data.Puzzle == nil {
		l4g.Errorf("user: %d C2L_ActivitySumActivePuzzleCell Puzzle not init", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityType != goxml.ActivitySumPuzzle || cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2L_ActivitySumActivePuzzleCell client msg atype:%d aid:%d server atyp:%d aid::%d", c.Msg.UID,
			cmsg.ActivityType, cmsg.ActivityId, goxml.ActivitySumPuzzle, data.SysId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumActivePuzzleCell main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumActivePuzzleCell: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("usaer: %d C2L_ActivitySumActivePuzzleCell activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	ret, _ := c.User.CheckResourcesSize(activityInfo.TicketClRes, c.Srv)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivitySumActivePuzzleCell check ticket Res failed", c.Msg.UID)
		return c.Error(smsg, ret)
	}

	ret = data.CheckCell(cmsg.X, cmsg.Y)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivitySumActivePuzzleCell failed to checkcell ret:%d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	ret = c.User.Consume(c.Srv, activityInfo.TicketClRes, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_CELL), 0)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivitySumActivePuzzleCell consume failed errorcode:%d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	data.AppendCell(cmsg.X, cmsg.Y)
	c.User.ActivitySumM().SetChange(data)

	ret, award := data.CheckPuzzleFinish()
	if ret == uint32(cret.RET_OK) && len(award) > 0 {
		ret, smsg.Awards = c.User.Award(c.Srv, award, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_PUZZLE_AWARD), 0)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_ActivitySumActivePuzzleCell award failed errorcode:%d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}
	}

	copyId := data.Puzzle.CopyId
	var complete uint32
	flipTimes := uint32(len(data.Puzzle.Cells))
	if len(award) > 0 {
		smsg.OldCopyId = copyId
		smsg.OldPuzzleId = data.Puzzle.PuzzleId
		complete = 1
		ret = data.RefreshPuzzle(c.Srv)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_ActivitySumActivePuzzleCell RefreshPuzzle failed errorcode:%d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}
	}

	c.User.ActivitySumM().SetChange(data)

	smsg.X = cmsg.X
	smsg.Y = cmsg.Y
	smsg.ActivityId = cmsg.ActivityId
	smsg.ActivityType = cmsg.ActivityType
	smsg.Puzzle = character.FlushPuzzle2Client(data).ActivityPuzzle

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumActivePuzzleCell, smsg)
	c.User.LogActivitySumPuzzleCell(c.Srv, data.ActivityType, data.SysId, copyId, complete, flipTimes)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumTicketBuyCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumTicketBuyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumTicketBuyCommand) Error(msg *cl.L2C_ActivitySumTicketBuy, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTicketBuy, msg)
	return false
}

func (c *C2LActivitySumTicketBuyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumTicketBuy{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumTicketBuy Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumTicketBuy: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ActivitySumTicketBuy{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if data == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTicketBuy activityType:%d not init",
			c.User.ID(), cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2L_ActivitySumTicketBuy activityType:%d actID:%d not match client id:%d",
			c.User.ID(), cmsg.ActivityType, data.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTicketBuy main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumTicketBuy: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("usaer: %d C2L_ActivitySumTicketBuy activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	passDay := util.DaysBetweenTimes(now, activityInfo.OpenTime) + 1
	if passDay < activityInfo.UnlockDay {
		l4g.Errorf("user:%d C2L_ActivitySumTicketBuy passDay:%d buy ticket chek failed", c.Msg.UID, passDay)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	ticketRes := make([]*cl.Resource, 0, len(activityInfo.TicketClRes))
	for _, v := range activityInfo.TicketClRes {
		cloneRes := v.Clone()
		cloneRes.Count = cmsg.BuyCount
		ticketRes = append(ticketRes, cloneRes)
	}

	var numSummary *cl.NumInfo
	var costs []*cl.Resource

	smsg.Ret, costs, numSummary = c.User.CheckNumByType(activityInfo.BuyID, cmsg.BuyCount, c.Srv)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivitySumTicketBuy: receive award failed. count not enough", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}
	if len(costs) > 0 {
		smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, ticketRes, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_BUY_TICKET), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_ActivitySumTicketBuy: receive Trade. trade failed ret:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	} else {
		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, ticketRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_BUY_TICKET), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_ActivitySumTicketBuy: receive award. award failed ret:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	}

	if numSummary != nil {
		c.User.AddNumByType(activityInfo.BuyID, numSummary)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTicketBuy, smsg)
	c.User.LogAddPurchaseNum(c.Srv, activityInfo.BuyID, cmsg.BuyCount, uint32(common.PURCHASE_TYPE_PT_NORMAL))

	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumExchangeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumExchangeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumExchangeCommand) Error(msg *cl.L2C_ActivitySumExchange, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumExchange, msg)
	return false
}

func (c *C2LActivitySumExchangeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumExchange{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumExchange Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumExchange: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ActivitySumExchange{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if data == nil {
		l4g.Errorf("user:%d C2L_ActivitySumExchange activityType:%d not init",
			c.User.ID(), cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2L_ActivitySumExchange activityType:%d actID:%d not match client id:%d",
			c.User.ID(), cmsg.ActivityType, data.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumExchange main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumExchange: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("usaer: %d C2L_ActivitySumExchange activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	shopInfo := goxml.GetData().ActivityStoryShopInfoM.Index(cmsg.ExchangeId)
	if shopInfo == nil {
		l4g.Errorf("user: %d C2L_ActivitySumExchange check shop id:%d failed", c.Msg.UID, cmsg.ExchangeId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if activityInfo.StoryShop != shopInfo.ShopId {
		l4g.Errorf("user: %d C2L_ActivitySumExchange: cmsg id:%d is error", c.Msg.UID, cmsg.ExchangeId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if shopInfo.UnlockDay > 0 && helper.DaysBetweenTimes(activityInfo.OpenTime, now)+1 < shopInfo.UnlockDay {
		l4g.Errorf("user: %d C2L_ActivitySumExchange exchange:%d open day check failed", c.Msg.UID, cmsg.ExchangeId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if data.Exchange == nil {
		l4g.Errorf("user:%d C2L_ActivitySumExchange exchange data is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	exchange := data.GetExchange(cmsg.ExchangeId)
	if exchange == nil {
		exchange = &cl.ActivityExchange{
			Id: cmsg.ExchangeId,
		}
	}

	if exchange.Count+cmsg.ExchangeCount > shopInfo.BuyLimitCount {
		l4g.Errorf("user:%d C2L_ActivitySumExchange user count:%d buy count :%d limit count:%d ", c.Msg.UID, exchange.Count, cmsg.ExchangeCount, shopInfo.BuyLimitCount)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	costs := shopInfo.GetBuyCost(goxml.GetData(), exchange.Count, cmsg.ExchangeCount)
	awards := goxml.MakeRes(shopInfo.RewardClRes, cmsg.ExchangeCount)

	smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, awards,
		uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_EXCHANGE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivitySumExchange: trade failed. ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	exchange.Count += cmsg.ExchangeCount
	data.SetExchange(exchange)
	c.User.ActivitySumM().SetChange(data)

	smsg.Exchange = data.DeepCopyExchange()
	smsg.ActivityId = cmsg.ActivityId
	smsg.ActivityType = cmsg.ActivityType
	smsg.ExchangeId = cmsg.ExchangeId
	smsg.ExchangeCount = cmsg.ExchangeCount
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumExchange, smsg)
	c.User.LogActivitySumExchangeAward(c.Srv, cmsg.ExchangeId, data.SysId, data.ActivityType, cmsg.ExchangeCount)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumTurnTableSummonCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumTurnTableSummonCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumTurnTableSummonCommand) Error(msg *cl.L2C_ActivitySumTurnTableSummon, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTurnTableSummon, msg)
	return false
}

//nolint:funlen
func (c *C2LActivitySumTurnTableSummonCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumTurnTableSummon{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumTurnTableSummon Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumTurnTableSummon: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumTurnTableSummon{
		Ret: uint32(cret.RET_OK),
	}
	l4g.Debugf("user: %d C2L_ActivitySumTurnTableSummon: cmsg:%s", c.Msg.UID, cmsg)

	data := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if data == nil || data.TurnTable == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSummon activityType:%d not init",
			c.User.ID(), cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != data.SysId || cmsg.ActivityType != goxml.ActivitySumTurnTable {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSummon activityType:%d actID:%d not match client id:%d",
			c.User.ID(), cmsg.ActivityType, data.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSummon main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumTurnTableSummon: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("usaer: %d C2L_ActivitySumExchange activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	ret, cost := c.User.CheckResourcesSize(activityInfo.TicketClRes, c.Srv)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSummon check ticket is failed", c.Msg.UID)
		return c.Error(smsg, uint32(ret))
	}

	var buffInfo *goxml.ActivityTurntableBuffInfo
	if cmsg.Buff > 0 {
		buffInfo = goxml.GetData().ActivityTurntableBuffInfoM.Index(cmsg.Buff)
		if buffInfo == nil {
			l4g.Errorf("user:%d C2L_ActivitySumTurnTableSummon buff is nil buff:%d", c.Msg.UID, cmsg.Buff)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		if data.TurnTable.BuffIds[cmsg.Buff] < 1 {
			l4g.Errorf("user:%d C2L_ActivitySumTurnTableSummon buff:%d check buff is less than 1", c.Msg.UID, cmsg.Buff)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_TURN_TABLE_BUFF_NOT_ENOUGH))
		}
	}

	var buffEffect *BuffEffect
	if buffInfo != nil {
		buffEffect = c.BuffEffect(buffInfo, c.Srv.Rand())
	}

	summonCount := uint32(1)
	if buffEffect != nil {
		summonCount += buffEffect.Guarantee
	}
	logSummonCount := data.GuaranteeCount() + summonCount

	var fixCount uint32
	summonParam, exist := goxml.GetData().ActivityTurntableRewardShowInfoM.SummonParam[data.SysId]
	if exist {
		fixCount = summonParam.FixCount
	}

	var info *goxml.ActivityTurntableRewardShowInfo
	notFix := logSummonCount < fixCount
	disposableFull := len(data.GetDisposableM()) >= goxml.GetData().ActivityTurntableRewardShowInfoM.GetRoundDisposableCount(data.SysId)
	if buffEffect != nil && buffEffect.MustSummonInfo != nil && (notFix || disposableFull) {
		info = buffEffect.MustSummonInfo
	} else {
		info = goxml.GetData().ActivityTurntableRewardShowInfoM.Summon(c.User.ID(), c.Srv.Rand(), data.SysId, data.GetDisposableM(), logSummonCount)
		if buffEffect != nil && buffEffect.MustSummonInfo != nil {
			buffEffect.CanUse = false
		}
	}

	if info == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSummon summon nothing", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	totalReward := info.RewardClRes
	if buffEffect != nil && len(buffEffect.AddResource) > 0 {
		totalReward = append(totalReward, buffEffect.AddResource...)
	}
	if buffEffect != nil && buffEffect.AwardMulti > 1 {
		if info.IsDisposable == goxml.TurnTableDisposable {
			buffEffect.CanUse = false
		} else {
			for _, v := range totalReward {
				v.Count *= buffEffect.AwardMulti
			}
		}
	}
	smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, cost, totalReward, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_TURN_TABLE_SUMMON), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivitySumTurnTableSummon: trade error", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}
	data.AddGuaranteeCount(summonCount, info.IsDisposable)
	if info.IsDisposable == goxml.TurnTableDisposable {
		data.AppendBigAwardID(info.Id)
	}
	if buffEffect != nil && buffEffect.CanUse {
		data.ReduceBuff(cmsg.Buff)
	}
	c.User.ActivitySumM().SetChange(data)

	smsg.Buff = cmsg.Buff
	smsg.BuffIds = data.FlushBuff()
	smsg.GroupId = info.Id
	smsg.BigAwardId = data.FlushBigAward()
	smsg.GuaranteeCount = data.GuaranteeCount()
	smsg.ActivityType = cmsg.ActivityType
	smsg.ActivityId = cmsg.ActivityId
	c.User.FireCommonEvent(c.Srv.EventM(), aevent.IeActivityTurnTableSummon, 1)
	c.User.LogActivitySumTurnTableSummon(c.Srv, goxml.ActivitySumTurnTable, data.SysId, info.Id, cmsg.Buff, logSummonCount, info.IsDisposable, smsg.Awards)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTurnTableSummon, smsg)

	return c.ResultOK(smsg.Ret)
}

type BuffEffect struct {
	AddResource    []*cl.Resource
	AwardMulti     uint32
	Guarantee      uint32
	MustSummonInfo *goxml.ActivityTurntableRewardShowInfo
	CanUse         bool
}

func (c *C2LActivitySumTurnTableSummonCommand) BuffEffect(info *goxml.ActivityTurntableBuffInfo, rd *rand.Rand) *BuffEffect {
	ret := &BuffEffect{}
	switch info.EffectType {
	case goxml.TurnTableEffectTargetId:
		showInfo := goxml.GetData().ActivityTurntableRewardShowInfoM.Index(info.Count)
		if showInfo != nil {
			ret.MustSummonInfo = showInfo.Clone()
		}
	case goxml.TurnTableEffectDropGroupRandom:
		res, success := c.User.Drop().DoDrop(rd, info.DropId)
		if success {
			for _, v := range res {
				ret.AddResource = append(ret.AddResource, v.Clone())
			}
		}
	case goxml.TurnTableEffectMulti:
		ret.AwardMulti = info.Count
	case goxml.TurnTableEffectAward:
		for _, v := range info.ClRes {
			ret.AddResource = append(ret.AddResource, v.Clone())
		}
	case goxml.TurnTableEffectAddGuarantee:
		ret.Guarantee = info.Count
	}
	ret.CanUse = true
	return ret
}

type C2LActivitySumTurnTableSelectBuffCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumTurnTableSelectBuffCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumTurnTableSelectBuffCommand) Error(msg *cl.L2C_ActivitySumTurnTableSelectBuff, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTurnTableSelectBuff, msg)
	return false
}

func (c *C2LActivitySumTurnTableSelectBuffCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumTurnTableSelectBuff{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumTurnTableSelectBuff Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumTurnTableSelectBuff: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumTurnTableSelectBuff{
		Ret: uint32(cret.RET_OK),
	}
	l4g.Debugf("user: %d C2L_ActivitySumTurnTableSelectBuff: cmsg:%s", c.Msg.UID, cmsg)

	data := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if data == nil || data.TurnTable == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSelectBuff activityType:%d not init",
			c.User.ID(), cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != data.SysId || cmsg.ActivityType != goxml.ActivitySumTurnTable {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSelectBuff activityType:%d actID:%d not match client id:%d",
			c.User.ID(), cmsg.ActivityType, data.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSelectBuff main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumTurnTableSelectBuff: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("usaer: %d C2L_ActivitySumTurnTableSelectBuff activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	buffInfo := goxml.GetData().ActivityTurntableBuffInfoM.Index(cmsg.BuffId)
	if buffInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSelectBuff buff id:%d is not exist", c.Msg.UID, cmsg.BuffId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	oldBuff := data.FlushSelectBuff()
	if !data.IsSelectBuff(cmsg.BuffId) {
		l4g.Errorf("user:%d C2L_ActivitySumTurnTableSelectBuff buff id:%d is not select buff", c.Msg.UID, cmsg.BuffId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	c.User.ActivitySumM().SetChange(data)

	smsg.BuffId = cmsg.BuffId
	smsg.BuffIds = data.AfterSelectBuff(cmsg.BuffId)
	smsg.ActivityType = cmsg.ActivityType
	smsg.ActivityId = cmsg.ActivityId

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumTurnTableSelectBuff, smsg)
	c.User.LogActivitySumTurnTableSelectBuff(c.Srv, goxml.ActivitySumTurnTable, cmsg.ActivityId, cmsg.BuffId, oldBuff)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumFeedCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumFeedCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumFeedCommand) Error(msg *cl.L2C_ActivitySumFeed, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumFeed, msg)
	return false
}

func (c *C2LActivitySumFeedCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumFeed{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumFeed Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumFeed: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumFeed{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(goxml.ActivitySumFeed)

	if data == nil || data.Feed == nil {
		l4g.Errorf("user: %d C2L_ActivitySumFeed Puzzle not init", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityType != goxml.ActivitySumFeed || cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2L_ActivitySumFeed client msg atype:%d aid:%d server atyp:%d aid:%d",
			c.User.ID(), cmsg.ActivityType, cmsg.ActivityId, goxml.ActivitySumFeed, data.SysId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumFeed main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumFeed: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("user: %d C2L_ActivitySumFeed activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	giftInfo := goxml.GetData().ActivityFeedingGiftsInfoM.Index(activityInfo.ID, cmsg.GiftId)
	if giftInfo == nil || cmsg.Count == 0 {
		l4g.Errorf("user: %d C2L_ActivitySumFeed get actid:%d gift info id:%d check failed", c.Msg.UID, data.SysId, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	cost := []*cl.Resource{
		{
			Type:  giftInfo.ItemType,
			Value: giftInfo.ItemValue,
			Count: cmsg.Count,
		},
	}

	ret := c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_FEED), 0)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivitySumFeed consume failed ret:%d", c.Msg.UID, ret)
		return c.Error(smsg, uint32(ret))
	}

	awards, addExp := data.FeedGifts(giftInfo, cmsg.Count)
	c.User.ActivitySumM().SetChange(data)

	if len(awards) > 0 {
		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_FEED), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_ActivitySumFeed consume Award ret:%d", c.Msg.UID, ret)
			return c.Error(smsg, uint32(ret))
		}
	}

	smsg.ActivityId = cmsg.ActivityId
	smsg.ActivityType = cmsg.ActivityType
	smsg.GiftId = cmsg.GiftId
	smsg.Count = cmsg.Count
	smsg.FeedLevel = data.Feed.FeedLevel
	smsg.FeedExp = data.Feed.FeedExp
	smsg.FeedCond = data.Feed.FeedCond
	smsg.FeedCount = data.Feed.FeedCount
	smsg.AddExp = addExp

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumFeed, smsg)
	c.User.LogActivitySumFeed(c.Srv, goxml.ActivitySumFeed, data.SysId, cmsg.GiftId, cmsg.Count, addExp,
		smsg.FeedCond, smsg.FeedLevel, smsg.FeedExp, smsg.FeedCond)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumMakeGiftCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumMakeGiftCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumMakeGiftCommand) Error(msg *cl.L2C_ActivitySumMakeGift, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumMakeGift, msg)
	return false
}

func (c *C2LActivitySumMakeGiftCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumMakeGift{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivitySumMakeGift Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivitySumMakeGift: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumMakeGift{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(goxml.ActivitySumFeed)

	if data == nil || data.Feed == nil {
		l4g.Errorf("user: %d C2L_ActivitySumMakeGift Puzzle not init", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityType != goxml.ActivitySumFeed || cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2L_ActivitySumMakeGift client msg atype:%d aid:%d server atyp:%d aid:%d",
			c.User.ID(), cmsg.ActivityType, cmsg.ActivityId, goxml.ActivitySumFeed, data.SysId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2L_ActivitySumMakeGift main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2L_ActivitySumMakeGift: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("user: %d C2L_ActivitySumMakeGift activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	giftInfo := goxml.GetData().ActivityFeedingGiftsInfoM.Index(activityInfo.ID, cmsg.GiftId)
	if giftInfo == nil || cmsg.Count == 0 {
		l4g.Errorf("user: %d C2L_ActivitySumMakeGift get actid:%d gift info id:%d check failed", c.Msg.UID, data.SysId, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	cost := giftInfo.GetMakeCost(cmsg.Count)
	awards := []*cl.Resource{
		{
			Type:  giftInfo.ItemType,
			Value: giftInfo.ItemValue,
			Count: cmsg.Count,
		},
	}

	smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, cost, awards, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_MAKE_GIFT), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivitySumMakeGift make gift failed code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.ActivityId = cmsg.ActivityId
	smsg.ActivityType = cmsg.ActivityType
	smsg.GiftId = cmsg.GiftId
	smsg.Count = cmsg.Count
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumMakeGift, smsg)
	c.User.LogActivitySumMakeGift(c.Srv, goxml.ActivitySumFeed, data.SysId, cmsg.GiftId, cmsg.Count)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumShootGameFight struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumShootGameFight) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumShootGameFight) Error(msg *cl.L2C_ActivitySumShootGameFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumShootGameFight, msg)
	return false
}

func (c *C2LActivitySumShootGameFight) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumShootGameFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LActivitySumShootGameFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2LActivitySumShootGameFight: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumShootGameFight{
		Ret: uint32(cret.RET_OK),
	}

	data := c.User.ActivitySumM().GetTypeActivity(goxml.ActivitySumShoot)

	if data == nil || data.Shoot == nil {
		l4g.Errorf("user: %d C2LActivitySumShootGameFight Puzzle not init", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityType != goxml.ActivitySumShoot || cmsg.ActivityId != data.SysId {
		l4g.Errorf("user:%d C2LActivitySumShootGameFight client msg atype:%d aid:%d server atyp:%d aid:%d",
			c.User.ID(), cmsg.ActivityType, cmsg.ActivityId, goxml.ActivitySumShoot, data.SysId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(data.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2LActivitySumShootGameFight main info id:%d check failed", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LActivitySumShootGameFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("user: %d C2LActivitySumShootGameFight activity id:%d  not open", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	nextStageInfo := data.GetNextStageInfo()
	if nextStageInfo == nil || cmsg.StageId != nextStageInfo.StageId {
		l4g.Errorf("user: %d C2LActivitySumShootGameFight id:%d not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUB_SHOT_STAGE_CHECK_FAILED))
	}

	if util.DaysBetweenTimes(activityInfo.OpenTime, now) < nextStageInfo.OpenTime {
		l4g.Errorf("user:%d C2LActivitySumShootGameFight activity id:%d stage id:%d not open", c.Msg.UID, data.SysId, nextStageInfo.OpenTime)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUB_SHOT_OPEN_DAY_CHECK_FAILED))
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, nextStageInfo.RewardClRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_SHOOT), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2LActivitySumShootGameFight award failed ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, uint32(smsg.Ret))
	}

	data.SetStageID(nextStageInfo)
	c.User.ActivitySumM().SetChange(data)

	smsg.StageId = cmsg.StageId
	smsg.ActivityType = cmsg.ActivityType
	smsg.ActivityId = cmsg.ActivityId
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumShootGameFight, smsg)
	c.User.LogActivitySumShoot(c.Srv, goxml.ActivitySumShoot, data.SysId, cmsg.StageId)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumSynthesisGameStart struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumSynthesisGameStart) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumSynthesisGameStart) Error(msg *cl.L2C_ActivitySumSynthesisGameStart, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumSynthesisGameStart, msg)
	return false
}

func (c *C2LActivitySumSynthesisGameStart) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumSynthesisGameStart{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LActivitySumSynthesisGameStart Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2LActivitySumSynthesisGameStart: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumSynthesisGameStart{
		Ret:          uint32(cret.RET_OK),
		ActivityType: cmsg.ActivityType,
		ActivityId:   cmsg.ActivityId,
	}

	activity := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if activity == nil || activity.Synthesis == nil {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameStart: activity no synthesis game. activityType %d", c.Msg.UID, cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != activity.SysId {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameStart activityType:%d actID:%d not match client id:%d", c.User.ID(), cmsg.ActivityType, activity.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(activity.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameStart: no activityInfo. id %d", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameStart: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameStart activity id:%d  not open", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	// 消耗次数
	if !activity.SynthesisGetGameOpen() {
		ret, costs, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_SYNTHESIS_GAME_CHALLENGE_COUNT), 1, c.Srv)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2LActivitySumSynthesisGameStart: count not enough", c.Msg.UID)
			return c.Error(smsg, ret)
		}
		c.User.AddNumByType(uint32(common.PURCHASEID_SYNTHESIS_GAME_CHALLENGE_COUNT), numSummary)
		if len(costs) > 0 {
			smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_SYNTHESIS_GAME_USE_TICKET), 0)
			if smsg.Ret != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2LActivitySumSynthesisGameStart: consume failed. ret:%d", c.Msg.UID, smsg.Ret)
				return c.Error(smsg, smsg.Ret)
			}
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumShootGameFight, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumSynthesisGameUseItem struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumSynthesisGameUseItem) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumSynthesisGameUseItem) Error(msg *cl.L2C_ActivitySumSynthesisGameUseItem, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumSynthesisGameUseItem, msg)
	return false
}

func (c *C2LActivitySumSynthesisGameUseItem) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumSynthesisGameUseItem{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LActivitySumSynthesisGameUseItem Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2LActivitySumSynthesisGameUseItem: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumSynthesisGameUseItem{
		Ret:          uint32(cret.RET_OK),
		ActivityType: cmsg.ActivityType,
		ActivityId:   cmsg.ActivityId,
		ItemType:     cmsg.ItemType,
	}

	activity := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if activity == nil || activity.Synthesis == nil {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUseItem: activity no synthesis game. activityType %d", c.Msg.UID, cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != activity.SysId {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameUseItem activityType:%d actID:%d not match client id:%d", c.User.ID(), cmsg.ActivityType, activity.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(activity.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameUseItem: no activityInfo. id %d", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUseItem: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUseItem activity id:%d  not open", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	var itemId uint32
	switch cmsg.ItemType {
	case uint32(common.SYNTHESIS_GAME_ITEM_TYPE_SGIT_DELETE): // 兽笼
		if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("OBJECT_DELETE_ITEM"); info != nil {
			itemId = info.Value
		}
	case uint32(common.SYNTHESIS_GAME_ITEM_TYPE_SGIT_UP): // 兽粮
		if info := goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey("OBJECT_UP_ITEM"); info != nil {
			itemId = info.Value
		}
	default:
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUseItem: item type error. type %d", c.Msg.UID, cmsg.ItemType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	itemInfo := goxml.GetData().ItemInfoM.GetRecordById(itemId)
	if itemInfo == nil {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUseItem: no itemInfo. id %d", c.Msg.UID, cmsg.ItemType)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	costs := goxml.GenSimpleResource(uint32(common.RESOURCE_ITEM), itemId, 1)
	smsg.Ret = c.User.Consume(c.Srv, []*cl.Resource{costs}, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_SUM_SYNTHESIS_GAME_USE_ITEM), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUseItem: consume failed. ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumSynthesisGameUseItem, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LActivitySumSynthesisGameUpdate struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumSynthesisGameUpdate) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumSynthesisGameUpdate) Error(msg *cl.L2C_ActivitySumSynthesisGameUpdate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumSynthesisGameUpdate, msg)
	return false
}

func (c *C2LActivitySumSynthesisGameUpdate) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumSynthesisGameUpdate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LActivitySumSynthesisGameUpdate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2LActivitySumSynthesisGameUpdate: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumSynthesisGameUpdate{
		Ret:          uint32(cret.RET_OK),
		ActivityType: cmsg.ActivityType,
		ActivityId:   cmsg.ActivityId,
	}

	activity := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if activity == nil || activity.Synthesis == nil {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUpdate: activity no synthesis game. activityType %d", c.Msg.UID, cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != activity.SysId {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameUpdate activityType:%d actID:%d not match client id:%d", c.User.ID(), cmsg.ActivityType, activity.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(activity.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameUpdate: no activityInfo. id %d", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUpdate: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUpdate activity id:%d  not open", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameUpdate: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if data := cmsg.Data; data != nil {
		// 最大积分
		if data.Score > activity.SynthesisGetMaxScore() {
			activity.SynthesisSetMaxScore(data.Score)
			c.User.FireCommonEvent(c.Srv.EventM(), aevent.AeActivitySumSynthesisGameMaxScoreToX, uint64(data.Score))
		}

		// 累计积分
		if data.Score > 0 {
			activity.SynthesisAddTotalScore(data.Score)
			c.User.FireCommonEvent(c.Srv.EventM(), aevent.AeActivitySumSynthesisGameTotalScoreToX, uint64(activity.SynthesisGetTotalScore()))
		}

		// 最高合成等级
		if data.MaxSynthesisLevel > activity.SynthesisGetMaxLevel() {
			activity.SynthesisSetMaxLevel(data.MaxSynthesisLevel)
			c.User.FireCommonEvent(c.Srv.EventM(), aevent.AeActivitySumSynthesisGameMaxLevelToX, uint64(data.MaxSynthesisLevel))
		}
	}

	// 游戏结束
	activity.SynthesisSetGameOpen(false)
	c.User.ActivitySumM().SetChange(activity)

	smsg.TotalScore = activity.SynthesisGetTotalScore()
	smsg.MaxScore = activity.SynthesisGetMaxScore()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumSynthesisGameUpdate, smsg)
	return c.ResultOK(smsg.Ret)
}

// 购买道具
type C2LActivitySumSynthesisGameBuyItem struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivitySumSynthesisGameBuyItem) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivitySumSynthesisGameBuyItem) Error(msg *cl.L2C_ActivitySumSynthesisGameBuyItem, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumSynthesisGameBuyItem, msg)
	return false
}

func (c *C2LActivitySumSynthesisGameBuyItem) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivitySumSynthesisGameBuyItem{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LActivitySumSynthesisGameBuyItem Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2LActivitySumSynthesisGameBuyItem: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivitySumSynthesisGameBuyItem{
		Ret:          uint32(cret.RET_OK),
		ActivityType: cmsg.ActivityType,
		ActivityId:   cmsg.ActivityId,
		ItemType:     cmsg.ItemType,
	}

	activity := c.User.ActivitySumM().GetTypeActivity(cmsg.ActivityType)
	if activity == nil || activity.Synthesis == nil {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameBuyItem: activity no synthesis game. activityType %d", c.Msg.UID, cmsg.ActivityType)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_INIT))
	}

	if cmsg.ActivityId != activity.SysId {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameBuyItem activityType:%d actID:%d not match client id:%d", c.User.ID(), cmsg.ActivityType, activity.SysId, cmsg.ActivityId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	activityInfo := goxml.GetData().ActivityTurntableInfoM.Index(activity.SysId)
	if activityInfo == nil {
		l4g.Errorf("user:%d C2LActivitySumSynthesisGameBuyItem: no activityInfo. id %d", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameBuyItem: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !activityInfo.IsOpen(now) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameBuyItem activity id:%d  not open", c.Msg.UID, activity.SysId)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_SUM_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(activityInfo.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LActivitySumSynthesisGameBuyItem: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 获取购买道具的消耗
	ok, price := goxml.GetData().BuyPriceInfoM.GetPrice(goxml.GetData().SynthesisGameConfigInfoM.GetRecordByKey(), buyCount, cmsg.Num)
	if !ok || price == 0 {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes: GetPrice failed. purchasedNum:%d, num:%d",
			c.User.ID(), buyCount, cmsg.Num)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivitySumSynthesisGameBuyItem, smsg)
	return c.ResultOK(smsg.Ret)
}
