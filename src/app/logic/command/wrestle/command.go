package wrestle

import (
	"app/goxml"
	"app/logic/activity"
	ab "app/logic/activity/base"
	awrestle "app/logic/activity/wrestle"
	"app/logic/character"
	"app/logic/command"
	"app/logic/helper"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/time"

	"app/logic/command/base"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleInfo), &C2LWrestleInfoCommand{}, state)                   // 获取基本信息
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleMapInfo), &C2LWrestleMapInfoCommand{}, state)             // 获取地图信息
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleTopUserList), &C2LWrestleTopUserListCommand{}, state)     // 获取头部玩家
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleRoomInfo), &C2LWrestleRoomInfoCommand{}, state)           // 获取房间信息
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleFightLog), &C2LWrestleFightLogCommand{}, state)           // 获取历史记录
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleFight), &C2LWrestleFightCommand{}, state)                 // 战斗
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleLike), &C2LWrestleLikeCommand{}, state)                   // 点赞
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleRankList), &C2LWrestleRankListCommand{}, state)           // 获取排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleRecvTaskAward), &C2LWrestleRecvTaskAwardCommand{}, state) // 领取任务奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleHallOfFame), &C2LWrestleHallOfFameCommand{}, state)       // 获取荣誉殿堂
	cmds.Register(uint32(cl.ID_MSG_C2L_WrestleChangeRoom), &C2LWrestleChangeRoomCommand{}, state)       // 更换房间
}

// @param day uint32 开服第几天
// @param now int64 当前时间戳
func wrestleReqLimit(uid uint64, day uint32, now int64) uint32 {

	if goxml.IsWrestleDailyResetting(now) {
		l4g.Errorf("user: %d : wrestle daily resetting.", uid)
		return uint32(cret.RET_WRESTLE_RESETTING)
	}

	if !goxml.IsWrestleActive(goxml.GetData(), day, now) {
		l4g.Errorf("user: %d wrestle function not open.", uid)
		return uint32(cret.RET_WRESTLE_NOT_OPEN)
	}

	return uint32(cret.RET_OK)
}

type C2LWrestleInfoCommand struct {
	base.UserCommand
}

func (c *C2LWrestleInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleInfoCommand) Error(msg *cl.L2C_WrestleInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleInfo, msg)
	return false
}

func (c *C2LWrestleInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleInfo Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleInfo: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleInfo{
		Ret: uint32(cret.RET_OK),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE)) {
	//	l4g.Errorf("user: %d C2L_WrestleInfo: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	if !goxml.IsWrestleOpen(goxml.GetData(), serverDay) {
		l4g.Errorf("user: %d C2L_WrestleInfo: wrestle function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_NOT_OPEN))
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleInfo failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser != nil {
		if !goxml.IsWrestleActive(goxml.GetData(), serverDay, now) {
			// 处于赛季的准备期
			smsg.Level = wrestleUser.GetPrevSeasonLevelAfterReset()
			smsg.TotalRank = wrestleUser.GetPrevSeasonTotalRank()
			smsg.SeasonMaxLevel = 0
		} else {
			if goxml.IsWrestleDailyResetting(now) {
				if wrestleM.IsNeedAward(c.Srv, now) {
					smsg.Level = wrestleUser.GetLevel()
				} else {
					if goxml.GetWrestleSeasonResetTime(goxml.GetData(), now) < now {
						smsg.Level = wrestleUser.GetPrevSeasonLevelBeforeReset()
					} else {
						smsg.Level = wrestleUser.GetLastLevel()
					}
				}
			} else {
				smsg.Level = wrestleUser.GetLevel()
			}
			smsg.TotalRank = wrestleUser.GetTotalRank()
			smsg.SeasonMaxLevel = wrestleUser.GetSeasonMaxLevel()
		}
		smsg.Rank = wrestleUser.GetRank()
		smsg.ReceivedLevelAward = wrestleUser.GetReceivedLevelAward()
		smsg.BeDefeated = wrestleUser.BeDefeated()
		smsg.PreSeasonLevel = wrestleUser.GetPrevSeasonLevelAfterReset()
		smsg.PreSeasonRank = wrestleUser.GetPrevSeasonTotalRank()
		smsg.EnterTm = wrestleUser.GetEnterTime()
	} // 等于nil传前端默认值，按未报名处理

	smsg.SeasonStatus = wrestleM.GetSeasonStatus(c.Srv, now)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleInfo, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWrestleMapInfoCommand struct {
	base.UserCommand
}

func (c *C2LWrestleMapInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleMapInfoCommand) Error(msg *cl.L2C_WrestleMapInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleMapInfo, msg)
	return false
}

func (c *C2LWrestleMapInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleMapInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleMapInfo Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleMapInfo: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleMapInfo{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleMapInfo: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	limitRet := wrestleReqLimit(c.Msg.UID, serverDay, now)
	if limitRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleMapInfo: wrestle req limit. ret:%d", c.Msg.UID, limitRet)
		return c.Error(smsg, limitRet)
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleMapInfo failed, wrestleManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !wrestleM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_WrestleMapInfo failed, cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		if !c.User.HeroManager().IsUniqHeroEnough(goxml.TwoTeamRequireMinHeroCount) {
			l4g.Errorf("user: %d C2L_WrestleMapInfo: hero count not enough", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_HERO_NOT_ENOUGH))
		}

		flag, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE))
		if !flag {
			l4g.Errorf("user: %d C2L_WrestleMapInfo: formation info not exist", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		// 进入地图先在本地报名，产生数据，跨服报名在进入赛场时进行
		wrestleUser = wrestleM.NewWrestleUserSignUp(c.User.ID())

		// 第一次进入，需要初始化防守阵容
		if c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)) == nil {
			if !wrestleUser.InitFormation(c.Srv, c.User, teamNum) {
				l4g.Errorf("user: %d C2L_WrestleMapInfo: init formation failed", c.Msg.UID)
				return c.Error(smsg, uint32(cret.RET_ERROR))
			}
		}
		wrestleM.SetChange(c.Srv, wrestleUser)
	} else {
		if wrestleUser.GetLevel() == uint32(common.WRESTLE_STATUS_WS_NONE) {
			// 曾经参与过的玩家，本地报名
			wrestleUser.UserSignUp()
			wrestleM.SetChange(c.Srv, wrestleUser)
		}
	}

	/*if wrestleM.IsNeedNewTopData(now) {
		wrestleM.SetTopDataUpdating()
		if !c.Srv.SendCmdToCross(
			l2c.ID_MSG_L2C_WrestleTopList, c.Msg.UID, &l2c.L2C_WrestleTopList{
				Level: 0, // 获取全部
			}) {
			wrestleM.SetTopDataUpdateFinish()
			l4g.Errorf("[Wrestle] user:%d cross maintain", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
		}
		return true
	}*/

	smsg.NowLevel = wrestleUser.GetLevel()
	smsg.LastLevel = wrestleUser.GetLastLevel()
	smsg.LastRoomRank = wrestleUser.GetLastRoomRank()
	smsg.Users = wrestleM.GetMapTopThree()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleMapInfo, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWrestleTopUserListCommand struct {
	base.UserCommand
}

func (c *C2LWrestleTopUserListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleTopUserListCommand) Error(msg *cl.L2C_WrestleTopUserList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleTopUserList, msg)
	return false
}

func (c *C2LWrestleTopUserListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleTopUserList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleTopUserList Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleTopUserList: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleTopUserList{
		Ret:   uint32(cret.RET_OK),
		Level: cmsg.Level,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleTopUserList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	limitRet := wrestleReqLimit(c.Msg.UID, serverDay, now)
	if limitRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleTopUserList: wrestle req limit. ret:%d", c.Msg.UID, limitRet)
		return c.Error(smsg, limitRet)
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleTopUserList failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if wrestleM.IsNeedNewTopData(now) {
		wrestleM.SetTopDataUpdating()
		if !c.Srv.SendCmdToCross(
			l2c.ID_MSG_L2C_WrestleTopList, c.Msg.UID, &l2c.L2C_WrestleTopList{
				Level: cmsg.Level,
			}) {
			wrestleM.SetTopDataUpdateFinish()
			l4g.Errorf("[Wrestle] user:%d cross maintain", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
		}
		return true
	}
	smsg.TopUsers = wrestleM.GetTopUserByLevel(cmsg.Level)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleTopUserList, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWrestleRoomInfoCommand struct {
	base.UserCommand
}

func (c *C2LWrestleRoomInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleRoomInfoCommand) Error(msg *cl.L2C_WrestleRoomInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRoomInfo, msg)
	return false
}

func (c *C2LWrestleRoomInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleRoomInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleRoomInfo Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleRoomInfo: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleRoomInfo{
		Ret:        uint32(cret.RET_OK),
		NeedChange: cmsg.NeedChange,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleRoomInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	limitRet := wrestleReqLimit(c.Msg.UID, serverDay, now)
	if limitRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleRoomInfo: wrestle req limit. ret:%d", c.Msg.UID, limitRet)
		return c.Error(smsg, limitRet)
	}
	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleRoomInfo failed, wrestleManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !wrestleM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_WrestleRoomInfo failed, cross not connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if wrestleM.Get(c.Msg.UID) == nil {
		l4g.Errorf("user: %d C2L_WrestleRoomInfo failed, wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	msg := &l2c.L2C_WrestleInfo{
		Snapshot:   c.User.NewUserSnapshot(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)),
		NeedChange: cmsg.NeedChange,
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleInfo, c.Msg.UID, msg) {
		l4g.Errorf("[Wrestle] user:%d cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LWrestleFightLogCommand struct {
	base.UserCommand
}

func (c *C2LWrestleFightLogCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleFightLogCommand) Error(msg *cl.L2C_WrestleFightLog, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFightLog, msg)
	return false
}

func (c *C2LWrestleFightLogCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleFightLog{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleFightLog Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleFightLog: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleFightLog{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleFightLog: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	if !goxml.IsWrestleOpen(goxml.GetData(), serverDay) || goxml.IsWrestleDailyResetting(now) {
		l4g.Errorf("user: %d C2L_WrestleFightLog: wrestle req limit.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleFightLog failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)

	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleFightLog failed, wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//日志红点提示的处理
	if wrestleUser.GetNewLogTip() {
		wrestleUser.SetNewLogTip(false)
	}

	// 进入之后被击败重置为false
	wrestleUser.SetBeDefeated(false)
	wrestleM.SetChange(c.Srv, wrestleUser)

	logM := wrestleM.GetLogM()
	wrestleLog := logM.GetOwnerLogs(wrestleUser)
	loaded := wrestleLog.CheckLoaded()
	if loaded {
		smsg.Logs = filterLogs(wrestleLog, logM)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFightLog, smsg)
	} else {
		req := &AsyncC2LWrestleFightLogReq{smsg}
		c.AsyncGetWrestleLog(req)
	}
	return c.ResultOK(smsg.Ret)
}

func filterLogs(wrestleLog *ab.Logs, logM *ab.LogM) []*cl.WrestleLog {
	logs := wrestleLog.GetAll()
	retLogs := make([]*cl.WrestleLog, 0, len(logs))
	expiredIDs := make([]uint64, 0, len(logs)/2)
	for _, v := range logs {
		oneLog, ok := v.(*awrestle.Log)
		if !ok {
			continue
		}
		if goxml.GetWrestleSeasonResetTime(goxml.GetData(), oneLog.GetCreatTm()) != goxml.GetWrestleSeasonResetTime(goxml.GetData(), time.Now().Unix()) {
			expiredIDs = append(expiredIDs, oneLog.GetUniqID())
			continue
		}
		retLogs = append(retLogs, oneLog.Flush())
	}

	if len(expiredIDs) > 0 {
		wrestleLog.DelLog(expiredIDs)
		logM.SetChange(wrestleLog)
	}
	return retLogs
}

type AsyncC2LWrestleFightLogReq struct {
	smsg *cl.L2C_WrestleFightLog
}

func (ar *AsyncC2LWrestleFightLogReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	list := dbData.(map[uint32]*cl.WrestleLog)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleFightLog: get data error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFightLog, smsg)
		return false
	}

	wrestleM, ok := srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleFightLog failed, wrestleManager not exist", args.UID)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFightLog, smsg)
		return false
	}
	wrestleUser := wrestleM.Get(args.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleFightLog: no user data", args.UID)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFightLog, smsg)
		return false
	}
	logM := wrestleM.GetLogM()
	wrestleLog := logM.GetOwnerLogs(wrestleUser)
	for _, data := range list {
		wrestleLog.LoadFromDB(awrestle.NewLogFromDB(data))
	}
	wrestleLog.LoadFinish()

	smsg.Logs = filterLogs(wrestleLog, logM)
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFightLog, smsg)
	return true
}

type C2LWrestleFightCommand struct {
	base.UserCommand
}

func (c *C2LWrestleFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleFightCommand) Error(msg *cl.L2C_WrestleFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, msg)
	return false
}

func (c *C2LWrestleFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleFight: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleFight{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	//防刷验证
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIWrestle) {
		l4g.Errorf("user: %d C2L_WrestleFight: operate too frequently", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_OPERATE_TOO_OFTEN))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_WrestleFight: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	now := time.Now().Unix()
	if !goxml.IsWrestleActive(goxml.GetData(), c.Srv.ServerDay(now), now) {
		l4g.Errorf("user: %d C2L_WrestleFight: wrestle function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_NOT_OPEN))
	}

	// 重置时间内不能打
	if now >= goxml.GetWrestleDailyResetTime(now) {
		l4g.Errorf("user: %d C2L_WrestleFight: wrestle is daily resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_RESETTING))
	}

	if cmsg.Id == c.Msg.UID {
		l4g.Errorf("user: %d C2L_WrestleFight: opponent id is error. cmsg.Id:%d ", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleFight failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleFight failed, wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if wrestleUser.IsLocked() {
		l4g.Errorf("user: %d C2L_WrestleFight failed, wrestleUser is attacking", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_SELF_IS_ATTACKING))
	}

	if c.User.GetFormation(uint32(common.FORMATION_ID_FI_CROSS_ARENA_ATTACK)) == nil {
		l4g.Errorf("user:%d C2L_WrestleFight: attack formation is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	// 最高等级以下的赛场，检查攻打的对手是不是房间内的玩家
	if wrestleUser.GetLevel() < goxml.WrestleTopLevel() &&
		!wrestleUser.CheckIdIsMember(cmsg.Id) {
		l4g.Errorf("user: %d C2L_WrestleFight failed, defenderId not ok. defenderId:%d defenderSid:%d",
			c.Msg.UID, cmsg.Id, cmsg.Sid)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if cdStart := wrestleUser.GetDefenderCdStart(cmsg.Id); cdStart != 0 && now > cdStart {
		if now-cdStart < goxml.GetData().WrestleConfigInfoM.GetFailCD() {
			l4g.Errorf("user: %d C2L_WrestleFight failed, fail cd. defenderId:%d defenderSid:%d cdStart:%d",
				c.Msg.UID, cmsg.Id, cmsg.Sid, cdStart)
			return c.Error(smsg, uint32(cret.RET_WRESTLE_FAIL_CD_NOT_EXPIRE))
		}
	}

	msg := &l2c.L2C_WrestleStart{
		DefenderUid:  cmsg.Id,
		DefenderSid:  cmsg.Sid,
		DefenderRank: cmsg.Rank,
		AttackerSid:  c.User.ServerID(),
	}
	wrestleUser.Lock()
	wrestleUser.CacheClientData(cmsg.ClientData)
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleStart, c.Msg.UID, msg) {
		l4g.Errorf("[WrestleStart] user:%d cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LWrestleLikeCommand struct {
	base.UserCommand
}

func (c *C2LWrestleLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleLikeCommand) Error(msg *cl.L2C_WrestleLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleLike, msg)
	return false
}

func (c *C2LWrestleLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleLike Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleLike: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleLike{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleLike: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)

	if !goxml.IsWrestleOpen(goxml.GetData(), serverDay) {
		l4g.Errorf("user: %d C2L_WrestleLike: wrestle function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_NOT_OPEN))
	}

	if goxml.IsWrestleDailyResetting(now) {
		l4g.Errorf("user: %d : wrestle daily resetting.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_RESETTING))
	}

	fret, _, _ := c.User.CheckNumByType(uint32(common.PURCHASEID_WRESTLE_LIKE_COUNT), 1)
	if fret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleLike: check num err: %d", c.Msg.UID, fret)
		return c.Error(smsg, fret)
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleLike failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	rank := wrestleM.GetLikeRank(cmsg.Id)

	if rank == 0 || rank > 3 {
		l4g.Errorf("user: %d C2L_WrestleLike failed, wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	smsgToCross := &l2c.L2C_WrestleLike{
		Uid: cmsg.Id,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleLike, c.Msg.UID, smsgToCross) {
		l4g.Errorf("[WrestleLike] user:%d cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	//c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleLike, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWrestleRankListCommand struct {
	base.UserCommand
}

func (c *C2LWrestleRankListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleRankListCommand) Error(msg *cl.L2C_WrestleRankList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRankList, msg)
	return false
}

func (c *C2LWrestleRankListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleRankList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleRankList Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleRankList: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleRankList{
		Ret:  uint32(cret.RET_OK),
		Type: cmsg.Type,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleRankList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleRankList failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)

	switch cmsg.Type {
	case uint32(common.WRESTLE_RANK_WR_CURRENT): // 当前排名
		limitRet := wrestleReqLimit(c.Msg.UID, serverDay, now)
		if limitRet != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_WrestleRankList: wrestle req limit. ret:%d", c.Msg.UID, limitRet)
			return c.Error(smsg, limitRet)
		}
		if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleRank, c.Msg.UID, &l2c.L2C_WrestleRank{
			Uid: c.Msg.UID,
		}) {
			l4g.Errorf("[Wrestle] user:%d cross maintain", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
		}
		return true
	case uint32(common.WRESTLE_RANK_WR_HALL_OF_NAME): // 荣耀殿堂
		wrestleUser := wrestleM.Get(c.Msg.UID)
		smsg.List = wrestleM.GetRankOfHallOfFame()
		if !goxml.IsWrestleActive(goxml.GetData(), serverDay, now) && wrestleUser != nil {
			smsg.SelfRank = wrestleUser.GetPrevSeasonTotalRank()
			smsg.SelfValue = &cl.RankValue{
				User:  c.User.NewUserSnapshot(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)),
				Value: uint64(wrestleUser.GetPrevSeasonLevelAfterReset()),
			}
		}

	default:
		l4g.Errorf("user: %d C2L_WrestleRankList: type error. type: %d ", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_NOT_OPEN))
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRankList, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWrestleRecvTaskAwardCommand struct {
	base.UserCommand
}

func (c *C2LWrestleRecvTaskAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleRecvTaskAwardCommand) Error(msg *cl.L2C_WrestleRecvTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRecvTaskAward, msg)
	return false
}

func (c *C2LWrestleRecvTaskAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleRecvTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleRecvTaskAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleRecvTaskAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleRecvTaskAward{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleRecvTaskAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if len(cmsg.Ids) > goxml.GetData().WrestleLevelTaskInfoM.GetLen() {
		l4g.Errorf("user: %d C2L_WrestleRecvTaskAward: param error. len:%d", c.Msg.UID, len(cmsg.Ids))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	limitRet := wrestleReqLimit(c.Msg.UID, serverDay, now)
	if limitRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleRecvTaskAward: wrestle req limit. ret:%d", c.Msg.UID, limitRet)
		return c.Error(smsg, limitRet)
	}
	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleRecvTaskAward failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleRecvTaskAward failed, wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	reqIds := make(map[uint32]struct{}, len(cmsg.Ids))
	allAwardIndex := make([]uint32, 0, len(cmsg.Ids))
	allAwards := make([]*cl.Resource, 0, len(cmsg.Ids))
	for _, id := range cmsg.Ids {
		if _, exist := reqIds[id]; exist {
			l4g.Error("user: %d C2L_WrestleRecvTaskAward: request param repeated. cmsg.Ids:%+v",
				c.Msg.UID, cmsg.Ids)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		} else {
			reqIds[id] = struct{}{}
		}

		if wrestleUser.CheckLevelTaskRecvRepeat(id) {
			l4g.Errorf("user: %d C2L_WrestleRecvTaskAward failed, recv repeat. level:%d ", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		levelTaskInfo := goxml.GetData().WrestleLevelTaskInfoM.Index(id)

		if levelTaskInfo == nil {
			l4g.Errorf("user: %d C2L_WrestleRecvTaskAward: levelTaskInfo not exist. id:%d",
				c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}
		if wrestleUser.GetSeasonMaxLevel() < levelTaskInfo.Level {
			l4g.Errorf("user: %d C2L_WrestleRecvTaskAward: level error. userLevel:%d reqLevel:%d",
				c.Msg.UID, wrestleUser.GetLevel(), levelTaskInfo.Level)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}

		awards := goxml.GetData().WrestleLevelTaskInfoM.GetTaskAwards(id)
		if len(awards) == 0 {
			l4g.Errorf("user: %d C2L_WrestleRecvTaskAward failed, awards is nil. level:%d ", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		allAwardIndex = append(allAwardIndex, id)
		allAwards = append(allAwards, awards...)
	}

	for _, index := range allAwardIndex {
		wrestleUser.SetLevelTaskAwarded(index)
	}
	cRet, resources := c.User.Award(c.Srv, allAwards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_WRESTLE_RECV_LEVEL_TASK), 0)
	if cRet != uint32(cret.RET_OK) {
		l4g.Errorf("%d [C2L_WrestleRecvTaskAward] emblem revive award error. %d, %d", c.Msg.UID, cmsg.Ids, cRet)
	}
	wrestleM.SetChange(c.Srv, wrestleUser)
	smsg.Awards = resources
	smsg.ReceivedLevelAward = wrestleUser.GetReceivedLevelAward()

	c.User.LogWrestleRecvLevelAward(c.Srv, cmsg.Ids)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRecvTaskAward, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWrestleHallOfFameCommand struct {
	base.UserCommand
}

func (c *C2LWrestleHallOfFameCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleHallOfFameCommand) Error(msg *cl.L2C_WrestleHallOfFame, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleHallOfFame, msg)
	return false
}

func (c *C2LWrestleHallOfFameCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleHallOfFame{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleHallOfFame Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleHallOfFame: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleHallOfFame{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleHallOfFame: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	if !goxml.IsWrestleOpen(goxml.GetData(), serverDay) {
		l4g.Errorf("user: %d C2L_WrestleHallOfFame: wrestle function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_NOT_OPEN))
	}
	if goxml.IsWrestleDailyResetting(now) {
		l4g.Errorf("user: %d C2L_WrestleHallOfFame: wrestle daily resetting.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_RESETTING))
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleHallOfFame failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if wrestleM.IsNeedUpdateHallOfFame(now) {
		msg := &l2c.L2C_WrestleLastRank{}
		if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleLastRank, c.Msg.UID, msg) {
			l4g.Errorf("[WrestleLastRank] user:%d cross maintain", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
		}
		return true
	}

	smsg.List, smsg.LikeNum = wrestleM.GetHallOfFameTop3()
	if !goxml.IsWrestleActive(goxml.GetData(), serverDay, now) {
		wrestleUser := wrestleM.Get(c.Msg.UID)
		if wrestleUser != nil {
			smsg.SelfRank = wrestleUser.GetPrevSeasonTotalRank()

			smsg.SelfValue = &cl.RankValue{
				User:  c.User.NewUserSnapshot(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)),
				Value: uint64(wrestleUser.GetPrevSeasonLevelAfterReset()),
			}
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleHallOfFame, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWrestleChangeRoomCommand struct {
	base.UserCommand
}

func (c *C2LWrestleChangeRoomCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWrestleChangeRoomCommand) Error(msg *cl.L2C_WrestleChangeRoom, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleChangeRoom, msg)
	return false
}

func (c *C2LWrestleChangeRoomCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WrestleChangeRoom{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleChangeRoom Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_WrestleChangeRoom: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_WrestleChangeRoom{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WRESTLE), c.Srv) {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)
	if !goxml.IsWrestleOpen(goxml.GetData(), serverDay) {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: wrestle function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_NOT_OPEN))
	}

	if goxml.IsWrestleDailyResetting(now) {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: wrestle daily resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_RESETTING))
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if wrestleUser.GetLevel() >= goxml.WrestleTopLevel() {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: level illegal", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	nextChangeTm := wrestleUser.GetNextChangeRoomTm()
	if time.Now().Unix() < nextChangeTm {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: change room cd limit, nextChangeTm:%d",
			c.Msg.UID, nextChangeTm)
		return c.Error(smsg, uint32(cret.RET_WRESTLE_CHANGE_ROOM_CD))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleChangeRoom, c.Msg.UID, &l2c.L2C_WrestleChangeRoom{}) {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}
