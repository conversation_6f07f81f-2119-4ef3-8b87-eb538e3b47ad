package announcement

import (
	"app/gmxml"
	"app/protos/out/cl"
	"context"

	"app/logic/command/base"
	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_AnnouncementGetData), &C2LAnnouncementGetDataCommand{}, state) // 获取公告信息
}

type C2LAnnouncementGetDataCommand struct {
	base.UserCommand
}

func (c *C2LAnnouncementGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LAnnouncementGetDataCommand) Error(msg *cl.L2C_AnnouncementGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_AnnouncementGetData, msg)
	return false
}

func (c *C2LAnnouncementGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_AnnouncementGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_AnnouncementGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_AnnouncementGetData: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_AnnouncementGetData{
		Ret:  uint32(cret.RET_OK),
		Lang: cmsg.Lang,
	}

	if cmsg.Lang == "" {
		l4g.Errorf("user: %d C2L_AnnouncementGetData: param lang is invalid.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	smsg.Announcements = gmxml.AnnouncementInfoM.Flush(cmsg.Lang, c.User.OpID(), c.User.Channel())
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_AnnouncementGetData, smsg)

	return c.ResultOK(smsg.Ret)
}
