package gst

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/gst"
	aguild "app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/mongo"
	"app/protos/in/l2c"
	"app/protos/in/l2m"
	"app/protos/in/log"
	"app/protos/in/p2l"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	cret "app/protos/out/ret"
	"context"
	"unicode/utf8"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetData), &C2LGSTGetDataCommand{}, state)                               //获取基础数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetGroundData), &C2LGSTGetGroundDataCommand{}, state)                   //获取指定地块数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetTeamsData), &C2LGSTGetTeamsDataCommand{}, state)                     //获取队伍数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetLogData), &C2LGSTGetLogDataCommand{}, state)                         //获取日志
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTExchangeGroundTeam), &C2LGSTExchangeGroundTeamCommand{}, state)         //交换地块队伍出战顺序
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTTeamOperate), &C2LGSTTeamOperateCommand{}, state)                       //队伍操作
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTMessageEdit), &C2LGSTMessageEditCommand{}, state)                       // 更改公会战公告
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDonate), &C2LGSTDonateCommand{}, state)                                 // 公会战捐赠
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTPreviewHangUpReward), &C2LGSTPreviewHangUpRewardCommand{}, state)       // 挂机奖励预览
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetHangUpReward), &C2LGSTGetHangUpRewardCommand{}, state)               // 领取挂机资源
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTRank), &C2LGSTRankCommand{}, state)                                     // 获取排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetTasksData), &C2LGSTGetTasksDataCommand{}, state)                     // 获取任务数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetTasksReward), &C2LGSTGetTasksRewardCommand{}, state)                 // 领取任务奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetGuildDonateData), &C2LGSTGetGuildDonateDataCommand{}, state)         // 获取捐赠信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetGuildDonateMemData), &C2LGSTGetGuildDonateMemDataCommand{}, state)   // 公会捐赠成员信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGuildBuildGetData), &C2LGSTGuildBuildGetDataCommand{}, state)           // 获取建筑信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGuildBuildDonate), &C2LGSTGuildBuildDonateCommand{}, state)             // 公会建筑捐赠
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGuildBuildDonateRank), &C2LGSTGuildBuildDonateRankCommand{}, state)     // 公会建筑捐赠排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGuildBuildDispatchHero), &C2LGSTGuildBuildDispatchHeroCommand{}, state) // 派遣英雄
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGuildBuildGetTaskData), &C2LGSTGuildBuildGetTaskDataCommand{}, state)   // 获取建筑任务数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGuildBuildRecvTaskAward), &C2LGSTGuildBuildRecvTaskAwardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGroupUsersRank), &C2LGSTGroupUsersRankCommand{}, state)         // 获取排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGroupUsersRankLike), &C2LGSTGroupUsersRankLikeCommand{}, state) // 排行榜点赞
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTArenaVote), &C2LGSTArenaVoteCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTGetArenaVoteRecord), &C2LGSTGetArenaVoteRecordCommand{}, state) // 擂台助威
	//cmds.Register(uint32(cl.ID_MSG_C2L_GSTBossGet), &C2LGSTBossGetCommand{}, state)                           // boss信息获取
	//cmds.Register(uint32(cl.ID_MSG_C2L_GSTBossFight), &C2LGSTBossFightCommand{}, state)                       // boss战斗
	//cmds.Register(uint32(cl.ID_MSG_C2L_GSTBossAward), &C2LGSTBossAwardCommand{}, state)                       // boss领奖
	//cmds.Register(uint32(cl.ID_MSG_C2L_GSTBossRank), &C2LGSTBossRankCommand{}, state)                         // boss排行
	//cmds.Register(uint32(cl.ID_MSG_C2L_GSTBossBuyChallenge), &C2LGSTBossBuyChallengeCommand{}, state)         // boss购买挑战令
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonGetData), &C2LGSTDragonGetDataCommand{}, state)               // 龙战获取基础数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonGetCultivation), &C2LGSTDragonGetCultivationCommand{}, state) // 龙战获取养成数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonShow), &C2LGSTDragonShowCommand{}, state)                     // 设置展示的龙
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonEvolve), &C2LGSTDragonEvolveCommand{}, state)                 // 龙进化
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonFight), &C2LGSTDragonFightCommand{}, state)                   // 玩家攻打对方公会的龙
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonFightBuyCount), &C2LGSTDragonFightBuyCountCommand{}, state)   // 钻石购买龙战挑战次数
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonFightUseTicket), &C2LGSTDragonFightUseTicketCommand{}, state) // 道具购买龙战挑战次数
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonTaskGetData), &C2LGSTDragonTaskGetDataCommand{}, state)       // 龙战任务获取数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonTaskAward), &C2LGSTDragonTaskAwardCommand{}, state)           // 龙战任务领奖
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonRank), &C2LGSTDragonRankCommand{}, state)                     // 公会成员龙战排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonSkillOperate), &C2LGSTDragonSkillOperateCommand{}, state)     // 攻城技能
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTDragonStrategySkill), &C2LGSTDragonStrategySkillCommand{}, state)   // 祝福技能
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTScorePreview), &C2LGSTScorePreviewCommand{}, state)                 // 积分预览
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildMobilizationGuildRank), &C2LGuildMobilizationGuildRankCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTOreGetData), &C2LGSTOreGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTOreFight), &C2LGSTOreFightCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTOreBuyTimes), &C2LGSTOreBuyTimesCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTOreSearchAssist), &C2LGSTOreSearchAssistCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTOreOccupy), &C2LGSTOreOccupyCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTOreGetOreData), &C2LGSTOreGetOreDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTTechGetData), &C2LGSTTechGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTTechDonate), &C2LGSTTechDonateCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTTechTaskReward), &C2LGSTTechTaskRewardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTTechGuildUserRank), &C2LGSTTechGuildUserRankCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTSkillAssemble), &C2LGSTSkillAssembleCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GSTTechSign), &C2LGSTTechSignCommand{}, state)
	InitGSTChallenge(cmds, state) // 新擂台赛
}

type C2LGSTMessageEditCommand struct {
	base.UserCommand
}

func (c *C2LGSTMessageEditCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTMessageEditCommand) Error(msg *cl.L2C_GSTMessageEdit, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTMessageEdit, msg)
	return false
}

func (c *C2LGSTMessageEditCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTMessageEdit{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTMessageEdit Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTMessageEdit: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTMessageEdit{
		Ret:     uint32(cret.RET_OK),
		Message: cmsg.Message,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTMessageEdit: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTMessageEdit: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTMessageEdit guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if !guildUser.IsGstGuildManager() {
		l4g.Errorf("user: %d C2L_GSTMessageEdit: not leader. ", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	retn := CheckString(cmsg.Message, c.Msg.UID, int(goxml.GetData().GuildConfigInfoM.NoticeLengthMax))
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GSTMessageEdit: notice error. guildId:%d", c.Msg.UID, guild.ID())
		return c.Error(smsg, retn)
	}

	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		var checks []*p2l.SensitiveWordCheckReq
		if len(cmsg.Message) != 0 {
			checks = append(checks, c.User.GetCheckSensitiveWordReq(cmsg.Message, c.Srv.ServerID(), character.SensitiveGstMessage, platformConfig.Channel))
		}
		if len(checks) > 0 {
			base.AsyncSensitiveWordCheck(c.Srv, c.User.Args(c.Msg.Cmd), &AsyncC2LGSTMessageEditReq{
				Smsg: smsg,
			}, checks, nil)
			return true
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTMessageEdit, c.Msg.UID, &l2c.L2CS_GSTMessageEdit{
		Message: cmsg.Message,
	}) {
		l4g.Errorf("user: %d C2L_GSTMessageEdit: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

func CheckString(content string, uid uint64, maxLen int) uint32 {
	if len(content) > maxLen*character.MaxCharactUtf8Len { //  提前检查字节长度
		l4g.Errorf("checkcontent %d content: %s length error len:%d ", uid, content, len(content))
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	if !utf8.ValidString(content) { //  这里有遍历
		l4g.Errorf("uid %d content illegal: %s", uid, content)
		return uint32(cret.RET_GUILD_STRING_ILLEGAL_CHARACTER)
	}
	/*	if strings.Trim(content, " ") == "" {
		l4g.Errorf("uid %d content: %s length error, all empty space", uid, content)
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}*/
	// utf8字符长度
	if length := utf8.RuneCountInString(content); length > maxLen {
		l4g.Errorf("uid %d content: %s length error: %d", uid, content, length)
		return uint32(cret.RET_GUILD_STRING_LENGTH_LIMIT)
	}
	return uint32(cret.RET_OK)
}

type C2LGSTDonateCommand struct {
	base.UserCommand
}

func (c *C2LGSTDonateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDonateCommand) Error(msg *cl.L2C_GSTDonate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDonate, msg)
	return false
}

//nolint:funlen
func (c *C2LGSTDonateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDonate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDonate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTDonate: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDonate{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDonate: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDonate: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDonate guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if len(cmsg.GstClientDonates) == 0 || len(cmsg.GstClientDonates) > goxml.GetData().GuildSandTableConfigInfoM.DonateMaxLen() {
		l4g.Errorf("user:%d C2L_GSTDonate. GstClientDonates lens:%d is error", c.Msg.UID, len(cmsg.GstClientDonates))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	goddessInfo := goxml.GetData().GuildSandTableGoddessInfoM.Index(cmsg.GoddessId)
	if goddessInfo == nil {
		l4g.Errorf("user:%d C2L_GSTDonate. get GuildSandTableGoddessInfoM goddessId:%d failed", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	repeatedCheck := make(map[uint32]map[uint32]struct{})
	var totalCost []*cl.Resource
	for _, v := range cmsg.GstClientDonates { //nolint:varnamelen
		if v == nil {
			l4g.Errorf("user:%d C2L_GSTDonate GstClientDonates item is nil", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		_, exit := repeatedCheck[v.DonateType]
		if exit && v.DonateType != uint32(common.GVG_DONATE_TYPE_GVGD_ITEM) {
			l4g.Errorf("user:%d C2L_GSTDonate GstClientDonate donate type:%d is repeated", c.Msg.UID, v.DonateType)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		_, exit = repeatedCheck[v.DonateType][v.Value]
		if exit {
			l4g.Errorf("user:%d C2L_GSTDonate GstClientDonate item donate value:%d is repeated", c.Msg.UID, v.Value)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		} else {
			if repeatedCheck[v.DonateType] == nil {
				repeatedCheck[v.DonateType] = make(map[uint32]struct{})
			}
			repeatedCheck[v.DonateType][v.Value] = struct{}{}
		}

		switch v.DonateType {
		case uint32(common.GVG_DONATE_TYPE_GVGD_ITEM):
			_, exist := goxml.GetData().GuildSandTableConfigInfoM.DonateItemId[v.Value]
			if !exist {
				l4g.Errorf("user:%d C2L_GSTDonate GstClientDonate item value:%d is error", c.Msg.UID, v.Value)
				return c.Error(smsg, uint32(cret.RET_GST_USER_DONATE_ITEM_ID_ERROR))
			}
			totalCost = append(totalCost, &cl.Resource{
				Type:  uint32(common.RESOURCE_ITEM),
				Value: uint32(v.Value),
				Count: uint32(v.Count),
			})
		case uint32(common.GVG_DONATE_TYPE_GVGD_MANAGER):
			if !guildUser.IsGuildLeader() {
				l4g.Errorf("user: %d C2L_GSTDonate: not leader. ", c.Msg.UID)
				return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
			}
		default:
			l4g.Errorf("user:%d C2L_GSTDonate type:%d  is error", c.Msg.UID, v.DonateType)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	if len(totalCost) > 0 {
		ret, _ := c.User.CheckResourcesSize(totalCost, c.Srv)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_GSTDonate check resource size failed", c.Msg.UID)
			return c.Error(smsg, ret)
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDonate, c.Msg.UID, &l2c.L2CS_GSTDonate{
		GoddessId:        goddessInfo.Id,
		GstClientDonates: cmsg.GstClientDonates,
	}) {
		l4g.Errorf("user: %d C2L_GSTDonate: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTPreviewHangUpRewardCommand struct {
	base.UserCommand
}

func (c *C2LGSTPreviewHangUpRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTPreviewHangUpRewardCommand) Error(msg *cl.L2C_GSTPreviewHangUpReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTPreviewHangUpReward, msg)
	return false
}

func (c *C2LGSTPreviewHangUpRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTPreviewHangUpReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTPreviewHangUpReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTPreviewHangUpReward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTPreviewHangUpReward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTPreviewHangUpReward: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTPreviewHangUpReward: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDonate guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTPreviewHangUpReward, c.Msg.UID, &l2c.L2CS_GSTPreviewHangUpReward{}) {
		l4g.Errorf("user: %d C2L_GSTPreviewHangUpReward: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetHangUpRewardCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetHangUpRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetHangUpRewardCommand) Error(msg *cl.L2C_GSTGetHangUpReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetHangUpReward, msg)
	return false
}

func (c *C2LGSTGetHangUpRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetHangUpReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetHangUpReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTGetHangUpReward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetHangUpReward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetHangUpReward: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetHangUpReward: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDonate guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetHangUpReward, c.Msg.UID, &l2c.L2CS_GSTGetHangUpReward{}) {
		l4g.Errorf("user: %d C2L_GSTGetHangUpReward: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTRankCommand struct {
	base.UserCommand
}

func (c *C2LGSTRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTRankCommand) Error(msg *cl.L2C_GSTRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTRank, msg)
	return false
}

func (c *C2LGSTRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTRank: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTRank{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTRank: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTRank: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDonate guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTRank, c.Msg.UID, &l2c.L2CS_GSTRank{}) {
		l4g.Errorf("user: %d C2L_GSTRank: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetDataCommand) Error(msg *cl.L2C_GSTGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetData, msg)
	return false
}

func (c *C2LGSTGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGetData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	reqMsg := &l2c.L2CS_GSTGetData{
		Type: cmsg.Type,
		User: &l2c.GSTSignUser{
			Id:      c.User.ID(),
			GuildId: guildUser.GuildID(),
			Sid:     c.User.ServerID(),
		},
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	needSign := gstM.NeedSign(c.Msg.UID)
	if needSign {
		reqMsg.NeedSign = true
		reqMsg.Partition = c.Srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA))
		c.SignGst(reqMsg.User)
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetData, c.Msg.UID, reqMsg) {
		l4g.Errorf("user: %d C2L_GSTGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

func (c *C2LGSTGetDataCommand) SignGst(signUser *l2c.GSTSignUser) {
	signUser.Name = c.User.Name()
	signUser.SeasonLv = c.User.GetSeasonLevel()
	signUser.BaseId = c.User.BaseID()
	signUser.SeasonLinkPower = c.User.SeasonLink().GetBattleSeasonLinkPower()
	signUser.ExpireTime = c.User.ExpireTime()
	signUser.SeasonlinkActived = make(map[uint32]bool)
	signUser.AddFightTimes = c.User.GetGstAddFightTimes()
	signUser.Image = c.User.GetImage()
	signUser.ShowHero = c.User.GetShowHero()
	signUser.TalentTreeLv = c.User.TalentTree().GetRootLevel()
	signUser.Title = c.User.GetTitle()
	signUser.SeasonAdd = c.User.GetSeasonAddData()
	signUser.PokemonImage = c.User.GetPokemonShowImage()
	for heroId := range c.User.SeasonLink().GetActivedHeros() {
		signUser.SeasonlinkActived[heroId] = true
	}

	formation := c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_GST))
	if formation == nil {
		success, num := c.User.GetTeamNum(uint32(common.FORMATION_ID_FI_GST))
		if success {
			c.User.FormationManager().SetFormationByPower(c.Srv, uint32(common.FORMATION_ID_FI_GST), num)
			formation = c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_GST))
			c.User.FormationManager().NewFormation(c.Srv, uint32(common.FORMATION_ID_FI_GST), formation)
		}
	}
	if formation != nil {
		signUser.Teams = c.User.FormationManager().Convert2GSTTeam(formation.Teams)
	}
}

type C2LGSTGetGroundDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetGroundDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetGroundDataCommand) Error(msg *cl.L2C_GSTGetGroundData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetGroundData, msg)
	return false
}

func (c *C2LGSTGetGroundDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetGroundData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetGroundData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGetGroundData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetGroundData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetGroundData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetGroundData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetGroundData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetGroundData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetGroundData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetGroundData, c.Msg.UID, &l2c.L2CS_GSTGetGroundData{
		Id: cmsg.Id,
	}) {
		l4g.Errorf("user: %d C2L_GSTGetGroundData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetTeamsDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetTeamsDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetTeamsDataCommand) Error(msg *cl.L2C_GSTGetTeamsData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetTeamsData, msg)
	return false
}

func (c *C2LGSTGetTeamsDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetTeamsData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetTeamsData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGetTeamsData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetTeamsData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetTeamsData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetTeamsData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetTeamsData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetTeamsData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetTeamsData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	//nolint:mnd
	if cmsg.Type == 2 { //全部的队伍,需要管理权限
		if !guildUser.IsGstGuildManager() {
			l4g.Errorf("user: %d C2L_GSTGetTeamsData: not leader. ", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetTeamsData, c.Msg.UID, &l2c.L2CS_GSTGetTeamsData{
		Type: cmsg.Type,
	}) {
		l4g.Errorf("user: %d C2L_GSTGetTeamsData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetLogDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetLogDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetLogDataCommand) Error(msg *cl.L2C_GSTGetLogData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetLogData, msg)
	return false
}

func (c *C2LGSTGetLogDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetLogData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetLogData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGetLogData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetLogData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetLogData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetLogData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGetLogData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetLogData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetLogData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetLogData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.MongoM().Send(c.parseMongoReq(cmsg)) {
		return c.Error(smsg, uint32(ret.RET_SERVER_ERROR))
	}
	return true
}

func (c *C2LGSTGetLogDataCommand) parseMongoReq(req *cl.C2L_GSTGetLogData) *mongo.GetGstLogMessage {
	mongoReq := &mongo.GetGstLogMessage{
		Source: &l2m.GSTGetLogReq{
			ReqUid:  c.User.ID(),
			Req:     req.Req,
			OpGroup: uint32(c.Srv.OpGroup()),
		},
	}
	mongoReq.ParseQuery(c.Srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_GST_AREA)))
	return mongoReq
}

type C2LGSTExchangeGroundTeamCommand struct {
	base.UserCommand
}

func (c *C2LGSTExchangeGroundTeamCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTExchangeGroundTeamCommand) Error(msg *cl.L2C_GSTExchangeGroundTeam, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTExchangeGroundTeam, msg)
	return false
}

func (c *C2LGSTExchangeGroundTeamCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTExchangeGroundTeam{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTExchangeGroundTeam Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTExchangeGroundTeam: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTExchangeGroundTeam{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTExchangeGroundTeam: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTExchangeGroundTeam: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTExchangeGroundTeam guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if !guildUser.IsGstGuildManager() {
		l4g.Errorf("user: %d C2L_GSTExchangeGroundTeam: not leader. ", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTExchangeGroundTeam gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTExchangeGroundTeam cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTExchangeGroundTeam, c.Msg.UID, &l2c.L2CS_GSTExchangeGroundTeam{
		Id:    cmsg.Id,
		Teams: cmsg.Teams,
	}) {
		l4g.Errorf("user: %d C2L_GSTExchangeGroundTeam: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTTeamOperateCommand struct {
	base.UserCommand
}

func (c *C2LGSTTeamOperateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTTeamOperateCommand) Error(msg *cl.L2C_GSTTeamOperate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTTeamOperate, msg)
	return false
}

func (c *C2LGSTTeamOperateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTTeamOperate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTTeamOperate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTTeamOperate: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTTeamOperate{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTeamOperate: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTTeamOperate: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTMessageEdit gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTMessageEdit cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTTeamOperate guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	//操控其他人的队伍 需要管理权限
	for _, operate := range cmsg.Operates {
		for _, team := range operate.Teams {
			if team.User == nil {
				l4g.Errorf("user: %d team.User nil. ", c.Msg.UID)
				return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
			}
			if team.User.Id != c.Msg.UID {
				if !guildUser.IsGstGuildManager() {
					l4g.Errorf("user: %d C2L_GSTTeamOperate: not leader. ", c.Msg.UID)
					return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
				}
				break
			}
		}
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTTeamOperate, c.Msg.UID, &l2c.L2CS_GSTTeamOperate{
		Type:     cmsg.Type,
		Operates: cmsg.Operates,
	}) {
		l4g.Errorf("user: %d C2L_GSTTeamOperate: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetTasksDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetTasksDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetTasksDataCommand) Error(msg *cl.L2C_GSTGetTasksData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetTasksData, msg)
	return false
}

func (c *C2LGSTGetTasksDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetTasksData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTGetTaskData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetTasksData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetTaskData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetTaskData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetTaskData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetTaskData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetTaskData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetTasksData, c.Msg.UID, &l2c.L2CS_GSTGetTasksData{}) {
		l4g.Errorf("user: %d C2L_GSTGetTaskData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetTasksRewardCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetTasksRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetTasksRewardCommand) Error(msg *cl.L2C_GSTGetTasksReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetTasksReward, msg)
	return false
}

func (c *C2LGSTGetTasksRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetTasksReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetTasksRewardUnmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTGetTasksReward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetTasksReward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetTasksReward: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetTasksReward: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTMessageEdit: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetTasksReward guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetTasksReward gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetTasksReward cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetTasksReward get Gst user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	repeatedM := make(map[uint32]struct{})
	for _, taskId := range cmsg.TaskIds {
		_, exist := repeatedM[taskId]
		if exist {
			l4g.Errorf("user:%d C2L_GSTGetTasksReward taskId:%d is repeated", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedM[taskId] = struct{}{}

		if gstUser.TaskIsRecv(taskId) {
			l4g.Errorf("user:%d C2L_GSTGetTasksReward taskId:%d is recv", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}
	}

	crossMsg := &l2c.L2CS_GSTGetTasksReward{
		TaskIds: cmsg.TaskIds,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetTasksReward, c.Msg.UID, crossMsg) {
		l4g.Errorf("user: %d C2L_GSTGetTaskData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetGuildDonateDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetGuildDonateDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetGuildDonateDataCommand) Error(msg *cl.L2C_GSTGetGuildDonateData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetGuildDonateData, msg)
	return false
}

func (c *C2LGSTGetGuildDonateDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetGuildDonateData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetGuildDonateData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTGetGuildDonateData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetGuildDonateData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetGuildDonateData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetGuildDonateData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetGuildDonateData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetGuildDonateData, c.Msg.UID, &l2c.L2CS_GSTGetGuildDonateData{}) {
		l4g.Errorf("user: %d C2L_GSTGetGuildDonateData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGetGuildDonateMemDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetGuildDonateMemDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetGuildDonateMemDataCommand) Error(msg *cl.L2C_GSTGetGuildDonateMemData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetGuildDonateMemData, msg)
	return false
}

func (c *C2LGSTGetGuildDonateMemDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetGuildDonateMemData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetGuildDonateMemData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGetGuildDonateMemData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetGuildDonateMemData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetGuildDonateMemData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetGuildDonateMemData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateMemData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateMemData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	goddessInfo := goxml.GetData().GuildSandTableGoddessInfoM.Index(cmsg.GoddessId)
	if goddessInfo == nil {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateMemData. get GuildSandTableGoddessInfoM goddessId:%d failed", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetGuildDonateMemData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateMemData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetGuildDonateMemData, c.Msg.UID, &l2c.L2CS_GSTGetGuildDonateMemData{
		GoddessId: cmsg.GoddessId,
	}) {
		l4g.Errorf("user: %d C2L_GSTGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGuildBuildGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGuildBuildGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGuildBuildGetDataCommand) Error(msg *cl.L2C_GSTGuildBuildGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGuildBuildGetData, msg)
	return false
}

func (c *C2LGSTGuildBuildGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGuildBuildGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGuildBuildGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGuildBuildGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGuildBuildGetData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGuildBuildGetData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d C2L_GSTGetGuildDonateMemData gst manager is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetGuildDonateMemData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGuildBuildGetData, c.Msg.UID, &l2c.L2CS_GSTGuildBuildGetData{}) {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGuildBuildDonateCommand struct {
	base.UserCommand
}

func (c *C2LGSTGuildBuildDonateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGuildBuildDonateCommand) Error(msg *cl.L2C_GSTGuildBuildDonate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGuildBuildDonate, msg)
	return false
}

//nolint:funlen
func (c *C2LGSTGuildBuildDonateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGuildBuildDonate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGuildBuildDonate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGuildBuildDonate: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGuildBuildDonate{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonate: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonate: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonate: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonate guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonate gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonate cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonate get Gst user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_USER_NOT_SIGN))
	}

	buildInfo := goxml.GetData().GuildSandTableBuildInfoM.GetSeasonBuild(gstUser.Season, cmsg.BuildType)
	if buildInfo == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonate get season:%d build:%d failed", c.Msg.UID, gstUser.Season, cmsg.BuildType)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if len(cmsg.Cost) == 0 || len(cmsg.Cost) > len(buildInfo.LegalToken) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonate user len:%d is zero or more than buildInfo len:%d", c.Msg.UID, len(cmsg.Cost), len(buildInfo.LegalToken))
		return c.Error(smsg, uint32(cret.RET_PARAM_LENGTH_LIMIT))
	}
	repeatedM := make(map[uint32]struct{})
	var totalCount uint32
	for _, res := range cmsg.Cost {
		if res.Type != uint32(common.RESOURCE_TOKEN) {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDonate cost type:%d is not token", c.Msg.UID, res.Type)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		_, exist := repeatedM[res.Value]
		if exist {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDonate cost repeated check failed token:%d is repeated", c.Msg.UID, res.Value)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedM[res.Value] = struct{}{}

		if res.Count > goxml.GetData().GuildSandTableConfigInfoM.DonateSingleMaxNum {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDonate token:%d count:%d more than cost limit count:%d ", c.Msg.UID, res.Value, res.Count, goxml.GetData().GuildSandTableConfigInfoM.DonateSingleMaxNum)
			return c.Error(smsg, uint32(cret.RET_GST_USER_ONCE_DONATE_COUNT_IS_ERROR))
		}
		totalCount += res.Count
		if totalCount > goxml.GetData().GuildSandTableConfigInfoM.DonateSingleMaxNum {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDonate total count:%d more than cost limit count:%d ", c.Msg.UID, totalCount, goxml.GetData().GuildSandTableConfigInfoM.DonateSingleMaxNum)
			return c.Error(smsg, uint32(cret.RET_GST_USER_ONCE_DONATE_COUNT_IS_ERROR))
		}
	}

	if !gstM.IsCrossConnected() {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonate gst cross is not Connected", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	consumeRet := c.User.Consume(c.Srv, cmsg.Cost, uint32(log.RESOURCE_CHANGE_REASON_GUILD_BUILD_DONATE_COST), 0)
	if consumeRet != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GSTGuildBuildDonate user:%d reduce cost:%v failed", c.Msg.UID, cmsg.Cost)
		return c.Error(smsg, consumeRet)
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGuildBuildDonate, c.Msg.UID, &l2c.L2CS_GSTGuildBuildDonate{
		BuildType: cmsg.BuildType,
		Cost:      cmsg.Cost,
	}) {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonate: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGuildBuildDonateRankCommand struct {
	base.UserCommand
}

func (c *C2LGSTGuildBuildDonateRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGuildBuildDonateRankCommand) Error(msg *cl.L2C_GSTGuildBuildDonateRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGuildBuildDonateRank, msg)
	return false
}

func (c *C2LGSTGuildBuildDonateRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGuildBuildDonateRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGuildBuildDonateRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGuildBuildDonateRank: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGuildBuildDonateRank{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonateRank: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonateRank: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonateRank: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonateRank guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonateRank gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDonateRank cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGuildBuildDonateRank, c.Msg.UID, &l2c.L2CS_GSTGuildBuildDonateRank{}) {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDonateRank: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGuildBuildDispatchHeroCommand struct {
	base.UserCommand
}

func (c *C2LGSTGuildBuildDispatchHeroCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGuildBuildDispatchHeroCommand) Error(msg *cl.L2C_GSTGuildBuildDispatchHero, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGuildBuildDispatchHero, msg)
	return false
}

//nolint:funlen
func (c *C2LGSTGuildBuildDispatchHeroCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGuildBuildDispatchHero{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGuildBuildDispatchHero Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGuildBuildDispatchHero: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGuildBuildDispatchHero{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDispatchHero: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDispatchHero: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDispatchHero gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDispatchHero get Gst user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_USER_NOT_SIGN))
	}

	heroesLen := len(cmsg.HeroID)
	maxBlessedCount := c.User.GetMaxBlessedHeroCnt()
	// 等于0为下掉对应建筑的英雄
	if uint32(heroesLen) > 0 {
		if !goxml.GetData().GuildSandTableHeroWorkInfoM.DispatchHeroNumCheck(cmsg.BuildType, uint32(heroesLen), maxBlessedCount) {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero check num failed build id:%d hero len:%d maxBlessedCount:%d", c.Msg.UID, cmsg.BuildType, heroesLen, maxBlessedCount)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	heroSysIds := make([]uint32, 0, len(cmsg.HeroID))
	repeatedCheck := make(map[uint64]struct{})
	sysHeroIDCheck := make(map[uint32]struct{})
	dispatchHero := make([]*cl.GSTBuildDispatchHero, 0, len(cmsg.HeroID))
	for _, heroID := range cmsg.HeroID {
		_, exist := repeatedCheck[heroID]
		if exist {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero heroID:%d is repeated", c.Msg.UID, heroID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		repeatedCheck[heroID] = struct{}{}

		hero := c.User.HeroManager().Get(heroID)
		if hero == nil {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero hero:%d is nil", c.Msg.UID, heroID)
			return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
		}

		if hero.IsTagMarterial() {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero hero id:%d is freedom hero", c.Msg.UID, heroID)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_IS_FREEDOM_HERO))
		}

		_, exist = sysHeroIDCheck[hero.GetHeroSysID()]
		if exist {
			l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero heroID:%d sysId:%d is repeated", c.Msg.UID, heroID, hero.GetHeroSysID())
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		heroSysIds = append(heroSysIds, hero.GetHeroSysID())
		dispatchHero = append(dispatchHero, &cl.GSTBuildDispatchHero{
			SysId:  hero.GetHeroSysID(),
			Star:   hero.GetStar(),
			HeroId: hero.GetHid(),
		})
	}

	if !c.User.GstCheckBuildDispatch(heroSysIds) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildDispatchHero GstCheckBuildDispatch is failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_DISPATCH_HERO_OTHER_BUILD_REPEATER))
	}

	dispatchHeroes := gstUser.ExchangeDispatchBuildHero(cmsg.BuildType, dispatchHero)
	gstM.SetChange(gstUser)

	crossMsg := &l2c.L2CS_GSTGuildBuildDispatchHero{
		BuildDispatchHeroes: dispatchHeroes,
		BuildType:           cmsg.BuildType,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGuildBuildDispatchHero, c.Msg.UID, crossMsg) {
		l4g.Errorf("user: %d C2L_GSTGuildBuildDispatchHero: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser.SetDispatchBuildHero(dispatchHeroes)
	gstM.SetChange(gstUser)

	return true
}

type C2LGSTGuildBuildGetTaskDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTGuildBuildGetTaskDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGuildBuildGetTaskDataCommand) Error(msg *cl.L2C_GSTGuildBuildGetTaskData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGuildBuildGetTaskData, msg)
	return false
}

func (c *C2LGSTGuildBuildGetTaskDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGuildBuildGetTaskData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGuildBuildGetTaskData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGuildBuildGetTaskData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGuildBuildGetTaskData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetTaskData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetTaskData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGuildBuildGetTaskData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildGetTaskData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetTaskData gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGuildBuildGetTaskData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetTaskData get Gst user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_USER_NOT_SIGN))
	}

	crossMsg := &l2c.L2CS_GSTGuildBuildGetTaskData{}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGuildBuildGetTaskData, c.Msg.UID, crossMsg) {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetTaskData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGuildBuildRecvTaskAwardCommand struct {
	base.UserCommand
}

func (c *C2LGSTGuildBuildRecvTaskAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGuildBuildRecvTaskAwardCommand) Error(msg *cl.L2C_GSTGuildBuildRecvTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGuildBuildRecvTaskAward, msg)
	return false
}

func (c *C2LGSTGuildBuildRecvTaskAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGuildBuildRecvTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGuildBuildRecvTaskAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTGuildBuildRecvTaskAward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGuildBuildRecvTaskAward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildRecvTaskAward: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildRecvTaskAward: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGuildBuildRecvTaskAward: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGuildBuildRecvTaskAward guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGuildBuildRecvTaskAward gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGuildBuildRecvTaskAward cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Errorf("user: %d C2L_GSTGuildBuildRecvTaskAward get Gst user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_USER_NOT_SIGN))
	}

	crossMsg := &l2c.L2CS_GSTGuildBuildRecvTaskAward{
		TaskIds: cmsg.TaskIds,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGuildBuildRecvTaskAward, c.Msg.UID, crossMsg) {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetTaskData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGroupUsersRankCommand struct {
	base.UserCommand
}

func (c *C2LGSTGroupUsersRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGroupUsersRankCommand) Error(msg *cl.L2C_GSTGroupUsersRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGroupUsersRank, msg)
	return false
}

func (c *C2LGSTGroupUsersRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGroupUsersRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGroupUsersRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTGroupUsersRank: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGroupUsersRank{
		Ret:    uint32(cret.RET_OK),
		RankId: cmsg.RankId,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	numberType := goxml.GstGroupRank2NumberType[cmsg.RankId]
	if numberType == 0 {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRank: rankId error. rankId:%d", c.Msg.UID, cmsg.RankId)
		return c.Error(smsg, uint32(cret.RET_COUNT_NOT_ENOUGH))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRank: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRank: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGroupUsersRank: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGroupUsersRank guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRank gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGroupUsersRank cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGroupUsersRank, c.Msg.UID, &l2c.L2CS_GSTGroupUsersRank{Req: cmsg}) {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRankLike: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTGroupUsersRankLikeCommand struct {
	base.UserCommand
}

func (c *C2LGSTGroupUsersRankLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGroupUsersRankLikeCommand) Error(msg *cl.L2C_GSTGroupUsersRankLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGroupUsersRankLike, msg)
	return false
}

func (c *C2LGSTGroupUsersRankLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGroupUsersRankLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGroupUsersRankLike Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTGroupUsersRankLike: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGroupUsersRankLike{
		Ret:    uint32(cret.RET_OK),
		RankId: cmsg.RankId,
		Id:     cmsg.Id,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	numberType := goxml.GstGroupRank2NumberType[cmsg.RankId]
	if numberType == 0 {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRankLike: rankId error. rankId:%d", c.Msg.UID, cmsg.RankId)
		return c.Error(smsg, uint32(cret.RET_COUNT_NOT_ENOUGH))
	}

	retCode, _, _ := c.User.CheckNumByType(numberType, 1, c.Srv)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRankLike: failed.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_COUNT_NOT_ENOUGH))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRankLike: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRankLike: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGroupUsersRankLike: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRankLike gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGroupUsersRankLike cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGroupUsersRankLike guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGroupUsersRankLike, c.Msg.UID, &l2c.L2CS_GSTGroupUsersRankLike{Req: cmsg}) {
		l4g.Errorf("user: %d C2L_GSTGroupUsersRankLike: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTArenaVoteCommand struct {
	base.UserCommand
}

func (c *C2LGSTArenaVoteCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTArenaVoteCommand) Error(msg *cl.L2C_GSTArenaVote, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTArenaVote, msg)
	return false
}

func (c *C2LGSTArenaVoteCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTArenaVote{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTArenaVote Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTArenaVote: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTArenaVote{
		Ret: uint32(cret.RET_OK),
	}

	// 屏蔽老的擂台赛
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))

	/*
		if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
			l4g.Errorf("function not open, uid:%d", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
		}

		guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
		if !ok {
			l4g.Errorf("user: %d C2L_GSTArenaVote: failed, guildManager not exist.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}

		guildUser := guildM.GetGuildUser(c.User.ID())
		if guildUser == nil {
			l4g.Errorf("user: %d C2L_GSTArenaVote: guildUser not exist.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
		}

		guild := guildM.GetGuildByID(guildUser.GuildID())
		if guild == nil {
			l4g.Errorf("user:%d C2L_GSTArenaVote: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
			return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
		}

		gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
		if !ok {
			l4g.Errorf("user: %d C2L_GSTArenaVote gstManager not exist.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
		}

		if gstM.CrossStateNil() {
			l4g.Errorf("user:%d C2L_GSTArenaVote cross state is nil", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
		}

		if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
			l4g.Errorf("user:%d C2L_GSTArenaVote guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
			return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
		}

		if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTArenaVote, c.Msg.UID, &l2c.L2CS_GSTArenaVote{Req: cmsg}) {
			l4g.Errorf("user: %d C2L_GSTArenaVote: cross maintain", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
		}
		return true
	*/
}

type C2LGSTGetArenaVoteRecordCommand struct {
	base.UserCommand
}

func (c *C2LGSTGetArenaVoteRecordCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTGetArenaVoteRecordCommand) Error(msg *cl.L2C_GSTGetArenaVoteRecord, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTGetArenaVoteRecord, msg)
	return false
}

func (c *C2LGSTGetArenaVoteRecordCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTGetArenaVoteRecord{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTGetArenaVoteRecord Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GSTGetArenaVoteRecord: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTGetArenaVoteRecord{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetArenaVoteRecord: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTGetArenaVoteRecord: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTGetArenaVoteRecord: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTGetArenaVoteRecord gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTGetArenaVoteRecord cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTGetArenaVoteRecord guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTGetArenaVoteRecord, c.Msg.UID, &l2c.L2CS_GSTGetArenaVoteRecord{
		GuildId: cmsg.GuildId,
	}) {
		l4g.Errorf("user: %d C2L_GSTGetArenaVoteRecord: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

//type C2LGSTBossGetCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGSTBossGetCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGSTBossGetCommand) Error(msg *cl.L2C_GSTBossGet, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTBossGet, msg)
//	return false
//}
//
//func (c *C2LGSTBossGetCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GSTBossGet{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GSTBossGet Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GSTBossGet: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GSTBossGet{
//		Ret: uint32(cret.RET_OK),
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
//		l4g.Errorf("C2L_GSTBossGet function not open, uid:%d", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GSTBossGet: failed, guildManager not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GSTBossGet: guildUser not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	guild := guildM.GetGuildByID(guildUser.GuildID())
//	if guild == nil {
//		l4g.Errorf("user:%d C2L_GSTBossGet: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
//		l4g.Errorf("user:%d C2L_GSTBossGet guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
//		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
//	}
//
//	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
//	if gstM == nil {
//		l4g.Errorf("user:%d C2L_GSTBossGet: gstM not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//
//	if gstM.CrossStateNil() {
//		l4g.Errorf("user:%d C2L_GSTBossGet cross state is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//
//	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTBossGet, c.Msg.UID, &l2c.L2CS_GSTBossGet{}) {
//		l4g.Errorf("user: %d C2L_GSTBossGet: cross maintain", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//	return true
//}
//
//type C2LGSTBossFightCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGSTBossFightCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGSTBossFightCommand) Error(msg *cl.L2C_GSTBossFight, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTBossFight, msg)
//	return false
//}
//
//func (c *C2LGSTBossFightCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GSTBossFight{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GSTBossFight Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GSTBossFight: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GSTBossFight{
//		Ret:    uint32(cret.RET_OK),
//		BossId: cmsg.BossId,
//		Sweep:  cmsg.Sweep,
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_BOSS), c.Srv) {
//		l4g.Errorf("C2L_GSTBossFight function not open, uid:%d", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//	monsterInfo := goxml.GetData().GuildSandTableSiegeMonsterInfoM.GetRecordById(cmsg.BossId)
//	if monsterInfo == nil {
//		l4g.Errorf("user: %d C2L_GSTBossFight: GuildSandTableSiegeMonsterInfoM not exist bossid:%d", c.Msg.UID, cmsg.BossId)
//		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
//	}
//	//资源数量恢复
//	ret := c.User.GST().RecoverChallengeItem(c.Srv)
//	if ret != uint32(cret.RET_OK) && ret != uint32(cret.RET_GST_BOSS_USER_NOT_SIGN) {
//		l4g.Errorf("user: %d C2L_GSTBossFight: RecoverChallengeItem fail. bossid:%d", c.Msg.UID, cmsg.BossId)
//		return c.Error(smsg, ret)
//	}
//	//检查挑战令数量
//	challengeItem := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeItem
//	cost := &cl.Resource{
//		Type:  challengeItem.Type,
//		Value: challengeItem.Value,
//		Count: 1,
//	}
//	//扣除挑战令，失败再返回
//	ret = c.User.Consume(c.Srv, []*cl.Resource{cost}, uint32(log.RESOURCE_CHANGE_REASON_GST_BOSS_USE_CHALLENGE), 0)
//	if ret != uint32(cret.RET_OK) {
//		l4g.Errorf("C2L_GSTBossFight user: %d clearToken: cost token failed. retCode: %d", c.Msg.UID, ret)
//		return c.Error(smsg, ret)
//	}
//	damage := uint64(0)
//	ReportId := ""
//	if !cmsg.Sweep {
//		//本地进行战斗
//		battleReport, retCode := c.User.AttackGSTBoss(c.Srv, monsterInfo.MonsterGroup, common.FORMATION_ID(monsterInfo.FormationId), cmsg.ClientData)
//		if retCode != cret.RET_OK {
//			l4g.Errorf("C2L_GSTBossFight user:%d AttackGSTBoss error code:%d", c.User.ID(), retCode)
//			return c.Error(smsg, uint32(retCode))
//		}
//		damage = battleReport.Reports[0].GetTotalHurt()
//		ReportId = battleReport.Id
//	}
//	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
//	if gstM == nil {
//		l4g.Errorf("user:%d C2L_GSTBossFight: gstM not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//
//	if gstM.CrossStateNil() {
//		l4g.Errorf("user:%d C2L_GSTBossFight cross state is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTBossFight, c.User.ID(), &l2c.L2CS_GSTBossFight{
//		BossId:   cmsg.BossId,
//		Sweep:    cmsg.Sweep,
//		Damage:   damage,
//		ReportId: ReportId,
//	}) {
//		l4g.Errorf("user: %d C2L_GSTBossFight cross maintain", c.User.ID())
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//	return c.ResultOK(smsg.Ret)
//}
//
//type C2LGSTBossAwardCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGSTBossAwardCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGSTBossAwardCommand) Error(msg *cl.L2C_GSTBossAward, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTBossGet, msg)
//	return false
//}
//
//func (c *C2LGSTBossAwardCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GSTBossAward{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GSTBossAward Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GSTBossAward: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GSTBossAward{
//		Ret: uint32(cret.RET_OK),
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_BOSS), c.Srv) {
//		l4g.Errorf("C2L_GSTBossAward function not open, uid:%d", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GSTBossAward: failed, guildManager not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GSTBossAward: guildUser not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	guild := guildM.GetGuildByID(guildUser.GuildID())
//	if guild == nil {
//		l4g.Errorf("user:%d C2L_GSTBossAward: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
//		l4g.Errorf("user:%d C2L_GSTBossAward guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
//		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
//	}
//	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
//	if gstM == nil {
//		l4g.Errorf("user:%d C2L_GSTBossAward: gstM not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//
//	if gstM.CrossStateNil() {
//		l4g.Errorf("user:%d C2L_GSTBossAward cross state is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTBossAward, c.Msg.UID, &l2c.L2CS_GSTBossAward{}) {
//		l4g.Errorf("user: %d C2L_GSTBossAward: cross maintain", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//
//	return true
//}
//
//type C2LGSTBossRankCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGSTBossRankCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGSTBossRankCommand) Error(msg *cl.L2C_GSTBossRank, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTBossGet, msg)
//	return false
//}
//
//func (c *C2LGSTBossRankCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GSTBossRank{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GSTBossRank Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GSTBossRank: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GSTBossRank{
//		Ret: uint32(cret.RET_OK),
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_BOSS), c.Srv) {
//		l4g.Errorf("C2L_GSTBossRank function not open, uid:%d", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GSTBossRank: failed, guildManager not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GSTBossRank: guildUser not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	guild := guildM.GetGuildByID(guildUser.GuildID())
//	if guild == nil {
//		l4g.Errorf("user:%d C2L_GSTBossRank: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
//		l4g.Errorf("user:%d C2L_GSTBossRank guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
//		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
//	}
//	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
//	if gstM == nil {
//		l4g.Errorf("user:%d C2L_GSTBossRank: gstM not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//
//	if gstM.CrossStateNil() {
//		l4g.Errorf("user:%d C2L_GSTBossRank cross state is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTBossRank, c.Msg.UID, &l2c.L2CS_GSTBossRank{}) {
//		l4g.Errorf("user: %d C2L_GSTBossRank: cross maintain", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//	return true
//}
//
//type C2LGSTBossBuyChallengeCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGSTBossBuyChallengeCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGSTBossBuyChallengeCommand) Error(msg *cl.L2C_GSTBossBuyChallenge, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTBossGet, msg)
//	return false
//}
//
//func (c *C2LGSTBossBuyChallengeCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GSTBossBuyChallenge{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GSTBossBuyChallenge Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GSTBossBuyChallenge: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GSTBossBuyChallenge{
//		Ret:      uint32(cret.RET_OK),
//		BuyType:  cmsg.BuyType,
//		BuyCount: cmsg.BuyCount,
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_BOSS), c.Srv) {
//		l4g.Errorf("C2L_GSTBossBuyChallenge function not open, uid:%d", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GSTBossBuyChallenge: failed, guildManager not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GSTBossBuyChallenge: guildUser not exist.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	guild := guildM.GetGuildByID(guildUser.GuildID())
//	if guild == nil {
//		l4g.Errorf("user:%d C2L_GSTBossBuyChallenge: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
//	if gstM == nil {
//		l4g.Errorf("user:%d C2L_GSTBossBuyChallenge: gstM not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//
//	if gstM.CrossStateNil() {
//		l4g.Errorf("user:%d C2L_GSTBossBuyChallenge cross state is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
//	}
//	if cmsg.BuyCount == 0 {
//		l4g.Errorf("user: %d C2L_GSTBossBuyChallenge: buy count error, buyCount: %d", c.Msg.UID, cmsg.BuyCount)
//		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
//	}
//	if cmsg.BuyType != uint32(common.GST_BOSS_BUY_TYPE_GBBT_DIAMOND) && cmsg.BuyType != uint32(common.GST_BOSS_BUY_TYPE_GBBT_ITEM) {
//		l4g.Errorf("user: %d C2L_GSTBossBuyChallenge: buy type error, buyType: %d", c.Msg.UID, cmsg.BuyType)
//		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
//	}
//	//资源数量恢复
//	ret := c.User.GST().RecoverChallengeItem(c.Srv)
//	if ret != uint32(cret.RET_OK) {
//		l4g.Errorf("user: %d C2L_GSTBossBuyChallenge: RecoverChallengeItem fail. BuyType:%d BuyCount:%d", c.Msg.UID, cmsg.BuyType, cmsg.BuyCount)
//		return c.Error(smsg, ret)
//	}
//
//	challengeItem := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeItem
//
//	var awards, costs []*cl.Resource
//	var numSummary *cl.NumInfo
//	if cmsg.BuyType == uint32(common.DISORDER_LAND_BUY_TYPE_DLBT_DIAMOND) {
//		smsg.Ret, costs, numSummary = c.User.CheckNumByType(uint32(common.PURCHASEID_GST_BOSS_BUY_CHALLENGE_COUNT), cmsg.BuyCount)
//		if smsg.Ret != uint32(cret.RET_OK) {
//			l4g.Errorf("user: %d C2L_GSTBossBuyChallenge: receive worship award failed. count not enough", c.Msg.UID)
//			return c.Error(smsg, smsg.Ret)
//		}
//		awards = []*cl.Resource{
//			goxml.GenSimpleResource(challengeItem.Type, challengeItem.Value, cmsg.BuyCount),
//		}
//	} else {
//		boxItem := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeBoxItem
//		costs = []*cl.Resource{
//			goxml.GenSimpleResource(boxItem.Type, boxItem.Value, cmsg.BuyCount),
//		}
//		exchangeNum := goxml.GetData().GuildSandTableConfigInfoM.SiegeChallengeBoxExchangeNum
//		awards = []*cl.Resource{
//			goxml.GenSimpleResource(challengeItem.Type, challengeItem.Value, exchangeNum*cmsg.BuyCount),
//		}
//	}
//	smsg.Ret, _ = c.User.Trade(c.Srv, costs, awards, uint32(log.RESOURCE_CHANGE_REASON_GST_BOSS_BUY_CHALLENGE), 0)
//	if smsg.Ret != uint32(cret.RET_OK) {
//		l4g.Errorf("user: %d C2L_GSTBossBuyChallenge: trade error, ret: %d", c.Msg.UID, smsg.Ret)
//		return c.Error(smsg, smsg.Ret)
//	}
//	smsg.ChallengeRecoverTime = c.User.GST().GetRefreshTime()
//	if numSummary != nil {
//		c.User.AddNumByType(uint32(common.PURCHASEID_GST_BOSS_BUY_CHALLENGE_COUNT), numSummary)
//	}
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTBossBuyChallenge, smsg)
//	c.User.LogGSTBossBuyChallenge(c.Srv, cmsg.BuyType, cmsg.BuyCount, smsg.ChallengeRecoverTime)
//
//	return true
//}

type C2LGSTDragonGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonGetDataCommand) Error(msg *cl.L2C_GSTDragonGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonGetData, msg)
	return false
}

func (c *C2LGSTDragonGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonGetData: cmsg %+v", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonGetData{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("user: %d C2L_GSTDragonGetData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonGetData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonGetData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonGetData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonGetData: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonGetData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonGetData, c.Msg.UID, &l2c.L2CS_GSTDragonGetData{}) {
		l4g.Errorf("user: %d C2L_GSTDragonGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonGetCultivationCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonGetCultivationCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonGetCultivationCommand) Error(msg *cl.L2C_GSTDragonGetCultivation, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonGetCultivation, msg)
	return false
}

func (c *C2LGSTDragonGetCultivationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonGetCultivation{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonGetCultivation Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonGetCultivation: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonGetCultivation{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonGetCultivation: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonGetCultivation: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonGetCultivation: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonGetCultivation: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonGetCultivation: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonGetCultivation cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonGetCultivation, c.Msg.UID, &l2c.L2CS_GSTDragonGetCultivation{}) {
		l4g.Errorf("user: %d C2L_GSTDragonGetCultivation: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonShowCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonShowCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonShowCommand) Error(msg *cl.L2C_GSTDragonShow, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonShow, msg)
	return false
}

func (c *C2LGSTDragonShowCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonShow{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonShow Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonShow: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonShow{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonShow: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonShow: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	// 仅会长可操作
	if !guildUser.IsGuildLeader() {
		l4g.Errorf("user: %d C2L_GSTDragonShow: not leader.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonShow: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonShow: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonShow: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonShow cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonShow, c.Msg.UID, &l2c.L2CS_GSTDragonShow{
		DragonPos: cmsg.DragonPos,
	}) {
		l4g.Errorf("user: %d C2L_GSTDragonShow: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonEvolveCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonEvolveCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonEvolveCommand) Error(msg *cl.L2C_GSTDragonEvolve, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonEvolve, msg)
	return false
}

func (c *C2LGSTDragonEvolveCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonEvolve{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonEvolve Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonEvolve: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonEvolve{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonEvolve: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonEvolve: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	// 仅会长可操作
	if !guildUser.IsGuildLeader() {
		l4g.Errorf("user: %d C2L_GSTDragonEvolve: not leader.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonEvolve: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonEvolve: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonEvolve: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonEvolve cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonEvolve, c.Msg.UID, &l2c.L2CS_GSTDragonEvolve{
		DragonPos: cmsg.DragonPos,
		EvolveId:  cmsg.EvolveId,
	}) {
		l4g.Errorf("user: %d C2L_GSTDragonEvolve: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonFightCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonFightCommand) Error(msg *cl.L2C_GSTDragonFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonFight, msg)
	return false
}

func (c *C2LGSTDragonFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonFight: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonFight{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonFight: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonFight: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonFight: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonFight: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonFight: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonFight cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonFightStart, c.User.ID(), &l2c.L2CS_GSTDragonFightStart{
		DragonPos:  cmsg.DragonPos,
		Sweep:      cmsg.Sweep,
		ClientData: cmsg.ClientData,
	}) {
		l4g.Errorf("user: %d C2L_GSTDragonFight: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonFightBuyCountCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonFightBuyCountCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonFightBuyCountCommand) Error(msg *cl.L2C_GSTDragonFightBuyCount, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonFightBuyCount, msg)
	return false
}

func (c *C2LGSTDragonFightBuyCountCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonFightBuyCount{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonFightBuyCount Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonFightBuyCount: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonFightBuyCount{
		Ret:   uint32(cret.RET_OK),
		Count: cmsg.Count,
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cmsg.Count == 0 {
		l4g.Errorf("user: %d C2L_GSTDragonFightBuyCount: count is 0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonFightBuyCount: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonFightBuyCount: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonFightBuyCount: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonFightBuyCount: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonFightBuyCount: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonFightBuyCount cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonFightBuyCountStart, c.Msg.UID, &l2c.L2CS_GSTDragonFightBuyCountStart{
		Count: cmsg.Count,
	}) {
		l4g.Errorf("user: %d C2L_GSTDragonFightBuyCount: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LGSTDragonFightUseTicketCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonFightUseTicketCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonFightUseTicketCommand) Error(msg *cl.L2C_GSTDragonFightUseTicket, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonFightUseTicket, msg)
	return false
}

func (c *C2LGSTDragonFightUseTicketCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonFightUseTicket{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonFightUseTicket Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonFightUseTicket: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonFightUseTicket{
		Ret:   uint32(cret.RET_OK),
		Count: cmsg.Count,
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cmsg.Count == 0 {
		l4g.Errorf("user: %d C2L_GSTDragonFightUseTicket: count is 0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonFightUseTicket: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonFightUseTicket: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonFightUseTicket: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonFightUseTicket: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonFightUseTicket: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonFightUseTicket cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	// 先消耗资源，返回时如果报错则返还资源
	costs := goxml.GetData().GuildSandTableConfigInfoM.GetDragonChallengeBuyItemCostBySize(cmsg.Count)
	smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GST_DRAGON_DIAMOND_BUY_CHALLENGE_COUNT), 0)
	if smsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("C2L_GSTDragonFightUseTicket user:%d consume award failed. trade failed ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	msg := &l2c.L2CS_GSTDragonFightUseTicket{
		Count: cmsg.Count,
		Costs: costs,
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonFightUseTicket, c.Msg.UID, msg) {
		l4g.Errorf("user: %d C2L_GSTDragonFightUseTicket: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonTaskGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonTaskGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonTaskGetDataCommand) Error(msg *cl.L2C_GSTDragonTaskGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonTaskGetData, msg)
	return false
}

func (c *C2LGSTDragonTaskGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonTaskGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonTaskGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonTaskGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonTaskGetData{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonTaskGetData: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonTaskGetData: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonTaskGetData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonTaskGetData guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonTaskGetData gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonTaskGetData cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonTaskGetData get Gst user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_USER_NOT_SIGN))
	}

	crossMsg := &l2c.L2CS_GSTDragonTaskGetData{}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonTaskGetData, c.Msg.UID, crossMsg) {
		l4g.Errorf("user: %d C2L_GSTDragonTaskGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonTaskAwardCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonTaskAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonTaskAwardCommand) Error(msg *cl.L2C_GSTDragonTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonTaskAward, msg)
	return false
}

func (c *C2LGSTDragonTaskAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonTaskAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonTaskAward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonTaskAward{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonTaskAward: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonTaskAward: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonTaskAward: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonTaskAward guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonTaskAward gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonTaskAward cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonTaskAward get Gst user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_USER_NOT_SIGN))
	}

	crossMsg := &l2c.L2CS_GSTDragonTaskAward{
		TaskIds: cmsg.TaskIds,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonTaskAward, c.Msg.UID, crossMsg) {
		l4g.Errorf("user: %d C2L_GSTGuildBuildGetTaskData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonRankCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonRankCommand) Error(msg *cl.L2C_GSTDragonRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonRank, msg)
	return false
}

func (c *C2LGSTDragonRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonRank: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonRank{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonRank: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonRank: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonRank: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonRank: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Errorf("user:%d C2L_GSTDragonRank: gstM not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonRank cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonRank, c.Msg.UID, &l2c.L2CS_GSTDragonRank{}) {
		l4g.Errorf("user: %d C2L_GSTDragonRank: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonSkillOperateCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonSkillOperateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonSkillOperateCommand) Error(msg *cl.L2C_GSTDragonSkillOperate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonSkillOperate, msg)
	return false
}

func (c *C2LGSTDragonSkillOperateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonSkillOperate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonSkillOperate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonSkillOperate: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonSkillOperate{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonSkillOperate: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonSkillOperate: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	// 可操作:会长、副会长、军团长
	if !guildUser.IsGstGuildManager() {
		l4g.Errorf("user: %d C2L_GSTDragonSkillOperate: not manager.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonSkillOperate: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonSkillOperate: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonSkillOperate gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonSkillOperate cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonSkillOperate, c.User.ID(), &l2c.L2CS_GSTDragonSkillOperate{
		GroundId: cmsg.GroundId,
	}) {
		l4g.Errorf("user: %d C2L_GSTDragonSkillOperate: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTDragonStrategySkillCommand struct {
	base.UserCommand
}

func (c *C2LGSTDragonStrategySkillCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTDragonStrategySkillCommand) Error(msg *cl.L2C_GSTDragonStrategySkill, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTDragonStrategySkill, msg)
	return false
}

func (c *C2LGSTDragonStrategySkillCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTDragonStrategySkill{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTDragonStrategySkill Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTDragonStrategySkill: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTDragonStrategySkill{
		Ret: uint32(cret.RET_OK),
	}

	// 龙战开启检查
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_DRAGON), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 公会战通用检查
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonStrategySkill: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTDragonStrategySkill: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	// 可操作:会长、副会长、军团长
	if !guildUser.IsGstGuildManager() {
		l4g.Errorf("user: %d C2L_GSTDragonStrategySkill: not leader.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTDragonStrategySkill: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !goxml.GetData().GuildSandTableConfigInfoM.GstCheckGuildLevel(guild.GetLevel()) {
		l4g.Errorf("user:%d C2L_GSTDragonStrategySkill: guild:%d guild level:%d check failed", c.Msg.UID, guild.ID(), guild.GetLevel())
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTDragonStrategySkill gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTDragonStrategySkill cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTDragonStrategySkill, c.User.ID(), &l2c.L2CS_GSTDragonStrategySkill{
		Count: cmsg.Count,
	}) {
		l4g.Errorf("user: %d C2L_GSTDragonSkillOperate: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTScorePreviewCommand struct {
	base.UserCommand
}

func (c *C2LGSTScorePreviewCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGSTScorePreviewCommand) Error(msg *cl.L2C_GSTScorePreview, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTScorePreview, msg)
	return false
}

func (c *C2LGSTScorePreviewCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GSTScorePreview{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTScorePreview Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTScorePreview: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTScorePreview{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTScorePreview: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTScorePreview: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTScorePreview gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTScorePreview cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTScorePreview, c.Msg.UID, &l2c.L2CS_GSTScorePreview{
		GuildId: cmsg.GuildId,
	}) {
		l4g.Errorf("user: %d C2L_GSTScorePreview: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildMobilizationGuildRankCommand struct {
	base.UserCommand
}

func (c *C2LGuildMobilizationGuildRankCommand) Return(msg *cl.L2C_GuildMobilizationGuildRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMobilizationGuildRank, msg)
	return false
}

func (c *C2LGuildMobilizationGuildRankCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GuildMobilizationGuildRank{}
	cmsg := &cl.C2L_GuildMobilizationGuildRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMobilizationGuildRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildMobilizationGuildRank: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationGuildRank: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildMobilizationGuildRank: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GuildMobilizationGuildRank: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildMobilizationGuildRank gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GuildMobilizationGuildRank cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GuildMobilizationGuildRank, c.Msg.UID, &l2c.L2CS_GuildMobilizationGuildRank{
		GuildId:  guildUser.GuildID(),
		RankType: cmsg.RankType,
	}) {
		l4g.Errorf("user: %d C2L_GuildMobilizationGuildRank: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTOreGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTOreGetDataCommand) Return(msg *cl.L2C_GSTOreGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTOreGetData, msg)
	return false
}

func (c *C2LGSTOreGetDataCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTOreGetData{}
	cmsg := &cl.C2L_GSTOreGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTOreGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTOreGetData: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_ORE), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreGetData: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTOreGetData: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTOreGetData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreGetData gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTOreGetData cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTOreGetData, c.Msg.UID, &l2c.L2CS_GSTOreGetData{}) {
		l4g.Errorf("user: %d C2L_GSTOreGetData: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTOreFightCommand struct {
	base.UserCommand
}

func (c *C2LGSTOreFightCommand) Return(msg *cl.L2C_GSTOreFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTOreFight, msg)
	return false
}

func (c *C2LGSTOreFightCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTOreFight{}
	cmsg := &cl.C2L_GSTOreFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTOreFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTOreFight: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_ORE), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreFight: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTOreFight: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTOreFight: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreFight gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTOreFight cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	if !cmsg.Sweep {
		c.User.CacheClientData(common.FUNCID_MODULE_GST_ORE, cmsg.ClientData)
		cmsg.ClientData = nil
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTOreFightStart, c.Msg.UID, &l2c.L2CS_GSTOreFightStart{
		Req: cmsg,
	}) {
		l4g.Errorf("user: %d C2L_GSTOreFight: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTOreBuyTimesCommand struct {
	base.UserCommand
}

func (c *C2LGSTOreBuyTimesCommand) Return(msg *cl.L2C_GSTOreBuyTimes, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTOreBuyTimes, msg)
	return false
}

func (c *C2LGSTOreBuyTimesCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTOreBuyTimes{}
	cmsg := &cl.C2L_GSTOreBuyTimes{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTOreBuyTimes Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTOreBuyTimes: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_ORE), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreBuyTimes: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTOreBuyTimes: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTOreBuyTimes: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreBuyTimes gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTOreBuyTimes cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if cmsg.Type == 1 {
		result, costs, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_GST_ORE_FIGHT), cmsg.BuyCount, c.Srv)
		if result != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_GSTOreBuyTimes error", c.Msg.UID)
			return c.Return(smsg, result)
		}
		if len(costs) > 0 {
			result = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GST_ORE_BUY_FIGHT_TIMES), 0)
			if result != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2L_GSTOreBuyTimes: consume err", c.Msg.UID)
				return c.Return(smsg, result)
			}
		}
		c.User.AddNumByType(uint32(common.PURCHASEID_GST_ORE_FIGHT), numSummary)
		//nolint:mnd
	} else if cmsg.Type == 2 {
		result, costs, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_GST_ORE_ASSIST), cmsg.BuyCount, c.Srv)
		if result != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_GSTOreBuyTimes error %d", c.Msg.UID, result)
			return c.Return(smsg, result)
		}
		if len(costs) > 0 {
			result = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GST_ORE_BUY_ASSIST_TIMES), 0)
			if result != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2L_GSTOreBuyTimes: consume err", c.Msg.UID)
				return c.Return(smsg, result)
			}
		}
		c.User.AddNumByType(uint32(common.PURCHASEID_GST_ORE_ASSIST), numSummary)
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTOreBuyTimes, c.Msg.UID, &l2c.L2CS_GSTOreBuyTimes{
		Req: cmsg,
	}) {
		l4g.Errorf("user: %d C2L_GSTOreBuyTimes: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTOreSearchAssistCommand struct {
	base.UserCommand
}

func (c *C2LGSTOreSearchAssistCommand) Return(msg *cl.L2C_GSTOreSearchAssist, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTOreSearchAssist, msg)
	return false
}

func (c *C2LGSTOreSearchAssistCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTOreSearchAssist{}
	cmsg := &cl.C2L_GSTOreSearchAssist{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTOreSearchAssist Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTOreSearchAssist: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_ORE), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreSearchAssist: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTOreSearchAssist: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTOreSearchAssist: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreSearchAssist gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() || !gstM.IsCrossConnected() {
		l4g.Errorf("user:%d C2L_GSTOreSearchAssist cross err", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTOreSearchAssist, c.Msg.UID, &l2c.L2CS_GSTOreSearchAssist{}) {
		l4g.Errorf("user: %d C2L_GSTOreSearchAssist: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTOreOccupyCommand struct {
	base.UserCommand
}

func (c *C2LGSTOreOccupyCommand) Return(msg *cl.L2C_GSTOreOccupy, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTOreOccupy, msg)
	return false
}

func (c *C2LGSTOreOccupyCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTOreOccupy{}
	cmsg := &cl.C2L_GSTOreOccupy{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTOreOccupy Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTOreOccupy: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_ORE), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreOccupy: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTOreOccupy: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTOreOccupy: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreOccupy gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() || !gstM.IsCrossConnected() {
		l4g.Errorf("user:%d C2L_GSTOreOccupy cross err", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTOreOccupy, c.Msg.UID, &l2c.L2CS_GSTOreOccupy{
		Req: cmsg,
	}) {
		l4g.Errorf("user: %d C2L_GSTOreOccupy: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTOreGetOreDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTOreGetOreDataCommand) Return(msg *cl.L2C_GSTOreGetOreData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTOreGetOreData, msg)
	return false
}

func (c *C2LGSTOreGetOreDataCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTOreGetOreData{}
	cmsg := &cl.C2L_GSTOreGetOreData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTOreGetOreData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTOreGetOreData: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_ORE), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreGetOreData: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTOreGetOreData: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTOreGetOreData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTOreGetOreData gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() || !gstM.IsCrossConnected() {
		l4g.Errorf("user:%d C2L_GSTOreGetOreData cross err", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTOreGetOreData, c.Msg.UID, &l2c.L2CS_GSTOreGetOreData{
		Req: cmsg,
	}) {
		l4g.Errorf("user: %d C2L_GSTOreGetOreData: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTTechGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGSTTechGetDataCommand) Return(msg *cl.L2C_GSTTechGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTTechGetData, msg)
	return false
}

func (c *C2LGSTTechGetDataCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTTechGetData{}
	cmsg := &cl.C2L_GSTTechGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTTechGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTTechGetData: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_TECH), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechGetData: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTTechGetData: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTTechGetData: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechGetData gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTTechGetData cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTTechGetData, c.Msg.UID, &l2c.L2CS_GSTTechGetData{}) {
		l4g.Errorf("user: %d C2L_GSTTechGetData: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTTechDonateCommand struct {
	base.UserCommand
}

func (c *C2LGSTTechDonateCommand) Return(msg *cl.L2C_GSTTechDonate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTTechDonate, msg)
	return false
}

func (c *C2LGSTTechDonateCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTTechDonate{}
	cmsg := &cl.C2L_GSTTechDonate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTTechDonate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTTechDonate: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_TECH), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechDonate: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTTechDonate: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTTechDonate: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechDonate gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() || !gstM.IsCrossConnected() {
		l4g.Errorf("user:%d C2L_GSTTechDonate cross state error", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	oreResource := goxml.GetData().GuildSandTableConfigInfoM.OreSeasonInitItem
	costResource := make([]*cl.Resource, 0)
	costResource = append(costResource, &cl.Resource{
		Type:  oreResource.Type,
		Value: oreResource.Value,
		Count: cmsg.DonateNum,
	})
	result := c.User.Consume(c.Srv, costResource, uint32(log.RESOURCE_CHANGE_REASON_GST_TECH_DONATE), 0)
	if result != uint32(ret.RET_OK) {
		l4g.Errorf("user:%d C2L_GSTTechDonate Consume err", c.Msg.UID)
		return c.Return(smsg, result)
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTTechDonate, c.Msg.UID, &l2c.L2CS_GSTTechDonate{
		TechId:    cmsg.TechId,
		DonateNum: cmsg.DonateNum,
	}) {
		l4g.Errorf("user: %d C2L_GSTTechDonate: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTTechTaskRewardCommand struct {
	base.UserCommand
}

func (c *C2LGSTTechTaskRewardCommand) Return(msg *cl.L2C_GSTTechTaskReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTTechTaskReward, msg)
	return false
}

func (c *C2LGSTTechTaskRewardCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTTechTaskReward{}
	cmsg := &cl.C2L_GSTTechTaskReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTTechTaskReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTTechTaskReward: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_TECH), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechTaskReward: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTTechTaskReward: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTTechTaskReward: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechTaskReward gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTTechTaskReward cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTTechTaskReward, c.Msg.UID, &l2c.L2CS_GSTTechTaskReward{
		TaskIds: cmsg.TaskIds,
	}) {
		l4g.Errorf("user: %d C2L_GSTTechTaskReward: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTTechGuildUserRankCommand struct {
	base.UserCommand
}

func (c *C2LGSTTechGuildUserRankCommand) Return(msg *cl.L2C_GSTTechGuildUserRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTTechGuildUserRank, msg)
	return false
}

func (c *C2LGSTTechGuildUserRankCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTTechGuildUserRank{}
	cmsg := &cl.C2L_GSTTechGuildUserRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTTechGuildUserRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTTechGuildUserRank: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_TECH), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechGuildUserRank: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTTechGuildUserRank: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTTechGuildUserRank: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechGuildUserRank gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTTechGuildUserRank cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTTechGuildUserRank, c.Msg.UID, &l2c.L2CS_GSTTechGuildUserRank{}) {
		l4g.Errorf("user: %d C2L_GSTTechGuildUserRank: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTSkillAssembleCommand struct {
	base.UserCommand
}

func (c *C2LGSTSkillAssembleCommand) Return(msg *cl.L2C_GSTSkillAssemble, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTSkillAssemble, msg)
	return false
}

func (c *C2LGSTSkillAssembleCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTSkillAssemble{}
	cmsg := &cl.C2L_GSTSkillAssemble{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTSkillAssemble Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTSkillAssemble: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_TECH), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTSkillAssemble: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTSkillAssemble: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !guildUser.IsGstGuildManager() {
		l4g.Errorf("user: %d C2L_GSTSkillAssemble: not leader. ", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTSkillAssemble: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTSkillAssemble gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTSkillAssemble cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTSkillAssemble, c.Msg.UID, &l2c.L2CS_GSTSkillAssemble{
		GroundId: cmsg.GroundId,
	}) {
		l4g.Errorf("user: %d C2L_GSTSkillAssemble: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGSTTechSignCommand struct {
	base.UserCommand
}

func (c *C2LGSTTechSignCommand) Return(msg *cl.L2C_GSTTechSign, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTTechSign, msg)
	return false
}

func (c *C2LGSTTechSignCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_GSTTechSign{}
	cmsg := &cl.C2L_GSTTechSign{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GSTTechSign Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GSTTechSign: cmsg:%s", c.Msg.UID, cmsg)

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GST_TECH), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechSign: failed, guildManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GSTTechSign: guildUser not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !guildUser.IsGstGuildManager() {
		l4g.Errorf("user: %d C2L_GSTTechSign: not leader. ", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GSTTechSign: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return c.Return(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GSTTechSign gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if gstM.CrossStateNil() {
		l4g.Errorf("user:%d C2L_GSTTechSign cross state is nil", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTTechSign, c.Msg.UID, &l2c.L2CS_GSTTechSign{
		TechId: cmsg.TechId,
	}) {
		l4g.Errorf("user: %d C2L_GSTTechSign: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type AsyncC2LGSTMessageEditReq struct {
	Smsg *cl.L2C_GSTMessageEdit
}

func (ar *AsyncC2LGSTMessageEditReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LGSTMessageEditReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTMessageEdit, ar.Smsg)
		return false
	}

	if !srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTMessageEdit, args.UID, &l2c.L2CS_GSTMessageEdit{
		Message: ar.Smsg.Message,
	}) {
		l4g.Errorf("user: %d C2L_GuildSendMail: cross maintain", args.UID)

		ar.Smsg.Ret = uint32(cret.RET_CROSS_MAINTAIN)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTMessageEdit, ar.Smsg)
		return false
	}
	return true
}
