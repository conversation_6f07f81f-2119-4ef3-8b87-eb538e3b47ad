package balancearena

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/balancearena"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaGetData), &C2LBalanceArenaGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaGetMatches), &C2LBalanceArenaGetMatchesCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaCardChoose), &C2LBalanceArenaCardChooseCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaCardCustomize), &C2LBalanceArenaCardCustomizeCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaTeamUp), &C2LBalanceArenaTeamUpCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaReward), &C2LBalanceArenaRewardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaGetRankList), &C2LBalanceArenaGetRankListCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaGetUserMatches), &C2LBalanceArenaGetUserMatchesCommand{}, state)
}

type C2LBalanceArenaGetDataCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaGetDataCommand) Error(msg *cl.L2C_BalanceArenaGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetData, msg)
	return false
}

func (c *C2LBalanceArenaGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaGetData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	msg := &l2c.L2CS_BalanceArenaGetData{
		UserSnapshot: c.User.NewUserSnapshot(0),
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetData, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaGetMatchesCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaGetMatchesCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaGetMatchesCommand) Error(msg *cl.L2C_BalanceArenaGetMatches, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetMatches, msg)
	return false
}

func (c *C2LBalanceArenaGetMatchesCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaGetMatches{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaGetMatches Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaGetMatches: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaGetMatches{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetMatches: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetMatches: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetMatches: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	msg := &l2c.L2CS_BalanceArenaGetMatches{
		PhaseType:   cmsg.PhaseType,
		BattleGroup: cmsg.BattleGroup,
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetMatches, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaGetMatches: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaCardChooseCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaCardChooseCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaCardChooseCommand) Error(msg *cl.L2C_BalanceArenaCardChoose, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardChoose, msg)
	return false
}

func (c *C2LBalanceArenaCardChooseCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaCardChoose{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaCardChoose Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaCardChoose: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaCardChoose{
		Ret:     uint32(cret.RET_OK),
		OpType:  cmsg.OpType,
		CardIds: cmsg.CardIds,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	// 检查玩法当前阶段
	state := manager.GetCrossState()
	if state == nil {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	phaseInfo := goxml.GetData().BalanceArenaPhaseInfoM.GetRecordById(state.GetStage())
	if phaseInfo == nil {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: phase info is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	if phaseInfo.PhaseType != uint32(common.BALANCE_ARENA_STAGE_BAS_SIGN) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: phase not sign. phaseType:%d", c.Msg.UID, phaseInfo.PhaseType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	msg := &l2c.L2CS_BalanceArenaCardChoose{
		OpType:  cmsg.OpType,
		CardIds: cmsg.CardIds,
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaCardChoose, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaCardChoose: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaCardCustomizeCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaCardCustomizeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaCardCustomizeCommand) Error(msg *cl.L2C_BalanceArenaCardCustomize, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardCustomize, msg)
	return false
}

func (c *C2LBalanceArenaCardCustomizeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaCardCustomize{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaCardCustomize Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaCardCustomize{
		Ret:      uint32(cret.RET_OK),
		CardType: cmsg.CardType,
		SysId:    cmsg.SysId,
		Hid:      cmsg.Hid,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	// 检查玩法当前阶段
	state := manager.GetCrossState()
	if state == nil {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: cross state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	phaseInfo := goxml.GetData().BalanceArenaPhaseInfoM.GetRecordById(state.GetStage())
	if phaseInfo == nil {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: phase info is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	if phaseInfo.PhaseType != uint32(common.BALANCE_ARENA_STAGE_BAS_SIGN) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: phase not sign. phaseType:%d", c.Msg.UID, phaseInfo.PhaseType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	msg := &l2c.L2CS_BalanceArenaCardCustomize{
		CardType: cmsg.CardType,
		SysId:    cmsg.SysId,
		Hid:      cmsg.Hid,
	}
	switch cmsg.CardType {
	case goxml.BalanceArenaCardTypeHero:
		// 检查英雄是否存在
		hero := c.User.HeroManager().Get(cmsg.Hid)
		if hero == nil {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: hero not exist. hid:%d", c.Msg.UID, cmsg.Hid)
			return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
		}
		msg.Star = hero.GetStar()

		// 使用英雄符文的条件：持有橙色专属技能->专属技能等级>0
		// 四橙/三橙一红 level = 1 二橙二红/一橙三红 level = 2  四红 level = 3
		_, level := hero.CalcHeroEmblemSkill(c.User)
		if level == 0 {
			// 英雄符文不符合要求，使用默认符文
			msg.Emblems = goxml.GetData().EmblemInfoM.GetDefaultSimpleEmblems(hero.GetHeroSysID())
		} else {
			// 使用英雄符文
			emblems := hero.GetDisplayEmblems(c.User)
			msg.Emblems = convertEmblems2SimpleEmblems(emblems, hero.GetHeroSysID())
		}
	case goxml.BalanceArenaCardTypeArtifact:
		// 检查神器是否存在
		artifact := c.User.ArtifactManager().GetArtifact(cmsg.SysId)
		if artifact == nil {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: artifact not exist. aid:%d", c.Msg.UID, cmsg.SysId)
			return c.Error(smsg, uint32(cret.RET_ARTIFACT_NOT_EXIST))
		}
		msg.Star = artifact.GetStar()
	default:
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: cardType error. cardType:%d", c.Msg.UID, cmsg.CardType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaCardCustomize, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaCardCustomize: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

func convertEmblems2SimpleEmblems(emblems []*character.Emblem, heroSysId uint32) []*cl.SimpleEmblemInfo {
	simpleEmblems := make([]*cl.SimpleEmblemInfo, 0, len(emblems))
	for _, emblem := range emblems {
		if emblem == nil || emblem.Data == nil {
			continue
		}
		simpleEmblems = append(simpleEmblems, &cl.SimpleEmblemInfo{
			SysId:        emblem.Data.SysId,
			AdditiveHero: heroSysId,
		})
	}
	return simpleEmblems
}

type C2LBalanceArenaTeamUpCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaTeamUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaTeamUpCommand) Error(msg *cl.L2C_BalanceArenaTeamUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaTeamUp, msg)
	return false
}

func (c *C2LBalanceArenaTeamUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaTeamUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaTeamUp Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaTeamUp: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaTeamUp{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	// 修改组队和报名都需要把组队发给跨服
	msg := &l2c.L2CS_BalanceArenaTeamUp{
		UserSnapshot: c.User.NewUserSnapshot(0),
		Teams:        cmsg.Teams,
	}

	// 检查队伍数量是否合法
	if uint32(len(cmsg.Teams)) != goxml.BalanceArenaTeamNum {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: team num error. num:%d", c.Msg.UID, len(cmsg.Teams))
		return c.Error(smsg, uint32(cret.RET_BALANCE_ARENA_TEAM_NUM_ERROR))
	}

	if cmsg.Sign {
		msg.OpType = goxml.BalanceArenaTeamUpTypeSign
	} else {
		msg.OpType = goxml.BalanceArenaTeamUpTypeChange
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaTeamUp, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaTeamUp: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaGetRankListCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaGetRankListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaGetRankListCommand) Error(msg *cl.L2C_BalanceArenaGetRankList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetRankList, msg)
	return false
}

func (c *C2LBalanceArenaGetRankListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaGetRankList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaGetRankList Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaGetRankList: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaGetRankList{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetRankList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetRankList: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetRankList: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	msg := &l2c.L2CS_BalanceArenaGetRankList{
		Req: cmsg,
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetRankList, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaGetRankList: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaRewardCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaRewardCommand) Error(msg *cl.L2C_BalanceArenaReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaReward, msg)
	return false
}

func (c *C2LBalanceArenaRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaReward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaReward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaReward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaReward: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaReward: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	msg := &l2c.L2CS_BalanceArenaReward{}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaReward, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaReward: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaGetUserMatchesCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaGetUserMatchesCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaGetUserMatchesCommand) Error(msg *cl.L2C_BalanceArenaGetUserMatches, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetUserMatches, msg)
	return false
}

func (c *C2LBalanceArenaGetUserMatchesCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaGetUserMatches{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaGetUserMatches Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaGetUserMatches: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaGetUserMatches{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetUserMatches: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetUserMatches: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetUserMatches: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	msg := &l2c.L2CS_BalanceArenaGetUserMatches{
		Uid: cmsg.GetUid(),
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetUserMatches, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaGetUserMatches: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}
