package balancearena

import (
	"app/logic/activity"
	"app/logic/activity/balancearena"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaGetData), &C2LBalanceArenaGetDataCommand{}, state)
}

type C2LBalanceArenaGetDataCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaGetDataCommand) Error(msg *cl.L2C_BalanceArenaGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetData, msg)
	return false
}

func (c *C2LBalanceArenaGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaGetData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	msg := &l2c.L2CS_BalanceArenaGetData{}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetData, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}
