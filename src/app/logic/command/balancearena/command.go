package balancearena

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/balancearena"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaGetData), &C2LBalanceArenaGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaCardChoose), &C2LBalanceArenaCardChooseCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaCardCustomize), &C2LBalanceArenaCardCustomizeCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_BalanceArenaTeamUp), &C2LBalanceArenaTeamUpCommand{}, state)
}

type C2LBalanceArenaGetDataCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaGetDataCommand) Error(msg *cl.L2C_BalanceArenaGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetData, msg)
	return false
}

func (c *C2LBalanceArenaGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaGetData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaGetData: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	msg := &l2c.L2CS_BalanceArenaGetData{}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaGetData, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaGetData: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaCardChooseCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaCardChooseCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaCardChooseCommand) Error(msg *cl.L2C_BalanceArenaCardChoose, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardChoose, msg)
	return false
}

func (c *C2LBalanceArenaCardChooseCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaCardChoose{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaCardChoose Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaCardChoose: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaCardChoose{
		Ret:     uint32(cret.RET_OK),
		OpType:  cmsg.OpType,
		CardIds: cmsg.CardIds,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	user := manager.GetUser(c.Msg.UID, nil)
	if user == nil {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: user not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	// 获取上一次的抽卡轮次
	lastDrawRound := user.GetData().DrawRound
	// 抽卡已结束
	if lastDrawRound >= goxml.BalanceArenaDrawRoundMax {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: draw round max. drawRound:%d", c.Msg.UID, lastDrawRound)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 根据抽卡轮次和仓库数据进行抽卡
	switch cmsg.OpType {
	case uint32(common.BALANCE_ARENA_CARD_OP_BACO_DRAW): // 第一次操作是抽牌
		if lastDrawRound > 0 {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: draw round max. drawRound:%d", c.Msg.UID, lastDrawRound)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	case uint32(common.BALANCE_ARENA_CARD_OP_BACO_CHOOSE_AND_DRAW): // 后面操作都是选牌+下一次抽牌
		// 无选牌操作
		if uint32(len(cmsg.CardIds)) != goxml.BalanceArenaDrawGroupNum {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: cardGroup is 0. opType:%d", c.Msg.UID, cmsg.OpType)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		// 至少是第二次抽牌
		if lastDrawRound == 0 {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: draw round max. drawRound:%d", c.Msg.UID, lastDrawRound)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	default:
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: opType error. opType:%d", c.Msg.UID, cmsg.OpType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	curDrawRound := lastDrawRound + 1
	switch curDrawRound {
	case goxml.BalanceArenaDrawRoundHero1, goxml.BalanceArenaDrawRoundHero2,
		goxml.BalanceArenaDrawRoundHero3, goxml.BalanceArenaDrawRoundHero4: // 英雄轮
		// 抽牌
		randomHeroes, ret := user.RandomDrawHeroes()
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: random draw heroes failed. ret:%d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}

		// 选牌并清空之前的牌池
		chooseHeroes, ret := user.ChooseDrawHeroResult(cmsg.CardIds)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: choose draw hero result failed. ret:%d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}
		if len(chooseHeroes) > 0 {
			user.GenerateDrawHeroes(chooseHeroes)
		}

		// 添加新的抽牌结果到牌池
		user.AddRandomDrawHeroes2Pool(randomHeroes)
		smsg.NextHeroGroups = randomHeroes
	case goxml.BalanceArenaDrawRoundArtifact1, goxml.BalanceArenaDrawRoundArtifact2: // 神器轮
		// 抽牌
		randomArtifacts, ret := user.RandomDrawArtifacts()
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: random draw artifacts failed. ret:%d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}

		// 选牌并清空之前的牌池
		chooseArtifacts, ret := user.ChooseDrawArtifactResult(cmsg.CardGroup)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardChoose: choose draw artifact result failed. ret:%d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}
		if len(chooseArtifacts) > 0 {
			user.GenerateDrawArtifacts(chooseArtifacts)
		}

		// 添加新的抽牌结果到牌池
		user.AddRandomDrawArtifacts2Pool(randomArtifacts)
		smsg.NextArtifactGroups = randomArtifacts
	}

	manager.SetChangeUser(user)

	smsg.DrawRound = curDrawRound
	smsg.AvailableHeroes = user.GetData().AvailableHeroes
	smsg.AvailableArtifacts = user.GetData().AvailableArtifacts

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardChoose, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaCardCustomizeCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaCardCustomizeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaCardCustomizeCommand) Error(msg *cl.L2C_BalanceArenaCardCustomize, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardCustomize, msg)
	return false
}

func (c *C2LBalanceArenaCardCustomizeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaCardCustomize{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaCardCustomize Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaCardCustomize{
		Ret:      uint32(cret.RET_OK),
		CardType: cmsg.CardType,
		SysId:    cmsg.SysId,
		Hid:      cmsg.Hid,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	user := manager.GetUser(c.Msg.UID, nil)
	if user == nil {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: user not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	switch cmsg.CardType {
	case goxml.BalanceArenaCardTypeHero:
		// 是否能自选
		if ret := user.CanCustomizeHero(cmsg.SysId); ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: cannot cutomize hero", c.Msg.UID)
			return c.Error(smsg, ret)
		}
		// 检查英雄是否存在
		hero := c.User.HeroManager().Get(cmsg.Hid)
		if hero == nil {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: hero not exist. hid:%d", c.Msg.UID, cmsg.Hid)
			return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
		}
		// 自选英雄
		user.GenerateCustomizeHeroes(c.User, hero)
	case goxml.BalanceArenaCardTypeArtifact:
		// 是否能自选
		if ret := user.CanCustomizeArtifact(cmsg.SysId); ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: cannot cutomize artifact", c.Msg.UID)
			return c.Error(smsg, ret)
		}

		// 检查神器是否存在
		artifact := c.User.ArtifactManager().GetArtifact(cmsg.SysId)
		if artifact == nil {
			l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: artifact not exist. aid:%d", c.Msg.UID, cmsg.SysId)
			return c.Error(smsg, uint32(cret.RET_ARTIFACT_NOT_EXIST))
		}
		// 自选神器
		user.GenerateCustomizeArtifacts(artifact)
	default:
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaCardCustomize: cardType error. cardType:%d", c.Msg.UID, cmsg.CardType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	manager.SetChangeUser(user)

	smsg.AvailableHeroes = user.GetData().AvailableHeroes
	smsg.AvailableArtifacts = user.GetData().AvailableArtifacts
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardCustomize, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LBalanceArenaTeamUpCommand struct {
	base.UserCommand
}

func (c *C2LBalanceArenaTeamUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBalanceArenaTeamUpCommand) Error(msg *cl.L2C_BalanceArenaTeamUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaTeamUp, msg)
	return false
}

func (c *C2LBalanceArenaTeamUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BalanceArenaTeamUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.C2L_BalanceArenaTeamUp Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d balanceArena.C2L_BalanceArenaTeamUp: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BalanceArenaTeamUp{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BALANCE_ARENA), c.Srv) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !manager.IsCrossConnected() {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	user := manager.GetUser(c.Msg.UID, nil)
	if user == nil {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: user not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	// 检查组队是否符合要求
	if ret := user.CheckTeams(cmsg.Teams); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d balanceArena.C2L_BalanceArenaTeamUp: check teams failed. ret:%d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	// 修改组队和报名都需要把组队发给跨服
	msg := &l2c.L2CS_BalanceArenaTeamUp{
		Teams: cmsg.Teams,
	}
	if cmsg.Sign {
		msg.OpType = goxml.BalanceArenaTeamUpTypeSign
	} else {
		msg.OpType = goxml.BalanceArenaTeamUpTypeChange
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaTeamUp, c.Msg.UID, msg) {
		l4g.Errorf("user:%d balanceArena.C2L_BalanceArenaTeamUp: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}
