package emblem

import (
	"app/goxml"
	"app/logic/character"
	aevent "app/logic/event"
	"app/protos/in/log"
	"context"
	"math"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	"app/logic/command/base"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemGet), &C2LEmblemGetCommand{}, state)                                   //获取纹章列表 NOLOG
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemWear), &C2LEmblemWearCommand{}, state)                                 //纹章穿戴替换卸下
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemLevelUp), &C2LEmblemLevelUpCommand{}, state)                           //纹章升级
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemDecompose), &C2LEmblemDecomposeCommand{}, state)                       //纹章分解
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemGrowTransfer), &C2LEmblemGrowTransferCommand{}, state)                 //纹章养成转移
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemSetAutoDecompose), &C2LEmblemSetAutoDecomposeCommand{}, state)         //纹章自动分解设置
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemCustomize), &C2LEmblemCustomizeCommand{}, state)                       //纹章定制
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemBuySlot), &C2LEmblemBuySlotCommand{}, state)                           //购买符文栏位
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemUpgrade), &C2LEmblemUpgradeCommand{}, state)                           //符文升阶
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemSuccinct), &C2LEmblemSuccinctCommand{}, state)                         //符文洗炼
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemSuccinctLockOrSave), &C2LEmblemSuccinctLockOrSaveCommand{}, state)     //符文洗炼结果锁定或保存
	cmds.Register(uint32(cl.ID_MSG_C2L_EmblemSuccinctItemConflate), &C2LEmblemSuccinctItemConflateCommand{}, state) //符文洗炼道具合成
}

type C2LEmblemGetCommand struct {
	base.UserCommand
}

func (c *C2LEmblemGetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemGetCommand) Error(msg *cl.L2C_EmblemGet, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemGet, msg)
	return false
}

func (c *C2LEmblemGetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemGet Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debugf("user: %d C2L_EmblemGet: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_EmblemGet{
		Ret: uint32(cret.RET_OK),
	}

	smsg.Emblems = c.User.EmblemManager().FlushAll()
	smsg.AutoRare = c.User.GetEmblemAutoDecomposeRare()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemGet, smsg)

	return true
}

type C2LEmblemWearCommand struct {
	base.UserCommand
}

func (c *C2LEmblemWearCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemWearCommand) Error(msg *cl.L2C_EmblemWear, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemWear, msg)
	return false
}

//nolint:funlen
func (c *C2LEmblemWearCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemWear{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemWear Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_EmblemWear: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_EmblemWear{
		Ret:  uint32(cret.RET_OK),
		Id:   cmsg.Id,
		Hid:  cmsg.Hid,
		Type: cmsg.Type,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EMBLEM), c.Srv) {
		l4g.Errorf("user: %d C2L_EmblemWear: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if len(cmsg.Id) > goxml.EmblemPosMax {
		l4g.Errorf("user: %d C2L_EmblemWear: id len:%d is more than max:%d", c.Msg.UID, len(cmsg.Id), goxml.EmblemPosMax)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	emblemM := c.User.EmblemManager()
	heroM := c.User.HeroManager()
	hero := heroM.Get(cmsg.Hid)
	if hero == nil {
		l4g.Errorf("user: %d C2L_EmblemWear hero not exist. hid:%d", c.Msg.UID, cmsg.Hid)
		return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
	}

	if hero.IsTagMarterial() {
		l4g.Errorf("user: %d C2L_EmblemWear: marterial hero cannot wear emblem. hid:%d",
			c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	switch smsg.Type {
	case uint32(common.WEAR_OP_WEAR):
		emblemWearPassInfos := make([]*emblemWearPassInfo, 0, goxml.EmblemPosMax)
		slotCheck := make(map[uint32]struct{})
		repeated := make(map[uint64]struct{})
		for _, id := range cmsg.Id {
			_, exist := repeated[id]
			if exist {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem id:%d is repeated", c.Msg.UID, id)
				return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
			}
			repeated[id] = struct{}{}

			emblem := emblemM.Get(id)
			if emblem == nil {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem to wear not exist. emblem id:%d", c.Msg.UID, id)
				return c.Error(smsg, uint32(cret.RET_EMBLEM_NOT_EXIST))
			}
			emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
			if emblemInfo == nil {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem cfg to wear not exist. emblem id:%d", c.Msg.UID, id)
				return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
			}

			_, exist = slotCheck[emblemInfo.Pos]
			if exist {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem has same slot:%d", c.Msg.UID, emblemInfo.Pos)
				return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
			}
			slotCheck[emblemInfo.Pos] = struct{}{}

			//原槽位上的纹章
			var heroOldEmblem *character.Emblem
			if takeOffID := hero.GetEmblemBySlot(emblemInfo.Pos); takeOffID > 0 {
				heroOldEmblem = emblemM.Get(takeOffID)
				if heroOldEmblem == nil {
					l4g.Errorf("user: %d [C2L_EmblemWear] emblem's hero do not found emblem.hero:%d, emblem id:%d",
						c.Msg.UID, cmsg.Hid, cmsg.Id)
					return c.Error(smsg, uint32(cret.RET_EMBLEM_WEAR_ERROR))
				}
			}

			if emblem.Data.Hid == cmsg.Hid {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem's take off hero is same with wear hero heroID:%d",
					c.Msg.UID, cmsg.Hid)
				return c.Error(smsg, uint32(cret.RET_EMBLEM_WEAR_ERROR))
			}
			//如果是从其它英雄身上替换下的。 记录原英雄
			emblemOldHero := c.User.HeroManager().Get(emblem.Data.Hid)
			if emblemOldHero != nil {
				_, exist = goxml.EmblemLegalPos[emblemInfo.Pos]
				if !exist {
					l4g.Errorf("user: %d [C2L_EmblemWear] emblem's hero do not found emblem.hero:%d, emblem id:%d",
						c.Msg.UID, cmsg.Hid, id)
					return c.Error(smsg, uint32(cret.RET_EMBLEM_WEAR_ERROR))
				}
			}

			if !c.checkSlotUnlock(emblemInfo.Pos, hero.GetDisplayLevel(c.User), hero.GetStar(), hero.GetDisplayStage(c.User)) {
				l4g.Errorf("user: %d [C2L_EmblemWear] request slot not unlock. slot:%d", c.Msg.UID, emblemInfo.Pos)
				return c.Error(smsg, uint32(cret.RET_EMBLEM_SLOT_NOT_UNLOCK))
			}

			PassInfo := &emblemWearPassInfo{
				slot:          emblemInfo.Pos,
				hero:          hero,
				emblem:        emblem,
				emblemOldHero: emblemOldHero,
				heroOldEmblem: heroOldEmblem,
			}
			emblemWearPassInfos = append(emblemWearPassInfos, PassInfo)
		}

		heroRepeated := make(map[uint64]*character.Hero)
		for _, info := range emblemWearPassInfos {
			if info.emblemOldHero != nil {
				id := info.emblemOldHero.GetHid()
				heroRepeated[id] = info.emblemOldHero
				info.emblemOldHero.TakeOffEmblem(c.User, info.slot)
			}

			hero.WearEmblem(c.User, info.slot, info.emblem)

			smsg.Emblems = append(smsg.Emblems, info.emblem.Data.Clone())

			if info.emblemOldHero != nil && info.heroOldEmblem != nil {
				info.emblemOldHero.WearEmblem(c.User, info.slot, info.heroOldEmblem)
			}
			if info.heroOldEmblem != nil {
				smsg.Emblems = append(smsg.Emblems, info.heroOldEmblem.Data.Clone())
			}
		}
		smsg.Heroes = append(smsg.Heroes, &cl.Hero{Data: hero.GetData().Clone()})
		for _, oldEmblemHero := range heroRepeated {
			if oldEmblemHero.GetData().Id == hero.GetData().Id {
				continue
			}
			smsg.Heroes = append(smsg.Heroes, &cl.Hero{Data: oldEmblemHero.GetData().Clone()})
			// 设置更新英雄属性的标识：false表示更新
			oldEmblemHero.SetHeroAttrChange()
		}

		c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)

	case uint32(common.WEAR_OP_TAKEOFF):
		var emblemTakeOffPassInfos []*emblemTakeOffPassInfo
		repeated := make(map[uint64]struct{})
		for _, id := range cmsg.Id {
			_, exist := repeated[id]
			if exist {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem id:%d is repeated", c.Msg.UID, id)
				return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
			}
			repeated[id] = struct{}{}
			takeOffPassInfo := &emblemTakeOffPassInfo{}
			slot := hero.GetEmblemSlot(id)
			_, exist = goxml.EmblemLegalPos[slot]
			if !exist {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem takeoff error, slot not found. hid:%d, emblem id:%d",
					c.Msg.UID, cmsg.Hid, id)
				return c.Error(smsg, uint32(cret.RET_EMBLEM_WEAR_ERROR))
			}
			takeOffPassInfo.slot = slot

			emblemUnWear := emblemM.Get(id)
			if emblemUnWear == nil {
				l4g.Errorf("user: %d [C2L_EmblemWear] emblem takeoff error, emblem not found. hid:%d, emblem:%d",
					c.Msg.UID, cmsg.Hid, id)
				return c.Error(smsg, uint32(cret.RET_EMBLEM_WEAR_ERROR))
			}
			if emblemUnWear.Data.Id != id {
				l4g.Errorf("user: %d [C2L_EmblemWear] client update id:%d not same with hero pos id:%d",
					c.Msg.UID, id, emblemUnWear.Data.Id)
				return c.Error(smsg, uint32(cret.RET_EMBLEM_WEAR_ERROR))
			}
			takeOffPassInfo.emblem = emblemUnWear
			emblemTakeOffPassInfos = append(emblemTakeOffPassInfos, takeOffPassInfo)
		}

		for _, info := range emblemTakeOffPassInfos {
			hero.TakeOffEmblem(c.User, info.slot)
			smsg.Heroes = append(smsg.Heroes, &cl.Hero{Data: hero.GetData().Clone()})
			smsg.Emblems = append(smsg.Emblems, info.emblem.Data.Clone())
		}
	default:
		l4g.Errorf("user: %d [C2L_EmblemWear] type not found. hid:%d, type:%d",
			c.Msg.UID, cmsg.Hid, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if hero.IsActiveLink4(c.User) {
		c.User.HandbookManager().GetHeroHandbookM().UpdateHeroLink4AttrId(hero.GetHeroSysID())
	}

	// 检查处理专属技能激活
	hero.CheckHandleActiveEmblemExclusive(c.Srv, c.User)

	hero.SetHeroAttrChange()
	c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemWear, smsg)

	return c.ResultOK(smsg.Ret)
}

type emblemWearPassInfo struct {
	slot          uint32
	hero          *character.Hero
	emblem        *character.Emblem
	emblemOldHero *character.Hero
	heroOldEmblem *character.Emblem
}

type emblemTakeOffPassInfo struct {
	slot   uint32
	emblem *character.Emblem
}

func (c *C2LEmblemWearCommand) checkSlotUnlock(slot, level, star, stage uint32) bool {
	var unlockID uint32
	switch slot {
	case goxml.EmblemPos1:
		unlockID = uint32(common.EMBLEM_CONFIG_SLOT_ONE_UNLOCK)
	case goxml.EmblemPos2:
		unlockID = uint32(common.EMBLEM_CONFIG_SLOT_TWO_UNLOCK)
	case goxml.EmblemPos3:
		unlockID = uint32(common.EMBLEM_CONFIG_SLOT_THREE_UNLOCK)
	case goxml.EmblemPos4:
		unlockID = uint32(common.EMBLEM_CONFIG_SLOT_FOUR_UNLOCK)
	default:
		l4g.Errorf("user: %d [C2L_EmblemWear] slotFuncInfo not exist. slot:%d", c.Msg.UID, slot)
		return false
	}
	// 纹章槽位解锁条件检查
	funcInfo := goxml.GetData().FunctionInfoM.Index(unlockID)
	if funcInfo == nil || funcInfo.Lv > c.User.Level() || funcInfo.HeroLv > level ||
		funcInfo.HeroStar > star || funcInfo.HeroStage > stage {
		return false
	}

	return true
}

type C2LEmblemLevelUpCommand struct {
	base.UserCommand
}

func (c *C2LEmblemLevelUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemLevelUpCommand) Error(msg *cl.L2C_EmblemLevelUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemLevelUp, msg)
	return false
}

//nolint:funlen
func (c *C2LEmblemLevelUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemLevelUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemLevelUp Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_EmblemLevelUp: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_EmblemLevelUp{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EMBLEM), c.Srv) {
		l4g.Errorf("user: %d C2L_EmblemLevelUp: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	l := len(cmsg.LevelUpInfos)

	if l == 0 || l > goxml.EmblemPosMax {
		l4g.Errorf("user: %d C2L_EmblemLevelUp: emblem levelup info len:%d is error", c.Msg.UID, l)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	emblemM := c.User.EmblemManager()

	repeatedCheck := make(map[uint64]struct{})
	passCheckInfos := make([]*passCheckInfo, 0, l)
	totalCost := make([]*cl.Resource, 0, goxml.EmblemLevelupResCostCount*2)
	for _, levelUpInfo := range cmsg.LevelUpInfos {
		if levelUpInfo == nil {
			l4g.Errorf("user: %d C2L_EmblemLevelUp: level Up Info is nil", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		_, exist := repeatedCheck[levelUpInfo.Id]
		if exist {
			l4g.Errorf("user: %d C2L_EmblemLevelUp: emblem id is repeated:%d", c.Msg.UID, levelUpInfo.Id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedCheck[levelUpInfo.Id] = struct{}{}
		passCheck := &passCheckInfo{}
		emblem := emblemM.Get(levelUpInfo.Id)
		if emblem == nil {
			l4g.Errorf("user: %d  [C2L_EmblemLevelUp] emblem not exist.id:%d", c.Msg.UID, levelUpInfo.Id)
			return c.Error(smsg, uint32(cret.RET_EMBLEM_NOT_EXIST))
		}
		passCheck.emblem = emblem

		var hero *character.Hero
		if emblem.Data.Hid > 0 {
			hero = c.User.HeroManager().Get(emblem.Data.Hid)
			if hero == nil {
				l4g.Errorf("user: %d [C2L_EmblemLevelUp] emblem level up, hero not found, emblemId:%dm hid: %d",
					c.Msg.UID, emblem.Data.Id, emblem.Data.Hid)
				return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
			}
			passCheck.hero = hero
		}

		info := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if info == nil {
			l4g.Errorf("user: %d [C2L_EmblemLevelUp] emblemInfo not exist. %d", c.Msg.UID, emblem.Data.SysId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		targetLevel := levelUpInfo.TargetsLevel

		if emblem.Data.Level >= targetLevel ||
			targetLevel-emblem.Data.Level > uint32(common.EMBLEM_CONFIG_MAX_LEVELUP_COUNT) ||
			targetLevel > info.LevelMax {
			l4g.Errorf("user: %d [C2L_EmblemLevelUp] emblem level:%d targetLevel:%d maxLevel:%d count not valid.",
				c.Msg.UID, emblem.Data.Level, targetLevel, info.LevelMax)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		passCheck.targetLevel = targetLevel

		for level := emblem.Data.Level; level < targetLevel; level++ {
			levelInfo := goxml.GetData().EmblemLevelInfoM.Index(info.LevelIndex, level)
			if levelInfo == nil {
				l4g.Errorf("user: %d [C2L_EmblemLevelUp] emblemLevelInfo not exist.levelIndex:%d, level:%d",
					c.Msg.UID, info.LevelIndex, level)
				return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
			}
			if c.User.Level() < levelInfo.LimitLevel {
				l4g.Errorf("user: %d [C2L_EmblemLevelUp] user level not enough.id:%d, level:%d, needLevel:%d",
					c.Msg.UID, levelUpInfo.Id, c.User.Level(), levelInfo.LimitLevel)
				return c.Error(smsg, uint32(cret.RET_COMMON_LEVEL_NOT_ENOUGH))
			}
			totalCost = character.MergeResources(append(totalCost, levelInfo.Costs...))
		}
		passCheckInfos = append(passCheckInfos, passCheck)
	}

	cRet := c.User.Consume(c.Srv, totalCost, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_LEVEL_UP), 0)
	if cRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d [C2L_EmblemLevelUp] emblem level up consume error. %d",
			c.Msg.UID, cRet)
		return c.Error(smsg, cRet)
	}

	heroCheck := make(map[uint64]*character.Hero)
	var cultivateCount uint64
	var maxLevel uint64
	for _, checkInfo := range passCheckInfos {
		oldLevel := checkInfo.emblem.Data.Level
		oldScore := checkInfo.emblem.GetScore()
		checkInfo.emblem.SetLevel(checkInfo.targetLevel)
		newScore := checkInfo.emblem.CalcScore(c.User)
		checkInfo.emblem.SetScore(newScore)
		emblemM.SetChange(checkInfo.emblem.Data.Id)
		smsg.Emblem = append(smsg.Emblem, checkInfo.emblem.Clone().Data)
		var heroSysId uint32
		var heroId uint64
		if checkInfo.hero != nil {
			heroSysId = checkInfo.hero.GetHeroSysID()
			heroId = checkInfo.hero.GetHid()
		}
		cultivateCount++
		if maxLevel < uint64(checkInfo.targetLevel) {
			maxLevel = uint64(checkInfo.targetLevel)
		}
		c.User.LogEmblemLevelUp(c.Srv, checkInfo.emblem, oldLevel,
			heroSysId, heroId, oldScore, newScore)
		if heroId != 0 {
			heroCheck[heroId] = checkInfo.hero
		}
	}

	c.User.FireCommonEvent(c.Srv.EventM(), aevent.IeEmblemCultivate, cultivateCount)
	c.User.FireCommonEvent(c.Srv.EventM(), aevent.AeEmblemLevelToX, maxLevel)

	for _, h := range heroCheck {
		h.SetHeroAttrChange()
	}
	c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemLevelUp, smsg)

	return c.ResultOK(smsg.Ret)
}

type passCheckInfo struct {
	emblem      *character.Emblem
	hero        *character.Hero
	targetLevel uint32
}

type C2LEmblemDecomposeCommand struct {
	base.UserCommand
}

func (c *C2LEmblemDecomposeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemDecomposeCommand) Error(msg *cl.L2C_EmblemDecompose, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemDecompose, msg)
	return false
}

func (c *C2LEmblemDecomposeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemDecompose{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemDecompose Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_EmblemDecompose: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_EmblemDecompose{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}
	emblemM := c.User.EmblemManager()
	emblemCount := uint32(len(cmsg.Ids))
	if emblemCount == 0 || emblemCount > goxml.GetData().BelltowerConfigInfoM.EmblemChooseLimit || emblemCount > emblemM.GetCount() {
		l4g.Errorf("user: %d [C2L_EmblemDecompose]count error. count:%d", c.Msg.UID, len(cmsg.Ids))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	costs := make([]*cl.Resource, 0, len(cmsg.Ids))
	awards := make([]*cl.Resource, 0, int(goxml.EmblemLevelupResCostCount)*len(cmsg.Ids))
	existEmblems := make(map[uint64]struct{}, len(cmsg.Ids))
	scoreLog := make([]*log.CultivateScoreStr, 0, len(cmsg.Ids))
	//emblemInfo := make([]*cl.EmblemInfo, 0, len(cmsg.Ids))
	for _, v := range cmsg.Ids { //nolint:varnamelen
		if _, exist := existEmblems[v]; exist {
			l4g.Errorf("user: %d [C2L_EmblemDecompose] emblemId repeat. emblemId:%d", c.Msg.UID, v)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		emblem := emblemM.Get(v)
		if emblem == nil {
			l4g.Errorf("user: %d [C2L_EmblemDecompose] emblem not exist. id:%d", c.Msg.UID, v)
			return c.Error(smsg, uint32(cret.RET_EMBLEM_NOT_EXIST))
		}
		if emblem.Data.Hid != 0 {
			l4g.Errorf("user: %d [C2L_EmblemDecompose] hid not zero.emblemId:%d, hid:%d", c.Msg.UID, v, emblem.Data.Hid)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if goxml.GetData().EmblemInfoM.GetRare(emblem.Data.SysId) >= goxml.GetData().ConfigInfoM.EmblemDeComposeRareLimit {
			l4g.Errorf("user: %d C2L_EmblemDecompose: emblem rare limit. emblemID:%d, sysId:%d", c.Msg.UID, v, emblem.Data.SysId)
			return c.Error(smsg, uint32(cret.RET_EMBLEM_DECOMPOSE_RARE_LIMIT))
		}

		costs = append(costs, &cl.Resource{
			Id:    v,
			Type:  uint32(common.RESOURCE_EMBLEM),
			Value: emblem.Data.SysId,
			Count: 1,
		})
		scoreLog = append(scoreLog, &log.CultivateScoreStr{
			Id:       strconv.FormatUint(v, 10),
			SysId:    emblem.Data.SysId,
			OldScore: strconv.FormatUint(uint64(emblem.Data.Score), 10),
		})
		awards = append(awards, emblem.MergeDecomposeRes()...)
		existEmblems[v] = struct{}{}
		//emblemInfo = append(emblemInfo, emblem.Data.Clone())
	}
	cRet := c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_DECOMPOSE), 0)
	if cRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d [C2L_EmblemDecompose] consume error. %+v", c.Msg.UID, costs)
		return c.Error(smsg, cRet)
	}
	if len(awards) > 0 {
		cRet, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_DECOMPOSE), 0)
		if cRet != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d [C2L_EmblemDecompose]award error. %+v", c.Msg.UID, awards)
		}
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemDecompose, smsg)
	c.User.LogEmblemDecompose(c.Srv, scoreLog)

	return c.ResultOK(smsg.Ret)
}

type C2LEmblemGrowTransferCommand struct {
	base.UserCommand
}

func (c *C2LEmblemGrowTransferCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemGrowTransferCommand) Error(msg *cl.L2C_EmblemGrowTransfer, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemGrowTransfer, msg)
	return false
}

//nolint:funlen
func (c *C2LEmblemGrowTransferCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemGrowTransfer{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemGrowTransfer Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_EmblemGrowTransfer: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_EmblemGrowTransfer{
		Ret:          uint32(cret.RET_OK),
		TransferInfo: cmsg.TransferInfo,
	}

	lens := len(cmsg.TransferInfo)
	if lens > goxml.EmblemPosMax || lens == 0 {
		l4g.Errorf("user: %d C2L_EmblemGrowTransfer transfers lens:%d err", c.Msg.UID, lens)
		return c.Error(smsg, uint32(cret.RET_PARAM_LENGTH_LIMIT))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BELLTOWER), c.Srv) ||
		//!c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BELLTOWER_EMBLEM_REVIVE)) ||
		!c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EMBLEM), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	emblemM := c.User.EmblemManager()
	heroM := c.User.HeroManager()

	var heroOfGrow *character.Hero
	emblems := make([]*growTransferEmblem, 0, len(cmsg.TransferInfo))
	totalResource := make([]*cl.Resource, 0, goxml.EmblemLevelupResCostCount)
	repeatedCheck := make(map[uint64]struct{})
	for _, v := range cmsg.TransferInfo { //nolint:varnamelen
		if v == nil {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer transfers Element is nil", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		_, exist := repeatedCheck[v.GrowEmblem]
		if exist {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer transfers grow emblem:%d is repeated", c.Msg.UID, v.GrowEmblem)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedCheck[v.GrowEmblem] = struct{}{}
		_, exist = repeatedCheck[v.ReviveEmblem]
		if exist {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer transfers revive emblem:%d is repeated", c.Msg.UID, v.ReviveEmblem)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedCheck[v.ReviveEmblem] = struct{}{}
		growEmblem := emblemM.Get(v.GrowEmblem)
		reviveEmblem := emblemM.Get(v.ReviveEmblem)
		if growEmblem == nil || reviveEmblem == nil {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer emblem not exist. growEmblemId:%d reviveEmblemId:%d",
				c.Msg.UID, v.GrowEmblem, v.ReviveEmblem)
			return c.Error(smsg, uint32(cret.RET_EMBLEM_NOT_EXIST))
		}
		preGrowEmblem, preReviveEmblem := growEmblem.Data.Clone(), reviveEmblem.Data.Clone()
		growEmblemInfo := goxml.GetData().EmblemInfoM.Index(growEmblem.Data.SysId)
		reviveEmblemInfo := goxml.GetData().EmblemInfoM.Index(reviveEmblem.Data.SysId)
		if growEmblemInfo == nil || reviveEmblemInfo == nil {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer emblemInfo not exist. growEmblemId:%d reviveEmblemId:%d",
				c.Msg.UID, v.GrowEmblem, v.ReviveEmblem)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		//0.10.0 高等级符文可以向低等级重生 方便凑套装
		//if growEmblemInfo.LevelMax < reviveEmblemInfo.LevelMax {
		//	l4g.Errorf("user: %d C2L_EmblemGrowTransfer rare error. growEmblemRare:%d reviveEmblemRare:%d",
		//		c.Msg.UID, growEmblemInfo.Rare, reviveEmblemInfo.Rare)
		//	return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		//}
		if reviveEmblem.CheckEmblemIsInitial() || !growEmblem.CheckEmblemIsInitial() {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer: emblem not revive. growEmblemId:%d reviveEmblemId:%d",
				c.Msg.UID, v.GrowEmblem, v.ReviveEmblem)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		heroOfGrow = heroM.Get(growEmblem.Data.Hid)
		if heroOfGrow == nil {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer hero not exist. hid:%d", c.Msg.UID, growEmblem.Data.Hid)
			return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
		}

		//新的修改 被转移的英雄必须为0
		if reviveEmblem.Data.Hid != 0 {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer revive emblem has hero. hid:%d", c.Msg.UID, reviveEmblem.Data.Id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		tmp := &growTransferEmblem{
			preGrowEmblemClone:   preGrowEmblem,
			preGrowEmblemInfo:    growEmblemInfo,
			preGrowEmblem:        growEmblem,
			preReviveEmblemClone: preReviveEmblem,
			preReviveEmblemInfo:  reviveEmblemInfo,
			preReviveEmblem:      reviveEmblem,
		}

		retResource, newLevel := emblemM.GrowTransferCheck(tmp.preReviveEmblem, tmp.preGrowEmblemInfo, tmp.preReviveEmblemInfo)
		totalResource = character.MergeResources(append(totalResource, retResource...))
		tmp.newLevel = newLevel

		emblems = append(emblems, tmp)
	}

	var retRes []*cl.Resource
	if len(totalResource) > 0 {
		smsg.Ret, retRes = c.User.Award(c.Srv, totalResource, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_GROW_TRANSFER), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_EmblemGrowTransfer growTransfer award error.", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}
	}

	pre := make([]*cl.EmblemInfo, 0, len(emblems)*2)
	cur := make([]*cl.EmblemInfo, 0, len(emblems)*2)
	for _, v := range emblems { //nolint:varnamelen
		emblemM.GrowTransfer(v.preGrowEmblem, v.preReviveEmblem, v.newLevel)

		// 设置更新英雄属性的标识：false表示更新
		heroOfGrow.SetHeroAttrChange()
		//更新缔约效果
		c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
		smsg.Emblems = append(smsg.Emblems, v.preGrowEmblem.Data.Clone())
		smsg.Emblems = append(smsg.Emblems, v.preReviveEmblem.Data.Clone())

		pre = append(pre, v.preGrowEmblemClone, v.preReviveEmblemClone)
		cur = append(cur, v.preGrowEmblem.Data, v.preReviveEmblem.Data)
	}
	c.User.LogEmblemGrowTransfer(c.Srv, pre, cur, retRes)

	smsg.LeftRes = retRes

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemGrowTransfer, smsg)

	return true
}

type growTransferEmblem struct {
	preGrowEmblemClone   *cl.EmblemInfo
	preGrowEmblem        *character.Emblem
	preGrowEmblemInfo    *goxml.EmblemInfosExt
	preReviveEmblemClone *cl.EmblemInfo
	preReviveEmblem      *character.Emblem
	preReviveEmblemInfo  *goxml.EmblemInfosExt
	newLevel             uint32
}

type C2LEmblemSetAutoDecomposeCommand struct {
	base.UserCommand
}

func (c *C2LEmblemSetAutoDecomposeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemSetAutoDecomposeCommand) Error(msg *cl.L2C_EmblemSetAutoDecompose, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSetAutoDecompose, msg)
	return false
}

func (c *C2LEmblemSetAutoDecomposeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemSetAutoDecompose{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemSetAutoDecompose Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_EmblemSetAutoDecompose: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_EmblemSetAutoDecompose{
		Ret:      uint32(cret.RET_OK),
		AutoRare: cmsg.AutoRare,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EQUIP), c.Srv) ||
		!c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BELLTOWER_EQUIP_DECOMPOSE), c.Srv) {
		l4g.Errorf("function not open, uid:%d", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if len(cmsg.AutoRare) > len(goxml.GetData().EmblemInfoM.Rare) {
		l4g.Errorf("user: %d C2L_EmblemSetAutoDecompose: cmsg.AutoRare num error. num:%d", c.Msg.UID, len(cmsg.AutoRare))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	repeatRare := make(map[uint32]struct{})
	for _, rare := range cmsg.AutoRare {
		if _, exist := repeatRare[rare]; exist {
			l4g.Errorf("user: %d C2L_EmblemSetAutoDecompose: cmsg.AutoRare repeat. rare:%d", c.Msg.UID, rare)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		} else {
			repeatRare[rare] = struct{}{}
		}
		if _, exist := goxml.GetData().EmblemInfoM.Rare[rare]; !exist {
			l4g.Errorf("user: %d C2L_EmblemSetAutoDecompose: cmsg.AutoRare not exist. rare:%d", c.Msg.UID, rare)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	c.User.SetEmblemAutoDecomposeRare(cmsg.AutoRare)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSetAutoDecompose, smsg)
	if c.ResultOK(smsg.Ret) {
		c.User.LogEmblemSetAutoDecompose(c.Srv, cmsg.AutoRare)
	}
	return c.ResultOK(smsg.Ret)
}

type C2LEmblemCustomizeCommand struct {
	base.UserCommand
}

func (c *C2LEmblemCustomizeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemCustomizeCommand) Error(msg *cl.L2C_EmblemCustomize, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemCustomize, msg)
	return false
}

//nolint:funlen
func (c *C2LEmblemCustomizeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemCustomize{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LEmblemCustomizeCommand Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2LEmblemCustomizeCommand: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_EmblemCustomize{
		Ret: uint32(cret.RET_OK),
	}

	ItemInfo := goxml.GetData().ItemInfoM.Index(cmsg.ItemId)
	if ItemInfo == nil {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: itemId:%d is nil", c.Msg.UID, cmsg.ItemId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	customizeInfo := goxml.GetData().EmblemCustomizeInfoM.Index(cmsg.Customize)
	if customizeInfo == nil {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d info is nil", c.Msg.UID, cmsg.Customize)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if ItemInfo.Value != customizeInfo.EmblemGroupId {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d group not same with item value:%d",
			c.Msg.UID, customizeInfo.EmblemGroupId, ItemInfo.Value)
		return c.Error(smsg, uint32(cret.RET_EMBLEM_CUSTOMIZE_ITEM_VALUE_ERROR))
	}

	// 检查屏蔽是否生效
	if goxml.GetData().ShieldInfoM.CheckShieldedByTime(time.Now().Unix(), uint32(common.RESOURCE_HERO), cmsg.HeroId) {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: heroId:%d is shielded", c.Msg.UID, cmsg.HeroId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(cmsg.HeroId)
	if heroInfo == nil {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: heroId:%d is error",
			c.Msg.UID, cmsg.HeroId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	heroGroupInfo := goxml.GetData().EmblemCustomizeHeroInfoM.InHeroGroup(customizeInfo.HeroGroupId, cmsg.HeroId)
	if heroGroupInfo == nil {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: heroId:%d not exist in customize hero group info:%d",
			c.Msg.UID, cmsg.HeroId, customizeInfo.HeroGroupId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	var typeId uint32
	if customizeInfo.TypeCustomize == goxml.EmblemCustomizeRandom {
		if cmsg.TypeId != 0 {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d Type is random don't need typeID:%d",
				c.Msg.UID, cmsg.Customize, cmsg.TypeId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		typeId, smsg.Ret = goxml.GetData().EmblemCustomizeTypeInfoM.RandomTypeId(c.Srv.Rand(), heroGroupInfo.TypeGroupId)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: EmblemCustomizeTypeInfoM RandomTypeId typeGroup:%d failed error:%d",
				c.Msg.UID, heroGroupInfo.TypeGroupId, smsg.Ret)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	} else {
		smsg.Ret = goxml.GetData().EmblemCustomizeTypeInfoM.InTypeId(heroGroupInfo.TypeGroupId, cmsg.TypeId)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: EmblemCustomizeTypeInfoM pick typeId:%d typeGroup:%d failed error:%d",
				c.Msg.UID, cmsg.TypeId, heroGroupInfo.TypeGroupId, smsg.Ret)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		typeId = cmsg.TypeId
	}

	var pos uint32
	if customizeInfo.PosCustomize == goxml.EmblemCustomizeRandom {
		if cmsg.PosId != 0 {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d pos is random don't need posId",
				c.Msg.UID, cmsg.Customize)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		pos = uint32(c.Srv.Rand().RandBetween(goxml.EmblemPosStart, goxml.EmblemPosMax))
	} else if customizeInfo.PosCustomize == goxml.EmblemCustomizePick {
		_, exist := goxml.EmblemLegalPos[cmsg.PosId]
		if !exist {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: pick pos:%d error",
				c.Msg.UID, cmsg.PosId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		pos = cmsg.PosId
	} else {
		if cmsg.PosId != 0 {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d pos is const:%d don't need posId",
				c.Msg.UID, cmsg.Customize, customizeInfo.PosCustomize)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		pos = customizeInfo.PosCustomize
	}

	var skill uint32
	if customizeInfo.MagicSkillCustomize == goxml.EmblemNoExistSkill {
		if cmsg.MagicSkill != 0 {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d skill is none don't need skill",
				c.Msg.UID, cmsg.Customize)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		skill = cmsg.MagicSkill
	} else if customizeInfo.MagicSkillCustomize == goxml.EmblemExistSkill {
		if cmsg.MagicSkill != 1 {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d skill is const need skill be 1",
				c.Msg.UID, cmsg.Customize)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		skill = cmsg.MagicSkill
	} else if customizeInfo.MagicSkillCustomize == goxml.EmblemCustomizeRandom {
		if cmsg.MagicSkill != 0 {
			l4g.Errorf("user: %d C2LEmblemCustomizeCommand: customize:%d skill is none don't need skill",
				c.Msg.UID, cmsg.Customize)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if customizeInfo.Rare >= goxml.GetData().EmblemConfigInfoM.GetEmblemCustomize() {
			skill = goxml.EmblemExistSkill
		} else {
			skill = uint32(c.Srv.Rand().RandBetween(goxml.EmblemNoExistSkill, goxml.EmblemExistSkill))
		}
	} else {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: skill:%d is error",
			c.Msg.UID, cmsg.MagicSkill)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	var emblemSysID uint64
	emblemSysID += uint64(customizeInfo.Rare) * goxml.EmblemCustomizeRareBase
	emblemSysID += uint64(heroInfo.Race) * goxml.EmblemCustomizeRaceBase
	emblemSysID += uint64(typeId) * goxml.EmblemCustomizeTypeBase
	emblemSysID += uint64(pos) * goxml.EmblemCustomizePosBase
	emblemSysID += 1 * goxml.EmblemCustomizeIsHeroGroupBase
	emblemSysID += uint64(skill)

	if emblemSysID > math.MaxUint32 {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: emblem SysId:%d over size",
			c.Msg.UID, cmsg.ItemId)
		return c.Error(smsg, uint32(cret.RET_EMBLEM_CUSTOMIZE_EMBLEM_ID_OVER_SIZE))
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(uint32(emblemSysID))
	if emblemInfo == nil {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: emblem SysId:%d cfg not exist",
			c.Msg.UID, cmsg.ItemId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	cost := &cl.Resource{
		Type:  uint32(common.RESOURCE_ITEM),
		Value: cmsg.ItemId,
		Count: 1,
	}
	attr := &cl.Attr{
		Type:  uint32(common.RESOURCE_ATTR_TYPE_RESAT_ADDITIVE_HERO),
		Value: int64(cmsg.HeroId),
	}
	emblem := &cl.Resource{
		Type:  uint32(common.RESOURCE_EMBLEM),
		Value: emblemInfo.Id,
		Count: 1,
		Attrs: []*cl.Attr{attr},
	}
	smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, []*cl.Resource{cost}, []*cl.Resource{emblem}, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_CUSTOMIZE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2LEmblemCustomizeCommand: emblem award failed error code:%d",
			c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.Customize = cmsg.Customize
	smsg.HeroId = cmsg.HeroId
	smsg.TypeId = typeId
	smsg.PosId = cmsg.PosId
	smsg.MagicSkill = skill
	smsg.ItemId = cmsg.ItemId

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemCustomize, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LEmblemBuySlotCommand struct {
	base.UserCommand
}

func (c *C2LEmblemBuySlotCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemBuySlotCommand) Error(msg *cl.L2C_EmblemBuySlot, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemBuySlot, msg)
	return false
}

func (c *C2LEmblemBuySlotCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemBuySlot{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemBuySlot Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_EmblemBuySlot: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_EmblemBuySlot{
		Ret:   uint32(cret.RET_OK),
		Count: cmsg.Count,
	}

	if cmsg.Count == 0 {
		l4g.Errorf("user: %d C2L_EmblemBuySlot: param error count is 0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EMBLEM), c.Srv) {
		l4g.Errorf("user: %d C2L_EmblemBuySlot: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	bought := c.User.GetEmblemBuySlotCount() / goxml.GetData().ConfigInfoM.GetEmblemSlotAddNum()
	ok, price := goxml.GetData().BuyPriceInfoM.GetPrice(goxml.GetData().ConfigInfoM.GetEmblemSlotBuyGroup(), bought, cmsg.Count)
	if !ok {
		l4g.Errorf("user: %d C2L_EmblemBuySlot: purchase emblem slot failed, buy group:%d error.",
			c.Msg.UID, goxml.GetData().ConfigInfoM.GetEmblemSlotBuyGroup())
		return c.Error(smsg, uint32(cret.RET_GOLDBUY_BUY_GROUP_NOT_EXIST))
	}
	cost := goxml.GenSimpleResource(uint32(common.RESOURCE_DIAMOND), 0, price)

	oldNum := c.User.GetEmblemSlotCount()
	newNum := oldNum + goxml.GetData().ConfigInfoM.GetEmblemSlotAddNum()*cmsg.Count
	maxLimit := goxml.GetData().ConfigInfoM.GetEmblemSlotMax(c.User.Vip())
	if newNum > maxLimit {
		l4g.Errorf("user: %d C2L_EmblemBuySlot: max slot limit. oldNum:%d, newNum:%d, maxLimit:%d",
			c.Msg.UID, oldNum, newNum, maxLimit)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	smsg.Ret = c.User.Consume(c.Srv, []*cl.Resource{cost}, uint32(log.RESOURCE_CHANGE_REASON_BUY_EMBLEM_SLOT), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_EmblemBuySlot: consume failed. cost:%v", c.Msg.UID, cost)
		return c.Error(smsg, smsg.Ret)
	}
	c.User.SetEmblemSlot(newNum)

	smsg.Slots = newNum
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemBuySlot, smsg)
	c.User.LogEmblemBuySlot(c.Srv, cmsg.Count, newNum)
	return c.ResultOK(smsg.Ret)
}

type C2LEmblemUpgradeCommand struct {
	base.UserCommand
}

func (c *C2LEmblemUpgradeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemUpgradeCommand) Error(msg *cl.L2C_EmblemUpgrade, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemUpgrade, msg)
	return false
}

//nolint:funlen
func (c *C2LEmblemUpgradeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemUpgrade{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemUpgrade Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_EmblemUpgrade: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_EmblemUpgrade{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}

	if len(cmsg.Ids) == 0 || len(cmsg.Ids) > goxml.EmblemPosMax {
		l4g.Errorf("user: %d C2L_EmblemUpgrade: client param num is invalid.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EMBLEM_UPGRADE), c.Srv) {
		l4g.Errorf("user: %d C2L_EmblemUpgrade: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	emblemM := c.User.EmblemManager()
	heroM := c.User.HeroManager()
	costs := make([]*cl.Resource, 0, len(cmsg.Ids))
	preEmblems := make([]*cl.EmblemInfo, 0, len(cmsg.Ids))
	heroPool := make(map[uint64]*character.Hero)
	emblemPool := make(map[*character.Emblem]*goxml.EmblemInfosExt, len(cmsg.Ids))
	skillPool := make(map[uint64]uint32)
	isRepeat := make(map[uint64]struct{})
	for _, id := range cmsg.Ids {
		if _, exist := isRepeat[id]; exist {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: client param is repeat. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		isRepeat[id] = struct{}{}

		emblem := emblemM.Get(id)
		if emblem == nil {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: emblem not exist. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_EMBLEM_NOT_EXIST))
		}
		eInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
		if eInfo == nil {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: emblemInfo is nil. sysID:%d", c.Msg.UID, emblem.Data.SysId)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		if eInfo.AscendId == 0 {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: ascendID is invalid. sysID:%d", c.Msg.UID, eInfo.Id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		if eInfo.Rare != goxml.EmblemRareOrange || eInfo.IsHeroGroup == 0 || emblem.Data.AdditiveHero == 0 {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: emblem rare is invalid. rare:%d", c.Msg.UID, eInfo.Rare)
			return c.Error(smsg, uint32(cret.RET_EMBLEM_UPGRADE_NOT_ORANGE_RARE))
		}
		emblemPool[emblem] = eInfo
		preEmblems = append(preEmblems, emblem.Data.Clone())
		if emblem.Data.SkillId == 0 {
			skillID := c.generateSkillID(emblem.Data.AdditiveHero, eInfo)
			if skillID == 0 {
				l4g.Errorf("user: %d C2L_EmblemUpgrade: generate skillID is invalid. skillID:%d", c.Msg.UID, skillID)
				return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
			}
			skillPool[emblem.Data.Id] = skillID
		}

		hero := heroM.Get(emblem.Data.Hid)
		if hero == nil {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: hero not exist. hid:%d", c.Msg.UID, emblem.Data.Hid)
			return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
		}
		heroPool[hero.GetHid()] = hero
		// 专属英雄
		hInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
		if hInfo == nil {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: heroInfo is nil. sysID:%d", c.Msg.UID, emblem.Data.AdditiveHero)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		aInfo := goxml.GetData().EmblemAscendInfoM.Index(hInfo.Race, eInfo.Pos)
		if aInfo == nil {
			l4g.Errorf("user: %d C2L_EmblemUpgrade: emblemAscendInfo is nil. race:%d, pos:%d", c.Msg.UID, hInfo.Race, eInfo.Pos)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		costs = append(costs, aInfo.CostClRes...)
	}

	smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_UPGRADE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_EmblemUpgrade: consume failed.", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.Emblems = c.upgrade(emblemPool, skillPool)
	c.heroChange(heroPool)
	c.User.FireCommonEvent(c.Srv.EventM(), aevent.IeNewEmblem, uint64(len(cmsg.Ids)), goxml.EmblemRareRed)
	for _, id := range cmsg.Ids {
		c.User.FireCommonEvent(c.Srv.EventM(), aevent.IeComplianceNewEmblem, id)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemUpgrade, smsg)
	c.User.LogEmblemUpgrade(c.Srv, preEmblems, smsg.Emblems)
	for _, e := range smsg.Emblems {
		if e.AdditiveHero > 0 || e.SkillId > 0 {
			c.User.LogEmblemExclusive(c.Srv, &character.Emblem{Data: e}, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_UPGRADE))
		}
	}

	return c.ResultOK(smsg.Ret)
}

func (c *C2LEmblemUpgradeCommand) generateSkillID(additiveHero uint32, eInfo *goxml.EmblemInfosExt) uint32 {
	gInfo := goxml.GetData().EmblemHeroGroupInfoM.InGroup(eInfo.HeroGroup, additiveHero)
	if gInfo == nil {
		l4g.Errorf("user:%d C2L_EmblemUpgrade: emblemHeroGroupInfo is nil %d %d", c.Msg.UID, eInfo.HeroGroup, additiveHero)
		return 0
	}

	skillID := goxml.GetData().EmblemHeroGroupInfoM.RandomMagicSkill(c.Srv.Rand(), gInfo.GroupId, gInfo.HeroId)
	if skillID == 0 {
		l4g.Errorf("user:%d C2L_EmblemUpgrade: rand magicSkillID failed. group:%d heroID:%d", c.Msg.UID, gInfo.GroupId, gInfo.HeroId)
		return 0
	}

	skillID = goxml.GetData().EmblemMagicSkillInfoM.RandSkill(c.Srv.Rand(), skillID)
	if skillID == 0 {
		l4g.Errorf("user:%d C2L_EmblemUpgrade: rand skillID is zero. magicSkillID:%d", c.Msg.UID, skillID)
		return 0
	}

	return skillID
}

func (c *C2LEmblemUpgradeCommand) upgrade(emblemPool map[*character.Emblem]*goxml.EmblemInfosExt, skillPool map[uint64]uint32) []*cl.EmblemInfo {
	retEmblems := make([]*cl.EmblemInfo, 0, len(emblemPool))
	for e, info := range emblemPool { //nolint:varnamelen
		if e.Data.SkillId == 0 {
			e.Data.SkillId = skillPool[e.Data.Id]
		}
		e.Data.SysId = info.AscendId
		if e.Data.Succinct != nil { // 符文升阶,清空洗炼数据,只保留当前词缀ID
			currentAffixId := e.GetSuccinctAffixId()
			e.Data.Succinct = nil
			if currentAffixId > 0 { // 旧的词缀ID保留
				e.Data.Succinct = &cl.EmblemSuccinctInfo{AffixId: currentAffixId}
			}
		}
		c.User.EmblemManager().SetChange(e.Data.Id)
		retEmblems = append(retEmblems, e.Data.Clone())
	}

	return retEmblems
}

func (c *C2LEmblemUpgradeCommand) heroChange(heroPool map[uint64]*character.Hero) {
	for _, h := range heroPool {
		// 检查处理专属技能激活
		h.CheckHandleActiveEmblemExclusive(c.Srv, c.User)
		h.SetHeroAttrChange()
	}
	c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
}

type C2LEmblemSuccinctCommand struct {
	base.UserCommand
}

func (c *C2LEmblemSuccinctCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemSuccinctCommand) Error(msg *cl.L2C_EmblemSuccinct, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSuccinct, msg)
	return false
}

//nolint:funlen
func (c *C2LEmblemSuccinctCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemSuccinct{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemSuccinct Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_EmblemSuccinct: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_EmblemSuccinct{
		Ret:      uint32(cret.RET_OK),
		EmblemId: cmsg.EmblemId,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EMBLEM), c.Srv) ||
		!c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_EMBLEM_SUCCINCT), c.Srv) {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	eM := c.User.EmblemManager()

	emblem := eM.Get(cmsg.EmblemId)
	if emblem == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: emblem not exist. id:%d", c.Msg.UID, cmsg.EmblemId)
		return c.Error(smsg, uint32(cret.RET_EMBLEM_NOT_EXIST))
	}

	hero := c.User.HeroManager().Get(emblem.Data.Hid)
	if hero == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: emblem hero not exist. emblemId:%d hid:%d",
			c.Msg.UID, cmsg.EmblemId, emblem.Data.Hid)
		return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
	}

	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: emblemInfo not exist. id:%d", c.Msg.UID, cmsg.EmblemId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	// 必须是橙色或者红色
	if emblemInfo.Rare != uint32(common.QUALITY_ORANGE) && emblemInfo.Rare != uint32(common.QUALITY_RED) {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: emblem rare error. id:%d rare:%d", c.Msg.UID, cmsg.EmblemId, emblemInfo.Rare)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	// 必须有专属英雄
	if emblem.Data.AdditiveHero == 0 {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: emblem have no additive hero", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	var (
		succinctSkillInfo                  *goxml.EmblemSuccinctMagicSkillInfo
		succinctSuitInfo                   *goxml.EmblemSuitInfo
		succinctSuitSysId, succinctAffixId uint32
	)
	lockID := emblem.GetSuccinctLockID()
	if lockID != uint32(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SKILL) { // 词条没有锁定
		succinctSkillInfo = c.succinctRandomSkill(emblem, emblemInfo)
		if succinctSkillInfo == nil {
			l4g.Errorf("user: %d C2L_EmblemSuccinct: succinctSkill error.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
	}

	if lockID != uint32(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SUIT) { // 套装没有锁定
		succinctSuitInfo, succinctSuitSysId, succinctAffixId = c.succinctRandomSuitAndAffix(emblem, emblemInfo)
		if succinctSuitInfo == nil || succinctSuitSysId == 0 {
			l4g.Errorf("user: %d C2L_EmblemSuccinct: succinctSuit error.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
	}

	heroInfo := goxml.GetData().HeroInfoM.Index(emblem.Data.AdditiveHero)
	if heroInfo == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: emblem additiveHero not exist. id:%d", c.Msg.UID, emblem.Data.AdditiveHero)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	constInfo := goxml.GetData().EmblemSuccinctInfoM.GetRecordByHeroRaceEmblemRare(heroInfo.Race, emblemInfo.Rare)
	if constInfo == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: emblemSuccinctInfo not exist. heroRace:%d emblemRare:%d",
			c.Msg.UID, heroInfo.Race, emblemInfo.Rare)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	allConst := []*cl.Resource{{Type: constInfo.CostType, Value: constInfo.CostValue, Count: constInfo.CostCount}}
	if lockID != uint32(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_NONE) { // 锁定扣资源
		info := goxml.GetData().EmblemSuccinctLockInfoM.GetRecordByRaceRare(heroInfo.Race, emblemInfo.Rare)
		if info == nil || info.CostType == 0 || info.CostCount == 0 {
			l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: lock cost is nil. race:%d rare:%d", c.Msg.UID, heroInfo.Race, emblemInfo.Rare)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		allConst = append(allConst, goxml.GenSimpleResource(info.CostType, info.CostValue, info.CostCount))
	}

	smsg.Ret = c.User.Consume(c.Srv, allConst, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_EmblemSuccinct: consume error. code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	preEmblem := emblem.Data.Clone() // 洗炼前的符文信息，日志用

	if lockID != uint32(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SKILL) { // 词条没有锁定
		emblem.SetTmpSkillId(succinctSkillInfo.Skill)
		emblem.AddSuccinctSkillGuaranteeCount(emblemInfo.Rare)
		c.checkResetSkillGuaranteeCount(emblemInfo, emblem, succinctSkillInfo)
	}
	if lockID != uint32(common.EMBLEM_SUCCINCT_LOCK_TYPE_ESLT_SUIT) {
		emblem.SetTmpSysId(succinctSuitSysId)
		emblem.SetTmpAffixId(succinctAffixId)
		emblem.AddSuccinctAffixGuaranteeCount(emblemInfo.Rare)
		c.checkResetAffixGuaranteeCount(emblemInfo, emblem, succinctSuitInfo, succinctAffixId)
	}
	eM.SetChange(emblem.Data.Id)
	smsg.Emblem = emblem.Data.Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSuccinct, smsg)
	c.User.LogEmblemSuccinct(c.Srv, preEmblem, smsg.Emblem)
	return true
}

// succinctRandomSkill
// @Description: 洗炼词条
// @receiver c
// @param emblem
// @param emblemInfo
// @return uint32
func (c *C2LEmblemSuccinctCommand) succinctRandomSkill(emblem *character.Emblem, emblemInfo *goxml.EmblemInfosExt) *goxml.EmblemSuccinctMagicSkillInfo {

	succinctMagicSkillInfos := goxml.GetData().EmblemSuccinctMagicSkillInfoM.GetRecordsByHeroIdEmblemRare(emblem.Data.AdditiveHero, emblemInfo.Rare)
	if len(succinctMagicSkillInfos) == 0 {
		l4g.Debugf("user:%d C2LEmblemSuccinctCommand.succinctRandomSkill: getSuccinctMagicSkillInfo error. additiveHero:%d emblemRare:%d",
			c.User.ID(), emblem.Data.AdditiveHero, emblemInfo.Rare)
		return nil
	}

	totalChance := uint32(0)
	orangeInfos := make([]*goxml.EmblemSuccinctMagicSkillInfo, 0, len(succinctMagicSkillInfos)) // 史诗品质词条
	redInfos := make([]*goxml.EmblemSuccinctMagicSkillInfo, 0, len(succinctMagicSkillInfos))    // 传说品质词条

	for _, info := range succinctMagicSkillInfos {
		totalChance += info.Chance
		if info.MagicRare == uint32(common.QUALITY_ORANGE) {
			orangeInfos = append(orangeInfos, info)
		}
		if info.MagicRare == uint32(common.QUALITY_RED) {
			redInfos = append(redInfos, info)
		}
	}

	randChance := uint32(c.Srv.Rand().RandBetween(1, int(totalChance)))

	var randInfo *goxml.EmblemSuccinctMagicSkillInfo
	for _, info := range succinctMagicSkillInfos {
		if info.Chance < randChance {
			randChance -= info.Chance
			continue
		}
		randInfo = info
		break
	}

	l4g.Debugf("user:%d C2LEmblemSuccinctCommand.succinctRandomSkill: rand skill. totalChance:%d randChance:%d",
		c.User.ID(), totalChance, randChance)

	if emblemInfo.Rare == uint32(common.QUALITY_ORANGE) || emblemInfo.Rare == uint32(common.QUALITY_RED) {
		if randInfo.MagicRare < uint32(common.QUALITY_ORANGE) &&
			emblem.GetSuccinctOrangeSkillGuaranteeCount()+1 >= goxml.GetData().EmblemConfigInfoM.GetSuccinctCountGuaranteeOrangeSkill() {
			// 达到保底橙色的条件，从下面的橙色词条中随机一条
			totalNum := len(orangeInfos)
			if totalNum > 0 {
				index := c.Srv.Rand().RandBetween(0, int(totalNum)-1)
				randInfo = orangeInfos[index]
			}
		}
	}

	if emblemInfo.Rare == uint32(common.QUALITY_RED) {
		if randInfo.MagicRare < uint32(common.QUALITY_RED) &&
			emblem.GetSuccinctRedSkillGuaranteeCount()+1 >= goxml.GetData().EmblemConfigInfoM.GetSuccinctCountGuaranteeRedSkill() {
			// 达到保底红色的条件，从下面的红色词条中随机一条
			totalNum := len(redInfos)
			if totalNum > 0 {
				index := c.Srv.Rand().RandBetween(0, int(totalNum)-1)
				randInfo = redInfos[index]
			}
		}
	}
	return randInfo
}

// succinctRandomSuitAndAffix
// @Description: 洗炼套装(实际是对应套装的符文系统ID)和词缀
// @receiver c
// @param emblem
// @param emblemInfo
// @return uint32
// @return uint32
func (c *C2LEmblemSuccinctCommand) succinctRandomSuitAndAffix(emblem *character.Emblem, emblemInfo *goxml.EmblemInfosExt) (*goxml.EmblemSuitInfo, uint32, uint32) {
	infos := goxml.GetData().EmblemInfoM.GetEmblemInfosByRareAndRace(emblemInfo.Rare, emblemInfo.Race)

	randList := make([]*goxml.EmblemInfosExt, 0, len(infos))
	for _, info := range infos {
		if info == nil {
			continue
		}
		if info.Pos == emblemInfo.Pos && info.IsHeroGroup == 1 && info.IsMagicSkill == 1 { // 必须洗炼有专属英雄和有专属词条的
			randList = append(randList, info)
		}
	}

	randIndex := c.Srv.Rand().RandBetween(0, len(randList)-1)
	targetInfo := randList[randIndex]

	suitInfo := goxml.GetData().EmblemSuitInfoM.Index(targetInfo.SuitSkill)
	if suitInfo == nil {
		l4g.Errorf("user:%d succinctRandomSuitAndAffix: suitInfo is nil. id:%d", c.User.ID(), targetInfo.SuitSkill)
		return nil, 0, 0
	}

	configInfoM := goxml.GetData().EmblemConfigInfoM

	targetAffixId := uint32(0)
	haveAffix := c.Srv.Rand().RandBetween(1, 10000) <= int(configInfoM.GetSuccinctHaveAffixPro(emblemInfo.Rare))
	if haveAffix { // 本次洗炼产生词缀
		if suitInfo.Affix1 != 0 && suitInfo.Affix2 == 0 { // 只有远古词缀，直接出远古词缀
			targetAffixId = suitInfo.Affix1
		} else { // 其它情况正常走一遍随机流程
			totalWeight := configInfoM.GetSuccinctAffixWeight1() + configInfoM.GetSuccinctAffixWeight2()
			randomNum := c.Srv.Rand().RandBetween(1, int(totalWeight))
			if uint32(randomNum) <= configInfoM.GetSuccinctAffixWeight1() {
				targetAffixId = suitInfo.Affix1 // 远古词缀
			} else {
				targetAffixId = suitInfo.Affix2 // 太古词缀
			}
		}
	}

	if emblemInfo.Rare == uint32(common.QUALITY_ORANGE) {
		if targetAffixId == 0 && emblem.GetSuccinctAffix1GuaranteeCount()+1 >= configInfoM.GetSuccinctCountGuaranteeAffixOrange() {
			targetAffixId = suitInfo.Affix1
		}
	}

	if suitInfo.Affix2 != 0 && emblemInfo.Rare == uint32(common.QUALITY_RED) {
		isAffix2 := targetAffixId == suitInfo.Affix2 // 是不是太古词缀
		if !isAffix2 && emblem.GetSuccinctAffix2GuaranteeCount()+1 >= configInfoM.GetSuccinctCountGuaranteeAffixRed() {
			targetAffixId = suitInfo.Affix2
		}
	}
	return suitInfo, targetInfo.Id, targetAffixId
}

func (c *C2LEmblemSuccinctCommand) checkResetSkillGuaranteeCount(emblemInfo *goxml.EmblemInfosExt, emblem *character.Emblem,
	succinctSkillInfo *goxml.EmblemSuccinctMagicSkillInfo) {
	if emblemInfo.Rare == uint32(common.QUALITY_ORANGE) || emblemInfo.Rare == uint32(common.QUALITY_RED) {
		if succinctSkillInfo.MagicRare == uint32(common.QUALITY_ORANGE) { // 策划要等于
			// 洗炼到橙色色，重置
			emblem.SetSuccinctOrangeSkillGuaranteeCount(0)
		}

		if emblem.GetSuccinctOrangeSkillGuaranteeCount() >= goxml.GetData().EmblemConfigInfoM.GetSuccinctCountGuaranteeOrangeSkill() {
			// 达到保底记数，也重置
			emblem.SetSuccinctOrangeSkillGuaranteeCount(0)
		}
	}
	if emblemInfo.Rare == uint32(common.QUALITY_RED) {
		if succinctSkillInfo.MagicRare >= uint32(common.QUALITY_RED) {
			// 洗炼到红色，重置
			emblem.SetSuccinctRedSkillGuaranteeCount(0)
		}
		if emblem.GetSuccinctRedSkillGuaranteeCount() >= goxml.GetData().EmblemConfigInfoM.GetSuccinctCountGuaranteeRedSkill() {
			// 达到保底记数，也重置
			emblem.SetSuccinctRedSkillGuaranteeCount(0)
		}
	}
}

func (c *C2LEmblemSuccinctCommand) checkResetAffixGuaranteeCount(emblemInfo *goxml.EmblemInfosExt, emblem *character.Emblem,
	suitInfo *goxml.EmblemSuitInfo, affixId uint32) {
	if emblemInfo.Rare == uint32(common.QUALITY_ORANGE) {
		if affixId != 0 && (affixId == suitInfo.Affix1 || affixId == suitInfo.Affix2) {
			// 洗炼到远古或太古词缀，重置
			emblem.SetSuccinctAffix1GuaranteeCount(0)
		}
		if emblem.GetSuccinctAffix1GuaranteeCount() >= goxml.GetData().EmblemConfigInfoM.GetSuccinctCountGuaranteeAffixOrange() {
			// 达到保底记数，也重置
			emblem.SetSuccinctAffix1GuaranteeCount(0)
		}
	}
	if emblemInfo.Rare == uint32(common.QUALITY_RED) {
		if affixId != 0 && affixId == suitInfo.Affix2 {
			// 洗炼到太古词缀，重置
			emblem.SetSuccinctAffix2GuaranteeCount(0)
		}
		if emblem.GetSuccinctAffix2GuaranteeCount() >= goxml.GetData().EmblemConfigInfoM.GetSuccinctCountGuaranteeAffixRed() {
			// 达到保底记数，也重置
			emblem.SetSuccinctAffix2GuaranteeCount(0)
		}
	}
}

type C2LEmblemSuccinctLockOrSaveCommand struct {
	base.UserCommand
}

func (c *C2LEmblemSuccinctLockOrSaveCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemSuccinctLockOrSaveCommand) Error(msg *cl.L2C_EmblemSuccinctLockOrSave, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSuccinctLockOrSave, msg)
	return false
}

func (c *C2LEmblemSuccinctLockOrSaveCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemSuccinctLockOrSave{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemSuccinctLockOrSave Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_EmblemSuccinctLockOrSave: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_EmblemSuccinctLockOrSave{
		Ret:      uint32(cret.RET_OK),
		EmblemId: cmsg.EmblemId,
		OpType:   cmsg.OpType,
		LockId:   cmsg.LockId,
	}

	if cmsg.OpType != uint32(common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_LOCK) &&
		cmsg.OpType != uint32(common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_SAVE) {
		l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: opType error. opType:%d", c.Msg.UID, cmsg.OpType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	emblemM := c.User.EmblemManager()
	emblem := emblemM.Get(cmsg.EmblemId)
	if emblem == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: emblem not exist. id:%d", c.User.ID(), cmsg.EmblemId)
		return c.Error(smsg, uint32(cret.RET_EMBLEM_NOT_EXIST))
	}
	emblemInfo := goxml.GetData().EmblemInfoM.Index(emblem.Data.SysId)
	if emblemInfo == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: emblemInfo not exist. id:%d", c.User.ID(), emblem.Data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	succinctInfo := emblem.GetSuccinctInfo()
	if succinctInfo == nil { // 没有洗炼过，或者洗炼出的套装和词条是空
		l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: emblem succinct not exist. id:%d", c.User.ID(), cmsg.EmblemId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	hero := c.User.HeroManager().Get(emblem.Data.Hid)
	if hero == nil {
		l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: emblem not on hero. emblemId:%d heroId:%d",
			c.User.ID(), cmsg.EmblemId, emblem.Data.Hid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	preEmblem := emblem.Data.Clone()

	if cmsg.OpType == uint32(common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_SAVE) {
		if succinctInfo.TmpSysId == 0 && succinctInfo.TmpSkillId == 0 { // 洗炼出的套装和词条都为空
			l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: emblem succinct not exist. id:%d", c.User.ID(), cmsg.EmblemId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		emblem.SuccinctSave()
		hero.SetHeroAttrChange()
		c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
	}

	if cmsg.OpType == uint32(common.EMBLEM_SUCCINCT_LOCK_OR_SAVE_OP_TYPE_ESLOSOT_LOCK) {
		if _, exist := common.EMBLEM_SUCCINCT_LOCK_TYPE_name[int32(cmsg.LockId)]; !exist {
			l4g.Errorf("user: %d C2L_EmblemSuccinctLockOrSave: lock id error. lockId:%d", c.Msg.UID, cmsg.LockId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		emblem.SetSuccinctLockID(cmsg.LockId)
	}

	emblemM.SetChange(emblem.Data.Id)
	smsg.Emblem = emblem.Data.Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSuccinctLockOrSave, smsg)
	c.User.LogEmblemSuccinctLockOrSave(c.Srv, preEmblem, smsg.Emblem, cmsg.OpType)
	return true
}

type C2LEmblemSuccinctItemConflateCommand struct {
	base.UserCommand
}

func (c *C2LEmblemSuccinctItemConflateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LEmblemSuccinctItemConflateCommand) Error(msg *cl.L2C_EmblemSuccinctItemConflate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSuccinctItemConflate, msg)
	return false
}

func (c *C2LEmblemSuccinctItemConflateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_EmblemSuccinctItemConflate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_EmblemSuccinctItemConflate Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_EmblemSuccinctItemConflate: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_EmblemSuccinctItemConflate{
		Ret:             uint32(cret.RET_OK),
		TargetResources: cmsg.TargetResources,
	}

	if len(cmsg.TargetResources) > len(goxml.GetData().EmblemSuccinctConflateInfoM.GetConflateValueRecordMap()) {
		l4g.Errorf("user: %d C2L_EmblemSuccinctItemConflate: request num too large. num:%d",
			c.Msg.UID, len(cmsg.TargetResources))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	costs := make([]*cl.Resource, 0, len(cmsg.TargetResources))
	for _, resource := range cmsg.TargetResources {
		if resource.Type != uint32(common.RESOURCE_ITEM) {
			l4g.Errorf("user: %d C2L_EmblemSuccinctItemConflate: request num too large. num:%d",
				c.Msg.UID, len(cmsg.TargetResources))
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if resource.Count == 0 {
			l4g.Errorf("user: %d C2L_EmblemSuccinctItemConflate: targetResource count is 0. target:%+v",
				c.Msg.UID, cmsg.TargetResources)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		conflateInfo := goxml.GetData().EmblemSuccinctConflateInfoM.GetRecordByConflateValue(resource.Value)
		if conflateInfo == nil {
			l4g.Errorf("user: %d C2L_EmblemSuccinctItemConflate: conflateInfo not exist. num:%d", c.User.ID(), resource.Value)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if conflateInfo.CostCount1 > 0 {
			costs = append(costs, &cl.Resource{Type: conflateInfo.CostType1, Value: conflateInfo.CostValue1, Count: conflateInfo.CostCount1 * resource.Count})
		}
		if conflateInfo.CostCount2 > 0 {
			costs = append(costs, &cl.Resource{Type: conflateInfo.CostType2, Value: conflateInfo.CostValue2, Count: conflateInfo.CostCount2 * resource.Count})
		}
		if conflateInfo.CostCount3 > 0 {
			costs = append(costs, &cl.Resource{Type: conflateInfo.CostType3, Value: conflateInfo.CostValue3, Count: conflateInfo.CostCount3 * resource.Count})
		}
	}

	smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT_ITEM_CONFLATE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_EmblemSuccinctItemConflate: consume error. code:%d", c.User.ID(), smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, cmsg.TargetResources, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_EMBLEM_SUCCINCT_ITEM_CONFLATE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_EmblemSuccinctItemConflate: award error. code:%d", c.User.ID(), smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_EmblemSuccinctItemConflate, smsg)
	c.User.EmblemSuccinctItemConflate(c.Srv, cmsg.TargetResources)
	return true
}
