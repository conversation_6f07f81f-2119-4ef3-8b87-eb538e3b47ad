package recharge

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_RechargeGetData), &C2LRechargeGetDataCommand{}, state)                 //充值信息
	cmds.Register(uint32(cl.ID_MSG_C2L_RechargeSimulation), &C2LRechargeSimulationCommand{}, state)           // 伪充
	cmds.Register(uint32(cl.ID_MSG_C2L_RechargeByCoupon), &C2LRechargeByCouponCommand{}, state)               // 代金券充值
	cmds.Register(uint32(cl.ID_MSG_C2L_RechargeFirstGiftReward), &C2LRechargeFirstGiftRewardCommand{}, state) // 首充领奖
}

type C2LRechargeGetDataCommand struct {
	base.UserCommand
}

func (c *C2LRechargeGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRechargeGetDataCommand) Error(msg *cl.L2C_RechargeGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RechargeGetData, msg)
	return false
}

func (c *C2LRechargeGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RechargeGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RechargeGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_RechargeGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_RechargeGetData{
		Ret: uint32(cret.RET_OK),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_RECHARGE)) {
	//	l4g.Errorf("user: %d C2L_RechargeGetData: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}

	data := c.User.Recharge().Clone()
	smsg.NormalIds = data.NormalIds
	smsg.FirstGiftRewardStatus = c.User.Recharge().GetFirstGiftData()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RechargeGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LRechargeSimulationCommand struct {
	base.UserCommand
}

func (c *C2LRechargeSimulationCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRechargeSimulationCommand) Error(msg *cl.L2C_RechargeSimulation, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RechargeSimulation, msg)
	return false
}

func (c *C2LRechargeSimulationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RechargeSimulation{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RechargeSimulation Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_RechargeSimulation: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_RechargeSimulation{
		Ret: uint32(cret.RET_OK),
	}

	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_RECHARGE)) {
	//	l4g.Errorf("user: %d C2L_RechargeSimulation: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}

	if goxml.GetData().ServerInfoM.GM == 0 {
		// 此接口只有内部使用
		l4g.Errorf("user: %d C2L_RechargeSimulation: request error.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	order := c.generateOrder(cmsg.Custom)

	if order == nil {
		l4g.Errorf("user: %d C2L_RechargeSimulation: generate order error.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//db会自动把订单加入到redis的waitProcessOrder集合中
	c.Srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_GRPC_AddOrder), 0, &r2l.L2R_GRPC_AddOrder{
		Order: order,
		Callback: func(retCode uint32, order *db.Order) {
			l4g.Infof(" C2L_RechargeSimulation:: add order callback:%d %+v", retCode, order)
			smsg.Ret = retCode
			if retCode == uint32(cret.RET_OK) {
				//在线处理订单
				user := c.Srv.UserM().GetUserWithCache(order.UserId)
				if user != nil {
					//如果是缓存User，也需要进行添加，等上线再进行发货
					user.Orders().Add(order)
					if user.IsOnline() {
						user.Orders().Process(c.Srv, []*db.Order{order})
						l4g.Infof("user: %d C2L_RechargeSimulation:: online order:%+v", user.ID(), order)
						if order.Status != uint32(common.ORDER_STATUS_OS_SUCCESS) {
							smsg.Ret = uint32(cret.RET_RECHARGE_ORDER_PROCESS_FAIL)
						}
					} else {
						l4g.Infof("user: %d C2L_RechargeSimulation:: cache order:%d %+v", user.ID(), retCode, order)
					}
					user.SendCmdToGateway(cl.ID_MSG_L2C_RechargeSimulation, smsg)
				} else {
					l4g.Infof(" C2L_RechargeSimulation:: offline order:%d %+v", retCode, order)
				}
			}
		},
	})

	return c.ResultOK(smsg.Ret)
}

func (c *C2LRechargeSimulationCommand) generateOrder(custom *cl.OrderCustomData) *db.Order {
	productInfo := goxml.GetData().RechargeProductInfoM.GetProductInfoByCustomData(c.Srv.ServerType(), custom)
	if productInfo == nil {
		l4g.Errorf("user: %d C2L_RechargeSimulation: productInfo not exist. custom:%+v",
			c.Msg.UID, custom)
		return nil
	}
	newOrder := &db.Order{}
	id := c.Srv.CreateUniqueID()
	newOrder.OrderId = "simulation_" + strconv.FormatUint(id, 10)
	newOrder.CooperatorOId = newOrder.OrderId
	newOrder.OpId = custom.OpId
	newOrder.Amount = productInfo.Amount
	newOrder.Type = uint32(common.ORDER_TYPE_OT_RECHARGE) // 订单类型
	newOrder.GameId = custom.GameId
	newOrder.ServerId = c.Srv.ServerID()
	newOrder.UserId = c.User.ID()
	newOrder.Uuid = c.User.UUID()
	newOrder.ProductId = productInfo.ProductId
	newOrder.ChannelId = custom.Channel
	newOrder.PayTime = time.Now().UnixNano() / 1e6 //nolint:mnd
	newOrder.PayAmount = float64(productInfo.Amount)
	newOrder.PayCurrency = productInfo.Currency
	newOrder.Custom = custom.Clone()
	newOrder.CustomData, _ = character.JSONMarshal(custom)
	return newOrder
}

type C2LRechargeByCouponCommand struct {
	base.UserCommand
}

func (c *C2LRechargeByCouponCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRechargeByCouponCommand) Error(msg *cl.L2C_RechargeByCoupon, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RechargeByCoupon, msg)
	return false
}

func (c *C2LRechargeByCouponCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RechargeByCoupon{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LRechargeByCouponCommand Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2LRechargeByCouponCommand: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_RechargeByCoupon{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_COUPON_USE), c.Srv) {
		l4g.Errorf("user: %d C2LRechargeByCouponCommand: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_REFUND_COUPON_DISABLED))
	}

	order := c.generateOrder(cmsg.Custom)
	if order == nil {
		l4g.Errorf("user: %d C2LRechargeByCouponCommand: generate order error.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//代金券支付不能用于充值代金券
	if !c.canRechargeByCoupon(c.Srv, order) {
		l4g.Errorf("user: %d C2LRechargeByCouponCommand: invalid recharge type %d.", c.Msg.UID, order.Custom.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//扣除代金券
	param := goxml.GetData().ConfigInfoM.RechargeByCouponCost
	cost := goxml.GenSimpleResource(uint32(common.RESOURCE_COUPON), 0, order.Amount*param)
	cRet := c.User.Consume(c.Srv, []*cl.Resource{cost}, uint32(log.RESOURCE_CHANGE_REASON_RECHARGE_BY_COUPON), 0)
	if cRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2LRechargeByCouponCommand: coupon consume error. ret:%d", c.Msg.UID, cRet)
		return c.Error(smsg, cRet)
	}

	c.User.Orders().Process(c.Srv, []*db.Order{order})
	l4g.Infof("user: %d C2LRechargeByCouponCommand: process order:%+v", c.Msg.UID, order)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RechargeByCoupon, smsg)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LRechargeByCouponCommand) generateOrder(custom *cl.OrderCustomData) *db.Order {
	productInfo := goxml.GetData().RechargeProductInfoM.GetProductInfoByCustomData(c.Srv.ServerType(), custom)
	if productInfo == nil {
		l4g.Errorf("user: %d C2L_RechargeByCoupon: productInfo not exist. custom:%+v",
			c.Msg.UID, custom)
		return nil
	}
	newOrder := &db.Order{}
	id := c.Srv.CreateUniqueID() //代金券充值没有sdk订单号
	newOrder.OrderId = "coupon_" + strconv.FormatUint(id, 10)
	newOrder.CooperatorOId = newOrder.OrderId
	newOrder.OpId = custom.OpId
	newOrder.Amount = productInfo.Amount
	newOrder.Type = uint32(common.ORDER_TYPE_OT_COUPON) // 订单类型为代金券充值
	newOrder.GameId = custom.GameId
	newOrder.ServerId = c.Srv.ServerID()
	newOrder.UserId = c.User.ID()
	newOrder.Uuid = c.User.UUID()
	newOrder.ProductId = productInfo.ProductId
	newOrder.ChannelId = custom.Channel
	newOrder.PayTime = time.Now().UnixNano() / 1e6 //nolint:mnd
	newOrder.PayAmount = float64(productInfo.Amount)
	newOrder.PayCurrency = productInfo.Currency
	newOrder.Custom = custom.Clone()
	//newOrder.CustomData, _ = character.JSONMarshal(custom)
	newOrder.SdkPid = custom.SdkPid
	return newOrder
}

func (c *C2LRechargeByCouponCommand) canRechargeByCoupon(srv command.Servicer, order *db.Order) bool {
	// 代金券支付不能用于充值代金券
	var IsRechargeCoupon bool
	fun, exist := character.IsRechargeCouponFuncM[order.Custom.Type]
	if exist {
		IsRechargeCoupon = fun(srv, order, c.User)
	} else if uint32(common.RECHARGE_TYPE_RT_LIFELONG_GIFT) == order.Custom.Type {
		if srv.ServerType() == goxml.SERVER_TYPE_XIAO7 {
			return false
		}
	}

	if common.ORDER_TYPE(order.Type) == common.ORDER_TYPE_OT_COUPON && IsRechargeCoupon {
		return false
	}
	// 代金券支付不能用于官网充值
	if common.ORDER_TYPE(order.Type) == common.ORDER_TYPE_OT_RECHARGE && (common.RECHARGE_TYPE(order.Custom.Type) == common.RECHARGE_TYPE_RT_WEB_DIAMOND || common.RECHARGE_TYPE(order.Custom.Type) == common.RECHARGE_TYPE_RT_WEB_GIFT) {
		return false
	}
	//越南新加的两个活动不能用代金券充值
	if common.RECHARGE_TYPE(order.Custom.Type) == common.RECHARGE_TYPE_RT_ZERO_DOLLAR_PURCHASE || common.RECHARGE_TYPE(order.Custom.Type) == common.RECHARGE_TYPE_RT_GODDESS_PRIZE_DRAW {
		return false
	}
	return true
}

type C2LRechargeFirstGiftRewardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LRechargeFirstGiftRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRechargeFirstGiftRewardCommand) Error(msg *cl.L2C_RechargeFirstGiftReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RechargeFirstGiftReward, msg)
	return false
}

func (c *C2LRechargeFirstGiftRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RechargeFirstGiftReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RechargeFirstGiftReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_RechargeFirstGiftReward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_RechargeFirstGiftReward{
		Id:  cmsg.Id,
		Day: cmsg.Day,
		Ret: uint32(cret.RET_OK),
	}

	// 获取礼包配置
	firstRechargeInfo := goxml.GetData().FirstRechargeInfoM.GetRecordById(cmsg.Id)
	if firstRechargeInfo == nil {
		l4g.Errorf("user: %d C2L_RechargeFirstGiftReward: firstRechargeInfo not found, id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 单次发放全部奖励的首充礼包不需要领奖
	if firstRechargeInfo.Type == goxml.FirstGiftTypeSingleReward {
		l4g.Errorf("user: %d C2L_RechargeFirstGiftReward: invalid firstRechargeType %d", c.Msg.UID, firstRechargeInfo.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 检查领奖天数并获取对应奖励
	var awards []*cl.Resource
	switch cmsg.Day {
	case uint32(common.FIRST_GIFT_REWARD_DAY_FGRD_DAY_TWO):
		awards = firstRechargeInfo.SecondAwardClRes
	case uint32(common.FIRST_GIFT_REWARD_DAY_FGRD_DAY_THREE):
		awards = firstRechargeInfo.ThirdAwardClRes
	default:
		l4g.Errorf("user: %d C2L_RechargeFirstGiftReward: invalid reward day %d", c.Msg.UID, smsg.Day)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 检查是否已领奖
	if ret := c.User.Recharge().CheckFirstGiftCanAward(cmsg.Id, cmsg.Day-1); ret != cret.RET_OK { // cmsg.Day > 1
		l4g.Errorf("user: %d C2L_RechargeFirstGiftReward: cannot reward, err:%d", c.Msg.UID, ret)
		return c.Error(smsg, uint32(ret))
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_FIRST_RECHARGE_GIFT), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_RechargeFirstGiftReward: send award failed. retCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	smsg.RewardStatus = c.User.Recharge().SetFirstGiftAwardToken(cmsg.Id, cmsg.Day-1) // cmsg.Day > 1

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RechargeFirstGiftReward, smsg)
	return c.ResultOK(smsg.Ret)
}
