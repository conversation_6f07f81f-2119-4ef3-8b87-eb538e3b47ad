package base

import (
	"app/csession"
	"app/logic/activity"
	"app/logic/activity/bancmd"
	aguild "app/logic/activity/guild"
	"context"

	"app/goxml"
	"app/logic/character"
	"app/logic/command"
	"app/logic/session"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/service"

	//"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func GetService(ctx context.Context) command.Servicer {
	return service.GetService(ctx).(command.Servicer)
}

type Command struct {
	Msg       *session.Message
	ProtoData []byte //proto data
	Srv       command.Servicer
	BanCmdM   *bancmd.Manager
}

func (c *Command) OnPause(ctx context.Context, msg parse.Messager) {
	parse.PutReadBufferToPool(c.Msg.Data)
}

func (c *Command) OnBefore(ctx context.Context, msg parse.Messager) bool {
	c.Msg = msg.(*session.Message)
	c.ProtoData = c.Msg.Data[parse.PackHeadSize:c.Msg.Length]
	if c.Srv == nil {
		c.Srv = GetService(ctx)
	}
	if c.BanCmdM == nil {
		banCmdM, ok := c.Srv.GetActivity(activity.BanCmd).(*bancmd.Manager)
		if ok {
			if banCmdM.Exist(c.Msg.GetCmd()) {
				notify := &cl.L2C_NotifyBanCmd{Cmd: c.Msg.GetCmd()}
				c.Srv.SendCmdToClient(c.Msg.SessionID, uint32(cl.ID_MSG_L2C_NotifyBanCmd), c.Msg.UID, notify)
				return false
			}
		}
	}

	return true
}

func (c *Command) OnAfter(ctx context.Context, result bool) {
	//回收，减少内存分配
	parse.PutReadBufferToPool(c.Msg.Data)
	c.Msg = nil
	c.ProtoData = nil
}

func (c *Command) CheckCrypto() bool {
	if c.Srv.EnableCrypto() && goxml.GetData().XorKeyM.NewSize > 0 && (c.Msg.Flags&parse.MsgFlagCrypto) != parse.MsgFlagCrypto {
		l4g.Errorf("crypto decode error, user: %d msg: %d flags: %d", c.Msg.UID, c.Msg.Cmd, c.Msg.Flags)
		return false
	}
	return true
}

type UserCommand struct {
	Command
	User *character.User
}

func (u *UserCommand) OnPause(ctx context.Context, msg parse.Messager) {
	u.Command.OnPause(ctx, msg)
}

func (u *UserCommand) OnBefore(ctx context.Context, msg parse.Messager) bool {
	if !u.Command.OnBefore(ctx, msg) {
		return false
	}
	u.User = u.Srv.UserM().GetUser(u.Msg.UID)
	if u.User == nil {
		l4g.Errorf("not found user: %d msg: %d", u.Msg.UID, u.Msg.Cmd)
		return false
	}
	if u.Msg.Cmd < uint32(l2c.ID_MSG_MIN) {
		if !u.CheckCrypto() {
			return false
		}
	}
	if !u.User.CheckAndSetCmdInterval(u.Msg.Cmd) {
		return false
	}
	if uint32(cl.ID_MSG_BEGIN) < u.Msg.Cmd &&
		u.Msg.Cmd < uint32(cl.ID_MSG_END) {
		u.User.SetCSeq(u.Msg.CSeq)
	}
	u.User.SetContextID(u.Srv.CreateLogID())
	u.User.LogLoginAuto(u.Srv) //完美的每天零点在线自动记录登录日志
	u.User.ResetSeason(u.Srv)  // 赛季重置处理
	guildM, ok := u.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if ok {
		guild := guildM.GetGuildByUser(u.User.ID())
		if guild != nil {
			guild.CheckSyncMedal(u.Srv, guildM)
		}
	}
	return true
}

func (u *UserCommand) OnAfter(ctx context.Context, result bool) {
	if u.User != nil {
		u.User.CmdArray().Add(u.Msg.Cmd, result)
		u.User.SetContextID(0)
		u.User.PushNilMsg(u.Msg.Cmd, result)
		u.User = nil
	}
	u.Command.OnAfter(ctx, result)
}

func (u *UserCommand) Trace(msg interface{}) {
	u.User.LogTrace(u.Srv, u.Msg.Cmd, msg)
}

// 需要重置每日数据，每周数据的时候，继承这个Command
type TimelyResetUserCommand struct {
	UserCommand
}

func (u *TimelyResetUserCommand) OnPause(ctx context.Context, msg parse.Messager) {
	u.UserCommand.OnPause(ctx, msg)
}

func (u *TimelyResetUserCommand) OnBefore(ctx context.Context, msg parse.Messager) bool {
	if !u.UserCommand.OnBefore(ctx, msg) {
		return false
	}
	u.User.ResetDaily(0, u.Srv)
	u.User.ResetWeekly(0)
	u.User.ResetMonthly(0)
	u.User.ResetWeeklyFriday(0)
	u.User.ResetShops(u.Srv)
	return true
}

func (u *TimelyResetUserCommand) OnAfter(ctx context.Context, result bool) {
	u.UserCommand.OnAfter(ctx, result)
}

type LimitedCommand struct {
	UserCommand
}

func (l *LimitedCommand) OnPause(ctx context.Context, msg parse.Messager) {
	l.UserCommand.OnPause(ctx, msg)
}

func (l *LimitedCommand) OnBefore(ctx context.Context, msg parse.Messager) bool {
	if !l.UserCommand.OnBefore(ctx, msg) {
		return false
	}
	if goxml.GetData().ServerInfoM.GM > 0 {
		return true
	}
	l4g.Errorf("正式服务器，玩家 %d 请求测试命令: %d", l.Msg.UID, l.Msg.Cmd)
	return false
}

func (l *LimitedCommand) OnAfter(ctx context.Context, result bool) {
	l.UserCommand.OnAfter(ctx, result)
}

type MasterCommand struct {
	Msg       *csession.Message
	ProtoData []byte //proto data
	Srv       command.Servicer
}

func (c *MasterCommand) OnPause(ctx context.Context, msg parse.Messager) {
	parse.PutReadBufferToPool(c.Msg.Data)
}

func (c *MasterCommand) OnBefore(ctx context.Context, msg parse.Messager) bool {
	c.Msg = msg.(*csession.Message)
	c.ProtoData = c.Msg.Data[parse.PackHeadSize:c.Msg.Length]
	if c.Srv == nil {
		c.Srv = GetService(ctx)
	}
	return true
}

func (c *MasterCommand) OnAfter(ctx context.Context, result bool) {
	//回收，减少内存分配
	parse.PutReadBufferToPool(c.Msg.Data)
	c.Msg = nil
	c.ProtoData = nil
}
