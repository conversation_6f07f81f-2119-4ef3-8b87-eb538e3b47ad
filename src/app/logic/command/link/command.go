package link

import (
	"app/goxml"
	"app/logic/command/base"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_LinkInfo), &C2LLinkInfoCommand{}, state)       //获取联结信息
	cmds.Register(uint32(cl.ID_MSG_C2L_LinkSetView), &C2LLinkSetViewCommand{}, state) //设置查看的联结信息
}

type C2LLinkInfoCommand struct {
	base.UserCommand
}

func (c *C2LLinkInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *C2LLinkInfoCommand) Error(msg *cl.L2C_LinkInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_LinkInfo, msg)
	return false
}

func (c *C2LLinkInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_LinkInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_LinkInfo Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_LinkInfo: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_LinkInfo{
		Ret: uint32(ret.RET_OK),
	}
	smsg.Info = c.User.Link().Clone()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_LinkInfo, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LLinkSetViewCommand struct {
	base.UserCommand
}

func (c *C2LLinkSetViewCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *C2LLinkSetViewCommand) Error(msg *cl.L2C_LinkSetView, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_LinkSetView, msg)
	return false
}

func (c *C2LLinkSetViewCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_LinkSetView{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_LinkSetView Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_LinkSetView: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_LinkSetView{
		Ret: uint32(ret.RET_OK),
	}

	if goxml.GetData().HeroInfoM.Index(cmsg.HeroSysID) == nil {
		l4g.Errorf("user: %d C2L_LinkSetView: param error.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	if goxml.GetData().HeroInfoM.GetLinkHeros(cmsg.LinkId) == nil {
		l4g.Errorf("user: %d C2L_LinkSetView: param error.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	c.User.Link().SetView(cmsg.HeroSysID, cmsg.LinkId)
	smsg.HeroSysID = cmsg.HeroSysID
	smsg.LinkId = cmsg.LinkId
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_LinkSetView, smsg)
	return c.ResultOK(smsg.Ret)
}
