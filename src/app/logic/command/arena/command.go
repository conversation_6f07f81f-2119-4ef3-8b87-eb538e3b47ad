package arena

import (
	"context"
	"strconv"

	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/arena"
	"app/logic/character"
	"app/logic/event"
	"app/logic/helper"

	"app/logic/command"
	"app/logic/command/base"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ArenaInfo), &C2LArenaInfoCommand{}, state)           //竞技场信息
	cmds.Register(uint32(cl.ID_MSG_C2L_ArenaRefresh), &C2LArenaRefreshCommand{}, state)     //刷新对手
	cmds.Register(uint32(cl.ID_MSG_C2L_ArenaFight), &C2LArenaFightCommand{}, state)         //战斗
	cmds.Register(uint32(cl.ID_MSG_C2L_ArenaLogList), &C2LArenaLogListCommand{}, state)     //战报信息
	cmds.Register(uint32(cl.ID_MSG_C2L_ArenaLike), &C2LArenaLikeCommand{}, state)           //点赞
	cmds.Register(uint32(cl.ID_MSG_C2L_ArenaRank), &C2LArenaRankCommand{}, state)           //技场排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_ArenaRecvAward), &C2LArenaRecvAwardCommand{}, state) //领取任务奖励
}

type C2LArenaInfoCommand struct {
	base.UserCommand
}

func (c *C2LArenaInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArenaInfoCommand) Error(msg *cl.L2C_ArenaInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaInfo, msg)
	return false
}

func (c *C2LArenaInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArenaInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArenaInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArenaInfo: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArenaInfo{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARENA), c.Srv) {
		l4g.Errorf("user: %d C2L_ArenaInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	//初始化数据
	arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
	arenaUser := arenaM.Get(c.Msg.UID)
	if arenaUser == nil {
		flag, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(uint32(common.FORMATION_ID_FI_ARENA_DEFENSE))
		if !flag {
			l4g.Errorf("user: %d C2L_ArenaInfo: formation info not exist", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		//防守阵容
		flag = arenaM.InitFormation(c.Srv, c.User, teamNum)
		if !flag {
			l4g.Errorf("user: %d C2L_ArenaInfo: init defense formation failed", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}
		//基础数据
		arenaUser = arenaM.InitOne(c.Srv, c.User.ID(), c.User.ArenaDefensePower())
		//生成对手
		updateOpponents(c.Srv, arenaM, c.User)
	}

	//赛季重置，重新匹配对手
	if arenaUser.IsResetOpponents() {
		updateOpponents(c.Srv, arenaM, c.User)
		arenaUser.CancelResetOpponentsFlag()
	}

	opponentM := c.User.ArenaOpponent()
	smsg.Rank = arenaM.Rank.GetRank(c.Msg.UID)
	smsg.Score = arenaUser.GetScore()
	smsg.RefreshTm = opponentM.GetRefreshTm()
	smsg.NewLogTip = arenaUser.GetNewLogTip()
	smsg.Resetting = arenaM.IsRestting()
	smsg.Task = arenaUser.FlushTaskData()
	smsg.FightCount = arenaUser.GetFightCount()

	if cmsg.ShowTop3 {
		smsg.Season = arenaM.Season.GetSeasonID()
		topData := arenaM.SeasonTop.Flush()
		l4g.Debugf("topData: %+v ", topData)
		if topData != nil {
			for k, v := range topData.Users {
				l4g.Debugf("topUser: %+v ", v.User)
				topUser := arenaM.Get(v.User.Id)
				topData.Users[k].Liked = topUser.GetLiked()
			}
			smsg.TopPlayers = topData
		}
	}

	//获取对手信息
	opponents := opponentM.GetOpponents()
	if len(opponents) > 0 {
		list, requestUids := getOpponentsData(arenaM, opponents)
		if len(requestUids) > 0 {
			c.GetLocalUserSnapshots(requestUids, &AsyncC2LArenaInfoReq{smsg, requestUids, list}, 0)
		} else {
			smsg.Opponents = list
			c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaInfo, smsg)
		}
	} else {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaInfo, smsg)
	}

	//检查更新防守阵容战力
	isUpdate := arenaUser.CheckUpdatePower(c.User.ArenaDefensePower(), false)
	if isUpdate {
		arenaM.SetChange(c.Srv, arenaUser)
	}

	return c.ResultOK(smsg.Ret)
}

func updateOpponents(srv character.Servicer, arenaM *arena.Manager, user *character.User) {
	arenaUser := arenaM.Get(user.ID())
	//将对手数据，导入已搜列表
	//避免登录后的首次刷新，出现重复
	hasInitSearchList := arenaUser.IsFinishInitSearchList()
	if !hasInitSearchList {
		opponentM := user.ArenaOpponent()
		for stage, uid := range opponentM.GetOpponentsUids() {
			if uid > 0 {
				arenaUser.UpdateSearchList(stage, uid)
			}
		}
		arenaUser.FinishInitSearchListStatus()
	}

	opponents := arenaM.GenerateOpponents(srv, arenaUser, user.Level(), user.MaxPower())
	if len(opponents) > 0 {
		user.ArenaOpponent().Update(opponents)
	}
}

type AsyncC2LArenaInfoReq struct {
	smsg        *cl.L2C_ArenaInfo
	requestUids []uint64
	list        []*cl.ArenaUserInfo
}

func (ar *AsyncC2LArenaInfoReq) Resp(srv command.Servicer, args *character.Args,
	retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	requestUids := ar.requestUids
	list := ar.list
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaInfo: get snapshot error. code:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaInfo, smsg)
		return false
	}

	snaps := dbData.([]*cl.UserSnapshot)
	l4g.Debugf("user: %d C2L_ArenaInfo: get data:%+v", args.UID, snaps)
	snapm := make(map[uint64]*cl.UserSnapshot, len(snaps))
	for k, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d C2L_ArenaInfo: no data. uid:%d",
				args.UID, requestUids[k])
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaInfo, smsg)
			return false
		}
		snapm[v.Id] = v
	}
	for _, v := range requestUids {
		if snapm[v] == nil {
			l4g.Errorf("user: %d C2L_ArenaInfo: no data. uid:%d",
				args.UID, v)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaInfo, smsg)
			return false
		}
	}

	for _, base := range list {
		if !base.Bot {
			base.User = snapm[base.User.Id]
		}
	}
	smsg.Opponents = list
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaInfo, smsg)
	return true
}

func getOpponentsData(arenaM *arena.Manager,
	opponents []*db.ArenaOpponent) ([]*cl.ArenaUserInfo, []uint64) {
	list := make([]*cl.ArenaUserInfo, 0, len(opponents))
	var requestUids []uint64
	for _, v := range opponents { //nolint:varnamelen
		info := &cl.ArenaUserInfo{}
		if v.Bot {
			info.User = &cl.UserSnapshot{
				Id:     v.Id,
				Level:  v.Level,
				Name:   v.Name,
				BaseId: v.BaseId,
			}
			info.Score = v.Score
			info.Bot = v.Bot
		} else {
			opArenaUser := arenaM.Get(v.Id)
			if opArenaUser == nil {
				l4g.Errorf("getOpponentsData. no opArenaUser, uid:%d", v.Id)
				continue
			}

			info.User = &cl.UserSnapshot{
				Id: v.Id,
			}
			info.Score = opArenaUser.GetScore()
			info.Rank = arenaM.Rank.GetRank(v.Id)
			requestUids = append(requestUids, v.Id)
		}
		list = append(list, info)
	}
	return list, requestUids
}

type C2LArenaRefreshCommand struct {
	base.UserCommand
}

func (c *C2LArenaRefreshCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArenaRefreshCommand) Error(msg *cl.L2C_ArenaRefresh, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRefresh, msg)
	return false
}

func (c *C2LArenaRefreshCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArenaRefresh{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArenaRefresh Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArenaRefresh: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArenaRefresh{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARENA), c.Srv) {
		l4g.Errorf("user: %d C2L_ArenaRefresh: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
	if arenaM.IsRestting() {
		l4g.Errorf("user: %d C2L_ArenaRefresh: arena is resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_NOT_OPEN))
	}

	arenaUser := arenaM.Get(c.Msg.UID)
	if arenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaRefresh: no user data", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_NO_USER_DATA))
	}

	//检查次数
	ret, costs, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_ARENA_REFRESH_COUNT), 1, c.Srv)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaRefresh: CheckNumByType failed: %d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	//检查刷新时间
	opponentM := c.User.ArenaOpponent()
	lastTm := opponentM.GetRefreshTm()
	now := time.Now().Unix()
	if lastTm+int64(goxml.GetData().ArenaConfigInfoM.RefreshCD) > now {
		l4g.Errorf("user: %d C2L_ArenaRefresh: refresh cd error. lastTm:%d now:%d",
			c.Msg.UID, lastTm, now)
		return c.Error(smsg, uint32(cret.RET_ARENA_REFRESH_CD_ERROR))
	}

	if len(costs) > 0 {
		smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_ARENA_REFRESH), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_ArenaRefresh: consume err", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}
	}

	//更新今日挑战次数
	c.User.AddNumByType(uint32(common.PURCHASEID_ARENA_REFRESH_COUNT), numSummary)
	//设置刷新时间
	opponentM.SetRefreshTm(now)
	//匹配对手
	updateOpponents(c.Srv, arenaM, c.User)
	arenaM.SetChange(c.Srv, arenaUser)

	smsg.RefreshTm = now

	//获取对手信息
	opponents := opponentM.GetOpponents()
	if len(opponents) > 0 {
		list, requestUids := getOpponentsData(arenaM, opponents)
		if len(requestUids) > 0 {
			c.GetLocalUserSnapshots(requestUids, &AsyncC2LArenaRefreshReq{smsg, requestUids, list}, 0)
		} else {
			smsg.Opponents = list
			c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRefresh, smsg)
			c.User.LogArenaRefresh(c.Srv, smsg.RefreshTm, c.User.ContextID())
		}
	}
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LArenaRefreshReq struct {
	smsg        *cl.L2C_ArenaRefresh
	requestUids []uint64
	list        []*cl.ArenaUserInfo
}

func (ar *AsyncC2LArenaRefreshReq) Resp(srv command.Servicer, args *character.Args,
	retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	requestUids := ar.requestUids
	list := ar.list
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LArenaRefreshCommand: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRefresh, smsg)
		return false
	}

	snaps := dbData.([]*cl.UserSnapshot)
	l4g.Debugf("user: %d C2L_ArenaRefresh: get data:%+v", args.UID, snaps)
	snapm := make(map[uint64]*cl.UserSnapshot, len(snaps))
	for k, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d C2L_ArenaRefresh: no data. uid:%d",
				args.UID, requestUids[k])
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRefresh, smsg)
			return false
		}
		snapm[v.Id] = v
	}
	for _, v := range requestUids {
		if snapm[v] == nil {
			l4g.Errorf("user: %d C2L_ArenaRefresh: no data. uid:%d",
				args.UID, v)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRefresh, smsg)
			return false
		}
	}

	arenaM := srv.GetActivity(activity.Arena).(*arena.Manager)
	for _, base := range list {
		if !base.Bot {
			base.User = snapm[base.User.Id]
			opArenaUser := arenaM.Get(base.User.Id)
			if opArenaUser != nil {
				//检查更新防守阵容战力
				isUpdate := opArenaUser.CheckUpdatePower(base.User.ArenaPower, false)
				if isUpdate {
					arenaM.SetChange(srv, opArenaUser)
				}
			}
		}
	}
	smsg.Opponents = list
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRefresh, smsg)
	args.Caller.LogArenaRefresh(srv, smsg.RefreshTm, args.CtxID)
	return true
}

type C2LArenaFightCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LArenaFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArenaFightCommand) Error(msg *cl.L2C_ArenaFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, msg)
	return false
}

//nolint:funlen
func (c *C2LArenaFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArenaFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArenaFight Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArenaFight: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_ArenaFight{
		Ret:   uint32(cret.RET_OK),
		Id:    cmsg.Id,
		Score: cmsg.Score,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARENA), c.Srv) {
		l4g.Errorf("user: %d C2L_ArenaFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_ArenaFight: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
	if arenaM.IsRestting() {
		l4g.Errorf("user: %d C2L_ArenaFight: arena is resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_NOT_OPEN))
	}

	attackFormation := c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_ARENA_ATTACK))
	if attackFormation == nil {
		l4g.Errorf("user: %d C2L_ArenaFight: no attack formation", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_ATTACK_FORMATION_NOT_EXIST))
	}

	opponentM := c.User.ArenaOpponent()
	opponents := opponentM.GetOpponents()
	if len(opponents) != int(goxml.ArenaOpponentNum) {
		l4g.Errorf("user: %d C2L_ArenaFight: opponent num error %d",
			c.Msg.UID, len(opponents))
		return c.Error(smsg, uint32(cret.RET_ARENA_OPPONENT_NUM_ERROR))
	}

	myArenaUser := arenaM.Get(c.Msg.UID)
	if myArenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaFight: no user data", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_NO_USER_DATA))
	}

	if myArenaUser.IsLocked() {
		l4g.Errorf("user: %d C2L_ArenaFight: self is attacking or being attacked", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_SELF_BEING_ATTACKED))
	}

	var (
		exist, opBot                                 bool
		opArenaUser                                  *arena.User
		opName                                       string
		monsterID, opBaseID, opLevel, opScore, stage uint32
	)
	for k, o := range opponents { //nolint:varnamelen
		if o.Id == cmsg.Id {
			exist = true
			if o.Bot {
				botInfo := goxml.GetData().BotInfoM.Index(uint32(cmsg.Id))
				if botInfo == nil {
					l4g.Errorf("user: %d C2L_ArenaFight: bot info not exist. id:%d",
						c.Msg.UID, cmsg.Id)
					return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
				}
				opBot = true
				monsterID = botInfo.MonsterGroup
				opName = o.Name
				opLevel = o.Level
				opBaseID = o.BaseId
				opScore = o.Score
			} else {
				opArenaUser = arenaM.Get(cmsg.Id)
				if opArenaUser == nil {
					l4g.Errorf("user: %d C2L_ArenaFight: no op data. id:%d",
						c.Msg.UID, cmsg.Id)
					return c.Error(smsg, uint32(cret.RET_ARENA_NO_USER_DATA))
				}

				if opArenaUser.IsLocked() {
					l4g.Errorf("user: %d C2L_ArenaFight: op is being attacked. id:%d",
						c.Msg.UID, cmsg.Id)
					return c.Error(smsg, uint32(cret.RET_ARENA_OP_BEING_ATTACKED))
				}

				var diff uint32
				opScore = opArenaUser.GetScore()
				if opScore > cmsg.Score {
					diff = opScore - cmsg.Score
				} else {
					diff = cmsg.Score - opScore
				}
				if diff > goxml.GetData().ArenaConfigInfoM.ScoreDiffLimit {
					l4g.Debugf("user: %d C2L_ArenaFight: op score change a lot. id:%d diff:%d",
						c.Msg.UID, cmsg.Id, diff)
					return c.Error(smsg, uint32(cret.RET_ARENA_OPPONENT_SCORE_CHANGE))
				}
				stage = uint32(k)
			}
			break
		}
	}
	if !exist {
		l4g.Errorf("user: %d C2L_ArenaFight: no op data. id:%d opponents:%v",
			c.Msg.UID, cmsg.Id, opponents)
		return c.Error(smsg, uint32(cret.RET_ARENA_OPPONENT_NOT_EXIST))
	}

	//检查挑战门票
	var costs []*cl.Resource
	fret, fightCost, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_ARENA_FIGHT_COUNT), 1, c.Srv)
	if fret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaFight: check ticket err: %d", c.Msg.UID, fret)
		return c.Error(smsg, fret)
	}
	if fightCost != nil {
		costs = fightCost
	}

	var (
		rewardType, winnerScore, loserScore uint32
		selfScoreChange, opScoreChange      int32
	)
	selfScore := myArenaUser.GetScore()
	selfRank := arenaM.Rank.GetRank(c.Msg.UID)
	matchInfo := goxml.GetData().ArenaMatchInfoM.Match(selfScore)

	l4g.Debugf("user: %d C2L_ArenaFight: opBot:%v, selfScore:%d, opScore:%d, fightCost:%+v, matchInfo:%+v",
		c.Msg.UID, opBot, selfScore, opScore, costs, matchInfo)
	if opBot {
		isWin, reportID, bRet := c.User.AttackArenaRobot(c.Srv, monsterID, opName, cmsg.ClientData)
		if bRet != cret.RET_OK {
			l4g.Errorf("user: %d C2L_ArenaFight: AttackArenaRobot failed. err:%d", c.Msg.UID, bRet)
			return c.Error(smsg, uint32(cret.RET_ARENA_BATTLE_ERROR))
		}

		if isWin {
			rewardType = goxml.ArenaFightWin
			winnerScore = selfScore
			loserScore = opScore
		} else {
			rewardType = goxml.ArenaFightLose
			winnerScore = opScore
			loserScore = selfScore
		}
		awards, isReset := GenerateArenaReward(c.Srv, c.User, myArenaUser, rewardType)
		if awards == nil {
			l4g.Errorf("user: %d C2L_ArenaFight: award info not exist. rewardType:%d",
				c.Msg.UID, rewardType)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		addScore, subScore := goxml.GetData().ArenaMatchInfoM.EloScores(matchInfo, winnerScore, loserScore, 0, false)

		if isWin {
			selfScoreChange = int32(addScore)
			opScoreChange = int32(0 - subScore)
		}

		//更新今日挑战次数
		c.User.AddNumByType(uint32(common.PURCHASEID_ARENA_FIGHT_COUNT), numSummary)

		//掉落活动 - 额外奖励
		daDropGroup, _, _ := character.GetDropActivity(c.Srv,
			uint32(common.FUNCID_MODULE_ARENA), goxml.SubFuncNone, c.User)
		if daDropGroup > 0 {
			if ret, flag := character.DoDrop(c.Srv.Rand(), daDropGroup); flag {
				awards = append(awards, ret...)
			}
		}

		//更新挑战获得和消耗的资源
		if len(costs) > 0 {
			smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, awards,
				uint32(log.RESOURCE_CHANGE_REASON_ARENA_FIGHT), 0)
		} else {
			smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
				uint32(log.RESOURCE_CHANGE_REASON_ARENA_FIGHT), 0)
		}
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_ArenaFight: update resource failed. costs:%v awards:%v",
				c.Msg.UID, costs, awards)
			return c.Error(smsg, smsg.Ret)
		}

		myArenaUser.UpdateRewardTimes(rewardType, isReset)

		//更新战斗相关数据
		attacker := c.User.NewMiniUserSnapshot()
		attacker.CurFormationPower = c.User.CalFormationPower(uint32(common.FORMATION_ID_FI_ARENA_ATTACK))
		defender := &cl.MiniUserSnapshot{
			Id:     cmsg.Id,
			Level:  opLevel,
			Name:   opName,
			BaseId: opBaseID,
		}
		updateFightData(c.Srv, selfScoreChange, opScoreChange, reportID,
			isWin, attacker, defender)

		//更新对手
		updateOpponents(c.Srv, arenaM, c.User)
		arenaM.SetChange(c.Srv, myArenaUser)

		selfNewRank := arenaM.Rank.GetRank(c.Msg.UID)
		selfNewScore := myArenaUser.GetScore()
		opNewScore := int32(opScore) + opScoreChange
		if opNewScore < 0 {
			opNewScore = 0
		}

		l4g.Debugf("user:%d arenaFight robot. win:%v, score:%d, rank:%d",
			c.Msg.UID, isWin, selfNewScore, selfNewRank)

		smsg.ReportId = reportID
		smsg.Win = isWin
		smsg.SelfChange = &cl.ArenaUserScoreAndRank{
			NewScore: selfNewScore,
			OldScore: selfScore,
			NewRank:  selfNewRank,
			OldRank:  selfRank,
		}
		smsg.OpponentChange = &cl.ArenaUserScoreAndRank{
			NewScore: uint32(opNewScore),
			OldScore: opScore,
		}
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		c.User.LogArenaFight(c.Srv, cmsg.Id, c.User.ContextID(), arenaM.Season.Get().GetId(),
			selfRank, selfNewScore, selfNewRank, isWin, opBot, smsg.ReportId)

		//抛出事件
		var winInt uint32
		if isWin {
			winInt = 1
		}
		c.User.FireCommonEvent(c.Srv.EventM(), event.IeArenaFight, 1, winInt, selfNewScore, selfNewRank)
		c.User.FireCommonEvent(c.Srv.EventM(), event.IeHotRankUpdate, uint64(selfNewRank), uint32(common.FORMATION_ID_FI_ARENA_ATTACK))
	} else {
		myArenaUser.Lock()
		opArenaUser.Lock()
		requestUids := []uint64{cmsg.Id}
		formationID := uint32(common.FORMATION_ID_FI_ARENA_DEFENSE)
		req := &AsyncC2LArenaFightReq{
			score:      cmsg.Score,
			stage:      stage,
			opUID:      cmsg.Id,
			smsg:       smsg,
			ClientData: cmsg.ClientData,
		}
		c.GetLocalUserBattleData(formationID, requestUids, req, func() {
			myArenaUser.Unlock()
			opArenaUser.Unlock()
		})
	}
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LArenaFightReq struct {
	score      uint32
	stage      uint32
	opUID      uint64
	smsg       *cl.L2C_ArenaFight
	ClientData []byte
}

//nolint:funlen
func (ar *AsyncC2LArenaFightReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	opUID := ar.opUID
	score := ar.score

	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaFight: get snapshot failed. error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}
	arenaM := srv.GetActivity(activity.Arena).(*arena.Manager)
	myArenaUser := arenaM.Get(args.UID)
	if myArenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaFight: no user data", args.UID)
		smsg.Ret = uint32(cret.RET_ARENA_NO_USER_DATA)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}

	opArenaUser := arenaM.Get(opUID)
	if opArenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaFight: no op data. uid:%d",
			args.UID, opUID)
		smsg.Ret = uint32(cret.RET_ARENA_NO_USER_DATA)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}

	//检查挑战门票
	var costs []*cl.Resource
	fret, fightCost, numSummary := args.Caller.CheckNumByType(uint32(common.PURCHASEID_ARENA_FIGHT_COUNT), 1, srv)
	if fret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaFight: check ticket err: %d", args.UID, fret)
		smsg.Ret = fret
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}
	if fightCost != nil {
		costs = fightCost
	}

	selfScore := myArenaUser.GetScore()
	selfRank := arenaM.Rank.GetRank(args.UID)
	matchInfo := goxml.GetData().ArenaMatchInfoM.Match(selfScore)
	opScore := opArenaUser.GetScore()
	opRank := arenaM.Rank.GetRank(opUID) //对手排名

	//检查分数差异
	var diff uint32
	if opScore > score {
		diff = opScore - score
	} else {
		diff = score - opScore
	}
	if diff > goxml.GetData().ArenaConfigInfoM.ScoreDiffLimit {
		l4g.Errorf("user: %d C2L_ArenaFight: op score change a lot. id:%d diff:%d",
			args.UID, opUID, diff)
		smsg.Ret = uint32(cret.RET_ARENA_OPPONENT_SCORE_CHANGE)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}

	snaps := dbData.([]*db.UserBattleData)
	snapm := make(map[uint64]*db.UserBattleData, len(snaps))
	for _, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d C2L_ArenaFight: no data. uid:%d", args.UID, opUID)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
			return false
		}
		snapm[v.User.Id] = v
	}

	if snapm[opUID] == nil {
		l4g.Errorf("user: %d C2L_ArenaFight: no data. uid:%d", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}

	opUser := character.GetUserFromUserBattleData(srv, snapm[opUID])
	clReport, bRet := args.Caller.AttackArenaUser(srv, opUser, common.FORMATION_ID_FI_ARENA_ATTACK, common.FORMATION_ID_FI_ARENA_DEFENSE, ar.ClientData)
	if bRet != cret.RET_OK {
		l4g.Errorf("user: %d C2L_ArenaFight: AttackArenaUser failed. err:%d",
			args.UID, bRet)
		smsg.Ret = uint32(cret.RET_ARENA_BATTLE_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}
	isWin := clReport.GetWin()
	reportID := clReport.GetId()

	var rewardType, winnerScore, loserScore uint32
	if isWin {
		rewardType = goxml.ArenaFightWin
		winnerScore = selfScore
		loserScore = opScore
	} else {
		rewardType = goxml.ArenaFightLose
		winnerScore = opScore
		loserScore = selfScore
	}
	awards, isReset := GenerateArenaReward(srv, args.Caller, myArenaUser, rewardType)
	if awards == nil {
		l4g.Errorf("user: %d C2L_ArenaFight: award info not exist. rewardType:%d",
			args.UID, rewardType)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}

	needFix := isWin
	addScore, subScore := goxml.GetData().ArenaMatchInfoM.EloScores(matchInfo, winnerScore,
		loserScore, ar.stage, needFix)

	var selfScoreChange, opScoreChange int32
	if isWin {
		selfScoreChange = int32(addScore)
		opScoreChange = int32(0 - subScore)
	}

	//更新今日挑战次数
	args.Caller.AddNumByType(uint32(common.PURCHASEID_ARENA_FIGHT_COUNT), numSummary)

	//掉落活动 - 额外奖励
	daDropGroup, _, _ := character.GetDropActivity(srv,
		uint32(common.FUNCID_MODULE_ARENA), goxml.SubFuncNone, args.Caller)
	if daDropGroup > 0 {
		if ret, flag := character.DoDrop(srv.Rand(), daDropGroup); flag {
			awards = append(awards, ret...)
		}
	}

	//更新挑战获得和消耗的资源
	if len(costs) > 0 {
		smsg.Ret, smsg.Awards = args.Caller.Trade(srv, costs, awards,
			uint32(log.RESOURCE_CHANGE_REASON_ARENA_FIGHT), 0)
	} else {
		smsg.Ret, smsg.Awards = args.Caller.Award(srv, awards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_ARENA_FIGHT), 0)
	}
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaFight: Update resource failed. costs:%+v, awards:%+v",
			args.UID, costs, awards)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}

	myArenaUser.UpdateRewardTimes(rewardType, isReset)

	//更新战斗相关数据
	attacker := args.Caller.NewMiniUserSnapshot()
	attacker.CurFormationPower = args.Caller.CalFormationPower(uint32(common.FORMATION_ID_FI_ARENA_ATTACK))
	defender := opUser.NewMiniUserSnapshot()
	defender.CurFormationPower = opUser.CalFormationPower(uint32(common.FORMATION_ID_FI_ARENA_DEFENSE))
	updateFightData(srv, selfScoreChange, opScoreChange, reportID,
		isWin, attacker, defender)

	//更新对手
	updateOpponents(srv, arenaM, args.Caller)
	arenaM.SetChange(srv, myArenaUser)

	selfNewRank := arenaM.Rank.GetRank(args.UID)
	selfNewScore := myArenaUser.GetScore()
	opNewRank := arenaM.Rank.GetRank(opUID)
	opNewScore := opArenaUser.GetScore()

	l4g.Debugf("user:%d arenaFight player. win:%v, score:%d, rank:%d",
		args.UID, isWin, selfNewScore, selfNewRank)

	smsg.ReportId = reportID
	smsg.Win = isWin
	smsg.SelfChange = &cl.ArenaUserScoreAndRank{
		NewScore: selfNewScore,
		OldScore: selfScore,
		NewRank:  selfNewRank,
		OldRank:  selfRank,
	}
	smsg.OpponentChange = &cl.ArenaUserScoreAndRank{
		NewScore: opNewScore,
		OldScore: opScore,
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
	args.Caller.LogArenaFight(srv, opUID, args.CtxID, arenaM.Season.Get().GetId(),
		selfRank, selfNewScore, selfNewRank, isWin, false, smsg.ReportId)
	opUser.LogArenaBeFight(srv, args.UID, args.CtxID, arenaM.Season.Get().GetId(),
		opRank, opNewScore, opNewRank, !isWin, false, smsg.ReportId)

	//抛出事件
	var winInt uint32
	if isWin {
		winInt = 1
	}
	args.Caller.FireCommonEvent(srv.EventM(), event.IeArenaFight, 1, winInt, selfNewScore, selfNewRank)
	args.Caller.FireCommonEvent(srv.EventM(), event.IeHotRankUpdate, uint64(selfNewRank), uint32(common.FORMATION_ID_FI_ARENA_ATTACK))
	return true
}

// 更新战斗相关数据
func updateFightData(srv command.Servicer, selfScoreChange, opScoreChange int32, reportID string,
	isWin bool, attacker, defender *cl.MiniUserSnapshot) {
	arenaM := srv.GetActivity(activity.Arena).(*arena.Manager)
	myArenaUser := arenaM.Get(attacker.Id)
	opArenaUser := arenaM.Get(defender.Id)
	if isWin {
		//更新对手的积分和排行
		if opArenaUser != nil {
			arenaM.UpdateScoreAndRank(srv, defender.Id, opScoreChange)
		}

		//更新自己的积分和排行
		arenaM.UpdateScoreAndRank(srv, attacker.Id, selfScoreChange)

		//检查更新段位信息
		score := myArenaUser.GetScore()
		rank := arenaM.Rank.GetRank(attacker.Id)

		myArenaUser.CheckUpdateBestDivision(srv, score, rank)

		now := time.Now().Unix()
		maxDivision := goxml.GetData().ArenaDivisionInfoM.GetMaxDivision()
		// 段位达到第一，推送消息到聊天的系统频道; 这个推送cd时间,在赛季结束时不重置(策划要求)
		if myArenaUser.GetSeasonBestDivision() == maxDivision && myArenaUser.GetPushTime() < now {
			userHead := &cl.ChatUserHead{
				Id:       strconv.FormatUint(attacker.Id, 10),
				BaseId:   attacker.BaseId,
				Name:     attacker.Name,
				Level:    attacker.Level,
				Power:    strconv.FormatUint(uint64(attacker.Power), 10),
				Vip:      attacker.Vip,
				ServerId: strconv.FormatUint(attacker.GetSid(), 10),
			}
			division := strconv.FormatUint(uint64(maxDivision), 10)
			message := character.NewMsg(character.Id(srv), character.Type(goxml.ChatTypeArenaRank),
				character.Head(userHead), character.Params(division))
			character.ChatSendMsgToPlatform(srv, attacker.Id, message)
			myArenaUser.SetPushTime(now + goxml.GetData().ChatConfigInfoM.ArenaSendMsgCD)
		}

		//重置连败次数
		myArenaUser.ResetDefeatCount()
	} else {
		//增加连败次数
		myArenaUser.AddDefeatCount()
	}
	//增加战斗次数
	myArenaUser.AddFightCount()

	now := time.Now().Unix()

	//更新挑战记录 TODO 目前唯一id同战报id，是否需要单独的唯一id?
	myLogM := arenaM.GetLogM().GetOwnerLogs(myArenaUser)
	isBot := opArenaUser == nil
	myLogM.AddLog(srv, arena.NewArenaLog(srv.CreateUserLogID(), reportID, selfScoreChange, isWin, isBot, attacker, defender, now))
	arenaM.GetLogM().SetChange(myLogM)
	if !isBot {
		opLogM := arenaM.GetLogM().GetOwnerLogs(opArenaUser)
		opLogM.AddLog(srv, arena.NewArenaLog(srv.CreateUserLogID(), reportID, opScoreChange, isWin, isBot, attacker, defender, now))
		opArenaUser.SetNewLogTip(true)
		arenaM.GetLogM().SetChange(opLogM)
		arenaM.SetChange(srv, opArenaUser)
	}
}

type C2LArenaLogListCommand struct {
	base.UserCommand
}

func (c *C2LArenaLogListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArenaLogListCommand) Error(msg *cl.L2C_ArenaLogList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaLogList, msg)
	return false
}

func (c *C2LArenaLogListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArenaLogList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArenaLogList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArenaLogList: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArenaLogList{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARENA), c.Srv) {
		l4g.Errorf("user: %d C2L_ArenaLogList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
	myArenaUser := arenaM.Get(c.Msg.UID)
	if myArenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaLogList: no user data", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_NO_USER_DATA))
	}

	//日志红点提示的处理
	if myArenaUser.GetNewLogTip() {
		myArenaUser.SetNewLogTip(false)
	}

	logsM := arenaM.GetLogM().GetOwnerLogs(myArenaUser)
	loaded := logsM.CheckLoaded()
	if loaded {
		logs := logsM.GetAll()
		cLogs := make([]*cl.ArenaLog, 0, len(logs))
		for _, v := range logs {
			cLogs = append(cLogs, v.(*arena.Log).Flush())
		}
		smsg.Logs = cLogs
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaLogList, smsg)
	} else {
		req := &AsyncC2LArenaLogListReq{smsg}
		c.AsyncGetArenaLog(req)
	}
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LArenaLogListReq struct {
	smsg *cl.L2C_ArenaLogList
}

func (ar *AsyncC2LArenaLogListReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LArenaLogList: get data error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaLogList, smsg)
		return false
	}

	arenaM := srv.GetActivity(activity.Arena).(*arena.Manager)
	myArenaUser := arenaM.Get(args.UID)
	if myArenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaLogList: no user data", args.UID)
		smsg.Ret = uint32(cret.RET_ARENA_NO_USER_DATA)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaLogList, smsg)
		return false
	}

	logsM := arenaM.GetLogM().GetOwnerLogs(myArenaUser)
	list := dbData.(map[uint32]*cl.ArenaLog)
	for _, data := range list {
		logsM.LoadFromDB(arena.NewLogFromDB(data))
	}
	logsM.LoadFinish()
	logs := logsM.GetAll()

	cLogs := make([]*cl.ArenaLog, 0, len(logs))
	for _, v := range logs {
		cLogs = append(cLogs, v.(*arena.Log).Flush())
	}
	smsg.Logs = cLogs
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaLogList, smsg)
	return true
}

type C2LArenaLikeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LArenaLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArenaLikeCommand) Error(msg *cl.L2C_ArenaLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaLike, msg)
	return false
}

func (c *C2LArenaLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArenaLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArenaLike Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArenaLike: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArenaLike{
		Ret: uint32(cret.RET_OK),
		Id:  cmsg.Id,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARENA_RANK), c.Srv) {
		l4g.Errorf("user: %d C2L_ArenaLike: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if c.Msg.UID == cmsg.Id {
		l4g.Errorf("user: %d C2L_ArenaLike: can't like yourself", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_TARGET_UID_IS_OWN))
	}

	arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
	rank := arenaM.Rank.GetRank(cmsg.Id)
	if rank == 0 {
		l4g.Errorf("user: %d C2L_ArenaLike: not in rank list. uid:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_ARENA_NOT_IN_RANK_LIST))
	}

	arenaUser := arenaM.Get(cmsg.Id)
	if arenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaLike: no user data. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_ARENA_NO_USER_DATA))
	}

	nRet, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_ARENA_LIKE_COUNT), 1, c.Srv)
	if nRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaLike: daily like num limit", c.Msg.UID)
		return c.Error(smsg, nRet)
	}

	awards, awardID := goxml.GetData().ArenaPraiseInfoM.Generate(c.Srv.Rand(), uint32(common.FUNCID_MODULE_ARENA))
	if awards == nil {
		l4g.Errorf("user: %d C2L_ArenaLike: reward info not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_ARENA_LIKE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaLike: Award failed", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.AddNumByType(uint32(common.PURCHASEID_ARENA_LIKE_COUNT), numSummary)
	arenaUser.AddLiked()
	arenaM.SetChange(c.Srv, arenaUser)

	smsg.AwardId = awardID
	c.User.FireCommonEvent(c.Srv.EventM(), uint32(event.IeArenaLike), uint64(1))
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaLike, smsg)
	c.User.LogArenaLike(c.Srv, cmsg.Id, awardID)
	return c.ResultOK(smsg.Ret)
}

type C2LArenaRankCommand struct {
	base.UserCommand
}

func (c *C2LArenaRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArenaRankCommand) Error(msg *cl.L2C_ArenaRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRank, msg)
	return false
}

func (c *C2LArenaRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArenaRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArenaRank Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArenaRank: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArenaRank{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARENA_RANK), c.Srv) {
		l4g.Errorf("user: %d C2L_ArenaRank: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
	selfInfo := arenaM.Get(c.Msg.UID)
	if selfInfo != nil {
		smsg.Rank = arenaM.Rank.GetRank(c.Msg.UID)
		smsg.Score = selfInfo.GetScore()
		smsg.Liked = selfInfo.GetLiked()
	}

	info := goxml.GetData().RankingInfoM.Index(goxml.ArenaRankId)
	if info == nil {
		l4g.Errorf("user: %d C2L_ArenaRank: rank info not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	rankList := arenaM.Rank.GetRanklist(info.ShowCount)
	if len(rankList) > 0 {
		requestUids := make([]uint64, 0, len(rankList))
		for _, v := range rankList {
			val := v.(*arena.ScoreValue)
			requestUids = append(requestUids, val.Key())
		}
		c.GetLocalUserSnapshots(requestUids, &AsyncC2LArenaRankReq{smsg, requestUids}, 0)
	} else {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRank, smsg)
	}
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LArenaRankReq struct {
	smsg        *cl.L2C_ArenaRank
	requestUids []uint64
}

func (ar *AsyncC2LArenaRankReq) Resp(srv command.Servicer, args *character.Args,
	retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	requestUids := ar.requestUids
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaRank: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRank, smsg)
		return false
	}

	snaps := dbData.([]*cl.UserSnapshot)
	snapm := make(map[uint64]*cl.UserSnapshot, len(snaps))
	for _, v := range snaps {
		snapm[v.Id] = v
	}

	arenaM := srv.GetActivity(activity.Arena).(*arena.Manager)
	for _, v := range requestUids { //nolint:varnamelen
		if snapm[v] == nil {
			l4g.Errorf("user: %d C2L_ArenaRank: no data. id:%d", args.UID, v)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRank, smsg)
			return false
		}

		opInfo := arenaM.Get(v)
		if opInfo == nil {
			l4g.Errorf("user: %d C2L_ArenaRank: no data. id:%d", args.UID, v)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRank, smsg)
			return false
		}

		item := &cl.ArenaRankInfo{
			Score: opInfo.GetScore(),
			Liked: opInfo.GetLiked(),
			User:  snapm[v],
		}
		smsg.List = append(smsg.List, item)
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRank, smsg)
	return true
}

type C2LArenaRecvAwardCommand struct {
	base.UserCommand
}

func (c *C2LArenaRecvAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LArenaRecvAwardCommand) Error(msg *cl.L2C_ArenaRecvAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRecvAward, msg)
	return false
}

func (c *C2LArenaRecvAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ArenaRecvAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ArenaRecvAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ArenaRecvAward: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ArenaRecvAward{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARENA), c.Srv) {
		l4g.Errorf("user: %d C2L_ArenaRecvAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	taskCount := len(cmsg.Ids)
	if taskCount == 0 || taskCount > goxml.GetData().ArenaDivisionTaskInfoM.GetTotalTaskCount() {
		l4g.Errorf("user: %d C2L_ArenaRecvAward: param err, param err, count:%d", c.Msg.UID, taskCount)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
	arenaUser := arenaM.Get(c.Msg.UID)
	if arenaUser == nil {
		l4g.Errorf("user: %d C2L_ArenaRecvAward: no self user data", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ARENA_NO_USER_DATA))
	}
	isResetting := arenaM.IsRestting()

	uniq := make(map[uint32]struct{}, taskCount)
	awards := make([]*cl.Resource, 0, taskCount*2)
	taskUpdateData := make([]*log.ArenaDivisionTaskAward, 0, taskCount)
	for _, id := range cmsg.Ids {
		if _, exist := uniq[id]; exist {
			l4g.Errorf("user: %d C2L_ArenaRecvAward: task id repeat. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[id] = struct{}{}

		info := goxml.GetData().ArenaDivisionTaskInfoM.Index(id)
		if info == nil {
			l4g.Errorf("user: %d C2L_ArenaRecvAward: task info not exist. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		awardType := arenaUser.GetTaskAwardType(id, info.DivisionID, isResetting)
		if awardType == activity.ArenaTaskAwardTypeNone {
			l4g.Errorf("user: %d C2L_ArenaRecvAward: no award can reveive. id:%d isResetting:%v",
				c.Msg.UID, id, isResetting)
			return c.Error(smsg, uint32(cret.RET_ARENA_TASK_NO_AWARD_CAN_RECV))
		}

		taskUpdateData = append(taskUpdateData, &log.ArenaDivisionTaskAward{
			TaskId:    id,
			AwardType: awardType,
		})
		award := c.getAwardsByType(info, awardType)
		awards = append(awards, award...)
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_ARENA_RECV_TASK_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ArenaRecvAward: Award failed", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	for _, data := range taskUpdateData {
		arenaUser.UpdateTaskReceiveStatus(data.TaskId, data.AwardType)
	}
	arenaM.SetChange(c.Srv, arenaUser)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ArenaRecvAward, smsg)
	c.User.LogArenaRecvAward(c.Srv, taskUpdateData)
	return c.ResultOK(smsg.Ret)
}

func (c *C2LArenaRecvAwardCommand) getAwardsByType(info *goxml.ArenaDivisionTaskInfoExt,
	awardType uint32) []*cl.Resource {
	awards := make([]*cl.Resource, 0, len(info.WeeklyAwards)+len(info.FirstAwards))
	if awardType == activity.ArenaTaskAwardTypeAll {
		awards = append(awards, info.WeeklyAwards...)
		awards = append(awards, info.FirstAwards...)
	} else if awardType == activity.ArenaTaskAwardTypeSeason {
		awards = append(awards, info.WeeklyAwards...)
	} else {
		awards = append(awards, info.FirstAwards...)
	}
	return awards
}

func GenerateArenaReward(srv command.Servicer, user *character.User, arenaUser *arena.User, rewardType uint32) ([]*cl.Resource, bool) {
	var reward *goxml.ArenaFightRewardInfo
	guaranteed := goxml.GetData().ArenaFightRewardInfoM.GetRewardGuaranteed(rewardType)
	if guaranteed != nil {
		if rewardType == goxml.ArenaFightWin {
			if arenaUser.GetWinRewardTimes()+1 >= guaranteed.Guaranteed {
				reward = guaranteed
			}
		} else if arenaUser.GetDefeatRewardTimes()+1 >= guaranteed.Guaranteed {
			reward = guaranteed
		}
	}
	if reward != nil {
		if resources, dropFlag := user.Drop().DoDrop(srv.Rand(), reward.Reward); dropFlag {
			return resources, true
		}
		l4g.Errorf("user: %d GenerateArenaReward DoDrop award error. dropGroup:%d",
			user.GetDBUser().Id, reward.Reward)
		return nil, true
	}

	info := goxml.GetData().ArenaFightRewardInfoM.Group(rewardType)
	totalWeight := goxml.GetData().ArenaFightRewardInfoM.Weight(rewardType)

	rNum := uint32(srv.Rand().RandBetween(1, totalWeight))
	for _, v := range info {
		if rNum <= v.Weight {
			if resources, dropFlag := user.Drop().DoDrop(srv.Rand(), v.Reward); dropFlag {
				return resources, v.Guaranteed != 0
			}
			l4g.Errorf("user: %d GenerateArenaReward DoDrop award error. dropGroup:%d",
				user.GetDBUser().Id, reward.Reward)
			return nil, false
		} else {
			rNum -= v.Weight
		}
	}

	l4g.Errorf("ArenaFightRewardInfoM. Generate fight reward failed")
	return nil, false
}
