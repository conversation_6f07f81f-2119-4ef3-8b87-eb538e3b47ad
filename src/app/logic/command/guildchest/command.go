package guildchest

import (
	"app/goxml"
	"app/logic/activity"
	aguild "app/logic/activity/guild"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/time"

	// "app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildChestGetData), &C2LGuildChestGetDataCommand{}, state)   // 获取数据 NOLOG
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildChestRecv), &C2LGuildChestRecvCommand{}, state)         // 领取公会分享
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildChestLike), &C2LGuildChestLikeCommand{}, state)         // 点赞公户分享
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildChestActivate), &C2LGuildChestActivateCommand{}, state) // 激活公会分享
}

type C2LGuildChestGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildChestGetDataCommand) Error(msg *cl.L2C_GuildChestGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestGetData, msg)
	return false
}

func (c *C2LGuildChestGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildChestGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildChestGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GuildChestGetData{
		Ret: uint32(cret.RET_OK),
	}

	chestItem := c.User.UserGuildChestItem()
	clientItem, _ := chestItem.Flush2Client()
	smsg.Items = clientItem
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestGetData failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildChestGetData not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestGetData guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildChestGetData: cross can't connect.", c.Msg.UID)
		smsg.RecvTotalChestLikeToken = guildUser.GetTotalFlowerToken()
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildChestGetData, c.User.ID(), &l2c.L2C_GuildChestGetData{}) {
		l4g.Errorf("user: %d C2L_GuildChestGetData cross maintain", c.User.ID())
		smsg.RecvTotalChestLikeToken = guildUser.GetTotalFlowerToken()
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildChestRecvCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestRecvCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildChestRecvCommand) Error(msg *cl.L2C_GuildChestRecv, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestRecv, msg)
	return false
}

func (c *C2LGuildChestRecvCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildChestRecv{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestRecv Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildChestRecv: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GuildChestRecv{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_CHEST), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildChestRecv: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestRecv failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildChestRecv not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestRecv guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildChestRecv: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildChestRecv, c.User.ID(), &l2c.L2C_GuildChestRecv{
		Id:        cmsg.Id,
		Level:     c.User.Level(),
		Avatar:    c.User.BaseID(),
		Name:      c.User.Name(),
		RecvCount: guildUser.GetChestWeeklyRecvCount(),
	}) {
		l4g.Errorf("user: %d C2L_GuildChestRecv cross maintain", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LGuildChestLikeCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildChestLikeCommand) Error(msg *cl.L2C_GuildChestLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestLike, msg)
	return false
}

func (c *C2LGuildChestLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildChestLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestLike Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildChestLike: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GuildChestLike{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_CHEST), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildChestLike: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestLike failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildChestLike not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestLike guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildChestLike: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if cmsg.LikeType != uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_NORMAL) && cmsg.LikeType != uint32(common.GUILD_CHEST_LIKE_TYPE_GCLT_SPECIFICAL) {
		l4g.Errorf("user: %d C2L_GuildChestLike: like type:%d is error", c.Msg.UID, cmsg.LikeType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	cost := goxml.GetData().GuildConfigInfoM.GetSetLikeReduceRes(cmsg.LikeType)
	costs := []*cl.Resource{cost}
	ret, _ := c.User.CheckResourcesSize(costs)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildChestLike: resource check failed code:%d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildChestLike, c.User.ID(), &l2c.L2C_GuildChestLike{
		Id:       cmsg.Id,
		LikeType: cmsg.LikeType,
	}) {
		l4g.Errorf("user: %d C2L_GuildChestLike cross maintain", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GUILD_CHEST_SEND_FLOWER), 0)
	return c.ResultOK(smsg.Ret)
}

type C2LGuildChestActivateCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestActivateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildChestActivateCommand) Error(msg *cl.L2C_GuildChestActivate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestActivate, msg)
	return false
}

func (c *C2LGuildChestActivateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildChestActivate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestActivate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildChestActivate: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GuildChestActivate{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_CHEST), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildChestActivate: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestActivate failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildChestActivate not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestActivate guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildChestActivate: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}
	now := time.Now().Unix()
	item, canUse := c.User.UserGuildChestItem().CanUse(cmsg.ItemId, now)
	if !canUse || item == nil {
		l4g.Errorf("user: %d C2L_GuildChestActivate: item:%d can`t be ues", c.Msg.UID, cmsg.ItemId)
		return c.Error(smsg, uint32(cret.RET_GUILD_CHEST_ITEM_IS_EXPIRE))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildChestCanActivate, c.User.ID(), &l2c.L2C_GuildChestCanActivate{
		Id:          item.Id,
		ChestId:     item.ChestId,
		ExpiredTime: item.ExpireTime,
		TaskId:      item.TaskId,
	}) {
		l4g.Errorf("user: %d C2L_GuildChestActivate cross maintain", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}
