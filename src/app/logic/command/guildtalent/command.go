package guildtalent

import (
	"app/goxml"
	"app/logic/activity"
	aguild "app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command"
	"app/logic/event"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildTalentList), &C2LGuildTalentListCommand{}, state)       // 公会天赋列表
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildTalentLevelUp), &C2LGuildTalentLevelUpCommand{}, state) // 公会天赋升级
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildTalentReset), &C2LGuildTalentResetCommand{}, state)     // 公会天赋重置
}

type C2LGuildTalentListCommand struct {
	base.UserCommand
}

func (c *C2LGuildTalentListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildTalentListCommand) Error(msg *cl.L2C_GuildTalentList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildTalentList, msg)
	return false
}

func (c *C2LGuildTalentListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildTalentList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d C2L_GuildTalentList Unmarshal error.  err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildTalentList: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildTalentList{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_TALENT), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildTalentList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 检查user是否在公会中
	if !IsExistGuild(c.Srv, c.Msg.UID) {
		l4g.Errorf("user: %d C2L_GuildTalentList: user not in guild. ", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	smsg.List = c.User.GuildTalent().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildTalentList, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LGuildTalentLevelUpCommand struct {
	base.UserCommand
}

func (c *C2LGuildTalentLevelUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildTalentLevelUpCommand) Error(msg *cl.L2C_GuildTalentLevelUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildTalentLevelUp, msg)
	return false
}

func (c *C2LGuildTalentLevelUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildTalentLevelUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d C2L_GuildTalentLevelUp Unmarshal error. err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildTalentLevelUp: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildTalentLevelUp{
		Ret:  uint32(cret.RET_OK),
		Kind: cmsg.Kind,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_TALENT), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildTalentLevelUp: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !IsExistGuild(c.Srv, c.Msg.UID) {
		l4g.Errorf("user: %d C2L_GuildTalentLevelUp: user not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	talentM := c.User.GuildTalent()
	curLevel := talentM.GetLevel(cmsg.Kind)
	talentInfo := goxml.GetData().GuildTalentInfoM.Index(cmsg.Kind, curLevel)
	if talentInfo == nil {
		l4g.Errorf("user: %d C2L_GuildTalentLevelUp: talInfo is nil. kind:%d level:%d", c.Msg.UID, cmsg.Kind, curLevel)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	floorI := talentM.GetGuildTalentFloor(talentInfo.Floor)
	if ret := floorI.CheckLevelUpLimit(cmsg.Kind, curLevel, cmsg.AddLevel, talentInfo, talentM); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user : %d C2L_GuildTalentLevelUp: kind levelUp failed. kind:%d ret:%d", c.Msg.UID, cmsg.Kind, ret)
		return c.Error(smsg, ret)
	}

	costs := floorI.GetCosts(cmsg.Kind, curLevel, cmsg.AddLevel, talentInfo)
	smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GUILD_TALENT_LEVEL_UP), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildTalentLevelUp: consume resource failed. kind: %d, retCode: %d", c.Msg.UID, cmsg.Kind, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	if talentInfo.Floor == goxml.GuildTalentFloorSecond {
		awards := goxml.GetData().GuildTalentInfoM.GetAwards(cmsg.Kind, curLevel+cmsg.AddLevel)
		if len(awards) > 0 {
			smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GUILD_TALENT_LEVEL_UP), 0)
			if smsg.Ret != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2L_GuildTalentLevelUp: send award resource failed. kind: %d, retCode: %d", c.Msg.UID, cmsg.Kind, smsg.Ret)
				return c.Error(smsg, smsg.Ret)
			}
		}
	}

	floorI.LevelUp(cmsg.Kind, cmsg.AddLevel, talentM)
	talentM.Save()
	smsg.Talent = talentM.Flush()
	c.User.HeroManager().SetHeroAttrChange()
	c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
	c.User.LogGuildTalentLevelUp(c.Srv, cmsg.Kind, curLevel, talentM.GetLevel(cmsg.Kind), talentM.GetScore())
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildTalentLevelUp, smsg)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGuildTalentLevelUp, uint64(curLevel+1), talentInfo.Floor, cmsg.Kind)

	return c.ResultOK(smsg.Ret)
}

type C2LGuildTalentResetCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LGuildTalentResetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildTalentResetCommand) Error(msg *cl.L2C_GuildTalentReset, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildTalentReset, msg)
	return false
}

func (c *C2LGuildTalentResetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildTalentReset{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d C2L_GuildTalentReset Unmarshal error. err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GuildTalentReset: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildTalentReset{
		Ret:  uint32(cret.RET_OK),
		Kind: cmsg.Kind,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_TALENT), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildTalentReset: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	// 检查user是否在公会中
	if !IsExistGuild(c.Srv, c.Msg.UID) {
		l4g.Errorf("user: %d C2L_GuildTalentReset: user not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	talentM := c.User.GuildTalent()
	curLevel := talentM.GetLevel(cmsg.Kind)
	if curLevel == 0 {
		l4g.Errorf("user: %d C2L_GuildTalentReset: level is zero. kind:%d", c.Msg.UID, cmsg.Kind)
		return c.Error(smsg, uint32(cret.RET_GUILD_TALENT_LEVEL_INVALID_NOT_RESET))
	}

	talentInfo := goxml.GetData().GuildTalentInfoM.Index(cmsg.Kind, curLevel)
	if talentInfo == nil {
		l4g.Errorf("user: %d C2L_GuildTalentReset: talInfo is nil. kind:%d level:%d", c.Msg.UID, cmsg.Kind, curLevel)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	if talentInfo.Floor != goxml.GuildTalentFloorSecond {
		l4g.Errorf("user: %d C2L_GuildTalentReset: floor is invalid. kind:%d floor:%d", c.Msg.UID, cmsg.Kind, talentInfo.Floor)
		return c.Error(smsg, uint32(cret.RET_GUILD_TALENT_FLOOR_NOT_RESET))
	}

	purchaseID := getPurchaseID(cmsg.Kind)
	if purchaseID == common.PURCHASEID(0) {
		l4g.Errorf("user: %d C2L_GuildTalentReset: purchaseID is invalid. kind:%d purchaseID:%d", c.Msg.UID, cmsg.Kind, purchaseID)
		return c.Error(smsg, uint32(cret.RET_GUILD_TALENT_PURCHASE_ID_INVALID))
	}

	ret, costs, newNumInfo := c.User.CheckNumByType(uint32(purchaseID), 1, c.Srv)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildTalentReset: reset talent failed. count not enough", c.Msg.UID)
		return c.Error(smsg, ret)
	}
	retRes := goxml.GetData().GuildTalentInfoM.GetResetRes(cmsg.Kind, curLevel)
	retCode, newAwards := c.User.Trade(c.Srv, costs, retRes, uint32(log.RESOURCE_CHANGE_REASON_GUILD_TALENT_RESET), 0)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildTalentReset: trade error. retCode: %d", c.Msg.UID, retCode)
		return c.Error(smsg, retCode)
	}
	c.User.AddNumByType(uint32(purchaseID), newNumInfo)

	oldTalent := talentM.Flush()
	talentM.Reset(cmsg.Kind, c.Srv)
	talentM.Save()
	c.User.HeroManager().SetHeroAttrChange()
	c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)

	smsg.Res = newAwards
	smsg.Talent = talentM.Flush()
	c.User.LogGuildTalentReset(c.Srv, oldTalent, talentM.GetTalent())
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildTalentReset, smsg)

	return c.ResultOK(smsg.Ret)
}

func IsExistGuild(srv command.Servicer, userID uint64) bool {
	guildM, ok := srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		return false
	}

	if guildM.GetGuildByUser(userID) == nil {
		return false
	}

	return true
}

func getPurchaseID(kind uint32) common.PURCHASEID {
	switch kind {
	case goxml.GuildTalentSecondFloorFirst:
		return common.PURCHASEID_GUILD_TALENT_FIRST_RESET_COUNT
	case goxml.GuildTalentSecondFloorSecond:
		return common.PURCHASEID_GUILD_TALENT_SECOND_RESET_COUNT
	case goxml.GuildTalentSecondFloorThird:
		return common.PURCHASEID_GUILD_TALENT_THIRD_RESET_COUNT
	case goxml.GuildTalentSecondFloorFourth:
		return common.PURCHASEID_GUILD_TALENT_FOURTH_RESET_COUNT
	case goxml.GuildTalentSecondFloorFifth:
		return common.PURCHASEID_GUILD_TALENT_FIFTH_RESET_COUNT
	case goxml.GuildTalentSecondFloorSixth:
		return common.PURCHASEID_GUILD_TALENT_SIXTH_RESET_COUNT
	}

	return common.PURCHASEID(0)
}
