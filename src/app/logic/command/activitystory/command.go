package activitystory

import (
	"app/goxml"
	"app/logic/character"
	aevent "app/logic/event"
	"app/logic/helper"
	"app/logic/rank"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/time"

	"app/logic/command/base"
	"app/protos/out/cl"

	// "app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityStoryGetData), &C2LActivityStoryGetDataCommand{}, state)       //获取信息
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityStoryLoginAward), &C2LActivityStoryLoginAwardCommand{}, state) //累登奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityStoryExchange), &C2LActivityStoryExchangeCommand{}, state)     //兑换
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityStoryFight), &C2LActivityStoryFightCommand{}, state)           //活动主线战斗
}

type C2LActivityStoryGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityStoryGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityStoryGetDataCommand) Error(msg *cl.L2C_ActivityStoryGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryGetData, msg)
	return false
}

func (c *C2LActivityStoryGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityStoryGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityStoryGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityStoryGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityStoryGetData{
		Ret: uint32(cret.RET_OK),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_STORY), c.Srv) {
	//	l4g.Errorf("user: %d C2L_ActivityStoryGetData: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}
	now := time.Now().Unix()
	activityStory := c.User.ActivityStory()
	info, needReset := activityStory.NeedReset(c.Srv)
	if needReset {
		refreshTime := helper.GetResetTime(uint32(common.RESET_TYPE_DAILY), now)
		activityStory.Reset(c.Srv, info, int64(refreshTime))
	}
	_, isOpen := activityStory.IsOpen(c.Srv, now)
	if !isOpen {
		l4g.Errorf("user: %d C2L_ActivityStoryGetData: activity is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_NOT_OPEN))
	}
	smsg.Data = c.User.ActivityStory().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LActivityStoryLoginAwardCommand struct {
	base.UserCommand
}

func (c *C2LActivityStoryLoginAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityStoryLoginAwardCommand) Error(msg *cl.L2C_ActivityStoryLoginAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryLoginAward, msg)
	return false
}

func (c *C2LActivityStoryLoginAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityStoryLoginAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityStoryLoginAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityStoryLoginAward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityStoryLoginAward{
		Ret: uint32(cret.RET_OK),
	}

	now := time.Now().Unix()
	activityStory := c.User.ActivityStory()
	activityStoryInfo, isOpen := activityStory.IsOpen(c.Srv, now)
	if !isOpen {
		l4g.Errorf("user: %d C2L_ActivityStoryLoginAward: activity is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_NOT_OPEN))
	}

	sysId := activityStory.GetSysId()
	if sysId != cmsg.ActId {
		l4g.Errorf("user: %d C2L_ActivityStoryLoginAward: activity is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_NEED_GET_DATA_BEFORE))
	}

	if !c.User.IsFunctionOpen(uint32(activityStoryInfo.FunctionID), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityStoryLoginAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	cLens := len(cmsg.LoginIds)
	if cLens == 0 || cLens > len(goxml.GetData().ActivityStoryLoginInfoM.GetRound(activityStoryInfo.LoginID)) {
		l4g.Errorf("user: %d C2L_ActivityStoryLoginAward client tasks:%v lens is error", c.Msg.UID, cmsg.LoginIds)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	repeatedM := make(map[uint32]struct{})
	totalReward := make([]*cl.Resource, 0, cLens*2)
	for _, loginID := range cmsg.LoginIds {
		_, exist := repeatedM[loginID]
		if exist {
			l4g.Errorf("user: %d C2L_ActivityStoryLoginAward client task:%d is repeated", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedM[loginID] = struct{}{}

		LoginInfo := goxml.GetData().ActivityStoryLoginInfoM.Index(loginID)
		if LoginInfo == nil {
			l4g.Errorf("user: %d C2L_ActivityStoryLoginAward client task:%d get data failed", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if activityStoryInfo.LoginID != LoginInfo.LoginId {
			l4g.Errorf("user: %d C2L_ActivityStoryLoginAward actId:%d login:%d is not same with client LoginInfo id:%d round:%d",
				c.Msg.UID, activityStoryInfo.ID, activityStoryInfo.LoginID, LoginInfo.Id, LoginInfo.LoginId)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_LOGIN_AWARD_CONDITION_FAILED))
		}

		if !activityStory.CheckLoginReward(loginID) {
			l4g.Errorf("user: %d C2L_ActivityStoryLoginAward client task:%d has recv", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}

		if !activityStory.CanReceive(LoginInfo.Days) {
			l4g.Errorf("user: %d C2L_ActivityStoryLoginAward task:%d check condition failed", c.Msg.UID, loginID)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_LOGIN_AWARD_CONDITION_FAILED))
		}

		totalReward = append(totalReward, LoginInfo.RewardClRes...)
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalReward, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_LOGIN_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivityStoryLoginAward: send login award failed. errorCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	activityStory.SetLoginReward(cmsg.LoginIds)
	smsg.ActId = cmsg.ActId
	smsg.LoginIds = cmsg.LoginIds
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryLoginAward, smsg)
	c.User.LogActivityStoryLoginAward(c.Srv, cmsg.ActId, cmsg.LoginIds)
	return c.ResultOK(smsg.Ret)
}

type C2LActivityStoryExchangeCommand struct {
	base.UserCommand
}

func (c *C2LActivityStoryExchangeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityStoryExchangeCommand) Error(msg *cl.L2C_ActivityStoryExchange, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryExchange, msg)
	return false
}

func (c *C2LActivityStoryExchangeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityStoryExchange{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityStoryExchange Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityStoryExchange: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityStoryExchange{
		Ret: uint32(cret.RET_OK),
	}

	now := time.Now().Unix()
	activityStory := c.User.ActivityStory()
	activityStoryInfo, isOpen := activityStory.IsOpen(c.Srv, now)
	if !isOpen {
		l4g.Errorf("user: %d C2L_ActivityStoryLoginAward: activity is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_NOT_OPEN))
	}

	sysId := activityStory.GetSysId()
	if sysId != cmsg.ActId {
		l4g.Errorf("user: %d C2L_ActivityStoryExchange: activity is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_NEED_GET_DATA_BEFORE))
	}

	if !c.User.IsFunctionOpen(uint32(activityStoryInfo.FunctionID), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityStoryExchange: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	shopInfo := goxml.GetData().ActivityStoryShopInfoM.Index(cmsg.Id)
	if shopInfo == nil {
		l4g.Errorf("user: %d C2L_ActivityStoryExchange: cmsg id:%d is error", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if shopInfo.ShopId != activityStoryInfo.ShopID {
		l4g.Errorf("user: %d C2L_ActivityStoryExchange: check shop id:%d failed", c.Msg.UID, shopInfo.ShopId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if shopInfo.UnlockDungeon > 0 && activityStory.GetDungeon() < shopInfo.UnlockDungeon {
		l4g.Errorf("user:%d C2L_ActivityStoryExchange Dungeon check failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_BUY_CONDITION_CHECK_FAILED))
	}

	if shopInfo.UnlockDay > 0 && activityStory.ActivityBetweenOpen(now, activityStoryInfo) < shopInfo.UnlockDay {
		l4g.Errorf("user:%d C2L_ActivityStoryExchange open day check failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_BUY_CONDITION_CHECK_FAILED))
	}

	exchange := activityStory.GetExchange(cmsg.Id)
	if exchange == nil {
		exchange = &cl.ActivityStoryExchange{
			Id: cmsg.Id,
		}
	}

	if exchange.Count+cmsg.Count > shopInfo.BuyLimitCount {
		l4g.Errorf("user:%d C2L_ActivityStoryExchange user buy count:%d buy limit count:%d", c.Msg.UID, exchange.Count+cmsg.Count, shopInfo.BuyLimitCount)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_BUY_CONDITION_CHECK_FAILED))
	}

	costs := shopInfo.GetBuyCost(goxml.GetData(), exchange.Count, cmsg.Count)
	awards := makeRes(shopInfo.RewardClRes, cmsg.Count)

	smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, awards,
		uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_EXCHANGE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DropActivityExchange: trade failed. ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	exchange.Count += cmsg.Count
	activityStory.SetExchange(exchange)

	smsg.Id = cmsg.Id
	smsg.Count = exchange.Count
	smsg.ActId = cmsg.ActId
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryExchange, smsg)
	c.User.LogActivityStoryExchangeAward(c.Srv, cmsg.Id, cmsg.ActId, cmsg.Count)
	return c.ResultOK(smsg.Ret)
}

func makeRes(baseRes []*cl.Resource, count uint32) []*cl.Resource {
	ret := make([]*cl.Resource, 0, len(baseRes))
	for _, v := range baseRes {
		ret = append(ret, &cl.Resource{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count * count,
		})
	}
	return ret
}

type C2LActivityStoryFightCommand struct {
	base.UserCommand
}

func (c *C2LActivityStoryFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityStoryFightCommand) Error(msg *cl.L2C_ActivityStoryFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryFight, msg)
	return false
}

//nolint:funlen
func (c *C2LActivityStoryFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityStoryFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityStoryFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityStoryFight: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityStoryFight{
		Ret: uint32(cret.RET_OK),
	}

	now := time.Now().Unix()
	activityStory := c.User.ActivityStory()
	activityStoryInfo, isOpen := activityStory.IsOpen(c.Srv, now)
	if !isOpen {
		l4g.Errorf("user: %d C2L_ActivityStoryLoginAward: activity is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_NOT_OPEN))
	}

	sysId := activityStory.GetSysId()
	if sysId != cmsg.ActId {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: activity is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_NEED_GET_DATA_BEFORE))
	}

	if !c.User.IsFunctionOpen(uint32(activityStoryInfo.FunctionID), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	dungeonInfo := goxml.GetData().ActivityStoryDungeonInfoM.Index(cmsg.DungeonId)
	if dungeonInfo == nil {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: client dungeon Id:%d is error", c.Msg.UID, cmsg.DungeonId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if activityStoryInfo.DungeonID != dungeonInfo.DungeonId {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: client dungeon ID:%d not same with act:%d dungeonID:%d",
			c.Msg.UID, dungeonInfo.DungeonId, cmsg.ActId, activityStoryInfo.DungeonID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//防刷验证
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIActivityStory) {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: operate too frequently", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_OPERATE_TOO_OFTEN))
	}

	ret, raisePS := activityStory.AltRaisePS(c.User, activityStoryInfo.DungeonID, dungeonInfo)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: get raisePS failed. ret:%d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	reduceCount := uint32(1)
	if cmsg.Type == uint32(common.ACTIVITY_STORY_FIGHT_TYPE_ASFT_SWEEP) {
		if cmsg.SweepCount == 0 {
			l4g.Errorf("user: %d C2L_ActivityStoryFight: sweep count is 0.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		} else {
			reduceCount = cmsg.SweepCount
		}
	}

	ret, costs := c.User.CheckResourcesSize(dungeonInfo.GetCostResource(reduceCount))
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: ticket check is failed err code:%d", c.Msg.UID, ret)
		return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_FIGHT_CHECK_FAILED))
	}

	if dungeonInfo.UnlockDay > 0 {
		betweenDay := helper.DaysBetweenTimes(activityStoryInfo.OpenTime, now)
		if betweenDay < dungeonInfo.UnlockDay {
			l4g.Errorf("user: %d C2L_ActivityStoryFight: dungeon open day:%d check now day:%d failed", c.User.ID(), dungeonInfo.UnlockDay, betweenDay)
			return c.Error(smsg, uint32(cret.RET_ACTIVITY_STORY_FIGHT_CHECK_FAILED))
		}
	}

	win := true
	if cmsg.Type == uint32(common.ACTIVITY_STORY_FIGHT_TYPE_ASFT_FIGHT) {
		if dungeonInfo.PrevDungeon != activityStory.GetDungeon() {
			l4g.Errorf("user: %d C2L_ActivityStoryFight: pre dungeon:%d cmsg dungeon:%d check failed", c.Msg.UID, dungeonInfo.PrevDungeon, cmsg.DungeonId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		battleReport, retCode := c.User.AttackActivityStory(c.Srv, dungeonInfo, raisePS, cmsg.ClientData)
		if retCode != cret.RET_OK {
			l4g.Errorf("user: %d C2L_ActivityStoryFight: battle error. ret:%d", c.Msg.UID, retCode)
			return c.Error(smsg, uint32(cret.RET_BATTLE_ERROR))
		}
		smsg.ReportId = battleReport.GetId()
		win = battleReport.GetWin()
		if battleReport.GetWin() {
			if len(dungeonInfo.PassClRes) > 0 {
				smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, dungeonInfo.PassClRes, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_FIGHT), 0)
				if smsg.Ret != uint32(cret.RET_OK) {
					l4g.Errorf("user:%d C2L_ActivityStoryFight fight awards:%+v retCode:%d", c.User.ID(), dungeonInfo.PassClRes, smsg.Ret)
					return c.Error(smsg, smsg.Ret)
				}
			}
			activityStory.SetDungeon(cmsg.DungeonId)
			commonRank := c.Srv.CommonRankM().GetCommonRank(activityStoryInfo.RankID)
			if commonRank != nil {
				c.Srv.CommonRankM().Insert(
					activityStoryInfo.RankID, rank.NewRankActivityStory(c.User.ID(), sysId, cmsg.DungeonId, time.Now().Unix(), uint64(c.User.CalFormationPower(dungeonInfo.Formation))))
			}
			c.User.FireCommonEvent(c.Srv.EventM(), aevent.IeActivityStoryDungeon, uint64(cmsg.DungeonId), activityStory.GetSysId())
		}
	} else if cmsg.Type == uint32(common.ACTIVITY_STORY_FIGHT_TYPE_ASFT_SWEEP) {
		if cmsg.DungeonId > activityStory.GetDungeon() {
			l4g.Errorf("user: %d C2L_ActivityStoryFight: sweep dungeon:%d pass dungeon:%d check failed", c.Msg.UID, cmsg.DungeonId, activityStory.GetDungeon())
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		award := dungeonInfo.GetQuickAward(reduceCount)
		if len(award) > 0 {
			smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, award, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_STORY_FIGHT), 0)
			if smsg.Ret != uint32(cret.RET_OK) {
				l4g.Errorf("user:%d C2L_ActivityStoryFight sweep awards:%+v retCode:%d", c.User.ID(), award, smsg.Ret)
				return c.Error(smsg, smsg.Ret)
			}
		}
		c.User.FireCommonEvent(c.Srv.EventM(), aevent.IeActivityStorySweep, uint64(reduceCount))
	} else {
		l4g.Errorf("user: %d C2L_ActivityStoryFight: cmsg type:%d is error", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	smsg.Type = cmsg.Type
	smsg.DungeonId = cmsg.DungeonId
	smsg.ActId = cmsg.ActId
	smsg.Win = win
	smsg.SweepCount = cmsg.SweepCount

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityStoryFight, smsg)
	c.User.LogActivityStoryFight(c.Srv, cmsg.ActId, cmsg.DungeonId, cmsg.Type, win, dungeonInfo.Formation, smsg.ReportId, cmsg.SweepCount)
	return c.ResultOK(smsg.Ret)
}
