package friend

import (
	"app/protos/in/l2c"
	"context"
	"slices"
	"sort"
	"unicode/utf8"

	"app/goxml"
	"app/logic/activity"
	afriend "app/logic/activity/friend"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	aevent "app/logic/event"
	"app/logic/rank"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/skiplist"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendInfo), &C2LFriendInfoCommand{}, state)                 //获取好友信息
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendRequestInfo), &C2LFriendRequestInfoCommand{}, state)   //好友申请信息
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendAdd), &C2LFriendAddCommand{}, state)                   //添加好友请求
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendConfirm), &C2LFriendConfirmCommand{}, state)           //请求确认
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendDelete), &C2LFriendDeleteCommand{}, state)             //删除好友
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendBlacklist), &C2LFriendBlacklistCommand{}, state)       //拉黑
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendRemBlacklist), &C2LFriendRemBlacklistCommand{}, state) //移除黑名单
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendSendLike), &C2LFriendSendLikeCommand{}, state)         //点赞
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendRecvLike), &C2LFriendRecvLikeCommand{}, state)         //接受点赞
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendRecommend), &C2LFriendRecommendCommand{}, state)       //推荐好友
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendSearch), &C2LFriendSearchCommand{}, state)             //查询玩家
	cmds.Register(uint32(cl.ID_MSG_C2L_FriendGetBlacklist), &C2LFriendGetBlacklistCommand{}, state) //获取拉黑列表
}

type C2LFriendInfoCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LFriendInfoCommand) Error(msg *cl.L2C_FriendInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendInfo, msg)
	return false
}

func (c *C2LFriendInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user:%d C2L_FriendInfo: Unmarshal error. err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendInfo: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendInfo{
		Ret:    uint32(cret.RET_OK),
		RcvCnt: c.User.DailyInfo().FriendRcvedCnt,
		SndCnt: c.User.DailyInfo().FriendSndCnt,
	}

	fl := c.Srv.GetActivity(activity.Friend).(*afriend.Manager).Get(c.Srv, c.Msg.UID, true)
	friendMsgs, users, blackIDs := fl.FlushFriends()
	smsg.Friends = friendMsgs
	smsg.BlackList = blackIDs
	smsg.DailyZero = fl.DailyZero()
	if len(users) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendInfo, smsg)
		return true
	}
	fNum := fl.GetFriendNum()
	if fNum > 0 {
		c.User.FireCommonEvent(c.Srv.EventM(), aevent.AeFriendCount, uint64(fNum))
	}
	c.GetFriendsUserSnapshotFromAll(users, &AsyncC2LFriendInfoReq{smsg}, 0)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendInfoReq struct {
	smsg *cl.L2C_FriendInfo
}

func (ar *AsyncC2LFriendInfoReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	rsps := dbData.([]*l2c.FriendInfoRsp)
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LFriendInfo: get snapshot error. retCode:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_GET_USER_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendInfo, smsg)
		return false
	}
	snapm := make(map[uint64]*cl.UserSnapshot)
	fm := srv.GetActivity(activity.Friend).(*afriend.Manager)
	for _, rsp := range rsps {
		for _, v := range rsp.Users {
			snapm[v.Id] = v
		}
		for _, id := range rsp.DelIds {
			fm.Delete(srv, args.Caller.ID(), id)
		}
	}
	friends := make([]*cl.Friend, 0, len(smsg.Friends))
	for _, v := range smsg.Friends {
		if snapm[v.Id] == nil {
			l4g.Errorf("user: %d C2LFriendInfo: get snapshot nil. id:%d",
				args.UID, v.Id)
			// smsg.Ret = uint32(cret.RET_GET_USER_DATA_ID_NOT_EXIST)
			// args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendInfo, smsg)
			// return false
			continue
		}
		v.User = snapm[v.Id]
		friends = append(friends, v)
	}
	smsg.Friends = friends

	l4g.Debugf("user: %d C2LFriendInfoCommand: smsg:%s", args.UID, smsg)
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendInfo, smsg)
	return true
}

type C2LFriendRequestInfoCommand struct {
	base.UserCommand
}

func (c *C2LFriendRequestInfoCommand) Error(msg *cl.L2C_FriendRequestInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRequestInfo, msg)
	return false
}

func (c *C2LFriendRequestInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendRequestInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendRequestInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user:%d C2L_FriendRequestInfo: Unmarshal error. error:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendRequestInfo: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendRequestInfo{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendRequestInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	fl := c.Srv.GetActivity(activity.Friend).(*afriend.Manager).Get(c.Srv, c.Msg.UID, true)
	l4g.Debugf("user: %d C2L_FriendRequestInfo data: %+v", c.Msg.UID, fl.Data())
	if len(fl.Data().Requests) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRequestInfo, smsg)
		return true
	}
	serverUsers := make([]*cl.ServerUser, 0, len(fl.Data().Requests))
	smsg.Requests = make([]*cl.FriendRequest, 0, len(fl.Data().Requests))

	for _, v := range fl.Data().Requests {
		serverUsers = append(serverUsers, &cl.ServerUser{Uid: v.Id, Sid: v.ServerId})
		smsg.Requests = append(smsg.Requests, &cl.FriendRequest{Tm: v.Tm, Id: v.Id})
	}
	c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LFriendRequestInfoReq{smsg}, 0)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendRequestInfoReq struct {
	smsg *cl.L2C_FriendRequestInfo
}

func (ar *AsyncC2LFriendRequestInfoReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps := dbData.([]*cl.UserSnapshot)
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LFriendRequestInfoCommand: get snapshot error. retCode:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_GET_USER_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendRequestInfo, smsg)
		return false
	}
	snapm := make(map[uint64]*cl.UserSnapshot, len(snaps))
	for _, v := range snaps {
		snapm[v.Id] = v
	}
	for _, v := range smsg.Requests {
		if snapm[v.Id] == nil {
			l4g.Errorf("user: %d C2LFriendRequestInfoCommand: get snapshot nil. id:%d",
				args.UID, v.Id)
			smsg.Ret = uint32(cret.RET_GET_USER_DATA_ID_NOT_EXIST)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendRequestInfo, smsg)
			return false
		}
		v.User = snapm[v.Id]
	}
	l4g.Debugf("user: %s,%d C2LFriendRequestInfoCommand: %s", args.Caller.Name(), args.UID, smsg)
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendRequestInfo, smsg)
	return true
}

type C2LFriendAddCommand struct {
	base.UserCommand
}

func (c *C2LFriendAddCommand) Error(msg *cl.L2C_FriendAdd, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendAdd, msg)
	return false
}

func (c *C2LFriendAddCommand) ResultOK(retCode uint32) bool { return retCode == uint32(cret.RET_OK) }

func (c *C2LFriendAddCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendAdd{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user:%d C2L_FriendAdd: Unmarshal error. error:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendAdd: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendAdd{
		Ret:   uint32(cret.RET_OK),
		Users: cmsg.Users,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendAdd: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if len(cmsg.Users) > int(goxml.GetData().ConfigInfoM.FriendRecommendNum) {
		l4g.Errorf("user:%d C2L_FriendAdd: id too long. cmsg:%v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	if len(cmsg.Users) == 0 {
		l4g.Errorf("user:%d C2L_FriendAdd: valid id. cmsg:%v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	users := make([]*cl.ServerUser, 0, len(cmsg.Users))
	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	for _, user := range cmsg.Users {
		if !slices.Contains(c.Srv.GetNormalPartSids(), user.Sid) {
			l4g.Errorf("user:%d C2L_FriendAdd: sid not in a same area. cmsg:%v", c.Msg.UID, cmsg)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if ret := fm.CheckAddBySelf(c.User, user.Uid, false); ret == uint32(cret.RET_OK) {
			users = append(users, user)
		} else if ret == uint32(cret.RET_FRIEND_REPEATED_REQUEST) {
			//重复申请认为是成功的
			users = append(users, user)
		} else if len(cmsg.Users) == 1 {
			l4g.Errorf("user:%d C2L_FriendAdd: checkAddBySelf error. cmsg:%v retCode:%d", c.Msg.UID, cmsg, ret)
			return c.Error(smsg, ret)
		}
	}
	if len(users) == 0 {
		l4g.Errorf("user:%d C2L_FriendAdd: no valid id. cmsg:%v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	c.FriendAddFromAll(users, &AsyncC2LFriendAddReq{smsg})
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendAddReq struct {
	smsg *cl.L2C_FriendAdd
}

func (ar *AsyncC2LFriendAddReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	successUsers := dbData.([]*cl.ServerUser)
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d L2C_FriendAdd: get snapshot error:%d",
			args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendAdd, smsg)
		return false
	}

	smsg.Users = successUsers
	if smsg.Ret == uint32(cret.RET_OK) {
		args.Caller.LogFriendAdd(srv, successUsers)
	}
	l4g.Debugf("user: %s,%d L2C_FriendAdd: %s", args.Caller.Name(), args.UID, smsg)
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendAdd, smsg)
	return true
}

type C2LFriendConfirmCommand struct {
	base.UserCommand
}

func (c *C2LFriendConfirmCommand) Error(msg *cl.L2C_FriendConfirm, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendConfirm, msg)
	return false
}

func (c *C2LFriendConfirmCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendConfirmCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendConfirm{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendConfirm Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendConfirm: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendConfirm{
		Ret:    uint32(cret.RET_OK),
		Accept: cmsg.Accept,
		Id:     cmsg.Id,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendConfirm: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if len(cmsg.Id) > int(goxml.GetData().ConfigInfoM.FriendRequestMaxNum) {
		l4g.Errorf("user:%d C2L_FriendConfirm: id too long. cmsg:%v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	if len(cmsg.Id) == 0 {
		l4g.Errorf("user:%d C2L_FriendConfirm: id error. cmsg:%v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	selfFriends := fm.Get(c.Srv, c.User.ID(), false)
	if cmsg.Accept {
		if fm.GetFriendNum(c.Msg.UID) >= goxml.GetData().ConfigInfoM.GetFriendMaxNum(c.User.Vip()) {
			l4g.Errorf("user:%d C2L_FriendConfirm:friend is full", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_FRIEND_LIST_FULL))
		}
		serverUsers := make([]*cl.ServerUser, 0, len(cmsg.Id))
		for i, uid := range cmsg.Id {
			if uint32(i)+fm.GetFriendNum(c.Msg.UID) >= goxml.GetData().ConfigInfoM.GetFriendMaxNum(c.User.Vip()) {
				break // 达到好友上限就break掉
			}
			retCode := fm.CheckAddBySelf(c.User, uid, true)
			if retCode != uint32(cret.RET_OK) {
				if len(cmsg.Id) == 1 {
					l4g.Errorf("user:%d C2L_FriendConfirm:friend check error. retCode:%d ", c.Msg.UID, retCode)
					return c.Error(smsg, retCode)
				}
				smsg.Id = append(smsg.Id, uid)
				continue
			}
			request := selfFriends.GetRequest(uid)
			if request == nil {
				continue
			}
			serverUsers = append(serverUsers, &cl.ServerUser{Uid: request.Id, Sid: request.ServerId})
		}
		if len(serverUsers) == 0 {
			l4g.Errorf("user:%d C2L_FriendConfirm: serverUsers is 0. cmsg:%v", c.Msg.UID, cmsg)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		c.FriendConfirmFromAll(serverUsers, &AsyncC2LFriendConfirmReq{smsg})
	} else {
		fm.DeleteRequest(c.Srv, c.Msg.UID, cmsg.Id)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendConfirm, smsg)
		c.User.LogFriendConfirm(c.Srv, cmsg.Id, smsg.Accept, c.User.ContextID())
	}
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendConfirmReq struct {
	smsg *cl.L2C_FriendConfirm
}

func (ar *AsyncC2LFriendConfirmReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	resps := dbData.([]*l2c.FriendConfirmRsp)
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d L2C_FriendConfirm: get snapshot error:%d",
			args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendConfirm, smsg)
		return false
	}
	fm := srv.GetActivity(activity.Friend).(*afriend.Manager)
	fl := fm.Get(srv, args.UID, true)
	delIDs := make([]uint64, 0, len(resps))
	addIDs := make([]uint64, 0, len(resps))
	for _, rsp := range resps {
		for _, v := range rsp.SuccessUsers {
			fm.Add(srv, args.UID, v.Id, v.Sid)
			if friend := fl.FlushFriend(v.Id); friend != nil {
				friend.User = v
				smsg.Friend = append(smsg.Friend, friend)
			}
			fm.DeleteRequest(srv, args.UID, []uint64{v.Id})
			addIDs = append(addIDs, v.Id)
		}
		delIDs = append(delIDs, rsp.DelIds...)
	}
	fm.DeleteRequest(srv, args.UID, delIDs)
	smsg.Id = delIDs //通知前端需要删除的id
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendConfirm, smsg)
	if smsg.Ret == uint32(cret.RET_OK) {
		fNum := fm.GetFriendNum(args.Caller.ID())
		if fNum > 0 {
			args.Caller.FireCommonEvent(srv.EventM(), aevent.AeFriendCount, uint64(fNum))
		}
		args.Caller.LogFriendConfirm(srv, addIDs, smsg.Accept, args.CtxID)
	}
	return true
}

type C2LFriendDeleteCommand struct {
	base.UserCommand
}

func (c *C2LFriendDeleteCommand) Error(msg *cl.L2C_FriendDelete, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendDelete, msg)
	return false
}

func (c *C2LFriendDeleteCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendDeleteCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendDelete{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendDelete Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendDelete: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendDelete{
		Ret: uint32(cret.RET_OK),
		Id:  cmsg.Id,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendDelete: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if cmsg.Id == c.Msg.UID { // || !base.IsValidUserID(c.Srv, cmsg.Id) {
		l4g.Errorf("user:%d C2L_FriendDelete: id is not valid. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	selfFriends := fm.Get(c.Srv, c.Msg.UID, false)
	if selfFriends == nil {
		l4g.Errorf("user:%d C2L_FriendDelete: id is not valid. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	selfFriendsData := selfFriends.Data()
	targetFriend := selfFriendsData.Friends[cmsg.Id]
	if targetFriend == nil {
		l4g.Errorf("user:%d C2L_FriendDelete: req id not exist. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	if fm.Delete(c.Srv, c.Msg.UID, cmsg.Id) {
		if opFriend := fm.Get(c.Srv, cmsg.Id, false); opFriend != nil {
			fm.Delete(c.Srv, cmsg.Id, c.Msg.UID)
			if opUser := c.Srv.UserM().GetUser(cmsg.Id); opUser != nil {
				fnmsg := &cl.L2C_FriendNotify{
					DelFriend: c.Msg.UID,
				}
				opUser.SendCmdToGateway(cl.ID_MSG_L2C_FriendNotify, fnmsg)
			}
		} else {
			c.FriendDeleteFromCross(&cl.ServerUser{Uid: cmsg.Id, Sid: targetFriend.ServerId}, &AsyncC2LFriendDeleteReq{smsg})
			return true
		}
	} else {
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendDelete, smsg)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendDeleteReq struct {
	smsg *cl.L2C_FriendDelete
}

func (ar *AsyncC2LFriendDeleteReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d L2C_FriendDelete: cross delete error. ret:%d",
			args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendDelete, smsg)
		return false
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendDelete, smsg)
	return true
}

type C2LFriendBlacklistCommand struct {
	base.UserCommand
}

func (c *C2LFriendBlacklistCommand) Error(msg *cl.L2C_FriendBlacklist, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendBlacklist, msg)
	return false
}

func (c *C2LFriendBlacklistCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendBlacklistCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendBlacklist{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendBlacklist Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendBlacklist: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendBlacklist{
		Ret:      uint32(cret.RET_OK),
		Id:       cmsg.Id,
		ServerId: cmsg.ServerId,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendBlackList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cmsg.Id == c.Msg.UID || cmsg.ServerId == 0 || (cmsg.ServerId == c.Srv.ServerID() && !base.IsValidUserID(c.Srv, cmsg.Id)) {
		l4g.Errorf("user: %d C2L_FriendBlacklist: id error.id %d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	if fm.GetBlackNum(c.Msg.UID) >= goxml.GetData().ConfigInfoM.FriendBlackMaxNum {
		l4g.Errorf("user: %d C2L_FriendBlacklist: black num full.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_BLACK_FRIEND_LIST_FULL))
	}
	var relation uint32
	switch relation = fm.GetRelation(c.Msg.UID, cmsg.Id); relation {
	case activity.RelationFriend:
		if clf := fm.ChangeBlacklist(c.Srv, c.Msg.UID, cmsg.Id, cmsg.ServerId); clf != nil {
			if c.Srv.ServerID() == cmsg.ServerId {
				fm.Delete(c.Srv, cmsg.Id, c.Msg.UID)
				if fu := c.Srv.UserM().GetUser(cmsg.Id); fu != nil {
					fnmsg := &cl.L2C_FriendNotify{
						DelFriend: c.Msg.UID,
					}
					fu.SendCmdToGateway(cl.ID_MSG_L2C_FriendNotify, fnmsg)
					clf.User = fu.NewUserSnapshot(0)
				}
			} else {
				c.FriendDeleteFromCross(&cl.ServerUser{Uid: cmsg.Id, Sid: cmsg.ServerId}, &AsyncC2LFriendBlacklistDeleteReq{smsg})
			}

			smsg.Friend = clf
		}
	case activity.RelationStranger:
		if clf := fm.AddBlacklist(c.Srv, c.Msg.UID, cmsg.Id, cmsg.ServerId); clf != nil {
			if c.Srv.ServerID() == cmsg.ServerId {
				if fu := c.Srv.UserM().GetUser(cmsg.Id); fu != nil {
					clf.User = fu.NewUserSnapshot(0)
				}
			}
			smsg.Friend = clf
		}
	default:
		l4g.Errorf("user: %d C2L_FriendBlacklist: relation is invalid. relation: %d", c.Msg.UID, relation)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	if c.ResultOK(smsg.Ret) {
		c.User.LogFriendBlacklist(c.Srv, cmsg.Id, relation)
		//获取离线信息
		if smsg.Friend != nil && smsg.Friend.User == nil {
			serverUser := &cl.ServerUser{Uid: cmsg.Id, Sid: cmsg.ServerId}
			c.GetUserSnapshotsFromAll([]*cl.ServerUser{serverUser}, &AsyncC2LFriendBlacklistReq{smsg}, 0)
			return true
		}
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendBlacklist, smsg)

	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendBlacklistReq struct {
	smsg *cl.L2C_FriendBlacklist
}

func (ar *AsyncC2LFriendBlacklistReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps := dbData.([]*cl.UserSnapshot)
	if len(snaps) != 0 {
		smsg.Friend.User = snaps[0]
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendBlacklist, smsg)
	return true
}

type AsyncC2LFriendBlacklistDeleteReq struct {
	smsg *cl.L2C_FriendBlacklist
}

func (ar *AsyncC2LFriendBlacklistDeleteReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d AsyncC2LFriendBlacklistDeleteReq: delete error, ret:%d sid:%d serverId:%d",
			args.UID, retCode, ar.smsg.Id, ar.smsg.ServerId)
		return false
	}
	return true
}

type C2LFriendRemBlacklistCommand struct {
	base.UserCommand
}

func (c *C2LFriendRemBlacklistCommand) Error(msg *cl.L2C_FriendRemBlacklist, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRemBlacklist, msg)
	return false
}

func (c *C2LFriendRemBlacklistCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendRemBlacklistCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendRemBlacklist{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendRemBlacklist Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendRemBlacklist: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendRemBlacklist{
		Ret: uint32(cret.RET_OK),
		Id:  cmsg.Id,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendRemBlackList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	//if cmsg.Id == c.Msg.UID || !base.IsValidUserID(c.Srv, cmsg.Id) {
	//	l4g.Errorf("user: %d C2L_FriendRemBlacklist: id error.id %d", c.Msg.UID, cmsg.Id)
	//	return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	//}
	// 会移除跨服玩家，所以去掉IsValidUserID的校验
	if cmsg.Id == c.Msg.UID {
		l4g.Errorf("user: %d C2L_FriendRemBlacklist: id error.id %d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	if !fm.RemBlacklist(c.Srv, c.Msg.UID, cmsg.Id) {
		l4g.Errorf("user:%d C2L_FriendRemBlacklist: rem fail. id:%d", c.Msg.UID, cmsg.Id)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	if c.ResultOK(smsg.Ret) {
		c.User.LogFriendRemBlacklist(c.Srv, cmsg.Id)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRemBlacklist, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LFriendSendLikeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LFriendSendLikeCommand) Error(msg *cl.L2C_FriendSendLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendSendLike, msg)
	return false
}

func (c *C2LFriendSendLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendSendLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendSendLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendSendLike Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendSendLike: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendSendLike{
		Ret: uint32(cret.RET_OK),
		Tp:  cmsg.Tp,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendSendLike: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	if cnt := c.User.GetFriendSendCount(); cnt != 0 {
		users := fm.SendLike(c.Srv, c.Msg.UID, cmsg.Tp, cmsg.Id, cnt)
		if len(users) == 0 {
			l4g.Errorf("user: %d C2L_FriendSendLike: no friend can be send ids. tp:%d id:%d",
				c.Msg.UID, cmsg.Tp, cmsg.Id)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		} else {
			otherServerUsers := make([]*cl.ServerUser, 0, len(users))
			var nmsg *cl.L2C_FriendNotify
			for _, user := range users {
				smsg.Ids = append(smsg.Ids, user.Uid)
				if user.Sid != c.Srv.ServerID() {
					otherServerUsers = append(otherServerUsers, user)
					continue
				}
				if tfl := fm.Get(c.Srv, user.Uid, true); tfl.BesentLike(c.User.ID()) {
					fm.SetChange(tfl)
				}
				if fu := c.Srv.UserM().GetUser(user.Uid); fu != nil {
					if nmsg == nil {
						nmsg = &cl.L2C_FriendNotify{
							RcvLikeId: c.Msg.UID,
						}
					}
					fu.SendCmdToGateway(cl.ID_MSG_L2C_FriendNotify, nmsg)
				}
			}
			c.User.AddFriendSentCount(uint32(len(smsg.Ids)))
			awards := []*cl.Resource{{
				Type:  uint32(common.RESOURCE_GOLD),
				Count: uint32(len(smsg.Ids)) * goxml.GetData().ConfigInfoM.FriendReceivePerGold,
			}}
			aRet, showAwards := c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_FRIEND_SEND_LIKE), 0)
			if aRet != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2L_FriendSendLike: award error:%d.tp:%d id:%d", c.Msg.UID, aRet, cmsg.Tp, cmsg.Id)
			}
			smsg.Awards = showAwards
			smsg.SndCnt = c.User.DailyInfo().FriendSndCnt
			c.User.FireCommonEvent(c.Srv.EventM(), aevent.AeFriendSendLikeCount, uint64(len(smsg.Ids)))
			if len(otherServerUsers) > 0 {
				c.FriendSendLikeFromCross(otherServerUsers, nil)
			}
		}
	} else {
		l4g.Errorf("user: %d C2L_FriendSendLike: count limit.tp:%d id:%d", c.Msg.UID, cmsg.Tp, cmsg.Id)
		smsg.Ret = uint32(cret.RET_FRIEND_SEND_LIKE_COUNT_LIMIT)
	}

	if c.ResultOK(smsg.Ret) {
		c.User.LogFriendSendLike(c.Srv, cmsg.Tp, smsg.Ids)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendSendLike, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LFriendRecvLikeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LFriendRecvLikeCommand) Error(msg *cl.L2C_FriendRecvLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRecvLike, msg)
	return false
}

func (c *C2LFriendRecvLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendRecvLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendRecvLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendRecvLike Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debugf("user: %d C2L_FriendRecvLike: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendRecvLike{
		Ret: uint32(cret.RET_OK),
		Tp:  cmsg.Tp,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendRecvLike: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	if cnt := c.User.GetFriendRecvCount(); cnt != 0 {
		smsg.Ids = fm.RecvLike(c.Srv, c.Msg.UID, cmsg.Tp, cmsg.Id, cnt)
		if len(smsg.Ids) == 0 {
			l4g.Errorf("user: %d C2L_FriendRecvLike: empty ids.tp:%d id:%d", c.Msg.UID, cmsg.Tp, cmsg.Id)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		} else {
			c.User.AddFriendRecvedCount(uint32(len(smsg.Ids)))
			awards := []*cl.Resource{{
				Type:  uint32(common.RESOURCE_TOKEN),
				Value: uint32(common.TOKEN_TYPE_FRIEND_LIKE),
				Count: uint32(len(smsg.Ids)) * goxml.GetData().ConfigInfoM.FriendReceivePerPoint,
			}, {
				Type:  uint32(common.RESOURCE_GOLD),
				Count: uint32(len(smsg.Ids)) * goxml.GetData().ConfigInfoM.FriendReceivePerGold,
			}}
			aRet, showAwards := c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_FRIEND_RECV_LIKE), 0)
			if aRet != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2L_FriendRecvLike: award error:%d.tp:%d id:%d", c.Msg.UID, aRet, cmsg.Tp, cmsg.Id)
			}
			smsg.RcvCnt = c.User.DailyInfo().FriendRcvedCnt
			smsg.Awards = showAwards
		}
	} else {
		smsg.Ret = uint32(cret.RET_FRIEND_RECV_LIKE_COUNT_LIMIT)
	}
	if c.ResultOK(smsg.Ret) {
		c.User.LogFriendRecvLike(c.Srv, cmsg.Tp, smsg.Ids)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRecvLike, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LFriendRecommendCommand struct {
	base.UserCommand
}

func (c *C2LFriendRecommendCommand) Error(msg *cl.L2C_FriendRecommend, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRecommend, msg)
	return false
}

// 推荐在线好友
func RecommendOnlineFriend(srv command.Servicer, userID uint64, excludedIds map[uint64]int64,
	geted map[uint64]bool, num int) []*cl.UserSnapshot {
	t := time.AccurateNow()
	users := getRecommendListByLevel(srv, userID, excludedIds, geted, num)
	if elapsed := time.Since(t).Nanoseconds(); elapsed > 1000000 {
		l4g.Infof("user:%d recommendOnlineFriend: elapsed time:%d", userID, elapsed)
	}
	return users
}

// 从在线玩家里按照等级范围取一些合适的人
// 从符合条件的等级段里每段里选取符合条件的N个人，然后合并起来在随机
func GetOnlineRecommendListByLevel(srv command.Servicer, userID uint64, level uint32, excludedIds map[uint64]int64) []*cl.UserSnapshot {
	maxLen := (int(goxml.GetData().ConfigInfoM.RecommendFriendRangeLevel)*2 + 1) * int(goxml.GetData().ConfigInfoM.FriendRecommendNum)
	list := make([]*cl.UserSnapshot, 0, maxLen)
	fm := srv.GetActivity(activity.Friend).(*afriend.Manager)
	minLevel := uint32(0)
	if level > goxml.GetData().ConfigInfoM.RecommendFriendRangeLevel {
		minLevel = level - goxml.GetData().ConfigInfoM.RecommendFriendRangeLevel
	}
	maxLevel := level + goxml.GetData().ConfigInfoM.RecommendFriendRangeLevel
	if minLevel < goxml.GetData().ConfigInfoM.FriendRecommendMinLevel {
		minLevel = goxml.GetData().ConfigInfoM.FriendRecommendMinLevel
	}
	if maxLevel > goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel {
		maxLevel = goxml.GetData().ConfigInfoM.FriendRecommendMaxLevel
	}
	getNum := 0
	for i := minLevel; i <= maxLevel; i++ {
		getNum = 0 //每个等级值需要推荐的个数
		srv.UserM().RandRangeByLevel(srv, i, func(u *character.User) bool {
			id := u.ID()
			if id == userID ||
				excludedIds[id] != 0 ||
				//	fm.GetRelation(userID, id) != activity.RelationStranger ||
				fm.GetRelation(id, userID) != activity.RelationStranger {
				return true
			}
			list = append(list, u.NewUserSnapshot(0))
			getNum++
			return getNum < int(goxml.GetData().ConfigInfoM.FriendRecommendNum)
		})
	}
	//l4g.Debug("uid: %d getOnlineRecommendListByLevel get list %+v", user.ID(), list)
	srv.Rand().Shuffle(len(list), func(i, j int) {
		list[i], list[j] = list[j], list[i]
	})
	if len(list) >= int(goxml.GetData().ConfigInfoM.FriendRecommendNum) {
		return list[0:int(goxml.GetData().ConfigInfoM.FriendRecommendNum)]
	}
	return list
}

// 从在线玩家里直接取出N个,完全随机了
func getRecommendListByLevel(srv command.Servicer, userID uint64, excludedIds map[uint64]int64,
	geted map[uint64]bool, num int) []*cl.UserSnapshot {
	list := make([]*cl.UserSnapshot, 0, num)
	fm := srv.GetActivity(activity.Friend).(*afriend.Manager)
	srv.UserM().RandRange(srv, func(u *character.User) bool {
		id := u.ID()
		//已经筛选过了
		if _, exist := geted[id]; exist {
			return true
		}
		if id == userID ||
			excludedIds[id] != 0 ||
			// fm.GetRelation(userID, id) != activity.RelationStranger ||
			fm.GetRelation(id, userID) != activity.RelationStranger {
			return true
		}
		list = append(list, u.NewUserSnapshot(0))
		return len(list) < num
	})
	//这边就不做随机和等级处理了，直接拿到数据
	return list
}

// 推荐排行榜好友
//
//nolint:mnd
func RecommendFriendFromRank(srv command.Servicer, user *character.User, exists map[uint64]bool, num int) []uint64 {
	levelR := srv.GetGlobalRank(rank.LEVEL)
	firstRank := levelR.First().Value()
	lastRank := levelR.Tail().Value()
	if firstRank == nil || lastRank == nil {
		l4g.Debugf("user: %s,%d recommendFriendFromRank: empty rank", user.Name(), user.ID())
		return nil
	}
	rankMinLevel := int(lastRank.(*rank.LevelValue).Level)
	rankMaxLevel := int(firstRank.(*rank.LevelValue).Level)
	rankLength := int(levelR.Length())

	l4g.Debugf("user: %s,%d recommendFriendFromRank: level rank range:%d %d length:%d",
		user.Name(), user.ID(), rankMinLevel, rankMaxLevel, rankLength)

	fm := srv.GetActivity(activity.Friend).(*afriend.Manager)
	userID := user.ID()
	rangeMin, rangeMax := 5, 10 //搜索等级范围
	span := 5                   //跨度
	base := int(user.Level()) + int(user.RecommendedNum())*span
	user.AddRecommendedNum(1)
	loop := 0
	loopMax := 20                  //最多循环次数
	for ; loop < loopMax; loop++ { //循环尝试扩大搜索范围
		list := make([]uint64, 0, 50)
		min := base - (rangeMin + loop*span)
		if min < rankMinLevel {
			min = rankMinLevel
		}
		if min >= rankMaxLevel {
			break
		}
		max := base + (rangeMax + loop*span)
		if max > rankMaxLevel {
			max = rankMaxLevel
		}
		t := time.AccurateNow() //nolint:varnamelen
		rankList := levelR.GetRangeByScore(&skiplist.RangeSpec{
			Reverse: true,
			Min:     uint64(min),
			Max:     uint64(max),
			Num:     100, //限制数量，防止可能出现过多数据
		})
		for i := len(rankList) - 1; i >= 0; i-- {
			lv := rankList[i].(*rank.LevelValue)
			id := lv.Key()
			if id == userID || exists[id] ||
				user.IsRecommendedFriend(id) ||
				fm.GetRelation(userID, id) != activity.RelationStranger ||
				fm.GetRelation(id, userID) != activity.RelationStranger {
				continue
			}
			list = append(list, id)
			if len(list) >= 50 {
				break
			}
		}
		if elapsed := time.Since(t).Nanoseconds(); elapsed > 1000000 {
			l4g.Errorf("user: %s,%d recommendFriendFromRank elapsed time:%d list:%d range:[%d,%d] loop:%d base:%d %d",
				user.Name(), user.ID(), elapsed, len(list), min, max, loop+1, base, user.Level())
		}
		l4g.Debugf("user: %s,%d recommendFriendFromRank: %v range:[%d,%d] loop:%d base:%d %d",
			user.Name(), user.ID(), list, min, max, loop+1, base, user.Level())
		if len(list) < num {
			if (loop+1) < loopMax && len(rankList) != rankLength && base < rankMaxLevel { //人数不够，而且没有搜索完排行榜，则继续搜索
				continue
			}
			return list
		} else if len(list) > num { //人数多了就随机下
			srv.Rand().Shuffle(len(list), func(i, j int) {
				list[i], list[j] = list[j], list[i]
			})
		}
		return list[:num]
	}
	l4g.Debugf("user: %s,%d recommendFriendFromRank: base out of range:[%d,%d] loop:%d base:%d %d",
		user.Name(), user.ID(), rankMinLevel, rankMaxLevel, loop, base, user.Level())
	return nil
}

// 从等级榜中获取范围玩家，并使用战力榜的更新时间来进行排序
// TODO 直接维护一个活跃的等级排行榜
func RecommendFriendFromRank2(srv command.Servicer, userID uint64, level uint32, excludedIds map[uint64]int64, exists map[uint64]bool, num int) []uint64 {
	t := time.AccurateNow() //nolint:varnamelen
	levelR := srv.GetGlobalRank(rank.LEVEL)
	fm := srv.GetActivity(activity.Friend).(*afriend.Manager)
	now := time.Now().Unix()
	checkF := func(value interface{}) bool {
		lv := value.(*rank.LevelValue)
		if now-lv.Tm > character.DaySecs*int64(goxml.GetData().ConfigInfoM.RecommendFriendActiveDay) {
			return false
		}
		id := lv.ID
		if id == userID || exists[id] ||
			excludedIds[id] != 0 ||
			// fm.GetRelation(userID, id) != activity.RelationStranger ||
			fm.GetRelation(id, userID) != activity.RelationStranger {
			return false
		}
		return true
	}
	uplist := levelR.GetRangeByScore(&skiplist.RangeSpec{
		Reverse:     true,
		MinEx:       true,
		Min:         uint64(level),
		Max:         uint64(level + goxml.GetData().ConfigInfoM.RecommendFriendRangeLevel),
		Num:         100, //这里是为了筛选时间接近的,所以数量取得大一些
		MaxCheckNum: 100,
		CheckF:      checkF,
	})
	min := int(level) - int(goxml.GetData().ConfigInfoM.RecommendFriendRangeLevel)
	if min < 1 {
		min = 1
	}
	downlist := levelR.GetRangeByScore(&skiplist.RangeSpec{
		Reverse:     true,
		Min:         uint64(min),
		Max:         uint64(level),
		Num:         100,
		MaxCheckNum: 100,
		CheckF:      checkF,
	})
	uplist = append(uplist, downlist...)
	//使用战力的时间来做活跃排行，因为战力的更新时间更频繁
	results := make([]*rank.LevelValue, 0, len(uplist))
	powerR := srv.GetGlobalRank(rank.POWER)
	for _, v := range uplist {
		lv := v.(*rank.LevelValue)
		elem := powerR.GetElement(lv.ID)
		if elem == nil {
			l4g.Errorf("user:%d RecommendFriendFromRank2: power rank error:%d", userID, lv.ID)
			continue
		}
		pv := elem.(*rank.PowerValue)
		results = append(results, &rank.LevelValue{
			Level: lv.Level,
			ID:    lv.ID,
			Tm:    pv.Tm,
		})
	}
	if len(results) > 1 {
		sort.Slice(results, func(i, j int) bool { //nolint:varnamelen
			//优先活跃度
			if results[i].Tm > results[j].Tm {
				return true
			}
			if results[i].Tm < results[j].Tm {
				return false
			}
			//优先等级近的
			ilv := int(level) - int(results[i].Level)
			if ilv < 0 {
				ilv = -ilv
			}
			jlv := int(level) - int(results[j].Level)
			if jlv < 0 {
				jlv = -jlv
			}
			if ilv < jlv {
				return true
			}
			if ilv > jlv {
				return false
			}
			return results[i].ID < results[j].ID
		})
	}
	l4g.Debugf("user:%d RecommendFriendFromRank2: results:%v level:%d",
		userID, results, level)
	if len(results) > num {
		results = results[:num]
	}
	ids := make([]uint64, 0, num)
	for _, v := range results {
		ids = append(ids, v.ID)
	}
	if elapsed := time.Since(t).Nanoseconds(); elapsed > 1000000 {
		l4g.Errorf("user:%d recommendFriendFromRank2 elapsed time:%d ids:%v base:%d",
			userID, elapsed, ids, level)
	}
	return ids
}

func (c *C2LFriendRecommendCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendRecommendCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendRecommend{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendRecommend Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendRecommend: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendRecommend{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user: %d C2L_FriendRecommend: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIRecommendFriend) {
		l4g.Errorf("user: %d C2L_FriendRecommend: operate too frequently", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_OPERATE_TOO_OFTEN))
	}
	c.User.ResetRecommendedFriend(time.Now().Unix())

	excludedIds := c.User.CloneRecommendedFriend() // 已经推荐过的ID
	fm := c.Srv.GetActivity(activity.Friend).(*afriend.Manager)
	fl := fm.Get(c.Srv, c.User.ID(), true)
	_, _, blackIDs := fl.FlushFriends()
	now := time.Now().Unix()
	for _, id := range blackIDs {
		excludedIds[id] = now // 黑名单和已经推荐过的ID放一起，组成被排除的ID
	}
	userList1 := GetOnlineRecommendListByLevel(c.Srv, c.User.ID(), c.User.Level(), excludedIds)
	smsg.Users = append(smsg.Users, userList1...)
	if len(userList1) < int(goxml.GetData().ConfigInfoM.FriendRecommendNum) {
		sids := make([]uint64, 0, len(c.Srv.GetNormalPartSids()))
		for _, sid := range c.Srv.GetNormalPartSids() {
			if sid == c.Srv.ServerID() {
				continue
			}
			sids = append(sids, sid)
		}
		c.Srv.Rand().Shuffle(len(sids), func(i, j int) {
			sids[i], sids[j] = sids[j], sids[i]
		})
		if len(sids) > 0 {
			c.FriendRecommendFromOtherServer(sids, excludedIds, &AsyncC2LFriendRecommendReq{smsg: smsg, excludedIds: excludedIds})
			return true
		}
	}

	c.User.AddRecommendedFriend(smsg.Users, time.Now().Unix())
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendRecommend, smsg)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendRecommendReq struct {
	smsg        *cl.L2C_FriendRecommend
	excludedIds map[uint64]int64
}

func (ar *AsyncC2LFriendRecommendReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps := dbData.([]*cl.UserSnapshot)
	if retCode != uint32(r2l.RET_OK) || len(snaps) == 0 {
		l4g.Errorf("user: %d C2L_FriendRecommend: get snapshot error.retCode:%d",
			args.UID, retCode)
	}
	leftNum := int(goxml.GetData().ConfigInfoM.FriendRecommendNum) - len(smsg.Users)
	if len(snaps) > leftNum {
		snaps = snaps[:leftNum]
	}
	smsg.Users = append(smsg.Users, snaps...)

	leftNum = int(goxml.GetData().ConfigInfoM.FriendRecommendNum) - len(smsg.Users)
	if leftNum > 0 {
		geted := make(map[uint64]bool)
		for _, v := range smsg.Users {
			geted[v.Id] = true
		}
		//没有人，在线玩家里面尝试获取
		smsg.Users = append(smsg.Users, RecommendOnlineFriend(srv, args.Caller.ID(), ar.excludedIds, geted, leftNum)...)
	}

	if len(smsg.Users) != 0 {
		args.Caller.AddRecommendedFriend(smsg.Users, time.Now().Unix())
	} else { //还是没人，把屏蔽列表清空
		args.Caller.ClearRecommendedFriend()
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendRecommend, smsg)
	return true
}

type C2LFriendSearchCommand struct {
	base.UserCommand
}

func (c *C2LFriendSearchCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendSearchCommand) Error(msg *cl.L2C_FriendSearch, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendSearch, msg)
	return false
}

func (c *C2LFriendSearchCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendSearch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FriendSearch Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendSearch: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendSearch{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FRIEND), c.Srv) {
		l4g.Errorf("user:%d C2L_FriendSearch: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if cmsg.Id != 0 {
		if !base.IsValidUserID(c.Srv, cmsg.Id) {
			l4g.Errorf("user:%d C2L_FriendSearch: id not valid. id:id:%d", c.Msg.UID, cmsg.Id)
			return c.Error(smsg, uint32(cret.RET_USER_ID_NOT_VALID))
		}
		c.GetLocalUserSnapshots([]uint64{cmsg.Id}, &AsyncC2LFriendSearchReq{smsg}, 0)
	} else if len(cmsg.Name) != 0 {
		if utf8.RuneCountInString(cmsg.Name) <= goxml.GetData().ConfigInfoM.UserNameMaxLength {
			c.GetUserSnapshotsByNameFromAll(c.Srv.GetNormalPartSids(), []string{cmsg.Name}, &AsyncC2LFriendSearchReq{smsg})
			return true
		}
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendSearch, smsg)
	} else {
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendSearch, smsg)
	}
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendSearchReq struct {
	smsg *cl.L2C_FriendSearch
}

func (ar *AsyncC2LFriendSearchReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps := dbData.([]*cl.UserSnapshot)
	/*
		if retCode != uint32(r2l.RET_OK) {
			l4g.Errorf("user: %d L2C_FriendSearch: get snapshot error.retCode:%d",
				args.UID, retCode)
			smsg.Ret = uint32(cret.RET_GET_USER_DATA_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendSearch, smsg)
			return false
		}
	*/
	if len(snaps) != 0 {
		smsg.User = append(smsg.User, snaps...)
	} else {
		smsg.Ret = uint32(cret.RET_GET_USER_DATA_ID_NOT_EXIST)
		l4g.Errorf("user: %d L2C_FriendSearch: user not exist", args.UID)
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendSearch, smsg)
	return true
}

type C2LFriendGetBlacklistCommand struct {
	base.UserCommand
}

func (c *C2LFriendGetBlacklistCommand) Error(msg *cl.L2C_FriendGetBlacklist, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendGetBlacklist, msg)
	return false
}

func (c *C2LFriendGetBlacklistCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFriendGetBlacklistCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FriendGetBlacklist{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user:%d C2L_FriendGetBlacklist: Unmarshal error. err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FriendGetBlacklist: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FriendGetBlacklist{
		Ret: uint32(cret.RET_OK),
	}

	fl := c.Srv.GetActivity(activity.Friend).(*afriend.Manager).Get(c.Srv, c.Msg.UID, true)
	blackList, serverUsers := fl.FlushBlackList()
	smsg.Friends = blackList
	if len(serverUsers) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FriendGetBlacklist, smsg)
		return true
	}

	c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LFriendGetBlacklistReq{smsg}, 0)

	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFriendGetBlacklistReq struct {
	smsg *cl.L2C_FriendGetBlacklist
}

func (ar *AsyncC2LFriendGetBlacklistReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	snaps := dbData.([]*cl.UserSnapshot)
	tmpSnaps := make(map[uint64]*cl.UserSnapshot, len(snaps))
	for _, u := range snaps {
		tmpSnaps[u.Id] = u
	}

	for _, f := range smsg.Friends {
		if snap, exist := tmpSnaps[f.Id]; exist {
			f.User = snap
		}
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FriendGetBlacklist, smsg)
	return true
}
