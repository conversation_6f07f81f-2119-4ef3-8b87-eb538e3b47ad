package dailyspecial

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/time"

	cret "app/protos/out/ret"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_DailySpecialGetData), &C2LDailySpecialGetDataCommand{}, state)     // 获取数据
	cmds.Register(uint32(cl.ID_MSG_C2L_DailySpecialRecvAward), &C2LDailySpecialRecvAwardCommand{}, state) // 领奖
}

type C2LDailySpecialGetDataCommand struct {
	base.UserCommand
}

func (c *C2LDailySpecialGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDailySpecialGetDataCommand) Error(msg *cl.L2C_DailySpecialGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailySpecialGetData, msg)
	return false
}

func (c *C2LDailySpecialGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DailySpecialGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DailySpecialGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_DailySpecialGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_DailySpecialGetData{
		Ret:          uint32(cret.RET_OK),
		DailySpecial: c.User.DailySpecial().Flush(),
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailySpecialGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LDailySpecialRecvAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LDailySpecialRecvAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDailySpecialRecvAwardCommand) Error(msg *cl.L2C_DailySpecialRecvAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailySpecialRecvAward, msg)
	return false
}

func (c *C2LDailySpecialRecvAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DailySpecialRecvAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DailySpecialRecvAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_DailySpecialRecvAward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_DailySpecialRecvAward{
		Ret:   uint32(cret.RET_OK),
		Type:  cmsg.Type,
		Slots: cmsg.Slots,
	}

	switch cmsg.Type {
	case uint32(common.DAILY_SPECIAL_RECV_AWARD_DSRA_DAILY):
		c.receiveDailyAward(smsg)
	case uint32(common.DAILY_SPECIAL_RECV_AWARD_DSRA_SCORE):
		c.receiveScoreAward(smsg)
	default:
		l4g.Errorf("user: %d C2L_DailySpecialRecvAward: receive award type is invalid. type: %d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	return c.ResultOK(smsg.Ret)
}

func (c *C2LDailySpecialRecvAwardCommand) receiveDailyAward(smsg *cl.L2C_DailySpecialRecvAward) bool {
	ret, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_DAILY_SPECIAL_DAILY_AWARD), 1, c.Srv)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DailySpecialRecvAward: receive daily award failed. count not enough", c.Msg.UID)
		return c.Error(smsg, ret)
	}
	awards := goxml.GetData().ActivityDailySpecialInfoM.GetDailyAward(c.Srv.ServerDay(time.Now().Unix()))
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_DAILY_SPECIAL_DAILY_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DailySpecialRecvAward: send daily award failed. errorCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	// 更新使用次数
	c.User.AddNumByType(uint32(common.PURCHASEID_DAILY_SPECIAL_DAILY_AWARD), numSummary)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailySpecialRecvAward, smsg)

	return true
}

func (c *C2LDailySpecialRecvAwardCommand) receiveScoreAward(smsg *cl.L2C_DailySpecialRecvAward) bool {
	if len(smsg.Slots) > goxml.DailySpecialScoreAwardCount {
		l4g.Errorf("user: %d C2L_DailySpecialRecvAward: param num is too long. param len: %d", c.Msg.UID, len(smsg.Slots))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	shopID := goxml.GetData().ActivityDailySpecialInfoM.GetOpenShop(c.Srv.ServerDay(time.Now().Unix()))
	if shopID == 0 {
		l4g.Errorf("user: %d C2L_DailySpecialRecvAward: daily special not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	dailySpecial := c.User.DailySpecial()
	isRepeat := make(map[uint32]struct{})

	awards := make([]*cl.Resource, 0, len(smsg.Slots))
	for _, slot := range smsg.Slots {
		if _, exist := isRepeat[slot]; exist {
			l4g.Errorf("user: %d C2L_DailySpecialRecvAward: slot is repeat. slot: %d", c.Msg.UID, slot)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		isRepeat[slot] = struct{}{}

		if dailySpecial.IsReceive(slot) {
			l4g.Errorf("user: %d C2L_DailySpecialRecvAward: award already received. slot: %d", c.Msg.UID, slot)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}

		cost, award := goxml.GetData().ActivityDailySpecialInfoM.GetScoreCostAndAward(shopID, slot)
		if cost == nil || award == nil {
			l4g.Errorf("user: %d C2L_DailySpecialRecvAward: award or cost is nil. shop: %d slot: %d", c.Msg.UID, shopID, slot)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		smsg.Ret, _ = c.User.CheckResourcesSize([]*cl.Resource{cost}, c.Srv)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_DailySpecialRecvAward: resource not enough.", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}

		awards = append(awards, award)
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_DAILY_SPECIAL_SCORE_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DailySpecialRecvAward: send score award failed. errorCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	if dailySpecial.IsConsumeScoreToken(smsg.Slots) {
		costs := goxml.GetData().ActivityDailySpecialInfoM.GetCostScoreToken(shopID)
		smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_DAILY_SPECIAL_SCORE_AWARD), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_DailySpecialRecvAward: cost resource failed. %d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}

		dailySpecial.Reset()
	} else {
		dailySpecial.UpdateSlotReceive(smsg.Slots)
	}
	smsg.DailySpecial = dailySpecial.Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailySpecialRecvAward, smsg)
	c.User.LogDailySpecialRecvAward(c.Srv, shopID, smsg.Slots)

	return true
}
