package forecast

import (
	"app/goxml"
	"app/logic/character"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ForecastGetData), &C2LForecastGetDataCommand{}, state)           // 获取新功能预告信息
	cmds.Register(uint32(cl.ID_MSG_C2L_ForecastReceiveAward), &C2LForecastReceiveAwardCommand{}, state) //  领取功能奖励
}

type C2LForecastGetDataCommand struct {
	base.UserCommand
}

func (c *C2LForecastGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LForecastGetDataCommand) Error(msg *cl.L2C_ForecastGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ForecastGetData, msg)
	return false
}

func (c *C2LForecastGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ForecastGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ForecastGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ForecastGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_ForecastGetData{
		Ret:      uint32(cret.RET_OK),
		Forecast: c.User.Forecast().Flush(),
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ForecastGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LForecastReceiveAwardCommand struct {
	base.UserCommand
}

func (c *C2LForecastReceiveAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LForecastReceiveAwardCommand) Error(msg *cl.L2C_ForecastReceiveAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ForecastReceiveAward, msg)
	return false
}

func (c *C2LForecastReceiveAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ForecastReceiveAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ForecastReceiveAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ForecastReceiveAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_ForecastReceiveAward{
		Ret:   uint32(cret.RET_OK),
		SysId: cmsg.SysId,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FORECAST), c.Srv) {
		l4g.Errorf("user: %d C2L_ForecastReceiveAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !isValid(cmsg.SysId) {
		l4g.Errorf("user: %d C2L_ForecastReceiveAward: forecastID is invalid. forecastID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	forecast := c.User.Forecast()
	if forecast.GetForecast() == nil {
		l4g.Errorf("user: %d C2L_ForecastReceiveAward: forecast is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FORECAST_NOT_EXIST))
	}
	if !forecast.IsReceive(cmsg.SysId) {
		l4g.Errorf("user: %d C2L_ForecastReceiveAward: award is received.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FORECAST_AWARD_RECEIVED))
	}
	// 检查该功能对应的任务是否完成
	if ret := c.checkTaskFinish(cmsg.SysId, forecast); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ForecastReceiveAward: task not complete. taskID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, ret)
	}

	awards := goxml.GetData().ForecastInfoM.GetAward(cmsg.SysId)
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_FORECAST_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ForecastReceiveAward: send award failed. errorCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	forecast.ReceiveAward(cmsg.SysId)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ForecastReceiveAward, smsg)
	c.User.LogForecastReceiveAward(c.Srv, cmsg.SysId)
	return c.ResultOK(smsg.Ret)
}

// 功能id是否有效
func isValid(forecastID uint32) bool {
	return goxml.GetData().ForecastInfoM.Index(forecastID) != nil
}

// 检查任务是否完成
func (c *C2LForecastReceiveAwardCommand) checkTaskFinish(forecastID uint32, forecast *character.Forecast) uint32 {
	taskInfos := goxml.GetData().ForecastTaskInfoM.GetTaskInfos(forecastID)
	if len(taskInfos) == 0 {
		l4g.Errorf("user:%d C2L_ForecastReceiveAward: taskInfo not exist. forecastID: %d", c.Msg.UID, forecastID)
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	for _, taskInfo := range taskInfos {
		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d C2L_ForecastReceiveAward: task not exist. typeId:%d", c.Msg.UID, taskInfo.TypeId)
			return uint32(cret.RET_SYSTEM_DATA_ERROR)
		}
		// 该任务是否完成
		progress := forecast.GetTaskProgress(taskTypeInfo)
		if !c.User.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
			return uint32(cret.RET_FORECAST_TASK_PROGRESS_NOT_FINISH)
		}
	}

	return uint32(cret.RET_OK)
}
