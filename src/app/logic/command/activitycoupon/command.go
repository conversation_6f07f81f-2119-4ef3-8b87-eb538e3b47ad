package activitycoupon

import (
	"app/gmxml"
	"app/goxml"
	"app/logic/character"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"slices"

	"app/logic/command/base"
	"app/protos/out/cl"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityCouponGetData), &C2LActivityCouponGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityCouponBuy), &C2LActivityCouponBuyCommand{}, state)
}

type C2LActivityCouponGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityCouponGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityCouponGetDataCommand) Error(msg *cl.L2C_ActivityCouponGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityCouponGetData, msg)
	return false
}

func (c *C2LActivityCouponGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityCouponGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityCouponGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityCouponGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityCouponGetData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_COUPON), c.Srv) {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityCouponGetData, smsg)
		return true
	}

	curAct := gmxml.ActivityCouponInfoM.GetCurrentActivity()
	c.User.ActivityCoupon().CheckAndReset(curAct)
	data := c.User.ActivityCoupon().Get()
	if data != nil {
		smsg.ActivityCoupon = data.Clone()
	}

	smsg.ActivityCouponXml = curAct.Clone()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityCouponGetData, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LActivityCouponBuyCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityCouponBuyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityCouponBuyCommand) Error(msg *cl.L2C_ActivityCouponBuy, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityCouponBuy, msg)
	return false
}

func (c *C2LActivityCouponBuyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityCouponBuy{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityCouponBuy Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityCouponBuy: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityCouponBuy{
		Ret:        uint32(cret.RET_OK),
		SysId:      cmsg.SysId,
		RewardType: cmsg.RewardType,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_COUPON), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityCouponBuy func is not open", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	_, exist := goxml.ActivityCouponType[cmsg.RewardType]
	if !exist {
		l4g.Errorf("user:%d C2L_ActivityCouponBuy reward type:%d is not exist", c.User.ID(), cmsg.RewardType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	now := time.Now().Unix()
	c.User.ActivityCoupon().CheckAndReset(gmxml.ActivityCouponInfoM.GetCurrentActivity())

	data := c.User.ActivityCoupon().Get()
	if data == nil {
		l4g.Errorf("user:%d C2L_ActivityCouponBuy activity coupon is nil", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	couponInfo := gmxml.ActivityCouponInfoM.Get(data.ActId)
	if couponInfo == nil || couponInfo.State == gmxml.OpStatusOff {
		l4g.Errorf("user:%d C2L_ActivityCouponBuy get xml failed", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	openDay := int(util.DaysBetweenTimes(couponInfo.OpenDay, now) + 1)

	if c.User.ActivityCoupon().IsBuy(openDay, cmsg.RewardType) {
		l4g.Errorf("user:%d C2l_ActivityCouponBuy openday:%d rewardtype:%d is buy", c.User.ID(), openDay, cmsg.RewardType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	index := slices.IndexFunc(couponInfo.Res, func(res *cl.ActivityCouponRes) bool {
		if res.Day == uint32(openDay) && res.AwardType == cmsg.RewardType {
			return true
		}
		return false
	})

	if index == -1 {
		l4g.Errorf("user:%d C2l_ActivityCouponBuy get ActivityCouponRes openDay:%d awardType:%d failed", c.User.ID(), openDay, cmsg.RewardType)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	awardInfo := couponInfo.Res[index]
	if awardInfo == nil || awardInfo.InternalId != 0 {
		l4g.Errorf("user:%d C2l_ActivityCouponBuy get awardinfo failed", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if len(awardInfo.Cost) > 0 {
		smsg.Ret, smsg.Award = c.User.Trade(c.Srv, awardInfo.Cost, awardInfo.Award, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_COUPON_RECV), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2l_ActivityCouponBuy trade failed error:%d", c.User.ID(), smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	} else {
		smsg.Ret, smsg.Award = c.User.Award(c.Srv, awardInfo.Award, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_COUPON_RECV), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2l_ActivityCouponBuy Award failed error:%d", c.User.ID(), smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	}

	c.User.ActivityCoupon().SetBuy(openDay, cmsg.RewardType)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityCouponBuy, smsg)
	return c.ResultOK(smsg.Ret)
}
