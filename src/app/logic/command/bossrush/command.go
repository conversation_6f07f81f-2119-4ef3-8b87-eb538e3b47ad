package bossrush

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	//cmds.Register(uint32(cl.ID_MSG_C2L_BossRushGetData), &C2LBossRushGetDataCommand{}, state)       //  MSG_C2L_BossRushGetData = 27801;
	//cmds.Register(uint32(cl.ID_MSG_C2L_BossRushFight), &C2LBossRushFightCommand{}, state)           //  MSG_C2L_BossRushFight = 27803;
	//cmds.Register(uint32(cl.ID_MSG_C2L_BossRushTaskAward), &C2LBossRushTaskAwardCommand{}, state)   //  MSG_C2L_BossRushTaskAward = 27805;
	//cmds.Register(uint32(cl.ID_MSG_C2L_BossRushBuyStamina), &C2LBossRushBuyStaminaCommand{}, state) //  MSG_C2L_BossRushBuyStamina = 27807;
}

type C2LBossRushGetDataCommand struct {
	base.UserCommand
}

func (c *C2LBossRushGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBossRushGetDataCommand) Error(msg *cl.L2C_BossRushGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushGetData, msg)
	return false
}

func (c *C2LBossRushGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BossRushGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_BossRushGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_BossRushGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BossRushGetData{
		Ret: uint32(cret.RET_OK),
	}

	bossRush := c.User.BossRush()
	bossRush.CheckInit(c.Srv)
	bossRush.RecoverStamina(c.Srv)

	smsg.Data = c.User.BossRush().Flush()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushGetData, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LBossRushFightCommand struct {
	base.UserCommand
}

func (c *C2LBossRushFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBossRushFightCommand) Error(msg *cl.L2C_BossRushFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushFight, msg)
	return false
}

//nolint:funlen
func (c *C2LBossRushFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BossRushFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_BossRushFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_BossRushFight: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BossRushFight{
		Ret:     uint32(cret.RET_OK),
		BossId:  cmsg.BossId,
		IsSweep: cmsg.IsSweep,
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BOSS_RUSH), c.Srv) {
		l4g.Errorf("user: %d C2L_BossRushFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	seasonId := c.User.GetSeasonID()
	bossRush := c.User.BossRush()
	bossRush.CheckInit(c.Srv)

	bossInfo := goxml.GetData().BossRushInfoM.GetRecordByIdSeasonId(cmsg.BossId, seasonId)
	if bossInfo == nil {
		l4g.Errorf("user: %d C2L_BossRushFight: no bossInfo. boss id %d", c.Msg.UID, cmsg.BossId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	bossHpInfo := goxml.GetData().BossRushHpInfoM.GetRecordById(cmsg.BossId)
	if bossHpInfo == nil {
		l4g.Errorf("user: %d C2L_BossRushFight: no bossHpInfo. boss id %d", c.Msg.UID, cmsg.BossId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if dayNum := c.User.SeasonOpenDay(time.Now().Unix()); dayNum < bossInfo.UnlockDay {
		l4g.Errorf("user: %d C2L_BossRushFight: boss locked by openDay. bossId %d", c.Msg.UID, bossInfo.Id)
		return c.Error(smsg, uint32(cret.RET_BOSS_RUSH_CUR_BOSS_LOCKED))
	}

	if totalFightCount := bossRush.GetTotalFightCount(); totalFightCount < bossInfo.UnlockNum {
		l4g.Errorf("user: %d C2L_BossRushFight: boss locked by fightCount. bossId %d", c.Msg.UID, bossInfo.Id)
		return c.Error(smsg, uint32(cret.RET_BOSS_RUSH_CUR_BOSS_LOCKED))
	}

	if !bossRush.CanAttackBoss(bossInfo, bossHpInfo) {
		l4g.Errorf("user: %d C2L_BossRushFight: cur boss can not attack. bossId %d", c.Msg.UID, bossInfo.Id)
		return c.Error(smsg, uint32(cret.RET_BOSS_RUSH_CUR_BOSS_CANNOT_ATTACK))
	}

	ret, costs := c.User.CheckResourcesSize([]*cl.Resource{goxml.GetData().BossRushConfigInfoM.Stamina})
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_BossRushFight: stamina not enough. ret:%d", c.Msg.UID, ret)
		return c.Error(smsg, ret)
	}

	var rawTeamData []*cl.BossRushTeamData
	reportId := ""
	if !cmsg.IsSweep {
		battleHp := goxml.GetData().BossRushHpInfoM.GetBossBattleHp(bossInfo.Monster)
		if battleHp == 0 {
			l4g.Errorf("user:%d C2L_BossRushFight battleHp = 0. MonsterId %d ", c.Msg.UID, bossInfo.Monster)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		battleReport, retCode, formationTeams := c.User.AttackBossRush(c.Srv, bossInfo.MonsterGroup, common.FORMATION_ID(bossInfo.FormationId), cmsg.ClientData, cmsg.IsSweep)
		if retCode != cret.RET_OK {
			l4g.Errorf("user:%d C2L_BossRushFight battle: error. %d", c.Msg.UID, retCode)
			return c.Error(smsg, uint32(cret.RET_BATTLE_ERROR))
		}

		reportsLen := uint32(len(battleReport.Reports))
		if reportsLen != uint32(len(formationTeams)) {
			l4g.Errorf("user:%d C2L_BossRushFight battle reportsLen %d != teamsLen %d ", c.Msg.UID, reportsLen, uint32(len(formationTeams)))
			return c.Error(smsg, uint32(cret.RET_BATTLE_ERROR))
		}
		rawTeamData = bossRush.GenerateBattleTeamData(battleReport, reportsLen, formationTeams, battleHp)
		reportId = battleReport.Id
	} else {
		bossData := bossRush.GetBossByGroup(bossInfo.GroupId)
		if bossData == nil {
			l4g.Errorf("user:%d C2L_BossRushFight: no bossData. groupId %d", c.Msg.UID, bossInfo.GroupId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if bossData.MaxRecord == nil {
			l4g.Errorf("user:%d C2L_BossRushFight: no MaxRecord. groupId %d", c.Msg.UID, bossInfo.GroupId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if bossData.BossId != cmsg.BossId {
			l4g.Errorf("user:%d C2L_BossRushFight: bossData.BossId %d not match cmsg.BossId %d.", c.Msg.UID, bossData.BossId, cmsg.BossId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		rawTeamData = bossRush.GenerateSweepTeamData(bossData.MaxRecord.TeamData)
	}
	if len(rawTeamData) == 0 {
		l4g.Error("user:%d C2L_BossRushFight gen rawTeamData err. ", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_BATTLE_ERROR))
	}

	dropAwards, dropFlag := c.User.Drop().DoDrop(c.Srv.Rand(), bossInfo.DropGroup)
	if !dropFlag {
		l4g.Errorf("user: %d C2L_BossRushFight: DoDrop get Awards error. DropGroup:%d", c.Msg.UID, bossInfo.DropGroup)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	l4g.Debugf("user: %d C2L_BossRushFight: GuildMedalAdd raw dropAwards %+v", c.Msg.UID, dropAwards)
	if guildMedal := c.Srv.GetGuildMedal(c.User.ID()); guildMedal != nil {
		dropAwards = c.addGuildMedal(dropAwards, guildMedal)
		l4g.Debugf("user: %d C2L_BossRushFight: GuildMedalAdd processed dropAwards %+v", c.Msg.UID, dropAwards)
	}

	// TODO 注意函数的单一职责，后续考虑优化
	processedTeamData, bossData, battleResult, pRet := bossRush.CalcBattleResult(bossInfo.GroupId, cmsg.BossId, bossHpInfo, rawTeamData, reportId, bossInfo)
	if pRet != uint32(cret.RET_OK) {
		l4g.Error("user: %d C2L_BossRushFight ProcessRawTeamData error. err:%+v", c.Msg.UID, pRet)
		return c.Error(smsg, pRet)
	}

	progressAwards := goxml.GetData().BossRushRewardInfoM.CalcRewardsByReduceHp(c.User.GetSeasonID(), cmsg.BossId, battleResult.BeforeReduceHp, battleResult.AfterReduceHp)

	// 奖励分开显示
	if len(costs) > 0 {
		smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, dropAwards, uint32(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_ATTACK_BOSS), uint64(log.BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_DROP))
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_BossRushFight: trade dropAwards error. ret:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	} else {
		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, dropAwards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_ATTACK_BOSS), uint64(log.BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_DROP))
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_BossRushFight: award dropAwards error. ret:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	}

	if len(progressAwards) > 0 {
		smsg.Ret, smsg.ProgressAwards = c.User.Award(c.Srv, progressAwards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_ATTACK_BOSS), uint64(log.BOSS_RUSH_AWARD_TYPE_BOSS_RUSH_AWARD_TYPE_PROGRESS))
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_BossRushFight: award progressAwards error. ret:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	}

	bossRush.UpdateBoss(bossInfo.GroupId, bossData)
	bossRush.AddTotalFightCount(1)
	if bossInfo.IsInfinite == 0 { // 非无限挑战Boss才更新
		if battleResult.KillBoss {
			c.User.AchievementsShowcase().UpdateAchieve(uint32(common.FUNCID_MODULE_BOSS_RUSH), bossInfo.GroupId, bossInfo.Level)
		}
		c.User.UpdateSeasonBossRushRank(c.Srv, bossInfo.RankId, bossInfo.Id, bossInfo.Level, battleResult.AfterReduceHp)
	}
	if battleResult.UpdateMaxRecord {
		c.User.CognitionManager().Send(c.Srv, bossInfo.FormationId, cmsg.BossId, bossRush.CalcProgressByReduceHp(battleResult.CalcTotalReduceHp(), bossHpInfo.MaxHp), battleResult.TotalDamage) // 复用round字段
	}
	bossRush.TriggerEvent(c.Srv.EventM(), bossInfo.GroupId, bossInfo.Level, battleResult.KillBoss)
	battleData := &log.LogBossRushFight{
		BossId:            cmsg.BossId,
		BossLevel:         bossInfo.Level,
		FormationId:       bossInfo.FormationId,
		TeamNum:           bossInfo.Team,
		IsSweep:           cmsg.IsSweep,
		BattleReportId:    reportId,
		TeamDamages:       battleResult.TeamDamages,
		TeamProgress:      battleResult.TeamProgress,
		BossTotalProgress: bossRush.CalcProgressByReduceHp(battleResult.AfterReduceHp, bossHpInfo.MaxHp),
		KillBoss:          battleResult.KillBoss,
	}
	c.User.LogBossRushFight(c.Srv, c.User.GetSeasonID(), battleData)

	smsg.TeamsData = processedTeamData
	smsg.BossData = bossData.Clone()
	smsg.TotalDamage = battleResult.TotalDamage
	smsg.TotalReduceHp = battleResult.CalcTotalReduceHp()
	smsg.ReportId = reportId
	smsg.TotalFightCount = bossRush.GetTotalFightCount()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushFight, smsg)
	return c.ResultOK(smsg.Ret)
}

func (c *C2LBossRushFightCommand) addGuildMedal(awards []*cl.Resource, guildMedal *db.LogicGuildMedal) []*cl.Resource {
	addValue := uint32(0)
	for _, medal := range guildMedal.Medals {
		info := goxml.GetData().GuildMedalTaskInfoM.Index(medal.GetMedalId())
		if info == nil {
			continue
		}
		if info.AddType != goxml.GuildMedalAddBossRush {
			continue
		}
		addValue += info.AddValue
	}
	if addValue == 0 {
		return awards
	}

	newAwards := make([]*cl.Resource, 0, len(awards))
	for _, award := range awards {
		cAward := award.Clone()
		cAward.Count = cAward.Count + cAward.Count*addValue/10000 //nolint:mnd
		newAwards = append(newAwards, cAward)
	}

	return newAwards
}

type C2LBossRushTaskAwardCommand struct {
	base.UserCommand
}

func (c *C2LBossRushTaskAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBossRushTaskAwardCommand) Error(msg *cl.L2C_BossRushTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushTaskAward, msg)
	return false
}

func (c *C2LBossRushTaskAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BossRushTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_BossRushTaskAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_BossRushTaskAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BossRushTaskAward{
		Ret:     uint32(cret.RET_OK),
		TaskIds: cmsg.TaskIds,
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BOSS_RUSH), c.Srv) {
		l4g.Errorf("user: %d C2L_BossRushTaskAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	bossRush := c.User.BossRush()
	bossRush.CheckInit(c.Srv)

	maxLen := goxml.GetData().BossRushTaskInfoM.GetTaskNum()
	if len(cmsg.TaskIds) == 0 || len(cmsg.TaskIds) > maxLen {
		l4g.Errorf("user:%d C2L_BossRushTaskAward: invalid taskIds. taskLen %d maxLen %d", c.Msg.UID, len(cmsg.TaskIds), maxLen)
		return c.Error(smsg, uint32(cret.RET_PARAM_LENGTH_LIMIT))
	}

	var totalAwards []*cl.Resource
	repeatedM := make(map[uint32]struct{})
	for _, taskId := range cmsg.TaskIds {
		_, exist := repeatedM[taskId]
		if exist {
			l4g.Errorf("user:%d C2L_BossRushTaskAward:  task id %d repeated. ", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		repeatedM[taskId] = struct{}{}

		taskInfo := goxml.GetData().BossRushTaskInfoM.GetRecordById(taskId)
		if taskInfo == nil {
			l4g.Errorf("user:%d C2L_BossRushTaskAward: no taskInfo. id %d", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if bossRush.IsTaskAward(taskId) {
			l4g.Errorf("user:%d C2L_BossRushTaskAward: taskId %d has received. ", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d C2L_BossRushTaskAward: taskTypeInfo not exist. typeID:%d", c.Msg.UID, taskInfo.TypeId)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		progress := bossRush.GetTaskProgress(taskTypeInfo)
		if !c.User.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
			l4g.Errorf("user:%d C2L_BossRushTaskAward: task id:%d not finish", c.Msg.UID, taskId)
			return c.Error(smsg, uint32(cret.RET_SEASON_ARENA_TASK_PROGRESS_NOT_ENOUGH))
		}

		totalAwards = append(totalAwards, taskInfo.Rewards...)
	}
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalAwards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_TASK_AWARD), 0)

	bossRush.SetTaskAward(cmsg.TaskIds)

	c.User.LogBossRushTaskAward(c.Srv, cmsg.TaskIds)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushTaskAward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LBossRushBuyStaminaCommand struct {
	base.UserCommand
}

func (c *C2LBossRushBuyStaminaCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LBossRushBuyStaminaCommand) Error(msg *cl.L2C_BossRushBuyStamina, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushBuyStamina, msg)
	return false
}

func (c *C2LBossRushBuyStaminaCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BossRushBuyStamina{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_BossRushBuyStamina Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_BossRushBuyStamina: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_BossRushBuyStamina{
		Ret:     uint32(cret.RET_OK),
		BuyType: cmsg.BuyType,
		Count:   cmsg.Count,
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_BOSS_RUSH), c.Srv) {
		l4g.Errorf("user: %d C2L_BossRushBuyStamina: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !goxml.IsFuncInSeason(goxml.GetData(), c.User.GetSeasonID(), uint32(common.FUNCID_MODULE_BOSS_RUSH)) {
		l4g.Debugf("[BossRushM] CheckInit: func not in season. ")
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	bossRush := c.User.BossRush()
	bossRush.CheckInit(c.Srv)
	bossRush.RecoverStamina(c.Srv)

	if cmsg.Count == 0 {
		l4g.Errorf("user: %d C2L_BossRushBuyStamina: buy count error, buyCount: %d", c.Msg.UID, cmsg.Count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	var numSummary *cl.NumInfo
	var costs, awards []*cl.Resource
	switch cmsg.BuyType {
	case uint32(common.BOSS_RUSH_BUY_TYPE_BRBT_DIAMOND):
		smsg.Ret, costs, numSummary = c.User.CheckNumByType(uint32(common.PURCHASEID_BOSS_RUSH_BUY_COUNT), cmsg.Count)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_BossRushBuyStamina: CheckNumByType err", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}
		awards = goxml.GetData().BossRushConfigInfoM.CalcDiamondBuyStaminaRes(cmsg.Count)
	case uint32(common.BOSS_RUSH_BUY_TYPE_BRBT_ITEM):
		costs, awards = goxml.GetData().BossRushConfigInfoM.CalcItemBuyStaminaRes(cmsg.Count)
	default:
		l4g.Errorf("user: %d C2L_BossRushBuyStamina: invalid buyType %d", c.Msg.UID, cmsg.BuyType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	smsg.Ret, _ = c.User.Trade(c.Srv, costs, awards, uint32(log.RESOURCE_CHANGE_REASON_BOSS_RUSH_STAMINA_BUY), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_BossRushBuyStamina: trade error, ret: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	if numSummary != nil {
		c.User.AddNumByType(uint32(common.PURCHASEID_BOSS_RUSH_BUY_COUNT), numSummary)
	}

	smsg.StaminaRecoverTime = bossRush.GetStaminaRecoverTime()

	c.User.FireCommonEvent(c.Srv.EventM(), event.IeBossRushBuyTimes, uint64(cmsg.Count))

	c.User.LogAddPurchaseNum(c.Srv, uint32(common.PURCHASEID_BOSS_RUSH_BUY_COUNT), cmsg.Count, cmsg.BuyType)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BossRushBuyStamina, smsg)
	return c.ResultOK(smsg.Ret)
}
