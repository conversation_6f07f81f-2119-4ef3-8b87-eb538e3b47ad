package worldboss

import (
	"app/goxml"
	"app/logic/battle"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"math"

	"gitlab.qdream.com/kit/sea/time"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossGetData), &C2LWorldBossGetDataCommand{}, state)         // 获取数据
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossSelectLevel), &C2LWorldBossSelectLevelCommand{}, state) // 选择等级并匹配房间
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossRoomInfo), &C2LWorldBossRoomInfoCommand{}, state)       // 房间信息
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossFight), &C2LWorldBossFightCommand{}, state)             // 战斗：挑战/扫荡
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossRecvAward), &C2LWorldBossRecvAwardCommand{}, state)     // 领取任务奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossWorship), &C2LWorldBossWorshipCommand{}, state)         // 对讨伐之星进行膜拜
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossRank), &C2LWorldBossRankCommand{}, state)               // 排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_WorldBossGetRoomLog), &C2LWorldBossGetRoomLogCommand{}, state)   // 获取房间日志
}

type C2LWorldBossGetDataCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossGetDataCommand) Error(msg *cl.L2C_WorldBossGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetData, msg)
	return false
}

func (c *C2LWorldBossGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossGetData: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossGetData{
		Ret: uint32(cret.RET_OK),
	}

	crossMsg := &l2c.C2L_WorldBossGetData{}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WorldBossGetData, c.Msg.UID, crossMsg) {
		worldBoss := c.User.WorldBoss()
		worldBoss.CheckReset(c.Srv)
		if c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
			smsg.WorldBoss = worldBoss.Flush()
		}
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetData, smsg)
	}

	return c.ResultOK(smsg.Ret)
}

type C2LWorldBossSelectLevelCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossSelectLevelCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossSelectLevelCommand) Error(msg *cl.L2C_WorldBossSelectLevel, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossSelectLevel, msg)
	return false
}

func (c *C2LWorldBossSelectLevelCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossSelectLevel{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossSelectLevel Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossSelectLevel: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossSelectLevel{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		l4g.Errorf("user: %d C2L_WorldBossSelectLevel: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	worldBoss := c.User.WorldBoss()
	openCfg := worldBoss.GetOpenWorldBossCfg(c.Srv, time.Now().Unix())
	if openCfg == nil {
		l4g.Errorf("user: %d C2L_WorldBossSelectLevel: worldBoss not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_NOT_OPEN))
	}
	worldBoss.CheckReset(c.Srv)

	handbookScore := c.handbookScore()
	level := goxml.GetData().WorldbossDifficultyInfoM.GetMaxLevel(handbookScore)
	if level == 0 || level != cmsg.Level {
		l4g.Errorf("user: %d C2L_WorldBossSelectLevel: handbook score is invalid. score:%d", c.Msg.UID, handbookScore)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_HANDBOOK_SCORE_INVALID))
	}

	if worldBoss.IsMatchRoom() {
		l4g.Errorf("user: %d C2L_WorldBossSelectLevel: worldBoss already match room", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_ALREADY_MATCH_ROOM))
	}

	msg := &l2c.L2C_WorldBossMatchRoom{
		SysId:          openCfg.Id,
		Level:          cmsg.Level,
		PlayerName:     c.User.Name(),
		ServerOpenTime: c.Srv.StartServiceTm(),
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WorldBossMatchRoom, c.Msg.UID, msg) {
		l4g.Errorf("user: %d C2L_WorldBossSelectLevel: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

func (c *C2LWorldBossSelectLevelCommand) handbookScore() uint32 {
	score := uint32(0)
	linkIDs := goxml.GetData().LinkBookInfoM.GetLinkIDs(goxml.LinkTypeCommon)
	for _, id := range linkIDs {
		score += c.User.HandbookManager().GetHeroHandbookM().GetLinkScore(id)
	}

	return score
}

type C2LWorldBossRoomInfoCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossRoomInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossRoomInfoCommand) Error(msg *cl.L2C_WorldBossRoomInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRoomInfo, msg)
	return false
}

func (c *C2LWorldBossRoomInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossRoomInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossRoomInfo Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossRoomInfo: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossRoomInfo{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		l4g.Errorf("user: %d C2L_WorldBossRoomInfo: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	smsg.Ret = isOpenOrMatchRoom(c.Srv, c.User)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossRoomInfo: worldBoss not open or not match room. ret: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	worldBoss := c.User.WorldBoss()
	msg := &l2c.L2C_WorldBossRoomInfo{
		MaxHurt:      worldBoss.GetMaxHurt(),
		MaxHurtScore: worldBoss.GetMaxHurtScore(),
		ActivityId:   worldBoss.GetWorldBossSysID(),
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WorldBossRoomInfo, c.Msg.UID, msg) {
		l4g.Errorf("user: %d C2L_WorldBossRoomInfo: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LWorldBossFightCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LWorldBossFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossFightCommand) Error(msg *cl.L2C_WorldBossFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossFight, msg)
	return false
}

//nolint:funlen
func (c *C2LWorldBossFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossFight: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossFight{
		Ret:  uint32(cret.RET_OK),
		Type: cmsg.Type,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		l4g.Errorf("user: %d C2L_WorldBossFight: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if cmsg.Type != uint32(common.WORLD_BOSS_FIGHT_TYPE_WBFT_SWEEP) && cmsg.Type != uint32(common.WORLD_BOSS_FIGHT_TYPE_WBFT_FIGHT) {
		l4g.Errorf("user: %d C2L_WorldBossFight: type is invalid. type:%d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	// 防刷验证
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIWorldBoss) {
		l4g.Errorf("user: %d C2L_WorldBossFight: operate too frequently", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_OPERATE_TOO_OFTEN))
	}

	smsg.Ret = isOpenOrMatchRoom(c.Srv, c.User)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossFight: worldBoss not open or not match room. ret: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	worldBoss := c.User.WorldBoss()
	data := worldBoss.GetWorldBossData()
	if data == nil {
		l4g.Errorf("user: %d C2L_WorldBossFight: not match room.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM))
	}
	worldBossInfo := goxml.GetData().WorldbossInfoM.Index(data.SysId)
	if worldBossInfo == nil {
		l4g.Errorf("user: %d C2L_WorldBossFight: get sysId:%d failed.", c.Msg.UID, data.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	bossID := goxml.GetData().WorldbossInfoM.GetBossID(data.SysId, data.Level)
	if bossID == 0 {
		l4g.Errorf("user: %d C2L_WorldBossFight: get bossID failed. id: %d, level: %d", c.Msg.UID, data.SysId, data.Level)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	dInfo := goxml.GetData().WorldbossDifficultyInfoM.Index(data.Level)
	if dInfo == nil {
		l4g.Errorf("user: %d C2L_WorldBossFight: difficultyInfo is nil. level: %d", c.Msg.UID, data.Level)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 战斗资源检查是否够
	fightCount := worldBoss.RecoverFightCount()
	var fightCosts []*cl.Resource
	if fightCount == 0 {
		ret, costs := c.User.CheckResourcesSize(goxml.GetData().WorldbossConfigInfoM.FightCostRes, c.Srv)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_WorldBossFight: resource not enough. ret:%d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}
		fightCosts = costs
	}

	// 扫荡/挑战
	var (
		curHurt      uint64
		curHurtScore uint64
	)
	var pokemonTowerFloor uint32
	if goxml.WorldBossTypeDefault != worldBossInfo.BossType {
		pokemonTowerFloor = c.User.TowerPokemon().GetDungeonId()
	}
	switch cmsg.Type {
	case uint32(common.WORLD_BOSS_FIGHT_TYPE_WBFT_SWEEP):
		if worldBoss.GetMaxHurt() == 0 {
			l4g.Errorf("user: %d C2L_WorldBossFight: not fighting.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_MAX_HURT_IS_ZERO))
		}
		curHurt = worldBoss.GetMaxHurt()
	case uint32(common.WORLD_BOSS_FIGHT_TYPE_WBFT_FIGHT):
		formationID := uint32(common.FORMATION_ID_FI_WORLD_BOSS)
		// TODO: 策划要求，v2.1版本不上，先注释掉
		altRaisePs := battle.NewAltRaisePS()
		skillRet := c.extraSkill(altRaisePs, worldBossInfo.BossType)
		if skillRet != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_WorldBossFight: calc buff failed. ret:%d", c.Msg.UID, uint32(skillRet))
			return c.Error(smsg, skillRet)
		}
		battleReport, retCode := c.User.AttackWorldBoss(c.Srv, bossID, formationID, altRaisePs, cmsg.ClientData)
		if retCode != cret.RET_OK {
			l4g.Errorf("user: %d C2L_WorldBossFight: fight failed. ret:%d", c.Msg.UID, uint32(retCode))
			return c.Error(smsg, uint32(cret.RET_BATTLE_ERROR))
		}
		smsg.ReportId = battleReport.GetId()
		curHurt = battleReport.Reports[0].GetTotalHurt()
	}
	// 伤害积分：伤害开平方根，向上取整
	curHurtScore = uint64(math.Ceil(math.Sqrt(float64(curHurt))))
	// 历史最高伤害
	maxHurt := c.calcMaxHurtScore(worldBoss.GetMaxHurt(), curHurt)
	// 历史最高伤害积分
	maxHurtScore := c.calcMaxHurtScore(worldBoss.GetMaxHurtScore(), curHurtScore)
	// 累计伤害和积分
	accumulativeHurt := worldBoss.GetAccumulativeHurt() + curHurt
	accumulativeHurtScore := worldBoss.GetAccumulativeHurtScore() + curHurtScore

	msg := &l2c.L2C_WorldBossSyncFightData{
		Type:                  cmsg.Type,
		Hurt:                  curHurt,
		HurtScore:             curHurtScore,
		ReportId:              smsg.ReportId,
		PlayerName:            c.User.Name(),
		MaxHurt:               maxHurt,
		MaxHurtScore:          maxHurtScore,
		AccumulativeHurt:      accumulativeHurt,
		AccumulativeHurtScore: accumulativeHurtScore,
		PokemonFloor:          pokemonTowerFloor,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WorldBossSyncFightData, c.Msg.UID, msg) {
		l4g.Errorf("user: %d C2L_WorldBossFight: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	// 扣资源
	var costType uint32
	if len(fightCosts) == 0 {
		worldBoss.DecFightCount()
		costType = goxml.WorldBossFightCostTypeFightCount
	} else {
		smsg.Ret = c.User.Consume(c.Srv, fightCosts, uint32(log.RESOURCE_CHANGE_REASON_WORLD_BOSS_FIGHT_COST), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_WorldBossFight: consume err.", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}
		costType = goxml.WorldBossFightCostTypeFightRoll
	}

	worldBoss.UpdateHurtAndScore(curHurt, curHurtScore)
	c.User.FireCommonEvent(c.Srv.EventM(), uint32(event.IeWorldBossFight), uint64(1))
	c.User.LogWorldBossFight(c.Srv, data.SysId, cmsg.Type, data.Room.GetRoomSysId(), costType, worldBoss.GetLevel(), curHurt, smsg.ReportId)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LWorldBossFightCommand) extraSkill(battlePs *battle.AltRaisePS, worldBossType uint32) uint32 {
	raisePSMap := make(map[uint32][]uint64)
	switch worldBossType {
	case goxml.WorldBossTypeDefault:
		return uint32(cret.RET_OK)
	case goxml.WorldBossTypeArtifact:
		dungeonID := c.User.TowerPokemon().GetDungeonId()
		floorBuff := goxml.GetData().WorldbossTowerBuffInfoM.GetFloorBuff(goxml.WorldBossAddTypeArtifact, dungeonID)
		if floorBuff != nil {
			raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(floorBuff.BuffId, floorBuff.SkillLv)
			if raisePassiveSkillInfo == nil {
				l4g.Errorf("user:%d C2LWorldBossFight extraSkill failed towerbuffID:%d", c.Msg.UID, floorBuff.Id)
				return uint32(cret.RET_SYSTEM_DATA_ERROR)
			}
			raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)
		}

		addInfo := goxml.GetData().WorldbossAddInfoM.GetAdd(c.User.GetSeasonID(), goxml.WorldBossAddTypeArtifact)
		if addInfo != nil {
			totalStar := c.User.ArtifactManager().CalcArtifactTotalStar(addInfo)
			addPsInfo := goxml.GetData().WorldbossAddInfoM.GetAddPs(c.User.GetSeasonID(), goxml.WorldBossAddTypeArtifact, totalStar)
			if addPsInfo != nil {
				raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(addPsInfo.Value, addPsInfo.SkillLv)
				if raisePassiveSkillInfo == nil {
					l4g.Errorf("user:%d RaisePassiveSkillInfoM extraSkill failed WorldbossAddInfo:%d", c.Msg.UID, addPsInfo.Id)
					return uint32(cret.RET_SYSTEM_DATA_ERROR)
				}
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)
			}
		}
		battlePs.AltAttack(raisePSMap)
		return uint32(cret.RET_OK)
	case goxml.WorldBossTypePokemon:
		dungeonID := c.User.TowerPokemon().GetDungeonId()
		floorBuff := goxml.GetData().WorldbossTowerBuffInfoM.GetFloorBuff(goxml.WorldBossAddTypePokemon, dungeonID)
		if floorBuff != nil {
			raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(floorBuff.BuffId, 1)
			if raisePassiveSkillInfo == nil {
				l4g.Errorf("user:%d C2LWorldBossFight extraSkill failed towerbuffID:%d", c.Msg.UID, floorBuff.Id)
				return uint32(cret.RET_SYSTEM_DATA_ERROR)
			}
			raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)
		}

		addInfo := goxml.GetData().WorldbossAddInfoM.GetAdd(c.User.GetSeasonID(), goxml.WorldBossAddTypePokemon)
		if addInfo != nil {
			totalStar := c.User.PokemonManager().CalcPokemonTotalStar(addInfo)
			addPsInfo := goxml.GetData().WorldbossAddInfoM.GetAddPs(c.User.GetSeasonID(), goxml.WorldBossAddTypePokemon, totalStar)
			if addPsInfo != nil {
				raisePassiveSkillInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(addPsInfo.Value, 1)
				if raisePassiveSkillInfo == nil {
					l4g.Errorf("user:%d RaisePassiveSkillInfoM extraSkill failed WorldbossAddInfo:%d", c.Msg.UID, addPsInfo.Id)
					return uint32(cret.RET_SYSTEM_DATA_ERROR)
				}
				raisePSMap[battle.TeamUniterBattlePos] = append(raisePSMap[battle.TeamUniterBattlePos], raisePassiveSkillInfo.ID)
			}
		}
		battlePs.AltAttack(raisePSMap)
		return uint32(cret.RET_OK)
	default:
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}
}

//func (c *C2LWorldBossFightCommand) extraSkill(dInfo *goxml.WorldbossDifficultyInfo, formationID uint32) *battle.AltRaisePS {
//	altRaisePS := battle.NewAltRaisePS()
//	c.emblemExtraSkill(altRaisePS, formationID, dInfo)
//
//	return altRaisePS
//}

//func (c *C2LWorldBossFightCommand) emblemExtraSkill(altRaisePS *battle.AltRaisePS, formationID uint32, dInfo *goxml.WorldbossDifficultyInfo) {
//	team := c.User.GetFormationTeam(formationID, goxml.FormationTeamOneIndex)
//	if team == nil || len(team.Info) == 0 {
//		return
//	}
//	ancientLayer := uint32(0)    // 远古词缀层数
//	immemorialLayer := uint32(0) // 太古词缀层数
//	pos2RaisePSs := make(map[uint32][]uint64)
//	for _, info := range team.Info {
//		heroM := c.User.HeroManager()
//		hero := heroM.Get(info.Hid)
//		if hero == nil {
//			continue
//		}
//		// 获取 符文套装词缀：远古还是太古
//		suitType := hero.GetHeroEmblemAffixType(c.User)
//		switch suitType {
//		case uint32(common.EMBLEM_AFFIX_TYPE_EAT_ANCIENT):
//			ancientLayer++
//		case uint32(common.EMBLEM_AFFIX_TYPE_EAT_ARCHAIC): // 太古满足，默认远古也满足
//			ancientLayer++
//			immemorialLayer++
//		}
//	}
//	if ancientLayer == 0 && immemorialLayer == 0 {
//		return
//	}
//	// 加远古技能
//	if ancientLayer > 0 && immemorialLayer == 0 {
//		c.addRaisePSs(pos2RaisePSs, dInfo.RaisePassiveSkill1, ancientLayer)
//	}
//	// 加太古技能：太古满足，默认远古也满足
//	if immemorialLayer > 0 {
//		c.addRaisePSs(pos2RaisePSs, dInfo.RaisePassiveSkill1, ancientLayer)
//		c.addRaisePSs(pos2RaisePSs, dInfo.RaisePassiveSkill2, immemorialLayer)
//	}
//
//	altRaisePS.AltAttack(pos2RaisePSs)
//}

//func (c *C2LWorldBossFightCommand) addRaisePSs(pos2RaisePSs map[uint32][]uint64, skillID, layer uint32) {
//	raisePSInfo := goxml.GetData().RaisePassiveSkillInfoM.GetRaisePassiveSkill(skillID, layer)
//	if raisePSInfo == nil {
//		return
//	}
//	pos2RaisePSs[battle.TeamUniterBattlePos] = append(pos2RaisePSs[battle.TeamUniterBattlePos], raisePSInfo.ID)
//}

func (c *C2LWorldBossFightCommand) calcMaxHurtScore(maxValue, curValue uint64) (value uint64) {
	if maxValue > curValue {
		value = maxValue
	} else {
		value = curValue
	}

	return
}

type C2LWorldBossRecvAwardCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossRecvAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossRecvAwardCommand) Error(msg *cl.L2C_WorldBossRecvAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRecvAward, msg)
	return false
}

func (c *C2LWorldBossRecvAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossRecvAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossRecvAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossRecvAward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossRecvAward{
		Ret:     uint32(cret.RET_OK),
		TaskIds: cmsg.TaskIds,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	taskNum := len(cmsg.TaskIds)
	if taskNum > goxml.GetData().WorldbossTaskInfoM.GetTaskNum() {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: task num too many.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	worldBoss := c.User.WorldBoss()
	openCfg := worldBoss.GetOpenWorldBossCfg(c.Srv, time.Now().Unix())
	if openCfg == nil {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: worldBoss not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_NOT_OPEN))
	}
	worldBoss.CheckReset(c.Srv)

	awards := make([]*cl.Resource, 0, taskNum)
	isRepeat := make(map[uint32]struct{}, taskNum)
	for _, taskID := range cmsg.TaskIds {
		ret, award := c.checkTaskID(isRepeat, taskID, worldBoss.GetWorldBossType())
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_WorldBossRecvAward: client param error. errorCode: %d", c.Msg.UID, ret)
			return c.Error(smsg, ret)
		}
		awards = append(awards, award...)
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_WORLD_BOSS_RECEIVE_TASK_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: send task award failed. errorCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	worldBoss.ReceiveTaskAward(cmsg.TaskIds)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRecvAward, smsg)
	c.User.LogWorldBossTaskRecvAward(c.Srv, worldBoss.GetWorldBossSysID(), cmsg.TaskIds)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LWorldBossRecvAwardCommand) checkTaskID(isRepeatMap map[uint32]struct{}, taskID uint32, worldBossType uint32) (uint32, []*cl.Resource) {
	// taskID是否重复
	if _, exist := isRepeatMap[taskID]; exist {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: taskID is repeat. repeat taskID: %d", c.Msg.UID, taskID)
		return uint32(cret.RET_REPEATED_PARAM), nil
	}
	isRepeatMap[taskID] = struct{}{}

	// taskID 是否存在
	taskInfo := goxml.GetData().WorldbossTaskInfoM.Index(taskID)
	if taskInfo == nil || taskInfo.BossType != worldBossType {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: taskID not exist. taskID: %d", c.Msg.UID, taskID)
		return uint32(cret.RET_SYSTEM_DATA_ERROR), nil
	}

	// 是否已领过奖
	if c.User.WorldBoss().IsReceiveTaskAward(taskID) {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: repeat receive task award. taskID: %d", c.Msg.UID, taskID)
		return uint32(cret.RET_WORLD_BOSS_TASK_AWARD_ALREADY_RECEIVED), nil
	}

	// 该任务是否完成
	if c.checkTaskFinish(taskInfo.TypeId, taskInfo.Value) != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossRecvAward: taskID not complete. taskID: %d", c.Msg.UID, taskID)
		return uint32(cret.RET_WORLD_BOSS_TASK_NOT_FINISH), nil
	}

	return uint32(cret.RET_OK), taskInfo.ClRes
}

// 检查任务是否完成
func (c *C2LWorldBossRecvAwardCommand) checkTaskFinish(typeID, value uint32) uint32 {
	taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(typeID)
	if taskTypeInfo == nil {
		l4g.Errorf("user:%d C2L_WorldBossRecvAward: taskTypeInfo not exist. typeId:%d", c.Msg.UID, typeID)
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	progress := c.User.WorldBoss().GetOneTypeProgress(taskTypeInfo)
	// 该任务是否完成
	if !c.User.CheckTaskFinish(progress, typeID, uint64(value)) {
		return uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}

	return uint32(cret.RET_OK)
}

type C2LWorldBossWorshipCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LWorldBossWorshipCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossWorshipCommand) Error(msg *cl.L2C_WorldBossWorship, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossWorship, msg)
	return false
}

func (c *C2LWorldBossWorshipCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossWorship{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossWorship Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossWorship: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossWorship{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		l4g.Errorf("user: %d C2L_WorldBossWorship: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	worldBoss := c.User.WorldBoss()
	openCfg := worldBoss.GetOpenWorldBossCfg(c.Srv, time.Now().Unix())
	if openCfg == nil {
		l4g.Errorf("user: %d C2L_WorldBossWorship: worldBoss not open or not match room.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_NOT_OPEN))
	}
	worldBoss.CheckReset(c.Srv)

	ret, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_WORLD_BOSS_DAILY_WORSHIP_COUNT), 1, c.Srv)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossWorship: receive worship award failed. count not enough", c.Msg.UID)
		return c.Error(smsg, ret)
	}

	dropAwards, dropFlag := c.User.Drop().DoDrop(c.Srv.Rand(), goxml.GetData().WorldbossConfigInfoM.WorshipDropGroup)
	if !dropFlag {
		l4g.Errorf("user: %d C2L_WorldBossWorship: get drop award failed. ", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_WORSHIP_DROP_AWARD_NOT_EXIST))
	}
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, dropAwards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_WORLD_BOSS_RECEIVE_WORSHIP_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossWorship: send worship award failed. errorCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	// 更新使用次数
	c.User.AddNumByType(uint32(common.PURCHASEID_WORLD_BOSS_DAILY_WORSHIP_COUNT), numSummary)
	c.User.FireCommonEvent(c.Srv.EventM(), uint32(event.IeWorldBossWorship), uint64(1))
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossWorship, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LWorldBossRankCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossRankCommand) Error(msg *cl.L2C_WorldBossRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRank, msg)
	return false
}

func (c *C2LWorldBossRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossRank: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossRank{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		l4g.Errorf("user: %d C2L_WorldBossRank: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_NONE) == cmsg.Type || cmsg.Type >= uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_MAX) {
		l4g.Errorf("user: %d C2L_WorldBossRank: rank type is invalid. rank:%d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	c.User.WorldBoss().CheckReset(c.Srv)

	if cmsg.Type == uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_PARTITION) {
		if goxml.GetData().WorldbossDifficultyInfoM.Index(cmsg.Level) == nil {
			l4g.Errorf("user: %d C2L_WorldBossRank: level is invalid. level:%d", c.Msg.UID, cmsg.Level)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if cmsg.StartRank == 0 || cmsg.EndRank == 0 || cmsg.StartRank > cmsg.EndRank {
			l4g.Errorf("user: %d C2L_WorldBossRank: start and end is invalid. level:%d start:%d, end:%d", c.Msg.UID, cmsg.Level, cmsg.StartRank, cmsg.EndRank)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

	}

	msg := &l2c.L2C_WorldBossRank{
		Type:      cmsg.Type,
		Level:     cmsg.Level,
		StartRank: cmsg.StartRank,
		EndRank:   cmsg.EndRank,
		BossType:  cmsg.BossType,
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WorldBossRank, c.Msg.UID, msg) {
		l4g.Errorf("user: %d C2L_WorldBossRank: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LWorldBossGetRoomLogCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossGetRoomLogCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossGetRoomLogCommand) Error(msg *cl.L2C_WorldBossGetRoomLog, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetRoomLog, msg)
	return false
}

func (c *C2LWorldBossGetRoomLogCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_WorldBossGetRoomLog{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossGetRoomLog Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_WorldBossGetRoomLog: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossGetRoomLog{
		Ret: uint32(cret.RET_OK),
		Num: cmsg.Num,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		l4g.Errorf("user: %d C2L_WorldBossGetRoomLog: function not open.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if cmsg.Num == 0 || cmsg.Num > goxml.WorldBossGetRoomLogMaxNum {
		l4g.Errorf("user: %d C2L_WorldBossGetRoomLog: num is invalid. num: %d", c.Msg.UID, cmsg.Num)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	smsg.Ret = isOpenOrMatchRoom(c.Srv, c.User)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossGetRoomLog: worldBoss not open or not match room. ret: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WorldBossGetRoomLog, c.Msg.UID, &l2c.L2C_WorldBossGetRoomLog{Num: cmsg.Num}) {
		l4g.Errorf("user: %d C2L_WorldBossRoomInfo: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

func isOpenOrMatchRoom(srv command.Servicer, u *character.User) uint32 {
	worldBoss := u.WorldBoss()
	openCfg := worldBoss.GetOpenWorldBossCfg(srv, time.Now().Unix())
	if openCfg == nil {
		return uint32(cret.RET_WORLD_BOSS_NOT_OPEN)
	}

	worldBoss.CheckReset(srv)
	if !worldBoss.IsMatchRoom() {
		return uint32(cret.RET_WORLD_BOSS_NOT_MATCH_ROOM)
	}

	return uint32(cret.RET_OK)
}
