package goddesscontract

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/event"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/time"

	"app/logic/command/base"
	"app/logic/helper"
	"app/protos/in/log"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessContractGetData), &C2LGoddessContractGetDataCommand{}, state)                     //获取数据 NOLOG
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessFeed), &C2LGoddessFeedCommand{}, state)                                           //喂食
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessTouch), &C2LGoddessTouchCommand{}, state)                                         //抚摸
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessStoryAward), &C2LGoddessStoryAwardCommand{}, state)                               // 英雄故事领奖
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessUnlock), &C2LGoddessUnlockCommand{}, state)                                       // 解锁对应英雄
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessChapterFight), &C2LGoddessChapterFightCommand{}, state)                           //章节战斗
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessCollect), &C2LGoddessCollectCommand{}, state)                                     //收藏女武神
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessChangeSuit), &C2LGoddessChangeSuitCommand{}, state)                               //更改女武神装扮
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessRecovery), &C2LGoddessRecoveryCommand{}, state)                                   //恢复女武神装扮
	cmds.Register(uint32(cl.ID_MSG_C2L_GoddessSuitUnlock), &C2LGoddessSuitUnlockCommand{}, state)                               //激活女武神皮肤
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityGoddessRecvRecoveryAward), &C2LActivityGoddessRecvRecoveryAwardCommand{}, state) //女武神家园治疗女武神奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityGoddessActive), &C2LActivityGoddessActiveCommand{}, state)                       //
}

type C2LGoddessContractGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGoddessContractGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessContractGetDataCommand) Error(msg *cl.L2C_GoddessContractGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessContractGetData, msg)
	return false
}

func (c *C2LGoddessContractGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessContractGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessContractGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessContractGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessContractGetData{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessContractGetData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	smsg.GoddessContract = c.User.GoddessContract().FlushAll()
	smsg.TotalExp = c.User.GoddessContract().GetExp()
	smsg.TotalLevel = c.User.GoddessContract().GetLevel()
	smsg.TouchCount = c.User.DailyInfo().GoddessTouchCount
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessContractGetData, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGoddessFeedCommand struct {
	base.UserCommand
}

func (c *C2LGoddessFeedCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessFeedCommand) Error(msg *cl.L2C_GoddessFeed, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessFeed, msg)
	return false
}

func (c *C2LGoddessFeedCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessFeed{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessFeed Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessFeed: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessFeed{
		Ret:       uint32(cret.RET_OK),
		GoddessId: cmsg.GoddessId,
		ItemId:    cmsg.ItemId,
		ItemCount: cmsg.ItemCount,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessFeed: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	gc := c.User.GoddessContract()
	goddess := gc.Get(cmsg.GoddessId)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_FeedGoddessContract: Goddess:%d not init", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}

	if !goxml.GetData().GoddessContractTreatInfoM.IsRecoveryFinish(goddess.Id, goddess.RecoveryCount) {
		l4g.Errorf("user: %d C2L_FeedGoddessContract: Goddess:%d not Finish", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_RECOVER_NOT_FINISH))
	}

	exp, favourite := goxml.GetData().GoddessContractGiftsInfoM.GetItemExp(cmsg.GoddessId, cmsg.ItemId)
	if exp == 0 {
		l4g.Errorf("user: %d C2L_FeedGoddessContract: get giftID:%d exp failed", c.User.ID(), cmsg.ItemId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.ItemCount > goxml.GetData().ConfigInfoM.GetGiftsNum() {
		l4g.Errorf("user: %d C2L_GoddessContractFeed: feed count:%d more than config count:%d error,",
			c.User.ID(), cmsg.ItemCount, goxml.GetData().ConfigInfoM.GetGiftsNum())
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	cost := make([]*cl.Resource, 0, 1)
	cost = append(cost, &cl.Resource{
		Value: cmsg.ItemId,
		Count: cmsg.ItemCount,
		Type:  uint32(common.RESOURCE_ITEM),
	})

	ret := c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_GODDESS_FEED), 0)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GoddessContractFeed: feed gift:%d count:%d not enough",
			c.Msg.UID, cmsg.ItemId, cmsg.ItemCount)
		return c.Error(smsg, uint32(cret.RET_GODDESS_FEED_ITEM_NOT_ENOUGH))
	}

	totalExp := exp * smsg.ItemCount
	if favourite {
		goddess.SetFavourite(cmsg.ItemId)
	}
	gc.AddGoddessExp(c.Srv, goddess, totalExp)

	smsg.Goddess = (*cl.Goddess)(goddess).Clone()
	smsg.TotalLevel = gc.GetLevel()
	smsg.TotalExp = gc.GetExp()
	smsg.Collections = gc.GetCollections()

	c.User.LogGoddessFeed(c.Srv, goddess.Id, goddess.Exp)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessFeed, smsg)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGoddessFeed, uint64(cmsg.ItemCount))
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGoddessTouchOrFeed, uint64(cmsg.ItemCount))

	return c.ResultOK(smsg.Ret)
}

type C2LGoddessTouchCommand struct {
	base.UserCommand
}

func (c *C2LGoddessTouchCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessTouchCommand) Error(msg *cl.L2C_GoddessTouch, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessTouch, msg)
	return false
}

func (c *C2LGoddessTouchCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessTouch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessTouch Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessTouch: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessTouch{
		Ret:       uint32(cret.RET_OK),
		GoddessId: cmsg.GoddessId,
		BodyType:  cmsg.BodyType,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessTouch: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	gc := c.User.GoddessContract()
	goddess := gc.Get(cmsg.GoddessId)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_GoddessTouch: Goddess:%d not init", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}

	if !goxml.GetData().GoddessContractTreatInfoM.IsRecoveryFinish(goddess.Id, goddess.RecoveryCount) {
		l4g.Errorf("user: %d C2L_GoddessTouch: Goddess:%d not Finish", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_RECOVER_NOT_FINISH))
	}

	if c.User.DailyInfo().GoddessTouchCount >= c.User.GoddessTouchCountMax() {
		l4g.Errorf("user:%d C2L_GoddessTouch daily touch count:%d is uesd:%d",
			c.User.ID(), c.User.DailyInfo().GoddessTouchCount, c.User.GoddessTouchCountMax())
		return c.Error(smsg, uint32(cret.RET_GODDESS_TOUCH_COUNT_NOT_ENOUGH))
	}

	levelInfo := goxml.GetData().GoddessContractTrustInfoM.Index(goddess.Level)
	if levelInfo == nil {
		l4g.Errorf("user: %d C2L_GoddessTouch: Goddess:%d level:%d is nil", c.Msg.UID, cmsg.GoddessId, goddess.Level)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	var addexp uint32
	switch cmsg.BodyType {
	case uint32(common.GODDESS_BODY_TOUCH_TYPE_NORMAL):
		addexp = levelInfo.TouchExp1
	case uint32(common.GODDESS_BODY_TOUCH_TYPE_INTIMATE):
		addexp = levelInfo.TouchExp2
	default:
		l4g.Errorf("user: %d C2L_GoddessTouch: goddessId:%d touch type:%d is error", c.Msg.UID, cmsg.GoddessId, cmsg.BodyType)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	gc.AddGoddessExp(c.Srv, goddess, addexp)
	c.User.AddGoddessTouchCount()

	smsg.TotalExp = gc.GetExp()
	smsg.TotalLevel = gc.GetLevel()
	smsg.Goddess = goddess.Clone()
	smsg.TouchCount = c.User.DailyInfo().GoddessTouchCount
	smsg.Collections = gc.GetCollections()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessTouch, smsg)
	c.User.LogGoddessTouch(c.Srv, goddess.Id, goddess.Exp)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGoddessTouch, 1)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGoddessTouchOrFeed, 1)

	return c.ResultOK(smsg.Ret)
}

type C2LGoddessStoryAwardCommand struct {
	base.UserCommand
}

func (c *C2LGoddessStoryAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessStoryAwardCommand) Error(msg *cl.L2C_GoddessStoryAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessStoryAward, msg)
	return false
}

func (c *C2LGoddessStoryAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessStoryAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessStoryAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessStoryAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessStoryAward{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessStoryAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	eliteInfo := goxml.GetData().GoddessContractEliteInfoM.Index(cmsg.StoryId)
	if eliteInfo == nil {
		l4g.Errorf("user: %d C2L_GoddessStoryAward: story:%d not exist in goddess contract elite xml ", c.Msg.UID, cmsg.StoryId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	gc := c.User.GoddessContract()
	goddess := gc.Get(eliteInfo.Goddess)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_GoddessStoryAward: Goddess:%d is not init", c.Msg.UID, eliteInfo.Goddess)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}

	if goddess.Level < eliteInfo.UnlockLevel {
		l4g.Errorf("user: %d C2L_GoddessStoryAward: Goddess:%d level:%d is less than xml level:%d ", c.Msg.UID, eliteInfo.Goddess, goddess.Level, eliteInfo.UnlockLevel)
		return c.Error(smsg, uint32(cret.RET_GODDESS_LEVEL_NOT_ENOUGH))
	}

	if goddess.IsStoryAwarded(eliteInfo.EliteId) {
		l4g.Errorf("user: %d C2L_GoddessStoryAward: Goddess:%d story:%d is awarded", c.Msg.UID, eliteInfo.Goddess, eliteInfo.EliteId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_STORY_AWARD_IS_TOKEN))
	}

	var retResource []*cl.Resource
	smsg.Ret, retResource = c.User.Award(c.Srv, eliteInfo.RewardsClRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GODDESS_STORY_AWARDS), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GoddessStoryAward award eror", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	goddess.SetAwardState(eliteInfo.EliteId)
	gc.SaveGoddess()
	smsg.StoryId = cmsg.StoryId
	smsg.Goddess = goddess.Clone()
	smsg.Reward = retResource
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessStoryAward, smsg)
	c.User.LogGoddessStoryTakeReward(c.Srv, goddess.Id, cmsg.StoryId)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGoddessStoryFinish, 1)

	return c.ResultOK(smsg.Ret)
}

type C2LGoddessUnlockCommand struct {
	base.UserCommand
}

func (c *C2LGoddessUnlockCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessUnlockCommand) Error(msg *cl.L2C_GoddessUnlock, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessUnlock, msg)
	return false
}

func (c *C2LGoddessUnlockCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessUnlock{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessUnlock Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessUnlock: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessUnlock{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessUnlock: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	goddessInfo := goxml.GetData().GoddessContractInfoM.Index(cmsg.GoddessId)
	if goddessInfo == nil {
		l4g.Errorf("user: %d C2L_GoddessUnlock: Goddess:%d not exist in goddess contract info xml ", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if goddessInfo.GoddessSheild != 0 {
		l4g.Errorf("user: %d C2L_GoddessUnlock: Goddess id:%d unlock is error", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	gc := c.User.GoddessContract()
	if gc.Get(cmsg.GoddessId) != nil {
		l4g.Errorf("user: %d C2L_GoddessUnlock: Goddess:%d is init", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_HAS_INIT))
	}

	if c.User.Dungeon().GetDungeonID() < goddessInfo.GoddessId {
		l4g.Errorf("user: %d C2L_GoddessUnlock: Goddess:%d is init goddess info dungeon:%d user dungeon:%d",
			c.Msg.UID, cmsg.GoddessId, goddessInfo.GoddessId, c.User.Dungeon().GetDungeonID())
		return c.Error(smsg, uint32(cret.RET_GODDESS_INIT_FAILED))
	}

	unlocks := make([]*goxml.GoddessContractInfo, 0, 1)
	unlocks = append(unlocks, goddessInfo)
	gc.AddGoddess(unlocks)
	gc.SaveGoddess()
	goddess := gc.Get(cmsg.GoddessId)

	smsg.GoddessId = cmsg.GoddessId
	smsg.Goddess = goddess.Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessUnlock, smsg)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGoddessUnlock, 1)
	c.User.LogGoddessInit(c.Srv, cmsg.GoddessId)

	return c.ResultOK(smsg.Ret)
}

type C2LGoddessChapterFightCommand struct {
	base.UserCommand
}

func (c *C2LGoddessChapterFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessChapterFightCommand) Error(msg *cl.L2C_GoddessChapterFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessChapterFight, msg)
	return false
}

func (c *C2LGoddessChapterFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessChapterFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessChapterFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessChapterFight: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessChapterFight{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	chapterCfg := goxml.GetData().GoddessTalesDungeonInfoM.Index(cmsg.Id)
	if chapterCfg == nil {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: chapterId:%d not fund goddess_tales_dungeon_info",
			c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if chapterCfg.MonsterGroup == 0 {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: chapterId:%d has no fight", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	gc := c.User.GoddessContract()

	goddess := gc.Get(chapterCfg.TalesId)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: chapterId:%d goddess:%d not init",
			c.Msg.UID, cmsg.Id, chapterCfg.TalesId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}

	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIBattle) {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: operate too frequently", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_OPERATE_TOO_OFTEN))
	}

	if len(cmsg.Assists) > goxml.TeamMaxPos {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: chapterId:%d tales:%d Check Assists failed",
			c.Msg.UID, cmsg.Id, chapterCfg.TalesId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	checkRepeat := make(map[uint32]struct{}, len(cmsg.Assists))
	for _, id := range cmsg.Assists {
		if id == 0 { //0代表空位
			continue
		}
		if !chapterCfg.IsInAssistHeros(id) {
			l4g.Errorf("user: %d C2L_TalesChapterFight: chapterId:%d assistHero:%d error",
				c.Msg.UID, cmsg.Id, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if _, ok := checkRepeat[id]; ok {
			l4g.Errorf("user:%d C2L_TalesChapterFight: id repeated. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		checkRepeat[id] = struct{}{}
	}
	if len(checkRepeat) == 0 {
		l4g.Errorf("user: %d C2L_GoddessChapterFight: chapterId:%d tales:%d Check Assists failed",
			c.Msg.UID, cmsg.Id, chapterCfg.TalesId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	for _, mustAssistHero := range chapterCfg.MustAssistHeros {
		if _, exist := checkRepeat[mustAssistHero]; !exist {
			l4g.Errorf("user: %d C2L_TalesChapterFight: chapterId:%d mustAssistHero:%d dont set",
				c.Msg.UID, cmsg.Id, mustAssistHero)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}
	win, reportID, bRet := c.User.AttackGoddess(c.Srv, chapterCfg.MonsterGroup, cmsg.Assists, cmsg.ClientData)
	if bRet != cret.RET_OK {
		l4g.Errorf("user: %d C2L_TalesChapterFight: AttackTales err.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_BATTLE_ERROR))
	}
	smsg.Id = cmsg.Id
	smsg.Win = win
	smsg.ReportId = reportID
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessChapterFight, smsg)
	c.User.LogGoddessChapterFight(c.Srv, chapterCfg.TalesId, cmsg.Id, win, smsg.ReportId)
	return c.ResultOK(smsg.Ret)
}

type C2LGoddessCollectCommand struct {
	base.UserCommand
}

func (c *C2LGoddessCollectCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessCollectCommand) Error(msg *cl.L2C_GoddessCollect, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessCollect, msg)
	return false
}

func (c *C2LGoddessCollectCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessCollect{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessCollectCommand Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessCollectCommand: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessCollect{
		Ret:       uint32(cret.RET_OK),
		GoddessId: cmsg.GoddessId,
		Collect:   cmsg.Collect,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessCollectCommand: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	gc := c.User.GoddessContract()
	goddess := gc.Get(cmsg.GoddessId)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_GoddessCollectCommand: Goddess:%d is not init", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}
	goddess.SetCollect(cmsg.Collect)
	gc.SaveGoddess()

	smsg.Goddess = goddess.Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessCollect, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGoddessChangeSuitCommand struct {
	base.UserCommand
}

func (c *C2LGoddessChangeSuitCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessChangeSuitCommand) Error(msg *cl.L2C_GoddessChangeSuit, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessChangeSuit, msg)
	return false
}

func (c *C2LGoddessChangeSuitCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessChangeSuit{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessChangeSuitCommand Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessChangeSuitCommand: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessChangeSuit{
		Ret:       uint32(cret.RET_OK),
		GoddessId: cmsg.GoddessId,
		SuitId:    cmsg.SuitId,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessChangeSuitCommand: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	gc := c.User.GoddessContract()

	goddess := gc.Get(cmsg.GoddessId)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_GoddessChangeSuitCommand: Goddess:%d is not init", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}

	have := goddess.HasSuit(cmsg.SuitId)
	if !have {
		l4g.Errorf("user: %d C2L_GoddessChangeSuitCommand: Goddess:%d is not init", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_SUIT_NOT_EXIST))
	}

	goddess.SetSuit(cmsg.SuitId)
	gc.SaveGoddess()

	smsg.Goddess = goddess.Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessChangeSuit, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGoddessRecoveryCommand struct {
	base.UserCommand
}

func (c *C2LGoddessRecoveryCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessRecoveryCommand) Error(msg *cl.L2C_GoddessRecovery, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessRecovery, msg)
	return false
}

func (c *C2LGoddessRecoveryCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessRecovery{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessRecoveryCommand Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessRecoveryCommand: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessRecovery{
		Ret:       uint32(cret.RET_OK),
		GoddessId: cmsg.GoddessId,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessRecoveryCommand: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	gc := c.User.GoddessContract()

	goddess := gc.Get(cmsg.GoddessId)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_GoddessRecoveryCommand: Goddess:%d is not init", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}
	//是否完成恢复
	if goxml.GetData().GoddessContractTreatInfoM.IsRecoveryFinish(goddess.Id, goddess.RecoveryCount) {
		l4g.Errorf("user: %d C2L_GoddessRecoveryCommand: Goddess:%d recovery finish", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_RECOVER_FINISH))
	}
	//时间是否恢复
	now := time.Now()
	if !goddess.CanRecovery(now.Unix()) {
		l4g.Errorf("user: %d C2L_GoddessRecoveryCommand: Goddess:%d recovery time not", c.Msg.UID, cmsg.GoddessId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_RECOVER_TIME_NOT_ENOUGH))
	}
	//下一次表数据
	recoveryCount := goddess.RecoveryCount
	nextRecoveryCount := recoveryCount + 1

	recoveryInfo := goxml.GetData().GoddessContractTreatInfoM.GetGoddessTimes(goddess.Id, nextRecoveryCount)
	if recoveryInfo == nil {
		l4g.Errorf("user: %d C2L_GoddessRecoveryCommand: Goddess:%d nextRecoveryCount:%d", c.Msg.UID, cmsg.GoddessId, nextRecoveryCount)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	nextRecoveryTime := now.Add(time.Duration(recoveryInfo.Cd) * time.Hour).Unix()
	if len(recoveryInfo.ItemClRes) > 0 {
		smsg.Ret = c.User.Consume(c.Srv, recoveryInfo.ItemClRes, uint32(log.RESOURCE_CHANGE_REASON_GODDESS_RECOVERY), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_GoddessRecovery: Goddess:%d nextRecoveryCount:%d reduce consume failed error code:%d", c.Msg.UID, cmsg.GoddessId, nextRecoveryCount, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
	}

	goddess.Recovery(nextRecoveryCount, nextRecoveryTime)

	gc.AddGoddessExp(c.Srv, goddess, recoveryInfo.TrustValue)

	gc.SaveGoddess()
	smsg.Goddess = goddess.Clone()
	smsg.TotalExp = gc.GetExp()
	smsg.TotalLevel = gc.GetLevel()
	smsg.Collections = gc.GetCollections()

	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGoddessRecovery, uint64(recoveryInfo.TreatElite))

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessRecovery, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGoddessSuitUnlockCommand struct {
	base.UserCommand
}

func (c *C2LGoddessSuitUnlockCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGoddessSuitUnlockCommand) Error(msg *cl.L2C_GoddessSuitUnlock, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessSuitUnlock, msg)
	return false
}

func (c *C2LGoddessSuitUnlockCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GoddessSuitUnlock{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GoddessSuitUnlock Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GoddessSuitUnlock: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GoddessSuitUnlock{
		Ret:    uint32(cret.RET_OK),
		SuitId: cmsg.SuitId,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_GoddessSuitUnlocks: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	suitInfo := goxml.GetData().GoddessContractSkinInfoM.Index(cmsg.SuitId)
	if suitInfo == nil {
		l4g.Errorf("user:%d C2L_GoddessSuitUnlocks: suitId:%d xml is nil", c.Msg.UID, cmsg.SuitId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	gc := c.User.GoddessContract()

	goddess := gc.Get(suitInfo.Goddess)
	if goddess == nil {
		l4g.Errorf("user: %d C2L_GoddessSuitUnlocks: Goddess:%d is not init", c.Msg.UID, suitInfo.Goddess)
		return c.Error(smsg, uint32(cret.RET_GODDESS_NOT_INIT))
	}

	have := goddess.HasSuit(cmsg.SuitId)
	if have {
		l4g.Errorf("user: %d C2L_GoddessSuitUnlocks: Goddess:%d suit:%d is repeated", c.Msg.UID, suitInfo.Goddess, cmsg.SuitId)
		return c.Error(smsg, uint32(cret.RET_GODDESS_SUIT_NOT_EXIST))
	}

	if goddess.Level < suitInfo.UnlockLevel {
		l4g.Errorf("user: %d C2L_GoddessSuitUnlocks goddess:%d level:%d suit:%d level:%d", c.Msg.UID, goddess.Id, goddess.Level, suitInfo.GoddessSkinId, suitInfo.UnlockLevel)
		return c.Error(smsg, uint32(cret.RET_GODDESS_LEVEL_NOT_ENOUGH))
	}

	gc.AddSuit(c.Srv, suitInfo, goddess)

	smsg.Goddess = goddess.Clone()
	smsg.SuitId = cmsg.SuitId
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GoddessSuitUnlock, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LActivityGoddessRecvRecoveryAwardCommand struct {
	base.UserCommand
}

func (c *C2LActivityGoddessRecvRecoveryAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityGoddessRecvRecoveryAwardCommand) Error(msg *cl.L2C_ActivityGoddessRecvRecoveryAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityGoddessRecvRecoveryAward, msg)
	return false
}

func (c *C2LActivityGoddessRecvRecoveryAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityGoddessRecvRecoveryAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityGoddessRecvRecoveryAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityGoddessRecvRecoveryAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_ActivityGoddessRecvRecoveryAward{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityGoddessRecvRecoveryAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if c.User.GoddessContract().CheckActivityGoddessRecv(cmsg.Id) {
		l4g.Errorf("user:%d C2L_ActivityGoddessRecvRecoveryAward client param id:%d is recv", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	rewardInfo := goxml.GetData().ActivityGoddessInfoM.Index(cmsg.Id)
	if rewardInfo == nil {
		l4g.Errorf("user:%d C2L_ActivityGoddessRecvRecoveryAward get reward info:%d failed", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	recoveryCount := c.User.GoddessContract().GetRecoveryCount()
	if cmsg.Id == goxml.TotalRecoveryReward {
		if recoveryCount < goxml.GetData().ActivityGoddessInfoM.MaxCount() {
			l4g.Errorf("user:%d C2L_ActivityGoddessRecvRecoveryAward client ID:%d recovery count:%d ", c.Msg.UID, cmsg.Id, recoveryCount)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	} else {
		if cmsg.Id > recoveryCount {
			l4g.Errorf("user:%d C2L_ActivityGoddessRecvRecoveryAward client ID:%d recovery count:%d ", c.Msg.UID, cmsg.Id, recoveryCount)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	smsg.Ret, smsg.Reward = c.User.Award(c.Srv, rewardInfo.RewardClRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GODDESS_CONTRACT_RECOVERY), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivityGoddessRecvRecoveryAward awrad failed error code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.GoddessContract().SetGoddessContractRecoveryRecv(cmsg.Id)
	smsg.Id = cmsg.Id
	smsg.Recv = c.User.GoddessContract().FlushActivityGoddessRecv()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityGoddessRecvRecoveryAward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LActivityGoddessActiveCommand struct {
	base.UserCommand
}

func (c *C2LActivityGoddessActiveCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityGoddessActiveCommand) Error(msg *cl.L2C_ActivityGoddessActive, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityGoddessActive, msg)
	return false
}

func (c *C2LActivityGoddessActiveCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityGoddessActive{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityGoddessActive Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityGoddessActive: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_ActivityGoddessActive{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_GODDESS_CONTRACT), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityGoddessActive: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	info := goxml.GetData().GoddessContractInfoM.Index(cmsg.Id)
	if info == nil {
		l4g.Errorf("user: %d C2L_ActivityGoddessActive id:%d is nil", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	dungeonId := c.User.DungeonID()
	if dungeonId < info.SaveDungeon {
		l4g.Errorf("user :%d C2L_ActivityGoddessActive id:%d save failed dungeon:%d info dungeon:%d ", c.Msg.UID, cmsg.Id, dungeonId, info.SaveDungeon)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if c.User.GoddessContract().IsActive(cmsg.Id) {
		l4g.Errorf("user:%d C2L_ActivityGoddessActive goddess:%d is active", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	c.User.GoddessContract().AppendActive(cmsg.Id)
	c.User.GoddessContract().SaveGoddess()
	smsg.Id = cmsg.Id
	smsg.ActivityState = c.User.GoddessContract().CopyActive()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityGoddessActive, smsg)
	c.User.FireCommonEvent(c.Srv.EventM(), event.AeActiveGoddessToX, 1)
	return c.ResultOK(smsg.Ret)
}
