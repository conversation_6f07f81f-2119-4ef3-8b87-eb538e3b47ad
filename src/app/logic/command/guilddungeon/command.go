package guilddungeon

import (
	"app/goxml"
	"app/logic/activity"
	aguild "app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command/base"
	"app/logic/event"
	"app/logic/helper"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonInfo), &C2LGuildDungeonInfoCommand{}, state) //1
	//进入公会副本
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonRecvChapterTaskAward), &C2LGuildDungeonRecvChapterTaskAwardCommand{}, state) //1
	//领取章节任务奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonFight), &C2LGuildDungeonFightCommand{}, state)
	//挑战关卡
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonUserDamageRank), &C2LGuildDungeonUserDamageRankCommand{}, state) //1
	//公会成员副本伤害(历史最高)排行
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonLogList), &C2LGuildDungeonLogListCommand{}, state)
	//攻打日志
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonChapterInfo), &C2LGuildDungeonChapterInfoCommand{}, state)
	//进入章节
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonRecvBossBoxAward), &C2LGuildDungeonRecvBossBoxAwardCommand{}, state)
	//领取Boss宝箱奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonRecvAllBossBoxAward), &C2LGuildDungeonRecvAllBossBoxAwardCommand{}, state)
	//一键领取Boss宝箱奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonBuyChallengeTimes), &C2LGuildDungeonBuyChallengeTimesCommand{}, state)
	//购买挑战次数
	//cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonSetMessageBoard), &C2LGuildDungeonSetMessageBoardCommand{}, state)
	////设置留言板
	//cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonGetMessageBoard), &C2LGuildDungeonGetMessageBoardCommand{}, state)
	////获取留言板
	//cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonDelMessageBoard), &C2LGuildDungeonDelMessageBoardCommand{}, state)
	////删除留言板
	//cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonRankLike), &C2LGuildDungeonRankLikeCommand{}, state)
	////公会副本点赞
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonSeasonDivisionAward), &C2LGuildDungeonSeasonDivisionAwardCommand{}, state) //1
	//公会赛季最高段位领奖
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonSetFocus), &C2LGuildDungeonSetFocusCommand{}, state) //1
	//公会副本设置集火
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonHallOfFame), &C2LGuildDungeonHallOfCommand{}, state)
	//公会副本荣耀殿堂
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonTop3Guild), &C2LGuildDungeonTop3GuildCommand{}, state)
	//获取Top3公会信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonGetStrategy), &C2LGuildDungeonGetStrategyCommand{}, state)
	//获取秘技信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonUseStrategy), &C2LGuildDungeonUseStrategyCommand{}, state)
	//使用秘技
	cmds.Register(uint32(cl.ID_MSG_C2L_GuildDungeonGetMembersFightInfo), &C2LGuildDungeonGetMembersFightInfoCommand{}, state)
}

type C2LGuildDungeonInfoCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LGuildDungeonInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonInfoCommand) Error(msg *cl.L2C_GuildDungeonInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonInfo, msg)
	return false
}

func (c *C2LGuildDungeonInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonInfo: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonInfo{
		Ret: uint32(cret.RET_OK),
	}

	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonInfo failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDungeonInfo: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonInfo not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonInfo guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonInfo, c.User.ID(), &l2c.L2C_GuildDungeonInfo{
		SignUp: cmsg.SignUp,
	})

	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonRecvChapterTaskAwardCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonRecvChapterTaskAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonRecvChapterTaskAwardCommand) Error(msg *cl.L2C_GuildDungeonRecvChapterTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonRecvChapterTaskAward, msg)
	return false
}

//nolint:funlen
func (c *C2LGuildDungeonRecvChapterTaskAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonRecvChapterTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonRecvChapterTaskAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonRecvChapterTaskAward: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonRecvChapterTaskAward{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}

	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyResetting(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward: guildDungeon resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	// 检查guildUser是否为空
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	taskNum := goxml.GetData().GuildDungeonTaskInfoM.GetTaskNum()
	if len(cmsg.Ids) == 0 || len(cmsg.Ids) > taskNum {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward request param error. cmsg.Chapter num:%d taskNum:%d",
			c.Msg.UID, len(cmsg.Ids), taskNum)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	passChapter, exist := guild.GetPassChapter()
	if !exist {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward guild dungeon with no cache", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_NO_CACHE))
	}

	allAwards := make([]*cl.Resource, 0, len(cmsg.Ids))
	reqTaskIds := make(map[uint32]struct{}, len(cmsg.Ids))
	for _, id := range cmsg.Ids {
		if _, exist := reqTaskIds[id]; exist {
			l4g.Error("user: %d C2L_GuildDungeonRecvChapterTaskAward: request param repeated. cmsg.Chapter:%+v",
				c.Msg.UID, cmsg.Ids)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		} else {
			reqTaskIds[id] = struct{}{}
		}

		if guildUser.CheckChapterAwardIsReceived(id) {
			l4g.Error("user: %d C2L_GuildDungeonRecvChapterTaskAward: chapter award received. chapter:%d",
				c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}

		info := goxml.GetData().GuildDungeonTaskInfoM.Index(id)
		if info == nil {
			l4g.Error("user: %d C2L_GuildDungeonRecvChapterTaskAward: GuildDungeonTaskInfo is nil. id:%d",
				c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if passChapter < info.Level {
			l4g.Error("user: %d C2L_GuildDungeonRecvChapterTaskAward: task not finish. dungeonChapter:%d, requestChapter:%d",
				c.Msg.UID, passChapter, info.Level)
			return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_TASK_NOT_FINISH))
		}

		if len(info.ClRes) == 0 {
			l4g.Error("user: %d C2L_GuildDungeonRecvChapterTaskAward: task awards is nil. taskId:%d",
				c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		allAwards = append(allAwards, info.ClRes...)
	}

	guildUser.AddReceivedAwardChapter(cmsg.Ids)

	retCode, retAward := c.User.Award(c.Srv, allAwards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_CHAPTER_TASK_RECEIVE), 0)

	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvChapterTaskAward award eror", c.Msg.UID)
	}

	smsg.Awards = retAward
	guildM.SaveGuildUser(guildUser)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonRecvChapterTaskAward, smsg)
	c.User.LogGuildDungeonRecvChapterTaskAward(c.Srv, cmsg.Ids)
	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonFightCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LGuildDungeonFightCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonFightCommand) Error(msg *cl.L2C_GuildDungeonFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonFight, msg)
	return false
}

//nolint:funlen
func (c *C2LGuildDungeonFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonFight Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonFight: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonFight{
		Ret:     uint32(cret.RET_OK),
		Chapter: cmsg.Chapter,
		Monster: cmsg.Monster,
		Sweep:   cmsg.Sweep,
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonFight: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_GuildDungeonFight: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyClosing(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonFight: guildDungeon resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonFight failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDungeonFight: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonFight not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonFight guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	curChapter, exist := guild.GetCurChapter()
	if !exist {
		l4g.Errorf("user :%d C2L_GuildDungeonFight guild:%d no cache", c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_NO_CACHE))
	}

	if cmsg.Chapter != curChapter {
		l4g.Errorf("user: %d C2L_GuildDungeonFight param error cmsg.Chapter:%d guild.Chapter:%d",
			c.Msg.UID, cmsg.Chapter, curChapter)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_CHAPTER_CHANGE))
	}

	chapterInfo := goxml.GetData().GuildDungeonChapterInfoM.GetChapter(cmsg.Chapter)
	if chapterInfo == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonFight chapterInfo is nil. chapterId:%d",
			c.Msg.UID, cmsg.Chapter)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildUser.CalcNewChallengeTimes(c.Srv, guildM, now)

	if !guildUser.CheckChallengeCount(1) {
		l4g.Errorf("user: %d C2L_GuildDungeonFight: challenge count not enough. challengeTimes:%d",
			c.Msg.UID, guildUser.GetChallengeTimes())
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	monsterInfo := goxml.GetData().GuildDungeonChapterInfoM.GetMonsterInfo(chapterInfo, cmsg.Monster)
	if monsterInfo == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonFight param monster error cmsg.Chapter:%d  cmsg.Monster:%d",
			c.Msg.UID, cmsg.Chapter, cmsg.Monster)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.Sweep {
		fightDamage := guildUser.GetFightBossDailyBestDamage(cmsg.Monster)
		if fightDamage == 0 {
			l4g.Errorf("user: %d C2L_GuildDungeonFight user have not fight this boss.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_SWEEP_LIMIT))
		}
	}

	guildUser.CacheClientData(cmsg.ClientData)
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonFightStart, c.User.ID(), &l2c.L2C_GuildDungeonFightStart{
		Chapter: cmsg.Chapter,
		Boss:    cmsg.Monster,
		Sweep:   cmsg.Sweep,
	}) {
		l4g.Errorf("user: %d C2L_GuildDungeonFight cross maintain", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonUserDamageRankCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonUserDamageRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonUserDamageRankCommand) Error(msg *cl.L2C_GuildDungeonUserDamageRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonUserDamageRank, msg)
	return false
}

func (c *C2LGuildDungeonUserDamageRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonUserDamageRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonUserDamageRank Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonUserDamageRank: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonUserDamageRank{
		Ret: uint32(cret.RET_OK),
	}

	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonUserDamageRank: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonUserDamageRank: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonUserDamageRank failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonUserDamageRank not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonGetMembersWeeklyDamage, c.User.ID(), &l2c.L2C_GuildDungeonGetMembersWeeklyDamage{})

	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonLogListCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonLogListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonLogListCommand) Error(msg *cl.L2C_GuildDungeonLogList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonLogList, msg)
	return false
}

func (c *C2LGuildDungeonLogListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonLogList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonLogList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonLogList: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonLogList{
		Ret: uint32(cret.RET_OK),
	}

	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonLogList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonLogList: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonLogList: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDungeonLogList: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonLogList: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonFightLogList, c.User.ID(), &l2c.L2C_GuildDungeonFightLogList{})

	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonChapterInfoCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonChapterInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonChapterInfoCommand) Error(msg *cl.L2C_GuildDungeonChapterInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonChapterInfo, msg)
	return false
}

func (c *C2LGuildDungeonChapterInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonChapterInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonChapterInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonChapterInfo: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonChapterInfo{
		Ret:     uint32(cret.RET_OK),
		Chapter: cmsg.Chapter,
		Enter:   cmsg.Enter,
	}

	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonChapterInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonChapterInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyResetting(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonChapterInfo: guild resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonChapterInfo failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonChapterInfo not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	chapter, exist := guild.GetCurChapter()
	if !exist {
		l4g.Errorf("user:%d C2L_GuildDungeonChapterInfo no cache", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_NO_CACHE))
	}

	if cmsg.Chapter > chapter {
		l4g.Errorf("user:%d C2L_GuildDungeonChapterInfo Cache chapter:%d req chapter:%d", c.Msg.UID, chapter, cmsg.Chapter)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonChapterInfo, c.User.ID(), &l2c.L2C_GuildDungeonChapterInfo{
		Chapter: cmsg.Chapter,
		Enter:   cmsg.Enter,
	})

	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonRecvBossBoxAwardCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonRecvBossBoxAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonRecvBossBoxAwardCommand) Error(msg *cl.L2C_GuildDungeonRecvBossBoxAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonRecvBossBoxAward, msg)
	return false
}

func (c *C2LGuildDungeonRecvBossBoxAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonRecvBossBoxAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonRecvBossBoxAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonRecvBossBoxAward: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonRecvBossBoxAward{
		Ret:     uint32(cret.RET_OK),
		Chapter: cmsg.Chapter,
		Box:     cmsg.Box,
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}
	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyResetting(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward guild resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	chapterInfo := goxml.GetData().GuildDungeonChapterInfoM.GetChapter(cmsg.Chapter)
	if chapterInfo == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward chapter is error. cmsg.Chapter:%d",
			c.Msg.UID, cmsg.Chapter)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	passChapter, exist := guild.GetPassChapter()
	if !exist {
		l4g.Errorf("user :%d C2L_GuildDungeonRecvBossBoxAward guild:%d no cache", c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_NO_CACHE))
	}

	if cmsg.Chapter > passChapter {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward param error cmsg.Chapter:%d guild.passChapter:%d",
			c.Msg.UID, cmsg.Chapter, passChapter)
		return c.Error(smsg, uint32(cret.RET_GUID_DUNGEON_CHAPTER_NOT_FINISH))
	}

	if guildUser.IsBossBoxReceived(cmsg.Chapter) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward boss received. cmsg.Chapter:%d",
			c.Msg.UID, cmsg.Chapter)
		return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
	}

	guildLevel := guild.GetLevel()
	levelInfo := goxml.GetData().GuildLevelInfoM.Index(guildLevel)
	if levelInfo == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward levelInfo not exist. level:%d", c.Msg.UID, guildLevel)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if cmsg.Box <= 0 || cmsg.Box > levelInfo.Member {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvBossBoxAward cmsg.Box error. box:%d", c.Msg.UID, cmsg.Box)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	crossMsg := &l2c.L2C_GuildDungeonRecvBox{
		Chapter: cmsg.Chapter,
		Box:     cmsg.Box,
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonRecvBox, c.User.ID(), crossMsg)
	return c.ResultOK(uint32(cret.RET_OK))
}

type C2LGuildDungeonBuyChallengeTimesCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonBuyChallengeTimesCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonBuyChallengeTimesCommand) Error(msg *cl.L2C_GuildDungeonBuyChallengeTimes, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonBuyChallengeTimes, msg)
	return false
}

//nolint:funlen
func (c *C2LGuildDungeonBuyChallengeTimesCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonBuyChallengeTimes{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonBuyChallengeTimes Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonBuyChallengeTimes: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonBuyChallengeTimes{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cmsg.Num < 1 {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes: param num is 0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	division, exist := guild.GetCurDivision()
	if !exist {
		l4g.Errorf("user :%d C2L_GuildDungeonBuyChallengeTimes guild:%d no cache", c.Msg.UID, guild.ID())
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_NO_CACHE))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	buyCount := guildUser.GetBuyCount()

	divisionInfo := goxml.GetData().GuildDungeonDivisionInoM.Index(division)
	if divisionInfo == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes get division:%d info failed ", c.Msg.UID, division)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if buyCount+cmsg.Num > goxml.GetData().GuildConfigInfoM.GetBuyLimit()+divisionInfo.BuyLimit {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes buy limit. cmsg.Num:%d buyCount:%d limitCount:%d",
			c.Msg.UID, cmsg.Num, buyCount, goxml.GetData().GuildConfigInfoM.GetBuyLimit())
		return c.Error(smsg, uint32(cret.RET_BUY_COUNT_LIMIT))
	}

	now := time.Now().Unix()

	currentTimes := guildUser.CalcNewChallengeTimes(c.Srv, guildM, now)
	limitCount := goxml.GetData().GuildConfigInfoM.GetDungeonChallengeMaxCount()
	if currentTimes+cmsg.Num > limitCount {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes buy limit. cmsg.Num:%d currentTimes:%d limitCount:%d",
			c.Msg.UID, cmsg.Num, currentTimes, limitCount)
		return c.Error(smsg, uint32(cret.RET_BUY_COUNT_LIMIT))
	}

	ok, price := goxml.GetData().BuyPriceInfoM.GetPrice(goxml.GetData().GuildConfigInfoM.GetBuyCountGroup(), buyCount, cmsg.Num)
	if !ok || price == 0 {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes: GetPrice failed. purchasedNum:%d, num:%d",
			c.User.ID(), buyCount, cmsg.Num)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	costs := []*cl.Resource{{
		Type:  uint32(common.RESOURCE_DIAMOND),
		Value: 0,
		Count: price,
	}}

	cRet := c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_BUY_CHALLENGE_TIMES), 0)
	if cRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildDungeonBuyChallengeTimes: consume failed, ret:%d", c.Msg.UID, cRet)
		return c.Error(smsg, cRet)
	}

	guildUser.AddBuyCount(cmsg.Num)
	guildUser.AddChallengeTimes(cmsg.Num)
	guildM.SaveGuildUser(guildUser)

	smsg.ChallengeTimes = guildUser.GetChallengeTimes()
	smsg.LastRecoverTm = guildUser.GetLastRecoverTm()
	smsg.BuyCount = guildUser.GetBuyCount()
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGuildDungeonBuyChallengeTimesToX, uint64(cmsg.Num)) // 公会副本

	c.User.LogGuildDungeonBuyChallengeTimes(c.Srv, currentTimes, buyCount, guildUser.GetBuyCount(), guildUser.GetChallengeTimes())
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonBuyChallengeTimes, smsg)

	if guildM.IsCrossConnected() {
		if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonBuyChallengeTimes, c.User.ID(), &l2c.L2C_GuildDungeonBuyChallengeTimes{
			ButCount: cmsg.Num,
		}) {
			l4g.Errorf("user:%d C2L_GuildDungeonBuyChallengeTimes: send cross error.", c.User.ID())
		}
	}
	return c.ResultOK(smsg.Ret)
}

//type C2LGuildDungeonSetMessageBoardCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGuildDungeonSetMessageBoardCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGuildDungeonSetMessageBoardCommand) Error(msg *cl.L2C_GuildDungeonSetMessageBoard, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonSetMessageBoard, msg)
//	return false
//}
//
//func (c *C2LGuildDungeonSetMessageBoardCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GuildDungeonSetMessageBoard{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GuildDungeonSetMessageBoard Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GuildDungeonSetMessageBoard: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GuildDungeonSetMessageBoard{
//		Ret:        uint32(cret.RET_OK),
//		NewMessage: cmsg.NewMessage,
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard: function not open", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard failed, guildManager not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	dungeon := guildM.GetGuildDungeon(c.User.ID())
//	if dungeon == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard not in guild", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	//if !guild.Loaded() {
//	//	l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard guild %d loading", c.Msg.UID, guild.ID())
//	//	return c.Error(smsg, uint32(cret.RET_GUILD_LOADING))
//	//}
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard guildUser is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	guild := guildM.GetGuildByUser(c.User.ID(), c.Srv)
//	if guild == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard guild is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	if !guild.IsGuildManager(c.User.ID()) {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard: not manager. guild:%d", c.Msg.UID, guild.ID())
//		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
//	}
//
//	retn := cguild.CheckString(cmsg.NewMessage, c.Msg.UID, int( goxml.GetData().GuildConfigInfoM.GetMessageBoardLengthMax()))
//	if retn != uint32(cret.RET_OK) {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard: not leader. guildId:%d", c.Msg.UID, guild.ID())
//		return c.Error(smsg, retn)
//	}
//
//	messageBoard := guildM.GetGuildDungeonMessageBoard(c.User.ID())
//
//	if messageBoard == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard: guild is nil. guildId:%d", c.Msg.UID, guild.ID())
//		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
//	}
//
//	logsM := guildM.GetMessageBoardLogM().GetOwnerLogs(messageBoard)
//	logsM.AddLog(c.Srv, aguild.NewGuildDungeonMessageBoardLog(c.Srv.CreateUniqueID(), cmsg.NewMessage, c.User.Name()))
//	guildM.GetMessageBoardLogM().SetChange(logsM)
//
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonSetMessageBoard, smsg)
//
//	return c.ResultOK(smsg.Ret)
//}
//
//type C2LGuildDungeonGetMessageBoardCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGuildDungeonGetMessageBoardCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGuildDungeonGetMessageBoardCommand) Error(msg *cl.L2C_GuildDungeonGetMessageBoard, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonGetMessageBoard, msg)
//	return false
//}
//
//func (c *C2LGuildDungeonGetMessageBoardCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GuildDungeonGetMessageBoard{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GuildDungeonGetMessageBoard Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GuildDungeonGetMessageBoard: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GuildDungeonGetMessageBoard{
//		Ret: uint32(cret.RET_OK),
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
//		l4g.Errorf("user: %d C2L_GuildDungeonGetMessageBoard: function not open", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GuildDungeonGetMessageBoard failed, guildManager not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	dungeon := guildM.GetGuildDungeon(c.User.ID())
//	if dungeon == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonGetMessageBoard not in guild", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	//if !guild.Loaded() {
//	//	l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard guild %d loading", c.Msg.UID, guild.ID())
//	//	return c.Error(smsg, uint32(cret.RET_GUILD_LOADING))
//	//}
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonGetMessageBoard guildUser is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	messageBoard := guildM.GetGuildDungeonMessageBoard(c.User.ID())
//
//	if messageBoard == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonGetMessageBoard: guild is nil. guildId:%d", c.Msg.UID, dungeon.ID())
//		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
//	}
//
//	logsM := guildM.GetMessageBoardLogM().GetOwnerLogs(messageBoard)
//	loaded := logsM.CheckLoaded()
//	if loaded {
//		logs := logsM.GetAll()
//		cLogs := make([]*cl.GuildDungeonMessageBoardLog, 0, len(logs))
//		for _, v := range logs {
//			cLogs = append(cLogs, v.(*aguild.MessageBoardLog).Flush())
//		}
//		smsg.AllMessages = cLogs
//		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonGetMessageBoard, smsg)
//	} else {
//		c.AsyncGetMessageBoardLog(dungeon.ID(), &AsyncGuildDungeonGetMessageBoardReq{smsg})
//	}
//
//	return c.ResultOK(smsg.Ret)
//}
//
//type AsyncGuildDungeonGetMessageBoardReq struct {
//	smsg *cl.L2C_GuildDungeonGetMessageBoard
//}
//
//func (ar *AsyncGuildDungeonGetMessageBoardReq) Resp(srv command.Servicer, args *character.Args,
//	retCode uint32, dbData interface{}) bool {
//	smsg := ar.smsg
//	if retCode != uint32(r2l.RET_OK) {
//		l4g.Errorf("user: %d L2C_GuildDungeonGetMessageBoard: get data error:%d", args.UID, retCode)
//		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
//		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonGetMessageBoard, smsg)
//		return false
//	}
//
//	list := dbData.(map[uint32]*cl.GuildDungeonMessageBoardLog)
//	l4g.Debugf("guildDungeon get messageBoard log list %+v", list)
//
//	guildM := srv.GetActivity(activity.Guild).(*aguild.Manager)
//
//	messageBoard := guildM.GetGuildDungeonMessageBoard(args.UID)
//	if messageBoard == nil {
//		l4g.Errorf("user: %d L2C_GuildDungeonGetMessageBoard: get data error:%d", args.UID, retCode)
//		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
//		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonGetMessageBoard, smsg)
//		return false
//	}
//	logsM := guildM.GetMessageBoardLogM().GetOwnerLogs(messageBoard)
//	for _, data := range list {
//		logsM.LoadFromDB(aguild.NewMessageBoardLogFromDB(data))
//	}
//	logsM.LoadFinish()
//	logs := logsM.GetAll()
//
//	cLogs := make([]*cl.GuildDungeonMessageBoardLog, 0, len(logs))
//	for _, v := range logs {
//		cLogs = append(cLogs, v.(*aguild.MessageBoardLog).Flush())
//	}
//	smsg.AllMessages = cLogs
//	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonGetMessageBoard, smsg)
//	return true
//}
//
//type C2LGuildDungeonDelMessageBoardCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGuildDungeonDelMessageBoardCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGuildDungeonDelMessageBoardCommand) Error(msg *cl.L2C_GuildDungeonDelMessageBoard, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonDelMessageBoard, msg)
//	return false
//}
//
//func (c *C2LGuildDungeonDelMessageBoardCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GuildDungeonDelMessageBoard{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GuildDungeonDelMessageBoard Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GuildDungeonDelMessageBoard: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GuildDungeonDelMessageBoard{
//		Ret: uint32(cret.RET_OK),
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard: function not open", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard failed, guildManager not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	dungeon := guildM.GetGuildDungeon(c.User.ID())
//	if dungeon == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard not in guild", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//	//if !guild.Loaded() {
//	//	l4g.Errorf("user: %d C2L_GuildDungeonSetMessageBoard guild %d loading", c.Msg.UID, guild.ID())
//	//	return c.Error(smsg, uint32(cret.RET_GUILD_LOADING))
//	//}
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard guildUser is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//
//	guild := guildM.GetGuildByUser(c.User.ID(), c.Srv)
//	if guild == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard guild is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	if !guild.IsGuildManager(c.User.ID()) {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard: not manager. guild:%d", c.Msg.UID, guild.ID())
//		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
//	}
//
//	messageBoard := guildM.GetGuildDungeonMessageBoard(c.User.ID())
//
//	if messageBoard == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard: guild is nil. guildId:%d", c.Msg.UID, guild.ID())
//		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
//	}
//
//	logsM := guildM.GetMessageBoardLogM().GetOwnerLogs(messageBoard)
//	loaded := logsM.CheckLoaded()
//	if loaded {
//		logsM.DelLog([]uint64{cmsg.UniqID})
//		guildM.GetMessageBoardLogM().SetChange(logsM)
//		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonDelMessageBoard, smsg)
//	} else {
//		l4g.Errorf("user: %d C2L_GuildDungeonDelMessageBoard log not loaded", c.Msg.UID)
//		c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
//	}
//
//	return c.ResultOK(smsg.Ret)
//}
//
//type C2LGuildDungeonRankLikeCommand struct {
//	base.UserCommand
//}
//
//func (c *C2LGuildDungeonRankLikeCommand) ResultOK(retCode uint32) bool {
//	return retCode == uint32(cret.RET_OK)
//}
//
//func (c *C2LGuildDungeonRankLikeCommand) Error(msg *cl.L2C_GuildDungeonRankLike, retCode uint32) bool {
//	msg.Ret = retCode
//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonRankLike, msg)
//	return false
//}
//
//func (c *C2LGuildDungeonRankLikeCommand) Execute(ctx context.Context) bool {
//	cmsg := &cl.C2L_GuildDungeonRankLike{}
//	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
//		l4g.Errorf("C2L_GuildDungeonRankLike Unmarshal error: %d %s", c.Msg.UID, err)
//		return false
//	}
//	c.Trace(cmsg)
//
//	l4g.Debugf("user: %d C2L_GuildDungeonRankLike: %s", c.Msg.UID, cmsg)
//	//edit here
//	smsg := &cl.L2C_GuildDungeonRankLike{
//		Ret: uint32(cret.RET_OK),
//		Gid: cmsg.Gid,
//	}
//	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike: function not open", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
//	}
//
//	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
//	if !ok {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike failed, guildManager not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	dungeon := guildM.GetGuildDungeonById(cmsg.Gid)
//	if dungeon == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike guild not exist", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
//	}
//
//	guildUser := guildM.GetGuildUser(c.User.ID())
//	if guildUser == nil {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike guildUser is nil", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_ERROR))
//	}
//	if guildUser.GuildID() == 0 {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike user not in guild.", c.Msg.UID)
//		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
//	}
//
//	rank := c.Srv.CommonRankM().GetRank(goxml.GuildDungeonChapterRankId, cmsg.Gid)
//	if rank == 0 {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike: not in rank list. gid:%d", c.Msg.UID, cmsg.Gid)
//		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
//	}
//
//	likeCount := guildUser.GetLikeCount()
//
//	if likeCount >=  goxml.GetData().GuildConfigInfoM.GetGuildDungeonLikeMaxCount() {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike likeCount limit. likeCount:%d", c.Msg.UID, likeCount)
//		return c.Error(smsg, uint32(cret.RET_COUNT_NOT_ENOUGH))
//	}
//
//	awards :=  goxml.GetData().GuildConfigInfoM.GetGuildDungeonChapterRankLikeAwards()
//
//	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
//		uint32(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_CHAPTER_RANK_LIKE), 0)
//	if smsg.Ret != uint32(cret.RET_OK) {
//		l4g.Errorf("user: %d C2L_GuildDungeonRankLike award error", c.Msg.UID)
//		return c.Error(smsg, smsg.Ret)
//	}
//
//	guildUser.AddLikeCount(1)
//	dungeon.AddLikedCount(1)
//	guildM.SaveGuildUser(guildUser)
//	guildM.SaveGuildDungeon(dungeon)
//	c.User.LogGuildDungeonChapterRankLike(c.Srv, cmsg.Gid)
//	return c.ResultOK(smsg.Ret)
//}

type C2LGuildDungeonSeasonDivisionAwardCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonSeasonDivisionAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonSeasonDivisionAwardCommand) Error(msg *cl.L2C_GuildDungeonSeasonDivisionAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonSeasonDivisionAward, msg)
	return false
}

func (c *C2LGuildDungeonSeasonDivisionAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonSeasonDivisionAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonSeasonDivisionAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonSeasonDivisionAward: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonSeasonDivisionAward{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyResetting(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward: guild resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, get guild failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, get guildUser failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildTopDivision, exist := guild.GetSeasonTopDivision()
	if !exist {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, get season top division failed no cache", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_NO_CACHE))
	}

	cmsgDivisionLen := len(cmsg.Division)
	if cmsgDivisionLen == 0 || cmsgDivisionLen > goxml.GetData().GuildDungeonLevelUpRewardInfoM.DataLens() {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, cmsg Divisions length:%d is error", c.Msg.UID, cmsgDivisionLen)
		return c.Error(smsg, uint32(cret.RET_PARAM_LENGTH_LIMIT))
	}

	repeatedCheck := make(map[uint32]struct{})
	totalReward := make([]*cl.Resource, 0, len(cmsg.Division)*2)
	for _, d := range cmsg.Division { //nolint:varnamelen
		_, exist := repeatedCheck[d]
		if exist {
			l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, division is repeated:%d", c.Msg.UID, d)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		} else {
			repeatedCheck[d] = struct{}{}
		}

		if d > guildTopDivision {
			l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, division:%d more than top division:%d", c.Msg.UID, d, guildTopDivision)
			return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_TOP_DIVISION_NOT_FINISH))
		}

		if guildUser.IsSeasonRecvFirstDivisionReward(d) {
			l4g.Errorf("user: %d C2L_GuildDungeonSeasonDivisionAward failed, division is award:%d", c.Msg.UID, d)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}

		levelUpInfo := goxml.GetData().GuildDungeonLevelUpRewardInfoM.Index(d)
		if levelUpInfo == nil {
			l4g.Errorf("user:%d C2L_GuildDungeonSeasonDivisionAward find division:%d reward info failed ", c.Msg.UID, d)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		totalReward = append(totalReward, levelUpInfo.ClRes...)
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalReward, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GUILD_DUNGEON_SEASON_TOP_DIVISION), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_GuildDungeonSeasonDivisionAward award failed errorCode:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	guildUser.AppedSeasonRecvFirstDivision(cmsg.Division)
	guildM.SaveGuildUser(guildUser)
	smsg.Division = cmsg.Division
	smsg.RecvDivison = guildUser.GetSeasonRecvFirstDivision()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonSeasonDivisionAward, smsg)
	c.User.LogicGuildDungeonRecvTopDivision(c.Srv, cmsg.Division)
	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonSetFocusCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonSetFocusCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonSetFocusCommand) Error(msg *cl.L2C_GuildDungeonSetFocus, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonSetFocus, msg)
	return false
}

func (c *C2LGuildDungeonSetFocusCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonSetFocus{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonSeasonSetFocus Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonSeasonSetFocus: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonSetFocus{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonSetFocus: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonSetFocus: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonSetFocus failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonSetFocus: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonSetFocus failed, get guild failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonSetFocus failed, get guildUser failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if guildUser.Grade() != character.GuildGradeLeader {
		l4g.Errorf("user: %d C2L_GuildDungeonSeasonSetFocus failed, is grade member", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonSetFocus, c.User.ID(), &l2c.L2C_GuildDungeonSetFocus{
		Chapter:  cmsg.Chapter,
		FocusIds: cmsg.FocusIds,
	})

	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonHallOfCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonHallOfCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonHallOfCommand) Error(msg *cl.L2C_GuildDungeonHallOfFame, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonHallOfFame, msg)
	return false
}

func (c *C2LGuildDungeonHallOfCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonHallOfFame{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonHallOfFame Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonHallOfFame: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonHallOfFame{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d L2C_GuildDungeonHallOfFame: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d L2C_GuildDungeonHallOfFame: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d L2C_GuildDungeonHallOfFame failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d L2C_GuildDungeonHallOfFame: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if cmsg.SeasonId == 0 {
		l4g.Errorf("user: %d L2C_GuildDungeonHallOfFame: season ID:%d is error", c.Msg.UID, cmsg.SeasonId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonGetHallOfFame, c.User.ID(), &l2c.L2C_GuildDungeonGetHallOfFame{
		SeasonId: cmsg.SeasonId,
	})

	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonTop3GuildCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonTop3GuildCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonTop3GuildCommand) Error(msg *cl.L2C_GuildDungeonTop3Guild, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonTop3Guild, msg)
	return false
}

func (c *C2LGuildDungeonTop3GuildCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonTop3Guild{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonTop3Guild Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonTop3Guild: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonTop3Guild{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonTop3Guild: function not open", c.Msg.UID)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonTop3Guild, smsg)
		return false
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonTop3Guild: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonTop3Guild failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !goxml.GuildDungeonResetting(goxml.GetData(), time.Now().Unix()) { // 结算期不执行
		smsg.LastSeasonTop_3, smsg.LastSeasonId = guildM.GetLastSeasonTop3()
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonTop3Guild, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonGetStrategyCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonGetStrategyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonGetStrategyCommand) Error(msg *cl.L2C_GuildDungeonGetStrategy, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonGetStrategy, msg)
	return false
}

func (c *C2LGuildDungeonGetStrategyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonGetStrategy{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonGetStrategy Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonGetStrategy: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonGetStrategy{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyResetting(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy: guildDungeon resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy failed. user not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	//if !guildUser.IsGuildManager() {
	//	l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy: not manager.", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	//}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDungeonGetStrategy failed,  cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonGetStrategy, c.User.ID(), &l2c.L2C_GuildDungeonGetStrategy{}) {
		l4g.Errorf("[Guild] user:%d C2L_GuildDungeonGetStrategy: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonUseStrategyCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonUseStrategyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonUseStrategyCommand) Error(msg *cl.L2C_GuildDungeonUseStrategy, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonUseStrategy, msg)
	return false
}

func (c *C2LGuildDungeonUseStrategyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonUseStrategy{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonUseStrategy Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonUseStrategy: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonUseStrategy{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyResetting(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy: guildDungeon resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if cmsg.Gid == 0 || cmsg.UseCount == 0 || cmsg.StrategyId == 0 {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy failed, param is 0. cmsg:%+v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy failed. user not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !guildUser.IsGuildManager() {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy: not manager.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_GUILD_LEADER_OR_DEPUTY))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDungeonUseStrategy failed,  cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonUseStrategy, c.User.ID(), &l2c.L2C_GuildDungeonUseStrategy{
		StrategyId: cmsg.StrategyId,
		Gid:        cmsg.Gid,
		UseCount:   cmsg.UseCount,
	}) {
		l4g.Errorf("[Guild] user:%d C2L_GuildDungeonUseStrategy: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return c.ResultOK(smsg.Ret)
}

type C2LGuildDungeonGetMembersFightInfoCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonGetMembersFightInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonGetMembersFightInfoCommand) Error(msg *cl.L2C_GuildDungeonGetMembersFightInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonGetMembersFightInfo, msg)
	return false
}

func (c *C2LGuildDungeonGetMembersFightInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonGetMembersFightInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonGetMembersFightInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonGetMembersFightInfo: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonGetMembersFightInfo{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonGetMembersFightInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	//检查公会副本是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonGetMembersFightInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonGetMembersFightInfo: guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonGetMembersFightInfo: not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildDungeonGetMembersFightInfo: cross not connect", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonGetMembersFightInfo, c.User.ID(), &l2c.L2C_GuildDungeonInfo{}) {
		l4g.Errorf("user:%d C2L_GuildDungeonGetMembersFightInfo: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LGuildDungeonRecvAllBossBoxAwardCommand struct {
	base.UserCommand
}

func (c *C2LGuildDungeonRecvAllBossBoxAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGuildDungeonRecvAllBossBoxAwardCommand) Error(msg *cl.L2C_GuildDungeonRecvAllBossBoxAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDungeonRecvAllBossBoxAward, msg)
	return false
}

func (c *C2LGuildDungeonRecvAllBossBoxAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GuildDungeonRecvAllBossBoxAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDungeonRecvAllBossBoxAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: %s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_GuildDungeonRecvAllBossBoxAward{
		Ret: uint32(cret.RET_OK),
	}
	if goxml.GetData().GuildConfigInfoM.IsGuildDungeonSuspended(time.Now().Unix()) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	//检查公会副本是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	//检查玩家等级是否达到一键领取要求
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_GUILD_DUNGEON_BOX_ALL_RECV), c.Srv) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: insufficient user level %d", c.Msg.UID, c.User.Level())
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: not in guild", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}
	now := time.Now().Unix()
	if goxml.GetGuildDungeonWeeklyResetting(now) {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: guild resetting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_RESETTING))
	}

	//判断是否有剩余宝箱可领取
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: get guild user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//判断是否有剩余宝箱可领取
	recvBoxes := make(map[uint32]struct{})
	for _, v := range guildUser.GetReceivedChapterBoss() {
		recvBoxes[v] = struct{}{}
	}
	passChapter, exist := guild.GetPassChapter()
	if !exist {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: guild dungeon with no cache", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_DUNGEON_NO_CACHE))
	}
	var canRecvChapters []uint32
	for chapter := uint32(1); chapter <= passChapter; chapter++ {
		if _, exist := recvBoxes[chapter]; !exist {
			canRecvChapters = append(canRecvChapters, chapter)
		}
	}
	if len(canRecvChapters) == 0 {
		l4g.Errorf("user: %d C2L_GuildDungeonRecvAllBossBoxAward: no box can recv", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	crossMsg := &l2c.L2C_GuildDungeonRecvAllBox{
		OpChapters: canRecvChapters,
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildDungeonRecvAllBox, c.User.ID(), crossMsg)
	return c.ResultOK(uint32(cret.RET_OK))
}
