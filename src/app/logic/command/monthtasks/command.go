package monthtasks

import (
	"app/goxml"
	"app/logic/activity"
	amonthtasks "app/logic/activity/monthtasksmessages"
	"app/logic/character"
	"app/logic/event"
	"app/logic/helper"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	"app/logic/command/base"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_MonthTasksGetData), &C2LMonthTasksGetDataCommand{}, state)       //  获取全民无双信息
	cmds.Register(uint32(cl.ID_MSG_C2L_MonthTasksRecvAwards), &C2LMonthTasksRecvAwardsCommand{}, state) //  领取全民无双奖励
}

type C2LMonthTasksGetDataCommand struct {
	base.UserCommand
}

func (c *C2LMonthTasksGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LMonthTasksGetDataCommand) Error(msg *cl.L2C_MonthTasksGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MonthTasksGetData, msg)
	return false
}

func (c *C2LMonthTasksGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_MonthTasksGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_MonthTasksGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_MonthTasksGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_MonthTasksGetData{
		Ret: uint32(cret.RET_OK),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_TASKS)) {
	//	l4g.Errorf("user: %d C2L_MonthTasksGetData: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}

	monthTasksM := c.User.MonthTasks()

	var activityIds []uint32
	smsg.Data, activityIds = monthTasksM.Flush()

	monthTasksMessageM, ok := c.Srv.GetActivity(activity.MonthTasksMsg).(*amonthtasks.Manager)
	if ok {
		smsg.Messages = monthTasksMessageM.FlushAllMessages(activityIds)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MonthTasksGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LMonthTasksRecvAwardsCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LMonthTasksRecvAwardsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LMonthTasksRecvAwardsCommand) Error(msg *cl.L2C_MonthTasksRecvAwards, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MonthTasksRecvAwards, msg)
	return false
}

//nolint:funlen
func (c *C2LMonthTasksRecvAwardsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_MonthTasksRecvAwards{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_MonthTasksRecvAwards Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_MonthTasksRecvAwards: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_MonthTasksRecvAwards{
		Ret:            uint32(cret.RET_OK),
		Id:             cmsg.Id,
		TaskIds:        cmsg.TaskIds,
		RecvDailyAward: cmsg.RecvDailyAward,
	}

	if len(cmsg.TaskIds) > goxml.GetData().ActivityMonthTasksInfoM.TaskNum() {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: len(ids):%d too large", c.Msg.UID, len(cmsg.TaskIds))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 领取的参数为空
	if len(cmsg.TaskIds) == 0 && !cmsg.RecvDailyAward {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: len(ids):%d is 0 and recvDaily is false",
			c.Msg.UID, len(cmsg.TaskIds))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	monthTasksInfo := goxml.GetData().ActivityMonthInfoM.Index(cmsg.Id)
	if monthTasksInfo == nil {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: monthTasksInfo is nil. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	startServiceDay := helper.DaysAfterStartOfService(c.Srv.StartServiceTm(), time.Now().Unix())

	if smsg.Ret = c.isOpen(monthTasksInfo, startServiceDay); smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: monthTasks not open. ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	var numSummary *cl.NumInfo
	if cmsg.RecvDailyAward {
		if startServiceDay >= monthTasksInfo.DailyRewardEndDay {
			l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: daily reward is end. startServiceDay:%d dailyRewardEndDay:%d",
				c.Msg.UID, startServiceDay, monthTasksInfo.DailyRewardEndDay)
			return c.Error(smsg, uint32(cret.RET_MONTH_TASKS_DAILY_REWARD_END))
		}
		smsg.Ret, _, numSummary = c.User.CheckNumByType(uint32(common.PURCHASEID_MONTH_TASKS_DAILY_AWARD_RECV_COUNT), 1, c.Srv)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: no extend time chance today", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}
	}

	monthTasksM := c.User.MonthTasks()
	monthTasks := monthTasksM.GetActivityDataById(cmsg.Id)
	if len(cmsg.TaskIds) > 0 && (monthTasks == nil || monthTasks.Finish) {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: monthTaskData is nil. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	addPoints := uint32(0)
	allAwards := make([]*cl.Resource, 0, len(cmsg.TaskIds)*goxml.GetData().ActivityMonthTasksInfoM.GetTaskAwardsNum()+1)
	uniq := make(map[uint32]struct{})
	for _, taskID := range cmsg.TaskIds {
		if _, exist := uniq[taskID]; exist {
			l4g.Errorf("user:%d C2L_MonthTasksRecvAwards: id repeated. id:%d", c.Msg.UID, taskID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[taskID] = struct{}{}
		taskInfo := goxml.GetData().ActivityMonthTasksInfoM.Index(taskID)
		if taskInfo == nil {
			l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: taskInfo not exist. taskID:%d", c.Msg.UID, taskID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		// 检查是否可以领奖
		if smsg.Ret = c.checkIsCanRecvAwards(monthTasks, taskInfo); smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: task can not recv. taskID:%d, ret:%d",
				c.Msg.UID, taskID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}

		timeCanReplace := startServiceDay >= monthTasksInfo.RewardChangeDay

		awardFixed := false
		if taskInfo.Replace {
			awardFixed = monthTasks.IsAwardBeFixed(taskID)
		}

		// 添加奖励
		allAwards = append(allAwards, taskInfo.GetTaskAwards(awardFixed, timeCanReplace)...)
		addPoints += taskInfo.GetPoints
	}

	if cmsg.RecvDailyAward {
		allAwards = append(allAwards, monthTasksInfo.GetDailyAwards()...)
		c.User.AddNumByType(uint32(common.PURCHASEID_MONTH_TASKS_DAILY_AWARD_RECV_COUNT), numSummary)
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, allAwards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_MONTH_TASKS_RECV_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: recvAwards error. ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	beforePoint := c.getPoints(monthTasks)
	currentPoint := beforePoint + addPoints
	if len(cmsg.TaskIds) > 0 {
		if addPoints > 0 {
			c.User.FireCommonEvent(c.Srv.EventM(), event.AeMonthTasksPoints, uint64(addPoints), cmsg.Id)
			c.handlePointMsg(cmsg.Id, currentPoint, beforePoint)
		}
		monthTasks.SetTasksAwarded(cmsg.TaskIds)
		smsg.Finish = monthTasks.Finish
		monthTasksM.Save()
	}

	c.User.LogMonthTasksRecvAwards(c.Srv, cmsg, currentPoint)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MonthTasksRecvAwards, smsg)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LMonthTasksRecvAwardsCommand) isOpen(monthTasksInfo *goxml.ActivityMonthInfo, startServiceDay uint32) uint32 {
	if !c.User.IsFunctionOpen(monthTasksInfo.GetFuncID(), c.Srv) {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: function not open", c.Msg.UID)
		return uint32(cret.RET_FUNCTION_NOT_OPEN)
	}

	if startServiceDay < monthTasksInfo.GetOpenDay() {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: monthTasks not open. id:%d", c.Msg.UID, monthTasksInfo.Id)
		return uint32(cret.RET_FUNCTION_NOT_OPEN)
	}

	return uint32(cret.RET_OK)
}

func (c *C2LMonthTasksRecvAwardsCommand) checkIsCanRecvAwards(monthTasks *character.MonthTasks,
	taskInfo *goxml.ActivityMonthTasksInfoExt) uint32 {

	// 检查所属页签是否解锁
	if !goxml.GetData().ActivityMonthGroupInfoM.CheckGroupOpen(taskInfo.Group, c.User.Level(),
		c.User.Dungeon().GetDungeonID()) {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: group not open. group:%d", c.Msg.UID, taskInfo.TaskID)
		return uint32(cret.RET_MONTH_TASKS_GROUP_NOT_OPEN)
	}

	// 检查是否已领奖
	if monthTasks.CheckIsRepeatReceive(taskInfo.ActivityID, taskInfo.TaskID) {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: repeat receive. taskID:%d", c.Msg.UID, taskInfo.TaskID)
		return uint32(cret.RET_REPEATED_RECEIVE_AWARD)
	}

	taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TaskTypeId)
	if taskTypeInfo == nil {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: taskTypeInfo not exist. taskID:%d", c.Msg.UID, taskInfo.TaskID)
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	// 检查任务是否完成
	var pro *cl.TaskTypeProgress
	if taskTypeInfo.IsClient == goxml.ClientHandleTask {
		pro = c.User.CalcTaskProgress(taskTypeInfo)
	} else {
		pro = monthTasks.GetOneTypeProgress(taskTypeInfo.Id)
	}
	if !c.User.CheckTaskFinish(pro, taskInfo.TaskTypeId, uint64(taskInfo.Value)) {
		l4g.Errorf("user: %d C2L_MonthTasksRecvAwards: task not finish. taskID:%d", c.Msg.UID, taskInfo.TaskID)
		return uint32(cret.RET_MONTH_TASKS_TASK_NOT_FINISH)
	}

	return uint32(cret.RET_OK)
}

func (c *C2LMonthTasksRecvAwardsCommand) handlePointMsg(id, currentPoints, beforePoint uint32) {
	finishPoint := goxml.GetData().ChatConfigInfoM.GetMonthTasksPoints(currentPoints, beforePoint)
	if finishPoint == 0 {
		return
	}
	finishPointS := strconv.FormatUint(uint64(finishPoint), 10)
	chatMessage := character.NewMsg(character.Id(c.Srv), character.Head(c.User.NewChatUserHead(c.Srv.GetLogicGuild(c.User.ID()))),
		character.Type(goxml.ChatTypeMonthTasksPoints), character.Params([]string{finishPointS}...))
	character.ChatSendMsgToPlatform(c.Srv, c.User.ID(), chatMessage)

	monthTasksM, ok := c.Srv.GetActivity(activity.MonthTasksMsg).(*amonthtasks.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildList failed, guild manager not exist", c.Msg.UID)
		return
	}

	message := &cl.Message{
		Id:     c.Srv.CreateUserLogID(),
		User:   c.User.NewUserChatHead(),
		Type:   goxml.ChatTypeMonthTasksPoints,
		Params: []string{finishPointS},
	}

	monthTasksM.AddMessage(id, message)
}

func (c *C2LMonthTasksRecvAwardsCommand) getPoints(monthTasks *character.MonthTasks) uint32 {
	if monthTasks == nil {
		return 0
	}
	return monthTasks.GetPoints()
}
