package carnival

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/helper"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"

	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_CarnivalGetData), &C2LCarnivalGetDataCommand{}, state)           //获取嘉年华信息
	cmds.Register(uint32(cl.ID_MSG_C2L_CarnivalReceiveAward), &C2LCarnivalReceiveAwardCommand{}, state) //领取嘉年华奖励
}

type C2LCarnivalGetDataCommand struct {
	base.UserCommand
}

func (c *C2LCarnivalGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCarnivalGetDataCommand) Error(msg *cl.L2C_CarnivalGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CarnivalGetData, msg)
	return false
}

func (c *C2LCarnivalGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CarnivalGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CarnivalGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CarnivalGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_CarnivalGetData{
		Ret: uint32(cret.RET_OK),
	}

	smsg.Carnival = c.User.Carnival().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CarnivalGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LCarnivalReceiveAwardCommand struct {
	base.UserCommand
}

func (c *C2LCarnivalReceiveAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCarnivalReceiveAwardCommand) Error(msg *cl.L2C_CarnivalReceiveAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CarnivalReceiveAward, msg)
	return false
}

//nolint:funlen
func (c *C2LCarnivalReceiveAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CarnivalReceiveAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CarnivalReceiveAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CarnivalReceiveAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_CarnivalReceiveAward{
		Ret:        uint32(cret.RET_OK),
		CarnivalId: cmsg.CarnivalId,
		Ids:        cmsg.Ids,
	}

	carnivalM := c.User.Carnival()

	if !carnivalM.IsCarnivalOpen(cmsg.CarnivalId) {
		l4g.Errorf("user: %d C2L_CarnivalReceiveAward: carnival is close, id:%d", c.Msg.UID, cmsg.CarnivalId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	carnivalInfo := goxml.GetData().CarnivalInfoM.Index(cmsg.CarnivalId)
	if carnivalInfo == nil {
		l4g.Errorf("user: %d C2L_CarnivalReceiveAward: no carnivalInfo. id:%d", c.Msg.UID, cmsg.CarnivalId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	var funcId uint32
	if cmsg.CarnivalId == character.CarnivalSeven {
		funcId = uint32(common.FUNCID_MODULE_CARNIVAL_SEVEN)
	} else if cmsg.CarnivalId == character.CarnivalFourteen {
		funcId = uint32(common.FUNCID_MODULE_CARNIVAL_FOURTEEN)
	} else if carnivalInfo.SeasonId > 0 {
		funcId = uint32(common.FUNCID_MODULE_CARNIVAL_SEASON)
	} else {
		l4g.Errorf("user: %d C2L_CarnivalReceiveAward: carnivalId error. carnivalId:%d", c.Msg.UID, cmsg.CarnivalId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	if !c.User.IsFunctionOpen(funcId, c.Srv) {
		l4g.Errorf("user: %d C2L_CarnivalReceiveAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if len(cmsg.Ids) > len(goxml.GetData().CarnivalTaskInfoM.Datas) {
		l4g.Errorf("user: %d C2L_CarnivalReceiveAward: ids num illegal. num:%d", c.Msg.UID, len(cmsg.Ids))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	uniq := make(map[uint32]struct{})
	smsg.Awards = make([]*cl.Resource, 0, len(cmsg.Ids)) // 奖励不合并
	totalAwards := make([]*cl.Resource, 0, len(cmsg.Ids))
	oldPoints := carnivalM.GetCarnivalPointsById(cmsg.CarnivalId)
	addPoints := uint32(0) //添加的积分
	for _, id := range cmsg.Ids {
		if _, exist := uniq[id]; exist {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: id repeated. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[id] = struct{}{}

		carnivalTaskInfo := goxml.GetData().CarnivalTaskInfoM.Index(id)
		if carnivalTaskInfo == nil {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: id error. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if carnivalTaskInfo.DayNumber+goxml.GetData().CarnivalInfoM.Index(cmsg.CarnivalId).OpenDay-1 >
			//helper.DaysAfterStartOfService(c.Srv.StartServiceTm(), time.Now().Unix()) {
			helper.DaysAfterStartOfService(c.User.CreateTime(), time.Now().Unix()) {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: task not open. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if cmsg.CarnivalId != carnivalTaskInfo.CarnivalId {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: id error. id:%d", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(carnivalTaskInfo.TypeId)
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d C2L_TaskReceiveAward: taskTypeInfo not exist. typeId:%d", c.Msg.UID, carnivalTaskInfo.TypeId)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		progress := carnivalM.GetProgressByType(taskTypeInfo, cmsg.CarnivalId)
		if !c.User.CheckTaskFinish(progress, carnivalTaskInfo.TypeId, uint64(carnivalTaskInfo.Value)) {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: not finish. id:%d pro:%+v typeId:%d value:%d",
				c.Msg.UID, id, progress, carnivalTaskInfo.TypeId, carnivalTaskInfo.Value)
			return c.Error(smsg, uint32(cret.RET_CARNIVAL_TASK_PROGRESS_NOT_FINISH))
		}

		if carnivalM.IsAwarded(cmsg.CarnivalId, id) {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: has receive award. id:%d ", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}

		//需要判断，嘉年华没有停止才加积分
		if carnivalTaskInfo.GetPoints > 0 {
			addPoints += carnivalTaskInfo.GetPoints
		}
		awards := carnivalTaskInfo.ClRes
		if awards == nil {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: awards empty. id:%d ", c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		totalAwards = append(totalAwards, awards...)
		smsg.Awards = append(smsg.Awards, awards...)
	}
	smsg.Ret, _ = c.User.Award(c.Srv, totalAwards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_CARNIVAL_TASK_RECV_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_CarnivalReceiveAward: award error. ret:%d ", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	carnivalM.ReceiveAward(cmsg.CarnivalId, cmsg.Ids)
	carnivalM.AddPoints(c.Srv, cmsg.CarnivalId, addPoints, carnivalInfo.SeasonId > 0)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CarnivalReceiveAward, smsg)

	if c.ResultOK(smsg.Ret) {
		c.User.LogCarnivalReceiveAward(c.Srv, smsg.Ids, smsg.CarnivalId, addPoints, oldPoints)
	}

	return c.ResultOK(smsg.Ret)
}
