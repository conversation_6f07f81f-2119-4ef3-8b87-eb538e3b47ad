package pushgift

import (
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"
	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_PushGiftGetData), &C2LPushGiftGetDataCommand{}, state) //获取推送礼包信息
}

type C2LPushGiftGetDataCommand struct {
	base.UserCommand
}

func (c *C2LPushGiftGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPushGiftGetDataCommand) Error(msg *cl.L2C_PushGiftGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PushGiftGetData, msg)
	return false
}

func (c *C2LPushGiftGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PushGiftGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PushGiftGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PushGiftGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PushGiftGetData{
		Ret: uint32(cret.RET_OK),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_PUSHGIFT)) {
	//	l4g.Errorf("user: %d C2L_PushGiftGetData: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}

	c.User.PushGift().CheckExpireGift(c.Srv)
	smsg.PushGifts = c.User.PushGift().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PushGiftGetData, smsg)
	return c.ResultOK(smsg.Ret)
}
