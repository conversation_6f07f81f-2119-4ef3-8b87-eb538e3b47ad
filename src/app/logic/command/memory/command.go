package memory

import (
	"app/goxml"
	"app/logic/character"
	"app/protos/in/log"
	"context"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	"app/logic/command/base"
	"app/protos/out/cl"

	"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_MemoryLatest), &C2LMemoryLatestCommand{}, state) //最新的回忆
	cmds.Register(uint32(cl.ID_MSG_C2L_MemoryUnlock), &C2LMemoryUnlockCommand{}, state) //回忆点解锁
}

type C2LMemoryLatestCommand struct {
	base.UserCommand
}

func (c *C2LMemoryLatestCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LMemoryLatestCommand) Error(msg *cl.L2C_MemoryLatest, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MemoryLatest, msg)
	return false
}

func (c *C2LMemoryLatestCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_MemoryLatest{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d C2L_MemoryLatest Unmarshal error: %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_MemoryLatest: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_MemoryLatest{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_MEMORY), c.Srv) {
		l4g.Errorf("user: %d C2L_MemoryLatest: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	smsg.Memory = c.User.Memory().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MemoryLatest, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LMemoryUnlockCommand struct {
	base.UserCommand
}

func (c *C2LMemoryUnlockCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LMemoryUnlockCommand) Error(msg *cl.L2C_MemoryUnlock, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MemoryUnlock, msg)
	return false
}

func (c *C2LMemoryUnlockCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_MemoryUnlock{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d  C2L_MemoryUnlock Unmarshal error: %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_MemoryUnlock: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_MemoryUnlock{
		Ret:   uint32(cret.RET_OK),
		SysId: cmsg.SysId,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_MEMORY), c.Srv) {
		l4g.Errorf("user: %d C2L_MemoryUnlock: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 验证解锁id是否存在
	chipInfo := goxml.GetData().MemoryChipInfoM.Index(cmsg.SysId)
	if chipInfo == nil {
		l4g.Errorf("user: %d C2L_MemoryUnlock failed: memory chip not exist. chip ID: %d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_MEMORY_CHIP_NOT_EXIST))
	}

	memoryM := c.User.Memory()
	if memoryM.GetMemory() == nil {
		// 验证回忆点的 group(回忆) 是否为1
		if chipInfo.Group != goxml.MemoryFirst {
			l4g.Errorf("user: %d C2L_MemoryUnlock failed: chip group not first group. chip ID: %d, chip Group: %d",
				c.Msg.UID, cmsg.SysId, chipInfo.Group)
			return c.Error(smsg, uint32(cret.RET_MEMORY_CHIP_GROUP_NOT_FIRST))
		}

	} else {
		// 验证是否重复解锁
		if memoryM.CheckRepeatUnlock(cmsg.SysId) {
			l4g.Errorf("user: %d C2L_MemoryUnlock failed: unlock failed, repeat unlock. chip ID: %d",
				c.Msg.UID, cmsg.SysId)
			return c.Error(smsg, uint32(cret.RET_MEMORY_CHIP_REPEAT_UNLOCK))
		}

		unLockChipInfo := goxml.GetData().MemoryChipInfoM.Index(memoryM.GetUnlockID())
		if unLockChipInfo == nil {
			l4g.Errorf("user: %d C2L_MemoryUnlock: memory chip not exist. chip ID: %d",
				c.Msg.UID, memoryM.GetUnlockID())
			return c.Error(smsg, uint32(cret.RET_MEMORY_CHIP_NOT_EXIST))
		}

		unlockNum := memoryM.GetUnlockCount()
		if unlockNum < goxml.MemoryChipNum {
			// 当数量小于5时，验证要解锁的回忆点的group是否跟已解锁的一致
			if unLockChipInfo.Group != chipInfo.Group {
				l4g.Errorf("user: %d C2L_MemoryUnlock: group not same. chip ID: %d. Group: %d,unlocked Group: %d",
					c.Msg.UID, cmsg.SysId, chipInfo.Group, unLockChipInfo.Group)
				return c.Error(smsg, uint32(cret.RET_MEMORY_CHIP_GROUP_NOT_SAME))
			}

		} else {
			// 当数量等于5时，验证要解锁的回忆点的group是否比已解锁的回忆点多1，保证回忆有序解锁
			if chipInfo.Group-unLockChipInfo.Group != 1 {
				l4g.Errorf("user: %d C2L_MemoryUnlock: pre group not valid. chip ID: %d,Group: %d,unlocked Group: %d",
					c.Msg.UID, cmsg.SysId, chipInfo.Group, unLockChipInfo.Group)
				return c.Error(smsg, uint32(cret.RET_MEMORY_CHIP_PRE_GROUP_NOT_VALID))
			}
		}
	}

	// 消耗世界石碎片
	retCode := c.User.Consume(c.Srv, chipInfo.ResClRes, uint32(log.RESOURCE_CHANGE_REASON_MEMORY_UNLOCK_CHIP), 0)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_MemoryUnlock: consume resource failed. chip ID: %d, errorCode: %d",
			c.Msg.UID, cmsg.SysId, retCode)
		return c.Error(smsg, retCode)
	}

	// 解锁回忆点
	memoryM.Add(c.Srv, cmsg.SysId)
	// 更新全局属性
	memoryM.UpdateMemoryGlobalAttrToClient()
	// 更新英雄属性是否重新计算的标识
	c.User.HeroManager().SetHeroAttrChange()
	// 更新阵容战力
	c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
	// 日志
	c.User.LogMemoryUnlock(c.Srv, cmsg.SysId)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MemoryUnlock, smsg)

	return c.ResultOK(smsg.Ret)
}
