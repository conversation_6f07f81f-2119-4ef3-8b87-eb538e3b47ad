package shop

import (
	"app/goxml"
	"testing"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

const dataPath = "../../../../../data/"

func TestRandom(t *testing.T) {
	l4g.ChangeFilterLevel("stdout", l4g.INFO)
	defer l4g.Close()
	goxml.Load(dataPath, false, false)
	group := uint32(10201)
	num := uint32(25)
	rd := rand.New(time.Now().UnixNano())
	result := goxml.GetData().ShopRandomGoodsInfoM.Generate(rd, group, num)
	t.Logf("count:%d, result:%+v, \n", len(result), result)
}

func BenchmarkRandom(b *testing.B) {
	l4g.ChangeFilterLevel("stdout", l4g.INFO)
	defer l4g.Close()
	goxml.Load(dataPath, false, false)
	group := uint32(10201)
	num := uint32(3)
	rd := rand.New(time.Now().UnixNano())
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ret := goxml.GetData().ShopRandomGoodsInfoM.Generate(rd, group, num)
		b.Logf("count:%d, result:%+v, \n", len(ret), ret)
	}
}
