package summon

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/math/rand"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_Summon), &C2LSummonCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SummonGetData), &C2LSummonGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SummonSetHeroAutoDecompose), &C2LSummonSetHeroAutoDecomposeCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SummonSetWishList), &C2LSummonSetWishListCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SummonArtifactPointsExchange), &C2LSummonArtifactPointsExchangeCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SummonSimulation), &C2LSummonSimulationCommand{}, state)
}

type C2LSummonCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSummonCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSummonCommand) Error(msg *cl.L2C_Summon, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Summon, msg)
	return false
}

//nolint:funlen
func (c *C2LSummonCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_Summon{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_Summon Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_Summon: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_Summon{
		Ret:    uint32(cret.RET_OK),
		Id:     cmsg.Id,
		Plural: cmsg.Plural,
	}
	var totalCosts []*cl.Resource
	summonTypeInfo := goxml.GetData().SummonTypeInfoM.Index(cmsg.Id)
	if summonTypeInfo == nil {
		l4g.Errorf("user: %d C2L_Summon: config error. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	if !c.User.IsFunctionOpen(summonTypeInfo.FuncID, c.Srv) {
		l4g.Errorf("user: %d C2L_Summon: function not open. funcId:%d", c.Msg.UID, summonTypeInfo.FuncID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	count := uint32(1)
	if cmsg.Plural {
		count = summonTypeInfo.Plural
		if count == 0 {
			l4g.Errorf("user: %d C2L_Summon: request param error. group:%d", c.Msg.UID, summonTypeInfo.Group)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	free := checkFree(c.User, count, summonTypeInfo)

	if !free {
		totalCosts, smsg.Ret = calcCosts(c.User, cmsg.Plural, summonTypeInfo)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_Summon: resource not enough.", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}
	}

	summonM := c.User.Summon()
	oldTotalCount, oldSpecialCount := summonM.GetSummonCount(summonTypeInfo.Group), uint32(0)

	summonHandle := initSummonHandle(summonTypeInfo.Group, summonM, c.Srv, c.User)

	if summonHandle == nil {
		l4g.Errorf("user: %d C2L_Summon: group error. group:%d", c.Msg.UID, summonTypeInfo.Group)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	summon(summonHandle, summonTypeInfo.Group, count, c.Srv.Rand(), c.Msg.UID)

	summonHandle.save(summonTypeInfo.Group, count)

	if free {
		c.User.AddSummonFreeCount(summonTypeInfo.Group, count)
	}

	var errorCode, trueFiveHeroCount uint32
	var allAwards, artifactBeDecomposed []*cl.Resource //最终发放的所有奖
	var ArtifactRare map[uint32]uint32
	errorCode, allAwards, smsg.Awards, trueFiveHeroCount, ArtifactRare, artifactBeDecomposed = c.summonAwardsTransform(summonHandle.getSummonAwards())
	if errorCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_Summon: award transform error. errorCode:%d", c.Msg.UID, errorCode)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if len(totalCosts) > 0 {
		cRet := c.User.Consume(c.Srv, totalCosts, uint32(log.RESOURCE_CHANGE_REASON_SUMMON_HERO), uint64(summonTypeInfo.Group))
		if cRet != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_Summon: consume error.", c.Msg.UID)
			return c.Error(smsg, cRet)
		}
	}

	c.User.FireCommonEvent(c.Srv.EventM(), event.IeSummon, uint64(count), summonTypeInfo.Group)
	if trueFiveHeroCount > 0 {
		c.User.FireCommonEvent(c.Srv.EventM(), event.IeSummonTrueFiveHero, uint64(trueFiveHeroCount))
	}
	if summonTypeInfo.Group == goxml.ArtifactSummon {
		for Rare, count := range ArtifactRare {
			c.User.FireCommonEvent(c.Srv.EventM(), event.IeArtifactSummon, uint64(count), Rare)
		}
	}
	if summonTypeInfo.Group == goxml.ArtifactPoinsSummon || summonTypeInfo.Group == goxml.ArtifactSummon {
		for Rare, count := range ArtifactRare {
			c.User.FireCommonEvent(c.Srv.EventM(), event.IeArtifactPointSummon, uint64(count), Rare)
		}
	}

	//增加额外获得
	if len(summonTypeInfo.GetAwards) > 0 {
		if cmsg.Plural {
			allAwards = append(allAwards, summonTypeInfo.GetAwardsPlural...)
			smsg.Awards = append(smsg.Awards, summonTypeInfo.GetAwardsPlural...)
		} else {
			allAwards = append(allAwards, summonTypeInfo.GetAwards...)
			smsg.Awards = append(smsg.Awards, summonTypeInfo.GetAwards...)
		}
	}
	var retAwards []*cl.Resource
	smsg.Ret, retAwards = c.User.Award(c.Srv, allAwards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SUMMON_HERO), uint64(summonTypeInfo.Group))
	if summonTypeInfo.Group == goxml.ArtifactPoinsSummon || summonTypeInfo.Group == goxml.ArtifactSummon {
		smsg.Awards = retAwards
		smsg.Awards = append(smsg.Awards, artifactBeDecomposed...)
	}

	if summonM.CheckNeedShowLeftCount(cmsg.Id) {
		smsg.LeftGuarantee = summonM.GetColorLeftCount(cmsg.Id)
	}
	c.User.SendSelfToClient()
	c.User.SendSummonDataToClient()
	if c.ResultOK(smsg.Ret) {
		wishHeroes := make([]uint32, count)
		c.User.LogSummon(c.Srv, cmsg.Id, count, oldTotalCount, oldSpecialCount,
			summonM.GetSummonCount(summonTypeInfo.Group), 0, 0, 0, summonHandle.getGuaranteeWay(),
			totalCosts, smsg.Awards, summonHandle.getSummonAwards(), wishHeroes)
	}

	smsg.AdvancedSummonCount = c.User.Summon().GetContinuitySummonCount(goxml.AdvancedSummon)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Summon, smsg)
	if c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_CHAT), c.Srv) {
		msgParams := getMsgParam(summonTypeInfo.Group, summonHandle.getSummonAwards())
		if len(msgParams) > 0 {
			msgType := goxml.GetData().ChatAdminInfoM.GetMsgTypeFromSummon(summonTypeInfo.Group, cmsg.Plural)
			if msgType > 0 {
				message := character.NewMsg(character.Id(c.Srv), character.Head(c.User.NewChatUserHead(c.Srv.GetLogicGuild(c.User.ID()))),
					character.Type(msgType), character.Params(msgParams...))
				character.ChatSendMsgToPlatform(c.Srv, c.User.ID(), message)
			}
		}
	}

	return c.ResultOK(smsg.Ret)
}

func initSummonHandle(group uint32, summonM *character.Summon, srv command.Servicer, user *character.User, simulation ...*cl.L2C_SummonSimulation) summonHandle {
	thisCount := summonM.GetSummonCount(group)
	newBaseSummon := &baseSummon{
		thisCount:           thisCount,
		summonM:             summonM,
		colorGuaranteeCount: summonM.GetColorGuaranteeCount(group),
		summonAwards:        make([]*cl.Resource, 0),
		srv:                 srv,
		user:                user,
		activityGuarantee:   summonM.GetActivityGuarantee(group),
		SummonAwardWaySlice: make([]uint32, 0),
	}
	if len(simulation) > 0 && simulation[0] != nil {
		newBaseSummon.SummonSimulation = simulation[0]
	}

	switch group {
	case goxml.BasicSummon, goxml.AssocSummonEmpire, goxml.AssocSummonWoodland, goxml.AssocSummonEclipse,
		goxml.AssocSummonDemon, goxml.ArtifactPoinsSummon:
		return newBaseSummon
	case goxml.AdvancedSummon:
		return &advancedSummon{
			baseSummon:         newBaseSummon,
			wishIds:            summonM.GetWishList(),
			wishGuaranteeCount: summonM.GetWishListGuaranteeCount(),
			wishNum:            summonM.GetWishNum(),
		}
	case goxml.ArtifactSummon:
		return &artifactSummon{
			baseSummon:                     newBaseSummon,
			artifactFragmentGuaranteeCount: summonM.GetArtifactFragmentGuaranteeCount(),
		}
	default:
		return nil
	}
}

//nolint:unparam
func summon(summonHandle summonHandle, group, count uint32, rand *rand.Rand, uid uint64) uint32 {
	for i := uint32(1); i <= count; i++ {
		var ret uint32
		activityGuaranteeInfo, activityTrigger, _ := summonHandle.inActivityGuarantee(group)
		thisCount := summonHandle.updateThisCount(1, group, uid)
		l4g.Debugf("summon this count:%d", thisCount)
		var class uint32
		var indexCount uint32
		specialClassInfo := goxml.GetData().SummonSpecialClassInfoM.IndexGroupSpecialClassInfo(group, thisCount)
		if specialClassInfo != nil {
			l4g.Debugf("specialClassInfo.Id:%v thisCount:%d", specialClassInfo.Id, thisCount)
			classInfo := summonHandle.checkColorGuarantee(group)
			if classInfo != nil {
				ret = summonHandle.colorGuaranteeSummonWithGuarantee(rand, classInfo, uid)
				class = classInfo.Class
				if ret != uint32(cret.RET_OK) {
					l4g.Errorf("user: %d summon with guarantee failed ret:%d", uid, ret)
					return ret
				}
			} else {
				ret = summonHandle.guaranteeSummon(rand, uid, specialClassInfo.ChangeClass, character.SummonChangeGuarantee)
				if ret != uint32(cret.RET_OK) {
					l4g.Errorf("user: %d summon: fixedGuarantee summon error. ret:%d", uid, ret)
					return ret
				}
				class = specialClassInfo.ChangeClass
			}
		} else if activityTrigger {
			l4g.Debugf("activityTrigger.Id:%v thisCount:%d", activityGuaranteeInfo.Id, thisCount)
			ret = summonHandle.activitySummon(rand, uid, activityGuaranteeInfo.GuaranteeClass, character.SummonRoundActivityGuarantee)
			if ret != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d summon: round activity guarantee summon error. ret:%d", uid, ret)
				return ret
			}
			class = activityGuaranteeInfo.GuaranteeClass
		} else {
			l4g.Debugf("normal summon thisCount:%d", thisCount)
			classInfo := summonHandle.checkColorGuarantee(group)
			if classInfo != nil {
				ret = summonHandle.colorGuaranteeSummonWithGuarantee(rand, classInfo, uid)
				class = classInfo.Class
			} else {
				ret, class = summonHandle.colorGuaranteeSummonWithChangeWeight(rand, group, uid)
			}

			if ret != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d summon: normal summon error. ret:%d", uid, ret)
				return ret
			}
		}

		// 不同抽卡的特殊处理（比如：高级抽的心愿单保底、神器抽的碎片保底）
		ret = summonHandle.extraGuaranteeSummon(rand, uid)
		if ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d summon: handleExtraGuaranteeSummon error. ret:%d", uid, ret)
			return ret
		}

		//根据最终奖励更新保底记数
		indexCount = summonHandle.updateColorGuaranteeByAward(uid, group, activityGuaranteeInfo)
		if summonHandle.isSummonSimulation() {
			summonHandle.simulationRecord(uid, group, class, indexCount)
		}
		summonHandle.recordGuaranteeWay()
		summonHandle.record(uid, group)
	}
	return uint32(cret.RET_OK)
}

func getMsgParam(summonType uint32, awards []*cl.Resource) []string {
	var params []string

	switch summonType {
	case goxml.BasicSummon, goxml.AdvancedSummon:
		params = goxml.GetData().ChatConfigInfoM.GetMatchHeroSysID(awards)
	case goxml.ArtifactSummon, goxml.ArtifactPoinsSummon:
		params = goxml.GetData().ChatConfigInfoM.GetMatchArtifactSysID(awards)
	}

	return params
}

func checkFree(user *character.User, count uint32, summonTypeInfo *goxml.SummonTypeInfoExt) bool {
	if summonTypeInfo.FreeCnt == 0 {
		return false
	}
	return user.SummonFreeCount()[summonTypeInfo.Group]+count <= summonTypeInfo.FreeCnt
}

func calcCosts(user *character.User, plural bool, summonTypeInfo *goxml.SummonTypeInfoExt) ([]*cl.Resource, uint32) {
	var costs []*cl.Resource
	if plural {
		costs = summonTypeInfo.ConsPlural
	} else {
		costs = summonTypeInfo.Cons
	}
	for _, cost := range costs {
		res, newCosts := user.CheckResourcesSize([]*cl.Resource{cost})
		if res == uint32(cret.RET_OK) {
			return newCosts, res
		}
	}
	return nil, uint32(cret.RET_NOT_ENOUGH_RESOURCES)
}

// 对抽到的卡进行转换
func (c *C2LSummonCommand) summonAwardsTransform(summonAwards []*cl.Resource) (uint32, []*cl.Resource, []*cl.Resource, uint32, map[uint32]uint32, []*cl.Resource) {
	oldNum := c.User.HeroManager().GetCount()
	maxNum := c.User.HeroSlot()
	var allAwards, retAwards, artifactBeDecomposed []*cl.Resource
	var TureFiveHeroCount uint32
	var ArtifactRare map[uint32]uint32
	//对英雄和神器进行转换处理
	for _, v := range summonAwards { //nolint:varnamelen
		if v.Type == uint32(common.RESOURCE_HERO) { // 英雄有可能转换为碎片
			heroFragment := goxml.GetData().FragmentInfoM.Hero2Fragment(v.Value)
			if heroFragment == nil {
				l4g.Errorf("user: %d C2L_Summon: hero2fragment error. hid %d", c.User.ID(), v.Value)
				return uint32(cret.RET_SYSTEM_DATA_ERROR), nil, nil, 0, nil, nil
			}
			heroInfo := goxml.GetData().HeroInfoM.Index(v.Value)
			if heroInfo.Star <= 2 && c.User.IsSummonAutoDecompose() {
				starInfo := goxml.GetData().HeroStarInfoM.Index(heroInfo.Star, heroInfo.Rare, heroInfo.Race)
				allAwards = append(allAwards, starInfo.ResAwards...)
				retAwards = append(retAwards, v)
				continue
			}
			if heroInfo.Rare >= goxml.HeroRareTrueFive {
				TureFiveHeroCount++
			}
			oldNum++
			if oldNum > maxNum {
				allAwards = append(allAwards, heroFragment)
				retAwards = append(retAwards, heroFragment)
				continue
			}
		} else if v.Type == uint32(common.RESOURCE_ARTIFACT) { // 神器有可能转换为碎片
			artifactInfo := goxml.GetData().ArtifactInfoM.Index(v.Value)
			if artifactInfo == nil {
				l4g.Errorf("user: %d C2L_Summon: artifact error. artifactId:%d", c.User.ID(), v.Value)
				return uint32(cret.RET_SYSTEM_DATA_ERROR), nil, nil, 0, nil, nil
			}
			if ArtifactRare == nil {
				ArtifactRare = make(map[uint32]uint32)
			}
			ArtifactRare[artifactInfo.Rare]++
			if c.User.ArtifactManager().GetArtifact(v.Value) != nil {
				fragment := goxml.GetData().ArtifactInfoM.Artifact2Fragment(v.Value, 1)
				if fragment == nil {
					l4g.Errorf("user: %d C2L_Summon: artifact2fragment error. artifactId:%d", c.User.ID(), v.Value)
					return uint32(cret.RET_SYSTEM_DATA_ERROR), nil, nil, 0, nil, nil
				}
				artifactBeDecomposed = append(artifactBeDecomposed, &cl.Resource{
					Type:  uint32(common.RESOURCE_ARTIFACT),
					Value: v.Value,
					Count: 1,
					Attrs: []*cl.Attr{{
						Type:  uint32(common.RESOURCE_ATTR_TYPE_RESAT_AWARD_FLAG),
						Value: int64(common.RESOURCE_REWARD_FLAG_RRF_NEW_DECOMPOSED),
					}},
				})
				allAwards = append(allAwards, fragment)
				retAwards = append(retAwards, fragment)
				continue
			}
		}
		allAwards = append(allAwards, v)
		retAwards = append(retAwards, v)
	}
	return uint32(cret.RET_OK), allAwards, retAwards, TureFiveHeroCount, ArtifactRare, artifactBeDecomposed
}

type C2LSummonGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSummonGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSummonGetDataCommand) Error(msg *cl.L2C_SummonGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonGetData, msg)
	return false
}

func (c *C2LSummonGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SummonGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SummonGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user: %d C2L_SummonGetData: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SummonGetData{
		Ret: uint32(cret.RET_OK),
	}

	c.User.Summon().Flush(smsg)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonGetData, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LSummonSetHeroAutoDecomposeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSummonSetHeroAutoDecomposeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSummonSetHeroAutoDecomposeCommand) Error(msg *cl.L2C_SummonSetHeroAutoDecompose, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonSetHeroAutoDecompose, msg)
	return false
}

func (c *C2LSummonSetHeroAutoDecomposeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SummonSetHeroAutoDecompose{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SummonSetHeroAutoDecompose Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user: %d C2L_SummonSetHeroAutoDecompose: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SummonSetHeroAutoDecompose{
		Ret:           uint32(cret.RET_OK),
		AutoDecompose: cmsg.AutoDecompose,
	}
	l4g.Errorf("function not open, uid:%d", c.Msg.UID)
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
}

type C2LSummonSetWishListCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSummonSetWishListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSummonSetWishListCommand) Error(msg *cl.L2C_SummonSetWishList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonSetWishList, msg)
	return false
}

func (c *C2LSummonSetWishListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SummonSetWishList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SummonSetWishList Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user: %d C2L_SummonSetWishList: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SummonSetWishList{
		Ret:    uint32(cret.RET_OK),
		Slot:   cmsg.Slot,
		WishId: cmsg.WishId,
	}

	if !c.User.IsFunctionOpen(goxml.SummonGroupFuncID[goxml.WishListGroup], c.Srv) {
		l4g.Errorf("user: %d C2L_SummonSetWishList: function not open. funcId:%d", c.Msg.UID, goxml.SummonGroupFuncID[goxml.WishListGroup])
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	summonM := c.User.Summon()
	totalCount := summonM.GetSummonCount(goxml.WishListGroup)

	if totalCount < goxml.GetData().WishListConfigInfoM.GetWishListOpenNum() {
		l4g.Errorf("user: %d C2L_SummonSetWishList: wishList not open. total:%d", c.Msg.UID, totalCount)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.Slot != character.WishSlotLeft && cmsg.Slot != character.WishSlotRight {
		l4g.Errorf("user: %d C2L_SummonSetWishList: wishId repeat. wishId:%d", c.Msg.UID, cmsg.WishId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//wishId等于0表示要去掉这个格位的wishID
	if cmsg.WishId != 0 && !goxml.GetData().WishListRangeInfoM.CheckWishId(goxml.WishListGroup, cmsg.WishId) {
		l4g.Errorf("user: %d C2L_SummonSetWishList: wishId is error. wishId:%d", c.Msg.UID, cmsg.WishId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if summonM.IsWishIdRepeat(goxml.WishListGroup, cmsg.WishId) {
		l4g.Errorf("user: %d C2L_SummonSetWishList: wishId repeat. wishId:%d", c.Msg.UID, cmsg.WishId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.WishId != 0 {
		summonM.SetWishList(goxml.WishListGroup, cmsg.Slot, cmsg.WishId)
	} else {
		summonM.DeleteWishID(goxml.WishListGroup, cmsg.Slot)
	}

	//把距离保底还有几次传给前端更新
	//smsg.NumToGuarantee = summonM.CalcWishListNumToGuarantee(goxml.WishListGroup)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonSetWishList, smsg)
	if c.ResultOK(smsg.Ret) {
		c.User.LogSetWishList(c.Srv, cmsg.Slot, cmsg.WishId)
	}
	return c.ResultOK(smsg.Ret)
}

type C2LSummonArtifactPointsExchangeCommand struct {
	base.UserCommand
}

func (c *C2LSummonArtifactPointsExchangeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSummonArtifactPointsExchangeCommand) Error(msg *cl.L2C_SummonArtifactPointsExchange, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonSetWishList, msg)
	return false
}

func (c *C2LSummonArtifactPointsExchangeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SummonArtifactPointsExchange{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SummonArtifactPointsExchange Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user: %d C2L_SummonArtifactPointsExchange: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SummonArtifactPointsExchange{
		Ret:     uint32(cret.RET_OK),
		AwardId: cmsg.AwardId,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ARTIFACT_POINTS_EXCHANGE), c.Srv) {
		l4g.Errorf("user: %d C2L_SummonArtifactPointsExchange: function not open. funcId:%d",
			c.Msg.UID, common.FUNCID_MODULE_ARTIFACT_POINTS_EXCHANGE)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if goxml.GetData().ArtifactExchangeInfoM.Index(cmsg.AwardId) == nil {
		l4g.Errorf("user: %d C2L_SummonArtifactPointsExchange: request id not exist. id:%d", c.Msg.UID, cmsg.AwardId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	costs := goxml.GetData().ConfigInfoM.GetArtifactExchangeCost()

	awards := []*cl.Resource{
		{
			Type:  uint32(common.RESOURCE_ARTIFACT),
			Value: cmsg.AwardId,
			Count: 1,
		},
	}

	smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, costs, awards, uint32(log.RESOURCE_CHANGE_REASON_ARTIFACT_POINTS_EXCHANGE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_SummonArtifactPointsExchange: trade error. ret:%d",
			c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonArtifactPointsExchange, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LSummonSimulationCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSummonSimulationCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSummonSimulationCommand) Error(msg *cl.L2C_SummonSimulation, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonSimulation, msg)
	return false
}

func (c *C2LSummonSimulationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SummonSimulation{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SummonSimulation Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SummonSimulation: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_SummonSimulation{
		Ret:                uint32(cret.RET_OK),
		HeroCount:          make(map[uint32]uint32),
		PoolCount:          make(map[uint32]uint32),
		RareCount:          make(map[uint32]uint32),
		TopRareSummonCount: make(map[uint32]uint32),
	}

	if goxml.GetData().ServerInfoM.GM == 0 {
		l4g.Errorf("user: %d C2L_SummonSimulation: functions is not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	summonTypeInfo := goxml.GetData().SummonTypeInfoM.Index(cmsg.Id)
	if summonTypeInfo == nil {
		l4g.Errorf("user: %d C2L_SummonSimulation: config error. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	summonM := c.User.Summon()
	summonHandle := initSummonHandle(summonTypeInfo.Group, summonM, c.Srv, c.User, smsg)

	if summonHandle == nil {
		l4g.Errorf("user: %d C2L_Summon: group error. group:%d", c.Msg.UID, summonTypeInfo.Group)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	summon(summonHandle, summonTypeInfo.Group, cmsg.Count, c.Srv.Rand(), c.Msg.UID)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SummonSimulation, smsg)

	return c.ResultOK(smsg.Ret)
}
