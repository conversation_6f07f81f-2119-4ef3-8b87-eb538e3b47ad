package user

import (
	"app/logic/activity/gst"
	"app/logic/activity/seasonarena"
	"app/logic/command"
	"app/logic/etcdcache"
	"app/logic/event"
	"app/logic/helper"
	"app/logic/mongo"
	"app/protos/in/l2c"
	"app/protos/in/l2m"
	"app/protos/in/p2l"
	"context"
	"math"
	"strconv"
	"strings"
	"sync"
	"unicode/utf8"

	"app/logic/activity"
	"app/logic/activity/arena"
	aguild "app/logic/activity/guild"
	apeak "app/logic/activity/peak"
	"app/logic/character"

	//"app/logic/command"
	"app/logic/command/base"

	//h "app/logic/helper"

	//aevent "app/logic/event"
	"app/goxml"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	cret "app/protos/out/ret"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_Flush), &C2LFlushCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_KeepAlive), &C2LKeepAliveCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_AddPurchaseNum), &C2LAddPurchaseNumCommand{}, state)         // 购买次数
	cmds.Register(uint32(cl.ID_MSG_C2L_GM), &C2LGMCommand{}, state)                                 //GM
	cmds.Register(uint32(cl.ID_MSG_C2L_GetCommonRank), &C2LGetCommonRankCommand{}, state)           // 获取通用排行榜
	cmds.Register(uint32(cl.ID_MSG_C2L_GetCommonRankFirst), &C2LGetCommonRankFirstCommand{}, state) // 获取通用排行榜首页数据
	cmds.Register(uint32(cl.ID_MSG_C2L_CommonRankLike), &C2LCommonRankLikeCommand{}, state)         // 通用排行榜点赞
	cmds.Register(uint32(cl.ID_MSG_C2L_ViewUser), &C2LViewUserCommand{}, state)                     //查看玩家信息
	cmds.Register(uint32(cl.ID_MSG_C2L_Formation), &C2LFormationCommand{}, state)                   //布阵
	cmds.Register(uint32(cl.ID_MSG_C2L_GetFormation), &C2LGetFormationCommand{}, state)             //查看布阵信息
	//cmds.Register(uint32(cl.ID_MSG_C2L_BattleTest), &C2LBattleTestCommand{}, state)               //战斗测试
	cmds.Register(uint32(cl.ID_MSG_C2L_GetUserBattleData), &C2LGetUserBattleDataCommand{}, state)           //获取玩家战斗数据
	cmds.Register(uint32(cl.ID_MSG_C2L_TestBattleData), &C2LTestBattleDataCommand{}, state)                 //测试接口，比较阵容数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GlobalAttrGet), &C2LGlobalAttrGetCommand{}, state)                   //获取全局属性
	cmds.Register(uint32(cl.ID_MSG_C2L_SetName), &C2LSetNameCommand{}, state)                               //设置昵称
	cmds.Register(uint32(cl.ID_MSG_C2L_RobotBattle), &C2LRobotBattleCommand{}, state)                       //机器人战斗测试
	cmds.Register(uint32(cl.ID_MSG_C2L_SyncQuestionnaire), &C2LSyncQuestionnaireCommand{}, state)           //同步问卷
	cmds.Register(uint32(cl.ID_MSG_C2L_Duel), &C2LDuelCommand{}, state)                                     //切磋
	cmds.Register(uint32(cl.ID_MSG_C2L_Accusation), &C2LAccusationCommand{}, state)                         //举报
	cmds.Register(uint32(cl.ID_MSG_C2L_GetClientInfo), &C2LGetClientInfoCommand{}, state)                   //拉取客户端信息
	cmds.Register(uint32(cl.ID_MSG_C2L_SetClientInfo), &C2LSetClientInfoCommand{}, state)                   //设置客户端信息
	cmds.Register(uint32(cl.ID_MSG_C2L_GetUserSnapshots), &C2LGetUserSnapshotsCommand{}, state)             //获取玩家快照
	cmds.Register(uint32(cl.ID_MSG_C2L_RecvShareAward), &C2LRecvShareAwardCommand{}, state)                 //领取分享奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_GetGiftCodeAward), &C2LGetGiftCodeAwardCommand{}, state)             //领取礼包码奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_ClientSetMultiLang), &C2LClientSetMultiLangCommand{}, state)         //设置前端语言
	cmds.Register(uint32(cl.ID_MSG_C2L_ClientGetMultiLang), &C2LClientGetMultiLangCommand{}, state)         //获取前端语言
	cmds.Register(uint32(cl.ID_MSG_C2L_ViewFormation), &C2LViewFormationCommand{}, state)                   //查看玩家战斗数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GlobalAttrScoreGet), &C2LGlobalAttrScoreGetCommand{}, state)         // 查看全局养成评分
	cmds.Register(uint32(cl.ID_MSG_C2L_GetCrossRankFirst), &C2LGetCrossRankFirstCommand{}, state)           // 获取跨服排行榜的首页数据
	cmds.Register(uint32(cl.ID_MSG_C2L_OssBattleReport), &C2L_OssBattleReportCommand{}, state)              // 测试oss战报服务
	cmds.Register(uint32(cl.ID_MSG_C2L_OSSUrl), &C2L_OSSUrlCommand{}, state)                                //获取oss下载地址
	cmds.Register(uint32(cl.ID_MSG_C2L_GetAchieveShowcase), &C2LGetAchieveShowcaseCommand{}, state)         // 获取玩家成就展示
	cmds.Register(uint32(cl.ID_MSG_C2L_GetCognitionLog), &C2LGetCognitionLogCommand{}, state)               // 获取战斗认知日志
	cmds.Register(uint32(cl.ID_MSG_C2L_HasRecvH5DesktopReward), &C2LHasRecvH5DesktopRewardCommand{}, state) // 获取是否已领取h5保存到桌面的奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_RecvH5DesktopReward), &C2LRecvH5DesktopRewardCommand{}, state)       // 领取h5保存到桌面的奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_Test), &C2LTestCommand{}, state)                                     // 测试协议
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonEnter), &C2LSeasonEnterCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_GetSeasonRankFirst), &C2LGetSeasonRankFirstCommand{}, state) // 获取赛季玩法排行榜首页数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GetSeasonRankList), &C2LGetSeasonRankListCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SetPush), &C2LSetPushCommand{}, state)                                 //设置推送标识
	cmds.Register(uint32(cl.ID_MSG_C2L_GetPush), &C2LGetPushCommand{}, state)                                 //获取推送标识
	cmds.Register(uint32(cl.ID_MSG_C2L_GetSeasonFlashBackData), &C2LGetSeasonFlashBackDataCommand{}, state)   // 获取赛季回顾数据
	cmds.Register(uint32(cl.ID_MSG_C2L_GetDefFormationPower), &C2LGetDefFormationPowerCommand{}, state)       // 获取防守阵容
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityScheduleGetData), &C2LActivityScheduleGetDataCommand{}, state) // 获取防守阵容
	cmds.Register(uint32(cl.ID_MSG_C2L_TestEtcdGiftCode), &C2LTestEtcdCodeCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_TestDrop), &C2LTestDropCommand{}, state)       // 掉落测试
	cmds.Register(uint32(cl.ID_MSG_C2L_MuteAccount), &C2LMuteAccountCommand{}, state) // 禁言账号
}

type C2LFlushCommand struct {
	base.UserCommand
}

//nolint:funlen
func (c *C2LFlushCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_Flush{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_Flush Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("C2L_Flush, %d %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_Flush{
		Ret:                   uint32(cret.RET_OK),
		User:                  cmsg.User,
		Bags:                  cmsg.Bags,
		Equips:                cmsg.Equips,
		Artifacts:             cmsg.Artifacts,
		Gems:                  cmsg.Gems,
		Emblems:               cmsg.Emblems,
		GlobalAttr:            cmsg.GlobalAttr,
		Heroes:                cmsg.Heroes,
		Formation:             cmsg.Formation,
		Dungeon:               cmsg.Dungeon,
		Tasks:                 cmsg.Tasks,
		GuildTalent:           cmsg.GuildTalent,
		SummonData:            cmsg.SummonData,
		Crystal:               cmsg.Crystal,
		PushGift:              cmsg.PushGift,
		OperateActivity:       cmsg.OperateActivity,
		Dispatch:              cmsg.Dispatch,
		Rite:                  cmsg.Rite,
		PreSeason:             cmsg.PreSeason,
		ActivityLifelongGifts: cmsg.ActivityLifelongGifts,
		Pokemon:               cmsg.Pokemon,
	}
	if cmsg.GetUser() {
		c.User.SendSelfToClient()
	}
	if cmsg.GetBags() {
		c.User.SendBagsToClient(c.Srv)
	}
	if cmsg.GetEquips() {
		c.User.SendEquipsToClient(nil)
	}
	if cmsg.GetArtifacts() {
		c.User.SendArtifactsToClient()
	}
	if cmsg.GetGems() {
		c.User.SendGemsToClient(nil)
	}
	if cmsg.GetEmblems() {
		c.User.SendEmblemsToClient(nil)
	}
	if cmsg.GetGlobalAttr() {
		c.User.SendGlobalAttrToClient()
	}
	if cmsg.GetSeasonJewelry() {
		c.User.SendSeasonJewelryToClient(nil)
	}
	if cmsg.GetHeroes() {
		c.User.SendHeroesToClient()
	}
	if cmsg.GetFormation() {
		c.User.SendFormationToClient(nil)
	}
	if cmsg.GetDungeon() {
		c.User.Dungeon().SendDungeonToClient()
	}
	if cmsg.GetTasks() {
		c.User.SendTasksToClient(0)
	}
	if cmsg.GetGuildTalent() {
		c.User.GuildTalent().SendGuildTalentToClient()
	}
	if cmsg.GetGuidance() {
		c.User.Guidance().SendGuidanceToClient()
	}
	if cmsg.GetSevendayLogin() {
		c.User.SevenDayLogin().SendDataToClient()
	}
	if cmsg.GetClientInfo() {
		c.User.ClientInfo().SendToClient()
	}
	if cmsg.GetSummonData() {
		c.User.SendSummonDataToClient()
	}
	// if cmsg.GetCrystal() {
	// 	c.User.SendCrystalToClient()
	// }
	if cmsg.GetPushGift() {
		c.User.PushGift().CheckExpireGift(c.Srv)
		c.User.SendPushGiftToClient()
	}
	if cmsg.GetDispatch() {
		c.User.Dispatch().SendDispatchToClient()
	}
	if cmsg.GetRite() {
		c.User.SendRitesToClient()
	}
	if cmsg.GetSkin() {
		c.User.SendSkinsToClient()
	}
	if cmsg.GetPreSeason() {
		c.User.SendPreSeasonToClient(c.Srv)
	}
	if cmsg.ActivityLifelongGifts {
		c.User.ActivityLifelongGifts().FlushGift2Client(nil)
	}
	if cmsg.GetPokemon() {
		c.User.PokemonManager().SendPokemonsToClient()
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Flush, smsg)
	return true
}

type C2LKeepAliveCommand struct {
	base.UserCommand
}

func (c *C2LKeepAliveCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_KeepAlive{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_KeepAlive Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	c.Trace(cmsg)

	l4g.Debugf("C2L_KeepAlive, %d %s", c.Msg.UID, cmsg)

	now := time.Now()
	_, zone := now.Zone()
	ms := now.UnixNano() / 1000000 //nolint:mnd
	smsg := &cl.L2C_KeepAlive{
		Now:         ms,
		Zone:        int32(zone / 3600), //nolint:mnd
		Open:        c.Srv.StartServiceTm(),
		DataVersion: uint32(goxml.GetData().ServerInfoM.Version),
	}
	if ms < cmsg.Now || ms-cmsg.Now > 1000 { //nolint:mnd
		l4g.Debugf("KeepAlive user:%d sync time error: %d %d", c.Msg.UID, ms, cmsg.Now)
	}
	offset := ms - cmsg.Now
	c.User.UpdateKeepAliveTimeOffset(offset)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_KeepAlive, smsg)
	return true
}

type C2LAddPurchaseNumCommand struct {
	base.UserCommand
}

func (c *C2LAddPurchaseNumCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LAddPurchaseNumCommand) Error(msg *cl.L2C_AddPurchaseNum, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_AddPurchaseNum, msg)
	return false
}

func (c *C2LAddPurchaseNumCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_AddPurchaseNum{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_AddPurchaseNum Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_AddPurchaseNum: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_AddPurchaseNum{
		Ret:  uint32(cret.RET_OK),
		Type: cmsg.Type,
		Num:  cmsg.Num,
	}

	if cmsg.Num < 1 {
		l4g.Errorf("user: %d C2L_AddPurchaseNum: param num is 0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	info := goxml.GetData().NumberTypeInfoM.Index(cmsg.Type)
	if info == nil || info.BuyType == goxml.NumBuyForbid || info.CountType != goxml.NumBuyOnly {
		l4g.Errorf("user: %d C2L_AddPurchaseNum: numberTypeInfo not exist, type:%d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.CheckUserNumPurchaseLimit(cmsg.Type, cmsg.Num) {
		l4g.Errorf("user: %d C2L_AddPurchaseNum: CheckUserNumBuyLimit failed, type:%d num:%d",
			c.Msg.UID, cmsg.Type, cmsg.Num)
		return c.Error(smsg, uint32(cret.RET_BUY_COUNT_LIMIT))
	}

	retn, costs := c.User.GetPurchaseNumCostRes(cmsg.Type, cmsg.Num, info)
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_AddPurchaseNum: getPurchaseCost failed, ret:%d", c.Msg.UID, retn)
		return c.Error(smsg, retn)
	}
	cRet := c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_ADD_PURCHASE_NUM), 0)
	if cRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_AddPurchaseNum: consume failed, ret:%d", c.Msg.UID, cRet)
		return c.Error(smsg, cRet)
	}

	smsg.PurchaseNum = c.User.AddPurchaseNum(cmsg.Type, cmsg.Num)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_AddPurchaseNum, smsg)
	c.User.LogAddPurchaseNum(c.Srv, cmsg.Type, cmsg.Num, uint32(common.PURCHASE_TYPE_PT_NORMAL))
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeBuyNumberTypeCount, uint64(cmsg.Num), cmsg.Type)
	return c.ResultOK(smsg.Ret)
}

type C2LGMCommand struct {
	base.LimitedCommand
}

func (c *C2LGMCommand) Error(msg *cl.L2C_AddPurchaseNum, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GM, msg)
	return false
}

func (c *C2LGMCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGMCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GM{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GM Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GM: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GM{
		Ret: uint32(cret.RET_OK),
	}
	if len(cmsg.Awards) > 0 {
		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, cmsg.Awards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_GM), 0)
	}
	if cmsg.TestData != 0 {
		_, smsg.Awards = character.InitTestData(c.Srv, c.User, 0, cmsg.TestData)
	}

	if cmsg.GuildLevel != 0 {
		c.gmAutoJoinGuild(cmsg.GuildLevel)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GM, smsg)
	return c.ResultOK(smsg.Ret)
}

// 自动加入公会 - 仅用于robot
// 规则：有空闲公会则直接加入，没有空闲公会则创建
//
//nolint:unparam
func (c *C2LGMCommand) gmAutoJoinGuild(guildLv uint32) uint64 {
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GM failed, guildManager not exist", c.Msg.UID)
		return 0
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GM failed, guild cross not connect", c.Msg.UID)
		return 0
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser != nil && guildM.GetGuildByUser(c.User.ID()) != nil {
		l4g.Errorf("user: %d C2L_GM user has guild", c.Msg.UID)
		return 0
	}

	if guildUser == nil {
		guildUser = guildM.NewGuildUser(c.User.ID(), c.User.Name())
	}
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GM: guildUser create error.", c.Msg.UID)
		return 0
	}

	//请求跨服加入或创建公会
	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildJoinOrCreateForGM, c.Msg.UID, &l2c.L2C_GuildJoinOrCreateForGM{
		Name:       c.User.UUID(),
		Level:      c.User.Level(),
		Power:      c.User.Power(),
		NewGid:     c.Srv.CreateUniqueID(),
		GuildLevel: guildLv,
	})
	return 0
}

type C2LGetCommonRankCommand struct {
	base.UserCommand
}

func (c *C2LGetCommonRankCommand) Error(msg *cl.L2C_GetCommonRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRank, msg)
	return true
}

func (c *C2LGetCommonRankCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetCommonRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetCommonRank Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	rankID := cmsg.RankId
	smsg := &cl.L2C_GetCommonRank{
		Ret:    uint32(cret.RET_OK),
		RankId: rankID,
	}

	if rankID == 0 || !goxml.LegalCommonRankId[rankID] {
		l4g.Errorf("user: %d C2L_GetCommonRank: param error. %d", c.Msg.UID, rankID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	rankInfo := goxml.GetData().RankingInfoM.Index(rankID)
	if rankInfo == nil {
		l4g.Errorf("C2L_GetCommonRank config not exist:%d %d", c.Msg.UID, rankID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	list := c.Srv.CommonRankM().GetRangeByRank(rankID, 1, rankInfo.ShowCount)
	if len(list) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRank, smsg)
		return true
	}

	if rankID == goxml.GuildDungeonChapterRankId {
		return c.guildDungeonChapterRank(list, smsg)
	}

	if rankID == goxml.GuildLevelRankID {
		l4g.Errorf("C2L_GetCommonRank config not exist:%d %d", c.Msg.UID, rankID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		//return c.guildLevelRank(list, smsg)
	}

	//if rankID == goxml.GuildDungeonWeeklyDamageRankId {
	//	return c.guildDungeonWeeklyDamageRank(list, smsg)
	//}

	if rankID == goxml.ActivityTowerRankID {
		if !goxml.GetData().ConfigInfoM.ActivityTowerShow(c.Srv.ServerOpenDay(time.Now().Unix()), c.User.Level()) {
			l4g.Errorf("C2L_GetCommonRank rank not in showtime. %d %d", c.Msg.UID, rankID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	if rankID == goxml.ActivityMirageRankID {
		if !goxml.GetData().ConfigInfoM.ActivityMirageShow(c.Srv.ServerOpenDay(time.Now().Unix()), c.User.Level()) {
			l4g.Errorf("C2L_GetCommonRank rank not in showtime. %d %d", c.Msg.UID, rankID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	userIds := make([]uint64, 0, len(list))
	rankValues := make([]*cl.RankValue, 0, len(list))

	for _, v := range list {
		userIds = append(userIds, v.(rank.CommonRankValuer).Key())
		rankValues = append(rankValues, v.(rank.CommonRankValuer).ShowValue())
	}
	smsg.List = rankValues
	selfRank := c.Srv.CommonRankM().GetRank(rankID, c.Msg.UID)
	smsg.SelfRank = selfRank
	if selfRank > 0 {
		selfRankElement := c.Srv.CommonRankM().GetElement(rankID, c.Msg.UID)
		smsg.SelfValue = selfRankElement.(rank.CommonRankValuer).ShowValue()
	} else {
		//不在榜上,取自己的数据
		smsg.SelfValue = c.User.GetDefaultRankValue(rankID, c.Srv)
	}
	req := &AsyncC2LGetCommonRankReq{
		smsg: smsg,
	}
	c.GetLocalUserSnapshots(userIds, req, 0)
	return true
}

type AsyncC2LGetCommonRankReq struct {
	smsg *cl.L2C_GetCommonRank
}

func (ar *AsyncC2LGetCommonRankReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LCommonRankCommand: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRank, smsg)
		return false
	}
	users := dbData.([]*cl.UserSnapshot)
	snapm := make(map[uint64]*cl.UserSnapshot, len(users))
	for _, v := range users {
		snapm[v.Id] = v
	}
	for _, v := range smsg.List {
		if snapm[v.Id] == nil {
			l4g.Errorf("user: %d C2L_CommonRank: get snapshot nil. id:%d",
				args.UID, v.Id)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRank, smsg)
			return true
		}
		v.User = snapm[v.Id]
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRank, smsg)
	return true
}

func (c *C2LGetCommonRankCommand) guildDungeonChapterRank(_ []interface{}, smsg *cl.L2C_GetCommonRank) bool {
	// todo
	//guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	//if !ok {
	//	l4g.Errorf("user: %d C2L_GetCommonRank failed, guildManager not exist", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_ERROR))
	//}
	//getGuildDungeonChapterBaseRankData(guildM, list, smsg)
	//
	//guildDungeon := guildM.GetGuildDungeon(c.User.ID())
	//guild := guildM.GetGuildByUser(c.User.ID(), c.Srv)
	//if guildDungeon == nil || guild == nil {
	//	l4g.Debugf("user: %d C2L_GetCommonRank, guildDungeon is nil", c.Msg.UID)
	//	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRank, smsg)
	//	return true
	//}
	//selfRank := c.Srv.CommonRankM().GetRank(goxml.GuildDungeonChapterRankId, guild.ID())
	//smsg.SelfRank = selfRank
	//selfRankElement := c.Srv.CommonRankM().GetElement(goxml.GuildDungeonChapterRankId, guild.ID())
	//var selfValue *cl.RankValue
	//if selfRank > 0 {
	//	selfValue = selfRankElement.(rank.CommonRankValuer).ShowValue()
	//} else {
	//	selfValue = rank.NewGuildDungeon(guild.ID(), guildDungeon.GetChapter(), guildDungeon.GetChapterRate(), time.Now().Unix()).ShowValue()
	//}
	//smsg.SelfValue = &cl.RankValue{
	//	Id:         guild.ID(),
	//	Value:      selfValue.Value,
	//	Guild:      guild.Snapshot(),
	//	Param1:     selfValue.Param1,
	//	Param2:     selfValue.Param2,
	//	LikedCount: guildDungeon.GetLikedCount(),
	//}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRank, smsg)
	return true
}

func getOneGuildBaseRankData(guild *aguild.Guild, rankValue *cl.RankValue) *cl.RankValue {
	return &cl.RankValue{
		Id:    guild.ID(),
		Value: rankValue.Value,
		//Guild:  guild.Snapshot(),
		Param1: rankValue.Param1,
		Param2: rankValue.Param2,
	}
}

// TODO:这个协议目前做了兼容性处理，后续可以换掉
type C2LViewUserCommand struct {
	base.UserCommand
}

func (c *C2LViewUserCommand) Error(msg *cl.L2C_ViewUser, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ViewUser, msg)
	return false
}

func (c *C2LViewUserCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LViewUserCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ViewUser{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ViewUser Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ViewUser: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ViewUser{
		Ret:         uint32(cret.RET_OK),
		FormationId: cmsg.FormationId,
	}

	if cmsg.FormationId == 0 {
		l4g.Errorf("user: %d C2L_ViewUser: param is 0, uid:%d serverID:%d fid:%d ",
			c.Msg.UID, cmsg.Id, cmsg.ServerId, cmsg.FormationId)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	/*
		if cmsg.Id == c.Msg.UID {
			l4g.Errorf("user: %d C2L_ViewUser: view self", c.Msg.UID)
			return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
	*/
	//检查是否操作太频繁
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIViewUser) {
		l4g.Errorf("user: %d C2L_ViewUser: operate too often", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_OPERATE_TOO_OFTEN))
	}
	//检查跨服的查询一次不能超过50个人
	if len(cmsg.Users) > 50 { //nolint:mnd
		l4g.Errorf("user: %d C2L_ViewUser: too much user", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	//todo 加一个异步的锁
	req := &AsyncC2LViewUserReq{
		smsg: smsg,
	}

	var reqUsers []*cl.ServerUser
	if cmsg.Id > 0 {
		reqUsers = append(reqUsers, &cl.ServerUser{Sid: cmsg.ServerId, Uid: cmsg.Id})
	} else {
		if len(cmsg.Users) == 0 {
			l4g.Errorf("user: %d C2L_ViewUser: users is 0, fid:%d ",
				c.Msg.UID, cmsg.FormationId)
			return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		reqUsers = cmsg.Users
	}

	//查看跨服user
	c.GetUserSnapshotsFromAll(reqUsers, req, cmsg.FormationId)
	return true
}

type AsyncC2LViewUserReq struct {
	smsg *cl.L2C_ViewUser
}

func (ar *AsyncC2LViewUserReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_ViewUser: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewUser, smsg)
		return false
	}

	snapshots := dbData.([]*cl.UserSnapshot)
	if len(snapshots) == 0 {
		l4g.Errorf("user: %d C2L_ViewUser: get snapshot failed, no data", args.UID)
		smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewUser, smsg)
		return false
	}

	if len(snapshots) == 1 {
		smsg.User = snapshots[0]
	} else {
		smsg.Users = snapshots
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewUser, smsg)
	return true
}

type C2LViewFormationCommand struct {
	base.UserCommand
}

func (c *C2LViewFormationCommand) Error(msg *cl.L2C_ViewFormation, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ViewFormation, msg)
	return false
}

func (c *C2LViewFormationCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(ret.RET_OK)
}

func (c *C2LViewFormationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ViewFormation{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d C2L_ViewFormation Unmarshal error: %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ViewFormation: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ViewFormation{
		Ret: uint32(ret.RET_OK),
		Uid: cmsg.Uid,
		Fid: cmsg.Fid,
	}

	if cmsg.Uid == 0 || cmsg.ServerId == 0 {
		l4g.Errorf("user: %d C2L_ViewFormation: param is 0, uid:%d serverID:%d fid:%d",
			c.Msg.UID, cmsg.Uid, cmsg.ServerId, cmsg.Fid)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.Uid == c.Msg.UID {
		l4g.Errorf("user: %d C2L_ViewFormation: view self", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.Fid != 0 && !goxml.GetData().FormationInfoM.IsLegalFormation(cmsg.Fid) {
		l4g.Errorf("user: %d C2L_ViewFormation formation id illegal: %d", c.Msg.UID, cmsg.Fid)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//检查是否操作太频繁
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIViewFormation) {
		l4g.Errorf("user: %d C2L_ViewFormation: operate too often", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_OPERATE_TOO_OFTEN))
	}

	//todo 加一个异步的锁
	req := &AsyncC2LViewFormationReq{
		uid:  cmsg.Uid,
		smsg: smsg,
		fid:  cmsg.Fid,
	}

	//查看跨服user
	reqUsers := []*cl.ServerUser{{Sid: cmsg.ServerId, Uid: cmsg.Uid}}
	c.GetUserBattleDataFromAll(reqUsers, req, cmsg.Fid)
	return true
}

type AsyncC2LViewFormationReq struct {
	uid  uint64
	smsg *cl.L2C_ViewFormation
	fid  uint32
}

func (ar *AsyncC2LViewFormationReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	l4g.Debugf("user: %d C2L_ViewFormation: get data. uid:%d data:%+v", args.UID, ar.uid, dbData)

	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_ViewFormation: get battle data error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewFormation, smsg)
		return false
	}

	datas := dbData.([]*db.UserBattleData)
	if len(datas) != 1 || datas[0] == nil {
		l4g.Errorf("user: %d C2L_ViewFormation: get battle data nil:%d",
			args.UID, ar.uid)
		smsg.Ret = uint32(cret.RET_FORMATION_NOT_EXIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewFormation, smsg)
		return false
	}

	user := character.GetUserFromUserBattleData(srv, datas[0])
	if user == nil {
		l4g.Errorf("user: %d C2L_ViewFormation: GetUserFromUserBattleData is nil:%d, data:%+v",
			args.UID, ar.uid, datas[0])
		smsg.Ret = uint32(cret.RET_FORMATION_NOT_EXIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewFormation, smsg)
		return false
	}
	battleData := user.FlushBattleData(srv, ar.fid, ar.fid == 0)
	if battleData == nil {
		l4g.Errorf("user: %d C2L_ViewFormation: get battle nil:%d", args.UID, ar.uid)
		if ar.fid == uint32(common.FORMATION_ID_FI_DUEL_1) || ar.fid == uint32(common.FORMATION_ID_FI_DUEL_2) || ar.fid == uint32(common.FORMATION_ID_FI_DUEL_3) {
			smsg.Ret = uint32(cret.RET_DUEL_RIVAL_FORMATION_NOT_EXIST)
		} else {
			smsg.Ret = uint32(cret.RET_FORMATION_NOT_EXIST)
		}
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewFormation, smsg)
		return false
	}

	//添加巅峰竞技场赛季积分
	if battleData.User != nil {
		rankScore := getPeakSeasonRankScore(srv, ar.uid)
		if rankScore != nil {
			battleData.User.PeakScore = rankScore.Score
			battleData.User.PeakRank = rankScore.Rank
		}
	}

	smsg.Data = battleData
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ViewFormation, smsg)
	return true
}

func getPeakSeasonRankScore(srv command.Servicer, uid uint64) *cl.PeakRankScore {
	manager, ok := srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("getPeakSeasonRankScore: manager not exist")
		return nil
	}

	return manager.GetRankScore(uid)
}

type C2LFormationCommand struct {
	base.UserCommand
}

func (c *C2LFormationCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFormationCommand) Error(msg *cl.L2C_Formation, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Formation, msg)
	return false
}

//nolint:funlen
func (c *C2LFormationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_Formation{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d C2L_Formation: Unmarshal error. error:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_Formation: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_Formation{
		Ret:         uint32(cret.RET_OK),
		FormationId: cmsg.FormationId,
		Formation:   cmsg.Formation,
	}
	// if cmsg.Formation == nil || len(cmsg.Formation.Teams) == 0 {
	if cmsg.Formation == nil {
		l4g.Errorf("user: %d C2L_Formation: data error nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	formationID := cmsg.FormationId
	formation := cmsg.Formation
	if !goxml.GetData().FormationInfoM.IsLegalFormation(formationID) {
		l4g.Errorf("user: %d C2L_Formation: formationID error. formationID:%d", c.Msg.UID, formationID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	if formationID != formation.Id {
		l4g.Errorf("user: %d C2L_Formation: formationID error. formationID:%d id:%d",
			c.Msg.UID, formationID, formation.Id)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if formationID == uint32(common.FORMATION_ID_FI_GST) {
		gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
		if !ok {
			l4g.Errorf("user:%d C2L_Formation set gst formation error gst manager is nil", c.User.ID())
			return c.Error(smsg, uint32(ret.RET_GST_USER_CANT_OPERATE))
		}
		if gstM.GetCrossGst() != nil && gstM.GetCrossGst().Stage >= cl.GST_STAGE_RoundFightState_Fight && gstM.GetCrossGst().Stage <= cl.GST_STAGE_RoundFightState_Reward {
			return c.Error(smsg, uint32(ret.RET_GST_USER_CANT_OPERATE))
		}

		gstUser := gstM.GetUser(c.User.ID())
		if gstUser == nil {
			l4g.Errorf("user:%d C2L_Formation get gst user failed", c.Msg.UID)
			return c.Error(smsg, uint32(ret.RET_GST_USER_NOT_SIGN))
		}

		if !gstUser.CheckDispatchHero(formation) {
			l4g.Errorf("user:%d C2L_Formation CheckDispatchHero failed", c.Msg.UID)
			return c.Error(smsg, uint32(ret.RET_GST_DISPATCH_HERO_FORMATION_REPEATER))
		}
	}

	if !c.checkFuncOpen(cmsg.FormationId) {
		l4g.Errorf("user: %d C2LFormationCommand: function not open, formationID:%d", c.Msg.UID, cmsg.FormationId)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flag, teamNum := c.User.GetTeamNum(cmsg.FormationId)
	if !flag {
		l4g.Errorf("user: %d C2L_Formation: GetTeamNum failed. formationID:%d", c.Msg.UID, formationID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if formationID != uint32(common.FORMATION_ID_FI_GST_CHALLENGE) {
		if len(cmsg.Formation.Teams) != 0 && len(cmsg.Formation.Teams) != teamNum {
			l4g.Errorf("user: %d C2L_Formation: team num error. reqTeamNum:%d teamNum:%d",
				c.Msg.UID, len(cmsg.Formation.Teams), teamNum)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}
	} else {
		gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
		if !ok {
			l4g.Errorf("user:%d C2L_Formation set gst formation error gst manager is nil", c.User.ID())
			return c.Error(smsg, uint32(ret.RET_GST_USER_CANT_OPERATE))
		}
		gstUser := gstM.GetUser(c.User.ID())
		if gstUser == nil {
			l4g.Errorf("user:%d C2L_Formation get gst user failed", c.Msg.UID)
			return c.Error(smsg, uint32(ret.RET_GST_USER_NOT_SIGN))
		}
		if gstUser.GetChallengeFormationLimit() {
			l4g.Errorf("user:%d C2L_Formation set gst challenge limit", c.User.ID())
			return c.Error(smsg, uint32(ret.RET_GST_USER_CANT_OPERATE))
		}
	}

	smsg.Ret = c.FormationCheckOccupy(cmsg)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_Formation: FormationCheckOccupy failed. formationID:%d", c.Msg.UID, formationID)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.Ret = c.FormationCheck(cmsg)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_Formation: FormationCheck failed. formationID:%d id:%d", c.Msg.UID, formationID, formation.Id)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.Ret = c.FormationCheckLockPos(cmsg)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_Formation: FormationCheckLockPos failed. formationID:%d", c.Msg.UID, formationID)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.FormationManager().NewFormation(c.Srv, formationID, formation)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Formation, smsg)
	c.User.LogFormation(c.Srv, cmsg.Formation)

	//竞技场 - 更新玩家防守战力
	if formationID == uint32(common.FORMATION_ID_FI_ARENA_DEFENSE) {
		arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
		arenaUser := arenaM.Get(c.User.ID())
		if arenaUser != nil {
			arenaUser.CheckUpdatePower(c.User.ArenaDefensePower(), true)
			arenaM.SetChange(c.Srv, arenaUser)
		}
	}
	if formationID == uint32(common.FORMATION_ID_FI_GST) {
		gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
		if !ok {
			return c.ResultOK(smsg.Ret)
		}
		crossMsg := c.convert2GSTTeam(cmsg.Formation.Teams)
		needSync := gstM.NeedSyncUserTeamInfo(c.Msg.UID, true)
		crossMsg.IsHand = true
		if needSync && gstM.GetCrossGst() != nil && !(gstM.GetCrossGst().Stage >= cl.GST_STAGE_RoundFightState_Fight && gstM.GetCrossGst().Stage <= cl.GST_STAGE_RoundFightState_Reward) {
			if gstM.IsCrossConnected() {
				if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTSetTeam, c.Msg.UID, crossMsg) {
					l4g.Errorf("user: %d L2CS_GSTSetTeam: cross maintain", c.Msg.UID)
					return c.ResultOK(smsg.Ret)
				}
			}
		}
		return c.ResultOK(smsg.Ret)
	}
	_, exist := goxml.SeasonArenaDefender2Attack[formationID]
	if exist {
		seasonArenaM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
		if !ok {
			return c.ResultOK(smsg.Ret)
		}
		if !seasonArenaM.IsCrossConnected() {
			return c.ResultOK(smsg.Ret)
		}
		saUser := seasonArenaM.GetUser(c.User.ID())
		if saUser != nil && saUser.IsCurSeasonRound(seasonArenaM.CrossState.GetSeason(), seasonArenaM.CrossState.GetRound()) {
			defPower := c.User.CalFormationPower(formationID)
			crossMsg := &l2c.L2CS_SeasonArenaUpdateFormationPower{
				Data: []*cl.FormationSidUidPower{
					{
						FormationSidUid: &cl.FormationSidUid{
							FormationId: formationID,
							Sid:         c.Srv.ServerID(),
							Uid:         c.User.ID(),
						},
						DefPower: defPower,
					},
				},
			}
			c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaUpdateFormationPower, 0, crossMsg)
		}
	}

	return c.ResultOK(smsg.Ret)
}

func (c *C2LFormationCommand) convert2GSTTeam(teams []*cl.FormationTeamInfo) *l2c.L2CS_GSTSetTeam {
	ret := &l2c.L2CS_GSTSetTeam{}
	ret.FormationId = uint32(common.FORMATION_ID_FI_GST)
	ret.Formation = make([]*cl.GSTSetFormationTeamInfo, 0, len(teams))
	for index, team := range teams {
		gstTeam := &cl.GSTSetFormationTeamInfo{
			Heros:     make([]*cl.GSTTeamHeroInfo, 0, len(teams)),
			TeamIndex: uint32(index),
		}

		for _, hero := range team.Info {
			heroData := c.User.HeroManager().Get(hero.Hid)
			if heroData == nil {
				l4g.Errorf("uid:%d convert2Gst hero id:%d failed", c.Msg.UID, hero.Hid)
				return nil
			}
			gstHero := &cl.GSTTeamHeroInfo{
				Pos:   hero.Pos,
				Star:  heroData.GetStar(),
				SysId: heroData.GetHeroSysID(),
			}
			gstTeam.Heros = append(gstTeam.Heros, gstHero)
		}

		ret.Formation = append(ret.Formation, gstTeam)
	}

	return ret
}

// 根据阵容id，判断玩法是否开启
func (c *C2LFormationCommand) checkFuncOpen(formationID uint32) bool {
	info := goxml.GetData().FormationInfoM.Index(formationID)
	if info == nil {
		l4g.Errorf("user: %d C2LFormationCommand: function info not exist:%d", c.Msg.UID, formationID)
		return false
	}
	if !c.User.IsFunctionOpen(info.FunctionId, c.Srv) {
		l4g.Errorf("user: %d C2LFormationCommand: function not open, functionID:%d", c.Msg.UID, info.FunctionId)
		return false
	}
	return true
}

//nolint:funlen
func (c *C2LFormationCommand) FormationCheck(cmsg *cl.C2L_Formation) uint32 {
	newFormation := cmsg.Formation
	heroes := make(map[uint64]struct{}, len(newFormation.Teams)*int(character.FormationMaxPos))
	sysHeroes := make(map[uint32]struct{}, len(newFormation.Teams)*int(character.FormationMaxPos))
	artifacts := make(map[uint32]struct{}) // 神器
	remains := make(map[uint32]struct{})   // 遗物
	var helpMonster map[uint32]struct{}
	isSeasonFunction := c.isSeasonFunction(cmsg.FormationId)
	for index, team := range newFormation.Teams {
		if (team == nil || len(team.Info) == 0) && cmsg.FormationId == uint32(common.FORMATION_ID_FI_GST_CHALLENGE) {
			continue
		}
		//至少有一个布阵位置
		if len(team.Info) < 1 || len(team.Info) > int(character.FormationMaxPos) {
			l4g.Errorf("user: %d C2L_Formation: date error len error. infoLen:%d", c.Msg.UID, len(team.Info))
			return uint32(cret.RET_ERROR)
		}
		if len(team.Artifacts) > 3 { //nolint:mnd
			l4g.Errorf("user: %d C2L_Formation: date error len error. artifactLen:%d", c.Msg.UID, len(team.Artifacts))
			return uint32(cret.RET_ERROR)
		}
		poses := make(map[uint32]struct{})
		userHeroNum := 0              //玩家上阵英雄数量
		var omniheroCnt uint32        // 全能英雄数量
		for _, v := range team.Info { //nolint:varnamelen
			if v.Uid > 0 {
				l4g.Errorf("user: %d C2L_Formation: uid error. formationUid:%d", c.Msg.UID, v.Uid)
				return uint32(cret.RET_ERROR)
			}
			if _, exist := heroes[v.Hid]; exist {
				l4g.Errorf("user: %d C2L_Formation: heroes error. funcId:%d Hid:%d", c.Msg.UID, newFormation.Id, v.Hid)
				return uint32(cret.RET_ERROR)
			}
			heroes[v.Hid] = struct{}{}

			var keyID uint32
			var sysID uint32
			if v.Hid > math.MaxUint32 {
				userHero := c.User.HeroManager().Get(v.Hid)
				if userHero == nil {
					l4g.Errorf("user: %d C2L_Formation: no hero, teamIndex:%d hid:%d", c.Msg.UID, index, v.Hid)
					return uint32(cret.RET_ERROR)
				}

				if !userHero.IsInBattleList() {
					l4g.Errorf("user: %d C2L_Formation: hero not in battle list. formationId:%d Hid:%d",
						c.Msg.UID, newFormation.Id, v.Hid)
					return uint32(cret.RET_HERO_NOT_IN_CRYSTAL)
				}

				keyID = userHero.GetData().SysId
				sysID = keyID
				if _, exist := sysHeroes[keyID]; exist {
					l4g.Errorf("user: %d C2L_Formation: heroes same error. hid:%d keyID:%d", c.Msg.UID, v.Hid, keyID)
					return uint32(cret.RET_ERROR)
				}
				sysHeroes[keyID] = struct{}{}
				userHeroNum++
			} else if monsterInfo := goxml.GetData().MonsterInfoM.Index(uint32(v.Hid)); monsterInfo != nil {
				keyID = uint32(v.Hid)
				sysID = monsterInfo.HeroId
				if _, exist := helpMonster[keyID]; exist {
					l4g.Errorf("user: %d C2L_Formation: monster same error. Hid:%d keyID:%d", c.Msg.UID, v.Hid, keyID)
					return uint32(cret.RET_ERROR)
				}
				if helpMonster == nil {
					helpMonster = make(map[uint32]struct{})
				}
				helpMonster[keyID] = struct{}{}
			} else {
				l4g.Errorf("user: %d C2L_Formation: get hero nil. Hid:%d", c.Msg.UID, v.Hid)
				return uint32(cret.RET_ERROR)
			}

			if _, exist := poses[v.Pos]; exist {
				l4g.Errorf("user: %d C2L_Formation: poses error. formationInfo:%+v", c.Msg.UID, team.Info)
				return uint32(cret.RET_ERROR)
			}
			poses[v.Pos] = struct{}{}
			if v.Pos == 0 || v.Pos > character.FormationMaxPos {
				l4g.Errorf("user: %d C2L_Formation: pos len error. pos:%d", c.Msg.UID, v.Pos)
				return uint32(cret.RET_ERROR)
			}

			// 全能英雄相关验证
			info := goxml.GetData().HeroInfoM.Index(sysID)
			if info == nil {
				l4g.Errorf("user: %d C2L_Formation: no hero info, hid:%d, sysID:%d", c.Msg.UID, v.Hid, sysID)
				return uint32(cret.RET_SYSTEM_DATA_ERROR)
			}
			if info.Omnihero == 1 {
				// 最多只能有一个全能英雄
				omniheroCnt++
				if omniheroCnt > 1 {
					l4g.Errorf("user: %d C2L_Formation: omnihero num error. formationInfo:%+v", c.Msg.UID, team.Info)
					return uint32(cret.RET_FORMATION_OMNIHERO_COUNT_EXCEED)
				}

				// 验证选择的羁绊
				linkType := make(map[uint32]struct{})
				for typ, linkID := range v.Links {
					linkInfo := goxml.GetData().LinkInfoM.Index(linkID)
					if linkInfo == nil {
						l4g.Errorf("user: %d C2L_Formation: no link info, hid:%d, sysID:%d, linkID:%d",
							c.Msg.UID, v.Hid, sysID, linkID)
						return uint32(cret.RET_SYSTEM_DATA_ERROR)
					}

					if typ != linkInfo.LinkSubtype {
						l4g.Errorf("user: %d C2L_Formation: type not match. %d-%d formationInfo:%+v",
							c.Msg.UID, typ, linkInfo.ChangeType, team.Info)
						return uint32(cret.RET_FORMATION_OMNIHERO_LINK_TYPE_ERR)
					}

					// 所需羁绊类型错误
					if linkInfo.LinkSubtype == goxml.LinkTypeChange {
						l4g.Errorf("user: %d C2L_Formation: link type illegal. formationInfo:%+v", c.Msg.UID, team.Info)
						return uint32(cret.RET_FORMATION_OMNIHERO_LINK_TYPE_ERR)
					}

					// 所需羁绊类型重复
					if _, exist := linkType[linkInfo.LinkSubtype]; exist {
						l4g.Errorf("user: %d C2L_Formation: link type repeat. formationInfo:%+v", c.Msg.UID, team.Info)
						return uint32(cret.RET_FORMATION_OMNIHERO_LINK_TYPE_ERR)
					}
					linkType[linkInfo.LinkSubtype] = struct{}{}

					// 赛季羁绊没激活
					if linkInfo.ChangeType == goxml.LinkTypeSeason {
						if !c.User.SeasonLink().IsHeroSeasonLinkActivated(sysID) {
							l4g.Errorf("user: %d C2L_Formation: season link not active. formationInfo:%+v", c.Msg.UID, team.Info)
							return uint32(cret.RET_FORMATION_OMNIHERO_LINK_NOT_ACTIVE)
						}

						// 验证羁绊所属赛季与当前赛季是否吻合
						if c.User.GetSeasonID() != linkInfo.SeasonLinkId {
							l4g.Errorf("user: %d C2L_Formation: season link not match season. formationInfo:%+v, season:%d",
								c.Msg.UID, team.Info, c.User.GetSeasonID())
							return uint32(cret.RET_FORMATION_OMNIHERO_SEASON_LINK_NOT_MATCH)
						}
					}
				}
			} else {
				// 非全能英雄选择了羁绊
				if len(v.Links) > 0 {
					l4g.Errorf("user: %d C2L_Formation: not omnihero have links. formationInfo:%+v", c.Msg.UID, team.Info)
					return uint32(cret.RET_FORMATION_NOT_OMNIHERO_HAVE_LINKS)
				}
			}
		}
		if userHeroNum <= 0 {
			l4g.Errorf("user: %d C2L_Formation: user hero 0. reqMsg:%+v", c.Msg.UID, team)
			return uint32(cret.RET_ERROR)
		}

		artsPoses := make(map[uint32]struct{}) // 神器位置
		for k, v := range team.Artifacts {     //nolint:varnamelen
			artifact := c.User.ArtifactManager().GetArtifact(v.Aid)
			if artifact == nil {
				l4g.Errorf("user: %d C2L_Formation: get artifact nil. artifactid:%d", c.Msg.UID, v.Aid)
				return uint32(cret.RET_ERROR)
			}

			if v.Pos != uint32(k+1) {
				l4g.Errorf("user: %d C2L_Formation: artifact pos error. artifactid:%d, pos:%d needpos:%d",
					c.Msg.UID, v.Aid, v.Pos, k+1)
				return uint32(cret.RET_ERROR)
			}

			if _, exist := artifacts[v.Aid]; exist {
				l4g.Errorf("user: %d C2L_Formation: artifacts same error. aid:%d", c.Msg.UID, v.Aid)
			}
			artifacts[v.Aid] = struct{}{}

			if _, exist := artsPoses[v.Pos]; exist {
				l4g.Errorf("user: %d C2L_Formation: artifact poses error. artifacts:%+v", c.Msg.UID, team.Artifacts)
				return uint32(cret.RET_ERROR)
			}
			artsPoses[v.Pos] = struct{}{}
			if v.Pos == 0 || v.Pos > character.FormationArtifMaxPos {
				l4g.Errorf("user: %d C2L_Formation: artifact pos len error. pos:%d", c.Msg.UID, v.Pos)
				return uint32(cret.RET_ERROR)
			}
		}

		// 仪式：被屏蔽
		if team.RiteInfo != nil {
			team.RiteInfo = nil
		}

		// 遗物：多队不可以重复上阵
		for _, v := range team.RemainInfo {
			if _, exist := remains[v.Id]; exist {
				l4g.Errorf("user: %d C2L_Formation: remain same error. aid:%d", c.Msg.UID, v.Id)
				return uint32(cret.RET_CLIENT_REQUEST_ERROR)
			}
			remains[v.Id] = struct{}{}
		}

		// 检查上阵的遗物是否合法
		if c.User.RemainM().IsCheckRemainWithFormation(team) {
			retCode := c.User.RemainM().CheckRemainWithFormation(c.Srv, cmsg.FormationId, team, isSeasonFunction)
			if retCode != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2L_Formation: remain data is invalid. ret:%d", c.Msg.UID, retCode)
				return retCode
			}
		}
	}
	return uint32(cret.RET_OK)
}

// 检查阵容占用
//
//nolint:funlen
func (c *C2LFormationCommand) FormationCheckOccupy(cmsg *cl.C2L_Formation) uint32 {
	switch cmsg.FormationId {
	case uint32(common.FORMATION_ID_FI_SEASON_DOOR_1),
		uint32(common.FORMATION_ID_FI_SEASON_DOOR_2),
		uint32(common.FORMATION_ID_FI_SEASON_DOOR_3),
		uint32(common.FORMATION_ID_FI_SEASON_DOOR_4),
		uint32(common.FORMATION_ID_FI_SEASON_DOOR_5),
		uint32(common.FORMATION_ID_FI_SEASON_DOOR_6),
		uint32(common.FORMATION_ID_FI_SEASON_DOOR_7),
		uint32(common.FORMATION_ID_FI_SEASON_DOOR_8):
		return c.checkSeasonDoorFormationOccupy(cmsg)
	}
	return uint32(cret.RET_OK)
}

//nolint:funlen
func (c *C2LFormationCommand) checkSeasonDoorFormationOccupy(cmsg *cl.C2L_Formation) uint32 {
	user := c.User
	if !user.SeasonDoor().CheckFormation(cmsg.FormationId, cmsg.Formation.Teams) {
		l4g.Errorf("SeasonDoor CheckFormation err")
		return uint32(cret.RET_ERROR)
	}
	return uint32(cret.RET_OK)
}

// 检查队伍锁定位置
func (c *C2LFormationCommand) FormationCheckLockPos(cmsg *cl.C2L_Formation) uint32 {
	info := goxml.GetData().FormationInfoM.Index(cmsg.FormationId)
	if info == nil {
		l4g.Errorf("user: %d C2LFormationCommand: FormationCheckLockPos function info not exist:%d",
			c.Msg.UID, cmsg.FormationId)
		return uint32(cret.RET_ERROR)
	}

	if len(info.TeamPosLock) == 0 {
		return uint32(cret.RET_OK)
	}

	for index, team := range cmsg.Formation.Teams {
		lockList := info.TeamPosLock[index]
		if len(lockList) == 0 {
			continue
		}

		for _, v := range team.Info { //nolint:varnamelen
			if util.InUint32s(lockList, v.Pos) {
				l4g.Errorf("user: %d C2LFormationCommand: FormationCheckLockPos pos locked. formationId:%d pos:%d",
					c.Msg.UID, cmsg.FormationId, v.Pos)
				return uint32(cret.RET_ERROR) // lbTODO 待添加专属错误码
			}
		}
	}
	return uint32(cret.RET_OK)
}

// 是否当前赛季玩法
func (c *C2LFormationCommand) isSeasonFunction(formationId uint32) bool {
	formationInfo := goxml.GetData().FormationInfoM.Index(formationId)
	if formationInfo == nil {
		return false
	}
	return goxml.IsSeasonFunctionOpen(goxml.GetData(), time.Now().Unix(), formationInfo.FunctionId)
}

type C2LGetFormationCommand struct {
	base.UserCommand
}

func (c *C2LGetFormationCommand) Error(msg *cl.L2C_GetFormation, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetFormation, msg)
	return true
}

func (c *C2LGetFormationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetFormation{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("user: %d C2L_GetFormation: Unmarshal error. error:%s", c.Msg.UID, err)
		return false
	}

	l4g.Debugf("user: %d C2L_GetFormation: %s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_GetFormation{
		Ret:          uint32(cret.RET_OK),
		FormationIds: cmsg.FormationIds,
	}

	if len(cmsg.FormationIds) > len(goxml.GetData().FormationInfoM.GetAllFids()) {
		l4g.Errorf("user: %d C2L_GetFormation: formationIDs too much. formationIDs:%+v", c.Msg.UID, cmsg.FormationIds)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	c.User.SendFormationToClient(cmsg.FormationIds)
	var findGst bool
	for _, formationId := range cmsg.FormationIds {
		if formationId == uint32(common.FORMATION_ID_FI_GST) {
			findGst = true
			break
		}
	}
	if findGst {
		gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
		if !ok {
			return true
		}
		if gstM.IsCrossConnected() {
			crossMsg := &l2c.L2CS_GSTUpdateViewFormation{}
			if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTUpdateViewFormation, c.Msg.UID, crossMsg) {
				l4g.Errorf("user: %d ID_MSG_L2CS_GSTUpdateViewFormation: cross maintain", c.Msg.UID)
				return true
			}
		}
	}
	return true
}

/*
type C2LBattleTestCommand struct {
	base.UserCommand
}

func (c *C2LBattleTestCommand) Error(msg *cl.L2C_BattleTest, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BattleTest, msg)
	return true
}

func (c *C2LBattleTestCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_BattleTest{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_BattleTest Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debugf("user: %d C2L_BattleTest: %s", c.Msg.UID, cmsg)

	report, _ := c.User.BattleTest()
	smsg := &cl.L2C_BattleTest{
		Ret:    uint32(cret.RET_OK),
		Report: report,
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BattleTest, smsg)
	return true
}
i*/

type C2LRobotBattleCommand struct {
	base.LimitedCommand
}

func (c *C2LRobotBattleCommand) Error(msg *cl.L2C_RobotBattle, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RobotBattle, msg)
	return true
}
func (c *C2LRobotBattleCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RobotBattle{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RobotBattle Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user: %s C2L_RobotBattle: %s", c.User.Name(), cmsg)
	smsg := &cl.L2C_RobotBattle{
		FuncId: cmsg.FuncId,
	}
	//从排行榜前500名里随机获取一个
	rankID := goxml.PowerRankId
	list := c.Srv.CommonRankM().GetRangeByRank(rankID, 1, 50) //nolint:mnd
	if len(list) == 0 {
		l4g.Errorf("C2L_GetCommonRank config not exist:%d %d", c.Msg.UID, rankID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	randKey := c.Srv.Rand().RandBetween(0, len(list)-1)
	opUid := list[randKey].(rank.CommonRankValuer).Key()
	c.GetLocalUserBattleData(uint32(common.FORMATION_ID_FI_DUNGEON),
		[]uint64{opUid}, &AsyncC2LRobotBattleReq{opUid}, nil)
	return true
}

type AsyncC2LRobotBattleReq struct {
	opUID uint64
}

func (ar *AsyncC2LRobotBattleReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	opUID := ar.opUID
	cbusers := dbData.([]*db.UserBattleData)
	smsg := &cl.L2C_RobotBattle{
		Ret:    uint32(cret.RET_OK),
		FuncId: uint32(common.FORMATION_ID_FI_ARENA_DEFENSE),
		UserId: ar.opUID,
	}

	if retCode != uint32(r2l.RET_OK) {
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_RobotBattle, smsg)
		l4g.Errorf("user: %d AsyncTestBattlePvP: get snapshot failed. error:%d", args.UID, retCode)
		return false
	}
	var battleData *db.UserBattleData
	for _, v := range cbusers {
		if v == nil {
			l4g.Errorf("user: %d AsyncTestBattlePvP: no data. uid:%d",
				args.UID, opUID)
			continue
		}
		if v.User.Id == opUID {
			battleData = v
			break
		}
	}
	if battleData == nil {
		l4g.Errorf("user: %d AsyncTestBattlePvP: no data. uid:%d",
			args.UID, opUID)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_RobotBattle, smsg)
		return false
	}
	opUser := character.GetUserFromUserBattleData(srv, battleData)
	win, reportID, ret := args.Caller.AttackPVPTest(srv, opUser)
	if ret != cret.RET_OK {
		l4g.Errorf("user: %d AsyncTestBattleUser: AttackPVPTest failed. error:%d", args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_RobotBattle, smsg)
		return false
	}
	smsg.Win = win
	smsg.ReportId = reportID
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_RobotBattle, smsg)
	return true
}

type C2LGetUserBattleDataCommand struct {
	base.UserCommand
}

func (c *C2LGetUserBattleDataCommand) Error(msg *cl.L2C_GetUserBattleData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, msg)
	return true
}

func (c *C2LGetUserBattleDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetUserBattleData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetUserBattleData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	smsg := &cl.L2C_GetUserBattleData{
		Ret:         uint32(cret.RET_OK),
		Uids:        cmsg.Uids,
		FormationId: cmsg.FormationId,
		Type:        cmsg.Type,
	}
	if len(cmsg.Uids) == 0 {
		l4g.Errorf("C2L_GetUserBattleData uids len error: %d %d", c.Msg.UID, len(cmsg.Uids))
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	formationID := cmsg.FormationId
	if formationID != 0 && !goxml.GetData().FormationInfoM.IsLegalFormation(formationID) {
		l4g.Errorf("C2L_GetUserBattleData formation id error: %d %d", c.Msg.UID, formationID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if len(cmsg.Uids) > character.CommandMaxParamLen {
		l4g.Errorf("C2L_GetUserBattleData uid len  error: %d %d", c.Msg.UID, len(cmsg.Uids))
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	requestUids := make(map[uint64]struct{}, len(cmsg.Uids))
	for _, v := range cmsg.Uids {
		if _, exist := requestUids[v]; exist {
			l4g.Errorf("C2L_GetUserBattleData uid repeated error: %d %+v", c.Msg.UID, cmsg.Uids)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}
		requestUids[v] = struct{}{}
	}

	l4g.Debugf("user: %d C2L_GetUserBattleData: %s", c.Msg.UID, cmsg)

	req := &AsyncC2LGetUserBattleDataReq{
		formationID: formationID,
		userIds:     cmsg.Uids,
		smsg:        smsg,
	}
	switch cmsg.Type {
	case character.RivalFormation:
		c.GetLocalUserBattleData(formationID, cmsg.Uids, req, nil)
	case character.RivalBattleSnapshot:
		c.AsyncGetUserBattleSnapshot(formationID, cmsg.Uids, req)
	default:
		l4g.Errorf("C2L_GetUserBattleData type error: %d %d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	return true
}

type AsyncC2LGetUserBattleDataReq struct {
	formationID uint32
	userIds     []uint64
	smsg        *cl.L2C_GetUserBattleData
}

func (ar *AsyncC2LGetUserBattleDataReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	userIds := ar.userIds
	formationID := ar.formationID
	smsg := ar.smsg
	l4g.Debugf("user: %d C2L_GetUserBattleData: get data. userIds: %+v data:%+v", args.UID, userIds, dbData)
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LGetUserBattleDataCommand: get battle data error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, smsg)
		return false
	}
	snapm := make(map[uint64]*db.UserBattleData, len(userIds))

	switch smsg.Type {
	case character.RivalFormation:
		users := dbData.([]*db.UserBattleData)
		for k, v := range users {
			if v == nil {
				l4g.Errorf("user: %d C2LGetUserBattleDataCommand: get battle data nil:%d",
					args.UID, userIds[k])
				smsg.Ret = uint32(cret.RET_FORMATION_NOT_EXIST)
				args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, smsg)
				return false
			}
			snapm[v.User.Id] = v
		}
	case character.RivalBattleSnapshot:
		users := dbData.([]*db.UserBattleSnapshot)
		for k, v := range users {
			if v == nil {
				l4g.Errorf("user: %d C2LGetUserBattleDataCommand: get battle data nil:%d",
					args.UID, userIds[k])
				smsg.Ret = uint32(cret.RET_FORMATION_NOT_EXIST)
				args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, smsg)
				return false
			}
			snapm[v.Id] = v.Info
		}
	default:
		l4g.Errorf("user: %d C2LGetUserBattleDataCommand: get battle data type error:%d",
			args.UID, smsg.Type)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, smsg)
		return false
	}
	for _, v := range userIds { //nolint:varnamelen
		if snapm[v] == nil {
			l4g.Errorf("user: %d C2L_GetUserBattleData: get battle nil:%d, fid:%d",
				args.UID, v, formationID)
			smsg.Ret = uint32(cret.RET_FORMATION_NOT_EXIST)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, smsg)
			return true
		}

		newUser := character.GetUserFromUserBattleData(srv, snapm[v])
		if battleData := newUser.FlushBattleData(srv, formationID, formationID == 0); battleData != nil {
			//添加巅峰竞技场赛季积分
			if battleData.User != nil {
				rankScore := getPeakSeasonRankScore(srv, v)
				if rankScore != nil {
					battleData.User.PeakScore = rankScore.Score
					battleData.User.PeakRank = rankScore.Rank
				}
			}
			smsg.Users = append(smsg.Users, battleData)
		}
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserBattleData, smsg)
	return true
}

type C2LTestBattleDataCommand struct {
	base.LimitedCommand
}

func (c *C2LTestBattleDataCommand) Error(msg *cl.L2C_TestBattleData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, msg)
	return true
}

func (c *C2LTestBattleDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TestBattleData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TestBattlData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	smsg := &cl.L2C_TestBattleData{
		Ret: uint32(cret.RET_OK),
	}

	l4g.Debugf("user: %d C2L_TestBattleData: %s", c.Msg.UID, cmsg)

	formationInfo := goxml.GetData().FormationInfoM.Index(cmsg.FormationId)
	if formationInfo == nil {
		l4g.Errorf("user:%d C2L_TestBattleData formation id:%d", c.Msg.UID, cmsg.FormationId)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return false
	}

	power := c.User.CalFormationPower(cmsg.FormationId)

	battleData := character.GetBattleDataFromUser(c.User, uint32(cmsg.FormationId))
	newUser := character.GetUserFromUserBattleData(c.Srv, battleData)

	newPower := newUser.CalFormationPower(uint32(cmsg.FormationId))

	if power != newPower {
		l4g.Debugf("user's power %d not equal newUser's power %d", power, newPower)
		smsg.Ret = uint32(cret.RET_ERROR)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return false
	}

	req := &AsyncC2LTestBattleDataReq{
		opUID: c.Msg.UID,
		power: power,
		smsg:  smsg,
	}
	c.GetLocalUserBattleData(uint32(common.FORMATION_ID_FI_DUNGEON), []uint64{c.Msg.UID}, req, nil)
	return true
}

type AsyncC2LTestBattleDataReq struct {
	opUID uint64
	power int64
	smsg  *cl.L2C_TestBattleData
}

func (ar *AsyncC2LTestBattleDataReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	opUID := ar.opUID
	power := ar.power
	smsg := ar.smsg

	l4g.Debugf("user: %d C2L_TestBattleDataCommand: get data %d %+v", args.UID, opUID, dbData)
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LTestBattleDataCommand: get snapshot error:%d",
			args.UID, retCode)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return true
	}
	cbusers := dbData.([]*db.UserBattleData)
	if len(cbusers) == 0 || cbusers[0] == nil {
		l4g.Errorf("user: %d C2LGetUserBattleDataCommand: get battle data nil:%d",
			args.UID, opUID)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return true
	}

	cbUser := character.GetUserFromUserBattleData(srv, cbusers[0])
	cbPower := cbUser.CalFormationPower(uint32(common.FORMATION_ID_FI_DUNGEON))

	if power != cbPower {
		l4g.Debugf("user's power %d not equal cbUser's power %d", power, cbPower)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
		return false
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_TestBattleData, smsg)
	return true
}

type C2LGlobalAttrGetCommand struct {
	base.UserCommand
}

func (c *C2LGlobalAttrGetCommand) Error(msg *cl.L2C_GlobalAttrGet, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GlobalAttrGet, msg)
	return true
}

func (c *C2LGlobalAttrGetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GlobalAttrGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GlobalAttrGet Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	smsg := &cl.L2C_GlobalAttrGet{
		Ret:        uint32(cret.RET_OK),
		GlobalAttr: &cl.GlobalAttr{},
	}

	l4g.Debugf("C2L_GlobalAttrGet, %d %s", c.Msg.UID, cmsg)

	smsg.GlobalAttr.MemoryAttr = c.User.Memory().FlushMemoryGlobalAttr()
	smsg.GlobalAttr.ArtifactAttr = c.User.ArtifactManager().FlushArtifactGlobalAttr()
	smsg.GlobalAttr.GuildAttr = c.User.GuildTalent().FlushGuildTalentGlobalAttr()
	smsg.GlobalAttr.GoddessContractAttr = c.User.GoddessContract().FlushGoddessGlobalAttr()
	smsg.GlobalAttr.ShareGrowth = c.User.ShareGrowth().Flush()
	smsg.GlobalAttr.PokemonAttr = c.User.PokemonManager().FlushGlobalAttr()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GlobalAttrGet, smsg)
	return true
}

type C2LSetNameCommand struct {
	base.UserCommand
}

func (c *C2LSetNameCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSetNameCommand) Error(msg *cl.L2C_SetName, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SetName, msg)
	return false
}

//检查名字的合法性
/*
TODO
长度问题，中日韩2个长度，其他1个长度。
*/
func (c *C2LSetNameCommand) checkName(name string) uint32 {
	if len(name) > goxml.GetData().ConfigInfoM.UserNameMaxLength*character.MaxCharactUtf8Len {
		l4g.Errorf("checkname %d name: %s length error len:%d ", c.Msg.UID, name, len(name))
		return uint32(cret.RET_USER_NAME_LENGTH_LIMIT)
	}
	if !utf8.ValidString(name) {
		l4g.Errorf("create user %d name llegal: %s", c.Msg.UID, name)
		return uint32(cret.RET_USER_NAME_ILLEGAL_CHARACTER)
	}
	if strings.Trim(name, " ") == "" {
		l4g.Errorf("checkname %d name: %s length error, all empty space", c.Msg.UID, name)
		return uint32(cret.RET_USER_NAME_LENGTH_LIMIT)
	}
	if length := utf8.RuneCountInString(name); length == 0 || length > goxml.GetData().ConfigInfoM.UserNameMaxLength {
		l4g.Errorf("create user %d name: %s length error: %d", c.Msg.UID, name, length)
		return uint32(cret.RET_USER_NAME_LENGTH_LIMIT)
	}
	if strings.IndexFunc(name, character.HasIllegalNameRune) != -1 {
		l4g.Errorf("create user %d name llegal: %s", c.Msg.UID, name)
		return uint32(cret.RET_USER_NAME_ILLEGAL_CHARACTER)
	}
	return uint32(cret.RET_OK)
}

func (c *C2LSetNameCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SetName{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SetName Unmarshal error: uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SetName: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_SetName{
		Ret:  uint32(cret.RET_OK),
		Name: cmsg.Name,
	}
	if c.User.CheckSetNameLock() {
		l4g.Errorf("user: %d C2L_SetName name error setting", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_USER_SAME_OPERATE))
	}
	name := cmsg.GetName()
	var costs []*cl.Resource
	if name == c.User.Name() {
		l4g.Errorf("user: %d C2L_SetName name error same name, name:%s", c.Msg.UID, name)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	if nret := c.checkName(name); nret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_SetName name error, name:%s nret:%d", c.Msg.UID, name, nret)
		return c.Error(smsg, nret)
	}
	if c.User.Name() != "" {
		costs = append(costs, &cl.Resource{
			Type:  uint32(common.RESOURCE_DIAMOND),
			Value: 0,
			Count: goxml.GetData().ConfigInfoM.UserNameSetCost,
		})
	}
	if len(costs) > 0 {
		if nret, _ := c.User.CheckResourcesSize(costs); nret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_SetName name check resource size error, nret:%d", c.Msg.UID, nret)
			return c.Error(smsg, nret)
		}
	}
	c.User.LockSetName()
	platformConfig := c.Srv.PlatformConfig()
	if platformConfig != nil && platformConfig.SensitiveWordCheckUrl != "" {
		uid := c.Msg.UID
		base.AsyncSensitiveWordCheck(
			c.Srv,
			c.User.Args(c.Msg.Cmd),
			&AsyncC2LSetNameReq{
				OldName: c.User.Name(),
				Smsg:    smsg,
			},
			[]*p2l.SensitiveWordCheckReq{
				c.User.GetCheckSensitiveWordReq(name, c.Srv.ServerID(), character.SensitiveUserName, platformConfig.Channel),
			},
			func() {
				user := c.Srv.UserM().GetUser(uid)
				if user == nil {
					user = c.Srv.UserM().GetUserWithCache(uid)
				}
				if user != nil {
					user.UnlockSetName()
				}
			},
		)
		return true
	}
	c.Srv.RedisActor().AddMessage(uint32(r2l.ID_MSG_L2R_SetName), c.Msg.UID, &r2l.L2R_SetName{
		Id:      c.User.ID(),
		OldName: c.User.Name(),
		NewName: name,
	})
	return c.ResultOK(smsg.Ret)
}

type C2LSyncQuestionnaireCommand struct {
	base.UserCommand
}

func (c *C2LSyncQuestionnaireCommand) Error(msg *cl.L2C_SyncQuestionnaire, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SyncQuestionnaire, msg)
	return false
}

func (c *C2LSyncQuestionnaireCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSyncQuestionnaireCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SyncQuestionnaire{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SyncQuestionnaire Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SyncQuestionnaire: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SyncQuestionnaire{
		Ret:  uint32(cret.RET_OK),
		Lang: cmsg.Lang,
	}
	now := time.Now().Unix()
	checkMsg := &etcdcache.CheckQuestionnaireMsg{
		NowTime:        uint32(now),
		Level:          c.User.Level(),
		Vip:            c.User.Vip(),
		ServerDay:      c.Srv.ServerDay(now),
		OpID:           c.User.OpID(),
		Channel:        c.User.Channel(),
		CreateRoleDay:  helper.DaysBetweenTimes(now, c.User.CreateTime()) + 1,
		RechargeAmount: c.User.GetRecharge(),
		FinishIDs:      make(map[uint32]bool),
	}
	questionnaires := c.User.Questionnaires()
	finishIDs := questionnaires.GetFinishIDs()
	for id := range finishIDs {
		checkMsg.FinishIDs[id] = true
	}
	smsg.Questions = c.Srv.QuestionnaireFlush(checkMsg, cmsg.Lang)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SyncQuestionnaire, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LDuelCommand struct {
	base.UserCommand
}

func (c *C2LDuelCommand) Error(msg *cl.L2C_Duel, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Duel, msg)
	return false
}

func (c *C2LDuelCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDuelCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_Duel{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_Duel Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_Duel: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_Duel{
		Ret:     uint32(cret.RET_OK),
		RivalId: cmsg.RivalId,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DUEL), c.Srv) {
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	//防刷验证
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIDuel) {
		l4g.Errorf("user: %d C2L_Duel: operate too frequently", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_OPERATE_TOO_OFTEN))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_Duel: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	requestUids := []uint64{cmsg.RivalId}
	formationID := uint32(common.FORMATION_ID_FI_DUNGEON)
	req := &AsyncC2LDuelReq{
		rivalID:    cmsg.RivalId,
		smsg:       smsg,
		ClientData: cmsg.ClientData,
	}
	c.GetLocalUserBattleData(formationID, requestUids, req, nil)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Duel, smsg)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LDuelReq struct {
	rivalID    uint64
	smsg       *cl.L2C_Duel
	ClientData []byte
}

func (ar *AsyncC2LDuelReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	rivalID := ar.rivalID
	snaps := dbData.([]*db.UserBattleData)
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_Duel: get snapshot failed. error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_ArenaFight, smsg)
		return false
	}

	snapm := make(map[uint64]*db.UserBattleData, len(snaps))
	for _, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d C2L_Duel: no data. uid:%d", args.UID, rivalID)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_Duel, smsg)
			return false
		}
		snapm[v.User.Id] = v
	}

	if snapm[rivalID] == nil {
		l4g.Errorf("user: %d C2L_Duel: no data. uid:%d", args.UID, rivalID)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_Duel, smsg)
		return false
	}

	rivalUser := character.GetUserFromUserBattleData(srv, snapm[rivalID])
	win, reportID, bRet := args.Caller.AttackDuel(srv, rivalUser, ar.ClientData)
	if bRet != cret.RET_OK {
		l4g.Errorf("user: %d C2L_Duel: AttackDuel failed. err:%d",
			args.UID, bRet)
		smsg.Ret = uint32(cret.RET_BATTLE_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_Duel, smsg)
		return false
	}

	smsg.ReportId = reportID
	smsg.Win = win
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_Duel, smsg)
	args.Caller.LogDuel(srv, rivalID, args.CtxID, win)
	return true
}

type C2LAccusationCommand struct {
	base.UserCommand
}

func (c *C2LAccusationCommand) Error(msg *cl.L2C_Accusation, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Accusation, msg)
	return false
}

func (c *C2LAccusationCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LAccusationCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_Accusation{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_Accusation Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_Accusation: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_Accusation{
		Ret: uint32(cret.RET_OK),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACCUSATION)) {
	//	l4g.Errorf("user: %d C2L_Accusation: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}
	//numType := uint32(1)
	//smsg.Ret, _ = c.User.CheckNumByType(numType, 1)
	//if smsg.Ret != uint32(cret.RET_OK) {
	//	l4g.Errorf("user: %d C2L_Accusation: check num error. numType:%d", c.Msg.UID, numType)
	//	return c.Error(smsg, smsg.Ret)
	//}

	//todo 增加按照数数的格式记录日志
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Accusation, smsg)
	c.User.LogAccusation(c.Srv, cmsg.Uid, cmsg.Reason)
	return c.ResultOK(smsg.Ret)
}

type C2LGetClientInfoCommand struct {
	base.UserCommand
}

func (c *C2LGetClientInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetClientInfoCommand) Error(msg *cl.L2C_GetClientInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetClientInfo, msg)
	return true
}

func (c *C2LGetClientInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetClientInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetClientInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetClientInfo: %s", c.Msg.UID, cmsg)
	c.User.ClientInfo().SendToClient()
	return true
}

type C2LSetClientInfoCommand struct {
	base.UserCommand
}

func (c *C2LSetClientInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSetClientInfoCommand) Error(msg *cl.L2C_SetClientInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SetClientInfo, msg)
	return true
}

func (c *C2LSetClientInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SetClientInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SetClientInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SetClientInfo: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SetClientInfo{
		Ret: uint32(cret.RET_OK),
	}

	//防刷验证
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIClientInfo) {
		l4g.Errorf("user: %d C2L_SetClientInfo: operate too frequently", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_OPERATE_TOO_OFTEN))
	}

	//限制长度
	if cmsg.ClientInfo == nil {
		l4g.Errorf("user: %d C2L_SetClientInfo: client info nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	clInfos := cmsg.ClientInfo.Infos
	if len(clInfos) == 0 {
		l4g.Errorf("user: %d C2L_SetClientInfo: cl client info empty.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	for k, v := range clInfos {
		if k > 10 { //nolint:mnd
			l4g.Errorf("user: %d C2L_SetClientInfo: cl client key les than 10. key:%d", c.Msg.UID, k)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if v == nil {
			l4g.Errorf("user: %d C2L_SetClientInfo: cl client value is nil.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		if len(v.Datas) > 100 { //nolint:mnd
			l4g.Errorf("user: %d C2L_SetClientInfo: cl client value more than 100. key:%d", c.Msg.UID, len(v.Datas))
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}
	c.User.ClientInfo().Update(cmsg.ClientInfo)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SetClientInfo, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGetUserSnapshotsCommand struct {
	base.UserCommand
}

func (c *C2LGetUserSnapshotsCommand) Error(msg *cl.L2C_GetUserSnapshots, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetUserSnapshots, msg)
	return false
}

func (c *C2LGetUserSnapshotsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetUserSnapshotsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetUserSnapshots{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetUserSnapshots Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetUserSnapshots: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GetUserSnapshots{
		Ret: uint32(cret.RET_OK),
		Fid: cmsg.Fid,
	}

	num := len(cmsg.UserIds)
	if num < 1 || num > character.UserSnapshotReqMax {
		l4g.Errorf("user: %d C2L_GetUserSnapshots: recv ids error, num:%d", c.Msg.UID, num)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//cmsg.Fid只能为0，或防守阵容id
	if cmsg.Fid > 0 && !goxml.GetData().FormationInfoM.IsNonemptyFormation(cmsg.Fid) {
		l4g.Errorf("user: %d C2L_GetUserSnapshots: not defense fid, fid:%d", c.Msg.UID, cmsg.Fid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//对请求列表去重
	userIDs := util.UniqueUint64s(cmsg.UserIds)
	c.GetLocalUserSnapshots(userIDs, &AsyncC2LGetUserSnapshotsReq{smsg}, cmsg.Fid)

	return c.ResultOK(smsg.Ret)
}

type AsyncC2LGetUserSnapshotsReq struct {
	smsg *cl.L2C_GetUserSnapshots
}

func (ar *AsyncC2LGetUserSnapshotsReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LGetUserSnapshots: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserSnapshots, smsg)
		return false
	}

	if users := dbData.([]*cl.UserSnapshot); len(users) > 0 {
		smsg.Users = make([]*cl.UserSnapshot, 0, len(users))
		for _, user := range users {
			smsg.Users = append(smsg.Users, user.Clone())
		}
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetUserSnapshots, smsg)
	return true
}

type C2LRecvShareAwardCommand struct {
	base.UserCommand
}

func (c *C2LRecvShareAwardCommand) Error(msg *cl.L2C_RecvShareAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RecvShareAward, msg)
	return false
}

func (c *C2LRecvShareAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRecvShareAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RecvShareAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RecvShareAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_RecvShareAward: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_RecvShareAward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SHARE), c.Srv) {
		l4g.Errorf("user: %d C2L_RecvShareAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	//检查领奖次数
	nRet, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_SHARE_RECV_AWARD_COUNT), 1)
	if nRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_RecvShareAward: no loot chance today", c.Msg.UID)
		return c.Error(smsg, nRet)
	}

	award := goxml.GetData().ShareConfigInfoM.GetShareAward()

	if award == nil {
		l4g.Errorf("user: %d C2L_RecvShareAward: award is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	//更新资源
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, []*cl.Resource{award}, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_RECV_SHARE_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_RecvShareAward: award failed. awards:%+v",
			c.User.ID(), award)
		return c.Error(smsg, smsg.Ret)
	}

	//
	c.User.AddNumByType(uint32(common.PURCHASEID_SHARE_RECV_AWARD_COUNT), numSummary)
	c.User.LogRecvShareAward(c.Srv, 1)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RecvShareAward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGetGiftCodeAwardCommand struct {
	base.UserCommand
}

func (c *C2LGetGiftCodeAwardCommand) Error(msg *cl.L2C_GetGiftCodeAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetGiftCodeAward, msg)
	return false
}

func (c *C2LGetGiftCodeAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetGiftCodeAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetGiftCodeAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetGiftCodeAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetGiftCodeAward: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GetGiftCodeAward{
		Ret:  uint32(cret.RET_OK),
		Code: cmsg.Code,
	}

	cnt := utf8.RuneCountInString(cmsg.Code)
	if cnt == 0 || cnt > character.GiftCodeMaxLength {
		l4g.Errorf("user: %d C2L_GetGiftCodeAward: gift code length illegal. code:%s", c.Msg.UID, cmsg.Code)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	pmsg := &p2l.L2P_UseGiftCode{
		Code:     cmsg.Code,
		Uuid:     c.User.UUID(),
		ServerId: c.User.ServerID(),
		OpId:     c.User.OpID(),
		Channel:  c.User.Channel(),
		Uid:      c.User.ID(),
		OpGroup:  c.Srv.OpGroup(),
	}
	if c.Srv.ServerType() == goxml.SERVER_TYPE_CN {
		pmsg.Channel = c.User.Gid() // 国服的二级渠道，使用三七的 gid
	}
	c.Srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_UseGiftCode), c.Msg.UID, pmsg)
	return true
}

type C2LClientSetMultiLangCommand struct {
	base.UserCommand
}

func (c *C2LClientSetMultiLangCommand) Error(msg *cl.L2C_ClientSetMultiLang, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ClientSetMultiLang, msg)
	return false
}

func (c *C2LClientSetMultiLangCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LClientSetMultiLangCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ClientSetMultiLang{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ClientSetMultiLang Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_ClientSetMultiLang: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_ClientSetMultiLang{
		Ret: uint32(cret.RET_OK),
	}
	if len(cmsg.Lang) > 20 { //nolint:mnd
		l4g.Errorf("user: %d C2L_ClientSetMultiLang: Lang more than 20. long:%d", c.Msg.UID, len(cmsg.Lang))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	c.User.SetLang(cmsg.Lang)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ClientSetMultiLang, smsg)

	// 玩家手动切换语言且聊天功能开启，再同步给聊天服
	if cmsg.IsHand && c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_CHAT), c.Srv) {
		character.ChatJoinAllWorldGroupToPlatform(c.Srv, c.Msg.UID, cmsg.Lang, character.ChatGroupOptSeven)
	}

	return c.ResultOK(smsg.Ret)
}

type C2LClientGetMultiLangCommand struct {
	base.UserCommand
}

func (c *C2LClientGetMultiLangCommand) Error(msg *cl.L2C_ClientSetMultiLang, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ClientSetMultiLang, msg)
	return false
}

func (c *C2LClientGetMultiLangCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LClientGetMultiLangCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ClientGetMultiLang{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ClientGetLang Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_ClientGetLang: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_ClientGetMultiLang{
		Ret: uint32(cret.RET_OK),
	}
	smsg.Lang = c.User.GetLang()
	smsg.DataVersion = uint32(goxml.GetData().ServerInfoM.Version)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ClientGetMultiLang, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGetCommonRankFirstCommand struct {
	base.UserCommand
}

func (c *C2LGetCommonRankFirstCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetCommonRankFirstCommand) Error(msg *cl.L2C_GetCommonRankFirst, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRankFirst, msg)
	return true
}

func (c *C2LGetCommonRankFirstCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetCommonRankFirst{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetCommonRankFirst Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetCommonRankFirst: %s", c.Msg.UID, cmsg)

	count := len(cmsg.RankIds)
	smsg := &cl.L2C_GetCommonRankFirst{
		Ret:  uint32(cret.RET_OK),
		List: make([]*cl.FirstRankValue, 0, count),
	}

	if count == 0 || count > len(goxml.LegalCommonRankId) {
		l4g.Errorf("user: %d C2L_GetCommonRankFirst: param error. count:%d", c.Msg.UID, count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GetCommonRankFirst failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	topUids := make([]uint64, 0, count)
	uniq := make(map[uint32]struct{})
	for _, rankID := range cmsg.RankIds {
		if _, exist := uniq[rankID]; exist {
			l4g.Errorf("user: %d C2L_GetCommonRankFirst: RankIds repeat. RankIds:%v",
				c.Msg.UID, cmsg.RankIds)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[rankID] = struct{}{}

		rankInfo := goxml.GetData().RankingInfoM.Index(rankID)
		if rankInfo == nil {
			l4g.Errorf("user: %d C2L_GetCommonRankFirst: config not exist. rankID: %d", c.Msg.UID, rankID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if !goxml.LegalCommonRankId[rankID] {
			l4g.Errorf("user: %d C2L_GetCommonRankFirst: rankID illegal. rankID: %d", c.Msg.UID, rankID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		data := &cl.FirstRankValue{
			RankId: rankID,
		}

		var item interface{}
		if rankID == goxml.ArenaRankId {
			arenaM := c.Srv.GetActivity(activity.Arena).(*arena.Manager)
			node := arenaM.Rank.GetNodeByRank(1)
			if node != nil {
				item = node.Value()
			}
		} else {
			item = c.Srv.CommonRankM().GetElementByRank(rankID, 1)
		}

		if item != nil {
			if rankID == goxml.ArenaRankId {
				val := item.(*arena.ScoreValue)
				data.RankValue = &cl.RankValue{
					Id:     val.ID,
					Value:  uint64(val.CScore),
					Param1: uint64(val.Tm),
				}
			} else {
				data.RankValue = item.(rank.CommonRankValuer).ShowValue()
			}

			if rankID == goxml.GuildLevelRankID {
				guild := guildM.GetGuildByID(data.RankValue.Id)
				if guild != nil {
					data.RankValue = getOneGuildBaseRankData(guild, data.RankValue)
				}
			} else {
				topUids = append(topUids, data.RankValue.Id)
			}
		}
		smsg.List = append(smsg.List, data)
	}

	if len(topUids) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRankFirst, smsg)
		return c.ResultOK(smsg.Ret)
	}

	req := &AsyncC2LGetCommonRankFirstReq{
		smsg: smsg,
	}
	c.GetLocalUserSnapshots(topUids, req, 0)
	return true
}

type AsyncC2LGetCommonRankFirstReq struct {
	smsg *cl.L2C_GetCommonRankFirst
}

func (ar *AsyncC2LGetCommonRankFirstReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_GetCommonRankFirst: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRankFirst, smsg)
		return false
	}

	users := dbData.([]*cl.UserSnapshot)
	snapm := make(map[uint64]*cl.UserSnapshot, len(users))
	for _, v := range users {
		snapm[v.Id] = v
	}

	l4g.Debugf("user: %d AsyncC2LGetCommonRankFirstReq: smsg:%v, snapm:%v", args.UID, smsg, snapm)
	for _, v := range smsg.List { //nolint:varnamelen
		if v.RankId == goxml.GuildLevelRankID {
			continue
		}

		if v.RankValue == nil {
			continue
		}

		snapshot := snapm[v.RankValue.Id]
		if snapshot == nil {
			l4g.Errorf("user: %d C2L_GetCommonRankFirst: get snapshot nil. id:%d",
				args.UID, v.RankValue.Id)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRankFirst, smsg)
			return false
		}
		v.RankValue.User = snapshot
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCommonRankFirst, smsg)
	return true
}

type C2LGlobalAttrScoreGetCommand struct {
	base.UserCommand
}

func (c *C2LGlobalAttrScoreGetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGlobalAttrScoreGetCommand) Error(msg *cl.L2C_GetCommonRankFirst, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GlobalAttrScoreGet, msg)
	return true
}

func (c *C2LGlobalAttrScoreGetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GlobalAttrScoreGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GlobalScoreGetCommand Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GlobalScoreGetCommand: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GlobalAttrScoreGet{
		Ret: uint32(cret.RET_OK),
	}

	score := &cl.GlobalScore{}

	score.ArtifactScore = c.User.ArtifactManager().GetScore()
	score.GuildScore = c.User.GuildTalent().GetScore()
	//score.HandbookScore = c.User.Handbook().GetScore()
	// score.CrystalBlessingScore = c.User.Crystal().GetScore()
	score.GoddessContractScore = c.User.GoddessContract().GetScore()
	smsg.Score = score

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GlobalAttrScoreGet, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LGetCrossRankFirstCommand struct {
	base.UserCommand
}

func (c *C2LGetCrossRankFirstCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetCrossRankFirstCommand) Error(msg *cl.L2C_GetCrossRankFirst, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCrossRankFirst, msg)
	return true
}

func (c *C2LGetCrossRankFirstCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetCrossRankFirst{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetCrossRankFirst Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetCrossRankFirst: %s", c.Msg.UID, cmsg)

	count := len(cmsg.RankIds)
	smsg := &cl.L2C_GetCrossRankFirst{
		Ret:     uint32(cret.RET_OK),
		RankIds: cmsg.RankIds,
		List:    make([]*cl.FirstRankValue, 0, count),
	}

	if count == 0 || count > len(goxml.LegalCommonRankId) {
		l4g.Errorf("user: %d C2L_GetCrossRankFirst: param error. count:%d", c.Msg.UID, count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	uniq := make(map[uint32]struct{})
	for _, rankID := range cmsg.RankIds {
		if _, exist := uniq[rankID]; exist {
			l4g.Errorf("user: %d C2L_GetCrossRankFirst: RankIds repeat. RankIds:%v",
				c.Msg.UID, cmsg.RankIds)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[rankID] = struct{}{}

		rankInfo := goxml.GetData().RankingInfoM.Index(rankID)
		if rankInfo == nil {
			l4g.Errorf("user: %d C2L_GetCrossRankFirst: config not exist. rankID: %d", c.Msg.UID, rankID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		//if !goxml.LegalCommonRankId[rankID] {
		//	l4g.Errorf("user: %d C2L_GetCrossRankFirst: rankID illegal. rankID: %d", c.Msg.UID, rankID)
		//	return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		//}
	}

	c.GetCrossRankFirstFromAll(cmsg.RankIds, &AsyncC2LGetCrossRankFirstReq{smsg: smsg})

	return true
}

type AsyncC2LGetCrossRankFirstReq struct {
	smsg *cl.L2C_GetCrossRankFirst
}

func (ar *AsyncC2LGetCrossRankFirstReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, data interface{}) bool {
	smsg := ar.smsg

	l4g.Debugf("user: %d AsyncC2LGetCrossRankFirstReq: smsg:%v, retCode:%d data:%+v",
		args.UID, smsg, retCode, data)

	values, ok := data.([]*cl.FirstRankValue)
	if !ok {
		l4g.Errorf("user: %d C2L_GetCrossRankFirst: get firstRankValue error. ret:%d",
			args.UID, cret.RET_ERROR)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCrossRankFirst, smsg)
		return false
	}

	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_GetCrossRankFirst: get firstRankValue error. ret:%d",
			args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCrossRankFirst, smsg)
		return false
	}

	rankDataMap := make(map[uint32]*cl.FirstRankValue)

	for _, value := range values {
		if value == nil {
			continue
		}
		rankDataMap[value.RankId] = value
	}

	for _, rankId := range smsg.RankIds {
		value := rankDataMap[rankId]
		if value == nil {
			smsg.List = append(smsg.List, &cl.FirstRankValue{
				RankId: rankId,
			})
			continue
		}
		smsg.List = append(smsg.List, value)
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCrossRankFirst, smsg)
	return true
}

type C2L_OssBattleReportCommand struct {
	base.LimitedCommand
}

func (c *C2L_OssBattleReportCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2L_OssBattleReportCommand) Error(msg *cl.L2C_OssBattleReport, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OssBattleReport, msg)
	return true
}

func (c *C2L_OssBattleReportCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OssBattleReport{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OssBattleReport Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_OssBattleReport: %s", c.Msg.UID, cmsg)

	c.initTestReport()

	msg := &p2l.L2O_CreateBattleReport{
		ObjectKey: c.Srv.OSSM().GetOssKey(c.Srv.CreateUserLogID(), common.FUNCID_MODULE_DUNGEON),
		Data:      testReportData,
	}
	c.Srv.SendCmdToOSS(uint32(p2l.ID_MSG_L2O_CreateBattleReport), c.Msg.UID, msg, common.FUNCID_MODULE_DUNGEON)
	return true
}

const testReportLen = 10 * 1024 //10K长的战报
var testReportData []byte
var testGuard sync.Mutex

func (c *C2L_OssBattleReportCommand) initTestReport() {
	if len(testReportData) > 0 {
		return
	}
	testGuard.Lock()
	if len(testReportData) > 0 {
		return
	}
	testReportData = make([]byte, testReportLen)
	for index := range testReportData {
		testReportData[index] = 'a'
	}
	testGuard.Unlock()
}

type C2L_OSSUrlCommand struct {
	base.UserCommand
}

func (c *C2L_OSSUrlCommand) ResultOK(retCode ret.RET) bool {
	return retCode == cret.RET_OK
}

func (c *C2L_OSSUrlCommand) Error(msg *cl.L2C_OSSUrl, retCode ret.RET) bool {
	msg.Ret = uint32(retCode)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OSSUrl, msg)
	return true
}

func (c *C2L_OSSUrlCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OSSUrl{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OSSUrl Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_OSSUrl: %s", c.Msg.UID, cmsg)

	msg := &cl.L2C_OSSUrl{
		Ret:      uint32(ret.RET_OK),
		ReportID: cmsg.ReportID,
	}
	url, retCode := c.Srv.OSSM().GetOssUrl(cmsg.ReportID)
	if retCode != ret.RET_OK {
		return c.Error(msg, retCode)
	}
	msg.Url = url
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OSSUrl, msg)
	return true
}

type C2LGetAchieveShowcaseCommand struct {
	base.UserCommand
}

func (c *C2LGetAchieveShowcaseCommand) ResultOK(retCode ret.RET) bool {
	return retCode == cret.RET_OK
}

func (c *C2LGetAchieveShowcaseCommand) Error(msg *cl.L2C_GetAchieveShowcase, retCode ret.RET) bool {
	msg.Ret = uint32(retCode)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetAchieveShowcase, msg)
	return true
}

func (c *C2LGetAchieveShowcaseCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetAchieveShowcase{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetAchieveShowcase Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetAchieveShowcase: %s", c.Msg.UID, cmsg)

	msg := &cl.L2C_GetAchieveShowcase{
		Ret: uint32(ret.RET_OK),
	}

	showcase := c.User.AchievementsShowcase()
	if showcase == nil {
		l4g.Errorf("user: %d C2L_GetAchieveShowcase: showcase is nil", c.Msg.UID)
		return c.Error(msg, cret.RET_SERVER_ERROR)
	}

	msg.Achieves = showcase.FlushAchieve(c.Srv)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetAchieveShowcase, msg)
	return true
}

type C2LGetCognitionLogCommand struct {
	base.UserCommand
}

func (c *C2LGetCognitionLogCommand) ResultOK(retCode ret.RET) bool {
	return retCode == cret.RET_OK
}

func (c *C2LGetCognitionLogCommand) Error(msg *cl.L2C_GetCognitionLog, retCode ret.RET) bool {
	msg.Ret = uint32(retCode)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetCognitionLog, msg)
	return true
}

func (c *C2LGetCognitionLogCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetCognitionLog{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetCognitionLog Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetCognitionLog: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GetCognitionLog{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_COGNITION), c.Srv) {
		l4g.Errorf("user: %d C2L_GetCognitionLog: function not open", c.Msg.UID)
		return c.Error(smsg, cret.RET_FUNCTION_NOT_OPEN)
	}

	req := &l2m.GetCognitionLogReq{
		Uid:         c.User.ID(),
		FormationId: cmsg.FormationId,
		TargetId:    cmsg.TargetId,
		Page:        cmsg.Page,
		Sid:         c.Srv.ServerID(),
		OpGroup:     uint32(c.Srv.OpGroup()),
		Arena:       c.Srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)),
	}

	formationInfo := goxml.GetData().FormationInfoM.Index(cmsg.FormationId)
	if formationInfo == nil {
		l4g.Errorf("user:%d C2L_GetCognitionLog: formationInfo is nil. fid:%d", c.User.ID(), cmsg.FormationId)
		return c.Error(smsg, cret.RET_CLIENT_REQUEST_ERROR)
	}

	cogType := c.User.CognitionManager().GetCognitionType(common.FORMATION_ID(cmsg.FormationId))
	if cogType == l2m.CognitionType_CT_NONE {
		l4g.Errorf("user:%d C2L_GetCognitionLog: CognitionType not exist. fid:%d", c.User.ID(), cmsg.FormationId)
		return c.Error(smsg, cret.RET_CLIENT_REQUEST_ERROR)
	}
	req.Type = cogType
	if cmsg.FormationId == uint32(common.FORMATION_ID_FI_WORLD_BOSS) {
		req.Round = c.User.WorldBoss().GetWorldBossSysID()
	}

	seasonID := goxml.GetCurrentSeasonID(goxml.GetData(), time.Now().Unix())
	// 赛季ID使用时间获取，不用玩家身上的，玩家可能没有解锁赛季，比如:百塔
	if goxml.IsFuncInSeason(goxml.GetData(), seasonID, formationInfo.FunctionId) {
		req.SeasonId = seasonID
	}

	if !c.Srv.MongoM().Send(&mongo.GetCognitionLogMessage{Source: req}) {
		return c.Error(smsg, cret.RET_COGNITION_LOGS_SERVER_ERROR)
	}
	return true
}

type C2LHasRecvH5DesktopRewardCommand struct {
	base.UserCommand
}

func (c *C2LHasRecvH5DesktopRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LHasRecvH5DesktopRewardCommand) Error(msg *cl.L2C_HasRecvH5DesktopReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_HasRecvH5DesktopReward, msg)
	return true
}

func (c *C2LHasRecvH5DesktopRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_HasRecvH5DesktopReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_HasRecvH5DesktopReward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_HasRecvH5DesktopReward: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_HasRecvH5DesktopReward{
		Ret:      uint32(ret.RET_OK),
		Received: c.User.HasReceivedH5DesktopReward(),
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_HasRecvH5DesktopReward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LRecvH5DesktopRewardCommand struct {
	base.UserCommand
}

func (c *C2LRecvH5DesktopRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRecvH5DesktopRewardCommand) Error(msg *cl.L2C_RecvH5DesktopReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RecvH5DesktopReward, msg)
	return true
}

func (c *C2LRecvH5DesktopRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RecvH5DesktopReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RecvH5DesktopReward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_RecvH5DesktopReward: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_RecvH5DesktopReward{
		Ret: uint32(ret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SAVE_H5_DESKTOP), c.Srv) {
		l4g.Errorf("user: %d C2L_RecvH5DesktopReward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if c.User.HasReceivedH5DesktopReward() {
		l4g.Errorf("user: %d C2L_RecvH5DesktopReward: repeat save", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_REPEAT_RECV_SAVE_H5_REWARD))
	}

	awards := goxml.GetData().WebRewardInfoM.GetH5DesktopReward()
	if len(awards) == 0 {
		l4g.Errorf("user:%d C2L_RecvH5DesktopReward: no awards", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	smsg.Ret, _ = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_RECV_H5_DESKTOP_REWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_RecvH5DesktopReward: Award failed, code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.RecvH5DesktopReward()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RecvH5DesktopReward, smsg)
	c.User.LogRecvH5DesktopReward(c.Srv)
	return c.ResultOK(smsg.Ret)
}

type C2LTestCommand struct {
	base.LimitedCommand
}

func (c *C2LTestCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LTestCommand) Error(msg *cl.L2C_Test, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Test, msg)
	return true
}

func (c *C2LTestCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_Test{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_Test Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_Test: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_Test{
		Ret: uint32(cret.RET_OK),
	}

	//resetTime := int64(util.WeekdayZero(7)) + 8460000 - 7*24*3600

	if cmsg.P4 == 0 {
		/*
			power := crank.NewUserPower(cmsg.P1, cmsg.P2, cmsg.P3, time.Now().Unix())
			data, err := proto.Marshal(power)
			if err != nil {
				l4g.Errorf("C2L_Teset marshal error :%s", err)
				return false
			}
			msg := &l2c.L2C_RankUpdate{
				RankId:    goxml.UserPowerTestRankId,
				ResetTime: uint64(resetTime),
				Data:      data,
			}
			l4g.Debug("send msg to cross msg:%v", msg)
			c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_RankUpdate, c.User.ID(), msg)
		*/
	} else if cmsg.P4 == 1 {
		//查看排行榜
		msg := &l2c.L2C_RankGetList{
			RankId:    goxml.GuildDungeonChapterRankId,
			ResetTime: uint64(cmsg.P5),
			SelfId:    cmsg.P1,
			BeginRank: uint32(cmsg.P2),
			EndRank:   uint32(cmsg.P3),
		}
		l4g.Debug("send msg to cross msg:%v", msg)
		c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_RankGetList, c.User.ID(), msg)
	} else if cmsg.P4 == 2 { //nolint:mnd
		//查看排行榜
		msg := &l2c.L2C_RankGetList{
			RankId:    goxml.GuildDungeonChapterRankId,
			ResetTime: uint64(cmsg.P5),
			SelfId:    cmsg.P1,
			BeginRank: uint32(cmsg.P2),
			EndRank:   uint32(cmsg.P3),
			GetLast:   true,
		}
		l4g.Debug("send msg to cross msg:%v", msg)
		c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_RankGetList, c.User.ID(), msg)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_Test, smsg)
	return true
}

type C2LSeasonEnterCommand struct {
	base.UserCommand
}

func (c *C2LSeasonEnterCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSeasonEnterCommand) Error(msg *cl.L2C_SeasonEnter, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonEnter, msg)
	return true
}

func (c *C2LSeasonEnterCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonEnter{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonEnter Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SeasonEnter: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonEnter{
		Ret:      uint32(cret.RET_OK),
		SeasonId: cmsg.SeasonId,
	}

	now := time.Now().Unix()

	seasonID := goxml.GetCurrentSeasonID(goxml.GetData(), now)

	if cmsg.SeasonId != seasonID || cmsg.SeasonId == 0 {
		l4g.Errorf("user: %d C2L_SeasonEnter: seasonID error. realSeasonID:%d cmsg:%+v", c.Msg.UID, seasonID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonEnter: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	c.User.SetSeasonEnter(true)

	c.User.SendSelfToClient()
	c.User.SendSeasonJewelryToClient(nil)

	smsg.SeasonEnter = c.User.GetSeasonEnter()
	c.User.LogSeasonEnter(c.Srv)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonEnter, smsg)
	return true
}

type C2LGetSeasonRankFirstCommand struct {
	base.UserCommand
}

func (c *C2LGetSeasonRankFirstCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetSeasonRankFirstCommand) Error(msg *cl.L2C_GetSeasonRankFirst, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonRankFirst, msg)
	return true
}

func (c *C2LGetSeasonRankFirstCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetSeasonRankFirst{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetSeasonRankFirst Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetSeasonRankFirst: %s", c.Msg.UID, cmsg)

	count := len(cmsg.RankIds)
	smsg := &cl.L2C_GetSeasonRankFirst{
		Ret:     uint32(cret.RET_OK),
		RankIds: cmsg.RankIds,
		List:    make([]*cl.FirstRankValue, 0, count),
	}
	// c.User.UpdateAllSeasonRank(c.Srv) // 同步赛季排行榜
	if count == 0 || count > len(goxml.LegalCommonRankId) {
		l4g.Errorf("user: %d C2L_GetSeasonRankFirst: param error. count:%d", c.Msg.UID, count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	uniq := make(map[uint32]struct{})
	for _, rankID := range cmsg.RankIds {
		if _, exist := uniq[rankID]; exist {
			l4g.Errorf("user: %d C2L_GetSeasonRankFirst: RankIds repeat. RankIds:%v",
				c.Msg.UID, cmsg.RankIds)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		uniq[rankID] = struct{}{}

		rankInfo := goxml.GetData().RankingInfoM.Index(rankID)
		if rankInfo == nil {
			l4g.Errorf("user: %d C2L_GetSeasonRankFirst: config not exist. rankID: %d", c.Msg.UID, rankID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		//if !goxml.LegalCommonRankId[rankID] {
		//	l4g.Errorf("user: %d C2L_GetCrossRankFirst: rankID illegal. rankID: %d", c.Msg.UID, rankID)
		//	return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		//}
	}

	c.GetSeasonRankFirstFromAll(cmsg.RankIds, &AsyncC2LGetSeasonRankFirstReq{smsg: smsg})

	return true
}

type AsyncC2LGetSeasonRankFirstReq struct {
	smsg *cl.L2C_GetSeasonRankFirst
}

func (ar *AsyncC2LGetSeasonRankFirstReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, data interface{}) bool {
	smsg := ar.smsg

	l4g.Debugf("user: %d AsyncC2LGetSeasonRankFirstReq: smsg:%v, retCode:%d data:%+v",
		args.UID, smsg, retCode, data)

	values, ok := data.([]*cl.FirstRankValue)
	if !ok {
		l4g.Errorf("user: %d C2L_GetSeasonRankFirst: get firstRankValue error. ret:%d",
			args.UID, cret.RET_ERROR)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetCrossRankFirst, smsg)
		return false
	}

	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_GetSeasonRankFirst: get firstRankValue error. ret:%d",
			args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonRankFirst, smsg)
		return false
	}

	rankDataMap := make(map[uint32]*cl.FirstRankValue)

	for _, value := range values {
		if value == nil {
			continue
		}
		rankDataMap[value.RankId] = value
	}

	for _, rankId := range smsg.RankIds {
		value := rankDataMap[rankId]
		if value == nil {
			smsg.List = append(smsg.List, &cl.FirstRankValue{
				RankId: rankId,
			})
			continue
		}
		smsg.List = append(smsg.List, value)
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonRankFirst, smsg)
	return true
}

type C2LGetSeasonRankListCommand struct {
	base.UserCommand
}

func (c *C2LGetSeasonRankListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetSeasonRankListCommand) Error(msg *cl.L2C_GetSeasonRankList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonRankList, msg)
	return true
}

func (c *C2LGetSeasonRankListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetSeasonRankList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetSeasonRankFirst Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetSeasonRankFirst: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GetSeasonRankList{
		Ret: uint32(cret.RET_OK),
	}

	rankInfo := goxml.GetData().RankingInfoM.Index(cmsg.RankingId)
	if rankInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonRankList: rankInfo not exist. rankID:%d", c.Msg.UID, cmsg.RankingId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	endRank := rankInfo.ShowCount

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_RankGetList, c.Msg.UID, &l2c.L2C_RankGetList{
		RankId:    cmsg.RankingId,
		ResetTime: uint64(goxml.GetSeasonLastBeginResetTime(time.Now().Unix())),
		SelfId:    c.User.ID(),
		BeginRank: 1,
		EndRank:   endRank,
		GetLast:   false,
	}) {
		l4g.Errorf("user: %d C2L_GetSeasonRankList: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}

type C2LSetPushCommand struct {
	base.UserCommand
}

func (c *C2LSetPushCommand) Error(msg *cl.L2C_SetPush, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SetPush, msg)
	return false
}

func (c *C2LSetPushCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSetPushCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SetPush{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SetPush Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_SetPush: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SetPush{
		Ret:    uint32(cret.RET_OK),
		Id:     cmsg.Id,
		IsPush: cmsg.IsPush,
	}
	if cmsg.Id == uint32(common.PUSH_SET_TYPE_PST_NONE) || cmsg.Id >= uint32(common.PUSH_SET_TYPE_PST_MAX) {
		l4g.Errorf("user: %d C2L_SetPush: push id is invalid. id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	c.User.SetPush(cmsg.Id, cmsg.IsPush)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SetPush, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LGetPushCommand struct {
	base.UserCommand
}

func (c *C2LGetPushCommand) Error(msg *cl.L2C_GetPush, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetPush, msg)
	return false
}

func (c *C2LGetPushCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetPushCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetPush{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetPush Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetPush: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GetPush{
		Ret: uint32(cret.RET_OK),
	}
	smsg.PushSetting = c.User.FlushPushSetting()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetPush, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LGetSeasonFlashBackDataCommand struct {
	base.UserCommand
}

func (c *C2LGetSeasonFlashBackDataCommand) Error(msg *cl.L2C_GetSeasonFlashBackData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonFlashBackData, msg)
	return false
}

func (c *C2LGetSeasonFlashBackDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetSeasonFlashBackDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetSeasonFlashBackData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetSeasonFlashBackData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetSeasonFlashBackData: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GetSeasonFlashBackData{
		Ret:      uint32(cret.RET_OK),
		SeasonId: cmsg.SeasonId,
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON), c.Srv) {
		l4g.Errorf("user: %d C2L_GetSeasonFlashBackData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_FLASH_BACK), c.Srv) {
		l4g.Errorf("user: %d C2L_GetSeasonFlashBackData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cmsg.SeasonId == 0 {
		smsg.Ids = c.User.GetSeasonFlashBackIds()
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonFlashBackData, smsg)
		return true
	}

	if goxml.GetData().SeasonInfoM.Index(cmsg.SeasonId) == nil {
		l4g.Errorf("user: %d C2L_GetSeasonFlashBackData: seasonId:%d error", c.Msg.UID, cmsg.SeasonId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	c.Srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_GetSeasonFlashBackData), c.Msg.UID, &p2l.L2P_GetSeasonFlashBackData{
		Uid:      c.User.ID(),
		SeasonId: cmsg.SeasonId,
	})
	return true
}

type C2LGetDefFormationPowerCommand struct {
	base.UserCommand
}

func (c *C2LGetDefFormationPowerCommand) Error(msg *cl.L2C_GetDefFormationPower, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GetDefFormationPower, msg)
	return false
}

func (c *C2LGetDefFormationPowerCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LGetDefFormationPowerCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_GetDefFormationPower{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GetDefFormationPower Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_GetDefFormationPower: %s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GetDefFormationPower{
		Ret: uint32(cret.RET_OK),
	}

	//检查是否操作太频繁
	if !c.User.CheckAndSetOperateInterval(c.Srv, character.OIViewUserDefPower) {
		l4g.Errorf("user: %d C2L_GetDefFormationPower: operate too often", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_OPERATE_TOO_OFTEN))
	}
	//竞技场场景最多只有三个人
	if len(cmsg.FormationSidUid) > 3 || len(cmsg.FormationSidUid) == 0 {
		l4g.Errorf("user: %d C2L_GetDefFormationPower: too much user", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	repeatedCheck := make(map[uint64]map[uint32]map[uint64]struct{})
	for index, v := range cmsg.FormationSidUid { //nolint:varnamelen
		if v == nil {
			l4g.Errorf("user:%d C2L_GetDefFormationPower cmsg uids index:%d is nil", c.Msg.UID, index)
			return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		_, exist := repeatedCheck[v.Uid]
		if !exist {
			repeatedCheck[v.Uid] = make(map[uint32]map[uint64]struct{})
		} else {
			l4g.Errorf("user:%d C2L_GetDefFormationPower req uid:%d is repeated", c.Msg.UID, v.Uid)
			return c.Error(smsg, uint32(ret.RET_REPEATED_PARAM))
		}
		formationInfo := goxml.GetData().FormationInfoM.Index(v.FormationId)
		if formationInfo == nil {
			l4g.Errorf("user:%d C2L_GetDefFormationPower uid:%d formationId:%d is error", c.Msg.UID, v.Uid, v.FormationId)
			return c.Error(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		_, exist = repeatedCheck[v.Uid][v.FormationId]
		if !exist {
			repeatedCheck[v.Uid][v.FormationId] = make(map[uint64]struct{})
		}
		_, exist = repeatedCheck[v.Uid][v.FormationId][v.Sid]
		if exist {
			l4g.Errorf("user:%d C2L_GetDefFormationPower uid:%d formationId:%d sid:%d is repeated", c.Msg.UID, v.Uid, v.FormationId, v.Sid)
			return c.Error(smsg, uint32(ret.RET_REPEATED_PARAM))
		}
		repeatedCheck[v.Uid][v.FormationId][v.Sid] = struct{}{}
	}

	req := &AsyncC2LGetDefFormationPowerReq{
		callUid: c.Msg.UID,
		cmsg:    cmsg,
		smsg:    smsg,
	}

	//查看跨服user
	c.GetUserBattleDataForDefPowerFromAll(cmsg.FormationSidUid, req)
	return true
}

type AsyncC2LGetDefFormationPowerReq struct {
	callUid uint64
	cmsg    *cl.C2L_GetDefFormationPower
	smsg    *cl.L2C_GetDefFormationPower
}

func (ar *AsyncC2LGetDefFormationPowerReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	cmsg := ar.cmsg
	var needSendClient bool
	user := srv.UserM().GetUser(ar.callUid)
	if user != nil && user.State() == character.StateOnline {
		needSendClient = true
	}
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_GetDefFormationPower: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = retCode
		if needSendClient {
			user.SendCmdToGateway(cl.ID_MSG_L2C_GetDefFormationPower, smsg)
		}
		return false
	}

	datas := dbData.([]*db.UserBattleData)
	FormationSidUidPowers := make([]*cl.FormationSidUidPower, 0, len(datas))
	l4g.Debugf("AsyncC2LGetDefFormationPowerReq Resp:%v", datas)
	for _, v := range cmsg.FormationSidUid { //nolint:varnamelen
		if v == nil {
			continue
		}
		for _, data := range datas {
			if data == nil || data.User == nil {
				continue
			}
			if v.Uid == data.User.Id && v.FormationId == data.FormationId {
				//user := character.GetUserFromUserBattleData(srv,data)
				//defPower := user.CalFormationPower(v.FormationId)
				FormationSidUidPowers = append(FormationSidUidPowers, &cl.FormationSidUidPower{
					FormationSidUid: v.Clone(),
					DefPower:        data.Power,
				})
				continue
			}
		}
	}

	clientMsg := &cl.L2C_GetDefFormationPower{
		Ret:                   uint32(r2l.RET_OK),
		FormationSidUidPowers: FormationSidUidPowers,
	}

	crossMsg := &l2c.L2CS_SeasonArenaUpdateFormationPower{}
	for _, formationSidUidPower := range FormationSidUidPowers {
		_, exit := goxml.SeasonArenaDefender2Attack[formationSidUidPower.FormationSidUid.FormationId]
		if exit {
			crossMsg.Data = append(crossMsg.Data, formationSidUidPower.Clone())
		}
	}
	if needSendClient {
		user.SendCmdToGateway(cl.ID_MSG_L2C_GetDefFormationPower, clientMsg)
	}

	arenaM, ok := srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if ok && arenaM.IsCrossConnected() {
		srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaUpdateFormationPower, 0, crossMsg)
	}

	return true
}

type AsyncC2LSetNameReq struct {
	OldName string
	Smsg    *cl.L2C_SetName
}

func (ar *AsyncC2LSetNameReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) && retCode != uint32(ret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LSetNameReq: checkName error:%d", args.UID, retCode)
		ar.Smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SetName, ar.Smsg)
		return false
	}
	srv.RedisActor().AddMessage(uint32(r2l.ID_MSG_L2R_SetName), args.UID, &r2l.L2R_SetName{
		Id:      args.UID,
		OldName: ar.OldName,
		NewName: ar.Smsg.Name,
	})
	return true
}

type C2LActivityScheduleGetDataCommand struct {
	base.UserCommand
}

func (c *C2LActivityScheduleGetDataCommand) Return(msg *cl.L2C_ActivityScheduleGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityScheduleGetData, msg)
	return true
}

func (c *C2LActivityScheduleGetDataCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_ActivityScheduleGetData{}
	cmsg := &cl.C2L_ActivityScheduleGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityScheduleGetData Unmarshal error: %d %s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d C2L_ActivityScheduleGetData: %s", c.Msg.UID, cmsg)
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_SCHEDULE), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityScheduleGetData: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	scheduleConfig := c.Srv.GetActivityScheduleConfig()
	if scheduleConfig != nil {
		smsg.Data = &cl.ActivityScheduleDatas{}
		for _, activity := range scheduleConfig.Datas {
			if cmsg.StartTime >= activity.EndTime || cmsg.EndTime <= activity.StartTime {
				continue
			}
			smsg.Data.Datas = append(smsg.Data.Datas, activity.Clone())
		}
	}

	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LTestEtcdCodeCommand struct {
	base.LimitedCommand
}

func (c *C2LTestEtcdCodeCommand) Return(msg *cl.L2C_TestEtcdGiftCode, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TestEtcdGiftCode, msg)
	return true
}

func (c *C2LTestEtcdCodeCommand) Execute(ctx context.Context) bool {
	smsg := &cl.L2C_TestEtcdGiftCode{}
	cmsg := &cl.C2L_TestEtcdGiftCode{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TestEtcdGiftCode Unmarshal error: %d %s", c.Msg.UID, err)
		return c.Return(smsg, uint32(ret.RET_ERROR))
	}
	c.Trace(cmsg)

	smsg.CodeId = cmsg.CodeId
	smsg.Awards = c.Srv.GetGiftCodeAwards(cmsg.CodeId)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LCommonRankLikeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LCommonRankLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCommonRankLikeCommand) Error(msg *cl.L2C_CommonRankLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CommonRankLike, msg)
	return false
}

func (c *C2LCommonRankLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CommonRankLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CommonRankLike Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CommonRankLike: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_CommonRankLike{
		Ret:    uint32(cret.RET_OK),
		RankId: cmsg.RankId,
		Uid:    cmsg.Uid,
	}

	now := time.Now().Unix()

	var funcId, numType, awardReason uint32
	var awards []*cl.Resource
	switch cmsg.RankId {
	case goxml.ActivityTowerRankID:
		funcId = uint32(common.FUNCID_MODULE_TOWER)
		if !goxml.GetData().ConfigInfoM.ActivityTowerOpen(c.Srv.ServerOpenDay(now), c.User.Level()) {
			l4g.Errorf("user: %d C2L_CommonRankLike: function not open. id %d", c.Msg.UID, funcId)
			return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
		}
		numType = uint32(common.PURCHASEID_ACTIVITY_TOWER_RANK_LIKE_COUNT)
		awards = goxml.GetData().ConfigInfoM.ActivityTowerLikeAwards()
		awardReason = uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_TOWER_LIKE)
	case goxml.ActivityMirageRankID:
		funcId = uint32(common.FUNCID_MODULE_MIRAGE)
		if !goxml.GetData().ConfigInfoM.ActivityMirageOpen(c.Srv.ServerOpenDay(now), c.User.Level()) {
			l4g.Errorf("user: %d C2L_CommonRankLike: function not open. id %d", c.Msg.UID, funcId)
			return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
		}
		numType = uint32(common.PURCHASEID_ACTIVITY_MIRAGE_RANK_LIKE_COUNT)
		awards = goxml.GetData().ConfigInfoM.ActivityMirageLikeAwards()
		awardReason = uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_MIRAGE_LIKE)
	default:
		l4g.Errorf("user: %d C2L_CommonRankLike: invalid rankId. %d", c.Msg.UID, cmsg.RankId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(funcId, c.Srv) {
		l4g.Errorf("user: %d C2L_CommonRankLike: function not open. id %d", c.Msg.UID, funcId)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	fret, _, numSummary := c.User.CheckNumByType(numType, 1)
	if fret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_CommonRankLike: check num err: %d", c.Msg.UID, fret)
		return c.Error(smsg, fret)
	}

	rankElement := c.Srv.CommonRankM().GetElement(cmsg.RankId, cmsg.Uid)
	if rankElement == nil {
		l4g.Errorf("user:%d C2L_CommonRankLike: like user %d not exist in rank %d", c.Msg.UID, cmsg.Uid, cmsg.RankId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	smsg.LikeCount = rankElement.(rank.CommonRankValuer).Like()
	c.Srv.CommonRankM().SetChange(cmsg.RankId, rankElement.(rank.CommonRankValuer).Key(), rankElement)

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail, awardReason, 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_CommonRankLike: award failed error code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	c.User.AddNumByType(numType, numSummary)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CommonRankLike, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LTestDropCommand struct {
	base.LimitedCommand
}

func (c *C2LTestDropCommand) Return(msg *cl.L2C_TestDrop, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TestDrop, msg)
	return true
}

func (c *C2LTestDropCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_TestDrop{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_TestDrop Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_TestDrop: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_TestDrop{}

	res := make(map[uint32]map[uint32]uint32)
	for i := 0; i < int(cmsg.Count); i++ {
		dropAwards, _ := c.User.Drop().DoDrop(c.Srv.Rand(), cmsg.DropGroup)
		for _, drop := range dropAwards {
			if _, ok := res[drop.Type]; !ok {
				res[drop.Type] = make(map[uint32]uint32)
			}
			res[drop.Type][drop.Value] += drop.Count
		}
	}

	var awards []*cl.Resource
	for dropType, v := range res {
		for dropValue, dropCount := range v {
			awards = append(awards, goxml.GenSimpleResource(dropType, dropValue, dropCount))
		}
	}

	smsg.Awards = awards

	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LMuteAccountCommand struct {
	base.UserCommand
}

func (c *C2LMuteAccountCommand) Error(msg *cl.L2C_MuteAccount, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_MuteAccount, msg)
	return true
}

func (c *C2LMuteAccountCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_MuteAccount{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_MuteAccount Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_MuteAccount: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_MuteAccount{}

	day, exist := goxml.MuteAccountType[cmsg.MuteDay]
	if !exist {
		l4g.Errorf("user:%d muteAccount day:%d failed", c.User.ID(), cmsg.MuteDay)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	if c.User.CanMute() {
		c.GetUserSnapshotsFromAll([]*cl.ServerUser{{
			Sid: cmsg.ServerId,
			Uid: cmsg.Uid,
		}}, &AsyncMuteAccount{smsg, cmsg, day}, 0)
		return true
	} else {
		l4g.Errorf("user:%d muteAccount not have mute privilege", c.User.ID())
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
}

type AsyncMuteAccount struct {
	smsg *cl.L2C_MuteAccount
	cmsg *cl.C2L_MuteAccount
	Day  uint32
}

func (ar *AsyncMuteAccount) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_MuteAccount: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = retCode
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_MuteAccount, smsg)
		return false
	}

	snapshots := dbData.([]*cl.UserSnapshot)
	if len(snapshots) == 0 {
		l4g.Errorf("user: %d C2L_MuteAccount: get snapshot failed, no data", args.UID)
		smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_MuteAccount, smsg)
		return false
	}

	var userS *cl.UserSnapshot
	if len(snapshots) >= 1 {
		userS = snapshots[0]
	}

	if userS == nil {
		l4g.Errorf("user:%d C2L_MuteAccount: get snapshot failed", args.UID)
		smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_MuteAccount, smsg)
		return false
	}

	now := time.Now().Unix()
	endTime := now + int64(ar.Day)*util.DaySecs
	sUid := strconv.FormatUint(args.Caller.ID(), 10)
	pmsg := &p2l.L2P_MuteAccount{
		BanUserId: userS.Id,
		UserName:  userS.Name,
		ServerId:  userS.Sid,
		StartTime: now,
		EndTime:   endTime,
		Baner:     sUid,
		OpGroupId: uint32(srv.OpGroup()),
		RoleLevel: userS.Level,
		VipLevel:  userS.Vip,
	}
	srv.SendCmdToPlatform(uint32(p2l.ID_MSG_L2P_MuteAccount), args.Caller.ID(), pmsg)
	return true
}
