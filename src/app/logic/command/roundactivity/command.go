package roundactivity

import (
	"app/goxml"
	"context"

	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_RoundActivityGetData), &C2LRoundActivityGetDataCommand{}, state)             //获取活动数据
	cmds.Register(uint32(cl.ID_MSG_C2L_RoundActivityRecvTaskAward), &C2LRoundActivityRecvTaskAwardCommand{}, state) //领取任务奖励
}

type C2LRoundActivityGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LRoundActivityGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRoundActivityGetDataCommand) Error(msg *cl.L2C_RoundActivityGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RoundActivityGetData, msg)
	return false
}

func (c *C2LRoundActivityGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RoundActivityGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RoundActivityGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_RoundActivityGetData: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_RoundActivityGetData{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ROUND_ACTIVITY), c.Srv) {
		l4g.Errorf("user: %d C2L_RoundActivityGetData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	smsg.Datas = c.User.RoundActivityM().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RoundActivityGetData, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LRoundActivityRecvTaskAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LRoundActivityRecvTaskAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LRoundActivityRecvTaskAwardCommand) Error(msg *cl.L2C_RoundActivityRecvTaskAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RoundActivityRecvTaskAward, msg)
	return false
}

func (c *C2LRoundActivityRecvTaskAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_RoundActivityRecvTaskAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_RoundActivityRecvTaskAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_RoundActivityRecvTaskAward: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_RoundActivityRecvTaskAward{
		Ret: uint32(cret.RET_OK),
	}

	count := len(cmsg.Ids)
	if count == 0 || count > character.RoundActivityRecvAwardCountLimit {
		l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: param ids err. count:%d", c.Msg.UID, count)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ROUND_ACTIVITY), c.Srv) {
		l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	raManager := c.User.RoundActivityM()
	uniq := make(map[uint32]struct{})
	taskInfos := make([]*goxml.RoundActivityTaskInfo, 0, count)
	var category, stage uint32
	for k, id := range cmsg.Ids {
		if id == 0 {
			l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: param id=0", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if _, exist := uniq[id]; exist {
			l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: param repeat, ids:%v",
				c.Msg.UID, cmsg.Ids)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		uniq[id] = struct{}{}

		info := goxml.GetData().RoundActivityTaskInfoM.Index(id)
		if info == nil {
			l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: task info not exist, taskID:%d",
				c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		taskInfos = append(taskInfos, info)

		//验证任务id是否属于同一类型同一期的活动
		if k == 0 {
			category = info.Category
			stage = info.Stage
		} else {
			if info.Category != category || info.Stage != stage {
				l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: not same category or stage, ids:%v",
					c.Msg.UID, cmsg.Ids)
				return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
			}
		}

		//是否可领奖
		if !raManager.CanReceiveAward(info, true) {
			l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: cannot receive, taskID:%d",
				c.Msg.UID, id)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	awards := goxml.GetData().RoundActivityTaskInfoM.GetAwardByIDs(cmsg.Ids)
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_ROUND_ACTIVITY_RECV_TASK_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_RoundActivityRecvTaskAward: award error. retCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	raManager.ReceiveTasksAward(taskInfos)
	raManager.Save()

	smsg.Ids = cmsg.Ids
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_RoundActivityRecvTaskAward, smsg)
	c.User.LogRoundActivityRecvTaskAward(c.Srv, cmsg.Ids, character.RoundActivityRecvAwardFromUser,
		category, stage)
	return c.ResultOK(smsg.Ret)
}
