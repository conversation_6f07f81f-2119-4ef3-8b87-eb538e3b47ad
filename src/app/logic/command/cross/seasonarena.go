package cross

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/seasonarena"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/cr"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"context"
	"strconv"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitSeasonArena(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaGetData), &CS2LSeasonArenaGetDataCommand{}, state)         //获取赛季竞技场数据
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaGetSta), &CS2LSeasonArenaGetStaCommand{}, state)           //获取赛季竞技场状态
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaRefresh), &CS2LSeasonArenaRefreshCommand{}, state)         //刷新赛季竞技场对手
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaFightStart), &CS2LSeasonArenaFightStartCommand{}, state)   //战斗发起返回
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaFightFinish), &CS2LSeasonArenaFightFinishCommand{}, state) //战斗结束返回
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaDefinePush), &CS2LSeasonArenaDefinePushCommand{}, state)   //防守方推送
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaGetRankList), &CS2LSeasonArenaGetRankListCommand{}, state) //赛季获取排行榜
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaGetFame), &CS2LSeasonArenaGetFameCommand{}, state)         //获取荣耀殿堂数据
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaTopChange), &CS2LSeasonArenaTopChangeCommand{}, state)     //头部玩家更换
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonArenaAward), &CS2LSeasonArenaAwardCommand{}, state)             //结算发奖
}

type CS2LSeasonArenaGetDataCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonArenaGetDataCommand) Error(smsg *cl.L2C_SeasonArenaGetData, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaGetData, smsg)
	return false
}

func (c *CS2LSeasonArenaGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaGetData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2L_SeasonArenaGetData user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_SeasonArenaGetData{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CS2L_SeasonArenaGetData user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if cmsg.Sta == nil {
		l4g.Errorf("CS2L_SeasonArenaGetData user:%d sta or SeasonArenaUserBase is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	arenaM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaGetData gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, cmsg.Ret)
	}
	var oldScore uint32
	var newScore uint32
	smsg.SeasonArenaData = &cl.SeasonArenaData{
		Sta:   cmsg.Sta,
		Users: cmsg.OpponentClient,
	}
	var formationId uint32
	if cmsg.SeasonArenaUserBase != nil {
		saUser := arenaM.GetUser(c.User.ID())
		if saUser != nil {
			oldScore = saUser.GetScore()
		}

		saUser = arenaM.UpdateUser(c.User.ID(), cmsg.Sta, cmsg.SeasonArenaUserBase)

		if saUser != nil {
			newScore = saUser.GetScore()
			if saUser.GetNeedClearTask() {
				c.User.SeasonArenaTask().RoundClear()
				saUser.SetNeedClearTask(false)
				arenaM.SetChangeUser(saUser)
			}
		}

		if saUser.GetSkipFormation() && !c.User.SeasonArenaSkipFormation() {
			c.User.SetSeasonArenaSkipFormation(true)
		}
		smsg.SeasonArenaData.User = saUser.BuildSeasonArenaUserBase(c.User.SeasonArenaTask().GetData(), c.User.SeasonArenaSkipFormation())
		divisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(cmsg.SeasonArenaUserBase.Score, cmsg.SeasonArenaUserBase.Rank)
		if divisionInfo == nil {
			l4g.Errorf("user: %d CS2L_SeasonArenaGetData : score:%d rank:%d division info is nil.", c.User.ID(), cmsg.SeasonArenaUserBase.Score, cmsg.SeasonArenaUserBase.Rank)
		} else {
			formationId = divisionInfo.TeamNum
		}
	}
	smsg.Cross = cmsg.Cross
	smsg.LogicPartition = c.Srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA))
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaGetData, smsg)

	snapshotM := &l2c.L2CS_SeasonArenaUpdateSnapshot{
		Snapshot: c.User.NewUserSnapshot(formationId),
	}

	if newScore != oldScore || (cmsg.Snapshot != nil && ((cmsg.Snapshot.SeasonCrystalTop35Power != snapshotM.Snapshot.SeasonCrystalTop35Power) || (cmsg.Snapshot.DefensePower != snapshotM.Snapshot.DefensePower))) {
		if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaUpdateSnapshot, c.Msg.UID, snapshotM) {
			l4g.Errorf("user: %d CS2L_SeasonArenaGetData L2CS_SeasonArenaUpdateSnapshot: send cross msg fail", c.User.ID())
		}
	}

	return true
}

type CS2LSeasonArenaGetStaCommand struct {
	base.Command
}

func (c *CS2LSeasonArenaGetStaCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaGetSta{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaGetSta Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_SeasonArenaGetSta user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	saM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaGetSta gstManager not exist.", c.Msg.UID)
		return false
	}

	saM.SetCrossState(cmsg.Sta)

	needSend, seasonID, round := saM.NeedAward()
	if needSend {
		l4g.Info("CS2L_SeasonArenaGetSta time:%d send to cross ID_MSG_L2CS_SeasonArenaAward seasonID:%d round:%d", time.Now().Unix(), seasonID, round)
		c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaAward, 0, &l2c.L2CS_SeasonArenaAward{
			Season: seasonID,
			Round:  round,
			Sid:    c.Srv.ServerID(),
		})
	}
	//开始结算本轮未活跃的玩家清除
	if saM.CheckSettlement() {
		saM.Settlement()
	}

	c.Srv.BroadcastCmdToClient(cl.ID_MSG_L2C_SeasonArenaState, &cl.L2C_SeasonArenaState{
		Ret: uint32(ret.RET_OK),
		Sta: cmsg.Sta,
	})

	return true
}

type CS2LSeasonArenaRefreshCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonArenaRefreshCommand) Error(smsg *cl.L2C_SeasonArenaRefresh, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaRefresh, smsg)
	return false
}

func (c *CS2LSeasonArenaRefreshCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaRefresh{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaRefresh Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2L_SeasonArenaRefresh user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonArenaRefresh{
		Ret:       uint32(ret.RET_OK),
		RefreshTm: cmsg.Time,
		Opponents: cmsg.Opponent,
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CS2L_SeasonArenaRefresh user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	arenaM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaRefresh gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	arenaUser := arenaM.GetUser(c.Msg.UID)
	if arenaUser == nil {
		l4g.Errorf("user: %d CS2L_SeasonArenaRefresh: get user failed", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_SEASON_ARENA_USER_NOT_FIND))
	}

	arenaUser.SetRefreshOpponentTime(cmsg.Time)
	arenaUser.SetFreeRefreshFlag(false)
	arenaM.SetChangeUser(arenaUser)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaRefresh, smsg)

	return true
}

type CS2LSeasonArenaFightStartCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonArenaFightStartCommand) Error(smsg *cl.L2C_SeasonArenaFight, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsg)
	return false
}

func (c *CS2LSeasonArenaFightStartCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaFightStart{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaFightStart Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2L_SeasonArenaFightStart user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsgToClient := &cl.L2C_SeasonArenaFight{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) && cmsg.Ret != uint32(ret.RET_SEASON_ARENA_FIGHT_CHECK_FREE_REFRESH) {
		l4g.Errorf("user:%d season arena fight start error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsgToClient, cmsg.Ret)
	}

	saM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaFightStart gstManager not exist.", c.Msg.UID)
		return c.Error(smsgToClient, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}

	saUser := saM.GetUser(c.Msg.UID)
	if saUser == nil {
		l4g.Errorf("user:%d CS2L_SeasonArenaFightStart season arena is not sign", c.Msg.UID)
		return c.Error(smsgToClient, uint32(ret.RET_SEASON_ARENA_USER_NOT_FIND))
	}

	defer func() {
		if saUser != nil && smsgToClient.Ret != uint32(ret.RET_OK) {
			saUser.Unlock()
		}
	}()

	if cmsg.Ret == uint32(ret.RET_SEASON_ARENA_FIGHT_CHECK_FREE_REFRESH) {
		saUser.SetFreeRefreshFlag(true)
		saM.SetChangeUser(saUser)
		smsgToClient.Ret = uint32(ret.RET_SEASON_ARENA_FIGHT_CHECK_FREE_REFRESH)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return true
	}

	if !saUser.CheckChallengeCount() {
		l4g.Errorf("user:%d CS2L_SeasonArenaFightStart: check fight count failed ", c.Msg.UID)
		return c.Error(smsgToClient, uint32(ret.RET_SEASON_ARENA_FIGHT_COUNT_CHECK_FAILED))
	}

	if cmsg.BotId == 0 {
		req := &AsyncL2CSeasonArenaStartReq{
			Uid:             cmsg.DefenderUid,
			Sid:             cmsg.DefenderSid,
			DefFormationID:  cmsg.DefenseFormationId,
			AttackFormation: cmsg.AttackFormationId,
			FormationMode:   cmsg.FormationMode,
			ShowTeam:        cmsg.ShowTeam,
		}
		reqUsers := []*cl.ServerUser{{Sid: cmsg.DefenderSid, Uid: cmsg.DefenderUid}}
		c.GetUserBattleDataFromAll(reqUsers, req, cmsg.DefenseFormationId)
		return true
	}

	botInfo := goxml.GetData().SeasonArenaBotInfoM.Index(cmsg.BotId)
	if botInfo == nil {
		smsgToClient.Ret = uint32(ret.RET_SYSTEM_DATA_ERROR)
		l4g.Errorf("user: %d CS2L_SeasonArenaFightStart botInfo not exit. botId:%d ", c.User.ID(), cmsg.BotId)
		return c.Error(smsgToClient, smsgToClient.Ret)
	}

	win, reportID, attackRet := c.User.SeasonArenaRobot(c.Srv, botInfo.MonsterGroups,
		cmsg.BotName, saUser.GetClientData(), cmsg.AttackFormationId, cmsg.FormationMode, botInfo.ShowLevel, cmsg.ShowTeam)
	if attackRet != ret.RET_OK {
		smsgToClient.Ret = uint32(ret.RET_BATTLE_ERROR)
		l4g.Errorf("user: %d CS2L_SeasonArenaFightStart error. ret:%d ", c.User.ID(), smsgToClient.Ret)
		return c.Error(smsgToClient, smsgToClient.Ret)
	}

	smsg := &l2c.L2CS_SeasonArenaFightFinish{
		DefenderSid:       cmsg.DefenderSid,
		DefenderUid:       cmsg.DefenderUid,
		AttackerUid:       cmsg.AttackerUid,
		AttackerSid:       cmsg.AttackerSid,
		ReportId:          reportID,
		AttackerWin:       win,
		IsBot:             true,
		AttackFormationId: cmsg.AttackFormationId,
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaFightFinish, c.Msg.UID, smsg)
	return true
}

type AsyncL2CSeasonArenaStartReq struct {
	Uid             uint64
	Sid             uint64
	DefFormationID  uint32
	AttackFormation uint32
	FormationMode   uint32
	ShowTeam        uint32
}

func (ar *AsyncL2CSeasonArenaStartReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsgToClient := &cl.L2C_SeasonArenaFight{
		Ret: uint32(ret.RET_OK),
	}
	if retCode != uint32(ret.RET_OK) {
		smsgToClient.Ret = retCode
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return false
	}

	saM, ok := srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		smsgToClient.Ret = uint32(ret.RET_SYSTEM_DATA_ERROR)
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq SeasonArenaM not exit. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return false
	}

	saUser := saM.GetUser(args.UID)
	if saUser == nil {
		smsgToClient.Ret = uint32(ret.RET_SEASON_ARENA_USER_NOT_FIND)
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq SeasonArenaM not exit. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return true
	}

	snaps, ok := dbData.([]*db.UserBattleData)
	if !ok {
		smsgToClient.Ret = uint32(ret.RET_ERROR)
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return false
	}

	snapm := make(map[uint64]*db.UserBattleData, len(snaps))
	for _, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq: no data. opUid:%d opSid:%d", args.UID, ar.Uid, ar.Sid)
			smsgToClient.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
			return false
		}
		snapm[v.User.Id] = v
	}

	if snapm[ar.Uid] == nil {
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq: no data. opUid:%d opSid:%d", args.UID, ar.Uid, ar.Sid)
		smsgToClient.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return false
	}

	opUser := character.GetUserFromUserBattleData(srv, snapm[ar.Uid])
	if opUser == nil {
		smsgToClient.Ret = uint32(ret.RET_ERROR)
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return false
	}

	flag, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(ar.AttackFormation)
	if !flag {
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq: formation info not exist. opUid:%d opSid:%d", args.UID, ar.Uid, ar.Sid)
		smsgToClient.Ret = uint32(ret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return false
	}

	win, reportID, attackRet := args.Caller.SeasonArenaOpponent(srv, opUser, teamNum, saUser.GetClientData(), ar.AttackFormation, ar.DefFormationID, ar.FormationMode, ar.ShowTeam)
	if attackRet != ret.RET_OK {
		smsgToClient.Ret = uint32(ret.RET_BATTLE_ERROR)
		l4g.Errorf("user: %d AsyncL2CSeasonArenaStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsgToClient)
		return false
	}

	smsg := &l2c.L2CS_SeasonArenaFightFinish{
		DefenderUid:       ar.Uid,
		DefenderSid:       ar.Sid,
		AttackerUid:       args.Caller.ID(),
		AttackerSid:       args.Caller.ServerID(),
		ReportId:          reportID,
		AttackerWin:       win,
		DefenderSnapshot:  opUser.NewUserSnapshot(ar.DefFormationID),
		AttackFormationId: ar.AttackFormation,
	}

	srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaFightFinish, args.UID, smsg)
	return true
}

type CS2LSeasonArenaFightFinishCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonArenaFightFinishCommand) Error(smsg *cl.L2C_SeasonArenaFight, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsg)
	return false
}

//nolint:funlen
func (c *CS2LSeasonArenaFightFinishCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaFightFinish{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaFightFinish Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2L_SeasonArenaFightFinish user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonArenaFight{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CS2L_SeasonArenaFightFinish user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	arenaM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaFightFinish gstManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_SEASON_ARENA_USER_NOT_FIND))
	}

	saUser := arenaM.GetUser(c.User.ID())
	if saUser == nil {
		l4g.Errorf("user: %d CS2L_SeasonArenaFightFinish season arena is not sign", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_SEASON_ARENA_USER_NOT_FIND))
	}

	now := time.Now().Unix()
	smsg.ReportId = cmsg.ReportId

	attacker := c.User.NewMiniUserSnapshot()
	attacker.Power = c.User.GetCrystalPower()
	attacker.Level = c.User.GetSeasonLevel()
	scoreChange := int32(cmsg.AttackerNewResult.Score) - int32(cmsg.AttackerOldResult.Score)
	myLogM := arenaM.GetLogM().GetOwnerLogs(saUser)
	var teamCount uint32
	attackFormationInfo := goxml.GetData().FormationInfoM.Index(cmsg.AttackFormationId)
	if attackFormationInfo != nil {
		teamCount = attackFormationInfo.TeamNum
	}
	log := seasonarena.NewSeasonArenaLog(c.Srv.CreateUserLogID(), cmsg.ReportId, scoreChange, cmsg.AttackerWin, cmsg.IsBot, attacker,
		cmsg.DefenderSnapshot, now, teamCount)
	myLogM.AddLog(c.Srv, log)
	arenaM.GetLogM().SetChange(myLogM)

	smsg.Win = cmsg.AttackerWin
	smsg.ClientData = saUser.GetClientData()
	smsg.SelfChange = &cl.SeasonArenaUserScoreAndRank{
		OldRank:  cmsg.AttackerOldResult.Rank,
		NewRank:  cmsg.AttackerNewResult.Rank,
		OldScore: cmsg.AttackerOldResult.Score,
		NewScore: cmsg.AttackerNewResult.Score,
	}
	c.User.FireCommonEvent(c.Srv.EventM(), event.AeSeasonArenaScoreToX, uint64(cmsg.AttackerNewResult.Score))

	smsg.OpponentChange = &cl.SeasonArenaUserScoreAndRank{
		OldRank:  cmsg.DefenderOldResult.Rank,
		NewRank:  cmsg.DefenderNewResult.Rank,
		OldScore: cmsg.DefenderOldResult.Score,
		NewScore: cmsg.DefenderNewResult.Score,
	}

	saUser.SetScore(cmsg.Attacker.Score)
	saUser.SetRank(cmsg.Attacker.Rank)
	oldDivision := saUser.GetDivision()
	saUser.CalcDivision()
	newDivision := saUser.GetDivision()
	saUser.Unlock()
	saUser.ReduceChallengeCount()
	if !saUser.GetSkipFormation() && goxml.GetData().SeasonArenaConfigInfoM.CheckSkipFormation(saUser.GetScore()) {
		saUser.SetSkipFormation(true)
	}
	if saUser.GetSkipFormation() && !c.User.SeasonArenaSkipFormation() {
		c.User.SetSeasonArenaSkipFormation(true)
	}
	smsg.SkipFormation = c.User.SeasonArenaSkipFormation()
	arenaM.SetChangeUser(saUser)
	var winFlag uint32
	if cmsg.AttackerWin {
		winFlag = 1
	}
	defDefaultTeamID := uint32(common.FORMATION_ID_FI_SEASON_ARENA_THREE_DEFENSE)
	defNewDivision := uint32(1)
	defOldRankInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(cmsg.DefenderOldResult.Score, cmsg.DefenderOldResult.Rank)
	if defOldRankInfo != nil {
		defDefaultTeamID = defOldRankInfo.TeamNum
	}
	defNewRankInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(cmsg.DefenderNewResult.Score, cmsg.DefenderNewResult.Rank)
	if defNewRankInfo != nil {
		defNewDivision = defNewRankInfo.Id
	}
	formationID := goxml.SeasonArenaDefender2Attack[defDefaultTeamID]
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeSeasonArenaFight, 1, winFlag)
	if oldDivision != newDivision {
		c.User.LogSeasonArenaDivisionChange(c.Srv, oldDivision, newDivision, arenaM.CrossState.Round, c.Srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)))
	}
	var defId, defSid, defArena, defGid uint64
	if cmsg.DefenderSnapshot != nil {
		defId = cmsg.DefenderSnapshot.Id
		defSid = cmsg.DefenderSnapshot.Sid
		defArena = uint64(cmsg.DefenderArena)
		defGid = uint64(cmsg.DefenderGid)
	}
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeHotRankUpdate, uint64(saUser.GetRank()), uint32(cmsg.AttackFormationId))
	c.User.LogSeasonArenaFight(c.Srv, defId, defSid, defArena, defGid, c.User.ContextID(),
		formationID, arenaM.CrossState.Round, smsg.SelfChange, smsg.OpponentChange, newDivision, defNewDivision, cmsg.AttackerWin, cmsg.IsBot, cmsg.ReportId)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaFight, smsg)

	return true
}

type CS2LSeasonArenaDefinePushCommand struct {
	base.Command
}

//nolint:funlen
func (c *CS2LSeasonArenaDefinePushCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaDefinePush{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaDefinePush Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_SeasonArenaDefinePush user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CS2L_SeasonArenaDefinePush user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	arenaM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaDefinePush gstManager not exist.", c.Msg.UID)
		return false
	}

	saUser := arenaM.GetUser(c.Msg.UID)
	if saUser == nil || !saUser.IsCurSeasonRound(cmsg.Sta.Season, cmsg.Sta.Round) {
		saUser = arenaM.UpdateUser(c.Msg.UID, cmsg.Sta, cmsg.SeasonArenaUserBase)
		if saUser == nil {
			l4g.Errorf("user: %d CS2L_SeasonArenaDefinePush user is nil.", c.Msg.UID)
			return false
		}
	}

	now := time.Now().Unix()

	var defenderMinSnapshot *cl.MiniUserSnapshot
	if cmsg.Defender != nil && cmsg.Defender.Snapshot != nil {
		defenderMinSnapshot = &cl.MiniUserSnapshot{
			Id:     cmsg.Defender.Snapshot.Id,
			Level:  cmsg.Defender.Snapshot.SeasonLv,
			Name:   cmsg.Defender.Snapshot.Name,
			BaseId: cmsg.Defender.Snapshot.BaseId,
			Power:  cmsg.Defender.Snapshot.CrystalPower,
			Sid:    cmsg.Defender.Snapshot.Sid,
		}
	}

	scoreChange := int32(cmsg.NewResult.Score) - int32(cmsg.OldResult.Score)
	myLogM := arenaM.GetLogM().GetOwnerLogs(saUser)
	var teamCount uint32
	attackFormationInfo := goxml.GetData().FormationInfoM.Index(cmsg.AttackFormationId)
	if attackFormationInfo != nil {
		teamCount = attackFormationInfo.TeamNum
	}
	log := seasonarena.NewSeasonArenaLog(c.Srv.CreateUserLogID(), cmsg.ReportId, scoreChange, cmsg.AttackerWin, false, cmsg.AttackerSnapshot,
		defenderMinSnapshot, now, teamCount)
	myLogM.AddLog(c.Srv, log)
	arenaM.GetLogM().SetChange(myLogM)

	oldDivision := saUser.GetDivision()
	oldDivisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.Index(oldDivision)
	saUser.SetScore(cmsg.NewResult.Score)
	saUser.SetRank(cmsg.NewResult.Rank)
	saUser.SetNewFightLog(true)
	saUser.CalcDivision()
	if !saUser.GetSkipFormation() && goxml.GetData().SeasonArenaConfigInfoM.CheckSkipFormation(saUser.GetScore()) {
		saUser.SetSkipFormation(true)
	}
	newDivision := saUser.GetDivision()
	if oldDivision != newDivision {
		character.NewMaybeOfflineUserLog(c.Srv, c.Msg.UID).LogSeasonArenaDivisionChange(c.Srv, oldDivision, newDivision, arenaM.CrossState.Round, c.Srv.GetCrossArea(uint32(l2c.CROSS_ACT_AREA_ID_NORMAL_AREA)))
	}
	newDivisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.Index(saUser.GetDivision())
	if oldDivisionInfo != nil && newDivisionInfo != nil && oldDivisionInfo.TeamNum != newDivisionInfo.TeamNum && cmsg.Defender != nil && cmsg.Defender.Snapshot != nil {
		req := &AsyncL2CSeasonArenaDefineFormationUpdateReq{
			defUid: cmsg.Defender.Snapshot.Id,
			defSid: cmsg.Defender.Snapshot.Sid,
		}
		c.GetUserSnapshotsFromAll([]*cl.ServerUser{
			{
				Uid: cmsg.Defender.Snapshot.Id,
				Sid: cmsg.Defender.Snapshot.Sid,
			},
		}, req, newDivisionInfo.TeamNum)
	}

	if cmsg.AttackerFullSnapshot != nil && cmsg.AttackerOldResult != nil && cmsg.AttackerNewResult != nil {
		aSnapshot := cmsg.AttackerFullSnapshot
		attackChange := &cl.SeasonArenaUserScoreAndRank{
			OldRank:  cmsg.AttackerOldResult.Rank,
			NewRank:  cmsg.AttackerNewResult.Rank,
			OldScore: cmsg.AttackerOldResult.Score,
			NewScore: cmsg.AttackerNewResult.Score,
		}

		myChange := &cl.SeasonArenaUserScoreAndRank{
			OldRank:  cmsg.OldResult.Rank,
			NewRank:  cmsg.NewResult.Rank,
			OldScore: cmsg.OldResult.Score,
			NewScore: cmsg.NewResult.Score,
		}

		myNewDivision := uint32(1)
		myNewDivisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(cmsg.NewResult.Score, cmsg.NewResult.Rank)
		if myNewDivisionInfo != nil {
			myNewDivision = myNewDivisionInfo.Id
		}

		attackNewDivision := uint32(1)
		attackNewDivisionInfo := goxml.GetData().SeasonArenaDivisionInfoM.GetDivision(cmsg.AttackerNewResult.Score, cmsg.AttackerNewResult.Rank)
		if attackNewDivisionInfo != nil {
			myNewDivision = attackNewDivisionInfo.Id
		}

		offlineUser := character.NewMaybeOfflineUserLog(c.Srv, c.Msg.UID)
		offlineUser.LogSeasonArenaBeFight(c.Srv, aSnapshot.Id, aSnapshot.Sid, uint64(aSnapshot.AreaId), aSnapshot.GuildId, arenaM.CrossState.Round,
			myChange, attackChange, myNewDivision, attackNewDivision, !cmsg.AttackerWin, cmsg.ReportId)
	}

	saUser.Unlock()
	arenaM.SetChangeUser(saUser)

	return true
}

type AsyncL2CSeasonArenaDefineFormationUpdateReq struct {
	defUid uint64
	defSid uint64
}

func (ar *AsyncL2CSeasonArenaDefineFormationUpdateReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsgToCross := &l2c.L2CS_SeasonArenaUpdateSnapshot{}
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d AsyncL2CSeasonArenaDefineFormationUpdateReq error. ret:%d ", ar.defUid, retCode)
		return false
	}

	saM, ok := srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d sid:%d AsyncL2CSeasonArenaDefineFormationUpdateReq get SeasonArenaM module failed ret:%d ", ar.defUid, ar.defSid, retCode)
		return false
	}

	saUser := saM.GetUser(ar.defUid)
	if saUser == nil {
		l4g.Errorf("user: %d sid:%d AsyncL2CSeasonArenaDefineFormationUpdateReq SeasonArenaM not exit. ret:%d ", ar.defUid, ar.defSid, retCode)
		return false
	}

	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d sid:%d AsyncL2CSeasonArenaDefineFormationUpdateReq convert dbData error. ret:%d ", ar.defUid, ar.defSid, retCode)
		return false
	}

	snapm := make(map[uint64]*cl.UserSnapshot, len(snaps))
	for _, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d sid:%d AsyncL2CSeasonArenaDefineFormationUpdateReq: no data. ret:%d", ar.defUid, ar.defSid, retCode)
			return false
		}
		snapm[v.Id] = v
	}

	if snapm[ar.defUid] == nil {
		l4g.Errorf("user: %d sid:%d AsyncL2CSeasonArenaDefineFormationUpdateReq: no data. ret: %d", ar.defUid, ar.defSid, retCode)
		return false
	}

	smsgToCross.Snapshot = snapm[ar.defUid]

	srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonArenaUpdateSnapshot, ar.defUid, smsgToCross)
	return true
}

type CS2LSeasonArenaGetRankListCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonArenaGetRankListCommand) Error(smsg *cl.L2C_SeasonArenaGetRankList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaGetRankList, smsg)
	return false
}

func (c *CS2LSeasonArenaGetRankListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaGetRankList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaGetRankList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2L_SeasonArenaGetRankList user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_SeasonArenaGetRankList{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CS2L_SeasonArenaGetRankList user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.List = cmsg.List
	smsg.SelfRank = cmsg.SelfRank
	smsg.SelfValue = cmsg.SelfValue

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaGetRankList, smsg)

	return true
}

type CS2LSeasonArenaGetFameCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonArenaGetFameCommand) Error(smsg *cl.L2C_SeasonArenaOfFame, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaOfFame, smsg)
	return false
}

func (c *CS2LSeasonArenaGetFameCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaGetFame{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaGetFame Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2L_SeasonArenaGetFame user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_SeasonArenaOfFame{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CS2L_SeasonArenaGetFame user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	saM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaGetFame gstManager not exist.", c.Msg.UID)
		return false
	}

	saM.CheckAndSetFrame(cmsg.Fame)
	saM.GetFrame(cmsg.Simple, smsg, c.User.ID())
	if smsg.List == nil {
		smsg.List = make([]*cl.RankValue, 0, 1)
	}
	smsg.Simple = cmsg.Simple
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonArenaOfFame, smsg)
	return true
}

type CS2LSeasonArenaTopChangeCommand struct {
	base.Command
}

func (c *CS2LSeasonArenaTopChangeCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaTopChange{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaTopChange Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_SeasonArenaTopChange user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	saM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonArenaTopChange gstManager not exist.", c.Msg.UID)
		return false
	}

	saM.UpdateTopUser(cmsg.Rank)

	return true
}

type CS2LSeasonArenaAwardCommand struct {
	base.Command
}

//nolint:funlen
func (c *CS2LSeasonArenaAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonArenaAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonArenaAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_SeasonArenaAward user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("CS2L_SeasonArenaAward user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	saM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("user:%d CS2L_SeasonArenaAward gstManager not exist.", c.Msg.UID)
		return false
	}

	if !saM.CanAward(cmsg.Season, cmsg.Round) {
		l4g.Errorf("user:%d CS2L_SeasonArenaAward can cmsg:%v not award logic last award:%v .", c.Msg.UID, cmsg, saM.GetSeasonArenaLastAward())
		return false
	}

	divisionAwardCompensateM := make(map[uint64]*crossSeasonDivisionAward) //KEY掩码
	divisionAwardM := make(map[uint32]*crossSeasonDivisionAward)           //KEY最高段位
	rankRewardM := make(map[*goxml.SeasonArenaRankRewardInfo][]*cr.CrossSeasonArenaServerUserBak)

	dataForGuildMedal := make([]*cl.GuildMedalSeasonArenaBak, 0, len(cmsg.GetServerBack().GetServerBack()))
	guildMedalNeedRank, guildMedalNeedDivision := goxml.GetData().GuildMedalTaskInfoM.GetSeasonArenaRankAndDivision()

	for _, userback := range cmsg.GetServerBack().GetServerBack() {
		if userback == nil {
			continue
		}
		saUser := saM.GetUser(userback.Uid)
		var compensateMask uint64
		var change bool
		if saUser != nil {
			compensateMask, change = saUser.CalcDivisionCompensate(userback)
			if change {
				saM.SetChangeUser(saUser)
			}
		} else {
			for i := uint32(1); i <= userback.Division; i++ {
				compensateMask |= 1 << (i)
			}
		}

		_, exist := divisionAwardCompensateM[compensateMask]
		if !exist {
			divisionAwardCompensateM[compensateMask] = &crossSeasonDivisionAward{
				award: []*cl.Resource{},
				uids:  []uint64{},
			}
			for _, division := range findSetBits(compensateMask) {
				awardInfo := goxml.GetData().SeasonArenaDivisionRewardInfoM.GetAwardInfo(goxml.SeasonArenaDivisionRewardTypeRecv, division)
				if awardInfo == nil {
					l4g.Errorf("CS2L_SeasonArenaAward rewardType:%d division:%d award info not exist.", goxml.SeasonArenaDivisionRewardTypeRecv, division)
					continue
				}
				divisionAwardCompensateM[compensateMask].award = append(divisionAwardCompensateM[compensateMask].award, awardInfo.GetCloneRes()...)
			}
		}
		divisionAwardCompensateM[compensateMask].uids = append(divisionAwardCompensateM[compensateMask].uids, userback.Uid)

		_, exit := divisionAwardM[userback.Division]
		if !exit {
			res := make([]*cl.Resource, 0, 3) //nolint:mnd
			awardInfo := goxml.GetData().SeasonArenaDivisionRewardInfoM.GetAwardInfo(goxml.SeasonArenaDivisionRewardTypeMail, userback.Division)
			if awardInfo == nil {
				l4g.Errorf("CS2L_SeasonArenaAward rewardType:%d division:%d award info not exist.", goxml.SeasonArenaDivisionRewardTypeMail, userback.Division)
			} else {
				res = append(res, awardInfo.GetCloneRes()...)
				divisionAwardM[userback.Division] = &crossSeasonDivisionAward{
					award: res,
					uids:  []uint64{},
				}
			}
		}
		divisionAwardM[userback.Division].uids = append(divisionAwardM[userback.Division].uids, userback.Uid)

		rankRewardInfo := goxml.GetData().SeasonArenaRankRewardInfoM.GetRankRewardInfo(userback.Rank)
		if rankRewardInfo != nil {
			rankRewardM[rankRewardInfo] = append(rankRewardM[rankRewardInfo], userback)
		}

		if userback.Rank <= guildMedalNeedRank || userback.Division >= guildMedalNeedDivision {
			dataForGuildMedal = append(dataForGuildMedal, &cl.GuildMedalSeasonArenaBak{
				Uid:      userback.Uid,
				Rank:     userback.Rank,
				Division: userback.Division,
				Score:    userback.Score,
			})
		}
	}

	l4g.Infof("CS2L_SeasonArenaAwardCommand: divisionAwardCompensateM:%v, divisionAwardM:%v, rankRewardM:%v", divisionAwardCompensateM, divisionAwardM, rankRewardM)
	if len(divisionAwardCompensateM) > 0 {
		c.sendDivisionAwardCompensate(divisionAwardCompensateM, character.MailIDSADivisionAwardCompensate)
	}

	if len(divisionAwardM) > 0 {
		c.sendDivisionAward(divisionAwardM, character.MailIDSADivisionAward)
	}

	if len(rankRewardM) > 0 {
		c.sendRankAward(rankRewardM, character.MailIDSARankAward)
	}

	saM.SetLastAward(cmsg.Season, cmsg.Round)
	saM.SetDataForGuildMedal(dataForGuildMedal)
	saM.SyncGuild(c.Srv)

	return true
}

func (c *CS2LSeasonArenaAwardCommand) sendDivisionAward(divisionAward map[uint32]*crossSeasonDivisionAward, mailID uint32) {
	for division, awardInfo := range divisionAward {
		if awardInfo == nil {
			continue
		}
		awardInfo.award = c.updateAvatarExpireTime(awardInfo.award)
		character.SeasonArenaDivisionMail(c.Srv, mailID, []string{strconv.Itoa(int(division))}, awardInfo.award, awardInfo.uids)
		l4g.Infof("season arena SeasonArenaDivisionMail: finished, division:%d, awards:%v", division, awardInfo)
	}
}

func (c *CS2LSeasonArenaAwardCommand) sendDivisionAwardCompensate(divisionAward map[uint64]*crossSeasonDivisionAward, mailID uint32) {
	for umask, awardInfo := range divisionAward {
		if awardInfo == nil {
			continue
		}
		awardInfo.award = c.updateAvatarExpireTime(awardInfo.award)
		character.SeasonArenaDivisionCompensateMail(c.Srv, mailID, []string{}, awardInfo.award, awardInfo.uids)
		l4g.Infof("season arena SeasonArenaDivisionCompensateMail: finished, umask:%d, awards:%v", umask, awardInfo)
	}
}

func (c *CS2LSeasonArenaAwardCommand) sendRankAward(rankReward map[*goxml.SeasonArenaRankRewardInfo][]*cr.CrossSeasonArenaServerUserBak, mailID uint32) {
	for rankRewardInfo, userBacks := range rankReward {
		if rankRewardInfo == nil {
			continue
		}
		awards := c.updateAvatarExpireTime(rankRewardInfo.ClRes)
		for _, userBack := range userBacks {
			character.SeasonArenaRankMail(c.Srv, mailID, []string{strconv.Itoa(int(userBack.Rank))}, awards, []uint64{userBack.Uid})
			l4g.Infof("season arena SeasonArenaRankMail: finished, rankRewardInfo:%v, awards:%v", rankRewardInfo, []uint64{userBack.Uid})
		}
	}
}

// 更新限时avatar的过期时间
func (c *CS2LSeasonArenaAwardCommand) updateAvatarExpireTime(awards []*cl.Resource) []*cl.Resource {
	newAwards := make([]*cl.Resource, 0, len(awards))

	for _, award := range awards {
		newA := award.Clone()
		newAwards = append(newAwards, newA)
		if newA.Type != uint32(common.RESOURCE_AVATAR) {
			continue
		}
		info := goxml.GetData().AvatarInfoM.Index(newA.Value)
		if info != nil && info.DurationType == goxml.AvatarTimeTypeEndTime {
			endTm := time.Now().Unix() + int64(info.Duration*60) //nolint:mnd
			newA.Attrs = []*cl.Attr{
				{
					Value: endTm,
				},
			}
		}
	}
	return newAwards
}

type crossSeasonDivisionAward struct {
	award []*cl.Resource
	uids  []uint64
}

func findSetBits(num uint64) []uint32 {
	var setBits []uint32

	for i := 0; i < 64; i++ {
		bitMask := uint64(1) << i
		if (num & bitMask) != 0 {
			setBits = append(setBits, uint32(i))
		}
	}

	return setBits
}
