package cross

import (
	"app/goxml"
	"app/logic/activity"
	aguild "app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitGuildChest(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestGetData), &C2LGuildChestGetDataCommand{}, state)         // 收到公会数据
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestActivate), &C2LGuildChestActivateCommand{}, state)       // 激活公会宝箱
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestRecv), &C2LGuildChestRecvCommand{}, state)               // 领取奖励
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestLike), &C2LGuildChestLikeCommand{}, state)               // 设置点赞
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestCanActivate), &C2LGuildChestCanActivateCommand{}, state) // 查看是否能够激活
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestExpired), &C2LGuildChestExpiredCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestDetail), &C2LGuildChestDetailCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildChestNewChest), &C2LGuildChestNewChestCommand{}, state)
}

type C2LGuildChestGetDataCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestGetDataCommand) Error(smsg *cl.L2C_GuildChestGetData, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestGetData, smsg)
	return false
}

func (c *C2LGuildChestGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestGetData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildChestGetData user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildChestGetData{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildChestGetData user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestGetData failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestGetData failed, get guild user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	c.User.UserGuildChestItem().DelActivate(cmsg.GuildChests)
	guildChestItems, _ := c.User.UserGuildChestItem().Flush2Client()
	smsg.GuildChests = cmsg.GuildChests
	smsg.Items = guildChestItems
	smsg.UserChestWeeklyRecvCount = guildUser.GetChestWeeklyRecvCount()
	smsg.RecvTotalChestLikeToken = guildUser.GetTotalFlowerToken()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestGetData, smsg)

	return true
}

type C2LGuildChestCanActivateCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestCanActivateCommand) Error(smsg *cl.L2C_GuildChestActivate, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestActivate, smsg)
	return false
}

func (c *C2LGuildChestCanActivateCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestCanActivate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestCanActivate Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildChestCanActivate user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildChestActivate{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildChestCanActivate user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		if cmsg.Ret == uint32(cret.RET_GUILD_CHEST_CHEST_IS_EXIST) {
			c.User.UserGuildChestItem().DelItem(cmsg.Id)
		}
		return c.Error(smsg, smsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestCanActivate failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestCanActivate guildUser is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if !guildM.IsCrossConnected() {
		l4g.Errorf("user: %d C2L_GuildChestCanActivate: cross can't connect.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_REQ_TIMEOUT))
	}

	c.User.UserGuildChestItem().DelItem(cmsg.Id)

	crossMsg := &l2c.L2C_GuildChestActivate{
		Id:           cmsg.Id,
		Name:         c.User.Name(),
		Level:        c.User.Level(),
		AvatarId:     c.User.BaseID(),
		ChestId:      cmsg.ChestId,
		CreateFlower: guildUser.GetTotalFlowerToken(),
		TaskId:       cmsg.TaskId,
	}
	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildChestActivate, c.Msg.UID, crossMsg)
	return true
}

type C2LGuildChestActivateCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestActivateCommand) Error(smsg *cl.L2C_GuildChestActivate, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestActivate, smsg)
	return false
}

func (c *C2LGuildChestActivateCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestActivate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LGuildChestActivateCommand Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2LGuildChestActivateCommand user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildChestActivate{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2LGuildChestActivateCommand user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		if cmsg.Ret == uint32(cret.RET_GUILD_CHEST_CHEST_IS_EXIST) {
			c.User.UserGuildChestItem().DelItem(cmsg.Id)
		}
		return c.Error(smsg, cmsg.Ret)
	}

	guildMessage := character.NewMsg(character.Id(c.Srv), character.Type(goxml.ChatChestNewChest),
		character.Head(c.User.NewChatUserHead(c.Srv.GetLogicGuild(c.User.ID()))), character.Params(strconv.Itoa(int(cmsg.GuildChest.ChestId))))
	character.ChatSendMsgToPlatform(c.Srv, c.User.ID(), guildMessage)

	smsg.GuildChest = cmsg.GuildChest
	smsg.ItemId = cmsg.Id
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestActivate, smsg)
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if ok {
		guildUser := guildM.GetGuildUser(c.User.ID())
		if guildUser != nil {
			c.User.LogGuildChestActivate(c.Srv, cmsg.GuildChest.Id, guildUser.GuildID(), cmsg.ChestId)
		}
	}

	return true
}

type C2LGuildChestRecvCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestRecvCommand) Error(smsg *cl.L2C_GuildChestRecv, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestRecv, smsg)
	return false
}

func (c *C2LGuildChestRecvCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestRecv{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestRecv Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildChestRecv user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildChestRecv{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildChestRecv user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestRecv failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestRecv failed, get guild user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if len(cmsg.Awards) > 0 {
		smsg.Ret, _ = c.User.Award(c.Srv, cmsg.Awards, character.MailIDBagFull, uint32(log.RESOURCE_CHANGE_REASON_GUILD_CHEST_RECV), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user:%d C2L_GuildChestRecv award failed, error code:%d", c.Msg.UID, smsg.Ret)
			return c.Error(smsg, smsg.Ret)
		}
		if cmsg.GuildChest != nil {
			chestInfo := goxml.GetData().GuildChestInfoM.Index(cmsg.GuildChest.ChestId)
			if chestInfo != nil && chestInfo.IsCostNum() {
				guildUser.AddWeeklyRecved()
			}
		}
		guildM.SaveGuildUser(guildUser)
	}

	smsg.Id = cmsg.Id
	smsg.GuildChest = cmsg.GuildChest
	smsg.Awards = cmsg.Awards
	smsg.UserChestWeeklyRecvCount = guildUser.GetChestWeeklyRecvCount()
	smsg.ThisChestRecvCount = uint32(len(cmsg.GuildChest.RecvUsers))
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestRecv, smsg)
	if len(cmsg.Awards) > 0 {
		diamond := cmsg.Awards[0].Count
		c.User.LogGuildChestRecvAward(c.Srv, cmsg.GuildChest.Id, guildUser.GuildID(), cmsg.GuildChest.ChestId, diamond)
	}

	return true
}

type C2LGuildChestLikeCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestLikeCommand) Error(smsg *cl.L2C_GuildChestLike, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestLike, smsg)
	return false
}

func (c *C2LGuildChestLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LGuildChestLikeCommand Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2LGuildChestLikeCommand user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildChestLike{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2LGuildChestLikeCommand user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	smsg.Id = cmsg.Id
	smsg.LikeType = cmsg.LikeType
	smsg.GuildChest = cmsg.GuildChest
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestLike, smsg)
	var likeCount uint32
	for _, v := range cmsg.GuildChest.RecvUsers {
		if v == nil {
			continue
		}
		if v.Uid == c.User.ID() {
			likeCount = goxml.GetData().GuildConfigInfoM.GetLikeTypeFlowerCount(v.LikeLevel)
		}
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if ok {
		guildUser := guildM.GetGuildUser(c.User.ID())
		if guildUser != nil {
			c.User.LogGuildChestSetLike(c.Srv, cmsg.GuildChest.Id, guildUser.GuildID(), cmsg.GuildChest.ChestId, likeCount)
		}
	}
	return true
}

type C2LGuildChestExpiredCommand struct {
	base.Command
}

func (c *C2LGuildChestExpiredCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestExpired{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestExpired Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildChestExpired user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildChestExpired user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestExpired failed, guildManager not exist", c.Msg.UID)
		return false
	}

	var unloadUser []uint64
	var unloadChestExpired []*l2c.GuildChestExpired
	for _, v := range cmsg.GuildChestExpireds {
		if v == nil {
			continue
		}
		guildUser := guildM.GetGuildUser(v.Uid)
		if guildUser == nil {
			unloadUser = append(unloadUser, v.Uid)
			unloadChestExpired = append(unloadChestExpired, v)
			continue
		}
		guildUser.AppendGuildChestExpired(v)
		guildM.SaveGuildUser(guildUser)
	}
	if len(unloadUser) > 0 {
		req := &AsyncC2LGuildChestExpiredReq{
			uids:               unloadUser,
			unloadChestExpired: unloadChestExpired,
		}
		c.AsyncGetGuildUsers(req.uids, req, nil)
	}

	return true
}

type AsyncC2LGuildChestExpiredReq struct {
	uids               []uint64
	unloadChestExpired []*l2c.GuildChestExpired
}

func (ar *AsyncC2LGuildChestExpiredReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("AsyncC2LGuildChestExpiredReq: get guildUser error:%d", retCode)
		return false
	}

	dbGuildUsers, ok := data.(map[uint64]*db.GuildUser)
	if !ok {
		l4g.Errorf("AsyncC2LGuildChestExpiredReq: data to guildUser error")
		return false
	}

	guildM, ok := srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("AsyncC2LGuildChestExpiredReq get guildM error.")
		return false
	}

	for _, v := range ar.unloadChestExpired { //nolint:varnamelen
		if v == nil {
			continue
		}
		dbData := dbGuildUsers[v.Uid]
		if dbData == nil {
			l4g.Errorf("AsyncC2LGuildChestExpiredReq get db guild User:%d failed", v.Uid)
			continue
		}
		guildUser := guildM.LoadGuildUser(dbData)
		if guildUser == nil {
			l4g.Errorf("AsyncC2LGuildChestExpiredReq get guild User:%d failed", v.Uid)
			continue
		}
		guildUser.AppendGuildChestExpired(v)
		guildM.SaveGuildUser(guildUser)
	}

	return true
}

type C2LGuildChestDetailCommand struct {
	base.UserCommand
}

func (c *C2LGuildChestDetailCommand) Error(smsg *cl.L2C_GuildChestRecv, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestRecv, smsg)
	return false
}

func (c *C2LGuildChestDetailCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestDetail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestDetail Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildChestDetail user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildChestRecv{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildChestDetail user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildChestDetail failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildChestDetail failed, get guild user failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	smsg.Id = cmsg.Id
	smsg.GuildChest = cmsg.GuildChest
	smsg.UserChestWeeklyRecvCount = guildUser.GetChestWeeklyRecvCount()
	smsg.ThisChestRecvCount = uint32(len(cmsg.GuildChest.RecvUsers))
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildChestRecv, smsg)

	return true
}

type C2LGuildChestNewChestCommand struct {
	base.Command
}

func (c *C2LGuildChestNewChestCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildChestNewChest{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildChestNewChest Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildChestNewChest user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_UserGuildChestItemNotify{
		Ret:           cmsg.Ret,
		NewGuildChest: cmsg.GuildChest,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildChestNewChest user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user:%d C2L_GuildChestNewChest Clear boss damage failed, guildManager not exit", c.Msg.UID)
		return false
	}

	guild := guildM.GetGuildByID(cmsg.Gid)
	if guild == nil {
		l4g.Errorf("user:%d C2L_GuildChestNewChest Clear boss damage failed get guild failed", cmsg.Gid)
		return false
	}

	uids := make([]uint64, 0, len(guild.GetMembers())/2) //nolint:mnd
	for _, uid := range guild.GetMembers() {
		onlineUser := c.Srv.UserM().GetUserWithCache(uid)
		if onlineUser == nil {
			continue
		}
		uids = append(uids, uid)
	}
	if len(uids) > 0 {
		c.Srv.BroadCastCmdByUids(uids, cl.ID_MSG_L2C_UserGuildChestItemNotify, smsg)
	}

	return true
}
