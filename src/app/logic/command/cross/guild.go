package cross

import (
	"app/goxml"
	"app/logic/activity"
	aflower "app/logic/activity/flower"
	aguild "app/logic/activity/guild"
	"app/logic/activity/seasonarena"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/event"
	aevent "app/logic/event"
	"app/protos/in/cr"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitGuild(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildGetLogicGuilds), &C2LGuildGetLogicGuildsCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildCreate), &C2LGuildCreateCommand{}, state) //
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildRank), &C2LGuildRankCommand{}, state)     //
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildInfo), &C2LGuildInfoCommand{}, state)     //
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildList), &C2LGuildListCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildMail), &C2LGuildMailCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildManagerMember), &C2LGuildManagerMemberCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildNotify), &C2LGuildNotifyCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildModifyInfo), &C2LGuildModifyInfoCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildSetName), &C2LGuildSetNameCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildUpdateInfo), &C2LGuildUpdateInfoCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildModifyNotice), &C2LGuildModifyNoticeCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildApplyList), &C2LGuildApplyListCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildApplyRatify), &C2LGuildApplyRatifyCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildUserApply), &C2LGuildUserApplyCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildCombineApply), &C2LGuildCombineApplyCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildCombineCheck), &C2LGuildCombineCheckCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildCombineRatify), &C2LGuildCombineRatifyCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildSendMail), &C2LGuildSendMailCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildQuit), &C2LGuildQuitCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildDisband), &C2LGuildDisbandCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildSearch), &C2LGuildSearchCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildGetMembers), &C2LGuildGetMembersCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildGetDeclaration), &C2LGuildGetDeclarationCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildDonate), &C2LGuildDonateCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildGetDonateAward), &C2LGuildGetDonateAwardCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildLogList), &C2LGuildLogListCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildDonateLogList), &C2LGuildDonateLogListCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildListGetDetail), &C2LGuildListGetDetailCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildGetBadgeList), &C2LGuildGetBadgeListCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildGetDivisionAwardInfo), &C2LGuildGetDivisionAwardInfoCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildSyncKickCount), &GuildSyncKickCountCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildDeleteMails), &GuildDeleteMailsCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildSyncLogicGuild), &GuildSyncLogicGuildCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildGetMedals), &GuildGetMedalsCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildMedalLike), &GuildMedalLikeCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildSyncSeasonArenaData), &GuildSyncSeasonArenaDataCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_GuildSendRecruitMsg), &GuildSendRecruitMsgCommand{}, state)
}

type C2LGuildGetLogicGuildsCommand struct {
	base.Command
}

func (c *C2LGuildGetLogicGuildsCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildGetLogicGuilds{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetLogicGuilds Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildGetLogicGuilds user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildGetLogicGuilds user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetLogicGuilds failed, guildManager not exist", c.Msg.UID)
	}

	guildM.UpdateGuilds(c.Srv, cmsg.LogicGuilds)

	guildM.SetLastSeasonTop3(cmsg.LastSeasonTop_3, cmsg.LastSeasonId)
	return true
}

type C2LGuildCreateCommand struct {
	base.UserCommand
}

func (c *C2LGuildCreateCommand) Error(smsg *cl.L2C_GuildCreate, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCreate, smsg)
	return false
}

func (c *C2LGuildCreateCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildCreate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCreate Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildCreate user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildCreate{
		Ret:         uint32(cret.RET_OK),
		Name:        cmsg.Name,
		Badge:       cmsg.Badge,
		Language:    cmsg.Language,
		JoinType:    cmsg.JoinType,
		LvLimit:     cmsg.LvLimit,
		PowerLimit:  cmsg.PowerLimit,
		Label:       cmsg.Label,
		Declaration: cmsg.Declaration,
		Guild:       cmsg.Guild,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildCreate user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if cmsg.LogicGuild == nil {
		l4g.Errorf("C2L_GuildCreate user:%d logicGuild is nil, msg:%+v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildCreate failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser == nil {
		guildUser = guildM.NewGuildUser(c.User.ID(), c.User.Name())
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*aflower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildCreate failed, flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser.CreateGuild(cmsg.LogicGuild.Id, 1)

	guildM.CreateGuild(cmsg.LogicGuild)

	if ret := c.User.Consume(c.Srv, goxml.GetData().GuildConfigInfoM.CreateCost,
		uint32(log.RESOURCE_CHANGE_REASON_GUILD_CREATE), 0); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildCreate consume error", c.Msg.UID)
		//return c.Error(smsg, ret)
	}

	smsg.Guild = cmsg.Guild

	// 更新密林公会数据
	flowerM.UpdateGuildID(c.Srv, c.Msg.UID, 0, cmsg.Guild.Id)
	c.User.FireCommonEvent(c.Srv.EventM(), aevent.AeAddGuild, 1)
	// 加入公会群聊
	character.ChatJoinGuildGroupToPlatform(c.Srv, c.Msg.UID, cmsg.Guild.Id, character.ChatGroupOpJoin)

	c.User.UserGuild().Update(cmsg.Guild.Id, cmsg.Guild.Name) // 同步信息到userGuild

	c.User.GuildTalent().UserGuildChange(c.User, c.Srv) //需要放在上面的update之后
	character.ChatSyncUserToPlatform(c.Srv, c.User)
	c.User.SendSelfToClient()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCreate, smsg)

	c.User.LogGuildManage(c.Srv, &log.GuildManage{
		Gid:      cmsg.Guild.Id,
		Name:     cmsg.Guild.Name,
		Type:     log.GUILD_LOG_MANAGE_GLM_CREATE,
		CreateTm: cmsg.Guild.CreateTm,
	})
	c.User.LogGuildModifyTextInfo(c.Srv, cmsg.LogInfo) // 记录建立公会的时候的基本信息
	return true
}

type C2LGuildRankCommand struct {
	base.UserCommand
}

func (c *C2LGuildRankCommand) Error(smsg *cl.L2C_GuildRank, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildRank, smsg)
	return false
}

func (c *C2LGuildRankCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildRank Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildRank user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildRank{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildRank user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if cmsg.First {
		if !c.handleRankFirst(cmsg) {
			l4g.Errorf("C2L_GuildRank user:%d handle rank first error.", c.Msg.UID)
			return false
		}
		return true
	}

	smsg.List = cmsg.List
	smsg.RankId = cmsg.RankId
	smsg.SelfRank = cmsg.SelfRank
	smsg.SelfValue = cmsg.SelfValue
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildRank, smsg)
	return true
}

func (c *C2LGuildRankCommand) handleRankFirst(cmsg *l2c.C2L_GuildRank) bool {
	as, exist := c.Srv.GetAsyncMessage(cmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", cmsg.ClientMsgId)
		return false
	}
	amsg := as.(*base.AsyncMessage)

	data := &cl.FirstRankValue{
		RankId: cmsg.RankId,
	}

	if len(cmsg.List) > 0 {
		data.RankValue = cmsg.List[0]
	}
	defer c.Srv.DeleteAsyncMessage(cmsg.ClientMsgId)
	l4g.Infof("C2L_GuildRank do callback from cmd:%d", amsg.CallCmd)
	ret := amsg.Work(cmsg.Ret, data)
	return ret
}

type C2LGuildListCommand struct {
	base.UserCommand
}

func (c *C2LGuildListCommand) Error(smsg *cl.L2C_GuildList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildList, smsg)
	return false
}

func (c *C2LGuildListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildList user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildList{
		Ret:      uint32(cret.RET_OK),
		Page:     cmsg.Page,
		Screen:   cmsg.Screen,
		Language: cmsg.Language,
		JoinType: cmsg.JoinType,
		Label:    cmsg.Label,
		Total:    cmsg.Total,
		Combine:  cmsg.Combine,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildList user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Guilds = cmsg.Guilds

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildList failed, guildManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser != nil {
		smsg.ApplyIds = guildUser.ApplyIds()
	}

	// 合并请求状态
	if cmsg.Combine {
		smsg.CombineStatus = cmsg.CombineStatus
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildList, smsg)
	return true
}

type C2LGuildInfoCommand struct {
	base.UserCommand
}

func (c *C2LGuildInfoCommand) Error(smsg *cl.L2C_GuildMainInfo, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMainInfo, smsg)
	return false
}

func (c *C2LGuildInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildInfo user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildMainInfo{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildInfo user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if len(cmsg.Members) == 0 {
		l4g.Errorf("user:%d C2L_GuildInfo: cross return guild member count is 0, cmsg:%+v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR)) // todo 错误码修改
	}

	if cmsg.Guild == nil {
		l4g.Errorf("user:%d C2L_GuildInfo: cross return guild is nil. cmsg:%+v", c.Msg.UID, cmsg)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR)) // todo 错误码修改
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("C2L_GuildInfo user:%d get guildM error.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("C2L_GuildInfo user:%d guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser.UpdateCrossInfo(cmsg.Myself)

	smsg.Guild = cmsg.Guild
	guildM.UpdateGuild(cmsg.LogicGuild)
	smsg.User = guildUser.Flush()
	smsg.User.MedalLiked = c.User.FlushGuildMedalLike()
	smsg.DungeonSeason = cmsg.DungeonSeason
	smsg.HaveTransfer = cmsg.HaveTransfer
	smsg.HaveCombine = cmsg.HaveCombine
	if guildUser.GetApprover() != "" {
		guildUser.ClearApprover()
		guildM.SaveGuildUser(guildUser)
	}
	var count uint32
	if len(guildUser.GetGuildChestFinishRecv()) > 0 {
		smsg.GuildChestFinishRecv = guildUser.GetGuildChestFinishRecv()
		for _, v := range smsg.GuildChestFinishRecv {
			count += v.RecvLike
		}
		award := goxml.GetData().GuildConfigInfoM.GetRecvLikeRes(count)
		guildUser.ClearGuildChestFinishRecv()
		guildUser.AddTotalRecvFlowerToken(count)
		guildM.SaveGuildUser(guildUser)
		c.User.Award(c.Srv, []*cl.Resource{award}, character.MailIDBagFull, uint32(log.RESOURCE_CHANGE_REASON_GUILD_CHEST_RECV_FLOWER), 0)
	}

	req := &AsyncC2LGuildMainInfoReq{
		smsg: smsg,
	}

	c.GetGuildMemberFromAll(cmsg.Guild.Id, 0, cmsg.Members, req)
	return true
}

type AsyncC2LGuildMainInfoReq struct {
	smsg *cl.L2C_GuildMainInfo
}

func (ar *AsyncC2LGuildMainInfoReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, data interface{}) bool {
	smsg := ar.smsg
	smsg.DungeonRound = goxml.GetGuildDungeonCurrentRound(goxml.GetData(), time.Now().Unix())
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildInfo: get snapshot error:%d", args.UID, retCode)
		//smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		//args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildMainInfo, smsg)
		//return false
	}

	members := data.([]*cl.GuildMemberInfo)
	if len(members) == 0 {
		l4g.Errorf("user: %d C2L_GuildInfo: get members failed, no data", args.UID)
		//smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		//args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildMainInfo, smsg)
		//return false
	} else {
		smsg.Members = append(smsg.Members, members...)
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildMainInfo, smsg)
	return true
}

type C2LGuildMailCommand struct {
	base.Command
}

func (c *C2LGuildMailCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildMail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMail Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildMail recv from cross:%+v", cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildMail recv cross info error, ret:%d", cmsg.Ret)
		return false
	}

	character.GuildMail(c.Srv, cmsg.MailId, cmsg.Params, cmsg.Awards, cmsg.Users)
	return true
}

type C2LGuildManagerMemberCommand struct {
	base.UserCommand
}

func (c *C2LGuildManagerMemberCommand) Error(smsg *cl.L2C_GuildManagerMember, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildManagerMember, smsg)
	return false
}

func (c *C2LGuildManagerMemberCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildManagerMember{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildManagerMember Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildManagerMember user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildManagerMember{
		Ret:      uint32(cret.RET_OK),
		Id:       cmsg.Id,
		Type:     cmsg.Type,
		Leader:   cmsg.Leader,
		Deputy:   cmsg.Deputy,
		Regiment: cmsg.Regiment,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildManagerMember user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user:%d C2L_GuildManagerMember get guildM error.", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildManagerMember: guild not exist.", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//已在notify处理
	/*
		if cmsg.Type == character.GuildManagerLeader {
			guildUser := guildM.GetGuildUser(c.User.ID())
			if guildUser == nil {
				l4g.Errorf("user:%d C2L_GuildManagerMember get guildUser error. ret:%d", c.Msg.UID, cmsg.Ret)
				return c.Error(smsg, uint32(cret.RET_ERROR))
			}
			guildUser.SetGrade(character.GuildGradeMember)
			guildM.SaveGuildUser(guildUser)
		}
	*/

	c.User.LogGuildManagerMember(c.Srv, cmsg.Id, cmsg.Type, cmsg.MemberOldGrade, cmsg.MemberCurrentGrade, cmsg.Gid, cmsg.MemberCurrentGid)

	if cmsg.Type == character.GuildManagerKick {
		c.User.LogGuildManage(c.Srv, &log.GuildManage{
			Gid:      guild.ID(),
			Name:     guild.Name(),
			Type:     log.GUILD_LOG_MANAGE_GLM_KICK,
			MemberId: []uint64{cmsg.Id},
		})
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildManagerMember, smsg)
	return true
}

type C2LGuildNotifyCommand struct {
	base.Command
}

func (c *C2LGuildNotifyCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildNotify{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildNotify Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildNotify recv from cross:%+v", cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildNotify recv cross info error, ret:%d", cmsg.Ret)
		return false
	}

	if len(cmsg.Uids) == 0 {
		l4g.Errorf("C2L_GuildNotify recv cross info error. len uid is 0.")
		return false
	}

	if cmsg.Guild == nil && (cmsg.Approve || cmsg.JoinIn || cmsg.QuickJoin || cmsg.CombineJoin) {
		l4g.Errorf("C2L_GuildNotify recv cross info error. guild is nil.")
		return false
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("C2L_GuildNotify get guildM error.")
		return false
	}

	if cmsg.ApplyClear {
		guild := guildM.GetGuildByID(cmsg.Id)
		if guild != nil {
			guild.SetHaveApplicant(false)
			guildM.SetChange(guild)
		}
		return true
	}

	req := &AsyncC2LGuildNotifyReq{
		cmsg: cmsg,
	}

	c.AsyncGetGuildUsers(cmsg.Uids, req, nil)

	return true
}

type AsyncC2LGuildNotifyReq struct {
	cmsg *l2c.C2L_GuildNotify
}

//nolint:funlen
func (ar *AsyncC2LGuildNotifyReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, data interface{}) bool {
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("C2L_GuildNotify: get guildUser error:%d", retCode)
		return false
	}

	cmsg := ar.cmsg

	dbGuildUsers, ok := data.(map[uint64]*db.GuildUser)
	if !ok {
		l4g.Errorf("C2L_GuildNotify: data to guildUser error")
		return false
	}

	if len(cmsg.Uids) != len(dbGuildUsers) {
		l4g.Errorf("C2L_GuildNotify: data to guildUser error")
		return false
	}

	guildM, ok := srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("C2L_GuildNotify get guildM error.")
		return false
	}

	flowerM, ok := srv.GetActivity(activity.Flower).(*aflower.Manager)
	if !ok {
		l4g.Errorf("C2L_GuildNotify: failed, flowerManager not exist.")
		return false
	}

	guild := guildM.GetGuildByID(ar.cmsg.Id)
	if !ar.cmsg.Approve && !ar.cmsg.JoinIn && !ar.cmsg.Refuse && !ar.cmsg.QuickJoin && !ar.cmsg.CombineJoin {
		if guild == nil {
			l4g.Errorf("C2L_GuildNotify: failed, guild not exist. gid:%d cmsg:%+v", cmsg.Id, cmsg)
			return false
		}
	}

	now := time.Now().Unix()

	for _, dbData := range dbGuildUsers {
		if dbData == nil {
			continue
		}
		guildUser := guildM.LoadGuildUser(dbData)
		if guildUser == nil {
			continue
		}
		user := srv.UserM().GetUserWithCache(guildUser.ID())

		if cmsg.BeLeader {
			oldGrade := guildUser.Grade()
			guildUser.SetGrade(character.GuildGradeLeader)
			guildM.SaveGuildUser(guildUser)
			logInfo := &log.GuildPosition{
				Gid:               guild.ID(),
				Name:              guild.Name(),
				CreateTm:          cmsg.CreateTm,
				ManagerId:         cmsg.ManagerId,
				ManagerPosition:   cmsg.Grade,
				MemberOldPosition: oldGrade,
				MemberNewPosition: guildUser.Grade(),
			}
			if user != nil {
				notify := &cl.L2C_GuildNotify{
					Ret:      uint32(ret.RET_OK),
					Id:       cmsg.Id,
					BeLeader: true,
				}
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, notify)
				user.LogGuildPositionChange(srv, logInfo)
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildPositionChange(srv, logInfo)
			}
		}
		if cmsg.BeDeputy {
			oldGrade := guildUser.Grade()
			guildUser.SetGrade(character.GuildGradeDeputy)
			guildM.SaveGuildUser(guildUser)
			logInfo := &log.GuildPosition{
				Gid:               guild.ID(),
				Name:              guild.Name(),
				CreateTm:          cmsg.CreateTm,
				ManagerId:         cmsg.ManagerId,
				ManagerPosition:   cmsg.Grade,
				MemberOldPosition: oldGrade,
				MemberNewPosition: guildUser.Grade(),
			}
			if user != nil {
				notify := &cl.L2C_GuildNotify{
					Ret:      uint32(ret.RET_OK),
					Id:       cmsg.Id,
					BeDeputy: true,
				}
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, notify)
				user.LogGuildPositionChange(srv, logInfo)
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildPositionChange(srv, logInfo)
			}
		}

		if cmsg.BeRegiment {
			oldGrade := guildUser.Grade()
			guildUser.SetGrade(character.GuildGradeRegiment)
			guildM.SaveGuildUser(guildUser)
			logInfo := &log.GuildPosition{
				Gid:               guild.ID(),
				Name:              guild.Name(),
				CreateTm:          cmsg.CreateTm,
				ManagerId:         cmsg.ManagerId,
				ManagerPosition:   cmsg.Grade,
				MemberOldPosition: oldGrade,
				MemberNewPosition: guildUser.Grade(),
			}
			if user != nil {
				notify := &cl.L2C_GuildNotify{
					Ret:        uint32(ret.RET_OK),
					Id:         cmsg.Id,
					BeRegiment: true,
				}
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, notify)
				user.LogGuildPositionChange(srv, logInfo)
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildPositionChange(srv, logInfo)
			}
		}

		if cmsg.Recall {
			oldGrade := guildUser.Grade()
			guildUser.SetGrade(character.GuildGradeMember)
			guildM.SaveGuildUser(guildUser)
			logInfo := &log.GuildPosition{
				Gid:               guild.ID(),
				Name:              guild.Name(),
				CreateTm:          cmsg.CreateTm,
				ManagerId:         cmsg.ManagerId,
				ManagerPosition:   cmsg.Grade,
				MemberOldPosition: oldGrade,
				MemberNewPosition: guildUser.Grade(),
			}
			if user != nil {
				notify := &cl.L2C_GuildNotify{
					Ret:    uint32(ret.RET_OK),
					Id:     cmsg.Id,
					Recall: true,
				}
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, notify)
				user.LogGuildPositionChange(srv, logInfo)
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildPositionChange(srv, logInfo)
			}
		}
		if cmsg.Kicked || cmsg.CombineKicked {
			// 退出公会副本聊天群组,需要在QuitGuild之前
			character.ChatJoinGuildDungeonGroupToPlatform(srv, guildUser.ID(), guildUser.GetDungeonChatRoomId(), character.ChatGroupOpQuit)
			character.ChatQuitGST(srv, guildUser.ID())
			guildM.KickMember(guild, guildUser)

			guildUser.QuitGuild(true)
			guildM.SaveGuildUser(guildUser)

			logInfo := &log.GuildUserJoinOrQuit{
				Gid:      guild.ID(),
				Name:     guild.Name(),
				CreateTm: cmsg.CreateTm,
				OldGid:   guild.ID(),
			}

			if cmsg.Kicked {
				logInfo.Type = log.GUILD_LOG_USER_GLU_BE_KICK
			} else if cmsg.CombineKicked {
				logInfo.Type = log.GUILD_LOG_USER_GLU_COMBINE_KICK
			}

			if user != nil {
				user.UserGuild().Update(0, "")
				//公会改变，天赋全局属性发生变化
				user.GuildTalent().UserGuildChange(user, srv)
				character.ChatSyncUserToPlatform(srv, user)
				user.SendSelfToClient()
				if cmsg.Kicked {
					user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, &cl.L2C_GuildNotify{
						Ret:    uint32(ret.RET_OK),
						Kicked: true,
					})
				} else if cmsg.CombineKicked {
					user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, &cl.L2C_GuildNotify{
						Ret:           uint32(ret.RET_OK),
						CombineKicked: true,
					})
				}

				user.LogGuildUserJoinOrQuit(srv, logInfo)
				user.CalcQuitGuildResource(srv)

				user.DelMailsForQuitGuild()
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildUserJoinOrQuit(srv, logInfo)
				guildUser.SetNeedClearGstItemTrue()
				guildM.SaveGuildUser(guildUser)
			}

			// 更新密林公会数据
			flowerM.UpdateGuildID(srv, guildUser.ID(), cmsg.Id, 0)
			// 退出公会聊天群组
			character.ChatJoinGuildGroupToPlatform(srv, guildUser.ID(), guild.ID(), character.ChatGroupOpQuit)
		}

		if cmsg.Approve {
			if guild == nil {
				guild = guildM.AddGuild(cmsg.Guild)
				guildUser.JoinGuild(guild.ID(), uint32(character.GuildGradeMember))
			} else {
				guildM.JoinIn([]*aguild.User{guildUser}, guild)
			}
			user.FireCommonEvent(srv.EventM(), aevent.AeAddGuild, 1)

			guildUser.SetApprover(cmsg.Approver)
			// 将guildUser中的cd数据发给跨服
			srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildUserSyncLeaveCd, srv.ServerID(), &l2c.L2C_GuildUserSyncLeaveCd{
				UserId:        guildUser.ID(),
				LastLeaveTm:   guildUser.LeaveTm(),
				GuildLeaveCnt: guildUser.FlushGuildLevelCnt(),
			})

			logInfo := &log.GuildUserJoinOrQuit{
				Gid:      guild.ID(),
				Name:     guild.Name(),
				CreateTm: cmsg.CreateTm,
				OldGid:   guild.ID(),
				Type:     log.GUILD_LOG_USER_GLU_BE_APPROVED,
			}
			if user != nil {
				user.UserGuild().Update(guild.ID(), guild.Name())
				// 检查公会天赋是否激活，如果激活了，重新计算战力
				user.GuildTalent().UserGuildChange(user, srv)
				character.ChatSyncUserToPlatform(srv, user)
				user.SendSelfToClient()
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, &cl.L2C_GuildNotify{
					Ret:     uint32(ret.RET_OK),
					Approve: true,
				})
				user.LogGuildUserJoinOrQuit(srv, logInfo)
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildUserJoinOrQuit(srv, logInfo)
			}

			// 更新密林公会数据
			flowerM.UpdateGuildID(srv, guildUser.ID(), 0, cmsg.Id)
			// 加入公会群聊
			character.ChatJoinGuildGroupToPlatform(srv, guildUser.ID(), cmsg.Guild.Id, character.ChatGroupOpJoin)
			message := genGuildChatMessages(srv, guildUser)
			character.ChatSendMsgToPlatform(srv, guildUser.ID(), message)
		}
		if cmsg.Refuse {
			guildUser.DelApplyID(cmsg.Id)
		}

		if cmsg.NewApply {
			guild.SetHaveApplicant(true)
			guildM.SetChange(guild)
			if user != nil {
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, &cl.L2C_GuildNotify{
					Ret:      uint32(ret.RET_OK),
					NewApply: true,
				})
			}
		}

		if cmsg.CombineApply {
			guild.SetLastCombineApplyTime(cmsg.CombineApplyTime)
			guildM.SetChange(guild)

			if user != nil {
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, &cl.L2C_GuildNotify{
					Ret:          uint32(ret.RET_OK),
					CombineApply: true,
				})
			}
		}

		if cmsg.CancelCombineApply || cmsg.RejectCombineApply {
			guild.SetLastCombineApplyTime(cmsg.CombineApplyTime)
			guildM.SetChange(guild)
		}

		if cmsg.JoinIn || cmsg.QuickJoin || cmsg.CombineJoin {
			if guild == nil {
				guild = guildM.AddGuild(cmsg.Guild)
				guildUser.JoinGuild(guild.ID(), uint32(character.GuildGradeMember))
			} else {
				guildM.JoinIn([]*aguild.User{guildUser}, guild)
			}
			user.FireCommonEvent(srv.EventM(), aevent.AeAddGuild, 1)
			// 将guildUser中的cd数据发给跨服
			if cmsg.JoinIn || cmsg.CombineJoin {
				srv.SendCmdToCross(l2c.ID_MSG_L2C_GuildUserSyncLeaveCd, srv.ServerID(), &l2c.L2C_GuildUserSyncLeaveCd{
					UserId:        guildUser.ID(),
					LastLeaveTm:   guildUser.LeaveTm(),
					GuildLeaveCnt: guildUser.FlushGuildLevelCnt(),
				})
			}

			logInfo := &log.GuildUserJoinOrQuit{
				Gid:      guild.ID(),
				Name:     guild.Name(),
				CreateTm: cmsg.CreateTm,
				OldGid:   guild.ID(),
				Type:     log.GUILD_LOG_USER_GLU_JOIN,
			}

			if cmsg.QuickJoin {
				logInfo.Type = log.GUILD_LOG_USER_GLU_QUICK_JOIN
			} else if cmsg.CombineJoin {
				logInfo.Type = log.GUILD_LOG_USER_GLU_COMBINE_JOIN
			}

			if user != nil {
				user.UserGuild().Update(guild.ID(), guild.Name())
				// 检查公会天赋是否激活，如果激活了，重新计算战力
				user.GuildTalent().UserGuildChange(user, srv)
				character.ChatSyncUserToPlatform(srv, user)
				user.SendSelfToClient()
				user.LogGuildUserJoinOrQuit(srv, logInfo)
				if cmsg.CombineJoin {
					user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, &cl.L2C_GuildNotify{
						Ret:         uint32(ret.RET_OK),
						CombineJoin: true,
					})
				}
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildUserJoinOrQuit(srv, logInfo)
			}

			guildM.SetChange(guild)

			// 加入公会群聊
			character.ChatJoinGuildGroupToPlatform(srv, guildUser.ID(), guild.ID(), character.ChatGroupOpJoin)

			message := genGuildChatMessages(srv, guildUser)
			character.ChatSendMsgToPlatform(srv, guildUser.ID(), message)
			if guildUser.GetApprover() != "" {
				guildUser.ClearApprover()
				guildM.SaveGuildUser(guildUser)
			}
		}

		if cmsg.Quit {
			// 退出公会副本聊天群组,需要在QuitGuild之前
			character.ChatJoinGuildDungeonGroupToPlatform(srv, guildUser.ID(), guildUser.GetDungeonChatRoomId(), character.ChatGroupOpQuit)
			character.ChatQuitGST(srv, guildUser.ID())
			guildUser.QuitGuild(false)

			guildM.Quit(guildUser.ID(), guild)
			guildM.SetChange(guild)
			guildM.SaveGuildUser(guildUser)

			// 退出公会聊天群组
			character.ChatJoinGuildGroupToPlatform(srv, guildUser.ID(), guild.ID(), character.ChatGroupOpQuit)

			logInfo := &log.GuildUserJoinOrQuit{
				Gid:      guild.ID(),
				Name:     guild.Name(),
				CreateTm: cmsg.CreateTm,
				OldGid:   guild.ID(),
				Type:     log.GUILD_LOG_USER_GLU_QUIT,
			}

			if user != nil {
				user.UserGuild().Update(0, "")
				//检查公会天赋是否激活，如果激活了，重新计算战力
				user.GuildTalent().UserGuildChange(user, srv) //需要放在上面的update之后
				character.ChatSyncUserToPlatform(srv, user)
				user.SendSelfToClient()
				user.LogGuildUserJoinOrQuit(srv, logInfo)
				user.CalcQuitGuildResource(srv)
				user.DelMailsForQuitGuild()
			} else {
				character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildUserJoinOrQuit(srv, logInfo)
				guildUser.SetNeedClearGstItemTrue()
				guildM.SaveGuildUser(guildUser)
			}

			srv.SendCmdToCross(l2c.ID_MSG_L2C_RankDelete, srv.ServerID(), &l2c.L2C_RankDelete{
				RankId:    goxml.GuildDungeonUserDamageRankId,
				ResetTime: uint64(goxml.GetGuildDungeonWeeklyResetTime(now) - util.WeekSecs),
				DelKey:    guildUser.ID(),
			})
		}

		if cmsg.Disband || cmsg.CombineDisband {
			// 退出公会副本聊天群组,需要在QuitGuild之前
			character.ChatJoinGuildDungeonGroupToPlatform(srv, guildUser.ID(), guildUser.GetDungeonChatRoomId(), character.ChatGroupOpQuit)
			character.ChatQuitGST(srv, guildUser.ID())
			guildM.Disband(guildUser.ID(), guild)

			guildUser.QuitGuild(false)
			guildM.SaveGuildUser(guildUser)

			srv.SendCmdToCross(l2c.ID_MSG_L2C_RankDelete, srv.ServerID(), &l2c.L2C_RankDelete{
				RankId:    goxml.GuildDungeonUserDamageRankId,
				ResetTime: uint64(goxml.GetGuildDungeonWeeklyResetTime(now) - util.WeekSecs),
				DelKey:    guildUser.ID(),
			})

			// 更新密林公会数据
			//flowerM.UpdateGuildID(c.Srv, c.Msg.UID, cmsg.OldGid, 0)

			// 退出公会聊天群组
			character.ChatJoinGuildGroupToPlatform(srv, guildUser.ID(), guild.ID(), character.ChatGroupOpQuit)

			// 为合并解散记录埋点
			logInfo := &log.GuildManage{
				Gid:      guild.ID(),
				Name:     guild.Name(),
				CreateTm: cmsg.CreateTm,
				Type:     log.GUILD_LOG_MANAGE_GLM_DISBAND,
				MemberId: []uint64{1},
			}

			if user != nil {
				user.UserGuild().Update(0, "")
				//检查公会天赋是否激活，如果激活了，重新计算战力
				user.GuildTalent().UserGuildChange(user, srv) //需要放在上面的update之后
				character.ChatSyncUserToPlatform(srv, user)
				user.SendSelfToClient()
				user.CalcQuitGuildResource(srv)
				user.DelMailsForQuitGuild()
				if cmsg.CombineDisband {
					user.SendCmdToGateway(cl.ID_MSG_L2C_GuildNotify, &cl.L2C_GuildNotify{
						Ret:           uint32(ret.RET_OK),
						CombineKicked: true,
					})
					user.LogGuildManage(srv, logInfo)
				}
			} else {
				if cmsg.CombineDisband {
					character.NewMaybeOfflineUserLog(srv, guildUser.ID()).LogGuildManage(srv, logInfo)
				}
				guildUser.SetNeedClearGstItemTrue()
				guildM.SaveGuildUser(guildUser)
			}
		}

		guildM.SaveGuildUser(guildUser)
	}

	return true
}

type C2LGuildModifyInfoCommand struct {
	base.UserCommand
}

func (c *C2LGuildModifyInfoCommand) Error(smsg *cl.L2C_GuildModifyInfo, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyInfo, smsg)
	return false
}

func (c *C2LGuildModifyInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildModifyInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildModifyInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildModifyInfo user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildModifyInfo{
		Ret:         uint32(cret.RET_OK),
		Badge:       cmsg.Badge,
		JoinType:    cmsg.JoinType,
		Declaration: cmsg.Declaration,
		Language:    cmsg.Language,
		LvLimit:     cmsg.LvLimit,
		PowerLimit:  cmsg.PowerLimit,
		Label:       cmsg.Label,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildModifyInfo user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	// todo 这里的日志需要修改，目前记录的信息意义不大
	c.User.LogGuildModifyInfo(c.Srv, cmsg)
	c.User.LogGuildModifyTextInfo(c.Srv, cmsg.LogInfo)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyInfo, smsg)
	return true
}

type C2LGuildSetNameCommand struct {
	base.UserCommand
}

func (c *C2LGuildSetNameCommand) Error(smsg *cl.L2C_GuildSetName, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSetName, smsg)
	return false
}

func (c *C2LGuildSetNameCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildSetName{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSetName Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildSetName user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildSetName{
		Ret:          uint32(cret.RET_OK),
		Name:         cmsg.Name,
		UpdateNameTm: cmsg.UpdateNameTm,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildSetName user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildSetName. failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildSetName: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	if ret := c.User.Consume(c.Srv, goxml.GetData().GuildConfigInfoM.NameChangeCost,
		uint32(log.RESOURCE_CHANGE_REASON_GUILD_SET_NAME), 0); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildSetName: consume error.", c.Msg.UID)
	}

	guild.SetName(cmsg.Name)

	guildM.SetChange(guild)

	for _, uid := range guild.GetMembers() {
		user := c.Srv.UserM().GetUserWithCache(uid)
		if user != nil {
			user.UserGuild().SetGuildName(cmsg.Name)
			user.SendSelfToClient()
		}
	}

	c.User.LogGuildSetName(c.Srv, cmsg.Name, cmsg.Name)

	c.User.LogGuildModifyTextInfo(c.Srv, cmsg.LogInfo)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSetName, smsg)
	return true
}

type C2LGuildUpdateInfoCommand struct {
	base.Command
}

func (c *C2LGuildUpdateInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildUpdateInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildUpdateInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildUpdateInfo user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildUpdateInfo user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildUpdateInfo. failed, guildManager not exist.", c.Msg.UID)
		return false
	}

	guild := guildM.GetGuildByID(cmsg.Gid)
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildUpdateInfo: guild not exist. gid:%d", c.Msg.UID, cmsg.Gid)
		return false
	}

	if cmsg.Name != "" {
		guild.SetName(cmsg.Name)
		for _, uid := range guild.GetMembers() {
			user := c.Srv.UserM().GetUserWithCache(uid)
			if user != nil {
				user.UserGuild().SetGuildName(cmsg.Name)
				user.SendSelfToClient()
			}
		}
	}

	// todo 经验值需要实时推吗
	if guild.GetLevel() < cmsg.Level {
		guild.SetLevel(cmsg.Level)
		for _, uid := range guild.GetMembers() {
			user := c.Srv.UserM().GetUserWithCache(uid)
			if user != nil {
				user.UserGuild().SetGuildName(cmsg.Name)
				user.SendSelfToClient()
				user.SendCmdToGateway(cl.ID_MSG_L2C_GuildUpdateInfo,
					&cl.L2C_GuildUpdateInfo{
						Ret:   uint32(ret.RET_OK),
						Level: cmsg.Level,
						Exp:   cmsg.Exp,
					})
			}
		}
	}

	guildM.SetChange(guild)

	return true
}

type C2LGuildModifyNoticeCommand struct {
	base.UserCommand
}

func (c *C2LGuildModifyNoticeCommand) Error(smsg *cl.L2C_GuildModifyNotice, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyNotice, smsg)
	return false
}

func (c *C2LGuildModifyNoticeCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildModifyNotice{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildModifyNotice Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildModifyNotice user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildModifyNotice{
		Ret:      uint32(cret.RET_OK),
		Notice:   cmsg.Notice,
		NoticeId: cmsg.NoticeId,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildModifyInfo user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	c.User.LogGuildModifyNotice(c.Srv, cmsg.Notice, cmsg.Notice)
	c.User.LogGuildModifyTextInfo(c.Srv, cmsg.LogInfo)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildModifyNotice, smsg)
	return true
}

type C2LGuildApplyListCommand struct {
	base.UserCommand
}

func (c *C2LGuildApplyListCommand) Error(smsg *cl.L2C_GuildApplyList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyList, smsg)
	return false
}

func (c *C2LGuildApplyListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildApplyList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildApplyList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildApplyList user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildApplyList{
		Ret:       uint32(cret.RET_OK),
		ApplyType: cmsg.ApplyType,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildApplyList user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	// 公会合并请求则直接返回
	if cmsg.ApplyType == uint32(common.GUILD_APPLY_TYPE_GAT_GUILD_COMBINE) {
		// 公会申请合并的列表
		smsg.CombineApplyList = cmsg.ApplyList
		// 公会被申请合并的列表
		smsg.CombineAppliedList = cmsg.AppliedList
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyList, smsg)
		return true
	}

	if len(cmsg.List) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyList, smsg)
		return true
	}

	req := &AsyncC2LGuildApplyListReq{
		smsg:      smsg,
		applies:   cmsg.List,
		applyType: cmsg.ApplyType,
	}

	reqUsers := make([]*cl.ServerUser, 0, len(cmsg.List))
	for _, applyInfo := range cmsg.List {
		reqUsers = append(reqUsers, &cl.ServerUser{Sid: applyInfo.Sid, Uid: applyInfo.Uid})
	}

	//查看跨服user
	c.GetUserSnapshotsFromAll(reqUsers, req, 0)

	return true
}

type AsyncC2LGuildApplyListReq struct {
	smsg      *cl.L2C_GuildApplyList
	applies   []*cr.GuildApplyInfo
	applyType uint32
}

func (ar *AsyncC2LGuildApplyListReq) Resp(srv command.Servicer, args *character.Args,
	retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LGuildApplyList: get snapshot error. retCode:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyList, smsg)
		return false
	}
	users := dbData.([]*cl.UserSnapshot)
	snapm := make(map[uint64]*cl.UserSnapshot, len(ar.applies))
	for _, v := range users {
		snapm[v.Id] = v
	}
	for _, applyInfo := range ar.applies {
		if snapm[applyInfo.Uid] == nil {
			l4g.Errorf("user: %d C2L_GuildApplyList: get snapshot nil. uid:%+v",
				args.UID, applyInfo)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyList, smsg)
			return false
		}
		smsg.Users = append(smsg.Users, snapm[applyInfo.Uid])
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyList, smsg)
	return true
}

type C2LGuildCombineCheckCommand struct {
	base.UserCommand
}

func (c *C2LGuildCombineCheckCommand) Error(smsg *cl.L2C_GuildCombineCheck, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineCheck, smsg)
	return false
}

func (c *C2LGuildCombineCheckCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildCombineCheck{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCombineCheck Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildCombineCheck user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildCombineCheck{
		Ret:       uint32(cret.RET_OK),
		SourceGid: cmsg.SourceGid,
		TargetGid: cmsg.TargetGid,
		ApplyType: cmsg.ApplyType,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildCombineCheck user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if len(cmsg.JoinUsers) == 0 && len(cmsg.DismissUsers) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineCheck, smsg)
		return true
	}

	req := &AsyncC2LGuildCombineCheckReq{
		smsg:           smsg,
		joinMembers:    cmsg.JoinUsers,
		dismissMembers: cmsg.DismissUsers,
	}

	reqUsers := make([]*cl.ServerUser, 0, len(cmsg.JoinUsers)+len(cmsg.DismissUsers))
	for _, joinUser := range cmsg.JoinUsers {
		reqUsers = append(reqUsers, &cl.ServerUser{Sid: joinUser.Sid, Uid: joinUser.Id})
	}
	for _, dismissUser := range cmsg.DismissUsers {
		reqUsers = append(reqUsers, &cl.ServerUser{Sid: dismissUser.Sid, Uid: dismissUser.Id})
	}

	//查看跨服user
	c.GetUserSnapshotsFromAll(reqUsers, req, 0)

	return true
}

type AsyncC2LGuildCombineCheckReq struct {
	smsg           *cl.L2C_GuildCombineCheck
	joinMembers    []*cr.GuildMember
	dismissMembers []*cr.GuildMember
}

func (ar *AsyncC2LGuildCombineCheckReq) Resp(srv command.Servicer, args *character.Args,
	retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LGuildCombineCheck: get snapshot error. retCode:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineCheck, smsg)
		return false
	}
	users := dbData.([]*cl.UserSnapshot)
	snapm := make(map[uint64]*cl.UserSnapshot, len(ar.joinMembers)+len(ar.dismissMembers))
	for _, v := range users {
		snapm[v.Id] = v
	}
	for _, member := range ar.joinMembers {
		if snapm[member.Id] == nil {
			l4g.Errorf("user: %d C2L_GuildCombineCheck: get snapshot nil. uid:%+v",
				args.UID, member)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineCheck, smsg)
			return false
		}
		smsg.JoinUsers = append(smsg.JoinUsers, snapm[member.Id])
	}
	for _, member := range ar.dismissMembers {
		if snapm[member.Id] == nil {
			l4g.Errorf("user: %d C2L_GuildCombineCheck: get snapshot nil. uid:%+v",
				args.UID, member)
			smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineCheck, smsg)
			return false
		}
		smsg.DismissUsers = append(smsg.DismissUsers, snapm[member.Id])
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineCheck, smsg)
	return true
}

type C2LGuildCombineRatifyCommand struct {
	base.UserCommand
}

func (c *C2LGuildCombineRatifyCommand) Error(smsg *cl.L2C_GuildCombineRatify, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineRatify, smsg)
	return false
}

func (c *C2LGuildCombineRatifyCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildCombineRatify{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCombineRatify Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildCombineRatify user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildCombineRatify{
		Ret:    uint32(cret.RET_OK),
		Gids:   cmsg.Gids,
		Accept: cmsg.Accept,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildCombineRatify user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	// TODO LOG
	// guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	// if !ok {
	// 	l4g.Errorf("user: %d C2L_GuildCombineRatify: failed, guildManager not exist.", c.Msg.UID)
	// 	return c.Error(smsg, uint32(cret.RET_ERROR))
	// }

	// logInfo := &log.GuildManage{
	// 	Type:     log.GUILD_LOG_MANAGE_GLM_ACCEPT,
	// 	MemberId: cmsg.Id,
	// }

	// if !cmsg.Accept {
	// 	logInfo.Type = log.GUILD_LOG_MANAGE_GLM_REFUSE
	// }
	// c.User.LogGuildManage(c.Srv, logInfo)

	smsg.CombineApplyList = cmsg.ApplyList
	smsg.CombineAppliedList = cmsg.AppliedList

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineRatify, smsg)
	return true
}

type C2LGuildApplyRatifyCommand struct {
	base.UserCommand
}

func (c *C2LGuildApplyRatifyCommand) Error(smsg *cl.L2C_GuildApplyRatify, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyRatify, smsg)
	return false
}

func (c *C2LGuildApplyRatifyCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildApplyRatify{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildApplyRatify Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildApplyRatify user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildApplyRatify{
		Ret:    uint32(cret.RET_OK),
		Id:     cmsg.Id,
		Accept: cmsg.Accept,
		Delete: cmsg.Delete,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildApplyRatify user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	newApplyIds := make([]uint64, 0, len(cmsg.NewApplyInfos))
	for _, info := range cmsg.NewApplyInfos {
		newApplyIds = append(newApplyIds, info.Uid)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildApplyRatify: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guild := guildM.GetGuildByUser(c.User.ID())
	if guild != nil {
		guild.SetHaveApplicant(len(newApplyIds) > 0)
		guildM.SetChange(guild)
	}

	logInfo := &log.GuildManage{
		Type:     log.GUILD_LOG_MANAGE_GLM_ACCEPT,
		MemberId: cmsg.Id,
	}

	if !cmsg.Accept {
		logInfo.Type = log.GUILD_LOG_MANAGE_GLM_REFUSE
	}
	c.User.LogGuildManage(c.Srv, logInfo)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildApplyRatify, smsg)
	return true
}

type C2LGuildUserApplyCommand struct {
	base.UserCommand
}

func (c *C2LGuildUserApplyCommand) Error(smsg *cl.L2C_GuildUserApply, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildUserApply, smsg)
	return false
}

func (c *C2LGuildUserApplyCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildUserApply{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildUserApply Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildUserApply user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildUserApply{
		Ret:    uint32(cret.RET_OK),
		Id:     cmsg.Id,
		Type:   cmsg.Type,
		Guilds: cmsg.ApplyIds,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildUserApply user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildUserApply: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//flowerM, ok := c.Srv.GetActivity(activity.Flower).(*aflower.Manager)
	//if !ok {
	//	l4g.Errorf("user: %d C2L_GuildQuit: failed, flowerManager not exist.", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_ERROR))
	//}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildUserApply: failed, guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	switch cmsg.Type {
	case character.GuildUserJoinIn: // 加入

		// ***********这部分通过notify处理
		//guild := guildM.GetGuildByID(cmsg.Id)
		//if guild == nil {
		//	guild = guildM.AddGuild(cmsg.Guild)
		//	guildUser.JoinGuild(guild.ID(), uint32(character.GuildGradeMember))
		//} else {
		//	guildM.JoinIn([]*aguild.User{guildUser}, guild)
		//}

		//c.User.UserGuild().Update(guild.ID(), guild.Name())
		//// 检查公会天赋是否激活，如果激活了，重新计算战力
		//c.User.GuildTalent().UserGuildChange(c.User, c.Srv)
		//c.User.SendSelfToClient()
		//
		//// 更新密林公会数据
		//flowerM.UpdateGuildID(c.Srv, c.Msg.UID, 0, guild.ID())
		//guildM.SetChange(guild)
		//
		//// 加入公会群聊
		//character.ChatJoinGuildGroupToPlatform(c.Srv, c.Msg.UID, guild.ID(), character.ChatGroupOpJoin)
		//
		//// 在公会聊天频道发送消息
		//message := genGuildChatMessages(c.Srv, guildUser)
		//character.ChatSendMsgToPlatform(c.Srv, c.Msg.UID, message)
		//
		//if guildUser.GetApprover() != "" {
		//	guildUser.ClearApprover()
		//	guildM.SaveGuildUser(guildUser)
		//}
	case character.GuildUserApply: // 申请
		guildUser.AddApplyIds([]uint64{cmsg.Id})
		guildM.SaveGuildUser(guildUser)
		c.User.LogGuildUserJoinOrQuit(c.Srv, &log.GuildUserJoinOrQuit{Type: log.GUILD_LOG_USER_GLU_APPLY})
	case character.GuildUserCancelApply: // 取消申请
		guildUser.DelApplyID(cmsg.Id)
		guildM.SaveGuildUser(guildUser)
		c.User.LogGuildUserJoinOrQuit(c.Srv, &log.GuildUserJoinOrQuit{Type: log.GUILD_LOG_USER_GLU_CANCEL_APPLY})
	case character.GuildUserQuickJoin: // 快速加入
		if cmsg.Guild == nil || cmsg.Guild.Id == 0 {
			l4g.Errorf("user: %d C2L_GuildUserApply: quick join error. cmsg:%+v", c.Msg.UID, cmsg)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}

		// 这部分通过notify处理
		//guild := guildM.GetGuildByID(cmsg.Guild.Id)
		//if guild == nil {
		//	guild = guildM.AddGuild(cmsg.Guild)
		//	guildUser.JoinGuild(guild.ID(), uint32(character.GuildGradeMember))
		//} else {
		//	guildM.JoinIn([]*aguild.User{guildUser}, guild)
		//}

		//c.User.UserGuild().Update(guild.ID(), guild.Name())
		//// 检查公会天赋是否激活，如果激活了，重新计算战力
		//c.User.GuildTalent().UserGuildChange(c.User, c.Srv)
		//c.User.SendSelfToClient()
		//
		//
		//// 加入公会群聊
		//character.ChatJoinGuildGroupToPlatform(c.Srv, c.Msg.UID, guild.ID(), character.ChatGroupOpJoin)
		//
		//message := genGuildChatMessages(c.Srv, guildUser)
		//character.ChatSendMsgToPlatform(c.Srv, c.Msg.UID, message)
		//if guildUser.GetApprover() != "" {
		//	guildUser.ClearApprover()
		//	guildM.SaveGuildUser(guildUser)
		//}
	case character.GuildUserQuickTransfer: // 快速转会
		smsg.LeaveCount = guildUser.FlushGuildLevelCnt()
		c.User.LogGuildQuickTransfer(c.Srv, cmsg.OldGid, cmsg.Id)
	default:
		l4g.Errorf("user: %d C2L_GuildUserApply: request type error. cmsg.Type:%d", c.Msg.UID, cmsg.Type)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	smsg.Guilds = guildUser.ApplyIds()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildUserApply, smsg)
	c.User.LogGuildUserApply(c.Srv, cmsg.Type, cmsg.Id, cmsg.OldApplyIds, guildUser.ApplyIds())
	return true
}

type C2LGuildCombineApplyCommand struct {
	base.UserCommand
}

func (c *C2LGuildCombineApplyCommand) Error(smsg *cl.L2C_GuildCombineApply, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineApply, smsg)
	return false
}

func (c *C2LGuildCombineApplyCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildCombineApply{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildCombineApply Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildCombineApply user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildCombineApply{
		Ret:  uint32(cret.RET_OK),
		Gid:  cmsg.TargetGid,
		Type: cmsg.Type,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildCombineApply user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	// guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	// if !ok {
	// 	l4g.Errorf("user: %d C2L_GuildCombineApply: failed, guildManager not exist.", c.Msg.UID)
	// 	return c.Error(smsg, uint32(cret.RET_ERROR))
	// }

	// // 获取发起公会
	// sourceGuild := guildM.GetGuildByID(cmsg.SourceGid)
	// if sourceGuild == nil {
	// 	l4g.Errorf("user: %d C2L_GuildCombineApply: sourceGuild not exist. gid %d", c.Msg.UID, cmsg.SourceGid)
	// 	return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	// }

	// 返回发起公会的申请列表
	smsg.CombineApplyList = cmsg.SourceApplyList
	smsg.CombineAppliedList = cmsg.SourceAppliedList

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildCombineApply, smsg)

	// TODO 埋点
	//c.User.LogGuildApply(c.Srv, cmsg.Type, cmsg.Id, cmsg.OldApplyIds, guildUser.ApplyIds())

	return true
}

func genGuildChatMessages(srv command.Servicer, guildUser *aguild.User) *cl.ChatMessage {
	userHead := &cl.ChatUserHead{
		Id:   strconv.FormatUint(guildUser.ID(), 10),
		Name: guildUser.Name(),
	}
	message := character.NewMsg(character.Id(srv), character.Head(userHead),
		character.Type(goxml.ChatTypeGuildJoin))

	return message
}

type C2LGuildSendMailCommand struct {
	base.UserCommand
}

func (c *C2LGuildSendMailCommand) Error(smsg *cl.L2C_GuildSendMail, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendMail, smsg)
	return false
}

func (c *C2LGuildSendMailCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildSendMail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSendMail Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildSendMail user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildSendMail{
		Ret:     uint32(cret.RET_OK),
		Title:   cmsg.Title,
		Content: cmsg.Content,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildSendMail user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildSendMail: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildSendMail: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser.SetSendMailTm()
	guildM.SaveGuildUser(guildUser)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendMail, smsg)
	return true
}

type C2LGuildQuitCommand struct {
	base.UserCommand
}

func (c *C2LGuildQuitCommand) Error(smsg *cl.L2C_GuildQuit, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildQuit, smsg)
	return false
}

func (c *C2LGuildQuitCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildQuit{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildQuit Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildQuit user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildQuit{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildQuit user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	// ***** 这里只做给前端数据的回传，实际退出操作，已在GuildNotify中通过推送处理*****

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildQuit: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildQuit: guildUser is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	c.User.LogGuildUserJoinOrQuit(c.Srv, &log.GuildUserJoinOrQuit{
		OldGid: cmsg.OldGid,
		Type:   log.GUILD_LOG_USER_GLU_QUIT,
	})
	smsg.LeaveCount = guildUser.FlushGuildLevelCnt()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildQuit, smsg)
	return true
}

type C2LGuildDisbandCommand struct {
	base.UserCommand
}

func (c *C2LGuildDisbandCommand) Error(smsg *cl.L2C_GuildDisband, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDisband, smsg)
	return false
}

func (c *C2LGuildDisbandCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildDisband{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDisband Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildDisband user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildDisband{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildDisband user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	// ***** 这里只做给前端数据的回传，实际解散操作，已在GuildNotify中通过推送处理*****

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDisband: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.Msg.UID)
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDisband: failed, guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	c.User.LogGuildManage(c.Srv, cmsg.LogInfo)
	smsg.LeaveCount = guildUser.FlushGuildLevelCnt()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDisband, smsg)
	return true
}

type C2LGuildSearchCommand struct {
	base.UserCommand
}

func (c *C2LGuildSearchCommand) Error(smsg *cl.L2C_GuildSearch, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSearch, smsg)
	return false
}

func (c *C2LGuildSearchCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildSearch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSearch Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildSearch user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildSearch{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildSearch user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Guild = cmsg.Guild
	smsg.IsCombine = cmsg.IsCombine
	smsg.CombineStatus = cmsg.CombineStatus

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSearch, smsg)
	return true
}

type C2LGuildGetMembersCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetMembersCommand) Error(smsg *cl.L2C_GuildGetMembers, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMembers, smsg)
	return false
}

func (c *C2LGuildGetMembersCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildGetMembers{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetMembers Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildGetMembers user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildGetMembers{
		Ret: cmsg.Ret,
		Id:  cmsg.Id,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildGetMembers user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	req := &AsyncC2LGetGuildMembersSnapshotReq{
		smsg: smsg,
		cmsg: cmsg,
	}

	c.GetGuildMemberFromAll(cmsg.Id, 0, cmsg.Members, req)
	return true
}

type AsyncC2LGetGuildMembersSnapshotReq struct {
	smsg *cl.L2C_GuildGetMembers
	cmsg *l2c.C2L_GuildGetMembers
}

func (ar *AsyncC2LGetGuildMembersSnapshotReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, data interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildGetMembers: get snapshot error:%d", args.UID, retCode)
		//smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		//args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildMainInfo, smsg)
		//return false
	}

	members := data.([]*cl.GuildMemberInfo)
	if len(members) == 0 {
		l4g.Errorf("user: %d C2L_GuildGetMembers: get members failed, no data", args.UID)
		smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMembers, smsg)
		return false
	}
	fixClGuildMembers(members, ar.cmsg.Members)
	smsg.Members = members

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMembers, smsg)
	return true
}

// fixClGuildMembers
// @Description: 部分数据，游戏服和跨服都有，不是实时更新的。这里把以跨服为准的做替换。
// @param clGuildMembers
// @param sameServerMembers
func fixClGuildMembers(clGuildMembers []*cl.GuildMemberInfo, sameServerMembers map[uint64]*cr.GuildMembers) {
	for _, members := range sameServerMembers {
		for _, member := range members.GetMembers() {
			exist := false
			for _, clMember := range clGuildMembers {
				if clMember.GetInfo().GetId() == member.GetId() {
					clMember.Grade = member.Grade
					activityPoints := uint32(0)
					for _, p := range member.ActivityPoint {
						activityPoints += p
					}
					clMember.WeeklyActivityPoint = activityPoints
					exist = true
					break
				}
			}
			if !exist {
				clGuildMembers = append(clGuildMembers, &cl.GuildMemberInfo{
					Info: &cl.UserSnapshot{
						Id:   member.Id,
						Sid:  member.Sid,
						Name: member.Name,
					},
					Grade: member.Grade,
				})
			}
		}
	}
}

type C2LGuildGetDeclarationCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetDeclarationCommand) Error(smsg *cl.L2C_GuildGetDeclaration, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDeclaration, smsg)
	return false
}

func (c *C2LGuildGetDeclarationCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildGetDeclaration{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetDeclaration Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildGetDeclaration user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildGetDeclaration{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildGetDeclaration user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Declaration = cmsg.Declaration
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDeclaration, smsg)
	return true
}

type C2LGuildDonateCommand struct {
	base.UserCommand
}

func (c *C2LGuildDonateCommand) Error(smsg *cl.L2C_GuildDonate, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDonate, smsg)
	return false
}

func (c *C2LGuildDonateCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildDonate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDonate Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildDonate user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildDonate{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildDonate user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildDonate: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildDonate: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildDonate: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	donateInfo := goxml.GetData().GuildDonateInfoM.Index(cmsg.Id)
	if donateInfo == nil {
		l4g.Errorf("user: %d C2L_GuildDonate: donateInfo not exist. id:%d.", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if len(donateInfo.CostClRes) > 0 {
		smsg.Ret, smsg.Awards = c.User.Trade(c.Srv, donateInfo.CostClRes, donateInfo.AwardClRes, uint32(log.RESOURCE_CHANGE_REASON_GUILD_DONATE), 0)
	} else {
		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, donateInfo.AwardClRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GUILD_DONATE), 0)
	}

	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildDonate: trade failed", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	oldGuildLevel, oldGuildExp := cmsg.OldLevel, cmsg.OldExp

	if donateInfo.ActivityAdd > 0 {
		guildUser.AddWeeklyActivityPoint(donateInfo.ActivityAdd)
	}
	newCount := guildUser.AddDonateCount()
	guildM.SaveGuildUser(guildUser)

	smsg.DonatePointNum = cmsg.DonatePointNum
	smsg.DonatePersonNum = cmsg.DonatePersonNum
	smsg.DonateCount = newCount
	smsg.WeeklyActivityPoint = guildUser.GetWeeklyActivityPoint()
	smsg.Id = cmsg.Id
	smsg.Level = cmsg.Level
	smsg.Exp = cmsg.Exp
	smsg.GuildActivity = cmsg.GuildActivity
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDonate, smsg)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeGuildDonate, 1)
	c.User.LogGuildDonate(c.Srv, oldGuildLevel, oldGuildExp, cmsg.Level, cmsg.Exp, guild.ID(), cmsg.DonatePointNum, guildUser.GetWeeklyActivityPoint(), cmsg.Id)

	return true
}

type C2LGuildGetDonateAwardCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetDonateAwardCommand) Error(smsg *cl.L2C_GuildGetDonateAward, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDonateAward, smsg)
	return false
}

func (c *C2LGuildGetDonateAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildGetDonateAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetDonateAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildGetDonateAward user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildGetDonateAward{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildGetDonateAward user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildGetDonateAward: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user: %d C2L_GuildGetDonateAward: not in guild.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_NOT_IN_GUILD))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildGetDonateAward: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	totalAward := make([]*cl.Resource, 0, len(cmsg.Ids)*2) //nolint:mnd
	for _, v := range cmsg.Ids {

		if guildUser.IsDonateAwards(v) {
			l4g.Errorf("user: %d C2L_GuildGetDonateAward: client id:%d info is repeated", c.Msg.UID, v)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}

		collectiveDonateInfo := goxml.GetData().GuildCollectiveDonateInfoM.Index(v)
		if collectiveDonateInfo == nil {
			l4g.Errorf("user: %d C2L_GetDonateAward: client id:%d info is nil", c.Msg.UID, v)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		totalAward = append(totalAward, collectiveDonateInfo.ClRes...)
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalAward, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GUILD_GET_DONATE_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GetDonateAward: award failed", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	guildUser.AddDonateAwards(cmsg.Ids)

	guildM.SaveGuildUser(guildUser)

	smsg.DonateAwardIds = guildUser.GetDonateAwardIds()
	smsg.Ids = cmsg.Ids
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDonateAward, smsg)
	c.User.LogGuildDonateReceive(c.Srv, cmsg.Ids)
	return true
}

type C2LGuildLogListCommand struct {
	base.UserCommand
}

func (c *C2LGuildLogListCommand) Error(smsg *cl.L2C_GuildLogList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildLogList, smsg)
	return false
}

func (c *C2LGuildLogListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildLogList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildLogList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildLogList user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildLogList{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildLogList user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Logs = cmsg.Logs
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildLogList, smsg)
	return true
}

type C2LGuildDonateLogListCommand struct {
	base.UserCommand
}

func (c *C2LGuildDonateLogListCommand) Error(smsg *cl.L2C_GuildDonateLogList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDonateLogList, smsg)
	return false
}

func (c *C2LGuildDonateLogListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildDonateLogList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDonateLogList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildDonateLogList user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildDonateLogList{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildDonateLogList user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Logs = cmsg.Logs
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildDonateLogList, smsg)
	return true
}

type C2LGuildListGetDetailCommand struct {
	base.UserCommand
}

func (c *C2LGuildListGetDetailCommand) Error(smsg *cl.L2C_GuildListGetDetail, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildListGetDetail, smsg)
	return false
}

func (c *C2LGuildListGetDetailCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildListGetDetail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildListGetDetail Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildListGetDetail user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildListGetDetail{
		Ret:         cmsg.Ret,
		Gid:         cmsg.Gid,
		Declaration: cmsg.Declaration,
		Snapshot:    cmsg.Snapshot,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildListGetDetail user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	req := &AsyncC2LGuildListGetDetailReq{
		cmsg: cmsg,
		smsg: smsg,
	}

	c.GetGuildMemberFromAll(cmsg.Gid, 0, cmsg.Members, req)
	return true
}

type AsyncC2LGuildListGetDetailReq struct {
	cmsg *l2c.C2L_GuildListGetDetail
	smsg *cl.L2C_GuildListGetDetail
}

func (ar *AsyncC2LGuildListGetDetailReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, data interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d L2C_GuildListGetDetail: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildListGetDetail, smsg)
		return false
	}

	members := data.([]*cl.GuildMemberInfo)
	if len(members) == 0 {
		l4g.Errorf("user: %d L2C_GuildListGetDetail: get members failed, no data", args.UID)
		smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildListGetDetail, smsg)
		return false
	}

	fixClGuildMembers(members, ar.cmsg.Members)
	smsg.Members = members
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GuildListGetDetail, smsg)
	return true
}

type C2LGuildGetBadgeListCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetBadgeListCommand) Error(smsg *cl.L2C_GuildGetBadgeList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetBadgeList, smsg)
	return false
}

func (c *C2LGuildGetBadgeListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildGetBadgeList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetBadgeList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildGetBadgeList user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildGetBadgeList{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildGetBadgeList user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.List = cmsg.BadgeList
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetBadgeList, smsg)
	return true
}

type C2LGuildGetDivisionAwardInfoCommand struct {
	base.UserCommand
}

func (c *C2LGuildGetDivisionAwardInfoCommand) Error(smsg *cl.L2C_GuildGetDivisionAwardInfo, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDivisionAwardInfo, smsg)
	return false
}

func (c *C2LGuildGetDivisionAwardInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildGetDivisionAwardInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetDivisionAwardInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildGetDivisionAwardInfo user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildGetDivisionAwardInfo{
		Ret: cmsg.Ret,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildGetDivisionAwardInfo user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user:%d C2L_GuildGetDivisionAwardInfo  guildManager not exit", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user:%d C2L_GuildGetDivisionAwardInfo get guildUser failed", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guild := guildM.GetGuildByUser(c.User.ID())
	if guild == nil {
		l4g.Errorf("user:%d C2L_GuildGetDivisionAwardInfo get guild by user failed", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	// 更新下缓存，领奖要用
	guild.UpdateCache(cmsg.Chapter, cmsg.SeasonTopDivision, cmsg.CurrentDivision, cmsg.IsThrough)

	smsg.DivisionRank = cmsg.DivisionRank
	smsg.CurrentDivision = cmsg.CurrentDivision
	smsg.SeasonTopDivision = cmsg.SeasonTopDivision
	smsg.ReceivedDivisions = guildUser.GetSeasonRecvFirstDivision()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetDivisionAwardInfo, smsg)
	return true
}

type GuildSyncKickCountCommand struct {
	base.Command
}

func (c *GuildSyncKickCountCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildSyncKickCount{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSyncKickCount Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildSyncKickCount recv from cross:%+v", cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildSyncKickCount recv cross info error, ret:%d", cmsg.Ret)
		return false
	}

	if len(cmsg.Uids) == 0 {
		l4g.Errorf("C2L_GuildSyncKickCount recv cross info error. len uid is 0.")
		return false
	}

	if cmsg.KickCount == 0 {
		l4g.Errorf("C2L_GuildSyncKickCount KickCount is 0")
		return false
	}

	_, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("C2L_GuildSyncKickCount get guildM error.")
		return false
	}

	for _, uid := range cmsg.Uids {
		user := c.Srv.UserM().GetUserWithCache(uid)
		if user != nil {
			user.SendCmdToGateway(cl.ID_MSG_L2C_GuildSyncKickCount, &cl.L2C_GuildSyncKickCount{
				Ret:       uint32(ret.RET_OK),
				KickCount: cmsg.KickCount,
			})
		}
	}
	return true
}

type GuildDeleteMailsCommand struct {
	base.Command
}

func (c *GuildDeleteMailsCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildDeleteMails{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildDeleteMails Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildDeleteMails recv from cross:%+v", cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildDeleteMails recv cross info error, ret:%d", cmsg.Ret)
		return false
	}

	if len(cmsg.Uids) == 0 || len(cmsg.Uids) > 1000 {
		l4g.Errorf("C2L_GuildDeleteMails recv cross info error. len uid is 0.")
		return false
	}

	offLineIds := make([]uint64, 0, len(cmsg.Uids))
	for _, uid := range cmsg.Uids {
		user := c.Srv.UserM().GetUserWithCache(uid)
		if user == nil {
			offLineIds = append(offLineIds, uid)
			continue
		}
		mb := user.MailBox()
		delIds := make([]uint64, 0, 1)
		for mailId, mail := range mb.GetAll() {
			if mail.BaseId == character.MailIDGuildSendMail {
				delIds = append(delIds, mailId)
				continue
			}
		}
		if len(delIds) > 0 {
			mb.Delete(delIds)
		}
	}
	if len(offLineIds) > 0 {
		c.Srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_DeleteUserGuildMails), 0, &r2l.L2R_DeleteUserGuildMails{
			Ret:        uint32(cret.RET_OK),
			Uids:       offLineIds,
			MailBaseId: character.MailIDGuildSendMail,
		})
	}
	return true
}

type GuildSyncLogicGuildCommand struct {
	base.Command
}

func (c *GuildSyncLogicGuildCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildSyncLogicGuild{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSyncLogicGuild Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_GuildSyncLogicGuild recv from cross:%+v", cmsg)

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildSyncLogicGuild ret error: %d retCode:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	if cmsg.Guild == nil {
		l4g.Errorf("C2L_GuildSyncLogicGuild ret error: %d logicGuild is nil", c.Msg.UID)
		return false
	}

	guildM := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	guildM.UpdateLogicGuild(cmsg.Guild)
	return true
}

type GuildGetMedalsCommand struct {
	base.UserCommand
}

func (c *GuildGetMedalsCommand) Error(smsg *cl.L2C_GuildGetMedals, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMedals, smsg)
	return false
}

func (c *GuildGetMedalsCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildGetMedals{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildGetMedals Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildGetMedals user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildGetMedals{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildGetMedals user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if cmsg.Resp == nil {
		l4g.Errorf("C2L_GuildGetMedals user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, uint32(cret.RET_SERVER_ERROR))
	}

	smsg = cmsg.Resp

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user:%d C2L_GuildGetMedals  guildManager not exit", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user:%d C2L_GuildGetMedals get guildUser failed", c.User.ID())
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if guildUser.IsMedalLikeFlowerToItem() { // 已经给过奖励，数据不给前端，不弹框
		smsg.LikedCount = 0
		smsg.TopNames = smsg.TopNames[0:0]
	} else {
		c.handleMedalLikeFlowerToItem(guildM, guildUser, smsg)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildGetMedals, smsg)
	return true
}

func (c *GuildGetMedalsCommand) handleMedalLikeFlowerToItem(guildM *aguild.Manager, guildUser *aguild.User, smsg *cl.L2C_GuildGetMedals) {
	if smsg.LikedCount > 0 && !guildUser.IsMedalLikeFlowerToItem() {
		award := goxml.GetData().GuildConfigInfoM.GetRecvLikeRes(smsg.LikedCount)
		c.User.Award(c.Srv, []*cl.Resource{award}, character.MailIDBagFull, uint32(log.RESOURCE_CHANGE_REASON_GUILD_MEDAL_LIKE_TO_ITEM), 0)
		guildUser.SetMedalLikeFlowerToItem(true)
		guildM.SaveGuildUser(guildUser)
	}
}

type GuildMedalLikeCommand struct {
	base.UserCommand
}

func (c *GuildMedalLikeCommand) Error(smsg *cl.L2C_GuildMedalLike, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMedalLike, smsg)
	return false
}

func (c *GuildMedalLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildMedalLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildMedalLike Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_GuildMedalLike user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildMedalLike{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildMedalLike user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if cmsg.Resp == nil {
		l4g.Errorf("C2L_GuildMedalLike user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, uint32(cret.RET_SERVER_ERROR))
	}

	smsg = cmsg.Resp

	costs := goxml.GetData().GuildConfigInfoM.GetGuildMedalLikeCosts()
	retCode := c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_GUILD_MEDAL_LIKE), 0)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_GuildMedalLike: cost error. req:%s", c.Msg.UID, cmsg)
		return c.Error(smsg, retCode)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildMedalLike, smsg)
	return true
}

type GuildSyncSeasonArenaDataCommand struct {
	base.Command
}

func (c *GuildSyncSeasonArenaDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildSyncSeasonArenaData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSyncSeasonArenaData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("C2L_GuildSyncSeasonArenaData user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildSyncSeasonArenaData user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	saM, ok := c.Srv.GetActivity(activity.SeasonArena).(*seasonarena.Manager)
	if !ok {
		l4g.Errorf("C2L_GuildSyncSeasonArenaData seasonarena manager not exist.")
		return false
	}

	saM.ClearGuildMedalBak()
	return true
}

type GuildSendRecruitMsgCommand struct {
	base.UserCommand
}

func (c *GuildSendRecruitMsgCommand) Error(smsg *cl.L2C_GuildSendRecruitMsg, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendRecruitMsg, smsg)
	return false
}

func (c *GuildSendRecruitMsgCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_GuildSendRecruitMsg{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_GuildSendRecruitMsg Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("C2L_GuildSendRecruitMsg user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_GuildSendRecruitMsg{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_GuildSendRecruitMsg user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}
	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: failed, guildManager not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d C2L_GuildSendRecruitMsg: guildUser not exist.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_GUILD_NOT_EXIST))
	}

	message := character.NewMsg(
		character.Id(c.Srv),
		character.Head(c.User.NewChatUserHead(c.Srv.GetLogicGuild(c.User.ID()))),
		character.Sender(c.Msg.UID),
		character.Type(goxml.ChatTypeGuildRecruit),
		character.Params(
			strconv.FormatUint(cmsg.GuildId, 10),
			cmsg.Name,
			strconv.FormatBool(cmsg.IsApply),
			strconv.FormatUint(uint64(cmsg.Level), 10),
			strconv.FormatUint(uint64(cmsg.LvLimit), 10),
			strconv.FormatUint(uint64(cmsg.PowerLimit), 10),
		),
	)
	character.ChatSendMsgToPlatform(c.Srv, c.Msg.UID, message)

	guildUser.SetRecruitCdTime(time.Now().Unix() + goxml.GetData().ChatConfigInfoM.GuildRecruitCD)
	guildM.SaveGuildUser(guildUser)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GuildSendRecruitMsg, smsg)
	c.User.LogGuildManage(c.Srv, &log.GuildManage{
		Gid:  cmsg.GuildId,
		Name: cmsg.Name,
		Type: log.GUILD_LOG_MANAGE_GLM_RECRUIT,
	})
	return true
}
