package cross

import (
	"app/goxml"
	"app/logic/activity"
	apeak "app/logic/activity/peak"
	"app/logic/battle"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitPeak(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakBaseData), &C2LPeakBaseDataCommand{}, state)                       // 基本数据
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakGetMatch), &C2LPeakGetMatchCommand{}, state)                       // 获取比赛数据
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakGuessList), &C2LPeakGuessListCommand{}, state)                     // 获取竞猜列表数据
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakAddGuessCount), &C2LPeakAddGuessCountCommand{}, state)             // 增加竞猜下注次数
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakResult), &C2LPeakResultCommand{}, state)                           // 比赛结果
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakRankList), &C2LPeakRankListCommand{}, state)                       // 排行榜
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakFight), &C2LPeakFightCommand{}, state)                             // 战斗
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakFighterDetail), &C2LPeakFighterDetailCommand{}, state)             // 选手完赛详情数据
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakPushState), &C2LPeakPushStateCommand{}, state)                     // 推送新一轮比赛数据
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakUpdatePlayer), &C2LPeakUpdatePlayerCommand{}, state)               // 新周期开始，请求更新分数和参赛选手数据
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakGetSnapshot), &C2LPeakGetSnapshotCommand{}, state)                 // 获取快照
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakRankFirst), &C2LPeakRankFirstCommand{}, state)                     // 获取排行榜第一名
	cmds.Register(uint32(l2c.ID_MSG_C2L_PeakGetLastBattleReport), &C2LPeakGetLastBattleReportCommand{}, state) // 获取上一场战报
}

type C2LPeakBaseDataCommand struct {
	base.UserCommand
}

func (c *C2LPeakBaseDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPeakBaseDataCommand) Error(smsg *cl.L2C_PeakBaseData, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakBaseData, smsg)
	return false
}

func (c *C2LPeakBaseDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakBaseData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakBaseData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("user:%d C2L_PeakBaseData: recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_PeakBaseData{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PeakBaseData: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	//数据异常
	if cmsg.Peak == nil || cmsg.Peak.State == nil {
		l4g.Errorf("[FATAL] user: %d C2L_PeakBaseData: no peak data", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_PeakBaseData: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	smsg.Score = manager.GetScore(c.Msg.UID)
	smsg.Rank = manager.GetRank(c.Msg.UID)
	smsg.State = cmsg.Peak.State.State
	smsg.MyMatches = cmsg.MyMatches
	smsg.RecvInviteReward = manager.GetRecvInviteRewardStatus(c.Msg.UID)

	user := manager.GetUser(c.Msg.UID)
	if user == nil {
		user = manager.AddUser(c.Msg.UID)
	}

	var serverUsers []*cl.ServerUser
	smsg.GuessStatus = user.CalcInvolvedGuessCountByRound(cmsg.Peak.State.State.Round)
	if len(smsg.MyMatches) > 0 {
		serverUsers = makeServerUsersByMatch(smsg.MyMatches)
	}

	smsg.GuessMatch = cmsg.GuessMatch
	if smsg.GuessMatch != nil {
		guessMatchServerUsers := makeServerUsersByMatch([]*cl.PeakMatch{smsg.GuessMatch})
		serverUsers = append(serverUsers, guessMatchServerUsers...)
	}

	if len(serverUsers) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakBaseData, smsg)
		return c.ResultOK(smsg.Ret)
	}

	c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LPeakBaseDataReq{smsg}, 0)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LPeakBaseDataReq struct {
	smsg *cl.L2C_PeakBaseData
}

func (ar *AsyncC2LPeakBaseDataReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	if retCode == uint32(cret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LPeakBaseDataReq: cross req timeout", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakBaseData, smsg)
		return false
	}

	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d AsyncC2LPeakBaseDataReq: retCode error, ret:%d", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakBaseData, smsg)
		return false
	}

	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LPeakBaseDataReq: get snaps failed", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakBaseData, smsg)
		return false
	}

	tmpSnaps := make(map[uint64]*cl.UserSnapshot)
	for _, snap := range snaps {
		tmpSnaps[snap.Id] = snap
	}

	for _, match := range smsg.MyMatches {
		if snap, exist := tmpSnaps[match.LeftFighter.Uid]; exist {
			match.LeftFighter.Snapshot = snap
		}
		if snap, exist := tmpSnaps[match.RightFighter.Uid]; exist {
			match.RightFighter.Snapshot = snap
		}
	}

	if smsg.GuessMatch != nil {
		if snap, exist := tmpSnaps[smsg.GuessMatch.LeftFighter.Uid]; exist {
			smsg.GuessMatch.LeftFighter.Snapshot = snap
		}
		if snap, exist := tmpSnaps[smsg.GuessMatch.RightFighter.Uid]; exist {
			smsg.GuessMatch.RightFighter.Snapshot = snap
		}
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakBaseData, smsg)
	return true
}

type C2LPeakGetMatchCommand struct {
	base.UserCommand
}

func (c *C2LPeakGetMatchCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPeakGetMatchCommand) Error(smsg *cl.L2C_PeakGetMatch, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetMatch, smsg)
	return false
}

func (c *C2LPeakGetMatchCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakGetMatch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakGetMatch Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("user:%d C2L_PeakGetMatch: recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_PeakGetMatch{
		Ret:    uint32(cret.RET_OK),
		Params: cmsg.Params,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PeakGetMatch: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Matches = cmsg.Matches
	if len(smsg.Matches) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetMatch, smsg)
		return c.ResultOK(smsg.Ret)
	}

	serverUsers := makeServerUsersByMatch(cmsg.Matches)
	c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LPeakGetMatchReq{smsg, len(serverUsers)}, 0)
	return c.ResultOK(smsg.Ret)
}

func makeServerUsersByMatch(matches []*cl.PeakMatch) []*cl.ServerUser {
	serverUsers := make([]*cl.ServerUser, 0, len(matches))
	sameUID := make(map[uint64]util.None)
	for _, v := range matches { //nolint:varnamelen
		if _, exist := sameUID[v.LeftFighter.Uid]; !exist {
			serverUsers = append(serverUsers, &cl.ServerUser{
				Uid: v.LeftFighter.Uid,
				Sid: v.LeftFighter.Sid,
			})
			sameUID[v.LeftFighter.Uid] = util.None{}
		}

		if _, exist := sameUID[v.RightFighter.Uid]; !exist {
			serverUsers = append(serverUsers, &cl.ServerUser{
				Uid: v.RightFighter.Uid,
				Sid: v.RightFighter.Sid,
			})
			sameUID[v.RightFighter.Uid] = util.None{}
		}
	}
	return serverUsers
}

type AsyncC2LPeakGetMatchReq struct {
	smsg  *cl.L2C_PeakGetMatch
	count int
}

func (ar *AsyncC2LPeakGetMatchReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	if retCode == uint32(cret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LPeakGetMatchReq: cross req timeout", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetMatch, smsg)
		return false
	}

	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d AsyncC2LPeakGetMatchReq: retCode error, ret:%d", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetMatch, smsg)
		return false
	}

	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LPeakGetMatchReq: get snaps failed", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetMatch, smsg)
		return false
	}

	tmpSnaps := make(map[uint64]*cl.UserSnapshot)
	for _, snap := range snaps {
		tmpSnaps[snap.Id] = snap
	}

	for _, match := range smsg.Matches {
		if snap, exist := tmpSnaps[match.LeftFighter.Uid]; exist {
			match.LeftFighter.Snapshot = snap
		}
		if snap, exist := tmpSnaps[match.RightFighter.Uid]; exist {
			match.RightFighter.Snapshot = snap
		}
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetMatch, smsg)
	return true
}

type C2LPeakGuessListCommand struct {
	base.UserCommand
}

func (c *C2LPeakGuessListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPeakGuessListCommand) Error(smsg *cl.L2C_PeakGuessList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakGuessList, smsg)
	return false
}

func (c *C2LPeakGuessListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakGuessList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakGuessList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_PeakGuessList user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_PeakGuessList{
		Ret:    uint32(cret.RET_OK),
		Rounds: cmsg.Rounds,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PeakGuessList: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if len(cmsg.Matches) == 0 {
		l4g.Errorf("user: %d C2L_PeakGuessList: no guess match", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_PeakGuessList: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	user := manager.GetUser(c.Msg.UID)
	if user == nil {
		l4g.Errorf("user: %d C2L_PeakGuessList: no user", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_PEAK_NO_USER))
	}

	smsg.Matches = cmsg.Matches
	smsg.Guess = user.FlushGuessByRounds(cmsg.Rounds)

	serverUsers := makeServerUsersByMatch(cmsg.Matches)
	c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LPeakGuessListReq{smsg, len(serverUsers)}, 0)
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LPeakGuessListReq struct {
	smsg  *cl.L2C_PeakGuessList
	count int
}

func (ar *AsyncC2LPeakGuessListReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	if retCode == uint32(cret.RET_CROSS_REQ_TIMEOUT) {
		l4g.Errorf("user: %d AsyncC2LPeakGuessListReq: cross req timeout", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGuessList, smsg)
		return false
	}

	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d AsyncC2LPeakGuessListReq: retCode error, ret:%d", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGuessList, smsg)
		return false
	}

	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LPeakGuessListReq: get snaps failed", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGuessList, smsg)
		return false
	}

	tmpSnaps := make(map[uint64]*cl.UserSnapshot)
	for _, snap := range snaps {
		tmpSnaps[snap.Id] = snap
	}

	for _, match := range smsg.Matches {
		if snap, exist := tmpSnaps[match.LeftFighter.Uid]; exist {
			match.LeftFighter.Snapshot = snap
		}
		if snap, exist := tmpSnaps[match.RightFighter.Uid]; exist {
			match.RightFighter.Snapshot = snap
		}
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakGuessList, smsg)
	return true
}

type C2LPeakResultCommand struct {
	base.Command
}

func (c *C2LPeakResultCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakResult{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("[FATAL] C2L_PeakResult Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("C2L_PeakResult: recv from cross:%+v", cmsg)
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[FATAL] C2L_PeakResult: recv cross rank award error, ret:%d", cmsg.Ret)
		return false
	}

	if cmsg.Round == 0 || cmsg.Time == 0 {
		l4g.Errorf("[FATAL] C2L_PeakResult: param err, round:%d, time:%d", cmsg.Round, cmsg.Time)
		return false
	}

	if len(cmsg.Scores) < int(goxml.PeakPlayerCount) {
		l4g.Errorf("C2L_PeakResult: param error, scoresCount:%d", len(cmsg.Scores))
		return false
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("[FATAL] C2L_PeakResult: manager not exist")
		return false
	}

	//检查当前轮次是否按结算顺序回来的数据
	if !manager.IsRightRound(cmsg.Round) {
		l4g.Errorf("C2L_PeakResult: repeat run, round:%d, fightTm:%d", cmsg.Round, cmsg.Time)
		return false
	}

	//选手发奖 - 轮次奖励
	if len(cmsg.RoundRanks) > 0 {
		roundRankUids := make(map[uint32][]uint64)
		lastRewardRank := goxml.GetData().PeakRankRewardInfoM.GetLastRank(goxml.PeakRewardTypePhase)
		for _, v := range cmsg.RoundRanks {
			if v.Rank > lastRewardRank {
				continue
			}

			if _, exist := roundRankUids[v.Rank]; !exist {
				roundRankUids[v.Rank] = make([]uint64, 0, 1)
			}
			roundRankUids[v.Rank] = append(roundRankUids[v.Rank], v.Uid)
		}
		if len(roundRankUids) > 0 {
			c.sendRankAward(roundRankUids, goxml.PeakRewardTypePhase, character.MailIDPeakPhase, cmsg.Time)
		}
	}

	//选手发奖 - 赛季奖励
	if goxml.IsLastRoundToSeason(manager.GetPhase(), cmsg.Round) {
		seasonRankUids := make(map[uint32][]uint64)
		lastRewardRank := goxml.GetData().PeakRankRewardInfoM.GetLastRank(goxml.PeakRewardTypeSeason)
		for _, v := range cmsg.SeasonRanks {
			if v.RealRank > lastRewardRank {
				continue
			}

			if _, exist := seasonRankUids[v.RealRank]; !exist {
				seasonRankUids[v.RealRank] = make([]uint64, 0, 1)
			}
			seasonRankUids[v.RealRank] = append(seasonRankUids[v.RealRank], v.Uid)
		}
		if len(seasonRankUids) > 0 {
			c.sendRankAward(seasonRankUids, goxml.PeakRewardTypeSeason, character.MailIDPeakSeason, cmsg.Time)
		}
	}

	//竞猜发奖
	if len(cmsg.GuessMatches) > 0 {
		manager.SendGuessAward(c.Srv, cmsg.Round, cmsg.GuessMatches)
	}

	//游戏服玩法数据更新
	manager.UpdateRoundFinish(cmsg.Round)

	//游戏服玩法数据更新
	manager.SetAllScore(cmsg.Scores)
	manager.SetPeakChanged(c.Srv)

	//跑马灯
	fid, _ := manager.GetFidAndRaisePSs()
	if cmsg.NewTop1 != nil && cmsg.NewTop1.Sid == c.Srv.ServerID() {
		reqUsers := []*cl.ServerUser{{Sid: cmsg.NewTop1.Sid, Uid: cmsg.NewTop1.Uid}}
		c.GetUserSnapshotsFromAll(reqUsers, &AsyncC2LPeakPushMsgReq{goxml.ChatPeakTop1Change}, fid)
	}
	if cmsg.PhaseTop1 != nil && cmsg.PhaseTop1.Sid == c.Srv.ServerID() {
		reqUsers := []*cl.ServerUser{{Sid: cmsg.PhaseTop1.Sid, Uid: cmsg.PhaseTop1.Uid}}
		c.GetUserSnapshotsFromAll(reqUsers, &AsyncC2LPeakPushMsgReq{goxml.ChatPeakPhaseTop1}, fid)
	}
	if cmsg.SeasonTop1 != nil && cmsg.SeasonTop1.Sid == c.Srv.ServerID() {
		reqUsers := []*cl.ServerUser{{Sid: cmsg.SeasonTop1.Sid, Uid: cmsg.SeasonTop1.Uid}}
		c.GetUserSnapshotsFromAll(reqUsers, &AsyncC2LPeakPushMsgReq{goxml.ChatPeakSeasonTop1}, fid)
	}

	//数据推送
	c.Srv.BroadcastCmdToClient(cl.ID_MSG_L2C_PeakUpdateTip, &cl.L2C_PeakUpdateTip{
		Ret: uint32(cret.RET_OK),
	})
	return true
}

// 发奖
// @param map[uint32][]uint64 rankUids 不同等级的玩家id列表
// @param uint32 rewardType 奖励类型 1-小周期奖励 2-赛季奖励
// @param uint32 mailID
// @param int64 发奖时间
func (c *C2LPeakResultCommand) sendRankAward(rankUids map[uint32][]uint64,
	rewardType, mailID uint32, awardTm int64) {
	for rank, uids := range rankUids {
		awardData := goxml.GetData().PeakRankRewardInfoM.GetReward(rewardType, rank, awardTm)
		if len(awardData) == 0 {
			l4g.Errorf("peak.sendRankAward: no awardData, rewardType:%d, rank:%d, uids:%v",
				rewardType, rank, uids)
			continue
		}

		character.PeakMail(c.Srv, mailID, []string{strconv.Itoa(int(rank))}, awardData, uids)
		l4g.Infof("peak.sendRankAward: finished, rewardType:%d, rank:%d, uids:%v",
			rewardType, rank, uids)
	}
}

type AsyncC2LPeakPushMsgReq struct {
	msgType uint32
}

func (ar *AsyncC2LPeakPushMsgReq) Resp(srv command.Servicer, args *character.Args,
	retCode uint32, dbData interface{}) bool {
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("AsyncC2LPeakPushMsgReq: retCode error, ret:%d", retCode)
		return false
	}

	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("AsyncC2LPeakPushMsgReq: get snaps failed.")
		return false
	}

	if len(snaps) != 1 {
		l4g.Errorf("AsyncC2LPeakPushMsgReq: snapshot count err, count:%d", len(snaps))
		return false
	}
	snap := snaps[0]

	chatUserHead := &cl.ChatUserHead{
		Id:       strconv.FormatUint(snap.Id, 10),
		Name:     snap.Name,
		BaseId:   snap.BaseId,
		Level:    snap.Level,
		Power:    strconv.FormatUint(uint64(snap.Power), 10),
		Vip:      snap.Vip,
		ServerId: strconv.FormatUint(snap.Sid, 10),
	}
	message := character.NewMsg(character.Id(srv), character.Head(chatUserHead), character.Type(ar.msgType))
	character.ChatSendMsgToPlatform(srv, snap.Id, message)
	return true
}

type C2LPeakRankListCommand struct {
	base.UserCommand
}

func (c *C2LPeakRankListCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPeakRankListCommand) Error(smsg *cl.L2C_PeakRankList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakRankList, smsg)
	return false
}

func (c *C2LPeakRankListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakRankList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakRankList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("user:%d C2L_PeakRankList: recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_PeakRankList{
		Ret:       uint32(cret.RET_OK),
		Type:      cmsg.Type,
		StartRank: cmsg.StartRank,
		EndRank:   cmsg.EndRank,
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PeakRankList: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if len(cmsg.List) == 0 {
		l4g.Infof("user: %d C2L_PeakRankList: no data, type:%d", c.Msg.UID, cmsg.Type)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakRankList, smsg)
		return c.ResultOK(smsg.Ret)
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_PeakRankList: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	user := manager.GetUser(c.Msg.UID)
	if user == nil {
		l4g.Errorf("user: %d C2L_PeakRankList: no user", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_PEAK_NO_USER))
	}

	smsg.List = cmsg.List
	serverUsers := makeServerUsersByResult(cmsg.List)
	if cmsg.Self != nil {
		smsg.Self = cmsg.Self
		serverUsers = append(serverUsers, &cl.ServerUser{
			Sid: cmsg.Self.Sid,
			Uid: cmsg.Self.Uid,
		})
	}
	c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LPeakRankListReq{smsg, len(serverUsers)}, 0)
	return c.ResultOK(smsg.Ret)
}

func makeServerUsersByResult(rankList []*cl.PeakResult) []*cl.ServerUser {
	serverUsers := make([]*cl.ServerUser, 0, len(rankList))
	for _, v := range rankList {
		serverUsers = append(serverUsers, &cl.ServerUser{
			Sid: v.Sid,
			Uid: v.Uid,
		})
	}
	return serverUsers
}

type AsyncC2LPeakRankListReq struct {
	smsg  *cl.L2C_PeakRankList
	count int
}

func (ar *AsyncC2LPeakRankListReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LPeakRankListReq: get snaps failed", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakRankList, smsg)
		return false
	}

	tmpSnaps := make(map[uint64]*cl.UserSnapshot)
	for _, snap := range snaps {
		tmpSnaps[snap.Id] = snap
	}

	for _, v := range smsg.List {
		if snap, exist := tmpSnaps[v.Uid]; exist {
			v.Snapshot = snap
		}
	}

	if smsg.Self != nil {
		if snap, exist := tmpSnaps[smsg.Self.Uid]; exist {
			smsg.Self.Snapshot = snap
		}
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_PeakRankList, smsg)
	return true
}

type C2LPeakAddGuessCountCommand struct {
	base.UserCommand
}

func (c *C2LPeakAddGuessCountCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPeakAddGuessCountCommand) Error(smsg *cl.L2C_PeakDoGuess, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakDoGuess, smsg)
	return false
}

func (c *C2LPeakAddGuessCountCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakAddGuessCount{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakAddGuessCount Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_PeakAddGuessCount user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_PeakDoGuess{
		Ret:     uint32(cret.RET_OK),
		MatchId: cmsg.MatchId,
		Count:   cmsg.Count,
		Uid:     cmsg.Uid,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PeakAddGuessCount: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_PeakAddGuessCount: manager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	user := manager.GetUser(c.Msg.UID)
	if user == nil {
		l4g.Errorf("user: %d C2L_PeakAddGuessCount: no user", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_PEAK_NO_USER))
	}

	usedCount := user.CalcUsedGuessCount(cmsg.Round)
	if usedCount+cmsg.Count > goxml.GetData().PeakConfigInfoM.GetGuessCount(cmsg.Round) {
		l4g.Errorf("user: %d C2L_PeakAddGuessCount: no guess count, curRound:%d, usedCount:%d, requestCount:%d",
			c.Msg.UID, cmsg.Round, usedCount, cmsg.Count)
		return c.Error(smsg, uint32(cret.RET_PEAK_NO_GUESS_COUNT))
	}

	costs := goxml.GetData().PeakConfigInfoM.GetGuessCost(cmsg.Count)
	smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_PEAK_GUESS), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PeakAddGuessCount: consume err", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	guessData := &cl.PeakUserGuess{
		MatchId:   cmsg.MatchId,
		Group:     cmsg.Group,
		WinnerUid: cmsg.Uid,
		Count:     cmsg.Count,
		Round:     cmsg.Round,
	}
	manager.DoGuess(user, guessData)
	c.User.FireCommonEvent(c.Srv.EventM(), event.AePeakGuessCountToX, 1)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakDoGuess, smsg)
	c.User.LogPeakDoGuess(c.Srv, guessData)
	return c.ResultOK(smsg.Ret)
}

type C2LPeakFightCommand struct {
	base.Command
}

func (c *C2LPeakFightCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakFight Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	l4g.Debug("C2L_PeakFight user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	users := []*cl.ServerUser{{
		Sid: cmsg.LeftSid,
		Uid: cmsg.LeftUid,
	}, {
		Sid: cmsg.RightSid,
		Uid: cmsg.RightUid,
	}}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("[FATAL] C2L_PeakFight: manager not exist")
		return false
	}
	fid, raisePSs := manager.GetFidAndRaisePSs()

	req := &AsyncC2LPeakFightReq{
		matchID:   cmsg.MatchId,
		fightTime: cmsg.FightTime,
		leftUid:   cmsg.LeftUid,
		rightUid:  cmsg.RightUid,
		leftPos:   cmsg.LeftPos,
		rightPos:  cmsg.RightPos,
		fid:       fid,
		raisePSs:  raisePSs,
	}
	c.GetUserBattleDataFromAll(users, req, fid)
	return true
}

type AsyncC2LPeakFightReq struct {
	matchID           uint32
	fightTime         int64
	leftUid, rightUid uint64
	leftPos, rightPos uint32
	fid               uint32
	raisePSs          []uint64
}

//nolint:funlen
func (ar *AsyncC2LPeakFightReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	msg := &l2c.L2C_PeakFight{
		Ret:       uint32(cret.RET_OK),
		MatchId:   ar.matchID,
		FightTime: ar.fightTime,
	}

	if retCode != uint32(cret.RET_OK) {
		msg.Ret = retCode
		l4g.Errorf("AsyncC2LPeakFightReq: retCode error, ret:%d ", msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}

	snaps, ok := dbData.([]*db.UserBattleData)
	if !ok {
		msg.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("AsyncC2LPeakFightReq: UserBattleData error, ret:%d ", msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}

	if len(snaps) != 2 { //nolint:mnd
		msg.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("AsyncC2LPeakFightReq: UserBattleData not enough, ret:%d ", msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}

	snapm := make(map[uint64]*db.UserBattleData, len(snaps))
	for _, v := range snaps {
		if v == nil {
			msg.Ret = uint32(cret.RET_ERROR)
			l4g.Errorf("AsyncC2LPeakFightReq: snap is nil, ret:%d ", msg.Ret)
			srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
			return false
		}
		snapm[v.User.Id] = v
	}

	leftFighter := snapm[ar.leftUid]
	if leftFighter == nil {
		msg.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("AsyncC2LPeakFightReq: no leftFighter, uid:%d, ret:%d ", ar.leftUid, msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}
	leftUser := character.GetUserFromUserBattleData(srv, leftFighter)
	if leftUser == nil {
		msg.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("AsyncC2LPeakFightReq: no leftUser, uid:%d, ret:%d ", ar.leftUid, msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}

	rightFighter := snapm[ar.rightUid]
	if rightFighter == nil {
		msg.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("AsyncC2LPeakFightReq: no rightFighter, uid:%d, ret:%d ", ar.rightUid, msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}
	rightUser := character.GetUserFromUserBattleData(srv, rightFighter)
	if rightUser == nil {
		msg.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("AsyncC2LPeakFightReq: no rightUser, uid:%d, ret:%d ", ar.rightUid, msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}

	// 战场buff效果
	var altRaisePS *battle.AltRaisePS
	if len(ar.raisePSs) > 0 {
		raisePSMap := make(map[uint32][]uint64)
		raisePSMap[0] = ar.raisePSs
		altRaisePS = battle.NewAltRaisePS()
		altRaisePS.AltAttack(raisePSMap)
		altRaisePS.AltDefense(raisePSMap)
	}

	win, reportID, attackRet, leftScore, rightScore := character.PeakFight(srv, leftUser,
		rightUser, ar.leftPos, ar.rightPos, ar.fid, altRaisePS)
	if attackRet != cret.RET_OK {
		msg.Ret = uint32(cret.RET_BATTLE_ERROR)
		l4g.Errorf("AsyncC2LPeakFightReq: battle failed, leftUid:%d, rightUid:%d, ret:%d ",
			ar.leftUid, ar.rightUid, msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)
		return false
	}

	msg.LeftSnapshot = leftUser.NewUserSnapshot(ar.fid)
	msg.RightSnapshot = rightUser.NewUserSnapshot(ar.fid)
	msg.LeftWin = win
	msg.ReportId = reportID
	msg.LeftScore = leftScore
	msg.RightScore = rightScore
	srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakFight, 0, msg)

	// 埋点记录当前期数
	var phase uint32
	manager, ok := srv.GetActivity(activity.Peak).(*apeak.Manager)
	if ok {
		phase = manager.GetPhase()
	}

	leftUser.LogPeakFight(srv, reportID, win, leftScore, rightScore,
		ar.matchID, phase, ar.fid, ar.rightUid)
	return true
}

type C2LPeakFighterDetailCommand struct {
	base.UserCommand
}

func (c *C2LPeakFighterDetailCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPeakFighterDetailCommand) Error(smsg *cl.L2C_PeakFighterDetail, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakFighterDetail, smsg)
	return false
}

func (c *C2LPeakFighterDetailCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakFighterDetail{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakFighterDetail Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_PeakFighterDetail user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_PeakFighterDetail{
		Ret: uint32(cret.RET_OK),
		Uid: cmsg.Uid,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PeakFighterDetail: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Matches = cmsg.Matches
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakFighterDetail, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LPeakPushStateCommand struct {
	base.Command
}

func (c *C2LPeakPushStateCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakPushState{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("[FATAL] C2L_PeakPushState Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("C2L_PeakPushState: recv from cross:%+v", cmsg)
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[FATAL] C2L_PeakPushState: recv cross rank award error, ret:%d", cmsg.Ret)
		return false
	}

	if cmsg.State == nil {
		l4g.Infof("C2L_PeakPushState: state is nil, peak not start")
		return false
	}

	if cmsg.State.Season == 0 || cmsg.State.Phase == 0 || cmsg.State.Round == 0 {
		l4g.Infof("C2L_PeakPushState: should do nothing, state:%+v", cmsg.State)
		return false
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("[FATAL] C2L_PeakPushState: manager not exist")
		return false
	}
	l4g.Debug("C2L_PeakPushState: paramState:%+v, curState:%+v", cmsg.State, manager.FlushState())

	//同步赛季数据
	if cmsg.State.Season != manager.GetSeason() {
		manager.SeasonReset(c.Srv, cmsg.State)
	}

	//同步小周期数据
	if cmsg.State.Phase != manager.GetPhase() && cmsg.State.PhaseState >= uint32(common.PEAK_PHASE_STATE_PPS_RUNNING) {
		//新周期更新选手数据
		c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakUpdatePlayer, 0, &l2c.L2C_PeakUpdatePlayer{
			Phase: cmsg.State.Phase,
		})
	}

	//同步轮次初始数据 - 游戏服round较跨服少1轮，且处于已完成状态，跨服处于初始化状态时，需同步状态
	if manager.IsNeedUpdateRoundToNextInitState(cmsg.State.Round, cmsg.State.RoundState) {
		manager.RoundReset(c.Srv, cmsg.State.Round)
	}

	//计算待结算轮次
	round := manager.GetRound()
	finishRounds := make([]uint32, 0, 1)
	for i := round; i <= cmsg.State.Round; i++ {
		if i == round && manager.GetRoundState() == uint32(common.PEAK_ROUND_STATE_PRS_END) {
			continue
		}

		if i < cmsg.State.Round || cmsg.State.RoundState == uint32(common.PEAK_ROUND_STATE_PRS_END) {
			finishRounds = append(finishRounds, i)
		}
	}

	//请求结算数据
	for _, finishRound := range finishRounds {
		c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakResult, 0, &l2c.L2C_PeakResult{
			Round: finishRound,
		})
	}
	return true
}

type C2LPeakUpdatePlayerCommand struct {
	base.Command
}

func (c *C2LPeakUpdatePlayerCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakUpdatePlayer{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("[FATAL] C2L_PeakUpdatePlayer Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("C2L_PeakUpdatePlayer: recv from cross:%+v", cmsg)
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[FATAL] C2L_PeakUpdatePlayer: recv cross player data error, ret:%d", cmsg.Ret)
		return false
	}

	if cmsg.State == nil {
		l4g.Errorf("[FATAL] C2L_PeakUpdatePlayer: state is nil")
		return false
	}

	if len(cmsg.Scores) == 0 {
		l4g.Errorf("C2L_PeakUpdatePlayer: no scores data, maybe new partition")
		return false
	}

	if len(cmsg.Scores) < int(goxml.PeakPlayerCount) {
		l4g.Errorf("[FATAL] C2L_PeakUpdatePlayer: param error, scoresCount:%d", len(cmsg.Scores))
		return false
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("[FATAL] C2L_PeakUpdatePlayer: manager not exist")
		return false
	}

	//游戏服玩法数据更新
	manager.SetAllScore(cmsg.Scores)

	//重置小周期数据
	manager.PhaseReset(cmsg.State, cmsg.Uids)
	manager.SetPeakChanged(c.Srv)

	//数据推送
	c.Srv.BroadcastCmdToClient(cl.ID_MSG_L2C_PeakUpdateTip, &cl.L2C_PeakUpdateTip{
		Ret: uint32(cret.RET_OK),
	})
	return true
}

type C2LPeakGetSnapshotCommand struct {
	base.Command
}

func (c *C2LPeakGetSnapshotCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakGetSnapshot{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakGetSnapshot Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	l4g.Debug("C2L_PeakGetSnapshot user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	if len(cmsg.Uids) == 0 {
		l4g.Errorf("C2L_PeakGetSnapshot no uids")
		return false
	}

	manager, ok := c.Srv.GetActivity(activity.Peak).(*apeak.Manager)
	if !ok {
		l4g.Errorf("[FATAL] C2L_PeakGetSnapshot: manager not exist")
		return false
	}
	fid, _ := manager.GetFidAndRaisePSs()

	c.GetLocalUserSnapshots(cmsg.Uids, &AsyncC2LPeakGetSnapshotReq{len(cmsg.Uids)}, fid)
	return true
}

type AsyncC2LPeakGetSnapshotReq struct {
	count int
}

func (ar *AsyncC2LPeakGetSnapshotReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	msg := &l2c.L2C_PeakGetSnapshot{
		Ret:       uint32(cret.RET_OK),
		Snapshots: make([]*cl.UserSnapshot, 0, ar.count),
	}

	if retCode != uint32(cret.RET_OK) {
		msg.Ret = retCode
		l4g.Errorf("AsyncC2LPeakGetSnapshotReq: retCode error, ret:%d ", msg.Ret)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakGetSnapshot, 0, msg)
		return false
	}

	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LPeakGetSnapshotReq: get snaps failed", args.UID)
		srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakGetSnapshot, 0, msg)
		return false
	}

	msg.Snapshots = append(msg.Snapshots, snaps...)
	srv.SendCmdToCross(l2c.ID_MSG_L2C_PeakGetSnapshot, 0, msg)
	return true
}

type C2LPeakRankFirstCommand struct {
	base.UserCommand
}

func (c *C2LPeakRankFirstCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakRankFirst{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakRankFirst Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_PeakRankFirst user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	as, exist := c.Srv.GetAsyncMessage(cmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", cmsg.ClientMsgId)
		return false
	}

	amsg := as.(*base.AsyncMessage)
	var data *cl.FirstRankValue
	if cmsg.Value == nil || cmsg.Value.Sid == 0 || cmsg.Value.Uid == 0 {
		ret := amsg.Work(cmsg.Ret, data)
		c.Srv.DeleteAsyncMessage(cmsg.ClientMsgId)
		return ret
	}

	c.GetUserSnapshotsFromAll([]*cl.ServerUser{{Sid: cmsg.Value.Sid, Uid: cmsg.Value.Uid}},
		&AsyncC2LPeakRankFirstReq{cmsg}, 0)
	return true
}

type AsyncC2LPeakRankFirstReq struct {
	C2LPeakRankFirst *l2c.C2L_PeakRankFirst
}

func (r *AsyncC2LPeakRankFirstReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {

	as, exist := srv.GetAsyncMessage(r.C2LPeakRankFirst.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", r.C2LPeakRankFirst.ClientMsgId)
		return false
	}

	defer srv.DeleteAsyncMessage(r.C2LPeakRankFirst.ClientMsgId)

	amsg := as.(*base.AsyncMessage)

	l4g.Debugf("user: %d AsyncC2LPeakRankFirstReq do callback from cmd:%d", args.UID, amsg.CallCmd)

	var data *cl.FirstRankValue

	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d AsyncC2LPeakRankFirstReq: get snaps return code error. code:%d targetSid:%d targetUid:%d",
			args.UID, retCode, r.C2LPeakRankFirst.Value.Sid, r.C2LPeakRankFirst.Value.Uid)
		amsg.Work(retCode, data)
		return false
	}

	if r.C2LPeakRankFirst == nil || r.C2LPeakRankFirst.Value == nil {
		amsg.Work(retCode, data)
		return false
	}

	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LPeakRankFirstReq: get snaps failed. targetSid:%d targetUid:%d",
			args.UID, r.C2LPeakRankFirst.Value.Sid, r.C2LPeakRankFirst.Value.Uid)
		amsg.Work(retCode, data)
		return false
	}

	for _, snap := range snaps {
		if snap.Id == r.C2LPeakRankFirst.Value.Uid {
			r.C2LPeakRankFirst.Value.Snapshot = snap
		}
	}

	if r.C2LPeakRankFirst.Value.Snapshot == nil {
		amsg.Work(retCode, data)
		return false
	}

	data = &cl.FirstRankValue{
		RankId: goxml.PeakRankID,
		RankValue: &cl.RankValue{
			Id:     r.C2LPeakRankFirst.Value.Uid,
			User:   r.C2LPeakRankFirst.Value.Snapshot,
			Value:  uint64(r.C2LPeakRankFirst.Value.Score),
			Param1: uint64(r.C2LPeakRankFirst.Value.Phase),
			Sid:    r.C2LPeakRankFirst.Value.Sid,
		},
	}

	ret := amsg.Work(retCode, data)
	return ret
}

type C2LPeakGetLastBattleReportCommand struct {
	base.UserCommand
}

func (c *C2LPeakGetLastBattleReportCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPeakGetLastBattleReportCommand) Error(smsg *cl.L2C_PeakGetLastBattleReport, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetLastBattleReport, smsg)
	return false
}

func (c *C2LPeakGetLastBattleReportCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_PeakGetLastBattleReport{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PeakGetLastBattleReport Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_PeakGetLastBattleReport user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_PeakGetLastBattleReport{
		Ret: uint32(cret.RET_OK),
		Uid: cmsg.Uid,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_PeakGetLastBattleReport: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.ReportId = cmsg.ReportId
	smsg.IsAttacker = cmsg.IsAttacker

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PeakGetLastBattleReport, smsg)
	return c.ResultOK(smsg.Ret)
}
