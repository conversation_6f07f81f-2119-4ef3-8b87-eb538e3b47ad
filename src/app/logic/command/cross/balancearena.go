package cross

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/balancearena"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitBalanceArena(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaGetData), &CS2LBalanceArenaGetDataCommand{}, state)       //获取公平竞技场数据
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaGetMatches), &CS2LBalanceArenaGetMatchesCommand{}, state) //获取公平竞技场比赛数据
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaGetSta), &CS2LBalanceArenaGetStaCommand{}, state)         //获取公平竞技场状态
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaCardChoose), &CS2LBalanceArenaCardChooseCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaCardCustomize), &CS2LBalanceArenaCardCustomizeCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaTeamUp), &CS2LBalanceArenaTeamUpCommand{}, state) //组队
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaFight), &CS2LBalanceArenaFightCommand{}, state)   // 战斗
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaRankSettle), &CS2LBalanceArenaRankSettleCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaReward), &CS2LBalanceArenaRewardCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaGetRankList), &CS2LBalanceArenaGetRankListCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_BalanceArenaGetUserMatches), &CS2LBalanceArenaGetUserMatchesCommand{}, state)
}

type CS2LBalanceArenaGetDataCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaGetDataCommand) Error(smsg *cl.L2C_BalanceArenaGetData, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetData, smsg)
	return false
}

func (c *CS2LBalanceArenaGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("balanceArena.CS2L_BalanceArenaGetData user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_BalanceArenaGetData{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetData user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if cmsg.State == nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetData user:%d state is nil", c.Msg.UID)
		return c.Error(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	manager, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.CS2L_BalanceArenaGetData no manager.", c.Msg.UID)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.State = manager.GetCrossState() // 使用本地状态
	smsg.User = cmsg.GetUser()
	smsg.BigGroupId = cmsg.GetBigGroup()
	smsg.SmallGroupId = cmsg.GetSmallGroup()
	smsg.EliminationGroupId = cmsg.GetEliminationGroup()
	smsg.EliminationReward = cmsg.GetEliminationReward()
	smsg.Rank = cmsg.RankData

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetData, smsg)
	return true
}

type CS2LBalanceArenaGetMatchesCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaGetMatchesCommand) Error(smsg *cl.L2C_BalanceArenaGetMatches, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetMatches, smsg)
	return false
}

func (c *CS2LBalanceArenaGetMatchesCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaGetMatches{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetMatches Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("balanceArena.CS2L_BalanceArenaGetMatches user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_BalanceArenaGetMatches{
		Ret:         uint32(ret.RET_OK),
		PhaseType:   cmsg.PhaseType,
		BattleGroup: cmsg.BattleGroup,
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetMatches user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}
	smsg.Matches = cmsg.Matches
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetMatches, smsg)
	return true
}

type CS2LBalanceArenaGetStaCommand struct {
	base.Command
}

func (c *CS2LBalanceArenaGetStaCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaGetSta{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetSta Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("balanceArena.CS2L_BalanceArenaGetSta user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	balanceArenaM, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.CS2L_BalanceArenaGetSta: get manager failed.", c.Msg.UID)
		return false
	}

	balanceArenaM.SetCrossState(cmsg.Sta)
	c.Srv.BroadcastCmdToClient(cl.ID_MSG_L2C_BalanceArenaPushState, &cl.L2C_BalanceArenaPushState{
		Ret: uint32(ret.RET_OK),
		Sta: cmsg.Sta,
	})

	return true
}

type CS2LBalanceArenaCardChooseCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaCardChooseCommand) Error(smsg *cl.L2C_BalanceArenaCardChoose, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardChoose, smsg)
	return false
}

func (c *CS2LBalanceArenaCardChooseCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaCardChoose{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaCardChoose Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("balanceArena.CS2L_BalanceArenaCardChoose user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_BalanceArenaCardChoose{
		Ret:                uint32(ret.RET_OK),
		OpType:             cmsg.OpType,
		CardIds:            cmsg.CardIds,
		DrawRound:          cmsg.DrawRound,
		NextHeroGroups:     cmsg.NextHeroGroups,
		NextArtifactGroups: cmsg.NextArtifactGroups,
		AvailableHeroes:    cmsg.AvailableHeroes,
		AvailableArtifacts: cmsg.AvailableArtifacts,
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaCardChoose user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardChoose, smsg)
	return true
}

type CS2LBalanceArenaCardCustomizeCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaCardCustomizeCommand) Error(smsg *cl.L2C_BalanceArenaCardCustomize, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardCustomize, smsg)
	return false
}

func (c *CS2LBalanceArenaCardCustomizeCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaCardCustomize{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaCardCustomize Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("balanceArena.CS2L_BalanceArenaCardCustomize user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_BalanceArenaCardCustomize{
		Ret:                uint32(ret.RET_OK),
		CardType:           cmsg.CardType,
		SysId:              cmsg.SysId,
		Hid:                cmsg.Hid,
		AvailableHeroes:    cmsg.AvailableHeroes,
		AvailableArtifacts: cmsg.AvailableArtifacts,
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaCardCustomize user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaCardCustomize, smsg)
	return true
}

type CS2LBalanceArenaFightCommand struct {
	base.Command
}

func (c *CS2LBalanceArenaFightCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_BalanceArenaFight Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	l4g.Debug("CS2L_BalanceArenaFight recv from cross:%+v", cmsg)
	rspMsg := &l2c.L2CS_BalanceArenaFight{
		Ret:        uint32(ret.RET_OK),
		MatchId:    cmsg.MatchId,
		AttackUid:  cmsg.AttackUser.Id,
		DefenseUid: cmsg.DefenseUser.Id,
	}
	battleReport, retCode := character.BalanceArenaFight(c.Srv, cmsg)
	if retCode != ret.RET_OK {
		l4g.Errorf("CS2L_BalanceArenaFight:matchID:%d bRet:%d", cmsg.MatchId, retCode)
		rspMsg.AttackWin = true
		rspMsg.AttackScore = goxml.BalanceArenaTeamNum
	} else {
		rspMsg.ReportId = battleReport.Id
		rspMsg.AttackWin = battleReport.Win
		for _, report := range battleReport.Reports {
			if report.Win {
				rspMsg.AttackScore++
			} else {
				rspMsg.DefenseScore++
			}
		}
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaFight, 0, rspMsg)
	return true
}

type CS2LBalanceArenaTeamUpCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaTeamUpCommand) Error(smsg *cl.L2C_BalanceArenaTeamUp, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaTeamUp, smsg)
	return false
}

func (c *CS2LBalanceArenaTeamUpCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaTeamUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaTeamUp Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("balanceArena.CS2L_BalanceArenaTeamUp user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_BalanceArenaTeamUp{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaTeamUp user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Teams = cmsg.Teams
	smsg.SignSuccess = true

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaTeamUp, smsg)
	return true
}

type CS2LBalanceArenaRankSettleCommand struct {
	base.Command
}

func (c *CS2LBalanceArenaRankSettleCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaRankSettle{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_BalanceArenaRankSettle Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	l4g.Debug("CS2L_BalanceArenaRankSettle recv from cross:%+v", cmsg)

	balanceArenaM, ok := c.Srv.GetActivity(activity.BalanceArena).(*balancearena.Manager)
	if !ok {
		l4g.Errorf("user: %d balanceArena.CS2L_BalanceArenaRankSettle: get manager failed.", c.Msg.UID)
		return false
	}
	rspMsg := &l2c.L2CS_BalanceArenaRankSettle{
		State: cmsg.State.Clone(),
	}
	balanceArenaM.SettleReward(c.Srv, cmsg)
	c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_BalanceArenaRankSettle, 0, rspMsg)
	return true
}

type CS2LBalanceArenaGetRankListCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaGetRankListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaGetRankList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetRankList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("balanceArena.CS2L_BalanceArenaGetRankList user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetRankList, cmsg.Rsp)
	return true
}

type CS2LBalanceArenaRewardCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaRewardCommand) Error(smsg *cl.L2C_BalanceArenaReward, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaReward, smsg)
	return false
}

func (c *CS2LBalanceArenaRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaReward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	l4g.Debug("balanceArena.CS2L_BalanceArenaReward user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_BalanceArenaReward{
		Ret: uint32(ret.RET_OK),
	}
	if cmsg.Ret != uint32(ret.RET_OK) {
		return c.Error(smsg, cmsg.Ret)
	}
	dropAwards, dropFlag := c.User.Drop().DoDrop(c.Srv.Rand(), goxml.GetData().BalanceArenaConfigInfoM.EliminationDrop)
	if !dropFlag {
		l4g.Errorf("user: %d, Drop award is nil.", c.User.ID())
		return c.Error(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	smsg.Awards = dropAwards
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaReward, smsg)
	return true
}

type CS2LBalanceArenaGetUserMatchesCommand struct {
	base.UserCommand
}

func (c *CS2LBalanceArenaGetUserMatchesCommand) Error(smsg *cl.L2C_BalanceArenaGetUserMatches, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetUserMatches, smsg)
	return false
}

func (c *CS2LBalanceArenaGetUserMatchesCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_BalanceArenaGetUserMatches{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetUserMatches Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("balanceArena.CS2L_BalanceArenaGetUserMatches user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_BalanceArenaGetUserMatches{
		Ret: uint32(ret.RET_OK),
		Uid: cmsg.GetUid(),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("balanceArena.CS2L_BalanceArenaGetUserMatches user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}
	smsg.Matches = cmsg.Matches
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_BalanceArenaGetUserMatches, smsg)
	return true
}
