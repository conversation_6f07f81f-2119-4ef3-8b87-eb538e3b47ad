package cross

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/seasoncompliance"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitSeasonCompliance(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonComplianceRankGetList), &CS2LSeasonComplianceRankGetListCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonComplianceRankAward), &CS2LSeasonComplianceRankAwardCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_SeasonComplianceGetData), &CS2LSeasonComplianceGetDataCommand{}, state)
}

type CS2LSeasonComplianceGetDataCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonComplianceGetDataCommand) Error(smsg *cl.L2C_SeasonComplianceGetData, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonComplianceGetData, smsg)
	return false
}

func (c *CS2LSeasonComplianceGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonComplianceGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_SeasonComplianceGetData Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2L_SeasonComplianceGetData user:%d rev from cross:%+v", c.Msg.UID, cmsg)
	clientMsg := &cl.L2C_SeasonComplianceGetData{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("CS2L_SeasonComplianceGetData user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(clientMsg, cmsg.Ret)
	}

	seasonComplianceM, ok := c.Srv.GetActivity(activity.SeasonCompliance).(*seasoncompliance.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_SeasonComplianceGetData failed, seasonComplianceM not exist", c.Msg.UID)
		return c.Error(clientMsg, uint32(cret.RET_ERROR))
	}

	cruStage := seasonComplianceM.GetCurStage()
	stagID, exist := goxml.StageRankSeasonCompliance[cmsg.RankId]
	if !exist || cruStage == nil {
		l4g.Errorf("user: %d CS2L_SeasonComplianceGetData rankID:%d to StageID:%d failed", c.Msg.UID, cmsg.RankId, stagID)
		return c.Error(clientMsg, uint32(cret.RET_ERROR))
	}

	seasonCompliance := c.User.SeasonComplianceM().GetSeasonCompliance(cruStage.Phase, stagID, c.Srv, true)
	totalLessPoint := cmsg.GetTotalList().GetLessPoint()
	if cmsg.GetTotalList().GetSelfRank() == 0 {
		totalScore := c.User.SeasonComplianceM().GetAllStageTotalScore(cruStage.Phase)
		lastPoint := cmsg.GetTotalList().GetLastPoint()
		if lastPoint >= totalScore {
			totalLessPoint = lastPoint - totalScore
		}
	}

	lessPoint := cmsg.GetList().GetLessPoint()
	if cmsg.GetList().GetSelfRank() == 0 {
		var stageID uint32
		stageID, exist = goxml.StageRankSeasonCompliance[cmsg.RankId]
		if exist && stageID != goxml.SeasonComplianceTotalStage {
			totalScore := seasonCompliance.GetTotalScore()
			lastPoint := cmsg.GetList().GetLastPoint()
			if lastPoint >= totalScore {
				lessPoint = lastPoint - totalScore
			}
		}
	}

	clientMsg.Top3 = cmsg.GetTotalList().GetList()
	clientMsg.Score = seasonCompliance.GetTotalScore()
	clientMsg.TotalScore = c.User.SeasonComplianceM().GetAllStageTotalScore(cruStage.Phase)
	clientMsg.Recv = seasonCompliance.GetAward2Client()
	clientMsg.LessPoint = lessPoint
	clientMsg.SourcePoints = seasonCompliance.GetSource2Client()
	clientMsg.Stage = stagID
	clientMsg.SelfRank = cmsg.GetList().GetSelfRank()
	clientMsg.TotalLessPoint = totalLessPoint
	clientMsg.TotalSelfRank = cmsg.GetTotalList().GetSelfRank()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonComplianceGetData, clientMsg)
	return true
}

type CS2LSeasonComplianceRankGetListCommand struct {
	base.UserCommand
}

func (c *CS2LSeasonComplianceRankGetListCommand) Error(smsg *cl.L2C_SeasonComplianceList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonComplianceList, smsg)
	return false
}

func (c *CS2LSeasonComplianceRankGetListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonComplianceRankGetList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2LSeasonComplianceRankGetList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("CS2LSeasonComplianceRankGetList user:%d rev from cross:%+v", c.Msg.UID, cmsg)
	clientMsg := &cl.L2C_SeasonComplianceList{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("CS2LSeasonComplianceRankGetList user:%d recv cross info error,ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(clientMsg, cmsg.Ret)
	}

	clientMsg.Lists = cmsg.ComplianceList
	if cmsg.EndRank == 3 {
		clientMsg.Top3 = true
	}
	stageID, exist := goxml.StageRankSeasonCompliance[cmsg.RankId]
	if exist {
		clientMsg.StageId = stageID
		if clientMsg.Lists.SelfRank == 0 {
			clientMsg.Lists.SelfValue = c.buildSelfRankInfo(stageID)
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonComplianceList, clientMsg)
	return true
}

func (c *CS2LSeasonComplianceRankGetListCommand) buildSelfRankInfo(stageID uint32) *cl.RankValue {
	var ret *cl.RankValue
	seasonComplianceM, ok := c.Srv.GetActivity(activity.SeasonCompliance).(*seasoncompliance.Manager)
	if !ok {
		return ret
	}

	cruStage := seasonComplianceM.GetCurStage()
	var rankScore uint32
	if cruStage != nil {
		if stageID == goxml.SeasonComplianceTotalStage {
			rankScore = c.User.SeasonComplianceM().GetAllStageTotalScore(cruStage.Phase)
		} else {
			seasonCompliance := c.User.SeasonComplianceM().GetSeasonCompliance(cruStage.Phase, stageID, c.Srv, false)
			if seasonCompliance != nil {
				rankScore = seasonCompliance.GetTotalScore()
			}
		}
	}

	ret = &cl.RankValue{}
	ret.Id = c.User.ID()
	ret.Sid = c.User.ServerID()
	ret.User = c.User.NewUserSnapshot(0)
	ret.Value = uint64(rankScore)
	return ret
}

type CS2LSeasonComplianceRankAwardCommand struct {
	base.Command
}

func (c *CS2LSeasonComplianceRankAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_SeasonComplianceRankAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2LSeasonComplianceRankAwardCommand Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2LSeasonComplianceRankAwardCommand user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[CS2LSeasonComplianceRankAwardCommand] user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	scManager, ok := c.Srv.GetActivity(activity.SeasonCompliance).(*seasoncompliance.Manager)
	if !ok {
		l4g.Errorf("CS2LSeasonComplianceRankAwardCommand manager not exist.")
		return false
	}

	if !scManager.CanAward(cmsg.Stage) {
		l4g.Errorf("CS2LSeasonComplianceRankAwardCommand stage:%+v award failed", cmsg.Stage)
		return false
	}

	phaseInfo := goxml.GetData().SeasonComplianceInfoM.GetCurPhaseInfo(cmsg.Stage.Phase)
	if phaseInfo == nil {
		l4g.Errorf("CS2LSeasonComplianceRankAwardCommand get cur phase:%d info failed", cmsg.Stage.Phase)
		return false
	}

	if cmsg.Stage.Stage == 0 || cmsg.Stage.Stage > uint32(len(phaseInfo.StageInfos)) {
		l4g.Errorf("CS2LSeasonComplianceRankAwardCommand get stage failed")
		return false
	}

	if len(cmsg.RankData) > 0 {
		rankRewardUser := make(map[*goxml.SeasonComplianceRankRewardInfo][]uint64)
		rankReward := phaseInfo.StageInfos[int(cmsg.Stage.Stage-1)].StageRankReward
		stagType := phaseInfo.StageInfos[int(cmsg.Stage.Stage-1)].StageType
		for _, v := range cmsg.RankData {
			rewardInfo := goxml.GetData().SeasonComplianceRankRewardInfoM.GetRewardInfo(rankReward, uint32(v.Param1))
			if rewardInfo == nil {
				continue
			}
			rankRewardUser[rewardInfo] = append(rankRewardUser[rewardInfo], uint64(v.User.Id))
		}

		if len(rankRewardUser) > 0 {
			mailInfo, exist := goxml.SeasonComplianceMail[stagType]
			if exist {
				c.sendRankAward(rankRewardUser, mailInfo.RankMail)
			}
		}
	}

	if len(cmsg.TotalRankData) > 0 {
		totalRankRewardUser := make(map[*goxml.SeasonComplianceRankRewardInfo][]uint64)
		for _, v := range cmsg.TotalRankData {
			rewardInfo := goxml.GetData().SeasonComplianceRankRewardInfoM.GetRewardInfo(phaseInfo.TotalRankReward, uint32(v.Param1))
			if rewardInfo == nil {
				continue
			}
			totalRankRewardUser[rewardInfo] = append(totalRankRewardUser[rewardInfo], uint64(v.User.Id))
		}
		if len(totalRankRewardUser) > 0 {
			c.sendRankAward(totalRankRewardUser, goxml.MailIDSeasonComplianceTotalRankReward)
		}
	}

	scManager.SetAward(cmsg.Stage, c.Srv)

	return true
}

func (c *CS2LSeasonComplianceRankAwardCommand) sendRankAward(rank map[*goxml.SeasonComplianceRankRewardInfo][]uint64, mailID uint32) {
	for info, userIDS := range rank {
		if info == nil {
			continue
		}
		character.SeasonComplianceMail(c.Srv, mailID, []string{strconv.FormatUint(uint64(info.RankMax), 10), strconv.FormatUint(uint64(info.RankMin), 10)}, info.ClRes, userIDS)
		l4g.Infof("SeasonComplianceMail: finished, info:%+v, userIDS:%+v", info, userIDS)
	}
}
