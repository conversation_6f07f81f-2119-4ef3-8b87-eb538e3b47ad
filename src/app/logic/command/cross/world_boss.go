package cross

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/worldboss"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"strconv"

	"gitlab.qdream.com/kit/sea/time"

	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"

	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitWorldBoss(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_C2L_WorldBossMatchRoom), &C2LWorldBossMatchRoomCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_WorldBossRoomInfo), &C2LWorldBossRoomInfoCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_WorldBossGetRoomLog), &C2LWorldBossGetRoomLogCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_WorldBossRank), &C2LWorldBossRankCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_WorldBossSyncFightData), &C2LWorldBossSyncFightDataCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_WorldBossSettle), &C2LWorldBossSettleCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_C2L_WorldBossGetData), &C2LWorldBossGetDataCommand{}, state)
}

type C2LWorldBossMatchRoomCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossMatchRoomCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossMatchRoomCommand) Error(msg *cl.L2C_WorldBossSelectLevel, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossSelectLevel, msg)
	return false
}

func (c *C2LWorldBossMatchRoomCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WorldBossMatchRoom{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossMatchRoom: unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d C2L_WorldBossMatchRoom:. recv from logic:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossSelectLevel{
		Ret:   uint32(cret.RET_OK),
		Level: cmsg.Level,
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossMatchRoom: cross data error. ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}
	if cmsg.WUniqueId == 0 || cmsg.RoomUniqueId == 0 || cmsg.RoomSysId == 0 {
		l4g.Errorf("user: %d C2L_WorldBossMatchRoom: cross init data failed. id:%d roodID:%d", c.Msg.UID, cmsg.WUniqueId, cmsg.RoomUniqueId)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_CROSS_INIT_DATA_FAILED))
	}
	worldBoss := c.User.WorldBoss()
	if worldBoss.GetWorldBossSysID() != cmsg.WSysId {
		l4g.Errorf("user: %d C2L_WorldBossMatchRoom: boss sysID not same. id:%d roodID:%d", c.Msg.UID, worldBoss.GetWorldBossSysID(), cmsg.WSysId)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	worldBossM, ok := c.Srv.GetActivity(activity.WorldBoss).(*worldboss.Manager)
	if ok {
		worldBossM.Join(c.Srv, cmsg.WSysId)
	}

	worldBoss.InitWorldBoss(cmsg)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossSelectLevel, smsg)
	c.User.LogWorldBossSelectLevel(c.Srv, worldBoss.GetWorldBossData())

	return c.ResultOK(cmsg.Ret)
}

func newServerUser(users []*cl.RankValue) []*cl.ServerUser {
	retUsers := make([]*cl.ServerUser, 0, len(users))
	for _, u := range users {
		if u == nil {
			continue
		}
		serverUser := &cl.ServerUser{
			Uid: u.Id,
			Sid: u.Sid,
		}
		retUsers = append(retUsers, serverUser)
	}
	return retUsers
}

type C2LWorldBossRoomInfoCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossRoomInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossRoomInfoCommand) Error(msg *cl.L2C_WorldBossRoomInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRoomInfo, msg)
	return false
}

func (c *C2LWorldBossRoomInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WorldBossRoomInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossRoomInfo unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d C2L_WorldBossRoomInfo. recv from logic:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossRoomInfo{
		Ret:  uint32(cret.RET_OK),
		Data: c.User.WorldBoss().FlushData(),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossRoomInfo: cross data error. ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	if len(cmsg.Logs) == 0 {
		l4g.Errorf("user: %d C2L_WorldBossRoomInfo: from cross get room log data failed.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_WORLD_BOSS_CROSS_GET_ROOM_LOG_FAILED))
	}

	smsg.RoomTop = cmsg.RoomTop
	smsg.List = cmsg.List
	smsg.Logs = cmsg.Logs
	smsg.Users = cmsg.Users
	smsg.Data = c.User.WorldBoss().FlushData()
	smsg.PartitionRank = cmsg.PartitionRank

	serverUsers := make([]*cl.ServerUser, 0, len(smsg.Users)+len(smsg.List)+1)
	serverUsers = append(serverUsers, newServerUser(smsg.Users)...)
	serverUsers = append(serverUsers, newServerUser(smsg.List)...)
	if smsg.RoomTop != nil {
		serverUsers = append(serverUsers, newServerUser([]*cl.RankValue{smsg.RoomTop})...)
	}
	if len(serverUsers) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRoomInfo, smsg)
	} else {
		c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LWorldBossRoomInfoReq{smsg}, 0)
	}

	return c.ResultOK(cmsg.Ret)
}

type AsyncC2LWorldBossRoomInfoReq struct {
	smsg *cl.L2C_WorldBossRoomInfo
}

func (ar *AsyncC2LWorldBossRoomInfoReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LWorldBossRoomInfoReq: get snaps failed.", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRoomInfo, smsg)
		return false
	}

	tmpSnaps := make(map[uint64]*cl.UserSnapshot)
	for _, snap := range snaps {
		tmpSnaps[snap.Id] = snap
	}
	if smsg.RoomTop != nil {
		if snap, exist := tmpSnaps[smsg.RoomTop.Id]; exist {
			smsg.RoomTop.User = snap
		}
	}

	for _, l := range smsg.List {
		if snap, exist := tmpSnaps[l.Id]; exist {
			l.User = snap
		}
	}
	for _, u := range smsg.Users {
		if snap, exist := tmpSnaps[u.Id]; exist {
			u.User = snap
		}
	}
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRoomInfo, smsg)

	return true
}

type C2LWorldBossRankCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossRankCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossRankCommand) Error(msg *cl.L2C_WorldBossRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRank, msg)
	return false
}

func (c *C2LWorldBossRankCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WorldBossRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossRank unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d C2L_WorldBossRank. recv from logic:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossRank{
		Ret:       uint32(cret.RET_OK),
		Type:      cmsg.Type,
		Level:     cmsg.Level,
		StartRank: cmsg.StartRank,
		EndRank:   cmsg.EndRank,
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossRank: cross data error. ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.List = cmsg.List
	smsg.SelfRank = cmsg.SelfRank
	smsg.SelfValue = cmsg.SelfValue

	serverUsers := make([]*cl.ServerUser, 0, len(smsg.List)+1)
	serverUsers = append(serverUsers, newServerUser(smsg.List)...)
	if smsg.SelfValue != nil {
		serverUsers = append(serverUsers, newServerUser([]*cl.RankValue{smsg.SelfValue})...)
	}
	if len(serverUsers) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRank, smsg)
	} else {
		c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LWorldBossRankReq{smsg}, 0)
	}

	return c.ResultOK(cmsg.Ret)
}

type AsyncC2LWorldBossRankReq struct {
	smsg *cl.L2C_WorldBossRank
}

func (ar *AsyncC2LWorldBossRankReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LWorldBossRankReq: get snaps failed.", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRank, smsg)
		return false
	}

	tmpSnaps := make(map[uint64]*cl.UserSnapshot)
	for _, snap := range snaps {
		tmpSnaps[snap.Id] = snap
	}
	if smsg.SelfValue != nil {
		if snap, exist := tmpSnaps[smsg.SelfValue.Id]; exist {
			smsg.SelfValue.User = snap
		}
	}

	for _, l := range smsg.List {
		if snap, exist := tmpSnaps[l.Id]; exist {
			l.User = snap
		}
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossRank, smsg)

	return true
}

type C2LWorldBossGetRoomLogCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossGetRoomLogCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossGetRoomLogCommand) Error(msg *cl.L2C_WorldBossGetRoomLog, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetRoomLog, msg)
	return false
}

func (c *C2LWorldBossGetRoomLogCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WorldBossGetRoomLog{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossGetRoomLog unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d C2L_WorldBossGetRoomLog. recv from logic:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossGetRoomLog{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossGetRoomLog: cross data error. ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.Logs = cmsg.Logs
	smsg.RoomTop = cmsg.RoomTop
	smsg.List = cmsg.List
	smsg.PartitionRank = cmsg.PartitionRank

	serverUsers := make([]*cl.ServerUser, 0, len(smsg.List)+1)
	serverUsers = append(serverUsers, newServerUser(smsg.List)...)
	if smsg.RoomTop != nil {
		serverUsers = append(serverUsers, newServerUser([]*cl.RankValue{smsg.RoomTop})...)
	}
	if len(serverUsers) == 0 {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetRoomLog, smsg)
	} else {
		c.GetUserSnapshotsFromAll(serverUsers, &AsyncC2LWorldBossGetRoomLogReq{smsg}, 0)
	}

	return c.ResultOK(cmsg.Ret)
}

type AsyncC2LWorldBossGetRoomLogReq struct {
	smsg *cl.L2C_WorldBossGetRoomLog
}

func (ar *AsyncC2LWorldBossGetRoomLogReq) Resp(srv command.Servicer, args *character.Args, retCode uint32,
	dbData interface{}) bool {
	smsg := ar.smsg
	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("user: %d AsyncC2LWorldBossMatchRoomReq: get snaps failed.", args.UID)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetRoomLog, smsg)
		return false
	}

	tmpSnaps := make(map[uint64]*cl.UserSnapshot)
	for _, snap := range snaps {
		tmpSnaps[snap.Id] = snap
	}
	if smsg.RoomTop != nil {
		if snap, exist := tmpSnaps[smsg.RoomTop.Id]; exist {
			smsg.RoomTop.User = snap
		}
	}

	for _, l := range smsg.List {
		if snap, exist := tmpSnaps[l.Id]; exist {
			l.User = snap
		}
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetRoomLog, smsg)

	return true
}

type C2LWorldBossSettleCommand struct {
	base.Command
}

func (c *C2LWorldBossSettleCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossSettleCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WorldBossSettle{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossSettle unmarshal error. sid:%d err:%v", c.Msg.UID, err)
		return false
	}

	l4g.Debugf("sid:%d C2L_WorldBossSettle. recv from logic:%+v", c.Msg.UID, cmsg)
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("sid: %d C2L_WorldBossSettle: cross data error. ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}
	if len(cmsg.List) == 0 && len(cmsg.PartitionFirst) == 0 {
		l4g.Errorf("sid: %d C2L_WorldBossSettle: get rank data err from cross. ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	worldBossM, ok := c.Srv.GetActivity(activity.WorldBoss).(*worldboss.Manager)
	if !ok {
		l4g.Errorf("C2L_WorldBossSettle: get worldBossM failed.")
		return false
	}

	if worldBossM.GetSettleStatus(cmsg.Id) {
		l4g.Errorf("sid: %d C2L_WorldBossSettle: repeat settle from cross.", c.Msg.UID)
		return false
	}

	// 跑马灯：各难度战区第一名
	if len(cmsg.PartitionFirst) > 0 {
		c.sendSystemMsg(cmsg.PartitionFirst)
	}

	bossInfo := goxml.GetData().WorldbossInfoM.GetRecordById(cmsg.Id)
	if bossInfo == nil {
		l4g.Errorf("sid: %d C2L_WorldBossSettle: id:%d info is nil", c.Msg.UID, cmsg.Id)
		return false
	}

	// 发送奖励邮件
	if len(cmsg.List) > 0 {
		c.sendRankSettleAward(cmsg.List, bossInfo.BossType)
	}

	worldBossM.UpdateSettleStatus(c.Srv, cmsg.Id)
	l4g.Infof("C2L_WorldBossSettle: settle finish.")

	return true
}

func (c *C2LWorldBossSettleCommand) sendRankSettleAward(list []*cl.RankValue, bossType uint32) {
	for _, v := range list {
		// 房间榜奖励
		c.sendMail(bossType, uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_ROOM), uint32(v.Param1), uint32(v.Param2), character.MailIDWorldBossRoomRank, v.Id)
		// 战区榜奖励
		c.sendMail(bossType, uint32(common.WORLD_BOSS_RANK_TYPE_WBRT_PARTITION), uint32(v.Param1), uint32(v.Param3), character.MailIDWorldBossPartitionRank, v.Id)
	}
}

func (c *C2LWorldBossSettleCommand) sendMail(bossType, rankType, level, rank, mailID uint32, uid uint64) {
	awards := goxml.GetData().WorldbossRankRewardInfoM.GetRankAward(bossType, rankType, level, rank)
	if len(awards) == 0 {
		l4g.Errorf("get room rank award failed. level:%d, rank:%d uid:%d", level, rank, uid)
		return
	}
	c.updateAvatarExpireTime(awards)
	params := []string{strconv.Itoa(int(level)), strconv.Itoa(int(rank))}
	character.WorldBossSettleMail(c.Srv, mailID, params, awards, uid)
}

// 更新限时avatar的过期时间
func (c *C2LWorldBossSettleCommand) updateAvatarExpireTime(awards []*cl.Resource) {
	for _, award := range awards {
		if award.Type != uint32(common.RESOURCE_AVATAR) {
			continue
		}

		info := goxml.GetData().AvatarInfoM.Index(award.Value)
		if info != nil && info.DurationType == goxml.AvatarTimeTypeEndTime {
			endTm := time.Now().Unix() + int64(info.Duration*60) //nolint:mnd
			award.Attrs = []*cl.Attr{
				{
					Value: endTm,
				},
			}
		}
	}
}

func (c *C2LWorldBossSettleCommand) sendSystemMsg(ranks []*cl.RankValue) {
	uids := make([]uint64, 0, len(ranks))
	for _, v := range ranks {
		uids = append(uids, v.Id)
	}

	c.GetLocalUserSnapshots(uids, &AsyncC2LWorldBossSettleReq{ranks: ranks}, 0)
}

type AsyncC2LWorldBossSettleReq struct {
	ranks []*cl.RankValue
}

func (ar *AsyncC2LWorldBossSettleReq) Resp(srv command.Servicer, args *character.Args, retCode uint32, dbData interface{}) bool {
	snaps, ok := dbData.([]*cl.UserSnapshot)
	if !ok {
		l4g.Errorf("AsyncC2LWorldBossSettleReq: get snaps failed.")
		return false
	}

	for _, rank := range ar.ranks {
		chatUserHead := ar.getChatUserHead(snaps, rank.Id)
		if chatUserHead == nil {
			l4g.Errorf("AsyncC2LWorldBossSettleReq: get snaps failed. user: %d", rank.Id)
			continue
		}
		level := strconv.FormatUint(rank.Param1, 10)
		message := character.NewMsg(character.Id(srv), character.Head(chatUserHead), character.Type(goxml.ChatWorldBossPartitionRankSettle), character.Params(level))
		character.ChatSendMsgToPlatform(srv, rank.Id, message)
	}

	return true
}

func (ar *AsyncC2LWorldBossSettleReq) getChatUserHead(snaps []*cl.UserSnapshot, uid uint64) *cl.ChatUserHead {
	for _, snap := range snaps {
		if snap.Id == uid {
			return &cl.ChatUserHead{
				Id:       strconv.FormatUint(snap.Id, 10),
				Name:     snap.Name,
				BaseId:   snap.BaseId,
				Level:    snap.Level,
				Power:    strconv.FormatUint(uint64(snap.Power), 10),
				Vip:      snap.Vip,
				ServerId: strconv.FormatUint(snap.Sid, 10),
			}
		}
	}

	return nil
}

type C2LWorldBossSyncFightDataCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossSyncFightDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossSyncFightDataCommand) Error(msg *cl.L2C_WorldBossFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossFight, msg)
	return false
}

func (c *C2LWorldBossSyncFightDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WorldBossSyncFightData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossSyncFightData unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d C2L_WorldBossSyncFightData. recv from logic:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossFight{
		Ret:  uint32(cret.RET_OK),
		Type: cmsg.Type,
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossSyncFightData: cross data error. ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	smsg.ReportId = cmsg.ReportId
	smsg.Hurt = cmsg.Hurt
	smsg.HurtScore = cmsg.HurtScore
	smsg.MaxHurt = cmsg.MaxHurt
	smsg.MaxHurtScore = cmsg.MaxHurtScore
	smsg.AccumulativeHurt = cmsg.AccumulativeHurt
	smsg.AccumulativeHurtScore = cmsg.AccumulativeHurtScore
	smsg.Win = true

	worldBoss := c.User.WorldBoss()
	room := worldBoss.GetWorldBossData().GetRoom()
	if room != nil {
		smsg.AccumulativeHurt = room.AccumulativeHurt
		smsg.FightCount = room.FightCount
		smsg.RecoveryTimeFightCount = room.RecoveryTimeFightCount
	}
	if cmsg.IsSetRecommendFormation {
		c.User.CognitionManager().Send(c.Srv, uint32(common.FORMATION_ID_FI_WORLD_BOSS),
			worldBoss.GetLevel(), worldBoss.GetWorldBossSysID(), worldBoss.GetMaxHurtScore())
	}
	// 发送跑马灯：各等级战区榜第1名变化
	if cmsg.IsSendSystemMsg {
		if c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_CHAT_PARTITION), c.Srv) {
			level := strconv.FormatUint(uint64(worldBoss.GetLevel()), 10)
			message := character.NewMsg(character.Id(c.Srv), character.Head(c.User.NewChatUserHead(c.Srv.GetLogicGuild(c.User.ID()))),
				character.Type(goxml.ChatWorldBossPartitionRankFirstChange), character.Params(level))
			character.ChatSendMsgToPlatform(c.Srv, c.User.ID(), message)
		}
	}

	if cmsg.IsSetPeakFormation {
		c.setPeakFormation()
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossFight, smsg)

	return true
}

func (c *C2LWorldBossSyncFightDataCommand) setPeakFormation() {
	formationM := c.User.FormationManager()
	fid1 := uint32(common.FORMATION_ID_FI_PEAK_1)
	if formationM.Get(fid1) == nil {
		formationM.SetFormationByPower(c.Srv, fid1, goxml.GetPeakTeamNum(fid1))
	}

	fid2 := uint32(common.FORMATION_ID_FI_PEAK_2)
	if formationM.Get(fid2) == nil {
		formationM.SetFormationByPower(c.Srv, fid2, goxml.GetPeakTeamNum(fid2))
	}
}

type C2LWorldBossGetDataCommand struct {
	base.UserCommand
}

func (c *C2LWorldBossGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LWorldBossGetDataCommand) Error(msg *cl.L2C_WorldBossGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetData, msg)
	return false
}

func (c *C2LWorldBossGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WorldBossGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WorldBossGetData unmarshal error. uid:%d err:%v", c.Msg.UID, err)
		return false
	}
	l4g.Debugf("user:%d C2L_WorldBossGetData. recv from logic:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WorldBossGetData{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WorldBossGetData: cross data error. ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	worldBoss := c.User.WorldBoss()
	worldBoss.CheckReset(c.Srv)
	if c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_WORLD_BOSS), c.Srv) {
		cloneWorld := worldBoss.Flush()
		cloneWorld.Data.BossTypeLevel = cmsg.BossTypeLevel
		smsg.WorldBoss = cloneWorld
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WorldBossGetData, smsg)
	return true
}
