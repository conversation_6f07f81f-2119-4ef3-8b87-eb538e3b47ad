package cross

import (
	"app/goxml"
	"app/logic/activity"
	awrestle "app/logic/activity/wrestle"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/cr"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"strconv"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitWrestle(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleInfo), &C2LWrestleInfoCommand{}, state)             // 收到跨服信息
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleStart), &C2LWrestleStartCommand{}, state)           // 战斗发起返回
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleFinish), &C2LWrestleFinishCommand{}, state)         // 战斗结束返回
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestlePush), &C2LWrestlePushCommand{}, state)             // 战斗结束推送
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleAward), &C2LWrestleAwardCommand{}, state)           // 结算
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleRank), &C2LWrestleRankCommand{}, state)             // 排行榜
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleLike), &C2LWrestleLikeCommand{}, state)             // 点赞
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleTopList), &C2LWrestleTopListCommand{}, state)       // 获取头部玩家
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleLastRank), &C2LWrestleLastRankCommand{}, state)     // 获取上赛季排行榜
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleRankFirst), &C2LWrestleRankFirstCommand{}, state)   // 获取排行榜第一名
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleChangeRoom), &C2LWrestleChangeRoomCommand{}, state) // 更换房间
	cmds.Register(uint32(l2c.ID_MSG_C2L_WrestleRoomUpdate), &C2LWrestleRoomUpdateCommand{}, state) // 房间数据更新
}

type C2LWrestleInfoCommand struct {
	base.UserCommand
}

func (c *C2LWrestleInfoCommand) Error(smsg *cl.L2C_WrestleRoomInfo, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRoomInfo, smsg)
	return false
}

func (c *C2LWrestleInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleInfo Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleInfo user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WrestleRoomInfo{
		Ret:        uint32(cret.RET_OK),
		NeedChange: cmsg.NeedChange,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[WrestleInfo] user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleInfo failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)

	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleInfo failed, wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if cmsg.Fighter == nil {
		l4g.Errorf("user: %d C2L_WrestleInfo failed, cmsg.Fighter is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	now := time.Now().Unix()
	if goxml.GetWrestleSeasonResetTime(goxml.GetData(), wrestleUser.GetEnterTime()) !=
		goxml.GetWrestleSeasonResetTime(goxml.GetData(), now) {
		//赛季第一次进入，更新进入时间
		wrestleUser.SetEnterTime(now)
	}

	wrestleUser.SetLevel(cmsg.Fighter.Level)
	wrestleUser.SetScore(cmsg.Fighter.Score)
	wrestleUser.SetTotalRank(cmsg.Fighter.Rank)

	if wrestleUser.GetLevel() == goxml.WrestleTopLevel() {
		wrestleUser.SetRank(cmsg.Fighter.Rank)

		// 顶级赛场，前三名必须传
		for _, fighter := range cmsg.Top3 {
			newOpponent := fighterToOpponent(wrestleUser, fighter)
			smsg.Opponents = append(smsg.Opponents, newOpponent)
		}
		// 自己
		newOpponent := fighterToOpponent(wrestleUser, cmsg.Fighter)
		smsg.Opponents = append(smsg.Opponents, newOpponent)
	}

	c.addOpponentsToSmsg(cmsg, wrestleUser, smsg)

	smsg.NewLogTip = wrestleUser.GetNewLogTip()
	smsg.TotalRank = wrestleUser.GetTotalRank()
	smsg.BeDefeated = wrestleUser.BeDefeated()
	smsg.RoomId = cmsg.Fighter.RoomId
	smsg.SeasonJoinNum = wrestleUser.GetSeasonJoinNum()
	if cmsg.NeedChange {
		smsg.LastFightChange = wrestleUser.GetLastFightChange()
	}
	smsg.NextChangeRoomTm = wrestleUser.GetNextChangeRoomTm()

	wrestleM.SetChange(c.Srv, wrestleUser)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRoomInfo, smsg)
	return true
}

func fighterToOpponent(wrestleUser *awrestle.User, fighter *cr.Fighter) *cl.WrestleOpponentInfo {
	cdStart := wrestleUser.GetDefenderCdStart(fighter.Uid)
	newOpponent := &cl.WrestleOpponentInfo{
		Base:  fighter.Snapshot,
		Rank:  fighter.Rank,
		Score: fighter.Score,
		BotId: fighter.BotId,
	}
	newOpponent.CdStart = cdStart
	return newOpponent
}

func (c *C2LWrestleInfoCommand) addOpponentsToSmsg(cmsg *l2c.C2L_WrestleInfo, wrestleUser *awrestle.User,
	smsg *cl.L2C_WrestleRoomInfo) {

	revengeUids := make(map[uint64]struct{})
	for _, uid := range cmsg.Fighter.Revenge {
		revengeUids[uid] = struct{}{}
	}

	defeatUids := make(map[uint64]struct{})
	for _, uid := range cmsg.Fighter.Defeated {
		defeatUids[uid] = struct{}{}
	}

	topLevel := wrestleUser.GetLevel() == goxml.WrestleTopLevel()

	wrestleUser.ClearMemberIDs()

	for index, defender := range cmsg.Defenders {
		if defender == nil || defender.Snapshot == nil {
			l4g.Errorf("user: %d C2L_WrestleInfo failed, defender is nil. cmsg.Defenders:%+v", c.Msg.UID, cmsg.Defenders)
			continue
		}
		newOpponent := fighterToOpponent(wrestleUser, defender)
		smsg.Opponents = append(smsg.Opponents, newOpponent)
		if defender.Snapshot.Id == wrestleUser.GetUID() && !topLevel {
			wrestleUser.SetRank(uint32(index + 1))
			continue
		}
		if topLevel {
			continue
		}
		wrestleUser.AddMember(defender.Snapshot.Id)
		if _, exist := revengeUids[defender.Snapshot.Id]; exist {
			newOpponent.Status = uint32(common.WRESTLE_OP_STATUS_WOS_REVENGE)
			continue
		}
		if _, exist := defeatUids[defender.Snapshot.Id]; exist {
			newOpponent.Status = uint32(common.WRESTLE_OP_STATUS_WOS_DEFEATED)
		}
	}
}

type C2LWrestleStartCommand struct {
	base.UserCommand
}

func (c *C2LWrestleStartCommand) Error(smsg *cl.L2C_WrestleFight, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsg)
	return false
}

func (c *C2LWrestleStartCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleStart{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleStart Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleStart user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsgToClient := &cl.L2C_WrestleFight{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleStart failed, cross return ret: %d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsgToClient, cmsg.Ret)
	}

	if cmsg.BotId == 0 {
		req := &AsyncL2CWrestleStartReq{
			Uid:         cmsg.DefenderUid,
			Sid:         cmsg.DefenderSid,
			FormationID: uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE),
		}
		reqUsers := []*cl.ServerUser{{Sid: cmsg.DefenderSid, Uid: cmsg.DefenderUid}}
		c.GetUserBattleDataFromAll(reqUsers, req, uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE))
		return true
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		smsgToClient.Ret = uint32(cret.RET_SERVER_ERROR)
		l4g.Errorf("user: %d C2L_WrestleStart wrestleM not exit. ret:%d ", c.User.ID(), uint32(cret.RET_SERVER_ERROR))
		return c.Error(smsgToClient, smsgToClient.Ret)
	}

	wrestleUser := wrestleM.Get(c.User.ID())
	if wrestleUser == nil {
		smsgToClient.Ret = uint32(cret.RET_SERVER_ERROR)
		l4g.Errorf("user: %d C2L_WrestleStart wrestleUser not exit. ret:%d ", c.User.ID(), uint32(cret.RET_SERVER_ERROR))
		return c.Error(smsgToClient, smsgToClient.Ret)
	}
	botInfo := goxml.GetData().WrestleBotInfoM.Index(cmsg.BotId)
	if botInfo == nil {
		smsgToClient.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		l4g.Errorf("user: %d C2L_WrestleStart botInfo not exit. botId:%d ", c.User.ID(), cmsg.BotId)
		return c.Error(smsgToClient, smsgToClient.Ret)
	}

	seasonBuffs := goxml.GetData().WrestleBuffInfoM.GetCurrentBuffs()
	if len(seasonBuffs) == 0 {
		smsgToClient.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		l4g.Errorf("user: %d C2L_WrestleStart no seasonBuffs", c.User.ID())
		return c.Error(smsgToClient, smsgToClient.Ret)
	}

	win, reportID, attackRet := c.User.AttackWrestleRobot(c.Srv, botInfo.MonsterGroups,
		cmsg.BotName, wrestleUser.GetClientData(), seasonBuffs)
	if attackRet != cret.RET_OK {
		smsgToClient.Ret = uint32(cret.RET_BATTLE_ERROR)
		l4g.Errorf("user: %d C2L_WrestleStart error. ret:%d ", c.User.ID(), smsgToClient.Ret)
		return c.Error(smsgToClient, smsgToClient.Ret)
	}

	smsg := &l2c.L2C_WrestleFinish{
		DefenderUid: cmsg.DefenderUid,
		DefenderSid: cmsg.DefenderSid,
		AttackerUid: cmsg.AttackerUid,
		AttackerSid: cmsg.AttackerSid,
		ReportId:    reportID,
		AttackerWin: win,
	}

	c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleFinish, c.Msg.UID, smsg)
	return true
}

type AsyncL2CWrestleStartReq struct {
	Uid         uint64
	Sid         uint64
	FormationID uint32
}

func (ar *AsyncL2CWrestleStartReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsgToClient := &cl.L2C_WrestleFight{
		Ret: uint32(cret.RET_OK),
	}
	if retCode != uint32(cret.RET_OK) {
		smsgToClient.Ret = retCode
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	wrestleM, ok := srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		smsgToClient.Ret = retCode
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq wrestleM not exit. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	wrestleUser := wrestleM.Get(args.UID)

	if wrestleUser == nil {
		smsgToClient.Ret = retCode
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq wrestleM not exit. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	snaps, ok := dbData.([]*db.UserBattleData)
	if !ok {
		smsgToClient.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	snapm := make(map[uint64]*db.UserBattleData, len(snaps))
	for _, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d AsyncL2CWrestleStartReq: no data. opUid:%d opSid:%d", args.UID, ar.Uid, ar.Sid)
			smsgToClient.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
			return false
		}
		snapm[v.User.Id] = v
	}

	if snapm[ar.Uid] == nil {
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq: no data. opUid:%d opSid:%d", args.UID, ar.Uid, ar.Sid)
		smsgToClient.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return false
	}

	opUser := character.GetUserFromUserBattleData(srv, snapm[ar.Uid])
	if opUser == nil {
		smsgToClient.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	flag, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(uint32(common.FORMATION_ID_FI_CROSS_ARENA_ATTACK))
	if !flag {
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq: formation info not exist. opUid:%d opSid:%d", args.UID, ar.Uid, ar.Sid)
		smsgToClient.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	seasonBuffs := goxml.GetData().WrestleBuffInfoM.GetCurrentBuffs()
	if len(seasonBuffs) == 0 {
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq no seasonBuffs", args.UID)
		smsgToClient.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	win, reportID, attackRet := args.Caller.AttackWrestleOpponent(srv, opUser, common.FORMATION_ID_FI_CROSS_ARENA_ATTACK, common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE, teamNum, wrestleUser.GetClientData(), seasonBuffs)
	if attackRet != cret.RET_OK {
		smsgToClient.Ret = uint32(cret.RET_BATTLE_ERROR)
		l4g.Errorf("user: %d AsyncL2CWrestleStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsgToClient)
		return true
	}

	smsg := &l2c.L2C_WrestleFinish{
		DefenderUid:      ar.Uid,
		DefenderSid:      ar.Sid,
		AttackerUid:      args.Caller.ID(),
		AttackerSid:      args.Caller.ServerID(),
		ReportId:         reportID,
		AttackerWin:      win,
		DefenderSnapshot: opUser.NewUserSnapshot(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)),
	}

	srv.SendCmdToCross(l2c.ID_MSG_L2C_WrestleFinish, args.UID, smsg)
	return true
}

type C2LWrestleFinishCommand struct {
	base.UserCommand
}

func (c *C2LWrestleFinishCommand) Error(smsg *cl.L2C_WrestleFight, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsg)
	return false
}

//nolint:funlen
func (c *C2LWrestleFinishCommand) Execute(ctx context.Context) bool {

	cmsg := &l2c.C2L_WrestleFinish{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleFinish Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleFinish user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WrestleFight{
		Ret:      uint32(cret.RET_OK),
		Promoted: cmsg.Promoted,
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleFinish failed, cross return ret: %d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleFinish error. ret:%d ", c.Msg.UID, uint32(cret.RET_ERROR))
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleFinish failed, cross return ret: %d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	now := time.Now().Unix()

	smsg.ReportId = cmsg.ReportId

	attacker := &cl.MiniUserSnapshot{
		Id:     c.User.ID(),
		Level:  c.User.Level(),
		Name:   c.User.Name(),
		BaseId: c.User.BaseID(),
		Power:  c.User.GetCrystalPower(),
		Sid:    c.User.ServerID(),
	}

	selfRankChange := int32(cmsg.AttackerOldResult.Rank) - int32(cmsg.AttackerNewResult.Rank)

	//更新挑战记录
	newLevel := cmsg.Attacker.Level
	myLogM := wrestleM.GetLogM().GetOwnerLogs(wrestleUser)
	myLogM.AddLog(c.Srv, awrestle.NewWrestleLog(c.Srv.CreateUserLogID(), cmsg.ReportId, selfRankChange, cmsg.AttackerWin, attacker,
		cmsg.DefenderSnapshot, now, newLevel, cmsg.Attacker.RoomId, cmsg.IsBot))
	wrestleM.GetLogM().SetChange(myLogM)

	smsg.Win = cmsg.AttackerWin //

	smsg.Id = cmsg.DefenderSnapshot.Id
	// smsg.Sid = cmsg.DefenderSnapshot.Sid
	smsg.SelfChange = &cl.WrestleUserScoreAndRank{
		OldRank:  cmsg.AttackerOldResult.Rank,
		NewRank:  cmsg.AttackerNewResult.Rank,
		OldScore: cmsg.AttackerOldResult.Score,
		NewScore: cmsg.AttackerNewResult.Score,
	}

	smsg.OpponentChange = &cl.WrestleUserScoreAndRank{
		OldRank:  cmsg.DefenderOldResult.Rank,
		NewRank:  cmsg.DefenderNewResult.Rank,
		OldScore: cmsg.DefenderOldResult.Score,
		NewScore: cmsg.DefenderNewResult.Score,
	}

	wrestleUser.SetLastFightChange(&cl.WrestleUserScoreAndRank{
		NewScore: cmsg.AttackerNewResult.Score,
		OldScore: cmsg.AttackerOldResult.Score,
		NewRank:  cmsg.AttackerNewResult.Rank,
		OldRank:  cmsg.AttackerOldResult.Rank,
	})

	oldLevel := wrestleUser.GetLevel()
	wrestleUser.SetScore(cmsg.AttackerNewResult.Score)
	wrestleUser.SetLevel(newLevel)
	wrestleUser.SetRank(cmsg.AttackerNewResult.Rank)
	wrestleUser.SetTotalRank(cmsg.Attacker.Rank)
	//wrestleUser.SetNewLogTip(true)
	wrestleUser.Unlock()
	if !smsg.Win {
		wrestleUser.SetDefenderCdStart(cmsg.DefenderSnapshot.Id, time.Now().Unix())
	}

	//自动晋升
	if smsg.Win && newLevel > oldLevel {
		wrestleUser.SetLastLevel(oldLevel)
		if newLevel > wrestleUser.GetSeasonMaxLevel() {
			wrestleUser.SetSeasonMaxLevel(newLevel)
		}
		wrestleUser.SetBeDefeated(false)
		wrestleUser.SetLastRoomRank(1)
		wrestleUser.ResetFailTm()

		lastTopScore := goxml.WrestleCommonRoomTopScore()
		c.User.FireCommonEvent(c.Srv.EventM(), event.IeWrestleFight, uint64(1), lastTopScore, oldLevel)
	}
	wrestleM.SetChange(c.Srv, wrestleUser)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeWrestleFight, uint64(1), wrestleUser.GetScore(), wrestleUser.GetLevel())
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeHotRankUpdate, uint64(wrestleUser.GetRank()), uint32(common.FORMATION_ID_FI_CROSS_ARENA_ATTACK))
	c.User.LogWrestleFight(c.Srv, cmsg.DefenderSnapshot.Id, cmsg.DefenderSnapshot.Sid, c.User.ContextID(), wrestleUser.GetLevel(),
		smsg.SelfChange, smsg.OpponentChange, smsg.Win, smsg.Promoted, smsg.ReportId)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleFight, smsg)
	return true
}

type C2LWrestlePushCommand struct {
	base.Command
}

func (c *C2LWrestlePushCommand) Execute(ctx context.Context) bool {

	cmsg := &l2c.C2L_WrestlePush{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestlePush Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	//c.Trace(cmsg)

	l4g.Debug("C2L_WrestlePush user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestlePush error. ret:%d ", c.Msg.UID, uint32(cret.RET_ERROR))
		return false
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestlePush user is nil. ret:%d ", c.Msg.UID, uint32(cret.RET_ERROR))
		return false
	}

	now := time.Now().Unix()

	rankChange := int32(cmsg.OldResult.Rank) - int32(cmsg.NewResult.Rank)

	var defender *cl.MiniUserSnapshot
	if cmsg.Defender != nil && cmsg.Defender.Snapshot != nil {
		defender = &cl.MiniUserSnapshot{
			Id:     cmsg.Defender.Uid,
			Level:  cmsg.Defender.Snapshot.Level,
			Name:   cmsg.Defender.Snapshot.Name,
			BaseId: cmsg.Defender.Snapshot.BaseId,
			Power:  cmsg.Defender.Snapshot.CrystalPower,
			Sid:    cmsg.Defender.Sid,
		}
	}

	opLogM := wrestleM.GetLogM().GetOwnerLogs(wrestleUser)
	opLogM.AddLog(c.Srv, awrestle.NewWrestleLog(c.Srv.CreateUserLogID(), cmsg.ReportId, rankChange, cmsg.AttackerWin,
		cmsg.AttackerSnapshot, defender, now, cmsg.Defender.Level, cmsg.Defender.RoomId, false))
	wrestleUser.SetNewLogTip(true)
	wrestleM.GetLogM().SetChange(opLogM)

	//if cmsg.OldResult.Rank < cmsg.Defender.Rank {
	if cmsg.AttackerWin { // 防守方失败
		wrestleUser.SetBeDefeated(true)
	}
	wrestleUser.SetRank(cmsg.NewResult.Rank)
	wrestleUser.SetScore(cmsg.NewResult.Score)
	wrestleUser.SetTotalRank(cmsg.Defender.Rank)
	wrestleUser.SetLastFightChange(&cl.WrestleUserScoreAndRank{
		NewScore: cmsg.NewResult.Score,
		OldScore: cmsg.OldResult.Score,
		NewRank:  cmsg.NewResult.Rank,
		OldRank:  cmsg.OldResult.Rank,
	})
	wrestleM.SetChange(c.Srv, wrestleUser)

	//c.User.LogWrestleBeFight(c.Srv, defender.Id, c.User.ContextID(), cmsg.OldResult.Rank,
	//	wrestleUser.GetRank(), cmsg.AttackerWin)
	return true
}

type C2LWrestleAwardCommand struct {
	base.Command
}

func (c *C2LWrestleAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleAward Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	// c.Trace(cmsg)

	l4g.Debug("C2L_WrestleAward user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("C2L_WrestleAward recv cross rank award error, ret:%d", cmsg.Ret)
		return false
	}

	if cmsg.Sid != c.Srv.ServerID() {
		l4g.Errorf("C2L_WrestleAward user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return false
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("C2L_WrestleAward error. ret:%d ", uint32(cret.RET_ERROR))
		return false
	}

	if cmsg.Time < goxml.GetWrestleDailyResetTime(cmsg.Time) {
		l4g.Errorf("C2L_WrestleAward cross rank award time error.  cmsg.Time:%d",
			cmsg.Time)
		return false
	}

	haveSeasonAward := util.InSameDay(cmsg.Time, goxml.GetWrestleSeasonResetTime(goxml.GetData(), cmsg.Time))

	awardTime := cmsg.Time

	lastDailyTime := wrestleM.GetLastDailyAwardTime()
	lastSeasonTime := wrestleM.GetLastSeasonAwardTime()
	if lastDailyTime >= awardTime || lastSeasonTime >= awardTime {
		l4g.Errorf("C2L_WrestleAward cross rank has awarded, award lastDailyTime:%d lastSeasonTime:%d",
			lastDailyTime, lastSeasonTime)
		return false
	}

	userByLevel := make(map[uint32][]uint64) // key: level

	if len(cmsg.Ranks) > 0 {
		for _, data := range cmsg.Ranks {
			if data == nil || data.Result == nil {
				l4g.Errorf("C2L_WrestleAward: rankData is nil.")
				continue
			}
			rankData := data.Result
			wrestleUser := wrestleM.Get(rankData.Uid)
			if wrestleUser == nil {
				l4g.Errorf("C2L_WrestleAward wrestleUser not exist. uid:%d", rankData.Uid)
				continue
			}
			if haveSeasonAward {
				wrestleUser.SetPrevSeasonLevelBeforeReset(wrestleUser.GetLevel())
			}
			wrestleUser.DailyReset(c.Srv, rankData, data.LastRoomRank)

			userByLevel[rankData.Level] = append(userByLevel[rankData.Level], rankData.Uid)

			if haveSeasonAward {
				if rankData.Rank == 1 {
					wrestleUser.AddNumOfChampion(1)
				}
				if rankData.Rank > 0 && rankData.Rank <= goxml.GetData().WrestleRankRewardInfoM.GetLastRank() {
					c.handleRankAwards(rankData, wrestleUser, cmsg.Time)
				}
				c.handleLevelTaskAwards(wrestleUser)
				wrestleUser.SeasonReset()
				wrestleUser.SeasonAutoSignUp(awardTime, rankData.Level)
			}
			wrestleM.SetChange(c.Srv, wrestleUser)
		}

		c.handleLevelAwards(haveSeasonAward, userByLevel)
	}

	//设置跨服排行奖励发放时间
	wrestleM.SetDailyAwardTime(cmsg.Time)
	if haveSeasonAward {
		wrestleM.SetSeasonAwardTime(cmsg.Time)
	}
	wrestleM.SetWrestleChange(c.Srv)
	return true
}

func (c *C2LWrestleAwardCommand) handleLevelAwards(haveSeasonAward bool, userByLevel map[uint32][]uint64) {
	for i := uint32(1); i < uint32(common.WRESTLE_LEVEL_WL_MAX); i++ {
		uids := userByLevel[i]
		if len(uids) == 0 {
			continue
		}

		lvDailyAwards := goxml.GetData().WrestleRewardInfoM.GetReward(1, i)
		character.WrestleMail(c.Srv, character.MailIDWrestleDaily,
			[]string{strconv.Itoa(int(i))}, lvDailyAwards, uids)

		if haveSeasonAward {
			lvSeasonAwards := goxml.GetData().WrestleRewardInfoM.GetReward(2, i)
			character.WrestleMail(c.Srv, character.MailIDWrestleSeason,
				[]string{strconv.Itoa(int(i))}, lvSeasonAwards, uids)
		}

		l4g.Infof("wrestle.sendAwards: level award finished. level:%d, uids:%v",
			i, uids)
	}
}

// 排名奖励
func (c *C2LWrestleAwardCommand) handleRankAwards(rankData *cr.RankBakData, wrestleUser *awrestle.User, resetTm int64) {
	allAwards := goxml.GetData().WrestleRankRewardInfoM.GetRankAwards(rankData.Rank, resetTm)

	if len(allAwards) == 0 {
		l4g.Errorf("handleRankAwards: rankRewards not exist. rank:%d, uid:%d",
			rankData.Rank, rankData.Uid)
		return
	}

	//连冠头像
	if rankData.Rank == 1 {
		count := wrestleUser.GetNumOfChampion()
		avatarID := goxml.GetData().WrestleConfigInfoM.GetChampionAvatar(count)
		if avatarID > 0 {
			allAwards = append(allAwards, &cl.Resource{
				Type:  uint32(common.RESOURCE_AVATAR),
				Value: avatarID,
				Count: 1,
			})
		}
	}

	character.WrestleMail(c.Srv, character.MailIDWrestleSeasonRank,
		[]string{strconv.Itoa(int(rankData.Rank))}, allAwards, []uint64{rankData.Uid})

	l4g.Infof("wrestle.sendAwards: rank award finished. rank:%d, uid:%v",
		rankData.Rank, rankData.Uid)
}

func (c *C2LWrestleAwardCommand) handleLevelTaskAwards(wrestleUser *awrestle.User) {
	level := wrestleUser.GetSeasonMaxLevel()
	awards := make([]*cl.Resource, 0, goxml.WrestleTopLevel())
	for i := uint32(common.WRESTLE_LEVEL_WL_FIRST); i <= level; i++ {
		if wrestleUser.CheckLevelTaskRecvRepeat(i) {
			continue
		}
		addAwards := goxml.GetData().WrestleLevelTaskInfoM.GetTaskAwards(i)
		if len(addAwards) == 0 {
			continue
		}
		awards = append(awards, addAwards...)
	}
	if len(awards) > 0 {
		character.WrestleMail(c.Srv, character.MailIDWrestleLevelAwardsReissue,
			[]string{strconv.Itoa(int(level))}, awards, []uint64{wrestleUser.GetUID()})
	}
}

type C2LWrestleRankCommand struct {
	base.UserCommand
}

func (c *C2LWrestleRankCommand) Error(smsg *cl.L2C_WrestleRankList, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRankList, smsg)
	return false
}

func (c *C2LWrestleRankCommand) Execute(ctx context.Context) bool {

	cmsg := &l2c.C2L_WrestleRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleRank Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleRank user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WrestleRankList{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[WrestleRank] user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleRank error. ret:%d ", c.Msg.UID, uint32(cret.RET_ERROR))
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleRank failed, cross return ret: %d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	var param1 uint64
	if wrestleUser.GetLevel() != goxml.WrestleTopLevel() {
		param1 = uint64(wrestleUser.GetScore())
	}

	smsg.List = cmsg.Values
	smsg.SelfRank = cmsg.SelfRank
	smsg.Type = uint32(common.WRESTLE_RANK_WR_CURRENT)
	smsg.SelfValue = &cl.RankValue{
		Value:  uint64(wrestleUser.GetLevel()),
		User:   c.User.NewUserSnapshot(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)),
		Param1: param1,
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleRankList, smsg)
	return true
}

type C2LWrestleLikeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LWrestleLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleLike Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleLike user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WrestleLike{
		Ret: uint32(cret.RET_OK),
		Id:  cmsg.Uid,
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[Wrestle] user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		smsg.Ret = cmsg.Ret
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleLike, smsg)
		return false
	}

	fret, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_WRESTLE_LIKE_COUNT), 1, c.Srv)
	if fret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleLike: check num err: %d", c.Msg.UID, fret)
		smsg.Ret = cmsg.Ret
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleLike, smsg)
		return false
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleLike failed, wrestleManager not exist", c.Msg.UID)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleLike, uint32(cret.RET_ERROR))
		return false
	}

	wrestleM.UpdateHallOfFameTop3LikeCount(c.Srv, cmsg.LikedCount)

	rank := wrestleM.GetLikeRank(cmsg.Uid)

	awards := goxml.GetData().WrestleConfigInfoM.GetLikeReward()

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_WRESTLE_LIKE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("%d [C2L_WrestleLike] emblem revive award error. %d, %d", c.Msg.UID, cmsg.Uid, smsg.Ret)
	}
	//smsg.LikeNum = wrestleM.GetLikeNum()
	//更新今日挑战次数
	c.User.AddNumByType(uint32(common.PURCHASEID_WRESTLE_LIKE_COUNT), numSummary)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeWrestleLikeCount, 1)
	c.User.LogWrestleLike(c.Srv, cmsg.Uid, rank)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleLike, smsg)
	return true
}

type C2LWrestleTopListCommand struct {
	base.UserCommand
}

func (c *C2LWrestleTopListCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleTopList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleTopList Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleTopList user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	if cmsg.Level == 0 {
		c.HandleMapInfo(cmsg)
		return true
	}

	smsg := &cl.L2C_WrestleTopUserList{
		Ret:   uint32(cret.RET_OK),
		Level: cmsg.Level,
	}
	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleTopList failed, wrestleManager not exist", c.Msg.UID)
		smsg.Ret = uint32(cret.RET_ERROR)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleTopUserList, smsg)
	}

	wrestleM.SetTopDataUpdateFinish()

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[Wrestle] user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		smsg.Ret = cmsg.Ret
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleTopUserList, smsg)
		return false
	}

	wrestleM.UpdateTopUsers(cmsg.Level, cmsg.Snapshots[cmsg.Level].Data)

	smsg.TopUsers = wrestleM.GetTopUserByLevel(cmsg.Level)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleTopUserList, smsg)
	return true
}

func (c *C2LWrestleTopListCommand) HandleMapInfo(cmsg *l2c.C2L_WrestleTopList) {

	smsg := &cl.L2C_WrestleMapInfo{
		Ret: uint32(cret.RET_OK),
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleTopList failed, wrestleManager not exist", c.Msg.UID)
		smsg.Ret = uint32(cret.RET_ERROR)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleMapInfo, smsg)
		return
	}
	wrestleM.SetTopDataUpdateFinish()

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[Wrestle] user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		smsg.Ret = cmsg.Ret
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleMapInfo, smsg)
		return
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)

	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleTopList failed, wrestleUser not exist", c.Msg.UID)
		smsg.Ret = uint32(cret.RET_ERROR)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleMapInfo, smsg)
		return
	}

	for level, data := range cmsg.Snapshots {
		wrestleM.UpdateTopUsers(level, data.Data)
	}

	smsg.Users = wrestleM.GetMapTopThree()

	smsg.NowLevel = wrestleUser.GetLevel()
	smsg.LastLevel = wrestleUser.GetLastLevel()
	smsg.LastRoomRank = wrestleUser.GetLastRoomRank()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleMapInfo, smsg)
}

type C2LWrestleLastRankCommand struct {
	base.UserCommand
}

func (c *C2LWrestleLastRankCommand) Error(smsg *cl.L2C_WrestleHallOfFame, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleHallOfFame, smsg)
	return false
}

func (c *C2LWrestleLastRankCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleLastRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleLastRank Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleLastRank user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	smsg := &cl.L2C_WrestleHallOfFame{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("[WrestleLastRank] user:%d recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	now := time.Now().Unix()
	serverDay := c.Srv.ServerDay(now)

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d L2C_WrestleHallOfFame failed, wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	if cmsg.Top50 == nil {
		// 荣誉殿堂没有数据
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleHallOfFame, smsg)
		return true
	}

	wrestleM.UpdateHallOfFame(c.Srv, cmsg.Top50.Snapshots)

	smsg.List, smsg.LikeNum = wrestleM.GetHallOfFameTop3()

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if !goxml.IsWrestleActive(goxml.GetData(), serverDay, now) && wrestleUser != nil {
		smsg.SelfRank = wrestleUser.GetPrevSeasonTotalRank()
		smsg.SelfValue = &cl.RankValue{
			User:  c.User.NewUserSnapshot(uint32(common.FORMATION_ID_FI_CROSS_ARENA_DEFENSE)),
			Value: uint64(wrestleUser.GetPrevSeasonLevelAfterReset()),
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleHallOfFame, smsg)
	return true
}

type C2LWrestleRankFirstCommand struct {
	base.UserCommand
}

func (c *C2LWrestleRankFirstCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleRankFirst{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleRankFirst Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleRankFirst user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	as, exist := c.Srv.GetAsyncMessage(cmsg.ClientMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", cmsg.ClientMsgId)
		return false
	}
	amsg := as.(*base.AsyncMessage)

	rankID := goxml.WrestleRankID

	data := &cl.FirstRankValue{
		RankId:    rankID,
		RankValue: cmsg.Value,
	}

	defer c.Srv.DeleteAsyncMessage(cmsg.ClientMsgId)
	l4g.Infof("C2L_WrestleRankFirst do callback from cmd:%d", amsg.CallCmd)
	ret := amsg.Work(cmsg.Ret, data)
	return ret
}

type C2LWrestleChangeRoomCommand struct {
	base.UserCommand
}

func (c *C2LWrestleChangeRoomCommand) Error(smsg *cl.L2C_WrestleChangeRoom, code uint32) bool {
	smsg.Ret = code
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleChangeRoom, smsg)
	return false
}

func (c *C2LWrestleChangeRoomCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleChangeRoom{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleChangeRoom Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debug("C2L_WrestleChangeRoom user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	smsg := &cl.L2C_WrestleChangeRoom{
		Ret: uint32(cret.RET_OK),
	}

	//跨服返回错误
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		return c.Error(smsg, cmsg.Ret)
	}

	wrestleM, ok := c.Srv.GetActivity(activity.Wrestle).(*awrestle.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: wrestleManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	wrestleUser := wrestleM.Get(c.Msg.UID)
	if wrestleUser == nil {
		l4g.Errorf("user: %d C2L_WrestleChangeRoom: wrestleUser not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	now := time.Now().Unix()
	nextTm := now + goxml.GetData().WrestleConfigInfoM.GetChangeRoomFailedCD()
	if cmsg.Success {
		if cmsg.Fighter == nil {
			l4g.Errorf("user: %d C2L_WrestleChangeRoom: cmsg.Fighter is nil", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}

		nextTm = now + goxml.GetData().WrestleConfigInfoM.GetChangeRoomSuccessCD()
		wrestleUser.SetLevel(cmsg.Fighter.Level)
		wrestleUser.SetScore(cmsg.Fighter.Score)
		wrestleUser.SetTotalRank(cmsg.Fighter.Rank)
		wrestleUser.ResetFailTm()

		smsg.RoomId = cmsg.Fighter.RoomId
		c.addOpponentsToSmsg(cmsg.Defenders, wrestleUser, smsg)
	}
	wrestleUser.SetNextChangeRoomTm(nextTm)
	wrestleM.SetChange(c.Srv, wrestleUser)

	smsg.Success = cmsg.Success
	smsg.NextChangeRoomTm = nextTm
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_WrestleChangeRoom, smsg)
	c.User.LogWrestleChangeRoom(c.Srv, nextTm, smsg.Success)
	return true
}

func (c *C2LWrestleChangeRoomCommand) addOpponentsToSmsg(defenders []*cr.Fighter,
	wrestleUser *awrestle.User, smsg *cl.L2C_WrestleChangeRoom) {
	wrestleUser.ClearMemberIDs()
	for index, defender := range defenders {
		if defender == nil || defender.Snapshot == nil {
			l4g.Errorf("user: %d C2L_WrestleChangeRoom: defender is nil, defenders:%+v",
				c.Msg.UID, defenders)
			continue
		}

		smsg.Opponents = append(smsg.Opponents, fighterToOpponent(wrestleUser, defender))
		if defender.Snapshot.Id == wrestleUser.GetUID() {
			wrestleUser.SetRank(uint32(index + 1))
			continue
		}

		wrestleUser.AddMember(defender.Snapshot.Id)
	}
}

type C2LWrestleRoomUpdateCommand struct {
	base.Command
}

func (c *C2LWrestleRoomUpdateCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.C2L_WrestleRoomUpdate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_WrestleRoomUpdate Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("C2L_WrestleRoomUpdate user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	if len(cmsg.Uids) > 0 {
		c.Srv.BroadCastCmdByUids(cmsg.Uids, cl.ID_MSG_L2C_WrestleRoomUpdate, &cl.L2C_WrestleRoomUpdate{
			Ret: uint32(cret.RET_OK),
		})
	}
	return true
}
