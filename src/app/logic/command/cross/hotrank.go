package cross

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/hotrank"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitHotRank(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_CS2L_HotRankCollectionLogUpdate), &CS2LHotRankCollectionLogUpdateCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_HotRankGet), &CS2LHotRankGetCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_HotRankSyncTalentTreeHot), &CS2LHotRankSyncTalentTreeCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_HotRankFlushTalentTreeHot), &CS2LHotRankFlushTalentTreeHotCommand{}, state)
}

type CS2LHotRankCollectionLogUpdateCommand struct {
	base.Command
}

func (c *CS2LHotRankCollectionLogUpdateCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_HotRankCollectionLogUpdate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_HotRankCollectionLogUpdate Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_HotRankCollectionLogUpdate user:%d recv from cross:%+v", c.Msg.UID, cmsg)

	if cmsg.Ret == uint32(ret.RET_OK) {
		hotRankM := c.Srv.GetActivity(activity.HotRank).(*hotrank.Manager)
		hotRankM.DelLogicRankLog()
	}

	return true
}

type CS2LHotRankGetCommand struct {
	base.UserCommand
}

func (c *CS2LHotRankGetCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_HotRankGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_HotRankGet Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_HotRankGet user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	clientMsg := &cl.L2C_HotRankGet{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user:%d CS2L_HotRankGet: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		clientMsg.Ret = uint32(cmsg.Ret)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_HotRankGet, clientMsg)
		return false
	}

	clientMsg.HotRank = cmsg.HotRank
	clientMsg.NextMergeTime = cmsg.NextMergeTime

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_HotRankGet, clientMsg)

	return true
}

type CS2LHotRankSyncTalentTreeCommand struct {
	base.Command
}

func (c *CS2LHotRankSyncTalentTreeCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_HotRankSyncTalentTreeHot{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_HotRankSyncTalentTreeHot Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_HotRankSyncTalentTreeHot recv from cross:%+v", cmsg)

	if cmsg.Ret == uint32(ret.RET_OK) {
		hotRankM := c.Srv.GetActivity(activity.HotRank).(*hotrank.Manager)
		hotRankM.GetTalentTreeHotM().SetTalentTreeSyncSuccess()
	}
	return true
}

type CS2LHotRankFlushTalentTreeHotCommand struct {
	base.UserCommand
}

func (c *CS2LHotRankFlushTalentTreeHotCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_HotRankFlushTalentTreeHot{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_HotRankFlushTalentTreeHot Unmarshal error: %d %s", c.Msg.UID, err)
		return false
	}

	l4g.Debug("CS2L_HotRankFlushTalentTreeHot user:%d recv from cross:%+v", c.Msg.UID, cmsg)
	clientMsg := &cl.L2C_TalentTreeHot{
		Ret: uint32(ret.RET_OK),
	}

	if cmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user:%d CS2L_HotRankFlushTalentTreeHot: recv cross info error, ret:%d", c.Msg.UID, cmsg.Ret)
		clientMsg.Ret = uint32(cmsg.Ret)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeHot, clientMsg)
		return false
	}
	clientMsg.Nodes = cmsg.Nodes
	clientMsg.NextRefreshTm = goxml.GetTalentTreeHotNextRefreshTm(time.Now().Unix())
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_TalentTreeHot, clientMsg)
	return true
}
