package cross

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/gst"
	aguild "app/logic/activity/guild"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"fmt"

	"gitlab.qdream.com/kit/library/json"

	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/time"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitGSTChallenge(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeGetData), &CS2LGSTChallengeGetDataCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeMatch), &CS2LGSTChallengeMatchCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeFightStart), &CS2LGSTChallengeFightStartCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeFightFinish), &CS2LGSTChallengeFightFinishCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeBuffChoose), &CS2LGSTChallengeBuffChooseCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeTaskReward), &CS2LGSTChallengeTaskRewardCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeRank), &CS2LGSTChallengeRankCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeAward), &CS2LGSTChallengeAwardCommand{}, state)
	cmds.Register(uint32(l2c.ID_MSG_CS2L_GSTChallengeTaskUpdate), &CS2LGSTChallengeTaskUpdateCommand{}, state)
}

type CS2LGSTChallengeGetDataCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeGetDataCommand) Return(msg *cl.L2C_GSTChallengeGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeGetData, msg)
	return true
}

func (c *CS2LGSTChallengeGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeGetData{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GSTChallengeGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	if cmsg.Resp.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("CS2L_GSTChallengeGetData cross error code . uid:%d code:%d", c.Msg.UID, cmsg.Resp.Ret)
		return c.Return(cmsg.Resp, cmsg.Resp.Ret)
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d CS2L_GSTChallengeGetData gst manager is nil", c.Msg.UID)
		return c.Return(cmsg.Resp, uint32(cret.RET_ERROR))
	}
	gstUser := gstM.GetUser(c.Msg.UID)
	if gstUser == nil {
		l4g.Error("user:%d CS2L_GSTChallengeGetData gstUser is nil", c.Msg.UID)
		return c.Return(cmsg.Resp, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	if cmsg.Resp.User != nil && cmsg.Resp.User.MatchRound == 0 {
		gstUser.SetChallengeFormationLimit(false)
		if gstUser.GetLogIndex() > 0 {
			myLogM := gstM.GetChallengeLogM().GetOwnerLogs(gstUser)
			myLogM.DelAllLog(gstM.GetChallengeLogM())
			gstUser.SetLogIndex(0)
		}
		gstM.SetChange(gstUser)
	}

	formation := c.User.GetFormation(uint32(common.FORMATION_ID_FI_GST_CHALLENGE))
	if formation == nil {
		gstFormation := c.User.GetFormation(uint32(common.FORMATION_ID_FI_GST))
		if gstFormation == nil {
			l4g.Errorf("user:%d C2L_GSTChallengeGetData gstFormation nil", c.Msg.UID)
		} else {
			formation = gstFormation.Clone()
			c.User.FormationManager().NewFormation(c.Srv, uint32(common.FORMATION_ID_FI_GST_CHALLENGE), formation)
		}
	}

	ok, num := c.User.GetTeamNum(uint32(common.FORMATION_ID_FI_GST_CHALLENGE))
	if !ok {
		l4g.Errorf("user:%d C2L_GSTChallengeGetData: get teamNum is 0", c.Msg.UID)
	} else {
		if formation != nil && len(formation.Teams) > num && !gstUser.GetChallengeFormationLimit() {
			newFormation := formation.Clone()
			newFormation.Teams = newFormation.Teams[0:num]
			c.User.FormationManager().NewFormation(c.Srv, uint32(common.FORMATION_ID_FI_GST_CHALLENGE), newFormation)
		}
	}

	cmsg.Resp.SelfTeams = make([]*cl.GSTChallengeTeamInfo, 0, len(cmsg.Resp.User.MatchData))
	for _, info := range cmsg.Resp.GetUser().GetMatchData() {
		cmsg.Resp.SelfTeams = append(cmsg.Resp.SelfTeams, c.User.GenerateGSTChallengeTeam(info.Index))
	}
	return c.Return(cmsg.Resp, cmsg.Resp.Ret)
}

type CS2LGSTChallengeMatchCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeMatchCommand) Return(msg *cl.L2C_GSTChallengeMatch, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeMatch, msg)
	return true
}

func (c *CS2LGSTChallengeMatchCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeMatch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeMatch Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeMatch{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GSTChallengeMatch: cmsg:%s", c.Msg.UID, cmsg)
	// edit here

	if cmsg.Resp.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("CS2L_GSTChallengeMatch cross error code . uid:%d code:%d", c.Msg.UID, cmsg.Resp.Ret)
		return c.Return(cmsg.Resp, cmsg.Resp.Ret)
	}

	cmsg.Resp.SelfTeams = make([]*cl.GSTChallengeTeamInfo, 0, len(cmsg.Resp.Data.MatchData))
	for _, info := range cmsg.Resp.GetData().GetMatchData() {
		cmsg.Resp.SelfTeams = append(cmsg.Resp.SelfTeams, c.User.GenerateGSTChallengeTeam(info.Index))
	}

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if gstM == nil {
		l4g.Error("user:%d CS2L_GSTChallengeMatch gst manager is nil", c.Msg.UID)
	} else {
		gstUser := gstM.GetUser(c.Msg.UID)
		if gstUser != nil {
			if !gstUser.GetChallengeFormationLimit() && cmsg.Resp.Data != nil && cmsg.Resp.Data.MatchRound != 0 {
				gstUser.SetChallengeFormationLimit(true)
				gstM.SetChange(gstUser)
			}
		}
	}
	return c.Return(cmsg.Resp, cmsg.Resp.Ret)
}

type CS2LGSTChallengeFightStartCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeFightStartCommand) Return(msg *cl.L2C_GSTChallengeFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, msg)
	return true
}

func (c *CS2LGSTChallengeFightStartCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeFightStart{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeFightStart Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeFight{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GSTChallengeFightStart: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTChallengeFight{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d CS2L_GSTChallengeFightStart cross resp error. ret:%d ", c.User.ID(), cmsg.Ret)
		return c.Return(smsg, cmsg.Ret)
	}

	isBot := cmsg.Match.Bot != nil

	if isBot {
		// 和机器人打
		botInfo := goxml.GetData().GuildSandTableChallengeBotInfoM.GetRecordById(uint32(cmsg.Match.Bot.Id))
		if botInfo == nil {
			l4g.Errorf("user: %d CS2L_GSTChallengeFightStart botInfo not exit. botId:%d ", c.User.ID(), cmsg.Match.Bot.Id)
			return c.Return(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		retCode, reportId := c.User.GSTChallengePVEFight(c.Srv, cmsg, c.User.GetClientData(common.FUNCID_MODULE_GST_CHALLENGE))
		if retCode != cret.RET_OK {
			l4g.Errorf("user: %d CS2L_GSTChallengeFightStart error. ret:%d ", c.User.ID(), retCode)
			return c.Return(smsg, uint32(retCode))
		}
		fightLog := &cl.GSTChallengeFightLog{
			LogId:      c.Srv.CreateUserLogID(),
			Attacker:   c.User.GenerateGSTChallengeTeam(cmsg.Match.Index),
			Defender:   cmsg.Defender,
			ReportId:   reportId,
			Match:      cmsg.Match.Clone(),
			Tm:         time.Now().Unix(),
			Bot:        true,
			MatchRound: cmsg.User.MatchRound,
		}
		return c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTChallengeFightFinish, c.Msg.UID, &l2c.L2CS_GSTChallengeFightFinish{
			FightLog: fightLog,
		})
	}

	// 和真人打
	req := &AsyncL2CSGSTChallengeStartReq{
		cmsg: cmsg,
	}
	reqUsers := []*cl.ServerUser{{Sid: cmsg.Defender.UserInfo.Sid, Uid: cmsg.Defender.UserInfo.Id}}
	c.GetUserBattleDataFromAll(reqUsers, req, uint32(common.FORMATION_ID_FI_GST))
	return true
}

type AsyncL2CSGSTChallengeStartReq struct {
	cmsg *l2c.CS2L_GSTChallengeFightStart
}

func (ar *AsyncL2CSGSTChallengeStartReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsgToClient := &cl.L2C_GSTChallengeFight{
		Ret: uint32(cret.RET_OK),
	}
	if retCode != uint32(cret.RET_OK) {
		smsgToClient.Ret = retCode
		l4g.Errorf("user: %d AsyncL2CSGSTChallengeStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, smsgToClient)
		return true
	}

	snaps, ok := dbData.([]*db.UserBattleData)
	if !ok {
		smsgToClient.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("user: %d AsyncL2CSGSTChallengeStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, smsgToClient)
		return true
	}

	snapm := make(map[uint64]*db.UserBattleData, len(snaps))
	for _, v := range snaps {
		if v == nil {
			l4g.Errorf("user: %d AsyncL2CSGSTChallengeStartReq: no data. opUid:%d opSid:%d", args.UID,
				ar.cmsg.Defender.UserInfo.Id, ar.cmsg.Defender.UserInfo.Sid)
			smsgToClient.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, smsgToClient)
			return false
		}
		snapm[v.User.Id] = v
	}

	if snapm[ar.cmsg.Defender.UserInfo.Id] == nil {
		l4g.Errorf("user: %d AsyncL2CSGSTChallengeStartReq: no data. opUid:%d opSid:%d", args.UID,
			ar.cmsg.Defender.UserInfo.Id, ar.cmsg.Defender.UserInfo.Sid)
		smsgToClient.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, smsgToClient)
		return false
	}

	opUser := character.GetUserFromUserBattleData(srv, snapm[ar.cmsg.Defender.UserInfo.Id])
	if opUser == nil {
		smsgToClient.Ret = uint32(cret.RET_ERROR)
		l4g.Errorf("user: %d AsyncL2CSGSTChallengeStartReq error. ret:%d ", args.UID, retCode)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, smsgToClient)
		return true
	}

	attackRet, reportID := args.Caller.GSTChallengePVPFight(srv, ar.cmsg, opUser, args.Caller.GetClientData(common.FUNCID_MODULE_GST_CHALLENGE))
	if attackRet != cret.RET_OK {
		smsgToClient.Ret = uint32(cret.RET_BATTLE_ERROR)
		l4g.Errorf("user: %d AsyncL2CSGSTChallengeStartReq error. ret:%d ", args.UID, attackRet)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, smsgToClient)
		return true
	}
	fightLog := &cl.GSTChallengeFightLog{
		LogId:      srv.CreateUserLogID(),
		Attacker:   args.Caller.GenerateGSTChallengeTeam(ar.cmsg.Match.Index),
		Defender:   ar.cmsg.Defender,
		ReportId:   reportID,
		Match:      ar.cmsg.Match.Clone(),
		Tm:         time.Now().Unix(),
		Bot:        false,
		MatchRound: ar.cmsg.User.MatchRound,
	}
	return srv.SendCmdToCross(l2c.ID_MSG_L2CS_GSTChallengeFightFinish, args.Caller.ID(), &l2c.L2CS_GSTChallengeFightFinish{
		FightLog: fightLog,
	})
}

type CS2LGSTChallengeFightFinishCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeFightFinishCommand) Return(msg *cl.L2C_GSTChallengeFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeFight, msg)
	return true
}

func (c *CS2LGSTChallengeFightFinishCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeFightFinish{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeFightFinish Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeFight{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GSTChallengeFightFinish: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_GSTChallengeFight{
		Ret: uint32(cret.RET_OK),
	}

	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d CS2L_GSTChallengeFightFinish cross resp error. ret:%d ", c.User.ID(), cmsg.Ret)
		return c.Return(smsg, cmsg.Ret)
	}

	gstM, ok := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if !ok {
		l4g.Errorf("user: %d CS2L_GSTChallengeFightFinish gstManager not exist.", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	gstUser := gstM.GetUser(c.User.ID())
	if gstUser == nil {
		l4g.Errorf("user: %d CS2L_GSTChallengeFightFinish get Gst user failed", c.Msg.UID)
		return c.Return(smsg, uint32(cret.RET_GST_GUILD_LEVEL_IS_NOT_OPEN))
	}

	if cmsg.MatchRoundEnd {
		smsg.Awards = make([]*cl.Resource, 0, len(cmsg.Challenge.Wins))
		for index, num := range cmsg.Challenge.Wins {
			matchRound := uint32(index + 1)
			matchInfo := goxml.GetData().GuildSandTableChallengeMatchInfoM.GetRecordByRound(matchRound)
			if matchInfo == nil {
				l4g.Errorf("user: %d CS2L_GSTChallengeFightFinish get match Info nil. matchRound:%d ", c.User.ID(), matchRound)
				continue
			}
			for i := uint32(0); i < num; i++ {
				res, ok := c.User.Drop().DoDrop(c.Srv.Rand(), matchInfo.DropGroup)
				if ok {
					smsg.Awards = append(smsg.Awards, res...)
				}
			}
		}
		retCode, _ := c.User.Award(c.Srv, smsg.Awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GST_CHALLENGE_BOX_REWARD), 0)
		if retCode != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d CS2L_GSTChallengeFightFinish award failed. ret:%d ", c.User.ID(), retCode)
		}

		// 处理聊天消息推送
		c.handleChatMessage(cmsg.Challenge, gstUser)

		gstUser.SetChallengeFormationLimit(false)
	}

	myLogM := gstM.GetChallengeLogM().GetOwnerLogs(gstUser)
	myLogM.AddLog(c.Srv, gst.NewGstChallengeLog(cmsg.FightLog))
	gstM.GetChallengeLogM().SetChange(myLogM)
	gstM.SetChange(gstUser)

	smsg.MatchInfo = cmsg.FightLog.Match.Clone()
	smsg.Score = cmsg.Challenge.Score
	smsg.Wins = make([]uint32, len(cmsg.Challenge.Wins))
	copy(smsg.Wins, cmsg.Challenge.Wins)
	smsg.MaxContinueWin = cmsg.Challenge.MaxContinueWin
	smsg.RoomRank = cmsg.RoomRank
	if cmsg.LogData != nil {
		c.User.LogGSTChallengeFight(c.Srv, cmsg.LogData)
	}
	c.Return(smsg, smsg.Ret)
	return true
}

func (c *CS2LGSTChallengeFightFinishCommand) handleChatMessage(challenge *cl.GSTGuildUserChallenge, gstUser *gst.GstUser) {
	if challenge.MaxContinueWin < goxml.GetData().GuildSandTableConfigInfoM.GetChallengeShowNumChat() {
		return
	}
	teamIndex := uint32(0)
	var findOk bool
	for _, data := range challenge.MatchData {
		if data.WinTimes >= goxml.GetData().GuildSandTableConfigInfoM.GetChallengeShowNumChat() {
			teamIndex = data.Index
			findOk = true
			break
		}
	}

	if !findOk {
		return
	}

	guildM, ok := c.Srv.GetActivity(activity.Guild).(*aguild.Manager)
	if !ok {
		l4g.Errorf("user: %d handleChatMessage: failed, guildManager not exist.", c.Msg.UID)
		return
	}
	guildUser := guildM.GetGuildUser(c.User.ID())
	if guildUser == nil {
		l4g.Errorf("user: %d handleChatMessage: guildUser not exist.", c.Msg.UID)
		return
	}
	guild := guildM.GetGuildByID(guildUser.GuildID())
	if guild == nil {
		l4g.Errorf("user:%d handleChatMessage: guild:%d is nil", c.Msg.UID, guildUser.GuildID())
		return
	}

	teamInfo := c.User.GenerateGSTChallengeTeam(teamIndex)
	newTeamInfo := &cl.GSTFormationTeamInfo{
		Heros: teamInfo.Heroes,
	}
	teamParam, _ := json.MarshalToString(newTeamInfo)

	params := []string{c.User.Name(), fmt.Sprintf("%d", c.User.BaseID()), fmt.Sprintf("%d", challenge.MaxContinueWin),
		"0", guild.Name(), teamParam}
	gstMessage := character.NewMsg(character.Id(c.Srv), character.Type(goxml.ChatGstChallengeWins),
		character.Params(params...))
	character.ChatGstPushMessageToPlatform(c.Srv, gstUser.ChatRoomId, 0, gstMessage)
}

type CS2LGSTChallengeBuffChooseCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeBuffChooseCommand) Return(msg *cl.L2C_GSTChallengeBuffChoose, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeBuffChoose, msg)
	return true
}

func (c *CS2LGSTChallengeBuffChooseCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeBuffChoose{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeBuffChoose Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeBuffChoose{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GSTChallengeBuffChoose: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	return c.Return(cmsg.Resp, cmsg.Resp.Ret)
}

type CS2LGSTChallengeTaskUpdateCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeTaskUpdateCommand) Return(msg *cl.L2C_GSTChallengeTaskUpdate, retCode uint32) bool {
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeTaskUpdate, msg)
	return true
}

func (c *CS2LGSTChallengeTaskUpdateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.L2C_GSTChallengeTaskUpdate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("L2C_GSTChallengeTaskUpdate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeTaskUpdate{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d L2C_GSTChallengeTaskUpdate: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	return c.Return(cmsg, uint32(cret.RET_OK))
}

type CS2LGSTChallengeTaskRewardCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeTaskRewardCommand) Return(msg *cl.L2C_GSTChallengeTaskReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeTaskReward, msg)
	return true
}

func (c *CS2LGSTChallengeTaskRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeTaskReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeTaskReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeTaskReward{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GSTChallengeTaskReward: cmsg:%s", c.Msg.UID, cmsg)
	if cmsg.Resp.Ret != uint32(cret.RET_OK) {
		return c.Return(cmsg.Resp, cmsg.Resp.Ret)
	}

	totalAward := make([]*cl.Resource, 0, len(cmsg.Resp.TaskIds))
	for _, taskID := range cmsg.Resp.TaskIds {
		taskInfo := goxml.GetData().GuildSandTableChallengeTaskInfoM.GetRecordById(taskID)
		if taskInfo == nil {
			l4g.Errorf("CS2L_GSTChallengeTaskReward user:%d taskID:%d info is nil", c.Msg.UID, taskID)
			return c.Return(cmsg.Resp, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		for _, reward := range taskInfo.Rewards {
			totalAward = append(totalAward, reward.Clone())
		}
	}

	if len(totalAward) > 0 {
		cmsg.Resp.Ret, cmsg.Resp.Awards = c.User.Award(c.Srv, totalAward, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_GST_CHALLENGE_TASK), 0)
		if cmsg.Resp.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("CS2L_GSTChallengeTaskReward user:%d award failed code:%d", c.Msg.UID, cmsg.Resp.Ret)
			return c.Return(cmsg.Resp, cmsg.Resp.Ret)
		}
	}
	logData := &log.LogGSTChallengeTaskAward{
		TaskId: cmsg.Resp.TaskIds,
	}
	c.User.LogGSTChallengeTaskAward(c.Srv, logData)
	return c.Return(cmsg.Resp, cmsg.Resp.Ret)
}

type CS2LGSTChallengeRankCommand struct {
	base.UserCommand
}

func (c *CS2LGSTChallengeRankCommand) Return(msg *cl.L2C_GSTChallengeRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_GSTChallengeRank, msg)
	return true
}

func (c *CS2LGSTChallengeRankCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return c.Return(&cl.L2C_GSTChallengeRank{}, uint32(cret.RET_ERROR))
	}
	c.Trace(cmsg)
	l4g.Debugf("user: %d CS2L_GSTChallengeRank: cmsg:%s", c.Msg.UID, cmsg)
	if cmsg.Resp.Ret != uint32(cret.RET_OK) {
		return c.Return(cmsg.Resp, cmsg.Resp.Ret)
	}

	return c.Return(cmsg.Resp, cmsg.Resp.Ret)
}

type CS2LGSTChallengeAwardCommand struct {
	base.Command
}

func (c *CS2LGSTChallengeAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &l2c.CS2L_GSTChallengeAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("CS2L_GSTChallengeAward Unmarshal error: %s", err)
		return false
	}
	l4g.Info("CS2L_GSTChallengeAward recv from cross:%+v", cmsg)

	gstM := c.Srv.GetActivity(activity.GST).(*gst.Manager)
	if cmsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("CS2L_GSTChallengeAward: recv cross get sta failed")
		return false
	}

	if !gstM.CanChallengeAward(cmsg.SeasonId, cmsg.Round) {
		l4g.Errorf("CS2L_GSTChallengeAward: cmsg.Season:%d cmsg.Round:%d ", cmsg.SeasonId, cmsg.Round)
		return false
	}

	nowTm := time.Now().Unix()
	if cmsg.BakData != nil {
		for rank, userIds := range cmsg.BakData.UserRank {

			info := goxml.GetData().GuildSandTableChallengeRankRewardInfoM.GetRecordByRankMinMaxLe(rank)
			if info == nil {
				l4g.Errorf("CS2L_GSTChallengeAward: get challenge rank reward info nil. rank:%d", rank)
				continue
			}
			if rank > info.RankMax {
				continue
			}
			if len(info.Rewards) > 0 {
				rewards := c.getNewReward(info.Rewards, nowTm)
				character.GSTChallengeSettlementMail(c.Srv, character.MailIDGSTChallengeTotalScoreRank, []string{fmt.Sprintf("%d", rank)},
					rewards, userIds.GetIds())
			}
		}
	} else {
		l4g.Errorf("CS2L_GSTChallengeAward: cross bak data is nil. seasonId:%d round:%d", cmsg.SeasonId, cmsg.Round)
	}
	gstM.SetChallengeAwardStage(cmsg.SeasonId, cmsg.Round)
	return true
}

func (c *CS2LGSTChallengeAwardCommand) getNewReward(rewards []*cl.Resource, now int64) []*cl.Resource {
	newRewards := make([]*cl.Resource, 0, len(rewards))
	for _, reward := range rewards {
		newReward := reward.Clone()
		if newReward.Type == uint32(common.RESOURCE_AVATAR) {
			avatarInfo := goxml.GetData().AvatarInfoM.Index(newReward.Value)
			if avatarInfo == nil {
				l4g.Errorf("CS2LGSTChallengeAwardCommand.getNewReward: avatarInfo not exist, id:%d", newReward.Value)
				continue
			}
			if avatarInfo.DurationType == goxml.AvatarTimeTypeEndTime {
				newReward.Attrs = []*cl.Attr{{
					Value: now + int64(avatarInfo.Duration*util.MinuteSecs),
				}}
			}
		}
		newRewards = append(newRewards, newReward)
	}

	return newRewards
}
