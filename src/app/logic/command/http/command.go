package http

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/logic/platform"
	"app/protos/in/log"
	"app/protos/in/p2l"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"context"
	"encoding/json"

	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(p2l.ID_MSG_P2L_UseGiftCode), &P2LUseGiftCodeCommand{}, state)     //平台返回使用礼包码的信息
	cmds.Register(uint32(p2l.ID_MSG_P2L_ChatGetToken), &P2LChatGetTokenCommand{}, state)   //聊天 - 返回的token
	cmds.Register(uint32(p2l.ID_MSG_P2L_ChatJoinGroup), &P2LChatJoinGroupCommand{}, state) //聊天 - 加入,退出群组
	//cmds.Register(uint32(p2l.ID_MSG_P2L_ChatDismissGroup), &P2LChatDismissGroupCommand{}, state) //聊天 - 退出群组
	cmds.Register(uint32(p2l.ID_MSG_P2L_GetSeasonFlashBackData), &P2LChatGetSeasonFlashBackDataCommand{}, state) // 获取赛季回顾数据
	cmds.Register(uint32(p2l.ID_MSG_P2L_SensitiveWordCheck), &P2LSensitiveWordCheckCommand{}, state)
	cmds.Register(uint32(p2l.ID_MSG_P2L_RebaseGet), &P2LRebaseGetCommand{}, state)
	cmds.Register(uint32(p2l.ID_MSG_P2L_MuteAccount), &P2LMuteAccountCommand{}, state)
	cmds.Register(uint32(p2l.ID_MSG_P2L_GetLog), &P2LGetLogCommand{}, state)
}

type BaseCommand struct {
	msg *platform.HTTPResponse
}

func (b *BaseCommand) OnPause(ctx context.Context, msg parse.Messager) {
}

func (b *BaseCommand) OnBefore(ctx context.Context, msg parse.Messager) bool {
	b.msg = msg.(*platform.HTTPResponse)
	return true
}

func (b *BaseCommand) OnAfter(ctx context.Context, result bool) {
}

type P2LUseGiftCodeCommand struct {
	BaseCommand
}

func (p *P2LUseGiftCodeCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_UseGiftCode)
	l4g.Debugf("P2LUseGiftCodeCommand recv: %+v", pmsg)

	srv := base.GetService(ctx)
	user := srv.UserM().GetUserWithCache(p.msg.PackHead.UID)
	if user == nil {
		l4g.Errorf("user: %d P2LUseGiftCodeCommand: user not find.", p.msg.PackHead.UID)
		if pmsg.Ret == uint32(ret.RET_OK) {
			l4g.Infof("user: %d P2LUseGiftCodeCommand: offline user gift code award id: %d", p.msg.PackHead.UID, pmsg.AwardId)
		}
		return false
	}

	smsg := &cl.L2C_GetGiftCodeAward{
		Ret: uint32(ret.RET_OK),
	}

	if pmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d P2LUseGiftCodeCommand: use gift code error. ret:%d", user.ID(), pmsg.Ret)
		smsg.Ret = pmsg.Ret
		user.SendCmdToGateway(cl.ID_MSG_L2C_GetGiftCodeAward, smsg)
		return false
	}

	awards := srv.GetGiftCodeAwards(pmsg.AwardId)
	if len(awards) == 0 {
		l4g.Errorf("user: %d P2LUseGiftCodeCommand: get awards error. awardId:%d", p.msg.PackHead.UID, pmsg.AwardId)
		smsg.Ret = uint32(ret.RET_CLIENT_REQUEST_ERROR)
		user.SendCmdToGateway(cl.ID_MSG_L2C_GetGiftCodeAward, smsg)
		return false
	}

	//这里要使用AwardTagMail
	//礼包码使用流程 client->logic(1)->plat->logic(2)->client
	//在logic(1)的过程中，即使检查了背包，但平台异步回调过来的时候，即logic(2)的过程，可能背包又满了
	//如果在logic(2)发奖失败，会导致在plat上标记已领奖，但实际发奖失败
	if smsg.Ret, smsg.Awards = user.Award(srv, awards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_GIFT_CODE), 0); smsg.Ret == uint32(ret.RET_OK) {
		user.LogGetGiftCodeAward(srv, pmsg.Code)
	}

	user.SendCmdToGateway(cl.ID_MSG_L2C_GetGiftCodeAward, smsg)
	return true
}

type P2LChatGetTokenCommand struct {
	BaseCommand
}

func (p *P2LChatGetTokenCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_ChatGetToken)
	l4g.Debugf("P2L_ChatGetToken recv: %+v", pmsg)

	srv := base.GetService(ctx)
	user := srv.UserM().GetUser(p.msg.PackHead.UID)
	if user == nil {
		l4g.Errorf("user: %d P2L_ChatGetToken: user not find.", p.msg.PackHead.UID)
		return false
	}
	smsg := &cl.L2C_ChatGetToken{
		Ret:        uint32(ret.RET_OK),
		Token:      pmsg.Token,
		ExpireTime: pmsg.ExpireTime,
	}

	if pmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d P2L_ChatGetToken: get token failed from chat server. err: %+v", user.ID(), pmsg.FailedTagErrors)

		smsg.Ret = pmsg.Ret
		user.SendCmdToGateway(cl.ID_MSG_L2C_ChatGetToken, smsg)
		return false
	}
	chatGroupTag := user.ChatGroupTag()
	if len(pmsg.SuccessTags) > 0 {
		chatGroupTag.AddGroupTag(pmsg.SuccessTags)
	}
	chatGroupTag.UpdateCrossArea(pmsg.CrossArea)
	chatGroupTag.UpdateLangMaxPeople(pmsg.Lang)
	guildID := srv.GetGuildID(user.ID())
	if guildID > 0 {
		chatGroupTag.UpdateGuildID(guildID)
	}

	if pmsg.IsHand {
		user.SendCmdToGateway(cl.ID_MSG_L2C_ChatGetToken, smsg)
	}

	return true
}

type P2LChatJoinGroupCommand struct {
	BaseCommand
}

func (p *P2LChatJoinGroupCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_ChatJoinGroup)
	l4g.Debugf("P2L_ChatJoinGroup recv: %+v", pmsg)

	srv := base.GetService(ctx)
	user := srv.UserM().GetUser(p.msg.PackHead.UID)
	if user == nil {
		l4g.Errorf("user: %d P2L_ChatJoinGroup: user not find.", p.msg.PackHead.UID)
		return false
	}
	chatGroupTag := user.ChatGroupTag()
	if pmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d P2L_ChatJoinGroup: join chat group failed from chat server. ret:%d", pmsg.Uid, pmsg.Ret)
		chatGroupTag.DelGroupTag([]string{pmsg.GroupTag})
		return false
	}

	if pmsg.GroupOp == uint32(character.ChatGroupOpQuit) {
		chatGroupTag.DelGroupTag([]string{pmsg.GroupTag})
		chatGroupTag.UpdateGuildID(0)
	} else {
		chatGroupTag.AddGroupTag(pmsg.SuccessTags)
		chatGroupTag.UpdateGuildID(srv.GetGuildID(user.ID()))
	}

	user.SendCmdToGateway(cl.ID_MSG_L2C_ChatGroupUpdateNotify, &cl.L2C_ChatGroupUpdateNotify{
		GroupTag: pmsg.GroupTag,
	})

	return true
}

//type P2LChatDismissGroupCommand struct {
//	BaseCommand
//}
//
//func (p *P2LChatDismissGroupCommand) Execute(ctx context.Context) bool {
//	pmsg := p.msg.Data.(*p2l.P2L_ChatDismissGroup)
//	l4g.Debugf("P2L_ChatDismissGroup recv: %+v", pmsg)
//	if pmsg.Ret != uint32(ret.RET_OK) {
//		l4g.Errorf("user: %d P2L_ChatDismissGroup: quit chat group failed from chat server. ret:%d", pmsg.Uid, pmsg.Ret)
//		return false
//	}
//
//	srv := base.GetService(ctx)
//	srv.GetChatUserCache().DelGroupTag(pmsg.Uid, []string{pmsg.GroupTag})
//
//	return true
//}

type P2LChatGetSeasonFlashBackDataCommand struct {
	BaseCommand
}

func (p *P2LChatGetSeasonFlashBackDataCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_GetSeasonFlashBackData)
	l4g.Debugf("P2L_GetSeasonFlashBackData recv: %+v", pmsg)
	srv := base.GetService(ctx)
	user := srv.UserM().GetUserWithCache(p.msg.PackHead.UID)
	if user == nil {
		l4g.Errorf("user: %d P2L_GetSeasonFlashBackData: user not find.", p.msg.PackHead.UID)
		if pmsg.Ret == uint32(ret.RET_OK) {
			l4g.Infof("user: %d P2L_GetSeasonFlashBackData: offline user .", p.msg.PackHead.UID)
		}
		return false
	}

	smsg := &cl.L2C_GetSeasonFlashBackData{
		Ret:      pmsg.Ret,
		SeasonId: pmsg.SeasonId,
		Points:   pmsg.Points,
		Ids:      pmsg.Ids,
	}

	if pmsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d P2L_GetSeasonFlashBackData: . ret:%d", pmsg.Uid, pmsg.Ret)
		smsg.Ret = pmsg.Ret
		user.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonFlashBackData, smsg)
		return false
	}

	user.SendCmdToGateway(cl.ID_MSG_L2C_GetSeasonFlashBackData, smsg)
	return true
}

type P2LSensitiveWordCheckCommand struct {
	BaseCommand
}

func (p *P2LSensitiveWordCheckCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_SensitiveWordCheck)
	l4g.Debugf("P2LSensitiveWordCheckCommand recv: %+v", pmsg)
	srv := base.GetService(ctx)
	as, exist := srv.GetAsyncMessage(pmsg.AsyncMsgId)
	if !exist {
		l4g.Errorf("no found async message: %d", pmsg.AsyncMsgId)
		return false
	}
	defer srv.DeleteAsyncMessage(pmsg.AsyncMsgId)
	amsg := as.(*base.AsyncMessage)
	amsg.Work(pmsg.Ret, nil)
	return true
}

type P2LRebaseGetCommand struct {
	BaseCommand
}

func (p *P2LRebaseGetCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_RebaseGet)
	l4g.Debugf("P2LRebaseGetCommand recv: %+v", pmsg)

	srv := base.GetService(ctx)
	user := srv.UserM().GetUserWithCache(p.msg.PackHead.UID)
	if user == nil {
		l4g.Errorf("user: %d P2LRebaseGetCommand: user not find.", p.msg.PackHead.UID)
		if pmsg.Ret == uint32(ret.RET_OK) {
			l4g.Infof("user: %d P2LRebaseGetCommand: offline user rebase get", p.msg.PackHead.UID)
		}
		return false
	}

	if pmsg.Ret == uint32(ret.RET_OK) {
		if pmsg.Operated == goxml.RebaseOperatedAwardAndSet {
			now := time.Now().Unix()
			if !user.RebaseCheck(now, srv) {
				return false
			}

			if pmsg.GameRecvId != user.ID() || pmsg.GameServer != srv.ServerID() {
				user.SetHasRebase(true)
				return true
			}

			if pmsg.RechargeNum > 0 {
				reward := goxml.GetData().RechargeRefundInfoM.CalcReward(pmsg.RechargeNum)
				if len(reward) > 0 {
					reward = user.MergeResources(reward)
					character.RebaseMail(srv, user, reward, character.MailIDRebase, pmsg.RechargeNum)
				}
			}
			user.SetHasRebase(true)
		} else if pmsg.Operated == goxml.RebaseOperatedSet {
			user.SetHasRebase(true)
		}
	} else {
		return false
	}

	return true
}

type P2LMuteAccountCommand struct {
	BaseCommand
}

func (p *P2LMuteAccountCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_MuteAccount)
	l4g.Debugf("P2L_MuteAccount recv: %+v", pmsg)

	srv := base.GetService(ctx)
	user := srv.UserM().GetUserWithCache(p.msg.PackHead.UID)
	if user == nil {
		l4g.Errorf("user: %d P2LMuteAccounnt: user not find.", p.msg.PackHead.UID)
		if pmsg.Ret == uint32(ret.RET_OK) {
			l4g.Infof("user: %d P2LMuteAccounnt: offline user mute account", p.msg.PackHead.UID)
		}
		return false
	}

	muteAccountRet := &cl.L2C_MuteAccount{
		Ret: pmsg.Ret,
	}
	if pmsg.Ret == uint32(ret.RET_OK) {
		user.SendCmdToGateway(cl.ID_MSG_L2C_MuteAccount, muteAccountRet)
		user.LogMuteAccount(srv, pmsg.Baner, pmsg.Uid, uint64(pmsg.StartTime), uint64(pmsg.EndTime))
		return true
	} else {
		user.SendCmdToGateway(cl.ID_MSG_L2C_MuteAccount, muteAccountRet)
		return false
	}
}

type P2LGetLogCommand struct {
	BaseCommand
}

func (p *P2LGetLogCommand) Execute(ctx context.Context) bool {
	pmsg := p.msg.Data.(*p2l.P2L_GetLog)
	l4g.Debugf("P2LGetLogCommand recv: %+v", pmsg)

	srv := base.GetService(ctx)
	user := srv.UserM().GetUserWithCache(p.msg.PackHead.UID)
	if user == nil {
		l4g.Errorf("user: %d P2LGetLogCommand: user not find.", p.msg.PackHead.UID)
		return false
	}
	p.OpMsg(pmsg, user)
	return true
}

func (p *P2LGetLogCommand) OpMsg(msg *p2l.P2L_GetLog, user *character.User) {
	switch msg.LogType {
	case cl.GetLogType_GetLogType_Test:
		smsg := &cl.L2C_TestGetLog{
			Ret: uint32(ret.RET_OK),
			Rsp: msg.Rsp,
		}
		//p.parseMongoUserInfo(msg.Rsp.GetDatas())
		p.parseEsUserInfo(msg.Rsp.GetDatas())
		user.SendCmdToGateway(cl.ID_MSG_L2C_TestGetLog, smsg)
	}
}

func (p *P2LGetLogCommand) parseMongoUserInfo(datas []string) {
	for _, data := range datas {
		userInfo := &log.MongoUserInfo{}
		err := json.Unmarshal([]byte(data), userInfo)
		if err != nil {
			l4g.Errorf("parseMongoUserInfo err. %s", err.Error())
		}
		l4g.Debugf("parseMongoUserInfo %+v name %s", userInfo, userInfo.Name)
	}
}

func (p *P2LGetLogCommand) parseEsUserInfo(datas []string) {
	for _, data := range datas {
		userInfo := &log.ESUserInfo{}
		err := json.Unmarshal([]byte(data), userInfo)
		if err != nil {
			l4g.Errorf("parseEsUserInfo err. %s", err.Error())
		}
		l4g.Debugf("parseEsUserInfo %+v name %s", userInfo, userInfo.Name)
	}
}
