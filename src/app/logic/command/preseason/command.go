package preseason

import (
	"app/goxml"
	"app/logic/character"
	"app/protos/out/common"
	"context"
	"time"

	"app/logic/command/base"
	"app/protos/in/log"
	"app/protos/out/cl"

	// "app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_PreSeasonGetData), &C2LPreSeasonGetDataCommand{}, state)     //  MSG_C2L_PreSeasonGetData = 18801;
	cmds.Register(uint32(cl.ID_MSG_C2L_PreSeasonRecvAward), &C2LPreSeasonRecvAwardCommand{}, state) //  MSG_C2L_PreSeasonRecvAward = 18803;
}

type C2LPreSeasonGetDataCommand struct {
	base.UserCommand
}

func (c *C2LPreSeasonGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPreSeasonGetDataCommand) Error(msg *cl.L2C_PreSeasonGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PreSeasonGetData, msg)
	return false
}

func (c *C2LPreSeasonGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PreSeasonGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PreSeasonGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PreSeasonGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_PreSeasonGetData{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_PRE_SEASON), c.Srv) {
		l4g.Errorf("user: %d C2L_PreSeasonGetData: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if c.User.PreSeason().IsOpen(c.Srv) {
		c.Srv.UserM().AddLastLoginUserName(c.User.Name())
		smsg.PreSeason = c.User.PreSeason().Flush2Client()
		smsg.Name = c.Srv.UserM().GetLastLoginUserNameClone(c.User.Name())
	} else {
		smsg.PreSeason = c.User.PreSeason().Flush2Client()
		if smsg.PreSeason == nil {
			smsg.PreSeason = &cl.PreSeason{}
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PreSeasonGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LPreSeasonRecvAwardCommand struct {
	base.UserCommand
}

func (c *C2LPreSeasonRecvAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPreSeasonRecvAwardCommand) Error(msg *cl.L2C_PreSeasonRecvAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PreSeasonRecvAward, msg)
	return false
}

func (c *C2LPreSeasonRecvAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PreSeasonRecvAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PreSeasonRecvAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PreSeasonRecvAward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_PreSeasonRecvAward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_PRE_SEASON), c.Srv) {
		l4g.Errorf("user: %d C2L_PreSeasonRecvAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if !c.User.PreSeason().IsOpen(c.Srv) {
		l4g.Errorf("user: %d C2L_PreSeasonRecvAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	preSeason := c.User.PreSeason()
	now := time.Now().Unix()
	totalAward := make([]*cl.Resource, 0, 1)
	serverDay := c.Srv.ServerOpenDay(now)

	awardInfo := goxml.GetData().SeasonCountdownInfoM.Index(cmsg.AwardId)
	if awardInfo == nil {
		l4g.Errorf("user:%d C2L_PreSeasonRecvAward get awardId:%d info failed", c.Msg.UID, cmsg.AwardId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !preSeason.IsRecvAward(cmsg.AwardId) {
		l4g.Errorf("user:%d C2L_PreSeasonRecvAward awardId:%d has recv", c.Msg.UID, cmsg.AwardId)
		return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
	}

	info := goxml.GetData().SeasonCountdownInfoM.GetRewardId(serverDay)
	if info == nil {
		l4g.Errorf("user:%d C2L_PreSeasonRecvAward serverDay:%d get Reward failed", c.User.ID(), serverDay)
		return c.Error(smsg, uint32(cret.RET_PRE_SEASON_RECV_CHECK_FAILED))
	}

	if info.Id != cmsg.AwardId {
		l4g.Errorf("user:%d C2L_PreSeasonRecvAward client:%d server:%d not same", c.User.ID(), cmsg.AwardId, info.Id)
		return c.Error(smsg, uint32(cret.RET_PRE_SEASON_RECV_CHECK_FAILED))
	}

	totalAward = append(totalAward, awardInfo.RewardClRes...)

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalAward, character.MailIDBagFull, uint32(log.RESOURCE_CHANGE_REASON_PRE_SEASON_RECV), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PreSeasonRecvAward: award err code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	preSeason.SetRecvAward(cmsg.AwardId)
	preSeason.Save()
	smsg.PreSeason = preSeason.Flush2Client()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PreSeasonRecvAward, smsg)
	c.User.LogPreSeasonRecv(c.Srv, cmsg.AwardId)

	return c.ResultOK(smsg.Ret)
}
