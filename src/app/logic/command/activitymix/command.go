package activitymix

import (
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"
	// "app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityMixGetData), &C2LActivityMixGetDataCommand{}, state) //  MSG_C2L_ActivityMixGetData = 17701;
}

type C2LActivityMixGetDataCommand struct {
	base.UserCommand
}

func (c *C2LActivityMixGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityMixGetDataCommand) Error(msg *cl.L2C_ActivityMixGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityMixGetData, msg)
	return false
}

func (c *C2LActivityMixGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityMixGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityMixGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityMixGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityMixGetData{
		Ret: uint32(cret.RET_OK),
	}

	smsg.ActivityMixs = c.Srv.ActivityMixM().Flush2Client()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityMixGetData, smsg)
	return c.ResultOK(smsg.Ret)
}
