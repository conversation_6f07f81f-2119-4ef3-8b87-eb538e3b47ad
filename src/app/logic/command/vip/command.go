package vip

import (
	"app/goxml"
	"app/logic/character"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/util"

	"app/logic/command/base"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_VipInfoGet), &C2LVipInfoGetCommand{}, state) //获取vip信息
	cmds.Register(uint32(cl.ID_MSG_C2L_VipBuyGift), &C2LVipBuyGiftCommand{}, state) //购买礼包
}

type C2LVipInfoGetCommand struct {
	base.UserCommand
}

func (c *C2LVipInfoGetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LVipInfoGetCommand) Error(msg *cl.L2C_VipInfoGet, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_VipInfoGet, msg)
	return false
}

func (c *C2LVipInfoGetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_VipInfoGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_VipInfoGet Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_VipInfoGet: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_VipInfoGet{
		Ret: uint32(cret.RET_OK),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_VIP)) {
	//	l4g.Errorf("user: %d C2L_VipInfoGet: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}

	smsg.VipInfo = c.User.VipManager().Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_VipInfoGet, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LVipBuyGiftCommand struct {
	base.UserCommand
}

func (c *C2LVipBuyGiftCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LVipBuyGiftCommand) Error(msg *cl.L2C_VipBuyGift, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_VipBuyGift, msg)
	return false
}

func (c *C2LVipBuyGiftCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_VipBuyGift{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_VipBuyGift Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_VipBuyGift: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_VipBuyGift{
		Ret:   uint32(cret.RET_OK),
		Level: cmsg.Level,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_VIP), c.Srv) {
		l4g.Errorf("user: %d C2L_VipBuyGift: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cmsg.Level > c.User.Vip() {
		l4g.Errorf("user: %d C2L_VipBuyGift: level error. level:%d", c.Msg.UID, cmsg.Level)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.Level > uint32(goxml.GetData().VipInfoM.MaxLv) {
		l4g.Errorf("user: %d C2L_VipBuyGift: level error. level:%d", c.Msg.UID, cmsg.Level)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	vipInfo := goxml.GetData().VipInfoM.Index(cmsg.Level)
	if vipInfo == nil {
		l4g.Errorf("user: %d C2L_VipBuyGift: level error. level:%d", c.Msg.UID, cmsg.Level)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	data := c.User.VipManager().GetData()
	if data == nil {
		l4g.Errorf("user: %d C2L_VipBuyGift: data is nil.", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if util.BitAndUint32(data.GiftStatus, int(cmsg.Level)) {
		l4g.Errorf("user: %d C2L_VipBuyGift: repeat receive award. giftFlag:%d level:%d",
			c.Msg.UID, data.GiftStatus, cmsg.Level)
		return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
	}

	cost := goxml.GetData().VipInfoM.GetGiftCost(cmsg.Level)
	if cost != nil { // 等于nil的是免费礼包，不需要Consume
		cRet := c.User.Consume(c.Srv, []*cl.Resource{cost}, uint32(log.RESOURCE_CHANGE_REASON_VIP_BUY_GIFT), 0)
		if cRet != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_VipBuyGift: buy gift consume error. ret:%d", c.Msg.UID, cRet)
			return c.Error(smsg, cRet)
		}
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, vipInfo.RewardClRes, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_VIP_BUY_GIFT), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_VipBuyGift: buy vipLevelGift award error. level:%d", c.Msg.UID, cmsg.Level)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.VipManager().SetBuyGift(cmsg.Level)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_VipBuyGift, smsg)
	if c.ResultOK(smsg.Ret) {
		c.User.LogVipBuyGift(c.Srv, cmsg.Level, c.User.VipManager().GetData().GiftStatus)
	}
	return c.ResultOK(smsg.Ret)
}
