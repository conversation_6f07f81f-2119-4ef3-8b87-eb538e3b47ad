package hotrank

import (
	"app/goxml"
	"app/logic/command/base"
	"app/protos/in/l2c"
	"app/protos/out/cl"
	"app/protos/out/common"
	"context"

	// "app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_HotRankGet), &C2LHotRankGet{}, state)
}

type C2LHotRankGet struct {
	base.UserCommand
}

func (c *C2LHotRankGet) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LHotRankGet) Error(msg *cl.L2C_HotRankGet, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_HotRankGet, msg)
	return false
}

func (c *C2LHotRankGet) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_HotRankGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_HotRankGet Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_HotRankGet: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_HotRankGet{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_HOT_RANK), c.Srv) {
		l4g.Errorf("user:%d C2L_HotRankGet: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	hotRankInfo := goxml.GetData().HotRankInfoM.Index(cmsg.RankId)
	if hotRankInfo == nil {
		l4g.Errorf("user:%d C2L_HotRankGet: rank id:%d not exist", c.Msg.UID, cmsg.RankId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_HotRankGet, c.Msg.UID, &l2c.L2CS_HotRankGet{
		Rank: cmsg.RankId,
	}) {
		l4g.Errorf("user: %d C2L_HotRankGet: cross maintain", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(smsg.Ret)
}
