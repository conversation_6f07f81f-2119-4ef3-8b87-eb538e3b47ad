package activityreturn

import (
	"app/goxml"
	"app/protos/in/log"
	"context"

	"app/logic/character"
	"app/logic/command/base"
	"app/protos/out/cl"
	"app/protos/out/common"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityReturnGetData), &C2LActivityReturnGetDataCommand{}, state)                 // 请求数据
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityReturnTakeLoginAwards), &C2LActivityReturnTakeLoginAwardsCommand{}, state) // 领取奖励
}

type C2LActivityReturnGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityReturnGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityReturnGetDataCommand) Error(msg *cl.L2C_ActivityReturnGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityReturnGetData, msg)
	return false
}

func (c *C2LActivityReturnGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityReturnGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityReturnGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityReturnGetData: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_ActivityReturnGetData{
		Ret: uint32(cret.RET_OK),
	}

	smsg.Data = c.User.ActivityReturn().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityReturnGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LActivityReturnTakeLoginAwardsCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityReturnTakeLoginAwardsCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityReturnTakeLoginAwardsCommand) Error(msg *cl.L2C_ActivityReturnTakeLoginAwards, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityReturnTakeLoginAwards, msg)
	return false
}

func (c *C2LActivityReturnTakeLoginAwardsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityReturnTakeLoginAwards{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityReturnTakeLoginAwards Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityReturnTakeLoginAwards: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_ActivityReturnTakeLoginAwards{
		DayIndex: cmsg.DayIndex,
		Ret:      uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_RETURN), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityReturnTakeLoginAwards: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	if ret := c.User.ActivityReturn().CheckTakeAward(cmsg.DayIndex); ret != cret.RET_OK {
		l4g.Errorf("user: %d C2L_ActivityReturnTakeLoginAwards: CanTakeAward failed. errCode:%d", c.Msg.UID, ret)
		return c.Error(smsg, uint32(ret))
	}
	returnData := c.User.ActivityReturn().GetData()
	cfgReturnInfo := goxml.GetData().ActivityReturnInfoM.Index(returnData.ReturnId)
	if cfgReturnInfo == nil {
		l4g.Errorf("user: %d C2L_ActivityReturnTakeLoginAwards: return config not found. returnId:%d",
			c.Msg.UID, returnData.ReturnId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	cfgLoginInfo := goxml.GetData().ActivityReturnLoginInfoM.Index(cfgReturnInfo.LoginId, cmsg.DayIndex)
	if cfgLoginInfo == nil {
		l4g.Errorf("user: %d C2L_ActivityReturnTakeLoginAwards: login config not found. returnId:%d dayIndex:%d",
			c.Msg.UID, returnData.ReturnId, cmsg.DayIndex)
		return c.Error(smsg, uint32(cret.RET_RETURN_INVALID_LOGIN_DAY_INDEX))
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, cfgLoginInfo.ClRes, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS), uint64(returnData.ReturnId))
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivityReturnTakeLoginAwards: send award failed. returnId:%d dayIndex:%d retCode: %d",
			c.Msg.UID, returnData.ReturnId, cmsg.DayIndex, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	smsg.Login = c.User.ActivityReturn().SetAwardToken(cmsg.DayIndex).Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityReturnTakeLoginAwards, smsg)
	c.User.LogActivityReturnTakeLoginAwards(c.Srv, returnData.ReturnId, cmsg.DayIndex, smsg.Login)
	return c.ResultOK(smsg.Ret)
}
