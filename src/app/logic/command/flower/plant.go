package flower

import (
	"app/goxml"
	"app/logic/activity"
	"app/logic/activity/flower"
	"app/logic/character"
	"app/logic/command"
	"app/logic/command/base"
	"app/logic/event"
	"app/logic/helper"
	"app/protos/in/db"
	"app/protos/in/log"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"
	"math"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func InitPlant(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerMainInfo), &C2LFlowerMainInfoCommand{}, state)                 //获取主界面信息 NOLOG
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerStartFeed), &C2LFlowerStartFeedCommand{}, state)               //开始献祭(喂养种子)
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerFeedGoblin), &C2LFlowerFeedGoblinCommand{}, state)             //献祭哥布林
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerChangeGoblin), &C2LFlowerChangeGoblinCommand{}, state)         //更换哥布林
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerFeedSpecial), &C2LFlowerFeedSpecialCommand{}, state)           //使用特殊道具，将种子升至最高品质
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerStartPlant), &C2LFlowerStartPlantCommand{}, state)             //开始种花
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerSpeedGrow), &C2LFlowerSpeedGrowCommand{}, state)               //加速生长
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerHarvest), &C2LFlowerHarvestCommand{}, state)                   //收获资源
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerSearch), &C2LFlowerSearchCommand{}, state)                     //搜索
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerEnemiesInfo), &C2LFlowerEnemiesInfoCommand{}, state)           //获取对手信息 NOLOG
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerSnatch), &C2LFlowerSnatchCommand{}, state)                     //协助玩家的花
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerAssistSendLike), &C2LFlowerAssistSendLikeCommand{}, state)     //协助:赠送友情点
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerAssistRecvLike), &C2LFlowerAssistRecvLikeCommand{}, state)     //协助:收取友情点
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerAttackLevelGuard), &C2LFlowerAttackLevelGuardCommand{}, state) //攻打升级守卫
	cmds.Register(uint32(cl.ID_MSG_C2L_FlowerLevelUp), &C2LFlowerLevelUpCommand{}, state)                   //密林升级
}

type C2LFlowerMainInfoCommand struct {
	base.UserCommand
}

func (c *C2LFlowerMainInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerMainInfoCommand) Error(msg *cl.L2C_FlowerMainInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerMainInfo, msg)
	return false
}

func (c *C2LFlowerMainInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerMainInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerMainInfo Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerMainInfo: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_FlowerMainInfo{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerMainInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerMainInfo: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		//初始密林防守阵容时，需要依赖主线阵容。因此先要确认主线阵容是存在的
		dungeonFormation := c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_DUNGEON))
		if dungeonFormation == nil {
			l4g.Errorf("user: %d C2L_FlowerMainInfo: flower init formation err", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_NO_DUNGEON_FORMATION))
		}

		flag, teamNum := goxml.GetData().FormationInfoM.GetTeamNum(uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE))
		if !flag {
			l4g.Errorf("user: %d C2L_FlowerMainInfo: formation info not exist", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		flower = flowerM.InitOne(c.Srv, c.User, teamNum)
	} else {
		flowerM.CheckUpdatePower(c.Srv, flower, c.User.MaxPower(), false)
	}

	//检查更新进攻次数的恢复
	if flower.GetOccupy() != nil {
		c.User.Flower().Recover()
		smsg.Num = c.User.Flower().Clone()
	}

	smsg.Flower = flower.Flush()

	occupyPreviewAward := flower.GetPreviewAward().Clone()
	if occupyPreviewAward != nil && len(occupyPreviewAward.Res) > 0 {
		smsg.MonthlyCardAwards = calcMonthlyCardAwards(c.User, occupyPreviewAward.Res)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerMainInfo, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LFlowerStartFeedCommand struct {
	base.UserCommand
}

func (c *C2LFlowerStartFeedCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerStartFeedCommand) Error(msg *cl.L2C_FlowerStartFeed, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerStartFeed, msg)
	return false
}

func (c *C2LFlowerStartFeedCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerStartFeed{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerStartFeed Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerStartFeed: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerStartFeed{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerStartFeed: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerStartFeed: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.User.ID())
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerStartFeed: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant != nil && plant.Stage != uint32(common.FLOWER_STAGE_FS_WAITING) {
		l4g.Errorf("user: %d C2L_FlowerStartFeed: stage err. stage:%d", c.Msg.UID, plant.Stage)
		return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
	}

	lvInfo := goxml.GetData().FlowerLvInfoM.Index(flower.GetLevel())
	if lvInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerStartFeed: plant level:%d info is nil", c.Msg.UID, plant.Level)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 第一朵花不消耗种花次数
	if plant != nil && plant.GetFlowerIncrId() > 0 {
		nRet, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_FLOWER_PLANT_DAILY_COUNT), 1, c.Srv)
		if nRet != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_FlowerStartFeed: no start feed chance today", c.Msg.UID)
			return c.Error(smsg, nRet)
		}
		c.User.AddNumByType(uint32(common.PURCHASEID_FLOWER_PLANT_DAILY_COUNT), numSummary)
	}

	if !flower.StartFeed(c.Srv, lvInfo) {
		l4g.Errorf("user: %d C2L_FlowerStartFeed: startFeed failed", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_START_FEED_FAILED))
	}
	flowerM.SetChange(c.Srv, flower)

	smsg.CurrentGoblin = flower.GetSeed().GetCurrentGoblin()
	smsg.NextGoblin = flower.GetSeed().GetNextGoblin()
	c.User.LogFlowerStartFeed(c.Srv, smsg.CurrentGoblin, smsg.NextGoblin, lvInfo.PlantType)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerStartFeed, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LFlowerFeedGoblinCommand struct {
	base.UserCommand
}

func (c *C2LFlowerFeedGoblinCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerFeedGoblinCommand) Error(msg *cl.L2C_FlowerFeedGoblin, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerFeedGoblin, msg)
	return false
}

func (c *C2LFlowerFeedGoblinCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerFeedGoblin{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerFeedGoblin Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerFeedGoblin: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerFeedGoblin{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//检查所处阶段
	if plant.Stage != uint32(common.FLOWER_STAGE_FS_FEEDING) {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: stage err. stage:%d", c.Msg.UID, plant.Stage)
		return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
	}

	seed := flower.GetSeed()
	if seed == nil {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: plant seed is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SEED_NOT_EXIST))
	}

	//检查是否已处于中止献祭状态
	if goxml.GetData().FlowerPlantScoreInfoM.IsStopFeedScore(plant.Type, seed.Score) {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: hit stop feed score. score:%d",
			c.Msg.UID, seed.Score)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SCORE_ERROR))
	}

	guide := flower.GetGuide()
	if guide == nil {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: flower guid is nil",
			c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_GUIDED_NOT_EXIST))
	}

	//更新数据
	addScore := goxml.GetData().FlowerPlantGoblinInfoM.RandomScore(c.Srv.Rand(), plant.Type,
		seed.CurrentGoblin, flower.GetSeedFeedStep())
	if addScore == 0 {
		l4g.Errorf("user: %d C2L_FlowerFeedGoblin: random score:%d failed plant Type:%d Current Goblin:%d GetSeedFeedStep:%d",
			c.Msg.UID, addScore, plant.Type, seed.CurrentGoblin, flower.GetSeedFeedStep())
		return c.Error(smsg, uint32(cret.RET_FLOWER_RANDOM_GOBLIN_SCORE_FAILED))
	}

	flower.FeedGoblin(c.Srv, guide, seed, plant.Type, addScore)
	flowerM.SetChange(c.Srv, flower)

	smsg.Score = seed.Score
	smsg.CurrentGoblin = seed.CurrentGoblin
	smsg.NextGoblin = seed.NextGoblin
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerFeedGoblin, smsg)
	c.User.LogFlowerFeedGoblin(c.Srv, seed.CurrentGoblin, seed.NextGoblin, seed.Score, addScore, plant.Type)

	return c.ResultOK(smsg.Ret)
}

type C2LFlowerChangeGoblinCommand struct {
	base.UserCommand
}

func (c *C2LFlowerChangeGoblinCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerChangeGoblinCommand) Error(msg *cl.L2C_FlowerChangeGoblin, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerChangeGoblin, msg)
	return false
}

func (c *C2LFlowerChangeGoblinCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerChangeGoblin{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerChangeGoblin Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerChangeGoblin: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerChangeGoblin{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//检查所处阶段
	if plant.Stage != uint32(common.FLOWER_STAGE_FS_FEEDING) {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: stage err. stage:%d", c.Msg.UID, plant.Stage)
		return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
	}

	seed := flower.GetSeed()
	if seed == nil {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: plant seed is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SEED_NOT_EXIST))
	}

	//检查是否已转换过
	if seed.ChangedGoblin {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: goblin cannot change again", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_GOBLIN_CANNOT_CHANGE_AGAIN))
	}

	cost := goxml.GetData().FlowerPlantTypeInfoM.ChangeGoblinCost(plant.Type)
	if len(cost) == 0 {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: cost len is 0 plantType:%d", c.Msg.UID, plant.Type)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	smsg.Ret = c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_FLOWER_CHANGE_GOBLIN), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerChangeGoblin: consume err", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}
	flower.ChangeGoblin(seed)
	flowerM.SetChange(c.Srv, flower)
	smsg.CurrentGoblin = seed.CurrentGoblin
	smsg.NextGoblin = seed.NextGoblin
	c.User.LogFlowerChangeGoblin(c.Srv, smsg.CurrentGoblin, smsg.NextGoblin, plant.Type)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerChangeGoblin, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LFlowerFeedSpecialCommand struct {
	base.UserCommand
}

func (c *C2LFlowerFeedSpecialCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerFeedSpecialCommand) Error(msg *cl.L2C_FlowerFeedSpecial, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerFeedSpecial, msg)
	return false
}

func (c *C2LFlowerFeedSpecialCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerFeedSpecial{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerFeedSpecial Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerFeedSpecial: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerFeedSpecial{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//检查所处阶段
	if plant.Stage != uint32(common.FLOWER_STAGE_FS_FEEDING) {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: stage err. stage:%d", c.Msg.UID, plant.Stage)
		return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
	}

	seed := flower.GetSeed()
	if seed == nil {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: plant seed is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SEED_NOT_EXIST))
	}

	oldScore := seed.Score
	if goxml.GetData().FlowerPlantScoreInfoM.IsBestScore(plant.Type, oldScore) {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: already bestScore:%d", c.Msg.UID, oldScore)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SCORE_ERROR))
	}

	bestScore := goxml.GetData().FlowerPlantScoreInfoM.BestMaxScore(plant.Type)

	cost := goxml.GetData().FlowerPlantTypeInfoM.HighestSeedCost(plant.Type)
	if len(cost) == 0 {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: cost len is 0 plantType:%d", c.Msg.UID, plant.Type)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	smsg.Ret = c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_FLOWER_CHANGE_GOBLIN), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerFeedSpecial: consume err", c.Msg.UID)
		return c.Error(smsg, smsg.Ret)
	}

	flower.SetSeedScore(bestScore, seed)
	flowerM.SetChange(c.Srv, flower)

	smsg.Score = bestScore
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerFeedSpecial, smsg)
	c.User.LogFlowerFeedSpecial(c.Srv, oldScore, plant.Type)
	return c.ResultOK(smsg.Ret)
}

type C2LFlowerStartPlantCommand struct {
	base.UserCommand
}

func (c *C2LFlowerStartPlantCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerStartPlantCommand) Error(msg *cl.L2C_FlowerStartPlant, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerStartPlant, msg)
	return false
}

func (c *C2LFlowerStartPlantCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerStartPlant{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerStartPlant Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerStartPlant: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerStartPlant{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//检查所处阶段
	if plant.Stage != uint32(common.FLOWER_STAGE_FS_FEEDING) {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: stage err. stage:%d", c.Msg.UID, plant.Stage)
		return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
	}

	seed := flower.GetSeed()
	if seed == nil {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: plant seed is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SEED_NOT_EXIST))
	}

	if seed.Score == 0 {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: score=0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SCORE_ERROR))
	}

	flowerLvInfo := goxml.GetData().FlowerLvInfoM.Index(plant.GetLevel())
	if flowerLvInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: plant level:%d info is nil", c.Msg.UID, plant.Level)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	plantInfo := goxml.GetData().FlowerPlantScoreInfoM.GetTreeInfoByScore(seed.Score, flowerLvInfo.PlantType)
	if plantInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerStartPlant: plant config not exist. score:%d",
			c.Msg.UID, seed.Score)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	flower.Plant(c.User, plantInfo.CollectTime, plantInfo.Rare, plant)
	flowerM.SetChange(c.Srv, flower)
	flowerM.UpdateSearchList(flower)

	smsg.Score = seed.Score
	smsg.StartTm = plant.StartTm
	smsg.EndTm = plant.EndTm
	smsg.FlowerIncrId = plant.FlowerIncrId

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerStartPlant, smsg)

	c.User.LogFlowerStartPlant(c.Srv, seed.Score, flowerLvInfo.PlantType, plant.StartTm, plant.EndTm)
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeFlowerPlant, 1, plantInfo.Rare, flowerLvInfo.PlantType)
	return c.ResultOK(smsg.Ret)
}

type C2LFlowerSpeedGrowCommand struct {
	base.UserCommand
}

func (c *C2LFlowerSpeedGrowCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerSpeedGrowCommand) Error(msg *cl.L2C_FlowerSpeedGrow, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSpeedGrow, msg)
	return false
}

func (c *C2LFlowerSpeedGrowCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerSpeedGrow{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerSpeedGrow Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerSpeedGrow: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerSpeedGrow{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//检查所处阶段
	if plant.Stage != uint32(common.FLOWER_STAGE_FS_GROWING) {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: stage err. stage:%d", c.Msg.UID, plant.Stage)
		return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
	}

	seed := flower.GetSeed()
	if seed == nil {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: plant seed is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_SEED_NOT_EXIST))
	}

	guide := flower.GetGuide()
	if guide == nil {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: plant guide is nil", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_GUIDED_NOT_EXIST))
	}

	leftTm, ok := flower.CanHarvest(plant)
	if ok {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: hit endTm, not need speedUp", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NEED_HARVEST))
	}

	flowerLvInfo := goxml.GetData().FlowerLvInfoM.Index(plant.Level)
	if flowerLvInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerSpeedGrow: plant level:%d info is nil", c.Msg.UID, plant.Level)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	//更新数据
	if guide.GetSpeedGrow() {
		costs := goxml.GetData().FlowerPlantTypeInfoM.CD2Res(leftTm, flowerLvInfo.PlantType)
		l4g.Debugf("user: %d C2L_FlowerSpeedGrow: cost %+v", c.User.ID(), costs)
		smsg.Ret = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_FLOWER_SPEED_GROW), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_FlowerSpeedGrow: consume err", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}
	}

	flower.SetHarvest(plant)
	flowerM.SetChange(c.Srv, flower)
	flowerM.DeleteSearchList(flower)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSpeedGrow, smsg)
	c.User.LogFlowerSpeedGrow(c.Srv, plant.Type)
	return c.ResultOK(smsg.Ret)
}

type C2LFlowerHarvestCommand struct {
	base.UserCommand
}

func (c *C2LFlowerHarvestCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerHarvestCommand) Error(msg *cl.L2C_FlowerHarvest, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerHarvest, msg)
	return false
}

//nolint:funlen
func (c *C2LFlowerHarvestCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerHarvest{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerHarvest Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerHarvest: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerHarvest{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerHarvest: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerHarvest: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerHarvest: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerHarvest: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//检查所处阶段
	if plant.Stage != uint32(common.FLOWER_STAGE_FS_GROWING) {
		l4g.Errorf("user: %d C2L_FlowerHarvest: stage err. stage:%d", c.Msg.UID, plant.Stage)
		return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
	}

	_, ok = flower.CanHarvest(plant)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerHarvest: not harvest time", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_HARVEST_TIME))
	}

	plantRewardInfo := goxml.GetData().FlowerPlantRewardInfoM.Index(plant.GetLevel(), plant.GetRare())
	if plantRewardInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerHarvest: plantReward config not exist. plant level:%d, rare:%d",
			c.Msg.UID, plant.GetLevel(), plant.GetRare())
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	plantInfo := goxml.GetData().FlowerPlantInfoM.Index(plant.GetType(), plant.GetRare())
	if plantInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerHarvest: plantReward config not exist. plant type:%d, rare:%d",
			c.Msg.UID, plant.GetType(), plant.GetRare())
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	//暴击逻辑
	dropParam := goxml.BaseInt64
	isCrit := flower.IsCritical(c.Srv, plantInfo.Critical)
	if isCrit {
		dropParam = int64(plantInfo.Multiple)
	}

	//种花的原始产出
	dropAwards := flower.CalcHarvestResource(plantRewardInfo, dropParam, plantInfo.LootProp)
	l4g.Debugf("user: %d C2L_FlowerHarvest: dropAwards:%+v", c.Msg.UID, dropAwards)

	//vip特权加成后的掉落
	exp, vipAwards := goxml.GetData().VipPrivilegeInfoM.FlowerPrivilege(c.User.Vip(), plantRewardInfo.Exp, dropAwards)
	l4g.Debugf("user: %d C2L_FlowerHarvest: vipAwards:%+v", c.Msg.UID, vipAwards)

	if guildMedal := c.Srv.GetGuildMedal(c.User.ID()); guildMedal != nil {
		vipAwards = c.addGuildMedal(vipAwards, guildMedal)
		l4g.Debugf("user: %d C2L_FlowerHarvest: vipAwardsGuildMedalAdd:%+v", c.Msg.UID, vipAwards)
	}

	//月卡加成
	extraAwards := c.calcMonthlyCardAwards(vipAwards)
	l4g.Debugf("user: %d C2L_FlowerHarvest: extraAwards:%+v", c.Msg.UID, extraAwards)
	if len(extraAwards) > 0 {
		smsg.Ret, smsg.MonthlyCardAwards = c.User.Award(c.Srv, extraAwards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_FLOWER_HARVEST), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_FlowerHarvest: award err. drop:%v", c.Msg.UID, extraAwards)
			return c.Error(smsg, smsg.Ret)
		}
	}

	//掉落活动 - 额外奖励
	daDropGroup, _, _ := character.GetDropActivity(c.Srv,
		uint32(common.FUNCID_MODULE_FLOWER), goxml.SubFuncFlowerPlat, c.User)
	if daDropGroup > 0 {
		if ret, flag := character.DoDrop(c.Srv.Rand(), daDropGroup); flag {
			vipAwards = append(vipAwards, ret...)
		}
	}

	l4g.Debugf("user: %d C2L_FlowerHarvest: awards:%+v", c.Msg.UID, vipAwards)
	//更新数据
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, vipAwards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_FLOWER_HARVEST), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerHarvest: award err. drop:%v", c.Msg.UID, vipAwards)
		return c.Error(smsg, smsg.Ret)
	}

	guide := flower.GetGuide()
	if !guide.GetSpeedGrow() {
		flower.SetSpeedGrow()
	}

	flower.AddExp(exp)
	flower.ResetPlant(plant)
	flowerM.SetChange(c.Srv, flower)
	flowerM.DeleteSearchList(flower)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerHarvest, smsg)
	smsg.MonthlyCardAwards = extraAwards
	smsg.Level = flower.GetLevel()
	smsg.Exp = flower.GetExp()
	smsg.Crit = isCrit
	c.User.LogFlowerHarvest(c.Srv, smsg.Level, plant.Type, uint64(smsg.Exp), smsg.Crit)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LFlowerHarvestCommand) calcMonthlyCardAwards(baseAwards []*cl.Resource) []*cl.Resource {
	var extraAwards []*cl.Resource
	privilegeValue := c.User.MonthlyCard().GetPrivilegeValue(goxml.MonthlyCardFlowerBox)
	if privilegeValue > 0 {
		for _, res := range baseAwards {
			count := math.Ceil((float64(privilegeValue) * float64(res.Count)) / float64(goxml.BaseInt64))
			extraAwards = append(extraAwards, goxml.GenSimpleResource(res.Type, res.Value, uint32(count)))
		}
	}

	return extraAwards
}

func (c *C2LFlowerHarvestCommand) addGuildMedal(awards []*cl.Resource, guildMedal *db.LogicGuildMedal) []*cl.Resource {
	addValue := uint32(0)
	for _, medal := range guildMedal.Medals {
		info := goxml.GetData().GuildMedalTaskInfoM.Index(medal.GetMedalId())
		if info == nil {
			continue
		}
		if info.AddType != goxml.GuildMedalAddFlower {
			continue
		}
		addValue += info.AddValue
	}
	if addValue == 0 {
		return awards
	}

	newAwards := make([]*cl.Resource, 0, len(awards))
	for _, award := range awards {
		cAward := award.Clone()
		cAward.Count = cAward.Count + cAward.Count*addValue/10000
		newAwards = append(newAwards, cAward)
	}

	return newAwards
}

type C2LFlowerSearchCommand struct {
	base.UserCommand
}

func (c *C2LFlowerSearchCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerSearchCommand) Error(msg *cl.L2C_FlowerSearch, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSearch, msg)
	return false
}

func (c *C2LFlowerSearchCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerSearch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerSearch Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerSearch: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerSearch{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerSearch: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerSearch: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerSearch: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerSearch: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//检查每日协助次数
	//nRet, _, _ := c.User.CheckNumByType(uint32(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT), 1)
	//if nRet != uint32(cret.RET_OK) {
	//	l4g.Errorf("user: %d C2L_FlowerSearch: no snatch chance today", c.Msg.UID)
	//	return c.Error(smsg, nRet)
	//}

	// if !flower.PlantSearchCD() {
	// 	l4g.Errorf("user: %d C2L_FlowerSearch: cooldown", c.Msg.UID)
	// 	return c.Error(smsg, uint32(cret.RET_FLOWER_SEARCH_CD))
	// }

	flowerM.CheckUpdatePower(c.Srv, flower, c.User.MaxPower(), false)

	players, bots := flowerM.Search(c.Srv, flower, c.User.MaxPower(), c.User.Level())
	if len(players) == 0 && len(bots) == 0 {
		l4g.Errorf("user: %d C2L_FlowerSearch: search logic err", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	smsg.Enemies = make([]*cl.FlowerSnatchEnemy, 0, activity.FlowerAssistMaxCount)

	if len(bots) > 0 {
		botSnapShot := make([]*cl.FlowerSnatchEnemy, 0, len(bots))
		for i := 0; i < len(bots); i++ {
			tempEnemy := &cl.FlowerSnatchEnemy{
				Snapshot: goxml.GetData().BotInfoM.MakeBotSnapshot(c.Srv.Rand(), bots[i], c.User.Level()),
				IsBot:    true,
			}
			botSnapShot = append(botSnapShot, tempEnemy)
			c.User.LogFlowerSearch(c.Srv, 0, tempEnemy.Snapshot.Id, 0, true)
			smsg.Enemies = botSnapShot
		}
		flower.UpdateBotsSnapShot(botSnapShot)
	} else {
		flower.ClearBotsSnapShot()
	}
	if len(players) > 0 {
		c.GetLocalUserSnapshots(players, &AsyncC2LFlowerSearchReq{smsg}, uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE))
	} else {
		flower.UpdateSearchTime()
		flowerM.SetChange(c.Srv, flower)
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSearch, smsg)
	}
	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFlowerSearchReq struct {
	smsg *cl.L2C_FlowerSearch
}

func (ar *AsyncC2LFlowerSearchReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2LFlowerSearchCommand: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSearch, smsg)
		return false
	}
	snaps := dbData.([]*cl.UserSnapshot)
	flowerM := srv.GetActivity(activity.Flower).(*flower.Manager)

	myFlower := flowerM.Get(args.UID)
	if myFlower == nil {
		l4g.Errorf("user: %d C2LFlowerSearchCommand: flower not exist", args.UID)
		smsg.Ret = uint32(cret.RET_FLOWER_NOT_EXIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	for i := 0; i < len(snaps); i++ {
		if snaps[i] == nil {
			l4g.Errorf("user: %d C2L_FlowerSearch: get snaps failed", args.UID)
			continue
		}
		flower := flowerM.Get(snaps[i].Id)
		if flower == nil {
			l4g.Errorf("user: %d C2L_FlowerSearch: flower not exist", snaps[i].Id)
			continue
		}
		plant := flower.GetPlant()
		if plant == nil {
			l4g.Errorf("user: %d C2L_FlowerSearch: flower plant not exist", snaps[i].Id)
			continue
		}
		var leftCount uint32
		level := flower.GetLevel()
		time := plant.GetEndTm()
		if plant.GetStage() == uint32(common.FLOWER_STAGE_FS_GROWING) {
			flowerPlantInfo := goxml.GetData().FlowerPlantInfoM.Index(plant.Type, plant.Rare)
			if flowerPlantInfo == nil {
				l4g.Errorf("user: %d C2L_FlowerSearch: flower plant type:%d plant Rare:%d plant Score:%d",
					snaps[i].Id, plant.GetType(), plant.GetRare(), plant.GetSeed().GetScore())
				continue
			}
			if flowerPlantInfo.LootTime > uint32(len(plant.Robbers)) {
				leftCount = flowerPlantInfo.LootTime - uint32(len(plant.Robbers))
			}
			level = plant.GetLevel()
			if plant.NeedHarvest {
				time = 0
			}
		}

		tempEnemy := &cl.FlowerSnatchEnemy{
			Snapshot:  snaps[i],
			Level:     level,
			Score:     plant.GetSeed().GetScore(),
			Stage:     plant.GetStage(),
			LeftCount: leftCount,
			EndTm:     time,
		}
		smsg.Enemies = append(smsg.Enemies, tempEnemy)

		flowerM.CheckUpdatePower(srv, flower, snaps[i].MaxPower, false)
		args.Caller.LogFlowerSearch(srv, tempEnemy.Score, tempEnemy.Snapshot.Id, args.CtxID, false)
	}
	myFlower.UpdateSearchTime()
	flowerM.SetChange(srv, myFlower)

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSearch, smsg)
	return true
}

type C2LFlowerEnemiesInfoCommand struct {
	base.UserCommand
}

func (c *C2LFlowerEnemiesInfoCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerEnemiesInfoCommand) Error(msg *cl.L2C_FlowerEnemiesInfo, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerEnemiesInfo, msg)
	return false
}

func (c *C2LFlowerEnemiesInfoCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerEnemiesInfo{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerEnemiesInfo Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerEnemiesInfo: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerEnemiesInfo{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerEnemiesInfo: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerEnemiesInfo: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerEnemiesInfo: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := flower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerEnemiesInfo: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//if !flower.PlantSearchCD() {
	//	l4g.Errorf("user: %d C2L_FlowerSearch: cooldown", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FLOWER_SEARCH_CD))
	//}

	flowerM.CheckUpdatePower(c.Srv, flower, c.User.MaxPower(), false)

	var players, bots []uint64
	checkRepeat := make(map[uint64]struct{})
	for _, uid := range cmsg.Uids {
		_, exist := checkRepeat[uid]
		if exist {
			l4g.Errorf("user: %d C2L_FlowerEnemiesInfo: client send uid repeat:%d", c.User.ID(), uid)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		checkRepeat[uid] = struct{}{}

		if !flower.IsInExistPool(uid) {
			l4g.Errorf("user: %d C2L_FlowerEnemiesInfo: enemy uid illegal. uid:%d not in searchPool",
				c.Msg.UID, uid)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if goxml.GetData().BotInfoM.CheckEnemyIsBot(uid) {
			bots = append(bots, uid)
		} else {
			players = append(players, uid)
		}
	}

	smsg.Enemies = make([]*cl.FlowerSnatchEnemy, 0, activity.FlowerAssistMaxCount)

	if len(bots) > 0 {
		for i := 0; i < len(bots); i++ {
			snapShot := flower.GetBotsSnapShot(bots[i])
			if snapShot != nil {
				smsg.Enemies = append(smsg.Enemies, snapShot)
			}
		}
	}

	if len(players) > 0 {
		c.GetLocalUserSnapshots(players, &AsyncC2LFlowerEnemiesInfoReq{smsg}, uint32(common.FORMATION_ID_FI_FLOWER_DEFENSE))
	} else {
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerEnemiesInfo, smsg)
	}

	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFlowerEnemiesInfoReq struct {
	smsg *cl.L2C_FlowerEnemiesInfo
}

func (ar *AsyncC2LFlowerEnemiesInfoReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	smsg := ar.smsg
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerEnemiesInfo: get snapshot error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerEnemiesInfo, smsg)
		return false
	}
	snaps := dbData.([]*cl.UserSnapshot)
	flowerM := srv.GetActivity(activity.Flower).(*flower.Manager)

	for i := 0; i < len(snaps); i++ {
		if snaps[i] == nil {
			l4g.Errorf("user: %d C2L_FlowerSearch: get snaps failed", args.UID)
			continue
		}
		flower := flowerM.Get(snaps[i].Id)
		if flower == nil {
			l4g.Errorf("user: %d C2L_FlowerSearch: flower not exist", snaps[i].Id)
			continue
		}

		plant := flower.GetPlant()
		if plant == nil {
			l4g.Errorf("user: %d C2L_FlowerSearch: flower plant not exist", snaps[i].Id)
			continue
		}

		var leftCount uint32
		level := flower.GetLevel()
		time := plant.GetEndTm()
		if plant.GetStage() == uint32(common.FLOWER_STAGE_FS_GROWING) {
			flowerPlantInfo := goxml.GetData().FlowerPlantInfoM.Index(plant.Type, plant.Rare)
			if flowerPlantInfo == nil {
				l4g.Errorf("user: %d C2L_FlowerSearch: flower plant type:%d plant Rare:%d plant Score:%d",
					snaps[i].Id, plant.GetType(), plant.GetRare(), plant.GetSeed().GetScore())
				continue
			}
			if flowerPlantInfo.LootTime > uint32(len(plant.Robbers)) {
				leftCount = flowerPlantInfo.LootTime - uint32(len(plant.Robbers))
			}
			level = plant.GetLevel()
			if plant.NeedHarvest {
				time = 0
			}
		}

		tempEnemy := &cl.FlowerSnatchEnemy{
			Snapshot:  snaps[i],
			Level:     level,
			Score:     plant.GetSeed().GetScore(),
			Stage:     plant.GetStage(),
			LeftCount: leftCount,
			EndTm:     time,
			BeAssist:  flower.HasSnatchByUid(plant, args.Caller.ID()),
		}
		smsg.Enemies = append(smsg.Enemies, tempEnemy)

		flowerM.CheckUpdatePower(srv, flower, snaps[i].MaxPower, false)
	}

	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerEnemiesInfo, smsg)
	return true
}

type C2LFlowerSnatchCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LFlowerSnatchCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerSnatchCommand) Error(msg *cl.L2C_FlowerSnatch, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, msg)
	return false
}

//nolint:funlen
func (c *C2LFlowerSnatchCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerSnatch{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerSnatch Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerSnatch: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerSnatch{
		Ret:         uint32(cret.RET_OK),
		Uid:         cmsg.Uid,
		FormationId: cmsg.FormationId,
	}

	if cmsg.Uid == 0 {
		l4g.Errorf("user: %d C2L_FlowerSnatch: param err. uid=0", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 检查输入的阵容id
	if cmsg.FormationId != uint32(common.FORMATION_ID_FI_ARENA_DEFENSE) && cmsg.FormationId != uint32(common.FORMATION_ID_FI_DUNGEON) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: invalid FormationId %d", c.Msg.UID, cmsg.FormationId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerSnatch: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	myFlower := flowerM.Get(c.Msg.UID)
	if myFlower == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	plant := myFlower.GetPlant()
	if plant == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: flower plant not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
	}

	//验证所传uid是否是刚搜到的
	inPool := myFlower.IsInExistPool(cmsg.Uid)
	if !inPool {
		l4g.Errorf("user: %d C2L_FlowerSnatch: enemy uid illegal. uid:%d not in searchPool",
			c.Msg.UID, cmsg.Uid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	//检查每日协助次数
	nRet, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT), 1, c.Srv)
	if nRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: no loot chance today", c.Msg.UID)
		return c.Error(smsg, nRet)
	}

	//检查进攻阵容
	attackFormation := c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_FLOWER_ATTACK))
	if attackFormation == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: no attack formation", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NO_ATTACK_FORMATION))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//判断是否是机器人
	isBot := goxml.GetData().BotInfoM.CheckEnemyIsBot(cmsg.Uid)
	if isBot {
		botInfo := goxml.GetData().BotInfoM.Index(uint32(cmsg.Uid))
		if botInfo == nil {
			l4g.Errorf("user: %d C2L_FlowerSnatch: bot info not exist. id:%d", c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}

		if myFlower.BotHasBeAssist(cmsg.Uid) {
			l4g.Errorf("user: %d C2L_FlowerSnatch: repeat snatch. op:%d", c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_REPEAT_SNATCH))
		}

		win, reportID, bRet := c.User.AttackFlowerRobot(c.Srv, botInfo.MonsterGroup, cmsg.ClientData)
		if bRet != cret.RET_OK {
			l4g.Errorf("user: %d C2L_FlowerSnatch: Flower attack bot err.", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_FLOWER_BATTLE_ERROR))
		}

		if win {
			snatchRes := myFlower.CalcRobotSnatchItems()
			if len(snatchRes) == 0 {
				l4g.Errorf("user:%d C2L_FlowerSnatch calc award failed", c.Msg.UID)
				return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
			}

			//更新资源
			smsg.Ret, smsg.Awards = c.User.Award(c.Srv, snatchRes, character.AwardTagMail,
				uint32(log.RESOURCE_CHANGE_REASON_FLOWER_SNATCH), 0)
			if smsg.Ret != uint32(cret.RET_OK) {
				l4g.Errorf("user: %d C2L_FlowerSnatch: award failed. awards:%+v", c.Msg.UID, snatchRes)
				return c.Error(smsg, smsg.Ret)
			}

			myFlower.SetBotBeAssist(cmsg.Uid)
			flowerM.SetChange(c.Srv, myFlower)
			//更新密林系统相关数据
			c.User.AddNumByType(uint32(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT), numSummary)
		}
		smsg.ReportId = reportID
		smsg.Win = win
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		c.User.LogFlowerSnatch(c.Srv, cmsg.Uid, 0, smsg.Win, true)
		c.User.FireCommonEvent(c.Srv.EventM(), uint32(event.IeFlowerLoot), 1)
	} else {
		//检查对方是否在搜索列表中
		if !flowerM.IsInSearchList(cmsg.Uid) {
			l4g.Errorf("user: %d C2L_FlowerSnatch: enemy:%d not in list now",
				c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_ENEMY_NOT_IN_SEARCH_LIST))
		}

		opFlower := flowerM.Get(cmsg.Uid)
		if opFlower == nil {
			l4g.Errorf("user: %d C2L_FlowerSnatch: enemy:%d flower not exist",
				c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
		}

		opPlant := opFlower.GetPlant()
		if opPlant == nil {
			l4g.Errorf("user: %d C2L_FlowerSnatch: enemy:%d flower not exist",
				c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST))
		}

		//检查对方所处阶段
		if opPlant.GetStage() != uint32(common.FLOWER_STAGE_FS_GROWING) {
			l4g.Errorf("user: %d C2L_FlowerSnatch: stage err. stage:%d", c.Msg.UID, opPlant.GetStage())
			return c.Error(smsg, uint32(cret.RET_FLOWER_STAGE_ERROR))
		}

		//检查对手生长期是否已结束
		if _, ok := opFlower.CanHarvest(opPlant); ok || opPlant.NeedHarvest {
			l4g.Errorf("user: %d C2L_FlowerSnatch: op's tree end grow. op:%d", c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_END_GROW))
		}

		plantInfo := goxml.GetData().FlowerPlantInfoM.Index(opPlant.Type, opPlant.Rare)
		if plantInfo == nil {
			l4g.Errorf("user: %d C2L_FlowerSnatch: plantInfo config not exist. opUID:%d, type:%d, rare:%d",
				c.User.ID(), cmsg.Uid, opPlant.Type, opPlant.Rare)
			smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
			c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
			return false
		}

		//检查对方是否有可被协助次数
		if len(opPlant.Robbers) >= int(plantInfo.LootTime) {
			l4g.Errorf("user: %d C2L_FlowerSnatch: op's plant no left snatch chance. opUID:%d",
				c.User.ID(), cmsg.Uid)
			smsg.Ret = uint32(cret.RET_FLOWER_ENEMY_NO_SNATCH_CHANGE)
			c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
			return false
		}

		//检查对方是否已被协助过
		if opFlower.HasSnatchByUid(opPlant, c.Msg.UID) {
			l4g.Errorf("user: %d C2L_FlowerSnatch: repeat snatch. op:%d", c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_REPEAT_SNATCH))
		}

		if !flowerM.TryLock(cmsg.Uid) {
			l4g.Errorf("user: %d C2L_FlowerSnatch: user:%d plant has been snatch", c.Msg.UID, cmsg.Uid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_OTHER_SNATCH))
		}

		//协助机制不存种花防守阵容，优先使用竞技场防守阵容，其次是主线阵容
		req := &AsyncC2LFlowerSnatchReq{
			opUID:       cmsg.Uid,
			smsg:        smsg,
			clientData:  cmsg.ClientData,
			formationId: cmsg.FormationId,
		}
		c.GetLocalUserBattleData(cmsg.FormationId, []uint64{cmsg.Uid}, req, func() {
			flowerM.UnLock(cmsg.Uid)
		})
	}

	return c.ResultOK(smsg.Ret)
}

type AsyncC2LFlowerSnatchReq struct {
	opUID       uint64
	smsg        *cl.L2C_FlowerSnatch
	clientData  []byte
	formationId uint32
}

//nolint:funlen
func (ar *AsyncC2LFlowerSnatchReq) Resp(srv command.Servicer,
	args *character.Args, retCode uint32, dbData interface{}) bool {
	opUID := ar.opUID
	smsg := ar.smsg
	clientData := ar.clientData
	formationId := ar.formationId
	if retCode != uint32(r2l.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: get battleData error:%d", args.UID, retCode)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	flowerM, ok := srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerSnatch: flower manager not exist", args.UID)
		smsg.Ret = uint32(cret.RET_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	myFlower := flowerM.Get(args.UID)
	if myFlower == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: flower not exist", args.UID)
		smsg.Ret = uint32(cret.RET_FLOWER_NOT_EXIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//验证对方uid是否是刚搜到的
	if !myFlower.IsInExistPool(opUID) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: enemy uid illegal. enemy uid:%d ",
			args.UID, opUID)
		smsg.Ret = uint32(cret.RET_CLIENT_REQUEST_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//检查对方是否在搜索列表中
	if !flowerM.IsInSearchList(opUID) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: enemy not in list now, opUID:%d",
			args.UID, opUID)
		smsg.Ret = uint32(cret.RET_FLOWER_ENEMY_NOT_IN_SEARCH_LIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//检查每日掠夺次数
	nRet, _, numSummary := args.Caller.CheckNumByType(uint32(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT), 1, srv)
	if nRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: no loot chance today", args.UID)
		smsg.Ret = nRet
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//获取被协助目标的种花信息
	opFlower := flowerM.Get(opUID)
	if opFlower == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: flower not exist. opUID:%d", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_FLOWER_NOT_EXIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	opPlant := opFlower.GetPlant()
	if opPlant == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: tree not exist. opUID:%d", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	opSeed := opPlant.GetSeed()
	if opSeed == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: enemy:%d seed not exist", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_FLOWER_PLANT_MODE_NOT_EXIST)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//获取配置信息
	plantInfo := goxml.GetData().FlowerPlantInfoM.Index(opPlant.Type, opPlant.Rare)
	if plantInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: plantInfo config not exist. opUID:%d, type:%d, rare:%d",
			args.UID, opUID, opPlant.Type, opPlant.Rare)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	plantRewardInfo := goxml.GetData().FlowerPlantRewardInfoM.Index(opPlant.Level, opPlant.Rare)
	if plantRewardInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: plantReward config not exist. opUID:%d, level:%d, rare:%d",
			args.UID, opUID, opPlant.Level, opPlant.Rare)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//检查对方所处阶段
	if opPlant.Stage != uint32(common.FLOWER_STAGE_FS_GROWING) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: stage err. opUID:%d, stage:%d", args.UID, opUID, opPlant.Stage)
		smsg.Ret = uint32(cret.RET_FLOWER_STAGE_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//检查对方生长期是否已结束
	if _, ok := opFlower.CanHarvest(opPlant); ok || opPlant.NeedHarvest {
		l4g.Errorf("user: %d C2L_FlowerSnatch: op's plant end grow. opUID:%d", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_FLOWER_END_GROW)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//检查对方是否有可被协助次数
	if len(opPlant.Robbers) >= int(plantInfo.LootTime) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: op's plant no left snatch chance. opUID:%d", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_FLOWER_ENEMY_NO_SNATCH_CHANGE)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	//检查对方是否已被协助过
	if opFlower.HasSnatchByUid(opPlant, args.UID) {
		l4g.Errorf("user: %d C2L_FlowerSnatch: repeat snatch. opUID:%d", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_FLOWER_REPEAT_SNATCH)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	cbusers := dbData.([]*db.UserBattleData)
	snapm := make(map[uint64]*db.UserBattleData, len(cbusers))
	for _, v := range cbusers {
		if v == nil {
			l4g.Errorf("user: %d C2L_FlowerSnatch: get battle data nil. opUID:%d", args.UID, opUID)
			smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
			return false
		}
		snapm[v.User.Id] = v
	}

	if snapm[opUID] == nil {
		l4g.Errorf("user: %d C2L_FlowerSnatch: op battle data nil. opUID:%d", args.UID, opUID)
		smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	opUser := character.GetUserFromUserBattleData(srv, snapm[opUID])
	isWin, reportID, bRet := args.Caller.AttackFlowerSnatch(srv, opUser, clientData, formationId)
	if bRet != cret.RET_OK {
		l4g.Errorf("user: %d C2L_FlowerSnatch: AttackFlower err.",
			args.UID)
		smsg.Ret = uint32(cret.RET_FLOWER_BATTLE_ERROR)
		args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
		return false
	}

	if isWin {
		snatchRes := opFlower.CalcPLayerSnatchItems(plantRewardInfo.Awards, plantInfo.LootProp)
		if len(snatchRes) == 0 {
			l4g.Errorf("user:%d C2L_FlowerSnatch calc award failed", args.UID)
			smsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
			return false
		}

		smsg.Ret, smsg.Awards = args.Caller.Award(srv, snatchRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_FLOWER_SNATCH), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_FlowerSnatch: award failed. awards:%+v",
				args.UID, snatchRes)
			args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
			return false
		}

		//为对方增加协助者，如果协助次数超过上限，则将花从搜索列表中删除
		opFlower.AddRobber(args.UID)
		//对方的花如果成熟了或者达到被协助上限，则从搜索列表中删除
		_, ok := opFlower.CanHarvest(opPlant)
		if ok || opFlower.RobberNum() >= plantInfo.LootTime {
			flowerM.DeleteSearchList(opFlower)
			l4g.Debugf("user: %d C2L_FlowerSnatch: clean opFlower out of search list. opUID:%d", args.UID, opUID)
		}
		//为对方生成赠送友情点日志
		robber := args.Caller.NewMiniUserSnapshot()
		logM := flowerM.GetSnatchLogM()
		logOwner := opFlower.GetSnatchLogManager()
		opFlowerLog := logM.GetOwnerLogs(logOwner)
		detail := flower.NewFlowerSnatchLogDetailSend(opPlant.FlowerIncrId, opPlant.Level, opSeed.Score)
		log := flower.NewFlowerSnatchLog(robber, srv.CreateUniqueID(), detail)
		opFlowerLog.AddLog(srv, log)
		logM.SetChange(opFlowerLog)
		flowerM.SetChange(srv, opFlower)
		//对方在线则通知客户端
		if opUser := srv.UserM().GetUser(opUID); opUser != nil {
			opUser.SendCmdToGateway(cl.ID_MSG_L2C_FlowerAssistNotify, &cl.L2C_FlowerAssistNotify{})
		}
		//更新每日次数
		args.Caller.AddNumByType(uint32(common.PURCHASEID_FLOWER_SNATCH_DAILY_COUNT), numSummary)
	}
	smsg.ReportId = reportID
	smsg.Win = isWin
	args.Caller.SendCmdToGateway(cl.ID_MSG_L2C_FlowerSnatch, smsg)
	args.Caller.LogFlowerSnatch(srv, opUID, args.CtxID, smsg.Win, false)
	opUser.LogFlowerBeSnatch(srv, args.UID, args.CtxID, !smsg.Win)
	args.Caller.FireCommonEvent(srv.EventM(), uint32(event.IeFlowerLoot), 1)
	return true
}

type C2LFlowerAttackLevelGuardCommand struct {
	base.UserCommand
}

func (c *C2LFlowerAttackLevelGuardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerAttackLevelGuardCommand) Error(msg *cl.L2C_FlowerAttackLevelGuard, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerAttackLevelGuard, msg)
	return false
}

func (c *C2LFlowerAttackLevelGuardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerAttackLevelGuard{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerAttackLevelGuard Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerAttackLevelGuard: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerAttackLevelGuard{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}
	oldLevel := flower.GetLevel()

	if goxml.GetData().FlowerLvInfoM.IsTopLv(oldLevel) {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard flower level:%d is top", c.Msg.UID, oldLevel)
		return c.Error(smsg, uint32(cret.RET_FLOWER_LEVEL_IS_TOP))
	}

	lvInfo := goxml.GetData().FlowerLvInfoM.Index(oldLevel)
	if lvInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard Get levelInfo:%d err", c.Msg.UID, oldLevel)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if lvInfo.Type != goxml.FlowerLvUpTypeBeatGuard {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard level up :%d type:%d is not betGuard", c.Msg.UID, oldLevel, lvInfo.Type)
		return c.Error(smsg, uint32(cret.RET_FLOWER_LEVEL_UP_METHOD_ERROR))
	}

	if !helper.CheckBytesLen(cmsg.ClientData, character.MaxClientDataLen) {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard: ClientData too long", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	//检查进攻阵容
	attackFormation := c.User.FormationManager().Get(uint32(common.FORMATION_ID_FI_FLOWER_ATTACK))
	if attackFormation == nil {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard: no attack formation", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NO_ATTACK_FORMATION))
	}

	win, reportID, ret := c.User.AttackFlowerRobot(c.Srv, lvInfo.MonsterGroup, cmsg.ClientData)
	if ret != cret.RET_OK {
		l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard level up :%d type:%d is not betGuard", c.Msg.UID, oldLevel, lvInfo.Type)
		return c.Error(smsg, uint32(cret.RET_FLOWER_BATTLE_ERROR))
	}

	if win {
		flower.AddLevel()
		awards := make([]*cl.Resource, 0, len(lvInfo.GuardRewardClRes)+len(lvInfo.RewardClRes))
		awards = append(awards, lvInfo.GuardRewardClRes...)
		awards = append(awards, lvInfo.RewardClRes...)

		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_FLOWER_RECV_LV_AWARD), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_FlowerAttackLevelGuard: award err. drop:%v", c.Msg.UID, awards)
			return c.Error(smsg, smsg.Ret)
		}
		c.User.FireCommonEvent(c.Srv.EventM(), event.IeFlowerLevel, uint64(flower.GetLevel()), oldLevel)
		c.User.LogFlowerLevelUp(c.Srv, flower.GetLevel(), oldLevel, flower.GetExp())
	}
	c.User.LogFlowerAttackLevelGuard(c.Srv, lvInfo.MonsterGroup, win)

	flowerM.SetChange(c.Srv, flower)

	smsg.Level = flower.GetLevel()
	smsg.ReportId = reportID
	smsg.Win = win

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerAttackLevelGuard, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LFlowerLevelUpCommand struct {
	base.UserCommand
}

func (c *C2LFlowerLevelUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerLevelUpCommand) Error(msg *cl.L2C_FlowerLevelUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerLevelUp, msg)
	return false
}

func (c *C2LFlowerLevelUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerLevelUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerLevelUp Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerLevelUp: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_FlowerLevelUp{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerLevelUp: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerLevelUp: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	flower := flowerM.Get(c.Msg.UID)
	if flower == nil {
		l4g.Errorf("user: %d C2L_FlowerLevelUp: flower not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	oldLevel := flower.GetLevel()

	if goxml.GetData().FlowerLvInfoM.IsTopLv(oldLevel) {
		l4g.Errorf("user: %d C2L_FlowerLevelUp flower level:%d is top", c.Msg.UID, oldLevel)
		return c.Error(smsg, uint32(cret.RET_FLOWER_LEVEL_IS_TOP))
	}

	lvInfo := goxml.GetData().FlowerLvInfoM.Index(oldLevel)
	if lvInfo == nil {
		l4g.Errorf("user: %d C2L_FlowerLevelUp Get levelInfo:%d err", c.Msg.UID, oldLevel)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if lvInfo.Type != goxml.FlowerLvUpTypeCostExp {
		l4g.Errorf("user: %d C2L_FlowerLevelUp level up :%d type:%d is not cost exp", c.Msg.UID, oldLevel, lvInfo.Type)
		return c.Error(smsg, uint32(cret.RET_FLOWER_LEVEL_UP_METHOD_ERROR))
	}

	if !flower.CanReduceExp(lvInfo.Exp) {
		l4g.Errorf("user: %d C2L_FlowerLevelUp level up :%d failed exp:%d need exp :%d", c.Msg.UID, oldLevel, flower.GetExp(), lvInfo.Exp)
		return c.Error(smsg, uint32(cret.RET_NOT_ENOUGH_RESOURCES))
	}

	flower.ReduceExp(lvInfo.Exp)
	flower.AddLevel()

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, lvInfo.RewardClRes, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_FLOWER_RECV_LV_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerLevelUp: award err. drop:%v", c.Msg.UID, lvInfo.RewardClRes)
		return c.Error(smsg, smsg.Ret)
	}

	flowerM.SetChange(c.Srv, flower)
	smsg.Level = flower.GetLevel()
	smsg.Exp = flower.GetExp()

	c.User.LogFlowerLevelUp(c.Srv, flower.GetLevel(), oldLevel, flower.GetExp())
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeFlowerLevel, uint64(smsg.Level), oldLevel)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerLevelUp, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LFlowerAssistSendLikeCommand struct {
	base.UserCommand
}

func (c *C2LFlowerAssistSendLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerAssistSendLikeCommand) Error(msg *cl.L2C_FlowerAssistSendLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerAssistSendLike, msg)
	return false
}

//nolint:funlen
func (c *C2LFlowerAssistSendLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerAssistSendLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2LFlowerAssistSendLike: Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2LFlowerAssistSendLike: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_FlowerAssistSendLike{
		Ret: uint32(cret.RET_OK),
	}

	if len(cmsg.LogIds) == 0 {
		l4g.Errorf("user: %d C2LFlowerAssistSendLike: no logIds", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if uint32(len(cmsg.LogIds)) > goxml.FlowerLogMaxNum {
		l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId num %d exceed the limit %d", c.Msg.UID, len(cmsg.LogIds), goxml.FlowerLogMaxNum)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2LFlowerAssistSendLike: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2LFlowerAssistSendLike: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	myFlower := flowerM.Get(c.Msg.UID)
	if myFlower == nil {
		l4g.Errorf("user: %d C2LFlowerAssistSendLike: get data error", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	//获取玩家的协助日志信息
	logM := flowerM.GetSnatchLogM()
	logOwner := myFlower.GetSnatchLogManager()
	myFlowerLog := logM.GetOwnerLogs(logOwner) //此处确保了myFlowerLog不为空
	isRepeat := make(map[uint64]struct{}, len(cmsg.LogIds))

	//第一次循环做检查
	for _, logId := range cmsg.LogIds {
		//检查日志id是否重复
		if _, exist := isRepeat[logId]; exist {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId %d is repeated", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		isRepeat[logId] = struct{}{}

		//根据日志唯一id获取日志信息
		iLog := myFlowerLog.GetLog(logId)
		if iLog == nil {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId %d not exist", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_FLOWER_LOG_NOT_EXIST))
		}
		flowerLog, ok := iLog.(*flower.AssistLog)
		if !ok {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId %d is not flower log", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}
		//检查日志类型
		if flowerLog.GetLogType() != uint32(common.FLOWER_LOG_FL_ASSISTED) {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId %d type err", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		//检查是否已赠送友情点
		if flowerLog.IsFriendPointSent() {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId %d already sent ", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		opUid := flowerLog.GetEnemyID()
		opFlower := flowerM.Get(opUid)
		if opFlower == nil {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: flower not exist, opUID %d", c.Msg.UID, opUid)
			return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
		}
	}

	//第二次循环做更新
	for _, logId := range cmsg.LogIds {
		iLog := myFlowerLog.GetLog(logId)
		if iLog == nil {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId %d not exist", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_FLOWER_LOG_NOT_EXIST))
		}
		flowerLog, ok := iLog.(*flower.AssistLog)
		if !ok {
			l4g.Errorf("user: %d C2LFlowerAssistSendLike: logId %d is not flower log", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}

		//玩家修改自己的日志状态
		flowerLog.SendFriendPoint()
		myFlowerLog.ModifyLog(flowerLog)
		logM.SetChange(myFlowerLog)
		flowerM.SetChange(c.Srv, myFlower)

		//玩家2赠送玩家1友情点时为玩家1生成收取友情点日志
		opUid := flowerLog.GetEnemyID()
		opFlower := flowerM.Get(opUid)
		myUser := c.User.NewMiniUserSnapshot()
		logOwner := opFlower.GetSnatchLogManager()
		opFlowerLog := logM.GetOwnerLogs(logOwner)
		detail := flower.NewFlowerSnatchLogDetailRecv()
		log := flower.NewFlowerSnatchLog(myUser, c.Srv.CreateUniqueID(), detail)
		opFlowerLog.AddLog(c.Srv, log)
		logOwner.SetNewLogTip(true)
		logM.SetChange(opFlowerLog)
		flowerM.SetChange(c.Srv, opFlower)

		smsg.LogIds = append(smsg.LogIds, logId)
	}

	if c.ResultOK(smsg.Ret) {
		c.User.LogFlowerAssistSendLike(c.Srv, smsg.LogIds)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerAssistSendLike, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LFlowerAssistRecvLikeCommand struct {
	base.UserCommand
}

func (c *C2LFlowerAssistRecvLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LFlowerAssistRecvLikeCommand) Error(msg *cl.L2C_FlowerAssistRecvLike, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerAssistRecvLike, msg)
	return false
}

//nolint:funlen
func (c *C2LFlowerAssistRecvLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_FlowerAssistRecvLike{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_FlowerAssistRecvLike Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_FlowerAssistRecvLike: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_FlowerAssistRecvLike{
		Ret: uint32(cret.RET_OK),
	}

	if len(cmsg.LogIds) == 0 {
		l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: no logIds", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if uint32(len(cmsg.LogIds)) > goxml.FlowerLogMaxNum {
		l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId num %d exceed the limit %d", c.Msg.UID, len(cmsg.LogIds), goxml.FlowerLogMaxNum)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_FLOWER), c.Srv) {
		l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	flowerM, ok := c.Srv.GetActivity(activity.Flower).(*flower.Manager)
	if !ok {
		l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: flowerManager not exist", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	myFlower := flowerM.Get(c.Msg.UID)
	if myFlower == nil {
		l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: get data error", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FLOWER_NOT_EXIST))
	}

	//获取玩家的协助日志信息
	logM := flowerM.GetSnatchLogM()
	logOwner := myFlower.GetSnatchLogManager()
	myFlowerLog := logM.GetOwnerLogs(logOwner) //此处确保了myFlowerLog不为空
	isRepeat := make(map[uint64]struct{}, len(cmsg.LogIds))

	//第一次循环做检查
	for _, logId := range cmsg.LogIds {
		//检查日志id是否重复
		if _, exist := isRepeat[logId]; exist {
			l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId %d is repeated", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		isRepeat[logId] = struct{}{}
		//根据日志唯一id获取日志信息
		iLog := myFlowerLog.GetLog(logId)
		if iLog == nil {
			l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId %d not exist", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_FLOWER_LOG_NOT_EXIST))
		}
		flowerLog, ok := iLog.(*flower.AssistLog)
		if !ok {
			l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId %d is not flower log", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}
		//检查日志类型
		if flowerLog.GetLogType() != uint32(common.FLOWER_LOG_FL_ASSIST) {
			l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId %d type err", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		//检查是否已收取友情点
		if flowerLog.IsFriendPointRecv() {
			l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId %d already recv", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	//第二次循环做更新
	for _, logId := range cmsg.LogIds {
		iLog := myFlowerLog.GetLog(logId)
		if iLog == nil {
			l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId %d not exist", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_FLOWER_LOG_NOT_EXIST))
		}
		flowerLog, ok := iLog.(*flower.AssistLog)
		if !ok {
			l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: logId %d is not flower log", c.Msg.UID, logId)
			return c.Error(smsg, uint32(cret.RET_ERROR))
		}

		//玩家修改自己的日志状态
		flowerLog.RecvFriendPoint()
		myFlowerLog.ModifyLog(flowerLog)
		logM.SetChange(myFlowerLog)
		flowerM.SetChange(c.Srv, myFlower)

		smsg.LogIds = append(smsg.LogIds, logId)
	}

	awards := []*cl.Resource{{
		Type:  uint32(common.RESOURCE_TOKEN),
		Value: uint32(common.TOKEN_TYPE_FRIEND_LIKE),
		Count: uint32(len(smsg.LogIds)) * goxml.GetData().FlowerConfigInfoM.GetAssistRes(),
	}}
	aRet, showAwards := c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_FLOWER_ASSIST_RECV), 0)
	if aRet != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_FlowerAssistRecvLike: award failed, awards:%+v", c.Msg.UID, awards)
	}
	smsg.Awards = showAwards

	if c.ResultOK(smsg.Ret) {
		c.User.LogFlowerAssistRecvLike(c.Srv, smsg.LogIds)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_FlowerAssistRecvLike, smsg)
	return c.ResultOK(smsg.Ret)
}
