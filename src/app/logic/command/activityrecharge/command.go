package activityrecharge

import (
	"context"

	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityRechargeGet), &C2LActivityRechargeGetCommand{}, state) //拉取信息
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityRechargeBuy), &C2LActivityRechargeBuyCommand{}, state) //购买礼包
}

type C2LActivityRechargeGetCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityRechargeGetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityRechargeGetCommand) Error(msg *cl.L2C_ActivityRechargeGet, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityRechargeGet, msg)
	return false
}

func (c *C2LActivityRechargeGetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityRechargeGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityRechargeGet Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityRechargeGet: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	//临时添加，下一版character.Servicer 添加ETCD管理器
	linkSummonId := c.Srv.GetLinkSummonId()
	//活动商店检查重置
	c.User.ActivityRecharge().CheckResetActivityShop(c.Srv, linkSummonId)

	smsg := &cl.L2C_ActivityRechargeGet{
		Ret:              uint32(cret.RET_OK),
		ActivityRecharge: c.User.ActivityRecharge().Clone(),
	}
	//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_RECHARGE)) {
	//	l4g.Errorf("user: %d C2L_ActivityRechargeGet: function not open", c.Msg.UID)
	//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	//}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityRechargeGet, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LActivityRechargeBuyCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityRechargeBuyCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityRechargeBuyCommand) Error(msg *cl.L2C_ActivityRechargeBuy, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityRechargeBuy, msg)
	return false
}

func (c *C2LActivityRechargeBuyCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityRechargeBuy{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityRechargeBuy Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityRechargeBuy: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_ActivityRechargeBuy{
		Ret:    uint32(cret.RET_OK),
		ShopId: cmsg.ShopId,
		GiftId: cmsg.GiftId,
	}

	if cmsg.ShopId == 0 || cmsg.GiftId == 0 || cmsg.BuyNum == 0 {
		l4g.Errorf("user: %d C2L_ActivityRechargeBuy: param err. shopID:%d giftID:%d",
			c.Msg.UID, cmsg.ShopId, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	shopInfo := goxml.GetData().ActivityRechargeShopInfoM.Index(cmsg.ShopId)
	if shopInfo == nil {
		l4g.Errorf("user:%d ActivityRecharge no find shop:%d", c.Msg.UID, cmsg.ShopId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if shopInfo.FuncId > 0 {
		if !c.User.IsFunctionOpen(shopInfo.FuncId, c.Srv) {
			l4g.Errorf("user: %d C2L_ActivityRechargeBuy: function not open", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
		}
	}

	giftInfo := goxml.GetData().ActivityRechargeGiftInfoM.Index(cmsg.GiftId)
	if giftInfo == nil {
		l4g.Errorf("user: %d C2L_ActivityRechargeBuy: no find gift info:%d", c.Msg.UID, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	activityRecharge := c.User.ActivityRecharge()
	retCode, award := activityRecharge.CheckBuy(c.Srv, shopInfo, giftInfo, cmsg.BuyNum)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivityRechargeBuy: check buy error. shopID:%d giftID:%d", c.Msg.UID, cmsg.ShopId, cmsg.GiftId)
		return c.Error(smsg, retCode)
	}

	if giftInfo.InternalId == 0 {
		//直接买

		smsg.Awards = award
		smsg.BuyType = uint32(common.ACTIVITY_RECHARGE_SHOP_BUY_TYPE_ARSBT_SUCCESS)

		var mergeAward []*cl.Resource
		costRes := activityRecharge.RecalcRes(cmsg.BuyNum, giftInfo.CostRes)
		if len(costRes) > 0 {
			smsg.Ret, mergeAward = c.User.Trade(c.Srv, costRes, award, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_RECHARGE_BUY), 0)
		} else {
			smsg.Ret, mergeAward = c.User.Award(c.Srv, award, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_RECHARGE_BUY), 0)
		}
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_ActivityRechargeBuy: award error. shopID:%d giftID:%d", c.Msg.UID, cmsg.ShopId, cmsg.GiftId)
			return c.Error(smsg, smsg.Ret)
		}
		activityRecharge.Buy(cmsg.ShopId, cmsg.BuyNum, giftInfo)
		smsg.Shop = activityRecharge.FlushShop(cmsg.ShopId)
		c.User.LogActivityRechargeBuy(c.Srv, cmsg.ShopId, cmsg.GiftId, mergeAward)

	} else {
		smsg.BuyType = uint32(common.ACTIVITY_RECHARGE_SHOP_BUY_TYPE_ARSBT_CHECK)
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityRechargeBuy, smsg)
	return c.ResultOK(smsg.Ret)
}
