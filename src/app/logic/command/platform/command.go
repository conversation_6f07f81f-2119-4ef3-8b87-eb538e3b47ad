package platform

import (
	"app/goxml"
	"app/protos/out/cl"
	"context"
	"fmt"

	pm "app/logic/platform"
	"app/protos/in/p2l"
	"app/protos/out/ret"
	"app/service"

	"gitlab.qdream.com/kit/sea/zebra/parse"

	l4g "github.com/ivanabc/log4go"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(p2l.ID_MSG_L2P_ChangeEtcdConfig), &L2PChangeEtcdConfigCommand{}, state) //etcd 配置变动更新
	cmds.Register(uint32(p2l.ID_MSG_L2P_UseGiftCode), &L2PUseGiftCodeCommand{}, state)           //请求平台使用礼包码
	cmds.Register(uint32(p2l.ID_MSG_L2P_ChatGetToken), &L2PChatGetTokenCommand{}, state)         //聊天 - 获取token
	cmds.Register(uint32(p2l.ID_MSG_L2P_ChatSyncUser), &L2PChatSyncUserCommand{}, state)         //聊天 - 同步玩家数据
	cmds.Register(uint32(p2l.ID_MSG_L2P_ChatJoinGroup), &L2PChatJoinGroupCommand{}, state)       //聊天 - 加入群组
	//cmds.Register(uint32(p2l.ID_MSG_L2P_ChatDismissGroup), &L2PChatDismissGroupCommand{}, state) //聊天 - 退出群组
	cmds.Register(uint32(p2l.ID_MSG_L2P_ChatSendMsg), &L2PChatSendMsgCommand{}, state) //聊天 - 推送系统消息
	//cmds.Register(uint32(p2l.ID_MSG_L2P_ChatBan), &L2PChatBanCommand{}, state)         //聊天 - 封禁/解禁
	cmds.Register(uint32(p2l.ID_MSG_L2P_FlowerPushMsg), &L2PFlowerPushMsgCommand{}, state)                   //密林 - 推送消息
	cmds.Register(uint32(p2l.ID_MSG_L2P_GetSeasonFlashBackData), &L2PGetSeasonFlashBackDataCommand{}, state) // 获取赛季成就回顾数据
	cmds.Register(uint32(p2l.ID_MSG_L2P_SensitiveWordCheck), &L2PSensitiveWordCheckCommand{}, state)
	cmds.Register(uint32(p2l.ID_MSG_L2P_RebaseGet), &L2PRebaseGetCommand{}, state)
	cmds.Register(uint32(p2l.ID_MSG_L2P_MuteAccount), &L2PMuteAccountCommand{}, state)
}

func getHTTPActor(ctx context.Context) *pm.HTTPActor {
	return service.GetService(ctx).(*pm.HTTPActor)
}

type BaseCommand struct {
	msg *pm.HTTPRequest
}

func (b *BaseCommand) OnPause(ctx context.Context, msg parse.Messager) {
}

func (b *BaseCommand) OnBefore(ctx context.Context, msg parse.Messager) bool {
	b.msg = msg.(*pm.HTTPRequest)
	return true
}

func (b *BaseCommand) OnAfter(ctx context.Context, result bool) {
}

type L2PChangeEtcdConfigCommand struct {
	BaseCommand
}

func (l *L2PChangeEtcdConfigCommand) Execute(ctx context.Context) bool {
	actor := getHTTPActor(ctx)
	msg := l.msg.Data.(*p2l.L2P_ChangeEtcdConfig)
	l4g.Debugf("[L2P_ChangeEtcdConfig] recv data: %d %+v", l.msg.PackHead.UID, msg)

	actor.UpdateEtcdConfig(msg.EtcdConfig)

	return true
}

type L2PUseGiftCodeCommand struct {
	BaseCommand
}

func (l *L2PUseGiftCodeCommand) Execute(ctx context.Context) bool {
	httpA := getHTTPActor(ctx)
	recv := l.msg.Data.(*p2l.L2P_UseGiftCode)
	l4g.Debugf("[UseGiftCode] recv data: %d %+v", l.msg.PackHead.UID, recv)

	msg := &p2l.P2L_UseGiftCode{
		Ret:  uint32(ret.RET_OK),
		Code: recv.Code,
	}

	msg.Ret, msg.AwardId = httpA.UseGiftCode(recv)
	httpA.ReturnMessage(uint32(p2l.ID_MSG_P2L_UseGiftCode), l.msg.PackHead.UID, msg)
	return true
}

type L2PChatGetTokenCommand struct {
	BaseCommand
}

func (l *L2PChatGetTokenCommand) Execute(ctx context.Context) bool {
	actor := getHTTPActor(ctx)
	cmsg := l.msg.Data.(*p2l.L2P_ChatGetToken)
	l4g.Debugf("[L2P_ChatGetToken] recv data: %d %+v", l.msg.PackHead.UID, cmsg)

	smsg := &p2l.P2L_ChatGetToken{
		Ret:       uint32(ret.RET_OK),
		CrossArea: cmsg.CrossArea,
		IsHand:    cmsg.IsHand,
		Lang:      cmsg.Lang,
	}

	err, resp := actor.ChatGetToken(cmsg)
	if err != nil {
		l4g.Errorf("user: %d L2P_ChatGetToken: get token failed. err: %s", l.msg.PackHead.UID, err.Error())
		smsg.Ret = uint32(ret.RET_ERROR)
		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatGetToken), l.msg.PackHead.UID, smsg)
		return false
	}
	if resp == nil {
		l4g.Errorf("user: %d L2P_ChatGetToken: get token failed, http return resp is nil.", l.msg.PackHead.UID)
		smsg.Ret = uint32(ret.RET_ERROR)
		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatGetToken), l.msg.PackHead.UID, smsg)
		return false
	}
	// 记录下返回的错误tag
	if len(resp.FailedTagErrorMap) > 0 {
		l4g.Errorf("user: %d L2P_ChatGetToken: failed group tag. tag: %+v", l.msg.PackHead.UID, resp.FailedTagErrorMap)
	}

	for tag := range resp.SuccessTagGroupMap {
		smsg.SuccessTags = append(smsg.SuccessTags, tag)
	}
	smsg.Token = resp.Token
	smsg.ExpireTime = resp.ExpireTimeSeconds
	smsg.FailedTagErrors = resp.FailedTagErrorMap

	actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatGetToken), l.msg.PackHead.UID, smsg)

	return true
}

type L2PChatJoinGroupCommand struct {
	BaseCommand
}

func (l *L2PChatJoinGroupCommand) Execute(ctx context.Context) bool {
	actor := getHTTPActor(ctx)
	cmsg := l.msg.Data.(*p2l.L2P_ChatJoinGroup)
	l4g.Debugf("[L2P_ChatJoinGroup] recv data: %d %+v", l.msg.PackHead.UID, cmsg)
	smsg := &p2l.P2L_ChatJoinGroup{
		Ret:      uint32(ret.RET_OK),
		Uid:      cmsg.Uid,
		GroupOp:  cmsg.GroupOp,
		GroupTag: cmsg.GroupTag,
	}

	err, resp := actor.ChatJoinGroup(cmsg)
	if err != nil {
		l4g.Errorf("user: %d L2P_ChatJoinGroup: join group failed. err: %s", l.msg.PackHead.UID, err.Error())
		smsg.Ret = uint32(ret.RET_ERROR)
		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatJoinGroup), l.msg.PackHead.UID, smsg)
		return false
	}
	if resp == nil {
		l4g.Errorf("user: %d L2P_ChatJoinGroup: join group failed, http return resp is nil.", l.msg.PackHead.UID)
		smsg.Ret = uint32(ret.RET_ERROR)
		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatJoinGroup), l.msg.PackHead.UID, smsg)
		return false
	}
	if len(resp.FailedTagErrorMap) > 0 {
		l4g.Errorf("user: %d L2P_ChatJoinGroup: failed group tag. tag: %+v", l.msg.PackHead.UID, resp.FailedTagErrorMap)
	}

	for tag := range resp.SuccessTagGroupMap {
		smsg.SuccessTags = append(smsg.SuccessTags, tag)
	}
	actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatJoinGroup), l.msg.PackHead.UID, smsg)

	return true
}

//type L2PChatDismissGroupCommand struct {
//	BaseCommand
//}
//
//func (l *L2PChatDismissGroupCommand) Execute(ctx context.Context) bool {
//	actor := getHTTPActor(ctx)
//	cmsg := l.msg.Data.(*p2l.L2P_ChatDismissGroup)
//	l4g.Debugf("[L2P_ChatDismissGroup] recv data: %d %+v", l.msg.PackHead.UID, cmsg)
//
//	smsg := &p2l.P2L_ChatDismissGroup{
//		Ret:      uint32(ret.RET_OK),
//		GroupId:  cmsg.DismissGroup.GroupID,
//		GroupTag: cmsg.DismissGroup.GroupTag,
//	}
//	err := actor.ChatDismissGroup(cmsg)
//	if err != nil {
//		l4g.Errorf("user: %d L2P_ChatDismissGroup: quit group failed. err: %s", l.msg.PackHead.UID, err.Error())
//		smsg.Ret = uint32(ret.RET_ERROR)
//		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatDismissGroup), l.msg.PackHead.UID, smsg)
//		return false
//	}
//
//	actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_ChatDismissGroup), l.msg.PackHead.UID, smsg)
//
//	return true
//}

type L2PChatSyncUserCommand struct {
	BaseCommand
}

func (l *L2PChatSyncUserCommand) Execute(ctx context.Context) bool {
	actor := getHTTPActor(ctx)
	cmsg := l.msg.Data.(*p2l.L2P_ChatSyncUser)
	l4g.Debugf("[L2P_ChatSyncUser] recv data: %d %+v", l.msg.PackHead.UID, cmsg)

	err := actor.ChatSyncUser(cmsg)
	if err != nil {
		l4g.Errorf("user: %d L2P_ChatSyncUser: sync user failed. err: %s", l.msg.PackHead.UID, err.Error())
		return false
	}

	return true
}

type L2PChatSendMsgCommand struct {
	BaseCommand
}

func (l *L2PChatSendMsgCommand) Execute(ctx context.Context) bool {
	actor := getHTTPActor(ctx)
	cmsg := l.msg.Data.(*p2l.L2P_ChatSendMsg)
	l4g.Debugf("[L2P_ChatSendMsg] recv data: %d %+v", l.msg.PackHead.UID, cmsg)

	actor.ChatSendMsg(cmsg)

	return true
}

//type L2PChatBanCommand struct {
//	BaseCommand
//}
//
//func (l *L2PChatBanCommand) Execute(ctx context.Context) bool {
//	actor := getHTTPActor(ctx)
//	cmsg := l.msg.Data.(*p2l.L2P_ChatBan)
//	l4g.Debugf("[L2P_ChatBan] recv data: %d %+v", l.msg.PackHead.UID, cmsg)
//
//	actor.ChatBan(cmsg)
//
//	return true
//}

type L2PFlowerPushMsgCommand struct {
	BaseCommand
}

func (l *L2PFlowerPushMsgCommand) Execute(ctx context.Context) bool {
	actor := getHTTPActor(ctx)
	msg := l.msg.Data.(*p2l.L2P_FlowerPushMsg)
	l4g.Debugf("[L2P_FlowerPushMsg] recv data: %d %+v", l.msg.PackHead.UID, msg)

	actor.FlowerPush(msg)

	return true
}

type L2PGetSeasonFlashBackDataCommand struct {
	BaseCommand
}

func (l *L2PGetSeasonFlashBackDataCommand) Execute(ctx context.Context) bool {
	actor := getHTTPActor(ctx)
	msg := l.msg.Data.(*p2l.L2P_GetSeasonFlashBackData)
	l4g.Debugf("[L2P_GetSeasonFlashBackData] recv data: %d %+v", l.msg.PackHead.UID, msg)

	pmsg := &p2l.P2L_GetSeasonFlashBackData{
		Ret:      uint32(ret.RET_OK),
		SeasonId: msg.SeasonId,
		Uid:      msg.Uid,
	}

	infos := goxml.GetData().SeasonFlashBackParamsInfoM.GetSeasonInfos(msg.SeasonId)
	if len(infos) == 0 {
		pmsg.Ret = uint32(ret.RET_SYSTEM_DATA_ERROR)
		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_GetSeasonFlashBackData), l.msg.PackHead.UID, pmsg)
		return false
	}

	respData, retCode := actor.GetSeasonFlashBackData(msg)

	if retCode != uint32(ret.RET_OK) {
		pmsg.Ret = retCode
		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_GetSeasonFlashBackData), l.msg.PackHead.UID, pmsg)
		return false
	}

	if respData == nil {
		pmsg.Ret = uint32(ret.RET_SYSTEM_DATA_ERROR)
		actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_GetSeasonFlashBackData), l.msg.PackHead.UID, pmsg)
		return false
	}

	for id, info := range infos {
		point := &cl.SeasonUserLogPoint{
			Id: id,
		}
		point.Data = make(map[string]string)
		if info.Value1 > 0 {
			point.Values = append(point.Values, respData[fmt.Sprintf("%d", info.Value1)])
			point.Data[fmt.Sprintf("%d", info.Value1)] = respData[fmt.Sprintf("%d", info.Value1)]
		}
		if info.Value2 > 0 {
			point.Values = append(point.Values, respData[fmt.Sprintf("%d", info.Value2)])
			point.Data[fmt.Sprintf("%d", info.Value2)] = respData[fmt.Sprintf("%d", info.Value2)]
		}
		if info.Value3 > 0 {
			point.Values = append(point.Values, respData[fmt.Sprintf("%d", info.Value3)])
			point.Data[fmt.Sprintf("%d", info.Value3)] = respData[fmt.Sprintf("%d", info.Value3)]
		}
		if info.Value4 > 0 {
			point.Values = append(point.Values, respData[fmt.Sprintf("%d", info.Value4)])
			point.Data[fmt.Sprintf("%d", info.Value4)] = respData[fmt.Sprintf("%d", info.Value4)]
		}
		pmsg.Points = append(pmsg.Points, point)
	}

	actor.ReturnMessage(uint32(p2l.ID_MSG_P2L_GetSeasonFlashBackData), l.msg.PackHead.UID, pmsg)
	return true
}

type L2PSensitiveWordCheckCommand struct {
	BaseCommand
}

func (l *L2PSensitiveWordCheckCommand) Execute(ctx context.Context) bool {
	httpA := getHTTPActor(ctx)
	recv := l.msg.Data.(*p2l.L2P_SensitiveWordCheck)
	l4g.Debugf("[SensitiveWordCheck] recv data: %d %+v", l.msg.PackHead.UID, recv)

	msg := &p2l.P2L_SensitiveWordCheck{
		Ret:        uint32(ret.RET_OK),
		AsyncMsgId: recv.AsyncMsgId,
	}

	for _, req := range recv.Reqs {
		msg.Ret = httpA.CheckSensitive(req)
		if msg.Ret != uint32(ret.RET_OK) {
			break
		}
	}
	httpA.ReturnMessage(uint32(p2l.ID_MSG_P2L_SensitiveWordCheck), l.msg.PackHead.UID, msg)
	return true
}

type L2PRebaseGetCommand struct {
	BaseCommand
}

func (l *L2PRebaseGetCommand) Execute(ctx context.Context) bool {
	httpA := getHTTPActor(ctx)
	recv := l.msg.Data.(*p2l.L2P_RebaseGet)
	l4g.Debugf("[L2P_RebaseGet] recv data: %d %+v", l.msg.PackHead.UID, recv)

	msg := httpA.RebaseGet(recv)
	l4g.Debugf("[L2P_RebaseGet] RebaseGet ret uid:%d msg %+v", l.msg.PackHead.UID, msg)
	httpA.ReturnMessage(uint32(p2l.ID_MSG_P2L_RebaseGet), l.msg.PackHead.UID, msg)
	return true
}

type L2PMuteAccountCommand struct {
	BaseCommand
}

func (l *L2PMuteAccountCommand) Execute(ctx context.Context) bool {
	httpA := getHTTPActor(ctx)
	recv := l.msg.Data.(*p2l.L2P_MuteAccount)
	l4g.Debugf("[L2P_MuteAccount] recv data: %d %+v", l.msg.PackHead.UID, recv)

	msg := httpA.MuteAccount(recv)
	l4g.Debugf("[L2P_MuteAccount] MuteAccount ret uid:%d msg %+v", l.msg.PackHead.UID, msg)
	httpA.ReturnMessage(uint32(p2l.ID_MSG_P2L_MuteAccount), l.msg.PackHead.UID, msg)
	return true
}
