package seasonmap

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/logic/event"
	"app/protos/in/db"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"app/protos/out/ret"
	"context"

	"gitlab.qdream.com/kit/sea/time"

	// "app/protos/out/common"
	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapGetData), &C2LSeasonMapGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapFight), &C2LSeasonMapFightCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapMovePosition), &C2LSeasonMapMovePositionCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapTaskReward), &C2LSeasonMapTaskRewardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapDialogue), &C2LSeasonMapDialogueCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapTrade), &C2LSeasonMapTradeCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapMaster), &C2LSeasonMapMasterCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapAltar), &C2LSeasonMapAltarCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapPositionLogs), &C2LSeasonMapPositionLogsCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapBuyStamina), &C2LSeasonMapBuyStaminaCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapGetRankList), &C2LSeasonMapGetRankListCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_SeasonMapPassPosition), &C2LSeasonMapPassPositionCommand{}, state)
}

type C2LSeasonMapGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSeasonMapGetDataCommand) Return(msg *cl.L2C_SeasonMapGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapGetData, msg)
	return true
}

func (c *C2LSeasonMapGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapGetData: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapGetData{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapGetData: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	c.User.SeasonMap().RecoverStamina(c.Srv)
	retCode, rewards := c.User.SeasonMap().RecoverCurrency(c.Srv)
	if retCode != ret.RET_OK {
		l4g.Errorf("user: %d C2L_SeasonMapGetData: error", c.Msg.UID)
		return c.Return(smsg, uint32(retCode))
	}
	smsg.Data = c.User.SeasonMap().FlushData()
	smsg.Rewards = rewards
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapFightCommand struct {
	base.UserCommand
}

func (c *C2LSeasonMapFightCommand) Return(msg *cl.L2C_SeasonMapFight, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapFight, msg)
	return true
}

//nolint:funlen
func (c *C2LSeasonMapFightCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapFight{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapFight Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapFight: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapFight{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapFight: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	seasonMap := c.User.SeasonMap()
	seasonMap.RecoverFightStamina(c.Srv)

	positionInfo := goxml.GetData().SeasonMapPositionInfoM.Index(cmsg.Position)
	if positionInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapFight: no positionInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	positionData := seasonMap.GetPositionData(cmsg.Position)
	if positionData == nil {
		l4g.Errorf("user: %d C2L_SeasonMapFight: no positionData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_POSITION_NOT_EXIST))
	}

	var monsterInfo *goxml.SeasonMapMonsterInfoExt
	for _, monsterID := range positionInfo.Monsters {
		monsterInfo = goxml.GetData().SeasonMapMonsterInfoM.Index(monsterID)
		if monsterInfo == nil {
			l4g.Errorf("user: %d C2L_SeasonMapFight: no monsterInfo. monsterId:%d", c.Msg.UID, cmsg.MonsterId)
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		if monsterID == cmsg.MonsterId {
			break
		}
		monsterData := seasonMap.GetMonsterData(positionData, monsterID)
		monsterHpInfo := goxml.GetData().SeasonMapMonsterHpInfoM.GetRecordById(monsterID)
		if monsterHpInfo == nil {
			l4g.Errorf("user: %d C2L_SeasonMapFight: no monsterHpInfo. monsterId:%d", c.Msg.UID, cmsg.MonsterId)
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		if monsterData == nil || monsterData.ReduceHp < monsterHpInfo.MaxHp {
			l4g.Errorf("user: %d C2L_SeasonMapFight: monster not dead. monsterId %d", c.Msg.UID, cmsg.MonsterId)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		monsterInfo = nil
	}

	if monsterInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapFight: no monsterId. position:%d monsterId:%d", c.Msg.UID, cmsg.Position, cmsg.MonsterId)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	monsterHpInfo := goxml.GetData().SeasonMapMonsterHpInfoM.GetRecordById(cmsg.MonsterId)
	if monsterHpInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapFight: no monsterHpInfo. monsterId:%d", c.Msg.UID, cmsg.MonsterId)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}

	monsterData := seasonMap.GetMonsterData(positionData, monsterInfo.Data.MonsterId)
	if cmsg.IsSweep && (monsterData == nil || monsterData.MaxRecord == nil) {
		// 还没打过
		l4g.Errorf("user: %d C2L_SeasonMapFight: sweepData is nil. monsterId %d", c.Msg.UID, cmsg.MonsterId)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	if monsterData != nil && monsterData.ReduceHp >= monsterHpInfo.MaxHp {
		l4g.Errorf("user: %d C2L_SeasonMapFight: monster dead. monsterId %d", c.Msg.UID, cmsg.MonsterId)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_MONSTER_DEAD))
	}

	cost := goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, monsterInfo.Data.Ap)
	costs := []*cl.Resource{cost}
	retCode, costs := c.User.CheckResourcesSize(costs)
	if retCode != uint32(ret.RET_OK) {
		l4g.Errorf("C2L_SeasonMapFight: CheckResourcesSize error. retCode %d user %d", retCode, c.Msg.UID)
		return c.Return(smsg, retCode)
	}

	var (
		record *cl.SeasonMapMonsterRecord
	)
	if cmsg.IsSweep {
		record = monsterData.MaxRecord.Clone()
	} else {
		battleReport, retCode := c.User.AttackSeasonMap(c.Srv, monsterInfo.Data.MonsterGroup, common.FORMATION_ID(monsterInfo.Data.FormationId), positionInfo.Terrain, monsterInfo.Data.Level, cmsg.ClientData)
		if retCode != ret.RET_OK {
			l4g.Errorf("user:%d C2L_SeasonMapFight battle: error. %d", c.Msg.UID, retCode)
			return c.Return(smsg, uint32(ret.RET_BATTLE_ERROR))
		}
		record = seasonMap.GenerateSeasonMapMonsterRecord(battleReport, monsterHpInfo, cmsg.Position)
	}
	result := seasonMap.UseFightStamina(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_FIGHT))
	if result != ret.RET_OK {
		l4g.Errorf("user: %d C2L_SeasonMapFight: UseStamina error. monsterId %d", c.Msg.UID, cmsg.MonsterId)
		return c.Return(smsg, uint32(retCode))
	}
	dropRewards, dropFlag := c.User.Drop().DoDrop(c.Srv.Rand(), monsterInfo.Data.DropGroup)
	if !dropFlag {
		l4g.Errorf("user: %d C2L_SeasonMapFight: DoDrop get Awards error. DropGroup:%d", c.Msg.UID, monsterInfo.Data.DropGroup)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}

	if guildMedal := c.Srv.GetGuildMedal(c.User.ID()); guildMedal != nil {
		dropRewards = c.addGuildMedal(dropRewards, guildMedal)
	}

	smsg.Ret, smsg.Rewards = c.User.Award(c.Srv, dropRewards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_FIGHT), 0)
	if smsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user:%d C2L_SeasonMapFight: award dropRewards error. ret:%d", c.Msg.UID, smsg.Ret)
		return c.Return(smsg, smsg.Ret)
	}
	if monsterData == nil {
		monsterData = &cl.SeasonMapMonster{
			MonsterId: monsterInfo.Data.MonsterId,
		}
		seasonMap.AddMonsterData(positionData, monsterData)
	}
	battleResult := seasonMap.FinishFight(record, positionData, monsterData, monsterInfo, monsterHpInfo)
	progressRewards := goxml.GetData().SeasonMapMonsterRewardInfoM.CalcRewardsByReduceHp(cmsg.MonsterId, battleResult.BeforeReduceHp, battleResult.AfterReduceHp)
	if len(progressRewards) > 0 {
		smsg.Ret, smsg.ProgressRewards = c.User.Award(c.Srv, progressRewards, character.AwardTagMail,
			uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_FIGHT_PROGRESS), 0)
		if smsg.Ret != uint32(ret.RET_OK) {
			l4g.Errorf("user:%d C2L_SeasonMapFight: award progressAwards error. ret:%d", c.Msg.UID, smsg.Ret)
			return c.Return(smsg, smsg.Ret)
		}
	}
	if battleResult.Dead {
		seasonMap.OnMonsterDead(cmsg.Position, positionData, monsterInfo, c.Srv)
		c.User.FireCommonEvent(c.Srv.EventM(), event.AeSeasonMapBeatX, uint64(monsterInfo.Data.Level), cmsg.Position)
	}
	if battleResult.UpdateMaxRecord {
		c.User.CognitionManager().Send(c.Srv, monsterInfo.Data.FormationId, cmsg.MonsterId, record.TotalReduceHp, record.TotalDamage) // 复用round字段
	}
	c.User.FireCommonEvent(c.Srv.EventM(), event.IeSeasonMapFightX, uint64(1))
	smsg.Position = cmsg.Position
	smsg.MonsterId = cmsg.MonsterId
	smsg.IsSweep = cmsg.IsSweep
	smsg.Record = record
	smsg.ReportId = record.ReportId
	smsg.MonsterData = monsterData.Clone()
	smsg.ExploreRate = seasonMap.GetData().ExploreRate
	smsg.EventData = make(map[uint32]*cl.SeasonMapEvent)
	for eventType, event := range positionData.EventData {
		smsg.EventData[eventType] = event.Clone()
	}
	var teamDamages []uint64
	var teamProgress []uint32
	for _, team := range record.TeamData {
		teamDamages = append(teamDamages, team.TeamDamage)
		teamProgress = append(teamProgress, team.TeamReduceHp)
	}
	battleData := &log.LogSeasonMapFight{
		Position:       cmsg.Position,
		MonsterId:      cmsg.MonsterId,
		FormationId:    monsterInfo.Data.FormationId,
		IsSweep:        cmsg.IsSweep,
		BattleReportId: record.ReportId,
		TeamDamages:    teamDamages,
		TeamProgress:   teamProgress,
		Kill:           battleResult.Dead,
	}
	c.User.LogSeasonMapFight(c.Srv, battleData)
	return c.Return(smsg, uint32(ret.RET_OK))
}

func (c *C2LSeasonMapFightCommand) addGuildMedal(awards []*cl.Resource, guildMedal *db.LogicGuildMedal) []*cl.Resource {
	addValue := uint32(0)
	for _, medal := range guildMedal.Medals {
		info := goxml.GetData().GuildMedalTaskInfoM.Index(medal.GetMedalId())
		if info == nil {
			continue
		}
		if info.AddType != goxml.GuildMedalAddSeasonMap {
			continue
		}
		addValue += info.AddValue
	}
	if addValue == 0 {
		return awards
	}

	newAwards := make([]*cl.Resource, 0, len(awards))
	for _, award := range awards {
		cAward := award.Clone()
		cAward.Count = cAward.Count + cAward.Count*addValue/10000 //nolint:mnd
		newAwards = append(newAwards, cAward)
	}

	return newAwards
}

type C2LSeasonMapMovePositionCommand struct {
	base.UserCommand
}

func (c *C2LSeasonMapMovePositionCommand) Return(msg *cl.L2C_SeasonMapMovePosition, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapMovePosition, msg)
	return true
}

func (c *C2LSeasonMapMovePositionCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapMovePosition{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapMovePosition Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapMovePosition: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapMovePosition{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapMovePosition: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	seasonMap := c.User.SeasonMap()
	positionData := seasonMap.GetPositionData(cmsg.Position)
	if positionData == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMovePosition: no positionData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_POSITION_NOT_EXIST))
	}
	positionInfo := goxml.GetData().SeasonMapPositionInfoM.Index(cmsg.Position)
	if positionInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMovePosition: no positionInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	if len(positionInfo.Monsters) == 0 {
		l4g.Errorf("user: %d C2L_SeasonMapMovePosition: position no monster. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	monsterData := seasonMap.GetMonsterData(positionData, positionInfo.Monsters[0])
	if monsterData == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMovePosition: monsterData is nil. monsterId %d", c.Msg.UID, positionInfo.Monsters[0])
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	} else {
		monsterHpInfo := goxml.GetData().SeasonMapMonsterHpInfoM.GetRecordById(positionInfo.Monsters[0])
		if monsterHpInfo == nil {
			l4g.Errorf("user: %d C2L_SeasonMapMovePosition: no monsterHpInfo. monsterId:%d", c.Msg.UID, positionInfo.Monsters[0])
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		if monsterData.ReduceHp < monsterHpInfo.MaxHp {
			l4g.Errorf("user: %d C2L_SeasonMapMovePosition: monster not dead. monsterId %d", c.Msg.UID, positionInfo.Monsters[0])
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
	}
	seasonMap.Sync2Cross(c.Srv, &l2c.SeasonMapSyncPositionLog{
		Position: cmsg.Position,
		LogType:  l2c.SeasonMapPositionLogType_Move1,
		MoveLog: &cl.SeasonMapMoveLog{
			Name:         c.User.Name(),
			Image:        c.User.GetImage(),
			PokemonImage: c.User.GetPokemonShowImage(),
			From:         seasonMap.GetPosition(),
			To:           cmsg.Position,
		},
	})
	seasonMap.SetPosition(cmsg.Position)
	smsg.Position = cmsg.Position
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapTaskRewardCommand struct {
	base.UserCommand
}

func (c *C2LSeasonMapTaskRewardCommand) Return(msg *cl.L2C_SeasonMapTaskReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapTaskReward, msg)
	return true
}

func (c *C2LSeasonMapTaskRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapTaskReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapTaskReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapTaskReward: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapTaskReward{
		TaskIds: cmsg.TaskIds,
		Type:    cmsg.Type,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapTaskReward: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	seasonMap := c.User.SeasonMap()
	maxLen := goxml.GetData().SeasonMapTaskInfoM.GetTaskMaxLen()
	if len(cmsg.TaskIds) == 0 || len(cmsg.TaskIds) > maxLen {
		l4g.Errorf("user:%d C2L_SeasonMapTaskReward: invalid taskIds. taskLen %d maxLen %d", c.Msg.UID, len(cmsg.TaskIds), maxLen)
		return c.Return(smsg, uint32(ret.RET_PARAM_LENGTH_LIMIT))
	}

	var totalRewards []*cl.Resource
	repeatedM := make(map[uint32]struct{})
	for _, taskId := range cmsg.TaskIds {
		_, exist := repeatedM[taskId]
		if exist {
			l4g.Errorf("user:%d C2L_SeasonMapTaskReward:  task id %d repeated. ", c.Msg.UID, taskId)
			return c.Return(smsg, uint32(ret.RET_REPEATED_PARAM))
		}
		repeatedM[taskId] = struct{}{}

		taskInfo := goxml.GetData().SeasonMapTaskInfoM.Index(taskId)
		if taskInfo == nil {
			l4g.Errorf("user:%d C2L_SeasonMapTaskReward: no taskInfo. id %d", c.Msg.UID, taskId)
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}

		if cmsg.Type != taskInfo.Category {
			l4g.Errorf("user:%d C2L_SeasonMapTaskReward: taskId %d type err", c.Msg.UID, taskId)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}

		if seasonMap.IsRecvAward(taskId) {
			l4g.Errorf("user:%d C2L_SeasonMapTaskReward: taskId %d has received. ", c.Msg.UID, taskId)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}

		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(taskInfo.TypeId)
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d C2L_SeasonMapTaskReward: taskTypeInfo not exist. typeID:%d", c.Msg.UID, taskInfo.TypeId)
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}

		progress := seasonMap.GetTaskProgress(taskTypeInfo)
		if !c.User.CheckTaskFinish(progress, taskInfo.TypeId, uint64(taskInfo.Value)) {
			l4g.Errorf("user:%d C2L_SeasonMapTaskReward: task id:%d not finish", c.Msg.UID, taskId)
			return c.Return(smsg, uint32(ret.RET_SEASON_MAP_TASK_PROGRESS_NOT_ENOUGH))
		}
		for _, res := range taskInfo.ClRes {
			totalRewards = append(totalRewards, res.Clone())
		}
	}
	smsg.Ret, smsg.Rewards = c.User.Award(c.Srv, totalRewards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_TASK_AWARD), 0)
	seasonMap.SetRecvAward(cmsg.TaskIds)

	c.User.LogSeasonMapTaskAward(c.Srv, cmsg.TaskIds)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapDialogueCommand struct {
	base.UserCommand
}

func (c *C2LSeasonMapDialogueCommand) Return(msg *cl.L2C_SeasonMapDialogue, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapDialogue, msg)
	return true
}

func (c *C2LSeasonMapDialogueCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapDialogue{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapDialogue Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapDialogue: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapDialogue{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapDialogue: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	seasonMap := c.User.SeasonMap()
	positionData := seasonMap.GetPositionData(cmsg.Position)
	if positionData == nil {
		l4g.Errorf("user: %d C2L_SeasonMapDialogue: no positionData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_POSITION_NOT_EXIST))
	}
	eventData := seasonMap.GetEventData(positionData, cl.SeasonMapEventType_EventType_Dialogue)
	if eventData == nil || eventData.DialogueEvent == nil {
		l4g.Errorf("user: %d C2L_SeasonMapDialogue: no eventData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_EVENT_NOT_EXIST))
	}
	eventData.DialogueEvent.Finish = true
	seasonMap.Save()
	positionLog := &l2c.SeasonMapSyncPositionLog{
		Position: cmsg.Position,
		LogType:  l2c.SeasonMapPositionLogType_Event1,
		EventLog: &cl.SeasonMapEventLog{
			Name:  c.User.Name(),
			Image: c.User.GetImage(),
			Event: cl.SeasonMapEventType_EventType_Dialogue,
		},
	}
	seasonMap.Sync2Cross(c.Srv, positionLog)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapTradeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSeasonMapTradeCommand) Return(msg *cl.L2C_SeasonMapTrade, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapTrade, msg)
	return true
}

//nolint:funlen
func (c *C2LSeasonMapTradeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapTrade{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapTrade Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapTrade: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapTrade{
		Position: cmsg.Position,
		GoodsId:  cmsg.GoodsId,
		Count:    cmsg.Count,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	if cmsg.Count == 0 {
		l4g.Errorf("user:%d C2L_SeasonMapTrade: cmsg.Count = 0 ", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	seasonMap := c.User.SeasonMap()
	var eventData *cl.SeasonMapEvent
	positionData := seasonMap.GetPositionData(cmsg.Position)
	if positionData == nil {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: no positionData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_POSITION_NOT_EXIST))
	}
	positionInfo := goxml.GetData().SeasonMapPositionInfoM.Index(cmsg.Position)
	if positionInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: no positionInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	eventInfo := positionInfo.GetEventInfo(cl.SeasonMapEventType_EventType_Trade)
	if eventInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: no eventInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	tradeInfo := goxml.GetData().SeasonMapTradeInfoM.Index(eventInfo.EventTypeId)
	if tradeInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: no tradeInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	tradeGoodsInfo := tradeInfo.GetTradeGoodsInfo(cmsg.GoodsId)
	if tradeGoodsInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: no tradeGoodsInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	eventData = seasonMap.GetEventData(positionData, cl.SeasonMapEventType_EventType_Trade)
	if eventData == nil || eventData.TradeEvent == nil {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: no eventData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_EVENT_NOT_EXIST))
	}
	goodsInfo := goxml.GetData().SeasonMapTradeGoodsInfoM.Index(cmsg.GoodsId)
	if goodsInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: cant find goodsInfo. goodsId %d", c.Msg.UID, cmsg.GoodsId)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	if eventData.TradeEvent.BuyGoods == nil {
		eventData.TradeEvent.BuyGoods = make(map[uint32]uint32)
	}
	hadBuyNum := eventData.TradeEvent.BuyGoods[cmsg.GoodsId]
	if hadBuyNum+cmsg.Count > tradeGoodsInfo.GoodsMaxNum {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: goods limited. goodsId %d", c.Msg.UID, cmsg.GoodsId)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_BUY_GOODS_NUM_LIMIT))
	}
	seasonMap.RecoverGoodsStamina(c.Srv)
	var costCurrency, costGoodsStamina uint32
	price := seasonMap.GetBuyGoodsPrice(cmsg.Position, goodsInfo)
	costCurrency = price * cmsg.Count
	costs := make([]*cl.Resource, 0)
	costs = append(costs, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.CurrencyTokenId, costCurrency))
	if tradeGoodsInfo.GoodsBuyAp != 0 {
		costAp := tradeGoodsInfo.GoodsBuyAp
		buyStaminaReduce := seasonMap.GetAltarAdd(character.SeasonMapBuffBuyStamina)
		if buyStaminaReduce >= goxml.BaseUInt32 {
			costAp = 0
		} else {
			costAp = costAp * (goxml.BaseUInt32 - buyStaminaReduce) / goxml.BaseUInt32
		}
		if costAp == 0 {
			costAp = 1
		}
		costGoodsStamina = cmsg.Count * costAp
		costs = append(costs, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN), goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, costGoodsStamina))
	}
	retCode, rewards := seasonMap.BuyGoodsCost(c.Srv, costs,
		[]*cl.Resource{goxml.GenSimpleResource(uint32(common.RESOURCE_SEASON_MAP_GOODS),
			cmsg.GoodsId, cmsg.Count)}, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_BUY_GOODS))
	if retCode != ret.RET_OK {
		l4g.Errorf("user: %d C2L_SeasonMapTrade: UseGoodsStamina error. retCode %d ", c.Msg.UID, retCode)
		return c.Return(smsg, uint32(retCode))
	}
	smsg.Rewards = rewards
	eventData.TradeEvent.BuyGoods[cmsg.GoodsId] += cmsg.Count
	c.User.FireCommonEvent(c.Srv.EventM(), event.AeSeasonMapBuyGoodsX, uint64(cmsg.Count))
	smsg.TradeEvent = eventData.TradeEvent.Clone()
	if costCurrency != 0 {
		c.User.FireCommonEvent(c.Srv.EventM(), event.AeSeasonMapBuyGoodsCurrencyX, uint64(costCurrency))
	}
	if costGoodsStamina != 0 {
		c.User.FireCommonEvent(c.Srv.EventM(), event.AeSeasonMapUseGoodsStaminaX, uint64(costGoodsStamina))
	}
	seasonMap.Save()
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapMasterCommand struct {
	base.UserCommand
}

func (c *C2LSeasonMapMasterCommand) Return(msg *cl.L2C_SeasonMapMaster, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapMaster, msg)
	return true
}

func (c *C2LSeasonMapMasterCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapMaster{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapMaster Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapMaster: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapMaster{
		Position: cmsg.Position,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapMaster: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}

	seasonMap := c.User.SeasonMap()
	positionData := seasonMap.GetPositionData(cmsg.Position)
	if positionData == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMaster: no positionData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_POSITION_NOT_EXIST))
	}
	positionInfo := goxml.GetData().SeasonMapPositionInfoM.Index(cmsg.Position)
	if positionInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMaster: no positionInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	eventInfo := positionInfo.GetEventInfo(cl.SeasonMapEventType_EventType_Master)
	if eventInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMaster: no eventInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	eventData := seasonMap.GetEventData(positionData, cl.SeasonMapEventType_EventType_Master)
	if eventData == nil || eventData.MasterEvent == nil || eventData.MasterEvent.TaskId == 0 {
		l4g.Errorf("user: %d C2L_SeasonMapMaster: no eventData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_EVENT_NOT_EXIST))
	}
	masterInfo := goxml.GetData().SeasonMapMasterInfoM.Index(eventInfo.EventTypeId)
	if masterInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMaster: no masterInfo. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	materTaskInfo := masterInfo.GetMaterTaskInfo(eventData.MasterEvent.TaskId)
	if materTaskInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapMaster: no materTaskInfo. position:%d task:%d", c.Msg.UID, cmsg.Position, eventData.MasterEvent.TaskId)
		return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
	}
	var costs []*cl.Resource
	for _, cost := range materTaskInfo.GoodsClRes {
		costs = append(costs, cost.Clone())
	}
	if len(costs) > 0 {
		retCode, costs := c.User.CheckResourcesSize(costs)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d C2L_SeasonMapMaster: CheckResourcesSize error. retCode %d ", c.Msg.UID, retCode)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		retCode = c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_MASTER), 0)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d C2L_SeasonMapMaster: Consume error. retCode %d ", c.Msg.UID, retCode)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
	}

	if materTaskInfo.TechId != 0 {
		talentTree := c.User.TalentTree()
		retCode, _ := talentTree.NodesLevelUp(c.Srv, []*cl.TalentTreeNode{{
			Id:          materTaskInfo.TechId,
			TargetLevel: materTaskInfo.TechLv,
		}}, true)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d C2L_SeasonMapMaster: NodesLevelUp error. retCode %d ", c.Msg.UID, retCode)
			return c.Return(smsg, retCode)
		}
	}

	c.User.FireCommonEvent(c.Srv.EventM(), event.AeSeasonMapFinishMasterTask, uint64(materTaskInfo.Id))
	c.User.FireCommonEvent(c.Srv.EventM(), event.AeSeasonMapFinishMasterTaskX, uint64(1))
	eventData.MasterEvent.TaskId = materTaskInfo.NextTaskId
	seasonMap.Save()
	smsg.MasterEvent = eventData.MasterEvent.Clone()

	positionLog := &l2c.SeasonMapSyncPositionLog{
		Position: cmsg.Position,
		LogType:  l2c.SeasonMapPositionLogType_Event1,
		EventLog: &cl.SeasonMapEventLog{
			Name:  c.User.Name(),
			Image: c.User.GetImage(),
			Event: cl.SeasonMapEventType_EventType_Master,
		},
	}
	seasonMap.Sync2Cross(c.Srv, positionLog)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapAltarCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSeasonMapAltarCommand) Return(msg *cl.L2C_SeasonMapAltar, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapAltar, msg)
	return true
}

func (c *C2LSeasonMapAltarCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapAltar{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapAltar Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapAltar: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapAltar{
		Position: cmsg.Position,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapAltar: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}

	seasonMap := c.User.SeasonMap()
	positionData := seasonMap.GetPositionData(cmsg.Position)
	if positionData == nil {
		l4g.Errorf("user: %d C2L_SeasonMapAltar: no positionData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_POSITION_NOT_EXIST))
	}
	eventData := seasonMap.GetEventData(positionData, cl.SeasonMapEventType_EventType_Altar)
	if eventData == nil || eventData.AltarEvent == nil {
		l4g.Errorf("user: %d C2L_SeasonMapAltar: no eventData. position:%d", c.Msg.UID, cmsg.Position)
		return c.Return(smsg, uint32(ret.RET_SEASON_MAP_EVENT_NOT_EXIST))
	}
	if cmsg.ChooseBuff == 0 {
		// 重置可选的buff
		ok, price := goxml.GetData().BuyPriceInfoM.GetPrice(goxml.GetData().SeasonMapConfigInfoM.ResetAltarBuffPrice, eventData.AltarEvent.ResetTimes, 1)
		if !ok || price == 0 {
			l4g.Errorf("C2L_SeasonMapAltar user:%d GetPrice failed. ", c.Msg.UID)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		costs := []*cl.Resource{goxml.GenSimpleResource(uint32(common.RESOURCE_DIAMOND), 0, price)}
		retCode := c.User.Consume(c.Srv, costs, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_ALTAR_RESET), 0)
		if retCode != uint32(ret.RET_OK) {
			l4g.Errorf("user: %d C2L_SeasonMapAltar: Consume error. retCode %d ", c.Msg.UID, retCode)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		eventData.AltarEvent.Buffs = seasonMap.AltarRandBuff(eventData.AltarEvent, cmsg.Position, c.Srv)
		eventData.AltarEvent.Choosed = false
		eventData.AltarEvent.ResetTimes++
	} else {
		if eventData.AltarEvent.Choosed {
			l4g.Errorf("user: %d C2L_SeasonMapAltar: had choosed.", c.Msg.UID)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
		if !util.InUint32s(eventData.AltarEvent.Buffs, cmsg.ChooseBuff) {
			l4g.Errorf("user: %d C2L_SeasonMapAltar: no buff. position:%d buff:%d", c.Msg.UID, cmsg.Position, cmsg.ChooseBuff)
			return c.Return(smsg, uint32(ret.RET_SEASON_MAP_EVENT_NOT_EXIST))
		}
		eventData.AltarEvent.ChooseBuff = cmsg.ChooseBuff
		eventData.AltarEvent.Choosed = true
	}
	seasonMap.Save()
	smsg.AltarEvent = eventData.AltarEvent.Clone()

	positionLog := &l2c.SeasonMapSyncPositionLog{
		Position: cmsg.Position,
		LogType:  l2c.SeasonMapPositionLogType_Event1,
		EventLog: &cl.SeasonMapEventLog{
			Name:  c.User.Name(),
			Image: c.User.GetImage(),
			Event: cl.SeasonMapEventType_EventType_Altar,
		},
	}

	seasonMap.Sync2Cross(c.Srv, positionLog)
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapPositionLogsCommand struct {
	base.UserCommand
}

func (c *C2LSeasonMapPositionLogsCommand) Return(msg *cl.L2C_SeasonMapPositionLogs, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapPositionLogs, msg)
	return true
}

func (c *C2LSeasonMapPositionLogsCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapPositionLogs{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapPositionLogs Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapPositionLogs: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapPositionLogs{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapPositionLogs: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}

	reqMsg := &l2c.L2CS_SeasonMapPositionLogs{
		Req: cmsg,
	}
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2CS_SeasonMapPositionLogs, c.Msg.UID, reqMsg) {
		l4g.Errorf("user: %d C2L_SeasonMapPositionLogs: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}
	return true
}

type C2LSeasonMapBuyStaminaCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSeasonMapBuyStaminaCommand) Return(msg *cl.L2C_SeasonMapBuyStamina, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapBuyStamina, msg)
	return true
}

func (c *C2LSeasonMapBuyStaminaCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapBuyStamina{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapBuyStamina Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapBuyStamina: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapBuyStamina{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	if cmsg.Count == 0 {
		l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: buy count error, buyCount: %d", c.Msg.UID, cmsg.Count)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	seasonMap := c.User.SeasonMap()
	seasonMap.RecoverStamina(c.Srv)

	var numSummary *cl.NumInfo
	var costs, rewards []*cl.Resource
	switch cmsg.BuyType {
	case cl.SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_Diamond:
		switch cmsg.StaminaType {
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina:
			smsg.Ret, costs, numSummary = c.User.CheckNumByType(goxml.GetData().SeasonMapConfigInfoM.BuyFightStaminaNumType, cmsg.Count)
			if smsg.Ret != uint32(ret.RET_OK) {
				l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: CheckNumByType err", c.Msg.UID)
				return c.Return(smsg, smsg.Ret)
			}
			rewards = append(rewards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
				goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, goxml.GetData().SeasonMapConfigInfoM.BuyFightStaminaRecoverNum*cmsg.Count))
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina:
			smsg.Ret, costs, numSummary = c.User.CheckNumByType(goxml.GetData().SeasonMapConfigInfoM.BuyGoodsStaminaNumType, cmsg.Count)
			if smsg.Ret != uint32(ret.RET_OK) {
				l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: CheckNumByType err", c.Msg.UID)
				return c.Return(smsg, smsg.Ret)
			}
			rewards = append(rewards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
				goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, goxml.GetData().SeasonMapConfigInfoM.BuyGoodsStaminaRecoverNum*cmsg.Count))
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_None, cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency:
			l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: invalid StaminaType %d", c.Msg.UID, cmsg.StaminaType)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		default:
			l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: invalid StaminaType %d", c.Msg.UID, cmsg.StaminaType)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
	case cl.SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_Item:
		switch cmsg.StaminaType {
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina:
			cost := goxml.GetData().SeasonMapConfigInfoM.FightStaminaItem.Clone()
			cost.Count = cost.Count * cmsg.Count
			costs = append(costs, cost)
			rewards = append(rewards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
				goxml.GetData().SeasonMapConfigInfoM.FightStaminaTokenId, goxml.GetData().SeasonMapConfigInfoM.FightStaminaItemRecoverNum*cmsg.Count))
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina:
			cost := goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaItem.Clone()
			cost.Count = cost.Count * cmsg.Count
			costs = append(costs, cost)
			rewards = append(rewards, goxml.GenSimpleResource(uint32(common.RESOURCE_TOKEN),
				goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaTokenId, goxml.GetData().SeasonMapConfigInfoM.GoodsStaminaItemRecoverNum*cmsg.Count))
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_None, cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency:
			l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: invalid StaminaType %d", c.Msg.UID, cmsg.StaminaType)
			return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
		}
	case cl.SeasonMapBuyStaminaType_SeasonMapBuyStaminaType_None:
		l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: invalid buyType %d", c.Msg.UID, cmsg.BuyType)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	default:
		l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: invalid buyType %d", c.Msg.UID, cmsg.BuyType)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}

	smsg.Ret, _ = c.User.Trade(c.Srv, costs, rewards, uint32(log.RESOURCE_CHANGE_REASON_SEASON_MAP_BUY_STAMINA), 0)
	if smsg.Ret != uint32(ret.RET_OK) {
		l4g.Errorf("user: %d C2L_SeasonMapBuyStamina: trade error, ret: %d", c.Msg.UID, smsg.Ret)
		return c.Return(smsg, smsg.Ret)
	}

	if numSummary != nil {
		switch cmsg.StaminaType {
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_Fight_Stamina:
			c.User.AddNumByType(goxml.GetData().SeasonMapConfigInfoM.BuyFightStaminaNumType, numSummary)
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_Goods_Stamina:
			c.User.AddNumByType(goxml.GetData().SeasonMapConfigInfoM.BuyGoodsStaminaNumType, numSummary)
		case cl.SeasonMapRecoverType_SeasonMapRecoverType_None, cl.SeasonMapRecoverType_SeasonMapRecoverType_Currency:
			break
		}
	}
	return c.Return(smsg, uint32(ret.RET_OK))
}

type C2LSeasonMapGetRankListCommand struct {
	base.UserCommand
}

func (c *C2LSeasonMapGetRankListCommand) Return(msg *cl.L2C_SeasonMapGetRankList, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapGetRankList, msg)
	return true
}

func (c *C2LSeasonMapGetRankListCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapGetRankList{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapGetRankList Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapGetRankList: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapGetRankList{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapGetRankList: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	rankInfo := goxml.GetData().RankingInfoM.Index(cmsg.RankId)
	if rankInfo == nil {
		l4g.Errorf("user: %d C2L_SeasonMapGetRankList: rankInfo not exist. rankID:%d", c.Msg.UID, cmsg.RankId)
		return c.Return(smsg, uint32(ret.RET_CLIENT_REQUEST_ERROR))
	}
	endRank := rankInfo.ShowCount
	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_RankGetList, c.Msg.UID, &l2c.L2C_RankGetList{
		RankId:    cmsg.RankId,
		ResetTime: uint64(goxml.GetSeasonLastBeginResetTime(time.Now().Unix())),
		SelfId:    c.User.ID(),
		BeginRank: 1,
		EndRank:   endRank,
	}) {
		l4g.Errorf("user: %d C2L_GetSeasonRankList: cross maintain", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_CROSS_MAINTAIN))
	}

	return true
}

type C2LSeasonMapPassPositionCommand struct {
	base.LimitedCommand
}

func (c *C2LSeasonMapPassPositionCommand) Return(msg *cl.L2C_SeasonMapPassPosition, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SeasonMapPassPosition, msg)
	return true
}

func (c *C2LSeasonMapPassPositionCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SeasonMapPassPosition{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SeasonMapPassPosition Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SeasonMapPassPosition: cmsg:%s", c.Msg.UID, cmsg)

	smsg := &cl.L2C_SeasonMapPassPosition{}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEASON_MAP), c.Srv) {
		l4g.Errorf("user: %d C2L_SeasonMapPassPosition: function not open", c.Msg.UID)
		return c.Return(smsg, uint32(ret.RET_FUNCTION_NOT_OPEN))
	}
	seasonmap := c.User.SeasonMap()
	seasonmap.SeasonInit(c.Srv)
	for _, position := range cmsg.Positions {
		positionInfo := goxml.GetData().SeasonMapPositionInfoM.Index(position)
		if positionInfo == nil {
			l4g.Errorf("user: %d CheckInit: no positionInfo. position:%d", c.Msg.UID, position)
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		positionData := seasonmap.GetPositionData(position)
		if positionData == nil {
			positionData = &cl.SeasonMapPosition{}
			seasonmap.GetData().PositionData[position] = positionData
		}
		if len(positionInfo.Monsters) == 0 {
			continue
		}
		monster := positionInfo.Monsters[0]
		monsterData := seasonmap.GetMonsterData(positionData, monster)
		if monsterData != nil {
			continue
		}
		monsterData = &cl.SeasonMapMonster{
			MonsterId: monster,
		}
		seasonmap.AddMonsterData(positionData, monsterData)
		monsterHpInfo := goxml.GetData().SeasonMapMonsterHpInfoM.GetRecordById(monster)
		if monsterHpInfo == nil {
			l4g.Errorf("user: %d CheckInit: no monsterHpInfo. monsterId:%d", c.Msg.UID, monster)
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		monsterData.ReduceHp = monsterHpInfo.MaxHp
		monsterInfo := goxml.GetData().SeasonMapMonsterInfoM.Index(monster)
		if monsterInfo == nil {
			l4g.Errorf("user: %d CheckInit: no monsterInfo. monsterId:%d", c.Msg.UID, monster)
			return c.Return(smsg, uint32(ret.RET_SYSTEM_DATA_ERROR))
		}
		seasonmap.OnMonsterDead(position, positionData, monsterInfo, c.Srv)
	}

	return c.Return(smsg, uint32(ret.RET_OK))
}
