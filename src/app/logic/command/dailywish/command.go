package dailywish

import (
	"app/gmxml"
	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_DailyWishGet), &C2LDailyWishGetCommand{}, state)       // 获取每日许愿数据 NOLOG
	cmds.Register(uint32(cl.ID_MSG_C2L_DailyWishSummon), &C2LDailyWishSummonCommand{}, state) // 抽取每日许愿奖励
	cmds.Register(uint32(cl.ID_MSG_C2L_DailyWishXmlGet), &C2LDailyWishXmlGetCommand{}, state) // 获取每日活动XML信息
}

type C2LDailyWishGetCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LDailyWishGetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDailyWishGetCommand) Error(msg *cl.L2C_DailyWishGet, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailyWishGet, msg)
	return false
}

func (c *C2LDailyWishGetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DailyWishGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DailyWishGet Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_DailyWishGet: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_DailyWishGet{
		Ret: uint32(cret.RET_OK),
		Id:  cmsg.Id,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DAILY_WISH), c.Srv) {
		l4g.Errorf("user: %d C2L_DailyWishGet: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := c.User.Now(c.Srv)

	actInfo, isNew := gmxml.DailyWishActivityInfoM.Index(cmsg.Id, now)
	if actInfo == nil {
		l4g.Errorf("user: %d DailyWishActivityInfoM: get activity:%d failed", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if !actInfo.IsOpen(now) {
		l4g.Errorf("user: %d DailyWishActivityInfoM: act id:%d not open", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_DAILY_WISH_ACTIVITY_NOT_OPEN))
	}

	//玩家不充钱没触发造个假数据
	dailyWish := c.User.DailyWishM().Get(smsg.Id, isNew)
	if dailyWish != nil {
		smsg.DailyWish = dailyWish.Clone()
	} else {
		smsg.DailyWish = &cl.DailyWish{
			Id: cmsg.Id,
		}
	}

	smsg.DailyRechargeAmount = c.User.DailyInfo().RechargeAmount
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailyWishGet, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LDailyWishSummonCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LDailyWishSummonCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDailyWishSummonCommand) Error(msg *cl.L2C_DailyWishSummon, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailyWishSummon, msg)
	return false
}

func (c *C2LDailyWishSummonCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DailyWishSummon{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DailyWishSummon Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_DailyWishSummon: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_DailyWishSummon{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DAILY_WISH), c.Srv) {
		l4g.Errorf("user: %d C2L_DailyWishSummon: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	var dailyWish *character.DailyWish
	var isNew bool
	now := c.User.Now(c.Srv)
	dailyWishM := c.User.DailyWishM()
	isNew = goxml.GetData().DailyWishOpenInfoM.InProtect(now[int(common.TIME_TYPE_SERVER_OPEN)])
	dailyWish = dailyWishM.Get(cmsg.Id, isNew)
	if dailyWish == nil {
		l4g.Errorf("user: %d C2L_DailyWishSummon: get daily wish failed actId:%d not open", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_DAILY_WISH_ACTIVITY_NOT_OPEN))
	}

	summonC := dailyWish.GetSummonCount()
	if summonC < 1 {
		l4g.Errorf("user: %d C2L_DailyWishSummon: summon count:%d not enough", c.Msg.UID, summonC)
		return c.Error(smsg, uint32(cret.RET_DAILY_WISH_SUMMON_COUNT_NOT_ENOUGH))
	}

	roundCount := uint32(len(dailyWish.TakeAward) + 1)

	awardId := goxml.GetData().DailyWishWeightM.Summon(c.Srv.Rand(), dailyWish.TakeAward, roundCount)
	if awardId == 0 {
		l4g.Errorf("user: %d C2L_DailyWishSummon: rand awardId:%d failed roundCount:%d take award:%+v",
			c.Msg.UID, awardId, roundCount, dailyWish.TakeAward)
		return c.Error(smsg, uint32(cret.RET_DAILY_WISH_ACTIVITY_RAND_AWARD_ID))
	}

	awardInfo := gmxml.DailyWishAwardInfoM.Award(cmsg.Id, awardId, dailyWish.IsNew)
	if awardInfo == nil {
		l4g.Errorf("user: %d C2L_DailyWishSummon: get daily wish actId:%d awardId:%d failed ",
			c.Msg.UID, cmsg.Id, awardId)
		return c.Error(smsg, uint32(cret.RET_DAILY_WISH_AWARD_ID_NOT_EXIST))
	}
	smsg.Ret, smsg.Award = c.User.Award(c.Srv, awardInfo.Awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_DAILY_WISH_SUMMON), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DailyWishSummon: award failed. awards:%+v",
			c.User.ID(), smsg.Award)
		return c.Error(smsg, smsg.Ret)
	}
	dailyWish.AfterSummonAward(awardInfo.Id)
	dailyWishM.Save()

	smsg.DailyWish = dailyWish.Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailyWishSummon, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LDailyWishXmlGetCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LDailyWishXmlGetCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LDailyWishXmlGetCommand) Error(msg *cl.L2C_DailyWishXmlGet, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailyWishXmlGet, msg)
	return false
}

func (c *C2LDailyWishXmlGetCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_DailyWishXmlGet{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_DailyWishXmlGet Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_DailyWishXmlGet: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_DailyWishXmlGet{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DAILY_WISH), c.Srv) {
		l4g.Errorf("user: %d C2L_DailyWishXmlGet: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	infos := make([]*cl.DailyWishActivityInfo, 0, gmxml.DailyWishActivityInfoM.DataLen())
	cLens := len(cmsg.Ids)
	now := c.User.Now(c.Srv)
	if cLens == 0 {
		datas := gmxml.DailyWishActivityInfoM.GetDatas(now)
		for _, activityInfo := range datas {
			if activityInfo == nil {
				continue
			}
			clientInfo, ret := buildClientMsg(activityInfo.Id, now)
			if ret != uint32(cret.RET_OK) {
				continue
			}
			infos = append(infos, clientInfo)
		}
	} else {
		dLens := gmxml.DailyWishActivityInfoM.DataLen()
		if uint32(cLens) > dLens {
			l4g.Errorf("user:%d C2L_DailyWishXmlGet client lens:%d is more than data lens:%d", c.Msg.UID, cLens, dLens)
			return c.Error(smsg, uint32(cret.RET_PARAM_LENGTH_LIMIT))
		}

		for _, id := range cmsg.Ids {
			clientInfo, ret := buildClientMsg(id, now)
			if ret != uint32(cret.RET_OK) {
				l4g.Errorf("user:%d C2L_DailyWishXmlGet build client msg failed actId:%d", c.Msg.UID, id)
				return c.Error(smsg, ret)
			}
			infos = append(infos, clientInfo)
		}
	}
	smsg.Infos = infos
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DailyWishXmlGet, smsg)
	return c.ResultOK(smsg.Ret)
}

func buildClientMsg(actId uint32, now []uint32) (*cl.DailyWishActivityInfo, uint32) {
	activityInfo, isNew := gmxml.DailyWishActivityInfoM.Index(actId, now)
	if activityInfo == nil {
		l4g.Errorf("buildClientMsg get actId:%d info failed", actId)
		return nil, uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	if activityInfo.IsTimeExpire(now) {
		return nil, uint32(cret.RET_DAILY_WISH_ACTIVITY_TIME_EXPIRE)
	}
	awardInfos := gmxml.DailyWishAwardInfoM.ActGroup(actId, isNew)
	if awardInfos == nil {
		l4g.Errorf("buildClientMsg get actId:%d award group info failed", actId)
		return nil, uint32(cret.RET_CLIENT_REQUEST_ERROR)
	}
	tmp := &cl.DailyWishActivityInfo{}
	activityInfo.Flush2Client(tmp)
	tmp.AwardInfo = make([]*cl.DailyWishAwardInfo, 0, len(awardInfos))
	for _, award := range awardInfos {
		tmp.AwardInfo = append(tmp.AwardInfo, award.Flush2Client())
	}

	return tmp, uint32(cret.RET_OK)
}
