package sevendaylogin

import (
	"app/goxml"
	"app/logic/character"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_SevenDayLoginData), &C2LSevenDayLoginDataCommand{}, state)           // 请求数据
	cmds.Register(uint32(cl.ID_MSG_C2L_SevenDayLoginTakeAward), &C2LSevenDayLoginTakeAwardCommand{}, state) // 领取奖励
}

type C2LSevenDayLoginDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSevenDayLoginDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSevenDayLoginDataCommand) Error(msg *cl.L2C_SevenDayLoginData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SevenDayLoginData, msg)
	return false
}

func (c *C2LSevenDayLoginDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SevenDayLoginData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SevenDayLoginTakeAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	c.User.SevenDayLogin().SendDataToClient()
	return true
}

type C2LSevenDayLoginTakeAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LSevenDayLoginTakeAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LSevenDayLoginTakeAwardCommand) Error(msg *cl.L2C_SevenDayLoginTakeAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SevenDayLoginTakeAward, msg)
	return false
}

func (c *C2LSevenDayLoginTakeAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_SevenDayLoginTakeAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_SevenDayLoginTakeAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_SevenDayLoginTakeAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_SevenDayLoginTakeAward{
		Id:  cmsg.Id,
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_SEVENDAY_LOGIN), c.Srv) {
		l4g.Errorf("user: %d C2L_SevenDayLoginTakeAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	cfgInfo := goxml.GetData().SevenDayLoginM.Index(cmsg.Id)
	if cfgInfo == nil {
		l4g.Errorf("user: %d C2L_SevenDayLoginTakeAward: config not found, id:%d", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	if ret := c.User.SevenDayLogin().CheckTakeAward(cmsg.Id); ret != cret.RET_OK {
		l4g.Errorf("user: %d C2L_SevenDayLoginTakeAward: CanTakeAward err, errCode:%d", c.Msg.UID, ret)
		return c.Error(smsg, uint32(ret))
	}
	smsg.RewardStatus = c.User.SevenDayLogin().SetAwardToken(cmsg.Id)
	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, cfgInfo.RewardClRes, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_SEVENDAY_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_SevenDayLoginTakeAward: send award failed. retCode: %d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_SevenDayLoginTakeAward, smsg)
	c.User.LogSevenDayLoginTakeAward(c.Srv, cmsg.Id, c.User.SevenDayLogin().GetData())
	return c.ResultOK(smsg.Ret)
}
