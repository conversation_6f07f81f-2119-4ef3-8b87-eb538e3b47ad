package pokemon

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/command/base"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	"context"

	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_PokemonGetData), &C2LPokemonGetDataCommand{}, state)                   // 获取宠物数据
	cmds.Register(uint32(cl.ID_MSG_C2L_PokemonActivate), &C2LPokemonActivateCommand{}, state)                 // 宠物激活
	cmds.Register(uint32(cl.ID_MSG_C2L_PokemonSetBall), &C2LPokemonSetBallCommand{}, state)                   // 设置兽栏
	cmds.Register(uint32(cl.ID_MSG_C2L_PokemonStarUp), &C2LPokemonStarUpCommand{}, state)                     // 宠物升星
	cmds.Register(uint32(cl.ID_MSG_C2L_PokemonPotentialLevelUp), &C2LPokemonPotentialLevelUpCommand{}, state) // 宠物潜能升级
	cmds.Register(uint32(cl.ID_MSG_C2L_PokemonMasterReward), &C2LPokemonMasterRewardCommand{}, state)         // 宠物大师等级领奖
	cmds.Register(uint32(cl.ID_MSG_C2L_PokemonSetShow), &C2LPokemonSetShowCommand{}, state)                   // 宠物设置展示
}

type C2LPokemonGetDataCommand struct {
	base.UserCommand
}

func (c *C2LPokemonGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPokemonGetDataCommand) Error(msg *cl.L2C_PokemonGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonGetData, msg)
	return false
}

func (c *C2LPokemonGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PokemonGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PokemonGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PokemonGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PokemonGetData{
		Ret: uint32(cret.RET_OK),
	}

	pokemonM := c.User.PokemonManager()
	smsg.Pokemons = pokemonM.FlushAllPokemons()
	smsg.Balls = pokemonM.FlushBalls()
	smsg.MasterExp = pokemonM.GetMasterExp()
	smsg.MasterLevel = pokemonM.CalcMasterLevel()
	smsg.RewardedMasterLevels = pokemonM.GetRewardedMasterLevels()
	smsg.PotentialLevel = c.User.GetPokemonPotentialLevel()
	smsg.ShowPokemonId = c.User.GetPokemonShowImage()

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LPokemonActivateCommand struct {
	base.UserCommand
}

func (c *C2LPokemonActivateCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPokemonActivateCommand) Error(msg *cl.L2C_PokemonActivate, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonActivate, msg)
	return false
}

func (c *C2LPokemonActivateCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PokemonActivate{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PokemonActivate Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PokemonActivate: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PokemonActivate{
		Ret:   uint32(cret.RET_OK),
		SysId: cmsg.SysId,
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_POKEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_PokemonActivate: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	pokemonM := c.User.PokemonManager()

	// 检查是否已经激活
	pokemon := pokemonM.GetPokemon(cmsg.SysId)
	if pokemon != nil {
		l4g.Errorf("user: %d C2L_PokemonActivate: pokemon already active. pokemonId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_POKEMON_ACTIVATED))
	}

	pokemonInfo := goxml.GetData().PokemonInfoM.GetRecordByPokemonId(cmsg.SysId)
	if pokemonInfo == nil {
		l4g.Errorf("user: %d C2L_PokemonActivate: pokemonInfo is nil. pokemonId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	pokemonStarInfo := goxml.GetData().PokemonStarInfoM.GetRecordByMouldIdStar(pokemonInfo.MouldId, goxml.PokemonInitialStar)
	if pokemonStarInfo == nil {
		l4g.Errorf("user: %d C2L_PokemonActivate: pokemonStarInfo is nil. mouldId:%d, star:%d", c.Msg.UID, pokemonInfo.MouldId, goxml.PokemonInitialStar)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 激活消耗
	activateCosts := pokemonInfo.ActivateCosts
	// 宠物奖励
	pokemonRewards := goxml.GenSimpleResource(uint32(common.RESOURCE_POKEMON), cmsg.SysId, 1)

	retCode, _ := c.User.Trade(c.Srv, activateCosts, []*cl.Resource{pokemonRewards}, uint32(log.RESOURCE_CHANGE_REASON_POKEMON_ACTIVATE), 0)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PokemonActivate: trade failed. ret:%d", c.Msg.UID, retCode)
		return c.Error(smsg, retCode)
	}

	// 获取激活后的宠物
	pokemon = pokemonM.GetPokemon(cmsg.SysId)
	if pokemon == nil {
		l4g.Errorf("user: %d C2L_PokemonActivate: get pokemon failed. pokemonId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_POKEMON_ACTIVATE_FAILED))
	}
	pokemonM.UpdateAttrAndPower(c.Srv)

	smsg.Pokemon = pokemon.GetData().Clone()
	smsg.MasterExp = pokemonM.GetMasterExp()
	smsg.MasterLevel = pokemonM.CalcMasterLevel()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonActivate, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LPokemonSetBallCommand struct {
	base.UserCommand
}

func (c *C2LPokemonSetBallCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPokemonSetBallCommand) Error(msg *cl.L2C_PokemonSetBall, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonSetBall, msg)
	return false
}

func (c *C2LPokemonSetBallCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PokemonSetBall{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PokemonSetBall Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PokemonSetBall: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PokemonSetBall{
		Ret:         uint32(cret.RET_OK),
		PokemonBall: cmsg.PokemonBall,
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_POKEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_PokemonSetBall: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	// 检查兽栏配置
	retCode := c.User.PokemonManager().CheckBall(cmsg.PokemonBall)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PokemonSetBall: check ball error. retCode:%d", c.Msg.UID, retCode)
		return c.Error(smsg, retCode)
	}

	c.User.PokemonManager().SetBall(cmsg.PokemonBall)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonSetBall, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LPokemonStarUpCommand struct {
	base.UserCommand
}

func (c *C2LPokemonStarUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPokemonStarUpCommand) Error(msg *cl.L2C_PokemonStarUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonStarUp, msg)
	return false
}

func (c *C2LPokemonStarUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PokemonStarUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PokemonStarUp Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PokemonStarUp: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PokemonStarUp{
		Ret:   uint32(cret.RET_OK),
		SysId: cmsg.SysId,
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_POKEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_PokemonStarUp: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	pokemonM := c.User.PokemonManager()

	// 检查宠物是否存在
	pokemon := pokemonM.GetPokemon(cmsg.SysId)
	if pokemon == nil {
		l4g.Errorf("user: %d C2L_PokemonStarUp: pokemon not exist. pokemonId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_POKEMON_NOT_EXIST))
	}

	pokemonInfo := goxml.GetData().PokemonInfoM.GetRecordByPokemonId(cmsg.SysId)
	if pokemonInfo == nil {
		l4g.Errorf("user: %d C2L_PokemonStarUp: pokemonInfo is nil. pokemonId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	star := pokemon.GetStar()
	starInfo := goxml.GetData().PokemonStarInfoM.GetRecordByMouldIdStar(pokemonInfo.MouldId, star)
	if starInfo == nil {
		l4g.Errorf("user: %d C2L_PokemonStarUp: pokemonStarInfo is nil. mouldId:%d, star:%d", c.Msg.UID, pokemonInfo.MouldId, star)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	nextStar := star + 1
	nextStarInfo := goxml.GetData().PokemonStarInfoM.GetRecordByMouldIdStar(pokemonInfo.MouldId, nextStar)
	if nextStarInfo == nil {
		l4g.Errorf("user: %d C2L_PokemonStarUp: next pokemonStarInfo is nil. mouldId:%d, star:%d", c.Msg.UID, pokemonInfo.MouldId, nextStar)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	starUpCosts := []*cl.Resource{goxml.GenSimpleResource(uint32(common.RESOURCE_POKEMON_FRAGMENT), pokemonInfo.FragmentValue, starInfo.FragmentCount)}
	retCode := c.User.Consume(c.Srv, starUpCosts, uint32(log.RESOURCE_CHANGE_REASON_POKEMON_STAR_UP), 0)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PokemonStarUp: consume failed. retCode:%d", c.Msg.UID, retCode)
		return c.Error(smsg, retCode)
	}

	pokemon.SetStar(nextStar)
	pokemonM.SetChange(pokemon.GetSysId())
	pokemonM.AddMasterExp(nextStarInfo.MasterExp)
	pokemonM.UpdateAttrAndPower(c.Srv)

	c.User.LogPokemonStarUp(c.Srv, cmsg.SysId, star, nextStar)

	smsg.Pokemon = pokemon.GetData().Clone()
	smsg.MasterExp = pokemonM.GetMasterExp()
	smsg.MasterLevel = pokemonM.CalcMasterLevel()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonStarUp, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LPokemonPotentialLevelUpCommand struct {
	base.UserCommand
}

func (c *C2LPokemonPotentialLevelUpCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPokemonPotentialLevelUpCommand) Error(msg *cl.L2C_PokemonPotentialLevelUp, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonPotentialLevelUp, msg)
	return false
}

func (c *C2LPokemonPotentialLevelUpCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PokemonPotentialLevelUp{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PokemonPotentialLevelUp Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PokemonPotentialLevelUp: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PokemonPotentialLevelUp{
		Ret: uint32(cret.RET_OK),
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_POKEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_PokemonPotentialLevelUp: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	pokemonM := c.User.PokemonManager()

	potentialLevel := c.User.GetPokemonPotentialLevel()
	info := goxml.GetData().PokemonPotentialInfoM.GetRecordByLevel(potentialLevel)
	if info == nil {
		l4g.Errorf("user: %d C2L_PokemonPotentialLevelUp: info is nil. potentialLevel:%d", c.Msg.UID, potentialLevel)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	nextLevel := potentialLevel + 1
	nextLevelInfo := goxml.GetData().PokemonPotentialInfoM.GetRecordByLevel(nextLevel)
	if nextLevelInfo == nil {
		l4g.Errorf("user: %d C2L_PokemonPotentialLevelUp: nextLevelInfo is nil. potentialLevel:%d", c.Msg.UID, potentialLevel)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	retCode := c.User.Consume(c.Srv, info.LevelUpCosts, uint32(log.RESOURCE_CHANGE_REASON_POKEMON_POTENTIAL_LEVEL_UP), 0)
	if retCode != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PokemonPotentialLevelUp: consume failed. retCode:%d", c.Msg.UID, retCode)
		return c.Error(smsg, retCode)
	}

	c.User.SetPokemonPotentialLevel(nextLevel)
	smsg.Level = c.User.GetPokemonPotentialLevel()

	pokemonM.UpdateAttrAndPower(c.Srv)

	c.User.LogPokemonPotentialLevelUp(c.Srv, potentialLevel, nextLevel)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonPotentialLevelUp, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LPokemonMasterRewardCommand struct {
	base.UserCommand
}

func (c *C2LPokemonMasterRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPokemonMasterRewardCommand) Error(msg *cl.L2C_PokemonMasterReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonMasterReward, msg)
	return false
}

func (c *C2LPokemonMasterRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PokemonMasterReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PokemonMasterReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PokemonMasterReward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PokemonMasterReward{
		Ret: uint32(cret.RET_OK),
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_POKEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_PokemonMasterReward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	pokemonM := c.User.PokemonManager()

	masterRewards := make([]*cl.Resource, 0)
	for _, masterLevel := range cmsg.MasterLevels {
		// 检查是否已经领取
		if pokemonM.IsMasterLevelReward(masterLevel) {
			l4g.Errorf("user: %d C2L_PokemonMasterReward: already received. level:%d", c.Msg.UID, masterLevel)
			return c.Error(smsg, uint32(cret.RET_REPEATED_RECEIVE_AWARD))
		}
		// 检查是否满足领取条件
		maxMasterLevel := pokemonM.CalcMasterLevel()
		if maxMasterLevel < masterLevel {
			l4g.Errorf("user: %d C2L_PokemonMasterReward: maxMasterlevel %d < masterLevel %d", c.Msg.UID, maxMasterLevel, masterLevel)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		// 获取奖励
		info := goxml.GetData().PokemonMasterInfoM.GetRecordByMasterLevel(masterLevel)
		if info == nil {
			l4g.Errorf("user: %d C2L_PokemonMasterReward: info is nil. level:%d", c.Msg.UID, masterLevel)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		masterRewards = append(masterRewards, info.MasterRewards...)
	}

	smsg.Ret, smsg.Rewards = c.User.Award(c.Srv, masterRewards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_POKEMON_MASTER_REWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_PokemonMasterReward: award failed. ret:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	for _, matserLevel := range cmsg.MasterLevels {
		pokemonM.SetMasterLevelReward(matserLevel)
	}
	pokemonM.SaveGlobal()

	smsg.RewardedMasterLevels = pokemonM.GetRewardedMasterLevels()

	c.User.LogPokemonMasterReward(c.Srv, cmsg.MasterLevels, smsg.Rewards)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonMasterReward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LPokemonSetShowCommand struct {
	base.UserCommand
}

func (c *C2LPokemonSetShowCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LPokemonSetShowCommand) Error(msg *cl.L2C_PokemonSetShow, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonSetShow, msg)
	return false
}

func (c *C2LPokemonSetShowCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_PokemonSetShow{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_PokemonSetShow Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_PokemonSetShow: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_PokemonSetShow{
		Ret:   uint32(cret.RET_OK),
		SysId: cmsg.SysId,
	}

	// 检查功能是否开启
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_POKEMON), c.Srv) {
		l4g.Errorf("user: %d C2L_PokemonSetShow: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	pokemonM := c.User.PokemonManager()

	if pokemonInfo := goxml.GetData().PokemonInfoM.GetRecordByPokemonId(cmsg.SysId); pokemonInfo == nil {
		l4g.Errorf("user: %d C2L_PokemonSetShow: pokemonInfo is nil. sysId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	// 检查是否已经展示
	if c.User.GetPokemonShowImage() == cmsg.SysId {
		l4g.Errorf("user: %d C2L_PokemonSetShow: already show. sysId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	// 检查是否已经激活
	if pokemon := pokemonM.GetPokemon(cmsg.SysId); pokemon == nil {
		l4g.Errorf("user: %d C2L_PokemonSetShow: not activated. sysId:%d", c.Msg.UID, cmsg.SysId)
		return c.Error(smsg, uint32(cret.RET_POKEMON_NOT_EXIST))
	}

	c.User.SetPokemonShowImage(cmsg.SysId)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_PokemonSetShow, smsg)
	return c.ResultOK(smsg.Ret)
}
