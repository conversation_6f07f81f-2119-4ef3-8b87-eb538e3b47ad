package activitycompliance

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/rank"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"gitlab.qdream.com/kit/sea/time"

	"app/logic/command/base"
	"app/protos/out/cl"

	// "app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityComplianceGetData), &C2LActivityComplianceGetDataCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityComplianceRecvAward), &C2LActivityComplianceRecvAwardCommand{}, state)
	cmds.Register(uint32(cl.ID_MSG_C2L_ActivityComplianceLikeRank), &C2LActivityComplianceLikeCommand{}, state)
}

type C2LActivityComplianceGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityComplianceGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityComplianceGetDataCommand) Error(msg *cl.L2C_ActivityComplianceGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityComplianceGetData, msg)
	return false
}

func (c *C2LActivityComplianceGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityComplianceGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityComplianceGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityComplianceGetData: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityComplianceGetData{
		Ret: uint32(cret.RET_OK),
	}

	smsg.Score = c.User.ActivityCompliance().GetScore()
	smsg.Data = c.User.ActivityCompliance().Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityComplianceGetData, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LActivityComplianceRecvAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityComplianceRecvAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityComplianceRecvAwardCommand) Error(msg *cl.L2C_ActivityComplianceRecvAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityComplianceRecvAward, msg)
	return false
}

func (c *C2LActivityComplianceRecvAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityComplianceRecvAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityComplianceRecvAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityComplianceRecvAward: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityComplianceRecvAward{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_COMPLIANCE), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityComplianceRecvAwards: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}
	now := time.Now().Unix()
	if !goxml.GetData().ConfigInfoM.ActivityComplianceOpen(c.Srv.ServerOpenDay(now), now) {
		l4g.Errorf("user: %d C2L_ActivityComplianceRecvAwards not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	score := c.User.ActivityCompliance().GetScore()

	rewardInfo := goxml.GetData().ActivityComplianceInfoM.Index(cmsg.Id)
	if rewardInfo == nil {
		l4g.Errorf("user:%d C2L_ActivityComplianceRecvAwards ActivityComplianceInfoM index:%d not exist", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if c.User.ActivityCompliance().IsAward(cmsg.Id) {
		l4g.Errorf("user:%d C2L_ActivityComplianceRecvAwards awrad id:%d has recv", c.Msg.UID, cmsg.Id)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	if score < rewardInfo.Score {
		l4g.Errorf("user:%d C2L_ActivityComplianceRecvAwards score:%d recv score:%d not enought", c.Msg.UID, score, rewardInfo.Score)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, rewardInfo.ClRes, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_COMPLIANCE_RECV_AWARD), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivityComplianceRecvAwards award failed error code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.ActivityCompliance().SetAward(cmsg.Id)

	smsg.Id = cmsg.Id
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityComplianceRecvAward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LActivityComplianceLikeCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LActivityComplianceLikeCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LActivityComplianceLikeCommand) Error(msg *cl.L2C_ActivityComplianceLikeRank, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityComplianceLikeRank, msg)
	return false
}

func (c *C2LActivityComplianceLikeCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_ActivityComplianceLikeRank{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_ActivityComplianceLikeRank Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_ActivityComplianceLikeRank: cmsg:%s", c.Msg.UID, cmsg)
	// edit here
	smsg := &cl.L2C_ActivityComplianceLikeRank{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ACTIVITY_COMPLIANCE), c.Srv) {
		l4g.Errorf("user: %d C2L_ActivityComplianceLikeRank: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	now := time.Now().Unix()
	if !goxml.GetData().ConfigInfoM.ActivityComplianceOpen(c.Srv.ServerOpenDay(now), now) {
		l4g.Errorf("user: %d C2L_ActivityComplianceLikeRank not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	addNum := uint32(1)
	retn, _, numSummary := c.User.CheckNumByType(uint32(common.PURCHASEID_ACTIVITY_COMPLIANCE_RANK_LIKE_COUNT), addNum, c.Srv)
	if retn != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_ActivityComplianceLikeRank: checkNum failed, retn:%d", c.Msg.UID, retn)
		return c.Error(smsg, retn)
	}

	if c.User.ActivityCompliance().DailyIsLike(cmsg.Uid) {
		l4g.Errorf("user: %d C2L_ActivityComplianceLikeRank: daily was liked", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	rankElement := c.Srv.CommonRankM().GetElement(goxml.ActivityComplianceID, cmsg.Uid)
	if rankElement == nil {
		l4g.Errorf("user:%d C2L_ActivityComplianceLikeRank like user:%d not exist in rank", c.Msg.UID, cmsg.Uid)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	smsg.LikeCount = rankElement.(rank.CommonRankValuer).Like()
	c.Srv.CommonRankM().SetChange(goxml.ActivityComplianceID, rankElement.(rank.CommonRankValuer).Key(), rankElement)

	award := goxml.GetData().ConfigInfoM.GetActivityComplianceLikeDiamondResource()
	var totalAward []*cl.Resource
	totalAward = append(totalAward, award)

	smsg.Ret, smsg.Awards = c.User.Award(c.Srv, totalAward, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_ACTIVITY_COMPLIANCE_LIKE), 0)
	if smsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_ActivityComplianceLikeRank award failed error code:%d", c.Msg.UID, smsg.Ret)
		return c.Error(smsg, smsg.Ret)
	}

	c.User.AddNumByType(uint32(common.PURCHASEID_ACTIVITY_COMPLIANCE_RANK_LIKE_COUNT), numSummary)
	smsg.DailyLikeUsers = c.User.ActivityCompliance().FlushDailyUser()
	smsg.Uid = cmsg.Uid
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_ActivityComplianceLikeRank, smsg)
	return c.ResultOK(smsg.Ret)
}
