package operateactivity

import (
	"app/gmxml"
	"app/goxml"
	"app/logic/character"
	"app/protos/in/log"
	"app/protos/out/common"
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"

	//"app/protos/out/common"
	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_OperateActivityGetData), &C2LOperateActivityGetDataCommand{}, state)                               //  获取配置活动数据 = 15701;
	cmds.Register(uint32(cl.ID_MSG_C2L_OperateActivityGetXML), &C2LOperateActivityGetXMLCommand{}, state)                                 //  获取配置活动表数据 = 15703;
	cmds.Register(uint32(cl.ID_MSG_C2L_OperateActivityInitActivity), &C2LOperateActivityInitActivityCommand{}, state)                     //  初始化礼包活动 = 15707;
	cmds.Register(uint32(cl.ID_MSG_C2L_OperateActivityGetTaskReward), &C2LOperateActivityGetTaskRewardCommand{}, state)                   //  获取任务奖励 = 15909;
	cmds.Register(uint32(cl.ID_MSG_C2L_OperateActivityPromotionSelectAward), &C2LOperateActivityPromotionSelectAwardCommand{}, state)     //  新推送礼包选择奖励物品
	cmds.Register(uint32(cl.ID_MSG_C2L_OperateActivityPromotionRechargeCheck), &C2LOperateActivityPromotionRechargeCheckCommand{}, state) // 新推送礼包充值检查
}

type C2LOperateActivityGetDataCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LOperateActivityGetDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LOperateActivityGetDataCommand) Error(msg *cl.L2C_OperateActivityGetData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityGetData, msg)
	return false
}

func (c *C2LOperateActivityGetDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OperateActivityGetData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OperateActivityGetData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_OperateActivityGetData: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_OperateActivityGetData{
		Ret: uint32(cret.RET_OK),
	}

	om := c.User.OperateActivityM()
	now := c.User.Now(c.Srv)
	actIds := om.DelExpiredActivity(c.Srv, now)
	smsg.Activities = om.Flush(actIds, c.Srv)

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityGetData, smsg)

	return c.ResultOK(smsg.Ret)
}

type C2LOperateActivityGetXMLCommand struct {
	base.UserCommand
}

func (c *C2LOperateActivityGetXMLCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LOperateActivityGetXMLCommand) Error(msg *cl.L2C_OperateActivityGetXML, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityGetXML, msg)
	return false
}

func (c *C2LOperateActivityGetXMLCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OperateActivityGetXML{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OperateActivityGetXML Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_OperateActivityGetXML: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_OperateActivityGetXML{
		Ret: uint32(cret.RET_OK),
	}

	actsLen := len(cmsg.ActIds)

	if actsLen > gmxml.OperateActivityInfoM.DataLen() {
		l4g.Errorf("user: %d C2L_OperateActivityGetXML: reqActIds:%d is more than OperateActivityMaxLen :%d",
			c.Msg.UID, actsLen, gmxml.OperateActivityInfoM.DataLen())
		return c.Error(smsg, uint32(cret.RET_ERROR))
	}

	config := &cl.OperateActivityConfig{}
	if actsLen == 0 {
		now := c.User.Now(c.Srv)
		var expired map[uint32]struct{}
		config.Infos, expired = gmxml.OperateActivityInfoM.Flush2Client(now)
		config.Gifts = gmxml.OperateGiftInfoM.Flush2Client(expired)
		config.Tasks = gmxml.OperateTaskInfoM.Flush2Client(expired)
		config.PromotionGifts = gmxml.PromotionGiftInfoM.Flush2Client(expired)
	} else {
		checkRepeat := make(map[uint32]struct{}, actsLen)
		for _, actID := range cmsg.ActIds {
			_, exist := checkRepeat[actID]
			if exist {
				l4g.Errorf("user: %d C2L_OperateActivityGetXML: act ID: %d is repeat",
					c.Msg.UID, actID)
				return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
			}
			checkRepeat[actID] = struct{}{}
			activityInfo := gmxml.OperateActivityInfoM.Index(actID)
			if activityInfo == nil {
				l4g.Errorf("user: %d C2L_OperateActivityGetXML: act ID: %d is not exist in OperateActivity",
					c.Msg.UID, actID)
				return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
			}
			config.Infos = append(config.Infos, activityInfo.Flush())
			switch activityInfo.Type {
			case uint32(common.OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_GIFT):
				for _, Info := range gmxml.OperateGiftInfoM.Group(actID) {
					config.Gifts = append(config.Gifts, Info.Flush())
				}
			case uint32(common.OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_TASK):
				for _, Info := range gmxml.OperateTaskInfoM.Group(actID) {
					config.Tasks = append(config.Tasks, Info.Flush())
				}
			case uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT), uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_DISCOUNT_GIFT),
				uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_CHAIN_GIFT):
				for _, info := range gmxml.PromotionGiftInfoM.Group(actID) {
					config.PromotionGifts = append(config.PromotionGifts, info.Flush())
				}
			default:
				l4g.Errorf("user: %d C2L_OperateActivityGetXML: act ID: %d is activityInfo type:%d is error",
					c.Msg.UID, actID, activityInfo.Type)
				return c.Error(smsg, uint32(cret.RET_ERROR))
			}
		}
	}

	smsg.Config = config
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityGetXML, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LOperateActivityInitActivityCommand struct {
	base.UserCommand
}

func (c *C2LOperateActivityInitActivityCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LOperateActivityInitActivityCommand) Error(msg *cl.L2C_OperateActivityInitActivity, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityInitActivity, msg)
	return false
}

//nolint:funlen
func (c *C2LOperateActivityInitActivityCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OperateActivityInitActivity{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OperateActivityInitActivity Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_OperateActivityInitActivity: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_OperateActivityInitActivity{
		Ret: uint32(cret.RET_OK),
	}

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_OPERATE_ACTIVITY), c.Srv) {
		l4g.Errorf("user: %d C2L_OperateActivityInitActivity: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	actsLen := gmxml.OperateActivityInfoM.DataLen()
	if actsLen == 0 || len(cmsg.ActIds) > actsLen {
		l4g.Errorf("user: %d C2L_OperateActivityInitActivity: reqACtIds:%d is more than OperateActivityMaxLen :%d",
			c.Msg.UID, len(cmsg.ActIds), actsLen)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	om := c.User.OperateActivityM()
	amount := c.User.GetRecharge()
	now := time.Now().Unix()
	userNow := c.User.Now(c.Srv)
	checkRepeat := make(map[uint32]struct{}, len(cmsg.ActIds))
	operateActivity := make([]*cl.OperateActivity, 0, len(cmsg.ActIds))
	initActivities := make([]uint32, 0, len(cmsg.ActIds))
	for _, actID := range cmsg.ActIds {
		_, exist := checkRepeat[actID]
		if exist {
			l4g.Errorf("user: %d C2L_OperateActivityInitActivity: act ID: %d is repeat",
				c.Msg.UID, actID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}

		activityInfo := gmxml.OperateActivityInfoM.Index(actID)
		if activityInfo == nil {
			l4g.Errorf("user: %d C2L_OperateActivityInitActivity: act ID: %d is not exist in OperateActivity",
				c.Msg.UID, actID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if !c.checkActivityType(activityInfo.Type) {
			l4g.Errorf("user: %d C2L_OperateActivityInitActivity: act ID: %d is not a gift type", c.Msg.UID, actID)
			return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_INIT_TYPE_ERROR))
		}

		if !om.IsOpening(c.Srv, activityInfo, userNow) {
			l4g.Errorf("user: %d C2L_OperateActivityInitActivity: act ID:%d is not open", c.Msg.UID, actID)
			return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_IS_NOT_OPENING))
		}

		activity := om.GetActivity(actID)
		if activity == nil {
			initActivities = append(initActivities, actID)
		} else {
			operateActivity = append(operateActivity, activity.Clone())
		}

		checkRepeat[actID] = struct{}{}
	}

	for _, actID := range initActivities {
		activity := om.InitActivity(actID)
		activityInfo := gmxml.OperateActivityInfoM.Index(actID)
		if activityInfo == nil {
			l4g.Errorf("user: %d C2L_OperateActivityInitActivity: act ID: %d is not exist in OperateActivity",
				c.Msg.UID, actID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}
		switch activityInfo.Type {
		case uint32(common.OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_GIFT):
			activity.InitGift(amount)
			c.User.LogOperateActivityGiftInit(c.Srv, actID, amount)
			for _, giftInfo := range gmxml.OperateGiftInfoM.Group(actID) {
				if giftInfo.AmountMax == 0 && giftInfo.AmountMin <= amount {
					activity.AddGift(giftInfo, now)
					continue
				}
				if giftInfo.AmountMin <= amount && amount < giftInfo.AmountMax {
					activity.AddGift(giftInfo, now)
					continue
				}
				if giftInfo.AmountMin == 0 && giftInfo.AmountMax == 0 {
					activity.AddGift(giftInfo, now)
					continue
				}
			}
		case uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_DISCOUNT_GIFT), uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT),
			uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_CHAIN_GIFT):
			newGift := activity.InitPromotionGift(c.Srv, c.User)
			c.User.LogPromotionGiftInit(c.Srv, actID, newGift)
		}

		operateActivity = append(operateActivity, activity.Clone())
		om.SetChange(activity)
	}

	smsg.ActIds = cmsg.ActIds
	smsg.Activities = operateActivity

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityInitActivity, smsg)

	return c.ResultOK(smsg.Ret)
}

func (c *C2LOperateActivityInitActivityCommand) checkActivityType(actType uint32) bool {
	if actType == uint32(common.OPERATE_ACTIVITY_TYPE_OPERATE_ACTIVITY_TYPE_GIFT) { // 直充礼包
		return true
	}
	if actType == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_DISCOUNT_GIFT) || actType == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT) ||
		actType == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_CHAIN_GIFT) { // 新推送礼包
		return true
	}
	return false
}

type C2LOperateActivityGetTaskRewardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LOperateActivityGetTaskRewardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LOperateActivityGetTaskRewardCommand) Error(msg *cl.L2C_OperateActivityGetTaskReward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityGetTaskReward, msg)
	return false
}

//nolint:funlen
func (c *C2LOperateActivityGetTaskRewardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OperateActivityGetTaskReward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OperateActivityGetTaskReward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_OperateActivityGetTaskReward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_OperateActivityGetTaskReward{
		Ret: uint32(cret.RET_OK),
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_ROUND_ACTIVITY), c.Srv) {
		l4g.Errorf("user: %d C2L_OperateActivityGetTaskReward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	activityInfo := gmxml.OperateActivityInfoM.Index(cmsg.ActId)
	if activityInfo == nil {
		l4g.Errorf("user: %d C2L_OperateActivityInitActivity: act ID: %d is not exist in OperateActivity",
			c.Msg.UID, cmsg.ActId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	now := c.User.Now(c.Srv)

	om := c.User.OperateActivityM()
	if !om.IsOpening(c.Srv, activityInfo, now) {
		l4g.Errorf("user: %d C2L_OperateActivityGetTaskReward: ActID:%d is not open", c.Msg.UID, cmsg.ActId)
		return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_IS_NOT_OPENING))
	}

	if activityInfo.IsNoviceProtection(c.Srv.ServerDay(int64(now[int(common.TIME_TYPE_NORMAL_DATE)]))) {
		l4g.Errorf("user: %d C2L_OperateActivityGetTaskReward: ActID:%d is in novice protection", c.Msg.UID, cmsg.ActId)
		return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_TASK_IN_NOVICE_PROTECTION))
	}

	taskIdsLen := len(cmsg.TaskIds)
	tasks := gmxml.OperateTaskInfoM.Group(cmsg.ActId)
	if taskIdsLen == 0 || taskIdsLen > len(tasks) {
		l4g.Errorf("user: %d C2L_OperateActivityGetTaskReward: taskIDs is more than activity id:%d tasks", c.Msg.UID, len(tasks))
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	var des uint32
	if len(tasks) > 0 {
		des = tasks[0].DescribeType
	}

	var init bool
	activity := om.GetActivity(cmsg.ActId)
	if activity == nil {
		if des == gmxml.OperateTaskEventRecordFromServiceOpen {
			activity = om.InitTempTaskActivity(cmsg.ActId)
			init = true
		} else {
			l4g.Errorf("user: %d C2L_OperateActivityGetTaskReward: act ID :%d is not init", c.Msg.UID, cmsg.ActId)
			return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_NOT_INIT))
		}
	}
	checkRepeat := make(map[uint32]struct{}, taskIdsLen)
	rewards := make([]*cl.Resource, 0, taskIdsLen*4)
	logTasks := make([]*log.OperateActivityTask, 0, taskIdsLen)
	for _, ID := range cmsg.TaskIds {
		_, exist := checkRepeat[ID]
		if exist {
			l4g.Errorf("user: %d C2L_OperateActivityGetTaskReward: taskId:%d is repeat", c.Msg.UID, ID)
			return c.Error(smsg, uint32(cret.RET_REPEATED_PARAM))
		}
		checkRepeat[ID] = struct{}{}

		operateTaskInfo := gmxml.OperateTaskInfoM.Index(ID)
		if operateTaskInfo == nil {
			l4g.Errorf("user:%d C2L_OperateActivityGetTaskReward: task not exist. taskId:%d", c.Msg.UID, ID)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if !gmxml.OperateTaskInfoM.CheckPreRoundFinish(operateTaskInfo.ActId, operateTaskInfo.Round, activity.GetReward()) {
			l4g.Errorf("user:%d C2L_OperateActivityGetTaskReward: taskId:%d last round not finish", c.Msg.UID, ID)
			return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_LAST_ROUND_NOT_FINISH))
		}

		taskTypeInfo := goxml.GetData().TaskTypeInfoM.Index(operateTaskInfo.Event)
		if taskTypeInfo == nil {
			l4g.Errorf("user:%d C2L_OperateActivityGetTaskReward: taskType not exist. Event:%d", c.Msg.UID, operateTaskInfo.Event)
			return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
		}

		if activity.IsReward(ID) {
			l4g.Errorf("user: %d C2L_OperateActivityGetTaskReward taskId:%d is reward", c.Msg.UID, ID)
			return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_TASK_AWARD_REPEAT_RECEIVE))
		}

		var canReceive bool
		var tmpLog *log.OperateActivityTask
		if operateTaskInfo.DescribeType == gmxml.OperateTaskEventRecordFromServiceOpen {
			progress := c.User.Achieve().GetOneTypeProgress(taskTypeInfo)
			if progress != nil {
				canReceive = c.User.CheckTaskFinish(progress, progress.TaskTypeId, uint64(operateTaskInfo.Progress))
				if canReceive {
					logTasks = append(logTasks, &log.OperateActivityTask{
						Progress: uint32(progress.Progress),
						TaskType: taskTypeInfo.Id,
					})
				}
			}
		} else if operateTaskInfo.DescribeType == gmxml.OperateTaskEventRecordFromActivityOpen {
			canReceive, tmpLog = om.GetOneTypeProgress(taskTypeInfo, operateTaskInfo, cmsg.ActId)
			if canReceive {
				logTasks = append(logTasks, tmpLog)
			}
		}

		if !canReceive {
			l4g.Errorf("user:%d C2L_CarnivalReceiveAward: not finish. id:%d ",
				c.Msg.UID, ID)
			return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_TASK_AWARD_REPEAT_RECEIVE))
		}

		rewards = append(rewards, operateTaskInfo.Flush().Awards...)
	}

	ret, mergeAwards := c.User.Award(c.Srv, rewards, character.AwardTagMail,
		uint32(log.RESOURCE_CHANGE_REASON_OPERATION_ACTIVITY_TASK), 0)
	if ret != uint32(cret.RET_OK) {
		l4g.Errorf("user:%d C2L_OperateActivityGetTaskReward: award is error code:%d", c.User.ID(), ret)
		return c.Error(smsg, ret)
	}

	if init {
		om.SetActivity(activity)
	}
	om.SetTaskAward(c.Srv, cmsg.ActId, cmsg.TaskIds)
	om.SetChange(activity)

	smsg.ActId = cmsg.ActId
	smsg.TaskIds = cmsg.TaskIds
	smsg.Reward = mergeAwards
	smsg.Task = activity.Task

	c.User.LogOperateActivityTaskReceived(c.Srv, cmsg.ActId, cmsg.TaskIds, logTasks)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityGetTaskReward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LOperateActivityPromotionSelectAwardCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LOperateActivityPromotionSelectAwardCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LOperateActivityPromotionSelectAwardCommand) Error(msg *cl.L2C_OperateActivityPromotionSelectAward, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityPromotionSelectAward, msg)
	return false
}

func (c *C2LOperateActivityPromotionSelectAwardCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OperateActivityPromotionSelectAward{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OperateActivityPromotionSelectAward Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_OperateActivityPromotionSelectAward: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_OperateActivityPromotionSelectAward{
		Ret:         uint32(cret.RET_OK),
		GiftId:      cmsg.GiftId,
		AwardIndex:  cmsg.AwardIndex,
		TargetIndex: cmsg.TargetIndex,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_PROMOTION), c.Srv) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	giftInfo := gmxml.PromotionGiftInfoM.Index(cmsg.GiftId)
	if giftInfo == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: giftID: %d is not exist in PromotionGiftInfoM",
			c.Msg.UID, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	activityInfo := gmxml.OperateActivityInfoM.Index(giftInfo.ActId)
	if activityInfo == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: actID: %d is not exist in OperateActivity",
			c.Msg.UID, giftInfo.ActId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	now := c.User.Now(c.Srv)

	om := c.User.OperateActivityM()
	if !om.IsOpening(c.Srv, activityInfo, now) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: actID:%d is not open", c.Msg.UID, giftInfo.ActId)
		return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_IS_NOT_OPENING))
	}

	if activityInfo.IsNoviceProtection(c.Srv.ServerDay(int64(now[int(common.TIME_TYPE_NORMAL_DATE)]))) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: giftID:%d is in novice protection", c.Msg.UID, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_TASK_IN_NOVICE_PROTECTION))
	}

	activity := om.GetActivity(giftInfo.ActId)
	if activity == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: act:%d is not exist", c.Msg.UID, giftInfo.ActId)
		return c.Error(smsg, uint32(cret.RET_SERVER_ERROR))
	}

	promotionGift := activity.PromotionGift.Goods[cmsg.GiftId]
	if promotionGift == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: goods not exist. giftId:%d ", c.Msg.UID, giftInfo.Id)
		return c.Error(smsg, uint32(cret.RET_SERVER_ERROR))
	}
	if cmsg.AwardIndex+1 > uint32(len(giftInfo.OptionalAwards)) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: req awardIndex error.giftId:%d awardIndex:%d",
			c.Msg.UID, giftInfo.Id, cmsg.AwardIndex)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cmsg.TargetIndex+1 > uint32(len(giftInfo.OptionalAwards[cmsg.AwardIndex].Ress)) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionSelectAward: req awardIndex error.giftId:%d targetIndex:%d",
			c.Msg.UID, giftInfo.Id, cmsg.TargetIndex)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	promotionGift.TargetAwards[cmsg.AwardIndex] = int32(cmsg.TargetIndex)

	om.SetChange(activity)
	c.User.LogPromotionGiftSelectAward(c.Srv, giftInfo.ActId, cmsg.GiftId, cmsg.AwardIndex, cmsg.TargetIndex)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityPromotionSelectAward, smsg)
	return c.ResultOK(smsg.Ret)
}

type C2LOperateActivityPromotionRechargeCheckCommand struct {
	base.TimelyResetUserCommand
}

func (c *C2LOperateActivityPromotionRechargeCheckCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LOperateActivityPromotionRechargeCheckCommand) Error(msg *cl.L2C_OperateActivityPromotionRechargeCheck, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityPromotionRechargeCheck, msg)
	return false
}

func (c *C2LOperateActivityPromotionRechargeCheckCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_OperateActivityPromotionRechargeCheck{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_OperateActivityPromotionRechargeCheck Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_OperateActivityPromotionRechargeCheck: cmsg:%s", c.Msg.UID, cmsg)
	//edit here
	smsg := &cl.L2C_OperateActivityPromotionRechargeCheck{
		Ret:    uint32(cret.RET_OK),
		GiftId: cmsg.GiftId,
	}
	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_PROMOTION), c.Srv) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: function not open", c.Msg.UID)
		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	giftInfo := gmxml.PromotionGiftInfoM.Index(cmsg.GiftId)
	if giftInfo == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: giftID: %d is not exist in PromotionGiftInfoM",
			c.Msg.UID, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	activityInfo := gmxml.OperateActivityInfoM.Index(giftInfo.ActId)
	if activityInfo == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: actID: %d is not exist in OperateActivity",
			c.Msg.UID, giftInfo.ActId)
		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	now := c.User.Now(c.Srv)
	om := c.User.OperateActivityM()
	if !om.IsOpening(c.Srv, activityInfo, now) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: ActID:%d is not open", c.Msg.UID, giftInfo.ActId)
		return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_IS_NOT_OPENING))
	}

	if activityInfo.IsNoviceProtection(c.Srv.ServerDay(int64(now[int(common.TIME_TYPE_NORMAL_DATE)]))) {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: giftID:%d is in novice protection", c.Msg.UID, cmsg.GiftId)
		return c.Error(smsg, uint32(cret.RET_OPERATE_ACTIVITY_TASK_IN_NOVICE_PROTECTION))
	}

	retCode := om.PromotionRechargeCheck(activityInfo, giftInfo)
	if retCode != cret.RET_OK {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: rechargeCheck error. giftId:%d retCode:%d",
			c.Msg.UID, cmsg.GiftId, retCode)
		return c.Error(smsg, uint32(retCode))
	}

	act := om.GetActivity(giftInfo.ActId)
	if act == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: activity %d is not init", c.User.ID(), giftInfo.ActId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	gift := act.PromotionGift
	if gift == nil {
		l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: activity :%d gift is not init",
			c.User.ID(), giftInfo.ActId)
		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if giftInfo.Free == 1 { // 免费礼包直接发奖
		goods, exist := gift.Goods[giftInfo.Id]
		if !exist {
			gift.Goods[giftInfo.Id] = &cl.GoodsState{Id: giftInfo.Id, Time: time.Now().Unix()}
			goods = gift.Goods[giftInfo.Id]
		}
		count := goods.Count
		if count+1 > giftInfo.Number {
			l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: gift num not enough. count %d gift number %d",
				c.User.ID(), count, giftInfo.Number)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}
		awards := giftInfo.Awards()
		if activityInfo.Type == uint32(common.OPERATE_ACTIVITY_TYPE_PROMOTION_OPTIONAL_GIFT) {
			awards = om.GetUserOptionalAwards(giftInfo, goods.GetTargetAwards())
		}
		if len(awards) == 0 {
			l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: awards config error. gift Id %d", c.User.ID(), giftInfo.Id)
			return false
		}

		smsg.Ret, smsg.Awards = c.User.Award(c.Srv, awards, character.AwardTagMail, uint32(log.RESOURCE_CHANGE_REASON_PROMOTION_FREE_GIFT), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_OperateActivityPromotionRechargeCheck: awards is error, error code :%d", c.User.ID(), smsg.Ret)
			return c.Error(smsg, uint32(cret.RET_SERVER_ERROR))
		}
		goods.Count++
		om.SetChange(act)
	}

	smsg.PromotionGift = gift.Clone()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_OperateActivityPromotionRechargeCheck, smsg)
	return c.ResultOK(smsg.Ret)
}
