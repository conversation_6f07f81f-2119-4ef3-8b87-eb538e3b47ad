package disorderland

import (
	"app/goxml"
	"app/logic/character"
	"app/logic/event"
	"app/protos/in/l2c"
	"app/protos/in/log"
	"app/protos/out/cl"
	"app/protos/out/common"
	cret "app/protos/out/ret"
	"context"

	l4g "github.com/ivanabc/log4go"
)

func (c *C2LDisorderLandGetDataCommand) Execute(_ context.Context) bool {
	flag, _, sMsg := c.ParseProto()
	if !flag {
		return false
	}

	disorder := c.User.DisorderLand()
	disorder.Init(c.Srv)
	// 必须在 Init 执行后调用
	disorder.StaminaRecover(c.Srv)
	disorder.HandleGuildMedalAdd(c.Srv)
	sMsg.Disorderland = disorder.Flush()
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DisorderlandGetData, sMsg)

	return c.ResultOK(sMsg.Ret)
}

func (c *C2LDisorderLandTriggerEventCommand) Execute(_ context.Context) bool {
	flag, cMsg, sMsg := c.ParseProto()
	if !flag {
		return false
	}
	sMsg.Difficulty = cMsg.Difficulty
	sMsg.FightType = cMsg.FightType
	sMsg.NodeId = cMsg.NodeId

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DISORDER_LAND), c.Srv) {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: function not open", c.Msg.UID)
		return c.Error(sMsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	disorder := c.User.DisorderLand()

	dInfo := goxml.GetData().DisorderlandInfoM.Index(cMsg.Difficulty)
	if dInfo == nil {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: difficulty not exist, difficulty:%d", c.Msg.UID, cMsg.Difficulty)
		return c.Error(sMsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	mapInfo := goxml.GetData().DisorderlandMapInfoM.Index(dInfo.MapId, cMsg.NodeId)
	if mapInfo == nil {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: node not exist, mapId:%d, nodeId:%d", c.Msg.UID, dInfo.MapId, cMsg.NodeId)
		return c.Error(sMsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}

	mapM := disorder.GetMapM(cMsg.Difficulty)
	if mapM == nil {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: mapM not exist, difficulty:%d", c.Msg.UID, cMsg.Difficulty)
		return c.Error(sMsg, uint32(cret.RET_ERROR))
	}

	// 非战斗事件类型，不能重复完成
	if !c.isBattleEvent(mapInfo.EventType) && mapM.IsNodeComplete(cMsg.NodeId) {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: node already complete, difficulty:%d, nodeId:%d", c.Msg.UID, cMsg.Difficulty, cMsg.NodeId)
		return c.Error(sMsg, uint32(cret.RET_DISORDER_LAND_NODE_ALREADY_COMPLETE))
	}
	// 难度解锁检查
	if sMsg.Ret = c.isUnlockDifficulty(cMsg.Difficulty, dInfo); sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: difficulty not open, difficulty:%d", c.Msg.UID, cMsg.Difficulty)
		return c.Error(sMsg, sMsg.Ret)
	}
	// 关联节点检查
	if sMsg.Ret = c.isUnlockNode(mapM, mapInfo); sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: node not open, nodeID:%d", c.Msg.UID, cMsg.NodeId)
		return c.Error(sMsg, sMsg.Ret)
	}

	// 获取事件处理器，进行事件处理
	handle := character.GetEventHandler(mapInfo.EventType)
	if handle == nil {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: handler not exist, eventType:%d", c.Msg.UID, mapInfo.EventType)
		return c.Error(sMsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	}
	if !handle(disorder, mapM, c.Srv, sMsg, mapInfo, cMsg.ClientData) {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: exec event failed. mapID:%d, nodeID:%d", c.Msg.UID, mapInfo.MapId, mapInfo.Id)
		return c.Error(sMsg, sMsg.Ret)
	}
	disorder.Save()

	c.User.FireCommonEvent(c.Srv.EventM(), event.AeDisorderLandFinishToX,
		uint64(disorder.GetDegreeOfNodeFinish(cMsg.Difficulty)), cMsg.Difficulty)
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DisorderlandTriggerEvent, sMsg)
	c.User.LogDisorderLandTriggerEvent(c.Srv, cMsg.Difficulty, mapInfo.MapId, cMsg.NodeId, mapInfo.EventId, sMsg.Node)

	return c.ResultOK(sMsg.Ret)
}

func (c *C2LDisorderLandTriggerEventCommand) isUnlockDifficulty(difficulty uint32, dInfo *goxml.DisorderlandInfoExt) uint32 {
	if difficulty == uint32(common.DISORDER_LAND_LEVEL_DLL_NORMAL) {
		return uint32(cret.RET_OK)
	}

	return c.checkUnlock(dInfo)
}

// 是否解锁影响因素：1. 赛季等级 2. 前置难度是否通关
func (c *C2LDisorderLandTriggerEventCommand) checkUnlock(dInfo *goxml.DisorderlandInfoExt) uint32 {
	disorder := c.User.DisorderLand()
	// 赛季相关检查
	if ret := c.checkSeason(dInfo); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: check failed for season. ret:%d", c.Msg.UID, ret)
		return ret
	}
	// 丰碑相关
	if ret := c.checkSeasonLink(dInfo); ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: check failed for seasonLink. ret:%d", c.Msg.UID, ret)
		return ret
	}
	// 前置难度是否通关？
	preDifficulty := dInfo.DifficultyUnlock
	disorderInfo := goxml.GetData().DisorderlandInfoM.Index(preDifficulty)
	if disorderInfo == nil {
		l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: pre difficulty not exist, difficulty:%d", c.Msg.UID, preDifficulty)
		return uint32(cret.RET_SYSTEM_DATA_ERROR)
	}

	mamM := disorder.GetMapM(preDifficulty)
	// 任意一个节点完成，即算通关
	if len(disorderInfo.UnlockPoints) > 0 {
		isPass := false
		for _, id := range disorderInfo.UnlockPoints {
			if mamM.IsNodeComplete(id) {
				isPass = true
				break
			}
		}

		if !isPass {
			l4g.Errorf("user: %d C2L_DisorderLandTriggerEvent: pre difficulty not pass, difficulty:%d", c.Msg.UID, preDifficulty)
			return uint32(cret.RET_DISORDER_LAND_PRE_DIFFICULTY_NOT_PASS)
		}
	}

	return uint32(cret.RET_OK)
}

func (c *C2LDisorderLandTriggerEventCommand) checkSeason(dInfo *goxml.DisorderlandInfoExt) uint32 {
	disorder := c.User.DisorderLand()
	// 赛季天数
	if disorder.GetSeasonDayFromSeason() < dInfo.SeasonDay {
		return uint32(cret.RET_DISORDER_LAND_SEASON_DAY_NOT_ENOUGH)
	}

	return uint32(cret.RET_OK)
}

func (c *C2LDisorderLandTriggerEventCommand) checkSeasonLink(dInfo *goxml.DisorderlandInfoExt) uint32 {
	disorder := c.User.DisorderLand()
	if disorder.GetSeasonLinkMonumentNumWithLevel(dInfo.MonumentUnlock) < dInfo.NumUnlock {
		return uint32(cret.RET_DISORDER_LAND_SEASON_LINK_NUM_NOT_ENOUGH)
	}

	return uint32(cret.RET_OK)
}

func (c *C2LDisorderLandTriggerEventCommand) isUnlockNode(m *character.DisorderLandMap, mapInfo *goxml.DisorderlandMapInfoExt) uint32 {
	switch mapInfo.UnlockType {
	case uint32(common.DISORDER_LAND_NODE_UNLOCK_DLNU_UNLOCK):
		return uint32(cret.RET_OK)
	case uint32(common.DISORDER_LAND_NODE_UNLOCK_DLNU_ANY):
		return c.anyNodeUnlock(m, mapInfo)
	case uint32(common.DISORDER_LAND_NODE_UNLOCK_DLNU_ALL):
		return c.allNodeUnlock(m, mapInfo)
	}

	return uint32(cret.RET_SYSTEM_DATA_ERROR)
}

// 任意一个关联节点完成，即可解锁
func (c *C2LDisorderLandTriggerEventCommand) anyNodeUnlock(m *character.DisorderLandMap, mapInfo *goxml.DisorderlandMapInfoExt) uint32 {
	for _, id := range mapInfo.Points {
		if m.IsNodeComplete(id) {
			return uint32(cret.RET_OK)
		}
	}

	return uint32(cret.RET_DISORDER_LAND_NEIGHBOR_NODE_NOT_COMPLETE)
}

// 所有关联节点都完成，即可解锁
func (c *C2LDisorderLandTriggerEventCommand) allNodeUnlock(m *character.DisorderLandMap, mapInfo *goxml.DisorderlandMapInfoExt) uint32 {
	for _, id := range mapInfo.Points {
		if !m.IsNodeComplete(id) {
			return uint32(cret.RET_DISORDER_LAND_NEIGHBOR_NODE_NOT_COMPLETE)
		}
	}

	return uint32(cret.RET_OK)
}

func (c *C2LDisorderLandTriggerEventCommand) isBattleEvent(eventType uint32) bool {
	return eventType == uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_BATTLE)
}

func (c *C2LDisorderLandRankCommand) Execute(_ context.Context) bool {
	flag, cMsg, sMsg := c.ParseProto()
	if !flag {
		return false
	}
	sMsg.HurdleType = cMsg.HurdleType
	sMsg.RankId = cMsg.RankId

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DISORDER_LAND), c.Srv) {
		l4g.Errorf("user: %d C2L_DisorderLandRank: function not open", c.Msg.UID)
		return c.Error(sMsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	if cMsg.HurdleType == 0 || cMsg.HurdleType >= uint32(common.DISORDER_LAND_HURDLE_TYPE_DLHT_MAX) {
		l4g.Errorf("user: %d C2L_DisorderLandRank: hurdle type is invalid. type:%d", c.Msg.UID, cMsg.HurdleType)
		return c.Error(sMsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}
	disorder := c.User.DisorderLand()
	msg := &l2c.L2C_DisorderLandRank{
		SeasonId:   disorder.GetSeasonIDFromSeason(),
		HurdleType: cMsg.HurdleType,
		RankId:     cMsg.RankId,
	}

	if !c.Srv.SendCmdToCross(l2c.ID_MSG_L2C_DisorderLandRank, c.Msg.UID, msg) {
		l4g.Errorf("user: %d C2L_DisorderLandRank: cross maintain", c.Msg.UID)
		return c.Error(sMsg, uint32(cret.RET_CROSS_MAINTAIN))
	}

	return c.ResultOK(sMsg.Ret)
}

func (c *C2LDisorderLandBuyStaminaCommand) Execute(_ context.Context) bool {
	flag, cMsg, sMsg := c.ParseProto()
	if !flag {
		return false
	}
	sMsg.BuyCount = cMsg.BuyCount
	sMsg.BuyType = cMsg.BuyType

	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_DISORDER_LAND), c.Srv) {
		l4g.Errorf("user: %d C2L_DisorderLandBuyStamina: function not open", c.Msg.UID)
		return c.Error(sMsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	}

	disorder := c.User.DisorderLand()
	disorder.StaminaRecover(c.Srv)

	// buyCount：代表组的概念：一组，二组...，消耗和获得配置在量表里
	if cMsg.BuyCount == 0 {
		l4g.Errorf("user: %d C2L_DisorderLandBuyStamina: buy count error, buyCount: %d", c.Msg.UID, cMsg.BuyCount)
		return c.Error(sMsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	if cMsg.BuyType != uint32(common.DISORDER_LAND_BUY_TYPE_DLBT_DIAMOND) && cMsg.BuyType != uint32(common.DISORDER_LAND_BUY_TYPE_DLBT_ITEM) {
		l4g.Errorf("user: %d C2L_DisorderLandBuyStamina: buy type error, buyType: %d", c.Msg.UID, cMsg.BuyType)
		return c.Error(sMsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	}

	costs, awards := goxml.GetData().DisorderlandConfigInfoM.CalcResWithBuy(cMsg.BuyType, cMsg.BuyCount)
	var numSummary *cl.NumInfo
	if cMsg.BuyType == uint32(common.DISORDER_LAND_BUY_TYPE_DLBT_DIAMOND) {
		sMsg.Ret, costs, numSummary = c.User.CheckNumByType(uint32(common.PURCHASEID_DISORDER_LAND_BUY_COUNT), cMsg.BuyCount, c.Srv)
		if sMsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_DisorderLandBuyStamina: receive worship award failed. count not enough", c.Msg.UID)
			return c.Error(sMsg, sMsg.Ret)
		}
	}

	sMsg.Ret, _ = c.User.Trade(c.Srv, costs, awards, uint32(log.RESOURCE_CHANGE_REASON_DISORDER_LAND_BUY_STAMINA), 0)
	if sMsg.Ret != uint32(cret.RET_OK) {
		l4g.Errorf("user: %d C2L_DisorderLandBuyStamina: trade error, ret: %d", c.Msg.UID, sMsg.Ret)
		return c.Error(sMsg, sMsg.Ret)
	}

	sMsg.StaminaRecoverTime = disorder.GetStaminaRecoverTime()
	if numSummary != nil {
		c.User.AddNumByType(uint32(common.PURCHASEID_DISORDER_LAND_BUY_COUNT), numSummary)
	}
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DisorderlandBuyStamina, sMsg)
	c.User.LogDisorderLandBuyStamina(c.Srv, cMsg.BuyType, cMsg.BuyCount, sMsg.StaminaRecoverTime)

	return c.ResultOK(sMsg.Ret)
}

func (c *C2LDisorderLandTestSweepCommand) Execute(_ context.Context) bool {
	flag, cMsg, sMsg := c.ParseProto()
	if !flag {
		return false
	}
	sMsg.DungeonId = cMsg.DungeonId
	dungeonInfo := goxml.GetData().DisorderlandDungeonInfoM.Index(cMsg.DungeonId)
	if dungeonInfo == nil {
		l4g.Errorf("user: %d TestSweep: dungeon is invalid. dungeonID: %d", c.Msg.UID, cMsg.DungeonId)
		sMsg.Ret = uint32(cret.RET_SYSTEM_DATA_ERROR)
		return c.Error(sMsg, sMsg.Ret)
	}

	for i := uint32(0); i < cMsg.SweepCount; i++ {
		if !character.TestSweep(c.Srv, c.User.DisorderLand(), sMsg, dungeonInfo) {
			return c.Error(sMsg, sMsg.Ret)
		}
	}

	c.User.SendCmdToGateway(cl.ID_MSG_L2C_DisorderlandTestSweep, sMsg)
	return c.ResultOK(sMsg.Ret)
}
