package command

import (
	"app/csession"
	"app/logic/activity"
	"app/logic/actm"
	"app/logic/character"
	"app/logic/db"
	"app/logic/etcdcache"
	"app/logic/mongo"
	"app/logic/oss"
	"app/logic/session"
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/skiplist"
)

type Servicer interface {
	character.Servicer

	//管理器
	RedisActor() *db.RedisActor
	Queue() *skiplist.Set
	GetActivity(activity.ID) activity.Activity
	GetActivityM(actm.ID) actm.Activity
	OSSM() *oss.OSSManager
	MongoM() *mongo.Manager

	//管理器
	//RedisActor() *db.RedisActor
	//Queue() *skiplist.Set
	//GateM() *gateway.Manager
	//GetActivity(activity.ID) activity.Activity
	//GetHTTPManager() *helper.HTTPManager
	//ReqTimeM() *request.TimeOutManager
	//ReportM() *report.Manager

	//方法
	SendCmdToClient(uint64, uint32, uint64, interface{})
	GetAsyncMessage(uint64) (interface{}, bool)
	DeleteAsyncMessage(uint64)
	CrossSessionChange(uint64, uint64, session.SessionChangeType)
	CrossMasterSessionChange(uint64, uint64, csession.SessionChangeType)

	//配置
	EnableCrypto() bool
	EnableLoginLimit() bool
	ServiceStatus() int32
	LoginCheck(ip, deviceId, uuid string) bool
	GetFunctionStatus(uint32) bool
	QuestionnaireFlush(*etcdcache.CheckQuestionnaireMsg, string) []*cl.Questionnaire
	GetGiftCodeAwards(uint32) []*cl.Resource
	GetLinkSummonId() uint32
	GetLinkSummonTimes() []*cl.LinkSummonTime
	ServerID() uint64
}
