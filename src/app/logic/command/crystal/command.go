package crystal

import (
	"context"

	"app/logic/command/base"
	"app/protos/out/cl"

	cret "app/protos/out/ret"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/zebra/parse"
)

func Init(cmds *parse.CommandM, state bool) {
	cmds.Register(uint32(cl.ID_MSG_C2L_CrystalAddHero), &C2LCrystalAddHeroCommand{}, state)                     //添加共鸣英雄
	cmds.Register(uint32(cl.ID_MSG_C2L_CrystalRemoveHero), &C2LCrystalRemoveHeroCommand{}, state)               //移出共鸣英雄
	cmds.Register(uint32(cl.ID_MSG_C2L_CrystalUnlockSlot), &C2LCrystalUnlockSlotCommand{}, state)               //解锁槽位
	cmds.Register(uint32(cl.ID_MSG_C2L_CrystalSpeedSlotCD), &C2LCrystalSpeedSlotCDCommand{}, state)             //加速完成槽位冷却
	cmds.Register(uint32(cl.ID_MSG_C2L_CrystalGetAllData), &C2LCrystalGetAllDataCommand{}, state)               //获取水晶信息
	cmds.Register(uint32(cl.ID_MSG_C2L_CrystalActiveAchievement), &C2LCrystalActiveAchievementCommand{}, state) //激活成就
}

type C2LCrystalAddHeroCommand struct {
	base.UserCommand
}

func (c *C2LCrystalAddHeroCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCrystalAddHeroCommand) Error(msg *cl.L2C_CrystalAddHero, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalAddHero, msg)
	return false
}

func (c *C2LCrystalAddHeroCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CrystalAddHero{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CrystalAddHero Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CrystalAddHero: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_CrystalAddHero{
		Ret:    uint32(cret.RET_OK),
		Hid:    cmsg.Hid,
		SlotId: cmsg.SlotId,
	}
	l4g.Errorf("function not open, uid:%d", c.Msg.UID)
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	/*

		if cmsg.Hid == 0 || cmsg.SlotId == 0 {
			l4g.Errorf("user: %d C2L_CrystalAddHero: param error. hid:%d, slotID:%d",
				c.Msg.UID, cmsg.Hid, cmsg.SlotId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_CRYSTAL), c.Srv) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: function not open", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
		}

		crystalM := c.User.Crystal()
		if !crystalM.IsLegalSlotID(cmsg.SlotId) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: param slotID is illegal. slotID:%d",
				c.Msg.UID, cmsg.SlotId)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_ID_ILLEGAL))
		}

		slot := crystalM.GetSlot(cmsg.SlotId)
		if slot == nil {
			l4g.Errorf("user: %d C2L_CrystalAddHero: slot not exist. slotID:%d",
				c.Msg.UID, cmsg.SlotId)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_NOT_EXIST))
		}

		if !crystalM.IsSlotEmpty(slot.Hid) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: slot not empty. slotID:%d, hid:%d",
				c.Msg.UID, cmsg.SlotId, slot.Hid)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_NOT_EMPTY))
		}

		if crystalM.IsSlotUnderCD(slot.CdEndTime) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: slot under cool down. slotID:%d, CdEndTime:%d",
				c.Msg.UID, cmsg.SlotId, slot.CdEndTime)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_UNDER_CD))
		}

		heroManager := c.User.HeroManager()
		hero := heroManager.Get(cmsg.Hid)
		if hero == nil {
			l4g.Errorf("user: %d C2L_CrystalAddHero: hero not exist. hid:%d", c.Msg.UID, cmsg.Hid)
			return c.Error(smsg, uint32(cret.RET_HERO_NOT_EXIST))
		}

		heroRare := goxml.GetData().HeroInfoM.GetRare(hero.GetHeroSysID())
		if !goxml.GetData().CrystalConfigInfoM.CheckCrystalHeroRare(heroRare) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: hero rare limit. hid:%d, heroRare:%d",
				c.Msg.UID, cmsg.Hid, heroRare)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_RESONANCE_RARE_LIMIT))
		}

		if !crystalM.IsFreedomHero(hero.GetHid()) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: hero crystal type not freedom. hid:%d",
				c.Msg.UID, cmsg.Hid)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_NOT_FREEDOM_HERO))
		}

		if crystalM.IsSysIDExistInResonanceHeroes(hero.GetHeroSysID()) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: hero exist in resonance hero list. hid:%d, sysID:%d",
				c.Msg.UID, cmsg.Hid, hero.GetHeroSysID())
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_EXIST_IN_RESONANCE_HEROES))
		}

		if crystalM.IsSysIDExistInContractHeroes(hero.GetHeroSysID()) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: hero exist in contract hero list. hid:%d, sysID:%d",
				c.Msg.UID, cmsg.Hid, hero.GetHeroSysID())
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_EXIST_IN_CONTRACT_HEROES))
		}

		smsg.Ret, smsg.Awards = heroManager.DoRevive(c.Srv, hero, false,
			uint32(log.RESOURCE_CHANGE_REASON_CRYSTAL_ADD_RESONANCE))
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_CrystalAddHero: DoRevive failed. hid:%d", c.Msg.UID, hero.GetHid())
			return c.Error(smsg, smsg.Ret)
		}

		crystalM.PutIn(c.Srv, slot, cmsg.Hid)
		c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
		smsg.Hero = hero.Flush()
		c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalAddHero, smsg)
		c.User.LogCrystalAddHero(c.Srv, cmsg.Hid, cmsg.SlotId)
		return c.ResultOK(smsg.Ret)
	*/
}

type C2LCrystalRemoveHeroCommand struct {
	base.UserCommand
}

func (c *C2LCrystalRemoveHeroCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCrystalRemoveHeroCommand) Error(msg *cl.L2C_CrystalRemoveHero, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalRemoveHero, msg)
	return false
}

func (c *C2LCrystalRemoveHeroCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CrystalRemoveHero{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CrystalRemoveHero Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CrystalRemoveHero: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_CrystalRemoveHero{
		Ret:    uint32(cret.RET_OK),
		SlotId: cmsg.SlotId,
	}
	l4g.Errorf("function not open, uid:%d", c.Msg.UID)
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))

	/*
	   	if cmsg.SlotId == 0 {
	   		l4g.Errorf("user: %d C2L_CrystalRemoveHero: param error. slotID:%d", c.Msg.UID, cmsg.SlotId)
	   		return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
	   	}

	   	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_CRYSTAL), c.Srv) {
	   		l4g.Errorf("user: %d C2L_CrystalRemoveHero: function not open", c.Msg.UID)
	   		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	   	}

	   crystalM := c.User.Crystal()

	   	if !crystalM.IsLegalSlotID(cmsg.SlotId) {
	   		l4g.Errorf("user: %d C2L_CrystalRemoveHero: param slotID is illegal, slotID:%d",
	   			c.Msg.UID, cmsg.SlotId)
	   		return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_ID_ILLEGAL))
	   	}

	   slot := crystalM.GetSlot(cmsg.SlotId)

	   	if slot == nil {
	   		l4g.Errorf("user: %d C2L_CrystalRemoveHero: slot not exist, slotID:%d",
	   			c.Msg.UID, cmsg.SlotId)
	   		return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_NOT_EXIST))
	   	}

	   hid := slot.Hid

	   	if hid == 0 {
	   		l4g.Errorf("user: %d C2L_CrystalRemoveHero: empty slot, slotID:%d",
	   			c.Msg.UID, cmsg.SlotId)
	   		return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_EMPTY))
	   	}

	   crystalM.RemoveOut(c.Srv, slot)

	   heroManager := c.User.HeroManager()
	   hero := heroManager.Get(hid)
	   c.User.UpdateAllPower(c.Srv, character.PowerUpdateByNormalRaise)
	   smsg.Hero = hero.Flush()
	   c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalRemoveHero, smsg)
	   c.User.LogCrystalRemoveHero(c.Srv, hid, cmsg.SlotId)

	   return c.ResultOK(smsg.Ret)
	*/
}

type C2LCrystalUnlockSlotCommand struct {
	base.UserCommand
}

func (c *C2LCrystalUnlockSlotCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCrystalUnlockSlotCommand) Error(msg *cl.L2C_CrystalUnlockSlot, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalUnlockSlot, msg)
	return false
}

func (c *C2LCrystalUnlockSlotCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CrystalUnlockSlot{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CrystalUnlockSlot Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CrystalUnlockSlot: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_CrystalUnlockSlot{
		Ret: uint32(cret.RET_OK),
	}
	l4g.Errorf("function not open, uid:%d", c.Msg.UID)
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	/*
	   	if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_CRYSTAL), c.Srv) {
	   		l4g.Errorf("user: %d C2L_CrystalUnlockSlot: function not open", c.Msg.UID)
	   		return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	   	}

	   crystalM := c.User.Crystal()
	   slotID, isFull := crystalM.CalcUnlockSlotID()

	   	if isFull {
	   		l4g.Errorf("user: %d C2L_CrystalUnlockSlot: all slots unlocked, slotID:%d",
	   			c.Msg.UID, slotID)
	   		return c.Error(smsg, uint32(cret.RET_CRYSTAL_NO_MORE_SLOT_CAN_UNLOCK))
	   	}

	   purchasedNum := crystalM.GetPurchasedSlotCount()
	   itemPrice, diamondPrice := goxml.GetData().CrystalConfigInfoM.GetSlotPrice(purchasedNum)

	   	if len(itemPrice) == 0 && len(diamondPrice) == 0 {
	   		l4g.Errorf("user: %d C2L_CrystalUnlockSlot: GetSlotPrice err, purchasedNum:%d",
	   			c.Msg.UID, purchasedNum)
	   		return c.Error(smsg, uint32(cret.RET_SYSTEM_DATA_ERROR))
	   	}

	   var cost []*cl.Resource
	   itemEnough, _ := c.User.CheckResourcesSize(itemPrice)

	   	if itemEnough == uint32(cret.RET_OK) {
	   		cost = itemPrice
	   	} else {

	   		cost = diamondPrice
	   	}

	   smsg.Ret = c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_CRYSTAL_UNLOCK_SLOT), 0)

	   	if smsg.Ret != uint32(cret.RET_OK) {
	   		l4g.Errorf("user: %d C2L_CrystalUnlockSlot: consume err", c.Msg.UID)
	   		return c.Error(smsg, smsg.Ret)
	   	}

	   crystalM.UnlockSlot(slotID)

	   smsg.PurchasedNum = crystalM.GetPurchasedSlotCount()
	   c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalUnlockSlot, smsg)
	   c.User.LogCrystalUnlockSlot(c.Srv, slotID)
	   c.User.FireCommonEvent(c.Srv.EventM(), event.IeCrystalUnlockSlot, 1)
	   return c.ResultOK(smsg.Ret)
	*/
}

type C2LCrystalSpeedSlotCDCommand struct {
	base.UserCommand
}

func (c *C2LCrystalSpeedSlotCDCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCrystalSpeedSlotCDCommand) Error(msg *cl.L2C_CrystalSpeedSlotCD, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalSpeedSlotCD, msg)
	return false
}

func (c *C2LCrystalSpeedSlotCDCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CrystalSpeedSlotCD{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CrystalSpeedSlotCD Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CrystalSpeedSlotCD: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_CrystalSpeedSlotCD{
		Ret:    uint32(cret.RET_OK),
		SlotId: cmsg.SlotId,
	}
	l4g.Errorf("function not open, uid:%d", c.Msg.UID)
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
	/*
		if cmsg.SlotId == 0 {
			l4g.Errorf("user: %d C2L_CrystalSpeedSlotCD: param error. slotID:%d", c.Msg.UID, cmsg.SlotId)
			return c.Error(smsg, uint32(cret.RET_CLIENT_REQUEST_ERROR))
		}

		if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_CRYSTAL), c.Srv) {
			l4g.Errorf("user: %d C2L_CrystalSpeedSlotCD: function not open", c.Msg.UID)
			return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
		}

		crystalM := c.User.Crystal()
		slot := crystalM.GetSlot(cmsg.SlotId)
		if slot == nil {
			l4g.Errorf("user: %d C2L_CrystalSpeedSlotCD: slot not exist, slotID:%d",
				c.Msg.UID, cmsg.SlotId)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_NOT_EXIST))
		}

		leftCDTime := crystalM.GetCDLeftTime(slot.CdEndTime)
		if leftCDTime == 0 {
			l4g.Errorf("user: %d C2L_CrystalSpeedSlotCD: cd is over, slotID:%d",
				c.Msg.UID, cmsg.SlotId)
			return c.Error(smsg, uint32(cret.RET_CRYSTAL_SLOT_CD_OVER))
		}

		cost := goxml.GetData().CrystalConfigInfoM.CD2Res(leftCDTime)
		smsg.Ret = c.User.Consume(c.Srv, cost, uint32(log.RESOURCE_CHANGE_REASON_CRYSTAL_SPEED_SLOT_CD), 0)
		if smsg.Ret != uint32(cret.RET_OK) {
			l4g.Errorf("user: %d C2L_CrystalSpeedSlotCD: consume err", c.Msg.UID)
			return c.Error(smsg, smsg.Ret)
		}

		crystalM.SpeedSlotCD(slot)

		c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalSpeedSlotCD, smsg)
		c.User.LogCrystalSpeedSlotCD(c.Srv, cmsg.SlotId)
		return c.ResultOK(smsg.Ret)
	*/
}

type C2LCrystalGetAllDataCommand struct {
	base.UserCommand
}

func (c *C2LCrystalGetAllDataCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCrystalGetAllDataCommand) Error(msg *cl.L2C_CrystalGetAllData, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalGetAllData, msg)
	return false
}

func (c *C2LCrystalGetAllDataCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CrystalGetAllData{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CrystalGetAllData Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CrystalGetAllData: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_CrystalGetAllData{
		Ret: uint32(cret.RET_OK),
	}
	l4g.Errorf("function not open, uid:%d", c.Msg.UID)
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))
}

type C2LCrystalActiveAchievementCommand struct {
	base.UserCommand
}

func (c *C2LCrystalActiveAchievementCommand) ResultOK(retCode uint32) bool {
	return retCode == uint32(cret.RET_OK)
}

func (c *C2LCrystalActiveAchievementCommand) Error(msg *cl.L2C_CrystalActiveAchievement, retCode uint32) bool {
	msg.Ret = retCode
	c.User.SendCmdToGateway(cl.ID_MSG_L2C_CrystalActiveAchievement, msg)
	return false
}

func (c *C2LCrystalActiveAchievementCommand) Execute(ctx context.Context) bool {
	cmsg := &cl.C2L_CrystalActiveAchievement{}
	if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {
		l4g.Errorf("C2L_CrystalActiveAchievement Unmarshal error. uid:%d err:%s", c.Msg.UID, err)
		return false
	}
	c.Trace(cmsg)

	l4g.Debugf("user: %d C2L_CrystalActiveAchievement: cmsg:%s", c.Msg.UID, cmsg)
	smsg := &cl.L2C_CrystalActiveAchievement{
		Ret: uint32(cret.RET_OK),
		Ids: cmsg.Ids,
	}
	l4g.Errorf("function not open, uid:%d", c.Msg.UID)
	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))

}
