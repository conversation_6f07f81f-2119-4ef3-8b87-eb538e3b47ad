package goddessprizedraw

import (
	"app/goxml"
	"app/logic/actm"
	"app/logic/rank"
	"app/protos/in/db"
	"app/protos/in/r2l"
	"app/protos/out/cl"
	"app/protos/out/ret"
	"encoding/json"
	"slices"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type Manager struct {
	data   *db.GoddessPrizeDraw //这个里面的数据都要保证不为nil
	change bool
}

func NewManager() *Manager {
	m := &Manager{}
	return m
}

func (m *Manager) Init(actm.Servicer) {
}

func (m *Manager) Load(msg *r2l.R2L_Load) {
	l4g.Debugf("goddessprizedraw manager load data:%+v", msg.GoddessPrizeDraw)
	m.data = msg.GoddessPrizeDraw
	if m.data == nil {
		m.data = newGoddessPrizeDraw()
	}
}

// 保证db.GoddessPrizeDraw里的每个指针都不为nil,这样保存的时候不需要传op
func newGoddessPrizeDraw() *db.GoddessPrizeDraw {
	return &db.GoddessPrizeDraw{
		ApplyUsers: &db.GoddessPrizeDrawUsers{},
		WinUsers:   &db.GoddessPrizeDrawUsers{},
	}
}

func (m *Manager) Save(srv actm.Servicer) {
	data := &r2l.L2R_SaveGoddessPrizeDraw{
		Data: m.data.Clone(),
	}

	srv.SendCmdToDB(uint32(r2l.ID_MSG_L2R_SaveGoddessPrizeDraw), 0, data)
	m.change = false
}

func (m *Manager) OnTimer(srv actm.Servicer, now int64) {
	//l4g.Debugf("goddessprizedraw manager onTimer data:%+v, change:%+v", m.data, m.change)
	//检查奖励结算
	if m.change {
		m.Save(srv)
	}
	m.Award(srv, now)
}

func (m *Manager) Flush(uid uint64) *cl.GoddessPrizeDraw {
	hasBuy := m.HasBuy(uid)
	data := &cl.GoddessPrizeDraw{
		Buy:          hasBuy,
		DailyTime:    m.data.DailyTime,
		Draw:         m.data.DrawInt == uint32(1),
		WinUsers:     slices.Clone(m.data.WinUsers.Users),
		WinUserNames: slices.Clone(m.data.WinUsers.UserNames),
		ApplyCount:   uint32(len(m.data.ApplyUsers.Users)),
	}
	if len(data.WinUsers) > 1000 {
		data.WinUsers = data.WinUsers[0:1000]
		data.WinUserNames = data.WinUserNames[0:1000]
	}
	return data
}

func (m *Manager) GetDailyTime() int64 {
	return m.data.DailyTime
}

func (m *Manager) HasBuy(uid uint64) bool {
	for _, v := range m.data.ApplyUsers.Users {
		if v == uid {
			return true
		}
	}
	return false
}

func (m *Manager) Buy(uid uint64, name string) {
	m.data.ApplyUsers.Users = append(m.data.ApplyUsers.Users, uid)
	m.data.ApplyUsers.UserNames = append(m.data.ApplyUsers.UserNames, name)
	m.change = true
}

// 是否需要补发奖励
func (m *Manager) isNeedAward(now int64) bool {
	dailyZeroTime := int64(util.DailyZeroByTime(now))
	awardHour := int64(goxml.GetData().ConfigInfoM.GoddessPrizeDrawSignUpTime)
	awardMinute := int64(goxml.GetData().ConfigInfoM.GoddessPrizeDrawDrawTime)
	sendTime := dailyZeroTime + awardHour*3600 + awardMinute*60 //应该发奖的时间
	if m.data.DrawInt == 0 && (m.data.DailyTime != dailyZeroTime || now >= sendTime) {
		//没有发奖,隔天了或者当天超过9点半需要发奖
		return true
	}
	return false
}

func (m *Manager) isNeedReset(now int64) bool {
	dailyZeroTime := int64(util.DailyZeroByTime(now))
	if m.data.DailyTime != dailyZeroTime {
		return true
	}
	return false
}

// 零点重置
func (m *Manager) Reset(srv actm.Servicer, now int64) {
	//先看下是否要发奖
	m.Award(srv, now)
	if m.isNeedReset(now) {
		m.reset(srv, now)
	}
}

func (m *Manager) reset(srv actm.Servicer, now int64) {
	dailyZeroTime := int64(util.DailyZeroByTime(now))
	m.data = newGoddessPrizeDraw()
	m.data.DailyTime = dailyZeroTime
	m.Save(srv)
	l4g.Infof("GoddessPrizeDraw reset dailyZeroTime:%d, now:%d", dailyZeroTime, now)
}

// 9点半发奖
func (m *Manager) Award(srv actm.Servicer, now int64) {
	if m.isNeedAward(now) {
		m.award(srv, m.data.DailyTime, now)
	}
}

func (m *Manager) award(srv actm.Servicer, dailyTime, now int64) {
	serverOpenDay := srv.ServerDay(dailyTime)
	info := goxml.GetData().ActivityGoddessPrizeDrawInfoM.GetRecordByServerDayMinMaxLe(serverOpenDay)
	l4g.Debugf("goddessPrizeDraw begin award daily:%d now:%d openday:%d, info:%+v", dailyTime, now, serverOpenDay, info)
	if info == nil {
		l4g.Errorf("goddessPrizeDraw begin award error no found config.daily:%d now:%d openday:%d, info:%+v", dailyTime, now, serverOpenDay, info)
		return
	}
	count := len(m.data.ApplyUsers.Users)
	baseNum := int(goxml.GetData().ConfigInfoM.GoddessPrizeDrawWinBaseNum)
	exNum := int(goxml.GetData().ConfigInfoM.GoddessPrizeDrawWinExtraNum)
	if count == 0 {
		m.data.DrawInt = uint32(1)
		m.Save(srv)
		return
	}
	winNum := 0
	if count <= baseNum {
		winNum = 1
	} else {
		winNum = 1 + (count-baseNum-1)/exNum + 1
	}

	j := count
	for i := 0; i < winNum; i++ {
		winIndex := srv.Rand().RandBetween(0, j-1)
		uid := m.data.ApplyUsers.Users[winIndex]
		name := m.data.ApplyUsers.UserNames[winIndex]
		m.data.WinUsers.Users = append(m.data.WinUsers.Users, uid)
		m.data.WinUsers.UserNames = append(m.data.WinUsers.UserNames, name)
		if winIndex != j-1 {
			m.data.ApplyUsers.Users[winIndex], m.data.ApplyUsers.Users[j-1] = m.data.ApplyUsers.Users[j-1], m.data.ApplyUsers.Users[winIndex]
			m.data.ApplyUsers.UserNames[winIndex], m.data.ApplyUsers.UserNames[j-1] = m.data.ApplyUsers.UserNames[j-1], m.data.ApplyUsers.UserNames[winIndex]
		}
		j--
	}
	winUsers := m.data.ApplyUsers.Users[count-winNum:]
	loseUsers := m.data.ApplyUsers.Users[0 : count-winNum]
	srv.SendMail(goxml.GoddessPrizeDrawWinMail, nil, info.WinRewards, winUsers)
	srv.SendMail(goxml.GoddessPrizeDrawLoseMail, nil, info.LoseRewards, loseUsers)
	m.data.DrawInt = uint32(1)
	l4g.Infof("GoddessPrizeDraw awards winUsers:%+v, loseUsers:%+v", winUsers, loseUsers)
	m.Save(srv)
}

func (m *Manager) QaSet(srv actm.Servicer, content string) uint32 {
	type GoddessPrizeDrawQaSetReq struct {
		Num uint32 `json:"num"`
	}
	req := &GoddessPrizeDrawQaSetReq{}
	err := json.Unmarshal([]byte(content), req)
	if err != nil {
		l4g.Error("GoddessPrizeDraw QaSet unmarshal error:%s, content:%s", err, content)
		return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
	}
	if req.Num == 0 {
		l4g.Error("GoddessPrizeDraw QaSet num is 0:%d, content:%s", req.Num, content)
		return uint32(ret.RET_GM_REQ_PARAM_ILLEGAL)
	}
	//从排行榜里找出n个人放进来
	rk := srv.GetGlobalRank(rank.POWER)
	users := rk.GetRangeByRank(1, req.Num)
	for _, user := range users {
		v := user.(*rank.PowerValue)
		if !m.HasBuy(v.ID) {
			name := strconv.FormatUint(v.ID, 10)
			m.Buy(v.ID, name)
		}
	}
	return uint32(ret.RET_OK)
}
