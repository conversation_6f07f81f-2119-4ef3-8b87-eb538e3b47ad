package actm

import (
	"app/logic/rank"
	"app/protos/in/l2c"
	"app/protos/in/r2l"
	"app/protos/out/cl"

	"gitlab.qdream.com/kit/sea/math/rand"
)

// 注意这个里面不能包含chracter包
type Servicer interface {
	ServerDay(now int64) uint32
	Rand() *rand.Rand
	SendCmdToDB(uint32, uint64, interface{})
	SendCmdToCross(cmd l2c.ID, uid uint64, msg interface{}) bool
	SendMail(uint32, []string, []*cl.Resource, []uint64) //给指定的用户发邮件
	GetGlobalRank(rank.ID) *rank.Global
}

type Activity interface {
	Load(*r2l.R2L_Load)
	Init(Servicer)
	//每秒触发
	OnTimer(Servicer, int64)
	Save(Servicer)
}
