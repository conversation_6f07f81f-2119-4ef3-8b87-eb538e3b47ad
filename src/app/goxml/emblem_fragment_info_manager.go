package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type EmblemFragmentInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*EmblemFragmentInfo
	Types   map[uint32]map[uint32][]*EmblemFragmentInfo //type->quality->*info
}

func newEmblemFragmentInfoManager(xmlData *XmlData) *EmblemFragmentInfoManager {
	m := &EmblemFragmentInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *EmblemFragmentInfoManager) name() string {
	return "EmblemFragmentInfo"
}

func (m *EmblemFragmentInfoManager) ignoreForCheck() bool {
	return false
}

func (m *EmblemFragmentInfoManager) checkData() error {
	return nil
}

func (m *EmblemFragmentInfoManager) load(dir string, isShow bool) error {
	tmp := &EmblemFragmentInfos{}
	fileName := filepath.Join(dir, "emblem_fragment_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*EmblemFragmentInfo, len(tmp.Datas))
	m.Types = make(map[uint32]map[uint32][]*EmblemFragmentInfo)
	for _, data := range tmp.Datas {
		m.Datas[data.Id] = data
		if m.Types[data.Type] == nil {
			m.Types[data.Type] = make(map[uint32][]*EmblemFragmentInfo)
		}
		m.Types[data.Type][data.Rare] = append(m.Types[data.Type][data.Rare], data)
	}
	return nil
}

func (m *EmblemFragmentInfoManager) GetRecordById(id uint32) *EmblemFragmentInfo {
	return m.Datas[id]
}

func (m *EmblemFragmentInfoManager) Index(key uint32) *EmblemFragmentInfo {
	return m.Datas[key]
}

func (m *EmblemFragmentInfoManager) Cost(id uint32, num uint32) *cl.Resource {
	fragmentInfo := m.Index(id)
	cost := &cl.Resource{
		Type:  uint32(common.RESOURCE_EMBLEM_FRAGMENT),
		Value: fragmentInfo.Id,
		Count: num * fragmentInfo.Count,
	}
	return cost
}

func (m *EmblemFragmentInfoManager) Award(id uint32, num uint32) *cl.Resource {
	fragmentInfo := m.Index(id)
	award := &cl.Resource{
		Type:  uint32(common.RESOURCE_EMBLEM),
		Value: fragmentInfo.Aim,
		Count: num,
	}
	return award
}
