package goxml

// 添加完排行榜之后需要添加character/user.go 给默认值赋值
const (
	PowerRankId                    uint32 = 1  //战力排行榜
	DungeonRankId                  uint32 = 2  //主线排行榜
	TowerRankId                    uint32 = 3  //爬塔排行榜
	ArenaRankId                    uint32 = 4  //竞技场排行榜
	MirageEmpireRankId             uint32 = 5  //个人boss-种族1排行榜
	MirageForestRankId             uint32 = 6  //个人boss-种族2排行榜
	MirageMoonRankId               uint32 = 7  //个人boss-种族3排行榜
	MirageProtossRankId            uint32 = 8  //个人boss-种族4排行榜
	MirageDemonRankId              uint32 = 9  //个人boss-种族5排行榜
	GuildMemberDungeonDamageRankId uint32 = 10 //公会成员副本伤害排名(只有本公会的，走的单独接口)
	GuildDungeonChapterRankId      uint32 = 11 //公会副本章节进度排行榜
	TowerstarRankId                uint32 = 12 //条件爬塔排行榜
	GuildLevelRankID               uint32 = 13 //公会等级排行榜
	WrestleRankID                  uint32 = 14 //神树争霸排行榜
	//GuildDungeonWeeklyDamageRankId uint32 = 15 //公会副本周伤害排行
	MirageSixRankId               uint32 = 16    //个人boss-种族6排行榜
	GuildDivisionRankID           uint32 = 17    // 公会段位榜
	GuildActivityRankID           uint32 = 18    // 公会活跃度榜
	GuildDungeonUserDamageRankId  uint32 = 19    // 公会玩家副本伤害排行
	WorldBossRankID               uint32 = 20    // 世界boss排行
	TowerSeasonRankID             uint32 = 21    // 百塔排行榜
	ActivityStoryRankID           uint32 = 22    // 活动故事排行榜ID
	PeakRankID                    uint32 = 23    // 巅峰竞技场排行榜 - 仅前端需要
	SeasonDungeonRankID           uint32 = 24    // 赛季主线排行榜   TODO 改为正式的id
	SeasonPowerRankID             uint32 = 25    // 赛季战力排行榜
	SeasonArenaRankID             uint32 = 26    // 赛季竞技场排行榜
	SeasonArenaFameID             uint32 = 27    // 赛季竞技场声望排行榜
	GuildMobRankID                uint32 = 28    // 公会竞赛排行榜
	ActivityComplianceID          uint32 = 29    // 开服冲榜活动排行榜
	ActivityMirageRankID          uint32 = 30    // 幻境冲榜活动排行榜
	ActivityTowerRankID           uint32 = 31    // 地宫冲榜活动排行榜
	ActivityTowerSeasonRankID     uint32 = 40    // 百塔冲榜排行榜
	SeasonComplianceTotalID       uint32 = 50    // 赛季冲榜总榜
	SeasonComplianceHeroID        uint32 = 51    // 赛季冲榜英雄
	SeasonComplianceChallengeID   uint32 = 52    // 赛季冲榜英雄
	SeasonComplianceArtifactID    uint32 = 53    // 赛季冲榜英雄
	SeasonComplianceEmblemID      uint32 = 54    // 赛季冲榜英雄
	SeasonCompliancePokemonID     uint32 = 55    // 赛季冲榜宠物
	SeasonMapExploreRate          uint32 = 56    // 赛季地图探索度
	TowerPokemonRank              uint32 = 58    // 赛季爬塔
	BalanceArenaBigGroupRank      uint32 = 59    // 公平竞技场大组赛
	BalanceArenaSmallGroupRank    uint32 = 60    // 公平竞技场小组组赛
	BalanceArenaSeasonRank        uint32 = 61    // 公平竞技场赛季
	DisorderLandHurdle1RankID     uint32 = 1001  // 失序空间 - 关卡类型A的排行榜
	DisorderLandHurdle2RankID     uint32 = 1002  // 失序空间 - 关卡类型B的排行榜
	DisorderLandHurdle3RankID     uint32 = 1003  // 失序空间 - 关卡类型C的排行榜
	DisorderLandHurdle4RankID     uint32 = 1004  // 失序空间 - 关卡类型D的排行榜
	DisorderLandHurdle5RankID     uint32 = 1005  // 失序空间 - 关卡类型E的排行榜
	DisorderLandHurdle6RankID     uint32 = 1006  // 失序空间 - 关卡类型F的排行榜
	DisorderLandHurdle7RankID     uint32 = 1007  // 失序空间 - 关卡类型G的排行榜
	DisorderLandHurdle8RankID     uint32 = 1008  // 失序空间 - 关卡类型H的排行榜
	SeasonLink1RankID             uint32 = 2001  // 战区赛季羁绊1养成榜
	SeasonLink2RankID             uint32 = 2002  // 战区赛季羁绊2养成榜
	SeasonLink3RankID             uint32 = 2003  // 战区赛季羁绊3养成榜
	SeasonLink4RankID             uint32 = 2004  // 战区赛季羁绊4养成榜
	SeasonLink5RankID             uint32 = 2005  // 战区赛季羁绊5养成榜
	SeasonLink6RankID             uint32 = 2006  // 战区赛季羁绊6养成榜
	SeasonLink7RankID             uint32 = 2007  // 战区赛季羁绊7养成榜
	SeasonLink8RankID             uint32 = 2008  // 战区赛季羁绊8养成榜
	SeasonRemainBookExpRankID     uint32 = 2009  // 战区赛季遗物图鉴养成榜
	BossRush1RankID               uint32 = 2101  // 战区Boss挑战排行榜1
	BossRush2RankID               uint32 = 2102  // 战区Boss挑战排行榜2
	BossRush3RankID               uint32 = 2103  // 战区Boss挑战排行榜3
	BossRush4RankID               uint32 = 2104  // 战区Boss挑战排行榜4
	BossRush5RankID               uint32 = 2105  // 战区Boss挑战排行榜5
	BossRush6RankID               uint32 = 2106  // 战区Boss挑战排行榜6
	BossRush7RankID               uint32 = 2107  // 战区Boss挑战排行榜7
	BossRush8RankID               uint32 = 2108  // 战区Boss挑战排行榜8
	GstGroupUsersContributionRank uint32 = 27001 // 公会战小组玩家贡献值排行
	GstGroupUsersKillRank         uint32 = 27002 // 公会战小组玩家击杀排行
	GstGroupUsersTripleKillRank   uint32 = 27003 // 公会战小组玩家三杀排行
	TalentTreeRootLevelRank       uint32 = 31501 // 天赋树根节点等级榜
	GSTChallengeScoreRank         uint32 = 27004 // 公会战新擂台赛单场积分榜
	GSTChallengeTotalScoreRank    uint32 = 27005 // 公会战新擂台赛总积分榜
)

func IsSeasonLinkRankId(rankId uint32) bool {
	//if rankId == SeasonLink1RankID || rankId == SeasonLink2RankID || rankId == SeasonLink3RankID ||
	//	rankId == SeasonLink4RankID || rankId == SeasonLink5RankID || rankId == SeasonLink6RankID ||
	//	rankId == SeasonLink7RankID || rankId == SeasonLink8RankID {
	//	return true
	//}
	return false
}
