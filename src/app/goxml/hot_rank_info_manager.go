package goxml

import (
	"fmt"
	"golang.org/x/exp/slices"
	"path/filepath"
	"strconv"
	"strings"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type HotRankInfoManager struct {
	xmlData           *XmlData
	Datas             map[uint32]*HotRankInfoExt
	Formation         map[uint32][]*HotRankInfoExt
	TypeMergeHotRanks map[uint32][]*HotRankInfoExt //type 待合并的排行榜ID
}

type HotRankInfoExt struct {
	ID           uint32
	Type         uint32
	FormationIDs []uint32
	Param1       uint32
	Param2       uint32
	Max          uint32
	RefreshTime  uint32
}

func newHotRankInfoManager(xmlData *XmlData) *HotRankInfoManager {
	m := &HotRankInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HotRankInfoManager) name() string {
	return "hot_rank_info.xml"
}

func (m *HotRankInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HotRankInfoManager) checkData() error {
	return nil
}

func (m *HotRankInfoManager) load(dir string, show bool) error {
	tmp := &HotRankInfos{}
	fileName := filepath.Join(dir, "hot_rank_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*HotRankInfoExt, len(tmp.Datas))
	m.Formation = make(map[uint32][]*HotRankInfoExt)
	m.TypeMergeHotRanks = make(map[uint32][]*HotRankInfoExt)
	for _, data := range tmp.Datas {
		data.prepare()
		formationIDS := strings.Split(data.FormationId, ",")
		ext := &HotRankInfoExt{
			FormationIDs: make([]uint32, 0, len(data.FormationId)),
		}
		for index, formation := range formationIDS {
			if formation != "" {
				formationID, err := strconv.Atoi(formation)
				if err != nil {
					panic(fmt.Sprintf("file:%s id:%d parse formation index:%d id error: %s", fileName, data.Id, index, formation))
				}
				formationInfo := m.xmlData.FormationInfoM.Index(uint32(formationID))
				if formationInfo == nil {
					panic(fmt.Sprintf("file:%s id:%d formation:%d info is nil", fileName, data.Id, uint32(formationID)))
				}
				ext.FormationIDs = append(ext.FormationIDs, uint32(formationID))

				if data.Param1 >= data.Param2 {
					panic(fmt.Sprintf("file:%s id:%d param1:%d more or equal param2:%d", fileName, data.Id, data.Param1, data.Param2))
				}
			}
		}

		ext.ID = data.Id
		ext.Type = data.Type
		ext.Param1 = data.Param1
		ext.Param2 = data.Param2
		ext.Max = data.Max
		ext.RefreshTime = data.RefreshTime

		if _, exist := m.Datas[data.Id]; exist {
			panic(fmt.Sprintf("file:%s id:%d is repeated", fileName, data.Id))
		} else {
			m.Datas[data.Id] = ext
		}

		for _, formation := range ext.FormationIDs {
			_, exist := m.Formation[formation]
			if !exist {
				m.Formation[formation] = make([]*HotRankInfoExt, 0, 3)
				m.Formation[formation] = append(m.Formation[formation], ext)
			} else {
				if !slices.Contains(m.Formation[formation], ext) {
					m.Formation[formation] = append(m.Formation[formation], ext)
				}
			}
		}

		if len(ext.FormationIDs) > 0 {
			m.TypeMergeHotRanks[data.Type] = append(m.TypeMergeHotRanks[data.Type], ext)
		}
	}
	return nil
}

func (m *HotRankInfoManager) Index(key uint32) *HotRankInfoExt {
	return m.Datas[key]
}

func (m *HotRankInfoManager) GetInsertRank(formation uint32, progress uint32) *HotRankInfoExt {
	for _, rankInfo := range m.Formation[formation] {
		if rankInfo == nil {
			continue
		}
		if progress >= rankInfo.Param1 && progress <= rankInfo.Param2 {
			return rankInfo
		}
	}
	return nil
}

func (m *HotRankInfoManager) GetTypeMergeHotRanks() map[uint32][]*HotRankInfoExt {
	return m.TypeMergeHotRanks
}
