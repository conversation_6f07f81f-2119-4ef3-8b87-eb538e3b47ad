package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type BelltowerConfigInfoManager struct {
	xmlData *XmlData
	Datas   map[string]*BelltowerConfigInfo

	HeroBackCost       []*cl.Resource
	HeroChangeCost     []*cl.Resource
	EquipRevivieCost   []*cl.Resource
	EmblemRevivieCost  []*cl.Resource
	ArtifactReviveCost []*cl.Resource
	HeroChooseLimit    uint32
	EquipChooseLimit   uint32
	GemChooseLimit     uint32
	EmblemChooseLimit  uint32
}

func newBelltowerConfigInfoManager(xmlData *XmlData) *BelltowerConfigInfoManager {
	m := &BelltowerConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *BelltowerConfigInfoManager) name() string {
	return "BelltowerConfigInfo"
}

func (m *BelltowerConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *BelltowerConfigInfoManager) checkData() error {
	return nil
}

func (m *BelltowerConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &BelltowerConfigInfos{}
	fileName := filepath.Join(dir, "belltower_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	l4g.Debug("BelltowerConfigInfoM data: %+v", tmp.Datas)
	m.Datas = make(map[string]*BelltowerConfigInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Count == 0 {
			panic(fmt.Sprintf("load %s fail: count is 0. %v", fileName, data))
		}
		if (data.Key == "HERO_BACK_COST" || data.Key == "HERO_CHANGE_COST" || data.Key == "EQUIP_REVIVIE_COST" || data.Key == "EMBLEM_REVIVIE_COST" || data.Key == "ARTIFACT_REVIVE_COST") && data.Type == 0 {
			panic(fmt.Sprintf("load %s fail: type is 0. %v", fileName, data))
		}
		m.Datas[data.Key] = data
	}

	m.HeroBackCost = m.Datas["HERO_BACK_COST"].ClRes
	m.HeroChangeCost = m.Datas["HERO_CHANGE_COST"].ClRes
	m.EquipRevivieCost = m.Datas["EQUIP_REVIVIE_COST"].ClRes
	m.EmblemRevivieCost = m.Datas["EMBLEM_REVIVIE_COST"].ClRes
	m.HeroChooseLimit = m.Datas["HERO_CHOOSE_LIMIT"].Count
	m.EquipChooseLimit = m.Datas["EQUIP_CHOOSE_LIMIT"].Count
	m.GemChooseLimit = m.Datas["GEM_CHOOSE_LIMIT"].Count
	m.EmblemChooseLimit = m.Datas["EMBLEM_CHOOSE_LIMIT"].Count
	m.ArtifactReviveCost = m.Datas["ARTIFACT_REVIVE_COST"].ClRes
	return nil
}

func (m *BelltowerConfigInfoManager) Index(key string) *BelltowerConfigInfo {
	return m.Datas[key]
}
