package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type EmblemCustomizeHeroInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*EmblemCustomizeHeroInfo
	Group   map[uint32][]*EmblemCustomizeHeroInfo
}

func newEmblemCustomizeHeroInfoManager(xmlData *XmlData) *EmblemCustomizeHeroInfoManager {
	m := &EmblemCustomizeHeroInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *EmblemCustomizeHeroInfoManager) name() string {
	return "EmblemCustomizeHeroInfo"
}

func (m *EmblemCustomizeHeroInfoManager) ignoreForCheck() bool {
	return false
}

func (m *EmblemCustomizeHeroInfoManager) checkData() error {
	return nil
}

func (m *EmblemCustomizeHeroInfoManager) load(dir string, isShow bool) error {
	tmp := &EmblemCustomizeHeroInfos{}
	fileName := filepath.Join(dir, "emblem_customize_hero_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*EmblemCustomizeHeroInfo, len(tmp.Datas))
	}
	m.Group = make(map[uint32][]*EmblemCustomizeHeroInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		_, exist := m.Group[data.HeroGroupId]
		if !exist {
			m.Group[data.HeroGroupId] = make([]*EmblemCustomizeHeroInfo, 0, 8)
		}
		m.Group[data.HeroGroupId] = append(m.Group[data.HeroGroupId], data)

		heroInfo := m.xmlData.HeroInfoM.Index(data.HeroId)
		if heroInfo == nil {
			panic(fmt.Sprintf("load config %s data:%d heroId:%d not exist in heroInfo ", fileName, data.Id, data.HeroId))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *EmblemCustomizeHeroInfoManager) GetRecordById(id uint32) *EmblemCustomizeHeroInfo {
	return m.Datas[id]
}

func (m *EmblemCustomizeHeroInfoManager) Index(key uint32) *EmblemCustomizeHeroInfo {
	return m.Datas[key]
}

func (m *EmblemCustomizeHeroInfoManager) InHeroGroup(groupId, sysHeroId uint32) *EmblemCustomizeHeroInfo {
	for _, v := range m.Group[groupId] {
		if v.HeroId == sysHeroId {
			return v
		}
	}
	return nil
}

func (m *EmblemCustomizeHeroInfoManager) GetGroup() map[uint32][]*EmblemCustomizeHeroInfo {
	return m.Group
}
