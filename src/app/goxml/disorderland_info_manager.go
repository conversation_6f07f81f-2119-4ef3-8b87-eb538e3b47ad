package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type DisorderlandInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*DisorderlandInfoExt
}

type DisorderlandInfoExt struct {
	Difficulty       uint32
	MapId            uint32 //int:索引地图id
	SeasonLevel      uint32 //int:解锁巅峰等级
	DifficultyUnlock uint32 //int:解锁前置难度
	SeasonDay        uint32 //int:解锁赛季天数
	MonumentUnlock   uint32 //int:解锁丰碑等级
	NumUnlock        uint32 //int:解锁丰碑数量
	UnlockPoints     []uint32
}

func newDisorderlandInfoManager(xmlData *XmlData) *DisorderlandInfoManager {
	m := &DisorderlandInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DisorderlandInfoManager) name() string {
	return "DisorderlandInfo"
}

func (m *DisorderlandInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DisorderlandInfoManager) checkData() error {
	return nil
}

func (m *DisorderlandInfoManager) load(dir string, isShow bool) error {
	tmp := &DisorderlandInfos{}
	fileName := filepath.Join(dir, "disorderland_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*DisorderlandInfoExt, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &DisorderlandInfoExt{
			Difficulty:       data.Difficulty,
			MapId:            data.MapId,
			SeasonLevel:      data.SeasonLevel,
			DifficultyUnlock: data.DifficultyUnlock,
			SeasonDay:        data.SeasonDay,
			MonumentUnlock:   data.MonumentUnlock,
			NumUnlock:        data.NumUnlock,
			UnlockPoints:     make([]uint32, 0, 3),
		}
		dataExt.putNode(data.Point1)
		dataExt.putNode(data.Point2)
		dataExt.putNode(data.Point3)

		for _, n := range dataExt.UnlockPoints {
			if m.xmlData.DisorderlandMapInfoM.Index(data.MapId, n) == nil {
				panic(fmt.Sprintf("load config %s fail: mapID or nodeID is invalid. mapID:%d nodeID:%d", fileName, data.MapId, n))
			}
		}

		if ptr, exist := m.Datas[data.Difficulty]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[dataExt.Difficulty] = dataExt
		}
	}
	return nil
}

func (e *DisorderlandInfoExt) putNode(id uint32) {
	if id > 0 {
		e.UnlockPoints = append(e.UnlockPoints, id)
	}
}

func (m *DisorderlandInfoManager) GetRecordByDifficulty(difficulty uint32) *DisorderlandInfoExt {
	return m.Datas[difficulty]
}

func (m *DisorderlandInfoManager) Index(key uint32) *DisorderlandInfoExt {
	return m.Datas[key]
}
