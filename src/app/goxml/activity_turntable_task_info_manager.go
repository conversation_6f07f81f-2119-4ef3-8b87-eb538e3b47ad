package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

func (m *ActivityTurntableTaskInfo) Check() error {
	return nil
}

type ActivityTurntableTaskInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]*ActivityTurntableTaskInfo
	allEvent    []uint32
	taskLen     int
	ModuleEvent map[uint32]map[uint32]struct{}
	ModuleData  map[uint32][]*ActivityTurntableTaskInfo
}

func newActivityTurntableTaskInfoManager(xmlData *XmlData) *ActivityTurntableTaskInfoManager {
	m := &ActivityTurntableTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityTurntableTaskInfoManager) name() string {
	return "activity_turntable_task_info.xml"
}

func (m *ActivityTurntableTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityTurntableTaskInfoManager) checkData() error {
	return nil
}

func (m *ActivityTurntableTaskInfoManager) load(dir string, show bool) error {
	tmp := &ActivityTurntableTaskInfos{}
	fileName := filepath.Join(dir, "activity_turntable_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*ActivityTurntableTaskInfo, len(tmp.Datas))
	allEvents := make(map[uint32]struct{})
	m.ModuleEvent = make(map[uint32]map[uint32]struct{})
	m.ModuleData = make(map[uint32][]*ActivityTurntableTaskInfo)
	for _, data := range tmp.Datas {
		data.prepare()

		if data.TypeId > 0 {
			taskType := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
			if taskType == nil {
				panic(fmt.Sprintf("file name:%s check data error %d task type:%d is error", fileName, data.Id, data.TypeId))
			}
			allEvents[taskType.Type] = struct{}{}
			_, exist := m.ModuleEvent[data.Module]
			if !exist {
				m.ModuleEvent[data.Module] = make(map[uint32]struct{})
			}
			m.ModuleEvent[data.Module][taskType.Type] = struct{}{}
		}

		_, exist := m.ModuleData[data.Module]
		if !exist {
			m.ModuleData[data.Module] = make([]*ActivityTurntableTaskInfo, 0, 2)
		}
		m.ModuleData[data.Module] = append(m.ModuleData[data.Module], data)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	for event := range allEvents {
		m.allEvent = append(m.allEvent, event)
	}
	m.taskLen = len(m.Datas)

	return nil
}

func (m *ActivityTurntableTaskInfoManager) Index(key uint32) *ActivityTurntableTaskInfo {
	return m.Datas[key]
}

func (m *ActivityTurntableTaskInfoManager) AllEvents() []uint32 {
	return m.allEvent
}

func (m *ActivityTurntableTaskInfoManager) TaskLen() int {
	return m.taskLen
}

func (m *ActivityTurntableTaskInfoManager) ModuleTaskLen(module uint32) int {
	return len(m.ModuleData[module])
}

func (m *ActivityTurntableTaskInfoManager) GetRecvTaskID(id uint32) RecvTask {
	info := m.Index(id)
	if info == nil {
		return nil
	}
	return m.Datas[id]
}

func (m *ActivityTurntableTaskInfo) GetID() uint32 {
	return m.Id
}

func (m *ActivityTurntableTaskInfo) TypeID() uint32 {
	return m.TypeId
}

func (m *ActivityTurntableTaskInfo) TaskValue() uint32 {
	return m.Value
}

func (m *ActivityTurntableTaskInfo) GetModule() uint32 {
	return m.Module
}

func (m *ActivityTurntableTaskInfo) GetAward() []*cl.Resource {
	return m.ClRes
}
