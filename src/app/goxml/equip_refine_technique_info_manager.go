package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type EquipRefineTechniqueInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32][]*EquipRefineTechniqueInfo
}

func newEquipRefineTechniqueInfoManager(xmlData *XmlData) *EquipRefineTechniqueInfoManager {
	m := &EquipRefineTechniqueInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *EquipRefineTechniqueInfoManager) name() string {
	return "EquipRefineTechniqueInfo"
}

func (m *EquipRefineTechniqueInfoManager) ignoreForCheck() bool {
	return false
}

func (m *EquipRefineTechniqueInfoManager) checkData() error {
	return nil
}

func (m *EquipRefineTechniqueInfoManager) load(dir string, isShow bool) error {
	tmp := &EquipRefineTechniqueInfos{}
	fileName := filepath.Join(dir, "equip_refine_technique_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32][]*EquipRefineTechniqueInfo)

	for _, data := range tmp.Datas {
		m.Datas[data.EquipId] = append(m.Datas[data.EquipId], data)
	}
	return nil
}

func (m *EquipRefineTechniqueInfoManager) Index(equipId, level uint32) *EquipRefineTechniqueInfo {
	if refineTechniqueInfo, exist := m.Datas[equipId]; exist {
		num := 0
		for _, v := range refineTechniqueInfo {
			if level >= v.Level {
				num++
				continue
			}
			break
		}
		if num <= 0 {
			return nil
		}
		return refineTechniqueInfo[num-1]
	}
	return nil
}

func (m *EquipRefineTechniqueInfoManager) GetAttr(totalAttr []int64, equipId, level uint32) {
	if refineTechniqueInfo, exist := m.Datas[equipId]; exist {
		num := 0
		for _, v := range refineTechniqueInfo {
			if level >= v.Level {
				num++
				continue
			}
			break
		}
		if num <= 0 {
			return
		}
		totalAttr[refineTechniqueInfo[num-1].AttrType] += int64(refineTechniqueInfo[num-1].AttrValue)
	}
}
