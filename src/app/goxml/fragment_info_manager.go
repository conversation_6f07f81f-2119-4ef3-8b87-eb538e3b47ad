package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type FragmentInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*FragmentInfo
	Types   map[uint32]map[uint32][]*FragmentInfo //type->quality->*info
}

func newFragmentInfoManager(xmlData *XmlData) *FragmentInfoManager {
	m := &FragmentInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *FragmentInfoManager) name() string {
	return "FragmentInfo"
}

func (m *FragmentInfoManager) ignoreForCheck() bool {
	return false
}

func (m *FragmentInfoManager) checkData() error {
	return nil
}

func (m *FragmentInfoManager) load(dir string, isShow bool) error {
	tmp := &FragmentInfos{}
	fileName := filepath.Join(dir, "fragment_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*FragmentInfo, len(tmp.Datas))
	m.Types = make(map[uint32]map[uint32][]*FragmentInfo)
	for _, data := range tmp.Datas {
		m.Datas[data.Id] = data
		if m.Types[data.Type] == nil {
			m.Types[data.Type] = make(map[uint32][]*FragmentInfo)
		}
		m.Types[data.Type][data.Rare] = append(m.Types[data.Type][data.Rare], data)
	}
	return nil
}

func (m *FragmentInfoManager) GetRecordById(id uint32) *FragmentInfo {
	return m.Datas[id]
}

func (m *FragmentInfoManager) Index(key uint32) *FragmentInfo {
	return m.Datas[key]
}

func (m *FragmentInfoManager) RandByQuality(rd *rand.Rand, typ, quality uint32) *FragmentInfo {
	if length := len(m.Types[typ][quality]); length != 0 {
		return m.Types[typ][quality][rd.Intn(length)]
	}
	return nil
}

func (m *FragmentInfoManager) Cost(id uint32, num uint32) *cl.Resource {
	fragmentInfo := m.Index(id)
	cost := &cl.Resource{
		Type:  uint32(common.RESOURCE_FRAGMENT),
		Value: fragmentInfo.Id,
		Count: num * fragmentInfo.Count,
	}
	return cost
}

func (m *FragmentInfoManager) Award(id uint32, num uint32) *cl.Resource {
	fragmentInfo := m.Index(id)
	award := &cl.Resource{
		Type:  uint32(common.RESOURCE_HERO),
		Value: fragmentInfo.Aim,
		Count: num,
	}
	return award
}

// 单个英雄转碎片
func (m *FragmentInfoManager) Hero2Fragment(heroId uint32) *cl.Resource {
	heroInfo := m.xmlData.HeroInfoM.Index(heroId)
	if heroInfo == nil {
		l4g.Error("hero not exist:%d ", heroId)
		return nil
	}
	fragmentInfo := m.Index(heroInfo.FragmentId)
	if fragmentInfo == nil {
		return nil
	}
	heroFragment := &cl.Resource{
		Type:  uint32(common.RESOURCE_FRAGMENT),
		Value: fragmentInfo.Id,
		Count: fragmentInfo.Count,
	}
	return heroFragment
}
