package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type PlayerLevelInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*PlayerLevelInfoExt
	MaxLv   uint32
	lastLv  uint32
}

type PlayerLevelInfoExt struct {
	Level                   uint32
	Exp                     uint32
	Awards                  []*cl.Resource
	TrialCumulativeDuration int64
}

func newPlayerLevelInfoManager(xmlData *XmlData) *PlayerLevelInfoManager {
	m := &PlayerLevelInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *PlayerLevelInfoManager) name() string {
	return "PlayerLevelInfo"
}

func (m *PlayerLevelInfoManager) ignoreForCheck() bool {
	return false
}

func (m *PlayerLevelInfoManager) checkData() error {
	return nil
}

func (m *PlayerLevelInfoManager) load(dir string, isShow bool) error {
	tmp := &PlayerLevelInfos{}
	fileName := filepath.Join(dir, "player_level_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*PlayerLevelInfoExt, len(tmp.Datas))
	}
	m.MaxLv = uint32(0)
	m.lastLv = uint32(0)

	for _, data := range tmp.Datas {
		data.prepare()
		if m.lastLv+1 != data.Level {
			panic(fmt.Sprintf("config data discontinuity: %s, %d", fileName, m.lastLv+1))
		}

		if data.Level > m.MaxLv {
			m.MaxLv = data.Level
		}

		if len(data.RewardClRes) <= 0 {
			panic(fmt.Sprintf("load config %s fail: no reward. level:%d", fileName, data.Level))
		}

		dataExt := &PlayerLevelInfoExt{
			Level:                   data.Level,
			Exp:                     data.Pexp,
			Awards:                  data.RewardClRes,
			TrialCumulativeDuration: int64(data.TrialCumulativeDuration),
		}

		if ptr, exist := m.Datas[data.Level]; exist {
			*ptr = *dataExt
			m.Datas[data.Level] = ptr
		} else {
			m.Datas[data.Level] = dataExt
		}
		m.lastLv++
	}
	return nil
}

func (m *PlayerLevelInfoManager) Index(level uint32) *PlayerLevelInfoExt {
	return m.Datas[level]
}

func (m *PlayerLevelInfoManager) IsTopLv(level uint32) bool {
	return level >= m.MaxLv
}

func (m *PlayerLevelInfoManager) MaxLevel() uint32 {
	return m.MaxLv
}

func (m *PlayerLevelInfoManager) GetLevelUpAwards(uid uint64, oldLv, newLv uint32) []*cl.Resource {
	if newLv <= oldLv {
		l4g.Errorf("user %d GetLevelUpAwards: newLv:%d <= oldLv:%d", uid, newLv, oldLv)
		return nil
	}

	if newLv > m.MaxLv {
		l4g.Errorf("user %d GetLevelUpAwards: newLv:%d > MaxLv:%d", uid, newLv, m.MaxLv)
		return nil
	}

	awards := make([]*cl.Resource, 0, newLv-oldLv)
	for i := oldLv; i < newLv; i++ {
		info := m.Index(i)
		if info == nil {
			l4g.Errorf("user %d GetLevelUpAwards: info not exist, level:%d", uid, i)
			return nil
		}
		awards = append(awards, info.Awards...)
	}
	return awards
}
