package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ChatInfoManager struct {
	xmlData               *XmlData
	Datas                 map[uint32]*ChatInfo
	groupTags             map[string]*ChatInfo
	ChatGuildGroupTag     string
	ChatPartitionGroupTag string
}

func newChatInfoManager(xmlData *XmlData) *ChatInfoManager {
	m := &ChatInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ChatInfoManager) name() string {
	return "ChatInfo"
}

func (m *ChatInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ChatInfoManager) checkData() error {
	return nil
}

func (m *ChatInfoManager) load(dir string, isShow bool) error {
	tmp := &ChatInfos{}
	fileName := filepath.Join(dir, "chat_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ChatInfo, len(tmp.Datas))
	}
	m.groupTags = make(map[string]*ChatInfo, 4)
	m.ChatGuildGroupTag = ""
	m.ChatPartitionGroupTag = ""
	for _, data := range tmp.Datas {
		data.prepare()
		if !m.checkGroupTagAndName(data) {
			panic(fmt.Sprintf("chat_info.xml config error. group tag or name is invalid. ID: %d", data.Id))
		}
		if data.GroupTag != "" {
			m.groupTags[data.GroupTag] = data
		}
		if data.Id == ChatGuild {
			m.ChatGuildGroupTag = data.GroupTag
		} else if data.Id == ChatPartition {
			m.ChatPartitionGroupTag = data.GroupTag
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ChatInfoManager) GetRecordById(id uint32) *ChatInfo {
	return m.Datas[id]
}

func (m *ChatInfoManager) Index(id uint32) *ChatInfo {
	return m.Datas[id]
}

func (m *ChatInfoManager) GetChatInfo(groupTag string) *ChatInfo {
	return m.groupTags[groupTag]
}

func (m *ChatInfoManager) checkGroupTagAndName(data *ChatInfo) bool {
	switch data.Id {
	case ChatGuild, ChatLocalWorld, ChatAllWorld, ChatPartition, ChatGuildDungeon, ChatGST, ChatSystemGuild:
		if data.GroupName == "" || data.GroupTag == "" {
			return false
		}
		return true
	default:
		if data.GroupName != "" || data.GroupTag != "" {
			return false
		}

		return true
	}
}

func (m *ChatInfoManager) GetAllGroupTag() []string {
	tags := make([]string, 0, 4)
	for _, data := range m.Datas {
		if data.GroupTag == "" {
			continue
		}
		tags = append(tags, data.GroupTag)
	}

	return tags
}
