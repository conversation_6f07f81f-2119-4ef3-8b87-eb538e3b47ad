package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityFeedingInfoManager struct {
	xmlData     *XmlData
	ActIDLevel  map[uint32]map[uint32]*ActivityFeedingInfo //actID level
	ActMaxLevel map[uint32]uint32
}

func newActivityFeedingInfoManager(xmlData *XmlData) *ActivityFeedingInfoManager {
	m := &ActivityFeedingInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityFeedingInfoManager) name() string {
	return "activity_feeding_info.xml"
}

func (m *ActivityFeedingInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityFeedingInfoManager) checkData() error {
	return nil
}

func (m *ActivityFeedingInfoManager) load(dir string, show bool) error {
	tmp := &ActivityFeedingInfos{}
	fileName := filepath.Join(dir, "activity_feeding_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.ActIDLevel = make(map[uint32]map[uint32]*ActivityFeedingInfo)
	m.ActMaxLevel = make(map[uint32]uint32)
	var levelCheck uint32
	var actID uint32
	for _, data := range tmp.Datas {
		data.prepare()
		actInfo := m.xmlData.ActivityTurntableInfoM.Index(data.Id)
		if actInfo == nil {
			panic(fmt.Sprintf("load config %s fail: %d id in actInfo is nil", fileName, data.Id))
		}
		if actID != data.Id {
			if data.Level != ActivitySumFeedDefaultCond || len(data.RewardClRes) > 0 {
				panic(fmt.Sprintf("load config %s fail: id %d level:%d default config is error %d", fileName, data.Id, data.Level))
			}
			actID = data.Id
			levelCheck = 0
		}

		if levelCheck+1 != data.Level {
			panic(fmt.Sprintf("load config %s fail: id %d level:%d level check fail", fileName, data.Id, data.Level))
		}

		levelCheck = data.Level

		if m.ActMaxLevel[data.Id] < data.Level {
			m.ActMaxLevel[data.Id] = data.Level
		}

		_, exist := m.ActIDLevel[actID]
		if !exist {
			m.ActIDLevel[actID] = make(map[uint32]*ActivityFeedingInfo)
		}
		m.ActIDLevel[data.Id][data.Level] = data
	}

	return nil
}

func (m *ActivityFeedingInfoManager) Index(actID uint32, level uint32) *ActivityFeedingInfo {
	return m.ActIDLevel[actID][level]
}

func (m *ActivityFeedingInfoManager) GetMaxLevel(actID uint32) uint32 {
	return m.ActMaxLevel[actID]
}
