package goxml

import (
	"app/protos/out/common"
	"fmt"
	"path/filepath"
	"sort"
	"strings"

	"time"

	l4g "github.com/ivanabc/log4go"
	seaTime "gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
)

type WorldbossInfoExt struct {
	Id            uint32 // 活动id
	StartTime     int64  // 开始时间
	EndTime       int64  // 停止时间
	SustainTime   int64  // 持续时间
	NormalBoss    uint32 // 普通boss
	HardBoss      uint32 // 困难boss
	NightmareBoss uint32 // 噩梦boss
	BossType      uint32 // 世界BOSS类型
}

type WorldbossInfoManager struct {
	xmlData  *XmlData
	Datas    []*WorldbossInfoExt
	infos    map[int64]*WorldbossInfoExt
	TypeSort map[uint32][]*WorldbossInfoExt
}

func newWorldbossInfoManager(xmlData *XmlData) *WorldbossInfoManager {
	m := &WorldbossInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *WorldbossInfoManager) name() string {
	return "WorldbossInfo"
}

func (m *WorldbossInfoManager) ignoreForCheck() bool {
	return false
}

func (m *WorldbossInfoManager) checkData() error {
	return nil
}

func (m *WorldbossInfoManager) load(dir string, isShow bool) error {
	tmp := &WorldbossInfos{}
	fileName := filepath.Join(dir, "worldboss_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = m.Datas[:0]
	m.infos = make(map[int64]*WorldbossInfoExt)
	m.TypeSort = make(map[uint32][]*WorldbossInfoExt)

	lastID := uint32(0)
	for _, data := range tmp.Datas {
		data.prepare()
		if lastID >= data.Id {
			panic(fmt.Sprintf("load config %s fail: id not incremental. id: %d", fileName, data.Id))
		}
		lastID = data.Id

		dataExt := &WorldbossInfoExt{
			Id:            data.Id,
			SustainTime:   int64(data.SustainDay) * util.DaySecs,
			NormalBoss:    data.DifficultyNormal,
			HardBoss:      data.DifficultyHard,
			NightmareBoss: data.DifficultyNightmare,
			BossType:      data.BossType,
		}
		stamp, err := time.ParseInLocation(TIME_LAYOUT, strings.Trim(data.Date, " "), time.Local)
		if err != nil {
			panic(fmt.Sprintf("load config %s fail: start time trans failed. %s", fileName, err))
		}

		dataExt.StartTime = stamp.Unix()
		dataExt.EndTime = dataExt.StartTime + int64(data.SustainDay)*util.DaySecs

		for _, v := range m.Datas {
			//验证时间区间
			if dataExt.StartTime >= v.EndTime || dataExt.EndTime <= v.StartTime {
				continue
			}

			panic(fmt.Sprintf("WorldbossInfoManager.checkTimeOverlap: failed, data:%d and data:%d time overlap",
				dataExt.Id, v.Id))
		}

		/*if lastEndTime >= dataExt.EndTime+m.xmlData.WorldbossConfigInfoM.ShowTimeSec {
			panic(fmt.Sprintf("load config %s fail: start time config invalid. id:%d", fileName, dataExt.Id))
		}
		lastEndTime = dataExt.EndTime + m.xmlData.WorldbossConfigInfoM.ShowTimeSec*/

		m.Datas = append(m.Datas, dataExt)
		m.TypeSort[data.BossType] = append(m.TypeSort[data.BossType], dataExt)
		lens := len(m.TypeSort[data.BossType])
		if lens > 1 {
			last := m.TypeSort[data.BossType][lens-1]
			if last.EndTime >= dataExt.EndTime+m.xmlData.WorldbossConfigInfoM.ShowTimeSec {
				panic(fmt.Sprintf("load config %s fail: start time config invalid. id:%d", fileName, dataExt.Id))
			}
		}

		m.infos[dataExt.StartTime] = dataExt
	}
	sort.Slice(m.Datas, func(i, j int) bool {
		if m.Datas[i].StartTime < m.Datas[j].StartTime {
			return true
		}
		return false
	})
	return nil
}

func (m *WorldbossInfoManager) GetRecordById(id uint32) *WorldbossInfoExt {
	return m.Datas[id]
}

func (m *WorldbossInfoManager) Index(id uint32) *WorldbossInfoExt {
	for _, info := range m.Datas {
		if id == info.Id {
			return info
		}
	}

	return nil
}

func (m *WorldbossInfoManager) GetOpenWorldBoss(now int64) *WorldbossInfoExt {
	for _, info := range m.Datas {
		if now >= info.StartTime && now < info.EndTime {
			return info
		}
	}

	return nil
}

func (m *WorldbossInfoManager) IsOpen(sysID uint32) bool {
	for _, info := range m.Datas {
		if info.Id == sysID {
			now := seaTime.Now().Unix()
			if now >= info.StartTime && now < info.EndTime {
				return true
			}
		}
	}

	return false
}

func (m *WorldbossInfoManager) GetBossID(id, level uint32) uint32 {
	info := m.Index(id)
	if info == nil {
		return 0
	}

	switch level {
	case uint32(common.WORLD_BOSS_LEVEL_WBL_NORMAL):
		return info.NormalBoss
	case uint32(common.WORLD_BOSS_LEVEL_WBL_HARD):
		return info.HardBoss
	case uint32(common.WORLD_BOSS_LEVEL_WBL_NIGHT_MIRE):
		return info.NightmareBoss
	}

	return 0
}

// GetLastExpireWorldBoss 获取最近过期的worldBoss
func (m *WorldbossInfoManager) GetLastExpireWorldBoss(now int64) *WorldbossInfoExt {
	result := make([]*WorldbossInfoExt, 0, len(m.Datas))
	for _, info := range m.Datas {
		if now >= info.EndTime {
			result = append(result, info)
		}
	}
	length := len(result)
	if length > 0 {
		return result[length-1]
	}

	return nil
}

func (m *WorldbossInfoManager) CheckExistByTime(timestamp int64) bool {
	if _, exist := m.infos[timestamp]; exist {
		return true
	}

	return false
}

func (m *WorldbossInfoManager) GetInfoByTime(timestamp int64) *WorldbossInfoExt {
	return m.infos[timestamp]
}

func (m *WorldbossInfoManager) GetNextBossInfo(id uint32) *WorldbossInfoExt {
	var findIndex int
	for index, data := range m.Datas {
		if data.Id == id {
			findIndex = index
		}
	}
	dataLens := len(m.Datas)
	if findIndex >= dataLens {
		return nil
	}

	return m.Datas[findIndex+1]
}
