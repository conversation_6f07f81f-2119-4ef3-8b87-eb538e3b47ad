package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityMonthInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ActivityMonthInfo
}

func newActivityMonthInfoManager(xmlData *XmlData) *ActivityMonthInfoManager {
	m := &ActivityMonthInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityMonthInfoManager) name() string {
	return "ActivityMonthInfo"
}

func (m *ActivityMonthInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityMonthInfoManager) checkData() error {
	return nil
}

func (m *ActivityMonthInfoManager) load(dir string, isShow bool) error {
	tmp := &ActivityMonthInfos{}
	fileName := filepath.Join(dir, "activity_month_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ActivityMonthInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ActivityMonthInfoManager) GetRecordById(id uint32) *ActivityMonthInfo {
	return m.Datas[id]
}

func (m *ActivityMonthInfoManager) Index(key uint32) *ActivityMonthInfo {
	return m.Datas[key]
}

func (r *ActivityMonthInfo) GetDailyAwards() []*cl.Resource {
	return r.DayClRes
}

func (r *ActivityMonthInfo) GetReplaceFinalAwardDay() uint32 {
	return r.RewardChangeDay
}

func (r *ActivityMonthInfo) GetOpenDay() uint32 {
	return r.OpenDay
}

func (r *ActivityMonthInfo) GetFuncID() uint32 {
	return r.FunctionId
}

func (m *ActivityMonthInfoManager) GetReplaceAwardDay(id uint32) uint32 {
	info := m.Index(id)
	if info == nil {
		return 0
	}
	return info.RewardChangeDay
}
