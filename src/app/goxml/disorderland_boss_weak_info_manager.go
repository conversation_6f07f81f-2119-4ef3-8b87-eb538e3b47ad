package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type DisorderlandBossWeakInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32][]*DisorderlandBossWeakInfo
}

func newDisorderlandBossWeakInfoManager(xmlData *XmlData) *DisorderlandBossWeakInfoManager {
	m := &DisorderlandBossWeakInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DisorderlandBossWeakInfoManager) name() string {
	return "disorderland_boss_weak_info.xml"
}

func (m *DisorderlandBossWeakInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DisorderlandBossWeakInfoManager) checkData() error {
	return nil
}

func (m *DisorderlandBossWeakInfoManager) load(dir string, show bool) error {
	tmp := &DisorderlandBossWeakInfos{}
	fileName := filepath.Join(dir, "disorderland_boss_weak_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32][]*DisorderlandBossWeakInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Skill == 0 {
			panic(fmt.Sprintf("load config %s fail: skill is zero. id: %d", fileName, data.Id))
		}

		m.Datas[data.Level] = append(m.Datas[data.Level], data)
	}
	return nil
}

func (m *DisorderlandBossWeakInfoManager) GetMaxBySeasonDay(hurdleLevel, seasonDay uint32) *DisorderlandBossWeakInfo {
	pool := m.Datas[hurdleLevel]
	if len(pool) == 0 {
		return nil
	}
	var max *DisorderlandBossWeakInfo
	for _, v := range pool {
		if seasonDay < v.SeasonDay {
			continue
		}
		if max == nil {
			max = v
		} else {
			if v.SeasonDay >= max.SeasonDay {
				max = v
			}
		}
	}

	return max
}
