package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityStoryDungeonInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ActivityStoryDungeonInfo
}

func newActivityStoryDungeonInfoManager(xmlData *XmlData) *ActivityStoryDungeonInfoManager {
	m := &ActivityStoryDungeonInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityStoryDungeonInfoManager) name() string {
	return "ActivityStoryDungeonInfo"
}

func (m *ActivityStoryDungeonInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityStoryDungeonInfoManager) checkData() error {
	return nil
}

func (m *ActivityStoryDungeonInfoManager) load(dir string, isShow bool) error {
	tmp := &ActivityStoryDungeonInfos{}
	fileName := filepath.Join(dir, "activity_story_dungeon_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ActivityStoryDungeonInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		formationInfo := m.xmlData.FormationInfoM.Index(data.Formation)
		if formationInfo == nil {
			panic(fmt.Sprintf("file name:%s data id:%d formation id:%d error", fileName, data.Id, data.Formation))
		}
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ActivityStoryDungeonInfoManager) GetRecordById(id uint32) *ActivityStoryDungeonInfo {
	return m.Datas[id]
}

func (m *ActivityStoryDungeonInfoManager) Index(key uint32) *ActivityStoryDungeonInfo {
	return m.Datas[key]
}

func (a *ActivityStoryDungeonInfo) GetCostResource(count uint32) []*cl.Resource {
	var ret []*cl.Resource
	for _, v := range a.CostClRes {
		if v == nil {
			continue
		}
		res := v.Clone()
		res.Count *= count
		ret = append(ret, res)
	}
	return ret
}

func (a *ActivityStoryDungeonInfo) GetQuickAward(count uint32) []*cl.Resource {
	var ret []*cl.Resource
	for _, v := range a.QuickClRes {
		if v == nil {
			continue
		}
		res := v.Clone()
		res.Count *= count
		ret = append(ret, res)
	}
	return ret
}
