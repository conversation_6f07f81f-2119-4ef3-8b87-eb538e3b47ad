package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type HeroDataInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]map[uint32]*HeroDataInfo
	MergeAttrs  map[uint32]map[uint32]map[uint32]int64
	MergeSkills map[uint32]map[uint32][]uint64
	lastStage   map[uint32]uint32
}

func newHeroDataInfoManager(xmlData *XmlData) *HeroDataInfoManager {
	m := &HeroDataInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HeroDataInfoManager) name() string {
	return "HeroDataInfo"
}

func (m *HeroDataInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HeroDataInfoManager) checkData() error {
	return nil
}

func (m *HeroDataInfoManager) load(dir string, isShow bool) error {
	tmp := &HeroDataInfos{}
	fileName := filepath.Join(dir, "hero_data_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]map[uint32]*HeroDataInfo, len(tmp.Datas))
	}
	m.MergeAttrs = make(map[uint32]map[uint32]map[uint32]int64, len(tmp.Datas))
	m.MergeSkills = make(map[uint32]map[uint32][]uint64, len(tmp.Datas))
	m.lastStage = make(map[uint32]uint32)

	for _, data := range tmp.Datas {
		if data.Stage > m.xmlData.HeroStageInfoM.GetDefaultStage() {
			if m.lastStage[data.Hero]+1 != data.Stage {
				panic(fmt.Sprintf("load config %s, stage discontinuity. id:%d", fileName, data.Id))
			}
		}
		m.lastStage[data.Hero] = data.Stage

		if ptr, exist := m.Datas[data.Hero][data.Stage]; exist {
			*ptr = *data
			data = ptr
		} else {
			if m.Datas[data.Hero] == nil {
				m.Datas[data.Hero] = make(map[uint32]*HeroDataInfo)
			}
			m.Datas[data.Hero][data.Stage] = data
		}

		//累计属性
		if m.MergeAttrs[data.Hero] == nil {
			m.MergeAttrs[data.Hero] = make(map[uint32]map[uint32]int64)
		}
		m.MergeAttrs[data.Hero][data.Stage] = make(map[uint32]int64, HeroStageAttrNum)
		if data.AttrType1 > 0 && data.AttrType1 < uint32(AttrMaxNum) && data.AttrValue1 > 0 {
			m.MergeAttrs[data.Hero][data.Stage][data.AttrType1] = int64(data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrType2 < uint32(AttrMaxNum) && data.AttrValue2 > 0 {
			m.MergeAttrs[data.Hero][data.Stage][data.AttrType2] += int64(data.AttrValue2)
		}
		if data.AttrType3 > 0 && data.AttrType3 < uint32(AttrMaxNum) && data.AttrValue3 > 0 {
			m.MergeAttrs[data.Hero][data.Stage][data.AttrType3] += int64(data.AttrValue3)
		}
		if data.Stage > m.xmlData.HeroStageInfoM.GetDefaultStage() {
			for k, v := range m.MergeAttrs[data.Hero][data.Stage-1] {
				m.MergeAttrs[data.Hero][data.Stage][k] += v
			}
		}

		//累计技能
		if m.MergeSkills[data.Hero] == nil {
			m.MergeSkills[data.Hero] = make(map[uint32][]uint64)
		}
		m.MergeSkills[data.Hero][data.Stage] = make([]uint64, 0, HeroStageSkillNum)
		if data.Skill > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill1))
			}
			m.MergeSkills[data.Hero][data.Stage] = append(m.MergeSkills[data.Hero][data.Stage], raisePSInfo.ID)
		}
		if data.Stage > m.xmlData.HeroStageInfoM.GetDefaultStage() && len(m.MergeSkills[data.Hero][data.Stage-1]) > 0 {
			m.MergeSkills[data.Hero][data.Stage] = append(m.MergeSkills[data.Hero][data.Stage], m.MergeSkills[data.Hero][data.Stage-1]...)
		}
	}
	return nil
}

func (m *HeroDataInfoManager) Index(sysId, stage uint32) *HeroDataInfo {
	return m.Datas[sysId][stage]
}

func (m *HeroDataInfoManager) GetStageAttrs(sysId, stage uint32) map[uint32]int64 {
	return m.MergeAttrs[sysId][stage]
}

func (m *HeroDataInfoManager) GetStageSkills(sysId, stage uint32) []uint64 {
	return m.MergeSkills[sysId][stage]
}
