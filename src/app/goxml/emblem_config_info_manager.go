package goxml

import (
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

// todo panic
type EmblemConfigInfoManager struct {
	xmlData                    *XmlData
	Datas                      map[string]*EmblemConfigInfo
	link4UnlockCondition       uint32
	heroSkillUnlockLabel       uint32
	heroSkillUnlockRare        uint32
	heroSkillLVUpLabel         uint32
	heroSkillLVUpRare          uint32
	emblemSuitCondition1       uint32
	emblemSuitCondition2       uint32
	emblemCustomize            uint32
	HeroSkillUnlockRare1       uint32
	succinctHaveAffixProOrange uint32 // 史诗（橙色）符文洗炼产生词缀的万分比
	succinctHaveAffixProRed    uint32 // 传说（红色）符文洗炼产生词缀的万分比
	succinctAffixWeight1       uint32 // 洗炼产生词缀，远古词缀的权重
	succinctAffixWeight2       uint32 // 洗炼产生词缀，太古词缀的权重
	succinctMagicSkillOrange   uint32 // 洗炼多少次保底橙色词条
	succinctMagicSkillRed      uint32 // 洗炼多少次保底红色词条
	succinctAffixOrange        uint32 // 洗炼多少次保底远古词缀
	succinctAffixRed           uint32 // 洗炼多少次保底太古词缀
}

func newEmblemConfigInfoManager(xmlData *XmlData) *EmblemConfigInfoManager {
	m := &EmblemConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *EmblemConfigInfoManager) name() string {
	return "EmblemConfigInfo"
}

func (m *EmblemConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *EmblemConfigInfoManager) checkData() error {
	return nil
}

func (m *EmblemConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &EmblemConfigInfos{}
	fileName := filepath.Join(dir, "emblem_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[string]*EmblemConfigInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Key]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Key] = data
		}
	}

	m.link4UnlockCondition = m.Datas["LINK4_UNLOCK_CONDITION"].Count
	m.heroSkillUnlockLabel = m.Datas["HERO_SKILL_UNLOCK_LABEL"].Count
	if m.heroSkillUnlockLabel != EmblemPosMax {
		panic("HERO_SKILL_UNLOCK_LABEL must be 4")
	}
	m.heroSkillUnlockRare = m.Datas["HERO_SKILL_UNLOCK_RARE"].Count
	m.heroSkillLVUpLabel = m.Datas["HERO_SKILL_LVUP_LABEL"].Count
	if m.heroSkillLVUpLabel != EmblemPosMax {
		panic("HERO_SKILL_LVUP_LABEL must be 4")
	}
	m.heroSkillLVUpRare = m.Datas["HERO_SKILL_LVUP_RARE"].Count
	if m.heroSkillUnlockRare > m.heroSkillLVUpRare {
		panic("")
	}
	m.emblemSuitCondition1 = m.Datas["EMBLEM_SUIT_CONDITION_1"].Count
	m.emblemSuitCondition2 = m.Datas["EMBLEM_SUIT_CONDITION_2"].Count
	if m.emblemSuitCondition2 != EmblemPosMax {
		panic("EMBLEM_SUIT_CONDITION_2 msut be 4")
	}
	if m.emblemSuitCondition1 > m.emblemSuitCondition2 {
		panic(fmt.Sprintf("EMBLEM_SUIT_CONDITION_1:%d must less EMBLEM_SUIT_CONDITION_2:%d", m.emblemSuitCondition1, m.emblemSuitCondition2))
	}
	m.emblemCustomize = m.Datas["emblem_customize"].Count
	m.HeroSkillUnlockRare1 = m.Datas["HERO_SKILL_UNLOCK_RARE_1"].Count
	m.succinctHaveAffixProOrange = m.Datas["SUCCINCT_PRO_ORANGE"].Count
	m.succinctHaveAffixProRed = m.Datas["SUCCINCT_PRO_RED"].Count
	m.succinctAffixWeight1 = m.Datas["SUCCINCT_AFFIX_WEIGHT_1"].Count
	m.succinctAffixWeight2 = m.Datas["SUCCINCT_AFFIX_WEIGHT_2"].Count
	m.succinctMagicSkillOrange = m.Datas["SUCCINCT_MAGIC_ORANGE"].Count
	m.succinctMagicSkillRed = m.Datas["SUCCINCT_MAGIC_RED"].Count
	m.succinctAffixOrange = m.Datas["SUCCINCT_AFFIX_ORANGE"].Count
	m.succinctAffixRed = m.Datas["SUCCINCT_AFFIX_RED"].Count
	if m.succinctHaveAffixProOrange == 0 || m.succinctHaveAffixProRed == 0 || m.succinctAffixWeight1 == 0 || m.succinctAffixWeight2 == 0 ||
		m.succinctMagicSkillOrange == 0 || m.succinctMagicSkillRed == 0 || m.succinctAffixOrange == 0 || m.succinctAffixRed == 0 {
		panic(fmt.Sprintf("data check error: succinct config is 0. fileName:%s ", fileName))
	}
	return nil
}

func (m *EmblemConfigInfoManager) Index(key string) *EmblemConfigInfo {
	return m.Datas[key]
}

func (m *EmblemConfigInfoManager) GetLink4UnlockCondition() uint32 {
	return m.link4UnlockCondition
}

func (m *EmblemConfigInfoManager) GetHeroSkillUnLockLabel() uint32 {
	return m.heroSkillUnlockLabel
}

func (m *EmblemConfigInfoManager) GetHeroSkillUnlockRare() uint32 {
	return m.heroSkillUnlockRare
}

func (m *EmblemConfigInfoManager) GetHeroSkillLVUpLabel() uint32 {
	return m.heroSkillLVUpLabel
}

func (m *EmblemConfigInfoManager) GetHeroSkillLVUpRare() uint32 {
	return m.heroSkillLVUpRare
}

func (m *EmblemConfigInfoManager) GetEmblemSuitCondition1() uint32 {
	return m.emblemSuitCondition1
}

func (m *EmblemConfigInfoManager) GetEmblemSuitCondition2() uint32 {
	return m.emblemSuitCondition2
}

func (m *EmblemConfigInfoManager) GetEmblemCustomize() uint32 {
	return m.emblemCustomize
}

func (m *EmblemConfigInfoManager) GetHeroSkillUnlockRare1() uint32 {
	return m.HeroSkillUnlockRare1
}

// GetSuccinctHaveAffixPro
// @Description: 获取洗炼产生词缀的万分比
// @receiver m
// @return uint32
func (m *EmblemConfigInfoManager) GetSuccinctHaveAffixPro(rare uint32) uint32 {
	if rare == uint32(common.QUALITY_ORANGE) {
		return m.succinctHaveAffixProOrange
	}
	if rare == uint32(common.QUALITY_RED) {
		return m.succinctHaveAffixProRed
	}
	return 0
}

// GetSuccinctAffixWeight1
// @Description: 洗炼产生远古词缀的权重
// @receiver m
// @return uint32
func (m *EmblemConfigInfoManager) GetSuccinctAffixWeight1() uint32 {
	return m.succinctAffixWeight1
}

// GetSuccinctAffixWeight2
// @Description: 洗炼产生太古词缀的权重
// @receiver m
// @return uint32
func (m *EmblemConfigInfoManager) GetSuccinctAffixWeight2() uint32 {
	return m.succinctAffixWeight2
}

// GetSuccinctCountGuaranteeOrangeSkill
// @Description: 获取洗炼多少次保底远古词缀
// @receiver m
// @return uint32
func (m *EmblemConfigInfoManager) GetSuccinctCountGuaranteeOrangeSkill() uint32 {
	return m.succinctMagicSkillOrange
}

// GetSuccinctCountGuaranteeRedSkill
// @Description: 获取洗炼多少次保底太古词缀
// @receiver m
// @return uint32
func (m *EmblemConfigInfoManager) GetSuccinctCountGuaranteeRedSkill() uint32 {
	return m.succinctMagicSkillRed
}

// GetSuccinctCountGuaranteeAffixOrange
// @Description: 获取洗炼多少次保底远古词缀
// @receiver m
// @return uint32
func (m *EmblemConfigInfoManager) GetSuccinctCountGuaranteeAffixOrange() uint32 {
	return m.succinctAffixOrange
}

// GetSuccinctCountGuaranteeAffixRed
// @Description: 获取洗炼多少次保底太古词缀
// @receiver m
// @return uint32
func (m *EmblemConfigInfoManager) GetSuccinctCountGuaranteeAffixRed() uint32 {
	return m.succinctAffixRed
}
