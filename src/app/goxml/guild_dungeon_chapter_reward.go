package goxml

//import "fmt"
import "app/protos/out/cl"

//import "gitlab.qdream.com/kit/sea/util"

//import l4g "github.com/ivanabc/log4go"

// Reference imports to suppress errors if they are not otherwise used.
type _ = cl.Resource

type GuildDungeonChapterReward struct {
	Id        uint32 `xml:"id,attr"`         //int:ID
	AwardType uint32 `xml:"award_type,attr"` //int:奖池类型
	Type      uint32 `xml:"type,attr"`       //int:奖励类型
	Value     uint32 `xml:"value,attr"`      //int:奖励类型值
	Count     uint32 `xml:"count,attr"`      //int:奖励数量
	Num       uint32 `xml:"num,attr"`        //int:份数
	Rare      uint32 `xml:"rare,attr"`       //int:是否是珍稀物品

	// type value count
	ClRes []*cl.Resource `xml:"-"` //非xml原始字段
}

func (m *GuildDungeonChapterReward) prepare() {
	// 资源
	if m.Type > 0 && m.Count > 0 {
		m.ClRes = append(m.ClRes, &cl.Resource{
			Type:  m.Type,
			Value: m.Value,
			Count: m.Count,
		})
	}
}

/*
func (m *GuildDungeonChapterReward) Check() error {
return nil
}
*/
type GuildDungeonChapterRewards struct {
	Datas []*GuildDungeonChapterReward `xml:"data"`
}

/*
type GuildDungeonChapterRewardManager struct {
xmlData *XmlData
Datas map[uint32]*GuildDungeonChapterReward
}

func newGuildDungeonChapterRewardManager(xmlData *XmlData) *GuildDungeonChapterRewardManager {
m := &GuildDungeonChapterRewardManager{
xmlData: xmlData,
}
xmlData.addManager(m)
return m
}

func (m *GuildDungeonChapterRewardManager) name() string {
return"guild_dungeon_chapter_reward.xml"
}

func (m *GuildDungeonChapterRewardManager) ignoreForCheck() bool {
return false
}

func (m *GuildDungeonChapterRewardManager) checkData() error {
return nil
}

func (m *GuildDungeonChapterRewardManager)load(dir string, show bool) error {
tmp := &GuildDungeonChapterRewards{}
fileName := filepath.Join(dir, "guild_dungeon_chapter_reward.xml")
if err := util.LoadConfig(fileName, tmp); err != nil {
panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
} else if show {
for _, data := range tmp.Datas {
l4g.Debug("config(%s): %+v", fileName, data)
}
}
if m.Datas == nil {
m.Datas = make(map[uint32]*GuildDungeonChapterReward, len(tmp.Datas))
}
for _, data := range tmp.Datas {
data.prepare()
if ptr, exist := m.Datas[data.Id]; exist {
*ptr = *data
 data = ptr
 } else {
m.Datas[data.Id] = data
}
}
return nil
}

func (m *GuildDungeonChapterRewardManager) Index(key uint32) *GuildDungeonChapterReward{
return m.Datas[key]
}
*/
