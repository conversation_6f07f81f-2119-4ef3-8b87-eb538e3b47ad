package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type MonsterInfo7Manager struct {
	xmlData *XmlData
}

func newMonsterInfo7Manager(xmlData *XmlData) *MonsterInfo7Manager {
	m := &MonsterInfo7Manager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MonsterInfo7Manager) name() string {
	return "monster_info_7.xml"
}

func (m *MonsterInfo7Manager) ignoreForCheck() bool {
	return false
}

func (m *MonsterInfo7Manager) checkData() error {
	return nil
}

func (m *MonsterInfo7Manager) load(dir string, isShow bool) error {
	tmp := &MonsterInfo7s{}
	fileName := filepath.Join(dir, "monster_info_7.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	for _, data := range tmp.Datas {
		data.prepare()
		monster := m.convert(data)
		m.xmlData.MonsterInfoM.Add(fileName, monster)
	}
	return nil
}

func (m *MonsterInfo7Manager) convert(data *MonsterInfo7) *MonsterInfo {
	return &MonsterInfo{
		Id:            data.Id,
		HeroId:        data.HeroId,
		Type:          data.Type,
		IsElite:       data.IsElite,
		Level:         data.Level,
		Star:          data.Star,
		Stage:         data.Stage,
		NeedNotKill:   data.NeedNotKill,
		PublicHp:      data.PublicHp,
		EtherBind:     data.EtherBind,
		SkillLevel:    data.SkillLevel,
		FuncType:      data.FuncType,
		DataType:      data.DataType,
		DataLevel:     data.DataLevel,
		AttackPct:     data.AttackPct,
		HpPct:         data.HpPct,
		DefPct:        data.DefPct,
		SpeedPct:      data.SpeedPct,
		PassiveSkill1: data.PassiveSkill1,
		PassiveSkill2: data.PassiveSkill2,
		PassiveSkill3: data.PassiveSkill3,
		PassiveSkill4: data.PassiveSkill4,
		PassiveSkill5: data.PassiveSkill5,
		PassiveSkill6: data.PassiveSkill6,
		PassiveSkill7: data.PassiveSkill7,
		PassiveSkill8: data.PassiveSkill8,
	}
}
