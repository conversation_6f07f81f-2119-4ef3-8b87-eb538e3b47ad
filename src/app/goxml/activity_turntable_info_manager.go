package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"
	"time"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type ActivityTurntableInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]*ActivityTurntableInfoExt
	TypeData map[uint32][]*ActivityTurntableInfoExt
}

func newActivityTurntableInfoManager(xmlData *XmlData) *ActivityTurntableInfoManager {
	m := &ActivityTurntableInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

type ActivityTurntableInfoExt struct {
	ID               uint32 `xml:"round,attr"`              //int:轮次
	FunctionId       uint32 `xml:"function_id,attr"`        //int:开启条件id
	OpenTime         int64  `xml:"open_day,attr"`           //string:开启时间
	EndTime          int64  `xml:"end_day,attr"`            //string:活动结束时间
	CloseServerTime  int64  `xml:"close_server_date,attr"`  //string:服务器开始时间在某个时间点之后的关闭活动
	CloseServerTime2 int64  `xml:"close_server_date2,attr"` //string:服务器开始时间在某个时间点之前的关闭活动
	PassId           uint32 `xml:"pass_id,attr"`            //int:战令
	LoginId          uint32 `xml:"login_id,attr"`           //int:登录轮次匹配
	TicketType       uint32 `xml:"ticket_type,attr"`        //int:转盘券类型
	TicketValue      uint32 `xml:"ticket_value,attr"`       //int:转盘券值
	TicketCount      uint32 `xml:"ticket_count,attr"`       //int:初始转盘券数量
	SkinRechargeId   uint32 `xml:"skin_recharge_id,attr"`   //int:皮肤商店id
	MailId           uint32 `xml:"mail_id,attr"`            //int:结算邮件
	Type             uint32 `xml:"type,attr"`               //int:活动类型
	BuyID            uint32 `xml:"buy_id,attr"`             //int:购买门票次数
	UnlockDay        uint32 `xml:"unlock_day,attr"`         //int:购买解锁日期
	StoryShop        uint32 `xml:"story_shop,attr"`         //int:兑换商店类型
	RechargeShop     uint32 `xml:"recharge_shop,attr"`      //int:充值商店类型
	DropActivity     uint32 `xml:"drop_activity,attr"`      //int:掉落活动ID
	Name             uint32 `xml:"name,attr"`

	TicketClRes        []*cl.Resource
	RecycleTicketClRes []*cl.Resource
}

type ActivityNameCheck struct {
	OpenTime       int64
	EndTime        int64
	SeparationTime int64
}

func (m *ActivityTurntableInfoManager) name() string {
	return "activity_turntable_info.xml"
}

func (m *ActivityTurntableInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityTurntableInfoManager) checkData() error {
	return nil
}

func (m *ActivityTurntableInfoManager) load(dir string, show bool) error {
	tmp := &ActivityTurntableInfos{}
	fileName := filepath.Join(dir, "activity_turntable_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*ActivityTurntableInfoExt, len(tmp.Datas))
	m.TypeData = make(map[uint32][]*ActivityTurntableInfoExt)
	nameChecks := make(map[uint32]*ActivityNameCheck)
	for _, data := range tmp.Datas {
		data.prepare()
		closeServerUnix := int64(0)
		if data.CloseServerDate != "" {
			tm, err := time.ParseInLocation(TIME_LAYOUT, data.CloseServerDate, time.Local)
			if err != nil {
				panic(fmt.Sprintf("activity_turntable_info data:%d close server date failed: %v err:%s", data.Id, data.CloseServerDate, err))
			}
			closeServerUnix = tm.Unix()
		}

		closeServerUnix2 := int64(0)
		if data.CloseServerDate2 != "" {
			tm, err := time.ParseInLocation(TIME_LAYOUT, data.CloseServerDate2, time.Local)
			if err != nil {
				panic(fmt.Sprintf("activity_turntable_info data:%d close server date 2 failed: %v err:%s", data.Id, data.CloseServerDate2, err))
			}
			closeServerUnix2 = tm.Unix()
		}

		openTime, err := time.ParseInLocation(TIME_LAYOUT, data.OpenDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("activity_turntable_info data:%d openTime failed: %v err:%s", data.Id, data.OpenDay, err))
		}
		openUnix := openTime.Unix()

		endTime, err := time.ParseInLocation(TIME_LAYOUT, data.EndDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("activity_turntable_info data:%d endTime failed: %v err:%s", data.Id, data.EndDay, err))
		}
		endUnix := endTime.Unix()

		if openUnix > endUnix {
			panic(fmt.Sprintf("activity_turntable_info data:%d openTime:%d more than endTime:%d", data.Id, openUnix, endUnix))
		}

		separationTime := int64(0)
		if closeServerUnix > 0 {
			separationTime = closeServerUnix
		}
		if closeServerUnix2 > 0 {
			if separationTime != 0 {
				panic(fmt.Sprintf("activity_turntable_info data:%d CloseServerDate CloseServerDate2 add two time", data.Id))
			}
			separationTime = closeServerUnix2
		}

		nameCheck, exist := nameChecks[data.Name]
		if !exist {
			nameChecks[data.Name] = &ActivityNameCheck{
				OpenTime:       openUnix,
				EndTime:        endUnix,
				SeparationTime: separationTime,
			}
		} else {
			if nameCheck.OpenTime != openUnix || nameCheck.EndTime != endUnix || nameCheck.SeparationTime != separationTime {
				panic(fmt.Sprintf("activity_turntable_info data:%d name:%d check failed", data.Id, data.Name))
			}
		}

		ext := &ActivityTurntableInfoExt{
			ID:                 data.Id,
			FunctionId:         data.FunctionId,
			PassId:             data.PassId,
			LoginId:            data.LoginId,
			OpenTime:           openUnix,
			EndTime:            endUnix,
			CloseServerTime:    closeServerUnix,
			CloseServerTime2:   closeServerUnix2,
			TicketType:         data.TicketType,
			TicketValue:        data.TicketValue,
			TicketCount:        data.TicketCount,
			SkinRechargeId:     data.SkinRechargeId,
			MailId:             data.MailId,
			TicketClRes:        data.TicketClRes,
			RecycleTicketClRes: data.RecycleTicketClRes,
			Type:               data.Type,
			BuyID:              data.BuyId,
			UnlockDay:          data.UnlockDay,
			StoryShop:          data.StoryShopId,
			RechargeShop:       data.RechargeShopId,
			DropActivity:       data.DropActivityId,
			Name:               data.Name,
		}

		_, exist = m.TypeData[data.Type]
		if !exist {
			m.TypeData[data.Type] = []*ActivityTurntableInfoExt{ext}
		} else {
			lens := len(m.TypeData[data.Type])
			info := m.TypeData[data.Type][lens-1]
			if info.Name == ext.Name {
				if info.EndTime != ext.EndTime || info.OpenTime != ext.OpenTime {
					panic(fmt.Sprintf("activity_turntable_info data:%d type:%d EndTime:%d or OpenTime:%d error", ext.ID, ext.Type, ext.EndTime, ext.OpenTime))
				}
			} else {
				if info.EndTime > ext.OpenTime {
					panic(fmt.Sprintf("activity_turntable_info data:%d type:%d time must be increase", ext.ID, ext.Type))
				}
			}
			m.TypeData[data.Type] = append(m.TypeData[data.Type], ext)
		}

		if ptr, exist := m.Datas[ext.ID]; exist {
			*ptr = *ext
			ext = ptr
		} else {
			m.Datas[data.Id] = ext
		}
	}
	return nil
}

func (m *ActivityTurntableInfoManager) Index(key uint32) *ActivityTurntableInfoExt {
	return m.Datas[key]
}

func (m *ActivityTurntableInfoManager) Current(actType uint32, now, srvOpenTime int64) *ActivityTurntableInfoExt {
	for _, info := range m.TypeData[actType] {
		if info == nil {
			continue
		}
		if info.CloseServerTime > 0 && info.CloseServerTime <= srvOpenTime {
			continue
		}
		if info.CloseServerTime2 > 0 && info.CloseServerTime2 > srvOpenTime {
			continue
		}
		if info.IsOpen(now) {
			return info
		}
	}
	return nil
}

// 这个只判断活动是否开启,不判断服务器是否应该开放,是否应该开放放在Current里
func (m *ActivityTurntableInfoExt) IsOpen(now int64) bool {
	return now >= m.OpenTime && now < m.EndTime
}

func (m *ActivityTurntableInfoManager) GetTypeData(actType uint32) []*ActivityTurntableInfoExt {
	return m.TypeData[actType]
}
