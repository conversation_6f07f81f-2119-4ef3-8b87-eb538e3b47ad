package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

const WorldBossRoomPlayerNum = 50

type WorldbossConfigInfoManager struct {
	xmlData              *XmlData
	UnlockDay            uint32         // 开服第x天解锁
	SustainTimeSec       int64          // 持续时间(单位：秒)
	ShowTimeSec          int64          // 展示天数(单位：秒)
	RoomPlayerNumLimit   uint32         // 房间人数限制
	InitialFightCount    uint32         // 初始挑战次数
	EachXMinuteSec       uint32         // 每X分钟恢复1次挑战次数(单位：秒)
	FightCountLimit      uint32         // 战斗次数上限
	WorshipDropGroup     uint32         // 膜拜掉落奖励组
	FightCostRes         []*cl.Resource // 挑战券
	ArtifactFightCostRes []*cl.Resource // 神器挑战券
	PokemonFightCostRes  []*cl.Resource // 宠物挑战券
	PeakPlayerServerDay  uint32         // 玩家服务器开服时间跟当期世界 boss 开启时间相隔天数 （成为巅峰选手的条件）
}

func newWorldbossConfigInfoManager(xmlData *XmlData) *WorldbossConfigInfoManager {
	m := &WorldbossConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *WorldbossConfigInfoManager) name() string {
	return "WorldbossConfigInfo"
}

func (m *WorldbossConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *WorldbossConfigInfoManager) checkData() error {
	return nil
}

func (m *WorldbossConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &WorldbossConfigInfos{}
	fileName := filepath.Join(dir, "worldboss_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.UnlockDay = 0
	m.SustainTimeSec = 0
	m.ShowTimeSec = 0
	m.RoomPlayerNumLimit = 0
	m.InitialFightCount = 0
	m.EachXMinuteSec = 0
	m.FightCountLimit = 0
	m.WorshipDropGroup = 0
	m.FightCostRes = m.FightCostRes[:0]
	m.PeakPlayerServerDay = 0

	for _, data := range tmp.Datas {
		switch data.Key {
		case "OPEN_SERVER_DAY":
			m.UnlockDay = data.Count
		case "SUSTAIN_DAY":
			if data.Count == 0 {
				panic(fmt.Sprintf("load config %s fail: sustain day is zero. key:%s", fileName, data.Key))
			}
			m.SustainTimeSec = util.DaySecs * int64(data.Count)
		case "SHOW_DAY":
			if data.Count == 0 {
				panic(fmt.Sprintf("load config %s fail: show day is zero. key:%s", fileName, data.Key))
			}
			m.ShowTimeSec = util.DaySecs * int64(data.Count)
		case "ROOM_PLAYER_NUM":
			if data.Count != WorldBossRoomPlayerNum {
				panic(fmt.Sprintf("load config %s fail: room player num is invalid. key:%s", fileName, data.Key))
			}
			m.RoomPlayerNumLimit = data.Count
		case "CHALLENGE_INITIAL":
			m.InitialFightCount = data.Count
		case "CHALLENGE_RESTORE":
			m.EachXMinuteSec = data.Count * 60
		case "CHALLENGE_MAX":
			m.FightCountLimit = data.Count
		case "CHALLENGE_ITEM":
			if data.Type == 0 || data.Value == 0 {
				panic(fmt.Sprintf("load config %s fail: fight cost res is invalid. key:%s", fileName, data.Key))
			}
			m.FightCostRes = append(m.FightCostRes, GenSimpleResource(data.Type, data.Value, 1))
		case "CHALLENGE_ITEM_ARTIFACT":
			if data.Type == 0 || data.Value == 0 {
				panic(fmt.Sprintf("load config %s fail: fight cost res is invalid. key:%s", fileName, data.Key))
			}
			m.ArtifactFightCostRes = append(m.ArtifactFightCostRes, GenSimpleResource(data.Type, data.Value, 1))
		case "CHALLENGE_ITEM_POKEMON":
			if data.Type == 0 || data.Value == 0 {
				panic(fmt.Sprintf("load config %s fail: fight cost res is invalid. key:%s", fileName, data.Key))
			}
			m.PokemonFightCostRes = append(m.PokemonFightCostRes, GenSimpleResource(data.Type, data.Value, 1))
		case "WORSHIP_REWARD":
			if m.xmlData.DropGroupInfoM.Group(data.Count) == nil {
				panic(fmt.Sprintf("load config %s fail: drop group is invalid. key:%s group:%d", fileName, data.Key, data.Count))
			}
			m.WorshipDropGroup = data.Count
		case "PEAK_PLAYER_SEVER_DAY":
			m.PeakPlayerServerDay = data.Count
		default:
			panic(fmt.Sprintf("load config %s fail: invalid key. key:%s", fileName, data.Key))
		}
	}

	if m.PeakPlayerServerDay == 0 {
		panic(fmt.Sprintf("load config %s fail: PeakPlayerServerDay is zero", fileName))
	}
	return nil
}

func (m *WorldbossConfigInfoManager) GetFightResource(bossType uint32) []*cl.Resource {
	switch bossType {
	case WorldBossTypeDefault:
		return m.FightCostRes
	case WorldBossTypeArtifact:
		return m.ArtifactFightCostRes
	case WorldBossTypePokemon:
		return m.PokemonFightCostRes
	default:
		return nil
	}
}
