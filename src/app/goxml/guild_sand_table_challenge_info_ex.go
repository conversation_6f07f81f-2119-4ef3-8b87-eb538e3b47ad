package goxml

type GuildSandTableChallengeInfoMEx struct {
	*GuildSandTableChallengeInfoM
}

func newGuildSandTableChallengeInfoMEx(xmlData *XmlData) *GuildSandTableChallengeInfoMEx {
	m := &GuildSandTableChallengeInfoMEx{
		GuildSandTableChallengeInfoM: &GuildSandTableChallengeInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableChallengeInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableChallengeInfoMEx) load(dir string, isShow bool) error {
	err := m.GuildSandTableChallengeInfoM.load(dir, isShow)
	if err != nil {
		return err
	}

	return nil
}

func (m *GuildSandTableChallengeInfoMEx) GetRecordByGSTRoundAndLRound(round, lRound uint32) *GuildSandTableChallengeInfo {
	infos := m.GetIndexByFightGroup(round)
	if infos == nil {
		return nil
	}
	for _, info := range infos.matchIndexRecordMap {
		if lRound >= info.Start && lRound <= info.End {
			return info
		}
	}
	return nil
}

// 获取当前公会战轮次，最后一场擂台赛结束的回合
func (m *GuildSandTableChallengeInfoMEx) GetCurrentGSTRoundMaxEndLRound(round uint32) uint32 {
	infos := m.GetIndexByFightGroup(round)
	if infos == nil {
		return 0
	}
	maxEnd := uint32(0)
	for _, info := range infos.matchIndexRecordMap {
		if maxEnd < info.End {
			maxEnd = info.End
		}
	}
	return maxEnd
}
