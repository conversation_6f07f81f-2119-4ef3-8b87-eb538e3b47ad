package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type DropActivityOpenInfoManager struct {
	xmlData *XmlData
	Datas   []*DropActivityOpenInfo
	EndDay  uint32
}

func newDropActivityOpenInfoManager(xmlData *XmlData) *DropActivityOpenInfoManager {
	m := &DropActivityOpenInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DropActivityOpenInfoManager) name() string {
	return "drop_activity_open_info.xml"
}

func (m *DropActivityOpenInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DropActivityOpenInfoManager) checkData() error {
	return nil
}

func (m *DropActivityOpenInfoManager) load(dir string, show bool) error {
	tmp := &DropActivityOpenInfos{}
	fileName := filepath.Join(dir, "drop_activity_open_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make([]*DropActivityOpenInfo, 0, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if data.OpenDay == 0 || data.EndDay == 0 || data.OpenDay >= data.EndDay {
			panic(fmt.Sprintf("config(%s): id:%d openday or endday check failed", fileName, data.Id))
		}

		dataL := len(m.Datas)
		if dataL > 0 {
			last := m.Datas[dataL-1]
			if last.EndDay > data.OpenDay {
				panic(fmt.Sprintf("config(%s): id:%d must be increases"))
			}
		}

		m.Datas = append(m.Datas, data)

		if data.EndDay > m.EndDay {
			m.EndDay = data.EndDay
		}
	}
	return nil
}

func (m *DropActivityOpenInfoManager) Index(key uint32) *DropActivityOpenInfo {
	for _, data := range m.Datas {
		if data.Id == key {
			return data
		}
	}
	return nil
}

func (m *DropActivityOpenInfoManager) IsOpen(openDay uint32) bool {
	return openDay < m.EndDay
}

// 寻找当前正在开的和待开的
func (m *DropActivityOpenInfoManager) GetOpenInfos(openDay uint32) []*DropActivityOpenInfo {
	var ret []*DropActivityOpenInfo
	for _, data := range m.Datas {
		if openDay >= data.OpenDay && openDay < data.EndDay {
			ret = append(ret, data)
		}
	}
	return ret
}

// 寻找当前正在开的
func (m *DropActivityOpenInfoManager) GetOpenInfo(openDay uint32) *DropActivityOpenInfo {
	for _, data := range m.Datas {
		if openDay >= data.OpenDay && openDay < data.EndDay {
			return data
		}
	}
	return nil
}

func (d *DropActivityOpenInfo) Convert2Base(serverOpenZero int64) *cl.DropActivityBase {
	return &cl.DropActivityBase{
		Id:         d.Id,
		ActivityId: d.DropActivityId,
		StartTime:  serverOpenZero + (int64(d.OpenDay-1) * util.DaySecs),
		EndTime:    serverOpenZero + (int64(d.EndDay-1) * util.DaySecs) - 1,
		OpStatus:   OpStatusRelease,
		NewServer:  true,
	}
}
