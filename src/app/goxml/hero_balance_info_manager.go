package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type HeroBalanceInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]map[uint32]*HeroBalanceInfoExt
}

type HeroBalanceInfoExt struct {
	*HeroBalanceInfo
	AddBuild map[uint32]uint32
}

func newHeroBalanceInfoManager(xmlData *XmlData) *HeroBalanceInfoManager {
	m := &HeroBalanceInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HeroBalanceInfoManager) name() string {
	return "HeroBalanceInfo"
}

func (m *HeroBalanceInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HeroBalanceInfoManager) checkData() error {
	return nil
}

func (m *HeroBalanceInfoManager) load(dir string, isShow bool) error {
	tmp := &HeroBalanceInfos{}
	fileName := filepath.Join(dir, "hero_balance_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]map[uint32]*HeroBalanceInfoExt, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		heroInfo := m.xmlData.HeroInfoM.Index(data.HeroId)
		if heroInfo == nil {
			panic(fmt.Sprintf("load %s failed. hero Id:%d is nil", fileName, data.HeroId))
		}
		if _, exist := m.Datas[data.HeroId]; !exist {
			m.Datas[data.HeroId] = make(map[uint32]*HeroBalanceInfoExt)
		}

		ext := &HeroBalanceInfoExt{
			HeroBalanceInfo: data,
		}
		buildAdd := make(map[uint32]uint32)
		if data.BuildType1 > 0 && data.AddRate1 > 0 {
			_, exist := buildAdd[data.BuildType1]
			if exist {
				panic(fmt.Sprintf("data: hero id:%d hero start:%d build id:%d is repeated", data.HeroId, data.HeroStar, data.BuildType1))
			}
			buildAdd[data.BuildType1] = data.AddRate1
		}

		if data.BuildType2 > 0 && data.AddRate2 > 0 {
			_, exist := buildAdd[data.BuildType2]
			if exist {
				panic(fmt.Sprintf("data: hero id:%d hero start:%d build id:%d is repeated", data.HeroId, data.HeroStar, data.BuildType2))
			}
			buildAdd[data.BuildType2] = data.AddRate2
		}

		if data.BuildType3 > 0 && data.AddRate3 > 0 {
			_, exist := buildAdd[data.BuildType3]
			if exist {
				panic(fmt.Sprintf("data: hero id:%d hero start:%d build id:%d is repeated", data.HeroId, data.HeroStar, data.BuildType3))
			}
			buildAdd[data.BuildType3] = data.AddRate3
		}
		ext.AddBuild = buildAdd
		m.Datas[data.HeroId][data.HeroStar] = ext
		monsterInfo := m.xmlData.MonsterInfoM.Index(data.MonsterId)
		if monsterInfo == nil {
			panic(fmt.Sprintf("file:%s hero Id:%d monster:%d is nil", fileName, data.HeroId, data.MonsterId))
		}
	}
	m.check(fileName)
	return nil
}

func (m *HeroBalanceInfoManager) Index(heroID, heroStar uint32) *HeroBalanceInfo {
	info, exist := m.Datas[heroID][heroStar]
	if !exist {
		return nil
	}
	return info.HeroBalanceInfo
}

func (m *HeroBalanceInfoManager) IndexExt(heroID, heroStar uint32) *HeroBalanceInfoExt {
	return m.Datas[heroID][heroStar]
}

func (m *HeroBalanceInfoManager) check(filename string) {
	Heroes := m.xmlData.HeroInfoM.GetShowManualHeroes()
	for _, heroInfo := range Heroes {
		minMaxStarInfo := m.xmlData.HeroStarInfoM.GetMinMaxStar(heroInfo.Rare, heroInfo.Race)
		for i := minMaxStarInfo.minStar; i <= minMaxStarInfo.maxStar; i++ {
			_, exist := m.Datas[heroInfo.Id][i]
			if !exist {
				panic(fmt.Sprintf("load %s failed. hero Id:%d star:%d ", filename, heroInfo.Id, i))
			}
		}
	}
}

func (m *HeroBalanceInfoManager) CalcBalancePowerByTeams(teams []*cl.BalanceArenaTeam) uint64 {
	var power uint64
	for _, team := range teams {
		if team == nil {
			continue
		}
		// 只有英雄有平衡战力
		for _, hero := range team.Heroes {
			if hero == nil {
				continue
			}
			power += m.CalcHeroBalancePower(hero.SysId, hero.Star)
		}
	}
	return power
}

func (m *HeroBalanceInfoManager) CalcHeroBalancePower(heroSysId, heroStar uint32) uint64 {
	info := m.Index(heroSysId, heroStar)
	if info == nil {
		return 0
	}
	monsterId := info.MonsterId
	monsterDataInfo := m.xmlData.MonsterDataInfoM.Index(monsterId)
	if monsterDataInfo == nil {
		return 0
	}
	return uint64(monsterDataInfo.Power)
}
