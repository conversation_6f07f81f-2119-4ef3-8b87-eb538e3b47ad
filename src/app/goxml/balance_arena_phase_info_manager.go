package goxml

import (
	"fmt"
	"gitlab.qdream.com/kit/sea/util"
	"strings"
	"time"
)

type BalanceArenaPhaseInfoMEx struct {
	*BalanceArenaPhaseInfoM
	RoundPeriod uint32 // 赛期持续时间 = 阶段时间最大值
}

func newBalanceArenaPhaseInfoMEx(xmlData *XmlData) *BalanceArenaPhaseInfoMEx {
	m := &BalanceArenaPhaseInfoMEx{
		BalanceArenaPhaseInfoM: &BalanceArenaPhaseInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *BalanceArenaPhaseInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *BalanceArenaPhaseInfoMEx) load(dir string, isShow bool) error {
	err := m.BalanceArenaPhaseInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	for _, v := range m.idRecordMap {
		if v.PhaseClose > m.RoundPeriod {
			m.RoundPeriod = v.PhaseClose
		}
	}
	return nil
}

func (m *BalanceArenaPhaseInfoMEx) checkData() error {
	err := m.BalanceArenaPhaseInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}

func (m *BalanceArenaPhaseInfoMEx) GetRoundPeriod() int64 {
	return int64(m.RoundPeriod)
}

// 获取赛期的开始结束时间
func (m *BalanceArenaPhaseInfoMEx) GetRoundTimeConfigByDate(date string) (int64, int64) {
	dateStr := strings.Trim(date, " ")
	if dateStr == "" {
		panic(fmt.Sprintf("balanceArena.GetRoundTimeConfigByDate: invalid date. %s", date))
	}

	t, err := time.ParseInLocation(TIME_LAYOUT, dateStr, time.Local)
	if err != nil {
		panic(fmt.Sprintf("balanceArena.GetRoundTimeConfigByDate: ParseInLocation failed. err %+v date %s", err, date))
	}

	dateInt := t.Unix()
	if dateInt != int64(util.DailyZeroByTime(dateInt)) {
		panic(fmt.Sprintf("balanceArena.GetRoundTimeConfigByDate: not zero time. date %s", date))
	}

	return dateInt, dateInt + m.GetRoundPeriod()
}
