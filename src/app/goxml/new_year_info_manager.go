package goxml

import (
	"fmt"
	"path/filepath"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type NewYearInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*NewYearActivityInfoExt
	slice   []*NewYearActivityInfoExt
}

type NewYearActivityInfoExt struct {
	ID         uint32
	FunctionID uint32
	OpenTime   int64
	EndTime    int64
	LoginID    uint32
}

func newNewYearInfoManager(xmlData *XmlData) *NewYearInfoManager {
	m := &NewYearInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *NewYearInfoManager) name() string {
	return "new_year_info.xml"
}

func (m *NewYearInfoManager) ignoreForCheck() bool {
	return false
}

func (m *NewYearInfoManager) checkData() error {
	return nil
}

func (m *NewYearInfoManager) load(dir string, show bool) error {
	tmp := &NewYearInfos{}
	fileName := filepath.Join(dir, "new_year_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*NewYearActivityInfoExt, len(tmp.Datas))
	lastStartUnix := int64(0)
	lastEndUnix := int64(0)
	for _, data := range tmp.Datas {
		data.prepare()

		openTime, err := time.ParseInLocation(TIME_LAYOUT, data.OpenDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("new_year_info data:%d openTime failed: %v err:%s", data.Id, data.OpenDay, err))
		}
		openUnix := openTime.Unix()

		endTime, err := time.ParseInLocation(TIME_LAYOUT, data.EndDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("new_year_info data:%d endTime failed: %v err:%s", data.Id, data.EndDay, err))
		}
		endUnix := openTime.Unix()

		if openUnix > endUnix {
			panic(fmt.Sprintf("new_year_info data:%d openTime:%+v more than endTime:%+v", data.Id, openTime, endTime))
		}

		if lastStartUnix != 0 && lastEndUnix != 0 {
			//后续活动时间只能排在之前活动后面
			if openUnix <= lastEndUnix {
				panic(fmt.Sprintf("activity_story_info data:%d open time illegal", data.Id))
			}
		}
		lastEndUnix = endUnix
		lastStartUnix = openUnix

		functionInfo := m.xmlData.FunctionInfoM.Index(data.FunctionId)
		if functionInfo == nil {
			panic(fmt.Sprintf("new_year_info data:%d function id:%d is error", data.Id, data.FunctionId))
		}

		roundInfo := m.xmlData.ActivityStoryLoginInfoM.GetRound(data.LoginId)
		if len(roundInfo) == 0 {
			panic(fmt.Sprintf("new_year_info data:%d login:%d is erorr", data.Id, data.LoginId))
		}

		if data.PassId > 0 {
			passInfo := m.xmlData.PassInfoM.Index(data.PassId)
			if passInfo == nil {
				panic(fmt.Sprintf("new_year_info data:%d pass:%d info is nil", data.Id, data.PassId))
			}
			if passInfo.PassType != PassTypeActivity {
				panic(fmt.Sprintf("new_year_info data:%d pass:%d passType:%d is error", data.Id, data.PassId, passInfo.PassType))
			}
			if passInfo.OpenDay != openTime.Unix() || passInfo.EndDay != endTime.Unix() {
				panic(fmt.Sprintf("new_year_info data:%d pass:%d open time or end time not same with activity:%d", data.Id, data.PassId, passInfo.Id))
			}
		}

		ext := &NewYearActivityInfoExt{
			ID:         data.Id,
			FunctionID: data.FunctionId,
			OpenTime:   openTime.Unix(),
			EndTime:    endTime.Unix(),
			LoginID:    data.LoginId,
		}

		_, exist := m.Datas[ext.ID]
		if exist {
			panic(fmt.Sprintf("activity_story_info data:%d is repeated", data.Id))
		}
		m.Datas[ext.ID] = ext
		m.slice = append(m.slice, ext)
	}
	return nil
}

func (m *NewYearInfoManager) Index(key uint32) *NewYearActivityInfoExt {
	return m.Datas[key]
}

// 活动时间左闭右开
func (m *NewYearInfoManager) GetCurrent(now int64) *NewYearActivityInfoExt {
	for _, v := range m.slice {
		if now >= v.OpenTime && now < v.EndTime {
			return v
		}
	}
	return nil
}

// 活动时间左闭右开
func (e *NewYearActivityInfoExt) IsOpen(now int64) bool {
	if now >= e.OpenTime && now < e.EndTime {
		return true
	}
	return false
}
