package goxml

import (
	"fmt"
	"path/filepath"
	"sort"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ArenaDivisionInfoManager struct {
	xmlData      *XmlData
	Datas        map[uint32]*ArenaDivisionInfoExt
	ScoreDatas   []*ArenaDivisionInfoExt
	maxID        uint32
	initScore    uint32
	initDivision uint32
}

type ArenaDivisionInfoExt struct {
	ID            uint32
	NeedScore     uint32
	NeedRank      uint32
	ResetScore    uint32
	ResetDivision uint32
}

func newArenaDivisionInfoManager(xmlData *XmlData) *ArenaDivisionInfoManager {
	m := &ArenaDivisionInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ArenaDivisionInfoManager) name() string {
	return "ArenaDivisionInfo"
}

func (m *ArenaDivisionInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ArenaDivisionInfoManager) checkData() error {
	return nil
}

func (m *ArenaDivisionInfoManager) load(dir string, isShow bool) error {
	tmp := &ArenaDivisionInfos{}
	fileName := filepath.Join(dir, "arena_division_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*ArenaDivisionInfoExt, len(tmp.Datas))
	}
	m.ScoreDatas = make([]*ArenaDivisionInfoExt, 0, len(tmp.Datas))
	m.maxID = 0
	m.initScore = 0

	var (
		lastNeedScore  uint32
		lastResetScore uint32
		lastNeedRank   uint32
	)

	for _, data := range tmp.Datas {
		data.prepare()

		if data.Id != m.maxID+1 {
			panic(fmt.Sprintf("load config %s fail: division is discontinuity. id:%d",
				fileName, data.Id))
		}
		m.maxID = data.Id

		//段位1的需求分数，必须是0
		if data.Id == 1 {
			if data.NeedScore != 0 {
				panic(fmt.Sprintf("load config %s fail: first division needScore is not 0", fileName))
			}
			m.initScore = data.ResetScore
			m.initDivision = data.Id
		}

		if data.Id > 1 && data.NeedScore < lastNeedScore {
			panic(fmt.Sprintf("load config %s fail: needScore is discontinuity. id:%d",
				fileName, data.Id))
		}
		lastNeedScore = data.NeedScore

		if lastNeedRank > 0 {
			if data.NeedRank == 0 || data.NeedRank >= lastNeedRank {
				panic(fmt.Sprintf("load config %s fail: needScore not in order. id:%d",
					fileName, data.Id))
			}
			lastNeedRank = data.NeedRank
		}

		if data.ResetScore < lastResetScore {
			panic(fmt.Sprintf("load config %s fail: resetScore is discontinuity. id:%d",
				fileName, data.Id))
		}
		lastResetScore = data.ResetScore

		dataExt := &ArenaDivisionInfoExt{}
		dataExt.ID = data.Id
		dataExt.NeedScore = data.NeedScore
		dataExt.NeedRank = data.NeedRank
		dataExt.ResetScore = data.ResetScore

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
		m.ScoreDatas = append(m.ScoreDatas, dataExt)

		//计算添加ResetDivision
		//高段位，仅允许重置回低段位
		info := m.getInfoByScore(data.ResetScore)
		if info == nil {
			panic(fmt.Sprintf("load config %s fail: ResetScore cannot match division, id:%d",
				fileName, dataExt.ID))
		}
		dataExt.ResetDivision = info.ID
	}

	if len(m.ScoreDatas) == 0 {
		panic(fmt.Sprintf("load config %s fail: no ScoreDatas", fileName))
	}

	m.checkResetScoreLegal()
	return nil
}

// 检查重置分数的合法性 - 不能重置为有排名要求的段位
func (m *ArenaDivisionInfoManager) checkResetScoreLegal() {
	for _, v := range m.ScoreDatas {
		info := m.getInfoByScore(v.ResetScore)
		if info == nil {
			panic(fmt.Sprintf("ArenaDivisionInfoManager: checkResetScoreLegal -> no data, resetScore:%d, file:arena_division_info.xml",
				v.ResetScore))
		}
		if info.NeedRank > 0 {
			panic(fmt.Sprintf("ArenaDivisionInfoManager: checkResetScoreLegal -> resetScore err, resetScore:%d, division:%d, file:arena_division_info.xml",
				v.ResetScore, info.ID))
		}
	}
}

func (m *ArenaDivisionInfoManager) Index(division uint32) *ArenaDivisionInfoExt {
	return m.Datas[division]
}

func (m *ArenaDivisionInfoManager) GetMaxDivision() uint32 {
	return m.maxID
}

// 根据积分和排名获取配置
// 已在加载量表时，确保了最小段位需求积分是0
// @param uint32 score 积分
// @param uint32 rank 排名
// @return *ArenaDivisionInfoExt
func (m *ArenaDivisionInfoManager) GetInfoByScoreAndRank(score, rank uint32) *ArenaDivisionInfoExt {
	info := m.getInfoByScore(score)
	if info != nil {
		if m.isRankEnough(rank, info.NeedRank) {
			return info
		}

		for i := info.ID - 1; i > 0; i-- {
			data := m.Index(i)
			if data != nil {
				if m.isRankEnough(rank, data.NeedRank) {
					return data
				}
			}
		}
	}

	l4g.Errorf("ArenaDivisionInfoM.GetInfoByScoreAndRank err")
	return m.ScoreDatas[0]
}

// 根据分数获取段位
// 重置计算段位时使用（因规则限制，重置后分数，对应的段位不会有排名限制）
// @param uint32 score 积分
// @return uint32 段位
func (m *ArenaDivisionInfoManager) GetDivisionByScore(score uint32) uint32 {
	info := m.getInfoByScore(score)
	if info != nil {
		return info.ID
	}

	l4g.Errorf("ArenaDivisionInfoM.GetDivisionByScore err")
	return 0
}

func (m *ArenaDivisionInfoManager) getInfoByScore(score uint32) *ArenaDivisionInfoExt {
	i := sort.Search(len(m.ScoreDatas), func(i int) bool {
		return m.ScoreDatas[i].NeedScore > score
	})

	i--
	if i < 0 {
		i = 0
	}
	return m.ScoreDatas[i]
}

// 排名是否满足要求
// @param uint32 rank 玩家名次
// @param uint32 needRank 需求名次
// @return bool
func (m *ArenaDivisionInfoManager) isRankEnough(rank, needRank uint32) bool {
	if needRank == 0 || rank <= needRank {
		return true
	}
	return false
}

func (m *ArenaDivisionInfoManager) GetInitScore() uint32 {
	return m.initScore
}

func (m *ArenaDivisionInfoManager) GetInitDivision() uint32 {
	return m.initDivision
}
