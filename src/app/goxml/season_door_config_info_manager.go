package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SeasonDoorConfigInfoManager struct {
	xmlData               *XmlData
	SeasonEndClearItems   []*cl.Resource
	InitQueueNum          int
	QueueMaxBattleNum     uint32
	LogMaxNum             int
	MatUnlockLevel        uint32
	FirstCD               uint32
	FirstDrop             uint32
	FightModelNum         uint32 // 战斗模版数
	MaxRestTime           int64  // 最大挂机时间（秒）
	AwardAutoDecomposeNum uint32 // 挂机领奖自动分解剩余数量
	MailAutoDecomposeNum  uint32 // 邮件领奖自动分解剩余数量
	AutoDecomposeMaxLevel uint32 // 自动分解的最大等级
}

func newSeasonDoorConfigInfoManager(xmlData *XmlData) *SeasonDoorConfigInfoManager {
	m := &SeasonDoorConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *SeasonDoorConfigInfoManager) name() string {
	return "season_door_config_info.xml"
}

func (m *SeasonDoorConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *SeasonDoorConfigInfoManager) checkData() error {
	return nil
}

func (m *SeasonDoorConfigInfoManager) load(dir string, show bool) error {
	tmp := &SeasonDoorConfigInfos{}
	fileName := filepath.Join(dir, "season_door_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	datas := make(map[string]*SeasonDoorConfigInfo, len(tmp.Datas))

	for _, data := range tmp.Datas {
		data.prepare()
		datas[data.Key] = data
	}

	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_1"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_2"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_3"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_4"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_5"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_6"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_7"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_8"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_9"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_10"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_11"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_12"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_13"].ClRes...)
	m.SeasonEndClearItems = append(m.SeasonEndClearItems, datas["EXIT_SEASOM_DELETE_ITEM_ID_14"].ClRes...)
	m.InitQueueNum = int(datas["SEASON_DOOR_START_QUEUE_NUM"].Count)
	m.LogMaxNum = int(datas["SEASON_DOOR_LOG_MAX"].Count)
	m.MatUnlockLevel = datas["SEASON_DOOR_TYPE_2_UNLOCK"].Count
	m.FirstCD = datas["SEASON_DOOR_FIRST_BATTLE_TIME"].Count
	m.FirstDrop = datas["SEASON_DOOR_FIRST_DROP"].Count
	m.FightModelNum = datas["SEASON_DOOR_DISPATCH_TEST_TIME"].Count
	if m.FightModelNum <= 0 {
		panic(fmt.Sprintf("load config %s fail: SEASON_DOOR_DISPATCH_TEST_TIME err", fileName))
	}
	m.MaxRestTime = int64(datas["SEASON_DOOR_DISPATCH_MAX_TIME"].Count)
	if m.MaxRestTime <= 0 {
		panic(fmt.Sprintf("load config %s fail: SEASON_DOOR_DISPATCH_MAX_TIME err", fileName))
	}
	m.AwardAutoDecomposeNum = datas["AWARD_AUTO_DECOMPOSE_NUM"].Count
	if m.AwardAutoDecomposeNum <= 0 {
		panic(fmt.Sprintf("load config %s fail: AWARD_AUTO_DECOMPOSE_NUM err", fileName))
	}
	m.MailAutoDecomposeNum = datas["MAIL_AUTO_DECOMPOSE_NUM"].Count
	if m.MailAutoDecomposeNum <= 0 {
		panic(fmt.Sprintf("load config %s fail: MAIL_AUTO_DECOMPOSE_NUM err", fileName))
	}
	m.AutoDecomposeMaxLevel = datas["AUTO_DECOMPOSE_MAX_LEVEL"].Count
	if m.AutoDecomposeMaxLevel <= 0 {
		panic(fmt.Sprintf("load config %s fail: AUTO_DECOMPOSE_MAX_LEVEL err", fileName))
	}
	return nil
}

func (m *SeasonDoorConfigInfoManager) GetMaxRestTime() int64 {
	return m.MaxRestTime
}
