package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"
	"sort"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildDungeonRewardInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32][]*GuildDungeonRewardInfo
}

func newGuildDungeonRewardInfoManager(xmlData *XmlData) *GuildDungeonRewardInfoManager {
	m := &GuildDungeonRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildDungeonRewardInfoManager) name() string {
	return "GuildDungeonRewardInfo"
}

func (m *GuildDungeonRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildDungeonRewardInfoManager) checkData() error {
	return nil
}

func (m *GuildDungeonRewardInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildDungeonRewardInfos{}
	fileName := filepath.Join(dir, "guild_dungeon_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32][]*GuildDungeonRewardInfo)

	lastDamage := make(map[uint32]uint64)
	for _, data := range tmp.Datas {
		data.prepare()
		// todo 暂时屏蔽掉落
		//if data.Drop1 == 0 || data.Drop2 == 0 || data.Drop3 == 0 || data.Drop4 == 0 {
		//	panic(fmt.Sprintf("check data error %s  dropId is 0. id:%d  ", fileName, data.Id))
		//}
		if damage, exist := lastDamage[data.GroupId]; exist {
			if damage > data.DamageNode {
				panic(fmt.Sprintf("check data error %s %d", fileName, data.Id))
			}
		} else {
			lastDamage[data.GroupId] = damage
		}
		if _, exist := m.Datas[data.GroupId]; exist {
			m.Datas[data.GroupId] = append(m.Datas[data.GroupId], data)
		} else {
			m.Datas[data.GroupId] = []*GuildDungeonRewardInfo{data}
		}
	}
	return nil
}

func (m *GuildDungeonRewardInfoManager) Index(id uint32) []*GuildDungeonRewardInfo {
	return m.Datas[id]
}

func (m *GuildDungeonRewardInfoManager) GetRewardInfoByDamage(group uint32, damage uint64) *GuildDungeonRewardInfo {
	infos := m.Datas[group]
	if len(infos) > 0 {
		i := sort.Search(len(infos), func(i int) bool {
			return uint64(infos[i].DamageNode) > damage
		})
		i--
		if i < 0 {
			i = 0
		}
		return infos[i]
	}
	return nil
}

func (m *GuildDungeonRewardInfoManager) GetRewardByDamage(group uint32, damage uint64) []*cl.Resource {
	infos := m.Datas[group]
	if len(infos) > 0 {
		i := sort.Search(len(infos), func(i int) bool {
			return uint64(infos[i].DamageNode) > damage
		})
		i--
		if i < 0 {
			i = 0
		}
		return infos[i].ClRes
	}
	return nil
}

func (m *GuildDungeonRewardInfoManager) GetDropId(randId uint32, rewardInfo *GuildDungeonRewardInfo) uint32 {
	if rewardInfo == nil {
		return 0
	}
	switch randId {
	case 1:
		return rewardInfo.Drop1
	case 2:
		return rewardInfo.Drop2
	case 3:
		return rewardInfo.Drop3
	case 4:
		return rewardInfo.Drop4
	default:
		// todo return 0
		return rewardInfo.Drop1
	}
}
