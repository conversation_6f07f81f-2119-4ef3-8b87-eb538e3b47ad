package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityPushgiftConfigInfoManager struct {
	xmlData                         *XmlData
	Datas                           map[string]*ActivityPushgiftConfigInfo
	DownCd                          uint32
	maxWeight                       uint32
	minWeight                       uint32
	weightValue                     map[uint32]uint32
	valueWeight                     map[uint32]uint32
	createDay                       uint32
	dailyTotalCount                 uint32
	singleTriggerCount              uint32
	randomCountLow                  uint32
	randomCountInterval             uint32
	refreshDay                      uint32
	triggersStartEnd                map[uint32]*triggerStartEnd
	randomBattleFailedCountLow      uint32
	randomBattleFailedCountInterval uint32
}

type triggerStartEnd struct {
	start uint32
	end   uint32
}

func newActivityPushgiftConfigInfoManager(xmlData *XmlData) *ActivityPushgiftConfigInfoManager {
	m := &ActivityPushgiftConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityPushgiftConfigInfoManager) name() string {
	return "ActivityPushgiftConfigInfo"
}

func (m *ActivityPushgiftConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityPushgiftConfigInfoManager) checkData() error {
	return nil
}

func (m *ActivityPushgiftConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &ActivityPushgiftConfigInfos{}
	fileName := filepath.Join(dir, "activity_pushgift_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[string]*ActivityPushgiftConfigInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Key]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Key] = data
		}
	}

	m.triggersStartEnd = make(map[uint32]*triggerStartEnd)
	m.weightValue = make(map[uint32]uint32)
	m.valueWeight = make(map[uint32]uint32)
	m.DownCd = m.Datas["ACTIVITY_PUSHGIFT_CD_DOWN"].Count
	config, exist := m.Datas["ACTIVITY_PUSHGIFT_MIN_VALUE"]
	if !exist {
		panic(fmt.Sprintf("PUSHGIFT_CONFIG must have max value"))
	}
	minValue := config.Count
	config, exist = m.Datas["ACTIVITY_PUSHGIFT_MAX_VALUE"]
	if !exist {
		panic(fmt.Sprintf("PUSHGIFT_CONFIG must have min value"))
	}
	maxValue := config.Count
	if minValue > maxValue {
		panic(fmt.Sprintf("min value more thane max value"))
	}
	for i := minValue; i <= maxValue; i++ {
		key := fmt.Sprintf("ACTIVITY_PUSHGIFT_VALUE%d", i)
		info, exist := m.Datas[key]
		if !exist {
			panic(fmt.Sprintf("ACTIVITY_PUSHGIFT_VALUE%d is nil", i))
		}
		if i == minValue {
			m.minWeight = info.Count
		}
		if i == maxValue {
			m.maxWeight = info.Count
		}
		m.weightValue[info.Count] = info.Value
		m.valueWeight[info.Value] = info.Count
	}
	//m.createDay = m.Datas["CREATE_DAY"].Count
	m.dailyTotalCount = m.Datas["DAILY_TOTAL_COUNT"].Count
	m.singleTriggerCount = m.Datas["SINGLE_TRIGGER_COUNT"].Count
	m.randomCountLow = m.Datas["RANDOM_COUNT_LOW"].Count
	m.randomCountInterval = m.Datas["RANDOM_COUNT_INTERVAL"].Count
	m.refreshDay = m.Datas["PUSHGIFT_REFRESH_DAY"].Count
	if m.refreshDay == 0 {
		m.refreshDay = 1
	}
	if m.singleTriggerCount > m.dailyTotalCount {
		panic(fmt.Sprintf("single trigger count:%d is more than daily total count %d",
			m.singleTriggerCount, m.dailyTotalCount))
	}
	if m.Datas["TRIGGER_TOTAL"].Type != 0 {
		panic(fmt.Sprintf("TRIGGER_TOTAL Type must be zero"))
	}
	m.triggersStartEnd[m.Datas["TRIGGER_TOTAL"].Type] = &triggerStartEnd{
		start: m.Datas["TRIGGER_TOTAL"].Value,
		end:   m.Datas["TRIGGER_TOTAL"].Count,
	}
	if m.Datas["TRIGGER_LEVEL"].Type == 0 {
		panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_LEVEL Type must not be zero"))
	}
	if m.Datas["TRIGGER_LEVEL"].Count == 0 {
		if m.Datas["TRIGGER_LEVEL"].Value == m.Datas["TRIGGER_LEVEL"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_LEVEL Value or Count failed"))
		}
	} else {
		if m.Datas["TRIGGER_LEVEL"].Value >= m.Datas["TRIGGER_LEVEL"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_LEVEL Value or Count failed"))
		}
	}
	m.triggersStartEnd[m.Datas["TRIGGER_LEVEL"].Type] = &triggerStartEnd{
		start: m.Datas["TRIGGER_LEVEL"].Value,
		end:   m.Datas["TRIGGER_LEVEL"].Count,
	}

	if m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Type == 0 {
		panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_SUMMON_TRUE_HERO Type must not be zero"))
	}
	if m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Count == 0 {
		if m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Value == m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_SUMMON_TRUE_HERO Value or Count failed"))
		}
	} else {
		if m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Value >= m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_SUMMON_TRUE_HERO Value or Count failed"))
		}
	}
	m.triggersStartEnd[m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Type] = &triggerStartEnd{
		start: m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Value,
		end:   m.Datas["TRIGGER_SUMMON_TRUE_HERO"].Count,
	}

	if m.Datas["TRIGGER_ADVANCE_SUMMON"].Type == 0 {
		panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_ADVANCE_SUMMON Type must not be zero"))
	}
	if m.Datas["TRIGGER_ADVANCE_SUMMON"].Count == 0 {
		if m.Datas["TRIGGER_ADVANCE_SUMMON"].Value == m.Datas["TRIGGER_ADVANCE_SUMMON"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_ADVANCE_SUMMON Value or Count failed"))
		}
	} else {
		if m.Datas["TRIGGER_ADVANCE_SUMMON"].Value >= m.Datas["TRIGGER_ADVANCE_SUMMON"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_ADVANCE_SUMMON Value or Count failed"))
		}
	}
	m.triggersStartEnd[m.Datas["TRIGGER_ADVANCE_SUMMON"].Type] = &triggerStartEnd{
		start: m.Datas["TRIGGER_ADVANCE_SUMMON"].Value,
		end:   m.Datas["TRIGGER_ADVANCE_SUMMON"].Count,
	}

	if m.Datas["TRIGGER_MAZE_SUCCESS"].Type == 0 {
		panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_MAZE_SUCCESS Type must not be zero"))
	}
	if m.Datas["TRIGGER_MAZE_SUCCESS"].Count == 0 {
		if m.Datas["TRIGGER_MAZE_SUCCESS"].Value == m.Datas["TRIGGER_MAZE_SUCCESS"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_MAZE_SUCCESS Value or Count failed"))
		}
	} else {
		if m.Datas["TRIGGER_MAZE_SUCCESS"].Value >= m.Datas["TRIGGER_MAZE_SUCCESS"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_MAZE_SUCCESS Value or Count failed"))
		}
	}
	m.triggersStartEnd[m.Datas["TRIGGER_MAZE_SUCCESS"].Type] = &triggerStartEnd{
		start: m.Datas["TRIGGER_MAZE_SUCCESS"].Value,
		end:   m.Datas["TRIGGER_MAZE_SUCCESS"].Count,
	}

	if m.Datas["TRIGGER_BATTLE_FAILED"].Type == 0 {
		panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_BATTLE_FAILED Type must not be zero"))
	}
	if m.Datas["TRIGGER_BATTLE_FAILED"].Count == 0 {
		if m.Datas["TRIGGER_BATTLE_FAILED"].Value == m.Datas["TRIGGER_BATTLE_FAILED"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_BATTLE_FAILED Value or Count failed"))
		}
	} else {
		if m.Datas["TRIGGER_BATTLE_FAILED"].Value >= m.Datas["TRIGGER_BATTLE_FAILED"].Count {
			panic(fmt.Sprintf("activity_pushgift_config_info TRIGGER_BATTLE_FAILED Value or Count failed"))
		}
	}
	m.triggersStartEnd[m.Datas["TRIGGER_BATTLE_FAILED"].Type] = &triggerStartEnd{
		start: m.Datas["TRIGGER_BATTLE_FAILED"].Value,
		end:   m.Datas["TRIGGER_BATTLE_FAILED"].Count,
	}

	m.randomBattleFailedCountLow = m.Datas["BATTLE_FAIL_RANDOM_COUNT_LOW"].Count
	m.randomBattleFailedCountInterval = m.Datas["BATTLE_FAIL_RANDOM_COUNT_INTERVAL"].Count
	return nil
}

func (m *ActivityPushgiftConfigInfoManager) Index(key string) *ActivityPushgiftConfigInfo {
	return m.Datas[key]
}

func (m *ActivityPushgiftConfigInfoManager) GetMinValueAndMaxValue() (uint32, uint32) {
	return m.minWeight, m.maxWeight
}

func (m *ActivityPushgiftConfigInfoManager) GetWeightValueMap() map[uint32]uint32 {
	return m.weightValue
}

func (m *ActivityPushgiftConfigInfoManager) GetValueWeightMap() map[uint32]uint32 {
	return m.valueWeight
}

func (m *ActivityPushgiftConfigInfoManager) GetDownCd() uint32 {
	return m.DownCd
}

func (m *ActivityPushgiftConfigInfoManager) GetCreateDay() uint32 {
	return m.createDay
}

func (m *ActivityPushgiftConfigInfoManager) GetDailyTotalCount() uint32 {
	return m.dailyTotalCount
}

func (m *ActivityPushgiftConfigInfoManager) GetSingleTriggerCount() uint32 {
	return m.singleTriggerCount
}

func (m *ActivityPushgiftConfigInfoManager) GetRandomCountLow() uint32 {
	return m.randomCountLow
}

func (m *ActivityPushgiftConfigInfoManager) GetRandomCountInterval() uint32 {
	return m.randomCountInterval
}

func (m *ActivityPushgiftConfigInfoManager) CheckCreateDayProtect(day uint32) bool {
	return day < m.createDay
}

func (m *ActivityPushgiftConfigInfoManager) CheckCircleSingleTriggerCount(count uint32) bool {
	return count >= m.singleTriggerCount
}

func (m *ActivityPushgiftConfigInfoManager) CheckCircleTotalCount(count uint32) bool {
	return count >= m.dailyTotalCount
}

func (m *ActivityPushgiftConfigInfoManager) GetRefreshDay() uint32 {
	return m.refreshDay
}

func (m *ActivityPushgiftConfigInfoManager) CanTrigger(trigger uint32, createDay uint32) bool {
	if m.triggersStartEnd == nil {
		return false
	}
	startEnd, exist := m.triggersStartEnd[trigger]
	if exist {
		if createDay < startEnd.start {
			return false
		}
		if startEnd.end == 0 {
			return true
		}
		if createDay <= startEnd.end && startEnd.end > startEnd.start {
			return true
		} else {
			return false
		}
	} else {
		totalStartEnd, exist := m.triggersStartEnd[m.Datas["TRIGGER_TOTAL"].Type]
		if !exist {
			return false
		} else {
			if createDay < totalStartEnd.start {
				return false
			}
			if totalStartEnd.end == 0 {
				return true
			}
			if createDay <= totalStartEnd.end && totalStartEnd.end > totalStartEnd.start {
				return true
			} else {
				return false
			}
		}
	}
}

func (m *ActivityPushgiftConfigInfoManager) GetRandomBattleFailedCountLow() uint32 {
	return m.randomBattleFailedCountLow
}

func (m *ActivityPushgiftConfigInfoManager) GetRandomBattleFailedCountInterval() uint32 {
	return m.randomBattleFailedCountInterval
}
