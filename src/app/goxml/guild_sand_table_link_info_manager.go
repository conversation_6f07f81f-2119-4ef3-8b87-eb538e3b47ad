package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildSandTableLinkInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildSandTableLinkInfoExt
}

type GuildSandTableLinkInfoExt struct {
	NeedNum   uint32
	RaisePSID uint64
}

func newGuildSandTableLinkInfoManager(xmlData *XmlData) *GuildSandTableLinkInfoManager {
	m := &GuildSandTableLinkInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableLinkInfoManager) name() string {
	return "guild_sand_table_link_info"
}

func (m *GuildSandTableLinkInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableLinkInfoManager) checkData() error {
	return nil
}

func (g *GuildSandTableLinkInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableLinkInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_link_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	g.Datas = make(map[uint32]*GuildSandTableLinkInfoExt)
	for _, data := range tmp.Datas {
		dataExt := &GuildSandTableLinkInfoExt{
			NeedNum: data.LinkHeroNum,
		}
		raisePSInfo := g.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill, 1)
		if raisePSInfo == nil {
			panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill))
		}
		dataExt.RaisePSID = raisePSInfo.ID
		g.Datas[data.Id] = dataExt
	}
	return nil
}

func (g *GuildSandTableLinkInfoManager) Index(key uint32) *GuildSandTableLinkInfoExt {
	return g.Datas[key]
}
