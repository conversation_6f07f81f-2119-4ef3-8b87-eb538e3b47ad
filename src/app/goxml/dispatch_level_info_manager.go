package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

// Reference imports to suppress errors if they are not otherwise used.
type _ = cl.Resource

type DispatchLevelInfoExt struct {
	Id uint32 `xml:"id,attr"` //
	//TaskInfos map[uint32]*dispatchTaskInfo
	TaskInfos []*dispatchTaskInfo
	RewardUp  uint32 `xml:"reward_up,attr"` //int:奖励提升万分比
	Group     uint32 `xml:"group,attr"`     //int:等级组
}

type dispatchTaskInfo struct {
	//RecordType uint32
	TaskType uint32
	Value    uint32
}

func (e *DispatchLevelInfoExt) prepare(Info *DispatchLevelInfo) {

}

type DispatchLevelInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]*DispatchLevelInfoExt
	allEvent []uint32
}

func newDispatchLevelInfoManager(xmlData *XmlData) *DispatchLevelInfoManager {
	m := &DispatchLevelInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DispatchLevelInfoManager) name() string {
	return "DispatchLevelInfo"
}

func (m *DispatchLevelInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DispatchLevelInfoManager) checkData() error {
	return nil
}

func (m *DispatchLevelInfoManager) load(dir string, isShow bool) error {
	tmp := &DispatchLevelInfos{}
	fileName := filepath.Join(dir, "dispatch_level_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*DispatchLevelInfoExt, len(tmp.Datas))
	}
	events := make(map[uint32]struct{})
	m.allEvent = make([]uint32, 0, len(tmp.Datas)*2)
	for _, data := range tmp.Datas {
		if data == nil {
			continue
		}
		tmpExt := &DispatchLevelInfoExt{}
		tmpExt.Id = data.Id
		tmpExt.Group = data.Group
		tmpExt.RewardUp = data.RewardUp
		tmpExt.TaskInfos = make([]*dispatchTaskInfo, 0, 2)
		if data.TaskTypeId1 > 0 && data.Value1 > 0 {
			_, exit := DispatchTaskRecordType[data.TaskTypeId1]
			if !exit {
				panic(fmt.Sprintf("data:%d taskTypeId1:%d is not exist in dispatchTaskRecordType", data.Id, data.TaskTypeId1))
			}
			taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(data.TaskTypeId1)
			if taskTypeInfo == nil {
				panic(fmt.Sprintf("data:%d taskTypeId1:%d is not find in taskTypeInfo", data.Id, data.TaskTypeId1))
			}
			taskInfo := &dispatchTaskInfo{}
			taskInfo.TaskType = data.TaskTypeId1
			taskInfo.Value = data.Value1
			tmpExt.TaskInfos = append(tmpExt.TaskInfos, taskInfo)
			events[taskTypeInfo.Type] = struct{}{}
		}
		if data.TaskTypeId2 > 0 && data.Value2 > 0 {
			_, exit := DispatchTaskRecordType[data.TaskTypeId2]
			if !exit {
				panic(fmt.Sprintf("data:%d taskTypeId2:%d is not exist in dispatchTaskRecordType", data.Id, data.TaskTypeId2))
			}
			taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(data.TaskTypeId2)
			if taskTypeInfo == nil {
				panic(fmt.Sprintf("data:%d taskTypeId2:%d is not find in taskTypeInfo", data.Id, data.TaskTypeId2))
			}
			taskInfo := &dispatchTaskInfo{}
			taskInfo.TaskType = data.TaskTypeId2
			taskInfo.Value = data.Value2
			tmpExt.TaskInfos = append(tmpExt.TaskInfos, taskInfo)
			events[taskTypeInfo.Type] = struct{}{}
		}
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *tmpExt
			tmpExt = ptr
		} else {
			m.Datas[data.Id] = tmpExt
		}
	}
	for event := range events {
		m.allEvent = append(m.allEvent, event)
	}
	return nil
}

func (m *DispatchLevelInfoManager) GetRecordById(id uint32) *DispatchLevelInfoExt {
	return m.Datas[id]
}

func (m *DispatchLevelInfoManager) Index(key uint32) *DispatchLevelInfoExt {
	return m.Datas[key]
}

func (m *DispatchLevelInfoManager) AllEvent() []uint32 {
	return m.allEvent
}
