package goxml

import (
	"app/protos/out/cl"
	"errors"
	"fmt"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityPyramidOpenInfoMEx struct {
	*ActivityPyramidOpenInfoM
	maxActServerDay uint32
}

func newActivityPyramidOpenInfoMEx(xmlData *XmlData) *ActivityPyramidOpenInfoMEx {
	m := &ActivityPyramidOpenInfoMEx{
		ActivityPyramidOpenInfoM: &ActivityPyramidOpenInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityPyramidOpenInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *ActivityPyramidOpenInfoMEx) load(dir string, isShow bool) error {
	err := m.ActivityPyramidOpenInfoM.load(dir, isShow)
	if err != nil {
		return err
	}

	m.maxActServerDay = ActivityPyramidProtectDay
	for _, v := range m.idRecordMap {
		if v.OpenDay >= ActivityPyramidProtectDay {
			return errors.New(fmt.Sprintf("config:%s data:%d open day:%d is more than ProtectDay:%d", m.name(), v.Id, v.OpenDay, ActivityPyramidProtectDay))
		}
		if v.OpenDay+v.OpenNum-1 > m.maxActServerDay {
			m.maxActServerDay = v.OpenDay + v.OpenNum - 1
		}
	}

	return nil
}

func (m *ActivityPyramidOpenInfoMEx) checkData() error {
	err := m.ActivityPyramidOpenInfoM.checkData()
	if err != nil {
		return err
	}

	return nil
}

func (m *ActivityPyramidOpenInfoMEx) IsNewServer(serverDay uint32) bool {
	return serverDay <= m.maxActServerDay
}

func (m *ActivityPyramidOpenInfoMEx) GetCurActivity(serverDay uint32, serverStartTime int64) *cl.PyramidActivityBase {
	for _, info := range m.idRecordMap {
		if serverDay >= info.OpenDay && serverDay < info.OpenDay+info.OpenNum {
			return info.ConvertBase(serverStartTime)
		}
	}
	return nil
}

func (info *ActivityPyramidOpenInfo) ConvertBase(serverStartTime int64) *cl.PyramidActivityBase {
	serverOpenZero := util.DailyZeroByTime(serverStartTime)
	return &cl.PyramidActivityBase{
		Id:         info.Id,
		ActivityId: info.OpenTheme,
		StartTime:  int64(serverOpenZero) + (int64(info.OpenDay-1) * int64(util.DaySecs)),
		EndTime:    int64(serverOpenZero) + (int64(info.OpenDay+info.OpenNum-1) * int64(util.DaySecs)) - 1,
		OpStatus:   1,
		NewServer:  true,
	}
}
