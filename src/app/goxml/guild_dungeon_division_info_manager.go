package goxml

import (
	"fmt"
	"math"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildDungeonDivisionInfoManager struct {
	xmlData         *XmlData
	Datas           map[uint32]*GuildDungeonDivisionInfo
	DataByStar      []*GuildDungeonDivisionInfo // 根据star递增
	initialDivision uint32
	maxDivision     uint32
}

func newGuildDungeonDivisionInfoManager(xmlData *XmlData) *GuildDungeonDivisionInfoManager {
	m := &GuildDungeonDivisionInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildDungeonDivisionInfoManager) name() string {
	return "GuildDungeonDivisionInfo"
}

func (m *GuildDungeonDivisionInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildDungeonDivisionInfoManager) checkData() error {
	return nil
}

func (m *GuildDungeonDivisionInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildDungeonDivisionInfos{}
	fileName := filepath.Join(dir, "guild_dungeon_division_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GuildDungeonDivisionInfo, len(tmp.Datas))
	}

	m.DataByStar = make([]*GuildDungeonDivisionInfo, 0, len(tmp.Datas))

	lastStar := uint32(0)

	m.initialDivision = uint32(0)
	m.maxDivision = uint32(0)

	for _, data := range tmp.Datas {
		data.prepare()

		if m.initialDivision == 0 {
			m.initialDivision = data.Id
		} else {
			if m.initialDivision > data.Id {
				m.initialDivision = data.Id
			}
		}

		if data.Point != 0 && data.Point <= lastStar {
			// 保证star是递增的
			panic(fmt.Sprintf("fileName:%s: star error. id:%d ", fileName, data.Id))
		}
		lastStar = data.Point

		if data.Id <= m.maxDivision {
			// 保证star是递增的
			panic(fmt.Sprintf("fileName:%s: id error. id:%d ", fileName, data.Id))
		}
		m.maxDivision = data.Id

		weeklyRewardInfos := m.xmlData.GuildDungeonWeeklyRewardInfoM.GetRanks()
		if weeklyRewardInfos[data.WeeklyRankRewardId] == nil {
			panic(fmt.Sprintf("guild_dungeon_division_info.xml data:%d week weekly_rank_reward_id:%d is error", data.Id, data.WeeklyRankRewardId))
		}
		seasonRewardInfo := m.xmlData.GuildDungeonSeasonRewardInfoM.Index(data.SeasonRankRewardId)
		if seasonRewardInfo == nil {
			panic(fmt.Sprintf("guild_dungeon_division_info.xml data:%d week season_rank_reward_id:%d is error", data.Id, data.SeasonRankRewardId))
		}
		levelUpRewardInfo := m.xmlData.GuildDungeonLevelUpRewardInfoM.Index(data.RankLevelUpRewardId)
		if levelUpRewardInfo == nil {
			panic(fmt.Sprintf("guild_dungeon_division_info.xml data:%d week rank_level_up_reward_id:%d is error", data.Id, data.RankLevelUpRewardId))
		}

		m.DataByStar = append(m.DataByStar, data)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	if m.maxDivision > 99 {
		// 段位不能大于99，因为公会副本生成房间号时，限定段位只能是0~99
		panic(fmt.Sprintf("data check error: maxDivision error. fileName:%s maxDivision:%d", fileName, m.maxDivision))
	}
	return nil
}

func (r *GuildDungeonDivisionInfo) GetRank() uint32 {
	return r.Point
}

func (m *GuildDungeonDivisionInfoManager) GetRecordById(id uint32) *GuildDungeonDivisionInfo {
	return m.Datas[id]
}

func (m *GuildDungeonDivisionInfoManager) Index(key uint32) *GuildDungeonDivisionInfo {
	return m.Datas[key]
}

func (m *GuildDungeonDivisionInfoManager) GetDivisionByStar(star uint32) *GuildDungeonDivisionInfo {
	return GetDataLessAndClosedByRank(m.DataByStar, star)
}

//func (m *GuildDungeonDivisionInfoManager) GetNextDivision(currentDivision uint32) uint32 {
//
//	for i := 0; i < len(m.dataByStar); i++ {
//		if m.dataByStar[i].Id != currentDivision {
//			continue
//		}
//		if i + 1 >= len(m.dataByStar) {
//			return 0
//		}
//		return m.dataByStar[i+1].Id
//	}
//
//	return 0
//}

// GetExtendedDivisionIds
// @Description:  获取匹配时的扩展段位
// @receiver g
// @param currentDivision
// @return []uint32
func (m *GuildDungeonDivisionInfoManager) GetExtendedDivisionIds(currentDivision uint32) []uint32 {

	divisionIds := make([]uint32, 0, 4)

	currentIndex := -1
	for i, info := range m.DataByStar {
		if info.Id == currentDivision {
			currentIndex = i
			break
		}
	}

	if currentIndex < 0 {
		return nil
	}

	//先向上2个段位扩展
	for i := 2; i > 0; i-- {
		if currentIndex+i >= len(m.DataByStar) {
			break
		}
		divisionIds = append(divisionIds, m.DataByStar[currentIndex+i].Id)
	}

	//然后向下2个段位
	for i := 1; i <= 2; i++ {
		if currentIndex < i {
			break
		}
		if currentIndex-i < 0 {
			continue
		}
		divisionIds = append(divisionIds, m.DataByStar[currentIndex-i].Id)
	}

	if currentDivision > m.xmlData.GuildConfigInfoM.GetGuildDungeonNeedExtendDivision() {
		return divisionIds
	}

	// 黄金2及以下段位继续向上扩展至钻3
	for i := 3; currentIndex+i < len(m.DataByStar); i++ {
		thisDivision := m.DataByStar[currentIndex+i].Id
		info := m.Index(thisDivision)
		if info == nil {
			break
		}
		if info.Id > m.xmlData.GuildConfigInfoM.GetGuildDungeonExtendEndDivision() {
			break
		}
		divisionIds = append(divisionIds, thisDivision)
	}

	// 黄金2及以下段位向下找至最低段位
	for i := 3; currentIndex-i >= 0; i++ {
		if currentIndex < i {
			break
		}

		divisionIds = append(divisionIds, m.DataByStar[currentIndex-i].Id)
	}

	return divisionIds
}

func (m *GuildDungeonDivisionInfoManager) GetInitialDivision() uint32 {
	return m.initialDivision
}

func (m *GuildDungeonDivisionInfoManager) GetInitialStar() uint32 {
	info := m.Index(m.initialDivision)
	if info == nil {
		return 0
	}
	return info.ResetPoint
}

func (m *GuildDungeonDivisionInfoManager) GetMaxDivision() uint32 {
	return m.maxDivision
}

func (m *GuildDungeonDivisionInfoManager) CalcResetDivisionAndPoint(currentPoint uint32) (uint32, uint32) {
	divisionInfo := m.GetDivisionByStar(currentPoint)
	if divisionInfo == nil {
		l4g.Errorf("divisionInfo not exist. points:%d", currentPoint)
		return 0, 0
	}

	fixPoint := uint32(0)
	if currentPoint > 1000 {
		fixPoint = uint32(math.Pow(float64(currentPoint-1000), 0.5)) // 修正分 =  (X - 1000) ^ 0.5
	}

	resetPoint := divisionInfo.ResetPoint + fixPoint // 重置分+修正分 = 真正的重置分

	resetDivisionInfo := m.GetDivisionByStar(resetPoint)
	if resetDivisionInfo == nil {
		l4g.Errorf("resetDivisionInfo not exist. id:%d", resetPoint)
		return 0, 0
	}
	return resetDivisionInfo.Id, resetPoint
}
