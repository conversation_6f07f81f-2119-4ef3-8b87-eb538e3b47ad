package goxml

import (
	"fmt"
	"strings"

	"gitlab.qdream.com/kit/sea/actor"
	"gitlab.qdream.com/kit/sea/discovery"
	"gitlab.qdream.com/kit/sea/util"
	"gitlab.qdream.com/kit/sea/zebra/tcp"

	l4g "github.com/ivanabc/log4go"
)

type CrossMasterConfig struct {
	Server  xmlServer  `xml:"server"`
	Clients xmlClients `xml:"clients"`
	Actors  xmlActors  `xml:"actors"`
	Etcd    EtcdConfig `xml:"etcd"`
}

func (s *CrossMasterConfig) GetActorConfig(kind int) *actor.Config {
	for _, value := range s.Actors.Set {
		if value.Kind == kind {
			return &actor.Config{
				Kind:      uint32(value.Kind),
				KindName:  value.Name,
				MsgQSize:  value.MsgQueueSize,
				Rate:      value.Rate,
				TimerSize: value.TimerSize,
			}
		}
	}
	return nil
}

func (s *CrossMasterConfig) Load(filename string, show bool) {
	if err := util.LoadConfig(filename, s); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", filename, err))
	} else if show {
		l4g.Debug("config(%s): %+v", filename, *s)
	}
}

func (s *CrossMasterConfig) GetServerConfig() *tcp.Config {
	cfg := &tcp.Config{
		MaxReadMsgSize:    s.Server.GetProperty("MaxReadMsgSize"),
		ReadMsgQueueSize:  s.Server.GetProperty("ReadMsgQueueSize"),
		MaxWriteMsgSize:   s.Server.GetProperty("MaxWriteMsgSize"),
		WriteMsgQueueSize: s.Server.GetProperty("WriteMsgQueueSize"),
		WriteTimeOut:      s.Server.GetProperty("WriteTimeOut"),
	}
	return cfg
}

func (s *CrossMasterConfig) GetZebraConfig(server string) *tcp.Config {
	var cfg *tcp.Config
	for _, value := range s.Clients.Data {
		if value.Server == server {
			cfg = &tcp.Config{
				MaxReadMsgSize:    value.GetProperty("MaxReadMsgSize"),
				ReadMsgQueueSize:  value.GetProperty("ReadMsgQueueSize"),
				MaxWriteMsgSize:   value.GetProperty("MaxWriteMsgSize"),
				WriteMsgQueueSize: value.GetProperty("WriteMsgQueueSize"),
				WriteTimeOut:      value.GetProperty("WriteTimeOut"),
			}
			return cfg
		}
	}
	return nil
}

func (s *CrossMasterConfig) GetDiscoveryConfig(prefix bool) *discovery.Config {
	return &discovery.Config{
		Servers:        strings.Split(s.Etcd.Servers, ","),
		DialTimeout:    s.Etcd.DialTimeout,
		RequestTimeout: s.Etcd.RequestTimeout,
		Prefix:         prefix,
	}
}
