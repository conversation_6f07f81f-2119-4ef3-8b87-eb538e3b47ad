package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type TowerPokemonDungeonInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*TowerPokemonDungeonInfoExt
}

type TowerPokemonDungeonInfoExt struct {
	Data          *TowerPokemonDungeonInfo
	MonsterGroups []uint32 //敌人集合
	FightEffects  []uint32 //关卡加成
}

func newTowerPokemonDungeonInfoManager(xmlData *XmlData) *TowerPokemonDungeonInfoManager {
	m := &TowerPokemonDungeonInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *TowerPokemonDungeonInfoManager) name() string {
	return "tower_pokemon_dungeon_info.xml"
}

func (m *TowerPokemonDungeonInfoManager) ignoreForCheck() bool {
	return false
}

func (m *TowerPokemonDungeonInfoManager) checkData() error {
	return nil
}

func (m *TowerPokemonDungeonInfoManager) load(dir string, show bool) error {
	tmp := &TowerPokemonDungeonInfos{}
	fileName := filepath.Join(dir, "tower_pokemon_dungeon_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*TowerPokemonDungeonInfoExt, len(tmp.Datas))

	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &TowerPokemonDungeonInfoExt{
			Data: data,
		}
		if data.MonsterGroup1 == 0 {
			panic(fmt.Sprintf("load config %s fail: MonsterGroup1=0, id:%d",
				fileName, data.Id))
		}
		if m.xmlData.MonsterGroupInfoM.Index(data.MonsterGroup1) == nil {
			panic(fmt.Sprintf("load config %s fail: MonsterGroupInfoM cant find MonsterGroup:%d",
				fileName, data.MonsterGroup1))
		}
		dataExt.MonsterGroups = append(dataExt.MonsterGroups, data.MonsterGroup1)

		if data.MonsterGroup2 == 0 {
			panic(fmt.Sprintf("load config %s fail: MonsterGroup2=0, id:%d",
				fileName, data.Id))
		}
		if m.xmlData.MonsterGroupInfoM.Index(data.MonsterGroup2) == nil {
			panic(fmt.Sprintf("load config %s fail: MonsterGroupInfoM cant find MonsterGroup:%d",
				fileName, data.MonsterGroup2))
		}
		dataExt.MonsterGroups = append(dataExt.MonsterGroups, data.MonsterGroup2)

		if data.FightEffect1 == 0 {
			panic(fmt.Sprintf("load config %s fail: FightEffect1=0, id:%d",
				fileName, data.Id))
		}
		dataExt.FightEffects = append(dataExt.FightEffects, data.FightEffect1)
		if data.FightEffect2 == 0 {
			panic(fmt.Sprintf("load config %s fail: FightEffect2=0, id:%d",
				fileName, data.Id))
		}
		dataExt.FightEffects = append(dataExt.FightEffects, data.FightEffect2)

		m.Datas[data.Id] = dataExt
	}
	return nil
}

func (m *TowerPokemonDungeonInfoManager) Index(key uint32) *TowerPokemonDungeonInfoExt {
	return m.Datas[key]
}
