package goxml

import (
	"fmt"
	"path/filepath"
	"sort"
	"strings"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityReturnInfoExt struct {
	*ActivityReturnInfo
	TakeEffectTime int64
}

type ActivityReturnInfoManager struct {
	xmlData                      *XmlData
	Datas                        map[uint32]*ActivityReturnInfoExt
	takeEffectTimeReturnInfosMap map[int64][]*ActivityReturnInfoExt
	takeEffectTimes              []int64 //从大到小排序
}

func newActivityReturnInfoManager(xmlData *XmlData) *ActivityReturnInfoManager {
	m := &ActivityReturnInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityReturnInfoManager) name() string {
	return "ActivityReturnInfo"
}

func (m *ActivityReturnInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityReturnInfoManager) checkData() error {
	return nil
}

func (m *ActivityReturnInfoManager) load(dir string, isShow bool) error {
	tmp := &ActivityReturnInfos{}
	fileName := filepath.Join(dir, "activity_return_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ActivityReturnInfoExt, len(tmp.Datas))
	}
	if m.takeEffectTimeReturnInfosMap == nil {
		m.takeEffectTimeReturnInfosMap = make(map[int64][]*ActivityReturnInfoExt)
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if len(m.xmlData.ActivityReturnLoginInfoM.DayLoginInfos(data.LoginId)) <= 0 {
			panic(fmt.Sprintf("data check error file: %s Id:%d LoginID:%d not exist.",
				fileName, data.Id, data.LoginId))
		}
		if data.RechargeShopId > 0 && m.xmlData.ActivityRechargeShopInfoM.Index(data.RechargeShopId) == nil {
			panic(fmt.Sprintf("data check error file: %s Id:%d RechargeShopId:%d not exist.",
				fileName, data.Id, data.RechargeShopId))
		}
		takeEffectTimeStr := strings.Trim(data.Time, " ")
		if takeEffectTimeStr == "" {
			panic(fmt.Sprintf("load %s fail: no takeEffectTimeStr, id:%d", fileName, data.Id))
		}
		t, err := time.ParseInLocation(TIME_LAYOUT, takeEffectTimeStr, time.Local)
		if err != nil {
			panic(fmt.Sprintf("data check error file: %s ParseInLocation failed:%v, id:%d",
				fileName, err, data.Id))
		}
		takeEffectTime := t.Unix()

		dataExt := &ActivityReturnInfoExt{
			ActivityReturnInfo: data,
			TakeEffectTime:     takeEffectTime,
		}
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
		c := len(m.takeEffectTimeReturnInfosMap[takeEffectTime])
		if c > 0 {
			prevData := m.takeEffectTimeReturnInfosMap[takeEffectTime][c-1]
			if data.AfkMin <= prevData.AfkMax {
				panic(fmt.Sprintf("afk min check error file: %s ParseInLocation failed:%v, id:%d",
					fileName, err, data.Id))
			}
		}
		m.takeEffectTimeReturnInfosMap[takeEffectTime] = append(m.takeEffectTimeReturnInfosMap[takeEffectTime], dataExt)
	}
	for k := range m.takeEffectTimeReturnInfosMap {
		m.takeEffectTimes = append(m.takeEffectTimes, k)
	}
	sort.Slice(m.takeEffectTimes, func(i, j int) bool {
		return m.takeEffectTimes[i] > m.takeEffectTimes[j]
	})
	return nil
}

func (m *ActivityReturnInfoManager) GetRecordById(id uint32) *ActivityReturnInfoExt {
	return m.Datas[id]
}

func (m *ActivityReturnInfoManager) Index(key uint32) *ActivityReturnInfoExt {
	return m.Datas[key]
}

func (m *ActivityReturnInfoManager) ReturnInfos(t int64) []*ActivityReturnInfoExt {
	for _, takeEffectTime := range m.takeEffectTimes {
		if t >= takeEffectTime {
			return m.takeEffectTimeReturnInfosMap[takeEffectTime]
		}
	}
	return nil
}

func (m *ActivityReturnInfoManager) GetByLossDayCount(t int64, lossDayCount uint32) *ActivityReturnInfoExt {
	//不得小于最小天数
	if lossDayCount < m.xmlData.ActivityReturnConfigInfoM.ConditionDayMiddle {
		return nil
	}
	returnInfos := m.ReturnInfos(t)
	if returnInfos == nil {
		return nil
	}
	for _, returnInfo := range returnInfos {
		if returnInfo.AfkMin <= lossDayCount && returnInfo.AfkMax >= lossDayCount {
			return returnInfo
		}
	}
	return nil
}
