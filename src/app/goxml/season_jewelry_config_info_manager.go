package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	l4g "github.com/ivanabc/log4go"
	"strings"
)

type SeasonJewelryConfigInfoMEx struct {
	*SeasonJewelryConfigInfoM
	DelResources []*cl.Resource
}

func newSeasonJewelryConfigInfoMEx(xmlData *XmlData) *SeasonJewelryConfigInfoMEx {
	m := &SeasonJewelryConfigInfoMEx{
		SeasonJewelryConfigInfoM: &SeasonJewelryConfigInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *SeasonJewelryConfigInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *SeasonJewelryConfigInfoMEx) load(dir string, isShow bool) error {
	err := m.SeasonJewelryConfigInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	for _, v := range m.keyRecordMap {
		if !strings.Contains(v.Key, "RESOURCE_DEL") {
			continue
		}
		m.DelResources = append(m.DelResources, GenSimpleResource(v.Type, v.Value, v.Count))
	}
	return nil
}

func (m *SeasonJewelryConfigInfoMEx) checkData() error {
	return nil
}

func (m *SeasonJewelryConfigInfoMEx) GetDelResources() []*cl.Resource {
	return m.DelResources
}

func (m *SeasonJewelryConfigInfoMEx) GetClassUpCosts() []*cl.Resource {
	var costs []*cl.Resource

	info := m.GetRecordByKey("CLASS_UP_COST")
	if info == nil {
		return costs
	}

	costs = append(costs, GenSimpleResource(info.Type, info.Value, info.Count))
	return costs
}

func (m *SeasonJewelryConfigInfoMEx) GetChangeCosts() []*cl.Resource {
	var costs []*cl.Resource

	info := m.GetRecordByKey("SKILL_CHANGE_COST")
	if info == nil {
		return costs
	}

	costs = append(costs, GenSimpleResource(info.Type, info.Value, info.Count))
	return costs
}

func (m *SeasonJewelryConfigInfoMEx) GetChangeCostsByChangeNum(changeNum uint32) []*cl.Resource {
	var costs []*cl.Resource

	info := m.GetRecordByKey("SKILL_CHANGE_COST")
	if info == nil {
		return costs
	}

	costs = append(costs, GenSimpleResource(info.Type, info.Value, info.Count*changeNum))
	return costs
}

func (m *SeasonJewelryConfigInfoMEx) GetDecomposeCostRate() int {
	info := m.GetRecordByKey("REC_COST_RATE")
	if info == nil {
		return 0
	}
	return int(info.Count)
}

func (m *SeasonJewelryConfigInfoMEx) GetDecomposeChangeCostRate() int {
	info := m.GetRecordByKey("REC_COST_RATE_CHANGE")
	if info == nil {
		return 0
	}
	return int(info.Count)
}

func (m *SeasonJewelryConfigInfoMEx) GetRecyclePointsByRare(rare uint32) uint32 {
	var info *SeasonJewelryConfigInfo
	switch rare {
	case uint32(common.QUALITY_GREEN):
		info = m.GetRecordByKey("SEASON_POINT_20")
	case uint32(common.QUALITY_BLUE):
		info = m.GetRecordByKey("SEASON_POINT_30")
	case uint32(common.QUALITY_PURPLE):
		info = m.GetRecordByKey("SEASON_POINT_40")
	case uint32(common.QUALITY_ORANGE):
		info = m.GetRecordByKey("SEASON_POINT_50")
	case uint32(common.QUALITY_RED):
		info = m.GetRecordByKey("SEASON_POINT_60")
	case uint32(common.QUALITY_GOLDEN):
		info = m.GetRecordByKey("SEASON_POINT_70")
	default:
		l4g.Errorf("GetRecyclePointsByRare: invalid rare. rare:%d", rare)
	}
	if info != nil {
		return info.Count
	}
	return 0
}
