package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildSandTableTaskAwardInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]map[uint32]*GuildSandTableTaskAwardInfo //group Id
}

func newGuildSandTableTaskAwardInfoManager(xmlData *XmlData) *GuildSandTableTaskAwardInfoManager {
	m := &GuildSandTableTaskAwardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableTaskAwardInfoManager) name() string {
	return "guild_sand_table_task_award_info"
}

func (m *GuildSandTableTaskAwardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableTaskAwardInfoManager) checkData() error {
	return nil
}

func (g *GuildSandTableTaskAwardInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableTaskAwardInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_task_award_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	g.Datas = make(map[uint32]map[uint32]*GuildSandTableTaskAwardInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		taskTypeInfo := g.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskTypeInfo == nil {
			panic(fmt.Sprintf("file:%s data id:%d typeId:%d check failed ", fileName, data.Id, data.TypeId))
		}
		if len(data.ClRes) == 0 {
			panic(fmt.Sprintf("file:%s data id:%d reward  is nil ", fileName, data.Id))
		}

		_, exist := g.Datas[data.HomeAwardGroup]
		if !exist {
			g.Datas[data.HomeAwardGroup] = make(map[uint32]*GuildSandTableTaskAwardInfo)
		}

		_, exist = g.Datas[data.HomeAwardGroup][data.Id]
		if exist {
			panic(fmt.Sprintf("file:%s data id:%d is repeated", fileName, data.Id))
		}
		g.Datas[data.HomeAwardGroup][data.Id] = data
	}
	return nil
}

func (g *GuildSandTableTaskAwardInfoManager) Index(group, id uint32) *GuildSandTableTaskAwardInfo {
	return g.Datas[group][id]
}
