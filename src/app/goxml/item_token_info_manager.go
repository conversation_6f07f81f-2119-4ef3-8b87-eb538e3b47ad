package goxml

import (
	"fmt"
	"path/filepath"
	"strings"
	"time"

	seaTime "gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type ItemTokenInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ItemTokenInfoExt
}

type ItemTokenInfoExt struct {
	Id         uint32 //int:货币ID
	Rare       uint32 //int:稀有度
	Max        uint32 //int:最大持有量
	IsLimit    uint32 //int:是否为限时道具
	DeleteTime int64  //string:过期时间
	Name       string //string:道具名
	IsSeason   uint32 //int:是否赛季道具
}

func newItemTokenInfoManager(xmlData *XmlData) *ItemTokenInfoManager {
	m := &ItemTokenInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ItemTokenInfoManager) name() string {
	return "item_token_info.xml"
}

func (m *ItemTokenInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ItemTokenInfoManager) checkData() error {
	return nil
}

func (m *ItemTokenInfoManager) load(dir string, show bool) error {
	tmp := &ItemTokenInfos{}
	fileName := filepath.Join(dir, "item_token_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ItemTokenInfoExt, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &ItemTokenInfoExt{
			Id:       data.Id,
			Rare:     data.Rare,
			Max:      data.Max,
			IsLimit:  data.IsLimit,
			Name:     data.Name,
			IsSeason: data.IsSeason,
		}
		if data.IsLimit == 1 && data.DeleteTime == "" {
			panic(fmt.Sprintf("load config %s fail: deleteTime is nil. id:%d", fileName, data.Id))
		}
		if data.IsLimit == 0 && data.DeleteTime != "" {
			panic(fmt.Sprintf("load config %s fail: deleteTime not nil. id:%d", fileName, data.Id))
		}

		if data.DeleteTime != "" {
			stamp, err := time.ParseInLocation(TIME_LAYOUT, strings.Trim(data.DeleteTime, " "), time.Local)
			if err != nil {
				panic(fmt.Sprintf("load config %s fail: start time trans failed. %s", fileName, err))
			}
			dataExt.DeleteTime = stamp.Unix()
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
	}
	return nil
}

func (m *ItemTokenInfoManager) Index(key uint32) *ItemTokenInfoExt {
	return m.Datas[key]
}

// IsExpired ：检查限时道具是否过期
func (m *ItemTokenInfoManager) IsExpired(id uint32) bool {
	data := m.Datas[id]
	if data == nil {
		return true
	}
	if data.DeleteTime != 0 && seaTime.Now().Unix() >= data.DeleteTime {
		return true
	}

	return false
}
