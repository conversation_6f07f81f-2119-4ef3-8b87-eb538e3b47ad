package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/ret"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

type DisorderlandDropGroupInfoManager struct {
	xmlData *XmlData
	groups  map[uint32][]*DisorderlandDropGroupInfo // key: groupID
}

func newDisorderlandDropGroupInfoManager(xmlData *XmlData) *DisorderlandDropGroupInfoManager {
	m := &DisorderlandDropGroupInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DisorderlandDropGroupInfoManager) name() string {
	return "DisorderlandDropGroupInfo"
}

func (m *DisorderlandDropGroupInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DisorderlandDropGroupInfoManager) checkData() error {
	return nil
}

func (m *DisorderlandDropGroupInfoManager) load(dir string, isShow bool) error {
	tmp := &DisorderlandDropGroupInfos{}
	fileName := filepath.Join(dir, "disorderland_drop_group_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.groups = make(map[uint32][]*DisorderlandDropGroupInfo)

	for _, data := range tmp.Datas {
		data.prepare()
		m.groups[data.Group] = append(m.groups[data.Group], data)
	}
	return nil
}

func (m *DisorderlandDropGroupInfoManager) IsExist(group uint32) bool {
	if _, exist := m.groups[group]; exist {
		return true
	}

	return false
}

// GetAwardWithImprove 提升的情况下获取 award
// param@: group 掉落组
// param@: runePos 丰碑中符文的位置
// param@: runeNextLevel 丰碑中符文等级的下一等级
//
// 掉落组中相同位置有此等级的符文，直接掉落对应的道具
// 掉落组中相同位置没有此等级的符文，掉落配置中位置相等，等级最低的道具
func (m *DisorderlandDropGroupInfoManager) GetAwardWithImprove(group, runePos, runeNextLevel uint32) (uint32, []*cl.Resource) {
	datas := m.groups[group]
	if len(datas) == 0 {
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}
	var (
		target *DisorderlandDropGroupInfo
		min    *DisorderlandDropGroupInfo
	)
	for _, data := range datas {
		// 掉落组中相同位置有此等级的符文，直接掉落对应的道具
		if data.Position == runePos && data.Level == runeNextLevel {
			target = data
			break
		}
		// 掉落组中相同位置没有此等级的符文，掉落配置中位置相等，等级最低的道具
		if data.Position == runePos {
			if min == nil {
				min = data
			} else {
				if min.Level > data.Level {
					min = data
				}
			}
		}
	}

	if target == nil {
		if min == nil {
			return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
		}

		return uint32(ret.RET_OK), min.ClRes
	}

	return uint32(ret.RET_OK), target.ClRes
}

// RandAwardWithNoImprove : 不提升的情况下获取 award
// param@: group 掉落组
// param@: runePos 丰碑中符文的位置
// param@: runeNextLevel 丰碑中符文当前等级
//
// 在掉落组中，取位置相等｜位置为0, 且配置等级≤目标等级的所有掉落物品中，按照权重随机一个进行掉落
func (m *DisorderlandDropGroupInfoManager) RandAwardWithNoImprove(rd *rand.Rand, group, runePos, runeLevel uint32) (uint32, []*cl.Resource) {
	datas := m.groups[group]
	if len(datas) == 0 {
		return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
	}
	var (
		totalWight uint32
		pool       []*DisorderlandDropGroupInfo
	)

	for _, data := range datas {
		if (data.Position == runePos || data.Position == 0) && data.Level <= runeLevel {
			totalWight += data.Chance
			pool = append(pool, data)
		}
	}

	return m.randomAward(rd, totalWight, pool)
}

func (m *DisorderlandDropGroupInfoManager) randomAward(rd *rand.Rand, totalWeight uint32, pool []*DisorderlandDropGroupInfo) (uint32, []*cl.Resource) {
	if totalWeight == 0 {
		return 0, nil
	}
	randNum := rd.Intn(int(totalWeight))
	var curTotal uint32
	for _, v := range pool {
		curTotal += v.Chance
		if uint32(randNum) < curTotal {
			return uint32(ret.RET_OK), v.ClRes
		}
	}

	return uint32(ret.RET_SYSTEM_DATA_ERROR), nil
}
