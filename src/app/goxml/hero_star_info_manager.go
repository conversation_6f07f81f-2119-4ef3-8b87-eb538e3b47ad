package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type HeroStarInfoManager struct {
	xmlData            *XmlData
	Datas              map[uint32]map[uint32]map[uint32]*HeroStarInfoExt //star rare race
	rareRaceMinMaxStar map[uint32]map[uint32]*minMaxStar                 //rare race
	maxStar            uint32                                            // 最大星级
}

type HeroStarInfoExt struct {
	Star              uint32                          //int:真实星级
	Rare              uint32                          //int:英雄品质
	Race              uint32                          //int:英雄种族
	HpPct             int64                           //int:生命%
	AttackPct         int64                           //int:攻击%
	PhyDefPct         int64                           //int:物防%
	MagDefPct         int64                           //int:魔防%
	FixedDamPct       int64                           //int:固定伤害%
	FixedDamReducePct int64                           //int:固定免伤%
	SpeedPct          int64                           //int:速度%
	HeroCosts         []*HeroRes                      //升星消耗的卡牌
	ResAwards         []*cl.Resource                  //分解返还的资源
	JobBaseAttr       map[uint32]*HeroJobStarBaseAttr //各职业升星基础属性
	LinkNums          []uint32                        //影响的羁绊点数
}

type minMaxStar struct {
	minStar uint32
	maxStar uint32
}

type HeroJobStarBaseAttr struct {
	Hp     int64
	Attack int64
	PhyDef int64
	MagDef int64
}

func newHeroStarInfoManager(xmlData *XmlData) *HeroStarInfoManager {
	m := &HeroStarInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HeroStarInfoManager) name() string {
	return "HeroStarInfo"
}

func (m *HeroStarInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HeroStarInfoManager) checkData() error {
	return nil
}

func (m *HeroStarInfoManager) Load(dir string, isShow bool) error {
	return m.load(dir, isShow)
}

func (m *HeroStarInfoManager) load(dir string, isShow bool) error {
	tmp := &HeroStarInfos{}
	fileName := filepath.Join(dir, "hero_star_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]map[uint32]map[uint32]*HeroStarInfoExt)
	}
	m.rareRaceMinMaxStar = make(map[uint32]map[uint32]*minMaxStar)
	for _, data := range tmp.Datas {
		data.prepare()

		dataExt := &HeroStarInfoExt{
			Star:              data.Star,
			Rare:              data.Rare,
			Race:              data.Race,
			HpPct:             data.HpPct,
			AttackPct:         data.AttackPct,
			PhyDefPct:         data.PhyDefPct,
			MagDefPct:         data.MagDefPct,
			FixedDamPct:       data.FixedDamPct,
			FixedDamReducePct: data.FixedDamReducePct,
			SpeedPct:          data.SpeedPct,
			HeroCosts:         make([]*HeroRes, 0, HeroStarUpCostHeroNum),
			ResAwards:         make([]*cl.Resource, 0, HeroDecomposeAwardResNum),
			JobBaseAttr:       make(map[uint32]*HeroJobStarBaseAttr),
		}

		// 升星消耗英雄卡牌
		if data.CardType1 > 0 && data.CardCount1 > 0 {
			if !HeroStarUpCostLegalType[data.CardType1] {
				panic(fmt.Sprintf("load config %s fail: %d CardType1 err", fileName, data.Star))
			}
			//对可回退的英雄，做基础材料星级的验证
			if data.Star >= HeroCanBackMinInitStar && !HeroStarUpCostLegalStar[data.CardStar1] {
				panic(fmt.Sprintf("load config %s fail: %d CardStar1 err", fileName, data.Star))
			}
			for i := uint32(0); i < data.CardCount1; i++ {
				dataExt.HeroCosts = append(dataExt.HeroCosts, GenHeroRes(data.CardType1, data.CardStar1))
			}
		}
		if data.CardType2 > 0 && data.CardCount2 > 0 {
			if !HeroStarUpCostLegalType[data.CardType2] {
				panic(fmt.Sprintf("load config %s fail: %d CardType2 err", fileName, data.Star))
			}
			//对可回退的英雄，做基础材料星级的验证
			if data.Star >= HeroCanBackMinInitStar && !HeroStarUpCostLegalStar[data.CardStar2] {
				panic(fmt.Sprintf("load config %s fail: %d CardStar2 err", fileName, data.Star))
			}
			for i := uint32(0); i < data.CardCount2; i++ {
				dataExt.HeroCosts = append(dataExt.HeroCosts, GenHeroRes(data.CardType2, data.CardStar2))
			}
		}
		if data.CardType3 > 0 && data.CardCount3 > 0 {
			if !HeroStarUpCostLegalType[data.CardType3] {
				panic(fmt.Sprintf("load config %s fail: %d CardType3 err", fileName, data.Star))
			}
			//对可回退的英雄，做基础材料星级的验证
			if data.Star >= HeroCanBackMinInitStar && !HeroStarUpCostLegalStar[data.CardStar3] {
				panic(fmt.Sprintf("load config %s fail: %d CardStar3 err", fileName, data.Star))
			}
			for i := uint32(0); i < data.CardCount3; i++ {
				dataExt.HeroCosts = append(dataExt.HeroCosts, GenHeroRes(data.CardType3, data.CardStar3))
			}
		}
		if data.CardType4 > 0 && data.CardCount4 > 0 {
			if !HeroStarUpCostLegalType[data.CardType4] {
				panic(fmt.Sprintf("load config %s fail: %d CardType4 err", fileName, data.Star))
			}
			//对可回退的英雄，做基础材料星级的验证
			if data.Star >= HeroCanBackMinInitStar && !HeroStarUpCostLegalStar[data.CardStar4] {
				panic(fmt.Sprintf("load config %s fail: %d CardStar4 err", fileName, data.Star))
			}
			for i := uint32(0); i < data.CardCount4; i++ {
				dataExt.HeroCosts = append(dataExt.HeroCosts, GenHeroRes(data.CardType4, data.CardStar4))
			}
		}

		// 英雄分解资源返还
		dataExt.ResAwards = data.DecClRes

		//职业基础属性
		dataExt.JobBaseAttr[HeroJobTank] = &HeroJobStarBaseAttr{
			data.Hp1,
			data.Attack1,
			data.PhyDef1,
			data.MagDef1,
		}
		dataExt.JobBaseAttr[HeroJobMaster] = &HeroJobStarBaseAttr{
			data.Hp2,
			data.Attack2,
			data.PhyDef2,
			data.MagDef2,
		}
		dataExt.JobBaseAttr[HeroJobSoldier] = &HeroJobStarBaseAttr{
			data.Hp3,
			data.Attack3,
			data.PhyDef3,
			data.MagDef3,
		}
		dataExt.JobBaseAttr[HeroJobAuxiliary] = &HeroJobStarBaseAttr{
			data.Hp4,
			data.Attack4,
			data.PhyDef4,
			data.MagDef4,
		}

		dataExt.LinkNums = append(dataExt.LinkNums, data.Link1Num)
		dataExt.LinkNums = append(dataExt.LinkNums, data.Link2Num)
		dataExt.LinkNums = append(dataExt.LinkNums, data.Link3Num)
		dataExt.LinkNums = append(dataExt.LinkNums, data.Link4Num)
		dataExt.LinkNums = append(dataExt.LinkNums, data.Link5Num)

		if starData, exist := m.Datas[data.Star]; exist {
			if rareData, exist := starData[data.Rare]; exist {
				rareData[data.Race] = dataExt
			} else {
				raceData := make(map[uint32]*HeroStarInfoExt)
				raceData[data.Race] = dataExt
				starData[data.Rare] = raceData
			}
		} else {
			raceData := make(map[uint32]*HeroStarInfoExt)
			raceData[data.Race] = dataExt
			rareData := make(map[uint32]map[uint32]*HeroStarInfoExt)
			rareData[data.Rare] = raceData
			m.Datas[data.Star] = rareData
		}

		raceMinMaxStar, exist := m.rareRaceMinMaxStar[data.Rare]
		if !exist {
			m.rareRaceMinMaxStar[data.Rare] = make(map[uint32]*minMaxStar)
			raceMinMaxStar = m.rareRaceMinMaxStar[data.Rare]
		}

		minMaxStarInfo, exist := raceMinMaxStar[data.Race]
		if !exist {
			raceMinMaxStar[data.Race] = &minMaxStar{
				minStar: data.Star,
				maxStar: data.Star,
			}
		} else {
			if data.Star > minMaxStarInfo.maxStar {
				minMaxStarInfo.maxStar = data.Star
			}
			if data.Star < minMaxStarInfo.minStar {
				minMaxStarInfo.minStar = data.Star
			}
		}

		if data.Star > m.maxStar {
			m.maxStar = data.Star
		}
	}
	return nil
}

func (m *HeroStarInfoManager) Index(star uint32, rare uint32, race uint32) *HeroStarInfoExt {
	return m.Datas[star][rare][race]
}

func (m *HeroStarInfoManager) GetJobBaseAttr(starInfo *HeroStarInfoExt, job uint32) *HeroJobStarBaseAttr {
	return starInfo.JobBaseAttr[job]
}

func (m *HeroStarInfoManager) GetMinMaxStar(rare uint32, race uint32) *minMaxStar {
	return m.rareRaceMinMaxStar[rare][race]
}

func (m *HeroStarInfoManager) GetMaxStar() uint32 {
	return m.maxStar
}

func (h *HeroStarInfoExt) GetLink(hero *HeroInfoExt) map[uint32]uint32 {
	link := make(map[uint32]uint32)
	if h.LinkNums[0] != 0 {
		link[hero.Link1ID] += h.LinkNums[0]
	}
	if h.LinkNums[1] != 0 {
		link[hero.Link2ID] += h.LinkNums[1]
	}
	if h.LinkNums[2] != 0 {
		link[hero.Link3ID] += h.LinkNums[2]
	}
	if h.LinkNums[3] != 0 {
		link[hero.Link4ID] += h.LinkNums[3]
	}
	if h.LinkNums[4] != 0 {
		link[hero.Link5ID] += h.LinkNums[4]
	}
	return link
}
