package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type HeroFetterInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]*HeroFetterInfoExt
	HeroArchive map[uint32][]uint32
}

type HeroFetterInfoExt struct {
	Id      uint32
	Rare    uint32
	HeroIds []uint32
	Attrs   []uint32
}

func newHeroFetterInfoManager(xmlData *XmlData) *HeroFetterInfoManager {
	m := &HeroFetterInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HeroFetterInfoManager) name() string {
	return "HeroFetterInfo"
}

func (m *HeroFetterInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HeroFetterInfoManager) checkData() error {
	return nil
}

func (m *HeroFetterInfoManager) load(dir string, isShow bool) error {
	tmp := &HeroFetterInfos{}
	fileName := filepath.Join(dir, "hero_fetter_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*HeroFetterInfoExt)
	}

	m.HeroArchive = make(map[uint32][]uint32)
	for _, data := range tmp.Datas {
		dataExt := &HeroFetterInfoExt{}
		dataExt.Id = data.Id
		dataExt.Rare = data.Rare
		if sysId := data.Hero1; sysId > 0 {
			dataExt.checkHeroId(m.xmlData, fileName, sysId)
			dataExt.HeroIds = append(dataExt.HeroIds, sysId)
			m.HeroArchive[sysId] = append(m.HeroArchive[sysId], data.Id)
		}
		if sysId := data.Hero2; sysId > 0 {
			dataExt.checkHeroId(m.xmlData, fileName, sysId)
			dataExt.HeroIds = append(dataExt.HeroIds, sysId)
			m.HeroArchive[sysId] = append(m.HeroArchive[sysId], data.Id)
		}
		if sysId := data.Hero3; sysId > 0 {
			dataExt.checkHeroId(m.xmlData, fileName, sysId)
			dataExt.HeroIds = append(dataExt.HeroIds, sysId)
			m.HeroArchive[sysId] = append(m.HeroArchive[sysId], data.Id)
		}
		if sysId := data.Hero4; sysId > 0 {
			dataExt.checkHeroId(m.xmlData, fileName, sysId)
			dataExt.HeroIds = append(dataExt.HeroIds, sysId)
			m.HeroArchive[sysId] = append(m.HeroArchive[sysId], data.Id)
		}
		if sysId := data.Hero5; sysId > 0 {
			dataExt.checkHeroId(m.xmlData, fileName, sysId)
			dataExt.HeroIds = append(dataExt.HeroIds, sysId)
			m.HeroArchive[sysId] = append(m.HeroArchive[sysId], data.Id)
		}
		if len(dataExt.HeroIds) == 0 {
			panic(fmt.Sprintf("check data error fileName:%s id:%d have no hero",
				fileName, data.Id))
		}
		if data.AttrType1 > 0 && data.AttrValue1 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType1)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrValue2 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType2)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue2)
		}
		if data.AttrType3 > 0 && data.AttrValue3 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType3)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue3)
		}
		if data.AttrType4 > 0 && data.AttrValue4 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType4)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue4)
		}

		if ptr, exist := m.Datas[dataExt.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[dataExt.Id] = dataExt
		}
	}
	return nil
}

func (e *HeroFetterInfoExt) checkHeroId(xmlData *XmlData, fileName string, sysId uint32) bool {
	if xmlData.HeroInfoM.Index(sysId) == nil {
		panic(fmt.Sprintf("check data error fileName:%s id:%d hero not exist",
			fileName, e.Id))
	}
	return true
}

func (m *HeroFetterInfoManager) GetRecordById(id uint32) *HeroFetterInfoExt {
	return m.Datas[id]
}

func (m *HeroFetterInfoManager) Index(id uint32) *HeroFetterInfoExt {
	return m.Datas[id]
}

func (m *HeroFetterInfoManager) GetHeroFetter(id uint32) []uint32 {
	return m.HeroArchive[id]
}

func (m *HeroFetterInfoManager) GetDataNum() int {
	return len(m.Datas)
}
