package goxml

import (
	"path/filepath"
	"slices"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"

	"gitlab.qdream.com/kit/sea/math/rand"

	"fmt"
)

type MazeBuffInfoManager struct {
	xmlData        *XmlData
	Datas          map[uint32]*MazeBuffInfoExt
	BuffTypes      map[uint32][]*MazeBuffInfoExt            // key: buff type
	alterBuffs     map[uint32]*MazeBuffAlter                // key: maze level
	roundTypeBuffs map[uint32]map[uint32][]*MazeBuffInfoExt // roundType roundEffect
}

type MazeBuffAlter struct {
	linkSort map[uint32]uint32 // key: linkID -> value: sort

	linkGroup            []*MazeBuffLink // link group pool
	linkGroupTotalChance uint32          // link group total chance

	LinkBuffPool map[uint32][]*MazeBuffInfoExt // key: linkID

	allAlterBuffPool []*MazeBuffInfoExt
}

type MazeBuffInfoExt struct {
	Id          uint32
	Type        uint32 //int:buff类型
	Chance      uint32 //int:buff随机权重
	Limit       uint32 //int:是否有场数限制
	LimitCount  uint32 //int:持续战斗场数
	Value       uint32 //int:效果值
	Tag         uint32
	AttrExplore uint32        //int:属性关联地图探索
	RaisePSs    []uint64      //被动技能
	AttrMap     map[int]int64 // 属性修正
	Link        uint32        // link id
	Sort        uint32        // link 随机优先级
	Hero        uint32
	Round       uint32
	RoundType   uint32
	EffectType  uint32
}

type MazeBuffLink struct {
	LinkID uint32
	Chance uint32
}

func newMazeBuffAlter() *MazeBuffAlter {
	return &MazeBuffAlter{
		linkSort:     make(map[uint32]uint32),
		LinkBuffPool: make(map[uint32][]*MazeBuffInfoExt),
	}
}

func (m *MazeBuffLink) GetWeight() int {
	return int(m.Chance)
}

func newMazeBuffInfoManager(xmlData *XmlData) *MazeBuffInfoManager {
	m := &MazeBuffInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MazeBuffInfoManager) name() string {
	return "MazeBuffInfo"
}

func (m *MazeBuffInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MazeBuffInfoManager) checkData() error {
	return nil
}

func (m *MazeBuffInfoManager) load(dir string, isShow bool) error {
	tmp := &MazeBuffInfos{}
	fileName := filepath.Join(dir, "maze_buff_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*MazeBuffInfoExt, len(tmp.Datas))
	}

	m.BuffTypes = make(map[uint32][]*MazeBuffInfoExt)
	m.alterBuffs = make(map[uint32]*MazeBuffAlter)
	m.roundTypeBuffs = make(map[uint32]map[uint32][]*MazeBuffInfoExt)
	checkSort := make(map[uint32]uint32)
	m.initMazeBuffAlter()
	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &MazeBuffInfoExt{
			Id:          data.Id,
			Type:        data.Type,
			Chance:      data.Chance,
			Limit:       data.Limit,
			LimitCount:  data.LimitCount,
			Value:       data.Value,
			Tag:         data.Tag,
			AttrExplore: data.AttrExplore,
			AttrMap:     make(map[int]int64),
			Link:        data.Link,
			Sort:        data.Sort,
			Hero:        data.Hero,
			Round:       data.Round,
			RoundType:   data.RoundType,
			EffectType:  data.EffectType,
		}

		if data.RaisePassiveSkill1 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill1))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill2 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill2, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill2))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill3 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill3, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill3))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}

		if data.AttrType1 > 0 && data.AttrValue1 > 0 {
			dataExt.AttrMap[int(data.AttrType1)] = int64(data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrValue2 > 0 {
			dataExt.AttrMap[int(data.AttrType2)] = int64(data.AttrValue2)
		}
		if data.AttrType3 > 0 && data.AttrValue3 > 0 {
			dataExt.AttrMap[int(data.AttrType3)] = int64(data.AttrValue3)
		}
		if data.DecAttrType1 > 0 {
			dataExt.AttrMap[int(data.DecAttrType1)] = data.DecAttrValue1
		}

		if data.Link > 0 {
			if data.Sort == 0 {
				panic(fmt.Sprintf("check config %s err. sort is zero. id: %d", fileName, data.Id))
			}
			if _, exist := checkSort[data.Sort]; exist {
				if checkSort[data.Sort] != data.Link {
					panic(fmt.Sprintf("check config %s err. same sort's linkID not the same. id: %d", fileName, data.Id))
				}
			}
			checkSort[data.Sort] = data.Link

			if data.Type != MazeSoulBuffType {
				panic(fmt.Sprintf("check config %s err. buff type is invalid. id: %d, type:%d", fileName, data.Id, data.Type))
			}

			if data.Difficulty != MazeMapLevelCommon && data.Difficulty != MazeMapLevelDifficult && data.Difficulty != MazeMapLevelHell {
				panic(fmt.Sprintf("check config %s err. alter buff difficulty is invalid. id: %d, type:%d", fileName, data.Id, data.Difficulty))
			}

			alterBuffIns := m.alterBuffs[data.Difficulty]
			if _, exist := alterBuffIns.linkSort[data.Link]; exist {
				if alterBuffIns.linkSort[data.Link] != data.Sort {
					panic(fmt.Sprintf("check config %s err. same linkID's sore not the same. id: %d", fileName, data.Id))
				}
			}
			alterBuffIns.linkSort[data.Link] = data.Sort
			alterBuffIns.LinkBuffPool[data.Link] = append(alterBuffIns.LinkBuffPool[data.Link], dataExt)
			alterBuffIns.allAlterBuffPool = append(alterBuffIns.allAlterBuffPool, dataExt)
		}
		if dataExt.Round > 0 {
			_, exist := m.roundTypeBuffs[dataExt.RoundType]
			if !exist {
				m.roundTypeBuffs[dataExt.RoundType] = make(map[uint32][]*MazeBuffInfoExt)
			}
			m.roundTypeBuffs[dataExt.RoundType][data.EffectType] = append(m.roundTypeBuffs[dataExt.RoundType][data.EffectType], dataExt)
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}

		if data.Chance > 0 {
			m.BuffTypes[data.Type] = append(m.BuffTypes[data.Type], dataExt)
		}
	}

	if len(m.alterBuffs) != 3 {
		panic(fmt.Sprintf("check config %s err. alter buff difficulty num not match. ", fileName))

	}

	for _, v := range m.alterBuffs {
		for linkID := range v.linkSort {
			v.linkGroup = append(v.linkGroup, &MazeBuffLink{
				LinkID: linkID,
				Chance: 10,
			})
			v.linkGroupTotalChance += 10
		}
	}
	return nil
}

func (m *MazeBuffInfoManager) initMazeBuffAlter() {
	m.alterBuffs[MazeMapLevelCommon] = newMazeBuffAlter()
	m.alterBuffs[MazeMapLevelDifficult] = newMazeBuffAlter()
	m.alterBuffs[MazeMapLevelHell] = newMazeBuffAlter()
}

func (m *MazeBuffInfoManager) GetRecordById(id uint32) *MazeBuffInfoExt {
	return m.Datas[id]
}

func (m *MazeBuffInfoManager) Index(key uint32) *MazeBuffInfoExt {
	return m.Datas[key]
}

func (m *MazeBuffInfoManager) IsAlterLinkBuff(buffID uint32) bool {
	data := m.Datas[buffID]
	if data == nil {
		return false
	}

	return data.Link > 0
}

// 获取没有获得的祭坛类型的buffId
func (m *MazeBuffInfoManager) getNoReceivedBuff(receivedBuffs map[uint32]struct{}, buffPool []*MazeBuffInfoExt) []*MazeBuffInfoExt {
	alterBuffs := make([]*MazeBuffInfoExt, 0, len(buffPool))
	if len(receivedBuffs) == 0 {
		alterBuffs = buffPool
		return alterBuffs
	}

	for _, buff := range buffPool {
		if _, exist := receivedBuffs[buff.Id]; !exist {
			alterBuffs = append(alterBuffs, buff)
		}
	}

	return alterBuffs
}

func (m *MazeBuffInfoManager) randomBuffID(rand *rand.Rand, linkID uint32, receivedBuffs map[uint32]struct{}, buffPool []*MazeBuffInfoExt) uint32 {
	buffs := m.getNoReceivedBuff(receivedBuffs, buffPool)
	if len(buffs) == 0 {
		return 0
	}

	totalChance := m.calcTotalChance(buffs)
	if totalChance == uint32(0) {
		l4g.Errorf("GenBuffID: random generate buffID failed. totalChance is zero. linkID: %d", linkID)
		return 0
	}

	randNum := rand.Intn(int(totalChance))
	var curTotal uint32
	for _, v := range buffs {
		curTotal += v.Chance
		if uint32(randNum) < curTotal {
			return v.Id
		}
	}

	return 0
}

func (m *MazeBuffInfoManager) calcTotalChance(buffs []*MazeBuffInfoExt) (totalChance uint32) {
	for _, buff := range buffs {
		totalChance += buff.Chance
	}

	return totalChance
}

func (m *MazeBuffInfoManager) randomLinks(rd *rand.Rand, linkPool []*MazeBuffLink, totalChance, num uint32) []uint32 {
	if num == 0 {
		l4g.Error("Generate link failed.  random num is zero.")
		return nil
	}

	if totalChance == 0 {
		l4g.Error("Generate link failed.  totalChance is zero.")
		return nil
	}

	if int(num) > len(linkPool) {
		l4g.Error("Generate link failed. num error %d %d", num, len(linkPool))
		return nil
	}

	data, err := RandomSomeNoRepeat(rd, int(num), int(totalChance), linkPool)
	if err != nil {
		l4g.Error("Generate link failed. %s", err)
		return nil
	}
	alterLinks := make([]uint32, 0, num)
	for _, link := range data {
		alterLinks = append(alterLinks, link.LinkID)
	}

	return alterLinks
}

func (m *MazeBuffInfoManager) GenBuffID(rd *rand.Rand, mapLevel, linkID uint32, receivedBuffs map[uint32]struct{}) []uint32 {
	alterBuffIns := m.alterBuffs[mapLevel]
	if alterBuffIns == nil {
		l4g.Errorf("GenBuffID: mapLevel not exist in m.alterBuffs. level: %d", mapLevel)
		return nil
	}

	buffIDs := make([]uint32, 0, m.xmlData.MazeConstantInfoM.AlterBuffNum)
	var links []uint32
	if linkID > 0 {
		linkPool, totalChance := m.convertLinkPoolAndTotalChance(linkID, alterBuffIns)
		links = m.randomLinks(rd, linkPool, totalChance, m.xmlData.MazeConstantInfoM.AlterBuffNum-1)
		links = append(links, linkID)
	} else {
		links = m.randomLinks(rd, alterBuffIns.linkGroup, alterBuffIns.linkGroupTotalChance, m.xmlData.MazeConstantInfoM.AlterBuffNum)
	}

	if uint32(len(links)) != m.xmlData.MazeConstantInfoM.AlterBuffNum {
		l4g.Errorf("GenBuffID: random link group num not match alter buff num. num: %d", len(links))
		return nil
	}

	for _, id := range links {
		buffID := m.randomBuffID(rd, id, receivedBuffs, alterBuffIns.LinkBuffPool[id])
		if buffID == 0 {
			buffID = m.randomBuffID(rd, id, receivedBuffs, alterBuffIns.allAlterBuffPool)
			if buffID == 0 {
				return nil
			}
		}
		buffIDs = append(buffIDs, buffID)
	}

	return buffIDs
}

func (m *MazeBuffInfoManager) convertLinkPoolAndTotalChance(linkID uint32, alterBuffIns *MazeBuffAlter) ([]*MazeBuffLink, uint32) {
	newLinkPool := make([]*MazeBuffLink, 0, len(alterBuffIns.linkGroup)-1)
	newTotalChance := uint32(0)
	for _, link := range alterBuffIns.linkGroup {
		if link.LinkID != linkID {
			newLinkPool = append(newLinkPool, link)
		} else {
			newTotalChance = alterBuffIns.linkGroupTotalChance - link.Chance
		}
	}

	return newLinkPool, newTotalChance
}

func (m *MazeBuffInfoManager) SearchMatchLink(mapLevel uint32, links []*LinkSkillInfoExt) uint32 {
	alterBuffIns := m.alterBuffs[mapLevel]
	if alterBuffIns == nil {
		l4g.Errorf("SearchMatchLink: mapLevel not exist in m.alterBuffs. level: %d", mapLevel)
		return 0
	}

	pool := make([]*LinkSkillInfoExt, 0, len(links))
	for _, link := range links {
		if _, exist := alterBuffIns.linkSort[link.LinkId]; exist {
			linkInfo := m.xmlData.LinkInfoM.Index(link.LinkId)
			if linkInfo != nil && linkInfo.LinkSubtype == LinkTypeCommon {
				pool = append(pool, link)
			}
		}
	}

	if len(pool) == 0 {
		return 0
	}

	var matchLink *LinkSkillInfoExt
	for _, link := range pool {
		if matchLink == nil {
			matchLink = link
		}

		if matchLink.LinkId != link.LinkId {
			if matchLink.Lv < link.Lv {
				matchLink = link
			} else if matchLink.Lv == link.Lv {
				matchSort := alterBuffIns.linkSort[matchLink.LinkId]
				linkSort := alterBuffIns.linkSort[link.LinkId]
				if matchSort != 0 && linkSort != 0 && matchSort < linkSort {
					matchLink = link
				}
			}
		}
	}

	if matchLink != nil {
		return matchLink.LinkId
	}

	return 0
}

func (m *MazeBuffInfoManager) filterBuff(buffInfos []*MazeBuffInfoExt, receivedBuffs map[uint32]struct{}, formationHeroes map[uint32]struct{}, beSelectBuff []uint32) []*MazeBuffInfoExt {
	if len(receivedBuffs) == 0 && len(formationHeroes) == 0 && len(beSelectBuff) == 0 {
		return buffInfos
	}
	tmp := make([]*MazeBuffInfoExt, 0, len(buffInfos))
	for _, buffInfo := range buffInfos {
		if buffInfo == nil {
			continue
		}
		_, exist := receivedBuffs[buffInfo.Id]
		if exist {
			continue
		}

		if slices.Contains(beSelectBuff, buffInfo.Id) {
			continue
		}

		if len(formationHeroes) > 0 {
			_, exist = formationHeroes[buffInfo.Hero]
			if exist {
				tmp = append(tmp, buffInfo)
			}
		} else {
			tmp = append(tmp, buffInfo)
		}
	}
	return tmp
}

func (m *MazeBuffInfoManager) SelectSuccessBuff(rd *rand.Rand, receivedBuffs map[uint32]struct{}, formationHeroes map[uint32]struct{}) []uint32 {
	ret := make([]uint32, 0, 3)
	for i := 0; i < 3; i++ {
		switch i {
		case 0:
			var buffID uint32
			if len(formationHeroes) > 0 {
				heroBuffTmp := m.filterBuff(m.roundTypeBuffs[1][1], receivedBuffs, formationHeroes, ret)
				if len(heroBuffTmp) > 0 {
					index := rd.RandBetween(0, len(heroBuffTmp)-1)
					buffID = heroBuffTmp[index].Id
					ret = append(ret, buffID)
					continue
				}
			}
			//专属的没随出来继续
			heroBuffTmp := m.filterBuff(m.roundTypeBuffs[1][1], receivedBuffs, nil, ret)
			if len(heroBuffTmp) > 0 {
				index := rd.RandBetween(0, len(heroBuffTmp)-1)
				buffID = heroBuffTmp[index].Id
				ret = append(ret, buffID)
				continue
			}
		case 1:
			var buffID uint32
			heroBuffTmp := m.filterBuff(m.roundTypeBuffs[1][1], receivedBuffs, nil, ret)
			if len(heroBuffTmp) > 0 {
				index := rd.RandBetween(0, len(heroBuffTmp)-1)
				buffID = heroBuffTmp[index].Id
				ret = append(ret, buffID)
				continue
			}
		case 2:
			var buffID uint32
			heroBuffTmp := m.filterBuff(m.roundTypeBuffs[1][2], receivedBuffs, nil, ret)
			if len(heroBuffTmp) > 0 {
				index := rd.RandBetween(0, len(heroBuffTmp)-1)
				buffID = heroBuffTmp[index].Id
				ret = append(ret, buffID)
				continue
			}
		}
	}
	return ret
}

func (m *MazeBuffInfoManager) SelectFailedBuff(rd *rand.Rand, receivedBuffs map[uint32]struct{}) uint32 {
	var buffID uint32
	randIndex := rd.RandBetween(1, 2)
	heroBuffTmp := m.filterBuff(m.roundTypeBuffs[2][uint32(randIndex)], receivedBuffs, nil, nil)
	if len(heroBuffTmp) > 0 {
		index := rd.RandBetween(0, len(heroBuffTmp)-1)
		buffID = heroBuffTmp[index].Id
	}
	return buffID
}
