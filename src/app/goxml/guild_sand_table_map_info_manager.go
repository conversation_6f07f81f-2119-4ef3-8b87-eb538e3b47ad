package goxml

import (
	"fmt"
	"path/filepath"
	"strconv"
	"strings"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildSandTableMapInfoManager struct {
	xmlData   *XmlData
	GridDatas map[uint32]*MapGroundInfo //id =>
	MapDatas  map[uint32]*MapInfoExt    //mapid =>
}

type MapInfoExt struct {
	MainBases []*MapGroundInfo
	Monsters  []*MapGroundInfo
	Arena     *MapGroundInfo
}

type MapGroundInfo struct {
	ID            uint32
	X             uint32
	Y             uint32
	MasterID      uint32          //如果是从属地,主地块的位置
	SlaveIDs      []uint32        //如果是主地块,从属地块的位置
	UnlockGrounds []uint32        //屏障解锁需要的地块
	Stages        []*MapGroundExt //
	LinkAddId     uint32          //结算羁绊加成
	MapID         uint32
	UnlockLround  uint32
	Ores          []uint32
}

func (m *MapGroundInfo) CanUnlock(lround uint32) bool {
	return lround >= m.UnlockLround
}

func (m *MapGroundInfo) IsOreExist(oreID uint32) bool {
	for _, ore := range m.Ores {
		if ore == oreID {
			return true
		}
	}
	return false
}

type MapGroundExt struct {
	LandID      uint32 //地块类型id
	MonsterSort uint32 //怪物组
	MonsterNum  uint32 //怪物队伍数量
}

func newGuildSandTableMapInfoManager(xmlData *XmlData) *GuildSandTableMapInfoManager {
	m := &GuildSandTableMapInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableMapInfoManager) name() string {
	return "guild_sand_table_map_info"
}

func (m *GuildSandTableMapInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableMapInfoManager) checkData() error {
	return nil
}

func (g *GuildSandTableMapInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableMapInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_map_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	g.MapDatas = make(map[uint32]*MapInfoExt)
	g.GridDatas = make(map[uint32]*MapGroundInfo)

	for _, data := range tmp.Datas {
		mapInfo := g.MapDatas[data.MapId]
		if mapInfo == nil {
			mapInfo = &MapInfoExt{}
			g.MapDatas[data.MapId] = mapInfo
		}
		groundInfo := &MapGroundInfo{
			ID:           data.Id,
			LinkAddId:    data.LinkAddId,
			X:            data.X,
			Y:            data.Y,
			MapID:        data.MapId,
			UnlockLround: data.UnlockTime,
		}
		if len(data.SuperiorCoordinate) > 0 {
			ids := g.ParseIDs(data.SuperiorCoordinate)
			groundInfo.MasterID = ids[0]
		}
		if len(data.SubordinateCoordinate) > 0 {
			ids := g.ParseIDs(data.SubordinateCoordinate)
			groundInfo.SlaveIDs = ids
		}
		if len(data.UnlockCoordinateGroup) > 0 {
			ids := g.ParseIDs(data.UnlockCoordinateGroup)
			groundInfo.UnlockGrounds = ids
		}
		//反向添加,从3=>1进行变化
		if data.PhotomaskLandId != 0 {
			groundExt := &MapGroundExt{
				LandID: data.PhotomaskLandId,
			}
			landInfo := g.xmlData.GuildSandTableLandInfoM.Index(data.PhotomaskLandId)
			if landInfo == nil {
				panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "cant find landID", data.PhotomaskLandId))
			}
			groundInfo.Stages = append(groundInfo.Stages, groundExt)
		}
		if data.AnimalLandId != 0 {
			groundExt := &MapGroundExt{
				LandID:      data.AnimalLandId,
				MonsterSort: data.AnimalMonsterSort,
			}
			if groundExt.MonsterSort != 0 {
				monsters := g.xmlData.GuildSandTableMonsterGroupInfoM.GetMonstersByMonsterSort(groundExt.MonsterSort)
				if monsters == nil {
					panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "cant find MonsterSort", groundExt.MonsterSort))
				}
				groundExt.MonsterNum = uint32(len(monsters))
				mapInfo.Monsters = append(mapInfo.Monsters, groundInfo)
			}
			landInfo := g.xmlData.GuildSandTableLandInfoM.Index(data.AnimalLandId)
			if landInfo == nil {
				panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "cant find landID", data.AnimalLandId))
			}
			groundInfo.Stages = append(groundInfo.Stages, groundExt)
		}
		if data.BuildLandId != 0 {
			groundExt := &MapGroundExt{
				LandID:      data.BuildLandId,
				MonsterSort: data.BuildMonsterSort,
			}
			if groundExt.MonsterSort != 0 {
				monsters := g.xmlData.GuildSandTableMonsterGroupInfoM.GetMonstersByMonsterSort(groundExt.MonsterSort)
				if monsters == nil {
					panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "cant find MonsterSort", groundExt.MonsterSort))
				}
				groundExt.MonsterNum = uint32(len(monsters))
			}
			landInfo := g.xmlData.GuildSandTableLandInfoM.Index(data.BuildLandId)
			if landInfo == nil {
				panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "cant find landID", data.BuildLandId))
			}
			groundInfo.Stages = append(groundInfo.Stages, groundExt)
			if landInfo.LandType == GuildSandTableLandTypeMainBase && len(data.SuperiorCoordinate) == 0 {
				mapInfo.MainBases = append(mapInfo.MainBases, groundInfo)
			}
			if landInfo.LandType == GuildSandTableLandTypeArena {
				mapInfo.Arena = groundInfo
			}
		} else if data.BlankLandId != 0 {
			groundExt := &MapGroundExt{
				LandID:      data.BlankLandId,
				MonsterSort: data.BlankMonsterSort,
			}
			if groundExt.MonsterSort != 0 {
				monsters := g.xmlData.GuildSandTableMonsterGroupInfoM.GetMonstersByMonsterSort(groundExt.MonsterSort)
				if monsters == nil {
					panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "cant find MonsterSort", groundExt.MonsterSort))
				}
				groundExt.MonsterNum = uint32(len(monsters))
			}

			landInfo := g.xmlData.GuildSandTableLandInfoM.Index(data.BlankLandId)
			if landInfo == nil {
				panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "cant find landID", data.BlankLandId))
			}
			groundInfo.Stages = append(groundInfo.Stages, groundExt)
		}

		if data.LinkAddId != 0 {
			raiseID := g.xmlData.GuildSandTableLinkInfoM.Index(data.LinkAddId)
			if raiseID == nil {
				panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "GuildSandTableLinkInfo LinkAddId", data.LinkAddId))
			}
		}

		// if data.OreSequence1 != 0 {
		// 	ore := g.xmlData.GuildSandTableOreLevelInfoM.Index(data.OreSequence1)
		// 	if ore == nil {
		// 		panic(fmt.Sprintf("load config %s fail: GuildSandTableOreLevelInfoM no find %d",
		// 			fileName, data.OreSequence1))
		// 	}
		// 	groundInfo.Ores = append(groundInfo.Ores, data.OreSequence1)
		// }
		// if data.OreSequence2 != 0 {
		// 	ore := g.xmlData.GuildSandTableOreLevelInfoM.Index(data.OreSequence2)
		// 	if ore == nil {
		// 		panic(fmt.Sprintf("load config %s fail: GuildSandTableOreLevelInfoM no find %d",
		// 			fileName, data.OreSequence2))
		// 	}
		// 	groundInfo.Ores = append(groundInfo.Ores, data.OreSequence2)
		// }
		// if data.OreSequence3 != 0 {
		// 	ore := g.xmlData.GuildSandTableOreLevelInfoM.Index(data.OreSequence3)
		// 	if ore == nil {
		// 		panic(fmt.Sprintf("load config %s fail: GuildSandTableOreLevelInfoM no find %d",
		// 			fileName, data.OreSequence3))
		// 	}
		// 	groundInfo.Ores = append(groundInfo.Ores, data.OreSequence3)
		// }
		// if data.OreSequence4 != 0 {
		// 	ore := g.xmlData.GuildSandTableOreLevelInfoM.Index(data.OreSequence4)
		// 	if ore == nil {
		// 		panic(fmt.Sprintf("load config %s fail: GuildSandTableOreLevelInfoM no find %d",
		// 			fileName, data.OreSequence4))
		// 	}
		// 	groundInfo.Ores = append(groundInfo.Ores, data.OreSequence4)
		// }

		g.GridDatas[data.Id] = groundInfo
	}

	for mapID, mapInfo := range g.MapDatas {
		if len(mapInfo.MainBases) != int(g.xmlData.GuildSandTableConfigInfoM.MapMatchNum) {
			panic(fmt.Sprintf("load config %s fail: %s%d", fileName, "map MainBaseNum EROR ", mapID))
		}
	}
	return nil
}

func (g *GuildSandTableMapInfoManager) GetMapInfoExt(key uint32) *MapInfoExt {
	return g.MapDatas[key]
}

func (g *GuildSandTableMapInfoManager) ParseIDs(data string) []uint32 {
	ids := make([]uint32, 0)
	strIDs := strings.Split(data, ",")
	for _, strID := range strIDs {
		id, err := strconv.ParseUint(strID, 10, 32)
		if err != nil {
			panic(err)
		}
		ids = append(ids, uint32(id))
	}
	return ids
}

func (g *GuildSandTableMapInfoManager) GetMapGroundInfo(id uint32) *MapGroundInfo {
	groundInfo := g.GridDatas[id]
	if groundInfo == nil {
		l4g.Errorf("GuildSandTableMapInfoM cant find. id:%d", id)
		return nil
	}
	return groundInfo
}

func (g *GuildSandTableMapInfoManager) GetMapGroundExt(id, groundID uint32) *MapGroundExt {
	groundInfo := g.GetMapGroundInfo(id)
	if groundInfo == nil {
		return nil
	}
	for _, mapGroundExt := range groundInfo.Stages {
		if mapGroundExt.LandID == groundID {
			return mapGroundExt
		}
	}
	return nil
}

func (g *GuildSandTableMapInfoManager) GetNextStage(id, groundID uint32) *MapGroundExt {
	groundInfo := g.GetMapGroundInfo(id)
	if groundInfo == nil {
		return nil
	}
	getNext := false
	for _, mapGroundExt := range groundInfo.Stages {
		if getNext {
			return mapGroundExt
		}
		if mapGroundExt.LandID == groundID {
			getNext = true
		}
	}
	return nil
}
