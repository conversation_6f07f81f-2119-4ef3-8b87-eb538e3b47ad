package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

const (
	EmblemAttrCount = 2
)

type EmblemInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*EmblemInfosExt
	Rare    map[uint32]*SameRareEmblemInfos // key: 稀有度
}

type SameRareEmblemInfos struct {
	SameRaceData map[uint32][]*EmblemInfosExt // key: 种族
}

type EmblemInfosExt struct {
	Id             uint32
	Rare           uint32 //稀有度
	Race           uint32 //种族
	Type           uint32 //纹章类型名
	Pos            uint32 //纹章位置
	SuitSkill      uint32 //套装索引
	IsHeroGroup    uint32 //是否有专属英雄
	HeroGroup      uint32 //专属英雄
	IsMagicSkill   uint32 //专属词条
	LevelMax       uint32 //等级上限
	LevelIndex     uint32 //等级索引
	StrengthCoe    uint32 //属性提升系数
	Attr           map[uint32]int64
	DecomposeClRes []*cl.Resource //分解返还道具
	AscendId       uint32         //升阶后id
}

func newEmblemInfoManager(xmlData *XmlData) *EmblemInfoManager {
	m := &EmblemInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *EmblemInfoManager) name() string {
	return "EmblemInfo"
}

func (m *EmblemInfoManager) ignoreForCheck() bool {
	return false
}

func (m *EmblemInfoManager) checkData() error {
	for _, data := range m.Datas {
		if data.IsHeroGroup == 1 {
			groupInfos := m.xmlData.EmblemHeroGroupInfoM.Group(data.HeroGroup)
			if len(groupInfos) == 0 {
				panic(fmt.Sprintf("load config emblem_info.xml fail: id:%d  heroGroup:%d config error", data.Id, data.HeroGroup))
			}
		}
	}
	return nil
}

func (m *EmblemInfoManager) load(dir string, isShow bool) error {
	tmp := &EmblemInfos{}
	fileName := filepath.Join(dir, "emblem_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*EmblemInfosExt, len(tmp.Datas))
	}
	m.Rare = make(map[uint32]*SameRareEmblemInfos)
	checkAscendIdPool := make(map[uint32]struct{})
	for _, data := range tmp.Datas {
		data.prepare()
		if data.AscendId > 0 {
			checkAscendIdPool[data.AscendId] = struct{}{}
		}
		maxLevel := m.xmlData.EmblemLevelInfoM.GetMaxLevel(data.LevelIndex)
		if maxLevel < data.LevelMax {
			panic(fmt.Sprintf("load config %s fail: no LevelIndex config, id:%d", fileName, data.Id))
		}

		_, exist := EmblemLegalPos[data.Pos]
		if !exist {
			panic(fmt.Sprintf("load config %s fail: id:%d error pos:%d", fileName, data.Id, data.Pos))
		}

		if data.StrengthCoe == 0 {
			panic(fmt.Sprintf("load config %s fail: id:%d error StrengthCoe must more than 0", fileName, data.Id))
		}

		if len(data.DecomposeClRes) == 0 {
			panic(fmt.Sprintf("load config %s fail: id:%d error decompose resource must more than 0", fileName, data.Id))
		}

		if data.IsHeroGroup != 1 && data.HeroGroup != 0 {
			panic(fmt.Sprintf("load config %s fail: id:%d  heroGroup error", fileName, data.Id))
		}

		info := &EmblemInfosExt{}
		info.Id = data.Id
		info.Rare = data.Rare
		info.Race = data.Race
		info.Type = data.Type
		info.Pos = data.Pos
		info.SuitSkill = data.SuitSkill
		info.IsHeroGroup = data.IsHeroGroup
		info.HeroGroup = data.HeroGroup
		info.IsMagicSkill = data.IsMagicSkill
		info.LevelMax = data.LevelMax
		info.LevelIndex = data.LevelIndex
		info.StrengthCoe = data.StrengthCoe
		info.DecomposeClRes = data.DecomposeClRes
		info.Attr = make(map[uint32]int64, EmblemAttrCount)
		if data.AttrType1 > 0 && data.AttrType1 < uint32(AttrMaxNum) && data.AttrValue1 > 0 {
			info.Attr[data.AttrType1] = int64(data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrType2 < uint32(AttrMaxNum) && data.AttrValue2 > 0 {
			info.Attr[data.AttrType2] = int64(data.AttrValue2)
		}
		info.AscendId = data.AscendId

		if _, exist = m.Rare[data.Rare]; !exist {
			m.Rare[data.Rare] = &SameRareEmblemInfos{
				SameRaceData: make(map[uint32][]*EmblemInfosExt),
			}
		}
		m.Rare[data.Rare].SameRaceData[data.Race] = append(m.Rare[data.Rare].SameRaceData[data.Race], info)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *info
		} else {
			m.Datas[data.Id] = info
		}
	}

	for id := range checkAscendIdPool {
		if data, exist := m.Datas[id]; exist {
			if data.Rare != EmblemRareRed {
				panic(fmt.Sprintf("load config %s fail: emblem rare is invalid. id:%d rare:%d", fileName, id, data.Rare))
			}
		} else {
			panic(fmt.Sprintf("load config %s fail: emblemID not exist. id:%d", fileName, id))
		}
	}

	return nil
}

func (m *EmblemInfoManager) GetRecordById(id uint32) *EmblemInfosExt {
	return m.Datas[id]
}

func (m *EmblemInfoManager) Index(key uint32) *EmblemInfosExt {
	return m.Datas[key]
}

func (m *EmblemInfoManager) GetRare(key uint32) uint32 {
	cfg := m.Index(key)
	if cfg == nil {
		return 0
	}
	return cfg.Rare
}

func (m *EmblemInfoManager) CalcHeroAndSkill(rd *rand.Rand, emblem *cl.EmblemInfo) {
	if emblem == nil {
		l4g.Errorf("EmblemInfoManager CalcHeroAndSkill emblem param is nil")
		return
	}
	emInfo := m.Datas[emblem.SysId]
	if emInfo == nil {
		l4g.Errorf("EmblemInfoManager CalcHeroAndSkill emblem sysid:%d not exist in xml datas", emblem.SysId)
		return
	}

	if emInfo.IsHeroGroup != 1 {
		l4g.Debug("EmblemInfoManager CalcHeroAndSkill emblem sysid:%d Info dont need rand add hero", emblem.SysId)
		return
	}

	// 随机符文的专属英雄
	groupInfo := m.xmlData.EmblemHeroGroupInfoM.RandEmblemHero(rd, emInfo.HeroGroup)
	if groupInfo == nil {
		l4g.Errorf("EmblemInfoManager CalcHeroAndSkill emblem sysid:%d random hero group:%d err not find groupInfo", emblem.SysId, emInfo.HeroGroup)
		return
	}
	emblem.AdditiveHero = groupInfo.HeroId

	if emInfo.IsMagicSkill != 1 {
		l4g.Debug("EmblemInfoManager CalcHeroAndSkill emblem sysid:%d Info dont need rand add skill", emblem.SysId)
		return
	}

	magicSkillId := m.xmlData.EmblemHeroGroupInfoM.RandomMagicSkill(rd, groupInfo.GroupId, groupInfo.HeroId)
	if magicSkillId == 0 {
		l4g.Errorf("EmblemInfoManager CalcHeroAndSkill emblem sysid:%d groupId:%d heroId:%d random Magic skill failed", emblem.SysId, groupInfo.GroupId, groupInfo.HeroId)
		return
	}

	skillId := m.xmlData.EmblemMagicSkillInfoM.RandSkill(rd, magicSkillId)
	if skillId == 0 {
		l4g.Errorf("EmblemInfoManager CalcHeroAndSkill rand magicSkillId:%d skill is zero", magicSkillId)
		return
	}
	emblem.SkillId = skillId
}

func (m *EmblemInfoManager) CalcSuitSkillId(userId uint64, TypeSuitSkills map[uint32]Uint32Slice, RaisePassiveSkill map[uint32]uint32) int {
	l4g.Debug("EmblemInfoManager CalcSuitSkillId start")
	emConfig := m.xmlData.EmblemConfigInfoM
	cond1 := emConfig.emblemSuitCondition1
	cond2 := emConfig.emblemSuitCondition2
	suitNum := 0
	for _, skills := range TypeSuitSkills {
		skills.Sort()
		if len(skills) < int(cond1) {
			continue
		}
		index1 := uint32(len(skills)) - cond1
		suitSkillInfo := m.xmlData.EmblemSuitInfoM.Index(skills[index1])
		if suitSkillInfo == nil {
			l4g.Errorf("user:%d EmblemInfoManager CalcSuitSkillId cond:%d get skillId:%d failed,", userId, cond1, skills[index1])
		} else if suitSkillInfo.Skill1 > 0 {
			l4g.Debug("EmblemInfoManager update skill1:%d into RaisePassiveSkill", suitSkillInfo.Skill1)
			suitNum = EmblemTwoSuit
			RaisePassiveSkill[suitSkillInfo.Skill1]++
		}
		if len(skills) < int(cond2) {
			continue
		}
		index2 := uint32(len(skills)) - cond2
		suitSkillInfo = m.xmlData.EmblemSuitInfoM.Index(skills[index2])
		if suitSkillInfo == nil {
			l4g.Errorf("user:%d EmblemInfoManager CalcSuitSkillId cond:%d get skillId:%d failed,", userId, cond1, skills[index2])
			continue
		} else if suitSkillInfo.Skill2 > 0 {
			l4g.Debug("EmblemInfoManager update skill2:%d into RaisePassiveSkill", suitSkillInfo.Skill2)
			suitNum = EmblemFourSuit
			RaisePassiveSkill[suitSkillInfo.Skill2]++
		}
	}
	l4g.Debug("EmblemInfoManager CalcSuitSkillId end")
	return suitNum
}

func (m *EmblemInfoManager) CalcSkill(rd *rand.Rand, emblem *cl.EmblemInfo) {
	if emblem == nil {
		l4g.Errorf("EmblemInfoManager CalcSkill emblem param is nil")
		return
	}

	emInfo := m.Datas[emblem.SysId]
	if emInfo == nil {
		l4g.Errorf("EmblemInfoManager CalcSkill emblem sysid:%d not exist in xml datas", emblem.SysId)
		return
	}

	if emInfo.IsHeroGroup != 1 {
		return
	}

	groupInfo := m.xmlData.EmblemHeroGroupInfoM.InGroup(emInfo.HeroGroup, emblem.AdditiveHero)
	if groupInfo == nil {
		l4g.Errorf("EmblemInfoManager CalcSkill emblem sysid:%d HeroGroup:%d not find hero:%d", emInfo.Id, emInfo.HeroGroup, emblem.AdditiveHero)
		return
	}

	if emInfo.IsMagicSkill != 1 {
		return
	}

	magicSkillId := m.xmlData.EmblemHeroGroupInfoM.RandomMagicSkill(rd, groupInfo.GroupId, groupInfo.HeroId)
	if magicSkillId == 0 {
		l4g.Errorf("EmblemInfoManager CalcSkill emblem sysid:%d groupId:%d heroId:%d random Magic skill failed", emblem.SysId, groupInfo.GroupId, groupInfo.HeroId)
		return
	}

	skillId := m.xmlData.EmblemMagicSkillInfoM.RandSkill(rd, magicSkillId)
	if skillId == 0 {
		l4g.Errorf("EmblemInfoManager CalcSkill rand magicSkillId:%d skill is zero", magicSkillId)
		return
	}
	emblem.SkillId = skillId
}

func (m *EmblemInfoManager) GetEmblemInfosByRareAndRace(rare, race uint32) []*EmblemInfosExt {
	sameRareEmblemData := m.Rare[rare]
	if sameRareEmblemData == nil {
		return nil
	}
	return sameRareEmblemData.SameRaceData[race]
}

// GetEmblemAffixType
// @Description: 获取符文的词缀类型
// @receiver m
// @return uint32
func (e *EmblemInfosExt) GetEmblemAffixType(affixId uint32) uint32 {
	if e == nil {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
	}

	if affixId == 0 {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
	}

	suitInfo := GetData().EmblemSuitInfoM.Index(e.SuitSkill)
	if suitInfo == nil {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
	}

	if affixId == suitInfo.Affix2 {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_ARCHAIC)
	}

	if affixId == suitInfo.Affix1 {
		return uint32(common.EMBLEM_AFFIX_TYPE_EAT_ANCIENT)
	}
	return uint32(common.EMBLEM_AFFIX_TYPE_EAT_NONE)
}

// 根据品质种族类型位置，获取符文配置
// @param rare uint32 品质
// @param race uint32 种族
// @param typ uint32 类型
// @param pos uint32 位置
// @return *EmblemInfosExt
func (m *EmblemInfoManager) GetRareLimitEmblemInfo(rare, race, typ, pos uint32) *EmblemInfosExt {
	sameRareEmblemData := m.Rare[rare]
	if sameRareEmblemData == nil {
		return nil
	}
	for _, val := range sameRareEmblemData.SameRaceData[race] {
		if val.Type == typ && val.Pos == pos {
			return val
		}
	}
	return nil
}
