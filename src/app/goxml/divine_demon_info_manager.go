package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type DivineDemonInfoExt struct {
	Id          uint32
	Type        uint32
	SingleGroup uint32
	CostType    uint32         //int:消耗资源类型1
	CostValue   uint32         //int:消耗资源值1
	CostCount   uint32         //int:消耗数量1
	SingleCosts []*cl.Resource // 单抽消耗
	PluralCosts []*cl.Resource // 连抽消耗
	ShopId      uint32         // 付费商城id
	StarShopId  uint32         // 升星礼包商城id
	HeroIds     map[uint32]struct{}
}

type DivineDemonInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*DivineDemonInfoExt
}

func newDivineDemonInfoManager(xmlData *XmlData) *DivineDemonInfoManager {
	m := &DivineDemonInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DivineDemonInfoManager) name() string {
	return "DivineDemonInfo"
}

func (m *DivineDemonInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DivineDemonInfoManager) checkData() error {
	return nil
}

func (m *DivineDemonInfoManager) load(dir string, isShow bool) error {
	tmp := &DivineDemonInfos{}
	fileName := filepath.Join(dir, "divine_demon_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*DivineDemonInfoExt, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Type != DivineDemonTypeDebut && data.Type != DivineDemonTypeNoDebut {
			panic(fmt.Sprintf("load config %s fail: activity type is invalid. id: %d, type: %d", fileName, data.Id, data.Type))
		}

		if data.ShopId == data.StarShopId {
			panic(fmt.Sprintf("load config %s fail: shop id is same. id: %d, shopID: %d", fileName, data.Id, data.ShopId))
		}

		dataExt := &DivineDemonInfoExt{
			Id:          data.Id,
			Type:        data.Type,
			SingleGroup: data.SingleGroup,
			CostType:    data.CostType,
			CostValue:   data.CostValue,
			CostCount:   data.CostCount,
			SingleCosts: data.CostClRes,
			ShopId:      data.ShopId,
			StarShopId:  data.StarShopId,
			HeroIds:     make(map[uint32]struct{}, 6),
		}
		if data.HeroId1 > 0 {
			dataExt.HeroIds[data.HeroId1] = struct{}{}
		}
		if data.HeroId2 > 0 {
			dataExt.HeroIds[data.HeroId2] = struct{}{}
		}
		if data.HeroId3 > 0 {
			dataExt.HeroIds[data.HeroId3] = struct{}{}
		}
		if data.HeroId4 > 0 {
			dataExt.HeroIds[data.HeroId4] = struct{}{}
		}
		if data.HeroId5 > 0 {
			dataExt.HeroIds[data.HeroId5] = struct{}{}
		}
		if data.HeroId6 > 0 {
			dataExt.HeroIds[data.HeroId6] = struct{}{}
		}

		for hero := range dataExt.HeroIds {
			heroInfo := m.xmlData.HeroInfoM.Index(hero)
			if heroInfo == nil {
				panic(fmt.Sprintf("load config %s data:%d fail: heroInfo is nil", fileName, data.Id))
			}
		}

		newCount := m.xmlData.DivineDemonConfigInfoM.SummonPlural * data.CostCount
		if newCount == 0 {
			panic(fmt.Sprintf("load config %s fail: gen plural summon costNum is zero. id: %d", fileName, data.Id))
		}
		dataExt.PluralCosts = append(dataExt.PluralCosts, GenSimpleResource(data.CostType, data.CostValue, newCount))

		if len(dataExt.SingleCosts) == 0 {
			panic(fmt.Sprintf("load config %s fail: single summon  cost is nil. id: %d", fileName, data.Id))
		}
		if len(dataExt.PluralCosts) == 0 {
			panic(fmt.Sprintf("load config %s fail: plural summon  cost is nil. id: %d", fileName, data.Id))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
	}
	return nil
}

func (m *DivineDemonInfoManager) GetRecordById(id uint32) *DivineDemonInfoExt {
	return m.Datas[id]
}

func (m *DivineDemonInfoManager) Index(key uint32) *DivineDemonInfoExt {
	return m.Datas[key]
}

func (m *DivineDemonInfoManager) IsDebut(sysID uint32) bool {
	if data, exist := m.Datas[sysID]; exist {
		if data.Type == DivineDemonTypeDebut {
			return true
		} else {
			return false
		}
	}

	return false
}

func (m *DivineDemonInfoManager) IsExistShopID(activityID, shopID uint32) bool {
	data := m.Datas[activityID]
	if data == nil {
		return false
	}

	if data.ShopId != shopID && data.StarShopId != shopID {
		return false
	}

	return true
}
