package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type FormationGroupInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*FormationGroupInfoExt
}

type FormationGroupInfoExt struct {
	FormationIds []uint32 //支持的阵容id
}

func newFormationGroupInfoManager(xmlData *XmlData) *FormationGroupInfoManager {
	m := &FormationGroupInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *FormationGroupInfoManager) name() string {
	return "FormationGroupInfo"
}

func (m *FormationGroupInfoManager) ignoreForCheck() bool {
	return false
}

func (m *FormationGroupInfoManager) checkData() error {
	return nil
}

func (m *FormationGroupInfoManager) load(dir string, isShow bool) error {
	tmp := &FormationGroupInfos{}
	fileName := filepath.Join(dir, "formation_group_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*FormationGroupInfoExt, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		extData := &FormationGroupInfoExt{}
		if data.FormationId1 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId1)
		}
		if data.FormationId2 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId2)
		}
		if data.FormationId3 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId3)
		}
		if data.FormationId4 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId4)
		}
		if data.FormationId5 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId5)
		}
		if data.FormationId6 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId6)
		}
		if data.FormationId7 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId7)
		}
		if data.FormationId8 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId8)
		}
		if data.FormationId9 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId9)
		}
		if data.FormationId10 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId10)
		}
		if data.FormationId11 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId11)
		}
		if data.FormationId12 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId12)
		}
		if data.FormationId13 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId13)
		}
		if data.FormationId14 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId14)
		}
		if data.FormationId15 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId15)
		}
		if data.FormationId16 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId16)
		}
		if data.FormationId17 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId17)
		}
		if data.FormationId18 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId18)
		}
		if data.FormationId19 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId19)
		}
		if data.FormationId20 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId20)
		}
		if data.FormationId21 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId21)
		}
		if data.FormationId22 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId22)
		}
		if data.FormationId23 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId23)
		}
		if data.FormationId24 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId24)
		}
		if data.FormationId25 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId25)
		}
		if data.FormationId26 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId26)
		}
		if data.FormationId27 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId27)
		}
		if data.FormationId28 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId28)
		}
		if data.FormationId29 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId29)
		}
		if data.FormationId30 != 0 {
			extData.FormationIds = append(extData.FormationIds, data.FormationId30)
		}
		m.Datas[data.Id] = extData
	}
	return nil
}

func (m *FormationGroupInfoManager) GetRecordById(id uint32) *FormationGroupInfoExt {
	return m.Datas[id]
}

func (m *FormationGroupInfoManager) Index(key uint32) *FormationGroupInfoExt {
	return m.Datas[key]
}
