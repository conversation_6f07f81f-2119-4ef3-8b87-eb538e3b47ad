package goxml

import (
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type GuildSandTableHeroWorkInfoManager struct {
	xmlData        *XmlData
	Datas          map[uint32]*GuildSandTableHeroWorkInfo
	BuildInfo      map[uint32]map[uint32]*GuildSandTableHeroWorkInfo
	buildBlessHero map[uint32][]*GuildSandTableHeroWorkInfo
}

func newGuildSandTableHeroWorkInfoManager(xmlData *XmlData) *GuildSandTableHeroWorkInfoManager {
	m := &GuildSandTableHeroWorkInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableHeroWorkInfoManager) name() string {
	return "guild_sand_table_hero_work_info.xml"
}

func (m *GuildSandTableHeroWorkInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableHeroWorkInfoManager) checkData() error {
	return nil
}

func (m *GuildSandTableHeroWorkInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableHeroWorkInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_hero_work_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*GuildSandTableHeroWorkInfo, len(tmp.Datas))
	m.BuildInfo = make(map[uint32]map[uint32]*GuildSandTableHeroWorkInfo)
	m.buildBlessHero = make(map[uint32][]*GuildSandTableHeroWorkInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		_, exist := m.BuildInfo[data.BuildType]
		if !exist {
			m.BuildInfo[data.BuildType] = make(map[uint32]*GuildSandTableHeroWorkInfo)
		}
		m.buildBlessHero[data.BuildType] = append(m.buildBlessHero[data.BuildType], data)
		_, exist = m.BuildInfo[data.BuildType][data.HeroWorkNum]
		if exist {
			panic(fmt.Sprintf("config %s fail id:%d buildType:%d hero work num:%d is repated", fileName, data.Id, data.BuildType, data.HeroWorkNum))
		}

		m.BuildInfo[data.BuildType][data.HeroWorkNum] = data
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildSandTableHeroWorkInfoManager) Index(key uint32) *GuildSandTableHeroWorkInfo {
	return m.Datas[key]
}

func (m *GuildSandTableHeroWorkInfoManager) DispatchHeroNumCheck(buildType, heroNum, maxBlessedCount uint32) bool {
	info, exist := m.BuildInfo[buildType][heroNum]
	if !exist {
		return false
	}
	return maxBlessedCount >= info.CrystalSlotLimit
}

func (m *GuildSandTableHeroWorkInfoManager) DispatchHeroCount(buildType, maxBlessedCount uint32) uint32 {
	infos, exist := m.buildBlessHero[buildType]
	if !exist {
		return 0
	}
	var ret uint32
	for _, info := range infos {
		if maxBlessedCount >= info.CrystalSlotLimit {
			ret = info.HeroWorkNum
		}
	}
	return ret
}
