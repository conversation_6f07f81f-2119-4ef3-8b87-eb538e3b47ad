package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityFeedingGiftsInfoManager struct {
	xmlData  *XmlData
	ActDatas map[uint32]map[uint32]*ActivityFeedingGiftsInfo
}

func newActivityFeedingGiftsInfoManager(xmlData *XmlData) *ActivityFeedingGiftsInfoManager {
	m := &ActivityFeedingGiftsInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityFeedingGiftsInfoManager) name() string {
	return "activity_feeding_gifts_info.xml"
}

func (m *ActivityFeedingGiftsInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityFeedingGiftsInfoManager) checkData() error {
	return nil
}

func (m *ActivityFeedingGiftsInfoManager) load(dir string, show bool) error {
	tmp := &ActivityFeedingGiftsInfos{}
	fileName := filepath.Join(dir, "activity_feeding_gifts_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.ActDatas = make(map[uint32]map[uint32]*ActivityFeedingGiftsInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		actInfo := m.xmlData.ActivityTurntableInfoM.Index(data.Id)
		if actInfo == nil {
			panic(fmt.Sprintf("load config %s fail: %d id in actInfo is nil", fileName, data.Id))
		}
		_, exist := m.ActDatas[data.Id]
		if !exist {
			m.ActDatas[data.Id] = make(map[uint32]*ActivityFeedingGiftsInfo)
		}
		_, exist = m.ActDatas[data.Id][data.Gift]
		if exist {
			panic(fmt.Sprintf("load config %s fail: %d id:%d gift:%d in actInfo is exist", fileName, data.Id, data.Gift))
		}
		m.ActDatas[data.Id][data.Gift] = data
	}
	return nil
}

func (m *ActivityFeedingGiftsInfoManager) Index(actID, giftID uint32) *ActivityFeedingGiftsInfo {
	return m.ActDatas[actID][giftID]
}

func (m *ActivityFeedingGiftsInfo) GetMakeCost(count uint32) []*cl.Resource {
	ret := make([]*cl.Resource, 0, len(m.ClRes))
	for _, v := range m.ClRes {
		if v == nil {
			continue
		}
		clone := v.Clone()
		clone.Count *= count
		ret = append(ret, clone)
	}
	return ret
}
