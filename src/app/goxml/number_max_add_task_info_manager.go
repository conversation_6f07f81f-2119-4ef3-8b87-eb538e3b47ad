package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type NumberMaxAddTaskInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]*NumberMaxAddTaskInfo
	allEvent    map[uint32]struct{}
	types       map[uint32][]*NumberMaxAddTaskInfo // key: task_type_info中的type
	numberTypes map[uint32][]*NumberMaxAddTaskInfo // key: numberType
}

func newNumberMaxAddTaskInfoManager(xmlData *XmlData) *NumberMaxAddTaskInfoManager {
	m := &NumberMaxAddTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *NumberMaxAddTaskInfoManager) name() string {
	return "NumberMaxAddTaskInfo"
}

func (m *NumberMaxAddTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *NumberMaxAddTaskInfoManager) checkData() error {
	return nil
}

func (m *NumberMaxAddTaskInfoManager) load(dir string, isShow bool) error {
	tmp := &NumberMaxAddTaskInfos{}
	fileName := filepath.Join(dir, "number_max_add_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*NumberMaxAddTaskInfo, len(tmp.Datas))
	}

	m.allEvent = make(map[uint32]struct{})
	m.types = make(map[uint32][]*NumberMaxAddTaskInfo)
	m.numberTypes = make(map[uint32][]*NumberMaxAddTaskInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Value == 0 {
			panic(fmt.Sprintf("check data error %s id: %d task value is zero", fileName, data.Id))
		}

		if m.xmlData.NumberTypeInfoM.Index(data.Type) == nil {
			panic(fmt.Sprintf("load config %s fail: number type not exist. type: %d", fileName, data.Type))
		}

		taskType := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskType == nil {
			panic(fmt.Sprintf("check data error %s %d. taskType not exist", fileName, data.Id))
		}
		m.allEvent[taskType.Type] = struct{}{}
		m.types[taskType.Type] = append(m.types[taskType.Type], data)
		m.numberTypes[data.Type] = append(m.numberTypes[data.Type], data)
		if ptr, exist := m.Datas[data.Type]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Type] = data
		}
	}
	return nil
}

func (m *NumberMaxAddTaskInfoManager) Index(numberType uint32) *NumberMaxAddTaskInfo {
	return m.Datas[numberType]
}

func (m *NumberMaxAddTaskInfoManager) Events() map[uint32]struct{} {
	return m.allEvent
}

func (m *NumberMaxAddTaskInfoManager) GetTaskInfos(event uint32) []*NumberMaxAddTaskInfo {
	return m.types[event]
}

func (m *NumberMaxAddTaskInfoManager) GetMaxNumInfos(numberType uint32) []*NumberMaxAddTaskInfo {
	return m.numberTypes[numberType]
}
