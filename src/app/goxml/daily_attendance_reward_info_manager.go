package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type DailyAttendanceRewardInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]map[uint32]*DailyAttendanceRewardInfo // key: group -> key: day
}

func newDailyAttendanceRewardInfoManager(xmlData *XmlData) *DailyAttendanceRewardInfoManager {
	m := &DailyAttendanceRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DailyAttendanceRewardInfoManager) name() string {
	return "DailyAttendanceRewardInfo"
}

func (m *DailyAttendanceRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DailyAttendanceRewardInfoManager) checkData() error {
	return nil
}

func (m *DailyAttendanceRewardInfoManager) load(dir string, isShow bool) error {
	tmp := &DailyAttendanceRewardInfos{}
	fileName := filepath.Join(dir, "daily_attendance_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	check := make(map[uint32][]uint32)
	m.Datas = make(map[uint32]map[uint32]*DailyAttendanceRewardInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()

		if len(data.RewardClRes) == 0 {
			panic(fmt.Sprintf("load config %s fail: award is nil. group: %d day: %d", fileName, data.Group, data.Day))
		}

		if _, exist := m.Datas[data.Group]; !exist {
			m.Datas[data.Group] = make(map[uint32]*DailyAttendanceRewardInfo)
		}

		m.Datas[data.Group][data.Day] = data

		check[data.Group] = append(check[data.Group], data.Day)
	}

	for group, v := range check {
		if uint32(len(v)) != DailyAttendanceEachGroupMaxDay {
			panic(fmt.Sprintf("load config %s fail: each group max num is invalid. group: %d", fileName, group))
		}

		preDay := uint32(0)
		for _, day := range v {
			if day-preDay != 1 {
				panic(fmt.Sprintf("load config %s fail: each group day is discrete. group: %d day: %d", fileName, group, day))
			}
			preDay = day
		}
	}
	return nil
}

func (m *DailyAttendanceRewardInfoManager) GetValidGroup(group uint32) uint32 {
	maxGroup := m.xmlData.DailyAttendanceInfoM.GetMaxGroup()
	if group < maxGroup {
		return group
	}
	return maxGroup
}

func (m *DailyAttendanceRewardInfoManager) Index(round, day uint32) *DailyAttendanceRewardInfo {
	return m.Datas[round][day]
}
