package goxml

import (
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/time"
)

type EmblemHeroGroupInfoMEx struct {
	*EmblemHeroGroupInfoM
	groupHero      map[uint32][]*EmblemHeroGroupInfo
	heroSkillGroup map[uint32][]uint32
}

func newEmblemHeroGroupInfoMEx(xmlData *XmlData) *EmblemHeroGroupInfoMEx {
	m := &EmblemHeroGroupInfoMEx{
		EmblemHeroGroupInfoM: &EmblemHeroGroupInfoM{
			xmlData: xmlData,
		},
		groupHero:      make(map[uint32][]*EmblemHeroGroupInfo),
		heroSkillGroup: make(map[uint32][]uint32),
	}
	xmlData.addManager(m)
	return m
}

func (m *EmblemHeroGroupInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *EmblemHeroGroupInfoMEx) load(dir string, isShow bool) error {
	err := m.EmblemHeroGroupInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	return nil
}

func (m *EmblemHeroGroupInfoMEx) checkData() error {
	err := m.EmblemHeroGroupInfoM.checkData()
	if err != nil {
		return err
	}
	for _, i := range m.groupIdIndexMap {
		for _, j := range i.heroIdRecordMap {
			shieldInfo := m.xmlData.ShieldInfoM.GetRecordByShieldId(j.HeroId)
			if shieldInfo != nil {
				j.ShieldEndTime = shieldInfo.EndDay.Unix()
			}
			m.groupHero[j.GroupId] = append(m.groupHero[j.GroupId], j)

			_, exist := m.heroSkillGroup[j.HeroId]
			if !exist {
				m.heroSkillGroup[j.HeroId] = make([]uint32, 0, 10)
			}
			m.heroSkillGroup[j.HeroId] = append(m.heroSkillGroup[j.HeroId], j.MagicSkill1, j.MagicSkill2)
		}
	}
	return nil
}

func (m *EmblemHeroGroupInfoMEx) Group(groupId uint32) []*EmblemHeroGroupInfo {
	var result []*EmblemHeroGroupInfo
	now := time.Now().Unix()
	for _, v := range m.groupHero[groupId] {
		if v.ShieldEndTime > now {
			continue
		}
		result = append(result, v)
	}
	return result
}

func (m *EmblemHeroGroupInfoMEx) InGroup(groupId, hero uint32) *EmblemHeroGroupInfo {
	if len(m.groupHero[groupId]) == 0 {
		return nil
	}

	for _, info := range m.groupHero[groupId] {
		if info.HeroId == hero {
			return info
		}
	}

	return nil
}

func (m *EmblemHeroGroupInfoMEx) RandEmblemHero(rd *rand.Rand, group uint32) *EmblemHeroGroupInfo {
	heroList := m.Group(group)
	if len(heroList) == 0 {
		return nil
	}

	var groupHeroWeight uint32
	for _, v := range heroList {
		groupHeroWeight += v.Chance
	}

	number := uint32(rd.RandBetween(1, int(groupHeroWeight)))

	for _, data := range heroList {
		if data.Chance < number {
			number -= data.Chance
			continue
		}
		return data
	}

	return nil
}

// 英雄已屏蔽则不需要屏蔽技能
func (m *EmblemHeroGroupInfoMEx) RandomMagicSkill(rd *rand.Rand, groupId, heroId uint32) uint32 {
	groupInfo := m.GetRecordByGroupIdHeroId(groupId, heroId)
	if groupInfo == nil {
		return 0
	}

	MaxWeight := groupInfo.MagicSkillChance1 + groupInfo.MagicSkillChance2
	number := rd.RandBetween(1, int(MaxWeight))

	if uint32(number) <= groupInfo.MagicSkillChance1 {
		return groupInfo.MagicSkill1
	}
	return groupInfo.MagicSkill2
}

// 拥有英雄才会进行判断，故不用考虑英雄屏蔽
func (m *EmblemHeroGroupInfoMEx) IsHeroAdditiveSkill(heroSysID uint32, skillId uint32) bool {
	_, exist := m.heroSkillGroup[heroSysID]
	if !exist {
		return false
	}
	for _, skill := range m.heroSkillGroup[heroSysID] {
		if skill == skillId {
			return true
		}
	}
	return false
}
