package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type WorldbossTaskInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]*WorldbossTaskInfo
	TypeData map[uint32]map[uint32]*WorldbossTaskInfo
	allEvent map[uint32]struct{}
}

func newWorldbossTaskInfoManager(xmlData *XmlData) *WorldbossTaskInfoManager {
	m := &WorldbossTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *WorldbossTaskInfoManager) name() string {
	return "WorldbossTaskInfo"
}

func (m *WorldbossTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *WorldbossTaskInfoManager) checkData() error {
	return nil
}

func (m *WorldbossTaskInfoManager) load(dir string, isShow bool) error {
	tmp := &WorldbossTaskInfos{}
	fileName := filepath.Join(dir, "worldboss_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*WorldbossTaskInfo, len(tmp.Datas))
	m.TypeData = make(map[uint32]map[uint32]*WorldbossTaskInfo, 0)
	m.allEvent = make(map[uint32]struct{})
	for _, data := range tmp.Datas {
		data.prepare()

		if len(data.ClRes) == 0 {
			panic(fmt.Sprintf("check data error %s id: %d must have award", fileName, data.Id))
		}
		if data.Value == 0 {
			panic(fmt.Sprintf("check data error %s id: %d task value is zero", fileName, data.Id))
		}

		taskType := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskType == nil {
			panic(fmt.Sprintf("check data error %s %d. taskType not exist", fileName, data.Id))
		}
		m.allEvent[taskType.Type] = struct{}{}

		_, exist := m.TypeData[data.BossType]
		if !exist {
			m.TypeData[data.BossType] = make(map[uint32]*WorldbossTaskInfo)
		}

		_, exist = m.TypeData[data.BossType][data.Id]
		if exist {
			panic(fmt.Sprintf("check data error: %s id:%d bossType:%d is exist", fileName, data.Id, data.BossType))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *WorldbossTaskInfoManager) GetRecordById(id uint32) *WorldbossTaskInfo {
	return m.Datas[id]
}

func (m *WorldbossTaskInfoManager) Index(key uint32) *WorldbossTaskInfo {
	return m.Datas[key]
}

func (m *WorldbossTaskInfoManager) Events() map[uint32]struct{} {
	return m.allEvent
}

func (m *WorldbossTaskInfoManager) GetTaskNum() int {
	return len(m.Datas)
}
