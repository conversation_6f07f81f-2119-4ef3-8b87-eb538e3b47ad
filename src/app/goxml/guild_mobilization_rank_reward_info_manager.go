package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildMobilizationRankRewardInfoManager struct {
	xmlData    *XmlData
	Datas      map[uint32]*GuildMobilizationRankRewardInfo
	RoundDatas map[uint32][]*GuildMobilizationRankRewardInfo
}

func newGuildMobilizationRankRewardInfoManager(xmlData *XmlData) *GuildMobilizationRankRewardInfoManager {
	m := &GuildMobilizationRankRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildMobilizationRankRewardInfoManager) name() string {
	return "guild_mobilization_rank_reward_info.xml"
}

func (m *GuildMobilizationRankRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildMobilizationRankRewardInfoManager) checkData() error {
	return nil
}

func (m *GuildMobilizationRankRewardInfoManager) load(dir string, show bool) error {
	tmp := &GuildMobilizationRankRewardInfos{}
	fileName := filepath.Join(dir, "guild_mobilization_rank_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*GuildMobilizationRankRewardInfo)
	m.RoundDatas = make(map[uint32][]*GuildMobilizationRankRewardInfo, 0)

	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
		m.RoundDatas[data.Round] = append(m.RoundDatas[data.Round], data)
	}
	return nil
}

func (m *GuildMobilizationRankRewardInfoManager) Index(id uint32) *GuildMobilizationRankRewardInfo {
	return m.Datas[id]
}

func (m *GuildMobilizationRankRewardInfoManager) GetRoundRewards(round uint32) []*GuildMobilizationRankRewardInfo {
	return m.RoundDatas[round]
}
