package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

type FlowerPlantGoblinInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]map[uint32]*FlowerPlantGoblinInfoExt
	TotalWeight map[uint32]int
	lastMax     map[uint32]uint32
}

type FlowerPlantGoblinInfoExt struct {
	Id     uint32
	Type   uint32
	Weight int
	Min    int
	Max    int
}

func newFlowerPlantGoblinInfoManager(xmlData *XmlData) *FlowerPlantGoblinInfoManager {
	m := &FlowerPlantGoblinInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *FlowerPlantGoblinInfoManager) name() string {
	return "FlowerPlantGoblinInfo"
}

func (m *FlowerPlantGoblinInfoManager) ignoreForCheck() bool {
	return false
}

func (m *FlowerPlantGoblinInfoManager) checkData() error {
	return nil
}

func (m *FlowerPlantGoblinInfoManager) load(dir string, isShow bool) error {
	tmp := &FlowerPlantGoblinInfos{}
	fileName := filepath.Join(dir, "flower_plant_goblin_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	typeCount := m.xmlData.FlowerLvInfoM.LegalTreeCount()
	if m.Datas == nil {
		m.Datas = make(map[uint32]map[uint32]*FlowerPlantGoblinInfoExt, typeCount)
	}
	m.TotalWeight = make(map[uint32]int, typeCount)
	m.lastMax = make(map[uint32]uint32, typeCount)
	for _, data := range tmp.Datas {
		data.prepare()
		//验证类型的合法性
		if !m.xmlData.FlowerLvInfoM.LegalFlowerPlantTreeType(data.Type) {
			panic(fmt.Sprintf("load config %s fail: type illegal. type:%d", fileName, data.Type))
		}

		if data.Weight == 0 {
			panic(fmt.Sprintf("load config %s fail: weight=0. id:%d", fileName, data.Id))
		}
		//验证分数区间合理性&连续性
		if data.Min != m.lastMax[data.Type]+1 {
			panic(fmt.Sprintf("load config %s fail: min max config err. id:%d type:%d",
				fileName, data.Id, data.Type))
		}
		m.lastMax[data.Type] = data.Max

		dataExt := &FlowerPlantGoblinInfoExt{}
		dataExt.Type = data.Type
		dataExt.Id = data.Id
		dataExt.Weight = int(data.Weight)
		dataExt.Min = int(data.Min)
		dataExt.Max = int(data.Max)

		if _, exist := m.Datas[data.Type]; !exist {
			m.Datas[data.Type] = make(map[uint32]*FlowerPlantGoblinInfoExt, len(tmp.Datas)/typeCount)
		}

		if ptr, exist := m.Datas[data.Type][data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Type][data.Id] = dataExt
		}
		m.TotalWeight[data.Type] += dataExt.Weight
	}
	return nil
}

func (m *FlowerPlantGoblinInfoManager) Index(treeType, id uint32) *FlowerPlantGoblinInfoExt {
	return m.Datas[treeType][id]
}

func (m *FlowerPlantGoblinInfoManager) GenerateGoblin(rd *rand.Rand, treeType, guidanceStep, lastGoblinID uint32) uint32 {
	//新手引导的特殊逻辑
	if m.xmlData.FlowerConfigInfoM.IsInGuidance(guidanceStep) {
		guidanceInfo := m.xmlData.FlowerConfigInfoM.GetGuidanceInfo(guidanceStep)
		if guidanceInfo != nil {
			return guidanceInfo.ID
		}
		l4g.Errorf("GenerateGoblin. guidanceInfo is nil. guidanceStep:%d", guidanceStep)
		return 0
	}

	//玩法常规逻辑
	sourceDatas := m.Datas[treeType]
	var totalWeight int
	datas := make(map[uint32]*FlowerPlantGoblinInfoExt, len(sourceDatas))
	if lastGoblinID > 0 {
		for _, v := range sourceDatas {
			if v.Id == lastGoblinID {
				continue
			}
			datas[v.Id] = v
			totalWeight += v.Weight
		}
	} else {
		datas = sourceDatas
		totalWeight = m.TotalWeight[treeType]
	}

	rNum := rd.RandBetween(1, totalWeight)
	for _, v := range datas {
		if rNum <= v.Weight {
			return v.Id
		}
		rNum -= v.Weight
	}

	l4g.Errorf("goblin generate failed. treeType:%d, rNum:%d, totalWeight:%d, lastGoblinID:%d",
		treeType, rNum, totalWeight, lastGoblinID)
	return 0
}

func (m *FlowerPlantGoblinInfoManager) RandomScore(rd *rand.Rand, treeType, id, guidanceStep uint32) uint32 {
	//新手引导的特殊逻辑
	if m.xmlData.FlowerConfigInfoM.IsInGuidance(guidanceStep) {
		guidanceInfo := m.xmlData.FlowerConfigInfoM.GetGuidanceInfo(guidanceStep)
		if guidanceInfo != nil {
			return guidanceInfo.Score
		}
		l4g.Errorf("RandomScore. guidanceInfo is nil. guidanceStep:%d", guidanceStep)
		return 0
	}

	//玩法常规逻辑
	info := m.Index(treeType, id)
	if info != nil {
		return uint32(rd.RandBetween(info.Min, info.Max))
	}

	l4g.Errorf("FlowerPlantGoblinInfoM. random score failed. id:%d", id)
	return 0
}

func (m *FlowerPlantGoblinInfoManager) MaxScore(treeType uint32) uint32 {
	return m.lastMax[treeType]
}

func (m *FlowerPlantGoblinInfoManager) CheckGuidanceGoblinScore(id, score uint32) bool {
	info := m.Index(m.xmlData.FlowerLvInfoM.MinLVPlantType(), id)
	if info == nil {
		return false
	}

	if int(score) >= info.Min && int(score) <= info.Max {
		return true
	}
	return false
}
