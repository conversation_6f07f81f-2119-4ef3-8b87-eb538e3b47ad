package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildMobilizationScoreRewardInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildMobilizationScoreRewardInfo
}

func newGuildMobilizationScoreRewardInfoManager(xmlData *XmlData) *GuildMobilizationScoreRewardInfoManager {
	m := &GuildMobilizationScoreRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildMobilizationScoreRewardInfoManager) name() string {
	return "guild_mobilization_Score_reward_info.xml"
}

func (m *GuildMobilizationScoreRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildMobilizationScoreRewardInfoManager) checkData() error {
	return nil
}

func (m *GuildMobilizationScoreRewardInfoManager) load(dir string, show bool) error {
	tmp := &GuildMobilizationScoreRewardInfos{}
	fileName := filepath.Join(dir, "guild_mobilization_score_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*GuildMobilizationScoreRewardInfo)

	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildMobilizationScoreRewardInfoManager) Index(id uint32) *GuildMobilizationScoreRewardInfo {
	return m.Datas[id]
}
