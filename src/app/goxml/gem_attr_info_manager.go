package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

type GemAttrInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GemAttrInfo

	// advance attr
	totalChanceMap map[uint32]map[uint32]uint32         // 高级属性组; group -> attrType -> total chance
	attrTypeMap    map[uint32][]*GemAttrType            // group -> GemAttrType
	chanceMap      map[uint32]uint32                    // group -> chance
	attrInfoMap    map[uint32]map[uint32][]*GemAttrInfo // group -> attrType -> *GemAttrInfo

	// basic attr
	BTotalChance map[uint32]uint32         // key: group  value: total chance
	BAttrPool    map[uint32][]*GemAttrInfo // key: group   value: basic attr pool
}

type GemAttrType struct {
	AttrType1 uint32
	Chance    uint32
}

func newGemAttrInfoManager(xmlData *XmlData) *GemAttrInfoManager {
	m := &GemAttrInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GemAttrInfoManager) name() string {
	return "GemAttrInfo"
}

func (m *GemAttrInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GemAttrInfoManager) checkData() error {
	return nil
}

func (m *GemAttrInfoManager) load(dir string, isShow bool) error {
	tmp := &GemAttrInfos{}
	fileName := filepath.Join(dir, "gem_attr_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GemAttrInfo, len(tmp.Datas))
	}

	m.totalChanceMap = make(map[uint32]map[uint32]uint32)
	m.attrTypeMap = make(map[uint32][]*GemAttrType)
	m.chanceMap = make(map[uint32]uint32)
	m.attrInfoMap = make(map[uint32]map[uint32][]*GemAttrInfo)
	m.BTotalChance = make(map[uint32]uint32)
	m.BAttrPool = make(map[uint32][]*GemAttrInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		if data.AttrType1 >= uint32(AttrMaxNum) {
			panic(fmt.Sprintf("gem attr info attrType config error, over max attrType. ID: %d, attrType1: %d", data.Id, data.AttrType1))
		}
		if data.AttrType2 >= uint32(AttrMaxNum) {
			panic(fmt.Sprintf("gem attr info attrType config error, over max attrType. ID: %d, attrType2: %d", data.Id, data.AttrType2))
		}
		if data.Chance == 0 {
			panic(fmt.Sprintf("gem attr info Chance config error, chance is zero. ID: %d", data.Id))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}

		if data.AttrClass == GemAttrTypeAdvanced {
			if data.AttrType2 > 0 {
				panic(fmt.Sprintf("gem attr info attrType2 config error, attrType2 can't over zero. ID: %d", data.Id))
			}
			m.chanceMap[data.Group] += data.Chance

			if _, exist := m.totalChanceMap[data.Group]; !exist {
				m.totalChanceMap[data.Group] = make(map[uint32]uint32)
			}
			m.totalChanceMap[data.Group][data.AttrType1] += data.Chance

			if _, exist := m.attrInfoMap[data.Group]; !exist {
				m.attrInfoMap[data.Group] = make(map[uint32][]*GemAttrInfo)
			}
			m.attrInfoMap[data.Group][data.AttrType1] = append(m.attrInfoMap[data.Group][data.AttrType1], data)

		} else if data.AttrClass == GemAttrTypeBasic {
			m.BTotalChance[data.Group] += data.Chance
			m.BAttrPool[data.Group] = append(m.BAttrPool[data.Group], data)
		}
	}

	for group, attrInfo := range m.totalChanceMap {
		for attrType1, chance := range attrInfo {
			attr := &GemAttrType{
				AttrType1: attrType1,
				Chance:    chance,
			}

			m.attrTypeMap[group] = append(m.attrTypeMap[group], attr)
		}
	}
	return nil
}

func (m *GemAttrInfoManager) GetRecordById(id uint32) *GemAttrInfo {
	return m.Datas[id]
}

func (m *GemAttrInfoManager) Index(key uint32) *GemAttrInfo {
	return m.Datas[key]
}

func (m *GemAttrInfoManager) GetAttrTypes(group uint32) []*GemAttrType {
	return m.attrTypeMap[group]
}

func (m *GemAttrInfoManager) GetChance(group uint32) uint32 {
	return m.chanceMap[group]
}

func (t *GemAttrType) GetWeight() int {
	return int(t.Chance)
}

// GenAdvancedAttr ： 随机生成宝石的高级属性
// 1. 需要先根据权重随机生成高级属性type, 再根据attr type 的权重随机生成具体的属性id
// inheritAttrs：继承的属性，随机时，需要把这个attr去掉
func (m *GemAttrInfoManager) GenAdvancedAttr(rd *rand.Rand, group, num uint32, inheritAttrs []uint32) []uint32 {
	if num == 0 {
		l4g.Error("Generate gem advance attr failed. num error")
		return nil
	}
	attrPool, totalWeight := m.resetAttrPoolAndWeight(group, inheritAttrs)
	if len(attrPool) == 0 {
		l4g.Error("Generate gem advance attr failed. group config error. %d", group)
		return nil
	}
	if totalWeight == 0 {
		l4g.Error("Generate gem advance attr failed. totalWeight config error %d", totalWeight)
		return nil
	}
	if int(num) > len(attrPool) {
		l4g.Error("Generate gem advance attr failed. num error %d %d", num, len(attrPool))
		return nil
	}

	data, err := RandomSomeNoRepeat(rd, int(num), int(totalWeight), attrPool)
	if err != nil {
		l4g.Error("Generate gem advance attr failed. %s", err)
		return nil
	}
	attrTypes := make([]uint32, 0, num)
	for _, d := range data {
		attrTypes = append(attrTypes, d.AttrType1)
	}

	gemAttrs := attrTypes[:0]
	for _, attrType1 := range attrTypes {
		if attrId := m.randomAttr(rd, group, attrType1); attrId > 0 {
			gemAttrs = append(gemAttrs, attrId)
		}
	}

	return gemAttrs
}

// 加权随机算法原理：计算出权重总和，取出一个随机数，遍历所有元素，把权重相加sum，当sum大于随机数字的时候停止，取出当前的id
func (m *GemAttrInfoManager) randomAttr(rd *rand.Rand, group, attrType1 uint32) (attrId uint32) {
	totalWeight := m.totalChanceMap[group][attrType1]
	if totalWeight == uint32(0) {
		return
	}

	randNum := rd.Intn(int(totalWeight))
	var curTotal uint32
	for _, attrInfo := range m.attrInfoMap[group][attrType1] {
		curTotal += attrInfo.Chance
		if uint32(randNum) < curTotal {
			attrId = attrInfo.Id
			break
		}
	}

	return
}

func (m *GemAttrInfoManager) GenBasicAttrs(rand *rand.Rand, group uint32) []uint32 {
	attrs := make([]uint32, 0, 2)
	totalChance := m.BTotalChance[group]
	if totalChance == uint32(0) {
		l4g.Errorf("GenBasicAttrs: random generate Attr failed. totalChance is zero")
		return nil
	}

	randNum := rand.Intn(int(totalChance))
	var curTotal uint32
	for _, v := range m.BAttrPool[group] {
		curTotal += v.Chance
		if uint32(randNum) < curTotal {
			attrs = append(attrs, v.Id)
			break
		}
	}

	return attrs
}

func (m *GemAttrInfoManager) resetAttrPoolAndWeight(group uint32, inheritAttrs []uint32) ([]*GemAttrType, uint32) {
	if len(inheritAttrs) == 0 {
		return m.GetAttrTypes(group), m.GetChance(group)
	}

	tmp := make(map[uint32]struct{})
	for _, id := range inheritAttrs {
		if attrInfo, exist := m.Datas[id]; exist {
			tmp[attrInfo.AttrType1] = struct{}{}
		}
	}

	attrs := make([]*GemAttrType, 0, 3)
	var totalWeight uint32
	for _, v := range m.GetAttrTypes(group) {
		if _, exist := tmp[v.AttrType1]; !exist {
			attrs = append(attrs, v)
			totalWeight += v.Chance
		}
	}

	if len(attrs) == 0 {
		return m.GetAttrTypes(group), m.GetChance(group)
	}

	return attrs, totalWeight
}

func (m *GemAttrInfoManager) GenGemAttr(id uint32, attrs []int64) {
	info := m.Index(id)
	if info == nil {
		return
	}

	if len(attrs) < AttrMaxNum {
		return
	}

	attrs[info.AttrType1] += info.AttrValue1
	if info.AttrType2 > 0 {
		attrs[info.AttrType2] += info.AttrValue2
	}
}
