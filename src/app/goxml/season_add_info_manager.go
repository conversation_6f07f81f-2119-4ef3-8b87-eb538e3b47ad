package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SeasonAddInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*OneSeasonAddInfo // key: seasonId
}

type OneSeasonAddInfo struct {
	OneTypeData map[uint32]*SeasonAddOneTypeInfo // 不同加成类型的数据   key：SeasonAddInfo.Type 加成类型
	SysIdData   map[uint32][]uint32              // 涉及的不同的SysId数据   key：handbookType value：sysId
}

type SeasonAddOneTypeInfo struct {
	Data map[uint32]map[uint32][]*SeasonAddInfo // key: 图鉴类型  value：(key：ID )
}

func newSeasonAddInfoManager(xmlData *XmlData) *SeasonAddInfoManager {
	m := &SeasonAddInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *SeasonAddInfoManager) name() string {
	return "season_add_info.xml"
}

func (m *SeasonAddInfoManager) ignoreForCheck() bool {
	return false
}

func (m *SeasonAddInfoManager) checkData() error {
	for _, data1 := range m.Datas {
		for _, data2 := range data1.OneTypeData {
			for _, data3 := range data2.Data {
				for _, data4 := range data3 {
					for _, data5 := range data4 {
						if data5.AddType == SeasonAddHandbookPokemonStar {
							if m.xmlData.PokemonInfoM.GetRecordByPokemonId(data5.AddValue) == nil {
								panic(fmt.Sprintf("data check error: addValue pokemon is error. seasonAddId:%d", data5.Id))
							}
						}
					}
				}
			}
		}
	}
	return nil
}

func (m *SeasonAddInfoManager) load(dir string, show bool) error {
	tmp := &SeasonAddInfos{}
	fileName := filepath.Join(dir, "season_add_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*OneSeasonAddInfo, len(tmp.Datas))
	}
	var isComplianceCheck uint32
	for _, data := range tmp.Datas {
		data.prepare()

		oneSeasonAddInfo := m.Datas[data.SeasonId]
		if oneSeasonAddInfo == nil {
			oneSeasonAddInfo = &OneSeasonAddInfo{
				OneTypeData: make(map[uint32]*SeasonAddOneTypeInfo),
				SysIdData:   make(map[uint32][]uint32),
			}
			m.Datas[data.SeasonId] = oneSeasonAddInfo
		}

		switch data.AddType {
		case SeasonAddHandbookHeroStar, SeasonAddHandbookHeroEmblemExclusiveLv:
			if m.xmlData.HeroInfoM.Index(data.AddValue) == nil {
				panic(fmt.Sprintf("data check error: addValue hero is error. fileName:%s id:%d", fileName, data.Id))
			}
		case SeasonAddHandbookArtifactStar:
			if m.xmlData.ArtifactInfoM.Index(data.AddValue) == nil {
				panic(fmt.Sprintf("data check error: addValue artifact is error. fileName:%s id:%d", fileName, data.Id))
			}
		case SeasonAddHandbookPokemonStar:
			// 在check时检查
		default:
			panic(fmt.Sprintf("data check error: addType is error. fileName:%s id:%d", fileName, data.Id))
		}

		if !util.InUint32s(oneSeasonAddInfo.SysIdData[data.AddType], data.AddValue) {
			oneSeasonAddInfo.SysIdData[data.AddType] = append(oneSeasonAddInfo.SysIdData[data.AddType], data.AddValue)
		}

		if oneSeasonAddInfo.OneTypeData[data.Type] == nil {
			oneSeasonAddInfo.OneTypeData[data.Type] = &SeasonAddOneTypeInfo{
				Data: make(map[uint32]map[uint32][]*SeasonAddInfo),
			}
		}

		if oneSeasonAddInfo.OneTypeData[data.Type].Data[data.AddType] == nil {
			oneSeasonAddInfo.OneTypeData[data.Type].Data[data.AddType] = make(map[uint32][]*SeasonAddInfo)
		}

		dataLens := len(oneSeasonAddInfo.OneTypeData[data.Type].Data[data.AddType][data.AddValue])
		if dataLens == 0 {
			isComplianceCheck = data.IsCompliance
		} else {
			if isComplianceCheck != oneSeasonAddInfo.OneTypeData[data.Type].Data[data.AddType][data.AddValue][dataLens-1].IsCompliance {
				panic(fmt.Sprintf("data check error: is_compliance is error. fileName:%s id:%d", m.name(), data.Id))
			}
		}
		oneSeasonAddInfo.OneTypeData[data.Type].Data[data.AddType][data.AddValue] = append(oneSeasonAddInfo.OneTypeData[data.Type].Data[data.AddType][data.AddValue], data)
	}
	return nil
}

func (m *SeasonAddInfoManager) GetOneSeasonAddInfo(seasonId uint32) *OneSeasonAddInfo {
	return m.Datas[seasonId]
}

func (m *SeasonAddInfoManager) GetSeasonAddOneTypeInfo(seasonId, typeId uint32) map[uint32]map[uint32][]*SeasonAddInfo {
	oneSeasonAddInfo := m.GetOneSeasonAddInfo(seasonId)
	if oneSeasonAddInfo == nil {
		return nil
	}
	if oneSeasonAddInfo.OneTypeData[typeId] == nil {
		return nil
	}
	return oneSeasonAddInfo.OneTypeData[typeId].Data
}

func (m *SeasonAddInfoManager) IsComplianceAdd(seasonID, addType, addValue uint32) bool {
	OneSeasonAddInfos := m.GetOneSeasonAddInfo(seasonID)
	if OneSeasonAddInfos == nil {
		return false
	}

	for _, OneTypeData := range OneSeasonAddInfos.OneTypeData {
		if OneTypeData != nil && OneTypeData.Data != nil && len(OneTypeData.Data[addType][addValue]) > 0 {
			return OneTypeData.Data[addType][addValue][0].IsCompliance > 0
		}
	}
	return false
}
