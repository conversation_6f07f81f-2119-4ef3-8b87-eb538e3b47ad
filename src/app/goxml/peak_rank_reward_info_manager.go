package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type PeakRankRewardInfoManager struct {
	xmlData *XmlData
	datas   map[uint32]*PeakRankRewardInfo
	groups  map[uint32][]*PeakRankRewardInfo
}

func newPeakRankRewardInfoManager(xmlData *XmlData) *PeakRankRewardInfoManager {
	m := &PeakRankRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *PeakRankRewardInfoManager) name() string {
	return "PeakRankRewardInfo"
}

func (m *PeakRankRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *PeakRankRewardInfoManager) checkData() error {
	return nil
}

func (m *PeakRankRewardInfoManager) load(dir string, isShow bool) error {
	tmp := &PeakRankRewardInfos{}
	fileName := filepath.Join(dir, "peak_rank_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	dataCount := len(tmp.Datas)
	if m.datas == nil {
		m.datas = make(map[uint32]*PeakRankRewardInfo, dataCount)
	}
	lastRankMin := make(map[uint32]uint32)
	var lastScore uint32
	m.groups = make(map[uint32][]*PeakRankRewardInfo, len(PeakRewardTypes))

	for _, data := range tmp.Datas {
		data.prepare()
		if len(data.ClRes) == 0 {
			panic(fmt.Sprintf("no reward. id:%d, file:%s", data.Id, fileName))
		}

		if !util.InUint32s(PeakRewardTypes, data.Type) {
			panic(fmt.Sprintf("illegal type. id:%d, file:%s", data.Id, fileName))
		}

		if data.RankMax > data.RankMin {
			panic(fmt.Sprintf("illegal rank. id:%d, file:%s", data.Id, fileName))
		}

		if data.RankMax <= lastRankMin[data.Type] {
			panic(fmt.Sprintf("rank discontinuous. id:%d, file:%s", data.Id, fileName))
		}

		//首个排名必须是1
		if lastRankMin[data.Type] == 0 && data.RankMax != 1 {
			panic(fmt.Sprintf("first rank not 1. id:%d, file:%s", data.Id, fileName))
		}

		if data.Type == PeakRewardTypePhase {
			if data.RankMax != data.RankMin {
				panic(fmt.Sprintf("rank not same. id:%d, file:%s", data.Id, fileName))
			}

			if data.RankMax > 1 && data.Score > lastScore {
				panic(fmt.Sprintf("score illegal. id:%d, file:%s", data.Id, fileName))
			}
			lastScore = data.Score
		} else {
			if data.RankMax != lastRankMin[data.Type]+1 {
				panic(fmt.Sprintf("season rank discontinuous. id:%d, file:%s", data.Id, fileName))
			}
		}
		lastRankMin[data.Type] = data.RankMin

		if ptr, exist := m.datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.datas[data.Id] = data
		}

		if _, exist := m.groups[data.Type]; !exist {
			m.groups[data.Type] = make([]*PeakRankRewardInfo, 0, dataCount/2)
		}
		m.groups[data.Type] = append(m.groups[data.Type], data)
	}

	if len(m.groups) != len(PeakRewardTypes) {
		panic(fmt.Sprintf("group data not enough. file:%s", fileName))
	}
	return nil
}

func (m *PeakRankRewardInfoManager) GetRecordById(id uint32) *PeakRankRewardInfo {
	return m.datas[id]
}

func (m *PeakRankRewardInfoManager) Index(id uint32) *PeakRankRewardInfo {
	return m.datas[id]
}

func (m *PeakRankRewardInfoManager) GetInitScore() uint32 {
	info := m.GetRankInfo(PeakRewardTypePhase, PeakPlayerCount)
	if info == nil {
		l4g.Debugf("PeakRankRewardInfoM.GetInitScore: no info")
		return 0
	}
	return info.Score
}

func (pi *PeakRankRewardInfo) GetRank() uint32 {
	return pi.RankMax
}

// 获取奖励配置数据
// @param uint32 rewardType 奖励类型
// @param uint32 rank 排名
// @return *PeakRankRewardInfo
func (m *PeakRankRewardInfoManager) GetRankInfo(rewardType, rank uint32) *PeakRankRewardInfo {
	if rank > m.GetLastRank(rewardType) {
		return nil
	}
	return GetDataLessAndClosedByRank(m.groups[rewardType], rank)
}

// 获取最后一个排名
// @param uint32 rewardType 奖励类型
// @return uint32
func (m *PeakRankRewardInfoManager) GetLastRank(rewardType uint32) uint32 {
	if len(m.groups[rewardType]) == 0 {
		return 0
	}

	return m.groups[rewardType][len(m.groups[rewardType])-1].RankMin
}

// 获取奖励
// @param uint32 rewardType 奖励类型
// @param uint32 rank 排名
// @param int64 fightTm 战斗开始时间
// @return []*cl.Resource
func (m *PeakRankRewardInfoManager) GetReward(rewardType, rank uint32,
	awardTm int64) []*cl.Resource {
	info := m.GetRankInfo(rewardType, rank)
	if info == nil {
		return nil
	}

	awards := make([]*cl.Resource, 0, len(info.ClRes))
	for _, res := range info.ClRes {
		newAward := res.Clone()
		if newAward.Type == uint32(common.RESOURCE_AVATAR) {
			avatarInfo := m.xmlData.AvatarInfoM.Index(newAward.Value)
			if avatarInfo == nil {
				l4g.Errorf("PeakRankRewardInfoM.GetReward: avatarInfo not exist, id:%d", newAward.Value)
				continue
			}
			if avatarInfo.DurationType == AvatarTimeTypeEndTime {
				newAward.Attrs = []*cl.Attr{{
					Value: awardTm + int64(avatarInfo.Duration*util.MinuteSecs),
				}}
			}
		}
		awards = append(awards, newAward)
	}
	return awards
}
