package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type MedalSkinInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]*MedalSkinInfo
	events   map[uint32]struct{} // 功勋所有的任务Type
	eliteIDs map[uint32]struct{}
}

func newMedalSkinInfoManager(xmlData *XmlData) *MedalSkinInfoManager {
	m := &MedalSkinInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MedalSkinInfoManager) name() string {
	return "MedalSkinInfo"
}

func (m *MedalSkinInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MedalSkinInfoManager) checkData() error {
	return nil
}

func (m *MedalSkinInfoManager) load(dir string, isShow bool) error {
	tmp := &MedalSkinInfos{}
	fileName := filepath.Join(dir, "medal_skin_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*MedalSkinInfo, len(tmp.Datas))
	}
	m.events = make(map[uint32]struct{})
	m.eliteIDs = make(map[uint32]struct{})

	isRepeat := make(map[uint32]struct{})
	for _, data := range tmp.Datas {
		data.prepare()

		if data.TypeId > 0 {
			if data.Value == 0 || m.xmlData.GoddessContractEliteInfoM.Index(data.Value) == nil {
				panic(fmt.Sprintf("load config %s fail. task value is invalid. id: %d, value: %d", fileName, data.Id, data.Value))
			}

			if _, exist := isRepeat[data.Value]; exist {
				panic(fmt.Sprintf("load config %s fail. task value is repeat. id: %d, value: %d", fileName, data.Id, data.Value))
			}
			isRepeat[data.Value] = struct{}{}

			taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
			if taskTypeInfo == nil {
				panic(fmt.Sprintf("load config %s fail. taskType config err. taskType: %d. ID: %d", fileName, data.TypeId, data.Id))
			}
			m.events[taskTypeInfo.Type] = struct{}{}

			m.eliteIDs[data.Value] = struct{}{}
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *MedalSkinInfoManager) GetRecordById(id uint32) *MedalSkinInfo {
	return m.Datas[id]
}

func (m *MedalSkinInfoManager) Index(key uint32) *MedalSkinInfo {
	return m.Datas[key]
}

func (m *MedalSkinInfoManager) MedalGoddessEvents() map[uint32]struct{} {
	return m.events
}

func (m *MedalSkinInfoManager) IsExistOfEliteID(eliteID uint32) bool {
	if _, exist := m.eliteIDs[eliteID]; exist {
		return true
	}

	return false
}
