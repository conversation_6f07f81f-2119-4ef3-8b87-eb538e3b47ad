package goxml

import (
	"app/logic/event"
	"app/protos/out/cl"
	"fmt"
	"gitlab.qdream.com/kit/sea/time"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type CarnivalTaskInfoManager struct {
	xmlData          *XmlData
	Datas            map[uint32]*CarnivalTaskInfo
	allEvents        []uint32
	event            map[uint32]map[uint32]uint32   // taskId => carnivalId => recordDay
	Tasks            map[uint32][]*CarnivalTaskInfo // taskType => []taskInfo
	carnivalEvents   map[uint32]map[uint32]struct{} // carnivalId => typeId
	carnivalDayTasks map[uint64][]*CarnivalTaskInfo // carnivalId + dayNum => []taskInfo
}

func newCarnivalTaskInfoManager(xmlData *XmlData) *CarnivalTaskInfoManager {
	m := &CarnivalTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *CarnivalTaskInfoManager) name() string {
	return "CarnivalTaskInfo"
}

func (m *CarnivalTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *CarnivalTaskInfoManager) checkData() error {
	return nil
}

func (m *CarnivalTaskInfoManager) load(dir string, isShow bool) error {
	tmp := &CarnivalTaskInfos{}
	fileName := filepath.Join(dir, "carnival_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*CarnivalTaskInfo, len(tmp.Datas))
	}
	m.event = make(map[uint32]map[uint32]uint32)
	m.allEvents = make([]uint32, 0, 100)
	events := make(map[uint32]map[uint32]uint32) // key-任务的逻辑类型 key-嘉年华id value-此逻辑类型的记录方式
	m.Tasks = make(map[uint32][]*CarnivalTaskInfo)
	m.carnivalEvents = make(map[uint32]map[uint32]struct{})
	m.carnivalDayTasks = make(map[uint64][]*CarnivalTaskInfo)

	for _, data := range tmp.Datas {
		data.prepare()
		taskType := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskType == nil {
			panic(fmt.Sprintf("check data error %s %d. taskType not exist", fileName, data.Id))
		}
		carnival := m.xmlData.CarnivalInfoM.Index(data.CarnivalId)
		if carnival == nil {
			panic(fmt.Sprintf("check data error %s %d. carnivalInfo Not exist", fileName, data.Id))
		}

		if _, exist := m.event[taskType.Type]; exist {
			if _, exist := m.event[taskType.Type][data.CarnivalId]; !exist {
				setRecordDay(m.event[taskType.Type], carnival, data)
			}
		} else {
			tmpMap := make(map[uint32]uint32)
			setRecordDay(tmpMap, carnival, data)
			m.event[taskType.Type] = tmpMap
		}

		if recordMsg, exist := events[taskType.Type]; exist {
			if recordType, exist := recordMsg[data.CarnivalId]; exist {
				if recordType != data.RecordType {
					panic(fmt.Sprintf("check data error %s %d. same taskLogicType recordType different", fileName, data.Id))
				}
			} else {
				events[taskType.Type][data.CarnivalId] = data.RecordType
			}
		} else {
			tmpMap := make(map[uint32]uint32)
			tmpMap[data.CarnivalId] = data.RecordType
			events[taskType.Type] = tmpMap
		}

		m.Tasks[data.TypeId] = append(m.Tasks[data.TypeId], data)

		key := uint64(data.CarnivalId)<<32 | uint64(data.DayNumber)
		m.carnivalDayTasks[key] = append(m.carnivalDayTasks[key], data)

		if _, exist := m.carnivalEvents[data.CarnivalId]; !exist {
			m.carnivalEvents[data.CarnivalId] = make(map[uint32]struct{})
		}
		m.carnivalEvents[data.CarnivalId][taskType.Type] = struct{}{}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}

	for k := range events {
		m.allEvents = append(m.allEvents, k)
	}
	return nil
}

func setRecordDay(paramMap map[uint32]uint32, carnival *CarnivalInfo, carnivalTask *CarnivalTaskInfo) {
	if carnivalTask.RecordType == CarnivalTaskEventRecordFromServiceOpen {
		paramMap[carnivalTask.CarnivalId] = 0
	}
	if carnivalTask.RecordType == CarnivalTaskEventRecordFromCarnivalOpen {
		paramMap[carnivalTask.CarnivalId] = carnival.OpenDay
	}
	if carnivalTask.RecordType == CarnivalTaskEventRecordFromCarnivalDayOpen {
		paramMap[carnivalTask.CarnivalId] = carnival.OpenDay + carnivalTask.DayNumber - 1
	}
	if carnivalTask.RecordType == CarnivalTaskEventRecordFromSeasonEnter {
		paramMap[carnivalTask.CarnivalId] = CarnivalSeasonEnterDay
	}
}

func (m *CarnivalTaskInfoManager) GetRecordById(id uint32) *CarnivalTaskInfo {
	return m.Datas[id]
}

func (m *CarnivalTaskInfoManager) Index(key uint32) *CarnivalTaskInfo {
	return m.Datas[key]
}

func (m *CarnivalTaskInfoManager) CarnivalEvents() []uint32 {
	return m.allEvents
}

func (m *CarnivalTaskInfoManager) GetCompletedTasks(progress map[uint32]*cl.TaskTypeProgress) (carnivals []*CarnivalTaskInfo) {
	for typeID, p := range progress {
		if taskInfos, exist := m.Tasks[typeID]; exist {
			for _, info := range taskInfos {
				if p.Progress >= uint64(info.Value) {
					carnivals = append(carnivals, info)
				}
			}
		}
	}

	return
}

// GetIntervalDayById 获取不同类型嘉年华的间隔天数
func (m *CarnivalTaskInfoManager) GetIntervalDayById(carnivalId uint32, createTime, seasonEnterTime int64) uint32 {
	var startTime int64
	carnivalType := m.xmlData.CarnivalInfoM.GetCarnivalTypeById(carnivalId)
	switch carnivalType {
	case CarnivalTypeNormal:
		startTime = createTime // 玩家创角时间
	case CarnivalTypeSeason:
		info := m.xmlData.CarnivalInfoM.Index(carnivalId)
		if info == nil {
			l4g.Errorf("GetIntervalDayById: info is nil. carnivalId %d", carnivalId)
			return 0
		}
		startTime = m.xmlData.SeasonInfoM.GetCurrentSeasonEnterTime(info.SeasonId, seasonEnterTime) // 玩家进赛季时间
	default:
		l4g.Errorf("GetIntervalDayById: carnivalType %d illegal. carnivalId %d", carnivalType, carnivalId)
		return 0
	}
	if startTime == 0 {
		return 0
	}
	return DaysAfterStartOfService(startTime, time.Now().Unix())
}

// IsScoreEvent 是否积分任务
func (m *CarnivalTaskInfoManager) IsScoreEvent(eventId uint32) bool {
	return eventId == event.AeCarnivalPoints || eventId == event.AeSeasonCarnivalPoints
}

// CanUpdateEvent 嘉年华任务进度是否可以更新
func (m *CarnivalTaskInfoManager) CanUpdateEvent(eventId, intervalDay, stopDay, closeDay, recordDay uint32) bool {
	// 非积分任务：recordDay <= intervalDay <= stopDay
	// 积分任务：recordDay <= intervalDay <= closeDay
	if intervalDay < recordDay {
		return false
	}

	if !m.IsScoreEvent(eventId) {
		if intervalDay > stopDay {
			return false
		}
	} else {
		if intervalDay > closeDay {
			return false
		}
	}

	return true
}

// GetUpdateCarnivalsByEvent 根据任务获取更新的嘉年华
func (m *CarnivalTaskInfoManager) GetUpdateCarnivalsByEvent(eventId uint32, createTime, seasonEnterTime int64) []uint32 {
	if m.event[eventId] == nil {
		return nil
	}
	var carnivalIds []uint32
	for carnivalId, recordDay := range m.event[eventId] {
		carnivalInfo := m.xmlData.CarnivalInfoM.Index(carnivalId)
		if carnivalInfo == nil {
			l4g.Debugf("yta test OnCarnivalEvent1: carnivalInfo nil. %d", carnivalId)
			continue
		}

		intervalDay := m.GetIntervalDayById(carnivalId, createTime, seasonEnterTime)
		if !m.CanUpdateEvent(eventId, intervalDay, carnivalInfo.StopDay, carnivalInfo.CloseDay, recordDay) {
			l4g.Debugf("yta test OnCarnivalEvent1: not CanUpdateEvent. eventId %d intervalDay %d carnivalInfo.StopDay %d carnivalInfo.CloseDay %d recordDay %d", eventId, intervalDay, carnivalInfo.StopDay, carnivalInfo.CloseDay, recordDay)
			continue
		}
		carnivalIds = append(carnivalIds, carnivalId)
	}

	return carnivalIds
}

func (m *CarnivalTaskInfoManager) GetRecordByCarnivalIdDayNumber(carnivalId, day uint32) []*CarnivalTaskInfo {
	return m.carnivalDayTasks[uint64(carnivalId)<<32|uint64(day)]
}
