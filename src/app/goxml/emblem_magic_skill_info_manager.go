package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/math/rand"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type EmblemMagicSkillInfoManager struct {
	xmlData      *XmlData
	Datas        map[uint32]*EmblemMagicSkillInfo
	Groups       map[uint32][]*EmblemMagicSkillInfo
	GroupsWeight map[uint32]uint32
}

func newEmblemMagicSkillInfoManager(xmlData *XmlData) *EmblemMagicSkillInfoManager {
	m := &EmblemMagicSkillInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *EmblemMagicSkillInfoManager) name() string {
	return "EmblemMagicSkillInfo"
}

func (m *EmblemMagicSkillInfoManager) ignoreForCheck() bool {
	return false
}

func (m *EmblemMagicSkillInfoManager) checkData() error {
	return nil
}

func (m *EmblemMagicSkillInfoManager) load(dir string, isShow bool) error {
	tmp := &EmblemMagicSkillInfos{}
	fileName := filepath.Join(dir, "emblem_magic_skill_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*EmblemMagicSkillInfo, len(tmp.Datas))
	m.Groups = make(map[uint32][]*EmblemMagicSkillInfo)
	m.GroupsWeight = make(map[uint32]uint32)

	for _, data := range tmp.Datas {
		if data.Chance == 0 || data.Skill == 0 {
			panic(fmt.Sprintf("load config %s fail: %d Chance or Skill is 0", fileName, data.Id))
		}

		if data.Type != EmblemSkillTypeNormal && data.Type != EmblemSkillTypeAdditive {
			panic(fmt.Sprintf("EmblemMagicSkillInfoManager data id:%d type:%d is err", data.Id, data.Type))
		}

		if ptr, exist := m.Datas[data.Skill]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Skill] = data
		}
		_, exist := m.Groups[data.MagicSkill]
		if !exist {
			m.Groups[data.MagicSkill] = make([]*EmblemMagicSkillInfo, 0, 20)
		}
		m.Groups[data.MagicSkill] = append(m.Groups[data.MagicSkill], data)
		m.GroupsWeight[data.MagicSkill] += data.Chance
	}
	return nil
}

func (m *EmblemMagicSkillInfoManager) GetRecordById(id uint32) *EmblemMagicSkillInfo {
	return m.Datas[id]
}

func (m *EmblemMagicSkillInfoManager) Index(key uint32) *EmblemMagicSkillInfo {
	return m.Datas[key]
}

func (m *EmblemMagicSkillInfoManager) Group(key uint32) []*EmblemMagicSkillInfo {
	return m.Groups[key]
}

func (m *EmblemMagicSkillInfoManager) RandSkill(rd *rand.Rand, magicSkill uint32) uint32 {
	skillList := m.Group(magicSkill)
	if len(skillList) == 0 {
		return 0
	}

	number := uint32(rd.RandBetween(0, int(m.GroupsWeight[magicSkill])))
	for _, skillInfo := range skillList {
		if skillInfo.Chance < number {
			number -= skillInfo.Chance
			continue
		}
		return skillInfo.Skill
	}
	return 0
}

func (m *EmblemMagicSkillInfoManager) IsAdditiveSkill(skillId uint32) bool {
	info, exist := m.Datas[skillId]
	if !exist {
		return false
	}

	return info.Type == EmblemSkillTypeAdditive
}
