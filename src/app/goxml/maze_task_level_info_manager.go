package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"
	"sort"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type MazeTaskLevelInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*MazeTaskLevelInfo
	datas   []*MazeTaskLevelInfo
	items   []*MazeTaskLevelInfo
}

func newMazeTaskLevelInfoManager(xmlData *XmlData) *MazeTaskLevelInfoManager {
	m := &MazeTaskLevelInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MazeTaskLevelInfoManager) name() string {
	return "MazeTaskLevelInfo"
}

func (m *MazeTaskLevelInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MazeTaskLevelInfoManager) checkData() error {
	return nil
}

func (m *MazeTaskLevelInfoManager) load(dir string, isShow bool) error {
	tmp := &MazeTaskLevelInfos{}
	fileName := filepath.Join(dir, "maze_task_level_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*MazeTaskLevelInfo, len(tmp.Datas))
	}
	m.datas = make([]*MazeTaskLevelInfo, 0, len(tmp.Datas))
	m.items = make([]*MazeTaskLevelInfo, 0, len(tmp.Datas)/2)
	for _, data := range tmp.Datas {
		data.prepare()
		if len(data.ClRes) == 0 {
			panic(fmt.Sprintf("maze_task_level_info config err, award is nil. level: %d", data.Level))
		}
		m.datas = append(m.datas, data)
		if data.RewardType == MazeTaskLevelAwardTypePrivilege {
			m.items = append(m.items, data)
		}

		if ptr, exist := m.Datas[data.Level]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Level] = data
		}
	}
	return nil
}

func (m *MazeTaskLevelInfoManager) GetRecordByLevel(level uint32) *MazeTaskLevelInfo {
	return m.Datas[level]
}

func (m *MazeTaskLevelInfoManager) Index(key uint32) *MazeTaskLevelInfo {
	return m.Datas[key]
}

func (m *MazeTaskLevelInfoManager) GetPrivilegeItem(level uint32) []*cl.Resource {
	if _, exist := m.Datas[level]; !exist {
		return nil
	}

	if len(m.items) > 0 {
		targetIdx := sort.Search(len(m.items), func(i int) bool {
			return level < m.items[i].Level
		})
		if targetIdx-1 >= 0 && targetIdx-1 < len(m.items) {
			return m.items[targetIdx-1].ClRes
		}
	}

	return nil
}

func (m *MazeTaskLevelInfoManager) GetData(score uint32) *MazeTaskLevelInfo {
	if len(m.datas) > 0 {
		targetIdx := sort.Search(len(m.datas), func(i int) bool {
			return score < m.datas[i].Score
		})
		if targetIdx-1 >= 0 && targetIdx-1 < len(m.datas) {
			return m.datas[targetIdx-1]
		}
	}

	return nil
}
