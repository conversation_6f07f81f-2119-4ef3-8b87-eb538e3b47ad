package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildDungeonSeasonStrategyInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildDungeonSeasonStrategyInfo
}

func newGuildDungeonSeasonStrategyInfoManager(xmlData *XmlData) *GuildDungeonSeasonStrategyInfoManager {
	m := &GuildDungeonSeasonStrategyInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildDungeonSeasonStrategyInfoManager) name() string {
	return "GuildDungeonSeasonStrategyInfo"
}

func (m *GuildDungeonSeasonStrategyInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildDungeonSeasonStrategyInfoManager) checkData() error {
	return nil
}

func (m *GuildDungeonSeasonStrategyInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildDungeonSeasonStrategyInfos{}
	fileName := filepath.Join(dir, "guild_dungeon_season_strategy_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GuildDungeonSeasonStrategyInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()

		if !m.xmlData.GuildDungeonWeekStrategyInfoM.IsWeekGroupExist(data.WeekStrategyAssociateId) {
			panic(fmt.Sprintf("check filename:%s data error. weekGroup not exist. id:%d", fileName, data.Id))
		}

		if m.xmlData.GuildDungeonStrategySkillInfoM.Index(data.SeasonStrategyId) == nil {
			panic(fmt.Sprintf("check filename:%s data error. seasonStrategyId not exist. id:%d", fileName, data.Id))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildDungeonSeasonStrategyInfoManager) GetRecordById(id uint32) *GuildDungeonSeasonStrategyInfo {
	return m.Datas[id]
}

func (m *GuildDungeonSeasonStrategyInfoManager) Index(key uint32) *GuildDungeonSeasonStrategyInfo {
	return m.Datas[key]
}
