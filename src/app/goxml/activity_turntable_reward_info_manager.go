package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type ActivityTurntableRewardInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ActivityTurntableRewardInfo
}

func newActivityTurntableRewardInfoManager(xmlData *XmlData) *ActivityTurntableRewardInfoManager {
	m := &ActivityTurntableRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityTurntableRewardInfoManager) name() string {
	return "activity_turntable_reward_info.xml"
}

func (m *ActivityTurntableRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityTurntableRewardInfoManager) checkData() error {
	return nil
}

func (m *ActivityTurntableRewardInfoManager) load(dir string, show bool) error {
	tmp := &ActivityTurntableRewardInfos{}
	fileName := filepath.Join(dir, "activity_turntable_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ActivityTurntableRewardInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ActivityTurntableRewardInfoManager) Index(key uint32) *ActivityTurntableRewardInfo {
	return m.Datas[key]
}
