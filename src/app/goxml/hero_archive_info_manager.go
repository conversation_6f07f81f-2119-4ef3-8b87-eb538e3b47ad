package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type HeroArchiveInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*HeroArchiveInfoExt
}

type HeroArchiveInfoExt struct {
	Rare       uint32
	ActiveType uint32
	Attrs      []uint32
}

func newHeroArchiveInfoManager(xmlData *XmlData) *HeroArchiveInfoManager {
	m := &HeroArchiveInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HeroArchiveInfoManager) name() string {
	return "HeroArchiveInfo"
}

func (m *HeroArchiveInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HeroArchiveInfoManager) checkData() error {
	return nil
}

func (m *HeroArchiveInfoManager) load(dir string, isShow bool) error {
	tmp := &HeroArchiveInfos{}
	fileName := filepath.Join(dir, "hero_archive_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*HeroArchiveInfoExt)
	}

	for _, data := range tmp.Datas {
		dataExt := &HeroArchiveInfoExt{}
		dataExt.Rare = data.Rare
		if data.ActiveType > 1 {
			panic(fmt.Sprintf("check data error fileName:%s rare:%d activeType error. activeType:%d",
				fileName, data.Rare, data.ActiveType))
		}
		dataExt.ActiveType = data.ActiveType

		if data.AttrType1 > 0 && data.AttrValue1 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType1)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrValue2 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType2)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue2)
		}
		if data.AttrType3 > 0 && data.AttrValue3 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType3)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue3)
		}

		if ptr, exist := m.Datas[dataExt.Rare]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[dataExt.Rare] = dataExt
		}
	}

	return nil
}

func (m *HeroArchiveInfoManager) Index(rare uint32) *HeroArchiveInfoExt {
	return m.Datas[rare]
}
