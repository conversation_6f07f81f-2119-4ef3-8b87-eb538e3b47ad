package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type MailInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*MailInfoExt
}

type MailInfoExt struct {
	Id        uint32 //系统邮件ID
	ParamNum  uint32 //参数个数
	LifeTm    int64  //有效期(秒)
	SenderNum uint32
	TitleNum  uint32
}

func newMailInfoManager(xmlData *XmlData) *MailInfoManager {
	m := &MailInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MailInfoManager) name() string {
	return "MailInfo"
}

func (m *MailInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MailInfoManager) checkData() error {
	return nil
}

func (m *MailInfoManager) load(dir string, isShow bool) error {
	tmp := &MailInfos{}
	fileName := filepath.Join(dir, "mail_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
			if data.DeleteDay == 0 {
				panic(fmt.Sprintf("load config %s fail. DeleteDay error. ID:%d", fileName, data.Id))
			}
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*MailInfoExt, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		dataExt := &MailInfoExt{
			Id:        data.Id,
			ParamNum:  data.SenderNum + data.TitleNum + data.ContentNum,
			LifeTm:    int64(data.DeleteDay) * 86400,
			SenderNum: data.SenderNum,
			TitleNum:  data.TitleNum,
		}
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			m.Datas[data.Id] = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
	}
	return nil
}

func (m *MailInfoManager) GetRecordById(id uint32) *MailInfoExt {
	return m.Datas[id]
}

func (m *MailInfoManager) Index(key uint32) *MailInfoExt {
	return m.Datas[key]
}

// 获取邮件有效期
func (m *MailInfoManager) GetMailLifeTm(mailID uint32, params []string) int64 {
	info := m.Index(mailID)
	if info == nil {
		l4g.Error("mail config not found, mail id: %d", mailID)
		return 0
	}
	if info.ParamNum > 0 {
		if len(params) != int(info.ParamNum) {
			l4g.Error("mail params num error, %d, %d, %d", mailID, len(params), info.ParamNum)
		}
	}
	return info.LifeTm
}
