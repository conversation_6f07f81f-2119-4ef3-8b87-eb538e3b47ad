package goxml

import (
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

const (
	NormalStartID    = 10001
	HardStartID      = 20001
	NightmareStartID = 30001
)

type WorldbossDifficultyInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*WorldbossDifficultyInfo
}

func newWorldbossDifficultyInfoManager(xmlData *XmlData) *WorldbossDifficultyInfoManager {
	m := &WorldbossDifficultyInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *WorldbossDifficultyInfoManager) name() string {
	return "WorldbossDifficultyInfo"
}

func (m *WorldbossDifficultyInfoManager) ignoreForCheck() bool {
	return false
}

func (m *WorldbossDifficultyInfoManager) checkData() error {
	return nil
}

func (m *WorldbossDifficultyInfoManager) load(dir string, isShow bool) error {
	tmp := &WorldbossDifficultyInfos{}
	fileName := filepath.Join(dir, "worldboss_difficulty_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*WorldbossDifficultyInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Type == uint32(common.WORLD_BOSS_LEVEL_WBL_NONE) || data.Type >= uint32(common.WORLD_BOSS_LEVEL_WBL_MAX) {
			panic(fmt.Sprintf("load config %s fail: type is invalid. type:%d", fileName, data.Type))
		}
		isInvalidStartID := false
		switch data.Type {
		case uint32(common.WORLD_BOSS_LEVEL_WBL_NORMAL):
			if data.RoomId != NormalStartID {
				isInvalidStartID = true
			}
		case uint32(common.WORLD_BOSS_LEVEL_WBL_HARD):
			if data.RoomId != HardStartID {
				isInvalidStartID = true
			}
		case uint32(common.WORLD_BOSS_LEVEL_WBL_NIGHT_MIRE):
			if data.RoomId != NightmareStartID {
				isInvalidStartID = true
			}
		}

		if isInvalidStartID {
			panic(fmt.Sprintf("load config %s fail: room start id is invalid. type:%d", fileName, data.Type))
		}

		if ptr, exist := m.Datas[data.Type]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Type] = data
		}
	}
	return nil
}

func (m *WorldbossDifficultyInfoManager) GetRecordByType(t uint32) *WorldbossDifficultyInfo {
	return m.Datas[t]
}

func (m *WorldbossDifficultyInfoManager) Index(key uint32) *WorldbossDifficultyInfo {
	return m.Datas[key]
}

func (w *WorldbossDifficultyInfoManager) GetMaxLevel(score uint32) uint32 {
	maxLevel := uint32(0)
	for _, data := range w.Datas {
		if score >= data.UnlockScore && data.Type > maxLevel {
			maxLevel = data.Type
		}
	}

	return maxLevel
}
