package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type MirageAffixInfoManager struct {
	xmlData *XmlData
	datas   map[uint32]*MirageAffixInfoExt
}

type MirageAffixInfoExt struct {
	Id       uint32   //
	RaisePSs []uint64 //被动技能
	Attrs    map[int]int64
	PowerPct uint32
}

func newMirageAffixInfoManager(xmlData *XmlData) *MirageAffixInfoManager {
	m := &MirageAffixInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MirageAffixInfoManager) name() string {
	return "MirageAffixInfo"
}

func (m *MirageAffixInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MirageAffixInfoManager) checkData() error {
	return nil
}

func (m *MirageAffixInfoManager) load(dir string, isShow bool) error {
	tmp := &MirageAffixInfos{}
	fileName := filepath.Join(dir, "mirage_affix_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	if m.datas == nil {
		m.datas = make(map[uint32]*MirageAffixInfoExt, len(tmp.Datas))
	}

	for _, data := range tmp.Datas {
		if !MirageLegalSkillTarget[data.PassiveSkillTarget] {
			panic(fmt.Sprintf("load %s fail: target illegal. target:%d", fileName, data.PassiveSkillTarget))
		}

		dataExt := &MirageAffixInfoExt{}
		if data.RaisePassiveSkill1 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill1))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill2 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill2, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill2))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill3 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill3, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill3))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}

		attrs := make(map[int]int64, MirageInitAffixAttrNum)
		if data.AttrType1 > 0 && data.AttrValue1 > 0 {
			attrs[int(data.AttrType1)] += int64(data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrValue2 > 0 {
			attrs[int(data.AttrType2)] += int64(data.AttrValue2)
		}
		if data.AttrType3 > 0 && data.AttrValue3 > 0 {
			attrs[int(data.AttrType3)] += int64(data.AttrValue3)
		}
		if data.DecAttrType1 > 0 && data.DecAttrValue1 < 0 { //DecAttrValue1为负时，才有意义
			attrs[int(data.DecAttrType1)] += int64(data.DecAttrValue1)
		}

		if len(attrs) == 0 && len(dataExt.RaisePSs) == 0 {
			panic(fmt.Sprintf("load %s fail: no attr or skill. id:%d", fileName, data.Id))
		}

		dataExt.Id = data.Id
		dataExt.Attrs = attrs
		dataExt.PowerPct = data.PowerPct
		if ptr, exist := m.datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.datas[data.Id] = dataExt
		}
	}
	return nil
}

func (m *MirageAffixInfoManager) GetRecordById(id uint32) *MirageAffixInfoExt {
	return m.datas[id]
}

func (m *MirageAffixInfoManager) Index(key uint32) *MirageAffixInfoExt {
	return m.datas[key]
}
