package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type ActivityTurntableRewardShowInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]*ActivityTurntableRewardShowInfo
	Round       map[uint32]map[uint32][]*ActivityTurntableRewardShowInfo
	SummonParam map[uint32]*ActivityTurntableRewardShowSummonExt
}

type ActivityTurntableRewardShowSummonExt struct {
	Round    uint32
	FixCount uint32
	ChanAdd  uint32
}

func newActivityTurntableRewardShowInfoManager(xmlData *XmlData) *ActivityTurntableRewardShowInfoManager {
	m := &ActivityTurntableRewardShowInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityTurntableRewardShowInfoManager) name() string {
	return "activity_turntable_reward_show_info.xml"
}

func (m *ActivityTurntableRewardShowInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityTurntableRewardShowInfoManager) checkData() error {
	return nil
}

func (m *ActivityTurntableRewardShowInfoManager) load(dir string, show bool) error {
	tmp := &ActivityTurntableRewardShowInfos{}
	fileName := filepath.Join(dir, "activity_turntable_reward_show_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*ActivityTurntableRewardShowInfo, len(tmp.Datas))
	m.Round = make(map[uint32]map[uint32][]*ActivityTurntableRewardShowInfo)
	m.SummonParam = make(map[uint32]*ActivityTurntableRewardShowSummonExt)
	for _, data := range tmp.Datas {
		data.prepare()
		if data.IsDisposable != TurnTableNormal && data.IsDisposable != TurnTableDisposable {
			panic(fmt.Sprintf("config(%s): data id:%d round:%d is error", fileName, data.Id, data.Round))
		}

		if data.IsDisposable == TurnTableDisposable {
			if data.ChanceAdd == 0 || data.FixedCount == 0 {
				panic(fmt.Sprintf("config(%s): data id:%d ", fileName, data.Id))
			}
			info, exist := m.SummonParam[data.Round]
			if !exist {
				m.SummonParam[data.Round] = &ActivityTurntableRewardShowSummonExt{
					Round:    data.Round,
					FixCount: data.FixedCount,
					ChanAdd:  data.ChanceAdd,
				}
			} else {
				if info.FixCount != data.FixedCount || info.ChanAdd != data.ChanceAdd {
					panic(fmt.Sprintf("config(%s): data id:%d ", fileName, data.Id))
				}
			}
		} else if data.IsDisposable == TurnTableNormal {
			if data.Chance == 0 {
				panic(fmt.Sprintf("config(%s): data id:%d  chance is nil ", fileName, data.Id))
			}
		}

		_, exist := m.Round[data.Round]
		if !exist {
			m.Round[data.Round] = make(map[uint32][]*ActivityTurntableRewardShowInfo)
		}

		_, exist = m.Round[data.Round][data.IsDisposable]
		if !exist {
			m.Round[data.Round][data.IsDisposable] = make([]*ActivityTurntableRewardShowInfo, 0, 3)
		}
		m.Round[data.Round][data.IsDisposable] = append(m.Round[data.Round][data.IsDisposable], data)

		if _, exist := m.Datas[data.Id]; exist {
			panic(fmt.Sprintf("config(%s): data id:%d is error", fileName, data.Id))
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ActivityTurntableRewardShowInfoManager) Index(key uint32) *ActivityTurntableRewardShowInfo {
	return m.Datas[key]
}

func (a *ActivityTurntableRewardShowInfo) Clone() *ActivityTurntableRewardShowInfo {
	ret := &ActivityTurntableRewardShowInfo{
		Id:           a.Id,
		Round:        a.Round,
		IsDisposable: a.IsDisposable,
		RewardType:   a.RewardType,
		RewardValue:  a.RewardValue,
		RewardCount:  a.RewardCount,
		Chance:       a.Chance,
		ChanceAdd:    a.ChanceAdd,
		FixedCount:   a.FixedCount,
	}
	for _, v := range a.RewardClRes {
		ret.RewardClRes = append(ret.RewardClRes, v.Clone())
	}
	return ret
}

func (a *ActivityTurntableRewardShowInfo) GetWeight() int {
	return int(a.Chance)
}

func (m *ActivityTurntableRewardShowInfoManager) Summon(uid uint64, rd *rand.Rand, round uint32, disposables map[uint32]struct{}, guaranteeCount uint32) *ActivityTurntableRewardShowInfo {
	tmp := make([]*ActivityTurntableRewardShowInfo, 0, 10)
	summonInfo, exist := m.Round[round]
	if !exist {
		l4g.Errorf("user:%d ActivityTurntableRewardShowInfoManager Summon round:%d not exist", uid, round)
		return nil
	}
	paramInfo, exist := m.SummonParam[round]
	if !exist {
		l4g.Errorf("user:%d ActivityTurntableRewardShowInfoManager Summon Param round:%d not exist", uid, round)
		return nil
	}
	var onlyDisposable bool
	if guaranteeCount >= paramInfo.FixCount && len(disposables) < len(m.Round[round][TurnTableDisposable]) {
		onlyDisposable = true
	}
	var totalWeight int
	for disposable, infos := range summonInfo {
		if infos == nil {
			continue
		}
		if onlyDisposable {
			if disposable != TurnTableDisposable {
				continue
			}
			for _, info := range infos {
				_, exist = disposables[info.Id]
				if !exist {
					clone := info.Clone()
					clone.Chance += guaranteeCount * clone.ChanceAdd
					if clone.Chance > 0 {
						totalWeight += int(clone.Chance)
						tmp = append(tmp, clone)
					}
				}
			}
		} else {
			if disposable == TurnTableDisposable {
				for _, info := range infos {
					_, exist = disposables[info.Id]
					if !exist {
						clone := info.Clone()
						clone.Chance += guaranteeCount * clone.ChanceAdd
						if clone.Chance > 0 {
							totalWeight += int(clone.Chance)
							tmp = append(tmp, clone)
						}
					}
				}
			} else {
				for _, info := range infos {
					clone := info.Clone()
					if clone.Chance > 0 {
						totalWeight += int(clone.Chance)
						tmp = append(tmp, clone)
					}
				}
			}
		}

	}

	ret, err := RandomSomeNoRepeat(rd, 1, totalWeight, tmp)
	if err != nil || len(ret) == 0 {
		return nil
	}
	return ret[0]
}

func (m *ActivityTurntableRewardShowInfoManager) GetRoundDisposableCount(round uint32) int {
	return len(m.Round[round][TurnTableDisposable])
}
