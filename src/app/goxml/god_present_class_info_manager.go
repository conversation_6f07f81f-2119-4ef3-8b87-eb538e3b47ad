package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

type GodPresentClassInfoManager struct {
	xmlData       *XmlData
	datas         map[uint32]*GodPresentClassInfoExt
	limitedHeroes []uint32 //单轮抽卡，不可重复的英雄列表
}

type GodPresentClassInfoExt struct {
	Class       uint32 //int:档次
	TotalWeight int
	Awards      []*AwardInfo
}

func newGodPresentClassInfoManager(xmlData *XmlData) *GodPresentClassInfoManager {
	m := &GodPresentClassInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GodPresentClassInfoManager) name() string {
	return "GodPresentClassInfo"
}

func (m *GodPresentClassInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GodPresentClassInfoManager) checkData() error {
	return nil
}

func (m *GodPresentClassInfoManager) load(dir string, isShow bool) error {
	tmp := &GodPresentClassInfos{}
	fileName := filepath.Join(dir, "god_present_class_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.datas = make(map[uint32]*GodPresentClassInfoExt, len(tmp.Datas))
	//uniqHids := make(map[uint32]struct{}, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()

		if data.Type != uint32(common.RESOURCE_HERO) {
			panic(fmt.Sprintf("load config %s fail. type not hero, id:%d", fileName, data.Id))
		}

		if m.xmlData.HeroInfoM.Index(data.Value) == nil {
			panic(fmt.Sprintf("load config %s fail. hero not exist, id:%d", fileName, data.Id))
		}

		// TODO 港澳台版本特殊需求：卡池可以重复
		//if _, exist := uniqHids[data.Value]; exist {
		//	panic(fmt.Sprintf("load config %s fail. hero repeated, id:%d, hid:%d", fileName, data.Id, data.Value))
		//}
		//uniqHids[data.Value] = struct{}{}

		if data.Count != 1 {
			panic(fmt.Sprintf("load config %s fail. count not 1, id:%d", fileName, data.Id))
		}

		if len(data.ClRes) != 1 {
			panic(fmt.Sprintf("load config %s fail. no award, id:%d", fileName, data.Id))
		}

		awardInfo := &AwardInfo{
			Award:  data.ClRes[0],
			Weight: int(data.Weight),
		}
		if _, exist := m.datas[data.Class]; exist {
			m.datas[data.Class].Awards = append(m.datas[data.Class].Awards, awardInfo)
			m.datas[data.Class].TotalWeight += int(data.Weight)
		} else {
			dataExt := &GodPresentClassInfoExt{
				Class:  data.Class,
				Awards: make([]*AwardInfo, 0, 5),
			}
			dataExt.Awards = append(dataExt.Awards, awardInfo)
			dataExt.TotalWeight += int(data.Weight)
			m.datas[data.Class] = dataExt
		}

		//单轮抽卡，不可重复的英雄列表
		if data.IsRepeat == 1 {
			m.limitedHeroes = append(m.limitedHeroes, data.Value)
		}
	}
	return nil
}

func (m *GodPresentClassInfoManager) GetRecordById(id uint32) *GodPresentClassInfoExt {
	return m.datas[id]
}

func (m *GodPresentClassInfoManager) Index(key uint32) *GodPresentClassInfoExt {
	return m.datas[key]
}

// 抽卡
// @param *rand.Rand rd
// @param uint32 classID 卡组id
// @param map[uint32]int weightReduce heroSysID=>要减掉的权重值
// @param []uint32 limitedHids 不可重复抽中的英雄列表
// @return *GodPresentClassInfoExt
// @return bool 是否是限制再次抽中的英雄
func (m *GodPresentClassInfoManager) Summon(rd *rand.Rand, classID uint32,
	weightReduce map[uint32]int, limitedHids []uint32) (*cl.Resource, bool) {
	info := m.Index(classID)
	if info == nil {
		l4g.Errorf("GodPresentClassInfoM.Summon: info not exist. class:%d", classID)
		return nil, false
	}

	fixedInfo := info.fixWeight(weightReduce, limitedHids)
	randomNum := rd.RandBetween(1, fixedInfo.TotalWeight)
	for _, v := range fixedInfo.Awards {
		if randomNum > 0 && randomNum <= v.Weight {
			return v.Award, m.isLimitedHid(v.Award.Value)
		}
		randomNum -= v.Weight
	}

	l4g.Errorf("GodPresentClassInfoM.Summon: failed, class:%d, weightReduce:%+v", classID, weightReduce)
	return nil, false
}

// 计算不可重复抽中的英雄列表
// @param []uint32 gotHids 已抽中的英雄列表
// @param []uint32 不可重复抽中的英雄列表
func (m *GodPresentClassInfoManager) CalcLimitedHids(gotHids []uint32) []uint32 {
	ret := make([]uint32, 0, 1)
	for _, hid := range gotHids {
		if util.InUint32s(m.limitedHeroes, hid) {
			ret = append(ret, hid)
		}
	}
	return ret
}

// 计算不可重复抽中的英雄列表
func (m *GodPresentClassInfoManager) isLimitedHid(hid uint32) bool {
	return util.InUint32s(m.limitedHeroes, hid)
}

// 修正随机权重
// @param map[uint32]int weightReduce heroSysID=>要减掉的权重值
// @param []uint32 limitedHids 不可重复抽中的英雄id列表
// @return *GodPresentClassInfoExt
func (e *GodPresentClassInfoExt) fixWeight(weightReduce map[uint32]int,
	limitedHids []uint32) *GodPresentClassInfoExt {
	if len(weightReduce) == 0 && len(limitedHids) == 0 {
		return e
	}

	ret := &GodPresentClassInfoExt{
		Class:  e.Class,
		Awards: make([]*AwardInfo, 0, len(e.Awards)),
	}

	for _, v := range e.Awards {
		if util.InUint32s(limitedHids, v.Award.Value) {
			continue
		}

		reduce := weightReduce[v.Award.Value]
		if reduce >= v.Weight {
			continue
		}

		awardInfo := &AwardInfo{
			Weight: v.Weight - reduce,
			Award:  v.Award.Clone(),
		}

		ret.TotalWeight += awardInfo.Weight
		ret.Awards = append(ret.Awards, awardInfo)
	}
	return ret
}
