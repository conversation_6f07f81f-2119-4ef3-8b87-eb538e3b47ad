package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type MirageAffixGroupInfoManager struct {
	xmlData *XmlData
	datas   map[uint32][]uint32
	groups  map[uint32]map[uint32]struct{} //key: hurdleID
}

func newMirageAffixGroupInfoManager(xmlData *XmlData) *MirageAffixGroupInfoManager {
	m := &MirageAffixGroupInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MirageAffixGroupInfoManager) name() string {
	return "MirageAffixGroupInfo"
}

func (m *MirageAffixGroupInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MirageAffixGroupInfoManager) checkData() error {
	return nil
}

func (m *MirageAffixGroupInfoManager) load(dir string, isShow bool) error {
	tmp := &MirageAffixGroupInfos{}
	fileName := filepath.Join(dir, "mirage_affix_group_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	m.datas = make(map[uint32][]uint32)
	m.groups = make(map[uint32]map[uint32]struct{})

	for _, data := range tmp.Datas {
		if _, exist := m.groups[data.HurdleId]; !exist {
			m.groups[data.HurdleId] = make(map[uint32]struct{})
		}
		if _, exist := m.groups[data.HurdleId][data.AffixId]; exist {
			panic(fmt.Sprintf("load %s fail: affix is repeat. hurdleID:%d, affixID:%d", fileName, data.HurdleId, data.AffixId))
		}
		m.groups[data.HurdleId][data.AffixId] = struct{}{}

		if _, exist := m.datas[data.HurdleId]; !exist {
			m.datas[data.HurdleId] = make([]uint32, 0, MirageInitAffixNum)
		}
		m.datas[data.HurdleId] = append(m.datas[data.HurdleId], data.AffixId)

		//验证词缀是否存在
		affixInfo := m.xmlData.MirageAffixInfoM.Index(data.AffixId)
		if affixInfo == nil {
			panic(fmt.Sprintf("load %s fail: affixInfo not exist. affixID:%d", fileName, data.AffixId))
		}
	}

	for hurdleID, affixes := range m.groups {
		if len(affixes) != MirageInitAffixNum {
			panic(fmt.Sprintf("load %s fail: affix num not match. hurdleID:%d, affix num:%d", fileName, hurdleID, len(affixes)))
		}
	}
	return nil
}

func (m *MirageAffixGroupInfoManager) Index(hurdleId uint32) []uint32 {
	return m.datas[hurdleId]
}

func (m *MirageAffixGroupInfoManager) IsExist(hurdleId uint32, affixes []uint32) bool {
	for _, id := range affixes {
		if _, exist := m.groups[hurdleId][id]; !exist {
			return false
		}
	}
	return true
}
