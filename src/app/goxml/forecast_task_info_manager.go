package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type ForecastTaskInfoManager struct {
	xmlData       *XmlData
	Datas         map[uint32]*ForecastTaskInfo
	taskInfos     map[uint32][]*ForecastTaskInfo      // key: ForecastID   value: id
	events        map[uint32]struct{}                 // key: typeID
	TaskTypeCache map[uint32]map[uint32]*ForecastTask // key: task type -> key: task Parameter1 -> value
}

type ForecastTask struct {
	TaskID     uint32
	Value      uint64
	FinishType uint32
}

func newForecastTaskInfoManager(xmlData *XmlData) *ForecastTaskInfoManager {
	m := &ForecastTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ForecastTaskInfoManager) name() string {
	return "ForecastTaskInfo"
}

func (m *ForecastTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ForecastTaskInfoManager) checkData() error {
	return nil
}

func (m *ForecastTaskInfoManager) load(dir string, isShow bool) error {
	tmp := &ForecastTaskInfos{}
	fileName := filepath.Join(dir, "forecast_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ForecastTaskInfo, len(tmp.Datas))
	}
	m.taskInfos = make(map[uint32][]*ForecastTaskInfo)
	m.events = make(map[uint32]struct{})
	m.TaskTypeCache = make(map[uint32]map[uint32]*ForecastTask)
	checkTypeMap := make(map[uint32]uint32)
	for _, data := range tmp.Datas {
		data.prepare()
		if m.xmlData.ForecastInfoM.Index(data.ForecastId) == nil {
			panic(fmt.Sprintf("forecast_task_info.xml err. forecastID not exist. id: %d, forecastID: %d",
				data.Id, data.ForecastId))
		}
		if data.Value == 0 {
			panic(fmt.Sprintf("forecast_task_info.xml err. value is zero. id: %d", data.Id))
		}

		// 任务的逻辑type
		taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskTypeInfo == nil {
			panic(fmt.Sprintf("forecast_task_info.xml taskType config err. taskType: %d. ID: %d", data.TypeId, data.Id))
		}

		if taskTypeInfo.Parameter1 == 0 {
			if forecastID, exist := checkTypeMap[taskTypeInfo.Type]; exist {
				if forecastID != data.ForecastId {
					panic(fmt.Sprintf("forecast_task_info.xml err. task has different forecastID. id: %d", data.Id))
				}
			} else {
				checkTypeMap[taskTypeInfo.Type] = data.ForecastId
			}
		} else {
			if _, exist := checkTypeMap[taskTypeInfo.Type]; exist {
				panic(fmt.Sprintf("forecast_task_info.xml err. task has different forecastID. id: %d", data.Id))
			}
		}

		if _, exist := m.TaskTypeCache[taskTypeInfo.Type]; !exist {
			m.TaskTypeCache[taskTypeInfo.Type] = make(map[uint32]*ForecastTask)
		}

		if m.TaskTypeCache[taskTypeInfo.Type][taskTypeInfo.Parameter1] == nil {
			for _, task := range m.xmlData.TaskTypeInfoM.IndexEvent(taskTypeInfo.Type) {
				if taskTypeInfo.Parameter1 != 0 && task.Parameter1 == 0 { // 有的任务是任意类型，参数为0，需要加上，否则任务进度不更新
					m.TaskTypeCache[taskTypeInfo.Type][0] = &ForecastTask{
						TaskID:     taskTypeInfo.Id,
						Value:      uint64(data.Value),
						FinishType: taskTypeInfo.FinishType,
					}
				}
			}

			m.TaskTypeCache[taskTypeInfo.Type][taskTypeInfo.Parameter1] = &ForecastTask{
				TaskID:     taskTypeInfo.Id,
				Value:      uint64(data.Value),
				FinishType: taskTypeInfo.FinishType,
			}
		} else {
			if m.TaskTypeCache[taskTypeInfo.Type][taskTypeInfo.Parameter1].Value < uint64(data.Value) {
				m.TaskTypeCache[taskTypeInfo.Type][taskTypeInfo.Parameter1].Value = uint64(data.Value)
			}
		}

		m.events[taskTypeInfo.Type] = struct{}{}
		m.taskInfos[data.ForecastId] = append(m.taskInfos[data.ForecastId], data)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ForecastTaskInfoManager) GetRecordById(id uint32) *ForecastTaskInfo {
	return m.Datas[id]
}

func (m *ForecastTaskInfoManager) Index(key uint32) *ForecastTaskInfo {
	return m.Datas[key]
}

// ForecastEvents : 新功能预告需要监听的所有任务type
func (m *ForecastTaskInfoManager) ForecastEvents() map[uint32]struct{} {
	return m.events
}

func (m *ForecastTaskInfoManager) GetTaskInfos(forecastID uint32) []*ForecastTaskInfo {
	return m.taskInfos[forecastID]
}
