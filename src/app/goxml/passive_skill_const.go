package goxml

const (
	TriggerTarget_None         = iota //全体
	TriggerTarget_Self                // 自己
	TriggerTarget_TeamAll             // 队友（包括自己）
	TriggerTarget_TeamOther           // 队友（不包括自己）
	TriggerTarget_OpTeamAll           // 敌方
	TriggerTarget_TeamAll2            // 队友（包括自己、神器）
	TriggerTarget_TeamUniter          // 我方全局对象
	TriggerTarget_OpTeamUniter        // 敌方全局对象
	TriggerTarget_TeamAllOther        // 全体（不包含自己,神器和全局对象）
)

const (
	TriggerCondition_None                     = iota
	TriggerCondition_AttackHurtType           // 伤害类型 1物理 2魔法
	TriggerCondition_SkillType                // 技能类型
	TriggerCondition_SkillAttackType          // 技能攻击类型
	TriggerCondition_SkillEffect              // 技能效果
	TriggerCondition_SkillAttackFactor        // 命中
	TriggerCondition_SkillAttackAddBuffFactor // 加Buff成功
	TriggerCondition_BuffType                 // Buff类型
	TriggerCondition_BuffBenefit              // 技能类型
	TriggerCondition_BuffIsControl            //是否是控制buff
	TriggerCondition_HurtType                 // 伤害类型
	TriggerCondition_BuffEffectType           // BuffEffect类型
	TriggerCondition_MemberRelation           //被动拥有者与触发成员关系
	TriggerCondition_Source                   //判断效果来源类型
	TriggerCondition_SkillType2               // 技能类型2
	TriggerCondition_SeasonLinkEnergyValue    // 达到的具体赛季羁绊能量值
	TriggerCondition_PurifyOrDispel           // 净化或驱散
	TriggerCondition_BuffExist                // buff是否存在（未重置）
	TriggerCondition_BuffID                   // Buff id
	TriggerCondition_AttackIsElite            // 攻击者是否精英怪
	TriggerCondition_SkillIsFollow            // 技能是否是追击技能
	TriggerCondition_Max                      //最大值
)

// 指定范围的被动
const (
	TriggerRange_AllOther         = -6 + iota // 全体(不包括被动的拥有者)
	TriggerRange_All                          // 全体
	TriggerRange_AttackTeam                   // 攻击方的队伍
	TriggerRange_AttackTeamOther              // 攻击方的队伍（不包括被动的拥有者）
	TriggerRange_DefenseTeam                  // 防守方的队伍
	TriggerRange_DefenseTeamOther             // 防守方的队伍（不包括被动的拥有者）
	TriggerRange_None                         // 无效
)

const (
	Event_None                           = iota // 无效
	Event_BattleInit                            // 战斗初始化
	Event_BattlePrepare                         // 战斗准备阶段
	Event_OneRoundPrepare                       // 回合开始前
	Event_OneRoundPrepareEnd                    // 回合开始后
	Event_ChooseSkill                           // 选择技能
	Event_ActionPrepare                         // 行动前
	Event_ActionEnd                             // 行动后
	Event_OneSkillPrepare                       // 技能释放前
	Event_OneSkillEnd                           // 技能释放后
	Event_OneEffectPrepare                      // 攻击阶段前
	Event_OneEffectEnd                          // 攻击阶段后
	Event_HurtPrepare                           // 伤害前
	Event_HurtEnd                               // 伤害后
	Event_BeHurtedPrepare                       // 受到伤害前
	Event_BeHurtedEnd                           // 受到伤害后
	Event_BuffBeAdd                             // 获得buff后
	Event_BuffBeRemove                          // buff消失后
	Event_DecHp                                 // 扣血
	Event_DeadHurt                              // 受到致死伤害
	Event_Dead                                  // 有人死亡了
	Event_Revive                                // 复活
	Event_BeHurtedBeforeCalDam                  // 被攻击时,计算伤害前
	Event_DoHurt                                //造成伤害时
	Event_BuffToAdd                             //加buff后
	Event_BattleBeforePrepare                   //战斗准备阶段之前
	Event_BattleBeforeInit                      //战斗初始化之前
	Event_BeCured                               // 受到治疗时
	Event_Dodge                                 //闪避
	Event_Combo                                 //连击前
	Event_Union                                 //联动后
	Event_BloodSuck                             //吸血后
	Event_BuffSettle                            //buff结算后
	Event_OverCure                              //溢出治疗
	Event_Bless                                 //庇佑触发
	Event_OneRoundPreEnd                        //回合结束前
	Event_SeasonLinkEnergyReach                 //赛季羁绊能量达到时
	Event_PurifyOrDispelEnd                     //净化或驱散后
	Event_Devour                                //成功吞噬时（杀死召唤物）
	Event_Call                                  //成功召唤时
	Event_SwapPos                               //成功换位时
	Event_StealHero                             //成功偷人时
	Event_EatHero                               //成功吃人时
	Event_BeforeStealHero                       //偷人前（加隐藏buff前）
	Event_BuffReflected                         //buff被反弹时
	Event_SeasonLinkNegEnergyReach              //赛季羁绊能量达到时(负值时)
	Event_BeforeBeDuel                          //受到决斗前
	Event_BuffMianyi                            //触发buff免疫时
	Event_BeforeToDuel                          //发起决斗前
	Event_ActiveHurtedBeforeHurtValueFix        //受到主动技能伤害，伤害修正前
	Event_HpChange                              //血量变化时
	Event_Max                                   //最大的情况
)

// 被动技能触发的条件
// 格式Cond+时机(可省略，省略了就是当前状态)+attack or defense +内容
// attack和defense代表的是一次攻击的攻击方和防守方，如果是治疗的话就有可能相同 op代表的一定是attack的对手
const (
	CondNone                              = iota //无条件
	CondOwnerHpLower                             //被动技能拥有者血量低于%x多少,触发时机放在每次扣血之后，并且是行动后触发的效果
	CondDefenseHpLess                            //防守方当前血量低于x
	CondDefenseHpMore                            //防守方当前血量高于x
	CondDefenseJob                               //防守方职业为x
	CondAttackJob                                //攻击方职业为x
	CondDefenseBuffState                         //防守方当前有x buff状态
	CondDefenseNoBuffState                       //防守方当前没有x buff
	CondAttackBuffState                          //攻击方当前有x buff状态
	CondOwnerBuffState                           //天赋拥有者有x buff状态
	CondAttackNoBuffState                        //攻击方当前没有x buff
	CondAttackIsControled                        //攻击方被控制
	CondDefenseIsControled                       //防守方被控制
	CondDefenseBuffStateOrControled              //防守方有x buff或者被控制
	CondAttackBuffStateOrDefenseControled        //攻击方有x buff或者防守方被控制
	CondAttackHasKill                            //有击杀目标
	CondAttackHasCrit                            //每次伤害的时候有暴击
	CondAttackHasOverCure                        //每次治疗的时候有溢出治疗
	CondAttackHasNoCritAndOnlyOne                //每次伤害的时候，攻击者没有暴击且只有一个活人
	CondAttackHasNoCrit                          //每次伤害的时候没有暴击
	CondAttackTargetNumLess                      //攻击方当前选择的目标数量小于X
	CondAttackHpMore                             //攻击方血量高于x
	CondAttackHpLess                             //攻击方血量低于x
	CondDefenseAttrLessOwner                     //防守方属性x低于天赋拥有者
	CondDefenseAttrMoreOwner                     //防守方属性x高于天赋拥有者
	CondControlNumMore                           //某方被控制数量大于x
	CondOwnerCanResurr                           //被动技能拥有者能复活？
	CondAttackHasAddControlBuff                  //技能释放后，添加了控制类buff
	CondRoundLess                                //当前回合数小于x
	CondRoundGE                                  // //当前回合数大于等于x
	CondSkillGroupEqual                          //目标技能的技能组id=x
	CondDefenseInOwnerTarget                     //防守方在onwer的目标范围x内
	CondDefenseCorruptionCountMore               //防守方感染腐蚀的数量大于x
	CondTriggerBuffIdCountMoreThan1              //触发者指定的xxbuff数量大于1
	CondAttackHurtOrCureLessDefenseHp            //攻击者造成的伤害或者治疗低于防守者的当前生命值的百分比（万分比）
	CondAttackHurtOrCureMoreDefenseHp            //攻击者造成的伤害或者治疗高于防守者的当前生命值的百分比（万分比）
	CondAttackInOwnerTarget                      //攻击方在onwer的目标范围x内
	CondAttackBenefitBuffCount                   //攻击方增益buff数量
	CondAttackNoBenefitBuffCount                 //攻击方减益buff数量
	CondDefenseBenefitBuffCount                  //防守方增益buff数量
	CondDefenseNoBenefitBuffCount                //防守方减益buff数量
	CondDefenseDefCompare                        //防守方物防和魔防的比较
	CondAttackRace                               //攻防阵营
	CondOwnerIsBenefitBuffCount                  //拥有者指定类型buff数量
	CondOwnerHpHigher                            //被动技能拥有者血量高于%x
	CondDefenseRace                              //防守方种族为x
	CondOneSkillAttackHasKill                    //整个技能中有击杀过目标
	CondAttackTeamBenefitBuffCount               //攻击方队伍的增益buff数量
	CondResurrection                             //是否复活过
	CondAttackTeamLinkNum                        //攻击方队伍的羁绊数量
	CondDefenseTeamLinkNum                       //攻击方队伍的羁绊数量
	CondTeamAttrMoreEqual                        //队伍某属性>=
	CondTeamResurrectionMoreEqual                //队伍的复活次数>=
	CondTeamDeathNumMoreEqual                    //队伍死亡次数>=
	CondBuffLayerCountMoreEqual                  //判断buff的层数数量>=
	CondBuffTypeCountMoreEqual                   //判断buff的类型数量>=
	CondOwnerTeamLinkNum                         //拥有者队伍的羁绊数量
	CondSameActionAddedBuffCount                 //队伍同一行动下加的指定buff的人数>=
	CondHasPsAttr                                //指定特殊属性不为0
	CondOwnerDeathState                          //拥有者死亡状态
	CondOwnerNoBuffState                         //拥有者没有指定buff状态
	CondLink                                     //判断对方是否拥有羁绊
	CondSeasonEnergy                             //判断赛季能量是否满足条件
	CondIsTargetExist                            //目标是否存在
	CondCurRoundBigSkillCount                    //当前回合第x个释放大招
	CondSeasonEnergyExtra                        //判断赛季能量是否满足条件(62)的补充条件，不能单独使用
	CondHeroStar                                 //判断英雄星级
	CondAttr                                     //判断属性值
	CondLinkTypeCount                            //判断某类型羁绊激活的数量
	CondHeroSex                                  //判断英雄性别
	CondHavePsID                                 //判断是否拥有某被动id
	CondOwnerPos                                 //判断被动拥有者站位
	CondLeftMemAndNoMonsterCallHero              //敌方剩余人数(排除召唤物)大于1且我方没有英雄类召唤物
	CondHaveEmptyPosOrMonsterCall                //有空位或者有普通召唤物
	CondAliveNum                                 //队伍存活单位数量
	CondNoBuffID                                 //某方没有指定buffID
	CondHeroID                                   //判断某方有无指定英雄
	CondBuffLayerCountLess                       //指定buff总层数小于某个值
	CondBuffLayerRecord                          //判断施加buff_type的累积数量，达到N层后触发
	CondBuffActiveSkillHurtEffective             //判断主动技能造成伤害有效（大于指定值）
	CondOwnerOpTeamLinkNum                       //拥有者对方队伍的羁绊数量
	CondAccumulateBeHurt                         //判断累计受到X点伤害
	CondCurrentRoundIsOddOrEven                  //判断当前回合是单数还是双数
	CondArtifactStar                             //判断神器星级
	CondMax
)

const (
	CondTargetOpTeam   = 1
	CondTargetSelfTeam = 2
	CondTargetAttack   = 3
	CondTargetDefense  = 4
)

const (
	AddRateCondNone             = iota //无条件
	AddRateCondOwnerTeamLinkNum        //攻击方队伍的羁绊数量
	AddRateCondMax
)

const (
	EffectHurt                      uint32 = iota //造成伤害
	EffectCure                                    //治疗
	EffectAddBuff                                 //添加buff
	EffectRemoveBuff                              //清除buff
	EffectNewSkill                                //在释放一个技能
	EffectComboSkill                              //连击
	EffectComboNormalSkill                        //暴追
	EffectBindSkill                               //绑定原有技能
	EffectReplaceSkill                            //替换原有技能
	EffectChangeAttr                              //修改临时属性，持续oneAttack
	EffectChangeHaloAttr                          //存活的光环属性
	EffectChangePsAttr                            //修改临时被动效果，持续oneAttack
	EffectChangeForevePsAttr                      //修改永久的特殊属性
	EffectChangeForeveAttr                        //修改永久属性
	EffectNextPsAttr                              //修改临时效果，下次技能生效
	EffectResurrection                            //复活
	EffectHpLowerAttr                             //血量低于一定百分比修改自己xx属性
	EffectHpHigherAttr                            //血量高于一定百分比修改自己xx属性
	EffectGuard                                   //保护
	EffectNextRoundSkill                          //指定下一回合的技能
	EffectDecArtifactEnergy                       // 神器能量减少
	EffectAddArtifactEnergy                       // 神器能量增加
	EffectDoKill                                  //斩杀
	EffectHasBuff                                 //拥有指定buff修改属性
	EffectAliveNum                                //根据己方存活人数修改属性
	EffectBuffType                                //影响指定的buff_type
	EffectBuffLayer                               //影响指定buff的层数
	EffectAddPS                                   //增加被动技能
	EffectComboNormalUnion                        //普攻联动
	EffectFightBack                               //回击
	EffectAddShield                               //加护盾并修改护盾值
	EffectCopyBuff                                //复制传递指定的buff
	EffectChangePS                                //替换指定被动技能
	EffectChangeArtifactCD                        //修改神器cd
	EffectStealShield                             //偷取护盾
	EffectHurtSelf                                //自残
	EffectSettleBuff                              //结算buff
	EffectFullHurt                                //真伤
	EffectCall                                    //召唤
	EffectChangeHaloPsAttr                        //存活的光环特殊属性
	EffectChangeForeveAttrWithLimit               //修改永久属性(有限制的增益)
	EffectBless                                   //施加庇佑
	EffectExplosiveShield                         //爆盾
	EffectSplashHurt                              //溅射伤害
	EffectStealSoul                               //拘魂效果
	EffectDecSeasonLinkEnergy                     //赛季羁绊能量(信仰点)减少
	EffectAddSeasonLinkEnergy                     //赛季羁绊能量(信仰点)增加

	EffectChangeAttrWithLimit //修改oneSkill临时属性(有限制的增益)//目前47的作用等同与40，故暂弃用。v2.8.0-dev中有加属性逻辑优化后的代码

	EffectDevour                     //吞噬
	EffectAddBuffByEffTarget         //根据触发目标buff数据，添加对应层数的新buff
	EffectChangeForeveAttrWithLimit2 //修改永久属性(有限制的增益) - 与40作用相同，但参数用法不同
	EffectAddShieldByInherit         //继承单次护盾值并施加护盾
	EffectSwapPos                    //交换位置
	EffectCopyBuffWhenSwapPos        //交换位置时复制buff，与EffectCopyBuff相似
	EffectAddSkillAttackCount        //增加技能攻击次数
	EffectAddBuffByBuffType          //根据buff类型, 添加相同层数的buff
	EffectComboBySkill               //根据参数选择技能联动
	EffectCleanBuffAddAttr           //清除debuff转属性
	EffectHurt2Cure                  //伤害转治疗
	EffectEat                        //吃人
	EffectKillNotDead                //非死亡类击杀
	EffectTransferBuff               //净化转移buff
	EffectAddBuffByBuffLayer         //根据当前添加buff层数，添加指定buff
	EffectChangeSkillAttackDefenser  //修改技能攻击防守方
	EffectCallDepartedSpirit         //召唤亡灵（继承被动技能，属性累加继承不覆盖）
	EffectDuel                       //指定两个目标决斗
	EffectHurtSelfDamageToTrigger    //特殊自残，伤害计算到被动触发者身上
	EffectReverseTargetsActOrder     //将目标的出手顺序倒序
	EffectResetSkillCD               //重置技能cd
	EffectEntityChangeForeveAttr     //被动属性
	EffectMax
)

const (
	EffectTimeAtOnce uint32 = 1 //立即触发的效果
	EffectTimeLater  uint32 = 2 //行动后触发的效果

)

const (
	Limit   uint32 = 1 //一次技能限制1次
	NoLimit uint32 = 0 //无限制
)

const (
	Op_Success = 0
	Op_Fail    = 1
)

// 事件优先级
const (
	EveNone                = iota
	EveDrainHp             = 1  //吸血
	EveRebounds            = 2  //反弹
	EveOneAttack           = 3  //每次攻击时
	EveOneAttackDef        = 4  //被攻击时触发
	EveOneEffect           = 5  //每段效果后
	EveOneEffectDef        = 6  //被每段效果后
	EveSkillEnd            = 7  //攻击后, 追加效果
	EveSkillEndCombo       = 8  //释放技能后，连接 ，在追加后面
	EveSkillEndComboNormal = 9  //释放技能后，暴追 ，在追加后面
	EveHpLess              = 10 //血量低于xx%回血
	EveDead                = 11 //死亡后的效果

	EveMax = 12
)

// 召唤类效果
var EffectCallList = []uint32{EffectCall, EffectStealSoul, EffectCallDepartedSpirit}
