package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

type ArtifactDebutDrawDropInfoManager struct {
	xmlData *XmlData
	datas   map[uint32]*ArtifactDebutDrawDropInfoExt
	groups  map[uint32][]*ArtifactDebutDrawDropInfoExt
	weights map[uint32]int
}

type ArtifactDebutDrawDropInfoExt struct {
	ID        uint32
	DropGroup uint32
	Kind      uint32
	Count     uint32 //当Kind非0时，Award无值，count>0
	Weight    int
	Award     []*cl.Resource
}

func newArtifactDebutDrawDropInfoManager(xmlData *XmlData) *ArtifactDebutDrawDropInfoManager {
	m := &ArtifactDebutDrawDropInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ArtifactDebutDrawDropInfoManager) name() string {
	return "ArtifactDebutDrawDropInfo"
}

func (m *ArtifactDebutDrawDropInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ArtifactDebutDrawDropInfoManager) checkData() error {
	return nil
}

func (m *ArtifactDebutDrawDropInfoManager) load(dir string, isShow bool) error {
	tmp := &ArtifactDebutDrawDropInfos{}
	fileName := filepath.Join(dir, "artifact_debut_draw_drop_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	if m.datas == nil {
		m.datas = make(map[uint32]*ArtifactDebutDrawDropInfoExt)
	}
	m.groups = make(map[uint32][]*ArtifactDebutDrawDropInfoExt)
	m.weights = make(map[uint32]int)

	for _, data := range tmp.Datas {
		data.prepare()
		//掉落类型验证
		if !ArtifactDebutDrawLegalKind[data.Kind] {
			panic(fmt.Sprintf("load config %s fail. kind illegal, id:%d", fileName, data.Id))
		}

		if data.Count <= 0 {
			panic(fmt.Sprintf("load config %s fail. count illegal, id:%d", fileName, data.Id))
		}

		if data.Kind == ArtifactDebutDrawKindCommon && len(data.ClRes) == 0 {
			panic(fmt.Sprintf("load config %s fail. no award, id:%d", fileName, data.Id))
		}

		//初级抽，kind值只能为0（初级抽被废弃，取消检查）
		//当前本表的掉落组，仅分高级抽（最小id:1000）和低级抽（最小id:2000）两大类
		//id调整时，此处判断也需一同调整
		//if data.Id >= ArtifactDebutJuniorDrawMinGropID && data.Kind != ArtifactDebutDrawKindCommon {
		//	panic(fmt.Sprintf("load config %s fail. junior summon kind should be 0, id:%d",
		//		fileName, data.Id))
		//}

		//验证神器配置的正确性
		if data.Type == uint32(common.RESOURCE_ARTIFACT) {
			if m.xmlData.ArtifactInfoM.Index(data.Value) == nil {
				panic(fmt.Sprintf("load config %s fail. artifact not exist, id:%d",
					fileName, data.Id))
			}
		}

		//验证神器碎片配置的正确性
		if data.Type == uint32(common.RESOURCE_ARTIFACT_FRAGMENT) {
			if m.xmlData.ArtifactFragmentInfoM.Index(data.Value) == nil {
				panic(fmt.Sprintf("load config %s fail. artifactFragment not exist, id:%d",
					fileName, data.Id))
			}
		}

		dataExt := &ArtifactDebutDrawDropInfoExt{
			ID:        data.Id,
			DropGroup: data.DropGroup,
			Kind:      data.Kind,
			Count:     data.Count,
			Weight:    int(data.Weight),
			Award:     data.ClRes,
		}

		if ptr, exist := m.datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.datas[data.Id] = dataExt
		}
		m.groups[dataExt.DropGroup] = append(m.groups[dataExt.DropGroup], dataExt)
		m.weights[dataExt.DropGroup] += dataExt.Weight
	}
	return nil
}

func (m *ArtifactDebutDrawDropInfoManager) GetRecordById(id uint32) *ArtifactDebutDrawDropInfoExt {
	return m.datas[id]
}

func (m *ArtifactDebutDrawDropInfoManager) Index(key uint32) *ArtifactDebutDrawDropInfoExt {
	return m.datas[key]
}

func (m *ArtifactDebutDrawDropInfoManager) Groups(dropGroup uint32) []*ArtifactDebutDrawDropInfoExt {
	return m.groups[dropGroup]
}

func (m *ArtifactDebutDrawDropInfoManager) Weight(dropGroup uint32) int {
	return m.weights[dropGroup]
}

func (m *ArtifactDebutDrawDropInfoManager) Random(rd *rand.Rand, dropGroup uint32) *ArtifactDebutDrawDropInfoExt {
	datas := m.Groups(dropGroup)
	if len(datas) == 0 {
		l4g.Errorf("ArtifactDebutDrawDropInfoM. no data, dropGroup:%d", dropGroup)
		return nil
	}

	totalWeight := m.Weight(dropGroup)
	if totalWeight == 0 {
		l4g.Errorf("ArtifactDebutDrawDropInfoM. no totalWeight, dropGroup:%d", dropGroup)
		return nil
	}

	rNum := rd.RandBetween(1, totalWeight)
	for _, v := range datas {
		if rNum <= v.Weight {
			return v
		} else {
			rNum -= v.Weight
		}
	}

	l4g.Errorf("ArtifactDebutDrawDropInfoM. Random err, dropGroup:%d, totalWeight:%d", dropGroup, totalWeight)
	return nil
}
