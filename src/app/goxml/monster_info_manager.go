package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

func (r *MonsterInfo) Check() error {
	return nil
}

type MonsterInfoEx struct {
	Id          uint32   `xml:"id,attr"`            //
	HeroId      uint32   `xml:"hero_id,attr"`       //int:怪物资源ID
	Type        uint32   `xml:"type,attr"`          //int:怪物类型
	IsElite     uint32   `xml:"is_elite,attr"`      //int:是否为精英
	Level       uint32   `xml:"level,attr"`         //int:怪物等级
	Star        uint32   `xml:"star,attr"`          //int:怪物星级
	Stage       uint32   `xml:"stage,attr"`         //int:怪物阶级
	NeedNotKill uint32   `xml:"need_not_kill,attr"` //int:是否无需击杀
	PublicHp    uint32   `xml:"public_hp,attr"`     //int:共用血量
	EtherBind   uint32   `xml:"ether_bind,attr"`    //int:虚拟单位绑定
	SkillLevel  uint32   `xml:"skill_level,attr"`   //int:本体技能等级
	FuncType    uint32   `xml:"func_type,attr"`     //int:玩法功能
	DataType    uint32   `xml:"data_type,attr"`     //int:数值模板
	DataLevel   uint32   `xml:"data_level,attr"`    //int:模板等级
	AttackPct   uint32   `xml:"attack_pct,attr"`    //int:攻击系数
	HpPct       uint32   `xml:"hp_pct,attr"`        //int:血量系数
	DefPct      uint32   `xml:"def_pct,attr"`       //int:双防系数
	SpeedPct    uint32   `xml:"speed_pct,attr"`     //int:速度系数
	Skills      []uint32 `xml:"skills,attr"`        //int:额外技能ID1

	attr     []int64           //属性
	LinkInfo map[uint32]uint32 //联结点数
}

func (e *MonsterInfoEx) GetAttr(xmlData *XmlData) []int64 {
	if len(e.attr) > 0 {
		return e.attr
	}
	attrs := xmlData.MonsterDataInfoM.GetAttrMap(e.DataType, e.DataLevel)
	if len(attrs) == 0 {
		return nil
	}
	e.attr = make([]int64, len(attrs))
	for k, value := range attrs {
		if k == AttrAttack && e.AttackPct > 0 {
			value = int64(float64(value) / BaseFloat * float64(e.AttackPct))
		}
		if e.HpPct > 0 && k == AttrHp {
			value = int64(float64(value) / BaseFloat * float64(e.HpPct))
		}
		if e.DefPct > 0 && (k == AttrPhyDef || k == AttrMagDef) {
			value = int64(float64(value) / BaseFloat * float64(e.DefPct))
		}
		if e.SpeedPct > 0 && k == AttrSpeed {
			value = int64(float64(value) / BaseFloat * float64(e.SpeedPct))
		}
		e.attr[k] = value
	}
	return e.attr
}

func (e *MonsterInfoEx) AddSkill(skillId uint32) {
	if skillId > 0 {
		e.Skills = append(e.Skills, skillId)
	}
}

type MonsterInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*MonsterInfoEx
}

func newMonsterInfoManager(xmlData *XmlData) *MonsterInfoManager {
	m := &MonsterInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MonsterInfoManager) name() string {
	return "MonsterInfo"
}

func (m *MonsterInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MonsterInfoManager) checkData() error {
	return nil
}

func (m *MonsterInfoManager) load(dir string, isShow bool) error {
	tmp := &MonsterInfos{}
	fileName := filepath.Join(dir, "monster_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*MonsterInfoEx, len(tmp.Datas))

	for _, data := range tmp.Datas {
		data.prepare()
		m.Add(fileName, data)
	}
	return nil
}

func (m *MonsterInfoManager) GetRecordById(id uint32) *MonsterInfoEx {
	return m.Datas[id]
}

func (m *MonsterInfoManager) Index(key uint32) *MonsterInfoEx {
	return m.Datas[key]
}

func (m *MonsterInfoManager) Add(fileName string, data *MonsterInfo) {
	if _, exist := m.Datas[data.Id]; exist {
		panic(fmt.Sprintf("load config %s fail: monster repeated, id:%d", fileName, data.Id))
	}

	info := &MonsterInfoEx{
		Skills:   make([]uint32, 0, 8),
		LinkInfo: make(map[uint32]uint32),
	}

	info.Id = data.Id
	info.HeroId = data.HeroId
	info.Type = data.Type
	info.Level = data.Level
	info.Star = data.Star
	info.Stage = data.Stage
	info.NeedNotKill = data.NeedNotKill
	info.PublicHp = data.PublicHp
	info.EtherBind = data.EtherBind
	info.SkillLevel = data.SkillLevel
	info.FuncType = data.FuncType
	info.DataType = data.DataType
	info.DataLevel = data.DataLevel
	info.AttackPct = data.AttackPct
	info.HpPct = data.HpPct
	info.DefPct = data.DefPct
	info.SpeedPct = data.SpeedPct
	info.IsElite = data.IsElite
	info.AddSkill(data.PassiveSkill1)
	info.AddSkill(data.PassiveSkill2)
	info.AddSkill(data.PassiveSkill3)
	info.AddSkill(data.PassiveSkill4)
	info.AddSkill(data.PassiveSkill5)
	info.AddSkill(data.PassiveSkill6)
	info.AddSkill(data.PassiveSkill7)
	info.AddSkill(data.PassiveSkill8)
	info.AddSkill(data.PassiveSkill9)
	info.AddSkill(data.PassiveSkill10)
	info.AddSkill(data.PassiveSkill11)
	info.AddSkill(data.PassiveSkill12)
	info.AddSkill(data.PassiveSkill13)
	info.AddSkill(data.PassiveSkill14)
	info.AddSkill(data.PassiveSkill15)

	if info.HeroId > 0 {
		heroInfo := m.xmlData.HeroInfoM.Index(info.HeroId)
		if heroInfo == nil {
			panic(fmt.Sprintf("load config %s fail: no hero:%d, id:%d", fileName, info.HeroId, info.Id))
		}
		// 羁绊1
		if info.Star >= m.xmlData.ConfigInfoM.Link1UnlockHeroStar && heroInfo.Link1ID > 0 {
			info.LinkInfo[heroInfo.Link1ID] += 1
		}
		// 羁绊2
		if info.Star >= m.xmlData.ConfigInfoM.Link2UnlockHeroStar && heroInfo.Link2ID > 0 {
			info.LinkInfo[heroInfo.Link2ID] += 1
		}
		// 羁绊3
		if info.Star >= m.xmlData.ConfigInfoM.Link3UnlockHeroStar && heroInfo.Link3ID > 0 {
			info.LinkInfo[heroInfo.Link3ID] += 1
		}
		// 羁绊4
		if info.Star >= m.xmlData.ConfigInfoM.Link4UnlockHeroStar && heroInfo.Link4ID > 0 {
			info.LinkInfo[heroInfo.Link4ID] += 1
		}
	}

	m.Datas[data.Id] = info
}

func (e *MonsterInfoEx) GetPower(xmlData *XmlData) uint64 {
	info := xmlData.MonsterDataInfoM.GetInfo(e.DataType, e.DataLevel)
	if info == nil {
		return 0
	}
	return uint64(info.Power)
}
