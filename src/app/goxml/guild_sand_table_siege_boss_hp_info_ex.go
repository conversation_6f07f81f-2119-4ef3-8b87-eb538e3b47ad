package goxml

import (
	"fmt"
)

type GuildSandTableSiegeBossHpInfoMEx struct {
	*GuildSandTableSiegeBossHpInfoM
}

func newGuildSandTableSiegeBossHpInfoMEx(xmlData *XmlData) *GuildSandTableSiegeBossHpInfoMEx {
	m := &GuildSandTableSiegeBossHpInfoMEx{
		GuildSandTableSiegeBossHpInfoM: &GuildSandTableSiegeBossHpInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableSiegeBossHpInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableSiegeBossHpInfoMEx) load(dir string, isShow bool) error {
	err := m.GuildSandTableSiegeBossHpInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	return nil
}

func (m *GuildSandTableSiegeBossHpInfoMEx) checkData() error {
	err := m.GuildSandTableSiegeBossHpInfoM.checkData()
	if err != nil {
		return err
	}
	for k, r := range m.GuildSandTableSiegeBossHpInfoM.idRecordMap {
		if len(r.HpRange) != len(r.DamageRange) {
			return fmt.Errorf("len HpRange:%d len DamageRange:%d is unequal. id:%d", len(r.HpRange), len(r.DamageRange), k)
		}
	}
	return nil
}

/*
	func (m *GuildSandTableSiegeBossHpInfoMEx) GetHpRange(bossId uint32, value uint64) uint32 {
		hpInfo := m.GuildSandTableSiegeBossHpInfoM.GetRecordById(bossId)
		if hpInfo == nil {
			return 0
		}
		index := int32(-1)
		for i, damage := range hpInfo.DamageRange {
			if damage > value {
				break
			}
			index = int32(i)
		}
		if index < 0 {
			return 0
		}
		return hpInfo.HpRange[index]
	}
*/
func (m *GuildSandTableSiegeBossHpInfoMEx) CalcReduceHpByDamage(bossId uint32, value uint64) uint32 {
	minIndex := 0
	maxIndex := 0
	hpInfo := m.GuildSandTableSiegeBossHpInfoM.GetRecordById(bossId)
	if hpInfo == nil {
		return 0
	}
	for i, damage := range hpInfo.DamageRange {
		if value >= damage {
			minIndex = i
			maxIndex = i
			continue
		}
		maxIndex = i
		break
	}
	if maxIndex > minIndex { // 处于区间时，使用公式计算
		maxDamage := hpInfo.DamageRange[maxIndex]
		maxReduceHp := hpInfo.HpRange[maxIndex]

		minReduceHp := hpInfo.HpRange[minIndex]
		return uint32(float64(maxReduceHp-minReduceHp)*float64(value)/float64(maxDamage) + float64(minReduceHp))
	}
	if maxIndex == minIndex { // 已经到最大区间时，直接扣最大伤害对应的ReduceHp
		return hpInfo.HpRange[maxIndex]
	}
	return 0
}
