package goxml

import "gitlab.qdream.com/kit/sea/util"

type GuildSandTableDragonEvolveInfoMEx struct {
	*GuildSandTableDragonEvolveInfoM
	seasonDragons map[uint32][]uint32
}

func newGuildSandTableDragonEvolveInfoMEx(xmlData *XmlData) *GuildSandTableDragonEvolveInfoMEx {
	m := &GuildSandTableDragonEvolveInfoMEx{
		GuildSandTableDragonEvolveInfoM: &GuildSandTableDragonEvolveInfoM{
			xmlData: xmlData,
		},
		seasonDragons: make(map[uint32][]uint32),
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableDragonEvolveInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableDragonEvolveInfoMEx) load(dir string, isShow bool) error {
	err := m.GuildSandTableDragonEvolveInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	for _, data := range m.seasonIdRecordMap {
		m.seasonDragons[data.SeasonId] = append(m.seasonDragons[data.SeasonId], []uint32{data.DragonId, data.DragonId1, data.DragonId2, data.DragonId3, data.DragonId4}...)
	}
	return nil
}

func (m *GuildSandTableDragonEvolveInfoMEx) checkData() error {
	err := m.GuildSandTableDragonEvolveInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}

func (m *GuildSandTableDragonEvolveInfoMEx) GetSeasonByDragonId(dragonId uint32) uint32 {
	for seasonId, dragons := range m.seasonDragons {
		if util.InUint32s(dragons, dragonId) {
			return seasonId
		}
	}
	return 0
}
