package goxml

import (
	"app/protos/out/cl"
	"errors"
	"fmt"
)

type GuildSandTableChallengeTaskInfoMEx struct {
	*GuildSandTableChallengeTaskInfoM
	AllEvent map[uint32]struct{}
}

func newGuildSandTableChallengeTaskInfoMEx(xmlData *XmlData) *GuildSandTableChallengeTaskInfoMEx {
	m := &GuildSandTableChallengeTaskInfoMEx{
		GuildSandTableChallengeTaskInfoM: &GuildSandTableChallengeTaskInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableChallengeTaskInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableChallengeTaskInfoMEx) load(dir string, isShow bool) error {
	err := m.GuildSandTableChallengeTaskInfoM.load(dir, isShow)
	if err != nil {
		return err
	}

	if m.AllEvent == nil {
		m.AllEvent = make(map[uint32]struct{})
	}

	for _, v := range m.idRecordMap {
		taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(v.TypeId)
		if taskTypeInfo == nil {
			return errors.Join(fmt.Errorf("GuildSandTableChallengeTaskInfoMEx no taskTypeInfo. v.TypeId:%d", v.TypeId), err)
		}
		m.AllEvent[taskTypeInfo.Type] = struct{}{}

		// 奖励
		if v.Type1 > 0 && v.Count1 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type1,
				Value: v.Value1,
				Count: v.Count1,
			})
		}
		if v.Type2 > 0 && v.Count2 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type2,
				Value: v.Value2,
				Count: v.Count2,
			})
		}
		if v.Type3 > 0 && v.Count3 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type3,
				Value: v.Value3,
				Count: v.Count3,
			})
		}
	}
	return nil
}

func (m *GuildSandTableChallengeTaskInfoMEx) checkData() error {
	err := m.GuildSandTableChallengeTaskInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}

func (m *GuildSandTableChallengeTaskInfoMEx) GetAllEvent() map[uint32]struct{} {
	return m.AllEvent
}

func (m *GuildSandTableChallengeTaskInfoMEx) GetTaskNum() int {
	return len(m.idRecordMap)
}
