package goxml

type GuildSandTableDragonRewardInfoMEx struct {
	*GuildSandTableDragonRewardInfoM
}

func newGuildSandTableDragonRewardInfoMEx(xmlData *XmlData) *GuildSandTableDragonRewardInfoMEx {
	m := &GuildSandTableDragonRewardInfoMEx{
		GuildSandTableDragonRewardInfoM: &GuildSandTableDragonRewardInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableDragonRewardInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableDragonRewardInfoMEx) load(dir string, isShow bool) error {
	err := m.GuildSandTableDragonRewardInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	return nil
}

func (m *GuildSandTableDragonRewardInfoMEx) checkData() error {
	err := m.GuildSandTableDragonRewardInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}
