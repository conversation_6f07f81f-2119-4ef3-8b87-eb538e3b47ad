package goxml

import (
	"errors"
	"fmt"
)

type ActivityPyramidChooseInfoMEx struct {
	*ActivityPyramidChooseInfoM
}

func newActivityPyramidChooseInfoMEx(xmlData *XmlData) *ActivityPyramidChooseInfoMEx {
	m := &ActivityPyramidChooseInfoMEx{
		ActivityPyramidChooseInfoM: &ActivityPyramidChooseInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityPyramidChooseInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *ActivityPyramidChooseInfoMEx) load(dir string, isShow bool) error {
	err := m.ActivityPyramidChooseInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	return nil
}

func (m *ActivityPyramidChooseInfoMEx) checkData() error {
	err := m.ActivityPyramidChooseInfoM.checkData()
	if err != nil {
		return err
	}

	for _, groupData := range m.serverTypeIndexMap {
		for groupId, idData := range groupData.groupIndexMap {
			var hasInitChooseRound bool
			var totalRewardMax uint32
			for _, record := range idData.idRecords {
				if record.ChooseRound == ActivityPyramidInitialRound {
					hasInitChooseRound = true
				}
				totalRewardMax += record.RewardMax
			}
			// 表检查1: 同一奖励组中，必须至少包含一条 chooseRound = 1的奖励。否则一开始就没有自选奖励可以选择了
			if !hasInitChooseRound {
				return errors.Join(fmt.Errorf("ActivityPyramidChooseInfoMEx: no initChooseRound, groupId %d", groupId), err)
			}
			// 表检查2: 同一奖励组中，奖励可抽中次数之和需要大于轮数，否则后续轮就没有奖励可自选了
			for _, v := range m.xmlData.ActivityPyramidInfoM.idRecords {
				if totalRewardMax < v.CircleNum {
					return errors.Join(fmt.Errorf("ActivityPyramidChooseInfoMEx: totalRewardMax %d less than circumNum %d, groupId %d", totalRewardMax, v.CircleNum, groupId), err)
				}
			}
		}
	}

	return nil
}
