package goxml

import (
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type GuildSandTableBuildTaskInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]*GuildSandTableBuildTaskInfo
	SeasonEvent map[uint32]map[uint32][]*GuildSandTableBuildTaskInfo // seasonID taskType
	AllEvent    map[uint32]struct{}
}

func newGuildSandTableBuildTaskInfoManager(xmlData *XmlData) *GuildSandTableBuildTaskInfoManager {
	m := &GuildSandTableBuildTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableBuildTaskInfoManager) name() string {
	return "guild_sand_table_build_task_info.xml"
}

func (m *GuildSandTableBuildTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableBuildTaskInfoManager) checkData() error {
	return nil
}

func (m *GuildSandTableBuildTaskInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableBuildTaskInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_build_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*GuildSandTableBuildTaskInfo, len(tmp.Datas))
	m.SeasonEvent = make(map[uint32]map[uint32][]*GuildSandTableBuildTaskInfo)
	m.AllEvent = make(map[uint32]struct{})
	for _, data := range tmp.Datas {
		data.prepare()

		taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskTypeInfo == nil {
			panic(fmt.Sprintf("config %s data id:%d task type:%d is error", fileName, data.Id, data.TypeId))
		}
		m.AllEvent[taskTypeInfo.Type] = struct{}{}

		_, exist := m.SeasonEvent[data.SeasonId]
		if !exist {
			m.SeasonEvent[data.SeasonId] = make(map[uint32][]*GuildSandTableBuildTaskInfo)
		}
		_, exist = m.SeasonEvent[data.SeasonId][taskTypeInfo.Type]
		if !exist {
			m.SeasonEvent[data.SeasonId][taskTypeInfo.Type] = make([]*GuildSandTableBuildTaskInfo, 0, 10)
		}
		m.SeasonEvent[data.SeasonId][taskTypeInfo.Type] = append(m.SeasonEvent[data.SeasonId][taskTypeInfo.Type], data)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildSandTableBuildTaskInfoManager) Index(key uint32) *GuildSandTableBuildTaskInfo {
	return m.Datas[key]
}

func (m *GuildSandTableBuildTaskInfoManager) GetAllEvent() map[uint32]struct{} {
	return m.AllEvent
}

func (m *GuildSandTableBuildTaskInfoManager) GetSeasonEvent(seasonId uint32) map[uint32][]*GuildSandTableBuildTaskInfo {
	return m.SeasonEvent[seasonId]
}

func (m *GuildSandTableBuildTaskInfoManager) GetSeasonTaskLen(seasonId uint32) int {
	var totalLen int
	for _, v := range m.SeasonEvent[seasonId] {
		totalLen += len(v)
	}
	return totalLen
}
