package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ShopRegularGoodsInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]*ShopRegularGoodsInfoExt
	Groups   map[uint32][]*ShopRegularGoodsInfoExt
	MaxRound map[uint32]uint32
}

type ShopRegularGoodsInfoExt struct {
	Id            uint32
	ShopId        uint32
	Round         uint32
	MinLv         uint32
	MaxLv         uint32
	ExtType       uint32
	MinExt        uint32
	MaxExt        uint32
	BuyLimitType  uint32
	BuyLimitCount uint32
	CostType      uint32
	CostValue     uint32
	CostGroup     uint32
	Awards        []*cl.Resource
}

func newShopRegularGoodsInfoManager(xmlData *XmlData) *ShopRegularGoodsInfoManager {
	m := &ShopRegularGoodsInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ShopRegularGoodsInfoManager) name() string {
	return "ShopRegularGoodsInfo"
}

func (m *ShopRegularGoodsInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ShopRegularGoodsInfoManager) checkData() error {
	return nil
}

func (m *ShopRegularGoodsInfoManager) load(dir string, isShow bool) error {
	tmp := &ShopRegularGoodsInfos{}
	fileName := filepath.Join(dir, "shop_regular_goods_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*ShopRegularGoodsInfoExt, len(tmp.Datas))
	}
	m.Groups = make(map[uint32][]*ShopRegularGoodsInfoExt)
	m.MaxRound = make(map[uint32]uint32)
	roundCheck := make(map[uint32]map[uint32]struct{})
	uniq := make(map[uint32]*util.None)

	for _, data := range tmp.Datas {
		data.prepare()

		if len(data.AwardClRes) == 0 {
			panic(fmt.Sprintf("load config %s fail: no award. id:%d", fileName, data.Id))
		}

		if data.CostGroup == 0 {
			if ShopLimitType(data.BuyLimitType) != ShopLimitTypeForever || data.BuyLimitCount != 1 {
				panic(fmt.Sprintf("load config %s fail: no cost. id:%d", fileName, data.Id))
			}
		}

		//CostGroup不可重复
		if _, exist := uniq[data.CostGroup]; exist {
			panic(fmt.Sprintf("load config %s fail: CostGroup repeated. id:%d", fileName, data.Id))
		}
		uniq[data.CostGroup] = &util.None{}

		//CostGroup在buy_price_info中，必须存在
		if !m.xmlData.BuyPriceInfoM.IsExist(data.CostGroup) {
			panic(fmt.Sprintf("load config %s fail: buy group not exist. data:%v", fileName, data.Id))
		}

		if !ShopLegalBuyLimitType[ShopLimitType(data.BuyLimitType)] {
			panic(fmt.Sprintf("load config %s fail: illegal BuyLimitType. id:%d", fileName, data.Id))
		}

		//等级限制，若为0，则必须上下限都为0
		if data.MinLevel == 0 || data.MaxLevel == 0 {
			if data.MinLevel != data.MaxLevel {
				panic(fmt.Sprintf("load config %s fail: level limit err, not all 0. id:%d",
					fileName, data.Id))
			}
		}

		if data.MinLevel > data.MaxLevel {
			panic(fmt.Sprintf("load config %s fail: MinLevel > MaxLevel. id:%d",
				fileName, data.Id))
		}

		//关卡限制，若为0，则必须上下限都为0
		if data.ExtType == 1 {
			if data.MinExt == 0 || data.MaxExt == 0 {
				if data.MinExt != data.MaxExt {
					panic(fmt.Sprintf("load config %s fail: dungeon limit err, not all 0. id:%d",
						fileName, data.Id))
				}
			}
			if data.MinExt > data.MaxExt {
				panic(fmt.Sprintf("load config %s fail: MinDungeon > MaxDungeon. id:%d",
					fileName, data.Id))
			}
		}

		//等级限制与额外限制，必须有一组生效
		if data.MinLevel == 0 && data.MaxLevel == 0 && data.ExtType == 0 {
			panic(fmt.Sprintf("load config %s fail: level and ext limit all not exist. id:%d",
				fileName, data.Id))
		}

		if _, exist := shopGoodsLimit[data.ExtType]; !exist {
			panic(fmt.Sprintf("load config %s fail: data.ExtType not exist. id:%d",
				fileName, data.Id))
		}

		// 210：皮肤商城; 商品必须是永久皮肤
		if data.ShopId == 210 {
			if data.AwardType != uint32(common.RESOURCE_SKIN) {
				panic(fmt.Sprintf("load config %s fail: goods is invalid. not skin. id: %d", fileName, data.Id))
			}
		}

		dataExt := &ShopRegularGoodsInfoExt{}
		dataExt.Id = data.Id
		dataExt.ShopId = data.ShopId
		dataExt.Round = data.Round
		dataExt.MinLv = data.MinLevel
		dataExt.MaxLv = data.MaxLevel
		dataExt.ExtType = data.ExtType
		dataExt.MinExt = data.MinExt
		dataExt.MaxExt = data.MaxExt
		dataExt.BuyLimitType = data.BuyLimitType
		dataExt.BuyLimitCount = data.BuyLimitCount
		dataExt.CostType = data.CostType
		dataExt.CostValue = data.CostValue
		dataExt.CostGroup = data.CostGroup
		dataExt.Awards = data.AwardClRes

		if data.Round > 0 {
			if len(roundCheck[data.ShopId]) == 0 {
				roundCheck[data.ShopId] = make(map[uint32]struct{})
			}
			roundCheck[data.ShopId][data.Round] = struct{}{}

			if data.Round > m.MaxRound[data.ShopId] {
				m.MaxRound[data.ShopId] = data.Round
			}
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}

		if len(m.Groups[data.ShopId]) == 0 {
			m.Groups[data.ShopId] = make([]*ShopRegularGoodsInfoExt, 0, ShopGoodsInitCap)
		}
		m.Groups[data.ShopId] = append(m.Groups[data.ShopId], dataExt)
	}

	//验证轮次是否从1开始
	//验证轮次是否递增，无跳空
	for shopID, maxRound := range m.MaxRound {
		for i := uint32(1); i <= maxRound; i++ {
			if _, exist := roundCheck[shopID]; !exist {
				panic(fmt.Sprintf("load config %s fail: round shop data check failed, no shop info. shopID:%d",
					fileName, shopID))
			}

			if _, exist := roundCheck[shopID][i]; !exist {
				panic(fmt.Sprintf("load config %s fail: round shop data check failed, no round info. shopID:%d, round:%d",
					fileName, shopID, i))
			}
		}
	}
	return nil
}

func (m *ShopRegularGoodsInfoManager) GetRecordById(id uint32) *ShopRegularGoodsInfoExt {
	return m.Datas[id]
}

func (m *ShopRegularGoodsInfoManager) Index(key uint32) *ShopRegularGoodsInfoExt {
	return m.Datas[key]
}

func (m *ShopRegularGoodsInfoManager) Group(key uint32) []*ShopRegularGoodsInfoExt {
	return m.Groups[key]
}

func (m *ShopRegularGoodsInfoManager) GoodsInfoExist(goods_id, shop_id, level uint32) bool {
	info := m.Index(goods_id)
	if info != nil {
		if info.ShopId == shop_id && level >= info.MinLv && level <= info.MaxLv {
			return true
		}
	}
	return false
}

func (m *ShopRegularGoodsInfoManager) CalcRoundNum(shopID, num uint32) uint32 {
	maxRound := m.MaxRound[shopID]
	round := num % maxRound
	if round == 0 {
		round = maxRound
	}
	return round
}
