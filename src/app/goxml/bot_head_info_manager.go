package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

type BotHeadInfoManager struct {
	xmlData     *XmlData
	Datas       map[uint32]*BotHeadInfoExt
	TotalChance int
}

type BotHeadInfoExt struct {
	Id     uint32
	Chance int
}

func newBotHeadInfoManager(xmlData *XmlData) *BotHeadInfoManager {
	m := &BotHeadInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *BotHeadInfoManager) name() string {
	return "BotHeadInfo"
}

func (m *BotHeadInfoManager) ignoreForCheck() bool {
	return false
}

func (m *BotHeadInfoManager) checkData() error {
	return nil
}

func (m *BotHeadInfoManager) load(dir string, isShow bool) error {
	tmp := &BotHeadInfos{}
	fileName := filepath.Join(dir, "bot_head_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*BotHeadInfoExt, len(tmp.Datas))
	}
	m.TotalChance = 0
	for _, data := range tmp.Datas {
		if data.Chance == 0 {
			panic(fmt.Sprintf("load config %s fail: chance=0. id:%d", fileName, data.Id))
		}
		if m.xmlData.AvatarInfoM.Index(data.Id) == nil {
			panic(fmt.Sprintf("load config %s fail: avatar not found. id:%d", fileName, data.Id))
		}

		dataExt := &BotHeadInfoExt{}
		dataExt.Id = data.Id
		dataExt.Chance = int(data.Chance)
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
		m.TotalChance += dataExt.Chance
	}
	return nil
}

func (m *BotHeadInfoManager) GetRecordById(id uint32) *BotHeadInfoExt {
	return m.Datas[id]
}

func (m *BotHeadInfoManager) Index(key uint32) *BotHeadInfoExt {
	return m.Datas[key]
}

func (m *BotHeadInfoManager) GenerateAvatar(rd *rand.Rand) uint32 {
	rNum := rd.RandBetween(1, m.TotalChance)
	for _, v := range m.Datas {
		if rNum <= v.Chance {
			return v.Id
		}
		rNum -= v.Chance
	}

	l4g.Errorf("bot head generate failed. rNum:%d, totalWeight:%d", rNum, m.TotalChance)
	return 0
}
