package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildTalentConditionInfoExt struct {
	Id             uint32
	ConditionNodes []uint32
	RaisePSs       []uint64
}

type GuildTalentConditionInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildTalentConditionInfoExt
}

func newGuildTalentConditionInfoManager(xmlData *XmlData) *GuildTalentConditionInfoManager {
	m := &GuildTalentConditionInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildTalentConditionInfoManager) name() string {
	return "GuildTalentConditionInfo"
}

func (m *GuildTalentConditionInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildTalentConditionInfoManager) checkData() error {
	return nil
}

func (m *GuildTalentConditionInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildTalentConditionInfos{}
	fileName := filepath.Join(dir, "guild_talent_condition_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GuildTalentConditionInfoExt, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &GuildTalentConditionInfoExt{
			Id: data.Id,
		}
		if data.Condition1 > 0 {
			dataExt.ConditionNodes = append(dataExt.ConditionNodes, data.Condition1)
		}
		if data.Condition2 > 0 {
			dataExt.ConditionNodes = append(dataExt.ConditionNodes, data.Condition2)
		}
		if data.Condition3 > 0 {
			dataExt.ConditionNodes = append(dataExt.ConditionNodes, data.Condition3)
		}

		if data.RaisePassiveSkill1 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill1))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill2 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill2, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill2))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill3 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill3, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill3))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}

		if data.RaisePassiveSkill4 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill4, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill4))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill5 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill5, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill5))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill6 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill6, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. PassiveSkill %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill6))
			}
			dataExt.RaisePSs = append(dataExt.RaisePSs, raisePSInfo.ID)
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
	}
	return nil
}

func (m *GuildTalentConditionInfoManager) GetRecordById(id uint32) *GuildTalentConditionInfoExt {
	return m.Datas[id]
}

func (m *GuildTalentConditionInfoManager) Index(key uint32) *GuildTalentConditionInfoExt {
	return m.Datas[key]
}
