package goxml

import (
	"fmt"
	"path/filepath"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type AssistanceActivityOpenInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*AssistanceActivityOpenInfoExt
	slice   []*AssistanceActivityOpenInfoExt
}

type AssistanceActivityOpenInfoExt struct {
	Id         uint32
	FunctionId uint32
	OpenTime   int64
	EndTime    int64
}

func newAssistanceActivityOpenInfoManager(xmlData *XmlData) *AssistanceActivityOpenInfoManager {
	m := &AssistanceActivityOpenInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *AssistanceActivityOpenInfoManager) name() string {
	return "AssistanceActivityOpenInfo"
}

func (m *AssistanceActivityOpenInfoManager) ignoreForCheck() bool {
	return false
}

func (m *AssistanceActivityOpenInfoManager) checkData() error {
	return nil
}

func (m *AssistanceActivityOpenInfoManager) load(dir string, isShow bool) error {
	tmp := &AssistanceActivityOpenInfos{}
	fileName := filepath.Join(dir, "assistance_activity_open_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*AssistanceActivityOpenInfoExt, len(tmp.Datas))
	m.slice = make([]*AssistanceActivityOpenInfoExt, 0, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		//开启时间
		openTime, err := time.ParseInLocation(TIME_LAYOUT, data.OpenDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("assistance_activity_open_info data:%d openTime failed: %v err:%s", data.Id, data.OpenDay, err))
		}
		openUnix := openTime.Unix()

		endTime, err := time.ParseInLocation(TIME_LAYOUT, data.EndDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("assistance_activity_open_info data:%d endTime failed: %v err:%s", data.Id, data.EndDay, err))
		}
		endUnix := endTime.Unix()

		if openUnix > endUnix {
			panic(fmt.Sprintf("assistance_activity_open_info data:%d openTime:%+v more than endTime:%+v", data.Id, openTime, endTime))
		}

		ext := &AssistanceActivityOpenInfoExt{
			Id:         data.Id,
			FunctionId: data.FunctionId,
			OpenTime:   openUnix,
			EndTime:    endUnix,
		}
		m.slice = append(m.slice, ext)
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *ext
			ext = ptr
		} else {
			m.Datas[data.Id] = ext
		}
	}
	return nil
}

func (m *AssistanceActivityOpenInfoManager) GetRecordById(id uint32) *AssistanceActivityOpenInfoExt {
	return m.Datas[id]
}

func (m *AssistanceActivityOpenInfoManager) Index(key uint32) *AssistanceActivityOpenInfoExt {
	return m.Datas[key]
}

// 活动时间左开右闭
func (m *AssistanceActivityOpenInfoManager) GetCurrent(now int64) *AssistanceActivityOpenInfoExt {
	for _, v := range m.slice {
		if now > v.OpenTime && now <= v.EndTime {
			return v
		}
	}
	return nil
}

func (m *AssistanceActivityOpenInfoManager) IsOpen(actId uint32, now int64) bool {
	info, exist := m.Datas[actId]
	if !exist {
		return false
	}
	if now > info.OpenTime && now <= info.EndTime {
		return true
	}
	return false
}
