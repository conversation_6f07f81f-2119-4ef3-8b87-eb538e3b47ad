package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type DropActivityFunctionInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*DropActivityFunctionInfo
}

func newDropActivityFunctionInfoManager(xmlData *XmlData) *DropActivityFunctionInfoManager {
	m := &DropActivityFunctionInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DropActivityFunctionInfoManager) name() string {
	return "DropActivityFunctionInfo"
}

func (m *DropActivityFunctionInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DropActivityFunctionInfoManager) checkData() error {
	return nil
}

func (m *DropActivityFunctionInfoManager) load(dir string, isShow bool) error {
	tmp := &DropActivityFunctionInfos{}
	fileName := filepath.Join(dir, "drop_activity_function_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*DropActivityFunctionInfo, len(tmp.Datas))
	}

	for _, data := range tmp.Datas {
		data.prepare()

		//data.FunctionId为0时，是付费道具的相关配套配置，不做验证
		if data.FunctionId > 0 {
			if m.xmlData.FunctionInfoM.Index(data.FunctionId) == nil {
				panic(fmt.Sprintf("load config %s fail: no function. id:%d", fileName, data.Id))
			}
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			m.Datas[data.Id] = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *DropActivityFunctionInfoManager) GetRecordById(id uint32) *DropActivityFunctionInfo {
	return m.Datas[id]
}

func (m *DropActivityFunctionInfoManager) Index(id uint32) *DropActivityFunctionInfo {
	return m.Datas[id]
}
