package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type ArtifactForgeInfoExt struct {
	Rare         uint32
	Forge        uint32
	Attrs        []uint32
	CostClRes    []*cl.Resource
	TotalCostRes map[uint32]map[uint32]uint32 // 缓存强化累计消耗
}

type ArtifactForgeInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]map[uint32]*ArtifactForgeInfoExt
}

func newArtifactForgeInfoManager(xmlData *XmlData) *ArtifactForgeInfoManager {
	m := &ArtifactForgeInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ArtifactForgeInfoManager) name() string {
	return "ArtifactForgeInfo"
}

func (m *ArtifactForgeInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ArtifactForgeInfoManager) checkData() error {
	return nil
}

func (m *ArtifactForgeInfoManager) load(dir string, isShow bool) error {
	tmp := &ArtifactForgeInfos{}
	fileName := filepath.Join(dir, "artifact_forge_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]map[uint32]*ArtifactForgeInfoExt)
	}

	lastForge := make(map[uint32]uint32)
	for _, data := range tmp.Datas {
		data.prepare()

		if data.Forge != ArtifactInitialForge && lastForge[data.Rare]+1 != data.Forge { // 确保连续
			panic(fmt.Sprintf("load config %s fail: forge is discontinuity, rare:%d forge:%d",
				fileName, data.Rare, data.Forge))
		}
		lastForge[data.Rare] = data.Forge

		if data.Forge > ArtifactInitialForge &&
			(m.Datas[data.Rare][data.Forge-1] == nil || len(m.Datas[data.Rare][data.Forge-1].CostClRes) == 0) { //确保每个铸造等级都有消耗（除了最大级）
			panic(fmt.Sprintf("check data error %s cost is nil. rare:%d forge:%d", fileName, data.Rare, data.Forge))
		}

		dataExt := &ArtifactForgeInfoExt{}
		if data.AttrType1 != 0 && data.AttrValue1 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType1)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue1)
		}
		if data.AttrType2 != 0 && data.AttrValue2 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType2)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue2)
		}
		if data.AttrType3 != 0 && data.AttrValue3 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType3)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue3)
		}

		if data.Forge != ArtifactInitialForge && len(dataExt.Attrs) == 0 {
			panic(fmt.Sprintf("check data error %s attr is nil. rare:%d forge:%d", fileName, data.Rare, data.Forge))
		}

		dataExt.Rare = data.Rare
		dataExt.Forge = data.Forge
		dataExt.CostClRes = data.CostClRes
		dataExt.TotalCostRes = m.cacheCostRes(dataExt.Rare, dataExt.Forge)

		if m.Datas[data.Rare] == nil {
			tmpMap := make(map[uint32]*ArtifactForgeInfoExt)
			tmpMap[data.Forge] = dataExt
			m.Datas[data.Rare] = tmpMap
		} else {
			m.Datas[data.Rare][data.Forge] = dataExt
		}

		if v, exist := m.Datas[data.Rare]; exist {
			if ptr, exist := v[data.Forge]; exist {
				*ptr = *dataExt
				dataExt = ptr
			} else {
				v[data.Forge] = dataExt
			}
		} else {
			m.Datas[data.Rare] = make(map[uint32]*ArtifactForgeInfoExt)
			m.Datas[data.Rare][data.Forge] = dataExt
		}

	}
	return nil
}

func (m *ArtifactForgeInfoManager) Index(rare, forge uint32) *ArtifactForgeInfoExt {
	if m.Datas[rare] == nil {
		return nil
	}
	return m.Datas[rare][forge]
}

func (m *ArtifactForgeInfoManager) GetCosts(rare, lv, addLv uint32) []*cl.Resource {
	var awards []*cl.Resource
	for i := uint32(0); i < addLv; i++ {
		forgeInfo := m.Index(rare, lv+i)
		if forgeInfo != nil {
			awards = append(awards, forgeInfo.CostClRes...)
		}
	}
	return awards
}

func (m *ArtifactForgeInfoManager) cacheCostRes(race, curForgeLevel uint32) map[uint32]map[uint32]uint32 {
	if curForgeLevel == ArtifactInitialForge {
		return nil
	}

	resMap := make(map[uint32]map[uint32]uint32) // res: type -> value -> count
	for lv, fInfo := range m.Datas[race] {
		if lv <= curForgeLevel {
			for _, res := range fInfo.CostClRes {
				if resMap[res.Type] == nil {
					resMap[res.Type] = make(map[uint32]uint32)
				}
				resMap[res.Type][res.Value] += res.Count
			}
		}
	}

	return resMap
}

// GetReviveRes : 获取重生返还资源
func (m *ArtifactForgeInfoManager) GetReviveRes(race, curForgeLevel uint32) (resources []*cl.Resource) {
	if curForgeLevel == ArtifactInitialForge {
		return nil
	}
	forgeInfo := m.Index(race, curForgeLevel)
	if forgeInfo == nil {
		return nil
	}
	for resType, res := range forgeInfo.TotalCostRes {
		for value, count := range res {
			resources = append(resources, &cl.Resource{
				Type:  resType,
				Value: value,
				Count: count,
			})
		}
	}

	return
}

func (m *ArtifactForgeInfoManager) GetMaxLevel(rare uint32) uint32 {
	var maxLevel uint32
	for level := range m.Datas[rare] {
		if maxLevel < level {
			maxLevel = level
		}
	}
	return maxLevel
}
