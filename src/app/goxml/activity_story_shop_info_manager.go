package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityStoryShopInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]*ActivityStoryShopInfo
	AutoShop map[uint32]map[uint32]*ActivityStoryShopInfo // shopID ID
}

func newActivityStoryShopInfoManager(xmlData *XmlData) *ActivityStoryShopInfoManager {
	m := &ActivityStoryShopInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityStoryShopInfoManager) name() string {
	return "ActivityStoryShopInfo"
}

func (m *ActivityStoryShopInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityStoryShopInfoManager) checkData() error {
	return nil
}

func (m *ActivityStoryShopInfoManager) load(dir string, isShow bool) error {
	tmp := &ActivityStoryShopInfos{}
	fileName := filepath.Join(dir, "activity_story_shop_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*ActivityStoryShopInfo, len(tmp.Datas))
	m.AutoShop = make(map[uint32]map[uint32]*ActivityStoryShopInfo)
	autoResource := make(map[uint32]map[uint32]map[uint32]struct{})
	for _, data := range tmp.Datas {
		data.prepare()
		if len(data.CostClRes) == 0 {
			panic(fmt.Sprintf("filename:%s data:%d shop cost lens is zero", fileName, data.Id))
		}
		if data.AutoBuy > 0 {
			if len(data.CostClRes) != 1 {
				panic(fmt.Sprintf("filename:%s data:%d auto shop cost must be 1", fileName, data.Id))
			}
			_, exist := autoResource[data.ShopId]
			if !exist {
				autoResource[data.ShopId] = make(map[uint32]map[uint32]struct{})
			}
			_, exist = autoResource[data.ShopId][data.CostClRes[0].Type]
			if !exist {
				autoResource[data.ShopId][data.CostClRes[0].Type] = make(map[uint32]struct{})
			}
			_, exist = autoResource[data.ShopId][data.CostClRes[0].Type][data.CostClRes[0].Value]
			if exist {
				panic(fmt.Sprintf("filename:%s data:%d auto shop cost is repeated", fileName, data.Id))
			}
			_, exist = m.AutoShop[data.ShopId]
			if !exist {
				m.AutoShop[data.ShopId] = make(map[uint32]*ActivityStoryShopInfo)
			}
			m.AutoShop[data.ShopId][data.Id] = data
		}
		if !m.xmlData.BuyPriceInfoM.IsExist(data.CostGroup) {
			panic(fmt.Sprintf("acitivity_story_shop_info data id:%d buy price:%d not exist", data.Id, data.CostGroup))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ActivityStoryShopInfoManager) GetRecordById(id uint32) *ActivityStoryShopInfo {
	return m.Datas[id]
}

func (m *ActivityStoryShopInfoManager) Index(key uint32) *ActivityStoryShopInfo {
	return m.Datas[key]
}

func (a *ActivityStoryShopInfoManager) GetAutoBuy(shopId uint32) map[uint32]*ActivityStoryShopInfo {
	return a.AutoShop[shopId]
}

func (r *ActivityStoryShopInfo) GetBuyCost(xmlData *XmlData, currCount, increaseCount uint32) []*cl.Resource {
	var costs []*cl.Resource
	if len(r.CostClRes) > 1 {
		for _, v := range r.CostClRes {
			tmp := v.Clone()
			tmp.Count *= increaseCount
			costs = append(costs, tmp)
		}
	} else {
		success, price := xmlData.BuyPriceInfoM.GetPrice(r.CostGroup, currCount, increaseCount)
		if success {
			for _, v := range r.CostClRes {
				tmp := v.Clone()
				tmp.Count = price
				costs = append(costs, tmp)
			}
		}
	}
	return costs
}
