package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type SkillTargetInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*SkillTargetInfo
}

func newSkillTargetInfoManager(xmlData *XmlData) *SkillTargetInfoManager {
	m := &SkillTargetInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *SkillTargetInfoManager) name() string {
	return "SkillTargetInfo"
}

func (m *SkillTargetInfoManager) ignoreForCheck() bool {
	return false
}

func (m *SkillTargetInfoManager) checkData() error {
	return nil
}

func (m *SkillTargetInfoManager) load(dir string, isShow bool) error {
	tmp := &SkillTargetInfos{}
	fileName := filepath.Join(dir, "skill_target_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*SkillTargetInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		m.check(data)
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *SkillTargetInfoManager) check(skillTargetInfo *SkillTargetInfo) {
	if _, exist := gSkillTargetInfoInherit[skillTargetInfo.Inherit]; !exist {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: Inherit no found %d", skillTargetInfo.Inherit))
	}
	if _, exist := gSkillTargetInfoTargetType[skillTargetInfo.TargetType]; !exist {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: TargetType no found %d", skillTargetInfo.TargetType))
	}
	if _, exist := gSkillTargetInfoCondition[skillTargetInfo.Condition]; !exist {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: Condition no found %d", skillTargetInfo.Condition))
	}
	if skillTargetInfo.Condition == BaseChooseConditionBuffType {
		if m.xmlData.BuffTypeInfoM.Index(skillTargetInfo.CondParam) == nil {
			panic(fmt.Sprintf("load config SkillTargetInfoManager fail: Condition:%d,CondParam:%d",
				skillTargetInfo.Condition, skillTargetInfo.CondParam))
		}
	}
	if _, exist := gSkillTargetInfoSort[skillTargetInfo.Sort]; !exist {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: Sort no found %d", skillTargetInfo.Sort))
	}
	if skillTargetInfo.Count > uint32(TeamMaxPos) {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: Count overflow %d", skillTargetInfo.Count))
	}
	if _, exist := gSkillTargetInfoAdded[skillTargetInfo.Added]; !exist {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: Added no found %d", skillTargetInfo.Added))
	}
	if skillTargetInfo.Added == ReviseAddCount && skillTargetInfo.AddedCount > 0 && skillTargetInfo.Condition == BaseChooseConditionNo {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: no meaning! Added:%d,AddedCount:%d,Condition:%d",
			skillTargetInfo.Added, skillTargetInfo.AddedCount, skillTargetInfo.Condition))
	}
	if skillTargetInfo.AddedCount > uint32(TeamMaxPos) {
		panic(fmt.Sprintf("load config SkillTargetInfoManager fail: AddedCount overflow %d", skillTargetInfo.Count))
	}
}

func (m *SkillTargetInfoManager) GetRecordById(id uint32) *SkillTargetInfo {
	return m.Datas[id]
}

func (m *SkillTargetInfoManager) Index(key uint32) *SkillTargetInfo {
	return m.Datas[key]
}
