package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityStoryInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ActivityStoryInfoExt
	slice   []*ActivityStoryInfoExt
}

type ActivityStoryInfoExt struct {
	ID              uint32
	FunctionID      uint32
	OpenTime        int64
	EndTime         int64
	LoginID         uint32
	DungeonID       uint32
	ShopID          uint32
	TicketClRes     []*cl.Resource
	FightTicket     []*cl.Resource
	RankID          uint32
	RechargeShopIds []uint32
	MailId          uint32
}

func newActivityStoryInfoManager(xmlData *XmlData) *ActivityStoryInfoManager {
	m := &ActivityStoryInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityStoryInfoManager) name() string {
	return "ActivityStoryInfo"
}

func (m *ActivityStoryInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityStoryInfoManager) checkData() error {
	return nil
}

func (m *ActivityStoryInfoManager) load(dir string, isShow bool) error {
	tmp := &ActivityStoryInfos{}
	fileName := filepath.Join(dir, "activity_story_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*ActivityStoryInfoExt, len(tmp.Datas))
	m.slice = make([]*ActivityStoryInfoExt, 0, len(tmp.Datas)/2)
	idCheck := uint32(0)
	lastStartUnix := int64(0)
	lastEndUnix := int64(0)
	for _, data := range tmp.Datas {
		idCheck++
		if idCheck != data.Id {
			panic(fmt.Sprintf("activity_story_info data:%d error id must be continue", data.Id))
		}
		data.prepare()
		//开启时间
		openTime, err := time.ParseInLocation(TIME_LAYOUT, data.OpenDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("activity_story_info data:%d openTime failed: %v err:%s", data.Id, data.OpenDay, err))
		}
		openUnix := openTime.Unix()

		endTime, err := time.ParseInLocation(TIME_LAYOUT, data.EndDay, time.Local)
		if err != nil {
			panic(fmt.Sprintf("activity_story_info data:%d endTime failed: %v err:%s", data.Id, data.EndDay, err))
		}
		endUnix := openTime.Unix()

		if openUnix > endUnix {
			panic(fmt.Sprintf("activity_story_info data:%d openTime:%+v more than endTime:%+v", data.Id, openTime, endTime))
		}

		if lastStartUnix != 0 && lastEndUnix != 0 {
			//后续活动时间只能排在之前活动后面
			if openUnix <= lastEndUnix {
				panic(fmt.Sprintf("activity_story_info data:%d open time illegal", data.Id))
			}
		}

		functionInfo := m.xmlData.FunctionInfoM.Index(data.FunctionId)
		if functionInfo == nil {
			panic(fmt.Sprintf("activity_story_info data:%d function id:%d is error", data.Id, data.FunctionId))
		}

		roundInfo := m.xmlData.ActivityStoryLoginInfoM.GetRound(data.LoginId)
		if len(roundInfo) == 0 {
			panic(fmt.Sprintf("activity_story_info data:%d login:%d is erorr", data.Id, data.LoginId))
		}

		fightTicket := make([]*cl.Resource, 0, len(data.TicketClRes))
		for _, ticket := range data.TicketClRes {
			tmpTicket := ticket.Clone()
			tmpTicket.Count = 1
			fightTicket = append(fightTicket, tmpTicket)
		}

		if data.PassId > 0 {
			passInfo := m.xmlData.PassInfoM.Index(data.PassId)
			if passInfo == nil {
				panic(fmt.Sprintf("activity_story_info data:%d pass:%d info is nil", data.Id, data.PassId))
			}
			if passInfo.PassType != PassTypeActivity {
				panic(fmt.Sprintf("activity_story_info data:%d pass:%d passType:%d is error", data.Id, data.PassId, passInfo.PassType))
			}
			if passInfo.OpenDay != openTime.Unix() || passInfo.EndDay != endTime.Unix() {
				panic(fmt.Sprintf("activity_story_info data:%d pass:%d open time or end time not same with activity:%d", data.Id, data.PassId, passInfo.Id))
			}
		}

		rechargeShopIDs := make([]uint32, 0, 2)
		if data.RechargeId > 0 {
			shopInfo := m.xmlData.ActivityRechargeShopInfoM.Index(data.RechargeId)
			if shopInfo == nil {
				panic(fmt.Sprintf("activity_story_info data:%d recharge id:%d is error", data.Id, data.RechargeId))
			}
			rechargeShopIDs = append(rechargeShopIDs, data.RechargeId)
		}
		if data.SkinRechargeId > 0 {
			shopInfo := m.xmlData.ActivityRechargeShopInfoM.Index(data.SkinRechargeId)
			if shopInfo == nil {
				panic(fmt.Sprintf("activity_story_info data:%d recharge id:%d is error", data.Id, data.SkinRechargeId))
			}
			rechargeShopIDs = append(rechargeShopIDs, data.SkinRechargeId)
		}
		if data.MailId > 0 {
			mailInfo := m.xmlData.MailInfoM.Index(data.MailId)
			if mailInfo == nil {
				panic(fmt.Sprintf("activity_story_info data:%d mail id:%d is error", data.Id, data.MailId))
			}
		}

		ext := &ActivityStoryInfoExt{
			ID:              data.Id,
			FunctionID:      data.FunctionId,
			OpenTime:        openTime.Unix(),
			EndTime:         endTime.Unix(),
			LoginID:         data.LoginId,
			DungeonID:       data.DungeonId,
			ShopID:          data.ShopId,
			TicketClRes:     data.TicketClRes,
			FightTicket:     fightTicket,
			RankID:          data.RankId,
			RechargeShopIds: rechargeShopIDs,
			MailId:          data.MailId,
		}

		_, exist := m.Datas[ext.ID]
		if exist {
			panic(fmt.Sprintf("activity_story_info data:%d is repeated", data.Id))
		}
		m.Datas[ext.ID] = ext
		m.slice = append(m.slice, ext)
	}
	return nil
}

func (m *ActivityStoryInfoManager) GetRecordById(id uint32) *ActivityStoryInfoExt {
	return m.Datas[id]
}

func (m *ActivityStoryInfoManager) Index(key uint32) *ActivityStoryInfoExt {
	return m.Datas[key]
}

// 活动时间左闭右开
func (m *ActivityStoryInfoManager) GetCurrent(now int64) *ActivityStoryInfoExt {
	for _, v := range m.slice {
		if now >= v.OpenTime && now < v.EndTime {
			return v
		}
	}
	return nil
}

func (e *ActivityStoryInfoExt) GetRank() uint32 {
	return uint32(e.OpenTime)
}

// 活动时间左闭右开
func (e *ActivityStoryInfoExt) IsOpen(now int64) bool {
	if now >= e.OpenTime && now < e.EndTime {
		return true
	}
	return false
}

func (m *ActivityStoryInfoManager) GetCloseEndActivity(now int64) *ActivityStoryInfoExt {
	return GetDataLessAndClosedByRank(m.slice, uint32(now))
}

func (m *ActivityStoryInfoManager) GetSlice() []*ActivityStoryInfoExt {
	return m.slice
}
