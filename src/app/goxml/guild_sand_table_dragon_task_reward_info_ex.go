package goxml

import (
	"app/protos/out/cl"
	"errors"
	"fmt"
)

type GuildSandTableDragonTaskRewardInfoMEx struct {
	*GuildSandTableDragonTaskRewardInfoM
	GroupEvent map[uint32]map[uint32][]*GuildSandTableDragonTaskRewardInfo // groupID => taskType => data
	AllEvent   map[uint32]struct{}
}

func newGuildSandTableDragonTaskRewardInfoMEx(xmlData *XmlData) *GuildSandTableDragonTaskRewardInfoMEx {
	m := &GuildSandTableDragonTaskRewardInfoMEx{
		GuildSandTableDragonTaskRewardInfoM: &GuildSandTableDragonTaskRewardInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableDragonTaskRewardInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableDragonTaskRewardInfoMEx) load(dir string, isShow bool) error {
	err := m.GuildSandTableDragonTaskRewardInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	if m.GroupEvent == nil {
		m.GroupEvent = make(map[uint32]map[uint32][]*GuildSandTableDragonTaskRewardInfo)
	}

	if m.AllEvent == nil {
		m.AllEvent = make(map[uint32]struct{})
	}

	for _, v := range m.idRecordMap {
		taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(v.TypeId)
		if taskTypeInfo == nil {
			return errors.Join(fmt.Errorf("GuildSandTableDragonTaskRewardInfoMEx no taskTypeInfo. v.TypeId:%d", v.TypeId), err)
		}
		m.AllEvent[taskTypeInfo.Type] = struct{}{}

		_, exist := m.GroupEvent[v.Group]
		if !exist {
			m.GroupEvent[v.Group] = make(map[uint32][]*GuildSandTableDragonTaskRewardInfo)
		}
		_, exist = m.GroupEvent[v.Group][taskTypeInfo.Type]
		if !exist {
			m.GroupEvent[v.Group][taskTypeInfo.Type] = make([]*GuildSandTableDragonTaskRewardInfo, 0, 5)
		}
		m.GroupEvent[v.Group][taskTypeInfo.Type] = append(m.GroupEvent[v.Group][taskTypeInfo.Type], v)

		// 奖励
		if v.Type1 > 0 && v.Count1 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type1,
				Value: v.Value1,
				Count: v.Count1,
			})
		}
		if v.Type2 > 0 && v.Count2 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type2,
				Value: v.Value2,
				Count: v.Count2,
			})
		}
		if v.Type3 > 0 && v.Count3 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type3,
				Value: v.Value3,
				Count: v.Count3,
			})
		}
	}
	return nil
}

func (m *GuildSandTableDragonTaskRewardInfoMEx) checkData() error {
	err := m.GuildSandTableDragonTaskRewardInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}

func (m *GuildSandTableDragonTaskRewardInfoMEx) GetAllEvent() map[uint32]struct{} {
	return m.AllEvent
}

func (m *GuildSandTableDragonTaskRewardInfoMEx) GetGroupEvent(group uint32) map[uint32][]*GuildSandTableDragonTaskRewardInfo {
	return m.GroupEvent[group]
}

func (m *GuildSandTableDragonTaskRewardInfoMEx) GetGroupTaskLen(group uint32) int {
	var totalLen int
	for _, v := range m.GroupEvent[group] {
		totalLen += len(v)
	}
	return totalLen
}
