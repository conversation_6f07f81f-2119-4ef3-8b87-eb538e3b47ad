package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
	"strconv"
	"strings"
)

type PokemonSummonInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*PokemonSummonInfoExt
}

type PokemonSummonInfoExt struct {
	*PokemonSummonInfo
	CanChooseID    map[uint32]struct{}
	PluralCosts    []*cl.Resource
	guaranteeGroup map[int]uint32
}

func newPokemonSummonInfoManager(xmlData *XmlData) *PokemonSummonInfoManager {
	m := &PokemonSummonInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *PokemonSummonInfoManager) name() string {
	return "pokemon_summon_info.xml"
}

func (m *PokemonSummonInfoManager) ignoreForCheck() bool {
	return false
}

func (m *PokemonSummonInfoManager) checkData() error {
	return nil
}

func (m *PokemonSummonInfoManager) load(dir string, show bool) error {
	tmp := &PokemonSummonInfos{}
	fileName := filepath.Join(dir, "pokemon_summon_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*PokemonSummonInfoExt, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		ext := &PokemonSummonInfoExt{
			PokemonSummonInfo: data,
			CanChooseID:       make(map[uint32]struct{}),
			guaranteeGroup:    make(map[int]uint32),
		}
		pokemons := strings.Split(data.SelectGroup, ";")
		for _, pokemon := range pokemons {
			pokemonID, err := strconv.ParseUint(pokemon, 10, 32)
			if err != nil {
				panic(fmt.Sprintf("config(%s) id:%d pokemon:%s convert to uint failed", fileName, data.Id, pokemon))
			}
			_, exist := m.xmlData.PokemonInfoM.pokemonIdRecordMap[uint32(pokemonID)]
			if !exist {
				panic(fmt.Sprintf("config(%s) id:%d get pokemonID:%d failed", fileName, data.Id, pokemonID))
			}
			ext.CanChooseID[uint32(pokemonID)] = struct{}{}
		}

		PluralCosts := make([]*cl.Resource, 0, len(data.CostClRes))
		for _, v := range data.CostClRes {
			tmp := v.Clone()
			tmp.Count *= m.xmlData.PokemonSummonConfigInfoM.SummonPlural
			PluralCosts = append(PluralCosts, tmp)
		}
		ext.PluralCosts = PluralCosts

		ext.guaranteeGroup[PokemonSummonRedGuaranteeIndex] = data.GroupId60
		ext.guaranteeGroup[PokemonSummonOrangeGuaranteeIndex] = data.GroupId50
		ext.guaranteeGroup[PokemonSummonPurpleGuaranteeIndex] = data.GroupId40
		ext.guaranteeGroup[PokemonSummonBlueGuaranteeIndex] = data.GroupId30
		ext.guaranteeGroup[PokemonSummonRedFragmentGuaranteeIndex] = data.FragmentGroupId60
		ext.guaranteeGroup[PokemonSummonOrangeFragmentGuaranteeIndex] = data.FragmentGroupId50
		ext.guaranteeGroup[PokemonSummonPurpleFragmentGuaranteeIndex] = data.FragmentGroupId40

		if ptr, exist := m.Datas[ext.Id]; exist {
			*ptr = *ext
			ext = ptr
		} else {
			m.Datas[ext.Id] = ext
		}
	}
	return nil
}

func (m *PokemonSummonInfoManager) Index(key uint32) *PokemonSummonInfoExt {
	return m.Datas[key]
}

func (m *PokemonSummonInfoManager) RandGroup(rd *rand.Rand, colorGuarantee []uint32, id uint32, firstDraw bool) (uint32, bool, bool) {
	if len(colorGuarantee) != PokemonSummonMaxGuaranteeLen {
		l4g.Errorf("PokemonSummonInfoManager RandomGroup: colorGuarantee len is error")
		return 0, false, false
	}

	data, exist := m.Datas[id]
	if !exist {
		return 0, false, false
	}

	weightIds := make([]*WeightAndIdRed, 0, PokemonSummonMaxGuaranteeLen)
	var totalWeight uint32
	for i := PokemonSummonMaxGuaranteeLen - 1; i >= 0; i-- {
		if !firstDraw && i == PokemonSummonRedGuaranteeIndex {
			if m.xmlData.PokemonSummonConfigInfoM.IsFirstDraw(colorGuarantee[i]) {
				return data.guaranteeGroup[i], true, true
			}
		}
		if m.xmlData.PokemonSummonConfigInfoM.Guarantee(i, colorGuarantee[i]) {
			return data.guaranteeGroup[i], true, data.guaranteeGroup[i] == data.GroupId60
		} else {
			weight := m.xmlData.PokemonSummonConfigInfoM.GetWeight(i, colorGuarantee[i])
			weightIds = append(weightIds, &WeightAndIdRed{
				Weight: weight,
				Id:     data.guaranteeGroup[i],
				IsRed:  data.guaranteeGroup[i] == data.GroupId60,
			})
			totalWeight += weight
		}
	}

	if totalWeight < uint32(BaseInt) {
		weightIds = append(weightIds, &WeightAndIdRed{
			Weight: uint32(BaseInt) - totalWeight,
			Id:     data.FragmentGroupId30,
			IsRed:  false,
		})
		totalWeight = uint32(BaseInt)
	}

	for _, weight := range weightIds {
		l4g.Debugf("weightIds weight:%+v", weight)
	}

	randNum := uint32(rd.RandBetween(1, int(totalWeight)))
	for _, weight := range weightIds {
		if weight.Weight < randNum {
			randNum -= weight.Weight
			continue
		}
		return weight.Id, false, weight.IsRed
	}

	return 0, false, false
}

func (m *PokemonSummonInfoManager) IsExistShopID(id uint32, shopID uint32) bool {
	data, exist := m.Datas[id]
	if !exist {
		return false
	}
	return data.RechargeShopId == shopID
}

func (m *PokemonSummonInfoManager) SpecialGroupReward(id uint32, groupId uint32, wishPokemonID uint32, rd *rand.Rand) *cl.Resource {
	data, exist := m.Datas[id]
	if !exist {
		return nil
	}
	if data.GroupId60 == groupId {
		return &cl.Resource{
			Type:  uint32(common.RESOURCE_POKEMON),
			Value: wishPokemonID,
			Count: 1,
		}
	} else if data.FragmentGroupId60 == groupId {
		count := m.xmlData.PokemonSummonConfigInfoM.RandomFragmentCount(rd)
		if count > 0 {
			pokemonInfo := m.xmlData.PokemonInfoM.GetRecordByPokemonId(wishPokemonID)
			if pokemonInfo != nil {
				return &cl.Resource{
					Type:  uint32(common.RESOURCE_POKEMON_FRAGMENT),
					Value: pokemonInfo.FragmentValue,
					Count: count,
				}
			}
		}
	}
	return nil
}

func (p *PokemonSummonInfoExt) GetPluralCosts() []*cl.Resource {
	ret := make([]*cl.Resource, 0, len(p.PluralCosts))
	for _, cost := range p.PluralCosts {
		ret = append(ret, cost.Clone())
	}
	return ret
}
