package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ArenaFightRewardInfoManager struct {
	xmlData         *XmlData
	Datas           map[uint32]*ArenaFightRewardInfo   //group =>
	Groups          map[uint32][]*ArenaFightRewardInfo //group =>
	TotalWeight     map[uint32]int                     //group => weight
	GroupGuaranteed map[uint32]*ArenaFightRewardInfo   //group =>
}

func newArenaFightRewardInfoManager(xmlData *XmlData) *ArenaFightRewardInfoManager {
	m := &ArenaFightRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ArenaFightRewardInfoManager) name() string {
	return "ArenaFightRewardInfo"
}

func (m *ArenaFightRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ArenaFightRewardInfoManager) checkData() error {
	return nil
}

func (m *ArenaFightRewardInfoManager) load(dir string, isShow bool) error {
	tmp := &ArenaFightRewardInfos{}
	fileName := filepath.Join(dir, "arena_fight_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*ArenaFightRewardInfo, len(tmp.Datas))
	}
	m.Groups = make(map[uint32][]*ArenaFightRewardInfo)
	m.TotalWeight = make(map[uint32]int)
	m.GroupGuaranteed = make(map[uint32]*ArenaFightRewardInfo)

	for _, data := range tmp.Datas {
		data.prepare()
		if data.Weight == 0 {
			delete(m.Datas, data.Id)
			l4g.Debugf("config(%s). weight is 0. id:%d", fileName, data.Id)
			continue
		}

		if data.Reward == 0 || len(m.xmlData.DropGroupInfoM.Group(data.Reward)) == 0 {
			panic(fmt.Sprintf("load config %s fail: dropInfo not exist. id:%d dropGroupId:%d", fileName, data.Id, data.Reward))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
		m.Groups[data.Group] = append(m.Groups[data.Group], data)
		m.TotalWeight[data.Group] += int(data.Weight)
		if data.Guaranteed != 0 {
			if _, exist := m.GroupGuaranteed[data.Group]; exist {
				panic(fmt.Sprintf("load config %s fail: Guaranteed repeated. group:%d id:%d", fileName, data.Group, data.Id))
			}
			m.GroupGuaranteed[data.Group] = data
		}
	}

	if len(m.Groups[ArenaFightWin]) < 1 || len(m.Groups[ArenaFightLose]) < 1 {
		panic(fmt.Sprintf("load config %s fail: reward group error. groupWinLen:%d, groupLoseLen:%d", fileName,
			len(m.Groups[ArenaFightWin]), len(m.Groups[ArenaFightLose])))
	}
	if m.TotalWeight[ArenaFightWin] == 0 || m.TotalWeight[ArenaFightLose] == 0 {
		panic(fmt.Sprintf("load config %s fail: weights data error. winWeights:%d, loseWeights:%d", fileName,
			m.TotalWeight[ArenaFightWin], m.TotalWeight[ArenaFightLose]))
	}
	return nil
}

func (m *ArenaFightRewardInfoManager) GetRecordById(id uint32) *ArenaFightRewardInfo {
	return m.Datas[id]
}

func (m *ArenaFightRewardInfoManager) Index(key uint32) *ArenaFightRewardInfo {
	return m.Datas[key]
}

func (m *ArenaFightRewardInfoManager) Group(key uint32) []*ArenaFightRewardInfo {
	return m.Groups[key]
}

func (m *ArenaFightRewardInfoManager) Weight(key uint32) int {
	return m.TotalWeight[key]
}

func (m *ArenaFightRewardInfoManager) GetRewardGuaranteed(rewardType uint32) *ArenaFightRewardInfo {
	return m.GroupGuaranteed[rewardType]
}
