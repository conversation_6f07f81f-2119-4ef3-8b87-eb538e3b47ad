package goxml

import (
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"

	"fmt"
)

type MonsterDataInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]*MonsterDataInfo
	AttrsMap map[uint32]map[uint32][]int64
	mapDatas map[uint64]*MonsterDataInfo
}

func monsterDataToMap(dataInfo *MonsterDataInfo) []int64 {
	// TODO: 当monster的attr大于 MonsterAttrMaxNum 的值时，要修改 MonsterAttrMaxNum 的值为monster的最大 attr+1
	attr := make([]int64, AttrMaxNum)
	attr[AttrAttack] = int64(dataInfo.Attack)
	attr[AttrHp] = int64(dataInfo.Hp)
	attr[AttrSpeed] = int64(dataInfo.Speed)
	attr[AttrPhyDef] = int64(dataInfo.PhyDef)
	attr[AttrMagDef] = int64(dataInfo.MagDef)
	attr[AttrFixedDam] = int64(dataInfo.FixedDam)
	attr[AttrFixedDamReduce] = int64(dataInfo.FixedDamReduce)
	attr[AttrHitRate] = int64(dataInfo.HitRate)
	attr[AttrDodgeRate] = int64(dataInfo.DodgeRate)
	attr[AttrCritRate] = int64(dataInfo.CritRate)
	attr[AttrCritReduceRate] = int64(dataInfo.CritReduceRate)
	attr[AttrControlRate] = int64(dataInfo.ControlRate)
	attr[AttrControlReduceRate] = int64(dataInfo.ControlReduceRate)
	attr[AttrHealRate] = int64(dataInfo.HealRate)
	attr[AttrGetHealRate] = int64(dataInfo.GetHealRate)
	attr[AttrDamAddRate] = int64(dataInfo.DamAddRate)
	attr[AttrDamReduceRate] = int64(dataInfo.DamReduceRate)
	attr[AttrCritDamAddRate] = int64(dataInfo.CritDamAddRate)
	attr[AttrCritDamReduceRate] = int64(dataInfo.CritDamReduceRate)
	attr[AttrDamReflectRate] = int64(dataInfo.DamReflectRate)
	attr[AttrPhyDamReduceRate] = int64(dataInfo.PhyDamReduceRate)
	attr[AttrMagDamReduceRate] = int64(dataInfo.MagDamReduceRate)
	return attr
}

func newMonsterDataInfoManager(xmlData *XmlData) *MonsterDataInfoManager {
	m := &MonsterDataInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *MonsterDataInfoManager) name() string {
	return "MonsterDataInfo"
}

func (m *MonsterDataInfoManager) ignoreForCheck() bool {
	return false
}

func (m *MonsterDataInfoManager) checkData() error {
	return nil
}

func (m *MonsterDataInfoManager) load(dir string, isShow bool) error {
	tmp := &MonsterDataInfos{}
	fileName := filepath.Join(dir, "monster_data_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*MonsterDataInfo, len(tmp.Datas))
	}
	if m.mapDatas == nil {
		m.mapDatas = make(map[uint64]*MonsterDataInfo, len(tmp.Datas))
	}
	//	m.Attrs = make(map[uint32]map[uint32]*MonsterDataInfo, len(tmp.Datas))
	m.AttrsMap = make(map[uint32]map[uint32][]int64, len(tmp.Datas))
	var key uint64
	for _, data := range tmp.Datas {
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}

		if _, exist := m.AttrsMap[data.DataType]; !exist {
			m.AttrsMap[data.DataType] = make(map[uint32][]int64)
		}
		m.AttrsMap[data.DataType][data.Level] = monsterDataToMap(data)
		key = m.genKey(data.DataType, data.Level)
		if ptr, exist := m.mapDatas[key]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.mapDatas[key] = data
		}
	}
	return nil
}

func (m *MonsterDataInfoManager) GetRecordById(id uint32) *MonsterDataInfo {
	return m.Datas[id]
}

func (m *MonsterDataInfoManager) Index(key uint32) *MonsterDataInfo {
	return m.Datas[key]
}

func (m *MonsterDataInfoManager) GetAttrMap(dataType, dataLevel uint32) []int64 {
	return m.AttrsMap[dataType][dataLevel]
}

func (m *MonsterDataInfoManager) GetInfo(dataType, dataLevel uint32) *MonsterDataInfo {
	return m.mapDatas[m.genKey(dataType, dataLevel)]
}

func (m *MonsterDataInfoManager) genKey(dataType, dataLevel uint32) uint64 {
	return uint64(dataType)<<32 | uint64(dataLevel)
}
