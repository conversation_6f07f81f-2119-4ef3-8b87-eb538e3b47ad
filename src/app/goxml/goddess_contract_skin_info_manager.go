package goxml

import (
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type GoddessContractSkinInfoManager struct {
	xmlData          *XmlData
	Datas            map[uint32]*GoddessContractSkinExt
	goddessLevelSuit map[uint32]map[uint32]*GoddessContractSkinExt //goddess Level
}

type GoddessContractSkinExt struct {
	GoddessSkinId uint32
	Goddess       uint32
	UnlockLevel   uint32
	GlobalAttr    map[uint32]int64
	AutoWear      uint32
}

func newGoddessContractSkinInfoManager(xmlData *XmlData) *GoddessContractSkinInfoManager {
	m := &GoddessContractSkinInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GoddessContractSkinInfoManager) name() string {
	return "GoddessContractSkinInfo"
}

func (m *GoddessContractSkinInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GoddessContractSkinInfoManager) checkData() error {
	return nil
}

func (m *GoddessContractSkinInfoManager) load(dir string, isShow bool) error {
	tmp := &GoddessContractSkinInfos{}
	fileName := filepath.Join(dir, "goddess_contract_skin_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*GoddessContractSkinExt, len(tmp.Datas))
	m.goddessLevelSuit = make(map[uint32]map[uint32]*GoddessContractSkinExt)
	for _, data := range tmp.Datas {
		if data.UnlockLevel == 0 {
			if data.AttrType1 != 0 || data.AttrValue1 != 0 || data.AttrType2 != 0 || data.AttrValue2 != 0 || data.AttrType3 != 0 || data.AttrValue3 != 0 || data.AttrType4 != 0 || data.AttrValue4 != 0 {
				panic(fmt.Sprintf("data id:%d attrs must be 0", data.GoddessSkinId))
			}
			continue
		}
		ext := &GoddessContractSkinExt{}
		ext.Goddess = data.Goddess
		ext.GoddessSkinId = data.GoddessSkinId
		ext.UnlockLevel = data.UnlockLevel
		ext.GlobalAttr = make(map[uint32]int64)
		ext.AutoWear = data.AutoWear
		if data.AttrType1 > 0 && data.AttrValue1 > 0 {
			ext.GlobalAttr[data.AttrType1] += int64(data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrValue2 > 0 {
			ext.GlobalAttr[data.AttrType2] += int64(data.AttrValue2)
		}
		if data.AttrType3 > 0 && data.AttrValue3 > 0 {
			ext.GlobalAttr[data.AttrType3] += int64(data.AttrValue3)
		}
		if data.AttrType4 > 0 && data.AttrValue4 > 0 {
			ext.GlobalAttr[data.AttrType4] += int64(data.AttrValue4)
		}
		goddessInfo := m.xmlData.GoddessContractInfoM.Index(data.Goddess)
		if goddessInfo == nil {
			panic(fmt.Sprintf("data skinId:%d goddess:%d is error", data.GoddessSkinId, data.Goddess))
		}

		_, exist := m.goddessLevelSuit[data.Goddess]
		if !exist {
			m.goddessLevelSuit[data.Goddess] = make(map[uint32]*GoddessContractSkinExt)
		}
		m.goddessLevelSuit[data.Goddess][data.UnlockLevel] = ext

		if ptr, exist := m.Datas[ext.GoddessSkinId]; exist {
			*ptr = *ext
			ext = ptr
		} else {
			m.Datas[data.GoddessSkinId] = ext
		}
	}
	return nil
}

func (m *GoddessContractSkinInfoManager) Index(index uint32) *GoddessContractSkinExt {
	return m.Datas[index]
}

func (m *GoddessContractSkinInfoManager) GetSuit(goddessID, level uint32) *GoddessContractSkinExt {
	return m.goddessLevelSuit[goddessID][level]
}
