package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityStoryLoginInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ActivityStoryLoginInfo
	Round   map[uint32][]*ActivityStoryLoginInfo
}

func newActivityStoryLoginInfoManager(xmlData *XmlData) *ActivityStoryLoginInfoManager {
	m := &ActivityStoryLoginInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityStoryLoginInfoManager) name() string {
	return "ActivityStoryLoginInfo"
}

func (m *ActivityStoryLoginInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityStoryLoginInfoManager) checkData() error {
	return nil
}

func (m *ActivityStoryLoginInfoManager) load(dir string, isShow bool) error {
	tmp := &ActivityStoryLoginInfos{}
	fileName := filepath.Join(dir, "activity_story_login_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*ActivityStoryLoginInfo, len(tmp.Datas))
	m.Round = make(map[uint32][]*ActivityStoryLoginInfo)
	for _, data := range tmp.Datas {
		data.prepare()

		_, exist := m.Round[data.LoginId]
		if !exist {
			m.Round[data.LoginId] = make([]*ActivityStoryLoginInfo, 0, 15)
		}
		m.Round[data.LoginId] = append(m.Round[data.LoginId], data)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ActivityStoryLoginInfoManager) GetRecordById(id uint32) *ActivityStoryLoginInfo {
	return m.Datas[id]
}

func (m *ActivityStoryLoginInfoManager) Index(key uint32) *ActivityStoryLoginInfo {
	return m.Datas[key]
}

func (m *ActivityStoryLoginInfoManager) GetRound(round uint32) []*ActivityStoryLoginInfo {
	return m.Round[round]
}
