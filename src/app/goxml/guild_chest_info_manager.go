package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildChestInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildChestInfo
}

func newGuildChestInfoManager(xmlData *XmlData) *GuildChestInfoManager {
	m := &GuildChestInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildChestInfoManager) name() string {
	return "GuildChestInfo"
}

func (m *GuildChestInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildChestInfoManager) checkData() error {
	return nil
}

func (m *GuildChestInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildChestInfos{}
	fileName := filepath.Join(dir, "guild_chest_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GuildChestInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()

		if data.MinToken == 0 {
			panic(fmt.Sprintf("check data error %s: minToken is 0. id:%d", fileName, data.Id))
		}

		maxLevelInfo := m.xmlData.GuildLevelInfoM.Index(m.xmlData.GuildLevelInfoM.maxLevel)
		if maxLevelInfo == nil {
			panic(fmt.Sprintf("check data error %s: guildLevelInfo not exist. id:%d", fileName, m.xmlData.GuildLevelInfoM.maxLevel))
		}
		if data.Count < maxLevelInfo.Member*data.MinToken {
			panic(fmt.Sprintf("check data error %s: resourceCount error. id:%d", fileName, data.Id))
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildChestInfoManager) GetRecordById(id uint32) *GuildChestInfo {
	return m.Datas[id]
}

func (m *GuildChestInfoManager) Index(key uint32) *GuildChestInfo {
	return m.Datas[key]
}

func (m *GuildChestInfo) IsCostNum() bool {
	return m.CostNum == 1
}
