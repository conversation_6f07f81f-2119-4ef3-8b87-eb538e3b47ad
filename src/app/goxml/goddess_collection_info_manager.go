package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GoddessCollectionInfoManager struct {
	xmlData         *XmlData
	collectionInfos map[uint32]*GoddessCollectionInfo // key: goddessLevel
}

func newGoddessCollectionInfoManager(xmlData *XmlData) *GoddessCollectionInfoManager {
	m := &GoddessCollectionInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GoddessCollectionInfoManager) name() string {
	return "GoddessCollectionInfo"
}

func (m *GoddessCollectionInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GoddessCollectionInfoManager) checkData() error {
	return nil
}

func (m *GoddessCollectionInfoManager) load(dir string, isShow bool) error {
	tmp := &GoddessCollectionInfos{}
	fileName := filepath.Join(dir, "goddess_collection_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.collectionInfos = make(map[uint32]*GoddessCollectionInfo)
	isRepeat := make(map[uint32]struct{})
	for _, data := range tmp.Datas {
		data.prepare()
		if _, exist := isRepeat[data.UnlockLevel]; exist {
			panic(fmt.Sprintf("load config %s fail, unlock level is repeat. id: %d", fileName, data.Id))
		}
		if m.xmlData.GoddessContractBlessInfoM.GetLevelInfo(data.UnlockLevel) == nil {
			panic(fmt.Sprintf("load config %s fail, unlock level is invalid. id: %d", fileName, data.Id))
		}

		if data.Type != GoddessCollectionTypeOnhook && data.Type != GoddessCollectionTypeBox {
			panic(fmt.Sprintf("load config %s fail, addType is invalid. id: %d", fileName, data.Id))
		}

		if data.Type == GoddessCollectionTypeBox {
			if m.xmlData.GoddessCollectionBoxInfoM.Index(data.BoxId) == nil {
				panic(fmt.Sprintf("load config %s fail, boxID is invalid. id: %d, boxID:%d", fileName, data.Id, data.BoxId))
			}
		}

		if data.Type == GoddessCollectionTypeOnhook && len(data.ResClRes) == 0 {
			panic(fmt.Sprintf("load config %s fail, addResource is nil. id: %d", fileName, data.Id))
		}

		lastRes := new(cl.Resource)
		for _, res := range data.ResClRes {
			if res.Type == lastRes.Type && res.Value == lastRes.Value {
				panic(fmt.Sprintf("load config %s fail, resource is repeat. id: %d", fileName, data.Id))
			}
			lastRes = res
		}

		m.collectionInfos[data.UnlockLevel] = data
	}
	return nil
}

func (m *GoddessCollectionInfoManager) AllCollectionInfos() map[uint32]*GoddessCollectionInfo {
	return m.collectionInfos
}

func (m *GoddessCollectionInfoManager) GetCollectionInfo(goddessLevel uint32) *GoddessCollectionInfo {
	return m.collectionInfos[goddessLevel]
}

func (m *GoddessCollectionInfoManager) RecalcAwardFromOnHook(goddessLevel uint32, awards []*cl.Resource, userID uint64) []*cl.Resource {
	if len(awards) == 0 {
		return nil
	}

	collectionInfo := m.collectionInfos[goddessLevel]
	if collectionInfo == nil {
		l4g.Errorf("user: %d ActivateCollection: collectionInfo is nil. goddessLevel: %d", userID, goddessLevel)
		return nil
	}

	if collectionInfo.Type != GoddessCollectionTypeOnhook {
		return nil
	}

	return m.recalcAward(awards, collectionInfo)
}

func (m *GoddessCollectionInfoManager) recalcAward(awards []*cl.Resource, info *GoddessCollectionInfo) []*cl.Resource {
	retAwards := make([]*cl.Resource, 0, len(awards))
	for _, award := range awards {
		newCount := calcCount(award, info)
		if newCount == 0 {
			continue
		}
		copyAward := award.Clone()
		copyAward.Count = newCount
		retAwards = append(retAwards, copyAward)
	}

	return retAwards
}

func calcCount(award *cl.Resource, info *GoddessCollectionInfo) uint32 {
	for _, data := range info.ResClRes {
		if award.Type == data.Type && award.Value == data.Value {
			return uint32(float64(award.Count) * (float64(data.Count) / BaseFloat))
		}
	}

	return 0
}
