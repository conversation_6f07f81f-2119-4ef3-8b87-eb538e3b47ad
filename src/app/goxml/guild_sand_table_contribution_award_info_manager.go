package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/time"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type GuildSandTableContributionAwardInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]map[uint32][]*GuildSandTableContributionAwardInfo //group rank s
}

func newGuildSandTableContributionAwardInfoManager(xmlData *XmlData) *GuildSandTableContributionAwardInfoManager {
	m := &GuildSandTableContributionAwardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableContributionAwardInfoManager) name() string {
	return "guild_sand_table_contribution_award_info"
}

func (m *GuildSandTableContributionAwardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableContributionAwardInfoManager) checkData() error {
	return nil
}

func (g *GuildSandTableContributionAwardInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableContributionAwardInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_contribution_award_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	g.Datas = make(map[uint32]map[uint32][]*GuildSandTableContributionAwardInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		_, exist := g.Datas[data.ContributionAwardGroup]
		if !exist {
			g.Datas[data.ContributionAwardGroup] = make(map[uint32][]*GuildSandTableContributionAwardInfo)
		}
		_, exist = g.Datas[data.ContributionAwardGroup][data.GuildRank]
		if !exist {
			g.Datas[data.ContributionAwardGroup][data.GuildRank] = make([]*GuildSandTableContributionAwardInfo, 0, 2)
		}
		awardsLen := len(g.Datas[data.ContributionAwardGroup][data.GuildRank])
		if data.ContributionNumMin > data.ContributionNumMax {
			panic(fmt.Sprintf("data id:%d contribution min > data.contribution max", data.Id))
		}
		if awardsLen > 0 {
			preData := g.Datas[data.ContributionAwardGroup][data.GuildRank][awardsLen-1]
			if len(preData.LeaderAwardClRes) != len(data.LeaderAwardClRes) {
				panic(fmt.Sprintf("data:%d leader award len is not same with predata:%d", data.Id, preData.Id))
			}
			for index, resource := range data.LeaderAwardClRes {
				preResource := preData.LeaderAwardClRes[index]
				if resource.Type != preResource.Type || resource.Value != preResource.Value || resource.Count != preResource.Count {
					panic(fmt.Sprintf("data:%d leader award is diff wht predata:%d", data.Id, preData.Id))
				}
			}
			if preData.ContributionNumMax > data.ContributionNumMin {
				panic(fmt.Sprintf("pre data:%d max > data:%d min", preData.Id, data.Id))
			}
		}
		g.Datas[data.ContributionAwardGroup][data.GuildRank] = append(g.Datas[data.ContributionAwardGroup][data.GuildRank], data)
	}
	return nil
}

func (g *GuildSandTableContributionAwardInfoManager) GroupRankInfo(awardGroup, guildRank, userRank uint32) *GuildSandTableContributionAwardInfo {
	return GetDataLessAndClosedByRank(g.Datas[awardGroup][guildRank], userRank)
}

func (g *GuildSandTableContributionAwardInfo) GetRank() uint32 {
	return g.ContributionNumMin
}

func (g *GuildSandTableContributionAwardInfo) UpdateAvatarExpireTime(xmlData *XmlData) []*cl.Resource {
	newAwards := make([]*cl.Resource, 0, len(g.ClRes))

	for _, award := range g.ClRes {
		newA := award.Clone()
		newAwards = append(newAwards, newA)
		if newA.Type != uint32(common.RESOURCE_AVATAR) {
			continue
		}
		info := xmlData.AvatarInfoM.Index(newA.Value)
		if info != nil && info.DurationType == AvatarTimeTypeEndTime {
			endTm := time.Now().Unix() + int64(info.Duration*60)
			newA.Attrs = []*cl.Attr{
				{
					Value: endTm,
				},
			}
		}
	}
	return newAwards
}
