package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type DropActivityOpenExchangeInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*DropActivityOpenExchangeInfo
	Rules   map[uint32][]*cl.DropActivityExchangeRule
}

func newDropActivityOpenExchangeInfoManager(xmlData *XmlData) *DropActivityOpenExchangeInfoManager {
	m := &DropActivityOpenExchangeInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DropActivityOpenExchangeInfoManager) name() string {
	return "drop_activity_open_exchange_info.xml"
}

func (m *DropActivityOpenExchangeInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DropActivityOpenExchangeInfoManager) checkData() error {
	return nil
}

func (m *DropActivityOpenExchangeInfoManager) load(dir string, show bool) error {
	tmp := &DropActivityOpenExchangeInfos{}
	fileName := filepath.Join(dir, "drop_activity_open_exchange_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*DropActivityOpenExchangeInfo, len(tmp.Datas))
	}
	m.Rules = make(map[uint32][]*cl.DropActivityExchangeRule)
	for _, data := range tmp.Datas {
		data.prepare()
		_, exist := m.Rules[data.Id]
		if !exist {
			m.Rules[data.Id] = make([]*cl.DropActivityExchangeRule, 0, 10)
		}

		if len(data.Cost1ClRes) == 0 || len(data.RewardClRes) == 0 {
			panic(fmt.Sprintf("config %s data:%d cost1 or reward is nil", m.name(), data.Id))
		}

		rule := &cl.DropActivityExchangeRule{
			Tag:           data.Tag,
			Id:            data.Id,
			DisplayType:   data.DisplayType,
			DisplaySort:   data.DisplaySort,
			Max:           data.Max,
			Target:        data.RewardClRes,
			RecommendType: data.RecommendType,
		}
		rule.Condition = append(rule.Condition, data.Cost1ClRes...)

		if len(data.Cost2ClRes) > 0 {
			rule.Condition = append(rule.Condition, data.Cost2ClRes...)
		}
		if len(data.Cost3ClRes) > 0 {
			rule.Condition = append(rule.Condition, data.Cost3ClRes...)
		}

		m.Rules[data.ActivityId] = append(m.Rules[data.ActivityId], rule)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *DropActivityOpenExchangeInfoManager) Index(key uint32) *DropActivityOpenExchangeInfo {
	return m.Datas[key]
}

func (m *DropActivityOpenExchangeInfoManager) GetRules(key uint32) []*cl.DropActivityExchangeRule {
	return m.Rules[key]
}
