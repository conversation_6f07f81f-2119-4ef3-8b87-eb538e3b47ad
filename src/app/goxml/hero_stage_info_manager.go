package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type HeroStageInfoManager struct {
	xmlData            *XmlData
	Datas              map[uint32]*HeroStageInfoExt
	minStage, maxStage uint32
}

type HeroStageInfoExt struct {
	Stage  uint32 //int:当前突破等级
	NeedLv uint32 //int:需求等级
	Costs  []*cl.Resource
}

func newHeroStageInfoManager(xmlData *XmlData) *HeroStageInfoManager {
	m := &HeroStageInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HeroStageInfoManager) name() string {
	return "HeroStageInfo"
}

func (m *HeroStageInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HeroStageInfoManager) checkData() error {
	return nil
}

func (m *HeroStageInfoManager) load(dir string, isShow bool) error {
	tmp := &HeroStageInfos{}
	fileName := filepath.Join(dir, "hero_stage_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*HeroStageInfoExt, len(tmp.Datas))
	}
	m.maxStage = 0

	dataCount := len(tmp.Datas)
	if dataCount == 0 {
		panic(fmt.Sprintf("load config %s, no config data", fileName))
	}
	m.minStage = tmp.Datas[0].Stage
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Stage > m.minStage {
			if m.maxStage+1 != data.Stage {
				panic(fmt.Sprintf("config data discontinuity: %s, stage:%d", fileName, data.Stage))
			}
		}
		m.maxStage = data.Stage

		dataExt := &HeroStageInfoExt{
			Stage:  data.Stage,
			NeedLv: data.NeedLv,
			Costs:  make([]*cl.Resource, 0, HeroStageUpCostNum),
		}

		// 升级消耗
		dataExt.Costs = data.ClRes
		if dataCount-1 > int(data.Stage) {
			if len(dataExt.Costs) == 0 {
				panic(fmt.Sprintf("load config %s fail: %d no costs", fileName, data.Stage))
			}
		}

		if ptr, exist := m.Datas[data.Stage]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Stage] = dataExt
		}
	}
	return nil
}

func (m *HeroStageInfoManager) Index(stage uint32) *HeroStageInfoExt {
	return m.Datas[stage]
}

func (m *HeroStageInfoManager) GetDefaultStage() uint32 {
	return m.minStage
}

func (m *HeroStageInfoManager) GetMaxStage() uint32 {
	return m.maxStage
}
