package goxml

const (
	AttrNone                    = iota
	AttrAttack                  //攻击
	AttrHp                      //生命
	AttrDef                     //防御
	AttrSpeed                   //速度
	AttrPhyDef                  //物防
	AttrMagDef                  //魔防
	AttrFixedDam                //固定伤害
	AttrFixedDamReduce          //固定免伤
	AttrAttackPct               //(基础)攻击%
	AttrHpPct                   //(基础)生命%
	AttrDefPct                  //(基础)防御%
	AttrSpeedPct                //(基础)速度%
	AttrPhyDefPct               //(基础)物防%
	AttrMagDefPct               //(基础)魔防%
	AttrFixedDamPct             //(基础)固定伤害%
	AttrFixedDamReducePct       //(基础)固定免伤%
	AttrRealDam                 //真实攻击(真实伤害)
	AttrHitRate                 //命中率
	AttrDodgeRate               //闪避率
	AttrCritRate                //暴击率
	AttrCritReduceRate          //抗暴率
	AttrControlRate             //效果命中
	AttrControlReduceRate       //效果抗性
	AttrHealRate                //治疗
	AttrGetHealRate             //受疗
	AttrDamAddRate              //伤害加深
	AttrDamReduceRate           //伤害减免
	AttrPhyDamAddRate           //物伤加深
	AttrPhyDamReduceRate        //物伤减免
	AttrMagDamAddRate           //魔伤加深
	AttrMagDamReduceRate        //魔伤减免
	AttrSuckBloodRate           //吸血率
	AttrSuckBloodReduceRate     //吸血抗性
	AttrCritDamAddRate          //暴伤加深
	AttrCritDamReduceRate       //暴伤减免
	AttrDefPunctureRate         //无视防御
	AttrDefPunctureReduceRate   //无视防御抗性
	AttrDamReflectRate          //反弹率
	AttrDamReflectReduceRate    //反弹抗性
	AttrRealDefenseAddDam       //真实受伤害修正
	AttrGetEnergyPct            //神器能量获取加成%
	AttrFinalDamAddRate         //最终增伤加成
	AttrFinalDamReduceRate      //最终减伤加成
	AttrFinalHealRate           //最终治疗效果
	AttrPVPDamAddRate           //PVP增伤
	AttrPVPDamReduceRate        //PVP减伤
	AttrFixMaxHpPct             //最大血量百分比
	AttrShieldDamAddRate        //护盾增伤属性
	AttrAttackPct1              //隐式攻击百分比
	AttrHpPct1                  //隐式血量百分比
	AttrDefPct1                 //隐式防御百分比
	AttrSeasonPhyDamAddRate     //赛季物理增伤
	AttrSeasonPhyDamReduceRate  //赛季物理减伤
	AttrSeasonMagDamAddRate     //赛季魔法增伤
	AttrSeasonMagDamReduceRate  //赛季魔法减伤
	AttrSeasonDamAddRate        //赛季增伤
	AttrSeasonDamReduceRate     //赛季减伤
	AttrLockBlood               //锁血属性(万分比)
	AttrUndefined               //todo未定义，后续新增属性可使用此id
	AttrFixMaxHpPct2            //最大生命值百分比2
	AttrSeasonDamAddRateForLink //增伤(羁绊克制用)
	AttrSeasonDamReduceForLink  //减伤(羁绊克制用)
	AttrAttackPctForSeasonTeam  //攻击百分比(赛季队伍)
	AttrDefPctForSeasonTeam     //防御百分比(赛季队伍)
	AttrHpPctForSeasonTeam      //血量百分比(赛季队伍)
	AttrAttackPctForSeasonHero  //攻击百分比(赛季个人)
	AttrDefPctForSeasonHero     //防御百分比(赛季个人)
	AttrHpPctForSeasonHero      //血量百分比(赛季个人)
	AttrGVGDamAddRate           //GVG增伤
	AttrGVGDamReduceRate        //GVG减伤
	AttrGVGMoraleDamReduceRate  //GVG士气造成伤害降低
	AttrAbsoluteAttack          //攻击力绝对值
	AttrAbsoluteDef             //防御力绝对值
	AttrHitRate2                //命中率2
	AttrDodgeRate2              //闪避率2
	AttrCritDamAddRatePct       //暴击伤害加成
	AttrFixMaxHpPct3            //最大生命值百分比3
	AttrAbsoluteMaxHp           //最大生命值绝对值
	AttrYiShang                 //易伤
	AttrArtifactDamAddRate      //神器增伤
	AttrArtifactDamReduceRate   //神器减伤
	AttrSeasonShieldDamAddRate  //护盾增伤属性(赛季用)
	AttrAttackPctForPokemon     //攻击百分比
	AttrDefPctForPokemon        //防御百分比
	AttrHpPctForPokemon         //最大血量百分比
	AttrDamAddRateForPokemon    //增伤
	AttrDamReduceRateForPokemon //减伤
	AttrPokemonPct              //增强PokeMon属性的百分比
	AttrSuckBloodRateFix        //吸血率修正
	AttrMaxNum
)

// 属性变化标记相关常量
const (
	AttrChangeFlagParam      = 63                                 // 每组属性数量上限
	AttrChangeFlagGroupCount = AttrMaxNum/AttrChangeFlagParam + 1 // 分组数量
)

// 获取属性变化标记所在组
// @param realAttrID 实际配置的属性id
// @return group 属性变化标记所在组
// @return id 属性变化标记id --- 每组范围为1-63
func GetAttrChangeFlagGroupAndID(realAttrID int) (int, int) {
	group := (realAttrID - 1) / AttrChangeFlagParam
	id := (realAttrID - 1) % AttrChangeFlagParam
	return group, id + 1
}

// MonsterAttrMaxNum : 初始化monster, 属性slice的最大index
const (
	MonsterAttrMaxNum = AttrDamReduceRate
)

// 需要合并的属性, 计算前，先把k的属性合并到v上面
var (
	AttrDefMerged    = []int{AttrPhyDef, AttrMagDef}
	AttrDefPctMerged = []int{AttrPhyDefPct, AttrMagDefPct}
)

func GetAttrMerged(t int) []int {
	switch t {
	case AttrDef:
		return AttrDefMerged
	case AttrDefPct:
		return AttrDefPctMerged
	}
	return nil
}

var (
	AttrAttackPcts         = []int{AttrAttackPct}
	AttrHpPcts             = []int{AttrHpPct, AttrHpPct1, AttrHpPctForSeasonTeam, AttrHpPctForSeasonHero}
	AttrSpeedPcts          = []int{AttrSpeedPct}
	AttrPhyDefPcts         = []int{AttrPhyDefPct}
	AttrMagDefPcts         = []int{AttrMagDefPct}
	AttrFixedDamPcts       = []int{AttrFixedDamPct}
	AttrFixedDamReducePcts = []int{AttrFixedDamReducePct}
	AttrCritDamAddRatePcts = []int{AttrCritDamAddRatePct}
	AttrPokemonPcts        = []int{AttrPokemonPct}
)

func GetAttrTypeToPct(t int) []int {
	switch t {
	case AttrAttack:
		return AttrAttackPcts
	case AttrHp:
		return AttrHpPcts
	case AttrSpeed:
		return AttrSpeedPcts
	case AttrPhyDef:
		return AttrPhyDefPcts
	case AttrMagDef:
		return AttrMagDefPcts
	case AttrFixedDam:
		return AttrFixedDamPcts
	case AttrFixedDamReduce:
		return AttrFixedDamReducePcts
	case AttrCritDamAddRate:
		return AttrCritDamAddRatePcts
	case AttrAttackPctForPokemon, AttrDefPctForPokemon, AttrHpPctForPokemon, AttrDamAddRateForPokemon,
		AttrDamReduceRateForPokemon:
		return AttrPokemonPcts
	}
	return nil
}

// 独立攻防百分比
var (
	AttrAttackIndependentPcts = []int{AttrAttackPct1, AttrAttackPctForSeasonTeam, AttrAttackPctForSeasonHero,
		AttrAttackPctForPokemon}
	AttrDefIndependentPcts = []int{AttrDefPct1, AttrDefPctForSeasonTeam, AttrDefPctForSeasonHero,
		AttrDefPctForPokemon}
)

// 获取独立攻防百分比
func GetAttrTypeToIndependentPct(t int) []int {
	switch t {
	case AttrAttack:
		return AttrAttackIndependentPcts
	case AttrPhyDef, AttrMagDef:
		return AttrDefIndependentPcts
	}
	return nil
}

// 获取绝对值攻击、防御属性id
func GetAttrTypeToAbsAttrID(t int) int {
	switch t {
	case AttrAttack:
		return AttrAbsoluteAttack
	case AttrPhyDef, AttrMagDef:
		return AttrAbsoluteDef
	}
	return 0
}

var (
	AbsEffectAttrAttack = []int{AttrAttack}
	AbsEffectAttrDef    = []int{AttrPhyDef, AttrMagDef}
)

// 绝对值属性对应的属性类型
func GetAttrAbsType(t int) []int {
	switch t {
	case AttrAbsoluteAttack:
		return AbsEffectAttrAttack
	case AttrAbsoluteDef:
		return AbsEffectAttrDef
	}
	return nil
}

var (
	PctEffectAttrAttack         = []int{AttrAttack}
	PctEffectAttrHp             = []int{AttrHp}
	PctEffectAttrSpeed          = []int{AttrSpeed}
	PctEffectAttrPhyDef         = []int{AttrPhyDef}
	PctEffectAttrMagDef         = []int{AttrMagDef}
	PctEffectAttrDef            = []int{AttrPhyDef, AttrMagDef}
	PctEffectAttrFixedDam       = []int{AttrFixedDam}
	PctEffectAttrFixedDamReduce = []int{AttrFixedDamReduce}
	PctEffectAttrCritDamAddRate = []int{AttrCritDamAddRate}
	PctEffectPokemon            = []int{AttrAttackPctForPokemon, AttrDefPctForPokemon, AttrHpPctForPokemon,
		AttrDamAddRateForPokemon, AttrDamReduceRateForPokemon}
)

func GetAttrPctType(t int) []int {
	switch t {
	case AttrAttackPct, AttrAttackPct1, AttrAttackPctForSeasonTeam, AttrAttackPctForSeasonHero,
		AttrAttackPctForPokemon:
		return PctEffectAttrAttack
	case AttrHpPct, AttrHpPct1, AttrHpPctForSeasonTeam, AttrHpPctForSeasonHero:
		return PctEffectAttrHp
	case AttrSpeedPct:
		return PctEffectAttrSpeed
	case AttrPhyDefPct:
		return PctEffectAttrPhyDef
	case AttrMagDefPct:
		return PctEffectAttrMagDef
	case AttrDefPct1, AttrDefPctForSeasonTeam, AttrDefPctForSeasonHero, AttrDefPctForPokemon:
		return PctEffectAttrDef
	case AttrFixedDamPct:
		return PctEffectAttrFixedDam
	case AttrFixedDamReducePct:
		return PctEffectAttrFixedDamReduce
	case AttrCritDamAddRatePct:
		return PctEffectAttrCritDamAddRate
	case AttrPokemonPct:
		return PctEffectPokemon
	}
	return nil
}

func IsCannotNegativeAttr(t int) bool {
	switch t {
	//不能为负的属性
	case AttrAttack, AttrHp, AttrDef, AttrSpeed, AttrPhyDef, AttrMagDef, AttrFixedDam,
		AttrFixedDamReduce, AttrRealDam, AttrHitRate, AttrDodgeRate, AttrCritRate, AttrCritReduceRate,
		AttrControlRate, AttrControlReduceRate, AttrSuckBloodRate, AttrSuckBloodReduceRate,
		AttrCritDamAddRate, AttrCritDamReduceRate, AttrDefPunctureRate, AttrDefPunctureReduceRate,
		AttrDamReflectRate, AttrDamReflectReduceRate, AttrHitRate2, AttrDodgeRate2, AttrSuckBloodRateFix:
		return true
	}
	return false
}

func MergeAttr(attr []int64) {
	for k := range attr {
		if attr[k] == 0 {
			continue
		}
		//防御和防御百分比在最终属性里面是不存在的.他们量需要为0，并且把数据加到物理和魔法防御上面
		merged := GetAttrMerged(k)
		if len(merged) == 0 {
			continue
		}
		for _, v := range merged {
			attr[v] += attr[k]
		}
		attr[k] = 0
	}
}

// 百分比属性转化变成0
func AddPctAttr(attr []int64) {
	for k := range attr {
		if attr[k] == 0 {
			continue
		}
		effectAttrs := GetAttrPctType(k)
		if len(effectAttrs) > 0 {
			for _, v := range effectAttrs {
				attr[v] += attr[v] * attr[k] / int64(BaseInt)
			}
			attr[k] = 0
		}
	}
}

func FixNegativeNumAttr(attr []int64) {
	for k := range attr {
		if attr[k] >= 0 {
			continue
		}

		if IsCannotNegativeAttr(k) {
			attr[k] = 0
		}
	}
}

func FixAttr(attr []int64) {
	MergeAttr(attr)
	AddPctAttr(attr)
	FixNegativeNumAttr(attr)
}

// 影响属性的万分比类型属性,最终结果要+万分比
func IsPctTypeAttr(attrType int) bool {
	switch attrType {
	case AttrAttackPct, AttrHpPct, AttrHpPct1, AttrHpPctForSeasonTeam, AttrHpPctForSeasonHero,
		AttrDefPct, AttrSpeedPct, AttrPhyDefPct, AttrMagDefPct, AttrFixedDamPct, AttrFixedDamReducePct,
		AttrCritDamAddRatePct, AttrAttackPct1, AttrAttackPctForSeasonTeam, AttrAttackPctForSeasonHero,
		AttrDefPct1, AttrDefPctForSeasonTeam, AttrDefPctForSeasonHero, AttrAttackPctForPokemon,
		AttrDefPctForPokemon, AttrHpPctForPokemon, AttrPokemonPct, AttrFixMaxHpPct, AttrFixMaxHpPct2,
		AttrFixMaxHpPct3, AttrSuckBloodRateFix:
		return true
	}
	return false
}

func IsEffectMaxHp(attrType int) bool {
	switch attrType {
	case AttrHpPctForPokemon, AttrPokemonPct, AttrFixMaxHpPct, AttrFixMaxHpPct2,
		AttrFixMaxHpPct3, AttrAbsoluteMaxHp:
		return true
	}
	return false
}
