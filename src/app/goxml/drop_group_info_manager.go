package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"math"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type DropGroupInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*DropGroupInfoExt
	Groups  map[uint32][]*DropGroupInfoExt
}

type DropGroupInfoExt struct {
	Id         uint32  //int:掉落池ID
	GroupId    uint32  //int:掉落池ID
	DropId     uint32  //int:掉落组
	DropType   uint32  //int:随机类型 1-必掉 2-随机
	Chance     float64 //float:掉落组概率
	ChangeAdd  float64 //float:递增概率
	Count      float64 //float:掉落组数量
	FixedCount uint32  //int:保底必掉数量（随机到x-1次没中时，第x次必掉）
}

// 最大掉落组个数
const MaxCount = 30

func newDropGroupInfoManager(xmlData *XmlData) *DropGroupInfoManager {
	m := &DropGroupInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DropGroupInfoManager) name() string {
	return "DropGroupInfo"
}

func (m *DropGroupInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DropGroupInfoManager) checkData() error {
	return nil
}

func (m *DropGroupInfoManager) load(dir string, isShow bool) error {
	tmp := &DropGroupInfos{}
	fileName := filepath.Join(dir, "drop_group_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*DropGroupInfoExt, len(tmp.Datas))
	m.Groups = make(map[uint32][]*DropGroupInfoExt)
	numCheck := make(map[uint32]uint32)
	for _, data := range tmp.Datas {
		if data.Random != TypeDrop && data.Random != TypeRandom {
			panic(fmt.Sprintf("load config %s fail: Random illegal. id:%d", fileName, data.Id))
		}

		if data.Chance == 0 || data.Count == 0 {
			panic(fmt.Sprintf("load config %s fail: %d Chance or Count is 0", fileName, data.Id))
		}

		if _, exist := checkExist(m.xmlData, data.DropId); !exist {
			panic(fmt.Sprintf("load config %s fail: %d drop id:%d not exist 0", fileName, data.Id, data.DropId))
		}

		dataExt := &DropGroupInfoExt{
			Id:         data.Id,
			GroupId:    data.GroupId,
			DropId:     data.DropId,
			DropType:   data.Random,
			Chance:     float64(data.Chance),
			ChangeAdd:  float64(data.ChanceAdd),
			Count:      float64(data.Count),
			FixedCount: data.FixedCount,
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}
		m.Groups[data.GroupId] = append(m.Groups[data.GroupId], dataExt)
		numCheck[data.GroupId] += data.Count
		if numCheck[data.GroupId] > MaxCount {
			panic(fmt.Sprintf("load config %s fail. group:%d, total count > MaxCount", fileName, data.GroupId))
		}
	}
	return nil
}

func (m *DropGroupInfoManager) GetRecordById(id uint32) *DropGroupInfoExt {
	return m.Datas[id]
}

func (m *DropGroupInfoManager) Index(key uint32) *DropGroupInfoExt {
	return m.Datas[key]
}

func (m *DropGroupInfoManager) Group(key uint32) []*DropGroupInfoExt {
	return m.Groups[key]
}

// 计算随机次数
func CalcRandomNum(rd *rand.Rand, num float64) uint32 {
	intBase := uint32(num)
	intExt := uint32(math.Round((num - float64(intBase)) * BaseFloat))
	if uint32(rd.RandBetween(1, BaseInt)) <= intExt {
		intBase++
	}
	return intBase
}

// 获取真随机掉落资源
func GetRealDropAwards(xmlData *XmlData, rd *rand.Rand, dropID, dropType, num uint32) ([]*cl.Resource, bool) {
	awards := make([]*cl.Resource, 0, num)
	tmp := make([]*cl.Resource, 0, 1)
	var flag bool
	for i := uint32(0); i < num; i++ {
		switch dropType {
		case TypeDrop:
			tmp, flag = RealSureDrop(xmlData, rd, dropID)
		case TypeRandom:
			tmp, flag = RealRandomDrop(xmlData, rd, dropID)
		}

		if !flag {
			l4g.Error("calc awards failed. dropID:%d dropType:%d", dropID, dropType)
			return nil, false
		}
		awards = append(awards, tmp...)
	}
	return awards, true
}

// 获取真随机掉落资源
func GetExpectDropAwards(xmlData *XmlData, rd *rand.Rand, dropID, dropType uint32, num float64) ([]*cl.Resource, bool) {
	awards := make([]*cl.Resource, 0, int(num))
	tmp := make([]*cl.Resource, 0, 1)
	var flag bool
	switch dropType {
	case TypeDrop:
		tmp, flag = ExpectSureDrop(xmlData, rd, dropID, num)
	case TypeRandom:
		tmp, flag = ExpectRandomDrop(xmlData, rd, dropID, num)
	}

	if !flag {
		l4g.Error("calc awards failed. %d", dropType)
		return nil, false
	}
	awards = append(awards, tmp...)
	return awards, true
}

// 真随机掉落 - 注意：有可能什么都没随机到，返回空
func realGroupDrop(xmlData *XmlData, rd *rand.Rand, groupID uint32) ([]*cl.Resource, bool) {
	info := xmlData.DropGroupInfoM.Group(groupID)
	if info == nil {
		l4g.Error("drop group info not exist. %d", groupID)
		return nil, false
	}

	awards := make([]*cl.Resource, 0, len(info))
	for _, v := range info {
		for i := uint32(0); i < uint32(v.Count); i++ {
			//掉落概率小于10000，并且随机未中时，跳出本次循环
			if int(v.Chance) < BaseInt && int(v.Chance) < rd.RandBetween(1, BaseInt) {
				continue
			}

			realNum := uint32(1)
			tmp, flag := GetRealDropAwards(xmlData, rd, v.DropId, v.DropType, realNum)
			if flag {
				awards = append(awards, tmp...)
			}
		}
	}
	return awards, true
}
