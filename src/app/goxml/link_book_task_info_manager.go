package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type LinkBookTaskInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*LinkBookInfoExt

	heroToInfos map[uint32][]*LinkBookInfoExt
}

type LinkBookInfoExt struct {
	Id         uint32 //
	Group      uint32 //int:id组
	LinkBookId uint32 //int:对应羁绊组
	Grade      uint32 //int:任务奖励评分
	HeroGroup  uint32 //int:关联英雄
	Type       uint32 //int:条件类型
	Value      uint32 //int:参数
	Attrs      []*AttrTypeAndValue
}

func newLinkBookTaskInfoManager(xmlData *XmlData) *LinkBookTaskInfoManager {
	m := &LinkBookTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *LinkBookTaskInfoManager) name() string {
	return "LinkBookTaskInfo"
}

func (m *LinkBookTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *LinkBookTaskInfoManager) checkData() error {
	return nil
}

func (m *LinkBookTaskInfoManager) load(dir string, isShow bool) error {
	tmp := &LinkBookTaskInfos{}
	fileName := filepath.Join(dir, "link_book_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*LinkBookInfoExt, len(tmp.Datas))
	}

	m.heroToInfos = make(map[uint32][]*LinkBookInfoExt)

	for _, data := range tmp.Datas {
		data.prepare()

		heroInfo := m.xmlData.HeroInfoM.Index(data.HeroGroup)
		if heroInfo == nil {
			panic(fmt.Sprintf("data check error: hero not exist. fileName:%s id:%d", fileName, data.Id))
		}

		if _, exist := HeroHandbookLinkAttrBit[data.Type]; !exist {
			panic(fmt.Sprintf("data check error: data type error. fileName:%s id:%d", fileName, data.Id))
		}

		if data.Type == HeroHandbookAttrTypeStar {
			if heroInfo.Link1ID != data.LinkBookId {
				panic(fmt.Sprintf("data check error: data type link1 error. fileName:%s id:%d", fileName, data.Id))
			}
		} else if data.Type == HeroHandbookAttrTypeLink2 {
			if heroInfo.Link2ID != data.LinkBookId {
				panic(fmt.Sprintf("data check error: data type link2 error. fileName:%s id:%d", fileName, data.Id))
			}
		} else if data.Type == HeroHandbookAttrTypeLink3 {
			if heroInfo.Link3ID != data.LinkBookId {
				panic(fmt.Sprintf("data check error: data type link3 error. fileName:%s id:%d", fileName, data.Id))
			}
		} else if data.Type == HeroHandbookAttrTypeLink4 {
			if heroInfo.Link4ID != data.LinkBookId {
				panic(fmt.Sprintf("data check error: data type link4 error. fileName:%s id:%d", fileName, data.Id))
			}
		} else if data.Type == HeroHandbookAttrTypeLink5 {
			if m.xmlData.ConfigInfoM.Link5Id != data.LinkBookId {
				panic(fmt.Sprintf("data check error: data type link5 error. fileName:%s id:%d", fileName, data.Id))
			}
		}

		dataExt := &LinkBookInfoExt{}
		dataExt.Id = data.Id
		dataExt.Group = data.Group
		dataExt.LinkBookId = data.LinkBookId
		dataExt.Grade = data.Grade
		dataExt.HeroGroup = data.HeroGroup
		dataExt.Type = data.Type
		dataExt.Value = data.Value
		if data.AttrType1 > 0 && data.AttrValue1 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, GenAttrTypeAndValue(data.AttrType1, data.AttrValue1))
		}
		if data.AttrType2 > 0 && data.AttrValue2 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, GenAttrTypeAndValue(data.AttrType2, data.AttrValue2))
		}
		if data.AttrType3 > 0 && data.AttrValue3 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, GenAttrTypeAndValue(data.AttrType3, data.AttrValue3))
		}

		if data.AttrType4 > 0 && data.AttrValue4 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, GenAttrTypeAndValue(data.AttrType4, data.AttrValue4))
		}

		if data.AttrType5 > 0 && data.AttrValue5 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, GenAttrTypeAndValue(data.AttrType5, data.AttrValue5))
		}

		if data.AttrType6 > 0 && data.AttrValue6 > 0 {
			dataExt.Attrs = append(dataExt.Attrs, GenAttrTypeAndValue(data.AttrType6, data.AttrValue6))
		}

		m.heroToInfos[data.HeroGroup] = append(m.heroToInfos[data.HeroGroup], dataExt)

		if ptr, exist := m.Datas[dataExt.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[dataExt.Id] = dataExt
		}
	}
	return nil
}

func (m *LinkBookTaskInfoManager) GetRecordById(id uint32) *LinkBookInfoExt {
	return m.Datas[id]
}

func (m *LinkBookTaskInfoManager) Index(key uint32) *LinkBookInfoExt {
	return m.Datas[key]
}

func (m *LinkBookTaskInfoManager) GetHeroStarAttrId(heroSysId uint32, star uint32) uint32 {
	for _, info := range m.heroToInfos[heroSysId] {
		if info.Type == HeroHandbookAttrTypeStar && info.Value == star {
			return info.Id
		}
	}
	return 0
}

func (m *LinkBookTaskInfoManager) GetHeroLink2AttrId(heroSysId uint32) uint32 {
	for _, info := range m.heroToInfos[heroSysId] {
		if info.Type == HeroHandbookAttrTypeLink2 {
			return info.Id
		}
	}
	return 0
}

func (m *LinkBookTaskInfoManager) GetHeroLink3AttrId(heroSysId uint32) uint32 {
	for _, info := range m.heroToInfos[heroSysId] {
		if info.Type == HeroHandbookAttrTypeLink3 {
			return info.Id
		}
	}
	return 0
}

func (m *LinkBookTaskInfoManager) GetHeroLink4AttrId(heroSysId uint32) uint32 {
	for _, info := range m.heroToInfos[heroSysId] {
		if info.Type == HeroHandbookAttrTypeLink4 {
			return info.Id
		}
	}
	return 0
}

func (m *LinkBookTaskInfoManager) GetHeroLink5AttrId(heroSysId uint32) uint32 {
	for _, info := range m.heroToInfos[heroSysId] {
		if info.Type == HeroHandbookAttrTypeLink5 {
			return info.Id
		}
	}
	return 0
}
