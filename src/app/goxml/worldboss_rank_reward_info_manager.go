package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type WorldbossRankRewardInfoManager struct {
	xmlData    *XmlData
	Datas      map[uint32]*WorldbossRankRewardInfo
	rankAwards map[uint32]map[uint32]map[uint32][]*WorldbossRankRewardInfo // key:Boss类型 -> key: 榜单类型  -> key: 难度等级
}

func newWorldbossRankRewardInfoManager(xmlData *XmlData) *WorldbossRankRewardInfoManager {
	m := &WorldbossRankRewardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *WorldbossRankRewardInfoManager) name() string {
	return "WorldbossRankRewardInfo"
}

func (m *WorldbossRankRewardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *WorldbossRankRewardInfoManager) checkData() error {
	return nil
}

func (m *WorldbossRankRewardInfoManager) load(dir string, isShow bool) error {
	tmp := &WorldbossRankRewardInfos{}
	fileName := filepath.Join(dir, "worldboss_rank_reward_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*WorldbossRankRewardInfo, len(tmp.Datas))
	}
	m.rankAwards = make(map[uint32]map[uint32]map[uint32][]*WorldbossRankRewardInfo)
	for _, data := range tmp.Datas {
		data.prepare()

		if _, exist := m.rankAwards[data.BossType]; !exist {
			m.rankAwards[data.BossType] = make(map[uint32]map[uint32][]*WorldbossRankRewardInfo)
		}

		if _, exist := m.rankAwards[data.BossType][data.Rank]; !exist {
			m.rankAwards[data.BossType][data.Rank] = make(map[uint32][]*WorldbossRankRewardInfo)
		}

		m.rankAwards[data.BossType][data.Rank][data.Type] = append(m.rankAwards[data.BossType][data.Rank][data.Type], data)

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *WorldbossRankRewardInfoManager) GetRecordById(id uint32) *WorldbossRankRewardInfo {
	return m.Datas[id]
}

func (m *WorldbossRankRewardInfoManager) Index(key uint32) *WorldbossRankRewardInfo {
	return m.Datas[key]
}

func (m *WorldbossRankRewardInfoManager) GetRankAward(bossType, rankType, level, rank uint32) []*cl.Resource {
	infos := m.rankAwards[bossType][rankType][level]
	for _, info := range infos {
		// 应排名配表原因，获取数据时，需要倒过来
		if rank >= info.RankMax && rank <= info.RankMin {
			return copyAward(info.ClRes)
		}
	}

	return nil
}

func copyAward(awards []*cl.Resource) []*cl.Resource {
	copys := make([]*cl.Resource, 0, len(awards))
	for _, award := range awards {
		copys = append(copys, award.Clone())
	}

	return copys
}
