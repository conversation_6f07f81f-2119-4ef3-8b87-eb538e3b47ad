package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type BuffInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*BuffInfo
}

func newBuffInfoManager(xmlData *XmlData) *BuffInfoManager {
	m := &BuffInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *BuffInfoManager) name() string {
	return "BuffInfo"
}

func (m *BuffInfoManager) ignoreForCheck() bool {
	return false
}

func (m *BuffInfoManager) checkData() error {
	return nil
}

func (m *BuffInfoManager) load(dir string, isShow bool) error {
	tmp := &BuffInfos{}
	fileName := filepath.Join(dir, "buff_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*BuffInfo, len(tmp.Datas))

	for _, data := range tmp.Datas {
		data.prepare()
		if data.Condition != 0 {
			if m.xmlData.BuffConditionInfoM.Index(data.Condition) == nil {
				panic(fmt.Sprintf("load config %s,BuffConditionInfo cant find %d", fileName, data.Condition))
			}
		}
		m.Datas[data.Id] = data
	}
	return nil
}

func (m *BuffInfoManager) GetRecordById(id uint32) *BuffInfo {
	return m.Datas[id]
}

func (m *BuffInfoManager) Index(key uint32) *BuffInfo {
	return m.Datas[key]
}
