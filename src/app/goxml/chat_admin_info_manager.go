package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ChatAdminInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ChatAdminInfo
}

func newChatAdminInfoManager(xmlData *XmlData) *ChatAdminInfoManager {
	m := &ChatAdminInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ChatAdminInfoManager) name() string {
	return "ChatAdminInfo"
}

func (m *ChatAdminInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ChatAdminInfoManager) checkData() error {
	return nil
}

func (m *ChatAdminInfoManager) load(dir string, isShow bool) error {
	tmp := &ChatAdminInfos{}
	fileName := filepath.Join(dir, "chat_admin_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ChatAdminInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()

		if data.ChannelId != ChatLocalWorld && data.ChannelId != ChatGuild && data.ChannelId != ChatPartition &&
			data.ChannelId != ChatGuildDungeon && data.ChannelId != ChatGST && data.ChannelId != ChatSystemGuild {
			panic(fmt.Sprintf("load config %s fail: channelID is invalid. type: %d channelID: %d", fileName, data.Type, data.ChannelId))
		}
		if ptr, exist := m.Datas[data.Type]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Type] = data
		}
	}
	return nil
}

func (m *ChatAdminInfoManager) GetRecordByType(t uint32) *ChatAdminInfo {
	return m.Datas[t]
}

func (m *ChatAdminInfoManager) Index(key uint32) *ChatAdminInfo {
	return m.Datas[key]
}

func (m *ChatAdminInfoManager) GetMsgTypeFromSummon(summonType uint32, isPlural bool) (msgType uint32) {
	switch summonType {
	case BasicSummon:
		if isPlural {
			msgType = ChatTypeBasicSummonMulti
		} else {
			msgType = ChatTypeBasicSummonSingle
		}
	case AdvancedSummon:
		if isPlural {
			msgType = ChatTypeAdvancedSummonMulti
		} else {
			msgType = ChatTypeAdvancedSummonSingle
		}
	case ArtifactSummon:
		if isPlural {
			msgType = ChatTypeArtifactSummonMulti
		} else {
			msgType = ChatTypeArtifactSummonSingle
		}
	case ArtifactPoinsSummon:
		msgType = ChatTypeArtifactPointSummon
	case LinkSummon:
		if isPlural {
			msgType = ChatTypeLinkSummonMulti
		} else {
			msgType = ChatTypeLinkSummonSingle
		}
	}

	return
}
