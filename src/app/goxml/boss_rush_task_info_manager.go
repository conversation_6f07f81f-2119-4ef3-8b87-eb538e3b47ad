package goxml

import (
	"app/protos/out/cl"
	"errors"
	"fmt"
)

type BossRushTaskInfoMEx struct {
	*BossRushTaskInfoM
	AllEvent map[uint32]struct{}
	TaskNum  int
}

func newBossRushTaskInfoMEx(xmlData *XmlData) *BossRushTaskInfoMEx {
	m := &BossRushTaskInfoMEx{
		BossRushTaskInfoM: &BossRushTaskInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *BossRushTaskInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *BossRushTaskInfoMEx) load(dir string, isShow bool) error {
	err := m.BossRushTaskInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	if m.AllEvent == nil {
		m.AllEvent = make(map[uint32]struct{})
	}

	m.TaskNum = len(m.idRecordMap)
	for _, v := range m.idRecordMap {
		taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(v.TypeId)
		if taskTypeInfo == nil {
			return errors.Join(fmt.Errorf("BossRushTaskInfoMEx no taskTypeInfo. v.TypeId:%d", v.TypeId), err)
		}
		m.AllEvent[taskTypeInfo.Type] = struct{}{}

		// 奖励
		if v.Type1 > 0 && v.Count1 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type1,
				Value: v.Value1,
				Count: v.Count1,
			})
		}
		if v.Type2 > 0 && v.Count2 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type2,
				Value: v.Value2,
				Count: v.Count2,
			})
		}
		if v.Type3 > 0 && v.Count3 > 0 {
			v.Rewards = append(v.Rewards, &cl.Resource{
				Type:  v.Type3,
				Value: v.Value3,
				Count: v.Count3,
			})
		}
	}
	return nil
}

func (m *BossRushTaskInfoMEx) checkData() error {
	err := m.BossRushTaskInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}

func (m *BossRushTaskInfoMEx) GetAllEvent() map[uint32]struct{} {
	return m.AllEvent
}

func (m *BossRushTaskInfoMEx) GetTaskNum() int {
	return m.TaskNum
}
