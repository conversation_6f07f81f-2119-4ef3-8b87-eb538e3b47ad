package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/math/rand"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type DivineDemonGroupInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*DivineDemonGroupInfoExt
}

func newDivineDemonGroupInfoManager(xmlData *XmlData) *DivineDemonGroupInfoManager {
	m := &DivineDemonGroupInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DivineDemonGroupInfoManager) name() string {
	return "divine_demon_group_info.xml"
}

func (m *DivineDemonGroupInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DivineDemonGroupInfoManager) checkData() error {
	return nil
}

type DivineDemonGroupInfoExt struct {
	*DivineDemonGroupInfo
	guaranteeGroup map[int]uint32
}

func (m *DivineDemonGroupInfoManager) load(dir string, show bool) error {
	tmp := &DivineDemonGroupInfos{}
	fileName := filepath.Join(dir, "divine_demon_group_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*DivineDemonGroupInfoExt, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		dataExt := &DivineDemonGroupInfoExt{
			DivineDemonGroupInfo: data,
			guaranteeGroup:       make(map[int]uint32),
		}
		dataExt.guaranteeGroup[DivineDemonOrangeFragmentGuaranteeIndex] = data.GroupFragmentId50
		dataExt.guaranteeGroup[DivineDemonPurpleGuaranteeIndex] = data.GroupId40
		dataExt.guaranteeGroup[DivineDemonOrangeGuaranteeIndex] = data.GroupId50
		dataExt.guaranteeGroup[DivineDemonRedGuaranteeIndex] = data.GroupId60
		if _, exist := m.Datas[data.Id]; exist {
			panic(fmt.Sprintf("config:%s", fileName))
		}
		m.Datas[data.Id] = dataExt
	}
	return nil
}

func (m *DivineDemonGroupInfoManager) Index(key uint32) *DivineDemonGroupInfoExt {
	return m.Datas[key]
}

func (m *DivineDemonGroupInfoManager) RandomGroup(rd *rand.Rand, colorGuarantee []uint32, id uint32) (uint32, bool, bool, uint32) {
	if len(colorGuarantee) != DivineDemonMaxGuaranteeLen {
		l4g.Errorf("DivineDemonGroupInfoManager RandomGroup: colorGuarantee len is error")
		return 0, false, false, 0
	}

	data, exist := m.Datas[id]
	if !exist {
		l4g.Errorf("DivineDemonGroupInfoManager RandomGroup: id id:%d error", id)
		return 0, false, false, 0
	}

	weightIds := make([]*WeightAndIdRed, 0, 5)
	var totalWeight uint32
	for i := DivineDemonMaxGuaranteeLen - 1; i >= 0; i-- {
		if m.xmlData.DivineDemonConfigInfoM.Guarantee(i, colorGuarantee[i]) {
			return data.guaranteeGroup[i], true, data.guaranteeGroup[i] == data.GroupId60, data.GuaranteedDropGroupClass
		} else {
			weight := m.xmlData.DivineDemonConfigInfoM.GetWeight(i, colorGuarantee[i])
			weightIds = append(weightIds, &WeightAndIdRed{
				Weight: weight,
				Id:     data.guaranteeGroup[i],
				IsRed:  data.guaranteeGroup[i] == data.GroupId60,
			})
			totalWeight += weight
		}
	}

	if totalWeight < uint32(BaseInt) {
		weightIds = append(weightIds, &WeightAndIdRed{
			Weight: uint32(BaseInt) - totalWeight,
			Id:     data.GroupFragmentId40,
			IsRed:  false,
		})
		totalWeight = uint32(BaseInt)
	}

	for _, weight := range weightIds {
		l4g.Debugf("weightIds weight:%+v", weight)
	}

	randNum := uint32(rd.RandBetween(1, int(totalWeight)))
	for _, weight := range weightIds {
		if weight.Weight < randNum {
			randNum -= weight.Weight
			continue
		}
		return weight.Id, false, weight.IsRed, data.GuaranteedDropGroupClass
	}

	return 0, false, false, 0
}
