package goxml

import (
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"

	"app/protos/out/cl"
	"app/protos/out/common"

	l4g "github.com/ivanabc/log4go"
)

const (
	DropIndependentRand = 1 // 独立随机
	DropExclusiveRand   = 2 // n选1
)

// 法宝掉落类型对应stage
// 碎片掉落类型对应type
var DropTypeRandEnum = map[common.DROP_TYPE]uint32{}

type DropInfoUnit struct {
	Type    uint32
	Value   uint32
	MinSize uint32
	MaxSize uint32
	Percent uint32
}

type DropInfoExt struct {
	*DropInfo
	units []DropInfoUnit
}

type DropInfoManager struct {
	xmlData *XmlData
	index   map[uint32]*DropInfoExt
}

// todo 优化， 去掉一些为0的
func newDropInfoManager(xmlData *XmlData) *DropInfoManager {
	m := &DropInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DropInfoManager) name() string {
	return "DropInfo"
}

func (m *DropInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DropInfoManager) checkData() error {
	return nil
}

func (m *DropInfoManager) load(dir string, isShow bool) error {
	tmp := &DropInfos{}
	fileName := filepath.Join(dir, "drop_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.index = make(map[uint32]*DropInfoExt, len(tmp.Datas))
	for _, data := range tmp.Datas {
		units := make([]DropInfoUnit, 0, 5)
		if data.Chance1 > 0 {
			unit := DropInfoUnit{
				Type:    data.Type1,
				Value:   data.Value1,
				MinSize: data.Min1,
				MaxSize: data.Max1,
				Percent: data.Chance1,
			}
			units = append(units, unit)
		}
		if data.Chance2 > 0 {
			unit := DropInfoUnit{
				Type:    data.Type2,
				Value:   data.Value2,
				MinSize: data.Min2,
				MaxSize: data.Max2,
				Percent: data.Chance2,
			}
			units = append(units, unit)
		}
		if data.Chance3 > 0 {
			unit := DropInfoUnit{
				Type:    data.Type3,
				Value:   data.Value3,
				MinSize: data.Min3,
				MaxSize: data.Max3,
				Percent: data.Chance3,
			}
			units = append(units, unit)
		}
		if data.Chance4 > 0 {
			unit := DropInfoUnit{
				Type:    data.Type4,
				Value:   data.Value4,
				MinSize: data.Min4,
				MaxSize: data.Max4,
				Percent: data.Chance4,
			}
			units = append(units, unit)
		}
		if data.Chance5 > 0 {
			unit := DropInfoUnit{
				Type:    data.Type5,
				Value:   data.Value5,
				MinSize: data.Min5,
				MaxSize: data.Max5,
				Percent: data.Chance5,
			}
			units = append(units, unit)
		}

		m.index[data.Id] = &DropInfoExt{
			DropInfo: data,
			units:    units,
		}
	}
	return nil
}

func (m *DropInfoManager) GetRecordById(id uint32) *DropInfoExt {
	return m.index[id]
}

func (m *DropInfoManager) Index(key uint32) *DropInfoExt {
	return m.index[key]
}

func (m *DropInfoManager) CheckData() {
	for _, info := range m.index {
		// 检查0配置
		for i, unit := range info.units {
			if unit.Percent > 0 && unit.Type > 0 {
				if unit.MinSize > unit.MaxSize {
					panic(fmt.Sprintf("data/drop_info.xml error, drop id %d, data 0 while percent > 0, unit %d", info.Id, i+1))
				}
			}
		}

		// 检查9999的嵌套深度
		// common.DROP_TYPE_DT_RECURSION = 9999 表示嵌套掉落
		for _, unit := range info.units {
			if unit.Type == uint32(common.DROP_TYPE_DT_RECURSION) {
				subInfo := m.Index(unit.Value)
				if subInfo == nil {
					panic(fmt.Sprintf("data/drop_info.xml error, drop id: %d, drop value %d nil", info.Id, unit.Value))
				}
				for _, subUnit := range subInfo.units {
					if subUnit.Type == uint32(common.DROP_TYPE_DT_RECURSION) {
						gubInfo := m.Index(subUnit.Value)
						if gubInfo == nil {
							panic(fmt.Sprintf("data/drop_info.xml error, drop id: %d, drop value %d nil", info.Id, unit.Value))
						}
						for _, gubUnit := range gubInfo.units {
							if gubUnit.Type == uint32(common.DROP_TYPE_DT_RECURSION) {
								panic(fmt.Sprintf("data/drop_info.xml error, drop id %d, drop recursive too deep", info.Id))
							}
						}
					}
				}
			}
		}

		// 检查总的掉落概率
		if info.Random == DropExclusiveRand {
			total := uint32(0)
			for _, unit := range info.units {
				total += unit.Percent
			}
			if total != 10000 {
				panic(fmt.Sprintf("data/drop_info.xml error, drop id %d, total percent not 10000", info.Id))
			}
		}
	}
}

// 从掉落表drop_info生成掉落资源
// 连续超过100次的掉落 请调用DropMulti, 并与策划确认
func (m *DropInfoManager) Drop(id uint32, rd *rand.Rand) ([]*cl.Resource, bool) {
	info := m.xmlData.DropInfoM.Index(id)
	if info == nil {
		l4g.Error("Drop Id Nil %d", id)
		return nil, false
	}
	if info.Random == DropIndependentRand {
		// 各个配置项按独立概率 掉落
		var res []*cl.Resource
		for _, unit := range info.units {
			if unit.Percent > 0 && uint32(rd.RandBetween(0, 9999)) < unit.Percent {
				subs := m.dropUnit(unit, rd, unit.Percent != 10000)
				res = append(res, subs...)
			}
		}
		return res, true
	}
	if info.Random == DropExclusiveRand {
		// 各个配置项只掉落其中一项
		percent := uint32(rd.RandBetween(0, 9999))
		for _, unit := range info.units {
			if unit.Percent > 0 && percent < unit.Percent {
				return m.dropUnit(unit, rd, unit.Percent != 10000), true
			}
			percent -= unit.Percent
		}
		l4g.Error("DropExclusiveRand Error %d", id)
	}
	return nil, false
}

func (m *DropInfoManager) dropUnit(unit DropInfoUnit, rd *rand.Rand, isRand bool) []*cl.Resource {
	if unit.MinSize == 0 && unit.MaxSize == 0 {
		return nil
	}
	var res []*cl.Resource
	switch common.DROP_TYPE(unit.Type) {
	case 0:
		return nil
	case common.DROP_TYPE_DT_RECURSION:
		res, _ = m.Drop(unit.Value, rd)
	default:
		res = []*cl.Resource{{
			Type:  unit.Type,
			Value: unit.Value,
			Count: uint32(rd.RandBetween(int(unit.MinSize), int(unit.MaxSize))),
		}}
	}
	return res
}

// 从掉落表drop_info生成掉落资源
// 多次Drop，例如达到100次以上，为了性能考虑，计算期望结果
// 调用本函数的功能请与策划确认
func (m *DropInfoManager) DropMulti(id uint32, rd *rand.Rand, multi uint32) ([]*cl.Resource, bool) {
	info := m.xmlData.DropInfoM.Index(id)
	if info == nil {
		l4g.Error("Drop Id Nil %d", id)
		return nil, false
	}
	var res []*cl.Resource
	totalUnitMulti := uint32(0)
	for i, unit := range info.units {
		percent := unit.Percent * multi
		unitMulti := percent / 10000
		if subPer := percent % 10000; subPer > 0 {
			if uint32(rd.RandBetween(0, 9999)) < subPer {
				unitMulti++
			}
		}
		if info.Random == DropExclusiveRand {
			if multi > totalUnitMulti {
				if totalUnitMulti+unitMulti > multi {
					unitMulti = multi - totalUnitMulti
				}
				if i == len(info.units)-1 {
					unitMulti = multi - totalUnitMulti
				}
			} else {
				unitMulti = 0
			}
		}
		if unitMulti > 0 {
			subs := m.dropUnitMulti(unit, rd, unitMulti, unit.Percent != 10000)
			res = append(res, subs...)
		}
		totalUnitMulti += unitMulti
	}
	return res, true
}

// 多次Drop，例如达到100次以上，为了性能考虑，计算期望结果
func (m *DropInfoManager) dropUnitMulti(unit DropInfoUnit, rd *rand.Rand, multi uint32, isRand bool) []*cl.Resource {
	if unit.MinSize == 0 && unit.MaxSize == 0 {
		return nil
	}
	var res []*cl.Resource
	switch common.DROP_TYPE(unit.Type) {
	case 0:
		return nil
	case common.DROP_TYPE_DT_RECURSION:
		res, _ = m.DropMulti(unit.Value, rd, multi)
	default:
		res = []*cl.Resource{{
			Type:  unit.Type,
			Value: unit.Value,
			Count: uint32(rd.RandBetween(int(unit.MinSize*multi), int(unit.MaxSize*multi))),
		}}
	}
	return res
}
