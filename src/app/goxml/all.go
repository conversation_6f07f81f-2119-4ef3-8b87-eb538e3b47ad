package goxml

import (
	"fmt"
	"sync/atomic"

	l4g "github.com/ivanabc/log4go"
)

type manager interface {
	name() string
	ignoreForCheck() bool
	load(dir string, isShow bool) error
	checkData() error
}

var gData atomic.Pointer[XmlData]

type XmlData struct {
	managers                     []manager
	managersMap                  map[string]manager
	ServerInfoM                  *ServerInfo
	ConfigInfoM                  *ConfigInfoManager
	LoginLimitInfoM              *LoginLimitInfoManager
	PreciousResInfoM             *PreciousResInfoManager
	XorKeyM                      *XorKeyManager
	ResourceTypeInfoM            *ResourceTypeInfoManager
	ItemSelectiveGroupInfoM      *ItemSelectiveGroupInfoManager
	ItemInfoM                    *ItemInfoManager
	FragmentInfoM                *FragmentInfoManager
	ItemTokenInfoM               *ItemTokenInfoManager
	TaskTypeInfoM                *TaskTypeInfoManager
	TaskInfoM                    *TaskInfoManager
	BuyPriceInfoM                *BuyPriceInfoManager
	SkillInfoM                   *SkillInfoManager
	SkillLevelInfoM              *SkillLevelInfoManager
	PassiveSkillProbabilityInfoM *PassiveSkillProbabilityInfoManager
	PassiveSkillInfoM            *PassiveSkillInfoManager
	BuffTypeInfoM                *BuffTypeInfoManager
	BuffInfoM                    *BuffInfoManager
	BattleParaInfoM              *BattleParaInfoManager
	FunctionInfoM                *FunctionInfoManager
	NewDropInfoM                 *NewDropInfoManager
	DropGroupInfoM               *DropGroupInfoManager
	DropInfoM                    *DropInfoManager
	DungeonFirstDropInfoM        *DungeonFirstDropInfoManager
	DungeonConfigInfoM           *DungeonConfigInfoManager
	PlayerLevelInfoM             *PlayerLevelInfoManager
	HeroInfoM                    *HeroInfoManager
	HeroDataInfoM                *HeroDataInfoManager
	HeroLevelInfoM               *HeroLevelInfoManager
	HeroStageInfoM               *HeroStageInfoManager
	HeroStarInfoM                *HeroStarInfoManager
	GameInitialInfoM             *GameInitialInfoManager
	MonsterGroupInfoM            *MonsterGroupInfoManager
	MonsterInfoM                 *MonsterInfoManager
	MonsterInfo7M                *MonsterInfo7Manager
	MonsterInfo40M               *MonsterInfo40Manager
	MonsterInfo55M               *MonsterInfo55Manager
	RaceBuffInfoM                *RaceBuffInfoManager
	ArtifactInfoM                *ArtifactInfoManager
	ArtifactFragmentInfoM        *ArtifactFragmentInfoManager
	SummonClassInfoM             *SummonClassInfoManager
	SummonGroupInfoM             *SummonGroupInfoManager
	SummonTypeInfoM              *SummonTypeInfoManager
	SummonSpecialClassInfoM      *SummonSpecialClassInfoManager
	MailInfoM                    *MailInfoManager
	ShopRandomGoodsInfoM         *ShopRandomGoodsInfoManager
	ShopRegularGoodsInfoM        *ShopRegularGoodsInfoManager
	ShopRandomInfoM              *ShopRandomInfoManager
	ShopInfoM                    *ShopInfoManager
	EquipInfoM                   *EquipInfoManager
	EquipAttrCoeInfoM            *EquipAttrCoeInfoManager
	EquipStrengthInfoM           *EquipStrengthInfoManager
	EquipRefineInfoM             *EquipRefineInfoManager
	EquipRefineTechniqueInfoM    *EquipRefineTechniqueInfoManager
	EquipEnchantInfoM            *EquipEnchantInfoManager
	EquipEnchantMasterInfoM      *EquipEnchantMasterInfoManager
	EquipEvolutionInfoM          *EquipEvolutionInfoManager
	EquipEnchantRandInfoM        *EquipEnchantRandInfoManager
	RankingInfoM                 *RankingInfoManager
	TowerInfoM                   *TowerInfoManager
	ArenaMatchInfoM              *ArenaMatchInfoManager
	ArenaFightRewardInfoM        *ArenaFightRewardInfoManager
	ArenaBotInfoM                *ArenaBotInfoManager
	ArenaPraiseInfoM             *ArenaPraiseInfoManager
	NumberTypeInfoM              *NumberTypeInfoManager
	ArtifactStarInfoM            *ArtifactStarInfoManager
	ArtifactForgeInfoM           *ArtifactForgeInfoManager
	ArtifactStrengthInfoM        *ArtifactStrengthInfoManager
	ArtifactTalentInfoM          *ArtifactTalentInfoManager
	ArtifactSkillInfoM           *ArtifactSkillInfoManager
	GemComposeInfoM              *GemComposeInfoManager
	GemInfoM                     *GemInfoManager
	GemAttrInfoM                 *GemAttrInfoManager
	AvatarInfoM                  *AvatarInfoManager
	AvatarItemInfoM              *AvatarItemInfoManager
	TrialInfoM                   *TrialInfoManager
	EmblemConfigInfoM            *EmblemConfigInfoManager
	EmblemLevelInfoM             *EmblemLevelInfoManager
	EmblemInfoM                  *EmblemInfoManager
	EmblemSuitInfoM              *EmblemSuitInfoManager
	EmblemHeroGroupInfoM         *EmblemHeroGroupInfoMEx
	EmblemMagicSkillInfoM        *EmblemMagicSkillInfoManager
	EmblemFragmentInfoM          *EmblemFragmentInfoManager
	EmblemCustomizeInfoM         *EmblemCustomizeInfoManager
	EmblemCustomizeHeroInfoM     *EmblemCustomizeHeroInfoManager
	EmblemCustomizeTypeInfoM     *EmblemCustomizeTypeInfoManager
	EmblemAscendInfoM            *EmblemAscendInfoManager
	GoldBuyRefreshInfoM          *GoldBuyRefreshInfoManager
	GoldBuyInfoM                 *GoldBuyInfoManager
	DispatchInfoM                *DispatchInfoManager
	DispatchGroupInfoM           *DispatchGroupInfoManager
	DispatchLevelInfoM           *DispatchLevelInfoManager
	HeroAwakeInfoM               *HeroAwakeInfoManager
	RankingAchievementInfoM      *RankingAchievementInfoManager
	MazeConstantInfoM            *MazeConstantInfoManager
	MazeMapInfoM                 *MazeMapInfoManager
	MazeDifficultyInfoM          *MazeDifficultyInfoManager
	MazeBuffInfoM                *MazeBuffInfoManager
	MazeQuestionInfoM            *MazeQuestionInfoManager
	MazeAnswerInfoM              *MazeAnswerInfoManager
	MazeScrollInfoM              *MazeScrollInfoManager
	MonsterDataInfoM             *MonsterDataInfoManager
	MirageCopyInfoM              *MirageCopyInfoManager
	MirageAffixInfoM             *MirageAffixInfoManager
	MirageAffixGroupInfoM        *MirageAffixGroupInfoManager
	MirageHurdleInfoM            *MirageHurdleInfoManager
	GuildConfigInfoM             *GuildConfigInfoManager
	GuildLevelInfoM              *GuildLevelInfoManager
	GuildLogInfoM                *GuildLogInfoManager
	GuildBadgeInfoM              *GuildBadgeInfoManager
	BelltowerConfigInfoM         *BelltowerConfigInfoManager
	TalesInfoM                   *TalesInfoManager
	TalesDungeonInfoM            *TalesDungeonInfoManager
	TalesEliteInfoM              *TalesEliteInfoManager
	MemoryChipInfoM              *MemoryChipInfoManager
	GuildDungeonChapterRewardM   *GuildDungeonChapterRewardManager
	GuildDungeonRewardInfoM      *GuildDungeonRewardInfoManager
	GuildDungeonChapterInfoM     *GuildDungeonChapterInfoManager
	GuildDungeonTaskInfoM        *GuildDungeonTaskInfoManager
	GuildBossHpInfoM             *GuildBossHpInfoManager
	GuildDungeonRankRewardInfoM  *GuildDungeonRankRewardInfoManager
	BotHeadInfoM                 *BotHeadInfoManager
	PassiveSkillEffectM          *PassiveSkillEffectInfoManager
	PassiveSkillTriggerM         *PassiveSkillTriggerInfoManager
	GuildTalentInfoM             *GuildTalentInfoManager
	GuidanceInfoM                *GuidanceInfoManager
	GuidanceSkipConfigInfoM      *GuidanceSkipConfigInfoManager
	FormationInfoM               *FormationInfoManager
	MasterDataInfoM              *MasterDataInfoManager
	CarnivalInfoM                *CarnivalInfoManager
	CarnivalTaskInfoM            *CarnivalTaskInfoManager
	GodPresentChangeInfoM        *GodPresentChangeInfoManager
	GodPresentClassInfoM         *GodPresentClassInfoManager
	GodPresentGroupInfoM         *GodPresentGroupInfoManager
	GodPresentRecoveryInfoM      *GodPresentRecoveryInfoManager
	GodPresentConfigInfoM        *GodPresentConfigInfoManager
	GodPresentInfoM              *GodPresentInfoManager
	MazeBotInfoM                 *MazeBotInfoManager
	SevenDayLoginM               *ActivitySevenDayInfoManager
	MonsterArtifactM             *MonsterArtifactInfoManager
	HeroArchiveInfoM             *HeroArchiveInfoManager
	HeroFetterInfoM              *HeroFetterInfoManager
	MedalInfoM                   *MedalInfoManager
	MedalTaskInfoM               *MedalTaskInfoManager
	MazeBuyReviveInfoM           *MazeBuyReviveInfoManager
	CrystalConfigInfoM           *CrystalConfigInfoManager
	//CrystalEmblemInfoM          *CrystalEmblemInfoManager
	// CrystalGemInfoM           *CrystalGemInfoManager
	ChatInfoM                            *ChatInfoManager
	ForecastInfoM                        *ForecastInfoManager
	ForecastTaskInfoM                    *ForecastTaskInfoManager
	RechargeProductInfoM                 *RechargeProductInfoManager
	RechargeGoodsInfoM                   *RechargeGoodsInfoManager
	RechargeCouponInfoM                  *RechargeCouponInfoManager
	LinkSkillInfoM                       *LinkSkillInfoManager
	TowerConfigInfoM                     *TowerConfigInfoManager
	VipInfoM                             *VipInfoManager
	VipPrivilegeInfoM                    *VipPrivilegeInfoManager
	ActivityRechargeShopInfoM            *ActivityRechargeShopInfoManager
	ActivityRechargeGiftInfoM            *ActivityRechargeGiftInfoManager
	TrialConfigInfoM                     *TrialConfigInfoManager
	CrystalAchieveInfoM                  *CrystalAchieveInfoManager
	FirstRechargeInfoM                   *FirstRechargeInfoManager
	TowerstarConditionInfoM              *TowerstarConditionInfoManager
	TowerstarRewardInfoM                 *TowerstarRewardInfoManager
	TowerstarChapterInfoM                *TowerstarChapterInfoManager
	TowerstarDungeonInfoM                *TowerstarDungeonInfoManager
	TowerstarConfigInfoM                 *TowerstarConfigInfoManager
	MazeGuardianRewardInfoM              *MazeGuardianRewardInfoManager
	MazeTaskInfoM                        *MazeTaskInfoManager
	MazeTaskLevelInfoM                   *MazeTaskLevelInfoManager
	MazeCuresInfoM                       *MazeCuresInfoManager
	ShopBoxRandomInfoM                   *ShopBoxRandomInfoManager
	MazeBoxInfoM                         *MazeBoxInfoManager
	MonthlycardInfoM                     *MonthlycardInfoManager
	PassInfoM                            *PassInfoManager
	PassTaskInfoM                        *PassTaskInfoManager
	ActivityPushGiftInfoM                *ActivityPushGiftInfoManager
	ActivityPushgiftGroupInfoM           *ActivityPushGiftGroupInfoManager
	ActivityPushgiftConfigInfoM          *ActivityPushgiftConfigInfoManager
	BotInfoM                             *BotInfoManager
	ArenaConfigInfoM                     *ArenaConfigInfoManager
	ArenaDivisionInfoM                   *ArenaDivisionInfoManager
	ArenaDivisionTaskInfoM               *ArenaDivisionTaskInfoManager
	ArenaDivisionRewardInfoM             *ArenaDivisionRewardInfoManager
	ShareConfigInfoM                     *ShareConfigInfoManager
	SkillTargetInfoM                     *SkillTargetInfoManager
	WishListConfigInfoM                  *WishlistConfigInfoManager
	WishListGuaranteeInfoM               *WishlistGuaranteeInfoManager
	WishListRangeInfoM                   *WishlistRangeInfoManager
	PassCycleTaskInfoM                   *PassCycleTaskInfoManager
	ActivityTaskTypeInfoM                *ActivityTaskTypeInfoManager
	ChatConfigInfoM                      *ChatConfigInfoManager
	ChatAdminInfoM                       *ChatAdminInfoManager
	BattleSuppressInfoM                  *BattleSuppressInfoManager
	AttributeInfoM                       *AttributeInfoManager
	OldEmblemInfoM                       *OldEmblemInfoManager // 0.9.0-需要删除旧的符文列表
	HeroInitDataInfoM                    *HeroInitDataInfoManager
	ConversionInfoM                      *ConversionInfoManager
	WrestleConfigInfoM                   *WrestleConfigInfoManager
	WrestleBotInfoM                      *WrestleBotInfoManager
	WrestleLevelInfoM                    *WrestleLevelInfoManager
	WrestleRankInfoM                     *WrestleRankInfoManager
	WrestleLevelTaskInfoM                *WrestleLevelTaskInfoManager
	WrestleRewardInfoM                   *WrestleRewardInfoManager
	WrestleRankRewardInfoM               *WrestleRankRewardInfoManager
	GoddessContractBlessInfoM            *GoddessContractBlessInfoManager
	GoddessContractInfoM                 *GoddessContractInfoManager
	GoddessTalesDungeonInfoM             *GoddessTalesDungeonInfoManager
	GoddessContractEliteInfoM            *GoddessContractEliteInfoManager
	GoddessContractTrustInfoM            *GoddessContractTrustInfoManager
	GoddessContractGiftsInfoM            *GoddessContractGiftsInfoManager
	GoddessContractSkinInfoM             *GoddessContractSkinInfoManager
	GoddessContractTreatInfoM            *GoddessContractTreatInfoManager
	ActivityGoddessInfoM                 *ActivityGoddessInfoManager
	VipProductInfoM                      *VipProductInfoManager
	RaisePassiveSkillInfoM               *RaisePassiveSkillInfoManager
	GuildTalentConditionInfoM            *GuildTalentConditionInfoManager
	MazeModelInfoM                       *MazeModelInfoManager
	FlowerOccupyTimberInfoM              *FlowerOccupyTimberInfoManager
	FlowerLvInfoM                        *FlowerLvInfoManager
	FlowerOccupyBaseInfoM                *FlowerOccupyBaseInfoManager
	FlowerOccupyJungleInfoM              *FlowerOccupyJungleInfoManager
	FlowerPlantScoreInfoM                *FlowerPlantScoreInfoManager
	FlowerPlantGoblinInfoM               *FlowerPlantGoblinInfoManager
	FlowerPlantInfoM                     *FlowerPlantInfoManager
	FlowerPlantTypeInfoM                 *FlowerPlantTypeInfoManager
	FlowerPlantRewardInfoM               *FlowerPlantRewardInfoManager
	FlowerConfigInfoM                    *FlowerConfigInfoManager
	FlowerAssistDungeonInfoM             *FlowerAssistDungeonInfoManager
	FlowerMessageInfoM                   *FlowerMessageInfoManager
	GemLevelInfoM                        *GemLevelInfoManager
	MirageFightEffectInfoM               *MirageFightEffectInfoManager
	TrialHeroInfoM                       *TrialHeroInfoManager
	TeamInitDataInfoM                    *TeamInitDataInfoManager
	ActivityMonthInfoM                   *ActivityMonthInfoManager
	ActivityMonthGroupInfoM              *ActivityMonthGroupInfoManager
	ActivityMonthTasksInfoM              *ActivityMonthTasksInfoManager
	DailyWishWeightM                     *DailyWishWeightInfoManager
	DailyWishInfoM                       *DailyWishInfoManager
	DailyWishOpenInfoM                   *DailyWishOpenInfoManager
	ActivityLinkSummonInfoM              *ActivityLinkSummonInfoManager
	LinkSummonInfoM                      *LinkSummonInfoManager
	LinkSummonConfigInfoM                *LinkSummonConfigInfoManager
	ArtifactExchangeInfoM                *ArtifactExchangeInfoManager
	ArtifactDebutConfigInfoM             *ArtifactDebutConfigInfoManager
	ArtifactDebutTimeInfoM               *ArtifactDebutTimeInfoManager
	ArtifactDebutDrawCategoryInfoM       *ArtifactDebutDrawCategoryInfoManager
	ArtifactDebutDrawGroupInfoM          *ArtifactDebutDrawGroupInfoManager
	ArtifactDebutDrawDropInfoM           *ArtifactDebutDrawDropInfoManager
	ArtifactDebutDrawRewardInfoM         *ArtifactDebutDrawRewardInfoManager
	ArtifactDebutLoginRewardInfoM        *ArtifactDebutLoginRewardInfoManager
	ArtifactDebutPuzzleRewardInfoM       *ArtifactDebutPuzzleRewardInfoManager
	ArtifactDebutTaskInfoM               *ArtifactDebutTaskInfoManager
	ArtifactDebutDrawOptionalInfoM       *ArtifactDebutDrawOptionalInfoManager
	ArtifactDebutActivityInfoM           *ArtifactDebutActivityInfoManager
	DivineDemonInfoM                     *DivineDemonInfoManager
	DivineDemonConfigInfoM               *DivineDemonConfigInfoManager
	DivineDemonDefaultInfoM              *DivineDemonDefaultInfoManager
	DivineDemonTaskSummonInfoM           *DivineDemonTaskSummonInfoManager
	DivineDemonTaskActiveInfoM           *DivineDemonTaskActiveInfoManager
	DivineDemonTaskStarInfoM             *DivineDemonTaskStarInfoManager
	DivineDemonGroupInfoM                *DivineDemonGroupInfoManager
	LinkInfoM                            *LinkInfoManager
	NumberMaxInfoM                       *NumberMaxInfoManager
	NumberMaxAddTaskInfoM                *NumberMaxAddTaskInfoManager
	TrialCopyInfoM                       *TrialCopyInfoManager
	GuildCollectiveDonateInfoM           *GuildCollectiveDonateInfoManager
	GuildDonateInfoM                     *GuildDonateInfoManager
	RoundActivitySumInfoM                *RoundActivitySumInfoManager
	RoundActivityTaskInfoM               *RoundActivityTaskInfoManager
	RoundActivityOpenInfoM               *RoundActivityOpenInfoManager
	GoddessCollectionBoxInfoM            *GoddessCollectionBoxInfoManager
	GoddessCollectionInfoM               *GoddessCollectionInfoManager
	ActivityWebGiftInfoM                 *ActivityWebGiftInfoManager
	ArtifactDebutGuaranteeInfoM          *ArtifactDebutGuaranteeInfoManager
	LinkBookTaskInfoM                    *LinkBookTaskInfoManager
	WrestleBuffInfoM                     *WrestleBuffInfoManager
	TowerSeasonTaskInfoM                 *TowerSeasonTaskInfoManager
	TowerSeasonConfigInfoM               *TowerSeasonConfigInfoManager
	TowerSeasonDungeonInfoM              *TowerSeasonDungeonInfoManager
	TowerSeasonFightEffectInfoM          *TowerSeasonFightEffectInfoManager
	HeroExchangeInfoM                    *HeroExchangeInfoManager
	BuffGroupInfoM                       *BuffGroupInfoManager
	SummonActivityCountInfoM             *SummonActivityCountInfoManager
	SummonActivityGuaranteeInfoM         *SummonActivityGuaranteeInfoManager
	ChatLanguageInfoM                    *ChatLanguageInfoManager
	GuildLabelInfoM                      *GuildLabelInfoManager
	GuildDungeonDivisionInoM             *GuildDungeonDivisionInfoManager
	GuildDungeonLevelUpRewardInfoM       *GuildDungeonLevelUpRewardInfoManager
	GuildDungeonSeasonRewardInfoM        *GuildDungeonSeasonRewardInfoManager
	GuildDungeonPointInfoM               *GuildDungeonPointInfoManager
	GuildDungeonWeeklyRewardInfoM        *GuildDungeonWeeklyRewardInfoManager
	MedalSkinInfoM                       *MedalSkinInfoManager
	WebRewardInfoM                       *WebRewardInfoManager
	ArtifactLinkSkillInfoM               *ArtifactLinkSkillInfoManager
	GuildDungeonWeeklyBaseRewardInfoM    *GuildDungeonWeeklyBaseRewardInfoManager
	GuildDungeonArtifactAddInfoM         *GuildDungeonArtifactAddInfoManager
	DropActivityFunctionInfoM            *DropActivityFunctionInfoManager
	DropActivityInfoM                    *DropActivityInfoManager
	DropActivityTypeInfoM                *DropActivityTypeInfoManager
	DropActivityOpenInfoM                *DropActivityOpenInfoManager
	DropActivityOpenExchangeInfoM        *DropActivityOpenExchangeInfoManager
	DailyAttendanceInfoM                 *DailyAttendanceInfoManager
	DailyAttendanceRewardInfoM           *DailyAttendanceRewardInfoManager
	ActivityDailySpecialInfoM            *ActivityDailySpecialInfoManager
	GuildChestInfoM                      *GuildChestInfoManager
	ActivitySumInfoM                     *ActivitySumInfoManager
	WorldbossConfigInfoM                 *WorldbossConfigInfoManager
	WorldbossDifficultyInfoM             *WorldbossDifficultyInfoManager
	WorldbossInfoM                       *WorldbossInfoManager
	WorldbossTaskInfoM                   *WorldbossTaskInfoManager
	WorldbossRankRewardInfoM             *WorldbossRankRewardInfoManager
	LinkBookInfoM                        *LinkBookInfoManager
	TowerSeasonRankRewardInfoM           *TowerSeasonRankRewardInfoManager
	SkinInfoM                            *SkinInfoManager
	GuildDungeonStrategySkillInfoM       *GuildDungeonStrategySkillInfoManager
	GuildDungeonWeekStrategyInfoM        *GuildDungeonWeekStrategyInfoManager
	GuildDungeonSeasonStrategyInfoM      *GuildDungeonSeasonStrategyInfoManager
	ActivityStoryDungeonInfoM            *ActivityStoryDungeonInfoManager
	ActivityStoryInfoM                   *ActivityStoryInfoManager
	ActivityStoryLoginInfoM              *ActivityStoryLoginInfoManager
	ActivityStoryShopInfoM               *ActivityStoryShopInfoManager
	ActivityStoryLayerInfoM              *ActivityStoryLayerInfoManager
	BuffConditionInfoM                   *BuffConditionInfoManager
	AssistanceActivityOpenInfoM          *AssistanceActivityOpenInfoManager
	AssistanceActivityInfoM              *AssistanceActivityInfoManager
	RiteTypeInfoM                        *RiteTypeInfoManager
	RiteInfoM                            *RiteInfoManager
	RitePowerInfoM                       *RitePowerInfoManager
	RiteRareInfoM                        *RiteRareInfoManager
	RiteMarkInfoM                        *RiteMarkInfoManager
	RiteMonsterGroupInfoM                *RiteMonsterGroupInfoManager
	SeasonLevelInfoM                     *SeasonLevelInfoManager
	SeasonLevelAwardInfoM                *SeasonLevelAwardInfoManager
	SeasonLevelTaskInfoM                 *SeasonLevelTaskInfoManager
	SeasonInfoM                          *SeasonInfoManager
	SeasonDungeonInfoM                   *SeasonDungeonInfoManager
	SeasonDungeonRewardInfoM             *SeasonDungeonRewardInfoManager
	SeasonDungeonLayerInfoM              *SeasonDungeonLayerInfoManager
	FormationGroupInfoM                  *FormationGroupInfoManager
	ActivityReturnConfigInfoM            *ActivityReturnConfigInfoManager
	ActivityReturnLoginInfoM             *ActivityReturnLoginInfoManager
	ActivityReturnInfoM                  *ActivityReturnInfoManager
	DisorderlandConfigInfoM              *DisorderlandConfigInfoManager
	DisorderlandBoxInfoM                 *DisorderlandBoxInfoManager
	DisorderlandStoneInfoM               *DisorderlandStoneInfoManager
	DisorderlandDropGroupInfoM           *DisorderlandDropGroupInfoManager
	DisorderlandDropInfoM                *DisorderlandDropInfoManager
	DisorderlandFirstDropInfoM           *DisorderlandFirstDropInfoManager
	DisorderlandDungeonInfoM             *DisorderlandDungeonInfoManager
	DisorderlandMapInfoM                 *DisorderlandMapInfoManager
	DisorderlandInfoM                    *DisorderlandInfoManager
	DisorderlandBuffInfoM                *DisorderlandBuffInfoManager
	DisorderlandBossWeakInfoM            *DisorderlandBossWeakInfoManager
	PeakConfigInfoM                      *PeakConfigInfoManager
	PeakInfoM                            *PeakInfoManager
	PeakRankRewardInfoM                  *PeakRankRewardInfoManager
	SeasonCountdownInfoM                 *SeasonCountdownInfoManager
	HeroBalanceInfoM                     *HeroBalanceInfoManager
	SeasonDataRecordInfoM                *SeasonDataRecordInfoManager
	HeroAwakenLevelInfoM                 *HeroAwakenLevelInfoManager
	SeasonReturnInfoM                    *SeasonReturnInfoManager
	GuildQuitCdInfoM                     *GuildQuitCdInfoManager
	RiteRecycleRewardInfoM               *RiteRecycleRewardInfoM
	GuildSandTableConfigInfoM            *GuildSandTableConfigInfoManager
	GuildSandTableInfoM                  *GuildSandTableInfoManager
	GuildSandTableMapGroupInfoM          *GuildSandTableMapGroupInfoManager
	GuildSandTableLandInfoM              *GuildSandTableLandInfoManager
	GuildSandTableMapInfoM               *GuildSandTableMapInfoManager
	GuildSandTableMonsterGroupInfoM      *GuildSandTableMonsterGroupInfoManager
	GuildSandTableGoddessInfoM           *GuildSandTableGoddessInfoManager
	GuildSandTableBlessingLevelInfoM     *GuildSandTableBlessingLevelInfoManager
	GuildSandTableHomeAwardInfoM         *GuildSandTableHomeAwardInfoManager
	GuildSandTableFatigueInfoM           *GuildSandTableFatigueInfoManager
	GuildSandTableLinkInfoM              *GuildSandTableLinkInfoManager
	GuildSandTableContributionAwardInfoM *GuildSandTableContributionAwardInfoManager
	GuildSandTableTaskAwardInfoM         *GuildSandTableTaskAwardInfoManager
	GuildSandTablePointInfoM             *GuildSandTablePointInfoManager
	SeasonLinkInfoM                      *SeasonLinkInfoMEx
	SeasonLinkMonumentInfoM              *SeasonLinkMonumentInfoMEx
	SeasonLinkMonumentRareInfoM          *SeasonLinkMonumentRareInfoMEx
	SeasonLinkRuneInfoM                  *SeasonLinkRuneInfoM
	SeasonHeroAddInfoM                   *SeasonHeroAddInfoManager
	TowerSeasonQuickPrivilegeInfoM       *TowerSeasonQuickPrivilegeInfoManager
	SeasonLinkRecycleRewardInfoM         *SeasonLinkRecycleRewardInfoM
	StoryReviewGroupInfoM                *StoryReviewGroupInfoM
	EmblemSuccinctMagicSkillInfoM        *EmblemSuccinctMagicSkillInfoM
	EmblemSuccinctInfoM                  *EmblemSuccinctInfoM
	EmblemSuccinctConflateInfoM          *EmblemSuccinctConflateInfoM
	EmblemSuccinctLockInfoM              *EmblemSuccinctLockInfoM
	NewYearInfoM                         *NewYearInfoManager
	ActivityPyramidInfoM                 *ActivityPyramidInfoM
	ActivityPyramidLatticeInfoM          *ActivityPyramidLatticeInfoMEx
	ActivityPyramidChooseInfoM           *ActivityPyramidChooseInfoMEx
	ActivityPyramidTaskInfoM             *ActivityPyramidTaskInfoMEx
	ArtifactRecycleInfoM                 *ArtifactRecycleInfoManager
	ShieldInfoM                          *ShieldInfoMEx
	SeasonFlashBackConfigInfoM           *SeasonFlashBackConfigInfoManager
	SeasonFlashBackDataInfoM             *SeasonFlashBackDataInfoManager
	SeasonFlashBackParamsInfoM           *SeasonFlashBackParamsInfoManager
	GuildSandTableMoraleInfoM            *GuildSandTableMoraleInfoM
	RemainConfigInfoM                    *RemainConfigInfoManager
	RemainInfoM                          *RemainInfoManager
	RemainBlessInfoM                     *RemainBlessInfoManager
	RemainFragmentInfoM                  *RemainFragmentInfoManager
	RemainStarInfoM                      *RemainStarInfoManager
	RemainRecycleRewardInfoM             *RemainRecycleRewardInfoManager
	SeasonAddInfoM                       *SeasonAddInfoManager
	SeasonAddGroupInfoM                  *SeasonAddGroupInfoManager
	GuildSandTableBuildInfoM             *GuildSandTableBuildInfoManager
	GuildSandTableBuildTaskInfoM         *GuildSandTableBuildTaskInfoManager
	GuildSandTableHeroWorkInfoM          *GuildSandTableHeroWorkInfoManager
	SeasonResInfoM                       *SeasonResInfoM
	GuildMedalInfoM                      *GuildMedalInfoManager
	GuildMedalTaskInfoM                  *GuildMedalTaskInfoManager
	GuildSandTableArenaInfoM             *GuildSandTableArenaInfoM
	GuildSandTablePopularityInfoM        *GuildSandTablePopularityInfoM
	SeasonArenaConfigInfoM               *SeasonArenaConfigInfoManager
	SeasonArenaInfoM                     *SeasonArenaInfoManager
	SeasonArenaDivisionInfoM             *SeasonArenaDivisionInfoManager
	SeasonArenaMatchInfoM                *SeasonArenaMatchInfoManager
	SeasonArenaDivisionRewardInfoM       *SeasonArenaDivisionRewardInfoManager
	SeasonArenaBotInfoM                  *SeasonArenaBotInfoManager
	SeasonArenaTaskInfoM                 *SeasonArenaTaskInfoManager
	SeasonArenaRankRewardInfoM           *SeasonArenaRankRewardInfoManager
	GuildSandTableSiegeGroupInfoM        *GuildSandTableSiegeGroupInfoM
	GuildSandTableSiegeBossHpInfoM       *GuildSandTableSiegeBossHpInfoMEx
	GuildSandTableSiegeMonsterInfoM      *GuildSandTableSiegeMonsterInfoM
	GuildSandTableSiegeBossAwardInfoM    *GuildSandTableSiegeBossAwardInfoM
	GuildSandTableMoraleBuffInfoM        *GuildSandTableMoraleBuffInfoM
	ActivityTurntableInfoM               *ActivityTurntableInfoManager
	ActivityTurntableTaskInfoM           *ActivityTurntableTaskInfoManager
	ActivityTurntableRewardInfoM         *ActivityTurntableRewardInfoManager
	ActivityTurntableBuffInfoM           *ActivityTurntableBuffInfoManager
	ActivityTurntableRewardShowInfoM     *ActivityTurntableRewardShowInfoManager
	GuildSandTableDragonInfoM            *GuildSandTableDragonInfoMEx
	GuildSandTableDragonScheduleInfoM    *GuildSandTableDragonScheduleInfoMEx
	GuildSandTableDragonEvolveInfoM      *GuildSandTableDragonEvolveInfoMEx
	GuildSandTableDragonBossHpInfoM      *GuildSandTableDragonBossHpInfoMEx
	GuildSandTableDragonRewardInfoM      *GuildSandTableDragonRewardInfoMEx
	GuildSandTableDragonTaskRewardInfoM  *GuildSandTableDragonTaskRewardInfoMEx
	GuildSandTableDragonBotInfoM         *GuildSandTableDragonBotInfoM
	HotRankInfoM                         *HotRankInfoManager
	HotRankScoreInfoM                    *HotRankScoreInfoManager
	GuildMobilizationInfoM               *GuildMobilizationInfoManager
	GuildMobilizationTaskInfoM           *GuildMobilizationTaskInfoManager
	GuildMobilizationRewardInfoM         *GuildMobilizationRewardInfoManager
	GuildMobilizationRankRewardInfoM     *GuildMobilizationRankRewardInfoManager
	GuildMobilizationScoreRewardInfoM    *GuildMobilizationScoreRewardInfoManager
	BossRushConfigInfoM                  *BossRushConfigInfoManager
	BossRushInfoM                        *BossRushInfoMEx
	BossRushHpInfoM                      *BossRushHpInfoMEx
	BossRushTaskInfoM                    *BossRushTaskInfoMEx
	BossRushRewardInfoM                  *BossRushRewardInfoMEx
	SeasonTalentTreeBaseInfoM            *SeasonTalentTreeBaseInfoMEx
	SeasonTalentTreeLevelInfoM           *SeasonTalentTreeLevelInfoMEx
	SeasonTalentTreeConfigInfoM          *SeasonTalentTreeConfigInfoMEx
	SeasonTalentTreeRecyleAwardInfoM     *SeasonTalentTreeRecyleAwardInfoM
	SeasonTalentTreeTaskAwardInfoM       *SeasonTalentTreeTaskAwardInfoMEx
	ActivityPuzzleInfoM                  *ActivityPuzzleInfoManager
	ActivityPuzzleAimInfoM               *ActivityPuzzleAimInfoManager
	ActivityPuzzleCopyInfoM              *ActivityPuzzleCopyInfoManager
	ActivityComplianceInfoM              *ActivityComplianceInfoManager
	ActivityComplianceRankInfoM          *ActivityComplianceRankInfoManager
	// GuildSandTableTechTaskInfoM            *GuildSandTableTechTaskInfoManager
	// GuildSandTableOreLevelInfoM            *GuildSandTableOreLevelInfoManager
	// GuildSandTableOreMonsterInfoM          *GuildSandTableOreMonsterInfoManager
	// GuildSandTableOreBossHpInfoM           *GuildSandTableOreBossHpInfoManager
	// GuildSandTableOreBossAwardInfoM        *GuildSandTableOreBossAwardInfoManager
	// GuildSandTableTechLevelInfoM           *GuildSandTableTechLevelInfoManager
	ActivityFeedingInfoM                   *ActivityFeedingInfoManager
	ActivityFeedingConditionInfoM          *ActivityFeedingConditionInfoManager
	ActivityFeedingGiftsInfoM              *ActivityFeedingGiftsInfoManager
	SelectSummonConfigInfoM                *SelectSummonConfigInfoManager
	SelectSummonGroupInfoM                 *SelectSummonGroupInfoManager
	SelectSummonInfoM                      *SelectSummonInfoManager
	SelectSummonHeroInfoM                  *SelectSummonHeroInfoManager
	ActivityLifelongGiftInfoM              *ActivityLifelongGiftInfoManager
	GuildDungeonServerInfoM                *GuildDungeonServerInfoManager
	ActivityTowerRankInfoM                 *ActivityTowerRankInfoManager
	ActivityMirageRankInfoM                *ActivityMirageRankInfoManager
	SeasonStartTowerRankInfoM              *SeasonStartTowerRankInfoManager
	GuildSandTableChallengeInfoM           *GuildSandTableChallengeInfoMEx
	GuildSandTableChallengeGuildRankInfoM  *GuildSandTableChallengeGuildRankInfoM
	GuildSandTableChallengeMatchInfoM      *GuildSandTableChallengeMatchInfoM
	GuildSandTableChallengeBuffInfoM       *GuildSandTableChallengeBuffInfoMEx
	GuildSandTableChallengeTaskInfoM       *GuildSandTableChallengeTaskInfoMEx
	GuildSandTableChallengeBotInfoM        *GuildSandTableChallengeBotInfoMEx
	GuildSandTableChallengeRankRewardInfoM *GuildSandTableChallengeRankRewardInfoM
	SeasonComplianceInfoM                  *SeasonComplianceInfoManager
	SeasonComplianceRankRewardInfoM        *SeasonComplianceRankRewardInfoManager
	SeasonComplianceStageInfoM             *SeasonComplianceStageInfoManager
	SeasonComplianceStageRewardInfoM       *SeasonComplianceStageRewardInfoManager
	SeasonComplianceEmblemInfoM            *SeasonComplianceEmblemInfoManager
	SeasonJewelryInfoM                     *SeasonJewelryInfoMEx
	SeasonJewelrySkillRandomInfoM          *SeasonJewelrySkillRandomInfoMEx
	SeasonJewelrySkillInfoM                *SeasonJewelrySkillInfoM
	SeasonJewelrySkillLvInfoM              *SeasonJewelrySkillLvInfoM
	SeasonJewelryClassUpInfoM              *SeasonJewelryClassUpInfoM
	SeasonJewelrySkillChangeInfoM          *SeasonJewelrySkillChangeInfoMEx
	SeasonJewelrySuitInfoM                 *SeasonJewelrySuitInfoM
	SeasonJewelryRecycleInfoM              *SeasonJewelryRecycleInfoM
	SeasonJewelryConfigInfoM               *SeasonJewelryConfigInfoMEx
	SeasonDoorBuffInfoM                    *SeasonDoorBuffInfoManager
	SeasonDoorConfigInfoM                  *SeasonDoorConfigInfoManager
	SeasonDoorInfoM                        *SeasonDoorInfoManager
	SeasonDoorRestInfoM                    *SeasonDoorRestInfoManager
	SeasonDoorTaskInfoM                    *SeasonDoorTaskInfoManager
	SeasonDoorBossWeakInfoM                *SeasonDoorBossWeakInfoM
	TitleInfoM                             *TitleInfoMEx
	RechargeRefundInfoM                    *RechargeRefundInfoManager
	ActivityWebInfoM                       *ActivityWebInfoManager
	ShootGameStageInfoM                    *ShootGameStageInfoManager
	ActivitySeasonShopInfoM                *ActivitySeasonShopInfoMEx
	ActivitySeasonShopGoodsInfoM           *ActivitySeasonShopGoodsInfoMEx
	ActivityRankTimelimitInfoM             *ActivityRankTimelimitInfoManager
	DailyAttendanceHeroRewardInfoM         *DailyAttendanceHeroRewardInfoManager
	SeasonMapPositionInfoM                 *SeasonMapPositionInfoManager
	SeasonMapMonsterInfoM                  *SeasonMapMonsterInfoManager
	SeasonMapConnectInfoM                  *SeasonMapConnectInfoManager
	SeasonMapMasterInfoM                   *SeasonMapMasterInfoManager
	SeasonMapTradeInfoM                    *SeasonMapTradeInfoManager
	SeasonMapAltarBuffInfoM                *SeasonMapAltarBuffInfoManager
	SeasonMapAltarInfoM                    *SeasonMapAltarInfoManager
	SeasonMapConfigInfoM                   *SeasonMapConfigInfoManager
	SeasonMapMonsterHpInfoM                *SeasonMapMonsterHpInfoM
	SeasonMapMonsterRewardInfoM            *SeasonMapMonsterRewardInfoMEx
	SeasonMapTaskInfoM                     *SeasonMapTaskInfoManager
	SeasonMapTradeEventInfoM               *SeasonMapTradeEventInfoManager
	SeasonMapTradeGoodsInfoM               *SeasonMapTradeGoodsInfoManager
	SeasonMapTriggerInfoM                  *SeasonMapTriggerInfoManager
	SeasonMapDailyMoneyInfoM               *SeasonMapDailyMoneyInfoM
	PokemonInfoM                           *PokemonInfoMEx
	PokemonFragmentInfoM                   *PokemonFragmentInfoM
	PokemonBallInfoM                       *PokemonBallInfoMEx
	PokemonStarInfoM                       *PokemonStarInfoMEx
	PokemonPotentialInfoM                  *PokemonPotentialInfoMEx
	PokemonSkillInfoM                      *PokemonSkillInfoM
	PokemonSkillLevelInfoM                 *PokemonSkillLevelInfoMEx
	PokemonMasterInfoM                     *PokemonMasterInfoMEx
	TowerPokemonDungeonInfoM               *TowerPokemonDungeonInfoManager
	TowerPokemonFightEffectInfoM           *TowerPokemonFightEffectInfoManager
	TowerPokemonTaskInfoM                  *TowerPokemonTaskInfoManager
	TowerPokemonRankInfoM                  *TowerPokemonRankInfoManager
	TowerPokemonGoodsInfoM                 *TowerPokemonGoodsInfoManager
	PokemonSummonInfoM                     *PokemonSummonInfoManager
	PokemonSummonConfigInfoM               *PokemonSummonConfigInfoManager
	OfflineVipAwardInfoM                   *OfflineVipAwardInfoMEx
	ResourcesWarnInfoM                     *ResourcesWarnInfoM
	SynthesisGameConfigInfoM               *SynthesisGameConfigInfoMEx
	HeroRareUpInfoM                        *HeroRareUpInfoM
	BalanceArenaInfoM                      *BalanceArenaInfoMEx
	BalanceArenaPhaseInfoM                 *BalanceArenaPhaseInfoMEx
	BalanceArenaRewardInfoM                *BalanceArenaRewardInfoM
	BalanceArenaRankRewardInfoM            *BalanceArenaRankRewardInfoM
	BalanceArenaBotInfoM                   *BalanceArenaBotInfoM
	BalanceArenaConfigInfoM                *BalanceArenaConfigInfoM
}

func (data *XmlData) addManager(m manager) {
	if _, exist := data.managersMap[m.name()]; exist {
		panic(fmt.Sprintf("addManager duplicate name:%s", m.name()))
	}
	data.managers = append(data.managers, m)
	data.managersMap[m.name()] = m
}

func (data *XmlData) Init() {
	data.ServerInfoM = newServerInfo(data)
	data.LoginLimitInfoM = newLoginLimitInfoManager(data)
	data.PreciousResInfoM = newPreciousResInfoManager(data)
	data.XorKeyM = newXorKeyManager(data)
	data.ConfigInfoM = newConfigInfoManager(data)
	data.ResourceTypeInfoM = newResourceTypeInfoManager(data)
	data.ItemSelectiveGroupInfoM = newItemSelectiveGroupInfoManager(data)
	data.ItemInfoM = newItemInfoManager(data)
	data.FragmentInfoM = newFragmentInfoManager(data)
	data.ItemTokenInfoM = newItemTokenInfoManager(data)
	data.TaskTypeInfoM = newTaskTypeInfoManager(data)
	data.TaskInfoM = newTaskInfoManager(data)
	data.BuyPriceInfoM = newBuyPriceInfoManager(data)
	data.SkillInfoM = newSkillInfoManager(data)
	data.SkillLevelInfoM = newSkillLevelInfoManager(data)
	data.PassiveSkillTriggerM = newPassiveSkillTriggerInfoManager(data)
	data.PassiveSkillEffectM = newPassiveSkillEffectInfoManager(data)
	data.PassiveSkillProbabilityInfoM = newPassiveSkillProbabilityInfoManager(data)
	data.FormationGroupInfoM = newFormationGroupInfoManager(data)
	data.PassiveSkillInfoM = newPassiveSkillInfoManager(data)
	data.RaisePassiveSkillInfoM = newRaisePassiveSkillInfoManager(data)
	data.BuffTypeInfoM = newBuffTypeInfoManager(data)
	data.BuffConditionInfoM = newBuffConditionInfoManager(data)
	data.BuffInfoM = newBuffInfoManager(data)
	data.BattleParaInfoM = newBattleParaInfoManager(data)
	data.FunctionInfoM = newFunctionInfoManager(data)
	data.NewDropInfoM = newNewDropInfoManager(data)
	data.DropGroupInfoM = newDropGroupInfoManager(data)
	data.DropInfoM = newDropInfoManager(data)
	data.DungeonFirstDropInfoM = newDungeonFirstDropInfoManager(data)
	data.DungeonConfigInfoM = newDungeonConfigInfoManager(data)
	data.PlayerLevelInfoM = newPlayerLevelInfoManager(data)
	data.HeroInfoM = newHeroInfoManager(data)
	data.HeroDataInfoM = newHeroDataInfoManager(data)
	data.HeroLevelInfoM = newHeroLevelInfoManager(data)
	data.HeroStageInfoM = newHeroStageInfoManager(data)
	data.HeroStarInfoM = newHeroStarInfoManager(data)
	data.GameInitialInfoM = newGameInitialInfoManager(data)
	data.ArtifactInfoM = newArtifactInfoManager(data)
	data.ArtifactFragmentInfoM = newArtifactFragmentInfoManager(data)
	data.MonsterDataInfoM = newMonsterDataInfoManager(data)
	data.MonsterInfoM = newMonsterInfoManager(data)
	data.MonsterInfo7M = newMonsterInfo7Manager(data)
	data.MonsterInfo40M = newMonsterInfo40Manager(data)
	data.MonsterInfo55M = newMonsterInfo55Manager(data)
	data.MonsterArtifactM = newMonsterArtifactInfoManager(data)
	data.MonsterGroupInfoM = newMonsterGroupInfoManager(data)
	data.RaceBuffInfoM = newRaceBuffInfoManager(data)
	data.MailInfoM = newMailInfoManager(data)
	data.ShopRandomGoodsInfoM = newShopRandomGoodsInfoManager(data)
	data.ShopRegularGoodsInfoM = newShopRegularGoodsInfoManager(data)
	data.ShopRandomInfoM = newShopRandomInfoManager(data)
	data.ShopInfoM = newShopInfoManager(data)
	data.EquipInfoM = newEquipInfoManager(data)
	data.EquipAttrCoeInfoM = newEquipAttrCoeInfoManager(data)
	data.EquipStrengthInfoM = newEquipStrengthInfoManager(data)
	data.EquipRefineInfoM = newEquipRefineInfoManager(data)
	data.EquipRefineTechniqueInfoM = newEquipRefineTechniqueInfoManager(data)
	data.EquipEnchantInfoM = newEquipEnchantInfoManager(data)
	data.EquipEnchantMasterInfoM = newEquipEnchantMasterInfoManager(data)
	data.EquipEvolutionInfoM = newEquipEvolutionInfoManager(data)
	data.EquipEnchantRandInfoM = newEquipEnchantRandInfoManager(data)
	data.RankingInfoM = newRankingInfoManager(data)
	data.TowerInfoM = newTowerInfoManager(data)
	data.ArenaMatchInfoM = newArenaMatchInfoManager(data)
	data.ArenaFightRewardInfoM = newArenaFightRewardInfoManager(data)
	data.ArenaBotInfoM = newArenaBotInfoManager(data)
	data.ArenaPraiseInfoM = newArenaPraiseInfoManager(data)
	data.NumberTypeInfoM = newNumberTypeInfoManager(data)
	data.ArtifactStarInfoM = newArtifactStarInfoManager(data)
	data.ArtifactForgeInfoM = newArtifactForgeInfoManager(data)
	data.ArtifactStrengthInfoM = newArtifactStrengthInfoManager(data)
	data.ArtifactTalentInfoM = newArtifactTalentInfoManager(data)
	data.ArtifactSkillInfoM = newArtifactSkillInfoManager(data)
	data.GemComposeInfoM = newGemComposeInfoManager(data)
	data.GemInfoM = newGemInfoManager(data)
	data.GemAttrInfoM = newGemAttrInfoManager(data)
	data.AvatarInfoM = newAvatarInfoManager(data)
	data.AvatarItemInfoM = newAvatarItemInfoManager(data)
	data.TrialInfoM = newTrialInfoManager(data)
	data.EmblemConfigInfoM = newEmblemConfigInfoManager(data)
	data.EmblemLevelInfoM = newEmblemLevelInfoManager(data)
	data.EmblemSuitInfoM = newEmblemSuitInfoManager(data)
	data.EmblemMagicSkillInfoM = newEmblemMagicSkillInfoManager(data)
	data.EmblemHeroGroupInfoM = newEmblemHeroGroupInfoMEx(data)
	data.EmblemInfoM = newEmblemInfoManager(data)
	data.EmblemFragmentInfoM = newEmblemFragmentInfoManager(data)
	data.EmblemCustomizeTypeInfoM = newEmblemCustomizeTypeInfoManager(data)
	data.EmblemAscendInfoM = newEmblemAscendInfoManager(data)
	data.EmblemCustomizeHeroInfoM = newEmblemCustomizeHeroInfoManager(data)
	data.EmblemCustomizeInfoM = newEmblemCustomizeInfoManager(data)
	data.SummonClassInfoM = newSummonClassInfoManager(data)
	data.SummonGroupInfoM = newSummonGroupInfoManager(data)
	data.SummonTypeInfoM = newSummonTypeInfoManager(data)
	data.SummonSpecialClassInfoM = newSummonSpecialClassInfoManager(data)
	data.GoldBuyRefreshInfoM = newGoldBuyRefreshInfoManager(data)
	data.GoldBuyInfoM = newGoldBuyInfoManager(data)
	data.DispatchInfoM = newDispatchInfoManager(data)
	data.DispatchGroupInfoM = newDispatchGroupInfoManager(data)
	data.DispatchLevelInfoM = newDispatchLevelInfoManager(data)
	data.HeroAwakeInfoM = newHeroAwakeInfoManager(data)
	data.MazeConstantInfoM = newMazeConstantInfoManager(data)
	data.MazeDifficultyInfoM = newMazeDifficultyInfoManager(data)
	data.MazeMapInfoM = newMazeMapInfoManager(data)
	data.MazeBuffInfoM = newMazeBuffInfoManager(data)
	data.MazeQuestionInfoM = newMazeQuestionInfoManager(data)
	data.MazeAnswerInfoM = newMazeAnswerInfoManager(data) // 必须在 MazeBuffInfoM 后加载，因为依赖 MazeBuffInfoM 做了数据强校验
	data.MazeScrollInfoM = newMazeScrollInfoManager(data)
	data.MirageCopyInfoM = newMirageCopyInfoManager(data)
	data.MirageAffixInfoM = newMirageAffixInfoManager(data)
	data.MirageAffixGroupInfoM = newMirageAffixGroupInfoManager(data) //必须在词缀加载后加载
	data.MirageHurdleInfoM = newMirageHurdleInfoManager(data)         //必须放在个人boss相关量表的最后
	data.GuildConfigInfoM = newGuildConfigInfoManager(data)
	data.GuildLevelInfoM = newGuildLevelInfoManager(data)
	data.GuildLogInfoM = newGuildLogInfoManager(data)
	data.GuildBadgeInfoM = newGuildBadgeInfoManager(data)
	data.BelltowerConfigInfoM = newBelltowerConfigInfoManager(data)
	data.TalesInfoM = newTalesInfoManager(data)
	data.TalesDungeonInfoM = newTalesDungeonInfoManager(data)
	data.TalesEliteInfoM = newTalesEliteInfoManager(data)
	data.MemoryChipInfoM = newMemoryChipInfoManager(data)
	data.FormationInfoM = newFormationInfoManager(data)
	data.GuildDungeonChapterRewardM = newGuildDungeonChapterRewardManager(data)
	data.GuildDungeonRewardInfoM = newGuildDungeonRewardInfoManager(data)
	data.GuildDungeonChapterInfoM = newGuildDungeonChapterInfoManager(data)
	data.GuildDungeonTaskInfoM = newGuildDungeonTaskInfoManager(data)
	data.GuildBossHpInfoM = newGuildBossHpInfoManager(data)
	data.GuildDungeonRankRewardInfoM = newGuildDungeonRankRewardInfoManager(data)
	data.BotHeadInfoM = newBotHeadInfoManager(data)
	data.GuildTalentInfoM = newGuildTalentInfoManager(data)
	data.GuidanceInfoM = newGuidanceInfoManager(data)
	data.GuidanceSkipConfigInfoM = newGuidanceSkipConfigInfoManager(data)
	data.MasterDataInfoM = newMasterDataInfoManager(data)
	data.CarnivalInfoM = newCarnivalInfoManager(data)
	data.CarnivalTaskInfoM = newCarnivalTaskInfoManager(data)
	data.GodPresentChangeInfoM = newGodPresentChangeInfoManager(data)
	data.GodPresentClassInfoM = newGodPresentClassInfoManager(data)
	data.GodPresentGroupInfoM = newGodPresentGroupInfoManager(data)
	data.GodPresentRecoveryInfoM = newGodPresentRecoveryInfoManager(data)
	data.GodPresentConfigInfoM = newGodPresentConfigInfoManager(data)
	data.GodPresentInfoM = newGodPresentInfoManager(data)
	data.MazeBotInfoM = newMazeBotInfoManager(data)
	data.SevenDayLoginM = newActivitySevenDayInfoManager(data)
	data.HeroFetterInfoM = newHeroFetterInfoManager(data)
	data.HeroArchiveInfoM = newHeroArchiveInfoManager(data)
	data.MedalInfoM = newMedalInfoManager(data)
	data.MedalTaskInfoM = newMedalTaskInfoManager(data)
	data.MazeBuyReviveInfoM = newMazeBuyReviveInfoManager(data)
	data.CrystalConfigInfoM = newCrystalConfigInfoManager(data)
	// data.CrystalGemInfoM = newCrystalGemInfoManager(data)
	data.ChatInfoM = newChatInfoManager(data)
	data.ForecastInfoM = newForecastInfoManager(data) // forecast_info.xml 必须在 forecast_task_info.xml前面加载
	data.ForecastTaskInfoM = newForecastTaskInfoManager(data)
	data.RechargeProductInfoM = newRechargeProductInfoManager(data)
	data.RechargeGoodsInfoM = newRechargeGoodsInfoManager(data)
	data.RechargeCouponInfoM = newRechargeCouponInfoManager(data)
	data.LinkSkillInfoM = newLinkSkillInfoManager(data)
	data.TowerConfigInfoM = newTowerConfigInfoManager(data)
	data.VipInfoM = newVipInfoManager(data)
	data.VipPrivilegeInfoM = newVipPrivilegeInfoManager(data)
	data.ActivityRechargeShopInfoM = newActivityRechargeShopInfoManager(data)
	data.ActivityRechargeGiftInfoM = newActivityRechargeGiftInfoManager(data)
	data.TrialConfigInfoM = newTrialConfigInfoManager(data)
	data.CrystalAchieveInfoM = newCrystalAchieveInfoManager(data)
	data.FirstRechargeInfoM = newFirstRechargeInfoManager(data)
	data.TowerstarConditionInfoM = newTowerstarConditionInfoManager(data)
	data.TowerstarChapterInfoM = newTowerstarChapterInfoManager(data)
	data.TowerstarRewardInfoM = newTowerstarRewardInfoManager(data)
	data.TowerstarDungeonInfoM = newTowerstarDungeonInfoManager(data)
	data.TowerstarConfigInfoM = newTowerstarConfigInfoManager(data)
	data.MazeGuardianRewardInfoM = newMazeGuardianRewardInfoManager(data)
	data.MazeTaskInfoM = newMazeTaskInfoManager(data)
	data.MazeTaskLevelInfoM = newMazeTaskLevelInfoManager(data)
	data.MazeCuresInfoM = newMazeCuresInfoManager(data)
	data.ShopBoxRandomInfoM = newShopBoxRandomInfoManager(data)
	data.MazeBoxInfoM = newMazeBoxInfoManager(data)
	data.MonthlycardInfoM = newMonthlycardInfoManager(data)
	data.PassInfoM = newPassInfoManager(data)
	data.PassTaskInfoM = newPassTaskInfoManager(data)
	data.ActivityPushGiftInfoM = newActivityPushGiftInfoManager(data)
	data.ActivityPushgiftGroupInfoM = newActivityPushGiftGroupInfoManager(data)
	data.ActivityPushgiftConfigInfoM = newActivityPushgiftConfigInfoManager(data)
	data.BotInfoM = newBotInfoManager(data)
	data.ArenaConfigInfoM = newArenaConfigInfoManager(data)
	data.ArenaDivisionInfoM = newArenaDivisionInfoManager(data)
	data.ArenaDivisionTaskInfoM = newArenaDivisionTaskInfoManager(data)
	data.ArenaDivisionRewardInfoM = newArenaDivisionRewardInfoManager(data)
	data.ShareConfigInfoM = newShareConfigInfoManager(data)
	data.SkillTargetInfoM = newSkillTargetInfoManager(data)
	data.WishListConfigInfoM = newWishlistConfigInfoManager(data)
	data.WishListGuaranteeInfoM = newWishlistGuaranteeInfoManager(data)
	data.WishListRangeInfoM = newWishlistRangeInfoManager(data)
	data.PassCycleTaskInfoM = newPassCycleTaskInfoManager(data)
	data.ActivityTaskTypeInfoM = newActivityTaskTypeInfoManager(data)
	data.ChatConfigInfoM = newChatConfigInfoManager(data)
	data.ChatAdminInfoM = newChatAdminInfoManager(data)
	data.BattleSuppressInfoM = newBattleSuppressInfoManager(data)
	data.AttributeInfoM = newAttributeInfoManager(data)
	data.OldEmblemInfoM = newOldEmblemInfoManager(data)
	data.HeroInitDataInfoM = newHeroInitDataInfoManager(data)
	data.ConversionInfoM = newConversionInfoManager(data)
	data.WrestleConfigInfoM = newWrestleConfigInfoManager(data)
	data.WrestleBotInfoM = newWrestleBotInfoManager(data)
	data.WrestleLevelInfoM = newWrestleLevelInfoManager(data)
	data.WrestleRankInfoM = newWrestleRankInfoManager(data)
	data.WrestleLevelTaskInfoM = newWrestleLevelTaskInfoManager(data)
	data.WrestleRewardInfoM = newWrestleRewardInfoManager(data)
	data.WrestleRankRewardInfoM = newWrestleRankRewardInfoManager(data)
	data.GoddessContractBlessInfoM = newGoddessContractBlessInfoManager(data)
	data.GoddessContractInfoM = newGoddessContractInfoManager(data)
	data.GoddessTalesDungeonInfoM = newGoddessTalesDungeonInfoManager(data)
	data.ActivityGoddessInfoM = newActivityGoddessInfoManager(data)
	data.GoddessContractEliteInfoM = newGoddessContractEliteInfoManager(data)
	data.GoddessContractTrustInfoM = newGoddessContractTrustInfoManager(data)
	data.GoddessContractGiftsInfoM = newGoddessContractGiftsInfoManager(data)
	data.GoddessContractSkinInfoM = newGoddessContractSkinInfoManager(data)
	data.GoddessContractTreatInfoM = newGoddessContractTreatInfoManager(data)
	data.VipProductInfoM = newVipProductInfoManager(data)
	data.GuildTalentConditionInfoM = newGuildTalentConditionInfoManager(data)
	data.MazeModelInfoM = newMazeModelInfoManager(data)
	data.FlowerOccupyTimberInfoM = newFlowerOccupyTimberInfoManager(data)
	data.FlowerLvInfoM = newFlowerLvInfoManager(data)
	data.FlowerOccupyBaseInfoM = newFlowerOccupyBaseInfoManager(data)
	data.FlowerOccupyJungleInfoM = newFlowerOccupyJungleInfoManager(data)
	data.FlowerPlantScoreInfoM = newFlowerPlantScoreInfoManager(data)
	data.FlowerPlantGoblinInfoM = newFlowerPlantGoblinInfoManager(data)
	data.FlowerPlantInfoM = newFlowerPlantInfoManager(data)
	data.FlowerPlantTypeInfoM = newFlowerPlantTypeInfoManager(data)
	data.FlowerPlantRewardInfoM = newFlowerPlantRewardInfoManager(data)
	data.FlowerConfigInfoM = newFlowerConfigInfoManager(data)
	data.FlowerAssistDungeonInfoM = newFlowerAssistDungeonInfoManager(data)
	data.FlowerMessageInfoM = newFlowerMessageInfoManager(data)
	data.GemLevelInfoM = newGemLevelInfoManager(data)
	data.MirageFightEffectInfoM = newMirageFightEffectInfoManager(data)
	data.TrialHeroInfoM = newTrialHeroInfoManager(data)
	data.TeamInitDataInfoM = newTeamInitDataInfoManager(data)
	data.ActivityMonthInfoM = newActivityMonthInfoManager(data)
	data.ActivityMonthGroupInfoM = newActivityMonthGroupInfoManager(data)
	data.ActivityMonthTasksInfoM = newActivityMonthTasksInfoManager(data)
	data.DailyWishWeightM = newDailyWishWeightInfoManager(data)
	data.DailyWishInfoM = newDailyWishInfoManager(data)
	data.DailyWishOpenInfoM = newDailyWishOpenInfoManager(data)
	data.LinkSummonConfigInfoM = newLinkSummonConfigInfoManager(data)
	data.ActivityLinkSummonInfoM = newActivityLinkSummonInfoManager(data)
	data.LinkSummonInfoM = newLinkSummonInfoManager(data)
	data.ArtifactExchangeInfoM = newArtifactExchangeInfoManager(data)
	data.ArtifactDebutConfigInfoM = newArtifactDebutConfigInfoManager(data)
	data.ArtifactDebutTimeInfoM = newArtifactDebutTimeInfoManager(data)
	data.ArtifactDebutDrawDropInfoM = newArtifactDebutDrawDropInfoManager(data)
	data.ArtifactDebutDrawGroupInfoM = newArtifactDebutDrawGroupInfoManager(data)
	data.ArtifactDebutDrawCategoryInfoM = newArtifactDebutDrawCategoryInfoManager(data)
	data.ArtifactDebutDrawRewardInfoM = newArtifactDebutDrawRewardInfoManager(data)
	data.ArtifactDebutLoginRewardInfoM = newArtifactDebutLoginRewardInfoManager(data)
	data.ArtifactDebutPuzzleRewardInfoM = newArtifactDebutPuzzleRewardInfoManager(data)
	data.ArtifactDebutTaskInfoM = newArtifactDebutTaskInfoManager(data)
	data.ArtifactDebutDrawOptionalInfoM = newArtifactDebutDrawOptionalInfoManager(data)
	data.ArtifactDebutActivityInfoM = newArtifactDebutActivityInfoManager(data)
	data.DivineDemonConfigInfoM = newDivineDemonConfigInfoManager(data)
	data.DivineDemonInfoM = newDivineDemonInfoManager(data)
	data.DivineDemonDefaultInfoM = newDivineDemonDefaultInfoManager(data)
	data.DivineDemonTaskSummonInfoM = newDivineDemonTaskSummonInfoManager(data)
	data.DivineDemonTaskActiveInfoM = newDivineDemonTaskActiveInfoManager(data)
	data.DivineDemonTaskStarInfoM = newDivineDemonTaskStarInfoManager(data)
	data.DivineDemonGroupInfoM = newDivineDemonGroupInfoManager(data)
	data.LinkInfoM = newLinkInfoManager(data)
	data.NumberMaxInfoM = newNumberMaxInfoManager(data)
	data.NumberMaxAddTaskInfoM = newNumberMaxAddTaskInfoManager(data)
	data.TrialCopyInfoM = newTrialCopyInfoManager(data)
	data.GuildCollectiveDonateInfoM = newGuildCollectiveDonateInfoManager(data)
	data.GuildDonateInfoM = newGuildDonateInfoManager(data)
	data.RoundActivitySumInfoM = newRoundActivitySumInfoManager(data)
	data.RoundActivityTaskInfoM = newRoundActivityTaskInfoManager(data)
	data.RoundActivityOpenInfoM = newRoundActivityOpenInfoManager(data)
	data.GoddessCollectionBoxInfoM = newGoddessCollectionBoxInfoManager(data)
	data.GoddessCollectionInfoM = newGoddessCollectionInfoManager(data)
	data.ActivityWebGiftInfoM = newActivityWebGiftInfoManager(data)
	data.ArtifactDebutGuaranteeInfoM = newArtifactDebutGuaranteeInfoManager(data)
	data.LinkBookTaskInfoM = newLinkBookTaskInfoManager(data)
	data.WrestleBuffInfoM = newWrestleBuffInfoManager(data)
	data.TowerSeasonDungeonInfoM = newTowerSeasonDungeonInfoManager(data)
	data.TowerSeasonFightEffectInfoM = newTowerSeasonFightEffectInfoManager(data)
	data.TowerSeasonTaskInfoM = newTowerSeasonTaskInfoManager(data)
	data.HeroExchangeInfoM = newHeroExchangeInfoManager(data)
	data.BuffGroupInfoM = newBuffGroupInfoManager(data)
	data.SummonActivityCountInfoM = newSummonActivityCountInfoManager(data)
	data.SummonActivityGuaranteeInfoM = newSummonActivityGuaranteeInfoManager(data)
	data.ChatLanguageInfoM = newChatLanguageInfoManager(data)
	data.RankingAchievementInfoM = newRankingAchievementInfoManager(data)
	data.GuildDungeonSeasonRewardInfoM = newGuildDungeonSeasonRewardInfoManager(data)
	data.GuildDungeonWeeklyRewardInfoM = newGuildDungeonWeeklyRewardInfoManager(data)
	data.GuildDungeonLevelUpRewardInfoM = newGuildDungeonLevelUpRewardInfoManager(data)
	data.GuildLabelInfoM = newGuildLabelInfoManager(data)
	data.GuildDungeonPointInfoM = newGuildDungeonPointInfoManager(data)
	data.GuildDungeonDivisionInoM = newGuildDungeonDivisionInfoManager(data)
	data.MedalSkinInfoM = newMedalSkinInfoManager(data)
	data.WebRewardInfoM = newWebRewardInfoManager(data)
	data.ArtifactLinkSkillInfoM = newArtifactLinkSkillInfoManager(data)
	data.GuildDungeonWeeklyBaseRewardInfoM = newGuildDungeonWeeklyBaseRewardInfoManager(data)
	data.DropActivityFunctionInfoM = newDropActivityFunctionInfoManager(data)
	data.DropActivityInfoM = newDropActivityInfoManager(data)
	data.DropActivityTypeInfoM = newDropActivityTypeInfoManager(data)
	data.DropActivityOpenInfoM = newDropActivityOpenInfoManager(data)
	data.DropActivityOpenExchangeInfoM = newDropActivityOpenExchangeInfoManager(data)
	data.DailyAttendanceInfoM = newDailyAttendanceInfoManager(data)
	data.DailyAttendanceRewardInfoM = newDailyAttendanceRewardInfoManager(data)
	data.ActivityDailySpecialInfoM = newActivityDailySpecialInfoManager(data)
	data.GuildChestInfoM = newGuildChestInfoManager(data)
	data.ActivitySumInfoM = newActivitySumInfoManager(data)
	data.WorldbossConfigInfoM = newWorldbossConfigInfoManager(data)
	data.WorldbossDifficultyInfoM = newWorldbossDifficultyInfoManager(data)
	data.WorldbossInfoM = newWorldbossInfoManager(data) // 表中数据不能删，永远递增
	data.WorldbossTaskInfoM = newWorldbossTaskInfoManager(data)
	data.WorldbossRankRewardInfoM = newWorldbossRankRewardInfoManager(data)
	data.LinkBookInfoM = newLinkBookInfoManager(data)
	data.TowerSeasonRankRewardInfoM = newTowerSeasonRankRewardInfoManager(data)
	data.SkinInfoM = newSkinInfoManager(data)
	data.GuildDungeonStrategySkillInfoM = newGuildDungeonStrategySkillInfoManager(data)
	data.GuildDungeonWeekStrategyInfoM = newGuildDungeonWeekStrategyInfoManager(data)
	data.GuildDungeonSeasonStrategyInfoM = newGuildDungeonSeasonStrategyInfoManager(data)
	data.GuildDungeonArtifactAddInfoM = newGuildDungeonArtifactAddInfoManager(data)
	data.ActivityStoryDungeonInfoM = newActivityStoryDungeonInfoManager(data)
	data.ActivityStoryLoginInfoM = newActivityStoryLoginInfoManager(data)
	data.ActivityStoryInfoM = newActivityStoryInfoManager(data)
	data.ActivityStoryShopInfoM = newActivityStoryShopInfoManager(data)
	data.ActivityStoryLayerInfoM = newActivityStoryLayerInfoManager(data)
	data.AssistanceActivityOpenInfoM = newAssistanceActivityOpenInfoManager(data)
	data.AssistanceActivityInfoM = newAssistanceActivityInfoManager(data)
	data.RiteTypeInfoM = newRiteTypeInfoManager(data)
	data.RiteInfoM = newRiteInfoManager(data)
	data.RitePowerInfoM = newRitePowerInfoManager(data)
	data.RiteRareInfoM = newRiteRareInfoManager(data)
	data.RiteMarkInfoM = newRiteMarkInfoManager(data)
	data.RiteMonsterGroupInfoM = newRiteMonsterGroupInfoManager(data)
	data.SeasonLevelInfoM = newSeasonLevelInfoManager(data)
	data.SeasonLevelAwardInfoM = newSeasonLevelAwardInfoManager(data)
	data.SeasonLevelTaskInfoM = newSeasonLevelTaskInfoManager(data)
	data.SeasonInfoM = newSeasonInfoManager(data)
	data.SeasonDungeonInfoM = newSeasonDungeonInfoManager(data)
	data.SeasonDungeonLayerInfoM = newSeasonDungeonLayerInfoManager(data)
	data.SeasonDungeonRewardInfoM = newSeasonDungeonRewardInfoManager(data)
	data.ActivityReturnConfigInfoM = newActivityReturnConfigInfoManager(data)
	data.ActivityReturnLoginInfoM = newActivityReturnLoginInfoManager(data)
	data.ActivityReturnInfoM = newActivityReturnInfoManager(data)
	data.DisorderlandConfigInfoM = newDisorderlandConfigInfoManager(data)
	data.DisorderlandBoxInfoM = newDisorderlandBoxInfoManager(data)
	data.DisorderlandStoneInfoM = newDisorderlandStoneInfoManager(data)
	data.DisorderlandDropGroupInfoM = newDisorderlandDropGroupInfoManager(data)
	data.DisorderlandDropInfoM = newDisorderlandDropInfoManager(data)
	data.DisorderlandFirstDropInfoM = newDisorderlandFirstDropInfoManager(data)
	data.DisorderlandDungeonInfoM = newDisorderlandDungeonInfoManager(data)
	data.DisorderlandMapInfoM = newDisorderlandMapInfoManager(data)
	data.DisorderlandInfoM = newDisorderlandInfoManager(data)
	data.DisorderlandBuffInfoM = newDisorderlandBuffInfoManager(data)
	data.DisorderlandBossWeakInfoM = newDisorderlandBossWeakInfoManager(data)
	data.PeakConfigInfoM = newPeakConfigInfoManager(data)
	data.PeakInfoM = newPeakInfoManager(data)
	data.PeakRankRewardInfoM = newPeakRankRewardInfoManager(data)
	data.SeasonCountdownInfoM = newSeasonCountdownInfoManager(data)
	data.HeroBalanceInfoM = newHeroBalanceInfoManager(data)
	data.TowerSeasonConfigInfoM = newTowerSeasonConfigInfoManager(data)
	data.SeasonDataRecordInfoM = newSeasonDataRecordInfoManager(data)
	data.HeroAwakenLevelInfoM = newHeroAwakenLevelInfoManager(data)
	data.SeasonReturnInfoM = newSeasonReturnInfoManager(data)
	data.GuildQuitCdInfoM = newGuildQuitCdInfoManager(data)
	data.RiteRecycleRewardInfoM = newRiteRecycleRewardInfoM(data)
	data.GuildSandTableLinkInfoM = newGuildSandTableLinkInfoManager(data)
	data.GuildSandTableConfigInfoM = newGuildSandTableConfigInfoManager(data)
	data.GuildSandTableInfoM = newGuildSandTableInfoManager(data)
	data.GuildSandTableMapGroupInfoM = newGuildSandTableMapGroupInfoManager(data)
	data.GuildSandTableLandInfoM = newGuildSandTableLandInfoManager(data)
	data.GuildSandTableMonsterGroupInfoM = newGuildSandTableMonsterGroupInfoManager(data)
	//data.GuildSandTableOreLevelInfoM = newGuildSandTableOreLevelInfoManager(data)
	data.GuildSandTableMapInfoM = newGuildSandTableMapInfoManager(data)
	data.GuildSandTableBlessingLevelInfoM = newGuildSandTableBlessingLevelInfoManager(data)
	data.GuildSandTableGoddessInfoM = newGuildSandTableGoddessInfoManager(data)
	data.GuildSandTableHomeAwardInfoM = newGuildSandTableHomeAwardInfoManager(data)
	data.GuildSandTableFatigueInfoM = newGuildSandTableFatigueInfoManager(data)
	data.GuildSandTableContributionAwardInfoM = newGuildSandTableContributionAwardInfoManager(data)
	data.GuildSandTableTaskAwardInfoM = newGuildSandTableTaskAwardInfoManager(data)
	data.GuildSandTablePointInfoM = newGuildSandTablePointInfoManager(data)
	data.SeasonLinkInfoM = newSeasonLinkInfoMEx(data)
	data.SeasonLinkMonumentInfoM = newSeasonLinkMonumentInfoMEx(data)
	data.SeasonLinkMonumentRareInfoM = newSeasonLinkMonumentRareInfoMEx(data)
	data.SeasonLinkRuneInfoM = newSeasonLinkRuneInfoM(data)
	data.SeasonHeroAddInfoM = newSeasonHeroAddInfoManager(data)
	data.TowerSeasonQuickPrivilegeInfoM = newTowerSeasonQuickPrivilegeInfoManager(data)
	data.SeasonLinkRecycleRewardInfoM = newSeasonLinkRecycleRewardInfoM(data)
	data.StoryReviewGroupInfoM = newStoryReviewGroupInfoM(data)
	data.EmblemSuccinctMagicSkillInfoM = newEmblemSuccinctMagicSkillInfoM(data)
	data.EmblemSuccinctInfoM = newEmblemSuccinctInfoM(data)
	data.EmblemSuccinctConflateInfoM = newEmblemSuccinctConflateInfoM(data)
	data.EmblemSuccinctLockInfoM = newEmblemSuccinctLockInfoM(data)
	data.NewYearInfoM = newNewYearInfoManager(data)
	data.ActivityPyramidInfoM = newActivityPyramidInfoM(data)
	data.ActivityPyramidLatticeInfoM = newActivityPyramidLatticeInfoMEx(data)
	data.ActivityPyramidChooseInfoM = newActivityPyramidChooseInfoMEx(data)
	data.ActivityPyramidTaskInfoM = newActivityPyramidTaskInfoMEx(data)
	data.ArtifactRecycleInfoM = newArtifactRecycleInfoManager(data)
	data.ShieldInfoM = newShieldInfoMEx(data)
	data.SeasonFlashBackConfigInfoM = newSeasonFlashBackConfigInfoManager(data)
	data.SeasonFlashBackDataInfoM = newSeasonFlashBackDataInfoManager(data)
	data.SeasonFlashBackParamsInfoM = newSeasonFlashBackParamsInfoManager(data)
	data.GuildSandTableMoraleInfoM = newGuildSandTableMoraleInfoM(data)
	data.RemainConfigInfoM = newRemainConfigInfoManager(data)
	data.RemainInfoM = newRemainInfoManager(data)
	data.RemainBlessInfoM = newRemainBlessInfoManager(data)
	data.RemainFragmentInfoM = newRemainFragmentInfoManager(data)
	data.RemainStarInfoM = newRemainStarInfoManager(data)
	data.RemainRecycleRewardInfoM = newRemainRecycleRewardInfoManager(data)
	data.SeasonAddInfoM = newSeasonAddInfoManager(data)
	data.SeasonAddGroupInfoM = newSeasonAddGroupInfoManager(data)
	data.GuildSandTableBuildInfoM = newGuildSandTableBuildInfoManager(data)
	data.GuildSandTableBuildTaskInfoM = newGuildSandTableBuildTaskInfoManager(data)
	data.GuildSandTableHeroWorkInfoM = newGuildSandTableHeroWorkInfoManager(data)
	data.SeasonResInfoM = newSeasonResInfoM(data)
	data.GuildMedalInfoM = newGuildMedalInfoManager(data)
	data.GuildMedalTaskInfoM = newGuildMedalTaskInfoManager(data)
	data.GuildSandTableArenaInfoM = newGuildSandTableArenaInfoM(data)
	data.GuildSandTablePopularityInfoM = newGuildSandTablePopularityInfoM(data)
	data.SeasonArenaConfigInfoM = newSeasonArenaConfigInfoManager(data)
	data.SeasonArenaInfoM = newSeasonArenaInfoManager(data)
	data.SeasonArenaDivisionInfoM = newSeasonArenaDivisionInfoManager(data)
	data.SeasonArenaMatchInfoM = newSeasonArenaMatchInfoManager(data)
	data.SeasonArenaDivisionRewardInfoM = newSeasonArenaDivisionRewardInfoManager(data)
	data.SeasonArenaBotInfoM = newSeasonArenaBotInfoManager(data)
	data.SeasonArenaTaskInfoM = newSeasonArenaTaskInfoManager(data)
	data.SeasonArenaRankRewardInfoM = newSeasonArenaRankRewardInfoManager(data)
	data.GuildSandTableSiegeGroupInfoM = newGuildSandTableSiegeGroupInfoM(data)
	data.GuildSandTableSiegeBossHpInfoM = newGuildSandTableSiegeBossHpInfoMEx(data)
	data.GuildSandTableSiegeMonsterInfoM = newGuildSandTableSiegeMonsterInfoM(data)
	data.GuildSandTableSiegeBossAwardInfoM = newGuildSandTableSiegeBossAwardInfoM(data)
	data.GuildSandTableMoraleBuffInfoM = newGuildSandTableMoraleBuffInfoM(data)
	data.ActivityTurntableInfoM = newActivityTurntableInfoManager(data)
	data.ActivityTurntableTaskInfoM = newActivityTurntableTaskInfoManager(data)
	data.ActivityTurntableRewardInfoM = newActivityTurntableRewardInfoManager(data)
	data.ActivityTurntableBuffInfoM = newActivityTurntableBuffInfoManager(data)
	data.ActivityTurntableRewardShowInfoM = newActivityTurntableRewardShowInfoManager(data)
	data.GuildSandTableDragonInfoM = newGuildSandTableDragonInfoMEx(data)
	data.GuildSandTableDragonScheduleInfoM = newGuildSandTableDragonScheduleInfoMEx(data)
	data.GuildSandTableDragonEvolveInfoM = newGuildSandTableDragonEvolveInfoMEx(data)
	data.GuildSandTableDragonBossHpInfoM = newGuildSandTableDragonBossHpInfoMEx(data)
	data.GuildSandTableDragonRewardInfoM = newGuildSandTableDragonRewardInfoMEx(data)
	data.GuildSandTableDragonTaskRewardInfoM = newGuildSandTableDragonTaskRewardInfoMEx(data)
	data.GuildSandTableDragonBotInfoM = newGuildSandTableDragonBotInfoM(data)
	data.HotRankInfoM = newHotRankInfoManager(data)
	data.HotRankScoreInfoM = newHotRankScoreInfoManager(data)
	data.GuildMobilizationInfoM = newGuildMobilizationInfoManager(data)
	data.GuildMobilizationTaskInfoM = newGuildMobilizationTaskInfoManager(data)
	data.GuildMobilizationRewardInfoM = newGuildMobilizationRewardInfoManager(data)
	data.GuildMobilizationRankRewardInfoM = newGuildMobilizationRankRewardInfoManager(data)
	data.GuildMobilizationScoreRewardInfoM = newGuildMobilizationScoreRewardInfoManager(data)
	data.BossRushConfigInfoM = newBossRushConfigInfoManager(data)
	data.BossRushInfoM = newBossRushInfoMEx(data)
	data.BossRushHpInfoM = newBossRushHpInfoMEx(data)
	data.BossRushTaskInfoM = newBossRushTaskInfoMEx(data)
	data.BossRushRewardInfoM = newBossRushRewardInfoMEx(data)
	data.SeasonTalentTreeConfigInfoM = newSeasonTalentTreeConfigInfoMEx(data)
	data.SeasonTalentTreeBaseInfoM = newSeasonTalentTreeBaseInfoMEx(data)
	data.SeasonTalentTreeLevelInfoM = newSeasonTalentTreeLevelInfoMEx(data)
	data.SeasonTalentTreeRecyleAwardInfoM = newSeasonTalentTreeRecyleAwardInfoM(data)
	data.SeasonTalentTreeTaskAwardInfoM = newSeasonTalentTreeTaskAwardInfoMEx(data)
	data.ActivityPuzzleInfoM = newActivityPuzzleInfoManager(data)
	data.ActivityPuzzleAimInfoM = newActivityPuzzleAimInfoManager(data)
	data.ActivityPuzzleCopyInfoM = newActivityPuzzleCopyInfoManager(data)
	data.ActivityComplianceInfoM = newActivityComplianceInfoManager(data)
	data.ActivityComplianceRankInfoM = newActivityComplianceRankInfoManager(data)
	// data.GuildSandTableTechTaskInfoM = newGuildSandTableTechTaskInfoManager(data)
	// data.GuildSandTableOreMonsterInfoM = newGuildSandTableOreMonsterInfoManager(data)
	// data.GuildSandTableOreBossHpInfoM = newGuildSandTableOreBossHpInfoManager(data)
	// data.GuildSandTableOreBossAwardInfoM = newGuildSandTableOreBossAwardInfoManager(data)
	// data.GuildSandTableTechLevelInfoM = newGuildSandTableTechLevelInfoManager(data)
	data.ActivityFeedingGiftsInfoM = newActivityFeedingGiftsInfoManager(data)
	data.ActivityFeedingInfoM = newActivityFeedingInfoManager(data)
	data.ActivityFeedingConditionInfoM = newActivityFeedingConditionInfoManager(data)
	data.SelectSummonGroupInfoM = newSelectSummonGroupInfoManager(data)
	data.SelectSummonConfigInfoM = newSelectSummonConfigInfoManager(data)
	data.SelectSummonInfoM = newSelectSummonInfoManager(data)
	data.SelectSummonHeroInfoM = newSelectSummonHeroInfoManager(data)
	data.ActivityLifelongGiftInfoM = newActivityLifelongGiftInfoManager(data)
	data.GuildDungeonServerInfoM = newGuildDungeonServerInfoManager(data)
	data.ActivityTowerRankInfoM = newActivityTowerRankInfoManager(data)
	data.ActivityMirageRankInfoM = newActivityMirageRankInfoManager(data)
	data.SeasonStartTowerRankInfoM = newSeasonStartTowerRankInfoManager(data)
	data.GuildSandTableChallengeInfoM = newGuildSandTableChallengeInfoMEx(data)
	data.GuildSandTableChallengeGuildRankInfoM = newGuildSandTableChallengeGuildRankInfoM(data)
	data.GuildSandTableChallengeMatchInfoM = newGuildSandTableChallengeMatchInfoM(data)
	data.GuildSandTableChallengeBuffInfoM = newGuildSandTableChallengeBuffInfoMEx(data)
	data.GuildSandTableChallengeTaskInfoM = newGuildSandTableChallengeTaskInfoMEx(data)
	data.GuildSandTableChallengeBotInfoM = newGuildSandTableChallengeBotInfoMEx(data)
	data.GuildSandTableChallengeRankRewardInfoM = newGuildSandTableChallengeRankRewardInfoM(data)
	data.SeasonComplianceInfoM = newSeasonComplianceInfoManager(data)
	data.SeasonComplianceRankRewardInfoM = newSeasonComplianceRankRewardInfoManager(data)
	data.SeasonComplianceStageInfoM = newSeasonComplianceStageInfoManager(data)
	data.SeasonComplianceStageRewardInfoM = newSeasonComplianceStageRewardInfoManager(data)
	data.SeasonComplianceEmblemInfoM = newSeasonComplianceEmblemInfoManager(data)
	data.SeasonJewelryInfoM = newSeasonJewelryInfoMEx(data)
	data.SeasonJewelrySkillRandomInfoM = newSeasonJewelrySkillRandomInfoMEx(data)
	data.SeasonJewelrySkillInfoM = newSeasonJewelrySkillInfoM(data)
	data.SeasonJewelrySkillLvInfoM = newSeasonJewelrySkillLvInfoM(data)
	data.SeasonJewelryClassUpInfoM = newSeasonJewelryClassUpInfoM(data)
	data.SeasonJewelrySkillChangeInfoM = newSeasonJewelrySkillChangeInfoMEx(data)
	data.SeasonJewelrySuitInfoM = newSeasonJewelrySuitInfoM(data)
	data.SeasonJewelryRecycleInfoM = newSeasonJewelryRecycleInfoM(data)
	data.SeasonJewelryConfigInfoM = newSeasonJewelryConfigInfoMEx(data)
	data.SeasonDoorBuffInfoM = newSeasonDoorBuffInfoManager(data)
	data.SeasonDoorConfigInfoM = newSeasonDoorConfigInfoManager(data)
	data.SeasonDoorInfoM = newSeasonDoorInfoManager(data)
	data.SeasonDoorRestInfoM = newSeasonDoorRestInfoManager(data)
	data.SeasonDoorTaskInfoM = newSeasonDoorTaskInfoManager(data)
	data.SeasonDoorBossWeakInfoM = newSeasonDoorBossWeakInfoM(data)
	data.TitleInfoM = newTitleInfoMEx(data)
	data.RechargeRefundInfoM = newRechargeRefundInfoManager(data)
	data.ActivityWebInfoM = newActivityWebInfoManager(data)
	data.ShootGameStageInfoM = newShootGameStageInfoManager(data)
	data.ActivitySeasonShopInfoM = newActivitySeasonShopInfoMEx(data)
	data.ActivitySeasonShopGoodsInfoM = newActivitySeasonShopGoodsInfoMEx(data)
	data.ActivityRankTimelimitInfoM = newActivityRankTimelimitInfoManager(data)
	data.DailyAttendanceHeroRewardInfoM = newDailyAttendanceHeroRewardInfoManager(data)
	data.SeasonMapConfigInfoM = newSeasonMapConfigInfoManager(data)
	data.SeasonMapPositionInfoM = newSeasonMapPositionInfoManager(data)
	data.SeasonMapMonsterInfoM = newSeasonMapMonsterInfoManager(data)
	data.SeasonMapConnectInfoM = newSeasonMapConnectInfoManager(data)
	data.SeasonMapMasterInfoM = newSeasonMapMasterInfoManager(data)
	data.SeasonMapTradeInfoM = newSeasonMapTradeInfoManager(data)
	data.SeasonMapAltarBuffInfoM = newSeasonMapAltarBuffInfoManager(data)
	data.SeasonMapAltarInfoM = newSeasonMapAltarInfoManager(data)
	data.SeasonMapMonsterHpInfoM = newSeasonMapMonsterHpInfoM(data)
	data.SeasonMapMonsterRewardInfoM = newSeasonMapMonsterRewardInfoMEx(data)
	data.SeasonMapTaskInfoM = newSeasonMapTaskInfoManager(data)
	data.SeasonMapTradeEventInfoM = newSeasonMapTradeEventInfoManager(data)
	data.SeasonMapTradeGoodsInfoM = newSeasonMapTradeGoodsInfoManager(data)
	data.SeasonMapTriggerInfoM = newSeasonMapTriggerInfoManager(data)
	data.SeasonMapDailyMoneyInfoM = newSeasonMapDailyMoneyInfoM(data)
	data.PokemonInfoM = newPokemonInfoMEx(data)
	data.PokemonFragmentInfoM = newPokemonFragmentInfoM(data)
	data.PokemonBallInfoM = newPokemonBallInfoMEx(data)
	data.PokemonStarInfoM = newPokemonStarInfoMEx(data)
	data.PokemonPotentialInfoM = newPokemonPotentialInfoMEx(data)
	data.PokemonSkillInfoM = newPokemonSkillInfoM(data)
	data.PokemonSkillLevelInfoM = newPokemonSkillLevelInfoMEx(data)
	data.PokemonMasterInfoM = newPokemonMasterInfoMEx(data)
	data.TowerPokemonDungeonInfoM = newTowerPokemonDungeonInfoManager(data)
	data.TowerPokemonFightEffectInfoM = newTowerPokemonFightEffectInfoManager(data)
	data.TowerPokemonTaskInfoM = newTowerPokemonTaskInfoManager(data)
	data.TowerPokemonRankInfoM = newTowerPokemonRankInfoManager(data)
	data.PokemonSummonConfigInfoM = newPokemonSummonConfigInfoManager(data)
	data.PokemonSummonInfoM = newPokemonSummonInfoManager(data)
	data.TowerPokemonGoodsInfoM = newTowerPokemonGoodsInfoManager(data)
	data.OfflineVipAwardInfoM = newOfflineVipAwardInfoMEx(data)
	data.ResourcesWarnInfoM = newResourcesWarnInfoM(data)
	data.SynthesisGameConfigInfoM = newSynthesisGameConfigInfoMEx(data)
	data.HeroRareUpInfoM = newHeroRareUpInfoM(data)
	data.BalanceArenaInfoM = newBalanceArenaInfoMEx(data)
	data.BalanceArenaPhaseInfoM = newBalanceArenaPhaseInfoMEx(data)
	data.BalanceArenaRewardInfoM = newBalanceArenaRewardInfoM(data)
	data.BalanceArenaRankRewardInfoM = newBalanceArenaRankRewardInfoM(data)
	data.BalanceArenaBotInfoM = newBalanceArenaBotInfoM(data)
	data.BalanceArenaConfigInfoM = newBalanceArenaConfigInfoM(data)
}

func GetData() *XmlData {
	return gData.Load()
}

// isForCheck是标识是否是表检查机制的load.在策划本地检查配置表的时候，有几个表策划本地不存在
func Load(dir string, isShow, isForCheck bool) {
	data := LoadNotStore(dir, isShow, isForCheck)
	Store(data)

	l4g.Infof("load config success...")
}

func LoadNotStore(dir string, isShow, isForCheck bool) *XmlData {
	data := &XmlData{
		managers:    make([]manager, 0, 512),
		managersMap: make(map[string]manager, 512),
	}
	data.Init()

	for _, m := range data.managers {
		if isForCheck && m.ignoreForCheck() {
			continue
		}
		err := m.load(dir, isShow)
		if err != nil {
			l4g.Errorf("table:%s load failed. err:%s", m.name(), err)
			// return errors.Join(fmt.Errorf("table:%s load failed", m.name()), err)
			panic(fmt.Sprintf("table:%s load failed. err:%s", m.name(), err))
		}
	}
	for _, m := range data.managers {
		if isForCheck && m.ignoreForCheck() {
			continue
		}
		err := m.checkData()
		if err != nil {
			l4g.Errorf("table:%s checkData failed. err:%s", m.name(), err)
			// return errors.Join(fmt.Errorf("table:%s checkData failed", m.name()), err)
			panic(fmt.Sprintf("table:%s checkData failed. err:%s", m.name(), err))
		}
	}

	l4g.Infof("load and not store config success...")

	return data
}

func Store(data *XmlData) {
	gData.Store(data)
}
