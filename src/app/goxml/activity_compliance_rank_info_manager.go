package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityComplianceRankInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ActivityComplianceRankInfo
	sort    []*ActivityComplianceRankInfo
}

func newActivityComplianceRankInfoManager(xmlData *XmlData) *ActivityComplianceRankInfoManager {
	m := &ActivityComplianceRankInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityComplianceRankInfoManager) name() string {
	return "activity_compliance_rank_info.xml"
}

func (m *ActivityComplianceRankInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityComplianceRankInfoManager) checkData() error {
	return nil
}

func (m *ActivityComplianceRankInfoManager) load(dir string, show bool) error {
	tmp := &ActivityComplianceRankInfos{}
	fileName := filepath.Join(dir, "activity_compliance_rank_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ActivityComplianceRankInfo, len(tmp.Datas))
	}
	m.sort = make([]*ActivityComplianceRankInfo, 0, len(tmp.Datas))
	var rankCheck uint32
	for _, data := range tmp.Datas {
		data.prepare()
		if data.RankMax <= rankCheck {
			panic(fmt.Sprintf("config:%s data:%d invalid rank check: %d", fileName, data.Id, data.RankMax))
		}
		rankCheck = data.RankMax
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
		m.sort = append(m.sort, data)
	}
	return nil
}

func (m *ActivityComplianceRankInfoManager) Index(key uint32) *ActivityComplianceRankInfo {
	return m.Datas[key]
}

func (m *ActivityComplianceRankInfoManager) GetRankAward(rank uint32) *ActivityComplianceRankInfo {
	return GetDataLessAndClosedByRank(m.sort, rank)
}

func (m *ActivityComplianceRankInfo) GetRank() uint32 {
	return m.RankMax
}
