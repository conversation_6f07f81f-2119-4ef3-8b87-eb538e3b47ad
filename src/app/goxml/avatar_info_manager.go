package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
)

const DefaultBaseId = uint32(common.AVATAR_DEFAULT_FRAME)<<16 + uint32(common.AVATAR_DEFAULT_ICON)

type AvatarInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*AvatarInfo

	avatarType    map[uint32]map[uint32]*AvatarInfo // type -> Value -> Info
	skinAvatarIDs map[uint32][]uint32               //skinID => avatarID列表
}

func newAvatarInfoManager(xmlData *XmlData) *AvatarInfoManager {
	m := &AvatarInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *AvatarInfoManager) name() string {
	return "AvatarInfo"
}

func (m *AvatarInfoManager) ignoreForCheck() bool {
	return false
}

func (m *AvatarInfoManager) checkData() error {
	return nil
}

func (m *AvatarInfoManager) load(dir string, isShow bool) error {
	tmp := &AvatarInfos{}
	fileName := filepath.Join(dir, "avatar_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*AvatarInfo, len(tmp.Datas))
	}

	m.avatarType = make(map[uint32]map[uint32]*AvatarInfo)
	m.skinAvatarIDs = make(map[uint32][]uint32)
	hasDefaultIcon := false
	hasDefaultFrame := false
	hasDefaultImage := false
	for _, data := range tmp.Datas {
		if data.Id >= 1<<16 { //刚好是超过16位，取到第17位了
			panic(fmt.Sprintf("fileName:%s avatar data load  fail: id %d larger than 2^16", fileName, data.Id))
		}

		if data.Type == AvatarTypeIcon || data.Type == AvatarTypeFrame {
			if data.Id > 32767 { // 前端要求，不能超过这个值，不然会报错
				panic(fmt.Sprintf("fileName:%s avatar data load  fail: id %d larger 33000 ", fileName, data.Id))
			}
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			m.Datas[data.Id] = ptr
		} else {
			m.Datas[data.Id] = data
		}

		if data.Id == uint32(common.AVATAR_DEFAULT_ICON) && data.UnlockType == AvatarUnlockTypeNone {
			hasDefaultIcon = true
		}
		if data.Id == uint32(common.AVATAR_DEFAULT_FRAME) && data.UnlockType == AvatarUnlockTypeNone {
			hasDefaultFrame = true
		}
		if data.Id == uint32(common.AVATAR_DEFAULT_IMAGE) && data.UnlockType == AvatarUnlockTypeNone {
			hasDefaultImage = true
		}

		// 解锁类型是点亮图鉴的，走下面的逻辑
		if data.UnlockType == AvatarUnlockTypeHero {
			if heroType, exist := m.avatarType[data.Type]; exist {
				if value, exist := heroType[data.Value]; exist {
					panic(fmt.Sprintf("avatar data:%d %d has same UnlockType and value", data.Id, value.Id))
				} else {
					heroType[data.Value] = data
				}
			} else {
				tmp := make(map[uint32]*AvatarInfo)
				tmp[data.Value] = data
				m.avatarType[data.Type] = tmp
			}
		}

		//皮肤类型的avatar
		if data.UnlockType == AvatarUnlockTypeSkin {
			if _, exist := m.skinAvatarIDs[data.Value]; !exist {
				m.skinAvatarIDs[data.Value] = make([]uint32, 0, 2)
			}
			m.skinAvatarIDs[data.Value] = append(m.skinAvatarIDs[data.Value], data.Id)
		}
	}

	if !hasDefaultIcon {
		panic("avatar data load no find default icon id or default unlock icon type error")
	}
	if !hasDefaultFrame {
		panic("avatar data load no find default frame id or default unlock frame type error")
	}
	if !hasDefaultImage {
		panic("avatar data load no find default image id or default unlock image type error")
	}
	return nil
}

func (m *AvatarInfoManager) GetRecordById(id uint32) *AvatarInfo {
	return m.Datas[id]
}

func (m *AvatarInfoManager) Index(key uint32) *AvatarInfo {
	return m.Datas[key]
}

func (m *AvatarInfoManager) TypeProgress(Type, progress uint32) *AvatarInfo {
	ptr, exist := m.avatarType[Type]
	if !exist {
		return nil
	}

	return ptr[progress]
}

func (m *AvatarInfoManager) GetAvatarResourceByNewHero(sysID uint32) []*cl.Resource {
	res := make([]*cl.Resource, 0, 2)

	iconInfos, exist := m.avatarType[AvatarTypeIcon]
	if exist {
		avatarInfo := iconInfos[sysID]
		if avatarInfo != nil {
			// 这里是永久的头像，所以resource的attr不需要赋值
			res = append(res, &cl.Resource{Type: uint32(common.RESOURCE_AVATAR), Value: avatarInfo.Id, Count: 1})
		}
	}

	imageInfos, exist := m.avatarType[AvatarTypeImage]
	if exist {
		avatarInfo := imageInfos[sysID]
		if avatarInfo != nil {
			// 这里是永久的头像，所以resource的attr不需要赋值
			res = append(res, &cl.Resource{Type: uint32(common.RESOURCE_AVATAR), Value: avatarInfo.Id, Count: 1})
		}
	}

	return res
}

// 获取皮肤类型avatar的resource
func (m *AvatarInfoManager) GetSkinAvatarRes(skinID uint32) []*cl.Resource {
	ret := make([]*cl.Resource, 0, 1)
	for _, id := range m.skinAvatarIDs[skinID] {
		ret = append(ret, GenSimpleResource(uint32(common.RESOURCE_AVATAR), id, 1))
	}
	return ret
}

func (m *AvatarInfoManager) GetRandomAvatarByTypeUnlockType(rand *rand.Rand, tp, unlockType uint32) uint32 {
	valueData, exist := m.avatarType[tp]
	if !exist {
		return 0
	}

	avatars := make([]uint32, 0, len(valueData))
	for _, data := range valueData {
		if data.UnlockType != unlockType {
			continue
		}
		avatars = append(avatars, data.Id)
	}

	if len(avatars) == 0 {
		return 0
	}

	// 从avatar中随机一个
	idx := rand.Intn(len(avatars))
	return avatars[idx]
}
