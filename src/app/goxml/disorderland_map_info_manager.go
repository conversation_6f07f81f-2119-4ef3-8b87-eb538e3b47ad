package goxml

import (
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	"gitlab.qdream.com/kit/sea/util"

	l4g "github.com/ivanabc/log4go"
)

type DisorderlandMapInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]map[uint32]*DisorderlandMapInfoExt // key: map_id -> key: node_id
}

type DisorderlandMapInfoExt struct {
	MapId      uint32
	Id         uint32   //int:点id
	EventType  uint32   //int:事件类型
	EventId    uint32   //int:事件id
	UnlockType uint32   //int:解锁类型
	Points     []uint32 // int:相邻点
}

func newDisorderlandMapInfoManager(xmlData *XmlData) *DisorderlandMapInfoManager {
	m := &DisorderlandMapInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DisorderlandMapInfoManager) name() string {
	return "DisorderlandMapInfo"
}

func (m *DisorderlandMapInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DisorderlandMapInfoManager) checkData() error {
	return nil
}

func (m *DisorderlandMapInfoManager) load(dir string, isShow bool) error {
	tmp := &DisorderlandMapInfos{}
	fileName := filepath.Join(dir, "disorderland_map_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]map[uint32]*DisorderlandMapInfoExt, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()

		if data.EventType == 0 || data.EventType >= uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_MAX) {
			panic(fmt.Sprintf("load config %s fail: eventType is invalid. mapID: %d, type: %d", fileName, data.MapId, data.EventType))
		}

		if data.UnlockType == 0 || data.UnlockType >= uint32(common.DISORDER_LAND_NODE_UNLOCK_DLNU_MAX) {
			panic(fmt.Sprintf("load config %s fail: unlockType is invalid. mapID: %d, type: %d", fileName, data.MapId, data.UnlockType))
		}

		if data.EventType == uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_STONE) {
			//if data.UnlockType != uint32(common.DISORDER_LAND_NODE_UNLOCK_DLNU_ALL) {
			//	panic(fmt.Sprintf("load config %s fail: unlockType is invalid. mapID: %d, type: %d", fileName, data.MapId, data.UnlockType))
			//}
			//if m.xmlData.DisorderlandStoneInfoM.Index(data.EventId) == nil {
			//	panic(fmt.Sprintf("load config %s fail: stone is invalid. mapID: %d, eventID: %d", fileName, data.MapId, data.EventId))
			//}
		} else if data.EventType == uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_BOX) {
			if data.UnlockType != uint32(common.DISORDER_LAND_NODE_UNLOCK_DLNU_ANY) {
				panic(fmt.Sprintf("load config %s fail: unlockType is invalid. mapID: %d, type: %d", fileName, data.MapId, data.UnlockType))
			}
			if m.xmlData.DisorderlandBoxInfoM.Index(data.EventId) == nil {
				panic(fmt.Sprintf("load config %s fail: box is invalid. mapID: %d, boxID: %d", fileName, data.MapId, data.EventId))
			}
		} else if data.EventType == uint32(common.DISORDER_LAND_EVENT_TYPE_DLET_BATTLE) {
			if m.xmlData.DisorderlandDungeonInfoM.Index(data.EventId) == nil {
				panic(fmt.Sprintf("load config %s fail: dungeonID is invalid. mapID: %d, dungeonID: %d", fileName, data.MapId, data.EventId))
			}
		}

		dataExt := &DisorderlandMapInfoExt{
			MapId:      data.MapId,
			Id:         data.Id,
			EventType:  data.EventType,
			EventId:    data.EventId,
			UnlockType: data.UnlockType,
			Points:     make([]uint32, 0, 8),
		}
		dataExt.putNode(data.Point1)
		dataExt.putNode(data.Point2)
		dataExt.putNode(data.Point3)
		dataExt.putNode(data.Point4)
		dataExt.putNode(data.Point5)
		dataExt.putNode(data.Point6)
		dataExt.putNode(data.Point7)
		dataExt.putNode(data.Point8)

		if _, exist := m.Datas[dataExt.MapId]; !exist {
			m.Datas[dataExt.MapId] = make(map[uint32]*DisorderlandMapInfoExt)
		}
		m.Datas[dataExt.MapId][dataExt.Id] = dataExt
	}
	return nil
}

func (e *DisorderlandMapInfoExt) putNode(id uint32) {
	if id > 0 {
		e.Points = append(e.Points, id)
	}
}

func (m *DisorderlandMapInfoManager) Index(mapID, nodeID uint32) *DisorderlandMapInfoExt {
	return m.Datas[mapID][nodeID]
}

func (m *DisorderlandMapInfoManager) GetNodeNum(mapID uint32) uint32 {
	return uint32(len(m.Datas[mapID]))
}
