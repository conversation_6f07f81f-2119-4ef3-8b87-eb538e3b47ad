package goxml

import "app/protos/out/cl"

type GuildSandTableDragonScheduleInfoMEx struct {
	*GuildSandTableDragonScheduleInfoM
}

func newGuildSandTableDragonScheduleInfoMEx(xmlData *XmlData) *GuildSandTableDragonScheduleInfoMEx {
	m := &GuildSandTableDragonScheduleInfoMEx{
		GuildSandTableDragonScheduleInfoM: &GuildSandTableDragonScheduleInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableDragonScheduleInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableDragonScheduleInfoMEx) load(dir string, isShow bool) error {
	err := m.GuildSandTableDragonScheduleInfoM.load(dir, isShow)
	if err != nil {
		return err
	}

	for _, k := range m.idIndexMap {
		for _, v := range k.seasonIdRecordMap {
			if v.WinRewardType1 > 0 && v.WinRewardCount1 > 0 {
				v.WinAwards = append(v.WinAwards, &cl.Resource{
					Type:  v.WinRewardType1,
					Value: v.WinRewardValue1,
					Count: v.WinRewardCount1,
				})
			}
			if v.WinRewardType2 > 0 && v.WinRewardCount2 > 0 {
				v.WinAwards = append(v.WinAwards, &cl.Resource{
					Type:  v.WinRewardType2,
					Value: v.WinRewardValue2,
					Count: v.WinRewardCount2,
				})
			}
			if v.LoseRewardType1 > 0 && v.LoseRewardCount1 > 0 {
				v.LoseAwards = append(v.LoseAwards, &cl.Resource{
					Type:  v.LoseRewardType1,
					Value: v.LoseRewardValue1,
					Count: v.LoseRewardCount1,
				})
			}
			if v.LoseRewardType2 > 0 && v.LoseRewardCount2 > 0 {
				v.LoseAwards = append(v.LoseAwards, &cl.Resource{
					Type:  v.LoseRewardType2,
					Value: v.LoseRewardValue2,
					Count: v.LoseRewardCount2,
				})
			}
		}
	}

	return nil
}

func (m *GuildSandTableDragonScheduleInfoMEx) checkData() error {
	err := m.GuildSandTableDragonScheduleInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}
