package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ChatLanguageInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ChatLanguageInfo
	langs   map[string]*ChatLanguageInfo
}

func newChatLanguageInfoManager(xmlData *XmlData) *ChatLanguageInfoManager {
	m := &ChatLanguageInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ChatLanguageInfoManager) name() string {
	return "ChatLanguageInfo"
}

func (m *ChatLanguageInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ChatLanguageInfoManager) checkData() error {
	return nil
}

func (m *ChatLanguageInfoManager) load(dir string, isShow bool) error {
	tmp := &ChatLanguageInfos{}
	fileName := filepath.Join(dir, "chat_language_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ChatLanguageInfo, len(tmp.Datas))
	}
	m.langs = make(map[string]*ChatLanguageInfo, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		m.langs[data.LanguageName] = data

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *ChatLanguageInfoManager) GetRecordById(id uint32) *ChatLanguageInfo {
	return m.Datas[id]
}

func (m *ChatLanguageInfoManager) Index(key uint32) *ChatLanguageInfo {
	return m.Datas[key]
}

func (m *ChatLanguageInfoManager) GetLanguageInfo(lang string) *ChatLanguageInfo {
	return m.langs[lang]
}
