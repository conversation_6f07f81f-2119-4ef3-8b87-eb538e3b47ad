package goxml

import (
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type GuildLabelInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildLabelInfo
	maxID   uint32
}

func newGuildLabelInfoManager(xmlData *XmlData) *GuildLabelInfoManager {
	m := &GuildLabelInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildLabelInfoManager) name() string {
	return "GuildLabelInfo"
}

func (m *GuildLabelInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildLabelInfoManager) checkData() error {
	return nil
}

func (m *GuildLabelInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildLabelInfos{}
	fileName := filepath.Join(dir, "guild_label_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GuildLabelInfo, len(tmp.Datas))
	}

	m.maxID = 0

	for _, data := range tmp.Datas {

		if data.Id < m.maxID {
			panic(fmt.Sprintf("check data error. filename:%s id:%d", fileName, data.Id))
		}

		m.maxID = data.Id

		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildLabelInfoManager) GetRecordById(id uint32) *GuildLabelInfo {
	return m.Datas[id]
}

func (m *GuildLabelInfoManager) Index(key uint32) *GuildLabelInfo {
	return m.Datas[key]
}

// 推荐列表排序用
func (m *GuildLabelInfoManager) GetMaxLabelID() uint32 {
	return m.maxID
}
