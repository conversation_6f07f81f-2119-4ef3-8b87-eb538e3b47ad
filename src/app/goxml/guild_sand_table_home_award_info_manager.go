package goxml

import (
	"errors"
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type GuildSandTableHomeAwardInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildSandTableHomeAwardInfo
}

func newGuildSandTableHomeAwardInfoManager(xmlData *XmlData) *GuildSandTableHomeAwardInfoManager {
	m := &GuildSandTableHomeAwardInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableHomeAwardInfoManager) name() string {
	return "guild_sand_table_home_award_info"
}

func (m *GuildSandTableHomeAwardInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableHomeAwardInfoManager) checkData() error {
	for _, data := range m.Datas {
		info := m.xmlData.DropGroupInfoM.Group(data.DropGroup1)
		if info == nil {
			return errors.New(fmt.Sprintf("home_award_group id:%d group id1:%d", data.HomeAwardGroup, data.DropGroup1))
		}
		info = m.xmlData.DropGroupInfoM.Group(data.DropGroup2)
		if info == nil {
			return errors.New(fmt.Sprintf("home_award_group id:%d group id2:%d", data.HomeAwardGroup, data.DropGroup2))
		}
	}
	return nil
}

func (g *GuildSandTableHomeAwardInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableHomeAwardInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_home_award_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	g.Datas = make(map[uint32]*GuildSandTableHomeAwardInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		_, exist := g.Datas[data.HomeAwardGroup]
		if exist {
			panic(fmt.Sprintf("file %s home_award_group id:%d is repeated", fileName, data.HomeAwardGroup))
		}
		g.Datas[data.HomeAwardGroup] = data
	}
	return nil
}

func (g *GuildSandTableHomeAwardInfoManager) GetDropInfo(homeAwardGroup uint32) *GuildSandTableHomeAwardInfo {
	return g.Datas[homeAwardGroup]
}
