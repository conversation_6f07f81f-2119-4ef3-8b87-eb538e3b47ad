package goxml

import (
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type PokemonSummonConfigInfoManager struct {
	xmlData                           *XmlData
	SummonPlural                      uint32                      // 连抽次数
	FirstDisplayWishHeroMaxSummonCost uint32                      // 第一次出现心愿将保底抽卡次数
	Chance60                          uint32                      // 红色卡池初始权重
	ChanceNode60                      uint32                      // 红色卡池连续N抽不中后开始提升权重
	ChanceAdd60                       uint32                      // 红色卡池每次提升权重值
	GroupID60Guarantee                uint32                      // 红色卡池保底次数
	Chance50                          uint32                      // 橙色卡池初始权重
	ChanceNode50                      uint32                      // 橙色卡池连续N抽不中后开始提升权重
	ChanceAdd50                       uint32                      // 橙色卡池每次提升权重值
	GroupID50Guarantee                uint32                      // 橙色卡池保底次数
	Chance40                          uint32                      // 紫色卡池初始权重
	ChanceNode40                      uint32                      // 紫色卡池连续N抽不中后开始提升权重
	ChanceAdd40                       uint32                      // 紫色卡池每次提升权重值
	GroupID40Guarantee                uint32                      // 紫色卡池保底次数
	Chance30                          uint32                      // 蓝色卡池初始权重
	ChanceNode30                      uint32                      // 蓝色卡池连续N抽不中后开始提升权重
	ChanceAdd30                       uint32                      // 蓝色卡池每次提升权重值
	GroupID30Guarantee                uint32                      // 红色卡池保底次数
	FragmentChance60                  uint32                      // 红色碎片卡池初始权重
	FragmentChanceNode60              uint32                      // 红色碎片卡池连续N抽不中后开始提升权重
	FragmentChanceAdd60               uint32                      // 红色碎片卡池每次提升权重值
	FragmentGroupID60Guarantee        uint32                      // 红色卡池碎片保底次数
	FragmentChance50                  uint32                      // 橙色碎片卡池初始权重
	FragmentChanceNode50              uint32                      // 橙色碎片卡池连续N抽不中后开始提升权重
	FragmentChanceAdd50               uint32                      // 橙色碎片卡池每次提升权重值
	FragmentGroupID50Guarantee        uint32                      // 橙色卡池碎片保底次数
	FragmentChance40                  uint32                      // 橙色碎片卡池初始权重
	FragmentChanceNode40              uint32                      // 橙色碎片卡池连续N抽不中后开始提升权重
	FragmentChanceAdd40               uint32                      // 橙色碎片卡池每次提升权重值
	FragmentGroupID40Guarantee        uint32                      // 橙色卡池碎片保底次数
	guarantee                         map[int]uint32              // 各个卡池的保底次数
	weightFunc                        map[int]func(uint32) uint32 // 各个卡池的权重
	DailyFreeTime                     uint32                      // 本期免费次数上限
	DiamondSummonCountLimit           uint32                      // 钻石抽奖限制
	SummonCostDiamondCount            uint32
	firstDraw                         uint32
	WishFragmentCount1                uint32
	WishFragmentWeight1               uint32
	WishFragmentCount2                uint32
	WishFragmentWeight2               uint32
	WishFragmentCount3                uint32
	WishFragmentWeight3               uint32
	WishFragmentCount4                uint32
	WishFragmentWeight4               uint32
	WishFragmentCount5                uint32
	WishFragmentWeight5               uint32
	fragmentWeight                    []*WeightAndId
	fragmentTotalWeight               uint32
}

func newPokemonSummonConfigInfoManager(xmlData *XmlData) *PokemonSummonConfigInfoManager {
	m := &PokemonSummonConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *PokemonSummonConfigInfoManager) name() string {
	return "pokemon_summon_config_info.xml"
}

func (m *PokemonSummonConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *PokemonSummonConfigInfoManager) checkData() error {
	return nil
}

func (m *PokemonSummonConfigInfoManager) load(dir string, show bool) error {
	tmp := &PokemonSummonConfigInfos{}
	fileName := filepath.Join(dir, "pokemon_summon_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	for _, data := range tmp.Datas {
		switch data.Key {
		case "SUMMON_PLURAL":
			m.SummonPlural = data.ConfigValue

		case "INT_CHANCE_60":
			m.Chance60 = data.ConfigValue
		case "CHANCE_NODE_60":
			m.ChanceNode60 = data.ConfigValue
		case "CHANCE_ADD_60":
			m.ChanceAdd60 = data.ConfigValue
		case "GROUP_ID_60_GUARANTEE":
			m.GroupID60Guarantee = data.ConfigValue

		case "INT_CHANCE_50":
			m.Chance50 = data.ConfigValue
		case "CHANCE_NODE_50":
			m.ChanceNode50 = data.ConfigValue
		case "CHANCE_ADD_50":
			m.ChanceAdd50 = data.ConfigValue
		case "GROUP_ID_50_GUARANTEE":
			m.GroupID50Guarantee = data.ConfigValue

		case "INT_CHANCE_40":
			m.Chance40 = data.ConfigValue
		case "CHANCE_NODE_40":
			m.ChanceNode40 = data.ConfigValue
		case "CHANCE_ADD_40":
			m.ChanceAdd40 = data.ConfigValue
		case "GROUP_ID_40_GUARANTEE":
			m.GroupID40Guarantee = data.ConfigValue

		case "INT_CHANCE_30":
			m.Chance30 = data.ConfigValue
		case "CHANCE_NODE_30":
			m.ChanceNode30 = data.ConfigValue
		case "CHANCE_ADD_30":
			m.ChanceAdd30 = data.ConfigValue
		case "GROUP_ID_30_GUARANTEE":
			m.GroupID30Guarantee = data.ConfigValue

		case "INT_CHANCE_FRAGMENT_60":
			m.FragmentChance60 = data.ConfigValue
		case "INT_CHANCE_FRAGMENT_50":
			m.FragmentChance50 = data.ConfigValue
		case "INT_CHANCE_FRAGMENT_40":
			m.FragmentChance40 = data.ConfigValue

		case "DAILY_FREE_TIMES":
			m.DailyFreeTime = data.ConfigValue

		case "DIAMOND_SUMMON_COUNT_LIMIT":
			m.DiamondSummonCountLimit = data.ConfigValue
		case "SUMMON_COST_DIAMOND_COUNT":
			m.SummonCostDiamondCount = data.ConfigValue
		case "FIRST_DRAW":
			m.firstDraw = data.ConfigValue

		case "WISH_FRAGMENT_COUNT_1":
			m.WishFragmentCount1 = data.ConfigValue
		case "WISH_FRAGMENT_COUNT_2":
			m.WishFragmentCount2 = data.ConfigValue
		case "WISH_FRAGMENT_COUNT_3":
			m.WishFragmentCount3 = data.ConfigValue
		case "WISH_FRAGMENT_COUNT_4":
			m.WishFragmentCount4 = data.ConfigValue
		case "WISH_FRAGMENT_COUNT_5":
			m.WishFragmentCount5 = data.ConfigValue
		case "WISH_FRAGMENT_WEIGHT_1":
			m.WishFragmentWeight1 = data.ConfigValue
		case "WISH_FRAGMENT_WEIGHT_2":
			m.WishFragmentWeight2 = data.ConfigValue
		case "WISH_FRAGMENT_WEIGHT_3":
			m.WishFragmentWeight3 = data.ConfigValue
		case "WISH_FRAGMENT_WEIGHT_4":
			m.WishFragmentWeight4 = data.ConfigValue
		case "WISH_FRAGMENT_WEIGHT_5":
			m.WishFragmentWeight5 = data.ConfigValue
		}
	}

	if m.SummonPlural == 0 {
		panic(fmt.Sprintf("load config %s fail: SUMMON_PLURAL is zero", fileName))
	}

	m.guarantee = make(map[int]uint32)
	m.guarantee[PokemonSummonRedGuaranteeIndex] = m.GroupID60Guarantee
	m.guarantee[PokemonSummonOrangeGuaranteeIndex] = m.GroupID50Guarantee
	m.guarantee[PokemonSummonPurpleGuaranteeIndex] = m.GroupID40Guarantee
	m.guarantee[PokemonSummonBlueGuaranteeIndex] = m.GroupID30Guarantee

	m.weightFunc = make(map[int]func(uint32) uint32)
	m.weightFunc[PokemonSummonRedGuaranteeIndex] = func(count uint32) uint32 {
		ret := m.Chance60
		if count > m.ChanceNode60 {
			ret += (count - m.ChanceNode60) * m.ChanceAdd60
		}
		return ret
	}
	m.weightFunc[PokemonSummonOrangeGuaranteeIndex] = func(count uint32) uint32 {
		ret := m.Chance50
		if count > m.ChanceNode50 {
			ret += (count - m.ChanceNode50) * m.ChanceAdd50
		}
		return ret
	}
	m.weightFunc[PokemonSummonPurpleGuaranteeIndex] = func(count uint32) uint32 {
		ret := m.Chance40
		if count > m.ChanceNode40 {
			ret += (count - m.ChanceNode40) * m.ChanceAdd40
		}
		return ret
	}
	m.weightFunc[PokemonSummonBlueGuaranteeIndex] = func(count uint32) uint32 {
		ret := m.Chance30
		if count > m.ChanceNode30 {
			ret += (count - m.ChanceNode30) * m.ChanceAdd30
		}
		return ret
	}
	m.weightFunc[PokemonSummonPurpleFragmentGuaranteeIndex] = func(count uint32) uint32 {
		return m.FragmentChance40
	}
	m.weightFunc[PokemonSummonOrangeFragmentGuaranteeIndex] = func(count uint32) uint32 {
		return m.FragmentChance50
	}
	m.weightFunc[PokemonSummonRedFragmentGuaranteeIndex] = func(count uint32) uint32 {
		return m.FragmentChance60
	}
	m.fragmentWeight = append(m.fragmentWeight, &WeightAndId{
		Weight: m.WishFragmentWeight1,
		Id:     m.WishFragmentCount1,
	})
	m.fragmentTotalWeight += m.WishFragmentWeight1
	m.fragmentWeight = append(m.fragmentWeight, &WeightAndId{
		Weight: m.WishFragmentWeight2,
		Id:     m.WishFragmentCount2,
	})
	m.fragmentTotalWeight += m.WishFragmentWeight2
	m.fragmentWeight = append(m.fragmentWeight, &WeightAndId{
		Weight: m.WishFragmentWeight3,
		Id:     m.WishFragmentCount3,
	})
	m.fragmentTotalWeight += m.WishFragmentWeight3
	m.fragmentWeight = append(m.fragmentWeight, &WeightAndId{
		Weight: m.WishFragmentWeight4,
		Id:     m.WishFragmentCount4,
	})
	m.fragmentTotalWeight += m.WishFragmentWeight4
	m.fragmentWeight = append(m.fragmentWeight, &WeightAndId{
		Weight: m.WishFragmentWeight5,
		Id:     m.WishFragmentCount5,
	})
	m.fragmentTotalWeight += m.WishFragmentWeight5
	return nil
}

func (m *PokemonSummonConfigInfoManager) Guarantee(guaranteeType int, guaranteeCount uint32) bool {
	_, exist := m.guarantee[guaranteeType]
	if !exist {
		return false
	}
	return guaranteeCount >= m.guarantee[guaranteeType]
}

func (m *PokemonSummonConfigInfoManager) GetWeight(guaranteeType int, guaranteeCount uint32) uint32 {
	fun, exist := m.weightFunc[guaranteeType]
	if !exist {
		return 0
	}
	return fun(guaranteeCount)
}

func (m *PokemonSummonConfigInfoManager) FreeSummonCheck(count uint32) bool {
	return count <= m.DailyFreeTime
}

func (m *PokemonSummonConfigInfoManager) IsFirstDraw(count uint32) bool {
	return count >= m.firstDraw
}

func (m *PokemonSummonConfigInfoManager) RandomFragmentCount(rd *rand.Rand) uint32 {
	randomNum := rd.RandBetween(1, int(m.fragmentTotalWeight))
	for _, v := range m.fragmentWeight {
		if randomNum > 0 && randomNum <= int(v.Weight) {
			return v.Id
		}
		randomNum -= int(v.Weight)
	}
	return 0
}
