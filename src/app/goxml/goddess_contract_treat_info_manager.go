package goxml

import (
	"fmt"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
	"path/filepath"
)

type GoddessContractTreatInfoManager struct {
	xmlData          *XmlData
	Datas            map[uint32]*GoddessContractTreatInfo
	GoddessMaxTimes  map[uint32]uint32                               //goddess maxTimes
	GoddessTimesData map[uint32]map[uint32]*GoddessContractTreatInfo //goddess times
}

func newGoddessContractTreatInfoManager(xmlData *XmlData) *GoddessContractTreatInfoManager {
	m := &GoddessContractTreatInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GoddessContractTreatInfoManager) name() string {
	return "GoddessContractTreatInfo"
}

func (m *GoddessContractTreatInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GoddessContractTreatInfoManager) checkData() error {
	return nil
}

func (m *GoddessContractTreatInfoManager) load(dir string, isShow bool) error {
	tmp := &GoddessContractTreatInfos{}
	fileName := filepath.Join(dir, "goddess_contract_treat_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*GoddessContractTreatInfo, len(tmp.Datas))
	m.GoddessMaxTimes = make(map[uint32]uint32)
	m.GoddessTimesData = make(map[uint32]map[uint32]*GoddessContractTreatInfo)

	var goddess uint32
	var timesCheck uint32
	for _, data := range tmp.Datas {
		data.prepare()
		goddessInfo := m.xmlData.GoddessContractInfoM.Index(data.Goddess)
		if goddessInfo == nil {
			panic(fmt.Sprintf("data:%d goddess:%d is illegal", data.Id, data.Goddess))
		}

		if data.Goddess != goddess {
			goddess = data.Goddess
			timesCheck = 0
		}

		timesCheck++
		if timesCheck != data.TreatTimes {
			panic(fmt.Sprintf("data:%d goddess:%d treat_times discontinuity", data.Id, data.Goddess))
		}

		m.GoddessMaxTimes[goddess] = data.TreatTimes

		timesData, exist := m.GoddessTimesData[data.Goddess]
		if !exist {
			m.GoddessTimesData[data.Goddess] = make(map[uint32]*GoddessContractTreatInfo)
		}
		timesData = m.GoddessTimesData[data.Goddess]

		_, exist = timesData[data.TreatTimes]
		if exist {
			panic(fmt.Sprintf("data:%d goddess:%d times:%d treat_times repeated", data.Id, data.Goddess, data.TreatTimes))
		}
		timesData[data.TreatTimes] = data

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	l4g.Debugf("GoddessMaxTimes:%v", m.GoddessMaxTimes)
	return nil
}

func (m *GoddessContractTreatInfoManager) Index(index uint32) *GoddessContractTreatInfo {
	return m.Datas[index]
}

func (m *GoddessContractTreatInfoManager) GetGoddessTimes(goddessId uint32, treatCount uint32) *GoddessContractTreatInfo {
	return m.GoddessTimesData[goddessId][treatCount]
}

func (m *GoddessContractTreatInfoManager) IsRecoveryFinish(goddessId uint32, treatCount uint32) bool {
	return m.GoddessMaxTimes[goddessId] == treatCount
}
