package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ArtifactStarInfoExt struct {
	Id            uint32
	Star          uint32
	Attrs         []uint32
	CostClRes     []*cl.Resource
	RaisePsSkills []uint64
	Link1         uint32
	Link2         uint32
	CostValue     uint32 //int:消耗碎片（值）
	CostCount     uint32 //int:消耗碎片（数量）
}

type ArtifactStarInfoManager struct {
	xmlData           *XmlData
	Datas             map[uint32]map[uint32]*ArtifactStarInfoExt
	maxStar           map[uint32]uint32
	fragment2Artifact map[uint32]uint32 // key: fragmentID value: artifactID
}

func newArtifactStarInfoManager(xmlData *XmlData) *ArtifactStarInfoManager {
	m := &ArtifactStarInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ArtifactStarInfoManager) name() string {
	return "ArtifactStarInfo"
}

func (m *ArtifactStarInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ArtifactStarInfoManager) checkData() error {
	return nil
}

func (m *ArtifactStarInfoManager) load(dir string, isShow bool) error {
	tmp := &ArtifactStarInfos{}
	fileName := filepath.Join(dir, "artifact_star_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]map[uint32]*ArtifactStarInfoExt, len(tmp.Datas))
	}
	m.fragment2Artifact = make(map[uint32]uint32)
	m.maxStar = make(map[uint32]uint32)
	lastStar := make(map[uint32]uint32)
	for _, data := range tmp.Datas {
		dataExt := &ArtifactStarInfoExt{}

		data.prepare()

		if data.Star != ArtifactInitialStar && lastStar[data.Id]+1 != data.Star { // 确保连续且星级不会重复
			panic(fmt.Sprintf("load config %s fail: star is discontinuity, artifactId:%d star:%d",
				fileName, data.Id, data.Star))
		}
		lastStar[data.Id] = data.Star

		if data.Star > ArtifactInitialStar &&
			(m.Datas[data.Id][data.Star-1] == nil || len(m.Datas[data.Id][data.Star-1].CostClRes) == 0) { //确保每个星级都有消耗（除了最大级）
			panic(fmt.Sprintf("check data error %s cost is nil. artifactId:%d star:%d", fileName, data.Id, data.Star))
		}
		if m.maxStar[data.Id] < data.Star {
			m.maxStar[data.Id] = data.Star
		}
		dataExt.Id = data.Id
		dataExt.Star = data.Star
		dataExt.Link1 = data.Link1
		dataExt.Link2 = data.Link2
		dataExt.CostValue = data.CostValue
		dataExt.CostCount = data.CostCount
		if data.AttrType1 != 0 && data.AttrValue1 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType1)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue1)
		}
		if data.AttrType2 != 0 && data.AttrValue2 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType2)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue2)
		}
		if data.AttrType3 != 0 && data.AttrValue3 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType3)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue3)
		}
		if data.AttrType4 != 0 && data.AttrValue4 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType4)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue4)
		}
		if data.AttrType5 != 0 && data.AttrValue5 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType5)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue5)
		}
		if data.AttrType6 != 0 && data.AttrValue6 != 0 {
			dataExt.Attrs = append(dataExt.Attrs, data.AttrType6)
			dataExt.Attrs = append(dataExt.Attrs, data.AttrValue6)
		}

		if len(dataExt.Attrs) == 0 {
			panic(fmt.Sprintf("check data error %s attr is nil id:%d star:%d", fileName, data.Id, data.Star))
		}

		dataExt.CostClRes = data.CostClRes

		if data.RaisePassiveSkill1 != 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. link %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill1))
			}
			dataExt.RaisePsSkills = append(dataExt.RaisePsSkills, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill2 != 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill2, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. link %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill2))
			}
			dataExt.RaisePsSkills = append(dataExt.RaisePsSkills, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill3 != 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill3, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. link %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill3))
			}
			dataExt.RaisePsSkills = append(dataExt.RaisePsSkills, raisePSInfo.ID)
		}
		if data.RaisePassiveSkill4 != 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.RaisePassiveSkill4, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. link %d not in RaisePassiveSkillInfoM", fileName, data.RaisePassiveSkill4))
			}
			dataExt.RaisePsSkills = append(dataExt.RaisePsSkills, raisePSInfo.ID)
		}

		m.fragment2Artifact[data.CostValue] = data.Id

		if v, exist := m.Datas[data.Id]; exist {
			if ptr, exist := v[data.Star]; exist {
				*ptr = *dataExt
				dataExt = ptr
			} else {
				v[data.Star] = dataExt
			}
		} else {
			m.Datas[data.Id] = make(map[uint32]*ArtifactStarInfoExt)
			m.Datas[data.Id][data.Star] = dataExt
		}
	}
	return nil
}

func (m *ArtifactStarInfoManager) Index(id, star uint32) *ArtifactStarInfoExt {
	if m.Datas[id] == nil {
		return nil
	}
	return m.Datas[id][star]
}

func (m *ArtifactStarInfoManager) GetMaxStar(id uint32) uint32 {
	return m.maxStar[id]
}

func (m *ArtifactStarInfoManager) GetArtifactID(id uint32) uint32 {
	return m.fragment2Artifact[id]
}
