package goxml

type BalanceArenaInfoMEx struct {
	*BalanceArenaInfoM
}

func newBalanceArenaInfoMEx(xmlData *XmlData) *BalanceArenaInfoMEx {
	m := &BalanceArenaInfoMEx{
		BalanceArenaInfoM: &BalanceArenaInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *BalanceArenaInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *BalanceArenaInfoMEx) load(dir string, isShow bool) error {
	err := m.BalanceArenaInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	return nil
}

func (m *BalanceArenaInfoMEx) checkData() error {
	err := m.BalanceArenaInfoM.checkData()
	if err != nil {
		return err
	}
	for _, v := range m.idRecordMap {
		v.OpenTime, v.CloseTime = m.xmlData.BalanceArenaPhaseInfoM.GetRoundTimeConfigByDate(v.OpenDate1)
	}
	return nil
}

// 获取当前赛期
func (m *BalanceArenaInfoMEx) GetCurrentRound(now int64) *BalanceArenaInfo {
	for _, v := range m.idRecordMap {
		if now >= v.OpenTime && now < v.CloseTime {
			return v
		}
	}
	return nil
}
