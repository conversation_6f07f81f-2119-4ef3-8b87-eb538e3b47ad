package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"
	"strconv"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ActivityMirageRankInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ActivityMirageRankInfo
	sort    []*ActivityMirageRankInfo
}

func newActivityMirageRankInfoManager(xmlData *XmlData) *ActivityMirageRankInfoManager {
	m := &ActivityMirageRankInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ActivityMirageRankInfoManager) name() string {
	return "activity_mirage_rank_info.xml"
}

func (m *ActivityMirageRankInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ActivityMirageRankInfoManager) checkData() error {
	return nil
}

func (m *ActivityMirageRankInfoManager) load(dir string, show bool) error {
	tmp := &ActivityMirageRankInfos{}
	fileName := filepath.Join(dir, "activity_mirage_rank_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ActivityMirageRankInfo, len(tmp.Datas))
	}
	m.sort = make([]*ActivityMirageRankInfo, 0, len(tmp.Datas))
	var rankCheck uint32
	for _, data := range tmp.Datas {
		data.prepare()
		if data.RankMax <= rankCheck {
			panic(fmt.Sprintf("config:%s data:%d invalid rank check: %d", fileName, data.Id, data.RankMax))
		}
		rankCheck = data.RankMax
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
		m.sort = append(m.sort, data)
	}
	return nil
}

func (m *ActivityMirageRankInfoManager) Index(key uint32) *ActivityMirageRankInfo {
	return m.Datas[key]
}

func (m *ActivityMirageRankInfoManager) GetRankAward(rank uint32) *ActivityMirageRankInfo {
	return GetDataLessAndClosedByRank(m.sort, rank)
}

func (m *ActivityMirageRankInfo) GetRank() uint32 {
	return m.RankMax
}

type ActivityMirageAwardInfo struct {
	Info       *ActivityMirageRankInfo
	AwardUsers []uint64
}

func (a *ActivityMirageAwardInfo) GetMailParams() []string {
	if a.Info == nil {
		return []string{}
	}
	return []string{strconv.Itoa(int(a.Info.RankMax)), strconv.Itoa(int(a.Info.RankMin))}
}

func (a *ActivityMirageAwardInfo) GetAwards() []*cl.Resource {
	if a.Info == nil {
		return []*cl.Resource{}
	}

	awards := make([]*cl.Resource, 0, len(a.Info.ClRes))
	for _, v := range a.Info.ClRes {
		awards = append(awards, v.Clone())
	}
	return awards
}

func (a *ActivityMirageAwardInfo) GetAwardUsers() []uint64 {
	return a.AwardUsers
}
