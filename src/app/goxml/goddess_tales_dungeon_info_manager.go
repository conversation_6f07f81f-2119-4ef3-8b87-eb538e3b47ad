package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GoddessTalesDungeonInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GoddessTalesDungeonInfoExt
}

type GoddessTalesDungeonInfoExt struct {
	Id              uint32
	TalesId         uint32
	AssistHeros     []uint32
	MustAssistHeros []uint32
	MonsterGroup    uint32
}

func (e *GoddessTalesDungeonInfoExt) loadAssistHeros(xmlData *XmlData, data *GoddessTalesDungeonInfo) {
	e.addAssistHero(xmlData, data.Hero1, data.Type1)
	e.addAssistHero(xmlData, data.Hero2, data.Type2)
	e.addAssistHero(xmlData, data.Hero3, data.Type3)
	e.addAssistHero(xmlData, data.Hero4, data.Type4)
	e.addAssistHero(xmlData, data.Hero5, data.Type5)
	e.addAssistHero(xmlData, data.Hero6, data.Type6)
	e.addAssistHero(xmlData, data.Hero7, data.Type7)
	e.addAssistHero(xmlData, data.Hero8, data.Type8)
	e.addAssistHero(xmlData, data.Hero9, data.Type9)
	e.addAssistHero(xmlData, data.Hero10, data.Type10)
}

func (e *GoddessTalesDungeonInfoExt) IsInAssistHeros(heroID uint32) bool {
	for _, assistHero := range e.AssistHeros {
		if assistHero == heroID {
			return true
		}
	}
	return false
}

func (e *GoddessTalesDungeonInfoExt) addAssistHero(xmlData *XmlData, assistID uint32, assistType uint32) {
	if assistID == 0 {
		return
	}
	if xmlData.MonsterInfoM.Index(assistID) == nil {
		panic(fmt.Sprintf("tales_dungeon_info.xml assistHero not found: assistHero:%d", assistID))
	}
	e.AssistHeros = append(e.AssistHeros, assistID)
	if assistType == 1 {
		e.MustAssistHeros = append(e.MustAssistHeros, assistID)
	}
}

func newGoddessTalesDungeonInfoManager(xmlData *XmlData) *GoddessTalesDungeonInfoManager {
	m := &GoddessTalesDungeonInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GoddessTalesDungeonInfoManager) name() string {
	return "GoddessTalesDungeonInfo"
}

func (m *GoddessTalesDungeonInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GoddessTalesDungeonInfoManager) checkData() error {
	return nil
}

func (m *GoddessTalesDungeonInfoManager) load(dir string, isShow bool) error {
	tmp := &GoddessTalesDungeonInfos{}
	fileName := filepath.Join(dir, "goddess_tales_dungeon_info.xml")

	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GoddessTalesDungeonInfoExt, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()
		info := &GoddessTalesDungeonInfoExt{}
		info.loadAssistHeros(m.xmlData, data)
		info.Id = data.DungeonId
		info.TalesId = data.Tales
		info.MonsterGroup = data.MonsterGroup

		if m.xmlData.TalesInfoM.Index(info.TalesId) == nil {
			panic(fmt.Sprintf("goddess_tales_dungeon_info.xml talesId not found: id:%d, talesId:%d", info.Id, info.TalesId))
		}
		if info.MonsterGroup != 0 && m.xmlData.MonsterGroupInfoM.Index(info.MonsterGroup) == nil {
			panic(fmt.Sprintf("goddess_tales_dungeon_info.xml monsterGroup not found: id:%d, monsterGroup:%d", info.Id, info.MonsterGroup))
		}

		if ptr, exist := m.Datas[data.DungeonId]; exist {
			*ptr = *info
			info = ptr
		} else {
			m.Datas[data.DungeonId] = info
		}
	}
	return nil
}

func (m *GoddessTalesDungeonInfoManager) GetRecordByDungeonId(dungeonId uint32) *GoddessTalesDungeonInfoExt {
	return m.Datas[dungeonId]
}

func (m *GoddessTalesDungeonInfoManager) Index(key uint32) *GoddessTalesDungeonInfoExt {
	return m.Datas[key]
}
