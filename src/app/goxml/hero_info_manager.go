package goxml

import (
	"app/protos/out/common"
	"fmt"
	"gitlab.qdream.com/kit/sea/math/rand"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type HeroInfoManager struct {
	xmlData         *XmlData
	Datas           map[uint32]*HeroInfoExt
	SpecialDatas    map[uint32]map[uint32]*HeroInfoExt
	LinkHeros       map[uint32]map[uint32]util.None //linkID => heroID=>
	showManual      map[uint32]*HeroInfoExt
	AwakenHeroInfos []*HeroAwakenInfo // 英雄觉醒信息
	EmblemSkillHero map[uint32]uint32 // 符文技能对应的英雄
	DataSlice       []*HeroInfoExt
}

type HeroInfoExt struct {
	Id                         uint32 //系统id
	Rare                       uint32 //品质
	Job                        uint32 //职业
	Race                       uint32 //种族
	Sex                        uint32 //性别 1-男 2-女 0-怪物无性别
	Star                       uint32 //初始星级
	LimitStar                  uint32 //星级上限
	LimitStage                 uint32 //突破上限
	SkillLevelID               uint32 //技能等级组id - skill_level_info的id
	AttackType                 uint32 //伤害类型
	Hp                         uint32 //生命成长
	Attack                     uint32 //攻击成长
	PhyDef                     uint32 //物防成长
	MagDef                     uint32 //魔防成长
	FixedDam                   uint32 //固定伤害成长
	FixedDamReduce             uint32 //固定免伤成长
	Speed                      uint32 //速度成长
	Skills                     [SkillPosMax]uint32
	FragmentId                 uint32 //碎片id
	Special                    uint32 //是否是特殊英雄（只能被消耗） 1-是 0-否
	Back                       uint32 //是否可以进行回退操作 1-是 0-否
	Send                       uint32 //是否可以通过gm发放
	ShowManual                 bool   //是否在图鉴中显示
	GemPassiveSkill1RaisePS    uint64
	GemPassiveSkill2RaisePS    uint64
	EmblemSkill1               uint32   // 符文技能1
	Link1ID                    uint32   // 流派羁绊ID
	Link2ID                    uint32   // 神魔羁绊ID
	Link3ID                    uint32   // 种族羁绊ID
	Link4ID                    uint32   // 女神羁绊ID
	Link5ID                    uint32   //觉醒羁绊
	AwakenValue                uint32   // 专属觉醒材料ID
	AwakenPassiveSkillsRaisePS []uint64 //觉醒技能
	SeasonIdForSkill           uint32   // 赛季技能对应的赛季ID
	SeasonSkill1               uint32   // 赛季技能1
	EmblemExclusive1           uint32   //符文专属技能1
	EmblemExclusive2           uint32   //符文专属技能2
	EmblemExclusive3           uint32   //符文专属技能3
	EmblemRecommend            string   // 推荐符文
	Omnihero                   uint32   // 是否是全能英雄
	IsRareUp                   uint32
}

func newHeroInfoManager(xmlData *XmlData) *HeroInfoManager {
	m := &HeroInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *HeroInfoManager) name() string {
	return "HeroInfo"
}

func (m *HeroInfoManager) ignoreForCheck() bool {
	return false
}

func (m *HeroInfoManager) checkData() error {
	for _, data := range m.Datas {
		if data.AwakenValue == 0 { // 英雄不能觉醒
			continue
		}
		if !data.ShowManual { // 英雄未投放
			continue
		}
		// 觉醒英雄池
		heroInfo := &HeroAwakenInfo{
			HeroSysId:   data.Id,
			AwakenValue: data.AwakenValue,
		}
		shieldInfo := m.xmlData.ShieldInfoM.GetRecordByAwardTypeAndShiedId(uint32(common.RESOURCE_HERO), data.Id)
		if shieldInfo != nil {
			heroInfo.ShieldEndTime = shieldInfo.EndDay.Unix()
		}
		m.AwakenHeroInfos = append(m.AwakenHeroInfos, heroInfo)
	}
	return nil
}

func (m *HeroInfoManager) Load(dir string, isShow bool) error {
	return m.load(dir, isShow)
}

func (m *HeroInfoManager) load(dir string, isShow bool) error {
	tmp := &HeroInfos{}
	fileName := filepath.Join(dir, "hero_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*HeroInfoExt, len(tmp.Datas))
	}
	m.SpecialDatas = make(map[uint32]map[uint32]*HeroInfoExt)
	m.LinkHeros = make(map[uint32]map[uint32]util.None)
	emblemSkillCheck := make(map[uint32]uint32)
	m.showManual = make(map[uint32]*HeroInfoExt)
	m.EmblemSkillHero = make(map[uint32]uint32)

	for _, data := range tmp.Datas {
		if !HeroLegalRace[data.Race] {
			panic(fmt.Sprintf("load config %s fail: race err. id:%d", fileName, data.Id))
		}

		if data.Special != HeroCommon && data.Special != HeroSpecial {
			panic(fmt.Sprintf("load config %s fail: special field err. id:%d", fileName, data.Id))
		}

		if data.Special == HeroSpecial {
			if data.Race == RaceNone && !HeroSpecialNoRaceLegalStar[data.Star] {
				panic(fmt.Sprintf("load config %s fail: special no race star err. id:%d", fileName, data.Id))
			}
			if data.Race != RaceNone && !HeroSpecialNormalRaceLegalStar[data.Star] {
				panic(fmt.Sprintf("load config %s fail: special normal race star err. id:%d", fileName, data.Id))
			}
		}

		if data.Job != HeroJobTank && data.Job != HeroJobMaster &&
			data.Job != HeroJobSoldier && data.Job != HeroJobAuxiliary {
			panic(fmt.Sprintf("load config %s fail: job err. id:%d", fileName, data.Id))
		}

		//0.8.8 - 对原back字段的兼容处理，初始星级在5星及以上的英雄才可以回退
		if data.Back == HeroCanBack && data.Star < HeroCanBackMinInitStar {
			panic(fmt.Sprintf("load config %s fail: HeroCanBack initStar err. id:%d", fileName, data.Id))
		}

		//常规英雄，初始星级不能超过5 --- 超过后会影响升星回退逻辑
		if data.Special == HeroCommon && data.Star > HeroCanBackMinInitStar {
			panic(fmt.Sprintf("load config %s fail: HeroCommon initStar err. id:%d", fileName, data.Id))
		}

		dataExt := &HeroInfoExt{
			Id:                         data.Id,
			Rare:                       data.Rare,
			Job:                        data.Job,
			Race:                       data.Race,
			Sex:                        data.Sex,
			Star:                       data.Star,
			LimitStar:                  data.LimitStar,
			LimitStage:                 data.LimitStage,
			SkillLevelID:               data.SkillLevelId,
			AttackType:                 data.AttackType,
			Hp:                         data.Hp,
			Attack:                     data.Attack,
			PhyDef:                     data.PhyDef,
			MagDef:                     data.MagDef,
			FixedDam:                   data.FixedDam,
			FixedDamReduce:             data.FixedDamReduce,
			Speed:                      data.Speed,
			FragmentId:                 data.FragmentId,
			Special:                    data.Special,
			Back:                       data.Back,
			Send:                       data.Send,
			AwakenValue:                data.AwakenValue,
			AwakenPassiveSkillsRaisePS: make([]uint64, 0, HeroAwakenSkillMaxLevel),
			Omnihero:                   data.Omnihero,
			IsRareUp:                   data.IsRareUp,
			EmblemRecommend:            data.EmblemReco,
		}
		dataExt.ShowManual = data.ShowManual == 1
		dataExt.Skills[SkillPosNormal] = data.NormalSkill
		dataExt.Skills[SkillPosActive1] = data.Skill1
		dataExt.Skills[SkillPosActive2] = data.Skill2
		dataExt.Skills[SkillPosPassive1] = data.Skill3
		dataExt.Skills[SkillPosPassive2] = data.Skill4
		dataExt.Skills[SkillPosPassive3] = data.Skill5
		dataExt.Skills[SkillPosSeasonAddHero] = data.SeasonSkill1
		dataExt.Skills[SkillPosAwake] = data.AwakeSkill
		dataExt.SeasonIdForSkill = data.SeasonId
		dataExt.SeasonSkill1 = data.SeasonSkill1

		if data.IsRareUp == 1 {
			if data.Race != RaceProtoss && data.Race != RaceDemon {
				panic(fmt.Sprintf("load config %s fail. race %d cant rare up", fileName, data.Race))
			}
		}

		if data.Link1 > 0 {
			if heroMap, exist := m.LinkHeros[data.Link1]; exist {
				heroMap[dataExt.Id] = util.None{}
			} else {
				heroMap = make(map[uint32]util.None)
				heroMap[dataExt.Id] = util.None{}
				m.LinkHeros[data.Link1] = heroMap
			}
			dataExt.Link1ID = data.Link1
		}
		if data.Link2 > 0 {
			if heroMap, exist := m.LinkHeros[data.Link2]; exist {
				heroMap[dataExt.Id] = util.None{}
			} else {
				heroMap = make(map[uint32]util.None)
				heroMap[dataExt.Id] = util.None{}
				m.LinkHeros[data.Link2] = heroMap
			}
			dataExt.Link2ID = data.Link2
		}
		if data.Link3 > 0 {
			if heroMap, exist := m.LinkHeros[data.Link3]; exist {
				heroMap[dataExt.Id] = util.None{}
			} else {
				heroMap = make(map[uint32]util.None)
				heroMap[dataExt.Id] = util.None{}
				m.LinkHeros[data.Link3] = heroMap
			}
			dataExt.Link3ID = data.Link3
		}
		if data.Link4 > 0 {
			if heroMap, exist := m.LinkHeros[data.Link4]; exist {
				heroMap[dataExt.Id] = util.None{}
			} else {
				heroMap = make(map[uint32]util.None)
				heroMap[dataExt.Id] = util.None{}
				m.LinkHeros[data.Link4] = heroMap
			}
			dataExt.Link4ID = data.Link4
		}

		if data.GemPassiveSkill1 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.GemPassiveSkill1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. GemPassiveSkill1 %d not in RaisePassiveSkillInfoM", fileName, data.GemPassiveSkill1))
			}
			dataExt.GemPassiveSkill1RaisePS = raisePSInfo.ID
		}

		if data.GemPassiveSkill2 > 0 {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.GemPassiveSkill2, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. GemPassiveSkill2 %d not in RaisePassiveSkillInfoM", fileName, data.GemPassiveSkill2))
			}
			dataExt.GemPassiveSkill2RaisePS = raisePSInfo.ID
		}

		if data.EmblemSkill1 > 0 {
			emblemSkillCheck[data.EmblemSkill1]++
		}

		if data.EmblemExclusive1 > 0 {
			emblemSkillCheck[data.EmblemExclusive1]++
		}
		if data.EmblemExclusive2 > 0 {
			emblemSkillCheck[data.EmblemExclusive2]++
		}
		if data.EmblemExclusive3 > 0 {
			emblemSkillCheck[data.EmblemExclusive3]++
		}
		for v, c := range emblemSkillCheck {
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(v, c)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. emblemSkillCheck skill:%d count:%d not in RaisePassiveSkillInfoM", fileName, v, c))
			}
		}

		dataExt.EmblemSkill1 = data.EmblemSkill1
		dataExt.EmblemExclusive1 = data.EmblemExclusive1
		dataExt.EmblemExclusive2 = data.EmblemExclusive2
		dataExt.EmblemExclusive3 = data.EmblemExclusive3
		if data.EmblemExclusive1 != 0 {
			_, exist := m.EmblemSkillHero[data.EmblemExclusive1]
			if exist {
				panic(fmt.Sprintf("load config: %s fail,hero  data.EmblemExclusive1:%d skill is repeated hero:%d", fileName, data.EmblemExclusive1, m.EmblemSkillHero[data.EmblemExclusive1]))
			}
			m.EmblemSkillHero[data.EmblemExclusive1] = data.Id
		}

		if data.EmblemExclusive2 != 0 {
			_, exist := m.EmblemSkillHero[data.EmblemExclusive2]
			if exist {
				panic(fmt.Sprintf("load config: %s fail,hero  data.EmblemExclusive2:%d skill is repeated hero:%d", fileName, data.EmblemExclusive2, m.EmblemSkillHero[data.EmblemExclusive2]))
			}
			m.EmblemSkillHero[data.EmblemExclusive2] = data.Id
		}

		if data.EmblemExclusive3 != 0 {
			_, exist := m.EmblemSkillHero[data.EmblemExclusive3]
			if exist {
				panic(fmt.Sprintf("load config: %s fail,hero  data.EmblemExclusive3:%d skill is repeated hero:%d", fileName, data.EmblemExclusive3, m.EmblemSkillHero[data.EmblemExclusive3]))
			}
			m.EmblemSkillHero[data.EmblemExclusive3] = data.Id
		}

		//配置有觉醒材料就认为是可觉醒的
		if dataExt.IsAwakenable() {
			if m.xmlData.ItemInfoM.Index(data.AwakenValue) == nil {
				panic(fmt.Sprintf("load config %s fail. Id:%d AwakenValue:%d not in ItemInfoM", fileName, data.Id, data.AwakenValue))
			}
			raisePSInfo := m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.AwakenPassiveSkill1, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. Id:%d AwakenPassiveSkill1 %d not in RaisePassiveSkillInfoM", fileName, data.Id, data.AwakenPassiveSkill1))
			}
			dataExt.AwakenPassiveSkillsRaisePS = append(dataExt.AwakenPassiveSkillsRaisePS, raisePSInfo.ID)

			raisePSInfo = m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.AwakenPassiveSkill2, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. Id:%d AwakenPassiveSkill2 %d not in RaisePassiveSkillInfoM", fileName, data.Id, data.AwakenPassiveSkill2))
			}
			dataExt.AwakenPassiveSkillsRaisePS = append(dataExt.AwakenPassiveSkillsRaisePS, raisePSInfo.ID)

			raisePSInfo = m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.AwakenPassiveSkill3, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. Id:%d AwakenPassiveSkill3 %d not in RaisePassiveSkillInfoM", fileName, data.Id, data.AwakenPassiveSkill3))
			}
			dataExt.AwakenPassiveSkillsRaisePS = append(dataExt.AwakenPassiveSkillsRaisePS, raisePSInfo.ID)

			raisePSInfo = m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.AwakenPassiveSkill4, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. Id:%d AwakenPassiveSkill4 %d not in RaisePassiveSkillInfoM", fileName, data.Id, data.AwakenPassiveSkill4))
			}
			dataExt.AwakenPassiveSkillsRaisePS = append(dataExt.AwakenPassiveSkillsRaisePS, raisePSInfo.ID)

			raisePSInfo = m.xmlData.RaisePassiveSkillInfoM.GetRaisePassiveSkill(data.AwakenPassiveSkill5, 1)
			if raisePSInfo == nil {
				panic(fmt.Sprintf("load config %s fail. Id:%d AwakenPassiveSkill5 %d not in RaisePassiveSkillInfoM", fileName, data.Id, data.AwakenPassiveSkill5))
			}
			dataExt.AwakenPassiveSkillsRaisePS = append(dataExt.AwakenPassiveSkillsRaisePS, raisePSInfo.ID)
		} else {
			if data.AwakenPassiveSkill1 > 0 ||
				data.AwakenPassiveSkill2 > 0 ||
				data.AwakenPassiveSkill3 > 0 ||
				data.AwakenPassiveSkill4 > 0 ||
				data.AwakenPassiveSkill5 > 0 {
				panic(fmt.Sprintf("load config %s fail. awaken not unlocked but AwakenPassiveSkill not zero. Id:%d", fileName, data.Id))
			}
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *dataExt
			dataExt = ptr
		} else {
			m.Datas[data.Id] = dataExt
		}

		if data.Special == HeroSpecial {
			if _, ok := m.SpecialDatas[data.Race]; !ok {
				m.SpecialDatas[data.Race] = make(map[uint32]*HeroInfoExt)
			}
			m.SpecialDatas[data.Race][data.Star] = dataExt
		}

		if data.ShowManual == HeroShowManual {
			m.showManual[data.Id] = dataExt
		}

		m.DataSlice = append(m.DataSlice, dataExt)
	}

	if len(m.SpecialDatas) != len(HeroLegalRace) {
		panic(fmt.Sprintf("load config %s fail: specialDatas not enough. count:%d", fileName, len(HeroLegalRace)))
	}
	for race, info := range m.SpecialDatas {
		if race == RaceNone {
			if len(info) != len(HeroSpecialNoRaceLegalStar) {
				panic(fmt.Sprintf("load config %s fail: specialDatas no race data count err. count:%d",
					fileName, len(info)))
			}
		} else {
			if len(info) != len(HeroSpecialNormalRaceLegalStar) {
				panic(fmt.Sprintf("load config %s fail: specialDatas normal race data count err. count:%d",
					fileName, len(info)))
			}
		}
	}
	return nil
}

func (m *HeroInfoManager) GetRecordById(id uint32) *HeroInfoExt {
	return m.Datas[id]
}

func (m *HeroInfoManager) Index(id uint32) *HeroInfoExt {
	return m.Datas[id]
}

func (m *HeroInfoManager) GetSpecialHeroByRaceAndStar(race, star uint32) *HeroInfoExt {
	return m.SpecialDatas[race][star]
}

func (m *HeroInfoManager) IsCommonHero(id uint32) error {
	info := m.Index(id)
	if info == nil {
		return fmt.Errorf("hero config not exist. id:%d", id)
	}
	if info.Special != HeroCommon {
		return fmt.Errorf("not common hero. id:%d", id)
	}
	return nil
}

func (m *HeroInfoManager) IsCanBack(value uint32) bool {
	return value == HeroCanBack
}

func (m *HeroInfoManager) GetRare(id uint32) uint32 {
	info := m.Index(id)
	if info == nil {
		return 0
	}
	return info.Rare
}

func (m *HeroInfoManager) GetJob(id uint32) uint32 {
	if heroInfo, exist := m.Datas[id]; exist {
		return heroInfo.Job
	}

	return 0
}

func (m *HeroInfoManager) GetLinkHeros(linkID uint32) map[uint32]util.None {
	return m.LinkHeros[linkID]
}

func (m *HeroInfoManager) GetShowManualHeroes() map[uint32]*HeroInfoExt {
	return m.showManual
}

func (e *HeroInfoExt) IsAwakenable() bool {
	return e.AwakenValue > 0
}

func (e *HeroInfoExt) GetAwakenPassiveSkillRaisePSs(awakenLevel uint32) []uint64 {
	raisePSs := make([]uint64, 0, len(e.AwakenPassiveSkillsRaisePS))
	for i, raisePS := range e.AwakenPassiveSkillsRaisePS {
		if i >= int(awakenLevel) {
			break
		}
		raisePSs = append(raisePSs, raisePS)
	}
	return raisePSs
}

type HeroAwakenInfo struct {
	HeroSysId     uint32 // 系统id
	AwakenValue   uint32 // 专属觉醒材料ID
	ShieldEndTime int64  // 屏蔽结束时间，为0则不屏蔽
}

func (m *HeroInfoManager) GetAwakenHeroInfos() []*HeroAwakenInfo {
	return m.AwakenHeroInfos
}

func (m *HeroInfoManager) GetEmblemSkillHero(skillID uint32) uint32 {
	return m.EmblemSkillHero[skillID]
}

// 随机一定数量的英雄
func (m *HeroInfoManager) RandomHeroesByNum(rd *rand.Rand, num uint32, exclude map[uint32]struct{}) []uint32 {
	// 每次操作前重新随机一次英雄数组
	rd.Shuffle(len(m.DataSlice), func(i, j int) {
		m.DataSlice[i], m.DataSlice[j] = m.DataSlice[j], m.DataSlice[i]
	})
	heroes := make([]uint32, 0, num)
	var i uint32
	for _, v := range m.DataSlice {
		if i >= num {
			break
		}

		if !v.ShowManual {
			continue
		}

		if v.Rare != uint32(common.QUALITY_RED) && v.Rare != uint32(common.QUALITY_EX) {
			continue
		}
		_, exist := exclude[v.Id] // 排除
		if exist {
			continue
		}
		heroes = append(heroes, v.Id)
		i++
	}
	return heroes
}
