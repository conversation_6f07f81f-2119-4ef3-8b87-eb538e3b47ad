package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

const (
	EmblemLevelupResCostCount uint32 = 2
)

type EmblemLevelInfoManager struct {
	xmlData  *XmlData
	Datas    map[uint32]map[uint32]*EmblemLevelInfoExt
	MaxLevel map[uint32]uint32
}

type EmblemLevelInfoExt struct {
	Id         uint32
	Level      uint32
	LimitLevel uint32
	Attr       map[uint32]int64
	Costs      []*cl.Resource
}

func newEmblemLevelInfoManager(xmlData *XmlData) *EmblemLevelInfoManager {
	m := &EmblemLevelInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *EmblemLevelInfoManager) name() string {
	return "EmblemLevelInfo"
}

func (m *EmblemLevelInfoManager) ignoreForCheck() bool {
	return false
}

func (m *EmblemLevelInfoManager) checkData() error {
	return nil
}

func (m *EmblemLevelInfoManager) load(dir string, isShow bool) error {
	tmp := &EmblemLevelInfos{}
	fileName := filepath.Join(dir, "emblem_level_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]map[uint32]*EmblemLevelInfoExt, len(tmp.Datas))
	}
	m.MaxLevel = make(map[uint32]uint32)
	var firstID uint32
	for k, data := range tmp.Datas {
		//等级连续性验证
		if data.Level != m.MaxLevel[data.Id]+1 {
			panic(fmt.Sprintf("load config %s fail: level is discontinuity. id:%d", fileName, data.Id))
		}
		m.MaxLevel[data.Id] = data.Level

		if k == 0 {
			firstID = data.Id
		}

		levelInfo := &EmblemLevelInfoExt{}
		levelInfo.Id = data.Id
		levelInfo.Level = data.Level
		levelInfo.LimitLevel = data.LimitLevel
		levelInfo.Attr = make(map[uint32]int64)
		if data.AttrType1 > 0 && data.AttrType1 < uint32(AttrMaxNum) && data.AttrValue1 > 0 {
			levelInfo.Attr[data.AttrType1] = int64(data.AttrValue1)
		}
		if data.AttrType2 > 0 && data.AttrType2 < uint32(AttrMaxNum) && data.AttrValue2 > 0 {
			levelInfo.Attr[data.AttrType2] = int64(data.AttrValue2)
		}
		if data.AttrType3 > 0 && data.AttrType3 < uint32(AttrMaxNum) && data.AttrValue3 > 0 {
			levelInfo.Attr[data.AttrType3] = int64(data.AttrValue3)
		}
		if data.ResType1 > 0 && data.ResCount1 > 0 {
			levelInfo.Costs = append(levelInfo.Costs, GenSimpleResource(data.ResType1, data.ResValue1, data.ResCount1))
		}
		if data.ResType2 > 0 && data.ResCount2 > 0 {
			levelInfo.Costs = append(levelInfo.Costs, GenSimpleResource(data.ResType2, data.ResValue2, data.ResCount2))
		}
		if v, exist := m.Datas[data.Id]; exist {
			if ptr, exist := v[data.Level]; exist {
				*ptr = *levelInfo
				levelInfo = ptr
			} else {
				v[data.Level] = levelInfo
			}
		} else {
			m.Datas[data.Id] = make(map[uint32]*EmblemLevelInfoExt)
			m.Datas[data.Id][data.Level] = levelInfo
		}
	}

	//验证最大等级是否相同
	if firstID == 0 {
		panic(fmt.Sprintf("load config %s fail: firstID is 0", fileName))
	}
	maxLevel := m.MaxLevel[firstID]
	for id, level := range m.MaxLevel {
		if level != maxLevel {
			panic(fmt.Sprintf("load config %s fail: maxLevel is different, id:%d", fileName, id))
		}
	}
	return nil
}

func (m *EmblemLevelInfoManager) Index(id, level uint32) *EmblemLevelInfoExt {
	if emblemLevelInfo, exist := m.Datas[id]; exist {
		if levelInfo, exist := emblemLevelInfo[level]; exist {
			return levelInfo
		}
	}
	return nil
}

func (m *EmblemLevelInfoManager) GetAttr(id, level uint32) map[uint32]int64 {
	if emblemLevelInfo, exist := m.Datas[id]; exist {
		if levelInfo, exist := emblemLevelInfo[level]; exist {
			return levelInfo.Attr
		}
	}
	return nil
}

func (m *EmblemLevelInfoManager) GetMaxLevel(id uint32) uint32 {
	return m.MaxLevel[id]
}
