package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"math"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/math/rand"
	"gitlab.qdream.com/kit/sea/util"
)

type FlowerOccupyBaseInfoManager struct {
	xmlData           *XmlData
	Datas             map[uint32]*FlowerOccupyBaseInfo
	RareDatas         map[uint32]map[uint32]uint32 //timber => rare => id
	maxMonsterGroupID uint64
}

func newFlowerOccupyBaseInfoManager(xmlData *XmlData) *FlowerOccupyBaseInfoManager {
	m := &FlowerOccupyBaseInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *FlowerOccupyBaseInfoManager) name() string {
	return "FlowerOccupyBaseInfo"
}

func (m *FlowerOccupyBaseInfoManager) ignoreForCheck() bool {
	return false
}

func (m *FlowerOccupyBaseInfoManager) checkData() error {
	return nil
}

func (m *FlowerOccupyBaseInfoManager) load(dir string, isShow bool) error {
	tmp := &FlowerOccupyBaseInfos{}
	fileName := filepath.Join(dir, "flower_occupy_base_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}

	if m.Datas == nil {
		m.Datas = make(map[uint32]*FlowerOccupyBaseInfo, len(tmp.Datas))
	}
	m.maxMonsterGroupID = 0
	m.RareDatas = make(map[uint32]map[uint32]uint32)

	var lastID, lastExp uint32
	flowerbedRare := make(map[uint32]uint32)
	for _, data := range tmp.Datas {
		data.prepare()
		//验证花坛id的递增性
		if data.Id < lastID {
			panic(fmt.Sprintf("load config %s fail: id less than last. id:%d", fileName, data.Id))
		}
		lastID = data.Id

		//验证花坛品质的递增性
		timber := calcTimberByFlowerbedID(data.Id)
		if m.xmlData.FlowerOccupyTimberInfoM.Index(timber) == nil {
			//花坛id，未按预期规律配置
			panic(fmt.Sprintf("load config %s fail: timber not exist. id:%d", fileName, data.Id))
		}
		if data.Rare <= flowerbedRare[timber] {
			panic(fmt.Sprintf("load config %s fail: rare less than last. id:%d", fileName, data.Id))
		}
		flowerbedRare[timber] = data.Rare

		//验证花坛采集经验的递增性
		if data.Exp < lastExp {
			panic(fmt.Sprintf("load config %s fail: exp less than last. id:%d", fileName, data.Id))
		}
		lastExp = data.Exp

		//验证花坛默认占领者是否配置
		if data.MonsterGroup == 0 {
			panic(fmt.Sprintf("load config %s fail: monster group is 0. id:%d", fileName, data.Id))
		}

		//验证采集奖励是否配置
		if len(data.RewardClRes) == 0 {
			panic(fmt.Sprintf("load config %s fail: no reward. id:%d", fileName, data.Id))
		}

		//掉落组是否存在
		if len(m.xmlData.DropGroupInfoM.Group(data.DropGroup)) == 0 {
			panic(fmt.Sprintf("load config %s fail: DropGroup:%d not exist. id:%d", fileName, data.DropGroup, data.Id))
		}

		if uint64(data.MonsterGroup) > m.maxMonsterGroupID {
			m.maxMonsterGroupID = uint64(data.MonsterGroup)
		}

		//验证跑马灯id是否正确
		if data.ChatAdmin > 0 {
			if data.ChatAdmin != ChatTypeFlowerOccupyBestFlowerbed {
				panic(fmt.Sprintf("load config %s fail: Marquee id wrong. id:%d", fileName, data.Id))
			}
		}

		//构建品质数据
		if _, exist := m.RareDatas[timber]; !exist {
			m.RareDatas[timber] = make(map[uint32]uint32)
		}
		m.RareDatas[timber][data.Rare] = data.Id

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *FlowerOccupyBaseInfoManager) GetRecordById(id uint32) *FlowerOccupyBaseInfo {
	return m.Datas[id]
}

func (m *FlowerOccupyBaseInfoManager) Index(key uint32) *FlowerOccupyBaseInfo {
	return m.Datas[key]
}

func (m *FlowerOccupyBaseInfoManager) MaxCount() int {
	return len(m.Datas)
}

func calcTimberByFlowerbedID(id uint32) uint32 {
	//10是根据配置逻辑，得出的常数
	return id / 10
}

// 根据结算次数，计算基础掉落数据
// @param *rand.Rand rd
// @param uint32 id 花坛id
// @param uint32 times 结算次数
// @return []*cl.Resource 固定掉落的资源
// @return uint32 随机掉落组id
// @return uint32 随机掉落组的掉落次数
// @return uint32 获得经验值
func (m *FlowerOccupyBaseInfoManager) GetBaseAward(rd *rand.Rand, id, times uint32) ([]*cl.Resource, uint32, uint32, uint32) {
	data := m.Index(id)
	if data == nil {
		l4g.Errorf("FlowerOccupyBaseInfoManager.GetBaseAward: data not exist. id:%d, times:%d",
			id, times)
		return nil, 0, 0, 0
	}

	fixedDrop := make([]*cl.Resource, 0, len(data.RewardClRes))
	for _, v := range data.RewardClRes {
		fixedDrop = append(fixedDrop, &cl.Resource{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count * times,
		})
	}

	var randomDropTimes uint32
	if data.DropProb > 0 {
		for i := 0; i < int(times); i++ {
			if uint32(rd.RandBetween(1, BaseInt)) <= data.DropProb {
				randomDropTimes++
			}
		}
	}
	l4g.Debugf("FlowerOccupyBaseInfoManager.GetBaseAward: id:%d, times:%d, randomDropTimes:%d",
		id, times, randomDropTimes)
	return fixedDrop, data.DropGroup, randomDropTimes, data.Exp * times
}

// 计算返回公会加成奖励数据
// @param uint32 id 花坛id
// @param []*cl.Resource dropRes 随机掉落的资源（必须是合并后的）
// @param map[uint32]uint32 buff 公会加成 加成等级=>次数
// @return []*cl.Resource 公会加成掉落的资源
func (m *FlowerOccupyBaseInfoManager) GetGuildBuffAward(flowerbedID uint32, dropRes []*cl.Resource,
	buff map[uint32]uint32) []*cl.Resource {
	data := m.xmlData.FlowerOccupyBaseInfoM.Index(flowerbedID)
	if data == nil {
		l4g.Errorf("FlowerOccupyBaseInfoManager.GetGuildBuffAward: config not exist. id:%d", flowerbedID)
		return nil
	}

	buffAward := make([]*cl.Resource, 0, len(data.RewardClRes))
	for level, times := range buff {
		param := m.xmlData.FlowerConfigInfoM.GetGuildBuff(level)
		//基础奖励
		for _, v := range data.RewardClRes {
			count := uint32(math.Ceil(float64(v.Count*times) * (float64(param) / BaseFloat)))
			if count == 0 {
				l4g.Errorf("FlowerOccupyBaseInfoManager.GetGuildBuffAward: no guild buff base award. id:%d, level:%d",
					flowerbedID, level)
				continue
			}

			l4g.Debugf("FlowerOccupyBaseInfoManager.GetGuildBuffAward base: type:%d, value:%d, count:%d",
				v.Type, v.Value, count)

			buffAward = append(buffAward, &cl.Resource{
				Type:  v.Type,
				Value: v.Value,
				Count: count,
			})
		}
		//随机奖励
		for _, v := range dropRes {
			count := uint32(math.Ceil(float64(v.Count) * (float64(param) / BaseFloat)))
			if count == 0 {
				l4g.Errorf("FlowerOccupyBaseInfoManager.GetGuildBuffAward: no guild buff random award. id:%d, level:%d",
					flowerbedID, level)
				continue
			}

			l4g.Debugf("FlowerOccupyBaseInfoManager.GetGuildBuffAward random: type:%d, value:%d, count:%d",
				v.Type, v.Value, count)

			buffAward = append(buffAward, &cl.Resource{
				Type:  v.Type,
				Value: v.Value,
				Count: count,
			})
		}
	}
	return buffAward
}

func (m *FlowerOccupyBaseInfoManager) GetMaxMonsterGroup() uint64 {
	return m.maxMonsterGroupID
}

func (m *FlowerOccupyBaseInfoManager) HasMarquee(id uint32) bool {
	info := m.Index(id)
	if info == nil {
		return false
	}
	return info.ChatAdmin > 0
}

// 获取当前森林中对应品质的id
func (m *FlowerOccupyBaseInfoManager) GetIDByTimberAndRare(timber, rare uint32) uint32 {
	return m.RareDatas[timber][rare]
}
