package goxml

import "app/protos/out/common"

type BalanceArenaConfigInfoMEx struct {
	*BalanceArenaConfigInfoM
	PublicInitialRandomHero   uint32
	PublicInitialHeroStar     uint32
	PrivateInitialRandomHero  uint32
	InitialRandomArtifact     uint32
	PublicInitialArtifactStar uint32
	OptionalHeroNum           uint32
	OptionalArtifactNum       uint32
	PointWin3                 uint32
	PointWin2                 uint32
	PointWin1                 uint32
	PointWin0                 uint32
}

func newBalanceArenaConfigInfoMEx(xmlData *XmlData) *BalanceArenaConfigInfoMEx {
	m := &BalanceArenaConfigInfoMEx{
		BalanceArenaConfigInfoM: &BalanceArenaConfigInfoM{
			xmlData: xmlData,
		},
	}
	xmlData.addManager(m)
	return m
}

func (m *BalanceArenaConfigInfoMEx) ignoreForCheck() bool {
	return false
}

func (m *BalanceArenaConfigInfoMEx) load(dir string, isShow bool) error {
	err := m.BalanceArenaConfigInfoM.load(dir, isShow)
	if err != nil {
		return err
	}
	m.PublicInitialRandomHero = m.GetRecordByKey("public_initial_random_hero").Count
	m.PublicInitialHeroStar = m.GetRecordByKey("public_initial_hero_star").Count
	m.PrivateInitialRandomHero = m.GetRecordByKey("pivate_initial_random_hero").Count
	m.InitialRandomArtifact = m.GetRecordByKey("initial_random_artifact").Count
	m.PublicInitialArtifactStar = m.GetRecordByKey("public_initial_artifact_star").Count
	m.OptionalHeroNum = m.GetRecordByKey("optional_hero_num").Count
	m.OptionalArtifactNum = m.GetRecordByKey("optional_artifact_num").Count
	m.PointWin3 = m.GetRecordByKey("point_win_3").Count
	m.PointWin2 = m.GetRecordByKey("point_win_2").Count
	m.PointWin1 = m.GetRecordByKey("point_win_1").Count
	m.PointWin0 = m.GetRecordByKey("point_win_0").Count
	return nil
}

func (m *BalanceArenaConfigInfoMEx) checkData() error {
	err := m.BalanceArenaConfigInfoM.checkData()
	if err != nil {
		return err
	}
	return nil
}

func (m *BalanceArenaConfigInfoMEx) GetScoreByWinTimes(winTimes uint32) uint32 {
	switch winTimes {
	case 3:
		return m.PointWin3
	case 2:
		return m.PointWin2
	case 1:
		return m.PointWin1
	case 0:
		return m.PointWin0
	}
	return 0
}

func (m *BalanceArenaConfigInfoMEx) CalcFightResult(leftScore uint32) uint32 {
	if leftScore == m.PointWin3 {
		return uint32(common.BALANCE_ARENA_RESULT_BAR_3VS0)
	}
	if leftScore == m.PointWin2 {
		return uint32(common.BALANCE_ARENA_RESULT_BAR_2VS1)
	}
	if leftScore == m.PointWin1 {
		return uint32(common.BALANCE_ARENA_RESULT_BAR_1VS2)
	}
	if leftScore == m.PointWin0 {
		return uint32(common.BALANCE_ARENA_RESULT_BAR_0VS3)
	}
	return 0
}
