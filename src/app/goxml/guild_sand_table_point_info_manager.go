package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildSandTablePointInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32][]*GuildSandTablePointInfo
}

func newGuildSandTablePointInfoManager(xmlData *XmlData) *GuildSandTablePointInfoManager {
	m := &GuildSandTablePointInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTablePointInfoManager) name() string {
	return "guild_sand_table_point_info.xml"
}

func (m *GuildSandTablePointInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTablePointInfoManager) checkData() error {
	return nil
}

func (g *GuildSandTablePointInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTablePointInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_point_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	g.Datas = make(map[uint32][]*GuildSandTablePointInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Rank == 0 {
			panic(fmt.Sprintf("guild_sand_table_point_info data id:%d rank:%d must > 0", data.Id, data.Rank))
		}
		_, exist := g.Datas[data.GuildRankAwardGroup]
		if !exist {
			g.Datas[data.GuildRankAwardGroup] = make([]*GuildSandTablePointInfo, 0, 4)
		}

		if len(g.Datas[data.GuildRankAwardGroup]) > 0 {
			lastData := g.Datas[data.GuildRankAwardGroup][len(g.Datas[data.GuildRankAwardGroup])-1]
			if data.Rank <= lastData.Rank {
				panic(fmt.Sprintf("guild_sand_table_point_info data id:%d must be increate", data.Id))
			}
		}

		g.Datas[data.GuildRankAwardGroup] = append(g.Datas[data.GuildRankAwardGroup], data)
	}
	return nil
}

func (m *GuildSandTablePointInfo) GetRank() uint32 {
	return m.Rank
}

func (m *GuildSandTablePointInfoManager) GetRank(group, rank uint32) *GuildSandTablePointInfo {
	infos := m.Datas[group]
	return GetDataLessAndClosedByRank(infos, rank)
}
