package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"math"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type FlowerPlantTypeInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*FlowerPlantTypeInfo
}

func newFlowerPlantTypeInfoManager(xmlData *XmlData) *FlowerPlantTypeInfoManager {
	m := &FlowerPlantTypeInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *FlowerPlantTypeInfoManager) name() string {
	return "FlowerPlantTypeInfo"
}

func (m *FlowerPlantTypeInfoManager) ignoreForCheck() bool {
	return false
}

func (m *FlowerPlantTypeInfoManager) checkData() error {
	return nil
}

func (m *FlowerPlantTypeInfoManager) load(dir string, isShow bool) error {
	tmp := &FlowerPlantTypeInfos{}
	fileName := filepath.Join(dir, "flower_plant_type_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*FlowerPlantTypeInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()

		if ptr, exist := m.Datas[data.Type]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Type] = data
		}
	}
	return nil
}

func (m *FlowerPlantTypeInfoManager) GetRecordByType(t uint32) *FlowerPlantTypeInfo {
	return m.Datas[t]
}

func (m *FlowerPlantTypeInfoManager) Index(key uint32) *FlowerPlantTypeInfo {
	return m.Datas[key]
}

func (m *FlowerPlantTypeInfoManager) HighestSeedCost(plantType uint32) []*cl.Resource {
	typeInfo, exist := m.Datas[plantType]
	if !exist {
		return nil
	}
	return typeInfo.HighestClRes
}

func (m *FlowerPlantTypeInfoManager) ChangeGoblinCost(plantType uint32) []*cl.Resource {
	typeInfo, exist := m.Datas[plantType]
	if !exist {
		return nil
	}
	return typeInfo.ChangeClRes
}

func (m *FlowerPlantTypeInfoManager) CD2Res(leftTime int64, plantType uint32) []*cl.Resource {
	typeInfo, exist := m.Datas[plantType]
	if !exist {
		return nil
	}
	count := math.Ceil(float64(leftTime) / float64(typeInfo.SpeedMinute*SecondPerMinute))
	var cost []*cl.Resource
	for _, v := range typeInfo.SpeedClRes {
		cost = append(cost, &cl.Resource{
			Type:  v.Type,
			Value: v.Value,
			Count: v.Count * uint32(count),
		})
	}
	return cost
}
