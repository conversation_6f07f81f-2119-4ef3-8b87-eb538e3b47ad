package goxml

import (
	"app/protos/out/common"
	"time"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

// 巅峰竞技场
const (
	PeakPlayerCount           uint32 = 128                                  //参数人数
	PeakGroupCount            uint32 = 16                                   //小组赛的小组数
	PeakRound1TotalMatchCount        = PeakPlayerCount / 2                  //小组赛首轮总场次数
	PeakGroupRound1MatchCount        = PeakPlayerCount / PeakGroupCount / 2 //各小组赛第1轮场次数
	PeakGroupRound2MatchCount        = PeakGroupRound1MatchCount / 2        //各小组赛第2轮场次数

	PeakRound1 uint32 = 1 //赛程轮 - 第1轮
	PeakRound2 uint32 = 2 //赛程轮 - 第2轮
	PeakRound3 uint32 = 3 //赛程轮 - 第3轮
	PeakRound4 uint32 = 4 //赛程轮 - 第4轮
	PeakRound5 uint32 = 5 //赛程轮 - 第5轮
	PeakRound6 uint32 = 6 //赛程轮 - 第6轮
	PeakRound7 uint32 = 7 //赛程轮 - 第7轮

	PeakAreaUpper = 1 //赛区 - 上半区
	PeakAreaLower = 2 //赛区 - 下半区

	PeakPeriodGroup    uint32 = 1 //赛段 - 小组赛
	PeakPeriodFinal    uint32 = 2 //赛段 - 决赛
	PeakPeriodChampion uint32 = 3 //赛段 - 冠军赛

	PeakRewardTypePhase  uint32 = 1 //奖励类型 - 小周期奖励
	PeakRewardTypeSeason uint32 = 2 //奖励类型 - 赛季奖励

	PeakPhase1 uint32 = 1 //小周期 - 期数1
	PeakPhase2 uint32 = 2 //小周期 - 期数2
	PeakPhase3 uint32 = 3 //小周期 - 期数3
	PeakPhase4 uint32 = 4 //小周期 - 期数4

	PeakTop8Count uint32 = 8 //8强人数

	PeakGuessMatchCount = 3 //竞猜场次数 - xxx必须大于1，不然生成竞猜比赛时会有bug
	PeakGuessWinParam   = 2 //竞猜猜中赔率（2倍）
)

// 巅峰竞技场 - 阵容与队伍数量
func GetPeakTeamNum(fid uint32) int {
	if fid == uint32(common.FORMATION_ID_FI_PEAK_1) {
		return 5
	} else if fid == uint32(common.FORMATION_ID_FI_PEAK_2) {
		return 7
	}
	return 0
}

// 巅峰竞技场 - 赛程轮
var PeakRounds = []uint32{
	PeakRound1,
	PeakRound2,
	PeakRound3,
	PeakRound4,
	PeakRound5,
	PeakRound6,
	PeakRound7,
}

// 巅峰竞技场 - 赛区
var PeakAreas = []uint32{PeakAreaUpper, PeakAreaLower}

// 排名对应的分组
var PeakRank2Pos = [128]uint32{
	1, 9, 2, 10, 3, 11, 4, 12, 5, 13, 6, 14, 7, 15, 8, 16,
	1, 9, 2, 10, 3, 11, 4, 12, 5, 13, 6, 14, 7, 15, 8, 16,
	1, 9, 2, 10, 3, 11, 4, 12, 5, 13, 6, 14, 7, 15, 8, 16,
	1, 9, 2, 10, 3, 11, 4, 12, 5, 13, 6, 14, 7, 15, 8, 16,
	16, 8, 15, 7, 14, 6, 13, 5, 12, 4, 11, 3, 10, 2, 9, 1,
	16, 8, 15, 7, 14, 6, 13, 5, 12, 4, 11, 3, 10, 2, 9, 1,
	16, 8, 15, 7, 14, 6, 13, 5, 12, 4, 11, 3, 10, 2, 9, 1,
	16, 8, 15, 7, 14, 6, 13, 5, 12, 4, 11, 3, 10, 2, 9, 1,
}

// 巅峰竞技场 - 首轮轮次
func GetPeakFirstRound() uint32 {
	return PeakRounds[0]
}

// 巅峰竞技场 - 末轮轮次
func GetPeakLastRound() uint32 {
	return PeakRounds[len(PeakRounds)-1]
}

// 巅峰竞技场 - 最大的周期
func GetPeakLastPhase() uint32 {
	return PeakPhases[len(PeakPhases)-1]
}

// 巅峰竞技场 - 验证是否是赛季的最后一轮
func IsLastRoundToSeason(phase, round uint32) bool {
	return phase == GetPeakLastPhase() && round == GetPeakLastRound()
}

// 巅峰竞技场 - 赛段与轮
var PeakPeriodRounds = map[uint32][]uint32{
	PeakPeriodGroup:    {PeakRound1, PeakRound2, PeakRound3},
	PeakPeriodFinal:    {PeakRound4, PeakRound5, PeakRound6},
	PeakPeriodChampion: {PeakRound7},
}

// 巅峰竞技场 - 根据赛段获取对应轮次列表
func GetPeakRoundsByPeriod(period uint32) []uint32 {
	return PeakPeriodRounds[period]
}

// 巅峰竞技场 - 根据轮次获取对应的赛段
func GetPeakPeriodByRound(round uint32) uint32 {
	for period, rounds := range PeakPeriodRounds {
		if util.InUint32s(rounds, round) {
			return period
		}
	}
	return 0
}

// 巅峰竞技场 - 奖励类型
var PeakRewardTypes = []uint32{PeakRewardTypePhase, PeakRewardTypeSeason}

// 巅峰竞技场 - 小周期
var PeakPhases = []uint32{
	PeakPhase1,
	PeakPhase2,
	PeakPhase3,
	PeakPhase4,
}

// 巅峰竞技场 - 每轮参赛人数
func CalcPeakFighterCount(round uint32) uint32 {
	if round == 0 {
		l4g.Errorf("[FATAL] CalcPeakFighterCount: param err, round is 0")
		return 0
	}
	return PeakPlayerCount >> (round - 1)
}

// 巅峰竞技场 - 每轮比赛数量
func CalcPeakMatchCount(round uint32) uint32 {
	if round == 0 {
		l4g.Errorf("[FATAL] CalcPeakMatchCount: param err, round is 0")
		return 0
	}
	return PeakPlayerCount >> round
}

// 巅峰竞技场 - 是否是首个小周期
func IsFirstPeakPhase(phase uint32) bool {
	return phase == PeakPhases[0]
}

// 巅峰竞技场 - 是否是最后一个小周期
func IsLastPeakPhase(phase uint32) bool {
	return phase == PeakPhases[len(PeakPhases)-1]
}

// 巅峰竞技场 - 小周期间隔时间
var PeakPhaseDuration int64 = int64(len(PeakRounds)) * util.DaySecs

// 巅峰竞技场 - 玩法是否处于开放时间
func IsInPeakPhase(gXmlData *XmlData) bool {
	now := time.Now().Unix()
	phase := gXmlData.PeakInfoM.GetPeakPhase(now)
	return phase != nil
}

// 巅峰竞技场 - 竞猜是否处于开放时间
func IsPeakGuessOpen(gXmlData *XmlData) bool {
	now := time.Now().Unix()
	phase := gXmlData.PeakInfoM.GetPeakPhase(now)

	if phase != nil {
		duration := gXmlData.PeakConfigInfoM.GetRoundDuration()
		for start := phase.Start; start < phase.End; start += duration {
			end := start + gXmlData.PeakConfigInfoM.getRoundFightStartTm()
			if now >= start && now < end {
				return true
			}
		}
	}

	return false
}

// 巅峰竞技场 - 验证排行榜类型的合法性
func IsLegalPeakRankType(rankType uint32) bool {
	if rankType == uint32(common.PEAK_RANK_TYPE_PRT_SEASON_TOP3) ||
		rankType == uint32(common.PEAK_RANK_TYPE_PRT_PHASE_TOP8) ||
		rankType == uint32(common.PEAK_RANK_TYPE_PRT_SEASON_ALL) {
		return true
	}
	return false
}
