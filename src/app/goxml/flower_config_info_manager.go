package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type FlowerConfigInfoManager struct {
	xmlData *XmlData
	Datas   map[string]*FlowerConfigInfo

	logTime               int64
	searchCD              int64
	shareCD               int64
	occupyAwardInterval   int64
	occupyMaxTime         int64
	occupyAddTime         int64
	occupyFightResumeCD   int64
	occupyExtendLimit     uint32 //延长据点占领时间的次数上限
	occupyExtendPriceID   uint32 //延长据点占领时间，消耗的价格组id
	occupyFightMaxNum     uint32
	occupyFightMaxBuyNum  uint32
	occupyFightNumPriceID uint32
	occupyProtectTime     int64
	guildBuff             map[uint32]int64
	attackAllyCost        []*cl.Resource
	guidance              []*GuideGoblin
	friendPoint           uint32
}

type GuideGoblin struct {
	ID    uint32
	Score uint32
}

func newFlowerConfigInfoManager(xmlData *XmlData) *FlowerConfigInfoManager {
	m := &FlowerConfigInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *FlowerConfigInfoManager) name() string {
	return "FlowerConfigInfo"
}

func (m *FlowerConfigInfoManager) ignoreForCheck() bool {
	return false
}

func (m *FlowerConfigInfoManager) checkData() error {
	return nil
}

func (m *FlowerConfigInfoManager) load(dir string, isShow bool) error {
	tmp := &FlowerConfigInfos{}
	fileName := filepath.Join(dir, "flower_config_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	l4g.Debugf("FlowerConfigInfoM. data: %+v", tmp.Datas)
	m.Datas = make(map[string]*FlowerConfigInfo, len(tmp.Datas))
	m.guildBuff = make(map[uint32]int64)
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Count == 0 {
			panic(fmt.Sprintf("load %s fail: count is 0. %v", fileName, data))
		}
		if data.Key == "FIGHT_COST_EXTRA" && data.Type == 0 {
			panic(fmt.Sprintf("load %s fail: FIGHT_COST_EXTRA err, type is 0. %v", fileName, data))
		}
		m.Datas[data.Key] = data
	}
	m.logTime = int64(m.Datas["LOG_TIME"].Count * 3600) //小时转成秒
	m.searchCD = int64(m.Datas["SEARCH_CD"].Count)
	m.shareCD = int64(m.Datas["SEND_CD"].Count)
	attackAllyCost := m.Datas["FIGHT_COST_EXTRA"]
	m.attackAllyCost = []*cl.Resource{
		{
			Type:  attackAllyCost.Type,
			Value: attackAllyCost.Value,
			Count: attackAllyCost.Count,
		},
	}
	m.friendPoint = m.Datas["FRIEND_POINT"].Count
	m.guildBuff[FlowerGuildBuffThree] = int64(m.Datas["GUILD_EFFECT_3"].Count)
	m.guildBuff[FlowerGuildBuffFour] = int64(m.Datas["GUILD_EFFECT_4"].Count)
	m.guildBuff[FlowerGuildBuffFive] = int64(m.Datas["GUILD_EFFECT_5"].Count)
	m.occupyAwardInterval = int64(m.Datas["BASE_REWARD_TIME"].Count * 60)
	m.occupyMaxTime = int64(m.Datas["BASE_COLLECT_TIME"].Count * 60)
	m.occupyAddTime = int64(m.Datas["BASE_COLLECT_TIME_ADD"].Count * 60)
	m.occupyFightResumeCD = int64(m.Datas["BASE_FIGHT_RESUME_TIME"].Count * 60)
	m.occupyExtendLimit = uint32(m.Datas["BASE_COLLECT_TIME_ADD_NUM"].Count)
	m.occupyExtendPriceID = uint32(m.Datas["BASE_COLLECT_TIME_ADD_PRICE"].Count)
	m.occupyFightMaxNum = uint32(m.Datas["BASE_FIGHT_TIME"].Count)
	m.occupyFightMaxBuyNum = uint32(m.Datas["BASE_FIGHT_BUY"].Count)
	m.occupyFightNumPriceID = uint32(m.Datas["BASE_FIGHT_PRICE"].Count)
	m.occupyProtectTime = int64(m.Datas["OCCUPY_CD"].Count)

	m.guidance = make([]*GuideGoblin, 0, FlowerGuideStep)
	if gVal, gExist := m.Datas["FIRST_GOBLIN_1"]; gExist {
		if sVal, sExist := m.Datas["FIRST_SCORE_1"]; sExist {
			if !m.xmlData.FlowerPlantGoblinInfoM.CheckGuidanceGoblinScore(gVal.Count, sVal.Count) {
				panic(fmt.Sprintf("load %s fail: goblin guidance data 1 err. id:%d, score:%d",
					fileName, gVal.Count, sVal.Count))
			}
			m.guidance = append(m.guidance, &GuideGoblin{
				ID:    gVal.Count,
				Score: sVal.Count,
			})
		}
	}
	if gVal, gExist := m.Datas["FIRST_GOBLIN_2"]; gExist {
		if sVal, sExist := m.Datas["FIRST_SCORE_2"]; sExist {
			if !m.xmlData.FlowerPlantGoblinInfoM.CheckGuidanceGoblinScore(gVal.Count, sVal.Count) {
				panic(fmt.Sprintf("load %s fail: goblin guidance data 2 err. id:%d, score:%d",
					fileName, gVal.Count, sVal.Count))
			}
			m.guidance = append(m.guidance, &GuideGoblin{
				ID:    gVal.Count,
				Score: sVal.Count,
			})
		}
	}
	if gVal, gExist := m.Datas["FIRST_GOBLIN_3"]; gExist {
		if sVal, sExist := m.Datas["FIRST_SCORE_3"]; sExist {
			if !m.xmlData.FlowerPlantGoblinInfoM.CheckGuidanceGoblinScore(gVal.Count, sVal.Count) {
				panic(fmt.Sprintf("load %s fail: goblin guidance data 3 err. id:%d, score:%d",
					fileName, gVal.Count, sVal.Count))
			}
			m.guidance = append(m.guidance, &GuideGoblin{
				ID:    gVal.Count,
				Score: sVal.Count,
			})
		}
	}
	if len(m.guidance) != int(FlowerGuideStep) {
		panic(fmt.Sprintf("load %s fail: goblin guidance num err. num:%d", fileName, len(m.guidance)))
	}

	return nil
}

func (m *FlowerConfigInfoManager) Index(key string) *FlowerConfigInfo {
	return m.Datas[key]
}

func (m *FlowerConfigInfoManager) IsInGuidance(step uint32) bool {
	return step < FlowerGuideStep
}

func (m *FlowerConfigInfoManager) GetGuidanceInfo(step uint32) *GuideGoblin {
	return m.guidance[step]
}

func (m *FlowerConfigInfoManager) GetSearchCD() int64 {
	return m.searchCD
}

func (m *FlowerConfigInfoManager) GetShareCD() int64 {
	return m.shareCD
}

func (m *FlowerConfigInfoManager) GetLogTime() int64 {
	return m.logTime
}

func (m *FlowerConfigInfoManager) GetOccupyAwardInterval() int64 {
	return m.occupyAwardInterval
}

func (m *FlowerConfigInfoManager) GetOccupyMaxTime() int64 {
	return m.occupyMaxTime
}

func (m *FlowerConfigInfoManager) GetOccupyAddTime() int64 {
	return m.occupyAddTime
}

func (m *FlowerConfigInfoManager) GetGuildBuff(level uint32) int64 {
	return m.guildBuff[level]
}

func (m *FlowerConfigInfoManager) GetAttackAllyCost() []*cl.Resource {
	return m.attackAllyCost
}

func (m *FlowerConfigInfoManager) GetOccupyFightResumeCD() int64 {
	return m.occupyFightResumeCD
}

func (m *FlowerConfigInfoManager) GetOccupyExtendLimit() uint32 {
	return m.occupyExtendLimit
}

func (m *FlowerConfigInfoManager) GetOccupyExtendPriceID() uint32 {
	return m.occupyExtendPriceID
}

func (m *FlowerConfigInfoManager) GetOccupyMaxFightCount() uint32 {
	return m.occupyFightMaxNum
}

func (m *FlowerConfigInfoManager) GetOccupyFightMaxBuyNum() uint32 {
	return m.occupyFightMaxBuyNum
}

func (m *FlowerConfigInfoManager) GetOccupyFightNumPriceID() uint32 {
	return m.occupyFightNumPriceID
}

func (m *FlowerConfigInfoManager) GetOccupyProtectTime() int64 {
	return m.occupyProtectTime
}

// 协助模式获取友情点资源
func (m *FlowerConfigInfoManager) GetAssistRes() uint32 {
	return m.friendPoint
}
