package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildSandTableTechTaskInfoManager struct {
	xmlData            *XmlData
	Datas              map[uint32]*GuildSandTableTechTaskInfo
	AllEvent           map[uint32]struct{}
	RoundResetTaskType map[uint32]struct{}
	TaskNum            int
}

func newGuildSandTableTechTaskInfoManager(xmlData *XmlData) *GuildSandTableTechTaskInfoManager {
	m := &GuildSandTableTechTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableTechTaskInfoManager) name() string {
	return "guild_sand_table_tech_task_info.xml"
}

func (m *GuildSandTableTechTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableTechTaskInfoManager) checkData() error {
	return nil
}

func (m *GuildSandTableTechTaskInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableTechTaskInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_tech_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*GuildSandTableTechTaskInfo, len(tmp.Datas))
	m.AllEvent = make(map[uint32]struct{})
	m.RoundResetTaskType = make(map[uint32]struct{})

	for _, data := range tmp.Datas {
		data.prepare()
		m.Datas[data.Id] = data
		taskTypeInfo := m.xmlData.TaskTypeInfoM.Index(data.TypeId)
		if taskTypeInfo == nil {
			panic(fmt.Sprintf("TaskTypeInfoM no taskTypeInfo. data.TypeId:%d", data.TypeId))
		}
		m.AllEvent[taskTypeInfo.Type] = struct{}{}
		if data.TaskResetRype == 2 {
			m.RoundResetTaskType[data.TypeId] = struct{}{}
		}
	}
	m.TaskNum = len(m.Datas)
	return nil
}

func (m *GuildSandTableTechTaskInfoManager) Index(key uint32) *GuildSandTableTechTaskInfo {
	return m.Datas[key]
}

func (m *GuildSandTableTechTaskInfoManager) GetAllEvent() map[uint32]struct{} {
	return m.AllEvent
}

func (m *GuildSandTableTechTaskInfoManager) IsRoundReset(taskType uint32) bool {
	_, exist := m.RoundResetTaskType[taskType]
	return exist
}

func (m *GuildSandTableTechTaskInfoManager) GetTaskNum() int {
	return m.TaskNum
}
