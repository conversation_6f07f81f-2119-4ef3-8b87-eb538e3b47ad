package goxml

import (
	"fmt"
	"math"
	"path/filepath"
	"sort"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type ArenaMatchInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*ArenaMatchInfo
	sDatas  []*ArenaMatchInfo
}

func newArenaMatchInfoManager(xmlData *XmlData) *ArenaMatchInfoManager {
	m := &ArenaMatchInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *ArenaMatchInfoManager) name() string {
	return "ArenaMatchInfo"
}

func (m *ArenaMatchInfoManager) ignoreForCheck() bool {
	return false
}

func (m *ArenaMatchInfoManager) checkData() error {
	return nil
}

func (m *ArenaMatchInfoManager) load(dir string, isShow bool) error {
	tmp := &ArenaMatchInfos{}
	fileName := filepath.Join(dir, "arena_match_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debugf("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*ArenaMatchInfo, len(tmp.Datas))
	}
	m.sDatas = make([]*ArenaMatchInfo, 0, len(tmp.Datas))

	var preScore uint32
	maxExpandParam := int64(m.xmlData.ArenaConfigInfoM.MatchExpandCount)
	for _, data := range tmp.Datas {
		if data.Score <= preScore {
			panic(fmt.Sprintf("load config %s fail: score is not incremental. id:%d", fileName, data.Id))
		}
		preScore = data.Score

		//验证各阶段分数上下限，大小关系的合理性
		if data.HighMinAdd > data.HighMaxAdd || data.MidMinAdd > data.MidMaxAdd || data.LowMinAdd > data.LowMaxAdd {
			panic(fmt.Sprintf("load config %s fail: minAdd and maxAdd err. id:%d", fileName, data.Id))
		}

		//验证分数扩展下限是否为正数
		if int64(data.Score)+maxExpandParam*data.HighMinAdd < 0 {
			panic(fmt.Sprintf("load config %s fail: HighMinAdd err. id:%d", fileName, data.Id))
		}
		if int64(data.Score)+maxExpandParam*data.MidMinAdd < 0 {
			panic(fmt.Sprintf("load config %s fail: MidMinAdd err. id:%d", fileName, data.Id))
		}
		if int64(data.Score)+maxExpandParam*data.LowMinAdd < 0 {
			panic(fmt.Sprintf("load config %s fail: LowMinAdd err. id:%d", fileName, data.Id))
		}

		//机器人分数不能为0
		if data.BotHigh == 0 || data.BotMid == 0 || data.BotLow == 0 {
			panic(fmt.Sprintf("load config %s fail: bot score is 0. id:%d", fileName, data.Id))
		}

		//验证扩展类型
		if !IsLegalArenaMatchExtMode(data.ExtModeHigh) || !IsLegalArenaMatchExtMode(data.ExtModeMid) || !IsLegalArenaMatchExtMode(data.ExtModeLow) {
			panic(fmt.Sprintf("load config %s fail: extMode err. id:%d", fileName, data.Id))
		}

		//有扩展类型，其对应的限制分数必然大于0
		if data.ExtModeHigh != ArenaMatchExtModeNone {
			if data.DownLimitHigh == 0 {
				panic(fmt.Sprintf("load config %s fail: ExtModeHigh and DownLimitHigh not match. id:%d", fileName, data.Id))
			}
		}
		if data.ExtModeMid != ArenaMatchExtModeNone {
			if data.DownLimitMid == 0 {
				panic(fmt.Sprintf("load config %s fail: ExtModeMid and DownLimitMid not match. id:%d", fileName, data.Id))
			}
		}
		if data.ExtModeLow != ArenaMatchExtModeNone {
			if data.DownLimitLow == 0 {
				panic(fmt.Sprintf("load config %s fail: ExtModeLow and DownLimitLow not match. id:%d", fileName, data.Id))
			}
		}

		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
		m.sDatas = append(m.sDatas, data)
	}

	if len(m.sDatas) == 0 {
		panic(fmt.Sprintf("load config %s fail: no data", fileName))
	}
	return nil
}

func (m *ArenaMatchInfoManager) GetRecordById(id uint32) *ArenaMatchInfo {
	return m.Datas[id]
}

func (m *ArenaMatchInfoManager) Index(key uint32) *ArenaMatchInfo {
	return m.Datas[key]
}

func (m *ArenaMatchInfoManager) Match(score uint32) *ArenaMatchInfo {
	i := sort.Search(len(m.sDatas), func(i int) bool {
		return m.sDatas[i].Score > score
	})

	i--
	if i < 0 {
		i = 0
	}
	return m.sDatas[i]
}

// 计算分数
// 仅当pvp战胜对手时，才需要考虑修正对手分数的逻辑
// @param info *ArenaMatchInfo
// @param uint32 winnerScore 战胜者分数
// @param uint32 loserScore 战败者分数
// @param uint32 stage 对手所处的匹配档位（仅pvp时生效，高中低档）
// @param bool needFixScore 是否需要修正积分
// @return uint32 战胜者增加的积分
// @return uint32 失败者极少的积分
func (m *ArenaMatchInfoManager) EloScores(info *ArenaMatchInfo, winnerScore,
	loserScore, stage uint32, needFixScore bool) (uint32, uint32) {
	//ELO积分算法
	diff := float64(loserScore) - float64(winnerScore)
	p := math.Pow(10, diff/float64(info.EloZ))

	addScore := uint32(float64(info.EloK) * (1 - 1/(1+p)))
	if needFixScore {
		addScore = m.fixScore(info, addScore, stage)
	}

	if addScore < m.xmlData.ArenaConfigInfoM.WinnerMinAddScore {
		addScore = m.xmlData.ArenaConfigInfoM.WinnerMinAddScore
	}

	subScore := uint32(float64(addScore) * (float64(info.DeclineParam) / BaseFloat))
	if subScore == 0 {
		subScore = 1
	}

	l4g.Debugf("EloScores. info:%+v, winnerScore:%d, loserScore:%d, addScore:%d, subScore:%d, diff:%v, p:%v",
		info, winnerScore, loserScore, addScore, subScore, diff, p)
	return addScore, subScore
}

// 根据保底分数，修正玩家最终获得的分数
func (m *ArenaMatchInfoManager) fixScore(info *ArenaMatchInfo, score, stage uint32) uint32 {
	var floorScore uint32
	if info != nil {
		switch stage {
		case 0:
			floorScore = info.HighFloors
		case 1:
			floorScore = info.MidFloors
		case 2:
			floorScore = info.LowFloors
		}
	}

	if score < floorScore {
		return floorScore
	}
	return score
}
