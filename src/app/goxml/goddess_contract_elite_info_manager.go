package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GoddessContractEliteInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GoddessContractEliteInfo
	goddess map[uint32][]*GoddessContractEliteInfo
	len     int
}

func newGoddessContractEliteInfoManager(xmlData *XmlData) *GoddessContractEliteInfoManager {
	m := &GoddessContractEliteInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GoddessContractEliteInfoManager) name() string {
	return "GoddessContractEliteInfo"
}

func (m *GoddessContractEliteInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GoddessContractEliteInfoManager) checkData() error {
	return nil
}

func (m *GoddessContractEliteInfoManager) load(dir string, isShow bool) error {
	tmp := &GoddessContractEliteInfos{}
	fileName := filepath.Join(dir, "goddess_contract_elite_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}

	m.Datas = make(map[uint32]*GoddessContractEliteInfo, len(tmp.Datas))
	m.goddess = make(map[uint32][]*GoddessContractEliteInfo)
	for _, data := range tmp.Datas {
		data.prepare()
		_, exist := m.goddess[data.Goddess]
		if !exist {
			m.goddess[data.Goddess] = make([]*GoddessContractEliteInfo, 0, 5)
		}
		m.goddess[data.Goddess] = append(m.goddess[data.Goddess], data)

		if ptr, exist := m.Datas[data.EliteId]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.EliteId] = data
		}
	}
	m.len = len(m.Datas)
	return nil
}

func (m *GoddessContractEliteInfoManager) Index(index uint32) *GoddessContractEliteInfo {
	return m.Datas[index]
}

func (m *GoddessContractEliteInfoManager) Len() int {
	return m.len
}

func (m *GoddessContractEliteInfoManager) GoddessElite(goddessID uint32) []*GoddessContractEliteInfo {
	return m.goddess[goddessID]
}
