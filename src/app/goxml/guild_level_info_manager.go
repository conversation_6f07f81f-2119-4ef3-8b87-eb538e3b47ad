package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildLevelInfoManager struct {
	xmlData        *XmlData
	Datas          map[uint32]*GuildLevelInfo
	maxMemberCount uint32
	maxLevel       uint32
}

func newGuildLevelInfoManager(xmlData *XmlData) *GuildLevelInfoManager {
	m := &GuildLevelInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildLevelInfoManager) name() string {
	return "GuildLevelInfo"
}

func (m *GuildLevelInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildLevelInfoManager) checkData() error {
	return nil
}

func (m *GuildLevelInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildLevelInfos{}
	fileName := filepath.Join(dir, "guild_level_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GuildLevelInfo, len(tmp.Datas))
	}
	for _, data := range tmp.Datas {
		data.prepare()

		if data.Member > m.maxMemberCount {
			m.maxMemberCount = data.Member
		}

		if data.Level > m.maxLevel {
			m.maxLevel = data.Level
		}

		if ptr, exist := m.Datas[data.Level]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Level] = data
		}
	}
	return nil
}

func (m *GuildLevelInfoManager) GetRecordByLevel(level uint32) *GuildLevelInfo {
	return m.Datas[level]
}

func (m *GuildLevelInfoManager) Index(key uint32) *GuildLevelInfo {
	return m.Datas[key]
}

func (m *GuildLevelInfoManager) GetMaxMemberCount() uint32 {
	return m.maxMemberCount
}

func (m *GuildLevelInfoManager) GetMaxLevel() uint32 {
	return m.maxLevel
}
