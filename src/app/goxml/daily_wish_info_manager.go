package goxml

import (
	"app/protos/out/cl"
	"app/protos/out/common"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type DailyWishInfoManager struct {
	xmlData  *XmlData
	ActGroup map[uint32][]*DailyWishInfo //round id
}

func newDailyWishInfoManager(xmlData *XmlData) *DailyWishInfoManager {
	m := &DailyWishInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *DailyWishInfoManager) name() string {
	return "DailyWishInfo"
}

func (m *DailyWishInfoManager) ignoreForCheck() bool {
	return false
}

func (m *DailyWishInfoManager) checkData() error {
	return nil
}

func (m *DailyWishInfoManager) load(dir string, isShow bool) error {
	tmp := &DailyWishInfos{}
	fileName := filepath.Join(dir, "daily_wish_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.ActGroup = make(map[uint32][]*DailyWishInfo, 2)
	checkId := uint32(0)
	for _, data := range tmp.Datas {
		data.prepare()
		if len(data.ClRes) == 0 {
			panic(fmt.Sprintf("fileName:%s data resource is nil", fileName))
		}
		_, exist := m.ActGroup[data.Round]
		if !exist {
			m.ActGroup[data.Round] = make([]*DailyWishInfo, 0, int(common.DAILY_WISH_WEIGHT_END_ID))
			checkId = 0
		}
		checkId++
		if checkId != data.Id {
			panic(fmt.Sprintf("fileName:%s data: round:%d id:%d checkid failed", fileName, data.Round, data.Id))
		}
		m.ActGroup[data.Round] = append(m.ActGroup[data.Round], data)
	}
	return nil
}

func (m *DailyWishInfoManager) Index(round uint32) []*DailyWishInfo {
	return m.ActGroup[round]
}

func (r *DailyWishInfo) Flush2Client() *cl.DailyWishAwardInfo {
	ret := &cl.DailyWishAwardInfo{}
	ret.Awards = r.ClRes
	ret.Id = r.Id
	ret.Grade = r.Grade
	ret.ActId = r.Round
	return ret
}
