package goxml

import (
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildSandTableOreLevelInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GuildSandTableOreLevelInfo
}

func newGuildSandTableOreLevelInfoManager(xmlData *XmlData) *GuildSandTableOreLevelInfoManager {
	m := &GuildSandTableOreLevelInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildSandTableOreLevelInfoManager) name() string {
	return "guild_sand_table_ore_level_info.xml"
}

func (m *GuildSandTableOreLevelInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildSandTableOreLevelInfoManager) checkData() error {
	return nil
}

func (m *GuildSandTableOreLevelInfoManager) load(dir string, show bool) error {
	tmp := &GuildSandTableOreLevelInfos{}
	fileName := filepath.Join(dir, "guild_sand_table_ore_level_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if show {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	m.Datas = make(map[uint32]*GuildSandTableOreLevelInfo, len(tmp.Datas))

	for _, data := range tmp.Datas {
		data.prepare()
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildSandTableOreLevelInfoManager) Index(key uint32) *GuildSandTableOreLevelInfo {
	return m.Datas[key]
}
