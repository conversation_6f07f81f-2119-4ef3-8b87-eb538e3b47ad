package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GuildDungeonTaskInfoManager struct {
	xmlData       *XmlData
	Datas         map[uint32]*GuildDungeonTaskInfo
	sortTaskInfos []*GuildDungeonTaskInfo
}

func newGuildDungeonTaskInfoManager(xmlData *XmlData) *GuildDungeonTaskInfoManager {
	m := &GuildDungeonTaskInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GuildDungeonTaskInfoManager) name() string {
	return "GuildDungeonTaskInfo"
}

func (m *GuildDungeonTaskInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GuildDungeonTaskInfoManager) checkData() error {
	return nil
}

func (m *GuildDungeonTaskInfoManager) load(dir string, isShow bool) error {
	tmp := &GuildDungeonTaskInfos{}
	fileName := filepath.Join(dir, "guild_dungeon_task_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GuildDungeonTaskInfo, len(tmp.Datas))
	}
	m.sortTaskInfos = make([]*GuildDungeonTaskInfo, 0, len(tmp.Datas))
	lastLevel := uint32(0)
	for _, data := range tmp.Datas {
		data.prepare()
		if data.Level <= lastLevel {
			panic(fmt.Sprintf("check filename:%s data error. level is not ascending. lastLevel:%d thisLevel:%d",
				fileName, lastLevel, data.Level))
		}
		lastLevel = data.Level
		m.sortTaskInfos = append(m.sortTaskInfos, data)
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *data
			data = ptr
		} else {
			m.Datas[data.Id] = data
		}
	}
	return nil
}

func (m *GuildDungeonTaskInfoManager) GetRecordById(id uint32) *GuildDungeonTaskInfo {
	return m.Datas[id]
}

func (m *GuildDungeonTaskInfoManager) Index(key uint32) *GuildDungeonTaskInfo {
	return m.Datas[key]
}

func (m *GuildDungeonTaskInfoManager) GetTaskAwards(taskID uint32) []*cl.Resource {
	taskInfo := m.Index(taskID)
	if taskInfo == nil {
		return nil
	}
	return taskInfo.ClRes
}

func (m *GuildDungeonTaskInfoManager) CalcCompensateAwards(currentLevel uint32, receivedIds []uint32) []*cl.Resource {
	awards := make([]*cl.Resource, 0, len(m.sortTaskInfos))
	receivedIdMap := make(map[uint32]struct{}, len(receivedIds))
	for _, id := range receivedIds {
		receivedIdMap[id] = struct{}{}
	}
	for _, info := range m.sortTaskInfos {
		if info.Level < currentLevel {
			if _, exist := receivedIdMap[info.Level]; !exist {
				awards = append(awards, info.ClRes...)
			}
			continue
		} else {
			break
		}
	}
	return awards
}

func (m *GuildDungeonTaskInfoManager) GetTaskNum() int {
	return len(m.sortTaskInfos)
}
