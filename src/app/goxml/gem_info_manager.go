package goxml

import (
	"app/protos/out/cl"
	"fmt"
	"path/filepath"

	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type GemInfoManager struct {
	xmlData *XmlData
	Datas   map[uint32]*GemInfoExt
	raceMap map[uint32]uint32 // race -> count
	maxRare uint32
}

type GemInfoExt struct {
	Id               uint32         //int:宝石系统id
	Rare             uint32         //int:稀有度
	BasicAttrGroup   uint32         //int:基础属性随机组
	AdvanceAttrNum   uint32         //int:高级属性条数
	AdvanceAttrGroup uint32         //int:高级属性随机组
	IsRebuild        uint32         //int:是否可置换
	DecomposeRes     []*cl.Resource // 分解获得的资源
	RebuildRes       []*cl.Resource // 置换消耗的资源
	OneLockClRes     []*cl.Resource // 锁1条属性消耗的资源
	TwoLockClRes     []*cl.Resource // 锁2条属性消耗的资源
}

func newGemInfoManager(xmlData *XmlData) *GemInfoManager {
	m := &GemInfoManager{
		xmlData: xmlData,
	}
	xmlData.addManager(m)
	return m
}

func (m *GemInfoManager) name() string {
	return "GemInfo"
}

func (m *GemInfoManager) ignoreForCheck() bool {
	return false
}

func (m *GemInfoManager) checkData() error {
	return nil
}

func (m *GemInfoManager) load(dir string, isShow bool) error {
	tmp := &GemInfos{}
	fileName := filepath.Join(dir, "gem_info.xml")
	if err := util.LoadConfig(fileName, tmp); err != nil {
		panic(fmt.Sprintf("load config %s fail: %s", fileName, err))
	} else if isShow {
		for _, data := range tmp.Datas {
			l4g.Debug("config(%s): %+v", fileName, data)
		}
	}
	if m.Datas == nil {
		m.Datas = make(map[uint32]*GemInfoExt, len(tmp.Datas))
	}
	m.maxRare = 0
	m.raceMap = make(map[uint32]uint32, len(tmp.Datas))
	for _, data := range tmp.Datas {
		data.prepare()
		// 分解获得的资源
		if len(data.DecomposeClRes) == 0 {
			panic(fmt.Sprintf("gem decompose resource config error. id: %d", data.Id))
		}
		// 检查同一个rare是否有不同的系统ID
		if _, exist := m.raceMap[data.Rare]; exist {
			panic(fmt.Sprintf("gem_info.xml config error, the same gem rare have different sysID. race: %d", data.Rare))
		}

		infoExt := &GemInfoExt{
			Id:               data.Id,
			Rare:             data.Rare,
			BasicAttrGroup:   data.BasicAttrGroup,
			AdvanceAttrNum:   data.AdvanceAttrNum,
			AdvanceAttrGroup: data.AdvanceAttrGroup,
			IsRebuild:        data.IsRebuild,
			DecomposeRes:     data.DecomposeClRes,
			RebuildRes:       data.RebuildClRes,
			OneLockClRes:     data.OneLockClRes,
			TwoLockClRes:     data.TwoLockClRes,
		}

		if m.maxRare < infoExt.Rare {
			m.maxRare = infoExt.Rare
		}
		if ptr, exist := m.Datas[data.Id]; exist {
			*ptr = *infoExt
			infoExt = ptr
		} else {
			m.Datas[data.Id] = infoExt
		}
	}
	return nil
}

func (m *GemInfoManager) Index(sysID uint32) *GemInfoExt {
	return m.Datas[sysID]
}

func (m *GemInfoManager) GetRare(sysID uint32) uint32 {
	gemInfo := m.Index(sysID)
	if gemInfo == nil {
		return 0
	}
	return gemInfo.Rare
}

func (m *GemInfoManager) GetMaxRare() uint32 {
	return m.maxRare
}

func (m *GemInfoManager) GatAllGemsByRare(rare uint32) []*GemInfoExt {
	ret := make([]*GemInfoExt, 0, len(m.Datas))
	for _, info := range m.Datas {
		if info.Rare == rare {
			ret = append(ret, info)
		}
	}
	return ret
}

func (m *GemInfoManager) GetAdvanceAttrNum(sysID uint32) uint32 {
	gemInfo := m.Index(sysID)
	if gemInfo == nil {
		return 0
	}

	return gemInfo.AdvanceAttrNum
}

// GetConvertRes :
// @param: lockAttrNum - 锁属性的条数
func (e *GemInfoExt) GetConvertRes(lockAttrNum int) []*cl.Resource {

	costs := make([]*cl.Resource, 0, 3)
	switch lockAttrNum {
	case 0:
		costs = append(costs, e.RebuildRes...)
	case 1:
		costs = append(costs, e.OneLockClRes...)
	case 2:
		costs = append(costs, e.TwoLockClRes...)
	}

	return costs
}
