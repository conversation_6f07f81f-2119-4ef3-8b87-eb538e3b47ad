package logic

import (
	"app/goxml"
	"app/protos/in/db"
	"app/version"
	"context"
	"errors"
	"sync/atomic"
	"time"

	ls "app/logic/service"
	"app/protos/in/gm"
	"app/protos/out/cl"
	"app/protos/out/ret"
	appsrv "app/service"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
	"gitlab.qdream.com/kit/sea/util"
)

type Server struct {
	lastRequestTime []int64 //注意这里是被多个gourotuine访问的数据，需要原子操作
}

func NewServer() *Server {
	return &Server{
		lastRequestTime: make([]int64, gm.ID_Fn_MAX),
	}
}

func getGMOpen() int {
	if goxml.GetData() == nil {
		return 0
	}
	return goxml.GetData().ServerInfoM.GM
}

func (s *Server) rateLimitOK(id gm.ID) bool {
	now := time.Now().Unix()
	tm := atomic.LoadInt64(&s.lastRequestTime[id])
	if util.SafeSubInt64(now, tm) < onlineNumRequestPeriod {
		l4g.Errorf("[grpc] send online num too frequently, last request time %d, now %d, id %d",
			tm, now, id)
		return false
	}
	atomic.StoreInt64(&s.lastRequestTime[id], now)
	return true
}

const (
	onlineNumRequestPeriod int64 = 10
)

func (s *Server) process(id gm.ID, req proto.Message) (proto.Message, error) {
	msg := ls.NewGRPCMessage(id, req)
	appsrv.CtrlCommand(func(ctx context.Context) {
		ls.OnGRPCMessage(ctx, msg)
	})
	ret := <-msg.Ret
	if ret == nil {
		return nil, errors.New("grpc process panic error")
	}
	l4g.Debugf("[grpc] req: %s res: %s", msg.Data, ret)
	return ret, nil
}

func (s *Server) GetUser(ctx context.Context, req *gm.UserIndex) (*gm.RetUser, error) {
	ret, err := s.process(gm.ID_Fn_GetUser, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.RetUser), nil
}

func (s *Server) ReduceResources(ctx context.Context, req *gm.Object) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_ReduceResources, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

/*func (s *Server) ChangeLogLevel(ctx context.Context, req *gm.Cmd) (*gm.Result, error) {
	level := l4g.GetFilterLevel("file")
	if level == l4g.INFO {
		l4g.ChangeFilterLevel("file", l4g.DEBUG)
		l4g.Info("change log level (debug)")
	} else {
		l4g.ChangeFilterLevel("file", l4g.INFO)
		l4g.Info("change log level (info)")
	}
	return &gm.Result{Code: uint32(ret.RET_OK)}, nil
}*/

func (s *Server) BanAccount(ctx context.Context, req *gm.BanAccountReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_BanAccount, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SendUserMail(ctx context.Context, req *gm.UsersMail) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SendUserMail, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SendServerMail(ctx context.Context, req *gm.ServerMail) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SendServerMail, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}
func (s *Server) ToggleUserLogTrace(ctx context.Context, req *gm.LogTraceOp) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_ToggleUserLogTrace, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) OnlineNum(ctx context.Context, req *gm.Cmd) (*gm.RetOnlineNum, error) {
	if !s.rateLimitOK(gm.ID_Fn_OnlineNum) {
		return &gm.RetOnlineNum{
			Code: uint32(ret.RET_ERROR),
		}, nil
	}
	ret, err := s.process(gm.ID_Fn_OnlineNum, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.RetOnlineNum), nil
}

func (s *Server) SetDungeonID(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetDungeonID, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetGuidanceClose(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetGuidanceClose, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ChangeBags(ctx context.Context, req *gm.BagsOp) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_ChangeBags, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SendQuestionnaire(ctx context.Context, req *cl.Questionnaire) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SendQuestionnaire, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SendGiftCodeInfo(ctx context.Context, req *gm.GiftCodeInfo) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SendGiftCodeInfo, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetUserLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetUserLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SendTopResource(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SendTopResource, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ClearUserResource(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ClearUserResource, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) GetActivityInfo(ctx context.Context, req *gm.UserIndex) (*gm.ActivityInfoRsp, error) {
	ret, err := s.process(gm.ID_Fn_ActivityInfo, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.ActivityInfoRsp), nil
}

func (s *Server) ResetDailyNum(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetDailyNum, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetMedalLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetMedalLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetMaze(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetMaze, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetGuildQuitTm(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetGuildQuitTm, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetTowerFloor(ctx context.Context, req *gm.TowerReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetTowerFloor, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetTrialLevel(ctx context.Context, req *gm.TrialReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetTrialLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetArenaScore(ctx context.Context, req *gm.ArenaReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetArenaScore, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetMirageFloor(ctx context.Context, req *gm.MirageReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetMirageFloor, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetScore(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetScore, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetTaleChapterFinish(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetTaleChapterFinish, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetTaleElite(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetTaleElite, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetUserResource(ctx context.Context, req *gm.UserResource) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetUserResource, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) NotifyRecharge(ctx context.Context, req *db.Order) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_NotifyRecharge, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) NotifyRefund(ctx context.Context, req *db.Order) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_NotifyRefund, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetUserVip(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetUserVip, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetTowerstarDungeonID(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetTowerstarDungeonID, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ResetTowerstar(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ResetTowerstar, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetGmConfig(ctx context.Context, req *gm.GmConfigReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SetGmConfig, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) UpdateMultiLangs(ctx context.Context, req *gm.MultiLangs) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdateMultiLangs, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) QuestionnaireFinish(ctx context.Context, req *gm.QuestionnaireFinishReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_QuestionnaireFinish, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) ReleaseOperateActivity(ctx context.Context, req *gm.OperateActivity) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_ReleaseOperateActivity, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	if err != nil {
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) ReleaseOperatePageInfo(ctx context.Context, req *gm.OperatePageInfos) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_ReleaseOperatePageInfo, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetForestLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetForestLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) BanProtocol(ctx context.Context, req *gm.BanCmd) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_BanProtocol, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetAllPushGift(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetAllPushGift, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) GetRechargeList(ctx context.Context, req *gm.RechargeListReq) (*gm.RechargeListRsp, error) {
	ret, err := s.process(gm.ID_Fn_GetRechargeList, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.RechargeListRsp), nil
}

func (s *Server) CloseGuidance(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_CloseGuidance, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetMazeTaskLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetMazeTaskLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) GetUserMailList(ctx context.Context, req *gm.UserMailListReq) (*gm.UserMailListRsp, error) {
	ret, err := s.process(gm.ID_Fn_GetUserMailList, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.UserMailListRsp), nil
}

func (s *Server) DeleteUserMail(ctx context.Context, req *gm.DeleteUserMailReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_DeleteUserMail, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetAccountTag(ctx context.Context, req *gm.AccountTagReq) (*gm.AccountTagRsp, error) {
	ret, err := s.process(gm.ID_Fn_SetAccountTag, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.AccountTagRsp), nil
}

func (s *Server) SetServerTime(ctx context.Context, req *gm.ServerTimeReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetServerTime, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) QueryServerTime(ctx context.Context, req *gm.Cmd) (*gm.ServerTimeRsp, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_QueryServerTime, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.ServerTimeRsp), nil
	}
	return &gm.ServerTimeRsp{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetSinglePushGift(ctx context.Context, req *gm.SinglePushGiftReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetSinglePushGift, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}
	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) ImportUser(ctx context.Context, req *gm.ImportUserReq) (*gm.ImportUserRsp, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_ImportUser, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.ImportUserRsp), nil
	}
	return &gm.ImportUserRsp{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetDispatchLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetDispatchLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}
	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) UpdateDivineDemon(ctx context.Context, req *gm.DivineDemonReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdateDivineDemon, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetGuildDungeonCurrentChapter(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetGuildDungeonCurrentChapter, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}
	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) UpdateAnnouncement(ctx context.Context, req *gm.AnnouncementReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdateAnnouncement, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetGuildLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetGuildLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}
	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) RecoveryGuildDungeonResetTime(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_RecoveryGuildDungeonResetTime, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}
	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetFlowerLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetFlowerLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) UpdateDailyWishActivity(ctx context.Context, req *gm.DailyWishInfo) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdateDailyWishActivity, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) UpdateArtifactDebut(ctx context.Context, req *gm.ArtifactDebutReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdateArtifactDebut, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetTowerSeasonFloor(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetTowerSeasonFloor, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) DelChatGroupTag(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_DelChatGroupTag, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) UpdateDropActivity(ctx context.Context, req *cl.DropActivityBase) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdateDropActivity, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) CheckDropActivity(ctx context.Context, req *cl.DropActivityBase) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_CheckDropActivity, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) DeleteHero(ctx context.Context, req *gm.DeleteHeroReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_DeleteHero, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetDailyAttendance(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetDailyAttendance, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetWrestleLevel(ctx context.Context, req *gm.UserSetParam) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetWrestleLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) GetVersion(ctx context.Context, req *gm.VersionReq) (*gm.VersionRsp, error) {
	msg := &gm.VersionRsp{Code: uint32(ret.RET_OK)}
	msg.Version = version.GetVersion()
	return msg, nil
}

func (s *Server) SetRiteRare(ctx context.Context, req *gm.UserSetRiteRare) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetRiteRare, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetSeasonLevel(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetSeasonLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) DeleteCurrencies(ctx context.Context, req *gm.DeleteCurrenciesReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_DeleteCurrenciesReq, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetHeroAwakenLevel(ctx context.Context, req *gm.UserSetHeroAwakenLevel) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetHeroAwakenLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetDisorderLand(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetDisorderLand, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) DivineDemonBench(ctx context.Context, req *gm.DivineDemonBenchReq) (*gm.DivineDemonBenchRsp, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_GmDivineDemonBench, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.DivineDemonBenchRsp), nil
	}

	return &gm.DivineDemonBenchRsp{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetSeasonLinkMonuments(ctx context.Context, req *gm.SetSeasonLinkMonumentsReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SetSeasonLinkMonuments, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) UpdatePeopleGroupPackage(ctx context.Context, req *gm.UpdatePeopleGroupPackageReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdatePeopleGroupPackage, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SendPGPMail(ctx context.Context, req *gm.PGPMail) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SendPGPMail, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) KickAccount(ctx context.Context, req *gm.UserIndex) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_KickAccount, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) ChangeUserName(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_ChangeUserName, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetSeasonArenaScore(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_GmSetSeasonArenaScore, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetSeasonLink(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetSeasonLink, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) UpdatePyramidActivity(ctx context.Context, req *cl.PyramidActivityBase) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdatePyramidActivity, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetTaskFinish(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetTaskFinish, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) AddGuildMobScore(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_AddGuildMobScore, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}
	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetTalentTree(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetTalentTree, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetBossRushLevel(ctx context.Context, req *gm.SetBossRushLevelReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetBossRushLevel, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) SetBattleHeroStar(ctx context.Context, req *gm.BattleHeroStar) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_SetBattleHeroStar, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) Hotfix(ctx context.Context, req *gm.HotfixReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_Hotfix, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetRemain(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_Remain, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) UpdateSelectSummon(ctx context.Context, req *gm.SelectSummonReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdateSelectSummon, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) NewQuestionnaireFinish(ctx context.Context, req *gm.QuestionnaireFinishReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_NewQuestionnaireFinish, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetDailyAttendanceHero(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	if getGMOpen() > 0 {
		ret, err := s.process(gm.ID_Fn_GmDailyAttendanceHero, req)
		if err != nil {
			l4g.Errorf("logic grpc process error:%s", err)
			return nil, err
		}
		return ret.(*gm.Result), nil
	}

	return &gm.Result{Code: uint32(ret.RET_GRPC_SERVER_NOT_OPEN)}, nil
}

func (s *Server) DeleteServerMail(ctx context.Context, req *gm.DelServerMail) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_DeleteServerMail, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) UpdateCouponActivity(ctx context.Context, req *cl.ActivityCouponXml) (*gm.Result, error) {
	l4g.Debugf("UpdateCouponActivity %+v", req)
	ret, err := s.process(gm.ID_Fn_UpdateCouponActivity, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) UpdatePokemonSummon(ctx context.Context, req *gm.PokemonSummonReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_UpdatePokemonSummon, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}

func (s *Server) SetTowerPokemonDungeonID(ctx context.Context, req *gm.UserReq) (*gm.Result, error) {
	ret, err := s.process(gm.ID_Fn_SetTowerPokemonDungeonID, req)
	if err != nil {
		l4g.Errorf("logic grpc process error:%s", err)
		return nil, err
	}
	return ret.(*gm.Result), nil
}
