package cross

import (
	"context"

	cs "app/cross/service"
	"app/protos/in/gm"
	appsrv "app/service"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

type Server struct {
	lastRequestTime []int64 //注意这里是被多个gourotuine访问的数据，需要原子操作
}

func NewServer() *Server {
	return &Server{
		lastRequestTime: make([]int64, gm.ID_Fn_MAX),
	}
}

/*
func (s *Server) rateLimitOK(id gm.ID) bool {
	now := time.Now().Unix()
	tm := atomic.LoadInt64(&s.lastRequestTime[id])
	if util.SafeSubInt64(now, tm) < onlineNumRequestPeriod {
		l4g.Errorf("[grpc] send online num too frequently, last request time %d, now %d, id %d",
			tm, now, id)
		return false
	}
	atomic.StoreInt64(&s.lastRequestTime[id], now)
	return true
}

const (
	onlineNumRequestPeriod int64 = 10
)
*/

// 实际的跳转入口函数, 返回的数据是返回给调用放的
func (s *Server) process(id gm.ID, req proto.Message) proto.Message {
	msg := cs.NewGRPCMessage(id, req)
	//等待cross的主goroutine获取执行
	appsrv.CtrlCommand(func(ctx context.Context) {
		//实际执行的函数
		cs.OnGRPCMessage(ctx, msg)
	})
	//等待异步执行的函数返回
	ret := <-msg.Ret
	l4g.Debugf("[grpc] req: %s res: %s", msg.Data, ret)
	return ret
}

// 每个命令的入口函数
func (s *Server) GmGetGuildInfo(ctx context.Context, req *gm.GmGetGuildInfoReq) (*gm.GmGetGuildInfoResp, error) {
	ret := s.process(gm.ID_Fn_GmGetGuildInfo, req)
	return ret.(*gm.GmGetGuildInfoResp), nil
}

func (s *Server) GetRankInfo(ctx context.Context, req *gm.RankInfo) (*gm.GetRankInfoResp, error) {
	ret := s.process(gm.ID_Fn_GetRankInfo, req)
	return ret.(*gm.GetRankInfoResp), nil
}

func (s *Server) GmGetGuildList(ctx context.Context, req *gm.GmGetGuildListReq) (*gm.GmGetGuildListResp, error) {
	ret := s.process(gm.ID_Fn_GmGetGuildList, req)
	return ret.(*gm.GmGetGuildListResp), nil
}

func (s *Server) GmGuildKickMember(ctx context.Context, req *gm.GmGuildKickMemberReq) (*gm.GmGuildKickMemberResp, error) {
	ret := s.process(gm.ID_Fn_GmGuildKickMember, req)
	return ret.(*gm.GmGuildKickMemberResp), nil
}

func (s *Server) GmGuildDisband(ctx context.Context, req *gm.GmGuildDisbandReq) (*gm.GmGuildDisbandResp, error) {
	ret := s.process(gm.ID_Fn_GmGuildDisband, req)
	return ret.(*gm.GmGuildDisbandResp), nil
}

func (s *Server) GmGuildChangeName(ctx context.Context, req *gm.GmGuildChangeNameReq) (*gm.GmGuildChangeNameResp, error) {
	ret := s.process(gm.ID_Fn_GmGuildChangeName, req)
	return ret.(*gm.GmGuildChangeNameResp), nil
}

func (s *Server) GmGuildChangeLeader(ctx context.Context, req *gm.GmGuildChangeLeaderReq) (*gm.GmGuildChangeLeaderResp, error) {
	ret := s.process(gm.ID_Fn_GmGuildChangeLeader, req)
	return ret.(*gm.GmGuildChangeLeaderResp), nil
}

func (s *Server) GmGuildChangeNotice(ctx context.Context, req *gm.GmGuildChangeNoticeReq) (*gm.GmGuildChangeNoticeResp, error) {
	ret := s.process(gm.ID_Fn_GmGuildChangeNotice, req)
	return ret.(*gm.GmGuildChangeNoticeResp), nil
}

func (s *Server) GmGuildDeleteMail(ctx context.Context, req *gm.GmGuildDeleteMailReq) (*gm.GmGuildDeleteMailResp, error) {
	ret := s.process(gm.ID_Fn_GmGuildDeleteMail, req)
	return ret.(*gm.GmGuildDeleteMailResp), nil
}

func (s *Server) GmGSTTeamMove(ctx context.Context, req *gm.GMGSTTeamMoveReq) (*gm.GMGSTTeamMoveRsp, error) {
	ret := s.process(gm.ID_Fn_GmGSTTeamMove, req)
	return ret.(*gm.GMGSTTeamMoveRsp), nil
}
