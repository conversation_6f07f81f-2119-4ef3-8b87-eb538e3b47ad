package crossmaster

import (
	"context"

	cs "app/crossmaster/service"
	"app/protos/in/gm"
	appsrv "app/service"

	"github.com/gogo/protobuf/proto"
	l4g "github.com/ivanabc/log4go"
)

type Server struct {
	lastRequestTime []int64 //注意这里是被多个gourotuine访问的数据，需要原子操作
}

func NewServer() *Server {
	return &Server{
		lastRequestTime: make([]int64, gm.ID_Fn_MAX),
	}
}

/*
func (s *Server) rateLimitOK(id gm.ID) bool {
	now := time.Now().Unix()
	tm := atomic.LoadInt64(&s.lastRequestTime[id])
	if util.SafeSubInt64(now, tm) < onlineNumRequestPeriod {
		l4g.Errorf("[grpc] send online num too frequently, last request time %d, now %d, id %d",
			tm, now, id)
		return false
	}
	atomic.StoreInt64(&s.lastRequestTime[id], now)
	return true
}

const (
	onlineNumRequestPeriod int64 = 10
)
*/

// 实际的跳转入口函数, 返回的数据是返回给调用放的
func (s *Server) process(id gm.ID, req proto.Message) proto.Message {
	msg := cs.NewGRPCMessage(id, req)
	//等待cross的主goroutine获取执行
	appsrv.CtrlCommand(func(ctx context.Context) {
		//实际执行的函数
		cs.OnGRPCMessage(ctx, msg)
	})
	//等待异步执行的函数返回
	ret := <-msg.Ret
	l4g.Debugf("[grpc] req: %s res: %s", msg.Data, ret)
	return ret
}

// 每个命令的入口函数
func (s *Server) GetActPartition(ctx context.Context, req *gm.ActInfo) (*gm.GetActPartitionResp, error) {
	ret := s.process(gm.ID_Fn_GetActPartition, req)
	return ret.(*gm.GetActPartitionResp), nil
}

func (s *Server) SetActPartition(ctx context.Context, req *gm.SetActPartitionReq) (*gm.Result, error) {
	ret := s.process(gm.ID_Fn_SetActPartition, req)
	return ret.(*gm.Result), nil
}

func (s *Server) GetActPartitionRunInfo(ctx context.Context, req *gm.ActInfo) (*gm.GetActPartitionRunInfoResp, error) {
	l4g.Debugf("GetActPartitionRunInfo req:%+v", req)
	ret := s.process(gm.ID_Fn_GetActPartitionRunInfo, req)
	return ret.(*gm.GetActPartitionRunInfoResp), nil
}

func (s *Server) ResetArea(ctx context.Context, req *gm.ResetAreaReq) (*gm.Result, error) {
	ret := s.process(gm.ID_Fn_ResetArea, req)
	return ret.(*gm.Result), nil
}

func (s *Server) StopArea(ctx context.Context, req *gm.StopAreaReq) (*gm.Result, error) {
	l4g.Debugf("StopArea req:%+v", req)
	ret := s.process(gm.ID_Fn_StopArea, req)
	return ret.(*gm.Result), nil
}

func (s *Server) StartArea(ctx context.Context, req *gm.StartAreaReq) (*gm.Result, error) {
	l4g.Debugf("Start req:%+v", req)
	ret := s.process(gm.ID_Fn_StartArea, req)
	return ret.(*gm.Result), nil
}
