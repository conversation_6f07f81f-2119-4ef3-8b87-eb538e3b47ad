package hotfix

import (
	"errors"
	"fmt"
	"reflect"

	qhook "gitlab.qdream.com/kit/sea/gohook"

	"github.com/traefik/yaegi/interp"
)

var (
	convertFuncPatchErr   = errors.New("convert FuncPatch error")
	getFuncsErr           = errors.New("get funcs error")
	evalTextsLenErr       = errors.New("evalTexts len error")
	retrieveMethodNameErr = errors.New("retrieve method by name failed")
)

func ApplyMultiFunc(filePath string, symbols interp.Exports) error {
	// 构建解析器
	interpreter := interp.New(interp.Options{})
	interpreter.Use(symbols)

	_, err := interpreter.EvalPath(filePath)
	if err != nil {
		return err
	}

	//获取函数
	res, err := interpreter.Eval("hotfixfile.GetHotfixFuncs()")
	if err != nil {
		return err
	}

	evalTexts, ok := res.Interface().([]string)
	if !ok {
		return getFuncsErr
	}

	if len(evalTexts) == 0 {
		return evalTextsLenErr
	}

	fmt.Printf("evalTexts :%v \n", evalTexts)

	for _, evalText := range evalTexts {
		// 获取替换函数
		res, err := interpreter.Eval(evalText)
		if err != nil {
			return err
		}

		fp, ok := res.Interface().(*FuncPatch)
		if !ok {
			return convertFuncPatchErr
		}

		err = hookFunc(fp.StructType, fp.FuncName, fp.FuncValue)
		if err != nil {
			return err
		}
	}

	return nil
}

func loadFuncPatch(filePath string, evalText string, symbols interp.Exports) (*FuncPatch, error) {
	// 构建解析器
	interpreter := interp.New(interp.Options{})
	interpreter.Use(symbols)

	_, err := interpreter.EvalPath(filePath)
	if err != nil {
		return nil, err
	}

	// 获取替换函数
	res, err := interpreter.Eval(evalText)
	if err != nil {
		return nil, err
	}

	funcPatch, ok := res.Interface().(*FuncPatch)
	if !ok {
		return nil, convertFuncPatchErr
	}

	return funcPatch, nil
}

func hookFunc(source reflect.Type, methodName string, dest reflect.Value) error {
	m, ok := source.MethodByName(methodName)
	if !ok {
		return retrieveMethodNameErr
	}

	//fmt.Printf("hookFunc: m.Func Type: %v, m.Func Kind: %v\n", m.Func.Type(), m.Func.Kind()) // 增加打印

	return qhook.HookByReflect(m.Func, dest, true)
}
