package balancearena

import "app/protos/in/l2c"

type Manager struct{}

func NewManager() *Manager {
	return &Manager{}
}

// 是否需要分区
func (m *Manager) NeedPartition() bool {
	return true
}

func (m *Manager) NeedResetPartition() bool {
	return false
}

func (m *Manager) GetAreaType() l2c.CROSS_ACT_AREA_ID {
	return l2c.CROSS_ACT_AREA_ID_NORMAL_AREA
}

func (m *Manager) GetNextResetTime(lastResetTime int64) int64 {
	return 0
}

func (m *Manager) GetInitLastResetTime() int64 {
	return 0
}
