package service

import (
	"app/crossmaster/activity"
	"app/crossmaster/activity/balancearena"
	"app/crossmaster/activity/disorderland"
	"app/crossmaster/activity/gst"
	"app/crossmaster/activity/guild"
	"app/crossmaster/activity/hotrank"
	"app/crossmaster/activity/peak"
	"app/crossmaster/activity/rank"
	"app/crossmaster/activity/router"
	"app/crossmaster/activity/seasonarena"
	"app/crossmaster/activity/seasoncompliance"
	"app/crossmaster/activity/seasonmap"
	"app/crossmaster/activity/worldboss"
	"app/crossmaster/activity/wrestle"
	"app/protos/in/l2c"

	l4g "github.com/ivanabc/log4go"
)

// TODO 把几个new方法单独那出来都放在这里
func createNewManager(id uint32) activity.Activityer {
	switch l2c.ACTIVITYID(id) {
	case l2c.ACTIVITYID_ROUTER:
		return router.NewManager()
	case l2c.ACTIVITYID_WRESTLE:
		return wrestle.NewManager()
	case l2c.ACTIVITYID_GUILD:
		return guild.NewManager()
	case l2c.ACTIVITYID_RANK:
		return rank.NewManager()
	case l2c.ACTIVITYID_WORLDBOSS:
		return worldboss.NewManager()
	case l2c.ACTIVITYID_DISORDER_LAND:
		return disorderland.NewManager()
	case l2c.ACTIVITYID_PEAK:
		return peak.NewManager()
	case l2c.ACTIVITYID_GST:
		return gst.NewManager()
	case l2c.ACTIVITYID_SEASON_ARENA:
		return seasonarena.NewManager()
	case l2c.ACTIVITYID_HOT_RANK:
		return hotrank.NewManager()
	case l2c.ACTIVITYID_SEASON_COMPLIANCE:
		return seasoncompliance.NewManager()
	case l2c.ACTIVITYID_SEASON_MAP:
		return seasonmap.NewManager()
	case l2c.ACTIVITYID_BALANCE_ARENA:
		return balancearena.NewManager()
	default:
		l4g.Errorf("activity id not exist, id:%d", id)
	}
	return nil
}
