package version

import (
	"app/goxml"
	"hash/crc32"
	"strings"
	"sync/atomic"

	l4g "github.com/ivanabc/log4go"
)

var mainVersion uint64

var patchCodeVersion atomic.Value

// 计算version不包含Compile.保证每次重新打包version都能一致
func ShowVersion() {
	println("============ service info ============")
	codeVersion := Version
	patchCodeV := GetPatchCodeVersion()
	if patchCodeV != "" {
		println("Patch Code Version:", patchCodeV)
		codeVersion = patchCodeV
	}

	dataVersion := DataVersion
	if goxml.GetData() != nil && goxml.GetData().ServerInfoM.DataVersion != "" {
		println("Patch Data Version:", goxml.GetData().ServerInfoM.DataVersion)
		dataVersion = goxml.GetData().ServerInfoM.DataVersion
	}

	protoVersion := ProtoVersion
	println("Version:", codeVersion)
	println("Data Version:", dataVersion)
	println("Proto Version:", protoVersion)
	println("Compile:", Compile)

	l4g.Infof("Version: %s", codeVersion)
	l4g.Infof("Data Version: %s", dataVersion)
	l4g.Infof("Proto Version: %s", protoVersion)
	l4g.Infof("Compile: %s", Compile)

	atomic.StoreUint64(&mainVersion, uint64(crc32.ChecksumIEEE([]byte(codeVersion+dataVersion+protoVersion))))
	println("Service Version: ", GetVersion())
	l4g.Infof("Service Version: %d", GetVersion())
}

func GetVersion() uint64 {
	return atomic.LoadUint64(&mainVersion)
}

func GetCodeVersion() string {
	return Version
}

func SetPatchCodeVersion(v string) {
	patchCodeVersion.Store(v)
}

func GetPatchCodeVersion() string {
	v := patchCodeVersion.Load()
	if v == nil {
		return ""
	}
	return v.(string)
}

// 每次都是在前一次的基础上patch
func GetCodeGitVersion() string {
	codeV := Version
	patchCodeV := GetPatchCodeVersion()
	if patchCodeV != "" {
		println("Patch Code Version:", patchCodeV)
		codeV = patchCodeV
	}
	version := strings.Split(codeV, "@")
	if len(version) < 2 {
		return ""
	}
	v1 := strings.Trim(version[1], " ")
	if len(v1) <= 8 {
		return ""
	}
	return v1[:8]
}
