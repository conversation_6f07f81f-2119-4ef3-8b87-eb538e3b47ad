syntax = "proto3";

import "db.proto";

option go_package = "app/protos/in/l2w";

// world <-> logic
// world handle <-> world redis
package l2w;

enum RET {
  ERROR = 0;
  OK = 1;
}

enum ID {
  MSG_NONE = 0;
  MSG_MIN = 100000;
  MSG_MAX = 200000;
  MSG_BEGIN = 100001;
  MSG_END = 199999;
  MSG_L2W_Register = 100002;
  MSG_W2L_Register = 100003;
  MSG_L2W_Close = 100004;
  MSG_L2W_Transfer = 100005;  // 跨服转发消息
  MSG_L2W_KeepAlive = 100006;
  MSG_W2L_KeepAlive = 100007;
  MSG_L2WL_GetUser = 100008;
  MSG_WL2L_GetUser = 100009;
  MSG_L2W_UpdateCommonRank = 100100;
  MSG_W2L_UpdateCommonRank = 100101;
  MSG_W2R_GetRank = 100102;
  MSG_R2W_GetRank = 100103;
  MSG_W2R_SaveCommonRank = 100104;
  MSG_L2W_GetRankList = 100105;
  MSG_W2L_GetRankList = 100106;
  MSG_L2W_RankLoad = 100107;
  MSG_W2L_RankLoad = 100108;
  MSG_L2W_SaveRank = 100109;
  MSG_L2W_RankTest = 100110;
  MSG_W2L_RankTest = 100111;
}

message L2W_Register {
  repeated uint64 sids = 1;  // 合服的情况
}

message W2L_Register {
  uint32 ret = 1;
  repeated uint64 sid = 2;
  repeated uint32 mids = 3;  // 该跨服开启的模块
}

message L2W_Close {}

message L2W_Transfer {
  uint32 cmd = 1;
  bytes data = 2;
  uint32 timer_id = 3;
  uint64 from_sid = 4;
  uint64 to_sid = 5;
  uint64 uid = 6;
}

message L2W_KeepAlive {}

message W2L_KeepAlive {}

message L2WL_GetUser {
  uint64 sid = 1;
  uint64 uid = 2;
}

message WL2L_GetUser {
  uint32 ret = 1;
  uint64 sid = 2;
  uint64 uid = 3;
  db.User user = 4;
}

// 加载排行榜数据
message W2R_GetRank {
  repeated uint32 ids = 1;
}

message R2W_GetRank {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  map<uint32, CommonRankData> common_rank = 3;
}

message W2R_SaveCommonRank {
  repeated OpCommonRank datas = 1;
}

message CommonRankData {
  map<uint64, db.CommonRank> data = 1;
}

message OpCommonRank {
  uint32 id = 1;
  repeated db.CommonRank values = 2;
  repeated uint64 deletes = 3;
}

message L2W_UpdateCommonRank {
  uint64 uid = 1;
  uint64 server_id = 2;
  uint32 id = 3;
  bytes data = 4;
}

// 跨服推给logic的更新信息
message W2L_UpdateCommonRank {
  uint32 ret = 1;
  uint32 id = 2;
  bytes data = 3;
}

// 连接后获取排行榜数据
// 获取要load的排行榜id
message L2W_GetRankList {}

message W2L_GetRankList {
  uint32 ret = 1;
  repeated uint32 ids = 2;
}

// 加载具体排行榜
message L2W_RankLoad {
  repeated uint32 ids = 1;
}

message W2L_RankLoad {
  uint32 ret = 1;
  repeated uint32 ids = 2;
  map<uint32, CommonRankData> datas = 3;
  uint32 total = 4;   // 总条数, 判断数据没有丢失
  bool is_begin = 5;  // 开始标记
  bool is_end = 6;    // 结束标记
}

// 保存某个数据
message L2W_SaveRank {
  uint32 id = 1;
  repeated db.CommonRank datas = 2;
}

message L2W_RankTest {
  uint32 msgid = 1;
}

message W2L_RankTest {
  uint32 ret = 1;
  uint32 msgid = 2;
}
