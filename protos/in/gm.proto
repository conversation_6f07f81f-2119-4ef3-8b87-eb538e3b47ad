syntax = "proto3";

option go_package = "app/protos/in/gm";

import "cl.proto";
import "cr.proto";
import "db.proto";
import "r2l.proto";

package gm;

enum ID {
  Fn_Min = 0;
  Fn_GetUser = 1;                         // 玩家信息查询
  Fn_ReduceResources = 2;                 // 玩家道具删除
  Fn_SendUserMail = 3;                    // 玩家邮件
  Fn_SendServerMail = 4;                  // 全服邮件
  Fn_BanAccount = 5;                      // 玩家封禁解禁
  Fn_ToggleUserLogTrace = 6;              // 切换用户日志跟踪
  Fn_OnlineNum = 7;                       // 在线人数
  Fn_SetDungeonID = 8;                    // 修改主线关卡ID
  Fn_SetGuidanceClose = 9;                // 关闭/打开新手引导
  Fn_ChangeBags = 10;                     // 删除道具
  Fn_SendQuestionnaire = 11;              // 发布问卷
  Fn_SetUserLevel = 12;                   // 修改玩家level
  Fn_SendTopResource = 13;                // 发送高级账号资源
  Fn_ClearUserResource = 14;              // 清除玩家资源：钻石，金币和背包中的token所有值
  Fn_ActivityInfo = 15;                   // 活动信息查询
  Fn_ResetDailyNum = 16;                  // 重置各玩法次数回到初始状态
  Fn_SetMedalLevel = 17;                  // 修改功勋level
  Fn_ResetMaze = 18;                      // 重置迷宫
  Fn_ResetGuildQuitTm = 19;               // 重置退出公会时间
  Fn_ResetTowerFloor = 20;                // 重置爬塔floor
  Fn_ResetTrialLevel = 21;                // 重置材料本等级
  Fn_ResetArenaScore = 22;                // 重置竞技场积分
  Fn_ResetMirageFloor = 23;               // 重置个人Boss floor
  Fn_ResetScore = 24;                     // 重置日常、周常、嘉年华积分
  Fn_ResetTaleChapterFinish = 25;         // 设置列传章节完成
  Fn_ResetTaleElite = 26;                 // 设置列传强敌
  Fn_SetUserResource = 27;                // 设置玩家资源
  Fn_NotifyRecharge = 28;                 // 充值订单
  Fn_SetUserVip = 29;                     // 修改玩家vip
  Fn_SetTowerstarDungeonID = 30;          // 设置条件爬塔关卡
  Fn_ResetTowerstar = 31;                 // 重置条件爬塔
  Fn_SetGmConfig = 32;                    // 设置gm后台配置，id=1：评论
  Fn_SendGiftCodeInfo = 33;               // 发布礼包码
  Fn_UpdateMultiLangs = 34;               // 更新多语言
  Fn_QuestionnaireFinish = 35;            // 问卷完成通知
  Fn_ReleaseOperateActivity = 36;         // 发布可配置活动
  Fn_ReleaseOperatePageInfo = 37;         // 发布可配置页签
  Fn_SetForestLevel = 38;                 // 设置密林level
  Fn_BanProtocol = 39;                    // 开启关闭协议
  Fn_SetAllPushGift = 40;                 // 设置全部推送礼包
  Fn_GetRechargeList = 41;                // 玩家充值查询
  Fn_CloseGuidance = 42;                  // 关闭单个引导，线上使用
  Fn_SetMazeTaskLevel = 43;               // 设置迷宫任务等级
  Fn_GetUserMailList = 44;                // 获取玩家邮件列表
  Fn_DeleteUserMail = 45;                 // 删除玩家邮件
  Fn_SetAccountTag = 46;                  // 设置账号标识
  Fn_SetServerTime = 47;                  // 设置服务器时间
  Fn_QueryServerTime = 48;                // 查询服务器时间
  Fn_SetSinglePushGift = 49;              // 设置单个推送礼包
  Fn_ImportUser = 50;                     // 导号
  Fn_SetDispatchLevel = 51;               // 设置派遣等级
  Fn_GetActPartition = 52;                // 查询分组信息
  Fn_SetActPartition = 53;                // 拉取分组信息
  Fn_UpdateDivineDemon = 54;              // 更新神魔抽卡
  Fn_SetGuildDungeonCurrentChapter = 55;  // 修改公会当前章节
  Fn_UpdateAnnouncement = 56;             // 游戏公告
  Fn_SetGuildLevel = 57;                  // 修改公会等级
  Fn_RecoveryGuildDungeonResetTime = 58;  // 恢复公会副本的重置时间
  Fn_SetFlowerLevel = 59;                 // 设置密林level
  Fn_UpdateDailyWishActivity = 60;        // 每日许愿活动
  Fn_UpdateArtifactDebut = 61;            // 更新神器首发活动
  Fn_SetTowerSeasonFloor = 62;            // 设置百塔层数
  Fn_GetActPartitionRunInfo = 63;         // 查询跨服玩法的分区运行情况
  Fn_ResetArea = 64;                      // 重置战区
  Fn_StopArea = 65;                       // 关闭某个战区
  Fn_StartArea = 66;                      // 开启某个战区
  Fn_DelChatGroupTag = 67;                // 清除玩家chatGroupTag
  Fn_UpdateDropActivity = 68;             // 更新掉落活动
  Fn_GmGetGuildInfo = 69;                 // 查看公会信息
  Fn_GetRankInfo = 70;                    // 查看排行榜信息
  Fn_DeleteHero = 71;                     // 删除玩家英雄
  Fn_SetDailyAttendance = 72;             // 设置累登天数
  Fn_SetWrestleLevel = 73;                // 设置神树争霸赛场等级
  Fn_GetVersion = 74;                     // 查询版本号
  Fn_SetRiteRare = 75;                    // 设置仪式品质
  Fn_SetSeasonLevel = 76;                 // 设置赛季等级
  Fn_NotifyRefund = 77;                   // 订单退款
  Fn_DeleteCurrenciesReq = 78;            // 删除玩家货币（钻石、金币、代金券）
  Fn_SetHeroAwakenLevel = 79;             // 设置英雄觉醒等级
  Fn_GmGetGuildList = 80;                 // 获取战区内的公会
  Fn_GmGuildKickMember = 81;              // 将成员提出公会
  Fn_GmGuildDisband = 82;                 // 解散公会
  Fn_SetDisorderLand = 83;                // 失序空间 - 完成某个地图
  Fn_GmGuildChangeName = 84;              // 修改公会名称
  Fn_GmGuildChangeLeader = 85;            // 修改会长
  Fn_GmGuildChangeNotice = 86;            // 修改公告
  Fn_GmGuildDeleteMail = 87;              // 删除公会邮件
  Fn_GmDivineDemonBench = 88;             // 神魔抽卡跑测
  Fn_SetSeasonLinkMonuments = 89;         // 设置赛季羁绊丰碑数据
  Fn_GmGSTTeamMove = 90;                  // 公会战一键派遣到空地块
  Fn_UpdatePeopleGroupPackage = 91;       // 同步人群包
  Fn_SendPGPMail = 92;                    // 发送人群包邮件
  Fn_KickAccount = 93;                    // 踢下线
  Fn_ChangeUserName = 94;                 // 玩家改名
  Fn_GmSetSeasonArenaScore = 95;          // 设置赛季竞技场积分
  Fn_SetSeasonLink = 96;                  // 激活赛季羁绊
  Fn_UpdatePyramidActivity = 97;          // 更新金字塔活动
  Fn_GuildNoticeManage = 98;
  Fn_CheckDropActivity = 99;          // 检测掉落活动
  Fn_SetTaskFinish = 100;             // 设置任务完成
  Fn_AddGuildMobScore = 101;          // 加公会竞赛积分
  Fn_SetTalentTree = 102;             // 天赋树 - 一键满级
  Fn_SetBossRushLevel = 103;          // 修改Boss挑战等级
  Fn_SetBattleHeroStar = 104;         // 设置战斗英雄星级
  Fn_Hotfix = 105;                    // 修复代码
  Fn_UpdateSelectSummon = 106;        // 更新选择抽卡
  Fn_Remain = 107;                    // 一键遗物
  Fn_NewQuestionnaireFinish = 108;    // 新问卷完成通知
  Fn_GmDailyAttendanceHero = 109;     // 每日登录送英雄天数
  Fn_DeleteServerMail = 110;          // 删除全服邮件
  Fn_UpdateCouponActivity = 111;      // 更新代金券活动
  Fn_UpdatePokemonSummon = 112;       // 宠物抽卡
  Fn_SetTowerPokemonDungeonID = 113;  // 修改玩家level
  Fn_OfflineVipAward = 114;           // 线下充值累计发奖 越南专用
  Fn_MAX = 115;                       // 最大的协议编号
}

enum SEARCH {
  BY_NONE = 0;
  BY_NAME = 1;
  BY_ID = 2;
  BY_UUID = 3;
}

enum SET_DUNGEON_TYPE {
  SDT_NONE = 0;
  SDT_TOWER_SEASON = 1;
  SDT_ACTIVITY_STORY = 2;
  SDT_SEASON_DUNGEON = 3;
}

service Logic {
  rpc GetUser(UserIndex) returns (RetUser) {}
  rpc ReduceResources(Object) returns (Result) {}
  rpc SendUserMail(UsersMail) returns (Result) {}
  rpc SendServerMail(ServerMail) returns (Result) {}
  rpc DeleteServerMail(DelServerMail) returns (Result) {}
  rpc BanAccount(BanAccountReq) returns (Result) {}
  rpc ToggleUserLogTrace(LogTraceOp) returns (Result) {}
  rpc OnlineNum(Cmd) returns (RetOnlineNum) {}
  rpc ChangeBags(BagsOp) returns (Result) {}
  rpc SendQuestionnaire(cl.Questionnaire) returns (Result) {}
  rpc GetActivityInfo(UserIndex) returns (ActivityInfoRsp) {}
  rpc NotifyRecharge(db.Order) returns (Result) {}
  rpc SetGmConfig(GmConfigReq) returns (Result) {}
  rpc SendGiftCodeInfo(GiftCodeInfo) returns (Result) {}
  rpc UpdateMultiLangs(MultiLangs) returns (Result) {}
  rpc QuestionnaireFinish(QuestionnaireFinishReq) returns (Result) {}
  rpc ReleaseOperateActivity(OperateActivity) returns (Result) {}
  rpc ReleaseOperatePageInfo(OperatePageInfos) returns (Result) {}
  rpc BanProtocol(BanCmd) returns (Result) {}
  rpc GetRechargeList(RechargeListReq) returns (RechargeListRsp) {}
  rpc CloseGuidance(UserReq) returns (Result) {}                                 // 关闭引导
  rpc GetUserMailList(UserMailListReq) returns (UserMailListRsp) {}              // 获取玩家邮件列表
  rpc DeleteUserMail(DeleteUserMailReq) returns (Result) {}                      // 删除玩家指定邮件
  rpc SetAccountTag(AccountTagReq) returns (AccountTagRsp) {}                    // 设置账号标识
  rpc UpdateDivineDemon(DivineDemonReq) returns (Result) {}                      // 更新神魔抽卡
  rpc UpdateAnnouncement(AnnouncementReq) returns (Result) {}                    // 更新游戏公告
  rpc UpdateDailyWishActivity(DailyWishInfo) returns (Result) {}                 // 更新每日许愿
  rpc UpdateArtifactDebut(ArtifactDebutReq) returns (Result) {}                  // 更新神器首发活动
  rpc DelChatGroupTag(UserReq) returns (Result) {}                               // 清除玩家chatGroupTag
  rpc UpdateDropActivity(cl.DropActivityBase) returns (Result) {}                // 更新掉落活动
  rpc DeleteHero(DeleteHeroReq) returns (Result) {}                              // 删除玩家英雄
  rpc GetVersion(VersionReq) returns (VersionRsp) {}                             // 获取版本号
  rpc NotifyRefund(db.Order) returns (Result) {}                                 // 通知退款
  rpc DeleteCurrencies(DeleteCurrenciesReq) returns (Result) {}                  // 删除玩家货币（钻石、金币、代金券）
  rpc UpdatePeopleGroupPackage(UpdatePeopleGroupPackageReq) returns (Result) {}  // 同步人群包
  rpc SendPGPMail(PGPMail) returns (Result) {}                                   // 发送人群包邮件
  rpc KickAccount(UserIndex) returns (Result) {}                                 // 踢下线
  rpc ChangeUserName(UserReq) returns (Result) {}                                // 改名字
  rpc UpdatePyramidActivity(cl.PyramidActivityBase) returns (Result) {}          // 更新金字塔活动
  rpc CheckDropActivity(cl.DropActivityBase) returns (Result) {}                 // 更新前检查掉落活动
  rpc Hotfix(HotfixReq) returns (Result) {}                                      // 热更新
  rpc UpdateSelectSummon(SelectSummonReq) returns (Result) {}                    // 更新选择抽卡
  rpc NewQuestionnaireFinish(QuestionnaireFinishReq) returns (Result) {}
  rpc UpdateCouponActivity(cl.ActivityCouponXml) returns (Result) {}  // 更新代金券活动
  rpc UpdatePokemonSummon(PokemonSummonReq) returns (Result) {}       // 更新宠物抽卡
  rpc OfflineVipAward(OfflineVipAwardReq) returns (Result) {}         // 线下充值累计奖励 越南专用
}

// gm tool：专门给测试人员在测试环境使用
service GmTest {
  rpc SetDungeonID(UserReq) returns (Result) {}                               // 设置关卡
  rpc SetGuidanceClose(UserReq) returns (Result) {}                           // 关闭引导
  rpc SetUserLevel(UserReq) returns (Result) {}                               // 修改玩家level
  rpc ClearUserResource(UserReq) returns (Result) {}                          // 清除玩家资源
  rpc SendTopResource(UserReq) returns (Result) {}                            // 发送高级账号资源
  rpc ResetDailyNum(UserReq) returns (Result) {}                              // 重置各玩法次数回到初始状态(number_type_info.xml中配置的玩法)
  rpc SetMedalLevel(UserReq) returns (Result) {}                              // 修改功勋level
  rpc ResetMaze(UserReq) returns (Result) {}                                  // 重置迷宫
  rpc ResetGuildQuitTm(UserReq) returns (Result) {}                           // 重置退出公会时间
  rpc ResetTowerFloor(TowerReq) returns (Result) {}                           // 重置爬塔floor
  rpc ResetTrialLevel(TrialReq) returns (Result) {}                           // 重置材料本等级
  rpc ResetArenaScore(ArenaReq) returns (Result) {}                           // 重置竞技场积分
  rpc ResetMirageFloor(MirageReq) returns (Result) {}                         // 重置个人Boss floor
  rpc ResetScore(UserReq) returns (Result) {}                                 // 重置日常、周常、嘉年华积分
  rpc ResetTaleChapterFinish(UserReq) returns (Result) {}                     // 设置列传章节完成
  rpc ResetTaleElite(UserReq) returns (Result) {}                             // 设置列传强敌
  rpc SetUserResource(UserResource) returns (Result) {}                       // 设置玩家资源
  rpc SetUserVip(UserReq) returns (Result) {}                                 // 设置玩家vip
  rpc SetTowerstarDungeonID(UserReq) returns (Result) {}                      // 设置条件爬塔关卡
  rpc ResetTowerstar(UserReq) returns (Result) {}                             // 重置条件爬塔
  rpc SetForestLevel(UserReq) returns (Result) {}                             // 设置密林等级
  rpc SetAllPushGift(UserReq) returns (Result) {}                             // 设置推送礼包
  rpc SetMazeTaskLevel(UserReq) returns (Result) {}                           // 设置迷宫任务等级
  rpc SetServerTime(ServerTimeReq) returns (Result) {}                        // 设置服务器时间
  rpc QueryServerTime(Cmd) returns (ServerTimeRsp) {}                         // 查询服务器时间
  rpc SetSinglePushGift(SinglePushGiftReq) returns (Result) {}                // 设置单个推送礼包
  rpc ImportUser(ImportUserReq) returns (ImportUserRsp) {}                    // 导号
  rpc SetDispatchLevel(UserReq) returns (Result) {}                           // 设置派遣等级
  rpc SetGuildDungeonCurrentChapter(UserReq) returns (Result) {}              // 修改公会当前章节
  rpc SetGuildLevel(UserReq) returns (Result) {}                              // 修改公会等级
  rpc RecoveryGuildDungeonResetTime(UserReq) returns (Result) {}              // 恢复公会副本重置时间
  rpc SetFlowerLevel(UserReq) returns (Result) {}                             // 设置密林等级 0.9.8
  rpc SetTowerSeasonFloor(UserReq) returns (Result) {}                        // 设置百塔等级
  rpc SetDailyAttendance(UserReq) returns (Result) {}                         // 设置累登登录天数
  rpc SetWrestleLevel(UserSetParam) returns (Result) {}                       // 设置神树争霸赛场等级
  rpc SetRiteRare(UserSetRiteRare) returns (Result) {}                        // 设置仪式品质
  rpc SetSeasonLevel(UserReq) returns (Result) {}                             // 设置赛季等级
  rpc SetHeroAwakenLevel(UserSetHeroAwakenLevel) returns (Result) {}          // 设置英雄觉醒等级
  rpc SetDisorderLand(UserReq) returns (Result) {}                            // 失序空间 - 完成某个地图
  rpc DivineDemonBench(DivineDemonBenchReq) returns (DivineDemonBenchRsp) {}  // 神魔抽卡跑测
  rpc SetSeasonLinkMonuments(SetSeasonLinkMonumentsReq) returns (Result) {}   // 设置赛季羁绊丰碑数据
  rpc SetSeasonArenaScore(UserReq) returns (Result) {}                        // 设置赛季竞技场积分
  rpc SetSeasonLink(UserReq) returns (Result) {}                              // 激活赛季羁绊
  rpc SetTaskFinish(UserReq) returns (Result) {}                              // 设置对应Module下任务完成
  rpc AddGuildMobScore(UserReq) returns (Result) {}                           // 加公会竞赛积分
  rpc SetTalentTree(UserReq) returns (Result) {}                              // 天赋树 - 一键满级
  rpc SetBossRushLevel(SetBossRushLevelReq) returns (Result) {}               // 设置Boss挑战等级
  rpc SetBattleHeroStar(BattleHeroStar) returns (Result) {}                   // 设置战斗英雄星级
  rpc SetRemain(UserReq) returns (Result) {}                                  // 一键遗物
  rpc SetDailyAttendanceHero(UserReq) returns (Result) {}                     // 每日登录送英雄设置天数
  rpc SetTowerPokemonDungeonID(UserReq) returns (Result) {}                   // 修改宠物爬塔
}

service CrossMaster {
  rpc GetActPartition(ActInfo) returns (GetActPartitionResp) {}
  rpc SetActPartition(SetActPartitionReq) returns (Result) {}
  rpc GetActPartitionRunInfo(ActInfo) returns (GetActPartitionRunInfoResp) {}
  rpc ResetArea(ResetAreaReq) returns (Result) {}
  rpc StopArea(StopAreaReq) returns (Result) {}
  rpc StartArea(StartAreaReq) returns (Result) {}
  // rpc TransGmActReq(GmActReq) returns(TransGmActResp) {}
}

service Cross {
  rpc GetRankInfo(RankInfo) returns (GetRankInfoResp) {}
  rpc GmGetGuildList(GmGetGuildListReq) returns (GmGetGuildListResp) {}
  rpc GmGetGuildInfo(GmGetGuildInfoReq) returns (GmGetGuildInfoResp) {}
  rpc GmGuildKickMember(GmGuildKickMemberReq) returns (GmGuildKickMemberResp) {}
  rpc GmGuildDisband(GmGuildDisbandReq) returns (GmGuildDisbandResp) {}
  rpc GmGuildChangeName(GmGuildChangeNameReq) returns (GmGuildChangeNameResp) {}
  rpc GmGuildChangeLeader(GmGuildChangeLeaderReq) returns (GmGuildChangeLeaderResp) {}
  rpc GmGuildChangeNotice(GmGuildChangeNoticeReq) returns (GmGuildChangeNoticeResp) {}
  rpc GmGuildDeleteMail(GmGuildDeleteMailReq) returns (GmGuildDeleteMailResp) {}
  rpc GmGSTTeamMove(GMGSTTeamMoveReq) returns (GMGSTTeamMoveRsp) {}
}

message Cmd {}

message Result {
  uint32 code = 1;
}

message UserIndex {
  uint64 server_id = 1;        // 必须填写
  uint32 type = 2;             // 对应SEARCH_BY
  repeated string values = 3;  // 玩家账号或昵称
  repeated uint64 ids = 4;     // 玩家id
}

message RetUser {
  uint32 code = 1;
  r2l.R2L_Login data = 2;
}

message Object {
  UserIndex user = 1;
  repeated string txt = 2;        // 邮件内容
  repeated cl.Resource ress = 3;  // 生成的资源
  bool all = 4;                   // 全服邮件
}

// 群邮件
message UsersMail {
  UserIndex user = 1;
  string txt = 2;                 // 邮件内容 *必填*
  repeated cl.Resource ress = 3;  // 生成的资源
  // bool all = 4;                   //全服邮件
  int32 sender_icon = 5;                            // 发送方 icon
  string sender_name = 6;                           // 发送方 名称
  string title = 7;                                 // 邮件标题 *必填*
  int64 expired_time = 8;                           // 在什么时间过期，时间戳。如果不填写，则使用mail_info.xml默认过期规则7天
  uint32 type = 9;                                  // 0：普通邮件  1：大额充值邮件
  WebLargeRechargeMailParams recharge_params = 10;  // 大额充值邮件参数
}

// 大额充值邮件参数
message WebLargeRechargeMailParams {
  uint32 amount = 1;    // 商品金额
  string order_id = 2;  // 订单号
  uint32 op_id = 3;     // 支付方式所属的运营商ID
  int64 pay_time = 4;   // 订单支付时间, 时间戳，单位秒
  bool is_sandbox = 5;  // 是否沙盒
}

message BanAccountReq {
  UserIndex user = 1;
  db.GMBan ban = 2;
}

message ServerMail {
  repeated string content = 1;      // 邮件内容
  repeated cl.Resource awards = 2;  // 邮件附件
  repeated cl.MailCond and = 3;     // 条件1(且)
  repeated cl.MailCond or = 4;      // 条件2(或)
  repeated uint32 ops = 5;          // 运营商
  repeated uint32 channels = 6;     // 二级运营商
  int64 expired_time = 7;           // 过期时间
  uint64 gm_id = 8;                 // gm的邮件ID
}

message DelServerMail {
  uint64 gm_id = 1;
}

message PGPMail {
  repeated string content = 1;      // 邮件内容
  repeated cl.Resource awards = 2;  // 邮件附件
  int64 expired_time = 3;           // 过期时间
  uint32 pgp_id = 4;                // 人群包ID
}

message UserResReq {
  string uuid = 1;
  uint64 server_id = 2;
  repeated cl.Resource ress = 3;  // 要发放的资源
}

message UserReq {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 value = 3;  // 要设置的值
  uint32 type = 4;
}

message LogTraceOp {
  UserIndex user = 1;
  bool trace = 2;
}

message RetOnlineNum {
  uint32 code = 1;
  uint32 effective_num = 2;  // 有效人数
  uint32 register_num = 3;   // 注册人数
  uint32 online_num = 4;     // 在线人数
}

message BagsOp {
  UserIndex user = 1;
  db.Bags bag = 2;  // 删除的背包道具
}

message GiftCodeInfo {
  uint32 id = 1;
  repeated cl.Resource awards = 2;
}

message ActivityInfoRsp {
  uint32 code = 1;
  r2l.GmActivityInfo data = 2;
}

message TowerReq {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 tower_type = 3;  // 塔的类型
  uint32 value = 4;       // 要设置的值
}

message TrialReq {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 type = 3;   // 材料本类型
  uint32 level = 4;  // 要设置的值
}

message ArenaReq {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 score = 3;  // 积分
}

message MirageReq {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 hurdle_id = 3;  // 层数
}

message UserResource {
  string uuid = 1;
  uint64 server_id = 2;
  repeated cl.Resource res = 3;  // 修改的资源
}

message GmConfigReq {
  uint32 id = 1;      // 功能id
  uint32 status = 2;  // 评分引导状态； 1：开启  0：关闭
}

message MultiLangs {
  repeated db.MultiLang langs = 1;
}

message MailMultiLangs {
  map<string, string> langs = 1;
}

message QuestionnaireFinishReq {
  UserIndex user = 1;
  uint32 questionnaire_id = 2;
}

message OperateActivity {
  OperateActivityInfos base = 1;           // 活动信息
  OperateGiftInfos gift = 2;               // 直冲礼包
  OperateTaskInfos task = 3;               // 任务活动
  PromotionGiftInfos promotion_gifts = 4;  // 新推送礼包
}

message OperateActivityInfos {
  repeated cl.OperateActivityInfo changes = 1;  // 更新
  repeated uint32 deletes = 2;                  // 删除
}

message OperatePageInfos {
  repeated cl.OperatePageInfo changes = 1;  // 页签更新
  repeated uint32 deletes = 2;              // 页签删除
}

message OperateGiftInfos {
  repeated cl.OperateGiftInfo changes = 1;  // 礼包更新
  repeated uint32 deletes = 2;              // 礼包删除
}

message OperateTaskInfos {
  repeated cl.OperateTaskInfo changes = 1;  // 任务更新
  repeated uint32 deletes = 2;              // 任务删除
}

message PromotionGiftInfos {
  repeated cl.PromotionGiftInfo changes = 1;  // 礼包更新
  repeated uint32 deletes = 2;                // 礼包删除
}

message BanCmd {
  uint64 server_id = 1;          // 服务器id
  repeated uint32 add_cmds = 2;  // 增加禁用协议
  repeated uint32 del_cmds = 3;  // 删除禁用协议
}

message RechargeListReq {
  string uuid = 1;
  uint64 server_id = 2;
  repeated int64 time_interval = 3;  // 时间区间；第一个值：开始时间，第二个值：结束时间
}

message RechargeListRsp {
  uint32 code = 1;
  repeated db.Order orders = 2;  // 订单
}

message UserMailListReq {
  UserIndex user = 1;
}

message UserMailListRsp {
  Result result = 1;
  map<uint64, cl.Mail> mails = 2;
}

message DeleteUserMailReq {
  UserIndex user = 1;
  repeated uint64 mail_ids = 2;
}

message AccountTagReq {
  uint64 uid = 1;
  uint64 server_id = 2;
  uint32 value = 3;       // 要设置的值
  uint32 mute_value = 4;  // 禁言权限
}

message AccountTagRsp {
  uint32 code = 1;
  db.User user = 2;
}

message ServerTimeReq {
  uint64 server_time = 1;
}

message ServerTimeRsp {
  uint32 code = 1;
  uint64 server_time = 2;
  uint64 cross_time = 3;
}

message SinglePushGiftReq {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 giftId = 3;
  uint32 groupId = 4;
}

message ImportUserReq {
  r2l.R2L_Login data = 1;
  uint64 server_id = 2;
}

message ImportUserRsp {
  uint32 code = 1;
}

message ActInfo {
  uint32 id = 1;  // 活动id
}

message ActPartitionRunInfo {
  uint32 id = 1;    // 分区id
  uint32 node = 2;  // 当前所在节点id
  uint32 session = 3;
  uint64 reset_time = 4;                 // 当前重置时间
  uint64 last_send_create_msg_time = 5;  // 上次发送创角的信息
  uint32 create_state = 6;               // 创建状态
  uint64 check_reset_time = 7;           // 检查分区是否可以重置的锚点。
  bool node_can_reset = 8;               // 节点是否可以重置
  uint64 last_check_time = 9;            // 上次发送检查重置的时间
}

message GetActPartitionResp {
  uint32 code = 1;
  map<uint64, cr.ActPartition> parts = 2;
}

message SetActPartitionReq {
  uint32 id = 1;
  map<uint64, cr.ActPartition> parts = 2;
}

message GetActPartitionRunInfoResp {
  uint32 code = 1;
  map<uint32, ActPartitionRunInfo> parts = 2;
}

// TODO战区重置的逻辑.要指定哪两个战区合并到一起
message ResetAreaReq {
  repeated uint32 old_area = 1;  // 要合并的战区id
  uint32 new_area = 2;           // 新的战区id
  uint32 act_id = 3;             // 活动Id
}

// TODO关闭指定战区。和开启指定战区
message StopAreaReq {
  repeated uint32 area = 1;  // 要关闭的战区
  uint32 act_id = 2;         // 活动Id
}

message StartAreaReq {
  repeated uint32 area = 1;  // 要开启的战区
  uint32 act_id = 2;         // 活动Id
}

message DivineDemonReq {
  uint64 id = 1;         // gmID，唯一id
  uint32 sys_id = 2;     // 活动id
  int64 start_time = 3;  // 开始时间
  int64 end_time = 4;    // 结束时间
  uint32 op_status = 5;  // 1: 上线 2: 下线
}

message SelectSummonReq {
  uint64 id = 1;               // gmID，唯一id
  uint32 sys_id = 2;           // 活动id
  int64 start_time = 3;        // 开始时间
  int64 end_time = 4;          // 结束时间
  uint32 op_status = 5;        // 1: 上线 2: 下线
  uint32 server_open_day = 6;  // 开服天数限制
}

message PokemonSummonReq {
  uint64 id = 1;               // gmID，唯一id
  uint32 sys_id = 2;           // 活动id
  int64 start_time = 3;        // 开始时间
  int64 end_time = 4;          // 结束时间
  uint32 op_status = 5;        // 1: 上线 2: 下线
  uint32 server_open_day = 6;  // 开服天数限制
}

message AnnouncementReq {
  uint32 id = 1;                     // 公告id
  string title = 2;                  // 公告标题
  uint32 title_align = 3;            // 标题位置：0-靠左对齐，1-居中，2-靠右对齐
  string content = 4;                // 公告内容
  uint32 weight = 5;                 // 权重
  int64 start_time = 6;              // 开始时间
  int64 end_time = 7;                // 结束时间
  uint32 type = 8;                   // 公告类型，1-公告，2-活动，3-须知
  uint32 op_type = 9;                // 操作类型：1-更新公告 2-删除公告
  string default_lang = 10;          // 默认语言
  repeated int64 params = 11;        // 参数
  repeated uint32 op_ids = 12;       // 筛选的运营商
  repeated uint32 channel_ids = 13;  // 筛选的二级运营商 对于37就是gid
}

message DailyWishInfo {
  cl.DailyWishActivityInfo changes = 1;  // 更新
  uint32 deletes = 2;                    // 删除
}

message ArtifactDebutReq {
  uint32 id = 1;           // 活动唯一id，有gm生成（首个id是101）
  uint32 activity_id = 2;  // artifact_debut_activity_info量表id
  int64 start_time = 3;    // 开始时间
  int64 end_time = 4;      // 结束时间
  bool is_off = 5;         // 是否下线
}

// 神器首发 - 活动唯一id最小值
enum ARTIFACT_DEBUT {
  AD_NONE = 0;
  AD_MIN_ID = 101;
}

message RankInfo {
  uint32 act_id = 1;
  uint32 area = 2;
  uint32 rank_id = 3;
  uint32 begin_rank = 4;
  uint32 end_rank = 5;
}

message GetRankInfoResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint32 rank_id = 4;
  uint32 begin_rank = 5;
  uint32 end_rank = 6;
  repeated cl.RankValue datas = 7;
  uint32 total = 8;
}

message DeleteHeroReq {
  UserIndex user = 1;
  uint64 hid = 2;
}

message UserSetParam {
  UserIndex user = 1;
  uint32 param = 2;
}

message VersionReq {
  uint32 t = 1;  // 0 logic
}

message VersionRsp {
  uint32 code = 1;
  uint64 version = 2;
}

message UserSetRiteRare {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 rite_id = 3;
  uint32 rare = 4;
}

message DeleteCurrenciesReq {
  UserIndex user = 1;
  repeated cl.Resource del_currencies = 2;
}

message UserSetHeroAwakenLevel {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 hero_sys_id = 3;
  uint32 awaken_level = 4;
}

message GmGetGuildListReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
  string guild_name = 4;
  uint64 leader_id = 5;
  string language = 6;
  int64 start_tm = 7;
  int64 end_tm = 8;
  uint32 page = 9;
}

message GmGuildBaseInfo {
  uint32 area = 1;
  string name = 2;
  uint64 id = 3;
  uint64 leader = 4;
  string leader_name = 5;
  uint32 member_num = 6;
  string language = 7;
  uint32 activity_point = 8;
  uint32 level = 9;
  int64 create_tm = 10;
}

message GmGuildInfo {
  GmGuildBaseInfo base = 1;
  string notice = 2;
  uint32 lv_limit = 3;    // 等级限制
  int64 power_limit = 4;  // 战力限制
  uint32 join_type = 5;   //  0:自动加入 1：需要审核 2：拒绝加入
  repeated GmGuildMemberInfo members = 6;
}

message GmGuildMemberInfo {
  uint64 uid = 1;
  uint64 sid = 2;
  string name = 3;
  uint32 grade = 4;
  uint32 activity_point = 5;
}

message GmGetGuildListResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint32 total = 4;
  uint64 gid = 5;
  string guild_name = 6;
  uint64 leader_id = 7;
  string language = 8;
  int64 start_tm = 9;
  int64 end_tm = 10;
  repeated GmGuildBaseInfo list = 11;
}

message GmGetGuildInfoReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
}

message GmGetGuildInfoResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint64 gid = 4;
  GmGuildInfo info = 5;
}

message GmGuildKickMemberReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
  uint64 member_id = 4;
}

message GmGuildKickMemberResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint64 gid = 4;
  uint64 member_id = 5;
  GmGuildInfo info = 6;
}

message GmGuildDisbandReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
}

message GmGuildDisbandResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint64 gid = 4;
}

message GmGuildChangeNameReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
  string new_name = 4;
}

message GmGuildChangeNameResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint64 gid = 4;
  string new_name = 5;
}

message GmGuildChangeLeaderReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
  uint64 new_leader = 4;
}

message GmGuildChangeLeaderResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint64 gid = 4;
  uint64 new_leader = 5;
}

message GmGuildChangeNoticeReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
  string new_notice = 4;
}

message GmGuildChangeNoticeResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint64 gid = 4;
  string new_notice = 5;
}

message GmGuildDeleteMailReq {
  uint32 act_id = 1;
  uint32 area = 2;
  uint64 gid = 3;
}

message GmGuildDeleteMailResp {
  uint32 code = 1;
  uint32 act_id = 2;
  uint32 area = 3;
  uint64 gid = 4;
}

message DivineDemonBenchReq {
  uint32 activity_id = 1;          // 活动 id
  uint32 activity_type = 2;        // 活动类型：1：新服 2：通服
  uint32 wish_hero_id = 3;         // 心愿英雄 id
  repeated uint32 new_heroes = 4;  // 新英雄池
  repeated uint32 old_heroes = 5;  // 老英雄池
  uint32 loop_num = 6;             // 循环次数
}

message DivineDemonBenchRsp {
  uint32 code = 1;
  repeated string rets = 2;
}

message SetSeasonLinkMonumentsReq {
  string uuid = 1;
  uint64 server_id = 2;
  repeated SeasonLinkRune runes = 3;
}

message SeasonLinkRune {
  uint32 monument_id = 1;   // 丰碑id
  uint32 rune_quality = 2;  // 符石品质
}

message SetBossRushLevelReq {
  string uuid = 1;
  uint64 server_id = 2;
  repeated BossRush data = 3;
}

message BossRush {
  uint32 boss_group = 1;  // boss_group
  uint32 boss_id = 2;     // boss_id
}

message GMGSTTeamMoveReq {
  uint64 uid = 1;
  uint32 area = 2;
  bool rand = 3;  // 是否派遣随机地块
}

message GMGSTTeamMoveRsp {
  uint32 code = 1;
}

message UpdatePeopleGroupPackageReq {
  uint32 package_id = 1;
  repeated uint64 users = 2;
  bool offline = 3;
}

message BattleHeroStar {
  string uuid = 1;
  uint64 server_id = 2;
  uint32 hero = 3;
  uint32 star = 4;
}

message HotfixReq {
  uint32 id = 1;
  string title = 2;
  string code = 3;  // 代码
  uint32 type = 4;  // 修复类型,0功能代码,1修复玩家数据
}

message OfflineVipAwardReq {
  UserIndex user = 1;
  string id = 2;
}
