

syntax = "proto3";

option go_package = "app/protos/in/log";

import "cl.proto";
import "bt.proto";
package log;

// 日志类型

// 行为日志id
enum SUB_TYPE {
  ID_NONE = 0;
  ID_LOGIN_INIT = 1;     // 开始登陆（不包含USERID）
  ID_LOGIN_SUCCESS = 2;  // 登陆成功
  ID_TRACE = 11;         // 协议跟踪
  ID_MONITOR = 12;       // 协议监控

  // 装备 每个系统在前一个系统基础上加100
  // 所有资源消耗的类型也用行为日志的类型。
  ID_EQUIP_STRENGTH = 1000;            // 装备强化
  ID_EQUIP_ENCHANT = 1001;             // 装备附魔
  ID_EQUIP_DECOMPOSE = 1002;           // 装备分解
  ID_EQUIP_REVIVE = 1003;              // 装备重生
  ID_EQUIP_REFINE = 1004;              // 装备精炼
  ID_EQUIP_EVOLUTION = 1005;           // 装备进阶
  ID_EQUIP_GROW_TRANSFER = 1006;       // 装备养成转移
  ID_EQUIP_WEAR = 1007;                // 装备穿戴
  ID_EQUIP_SET_AUTO_DECOMPOSE = 1008;  // 装备自动分解设置

  // 纹章
  ID_EMBLEM_LEVELUP = 1101;
  ID_EMBLEM_STAGEUP = 1102;
  ID_EMBLEM_BLESSING = 1103;
  ID_EMBLEM_DECOMPOSE = 1104;
  ID_EMBLEM_REVIVE = 1105;
  ID_EMBLEM_COMPOSE = 1106;
  ID_EMBLEM_GROW_TRANSFER = 1107;           // 纹章养成转移
  ID_EMBLEM_EXCLUSIVE = 1108;               // 获得专属纹章
  ID_EMBLEM_SET_AUTO_DECOMPOSE = 1109;      // 纹章自动分解
  ID_EMBLEM_BUY_SLOT = 1110;                // 购买背包栏位
  ID_EMBLEM_UPGRADE = 1111;                 // 升阶
  ID_EMBLEM_SUCCINCT = 1112;                // 洗炼
  ID_EMBLEM_SUCCINCT_LOCK_OR_SAVE = 1113;   // 洗炼锁定或保存
  ID_EMBLEM_SUCCINCT_ITEM_CONFLATE = 1114;  // 合成洗炼道具

  // 英雄
  ID_HERO_LEVEL_UP = 1201;
  ID_HERO_STAGE_UP = 1202;
  ID_HERO_STAR_UP = 1203;
  ID_HERO_BUY_SLOT = 1204;
  ID_HERO_UPDATE_LOCK_STATUS = 1205;
  ID_HERO_DECOMPOSE = 1206;
  ID_HERO_BACK = 1207;
  ID_HERO_REVIVE = 1208;
  ID_HERO_CHANGE_RANDOM = 1209;
  ID_HERO_CHANGE_SAVE = 1210;
  ID_HERO_CONVERSION = 1211;
  ID_HERO_GEM_LEVEL_UP = 1212;
  ID_HERO_EXCHANGE = 1213;
  ID_HERO_AWAKEN = 1214;
  ID_HERO_CONVERT = 1215;
  ID_HERO_EMBLEM_SKILL = 1216;  // 英雄的符文技能
  ID_HERO_TAG_UPDATE = 1217;

  // 英雄列传
  ID_TALES_CHAPTER_FIGHT = 1301;
  ID_TALES_CHAPTER_FINISH = 1302;
  ID_TALES_CHAPTER_TAKE_REWARD = 1303;
  ID_TALES_ELITE_FIGHT = 1304;
  ID_TALES_ELITE_WIPE = 1305;

  // 宝石
  ID_GEM_DECOMPOSE = 1401;  // 宝石分解
  ID_GEM_WEAR = 1402;       // 宝石穿戴
  ID_GEM_COMPOSE = 1403;    // 宝石合成
  ID_GEM_CONVERT = 1404;    // 宝石置换

  // 境界
  ID_MEMORY_CHIP_UNLOCK = 1501;  // 境界-回忆点解锁

  // 公会
  ID_GUILD_CREATE = 1701;
  ID_GUILD_MANAGER_MEMBER = 1702;
  ID_GUILD_MODIFY_INFO = 1703;
  ID_GUILD_SET_NAME = 1704;
  ID_GUILD_MODIFY_NOTICE = 1705;
  ID_GUILD_APPLY_RATIFY = 1706;
  ID_GUILD_USER_APPLY = 1707;
  ID_GUILD_SEND_MAIL = 1708;
  ID_GUILD_SIGN_IN = 1709;
  ID_GUILD_QUIT = 1710;
  ID_GUILD_DISBAND = 1711;
  ID_GUILD_RECRUIT = 1712;                // 公会招募
  ID_GUILD_DONATE = 1713;                 // 公会捐献
  ID_GUILD_DONATE_RECEIVE = 1714;         // 公会捐赠领奖
  ID_GUILD_MODIFY_TEXT_INFO = 1715;       // 修改公会文本内容
  ID_GUILD_USER_JOIN_OR_QUIT = 1716;      // 公会玩家加入或退出
  ID_GUILD_MANAGE = 1717;                 // 公会管理
  ID_GUILD_POSITION_CHANGE = 1718;        // 职位变动
  ID_GUILD_SEASON_FINAL_DIVISION = 1719;  // 公会赛季最终段位

  // 公会副本
  ID_GUILD_DUNGEON_AWARD_RECEIVE = 1801;
  ID_GUILD_DUNGEON_FIGHT = 1802;
  ID_GUILD_DUNGEON_RECV_CHAPTER_TASK_AWARD = 1803;
  ID_GUILD_DUNGEON_RECV_BOSS_BOX_AWARD = 1804;
  ID_GUILD_DUNGEON_BUY_CHALLENGE_TIMES = 1805;
  ID_GUILD_DUNGEON_CHAPTER_RANK_LIKE = 1806;
  ID_GUILD_DUNGEON_NEW_FIGHT = 1807;
  ID_GUILD_DUNGEON_RECV_SEASON_TOP_DIVISION_AWARD = 1808;
  ID_GUILD_DUNGEON_USE_STRATEGY = 1809;

  // 点金系统
  ID_GOLD_BUY_GET_GOLD = 1901;  // 获取金币

  // 悬赏系统
  ID_DISPATCH_RECEIVE_TASK = 2001;   // 接收悬赏任务
  ID_DISPATCH_RECEIVE_AWARD = 2002;  // 领取任务奖励
  ID_DISPATCH_REFRESH_TASK = 2003;   // 刷新悬赏任务
  ID_DISPATCH_LEVEL_UP = 2004;       // 派遣等级提升

  // 迷宫
  ID_MAZE_GET_MAP = 2101;             // 获取地图信息
  ID_MAZE_TRIGGER_EVENT = 2102;       // 触发格子事件
  ID_MAZE_RECOVERY_HERO = 2103;       // 复活英雄
  ID_MAZE_GET_GRID = 2104;            // 获取格子信息
  ID_MAZE_FIGHT = 2105;               // 迷宫战斗
  ID_MAZE_BUY_REVIVE = 2106;          // 迷宫-购买复生神像
  ID_MAZE_USE_ITEM = 2107;            // 迷宫-使用道具
  ID_MAZE_TASK_RECEIVE_AWARD = 2108;  // 迷宫-领取任务奖励
  ID_MAZE_SELECT_BUFF = 2109;         // 迷宫-选择buff
  ID_MAZE_SWEEP = 2110;               // 迷宫 - 扫荡

  // 爬塔
  ID_TOWER_FIGHT = 2201;
  ID_TOWER_SWEEP = 2202;
  ID_TOWER_JUMP = 2203;

  // 主线
  ID_DUNGEON_FIGHT = 2301;
  ID_DUNGEON_RECV_AWARD = 2302;
  ID_DUNGEON_SPEED_RECV_AWARD = 2303;

  // 竞技场
  ID_ARENA_REFRESH = 2401;
  ID_ARENA_FIGHT = 2402;
  ID_ARENA_LIKE = 2403;
  ID_ARENA_BE_FIGHT = 2404;    // 被攻击
  ID_ARENA_RECV_AWARD = 2405;  // 领取任务奖励

  // 好友
  ID_FRIEND_ADD = 2501;                     // 好友申请
  ID_FRIEND_CONFIRM = 2502;                 // 好友申请确认
  ID_FRIEND_DELETE = 2503;                  // 好友删除
  ID_FRIEND_BLACKLIST = 2504;               // 好友拉黑
  ID_FRIEND_REMBLACKLIST = 2505;            // 好友删除合并单
  ID_FRIEND_SEND_LIKE = 2506;               // 好友点赞
  ID_FRIEND_RECV_LIKE = 2507;               // 好友领取点赞
  ID_FRIEND_ADDED = 2508;                   // 好友系统-被确认添加
  ID_FRIEND_DELETED = 2509;                 // 好友系统-被删除
  ID_FRIEND_BLACKLISTED = 2510;             // 好友系统-被拉黑
  ID_FRIEND_REMOVED_FROM_BLACKLIST = 2511;  // 好友系统-被移除黑名单

  // 神器
  ID_ARTIFACT_ACTIVATE = 2601;  // 神器激活
  ID_ARTIFACT_STAR_UP = 2602;   // 神器升星
  ID_ARTIFACT_STRENGTH = 2603;  // 神器强化
  ID_ARTIFACT_FORGE = 2604;     // 神器铸造
  ID_ARTIFACT_REVIVE = 2605;    // 神器重生

  // 公会天赋
  ID_GUILD_TALENT_LEVEL_UP = 2701;  // 职业线升级
  ID_GUILD_TALENT_RESET = 2702;     // 职业线重置

  // 设置
  ID_AVATAR_ADD = 2801;          // 激活头像
  ID_AVATAR_SET_ICON = 2802;     // 设置头像
  ID_AVATAR_TIME_EXPAND = 2803;  // 延长时间
  // 抽卡
  ID_SUMMON = 2901;                //	抽卡
  ID_SUMMON_SET_WISH_LIST = 2902;  // 设置心愿单

  // 新手引导
  ID_GUIDANCE_FINISH_NODE = 3001;      // 新手引导-完成节点
  ID_GUIDANCE_FINISH_GROUP = 3002;     // 新手引导-完成组
  ID_GUIDANCE_SELECT_SKIP_TAG = 3003;  // 新手引导 - 选择跳过标签
  ID_GUIDANCE_SKIP = 3004;             // 新手引导 - 跳过

  // 商店
  ID_SHOP_BUY = 3101;      // 商店购买商品
  ID_SHOP_REFRESH = 3102;  // 商店刷新

  // 任务
  ID_TASK_RECEIVE_AWARD = 3201;  // 任务-领奖

  // 材料本
  ID_TRIAL_FIGHT = 3301;          // 材料本-战斗
  ID_TRIAL_SWEEP = 3302;          // 材料本-扫荡
  ID_TRIAL_ONHOOK_REWARD = 3303;  // 材料本-挂机

  // 嘉年华
  ID_CARNIVAL_RECEIVE_AWARD = 3401;  // 嘉年华-领取奖励

  // 七日登录
  ID_SEVENDAY_LOGIN_TAKEAWARD = 3501;  // 七日登录- 领奖

  // 邮件
  ID_READ_MAIL = 3601;     // 读邮件
  ID_DRAW_MAILS = 3602;    // 领取邮件奖励
  ID_DELETE_MAILS = 3603;  // 删除邮件

  // 排行成就
  ID_RANK_ACHIEVE_RECV_AWARD = 3701;  // 领取排行成就奖励

  // 个人boss
  ID_MIRAGE_FIGHT = 3801;  // 战斗
  //  ID_MIRAGE_RECV_STAR_AWARD = 3802;  //领取星数奖励
  ID_MIRAGE_SAVE_AFFIXES = 3803;   // 保存词缀
  ID_MIRAGE_POWER_CRUSH = 3804;    // 战力碾压
  ID_MIRAGE_SWEEP = 3805;          // 扫荡
  ID_MIRAGE_RECEIVE_AWARD = 3806;  // 领取奖励

  // 钻石
  ID_ADD_DIAMOND = 3901;      // 钻石-获得
  ID_CONSUME_DIAMOND = 3902;  // 钻石-消耗

  // user模块
  ID_ADD_PURCHASE_NUM = 4001;        // 增加购买次数
  ID_FORMATION = 4002;               // 布阵
  ID_SET_NAME = 4003;                // 设置昵称
  ID_DUEL = 4005;                    // 切磋
  ID_ACCUSATION = 4006;              // 举报
  ID_WM_ONLINE = 4007;               // 完美的在线
  ID_RECV_SHARE_AWARD = 4008;        // 领取分享奖励
  ID_GIFT_CODE = 4009;               // 礼包码领取
  ID_VIP_UP = 4010;                  // vip等级提升
  ID_SET_ICON = 4011;                // 设置头像
  ID_QUESTIONNAIRE_FINISH = 4012;    // 问卷完成
  ID_RECV_H5_DESKTOP_REWARD = 4013;  // 领取保存h5桌面奖励
  ID_GM_DELETE_RESOURCES = 4014;     // gm删除玩家资源

  // item模块
  ID_ITEM_USE = 4101;     // 道具使用
  ID_ITEM_SELL = 4102;    // 道具出售
  ID_ITEM_SELECT = 4103;  // 兑换自选礼包

  // 战斗
  ID_FIGHT_PVE = 4201;             // PVE
  ID_FIGHT_PVP = 4202;             // PVP
  ID_FIGHT_REPORT = 4303;          // 战报
  ID_FIGHT_REPORT_DETAILS = 4304;  // 战报详情

  // 功勋
  ID_MEDAL_RECEIVE_AWARD = 4301;  // 功勋-领取奖励

  // 图鉴
  ID_HANDBOOKS_ACTIVE = 4401;            // 图鉴-激活
  ID_HANDBOOKS_RECV_AWARD = 4402;        // 图鉴-领奖
  ID_HANDBOOKS_ADD = 4403;               // 图鉴-添加新图鉴
  ID_HANDBOOKS_ACTIVE_HERO_ATTR = 4404;  // 图鉴-激活英雄属性

  // 碎片
  ID_FRAGMENT_COMPOSE = 4501;         // 碎片-合成
  ID_EMBLEM_FRAGMENT_COMPOSE = 4502;  // 纹章碎片-合成

  // 聊天
  ID_CHAT = 4601;       // 聊天
  ID_CHAT_LIKE = 4602;  // 聊天点赞

  // 水晶
  ID_CRYSTAL_ADD_HERO = 4701;            // 水晶-添加共鸣英雄
  ID_CRYSTAL_REMOVE_HERO = 4702;         // 水晶-移出共鸣英雄
  ID_CRYSTAL_UNLOCK_SLOT = 4703;         // 水晶-解锁槽位
  ID_CRYSTAL_SPEED_SLOT_C_D = 4704;      // 水晶-加速完成槽位冷却
  ID_CRYSTAL_INIT = 4705;                // 水晶-数据初始化
  ID_CRYSTAL_ACTIVE_ACHIEVEMENT = 4706;  // 水晶-激活成就
  ID_CRYSTAL_BLESSING_LEVEL_UP = 4707;   // 水晶-祝福升级

  // 充值
  ID_RECHARGE_NORMAL = 4801;          // 普通充值
  ID_RECHARGE_FIRST_GIFT = 4802;      // 首充礼包
  ID_RECHARGE_WEB_DIAMOND = 4803;     // 官网钻石充值
  ID_RECHARGE_WEB_GIFT = 4804;        // 官网礼包充值
  ID_RECHARGE_WEB_LARGE_MAIL = 4805;  // 官网大额充值
  ID_RECHARGE_COUPON = 4806;          // 代金券充值

  // 新功能预告
  ID_FORECAST_RECEIVE_AWARD = 4901;  // 新功能预告 - 领取奖励

  // 条件爬塔
  ID_TOWERSTAR_FIGHT = 5001;  // 战斗

  // 订单处理
  ID_ORDER_PROCESS = 5101;  // 订单处理
  ID_ORDER_REFUND = 5102;   // 订单退款

  // vip
  ID_VIP_BUY_GIFT = 5201;       // 购买vip等级礼包
  ID_VIP_RECHARGE_GIFT = 5202;  // vip付费礼包

  // 评分
  ID_RATE_SCORE = 5301;  // 评分

  // 密林 - 0.9.8
  ID_FLOWER_START_FEED = 5401;
  ID_FLOWER_FEED_GOBLIN = 5402;
  ID_FLOWER_CHANGE_GOBLIN = 5403;
  ID_FLOWER_FEED_SPECIAL = 5404;
  ID_FLOWER_UPDATE_SLOT = 5405;
  ID_FLOWER_START_PLANT = 5406;
  ID_FLOWER_SPEED_GROW = 5407;
  ID_FLOWER_HARVEST = 5408;
  ID_FLOWER_SEARCH = 5409;
  ID_FLOWER_SNATCH = 5410;
  ID_FLOWER_REVENGE = 5411;
  ID_FLOWER_BE_SNATCH = 5412;
  ID_FLOWER_OCCUPY_ATTACK = 5413;
  ID_FLOWER_OCCUPY_BE_ATTACK = 5414;
  ID_FLOWER_EXTEND_OCCUPY_TIME = 5415;
  ID_FLOWER_BUY_OCCUPY_ATTACK_NUM = 5416;
  ID_FLOWER_RECV_OCCUPY_AWARD = 5417;
  ID_FLOWER_LEVEL_UP = 5418;
  ID_FLOWER_ATTACK_LEVEL_GUARD = 5419;
  ID_FLOWER_RECV_PREVIEW_OCCUPY_AWARD = 5420;
  ID_FLOWER_SEND_LIKE = 5421;
  ID_FLOWER_RECV_LIKE = 5422;

  // 全民无双
  ID_MONTH_TASKS_RECV_AWARD = 5500;

  // 神器首发
  ID_ARTIFACT_DEBUT_SET_WISH = 5601;
  ID_ARTIFACT_DEBUT_SUMMON = 5602;
  ID_ARTIFACT_DEBUT_RECV_ACT_AWARD = 5603;
  ID_ARTIFACT_DEBUT_RECV_TASK_AWARD = 5604;
  ID_ARTIFACT_DEBUT_OPEN_PUZZLE = 5605;

  // 新服轮次活动
  ID_ROUND_ACTIVITY_RECV_TASK_AWARD = 5701;

  // 赛季等级
  ID_SEASON_LEVEL_UP = 5801;
  ID_SEASON_LEVEL_RECV_LV_AWARDS = 5802;
  ID_SEASON_LEVEL_RECV_TASK_AWARDS = 5803;

  ID_SEASON_ENTER = 5901;  // 进入赛季（点击赛季大门）

  // 每日资源快照
  ID_SNAPSHOT_MIN = 100000;
  ID_SNAPSHOT_USER_WM = 100001;              // 每日资源快照-玩家-完美
  ID_SNAPSHOT_HERO_WM = 100002;              // 每日资源快照-英雄及英雄所穿物品-完美
  ID_SNAPSHOT_BAG_WM = 100003;               // 每日资源快照-背包-完美
  ID_SNAPSHOT_FRIEND_WM = 100004;            // 每日资源快照-好友-完美
  ID_SNAPSHOT_END_WM = 100005;               // 每日资源快照-收集结束
  ID_SNAPSHOT_ARTIFACT_WM = 100006;          // 每日资源快照-神器
  ID_SNAPSHOT_FOREST_WM = 100007;            // 每日资源快照-密林
  ID_SNAPSHOT_FORMATION_WM = 100008;         // 每日资源快照-阵容
  ID_SNAPSHOT_GUILD_WM = 100009;             // 每日资源快照-公会
  ID_SNAPSHOT_WORLD_BOSS_RANK_WM = 100010;   // 每日资源快照-世界boss排行
  ID_SNAPSHOT_PEAK_SEASON_RANK_WM = 100011;  // 每日资源快照-巅峰竞技场排行快照
  ID_SNAPSHOT_GST_GUILD_WM = 100012;         // 每日资源快照-GST公会快照

  // 账号信息
  ID_ACCOUNT_ONLINE = 101001;    // 账号更新原因-登录
  ID_ACCOUNT_OFFLINE = 101002;   // 账号更新原因-下线
  ID_ACCOUNT_LEVEL = 101003;     // 账号更新原因-等级提升
  ID_ACCOUNT_VIP = 101004;       // 账号更新原因-vip提升
  ID_ACCOUNT_SET_NAME = 101005;  // 账号更新原因-修改昵称

  // 限时礼包
  ID_ACTIVITY_RECHARGE_BUY = 102001;  // 限时礼包-购买

  // 月卡
  ID_MONTHLY_CARD_RECEIVE_AWARD = 103001;  // 月卡 - 每日奖励
  ID_MONTHLY_CARD_RECHARGE = 103002;       // 月卡 - 充值

  // 战令
  ID_PASS_RECEIVE_AWARD = 104001;  // 战令 - 奖励
  ID_PASS_RECHARGE = 104002;       // 战令 - 充值
  ID_PASS_LEVEL_BUY = 104003;      // 战令 - 等级购买

  // 推送礼包
  ID_PUSH_GIFT_CREATE = 105001;    // 推送礼包 - 创建
  ID_PUSH_GIFT_DEL = 105002;       // 推送礼包 - 删除
  ID_PUSH_GIFT_RECHARGE = 105003;  // 推送礼包 - 充值

  // 账号标识
  ID_SET_ACCOUNT_TAG = 106001;  // 设置账号标识

  // 活动礼包
  ID_OPERATE_GIFT_RECHARGE = 107001;        // 活动礼包 - 充值
  ID_OPERATE_TASK_RECEIVED = 107002;        // 活动任务 - 领奖
  ID_OPERATE_ACTIVITY_GIFT_INTI = 107003;   // 配置礼品活动 - 初始化
  ID_OPERATE_ACTIVITY_TASK_INIT = 107004;   // 配置任务活动 - 初始化
  ID_PROMOTION_GIFT_INIT = 107005;          // 新推送礼包 - 初始化
  ID_PROMOTION_GIFT_SELECT_AWARD = 107006;  // 新推送礼包 - 自选礼包选奖励
  ID_PROMOTION_GIFT_RECHARGE = 107007;      // 新推送礼包 - 充值

  // 契约之所
  ID_GODDESS_LEVEL_UP = 108001;             // 女武神 - 升级
  ID_GODDESS_CONTRACT_LEVEL_UP = 108002;    // 契约之所 - 升级
  ID_GODDESS_INIT = 108003;                 // 女武神 - 初始化
  ID_GODDESS_FEED = 108004;                 // 女武神 - 喂养
  ID_GODDESS_CHAPTER_FIGHT = 108005;        // 女武神 - 章节战斗
  ID_GODDESS_CHAPTER_FINISH = 108006;       // 女武神 - 阅读章节
  ID_GODDESS_UNLOCK_CHAPTER = 108007;       // 女武神 - 解锁章节
  ID_GODDESS_CHAPTER_TAKE_REWARD = 108008;  // 女武神 - 领取章节奖励
  ID_GODDESS_TOUCH = 108009;                // 女武神 - 触碰
  ID_GODDESS_STORY_REWARD = 108010;         // 女武神 - 故事领奖
  ID_GODDESS_COLLECTION = 108011;           // 女武神 - 收集藏品

  // 神魔抽卡
  ID_DIVINE_DEMON_SUMMON = 109001;              // 神魔抽卡 - 抽卡
  ID_DIVINE_DEMON_RECEIVE_TASK_AWARD = 109002;  // 神魔抽卡 - 领取任务奖励

  // 神树争霸
  ID_WRESTLE_FIGHT = 110001;             // 战斗
  ID_WRESTLE_BE_FIGHT = 110002;          // 被打
  ID_WRESTLE_LIKE = 110003;              // 点赞
  ID_WRESTLE_RECV_LEVEL_AWARD = 110004;  // 领取赛场奖励
  ID_WRESTLE_CHANGE_ROOM = 110005;       // 更换房间

  // 永恒仪式
  ID_RITE_MARK_COLLECTION = 111001;  // 印记收集
  ID_RITE_RECYCLE = 111002;          // 阵法回收

  // 天赋树
  ID_TALENT_TREE_LEVEL_UP = 112001;             // 天赋树 - 升级
  ID_TALENT_TREE_RESET = 112002;                // 天赋树 - 洗点
  ID_TALENT_TREE_RECEIVE_TASK_AWARDS = 112003;  // 天赋树 - 领取任务奖励
  ID_TALENT_TREE_RANK_UPDATE = 112004;          // 天赋树 - 排行变化

  // 百塔
  ID_TOWER_SEASON_FIGHT = 120001;            // 战斗
  ID_TOWER_SEASON_RECV_TASK_AWARD = 120002;  // 领取任务奖励

  // 神之馈赠
  ID_GOD_PRESENT_RECV_ITEM = 121001;
  ID_GOD_PRESENT_SUMMON = 121002;
  ID_GOD_PRESENT_RECV_AWARDS = 121003;

  // 掉落活动
  ID_DROP_ACTIVITY_EXCHANGE = 122001;
  ID_DROP_ACTIVITY_RECV_DAILY_AWARD = 122002;

  // 累登
  ID_DAILY_ATTENDANCE_RECV_AWARD = 123001;  // 领奖

  // 每日特惠
  ID_DAILY_SPECIAL_RECV_AWARD = 124001;  // 领奖

  // 公会红包
  ID_GUILD_CHEST_RECV_AWARD = 125001;  // 领奖
  ID_GUILD_CHEST_SET_LIKE = 125002;    // 送花
  ID_GUILD_CHEST_ACTIVATE = 125003;    // 激活

  // 世界boss
  ID_WORLD_BOSS_SELECT_LEVEL = 126001;     // 选难度
  ID_WORLD_BOSS_TASK_RECV_AWARD = 126002;  // 任务领奖
  ID_WORLD_BOSS_FIGHT = 126003;            // 战斗

  // 英雄
  ID_SKIN_USE = 127001;

  // 故事活动
  ID_ACTIVITY_STORY_LOGIN_AWARD = 128001;  // 持续登录领奖
  ID_ACTIVITY_STORY_EXCHANGE = 128002;     // 兑换
  ID_ACTIVITY_STORY_FIGHT = 128003;        // 战斗

  // 助力活动
  ID_ASSISTANCE_ACTIVITY_RECV_AWARD = 129001;  // 助力活动领奖

  // 失序空间
  ID_DISORDER_LAND_TRIGGER_EVENT = 130001;      // 触发事件
  ID_DISORDER_LAND_FIGHT = 130002;              // 战斗事件
  ID_DISORDER_LAND_BUY_STAMINA = 130003;        // 购买体力
  ID_DISORDER_LAND_TRIGGER_GUARANTEE = 130004;  // 触发保底

  // 回流活动
  ID_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS = 131001;  // 领取登录奖励

  // 巅峰竞技场
  ID_PEAK_RECV_INVITE_REWARD = 132001;   // 领取邀请奖励
  ID_PEAK_FIGHT = 132002;                // 战斗
  ID_PEAK_DO_GUESS = 132003;             // 竞猜下注
  ID_PEAK_WORSHIP = 132004;              // 膜拜
  ID_PEAK_FIGHTER_RANK_CHANGE = 132005;  // 玩家排名变化

  // 赛季主线
  ID_SEASON_DUNGEON_FIGHT = 133001;  // 赛季主线战斗
  ID_SEASON_DUNGEON_RECV = 133002;   // 赛季主线领奖

  // 赛季前奖励
  ID_PRE_SEASON_RECV = 134001;  // 赛季前奖励

  // 赛季回流
  ID_SEASON_RETURN_ADD_AWARDS = 135001;   // 发放奖励
  ID_SEASON_RETURN_TAKE_AWARDS = 135002;  // 领取奖励

  // 赛季羁绊
  ID_SEASON_LINK_RUNE_COLLECTION = 136001;            // 符石收集
  ID_SEASON_LINK_MONUMENT_CULTIVATION = 136002;       // 丰碑养成
  ID_SEASON_LINK_RECYCLE = 136003;                    // 符石回收
  ID_SEASON_LINK_MONUMENT_RECV_RARE_AWARDS = 136004;  // 领取丰碑品质奖励
  ID_SEASON_LINK_ACTIVATION = 136005;                 // 羁绊激活

  // 公会战
  ID_GUILD_SAND_TABLE_FIGHT = 137001;                 // 公会战斗
  ID_GUILD_SAND_TABLE_DISPATCH = 137002;              // 公会队伍派遣
  ID_GUILD_SAND_TABLE_REORDER = 137003;               // 公会战调整队伍顺序
  ID_GUILD_SAND_TABLE_BLESS = 137004;                 // 公会战祝福
  ID_GUILD_SAND_TABLE_BLESS_CHANGE = 137005;          // 公会战祝福归属变化
  ID_GUILD_SAND_TABLE_DONATE = 137006;                // 公会战个人贡献变化
  ID_GUILD_SAND_TABLE_GUILD_SCORE = 137007;           // 公会战积分变化
  ID_GUILD_SAND_TABLE_TASK = 137008;                  // 公会战任务完成
  ID_GUILD_SAND_TABLE_OCCUPATION = 137009;            // 公会战格子变化
  ID_GUILD_SAND_TABLE_HOST = 137010;                  // 公会战托管
  ID_GUILD_SAND_TABLE_BALANCE = 137011;               // 公会战结算
  ID_GUILD_SAND_TABLE_MODIFY_NOTICE = 137012;         // 公会战更改公告 1
  ID_GUILD_SAND_TABLE_BUILD_DONATE = 137013;          // 公会战建筑捐赠
  ID_GUILD_SAND_TABLE_BUILD_DISPATCH = 137014;        // 公会战建筑派遣
  ID_GUILD_SAND_TABLE_BOX_RECV = 137015;              // 公会战宝箱领奖
  ID_GUILD_SAND_TABLE_BUILD_TASK = 137016;            // 公会战建筑任务领取
  ID_GUILD_SAND_TABLE_VOTE = 137017;                  // 公会战擂台助威
  ID_GUILD_SAND_TABLE_ORE_FIGHT = 137018;             // 公会战占矿
  ID_GUILD_SAND_TABLE_ORE_RESOURCE_CHANGE = 137019;   // 矿资源变化
  ID_GUILD_SAND_TABLE_TECH_TASK_AWARD = 137020;       // 公会战科技奖励
  ID_GUILD_SAND_TABLE_TECH_LEVEL_UP = 137021;         // 公会战科技升级
  ID_GUILD_SAND_TABLE_STREAK = 137022;                // 公会地块连胜场次
  ID_GUILD_SAND_TABLE_CHALLENGE_TASK_AWARD = 137023;  // 新擂台赛领取任务奖励
  ID_GUILD_SAND_TABLE_CHALLENGE_FIGHT = 137024;       // 新擂台赛战斗
  ID_GUILD_SAND_TABLE_CHALLENGE_STREAK = 137025;      // 新擂台赛队伍连胜记录

  // 剧情回忆录
  ID_STORY_REVIEW_UNLOCK = 138001;  // 剧情回忆录解锁剧情

  // 新年活动
  ID_NEW_YEAR_ACTIVITY_STORY_LOGIN_AWARD = 139001;  // 新年活动持续登录领奖

  // 金字塔活动
  ID_PYRAMID_CHOOSE_AWARD = 140001;  // 金字塔活动自选奖励
  ID_PYRAMID_DRAW = 140002;          // 金字塔活动抽奖
  ID_PYRAMID_TASK_AWARD = 140003;    // 金字塔活动任务领奖

  // 神器碎片溢出回收
  ID_ARTIFACT_FRAGMENT_RECYCLE = 141001;  // 神器碎片溢出回收

  // 赛季竞技场
  ID_SEASON_ARENA_RECV_TASK_AWARD = 142001;      // 赛季竞技场完成任务奖励
  ID_SEASON_ARENA_RECV_DIVISION_AWARD = 142002;  // 赛季竞技场完成段位奖励
  ID_SEASON_ARENA_DIVISION_CHANGE = 142003;      // 赛季竞技场段位变化
  ID_SEASON_ARENA_FIGHT = 142004;                // 赛季竞技场战斗
  ID_SEASON_ARENA_BE_FIGHT = 142005;             // 赛季竞技场战斗

  // 公会boss战
  ID_GST_BOSS_FIGHT = 143001;          // 战斗事件
  ID_GST_BOSS_AWARD = 143002;          // 购买体力
  ID_GST_BOSS_BUY_CHALLENGE = 143003;  // 购买挑战令

  // 称号
  ID_TITLE_ADD = 144001;  // 称号添加

  // 遗物
  ID_REMAIN_BOOK_RECV_EXP = 154001;  // 遗物图鉴接受经验
  ID_REMAIN_BOOK_LEVEL_UP = 154002;  // 遗物图鉴升级
  ID_REMAIN_STAR_UP = 154003;        // 遗物升星

  // 周年庆活动
  ID_ACTIVITY_TURN_TABLE_SUMMON = 155001;       // 周年庆活动抽奖
  ID_ACTIVITY_TURN_TABLE_LOGIN_AWARD = 155002;  // 周年庆活动登录奖励
  ID_ACTIVITY_TURN_TABLE_TASK_AWARD = 155003;   // 周年庆活动任务领奖

  // 龙战
  ID_GST_DRAGON_FIGHT = 156001;        // 龙战战斗
  ID_GST_DRAGON_SETTLEMENT = 156002;   // 龙战胜败结算
  ID_GST_DRAGON_CULTIVATION = 156003;  // 龙战养龙
  ID_GST_DRAGON_TASK_AWARD = 156004;   // 龙战任务领奖

  // 公会竞赛
  ID_GUILD_MOB_ACCEPT_TASK = 157001;  // 接取公会竞赛任务
  ID_GUILD_MOB_GET_SCORE = 157002;    // 公会竞赛积分
  ID_GUILD_MOB_BUY_TIMES = 157003;    // 公会竞赛次数购买

  // Boss挑战
  ID_BOSS_RUSH_FIGHT = 157101;       // Boss挑战战斗
  ID_BOSS_RUSH_TASK_AWARD = 157102;  // Boss挑战任务领奖

  // 活动聚合
  ID_ACTIVITY_SUM_LOGIN_REWARD = 157201;            // 登录领奖
  ID_ACTIVITY_SUM_TASK_REWARD = 157202;             // 任务领奖
  ID_ACTIVITY_SUM_PUZZLE_CELL = 157203;             // 翻格子
  ID_ACTIVITY_SUM_TICKET_BUY = 157204;              // 买门票
  ID_ACTIVITY_SUM_EXCHANGE = 157205;                // 兑换商店
  ID_ACTIVITY_SUM_TURN_TABLE_SUMMON = 157206;       // 周年庆活动抽卡
  ID_ACTIVITY_SUM_TURN_TABLE_SELECT_BUFF = 157207;  // 周年庆活动选择BUFF
  ID_ACTIVITY_SUM_FEED_FEED = 157208;               // 喂养
  ID_ACTIVITY_SUM_FEED_MAKE_GIFT = 157209;          // 制作礼物
  ID_ACTIVITY_SUM_SHOOT = 157210;                   // 射击小游戏领奖

  ID_SELECT_SUMMON_SUMMON = 157301;  // 选择抽卡-抽卡

  // 赛季冲榜积分变化
  ID_SEASON_COMPLIANCE_SCORE_CHANGE = 157401;  // 赛季冲榜积分变化

  // 赛季开门玩法
  ID_SEASON_DOOR_TASK_REWARD = 157501;  // 任务领奖
  ID_SEASON_DOOR_FIGHT = 157502;        // 战斗

  // 赛季装备
  ID_SEASON_JEWELRY_GET = 157601;                   // 赛季装备-获得
  ID_SEASON_JEWELRY_WEAR = 157602;                  // 赛季装备-穿戴
  ID_SEASON_JEWELRY_SKILL_LEVEL_UP = 157603;        // 赛季装备-词条升级
  ID_SEASON_JEWELRY_SKILL_CLASS_UP = 157604;        // 赛季装备-词条升阶
  ID_SEASON_JEWELRY_SKILL_CHANGE = 157605;          // 赛季装备-词条洗练
  ID_SEASON_JEWELRY_SKILL_CHANGE_CONFIRM = 157606;  // 赛季装备-词条洗练确认
  ID_SEASON_JEWELRY_DECOMPOSE = 157607;             // 赛季装备-分解
  ID_SEASON_JEWELRY_RECYCLE = 157608;               // 赛季装备-回收

  // 赛季地图
  ID_SEASON_MAP_TASK_AWARD = 157701;  // 赛季地图 - 任务领奖
  ID_SEASON_MAP_FIGHT = 157702;       // 赛季地图 - 战斗
  // 禁言账号
  ID_MUTE_ACCOUNT = 157801;  // 禁言

  // 宠物爬塔
  ID_TOWER_POKEMON_FIGHT = 157901;       // 宠物爬塔 - 战斗
  ID_TOWER_POKEMON_TASK_AWARD = 157902;  // 宠物爬塔 - 任务领奖

  // 宠物
  ID_POKEMON_STAR_UP = 158001;             // 宠物升星
  ID_POKEMON_POTENTIAL_LEVEL_UP = 158002;  // 宠物潜能升级
  ID_POKEMON_MASTER_REWARD = 158003;       // 宠物大师等级领奖

  ID_POKEMON_SUMMON_SUMMON = 158101;  // 宠物抽卡
}

// 资源变化类型
enum RESOURCE_CHANGE {
  REASON_NONE = 0;
  REASON_GM = 1;
  REASON_DUNGEON_FIGHT = 2;              // 战役
  REASON_ITEM_USE = 3;                   // 使用道具
  REASON_ACHIEVE = 4;                    // 成就
  REASON_DAILYTASK = 5;                  // 日常任务
  REASON_SHOP_BUY = 6;                   // 商店购买
  REASON_DRAW_MAIL = 7;                  // 有奖领奖
  REASON_ADD_PURCHASE_NUM = 8;           // 购买次数
  REASON_ONHOOK = 9;                     // 领取挂机奖励
  REASON_SPEED_ONHOOK = 10;              // 领取加速挂机奖励
  REASON_HERO_LEVEL_UP = 11;             // 英雄升级
  REASON_HERO_UPGRADE_STAGE = 12;        // 英雄突破
  REASON_BUY_HERO_SLOT = 13;             // 购买英雄栏位
  REASON_REG_ADD_RESOURCE = 14;          // 注册时添加资源
  REASON_DRAW_MAILS = 15;                // 领取邮件奖励
  REASON_HERO_UPGRADE_STAR = 16;         // 英雄升星
  REASON_PLAYER_LEVELUP = 17;            // 主角升级
  REASON_HERO_DISBAND = 18;              // 英雄分解
  REASON_SUMMON_HERO = 19;               // 召唤
  REASON_REFRESH_RANDOM_SHOP = 20;       // 刷新随机商店
  REASON_FRAGMENT_COMPOSE = 21;          // 碎片合成
  REASON_ITEM_SELL = 22;                 // 道具出售
  REASON_EQUIP_STRENGTH = 23;            // 装备强化
  REASON_EQUIP_REFINE = 24;              // 装备精炼
  REASON_EQUIP_ENCHANT = 25;             // 装备附魔
  REASON_TOWER_FIGHT = 26;               // 爬塔战斗
  REASON_TOWER_SWEEP = 27;               // 爬塔扫荡
  REASON_ARENA_FIGHT = 28;               // 竞技场-挑战
  REASON_ARENA_BUY_TICKET = 29;          // 竞技场-购买门票
  REASON_ROBOT_ADD_RESOURCE = 30;        // 机器人添加资源
  REASON_ARENA_LIKE = 31;                // 竞技场-点赞
  REASON_ARTIFACT_ACTIVATE = 32;         // 神器激活
  REASON_ARTIFACT_STAR_UP = 33;          // 神器升星
  REASON_GEM_DECOMPOSE = 34;             // 宝石分解
  REASON_GEM_REBUILD = 35;               // 宝石置换
  REASON_GEM_COMPOSE = 36;               // 宝石合成
  REASON_SET_NAME = 37;                  // 修改名称
  REASON_TRIAL_FIGHT = 38;               // 材料本战斗
  REASON_TRIAL_SWEEP = 39;               // 材料本扫荡
  REASON_TRIAL_RECEIVE_STAR_AWARD = 40;  // 材料本领取星级奖励
  REASON_EMBLEM_LEVEL_UP = 41;           // 纹章升级
  REASON_EMBLEM_STAGE_UP = 42;           // 纹章升星
  REASON_EMBLEM_DECOMPOSE = 43;          // 纹章分解
  REASON_GOLDBUY_RECEIVE = 44;           // 点金系统获取金币
  REASON_DISPATCH_RECEIVE_TASK = 45;     // 悬赏任务接取
  REASON_DISPATCH_RECEIVE_AWARD = 46;    // 悬赏奖励领取
  REASON_DISPATCH_REFRESH = 47;          // 悬赏任务刷新
  REASON_FRIEND_SEND_LIKE = 48;          // 好友发送友情点
  REASON_FRIEND_RECV_LIKE = 49;          // 好友领取友情点
  REASON_RANKACHIEVE_RECV_AWARD = 50;    // 领取排行成就奖励
  REASON_EQUIP_EVOLUTION = 51;           // 装备进阶
  REASON_EQUIP_DECOMPOSE = 52;           // 装备分解
  REASON_EQUIP_REVIVE = 53;              // 装备重生
  REASON_MAZE_RECOVERY_HEROES = 54;      // 迷宫-复活所有英雄
  REASON_MAZE_BATTLE_EVENT = 55;         // 迷宫-触发战斗事件
  REASON_MIRAGE_RECV_STAR_AWARD = 56;    // 个人boss领取星数奖励
  REASON_MIRAGE_FIGHT = 57;              // 个人boss战斗
  REASON_EMBLEM_BLESSING = 58;           // 纹章祝福
  REASON_GUILD_CREATE = 59;              // 公会-创建
  REASON_GUILD_SET_NAME = 60;            // 公会-修改名字
  REASON_GUILD_SIGN_IN = 61;             // 公会-签到
  REASON_MAZE_CHOICE_EVENT = 62;         // 迷宫-选择事件
  REASON_MAZE_BOX_EVENT = 63;            // 迷宫-宝库事件
  REASON_HERO_BACK = 64;                 // 英雄-重生
  REASON_HERO_REVIVE = 65;               // 英雄-回退
  REASON_HERO_CHANGE = 66;               // 英雄-转换
  REASON_TALES_TAKE_AWARD = 67;          // 列传领取章节奖励
  REASON_TALES_ELITE = 68;               // 列传强敌
  REASON_MEMORY_UNLOCK_CHIP = 69;        // 境界-解锁回忆点
  // REASON_FOREST_CHANGE_GOBLIN = 70;                    // 密林-更换哥布林
  // REASON_FOREST_CHANGE_HIGHEST_SEED = 71;              // 密林-购买品质最高的种子
  // REASON_FOREST_SPEED_GROW = 72;                       // 密林-加速生长
  // REASON_FOREST_HARVEST = 73;                          // 密林-收获
  // REASON_FOREST_LOOT = 74;                             // 密林-掠夺
  // REASON_FOREST_REVENGE = 75;                          // 密林-复仇
  REASON_GUILD_DUNGEON_CHAPTER_TASK_RECEIVE = 76;              // 公会副本-领取章节进度奖励
  REASON_GUILD_DUNGEON_FIGHT = 77;                             // 公会副本-战斗
  REASON_HERO_CHANGE_SAVE = 78;                                // 英雄-转换保存
  REASON_ARTIFACT_STRENGTH = 79;                               // 神器强化
  REASON_ARTIFACT_FORGE = 80;                                  // 神器铸造
  REASON_GUILD_TALENT_LEVEL_UP = 81;                           // 公会天赋-职能线重置
  REASON_GUILD_TALENT_RESET = 82;                              // 公会天赋-职能线重置
  REASON_ARTIFACT_REVIVE = 83;                                 // 神器重生
  REASON_EMBLEM_COMPOSE = 84;                                  // 纹章-合成
  REASON_CARNIVAL_TASK_RECV_AWARD = 85;                        // 嘉年华领奖
  REASON_SEVENDAY_AWARD = 86;                                  // 七日登录奖励
  REASON_HANDBOOK_AWARD = 87;                                  // 图鉴奖励
  REASON_MEDAL_DAILY_AWARD_RECEIVE = 88;                       // 功勋-每日奖励领取
  REASON_MEDAL_LEVEL_AWARD_RECEIVE = 89;                       // 功勋-等级奖励领取
  REASON_MEDAL_TASK_AWARD_RECEIVE = 90;                        // 功勋-任务奖励领取
  REASON_MAZE_BUY_REVIVE = 91;                                 // 迷宫-购买复生神像
  REASON_EQUIP_GROW_TRANSFER = 92;                             // 装备-装备养成转移
  REASON_EMBLEM_GROW_TRANSFER = 93;                            // 纹章-纹章养成转移
  REASON_FORECAST_AWARD = 94;                                  // 新功能预告 - 新功能奖励
  REASON_CRYSTAL_UNLOCK_SLOT = 95;                             // 水晶-解锁槽位
  REASON_CRYSTAL_SPEED_SLOT_CD = 96;                           // 水晶-加速完成槽位冷却
  REASON_CRYSTAL_ADD_RESONANCE = 97;                           // 水晶-添加共鸣英雄
  REASON_RECHARGE_NORMAL = 98;                                 // 充值-订单处理
  REASON_RECHARGE_FIRST_PRESENT = 99;                          // 充值-首充赠送
  REASON_TOWER_JUMP = 101;                                     // 爬塔跳关
  REASON_ACTIVITY_RECHARGE_BUY = 102;                          // 限时礼包-购买
  REASON_FIRST_RECHARGE_GIFT = 103;                            // 首充礼包-购买
  REASON_TOWERSTAR_STAR_AWARD = 104;                           // 条件爬塔星级奖励
  REASON_TOWERSTAR_DAILY_AWARD = 105;                          // 条件爬塔每日奖励
  REASON_TOWERSTAR_FIRST_AWARD = 106;                          // 条件爬塔首通奖励
  REASON_MAZE_TASK_AWARD = 107;                                // 迷宫 - 任务奖励
  REASON_MONTHLY_CARD_DAILY_AWARD_RECEIVE = 108;               // 月卡-每日奖励领取
  REASON_MONTHLY_CARD_RECHARGE = 109;                          // 月卡-购买
  REASON_VIP_BUY_GIFT = 110;                                   // vip礼包-购买
  REASON_PASS_RECEIVE = 111;                                   // 战令领取
  REASON_PASS_RECHARGE = 112;                                  // 战令购买
  REASON_RECV_SHARE_AWARD = 113;                               // 分享-领奖
  REASON_PUSH_GIFT_RECHARGE = 114;                             // 推送礼包购买
  REASON_MIRAGE_SWEEP = 115;                                   // 个人boss - 扫荡
  REASON_MIRAGE_POWER_CRUSH = 116;                             // 个人boss - 战力碾压
  REASON_GIFT_CODE = 117;                                      // 礼包码
  REASON_ARENA_REFRESH = 118;                                  // 竞技场-刷新
  REASON_ARENA_RECV_TASK_AWARD = 119;                          // 竞技场-领取任务奖励
  REASON_ITEM_SELECT = 120;                                    // 道具 - 兑换自选礼包
  REASON_OPERATION_ACTIVITY_GIFT = 121;                        // 活动礼包
  REASON_PASS_LEVEL_BUY = 122;                                 // 战令 - 等级购买
  REASON_OPERATION_ACTIVITY_TASK = 123;                        // 活动任务
  REASON_FOREST_RECV_LV_AWARD = 124;                           // 密林-领取等级奖励
  REASON_HERO_CONVERSION = 125;                                // 英雄-兑换
  REASON_GODDESS_FEED = 126;                                   // 喂养女武神
  REASON_GODDESS_CONTRACT_LEVEL_UP = 127;                      // 升级契约之所
  REASON_GODDESS_UNLOCK_DUNGEON = 128;                         // 女武神解锁章节
  REASON_GODDESS_TALES_TAKE_AWARDS = 129;                      // 领取女武神章节奖励
  REASON_DIVINE_DEMON_HERO_SUMMON = 130;                       // 神器抽卡 - 英雄抽卡
  REASON_DIVINE_DEMON_TASK_AWARD = 131;                        // 神器抽卡 - 领取任务奖励
  REASON_DIVINE_DEMON_SUMMON_AWARD = 132;                      // 神器抽卡 - 抽卡奖励
  REASON_VIP_RECHARGE_GIFT = 133;                              // VIP付费礼包
  REASON_WRESTLE_LIKE = 134;                                   // 神树争霸-点赞
  REASON_WRESTLE_RECV_LEVEL_TASK = 135;                        // 神树争霸-领取赛场奖励
  REASON_AVATAR_USE_ITEM = 136;                                // 头像框物品使用
  REASON_GUILD_DUNGEON_RECV_BOSS_BOX = 137;                    // 公会副本 - 领取boss宝箱
  REASON_GUILD_DUNGEON_BUY_CHALLENGE_TIMES = 138;              // 公会副本 - 购买挑战次数
  REASON_FLOWER_CHANGE_GOBLIN = 139;                           // 密林-更换哥布林
  REASON_FLOWER_CHANGE_HIGHEST_SEED = 140;                     // 密林-购买品质最高的种子
  REASON_FLOWER_SPEED_GROW = 141;                              // 密林-加速生长
  REASON_FLOWER_HARVEST = 142;                                 // 密林-收获
  REASON_FLOWER_LOOT = 143;                                    // 密林-掠夺
  REASON_FLOWER_REVENGE = 144;                                 // 密林-复仇
  REASON_FLOWER_OCCPUY_ATTACK_ALLY = 145;                      // 密林-攻打盟友据点
  REASON_FLOWER_BUY_OCCPUY_ATTACK_NUM = 146;                   // 密林-购买攻打据点次数
  REASON_FLOWER_SNATCH = 147;                                  // 密林-掠夺
  REASON_FLOWER_RECV_LV_AWARD = 149;                           // 密林-领取等级奖励
  REASON_FLOWER_OCCPUY_REVENGE_ALLY = 150;                     // 密林-反攻盟友据点
  REASON_FLOWER_LAST_OCCPUY_AWARD = 151;                       // 密林-攻打据点时，获得的上一个据点的结算奖励
  REASON_FLOWER_REVENGE_LAST_OCCPUY_AWARD = 152;               // 密林-反攻据点时，获得的上一个据点的结算奖励
  REASON_FLOWER_RECV_OCCUPY_AWARD = 153;                       // 密林-领取据点采集奖励
  REASON_FLOWER_EXTEND_OCCUPY_TIME = 154;                      // 密林-延长据点占领时间
  REASON_HERO_GEM_LEVEL_UP = 155;                              // 宝石 - 升级
  REASON_GODDESS_STORY_AWARDS = 156;                           // 契约之所 - 故事领奖
  REASON_GUILD_DUNGEON_CHAPTER_RANK_LIKE = 157;                // 公会副本 - 章节排行点赞
  REASON_MIRAGE_RECEIVE_AWARD = 158;                           // 个人boss - 领取一次性奖励
  REASON_EMBLEM_FRAGMENT_COMPOSE = 159;                        // 纹章 - 碎片合成
  REASON_FLOWER_RECV_OCCUPY_MONTHLY_CARD_AWARD = 160;          // 密林-领取据点采集奖励（月卡加成）
  REASON_MONTH_TASKS_RECV_AWARD = 161;                         // 领取全民无双奖励
  REASON_DAILY_WISH_SUMMON = 162;                              //  每日许愿抽卡
  REASON_EMBLEM_CUSTOMIZE = 163;                               // 符文定制
  REASON_LINK_SUMMON = 164;                                    // 流派抽卡
  REASON_ARTIFACT_POINTS_EXCHANGE = 165;                       // 神器积分兑换
  REASON_ARTIFACT_DEBUT_SUMMON = 166;                          // 神器首发 - 抽卡
  REASON_ARTIFACT_DEBUT_POINTS_EXCHANGE = 167;                 // 神器首发 - 积分兑换
  REASON_ARTIFACT_DEBUT_RECV_ACT_AWARD = 168;                  // 神器首发 - 领取活动奖励
  REASON_ARTIFACT_DEBUT_RECV_TASK_AWARD = 169;                 // 神器首发 - 领取任务奖励
  REASON_ARTIFACT_DEBUT_OPEN_PUZZLE = 170;                     // 神器首发 - 拼图掀开格子
  REASON_FLOWER_RECV_OCCUPY_PREVIEW_AWARD = 171;               // 密林-领取据点采集奖励
  REASON_FLOWER_RECV_OCCUPY_PREVIEW_MONTHLY_CARD_AWARD = 172;  // 密林-领取据点预览奖励（月卡加成）
  REASON_ARTIFACT_DEBUT_ADD_POINTS = 173;                      // 神器首发 - 抽卡获得积分
  REASON_ARTIFACT_DEBUT_RESET = 174;                           // 神器首发 - 活动重置
  REASON_TRIAL_ON_HOOK_RECV_AWARD = 175;                       // 材料本挂机
  REASON_TRIAL_SPEED_ON_HOOK_RECV_AWARD = 176;                 // 材料本加速挂机
  REASON_GUILD_DONATE = 177;                                   // 公会捐赠
  REASON_GUILD_GET_DONATE_AWARD = 178;                         // 获取公会捐赠奖励
  REASON_ROUND_ACTIVITY_RECV_TASK_AWARD = 179;                 // 新服轮次活动 - 领取任务奖励
  REASON_WEB_RECHARGE_DIAMOND = 180;                           // 官网钻石充值
  REASON_WEB_RECHARGE_GIFT = 181;                              // 官网礼包充值
  REASON_TOWER_SEASON_RECV_TASK_AWARD = 182;                   // 百塔 - 领取任务奖励
  REASON_TOWER_SEASON_FIGHT_PASS_AWARD = 183;                  // 百塔 - 战斗通过奖励发放
  REASON_HERO_EXCHANGE = 184;                                  // 英雄 - 转换
  REASON_GOD_PRESENT_REPLACEMENT = 185;                        // 神之馈赠 - 替换 - 已弃用
  REASON_GOD_PRESENT_COLLECTED = 186;                          // 神之馈赠 - 收集 - 已弃用
  REASON_GOD_PRESENT_SUMMON = 187;                             // 777抽卡活动 - 抽卡
  REASON_GODDESS_RECOVERY = 189;                               // 女武神 - 恢复
  REASON_GOD_PRESENT_RECV_ITEM = 190;                          // 777抽卡活动 - 领取抽卡道具
  REASON_GOD_PRESENT_RECV_AWARDS = 191;                        // 777抽卡活动 - 选定卡组，领取抽卡活动奖励
  REASON_RECV_H5_DESKTOP_REWARD = 192;                         // 领取保存h5桌面奖励
  REASON_GUILD_DUNGEON_SEASON_TOP_DIVISION = 193;              // 公会副本 - 赛季最高段位结算
  REASON_DROP_ACTIVITY_EXCHANGE = 194;                         // 掉落活动 - 兑换道具
  REASON_DAILY_ATTENDANCE_RECEIVE_AWARD = 195;                 // 累登 - 领奖
  REASON_DAILY_SPECIAL_DAILY_AWARD = 196;                      // 每日特惠 - 每日奖励
  REASON_DAILY_SPECIAL_SCORE_AWARD = 197;                      // 每日特惠 - 积分奖励
  REASON_WRESTLE_CHANGE_ROOM = 198;                            // 神树争霸 - 更换房间
  REASON_GUILD_CHEST_RECV = 199;                               // 公会宝箱 - 领奖
  REASON_GUILD_CHEST_SEND_FLOWER = 200;                        // 公会宝箱 - 送花
  REASON_GUILD_CHEST_RECV_FLOWER = 201;                        // 公会宝箱 - 收花
  REASON_NEW_HANDBOOK_HERO = 202;                              // 新增图鉴英雄(同名英雄算一个)
  REASON_WORLD_BOSS_RECEIVE_TASK_AWARD = 203;                  // 世界boss - 领取任务奖励
  REASON_WORLD_BOSS_RECEIVE_WORSHIP_AWARD = 204;               // 世界boss - 领取膜拜奖励
  REASON_WORLD_BOSS_FIGHT_COST = 205;                          // 世界boss - 战斗消耗
  REASON_WORLD_BOSS_FIGHT_AWARD = 206;                         // 世界boss - 战斗奖励
  REASON_BUY_EMBLEM_SLOT = 207;                                // 购买符文背包栏位
  REASON_TOWER_SEASON_LIKE_AWARD = 208;                        // 百塔 - 点赞奖励
  REASON_NEW_SKIN_AVATAR = 209;                                // 新增皮肤类型avatar
  REASON_ACTIVITY_STORY_SWEEP = 210;                           // 活动故事 - 扫荡
  REASON_ACTIVITY_STORY_LOGIN_AWARD = 211;                     // 活动故事 - 登录奖励
  REASON_ACTIVITY_STORY_EXCHANGE = 212;                        // 活动故事 - 交换
  REASON_ACTIVITY_STORY_CHANGE_ACTIVITY = 213;                 // 活动故事 - 更改活动ID
  REASON_ACTIVITY_STORY_TICKET_INCREASE = 214;                 // 活动故事 - 补票
  REASON_ACTIVITY_STORY_FIGHT = 215;                           // 活动故事 - 战斗
  REASON_FLOWER_ASSIST_RECV = 216;                             // 密林 - 协助模式收取友情点
  REASON_DROP_ACTIVITY_EXPIRED = 217;                          // 掉落活动 - 过期回收
  REASON_FRAGMENT_COMPOSE_ALL = 218;                           // 碎片一键合成
  REASON_AUTO_DECOMPOSE = 219;                                 // 资源自动分解
  REASON_RITE_MARK_DECOMPOSE = 220;                            // 永恒印记分解
  REASON_SEASON_LEVEL_UP = 221;                                // 赛季等级 - 升级
  REASON_RECV_SEASON_LEVEL_AWARDS = 222;                       // 赛季等级 - 领取等级奖励
  REASON_RECV_SEASON_LEVEL_TASK_AWARDS = 223;                  // 赛季等级 - 领取任务奖励
  REASON_RECV_SEASON_DUNGEON_AWARD = 224;                      // 塞自己主线 - 领取通关奖励
  REASON_DISORDER_LAND_BUY_STAMINA = 225;                      // 失序空间 - 购买体力
  REASON_DISORDER_LAND_BOX = 226;                              // 失序空间 - 宝箱奖励
  REASON_DISORDER_LAND_BATTLE = 227;                           // 失序空间 - 战斗事件获得和消耗
  REASON_ACTIVITY_RETURN_TAKE_LOGIN_AWARDS = 228;              // 回流 - 领取登录奖励
  REASON_PEAK_GUESS = 229;                                     // 巅峰竞技场 - 竞猜
  REASON_PEAK_WORSHIP = 230;                                   // 巅峰竞技场 - 膜拜
  REASON_PRE_SEASON_RECV = 231;                                // 赛季前 - 领奖
  REASON_PEAK_RECV_INVITE_AWARDS = 232;                        // 巅峰竞技场 - 领取邀请奖励
  REASON_RITE_TAKE_RARE_AWARDS = 233;                          // 永恒仪式 - 领取品质奖励
  REASON_MAZE_SWEEP_AWARDS = 234;                              // 迷宫 - 扫荡奖励
  REASON_SEASON_DUNGEON_RECV_AWARDS = 235;                     // 赛季主线 - 领取礼物
  REASON_RECHARGE_COUPON = 236;                                // 充值 - 充值代金券
  REASON_WEB_RECHARGE_COUPON = 237;                            // 充值 - 官网充值代金券
  REASON_RECHARGE_BY_COUPON = 238;                             // 充值 - 代金券支付
  REASON_RECHARGE_REFUND = 239;                                // 充值 - 退款
  REASON_DISORDER_LAND_STAMINA_OR_KEY = 240;                   // 失序空间 - 关卡资源恢复
  REASON_MIRAGE_BUY_COUNT = 241;                               // 个人 boss - 道具购买次数
  REASON_HERO_AWAKEN = 242;                                    // 英雄 - 三系英雄觉醒
  REASON_SEASON_RETURN_TAKE_AWARDS = 243;                      // 赛季回流 - 领取奖励
  REASON_EMBLEM_UPGRADE = 244;                                 // 符文升阶消耗
  REASON_RITE_RECYCLE = 245;                                   // 永恒仪式 - 阵法回收
  REASON_SEASON_LINK_ACTIVATE = 246;                           // 赛季羁绊 - 激活羁绊
  REASON_GST_TASK_REWARD = 247;                                // 公会战 - 任务领奖
  REASON_GST_DONATE_COST = 248;                                // 公会战 - 捐赠消耗
  REASON_TIME_LIMIT_ITEM = 249;                                // 限时道具删除
  REASON_GUIDANCE_SKIP = 250;                                  // 新手引导 - 跳过一部分
  REASON_SEASON_RESET_DEL_LEVEL_TOKEN = 251;                   // 赛季重置-删除赛季等级资源
  REASON_STORY_REVIEW_UNLOCK = 252;                            // 剧情回忆录 - 解锁剧情
  REASON_EMBLEM_SUCCINCT = 253;                                // 符文 - 洗炼消耗
  REASON_EMBLEM_SUCCINCT_ITEM_CONFLATE = 254;                  // 符文 - 洗炼道具合成
  REASON_QUIT_GUILD = 255;                                     // 退出公会
  REASON_EMBLEM_SUCCINCT_LOCK = 256;                           // 符文 - 洗炼锁定
  REASON_DISORDER_LAND_SWEEP = 257;                            // 失序空间 - 扫荡事件获得和消耗
  REASON_NEW_YEAR_LOGIN_ACTIVITY = 258;                        // 新年活动
  REASON_PYRAMID_DRAW = 259;                                   // 金字塔活动 - 抽奖
  REASON_PYRAMID_TASK_RECEIVE_AWARD = 260;                     // 金字塔活动 - 领取任务奖励
  REASON_SEASON_LINK_TAKE_RARE_AWARDS = 261;                   // 赛季羁绊 - 领取丰碑品质奖励
  REASON_ARTIFACT_FRAGMENT_RECYCLE_COST = 262;                 // 神器碎片回收 - 回收的碎片
  REASON_ARTIFACT_FRAGMENT_RECYCLE_AWARD = 263;                // 神器碎片回收 - 兑换的资源
  REASON_DISORDER_LAND_RELIC_AWARD = 264;                      // 失序空间 - 遗物奖励
  REASON_DISORDER_LAND_RELIC_CLEAN = 265;                      // 失序空间 - 赛季结束，清空遗物
  REASON_GUILD_BUILD_DONATE_COST = 266;                        // 公会建筑 - 捐赠消耗
  REASON_GST_GET_HANG_UP_RECV = 267;                           // 公会战 - 领取宝箱奖励
  REASON_GST_BUILD_SEASON_RESET = 268;                         // 公会战 - 赛季重置
  REASON_PROMOTION_GIFT = 269;                                 // 新推送礼包
  REASON_HERO_CONVERT = 270;                                   // 英雄 - 溢出英雄转换觉醒道具
  REASON_GST_GROUP_RANK_LIKE = 271;                            // 公会战-组内玩家排行榜点赞
  REASON_GUILD_MEDAL_LIKE = 272;                               //   公会功勋送花
  REASON_GUILD_MEDAL_DISORDER_LAND_ADD = 273;                  //  公会功勋-失序空间加成
  REASON_GUILD_MEDAL_LIKE_TO_ITEM = 274;                       //   公会功勋送花转道具
  REASON_PROMOTION_FREE_GIFT = 275;                            // 新推送礼包-领取免费礼包
  REASON_SEASON_ARENA_DIVISION_AWARD = 276;                    // 赛季竞技场 - 领取排名奖励
  REASON_SEASON_ARENA_TASK_AWARD = 277;                        // 赛季竞技场 - 领取任务奖励
  REASON_SEASON_ARENA_BUY_CHALLENGE_COUNT = 278;               // 赛季竞技场 - 购买挑战次数
  REASON_SEASON_ARENA_REFRESH_OPPONENT = 279;                  // 赛季竞技场 - 刷新对手
  REASON_SEASON_ARENA_CHALLENGE = 280;                         // 赛季竞技场 - 挑战
  REASON_ARENA_VOTE = 281;                                     // 公会战擂台投票
  REASON_SEASON_ARENA_USE_TICKET = 282;                        // 赛季竞技场 - 使用门票
  REASON_PYRAMID_RESET = 283;                                  // 金字塔活动 - 赛季重置清空抽奖道具
  REASON_DROP_ACTIVITY_DAILY_AWARD = 284;                      // 掉落活动 - 每日奖励
  REASON_REMAIN_BOOK_LEVEL_UP = 285;                           // 遗物图鉴 - 升级
  REASON_GST_BOSS_CHALLENGE_RECOVER = 286;                     // 公会boss战 - 挑战令恢复
  REASON_GST_BOSS_USE_CHALLENGE = 287;                         // 公会boss战 - 使用挑战令
  REASON_GST_BOSS_FAIL_BACK_CHALLENGE = 288;                   // 公会boss战 - 失败返回挑战令
  REASON_GST_BOSS_MEMBER_AWARD = 289;                          // 公会boss战 - 成员领奖
  REASON_GST_BOSS_BUY_CHALLENGE = 290;                         // 公会boss战 - 购买挑战令
  REASON_GST_BOSS_INIT_CHALLENGE = 291;                        // 公会boss战 - 初始发放挑战令
  REASON_GST_BOSS_CHALLENGE_AWARD = 292;                       // 公会boss战 - 挑战奖励
  REASON_BOX_OPEN = 293;                                       // 开箱子行为
  REASON_BOX_EXCHANGE = 294;                                   // 开箱子积分兑换
  REASON_ACTIVITY_RECHARGE_PRIVILEGED = 295;                   // 充值活动特权
  REASON_SHOP_RESET = 296;                                     // 商店重置
  REASON_ACTIVITY_TURN_TABLE_TASK_AWARD = 297;                 // 周年庆任务奖励领取
  REASON_ACTIVITY_TURN_TABLE_SUMMON = 298;                     // 周年庆转盘抽奖
  REASON_ACTIVITY_TURN_TABLE_BUY_TICKET = 299;                 // 周年庆购买门票
  REASON_ACTIVITY_TURN_TABLE_RECYCLE_TICKET = 300;             // 周年庆门票回收
  REASON_ACTIVITY_TURN_TABLE_LOGIN_AWARD = 301;                // 周年庆登录奖励领取
  REASON_GST_DRAGON_DIAMOND_BUY_CHALLENGE_COUNT = 302;         // 公会战龙战 - 钻石购买挑战次数
  REASON_GST_DRAGON_DIAMOND_BUY_FAILED = 303;                  // 公会战龙战 - 钻石购买失败退还
  REASON_GST_DRAGON_ITEM_BUY_CHALLENGE_COUNT = 304;            // 公会战龙战 - 道具购买挑战次数
  REASON_GST_DRAGON_ITEM_BUY_FAILED = 305;                     // 公会战龙战 - 道具购买失败退还
  REASON_GST_DRAGON_CHALLENGE_AWARD = 306;                     // 公会战龙战 - 挑战奖励
  REASON_GUILD_MOBILIZATION_SCORE_LEVELS = 307;                // 公会竞赛 - 积分等级
  REASON_GUILD_MOBILIZATION_BUY_TIMES = 308;                   // 公会竞赛 - 购买次数
  REASON_ACTIVITY_COMPLIANCE_RECV_AWARD = 309;                 // 冲榜活动 - 活动奖励
  REASON_GODDESS_CONTRACT_RECOVERY = 310;                      // 契约之所 - 治疗完成奖励
  REASON_BOSS_RUSH_ATTACK_BOSS = 311;                          // Boss挑战 - 攻打Boss
  REASON_BOSS_RUSH_STAMINA_INIT = 312;                         // Boss挑战 - 体力初始化
  REASON_BOSS_RUSH_STAMINA_RECOVER = 313;                      // Boss挑战 - 体力恢复
  REASON_BOSS_RUSH_STAMINA_BUY = 314;                          // Boss挑战 - 体力购买
  REASON_BOSS_RUSH_TASK_AWARD = 315;                           // Boss挑战 - 任务领奖
  REASON_TALENT_TREE_LEVEL_UP = 316;                           // 天赋树 - 升级
  REASON_TALENT_TREE_RESET = 317;                              // 天赋树 - 重置
  REASON_TALENT_TREE_RECV_TASK_AWARDS = 318;                   // 天赋树 - 领取任务奖励
  REASON_TALENT_TREE_SEASON_RESET = 319;                       // 天赋树 - 赛季重置
  REASON_ACTIVITY_PUZZLE_CELL = 320;                           // 活动激活拼图
  REASON_ACTIVITY_PUZZLE_AWARD = 321;                          // 活动拼图领奖
  REASON_ACTIVITY_SUM_TASK_AWARD = 322;                        // 任务奖励
  REASON_ACTIVITY_SUM_LOGIN_AWARD = 323;                       // 登录奖励
  REASON_ACTIVITY_PUZZLE_RECYCLE_TICKET = 324;                 // 回收门票
  REASON_ACTIVITY_SUM_BUY_TICKET = 325;                        // 购买门票
  REASON_ACTIVITY_PUZZLE_INIT = 326;                           // 发放互动初始门票
  REASON_ACTIVITY_SUM_EXCHANGE = 327;                          // 交换商店道具
  REASON_ACTIVITY_COMPLIANCE_LIKE = 328;                       // 冲榜活动 - 点赞排行榜
  REASON_ASSISTANCE_ACTIVITY = 329;                            // 赛季助力 - 领取奖励
  REASON_ACTIVITY_SUM_DROP_EXPIRED = 330;                      // 活动聚合 - 掉落活动结束
  REASON_GUILD_MOBILIZATION_USER_SCORE_AWARD = 331;            // 公会竞赛 - 个人积分奖励
  REASON_GST_ORE_FIGHT_AWARD = 332;                            // 占矿战斗
  REASON_GST_ORE_BUY_FIGHT_TIMES = 333;                        // 占矿购买挑战次数
  REASON_GST_ORE_BUY_ASSIST_TIMES = 334;                       // 占矿购买协助次数
  REASON_GST_ORE_SEASON_RESET = 335;                           // 赛季重置
  REASON_GST_TECH_DONATE = 336;                                // 科技捐献
  REASON_GST_TECH_TASK = 337;                                  // 科技任务
  REASON_GST_TECH_LEVEL_UP = 338;                              // 科技升级
  REASON_SEASON_TALENT_AWARD_RESET_ITEM = 339;                 // 赛季天赋 - 重置天赋物品
  REASON_ACTIVITY_SUM_FEED = 340;                              // 活动聚合 - 喂养
  REASON_ACTIVITY_SUM_MAKE_GIFT = 341;                         // 活动聚合 - 制作礼物
  REASON_SELECT_SUMMON_AWARD = 342;                            // 选择抽卡
  REASON_ACTIVITY_LIFELONG_GIFT = 343;                         // 终身限购礼包
  REASON_ACTIVITY_TOWER_LIKE = 344;                            // 地宫冲榜 - 点赞排行榜
  REASON_ACTIVITY_MIRAGE_LIKE = 345;                           // 幻境冲榜 - 点赞排行榜
  REASON_RANK_ACTIVITY_TOWER_SEASON_LIKE_AWARD = 346;          // 百塔冲榜 - 每日点赞奖励
  REASON_GST_CHALLENGE_TASK = 347;                             // GST新擂台赛任务
  REASON_GST_CHALLENGE_BOX_REWARD = 348;                       // GST新擂台赛宝箱奖励
  REASON_SEASON_COMPLIANCE_STAGE_REWARD = 349;                 // 赛季冲榜
  REASON_SEASON_DOOR_FIGHT_LINE = 350;                         // 赛季开门 - 战斗队列
  REASON_SEASON_DOOR_TASK_REWARD = 351;                        // 赛季开门 - 任务奖励
  REASON_SEASON_DOOR_FIGHT_LINE_REWARD = 352;                  // 赛季开门 - 战斗队列奖励
  REASON_SEASON_DOOR_SEASON_RESET = 353;                       // 赛季开门 - 赛季重置
  REASON_SEASON_JEWELRY_SKILL_LEVEL_UP = 354;                  // 赛季装备 - 词条升级
  REASON_SEASON_JEWELRY_SKILL_CLASS_UP = 355;                  // 赛季装备 - 词条升阶
  REASON_SEASON_JEWELRY_SKILL_CHANGE = 356;                    // 赛季装备 - 词条洗练
  REASON_SEASON_JEWELRY_SEASON_RESET = 357;                    // 赛季装备 - 赛季重置
  REASON_SEASON_JEWELRY_DECOMPOSE = 358;                       // 赛季装备 - 分解
  REASON_ACTIVITY_SUM_SHOOT = 359;                             // 活动射击活动 - 领奖
  REASON_SEASON_SHOP_BUY = 360;                                // 赛季商店 - 购买
  REASON_CONVERT_HERO_AWAKEN_ITEM = 361;                       // 转换英雄觉醒物品
  REASON_COMPLIANCE_TASKS = 362;                               // 竞赛任务领奖
  REASON_ACTIVITY_COUPON_RECV = 363;                           // 代金券活动领奖
  REASON_DAILY_ATTENDANCE_HERO = 364;                          // 每日登录
  REASON_SEASON_DOOR_USE_OIL = 365;                            // 赛季开门 - 涂油
  REASON_SEASON_MAP_INIT = 366;                                // 赛季地图 - 初始化
  REASON_SEASON_MAP_RECOVER = 367;                             // 赛季地图 - 恢复
  REASON_SEASON_MAP_BUY_STAMINA = 368;                         // 赛季地图 - 购买体力
  REASON_SEASON_MAP_FIGHT_PROGRESS = 369;                      // 赛季地图 - 战斗进度掉落
  REASON_SEASON_MAP_FIGHT = 370;                               // 赛季地图 - 战斗
  REASON_SEASON_MAP_TASK_AWARD = 371;                          // 赛季地图 - 任务领奖
  REASON_SEASON_MAP_BUY_GOODS = 372;                           // 赛季地图 - 购买物品
  REASON_SEASON_MAP_SELL_GOODS = 373;                          // 赛季地图 - 出售物品
  REASON_SEASON_MAP_ERROR_BACK = 374;                          // 赛季地图 - 出错返还
  REASON_SEASON_MAP_MASTER = 375;                              // 赛季地图 - 大师捐赠
  REASON_SEASON_MAP_ALTAR_RESET = 376;                         // 赛季地图 - 重置buff
  REASON_SEASON_MAP_SEASON_RESET = 377;                        // 赛季地图 - 赛季重置
  REASON_SEASON_DOOR_FIGHT_REWARD = 378;                       // 赛季开门 - 战斗奖励
  REASON_POKEMON_ACTIVATE = 379;                               // 宠物 - 激活
  REASON_POKEMON_STAR_UP = 380;                                // 宠物 - 升星
  REASON_POKEMON_POTENTIAL_LEVEL_UP = 381;                     // 宠物 - 潜能升级
  REASON_POKEMON_MASTER_REWARD = 382;                          // 宠物 - 大师等级领奖
  REASON_TOWER_POKEMON_TASK_AWARD = 383;                       // 宠物爬塔 - 任务领奖
  REASON_POKEMON_SUMMON_AWARD = 384;                           // 宠物抽卡 - 抽奖
  REASON_ACTIVITY_SUM_SYNTHESIS_GAME_INIT = 385;               // 活动聚合 - 初始化
  REASON_ACTIVITY_SUM_SYNTHESIS_GAME_USE_TICKET = 386;         // 活动聚合 - 合成小游戏消耗门票
  REASON_ACTIVITY_SUM_SYNTHESIS_GAME_USE_ITEM = 387;           // 活动聚合 - 合成小游戏消耗道具
  REASON_ACTIVITY_SUM_SYNTHESIS_GAME_BUY_ITEM = 388;           // 活动聚合 - 合成小游戏消耗道具
}

message LogHeroSelfData {
  uint64 id = 1;
  uint32 sys_id = 2;
  uint32 level = 3;
  uint32 stage = 4;
  uint32 star = 5;
}

message LogHeroSelfDataStr {
  string id = 1;
  uint32 sys_id = 2;
  uint32 level = 3;
  uint32 stage = 4;
  uint32 star = 5;
}

// 仅log使用的数据结构，在这里定义主要方便JSON化。
// 装备强化信息
message EquipStrength {
  string id = 1;
  uint32 sys_id = 2;
  uint32 pre_level = 3;
  uint32 level = 4;
  string old_score = 5;
  string new_score = 6;
}

message MailMessageForWm {  // 完美需要的邮件日志信息
  uint64 id = 1;            // 邮件唯一id
  uint32 mail_type = 2;     // 邮件类型
  string title = 3;         // 邮件标题
}

message UserSnapshotForWm {  // 完美玩家快照信息
  uint64 exp = 1;
  uint64 guild_id = 2;                    // 公会id
  uint32 recruit_left_hlv = 3;            // 剩余高级招募券
  uint32 crystal_left = 4;                // 剩余先知水晶
  uint32 recruit_left_rgl = 5;            // 剩余普通召唤券
  uint32 pvp_ticket_left = 6;             // 剩余竞技挑战券
  uint32 as_stone_left = 7;               // 剩余升阶石数量
  uint32 ref_stone_left = 8;              // 剩余精炼石数量
  uint32 arena_pt = 9;                    // 竞技场积分
  uint32 arena_challenge = 10;            // 竞技场挑战次数
  string highest_main = 11;               // 已通关的主线剧情最高关卡id
  string highest_guild = 12;              // 已通关的公会副本最高关卡/章节id
  string highest_maze = 13;               // 已通关的迷宫最高关卡ID/等级
  string highest_tower = 14;              // 已通关的爬塔最高关卡ID/层数
  string highest_activity = 15;           // "已通关的活动本最高关卡id（如有限时活动副本则填写对应最高关卡ID，如没有活动则留空/填‘0’）"
  uint64 daily_activity_lev = 16;         // 今日任务进度活跃度
  uint64 weekly_activity_lev = 17;        // 本周任务进度活跃度
  uint32 arena_rank = 18;                 // 竞技场徘行名次
  uint32 tower_rank = 19;                 // 爬塔排行名次
  uint32 medal_level = 20;                // 功勋等级
  string guild_talent_level = 21;         // 公会天赋等级
  uint64 talent_energy = 22;              // 剩余天赋能量
  uint64 contributions = 23;              // 剩余公会贡献币
  uint64 celestial_badge = 24;            // 剩余幻境徽章
  uint64 demonicpetal = 25;               // 剩余恶魔花瓣
  uint64 oracle_coin = 26;                // 剩余先知币
  uint64 soul_crystal = 27;               // 剩余灵魂结晶
  uint32 dispatch_lev = 28;               // 悬赏等级
  uint32 dispatch_lev_task = 29;          // 悬赏等级需要接取的任务
  string crystal_share_attr = 30;         // 水晶共鸣养成数据
  repeated uint64 contract_heroes = 31;   // 缔约英雄ID
  repeated uint64 resonance_heroes = 32;  // 共鸣英雄ID
  uint32 wrestle_level = 33;              // 神树争霸赛场等级
  uint32 tower_season_floor = 34;         // 百塔进度
  string mirage_ids = 35;                 // 个人Boss进度
  string trial_level = 36;                // 材料本进度
  uint32 tower_star_id = 37;              // 条件爬塔ID
  int64 left_coupon = 38;                 // 剩余代金券数量
  uint64 total_recharge_coupon = 39;      // 累计充值代金券数量
  uint32 guild_donate_cnt = 40;           // 公会捐献次数
  uint32 guild_activity = 44;             // 7日活跃度
  uint32 guild_grade = 45;                // 公会中的职位
  uint32 season_dungeon_id = 46;          // 赛季主线关卡Id
  string disorderL_pass_max_level = 47;   // 失序空间- 关卡事件最大等级 key: 关卡类型
  string rune_info = 48;                  //  赛季羁绊丰碑养成数据
  uint32 base_id = 49;                    //  玩家头像及头像框Id
  string season_link_active = 50;         //  赛季羁绊激活数据
  string guild_sand_table_resource = 51;  // 公会建筑资源
  string season_arena_info = 52;          // 赛季竞技场信息
}

message MirageSnapshot {
  map<uint32, uint32> id = 1;  // 当前进度ID
}

message TrialSnapshot {
  map<uint32, uint32> level = 1;  // 当前进度level
}

message GuildTalentSnapshot {
  map<uint32, uint32> level = 1;  // key:job职业  value：level 等级
}

message DisorderLandSnapshot {
  map<uint32, uint32> max_level = 1;  // key:难度   value: level 等级
}

message SeasonLinkActive {
  uint32 link_id = 1;
  repeated uint32 hero_ids = 2;
}

message HeroSnapshotForWm {
  FType1 f_type_1 = 1;               // 英雄基本信息
  repeated FType2 f_type_2 = 2;      // 英雄所穿装备的信息
  repeated FType3And4 f_type_3 = 3;  // 英雄技能信息
  repeated FType3And4 f_type_4 = 4;  // 英雄纹章和宝石信息
}

message HeroSnapshot {
  string id = 1;
  uint32 sys_id = 2;
  string power = 3;        // 战力
  uint32 rare = 4;         // 稀有度
  uint32 level = 5;        // 等级
  uint32 stage = 6;        // 突破等级
  uint32 star = 7;         // 星级
  uint32 level_limit = 8;  // 英雄等级上限
  uint32 gem1_level = 9;   // 1号位宝石等级
  uint32 gem2_level = 10;  // 2号位宝石等级
}

message EquipSnapshot {
  uint32 sys_id = 1;         // 装备系统id
  string id = 2;             // 装备唯一id
  uint32 level = 3;          // 装备等级
  uint32 refine_level = 4;   // 装备精炼等级
  uint32 rare = 5;           // 装备稀有度
  uint32 level_limit = 6;    // 装备等级上限
  uint32 enchant_level = 7;  // 装备附魔等级
  uint32 evolution = 8;      // 装备进阶等级
  string score = 9;          // 装备分数
  uint32 part = 10;          // 装备位置
}

message HeroSkillSnapshot {
  uint32 id = 1;     // 技能ID
  uint32 level = 2;  // 技能等级
}

message EmblemSnapshot {
  uint32 sys_id = 1;  // 系统ID
  string id = 2;      // 唯一ID
  uint32 level = 3;   // 等级
  string score = 4;   // 评分
}

message FType1 {           // 英雄快照的基本信息forWm
  uint64 power = 1;        // 战力
  uint32 rare = 2;         // 稀有度
  uint32 level = 3;        // 等级
  uint32 stage = 4;        // 突破等级
  uint32 star = 5;         // 星级
  uint32 level_limit = 6;  // 英雄等级上限
  uint32 gem1_level = 7;   // 1号位宝石等级
  uint32 gem2_level = 8;   // 2号位宝石等级
}

message FType2 {
  uint32 sysId = 1;          // 装备系统id
  uint64 id = 2;             // 装备唯一id
  uint32 level = 3;          // 装备等级
  uint32 refine_level = 4;   // 装备精炼等级
  uint32 rare = 5;           // 装备稀有度
  uint32 level_limit = 6;    // 装备等级上限
  uint32 enchant_level = 7;  // 装备附魔等级
  uint32 evolution = 8;      // 装备进阶等级
  int64 score = 9;           // 装备分数
}

message FType3And4 {
  uint32 id = 1;
  uint32 level = 2;
  uint32 extra_lv = 3;  // 其它养成等级。（纹章表示祝福等级）
  int64 score = 4;      // 养成分数
}

message BagSnapshotForWm {
  repeated CharDataBag items = 1;
  repeated CharDataBag fragments = 2;
  repeated CharDataBag artifactFragments = 3;  // 神器碎片
  repeated CharDataBag equip = 4;              // 装备
  repeated CharDataBag emblem = 5;             // 纹章
  repeated CharDataBag gem = 6;                // 宝石
  repeated CharDataBag remain_fragments = 7;   // 遗物碎片
}

message CharDataBag {  // 完美需要的日志数据
  uint32 type = 1;     // 类型
  uint32 id = 2;       // id
  uint32 count = 3;    // 数量
  uint32 lev = 4;      // 背包物品的养成等级
  uint32 raity = 5;    // 稀有度
}

message ArtifactSnapshotForWm {  // 神器快照的基本信息forWm
  uint32 id = 1;                 // 神器id
  uint32 level = 2;              // 神器强化等级
  uint32 stage = 3;              // 神器铸造等级
  uint32 star = 4;               // 神器星级
  int64 score = 5;               // 神器评分
}

message FormationSnapshotForWm {  // 阵容快照的基本信息forWm
  cl.Formation formation = 1;
}

message Hero {
  string id = 1;             // 英雄唯一id
  uint32 sys_id = 2;         // 英雄配置hero_info.xml表id
  uint32 star = 3;           // 星级
  uint32 level = 4;          // 等级
  uint32 stage = 5;          // 突破等级
  uint32 pos = 6;            // 位置
  string power = 7;          // 战力
  uint32 gem1_level = 8;     // 1号位宝石等级
  uint32 gem2_level = 9;     // 2号位宝石等级
  uint32 awaken_level = 10;  // 觉醒等级
  uint32 skin_id = 11;       // 穿戴的皮肤ID
}

message HeroGemAndEmblemAddition {
  string hid = 1;                      // 英雄唯一id
  uint32 pos = 2;                      // 上阵位置
  bool gem_link = 3;                   // 二羁绊是否激活
  bool gem_pas_skill_1 = 4;            // 宝石被动技能1是否激活
  bool gem_pas_skill_2 = 5;            // 宝石被动技能2是否激活
  bool emblem_link = 6;                // 三羁绊是否激活
  bool emblem_pas_skill_1 = 7;         // 符文被动技能1是否激活
  bool emblem_pas_skill_2 = 8;         // 符文被动技能2是否激活
  bool emblem_pas_skill_3 = 9;         // 符文被动技能3是否激活
  uint32 emblem_suit_num = 10;         // 纹章激活套装
  uint32 emblem_exclusive_num = 11;    // 专属纹章个数
  bool emblem_exclusive_skill_1 = 12;  // 符文专属技能1是否激活
  bool emblem_exclusive_skill_2 = 13;  // 符文专属技能2是否激活
  bool emblem_exclusive_skill_3 = 14;  // 符文专属技能3是否激活
}

// cl.FormationInfo字段转string
message FormationInfoStr {
  uint32 pos = 1;
  string hid = 2;  // 英雄id
  string uid = 3;  // 英雄所属玩家id，0代表自己的
                   //  uint64 hp = 4;//当前血量, 待定字段，暂时不生效
}

message Emblem {
  uint32 sys_id = 1;       // 文章的系统id
  string hid = 2;          // 英雄唯一id
  uint32 hero_sys_id = 3;  // 英雄的系统id
}

message Artifact {
  uint32 sys_id = 1;  // 神器的系统id
  uint32 pos = 2;     // 位置
}

message Link {
  uint32 sys_id = 1;  // 羁绊id
  uint32 num = 2;     // 羁绊人数
}

message Formation {
  repeated Team teams = 1;
}

message Team {
  repeated Hero heroes = 1;
  repeated Emblem emblems = 2;
  repeated Artifact artifacts = 3;
  repeated Link links = 4;
  repeated HeroGemAndEmblemAddition addition = 5;
  bt.Rite rite = 6;
  int64 power = 7;
  repeated uint32 remains = 8;
}

message ArenaDivisionTaskAward {
  uint32 task_id = 1;     // 任务id
  uint32 award_type = 2;  // 奖励类型 1-成就和赛季 2-赛季 3-成就
}

message CultivateScoreStr {
  string id = 1;
  uint32 sys_id = 2;
  string old_score = 3;
  string new_score = 4;
}

// cl.Attr字段转string
message AttrStr {
  uint32 type = 1;
  string value = 2;
}

// cl.Resource字段转string
message ResourceStr {
  string id = 1;
  uint32 type = 2;
  uint32 value = 3;
  uint32 count = 4;
  repeated AttrStr attrs = 5;
  string total_count = 6;  // 后端日志用，记录此资源拥有数量
}

message TokenResourceStr {
  uint32 type = 1;
  uint32 value = 2;
  string count = 3;
}

// cl.Equipment字段转string
message EquipmentStr {
  string id = 1;                           // 装备id
  uint32 sys_id = 2;                       // 装备配置equip_info.xml表id
  uint32 strength_level = 3;               // 强化等级
  uint32 refine_exp = 4;                   // 精炼进度
  uint32 refine_level = 5;                 // 精炼等级
  uint32 enchant_level = 6;                // 附魔等级
  uint32 evolution_level = 7;              // 进阶等级
  string hid = 8;                          // 英雄id
  string score = 9;                        // 评分
  uint32 enchant_seed = 10;                // 附魔种子ID
  repeated ResourceStr enchant_cost = 11;  // 附魔消耗
}

// 神器首发 - 抽卡
message ArtifactDebutSummon {
  uint32 category = 1;           // 分类 1-高级 2-初级
  uint32 count = 2;              // 本次抽卡次数
  uint32 senior_draw_count = 3;  // 高级抽已抽次数
  uint32 exchange_count = 4;     // 积分兑换次数
  uint32 left_points = 5;        // 剩余积分
  uint64 finish_groups = 6;      // 已抽尽的大组id
  uint32 current_group = 7;      // 当前所在大组id
  uint64 finish_ids = 8;         // 已抽尽的小组id
}

message OperateActivityTask {
  uint32 task_type = 1;
  uint32 progress = 2;
}

message TrialOnHook {
  uint32 trial_type = 1;
  uint32 num = 2;
  uint32 trial_level = 3;
  int64 on_hook_time = 4;
}

message HeroSysId {
  uint32 hero_sys_id = 1;
}

message GuildSnapshot {  // 公会快照信息
  uint64 id = 1;
  string name = 2;                     // 公会名称
  uint32 level = 3;                    // 等级
  uint32 member_cnt = 4;               // 成员数量
  uint32 chapter = 5;                  // 章节
  repeated uint32 boss_ids = 6;        // 当前公会副本bossID
  repeated uint32 boss_hp_pct = 7;     // 当前公会副本boss剩余hp
  uint32 season_id = 8;                // 赛季ID
  uint32 round = 9;                    // 轮次
  uint32 arena = 10;                   // 战区ID
  uint32 division = 11;                // 段位
  uint32 star = 12;                    // 积分
  uint32 activity_points = 13;         // 活跃度
  uint64 leader_id = 14;               // 会长ID
  string leader_name = 15;             // 会长名称
  uint32 donate_point = 16;            // 捐献进度
  repeated uint64 member_ids = 17;     // 成员列表
  string language = 18;                // 语言
  uint32 join_type = 19;               // 加入类型
  uint32 label = 20;                   // 标签
  uint32 level_limit = 21;             // 等级限制
  int64 power_limit = 22;              // 战力限制
  uint32 badge_icon = 23;              // 徽章-图案
  uint32 badge_background = 24;        //  徽章-背景
  string notice = 25;                  // 公告
  string declaration = 26;             // 宣言
  uint32 settlement_division = 27;     // 结算时的段位
  repeated LogGuildMedal medals = 28;  // 勋章信息
}

message GuildDungeonBossSnapshot {
  map<uint32, string> boss_hp = 1;  // boss剩余血量
}

message WorldBossRankSnapshot {  // 公会快照信息
  uint32 rank_type = 1;          // 排行类型
  uint32 rank = 2;               // 排行
  uint64 damage = 3;             // 伤害
  uint32 term = 4;               // 期数
  uint32 mode = 5;               // 难度
  uint32 arena = 6;              // 战区ID
}

// 公会文本内容设置
enum GUILD_LOG_TEXT_INFO {
  GLTI_MODIFY_INFO = 0;    // 公会基本信息设置
  GLTI_SET_NAME = 1;       // 修改公会名
  GLTI_MODIFY_NOTICE = 2;  // 修改公告
}

message GuildLogText {
  uint64 gid = 1;
  string name = 2;
  int64 create_tm = 3;
  string language = 4;
  uint32 join_type = 5;
  uint32 label = 6;
  uint32 lv_limit = 7;
  int64 power_limit = 8;
  GUILD_LOG_TEXT_INFO type = 9;
  string content = 10;
  uint32 icon = 11;
  uint32 background = 12;
}

// 公会玩家申请
enum GUILD_LOG_USER {
  GLU_JOIN = 0;          // 加入
  GLU_APPLY = 1;         // 申请
  GLU_CANCEL_APPLY = 2;  // 取消申请
  GLU_QUICK_JOIN = 3;    // 快速加入
  GLU_QUIT = 4;          // 退出
  GLU_BE_KICK = 5;       // 被踢
  GLU_BE_APPROVED = 6;   // 被批准加入公会
  GLU_COMBINE_JOIN = 7;  // 合并公会加入
  GLU_COMBINE_KICK = 8;  // 合并公会踢出非活跃玩家
}

message GuildUserJoinOrQuit {
  uint64 gid = 1;
  string name = 2;
  int64 create_tm = 3;
  uint64 old_gid = 4;
  GUILD_LOG_USER type = 5;
}

// 公会管理
enum GUILD_LOG_MANAGE {
  GLM_ACCEPT = 0;   // 同意申请
  GLM_REFUSE = 1;   // 拒绝申请
  GLM_RECRUIT = 2;  // 招募成员
  GLM_KICK = 3;     // 踢人
  GLM_DISBAND = 4;  // 解散
  GLM_CREATE = 5;   // 创建
}

message GuildManage {
  uint64 gid = 1;
  string name = 2;
  int64 create_tm = 3;
  GUILD_LOG_MANAGE type = 4;
  repeated uint64 member_id = 5;
}

message GuildPosition {
  uint64 gid = 1;
  string name = 2;
  int64 create_tm = 3;
  uint64 manager_id = 4;
  uint32 manager_position = 5;
  uint64 member_id = 6;
  uint32 member_old_position = 7;
  uint32 member_new_position = 8;
}

message PeakRankSnapshot {   // 巅峰竞技场排行快照
  uint32 arena = 1;          // 战区ID
  uint32 rank = 2;           // 排行
  uint32 phase = 3;          // 期数
  uint32 score = 4;          // 积分
  uint32 current_rank = 5;   // 当前小周期排名
  uint32 current_score = 6;  // 当前小周期积分
}

message SeasonLinkRune {
  uint32 position = 1;
  uint32 sys_id = 2;
  uint32 unique_level = 3;
}

message GstGuildSnapshot {  // GST 公会快照信息
  uint64 id = 1;
  string name = 2;            // 公会名称
  uint32 arena = 3;           // 战区ID
  uint32 round = 4;           // 轮次
  uint32 l_round = 5;         // 回合
  uint64 room = 6;            // 房间
  uint32 score = 7;           // 地块积分
  uint32 map_id = 8;          // 地图id
  uint32 division_score = 9;  // 段位积分
  uint32 ground_num = 10;     // 地块数量
  uint32 guild_rank = 11;     // 公会排名
  uint32 arena_round = 12;
  uint32 arena_rank = 13;
  uint32 arena_wins = 14;
  uint32 gst_division = 15;
  GSTGoddessBlessInfo bless_info = 16;
  GSTGroundInfo ground_info = 17;
  GSTGuildUsersScore users_score = 18;
  repeated LogGSTBuild build = 19;
  repeated uint32 first_occupy = 20;
  repeated LogGSTArenaRank area_ranks = 21;
}

message GSTGoddessBlessInfo {
  repeated cl.GSTGoddessBless bless_info = 1;  // 捐献信息
}

message GSTGroundInfo {
  map<uint32, uint32> ground_type_count = 1;  // 地块类型数量
}

message GSTGuildUsersScore {
  repeated GSTGuildUserScore users_score = 1;  // 成员贡献值
}

message GSTGuildUserScore {
  uint64 uid = 1;
  uint32 score = 2;
  uint32 build_donate_count = 3;
}

message GSTDispatchTeam {
  uint32 old_position = 1;
  uint32 new_position = 2;
  uint32 team_index = 3;
  string owner_id = 4;
  uint32 space_id = 5;
  uint32 space_type = 6;
  uint32 round = 7;
}

message GSTFightExt {
  uint32 room_quality = 1;
  uint32 arena_round = 2;
}

message GSTReorderTeam {
  string owner_id = 1;
  uint32 space_id = 2;
  uint32 space_type = 3;
  uint32 new_index = 4;
  uint32 old_index = 5;
  uint32 team_index = 6;
}

message GstDefenseInfo {
  uint64 uid = 1;
  uint64 server_id = 2;
  uint64 guild_id = 3;
}

message EmblemSuccinctLog {
  uint64 id = 1;
  uint32 sys_id = 2;
  uint32 hero_sys_id = 3;  // 专属英雄ID
  uint32 tmp_skill_id = 4;
  uint32 tmp_suit_id = 5;
  uint32 tmp_affix_id = 6;
  uint32 affix_type = 7;
  uint32 lock_id = 8;
}

// 玩家羁绊丰碑符石快照
message UserMonumentRuneSnapshot {
  repeated MonumentRune runes = 1;
}

message MonumentRune {
  uint32 monument_id = 1;
  uint32 monument_unique_level = 2;
  uint32 pos = 3;
  uint32 sys_id = 4;
  uint32 level = 5;
}

// 玩家羁绊激活快照
message SeasonLinkActivation {
  repeated uint32 hero_sys_id = 1;
}

message LogBuildDispatchHero {
  uint32 build_id = 1;
  string id = 2;
  uint32 sys_id = 3;
  uint32 star = 4;
}

message LogGSTBuild {
  uint32 build_id = 1;
  uint32 build_level = 2;
  repeated ResourceStr build_process = 3;
}

message LogGstBattleSub {
  uint32 morale = 1;
  uint32 space_id = 2;
  uint32 popular = 3;
}

message LogGuildMedal {
  uint64 uid = 1;
  uint32 medal_id = 2;
  uint32 status = 3;
  uint32 liked_count = 4;
}

message LogGSTVoteDetail {
  uint64 guild_id = 1;
  uint32 count = 2;
  int32 total_count = 3;
  bool is_favour = 4;
}

message LogGSTVote {
  cl.GstLogGeneral base = 1;
  uint32 arena_round = 2;
  uint32 partition = 3;
  uint64 uid = 4;
  LogGSTVoteDetail vote_detail = 5;
}

message LogGSTArenaRanks {
  repeated LogGSTArenaRank ranks = 1;
}

message LogGSTArenaRank {
  uint64 uid = 1;
  uint32 rank = 2;
  uint32 wins = 3;
}

message LogSeasonArena {
  uint32 rank = 1;
  uint32 score = 2;
  uint32 division = 3;
}

message PeakFighterRank {
  uint64 uid = 1;
  uint64 sid = 2;
  PeakRankSnapshot snapshot = 3;
}

message GuildSeasonFinalDivision {
  uint64 gid = 1;
  string name = 2;
  uint32 badge_icon = 3;        // 徽章-图案
  uint32 badge_background = 4;  //  徽章-背景
  uint32 division = 5;          // 段位
  repeated uint64 members = 6;  // 成员
}

message LogGSTDragonSettlement {
  uint64 guild_id = 1;
  bool is_bot = 2;             // 是否机器人公会
  uint32 guild_partition = 3;  // 公会所在小战区
  uint32 guild_grade = 4;      // 公会副本段位
  uint32 dragon_progress = 5;  // 龙战进度
}

// 公会秘技类型
enum GUILD_STRATEGY_TYPE {
  GST_STRATEGY_TYPE_NONE = 0;
  GST_STRATEGY_TYPE_DUNGEON = 1;                 // 公会副本
  GST_STRATEGY_TYPE_DRAGON_ATTACK = 2;           // 龙战攻城
  GST_STRATEGY_TYPE_DRAGON_CHALLENGE_COUNT = 3;  // 龙战加次数
}

message LogGSTDragonGuild {
  uint64 guild_id = 1;
  bool is_bot = 2;             // 是否机器人公会
  uint32 guild_partition = 3;  // 公会所在小战区
  uint32 guild_grade = 4;      // 公会副本段位
}

message LogGSTDragonFight {
  uint32 dragon_id = 1;
  uint32 dragon_level = 2;
  uint32 formation_id = 3;
  uint32 team_num = 4;
  string battle_report_id = 5;
  uint64 total_damage = 6;
  uint32 dragon_reduce_hp = 7;
}

message LogGuildMobScore {
  uint32 partition = 1;
  uint32 current_score = 2;
  uint32 score = 3;
  uint64 guild_id = 4;
  uint32 guild_level = 5;
}

message LogBossRushFight {
  uint32 boss_id = 1;
  uint32 boss_level = 2;
  uint32 formation_id = 3;
  uint32 team_num = 4;
  bool is_sweep = 5;
  string battle_report_id = 6;
  repeated uint64 team_damages = 7;
  repeated uint32 team_progress = 8;
  uint32 boss_total_progress = 9;
  bool kill_boss = 10;
}

// 英雄的符文技能激活情况
message LogHeroEmblemSkill {
  uint64 hid = 1;
  uint32 sys_id = 2;
  bool counter_skill = 3;
  uint32 exclusive_lv = 4;
}

// 失序Boss奖励类型
enum BOSS_RUSH_AWARD_TYPE {
  BOSS_RUSH_AWARD_TYPE_NONE = 0;
  BOSS_RUSH_AWARD_TYPE_DROP = 1;      // 掉落奖励
  BOSS_RUSH_AWARD_TYPE_PROGRESS = 2;  // 进度奖励
}

message LogGSTOreFight {
  uint32 battle_op_type = 1;  // 1:战斗,2:扫荡
  uint32 ore_id = 2;
  uint32 progress = 3;
  string battle_report_id = 4;
  uint32 turn = 5;   // 回合
  uint32 round = 6;  // 轮次
  uint32 ore_level = 7;
  uint32 monster_id = 8;
  uint32 type = 9;  // 1.挑战，2.协助
  uint64 assist_uid = 10;
  uint32 formation_id = 11;
  uint32 single_progress = 12;  // 单次进度
}

message LogOreResourceChange {
  uint32 type = 1;  // 1.捐献，2.产出
  uint32 count = 2;
  string uid_str = 3;
}

message LogGSTSkill {
  uint64 type = 1;  // 1.公会副本，2.龙攻城技能，3.龙战秘技，4.号令集结
  uint64 guild_id = 2;
  uint32 guild_division = 3;
  uint64 use_count = 4;
}

message LogGSTTechTaskAward {
  repeated uint32 task_id = 1;
}

message LogGSTTechLevelUp {
  uint32 node_id = 1;
  uint32 node_level_before = 2;
  uint32 node_level_after = 3;
}

message LogGSTStreak {
  string gst_guild_id = 1;
  uint32 gst_turn = 2;
  uint32 gst_team_index = 3;
  uint32 gst_team_streak = 4;
  uint32 gst_round = 5;
  uint32 gst_battle_zone = 6;
  string gst_room = 7;
  uint32 gst_room_quality = 8;
}

enum LOG_TYPE {
  LOG_TYPE_NONE = 0;
  KAFKA_LOG = 1;
}

enum KAFKA_TOPIC {
  KAFKA_TOPIC_NONE = 0;
  KAFKA_TOPIC_ES = 1;     // 写入到es
  KAFKA_TOPIC_MONGO = 2;  // 写入到mongo
}

enum KAFKA_HANDLER_TYPE {
  KAFKA_HANDLER_TYPE_NONE = 0;
  KAFKA_HANDLER_TYPE_ES = 1;
  KAFKA_HANDLER_TYPE_MONGO = 2;
}

message KafkaLogHandlerData {
  string topic = 1;  // kafka topic
  ESLogHandlerData es_data = 2;
  MongoLogHandlerData mongo_data = 3;
}

enum ES_LOG_INDEX {
  ES_LOG_INDEX_NONE = 0;
  ES_LOG_INDEX_USER_INFO = 1;
  ES_LOG_INDEX_GUILD_INFO = 2;
}

message ESLogHandlerData {
  string index = 1;  // ES_LOG_INDEX的小写
  string id = 2;     // 唯一ID(如果有会进行覆盖,没有则会自动生成)
  string data = 3;   // json数据
}

enum MONGO_LOG_COLL {
  MONGO_LOG_COLL_NONE = 0;
  MONGO_LOG_COLL_USER_INFO = 1;
}

message MongoLogHandlerData {
  string coll_name = 1;  // 表名 MONGO_LOG_COLL的小写
  string id = 2;         // 唯一ID(如果有会进行覆盖,没有则会自动生成)
  string data = 3;       // bson的base64字符串(proto要支持bson标签!)
}

enum LOG_QUERY_TYPE {
  LOG_QUERY_TYPE_NONE = 0;
  LOG_QUERY_TYPE_ES = 1;     // 查询es日志
  LOG_QUERY_TYPE_MONGO = 2;  // 查询mongo日志
}

message LogQueryReq {
  LOG_QUERY_TYPE type = 1;        // 日志查询类型
  EsQueryReq es_query = 2;        // es查询请求
  MongoQueryReq mongo_query = 3;  // mongo查询请求
}

message LogQueryRsp {
  repeated string datas = 1;  // 查询结果
}

message EsQueryReq {
  string index = 1;
  bytes query = 2;  // 查询语句
}

message MongoQueryReq {
  string coll_name = 1;
  bytes filter = 2;
  QueryOptions options = 3;
}

// 排序方向枚举
enum SortDirection {
  ASC = 0;
  DESC = 1;
}

message SortField {
  string field = 1;
  SortDirection direction = 2;
}

message QueryOptions {
  repeated SortField sort = 1;
  // 分页选项
  int64 limit = 2;
  int64 skip = 3;
}

message MongoUserInfo {
  uint64 uid = 1;
  string name = 2;
  uint64 server_id = 3;
}

message ESUserInfo {
  uint64 uid = 1;
  string name = 2;
  uint64 server_id = 3;
}

message ESGuildInfo {
  uint64 guild_id = 1;
  string name = 2;
  uint64 server_id = 3;
}

message LogGSTChallengeTaskAward {
  repeated uint32 task_id = 1;
}

message LogGSTChallengeFight {
  string battle_report_id = 1;
  uint32 battle_zone = 2;
  uint32 round = 3;            // 公会战轮次
  uint32 challenge_round = 4;  // 擂台赛第几场
  uint32 match_round = 5;      // 本场擂台赛第几次匹配
  uint32 team_index = 6;
  string room = 7;
  uint32 room_quality = 8;
  bool win = 9;
  uint32 add_score = 10;
  bool is_bot = 11;
  string defense_uid = 12;
  string defense_server_id = 13;
  string defense_guild_id = 14;
}

message LogGSTChallengeStreak {
  string guild_id = 1;
  uint32 round = 2;            // 公会战轮次
  uint32 challenge_round = 3;  // 擂台赛第几场
  repeated LogGSTChallengeTeam teams = 4;
  uint32 all_team_win = 5;  // 所有队伍胜场
  uint32 battle_zone = 6;
  string room = 7;
  uint32 room_quality = 8;
}

message LogGSTChallengeTeam {
  uint32 team_index = 1;      // 队伍编号
  uint32 team_streak = 2;     // 队伍连胜数
  uint32 team_total_win = 3;  // 队伍总胜场
}

message LogSeasonDoorTaskReward {
  repeated uint32 task_id = 1;
}

message LogSeasonDoorFight {
  repeated uint32 monster_id = 1;
  string battle_report_id = 2;
  uint32 door_type = 3;
  uint32 door_level = 4;
  bool win = 5;
  uint32 fight_time = 6;
  uint32 formation_id = 7;
  uint32 door_num = 8;
}

message LogSeasonJewelryData {
  string id = 1;
  uint32 sys_id = 2;
  string hero_id = 3;
  uint32 hero_sys_id = 4;
  repeated uint32 skill_sys_id = 5;
  repeated uint32 skill_level = 6;
  repeated uint32 skill_change_id = 7;
  uint32 jewelry_level = 8;
}

message LogSeasonJewelryDecompose {
  LogSeasonJewelryData jewelry = 1;
  repeated cl.Resource decompose_rewards = 2;
  repeated cl.Resource return_rewards = 3;
}

enum SEASON_JEWELRY_SKILL_CHANGE_CONFIRM_TYPE {
  SJSCCT_NONE = 0;
  SJSCCT_RESERVE = 1;  // 保留
  SJSCCT_GIVEUP = 2;   // 放弃
}

message LogSeasonMapFight {
  uint32 position = 1;
  uint32 monster_id = 2;
  uint32 formation_id = 3;
  bool is_sweep = 4;
  string battle_report_id = 5;
  repeated uint64 team_damages = 6;
  repeated uint32 team_progress = 7;
  bool kill = 8;
}

message LogTowerPokemonFight {
  uint32 dungeon_id = 1;
  bool win = 2;                 // 是否胜利
  string battle_report_id = 3;  // 战报ID
  uint32 formation_id = 4;
}
