syntax = "proto3";

option go_package = "app/protos/in/account";

// logic <-> account
package account;

service Store {
  rpc Put(Role) returns (Result) {}
  rpc Get(Query) returns (Roles) {}
}

message Result {
  string code = 1;
}

message Query {
  string uuid = 1;
  uint32 op_group = 2;
  uint32 app_id = 3;
  uint64 user_id = 4;
}

//$<ROP redis|map|r:string>
message Role {
  uint64 server_id = 1;  //$<ROP unique >
  uint32 app_id = 2;
  uint32 op_id = 3;
  uint64 user_id = 4;
  uint32 level = 5;
  uint32 vip = 6;
  uint64 create_time = 7;
  uint64 online_time = 8;
  uint64 current_time = 9;
  uint64 offline_time = 10;
  string uuid = 11;
  string name = 12;
  uint32 op_group = 13;
  uint32 event_type = 14;
  string ip = 15;
  uint32 base_id = 16;
}

message Roles {
  repeated Role data = 1;
}
