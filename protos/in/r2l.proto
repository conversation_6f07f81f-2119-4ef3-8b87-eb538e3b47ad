syntax = "proto3";

import "cl.proto";
import "db.proto";

option go_package = "app/protos/in/r2l";

// redis <-> logic
package r2l;

enum RET {
  ERROR = 0;
  OK = 1;
}

enum ID {
  MSG_NONE = 0;
  MSG_MIN = 100000;
  MSG_MAX = 200000;
  MSG_BEGIN = 100001;
  MSG_END = 199999;
  MSG_L2R_Login = 100002;  // service 启动时请求加载数据
  MSG_R2L_Login = 100003;
  MSG_L2R_Create = 100004;
  MSG_R2L_Create = 100005;
  MSG_L2R_SaveUser = 100006;
  MSG_L2R_Load = 100007;
  MSG_R2L_Load = 100008;
  MSG_L2R_LoadIDBackup = 100009;
  MSG_R2L_LoadIDBackup = 100010;
  MSG_L2R_SaveIDBackup = 100011;
  MSG_L2R_Finish = 100012;  // service close时同步状态
  MSG_R2L_Finish = 100013;
  MSG_L2R_OpMails = 100014;
  MSG_L2R_GroupMail = 100015;
  MSG_R2L_GroupMail = 100016;
  MSG_L2R_GetUserSnapshot = 100017;
  MSG_R2L_GetUserSnapshot = 100018;
  MSG_L2R_GetUserBattleData = 100019;
  MSG_R2L_GetUserBattleData = 100020;
  MSG_L2R_SaveSmallRank = 100021;
  MSG_L2R_SaveFriend = 100022;
  MSG_L2R_SaveCommonRank = 100023;
  MSG_L2R_ChangeConfig = 100024;
  MSG_L2R_SaveArena = 100028;
  MSG_L2R_SaveArenaSeason = 100029;
  MSG_L2R_GetArenaLog = 100030;
  MSG_R2L_GetArenaLog = 100031;
  MSG_L2R_SetName = 100032;  // 设置昵称
  MSG_R2L_SetName = 100033;  // 设置昵称
  MSG_L2R_SaveUserBan = 100034;
  MSG_L2R_SaveRankAchieve = 100035;
  MSG_L2R_SaveMazeMap = 100036;
  MSG_L2R_SaveMiragePass = 100037;
  MSG_L2R_SaveUserBattleSnapshot = 100038;
  MSG_R2L_SaveUserBattleSnapshot = 100039;
  MSG_L2R_GetUserBattleSnapshot = 100040;
  MSG_R2L_GetUserBattleSnapshot = 100041;
  MSG_L2R_DeleteUserBattleSnapshot = 100042;
  MSG_L2R_SaveGuild = 100043;
  MSG_L2R_GuildLogUpdateAndGet = 100044;
  MSG_R2L_GuildLogUpdateAndGet = 100045;
  MSG_L2R_LoadGuild = 100051;
  MSG_R2L_LoadGuild = 100052;
  MSG_L2R_GetGuildDungeonLog = 100053;
  MSG_R2L_GetGuildDungeonLog = 100054;
  MSG_L2R_GetGuildLog = 100055;
  MSG_R2L_GetGuildLog = 100056;
  //  MSG_L2R_SaveWorldMsg = 100058;    // 保存世界聊天消息
  //  MSG_L2R_SaveGuildMsg = 100059;    // 保存公会聊天消息
  //  MSG_L2R_SavePrivateMsg = 100060;  // 保存私聊消息
  //  MSG_R2L_SavePrivateMsg = 100061;
  //  MSG_L2R_GetPrivateMsg = 100062;  // 获取私聊消息
  //  MSG_R2L_GetPrivateMsg = 100063;
  //  MSG_L2R_GetGuildMsg = 100064;  // 获取公会聊天消息
  //  MSG_R2L_GetGuildMsg = 100065;
  //  MSG_L2R_GetPrivateMessageNum = 100066;  // 获取私聊离线消息数量
  //  MSG_R2L_GetPrivateMessageNum = 100067;
  MSG_L2R_SaveOrder = 100068;  // 存储充值订单
  //   MSG_L2R_SaveRate = 100069;       // 保存评分数据
  MSG_L2R_SaveMultiLang = 100070;              // 保存多语言缓存
  MSG_L2R_DelInvalidQuestionnaireID = 100071;  // 删除失效的问卷id
  MSG_L2R_OpBanCmds = 100072;                  // 修改协议屏蔽数据
  MSG_L2R_SaveArenaLastSeasonTop = 100073;     // 保存竞技场赛季前三名数据
  MSG_R2L_SaveArenaLastSeasonTop = 100074;
  //  MSG_L2R_SaveSystemMsg = 100075;     // 保存系统聊天消息
  //  MSG_L2R_SaveChatLikeList = 100076;  // 保存聊天点赞列表
  //  MSG_L2R_GetChatLikeList = 100077;   // 获取点赞列表
  //  MSG_R2L_GetChatLikeList = 100078;
  MSG_L2R_GetDBUser = 100079;  // 获取db.User 数据
  MSG_R2L_GetDBUser = 100080;
  MSG_L2R_SaveWrestle = 100081;
  MSG_L2R_GetWrestleLog = 100082;
  MSG_R2L_GetWrestleLog = 100083;
  MSG_L2R_GetGuildDungeonMessageBoardLog = 100084;
  MSG_R2L_GetGuildDungeonMessageBoardLog = 100085;
  MSG_L2R_LoadGuildUserForReset = 100086;
  MSG_R2L_LoadGuildUserForReset = 100087;
  MSG_L2R_SaveGuildDungeon = 100088;
  MSG_L2R_SaveFlower = 100089;
  // MSG_L2R_GetFlowerLog = 100092;
  // MSG_R2L_GetFlowerLog = 100093;
  MSG_L2R_GetFlowerSnatchLog = 100094;
  MSG_R2L_GetFlowerSnatchLog = 100095;
  MSG_L2R_GetFlowerOccupyLog = 100096;
  MSG_R2L_GetFlowerOccupyLog = 100097;
  MSG_L2R_SaveMonthTasksMsg = 100098;
  MSG_L2R_GetGuildDonateLog = 100099;
  MSG_R2L_GetGuildDonateLog = 100100;
  MSG_L2R_GetGuildMemberInfo = 100101;
  MSG_R2L_GetGuildMemberInfo = 100102;
  MSG_L2R_GetGuildUsers = 100103;
  MSG_R2L_GetGuildUsers = 100104;
  MSG_L2R_SaveWorldBossSettle = 100105;        // 保存世界boss结算数据
  MSG_L2R_SaveTowerSeasonReset = 100106;       // 保存百塔结算数据
  MSG_L2R_SaveActivityStoryRankRest = 100107;  // 保存活动故事ID
  MSG_L2R_SavePeak = 100108;
  MSG_L2R_SaveRefundOrder = 100109;           // 保存订单退款状态
  MSG_L2R_DeleteUserGuildMails = 100110;      // 删除玩家公会邮件
  MSG_L2R_SaveGST = 100111;                   // 存储公会战状态
  MSG_L2R_SaveSeasonLinkActivation = 100112;  // 保存赛季羁绊激活数据
  MSG_L2R_DelSeasonLinkActivation = 100113;   // 删除赛季羁绊激活数据
  MSG_L2R_SavePeopleGroupPackage = 100114;    // 保存人群包
  MSG_L2R_SaveSeasonArena = 100115;           // 存储赛季竞技场
  MSG_L2R_GetSeasonArenaLog = 100116;
  MSG_R2L_GetSeasonArenaLog = 100117;
  MSG_L2R_SaveHotRankUserFormation = 100118;              // 存储玩家热度榜数据
  MSG_L2R_SaveLogicHotRankCollectionUpdateFlag = 100119;  // 存储热度榜收集标志
  MSG_L2R_SaveLogicTalentTreeHot = 100120;                // 存储天赋树养成热度
  MSG_L2R_SaveOfflineResource = 100121;                   // 保存离线资源
  MSG_L2R_SaveDuel = 100122;                              // 保存切磋数据
  MSG_L2R_SaveActivityTower = 100123;                     // 存储地宫冲榜数据
  MSG_L2R_GetGstChallengeLog = 100124;
  MSG_R2L_GetGstChallengeLog = 100125;
  MSG_L2R_SaveSeasonComplianceReward = 100126;  // 存储赛季冲榜奖励状态
  MSG_L2R_SaveHotfixCode = 100127;              // 保存热更新代码
  MSG_L2R_SaveTowerPokemonReset = 100128;       // 保存宠物爬塔重置
  MSG_L2R_SaveBalanceArena = 100129;            // 保存赛季竞技场数据

  // GRPC相关消息
  MSG_L2R_GRPC_Login = 110001;
  MSG_R2L_GRPC_Login = 110002;
  MSG_L2R_GRPC_GetUserID = 110003;
  MSG_R2L_GRPC_GetUserID = 110004;
  MSG_L2R_GRPC_ChangeDungeonID = 110005;  // 设置主线关卡ID
  MSG_R2L_GRPC_ChangeDungeonID = 110006;
  MSG_L2R_GRPC_GuidanceClose = 110007;  // 关闭新手引导
  MSG_R2L_GRPC_GuidanceClose = 110008;
  MSG_L2R_GRPC_SetUserLevel = 110009;  // 修改玩家level
  MSG_R2L_GRPC_SetUserLevel = 110010;
  MSG_L2R_GRPC_ClearUserResource = 110011;  // 清除玩家资源
  MSG_R2L_GRPC_ClearUserResource = 110012;
  MSG_L2R_GRPC_ActivityInfo = 110013;  // 活动信息查询
  MSG_R2L_GRPC_ActivityInfo = 110014;
  MSG_L2R_GRPC_ResetDailyNum = 110015;  // 重置各玩法次数回到初始状态
  MSG_R2L_GRPC_ResetDailyNum = 110016;
  MSG_L2R_GRPC_SetMedalLevel = 110017;  // 修改功勋level
  MSG_R2L_GRPC_SetMedalLevel = 110018;
  MSG_L2R_GRPC_ResetMaze = 110019;  // 重置迷宫
  MSG_R2L_GRPC_ResetMaze = 110020;
  MSG_L2R_GRPC_ResetGuildQuitTm = 110021;  // 重置公会退出时间
  MSG_R2L_GRPC_ResetGuildQuitTm = 110022;
  MSG_L2R_GRPC_ResetTowerFloor = 110023;  // 重置tower floor
  MSG_R2L_GRPC_ResetTowerFloor = 110024;
  MSG_L2R_GRPC_ResetTrialLevel = 110025;  // 重置trial level
  MSG_R2L_GRPC_ResetTrialLevel = 110026;
  MSG_L2R_GRPC_ResetMirageFloor = 110027;  // 重置个人Boss floor
  MSG_R2L_GRPC_ResetMirageFloor = 110028;
  MSG_L2R_GRPC_ResetScore = 110029;  // 重置日常、周常、嘉年华积分
  MSG_R2L_GRPC_ResetScore = 110030;
  MSG_L2R_GRPC_ResetTaleChapterFinish = 110031;  // 设置列传章节完成
  MSG_R2L_GRPC_ResetTaleChapterFinish = 110032;
  MSG_L2R_GRPC_ResetTaleElite = 110033;  // 设置列传强敌
  MSG_R2L_GRPC_ResetTaleElite = 110034;
  MSG_L2R_GRPC_SetUserResource = 110035;  // 设置玩家资源
  MSG_R2L_GRPC_SetUserResource = 110036;
  MSG_L2R_GRPC_AddOrder = 110037;  // 充值增加新订单
  MSG_R2L_GRPC_AddOrder = 110038;
  MSG_L2R_GRPC_SetUserVip = 110039;  // 修改玩家vip
  MSG_R2L_GRPC_SetUserVip = 110040;
  MSG_L2R_GRPC_SetTowerstarDungeonID = 110041;  // 修改玩家条件爬塔关卡
  MSG_R2L_GRPC_SetTowerstarDungeonID = 110042;
  MSG_L2R_GRPC_ResetTowerstar = 110043;  // 重置条件爬塔关卡
  MSG_R2L_GRPC_ResetTowerstar = 110044;
  MSG_L2R_GRPC_SetQuestionnaireFinish = 110045;  // 设置已完成问卷
  MSG_R2L_GRPC_SetQuestionnaireFinish = 110046;
  MSG_L2R_GRPC_GetRechargeList = 110047;  // 订单查询
  MSG_R2L_GRPC_GetRechargeList = 110048;
  MSG_L2R_GRPC_CloseGuidance = 110049;  // 关闭新手引导的单个组
  MSG_R2L_GRPC_CloseGuidance = 110050;
  MSG_L2R_GRPC_SetMazeTaskLevel = 110051;  // 设置迷宫任务level
  MSG_R2L_GRPC_SetMazeTaskLevel = 110052;
  MSG_L2R_GRPC_GetUserMailList = 110053;  // 获取玩家邮件列表
  MSG_R2L_GRPC_GetUserMailList = 110054;
  MSG_L2R_GRPC_DeleteUserMail = 110055;  // 删除玩家指定邮件
  MSG_R2L_GRPC_DeleteUserMail = 110056;
  MSG_L2R_GRPC_SetAccountTag = 110057;  // 设置账号标识
  MSG_R2L_GRPC_SetAccountTag = 110058;
  MSG_L2R_GRPC_ImportUser = 110059;  // 导号
  MSG_R2L_GRPC_ImportUser = 110060;
  MSG_L2R_GRPC_SetDispatchLevel = 110061;  // 设置派遣等级
  MSG_R2L_GRPC_SetDispatchLevel = 110062;
  MSG_L2R_GRPC_DelChatGroupTag = 110063;  // 删除聊天groupTag
  MSG_R2L_GRPC_DelChatGroupTag = 110064;
  MSG_L2R_GRPC_SetDailyAttendance = 110065;  // 修改累登天数
  MSG_R2L_GRPC_SetDailyAttendance = 110066;
  MSG_L2R_GRPC_SetRiteRare = 110067;  // 设置仪式品质
  MSG_R2L_GRPC_SetRiteRare = 110068;
  MSG_L2R_GRPC_AddWaitRefundOrder = 110069;  // 新增待退款订单
  MSG_R2L_GRPC_AddWaitRefundOrder = 110070;
  MSG_L2R_GRPC_DeleteCurrencies = 110071;  // 删除玩家货币（钻石、金币、代金券）
  MSG_R2L_GRPC_DeleteCurrencies = 110072;
  MSG_L2R_GRPC_SetDisorderLand = 110073;  // 失序空间 - 完成某个地图
  MSG_R2L_GRPC_SetDisorderLand = 110074;
  MSG_L2R_GRPC_ChangeBags = 110075;  // gm - 删除背包物品
  MSG_R2L_GRPC_ChangeBags = 110076;
  MSG_L2R_GRPC_ChangeUserName = 110077;  // 改个随机名
  MSG_R2L_GRPC_ChangeUserName = 110078;
  MSG_L2R_GRPC_SetSeasonLink = 110080;  // 激活赛季羁绊
  MSG_R2L_GRPC_SetSeasonLink = 110081;
  MSG_L2R_GRPC_SetDailyAttendanceHero = 110082;
  MSG_R2L_GRPC_SetDailyAttendanceHero = 110083;
  MSG_L2R_GRPC_ChangeTowerPokemonDungeonID = 110084;  // 设置宠物爬塔关卡ID
  MSG_R2L_GRPC_ChangeTowerPokemonDungeonID = 110085;
  MSG_L2R_GRPC_SetOfflineVipAward = 110086;  // 设置官网充值领奖记录 越南专用
  MSG_R2L_GRPC_SetOfflineVipAward = 110087;
}

message GmRetUser {
  uint32 code = 1;
  R2L_Login data = 2;
}

message L2R_Login {
  string uuid = 1;
  uint64 server_id = 2;
}

message R2L_Login {
  uint32 ret = 1;
  db.User user = 2;
  map<uint64, cl.Mail> mails = 3;
  map<uint64, cl.HeroBody> heroes = 4;
  map<uint64, cl.Equipment> equips = 5;
  map<uint32, cl.Formation> formations = 6;
  map<uint32, cl.Artifact> artifacts = 8;
  map<uint64, cl.GemInfo> gems = 9;
  map<uint64, cl.EmblemInfo> emblems = 10;
  map<uint32, cl.Mirage> mirages = 11;
  map<uint64, cl.MazePlayer> mazePlayer = 12;
  reserved 13;
  reserved "forest";
  db.GuildUser guild_user = 14;                               // 玩家公会数据
  map<string, db.Order> orders = 15;                          // 充值订单(目前全量加载)
  repeated string wait_process_orders = 16;                   // 待处理的订单集合
  map<uint32, cl.OperateActivity> activities = 17;            // 可配置活动
  repeated uint32 questionnaires = 18;                        // 已完成问卷集合
  map<uint64, cl.HeroStarUpCosts> heroes_star_up_costs = 19;  // 英雄升星消耗
  db.UserBan user_ban = 20;                                   // 封禁信息
  cl.Flower flower = 21;
  map<uint32, cl.Skin> skins = 22;  // 皮肤
  map<uint32, cl.Rite> rites = 23;
  map<uint32, cl.SeasonUserLog> season_user_logs = 24;           // 赛季埋点日志
  map<string, db.WaitRefundOrder> wait_refund_orders = 25;       // 等待处理的退款订单集合
  map<string, db.RefundedOrder> refunded_orders = 26;            // 已退款的订单集合
  repeated uint32 seasonlink_activation = 27;                    // 赛季羁绊已激活英雄
  map<uint32, cl.SeasonLinkMonument> seasonlink_monuments = 28;  // 赛季羁绊丰碑数据
  map<uint32, cl.Remain> remains = 29;                           // 遗物数据
  map<uint32, cl.ActivitySum> activity_sum = 30;                 // 活动集合
  cl.Duel duel = 31;                                             // 切磋数据
  map<uint64, cl.SeasonJewelry> season_jewelry = 32;             // 赛季装备
  map<uint32, cl.Title> titles = 33;                             // 称号
  map<uint32, cl.Pokemon> pokemons = 34;                         // 宠物
}

message L2R_Create {
  string name = 1;
  string uuid = 2;
  uint64 server_id = 3;
  uint32 base_id = 4;
  uint32 career = 5;
  uint32 level = 6;
  uint64 id = 7;
  repeated uint32 face = 8;  // 捏脸
  uint32 op_id = 9;          // 渠道
  uint32 channel = 10;       // 二级渠道
  string ip = 11;
  string device_id = 12;
  uint32 pid = 13;  // 37 pid
  uint32 gid = 14;  // 37 gid
}

message R2L_Create {
  uint32 ret = 1;
  db.User user = 2;
  string ip = 3;
  string device_id = 4;
}

message OpMails {
  repeated cl.Mail changes = 1;
  repeated uint64 deletes = 2;
}

message OpHeroes {
  repeated cl.HeroBody changes = 1;
  repeated uint64 deletes = 2;
}

message OpEquips {
  repeated cl.Equipment changes = 1;
  repeated uint64 deletes = 2;
}

message OpFormations {
  repeated cl.Formation changes = 1;
  repeated uint32 deletes = 2;
}

message OpArtifacts {
  repeated cl.Artifact changes = 1;
  repeated uint32 deletes = 2;
}

message OpGems {
  repeated cl.GemInfo changes = 1;
  repeated uint64 deletes = 2;
}

message OpEmblems {
  repeated cl.EmblemInfo changes = 1;
  repeated uint64 deletes = 2;
}

message OpMirages {
  repeated cl.Mirage changes = 1;
}

message OpMazePlayers {
  repeated cl.MazePlayer changes = 1;
}

message OpOperateActivity {
  repeated cl.OperateActivity changes = 1;
  repeated uint32 deletes = 2;
}

message OpHeroesStarUpCosts {
  repeated cl.HeroStarUpCosts changes = 1;
  repeated uint64 deletes = 2;
}

message OpSkins {
  repeated cl.Skin changes = 1;
  repeated uint32 deletes = 2;
}

message OpSeasonLogs {
  repeated cl.SeasonUserLog changes = 1;
  repeated uint32 deletes = 2;
}

message OpRites {
  repeated cl.Rite changes = 1;
  repeated uint32 deletes = 2;
}

message OpSeasonLinkMonuments {
  repeated cl.SeasonLinkMonument changes = 1;
  repeated uint32 deletes = 2;
}

message OpRemains {
  repeated cl.Remain changes = 1;
  repeated uint32 deletes = 2;
}

message OpActivitySum {
  repeated cl.ActivitySum changes = 1;
  repeated uint32 deletes = 2;
}

message OpDuel {
  cl.Duel data = 1;
}

message OpSeasonJewelry {
  repeated cl.SeasonJewelry changes = 1;
  repeated uint64 deletes = 2;
}

message OpTitles {
  repeated cl.Title changes = 1;
  repeated uint32 deletes = 2;
}

message OpPokemon {
  repeated cl.Pokemon changes = 1;
  repeated uint32 deletes = 2;
}

message L2R_SaveUser {
  db.User user = 1;
  OpMails op_mails = 2;
  db.GlobalRank rank_data = 3;
  OpHeroes op_heroes = 4;
  OpEquips op_equips = 5;
  OpFormations op_formations = 6;
  OpArtifacts op_artifacts = 7;
  OpGems op_gems = 8;
  OpEmblems op_emblems = 9;
  OpMirages op_mirages = 10;
  OpMazePlayers op_mazePlayer = 11;
  OpOperateActivity op_activities = 12;  // 可配置活动
  OpHeroesStarUpCosts op_heroes_star_up_costs = 13;
  OpSkins op_skins = 14;
  OpRites op_rites = 15;
  OpSeasonLogs op_season_logs = 16;
  OpSeasonLinkMonuments op_season_link_monuments = 17;
  OpRemains op_remains = 18;
  OpActivitySum op_activity_sum = 19;
  OpDuel op_duel = 20;
  OpSeasonJewelry op_season_jewelry = 21;
  OpTitles op_titles = 22;
  OpPokemon op_pokemons = 23;
}

message L2R_OpMails {
  OpMails data = 1;
}

// 服务器启动加载数据
message L2R_Load {
  uint64 sid = 1;                // 服务器id
  repeated uint32 rank_ids = 2;  // 排行榜id
}

message R2L_Load {
  uint32 ret = 1;
  uint64 sid = 2;  // 服务器id
  repeated uint32 rank_ids = 3;
  map<uint64, cl.Mail> mails = 4;
  map<uint64, db.GlobalRank> rank_data = 5;         // 全局排行榜数据
  map<uint32, db.SmallRank> small_rank = 6;         // 小排行榜
  map<uint64, db.Friends> friends = 7;              // 好友
  map<uint32, CommonRankData> common_rank = 8;      // 通用排行榜
  map<uint64, cl.Arena> arena = 9;                  // 竞技场
  db.ArenaSeason arena_season = 10;                 // 竞技场赛季数据
  map<uint64, db.UserBan> user_bans = 11;           // 封禁
  map<uint32, db.RankAchieves> rank_achieves = 12;  // 排行成就
  map<uint32, cl.MazeMap> maze_map = 13;            // 迷宫地图
  map<uint32, db.MiragePass> mirage_pass = 14;      // 个人boss过关统计数据
  map<uint64, db.LogicGuild> logic_guilds = 15;     // 公会
  reserved 16, 17, 22;
  reserved "forests", "world_msg", "system_msg";
  //  db.WorldMsg world_msg = 17;                  //世界聊天
  //  db.GmRate rate = 18;                              //评分功能
  map<uint64, db.MultiLang> multi_langs = 19;  // 多语言缓存
  reserved 18;
  reserved "rate";
  repeated uint32 ban_cmds = 20;                                  // 已禁用协议
  map<uint32, cl.ArenaLastSeasonTop> arena_last_season_top = 21;  // 竞技场上赛季前三名数据
                                                                  //   db.SystemMsg system_msg = 22;                                   //系统聊天
  map<uint64, db.WrestleUser> wrestle_user = 23;                  // 神树争霸玩家信息
  db.Wrestle wrestle = 24;                                        // 神树争霸玩法信息
  // map<uint64, db.GuildDungeon> guild_dungeons = 25;               //公会副本
  // db.GuildDungeonReset guild_dungeon_reset = 26;                  //公会副本重置信息
  map<uint64, cl.Flower> flowers = 27;                           // 密林数据
  map<uint32, cl.MonthTasksMsg> month_tasks_msg = 28;            // 全民无双消息
  db.LogicGuildDungeonRefresh logic_guild_dungeon_refresh = 29;  // 跨服公会副本刷新时间
  db.WorldBossSettle world_boss = 30;                            // 世界boss结算记录
  db.TowerSeasonReset tower_season_reset = 31;
  db.ActivityStoryRankRest activity_story_rank_reset = 32;                       // 活动故事已重置排行榜
  db.Peak peak = 33;                                                             // 巅峰竞技场玩法信息
  map<uint64, cl.PeakUser> peak_user = 34;                                       // 巅峰竞技场玩家信息
  db.GstLastAward gst_last_award = 35;                                           // GVG状态
  map<uint64, db.GstLogicUser> gst_logic_user = 36;                              // GVG报名玩家
  db.GstLastFirstCenterAward gst_last_first_center = 37;                         // GVG首占奖励状态
  map<uint32, db.PeopleGroupPackage> people_group_package = 38;                  // 人群包
  map<uint64, db.SeasonArenaUser> season_arena_user = 39;                        // 赛季竞技场
  db.SeasonArenaTopUser season_arena_top_user = 40;                              // 赛季竞技场顶级玩家
  db.SeasonArenaLastAward season_arena_last_award = 41;                          // 赛季竞技场上一次已结算奖励
  db.GstDragonLastAward gst_dragon_last_award = 42;                              // GVG龙战发奖状态
  map<uint32, db.LogicHotRankCollectionLog> logic_hot_rank_collection_log = 43;  // 热度榜收集日志
  db.GstMobLastAward gst_mob_last_award = 44;                                    // GVG公会竞赛排行榜发奖状态
  db.LogicTalentTreeHot talent_tree_hot = 45;                                    // 天赋树养成热度数据
  map<uint64, db.OfflineResource> offline_resource = 46;                         // 离线修改资源
  db.ActivityTower activity_tower = 47;                                          // 地宫冲榜
  db.GstChallengeLastAward gst_challenge_last_award = 48;                        // GVG新擂台赛排行榜发奖状态
  db.SeasonComplianceAward season_compliance_award = 49;                         // 赛季冲榜领奖状态
  map<uint64, db.HotfixCode> hotfix_codes = 50;                                  // 热修复代码
  db.TowerPokemonReset tower_pokemon_reset = 51;                                 // 宠物爬塔重置数据
  map<uint64, cl.BalanceArenaUser> balance_arena_user = 52;                      // 公平竞技场玩家数据
}

message L2R_LoadIDBackup {
  string name = 1;
}

message R2L_LoadIDBackup {
  uint32 ret = 1;
  string name = 2;
  uint64 id = 3;
  int64 time = 4;
}

message L2R_SaveIDBackup {
  string name = 1;
  uint64 id = 2;
  int64 time = 3;
}

message L2R_Finish {}

message R2L_Finish {}

message L2R_GroupMail {
  repeated uint64 users = 1;
  cl.Mail data = 2;
}

message R2L_GroupMail {
  repeated uint64 users = 1;
  cl.Mail data = 2;
}

message L2R_GetUserSnapshot {
  repeated uint64 users = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
  repeated string names = 3;
  uint32 formation_id = 4;
}

message R2L_GetUserSnapshot {
  uint32 ret = 1;
  repeated db.OfflineUser users = 2;
  uint64 client_msg_id = 3;
  uint32 formation_id = 4;
}

// message UserBattleData { db.OfflineUser user = 1; }

message L2R_GetUserBattleData {
  repeated uint64 users = 1;
  uint32 formation_id = 2;
  uint64 client_msg_id = 3;
  bool only_power = 4;
}

message R2L_GetUserBattleData {
  uint32 ret = 1;
  repeated db.UserBattleData users = 2;
  uint32 formation_id = 3;
  uint64 client_msg_id = 4;
  bool only_power = 5;
}

message L2R_SaveSmallRank {
  repeated db.SmallRank ranks = 1;
}

message L2R_SaveFriend {
  repeated db.Friends friends = 1;
}

message L2R_SaveCommonRank {
  repeated OpCommonRank datas = 1;
  repeated uint32 deletes = 2;
}

message CommonRankData {
  map<uint64, db.CommonRank> data = 1;
}

message OpCommonRank {
  uint32 id = 1;
  repeated db.CommonRank values = 2;
  repeated uint64 deletes = 3;
}

message L2R_ChangeConfig {
  int32 account_size = 1;
  int32 ip_accounts = 2;
  int32 device_id_accounts = 3;
  int32 ip_accounts_hw = 4;
  int32 device_id_accounts_hw = 5;
  bool ban_register = 6;
  int32 status = 7;
}

// 保存普通竞技场主数据
message L2R_SaveArena {
  repeated cl.Arena changes = 1;
  repeated uint64 deletes = 2;
  map<uint64, cl.ArenaLogs> change_logs = 3;
  map<uint64, cl.ArenaLogDeletes> delete_logs = 4;
}

// 保存赛季竞技场数据
message L2R_SaveSeasonArena {
  repeated db.SeasonArenaUser change_users = 1;
  repeated uint64 deletes = 2;
  map<uint64, cl.SeasonArenaLogs> change_logs = 3;
  map<uint64, cl.SeasonArenaLogDeletes> delete_logs = 4;
  db.SeasonArenaTopUser top_user = 5;
  db.SeasonArenaLastAward season_award_last_award = 6;
}

// 保存普通竞技场赛季数据
message L2R_SaveArenaSeason {
  db.ArenaSeason season = 1;
}

// 获取普通竞技场战报数据
message L2R_GetArenaLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}

message R2L_GetArenaLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.ArenaLog> logs = 4;
}

message L2R_SetName {
  uint64 id = 1;
  string old_name = 2;
  string new_name = 3;
}

message R2L_SetName {
  uint32 ret = 1;
  uint64 id = 2;
  string old_name = 3;
  string new_name = 4;
}

message L2R_SaveUserBan {
  repeated db.UserBan changes = 1;
  repeated uint64 deletes = 2;
}

// 保存排行成就数据
message L2R_SaveRankAchieve {
  repeated db.RankAchieves changes = 1;
}

message L2R_SaveMazeMap {
  repeated cl.MazeMap changes = 1;
}

// 保存个人boss过关统计数据
message L2R_SaveMiragePass {
  repeated db.MiragePass changes = 1;
}

message L2R_SaveUserBattleSnapshot {
  repeated db.UserBattleSnapshot users = 1;
  uint32 formation_id = 2;
  uint64 client_msg_id = 3;
}

message R2L_SaveUserBattleSnapshot {
  uint32 ret = 1;
  uint32 formation_id = 2;
  uint64 client_msg_id = 3;
  repeated uint64 newIds = 4;
}

message L2R_GetUserBattleSnapshot {
  repeated uint64 ids = 1;
  uint32 formation_id = 2;
  uint64 client_msg_id = 3;
}

message R2L_GetUserBattleSnapshot {
  uint32 ret = 1;
  repeated db.UserBattleSnapshot infos = 2;
  uint32 formation_id = 3;
  uint64 client_msg_id = 4;
}

message L2R_DeleteUserBattleSnapshot {
  repeated uint64 ids = 1;
  uint64 client_msg_id = 2;
}

message L2R_SaveGuild {
  repeated db.LogicGuild changes = 1;
  repeated uint64 deletes = 2;
  repeated db.GuildUser user_changes = 3;
  db.LogicGuildDungeonRefresh dungeon_refresh = 4;
}

message L2R_LoadGuild {
  uint64 id = 1;             // 公会id
  repeated uint64 uids = 2;  // 成员和申请者id
}

message R2L_LoadGuild {
  uint32 ret = 1;
  uint64 guild = 2;
  repeated db.GuildUser users = 3;  // 成员和申请者信息
}

// 获取公会日志数据
message L2R_GetGuildLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}
message R2L_GetGuildLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.GuildLogInfo> logs = 4;
}

// 获取公会副本日志数据
message L2R_GetGuildDungeonLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}
message R2L_GetGuildDungeonLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.GuildDungeonLog> logs = 4;
}

// 获取公会副本留言板数据
message L2R_GetGuildDungeonMessageBoardLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}
message R2L_GetGuildDungeonMessageBoardLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.GuildDungeonMessageBoardLog> logs = 4;
}

message L2R_GetGuildDonateLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}

message R2L_GetGuildDonateLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.GuildDonateLogInfo> logs = 4;
}

message R2L_UserBase {
  uint32 ret = 1;
  db.User user = 2;
}

message GmCarnivalTask {
  uint32 id = 1;                    // 任务id
  uint32 value = 2;                 // 任务目标value
  repeated cl.Resource awards = 3;  // 已领取的奖励
  uint32 carnival_id = 4;           // 嘉年华类型id
  uint32 day = 5;                   // 第几天的任务
}

message GmSevenDayLogin {
  uint32 days = 1;                  // 登录天数
  repeated cl.Resource awards = 2;  // 已领取的奖励
}

message GmCarnivalInfo {
  repeated GmCarnivalTask tasks = 1;  // 已完成的任务ID
}

message GmActivityInfo {
  GmSevenDayLogin sevenDay = 1;
  GmCarnivalInfo carnival = 2;
}

//// 保存世界聊天消息
// message L2R_SaveWorldMsg {
//  db.WorldMsg msg = 1;  // 消息
//}
//
//// 保存公会聊天消息
// message L2R_SaveGuildMsg {
//  repeated db.GuildMsg changes = 1;
//  repeated uint64 deletes = 2;
//}
//
//// 保存私聊消息
// message L2R_SavePrivateMsg {
//  uint64 receiver_id = 1;    // 接收者id
//  uint64 client_msg_id = 2;  // 关联的请求id
//  cl.Message msg = 3;
//}
//
// message R2L_SavePrivateMsg {
//  uint32 ret = 1;
//  uint64 client_msg_id = 2;  // 关联的请求id
//}
//
//// 获取私聊消息
// message L2R_GetPrivateMsg {
//  uint64 id = 1;             // 玩家id
//  uint64 client_msg_id = 2;  // 关联的请求id
//}

// message R2L_GetPrivateMsg {
//  uint32 ret = 1;
//  uint64 id = 2;             // 玩家id
//  uint64 client_msg_id = 3;  // 关联的请求id
//  repeated cl.Message msgs = 4;
//}
//
//// 获取公会聊天消息
// message L2R_GetGuildMsg {
//  uint64 id = 1;             // 公会id
//  uint64 client_msg_id = 2;  // 关联的请求id
//}
//
// message R2L_GetGuildMsg {
//  uint32 ret = 1;
//  uint64 id = 2;             // 公会id
//  uint64 client_msg_id = 3;  // 关联的请求id
//  db.GuildMsg msg = 4;
//}
//
// message L2R_GetPrivateMessageNum {
//  uint64 id = 1;             // 玩家id
//  uint64 client_msg_id = 2;  // 关联的请求id
//}
//
// message R2L_GetPrivateMessageNum {
//  uint32 ret = 1;
//  uint64 id = 2;             // 玩家id
//  uint64 client_msg_id = 3;  // 关联的请求id
//  uint32 num = 4;            // 私聊离线消息数量
//}

message L2R_SaveOrder {
  repeated db.Order orders = 1;
}

// 保存多语言
message L2R_SaveMultiLang {
  repeated db.MultiLang multi_changes = 1;
  repeated uint64 multi_deletes = 2;
}

// 保存多语言
message L2R_DelInvalidQuestionnaireID {
  repeated uint32 invalid_ids = 1;
}

message L2R_OpBanCmds {
  repeated uint32 add_cmds = 1;
  repeated uint32 del_cmds = 2;
}

message R2L_GetRechargeList {
  uint32 ret = 1;
  repeated db.Order orders = 2;
}

// 保存竞技场赛季前三名数据
message L2R_SaveArenaLastSeasonTop {
  repeated cl.ArenaLastSeasonTop data = 1;
}

message R2L_SaveArenaLastSeasonTop {
  uint32 ret = 1;
  cl.ArenaLastSeasonTop data = 2;  // top3数据
}

//// 保存系统聊天消息
// message L2R_SaveSystemMsg {
//  db.SystemMsg msg = 1;  // 消息
//}
//
//// 保存聊天点赞列表
// message L2R_SaveChatLikeList {
//  repeated cl.ChatLike changes = 1;
//  repeated uint64 deletes = 2;
//}
//
//// 获取点赞列表
// message L2R_GetChatLikeList {
//  repeated uint64 msg_ids = 1;  // 消息id
//  uint64 client_msg_id = 2;     // 关联的请求id
//}
//
// message R2L_GetChatLikeList {
//  uint32 ret = 1;
//  repeated uint64 msg_ids = 2;  // 消息id
//  uint64 client_msg_id = 3;     // 关联的请求id
//  map<uint64, cl.ChatLike> msg = 4;
//}

// 获取db.User
message L2R_GetDBUser {
  repeated uint64 uids = 1;  // 玩家id
  uint64 client_msg_id = 2;  // 关联的请求id
}

message R2L_GetDBUser {
  uint32 ret = 1;
  repeated uint64 uids = 2;  // 玩家id
  uint64 client_msg_id = 3;  // 关联的请求id
  repeated db.User users = 4;
}

// 保存神树争霸玩家数据
message L2R_SaveWrestle {
  repeated db.WrestleUser changes = 1;
  repeated uint64 deletes = 2;
  map<uint64, cl.WrestleLogs> change_logs = 3;
  map<uint64, cl.WrestleLogDeletes> delete_logs = 4;
  db.Wrestle wrestle = 5;
}

// 获取神树争霸战报数据
message L2R_GetWrestleLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}

message R2L_GetWrestleLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.WrestleLog> logs = 4;
}

message GuildUserForReset {
  uint64 gid = 1;
  repeated uint64 uids = 2;
  repeated db.GuildUser users = 3;
}

message L2R_LoadGuildUserForReset {
  uint32 reset_type = 1;  // 重置类型   1: 周重置  2: 月重置
  repeated GuildUserForReset data = 2;
}

message R2L_LoadGuildUserForReset {
  uint32 ret = 1;
  uint32 reset_type = 2;
  repeated GuildUserForReset data = 3;
}

// 更新密林数据
message L2R_SaveFlower {
  repeated cl.Flower changes = 1;
  repeated uint64 deletes = 2;
  map<uint64, cl.FlowerSnatchLogs> change_snatch_logs = 3;
  map<uint64, cl.FlowerSnatchLogDeletes> delete_snatch_logs = 4;
  map<uint64, cl.FlowerOccupyLogs> change_occupy_logs = 5;
  map<uint64, cl.FlowerOccupyLogDeletes> delete_occupy_logs = 6;
}

// 获取密林掠夺模式日志
message L2R_GetFlowerSnatchLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}
message R2L_GetFlowerSnatchLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.FlowerSnatchLog> logs = 4;
}

// 获取密林据点模式日志
message L2R_GetFlowerOccupyLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}
message R2L_GetFlowerOccupyLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.FlowerOccupyLog> logs = 4;
}

// 保存全民无双消息
message L2R_SaveMonthTasksMsg {
  repeated cl.MonthTasksMsg changes = 1;
  repeated uint32 deletes = 2;
}

message L2R_GetGuildMemberInfo {
  repeated uint64 users = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
  repeated string names = 3;
  bool guild_need_load = 4;  // 公会数据需要加载
  uint64 gid = 5;            // 公会ID
}

message R2L_GetGuildMemberInfo {
  uint32 ret = 1;
  repeated db.OfflineUser users = 2;
  uint64 client_msg_id = 3;
  map<uint64, db.GuildUser> guild_user = 4;
  bool guild_need_load = 5;  // 公会数据需要加载
  uint64 gid = 6;            // 公会ID
}

message L2R_GetGuildUsers {
  repeated uint64 users = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}

message R2L_GetGuildUsers {
  uint32 ret = 1;
  map<uint64, db.GuildUser> users = 2;
  uint64 client_msg_id = 3;
}

message L2R_SaveWorldBossSettle {
  db.WorldBossSettle settle_data = 1;
}

message L2R_SaveTowerSeasonReset {
  db.TowerSeasonReset data = 1;
}

message L2R_SaveTowerPokemonReset {
  db.TowerPokemonReset data = 1;
}

message L2R_SaveActivityStoryRankRest {
  db.ActivityStoryRankRest data = 1;
}

// 保存巅峰竞技场数据
message L2R_SavePeak {
  db.Peak peak = 1;
  repeated cl.PeakUser changes = 2;
  bool is_season_reset = 3;  // 是否是赛季重置
}

// 保存退款数据
message L2R_SaveRefundOrder {
  repeated db.WaitRefundOrder wait_refund_orders = 1;  // 待退款订单
  repeated db.RefundedOrder refunded_orders = 2;       // 已退款订单
}

// 删除玩家公会邮件
message L2R_DeleteUserGuildMails {
  uint32 ret = 1;
  repeated uint64 uids = 2;
  uint32 mail_base_id = 3;
}

message L2R_SaveGST {
  db.GstLastAward gst_last_award = 1;
  repeated db.GstLogicUser user = 2;
  repeated uint64 delete_users = 3;
  db.GstLastFirstCenterAward gst_last_first_center_award = 4;
  db.GstDragonLastAward gst_dragon_last_award = 5;
  db.GstMobLastAward gst_mob_last_award = 6;
  db.GstChallengeLastAward gst_challenge_last_award = 7;
  map<uint64, cl.GSTChallengeFightLogs> change_logs = 8;
  map<uint64, cl.GSTChallengeFightLogDeletes> delete_logs = 9;
}

// 保存赛季羁绊激活数据
message L2R_SaveSeasonLinkActivation {
  uint32 season_link_activation = 1;  // 赛季羁绊激活英雄id
}

// 删除赛季羁绊激活数据
message L2R_DelSeasonLinkActivation {}

// 保存人群包
message L2R_SavePeopleGroupPackage {
  repeated db.PeopleGroupPackage changes = 1;
  repeated uint32 deletes = 2;
}

// 获取普通竞技场战报数据
message L2R_GetSeasonArenaLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}

message R2L_GetSeasonArenaLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.SeasonArenaLog> logs = 4;
}

message L2R_SaveHotRankUserFormation {
  repeated db.LogicHotRankUserFormation changes = 1;
}

message L2R_SaveLogicHotRankCollectionUpdateFlag {
  repeated db.LogicHotRankCollectionLog log = 1;
  bool delete = 2;
}

message L2R_SaveLogicTalentTreeHot {
  db.LogicTalentTreeHot data = 1;
}

message L2R_SaveOfflineResource {
  repeated db.OfflineResource changes = 1;
  repeated uint64 deletes = 2;
}

message L2R_SaveDuel {
  cl.Duel data = 1;
}

message L2R_SaveActivityTower {
  db.ActivityTower data = 1;
}

// 获取擂台赛战报数据
message L2R_GetGstChallengeLog {
  uint64 id = 1;
  uint64 client_msg_id = 2;  // 关联的请求id
}

message R2L_GetGstChallengeLog {
  uint32 ret = 1;
  uint64 id = 2;
  uint64 client_msg_id = 3;  // 关联的请求id
  map<uint32, cl.GSTChallengeFightLog> logs = 4;
}

message L2R_SaveSeasonComplianceReward {
  db.SeasonComplianceAward data = 1;
}

// 保存热更新代码
message L2R_SaveHotfixCode {
  db.HotfixCode code = 1;
}

// 保存公平竞技场数据
message L2R_SaveBalanceArena {
  repeated cl.BalanceArenaUser change_users = 1;
  repeated uint64 deletes = 2;
}
