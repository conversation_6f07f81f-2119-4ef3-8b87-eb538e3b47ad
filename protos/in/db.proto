syntax = "proto3";

option go_package = "app/protos/in/db";

import "cl.proto";

package db;

enum Op {
  None = 0;
  Dirty = 1;
  Delete = 2;
}

//$<ROP redis|hash|u:uint64|repeatable >
message OfflineUser {
  string uuid = 1;
  uint64 server_id = 2;
  uint64 id = 3;
  string name = 4;
  BaseAttr base = 5;  // 基础数据：影响技能，离线加载
  Op base_op = 6;
  ResourceAttr resource = 7;
  Op resource_op = 8;
}

//$<ROP redis|hash|u:uint64|repeatable >
message BattleUser {
  string uuid = 1;
  uint64 server_id = 2;
  uint64 id = 3;
  string name = 4;
  BaseAttr base = 5;  // 基础数据：影响技能，离线加载
  Op base_op = 6;
  ResourceAttr resource = 7;
  Op resource_op = 8;
  ModuleGlobalAttr module_global_attr = 9;  // 境界
  Op module_global_attr_op = 10;
  Crystal crystal = 11;  // 水晶数据
  Op crystal_op = 12;
  AchievementsShowcase achievements_showcase = 13;  // 成就展示
  Op achievements_showcase_op = 14;
  reserved 15;
  reserved "module2";
  reserved 16;
  reserved "module2_op";
}

//$<ROP redis|hash|u:uint64|repeatable >
message ResourceUser {
  string uuid = 1;
  uint64 server_id = 2;
  uint64 id = 3;
  string name = 4;
  BaseAttr base = 5;  // 基础数据：影响技能，离线加载
  Op base_op = 6;
  ResourceAttr resource = 7;  // 货币
  Op resource_op = 8;
  ModuleGlobalAttr module_global_attr = 9;  // 境界
  Op module_global_attr_op = 10;
  Bags bag = 11;
  Op Bag_op = 12;
  ModuleAttr module = 13;
  Op module_op = 14;
  ModuleAttr1 module1 = 15;  // 模块数据：不影响技能，离线不加载
  Op module1_op = 16;
  Crystal crystal = 17;  // 水晶数据
  Op crystal_op = 18;
  ModuleAttr4 module4 = 19;  // 模块数据：不影响技能，离线不加载
  Op module4_op = 20;
  ModuleAttr2 module2 = 21;  //
  Op module2_op = 22;
  ModuleAttr5 module5 = 23;
  Op module5_op = 24;
  ModuleAttrDisorderland disorderland = 25;
  Op disorderland_op = 26;
  ModuleAttr7 module7 = 27;
  Op module7_op = 28;
  ModuleAttr8 module8 = 29;
  Op module8_op = 30;
}

//$<ROP redis|hash|u:uint64 buffer=4096>
message User {
  string uuid = 1;
  uint64 server_id = 2;
  uint64 id = 3;
  string name = 4;
  BaseAttr base = 5;  // 基础数据：影响技能，离线加载
  Op base_op = 6;
  ResourceAttr resource = 7;
  Op resource_op = 8;
  ModuleAttr module = 9;  // 模块数据：不影响技能，离线不加载
  Op module_op = 10;
  Bags bag = 11;
  Op Bag_op = 12;
  ModuleAttr1 module1 = 13;  // 模块数据：不影响技能，离线不加载
  Op module1_op = 14;
  ModuleGlobalAttr module_global_attr = 15;  // 存放带有全局属性的数据，影响技能，离线加载
  Op module_global_attr_op = 16;
  ClientInfo client_info = 17;  // 客户端信息
  Op client_info_op = 18;
  reserved 19;  // string wm_extra = 19;  //完美sdk信息，快照日志用
  reserved 20;  // Op wm_extra_op = 20;
  ModuleAttr2 module2 = 21;
  Op module2_op = 22;
  Crystal crystal = 23;  // 水晶数据
  Op crystal_op = 24;
  ModuleAttr3 module3 = 25;
  Op module3_op = 26;
  AchievementsShowcase achievements_showcase = 27;  // 成就展示
  Op achievements_showcase_op = 28;
  ModuleAttr4 module4 = 29;
  Op module4_op = 30;
  ModuleAttr5 module5 = 31;
  Op module5_op = 32;
  ModuleAttrDisorderland disorderland = 33;  // 独立的 moduleAttr，失序空间专用
  Op disorderland_op = 34;
  ModuleAttr6 module6 = 35;
  Op module6_op = 36;
  ModuleAttr7 module7 = 37;
  Op module7_op = 38;
  ModuleAttr8 module8 = 39;
  Op module8_op = 40;
}

message BaseAttr {
  uint32 level = 1;
  int64 level_tm = 2;  // 等级改变的时间
  uint32 career = 3;
  uint32 base_id = 4;  // 灵宠base_id
  uint32 vip = 5;
  int64 create_time = 6;
  int64 online_time = 7;
  int64 offline_time = 8;
  uint64 gold = 9;
  uint64 recharge_diamond = 10;  // 充值钻石
  uint64 present_diamond = 11;   // 赠送钻石
  uint32 star = 12;
  uint32 energy = 13;
  uint32 energy_tm = 14;  // 上次结算精力的时间
  uint64 exp = 15;        // 修为
  int64 power = 16;
  int64 power_tm = 17;
  repeated uint32 face = 18;           // 捏脸
  uint32 dungeon_id = 19;              // 战役关卡id
  int64 onhook_tm = 20;                // 挂机开始时间
  uint32 vip_exp = 22;                 // vip经验
  uint32 op_id = 23;                   // 渠道
  uint32 channel = 24;                 // 二级渠道
  int64 last_online_time = 25;         // 上次登录时间
  int64 arena_power = 26;              // 竞技场防守战力
  int64 max_power = 27;                // 历史最高战力
  bool is_created = 28;                // 创角过程是否完整
  uint32 dc_id = 29;                   // 数据分析使用
  uint64 total_recharge_diamond = 30;  // 累计充值钻石
  uint64 total_add_diamond = 31;       // 累计增加钻石
  uint64 total_cost_diamond = 32;      // 累计消耗钻石
  uint64 total_add_gold = 33;          // 累计增加金币
  uint64 total_cost_gold = 34;         // 累计消耗金币
  reserved 35;
  int64 forest_power = 36;                // 密林防守战力
  uint32 recharge_amount = 37;            // 总充值累计金额
  string lang = 38;                       // 前端设置的语言
  repeated uint64 top5_heros = 39;        // top5英雄id
  uint32 account_tag = 40;                // 账号标识；1：福利充值
  string register_ip = 41;                // 注册IP
  string last_login_ip = 42;              // 最后登陆IP
  int64 last_recharge_time = 43;          // 最后充值时间
  map<uint32, int64> defense_power = 45;  // 防守阵容 key：formationID   value：power
  uint32 show_hero = 46;                  // 战力最高的英雄sysID
  repeated int64 expired_time = 47;       // 0:头像,1:头像框过期时间,2:形象过期时间,3:聊天气泡,4:称号过期时间
  uint32 equip_enchant_count = 48;        // 装备附魔次数 0:未附魔过不暴击 1:附魔过一次必定暴击 2:已经完成新手引导正常逻辑
  int64 crystal_power = 49;               // 水晶总战力 (2.16.5-cn后更名为战斗英雄列表总战力)
  bool trial_guide = 50;                  // 材料本新手引导
  uint32 login_day = 51;                  // 累计登录天数
  uint32 image = 52;                      // 形象ID
  uint32 season_lv = 53;
  int64 season_power = 54;                // 赛季总战力
  uint32 season_id = 55;                  // 赛季ID
  bool season_enter = 56;                 // 是否已点击赛季入口
  int64 coupon = 57;                      // 代金券
  uint64 total_add_coupon = 58;           // 累计增加代金券
  uint64 total_cost_coupon = 59;          // 累计消耗代金券
  bool season_end = 60;                   // 赛季是否已结算
  map<uint32, bool> push_setting = 61;    // 设置是否推送的标识; key: 不同的 pushID
  int64 season_top_power = 62;            // 本赛季最高战力
  int64 season_crystal_top35_power = 63;  // 本赛季水晶最高35名英雄战力
  bool season_arena_skip_formation = 64;  // 赛季竞技场跳过布阵
  uint32 pid = 65;                        // 37 pid
  uint32 gid = 66;                        // 37 gid
  uint32 chat_bubbles = 67;               // 聊天气泡
  uint32 season_before_open = 68;         // 玩家未达到开启赛季条件时最后一次请求时服务器的赛季ID，进入赛季后重置为0, season_id 为正确的赛季ID
  bool update_season_power = 69;          // 因2.16.5-cn水晶重构后，登录是否更新过赛季战力以及排名
  uint32 max_blessed_hero_cnt = 70;       // 赐福英雄数量的最大值
  int64 first_maze_power = 71;            // 首次生成迷宫战力
  uint32 title = 72;                      // 称号
  bool has_rebase = 73;                   // 是否请求过返利
  uint32 can_mute = 74;                   // 是否可以禁言账号
  int64 hotfix_version = 75;              // 热修复数据的版本
  uint32 pokemon_image = 76;              // 宠物形象ID
  uint32 pokemon_potential_level = 77;    // 宠物潜能等级
}

// 资源类型，有新资源同时增加MAX值
enum RESOURCE {
  ENERGY = 0;
  MAX = 1;
}

message ResourceAttr {
  repeated uint64 data = 1;
}

message ModuleAttr {
  DailyInfo daily = 1;                         // 每日重置数据
  map<uint32, uint32> shop_permanent_cnt = 2;  // 商店永久次数 id => cnt
  cl.AchieveInfo achieve = 3;                  // 成就
  uint32 hero_slot = 4;                        // 英雄栏位数量
  cl.Dungeon dungeon = 5;                      // 战役挂机信息
  reserved 6;
  reserved "summon_old";
  WeeklyInfo weekly = 7;                  // 每周重置数据
  map<uint32, cl.Shop> shops = 8;         // 商店数据
  map<uint32, cl.Tower> towers = 9;       // 爬塔数据
  ArenaOpponents arena_opponents = 10;    // 普通竞技场对手数据
  map<uint32, cl.Avatar> avatars = 11;    // 激活的头像
  map<uint32, cl.TrialInfo> trials = 12;  // 材料本数据
  map<uint32, cl.NumInfo> num_info = 13;  // 玩法通用次数信息
  cl.Dispatch dispatch = 14;              // 悬赏任务数据
  map<uint32, SummonInfo> summon = 15;    // 召唤信息
  bool summon_auto_decompose_close = 16;  // 注意：这里是关闭抽卡(英雄)自动分解，默认是false
}

message ModuleAttr1 {
  cl.GoldBuy gold_buy = 1;  // 点金信息
  reserved 2;
  reserved "handbooks";
  RankAchieveAwards rank_achieve_awards = 3;  // 已领奖的排行成就id列表
  cl.Tales tales = 4;                         // 英雄列传
  DropInfo drop_info = 5;                     // 掉落信息
  cl.Guidance guidance = 6;                   // 新手引导
  repeated cl.Carnival carnival = 7;          // 嘉年华
  cl.SevenDayLogin seven_day_login = 8;       // 7日登录活动
  cl.Medal medal = 9;                         // 功勋
  uint32 emblem_slot = 10;                    // 符文栏位数量
}

message ModuleAttr2 {
  cl.Forecast forecast = 1;                         // 新功能预告
  Recharge recharge = 2;                            // 充值信息
  cl.VipInfo vip_info = 3;                          // vip信息
  cl.ActivityRecharge activity_recharge = 4;        // 限时充值
  uint32 hero_star_limit = 5;                       // 英雄升星限制等级
  cl.CrystalBlessingAchieve crystal_achieve = 6;    // 水晶祝福成就 //TODO v.0.11.0上线后可reserved
  cl.Towerstar towerstar = 7;                       // 条件爬塔
  cl.MonthlyCard monthly_card = 8;                  // 月卡
  map<uint32, cl.PassData> passes = 9;              // 战令
  cl.PushGiftInfo push_gift_info = 10;              // 推送礼包
  cl.Rate rate = 11;                                // 评分
  repeated uint32 questionnaire_finished_ids = 12;  // 已完成问卷id
  repeated uint32 equip_auto_decompose_rare = 13;   // 自动分解的装备品质
  //   map<uint32, cl.Debut> debuts = 14;                // 英雄首发
  reserved 14;
  reserved "debuts";
}

// 充值信息（普通充值和首充礼包）
message Recharge {
  repeated uint32 normal_ids = 1;                                  // 普通充值id（起码充过一次）
  repeated uint32 first_gift_ids = 2;                              // 首充礼包id（废弃）
  repeated uint32 web_diamond_recharge_ids = 3;                    // 官网钻石充值ID（商品第一次充值的时候记录）
  int64 first_gift_buy_time = 4;                                   // 首充礼包购买时间（废弃）
  repeated cl.FirstGiftRewardStatus first_gift_reward_status = 5;  // 首充礼包领奖状态
}

// 存放带有全局属性的玩法
message ModuleGlobalAttr {
  cl.Memory memory = 1;                              // 最新的回忆信息
  cl.GuildTalent guild_talent = 2;                   // 公会天赋
  map<uint32, cl.Handbooks> handbooks = 3;           // 图鉴数据 图鉴类型=>图鉴数据
  cl.GoddessContractInfo goddess_contract_info = 4;  // 0.9.8的契约之前版本契约之所数据 - 已废弃
  uint32 goddess_contract_exp = 5;                   // 契约之所总经验
  repeated uint32 goddess_suits = 6;                 // 女武神装扮
  cl.Handbooks handbooks_data = 7;                   // 图鉴数据
  cl.RemainBook remain_book = 8;                     // 遗物图鉴
  map<uint32, uint32> goddess_levels = 9;            // 女武神等级冗余存储
  cl.TalentTreeCultivate talent_tree_cul = 10;       // 天赋树养成信息
  cl.ShareGrowth share_growth = 11;                  // 共享养成
}

message Bags {
  map<uint32, uint32> items = 1;
  map<uint32, uint32> fragments = 2;
  map<uint32, uint64> tokens = 3;                 // 基础资源
  map<uint32, uint32> artifactFragments = 4;      // 神器碎片
  map<uint32, uint32> emblem_fragments = 5;       // 符文碎片
  map<uint32, uint32> remain_fragments = 6;       // 遗物碎片
  map<uint32, cl.ExpiredItems> expired_item = 7;  // 过期道具
  SeasonMapBag season_map_bag = 8;                // 赛季地图背包
  map<uint32, uint32> pokemon_fragments = 9;      // 宠物碎片
  ResourcesWarn resources_warn = 10;              // 资源检测
}

message SeasonMapBag {
  map<uint32, uint32> goods = 1;  // 赛季地图道具
  // uint32 total = 2;               // 赛季地图道具总数量
}

message DailyInfo {
  uint32 daily_zero = 1;
  uint32 level = 2;  // 每日重置的时候的等级
  map<uint32, uint32> shop_cnt = 3;
  map<uint32, cl.NumInfo> num_info = 4;        // 玩法通用次数信息
  cl.DailyTask daily_task = 5;                 // 新的每日任务
  uint32 friend_rcved_cnt = 6;                 // 已领取点赞次数
  int64 total_play_time = 7;                   // 累计游戏时间
  reserved 8;                                  // 当日免费抽将次数(普通)
  reserved 9;                                  // 当日免费抽将次数(高级)
  map<uint32, uint32> tower_sweep_count = 10;  // 爬塔扫荡次数
  reserved 11;
  reserved "like_list";                        // 各玩法每日点赞列表 玩法id => LikeInfo
  reserved 12;                                 // 当日免费抽神器次数
  map<uint32, uint32> trial_give_count = 13;   // 材料本type => 赠送次数
  uint32 friend_snd_cnt = 14;                  // 好友赠送友情点次数
  bool guild_sign_in = 15;                     // 公会签到
  map<uint32, uint32> tales_elite_fight = 16;  // 英雄列传强敌挑战次数
                                               //   uint32 mirage_bonus_fight_count = 17;        //个人boss赠送次数
  reserved 17;
  reserved "mirage_bonus_fight_count";
  reserved "mirage_sky_bonus_fight_count";
  reserved 18;
  reserved "mirage_hell_bonus_fight_count";
  reserved 19;
  int64 maze_match_power = 20;                         // 迷宫匹配战力
  map<uint32, uint32> summon_free_count = 21;          // 免费召唤次数
  uint32 goddess_touch_count = 22;                     // 女武神抚摸次数
  uint32 recharge_amount = 23;                         // 每日充值总金额
  repeated cl.GuildMedalLiked guild_medal_liked = 24;  // 公会已送花勋章
}

message LikeInfo {
  map<uint64, uint32> like_cnt = 1;  // 点赞列表 被点赞玩家id => 点赞数量
}

message WeeklyInfo {
  uint32 weekly_zero = 1;
  uint32 level = 2;               // 每日重置的时候的等级
  cl.WeeklyTask weekly_task = 3;  // 新的每周任务
  reserved 4;                     // 心愿单 （0.8.8版本更新后删除）
  reserved "summon_wish_list";
  map<uint32, cl.NumInfo> num_info = 5;  // 玩法通用次数信息
  uint32 link_summon_free = 6;           // 流派抽卡免费次数
}

message WishList {
  reserved 1;
  reserved "locked_slot";
  map<uint32, uint32> list = 2;     // 心愿单 key=格位id  value=心愿id
  uint32 wish_guarantee_count = 4;  // 心愿单保底记数
  reserved 5;
  reserved "start_tm";
  reserved 6;
  reserved "reset_times";
  uint32 gain_wish_hero_num = 7;  // 获得心愿英雄次数
}

message ClientInfo {
  cl.ClientInfo client_info = 1;  // 客户端信息
}

message Crystal {
  cl.Crystal crystal = 1;  // 水晶数据
}

// 全服排行榜
//$<ROP redis|map|grank buffer=64>
message GlobalRank {
  uint64 id = 1;  //$<ROP unique >
  uint32 level = 2;
  int64 level_tm = 3;  // 等级更变的时间
  int64 power = 4;
  int64 power_tm = 5;
}

message RankValue {
  uint64 id = 1;
  uint64 value = 2;
  int64 valueTm = 3;  // 更新时间
}

//$<ROP redis|map|smallrank >
message SmallRank {
  uint32 id = 1;                  //$<ROP unique > //排行榜id
  repeated RankValue values = 2;  // 榜单数据
}

/*
//$<ROP redis|map|commonrank:uint64 >
message CommonRank {
  uint64 uid = 1;  //$<ROP unique >
  uint64 sid = 2;
  uint64 value = 3;
  uint64 value2 = 4;
  uint32 tm = 5;  //更新时间
}
*/

//$<ROP redis|map|commonrank:uint64 >
message CommonRank {
  uint64 uid = 1;  //$<ROP unique >
  bytes data = 2;  //
}

message Friend {
  uint64 id = 1;
  bool black = 2;
  uint64 server_id = 3;
}

message FriendLike {
  uint64 id = 1;        // 好友id
  uint32 snd_like = 2;  // 0-未点赞 1-已点赞
  uint32 rcv_like = 3;  // 0-未被点赞 1-被点赞 2-已领取 每日重置的时候只有已领取的才会被重置掉
}

message FriendRequest {
  int64 tm = 1;
  uint64 id = 2;
  uint64 server_id = 3;
}

//$<ROP redis|map|friends >
message Friends {
  uint64 id = 1;  //$<ROP unique>
  int64 daily_zero = 2;
  map<uint64, Friend> friends = 3;
  repeated FriendRequest requests = 4;       // 好友申请
  map<uint64, FriendLike> friend_likes = 5;  // 点赞信息
  bool is_sync = 6;
}

message UserPower {
  uint64 uid = 1;
  uint64 power = 2;
  uint32 tm = 3;
}

message SummonInfo {
  uint32 summon_count = 1;  // 召唤次数
  reserved 2;
  reserved "fixed_guarantee_count";
  reserved 3;
  reserved "change_guarantee_count";
  reserved 4;  // map<uint32, uint32> wish_list = 4;//心愿单 key=格位id  value=心愿英雄id (0.8.8版本更新后删除)
  reserved "wish_list";
  uint32 continuity_summon_count = 5;                        // 连抽次数，只有高级十连抽时，才记录次数，给评分功能使用
  WishList wish_list_msg = 6;                                // 心愿单
  uint32 artifact_fragment_guarantee_count = 7;              // 神器碎片保底记数
  repeated uint32 color_guarantee_count = 8;                 // 品质保底
  cl.ActivitySummonGuarantee activity_summon_guarantee = 9;  // 活动保底结构
}

// 玩家的战斗数据
message UserBattleData {
  BattleUser user = 1;
  uint32 formation_id = 2;
  map<uint32, cl.Formation> formations = 3;
  map<uint64, cl.HeroBody> heroes = 4;
  map<uint64, cl.Equipment> equips = 5;
  map<uint32, cl.Artifact> artifacts = 6;
  reserved 7;
  reserved "gems";
  map<uint64, cl.EmblemInfo> emblems = 8;
  GuildMsgForBattle guild_msg = 9;
  map<uint32, cl.Skin> skins = 10;
  map<uint32, cl.Rite> rites = 11;
  map<uint32, cl.SeasonLinkMonument> monuments = 12;
  repeated uint32 season_link_activation = 13;
  map<uint32, cl.Remain> remains = 14;
  uint32 area_id = 15;
  int64 power = 16;                                   // 该阵容的战力
  cl.Duel duel = 17;                                  // 切磋数据
  map<uint64, cl.SeasonJewelry> season_jewelry = 18;  // 赛季装备
  map<uint32, cl.Title> titles = 19;                  // 称号
  map<uint32, cl.Pokemon> pokemons = 20;              // 宠物
}

// 玩家战斗数据中所需的玩家公会信息
message GuildMsgForBattle {
  uint64 id = 1;    // 公会id
  string name = 2;  // 公会名
}

//$<ROP redis|map|ubs:uint64 >
message UserBattleSnapshot {
  uint64 id = 1;  //$<ROP unique>
  UserBattleData info = 2;
}

// 主线排行榜
message RankDungeon {
  uint64 uid = 1;
  uint32 dungeon_id = 2;
  int64 tm = 3;      // 时间
  uint64 power = 4;  // 通关时的阵容战力
}

// 赛季主线排行榜
message RankSeasonDungeon {
  uint64 uid = 1;
  uint32 dungeon_id = 2;
  int64 tm = 3;
}

// 爬塔排行榜
message RankTower {
  uint64 uid = 1;
  uint32 floor_id = 2;
  int64 tm = 3;      // 时间
  uint64 power = 4;  // 通关时的阵容战力
}

// 战力排行榜
message RankPower {
  uint64 uid = 1;
  uint64 power = 2;
  int64 tm = 3;  // 时间
}

// 个人boss排行榜
message RankMirage {
  uint64 uid = 1;
  uint32 floor = 2;  // 层数
  //   uint32 star = 3;   //星数
  int64 tm = 4;  // 时间
  reserved 3;
  reserved "star";
  uint64 power = 5;  // 通关时的阵容战力
}

message RankTowerstar {
  uint64 uid = 1;
  uint32 dungeon_id = 2;
  uint32 star = 3;   // 星数
  int64 tm = 4;      // 时间
  uint64 power = 5;  // 通关时的阵容战力
}

// 公会等级排行榜
message RankGuildLevel {
  uint64 gid = 1;
  uint32 level = 2;  // 等级
  uint32 exp = 3;    // 公会经验
  int64 tm = 4;      // 时间
}

//$<ROP redis|hash|arena_season >
message ArenaSeason {
  uint32 id = 1;        // 赛季编号，自增id
  int64 season_tm = 2;  // 赛季完成发奖的时间
  int64 daily_tm = 3;   // 每日完成发奖的时间
}

message ArenaOpponents {
  repeated ArenaOpponent opponents = 1;  // 对手信息
  int64 refresh_tm = 2;                  // 上一次刷新对手的时间
  reserved 3;                            // 刚刚打赢，需要刷新对手
}

message ArenaOpponent {
  uint64 id = 1;       // uid
  uint32 level = 2;    // 等级
  string name = 3;     // 昵称
  uint32 base_id = 4;  // 头像相关
  uint32 score = 5;    // 积分
  bool bot = 6;        // 是否是机器人
}

message GMBan {
  uint32 type = 1;  // 1:禁止发言 2:禁止登录 3:临时禁止登录
  int64 start = 2;  // 封禁开始时间
  int64 end = 3;    // 封禁结束时间; start == end && end == 0 结束封禁
}

//$<ROP redis|map|ban buffer=64>
message UserBan {
  uint64 id = 1;               //$<ROP unique>
  map<uint32, GMBan> ban = 2;  // key:禁止类型 value:禁止信息
}

// 排行成就
//$<ROP redis|map|rank_achieves >
message RankAchieves {
  uint32 id = 1;                         //$<ROP unique>        //量表中的rank_id
  repeated cl.RankAchieve achieves = 2;  // 成就具体信息
}

message RankAchieveAwards {
  repeated uint32 id = 1;
}

message DropInfo {
  map<uint64, uint32> fail_count = 1;  // 连续失败次数
}
// 个人boss过关人数统计
//$<ROP redis|map|mirage_pass >
message MiragePass {
  uint32 id = 1;     //$<ROP unique> //关卡hurdle_id
  uint32 count = 2;  // 过关人数
}

//$<ROP redis|map|logicguild >
message LogicGuild {
  uint64 id = 1;                      //$<ROP unique >
  string name = 2;                    // 昵称
  uint32 level = 3;                   // 等级
  repeated uint64 members = 4;        // 成员IDs  只是本服所属的ID，不是logic公会的所有成员
  bool have_applicant = 5;            // 有申请者
  uint32 season_top_division = 6;     // 赛季最高段位   // 只给红点判断用,实际发奖不用
  LogicGuildMedal medal = 7;          // 功勋
  int64 last_combine_apply_time = 8;  // 最新的合并请求发起时间，0表示没有请求
}

message LogicGuildMedal {
  // map<uint32, uint32> medals = 1;
  int64 sync_tm = 2;
  repeated cl.GuildMedal medals = 3;
}

message LogicGuildCombineApply {
  uint32 apply_type = 1;  // 发起的请求类型 common.GUILD_COMBINE_APPLY_TYPE
  uint64 gid = 2;         // 目标公会id
  int64 apply_tm = 3;     // 发起请求时间
}

//$<ROP redis|hash|logic_guild_dungeon_refresh >
message LogicGuildDungeonRefresh {
  int64 last_weekly_award_tm = 1;  // 上次星期结算时间
  int64 last_season_award_tm = 2;  // 上次公会副本计算时间
  Op hall_of_fame_op = 3;
}

//$<ROP redis|map|guilduser>
message GuildUser {
  uint64 id = 1;                  //$<ROP unique>
  uint64 guild_id = 2;            // 公会id 方便查询玩家所在公会
  string name = 3;                // 玩家名字  玩家离线时会用到,如:记录日志  todo 目前用不到
  bool sign_in = 4;               //     todo 已不需要
  uint32 grade = 5;               // 职位 1-leader 2-deputy 3-member
  repeated uint64 apply_ids = 6;  // 申请列表
  int64 last_leave_tm = 7;        // 上次离开公会的时间
  int64 send_mail_tm = 8;         // 发送邮件时间
  int64 daily_zero = 9;
  uint32 user_chapter = 10;                                       // 玩家所处章节    todo 已废弃
  repeated uint32 award_box = 11;                                 // 所处章节已领宝箱 todo 已废弃
  uint64 daily_dungeon_damage = 12;                               // 今日副本总伤害
  uint64 history_max_dungeon_damage = 13;                         // 副本单次挑战造成的最高伤害
  uint32 total_fight_count = 14;                                  // 挑战副本总次数  todo 已废弃
  int64 recruit_cd_time = 15;                                     // 公会招募cd过期时间
  uint32 buy_count = 16;                                          // 今日购买次数
  uint32 challenge_times = 17;                                    // 拥有的挑战次数 每日重置的时候将免费次数加到这个上面
  int64 last_recover_tm = 18;                                     // 挑战次数恢复时间
  repeated uint32 recv_chapter_task = 19;                         // 每周已领取的章节任务
  repeated uint32 recv_chapter_boss = 20;                         // 每周已领取的章节Boss宝箱奖励
  uint32 boss_award_received_max_chapter = 21;                    // todo
  uint32 drop_award_index = 22;                                   // 掉落奖励索引(每周随机 1~4) todo 已废弃
  map<uint32, uint64> today_fight_best_damage = 23;               // 今日挑战Boss的最佳伤害战斗伤害  key: bossID  value: damage
  uint32 like_count = 24;                                         // 点赞次数 todo 已废弃
  map<uint32, uint64> season_fight_best_damage = 25;              // 赛季挑战Boss的最佳伤害战斗伤害  key: bossID  value: damage
  uint64 season_max_dungeon_damage = 26;                          // 赛季副本单次挑战造成的最高伤害
  uint64 max_damage_power = 27;                                   // 最高伤害时的阵容战力
  uint32 donate_count = 28;                                       // 今日捐赠次数
  repeated uint32 donate_award_ids = 29;                          // 已领奖捐赠ID
  repeated uint32 activity_point = 30;                            // 下标0-6 对应星期1-星期天的活跃度
  string approver = 31;                                           // 批准加入的管理员的名字
  repeated uint32 season_recv_first_division = 32;                // 赛季段位首通奖励
  int64 weekly_zero_time = 33;                                    // 每周重置时间
  int64 season_zero_time = 34;                                    // 每赛季重置时间
  uint32 chest_weekly_recved_count = 35;                          // 每周已领取公会宝箱的次数
  repeated cl.GuildChestFinishRecv guild_chest_finish_recv = 36;  // 公会宝箱结束时花转换
  int64 join_tm = 37;                                             // 加入公会时间
  uint32 recv_total_chest_like_token = 38;                        // 收到花束的总数
  repeated cl.GuildDungeonStrategyEffect strategy_effect = 39;    // 秘技效果
  uint64 dungeon_chat_room_id = 40;                               // 副本聊天房间ID
  reserved 41;
  reserved "level_count";
  repeated cl.GuildLeaveCount guild_level_cnt = 42;     // 离开公会次数
  int64 last_gst_hang_reward = 43;                      // 上一次领取公会挂机奖励的时间
  bool need_clear_gst_item = 44;                        // 是否需要清除公会战物品
  bool medal_flower_to_item = 45;                       // 功勋点赞花是否转为道具
  GuildMobilizationGuildMember mobilization_data = 46;  // 公会任务竞赛数据
}

message GuildMobilizationGuildMember {
  uint32 round = 1;                                     // 数据所属期数
  map<uint32, cl.GuildMobilizationTaskData> tasks = 2;  // 接取的任务
}

// 公会聊天
//$<ROP redis|hash|guild_msg:uint64>
message GuildMsg {
  uint64 id = 1;  //  公会id
  ChatMsg msg = 2;
  Op msg_op = 3;
}

// 世界聊天
//$<ROP redis|hash|world_msg>
message WorldMsg {
  ChatMsg msg = 1;
  Op msg_op = 2;
}

// 系统聊天
//$<ROP redis|hash|system_msg>
message SystemMsg {
  ChatMsg msg = 1;
  Op msg_op = 2;
}

message ChatMsg {
  repeated cl.Message msgs = 1;  // 公会聊天、世界聊天的消息
}

//$<ROP redis|map|order:uint64>
message Order {
  string order_id = 1;         //$<ROP unique >
  string cooperator_o_id = 2;  // SDK订单号
  string uuid = 3;             // 平台用户id
  uint32 amount = 4;           // 商品金额
  uint64 server_id = 5;        // 服务器id
  string product_id = 6;
  uint32 game_id = 7;
  uint64 user_id = 8;       // 游戏角色ID
  uint32 op_id = 9;         // 运营商ID
  uint32 channel_id = 10;   // 二级运营商ID
  string custom_data = 11;  // 自定义参数CustomData
  int64 pay_time = 12;      // 订单支付时间, 时间戳，单位秒
  uint32 status = 13;       // 0-未到账 1-已入库 2-已发货 3-失败 enum ORDER_STATUS
  uint32 type = 14;         // 订单类型 0-充值订单 1-补单 enum ORDER_TYPE
  reserved 15;
  string pay_currency = 16;         // 实际支付币种(直接记录从SDK获取的值)
  double pay_amount = 17;           // 实际支付金额(直接记录从SDK获取的值)
  bool is_sandbox = 18;             // 是否沙盒
  string currency = 19;             // 商品货币
  string sdk_pid = 20;              // sdk平台的pid
  string sdk_extra = 21;            // 37 sdk的额外字段
  cl.OrderCustomData custom = 100;  // custom_data解析后的数据 游戏内使用
}

// 订单产生时，玩家离线了，就把订单id记录在此，玩家上线时遍历这个集合，处理订单
// 目的：通过此集合可直接筛选出 需要处理的订单，不用遍历所有订单了
//$<ROP redis|set|waitprocessorder:uint64>
message WaitProcessOrder {
  repeated string order_id = 1;  //$<ROP unique >
}

//$<ROP redis|map|wait_refund_order:uint64>
message WaitRefundOrder {
  string order_id = 1;  //$<ROP unique >
  Order order_data = 2;
}

//$<ROP redis|map|refunded_order:uint64>
message RefundedOrder {
  string order_id = 1;  //$<ROP unique >
  uint32 status = 2;    // 0-未处理 1-成功 2-失败 enum REFUND_STATUS
}

//$<ROP redis|map|multilang >
message MultiLang {
  uint64 id = 1;  //$<ROP unique >
  map<string, LangParams> value = 2;
  int64 expired_time = 3;
}

message LangParams {
  repeated string params = 1;
}

// 已完成的问卷
//$<ROP redis|set|questionnaire:uint64>
message Questionnaire {
  repeated uint32 finished_ids = 1;  //$<ROP unique >
}

//$<ROP redis|set|bancmd >
message BanCmd {
  repeated uint32 cmds = 1;  //$<ROP unique >
}

//$<ROP redis|map|wrestleuser >
message WrestleUser {
  uint64 uid = 1;                              //$<ROP unique>
  uint32 level = 2;                            // 赛场等级
  uint32 rank = 3;                             // 小组排名
  uint32 score = 4;                            // 积分
  uint32 total_rank = 5;                       // 赛季总排名，赛季结算完毕，下赛季未开始显示
  uint32 last_level = 6;                       // 结算前的赛场等级
  int64 enter_tm = 7;                          // 进入赛场时间
  uint32 like_status = 8;                      // 点赞
  map<uint64, int64> fail_tm = 9;              // 失败时间（只记录主动挑战）
  bool be_defeated = 10;                       // 被击败过
  int64 last_award_tm = 11;                    // 上次发奖时间
  uint32 received_level_award = 12;            // 赛季已领等级奖励，位存储
  uint32 log_index_id = 13;                    // 日志序号Id
  bool new_log_tip = 14;                       // 新日志提示
  uint32 max_level = 15;                       // 当前赛季达到的最高等级
  uint32 prev_season_level_after_reset = 16;   // 前一个赛季结算后的最终等级
  uint32 prev_season_total_rank = 17;          // 前一个赛季的总排名
  uint32 num_of_champion = 18;                 // 冠军次数
  uint32 last_room_rank = 19;                  // 结算前的房间内排名
  uint32 prev_season_level_before_reset = 20;  // 前一个赛季结算前的等级
  reserved 21;
  cl.WrestleUserScoreAndRank last_fight_change = 22;  // 最后一场战斗前的徽章数
  uint32 season_join_num = 23;                        // 参与赛季次数
  int64 next_change_room_tm = 24;                     // 下一次可更换房间的时间
}

//$<ROP redis|hash|wrestle >
message Wrestle {
  int64 season_award_tm = 1;           // 上次赛季结算时间
  int64 daily_award_tm = 2;            // 上次日结算时间
  int64 hall_of_fame_update_tm = 3;    // 荣耀殿堂数据更新时间
  WrestleHallOfFame hall_of_fame = 4;  // 荣耀殿堂数据
  Op hall_of_fame_op = 5;
}

message WrestleFighterRecord {
  uint32 level = 1;              // 战场等级
  uint32 rank = 2;               // 总排名
  uint32 score = 3;              // 积分
  uint32 liked_count = 4;        // 被点赞次数，仅总榜前三名可被点赞
  cl.UserSnapshot snapshot = 5;  // 玩家快照
}

message WrestleHallOfFame {
  repeated WrestleFighterRecord record = 1;  // 荣耀殿堂数据
}

message ModuleAttr3 {
  cl.LinkInfo link_info = 1;                         // 联结信息
  cl.FlowerOccupyNum flower_occupy_num = 2;          // 密林据点模式，玩法次数
  cl.GoddessContractInfo goddess_contract_info = 3;  // 契约之所
  repeated uint32 emblem_auto_decompose_rare = 4;    // 自动符文的装备品质
  ActivityMonthTasks month_tasks = 5;                // 全民无双活动
  map<uint32, cl.DailyWish> daily_wish = 6;          // 每日许愿
  cl.LinkHeroSummon link_hero_summon = 7;            // 流派抽卡
  cl.ArtifactDebut artifact_debut = 8;               // 神器首发
  cl.DivineDemon divine_demon = 9;                   // 神魔抽卡; key: 唯一id
  repeated cl.RoundActivity round_activity = 10;     // 新服轮次活动
}

message ModuleAttr4 {
  cl.TowerSeason tower_season = 1;  // 百塔
  cl.GodPresents god_presents = 2;  // 神馈赠 - 已废弃
                                    //   uint32 chat_group_cross_area = 31;  // 战区id, 没有变化时，就不同步给聊天服
  reserved 31, 3, 4;
  reserved "chat_group_cross_area", "chat_group_tags", "chat_group_area";
  //  map<string, bool> chat_group_tags = 3;  // 聊天群聊tag
  //  uint32 chat_group_area = 4;             // 战区id, 没有变化时，就不同步给聊天服
  ChatGroupTag chat_group_tag = 5;
  cl.GodPresentsNew god_presents_new = 6;                         // 777抽活动数据
  bool received_h5_reward = 7;                                    // 是否已领取h5保存到桌面的奖励
  cl.DropActivity drop_activity = 8;                              // 掉落活动
  cl.DailyAttendance daily_attendance = 9;                        // 累计登录数据
  cl.DailySpecial daily_special = 10;                             // 每日特惠
  map<uint64, cl.UserGuildChestItem> user_guild_chest_item = 11;  // 用户公会红包物品
  MonthlyInfo monthly_info = 12;                                  // 月重置任务
  cl.WorldBoss world_boss = 13;                                   // 世界boss
}

message ChatGroupTag {
  map<string, bool> chat_group_tags = 3;  // 聊天群聊tag
  uint32 chat_group_area = 4;             // 战区id, 没有变化时，就不同步给聊天服
  map<string, uint32> max_peoples = 5;    // 语言子频道最多人数; key: lang
  uint64 guild_id = 6;                    // 公会id, 公会id有变化，从chat_group_tags中删除guildGroupTag
}

message ActivityMonthTasks {
  repeated cl.MonthTasks activities = 1;
}

message AchievementsShowcase {
  cl.AchievementsShowcase achieves = 1;
}

// repair修复数据版本记录
//$<ROP redis|set|rr >
message RepairRecord {
  repeated string versions = 1;  //$<ROP unique >
}

message MonthlyInfo {
  uint32 monthly_zero = 1;
  cl.MonthlyTask monthly_task = 2;  // 新的每周任务
}

// 活动结束 - 排行结算
//$<ROP redis|hash|world_boss_settle >
message WorldBossSettle {
  WorldBossSettleDataMap settle_data = 1;  // key: worldBoss sys_id
  Op settle_data_op = 2;
}

message WorldBossSettleDataMap {
  map<uint32, WorldBossSettleData> data = 1;  // key: worldBoss sys_id
}

message WorldBossSettleData {
  bool is_join = 1;    // 是否存在玩家参与了该期worldBoss,不存在，不发送结算请求
  bool is_settle = 2;  // 是否已结算
}

// 百塔 - 重置数据
//$<ROP redis|hash|tower_season_reset >
message TowerSeasonReset {
  int64 last_award_tm = 1;    // 上次赛季结算时间
  uint32 last_have_rank = 2;  // 上赛季是否有排名（1:有 0:没有 红点用，新的战区上赛季没有）
}

message ModuleAttr5 {
  cl.ActivityStory activity_story = 1;
  cl.DivineDemonWishRecord divine_demon_wish_record = 2;
  cl.AssistanceActivity assistance_activity = 3;  // 助力活动
  cl.SeasonLevel season_level = 4;                // 赛季等级相关数据
  cl.SeasonDungeon season_dungeon = 5;            // 赛季主线
  WeeklyFridayInfo weekly_friday = 6;             // 每周五刷新
  cl.ActivityReturn activity_return = 7;          // 回流
  cl.PreSeason pre_season = 8;                    // 赛季前奖励
  cl.SeasonReturn season_return = 9;              // 赛季回流
  cl.StoryReviewData story_review = 10;           // 剧情回忆录
  cl.NewYearActivity new_year_activity = 11;      // 新年活动
  bool season_rank_sync = 12;                     // 赛季排行榜同步
  repeated uint32 season_flash_back_ids = 13;     // 可以赛季回顾的赛季ID列表
}

message ModuleAttr6 {
  cl.Pyramid pyramid = 1;                         // 金字塔活动
  uint32 assistantUnlockMark = 2;                 // 小助手解锁标记（记录修改解锁条件前已解锁的玩家）
  cl.RemainItem remain_items = 3;                 // 遗物 - 使用的增加cost道具
  cl.SeasonArenaTask season_arena_task = 4;       // 赛季竞技场任务
  cl.RemainBook remain_book = 5;                  // 遗物图鉴
  cl.GSTBossUser boss_user = 6;                   // 公会boss玩家数据
  bool season_arena_calc_formation_power = 7;     // 赛季竞技场计算防守阵容战力
  Box box = 8;                                    // 开箱子玩法
  cl.LineTask line_task = 9;                      // 链式任务
  cl.ActivityTurnTable activity_turn_table = 10;  // 周年庆活动
  cl.TalentTreeBase talent_tree = 11;             // 天赋树
}

message ModuleAttr7 {
  cl.BossRush boss_rush = 1;                             // Boss挑战
  bool boss_rush_init = 2;                               // 是否初始化
  cl.SelectSummon select_summon = 3;                     // 选择抽卡
  int64 season_enter_time = 4;                           // 当前赛季进入时间
  repeated cl.SeasonCompliance season_compliances = 5;   // 赛季冲榜
  SeasonJewelryConfig season_jewelry_config = 6;         // 赛季装备
  cl.ActivityWebDatas activity_web_datas = 7;            // 官网充值数据
  repeated SeasonShop season_shops = 8;                  // 赛季商店
  cl.ActivityLifelongGifts activity_lifelong_gifts = 9;  // 终身礼包
  cl.ActivityCompliance activity_compliance = 10;        // 开服冲榜活动
  SeasonDoor season_door = 11;                           // 赛季之门
}

message ModuleAttr8 {
  cl.ComplianceTasks compliance_tasks = 1;
  cl.ActivityCoupon activity_coupon = 2;
  cl.DailyAttendanceHero daily_attendance_hero = 3;
  cl.SeasonMap season_map = 4;                      // 赛季地图
  PokemonGlobal pokemon = 5;                        // 宠物
  cl.TowerPokemon tower_pokemon = 6;                // 宠物爬塔
  cl.PokemonSummon pokemon_summon = 7;              // 宠物抽卡
  RankActivityTowerUser rank_activity_tower = 8;    // 地宫冲榜
  RankActivityMirageUser rank_activity_mirage = 9;  // 幻境冲榜
  cl.ActivityXiao7Coupon xiao7 = 10;                // 小7渠道代金券活动
  cl.ZeroDollarPurchase zero_dollar_purchase = 11;  // 零元购
}

message ResourcesWarn {
  map<uint64, uint32> daily_warn = 1;
}

// 宠物爬塔 - 重置数据
//$<ROP redis|hash|tower_pokemon_reset >
message TowerPokemonReset {
  uint32 rewarded_season = 1;  // 结算过的赛季
}

message SeasonJewelryConfig {
  map<uint64, bool> lock_config = 1;                  // 锁定配置 hero_id => bool
  cl.SeasonJewelryDecomposeCfg decompose_config = 2;  // 自动分解配置
}

message SeasonDoor {
  cl.SeasonDoor season_door = 1;
  repeated cl.SeasonDoorFightLineLog logs = 2;  // 战斗队列日志
  bool first = 3;
}

message PokemonGlobal {
  map<uint32, cl.PokemonBall> balls = 1;             // 兽栏 ball_type => ball TODO 废弃
  uint32 matser_exp = 2;                             // 大师经验(自动升级，根据经验计算等级)
  map<uint32, bool> matser_level_reward_status = 3;  // 大师等级领奖状态
  uint32 potential_level = 4;                        // 潜能等级（主动升级）TODO 废弃
  uint32 show_pokemon_id = 5;                        // 展示的宠物id TODO 废弃
}

message RankActivityTowerUser {
  bool rank_updated = 1;  // 是否已更新排行榜
}

message RankActivityMirageUser {
  bool rank_updated = 1;  // 是否已更新排行榜
}

message ModuleAttrDisorderland {
  cl.Disorderland disorderland_data = 1;
  bool guild_medal_add = 2;  // 公会勋章加成
}

//$<ROP redis|set|activity_story_rank_reset >
message ActivityStoryRankRest {
  repeated uint32 ids = 1;  //$<ROP unique >
}

message RankActivityStory {
  uint64 uid = 1;
  uint32 dungeon_id = 2;
  int64 tm = 3;       // 时间
  uint64 power = 4;   // 通关时的阵容战力
  uint32 act_id = 5;  // 活动ID
}

message WeeklyFridayInfo {
  uint32 weekly_friday_zero = 1;
  map<uint32, cl.NumInfo> num_info = 2;  // 玩法通用次数信息
}

// 巅峰竞技场
//$<ROP redis|hash|peak>
message Peak {
  PeakState state = 1;  // 状态
  Op state_op = 2;
  PeakInviteReward invite_reward = 3;  // 邀请奖励
  Op invite_reward_op = 4;
  PeakRankScore rank_score = 5;  // 排名和积分
  Op rank_score_op = 6;
}

message PeakState {
  cl.PeakState data = 1;  // 状态
}

message PeakInviteReward {
  map<uint64, bool> data = 1;  // 本服参赛者，是否已领邀请奖励 uid=>bool
}

message PeakRankScore {
  map<uint64, cl.PeakRankScore> data = 1;  // 全部参赛者赛季积分 uid=>cl.PeakRankScore
}

// 公会战
//$<ROP redis|hash|gst>
message GST {
  cl.GSTSta data = 1;
}

// 公会战
//$<ROP redis|hash|gst_last_award>
message GstLastAward {
  uint32 season_id = 1;
  uint32 round = 2;
  uint32 lround = 3;
}

// 公会战用户
//$<ROP redis|map|gst_logic_user>
message GstLogicUser {
  uint64 id = 1;  //$<ROP unique>
  uint32 season = 2;
  bool sign = 3;
  uint32 round = 4;
  map<uint32, bool> task_awarded = 5;
  uint32 gst_task_point = 6;
  bool base_info_sync = 7;                                              // 基础信息同步标志位
  bool team_info_sync = 8;                                              // 队伍信息同步标志位
  string chat_room_id = 9;                                              // 聊天频道id
  map<uint32, cl.GSTBuildUserDispatchHeroes> build_dispatch_hero = 10;  // 建筑内派遣英雄
  bool build_dispatch_sync = 11;                                        // 建筑派遣英雄同步标志位
  uint64 guild_id = 12;                                                 // 公会ID
  uint32 log_index_id = 13;                                             // 日志序号Id
  bool challenge_formation_limit = 14;                                  // 新擂台赛阵容限制
}

// 公会战
//$<ROP redis|hash|gst_last_first_center_award>
message GstLastFirstCenterAward {
  uint32 season_id = 1;
  uint32 round = 2;
  uint32 lround = 3;
}

// 公会战龙战上次发奖
//$<ROP redis|hash|gst_dragon_last_award>
message GstDragonLastAward {
  uint32 season_id = 1;
  uint32 dragon_round = 2;
}

// 公会战擂台赛上次发奖
//$<ROP redis|hash|gst_challenge_last_award>
message GstChallengeLastAward {
  uint32 season_id = 1;
  uint32 round = 2;
}

// 公会竞赛上次发奖
//$<ROP redis|hash|gst_mob_last_award>
message GstMobLastAward {
  uint32 mob_round = 1;
}

// 羁绊激活数据
//$<ROP redis|set|seasonlink_activation:uint64>
message SeasonLinkActivation {
  repeated uint32 activated_heroes = 1;  //$<ROP unique >
}

// 人群包信息
//$<ROP redis|map|people_group_package>
message PeopleGroupPackage {
  uint32 id = 1;  //$<ROP unique>
  repeated uint64 uids = 2;
}

//$<ROP redis|map|season_arena_user>
message SeasonArenaUser {
  uint64 id = 1;  //$<ROP unique>
  uint32 season = 2;
  uint32 round = 3;
  uint32 score = 4;
  uint32 top_division = 5;
  uint64 recv_division = 6;          // 领奖段位 位存储
  uint32 daily_challenge_count = 7;  // 每日挑战次数
  int64 refresh_opponent_time = 8;   // 刷新对手时间
  reserved 9;
  reserved "arena_task";
  reserved 11;
  reserved "free_refresh";
  uint32 rank = 10;
  uint32 cur_division = 12;       // 当前缓存段位
  uint32 log_index_id = 13;       // 日志序号Id
  bool free_refresh_flag = 14;    // 免费刷新标志位
  int64 daily_refresh_time = 15;  // 每日次数恢复刷新时间
  uint32 last_round_score = 16;   // 上一轮积分
  uint32 last_round_rank = 17;    // 上一轮排名
  bool new_fight_log = 18;        // 新战斗日志
  reserved "is_last_reset_score";
  reserved 19;
  bool need_clear_task = 20;  // 是否清除任务信息
  bool skip_formation = 21;   // 跳过布阵
}

//$<ROP redis|hash|season_arena_last_award>
message SeasonArenaLastAward {
  uint32 season = 1;
  uint32 round = 2;
  GuildMedalSeasonArenaBakOpData data = 3;  // 公会勋章数据需要的玩家数据
  Op data_op = 4;
}

message GuildMedalSeasonArenaBakOpData {
  repeated cl.GuildMedalSeasonArenaBak list = 1;  // 公会勋章数据需要的玩家数据,发给跨服,跨服返回OK就删除
}

//$<ROP redis|hash|season_arena_top_user>
message SeasonArenaTopUser {
  SeasonArenaRankUser top_user = 1;
  Op top_user_op = 2;
}

message SeasonArenaRankUser {
  cl.RankValue user = 1;
}

// 开箱子信息
message Box {
  int32 point = 1;       // 分值
  uint32 cur_group = 2;  // 当前组
  uint32 cur_id = 3;     // 当前id
}

//$<ROP redis|map|logic_hot_rank_user_formation:uint64> //阵容ID + uid%10
message LogicHotRankUserFormation {
  uint64 uid = 1;                                    //$<ROP unique>
  uint32 progress = 2;                               // 对应玩法下的进度
  int64 update_time = 3;                             // 更新时间
  uint32 formation_id = 4;                           // 阵容ID
  repeated HotFormationBase hot_formation_base = 5;  // 热门阵容
}

message HotFormationBase {
  reserved 1, 2, 3;
  reserved "heroes", "red_artifacts", "orange_artifacts";
  repeated uint32 red_emblem_skills = 4;
  repeated uint32 orange_emblem_skills = 5;
  string hot_formation = 6;
}

//$<ROP redis|map|logic_hot_rank_collect>
message LogicHotRankCollectionLog {
  uint32 rank_id = 1;  //$<ROP unique>
  map<uint32, uint32> heroes = 2;
  map<uint32, uint32> red_artifacts = 4;
  map<uint32, uint32> orange_artifacts = 5;
  map<uint32, uint32> red_emblem_skills = 6;
  map<uint32, uint32> orange_emblem_skills = 7;
  map<string, uint32> hot_formation = 8;
  map<string, HotRankHeroCountArtifactEmblemLog> log = 9;
}

message HotRankHeroCountArtifactEmblemLog {
  uint32 formation_count = 1;
  map<uint32, uint32> red_artifacts = 2;
  map<uint32, uint32> orange_artifacts = 3;
  map<uint32, uint32> red_emblem_skills = 4;
  map<uint32, uint32> orange_emblem_skills = 5;
}

// 冲榜活动
//$<ROP redis|hash|activity_tower>
message ActivityTower {
  // 开服冲榜发奖后删除排行榜，不需要记录发奖状态
  uint32 rank_award = 1;                 // 地宫冲榜发奖状态
  uint32 mirage_award = 2;               // 幻境冲榜发奖状态
  int64 tower_season_last_award_tm = 3;  // 百塔冲榜上次发奖时间
}

// 开服冲榜排行榜
message RankActivityCompliance {
  uint64 uid = 1;
  uint32 score = 2;
  int64 tm = 3;           // 时间
  uint64 power = 4;       // 上榜时的战力
  uint32 like_count = 5;  // 点赞次数
}

// 爬塔冲榜排行榜
message RankActivityTower {
  uint64 uid = 1;
  uint32 floor_id = 2;    // 层数
  int64 tm = 3;           // 时间
  uint64 power = 4;       // 通关时的阵容战力
  uint32 like_count = 5;  // 点赞次数
}

// 幻境冲榜排行榜
message RankActivityMirage {
  uint64 uid = 1;
  uint32 total_floor = 2;  // 所有Boss的最高层数之和
  int64 tm = 3;            // 时间
  uint64 power = 4;        // 通关时的阵容战力
  uint32 like_count = 5;   // 点赞次数
}

//$<ROP redis|hash|logic_talent_tree_hot>
message LogicTalentTreeHot {
  LogicTalentTreeHotData data = 1;
  Op data_op = 2;
}

message LogicTalentTreeHotData {
  repeated cl.TalentTreeNodeHot nodes = 1;
  int64 last_sync_time = 2;
}

// 道具信息
message OfflineItem {
  uint32 type = 1;
  uint32 value = 2;
  int32 count = 3;  // 改变数量
  uint32 reason = 4;
  uint64 subReason = 5;
}

//$<ROP redis|map|offline_resource>
message OfflineResource {
  uint64 id = 1;  //$<ROP unique> 玩家id
  repeated OfflineItem items = 2;
}

//$<ROP redis|hash|season_compliance_user>
message SeasonComplianceUser {
  uint32 phase = 1;
  map<uint32, UserSnapshots> top3_users = 2;
}

message UserSnapshots {
  repeated cl.UserSnapshot user_snapshot = 1;
}

// 赛季冲榜活动
//$<ROP redis|hash|season_compliance_award>
message SeasonComplianceAward {
  DbSeasonComplianceAwardStage award_stage = 1;  // 赛季冲榜奖励时间
  Op award_stage_op = 2;
}

message DbSeasonComplianceAwardStage {
  cl.SeasonComplianceStage stage = 1;
}

// 赛季商店
message SeasonShop {
  uint32 shop_id = 1;                  // 商店id
  map<uint32, uint32> goods_list = 2;  // 商品列表 sysGoodsId => boughtNum
}

//$<ROP redis|map|hotfixcode >
message HotfixCode {
  uint64 version = 1;  //$<ROP unique >
  uint32 type = 2;
  string title = 3;
  string code = 4;
}

// 女神夺宝活动
//$<ROP redis|hash|goddess_prize_draw>
message GoddessPrizeDraw {
  int64 daily_time = 1;                   // 零点的时间
  GoddessPrizeDrawUsers apply_users = 2;  // 参加的用户
  Op apply_users_op = 3;
  GoddessPrizeDrawUsers win_users = 4;  // 中奖的用户
  Op win_users_op = 5;
  // bool draw = 6;  // 是否开奖
  uint32 draw_int = 7;  // 是否开奖 bool值在生成的代码里读不出来
}

// 加这个结构是因为hash的字段不能直接为[]uint64 []string这种类型
message GoddessPrizeDrawUsers {
  repeated uint64 users = 1;
  repeated string user_names = 2;
}

// 公平竞技场
//$<ROP redis|hash|balance_arena>
message BalanceArena {
  BalanceArenaReward reward = 1;
  Op reward_op = 2;
}
message BalanceArenaReward {
  cl.BalanceArenaState state = 1;
  bool big_group_rewarded = 2;    // 大组赛奖励完成
  bool small_group_rewarded = 3;  // 小组赛奖励完成
  bool elimination_rewarded = 4;  // 淘汰赛奖励完成
  bool season_end_rewarded = 5;   // 赛季奖励完成
}
