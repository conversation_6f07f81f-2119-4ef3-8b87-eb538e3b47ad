package r2l

import (
	"app/protos/in/db"
	"app/protos/out/cl"

	"github.com/gogo/protobuf/proto"
)

type L2R_GRPC_Login struct {
	ID       uint64
	Callback func(proto.Message)
}

type R2L_GRPC_Login struct {
	ID       uint64
	Callback func(proto.Message)
	Data     *R2L_Login
}

type L2R_GRPC_GetUserID struct {
	Type     uint32
	Values   []string
	IDs      []uint64
	Callback func(interface{})
}

type R2L_GRPC_GetUserID struct {
	Type     uint32
	Values   []string
	IDs      []uint64
	Callback func(interface{})
}

type L2R_GRPC_ChangeDungeonID struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_ChangeDungeonID struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_GuidanceClose struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值: 0 关闭
	Callback func()
}

type R2L_GRPC_GuidanceClose struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetUserLevel struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetUserLevel struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ClearUserResource struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type R2L_GRPC_ClearUserResource struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ActivityInfo struct {
	ID       uint64 // 玩家ID
	Callback func(proto.Message)
}

type R2L_GRPC_ActivityInfo struct {
	ID       uint64 // 玩家ID
	Callback func(proto.Message)
	Data     *R2L_UserBase
}

type L2R_GRPC_ResetDailyNum struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type R2L_GRPC_ResetDailyNum struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetMedalLevel struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetMedalLevel struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetMaze struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type R2L_GRPC_ResetMaze struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetGuildQuitTm struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type R2L_GRPC_ResetGuildQuitTm struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetTowerFloor struct {
	ID       uint64 // 玩家ID
	Type     uint32 // 塔的类型
	Value    uint32 // tower floor
	Callback func()
}

type R2L_GRPC_ResetTowerFloor struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetTrialLevel struct {
	ID       uint64 // 玩家ID
	Type     uint32 // 材料本的类型
	Level    uint32 // level
	Callback func()
}

type R2L_GRPC_ResetTrialLevel struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetMirageFloor struct {
	ID       uint64 // 玩家ID
	HurdleID uint32 // 关卡
	Callback func()
}

type R2L_GRPC_ResetMirageFloor struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetScore struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_ResetScore struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetTaleChapterFinish struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_ResetTaleChapterFinish struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetTaleElite struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_ResetTaleElite struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetUserResource struct {
	ID        uint64         // 玩家ID
	Resources []*cl.Resource // 要修改的资源
	Callback  func()
}

type R2L_GRPC_SetUserResource struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_AddOrder struct {
	Order    *db.Order
	Callback func(uint32, *db.Order)
}

type R2L_GRPC_AddOrder struct {
	Ret      uint32
	Order    *db.Order
	Callback func(uint32, *db.Order)
}

type L2R_GRPC_AddWaitRefundOrder struct {
	Order    *db.Order
	Callback func(uint32, *db.Order)
}

type R2L_GRPC_AddWaitRefundOrder struct {
	Ret      uint32
	Order    *db.Order
	Callback func(uint32, *db.Order)
}

type L2R_GRPC_SetUserVip struct {
	ID       uint64 // 玩家ID
	Vip      uint32 // 要修改vip
	VipExp   uint32 // 要修改vipExp
	Callback func()
}

type R2L_GRPC_SetUserVip struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetTowerstarDungeonID struct {
	ID       uint64 // 玩家ID
	Value    uint32
	Callback func()
}

type R2L_GRPC_SetTowerstarDungeonID struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ResetTowerstar struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type R2L_GRPC_ResetTowerstar struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetQuestionnaireFinish struct {
	ID              uint64 // 玩家ID
	QuestionnaireID uint32 //问卷ID
	Callback        func(uint32, uint64, uint32)
}

type R2L_GRPC_SetQuestionnaireFinish struct {
	Ret             uint32
	ID              uint64 // 玩家ID
	QuestionnaireID uint32 //问卷ID
	Callback        func(uint32, uint64, uint32)
}

type L2R_GRPC_GetRechargeList struct {
	ID                 uint64 // 玩家ID
	Callback           func(proto.Message)
	StartTime, EndTime int64 // 开始时间，结束时间; 时间区间
}

type R2L_GRPC_GetRechargeList struct {
	ID       uint64 // 玩家ID
	Callback func(proto.Message)
	Datas    *R2L_GetRechargeList
}

type L2R_GRPC_CloseGuidance struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值: 0 关闭
	Callback func()
}

type R2L_GRPC_CloseGuidance struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetMazeTaskLevel struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetMazeTaskLevel struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_DeleteUserMail struct {
	ID       uint64   // 玩家ID
	Deletes  []uint64 // 要删除的邮件
	Callback func(uint32)
}

type MailListFormation struct {
	Mails map[uint64]*cl.Mail
	Lang  string
}

type L2R_GRPC_GetUserMailList struct {
	ID       uint64 // 玩家ID
	Callback func(uint32, *MailListFormation)
}

type L2R_GRPC_SetAccountTag struct {
	ID        uint64 // 玩家ID
	Value     uint32 // 要修改的值
	MuteValue uint32 // 禁言
	Ret       uint32
	Callback  func(uint32, *db.User)
	Data      *db.User
}

type R2L_GRPC_SetAccountTag struct {
	ID       uint64 // 玩家ID
	Ret      uint32
	Callback func(uint32, *db.User)
	Data     *db.User
}

type L2R_GRPC_ImportUser struct {
	Data     *R2L_Login
	Callback func()
}

type R2L_GRPC_ImportUser struct {
	Data     *R2L_Login
	Callback func()
}

type L2R_GRPC_SetDispatchLevel struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetDispatchLevel struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_DelChatGroupTag struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type R2L_GRPC_DelChatGroupTag struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetDailyAttendance struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetDailyAttendance struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetRiteRare struct {
	ID       uint64 // 玩家ID
	RiteID   uint32
	Rare     uint32
	Grids    []*cl.RiteGrid
	Callback func()
}

type R2L_GRPC_SetRiteRare struct {
	ID       uint64 // 玩家ID
	Callback func()
}

// 删除玩家货币
type L2R_GRPC_DeleteCurrencies struct {
	ID            uint64 //
	DelCurrencies []*cl.Resource
	Callback      func(uint32)
}

type R2L_GRPC_DeleteCurrencies struct {
	Ret           uint32
	ID            uint64 //
	DelCurrencies []*cl.Resource
	Callback      func(uint32)
}

type L2R_GRPC_SetDisorderLand struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetDisorderLand struct {
	ID       uint64 // 玩家ID
	Callback func()
}

// 删除道具
type L2R_GRPC_ChangeBags struct {
	UserId   uint64
	Bag      *db.Bags
	BagOp    *db.Op
	Callback func(uint32, []*cl.Resource)
}

type R2L_GRPC_ChangeBags struct {
	Ret      uint32
	ID       uint64 //
	Bag      *db.Bags
	BagOp    *db.Op
	Callback func(uint32, []*cl.Resource)
}

type L2R_GRPC_ChangeUserName struct {
	UserId   uint64
	Callback func()
}

type R2L_GRPC_ChangeUserName struct {
	Ret      uint32
	Callback func()
}

type L2R_GRPC_SetSeasonLink struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetSeasonLink struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetDailyAttendanceHero struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_SetDailyAttendanceHero struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_ChangeTowerPokemonDungeonID struct {
	ID       uint64 // 玩家ID
	Value    uint32 // 要修改的值
	Callback func()
}

type R2L_GRPC_ChangeTowerPokemonDungeonID struct {
	ID       uint64 // 玩家ID
	Callback func()
}

type L2R_GRPC_SetOfflineVipAward struct {
	ID                uint64 // 玩家ID
	OfflineVipAwardID string //奖励ID
	Callback          func(uint32)
}

type R2L_GRPC_SetOfflineVipAward struct {
	Ret               uint32
	ID                uint64 // 玩家ID
	OfflineVipAwardID string //奖励ID
	Callback          func(uint32)
}
