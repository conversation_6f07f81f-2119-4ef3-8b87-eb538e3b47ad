syntax = "proto3";

import "config.proto";

option go_package = "app/protos/in/l2cm";

package l2cm;

enum ID {
  MSG_NONE = 0;
  MSG_MIN = 500000;
  MSG_MAX = 600000;
  MSG_BEGIN = 500001;
  MSG_END = 599999;

  // 服务器与服务器之间使用
  MSG_L2CM_SayHi = 500002;
  MSG_CM2L_SayHi = 500003;
  MSG_L2CM_KeepAlive = 500004;
  MSG_CM2L_KeepAlive = 500005;

  //
  MSG_L2CM_GetServerPartition = 500100;  // 获取分区情况
  MSG_CM2L_GetServerPartition = 500101;
  MSG_CM2L_UpdateServerPartition = 500102;  // 更新分区情况
}

message L2CM_SayHi {
  uint64 id = 1;
  repeated uint64 current = 2;
  repeated uint64 del = 3;
}

message CM2L_SayHi {}

message L2CM_KeepAlive {
  string extra = 1;  // json
}

message CM2L_KeepAlive {
  string extra = 1;  // json
}

message CrossActPart {
  uint32 act_id = 1;          // 活动id
  uint32 partition = 2;       // 对应的分区, 分区有三种类型,服务器分区,战区,大战区
  uint32 session = 3;         // 对应分区的sessionconfig.CrossNode _addr = 4;  //节点
  config.CrossNode node = 4;  // 节点
}

message L2CM_GetServerPartition {
  uint64 sid = 1;
}

message CM2L_GetServerPartition {
  uint32 ret = 1;
  uint64 sid = 2;
  repeated CrossActPart parts = 3;
  repeated uint64 normal_part_sids = 4;  // 同战区的服务器ID列表
}

message CM2L_UpdateServerPartition {
  uint32 ret = 1;
  uint64 sid = 2;
  CrossActPart part = 3;                 // 某一个活动的分区情况
  repeated uint64 normal_part_sids = 4;  // 同战区的服务器ID列表
}
