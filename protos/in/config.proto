syntax = "proto3";

option go_package = "app/protos/in/config";

import "cr.proto";
// config in etcd
package config;

message GatewayCluster {
  string etcd_servers = 1;
  string target = 2;
  string guard = 3;
  string white_list = 4;
  string function_status = 5;
  string oss_list = 6;
}

message Platform {
  string gift_code_url = 1;
  string secret = 2;
  uint32 parallel = 3;
  string pay_rebate_url = 4;
  string web_proxy_url = 5;
  string questionnaire = 6;
  string gift_code = 7;
  string link_summon = 8;
  Chat chat = 9;
  string cross_master_prefix = 10;
  string cross_master_addr = 11;
  string activity_mix = 12;
  string link_setting = 13;
  string coupon_cfg = 14;
  string refund_email = 15;
  string sdk_push_addr = 16;  // 推送地址
  string sdk_app_id = 17;
  string sdk_secret_key = 18;
  string server_type = 19;               // 服务器类型：台服，海外服，国服等
  string season_flash_back_url = 20;     // 赛季回顾数据获取地址
  string sdk_app_name = 21;              // 推送 app name
  string sensitive_word_check_url = 22;  // 屏蔽字检测地址
  string channel = 23;                   // 屏蔽字渠道
  string activity_schedule_target = 24;  // 活动日历
  string es_endpoints = 25;              // es地址("http://**********:9200;http://************:9200;http://************:9200")
  string rebase_url = 26;                // 官服开服返利地址
  string mute_url = 27;                  // 封号地址
}

message Logic {
  uint64 server_id = 1;
  uint64 app_id = 2;
  uint64 op_group = 3;
  uint64 node_id = 4;
  uint32 acc_parallel = 5;
  uint32 redis_index = 6;
  GatewayCluster gateway_cluster = 7;
  LogicService logic = 8;
  string acc_address = 9;
  string redis = 10;
  Platform platform = 11;
  int64 start_time = 12;
  bool enable_login_limit = 13;
  string server_name = 14;
  uint32 censor_parallel = 15;
  string censor_address = 16;
  uint32 status = 17;
  uint32 oss_parallel = 18;
  uint32 mongo_parallel = 19;
  string mongo_address = 20;
  uint32 is_ahead = 21;
}

message LogicService {
  int32 account_size = 1;
  int32 online_size = 2;
  int32 queue_pop_size = 3;
  int32 max_cache_entries = 4;
  int64 user_save_interval = 5;
  uint32 queue_size = 6;
  bool enable_trace_log = 7;
  int32 ip_accounts = 8;
  int32 device_id_accounts = 9;
  int32 ip_accounts_hw = 10;
  int32 device_id_accounts_hw = 11;
  bool enable_crypto = 12;
  int64 user_cache_interval = 13;
  bool battle_debug = 14;
  bool ban_register = 15;
  string self_cross_master = 16;
  Ahead ahead_config = 17;  // 先行服相关配置
}

// 先行服配置
message Ahead {
  string log_topic_dc = 1;
  string log_topic_resource = 2;
  string chat_addr = 3;   // 聊天addr: ip+port
  string mongo_addr = 4;  // 通关记录addr: ip+port
}

// 聊天配置
message Chat {
  //  string health_url = 1;         // 服务健康检查url
  //  string get_token_url = 2;      // 获取token的url
  //  string sync_user_url = 3;      // 同步玩家数据的url
  //  string join_group_url = 4;     // 加入群组的url
  //  string dismiss_group_url = 5;  // 解散群组的url
  //  string send_msg_url = 6;       // 发送消息的url
  //  string ban_url = 7;            // 禁言/解禁
  //  string ahead_addr = 8;         // 先行服 ip+port
  string current_addr = 9;  // 当前服 ip+port

  reserved 1, 2, 3, 4, 5, 6, 7, 8;
  reserved "health_url", "get_token_url", "sync_user_url", "join_group_url", "dismiss_group_url", "send_msg_url", "ban_url", "ahead_addr";
}

// 玩法配置.每个玩法有自己的独立的数据库.
// 在跨服服务器通过etcd来查找.但是跨服服务器通过哪个key来查找呢？
message CrossAct {
  uint32 id = 1;           // 玩法id. 0为默认玩法
  string redis_addr = 5;   // redis地址
  uint32 redis_index = 6;  // redis db index
  repeated uint32 nodes = 7;  // 所有的节点. 这里其实只要一个id就够了。但是一个CrossAct能完整表达一个意思。这里就所有数据都有了.配置的时候值配置ID.生成的时候补全数据.
                              // 想要补全数据用其他方式
  CrossActDb db = 8;  // 数据库信息
}

message CrossActDb {
  string redis_addr = 1;
  uint32 redis_index = 2;
  string mongo_addr = 3;
}

// 跨服管理节点。对应所有玩法的请求都发送到管理节点。然后管理节点转发给对应的子节点
//  TODO:是否可以让logic直接链接到对应的CrossChildNode.直接进行交互?好处是减少了消息的延迟.
//  每个游戏服直接通过etcd配置一个地址标记是跨服配置重点点。如果想要自己单独配置.就走logic.xml配置.覆盖etcd里的
//  上面的做法.在后台没办法查询这个节点的一些信息. 因为不知道自己的节点在哪。单独配置还是走logic的etcd来配置
//  这个节点每个服务器只会链接一个。不会有多个。所以可以和运营商区分开来，直接一个地址访问过去就好了
message Cross {
  uint32 id = 1;  // 服务器id
  string cross_addr = 2;
  // repeated uint32 act_ids = 3;  //所有玩法id 去掉
  string redis_addr = 4;
  uint32 redis_index = 5;
  repeated CrossAct acts = 6;    // 所有玩法. 方便直接查找到信息。多个cross的情况下好匹配. 直接用id多个分区的情况下不好控制
  repeated CrossNode nodes = 7;  // 所有的节点，直接放在cross里面.方便查找
  string grpc = 8;               // grpc地址
  uint32 op_group = 9;           // 运营组
}

// 各个分区之间基本没有交互，如果有交互的话通过数据库来操作.或者通过manager来中转
// 每个子节点必须收到manager的指令才开始load数据和启动.
// 在后台单独配置. 也更新到etcd 按照地址单独区分.每个cross也能拉取到所有的node.对外暴露grpc端口.可以直接查询这个node的一些信息
message CrossNode {
  uint32 id = 1;         // 标记id.
  string http_addr = 2;  // 对master开放的端口
  string grpc_addr = 3;  // 对外暴露的grpc_addr地址
                         //  uint32 act_id = 3;       //玩法id
                         //  string redis_addr = 4;   // redis地址. 数据库可以考虑不分子节点。一个玩法分一个数据库就够了.
                         //  uint32 redis_index = 5;  // redis db index
}

// 跨服分区信息
message Partition {
  uint32 id = 1;                       // 分区ID
  map<uint64, uint32> server_ids = 2;  // 空代表所有服务器在一个分区中
  string cross_addr = 3;
}

message Partitions {
  repeated Partition data = 1;
}

// 消息路由
message Router {
  Partitions parts = 1;  // 分区信息
}

message WhiteList {
  string ips = 1;
  string uuids = 2;
  string device_ids = 3;
}

message FunctionStatus {
  repeated FunctionStatusConfig config = 1;
}

message FunctionStatusConfig {
  uint32 func_id = 1;
  uint32 value = 2;
}

message RefundMailTemplate {
  string title = 1;
  string txt = 2;
}

message ETCDCrossPartition {
  map<uint32, ETCDCrossActivityPartition> act_partition = 1;  // actID=>
  string cross_master_net_addr = 2;
}

message ETCDCrossActivityPartition {
  map<uint64, cr.ActPartition> partition = 1;  // 服务器id或者分区id=>分区id或者超级分区id
}
