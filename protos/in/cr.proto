syntax = "proto3";

option go_package = "app/protos/in/cr";

// import "db.proto";
import "cl.proto";
import "db.proto";

package cr;

enum Op {
  None = 0;
  Dirty = 1;
  Delete = 2;
}

// 跨服战数据
message Wrestle {
  Matcher matcher = 1;
  map<uint64, Fighter> fighters = 2;
  WrestleLastSeasonTop top = 3;
  repeated Fighter baks = 4;
}

//$<ROP redis|hash|cw_m:uint32> //cw_m:跨服组id
message Matcher {
  int64 daily_reset_time = 1;      // 每日重置时间
  int64 season_reset_time = 2;     // 赛季重置时间
  int64 partition_reset_time = 3;  // 分组重置时间
}

//$<ROP redis|map|cw_f:uint32> //cw_f:跨服组id
message Fighter {
  uint64 uid = 1;                 //$<ROP unique > 玩家ID
  uint64 sid = 2;                 // 服务器ID
  uint32 score = 3;               // 玩家积分
  int64 change_time = 4;          // 积分更新时间
  uint32 room_id = 5;             // 房间id
  uint32 level = 6;               // 战场等级
  uint32 rank = 7;                // 排名，仅在顶级战场时有意义（level=5）
  repeated uint64 revenge = 8;    // 可复仇玩家列表
  repeated uint64 defeated = 9;   // 被打败玩家列表
  cl.UserSnapshot snapshot = 10;  // 玩家快照
  RankBakData last_score = 11;    // 上一次结算后的数据
  uint32 last_room_rank = 12;     // 上一次结算时，房间内的排名，仅对结算时处于非顶级战场的玩家有意义
  uint32 bot_id = 13;             // 机器人在量表中的id（真实玩家此值为0）
}

// 结算数据备份
message RankBakData {
  uint32 level = 1;  // 战场等级
  uint32 rank = 2;   // 玩家排名
  uint32 score = 3;  // 积分
  uint64 uid = 4;
}

// 荣耀殿堂数据（上一赛季跨服战排行数据）
//$<ROP redis|map|cw_wlst>
message WrestleLastSeasonTop {
  uint32 partition = 1;                  //$<ROP unique > //跨服组id
  int64 time = 2;                        // 数据更新时间
  repeated FighterRecord snapshots = 3;  // 排行榜快照
}

message FighterRecord {
  uint32 level = 1;              // 战场等级
  uint32 rank = 2;               // 总排名
  uint32 score = 3;              // 积分
  uint32 liked_count = 4;        // 被点赞次数，仅总榜前三名可被点赞
  cl.UserSnapshot snapshot = 5;  // 玩家快照
}

// 跨服战玩家数据备份
//$<ROP redis|map|cw_fb>
message FightersBak {
  uint64 sid = 1;                 //$<ROP unique > 服务器ID
  repeated Fighter fighters = 2;  // 玩家备份数据
  int64 time = 3;                 // 数据备份操作，所处赛季重置时间
}

// 玩法分组信息
//$<ROP redis|map|actp:uint64> //actp:跨服玩法id
message ActPartition {
  uint64 sid = 1;         //$<ROP unique> 服务器ID或者分区ID
  uint32 last_part = 2;   // 上个赛季分组
  uint32 now_part = 3;    // 当前赛季分组, 或者当前赛季的大分组
  uint32 next_part = 4;   // 下个赛季分组
  uint64 reset_time = 5;  // 重置时间
}

// 玩法信息
//$<ROP redis|hash|actb:uint64> //actb:跨服玩法id
message ActBase {
  uint64 reset_time = 1;       // 重置时间
  uint64 real_reset_time = 2;  // 实际执行的时间, 只是做标记用，没有实际价值
}

// repair修复数据版本记录
//$<ROP redis|set|rr >
message RepairRecord {
  repeated string versions = 1;  //$<ROP unique >
}

// 公会
//$<ROP redis|map|guild >
message Guild {
  uint64 id = 1;                             //$<ROP unique >
  string name = 2;                           // 昵称
  uint32 badge = 3;                          // 头像
  uint32 level = 4;                          // 等级
  uint32 exp = 5;                            // 公会经验
  string notice = 6;                         // 公告
  string declaration = 7;                    // 宣言
  string language = 8;                       // 语言
  uint32 label = 9;                          // 标签
  repeated GuildApplyInfo apply_infos = 10;  // 玩家申请列表
  uint32 join_type = 11;                     // 入会条件(0:自动加入 1:需要申请 2:拒绝加入)
  int64 update_name_tm = 12;                 // 更新公会名的时间
  int64 daily_zero = 13;
  uint64 leader = 14;                                              // 会长id
  int64 leader_login_tm = 15;                                      // 会长最近一次上线时间(自动转让会长用)
  repeated uint64 deputy = 16;                                     // 副会长ids
  uint32 log_index_id = 17;                                        // 公会日志序号id
  uint32 donate_point_num = 18;                                    // 今日总捐赠点数
  repeated uint64 donate_person_ids = 19;                          // 每日捐赠人ID
  uint64 notice_id = 20;                                           // 公告ID
  uint64 sid = 21;                                                 // 创建时的服务器ID
  int64 last_access_time = 22;                                     // 上次访问时间
  map<uint64, GuildMember> members = 23;                           // 公会成员 key:uid value：成员列表
  uint32 lv_limit = 24;                                            // 等级限制
  int64 power_limit = 25;                                          // 战力限制
  int64 create_tm = 26;                                            // 创建时间
  map<uint32, cl.Avatar> badges = 27;                              // 获得的公会徽章 ，这里复用了下cl.Avatar
  map<uint64, GuildChest> guild_chests = 28;                       // key:公会宝箱的唯一ID value:该用户分享的资源
  uint32 kick_count = 29;                                          // 当天踢玩家次数
  repeated uint64 regiments = 30;                                  // 团长ids
  GuildMedals medal = 31;                                          // 功勋
  repeated GuildCombineApplyInfo source_combine_apply_infos = 32;  // 发起的公会合并申请列表
  repeated GuildCombineApplyInfo target_combine_apply_infos = 33;  // 收到的公会合并申请列表
  GuildMobilizationData guild_mobilization_data = 34;
  bool notice_ban = 35;  // 公告禁止修改
}

message GuildMedals {
  repeated cl.GuildMedal medals = 1;  //   功勋
}

//$<ROP redis|map|g_user:uint32> //  uint32: 战区ID
message GuildCrossUser {
  uint64 id = 1;  //$<ROP unique >
  uint64 sid = 2;
  GuildMedalTowerSeason tower_season = 3;
  GuildMobilizationGuildMember mobilization_data = 4;
}

message GuildMedalTowerSeason {
  uint32 season_tower_floor = 1;
  int64 tm = 2;
}

//$<ROP redis|map|g_p_u> //  uint32: 战区ID
message GuildPeakPhaseUserBak {
  uint32 area_id = 1;  //$<ROP unique >
  map<uint64, PeakPhaseUserBak> data = 2;
  int64 tm = 3;
}

//$<ROP redis|map|g_wb_u> //  uint32: 战区ID
message GuildWorldBossUserBak {
  uint32 area_id = 1;  //$<ROP unique >
  map<uint64, WorldBossUserRankBak> data = 2;
  int64 tm = 3;
}

//$<ROP redis|map|g_sa_u:uint32> //  uint32: 战区ID
message GuildSeasonArenaBak {
  uint64 sid = 1;  //$<ROP unique >     服务器ID
  map<uint64, cl.GuildMedalSeasonArenaBak> data = 2;
}

message GuildChest {
  uint32 chest_id = 1;               // 表格ID
  uint64 uid = 2;                    // 用户ID
  uint64 id = 3;                     // 生成的唯一ID
  int64 expire_time = 4;             // 过期时间
  cl.Resource surplus_resource = 5;  // 剩余资源
  uint32 recv_like = 6;              // 已收到的花
  map<uint64, cl.GuildChestSlotDetail> recv_users = 7;
  string name = 8;                   //  创建者名字
  uint32 level = 9;                  //  创建者等级
  uint32 avatar_id = 10;             //  创建者头像
  uint32 create_flower = 11;         //  创建时创建者的花朵
  uint32 recv_chest_max_limit = 12;  //  该宝箱领取人数上限
  uint32 task_id = 13;               // 宝箱来源任务ID
}

message GuildApplyInfo {
  uint64 sid = 1;
  uint64 uid = 2;
  string name = 3;
}

message GuildCombineApplyInfo {
  uint64 gid = 1;
  uint32 type = 2;       // 申请类型 1 -申请加入 2 -邀请被加入
  int64 apply_time = 3;  // 申请发起时间
}

message GuildMembers {
  repeated GuildMember members = 1;
}

message GuildMember {
  uint64 id = 1;                                     // 玩家ID
  string name = 2;                                   // 玩家昵称   公会记录日志需要
  uint32 grade = 3;                                  // 职位
  uint64 sid = 4;                                    // 服务器ID
  uint64 weekly_damage = 5;                          // 副本周伤害
  repeated uint32 activity_point = 6;                // 下标0-6 对应星期1-星期天的活跃度
  uint64 weekly_fight_times = 7;                     // 副本周攻打次数
  GuildMemberMedalLike medal_like = 8;               // 功勋送花
  uint32 base_id = 9;                                // 头像、头像框
  repeated int64 expire_time = 10;                   // 头像、头像框过期时间
  int64 last_leave_tm = 11;                          // 上次离开公会的时间
  repeated cl.GuildLeaveCount guild_leave_cnt = 12;  // 离开公会次数
}

message GuildMemberMedalLike {
  repeated string liked_name = 1;  //  最近送花的3个人的名称
  uint32 weekly_like_count = 2;    // 本周送花的总数
  map<uint32, uint32> total_like_count = 3;
  uint32 last_weekly_like_count = 4;          // 上周送花的总数
  repeated string last_weekly_like_name = 5;  // 上周最后送花的3个人
}

//$<ROP redis|map|commonrank:uint64 >
message CommonRank {
  uint64 uid = 1;  //$<ROP unique >
  bytes data = 2;  //
}

// 公会等级排行榜
message RankGuildLevel {
  uint64 gid = 1;
  uint32 level = 2;  // 等级
  uint32 exp = 3;    // 公会经验
  int64 tm = 4;      // 时间
  cl.GuildSnapshot snapshot = 5;
  uint64 sid = 6;  // 公会创建的时候的sid
}

// 公会活跃度排行榜
message RankGuildActivityPoint {
  uint64 gid = 1;
  uint32 activity_point = 2;  // 活跃度
  int64 tm = 3;               // 时间
  cl.GuildSnapshot snapshot = 4;
  uint64 sid = 5;  // 公会创建的时候的sid
}

// 公会副本章节排行榜
message RankGuildDungeon {
  uint64 gid = 1;
  uint32 chapter = 2;       // 章节
  int64 tm = 3;             // 时间
  uint32 chapter_rate = 4;  // 当前章节的进度
  cl.GuildSnapshot snapshot = 5;
  uint64 sid = 6;  // 公会创建的时候的sid
}

// 公会副本周伤害排行榜
message RankGuildDungeonWeeklyDamage {
  uint64 gid = 1;
  uint64 damage = 2;  // 伤害
  int64 tm = 3;       // 时间
}

// 公会段位排行榜
message RankGuildDivision {
  uint64 gid = 1;
  uint32 division = 2;      // 段位
  uint32 star = 3;          // 积分
  int64 tm = 4;             // 时间
  uint32 chapter = 5;       // 章节
  uint32 chapter_rate = 6;  // 章节进度
}

// 玩家公会副本伤害榜
message RankGuildDungeonUserDamage {
  uint64 uid = 1;
  uint64 damage = 2;  // 伤害
  int64 tm = 3;       // 时间
  cl.UserSnapshot snapshot = 4;
  uint64 sid = 5;  //
}

message GuildLogs {
  repeated GuildLogInfo logs = 1;
}

message GuildLogDeletes {
  repeated uint32 ids = 2;
}

// 公会日志: 注意要与cl.proto的GuildLogInfo一致
//$<ROP redis|map|guild_log:uint64>
message GuildLogInfo {
  uint32 id = 1;            //$<ROP unique>
  uint32 log_type = 2;      // 日志类型
  int64 create_tm = 3;      // 发生时间
  string execute_name = 4;  // 执行者名称
  string accept_name = 5;   // 接受者名称
}

message GuildDonateLogs {
  repeated GuildDonateLogInfo logs = 1;
}

message GuildDonateDeletes {
  repeated uint32 ids = 2;
}

// 工会捐赠日志: 注意要与cl.proto的GuildDonateLogInfo一致
//$<ROP redis|map|guild_donate_log:uint64>
message GuildDonateLogInfo {
  uint32 id = 1;                  //$<ROP unique>
  string donate_name = 2;         // 捐赠者名字
  repeated cl.Resource cost = 3;  // 消耗的资源
  int64 time = 4;                 // 捐赠时间
}

// 服务器ID和公会ID,加载时使用
//$<ROP redis|map|guild_load_id>
message SidAndGidForGuildLoad {
  uint64 sid = 1;                         //$<ROP unique >
  map<uint64, int64> gids = 2;            // key：gid   value：上次访问时间
  map<uint64, bool> gid_for_dungeon = 3;  //
}

// 公会副本
//$<ROP redis|map|guild_dungeon >
message GuildDungeon {
  uint64 id = 1;  //$<ROP unique >
  string name = 2;
  uint32 badge = 3;
  uint32 chapter = 4;                                            // 副本所处章节
  uint32 dungeon_log_index_id = 5;                               // 公会副本日志序号id
  map<uint32, GuildDungeonChapter> chapters = 6;                 // 副本章节信息
  uint64 weekly_dungeon_damage = 7;                              // 本周副本总伤害
  uint32 room_id = 8;                                            // 房间id
  uint32 division = 9;                                           // 段位
  uint32 season_top_division = 10;                               // 赛季最高段位
  uint32 star = 11;                                              // 积分
  GuildDungeonResetBakData bak_data = 12;                        // 结算数据备份
  map<uint64, uint32> damage_rank = 13;                          // 当前伤害排名  key:uid  value: 排名
  repeated uint32 focus_ids = 14;                                // 集火ID
  int64 chapter_tm = 15;                                         // 达到当前章节(进度)的时间
  repeated uint32 large_divisions = 16;                          // 本赛季已经达到的大段位
  repeated cl.StrategyInfo strategies = 17;                      // 赛季秘技信息
  map<uint64, cl.GuildDungeonMemberFightInfo> fight_infos = 18;  // 挑战记录
  bool jump_chapter = 19;                                        // 是否跳章
}

message GuildDungeonResetBakData {
  uint32 division = 1;
  uint32 star = 2;                             // 积分
  map<uint64, uint32> member_damage_rank = 3;  // key:uid  value: 排名
  uint32 season_top_division = 4;              // 结算时的赛季最高段位
  uint32 division_before_reset = 5;            // 结算前的段位
  uint32 first_large_division = 6;             // 结算首次达到的大段
  uint32 room_rank = 7;                        // 结算时的房间内排名
  uint32 season_division_rank = 8;             // 段位排名
  int64 bak_tm = 9;                            // 备份时间
  uint32 reset_add_star = 10;                  // 结算增加积分
}

message GuildDungeonChapter {
  uint32 id = 1;                              // 章节ID
  repeated cl.GuildDungeonBossInfo boss = 2;  // boss信息
  uint32 buff_boss = 3;                       // 当章节mod为5时,添加buff时的bossID
  int64 buff_time_stamp = 4;
  repeated cl.GuildDungeonBossBoxInfo boxes = 5;
}

// 公会赛季信息
//$<ROP redis|map|guild_dungeon_reset >
message GuildDungeonReset {
  uint32 id = 1;  //$<ROP unique >   // 战区ID
  int64 last_weekly_reset_time = 2;
  int64 last_season_reset_time = 3;
  uint32 season_id = 4;  // 当前赛季ID
  map<uint32, GuildDungeonSeasonHallOfFame> hall_of_fame = 5;
  bool send_msg = 6;               // 是否发送新赛季开始消息
  bool first_week = 7;             // 该战区第一周
  uint32 gst_sync_star_round = 8;  // 公会战同步增加积分的轮次
}

message GuildDungeonSeasonHallOfFame {
  repeated cl.GuildDungeonHallOfFameInfo list = 1;
}

// 公会副本日志
//$<ROP redis|map|guild_dungeon_log:uint64>
message GuildDungeonLog {
  uint32 id = 1;             //$<ROP unique>
  uint32 log_type = 2;       // 日志类型
  int64 create_tm = 3;       // 发生时间
  uint32 chapter = 4;        // 章节id
  string member_name = 5;    // 成员名
  uint64 damage = 6;         // 本次造成伤害
  uint32 monster = 7;        // 本次攻打选择的怪物组
  uint32 reduce_hp_pct = 8;  // 扣除的血量万分比
}

message GuildDungeonLogs {
  repeated GuildDungeonLog logs = 1;
}

message GuildDungeonLogDeletes {
  repeated uint32 ids = 2;
}

// 百塔排行榜
message RankTowerSeasonFloor {
  uint64 uid = 1;
  uint64 sid = 2;  //
  int64 tm = 3;    // 时间
  uint32 floor = 4;
  cl.UserSnapshot snapshot = 5;
  uint32 like_count = 6;
}

// 百塔冲榜排行榜
message RankActivityTowerSeasonFloor {
  uint64 uid = 1;
  uint64 sid = 2;
  int64 tm = 3;
  uint32 floor = 4;
  cl.UserSnapshot snapshot = 5;
  uint32 like_count = 6;
}

message RankSeasonTopPower {
  uint64 uid = 1;
  uint64 sid = 2;              //
  int64 tm = 3;                // 时间
  int64 season_top_power = 4;  // 本赛季最高战力
  cl.UserSnapshot snapshot = 5;
}

message RankSeasonLink {
  uint64 uid = 1;
  uint64 sid = 2;
  int64 tm = 3;
  int64 recycle_point = 4;  // 羁绊回收积分
  cl.UserSnapshot snapshot = 5;
}

message RankBossRush {
  uint64 uid = 1;
  uint64 sid = 2;
  int64 tm = 3;
  uint32 boss_id = 4;
  uint32 boss_level = 5;
  uint32 total_progress = 6;  // 总进度值
  cl.UserSnapshot snapshot = 7;
}

message RankSeasonMap {
  uint64 uid = 1;
  uint64 sid = 2;
  int64 tm = 3;
  uint32 rank_value = 4;
  cl.UserSnapshot snapshot = 5;
}

// 每一条排行榜的数据
//$<ROP redis|map|crank:uint64 >
message RankData {
  uint64 uid = 1;  //$<ROP unique>
  uint64 sid = 2;
  uint32 rank_id = 3;     // 正式字段为0
  uint64 reset_time = 4;  // 正式字段为0
  bytes data = 5;
}

// 每个一个服务器一条数据
//$<ROP redis|map|crankreset:uint32 >
message RankReset {
  uint64 sid = 1;  //$<ROP unique>
  uint32 rank_id = 2;
  uint64 reset_time = 3;        // 最新的重置时间
  uint64 last_reset_time = 4;   // 上次重置时间
  uint64 begin_reset_time = 5;  // 当前正在进行的重置时间>0代表当前正在重置中
  bool copy_finish = 6;
}

// 每一条排行榜的数据
//$<MOP table|ccomrank >
message CommonRankData {
  uint64 uid = 1;         //$<MOP index:uid,unique:true,priority:1 >
  uint64 sid = 2;         //$<MOP index:sid >
  uint32 rank_id = 3;     // 正式字段为0
  uint64 reset_time = 4;  // 正式字段为0
  bytes data = 5;
}

message RankPower {
  uint64 uid = 1;
  uint64 sid = 2;
  uint64 power = 3;
  uint64 tm = 4;
}

//$<ROP redis|hash|world_boss:uint32 >
message WorldBoss {
  uint32 unique_id = 1;         // 活动唯一id
  uint32 sys_id = 2;            // 活动id
  WorldBossLevelMap level = 3;  // key: 难度等级
  Op level_op = 4;
  WorldBossSettle settle = 5;  // 结算信息
  Op settle_op = 6;
}

message WorldBossSettle {
  uint32 sys_id = 1;    // 世界boss id
  bool is_settle = 2;   // 是否结算
  bool push_guild = 3;  // 是否推送成功给了公会
}

message WorldBossLevelMap {
  map<uint32, WorldBossLevel> level_data = 1;  // key: 难度等级
}

message WorldBossLevel {
  uint32 cur_room_index = 1;       // 当前房间的增长序号
  uint32 cur_room_people_num = 2;  // 当前房间的人数量
}

//$<ROP redis|map|world_boss_room:uint32 > // uint32: 战区id
message WorldBossRoom {
  uint32 unique_id = 1;  //$<ROP unique>    房间唯一id: 基于战区号重新构建，保证唯一
  uint32 sys_id = 2;     // 房间系统id
  uint32 level = 3;      // 难度等级
  uint64 log_seq = 4;    // 日志序号
  repeated cl.WorldBossRoomLog logs = 5;
}

//$<ROP redis|map|world_boss_user:uint32 >  // uint32: 战区id
message WorldBossUser {
  uint64 uid = 1;             //$<ROP unique>
  uint32 level = 2;           // 难度等级
  uint32 room_unique_id = 3;  // 房间唯一id
  uint64 sid = 4;
  uint64 accumulative_hurt = 5;         // 累积伤害
  uint64 max_hurt = 6;                  // 最高伤害
  int64 hurt_create_time = 7;           // 伤害创建时间
  uint64 max_hurt_score = 8;            // 最高伤害积分
  string report_id = 9;                 // 战报id
  uint64 accumulative_hurt_score = 10;  // 累积伤害积分
  int64 server_open_time = 11;          // 服务器开服时间
}

// 前一期的玩家排名备份，用于结算
//$<ROP redis|map|world_boss_user_rank_bak:uint32 >  // uint32: 战区id
message WorldBossUserRankBak {
  uint64 uid = 1;  //$<ROP unique>
  uint64 sid = 2;
  uint32 level = 3;              // 难度等级
  uint32 room_rank = 4;          // 房间排名
  uint32 partition_rank = 5;     // 战区排名
  uint64 accumulative_hurt = 6;  // 累积伤害
  uint64 max_hurt_score = 7;     // 最高伤害积分
}

// 恶魔难度前128名玩家备份 - 为巅峰竞技场准备
//$<ROP redis|map|world_boss_user_top_arena:uint32 >  // uint32: 战区id
message WorldBossUserToPeakArena {
  uint32 world_id = 1;  //$<ROP unique>
  repeated WorldBossUserToPeakArenaData users = 2;
}

//$<ROP redis|map|world_boss_user_back:uint64>  // uint32: 战区id| uint32: bossType
message WorldBossUserBack {
  uint64 uid = 1;             //$<ROP unique>
  uint32 level = 2;           // 难度等级
  uint32 room_unique_id = 3;  // 房间唯一id
  uint64 sid = 4;
  uint64 accumulative_hurt = 5;         // 累积伤害
  uint64 max_hurt = 6;                  // 最高伤害
  int64 hurt_create_time = 7;           // 伤害创建时间
  uint64 max_hurt_score = 8;            // 最高伤害积分
  string report_id = 9;                 // 战报id
  uint64 accumulative_hurt_score = 10;  // 累积伤害积分
  int64 server_open_time = 11;          // 服务器开服时间
}

message WorldBossTypeUsersBack {
  map<uint64, WorldBossUserBack> users = 1;
}

message WorldBossUserToPeakArenaData {
  uint64 uid = 1;
  uint64 sid = 2;
  uint32 partition_rank = 5;  // 战区排名
}

//$<ROP redis|hash|disorder_land:uint32 >
message DisorderLand {
  uint32 season_id = 1;  // 赛季id;
}

//$<ROP redis|map|disorder_land_user:uint32 >  // uint32: 战区id
message DisorderLandUser {
  uint64 uid = 1;  //$<ROP unique>
  uint64 sid = 2;
  repeated DisorderLandHurdle hurdles = 3;
}

message DisorderLandHurdle {
  uint32 type = 1;        // 关卡类型
  uint32 max_level = 2;   // 最大等级
  int64 create_time = 3;  // 创建时间
}

// 巅峰竞技场
//$<ROP redis|hash|peak:uint32> //peak:战区id
message Peak {
  uint32 open_times = 1;  // 玩法开启次数，1个小周期记1次。新老战区不同
  PeakResults top8 = 2;   // 名人堂数据（每期前8名）
  Op top8_op = 3;
  PeakState state = 4;  // 状态信息
  Op state_op = 5;
  PeakResults top128 = 6;  // 前128名,公会用的
  Op top128_op = 7;
  uint32 push_guild = 8;  // 推送公会
}

message PeakState {
  cl.PeakState state = 1;
}

// 巅峰竞技场 - 全部比赛结果
message PeakResults {
  repeated cl.PeakResult datas = 1;
}

// 巅峰竞技场 - 对战数据
//$<ROP redis|map|peak_match:uint32> //peak_match:战区id
message PeakMatch {
  uint32 id = 1;                          //$<ROP unique > 比赛场次id
  cl.PeakMatchFighter left_fighter = 2;   // 左侧玩家
  cl.PeakMatchFighter right_fighter = 3;  // 右侧玩家
  string report_id = 4;                   // 战报id
  uint64 winner_uid = 5;                  // 获胜者uid
  uint32 round = 6;                       // 第几轮，小组赛:1-3 决赛:4-6 冠军赛:7
  uint32 area = 7;                        // 战区，小组赛/决赛:1上半区 2下半区  冠军赛:0
  uint32 group = 8;                       // 第几组，小组赛:1-16 决赛/冠军赛:0
  uint32 group_sub = 9;                   // 每轮比赛中的场次序号:1-4
  int64 guess_start_tm = 10;              // 竞猜开始时间 0-非竞猜场次
}

// 巅峰竞技场 - 参赛玩家
//$<ROP redis|map|peak_fighter:uint32> //peak_fighter:战区id
message PeakFighter {
  uint64 uid = 1;                 //$<ROP unique >
  uint64 sid = 2;                 // 服务器ID
  uint32 pos = 3;                 // 玩家在巅峰玩法中的占位id，继承于世界boss的排名
  uint32 phase_score = 4;         // 赛季积分
  uint32 season_score = 5;        // 赛季积分
  int64 time = 6;                 // 数据更新时间（战斗结算时间）
  uint32 rank = 7;                // 排名（所有8强玩家的排名均为8）
  bool is_end = 8;                // 是否比赛已结束（被淘汰了）
  uint32 phase = 9;               // 哪个周期的
  uint32 round = 10;              // 哪个轮次
  uint32 group = 11;              // 哪个分组
  uint32 group_sub = 12;          // 分组里的排名，只是先后迅速，不是连续的，没有其他意思，越小的越排前面
  cl.UserSnapshot snapshot = 13;  // 缓存玩家快照
  string last_report_id = 14;     // 上次战报id
  bool is_attacker = 15;          // 是否进攻方
}

message PeakPhaseUserBak {
  uint64 uid = 1;
  uint64 sid = 2;
  uint32 rank = 3;         // 属于阶段几强
  uint32 season_rank = 4;  // 赛季积分榜排行
}

// 公会战-----------------------------------------------------------------------------

//$<ROP redis|hash|GST_M:uint32> //跨服组id
message GSTManager {
  GSTState sta = 1;  // 状态
  Op sta_op = 2;
}

message GSTState {
  cl.GSTSta state = 1;
  uint64 min_log_id = 2;  // 最小日志id
  uint32 mob_round = 3;
  GSTGuildMobRankSettle mob_settle = 4;
}

message GSTGuildMobRankSettle {
  uint32 mob_round = 1;
  map<uint64, cl.Empty> sids = 2;                                   // 未发放个人结算奖励的服务器
  repeated GSTGuildMobRankSettleReward rewards = 3;                 // 个人结算奖励
  map<uint32, GSTGuildMobRankGuildSettleReward> guild_rewards = 4;  // 公会结算奖励 key: partition（当前分区发放完成后删除分区数据）
}

message GSTGuildMobRankSettleReward {
  uint32 rank_reward_id = 1;
  map<uint64, string> guild_ids = 2;
}

message GSTGuildMobRankGuildSettleReward {
  map<uint64, uint32> guild_rewards = 1;  // guild_id => reward_id
}

//$<ROP redis|map|GST_Guild:uint64> //seasonID << 32 | 跨服组id
message GSTGuildManager {
  uint64 guild_id = 1;  //$<ROP unique>
  GSTGuild guild = 2;
}

//$<ROP redis|map|GST_Dragon:uint64> //seasonID << 32 | 跨服组id
message GSTDragonManager {
  uint64 guild_id = 1;        //$<ROP unique>
  GSTDragonGuild dragon = 2;  // 与GSTGuild不同——公会解散不删除，龙战场次结算时才删除
}

//$<ROP redis|map|GST_GuildUser:uint64> //seasonID << 32 | 跨服组id
message GSTGuildUserManager {
  uint64 uid = 1;  //$<ROP unique>
  cl.GSTGuildUser user = 2;
}

//$<ROP redis|map|GST_Group:uint64> //seasonID << 32 | 跨服组id
message GSTGroupManager {
  uint64 group_id = 1;  //$<ROP unique>
  GSTGroupInfo info = 2;
}

//$<ROP redis|map|GST_Log:uint32> // 跨服组id
message GSTGuildLogManager {
  uint64 log_id = 1;           //$<ROP unique>
  cl.GSTLogInfo log_info = 2;  // 所有日志记录
}

//$<ROP redis|map|GST_Mob_Rank:uint32> //mobRound << 32 | 跨服组id
message GSTMobRankManager {
  uint64 guild_id = 1;  //$<ROP unique>
  cl.GuildMobilizationGuildRank info = 2;
}

//$<ROP redis|map|GST_Guild_Rank_Bak:uint32> // 跨服组id
message GSTGuildRankBak {
  uint64 guild_id = 1;  //$<ROP unique >
  GSTRankBak rank_bak = 2;
  GSTDragonBattleResultBak dragon = 3;
}

//$<ROP redis|map|GST_Fix_Gst_User_Build_Dispatch>
message GstFixGstUserBuildDispatch {
  uint64 uid = 1;                                                      //$<ROP unique >
  map<uint32, cl.GSTBuildUserDispatchHeroes> build_dispatch_hero = 2;  // 建筑内派遣英雄
}

message GSTRankBak {
  uint32 season_id = 1;                        // 赛季
  uint32 round = 2;                            // 轮次
  cl.GSTLastLRoundGuildRank guilds = 3;        // 公会排名
  repeated cl.GSTGuildUserBase user_rank = 4;  // 回合结算后的公会玩家排名
  uint32 map_config_id = 5;
  repeated uint64 leaders = 6;
  uint32 lround = 7;                      // 回合数
  repeated uint32 first_occupy_land = 8;  // 首占地块ID
  cl.GSTRoundSettle l_round_settle = 9;   // 公会该回合结算的数据(弃用)
}

message GSTDragonBattleResultBak {
  uint32 season_id = 1;                                    // 赛季
  uint32 dragon_round = 2;                                 // 龙战场次
  cl.GSTDragonBattleResult dragon_last_battle_result = 3;  // 上场龙战结果
}

//$<ROP redis|map|GST_Common_Bak:uint32> // 跨服组id
message GSTCommonBak {
  uint64 sid = 1;  //$<ROP unique >
  GSTDragonBak dragon_bak = 2;
  GSTChallengeBak challenge_bak = 3;
}

message GSTChallengeBak {
  uint32 season_id = 1;
  uint32 round = 2;   // 公会战轮次
  uint32 lround = 3;  // 公会战回合
  map<uint32, cl.MultipleUserIds> user_rank = 4;
}

message GSTDragonBak {
  uint32 season_id = 1;  // 赛季
  //  uint32 round = 2;                                         // 轮次
  //  uint32 lround = 3;                                        // 回合数
  uint32 dragon_round = 4;                                  // 龙战轮次
  repeated uint64 win_users = 5;                            // 获胜玩家
  repeated uint64 lose_users = 6;                           // 失败玩家
  map<uint64, GSTDragonTaskBak> user_unreceived_tasks = 7;  // 玩家未领取的任务奖励
}

message GSTDragonTaskBak {
  repeated uint32 task_ids = 1;  // 未领奖的任务id
}

message GSTAwardData {
  uint64 gid = 1;
  map<uint64, uint32> member_score_rank = 2;
  map<uint64, bool> leaders = 3;
  uint32 group_rank = 4;
  uint32 MapConfigId = 5;
  uint32 season = 6;  // 赛季
  uint32 round = 7;   // 轮次
  uint32 lround = 8;  // 回合
}

message GSTFirstCenterAwardData {
  repeated uint64 user_id = 1;
}

message GSTGuild {
  uint64 group_id = 1;  // 所在分组id
  cl.GSTSimpleGuild guild = 2;
  cl.GSTRoundSettle lround_info = 3;                          // 回合结算的信息
  uint32 use_free_donate_times = 4;                           // 公会免费捐献次数
  string message_board = 5;                                   // 留言板
  int64 guild_can_match_time = 6;                             // 公会能进行匹配的时间
  repeated cl.GSTGuildUserBase user_rank = 7;                 // 回合结算后的公会玩家排名
  uint64 last_group_id = 8;                                   // 上一轮的分组id
  repeated cl.GSTGoddessBless blessInfo = 9;                  // 女武神祝福信息
  uint64 message_board_id = 10;                               // 留言板ID
  uint32 max_ground_count = 11;                               // 最大地块数
  repeated cl.GSTBuild gst_build = 12;                        // 公会建筑信息
  repeated cl.GSTBuildDonateRank gst_build_donate_rank = 13;  // 公会建筑捐赠排行榜
  // cl.GSTBossGroup boss_group = 14;                            // boss组
  GSTDragon dragon = 15;      // 龙战数据
  cl.GSTGuildTech tech = 16;  // 公会战科技
}

message GSTDragon {
  GSTDragonGuildSeason dragon_season = 1;  // 龙战赛季数据
}

// 地图信息
message GSTMapInfo {
  uint32 map_config_id = 1;                       // 地图量表的id
  map<uint32, GSTGroundInfo> GSTGroundInfos = 2;  // 地块信息 key为地块格子主键
}

// 地图地块信息
message GSTGroundInfo {
  uint64 guild_id = 1;                         // 所属公会
  uint32 land_id = 2;                          // 地块量表类型id
  repeated GSTGuildFightTeam fight_teams = 3;  // 在地块上面的队伍
  cl.GSTFightResult fighting = 4;              // 匹配战斗中的队伍 一个地块只有1个队伍在战斗
  cl.GSTFightTeam monster_info = 5;            // 如果有怪物,怪物信息
  bool send_match_score = 6;
  uint32 fight_index = 7;                                 // 地块该出队伍的公会
  uint64 temp_control_guild_id = 8;                       // 即将占领地块的公会id
  bool fighted = 9;                                       // 该地块战斗过,给前端展示用
  string first_occupy_guild_name = 10;                    // 第一次占领地块名字
  uint32 fight_id = 11;                                   // 地块战斗唯一id
  repeated GSTDragonSkillGuild dragon_skill_guilds = 12;  // 对地块放龙战技能的公会
  uint32 dragon_skill_lround = 13;                        // 龙战技能释放的回合
}

message GSTDragonSkillGuild {
  uint64 guild_id = 1;
  int64 op_time = 2;
}

// 地块上公会所派遣的队伍
message GSTGuildFightTeam {
  repeated cl.GSTFightTeam teams = 1;
  uint32 match_index = 2;  // 匹配到第几队了
  uint64 guild_id = 3;
  uint32 win = 4;          // 单回合地块上获胜场次
  uint32 lose = 5;         // 单回合地块上失败场次
  uint32 guild_score = 6;  // 擂台匹配时,公会的积分
}

// 分组信息
message GSTGroupInfo {
  uint64 id = 1;
  GSTMapInfo map_info = 2;                   // 地图信息
  repeated uint64 matched_guild = 3;         // 匹配的公会
  uint32 roomQuality = 4;                    // 分组段位
  cl.GSTLastLRoundGuildRank guild_rank = 5;  // 公会积分排名
  // uint32 fight_id = 6;                       // 战斗唯一id
  reserved 6;
  reserved "fight_id";
  uint32 arena_unlocked_round = 7;                      // 解锁的擂台轮次
  cl.GSTArenaInfo arena_info = 8;                       // 擂台信息
  cl.GSTChallengeLastTop challenge_top = 9;             // 新擂台赛展示上一场Top数据
  repeated cl.GSTChallengeShowLog challenge_show = 10;  // 新擂台赛跑马灯数据
}

message GSTNextLroundAddScorePreView {
  bool need_update = 1;
  map<uint64, uint32> add_score_preview = 2;
}

//$<ROP redis|hash|GST_DragonGlobal:uint64> //seasonID << 32 | 跨服组id
message GSTDragonGlobal {
  GSTDragonMatch match = 1;  // 是否已完成匹配
  Op match_op = 2;
  uint64 bot_guild_id = 3;   // 当前匹配了几个bot
  int64 bot_fight_time = 4;  // bot 自动战斗的时间
}

message GSTDragonMatch {
  bool matched = 1;                            // 是否已完成匹配
  map<uint64, cl.Empty> unmatched_guilds = 2;  // 未完成匹配的公会
}

// 龙战公会场次数据，场次重置
message GSTDragonGuild {
  uint64 guild_id = 1;                 // 公会id
  GSTDragonBattle battle = 2;          // 龙战战斗数据
  cl.GSTDragonSkill dragon_skill = 3;  // 龙战攻城技能
  GSTDragonBotInfo bot_info = 4;       // 如果是机器人公会有机器人额外信息
}

message GSTDragonBotInfo {
  uint32 bot_id = 1;
  uint32 guild_badge = 2;
  uint32 rand_bot_name = 3;
  uint32 fight_num = 4;
  repeated GSTDragonBotMemberInfo member_info = 5;
}

// 机器人公会成员的战斗数据
message GSTDragonBotMemberInfo {
  uint32 member_fight_num = 1;
  uint64 total_damage = 2;
  cl.GSTGuildUserBase user_info = 3;
}

// 龙战公会赛季数据，赛季重置
message GSTDragonGuildSeason {
  uint32 score = 1;                                   // 龙战积分
  uint32 continuous_failed_count = 2;                 // 连败次数
  repeated uint64 battled_guilds = 3;                 // 已交战公会
  uint32 show_pos = 4;                                // 展示龙的位置
  map<uint32, GSTDragonCultivation> cultivation = 5;  // 养龙数据
  int32 dragon_skill_power = 6;                       // 攻城技能能量
  uint32 total_strategy_skill_num = 7;                // 祝福技能次数
  uint32 use_strategy_skill_num = 8;                  // 使用的祝福技能次数
}

message GSTDragonCultivation {
  uint32 pos = 1;
  repeated uint32 evolve_path = 2;  // 进化路径
}

message GSTDragonBattle {
  uint64 opponent_gid = 1;                         // 敌对公会id
  GSTDragonSnapshot own_snapshot = 2;              // 我方公会快照，匹配后不变
  map<uint32, uint64> guild_dragon_damage = 3;     // 公会对敌方每条龙造成的伤害 key: pos
  map<uint32, uint32> guild_dragon_reduce_hp = 4;  // 公会对敌方每条龙造成的掉血值 key: pos
}

message GSTDragonSnapshot {
  uint32 dragon_level = 1;
  map<uint32, uint32> dragon_pos_id = 2;  // key: pos value: dragon_id
  uint32 show_pos = 3;
  string guild_name = 4;
  uint32 guild_badge = 5;
}

//$<ROP redis|hash|cross_season_arena:uint32 > // uint32:大战区ID
message CrossSeasonArenaSta {
  uint32 season_id = 1;  // 赛季id;
  uint32 round = 2;
  uint32 state = 3;
}

//$<ROP redis|map|cross_season_arena_user:uint32> // uint32:大战区ID
message CrossSeasonArenaUser {
  uint64 uid = 1;  //$<ROP unique>
  uint64 sid = 2;
  uint32 score = 3;                                 // 积分
  uint32 season = 4;                                // 赛季ID
  uint32 round = 5;                                 // 轮次
  int64 time = 6;                                   // 达成时间时间
  repeated CrossSeasonArenaOpponent opponents = 7;  // 匹配对手
  cl.UserSnapshot snapshot = 8;                     // 玩家快照
  bool is_sign = 9;                                 // 本赛季是否手动报名
  uint32 last_round_rank = 10;                      // 上个赛季结算排名
  uint32 area_id = 11;                              // 战区ID
  uint32 last_round_score = 12;                     // 上赛季积分
  int64 season_crystal_top35_power = 13;            // 下一赛季水晶35名英雄
  bool season_rank_in = 14;                         // 本赛季是否进入排行榜
}

//$<ROP redis|hash|cross_season_arena_fame:uint32> // uint32:大战区ID
message CrossSeasonArenaFame {
  uint32 season = 1;
  uint32 round = 2;
  CrossSeasonArenaFameUsers users = 3;
  Op users_op = 4;
}

message CrossSeasonArenaFameUsers {
  repeated cl.RankValue users = 3;
}

//$<ROP redis|hash|cross_season_arena_award_bak:uint32 > // uint32:大战区ID
message CrossSeasonArenaAwardBak {
  uint32 season = 1;
  uint32 round = 2;
  CrossSeasonArenaServerAwardsBak server_award = 3;
  Op server_award_op = 4;
}

//$<ROP redis|map|cross_season_arena_award_server_bak:uint32> // uint32:大战区ID
message CrossSeasonArenaAwardServerBak {
  uint64 sid = 1;  //$<ROP unique>
  uint32 season = 2;
  uint32 round = 3;
  repeated CrossSeasonArenaServerUserBak user_back = 4;
}

message CrossSeasonArenaServerBak {
  repeated CrossSeasonArenaServerUserBak server_back = 1;
}

message CrossSeasonArenaServerUserBak {
  uint64 uid = 1;
  uint32 rank = 2;
  uint32 division = 3;
  uint32 score = 4;
}

message CrossSeasonArenaServerAwardsBak {
  map<uint64, CrossSeasonArenaServerBak> servers_award = 3;
}

message CrossSeasonArenaOpponent {
  uint64 id = 1;         // uid
  uint32 level = 2;      // 等级
  string name = 3;       // 昵称
  uint32 base_id = 4;    // 头像相关
  uint32 score = 5;      // 积分
  bool bot = 6;          // 是否是机器人
  uint64 server_id = 7;  // 服务器ID
}

message CrossSeasonArenaUserBase {
  uint32 score = 1;
  uint32 rank = 2;
  uint32 last_round_score = 3;
  uint32 last_round_rank = 4;
}

message RankSeasonRemainBookExp {
  uint64 uid = 1;
  uint64 sid = 2;             //
  int64 tm = 3;               // 时间
  int64 remain_book_exp = 4;  // 遗物图鉴经验
  cl.UserSnapshot snapshot = 5;
}

// 本日收集的所有服务器上报的日志
//$<ROP redis|map|cross_hot_rank_collect>
message CrossHotRankCollectionLog {
  uint32 rank_id = 1;  //$<ROP unique>
  db.LogicHotRankCollectionLog collection_log = 2;
}

// 上报成功的服务器
//$<ROP redis|hash|cross_hot_rank_finish_sid>
message CrossHotRankCollectionLogFinishSid {
  int64 last_clear_daily_time = 1;  // 上一次日重置时间
  FinishSid finish_sid = 2;
  Op finish_sid_op = 3;
  int64 last_rank_merge_time = 4;  // 上一次合并时间
}

message FinishSid {
  repeated uint64 sid = 1;
}

//$<ROP redis|map|cross_hot_show_rank>
message CrossHotRank {
  uint32 rank_id = 1;  //$<ROP unique>
  cl.CultivateHotRank hot_rank = 2;
}

//$<ROP redis|map|cross_hot_rank_server_collect>
message CrossHotRankServerCollectionLog {
  uint64 server_id = 1;  //$<ROP unique>
  repeated db.LogicHotRankCollectionLog collection_log = 2;
}

message GuildMobilizationData {
  uint32 round = 1;  // 数据所属期数
  repeated cl.GuildMobilizationGuildTask tasks = 2;
  uint32 score = 3;
  repeated cl.GuildMobilizationTaskLog logs = 4;
  uint32 can_fresh_times = 5;                    // 可刷新任务的次数
  uint32 bought_fresh_times = 6;                 // 购买刷新的次数
  repeated uint32 finished_tasks = 7;            // 已经完成的任务(不再刷新)
  map<uint64, uint32> users_score = 8;           // 成员提供的积分
  int64 score_change_time = 9;                   // 积分变动的时间
  map<uint32, uint32> task_finished_times = 10;  // 任务完成的次数
  cl.MessageBoard message = 11;                  // 公告
}

message GuildMobilizationGuildMember {
  uint32 round = 1;                                              // 数据所属期数
  map<uint32, cl.GuildMobilizationTaskStatus> score_levels = 2;  // 公会积分等级奖励
  uint32 can_accept_task_times = 3;                              // 可接取任务的次数
  uint32 bought_task_times = 4;                                  // 购买接取的次数
  map<uint64, uint32> guild_score = 5;                           // guild_id => score 在各公会获得的积分
  repeated uint32 received_ids = 6;                              // 已领取积分奖励
}

//$<ROP redis|map|hot_rank_tt>
message TalentTreeHot {
  uint64 sid = 1;  //$<ROP unique>
  repeated cl.TalentTreeNodeHot nodes = 2;
  int64 sync_tm = 3;
}

message RankTalentTree {
  uint64 uid = 1;
  uint64 sid = 2;
  int64 tm = 3;
  uint32 level = 4;
  cl.UserSnapshot snapshot = 5;
}

message SeasonCompliance {
  uint64 uid = 1;
  uint64 sid = 2;
  int64 tm = 3;
  uint32 score = 4;
  cl.UserSnapshot snapshot = 5;
}

// 每一条排行榜的数据
//$<ROP redis|map|season_compliance_crank:uint64 >
message SeasonComplianceRankData {  // partition:rankID
  uint64 uid = 1;                   //$<ROP unique>
  uint64 sid = 2;
  uint32 rank_id = 3;
  SeasonCompliance data = 4;
}

// 每个一个服务器一条数据
//$<ROP redis|hash|season_compliance_crankreset:uint32 >
message SeasonComplianceRankReset {  // partition
  SeasonComplianceStage stage = 1;
  Op stage_op = 3;
}

message SeasonComplianceStage {
  cl.SeasonComplianceStage stage = 1;
}

// 赛季地图
//$<ROP redis|hash|season_map:uint32> //uint32:战区id
message SeasonMapManager {
  SeasonMap data = 1;
  Op data_op = 2;
}

message SeasonMap {
  reserved 2, 3, 4, 6, 7, 8;
  uint32 season = 1;
  map<uint32, cl.SeasonMapPositionLog> position_logs = 5;
}

// 宠物爬塔排行榜
message RankTowerPokemon {
  uint64 uid = 1;
  uint64 sid = 2;
  int64 tm = 3;
  uint32 dungeon_id = 4;
  cl.UserSnapshot snapshot = 5;
}

// 公平竞技场-----------------------------------------------------------------------------

//$<ROP redis|hash|balance_arena:uint32>
message BalanceArena {
  BalanceArenaState state = 1; // 赛程状态
  Op state_op = 2;
}

message BalanceArenaState {
  cl.BalanceArenaState state = 1;
}

//$<ROP redis|map|balance_arena_user:uint32>
message BalanceArenaUser {
  uint64 uid = 1;  //$<ROP unique>
  uint64 sid = 2;
  uint32 area_id = 4;                              // 战区ID
  uint32 season = 5;                                // 赛季
  uint32 round = 6;                                 // 赛期
  uint32 stage = 7;                                 // 阶段
  // TODO(yta) 玩家卡组
  uint32 score = 8;                                 // 大组赛积分
  cl.UserSnapshot snapshot = 11;                     // 玩家快照
}

// 玩家卡组
message BalanceArenaBox {
  
}
