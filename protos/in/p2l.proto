syntax = "proto3";

option go_package = "app/protos/in/p2l";
import "config.proto";
import "cl.proto";

// platform <-> logic
package p2l;

enum ID {
  MSG_NONE = 0;
  MSG_MIN = 300000;
  MSG_MAX = 400000;
  MSG_BEGIN = 300001;
  MSG_END = 399999;
  MSG_L2P_UseGiftCode = 300002;
  MSG_P2L_UseGiftCode = 300003;
  MSG_L2O_CreateBattleReport = 300004;
  MSG_O2L_CreateBattleReport = 300005;
  MSG_L2P_ChangeEtcdConfig = 300006;
  MSG_L2P_GetSeasonFlashBackData = 300007;
  MSG_P2L_GetSeasonFlashBackData = 300008;
  MSG_L2P_SensitiveWordCheck = 300009;
  MSG_P2L_SensitiveWordCheck = 300010;

  // 聊天
  MSG_MIN_Chat = 300100;
  MSG_L2P_ChatGetToken = 300101;      // 获取token
  MSG_P2L_ChatGetToken = 300102;      // 获取token
  MSG_L2P_ChatSyncUser = 300104;      // 同步用户数据
  MSG_L2P_ChatJoinGroup = 300105;     // 加入群组
  MSG_P2L_ChatJoinGroup = 300106;     // 加入群组
  MSG_L2P_ChatDismissGroup = 300107;  // 遣散群组
  MSG_P2L_ChatDismissGroup = 300108;  // 遣散群组
  MSG_L2P_ChatSendMsg = 300109;       // 发送消息
  MSG_L2P_ChatBan = 300110;           // 禁言/解禁

  // 密林
  MSG_MIN_Flower = 300200;
  MSG_L2P_FlowerPushMsg = 300201;

  // 开服返利
  MSG_MIN_Rebase = 300300;
  MSG_L2P_RebaseGet = 300301;
  MSG_P2L_RebaseGet = 300302;

  MSG_MIN_MuteAccount = 300400;
  MSG_L2P_MuteAccount = 300401;
  MSG_P2L_MuteAccount = 300402;
}

message L2P_ChangeEtcdConfig {
  config.Logic etcd_config = 1;
}

message L2P_UseGiftCode {
  string uuid = 1;
  string code = 2;
  uint64 server_id = 3;  // 服务器ID
  uint32 op_id = 4;      // 运营商
  uint32 channel = 5;    // 渠道ID
  uint64 uid = 6;
  uint64 op_group = 7;  // 运营组
}

message P2L_UseGiftCode {
  uint32 ret = 1;
  string code = 2;
  uint32 award_id = 3;
}

message L2O_CreateBattleReport {
  string objectKey = 1;
  bytes data = 2;
}

message O2L_CreateBattleReport {
  uint32 ret = 1;
  string url = 2;
}

message L2P_GetSeasonFlashBackData {
  uint64 uid = 1;
  uint32 season_id = 2;
}

message P2L_GetSeasonFlashBackData {
  uint32 ret = 1;
  uint32 season_id = 2;
  repeated cl.SeasonUserLogPoint points = 3;
  repeated uint32 ids = 4;
  uint64 uid = 5;
}

message L2P_ChatGetToken {
  string unique_id = 1;
  string userID = 2;
  ChatSyncUserAndJoinGroup user = 3;
  uint32 cross_area = 4;
  bool is_hand = 5;  // true：客户端请求，false: 玩家登录推
  string lang = 6;
}

message P2L_ChatGetToken {
  uint32 ret = 1;
  string token = 2;
  int64 expire_time = 3;
  repeated string success_tags = 4;
  map<string, string> failed_tag_errors = 5;
  uint32 cross_area = 6;
  bool is_hand = 7;
  string lang = 8;
}

message RespChatGetToken {
  string token = 1;
  int64 expireTimeSeconds = 2;
  map<string, RespChatGroupAbstract> successTagGroupMap = 3;  // key: group tag
  map<string, string> failedTagErrorMap = 4;                  // key: group tag
}

message RespChatGroupAbstract {
  string groupID = 1;
  int32 memberNum = 2;
  uint64 memberIDsHash = 3;
}

message ChatSyncUserAndJoinGroup {
  ChatUserInfo user = 1;
  bool getToken = 2;
  repeated ChatGroupTag groupTags = 3;
}

message L2P_ChatSyncUser {
  string unique_id = 1;
  ChatUser user = 2;
}

message ChatUser {
  ChatUserInfo user = 1;
  bool getToken = 2;
}

message L2P_ChatJoinGroup {
  string unique_id = 1;
  uint64 uid = 2;
  ChatJoinGroup join_group = 3;
  uint32 group_op = 4;  // 群操作：1：加入 2：退出
  string group_tag = 5;
}

message ChatJoinGroup {
  string userID = 1;
  repeated ChatGroupTag groupTags = 2;
}

message P2L_ChatJoinGroup {
  uint32 ret = 1;
  uint64 uid = 2;
  repeated string success_tags = 3;  // 加入成功的group tag
  uint32 group_op = 4;               // 群操作：1：加入 2：退出
  string group_tag = 5;
}

message RespChatJoinGroup {
  map<string, RespChatGroupAbstract> successTagGroupMap = 1;
  map<string, string> failedTagErrorMap = 2;
}

message L2P_ChatDismissGroup {
  string unique_id = 1;
  uint64 uid = 2;
  ChatDismissGroup dismiss_group = 3;
}

message P2L_ChatDismissGroup {
  uint32 ret = 1;
  uint64 uid = 2;
  string group_id = 3;
  string group_tag = 4;
}

message ChatUserInfo {
  string userID = 1;
  string lexiconID = 2;
  string ex = 3;
  string faceURL = 4;   // 发送者头像相关 json
  string nickname = 5;  // 昵称
  uint64 serverID = 6;
  uint32 sendCd = 7;  // 聊天cd时间
}

message ChatGroupTag {
  string tag = 1;
  uint32 groupType = 2;
  uint32 opt = 3;
  uint32 policy = 4;
  repeated ChatGroupInfo groups = 5;
}

message ChatGroupInfo {
  string groupID = 1;
  uint32 maxMemberNum = 2;
  string ex = 3;
  uint32 groupType = 4;
  uint32 needVerification = 5;
  string groupName = 6;
}

message ChatDismissGroup {
  string groupID = 1;
  string groupTag = 2;
}

message L2P_ChatSendMsg {
  string unique_id = 1;
  uint64 uid = 2;
  ChatSendMsg sys_msg = 3;
}

message ChatSendMsg {
  ChatMsgData msgData = 1;
}

message ChatMsgData {
  string sendID = 1;
  string groupID = 2;
  string clientMsgID = 3;
  uint32 senderPlatformID = 4;
  uint32 sessionType = 5;
  uint32 msgFrom = 6;
  uint32 contentType = 7;
  bytes content = 8;
  string ex = 9;
}

message L2P_ChatBan {
  string unique_id = 1;
  ChatBan ban = 2;
}

message ChatBan {
  string userID = 2;
  int64 muteSeconds = 3;
}

message L2P_FlowerPushMsg {
  uint64 msg_id = 1;  // 消息 id
  uint64 uuid = 2;
  string title = 3;    // 消息标题
  string context = 4;  // 消息内容
  // TODO：消息发送的频率要限制？X小时
}

message L2P_SensitiveWordCheck {
  repeated SensitiveWordCheckReq reqs = 1;
  uint64 async_msg_id = 2;  // 异步消息id
}

message P2L_SensitiveWordCheck {
  uint32 ret = 1;
  uint64 async_msg_id = 2;  // 异步消息id
}

message SensitiveWordCheckReq {
  int64 time = 1;   // 请求时间
  string uid = 2;   // 第三方uid
  string gid = 3;   // 第三方游戏id
  string pid = 4;   // 第三方渠道id
  string dsid = 5;  // 区服id
  string sign = 6;
  string actor_name = 7;            // 角色名
  string actor_id = 8;              // 角色id
  string actor_level = 9;           // 角色等级
  string actor_recharge_gold = 10;  // 角色充值数
  string chat_time = 11;            // 聊天时间
  string content = 12;              // 内容
  string lexiconID = 13;
  string type = 14;  // 聊天类型
  string idfa = 15;
  string idfv = 16;
  string oudid = 17;
  string imei = 18;
  string mac = 19;
  string actor_ce = 20;
  string to_uid = 21;
  string to_actor_name = 22;
  string to_actor_id = 23;
  string to_actor_level = 24;
  string to_actor_recharge_gold = 25;
  string type_id = 26;
  string channel = 27;  // 渠道id
}

message L2P_RebaseGet {
  string uuid = 1;
  uint64 id = 2;
}

message P2L_RebaseGet {
  uint32 ret = 1;
  string uuid = 2;
  uint64 id = 3;
  uint64 game_recv_id = 4;
  uint64 game_server = 5;
  uint32 recharge_num = 6;
  uint32 operated = 7;
}

message L2P_MuteAccount {
  uint64 ban_user_id = 1;
  string user_name = 2;
  uint64 server_id = 4;
  int64 start_time = 5;
  int64 end_time = 6;
  int64 ban_time = 7;
  string baner = 8;
  uint32 role_level = 9;
  uint32 vip_level = 10;
  uint32 op_group_id = 11;
}

message P2L_MuteAccount {
  uint32 ret = 1;
  uint64 baner = 2;
  uint64 uid = 3;
  int64 start_time = 4;
  int64 end_time = 5;
}