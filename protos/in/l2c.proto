syntax = "proto3";

import "db.proto";
import "cl.proto";
import "cr.proto";
import "log.proto";

option go_package = "app/protos/in/l2c";

package l2c;

// 活动类型
enum ACTIVITYID {
  None = 0;
  ROUTER = 1;              // 路由
  WRESTLE = 2;             // 跨服竞技场
  GUILD = 3;               // 公会
  RANK = 4;                // 跨服排行榜
  WORLDBOSS = 5;           // 世界boss
  DISORDER_LAND = 6;       // 失序空间
  PEAK = 7;                // 巅峰竞技场
  GST = 8;                 // 公会战
  SEASON_ARENA = 9;        // 赛季竞技场
  HOT_RANK = 10;           // 热度榜
  SEASON_COMPLIANCE = 11;  // 赛季冲榜
  SEASON_MAP = 12;         // 赛季地图
  MAX = 13;                // 最大的
}

// 分区类型. 可以把他们当成一个特殊的活动ID
enum CROSS_ACT_AREA_ID {
  NORMAL_AREA = 0;                 // 普通分区
  WRESTLE_AREA = 2;                // 神树争霸分区
  SEASON_AREA = 10000;             // 赛季竞技场分区
  GST_AREA = 10001;                // 公会战分区
  SEASON_COMPLIANCE_AREA = 10002;  // 赛季冲榜
  DONT_USE_AREA = 99999;           // 不使用上述分区
}

enum ID {
  MSG_NONE = 0;
  MSG_MIN = 200000;
  MSG_MAX = 300000;
  MSG_BEGIN = 200001;
  MSG_END = 299999;

  // 服务器与服务器之间使用
  MSG_L2C_SayHi = 200002;
  MSG_C2L_SayHi = 200003;
  MSG_L2C_KeepAlive = 200004;
  MSG_C2L_KeepAlive = 200005;

  // 路由消息
  MSG_ROUTER_MIN = 200100;
  // MSG_L2C_Test = 200101;
  // MSG_C2L_Test = 200102;
  MSG_L2C_SendRemoteLogic = 200105;  //[内部协议]透传消息到目标Logic服务器
  MSG_C2L_ExecRemoteLogic = 200106;
  MSG_C2N_ChildNodeInit = 200107;  // 初始化子节点
  MSG_N2C_ChildNodeInit = 200108;
  MSG_C2N_ChildNodeClose = 200109;  // 关闭子节点
  MSG_N2C_ChildNodeClose = 200110;
  MSG_C2N_ChildNodeMainReset = 200111;  // 主节点重置
  MSG_N2C_ChildNodeMainReset = 200112;
  MSG_N2C_ChildNodeResetReset = 200113;           // 子节点重置
  MSG_L2C_RemoteCrossActivityPartition = 200114;  // 透传消息到指定Activity小分区的跨服服务器
  MSG_L2C_SetTime = 200115;                       // 设置时间
  MSG_C2L_SetTime = 200116;
  MSG_L2C_QueryTime = 200117;  // 查询时间
  MSG_C2L_QueryTime = 200118;
  MSG_ROUTER_MAX = 200499;

  // 跨服战
  MSG_WRESTLE_MIN = 200500;
  MSG_L2C_WrestleInfo = 200501;     // 获取信息&报名 logic -> cross
  MSG_C2L_WrestleInfo = 200502;     // cross -> logic
  MSG_L2C_WrestleStart = 200503;    // 发起战斗 logicA -> cross
  MSG_C2L_WrestleStart = 200504;    // cross -> logicA
  MSG_L2C_WrestleFinish = 200505;   // 战斗完成 logicA -> cross
  MSG_C2L_WrestleFinish = 200506;   // cross-> logicA
  MSG_C2L_WrestlePush = 200507;     // 通知对手 cross-> logicB
  MSG_L2C_WrestleAward = 200508;    // 结算发奖 logic -> cross
  MSG_C2L_WrestleAward = 200509;    // cross -> logic
  MSG_L2C_WrestleTopList = 200510;  // 获取赛场top玩家列表
  MSG_C2L_WrestleTopList = 200511;
  MSG_L2C_WrestleRank = 200512;  // 排行榜 - 实时
  MSG_C2L_WrestleRank = 200513;
  MSG_L2C_WrestleLastRank = 200514;  // 排行榜 - 荣耀殿堂
  MSG_C2L_WrestleLastRank = 200515;
  MSG_L2C_WrestleLike = 200516;  // 荣耀殿堂点赞
  MSG_C2L_WrestleLike = 200517;
  MSG_L2C_WrestleTest = 200518;  // 测试协议
  MSG_C2L_WrestleTest = 200519;
  MSG_L2C_WrestleRankFirst = 200520;  // 排行榜(本赛季或上赛季) - 第一名
  MSG_C2L_WrestleRankFirst = 200521;
  MSG_L2C_WrestleChangeRoom = 200522;  // 更换房间
  MSG_C2L_WrestleChangeRoom = 200523;
  MSG_C2L_WrestleRoomUpdate = 200524;            // 房间数据更新
  MSG_L2C_WrestleSetFighterLevelForGm = 200525;  // GM设置玩家赛场等级
  MSG_WRESTLE_MAX = 200599;

  // 公会
  MSG_GUILD_MIN = 200600;
  MSG_L2C_GuildList = 200601;  // 获取公会列表
  MSG_C2L_GuildList = 200602;
  MSG_L2C_GuildInfo = 200603;  // 获取公会信息
  MSG_C2L_GuildInfo = 200604;
  MSG_L2C_GuildCreate = 200605;  // 创建公会
  MSG_C2L_GuildCreate = 200606;
  MSG_L2C_GuildLogList = 200607;  // 获取公会日志
  MSG_C2L_GuildLogList = 200608;
  MSG_L2C_GuildManagerMember = 200609;  // 公会成员管理(任命为会长、任命为副会长(撤销)、踢出公会)
  MSG_C2L_GuildManagerMember = 200610;
  MSG_L2C_GuildModifyInfo = 200611;  // 修改公会信息(头像、设置加入条件、宣言)
  MSG_C2L_GuildModifyInfo = 200612;
  MSG_L2C_GuildSetName = 200613;  // 修改昵称
  MSG_C2L_GuildSetName = 200614;
  MSG_L2C_GuildModifyNotice = 200615;  // 修改公告
  MSG_C2L_GuildModifyNotice = 200616;
  MSG_L2C_GuildApplyList = 200617;  // 获取公会申请列表(会长or副会长)
  MSG_C2L_GuildApplyList = 200618;
  MSG_L2C_GuildApplyRatify = 200619;  // 审核公会申请
  MSG_C2L_GuildApplyRatify = 200620;
  MSG_L2C_GuildUserApply = 200621;  // 玩家申请(申请、取消申请、快速加入)
  MSG_C2L_GuildUserApply = 200622;
  MSG_L2C_GuildSendMail = 200623;  // 发送公会邮件(会长or副会长)
  MSG_C2L_GuildSendMail = 200624;
  MSG_L2C_GuildQuit = 200625;  // 退出公会 or 解散公会（for会长）
  MSG_C2L_GuildQuit = 200626;
  MSG_L2C_GuildDisband = 200627;
  MSG_C2L_GuildDisband = 200628;
  MSG_L2C_GuildSearch = 200629;  // 查找公会
  MSG_C2L_GuildSearch = 200630;
  MSG_L2C_GuildGetMembers = 200631;  // 获取公会成员
  MSG_C2L_GuildGetMembers = 200632;
  MSG_L2C_GuildGetDeclaration = 200633;  // 获取公会宣言
  MSG_C2L_GuildGetDeclaration = 200634;
  MSG_C2L_GuildNotify = 200635;      // 推送玩家变化
  MSG_C2L_GuildUpdateInfo = 200636;  // 推送公会等级和经验
  MSG_L2C_GuildDonate = 200637;      // 公会捐献
  MSG_C2L_GuildDonate = 200638;
  MSG_L2C_GuildGetDonateAward = 200639;  // 获取捐献奖励
  MSG_C2L_GuildGetDonateAward = 200640;
  MSG_L2C_GuildRank = 200641;  // 获取公会排行榜
  MSG_C2L_GuildRank = 200642;
  MSG_L2C_GuildGetLogicGuilds = 200643;  //
  MSG_C2L_GuildGetLogicGuilds = 200644;
  MSG_C2L_GuildMail = 200645;
  MSG_L2C_GuildDonateLogList = 200646;  // 获取公会捐赠日志 NOLOG
  MSG_C2L_GuildDonateLogList = 200647;
  MSG_L2C_GuildListGetDetail = 200648;  // 获取公会列表的公会详情
  MSG_C2L_GuildListGetDetail = 200649;
  MSG_L2C_GuildGetBadgeList = 200650;  // 获取徽章列表
  MSG_C2L_GuildGetBadgeList = 200651;
  MSG_L2C_GuildGetDivisionAwardInfo = 200652;  // 获取公会段位奖励相关信息
  MSG_C2L_GuildGetDivisionAwardInfo = 200653;
  MSG_L2C_GuildGetMedals = 200654;  // 获取功勋数据
  MSG_C2L_GuildGetMedals = 200656;
  MSG_L2C_GuildMedalLike = 200657;  // 功勋送花
  MSG_C2L_GuildMedalLike = 200658;
  MSG_L2C_GuildUserSyncTowerSeason = 200659;  // 游戏服向跨服同步百塔进度
  MSG_L2C_GuildSyncLogicGuild = 200660;       // 同步游戏服公会数据
  MSG_C2L_GuildSyncLogicGuild = 200661;
  MSG_L2C_GuildSyncSeasonArenaData = 200662;  // 同步功勋需要的赛季竞技场数据
  MSG_C2L_GuildSyncSeasonArenaData = 200663;
  MSG_L2C_GuildSendRecruitMsg = 200664;  // 公会发布招募消息
  MSG_C2L_GuildSendRecruitMsg = 200665;
  MSG_L2C_GuildSetLevelForGM = 200680;      // gm修改公会等级，暂时放在这里
  MSG_L2C_GuildJoinOrCreateForGM = 200681;  // robot创号，自动加入公会，没有就创建
  MSG_C2L_GuildSyncKickCount = 200682;      // 向公会官员，同步当日踢人次数
  MSG_C2L_GuildDeleteMails = 200683;        // 删除玩家的公会邮件
  MSG_L2C_GuildCombineApply = 200684;       // 公会合并请求 (1-申请合并 2-邀请被合并)
  MSG_C2L_GuildCombineApply = 200685;
  MSG_L2C_GuildCombineCheck = 200686;  // 公会合并确认
  MSG_C2L_GuildCombineCheck = 200687;
  MSG_L2C_GuildCombineRatify = 200688;  // 公会合并审核
  MSG_C2L_GuildCombineRatify = 200689;
  MSG_L2C_GuildUserSyncLeaveCd = 200690;  // 游戏服向跨服同步玩家公会cd信息
  MSG_C2C_GuildListGetDetail = 200691;    // 跨战区获取公会列表的公会详情

  // 公会副本的从200701开始
  MSG_L2C_GuildDungeonInfo = 200701;  //
  MSG_C2L_GuildDungeonInfo = 200702;
  MSG_L2C_GuildDungeonChapterInfo = 200703;  //
  MSG_C2L_GuildDungeonChapterInfo = 200704;
  MSG_L2C_GuildDungeonFightStart = 200705;
  MSG_C2L_GuildDungeonFightStart = 200706;
  MSG_L2C_GuildDungeonFightFinish = 200707;
  MSG_C2L_GuildDungeonFightFinish = 200708;  //
  MSG_L2C_GuildDungeonRecvBox = 200709;
  MSG_C2L_GuildDungeonRecvBox = 200710;
  MSG_L2C_GuildDungeonSetFocus = 200711;
  MSG_C2L_GuildDungeonSetFocus = 200712;
  MSG_C2L_GuildDungeonNotify = 200713;        //
  MSG_L2C_GuildDungeonFightLogList = 200714;  //
  MSG_C2L_GuildDungeonFightLogList = 200715;
  MSG_L2C_GuildDungeonAward = 200716;  // 结算发奖 logic -> cross
  MSG_C2L_GuildDungeonAward = 200717;  // cross -> logic
  MSG_L2C_GuildDungeonGetMembersWeeklyDamage = 200718;
  MSG_C2L_GuildDungeonGetMembersWeeklyDamage = 200719;
  MSG_L2C_GuildDungeonGetHallOfFame = 200720;
  MSG_C2L_GuildDungeonGetHallOfFame = 200721;
  MSG_C2L_GuildDungeonNotifyMsg = 200722;    // 公会副本推送消息
  MSG_L2C_GuildDungeonGetStrategy = 200723;  // 获取秘技信息
  MSG_C2L_GuildDungeonGetStrategy = 200724;
  MSG_L2C_GuildDungeonUseStrategy = 200725;  // 使用秘技
  MSG_C2L_GuildDungeonUseStrategy = 200726;
  MSG_L2C_GuildDungeonRecvAllBox = 200727;  // 一键领取boss宝箱奖励
  MSG_C2L_GuildDungeonRecvAllBox = 200728;
  MSG_L2C_GuildDungeonGetMembersFightInfo = 200729;  // 获取成员挑战信息
  MSG_C2L_GuildDungeonGetMembersFightInfo = 200730;
  MSG_L2C_GuildDungeonBuyChallengeTimes = 200731;  // 增加购买次数
  MSG_L2C_GuildDungeonSetChapterForGm = 200798;    // gm修改副本章节
  MSG_GUILD_MAX = 200799;

  // 排行榜
  MSG_RANK_MIN = 200800;
  MSG_L2C_RankUpdate = 200801;  // 更新排行榜数据
  MSG_C2L_RankUpdate = 200802;
  MSG_L2C_RankGetList = 200803;  // 获取排行榜列表
  MSG_C2L_RankGetList = 200804;
  MSG_L2C_RankGetOldList = 200805;  // 获取老的排行榜列表
  MSG_C2L_RankGetOldList = 200806;
  MSG_L2C_RankDelete = 200807;    // 删除排行榜中的数据
  MSG_L2C_RankGetFirst = 200808;  // 获取排行榜第一名
  MSG_C2L_RankGetFirst = 200809;
  MSG_L2C_RankLike = 200810;  // 排行榜点赞
  MSG_C2L_RankLike = 200811;
  MSG_L2C_RankGetForReset = 200812;  // 结算获取排行榜
  MSG_C2L_RankGetForReset = 200813;
  MSG_RANK_MAX = 200899;

  // 公会分享
  MSG_GUILD_CHEST_MIN = 200900;
  MSG_L2C_GuildChestGetData = 200901;  // 获取数据
  MSG_C2L_GuildChestGetData = 200902;
  MSG_L2C_GuildChestRecv = 200903;  // 领取公会分享
  MSG_C2L_GuildChestRecv = 200904;
  MSG_L2C_GuildChestLike = 200905;  // 点赞公户分享
  MSG_C2L_GuildChestLike = 200906;
  MSG_L2C_GuildChestActivate = 200907;  // 公会宝箱激活
  MSG_C2L_GuildChestActivate = 200908;
  MSG_L2C_GuildChestCanActivate = 200909;  // 是否激活
  MSG_C2L_GuildChestCanActivate = 200910;
  MSG_C2L_GuildChestExpired = 200911;  // 过期宝箱
  MSG_L2C_GuildChestDetail = 200912;   // 公会宝箱细节
  MSG_C2L_GuildChestDetail = 200913;
  MSG_C2L_GuildChestNewChest = 200914;  // 公会宝箱新公户宝箱
  MSG_GUILD_CHEST_MAX = 200999;

  // 世界boss
  MSG_WorldBoss_MIN = 201100;
  MSG_L2C_WorldBossMatchRoom = 201101;  // 匹配房间
  MSG_C2L_WorldBossMatchRoom = 201102;
  MSG_L2C_WorldBossRank = 201103;  // 排行榜
  MSG_C2L_WorldBossRank = 201104;
  MSG_L2C_WorldBossGetRoomLog = 201105;  // 房间日志
  MSG_C2L_WorldBossGetRoomLog = 201106;
  MSG_L2C_WorldBossSettle = 201107;  // 结算
  MSG_C2L_WorldBossSettle = 201108;
  MSG_L2C_WorldBossSyncFightData = 201111;  // 同步战斗数据：挑战/扫荡
  MSG_C2L_WorldBossSyncFightData = 201112;
  MSG_L2C_WorldBossRoomInfo = 201113;  // 房间信息
  MSG_C2L_WorldBossRoomInfo = 201114;
  MSG_L2C_WorldBossGetPeakPlayers = 201115;  // 获取巅峰竞技场参赛选手
  MSG_WB2Guild_SyncBakData = 201116;         // 公会同步备份数据
  MSG_Guild2WB_SyncBakData = 201117;
  MSG_WorldBoss_MAX = 201199;

  // 失序空间
  MSG_DisorderLand_MIN = 201200;
  MSG_L2C_DisorderLandSyncHurdleLevel = 201201;  // 同步关卡等级
  MSG_C2L_DisorderLandSyncHurdleLevel = 201202;
  MSG_L2C_DisorderLandRank = 201203;  // 排行榜
  MSG_C2L_DisorderLandRank = 201204;
  MSG_DisorderLand_MAX = 201299;

  // 巅峰竞技场
  MSG_PEAK_MIN = 201300;
  MSG_L2C_PeakInitData = 201301;  // 初始数据
  MSG_L2C_PeakBaseData = 201302;  // 基本信息
  MSG_C2L_PeakBaseData = 201303;
  MSG_L2C_PeakGetMatch = 201304;  // 获取比赛数据
  MSG_C2L_PeakGetMatch = 201305;
  MSG_L2C_PeakGuessList = 201306;  // 获取竞猜列表数据
  MSG_C2L_PeakGuessList = 201307;
  MSG_L2C_PeakAddGuessCount = 201308;  // 增加竞猜下注次数
  MSG_C2L_PeakAddGuessCount = 201309;
  MSG_L2C_PeakResult = 201310;  // 比赛结果
  MSG_C2L_PeakResult = 201311;
  MSG_L2C_PeakRankList = 201312;  // 排行榜
  MSG_C2L_PeakRankList = 201313;
  MSG_C2L_PeakFight = 201314;  // 战斗
  MSG_L2C_PeakFight = 201315;
  MSG_L2C_PeakFighterDetail = 201316;  // 选手完赛详情数据
  MSG_C2L_PeakFighterDetail = 201317;
  MSG_L2C_PeakPushState = 201318;  // 推送状态更新
  MSG_C2L_PeakPushState = 201319;
  MSG_L2C_PeakUpdatePlayer = 201320;  // 新周期开始，请求更新分数和参赛选手数据
  MSG_C2L_PeakUpdatePlayer = 201321;
  MSG_C2L_PeakGetSnapshot = 201322;  // 获取快照
  MSG_L2C_PeakGetSnapshot = 201323;
  MSG_L2C_PeakRankFirst = 201324;  // 排行榜 - 第一名
  MSG_C2L_PeakRankFirst = 201325;
  MSG_P2Guild_SyncBakData = 201326;  // 公会同步备份数据
  MSG_Guild2P_SyncBakData = 201327;
  MSG_L2C_PeakGetLastBattleReport = 201328;  // 获取上一场战报
  MSG_C2L_PeakGetLastBattleReport = 201329;
  MSG_PEAK_MAX = 201399;

  // 公会战 funcID=258
  MSG_MIN_GST = 225800;
  MSG_L2CS_GSTGetData = 225801;  // 获取基础数据
  MSG_CS2L_GSTGetData = 225802;
  MSG_L2CS_GSTGetGroundData = 225803;  // 获取地块信息数据 NOLOG
  MSG_CS2L_GSTGetGroundData = 225804;
  MSG_L2CS_GSTGetTeamsData = 225805;  // 获取队伍数据
  MSG_CS2L_GSTGetTeamsData = 225806;
  MSG_L2CS_GSTTeamOperate = 225809;  // 队伍操作.移动队伍,托管队伍
  MSG_CS2L_GSTTeamOperate = 225810;
  MSG_L2CS_GSTExchangeGroundTeam = 225811;  // 交换地块上队伍出战顺序 NOLOG
  MSG_CS2L_GSTExchangeGroundTeam = 225812;
  MSG_L2CS_GSTRank = 225813;  // 排行榜
  MSG_CS2L_GSTRank = 225814;
  MSG_L2CS_GSTGetTasksData = 225815;  // 获取任务数据
  MSG_CS2L_GSTGetTasksData = 225816;
  MSG_L2CS_GSTGetTasksReward = 225817;  // 获取任务奖励
  MSG_CS2L_GSTGetTasksReward = 225818;
  MSG_L2CS_GSTGetHangUpReward = 225819;  // 放置奖励
  MSG_CS2L_GSTGetHangUpReward = 225820;
  MSG_L2CS_GSTDonate = 225821;  // 捐献祝福
  MSG_CS2L_GSTDonate = 225822;
  MSG_CS2L_GSTPreviewHangUpReward = 225823;  // 预览放置奖励
  MSG_L2CS_GSTPreviewHangUpReward = 225824;
  MSG_L2CS_GSTMessageEdit = 225825;  // 留言板编辑
  MSG_CS2L_GSTMessageEdit = 225826;
  MSG_CS2L_GSTFight = 225827;  // 战斗
  MSG_L2CS_GSTFight = 225828;
  MSG_CS2L_GSTSetTeam = 225829;  // 同步队伍信息
  MSG_L2CS_GSTSetTeam = 225830;
  MSG_L2CS_GSTAward = 225833;  // 请求结算
  MSG_CS2L_GSTAward = 225834;
  MSG_L2CS_GSTGetGuildDonateData = 225835;  // 获取捐赠信息
  MSG_CS2L_GSTGetGuildDonateData = 225836;
  MSG_L2CS_GSTGetGuildDonateMemData = 225837;  // 获取捐赠排行榜成员信息
  MSG_CS2L_GSTGetGuildDonateMemData = 225838;
  MSG_L2CS_GSTSYNCGuildUserInfo = 225839;  // 同步公会用户数据
  MSG_CS2L_GSTSYNCGuildUserInfo = 225840;
  MSG_L2CS_GSTUpdateViewFormation = 225843;  // 查看阵容更新任务进度
  MSG_CS2L_GSTUpdateViewFormation = 225844;
  MSG_L2CS_GSTGuildBuildGetData = 225845;  // 获取建筑信息
  MSG_CS2L_GSTGuildBuildGetData = 225846;
  MSG_L2CS_GSTGuildBuildDonate = 225847;  // 建筑贡献
  MSG_CS2L_GSTGuildBuildDonate = 225848;
  MSG_L2CS_GSTGuildBuildDonateRank = 225849;  // 捐赠排行榜
  MSG_CS2L_GSTGuildBuildDonateRank = 225850;
  MSG_L2CS_GSTGuildBuildDispatchHero = 225851;  // 派遣英雄
  MSG_CS2L_GSTGuildBuildDispatchHero = 225852;
  MSG_L2CS_GSTGuildBuildGetTaskData = 225853;  // 获取建筑任务数据
  MSG_CS2L_GSTGuildBuildGetTaskData = 225854;
  MSG_L2CS_GSTGuildBuildRecvTaskAward = 225855;  // 领取建筑任务奖励
  MSG_CS2L_GSTGuildBuildRecvTaskAward = 225856;
  MSG_L2CS_GSTGuildFirstCenterAward = 225857;  // 领取中心首占奖励
  MSG_CS2L_GSTGuildFirstCenterAward = 225858;
  MSG_L2CS_GSTGroupUsersRank = 225859;  // 获取组内玩家排行榜
  MSG_CS2L_GSTGroupUsersRank = 225860;
  MSG_L2CS_GSTGroupUsersRankLike = 225861;  // 组内玩家排行榜点赞
  MSG_CS2L_GSTGroupUsersRankLike = 225862;
  MSG_CS2L_GSTPushChatMessage = 225863;
  MSG_L2CS_GSTArenaVote = 225864;  // 擂台投票
  MSG_CS2L_GSTArenaVote = 225865;
  MSG_L2CS_GSTGetArenaVoteRecord = 225866;
  MSG_CS2L_GSTGetArenaVoteRecord = 225867;
  MSG_L2CS_GSTBossGet = 225868;  // 获取boss信息
  MSG_CS2L_GSTBossGet = 225869;
  MSG_L2CS_GSTBossFight = 225870;  // boss战斗
  MSG_CS2L_GSTBossFight = 225871;
  MSG_L2CS_GSTBossAward = 225872;  // boss领奖
  MSG_CS2L_GSTBossAward = 225873;
  MSG_L2CS_GSTBossRank = 225874;  // boss排行
  MSG_CS2L_GSTBossRank = 225875;
  MSG_L2CS_GSTScorePreview = 225876;  // 积分预览
  MSG_CS2L_GSTScorePreview = 225877;
  MSG_CS2L_GSTMobRankSettle = 225878;
  MSG_L2CS_GSTMobRankSettle = 225879;
  MSG_L2CS_GuildMobilizationGuildRank = 225880;  // 公会竞赛排名
  MSG_CS2L_GuildMobilizationGuildRank = 225881;
  MSG_GUILD2GST_GSTSyncGuildMob = 225889;
  MSG_CS2L_GSTGuildBuildTaskProgressUpdate = 225890;  // 建筑任务同步
  MSG_CS2L_GSTTaskUpdateOther = 225891;               // 其他人的任务进度更新
  MSG_CS2L_GSTTaskUpdate = 225892;                    // 任务进度更新
  MSG_GST2GUILD_AddDivisionScore = 225893;            // 给公会结算段位积分
  MSG_GUILD2GST_AddDivisionScore = 225894;
  MSG_L2CS_GSTPushSta = 225895;
  MSG_CS2L_GSTPushSta = 225896;          // 同步公会战状态给游戏服
  MSG_GST2GUILD_GSTSyncGuilds = 225897;  // 同步公会信息
  MSG_GUILD2GST_GSTSyncGuilds = 225898;
  MSG_MAX_GST = 225899;

  // 赛季竞技场
  MSG_MIN_SEASON_ARENA = 225900;
  MSG_L2CS_SeasonArenaGetData = 225901;  // 获取竞技场数据
  MSG_CS2L_SeasonArenaGetData = 225902;
  MSG_L2CS_SeasonArenaGetRankList = 225903;  // 获取排行榜
  MSG_CS2L_SeasonArenaGetRankList = 225904;
  MSG_L2CS_SeasonArenaGetFame = 225905;  // 获取荣耀殿堂
  MSG_CS2L_SeasonArenaGetFame = 225906;
  MSG_L2CS_SeasonArenaFightStart = 225907;  // 战斗开始
  MSG_CS2L_SeasonArenaFightStart = 225908;
  MSG_L2CS_SeasonArenaFightFinish = 225909;  // 战斗结束
  MSG_CS2L_SeasonArenaFightFinish = 225910;
  MSG_CS2L_SeasonArenaDefinePush = 225911;  // 防守者战斗结束推送
  MSG_CS2L_SeasonArenaAward = 225912;       // 赛季领奖
  MSG_L2CS_SeasonArenaAward = 225913;
  MSG_L2CS_SeasonArenaGetSta = 225914;  // 获取玩法状态
  MSG_CS2L_SeasonArenaGetSta = 225915;
  MSG_L2CS_SeasonArenaRefresh = 225916;  // 搜寻对手
  MSG_CS2L_SeasonArenaRefresh = 225917;
  MSG_CS2L_SeasonArenaTopChange = 225918;             // 第一名发生变化
  MSG_L2CS_SeasonArenaUpdateSnapshot = 225919;        // 更新快照
  MSG_L2CS_GmSetSeasonArenaScore = 225920;            // 设置赛季竞技场分数
  MSG_L2CS_SeasonArenaUpdateFormationPower = 225921;  // 同步阵容战斗力
  MSG_MAX_SEASON_ARENA = 225999;

  // 公会战-龙战
  MSG_MIN_GST_DRAGON = 226000;
  MSG_L2CS_GSTDragonGetData = 226001;  // 获取基础数据
  MSG_CS2L_GSTDragonGetData = 226002;
  MSG_L2CS_GSTDragonGetCultivation = 226003;  // 获取养成数据
  MSG_CS2L_GSTDragonGetCultivation = 226004;
  MSG_L2CS_GSTDragonShow = 226005;  // 修改龙展示
  MSG_CS2L_GSTDragonShow = 226006;
  MSG_L2CS_GSTDragonEvolve = 226007;  // 龙进化
  MSG_CS2L_GSTDragonEvolve = 226008;
  MSG_L2CS_GSTDragonFightStart = 226009;  // 龙战开始战斗
  MSG_CS2L_GSTDragonFightStart = 226010;
  MSG_L2CS_GSTDragonFightFinish = 226011;  // 龙战结束战斗
  MSG_CS2L_GSTDragonFightFinish = 226012;
  MSG_L2CS_GSTDragonTaskGetData = 226013;  // 龙战任务获取数据
  MSG_CS2L_GSTDragonTaskGetData = 226014;
  MSG_L2CS_GSTDragonTaskAward = 226015;  // 龙战任务领奖
  MSG_CS2L_GSTDragonTaskAward = 226016;
  MSG_L2CS_GSTDragonAward = 226017;  // 龙战结算奖励
  MSG_CS2L_GSTDragonAward = 225018;
  MSG_CS2L_GSTDragonTaskProgressUpdate = 226019;  // 龙战任务同步
  MSG_L2CS_GSTDragonRank = 226020;                // 公会内成员排行榜
  MSG_CS2L_GSTDragonRank = 226021;
  MSG_L2CS_GSTDragonFightBuyCountStart = 226022;  // 钻石购买挑战次数开始
  MSG_CS2L_GSTDragonFightBuyCountStart = 226023;
  MSG_L2CS_GSTDragonFightBuyCountFinish = 226024;  // 钻石购买挑战次数结束
  MSG_CS2L_GSTDragonFightBuyCountFinish = 226025;
  MSG_L2CS_GSTDragonFightUseTicket = 226026;  // 道具购买挑战次数
  MSG_CS2L_GSTDragonFightUseTicket = 226027;
  MSG_L2CS_GSTDragonSkillOperate = 226028;  // 龙战攻城技能
  MSG_CS2L_GSTDragonSkillOperate = 226029;
  MSG_L2CS_GSTDragonStrategySkill = 226030;  // 龙战祝福技能
  MSG_CS2L_GSTDragonStrategySkill = 226031;

  MSG_MAX_GST_DRAGON = 226099;

  // 热度榜
  MSG_MIN_HOT_RANK = 226100;
  MSG_L2CS_HotRankCollectionLogUpdate = 226101;
  MSG_CS2L_HotRankCollectionLogUpdate = 226102;
  MSG_L2CS_HotRankGet = 226103;
  MSG_CS2L_HotRankGet = 226104;
  MSG_L2CS_HotRankSyncTalentTreeHot = 226105;
  MSG_CS2L_HotRankSyncTalentTreeHot = 226106;
  MSG_L2CS_HotRankFlushTalentTreeHot = 226107;  //
  MSG_CS2L_HotRankFlushTalentTreeHot = 226108;
  MSG_MAX_HOT_RANK = 226199;

  // 公会竞赛
  MSG_MIN_Guild_Mobilization = 226200;
  MSG_L2CS_GuildMobilizationGetData = 226201;
  MSG_CS2L_GuildMobilizationGetData = 226202;
  MSG_L2CS_GuildMobilizationAcceptTask = 226203;
  MSG_CS2L_GuildMobilizationAcceptTask = 226204;
  MSG_L2CS_GuildMobilizationSignTask = 226205;
  MSG_CS2L_GuildMobilizationSignTask = 226206;
  MSG_L2CS_GuildMobilizationFinishTaskLogs = 226207;
  MSG_CS2L_GuildMobilizationFinishTaskLogs = 226208;
  MSG_L2CS_GuildMobilizationPersonalRank = 226209;
  MSG_CS2L_GuildMobilizationPersonalRank = 226210;
  MSG_L2CS_GuildMobilizationRecvScoreLevel = 226211;
  MSG_CS2L_GuildMobilizationRecvScoreLevel = 226212;
  MSG_L2CS_GuildMobilizationFreshTask = 226213;
  MSG_CS2L_GuildMobilizationFreshTask = 226214;
  MSG_L2CS_GuildMobilizationBuyTimes = 226215;
  MSG_CS2L_GuildMobilizationBuyTimes = 226216;
  MSG_L2CS_GuildMobilizationSysFinishedTask = 226217;
  MSG_CS2L_GuildMobilizationSysFinishedTask = 226218;
  MSG_L2CS_GuildMobilizationEditMessageBoard = 226219;
  MSG_CS2L_GuildMobilizationEditMessageBoard = 226220;
  MSG_L2CS_GuildMobilizationGiveUpTask = 226221;
  MSG_CS2L_GuildMobilizationGiveUpTask = 226222;
  MSG_L2CS_GuildMobilizationScoreAward = 226223;
  MSG_CS2L_GuildMobilizationScoreAward = 226224;
  MSG_GST2GUILD_GuildMobilizationSettle = 226225;
  MSG_GUILD2GST_GuildMobilizationSettle = 226226;
  MSG_L2CS_GuildMobilizationCancelTask = 226227;
  MSG_CS2L_GuildMobilizationCancelTask = 226228;
  MSG_CS2L_GuildMobilizationBeCancelTask = 226229;
  MSG_L2CS_GuildMobilizationAddScoreForGM = 226298;
  MSG_MAX_Guild_Mobilization = 226299;

  // 公会战-占矿
  MSG_MIN_GSTOre = 233000;
  MSG_L2CS_GSTOreGetData = 233001;  // 获取数据
  MSG_CS2L_GSTOreGetData = 233002;
  MSG_L2CS_GSTOreFightStart = 233003;
  MSG_CS2L_GSTOreFightStart = 233004;
  MSG_L2CS_GSTOreFightFinish = 233005;
  MSG_CS2L_GSTOreFightFinish = 233006;
  MSG_L2CS_GSTOreBuyTimes = 233007;
  MSG_CS2L_GSTOreBuyTimes = 233008;
  MSG_L2CS_GSTOreSearchAssist = 233009;  // 搜寻可协助的矿
  MSG_CS2L_GSTOreSearchAssist = 233010;
  MSG_L2CS_GSTOreOccupy = 233011;
  MSG_CS2L_GSTOreOccupy = 233012;
  MSG_L2CS_GSTOreGetOreData = 233013;
  MSG_CS2L_GSTOreGetOreData = 233014;
  MSG_MAX_GSTOre = 233099;

  // 公会战-科技
  MSG_MIN_GSTTech = 233100;
  MSG_L2CS_GSTTechGetData = 233101;  // 获取数据
  MSG_CS2L_GSTTechGetData = 233102;
  MSG_L2CS_GSTTechDonate = 233103;  // 捐献资源
  MSG_CS2L_GSTTechDonate = 233104;
  MSG_L2CS_GSTTechTaskReward = 233107;
  MSG_CS2L_GSTTechTaskReward = 233108;
  MSG_L2CS_GSTTechGuildUserRank = 233109;
  MSG_CS2L_GSTTechGuildUserRank = 233110;
  MSG_L2CS_GSTSkillAssemble = 233111;  // 号令集结技能
  MSG_CS2L_GSTSkillAssemble = 233112;
  MSG_L2CS_GSTTechSign = 233113;
  MSG_CS2L_GSTTechSign = 233114;
  MSG_CS2L_GSTTechTaskUpdate = 233198;
  MSG_MAX_GSTTech = 233199;

  // 公会战-科技
  MSG_MIN_GSTChallenge = 233200;
  MSG_L2CS_GSTChallengeGetData = 233201;  // 获取数据
  MSG_CS2L_GSTChallengeGetData = 233202;
  MSG_L2CS_GSTChallengeMatch = 233203;  // 匹配
  MSG_CS2L_GSTChallengeMatch = 233204;
  MSG_L2CS_GSTChallengeFightStart = 233205;
  MSG_CS2L_GSTChallengeFightStart = 233206;
  MSG_L2CS_GSTChallengeFightFinish = 233207;
  MSG_CS2L_GSTChallengeFightFinish = 233208;
  MSG_L2CS_GSTChallengeBuffChoose = 233209;
  MSG_CS2L_GSTChallengeBuffChoose = 233210;
  MSG_CS2L_GSTChallengeTaskUpdate = 233211;
  MSG_L2CS_GSTChallengeTaskReward = 233212;
  MSG_CS2L_GSTChallengeTaskReward = 233213;
  MSG_L2CS_GSTChallengeRank = 233214;
  MSG_CS2L_GSTChallengeRank = 233215;
  MSG_L2CS_GSTChallengeAward = 233216;
  MSG_CS2L_GSTChallengeAward = 233217;
  MSG_MAX_GSTChallenge = 233299;

  MSG_MIN_SeasonComplianceRank = 233300;
  MSG_L2CS_SeasonComplianceRankUpdate = 233301;  // 更新排行榜数据
  MSG_CS2L_SeasonComplianceRankUpdate = 233302;
  MSG_L2CS_SeasonComplianceRankGetList = 233304;  // 获取排行榜列表
  MSG_CS2L_SeasonComplianceRankGetList = 233305;
  MSG_L2CS_SeasonComplianceRankAward = 233306;  // 结算获取排行榜
  MSG_CS2L_SeasonComplianceRankAward = 233307;
  MSG_L2CS_SeasonComplianceGetData = 233308;  // 获取数据
  MSG_CS2L_SeasonComplianceGetData = 233309;
  MSG_MAX_SeasonComplianceRank = 233399;

  // 赛季地图
  MSG_MIN_SeasonMap = 234300;
  MSG_L2CS_SeasonMapPositionLogs = 234307;
  MSG_CS2L_SeasonMapPositionLogs = 234308;
  MSG_L2CS_SeasonMapSync = 234398;
  MSG_MAX_SeasonMap = 234399;

  // 公平竞技场
  MSG_MIN_BALANCE_ARENA = 234400;
  MSG_L2CS_BalanceArenaGetData = 234401;  // 获取数据
  MSG_CS2L_BalanceArenaGetData = 234402;
  MSG_L2CS_BalanceArenaGetRankList = 234403;  // 获取排行榜
  MSG_CS2L_BalanceArenaGetRankList = 234404;
  MSG_MAX_BALANCE_ARENA = 234499;
}

message L2C_SayHi {
  uint64 id = 1;
  repeated uint64 current = 2;
  repeated uint64 del = 3;
}

message C2L_SayHi {}

message C2L_KeepAlive {
  string extra = 1;  // json
}

message L2C_KeepAlive {
  string extra = 1;  // json
}

// 源logic->cross
// 源logic告知cross，想要把消息透传给目标logic
message L2C_SendRemoteLogic {
  uint64 target_sid = 1;    // 目标服务器id
  uint32 remote_logic = 2;  // enum REMOTE_LOGIC
  bytes data = 3;           // 二进制数据流(encoded pb message)
}

// cross->目标logic
// 把源logic的消息，透传给目标logic
message C2L_ExecRemoteLogic {
  uint64 source_sid = 1;    // 源服务器id
  uint32 remote_logic = 2;  // enum REMOTE_LOGIC
  bytes data = 3;           // 二进制数据流(encoded pb message)
}

// 跨服通知子节点启动
message C2N_ChildNodeInit {
  uint32 act_id = 1;
  uint32 partition = 2;
  uint64 reset_time = 3;
  repeated uint64 servers = 4;
}

message N2C_ChildNodeInit {
  uint32 ret = 1;
  C2N_ChildNodeInit msg = 2;
  uint64 last_reset_time = 3;  // 子节点上次重置时间
}

// 跨服通知子节点关闭
message C2N_ChildNodeClose {
  uint32 act_id = 1;
  uint32 partition = 2;
}

message N2C_ChildNodeClose {
  uint32 ret = 1;
  C2N_ChildNodeClose msg = 2;
}

// 跨服通知子节点更新分区信息
message C2N_ChildNodeMainReset {
  uint32 act_id = 1;
  uint32 partition = 2;
  uint64 reset_time = 3;
  repeated uint64 servers = 4;
}

message N2C_ChildNodeNoticeReset {
  uint32 ret = 1;
  C2N_ChildNodeMainReset msg = 2;
}

// 子节点通知跨服重置完毕
message N2C_ChildNodeChildReset {
  uint32 ret = 1;
  uint64 reset_time = 2;
}

message L2C_SetTime {
  uint64 client_msg_id = 1;
  uint64 time = 2;
}

message C2L_SetTime {
  uint32 ret = 1;
  uint64 client_msg_id = 2;
  uint64 time = 3;
}

message L2C_QueryTime {
  uint64 client_msg_id = 1;
}

message C2L_QueryTime {
  uint32 ret = 1;
  uint64 client_msg_id = 2;
  uint64 time = 3;
}

//
message L2C_WrestleInfo {
  cl.UserSnapshot snapshot = 1;  // 快照
  bool need_change = 2;          // 回传游戏服
}

message C2L_WrestleInfo {
  uint32 ret = 1;
  cr.Fighter fighter = 2;             // 最新数据
  repeated cr.Fighter defenders = 3;  // 对手信息
  repeated cr.Fighter top3 = 4;       // 顶级战场，前三名玩家数据
  bool need_change = 5;               // 回传游戏服
}

// LogicA -> Cross
message L2C_WrestleStart {
  uint64 defender_uid = 1;   // 防守方id
  uint64 defender_sid = 2;   // 防守方sid
  uint32 defender_rank = 3;  // 顶级战场，防守方排名（普通战场不需要传）
  uint64 attacker_sid = 4;   // 进攻方sid
}

// Cross -> logicA
message C2L_WrestleStart {
  uint32 ret = 1;
  uint64 attacker_sid = 2;  // 进攻方sid
  uint64 attacker_uid = 3;  // 进攻方id
  uint64 defender_uid = 4;  // 防守方id
  uint64 defender_sid = 5;  // 防守方sid
  uint32 bot_id = 6;        // 机器人在量表中的id（真实玩家此值为0）
  string bot_name = 7;      // 机器人昵称（真实玩家此值为空）
}

// logicA -> Cross
message L2C_WrestleFinish {
  uint64 attacker_sid = 1;  // 进攻方服务器id
  uint64 attacker_uid = 2;  // 进攻方玩家id
  uint64 defender_sid = 3;  // 防守方玩家sid
  uint64 defender_uid = 4;  // 防守方玩家id
  reserved 5;
  cl.UserSnapshot defender_snapshot = 6;  // 防守方快照
  bool attacker_win = 7;                  // 进攻方是否胜利
  string report_id = 8;                   // 战报id
}

// Cross -> logicA
message C2L_WrestleFinish {
  uint32 ret = 1;
  cr.Fighter attacker = 2;                // 进攻方战后数据
  FighterResult attacker_old_result = 3;  // 进攻方战前成绩
  FighterResult attacker_new_result = 4;  // 进攻方战后成绩
  reserved 5;
  FighterResult defender_old_result = 6;      // 防守方战前成绩
  FighterResult defender_new_result = 7;      // 防守方战后成绩
  cl.MiniUserSnapshot defender_snapshot = 8;  // 防守方快照
  bool attacker_win = 9;                      // 进攻方是否胜利
  bool is_bot = 10;                           // 对手是否是机器人
  string report_id = 11;                      // 战报id
  bool promoted = 12;                         // 是否自动晋升
}

// 跨服战玩家成绩
message FighterResult {
  uint32 score = 1;  // 积分
  uint32 rank = 2;   // 排名
}

// Cross -> logicB
message C2L_WrestlePush {
  uint32 ret = 1;
  FighterResult old_result = 2;  // 战前成绩
  cr.Fighter defender = 3;       // 战后数据
  reserved 4;
  cl.MiniUserSnapshot attacker_snapshot = 5;  // 进攻方快照
  FighterResult new_result = 6;               // 战后成绩
  bool attacker_win = 7;                      // 进攻方是否胜利
  string report_id = 8;                       // 战报id
}

// 结算发奖
message L2C_WrestleAward {
  uint64 sid = 1;  // 服务器id
}
message C2L_WrestleAward {
  uint32 ret = 1;
  uint64 sid = 2;  // 服务器id
  repeated WrestleRankBakData ranks = 3;
  int64 time = 4;  // 结算开始时间
}

message WrestleRankBakData {
  uint32 last_room_rank = 1;  // 结算前，房间内排名
  cr.RankBakData result = 2;  // 结算后数据
}

// 获取赛场top玩家列表
message L2C_WrestleTopList {
  uint32 level = 1;  // 战场等级0-5 对应common.WRESTLE_LEVEL
}
message C2L_WrestleTopList {
  uint32 ret = 1;
  uint32 level = 2;  // 战场等级0-5，0代表全部
  map<uint32, Snapshots> snapshots = 3;
}

message Snapshots {
  repeated cl.UserSnapshot data = 1;
}

// 排行榜 - 实时
message L2C_WrestleRank {
  uint64 uid = 1;
}
message C2L_WrestleRank {
  uint32 ret = 1;
  uint64 uid = 2;
  uint32 self_rank = 3;              // 自己总排名
  repeated cl.RankValue values = 4;  // 排行榜 value-战场等级 param1-排名或分数 param2-时间
}

// 排行榜 - 荣耀殿堂
message L2C_WrestleLastRank {}
message C2L_WrestleLastRank {
  uint32 ret = 1;
  cr.WrestleLastSeasonTop top50 = 2;  // top50数据
}

// 荣耀殿堂点赞
message L2C_WrestleLike {
  uint64 uid = 1;
}
message C2L_WrestleLike {
  uint32 ret = 1;
  uint64 uid = 2;                   // 被点赞者uid
  repeated uint32 liked_count = 3;  // top3被点赞次数
}

// 排行榜(本赛季或上赛季) - 第一名
message L2C_WrestleRankFirst {
  uint64 client_msg_id = 1;
}
message C2L_WrestleRankFirst {
  uint32 ret = 1;
  uint64 client_msg_id = 2;
  cl.RankValue value = 3;  // 排行数据 value-战场等级 param1-排名或分数 param2-时间
}

// 测试协议
message L2C_WrestleTest {
  uint32 msgid = 1;
}
message C2L_WrestleTest {
  uint32 ret = 1;
  uint32 msgid = 2;
}

// 更换房间
message L2C_WrestleChangeRoom {}
message C2L_WrestleChangeRoom {
  uint32 ret = 1;
  cr.Fighter fighter = 2;             // 最新数据
  repeated cr.Fighter defenders = 3;  // 对手信息
  bool success = 4;                   // 是否找到可更换的房间
}

// 房间数据更新
message C2L_WrestleRoomUpdate {
  repeated uint64 uids = 1;  // 玩家id列表
}

message L2C_WrestleSetFighterLevelForGm {
  repeated uint64 uids = 1;  // 玩家IDs
  uint32 level = 2;          // 赛场等级
}

// logic之间的透传消息
enum REMOTE_LOGIC {
  RL_NONE = 0;
  VIEW_FORMATION_REQ = 1;
  VIEW_FORMATION_RSP = 2;
  VIEW_USER_REQ = 3;
  VIEW_USER_RSP = 4;
  VIEW_GUILD_MEMBER_REQ = 5;
  VIEW_GUILD_MEMBER_RSP = 6;
  FRIEND_ADD_REQ = 7;
  FRIEND_ADD_RSP = 8;
  FRIEND_CONFIRM_REQ = 9;
  FRIEND_CONFIRM_RSP = 10;
  FRIEND_DELETE_REQ = 11;
  FRIEND_DELETE_RSP = 12;
  FRIEND_SEND_LIKE_REQ = 13;
  FRIEND_SEND_LIKE_RSP = 14;
  FRIEND_RECOMMEND_REQ = 15;
  FRIEND_RECOMMEND_RSP = 16;
  FRIEND_INFO_REQ = 17;
  FRIEND_INFO_RSP = 18;
  MUTE_ACCOUNT_REQ = 19;
  MUTE_ACCOUNT_RSP = 20;
}

message ViewFormationReq {
  uint64 source_uid = 1;  // 发起操作的玩家
  uint32 fid = 2;         // 阵容id
  uint64 target_uid = 3;  // 被查看的玩家ID
  uint64 client_msg_id = 4;
  bool only_power = 5;
}

message ViewFormationRsp {
  uint32 ret = 1;
  ViewFormationReq req = 2;
  db.UserBattleData data = 3;
}

message ViewUserReq {
  uint64 source_uid = 1;            // 发起操作的玩家
  repeated uint64 target_uids = 2;  // 被查看的玩家
  uint32 fid = 3;                   // 传对应的阵容ID，UserSnapshot的defense_power字段会传对应的防守战力
  uint64 client_msg_id = 4;
  repeated string target_names = 5;
}

message ViewUserRsp {
  uint32 ret = 1;
  ViewUserReq req = 2;
  repeated cl.UserSnapshot snapshots = 3;
}

message ViewGuildMemberReq {
  uint64 source_uid = 1;            // 发起操作的玩家
  repeated uint64 target_uids = 2;  // 被查看的玩家
  uint32 fid = 3;                   // 传对应的阵容ID，UserSnapshot的defense_power字段会传对应的防守战力
  uint64 gid = 4;                   // 公会ID
  uint64 client_msg_id = 5;
}

message ViewGuildMemberRsp {
  uint32 ret = 1;
  ViewGuildMemberReq req = 2;
  repeated cl.GuildMemberInfo members = 3;
}

message FriendAddReq {
  uint64 source_uid = 1;  // 发起操作的玩家
  map<uint64, db.Friend> friends = 2;
  repeated uint64 target_uids = 3;
  uint64 client_msg_id = 4;
}

message FriendAddRsp {
  uint32 ret = 1;
  FriendAddReq req = 2;
  repeated cl.ServerUser success_users = 3;  // 成功的uid列表
}

message FriendConfirmReq {
  uint64 source_uid = 1;  // 发起操作的玩家
  map<uint64, db.Friend> friends = 2;
  repeated uint64 target_uids = 3;
  bool accept = 4;  // true-同意接受
  uint64 client_msg_id = 5;
  cl.UserSnapshot source_snapshot = 6;
}

message FriendConfirmRsp {
  uint32 ret = 1;
  FriendConfirmReq req = 2;
  repeated uint64 del_ids = 3;                 // 需要从申请列表里删除的id
  repeated cl.UserSnapshot success_users = 4;  // 成功的玩家Snapshot
}

message FriendDeleteReq {
  uint64 source_uid = 1;  // 发起操作的玩家
  uint64 target_uid = 2;
  uint64 client_msg_id = 3;
}

message FriendDeleteRsp {
  uint32 ret = 1;
  FriendDeleteReq req = 2;
}

message FriendSendLikeReq {
  uint64 source_uid = 1;  // 发起操作的玩家
  repeated uint64 target_uids = 2;
  uint64 client_msg_id = 3;
}

message FriendSendLikeRsp {
  uint32 ret = 1;
  FriendSendLikeReq req = 2;
}

message FriendRecommendReq {
  uint64 source_uid = 1;                // 发起操作的玩家
  map<uint64, int64> excluded_ids = 2;  // 发起方 需要排除的ID列表（包括已经推荐过的和黑名单）
  uint64 client_msg_id = 3;
  uint64 uid = 4;
  uint32 level = 5;
}

message FriendRecommendRsp {
  uint32 ret = 1;  //
  FriendRecommendReq req = 2;
  repeated cl.UserSnapshot users = 3;
}

message FriendInfoReq {
  uint64 source_uid = 1;            // 发起操作的玩家
  repeated uint64 target_uids = 2;  // 被查看的玩家
  uint32 fid = 3;                   // 传对应的阵容ID，UserSnapshot的defense_power字段会传对应的防守战力
  uint64 client_msg_id = 4;
}

message FriendInfoRsp {
  uint32 ret = 1;  //
  FriendInfoReq req = 2;
  repeated cl.UserSnapshot users = 3;
  repeated uint64 del_ids = 4;  // 返回需要删除的单边好友
}

message MuteAccountReq {
  uint64 source_uid = 1;
  uint64 client_msg_id = 2;
  uint64 mute_uid = 3;
  uint32 mute_day = 4;
}

message MuteAccountRsp {
  uint32 ret = 1;
  MuteAccountReq req = 2;
}

/************************  公会 start ************************/

message L2C_GuildCreate {
  string name = 1;
  uint32 badge = 2;
  string language = 3;
  uint32 join_type = 4;    // 0:自动加入 1：需要审核 2：拒绝加入
  uint32 lv_limit = 5;     // 等级限制
  int64 power_limit = 6;   // 战力限制
  uint32 label = 7;        //
  string declaration = 8;  //
  uint64 new_guild_id = 9;
  string user_name = 10;  // 玩家的名字
}

message C2L_GuildCreate {
  uint32 ret = 1;
  string name = 2;
  uint32 badge = 3;
  string language = 4;
  uint32 join_type = 5;   // 0:自动加入 1：需要审核 2：拒绝加入
  uint32 lv_limit = 6;    // 等级限制
  int64 power_limit = 7;  // 战力限制
  uint32 label = 8;       //
  string declaration = 9;
  cl.GuildInfo guild = 10;
  db.LogicGuild logic_guild = 11;
  log.GuildLogText log_info = 12;
}

message L2C_GuildList {
  uint32 page = 1;
  bool screen = 2;                  // 是否筛选
  string language = 3;              //
  uint32 join_type = 4;             //
  uint32 lv_limit = 5;              // 等级限制
  int64 power_limit = 6;            // 战力限制
  uint32 label = 7;                 // 0 代表标签不做为筛选条件
  uint32 lv_min = 8;                // 筛选公会等级最低值
  uint32 lv_max = 9;                // 筛选公会等级最高值
  uint32 member_min = 10;           // 公会人数最少值
  uint32 member_max = 11;           // 公会人数最大值
  uint32 division = 12;             // 段位
  bool transfer = 13;               // 是否转会推荐
  uint32 activity_member_min = 14;  // 活跃成员最小值
  uint32 activity_member_max = 15;  // 活跃成员最大值
  int64 user_power = 16;
  uint32 user_lv = 17;
  repeated cl.GuildLeaveCount leave_cnt = 18;  // 离开公会次数
  int64 leave_tm = 19;                         // 上次离开公会时间
  bool combine = 20;                           // 是否合并推荐列表
}

message C2L_GuildList {
  uint32 ret = 1;
  uint32 page = 2;
  bool screen = 3;         // 是否筛选
  string language = 4;     //
  uint32 join_type = 5;    //
  uint32 lv_limit = 6;     // 等级限制
  int64 power_limit = 7;   // 战力限制
  uint32 label = 8;        // 0 代表标签不做为筛选条件
  uint32 lv_min = 9;       // 筛选公会等级最低值
  uint32 lv_max = 10;      // 筛选公会等级最高值
  uint32 member_min = 11;  // 公会人数最少值
  uint32 member_max = 12;  // 公会人数最大值
  uint32 total = 13;       // 公会总数
  repeated cl.GuildSnapshot guilds = 14;
  uint32 division = 15;
  bool transfer = 16;                                  // 是否转会推荐
  uint32 activity_member_min = 17;                     // 活跃成员最小值
  uint32 activity_member_max = 18;                     // 活跃成员最大值
  bool combine = 19;                                   // 是否合并推荐列表
  repeated cl.GuildCombineStatus combine_status = 20;  // 合并请求状态
}

message L2C_GuildInfo {
  uint64 gid = 1;
  cr.GuildMember self = 2;  // 自己的member信息，用来更新跨服的数据
  int64 power = 3;          // 成员战力
  uint32 level = 4;
  repeated cl.GuildLeaveCount leave_cnt = 5;  // 离开公会次数
  int64 leave_tm = 6;
}

message C2L_GuildInfo {
  uint32 ret = 1;
  cl.GuildInfo guild = 2;
  map<uint64, cr.GuildMembers> members = 3;  // 成员  key：sid  value:成员ID
  db.LogicGuild logic_guild = 4;
  cr.GuildMember myself = 5;
  uint32 dungeon_season = 6;
  bool have_transfer = 7;  // 有转会的推荐公会
  bool have_combine = 8;   // 有合并的推荐公会
}

message L2C_GuildLogList {}

message C2L_GuildLogList {
  uint32 ret = 1;
  repeated cl.GuildLogInfo logs = 2;
  repeated cl.GuildDonateLogInfo donate_logs = 3;
}

message L2C_GuildManagerMember {
  uint64 id = 1;
  uint32 type = 2;  // 0-设为会长 1-更改副会长(设为副会长、若已经是副会长则撤销)
  // 2-踢出公会
}

message C2L_GuildManagerMember {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 type = 3;
  uint64 leader = 4;
  repeated uint64 deputy = 5;
  uint32 member_old_grade = 6;
  uint32 member_current_grade = 7;
  uint64 gid = 8;  // 公会ID
  uint64 member_current_gid = 9;
  log.GuildManage log_info = 10;
  repeated uint64 regiment = 11;
}

message L2C_GuildModifyInfo {
  uint32 badge = 1;
  uint32 join_type = 2;
  string declaration = 3;
  string language = 4;
  uint32 lv_limit = 5;    // 等级限制
  int64 power_limit = 6;  // 战力限制
  uint32 label = 7;       // 标签
}

message C2L_GuildModifyInfo {
  uint32 ret = 1;
  uint32 badge = 2;
  uint32 join_type = 3;
  string declaration = 4;
  string language = 5;
  uint32 lv_limit = 6;    // 等级限制
  int64 power_limit = 7;  // 战力限制
  uint32 label = 8;       // 标签
  log.GuildLogText log_info = 9;
}

message L2C_GuildSetName {
  string name = 1;
}

message C2L_GuildSetName {
  uint32 ret = 1;
  string name = 2;
  int64 update_name_tm = 3;  // 更新公会名的时间
  log.GuildLogText log_info = 4;
}

message L2C_GuildModifyNotice {
  string notice = 1;
}

message C2L_GuildModifyNotice {
  uint32 ret = 1;
  string notice = 2;
  uint64 notice_id = 3;
  log.GuildLogText log_info = 4;
}

message L2C_GuildApplyList {
  uint32 apply_type = 1;  // 申请类型 common.GUILD_APPLY_TYPE
}

message C2L_GuildApplyList {
  uint32 ret = 1;
  uint32 apply_type = 2;                               // 申请类型 common.GUILD_APPLY_TYPE
  repeated cr.GuildApplyInfo list = 3;                 // 返回ID,logic服去找对应的userSnapshot
  repeated cl.GuildCombineApplyItem apply_list = 4;    // 我方公会已请求列表，根据logic数据：我方公会已申请列表 + 我方公会已邀请列表，cross进行包装
  repeated cl.GuildCombineApplyItem applied_list = 5;  // 我方公会被其他公会请求的列表，从cross读取数据
}

message L2C_GuildApplyRatify {
  repeated uint64 id = 1;
  bool accept = 2;  // true:同意申请  false:拒绝申请
}

message C2L_GuildApplyRatify {
  uint32 ret = 1;
  repeated uint64 id = 2;
  bool accept = 3;
  repeated uint64 delete = 4;                      // 需要从审核列表中去掉的Id
  repeated cr.GuildApplyInfo old_apply_infos = 5;  // 旧的申请列表
  repeated cr.GuildApplyInfo new_apply_infos = 6;  // 新的申请列表
  log.GuildManage log_info = 7;
}
// 审核成功后，处理多个玩家的公会状态？？？？   直接推送处理？

message L2C_GuildCombineApply {
  uint64 source_gid = 1;  // 发起方公会id
  uint64 target_gid = 2;  // 目标方公会id
  uint32 type = 3;        // 1-申请合并 2-邀请被合并
}

message C2L_GuildCombineApply {
  uint32 ret = 1;
  uint64 source_gid = 2;
  uint64 target_gid = 3;
  uint32 type = 4;
  repeated cl.GuildCombineApplyItem source_apply_list = 5;    // 发起方公会已请求列表
  repeated cl.GuildCombineApplyItem source_applied_list = 6;  // 发起方公会收到的请求列表
}

message L2C_GuildCombineCheck {
  uint64 source_gid = 1;  // 发起方公会id
  uint64 target_gid = 2;  // 目标方公会id
  uint32 apply_type = 3;  // common.GUILD_COMBINE_APPLY_TYPE
}

message C2L_GuildCombineCheck {
  uint32 ret = 1;
  uint64 source_gid = 2;
  uint64 target_gid = 3;
  uint32 apply_type = 4;
  repeated cr.GuildMember join_users = 5;     // 加入的玩家
  repeated cr.GuildMember dismiss_users = 6;  // 遣散的玩家
}

message L2C_GuildCombineRatify {
  repeated uint64 gids = 1;
  uint32 apply_type = 2;  // common.GUILD_COMBINE_APPLY_TYPE
  bool accept = 3;        // true:同意申请  false:拒绝申请
}

message C2L_GuildCombineRatify {
  uint32 ret = 1;
  repeated uint64 gids = 2;
  uint32 apply_type = 3;                               // common.GUILD_COMBINE_APPLY_TYPE
  bool accept = 4;                                     // true:同意申请  false:拒绝申请
  repeated cl.GuildCombineApplyItem apply_list = 5;    // 我方公会发起请求的列表
  repeated cl.GuildCombineApplyItem applied_list = 6;  // 我方公会被其他公会请求的列表
  log.GuildManage log_info = 7;
}

message L2C_GuildUserApply {
  uint64 id = 1;
  uint32 type = 2;  // 0-加入公会 1-申请公会 2-取消申请 3-快速加入
  string name = 3;  // 名字，如果加入公会需要用到
  repeated uint64 old_apply_ids = 4;
  uint32 level = 5;                           // 如果加入公会需要用到
  int64 power = 6;                            // 如果加入公会需要用到
  string language = 7;                        // 语言，快速加入使用
  repeated cl.GuildLeaveCount leave_cnt = 8;  // 离开公会次数
  int64 leave_tm = 9;                         // 上次离开公会时间
}

message C2L_GuildUserApply {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 type = 3;
  repeated uint64 old_apply_ids = 4;
  repeated uint64 apply_ids = 5;
  db.LogicGuild guild = 6;
  repeated cl.GuildLeaveCount leave_cnt = 7;  // 离开公会次数
  int64 leave_tm = 8;                         // 上次离开公会时间
  uint64 old_gid = 9;                         // 离开的旧公会
}

message L2C_GuildSendMail {
  string title = 1;
  string content = 2;
  string name = 3;
}

message C2L_GuildSendMail {
  uint32 ret = 1;
  string title = 2;
  string content = 3;
}

message L2C_GuildQuit {
  string name = 1;
}

message C2L_GuildQuit {
  uint32 ret = 1;
  uint64 old_gid = 2;
}

message L2C_GuildDisband {
  string name = 1;
}

message C2L_GuildDisband {
  uint32 ret = 1;
  log.GuildManage log_info = 2;
}

message L2C_GuildSearch {
  uint64 id = 1;
  string name = 2;
  bool is_combine = 3;
}

message C2L_GuildSearch {
  uint32 ret = 1;
  uint64 id = 2;
  string name = 3;
  cl.GuildSnapshot guild = 4;
  bool is_combine = 5;
  repeated cl.GuildCombineStatus combine_status = 6;
}

message L2C_GuildGetMembers {
  uint64 id = 1;
}

message C2L_GuildGetMembers {
  uint32 ret = 1;
  uint64 id = 2;
  map<uint64, cr.GuildMembers> members = 3;  // 成员  key：sid  value:成员ID
}

message L2C_GuildGetDeclaration {
  uint64 id = 1;
}

message C2L_GuildGetDeclaration {
  uint32 ret = 1;
  string declaration = 2;
}

message C2L_GuildNotify {
  uint32 ret = 1;
  uint64 id = 2;             // 公会id
  bool be_leader = 3;        // 成为会长
  bool be_deputy = 4;        // 成为副会长
  bool kicked = 5;           // 被踢
  bool approve = 6;          // 申请公会通过
  bool recall = 7;           // 被罢免职位
  bool new_apply = 8;        // 有新申请，推送给公会管理者
  repeated uint64 uids = 9;  // 推送的玩家
  bool refuse = 10;          // 申请被拒
  db.LogicGuild guild = 11;
  string approver = 12;            // 同意加入公会的管理员的名字
  bool apply_clear = 13;           // 申请列表清空
  bool join_in = 14;               // 加入公会   同步logic消息，防止玩家离线后消息处理不到
  bool quit = 15;                  // 退出公会   同步logic消息，防止玩家离线后消息处理不到
  bool disband = 16;               // 解散公会   同步logic消息，防止玩家离线后消息处理不到
  bool quick_join = 17;            // 快速加入，用来日志区分
  int64 create_tm = 18;            // 日志用
  uint64 manager_id = 19;          // 管理员ID
  uint32 grade = 20;               // 管理员职位
  bool be_regiment = 21;           // 成为团长
  bool combine_join = 22;          // 由于公会合并加入新公会
  bool combine_kicked = 23;        // 由于公会合并被踢
  bool combine_apply = 24;         // 公会合并请求
  int64 combine_apply_time = 25;   // 公会合并请求时间
  bool cancel_combine_apply = 26;  // 取消公会合并请求
  bool reject_combine_apply = 27;  // 拒绝公会合并请求
  bool combine_disband = 28;       // 由于合并解散公会
}

message C2L_GuildUpdateInfo {
  uint32 ret = 1;
  uint64 gid = 2;               // 公会ID
  string name = 3;              // 公会最新名字
  uint32 level = 4;             // 公会最新等级
  uint32 exp = 5;               // 最新经验
  uint32 donate_point_num = 6;  // 今日公会捐献进度值
}

message L2C_GuildDonate {
  uint32 id = 1;
}

message C2L_GuildDonate {
  uint32 ret = 1;
  uint32 id = 2;                 // 捐赠表ID
  uint32 level = 3;              // 最新公会等级
  uint32 exp = 4;                // 最新公会经验
  uint32 donate_point_num = 5;   // 最新公会进度值
  uint32 donate_person_num = 6;  // 今日公会捐赠人数
  uint32 old_level = 7;          // 旧的公会等级
  uint32 old_exp = 8;            // 旧的公会经验
  uint32 guild_activity = 9;     // 公会的活跃度
}

message L2C_GuildGetDonateAward {
  repeated uint32 ids = 1;  // 领奖ID
}

message C2L_GuildGetDonateAward {
  uint32 ret = 1;
  repeated uint32 ids = 2;  // 领奖ID
}

message L2C_GuildRank {
  uint32 rank_id = 1;
  bool first = 2;
  uint64 client_msg_id = 3;
}

message C2L_GuildRank {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated cl.RankValue list = 3;
  uint32 self_rank = 4;
  cl.RankValue self_value = 5;
  bool first = 6;
  uint64 client_msg_id = 7;
}

message L2C_GuildGetLogicGuilds {
  uint64 sid = 1;
}

message C2L_GuildGetLogicGuilds {
  uint32 ret = 1;
  uint64 sid = 2;
  map<uint64, db.LogicGuild> logicGuilds = 3;
  repeated cl.GuildDungeonHallOfFameInfo last_season_top_3 = 4;
  uint32 last_season_id = 5;  // 上赛季ID
}

message C2L_GuildMail {
  uint32 ret = 1;
  uint32 mail_id = 2;
  repeated string params = 3;
  repeated cl.Resource awards = 4;
  repeated uint64 users = 5;
}

message L2C_GuildDonateLogList {}

message C2L_GuildDonateLogList {
  uint32 ret = 1;
  repeated cl.GuildDonateLogInfo logs = 2;
}

message L2C_GuildListGetDetail {
  uint64 gid = 1;
  uint64 sid = 2;
  uint64 uid = 3;
}

message C2L_GuildListGetDetail {
  uint32 ret = 1;
  uint64 gid = 2;
  string declaration = 3;
  map<uint64, cr.GuildMembers> members = 4;  // 成员  key：sid  value:成员ID
  cl.GuildSnapshot snapshot = 5;
}

message C2C_GuildListGetDetail {
  uint64 sid = 1;
  uint64 uid = 2;
  bytes data = 3;
}

message L2C_GuildGetBadgeList {}

message C2L_GuildGetBadgeList {
  uint32 ret = 1;
  repeated cl.Avatar badge_list = 2;
}

message L2C_GuildGetDivisionAwardInfo {}

message C2L_GuildGetDivisionAwardInfo {
  uint32 ret = 1;
  uint32 current_division = 2;
  uint32 season_top_division = 3;
  uint32 division_rank = 4;
  uint32 chapter = 5;   // 所在章节
  bool is_through = 6;  // 该章节是否通关
}

// 向公会官员，同步当日踢人次数
message C2L_GuildSyncKickCount {
  uint32 ret = 1;
  uint32 kick_count = 2;     // 当日踢人次数
  repeated uint64 uids = 3;  // 待推送的玩家id
}

message C2L_GuildDeleteMails {
  uint32 ret = 1;
  repeated uint64 uids = 2;  //
}

//  >>>>>>>>>>>>for gm
message L2C_GuildSetLevelForGM {
  uint64 gid = 1;
  uint32 level = 2;
  uint32 type = 3;  // 0:公会等级   1：段位
}

message L2C_GuildJoinOrCreateForGM {
  string name = 1;
  uint32 level = 2;
  int64 power = 3;
  uint64 new_gid = 4;      // 如果创建的话使用
  uint32 guild_level = 5;  // 设置公会等级
}

message L2C_GuildGetMedals {
  cl.C2L_GuildGetMedals req = 1;
}

message C2L_GuildGetMedals {
  uint32 ret = 1;
  cl.L2C_GuildGetMedals resp = 2;
}

message L2C_GuildMedalLike {
  cl.C2L_GuildMedalLike req = 1;
  string name = 2;
}

message C2L_GuildMedalLike {
  uint32 ret = 1;
  cl.L2C_GuildMedalLike resp = 2;
}

message L2C_GuildUserSyncTowerSeason {
  uint32 floor = 1;
  int64 tm = 2;
}

message L2C_GuildSyncLogicGuild {
  uint64 gid = 1;
}

message C2L_GuildSyncLogicGuild {
  uint32 ret = 1;
  db.LogicGuild guild = 2;
}

message L2C_GuildSyncSeasonArenaData {
  repeated cl.GuildMedalSeasonArenaBak data = 1;
}

message C2L_GuildSyncSeasonArenaData {
  uint32 ret = 1;
}

message L2C_GuildSendRecruitMsg {
  uint64 guild_id = 1;
}

message C2L_GuildSendRecruitMsg {
  uint32 ret = 1;
  uint64 guild_id = 2;
  string name = 3;
  bool is_apply = 4;
  uint32 level = 5;
  uint32 lv_limit = 6;
  int64 power_limit = 7;
}

message L2C_GuildUserSyncLeaveCd {
  uint64 user_id = 1;
  int64 last_leave_tm = 2;                          // 上次离开公会的时间
  repeated cl.GuildLeaveCount guild_leave_cnt = 3;  // 离开公会次数
}

// 公会合并
message L2C_GuildCombine {
  uint64 guild_id = 1;          // 公会id
  uint64 disband_guild_id = 2;  // 被合并需要解散的公会id
}

message C2L_GuildCombine {
  uint32 ret = 1;
  uint64 guild_id = 2;          // 公会id
  uint64 disband_guild_id = 3;  // 被合并需要解散的公会id
}

/************************  公会 end ************************/

/************************  公会副本 star ************************/

message L2C_GuildDungeonInfo {
  bool sign_up = 1;  // 报名
}

message C2L_GuildDungeonInfo {
  uint32 ret = 1;
  uint32 current_chapter = 2;
  bool is_through = 3;  // 当前章节是否通关
  repeated cl.GuildDungeonRoomRankInfo rank_info = 4;
  uint32 season_top_division = 5;
  uint32 current_division = 6;
  uint32 last_week_damage_rank = 7;       // 玩家上周伤害排名
  repeated uint32 no_box_chapters = 8;    // 箱子都领完的章节ID
  uint32 current_season_id = 9;           // 当前赛季ID
  uint32 division_before_reset = 10;      // 上次结算前段位
  bool sign_up = 11;                      // 报名
  bool sign_success = 12;                 // 之后报名成功会返回true，已经报名了，返回false
  uint32 division_after_reset = 13;       // 上次结算后段位
  uint32 last_reset_room_rank = 14;       // 上次结算时的房间内排名
  uint32 last_season_division_rank = 15;  // 上赛季段位排名
  bool first_week = 16;                   // 该战区第一周
  uint64 chat_room_id = 17;               // 副本聊天房间ID
  uint32 last_reset_add_point = 18;       // 上次结算增加的积分
  uint32 partition = 19;                  // 战区
  uint32 room_division = 20;              // 房间段位
  bool jump_chapter = 21;                 // 是否跳章
}

message L2C_GuildDungeonChapterInfo {
  uint32 chapter = 1;
  bool enter = 2;  // 是否进入章节
}

message C2L_GuildDungeonChapterInfo {
  uint32 ret = 1;
  uint32 chapter = 2;
  bool is_through = 3;  // 此章节是否通关
  bool enter = 4;       // 是否进入章节
  repeated cl.GuildDungeonBossInfo bosses = 5;
  repeated cl.GuildDungeonBossBoxInfo boxes = 6;
  repeated cl.GuildDungeonRoomRankInfo rank_info = 7;
  repeated uint32 focus_ids = 8;  // 集火ID
  uint32 marked_num = 9;          //  副本被标记为可加进度的成员数量
  bool marked = 10;               // 自己是否被标记
}

message L2C_GuildDungeonFightStart {
  uint32 chapter = 1;
  uint32 boss = 2;
  bool sweep = 3;
}

message C2L_GuildDungeonFightStart {
  uint32 ret = 1;
  uint32 chapter = 2;
  uint32 boss = 3;
  bool sweep = 4;
  cr.GuildDungeonChapter chapter_data = 5;  // 章节信息，logic计算战斗buff用
  bool is_strengthen = 6;                   // 该boss是否被强化了，是的话，需要重新打，不能扫荡
}

message L2C_GuildDungeonFightFinish {
  uint32 chapter = 1;
  uint32 boss = 2;
  bool sweep = 3;
  uint64 this_damage = 4;  // 玩家本次造成伤害
  uint32 reduce_hp_pct = 5;
  string report_id = 6;               // 战报id
  uint32 strategy_reduce_hp_pct = 7;  // 秘技效果减少的Boss血条
}

message C2L_GuildDungeonFightFinish {
  uint32 ret = 1;
  uint32 chapter = 2;
  uint32 boss = 3;
  bool sweep = 4;
  uint64 this_damage = 5;  // 玩家本次造成伤害
  uint32 reduce_hp_pct = 6;
  repeated cl.GuildDungeonBossInfo bosses = 7;
  repeated cl.GuildDungeonBossBoxInfo boxes = 8;
  repeated cl.GuildDungeonRoomRankInfo rank_info = 9;
  repeated uint32 reset_boss_ids = 10;       // 重置玩家的boss伤害
  string report_id = 11;                     // 战报id
  map<uint64, uint32> member_old_rank = 12;  // 打之前房间内成员的排名
  bool chapter_through = 13;                 // 本次是否通过章节
  uint32 season_id = 14;                     // 赛季ID
  uint64 weekly_damage = 15;                 // 周伤害
  uint32 strategy_reduce_hp_pct = 16;        // 秘技效果减少的Boss血条
  uint32 marked_num = 17;                    //  副本被标记为可加进度的成员数量
  bool marked = 18;                          // 自己是否被标记
}

message L2C_GuildDungeonRecvBox {
  uint32 chapter = 1;
  uint32 box = 2;
}

message C2L_GuildDungeonRecvBox {
  uint32 ret = 1;
  uint32 chapter = 2;
  uint32 box = 3;
  uint32 award_id = 4;
  repeated cl.GuildDungeonBossBoxInfo boxes = 5;
}

message L2C_GuildDungeonSetFocus {
  uint32 chapter = 1;
  repeated uint32 focus_ids = 2;
}

message C2L_GuildDungeonSetFocus {
  uint32 ret = 1;
  uint32 chapter = 2;
  repeated uint32 focus_ids = 3;
}

message C2L_GuildDungeonNotify {
  uint32 ret = 1;
  uint32 chapter = 2;
  bool is_through = 3;                   // 该章节是否通关
  repeated uint32 reset_damage_ids = 4;  // 需要清除成员伤害的bossID
  uint64 gid = 6;
  uint32 current_division = 7;         // 当前段位
  uint32 season_top_division = 8;      // 赛季最高段位
  GUILD_DUNGEON_NOTIFY_TYPE type = 9;  // 推送类型
  uint32 strategy_id = 10;             // 秘技ID
  uint64 use_strategy_member = 11;     // 使用秘技的玩家ID
  uint32 use_count = 12;               // 使用次数
}

enum GUILD_DUNGEON_NOTIFY_TYPE {
  GDNT_NONE = 0;
  GDNT_RESET_BOSS_DAMAGE = 1;
  GDNT_CHAPTER_CHANGE = 2;
  GDNT_USE_STRATEGY = 3;
  GDNT_MAX = 4;
}

message C2L_GuildDungeonNotifyMsg {
  uint32 ret = 1;
  repeated uint64 gids_for_msg = 2;  //
  uint32 season_id = 3;
}

message L2C_GuildDungeonFightLogList {}

message C2L_GuildDungeonFightLogList {
  uint32 ret = 1;
  repeated cl.GuildDungeonLog list = 2;
}

message L2C_GuildDungeonAward {
  uint64 sid = 1;  // 服务器id
}

message C2L_GuildDungeonAward {
  uint32 ret = 1;
  uint64 sid = 2;  // 服务器id
  repeated GuildDungeonAwardBakData ranks = 3;
  int64 time = 4;                                                // 结算开始时间
  bool is_season = 5;                                            // 是否为赛季结算
  repeated cl.GuildDungeonHallOfFameInfo last_season_top_3 = 6;  // 上赛季top3
  uint32 last_season_id = 7;                                     // 上赛季ID
}

message GuildDungeonAwardBakData {
  uint64 gid = 1;
  uint32 division = 2;
  map<uint64, uint32> member_damage_rank = 3;  // key:uid  value: 排名
  uint64 leader = 4;                           // 会长ID
  repeated uint64 deputy = 5;                  // 副会长IDs
  uint32 season_top_division = 6;              // 结算时的赛季最高段位
  uint32 first_large_division = 7;             // 结算首次达到的大段
  uint32 season_division_rank = 8;             // 赛季段位排名
}

message L2C_GuildDungeonGetMembersWeeklyDamage {}

message C2L_GuildDungeonGetMembersWeeklyDamage {
  uint32 ret = 1;
  map<uint64, cr.GuildMembers> members = 2;  // 成员  key：sid  value:成员ID
}

message L2C_GuildDungeonGetHallOfFame {
  uint32 season_id = 1;
}

message C2L_GuildDungeonGetHallOfFame {
  uint32 ret = 1;
  uint32 season_id = 2;
  repeated cl.GuildDungeonHallOfFameInfo list = 3;
  repeated uint32 season_ids = 4;  // 拥有数据的赛季ID
}

message L2C_GuildDungeonGetStrategy {}

message C2L_GuildDungeonGetStrategy {
  uint32 ret = 1;
  repeated cl.StrategyInfo strategy = 2;  // 秘技信息
  repeated cl.GuildDungeonRoomRankInfo rank_info = 3;
}

message L2C_GuildDungeonUseStrategy {
  uint32 strategy_id = 1;
  uint64 gid = 2;
  uint32 use_count = 3;
}

message C2L_GuildDungeonUseStrategy {
  uint32 ret = 1;
  uint32 strategy_id = 2;
  uint64 gid = 3;
  uint32 use_count = 4;
  repeated cl.StrategyInfo strategy = 5;  // 秘技信息
  uint32 be_use_guild_chapter = 6;        // 被使用公会的章节
  string guild_name = 7;                  // 被使用秘技的公会名称
}

message L2C_GuildDungeonSetChapterForGm {
  uint64 gid = 1;
  uint32 chapter = 2;
}

message GuildDungeonRecvData {
  uint32 chapter = 1;
  uint32 box = 2;
  uint32 award_id = 3;
}

message L2C_GuildDungeonRecvAllBox {
  repeated uint32 op_chapters = 1;
}

message C2L_GuildDungeonRecvAllBox {
  uint32 ret = 1;
  repeated GuildDungeonRecvData datas = 2;
}

message L2C_GuildDungeonGetMembersFightInfo {}

message C2L_GuildDungeonGetMembersFightInfo {
  uint32 ret = 1;
  repeated cl.GuildDungeonMemberFightInfo infos = 2;
}

message L2C_GuildDungeonBuyChallengeTimes {  // 同步跨服购买次数
  uint32 but_count = 1;
}

/************************  公会副本 end ************************/
message L2C_RankUpdate {
  uint32 rank_id = 1;
  uint64 reset_time = 2;
  bytes data = 3;  // 更新的数据
}

message C2L_RankUpdate {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 reset_time = 3;
  uint32 new_rank = 4;
}

message L2C_RankGetList {
  uint32 rank_id = 1;
  uint64 reset_time = 2;
  uint64 self_id = 3;
  uint32 begin_rank = 4;
  uint32 end_rank = 5;
  bool get_last = 6;       // 是否获取备份数据,true代表获取
  uint32 client_type = 7;  // 透传字段
}

message C2L_RankGetList {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 reset_time = 3;
  repeated cl.RankValue datas = 4;  //
  uint32 self_rank = 5;             // 自己的排行
  cl.RankValue self_data = 6;       // 自己的数据
  uint32 total = 7;                 // 总的数量
  bool get_last = 8;
  uint32 client_type = 9;  // 透传字段
}

message L2C_RankDelete {
  uint32 rank_id = 1;
  uint64 reset_time = 2;
  uint64 del_key = 3;
}

message L2C_RankGetFirst {
  uint32 rank_id = 1;
  uint64 client_msg_id = 2;
  uint64 reset_time = 3;
}

message C2L_RankGetFirst {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 client_msg_id = 3;
  uint64 reset_time = 4;
  cl.RankValue data = 5;  //
}

message L2C_RankLike {
  uint32 rank_id = 1;
  uint64 id = 2;
  uint64 reset_time = 3;
  bool last = 4;  // 是否点赞备份数据
}

message C2L_RankLike {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 id = 3;
  uint32 like_num = 4;
  bool last = 5;  // 是否点赞备份数据
}

message L2C_RankGetForReset {
  uint32 rank_id = 1;
  uint64 reset_time = 2;
  bool last = 3;  // 是否备份数据
  uint32 begin_rank = 4;
  uint32 end_rank = 5;
}

message C2L_RankGetForReset {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 reset_time = 3;
  bool last = 4;  // 是否点赞备份数据
  uint32 begin_rank = 5;
  uint32 end_rank = 6;
  repeated cl.RankValue datas = 7;
}

message L2C_GuildChestGetData {}

message C2L_GuildChestGetData {
  uint32 ret = 1;
  repeated cl.GuildChestData guild_chests = 2;
}

message L2C_GuildChestRecv {
  uint64 id = 1;
  uint32 level = 2;
  uint32 avatar = 3;
  string name = 4;
  uint32 recv_count = 5;
}

message C2L_GuildChestRecv {
  uint32 ret = 1;
  uint64 id = 2;
  cl.GuildChest guild_chest = 3;
  repeated cl.Resource awards = 4;
}

message L2C_GuildChestLike {
  uint64 id = 1;
  uint32 like_type = 2;
}

message C2L_GuildChestLike {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 like_type = 3;
  cl.GuildChest guild_chest = 4;
}

message L2C_GuildChestActivate {
  uint64 id = 1;             //  唯一ID
  string name = 2;           //  创建者名字
  uint32 level = 3;          //  创建者等级
  uint32 avatar_id = 4;      //  创建者头像
  uint32 create_flower = 6;  //  创建时创建者的花朵
  uint32 chest_id = 7;       //  物品ID
  uint32 task_id = 8;        //  宝箱的来源任务ID
}

message C2L_GuildChestActivate {
  uint32 ret = 1;
  uint64 id = 2;  //  唯一ID
  cl.GuildChest guild_chest = 3;
  string name = 4;           //  创建者名字
  uint32 level = 5;          //  创建者等级
  uint32 avatar_id = 6;      //  创建者头像
  uint32 create_flower = 7;  //  创建时创建者的花朵
  uint32 chest_id = 8;       //  物品ID
}

message L2C_GuildChestCanActivate {
  uint64 id = 1;
  uint32 chest_id = 2;
  int64 expired_time = 3;
  uint32 task_id = 4;
}

message C2L_GuildChestCanActivate {
  uint32 ret = 1;
  uint64 id = 2;
  uint32 chest_id = 3;
  int64 expired_time = 4;
  uint32 task_id = 5;
}

message GuildChestExpired {
  uint64 uid = 1;
  uint64 id = 2;
  uint32 chest_id = 3;
  uint32 recv_like = 4;
  uint32 recv_count = 5;
  repeated string recv_names = 6;
}

message C2L_GuildChestExpired {
  uint32 ret = 1;
  repeated GuildChestExpired guild_chest_expireds = 2;  // serverID
}

message C2L_GuildChestNewChest {
  uint32 ret = 1;
  uint64 gid = 2;
  cl.GuildChestData guild_chest = 3;
}

message L2C_GuildChestDetail {
  uint64 id = 1;
}

message C2L_GuildChestDetail {
  uint32 ret = 1;
  uint64 id = 2;
  cl.GuildChest guild_chest = 3;
}

// 世界boss - 匹配房间
message L2C_WorldBossMatchRoom {
  uint32 sys_id = 1;           // 活动id
  uint32 level = 2;            // 难度等级
  string player_name = 3;      // 玩家名字
  int64 server_open_time = 4;  // 服务器开服时间
}

message C2L_WorldBossMatchRoom {
  uint32 ret = 1;
  uint32 w_unique_id = 2;     // 世界boss唯一id
  uint32 w_sys_id = 3;        // 世界boss系统id
  uint32 level = 4;           // 难度等级
  uint32 room_unique_id = 5;  // 房间唯一id
  uint32 room_sys_id = 6;     // 房间系统id
}

// 世界boss - 排行榜
message L2C_WorldBossRank {
  uint32 type = 1;        // 1：战区榜 2：房间榜 3: 讨伐之星 4: 玩家自身
  uint32 level = 2;       // 战区榜有值：难度枚举
  uint32 start_rank = 3;  // 战区榜有值；【start_rank，end_rank】这个区间的所有值
  uint32 end_rank = 4;
}

message C2L_WorldBossRank {
  uint32 ret = 1;
  uint32 type = 2;
  uint32 level = 3;
  repeated cl.RankValue list = 4;
  uint32 self_rank = 5;
  cl.RankValue self_value = 6;
  uint32 start_rank = 7;  // 战区榜有值；【start_rank，end_rank】这个区间的所有值
  uint32 end_rank = 8;
}

// 世界boss - 房间日志
message L2C_WorldBossGetRoomLog {
  uint32 num = 1;  // 拉取数量： 0: 都拉取
}
message C2L_WorldBossGetRoomLog {
  uint32 ret = 1;
  uint32 num = 2;
  repeated cl.WorldBossRoomLog logs = 3;
  repeated cl.RankValue list = 4;  // 前一名、玩家自己、后一名
  cl.RankValue room_top = 5;       // 房间第1
  uint32 partition_rank = 6;       // 战区排行
}

// 世界boss - 结算
message L2C_WorldBossSettle {
  uint32 id = 1;  // 活动 id
}
message C2L_WorldBossSettle {
  uint32 ret = 1;
  uint32 id = 2;
  repeated cl.RankValue list = 3;             // 被结算的人员
  repeated cl.RankValue partition_first = 4;  // 不同难度的战区排行榜的第一
}

// 世界boss - 战斗
message L2C_WorldBossSyncFightData {
  uint32 type = 1;
  uint64 hurt = 2;  // 单次伤害
  //  repeated cl.Resource awards = 3;  // 战斗奖励
  string report_id = 4;                 // 战报id
  string player_name = 5;               // 玩家昵称
  uint64 max_hurt = 6;                  // 最大单次伤害
  uint64 accumulative_hurt = 7;         // 累计伤害
  uint64 hurt_score = 8;                // 当前伤害积分
  uint64 max_hurt_score = 9;            // 最大伤害积分
  uint64 accumulative_hurt_score = 10;  // 累计伤害积分

  reserved 3;
  reserved "awards";
}

message C2L_WorldBossSyncFightData {
  uint32 ret = 1;
  uint32 type = 2;
  string report_id = 3;                 // 战报id
  bool is_set_recommend_formation = 4;  // 是否设置推荐阵容
  bool is_send_system_msg = 5;          // 是否发送跑马灯
  uint64 hurt = 6;                      // 当前伤害
  //  repeated cl.Resource awards = 7;  // 战斗奖励
  uint64 max_hurt = 8;                  // 最大单次伤害
  uint64 hurt_score = 9;                // 当前伤害积分
  uint64 max_hurt_score = 10;           // 最大伤害积分
  uint64 accumulative_hurt = 11;        // 累计伤害
  uint64 accumulative_hurt_score = 12;  // 累计伤害积分
  bool is_set_peak_formation = 13;      // 是否设置巅峰竞技场阵容

  reserved 7;
  reserved "awards";
}

message L2C_WorldBossRoomInfo {
  uint64 max_hurt = 1;                 // 最大伤害
  uint64 accumulative_hurt = 2;        // 累计伤害
  uint64 max_hurt_score = 3;           // 最大伤害积分
  uint32 activity_id = 4;              // 世界boss活动id
  uint64 accumulative_hurt_score = 5;  // 累计伤害积分
}

message C2L_WorldBossRoomInfo {
  uint32 ret = 1;
  repeated cl.RankValue list = 2;   // 排行榜：前一名、玩家自己、后一名
  repeated cl.RankValue users = 3;  // 房间中的玩家
  cl.RankValue room_top = 4;        // 房间第1
  repeated cl.WorldBossRoomLog logs = 5;
  uint32 partition_rank = 6;  // 战区排行
}

message L2C_WorldBossGetPeakPlayers {
  uint32 id = 1;  // 世界bossID
}

message WB2Guild_SyncBakData {
  map<uint64, cr.WorldBossUserRankBak> data = 1;
}

message Guild2WB_SyncBakData {
  uint32 ret = 1;
}

message L2C_DisorderLandSyncHurdleLevel {
  uint32 season_id = 1;  // 赛季id
  uint32 type = 2;       // 关卡类型
  uint32 max_level = 3;
}

message C2L_DisorderLandSyncHurdleLevel {
  uint32 ret = 1;
  uint32 season_id = 2;  // 赛季id
  uint32 type = 3;       // 关卡类型
  uint32 max_level = 4;
}

message L2C_DisorderLandRank {
  uint32 season_id = 1;
  uint32 hurdle_type = 2;  // 关卡类型
  uint32 rank_id = 3;
  uint64 client_msg_id = 4;  // asyncMsgId。有值的话，说明是获取头部玩家
}

message C2L_DisorderLandRank {
  uint32 ret = 1;
  uint32 season_id = 2;
  uint32 hurdle_type = 3;  // 关卡类型
  repeated cl.RankValue list = 4;
  uint32 self_rank = 5;
  cl.RankValue self_value = 6;
  uint32 rank_id = 7;
  uint64 client_msg_id = 8;  // asyncMsgId。有值的话，说明是获取头部玩家
}

// 巅峰竞技场选手报名数据
message PeakPlayer {
  uint64 uid = 1;   // 玩家uid
  uint64 sid = 2;   // 服务器id
  uint32 rank = 3;  // 排名
}

// 巅峰竞技场 - 初始数据
message L2C_PeakInitData {
  uint32 ret = 1;
  uint32 id = 2;  // 世界bossID
  repeated PeakPlayer players = 3;
}

// 巅峰竞技场 - 基本信息
message L2C_PeakBaseData {
  uint64 uid = 1;             // 玩家id
  uint32 guess_match_id = 2;  // 玩家当前轮竞猜比赛id
  uint32 round = 3;           // 游戏服轮次参数
}
message C2L_PeakBaseData {
  uint32 ret = 1;
  uint64 uid = 2;  // 玩家id
  cr.Peak peak = 3;
  repeated cl.PeakMatch my_matches = 4;  // 玩家参与的比赛
  cl.PeakMatch guess_match = 5;          // 根据guess_match_id，获取竞猜比赛信息
}

// 巅峰竞技场 - 获取比赛数据
message L2C_PeakGetMatch {
  repeated cl.PeakMatchParam params = 1;
  cl.UserSnapshot snapshot = 2;  // 玩家快照 - 仅参赛选手传
}
message C2L_PeakGetMatch {
  uint32 ret = 1;
  repeated cl.PeakMatchParam params = 2;  // 参数回传
  repeated cl.PeakMatch matches = 3;      // 对战信息。根据参数，返回对应数据
}

// 巅峰竞技场 - 获取竞猜列表数据
message L2C_PeakGuessList {
  repeated uint32 rounds = 1;
}
message C2L_PeakGuessList {
  uint32 ret = 1;
  repeated uint32 rounds = 2;
  repeated cl.PeakMatch matches = 3;  // 对战信息。生成新数据时，根据赛程阶段，生成不同数量的数据
}

// 巅峰竞技场 - 增加竞猜下注次数
message L2C_PeakAddGuessCount {
  uint32 match_id = 1;  // 比赛场次id
  uint32 count = 2;     // 下注次数
  uint64 uid = 3;       // 下注选手id
}
message C2L_PeakAddGuessCount {
  uint32 ret = 1;
  uint32 match_id = 2;  // 比赛场次id
  uint32 count = 3;     // 下注次数
  uint64 uid = 4;       // 下注选手id
  uint32 group = 5;     // 所属组id
  uint32 round = 6;     // 所属轮次id
}

// 巅峰竞技场 - 比赛结果
message L2C_PeakResult {
  uint32 round = 1;  // 请求第x轮的结算数据
}
message C2L_PeakResult {
  uint32 ret = 1;
  uint32 round = 2;                             // 请求第x轮的结算数据
  repeated cl.PeakResult season_ranks = 3;      // 当前赛季，选手排名数据
  repeated cl.PeakResult round_ranks = 4;       // 当前轮次，完赛选手数据
  repeated cl.PeakUserGuess guess_matches = 5;  // 竞猜比赛结果数据
  cl.PeakResult phase_top1 = 6;                 // 小周期结算时的第一名
  cl.PeakResult season_top1 = 7;                // 赛季结算时的第一名
  cl.PeakResult new_top1 = 8;                   // 积分榜top1有变化后的玩家（新晋第一名）
  int64 time = 9;                               // 结算开始时间
  map<uint64, cl.PeakRankScore> scores = 10;    // 当前赛季全部参赛者排名积分
}

// 巅峰竞技场 - 排行榜
message L2C_PeakRankList {
  uint32 type = 1;        // 类型 1-赛季榜 2-名人堂 3-赛季榜
  uint32 start_rank = 2;  // 赛季榜分页，闭区间rank开始值
  uint32 end_rank = 3;    // 赛季榜分页，闭区间rank结束值
}
message C2L_PeakRankList {
  uint32 ret = 1;
  uint32 type = 2;                  // 类型 1-赛季榜 2-名人堂 3-赛季榜
  uint32 start_rank = 3;            // 赛季榜分页，闭区间rank开始值
  uint32 end_rank = 4;              // 赛季榜分页，闭区间rank结束值
  repeated cl.PeakResult list = 5;  // 排行数据列表
  cl.PeakResult self = 6;           // 仅type为赛季榜时，才会有值
}

// 巅峰竞技场 - 战斗
message C2L_PeakFight {
  uint32 match_id = 1;
  uint64 left_uid = 2;   // 左侧玩家（进攻方）uid
  uint64 left_sid = 3;   // 左侧玩家（进攻方）sid
  uint32 left_pos = 4;   // 左侧玩家占位id（世界boss排名）
  uint64 right_uid = 5;  // 右侧玩家（防守方）uid
  uint64 right_sid = 6;  // 右侧玩家（防守方）sid
  uint32 right_pos = 7;  // 右侧玩家占位id（世界boss排名）
  int64 fight_time = 8;  // 战斗时间
}
message L2C_PeakFight {
  uint32 ret = 1;
  uint32 match_id = 2;
  cl.UserSnapshot left_snapshot = 3;   // 左侧玩家（进攻方）快照
  cl.UserSnapshot right_snapshot = 4;  // 右侧玩家（防守方）快照
  bool left_win = 5;                   // 左侧玩家（进攻方）是否胜利
  string report_id = 6;                // 战报id
  uint32 left_score = 7;               // 左侧玩家（进攻方）得分
  uint32 right_score = 8;              // 右侧玩家（防守方）得分
  int64 fight_time = 9;                // 回传参数 - 战斗时间
}

// 巅峰竞技场 - 选手完赛详情数据
message L2C_PeakFighterDetail {
  uint64 uid = 1;  // 玩家id
}
message C2L_PeakFighterDetail {
  uint32 ret = 1;
  uint64 uid = 2;                     // 玩家id
  repeated cl.PeakMatch matches = 3;  // 全部完赛数据
}

// 巅峰竞技场 - 推送状态更新
message L2C_PeakPushState {}
message C2L_PeakPushState {
  uint32 ret = 1;
  cl.PeakState state = 2;  // 跨服当前状态数据
}

// 巅峰竞技场 - 新周期开始，请求更新分数和参赛选手数据
message L2C_PeakUpdatePlayer {
  uint32 phase = 1;  // 当前是第几个小周期
}
message C2L_PeakUpdatePlayer {
  uint32 ret = 1;
  cl.PeakState state = 2;                    // 跨服当前状态数据
  map<uint64, cl.PeakRankScore> scores = 3;  // 当前赛季全部参赛者排名积分
  repeated uint64 uids = 4;                  // 小周期参赛玩家id列表
}

// 巅峰竞技场 - 获取快照
message C2L_PeakGetSnapshot {
  repeated uint64 uids = 1;  // 玩家id列表
}
message L2C_PeakGetSnapshot {
  uint32 ret = 1;
  repeated cl.UserSnapshot snapshots = 2;  // 玩家快照列表
}

// 排行榜 - 第一名
message L2C_PeakRankFirst {
  uint64 client_msg_id = 1;
}
message C2L_PeakRankFirst {
  uint32 ret = 1;
  uint64 client_msg_id = 2;
  cl.PeakResult value = 3;  //
}

message P2Guild_SyncBakData {
  map<uint64, cr.PeakPhaseUserBak> data = 1;
}

message Guild2P_SyncBakData {
  uint32 ret = 1;
}

// 巅峰竞技场 - 获取上一场战报
message L2C_PeakGetLastBattleReport {
  uint64 uid = 1;
}
message C2L_PeakGetLastBattleReport {
  uint32 ret = 1;
  uint64 uid = 2;
  string report_id = 3;  // 战报id
  bool is_attacker = 4;  // 是否进攻方
}

// 公会战-----------------------------------------------------------------------------

message L2CS_GSTGetData {
  uint32 type = 1;  // 获取简略和详细
  bool need_sign = 2;
  GSTSignUser user = 3;
  uint32 partition = 4;  // 服务器所在小分区
}
message CS2L_GSTGetData {
  uint32 ret = 1;
  uint32 type = 2;
  cl.GSTSimpleData simple = 3;
  cl.GSTComplexData complex = 4;
  cl.GSTSta sta = 5;
  int64 reward_duration = 6;  // 挂机的有效时长
  bool box_have_reward = 7;   // 挂机宝箱是否有奖励
  string chat_room_id = 8;    // 聊天id
  bool signed = 9;            // 是否报过名了
}

message L2CS_GSTGetRoomData {}
message CS2L_GSTGetRoomData {
  uint32 ret = 1;
  cl.GSTMapInfo map_info = 2;
}

message L2CS_GSTGetTeamsData {
  uint32 type = 1;  // 自己的队伍,管理获取全部队伍
}
message CS2L_GSTGetTeamsData {
  uint32 ret = 1;
  uint32 type = 2;
  repeated cl.GSTTeamData teams_data = 3;
}

message L2CS_GSTTeamOperate {
  uint32 type = 1;  // 队伍移动,撤回队伍,托管队伍,取消托管
  repeated cl.GSTTeamOperate operates = 2;
  bool is_auto = 3;  // 是否为一键派遣
}
message CS2L_GSTTeamOperate {
  uint32 ret = 1;
  uint32 type = 2;
  repeated cl.GSTTeamOperate operates = 3;
  map<uint32, cl.GSTGroundInfo> GSTGroundInfos = 4;
  repeated cl.GSTLogInfo log_info = 5;
  bool is_auto = 6;                                     // 是否为一键派遣
  cl.GstLogGeneral log_general = 7;                     // 埋点日志结构
  repeated log.GSTDispatchTeam gst_dispatch_teams = 8;  // 操作队伍日志
}

message L2CS_GSTExchangeGroundTeam {
  uint32 id = 1;  // 地块id
  repeated cl.GSTFightTeam teams = 2;
}

message CS2L_GSTExchangeGroundTeam {
  uint32 ret = 1;
  repeated cl.GSTFightTeam teams = 2;
  cl.GstLogGeneral log_general = 3;                   // 埋点日志结构
  repeated log.GSTReorderTeam gst_reorder_teams = 4;  // 操作队伍日志
}

// 获取自己所在组的排行信息
message L2CS_GSTRank {}
message CS2L_GSTRank {
  uint32 ret = 1;
  cl.GSTLastLRoundGuildRank guilds = 2;
  repeated cl.GSTGuildUserBase mems = 3;  // 排名
}

// 获取自己所在组的排行信息
message L2CS_GSTGroupUsersRank {
  cl.C2L_GSTGroupUsersRank req = 1;
}
message CS2L_GSTGroupUsersRank {
  uint32 ret = 1;
  cl.L2C_GSTGroupUsersRank resp = 2;
}

message L2CS_GSTGroupUsersRankLike {
  cl.C2L_GSTGroupUsersRankLike req = 1;
}

message CS2L_GSTGroupUsersRankLike {
  uint32 ret = 1;
  cl.L2C_GSTGroupUsersRankLike resp = 2;
}

message CS2L_GSTPushChatMessage {
  uint32 ret = 1;
  uint32 type = 2;             // 消息类型
  repeated string params = 3;  // 消息参数
  string chat_room_id = 4;     // 聊天房间ID
  uint64 guild_id = 5;         // 公会id
}

message L2CS_GSTGetTasksData {}
message CS2L_GSTGetTasksData {
  uint32 ret = 1;
  cl.GSTTaskInfo cstg_task_info = 2;
}

message L2CS_GSTGetTasksReward {
  repeated uint32 task_ids = 1;
}
message CS2L_GSTGetTasksReward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  cl.GSTTaskInfo cstg_task_info = 3;
  uint32 map_group_id = 4;
  cl.GstLogGeneral log_general = 5;
}

message L2CS_GSTDonate {
  uint32 goddess_id = 1;
  repeated cl.GSTClientDonate gst_client_donates = 2;
}

message CS2L_GSTDonate {
  uint32 ret = 1;
  uint32 goddess_id = 2;
  repeated cl.GSTClientDonate gst_client_donates = 3;
  cl.GSTGoddessBless blessInfo = 4;
  cl.GstLogGeneral log_general = 5;
  uint32 this_donate_point = 6;
}

message L2CS_GSTMessageEdit {
  string message = 1;
  uint64 message_id = 2;
}
message CS2L_GSTMessageEdit {
  uint32 ret = 1;
  string message = 2;
  uint64 message_id = 3;
  cl.GstLogGeneral log_general = 4;
}

message CS2L_GSTFight {
  cl.GSTFightResult fight = 1;
  cl.GstLogGeneral log_general = 2;
  uint32 space_type = 3;
  uint32 space_id = 4;
}
message L2CS_GSTFight {
  uint32 ret = 1;  // 战斗错误情况下攻方赢,没有战报
  cl.GSTFightResult fight = 2;
}

message L2CS_GSTPushSta {}

message CS2L_GSTPushSta {
  uint32 ret = 1;
  cl.GSTSta sta = 2;
}

message L2CS_GSTGetHangUpReward {}

message CS2L_GSTGetHangUpReward {
  uint32 ret = 1;
  repeated cl.Resource award = 2;
  int64 reward_duration = 3;
  repeated cl.Resource dispatch_team_award = 4;
  repeated cl.Resource battle_win_award = 5;
  repeated cl.Resource build_donate_award = 6;
}

message L2CS_GSTSetTeam {
  uint32 formation_id = 1;  // 阵容ID
  repeated cl.GSTSetFormationTeamInfo formation = 2;
  bool is_hand = 3;
}

message CS2L_GSTSetTeam {
  uint32 ret = 1;
  uint32 formation_id = 2;
  repeated cl.GSTSetFormationTeamInfo formation = 3;
  bool is_hand = 4;
}

message L2CS_GSTPreviewHangUpReward {}

message CS2L_GSTPreviewHangUpReward {
  uint32 ret = 1;
  uint32 max_ground_count = 2;
  int64 reward_duration = 3;
  repeated cl.Resource award = 4;
  repeated cl.Resource dispatch_award = 5;
  repeated cl.Resource win_battle_award = 6;
  uint32 dispatch_award_team_count = 7;
  uint32 win_battle_award_team_count = 8;
  repeated cl.Resource build_dispatch_award = 9;
  cl.Resource ore_award = 10;
}

message GST2GUILD_GSTSyncGuilds {
  repeated uint64 guild_id = 1;
  bool full_sync = 2;
  L2CS_GSTGetData req = 3;
}

message GUILD2GST_GSTSyncGuilds {
  uint32 ret = 1;
  repeated GSTSyncGuild guilds = 2;
  bool full_sync = 3;
  L2CS_GSTGetData req = 4;
}

message GSTSyncGuild {
  bool dissolve = 1;  // 是否解散(db也找不到)
  uint64 guild_id = 2;
  uint32 guild_badge = 3;
  repeated uint64 user_ids = 4;
  uint32 division = 5;
  uint32 division_score = 6;
  string guild_name = 7;
  repeated uint64 leaders = 8;
  repeated int64 expire_time = 9;
  uint64 guild_sid = 10;
  uint32 partition = 11;
  uint32 mob_round = 12;
  uint32 mob_score = 13;
  int64 mob_time = 14;
  uint32 level = 15;
}

message GSTSignUser {
  uint64 id = 1;
  string name = 2;
  uint32 season_lv = 3;  // 赛季等级
  uint32 base_id = 4;    // 头像相关
  uint64 sid = 5;        // 服务器ID
  uint64 guild_id = 6;
  repeated GSTSignTeam teams = 7;  // 队伍信息
  int64 season_link_power = 8;     // 丰碑加成战斗力
  repeated int64 expire_time = 9;
  map<uint32, bool> seasonlink_actived = 10;  // 赛季羁绊激活的英雄
  uint32 add_fight_times = 11;                // 影响队伍的可战斗次数
  uint32 image = 12;                          // 形象ID
  uint32 show_hero = 13;                      // 战力最高的英雄sysID
  uint32 talent_tree_lv = 14;                 // 天赋树根节点等级
  uint32 title = 15;                          // 称号
  repeated cl.SeasonAddInfo season_add = 16;  // 赛季加成数据
  uint32 pokemon_image = 17;                  // 宠物形象ID
}

message GSTSignTeam {
  repeated cl.GSTTeamHeroInfo heros = 1;
  uint32 team_index = 2;
  int64 power = 3;
}

message L2CS_GSTGetGroundData {
  uint32 id = 1;  // 地块id
}
message CS2L_GSTGetGroundData {
  uint32 ret = 1;
  uint32 id = 2;  // 地块id
  cl.GSTGroundTeamData team_data = 3;
  cl.GSTFightTeam monster_team = 4;    // 怪物队伍信息
  string first_occupy_guild_name = 5;  // 首占公会
  cl.GSTArenaInfo arena_info = 6;
  uint32 arena_vote_times = 7;
}
message L2CS_GSTAward {
  uint32 season = 1;
  uint32 round = 2;
  uint32 lround = 3;
}

message CS2L_GSTAward {
  uint32 ret = 1;
  uint32 season = 2;
  uint32 round = 3;
  repeated cr.GSTAwardData gst_awardDatas = 4;
  cl.GSTSta sta = 5;
  uint32 lround = 6;
}

message L2CS_GSTGetGuildDonateData {}

message CS2L_GSTGetGuildDonateData {
  uint32 ret = 1;
  repeated cl.GSTSimpleGuild goddess_rank_info = 2;  // 公会捐赠信息
}

message L2CS_GSTGetGuildDonateMemData {
  uint32 goddess_id = 1;
}

message CS2L_GSTGetGuildDonateMemData {
  uint32 ret = 1;
  uint32 goddess_id = 2;
  repeated cl.GSTGuildUserBase user_rank = 3;
}

message L2CS_GSTSYNCGuildUserInfo {
  cl.GSTGuildUserBase gstUser = 1;
  map<uint32, bool> seasonlink_actived = 2;  // 赛季羁绊激活的英雄
  uint32 add_fight_times = 3;                // 影响队伍的可战斗次数
}

message CS2L_GSTSYNCGuildUserInfo {
  uint32 ret = 1;
}

message GST2GUILD_AddDivisionScore {
  uint32 round = 1;                            // 轮次
  map<uint64, uint32> add_division_score = 2;  // 公会增加的段位积分
}
message GUILD2GST_AddDivisionScore {}

message GST2GUILD_GuildMobilizationSettle {
  uint32 round = 1;
  map<uint64, uint32> settle_awards = 2;  // 排行结算奖励
}
message GUILD2GST_GuildMobilizationSettle {
  uint32 round = 1;
}

message CS2L_GSTTaskUpdate {
  uint32 ret = 1;
  map<uint32, cl.TaskTypeProgress> task_progress = 2;
}

message CS2L_GSTTaskUpdateOther {
  uint32 ret = 1;
  map<uint32, cl.TaskTypeProgress> task_progress = 2;
  uint64 uid = 3;
}

message L2CS_GSTUpdateViewFormation {}

message CS2L_GSTUpdateViewFormation {
  uint32 ret = 1;
}

message L2CS_GSTGuildBuildGetData {}

message CS2L_GSTGuildBuildGetData {
  uint32 ret = 1;
  repeated cl.GSTBuild builds = 2;
  map<uint32, uint32> guild_build_dispatch = 3;
}

message L2CS_GSTGuildBuildDonate {
  uint32 build_type = 1;
  repeated cl.Resource cost = 2;
}

message CS2L_GSTGuildBuildDonate {
  uint32 ret = 1;
  uint32 build_type = 2;
  repeated cl.Resource cost = 3;
  cl.GSTBuild build = 4;
  cl.GSTBuild old_build = 5;
}

message L2CS_GSTGuildBuildDispatchHero {
  map<uint32, cl.GSTBuildUserDispatchHeroes> build_dispatch_heroes = 1;
  uint32 build_type = 2;
}

message CS2L_GSTGuildBuildDispatchHero {
  uint32 ret = 1;
  map<uint32, cl.GSTBuildUserDispatchHeroes> build_dispatch_heroes = 2;
  map<uint32, uint32> build_heroes = 3;
  uint32 build_type = 4;
}

message L2CS_GSTGuildBuildDonateRank {}

message CS2L_GSTGuildBuildDonateRank {
  uint32 ret = 1;
  repeated cl.GSTBuildDonateRankClient rank = 2;
}

message L2CS_GSTGuildBuildGetTaskData {}

message CS2L_GSTGuildBuildGetTaskData {
  uint32 ret = 1;
  cl.GSTBuildTaskInfo task = 2;
}

message L2CS_GSTGuildBuildRecvTaskAward {
  repeated uint32 task_ids = 1;
}

message CS2L_GSTGuildBuildRecvTaskAward {
  uint32 ret = 1;
  repeated uint32 task_ids = 2;
  repeated cl.Resource awards = 3;
}

message CS2L_GSTGuildBuildTaskProgressUpdate {
  uint32 ret = 1;
  map<uint32, cl.TaskTypeProgress> task_progress = 2;
}

message L2CS_GSTGuildFirstCenterAward {
  uint32 season_id = 1;
  uint32 round = 2;
  uint32 lround = 3;
}

message CS2L_GSTGuildFirstCenterAward {
  uint32 ret = 1;
  map<uint32, cr.GSTFirstCenterAwardData> award_data = 2;
  uint32 season_id = 3;
  uint32 round = 4;
  uint32 lround = 5;
}

message L2CS_GSTArenaVote {
  cl.C2L_GSTArenaVote req = 1;
}

message CS2L_GSTArenaVote {
  cl.L2C_GSTArenaVote resp = 1;
}

message L2CS_GSTGetArenaVoteRecord {
  uint64 guild_id = 1;
}

message CS2L_GSTGetArenaVoteRecord {
  uint32 ret = 1;
  uint64 guild_id = 2;
  cl.GSTArenaGuildInfo record = 3;
}

message L2CS_GSTBossGet {}
message CS2L_GSTBossGet {
  uint32 ret = 1;
  cl.GSTBossGroup boss_group = 2;              // 公会boss_group
  repeated cl.GSTBossFightRank rank_list = 3;  // 前3名
  map<uint32, uint32> boss_award = 4;          // boss战奖励 index：0未领奖 1已领奖
}

message L2CS_GSTBossFight {
  uint32 boss_id = 1;    // bossId
  bool sweep = 2;        // 是否扫荡
  string report_id = 3;  // 战报id
  uint64 damage = 4;     // 伤害
}
message CS2L_GSTBossFight {
  uint32 ret = 1;
  uint32 boss_id = 2;      // bossId
  bool sweep = 3;          // 是否扫荡
  string report_id = 4;    // 战报id
  uint64 damage = 5;       // 伤害
  uint32 fight_hp = 6;     // 战斗血量
  cl.GSTBoss boss = 7;     // boss信息
  bool is_first_kill = 8;  // 是否首次击杀
  uint32 partition = 9;    // 战区
  cl.GSTSta sta = 10;
  uint64 group_id = 11;             // 房间id
  uint32 quality = 12;              // 房间品质
  cl.GSTBossGroup boss_group = 13;  // 公会boss_group
  bool need_update_rank = 14;       // 是否需要更新伤害榜
}

// 未领取奖励一次性领取
message L2CS_GSTBossAward {}
message CS2L_GSTBossAward {
  uint32 ret = 1;
  uint32 monster_group = 2;
  repeated uint32 award_index = 3;  // 奖励下标
  repeated cl.Resource awards = 4;  // 奖励
}

message L2CS_GSTBossRank {}
message CS2L_GSTBossRank {
  uint32 ret = 1;
  repeated cl.GSTBossFightRank rank_list = 2;
}

message L2CS_SeasonArenaGetData {
  bool cross = 1;
  cl.UserSnapshot snapshot = 2;
}

message CS2L_SeasonArenaGetData {
  uint32 ret = 1;
  cl.UserSnapshot snapshot = 2;
  cl.SeasonArenaSta sta = 3;
  cr.CrossSeasonArenaUserBase season_arena_user_base = 4;
  repeated cl.SeasonArenaOpponentClient opponent_client = 5;
  bool cross = 6;
}

// 排行榜 - 实时
message L2CS_SeasonArenaGetRankList {}

message CS2L_SeasonArenaGetRankList {
  uint32 ret = 1;
  uint64 uid = 2;
  repeated cl.RankValue list = 3;
  uint32 self_rank = 4;
  cl.RankValue self_value = 5;
}

message L2CS_SeasonArenaGetFame {
  bool simple = 1;
}

message CS2L_SeasonArenaGetFame {
  uint32 ret = 1;
  cr.CrossSeasonArenaFame fame = 2;
  bool simple = 3;
}

// LogicA -> Cross
message L2CS_SeasonArenaFightStart {
  uint64 defender_uid = 1;  // 防守方id
  uint64 defender_sid = 2;  // 防守方sid
  uint64 attacker_sid = 3;  // 进攻方sid
  uint32 division = 4;      // 段位
}

// Cross -> logicA
message CS2L_SeasonArenaFightStart {
  uint32 ret = 1;
  uint64 attacker_sid = 2;          // 进攻方sid
  uint64 attacker_uid = 3;          // 进攻方id
  uint64 defender_uid = 4;          // 防守方id
  uint64 defender_sid = 5;          // 防守方sid
  uint32 bot_id = 6;                // 机器人在量表中的id（真实玩家此值为0）
  string bot_name = 7;              // 机器人昵称（真实玩家此值为空）
  uint32 attack_formation_id = 8;   // 进攻方阵容
  uint32 defense_formation_id = 9;  // 防守方阵容
  uint32 formation_mode = 10;       // 阵容模式
  uint32 show_team = 11;            // 前几队不随机
}

// logicA -> Cross
message L2CS_SeasonArenaFightFinish {
  uint64 attacker_sid = 1;                // 进攻方服务器id
  uint64 attacker_uid = 2;                // 进攻方玩家id
  uint64 defender_sid = 3;                // 防守方玩家sid
  uint64 defender_uid = 4;                // 防守方玩家id
  cl.UserSnapshot defender_snapshot = 5;  // 防守方快照
  bool attacker_win = 6;                  // 进攻方是否胜利
  string report_id = 7;                   // 战报id
  bool is_bot = 8;                        // 对手是否是机器人
  bool defender_division = 9;             // 防守方是否是机器人
  uint32 attack_formation_id = 10;        // 进攻方阵容
}

message SeasonArenaFightResult {
  uint32 score = 1;  // 积分
  uint32 rank = 2;   // 排名
}

// Cross -> logicA
message CS2L_SeasonArenaFightFinish {
  uint32 ret = 1;
  cl.SeasonArenaOpponentClient attacker = 2;       // 进攻方战后数据
  SeasonArenaFightResult attacker_old_result = 3;  // 进攻方战前成绩
  SeasonArenaFightResult attacker_new_result = 4;  // 进攻方战后成绩
  SeasonArenaFightResult defender_old_result = 6;  // 防守方战前成绩
  SeasonArenaFightResult defender_new_result = 7;  // 防守方战后成绩
  cl.MiniUserSnapshot defender_snapshot = 8;       // 防守方快照
  bool attacker_win = 9;                           // 进攻方是否胜利
  bool is_bot = 10;                                // 对手是否是机器人
  string report_id = 11;                           // 战报id
  bool promoted = 12;                              // 是否自动晋升
  uint64 defender_gid = 13;                        // 防守者公会ID
  uint32 defender_arena = 14;                      // 防守者战区ID
  uint32 attack_formation_id = 15;                 // 进攻方队伍数量
}

message CS2L_SeasonArenaDefinePush {
  uint32 ret = 1;
  SeasonArenaFightResult old_result = 2;                    // 战前成绩
  cl.SeasonArenaOpponentClient defender = 3;                // 战后数据
  cl.MiniUserSnapshot attacker_snapshot = 4;                // 进攻方快照
  SeasonArenaFightResult new_result = 5;                    // 战后成绩
  bool attacker_win = 6;                                    // 进攻方是否胜利
  string report_id = 7;                                     // 战报id
  SeasonArenaFightResult attacker_old_result = 8;           // 进攻方战前成绩
  SeasonArenaFightResult attacker_new_result = 9;           // 进攻方战后成绩
  cl.UserSnapshot attacker_full_snapshot = 10;              // 进攻方完整快照
  cl.SeasonArenaSta sta = 11;                               // 竞技场状态
  cr.CrossSeasonArenaUserBase season_arena_user_base = 12;  // 竞技场玩家基础数据
  uint32 attack_formation_id = 13;                          // 进攻方队伍数量
}

message L2CS_SeasonArenaAward {
  uint64 sid = 1;  // 服务器ID
  uint32 season = 2;
  uint32 round = 3;
}

message CS2L_SeasonArenaAward {
  uint32 ret = 1;
  uint64 sid = 2;  // 服务器id
  uint32 season = 3;
  uint32 round = 4;
  cr.CrossSeasonArenaServerBak server_back = 5;
}

message L2CS_SeasonArenaRefresh {}

message CS2L_SeasonArenaRefresh {
  uint32 ret = 1;
  repeated cl.SeasonArenaOpponentClient Opponent = 3;
  int64 time = 4;
}

message L2CS_SeasonArenaGetSta {}

message CS2L_SeasonArenaGetSta {
  uint32 ret = 1;
  cl.SeasonArenaSta sta = 2;
}

message CS2L_SeasonArenaTopChange {
  uint32 ret = 1;
  cl.RankValue rank = 2;
}

message L2CS_SeasonArenaUpdateSnapshot {
  cl.UserSnapshot snapshot = 1;
}

message L2CS_GmSetSeasonArenaScore {
  uint32 score = 1;
}

message L2CS_SeasonArenaUpdateFormationPower {
  repeated cl.FormationSidUidPower data = 1;
}

// 源->cross(router) -> 指定cross
message L2C_RemoteCrossActivityPartition {
  uint32 act_id = 1;            // 玩法
  uint32 target_partition = 2;  // 目标小分区
  uint32 proto_id = 3;          // 协议
  bytes data = 4;               // 二进制数据流(encoded pb message)
}

// 龙战
message L2CS_GSTDragonGetData {}
message CS2L_GSTDragonGetData {
  uint32 ret = 1;
  uint32 dragon_round = 2;                  // 当前龙战场次
  cl.GSTDragonGuild dragon_guild = 3;       // 公会龙战数据
  cl.GSTDragonUser dragon_user = 4;         // 玩家龙战数据
  uint32 strategy_skill_num = 5;            // 祝福技能能使用次数
  repeated cl.GSTDragonFightRank top3 = 6;  // 公会内总伤害前3名
}

message L2CS_GSTDragonGetCultivation {}
message CS2L_GSTDragonGetCultivation {
  uint32 ret = 1;
  cl.GSTDragonCultivation data = 2;  // 龙战养龙数据
}

message L2CS_GSTDragonShow {
  uint32 dragon_pos = 1;
}

message CS2L_GSTDragonShow {
  uint32 ret = 1;
  uint32 dragon_pos = 2;
  uint32 dragon_id = 3;
}

message L2CS_GSTDragonEvolve {
  uint32 dragon_pos = 1;
  uint32 evolve_id = 2;
}

message CS2L_GSTDragonEvolve {
  uint32 ret = 1;
  uint32 dragon_pos = 2;
  uint32 dragon_id = 3;
}

message L2CS_GSTDragonFightStart {
  uint32 dragon_pos = 1;
  bool sweep = 2;         // 是否扫荡
  bytes client_data = 3;  // 前端透传字段
}

message CS2L_GSTDragonFightStart {
  uint32 ret = 1;
  uint32 dragon_pos = 2;
  uint32 dragon_id = 3;
  uint32 dragon_level = 4;
  bool sweep = 5;
  bytes client_data = 6;
}

message L2CS_GSTDragonFightFinish {
  uint32 dragon_pos = 1;
  uint32 dragon_id = 2;
  uint32 dragon_level = 3;
  repeated cl.GSTDragonFightRecordTeamData teams = 4;  // 各队数据
  bool sweep = 5;                                      // 是否扫荡
  string report_id = 6;                                // 战报id
  uint32 formation_id = 7;                             // 阵容id
}

message CS2L_GSTDragonFightFinish {
  uint32 ret = 1;
  bool sweep = 2;        // 是否扫荡
  string report_id = 3;  // 战报id
  uint32 dragon_pos = 4;
  uint32 dragon_id = 5;
  uint32 dragon_level = 6;
  uint64 total_damage = 7;
  uint32 reduce_hp = 9;
  uint32 add_progress = 10;
  bool need_update_rank = 11;                // 是否需要更新伤害榜
  uint32 total_count = 12;                   // 剩余挑战次数
  cl.GSTDragonBattle current_battle = 13;    // 本场龙战数据
  repeated cl.GSTDragonFightRank top3 = 14;  // 公会内总伤害前3名
  uint32 team_num = 15;
  uint32 formation_id = 16;
  log.LogGSTDragonGuild own_guild = 17;                     // 我方公会数据
  log.LogGSTDragonGuild op_guild = 18;                      // 敌方公会数据
  uint32 season_id = 19;                                    // 赛季
  uint32 dragon_round = 20;                                 // 龙战场次
  repeated cl.GSTDragonFightRecordTeamData team_data = 21;  // 各队数据
}

message L2CS_GSTDragonTaskGetData {}

message CS2L_GSTDragonTaskGetData {
  uint32 ret = 1;
  cl.GSTDragonTaskInfo task = 2;
}

message L2CS_GSTDragonTaskAward {
  repeated uint32 task_ids = 1;
}

message CS2L_GSTDragonTaskAward {
  uint32 ret = 1;
  uint32 dragon_round = 2;
  repeated uint32 task_ids = 3;
  repeated cl.Resource awards = 4;
}

message CS2L_GSTDragonTaskProgressUpdate {
  uint32 ret = 1;
  map<uint32, cl.TaskTypeProgress> task_progress = 2;
}

message L2CS_GSTDragonRank {}
message CS2L_GSTDragonRank {
  uint32 ret = 1;
  repeated cl.GSTDragonFightRank rank_list = 2;
}

message L2CS_GSTDragonAward {
  uint32 season = 1;
  uint32 dragon_round = 2;
}

message CS2L_GSTDragonAward {
  uint32 ret = 1;
  uint32 season = 2;
  uint32 dragon_round = 3;
  cl.GSTSta sta = 4;
  cr.GSTDragonBak dragon_award_data = 5;
}

message L2CS_GSTDragonFightBuyCountStart {
  uint32 count = 1;
}

message CS2L_GSTDragonFightBuyCountStart {
  uint32 ret = 1;
  uint32 count = 2;
  repeated cl.Resource costs = 3;
}

message L2CS_GSTDragonFightBuyCountFinish {
  uint32 count = 1;
  repeated cl.Resource costs = 2;
}

message CS2L_GSTDragonFightBuyCountFinish {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 total_count = 3;
  repeated cl.Resource costs = 4;
}

message L2CS_GSTDragonFightUseTicket {
  uint32 count = 1;
  repeated cl.Resource costs = 2;
}

message CS2L_GSTDragonFightUseTicket {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 total_count = 3;
  repeated cl.Resource costs = 4;
}

// 龙攻城技能操作
message L2CS_GSTDragonSkillOperate {
  uint32 ground_id = 1;  // 地块id
}

message CS2L_GSTDragonSkillOperate {
  uint32 ret = 1;
  uint32 ground_id = 2;
  uint64 own_gid = 3;
  uint64 op_gid = 4;
  uint32 own_division = 5;
  uint32 op_division = 6;
}

// 使用祝福技能
message L2CS_GSTDragonStrategySkill {
  uint32 count = 1;
}

message CS2L_GSTDragonStrategySkill {
  uint32 ret = 1;
  uint32 count = 2;
  uint32 left_count = 3;
  uint64 gid = 4;
  uint32 guild_division = 5;
}

message L2CS_GSTScorePreview {
  uint64 guild_id = 1;
}

message CS2L_GSTScorePreview {
  uint32 ret = 1;
  uint64 guild_id = 2;
  uint32 score = 3;
}

message L2CS_HotRankCollectionLogUpdate {
  map<uint32, db.LogicHotRankCollectionLog> collection_log = 1;
}

message CS2L_HotRankCollectionLogUpdate {
  uint32 ret = 1;
}

message L2CS_HotRankGet {
  uint32 rank = 1;
}

message CS2L_HotRankGet {
  uint32 ret = 1;
  uint32 rank = 2;
  cl.CultivateHotRank hot_rank = 3;
  int64 next_merge_time = 4;
}

message L2CS_GuildMobilizationGetData {}
message CS2L_GuildMobilizationGetData {
  cl.L2C_GuildMobilizationGetData rsp = 1;
}

message L2CS_GuildMobilizationAcceptTask {
  uint32 task_id = 1;
}
message CS2L_GuildMobilizationAcceptTask {
  cl.L2C_GuildMobilizationAcceptTask rsp = 1;
}

message L2CS_GuildMobilizationSignTask {
  uint32 task_id = 1;
  bool is_sign = 2;  // 加标记和撤销标记
}
message CS2L_GuildMobilizationSignTask {
  cl.L2C_GuildMobilizationSignTask rsp = 1;
}

message L2CS_GuildMobilizationFinishTaskLogs {}
message CS2L_GuildMobilizationFinishTaskLogs {
  cl.L2C_GuildMobilizationFinishTaskLogs rsp = 1;
}

message L2CS_GuildMobilizationPersonalRank {}
message CS2L_GuildMobilizationPersonalRank {
  cl.L2C_GuildMobilizationPersonalRank rsp = 1;
}

message L2CS_GuildMobilizationGuildRank {
  uint64 guild_id = 1;
  uint32 rank_type = 2;  // common.GUILD_MOBILIZATION_RANK_TYPE
}
message CS2L_GuildMobilizationGuildRank {
  cl.L2C_GuildMobilizationGuildRank rsp = 1;
}

message L2CS_GuildMobilizationRecvScoreLevel {
  uint32 level = 1;
}
message CS2L_GuildMobilizationRecvScoreLevel {
  cl.L2C_GuildMobilizationRecvScoreLevel rsp = 1;
}

message L2CS_GuildMobilizationFreshTask {
  uint32 task_id = 1;
}
message CS2L_GuildMobilizationFreshTask {
  cl.L2C_GuildMobilizationFreshTask rsp = 1;
}

message L2CS_GuildMobilizationBuyTimes {
  uint32 type = 1;  // 1 刷新任务次数 2 个人接取任务次数
  uint32 buy_times = 2;
  uint32 need_diamond = 3;  // 前端传来做校验用
}
message CS2L_GuildMobilizationBuyTimes {
  cl.L2C_GuildMobilizationBuyTimes rsp = 1;
}

message L2CS_GuildMobilizationSysFinishedTask {
  repeated uint32 finished_tasks = 1;
}
message CS2L_GuildMobilizationSysFinishedTask {
  cl.L2C_GuildMobilizationUpdateTaskProgress rsp = 1;
}

message CS2L_GSTMobRankSettle {
  uint32 mob_round = 1;
  repeated cr.GSTGuildMobRankSettleReward rewards = 2;
}
message L2CS_GSTMobRankSettle {
  uint32 mob_round = 1;
}

message GUILD2GST_GSTSyncGuildMob {
  cl.GuildMobilizationGuildRank data = 1;
}

message L2CS_GuildMobilizationAddScoreForGM {
  uint32 score = 1;
}

message L2CS_GuildMobilizationEditMessageBoard {
  string message = 1;
}

message CS2L_GuildMobilizationEditMessageBoard {
  cl.L2C_GuildMobilizationEditMessageBoard rsp = 1;
}

message L2CS_GuildMobilizationGiveUpTask {
  uint32 task_id = 1;
}

message CS2L_GuildMobilizationGiveUpTask {
  cl.L2C_GuildMobilizationGiveUpTask rsp = 1;
}

message L2CS_GuildMobilizationScoreAward {
  repeated uint32 ids = 1;
}

message CS2L_GuildMobilizationScoreAward {
  cl.L2C_GuildMobilizationScoreAward rsp = 1;
}

message L2CS_GuildMobilizationCancelTask {
  uint32 task_id = 1;
  uint64 uid = 2;
}

message CS2L_GuildMobilizationCancelTask {
  cl.L2C_GuildMobilizationCancelTask rsp = 1;
}

message CS2L_GuildMobilizationBeCancelTask {
  uint32 ret = 1;
  uint32 task_id = 2;
  uint64 uid = 3;
}

message L2CS_HotRankSyncTalentTreeHot {
  repeated cl.TalentTreeNodeHot nodes = 1;
}

message CS2L_HotRankSyncTalentTreeHot {
  uint32 ret = 1;
}

message L2CS_HotRankFlushTalentTreeHot {}

message CS2L_HotRankFlushTalentTreeHot {
  uint32 ret = 1;
  repeated cl.TalentTreeNodeHot nodes = 2;
}

message L2CS_GSTOreGetData {}
message CS2L_GSTOreGetData {
  cl.L2C_GSTOreGetData rsp = 1;
}

message L2CS_GSTOreFightStart {
  cl.C2L_GSTOreFight req = 1;
}
message CS2L_GSTOreFightStart {
  uint32 ret = 1;
  cl.C2L_GSTOreFight req = 2;
  map<uint32, cl.AltPassives> alt_passives = 3;
}

message L2CS_GSTOreFightFinish {
  cl.C2L_GSTOreFight req = 1;
  GSTOreFightData req_data = 2;
}
message GSTOreFightData {
  uint32 add_progress = 1;
  string report_id = 2;
  uint64 damage = 3;
}
message CS2L_GSTOreFightFinish {
  cl.L2C_GSTOreFight rsp = 1;
  uint32 turn = 2;
  uint32 round = 3;
}

message L2CS_GSTOreBuyTimes {
  cl.C2L_GSTOreBuyTimes req = 1;
}
message CS2L_GSTOreBuyTimes {
  cl.L2C_GSTOreBuyTimes rsp = 1;
}

message L2CS_GSTOreSearchAssist {}
message CS2L_GSTOreSearchAssist {
  cl.L2C_GSTOreSearchAssist rsp = 1;
}

message L2CS_GSTOreOccupy {
  cl.C2L_GSTOreOccupy req = 1;
}
message CS2L_GSTOreOccupy {
  cl.L2C_GSTOreOccupy rsp = 1;
}

message L2CS_GSTOreGetOreData {
  cl.C2L_GSTOreGetOreData req = 1;
}
message CS2L_GSTOreGetOreData {
  cl.L2C_GSTOreGetOreData rsp = 1;
}

message L2CS_GSTTechGetData {}
message CS2L_GSTTechGetData {
  cl.L2C_GSTTechGetData rsp = 1;
}

message L2CS_GSTTechDonate {
  uint32 tech_id = 1;
  uint32 donate_num = 2;
}
message CS2L_GSTTechDonate {
  cl.L2C_GSTTechDonate rsp = 1;
  uint32 node_level_before = 2;
}

message L2CS_GSTTechTaskReward {
  repeated uint32 task_ids = 1;
}
message CS2L_GSTTechTaskReward {
  cl.L2C_GSTTechTaskReward rsp = 1;
}

message L2CS_GSTTechGuildUserRank {}
message CS2L_GSTTechGuildUserRank {
  cl.L2C_GSTTechGuildUserRank rsp = 1;
}

message L2CS_GSTSkillAssemble {
  uint32 ground_id = 1;
}
message CS2L_GSTSkillAssemble {
  cl.L2C_GSTSkillAssemble rsp = 1;
}

message L2CS_GSTTechSign {
  uint32 tech_id = 1;
}
message CS2L_GSTTechSign {
  cl.L2C_GSTTechSign rsp = 1;
}

message L2CS_GSTChallengeGetData {}
message CS2L_GSTChallengeGetData {
  cl.L2C_GSTChallengeGetData resp = 1;
}

message L2CS_GSTChallengeMatch {
  repeated cl.GSTChallengeMatchInfo init_data = 1;
}

message CS2L_GSTChallengeMatch {
  cl.L2C_GSTChallengeMatch resp = 1;
}

message L2CS_GSTChallengeFightStart {
  uint32 team_index = 1;
}

message CS2L_GSTChallengeFightStart {
  uint32 ret = 1;
  cl.GSTChallengeMatchInfo match = 2;
  cl.GSTGuildUserChallenge user = 3;
  cl.GSTChallengeTeamInfo defender = 4;
}

message L2CS_GSTChallengeFightFinish {
  cl.GSTChallengeFightLog fight_log = 1;
}

message CS2L_GSTChallengeFightFinish {
  uint32 ret = 1;
  cl.GSTChallengeFightLog fight_log = 2;
  cl.GSTGuildUserChallenge challenge = 3;
  uint32 room_rank = 4;
  bool match_round_end = 5;
  log.LogGSTChallengeFight log_data = 6;
}

message L2CS_GSTChallengeBuffChoose {
  cl.C2L_GSTChallengeBuffChoose req = 1;
}

message CS2L_GSTChallengeBuffChoose {
  cl.L2C_GSTChallengeBuffChoose resp = 1;
}

message L2CS_GSTChallengeTaskReward {
  repeated uint32 task_ids = 1;
}

message CS2L_GSTChallengeTaskReward {
  cl.L2C_GSTChallengeTaskReward resp = 1;
}

message L2CS_GSTChallengeRank {
  cl.C2L_GSTChallengeRank req = 1;
}

message CS2L_GSTChallengeRank {
  cl.L2C_GSTChallengeRank resp = 1;
}

message L2CS_GSTChallengeAward {
  uint32 season_id = 1;
  uint32 round = 2;
}

message CS2L_GSTChallengeAward {
  uint32 ret = 1;
  uint32 season_id = 2;
  uint32 round = 3;
  cr.GSTChallengeBak bak_data = 5;
}

message L2CS_SeasonComplianceRankUpdate {
  cl.SeasonComplianceStage stage = 1;
  cr.SeasonCompliance common_data = 2;
  cr.SeasonCompliance total_data = 3;
}

message CS2L_SeasonComplianceRankUpdate {
  uint32 ret = 1;
  cl.SeasonComplianceStage stage = 2;
  cr.SeasonCompliance data = 3;
  repeated cl.ComplianceSourcePoint source_point = 4;
  uint32 new_rank = 5;
}

message L2CS_SeasonComplianceRankGetList {
  uint32 rank_id = 1;  // 对应表里的RankID
  uint32 begin_rank = 2;
  uint32 end_rank = 3;
}

message CS2L_SeasonComplianceRankGetList {
  uint32 ret = 1;
  uint32 rank_id = 2;  // 对应表里的RankID
  cl.ComplianceList compliance_list = 3;
  uint32 begin_rank = 4;
  uint32 end_rank = 5;
}

message L2CS_SeasonComplianceRankAward {
  cl.SeasonComplianceStage stage = 1;
  uint32 begin_rank = 2;
  uint32 end_rank = 3;
}

message CS2L_SeasonComplianceRankAward {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint32 award_time = 3;
  uint32 begin_rank = 4;
  uint32 end_rank = 5;
  repeated cl.RankValue rank_data = 6;
  repeated cl.RankValue total_rank_data = 7;
  cl.SeasonComplianceStage stage = 8;
}

message L2CS_SeasonComplianceGetData {
  uint32 rank_id = 1;
}

message CS2L_SeasonComplianceGetData {
  uint32 ret = 1;
  uint32 rank_id = 2;
  cl.ComplianceList list = 3;
  cl.ComplianceList total_list = 4;
}

message L2CS_SeasonMapPositionLogs {
  cl.C2L_SeasonMapPositionLogs req = 1;
}
message CS2L_SeasonMapPositionLogs {
  cl.L2C_SeasonMapPositionLogs rsp = 1;
}
message L2CS_SeasonMapSync {
  SeasonMapSyncPositionLog log = 1;
}

enum SeasonMapPositionLogType {
  None1 = 0;
  Move1 = 1;
  Event1 = 2;
}

message SeasonMapSyncPositionLog {
  uint32 position = 1;
  SeasonMapPositionLogType log_type = 2;
  cl.SeasonMapMoveLog move_log = 3;
  cl.SeasonMapEventLog event_log = 4;
}
