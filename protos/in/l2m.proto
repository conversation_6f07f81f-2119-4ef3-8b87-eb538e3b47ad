syntax = "proto3";

option go_package = "app/protos/in/l2m";

import "cl.proto";

// logic <-> mongoStorage
package l2m;

enum ID {
  Fn_Min = 0;
  Fn_GetCognitionLog = 1;  // 获取通关日志
  Fn_PutCognitionLog = 2;  // 保存通关日志
  Fn_PutSeasonLog = 3;     // 保存赛季记录
  Fn_PutGstLog = 4;        // 保存公会战日志
  Fn_GetGstLog = 5;        // 获取公会战日志
  Fn_MAX = 6;              // 最大的协议编号
}

service Mongo {
  rpc Get(GetCognitionLogReq) returns (GetCognitionLogRsp) {}
  rpc Put(PutCognitionLogReq) returns (PutCognitionLogRsp) {}
  rpc PutSeasonLog(cl.SeasonUserLog) returns (PutSeasonLogRsp) {}
  rpc PutGstLog(PutGSTLogReq) returns (PutGSTLogRsp) {}
  rpc GetGstLog(GSTGetLogReq) returns (GSTGetLogRsp) {}
}

// 走cognition通用接口的通关记录日志类型，不同的类型存取方式不同，数据保存时间也不同
enum CognitionType {
  CT_NONE = 0;
  CT_Normal = 1;           // 普通玩法的通关记录  如：主线、爬塔、材料本等
  CT_RoundPass = 2;        // 轮次刷新类型的通关记录  如: 世界Boss
  CT_SeasonRoundPass = 3;  // 赛季玩法，轮次刷新类型的通关记录   如：公会副本
  CT_SeasonPass = 4;       // 赛季玩法的通关记录   如：赛季主线、失序空间等
  CT_GstBossRank = 5;      // 公会战Boss攻打记录排行
  CT_GstDragonRank = 6;    // 公会战龙战攻打记录排行
  CT_BossRush = 7;         // Boss挑战通关记录
  CT_GstOreRank = 8;       // 公会战矿攻打记录排行
  CT_SeasonDoor = 9;
  CT_TowerSeason = 10;  // 百塔个人通关记录
  CT_SeasonMap = 11;
}

message GetCognitionLogReq {
  uint32 formation_id = 1;
  uint32 target_id = 2;
  uint64 uid = 3;
  uint32 page = 4;
  uint64 sid = 5;        // 玩家所在服务器ID
  uint32 round = 6;      // 轮次或者期数
  uint32 arena = 7;      // 战区ID
  uint32 season_id = 8;  // 赛季ID
  uint32 op_group = 9;   // 运营组
  CognitionType type = 10;
}

message GetCognitionLogRsp {
  uint32 ret = 1;
  repeated cl.CognitionLog logs = 2;
  uint64 uid = 3;
  int32 first_pass = 4;  // 本服首通（在logs中的index，-1代表没有）
  int32 less_power = 5;  // 最低战力（在logs中的index，-1代表没有）
  uint32 total = 6;      // 日志总数
  uint32 round = 7;      // 轮次或者期数
  uint32 arena = 8;      // 战区ID
  uint32 formation_id = 9;
  uint32 target_id = 10;
  uint32 page = 11;
  uint32 season_id = 12;  // 赛季ID
  CognitionType type = 13;
}

message PutCognitionLogReq {
  cl.CognitionLog log = 1;
  CognitionType type = 2;
}

message PutCognitionLogRsp {
  uint32 ret = 1;
  CognitionType type = 2;
}

message PutSeasonLogRsp {
  uint32 ret = 1;
  uint32 season_id = 2;
  uint64 uid = 3;
}

message PutGSTLogReq {
  cl.GSTManyLog log = 1;
  uint32 op_group = 2;  // 运营组
}

message PutGSTLogRsp {
  uint32 ret = 1;
  repeated uint64 all_log_ids = 2;
  repeated uint64 suc_log_ids = 3;
}

message GSTGetLogReq {
  cl.GSTGetLogReq req = 1;
  bytes query = 2;
  uint64 req_uid = 3;
  int64 limit = 4;
  uint32 op_group = 5;  // 运营组
}

message GSTGetLogRsp {
  uint64 req_uid = 1;
  cl.L2C_GSTGetLogData rsp = 2;
}
