syntax = "proto3";

option go_package = "app/protos/in/r2c";

import "cr.proto";

// redis <-> cross
package r2c;

enum RET {
  ERROR = 0;
  OK = 1;
}

enum ID {
  MSG_NONE = 0;
  MSG_MIN = 300000;
  MSG_MAX = 400000;
  MSG_BEGIN = 300001;
  MSG_END = 399999;
  MSG_C2R_Test = 300002;  // 测试
  MSG_R2C_Test = 300003;
  MSG_C2R_Load = 300004;  // 加载
  MSG_R2C_Load = 300005;
  MSG_C2R_Finish = 300006;  // 最后一条协议
  MSG_R2C_Finish = 300007;
  MSG_C2R_LoadActInfo = 300008;  // 拉取分组信息
  MSG_R2C_LoadActInfo = 300009;
  MSG_C2R_SaveActInfo = 300010;   // 保存分组信息
  MSG_C2R_LoadAreaInfo = 300011;  // 拉取战区信息
  MSG_R2C_LoadAreaInfo = 300012;
  MSG_C2R_SaveAreaInfo = 300013;    // 保存战区信息
  MSG_C2R_SaveCommonRank = 300014;  // 保存通用排行榜

  // 跨服战
  MSG_WRESTLE_MIN = 300100;
  MSG_C2R_WrestleSave = 300101;        // 跨服战存储
  MSG_C2R_WrestleFighterBak = 300102;  // 跨服战玩家数据备份
  MSG_C2R_WrestleLoad = 300103;        // 跨服战拉取数据
  MSG_R2C_WrestleLoad = 300104;
  MSG_C2R_WrestleLoadFighters = 300105;  // 跨服战新赛季动态分组时，加载数据
  MSG_R2C_WrestleLoadFighters = 300106;  // 跨服战新赛季动态分组时，加载数据
  MSG_C2R_WrestleClean = 300107;         // 清除跨服战分组数据
  MSG_R2C_WrestleFighterBak = 300108;
  MSG_WRESTLE_MAX = 300149;

  // 公会
  MSG_GUILD_MIN = 300200;
  MSG_C2R_GuildLoad = 300201;
  MSG_R2C_GuildLoad = 300202;
  MSG_C2R_GuildCreate = 300203;  //
  MSG_R2C_GuildCreate = 300204;
  MSG_C2R_GuildSave = 300205;
  MSG_C2R_GuildSetName = 300206;
  MSG_R2C_GuildSetName = 300207;
  MSG_C2R_GuildDeleteName = 300208;
  MSG_C2R_GuildSearch = 300209;
  MSG_R2C_GuildSearch = 300210;
  MSG_C2R_GuildDonateLogList = 300211;
  MSG_R2C_GuildDonateLogList = 300212;
  MSG_C2R_GuildLogList = 300213;
  MSG_R2C_GuildLogList = 300214;
  MSG_C2R_GuildLoadOne = 300215;
  MSG_R2C_GuildLoadOne = 300216;
  MSG_C2R_GuildLoadSome = 300217;
  MSG_R2C_GuildLoadSome = 300218;
  MSG_C2R_GuildSavePeakBakData = 300219;
  MSG_C2R_GuildSaveWorldBossBakData = 300220;
  MSG_C2R_GuildSaveSeasonArenaBakData = 300221;
  MSG_C2R_GuildUserSave = 300222;

  // 300300开始是公会副本
  MSG_C2R_GuildDungeonLogList = 300300;
  MSG_R2C_GuildDungeonLogList = 300301;
  MSG_C2R_GuildDungeonSave = 300302;
  MSG_GUILD_MAX = 300399;

  // 排行榜
  MSG_RANK_MIN = 300400;
  MSG_C2R_RankSave = 300401;  // 跨服战存储
  MSG_C2R_RankGet = 300403;   // 跨服战拉取数据
  MSG_R2C_RankGet = 300404;
  MSG_C2R_RankReset = 300405;  // 跨服战玩家数据备份
  MSG_R2C_RankReset = 300406;
  MSG_C2R_RankGetLast = 300407;  // 拉取备份排行榜
  MSG_R2C_RankGetLast = 300408;
  MSG_RANK_MAX = 300499;

  // 世界boss
  MSG_WorldBoss_MIN = 300500;
  MSG_C2R_WorldBossLoad = 300501;  // 加载worldBoss下的所有数据
  MSG_R2C_WorldBossLoad = 300502;
  MSG_C2R_WorldBossSaveRoom = 300503;             // 保存房间数据
  MSG_C2R_WorldBossRoomCleanAll = 300504;         // 清空全部房间数据
  MSG_C2R_WorldBossSaveUser = 300505;             // 保存玩家数据
  MSG_C2R_WorldBossUserCleanAll = 300506;         // 清空全部玩家数据
  MSG_C2R_WorldBossSaveBaseData = 300507;         // 保存世界boss基础数据
  MSG_C2R_WorldBossSaveUserRankBak = 300508;      // 保存玩家排行数据
  MSG_C2R_WorldBossUserRankBakCleanAll = 300509;  // 清空玩家结算排行数据
  MSG_C2R_WorldBossSaveUserToPeakArena = 300510;  // 保存噩梦等级前128名玩家数据
  MSG_C2R_WorldBossSaveUserBack = 300511;         // 存储玩家备份
  MSG_C2R_WorldBossClearUserBack = 300512;        // 清除指定BOSS类型备份
  MSG_WorldBoss_MAX = 300599;

  // 失序空间
  MSG_DisorderLand_MIN = 300600;
  MSG_C2R_DisorderLandLoad = 300601;  // 加载disorderLand下的所有数据
  MSG_R2C_DisorderLandLoad = 300602;
  MSG_C2R_DisorderLandSaveUser = 300603;      // 保存玩家排行数据
  MSG_C2R_DisorderLandUserCleanAll = 300604;  // 清空玩家结算排行数据
  MSG_C2R_DisorderLandSaveBaseData = 300605;  // 保存基础数据
  MSG_DisorderLand_MAX = 300699;

  // 巅峰竞技场
  MSG_PEAK_MIN = 300700;
  MSG_C2R_PeakLoad = 300701;  // 加载数据
  MSG_R2C_PeakLoad = 300702;
  MSG_C2R_PeakSave = 300703;         // 保存数据
  MSG_C2R_PeakClearSeason = 300704;  // 清除赛季数据
  MSG_C2R_PeakClearPhase = 300705;   // 清除周期数据
  MSG_PEAK_MAX = 300749;

  // 公会战 funcID=258
  MSG_GST_MIN = 325800;
  MSG_C2R_GSTLoad = 325801;
  MSG_R2C_GSTLoad = 325802;
  MSG_C2R_GSTClearGuild = 325803;
  MSG_R2C_GSTClearGuild = 325804;
  MSG_C2R_GSTSave = 325805;               // 保存数据
  MSG_C2R_GSTResetLRound = 325806;        // 重置小回合需要重置的数据
  MSG_C2R_GSTResetRound = 325807;         // 重置回合需要重置的数据
  MSG_C2R_GSTResetSeason = 325808;        // 重置赛季
  MSG_C2R_GSTResetGuildRankBak = 325809;  // 重置排名备份
  MSG_C2R_GSTSaveLog = 325810;            // 保存log
  MSG_C2R_GSTResetCommonBak = 325811;     // 重置通用备份
  MSG_C2R_GSTResetDragonRound = 325812;   // 重置龙战场次需要重置的数据
  MSG_C2R_GSTResetMobRank = 325813;       // 重置公会竞赛排行榜的数据
  MSG_GST_MAX = 325899;

  // 赛季竞技场 funcID=265
  MSG_SEASON_ARENA_MIN = 326500;
  MSG_C2R_SeasonArenaLoad = 326501;  // 加载数据
  MSG_R2C_SeasonArenaLoad = 326502;
  MSG_C2R_SeasonArenaSave = 326503;  // 保存数据

  MSG_C2R_HotRankLoad = 326601;  // 热度榜加载
  MSG_R2C_HotRankLoad = 326602;
  MSG_C2R_HotRankSave = 326603;  // 热度榜存储

  MSG_C2R_SeasonComplianceLoad = 326701;
  MSG_R2C_SeasonComplianceLoad = 326702;
  MSG_C2R_SeasonComplianceSave = 326703;

  MSG_C2R_SeasonMapLoad = 326801;
  MSG_R2C_SeasonMapLoad = 326802;
  MSG_C2R_SeasonMapSave = 326803;

  // 公平竞技场
  MSG_BALANCE_ARENA_MIN = 326900;
  MSG_C2R_BalanceArenaLoad = 326901;  // 加载数据
  MSG_R2C_BalanceArenaLoad = 326902;
  MSG_C2R_BalanceArenaSave = 326903;        // 保存数据
  MSG_C2R_BalanceArenaClearRound = 326904;  // 清除赛期数据
  MSG_BALANCE_ARENA_MAX = 326999;
}

message C2R_Test {
  uint32 value = 1;
}

message R2C_Test {
  uint32 value = 2;
}

message C2R_Load {}

message R2C_Load {
  uint32 ret = 1;
  map<uint32, cr.Wrestle> wrestles = 2;  // 跨服战(partition->data)
}

message C2R_Finish {
  int32 index = 1;
}

message R2C_Finish {}

message C2R_LoadActInfo {
  uint32 id = 1;
}

message R2C_LoadActInfo {
  uint32 ret = 1;
  uint32 id = 2;
  map<uint64, cr.ActPartition> parts = 3;  // 分组信息
  cr.ActBase base = 4;                     // 基础信息
}

message C2R_SaveActInfo {
  uint32 id = 1;
  repeated cr.ActPartition parts = 2;
  cr.ActBase base = 3;
}

message C2R_LoadAreaInfo {
  uint32 id = 1;
}

message R2C_LoadAreaInfo {
  uint32 ret = 1;
  uint32 id = 2;
  map<uint64, cr.ActPartition> parts = 3;  // 分组信息
}

message C2R_SaveAreaInfo {
  uint32 id = 1;
  repeated cr.ActPartition parts = 2;
}

message OpCommonRank {
  uint32 id = 1;
  repeated cr.CommonRank values = 2;
  repeated uint64 deletes = 3;
}

message C2R_SaveCommonRank {
  repeated OpCommonRank datas = 1;
  repeated uint32 deletes = 2;
}

message C2R_WrestleSave {
  uint32 partition = 1;
  cr.Matcher matcher = 2;
  repeated cr.Fighter fighters = 3;
  cr.WrestleLastSeasonTop top = 4;
  bool from_reset = 5;
  repeated uint64 deletes = 6;  // 需清除的机器人id
}

message C2R_WrestleFighterBak {
  repeated cr.FightersBak baks = 1;
}
message R2C_WrestleFighterBak {
  uint32 ret = 1;
}

message C2R_WrestleLoad {
  uint32 partition = 1;
  repeated uint64 sids = 2;
}

message R2C_WrestleLoad {
  uint32 ret = 1;
  uint32 partition = 2;
  repeated uint64 sids = 3;
  cr.Wrestle wrestle = 4;
}

message C2R_WrestleLoadFighters {
  repeated uint64 sids = 1;     // 服务器id列表
  int64 season_reset_time = 2;  // 赛季重置时间，用于与备份数据的时间做比较
}

message R2C_WrestleLoadFighters {
  uint32 ret = 1;
  repeated cr.Fighter fighters = 2;
}

message C2R_WrestleClean {
  uint32 partition = 1;    // 分组id
  bool clean_matcher = 2;  // 是否需要清除matcher数据（默认情况下，只清除fighter数据）
}

/************************  guild start ************************/
message C2R_GuildLoad {
  repeated uint64 sids = 1;
  uint32 arena_id = 2;  // 战区ID
}

message R2C_GuildLoad {
  uint32 ret = 1;
  repeated uint64 sids = 2;
  map<uint64, cr.Guild> guilds = 3;
  map<uint64, cr.GuildDungeon> dungeons = 4;
  map<uint64, cr.SidAndGidForGuildLoad> sid_and_gids = 5;
  cr.GuildDungeonReset dungeon_reset = 6;
  uint32 dungeon_season_id = 7;  // 全服通用的赛季ID
  cr.GuildPeakPhaseUserBak peak_bak = 8;
  cr.GuildWorldBossUserBak world_boss_bak = 9;
  map<uint64, cr.GuildCrossUser> users = 10;
  map<uint64, cr.GuildSeasonArenaBak> season_arena_bak = 11;
}

message CommonRankData {
  map<uint64, cr.CommonRank> data = 1;
}

message C2R_GuildCreate {
  cr.Guild guild = 1;
}

message R2C_GuildCreate {
  uint32 ret = 1;
  cr.Guild guild = 2;
}

message C2R_GuildSave {
  repeated cr.Guild changes = 1;
  repeated uint64 deletes = 2;
  map<uint64, cr.GuildLogs> guild_change_logs = 3;
  map<uint64, cr.GuildLogDeletes> guild_delete_logs = 4;
  map<uint64, cr.GuildDonateLogs> guild_donate_logs = 5;
  map<uint64, cr.GuildDonateDeletes> guild_donate_delete_logs = 6;
  repeated cr.SidAndGidForGuildLoad sid_and_gids = 7;
}

message C2R_GuildSetName {
  uint64 id = 1;
  string old_name = 2;
  string new_name = 3;
  uint32 client_msg_id = 4;  // 关联的请求id
}

message R2C_GuildSetName {
  uint32 ret = 1;
  uint64 id = 2;
  string old_name = 3;
  string new_name = 4;
  uint32 client_msg_id = 5;  // 关联的请求id
}

message C2R_GuildDeleteName {
  string del_name = 1;
}

message C2R_GuildSearch {
  uint64 sid = 1;
  uint64 id = 2;
  string name = 3;
  uint64 uid = 4;
}

message R2C_GuildSearch {
  uint32 ret = 1;
  uint64 sid = 2;
  uint64 id = 3;
  string name = 4;
  uint64 uid = 5;
  cr.Guild guild = 6;
  cr.GuildDungeon dungeon = 7;
}

message C2R_GuildLogList {
  uint64 gid = 1;
  uint64 sid = 2;
  uint64 uid = 3;
}

message R2C_GuildLogList {
  uint32 ret = 1;
  uint64 sid = 2;
  uint64 uid = 3;
  map<uint32, cr.GuildLogInfo> logs = 4;
}

message C2R_GuildDonateLogList {
  uint64 gid = 1;
  uint64 sid = 2;
  uint64 uid = 3;
}

message R2C_GuildDonateLogList {
  uint32 ret = 1;
  uint64 sid = 2;
  uint64 uid = 3;
  map<uint32, cr.GuildDonateLogInfo> logs = 4;
}

message C2R_GuildLoadOne {
  uint64 gid = 1;
  uint64 target_sid = 2;
  uint64 target_uid = 3;
}

message R2C_GuildLoadOne {
  uint32 ret = 1;
  uint64 gid = 2;
  uint64 target_sid = 3;
  uint64 target_uid = 4;
  cr.Guild guild = 5;
  cr.GuildDungeon dungeon = 6;
}

message C2R_GuildLoadSome {
  repeated uint64 gids = 1;
  uint32 client_msg_id = 2;
}

message R2C_GuildLoadSome {
  uint32 ret = 1;
  repeated uint64 gids = 2;
  uint32 client_msg_id = 3;
  map<uint64, cr.Guild> guilds = 4;
  map<uint64, cr.GuildDungeon> dungeons = 5;
}

message C2R_GuildUserSave {
  uint32 area_id = 1;  // 战区ID
  repeated cr.GuildCrossUser changes = 2;
}

message C2R_GuildSavePeakBakData {
  uint32 area_id = 1;                 // 战区ID
  cr.GuildPeakPhaseUserBak data = 2;  // 数据
}

message C2R_GuildSaveWorldBossBakData {
  uint32 area_id = 1;                 // 战区ID
  cr.GuildWorldBossUserBak data = 2;  // 数据
}

message C2R_GuildSaveSeasonArenaBakData {
  uint32 area_id = 1;               // 战区ID
  uint64 sid = 2;                   // 服务器ID
  cr.GuildSeasonArenaBak data = 3;  // 数据
}

message C2R_GuildDungeonLogList {
  uint64 gid = 1;
  uint64 sid = 2;
  uint64 uid = 3;
}

message R2C_GuildDungeonLogList {
  uint32 ret = 1;
  uint64 sid = 2;
  uint64 uid = 3;
  map<uint32, cr.GuildDungeonLog> logs = 4;
}

message C2R_GuildDungeonSave {
  uint32 arena_id = 1;
  repeated cr.GuildDungeon changes = 2;
  repeated uint64 deletes = 3;
  map<uint64, cr.GuildDungeonLogs> dungeon_change_logs = 4;
  map<uint64, cr.GuildDungeonLogDeletes> dungeon_delete_logs = 5;
  cr.GuildDungeonReset reset_data = 6;
  uint32 dungeon_season_id = 7;  // 全服通用的赛季ID
}

/************************  guild end ************************/
message C2R_RankGet {
  repeated uint32 rank_ids = 1;  // 要拉取哪些排行榜
  repeated uint64 servers = 2;   // 哪些服务器的
}

message R2C_RankGet {
  uint32 ret = 1;
  repeated uint32 rank_ids = 2;
  repeated uint64 servers = 3;
  repeated RankInfo infos = 4;
  repeated RankInfo last_infos = 5;  // 上个赛季的排名
  repeated RankReset resets = 6;
}

message RankReset {
  uint32 rank_id = 1;
  map<uint64, cr.RankReset> data = 2;
}

message RankInfo {
  uint32 rank_id = 1;
  uint64 sid = 2;
  map<uint64, cr.RankData> data = 3;
}

// 备份数据
message C2R_RankReset {
  uint32 rank_id = 1;
  uint64 sid = 2;
  uint64 reset_time = 3;
}

message R2C_RankReset {
  uint32 ret = 1;
  uint32 rank_id = 2;
  uint64 sid = 3;
  uint64 reset_time = 4;
}

message C2R_RankSave {
  repeated OpRankData datas = 1;
  bool last = 2;  // 保存上赛季排行榜的修改
}

message OpRankData {
  uint32 rank_id = 1;
  uint64 sid = 2;
  repeated cr.RankData values = 3;
  repeated uint64 deletes = 4;
}

message C2R_RankGetLast {
  uint32 rank_id = 1;           // 要拉取哪些排行榜
  repeated uint64 servers = 2;  // 哪些服务器的
}

message R2C_RankGetLast {
  uint32 ret = 1;
  uint32 rank_id = 2;
  repeated uint64 servers = 3;
  repeated RankInfo infos = 4;
}

message C2R_WorldBossLoad {
  uint32 partition_id = 1;
  repeated uint32 boss_type = 2;
}

message R2C_WorldBossLoad {
  uint32 ret = 1;
  uint32 partition_id = 2;
  cr.WorldBoss base_data = 3;
  map<uint32, cr.WorldBossRoom> rooms = 4;                          // key: 房间唯一id
  map<uint64, cr.WorldBossUser> users = 5;                          // key: 玩家id
  map<uint64, cr.WorldBossUserRankBak> user_rank_bak = 6;           // key: 玩家id
  map<uint32, cr.WorldBossUserToPeakArena> user_to_peak_arena = 7;  // key:世界boss ID
  map<uint32, cr.WorldBossTypeUsersBack> type_users = 8;            // key: boss类型
}

message C2R_WorldBossSaveRoom {
  uint32 partition_id = 1;
  repeated cr.WorldBossRoom changes = 2;
  repeated uint32 deletes = 3;
}

message C2R_WorldBossSaveUser {
  uint32 partition_id = 1;
  repeated cr.WorldBossUser changes = 2;
  repeated uint64 deletes = 3;
}

message C2R_WorldBossSaveBaseData {
  uint32 partition_id = 1;
  cr.WorldBoss base_data = 2;
}

message C2R_WorldBossSaveUserRankBak {
  uint32 partition_id = 1;
  repeated cr.WorldBossUserRankBak changes = 2;
  repeated uint64 deletes = 3;
}

message C2R_WorldBossRoomCleanAll {
  uint32 partition_id = 1;
}

message C2R_WorldBossUserCleanAll {
  uint32 partition_id = 1;
  repeated uint32 boss_type = 2;
}

message C2R_WorldBossUserRankBakCleanAll {
  uint32 partition_id = 1;
}

message C2R_WorldBossSaveUserToPeakArena {
  uint32 partition_id = 1;
  repeated cr.WorldBossUserToPeakArena changes = 2;
  repeated uint32 deletes = 3;
}

message C2R_WorldBossSaveUserBack {
  uint32 partition_id = 1;
  uint32 boss_type = 2;
  repeated cr.WorldBossUserBack user_backs = 3;
}

message C2R_WorldBossClearUserBack {
  uint32 partition_id = 1;
  uint32 boss_type = 2;
}

message C2R_DisorderLandLoad {
  uint32 partition_id = 1;
}

message R2C_DisorderLandLoad {
  uint32 ret = 1;
  uint32 partition_id = 2;
  cr.DisorderLand base_data = 3;
  map<uint64, cr.DisorderLandUser> users = 4;  // key: 玩家id
}

message C2R_DisorderLandSaveUser {
  uint32 partition_id = 1;
  repeated cr.DisorderLandUser changes = 2;
}

message C2R_DisorderLandUserCleanAll {
  uint32 partition_id = 1;
}

message C2R_DisorderLandSaveBaseData {
  uint32 partition_id = 1;
  cr.DisorderLand base_data = 2;
}

// 巅峰竞技场 - 加载数据
message C2R_PeakLoad {
  uint32 partition = 1;
}
message R2C_PeakLoad {
  uint32 ret = 1;
  uint32 partition = 2;
  cr.Peak peak = 3;
  map<uint32, cr.PeakMatch> matches = 4;
  map<uint64, cr.PeakFighter> fighters = 5;
}

// 巅峰竞技场 - 保存数据
message C2R_PeakSave {
  uint32 partition = 1;
  cr.Peak peak = 2;
  repeated cr.PeakMatch matches = 3;
  repeated cr.PeakFighter fighters = 4;
}

// 巅峰竞技场 - 赛季清除数据
message C2R_PeakClearSeason {
  uint32 partition = 1;
}

// 巅峰竞技场 - 小周期清除数据
message C2R_PeakClearPhase {
  uint32 partition = 1;
}

// 公会战-----------------------------------------------------------------------------

// 加载主数据
message C2R_GSTLoad {
  uint32 partition = 1;
}

message R2C_GSTLoad {
  uint32 ret = 1;
  cr.GSTManager mgr = 2;
  map<uint64, cr.GSTGuildManager> guild_mgr = 3;
  map<uint64, cr.GSTGuildUserManager> guild_user_mgr = 4;
  map<uint64, cr.GSTGroupManager> group_mgr = 5;
  map<uint64, cr.GSTGuildLogManager> guild_log_mgr = 6;
  map<uint64, cr.GSTGuildRankBak> guild_rank_bak = 7;
  cr.GSTDragonGlobal dragon_global = 8;
  map<uint64, cr.GSTDragonManager> dragon_guild = 9;
  map<uint64, cr.GSTCommonBak> common_bak = 10;
  map<uint64, cr.GSTMobRankManager> guild_mob_rank = 11;
}

// 删除指定公会相关数据
message C2R_GSTClearGuild {
  uint32 partition = 1;
  uint32 season_id = 2;
  uint64 guild_id = 3;
}

message R2C_GSTClearGuild {
  uint32 ret = 1;
}

// 保存数据
message C2R_GSTSave {
  uint32 partition = 1;
  uint32 season_id = 2;
  cr.GSTManager mgr = 3;
  repeated cr.GSTGuildManager guild_mgr = 4;
  repeated cr.GSTGuildUserManager guild_user_mgr = 5;
  repeated cr.GSTGroupManager group_mgr = 6;
  repeated cr.GSTGuildRankBak guild_rank_bak = 7;
  cr.GSTDragonGlobal dragon_global = 8;
  repeated cr.GSTDragonManager dragon_guild = 9;
  repeated cr.GSTCommonBak common_bak = 10;
  uint32 guild_mob_round = 11;
  repeated cr.GSTMobRankManager guild_mob_rank = 12;
}

message C2R_GSTResetLRound {
  uint32 partition = 1;
  uint32 season_id = 2;
}

message C2R_GSTResetRound {
  uint32 partition = 1;
  uint32 season_id = 2;
}

message C2R_GSTResetSeason {
  uint32 partition = 1;
  uint32 season_id = 2;
}

message C2R_GSTResetGuildRankBak {
  uint32 partition = 1;
}

message C2R_GSTResetCommonBak {
  uint32 partition = 1;
  bool reset_dragon = 2;     // 是否重置龙战备份
  bool reset_challenge = 3;  // 是否重置新擂台赛
}

message C2R_GSTResetDragonRound {
  uint32 partition = 1;
  uint32 season_id = 2;
}

message C2R_GSTSaveLog {
  uint32 partition = 1;
  repeated cr.GSTGuildLogManager guild_log_mgr_update = 2;
  repeated uint64 guild_log_mgr_del = 3;
}

message C2R_SeasonArenaLoad {
  uint32 partition = 1;
}

message R2C_SeasonArenaLoad {
  uint32 ret = 1;
  cr.CrossSeasonArenaSta season_arena = 2;
  map<uint64, cr.CrossSeasonArenaUser> users = 3;
  cr.CrossSeasonArenaFame fame = 4;
  map<uint64, cr.CrossSeasonArenaAwardServerBak> server_back = 5;
}

message C2R_SeasonArenaSave {
  uint32 partition = 1;
  cr.CrossSeasonArenaSta season_arena = 2;
  repeated cr.CrossSeasonArenaUser users = 3;
  cr.CrossSeasonArenaFame fame = 4;
  repeated cr.CrossSeasonArenaAwardServerBak server_back = 5;
  repeated uint64 user_del = 6;
  bool clear_server_bak = 7;
}

message C2R_HotRankLoad {
  uint32 partition = 1;
}

message R2C_HotRankLoad {
  uint32 ret = 1;
  cr.CrossHotRankCollectionLogFinishSid finish_sid = 2;
  map<uint32, cr.CrossHotRankCollectionLog> collection_log = 3;
  map<uint32, cr.CrossHotRank> cross_hot_rank = 4;
  map<uint64, cr.TalentTreeHot> talent_tree_hot = 5;
}

message C2R_HotRankSave {
  uint32 partition = 1;
  cr.CrossHotRankCollectionLogFinishSid finish_sid = 2;
  bool clear_collection_log = 3;
  repeated cr.CrossHotRankCollectionLog save_collection_log = 4;
  repeated cr.CrossHotRank cross_hot_rank = 5;
  repeated cr.TalentTreeHot talent_tree = 6;
}

message C2R_GSTResetMobRank {
  uint32 partition = 1;
  uint32 mob_round = 2;
}

message C2R_SeasonComplianceLoad {
  uint32 partition = 1;
  repeated uint32 rank_ids = 2;
}

message R2C_SeasonComplianceLoad {
  uint32 ret = 1;
  uint32 partition = 2;
  repeated uint32 rank_ids = 3;
  cr.SeasonComplianceRankReset rankReset = 4;
  repeated SeasonComplianceTotalRankData ranks = 5;
}

message SeasonComplianceTotalRankData {
  map<uint64, cr.SeasonComplianceRankData> rank_data = 1;
}

message C2R_SeasonComplianceSave {
  uint32 partition = 1;
  repeated cr.SeasonComplianceRankData change_data = 2;
  map<uint32, SeasonComplianceDeleteUsers> delete_users = 3;
  cr.SeasonComplianceRankReset stage = 4;
  repeated uint32 clear_rank = 5;
}

message SeasonComplianceDeleteUsers {
  repeated uint64 users = 1;
}

message C2R_SeasonMapLoad {
  uint32 partition = 1;
}

message R2C_SeasonMapLoad {
  uint32 ret = 1;
  cr.SeasonMapManager mgr = 2;
}

message C2R_SeasonMapSave {
  uint32 partition = 1;
  cr.SeasonMapManager mgr = 2;
}

// 公平竞技场
message C2R_BalanceArenaLoad {
  uint32 partition = 1;
}

message R2C_BalanceArenaLoad {
  uint32 ret = 1;
  uint32 partition = 2;
  cr.BalanceArena balance_arena = 3;              // 数据
  cr.BalanceArenaState state = 4;                 // 状态
  map<uint64, cr.BalanceArenaUser> users = 5;     // 选手数据
  map<uint32, cr.BalanceArenaMatch> matches = 6;  // 比赛
}

message C2R_BalanceArenaSave {
  uint32 partition = 1;
  cr.BalanceArena balance_arena = 2;
  cr.BalanceArenaState state = 3;
  repeated cr.BalanceArenaUser users = 4;
  repeated cr.BalanceArenaMatch matches = 5;
}

message C2R_BalanceArenaClearRound {
  uint32 partition = 1;
}
