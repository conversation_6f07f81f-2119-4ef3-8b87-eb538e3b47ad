syntax = "proto3";

import "config.proto";

option go_package = "app/protos/in/cm2c";

package cm2c;

enum ID {
  MSG_NONE = 0;
  MSG_MIN = 400000;
  MSG_MAX = 500000;
  MSG_BEGIN = 400001;
  MSG_END = 499999;

  // 服务器与服务器之间使用
  MSG_M2C_SayHi = 400002;
  MSG_C2M_SayHi = 400003;
  MSG_M2C_KeepAlive = 400004;
  MSG_C2M_KeepAlive = 400005;

  //
  MSG_M2C_CrossNodeInit = 400100;  // 创建分区
  MSG_C2M_CrossNodeInit = 400101;
  MSG_M2C_CrossNodeClose = 400102;  // 关闭分区
  MSG_C2M_CrossNodeClose = 400103;
  MSG_M2C_CrossNodeMasterReset = 400104;  // 通知主分区重置
  MSG_C2M_CrossNodeMasterReset = 400105;
  MSG_C2M_CrossNodeReset = 400106;              // 分区重置
  MSG_M2C_CrossNodeCheckCanResetPart = 400107;  // 检查跨服节点是否可以重置分区
  MSG_C2M_CrossNodeCheckCanResetPart = 400108;
  MSG_C2C_CrossNodeTransform = 400109;               // 转发协议
  MSG_M2C_CrossNodeMasterUpdateNormalArea = 400110;  // 更新分区信息
  MSG_C2M_CrossNodeMasterUpdateNormalArea = 400111;
}

message M2C_SayHi {
  uint64 id = 1;
  repeated uint64 current = 2;
  repeated uint64 del = 3;
}

message C2M_SayHi {}

message M2C_KeepAlive {
  string extra = 1;  // json
}

message C2M_KeepAlive {
  string extra = 1;  // json
}

// 跨服通知子节点启动
message M2C_CrossNodeInit {
  uint32 act_id = 1;
  uint32 partition = 2;
  uint64 reset_time = 3;
  repeated uint64 servers = 4;
  uint32 session = 5;
  config.CrossActDb db = 6;
  map<uint64, uint32> normal_area = 7;  // 普通分区信息
  uint32 op_group = 8;                  // 运营组
}

message C2M_CrossNodeInit {
  uint32 ret = 1;
  uint32 act_id = 2;
  uint32 partition = 3;
}

// 跨服通知子节点关闭
message M2C_CrossNodeClose {
  uint32 act_id = 1;
  uint32 partition = 2;
  bool is_del = 3;
}

message C2M_CrossNodeClose {
  uint32 ret = 1;
  M2C_CrossNodeClose msg = 2;
}

// 跨服通知子节点更新分区信息
message M2C_CrossNodeMasterReset {
  uint32 act_id = 1;
  uint32 partition = 2;
  uint64 reset_time = 3;
  uint32 session = 4;
  repeated uint64 servers = 5;
}

message C2M_CrossNodeMastereReset {
  uint32 ret = 1;
  M2C_CrossNodeMasterReset msg = 2;
}

message M2C_CrossNodeMasterUpdateNormalArea {
  uint32 act_id = 1;
  uint32 partition = 2;
  map<uint64, uint32> normal_area = 3;  // 普通分区信息
}

message C2M_CrossNodeMastereUpdateNormalArea {
  uint32 ret = 1;
  M2C_CrossNodeMasterUpdateNormalArea msg = 2;
}

// 子节点通知跨服重置完毕
message C2M_CrossNodeChildReset {
  uint32 ret = 1;
  uint64 reset_time = 2;
}

// 检查跨服节点是否可以重置分区
message M2C_CrossNodeCheckCanResetPart {
  uint32 act_id = 1;
  uint32 partition = 2;
  uint64 reset_time = 3;
}

message C2M_CrossNodeCheckCanResetPart {
  uint32 ret = 1;
  uint32 act_id = 2;
  uint32 partition = 3;
  uint64 reset_time = 4;
  bool can_reset = 5;  // 是否可以重置
}

message C2C_CrossNodeTransform {
  uint32 from_act_id = 1;
  uint32 from_partition = 2;
  uint32 act_id = 3;
  uint32 partition = 4;
  uint32 proto_id = 5;
  bytes data = 6;  // 具体数据
}
