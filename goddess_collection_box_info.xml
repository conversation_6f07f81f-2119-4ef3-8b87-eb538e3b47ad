<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:id type1=int:宝箱类型1 param1=int:参数1 res_type1=int:加成资源类型1 res_value_1=int:加成资源1 res_count_1=int:加成资源值1 type2=int:宝箱类型2 param2=int:参数2 res_type2=int:加成资源类型2 res_value_2=int:加成资源2 res_count_2=int:加成资源值2 type3=int:宝箱类型3 param3=int:参数3 res_type3=int:加成资源类型3 res_value_3=int:加成资源3 res_count_3=int:加成资源值3 -->

<root>
    <data id="1001" type1="1" param1="60" res_type1="4" res_value_1="10053" res_count_1="150" type2="0" param2="0" res_type2="0" res_value_2="0" res_count_2="0" type3="0" param3="0" res_type3="0" res_value_3="0" res_count_3="0" />
    <data id="1002" type1="1" param1="60" res_type1="4" res_value_1="10053" res_count_1="150" type2="2" param2="60" res_type2="4" res_value_2="10054" res_count_2="600" type3="0" param3="0" res_type3="0" res_value_3="0" res_count_3="0" />
    <data id="1003" type1="1" param1="60" res_type1="4" res_value_1="10053" res_count_1="150" type2="2" param2="60" res_type2="4" res_value_2="10054" res_count_2="600" type3="2" param3="100" res_type3="4" res_value_3="10055" res_count_3="150" />
</root>
