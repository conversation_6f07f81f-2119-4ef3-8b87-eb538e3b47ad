<?xml version="1.0" encoding="UTF-8"?>
<!--stage=int:当前突破 need_lv=int:需求英雄等级 type_1=int:消耗道具类型 value_1=int:消耗道具ID count_1=int:消耗道具数量 type_2=int:消耗道具类型 value_2=int:消耗道具ID count_2=int:消耗道具数量 -->

<root>
    <data stage="0" need_lv="10" type_1="4" value_1="10003" count_1="50" type_2="4" value_2="10014" count_2="0" />
    <data stage="1" need_lv="10" type_1="4" value_1="10003" count_1="80" type_2="4" value_2="10014" count_2="0" />
    <data stage="2" need_lv="10" type_1="4" value_1="10003" count_1="120" type_2="4" value_2="10014" count_2="20" />
    <data stage="3" need_lv="25" type_1="4" value_1="10003" count_1="200" type_2="4" value_2="10014" count_2="0" />
    <data stage="4" need_lv="25" type_1="4" value_1="10003" count_1="350" type_2="4" value_2="10014" count_2="0" />
    <data stage="5" need_lv="25" type_1="4" value_1="10003" count_1="500" type_2="4" value_2="10014" count_2="60" />
    <data stage="6" need_lv="30" type_1="4" value_1="10003" count_1="650" type_2="4" value_2="10014" count_2="0" />
    <data stage="7" need_lv="35" type_1="4" value_1="10003" count_1="800" type_2="4" value_2="10014" count_2="0" />
    <data stage="8" need_lv="35" type_1="4" value_1="10003" count_1="1000" type_2="4" value_2="10014" count_2="180" />
    <data stage="9" need_lv="40" type_1="4" value_1="10003" count_1="1200" type_2="4" value_2="10014" count_2="0" />
    <data stage="10" need_lv="40" type_1="4" value_1="10003" count_1="1450" type_2="4" value_2="10014" count_2="0" />
    <data stage="11" need_lv="45" type_1="4" value_1="10003" count_1="1700" type_2="4" value_2="10014" count_2="300" />
    <data stage="12" need_lv="45" type_1="4" value_1="10003" count_1="2000" type_2="4" value_2="10014" count_2="0" />
    <data stage="13" need_lv="50" type_1="4" value_1="10003" count_1="2300" type_2="4" value_2="10014" count_2="0" />
    <data stage="14" need_lv="50" type_1="4" value_1="10003" count_1="2700" type_2="4" value_2="10014" count_2="360" />
    <data stage="15" need_lv="55" type_1="4" value_1="10003" count_1="3100" type_2="4" value_2="10014" count_2="0" />
    <data stage="16" need_lv="55" type_1="4" value_1="10003" count_1="3600" type_2="4" value_2="10014" count_2="0" />
    <data stage="17" need_lv="60" type_1="4" value_1="10003" count_1="4100" type_2="4" value_2="10014" count_2="420" />
    <data stage="18" need_lv="60" type_1="4" value_1="10003" count_1="4700" type_2="4" value_2="10014" count_2="100" />
    <data stage="19" need_lv="65" type_1="4" value_1="10003" count_1="5300" type_2="4" value_2="10014" count_2="100" />
    <data stage="20" need_lv="65" type_1="4" value_1="10003" count_1="6000" type_2="4" value_2="10014" count_2="480" />
    <data stage="21" need_lv="70" type_1="4" value_1="10003" count_1="6700" type_2="4" value_2="10014" count_2="100" />
    <data stage="22" need_lv="70" type_1="4" value_1="10003" count_1="7400" type_2="4" value_2="10014" count_2="100" />
    <data stage="23" need_lv="75" type_1="4" value_1="10003" count_1="8200" type_2="4" value_2="10014" count_2="540" />
    <data stage="24" need_lv="75" type_1="4" value_1="10003" count_1="9000" type_2="4" value_2="10014" count_2="100" />
    <data stage="25" need_lv="75" type_1="4" value_1="10003" count_1="10000" type_2="4" value_2="10014" count_2="100" />
    <data stage="26" need_lv="75" type_1="4" value_1="10003" count_1="11000" type_2="4" value_2="10014" count_2="600" />
    <data stage="27" need_lv="85" type_1="4" value_1="10003" count_1="12000" type_2="4" value_2="10014" count_2="100" />
    <data stage="28" need_lv="85" type_1="4" value_1="10003" count_1="13000" type_2="4" value_2="10014" count_2="100" />
    <data stage="29" need_lv="90" type_1="4" value_1="10003" count_1="14000" type_2="4" value_2="10014" count_2="700" />
    <data stage="30" need_lv="90" type_1="4" value_1="10003" count_1="15500" type_2="4" value_2="10014" count_2="700" />
    <data stage="31" need_lv="90" type_1="4" value_1="10003" count_1="17000" type_2="4" value_2="10014" count_2="1000" />
    <data stage="32" need_lv="90" type_1="4" value_1="10003" count_1="19000" type_2="4" value_2="10014" count_2="1400" />
    <data stage="33" need_lv="90" type_1="4" value_1="10003" count_1="21000" type_2="4" value_2="10014" count_2="1800" />
    <data stage="34" need_lv="90" type_1="4" value_1="10003" count_1="23500" type_2="4" value_2="10014" count_2="2200" />
    <data stage="35" need_lv="90" type_1="4" value_1="10003" count_1="26000" type_2="4" value_2="10014" count_2="2600" />
    <data stage="36" need_lv="90" type_1="4" value_1="10003" count_1="29000" type_2="4" value_2="10014" count_2="3000" />
    <data stage="37" need_lv="90" type_1="4" value_1="10003" count_1="32000" type_2="4" value_2="10014" count_2="3400" />
    <data stage="38" need_lv="90" type_1="4" value_1="10003" count_1="35500" type_2="4" value_2="10014" count_2="3800" />
    <data stage="39" need_lv="90" type_1="4" value_1="10003" count_1="39000" type_2="4" value_2="10014" count_2="4200" />
    <data stage="40" need_lv="90" type_1="4" value_1="10003" count_1="43000" type_2="4" value_2="10014" count_2="4600" />
    <data stage="41" need_lv="90" type_1="4" value_1="10003" count_1="47000" type_2="4" value_2="10014" count_2="5000" />
    <data stage="42" need_lv="90" type_1="4" value_1="10003" count_1="51500" type_2="4" value_2="10014" count_2="5500" />
    <data stage="43" need_lv="90" type_1="4" value_1="10003" count_1="56000" type_2="4" value_2="10014" count_2="6000" />
    <data stage="44" need_lv="90" type_1="4" value_1="10003" count_1="61000" type_2="4" value_2="10014" count_2="6500" />
    <data stage="45" need_lv="90" type_1="4" value_1="10003" count_1="66000" type_2="4" value_2="10014" count_2="7000" />
    <data stage="46" need_lv="90" type_1="4" value_1="10003" count_1="71500" type_2="4" value_2="10014" count_2="7500" />
    <data stage="47" need_lv="90" type_1="4" value_1="10003" count_1="77000" type_2="4" value_2="10014" count_2="8000" />
    <data stage="48" need_lv="90" type_1="4" value_1="10003" count_1="83000" type_2="4" value_2="10014" count_2="8500" />
    <data stage="49" need_lv="90" type_1="4" value_1="10003" count_1="89000" type_2="4" value_2="10014" count_2="9000" />
    <data stage="50" need_lv="999" type_1="0" value_1="0" count_1="0" type_2="0" value_2="0" count_2="0" />
</root>
