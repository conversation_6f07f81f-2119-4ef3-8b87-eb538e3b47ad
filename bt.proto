syntax = "proto3";

option go_package = "app/protos/out/bt";

package bt;

message MultipleTeamsReport {
  repeated Report reports = 1;
  string id = 2;              // 战报id
  bytes client_data = 3;      // 序列化后的前端透传数据
  bool pvp = 4;               // 是否是pvp
  uint32 extra_id = 5;        // 额外id信息
  bool win = 6;               // 是否胜利
  uint32 formation_id = 7;    // 进攻方阵容id
  uint64 version = 8;         // 版本号
  uint32 report_version = 9;  // 自定义战报版本号
}

message Report {
  repeated Member attackers = 1;                            // 攻击方信息
  repeated Member defensers = 2;                            // 防守方信息
  Artifact attack_artifact = 3;                             // 攻击方神器信息
  Artifact defense_artifact = 4;                            // 防守方神器信息
  TeamMember attack_team_member = 5;                        // 攻击方全局对象
  TeamMember defense_team_member = 6;                       // 防守方全局对象
  repeated LinkInfo attack_link_info = 7;                   // 攻击方激活的联结
  repeated LinkInfo defense_link_info = 8;                  // 防守方激活的联结
  repeated Round rounds = 9;                                // 战斗回合
  repeated Member attackers_final = 10;                     // 结束时攻击方信息
  repeated Member defensers_final = 11;                     // 结束时防守方信息
  repeated Sta stas = 12;                                   // 战斗统计结果
  ReportInfo report_info = 13;                              // 战报信息
  repeated LinkInfo attack_artifact_link_info = 14;         // 攻击方神器激活的联结
  repeated LinkInfo defense_artifact_link_info = 15;        // 防守方神器激活的联结
  Rite attack_rite = 16;                                    // 攻击方仪式信息
  Rite defense_rite = 17;                                   // 防守方仪式信息
  SeasonTrain attack_season_train = 18;                     // 攻击方赛季养成信息
  SeasonTrain defense_season_train = 19;                    // 防守方赛季养成信息
  repeated uint32 attack_remains = 20;                      // 进攻方遗物id列表
  repeated uint32 defense_remains = 21;                     // 防守方遗物id列表
  repeated uint32 attack_season_link_actived_heroes = 22;   // 进攻方已激活赛季羁绊的英雄列表
  repeated uint32 defense_season_link_actived_heroes = 23;  // 防守方已激活赛季羁绊的英雄列表
  uint32 season_id = 24;                                    // 赛季id
  repeated RemainStarInfo attack_remain_star_info = 25;     // 进攻方遗物星级信息
  repeated RemainStarInfo defense_remain_star_info = 26;    // 防守方遗物星级信息
  TalentTree attack_talent_tree = 27;                       // 进攻方天赋树信息
  TalentTree defense_talent_tree = 28;                      // 防守方天赋树信息
  repeated Pokemon attack_pokemons = 29;                    // 宠物信息
  repeated Pokemon defense_pokemons = 30;
}

message Pokemon {
  uint32 id = 1;  // 战斗中成员唯一id
  uint32 sys_id = 2;
  uint32 star = 3;
  uint32 pos = 4;
}

message Member {
  uint32 id = 1;  // 战斗中成员唯一id
  MemberInfo info = 2;
  int64 now_hp = 3;
  int64 max_hp = 4;         // 实际最大血量
  repeated int64 attr = 5;  // 初始属性
  int64 show_max_hp = 6;    // 显示最大血量
  DebugInfo debug_info = 128;
}

message MemberInfo {
  uint64 uniq_id = 1;  // 外面的唯一id,玩家玩家hero id 怪物就是系统id
  uint32 sys_id = 2;
  uint32 stage = 3;  // stage
  uint32 star = 4;   // 星数
  uint32 pos = 5;
  uint32 dress = 6;                        // 时装效果
  uint32 level = 7;                        // 等级
  bool is_monster = 8;                     // true代表怪物
  uint64 power = 9;                        // 英雄战力
  repeated uint32 gem = 10;                // 宝石
  repeated uint32 emblem_id = 11;          // 穿戴的纹章系统id
  repeated uint64 raise_ps = 12;           // 养成被动
  uint32 awaken_level = 13;                // 觉醒等级
  uint32 template_id = 14;                 // 对应的怪物模版ID。 使用：比如召唤亡灵，会有怪物模版用来配置透明度。
  map<uint32, uint32> selected_link = 15;  // 全能英雄所选可变羁绊 羁绊类型=>羁绊id (选自动时删除此项)
  uint32 caller_dress = 16;                // 召唤者的时装
  uint32 rare = 17;                        // 品级
}

message ExtraResult {
  uint32 kill_num = 1;                // 击杀需要击杀的数量
  uint32 round = 2;                   // 回合数
  uint32 dead_num = 3;                // 我方阵亡人数
  uint32 left_hp_per = 4;             // 我方存活人数总血量万分比
  uint32 alive_num = 5;               // 我方存活人数
  uint64 max_round_hurt = 6;          // 单回合最高累计伤害，包括溢出的
  map<uint32, uint32> link_info = 7;  // 我方拥有羁绊信息
  uint32 active_link_num = 8;         // 我方实际激活羁绊数
}

message Sta {
  uint32 mid = 1;
  uint64 hurt = 2;             // 总伤害
  uint64 cure = 3;             // 总治疗
  uint64 be_hurt = 4;          // 承受伤害
  uint64 corruption_hurt = 5;  // 将羁绊buff伤害数据从英雄身上剥离 - 由腐蚀造成的伤害
  uint64 reflect_hurt = 6;     // 将羁绊buff伤害数据从英雄身上剥离 - 由反弹造成的伤害
  uint32 collect_mid = 7;      // 统计数据需要汇总到的目标memberID（比如召唤物的，要汇总到召唤者身上）
}

message Artifact {
  uint32 id = 1;
  repeated ArtifactInfo infos = 2;
  uint32 max_energy = 3;     // 总能量
  uint32 energy = 4;         // 初始能量
  DebugInfo debug_info = 5;  // 调试信息
}

message RiteGrid {
  uint32 pos = 1;
  uint32 mark_id = 2;
  uint32 power_id = 3;
}

message Rite {
  uint32 id = 1;
  uint32 rare = 2;
  uint32 restrict_state = 3;     // 克制状态（0-无克制 1-克制对手 2-被对手克制）
  repeated RiteGrid grids = 11;  // 格子列表
}

// 赛季养成信息
message SeasonTrain {
  uint32 id = 1;     // 养成玩法id
  uint32 level = 2;  // 养成等级
}

message ArtifactInfo {
  uint32 pos = 1;          // 位置 1,2,3
  uint32 sys_id = 2;       // 神器的系统id
  uint32 star = 3;         // 神器星级
  uint32 strength_lv = 4;  // 神器强化等级
  uint32 forge_lv = 5;     // 铸造等级
  uint32 id = 6;           // 唯一id
  uint32 skill = 128;      // 替换的skill 测试用
}

message ReportInfo {
  int64 attack_power = 1;       // 进攻方战力
  int64 defense_power = 2;      // 防守方战力
  uint32 max_round = 3;         // 最大回合数
  uint32 monster_group_id = 4;  // 如果对面是怪物
  bool notFinish = 5;           // 是否不结束
  uint64 total_hurt_show = 6;   // 战斗总伤害
  string attack_name = 7;
  string defense_name = 8;
  bool win = 9;                   // 攻击方是否胜利
  ExtraResult extra_result = 10;  // 额外战斗信息
  uint32 attack_base_id = 11;     // 头像
  uint32 defense_base_id = 12;
  uint32 attack_season_lv = 13;  // 赛季等级
  uint32 defense_season_lv = 14;
  uint32 attack_team_index = 15;  // 攻击方队伍序号，从0开始
  uint32 defense_team_index = 16;
  uint32 attack_title_id = 17;  // 称号
  uint32 defense_title_id = 18;
}

message TeamMember {
  uint32 id = 1;                 // 唯一id, 攻击方为7，防守方为107
  repeated uint64 raise_ps = 2;  // 养成被动
  DebugInfo debug_info = 128;    // 调试信息
}

message LinkInfo {
  uint32 link_id = 1;
  uint32 link_num = 2;
  repeated uint32 hero_id = 3;
}

message Round {
  repeated Action actions = 1;  // 回合内的行动
}

message Action {
  uint32 attack = 1;                // 当前行动的人
  repeated BaseEffect effects = 2;  // 当前action内的所有行为
}

message BaseEffect {
  BaseEffectInfo info = 1;
  HurtEffect hurt = 2;                                          // 伤害
  CureEffect cure = 3;                                          // 治疗
  AddBuffEffect add_buff = 4;                                   // 加buff
  DelBuffEffect del_buff = 5;                                   // 删buff
  UpdateBuffEffect update_buff = 6;                             // 更新buff
  ArtifactEnergyChangeEffect artifact_energy_change = 7;        // 神器能量变化
  ArtifactCdChangeEffect artifact_cd_change = 8;                // 神器cd变化
  SycLinkShow link_show = 9;                                    // 同步联结信息
  CallEffect call = 10;                                         // 召唤
  ChangeAttr change_attr = 11;                                  // 修改属性
  ChangeMaxHp change_max_hp = 12;                               // 最大血量改变
  SeasonLinkEnergyChangeEffect season_link_energy_change = 13;  // 赛季羁绊能量变化
  AccumulateHurtChangeEffect accumulate_hurt_change = 14;       // 积攒伤害值变化(s4神器)
  SwapPosEffect swap_pos = 15;                                  // 交换位置
  ChangeSkillAttackDefenser change_skill_attack_defenser = 16;  // 改变技能攻击目标
  string debug = 128;                                           // debug用
  repeated DebugFormula debug_formulas = 129;                   // 公式情况
}

message ChangeSkillAttackDefenser {
  uint32 defenser = 1;
}

message BaseEffectInfo {
  uint32 attack = 1;
  uint32 defense = 2;
  SourceType source_type = 3;         // 来源类型
  repeated uint32 source_params = 4;  // 来源的参数(弃用)
  EffectType effect_type = 5;         // 效果类型
  // repeated fixed32 source_params1 = 6;  //来源的参数
  DebugInfo attack_debug_info = 128;
  DebugInfo defense_debug_info = 129;
}

enum EffectType {
  EFFECT_HURT = 0;
  EFFECT_CURE = 1;
  EFFECT_ADD_BUFF = 2;
  EFFECT_DEL_BUFF = 3;
  EFFECT_UPDATE_BUFF = 4;
  EFFECT_ARTIFACT_ENERGY_CHANGE = 5;
  EFFECT_ARTIFACT_CD_CHANGE = 6;
  EFFECT_LINK_SHOW = 7;
  EFFECT_STUNED = 8;
  EFFECT_TRIGGER_PASSIVE = 9;
  EFFECT_DEBUG = 10;
  EFFECT_CALL = 11;
  EFFECT_CHANGE_ATTR = 12;
  EFFECT_CHANGE_MAX_HP = 13;
  EFFECT_SEASON_LINK_ENERGY_CHANGE = 14;     // 赛季羁绊信仰点
  EFFECT_ACCUMULATE_HURT_CHANGE = 15;        // 积攒伤害值变化(s4神器)
  EFFECT_SWAP_POS = 16;                      // 交换位置
  EFFECT_CHANGE_SKILL_ATTACK_DEFENSER = 17;  // 改变技能攻击目标
}

message SycLinkShow {
  repeated LinkShow attack = 1;
  repeated LinkShow defense = 2;
}

message LinkShow {
  uint32 show_type = 1;
  int64 show_value = 2;
}

enum SourceType {
  SOURCE_PASSIVE = 0;       // 被动技能 参数1:被动技能id
  SOURCE_ACTIVE = 1;        // 主动技能  参数1:主动技能id 参数2:技能的stage
  SOURCE_BUFF = 2;          // buff    参数1:buffID
  SOURCE_ACTIVE_COMBO = 3;  // 主动技能(连击) 参数同主动技能
}

message HurtEffect {
  uint64 hurt = 1;       // 伤害数值
  uint64 real_hurt = 2;  // 真实减血伤害
  int64 hp = 3;          // 剩余血量
  FactorType factor = 4;
  uint64 shield = 5;                           // 护盾吸收的值
  uint64 armor = 6;                            // 守护铠吸收的值
  SpecialType hurt_type = 7;                   // 伤害类型
  repeated MemberDeadInfo dead_info = 8;       // 死亡状态
  uint32 run_count = 9;                        // 执行次数, 默认0, 仅大于0时才走合并连击效果
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

enum FactorType {
  FactorNone = 0;
  FactorDodge = 1;   // 闪避
  FactorCrit = 2;    // 暴击
  FactorDodge2 = 3;  // 闪避2
}

enum SpecialType {
  SpecialTypeNone = 0;
  HurtTypeActive = 1;
  HurtTypeNormal = 2;
  HurtTypePassive = 3;
  HurtTypeBuff = 4;
  CureTypeSuck = 5;                      // 吸血
  HurtTypeReflect = 6;                   // 反弹
  HurtTypeDead = 7;                      // 技能攻击者死亡伤害
  HurtTypeDoKill = 8;                    // 斩杀
  HurtTypeTransfer = 9;                  // 传递类伤害
  HurtTypeHurtSelf = 10;                 // 自残
  CureTypeResurrect = 11;                // 复活
  CureTypeSteal = 12;                    // 偷治疗
  HurtTypeFull = 13;                     // 全额伤害
  HurtTypeDevour = 14;                   // 吞噬
  HurtTypeBuffShare = 15;                // 分摊积攒的伤害，s4神器技能
  HurtTypeKillNotDead = 16;              // 非死亡类击杀(无视锁血)
  HurtTypeKillIgnoreLockBlood = 17;      // 死亡类击杀(无视锁血)
  HurtTypeHurtSelfDamageToTrigger = 18;  // 特殊自残，（伤害算到触发者身上）
}

message CureEffect {
  uint64 hurt = 1;  // 治疗数值
  FactorType factor = 2;
  int64 hp = 3;
  SpecialType cure_type = 4;
  uint32 run_count = 5;                        // 执行次数, 默认0, 仅大于0时才走合并连击效果
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message AddBuffEffect {
  uint32 id = 1;                               // buff自增id
  uint32 sys_id = 2;                           // buff系统Id
  uint32 round = 3;                            // 剩余回合
  uint64 value = 4;                            // 代表新加buff获得的护盾的值
  uint32 layer = 5;                            // 层数
  uint64 armor_value = 6;                      // 代表新加buff获得的守护铠的值，总量
  uint64 left_armor_value = 7;                 // 守护铠当前的值
  uint32 ret = 8;                              // 0代表成功 1 代表免疫 2 代表抵抗
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message DelBuffEffect {
  uint32 id = 1;
  uint64 shield = 2;                           // 清除的护盾值
  uint64 armor = 3;                            // 清除的守护铠的值
  uint32 sys_id = 4;                           // buff系统Id
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message UpdateBuffEffect {
  uint32 id = 1;                               // buff自增id,应该是以存在的一个buff
  uint32 round = 2;                            // 剩余回合
  uint32 layer = 3;                            // 层数
  uint64 value = 4;                            // 护盾值更新
  uint64 armor = 5;                            // 守护铠值更新
  uint32 sys_id = 6;                           // buff系统Id
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message ArtifactCdChangeEffect {
  uint32 pos = 1;                              // 神器位置
  uint32 round = 2;                            // 更新后可执行的回合
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message ArtifactEnergyChangeEffect {
  uint32 energyReduction = 1;                  // 能量减少
  uint32 energyIncrease = 2;                   // 能量增加
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message SeasonLinkEnergyChangeEffect {
  uint32 energyReduction = 1;                  // 能量减少
  uint32 energyIncrease = 2;                   // 能量增加
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message AccumulateHurtChangeEffect {
  uint64 hurtReduction = 1;                    // 伤害值减少
  uint64 hurtIncrease = 2;                     // 伤害值增加
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message SwapPosEffect {
  uint32 oldPos = 1;                           // 换位前的位置
  uint32 newPos = 2;                           // 换位后的位置
  uint32 count = 3;                            // 换位次数
  repeated DebugFormula debug_formulas = 128;  // 公式情况
}

message DebugInfo {
  uint32 id = 1;                        // id
  map<uint32, int64> attrs = 2;         // 属性
  repeated BuffDebugInfo buffs = 3;     // buff
  repeated uint32 skills = 4;           // 所有主动技能
  repeated uint32 other_ps_skills = 5;  // 附加的被动技能
  bool is_void = 6;                     // 是否是虚拟单位
  uint32 boss_pos = 7;                  // 主体怪的位置
  uint32 pos = 8;                       // 当前位置
}

message BuffDebugInfo {
  uint32 id = 1;                // buff自增id
  uint32 sys_id = 2;            // buff系统Id
  uint32 round = 3;             // 剩余回合
  uint64 value = 4;             // 代表新加buff获得的护盾的值
  uint32 layer = 5;             // 层数
  uint64 armor_value = 6;       // 代表新加buff获得的守护铠的值，总量
  uint64 left_armor_value = 7;  // 守护铠当前的值
}

message DebugFormula {
  uint32 formula = 1;  // 公式id
  uint32 attack = 2;   // 攻击方
  uint32 defense = 3;  // 防守方
  int64 param1 = 4;
  int64 param2 = 5;
  int64 param3 = 6;
  int64 param4 = 7;
  repeated string infos = 8;
  int64 r = 9;  // 最终结果
}

message MemberDeadInfo {
  uint32 id = 1;
  uint32 sta = 2;  // 状态
}

// 召唤
message CallEffect {
  Member member = 1;
}

message ChangeAttr {
  uint32 attr_type = 1;
  int64 value = 2;
}

message ChangeMaxHp {
  int64 max_hp = 1;
  int64 hp = 2;
}

message RemainStarInfo {
  uint32 id = 1;
  uint32 star = 2;
}

message TalentTree {
  map<uint32, uint32> levels = 1;
}

// 战斗结果简要信息
message BattleSummary {
  string id = 1;              // 战报id
  bool win = 2;               // 是否胜利
  uint32 report_version = 3;  // 自定义战报版本号
  repeated BattleSummaryReport reports = 4;
}

// 战斗结果简要信息 - 单战报
message BattleSummaryReport {
  bool win = 1;                                      // 是否胜利
  uint32 round = 2;                                  // 回合数
  uint64 total_hurt = 3;                             // 战斗总伤害
  repeated BattleSummaryMember attackers = 4;        // 攻击方信息
  repeated BattleSummaryMember defensers = 5;        // 防守方信息
  repeated BattleSummaryMember attackers_final = 6;  // 结束时攻击方信息
  repeated BattleSummaryMember defensers_final = 7;  // 结束时防守方信息
  ExtraResult extra_result = 8;                      // 额外战斗信息
  int64 attack_power = 9;                            // 进攻方战力
  int64 defense_power = 10;                          // 防守方战力
}

// 战斗结果简要信息 - 战斗member
message BattleSummaryMember {
  uint32 uniter_id = 1;   // 战斗中成员唯一id
  uint64 uniq_id = 2;     // 外面的唯一id,玩家玩家hero id 怪物就是系统id
  uint32 sys_id = 3;      // 英雄系统id
  uint32 pos = 4;         // 占位id
  int64 now_hp = 5;       // 当前血量
  int64 max_hp = 6;       // 最大血量
  int64 show_max_hp = 7;  // 显示最大血量
  uint32 star = 8;        // 星数
  uint32 level = 9;       // 等级
}
