syntax = "proto3";

option go_package = "app/protos/out/ret";

package ret;

enum RET {
  /*********************************/
  // 以下同cg.proto中RET一致 禁止修改
  ERROR = 0;
  OK = 1;
  SERVER_MAINTAIN = 2;                         // 服务器维护中
  USER_NOT_EXIST = 3;                          // 玩家不存在
  USER_NAME_REPEAT = 4;                        // 创角时，角色名字重复
  LOGIN_REPEAT = 5;                            // 重复登录
  QUEUING = 6;                                 // 排队中
  ACCOUNT_SIZE_OUT_OF_LIMIT = 7;               // 账号数量超过限制
  GATEWAY_CLIENTS_OUT_OF_LIMIT = 8;            // 当前网关服务器人数超上限
  BAN_ACCOUNT = 9;                             // 玩家被禁止登陆
  BAN_USER_BEFORE_OP = 10;                     // 先封禁再操作
  USER_WRONG = 11;                             // 玩家错误
  CROSS_MAINTAIN = 30;                         // 跨服服务器维护中
  MODULE_NOT_REGISTER = 31;                    // 跨服玩法未注册（处理逻辑不存在）
  CROSS_ARENA_RESETTING = 32;                  // 跨服战处于结算期
  CROSS_ARENA_FIGHTER_NOT_EXIST = 33;          // 跨服战玩家数据不存在
  CROSS_ARENA_FIGHTERS_NOT_IN_SAME_ROOM = 34;  // 跨服战对战双方不在同一房间
  CROSS_ARENA_FIGHTER_LOCKED = 35;             // 跨服战，玩家被锁
  CROSS_ARENA_DEFENDER_RANK_CHANGED = 36;      // 跨服战，防守方排名变化，且已不在可打范围内
  CROSS_REQ_TIMEOUT = 37;                      // 跨服请求超时
  CROSS_PARTITION_RESET_NOT_FINISH = 38;       // 跨服分区重置未完成
  CROSS_PEAK_INIT_DATA_PREPARING = 40;         // 巅峰竞技场初始数据，数据正在准备中（世界boss结算尚未完成）
  CROSS_PEAK_INIT_DATA_NOT_ENOUGH = 41;        // 巅峰竞技场初始数据，数量不足（世界boss玩家不足128人）

  // 以上同cg.proto中RET一致 禁止修改
  /*********************************/

  // service自身错误码从101开始
  SERVER_BUSY = 101;               // 服务器繁忙
  SERVER_ERROR = 102;              // 服务器异常
  LOGIN_ABNORMAL = 103;            // 登录异常
  USER_NAME_ILLEGAL = 104;         // 非法的用户名
  MAIN_ROLE_TYPE_ILLEGAL = 105;    // 主角类型异常
  CREATE_USER_FALIED = 106;        // 创角失败
  USER_ACCOUNT_EXISTS = 107;       // 账号已存在
  NOT_ENOUGH_RESOURCES = 108;      // 资源不够
  NOT_ENOUGH_SPACE_IN_BAGS = 109;  // 背包空间不足
  LEVEL_UP_MAX = 110;              // 英雄已满级
  KNIGHT_NOT_EXIST = 111;          // 武将不存在
  REPEATED_PARAM = 112;            // 重复的参数
  GRPC_SERVER_NOT_OPEN = 113;      // grpc 接口没有开启
  // COMMON_CLIENT_PARAM_ERROR = 113;                        //非法参数
  REPEATED_RECEIVE_AWARD = 114;       // 重复领奖
  SYSTEM_DATA_ERROR = 115;            // 系统表配置错误,系统表数据不存在
  CLIENT_REQUEST_ERROR = 116;         // 客户端请求错误
  ITEM_CANNOT_USE = 117;              // 道具不可使用
  NOT_ITEM_RESOURCES = 118;           // 资源非道具类型
  ITEM_CANNOT_USE_LEVEL = 119;        // 当前境界不可以使用该道具
  ITEM_USE_DUJIE_PROP_FULL = 120;     // 渡劫概率已达100%，不需要使用更多丹药了
  FUNCTION_NOT_OPEN = 122;            // 等级或vip不足，功能未开放
  COMMON_LEVEL_NOT_ENOUGH = 123;      // 等级不足
  COMMON_USER_OFFLINE = 124;          // 玩家不在线
  USER_NAME_ILLEGAL_CHARACTER = 125;  // 角色名字存在特殊字符
  USER_NAME_LENGTH_LIMIT = 126;       // 角色名字超过长度限制
  // CLIENT_PARAM_ERROR = 127;                        //客户端参数错误
  // CLIENT_PARAM_SYS_INFO_ERROR = 128;               //客户端参数系统数据错误
  // CLIENT_PARAM_CHECK_ERROR = 129;                  //客户端参数服务器校验错误
  // USER_SYS_INFO_ERROR = 130;                       //用户系统数据错误
  // REPEATE_RECEIVE_AWARD = 131;                     //重复领奖
  GET_USER_DATA_ERROR = 132;                                  // 获取用户数据错误，包括获取snap和battle data
  GET_USER_DATA_ID_NOT_EXIST = 133;                           // 获取用户数据错误，id不存在
  BATTLE_TEAM_NO_HP = 134;                                    // 战斗队伍血量为0
  ITEM_USE_NUM_LIMIT = 155;                                   // 道具使用数量限制
  INVALID_TIME_FORMAT = 161;                                  // 无效的时间格式
  SET_TO_EARLIER_NOT_PERMITED = 162;                          // 不能设置更早的时间
  FRIEND_IN_TARGET_BLACKLIST = 221;                           // 被对方拉黑
  FRIEND_TARGET_LIST_FULL = 222;                              // 对方好友列表已满
  FRIEND_RECV_LIKE_COUNT_LIMIT = 223;                         // 领取点赞次数不足
  FRIEND_LIST_FULL = 224;                                     // 好友列表已满
  CHAT_FRIEND_IN_BLACKLIST = 225;                             // 已拉黑对方，不可发送信息
  DAILY_TASK_PROGRESS_NOT_FINISH = 226;                       // 任务进度没有完成
  ACHIEVE_PROGRESS_NOT_FINISH = 227;                          // 成就任务进度没有完成
  BLACK_FRIEND_LIST_FULL = 228;                               // 黑名单已满
  CLIENT_VERSION_TOO_LOWER = 229;                             // 客户端版本过低
  LOGIN_LIMIT = 230;                                          // 白名单限制
  USER_NAME_PURELY_NUMERICAL = 231;                           // 名字不能全部为数字
  CONTENT_LENGTH_LIMIT = 232;                                 // 发送信息超过长度限制
  CONTENT_INVALID = 233;                                      // 信息内容无效
  PREVIOUS_DUNGEON_NOT_FINISH = 240;                          // 前置关卡未完成
  ONHOOK_TIME_ERROR = 241;                                    // 挂机时间太短（不大于0）
  ONHOOK_AWARD_ERROR = 242;                                   // 挂机奖励领取失败
  COUNT_PRICE_ERROR = 243;                                    // 计算价格失败
  SPEED_ONHOOK_NUM_LIMIT = 244;                               // 加速挂机次数限制
  TIME_TOO_SHORT_NO_AWARD = 245;                              // 挂机时间过短，无奖励
  FIGHT_TIME_ILLEGAL = 246;                                   // 两次战斗请求时间间隔不合法
  HERO_NOT_EXIST = 250;                                       // 英雄数据不存在
  HERO_MAX_LEVEL_LIMIT = 251;                                 // 英雄等级已达上限
  HERO_STAGE_MAX_LEVEL_LIMIT = 252;                           // 英雄突破等级已达上限
  COMMON_STAR_NOT_ENOUGH = 253;                               // 星级不足
  HERO_SLOT_MAX_LIMIT = 254;                                  // 英雄栏位数量已达上限
  BATTLE_ERROR = 255;                                         // 战斗出错
  IP_SIZE_OUT_OF_LIMIT = 256;                                 // 相同ip创角数量超限制
  DEVICE_ID_SIZE_OUT_OF_LIMIT = 257;                          // 相同设备创角数量超限制
  HERO_STARUP_CARDS_NOT_MATCH = 258;                          // 英雄升星，客户端传递数据与要求不匹配
  HERO_DISBAND_CHECK_FAILED = 259;                            // 英雄分解验证失败
  VIP_LEVEL_NOT_ENOUGH = 260;                                 // vip等级不足
  SUMMON_HERO_ERROR = 261;                                    // 召唤出错
  SHOP_NOT_EXIST = 262;                                       // 玩家商店数据不存在
  CHECK_GOODS_FAILED = 263;                                   // 检查商品失败
  NO_REFRESH_TIMES = 264;                                     // 刷新次数不足
  FRAGMENT_USE_NUM_LIMIT = 265;                               // 碎片使用数量限制
  EQUIP_NOT_EXIST = 266;                                      // 装备不存在
  EQUIP_WEAR_TYPE_ERROR = 267;                                // 装备穿脱类型出错
  EQUIP_WAS_USED = 268;                                       // 装备已经被穿
  EQUIP_TARGET_LEVEL_ERROR = 269;                             // 装备养成的目标等级错误
  GOODS_BUY_NUM_LIMIT = 270;                                  // 单次购买商品数量超上限
  NOT_RANDOM_SHOP = 271;                                      // 不是随机商店，不能刷新
  NO_FIND_USER = 272;                                         // 没有找到user
  BATTLE_REPORT_NOT_EXIST = 273;                              // 战报不存在
  TOWER_SWEEP_COUNT_NOT_ENOUGH = 274;                         // 爬塔扫荡次数不足
  ARENA_NO_USER_DATA = 275;                                   // 竞技场，没有玩家数据
  ARENA_REFRESH_TYPE_ERROR = 276;                             // 竞技场，刷新类型错误 - 不符合自动刷新条件
  ARENA_REFRESH_CD_ERROR = 277;                               // 竞技场，手动刷新时间未到
  ARENA_OPPONENT_NUM_ERROR = 278;                             // 竞技场，对手列表数量不足
  ARENA_OPPONENT_NOT_EXIST = 279;                             // 竞技场，对手不在对手列表中
  ARENA_MATCH_CONFIG_ERROR = 280;                             // 竞技场，匹配表数据错误
  ARENA_ATTACK_FORMATION_NOT_EXIST = 281;                     // 竞技场，进攻阵容不存在
  EQUIP_ONE_KEY_WEAR_ERROR = 282;                             // 一键穿装备出错
  EQUIP_ONE_KEY_REMOVE_ERROR = 283;                           // 一键脱装备出错
  ARENA_OPPONENT_SCORE_CHANGE = 284;                          // 竞技场，对手分数变化超限
  ARENA_MUST_REFRESH = 285;                                   // 竞技场，需要刷新对手列表
  LOGIN_BEFORE_START_TIME = 286;                              // 服务器尚未开启
  ARENA_BATTLE_ERROR = 287;                                   // 场战斗异常
  ARENA_NOT_IN_RANK_LIST = 288;                               // 竞技场，玩家未上榜
  ARENA_REPEAT_LIKE_ERROR = 289;                              // 竞技场，重复点赞
  BUY_COUNT_LIMIT = 290;                                      // 购买次数不足
  COUNT_NOT_ENOUGH = 291;                                     // 次数不够
  TARGET_UID_IS_OWN = 292;                                    // 目标ID是自己
  ARTIFACT_NOT_EXIST = 293;                                   // 神器不存在
  ARENA_ADD_FAILED = 294;                                     // 竞技场，初始化玩家失败
  ARENA_NOT_OPEN = 295;                                       // 竞技场，未开启（重置中）
  ARENA_OP_BEING_ATTACKED = 296;                              // 竞技场，对手正在被他人攻击
  GEM_SLOT_NOT_MATCH = 297;                                   // 宝石穿戴的槽位不匹配
  GEM_NOT_EXIST = 298;                                        // 宝石不存在
  GEM_NOT_ENOUGH_LEVEL = 299;                                 // 英雄宝石左槽位没有达到解锁等级
  GEM_NOT_ENOUGH_STAR = 300;                                  // 英雄宝石右槽位没有达到解锁星数
  GEM_BE_HERO_WEAR = 301;                                     // 宝石只能被一个英雄穿戴
  GEM_NO_REBUILD = 302;                                       // 宝石不可置换属性
  GEM_NOT_MATCH_COMPOSE_SUM = 303;                            // 宝石合成数量不匹配 [2, 5]
  GEM_NOT_SAME_RARE = 304;                                    // 宝石稀有度不一致
  GEM_NOT_MATCH_TARGET_RARE = 305;                            // 目标宝石稀有度不匹配
  GEM_NOT_ENOUGH_SPACE_IN_BAG = 306;                          // 宝石背包空间不足
  USER_SET_NAME_REPEATED = 307;                               // 修改昵称名称重复
  USER_SAME_OPERATE = 308;                                    // 重复操作
  AVATAR_NO_ACTIVE = 309;                                     // 未激活的头像
  AVATAR_EXPIRED = 310;                                       // 激活的头像过期了
  DISPATCH_TASK_NOT_EXIST = 311;                              // 悬赏任务不存在
  DISPATCH_HERO_NOT_MATCH = 312;                              // 悬赏派遣的英雄跟任务不匹配
  DISPATCH_TOO_MANY_COMPLETED_TASKS = 313;                    // 已完成的悬赏任务未领取太多了
  DISPATCH_TASK_TIME_NOT_UP = 314;                            // 悬赏任务时间未到
  GOLDBUY_NOT_EXIST = 315;                                    // 点金信息不存在
  GOLDBUY_CHEST_NOT_EXIST = 316;                              // 点金宝箱不存在
  GOLDBUY_USE_COUNT_LIMIT = 317;                              // 点金宝箱可用次数达到上限
  GOLDBUY_BUY_GROUP_NOT_EXIST = 318;                          // 点金购买金币的消费组不存在
  EMBLEM_NOT_EXIST = 319;                                     // 纹章不存在
  EMBLEM_SLOT_NOT_UNLOCK = 320;                               // 纹章槽位未解锁
  EMBLEM_WEAR_ERROR = 321;                                    // 纹章穿戴出错
  DISPATCH_HERO_STAR_NOT_MATCH = 322;                         // 悬赏派遣的英雄星数跟任务不匹配
  DISPATCH_HERO_RACE_NOT_MATCH = 323;                         // 悬赏派遣的英雄种族跟任务不匹配
  DISPATCH_XML_INFO_NOT_EXIST = 324;                          // 悬赏量表信息不存在
  HERO_NOT_ENOUGH = 325;                                      // 英雄不足
  OPERATE_TOO_OFTEN = 326;                                    // 操作太频繁
  FRIEND_SEND_LIKE_COUNT_LIMIT = 327;                         // 赠送友情点次数达到上限
  FRIEND_NOT_IN_REQUEST = 328;                                // 对方不在申请列表里
  MAZE_USER_LEVEL_NOT_EXIST = 329;                            // 迷宫-玩家的难度等级为0
  MAZE_MAP_NOT_EXIST = 330;                                   // 迷宫-模板地图不存在
  MAZE_HERO_RECOVERY_CONFIG_NOT_EXIST = 331;                  // 迷宫-英雄复活消耗的物品配置错误
  MAZE_EVENT_ID_NOT_EXIST = 332;                              // 迷宫-事件id不存在
  MAZE_EVENT_TYPE_NOT_EXIST = 333;                            // 迷宫-事件类型不存在
  MAZE_MAP_PLAYER_NOT_EXIST = 334;                            // 迷宫-MazePlayer不存在
  MAZE_XML_DATA_CONFIG_ERROR = 335;                           // 迷宫-xml数据配置出错
  MAZE_FORMATION_NOT_EXIST = 336;                             // 迷宫-布阵阵容不存在
  MAZE_BATTLE_AWARDS_NOT_EXIST = 337;                         // 迷宫-战斗奖励不存在
  MAZE_EVENT_COMPLETED = 338;                                 // 迷宫-事件已完成
  FRIEND_REQUEST_FULL = 339;                                  // 对方申请列表已满
  MAZE_BOX_KEY_NOT_ENOUGH = 340;                              // 迷宫-宝库钥匙不够
  MAZE_SOUL_ALTER_COMPLETED_NUM_NOT_ENOUGH = 341;             // 迷宫-灵魂祭坛完成数量不够
  MAZE_BOSS_GRID_NOT_ALLOWED_GO_UP = 342;                     // 迷宫中的BOSS格子不允许走上去
  FRIEND_REPEATED_REQUEST = 343;                              // 好友重复申请
  USER_ID_NOT_VALID = 344;                                    // 错误的用户id
  MIRAGE_NOT_EXIST = 345;                                     // 个人boss-关卡信息不存在
  MIRAGE_CANNOT_SET_AFFIX = 346;                              // 个人boss-关卡不可设置词缀
  MIRAGE_VICTORY_AWARD_NOT_EXIST = 347;                       // 个人boss-胜利奖励掉落不存在
  MIRAGE_AFFIX_NOT_EXIST = 348;                               // 个人boss-关卡词缀数据不存在
  GUILD_NOT_EXIST = 349;                                      // 公会不存在
  NOT_IN_GUILD = 350;                                         // 没有加入公会
  GUILD_STRING_LENGTH_LIMIT = 351;                            // 字符串长度限制
  GUILD_STRING_ILLEGAL_CHARACTER = 352;                       // 字符串包含非法字符
  HAS_GUILD = 353;                                            // 已经在公会中
  GUILD_NAME_USED = 354;                                      // 公会名已被占用
  NOT_GUILD_MEMBER = 355;                                     // 不是此公会成员
  NOT_GUILD_LEADER = 356;                                     // 不是公会会长
  NOT_GUILD_LEADER_OR_DEPUTY = 357;                           // 不是公会会长或副会长
  GUILD_KICK_GRADE_LIMIT = 358;                               // 公会踢人职位限制
  GUILD_MEMBER_COUNT_LIMIT = 359;                             // 公会人数限制
  GUILD_JOIN_CD = 360;                                        // 加入公会CD中
  GUILD_CREATE_CD = 361;                                      // 公会创建CD中
  GUILD_SET_NAME_CD = 362;                                    // 公会改名CD中
  GUILD_SIGN_IN_LIMIT = 363;                                  // 公会今日已签到
  GUILD_DEPUTY_COUNT_LIMIT = 364;                             // 公会副会长人数限制
  GUILD_NEED_APPLY = 365;                                     // 公会需要申请
  GUILD_APPLY_LIST_FULL = 366;                                // 公会申请列表已满
  MAZE_MAP_EXPIRED = 367;                                     // 迷宫-地图已过期
  MAZE_BOX_AWARDS_NOT_EXIST = 368;                            // 迷宫-宝库奖励不存在
  MAZE_DUNGEON_RANK_NOT_EXIST = 369;                          // 迷宫-主线战力榜不存在
  MAZE_SNAPSHOT_USER_NOT_EXIST = 370;                         // 迷宫-玩家快照不存在
  NO_GUILD_CAN_JOIN = 371;                                    // 当前没有可直接加入的公会
  MEMORY_CHIP_NOT_EXIST = 372;                                // 境界-回忆点不存在
  MEMORY_CHIP_GROUP_NOT_FIRST = 373;                          // 境界-回忆点的组不是第一个回忆
  MEMORY_CHIP_GROUP_NOT_SAME = 374;                           // 境界-回忆点组不一致
  MEMORY_CHIP_REPEAT_UNLOCK = 375;                            // 境界-回忆点重复解锁
  MEMORY_CHIP_PRE_GROUP_NOT_VALID = 376;                      // 境界-回忆点前置回忆无效
  FOREST_ENEMY_NOT_IN_SEARCH_LIST = 377;                      // 密林-玩家已不在搜索列表中，不能被打
  FOREST_ENEMY_LOCKED = 378;                                  // 密林-玩家被锁定（正在被打），不能被打
  FOREST_NO_ATTACK_FORMATION = 379;                           // 密林-没有进攻阵容
  FOREST_BATTLE_ERROR = 380;                                  // 密林-战斗错误
  FOREST_LOG_NOT_EXIST = 381;                                 // 密林-掠夺日志不存在
  FOREST_NOT_EXIST = 382;                                     // 密林-玩家密林数据不存在
  FOREST_TREE_NOT_EXIST = 383;                                // 密林-玩家密林树的数据不存在
  FOREST_STAGE_ERROR = 384;                                   // 密林-所处阶段错误
  FOREST_NEED_HARVEST = 385;                                  // 密林-已处于待收获状态
  FOREST_SEARCH_CD = 386;                                     // 密林-搜索cooldown中
  FOREST_END_GROW = 387;                                      // 密林-被掠夺者的树已结束生长
  FOREST_NO_LOOTED_CHANCE = 388;                              // 密林-没有剩余被掠夺次数
  FOREST_NOT_HARVEST_TIME = 389;                              // 密林-未到收获时间
  FOREST_REPEAT_LOOT = 390;                                   // 密林-重复掠夺
  FOREST_SCORE_ERROR = 391;                                   // 密林-分数错误
  GUILD_LOADING = 392;                                        // 公会数据加载中
  PARAM_LENGTH_LIMIT = 393;                                   // 请求参数长度超过限制
  GUILD_DUNGEON_CHAPTER_CHANGE = 394;                         // 公会章节发生变化
  TOWER_NOT_EXIST = 395;                                      // 爬塔 - 玩家塔数据不存在
  GUILD_TALENT_XML_DATA_NOT_EXIST = 396;                      // 公会天赋-量表数据不存在
  GUILD_TALENT_JOB_LEVEL_FULL = 397;                          // 公会天赋-等级已满，不能再升级
  GUILD_TALENT_ALL_JOB_LEVEL_LIMIT = 398;                     // 公会天赋-全职业线等级限制
  GUILD_TALENT_OTHER_JOB_LEVEL_LIMIT = 399;                   // 公会天赋-其他职业线等级限制
  DUNGEON_NOT_START_ONHOOK = 400;                             // 主线-未开始挂机
  ARENA_SELF_BEING_ATTACKED = 401;                            // 竞技场-自己正在被他人攻击
  HAVE_SAME_ARTIFACT = 402;                                   // 已拥有同名神器
  GEM_UNLOCK_NOT_MATCH = 403;                                 // 英雄宝石槽位解锁不匹配
  HERO_LOCKED = 404;                                          // 英雄已锁定
  GUILD_SEND_MAIL_CD = 405;                                   // 公会发送全员邮件CD中
  GUILD_APPLY_HAS_BEEN_PROCESSED = 406;                       // 公会申请已被处理
  GUILD_HAVE_MEMBER_DISBAND_BE_LIMIT = 407;                   // 公会中有其它成员，不能解散
  SHOP_EXT_TYPE_CARNIVAL_FAILED = 408;                        // 检查商店嘉年华扩展类型失败
  CARNIVAL_TASK_PROGRESS_NOT_FINISH = 409;                    // 嘉年华任务未完成
  BAN_ACCOUNT_TEMPORARY = 410;                                // 账号存在异常，正在处理中，有问题请联系客服
  USER_NOT_FOUND = 411;                                       // 用户没找到
  DUNGEON_REPEAT_FIGHT = 412;                                 // 重复攻打，攻打已获胜关卡
  MEDAL_DAILY_AWARD_NOT_OPEN = 413;                           // 功勋-每日奖励没有开启
  MEDAL_REPEAT_RECEIVE_AWARD = 414;                           // 功勋-重复领取奖励
  MEDAL_TASK_AWARD_NOT_RECEIVE = 415;                         // 功勋-任务奖励没有全部领取完
  MEDAL_NOT_EXIST = 416;                                      // 功勋-功勋数据不存在
  MAZE_BUY_RECEIVE_COUNT_NOT_EXIST = 417;                     // 迷宫-购买复生神像次数不存在，已用完
  MIRAGE_ATTACK_FORMATION_NOT_EXIST = 418;                    // 个人boss-进攻阵容不存在
  MIRAGE_NOT_OPEN = 419;                                      // 个人boss-副本未开启
  MAZE_REVIVE_NOT_USE = 420;                                  // 迷宫- 复生神像不可以使用
  FOREST_GOBLIN_CANNOT_CHANGE_AGAIN = 421;                    // 密林-哥布林已经更换过一次了,不能再次更换
  RESONANCE_HERO_FORBIDDEN_OPERATE = 422;                     // 水晶 - 共鸣英雄禁止操作
  BAN_CHAT = 423;                                             // 聊天 - 禁言
  CHAT_GUILD_MSG_NOT_EXIST = 424;                             // 聊天 - 公会聊天没有初始化，不存在
  EMBLEM_NOT_INITIAL = 425;                                   // 纹章有被养成,不能上阵给共鸣英雄
  FORECAST_NOT_EXIST = 426;                                   // 新功能预告 - forecast 不存在
  FORECAST_AWARD_RECEIVED = 427;                              // 新功能预告 - 奖励已领取过
  CRYSTAL_SLOT_ID_ILLEGAL = 428;                              // 水晶 - 槽位id非法
  CRYSTAL_SLOT_NOT_EXIST = 429;                               // 水晶 - 槽位不存在
  CRYSTAL_SLOT_NOT_EMPTY = 430;                               // 水晶 - 槽位非空
  CRYSTAL_RESONANCE_RARE_LIMIT = 431;                         // 水晶 - 共鸣英雄品质限制
  CRYSTAL_NOT_FREEDOM_HERO = 432;                             // 水晶 - 不是自由英雄
  CRYSTAL_EXIST_IN_RESONANCE_HEROES = 433;                    // 水晶 - 共鸣英雄中，存在同名英雄
  CRYSTAL_EXIST_IN_CONTRACT_HEROES = 434;                     // 水晶 - 缔约英雄中，存在同名英雄
  CRYSTAL_SLOT_EMPTY = 435;                                   // 水晶 - 槽位是空的，没有放置英雄
  CRYSTAL_NO_MORE_SLOT_CAN_UNLOCK = 436;                      // 水晶 - 没有更多槽位可被解锁
  CRYSTAL_SLOT_CD_OVER = 437;                                 // 水晶 - 槽位cd时间已到，无需解锁
  CRYSTAL_CONTRACT_HERO_CANNOT_CHANGE = 438;                  // 水晶 - 缔约英雄不能转换
  ADD_ORDER_REPEAT = 439;                                     // 充值 - 订单重复
  CRYSTAL_SLOT_UNDER_CD = 440;                                // 水晶 - 槽位处于cd中
  RECHARGE_ORDER_PROCESS_FAIL = 441;                          // 充值 - 订单处理失败
  FORECAST_TASK_PROGRESS_NOT_FINISH = 442;                    // 新功能预告 - 任务没有完成
  TOWERSTAR_CHAPTER_NOT_UNLOCK = 443;                         // 条件爬塔 - 章节未解锁
  TOWERSTAR_DUNGEON_NOT_UNLOCK = 444;                         // 条件爬塔 - 关卡未解锁
  TOWERSTAR_DUNGEON_STAR_FULL = 445;                          // 条件爬塔 - 关卡已经满星
  TOWERSTAR_REWARD_ILLEGAL = 446;                             // 条件爬塔 - 非法奖励
  TOWERSTAR_DAILY_NO_REWARD = 447;                            // 条件爬塔 - 每日奖励奖励为空
  TOWER_TYPE_NOT_EXIST = 448;                                 // 爬塔 - 塔类型不存在
  TOWER_JUMP_START_FLOOR_LIMIT = 449;                         // 爬塔 - 跳关，未满足起始层数要求
  TOWER_JUMP_MULTI_LIMIT = 450;                               // 爬塔 - 跳关，未满足战力倍数要求
  TOWER_JUMP_COUNT_MIN_LIMIT = 451;                           // 爬塔 - 跳关，未满足跳关最小数量要求
  TOWER_JUMP_COUNT_MAX_LIMIT = 452;                           // 爬塔 - 跳关，超过最大跳关数量要求
  TOWER_JUMP_FLOOR_ILLEGAL = 453;                             // 爬塔 - 跳关，目标关卡战力不满足
  ACTIVITY_RECHARGE_BUY_LIMIT = 454;                          // 限时礼包 - 已经达到购买上限了
  ACTIVITY_RECHARGE_CONDITION_ERROR = 455;                    // 限时礼包 - 购买条件没有达到
  ACTIVITY_RECHARGE_GIFT_NOT_OPEN = 456;                      // 限时礼包 - 未开放购买
  ACTIVITY_RECHARGE_TIME_OUT = 457;                           // 限时礼包 - 不在时间范围内
  MAX_HERO_STAR_LIMIT = 458;                                  // 英雄升星，超过星级限制
  CRYSTAL_ACHIEVEMENT_NOT_FINISH = 459;                       // 水晶 - 成就未达成
  CRYSTAL_ACHIEVEMENT_ACTIVED = 460;                          // 水晶 - 成就已激活
  CRYSTAL_BLESS_MAX_LEVEL_LIMIT = 461;                        // 水晶 - 已达祝福最高等级
  CRYSTAL_BLESS_EXP_NOT_ENOUGH = 462;                         // 水晶 - 祝福经验值不足
  TOWERSTAR_DUNGEON_ILLEGAL = 463;                            // 条件爬塔 - 关卡非法
  TOWER_JUMP_POWER_NOT_ENOUGH = 464;                          // 爬塔 - 跳关，战力不足
  MONTHLY_CARD_NOT_EXIST = 465;                               // 月卡- 月卡不存在
  MAZE_OBSTACLE_NOT_ALLOWED_GO_UP = 466;                      // 迷宫 - 障碍物事件不允许走上去
  MAZE_DE_BUFF_NOT_EXIST = 467;                               // 迷宫 - 不存在减益buff
  MAZE_GRID_NOT_EXIST = 468;                                  // 迷宫 - 格子不存在
  MAZE_CURSE_LAND_NOT_EXIST = 469;                            // 迷宫 - 不存在诅咒地块事件
  MAZE_TASK_AWARD_RECEIVED = 470;                             // 迷宫 - 任务奖励已领取
  MAZE_TASK_PROGRESS_NOT_FINISH = 471;                        // 迷宫 - 任务没有完成
  MAZE_BOX_ID_NOT_EXIST = 472;                                // 迷宫 - 魔盒id不存在
  FOREST_TREE_TYPE_ERROR = 473;                               // 密林-树类型非法
  FOREST_PVP_TREE_NOT_OPEN = 474;                             // 密林-PVP树未开放
  GIFT_CODE_SERVICE_ERROR = 475;                              // 礼品码服务器暂不可用 请稍后再试
  GIFT_CODE_ILLEGAL = 476;                                    // 您输入的礼包码无效
  GIFT_CODE_CLOSED = 477;                                     // 您输入的礼包码已失效
  GIFT_CODE_EXPIRE = 478;                                     // 你输入的礼包码已过期
  GIFT_CODE_USED_SELF = 479;                                  // 您已使用过该类型礼品码
  GIFT_CODE_USED_OTHERS = 480;                                // 您输入的礼品码已被使用
  GIFT_CODE_FREQUENT = 481;                                   // 礼包码兑换太频繁
  GIFT_CODE_OP_ID_ERROR = 482;                                // 渠道有误
  GIFT_CODE_CHANNEL_ERROR = 483;                              // 二级渠道有误
  MIRAGE_STAR_NOT_ENOUGH = 484;                               // 个人boss - 关卡星数不够，不可以扫荡
  MIRAGE_FIGHT_COUNT_NOT_ENOUGH = 485;                        // 个人boss - 挑战次数不够
  MIRAGE_DROP_AWARD_NOT_EXIST = 486;                          // 个人boss - 掉落奖励不存在
  MIRAGE_POWER_NOT_ENOUGH = 487;                              // 个人boss - 战力不够，不可以碾压
  MIRAGE_HURDLE_ALREADY_PASS = 488;                           // 个人boss - 关卡已通关，不可以碾压
  RATE_ALREADY_SCORED = 489;                                  // 评分 - 已经评过分了
  ARENA_TASK_NO_AWARD_CAN_RECV = 490;                         // 竞技场，没有奖励可领（任务未达成，或已领过）
  PASS_TASK_PROGRESS_NOT_FINISH = 491;                        //  战令 - 占领任务未完成
  PASS_SYSTEM_ID_NOT_INIT = 492;                              //  战令 - 系统ID未初始化
  NO_DUNGEON_FORMATION = 493;                                 // 主线阵容不存在
  SERVER_CREATE_USER_CLOSED = 494;                            // 服务器创角关闭
  NO_ITEM = 495;                                              // 没有道具
  SUB_ITEM_NOT_BELONG_TO_ITEM = 496;                          // 所选道具，不属于当前自选礼包
  SELECT_ITEM_COUNT_MORE_THAN_ITEM_COUNT = 497;               // 所选道具数量，大于自选礼包数量
  USE_ITEM_COUNT_MORE_THAN_MAX_LIMIT = 498;                   // 使用自选礼包的数量，超过最大堆叠上限
  PASS_ACTIVE_NOT_RECHARGE = 499;                             // 战令 - 活跃战令没有充值
  PASS_TASK_AWARD_REPEAT_RECEIVE = 500;                       // 战令 - 任务奖励重复领取
  PASS_ACTIVE_TASK_FINISH = 501;                              // 战令 - 活跃战令任务已完成，不需要等级购买
  PASS_TASK_NOT_MATCH = 502;                                  // 战令 - 战令任务id跟战令不匹配
  MAZE_GUARD_BATTLE_COUNT_NOT_ENOUGH = 503;                   // 迷宫 - 守卫战斗次数不足
  OPERATE_ACTIVITY_TASK_AWARD_REPEAT_RECEIVE = 504;           // 配置活动 - 重复领奖
  OPERATE_ACTIVITY_NOT_INIT = 505;                            // 配置活动 - 未进行初始化
  OPERATE_ACTIVITY_PROGRESS_NOT_FINISH = 506;                 // 配置活动 - 活动进度未完成
  OPERATE_ACTIVITY_IS_NOT_OPENING = 507;                      // 配置活动 - 活动为开启
  OPERATE_ACTIVITY_TASK_PROGRESS_NOT_FINISH = 508;            // 配置活动 - 进度未完成
  OPERATE_ACTIVITY_TASK_IN_NOVICE_PROTECTION = 509;           // 配置活动 - 在新手保护期内
  OPERATE_ACTIVITY_INIT_TYPE_ERROR = 510;                     // 配置活动 - 初始化类型错误
  GUILD_RECRUIT_GRADE_INVALID = 511;                          // 公会招募 - 公会职位无效
  CHAT_MSG_TYPE_INVALID = 512;                                // 聊天 - 消息type无效
  CHAT_LIKE_BE_LIMIT = 513;                                   // 聊天 - 点赞数量达到上限
  CHAT_LIKE_MSG_ID_NOT_EXIST = 514;                           // 聊天 - 要点赞的消息id不存在
  CHAT_LIKE_NOT_CAN = 515;                                    // 聊天 - 不可以点赞
  GUILD_RECRUIT_CD_NOT_EXPIRE = 516;                          // 公会招募 - 还处于招募cd时间内
  CHAT_MSG_ID_INVALID = 517;                                  // 聊天 - 消息ID无效
  GEM_INHERIT_ATTR_NOT_MATCH = 518;                           // 宝石 - 继承的属性不匹配
  GEM_ADVANCED_ATTR_NOT_EXIST = 519;                          // 宝石 - 高级属性不存在
  GEM_LOCK_ATTR_NOT_MATCH = 520;                              // 宝石 - lock的属性不匹配
  FOREST_COLLECT_HERO_RARE_ERR = 521;                         // 密林-采集位英雄品质不满足要求
  FOREST_LEVEL_AWARD_RECEIVED = 522;                          // 密林-领取等级奖励，已领取过，不能再领
  FOREST_LEVEL_UNREACHED = 523;                               // 密林-领取等级奖励，未达到要领奖的等级
  FOREST_LEVEL_NO_AWARD = 524;                                // 密林-领取等级奖励，当前等级没有奖励
  EMBLEM_FEATURE_HERO_JOB_NOT_MATCH = 525;                    // 纹章 - 纹章feature 和英雄job不匹配
  EMBLEM_DECOMPOSE_RARE_LIMIT = 526;                          // 纹章 - 分解稀有度限制
  GEM_TMP_ATTR_NOT_EXIST = 527;                               // 宝石 - 临时属性不存在
  GEM_GEN_ATTR_IS_ZERO = 528;                                 // 宝石 - 生成属性为空
  GEM_CONVERT_OP_TYPE_INVALID = 529;                          // 宝石 - 洗炼操作type无效
  GODDESS_NOT_INIT = 530;                                     // 女武神未初始化
  GODDESS_FEED_ITEM_NOT_ENOUGH = 531;                         // 喂养物品不足
  GODDESS_IS_MAX_LEVEL = 532;                                 // 女武神为最高等级
  GODDESS_LEVEL_NOT_ENOUGH = 533;                             // 女武神等级不足
  GODDESS_HAS_INIT = 534;                                     // 女武神已经初始化
  CROSS_NO_FOUND_ACTIVITY = 546;                              // 跨服-没有找到对应玩法
  DEBUT_ACTIVITY_RELEASE_NUM_OVER_ONE = 547;                  // 英雄首发 - 已发布且有效的英雄抽卡活动数量不能大于1
  DEBUT_SUMMON_AWARD_NUM_NOT_MATCH = 548;                     // 英雄首发 - 抽卡获得的奖励数量和抽卡次数不匹配
  GODDESS_PRE_GODDESS_NOT_INIT = 549;                         // 前置女武神未初始化
  GODDESS_PRE_GODDESS_NOT_FINISH = 550;                       // 前置女武神章节未完成
  GODDESS_PRE_DUNGEON_NOT_FINISH = 551;                       // 前置关卡未完成
  GODDESS_DUNGEON_LOCK = 552;                                 // 关卡未解锁
  GODDESS_CONTRACT_MAX_LEVEL = 553;                           // 契约之所为最大等级
  WRESTLE_NOT_OPEN = 557;                                     // 神树争霸-赛季未开启
  WRESTLE_RESETTING = 558;                                    // 神树争霸-重置中
  WRESTLE_FAIL_CD_NOT_EXPIRE = 559;                           // 神树争霸-失败CD中...
  MAZE_PASS_EVENT_NOT_COMPLETE = 560;                         // 迷宫 - 存在未完成的事件，影响是否通关迷宫的判断
  MAZE_POWER_NOT_ENOUGH = 561;                                // 迷宫 - 获取某等级模板时，power不足
  MAZE_PASS_NUM_NOT_ENOUGH = 562;                             // 迷宫 - 某等级迷宫的通过次数不足
  MAZE_BUFF_ID_NOT_EXIST = 563;                               // 迷宫 - buffID 不存在
  MAZE_RANDOM_ALTER_BUFF_FAILED = 564;                        // 迷宫 - 祭坛buff生成失败
  GUILD_TALENT_NOT_INIT = 565;                                // 公会天赋 - 没有初始化
  GUILD_TALENT_CONNECT_TYPE_NOT_INVALID = 566;                // 公会天赋 - 关联关系类型无效
  GUILD_TALENT_PRE_NODE_NOT_ACTIVATE = 567;                   // 公会天赋 - 前置节点未激活
  GUILD_TALENT_PRE_NODE_LEVEL_NOT_ENOUGH = 568;               // 公会天赋 - 前置节点等级不够
  GUILD_TALENT_LEVEL_NOT_INVALID = 569;                       // 公会天赋 - 天赋等级无效
  GUILD_TALENT_POINT_NOT_ENOUGH = 570;                        // 公会天赋 - 天赋点不够
  GUILD_TALENT_LEVEL_INVALID_NOT_RESET = 571;                 // 公会天赋 - level为0，不可以重置
  GUILD_TALENT_FLOOR_NOT_RESET = 572;                         // 公会天赋 - 该floor的节点不可以重置
  GIFT_CODE_OVER_USE_COUNT = 573;                             // 礼包码 - 礼包码已到达使用上限
  GIFT_CODE_USE_ERROR = 574;                                  // 礼包码 - 礼包码使用失败
  GIFT_CODE_EXPIRE_OR_NOT_EXIST = 575;                        // 礼包码 - 过期或者不存在
  GUILD_DUNGEON_RESETTING = 576;                              // 公会副本 - 重置中...
  GUILD_DUNGEON_BOSS_BE_DEFEATED = 577;                       // 公会副本 - boss已被击败
  GUILD_DUNGEON_TASK_NOT_FINISH = 578;                        // 公会副本 - 任务未完成
  GUILD_TALENT_PURCHASE_ID_INVALID = 579;                     // 公会天赋 - purchaseID 无效
  WRESTLE_SELF_IS_ATTACKING = 580;                            // 神树争霸 - 正在战斗中
  GUILD_DUNGEON_RESETTING_LIMIT_KICK_MEMBER = 581;            // 公会副本 - 副本结算中，禁止踢人
  FLOWER_NOT_EXIST = 583;                                     // 密林-玩家密林数据不存在
  FLOWER_OCCUPY_MODE_NOT_OPEN = 584;                          // 密林-据点模式未开启
  FLOWER_FLOWERBED_OWNER_NOT_SAME = 585;                      // 密林-据点实际拥有者，与前端所传参数不符
  FLOWER_LOG_NOT_EXIST = 586;                                 // 密林-日志不存在
  FLOWER_PLANT_MODE_NOT_EXIST = 587;                          // 密林-种植模式未初始化
  FLOWER_STAGE_ERROR = 588;                                   // 密林-种植阶段有误
  FLOWER_SEED_NOT_EXIST = 589;                                // 密林-种子未初始化
  FLOWER_SCORE_ERROR = 590;                                   // 密林-积分错误
  FLOWER_GUIDED_NOT_EXIST = 591;                              // 密林-引导未初始化
  FLOWER_GOBLIN_CANNOT_CHANGE_AGAIN = 592;                    // 密林-不能再次交换哥布林
  FLOWER_LEVEL_IS_TOP = 593;                                  // 密林-等级最高
  FLOWER_LEVEL_UP_METHOD_ERROR = 594;                         // 密林-升级方式不对
  FLOWER_BATTLE_ERROR = 595;                                  // 密林-战斗报错
  FLOWER_SEARCH_CD = 596;                                     // 密林-搜索CD
  FLOWER_ENEMY_NOT_IN_SEARCH_LIST = 597;                      // 密林-对手不在
  FLOWER_ENEMY_NO_SNATCH_CHANGE = 598;                        // 密林-没有抢夺次数
  FLOWER_REPEAT_SNATCH = 599;                                 // 密林-重复抢夺
  FLOWER_END_GROW = 600;                                      // 密林-停止生长
  FLOWER_NOT_HARVEST_TIME = 601;                              // 密林-未到达收获时间
  FLOWER_NO_ATTACK_FORMATION = 602;                           // 密林-没有进攻队伍
  FLOWER_OTHER_SNATCH = 603;                                  // 密林-別人正在正在抢夺
  FLOWER_NEED_HARVEST = 604;                                  // 密林-需要收获
  GUILD_TALENT_NODE_ALREADY_LEVEL_UP = 605;                   // 公会天赋 - 第三层节点已升级过
  FLOWER_NO_FLOWERBED = 606;                                  // 密林-没有花坛
  FLOWER_ENEMY_LOCKED = 607;                                  // 密林-防守方被锁
  FLOWER_NO_OCCUPY_ATTACK_NUM = 608;                          // 密林-据点模式，没有进攻次数
  FLOWER_OCCUPY_ATTACK_NUM_LIMIT = 609;                       // 密林-据点模式，进攻次数已达上限
  FLOWER_FLOWERBED_IN_PROTECTING = 610;                       // 密林-据点模式，据点处于保护期
  FLOWER_TIMBER_NOT_OPEN = 611;                               // 密林-据点模式，森林尚未开放
  HERO_GEM_NOT_OPEN = 612;                                    // 英雄宝石未解锁
  GODDESS_STORY_AWARD_IS_TOKEN = 613;                         // 女武神 - 故事奖励已领取
  GODDESS_TOUCH_COUNT_NOT_ENOUGH = 614;                       // 女武神 - 触摸次数不足
  FLOWER_ATTACK_OWN_FLOWERBED = 615;                          // 密林-据点模式，不能攻打自己的据点
  FLOWER_OCCUPY_AWARD_NEED_RECV = 616;                        // 密林-据点模式，有历史结算奖励未领取
  ARTIFACT_STRENGTH_STAR_LIMIT = 617;                         // 神器 - 神器强化星级限制
  MIRAGE_AWARD_ALREADY_RECEIVED = 618;                        // 个人boss - 一次性奖励已领取
  MIRAGE_HURDLE_NOT_EXIST_AWARD = 619;                        // 个人boss - 该关卡不存在一次性奖励
  FLOWER_SHARE_CD_ERROR = 620;                                // 密林-据点模式，分享cd未到
  FLOWER_NO_OCCUPY_AWARD = 621;                               // 密林-据点模式，没有奖励数据
  GODDESS_INIT_FAILED = 623;                                  // 女武神 - 初始化失败
  GUILD_DUNGEON_SWEEP_LIMIT = 624;                            // 公会副本-boss强度变化，需要重新挑战才能扫荡
  FLOWER_SELF_LOCKED = 625;                                   // 密林-进攻方被锁
  MONTH_TASKS_GROUP_NOT_OPEN = 626;                           // 全民无双-任务组未解锁
  MONTH_TASKS_TASK_NOT_FINISH = 627;                          // 全民无双-任务未完成
  FLOWER_OCCUPY_EXTEND_NUM_LIMIT = 628;                       // 密林-据点模式，延长占领时间次数不足
  DAILY_WISH_ACTIVITY_SAVE_XML_ERROR = 629;                   // 每日许愿活动存储错误
  DAILY_WISH_ACTIVITY_NOT_OPEN = 630;                         // 每日许愿活动未开放
  DAILY_WISH_ACTIVITY_RAND_AWARD_ID = 631;                    // 每日许愿随机抽奖ID失败
  DAILY_WISH_AWARD_ID_NOT_EXIST = 632;                        // 每日许愿奖励ID不存在
  DAILY_WISH_SUMMON_COUNT_NOT_ENOUGH = 633;                   // 每日许愿抽奖次数不足
  DAILY_WISH_ACTIVITY_INFO_TIME_ERROR = 634;                  // 每日许愿信息时间错误
  DAILY_WISH_ACTIVITY_TIME_REPEATED = 635;                    // 每日许愿时间重叠
  DAILY_WISH_AWARD_LEN_ERROR = 636;                           // 每日许愿奖励信息长度错误
  DAILY_WISH_AWARD_ACT_ID_ERROR = 637;                        // 每日许愿活动ID错误
  DAILY_WISH_AWARD_SAVE_XML_ERROR = 638;                      // 每日许愿存储错误
  EMBLEM_CUSTOMIZE_RANDOM_TYPE_ERROR = 639;                   // 符文定制随机类型错误
  EMBLEM_CUSTOMIZE_TYPE_ERROR = 640;                          // 符文定制类型错误
  EMBLEM_CUSTOMIZE_EMBLEM_ID_OVER_SIZE = 641;                 // 符文ID超过限制
  EMBLEM_CUSTOMIZE_ITEM_VALUE_ERROR = 642;                    // 符文道具Value不匹配
  LINK_SUMMON_POOL_ID_IS_ERROR = 643;                         // 卡池ID非法
  LINK_SUMMON_RAND_GROUP_FAILED = 644;                        // 随机卡池失败
  LINK_SUMMON_TIMES_UPDATE = 645;                             // 更新流派抽卡时间
  FLOWER_RANDOM_GOBLIN_SCORE_FAILED = 646;                    // 哥布林随机分数失败
  FLOWER_START_FEED_FAILED = 647;                             // 哥布林开始喂养失败
  ARTIFACT_DEBUT_NOT_IN_ACTIVE_TIME = 648;                    // 神器首发 - 未处于活动时间内（没有活动）
  ARTIFACT_DEBUT_NOT_IN_AWARD_TIME = 649;                     // 神器首发 - 未处于活动领奖时间内（没有活动）
  ARTIFACT_DEBUT_NO_WISH_ARTIFACT = 650;                      // 神器首发 - 未设置心愿神器
  ARTIFACT_DEBUT_CANT_RECV_ACT_AWARD = 651;                   // 神器首发 - 无法领取活动奖励（未完成or重复领）
  DAILY_WISH_ACTIVITY_TIME_EXPIRE = 652;                      // 每日许愿活动时间过期
  GODDESS_SUIT_NOT_EXIST = 653;                               // 女武神装扮未解锁
  DIVINE_DEMON_ACTIVITY_ID_NOT_EXIST = 535;                   // 神魔抽卡 - 活动id不存在
  DIVINE_DEMON_ACTIVITY_ID_INVALID = 536;                     // 神魔抽卡 - 活动id无效
  DIVINE_DEMON_SUMMON_DROP_AWARD_NOT_EXIST = 538;             // 神魔抽卡 - 抽卡奖励掉落失败
  DIVINE_DEMON_TASK_ID_REPEAT = 540;                          // 神魔抽卡 - 任务id重复
  DIVINE_DEMON_TASK_INSTANCE_NOT_EXIST = 541;                 // 神魔抽卡 - 任务实例不存在
  DIVINE_DEMON_TASK_AWARD_BE_RECEIVE = 542;                   // 神魔抽卡 - 任务奖励被领取
  DIVINE_DEMON_TASK_ID_NOT_EXIST = 543;                       // 神魔抽卡 - 任务id不存在
  DIVINE_DEMON_TASK_NOT_FINISH = 544;                         // 神魔抽卡 - 任务没有完成
  DIVINE_DEMON_ACTIVITY_ALREADY_OFFLINE = 554;                // 神魔抽卡 - 活动已下线，执行下线操作失败
  DIVINE_DEMON_ACTIVITY_ALREADY_RELEASE = 555;                // 神魔抽卡 - 带有活动大厅的活动已发布过，不可以再发布
  DIVINE_DEMON_ACTIVITY_ALREADY_EXPIRE = 556;                 // 神魔抽卡 - 活动已过期，不可以执行下线操作
  DIVINE_DEMON_TIME_INVALID = 582;                            // 神魔抽卡 - 新创建的活动时间无效，跟已存在活动的时间冲突
  DIVINE_DEMON_SERVER_DAY_LIMIT_INVALID = 660;                // 神魔抽卡 - 服务器开服限制天数无效
  DIVINE_DEMON_OP_STATUS_INVALID = 661;                       // 神魔抽卡 - 操作类型无效
  DIVINE_DEMON_ACTIVITY_NOT_OPEN = 662;                       // 神魔抽卡 - 活动未开始
  DIVINE_DEMON_BE_SERVER_OPEN_DAY_LIMIT = 663;                // 神魔抽卡 - 被新服保护期限制(开服日期)
  DIVINE_DEMON_SUMMON_GRADE_INVALID = 664;                    // 神魔抽卡 - 抽取到的grade无效
  DIVINE_DEMON_SUMMON_RAND_SC_FAILED = 665;                   // 神魔抽卡 - 随机SC失败
  DIVINE_DEMON_SHOP_ID_INVALID = 666;                         // 神魔抽卡 - 商店id无效
  GODDESS_RECOVER_FINISH = 667;                               // 女武神恢复结束
  GODDESS_RECOVER_NOT_FINISH = 668;                           // 女武神为恢复结束
  GODDESS_RECOVER_TIME_NOT_ENOUGH = 669;                      // 女武神未达到下次恢复时间
  MONTH_TASKS_DAILY_REWARD_END = 670;                         // 全民无双-每日奖励已结束
  TRAIL_TYPE_NOT_INIT = 671;                                  // 材料本挂机未初始化
  TRAIL_TYPE_NOT_OPEN = 672;                                  // 材料本未开放
  GUILD_DONATE_COUNT_MAX = 673;                               // 公会捐献次数达到上限
  GUILD_DONATE_GET_AWARD_REPEATED = 674;                      // 公会获取奖励重复
  HERO_CANNOT_CONSUMED = 675;                                 // 英雄不能被消耗（特定阵容上阵中）
  OPERATE_ACTIVITY_LAST_ROUND_NOT_FINISH = 676;               // 配置活动上一轮未完成
  COGNITION_LOGS_SERVER_ERROR = 677;                          // 通关日志获取出错，请稍后再试
  GUILD_DONATE_POINT_NOT_ENOUGH = 678;                        // 公会捐赠点数不足
  HANDBOOK_NOT_EXIST = 679;                                   // 图鉴不存在
  HANDBOOK_REPEAT_ACTIVATE = 680;                             // 图鉴重复激活
  HANDBOOK_ACTIVATE_CHECK_ERROR = 681;                        // 不满足激活条件
  TOWER_SEASON_TASK_PROGRESS_NOT_ENOUGH = 682;                // 百塔-任务进度不满足
  NO_HERO_REACH_TOP_STAR = 683;                               // 没有英雄达到顶级星级
  DISPATCH_HERO_LINK_NOT_MATCH = 684;                         // 悬赏派遣的英雄的羁绊跟任务不匹配
  GOD_PRESENT_ALREADY_INIT = 685;                             // 777抽活动 - 数据已初始化
  GOD_PRESENT_LAST_NOT_FINISH = 686;                          // 777抽活动 - 前个子活动未完成
  GOD_PRESENT_NOT_EXIST = 687;                                // 777抽活动 - 子活动数据不存在
  GOD_PRESENT_SUMMON_ALREADY_FINISH = 689;                    // 777抽活动 - 子活动卡池已抽尽
  GOD_PRESENT_SUMMON_NOT_FINISH = 690;                        // 777抽活动 - 子活动卡池未抽尽
  GOD_PRESENT_AWARD_ALREADY_RECV = 691;                       // 777抽活动 - 子活动奖励已领取
  GOD_PRESENT_TIME_ERROR = 692;                               // 777抽活动 - 子活动未处于开启时段（量表配置）
  GOD_PRESENT_ANOTHER_POOL_HAS_STASH_AWARD = 693;             // 神器馈赠其他卡池有缓存奖励
  HERO_EXCHANGE_RACE_NOT_MATCH = 694;                         // 英雄转换 - 消耗英雄的种族类型与要求不匹配
  REPEAT_RECV_SAVE_H5_REWARD = 695;                           // 重复领取保存h5桌面奖励
  GUILD_REFUSE_JOIN = 696;                                    // 公会拒绝新人加入
  GUILD_JOIN_LEVEL_LIMIT = 697;                               // 不满足公会等级门槛
  GUILD_JOIN_POWER_LIMIT = 698;                               // 不满足公会战力门槛
  GUILD_DUNGEON_NO_CACHE = 699;                               // 公会副本没有缓存
  GUILD_DUNGEON_TOP_DIVISION_NOT_FINISH = 700;                // 公会赛季最高段位未达到
  DAILY_ATTENDANCE_DATA_NOT_EXIST = 701;                      // 累登 - 数据不存在
  DAILY_ATTENDANCE_AWARD_ALREADY_RECEIVED = 702;              // 累登 - 奖励已领取
  DAILY_ATTENDANCE_AWARD_OP_INVALID = 703;                    // 累登 - 领奖操作类型无效
  DROP_ACTIVITY_NO_ACTIVITY = 704;                            // 掉落活动 - 当前无活动
  DROP_ACTIVITY_ID_NOT_MATCH = 705;                           // 掉落活动 - 参数活动id与实际不匹配
  DROP_ACTIVITY_MAX_EXCHANGE_LIMIT = 706;                     // 掉落活动 - 已超过最大兑换限制次数
  GUID_DUNGEON_CHAPTER_NOT_FINISH = 707;                      // 公会副本 - 章节未通关
  DAILY_ATTENDANCE_NOT_RECHARGE = 708;                        // 累登 - 没有充值
  DAILY_SPECIAL_AWARD_ALREADY_RECEIVE = 709;                  // 每日特惠 - 奖励已领取
  GUILD_APPLY_REPEAT = 710;                                   // 公会申请重复
  WRESTLE_CHANGE_ROOM_CD = 711;                               // 神树争霸-更换房间CD中
  GUILD_CHEST_ACTIVATE_IS_MAX = 712;                          // 该公会宝箱激活数量达到上限
  GUILD_CHEST_ACTIVATE_REPEATED = 713;                        // 公会宝箱重复激活
  GUILD_CHEST_RECV_REPEATED = 714;                            // 公会宝箱重复领取
  GUILD_CHEST_ID_IS_NIL = 715;                                // 公会宝箱不存在
  GUILD_CHEST_LIKE_REPEATED = 716;                            // 公会宝箱重复点赞
  GUILD_CHEST_RECV_BEFORE_LIKE = 717;                         // 公会宝箱点赞前先领取
  GUILD_CHEST_CAN_NOT_RECV = 718;                             // 公会宝箱不能领取
  GUILD_CHEST_CHEST_RECV_FIN = 719;                           // 公会宝箱领取完成
  GUILD_CHEST_CHEST_IS_EXIST = 720;                           // 公会宝箱已存在
  GUILD_CHEST_ITEM_IS_EXPIRE = 721;                           // 公会宝箱物品过期
  GUILD_CHEST_ATTACH_LIMIT = 722;                             // 公会宝箱存在个数达到上限
  MAZE_HERO_HP_IS_ZERO = 723;                                 // 迷宫 - 上阵英雄血量为0
  WORLD_BOSS_WORSHIP_DROP_AWARD_NOT_EXIST = 724;              // 世界boss - 膜拜掉落奖励不存在
  WORLD_BOSS_TASK_AWARD_ALREADY_RECEIVED = 725;               // 世界boss - 任务奖励已领取
  WORLD_BOSS_TASK_NOT_FINISH = 726;                           // 世界boss - 任务没有完成
  WORLD_BOSS_NOT_OPEN = 727;                                  // 世界boss - 没有开放
  WORLD_BOSS_NOT_MATCH_ROOM = 728;                            // 世界boss - 没有匹配房间
  WORLD_BOSS_ALREADY_MATCH_ROOM = 729;                        // 世界boss - 已经匹配过房间
  WORLD_BOSS_LEVEL_INVALID = 730;                             // 世界boss - 等级无效
  WORLD_BOSS_HANDBOOK_SCORE_INVALID = 731;                    // 世界boss - 图鉴积分无效
  WORLD_BOSS_NOT_DISPLAY_PERIOD = 732;                        // 世界boss - 超过展示期时间范围
  WORLD_BOSS_CROSS_INIT_DATA_FAILED = 733;                    // 世界boss - 跨服初始化房间数据失败
  WORLD_BOSS_CROSS_GET_RANK_FAILED = 734;                     // 世界boss - 获取排行榜数据失败
  WORLD_BOSS_CROSS_GET_ROOM_LOG_FAILED = 735;                 // 世界boss - 获取房间log失败
  WORLD_BOSS_CROSS_AGAIN_MATCH_ROOM = 736;                    // 世界boss - 已开始新的一期，请重新匹配房间
  WORLD_BOSS_MAX_HURT_IS_ZERO = 737;                          // 世界boss - 单次最高伤害是0，不可以扫荡
  WORLD_BOSS_CROSS_NOT_EXIST_SETTLE_DATA = 738;               // 世界boss - 不存在结算数据
  GUILD_KICK_COUNT_LIMIT = 739;                               // 公会踢人次数限制
  FORMATION_NOT_EXIST = 740;                                  // 阵容不存在
  ACTIVITY_STORY_LOGIN_AWARD_CONDITION_FAILED = 741;          // 活动故事持续登录条件未达到
  CROSS_LAST_RANK_NOT_LOAD = 742;                             // 跨服上个赛季排行榜没有load
  USER_NAME_IS_EMPTY = 743;                                   // 用户名为空，请设置用户名
  SKIN_NOT_EXIST = 744;                                       // 皮肤 - 皮肤不存在
  SKIN_REPEAT_USE = 745;                                      // 皮肤 - 重复使用
  SKIN_NOT_BELONG_HERO = 746;                                 // 皮肤 - 不属于当前英雄
  SKIN_NOT_FOREVER = 747;                                     // 皮肤 - 不是永久皮肤
  GUILD_DUNGEON_NO_BOSS_CAN_RESTORE = 748;                    // 公会副本-没有BOSS可复活
  ACTIVITY_STORY_NOT_OPEN = 749;                              // 没有开放的活动
  ACTIVITY_STORY_BUY_CONDITION_CHECK_FAILED = 750;            // 购买条件检测失败
  ACTIVITY_STORY_NOT_HAS_OPEN = 751;                          // 没有开启的
  ACTIVITY_STORY_NEED_GET_DATA_BEFORE = 752;                  // 需要刷新
  ACTIVITY_STORY_FIGHT_CHECK_FAILED = 753;                    // 活动故事主线门派检查失败
  ASSISTANCE_ACTIVITY_NOT_OPEN = 754;                         // 助力活动为开放
  ASSISTANCE_ACTIVITY_CALC_OPEN_TIME_FAILED = 755;            // 助力活动计算开放时间失败
  MAZE_REPEAT_SWEEP = 756;                                    // 迷宫 - 重复扫荡
  MAZE_MONTHLY_CARD_NOT_EXIST = 757;                          // 迷宫 - 月卡未激活
  RITE_INVALID_RARE = 758;                                    // 永恒仪式 - 无效的仪式品质
  RITE_RARE_NOT_ENOUGH = 759;                                 // 永恒仪式 - 仪式品质不够
  RITE_DUPLICATED_RARE = 760;                                 // 永恒仪式 - 重复的仪式品质
  RITE_NOT_FOR_USER = 761;                                    // 永恒仪式 - 指定仪式不可养成
  RITE_GRIDS_NOT_ALL_EQUIPPED = 762;                          // 永恒仪式 - 指定仪式的阵格未装备满
  RITE_GRIDS_RARE_NOT_ENOUGH = 763;                           // 永恒仪式 - 指定仪式的阵格品质不够
  RITE_MAX_RARE = 764;                                        // 永恒仪式 - 指定仪式已达到最高品质
  RITE_NOT_FOUND = 765;                                       // 永恒仪式 - 找不到指定仪式
  RITE_NOT_ACTIVE = 766;                                      // 永恒仪式 - 指定仪式尚未激活
  RITE_POWER_NOT_FOUND = 767;                                 // 永恒仪式 - 找不到指定威能
  DISORDER_LAND_SEASON_LEVEL_NOT_ENOUGH = 771;                // 失序空间 - 赛季等级不足
  DISORDER_LAND_PRE_MAP_NOT_EXIST = 772;                      // 失序空间 - 前置关卡未通关
  DISORDER_LAND_PRE_DIFFICULTY_NOT_PASS = 773;                // 失序空间 - 前置关卡未通关
  DISORDER_LAND_NODE_ALREADY_COMPLETE = 774;                  // 失序空间 - 节点已完成
  DISORDER_LAND_NEIGHBOR_NODE_NOT_COMPLETE = 775;             // 失序空间 - 相邻节点未完成
  DISORDER_LAND_NOT_OPEN = 776;                               // 失序空间 - 未开放
  DISORDER_LAND_NOT_SELECT_DROP_GROUP = 777;                  // 失序空间 - 该关卡不能选择drop group
  DISORDER_LAND_RAND_DROP_GROUP_FAILED = 778;                 // 失序空间 - 随机drop group失败
  DISORDER_LAND_RAND_AWARD_FAILED = 779;                      // 失序空间 - 随机award失败
  DISORDER_LAND_BATTLE_RES_NOT_ENOUGH = 780;                  // 失序空间 - 挑战资源不足
  FORMATION_NOT_SEASON_FUNCTION = 791;                        // 布阵 - 不是赛季玩法
  FORMATION_DUPLICATE_RITE = 792;                             // 布阵 - 仪式重复上阵
  FORMATION_INVALID_POSITION = 793;                           // 布阵 - 无效的上阵位
  FORMATION_DUPLICATE_POSITION = 794;                         // 布阵 - 上阵位重复
  FORMATION_OMNIHERO_COUNT_EXCEED = 795;                      // 布阵 - 全能英雄数量超过上限
  FORMATION_NOT_OMNIHERO_HAVE_LINKS = 796;                    // 布阵 - 非全能英雄选择了羁绊
  FORMATION_OMNIHERO_SEASON_LINK_NOT_MATCH = 797;             // 布阵 - 全能英雄所选赛季羁绊与当前赛季不匹配
  FORMATION_OMNIHERO_LINK_TYPE_ERR = 798;                     // 布阵 - 全能英雄所选羁绊类型错误
  FORMATION_OMNIHERO_LINK_NOT_ACTIVE = 799;                   // 布阵 - 全能英雄选择的赛季羁绊未激活
  PEAK_FIRST_SEASON_NOT_START = 800;                          // 巅峰竞技场 - 首个赛季尚未开始
  PEAK_NOT_IN_SEASON = 801;                                   // 巅峰竞技场 - 未处于巅峰赛季起止时间中
  PEAK_NOT_IN_PHASE = 802;                                    // 巅峰竞技场 - 未处于巅峰小周期起止时间中
  PEAK_NOT_GUESS_TIME = 803;                                  // 巅峰竞技场 - 未处于竞猜时间内
  PEAK_MATCH_ROUND_ERR = 804;                                 // 巅峰竞技场 - 比赛场次与当前轮不匹配
  PEAK_GURSS_TIME_END = 805;                                  // 巅峰竞技场 - 竞猜时间已结束
  PEAK_GURSS_UID_NOT_IN_MATCH = 806;                          // 巅峰竞技场 - 竞猜的uid不在比赛数据中
  PEAK_FIGHT_NOT_FINISH = 807;                                // 巅峰竞技场 - 战斗未完成
  PEAK_NOT_PLAYER = 808;                                      // 巅峰竞技场 - 不是参赛选手
  PEAK_RECEIVED_INVITE_REWARD = 809;                          // 巅峰竞技场 - 已领过邀请奖励
  PEAK_NO_USER = 810;                                         // 巅峰竞技场 - 没有玩家数据
  PEAK_MATCH_NOT_EXIST = 811;                                 // 巅峰竞技场 - 比赛不存在
  PEAK_NO_FIGHTER = 812;                                      // 巅峰竞技场 - 跨服选手数据不存在
  PEAK_PHASE_FINISHED = 813;                                  // 巅峰竞技场 - 小周期已结算完成，无法领取邀请奖励
  PEAK_NO_GUESS_COUNT = 814;                                  // 巅峰竞技场 - 竞猜次数不足
  PEAK_NO_TOP3 = 815;                                         // 巅峰竞技场 - 目前没有产生巅峰霸主
  PEAK_RANDOM_WORSHIP_DROP_FAIL = 816;                        // 巅峰竞技场 - 计算膜拜掉落奖励失败
  PEAK_SAME_MATCH_GUESS_DIFFERENT = 817;                      // 巅峰竞技场 - 同一场比赛，下注不同选手
  PEAK_PHASE_NOT_OPEN = 818;                                  // 巅峰竞技场 - 当前比赛周期未开启（报名人数不足）
  RETURN_NOT_OPEN = 831;                                      // 回流 - 活动未触发
  RETURN_INVALID_LOGIN_DAY_INDEX = 832;                       // 回流 - 登录天序号错误
  PRE_SEASON_RECV_CHECK_FAILED = 833;                         // 赛季前奖励 - 条件检查失败
  SEASON_DUNGEON_TASK_PROGRESS_NOT_ENOUGH = 834;              // 赛季主线 - 任务进度不满足
  REFUND_ORDER_NOT_EXIST = 835;                               // 充值 - 退款的订单不存在
  REFUND_ORDER_PROCESS_FAIL = 836;                            // 充值 - 订单退款处理失败
  REFUND_COUPON_DISABLED = 837;                               // 充值 - 代金券功能未开启
  HERO_NOT_IN_CRYSTAL = 838;                                  // 英雄不在水晶
  RESOURCE_CAN_NOT_COST = 839;                                // 资源 - 不能被消耗
  RESOURCE_COST_ID_IS_REPEATER = 840;                         // 资源 - 消耗的英雄ID相同
  RESOURCE_RESOLVE_FAILED = 841;                              // 资源 - 资源拆分失败
  RESOURCE_MERGE_GET_XML_DATA_FAILED = 842;                   // 资源 - 活动表数据失败
  RESOURCE_CHECK_ATTR_FAILED = 843;                           // 资源 - 资源属性检查失败
  RESOURCE_IS_NIL_OR_COUNT_ZERO = 844;                        // 资源 - 空或者数量为0
  RESOURCE_LEN_IS_ZERO = 845;                                 // 资源 - 传入资源长度为0
  HERO_AWAKEN_LEVEL_MAX = 851;                                // 英雄觉醒 - 已觉醒到最大等级
  HERO_AWAKEN_NOT_UNLOCKED = 852;                             // 英雄觉醒 - 觉醒尚未解锁
  HERO_AWAKEN_LEVEL_OUT_OF_LIMIT = 853;                       // 英雄觉醒 - 觉醒等级超过最大值
  NO_HERO_AWAKEN_LEVEL_UNLOCKED = 854;                        // 英雄觉醒 - 未找到觉醒已解锁的英雄
  HERO_NOT_AWAKENABLE = 855;                                  // 英雄觉醒 - 英雄不可觉醒
  ASSISTANT_MONTHLY_CARD_EXPIRED = 856;                       // 小助手 - 月卡未激活
  ASSISTANT_REPEATED_GOODS_CHOICE = 857;                      // 小助手 - 重复的商品勾选
  SEASON_RETURN_AWARD_ID_NOT_FOUND = 861;                     // 赛季回流 - 找不到指定奖励ID
  SEASON_RETURN_DUNGEON_NOT_ENOUGH = 862;                     // 赛季回流 - 赛季主线通关关卡未达到条件
  SEASON_RETURN_DUPLICATE_AWARD_ID = 863;                     // 赛季回流 - 奖励ID重复
  EMBLEM_UPGRADE_NOT_ORANGE_RARE = 864;                       // 符文升阶 - 不是橙色品质不可以升阶
  DISORDER_LAND_SEASON_DAY_NOT_ENOUGH = 865;                  // 失序空间 - 赛季天数不足
  DISORDER_LAND_SEASON_LINK_LEVEL_NOT_ENOUGH = 866;           // 失序空间 - 丰碑等级不足
  DISORDER_LAND_SEASON_LINK_NUM_NOT_ENOUGH = 867;             // 失序空间 - 丰碑数量不足
  DISORDER_LAND_NODE_NOT_COMPLETE = 868;                      // 失序空间 - 节点未完成，不可以扫荡
  DISORDER_LAND_GET_RUNE_FAILED = 869;                        // 失序空间 - 获取丰碑符文失败
  SEASON_LINK_INVALID_HEROID = 880;                           // 赛季羁绊 - 无效的英雄ID
  SEASON_LINK_LINK_ACTIVATED = 881;                           // 赛季羁绊 - 羁绊已经被激活
  GST_GUILD_NIL = 900;                                        // 公会为空
  GST_SYNC_GUILD_NIL = 901;                                   // 公会信息为空
  GST_GROUP_NIL = 902;                                        // 房间为空
  GST_USER_NOT_SIGN = 903;                                    // 玩家没有报名
  GST_USER_CANT_SIGN = 904;                                   // 非报名时间
  GST_USER_HAD_SIGN = 905;                                    // 玩家已经报名了
  GST_GUILD_LEVEL_IS_NOT_OPEN = 906;                          // 公会战 - 当前公会等级未满足
  GST_GROUND_NIL = 907;                                       // 地块为空
  GST_TEAMS_NUM_ERR = 908;                                    // 队伍数量错误
  GST_CANT_FIND_TEAM = 909;                                   // 找不到队伍
  GST_TEAM_NOT_HANGUP = 910;                                  // 队伍没有被托管
  GST_MOVE_LINE_ILLEGAL = 911;                                // 移动路线非法
  GST_USER_CANT_OPERATE = 912;                                // 非操作时间
  GST_USER_DONATE_ITEM_ID_ERROR = 913;                        // 用户捐献物品ID错误
  GST_USER_ONCE_DONATE_COUNT_IS_ERROR = 914;                  // 用户捐赠物品次数错误
  GUIDANCE_REPEAT_SELECT_SKIP = 915;                          // 新手引导 - 重复选择跳过
  GUIDANCE_NOT_CAN_SELECT_SKIP = 916;                         // 新手引导 - 重复选择跳过
  GUIDANCE_REPEAT_SKIP = 917;                                 // 新手引导 - 重复跳过
  GST_GROUP_NOT_MATCH = 918;                                  // 没有匹配房间
  GST_RECV_TASK_SEASON_OR_ROUND_NOT_SAME = 919;               // 领奖用户任务奖励赛季或者轮次和跨服服务器不符
  GST_GUILD_CHANGED = 920;                                    // 公会变动
  STORY_REVIEW_STORY_UNLOCKED = 921;                          // 剧情回忆录 - 剧情已经被激活
  NEW_YEAR_ACTIVITY_FUNCTION_IS_NOT_OPEN = 922;               // 新年活动暂未开启
  NEW_YEAR_ACTIVITY_NEED_GET_DATA_BEFORE = 923;               // 需要获取数据之前
  NEW_YEAR_ACTIVITY_LOGIN_AWARD_CONDITION_FAILED = 924;       // 新年活动领奖条件检查失败
  MAIL_HAS_DELETE_OR_EXPIRED = 925;                           // 邮件删除或者过期
  ACTIVITY_PYRAMID_NOT_OPEN = 926;                            // 金字塔活动 - 未开放
  ACTIVITY_PYRAMID_LATTICE_CHANGE_CHOOSE_AWARD_FAILED = 927;  // 金字塔活动 - 格位无法更换自选奖励
  ACTIVITY_PYRAMID_CHOOSE_REQUIREMENT_NOT_ACHIEVE = 928;      // 金字塔活动 - 格位自选奖励未达到要求
  ACTIVITY_PYRAMID_NOT_CHOOSE_AWARD_BEFORE_DRAW = 929;        // 金字塔活动 - 抽奖前未进行自选奖励
  ACTIVITY_PYRAMID_DRAW_OVER_MAX_ROUND = 930;                 // 金字塔活动 - 已抽取完最大轮次（活动毕业）
  ACTIVITY_PYRAMID_DRAW_POOL_EMPTY = 931;                     // 金字塔活动 - 无可抽取的格位
  ACTIVITY_PYRAMID_DRAW_FAILED = 932;                         // 金字塔活动 - 抽奖失败
  ACTIVITY_PYRAMID_TASK_NOT_FINISH = 933;                     // 金字塔活动 - 任务未完成
  ACTIVITY_PYRAMID_CHOOSE_AWARD_DRAW_MAX = 934;               // 金字塔活动 - 自选奖励达到抽取限制，无法继续选择
  ACTIVITY_PYRAMID_DRAW_POOL_ERROR = 935;                     // 金字塔活动 - 奖池错误
  ACTIVITY_PYRAMID_DRAW_AWARDS_ERROR = 936;                   // 金字塔活动 - 奖励错误
  SEASON_LINK_INVALID_MONUMENT_ID = 940;                      // 赛季羁绊 - 无效的丰碑id
  SEASON_LINK_MONUMENT_RARE_NOT_ENOUGH = 941;                 // 赛季羁绊 - 丰碑品质不足
  DIVINE_DEMON_DIAMOND_SUMMON_COUNT_NOT_ENOUGH = 942;         // 神魔抽卡 - 钻石抽卡次数已达上限
  CRYSTAL_IS_FREEDOM_HERO = 943;                              // 不是水晶英雄
  GST_DISPATCH_HERO_FORMATION_REPEATER = 945;                 // 公会战 - 派遣英雄与阵容冲突
  GST_DISPATCH_HERO_OTHER_BUILD_REPEATER = 946;               // 公会战 - 与其他派遣英雄冲出
  PROMOTION_CHAIN_GIFT_PRE_GIFT_NOT_BUY = 947;                // 新推送礼包-连购礼包的前置礼包未购买
  PROMOTION_OPTIONAL_GIFT_AWARD_NOT_CHOOSE = 948;             // 新推送礼包-自选礼包有奖励没有选择
  PROMOTION_BUY_NUMBER_LIMIT = 949;                           // 新推送礼包-超过购买上限
  PROMOTION_GIFT_LIMIT = 950;                                 // 新推送礼包-不符合礼包限制条件
  HERO_STAR_NOT_ENOUSH = 951;                                 // 英雄 - 星级未达到要求
  HERO_CULTIVATED = 952;                                      // 英雄 - 已养成无法转换
  HERO_EQUIPPED = 953;                                        // 英雄 - 穿戴装备或符文，无法转换
  PASS_PREVIOUS_NOT_FINISHED = 954;                           // 战令 - 前置战令未完成
  HERO_IN_CRYSTAL = 955;                                      // 英雄 - 处于水晶中无法操作
  GUILD_MEDAL_NOT_OPEN = 956;                                 // 公会 - 功勋未解锁
  GUILD_MEDAL_LIKE_REPEAT = 957;                              // 公会 - 功勋点赞重复
  GST_ARENA_TEAM_LIMIT = 958;                                 // 公会擂台战 队伍限制
  GST_ARENA_NOT_OPEN = 959;                                   // 擂台没开
  GST_ARENA_VOTE_TIMES_NOT_ENOUGH = 960;                      // 助威次数不足
  GUILD_DUNGEON_STRATEGY_RESURRECTED_CHAPTER_BOSS = 961;      // 公会副本- 秘技已经复活过该章节的BOSS
  GUILD_NOTICE_FORBIDDEN = 962;                               // 公会-公告被封禁
  BOX_POINT_NOT_ENOUGH = 963;                                 // 开箱子-积分不足
  DAILY_ATTENDANCE_MAIL_REWARD_FAILED = 970;                  // 累登 - 邮件补发奖励失败
  GST_BOSS_USER_NOT_SIGN = 971;                               // 公会boss战 - 玩家未报名
  GST_BOSS_ALREADY_REFRESH = 972;                             // 公会boss战 - boss已刷新
  GST_BOSS_AWARD_ALREADY_RESET = 973;                         // 公会boss战 - 奖励已重置
  GST_BOSS_IS_DIED = 974;                                     // 公会boss战 - boss死亡
  GST_BOSS_FIGHT_AWARD = 975;                                 // 公会boss战 - 战斗结算期
  GUILD_COMBINE_NOT_OPEN = 976;                               // 公会-合并功能未开启
  GUILD_COMBINE_TARGET_NOT_OPEN = 977;                        // 公会-不能被对方公会合并，对方合并功能未开启
  GUILD_COMBINE_REQUEST_MEMBER_LIMIT = 978;                   // 公会-不能被对方公会合并，超过对方公会人数限制
  GUILD_COMBINE_INVITE_MEMBER_LIMIT = 979;                    // 公会-不能合并对方公会，超过我方公会人数限制
  GUILD_COMBINE_REQUEST_LEADER_NOT_JOIN = 980;                // 公会-不能被对方公会合并，我方会长无法加入
  GUILD_COMBINE_INVITE_LEADER_NOT_JOIN = 981;                 // 公会-不能合并对方公会，对方会长无法加入
  GUILD_COMBINE_APPLY_NOT_EXIST = 982;                        // 公会-合并请求不存在
  GUILD_COMBINE_APPLY_EXPIRED = 983;                          // 公会-合并请求已过期
  GUILD_COMBINE_APPLY_SEND_MAX = 984;                         // 公会-发送合并请求达到上限
  GUILD_COMBINE_APPLY_RECV_MAX = 985;                         // 公会-接收合并请求达到上限
  GUILD_COMBINE_APPLY_REFUSE_FAILED = 986;                    // 公会-拒绝合并请求失败
  CRYSTAL_CONTRACT_HERO_CANNOT_DELETE = 987;                  // 水晶 - 缔约英雄不能被删除
  CRYSTAL_CONTRACT_NOT_SUCCESS = 988;                         // 水晶 - 未缔约成功（缔约英雄不足5人）
  ACTIVITY_RECHARGE_CHECK_PRIVILEGED_FAILED = 989;            // 活动充值 - 特权领取检查失败
  ACTIVITY_RECHARGE_PRIVILEGED_NOT_EXPIRED = 990;             // 活动充值 - 特权未过期
  ACTIVITY_RECHARGE_TODAY_IS_RECHARGE = 991;                  // 活动充值 - 今日已充值
  ACTIVITY_TURN_TABLE_NOT_OPEN = 992;                         // 周年庆活动 - 未开启
  ACTIVITY_TURN_TABLE_LOGIN_AWARD_CONDITION_FAILED = 993;     // 周年庆 - 登录奖励检查失败
  ACTIVITY_TURN_TABLE_BUFF_NOT_ENOUGH = 994;                  // 周年庆 - BUFF不足
  TALENT_TREE_RESET_GST_LIMIT = 995;                          // 天赋树 - 公会战结算限制，不能洗点
  SEASON_SHOP_ACTIVITY_NOT_OPEN = 996;                        // 赛季商店 - 活动未开启

  SEASON_ARENA_USER_NOT_FIND = 1000;              // 赛季竞技场 - 找不到用户
  SEASON_ARENA_STATE_NOT_OPEN = 1001;             // 赛季竞技场 - 未打开
  SEASON_ARENA_SELF_IS_ATTACKING = 1002;          // 赛季竞技场 - 自己正在攻击
  SEASON_ARENA_STA_IS_NOT_OPEN = 1003;            // 赛季竞技场 - 状态未打开
  SEASON_ARENA_FIGHT_CHECK_FAILED = 1004;         // 赛季竞技场 - 战斗检查失败
  SEASON_ARENA_FIGHT_CHECK_FREE_REFRESH = 1005;   // 赛季竞技场 - 检查失败免费刷新
  SEASON_ARENA_CROSS_USER_LOCKED = 1006;          // 赛季竞技场 - 跨服用户被锁定
  SEASON_ARENA_ROUND_AWARD_CHECK_FAILED = 1007;   // 赛季竞技场 - 奖励检查失败
  SEASON_ARENA_TASK_PROGRESS_NOT_ENOUGH = 1008;   // 赛季竞技场 - 任务进度不满足
  SEASON_ARENA_SEARCH_OPPONENTS_FAILED = 1009;    // 赛季竞技场 - 查找对手失败
  SEASON_ARENA_FREE_REFRESH_CHECK_FAILED = 1010;  // 赛季竞技场 - 免费刷新检查失败
  SEASON_ARENA_FIGHT_COUNT_CHECK_FAILED = 1011;   // 赛季竞技场 - 挑战次数检查失败
  SEASON_ARENA_SERVER_ARENA_CHECK_FAILED = 1012;  // 赛季竞技场 - 战区ID检查失败
  WORD_CHECK_SENSITIVE = 1013;                    // 含有敏感词

  BOSS_RUSH_CUR_BOSS_LOCKED = 1020;         // Boss挑战 - Boss未解锁
  BOSS_RUSH_CUR_BOSS_CANNOT_ATTACK = 1021;  // Boss挑战 - 不可攻打当前Boss

  TOWERSTAR_NO_TEAM = 1040;  // 条件爬塔 - 阵容无队伍信息

  GST_DRAGON_POS_LOCKED = 1100;                // 公会战龙战 - 龙位置未解锁
  GST_DRAGON_CANNOT_EVOLVE = 1101;             // 公会战龙战 - 龙不可进化
  GST_DRAGON_LEVEL_NOT_ENOUGH = 1102;          // 公会战龙战 - 龙等级不足
  GST_DRAGON_OFF = 1103;                       // 公会战龙战 - 处于休赛期
  GST_DRAGON_SETTLEMENT = 1104;                // 公会战龙战 - 处于结算期
  GST_DRAGON_NOT_IN_FIGHT = 1105;              // 公会战龙战 - 未处于战斗期
  GST_DRAGON_CANNOT_ATTACK = 1106;             // 公会战龙战 - 无法攻击这条龙
  GST_ORE_NOT_IN_FIGHT_TIME = 1107;            // 没有在挑战时间
  GST_ORE_FIGHT_TIMES_NOT_ENOUGH = 1108;       // 挑战次数不足
  GST_ORE_CANT_FIND = 1109;                    // 矿不存在
  GST_ORE_CANT_SWEEP = 1110;                   // 不能扫荡
  GST_ORE_CANT_FIGHT = 1111;                   // 矿不能打
  GST_TECH_CANT_FIND = 1112;                   // 科技不存在
  GST_TECH_CANT_DONATE = 1113;                 // 科技无法捐献
  GST_TECH_CANT_LEVEL_UP = 1114;               // 科技无法升级
  GST_SKILL_ASSEMBLE_GROUND_MAX = 1115;        // 地块集结技能达到上限
  GST_SKILL_ASSEMBLE_TIMES_NOT_ENOUGH = 1116;  // 集结技能次数不够
  GST_ORE_NUM_MAX = 1117;                      // 占矿数量不足
  GST_CHALLENGE_NOT_OPEN = 1118;               // 新擂台赛-未开启
  GST_CHALLENGE_CANT_FIGHT = 1119;             // 新擂台赛-不能打
  GST_CHALLENGE_TEAM_EXPIRE = 1120;            // 新擂台赛-队伍失效
  GST_CHALLENGE_QUIT_LIMIT = 1121;             // 新擂台赛-退出前公会被限制
  GST_CHALLENGE_NEED_CHOOSE_BUFF = 1122;       // 新擂台赛-需要先选buff
  GST_CHALLENGE_OPPONENT_NIL = 1123;           // 新擂台赛-对手不存在

  DIVINE_DEMON_RAND_GROUP_FAILED = 1200;  // 神魔抽 - 随机组失败
  DIVINE_DEMON_RAND_CLASS_FAILED = 1201;  // 神魔抽 - 随机Class失败

  GUILD_MOB_GUILD_LEVEL_ERR = 1300;          // 公会等级不足
  GUILD_MOB_ACCEPT_TIMES_NOT_ENOUGH = 1301;  // 接取任务次数不足
  GUILD_MOB_SELF_ACCEPT_TASK_LIMIT = 1302;   // 个人同时接取任务数量超过上限
  GUILD_MOB_ACCEPT_TASK_REPEATED = 1303;     // 已经接取了任务
  GUILD_MOB_GUILD_ACCEPT_TASK_LIMIT = 1304;  // 公会任务接取数量超过上限
  GUILD_MOB_NO_TASK = 1305;                  // 任务不存在
  GUILD_MOB_TASKING = 1306;                  // 还有人进行任务中
  GUILD_MOB_BUY_LIMIT = 1307;                // 购买次数限制
  GUILD_MOB_FRESH_TIMES_NOT_ENOUGH = 1308;   // 刷新任务次数不足
  GUILD_MOB_TASK_OPEN_DAY_LIMIT = 1309;      // 任务开服天数限制
  GUILD_MOB_BUY_FAILED = 1310;               // 购买失败
  GUILD_MOB_BE_TASK_CANCEL = 1311;           // 任务被取消

  ACTIVITY_SUM_NOT_INIT = 1330;                    // 活动合集未初始化
  ACTIVITY_PUZZLE_REPEATED_CELL = 1331;            // 格子重复激活
  ACTIVITY_PUZZLE_CELL_XY_ERROR = 1332;            // 格子XY参数错误
  ACTIVITY_TASK_PROGRESS_ERROR = 1333;             // 任务进度检查失败
  ACTIVITY_TASK_MODULE_ERROR = 1334;               // 模块检查失败
  ACTIVITY_SUM_ID_CHECK_ERROR = 1335;              // 活动ID检查失败
  ACTIVITY_SUM_TICKET_BUY_OPEN_ERROR = 1336;       // 活动门票购买检查失败
  ACTIVITY_SUM_NOT_OPEN = 1337;                    // 活动未开放
  ACTIVITY_SUB_SHOT_STAGE_CHECK_FAILED = 1338;     // 射击活动关卡检查失败
  ACTIVITY_SUB_SHOT_OPEN_DAY_CHECK_FAILED = 1339;  // 射击活动开放天数检查失败

  SELECT_SUMMON_SHOP_ID_INVALID = 1340;  // 抽卡活动上商店非法
  SELECT_SUMMON_NOT_OPEN = 1341;         // 抽卡活动为开放
  SELECT_SUMMON_ID_INVALID = 1342;       // 抽卡活动ID非法
  SELECT_SUMMON_RAND_GROUP_FAILED = 1343;
  SELECT_SUMMON_RAND_CLASS_FAILED = 1344;

  DUEL_RIVAL_FORMATION_NOT_EXIST = 1400;  // 切磋 - 对方阵容不存在
  DUEL_IN_CD = 1401;                      // 切磋 - 处于冷却时间

  TALENT_TREE_PLAN_NUM_LIMIT = 1402;  // 天赋树方案数量达到上限

  SEASON_DOOR_LINE_CANT_FIGHT = 1403;         // 赛季开门 - 队列不能战斗
  SEASON_DOOR_LINE_FIGHT_TIMES_LIMIT = 1404;  // 赛季开门 - 队列战斗次数限制
  SEASON_DOOR_LINE_FIGHT_NOT_FINISH = 1405;   // 赛季开门 - 队列战斗未完成
  SEASON_DOOR_LINE_FIGHT_NO_REWARD = 1406;    // 赛季开门 - 队列战斗没有奖励
  SEASON_DOOR_LINE_FIGHT_HAD_FINISH = 1407;   // 赛季开门 - 队列战斗已完成
  SEASON_DOOR_LINE_OIL_USED = 1408;           // 赛季开门 - 目标油已被其他队列使用
  SEASON_DOOR_LINE_OIL_NOT_ENOUGH = 1409;     // 赛季开门 - 目标油数量不足

  SEASON_JEWELRY_NOT_EXIST = 1501;             // 赛季装备 - 装备不存在
  SEASON_JEWELRY_HERO_WEAR_RARE_ERROR = 1502;  // 赛季装备 - 非橙红英雄无法穿戴
  SEASON_JEWELRY_HERO_WEAR_TAG_ERROR = 1503;   // 赛季装备 - 材料英雄无法穿戴
  SEASON_JEWELRY_SKILL_NOT_EXIST = 1504;       // 赛季装备 - 词条不存在

  REBASE_JASON_MARSHAL_FAIL = 1601;  // 返利序列化失败
  REBASE_SERVER_ERROR = 1602;        // 返利服务失败

  HERO_CONVERT_AWAKEN_NOT_MORE_THAN_MAX = 1610;  // 客户端上传觉醒材料未溢出
  HERO_CONVERT_NO_RES_TO_CONVERT = 1611;         // 没有觉醒材料需要转换

  SEASON_MAP_POSITION_NOT_EXIST = 1701;        // 赛季地图 - 地图位置不存在
  SEASON_MAP_MONSTER_DEAD = 1702;              // 赛季地图 - 怪物已死亡
  SEASON_MAP_TASK_PROGRESS_NOT_ENOUGH = 1703;  // 赛季地图 - 任务进度不满足
  SEASON_MAP_EVENT_NOT_EXIST = 1704;           // 赛季地图 - 事件不存在
  SEASON_MAP_PRICE_CHANGED = 1705;             // 赛季地图 - 价格变动
  SEASON_MAP_BUY_GOODS_NUM_LIMIT = 1706;       // 赛季地图 - 物品购买数量限制
  SEASON_MAP_NO_BUFF = 1707;                   // 赛季地图 - 不存在指定buff

  POKEMON_NOT_EXIST = 1750;                // 宠物 - 宠物不存在
  POKEMON_ACTIVATE_FAILED = 1751;          // 宠物 - 激活失败
  POKEMON_ACTIVATED = 1752;                // 宠物 - 已激活
  POKEMON_BALL_CONFIG_DUPLICATE = 1753;    // 宠物 - 出战配置重复
  POKEMON_BALL_TYPE_ERROR = 1754;          // 宠物 - 兽栏类型错误
  POKEMON_MASTER_LEVEL_NOT_ENOUGH = 1755;  // 宠物 - 大师等级不足

  POKEMON_SUMMON_NOT_OPEN = 1760;         // 宠物 - 宠物抽卡未开启
  POKEMON_SUMMON_ID_INVALID = 1761;       // 宠物 - 抽完出卡ID非法
  POKEMON_SUMMON_SHOP_ID_INVALID = 1762;  // 宠物 - 商店ID非法
  POKEMON_SUMMON_RAND_GROUP_FAILED = 1763;
  POKEMON_SUMMON_RAND_CLASS_FAILED = 1764;

  MUTE_JASON_MARSHAL_FAIL = 1801;  // 禁言JSON转换错误
  MUTE_SERVER_ERROR = 1802;        // 禁言服务器错误

  // gm自身错误码从10000开始
  GM_SERVER_ERROR = 10000;                              // 服务器出现错误
  GM_REQ_PARAM_ILLEGAL = 10001;                         // 请求数据不合法
  GM_COMMON_ACTIVITY_FIX_TIME_ERR = 10002;              // 通服活动修正时间错误
  GM_ACTIVITY_DATA_UPDATE_FAILED = 10003;               // 活动数据更新失败
  GM_DAILY_WISH_ACTIVITY_LIMIT_ERR = 10004;             // 每日许愿限制开服天数错误
  GM_DAILY_WISH_ACTIVITY_RECHARGE_TRIGGER_ERR = 10005;  // 每日许愿-充值出发金额错误
  OPERATE_ACTIVITY_ROUND_TYPE_IS_ERROR = 10006;         // 轮次活动类型错误
  OPERATE_ACTIVITY_SUB_TASK_ROUND_ERROR = 10007;        // 轮次活动子活动轮次错误
  GM_CROSS_SET_PART_AREA_NO_NEED_NEXT_PART = 10008;     // 战区玩法不需要设置下一轮分
  GM_GUILD_NOT_EXIST = 10009;                           // 工会不存在
  GM_TIME_IS_LESS_THAN_LOGIC_NOW = 10010;               // 设置的时间比游戏服时间小
  GM_TIME_IS_LESS_THAN_CROSS_NOW = 10011;               // 设置的时间比跨服服时间小
}
