<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:序号 key=string:方法名 config_value=int:参数值 -->

<root>
    <data id="1" key="INT_CHANCE_60" config_value="0" />
    <data id="2" key="CHANCE_NODE_60" config_value="20" />
    <data id="3" key="CHANCE_ADD_60" config_value="13" />
    <data id="4" key="GROUP_ID_60_GUARANTEE" config_value="60" />
    <data id="5" key="INT_CHANCE_50" config_value="0" />
    <data id="6" key="CHANCE_NODE_50" config_value="9" />
    <data id="7" key="CHANCE_ADD_50" config_value="20" />
    <data id="8" key="GROUP_ID_50_GUARANTEE" config_value="43" />
    <data id="9" key="INT_CHANCE_40" config_value="0" />
    <data id="10" key="CHANCE_NODE_40" config_value="5" />
    <data id="11" key="CHANCE_ADD_40" config_value="30" />
    <data id="12" key="GROUP_ID_40_GUARANTEE" config_value="33" />
    <data id="13" key="INT_CHANCE_30" config_value="0" />
    <data id="14" key="CHANCE_NODE_30" config_value="5" />
    <data id="15" key="CHANCE_ADD_30" config_value="154" />
    <data id="16" key="GROUP_ID_30_GUARANTEE" config_value="20" />
    <data id="17" key="INT_CHANCE_FRAGMENT_60" config_value="1300" />
    <data id="18" key="INT_CHANCE_FRAGMENT_50" config_value="2000" />
    <data id="19" key="INT_CHANCE_FRAGMENT_40" config_value="2300" />
    <data id="20" key="FIRST_DRAW" config_value="20" />
    <data id="21" key="DAILY_FREE_TIMES" config_value="1" />
    <data id="22" key="DIAMOND_SUMMON_COUNT_LIMIT" config_value="60" />
    <data id="23" key="SUMMON_COST_DIAMOND_COUNT" config_value="980" />
    <data id="24" key="SUMMON_PLURAL" config_value="10" />
    <data id="25" key="POKEMON_RARE_LIMITED" config_value="50" />
    <data id="26" key="WISH_FRAGMENT_COUNT_1" config_value="1" />
    <data id="27" key="WISH_FRAGMENT_WEIGHT_1" config_value="100" />
    <data id="28" key="WISH_FRAGMENT_COUNT_2" config_value="1" />
    <data id="29" key="WISH_FRAGMENT_WEIGHT_2" config_value="100" />
    <data id="30" key="WISH_FRAGMENT_COUNT_3" config_value="1" />
    <data id="31" key="WISH_FRAGMENT_WEIGHT_3" config_value="100" />
    <data id="32" key="WISH_FRAGMENT_COUNT_4" config_value="1" />
    <data id="33" key="WISH_FRAGMENT_WEIGHT_4" config_value="100" />
    <data id="34" key="WISH_FRAGMENT_COUNT_5" config_value="1" />
    <data id="35" key="WISH_FRAGMENT_WEIGHT_5" config_value="100" />
</root>
