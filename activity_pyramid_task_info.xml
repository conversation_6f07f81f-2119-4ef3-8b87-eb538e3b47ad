<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:任务id server_type=int:开启类型 module=int:所属模块 type_id=int:任务类型 value=int:任务参数 type1=int:奖励类型1 value1=int:奖励值1 count1=int:奖励数量1 type2=int:奖励类型2 value2=int:奖励值2 count2=int:奖励数量2 type3=int:奖励类型3 value3=int:奖励值3 count3=int:奖励数量3 -->

<root>
    <data id="1001" server_type="2" module="280" type_id="1004000" value="1" type1="4" value1="10125" count1="8" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="1002" server_type="2" module="280" type_id="1004000" value="2" type1="4" value1="10125" count1="8" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="1003" server_type="2" module="280" type_id="1004000" value="3" type1="4" value1="10125" count1="8" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="1004" server_type="2" module="280" type_id="1004000" value="4" type1="4" value1="10125" count1="8" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="1005" server_type="2" module="280" type_id="1004000" value="6" type1="4" value1="10125" count1="8" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="2001" server_type="2" module="280" type_id="2800000" value="2" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="2002" server_type="2" module="280" type_id="2800000" value="4" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="2003" server_type="2" module="280" type_id="2800000" value="6" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="2004" server_type="2" module="280" type_id="2800000" value="8" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="2005" server_type="2" module="280" type_id="2800000" value="12" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="3001" server_type="2" module="280" type_id="3803000" value="5" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="3002" server_type="2" module="280" type_id="3803000" value="10" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="3003" server_type="2" module="280" type_id="3803000" value="20" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="3004" server_type="2" module="280" type_id="3803000" value="30" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="3005" server_type="2" module="280" type_id="3803000" value="40" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="4001" server_type="2" module="280" type_id="4900000" value="1" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="4002" server_type="2" module="280" type_id="4900000" value="2" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="4003" server_type="2" module="280" type_id="4900000" value="3" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="4004" server_type="2" module="280" type_id="4900000" value="4" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="4005" server_type="2" module="280" type_id="4900000" value="5" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="5001" server_type="2" module="280" type_id="4200000" value="10" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="5002" server_type="2" module="280" type_id="4200000" value="20" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="5003" server_type="2" module="280" type_id="4200000" value="30" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="5004" server_type="2" module="280" type_id="4200000" value="40" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="5005" server_type="2" module="280" type_id="4200000" value="50" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="6001" server_type="2" module="280" type_id="4801007" value="10" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="6002" server_type="2" module="280" type_id="4801007" value="20" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="6003" server_type="2" module="280" type_id="4801007" value="30" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="6004" server_type="2" module="280" type_id="4801007" value="40" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="6005" server_type="2" module="280" type_id="4801007" value="50" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="7001" server_type="2" module="280" type_id="4500000" value="10" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="7002" server_type="2" module="280" type_id="4500000" value="20" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="7003" server_type="2" module="280" type_id="4500000" value="30" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="7004" server_type="2" module="280" type_id="4500000" value="40" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="7005" server_type="2" module="280" type_id="4500000" value="50" type1="4" value1="10125" count1="2" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="8001" server_type="2" module="280" type_id="8010000" value="3" type1="4" value1="10125" count1="4" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="8002" server_type="2" module="280" type_id="8010000" value="5" type1="4" value1="10125" count1="4" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="8003" server_type="2" module="280" type_id="8010000" value="10" type1="4" value1="10125" count1="4" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="8004" server_type="2" module="280" type_id="8010000" value="15" type1="4" value1="10125" count1="4" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
    <data id="8005" server_type="2" module="280" type_id="8010000" value="20" type1="4" value1="10125" count1="4" type2="0" value2="0" count2="0" type3="0" value3="0" count3="0" />
</root>
