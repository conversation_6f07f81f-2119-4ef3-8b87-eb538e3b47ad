<?xml version="1.0" encoding="UTF-8"?>
<!--door_type=int:门大类 door_level=int:门级别 vic_round=int:胜利回合数 rest_time=int:额外休息秒数 -->

<root>
    <data door_type="1" door_level="1" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="1" vic_round="2" rest_time="21" />
    <data door_type="1" door_level="1" vic_round="3" rest_time="22" />
    <data door_type="1" door_level="1" vic_round="4" rest_time="23" />
    <data door_type="1" door_level="1" vic_round="5" rest_time="24" />
    <data door_type="1" door_level="1" vic_round="6" rest_time="25" />
    <data door_type="1" door_level="1" vic_round="7" rest_time="26" />
    <data door_type="1" door_level="1" vic_round="8" rest_time="27" />
    <data door_type="1" door_level="1" vic_round="9" rest_time="28" />
    <data door_type="1" door_level="1" vic_round="10" rest_time="29" />
    <data door_type="1" door_level="1" vic_round="11" rest_time="30" />
    <data door_type="1" door_level="1" vic_round="12" rest_time="31" />
    <data door_type="1" door_level="1" vic_round="13" rest_time="32" />
    <data door_type="1" door_level="1" vic_round="14" rest_time="33" />
    <data door_type="1" door_level="1" vic_round="15" rest_time="34" />
    <data door_type="1" door_level="2" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="2" vic_round="2" rest_time="21" />
    <data door_type="1" door_level="2" vic_round="3" rest_time="22" />
    <data door_type="1" door_level="2" vic_round="4" rest_time="23" />
    <data door_type="1" door_level="2" vic_round="5" rest_time="24" />
    <data door_type="1" door_level="2" vic_round="6" rest_time="25" />
    <data door_type="1" door_level="2" vic_round="7" rest_time="26" />
    <data door_type="1" door_level="2" vic_round="8" rest_time="27" />
    <data door_type="1" door_level="2" vic_round="9" rest_time="28" />
    <data door_type="1" door_level="2" vic_round="10" rest_time="29" />
    <data door_type="1" door_level="2" vic_round="11" rest_time="30" />
    <data door_type="1" door_level="2" vic_round="12" rest_time="31" />
    <data door_type="1" door_level="2" vic_round="13" rest_time="32" />
    <data door_type="1" door_level="2" vic_round="14" rest_time="33" />
    <data door_type="1" door_level="2" vic_round="15" rest_time="34" />
    <data door_type="1" door_level="3" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="3" vic_round="2" rest_time="21" />
    <data door_type="1" door_level="3" vic_round="3" rest_time="22" />
    <data door_type="1" door_level="3" vic_round="4" rest_time="23" />
    <data door_type="1" door_level="3" vic_round="5" rest_time="24" />
    <data door_type="1" door_level="3" vic_round="6" rest_time="25" />
    <data door_type="1" door_level="3" vic_round="7" rest_time="26" />
    <data door_type="1" door_level="3" vic_round="8" rest_time="27" />
    <data door_type="1" door_level="3" vic_round="9" rest_time="28" />
    <data door_type="1" door_level="3" vic_round="10" rest_time="29" />
    <data door_type="1" door_level="3" vic_round="11" rest_time="30" />
    <data door_type="1" door_level="3" vic_round="12" rest_time="31" />
    <data door_type="1" door_level="3" vic_round="13" rest_time="32" />
    <data door_type="1" door_level="3" vic_round="14" rest_time="33" />
    <data door_type="1" door_level="3" vic_round="15" rest_time="34" />
    <data door_type="1" door_level="4" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="4" vic_round="2" rest_time="21" />
    <data door_type="1" door_level="4" vic_round="3" rest_time="22" />
    <data door_type="1" door_level="4" vic_round="4" rest_time="23" />
    <data door_type="1" door_level="4" vic_round="5" rest_time="24" />
    <data door_type="1" door_level="4" vic_round="6" rest_time="25" />
    <data door_type="1" door_level="4" vic_round="7" rest_time="26" />
    <data door_type="1" door_level="4" vic_round="8" rest_time="27" />
    <data door_type="1" door_level="4" vic_round="9" rest_time="28" />
    <data door_type="1" door_level="4" vic_round="10" rest_time="29" />
    <data door_type="1" door_level="4" vic_round="11" rest_time="30" />
    <data door_type="1" door_level="4" vic_round="12" rest_time="31" />
    <data door_type="1" door_level="4" vic_round="13" rest_time="32" />
    <data door_type="1" door_level="4" vic_round="14" rest_time="33" />
    <data door_type="1" door_level="4" vic_round="15" rest_time="34" />
    <data door_type="1" door_level="5" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="5" vic_round="2" rest_time="21" />
    <data door_type="1" door_level="5" vic_round="3" rest_time="22" />
    <data door_type="1" door_level="5" vic_round="4" rest_time="23" />
    <data door_type="1" door_level="5" vic_round="5" rest_time="24" />
    <data door_type="1" door_level="5" vic_round="6" rest_time="25" />
    <data door_type="1" door_level="5" vic_round="7" rest_time="26" />
    <data door_type="1" door_level="5" vic_round="8" rest_time="27" />
    <data door_type="1" door_level="5" vic_round="9" rest_time="28" />
    <data door_type="1" door_level="5" vic_round="10" rest_time="29" />
    <data door_type="1" door_level="5" vic_round="11" rest_time="30" />
    <data door_type="1" door_level="5" vic_round="12" rest_time="31" />
    <data door_type="1" door_level="5" vic_round="13" rest_time="32" />
    <data door_type="1" door_level="5" vic_round="14" rest_time="33" />
    <data door_type="1" door_level="5" vic_round="15" rest_time="34" />
    <data door_type="1" door_level="6" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="6" vic_round="2" rest_time="21" />
    <data door_type="1" door_level="6" vic_round="3" rest_time="22" />
    <data door_type="1" door_level="6" vic_round="4" rest_time="23" />
    <data door_type="1" door_level="6" vic_round="5" rest_time="24" />
    <data door_type="1" door_level="6" vic_round="6" rest_time="25" />
    <data door_type="1" door_level="6" vic_round="7" rest_time="26" />
    <data door_type="1" door_level="6" vic_round="8" rest_time="27" />
    <data door_type="1" door_level="6" vic_round="9" rest_time="28" />
    <data door_type="1" door_level="6" vic_round="10" rest_time="29" />
    <data door_type="1" door_level="6" vic_round="11" rest_time="30" />
    <data door_type="1" door_level="6" vic_round="12" rest_time="31" />
    <data door_type="1" door_level="6" vic_round="13" rest_time="32" />
    <data door_type="1" door_level="6" vic_round="14" rest_time="33" />
    <data door_type="1" door_level="6" vic_round="15" rest_time="34" />
    <data door_type="1" door_level="7" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="7" vic_round="2" rest_time="24" />
    <data door_type="1" door_level="7" vic_round="3" rest_time="36" />
    <data door_type="1" door_level="7" vic_round="4" rest_time="48" />
    <data door_type="1" door_level="7" vic_round="5" rest_time="60" />
    <data door_type="1" door_level="7" vic_round="6" rest_time="72" />
    <data door_type="1" door_level="7" vic_round="7" rest_time="84" />
    <data door_type="1" door_level="7" vic_round="8" rest_time="96" />
    <data door_type="1" door_level="7" vic_round="9" rest_time="108" />
    <data door_type="1" door_level="7" vic_round="10" rest_time="120" />
    <data door_type="1" door_level="7" vic_round="11" rest_time="132" />
    <data door_type="1" door_level="7" vic_round="12" rest_time="144" />
    <data door_type="1" door_level="7" vic_round="13" rest_time="156" />
    <data door_type="1" door_level="7" vic_round="14" rest_time="168" />
    <data door_type="1" door_level="7" vic_round="15" rest_time="180" />
    <data door_type="1" door_level="8" vic_round="1" rest_time="20" />
    <data door_type="1" door_level="8" vic_round="2" rest_time="36" />
    <data door_type="1" door_level="8" vic_round="3" rest_time="54" />
    <data door_type="1" door_level="8" vic_round="4" rest_time="72" />
    <data door_type="1" door_level="8" vic_round="5" rest_time="90" />
    <data door_type="1" door_level="8" vic_round="6" rest_time="108" />
    <data door_type="1" door_level="8" vic_round="7" rest_time="126" />
    <data door_type="1" door_level="8" vic_round="8" rest_time="144" />
    <data door_type="1" door_level="8" vic_round="9" rest_time="162" />
    <data door_type="1" door_level="8" vic_round="10" rest_time="180" />
    <data door_type="1" door_level="8" vic_round="11" rest_time="198" />
    <data door_type="1" door_level="8" vic_round="12" rest_time="216" />
    <data door_type="1" door_level="8" vic_round="13" rest_time="234" />
    <data door_type="1" door_level="8" vic_round="14" rest_time="252" />
    <data door_type="1" door_level="8" vic_round="15" rest_time="270" />
    <data door_type="1" door_level="9" vic_round="1" rest_time="24" />
    <data door_type="1" door_level="9" vic_round="2" rest_time="48" />
    <data door_type="1" door_level="9" vic_round="3" rest_time="72" />
    <data door_type="1" door_level="9" vic_round="4" rest_time="96" />
    <data door_type="1" door_level="9" vic_round="5" rest_time="120" />
    <data door_type="1" door_level="9" vic_round="6" rest_time="144" />
    <data door_type="1" door_level="9" vic_round="7" rest_time="168" />
    <data door_type="1" door_level="9" vic_round="8" rest_time="192" />
    <data door_type="1" door_level="9" vic_round="9" rest_time="216" />
    <data door_type="1" door_level="9" vic_round="10" rest_time="240" />
    <data door_type="1" door_level="9" vic_round="11" rest_time="264" />
    <data door_type="1" door_level="9" vic_round="12" rest_time="288" />
    <data door_type="1" door_level="9" vic_round="13" rest_time="312" />
    <data door_type="1" door_level="9" vic_round="14" rest_time="336" />
    <data door_type="1" door_level="9" vic_round="15" rest_time="360" />
    <data door_type="1" door_level="10" vic_round="1" rest_time="30" />
    <data door_type="1" door_level="10" vic_round="2" rest_time="60" />
    <data door_type="1" door_level="10" vic_round="3" rest_time="90" />
    <data door_type="1" door_level="10" vic_round="4" rest_time="120" />
    <data door_type="1" door_level="10" vic_round="5" rest_time="150" />
    <data door_type="1" door_level="10" vic_round="6" rest_time="180" />
    <data door_type="1" door_level="10" vic_round="7" rest_time="210" />
    <data door_type="1" door_level="10" vic_round="8" rest_time="240" />
    <data door_type="1" door_level="10" vic_round="9" rest_time="270" />
    <data door_type="1" door_level="10" vic_round="10" rest_time="300" />
    <data door_type="1" door_level="10" vic_round="11" rest_time="330" />
    <data door_type="1" door_level="10" vic_round="12" rest_time="360" />
    <data door_type="1" door_level="10" vic_round="13" rest_time="390" />
    <data door_type="1" door_level="10" vic_round="14" rest_time="420" />
    <data door_type="1" door_level="10" vic_round="15" rest_time="450" />
    <data door_type="1" door_level="11" vic_round="1" rest_time="48" />
    <data door_type="1" door_level="11" vic_round="2" rest_time="96" />
    <data door_type="1" door_level="11" vic_round="3" rest_time="144" />
    <data door_type="1" door_level="11" vic_round="4" rest_time="192" />
    <data door_type="1" door_level="11" vic_round="5" rest_time="240" />
    <data door_type="1" door_level="11" vic_round="6" rest_time="288" />
    <data door_type="1" door_level="11" vic_round="7" rest_time="336" />
    <data door_type="1" door_level="11" vic_round="8" rest_time="384" />
    <data door_type="1" door_level="11" vic_round="9" rest_time="432" />
    <data door_type="1" door_level="11" vic_round="10" rest_time="480" />
    <data door_type="1" door_level="11" vic_round="11" rest_time="528" />
    <data door_type="1" door_level="11" vic_round="12" rest_time="576" />
    <data door_type="1" door_level="11" vic_round="13" rest_time="624" />
    <data door_type="1" door_level="11" vic_round="14" rest_time="672" />
    <data door_type="1" door_level="11" vic_round="15" rest_time="720" />
    <data door_type="1" door_level="12" vic_round="1" rest_time="48" />
    <data door_type="1" door_level="12" vic_round="2" rest_time="96" />
    <data door_type="1" door_level="12" vic_round="3" rest_time="144" />
    <data door_type="1" door_level="12" vic_round="4" rest_time="192" />
    <data door_type="1" door_level="12" vic_round="5" rest_time="240" />
    <data door_type="1" door_level="12" vic_round="6" rest_time="288" />
    <data door_type="1" door_level="12" vic_round="7" rest_time="336" />
    <data door_type="1" door_level="12" vic_round="8" rest_time="384" />
    <data door_type="1" door_level="12" vic_round="9" rest_time="432" />
    <data door_type="1" door_level="12" vic_round="10" rest_time="480" />
    <data door_type="1" door_level="12" vic_round="11" rest_time="528" />
    <data door_type="1" door_level="12" vic_round="12" rest_time="576" />
    <data door_type="1" door_level="12" vic_round="13" rest_time="624" />
    <data door_type="1" door_level="12" vic_round="14" rest_time="672" />
    <data door_type="1" door_level="12" vic_round="15" rest_time="720" />
    <data door_type="2" door_level="1" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="1" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="1" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="1" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="1" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="1" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="1" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="1" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="1" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="1" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="1" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="1" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="1" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="1" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="1" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="2" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="2" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="2" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="2" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="2" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="2" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="2" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="2" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="2" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="2" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="2" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="2" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="2" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="2" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="2" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="3" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="3" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="3" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="3" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="3" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="3" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="3" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="3" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="3" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="3" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="3" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="3" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="3" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="3" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="3" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="4" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="4" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="4" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="4" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="4" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="4" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="4" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="4" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="4" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="4" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="4" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="4" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="4" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="4" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="4" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="5" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="5" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="5" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="5" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="5" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="5" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="5" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="5" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="5" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="5" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="5" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="5" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="5" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="5" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="5" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="6" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="6" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="6" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="6" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="6" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="6" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="6" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="6" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="6" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="6" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="6" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="6" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="6" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="6" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="6" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="7" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="7" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="7" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="7" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="7" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="7" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="7" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="7" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="7" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="7" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="7" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="7" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="7" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="7" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="7" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="8" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="8" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="8" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="8" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="8" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="8" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="8" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="8" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="8" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="8" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="8" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="8" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="8" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="8" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="8" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="9" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="9" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="9" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="9" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="9" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="9" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="9" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="9" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="9" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="9" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="9" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="9" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="9" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="9" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="9" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="10" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="10" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="10" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="10" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="10" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="10" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="10" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="10" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="10" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="10" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="10" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="10" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="10" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="10" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="10" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="11" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="11" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="11" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="11" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="11" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="11" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="11" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="11" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="11" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="11" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="11" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="11" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="11" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="11" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="11" vic_round="15" rest_time="450" />
    <data door_type="2" door_level="12" vic_round="1" rest_time="30" />
    <data door_type="2" door_level="12" vic_round="2" rest_time="60" />
    <data door_type="2" door_level="12" vic_round="3" rest_time="90" />
    <data door_type="2" door_level="12" vic_round="4" rest_time="120" />
    <data door_type="2" door_level="12" vic_round="5" rest_time="150" />
    <data door_type="2" door_level="12" vic_round="6" rest_time="180" />
    <data door_type="2" door_level="12" vic_round="7" rest_time="210" />
    <data door_type="2" door_level="12" vic_round="8" rest_time="240" />
    <data door_type="2" door_level="12" vic_round="9" rest_time="270" />
    <data door_type="2" door_level="12" vic_round="10" rest_time="300" />
    <data door_type="2" door_level="12" vic_round="11" rest_time="330" />
    <data door_type="2" door_level="12" vic_round="12" rest_time="360" />
    <data door_type="2" door_level="12" vic_round="13" rest_time="390" />
    <data door_type="2" door_level="12" vic_round="14" rest_time="420" />
    <data door_type="2" door_level="12" vic_round="15" rest_time="450" />
</root>
