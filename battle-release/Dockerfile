FROM centos:7.9.2009


ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 下载gosu二进制文件
RUN curl -o /usr/local/bin/gosu -sSL "https://github.com/tianon/gosu/releases/download/1.17/gosu-amd64"

# 设置gosu可执行权限
RUN chmod +x /usr/local/bin/gosu


COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod a+x /usr/local/bin/docker-entrypoint.sh


WORKDIR /app/bin

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
