<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:id conflate_type=int:合成资源类型 conflate_value=int:合成资源值 conflate_count=int:合成资源数量 cost_type_1=int:消耗资源类型1 cost_value_1=int:消耗资源值1 cost_count_1=int:消耗资源数量1 cost_type_2=int:消耗资源类型2 cost_value_2=int:消耗资源值2 cost_count_2=int:消耗资源数量2 cost_type_3=int:消耗资源类型3 cost_value_3=int:消耗资源值3 cost_count_3=int:消耗资源数量3 -->

<root>
    <data id="1" conflate_type="4" conflate_value="10091" conflate_count="1" cost_type_1="4" cost_value_1="10092" cost_count_1="6" cost_type_2="4" cost_value_2="10093" cost_count_2="6" cost_type_3="0" cost_value_3="0" cost_count_3="0" />
    <data id="2" conflate_type="4" conflate_value="10094" conflate_count="1" cost_type_1="4" cost_value_1="10095" cost_count_1="6" cost_type_2="4" cost_value_2="10096" cost_count_2="6" cost_type_3="0" cost_value_3="0" cost_count_3="0" />
    <data id="3" conflate_type="4" conflate_value="10097" conflate_count="1" cost_type_1="4" cost_value_1="10098" cost_count_1="6" cost_type_2="4" cost_value_2="10099" cost_count_2="6" cost_type_3="0" cost_value_3="0" cost_count_3="0" />
    <data id="4" conflate_type="4" conflate_value="10100" conflate_count="1" cost_type_1="4" cost_value_1="10101" cost_count_1="4" cost_type_2="4" cost_value_2="10102" cost_count_2="4" cost_type_3="4" cost_value_3="10103" cost_count_3="4" />
    <data id="5" conflate_type="4" conflate_value="10104" conflate_count="1" cost_type_1="4" cost_value_1="10105" cost_count_1="4" cost_type_2="4" cost_value_2="10106" cost_count_2="4" cost_type_3="4" cost_value_3="10107" cost_count_3="4" />
    <data id="6" conflate_type="4" conflate_value="10108" conflate_count="1" cost_type_1="4" cost_value_1="10109" cost_count_1="6" cost_type_2="4" cost_value_2="10110" cost_count_2="6" cost_type_3="0" cost_value_3="0" cost_count_3="0" />
    <data id="7" conflate_type="4" conflate_value="10111" conflate_count="1" cost_type_1="4" cost_value_1="10112" cost_count_1="6" cost_type_2="4" cost_value_2="10113" cost_count_2="6" cost_type_3="0" cost_value_3="0" cost_count_3="0" />
    <data id="8" conflate_type="4" conflate_value="10114" conflate_count="1" cost_type_1="4" cost_value_1="10115" cost_count_1="6" cost_type_2="4" cost_value_2="10116" cost_count_2="6" cost_type_3="0" cost_value_3="0" cost_count_3="0" />
    <data id="9" conflate_type="4" conflate_value="10117" conflate_count="1" cost_type_1="4" cost_value_1="10118" cost_count_1="4" cost_type_2="4" cost_value_2="10119" cost_count_2="4" cost_type_3="4" cost_value_3="10120" cost_count_3="4" />
    <data id="10" conflate_type="4" conflate_value="10121" conflate_count="1" cost_type_1="4" cost_value_1="10122" cost_count_1="4" cost_type_2="4" cost_value_2="10123" cost_count_2="4" cost_type_3="4" cost_value_3="10124" cost_count_3="4" />
</root>
