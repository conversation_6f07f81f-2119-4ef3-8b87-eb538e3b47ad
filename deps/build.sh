#!/bin/bash

#因为github文件大小限制
if [ ! -f "protobuf/lib/libprotoc.a" ]; then
  tar -xf protobuf/lib/libprotoc.a.tar -C protobuf/lib/
fi

if [ ! -f "$HOME/go/bin/protoc-gen-go" ]  && [ ! -f "$GOPATH/go/bin/protoc-gen-go" ]; then
  go install github.com/golang/protobuf/protoc-gen-go@v1.3.2
fi

if [ ! -f "$HOME/go/bin/protoc-gen-gofast" ] && [ ! -f "$GOPATH/go/bin/protoc-gen-gofast" ]; then
  go install github.com/gogo/protobuf/protoc-gen-gofast@v1.3.1
fi

