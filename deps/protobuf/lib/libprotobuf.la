# libprotobuf.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.2
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libprotobuf.so.15'

# Names of this library.
library_names='libprotobuf.so.15.0.1 libprotobuf.so.15 libprotobuf.so'

# The name of the static archive.
old_library='libprotobuf.a'

# Linker flags that can not go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -lpthread -lz'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libprotobuf.
current=15
age=0
revision=1

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/tools/game-backend/static/deps/protobuf/lib'
