<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:等级id task_type_id1=int:类型类型1 value1=int:任务参数1 task_type_id2=int:类型类型2 value2=int:任务参数2 reward_up=int:奖励提升万分比 group=int:等级组 -->

<root>
    <data id="1" task_type_id1="2801000" value1="10315" task_type_id2="3804000" value2="5" reward_up="10000" group="11" />
    <data id="2" task_type_id1="2801000" value1="10640" task_type_id2="3804000" value2="15" reward_up="10000" group="12" />
    <data id="3" task_type_id1="2801000" value1="11140" task_type_id2="3805000" value2="25" reward_up="10000" group="13" />
    <data id="4" task_type_id1="2801000" value1="11640" task_type_id2="3805000" value2="40" reward_up="10000" group="14" />
    <data id="5" task_type_id1="2801000" value1="12160" task_type_id2="3806000" value2="100" reward_up="10000" group="15" />
    <data id="6" task_type_id1="2801000" value1="12660" task_type_id2="3807000" value2="100" reward_up="10000" group="16" />
    <data id="7" task_type_id1="2801000" value1="13660" task_type_id2="3808000" value2="100" reward_up="10000" group="17" />
    <data id="8" task_type_id1="2801000" value1="14760" task_type_id2="3808000" value2="110" reward_up="10000" group="18" />
    <data id="9" task_type_id1="2801000" value1="15160" task_type_id2="3808000" value2="120" reward_up="10000" group="19" />
    <data id="10" task_type_id1="2801000" value1="15260" task_type_id2="3808000" value2="130" reward_up="10000" group="20" />
    <data id="11" task_type_id1="2801000" value1="15360" task_type_id2="3808000" value2="140" reward_up="10000" group="21" />
    <data id="12" task_type_id1="2801000" value1="15460" task_type_id2="3808000" value2="150" reward_up="10000" group="22" />
    <data id="13" task_type_id1="2801000" value1="15560" task_type_id2="3808000" value2="160" reward_up="10000" group="23" />
    <data id="14" task_type_id1="2801000" value1="15660" task_type_id2="3808000" value2="170" reward_up="10000" group="24" />
    <data id="15" task_type_id1="0" value1="0" task_type_id2="0" value2="0" reward_up="10000" group="25" />
</root>
