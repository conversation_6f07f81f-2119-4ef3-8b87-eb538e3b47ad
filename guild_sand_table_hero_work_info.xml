<?xml version="1.0" encoding="UTF-8"?>
<!--id=int:ID build_type=int:建筑类型 hero_work_num=int:可任命英雄数量 crystal_slot_limit=int:需要的水晶格子数量 -->

<root>
    <data id="201" build_type="2" hero_work_num="1" crystal_slot_limit="46" />
    <data id="202" build_type="2" hero_work_num="2" crystal_slot_limit="49" />
    <data id="203" build_type="2" hero_work_num="3" crystal_slot_limit="52" />
    <data id="204" build_type="2" hero_work_num="4" crystal_slot_limit="55" />
    <data id="205" build_type="2" hero_work_num="5" crystal_slot_limit="58" />
    <data id="301" build_type="3" hero_work_num="1" crystal_slot_limit="47" />
    <data id="302" build_type="3" hero_work_num="2" crystal_slot_limit="50" />
    <data id="303" build_type="3" hero_work_num="3" crystal_slot_limit="53" />
    <data id="304" build_type="3" hero_work_num="4" crystal_slot_limit="56" />
    <data id="305" build_type="3" hero_work_num="5" crystal_slot_limit="59" />
    <data id="401" build_type="4" hero_work_num="1" crystal_slot_limit="48" />
    <data id="402" build_type="4" hero_work_num="2" crystal_slot_limit="51" />
    <data id="403" build_type="4" hero_work_num="3" crystal_slot_limit="54" />
    <data id="404" build_type="4" hero_work_num="4" crystal_slot_limit="57" />
    <data id="405" build_type="4" hero_work_num="5" crystal_slot_limit="60" />
</root>
