<?xml version="1.0" encoding="UTF-8"?>
<!--key=string:配置值 type=int:类型 value=int:值 count=int:数量 -->

<root>
    <data key="DEFAULT_KEY_NUM" type="0" value="0" count="180" />
    <data key="DEFAULT_ENERGY_NUM" type="9" value="9088" count="20" />
    <data key="ENERGY_RECOVER_TIME" type="0" value="0" count="45" />
    <data key="KEY_MAX" type="0" value="0" count="200" />
    <data key="DAILY_KEY_RECOVER" type="0" value="0" count="1" />
    <data key="ENERGY_MAX" type="0" value="0" count="140" />
    <data key="ENERGY_ITEM" type="4" value="10078" count="1" />
    <data key="ENERGY_ITEM_ADD_NUM" type="9" value="9088" count="5" />
    <data key="ENERGY_BUY_PRICE" type="2" value="0" count="50" />
    <data key="HERO_BUFF" type="0" value="0" count="44001002" />
    <data key="HERO_1" type="0" value="0" count="12010" />
    <data key="HERO_2" type="0" value="0" count="43010" />
    <data key="HERO_3" type="0" value="0" count="53010" />
    <data key="HERO_4" type="0" value="0" count="53011" />
    <data key="ENERGY_TOKEN" type="9" value="9088" count="0" />
    <data key="ENERGY_TOKEN_DEL_1" type="9" value="9058" count="0" />
    <data key="ENERGY_TOKEN_DEL_2" type="9" value="9068" count="0" />
    <data key="POSITION_MIN" type="0" value="0" count="2" />
</root>
