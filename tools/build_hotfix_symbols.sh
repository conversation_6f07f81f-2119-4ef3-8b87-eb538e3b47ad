#!/bin/bash

# 检查参数数量是否正确
if [ "$#" -ne 4 ]; then
    echo "Usage: $0 <directory> <output_file>"
    exit 1
fi

# 指定要遍历的目录
directory=$1

# 指定输出的文件名
output_file=$2

package_suffix=$3
name_suffix=$4




echo "package symbols" > $output_file

# 遍历指定目录下的文件夹并将结果输出到指定文件，并拼接后缀
for dir in $directory/*; do
    if [ -d "$dir" ]; then
        dir_name=$(basename "$dir")
        echo "//go:generate yaegi extract ${package_suffix}${dir_name} ${name_suffix}${dir_name}" >> $output_file
    fi
done

echo "logic command包已输出到 $output_file"
