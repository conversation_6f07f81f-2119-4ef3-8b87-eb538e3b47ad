#/bin/bash
#用于快速生成robot中协议测试代码模板

#使用方法：
#./create_robot_code.sh 模块名 模块描述 开始协议id 结束协议id
#                         ^-----------------------------------------------  多个单词可使用下划线拼接，比如:npc_room
#                                 ^---------------------------------------  模块中文描述：集贤居
#                                          ^------------------------------  cl中的协议开始id
#                                                     ^-------------------  cl中的协议结束id

#注意点：
#1.脚本会自动完成相应的模块添加，协议注册，基本不需要修改其他老代码文件，
# 只需要根据自身需求调整模块里代码即可，比如生成的npc_room.go。
# 修改TODO行，并在注有`edit here`处添加相应代码

#2.协议生成后可能需要手动调整的代码：
# 将L2CInitRet函数添加到初始化请求返回函数中，可参考arena.go进行添加


SUCCESS_COLOR="\033[32m"
FAILED_COLOR="\033[31m"
WARN_COLOR="\033[33m"
END_COLOR="\033[0m"

if [ $# -lt 4 ]; then
	echo -e "${WARN_COLOR}${0} 模块名 模块描述 开始协议id 结束协议id${END_COLOR}"
	exit
fi

module=$1
moduledes=$2
st=$3
ed=$4

echo -e "${SUCCESS_COLOR}开始生成代码文件 id范围:[$st,$ed] 模块名:$module${END_COLOR}"
echo "------------------------------------------------------"

csfile="./protos/out/cl.proto"
module_file="./src/app/tools/robot/${module}.go"
command_file="./src/app/tools/robot/${module}_command_register.go"
config_file="./config/robot.xml"
robot_go_file="./src/app/tools/robot/robot.go"
command_register_file="./src/app/tools/robot/robot_command_register.go"
action_moduler_go_file="./src/app/tools/robot/action_moduler.go"

#脚本执行目录
basedir=`basename $PWD`
if [[ ${basedir} == "tools" ]]; then
	csfile="../protos/out/cl.proto"
	module_file="../src/app/tools/robot/${module}.go"
	command_file="../src/app/tools/robot/${module}_command_register.go"
	config_file="../config/robot.xml"
	robot_go_file="../src/app/tools/robot/robot.go"
	command_register_file="../src/app/tools/robot/robot_command_register.go"
	action_moduler_go_file="../src/app/tools/robot/action_moduler.go"
fi

msgnames=`cat $csfile |grep "MSG_"|awk '{print $1}' FS=";"|grep -v "//"|awk -v st="$st" -v ed="$ed" '{if ($2 >= st && $2 <= ed) print $1;}' FS="="`
clientnames=`cat $csfile |grep "MSG_C2L_"|awk '{print $1}' FS=";"|grep -v "//"|awk -v st="$st" -v ed="$ed" '{if ($2 >= st && $2 <= ed) print $1;}' FS="="`
servernames=`cat $csfile |grep "MSG_L2C_"|awk '{print $1}' FS=";"|grep -v "//"|awk -v st="$st" -v ed="$ed" '{if ($2 >= st && $2 <= ed) print $1;}' FS="="`

echo ${msgnames[@]}

if [[ ${msgnames[@]} == "" ]]; then
	echo "没有找到指定范围的消息!"
	exit
fi

#代码中的module名字处理，将下划线转换成驼峰（首字符大写）
Module=
IFS=$'\n' read -d "" -ra arr <<< "${module//_/$'\n'}"
for word in ${arr[@]}
do
	Module=${Module}${word^}
done

#类指针
This=${module:0:1} #取第一个字符

create_module_file() {
	echo "
	package main
	import (
		\"app/protos/out/cl\"
		cret \"app/protos/out/ret\"

		l4g \"github.com/ivanabc/log4go\"
	)

	" >> $module_file
	
	#Action
	echo "
	type ${Module}Func func(*${Module})

	var g${Module}Actions []${Module}Func

	func Init${Module}Actions() { " >> $module_file

	for msg in $clientnames
	do
		func_name=`echo ${msg}|sed 's/MSG//;s/_//g'`
		echo "g${Module}Actions = append(g${Module}Actions, (*${Module}).${func_name})" >> $module_file
	done
	echo "}

	" >> $module_file

	echo "
	type ${Module} struct {
		*UserLoad
	}

	func New${Module}(r *Robot) *${Module} {
	    ${This} := &${Module}{
			UserLoad: NewUserLoad(r),
		}
	    return ${This}
	}

	//断线重连重置数据
	func (${This} *${Module}) reset() {
		${This}.load.reset()

		//edit here
	}

	func (${This} *${Module}) RandAction() {
		switch ${This}.load.state() {
		case dataLoadNone:
			${This}.C2LInit()
		case dataLoading:
			${This}.C2LRetryInit()
		default:
			if length := len(g${Module}Actions); length != 0 {
				g${Module}Actions[${This}.u.rd.Intn(length)](${This})
			}
		}
	}

	//初始化请求模块数据 并添加流程锁，防止重复拉取
	func (${This} *${Module}) C2LInit() {
	    ${This}.load.Lock()
	 	${This}.load.setState(dataLoading)

		//${This}.C2L${Module}FlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	}

	//请求协议返回后调用
	//TODO:需要手动添加到初始化协议返回，一般是Info协议
	func (${This} *${Module}) L2CInitRet(ret uint32) {
		if ${This}.load.state() == dataLoading {
			${This}.load.UnLock()
			if ret == uint32(cret.RET_OK) {
				${This}.load.setState(dataLoaded)
			} else {
				${This}.load.setState(dataLoadNone)
			}
		}
	}

	//初始化请求超时 重新发起请求
	func (${This} *${Module}) C2LRetryInit() {
	    if ${This}.load.Lock() {
        	//${This}.C2L${Module}FlushInfo()	//TODO:修改为初始化请求函数，比如带有Info的函数
	    }
	}

	" >> $module_file

	#协议函数
	for msg in $msgnames
	do
		msg_name=`echo ${msg}|sed 's/MSG//;s/_//'`
		func_name=`echo ${msg}|sed 's/MSG//;s/_//g'`
		if [[ ${func_name} =~ "C2L" ]]; then
			echo "
			func ($This *${Module}) $func_name() {
				cmsg := &cl.${msg_name}{}
				//edit here
				${This}.u.SendCmd(uint32(cl.ID_${msg}), cmsg)
			}
			" >> $module_file
		elif [[ ${func_name} =~ "L2C" ]]; then
			echo "
			func ($This *${Module}) $func_name(recv *cl.${msg_name}) {
				l4g.Debug(\"[${Module}] %s ${func_name} ret:%s, recv:%+v\",
			   		${This}.u.UUID(), cret.RET_name[int32(recv.Ret)], recv)

				if recv.Ret != uint32(cret.RET_OK) {
					return
				}

				//edit here
			}
			" >> $module_file
		fi
	done
	gofmt -w -s $module_file
	if [ -f $module_file ];then
		echo -e "${SUCCESS_COLOR}生成文件:$module_file${END_COLOR}"
	fi
}

create_command_file() {
	echo "
	package main
	import (
		\"app/protos/out/cl\"
		\"gitlab.qdream.com/kit/sea/zebra/parse\"

		\"github.com/golang/protobuf/proto\"

		l4g \"github.com/ivanabc/log4go\"
	)
	" >> $command_file

	echo "func InitRobot${Module}Command() {" >> $command_file

	#InitXXXX	
	for msg in $servernames
	do
		class_name=`echo ${msg}|sed 's/MSG//;s/_//g'`
		class_name=${class_name}"Command"
		echo "gRobotCommandM.Register(uint32(cl.ID_${msg}), ${class_name}{})" >> $command_file
	done

	echo "}
	" >> $command_file

	#class
	for msg in $servernames
	do
		msg_name=`echo ${msg}|sed 's/MSG//;s/_//'`
		func_name=`echo ${msg}|sed 's/MSG//;s/_//g'`
		class_name=${func_name}"Command"

		echo "
		type ${class_name} struct {
		}

		func (c ${class_name}) Execute(robot *Robot, ph *parse.PackHead, data []byte) bool {
			recv := &cl.${msg_name}{}
			if err := proto.Unmarshal(data, recv); err != nil {
				l4g.Error(\"${class_name} unmarshal error: %s\", err)
				return false
			}
			robot.${Module,}.${func_name}(recv)
			return recv.Ret == uint32(ret.RET_OK)	
		}
		" >> $command_file
	done
	gofmt -w -s $command_file
	if [ -f $command_file ];then
		echo -e "${SUCCESS_COLOR}生成文件:$command_file${END_COLOR}"
	fi
}

register_module(){
	#配置插入新模块
	sed -i "/<\/action>/i\    <\!--${moduledes}-->\\n    <item name=\"${module}\" prob=\"1\"\/>" ${config_file}
	echo -e "${config_file}新增：${SUCCESS_COLOR}<item name=\"${module}\" prob=\"1\"/>${END_COLOR}完成"

	#action_moduler.go
	sed -i "/\/\/<action module end>（这行不要删除不要修改，用于脚本识别插入）/i\\\t\"${module}\":       {Init${Module}Actions, func(r *Robot) { r.${Module,}.RandAction() }}," ${action_moduler_go_file}
	gofmt -w -s ${action_moduler_go_file}
	echo -e "${action_moduler_go_file}新增Action：${SUCCESS_COLOR}${module}${END_COLOR}完成"

	#robot.go添加模块
	sed -i "/\/\/<module define end>（这行不要删除不要修改，用于脚本识别插入）/i\\\t${Module,} *${Module}" ${robot_go_file}
	sed -i "/\/\/<InitModule end>（这行不要删除不要修改，用于脚本识别插入）/i\\\tr.${Module,} = New${Module}(r)" ${robot_go_file}
	sed -i "/\/\/<resetModule end>（这行不要删除不要修改，用于脚本识别插入）/i\\\tr.${Module,}.reset()" ${robot_go_file}
	gofmt -w -s ${robot_go_file}
	echo -e "${robot_go_file}新增模块：${SUCCESS_COLOR}${Module,}${END_COLOR}完成"

	#command注册
	sed -i "/\/\/<InitRobotCommand end>（这行不要删除不要修改，用于脚本识别插入）/i\\\t\/\/${moduledes}\\n\\tInitRobot${Module}Command()" ${command_register_file}
	echo -e "${command_register_file}新增协议注册：${SUCCESS_COLOR}InitRobot${Module}Command()${END_COLOR}完成"
}

create_module_file
create_command_file
register_module

echo "------------------------------------------------------"
if [ $? -eq 0 ]; then
	echo -e "$SUCCESS_COLOR 生成代码完成，请添加相应逻辑到${module_file}，并修改TODO行！ $END_COLOR"
else
	echo -e "$FAILED_COLOR 执行脚本出错！$END_COLOR"
fi
