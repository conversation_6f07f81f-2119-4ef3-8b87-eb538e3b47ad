#!/bin/bash

# 传递Go文件名、函数名和标记作为参数

go_file_name="./src/app/logic/character/user.go"
proto_file_name="./protos/in/db.proto"

global_end_line=1
read_lines_until() {
  local file_name=$1
  local start_line=$2
  local target_line=$3

  IFS= # 设置IFS为空，禁用字段分隔符
  local end_line=1
  while read -r line; do
    if [ "$end_line" -ge "$start_line" ]; then
      if [ "$line" = "$target_line" ]; then
        break
      fi
    fi

    ((end_line++))
  done < "$file_name"
	global_end_line=$end_line
	echo "global_end_line:$global_end_line"
}

global_count=0

get_tag_count() {
	local file_name=$1
	local start_line=$2
	local end_line=$3
	local tag=$4
	global_count=$(sed -n "$start_line,$end_line"p "$file_name" | grep -c "$tag")
}


go_function_start_line=0
proto_message_start_line=0

go_function_end_line=0
proto_message_end_line=0


global_start_line=1

get_start_line() {
	local file_name=$1
	local start_regex=$2
	global_start_line=$(grep -n -E "$start_regex" "$file_name" | cut -d ":" -f 1)
}

get_start_line $go_file_name "func \(u \*User\) clone\(\) \*db.User {"
go_function_start_line=$global_start_line
get_start_line $proto_file_name "message User {"
proto_message_start_line=$global_start_line


echo "go user clone函数开始行:$go_funciton_start_line"
echo "proto message user 开始行:$proto_message_start_line"

# 如果找不到函数定义，则输出错误信息并退出
if [ -z "$go_function_start_line" ]; then
  echo "找不到go函数 的定义。"
  exit 1
fi

if [ -z "$proto_message_start_line" ]; then
  echo "找不到proto message 的定义。"
  exit 1
fi

read_lines_until $go_file_name $go_function_start_line "}"
go_function_end_line=$global_end_line

read_lines_until $proto_file_name $proto_message_start_line "}"
proto_message_end_line=$global_end_line


echo "go user clone函数结束行:$go_funciton_end_line"
echo "proto message user 结束行:$proto_message_end_line"


go_tag_count=0
proto_tag_count=0
get_tag_count $go_file_name $go_function_start_line $go_function_end_line "Clone"
go_tag_count=$global_count
get_tag_count $proto_file_name $proto_message_start_line $proto_message_end_line "^[[:blank:]]*Op"
proto_tag_count=$global_count

echo "proto op个数:$proto_tag_count"
echo "user clone个数:$go_tag_count"
if [ $proto_tag_count != $go_tag_count ]; then
	echo "proto的op数量和user的clone数量不一致"
	exit 1
fi
