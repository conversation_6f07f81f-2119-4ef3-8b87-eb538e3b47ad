package character

type XXX struct {
	owner *User
	data  *proto.XXX
}

func newXXX(user *User) *XXX {
	return &XXX{
		owner: user,
	}
}

func (x *XXX) Owner() *User {
	return x.owner
}

// 加载数据
func (x *XXX) load(data *proto.XXX) {
	x.data = data
}

// 上线时数据的处理
func (x *XXX) online(now int64, fromCache bool) {
}

// 定时处理
func (x *XXX) onTimer(now int64) {
}

// 设置保存标签, 保存数据
func (x *XXX) save() {
	x.Owner().dbUser.Module.XXX = x.data
	x.Owner().setSaveTag(saveTagModule)
}

// clone内存数据
func (x *XXX) clone() *proto.XXX {
	return x.data.Clone()
}

// 数据更新需要通知客户端
func (x *XXX) Notify() {
	/*
		msg := &cl.L2C_{}
		x.Owner().SendCmdToClient(cl.ID_MSG_L2C_, msg)
	*/
}
