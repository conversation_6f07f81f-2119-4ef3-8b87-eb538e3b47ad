#!/bin/bash

WARN_COLOR="\033[31m"
END_COLOR="\033[0m"

if [ $# -lt 2 ]; then
  echo -e "$WARN_COLOR 生成character module 代码$END_COLOR"
  echo -e "$WARN_COLOR ./module_template.sh module名字 存储类型$END_COLOR"
  echo -e "$WARN_COLOR example: ./module_template.sh workshop cl$END_COLOR"
	exit 1
fi

FileName=$1
Receiver=${FileName:0:1}

StructName=''
StructArray=(${FileName//\_/ })
for i in "${!StructArray[@]}"; do
	StructName=$StructName${StructArray[i]^};
done

basedir=`basename $PWD`
if [[ ${basedir} == "tools" ]]; then 
  FileName="../src/app/logic/character/"$FileName.go
else
	FileName="src/app/logic/character/"$FileName.go
fi

echo "file name: "$FileName
echo "struct name: "$StructName
echo "struct receiver name: "$Receiver

if [[ ${basedir} == "tools" ]]; then 
  cp module_template.go $FileName
else
  cp tools/module_template.go $FileName
fi

sed -i "s|XXX|$StructName|g; s|proto|$2|g; s|(x |($Receiver |g; s|x\.|$Receiver\.|g" $FileName

goimports -w $FileName
gofmt -w -s $FileName
