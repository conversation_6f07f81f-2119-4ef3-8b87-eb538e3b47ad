#! /bin/bash

export PATH=$PROJECT_PATH/tools:$PATH
export REDISOP_OUTPUT_PATH=$PROJECT_PATH/src/app/logic/db/redisop

rm -f $REDISOP_OUTPUT_PATH/cl_*
rm -f $REDISOP_OUTPUT_PATH/db_*

redisop -proto=$PROJECT_PATH/protos/in/db.proto -protoImport=app/protos/in/db -goDir=$REDISOP_OUTPUT_PATH -compBytes=1024 -compFlag=snappy -protoBuffSize=1024
redisop -proto=$PROJECT_PATH/protos/out/cl.proto -protoImport=app/protos/out/cl -goDir=$REDISOP_OUTPUT_PATH -compBytes=1024 -compFlag=snappy -protoBuffSize=1024
goimports -w $PROJECT_PATH/src/app/logic/db/redisop/

# 检查数据库命名是否重复
function CheckRepeatedName() {
  SUCCESS_COLOR="\033[42;37m"
  FAILED_COLOR="\033[41;37m"
  END_COLOR="\033[0m"
  
  result=`awk '{if(NF==3){if($3 in all){print "FAILED WHEN CHECK REPEATED NAME: "$3}else{all[$3]=""}}}' $PROJECT_PATH/src/app/logic/db/redis_key.go $PROJECT_PATH/src/app/logic/db/redisop/redisop_key_cl.go $PROJECT_PATH/src/app/logic/db/redisop/redisop_key_db.go`
  if [[ $result == "" ]];then
    echo -e $SUCCESS_COLOR"SUCCESS WHEN CHECK REPEATED NAME"$END_COLOR
  else
    echo -e $FAILED_COLOR$result$END_COLOR
  fi
}

CheckRepeatedName
