#!/usr/bin/env bash
#消息注册代码 service/commands.go 配合create_msg.sh一起使用

SUCCESS_COLOR="\033[32m"
WARN_COLOR="\033[33m"
END_COLOR="\033[0m"

if [ $# -lt 2 ]; then
	echo -e "$WARN_COLOR $0 模块名 模块描述 $END_COLOR"
	exit
fi

#模块关键字(必须英文)和描述
module=$1
moduleDes=$2

#包名
packagename=
camelname=
read -d "" -ra arr <<< "${module//_/ }"
for word in ${arr[@]}
do
	echo $word
	packagename=${packagename}${word,,}
	camelname=${camelname}${word^}
done
cmdfile="./src/app/logic/service/commands.go"
tmpcmdfile="./src/app/logic/service/commands_`date +%s`.go"

basedir=`basename $PWD`
if [[ ${basedir} == "tools" ]];then
	cmdfile="../src/app/logic/service/commands.go"
	tmpcmdfile="../src/app/logic/service/commands_`date +%s`.go"
fi

#检查是否重复注册
num=`cat src/app/logic/service/commands.go |grep "CmdsType${camelname}" |wc -l`
if [ $num -ne 0 ]; then
	echo -e "$WARN_COLOR CmdsType${camelname}已存在，不再重复注册！ $END_COLOR"
	exit 0
fi

echo "package service" >> $tmpcmdfile
echo "" >> $tmpcmdfile

#import
import_lines=`sed -n '/^import (/,/^)/p' $cmdfile`
IFS=$'\n'
for line in $import_lines
do
	if [[ ${line} = *"l4g"* ]]; then
		echo "c$packagename \"app/logic/command/${packagename}\"" >> $tmpcmdfile
		echo "" >> $tmpcmdfile
	fi
	echo $line >> $tmpcmdfile
done
echo -e "\n" >> $tmpcmdfile

#const
CmdsType_lines=`sed -n '/^const (/,/^)/p' $cmdfile`
count=0
for line in $CmdsType_lines
do
	if [[ $line = ")"* ]]; then
		echo "CmdsType${camelname} = $count //${moduleDes}" >> $tmpcmdfile
	else
		let count++
	fi
	echo $line >> $tmpcmdfile
done
echo -e "\n" >> $tmpcmdfile

#func cmdsTypeString
cmdsTypeString_lines=`sed -n '/^func cmdsTypeString(tp int) string {/,/^}/p' $cmdfile`
for line in $cmdsTypeString_lines
do
	if [[ $line = *"default"* ]]; then
		echo "case CmdsType${camelname}:" >> $tmpcmdfile
		echo "	return \"${moduleDes}\"" >> $tmpcmdfile
	fi
	echo $line >> $tmpcmdfile
done
echo -e "\n" >> $tmpcmdfile

#func (l *LogicService) registerCmds() {}
registerCmds_lines=`sed -n '/^func (l \*LogicService) registerCmds() {/,/^}/p' $cmdfile`
for line in $registerCmds_lines
do
	if [[ $line = "}"* ]];then
		echo "l.RegisterGatewayCmds(CmdsType${camelname}, true)" >> $tmpcmdfile
	fi
	echo $line >> $tmpcmdfile
	if [[ $line = *"credis.Init"* ]];then
		echo -e "\n" >> $tmpcmdfile
	fi
done
echo -e "\n" >> $tmpcmdfile

#func (l *LogicService) RegisterGatewayCmds(tp int, open bool) { }
RegisterGatewayCmds_lines=`sed -n '/^func (l \*LogicService) RegisterGatewayCmds(tp int, open bool) {/,/^}/p' $cmdfile`
for line in $RegisterGatewayCmds_lines
do
	if [[ $line = *"default"* ]]; then
		echo "case CmdsType${camelname}:" >> $tmpcmdfile
		echo "c${packagename}.Init(l.gatewayCmds, open)" >> $tmpcmdfile
	fi
	echo $line >> $tmpcmdfile
done

gofmt -w -s $tmpcmdfile
mv $tmpcmdfile $cmdfile

echo -e "$SUCCESS_COLOR 新注册协议id:CmdsType${camelname}$END_COLOR"
