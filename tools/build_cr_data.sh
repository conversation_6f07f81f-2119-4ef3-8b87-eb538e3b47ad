#! /bin/bash

export PATH=$PROJECT_PATH/tools:$PATH
export REDISOP_OUTPUT_PATH=$PROJECT_PATH/src/app/cross/db/redisop
export REDISOP_CROSSMASTER_OUTPUT_PATH=$PROJECT_PATH/src/app/crossmaster/db/redisop

rm -f $REDISOP_OUTPUT_PATH/cr_*
rm -f $REDISOP_CROMSSMASTER_OUTPUT_PATH/cr_*

redisop -proto=$PROJECT_PATH/protos/in/cr.proto -protoImport=app/protos/in/cr -goDir=$REDISOP_OUTPUT_PATH -compBytes=1024 -compFlag=snappy -protoBuffSize=1024
goimports -w $PROJECT_PATH/src/app/cross/db/redisop/

cp $PROJECT_PATH/src/app/cross/db/redisop/{cr.go,cr_ActBase.go,cr_ActPartition.go,compress.go,redisop_key_cr.go}  $PROJECT_PATH/src/app/crossmaster/db/redisop/

# 检查数据库命名是否重复
function CheckRepeatedName() {
  SUCCESS_COLOR="\033[42;37m"
  FAILED_COLOR="\033[41;37m"
  END_COLOR="\033[0m"
  
  result=`awk '{if(NF==3){if($3 in all){print "FAILED WHEN CHECK REPEATED NAME: "$3}else{all[$3]=""}}}' $PROJECT_PATH/src/app/cross/db/redisop/redisop_key_cr.go`
  if [[ $result == "" ]];then
    echo -e $SUCCESS_COLOR"SUCCESS WHEN CHECK REPEATED NAME"$END_COLOR
  else
    echo -e $FAILED_COLOR$result$END_COLOR
  fi  
}

CheckRepeatedName
