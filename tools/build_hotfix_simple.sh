#! /bin/bash


USER_NAME=`whoami`
CUR_PATH=$(pwd)

if [ $# -ge 1 ]; then
  name=$1
else
  echo "para error $# $@"
  exit 1
fi


cd $PROJECT_PATH/src/app/symbols && go generate -run="character"
cd $PROJECT_PATH/src/app/symbols && go generate -run="${name}"
cd $PROJECT_PATH/src/app/symbols && go generate -run="app/protos/in/"
cd $PROJECT_PATH/src/app/symbols && go generate -run="app/protos/out/"

sed -i "s/\*db.RedisActor/\*rdb.RedisActor/g"  $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i 's/rdb \"app\/logic\/db\"/\"app\/logic\/db\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i 's/\"app\/logic\/db\"/rdb \"app\/logic\/db\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-cross-activity.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-cross-activity.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-cross-activity.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-logic-activity.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-activity.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-activity.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-logic-character.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-character.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-character.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go



cd $PROJECT_PATH/src/app/symbols && gofmt -w -s *.go
