#!/bin/bash

function echoC() {
  SUCCESS_COLOR="\033[42;37m"
  END_COLOR="\033[0m"
  #echo -e $SUCCESS_COLOR">>>   $1"$END_COLOR
  echo ">>>   $1"
}

# change dir to src/app
cd $1 
DIR=`pwd`
echoC "Project: $DIR"

function findgo() {
find $DIR \( -path "$DIR/tools" -o -path "$DIR/protos" -o -path "$DIR/logic/db/redisop" -o -path "$DIR/logic/goxml" -o -path "$DIR/logic/battle/cmd" \) -prune -o -iname "*.go" -print
  exit 0
}

#文件数量
FILES=`findgo | wc -l`
echoC "Files: $FILES"

#文件行数过长
fileLen=`findgo | xargs wc -l | awk '{if($1 > 1000 && $2 != "total" ) {print $0}}' | wc -l`
echoC "文件行数(>1000): $fileLen"
findgo | xargs wc -l | awk '{if($1 > 1000 && $2 != "total" ) {print $0}}'

#函数总量
funcs=`findgo | xargs grep "func " | wc -l`
echoC "Functions: $funcs"

golangci-lint-1.29 run logic/... postman/... service/... --enable-all  --timeout=10m  --max-issues-per-linter=0 --max-same-issues=0 --out-format=tab -D wsl,godox,godot,gofumpt --tests=false --sort-results > /tmp/golangci-lint.txt 
sort -k 2 < /tmp/golangci-lint.txt > /tmp/golangci-lint.sort.txt

funlenSize=`grep "funlen" /tmp/golangci-lint.sort.txt | wc -l`
echoC "Check funlen: $funlenSize"
grep "funlen" /tmp/golangci-lint.sort.txt

complexitySize=`grep "cognitive complexity" /tmp/golangci-lint.sort.txt | wc -l`
echoC "Check cognitive complexity: $complexitySize"
grep "cognitive complexity" /tmp/golangci-lint.sort.txt

echoC "Found `expr $fileLen + $funlenSize + $complexitySize` issues across $FILES files and $funcs functions"
