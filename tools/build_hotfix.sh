#! /bin/bash

if [ ! -f $HOME/bin/yaegi ]; then
	go install github.com/traefik/yaegi/cmd/yaegi@v0.16.1
fi

cd $PROJECT_PATH/src/app/symbols && find . -maxdepth 1 -name '*.go' ! -name 'symbols.go' -exec rm {} +

sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/logic/command/ $PROJECT_PATH/src/app/symbols/symbols_logic_command.go app/logic/command/ lc
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/logic/activity/ $PROJECT_PATH/src/app/symbols/symbols_logic_activity.go app/logic/activity/ la
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/logic/ $PROJECT_PATH/src/app/symbols/symbols_logic.go app/logic/ ls
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/protos/out/ $PROJECT_PATH/src/app/symbols/symbols_protos_out.go app/protos/out/ po
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/protos/in/ $PROJECT_PATH/src/app/symbols/symbols_protos_in.go app/protos/in/ pi
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/cross/ $PROJECT_PATH/src/app/symbols/symbols_cross.go app/cross/ cs
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/cross/command/ $PROJECT_PATH/src/app/symbols/symbols_cross_command.go app/cross/command cc
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/cross/activity/ $PROJECT_PATH/src/app/symbols/symbols_cross_activity.go app/cross/activity ca
sh $PROJECT_PATH/tools/build_hotfix_symbols.sh $PROJECT_PATH/src/app/crossmaster/ $PROJECT_PATH/src/app/symbols/symbols_crossmaster.go app/crossmaster/ cm

cd $PROJECT_PATH/src/app/symbols && go generate

sed -i "s/\*db.RedisActor/\*rdb.RedisActor/g"  $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i 's/rdb \"app\/logic\/db\"/\"app\/logic\/db\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i 's/\"app\/logic\/db\"/rdb \"app\/logic\/db\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-cross-activity.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-cross-activity.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-cross-activity.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-logic-activity.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-activity.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-activity.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-logic-character.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-character.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-character.go
sed -i "s/\log.LogI/\hlog.LogI/g"  $PROJECT_PATH/src/app/symbols/app-logic-activity.go
sed -i 's/hlog \"app\/logic\/helper\/log\"/\"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go
sed -i 's/\"app\/logic\/helper\/log\"/hlog \"app\/logic\/helper\/log\"/g' $PROJECT_PATH/src/app/symbols/app-logic-command.go

cd $PROJECT_PATH/src/app/symbols && gofmt -w -s *.go
