#!/bin/bash

CURRENT_PATH=$(pwd)

#cd $CURRENT_PATH/src/app && golangci-lint run --enable-all -D gochecknoinits,gochecknoglobals,gocritic,dupl,gocyclo,wsl,funlen,gocognit,godox,gomnd  --timeout=5m

cd $CURRENT_PATH/src/app && golangci-lint run logic/... cross/... postman/... service/...

#检查随机函数的使用
function CheckRand {
  find $CURRENT_PATH/src/app/logic  -iname "*.go" | xargs grep "math/rand" | grep -v "sea" | grep -v "_test.go"
  if [ $? -eq 0 ]; then
    echo -e "\033[41;37mFAILED WHEN CHECK RAND FUNCTION\033[0m"
  else
    echo -e "\033[42;37mSUCCESS WHEN CHECK RAND FUNCTION\033[0m"
  fi
}

CheckRand
