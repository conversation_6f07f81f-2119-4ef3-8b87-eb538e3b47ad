#/bin/bash
#使用注意：当前仅用于cl.proto文件

SUCCESS_COLOR="\033[32m"
FAILED_COLOR="\033[31m"
WARN_COLOR="\033[33m"
END_COLOR="\033[0m"

if [ $# -lt 4 ]; then
	echo -e "$WARN_COLOR ./create_msg.sh 模块名(目录名、包名) 模块描述 开始协议id 结束协议id $END_COLOR"
	exit
fi

csfile="./protos/out/cl.proto"
cmddir="./src/app/logic/command/"

basedir=`basename $PWD`
if [[ ${basedir} == "tools" ]];then
	csfile="../protos/out/cl.proto"
	cmddir="../src/app/logic/command/"
fi

if [ ! -f "$csfile" ];
then
	echo -e "$FAILED_COLOR 未找到cl.proto文件，请在server目录下执行脚本！ $END_COLOR"
	exit
fi

module=$1
moduledes=$2
st=$3
ed=$4

#代码中的module名字处理，将下划线转换成驼峰
packagename=
IFS=$'\n' read -d "" -ra arr <<< "${module//_/$'\n'}"
for word in ${arr[@]}
do
	packagename=${packagename}${word,,}
done


echo -e "${SUCCESS_COLOR} 生成协议文件 id范围:[$st,$ed] 模块名:$module $END_COLOR"

idnames=`cat $csfile |grep "MSG_C2L_"|awk '{print $1}' FS=";"|grep -v "//"|awk '{print $1;}' FS="="`
idnums=`cat $csfile |grep "MSG_C2L_"|awk '{print $1}' FS=";"|grep -v "//"|awk '{print $2;}' FS="="`
idnotes=`cat $csfile |grep "MSG_C2L_"|grep -v "//\s*MSG" |awk -F "//" '{print $NF}'`

declare -A arr_m=()
declare -A arr_k=()
declare -A arr_n=()
listmsg=

#echo ${#idnames[@]},${idnames[@]}
#echo ${#idnums[@]},${idnums[@]}
#echo ${#idnotes[@]},${idnotes[@]}

i=0
for index in ${idnums[@]}
do
	if [ $index -ge $st ] && [ $index -le $ed ];then
		echo "find cmdid:$index"
	    if [ $i -eq 0 ];then
			arr_k=$i
		else
			arr_k=$arr_k" "$i
		fi
	fi
	let i++
done

j=0
for name in ${idnames[@]}
do
	arr_m[$j]=$name
	let j++
done

if [ $i -ne $j ]; then
	echo "查找结果错误：协议名和ID数量不对应，检查是否有换行！"
	exit
fi

OLD_IFS=$IFS
IFS=$'\n'

k=0
for note in ${idnotes[@]} 
do
	arr_n[$k]=$note
	let k++
done

IFS=$OLD_IFS

if [ $j -ne $k ]; then
	echo "查找结果错误：协议名和注释数量不对应，检查是否遗漏注释！"
	exit
fi


#创建文件和目录
dstdir=${cmddir}${packagename}
file="$dstdir/command.go"
if [ ! -d "$dstdir" ]; then
	echo -e "$SUCCESS_COLOR 创建目录$dstdir $END_COLOR"
	mkdir "$dstdir"
else
	echo -e "$WARN_COLOR 发现目录${dstdir}已存在 $END_COLOR"
	if [ -f "$file" ]; then
		echo -e "$WARN_COLOR 发现文件${file}已存在 $END_COLOR"
		file="$dstdir/command_`date +%s`.go"
		echo -e "$WARN_COLOR 生成临时文件名:${file} $END_COLOR"
	fi
fi

create_msg()
{
	msg_map=()
	i=0
	echo "package ${packagename}" >> $file
	echo "" >> $file
	echo "import (" >> $file
	echo "\"context\"" >> $file
	echo "" >> $file
	echo "\"app/logic/command/base\"" >> $file
	echo "\"app/protos/out/cl\"" >> $file
	echo "//\"app/protos/out/common\"" >> $file
	echo "cret \"app/protos/out/ret\"" >> $file
	echo "" >> $file
	echo "\"github.com/gogo/protobuf/proto\"" >> $file
	echo "l4g \"github.com/ivanabc/log4go\"">> $file
	echo "\"gitlab.qdream.com/kit/sea/zebra/parse\"" >> $file
	echo ")" >> $file

	echo "func Init(cmds *parse.CommandM, state bool) {" >> $file
	for index in ${arr_k}
	do
		cmd_id="cl.ID_"${arr_m[$index]}
		msg=`echo ${arr_m[$index]} |awk '{print $2}' FS="_C2L_"`
		msg_map[$i]=$msg
		let i++
		msg_f="C2L"$msg"Command{}"
		cmd_note="//"${arr_n[$index]}
		echo "cmds.Register(uint32($cmd_id), &$msg_f, state) $cmd_note" >> $file
	done
	echo "}" >> $file

	for msg in ${msg_map[@]}
	do
		cmsg="C2L_"$msg
		msgcmd="C2L"$msg"Command"
		echo "type $msgcmd struct {" >> $file
		echo "base.UserCommand" >> $file
		echo "}" >> $file
		echo "" >> $file

		#ResultOK
		echo "
		func (c *$msgcmd) ResultOK(retCode uint32) bool {
			return retCode == uint32(cret.RET_OK)
		}
		" >>$file

		#生成Error
		echo "
		func (c *$msgcmd) Error(msg *cl.L2C_${msg}, retCode uint32) bool {
			msg.Ret = retCode
			c.User.SendCmdToGateway(cl.ID_MSG_L2C_${msg}, msg)
			return false
		}
		">>$file

		cmsg="C2L_"$msg
		echo "func (c *$msgcmd) Execute(ctx context.Context) bool {" >> $file
		echo "cmsg := &cl.$cmsg{}" >> $file
		echo "if err := proto.Unmarshal(c.ProtoData, cmsg); err != nil {" >> $file
		echo "l4g.Errorf(\"$cmsg Unmarshal error. uid:%d err:%s\", c.Msg.UID, err)" >> $file
		echo "return false" >> $file
		echo "}" >> $file
		echo "c.Trace(cmsg)" >> $file
		echo "" >> $file
		echo "l4g.Debugf(\"user: %d $cmsg: cmsg:%s\", c.Msg.UID, cmsg)" >> $file
		echo "//edit here" >> $file
		echo "smsg := &cl.L2C_$msg {" >> $file
		echo "	Ret: uint32(cret.RET_OK)," >> $file
		echo "}" >> $file
		echo "//if !c.User.IsFunctionOpen(uint32(common.FUNCID_MODULE_${module^^})) {" >> $file
		echo "//	l4g.Errorf(\"user: %d $cmsg: function not open\", c.Msg.UID)" >> $file
		echo "//	return c.Error(smsg, uint32(cret.RET_FUNCTION_NOT_OPEN))" >> $file
		echo "//}" >> $file
		echo "//c.User.SendCmdToGateway(cl.ID_MSG_L2C_$msg, smsg)" >> $file

		echo "" >> $file
		echo "return c.ResultOK(smsg.Ret)" >> $file
		echo "}" >> $file
	done
	gofmt -w -s $file
}

create_msg

if [ $? -eq 0 ]; then
	echo -e "$SUCCESS_COLOR 生成协议文件:${file}完成 $END_COLOR"
else
	echo -e "$FAILED_COLOR 执行脚本出错！$END_COLOR"
fi

#注册消息到service/commands.go
if [[ ${basedir} == "tools" ]];then
	./register_msg.sh $module $moduledes
else
	./tools/register_msg.sh $module $moduledes
fi
