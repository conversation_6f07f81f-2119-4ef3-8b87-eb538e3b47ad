beans: #定义基础数据结构
  - # 类型名，要求大驼峰命名
    name: "AttrTypeValue"
    # 定义字段列表
    fields:
      - # 字段名，要求小写蛇形命名
        name: "type"
        # 字段类型，支持int8/uint8/int16/uint16/int32/uint32/int64/uint64/float32/float64/string/time.Time
        type: "uint32"
      - name: "value"
        type: "uint64"
  - # 可以指定协议包的类型
    name: "cl.Resource"
    fields:
      - name: "type"
        type: "uint32"
        # 定义枚举，这里表示取值要求是common.RESOURCE枚举值
        enum: "common.RESOURCE"
        # 定义最小值，这里表示取值不能小于1，即不能是NONE_RESOURCE
        min: 1
      - name: "value"
        type: "uint32"
        # 定义条件引用，这里表示type==4时引用item_info表的id字段，type==6时引用hero_info表的id字段
        # 要求ItemInfoM和HeroInfoM实现GetRecordById接口（重要！！！）
        # ref: "type=4#item_info.id;type=6#hero_info.id"
      - name: "count"
        type: "uint32"
        min: 1
  - name: "PokemonSkillData"
    fields:
      - name: "skill_group"
        type: "uint32"
      - name: "skill_level"
        type: "uint32"
      - name: "effective_function"
        type: "uint32"
      - name: "effective_param"
        type: "uint32"
tables: # 定义量表
  - # 表名，生成的go代码文件也使用该名字
    name: "sample_info"
    # 策划本地检查时是否忽略，若不配置，默认为false
    ignore_for_check: false
    # 定义字段列表（只需要指定额外定义的字段）
    # 这里定义的字段会按一定规则与xml中定义的字段进行匹配，xml中未被匹配的字段会自动添加到字段列表
    # 当前表xml中的id/season_id/skill字段未在这里定义，生成代码时仍会自动添加这三个字段，类型采用xml中int默认对应的uint32类型
    # extra字段在xml中找不到对应字段，因而作为一个自定义扩展字段存在，业务系统根据需要使用该字段
    fields:
      - # 字段名，自动查找xml中的同名字段
        name: "rite_id"
        # 字段类型，支持int8/uint8/int16/uint16/int32/uint32/int64/uint64/float32/float64/string/time.Time，但要求与xml中的同名字段类型匹配
        # 若类型为int8/uint8/int16/uint16/int32/uint32/int64/uint64/float32/float64，则xml中必须为int类型
        # 若类型为string/time.Time，则xml中必须为string类型
        # 若不定义，则自动取xml中同名字段类型
        type: "uint32"
        # 定义引用，这里表示该字段引用rite_info表的id字段
        # 要求RiteInfoM实现GetRecordById接口（重要！！！）
        ref: "#rite_info.id"
      - # 字段名，自动查找xml中的同名字段
        name: "rare"
        # 类型可不定义（前提是xml中有同名字段）
        # type: "uint32"
        # 定义枚举，这里表示取值要求是common.QUALITY枚举值
        enum: "common.QUALITY"
        # 定义最小值，这里表示取值不能小于10，即不能是NONE_QUALITY
        min: 10
        # 定义最大值，这里表示取值不能小于60，即不能是GOLDEN
        max: 60
        # 定义取值范围
        # values: [20, 30, 40, 50, 60]
      - # 字段名，自动查找xml中的同名字段
        name: "start_time"
        # 字段类型，这里表示需要将xml中的同名字段从字符串转换成时间类型
        type: "time.Time"
      - # 字段名，自动查找xml中的对应字段
        name: "position_buffs"
        # 列表字段类型，自动查找xml中的position_buffs/position_buffs_1/position_buffs_2/...字段
        # 若按上面的方式未能找到匹配，则去掉尾部的s继续匹配，即自动查找xml中的position_buff/position_buff_1/position_buff_2/...字段
        type: "list(uint32)"
        # 过滤条件，这里表示忽略0值
        filter: "notzero"
        # 定义引用，这里表示列表的每个值都引用raise_passive_skill_info表的raise_passive_skill字段
        # 要求RaisePassiveSkillInfoM实现GetRecordByRaisePassiveSkill接口（重要！！！）
        ref: "#raise_passive_skill_info.raise_passive_skill"
        # 定义最小列表长度和最大列表长度，这里表示长度必须等于5
        min_len: 5
        max_len: 5
        # min/max/enum/values限制也同样适用于列表字段（略）
      - # 字段名，自动查找xml中的对应字段
        name: "awards"
        # 自定义列表字段类型，类型要求在前面beans中定义
        # 自动查找xml中的awards_type/awards_value/awards_count/awards_type_1/awards_value_1/awards_count_2/...字段
        # 若按上面的方式未能找到匹配，则去掉尾部的s继续匹配，即自动查找xml中的award_type/award_value/award_count/award_type_1/award_value_1/award_count_2/...字段
        type: "list(cl.Resource)"
        # 过滤条件，这里表示忽略0值
        filter: "notzero"
      - # 字段名
        name: "extra"
        # 字段类型，扩展字段在xml中找不到匹配，可以定义任意类型（包括interface{}，切片类型，map类型等）
        # 可定义类型举例：
        #   interface{}
        #   RaisePassiveSkillInfoExt
        #   list(RaisePassiveSkillInfoExt)
        #   []*RaisePassiveSkillInfoExt 等同于 list(RaisePassiveSkillInfoExt)
        #   map[uint32]*RaisePassiveSkillInfoExt
        type: "RaisePassiveSkillInfoExt"
    indexes: # 定义索引
      - # 定义空索引用于生成全部记录切片及相关获取接口
        name: ""
        unique: false
      - # 定义索引字段
        name: "id"
        # 是否唯一索引
        unique: true
      - # 定义非唯一索引用于生成该索引下的记录切片及相关获取接口
        name: "season_id"
        unique: false
      - # 是否唯一索引
        unique: true
        # 定义索引字段，支持用冒号分隔的多个字段组合索引，这里表示season_id rite_id rare三个字段组合唯一索引
        # 字段后面的+号表示排序定义，支持 ~ = + -四种符号，不带符号默认表示~
        #   ~ 生成代码时只定义map，不额外定义切片
        #   = 生成代码时除定义map外，还额外定义切片，但切片不排序，代码自动生成GetFirst和GetLast接口
        #   + 生成代码时除定义map外，还额外定义切片，切片按字段值升序排列，代码自动生成GetMinXxx、GetMaxXxx、GetRecordByXxxMaxLe和GetRecordByXxxMinGe接口
        #   - 生成代码时除定义map外，还额外定义切片，切片按字段值降序排列，代码自动生成GetMinXxx、GetMaxXxx、GetRecordByXxxMaxLe和GetRecordByXxxMinGe接口
        name: "season_id:rite_id+:rare="
  - name: "rite_recycle_reward_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
        filter: "notzero"
        min_len: 1
    indexes:
      - name: "season_id:point+"
        unique: true
  -
    name: "season_link_info"
    fields:
      - name: "hero_id"
        ref: "#hero_info.id"
      - name: "link_id_1"
        ref: "#link_info.link_id"
      - name: "link_id_2"
      - name: "link_id"
        type: "list(uint32)"
        filter: "notzero"
        min_len: 1
    indexes:
      -
        unique: true
        name: "season_id:hero_id"
  - name: "season_link_monument_info"
    fields:
      - name: "link"
        #ref: "#link_info.link_id"
    indexes:
      - unique: true
        name: "id"
      - unique: false
        name: "season_id"
  - name: "season_link_monument_rare_info"
    fields:
      - name: "monument_id"
        ref: "#season_link_monument_info.id"
      - name: "skill"
        type: "list(uint32)"
        filter: "notzero"
        ref: "#passive_skill_info.id"
        min_len: 1
        max_len: 10
      - name: "skill_ps"
        type: "list(uint64)"
      - name: "rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: ""
      - unique: true
        name: "monument_id:level_unique+"
  - name: "season_link_rune_info"
    fields:
      - name: "monument_id"
        ref: "#season_link_monument_info.id"
      - name: "sell"
        type: "cl.Resource"
    indexes: #定义索引
      - unique: true
        name: "id"
      - unique: false
        name: "monument_id:rare"
      - unique: false
        name: "monument_id:unique_level+"
      - unique: true
        name: "monument_id:position+:unique_level"
  - name: "season_link_recycle_reward_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
        filter: "notzero"
        min_len: 1
    indexes:
      - name: "season_id:point+"
        unique: true
  - name: "story_review_group_info"
    fields:
      - name: "end_day"
        type: "time.Time"
    indexes:
      - unique: true
        name: "id"
      - unique: false
        name: "story_type"
  - name: "emblem_succinct_magic_skill_info"
    fields:
      - name: "hero_id"
        #ref: "#season_link_monument_info.id"
      - name: "emblem_rare"
    indexes: #定义索引
      - unique: false
        name: "hero_id:emblem_rare"
  - name: "emblem_succinct_info"
    fields:
      - name: "hero_race"
        #ref: "#season_link_monument_info.id"
      - name: "emblem_rare"
    indexes: #定义索引
      - unique: true
        name: "hero_race:emblem_rare"
  - name: "emblem_succinct_conflate_info"
    fields:
      - name: "conflate_value"
      - name: "conflate_count"
        values: [1]
    indexes: #定义索引
      - unique: true
        name: "conflate_value"
  - name: "emblem_succinct_lock_info"
    fields:
      - name: "race"
      - name: "rare"
    indexes: #定义索引
      - unique: true
        name: "race:rare"
  - name: "activity_pyramid_info"
    fields:
    indexes:
      - unique: true
        name: "id+"
  - name: "activity_pyramid_open_info"
    fields:
    indexes:
      - unique: true
        name: "id+"
  - name: "activity_pyramid_lattice_info"
    fields:
    indexes:
      - unique: true
        name: "id"
      - unique: false
        name: "server_type:floor"
  - name: "activity_pyramid_choose_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
        filter: "notzero"
        min_len: 1
    indexes:
      - unique: true
        name: "server_type:group:id="
      - unique: true
        name: "id"
  - name: "activity_pyramid_task_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
    indexes:
      - unique: true
        name: "id"
  - name: "shield_info"
    fields:
      - name: "end_day"
        type: "time.Time"
    indexes:
      - unique: true
        name: "id"
      - unique: true
        name: "shield_id"
  - name: "emblem_hero_group_info"
    fields:
      - name: "shield_end_time"
        type: "int64"
    indexes:
      - unique: true
        name: "group_id:hero_id"
  - name: "guild_sand_table_morale_info"
    indexes:
      - name: "season_id:morale"
        unique: true
      - name: "season_id:distance+"
        unique: true
  - name: "season_res_info"
    fields:
      - name: "season_link_value"
        ref: "#item_info.id"
      - name: "season_link"
        type: "list(cl.Resource)"
    indexes:
      - name: "id"
        unique: true
  - name: "guild_sand_table_arena_info"
    indexes:
      - unique: true
        name: "arena_group:match_index:rank+"
  - name: "guild_sand_table_popularity_info"
    fields:
      - name: "popularity_range"
        type: "int32"
    indexes:
      - name: "popularity_range+"
        unique: true
  - name: "guild_sand_table_siege_group_info"
    fields:
      - name: "boss_id"
        type: "list(uint32)"
        filter: "notzero"
        min_len: 1
      - name: "first_reward"
        type: "list(cl.Resource)"
        min_len: 1
    indexes:
      - name: "siege_group:index+"
        unique: true
  - name: "guild_sand_table_siege_boss_hp_info"
    fields:
      - name: "hp_range"
        type: "list(uint32)"
        min_len: 1
      - name: "damage_range"
        type: "list(uint64)"
        min_len: 1
    indexes:
      - name: "id"
        unique: true
  - name: "guild_sand_table_siege_monster_info"
    indexes:
      - name: "id"
        unique: true
  - name: "guild_sand_table_siege_boss_award_info"
    fields:
      - name: "damage_range"
        type: "uint64"
    indexes:
      - name: "damage_group:damage_range+"
        unique: true
  - name: "guild_sand_table_morale_buff_info"
    indexes:
      - name: "id+"
        unique: true
  - name: "guild_sand_table_dragon_info"
    indexes:
      - name: "dragon_id:level"
        unique: true
  - name: "guild_sand_table_dragon_schedule_info"
    fields:
      - name: "win_awards"
        type: "list(cl.Resource)"
      - name: "lose_awards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id:season_id"
        unique: true
  - name: "guild_sand_table_dragon_evolve_info"
    indexes:
      - name: "season_id"
        unique: true
  - name: "guild_sand_table_dragon_bot_info"
    indexes:
      - name: "id"
        unique: true
  - name: "guild_sand_table_dragon_boss_hp_info"
    fields:
      - name: "hp_range"
        type: "list(uint32)"
        min_len: 1
      - name: "damage_range"
        type: "list(uint64)"
        min_len: 1
    indexes:
      - name: "id:level"
        unique: true
  - name: "guild_sand_table_dragon_reward_info"
    fields:
      - name: "dam"
        type: "uint64"
    indexes:
      - name: "drop_group:level:dam+"
        unique: true
  - name: "guild_sand_table_dragon_task_reward_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id"
        unique: true
  - name: "boss_rush_info"
    fields:
      - name: "reward"
        type: "list(cl.Resource)"
        min_len: 1
    indexes:
      - name: "id:season_id"
        unique: true
      - name: "season_id:group_id:level"
        unique: true
      - name: "season_id:formation_id"
        unique: false
  - name: "boss_rush_hp_info"
    fields:
      - name: "hp_range"
        type: "list(uint32)"
        min_len: 1
      - name: "damage_range"
        type: "list(uint64)"
        min_len: 1
    indexes:
      - name: "id"
        unique: true
  - name: "boss_rush_task_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id"
        unique: true
  - name: "boss_rush_reward_info"
    fields:
      - name: "hp_rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id:hp+"
        unique: true
  - name: "season_talent_tree_base_info"
    indexes:
      - name: "season_id:id"
        unique: true
      - name: "season_id:node_type"
        unique: false
      - name: "season_id:node_type:belong_page"
        unique: false
  - name: "season_talent_tree_config_info"
    indexes:
      - name: "key"
        unique: true
  - name: "season_talent_tree_level_info"
    fields:
      - name: "cost"
        type: "list(cl.Resource)"
        filter: "notzero"
      - name: "awards"
        type: "list(cl.Resource)"
        filter: "notzero"
      - name: "passive_params"
        type: "list(uint32)"
        filter: "notzero"
      - name: "attr"
        type: "list(AttrTypeValue)"
        filter: "notzero"
    indexes:
      - name: "id:level"
        unique: true
  - name: "season_talent_tree_recyle_award_info"
    fields:
      - name: "reward"
        type: "list(cl.Resource)"
        filter: "notzero"
    indexes:
      - name: "season_id:level+"
        unique: true
  - name: "season_talent_tree_task_award_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "season_id:id"
        unique: true
  - name: "guild_sand_table_challenge_info"
    indexes:
      - name: "fight_group:match_index"
        unique: true
  - name: "guild_sand_table_challenge_guild_rank_info"
    indexes:
      - name: "match_index:rank+"
        unique: true
  - name: "guild_sand_table_challenge_match_info"
    indexes:
      - name: "round+"
        unique: true
  - name: "guild_sand_table_challenge_buff_info"
    indexes:
      - name: "id"
        unique: true
  - name: "guild_sand_table_challenge_rank_reward_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
        filter: "notzero"
    indexes:
      - name: "rank_min+"
        unique: true
  - name: "guild_sand_table_challenge_task_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id"
        unique: true
  - name: "guild_sand_table_challenge_bot_info"
    indexes:
      - name: "id"
        unique: true
  - name: "season_jewelry_info"
    fields:
      - name: "level_up_cost"
        type: "list(cl.Resource)"
        filter: "notzero"
      - name: "decompose_rewards"
        type: "list(cl.Resource)"
        filter: "notzero"
      - name: "guarantee_change_cost"
        type: "list(cl.Resource)"
        filter: "notzero"
      - name: "pos"
        min: 1
        max: 4
      - name: "attr"
        type: "list(AttrTypeValue)"
        filter: "notzero"
    indexes:
      - name: "id"
        unique: true
  - name: "season_jewelry_skill_random_info"
    indexes:
      - name: "id"
        unique: true
      - name: "skill_group"
        unique: false
  - name: "season_jewelry_skill_info"
    indexes:
      - name: "skill_id"
        unique: true
      - name: "skill_type:class+"
        unique: true
  - name: "season_jewelry_skill_lv_info"
    indexes:
      - name: "skill_type:level"
        unique: true
  - name: "season_jewelry_class_up_info"
    indexes:
      - name: "class"
        unique: true
  - name: "season_jewelry_skill_change_info"
    indexes:
      - name: "id"
        unique: true
      - name: "jewelry_pos:skill_scort:class"
        unique: false
  - name: "season_jewelry_suit_info"
    indexes:
      - name: "suit_id"
        unique: true
      - name: "suit_type:suit_level"
        unique: true
  - name: "season_jewelry_recycle_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
        filter: "notzero"
        min_len: 1
    indexes:
      - name: "season_id:point+"
        unique: true
  - name: "season_jewelry_config_info"
    indexes:
      - name: "key"
        unique: true
  - name: "title_info"
    fields:
      - name: "passive_skills"
        type: "list(uint64)"
    indexes:
      - name: "id"
        unique: true
  - name: "activity_season_shop_info"
    indexes:
      - name: "shop_id"
        unique: true
  - name: "activity_season_shop_goods_info"
    fields:
      - name: "award"
        type: "list(cl.Resource)"
      - name: "cost"
        type: "list(cl.Resource)"
        min_len: 1
    indexes:
      - name: "id"
        unique: true
  - name: "season_door_boss_weak_info"
    indexes:
      - name: "id:season_day+"
        unique: true
  - name: "season_map_monster_hp_info"
    fields:
      - name: "hp_range"
        type: "list(uint32)"
        min_len: 1
      - name: "damage_range"
        type: "list(uint64)"
        min_len: 1
    indexes:
      - name: "id"
        unique: true
  - name: "season_map_monster_reward_info"
    fields:
      - name: "hp_rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id:hp+"
        unique: true
  - name: "pokemon_info"
    fields:
      - name: "activate_costs"
        type: "list(cl.Resource)"
    indexes:
      - name: "pokemon_id"
        unique: true
      - name: "fragment_value"
        unique: true
  - name: "pokemon_fragment_info"
    indexes:
      - name: "fragment_id"
        unique: true     
  - name: "pokemon_ball_info"
    fields:
      - name: "function"
        type: "list(uint32)"
        filter: "notzero"
        min_len: 1
    indexes:
      - name: "ball_type:position"
        unique: true 
  - name: "pokemon_star_info"
    fields:
      - name: "attr"
        type: "list(AttrTypeValue)"
    indexes:
      - name: "mould_id:star+"
        unique: true      
  - name: "pokemon_potential_info"
    fields:
      - name: "level_up_costs"
        type: "list(cl.Resource)"
      - name: "attr"
        type: "list(AttrTypeValue)"
    indexes:
      - name: "level"
        unique: true
  - name: "pokemon_skill_info"
    fields:
      - name: "passive"
        type: "list(uint64)"
        filter: "notzero"
      - name: "passive_skill"
        type: "list(uint32)"
        filter: "notzero"
      - name: "season_passive_skill"
        type: "list(uint64)"
        filter: "notzero"
    indexes:
      - name: "skill_id"
        unique: true
      - name: "skill_group:skill_level"
        unique: true
  - name: "pokemon_skill_level_info"
    fields:
      - name: "skills"
        type: "list(PokemonSkillData)"
        filter: "notzero"
        min_len: 1
    indexes:
      - name: "id:star"
        unique: true
  - name: "pokemon_master_info"
    fields:
      - name: "master_rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "master_level"
        unique: true 
  - name: "season_map_daily_money_info"
    indexes:
      - name: "explore_value+"
        unique: true
  - name: "offline_vip_award_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id"
        unique: true
  - name: "resources_warn_info"
    indexes:
      - name: "resource_type:value"
        unique: true
  - name: "synthesis_game_config_info"
    indexes:
      - name: "key"
        unique: true
  - name: "activity_coupon_daily_info"
    fields:
      - name: "good_rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id+"
        unique: true
  - name: "activity_coupon_task_info"
    indexes:
      - name: "id"
        unique: true
  - name: "activity_zero_dollar_purchase_info"
    fields:
      - name: "first_rewards"
        type: "list(cl.Resource)"
      - name: "second_rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id"
        unique: true
  - name: "activity_goddess_prize_draw_info"
    fields:
      - name: "now_rewards"
        type: "list(cl.Resource)"
      - name: "win_rewards"
        type: "list(cl.Resource)"
      - name: "lose_rewards"
        type: "list(cl.Resource)"
    indexes:
      - name: "id"
        unique: true
      - name: "server_day_min+"
        unique: true
  - name: "hero_rare_up_info"
    fields:
      - name: "cost"
        type: "list(cl.Resource)"
        filter: "notzero"
    indexes:
      - name: "hero_id:level="
        unique: true
  - name: "balance_arena_info"
    fields:
      - name: "open_time"
        type: "int64"
      - name: "close_time"
        type: "int64"
    indexes:
      - name: "id"
        unique: true
      - name: "season_id:game_num+"
        unique: true
  - name: "balance_arena_phase_info"
    fields:
    indexes:
      - name: "id"
        unique: true
      - name: "phase_type:battle_num:phase_stage"
        unique: true
  - name: "balance_arena_reward_info"
    fields:
      - name: "rewards"
        type: "list(cl.Resource)"
        filter: "notzero"
    indexes:
      - name: "id"
        unique: true
  - name: "balance_arena_rank_reward_info"
    fields:
    indexes:
      - name: "rank_type:rank_min+"
        unique: true
  - name: "balance_arena_bot_info"
    fields:
      - name: "monster_group"
        type: "list(uint32)"
        filter: "notzero"
    indexes:
      - name: "id"
        unique: true
  - name: "balance_arena_config_info"
    indexes:
      - name: "key"
        unique: true
  - name: "link_suppress_info"
    indexes:
      - name: "function_type:link_num:link_num_enemy"
        unique: true
