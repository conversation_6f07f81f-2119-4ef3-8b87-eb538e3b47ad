#!/bin/bash

export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$PROJECT_PATH/deps/protobuf/lib/
PROTOC=$PROJECT_PATH/deps/protobuf/bin/protoc
CODE_PATH=$PROJECT_PATH/src
CLANG_FORMAT="clang-format -style=\"{BasedOnStyle: google, ColumnLimit: 200}\""


CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

if [ -z "$CURRENT_BRANCH" ]; then
  echo "错误: 无法获取当前分支名称。"
  exit 1
fi

echo "当前分支是: $CURRENT_BRANCH"

# git clone or pull
if [ -n "$1" ]; then
  if [ ! -d $PROJECT_PATH/protos/out ]; then
    git clone --branch $CURRENT_BRANCH *********************:ngame/proto.git $PROJECT_PATH/protos/out
  else
    cd $PROJECT_PATH/protos/out
    echo "git pull $PROJECT_PATH/protos/out"
    git pull origin $CURRENT_BRANCH
  fi
fi

# git ls-files $PROJECT_PATH/src/app/protos/in/ --others |xargs -i rm {}
# git ls-files $PROJECT_PATH/src/app/protos/out/ --others |xargs -i rm {}

cd $CODE_PATH

# out protos
out_protos=(common ret cg cl bt)
if [ -f $PROJECT_PATH/src/kit/gateway/protos/out/cg.proto ]; then
  cp $PROJECT_PATH/src/kit/gateway/protos/out/cg.proto $PROJECT_PATH/protos/out/
  sed -i "1i\//文件从Gateway中Copy, 禁止修改" $PROJECT_PATH/protos/out/cg.proto
  sed -i '4ioption go_package = "app/protos/out/cg";' $PROJECT_PATH/protos/out/cg.proto
fi

for i in ${out_protos[@]}
do
  proto_path=$PROJECT_PATH/protos/out
  eval "$CLANG_FORMAT -i $proto_path/$i.proto"
	if $PROTOC -I=$proto_path --gofast_out=./ $proto_path/$i.proto; then
		echo build success $i.proto
	else
		echo build failed $i.proto
    exit 1
	fi
done

# in protos
# in_protos=(db g2l r2l l2w wdb wmodule log config p2l)
# in_protos=(db g2l r2l l2w wdb wmodule log config p2l l2c cr r2c)
in_protos=(db g2l r2l l2w log config p2l l2c cr r2c cm2c l2cm)

for i in ${in_protos[@]}
do
  proto_path=$PROJECT_PATH/protos/in
  eval "$CLANG_FORMAT -i $proto_path/$i.proto"
	if $PROTOC -I=$proto_path -I=$PROJECT_PATH/protos/out --gofast_out=./ $proto_path/$i.proto; then
		echo build success $i.proto
	else
		echo build failed $i.proto
    exit 1
	fi
done

# gm protos
gm_protos=(gm account l2m)

for i in ${gm_protos[@]}
do
  proto_path=$PROJECT_PATH/protos/in
  eval "$CLANG_FORMAT -i $proto_path/$i.proto"
	if $PROTOC -I=$proto_path -I=$PROJECT_PATH/protos/out --gofast_out=plugins=grpc:. $proto_path/$i.proto; then
		echo build success $i.proto
	else
		echo build failed $i.proto
    exit 1
	fi
done

# proto clone
$PROJECT_PATH/tools/protoclone -pbgo=$PROJECT_PATH/src/app/protos/out/cl/cl.pb.go -skip=L2C_,C2L_
goimports -w $PROJECT_PATH/src/app/protos/out/cl/cl.pb.clone.go
$PROJECT_PATH/tools/protoclone -pbgo=$PROJECT_PATH/src/app/protos/out/bt/bt.pb.go
$PROJECT_PATH/tools/protoclone -pbgo=$PROJECT_PATH/src/app/protos/in/db/db.pb.go
# $PROJECT_PATH/tools/protoclone -pbgo=$PROJECT_PATH/src/app/protos/in/wdb/wdb.pb.go
$PROJECT_PATH/tools/protoclone -pbgo=$PROJECT_PATH/src/app/protos/in/cr/cr.pb.go

# cp r2l.go
gofmt -w -s $PROJECT_PATH/protos/in/r2l_grpc.go
cp -p $PROJECT_PATH/protos/in/r2l_grpc.go $PROJECT_PATH/src/app/protos/in/r2l/


# 对于一些配置文件(比如可配置活动，幸运抽奖)，需要同时在gmxml和proto定义
# 添加字段比较麻烦，而且容易出错，所以这里对proto添加一个 //$<XMLATTR> 注释
# 自动给这些proto message的字段添加 xml:"field_name,attr" tag
# 以便让这些结构同时支持gmxml，proto
lineNum=0
isXmlAttrState=0
startXmlLine=0
cat $PROJECT_PATH/src/app/protos/out/cl/cl.pb.go | while read line;do
let lineNum++
if [[ "$line" = "//$<XMLATTR>" ]]; then
	if [[ $isXmlAttrState = 1 ]]; then
		echo "repeated xml attr state" $lineNum
		exit 1
	fi
	let isXmlAttrState=1
	let startXmlLine=${lineNum}
elif [[ "$line" = "}" ]]; then
	if [[ $isXmlAttrState = 1 ]]; then
		let isXmlAttrState=0
		#echo $startXmlLine $lineNum
		sed -i "${startXmlLine},${lineNum}"'s/json:"\(.*\),omitempty"/json:"\1,omitempty" xml:"\1,attr"/' $PROJECT_PATH/src/app/protos/out/cl/cl.pb.go
	fi
fi
done
