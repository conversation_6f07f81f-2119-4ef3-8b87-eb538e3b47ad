<?xml version="1.0" encoding="UTF-8"?>
<!--type=int:类型 id=int:id weight=int:抽选权重 min=int:分数下限 max=int:分数上限 -->

<root>
    <data type="1" id="1" weight="100" min="1" max="3" />
    <data type="1" id="2" weight="150" min="4" max="6" />
    <data type="1" id="3" weight="150" min="7" max="9" />
    <data type="2" id="1" weight="100" min="1" max="3" />
    <data type="2" id="2" weight="120" min="4" max="6" />
    <data type="2" id="3" weight="120" min="7" max="9" />
    <data type="2" id="4" weight="180" min="10" max="11" />
    <data type="3" id="1" weight="100" min="1" max="3" />
    <data type="3" id="2" weight="120" min="4" max="6" />
    <data type="3" id="3" weight="120" min="7" max="9" />
    <data type="3" id="4" weight="180" min="10" max="11" />
    <data type="4" id="1" weight="100" min="1" max="3" />
    <data type="4" id="2" weight="120" min="4" max="6" />
    <data type="4" id="3" weight="120" min="7" max="9" />
    <data type="4" id="4" weight="180" min="10" max="11" />
    <data type="5" id="1" weight="100" min="1" max="3" />
    <data type="5" id="2" weight="120" min="4" max="6" />
    <data type="5" id="3" weight="120" min="7" max="9" />
    <data type="5" id="4" weight="180" min="10" max="11" />
    <data type="6" id="1" weight="100" min="1" max="3" />
    <data type="6" id="2" weight="120" min="4" max="6" />
    <data type="6" id="3" weight="120" min="7" max="9" />
    <data type="6" id="4" weight="180" min="10" max="11" />
    <data type="7" id="1" weight="100" min="1" max="3" />
    <data type="7" id="2" weight="120" min="4" max="6" />
    <data type="7" id="3" weight="120" min="7" max="9" />
    <data type="7" id="4" weight="180" min="10" max="11" />
</root>
