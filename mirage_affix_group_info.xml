<?xml version="1.0" encoding="UTF-8"?>
<!--hurdle_id=int:关卡id affix_id=int:词缀id -->

<root>
    <data hurdle_id="101" affix_id="9400105" />
    <data hurdle_id="101" affix_id="9500101" />
    <data hurdle_id="101" affix_id="9500104" />
    <data hurdle_id="102" affix_id="9400105" />
    <data hurdle_id="102" affix_id="9500101" />
    <data hurdle_id="102" affix_id="9500105" />
    <data hurdle_id="103" affix_id="9400105" />
    <data hurdle_id="103" affix_id="9500101" />
    <data hurdle_id="103" affix_id="9500106" />
    <data hurdle_id="104" affix_id="9400105" />
    <data hurdle_id="104" affix_id="9500101" />
    <data hurdle_id="104" affix_id="9500107" />
    <data hurdle_id="105" affix_id="9400105" />
    <data hurdle_id="105" affix_id="9500101" />
    <data hurdle_id="105" affix_id="9500108" />
    <data hurdle_id="106" affix_id="9400105" />
    <data hurdle_id="106" affix_id="9500101" />
    <data hurdle_id="106" affix_id="9500109" />
    <data hurdle_id="107" affix_id="9400105" />
    <data hurdle_id="107" affix_id="9500101" />
    <data hurdle_id="107" affix_id="9500111" />
    <data hurdle_id="108" affix_id="9400105" />
    <data hurdle_id="108" affix_id="9500101" />
    <data hurdle_id="108" affix_id="9500111" />
    <data hurdle_id="109" affix_id="9400105" />
    <data hurdle_id="109" affix_id="9500101" />
    <data hurdle_id="109" affix_id="9500111" />
    <data hurdle_id="110" affix_id="9400105" />
    <data hurdle_id="110" affix_id="9500101" />
    <data hurdle_id="110" affix_id="9500111" />
    <data hurdle_id="111" affix_id="9400105" />
    <data hurdle_id="111" affix_id="9500101" />
    <data hurdle_id="111" affix_id="9500111" />
    <data hurdle_id="112" affix_id="9400105" />
    <data hurdle_id="112" affix_id="9500101" />
    <data hurdle_id="112" affix_id="9500111" />
    <data hurdle_id="113" affix_id="9400105" />
    <data hurdle_id="113" affix_id="9500101" />
    <data hurdle_id="113" affix_id="9500111" />
    <data hurdle_id="114" affix_id="9400105" />
    <data hurdle_id="114" affix_id="9500101" />
    <data hurdle_id="114" affix_id="9500111" />
    <data hurdle_id="115" affix_id="9400105" />
    <data hurdle_id="115" affix_id="9500101" />
    <data hurdle_id="115" affix_id="9500111" />
    <data hurdle_id="116" affix_id="9400105" />
    <data hurdle_id="116" affix_id="9500101" />
    <data hurdle_id="116" affix_id="9500111" />
    <data hurdle_id="117" affix_id="9400105" />
    <data hurdle_id="117" affix_id="9500101" />
    <data hurdle_id="117" affix_id="9500111" />
    <data hurdle_id="118" affix_id="9400105" />
    <data hurdle_id="118" affix_id="9500101" />
    <data hurdle_id="118" affix_id="9500102" />
    <data hurdle_id="119" affix_id="9400105" />
    <data hurdle_id="119" affix_id="9500101" />
    <data hurdle_id="119" affix_id="9500110" />
    <data hurdle_id="120" affix_id="9400105" />
    <data hurdle_id="120" affix_id="9500101" />
    <data hurdle_id="120" affix_id="9500111" />
    <data hurdle_id="121" affix_id="9400105" />
    <data hurdle_id="121" affix_id="9500101" />
    <data hurdle_id="121" affix_id="9500111" />
    <data hurdle_id="122" affix_id="9400105" />
    <data hurdle_id="122" affix_id="9500101" />
    <data hurdle_id="122" affix_id="9500111" />
    <data hurdle_id="123" affix_id="9400105" />
    <data hurdle_id="123" affix_id="9500101" />
    <data hurdle_id="123" affix_id="9500111" />
    <data hurdle_id="124" affix_id="9400105" />
    <data hurdle_id="124" affix_id="9500101" />
    <data hurdle_id="124" affix_id="9500111" />
    <data hurdle_id="125" affix_id="9400105" />
    <data hurdle_id="125" affix_id="9500101" />
    <data hurdle_id="125" affix_id="9500111" />
    <data hurdle_id="126" affix_id="9400105" />
    <data hurdle_id="126" affix_id="9500101" />
    <data hurdle_id="126" affix_id="9500110" />
    <data hurdle_id="127" affix_id="9400105" />
    <data hurdle_id="127" affix_id="9500101" />
    <data hurdle_id="127" affix_id="9500110" />
    <data hurdle_id="128" affix_id="9400105" />
    <data hurdle_id="128" affix_id="9500101" />
    <data hurdle_id="128" affix_id="9500111" />
    <data hurdle_id="129" affix_id="9400105" />
    <data hurdle_id="129" affix_id="9500101" />
    <data hurdle_id="129" affix_id="9500111" />
    <data hurdle_id="130" affix_id="9400105" />
    <data hurdle_id="130" affix_id="9500101" />
    <data hurdle_id="130" affix_id="9500111" />
    <data hurdle_id="131" affix_id="9400105" />
    <data hurdle_id="131" affix_id="9500101" />
    <data hurdle_id="131" affix_id="9500111" />
    <data hurdle_id="132" affix_id="9400105" />
    <data hurdle_id="132" affix_id="9500101" />
    <data hurdle_id="132" affix_id="9500111" />
    <data hurdle_id="133" affix_id="9400105" />
    <data hurdle_id="133" affix_id="9500101" />
    <data hurdle_id="133" affix_id="9500111" />
    <data hurdle_id="134" affix_id="9400105" />
    <data hurdle_id="134" affix_id="9500101" />
    <data hurdle_id="134" affix_id="9500111" />
    <data hurdle_id="135" affix_id="9400105" />
    <data hurdle_id="135" affix_id="9500101" />
    <data hurdle_id="135" affix_id="9500111" />
    <data hurdle_id="136" affix_id="9400105" />
    <data hurdle_id="136" affix_id="9500101" />
    <data hurdle_id="136" affix_id="9500111" />
    <data hurdle_id="137" affix_id="9400105" />
    <data hurdle_id="137" affix_id="9500101" />
    <data hurdle_id="137" affix_id="9500111" />
    <data hurdle_id="138" affix_id="9400105" />
    <data hurdle_id="138" affix_id="9500101" />
    <data hurdle_id="138" affix_id="9500111" />
    <data hurdle_id="139" affix_id="9400105" />
    <data hurdle_id="139" affix_id="9500101" />
    <data hurdle_id="139" affix_id="9500111" />
    <data hurdle_id="140" affix_id="9400105" />
    <data hurdle_id="140" affix_id="9500101" />
    <data hurdle_id="140" affix_id="9500111" />
    <data hurdle_id="141" affix_id="9400105" />
    <data hurdle_id="141" affix_id="9500101" />
    <data hurdle_id="141" affix_id="9500111" />
    <data hurdle_id="142" affix_id="9400105" />
    <data hurdle_id="142" affix_id="9500101" />
    <data hurdle_id="142" affix_id="9500111" />
    <data hurdle_id="143" affix_id="9400105" />
    <data hurdle_id="143" affix_id="9500101" />
    <data hurdle_id="143" affix_id="9500111" />
    <data hurdle_id="144" affix_id="9400105" />
    <data hurdle_id="144" affix_id="9500101" />
    <data hurdle_id="144" affix_id="9500111" />
    <data hurdle_id="145" affix_id="9400105" />
    <data hurdle_id="145" affix_id="9500101" />
    <data hurdle_id="145" affix_id="9500111" />
    <data hurdle_id="146" affix_id="9400105" />
    <data hurdle_id="146" affix_id="9500101" />
    <data hurdle_id="146" affix_id="9500111" />
    <data hurdle_id="147" affix_id="9400105" />
    <data hurdle_id="147" affix_id="9500101" />
    <data hurdle_id="147" affix_id="9500111" />
    <data hurdle_id="148" affix_id="9400105" />
    <data hurdle_id="148" affix_id="9500101" />
    <data hurdle_id="148" affix_id="9500111" />
    <data hurdle_id="149" affix_id="9400105" />
    <data hurdle_id="149" affix_id="9500101" />
    <data hurdle_id="149" affix_id="9500111" />
    <data hurdle_id="150" affix_id="9400105" />
    <data hurdle_id="150" affix_id="9500101" />
    <data hurdle_id="150" affix_id="9500111" />
    <data hurdle_id="151" affix_id="9400105" />
    <data hurdle_id="151" affix_id="9500101" />
    <data hurdle_id="151" affix_id="9500111" />
    <data hurdle_id="152" affix_id="9400105" />
    <data hurdle_id="152" affix_id="9500101" />
    <data hurdle_id="152" affix_id="9500111" />
    <data hurdle_id="153" affix_id="9400105" />
    <data hurdle_id="153" affix_id="9500101" />
    <data hurdle_id="153" affix_id="9500111" />
    <data hurdle_id="154" affix_id="9400105" />
    <data hurdle_id="154" affix_id="9500101" />
    <data hurdle_id="154" affix_id="9500111" />
    <data hurdle_id="155" affix_id="9400105" />
    <data hurdle_id="155" affix_id="9500101" />
    <data hurdle_id="155" affix_id="9500111" />
    <data hurdle_id="156" affix_id="9400105" />
    <data hurdle_id="156" affix_id="9500101" />
    <data hurdle_id="156" affix_id="9500111" />
    <data hurdle_id="157" affix_id="9400105" />
    <data hurdle_id="157" affix_id="9500101" />
    <data hurdle_id="157" affix_id="9500111" />
    <data hurdle_id="158" affix_id="9400105" />
    <data hurdle_id="158" affix_id="9500101" />
    <data hurdle_id="158" affix_id="9500111" />
    <data hurdle_id="159" affix_id="9400105" />
    <data hurdle_id="159" affix_id="9500101" />
    <data hurdle_id="159" affix_id="9500111" />
    <data hurdle_id="160" affix_id="9400105" />
    <data hurdle_id="160" affix_id="9500101" />
    <data hurdle_id="160" affix_id="9500111" />
    <data hurdle_id="161" affix_id="9400105" />
    <data hurdle_id="161" affix_id="9500101" />
    <data hurdle_id="161" affix_id="9500111" />
    <data hurdle_id="162" affix_id="9400105" />
    <data hurdle_id="162" affix_id="9500101" />
    <data hurdle_id="162" affix_id="9500111" />
    <data hurdle_id="163" affix_id="9400105" />
    <data hurdle_id="163" affix_id="9500101" />
    <data hurdle_id="163" affix_id="9500111" />
    <data hurdle_id="164" affix_id="9400105" />
    <data hurdle_id="164" affix_id="9500101" />
    <data hurdle_id="164" affix_id="9500111" />
    <data hurdle_id="165" affix_id="9400105" />
    <data hurdle_id="165" affix_id="9500101" />
    <data hurdle_id="165" affix_id="9500111" />
    <data hurdle_id="166" affix_id="9400105" />
    <data hurdle_id="166" affix_id="9500101" />
    <data hurdle_id="166" affix_id="9500111" />
    <data hurdle_id="167" affix_id="9400105" />
    <data hurdle_id="167" affix_id="9500101" />
    <data hurdle_id="167" affix_id="9500111" />
    <data hurdle_id="168" affix_id="9400105" />
    <data hurdle_id="168" affix_id="9500101" />
    <data hurdle_id="168" affix_id="9500111" />
    <data hurdle_id="169" affix_id="9400105" />
    <data hurdle_id="169" affix_id="9500101" />
    <data hurdle_id="169" affix_id="9500111" />
    <data hurdle_id="170" affix_id="9400105" />
    <data hurdle_id="170" affix_id="9500101" />
    <data hurdle_id="170" affix_id="9500111" />
    <data hurdle_id="171" affix_id="9400105" />
    <data hurdle_id="171" affix_id="9500101" />
    <data hurdle_id="171" affix_id="9500111" />
    <data hurdle_id="172" affix_id="9400105" />
    <data hurdle_id="172" affix_id="9500101" />
    <data hurdle_id="172" affix_id="9500111" />
    <data hurdle_id="173" affix_id="9400105" />
    <data hurdle_id="173" affix_id="9500101" />
    <data hurdle_id="173" affix_id="9500111" />
    <data hurdle_id="174" affix_id="9400105" />
    <data hurdle_id="174" affix_id="9500101" />
    <data hurdle_id="174" affix_id="9500111" />
    <data hurdle_id="175" affix_id="9400105" />
    <data hurdle_id="175" affix_id="9500101" />
    <data hurdle_id="175" affix_id="9500111" />
    <data hurdle_id="176" affix_id="9400105" />
    <data hurdle_id="176" affix_id="9500101" />
    <data hurdle_id="176" affix_id="9500111" />
    <data hurdle_id="177" affix_id="9400105" />
    <data hurdle_id="177" affix_id="9500101" />
    <data hurdle_id="177" affix_id="9500111" />
    <data hurdle_id="178" affix_id="9400105" />
    <data hurdle_id="178" affix_id="9500101" />
    <data hurdle_id="178" affix_id="9500111" />
    <data hurdle_id="179" affix_id="9400105" />
    <data hurdle_id="179" affix_id="9500101" />
    <data hurdle_id="179" affix_id="9500111" />
    <data hurdle_id="180" affix_id="9400105" />
    <data hurdle_id="180" affix_id="9500101" />
    <data hurdle_id="180" affix_id="9500111" />
    <data hurdle_id="181" affix_id="9400105" />
    <data hurdle_id="181" affix_id="9500101" />
    <data hurdle_id="181" affix_id="9500111" />
    <data hurdle_id="182" affix_id="9400105" />
    <data hurdle_id="182" affix_id="9500101" />
    <data hurdle_id="182" affix_id="9500111" />
    <data hurdle_id="183" affix_id="9400105" />
    <data hurdle_id="183" affix_id="9500101" />
    <data hurdle_id="183" affix_id="9500111" />
    <data hurdle_id="184" affix_id="9400105" />
    <data hurdle_id="184" affix_id="9500101" />
    <data hurdle_id="184" affix_id="9500111" />
    <data hurdle_id="185" affix_id="9400105" />
    <data hurdle_id="185" affix_id="9500101" />
    <data hurdle_id="185" affix_id="9500111" />
    <data hurdle_id="186" affix_id="9400105" />
    <data hurdle_id="186" affix_id="9500101" />
    <data hurdle_id="186" affix_id="9500111" />
    <data hurdle_id="187" affix_id="9400105" />
    <data hurdle_id="187" affix_id="9500101" />
    <data hurdle_id="187" affix_id="9500111" />
    <data hurdle_id="188" affix_id="9400105" />
    <data hurdle_id="188" affix_id="9500101" />
    <data hurdle_id="188" affix_id="9500111" />
    <data hurdle_id="189" affix_id="9400105" />
    <data hurdle_id="189" affix_id="9500101" />
    <data hurdle_id="189" affix_id="9500111" />
    <data hurdle_id="190" affix_id="9400105" />
    <data hurdle_id="190" affix_id="9500101" />
    <data hurdle_id="190" affix_id="9500111" />
    <data hurdle_id="191" affix_id="9400105" />
    <data hurdle_id="191" affix_id="9500101" />
    <data hurdle_id="191" affix_id="9500111" />
    <data hurdle_id="192" affix_id="9400105" />
    <data hurdle_id="192" affix_id="9500101" />
    <data hurdle_id="192" affix_id="9500111" />
    <data hurdle_id="193" affix_id="9400105" />
    <data hurdle_id="193" affix_id="9500101" />
    <data hurdle_id="193" affix_id="9500111" />
    <data hurdle_id="194" affix_id="9400105" />
    <data hurdle_id="194" affix_id="9500101" />
    <data hurdle_id="194" affix_id="9500111" />
    <data hurdle_id="195" affix_id="9400105" />
    <data hurdle_id="195" affix_id="9500101" />
    <data hurdle_id="195" affix_id="9500111" />
    <data hurdle_id="196" affix_id="9400105" />
    <data hurdle_id="196" affix_id="9500101" />
    <data hurdle_id="196" affix_id="9500111" />
    <data hurdle_id="197" affix_id="9400105" />
    <data hurdle_id="197" affix_id="9500101" />
    <data hurdle_id="197" affix_id="9500111" />
    <data hurdle_id="198" affix_id="9400105" />
    <data hurdle_id="198" affix_id="9500101" />
    <data hurdle_id="198" affix_id="9500111" />
    <data hurdle_id="199" affix_id="9400105" />
    <data hurdle_id="199" affix_id="9500101" />
    <data hurdle_id="199" affix_id="9500111" />
    <data hurdle_id="200" affix_id="9400105" />
    <data hurdle_id="200" affix_id="9500101" />
    <data hurdle_id="200" affix_id="9500111" />
    <data hurdle_id="201" affix_id="9400103" />
    <data hurdle_id="201" affix_id="9500101" />
    <data hurdle_id="201" affix_id="9500104" />
    <data hurdle_id="202" affix_id="9400103" />
    <data hurdle_id="202" affix_id="9500101" />
    <data hurdle_id="202" affix_id="9500105" />
    <data hurdle_id="203" affix_id="9400103" />
    <data hurdle_id="203" affix_id="9500101" />
    <data hurdle_id="203" affix_id="9500106" />
    <data hurdle_id="204" affix_id="9400103" />
    <data hurdle_id="204" affix_id="9500101" />
    <data hurdle_id="204" affix_id="9500107" />
    <data hurdle_id="205" affix_id="9400103" />
    <data hurdle_id="205" affix_id="9500101" />
    <data hurdle_id="205" affix_id="9500108" />
    <data hurdle_id="206" affix_id="9400103" />
    <data hurdle_id="206" affix_id="9500101" />
    <data hurdle_id="206" affix_id="9500109" />
    <data hurdle_id="207" affix_id="9400103" />
    <data hurdle_id="207" affix_id="9500101" />
    <data hurdle_id="207" affix_id="9500111" />
    <data hurdle_id="208" affix_id="9400103" />
    <data hurdle_id="208" affix_id="9500101" />
    <data hurdle_id="208" affix_id="9500111" />
    <data hurdle_id="209" affix_id="9400103" />
    <data hurdle_id="209" affix_id="9500101" />
    <data hurdle_id="209" affix_id="9500111" />
    <data hurdle_id="210" affix_id="9400103" />
    <data hurdle_id="210" affix_id="9500101" />
    <data hurdle_id="210" affix_id="9500111" />
    <data hurdle_id="211" affix_id="9400103" />
    <data hurdle_id="211" affix_id="9500101" />
    <data hurdle_id="211" affix_id="9500111" />
    <data hurdle_id="212" affix_id="9400103" />
    <data hurdle_id="212" affix_id="9500101" />
    <data hurdle_id="212" affix_id="9500111" />
    <data hurdle_id="213" affix_id="9400103" />
    <data hurdle_id="213" affix_id="9500101" />
    <data hurdle_id="213" affix_id="9500111" />
    <data hurdle_id="214" affix_id="9400103" />
    <data hurdle_id="214" affix_id="9500101" />
    <data hurdle_id="214" affix_id="9500111" />
    <data hurdle_id="215" affix_id="9400103" />
    <data hurdle_id="215" affix_id="9500101" />
    <data hurdle_id="215" affix_id="9500111" />
    <data hurdle_id="216" affix_id="9400103" />
    <data hurdle_id="216" affix_id="9500101" />
    <data hurdle_id="216" affix_id="9500111" />
    <data hurdle_id="217" affix_id="9400103" />
    <data hurdle_id="217" affix_id="9500101" />
    <data hurdle_id="217" affix_id="9500111" />
    <data hurdle_id="218" affix_id="9400103" />
    <data hurdle_id="218" affix_id="9500101" />
    <data hurdle_id="218" affix_id="9500102" />
    <data hurdle_id="219" affix_id="9400103" />
    <data hurdle_id="219" affix_id="9500101" />
    <data hurdle_id="219" affix_id="9500110" />
    <data hurdle_id="220" affix_id="9400103" />
    <data hurdle_id="220" affix_id="9500101" />
    <data hurdle_id="220" affix_id="9500111" />
    <data hurdle_id="221" affix_id="9400103" />
    <data hurdle_id="221" affix_id="9500101" />
    <data hurdle_id="221" affix_id="9500111" />
    <data hurdle_id="222" affix_id="9400103" />
    <data hurdle_id="222" affix_id="9500101" />
    <data hurdle_id="222" affix_id="9500111" />
    <data hurdle_id="223" affix_id="9400103" />
    <data hurdle_id="223" affix_id="9500101" />
    <data hurdle_id="223" affix_id="9500111" />
    <data hurdle_id="224" affix_id="9400103" />
    <data hurdle_id="224" affix_id="9500101" />
    <data hurdle_id="224" affix_id="9500111" />
    <data hurdle_id="225" affix_id="9400103" />
    <data hurdle_id="225" affix_id="9500101" />
    <data hurdle_id="225" affix_id="9500111" />
    <data hurdle_id="226" affix_id="9400103" />
    <data hurdle_id="226" affix_id="9500101" />
    <data hurdle_id="226" affix_id="9500110" />
    <data hurdle_id="227" affix_id="9400103" />
    <data hurdle_id="227" affix_id="9500101" />
    <data hurdle_id="227" affix_id="9500110" />
    <data hurdle_id="228" affix_id="9400103" />
    <data hurdle_id="228" affix_id="9500101" />
    <data hurdle_id="228" affix_id="9500111" />
    <data hurdle_id="229" affix_id="9400103" />
    <data hurdle_id="229" affix_id="9500101" />
    <data hurdle_id="229" affix_id="9500111" />
    <data hurdle_id="230" affix_id="9400103" />
    <data hurdle_id="230" affix_id="9500101" />
    <data hurdle_id="230" affix_id="9500111" />
    <data hurdle_id="231" affix_id="9400103" />
    <data hurdle_id="231" affix_id="9500101" />
    <data hurdle_id="231" affix_id="9500111" />
    <data hurdle_id="232" affix_id="9400103" />
    <data hurdle_id="232" affix_id="9500101" />
    <data hurdle_id="232" affix_id="9500111" />
    <data hurdle_id="233" affix_id="9400103" />
    <data hurdle_id="233" affix_id="9500101" />
    <data hurdle_id="233" affix_id="9500111" />
    <data hurdle_id="234" affix_id="9400103" />
    <data hurdle_id="234" affix_id="9500101" />
    <data hurdle_id="234" affix_id="9500111" />
    <data hurdle_id="235" affix_id="9400103" />
    <data hurdle_id="235" affix_id="9500101" />
    <data hurdle_id="235" affix_id="9500111" />
    <data hurdle_id="236" affix_id="9400103" />
    <data hurdle_id="236" affix_id="9500101" />
    <data hurdle_id="236" affix_id="9500111" />
    <data hurdle_id="237" affix_id="9400103" />
    <data hurdle_id="237" affix_id="9500101" />
    <data hurdle_id="237" affix_id="9500111" />
    <data hurdle_id="238" affix_id="9400103" />
    <data hurdle_id="238" affix_id="9500101" />
    <data hurdle_id="238" affix_id="9500111" />
    <data hurdle_id="239" affix_id="9400103" />
    <data hurdle_id="239" affix_id="9500101" />
    <data hurdle_id="239" affix_id="9500111" />
    <data hurdle_id="240" affix_id="9400103" />
    <data hurdle_id="240" affix_id="9500101" />
    <data hurdle_id="240" affix_id="9500111" />
    <data hurdle_id="241" affix_id="9400103" />
    <data hurdle_id="241" affix_id="9500101" />
    <data hurdle_id="241" affix_id="9500111" />
    <data hurdle_id="242" affix_id="9400103" />
    <data hurdle_id="242" affix_id="9500101" />
    <data hurdle_id="242" affix_id="9500111" />
    <data hurdle_id="243" affix_id="9400103" />
    <data hurdle_id="243" affix_id="9500101" />
    <data hurdle_id="243" affix_id="9500111" />
    <data hurdle_id="244" affix_id="9400103" />
    <data hurdle_id="244" affix_id="9500101" />
    <data hurdle_id="244" affix_id="9500111" />
    <data hurdle_id="245" affix_id="9400103" />
    <data hurdle_id="245" affix_id="9500101" />
    <data hurdle_id="245" affix_id="9500111" />
    <data hurdle_id="246" affix_id="9400103" />
    <data hurdle_id="246" affix_id="9500101" />
    <data hurdle_id="246" affix_id="9500111" />
    <data hurdle_id="247" affix_id="9400103" />
    <data hurdle_id="247" affix_id="9500101" />
    <data hurdle_id="247" affix_id="9500111" />
    <data hurdle_id="248" affix_id="9400103" />
    <data hurdle_id="248" affix_id="9500101" />
    <data hurdle_id="248" affix_id="9500111" />
    <data hurdle_id="249" affix_id="9400103" />
    <data hurdle_id="249" affix_id="9500101" />
    <data hurdle_id="249" affix_id="9500111" />
    <data hurdle_id="250" affix_id="9400103" />
    <data hurdle_id="250" affix_id="9500101" />
    <data hurdle_id="250" affix_id="9500111" />
    <data hurdle_id="251" affix_id="9400103" />
    <data hurdle_id="251" affix_id="9500101" />
    <data hurdle_id="251" affix_id="9500111" />
    <data hurdle_id="252" affix_id="9400103" />
    <data hurdle_id="252" affix_id="9500101" />
    <data hurdle_id="252" affix_id="9500111" />
    <data hurdle_id="253" affix_id="9400103" />
    <data hurdle_id="253" affix_id="9500101" />
    <data hurdle_id="253" affix_id="9500111" />
    <data hurdle_id="254" affix_id="9400103" />
    <data hurdle_id="254" affix_id="9500101" />
    <data hurdle_id="254" affix_id="9500111" />
    <data hurdle_id="255" affix_id="9400103" />
    <data hurdle_id="255" affix_id="9500101" />
    <data hurdle_id="255" affix_id="9500111" />
    <data hurdle_id="256" affix_id="9400103" />
    <data hurdle_id="256" affix_id="9500101" />
    <data hurdle_id="256" affix_id="9500111" />
    <data hurdle_id="257" affix_id="9400103" />
    <data hurdle_id="257" affix_id="9500101" />
    <data hurdle_id="257" affix_id="9500111" />
    <data hurdle_id="258" affix_id="9400103" />
    <data hurdle_id="258" affix_id="9500101" />
    <data hurdle_id="258" affix_id="9500111" />
    <data hurdle_id="259" affix_id="9400103" />
    <data hurdle_id="259" affix_id="9500101" />
    <data hurdle_id="259" affix_id="9500111" />
    <data hurdle_id="260" affix_id="9400103" />
    <data hurdle_id="260" affix_id="9500101" />
    <data hurdle_id="260" affix_id="9500111" />
    <data hurdle_id="261" affix_id="9400103" />
    <data hurdle_id="261" affix_id="9500101" />
    <data hurdle_id="261" affix_id="9500111" />
    <data hurdle_id="262" affix_id="9400103" />
    <data hurdle_id="262" affix_id="9500101" />
    <data hurdle_id="262" affix_id="9500111" />
    <data hurdle_id="263" affix_id="9400103" />
    <data hurdle_id="263" affix_id="9500101" />
    <data hurdle_id="263" affix_id="9500111" />
    <data hurdle_id="264" affix_id="9400103" />
    <data hurdle_id="264" affix_id="9500101" />
    <data hurdle_id="264" affix_id="9500111" />
    <data hurdle_id="265" affix_id="9400103" />
    <data hurdle_id="265" affix_id="9500101" />
    <data hurdle_id="265" affix_id="9500111" />
    <data hurdle_id="266" affix_id="9400103" />
    <data hurdle_id="266" affix_id="9500101" />
    <data hurdle_id="266" affix_id="9500111" />
    <data hurdle_id="267" affix_id="9400103" />
    <data hurdle_id="267" affix_id="9500101" />
    <data hurdle_id="267" affix_id="9500111" />
    <data hurdle_id="268" affix_id="9400103" />
    <data hurdle_id="268" affix_id="9500101" />
    <data hurdle_id="268" affix_id="9500111" />
    <data hurdle_id="269" affix_id="9400103" />
    <data hurdle_id="269" affix_id="9500101" />
    <data hurdle_id="269" affix_id="9500111" />
    <data hurdle_id="270" affix_id="9400103" />
    <data hurdle_id="270" affix_id="9500101" />
    <data hurdle_id="270" affix_id="9500111" />
    <data hurdle_id="271" affix_id="9400103" />
    <data hurdle_id="271" affix_id="9500101" />
    <data hurdle_id="271" affix_id="9500111" />
    <data hurdle_id="272" affix_id="9400103" />
    <data hurdle_id="272" affix_id="9500101" />
    <data hurdle_id="272" affix_id="9500111" />
    <data hurdle_id="273" affix_id="9400103" />
    <data hurdle_id="273" affix_id="9500101" />
    <data hurdle_id="273" affix_id="9500111" />
    <data hurdle_id="274" affix_id="9400103" />
    <data hurdle_id="274" affix_id="9500101" />
    <data hurdle_id="274" affix_id="9500111" />
    <data hurdle_id="275" affix_id="9400103" />
    <data hurdle_id="275" affix_id="9500101" />
    <data hurdle_id="275" affix_id="9500111" />
    <data hurdle_id="276" affix_id="9400103" />
    <data hurdle_id="276" affix_id="9500101" />
    <data hurdle_id="276" affix_id="9500111" />
    <data hurdle_id="277" affix_id="9400103" />
    <data hurdle_id="277" affix_id="9500101" />
    <data hurdle_id="277" affix_id="9500111" />
    <data hurdle_id="278" affix_id="9400103" />
    <data hurdle_id="278" affix_id="9500101" />
    <data hurdle_id="278" affix_id="9500111" />
    <data hurdle_id="279" affix_id="9400103" />
    <data hurdle_id="279" affix_id="9500101" />
    <data hurdle_id="279" affix_id="9500111" />
    <data hurdle_id="280" affix_id="9400103" />
    <data hurdle_id="280" affix_id="9500101" />
    <data hurdle_id="280" affix_id="9500111" />
    <data hurdle_id="281" affix_id="9400103" />
    <data hurdle_id="281" affix_id="9500101" />
    <data hurdle_id="281" affix_id="9500111" />
    <data hurdle_id="282" affix_id="9400103" />
    <data hurdle_id="282" affix_id="9500101" />
    <data hurdle_id="282" affix_id="9500111" />
    <data hurdle_id="283" affix_id="9400103" />
    <data hurdle_id="283" affix_id="9500101" />
    <data hurdle_id="283" affix_id="9500111" />
    <data hurdle_id="284" affix_id="9400103" />
    <data hurdle_id="284" affix_id="9500101" />
    <data hurdle_id="284" affix_id="9500111" />
    <data hurdle_id="285" affix_id="9400103" />
    <data hurdle_id="285" affix_id="9500101" />
    <data hurdle_id="285" affix_id="9500111" />
    <data hurdle_id="286" affix_id="9400103" />
    <data hurdle_id="286" affix_id="9500101" />
    <data hurdle_id="286" affix_id="9500111" />
    <data hurdle_id="287" affix_id="9400103" />
    <data hurdle_id="287" affix_id="9500101" />
    <data hurdle_id="287" affix_id="9500111" />
    <data hurdle_id="288" affix_id="9400103" />
    <data hurdle_id="288" affix_id="9500101" />
    <data hurdle_id="288" affix_id="9500111" />
    <data hurdle_id="289" affix_id="9400103" />
    <data hurdle_id="289" affix_id="9500101" />
    <data hurdle_id="289" affix_id="9500111" />
    <data hurdle_id="290" affix_id="9400103" />
    <data hurdle_id="290" affix_id="9500101" />
    <data hurdle_id="290" affix_id="9500111" />
    <data hurdle_id="291" affix_id="9400103" />
    <data hurdle_id="291" affix_id="9500101" />
    <data hurdle_id="291" affix_id="9500111" />
    <data hurdle_id="292" affix_id="9400103" />
    <data hurdle_id="292" affix_id="9500101" />
    <data hurdle_id="292" affix_id="9500111" />
    <data hurdle_id="293" affix_id="9400103" />
    <data hurdle_id="293" affix_id="9500101" />
    <data hurdle_id="293" affix_id="9500111" />
    <data hurdle_id="294" affix_id="9400103" />
    <data hurdle_id="294" affix_id="9500101" />
    <data hurdle_id="294" affix_id="9500111" />
    <data hurdle_id="295" affix_id="9400103" />
    <data hurdle_id="295" affix_id="9500101" />
    <data hurdle_id="295" affix_id="9500111" />
    <data hurdle_id="296" affix_id="9400103" />
    <data hurdle_id="296" affix_id="9500101" />
    <data hurdle_id="296" affix_id="9500111" />
    <data hurdle_id="297" affix_id="9400103" />
    <data hurdle_id="297" affix_id="9500101" />
    <data hurdle_id="297" affix_id="9500111" />
    <data hurdle_id="298" affix_id="9400103" />
    <data hurdle_id="298" affix_id="9500101" />
    <data hurdle_id="298" affix_id="9500111" />
    <data hurdle_id="299" affix_id="9400103" />
    <data hurdle_id="299" affix_id="9500101" />
    <data hurdle_id="299" affix_id="9500111" />
    <data hurdle_id="300" affix_id="9400103" />
    <data hurdle_id="300" affix_id="9500101" />
    <data hurdle_id="300" affix_id="9500111" />
    <data hurdle_id="301" affix_id="9400104" />
    <data hurdle_id="301" affix_id="9500101" />
    <data hurdle_id="301" affix_id="9500104" />
    <data hurdle_id="302" affix_id="9400104" />
    <data hurdle_id="302" affix_id="9500101" />
    <data hurdle_id="302" affix_id="9500105" />
    <data hurdle_id="303" affix_id="9400104" />
    <data hurdle_id="303" affix_id="9500101" />
    <data hurdle_id="303" affix_id="9500106" />
    <data hurdle_id="304" affix_id="9400104" />
    <data hurdle_id="304" affix_id="9500101" />
    <data hurdle_id="304" affix_id="9500107" />
    <data hurdle_id="305" affix_id="9400104" />
    <data hurdle_id="305" affix_id="9500101" />
    <data hurdle_id="305" affix_id="9500108" />
    <data hurdle_id="306" affix_id="9400104" />
    <data hurdle_id="306" affix_id="9500101" />
    <data hurdle_id="306" affix_id="9500109" />
    <data hurdle_id="307" affix_id="9400104" />
    <data hurdle_id="307" affix_id="9500101" />
    <data hurdle_id="307" affix_id="9500111" />
    <data hurdle_id="308" affix_id="9400104" />
    <data hurdle_id="308" affix_id="9500101" />
    <data hurdle_id="308" affix_id="9500111" />
    <data hurdle_id="309" affix_id="9400104" />
    <data hurdle_id="309" affix_id="9500101" />
    <data hurdle_id="309" affix_id="9500111" />
    <data hurdle_id="310" affix_id="9400104" />
    <data hurdle_id="310" affix_id="9500101" />
    <data hurdle_id="310" affix_id="9500111" />
    <data hurdle_id="311" affix_id="9400104" />
    <data hurdle_id="311" affix_id="9500101" />
    <data hurdle_id="311" affix_id="9500111" />
    <data hurdle_id="312" affix_id="9400104" />
    <data hurdle_id="312" affix_id="9500101" />
    <data hurdle_id="312" affix_id="9500111" />
    <data hurdle_id="313" affix_id="9400104" />
    <data hurdle_id="313" affix_id="9500101" />
    <data hurdle_id="313" affix_id="9500111" />
    <data hurdle_id="314" affix_id="9400104" />
    <data hurdle_id="314" affix_id="9500101" />
    <data hurdle_id="314" affix_id="9500111" />
    <data hurdle_id="315" affix_id="9400104" />
    <data hurdle_id="315" affix_id="9500101" />
    <data hurdle_id="315" affix_id="9500111" />
    <data hurdle_id="316" affix_id="9400104" />
    <data hurdle_id="316" affix_id="9500101" />
    <data hurdle_id="316" affix_id="9500111" />
    <data hurdle_id="317" affix_id="9400104" />
    <data hurdle_id="317" affix_id="9500101" />
    <data hurdle_id="317" affix_id="9500111" />
    <data hurdle_id="318" affix_id="9400104" />
    <data hurdle_id="318" affix_id="9500101" />
    <data hurdle_id="318" affix_id="9500102" />
    <data hurdle_id="319" affix_id="9400104" />
    <data hurdle_id="319" affix_id="9500101" />
    <data hurdle_id="319" affix_id="9500110" />
    <data hurdle_id="320" affix_id="9400104" />
    <data hurdle_id="320" affix_id="9500101" />
    <data hurdle_id="320" affix_id="9500111" />
    <data hurdle_id="321" affix_id="9400104" />
    <data hurdle_id="321" affix_id="9500101" />
    <data hurdle_id="321" affix_id="9500111" />
    <data hurdle_id="322" affix_id="9400104" />
    <data hurdle_id="322" affix_id="9500101" />
    <data hurdle_id="322" affix_id="9500111" />
    <data hurdle_id="323" affix_id="9400104" />
    <data hurdle_id="323" affix_id="9500101" />
    <data hurdle_id="323" affix_id="9500111" />
    <data hurdle_id="324" affix_id="9400104" />
    <data hurdle_id="324" affix_id="9500101" />
    <data hurdle_id="324" affix_id="9500111" />
    <data hurdle_id="325" affix_id="9400104" />
    <data hurdle_id="325" affix_id="9500101" />
    <data hurdle_id="325" affix_id="9500111" />
    <data hurdle_id="326" affix_id="9400104" />
    <data hurdle_id="326" affix_id="9500101" />
    <data hurdle_id="326" affix_id="9500110" />
    <data hurdle_id="327" affix_id="9400104" />
    <data hurdle_id="327" affix_id="9500101" />
    <data hurdle_id="327" affix_id="9500110" />
    <data hurdle_id="328" affix_id="9400104" />
    <data hurdle_id="328" affix_id="9500101" />
    <data hurdle_id="328" affix_id="9500111" />
    <data hurdle_id="329" affix_id="9400104" />
    <data hurdle_id="329" affix_id="9500101" />
    <data hurdle_id="329" affix_id="9500111" />
    <data hurdle_id="330" affix_id="9400104" />
    <data hurdle_id="330" affix_id="9500101" />
    <data hurdle_id="330" affix_id="9500111" />
    <data hurdle_id="331" affix_id="9400104" />
    <data hurdle_id="331" affix_id="9500101" />
    <data hurdle_id="331" affix_id="9500111" />
    <data hurdle_id="332" affix_id="9400104" />
    <data hurdle_id="332" affix_id="9500101" />
    <data hurdle_id="332" affix_id="9500111" />
    <data hurdle_id="333" affix_id="9400104" />
    <data hurdle_id="333" affix_id="9500101" />
    <data hurdle_id="333" affix_id="9500111" />
    <data hurdle_id="334" affix_id="9400104" />
    <data hurdle_id="334" affix_id="9500101" />
    <data hurdle_id="334" affix_id="9500111" />
    <data hurdle_id="335" affix_id="9400104" />
    <data hurdle_id="335" affix_id="9500101" />
    <data hurdle_id="335" affix_id="9500111" />
    <data hurdle_id="336" affix_id="9400104" />
    <data hurdle_id="336" affix_id="9500101" />
    <data hurdle_id="336" affix_id="9500111" />
    <data hurdle_id="337" affix_id="9400104" />
    <data hurdle_id="337" affix_id="9500101" />
    <data hurdle_id="337" affix_id="9500111" />
    <data hurdle_id="338" affix_id="9400104" />
    <data hurdle_id="338" affix_id="9500101" />
    <data hurdle_id="338" affix_id="9500111" />
    <data hurdle_id="339" affix_id="9400104" />
    <data hurdle_id="339" affix_id="9500101" />
    <data hurdle_id="339" affix_id="9500111" />
    <data hurdle_id="340" affix_id="9400104" />
    <data hurdle_id="340" affix_id="9500101" />
    <data hurdle_id="340" affix_id="9500111" />
    <data hurdle_id="341" affix_id="9400104" />
    <data hurdle_id="341" affix_id="9500101" />
    <data hurdle_id="341" affix_id="9500111" />
    <data hurdle_id="342" affix_id="9400104" />
    <data hurdle_id="342" affix_id="9500101" />
    <data hurdle_id="342" affix_id="9500111" />
    <data hurdle_id="343" affix_id="9400104" />
    <data hurdle_id="343" affix_id="9500101" />
    <data hurdle_id="343" affix_id="9500111" />
    <data hurdle_id="344" affix_id="9400104" />
    <data hurdle_id="344" affix_id="9500101" />
    <data hurdle_id="344" affix_id="9500111" />
    <data hurdle_id="345" affix_id="9400104" />
    <data hurdle_id="345" affix_id="9500101" />
    <data hurdle_id="345" affix_id="9500111" />
    <data hurdle_id="346" affix_id="9400104" />
    <data hurdle_id="346" affix_id="9500101" />
    <data hurdle_id="346" affix_id="9500111" />
    <data hurdle_id="347" affix_id="9400104" />
    <data hurdle_id="347" affix_id="9500101" />
    <data hurdle_id="347" affix_id="9500111" />
    <data hurdle_id="348" affix_id="9400104" />
    <data hurdle_id="348" affix_id="9500101" />
    <data hurdle_id="348" affix_id="9500111" />
    <data hurdle_id="349" affix_id="9400104" />
    <data hurdle_id="349" affix_id="9500101" />
    <data hurdle_id="349" affix_id="9500111" />
    <data hurdle_id="350" affix_id="9400104" />
    <data hurdle_id="350" affix_id="9500101" />
    <data hurdle_id="350" affix_id="9500111" />
    <data hurdle_id="351" affix_id="9400104" />
    <data hurdle_id="351" affix_id="9500101" />
    <data hurdle_id="351" affix_id="9500111" />
    <data hurdle_id="352" affix_id="9400104" />
    <data hurdle_id="352" affix_id="9500101" />
    <data hurdle_id="352" affix_id="9500111" />
    <data hurdle_id="353" affix_id="9400104" />
    <data hurdle_id="353" affix_id="9500101" />
    <data hurdle_id="353" affix_id="9500111" />
    <data hurdle_id="354" affix_id="9400104" />
    <data hurdle_id="354" affix_id="9500101" />
    <data hurdle_id="354" affix_id="9500111" />
    <data hurdle_id="355" affix_id="9400104" />
    <data hurdle_id="355" affix_id="9500101" />
    <data hurdle_id="355" affix_id="9500111" />
    <data hurdle_id="356" affix_id="9400104" />
    <data hurdle_id="356" affix_id="9500101" />
    <data hurdle_id="356" affix_id="9500111" />
    <data hurdle_id="357" affix_id="9400104" />
    <data hurdle_id="357" affix_id="9500101" />
    <data hurdle_id="357" affix_id="9500111" />
    <data hurdle_id="358" affix_id="9400104" />
    <data hurdle_id="358" affix_id="9500101" />
    <data hurdle_id="358" affix_id="9500111" />
    <data hurdle_id="359" affix_id="9400104" />
    <data hurdle_id="359" affix_id="9500101" />
    <data hurdle_id="359" affix_id="9500111" />
    <data hurdle_id="360" affix_id="9400104" />
    <data hurdle_id="360" affix_id="9500101" />
    <data hurdle_id="360" affix_id="9500111" />
    <data hurdle_id="361" affix_id="9400104" />
    <data hurdle_id="361" affix_id="9500101" />
    <data hurdle_id="361" affix_id="9500111" />
    <data hurdle_id="362" affix_id="9400104" />
    <data hurdle_id="362" affix_id="9500101" />
    <data hurdle_id="362" affix_id="9500111" />
    <data hurdle_id="363" affix_id="9400104" />
    <data hurdle_id="363" affix_id="9500101" />
    <data hurdle_id="363" affix_id="9500111" />
    <data hurdle_id="364" affix_id="9400104" />
    <data hurdle_id="364" affix_id="9500101" />
    <data hurdle_id="364" affix_id="9500111" />
    <data hurdle_id="365" affix_id="9400104" />
    <data hurdle_id="365" affix_id="9500101" />
    <data hurdle_id="365" affix_id="9500111" />
    <data hurdle_id="366" affix_id="9400104" />
    <data hurdle_id="366" affix_id="9500101" />
    <data hurdle_id="366" affix_id="9500111" />
    <data hurdle_id="367" affix_id="9400104" />
    <data hurdle_id="367" affix_id="9500101" />
    <data hurdle_id="367" affix_id="9500111" />
    <data hurdle_id="368" affix_id="9400104" />
    <data hurdle_id="368" affix_id="9500101" />
    <data hurdle_id="368" affix_id="9500111" />
    <data hurdle_id="369" affix_id="9400104" />
    <data hurdle_id="369" affix_id="9500101" />
    <data hurdle_id="369" affix_id="9500111" />
    <data hurdle_id="370" affix_id="9400104" />
    <data hurdle_id="370" affix_id="9500101" />
    <data hurdle_id="370" affix_id="9500111" />
    <data hurdle_id="371" affix_id="9400104" />
    <data hurdle_id="371" affix_id="9500101" />
    <data hurdle_id="371" affix_id="9500111" />
    <data hurdle_id="372" affix_id="9400104" />
    <data hurdle_id="372" affix_id="9500101" />
    <data hurdle_id="372" affix_id="9500111" />
    <data hurdle_id="373" affix_id="9400104" />
    <data hurdle_id="373" affix_id="9500101" />
    <data hurdle_id="373" affix_id="9500111" />
    <data hurdle_id="374" affix_id="9400104" />
    <data hurdle_id="374" affix_id="9500101" />
    <data hurdle_id="374" affix_id="9500111" />
    <data hurdle_id="375" affix_id="9400104" />
    <data hurdle_id="375" affix_id="9500101" />
    <data hurdle_id="375" affix_id="9500111" />
    <data hurdle_id="376" affix_id="9400104" />
    <data hurdle_id="376" affix_id="9500101" />
    <data hurdle_id="376" affix_id="9500111" />
    <data hurdle_id="377" affix_id="9400104" />
    <data hurdle_id="377" affix_id="9500101" />
    <data hurdle_id="377" affix_id="9500111" />
    <data hurdle_id="378" affix_id="9400104" />
    <data hurdle_id="378" affix_id="9500101" />
    <data hurdle_id="378" affix_id="9500111" />
    <data hurdle_id="379" affix_id="9400104" />
    <data hurdle_id="379" affix_id="9500101" />
    <data hurdle_id="379" affix_id="9500111" />
    <data hurdle_id="380" affix_id="9400104" />
    <data hurdle_id="380" affix_id="9500101" />
    <data hurdle_id="380" affix_id="9500111" />
    <data hurdle_id="381" affix_id="9400104" />
    <data hurdle_id="381" affix_id="9500101" />
    <data hurdle_id="381" affix_id="9500111" />
    <data hurdle_id="382" affix_id="9400104" />
    <data hurdle_id="382" affix_id="9500101" />
    <data hurdle_id="382" affix_id="9500111" />
    <data hurdle_id="383" affix_id="9400104" />
    <data hurdle_id="383" affix_id="9500101" />
    <data hurdle_id="383" affix_id="9500111" />
    <data hurdle_id="384" affix_id="9400104" />
    <data hurdle_id="384" affix_id="9500101" />
    <data hurdle_id="384" affix_id="9500111" />
    <data hurdle_id="385" affix_id="9400104" />
    <data hurdle_id="385" affix_id="9500101" />
    <data hurdle_id="385" affix_id="9500111" />
    <data hurdle_id="386" affix_id="9400104" />
    <data hurdle_id="386" affix_id="9500101" />
    <data hurdle_id="386" affix_id="9500111" />
    <data hurdle_id="387" affix_id="9400104" />
    <data hurdle_id="387" affix_id="9500101" />
    <data hurdle_id="387" affix_id="9500111" />
    <data hurdle_id="388" affix_id="9400104" />
    <data hurdle_id="388" affix_id="9500101" />
    <data hurdle_id="388" affix_id="9500111" />
    <data hurdle_id="389" affix_id="9400104" />
    <data hurdle_id="389" affix_id="9500101" />
    <data hurdle_id="389" affix_id="9500111" />
    <data hurdle_id="390" affix_id="9400104" />
    <data hurdle_id="390" affix_id="9500101" />
    <data hurdle_id="390" affix_id="9500111" />
    <data hurdle_id="391" affix_id="9400104" />
    <data hurdle_id="391" affix_id="9500101" />
    <data hurdle_id="391" affix_id="9500111" />
    <data hurdle_id="392" affix_id="9400104" />
    <data hurdle_id="392" affix_id="9500101" />
    <data hurdle_id="392" affix_id="9500111" />
    <data hurdle_id="393" affix_id="9400104" />
    <data hurdle_id="393" affix_id="9500101" />
    <data hurdle_id="393" affix_id="9500111" />
    <data hurdle_id="394" affix_id="9400104" />
    <data hurdle_id="394" affix_id="9500101" />
    <data hurdle_id="394" affix_id="9500111" />
    <data hurdle_id="395" affix_id="9400104" />
    <data hurdle_id="395" affix_id="9500101" />
    <data hurdle_id="395" affix_id="9500111" />
    <data hurdle_id="396" affix_id="9400104" />
    <data hurdle_id="396" affix_id="9500101" />
    <data hurdle_id="396" affix_id="9500111" />
    <data hurdle_id="397" affix_id="9400104" />
    <data hurdle_id="397" affix_id="9500101" />
    <data hurdle_id="397" affix_id="9500111" />
    <data hurdle_id="398" affix_id="9400104" />
    <data hurdle_id="398" affix_id="9500101" />
    <data hurdle_id="398" affix_id="9500111" />
    <data hurdle_id="399" affix_id="9400104" />
    <data hurdle_id="399" affix_id="9500101" />
    <data hurdle_id="399" affix_id="9500111" />
    <data hurdle_id="400" affix_id="9400104" />
    <data hurdle_id="400" affix_id="9500101" />
    <data hurdle_id="400" affix_id="9500111" />
    <data hurdle_id="401" affix_id="9400101" />
    <data hurdle_id="401" affix_id="9500101" />
    <data hurdle_id="401" affix_id="9500104" />
    <data hurdle_id="402" affix_id="9400101" />
    <data hurdle_id="402" affix_id="9500101" />
    <data hurdle_id="402" affix_id="9500105" />
    <data hurdle_id="403" affix_id="9400101" />
    <data hurdle_id="403" affix_id="9500101" />
    <data hurdle_id="403" affix_id="9500106" />
    <data hurdle_id="404" affix_id="9400101" />
    <data hurdle_id="404" affix_id="9500101" />
    <data hurdle_id="404" affix_id="9500107" />
    <data hurdle_id="405" affix_id="9400101" />
    <data hurdle_id="405" affix_id="9500101" />
    <data hurdle_id="405" affix_id="9500108" />
    <data hurdle_id="406" affix_id="9400101" />
    <data hurdle_id="406" affix_id="9500101" />
    <data hurdle_id="406" affix_id="9500109" />
    <data hurdle_id="407" affix_id="9400101" />
    <data hurdle_id="407" affix_id="9500101" />
    <data hurdle_id="407" affix_id="9500111" />
    <data hurdle_id="408" affix_id="9400101" />
    <data hurdle_id="408" affix_id="9500101" />
    <data hurdle_id="408" affix_id="9500111" />
    <data hurdle_id="409" affix_id="9400101" />
    <data hurdle_id="409" affix_id="9500101" />
    <data hurdle_id="409" affix_id="9500111" />
    <data hurdle_id="410" affix_id="9400101" />
    <data hurdle_id="410" affix_id="9500101" />
    <data hurdle_id="410" affix_id="9500111" />
    <data hurdle_id="411" affix_id="9400101" />
    <data hurdle_id="411" affix_id="9500101" />
    <data hurdle_id="411" affix_id="9500111" />
    <data hurdle_id="412" affix_id="9400101" />
    <data hurdle_id="412" affix_id="9500101" />
    <data hurdle_id="412" affix_id="9500111" />
    <data hurdle_id="413" affix_id="9400101" />
    <data hurdle_id="413" affix_id="9500101" />
    <data hurdle_id="413" affix_id="9500111" />
    <data hurdle_id="414" affix_id="9400101" />
    <data hurdle_id="414" affix_id="9500101" />
    <data hurdle_id="414" affix_id="9500111" />
    <data hurdle_id="415" affix_id="9400101" />
    <data hurdle_id="415" affix_id="9500101" />
    <data hurdle_id="415" affix_id="9500111" />
    <data hurdle_id="416" affix_id="9400101" />
    <data hurdle_id="416" affix_id="9500101" />
    <data hurdle_id="416" affix_id="9500111" />
    <data hurdle_id="417" affix_id="9400101" />
    <data hurdle_id="417" affix_id="9500101" />
    <data hurdle_id="417" affix_id="9500111" />
    <data hurdle_id="418" affix_id="9400101" />
    <data hurdle_id="418" affix_id="9500101" />
    <data hurdle_id="418" affix_id="9500102" />
    <data hurdle_id="419" affix_id="9400101" />
    <data hurdle_id="419" affix_id="9500101" />
    <data hurdle_id="419" affix_id="9500110" />
    <data hurdle_id="420" affix_id="9400101" />
    <data hurdle_id="420" affix_id="9500101" />
    <data hurdle_id="420" affix_id="9500111" />
    <data hurdle_id="421" affix_id="9400101" />
    <data hurdle_id="421" affix_id="9500101" />
    <data hurdle_id="421" affix_id="9500111" />
    <data hurdle_id="422" affix_id="9400101" />
    <data hurdle_id="422" affix_id="9500101" />
    <data hurdle_id="422" affix_id="9500111" />
    <data hurdle_id="423" affix_id="9400101" />
    <data hurdle_id="423" affix_id="9500101" />
    <data hurdle_id="423" affix_id="9500111" />
    <data hurdle_id="424" affix_id="9400101" />
    <data hurdle_id="424" affix_id="9500101" />
    <data hurdle_id="424" affix_id="9500111" />
    <data hurdle_id="425" affix_id="9400101" />
    <data hurdle_id="425" affix_id="9500101" />
    <data hurdle_id="425" affix_id="9500111" />
    <data hurdle_id="426" affix_id="9400101" />
    <data hurdle_id="426" affix_id="9500101" />
    <data hurdle_id="426" affix_id="9500110" />
    <data hurdle_id="427" affix_id="9400101" />
    <data hurdle_id="427" affix_id="9500101" />
    <data hurdle_id="427" affix_id="9500110" />
    <data hurdle_id="428" affix_id="9400101" />
    <data hurdle_id="428" affix_id="9500101" />
    <data hurdle_id="428" affix_id="9500111" />
    <data hurdle_id="429" affix_id="9400101" />
    <data hurdle_id="429" affix_id="9500101" />
    <data hurdle_id="429" affix_id="9500111" />
    <data hurdle_id="430" affix_id="9400101" />
    <data hurdle_id="430" affix_id="9500101" />
    <data hurdle_id="430" affix_id="9500111" />
    <data hurdle_id="431" affix_id="9400101" />
    <data hurdle_id="431" affix_id="9500101" />
    <data hurdle_id="431" affix_id="9500111" />
    <data hurdle_id="432" affix_id="9400101" />
    <data hurdle_id="432" affix_id="9500101" />
    <data hurdle_id="432" affix_id="9500111" />
    <data hurdle_id="433" affix_id="9400101" />
    <data hurdle_id="433" affix_id="9500101" />
    <data hurdle_id="433" affix_id="9500111" />
    <data hurdle_id="434" affix_id="9400101" />
    <data hurdle_id="434" affix_id="9500101" />
    <data hurdle_id="434" affix_id="9500111" />
    <data hurdle_id="435" affix_id="9400101" />
    <data hurdle_id="435" affix_id="9500101" />
    <data hurdle_id="435" affix_id="9500111" />
    <data hurdle_id="436" affix_id="9400101" />
    <data hurdle_id="436" affix_id="9500101" />
    <data hurdle_id="436" affix_id="9500111" />
    <data hurdle_id="437" affix_id="9400101" />
    <data hurdle_id="437" affix_id="9500101" />
    <data hurdle_id="437" affix_id="9500111" />
    <data hurdle_id="438" affix_id="9400101" />
    <data hurdle_id="438" affix_id="9500101" />
    <data hurdle_id="438" affix_id="9500111" />
    <data hurdle_id="439" affix_id="9400101" />
    <data hurdle_id="439" affix_id="9500101" />
    <data hurdle_id="439" affix_id="9500111" />
    <data hurdle_id="440" affix_id="9400101" />
    <data hurdle_id="440" affix_id="9500101" />
    <data hurdle_id="440" affix_id="9500111" />
    <data hurdle_id="441" affix_id="9400101" />
    <data hurdle_id="441" affix_id="9500101" />
    <data hurdle_id="441" affix_id="9500111" />
    <data hurdle_id="442" affix_id="9400101" />
    <data hurdle_id="442" affix_id="9500101" />
    <data hurdle_id="442" affix_id="9500111" />
    <data hurdle_id="443" affix_id="9400101" />
    <data hurdle_id="443" affix_id="9500101" />
    <data hurdle_id="443" affix_id="9500111" />
    <data hurdle_id="444" affix_id="9400101" />
    <data hurdle_id="444" affix_id="9500101" />
    <data hurdle_id="444" affix_id="9500111" />
    <data hurdle_id="445" affix_id="9400101" />
    <data hurdle_id="445" affix_id="9500101" />
    <data hurdle_id="445" affix_id="9500111" />
    <data hurdle_id="446" affix_id="9400101" />
    <data hurdle_id="446" affix_id="9500101" />
    <data hurdle_id="446" affix_id="9500111" />
    <data hurdle_id="447" affix_id="9400101" />
    <data hurdle_id="447" affix_id="9500101" />
    <data hurdle_id="447" affix_id="9500111" />
    <data hurdle_id="448" affix_id="9400101" />
    <data hurdle_id="448" affix_id="9500101" />
    <data hurdle_id="448" affix_id="9500111" />
    <data hurdle_id="449" affix_id="9400101" />
    <data hurdle_id="449" affix_id="9500101" />
    <data hurdle_id="449" affix_id="9500111" />
    <data hurdle_id="450" affix_id="9400101" />
    <data hurdle_id="450" affix_id="9500101" />
    <data hurdle_id="450" affix_id="9500111" />
    <data hurdle_id="451" affix_id="9400101" />
    <data hurdle_id="451" affix_id="9500101" />
    <data hurdle_id="451" affix_id="9500111" />
    <data hurdle_id="452" affix_id="9400101" />
    <data hurdle_id="452" affix_id="9500101" />
    <data hurdle_id="452" affix_id="9500111" />
    <data hurdle_id="453" affix_id="9400101" />
    <data hurdle_id="453" affix_id="9500101" />
    <data hurdle_id="453" affix_id="9500111" />
    <data hurdle_id="454" affix_id="9400101" />
    <data hurdle_id="454" affix_id="9500101" />
    <data hurdle_id="454" affix_id="9500111" />
    <data hurdle_id="455" affix_id="9400101" />
    <data hurdle_id="455" affix_id="9500101" />
    <data hurdle_id="455" affix_id="9500111" />
    <data hurdle_id="456" affix_id="9400101" />
    <data hurdle_id="456" affix_id="9500101" />
    <data hurdle_id="456" affix_id="9500111" />
    <data hurdle_id="457" affix_id="9400101" />
    <data hurdle_id="457" affix_id="9500101" />
    <data hurdle_id="457" affix_id="9500111" />
    <data hurdle_id="458" affix_id="9400101" />
    <data hurdle_id="458" affix_id="9500101" />
    <data hurdle_id="458" affix_id="9500111" />
    <data hurdle_id="459" affix_id="9400101" />
    <data hurdle_id="459" affix_id="9500101" />
    <data hurdle_id="459" affix_id="9500111" />
    <data hurdle_id="460" affix_id="9400101" />
    <data hurdle_id="460" affix_id="9500101" />
    <data hurdle_id="460" affix_id="9500111" />
    <data hurdle_id="461" affix_id="9400101" />
    <data hurdle_id="461" affix_id="9500101" />
    <data hurdle_id="461" affix_id="9500111" />
    <data hurdle_id="462" affix_id="9400101" />
    <data hurdle_id="462" affix_id="9500101" />
    <data hurdle_id="462" affix_id="9500111" />
    <data hurdle_id="463" affix_id="9400101" />
    <data hurdle_id="463" affix_id="9500101" />
    <data hurdle_id="463" affix_id="9500111" />
    <data hurdle_id="464" affix_id="9400101" />
    <data hurdle_id="464" affix_id="9500101" />
    <data hurdle_id="464" affix_id="9500111" />
    <data hurdle_id="465" affix_id="9400101" />
    <data hurdle_id="465" affix_id="9500101" />
    <data hurdle_id="465" affix_id="9500111" />
    <data hurdle_id="466" affix_id="9400101" />
    <data hurdle_id="466" affix_id="9500101" />
    <data hurdle_id="466" affix_id="9500111" />
    <data hurdle_id="467" affix_id="9400101" />
    <data hurdle_id="467" affix_id="9500101" />
    <data hurdle_id="467" affix_id="9500111" />
    <data hurdle_id="468" affix_id="9400101" />
    <data hurdle_id="468" affix_id="9500101" />
    <data hurdle_id="468" affix_id="9500111" />
    <data hurdle_id="469" affix_id="9400101" />
    <data hurdle_id="469" affix_id="9500101" />
    <data hurdle_id="469" affix_id="9500111" />
    <data hurdle_id="470" affix_id="9400101" />
    <data hurdle_id="470" affix_id="9500101" />
    <data hurdle_id="470" affix_id="9500111" />
    <data hurdle_id="471" affix_id="9400101" />
    <data hurdle_id="471" affix_id="9500101" />
    <data hurdle_id="471" affix_id="9500111" />
    <data hurdle_id="472" affix_id="9400101" />
    <data hurdle_id="472" affix_id="9500101" />
    <data hurdle_id="472" affix_id="9500111" />
    <data hurdle_id="473" affix_id="9400101" />
    <data hurdle_id="473" affix_id="9500101" />
    <data hurdle_id="473" affix_id="9500111" />
    <data hurdle_id="474" affix_id="9400101" />
    <data hurdle_id="474" affix_id="9500101" />
    <data hurdle_id="474" affix_id="9500111" />
    <data hurdle_id="475" affix_id="9400101" />
    <data hurdle_id="475" affix_id="9500101" />
    <data hurdle_id="475" affix_id="9500111" />
    <data hurdle_id="476" affix_id="9400101" />
    <data hurdle_id="476" affix_id="9500101" />
    <data hurdle_id="476" affix_id="9500111" />
    <data hurdle_id="477" affix_id="9400101" />
    <data hurdle_id="477" affix_id="9500101" />
    <data hurdle_id="477" affix_id="9500111" />
    <data hurdle_id="478" affix_id="9400101" />
    <data hurdle_id="478" affix_id="9500101" />
    <data hurdle_id="478" affix_id="9500111" />
    <data hurdle_id="479" affix_id="9400101" />
    <data hurdle_id="479" affix_id="9500101" />
    <data hurdle_id="479" affix_id="9500111" />
    <data hurdle_id="480" affix_id="9400101" />
    <data hurdle_id="480" affix_id="9500101" />
    <data hurdle_id="480" affix_id="9500111" />
    <data hurdle_id="481" affix_id="9400101" />
    <data hurdle_id="481" affix_id="9500101" />
    <data hurdle_id="481" affix_id="9500111" />
    <data hurdle_id="482" affix_id="9400101" />
    <data hurdle_id="482" affix_id="9500101" />
    <data hurdle_id="482" affix_id="9500111" />
    <data hurdle_id="483" affix_id="9400101" />
    <data hurdle_id="483" affix_id="9500101" />
    <data hurdle_id="483" affix_id="9500111" />
    <data hurdle_id="484" affix_id="9400101" />
    <data hurdle_id="484" affix_id="9500101" />
    <data hurdle_id="484" affix_id="9500111" />
    <data hurdle_id="485" affix_id="9400101" />
    <data hurdle_id="485" affix_id="9500101" />
    <data hurdle_id="485" affix_id="9500111" />
    <data hurdle_id="486" affix_id="9400101" />
    <data hurdle_id="486" affix_id="9500101" />
    <data hurdle_id="486" affix_id="9500111" />
    <data hurdle_id="487" affix_id="9400101" />
    <data hurdle_id="487" affix_id="9500101" />
    <data hurdle_id="487" affix_id="9500111" />
    <data hurdle_id="488" affix_id="9400101" />
    <data hurdle_id="488" affix_id="9500101" />
    <data hurdle_id="488" affix_id="9500111" />
    <data hurdle_id="489" affix_id="9400101" />
    <data hurdle_id="489" affix_id="9500101" />
    <data hurdle_id="489" affix_id="9500111" />
    <data hurdle_id="490" affix_id="9400101" />
    <data hurdle_id="490" affix_id="9500101" />
    <data hurdle_id="490" affix_id="9500111" />
    <data hurdle_id="491" affix_id="9400101" />
    <data hurdle_id="491" affix_id="9500101" />
    <data hurdle_id="491" affix_id="9500111" />
    <data hurdle_id="492" affix_id="9400101" />
    <data hurdle_id="492" affix_id="9500101" />
    <data hurdle_id="492" affix_id="9500111" />
    <data hurdle_id="493" affix_id="9400101" />
    <data hurdle_id="493" affix_id="9500101" />
    <data hurdle_id="493" affix_id="9500111" />
    <data hurdle_id="494" affix_id="9400101" />
    <data hurdle_id="494" affix_id="9500101" />
    <data hurdle_id="494" affix_id="9500111" />
    <data hurdle_id="495" affix_id="9400101" />
    <data hurdle_id="495" affix_id="9500101" />
    <data hurdle_id="495" affix_id="9500111" />
    <data hurdle_id="496" affix_id="9400101" />
    <data hurdle_id="496" affix_id="9500101" />
    <data hurdle_id="496" affix_id="9500111" />
    <data hurdle_id="497" affix_id="9400101" />
    <data hurdle_id="497" affix_id="9500101" />
    <data hurdle_id="497" affix_id="9500111" />
    <data hurdle_id="498" affix_id="9400101" />
    <data hurdle_id="498" affix_id="9500101" />
    <data hurdle_id="498" affix_id="9500111" />
    <data hurdle_id="499" affix_id="9400101" />
    <data hurdle_id="499" affix_id="9500101" />
    <data hurdle_id="499" affix_id="9500111" />
    <data hurdle_id="500" affix_id="9400101" />
    <data hurdle_id="500" affix_id="9500101" />
    <data hurdle_id="500" affix_id="9500111" />
    <data hurdle_id="501" affix_id="9400102" />
    <data hurdle_id="501" affix_id="9500101" />
    <data hurdle_id="501" affix_id="9500104" />
    <data hurdle_id="502" affix_id="9400102" />
    <data hurdle_id="502" affix_id="9500101" />
    <data hurdle_id="502" affix_id="9500105" />
    <data hurdle_id="503" affix_id="9400102" />
    <data hurdle_id="503" affix_id="9500101" />
    <data hurdle_id="503" affix_id="9500106" />
    <data hurdle_id="504" affix_id="9400102" />
    <data hurdle_id="504" affix_id="9500101" />
    <data hurdle_id="504" affix_id="9500107" />
    <data hurdle_id="505" affix_id="9400102" />
    <data hurdle_id="505" affix_id="9500101" />
    <data hurdle_id="505" affix_id="9500108" />
    <data hurdle_id="506" affix_id="9400102" />
    <data hurdle_id="506" affix_id="9500101" />
    <data hurdle_id="506" affix_id="9500109" />
    <data hurdle_id="507" affix_id="9400102" />
    <data hurdle_id="507" affix_id="9500101" />
    <data hurdle_id="507" affix_id="9500111" />
    <data hurdle_id="508" affix_id="9400102" />
    <data hurdle_id="508" affix_id="9500101" />
    <data hurdle_id="508" affix_id="9500111" />
    <data hurdle_id="509" affix_id="9400102" />
    <data hurdle_id="509" affix_id="9500101" />
    <data hurdle_id="509" affix_id="9500111" />
    <data hurdle_id="510" affix_id="9400102" />
    <data hurdle_id="510" affix_id="9500101" />
    <data hurdle_id="510" affix_id="9500111" />
    <data hurdle_id="511" affix_id="9400102" />
    <data hurdle_id="511" affix_id="9500101" />
    <data hurdle_id="511" affix_id="9500111" />
    <data hurdle_id="512" affix_id="9400102" />
    <data hurdle_id="512" affix_id="9500101" />
    <data hurdle_id="512" affix_id="9500111" />
    <data hurdle_id="513" affix_id="9400102" />
    <data hurdle_id="513" affix_id="9500101" />
    <data hurdle_id="513" affix_id="9500111" />
    <data hurdle_id="514" affix_id="9400102" />
    <data hurdle_id="514" affix_id="9500101" />
    <data hurdle_id="514" affix_id="9500111" />
    <data hurdle_id="515" affix_id="9400102" />
    <data hurdle_id="515" affix_id="9500101" />
    <data hurdle_id="515" affix_id="9500111" />
    <data hurdle_id="516" affix_id="9400102" />
    <data hurdle_id="516" affix_id="9500101" />
    <data hurdle_id="516" affix_id="9500111" />
    <data hurdle_id="517" affix_id="9400102" />
    <data hurdle_id="517" affix_id="9500101" />
    <data hurdle_id="517" affix_id="9500111" />
    <data hurdle_id="518" affix_id="9400102" />
    <data hurdle_id="518" affix_id="9500101" />
    <data hurdle_id="518" affix_id="9500102" />
    <data hurdle_id="519" affix_id="9400102" />
    <data hurdle_id="519" affix_id="9500101" />
    <data hurdle_id="519" affix_id="9500110" />
    <data hurdle_id="520" affix_id="9400102" />
    <data hurdle_id="520" affix_id="9500101" />
    <data hurdle_id="520" affix_id="9500111" />
    <data hurdle_id="521" affix_id="9400102" />
    <data hurdle_id="521" affix_id="9500101" />
    <data hurdle_id="521" affix_id="9500111" />
    <data hurdle_id="522" affix_id="9400102" />
    <data hurdle_id="522" affix_id="9500101" />
    <data hurdle_id="522" affix_id="9500111" />
    <data hurdle_id="523" affix_id="9400102" />
    <data hurdle_id="523" affix_id="9500101" />
    <data hurdle_id="523" affix_id="9500111" />
    <data hurdle_id="524" affix_id="9400102" />
    <data hurdle_id="524" affix_id="9500101" />
    <data hurdle_id="524" affix_id="9500111" />
    <data hurdle_id="525" affix_id="9400102" />
    <data hurdle_id="525" affix_id="9500101" />
    <data hurdle_id="525" affix_id="9500111" />
    <data hurdle_id="526" affix_id="9400102" />
    <data hurdle_id="526" affix_id="9500101" />
    <data hurdle_id="526" affix_id="9500110" />
    <data hurdle_id="527" affix_id="9400102" />
    <data hurdle_id="527" affix_id="9500101" />
    <data hurdle_id="527" affix_id="9500110" />
    <data hurdle_id="528" affix_id="9400102" />
    <data hurdle_id="528" affix_id="9500101" />
    <data hurdle_id="528" affix_id="9500111" />
    <data hurdle_id="529" affix_id="9400102" />
    <data hurdle_id="529" affix_id="9500101" />
    <data hurdle_id="529" affix_id="9500111" />
    <data hurdle_id="530" affix_id="9400102" />
    <data hurdle_id="530" affix_id="9500101" />
    <data hurdle_id="530" affix_id="9500111" />
    <data hurdle_id="531" affix_id="9400102" />
    <data hurdle_id="531" affix_id="9500101" />
    <data hurdle_id="531" affix_id="9500111" />
    <data hurdle_id="532" affix_id="9400102" />
    <data hurdle_id="532" affix_id="9500101" />
    <data hurdle_id="532" affix_id="9500111" />
    <data hurdle_id="533" affix_id="9400102" />
    <data hurdle_id="533" affix_id="9500101" />
    <data hurdle_id="533" affix_id="9500111" />
    <data hurdle_id="534" affix_id="9400102" />
    <data hurdle_id="534" affix_id="9500101" />
    <data hurdle_id="534" affix_id="9500111" />
    <data hurdle_id="535" affix_id="9400102" />
    <data hurdle_id="535" affix_id="9500101" />
    <data hurdle_id="535" affix_id="9500111" />
    <data hurdle_id="536" affix_id="9400102" />
    <data hurdle_id="536" affix_id="9500101" />
    <data hurdle_id="536" affix_id="9500111" />
    <data hurdle_id="537" affix_id="9400102" />
    <data hurdle_id="537" affix_id="9500101" />
    <data hurdle_id="537" affix_id="9500111" />
    <data hurdle_id="538" affix_id="9400102" />
    <data hurdle_id="538" affix_id="9500101" />
    <data hurdle_id="538" affix_id="9500111" />
    <data hurdle_id="539" affix_id="9400102" />
    <data hurdle_id="539" affix_id="9500101" />
    <data hurdle_id="539" affix_id="9500111" />
    <data hurdle_id="540" affix_id="9400102" />
    <data hurdle_id="540" affix_id="9500101" />
    <data hurdle_id="540" affix_id="9500111" />
    <data hurdle_id="541" affix_id="9400102" />
    <data hurdle_id="541" affix_id="9500101" />
    <data hurdle_id="541" affix_id="9500111" />
    <data hurdle_id="542" affix_id="9400102" />
    <data hurdle_id="542" affix_id="9500101" />
    <data hurdle_id="542" affix_id="9500111" />
    <data hurdle_id="543" affix_id="9400102" />
    <data hurdle_id="543" affix_id="9500101" />
    <data hurdle_id="543" affix_id="9500111" />
    <data hurdle_id="544" affix_id="9400102" />
    <data hurdle_id="544" affix_id="9500101" />
    <data hurdle_id="544" affix_id="9500111" />
    <data hurdle_id="545" affix_id="9400102" />
    <data hurdle_id="545" affix_id="9500101" />
    <data hurdle_id="545" affix_id="9500111" />
    <data hurdle_id="546" affix_id="9400102" />
    <data hurdle_id="546" affix_id="9500101" />
    <data hurdle_id="546" affix_id="9500111" />
    <data hurdle_id="547" affix_id="9400102" />
    <data hurdle_id="547" affix_id="9500101" />
    <data hurdle_id="547" affix_id="9500111" />
    <data hurdle_id="548" affix_id="9400102" />
    <data hurdle_id="548" affix_id="9500101" />
    <data hurdle_id="548" affix_id="9500111" />
    <data hurdle_id="549" affix_id="9400102" />
    <data hurdle_id="549" affix_id="9500101" />
    <data hurdle_id="549" affix_id="9500111" />
    <data hurdle_id="550" affix_id="9400102" />
    <data hurdle_id="550" affix_id="9500101" />
    <data hurdle_id="550" affix_id="9500111" />
    <data hurdle_id="551" affix_id="9400102" />
    <data hurdle_id="551" affix_id="9500101" />
    <data hurdle_id="551" affix_id="9500111" />
    <data hurdle_id="552" affix_id="9400102" />
    <data hurdle_id="552" affix_id="9500101" />
    <data hurdle_id="552" affix_id="9500111" />
    <data hurdle_id="553" affix_id="9400102" />
    <data hurdle_id="553" affix_id="9500101" />
    <data hurdle_id="553" affix_id="9500111" />
    <data hurdle_id="554" affix_id="9400102" />
    <data hurdle_id="554" affix_id="9500101" />
    <data hurdle_id="554" affix_id="9500111" />
    <data hurdle_id="555" affix_id="9400102" />
    <data hurdle_id="555" affix_id="9500101" />
    <data hurdle_id="555" affix_id="9500111" />
    <data hurdle_id="556" affix_id="9400102" />
    <data hurdle_id="556" affix_id="9500101" />
    <data hurdle_id="556" affix_id="9500111" />
    <data hurdle_id="557" affix_id="9400102" />
    <data hurdle_id="557" affix_id="9500101" />
    <data hurdle_id="557" affix_id="9500111" />
    <data hurdle_id="558" affix_id="9400102" />
    <data hurdle_id="558" affix_id="9500101" />
    <data hurdle_id="558" affix_id="9500111" />
    <data hurdle_id="559" affix_id="9400102" />
    <data hurdle_id="559" affix_id="9500101" />
    <data hurdle_id="559" affix_id="9500111" />
    <data hurdle_id="560" affix_id="9400102" />
    <data hurdle_id="560" affix_id="9500101" />
    <data hurdle_id="560" affix_id="9500111" />
    <data hurdle_id="561" affix_id="9400102" />
    <data hurdle_id="561" affix_id="9500101" />
    <data hurdle_id="561" affix_id="9500111" />
    <data hurdle_id="562" affix_id="9400102" />
    <data hurdle_id="562" affix_id="9500101" />
    <data hurdle_id="562" affix_id="9500111" />
    <data hurdle_id="563" affix_id="9400102" />
    <data hurdle_id="563" affix_id="9500101" />
    <data hurdle_id="563" affix_id="9500111" />
    <data hurdle_id="564" affix_id="9400102" />
    <data hurdle_id="564" affix_id="9500101" />
    <data hurdle_id="564" affix_id="9500111" />
    <data hurdle_id="565" affix_id="9400102" />
    <data hurdle_id="565" affix_id="9500101" />
    <data hurdle_id="565" affix_id="9500111" />
    <data hurdle_id="566" affix_id="9400102" />
    <data hurdle_id="566" affix_id="9500101" />
    <data hurdle_id="566" affix_id="9500111" />
    <data hurdle_id="567" affix_id="9400102" />
    <data hurdle_id="567" affix_id="9500101" />
    <data hurdle_id="567" affix_id="9500111" />
    <data hurdle_id="568" affix_id="9400102" />
    <data hurdle_id="568" affix_id="9500101" />
    <data hurdle_id="568" affix_id="9500111" />
    <data hurdle_id="569" affix_id="9400102" />
    <data hurdle_id="569" affix_id="9500101" />
    <data hurdle_id="569" affix_id="9500111" />
    <data hurdle_id="570" affix_id="9400102" />
    <data hurdle_id="570" affix_id="9500101" />
    <data hurdle_id="570" affix_id="9500111" />
    <data hurdle_id="571" affix_id="9400102" />
    <data hurdle_id="571" affix_id="9500101" />
    <data hurdle_id="571" affix_id="9500111" />
    <data hurdle_id="572" affix_id="9400102" />
    <data hurdle_id="572" affix_id="9500101" />
    <data hurdle_id="572" affix_id="9500111" />
    <data hurdle_id="573" affix_id="9400102" />
    <data hurdle_id="573" affix_id="9500101" />
    <data hurdle_id="573" affix_id="9500111" />
    <data hurdle_id="574" affix_id="9400102" />
    <data hurdle_id="574" affix_id="9500101" />
    <data hurdle_id="574" affix_id="9500111" />
    <data hurdle_id="575" affix_id="9400102" />
    <data hurdle_id="575" affix_id="9500101" />
    <data hurdle_id="575" affix_id="9500111" />
    <data hurdle_id="576" affix_id="9400102" />
    <data hurdle_id="576" affix_id="9500101" />
    <data hurdle_id="576" affix_id="9500111" />
    <data hurdle_id="577" affix_id="9400102" />
    <data hurdle_id="577" affix_id="9500101" />
    <data hurdle_id="577" affix_id="9500111" />
    <data hurdle_id="578" affix_id="9400102" />
    <data hurdle_id="578" affix_id="9500101" />
    <data hurdle_id="578" affix_id="9500111" />
    <data hurdle_id="579" affix_id="9400102" />
    <data hurdle_id="579" affix_id="9500101" />
    <data hurdle_id="579" affix_id="9500111" />
    <data hurdle_id="580" affix_id="9400102" />
    <data hurdle_id="580" affix_id="9500101" />
    <data hurdle_id="580" affix_id="9500111" />
    <data hurdle_id="581" affix_id="9400102" />
    <data hurdle_id="581" affix_id="9500101" />
    <data hurdle_id="581" affix_id="9500111" />
    <data hurdle_id="582" affix_id="9400102" />
    <data hurdle_id="582" affix_id="9500101" />
    <data hurdle_id="582" affix_id="9500111" />
    <data hurdle_id="583" affix_id="9400102" />
    <data hurdle_id="583" affix_id="9500101" />
    <data hurdle_id="583" affix_id="9500111" />
    <data hurdle_id="584" affix_id="9400102" />
    <data hurdle_id="584" affix_id="9500101" />
    <data hurdle_id="584" affix_id="9500111" />
    <data hurdle_id="585" affix_id="9400102" />
    <data hurdle_id="585" affix_id="9500101" />
    <data hurdle_id="585" affix_id="9500111" />
    <data hurdle_id="586" affix_id="9400102" />
    <data hurdle_id="586" affix_id="9500101" />
    <data hurdle_id="586" affix_id="9500111" />
    <data hurdle_id="587" affix_id="9400102" />
    <data hurdle_id="587" affix_id="9500101" />
    <data hurdle_id="587" affix_id="9500111" />
    <data hurdle_id="588" affix_id="9400102" />
    <data hurdle_id="588" affix_id="9500101" />
    <data hurdle_id="588" affix_id="9500111" />
    <data hurdle_id="589" affix_id="9400102" />
    <data hurdle_id="589" affix_id="9500101" />
    <data hurdle_id="589" affix_id="9500111" />
    <data hurdle_id="590" affix_id="9400102" />
    <data hurdle_id="590" affix_id="9500101" />
    <data hurdle_id="590" affix_id="9500111" />
    <data hurdle_id="591" affix_id="9400102" />
    <data hurdle_id="591" affix_id="9500101" />
    <data hurdle_id="591" affix_id="9500111" />
    <data hurdle_id="592" affix_id="9400102" />
    <data hurdle_id="592" affix_id="9500101" />
    <data hurdle_id="592" affix_id="9500111" />
    <data hurdle_id="593" affix_id="9400102" />
    <data hurdle_id="593" affix_id="9500101" />
    <data hurdle_id="593" affix_id="9500111" />
    <data hurdle_id="594" affix_id="9400102" />
    <data hurdle_id="594" affix_id="9500101" />
    <data hurdle_id="594" affix_id="9500111" />
    <data hurdle_id="595" affix_id="9400102" />
    <data hurdle_id="595" affix_id="9500101" />
    <data hurdle_id="595" affix_id="9500111" />
    <data hurdle_id="596" affix_id="9400102" />
    <data hurdle_id="596" affix_id="9500101" />
    <data hurdle_id="596" affix_id="9500111" />
    <data hurdle_id="597" affix_id="9400102" />
    <data hurdle_id="597" affix_id="9500101" />
    <data hurdle_id="597" affix_id="9500111" />
    <data hurdle_id="598" affix_id="9400102" />
    <data hurdle_id="598" affix_id="9500101" />
    <data hurdle_id="598" affix_id="9500111" />
    <data hurdle_id="599" affix_id="9400102" />
    <data hurdle_id="599" affix_id="9500101" />
    <data hurdle_id="599" affix_id="9500111" />
    <data hurdle_id="600" affix_id="9400102" />
    <data hurdle_id="600" affix_id="9500101" />
    <data hurdle_id="600" affix_id="9500111" />
    <data hurdle_id="601" affix_id="9400106" />
    <data hurdle_id="601" affix_id="9500101" />
    <data hurdle_id="601" affix_id="9500104" />
    <data hurdle_id="602" affix_id="9400106" />
    <data hurdle_id="602" affix_id="9500101" />
    <data hurdle_id="602" affix_id="9500105" />
    <data hurdle_id="603" affix_id="9400106" />
    <data hurdle_id="603" affix_id="9500101" />
    <data hurdle_id="603" affix_id="9500106" />
    <data hurdle_id="604" affix_id="9400106" />
    <data hurdle_id="604" affix_id="9500101" />
    <data hurdle_id="604" affix_id="9500107" />
    <data hurdle_id="605" affix_id="9400106" />
    <data hurdle_id="605" affix_id="9500101" />
    <data hurdle_id="605" affix_id="9500108" />
    <data hurdle_id="606" affix_id="9400106" />
    <data hurdle_id="606" affix_id="9500101" />
    <data hurdle_id="606" affix_id="9500109" />
    <data hurdle_id="607" affix_id="9400106" />
    <data hurdle_id="607" affix_id="9500101" />
    <data hurdle_id="607" affix_id="9500111" />
    <data hurdle_id="608" affix_id="9400106" />
    <data hurdle_id="608" affix_id="9500101" />
    <data hurdle_id="608" affix_id="9500111" />
    <data hurdle_id="609" affix_id="9400106" />
    <data hurdle_id="609" affix_id="9500101" />
    <data hurdle_id="609" affix_id="9500111" />
    <data hurdle_id="610" affix_id="9400106" />
    <data hurdle_id="610" affix_id="9500101" />
    <data hurdle_id="610" affix_id="9500111" />
    <data hurdle_id="611" affix_id="9400106" />
    <data hurdle_id="611" affix_id="9500101" />
    <data hurdle_id="611" affix_id="9500111" />
    <data hurdle_id="612" affix_id="9400106" />
    <data hurdle_id="612" affix_id="9500101" />
    <data hurdle_id="612" affix_id="9500111" />
    <data hurdle_id="613" affix_id="9400106" />
    <data hurdle_id="613" affix_id="9500101" />
    <data hurdle_id="613" affix_id="9500111" />
    <data hurdle_id="614" affix_id="9400106" />
    <data hurdle_id="614" affix_id="9500101" />
    <data hurdle_id="614" affix_id="9500111" />
    <data hurdle_id="615" affix_id="9400106" />
    <data hurdle_id="615" affix_id="9500101" />
    <data hurdle_id="615" affix_id="9500111" />
    <data hurdle_id="616" affix_id="9400106" />
    <data hurdle_id="616" affix_id="9500101" />
    <data hurdle_id="616" affix_id="9500111" />
    <data hurdle_id="617" affix_id="9400106" />
    <data hurdle_id="617" affix_id="9500101" />
    <data hurdle_id="617" affix_id="9500111" />
    <data hurdle_id="618" affix_id="9400106" />
    <data hurdle_id="618" affix_id="9500101" />
    <data hurdle_id="618" affix_id="9500102" />
    <data hurdle_id="619" affix_id="9400106" />
    <data hurdle_id="619" affix_id="9500101" />
    <data hurdle_id="619" affix_id="9500110" />
    <data hurdle_id="620" affix_id="9400106" />
    <data hurdle_id="620" affix_id="9500101" />
    <data hurdle_id="620" affix_id="9500111" />
    <data hurdle_id="621" affix_id="9400106" />
    <data hurdle_id="621" affix_id="9500101" />
    <data hurdle_id="621" affix_id="9500111" />
    <data hurdle_id="622" affix_id="9400106" />
    <data hurdle_id="622" affix_id="9500101" />
    <data hurdle_id="622" affix_id="9500111" />
    <data hurdle_id="623" affix_id="9400106" />
    <data hurdle_id="623" affix_id="9500101" />
    <data hurdle_id="623" affix_id="9500111" />
    <data hurdle_id="624" affix_id="9400106" />
    <data hurdle_id="624" affix_id="9500101" />
    <data hurdle_id="624" affix_id="9500111" />
    <data hurdle_id="625" affix_id="9400106" />
    <data hurdle_id="625" affix_id="9500101" />
    <data hurdle_id="625" affix_id="9500111" />
    <data hurdle_id="626" affix_id="9400106" />
    <data hurdle_id="626" affix_id="9500101" />
    <data hurdle_id="626" affix_id="9500110" />
    <data hurdle_id="627" affix_id="9400106" />
    <data hurdle_id="627" affix_id="9500101" />
    <data hurdle_id="627" affix_id="9500110" />
    <data hurdle_id="628" affix_id="9400106" />
    <data hurdle_id="628" affix_id="9500101" />
    <data hurdle_id="628" affix_id="9500111" />
    <data hurdle_id="629" affix_id="9400106" />
    <data hurdle_id="629" affix_id="9500101" />
    <data hurdle_id="629" affix_id="9500111" />
    <data hurdle_id="630" affix_id="9400106" />
    <data hurdle_id="630" affix_id="9500101" />
    <data hurdle_id="630" affix_id="9500111" />
    <data hurdle_id="631" affix_id="9400106" />
    <data hurdle_id="631" affix_id="9500101" />
    <data hurdle_id="631" affix_id="9500111" />
    <data hurdle_id="632" affix_id="9400106" />
    <data hurdle_id="632" affix_id="9500101" />
    <data hurdle_id="632" affix_id="9500111" />
    <data hurdle_id="633" affix_id="9400106" />
    <data hurdle_id="633" affix_id="9500101" />
    <data hurdle_id="633" affix_id="9500111" />
    <data hurdle_id="634" affix_id="9400106" />
    <data hurdle_id="634" affix_id="9500101" />
    <data hurdle_id="634" affix_id="9500111" />
    <data hurdle_id="635" affix_id="9400106" />
    <data hurdle_id="635" affix_id="9500101" />
    <data hurdle_id="635" affix_id="9500111" />
    <data hurdle_id="636" affix_id="9400106" />
    <data hurdle_id="636" affix_id="9500101" />
    <data hurdle_id="636" affix_id="9500111" />
    <data hurdle_id="637" affix_id="9400106" />
    <data hurdle_id="637" affix_id="9500101" />
    <data hurdle_id="637" affix_id="9500111" />
    <data hurdle_id="638" affix_id="9400106" />
    <data hurdle_id="638" affix_id="9500101" />
    <data hurdle_id="638" affix_id="9500111" />
    <data hurdle_id="639" affix_id="9400106" />
    <data hurdle_id="639" affix_id="9500101" />
    <data hurdle_id="639" affix_id="9500111" />
    <data hurdle_id="640" affix_id="9400106" />
    <data hurdle_id="640" affix_id="9500101" />
    <data hurdle_id="640" affix_id="9500111" />
    <data hurdle_id="641" affix_id="9400106" />
    <data hurdle_id="641" affix_id="9500101" />
    <data hurdle_id="641" affix_id="9500111" />
    <data hurdle_id="642" affix_id="9400106" />
    <data hurdle_id="642" affix_id="9500101" />
    <data hurdle_id="642" affix_id="9500111" />
    <data hurdle_id="643" affix_id="9400106" />
    <data hurdle_id="643" affix_id="9500101" />
    <data hurdle_id="643" affix_id="9500111" />
    <data hurdle_id="644" affix_id="9400106" />
    <data hurdle_id="644" affix_id="9500101" />
    <data hurdle_id="644" affix_id="9500111" />
    <data hurdle_id="645" affix_id="9400106" />
    <data hurdle_id="645" affix_id="9500101" />
    <data hurdle_id="645" affix_id="9500111" />
    <data hurdle_id="646" affix_id="9400106" />
    <data hurdle_id="646" affix_id="9500101" />
    <data hurdle_id="646" affix_id="9500111" />
    <data hurdle_id="647" affix_id="9400106" />
    <data hurdle_id="647" affix_id="9500101" />
    <data hurdle_id="647" affix_id="9500111" />
    <data hurdle_id="648" affix_id="9400106" />
    <data hurdle_id="648" affix_id="9500101" />
    <data hurdle_id="648" affix_id="9500111" />
    <data hurdle_id="649" affix_id="9400106" />
    <data hurdle_id="649" affix_id="9500101" />
    <data hurdle_id="649" affix_id="9500111" />
    <data hurdle_id="650" affix_id="9400106" />
    <data hurdle_id="650" affix_id="9500101" />
    <data hurdle_id="650" affix_id="9500111" />
    <data hurdle_id="651" affix_id="9400106" />
    <data hurdle_id="651" affix_id="9500101" />
    <data hurdle_id="651" affix_id="9500111" />
    <data hurdle_id="652" affix_id="9400106" />
    <data hurdle_id="652" affix_id="9500101" />
    <data hurdle_id="652" affix_id="9500111" />
    <data hurdle_id="653" affix_id="9400106" />
    <data hurdle_id="653" affix_id="9500101" />
    <data hurdle_id="653" affix_id="9500111" />
    <data hurdle_id="654" affix_id="9400106" />
    <data hurdle_id="654" affix_id="9500101" />
    <data hurdle_id="654" affix_id="9500111" />
    <data hurdle_id="655" affix_id="9400106" />
    <data hurdle_id="655" affix_id="9500101" />
    <data hurdle_id="655" affix_id="9500111" />
    <data hurdle_id="656" affix_id="9400106" />
    <data hurdle_id="656" affix_id="9500101" />
    <data hurdle_id="656" affix_id="9500111" />
    <data hurdle_id="657" affix_id="9400106" />
    <data hurdle_id="657" affix_id="9500101" />
    <data hurdle_id="657" affix_id="9500111" />
    <data hurdle_id="658" affix_id="9400106" />
    <data hurdle_id="658" affix_id="9500101" />
    <data hurdle_id="658" affix_id="9500111" />
    <data hurdle_id="659" affix_id="9400106" />
    <data hurdle_id="659" affix_id="9500101" />
    <data hurdle_id="659" affix_id="9500111" />
    <data hurdle_id="660" affix_id="9400106" />
    <data hurdle_id="660" affix_id="9500101" />
    <data hurdle_id="660" affix_id="9500111" />
    <data hurdle_id="661" affix_id="9400106" />
    <data hurdle_id="661" affix_id="9500101" />
    <data hurdle_id="661" affix_id="9500111" />
    <data hurdle_id="662" affix_id="9400106" />
    <data hurdle_id="662" affix_id="9500101" />
    <data hurdle_id="662" affix_id="9500111" />
    <data hurdle_id="663" affix_id="9400106" />
    <data hurdle_id="663" affix_id="9500101" />
    <data hurdle_id="663" affix_id="9500111" />
    <data hurdle_id="664" affix_id="9400106" />
    <data hurdle_id="664" affix_id="9500101" />
    <data hurdle_id="664" affix_id="9500111" />
    <data hurdle_id="665" affix_id="9400106" />
    <data hurdle_id="665" affix_id="9500101" />
    <data hurdle_id="665" affix_id="9500111" />
    <data hurdle_id="666" affix_id="9400106" />
    <data hurdle_id="666" affix_id="9500101" />
    <data hurdle_id="666" affix_id="9500111" />
    <data hurdle_id="667" affix_id="9400106" />
    <data hurdle_id="667" affix_id="9500101" />
    <data hurdle_id="667" affix_id="9500111" />
    <data hurdle_id="668" affix_id="9400106" />
    <data hurdle_id="668" affix_id="9500101" />
    <data hurdle_id="668" affix_id="9500111" />
    <data hurdle_id="669" affix_id="9400106" />
    <data hurdle_id="669" affix_id="9500101" />
    <data hurdle_id="669" affix_id="9500111" />
    <data hurdle_id="670" affix_id="9400106" />
    <data hurdle_id="670" affix_id="9500101" />
    <data hurdle_id="670" affix_id="9500111" />
    <data hurdle_id="671" affix_id="9400106" />
    <data hurdle_id="671" affix_id="9500101" />
    <data hurdle_id="671" affix_id="9500111" />
    <data hurdle_id="672" affix_id="9400106" />
    <data hurdle_id="672" affix_id="9500101" />
    <data hurdle_id="672" affix_id="9500111" />
    <data hurdle_id="673" affix_id="9400106" />
    <data hurdle_id="673" affix_id="9500101" />
    <data hurdle_id="673" affix_id="9500111" />
    <data hurdle_id="674" affix_id="9400106" />
    <data hurdle_id="674" affix_id="9500101" />
    <data hurdle_id="674" affix_id="9500111" />
    <data hurdle_id="675" affix_id="9400106" />
    <data hurdle_id="675" affix_id="9500101" />
    <data hurdle_id="675" affix_id="9500111" />
    <data hurdle_id="676" affix_id="9400106" />
    <data hurdle_id="676" affix_id="9500101" />
    <data hurdle_id="676" affix_id="9500111" />
    <data hurdle_id="677" affix_id="9400106" />
    <data hurdle_id="677" affix_id="9500101" />
    <data hurdle_id="677" affix_id="9500111" />
    <data hurdle_id="678" affix_id="9400106" />
    <data hurdle_id="678" affix_id="9500101" />
    <data hurdle_id="678" affix_id="9500111" />
    <data hurdle_id="679" affix_id="9400106" />
    <data hurdle_id="679" affix_id="9500101" />
    <data hurdle_id="679" affix_id="9500111" />
    <data hurdle_id="680" affix_id="9400106" />
    <data hurdle_id="680" affix_id="9500101" />
    <data hurdle_id="680" affix_id="9500111" />
    <data hurdle_id="681" affix_id="9400106" />
    <data hurdle_id="681" affix_id="9500101" />
    <data hurdle_id="681" affix_id="9500111" />
    <data hurdle_id="682" affix_id="9400106" />
    <data hurdle_id="682" affix_id="9500101" />
    <data hurdle_id="682" affix_id="9500111" />
    <data hurdle_id="683" affix_id="9400106" />
    <data hurdle_id="683" affix_id="9500101" />
    <data hurdle_id="683" affix_id="9500111" />
    <data hurdle_id="684" affix_id="9400106" />
    <data hurdle_id="684" affix_id="9500101" />
    <data hurdle_id="684" affix_id="9500111" />
    <data hurdle_id="685" affix_id="9400106" />
    <data hurdle_id="685" affix_id="9500101" />
    <data hurdle_id="685" affix_id="9500111" />
    <data hurdle_id="686" affix_id="9400106" />
    <data hurdle_id="686" affix_id="9500101" />
    <data hurdle_id="686" affix_id="9500111" />
    <data hurdle_id="687" affix_id="9400106" />
    <data hurdle_id="687" affix_id="9500101" />
    <data hurdle_id="687" affix_id="9500111" />
    <data hurdle_id="688" affix_id="9400106" />
    <data hurdle_id="688" affix_id="9500101" />
    <data hurdle_id="688" affix_id="9500111" />
    <data hurdle_id="689" affix_id="9400106" />
    <data hurdle_id="689" affix_id="9500101" />
    <data hurdle_id="689" affix_id="9500111" />
    <data hurdle_id="690" affix_id="9400106" />
    <data hurdle_id="690" affix_id="9500101" />
    <data hurdle_id="690" affix_id="9500111" />
    <data hurdle_id="691" affix_id="9400106" />
    <data hurdle_id="691" affix_id="9500101" />
    <data hurdle_id="691" affix_id="9500111" />
    <data hurdle_id="692" affix_id="9400106" />
    <data hurdle_id="692" affix_id="9500101" />
    <data hurdle_id="692" affix_id="9500111" />
    <data hurdle_id="693" affix_id="9400106" />
    <data hurdle_id="693" affix_id="9500101" />
    <data hurdle_id="693" affix_id="9500111" />
    <data hurdle_id="694" affix_id="9400106" />
    <data hurdle_id="694" affix_id="9500101" />
    <data hurdle_id="694" affix_id="9500111" />
    <data hurdle_id="695" affix_id="9400106" />
    <data hurdle_id="695" affix_id="9500101" />
    <data hurdle_id="695" affix_id="9500111" />
    <data hurdle_id="696" affix_id="9400106" />
    <data hurdle_id="696" affix_id="9500101" />
    <data hurdle_id="696" affix_id="9500111" />
    <data hurdle_id="697" affix_id="9400106" />
    <data hurdle_id="697" affix_id="9500101" />
    <data hurdle_id="697" affix_id="9500111" />
    <data hurdle_id="698" affix_id="9400106" />
    <data hurdle_id="698" affix_id="9500101" />
    <data hurdle_id="698" affix_id="9500111" />
    <data hurdle_id="699" affix_id="9400106" />
    <data hurdle_id="699" affix_id="9500101" />
    <data hurdle_id="699" affix_id="9500111" />
    <data hurdle_id="700" affix_id="9400106" />
    <data hurdle_id="700" affix_id="9500101" />
    <data hurdle_id="700" affix_id="9500111" />
    <data hurdle_id="10101" affix_id="9400105" />
    <data hurdle_id="10101" affix_id="9500101" />
    <data hurdle_id="10101" affix_id="9500111" />
    <data hurdle_id="10102" affix_id="9400105" />
    <data hurdle_id="10102" affix_id="9500101" />
    <data hurdle_id="10102" affix_id="9500111" />
    <data hurdle_id="10103" affix_id="9400105" />
    <data hurdle_id="10103" affix_id="9500101" />
    <data hurdle_id="10103" affix_id="9500111" />
    <data hurdle_id="10104" affix_id="9400105" />
    <data hurdle_id="10104" affix_id="9500101" />
    <data hurdle_id="10104" affix_id="9500111" />
    <data hurdle_id="10105" affix_id="9400105" />
    <data hurdle_id="10105" affix_id="9500101" />
    <data hurdle_id="10105" affix_id="9500111" />
    <data hurdle_id="10106" affix_id="9400105" />
    <data hurdle_id="10106" affix_id="9500101" />
    <data hurdle_id="10106" affix_id="9500111" />
    <data hurdle_id="10107" affix_id="9400105" />
    <data hurdle_id="10107" affix_id="9500101" />
    <data hurdle_id="10107" affix_id="9500111" />
    <data hurdle_id="10108" affix_id="9400105" />
    <data hurdle_id="10108" affix_id="9500101" />
    <data hurdle_id="10108" affix_id="9500111" />
    <data hurdle_id="10109" affix_id="9400105" />
    <data hurdle_id="10109" affix_id="9500101" />
    <data hurdle_id="10109" affix_id="9500111" />
    <data hurdle_id="10110" affix_id="9400105" />
    <data hurdle_id="10110" affix_id="9500101" />
    <data hurdle_id="10110" affix_id="9500111" />
    <data hurdle_id="10111" affix_id="9400105" />
    <data hurdle_id="10111" affix_id="9500101" />
    <data hurdle_id="10111" affix_id="9500111" />
    <data hurdle_id="10112" affix_id="9400105" />
    <data hurdle_id="10112" affix_id="9500101" />
    <data hurdle_id="10112" affix_id="9500111" />
    <data hurdle_id="10113" affix_id="9400105" />
    <data hurdle_id="10113" affix_id="9500101" />
    <data hurdle_id="10113" affix_id="9500111" />
    <data hurdle_id="10114" affix_id="9400105" />
    <data hurdle_id="10114" affix_id="9500101" />
    <data hurdle_id="10114" affix_id="9500111" />
    <data hurdle_id="10115" affix_id="9400105" />
    <data hurdle_id="10115" affix_id="9500101" />
    <data hurdle_id="10115" affix_id="9500111" />
    <data hurdle_id="10116" affix_id="9400105" />
    <data hurdle_id="10116" affix_id="9500101" />
    <data hurdle_id="10116" affix_id="9500111" />
    <data hurdle_id="10117" affix_id="9400105" />
    <data hurdle_id="10117" affix_id="9500101" />
    <data hurdle_id="10117" affix_id="9500111" />
    <data hurdle_id="10118" affix_id="9400105" />
    <data hurdle_id="10118" affix_id="9500101" />
    <data hurdle_id="10118" affix_id="9500111" />
    <data hurdle_id="10119" affix_id="9400105" />
    <data hurdle_id="10119" affix_id="9500101" />
    <data hurdle_id="10119" affix_id="9500111" />
    <data hurdle_id="10120" affix_id="9400105" />
    <data hurdle_id="10120" affix_id="9500101" />
    <data hurdle_id="10120" affix_id="9500111" />
    <data hurdle_id="10121" affix_id="9400105" />
    <data hurdle_id="10121" affix_id="9500101" />
    <data hurdle_id="10121" affix_id="9500111" />
    <data hurdle_id="10122" affix_id="9400105" />
    <data hurdle_id="10122" affix_id="9500101" />
    <data hurdle_id="10122" affix_id="9500111" />
    <data hurdle_id="10123" affix_id="9400105" />
    <data hurdle_id="10123" affix_id="9500101" />
    <data hurdle_id="10123" affix_id="9500111" />
    <data hurdle_id="10124" affix_id="9400105" />
    <data hurdle_id="10124" affix_id="9500101" />
    <data hurdle_id="10124" affix_id="9500111" />
    <data hurdle_id="10125" affix_id="9400105" />
    <data hurdle_id="10125" affix_id="9500101" />
    <data hurdle_id="10125" affix_id="9500111" />
    <data hurdle_id="10126" affix_id="9400105" />
    <data hurdle_id="10126" affix_id="9500101" />
    <data hurdle_id="10126" affix_id="9500111" />
    <data hurdle_id="10127" affix_id="9400105" />
    <data hurdle_id="10127" affix_id="9500101" />
    <data hurdle_id="10127" affix_id="9500111" />
    <data hurdle_id="10128" affix_id="9400105" />
    <data hurdle_id="10128" affix_id="9500101" />
    <data hurdle_id="10128" affix_id="9500111" />
    <data hurdle_id="10129" affix_id="9400105" />
    <data hurdle_id="10129" affix_id="9500101" />
    <data hurdle_id="10129" affix_id="9500111" />
    <data hurdle_id="10130" affix_id="9400105" />
    <data hurdle_id="10130" affix_id="9500101" />
    <data hurdle_id="10130" affix_id="9500111" />
    <data hurdle_id="10131" affix_id="9400105" />
    <data hurdle_id="10131" affix_id="9500101" />
    <data hurdle_id="10131" affix_id="9500111" />
    <data hurdle_id="10132" affix_id="9400105" />
    <data hurdle_id="10132" affix_id="9500101" />
    <data hurdle_id="10132" affix_id="9500111" />
    <data hurdle_id="10133" affix_id="9400105" />
    <data hurdle_id="10133" affix_id="9500101" />
    <data hurdle_id="10133" affix_id="9500111" />
    <data hurdle_id="10134" affix_id="9400105" />
    <data hurdle_id="10134" affix_id="9500101" />
    <data hurdle_id="10134" affix_id="9500111" />
    <data hurdle_id="10135" affix_id="9400105" />
    <data hurdle_id="10135" affix_id="9500101" />
    <data hurdle_id="10135" affix_id="9500111" />
    <data hurdle_id="10136" affix_id="9400105" />
    <data hurdle_id="10136" affix_id="9500101" />
    <data hurdle_id="10136" affix_id="9500111" />
    <data hurdle_id="10137" affix_id="9400105" />
    <data hurdle_id="10137" affix_id="9500101" />
    <data hurdle_id="10137" affix_id="9500111" />
    <data hurdle_id="10138" affix_id="9400105" />
    <data hurdle_id="10138" affix_id="9500101" />
    <data hurdle_id="10138" affix_id="9500111" />
    <data hurdle_id="10139" affix_id="9400105" />
    <data hurdle_id="10139" affix_id="9500101" />
    <data hurdle_id="10139" affix_id="9500111" />
    <data hurdle_id="10140" affix_id="9400105" />
    <data hurdle_id="10140" affix_id="9500101" />
    <data hurdle_id="10140" affix_id="9500111" />
    <data hurdle_id="10141" affix_id="9400105" />
    <data hurdle_id="10141" affix_id="9500101" />
    <data hurdle_id="10141" affix_id="9500111" />
    <data hurdle_id="10142" affix_id="9400105" />
    <data hurdle_id="10142" affix_id="9500101" />
    <data hurdle_id="10142" affix_id="9500111" />
    <data hurdle_id="10143" affix_id="9400105" />
    <data hurdle_id="10143" affix_id="9500101" />
    <data hurdle_id="10143" affix_id="9500111" />
    <data hurdle_id="10144" affix_id="9400105" />
    <data hurdle_id="10144" affix_id="9500101" />
    <data hurdle_id="10144" affix_id="9500111" />
    <data hurdle_id="10145" affix_id="9400105" />
    <data hurdle_id="10145" affix_id="9500101" />
    <data hurdle_id="10145" affix_id="9500111" />
    <data hurdle_id="10146" affix_id="9400105" />
    <data hurdle_id="10146" affix_id="9500101" />
    <data hurdle_id="10146" affix_id="9500111" />
    <data hurdle_id="10147" affix_id="9400105" />
    <data hurdle_id="10147" affix_id="9500101" />
    <data hurdle_id="10147" affix_id="9500111" />
    <data hurdle_id="10148" affix_id="9400105" />
    <data hurdle_id="10148" affix_id="9500101" />
    <data hurdle_id="10148" affix_id="9500111" />
    <data hurdle_id="10149" affix_id="9400105" />
    <data hurdle_id="10149" affix_id="9500101" />
    <data hurdle_id="10149" affix_id="9500111" />
    <data hurdle_id="10150" affix_id="9400105" />
    <data hurdle_id="10150" affix_id="9500101" />
    <data hurdle_id="10150" affix_id="9500111" />
    <data hurdle_id="20101" affix_id="9400103" />
    <data hurdle_id="20101" affix_id="9500101" />
    <data hurdle_id="20101" affix_id="9500111" />
    <data hurdle_id="20102" affix_id="9400103" />
    <data hurdle_id="20102" affix_id="9500101" />
    <data hurdle_id="20102" affix_id="9500111" />
    <data hurdle_id="20103" affix_id="9400103" />
    <data hurdle_id="20103" affix_id="9500101" />
    <data hurdle_id="20103" affix_id="9500111" />
    <data hurdle_id="20104" affix_id="9400103" />
    <data hurdle_id="20104" affix_id="9500101" />
    <data hurdle_id="20104" affix_id="9500111" />
    <data hurdle_id="20105" affix_id="9400103" />
    <data hurdle_id="20105" affix_id="9500101" />
    <data hurdle_id="20105" affix_id="9500111" />
    <data hurdle_id="20106" affix_id="9400103" />
    <data hurdle_id="20106" affix_id="9500101" />
    <data hurdle_id="20106" affix_id="9500111" />
    <data hurdle_id="20107" affix_id="9400103" />
    <data hurdle_id="20107" affix_id="9500101" />
    <data hurdle_id="20107" affix_id="9500111" />
    <data hurdle_id="20108" affix_id="9400103" />
    <data hurdle_id="20108" affix_id="9500101" />
    <data hurdle_id="20108" affix_id="9500111" />
    <data hurdle_id="20109" affix_id="9400103" />
    <data hurdle_id="20109" affix_id="9500101" />
    <data hurdle_id="20109" affix_id="9500111" />
    <data hurdle_id="20110" affix_id="9400103" />
    <data hurdle_id="20110" affix_id="9500101" />
    <data hurdle_id="20110" affix_id="9500111" />
    <data hurdle_id="20111" affix_id="9400103" />
    <data hurdle_id="20111" affix_id="9500101" />
    <data hurdle_id="20111" affix_id="9500111" />
    <data hurdle_id="20112" affix_id="9400103" />
    <data hurdle_id="20112" affix_id="9500101" />
    <data hurdle_id="20112" affix_id="9500111" />
    <data hurdle_id="20113" affix_id="9400103" />
    <data hurdle_id="20113" affix_id="9500101" />
    <data hurdle_id="20113" affix_id="9500111" />
    <data hurdle_id="20114" affix_id="9400103" />
    <data hurdle_id="20114" affix_id="9500101" />
    <data hurdle_id="20114" affix_id="9500111" />
    <data hurdle_id="20115" affix_id="9400103" />
    <data hurdle_id="20115" affix_id="9500101" />
    <data hurdle_id="20115" affix_id="9500111" />
    <data hurdle_id="20116" affix_id="9400103" />
    <data hurdle_id="20116" affix_id="9500101" />
    <data hurdle_id="20116" affix_id="9500111" />
    <data hurdle_id="20117" affix_id="9400103" />
    <data hurdle_id="20117" affix_id="9500101" />
    <data hurdle_id="20117" affix_id="9500111" />
    <data hurdle_id="20118" affix_id="9400103" />
    <data hurdle_id="20118" affix_id="9500101" />
    <data hurdle_id="20118" affix_id="9500111" />
    <data hurdle_id="20119" affix_id="9400103" />
    <data hurdle_id="20119" affix_id="9500101" />
    <data hurdle_id="20119" affix_id="9500111" />
    <data hurdle_id="20120" affix_id="9400103" />
    <data hurdle_id="20120" affix_id="9500101" />
    <data hurdle_id="20120" affix_id="9500111" />
    <data hurdle_id="20121" affix_id="9400103" />
    <data hurdle_id="20121" affix_id="9500101" />
    <data hurdle_id="20121" affix_id="9500111" />
    <data hurdle_id="20122" affix_id="9400103" />
    <data hurdle_id="20122" affix_id="9500101" />
    <data hurdle_id="20122" affix_id="9500111" />
    <data hurdle_id="20123" affix_id="9400103" />
    <data hurdle_id="20123" affix_id="9500101" />
    <data hurdle_id="20123" affix_id="9500111" />
    <data hurdle_id="20124" affix_id="9400103" />
    <data hurdle_id="20124" affix_id="9500101" />
    <data hurdle_id="20124" affix_id="9500111" />
    <data hurdle_id="20125" affix_id="9400103" />
    <data hurdle_id="20125" affix_id="9500101" />
    <data hurdle_id="20125" affix_id="9500111" />
    <data hurdle_id="20126" affix_id="9400103" />
    <data hurdle_id="20126" affix_id="9500101" />
    <data hurdle_id="20126" affix_id="9500111" />
    <data hurdle_id="20127" affix_id="9400103" />
    <data hurdle_id="20127" affix_id="9500101" />
    <data hurdle_id="20127" affix_id="9500111" />
    <data hurdle_id="20128" affix_id="9400103" />
    <data hurdle_id="20128" affix_id="9500101" />
    <data hurdle_id="20128" affix_id="9500111" />
    <data hurdle_id="20129" affix_id="9400103" />
    <data hurdle_id="20129" affix_id="9500101" />
    <data hurdle_id="20129" affix_id="9500111" />
    <data hurdle_id="20130" affix_id="9400103" />
    <data hurdle_id="20130" affix_id="9500101" />
    <data hurdle_id="20130" affix_id="9500111" />
    <data hurdle_id="20131" affix_id="9400103" />
    <data hurdle_id="20131" affix_id="9500101" />
    <data hurdle_id="20131" affix_id="9500111" />
    <data hurdle_id="20132" affix_id="9400103" />
    <data hurdle_id="20132" affix_id="9500101" />
    <data hurdle_id="20132" affix_id="9500111" />
    <data hurdle_id="20133" affix_id="9400103" />
    <data hurdle_id="20133" affix_id="9500101" />
    <data hurdle_id="20133" affix_id="9500111" />
    <data hurdle_id="20134" affix_id="9400103" />
    <data hurdle_id="20134" affix_id="9500101" />
    <data hurdle_id="20134" affix_id="9500111" />
    <data hurdle_id="20135" affix_id="9400103" />
    <data hurdle_id="20135" affix_id="9500101" />
    <data hurdle_id="20135" affix_id="9500111" />
    <data hurdle_id="20136" affix_id="9400103" />
    <data hurdle_id="20136" affix_id="9500101" />
    <data hurdle_id="20136" affix_id="9500111" />
    <data hurdle_id="20137" affix_id="9400103" />
    <data hurdle_id="20137" affix_id="9500101" />
    <data hurdle_id="20137" affix_id="9500111" />
    <data hurdle_id="20138" affix_id="9400103" />
    <data hurdle_id="20138" affix_id="9500101" />
    <data hurdle_id="20138" affix_id="9500111" />
    <data hurdle_id="20139" affix_id="9400103" />
    <data hurdle_id="20139" affix_id="9500101" />
    <data hurdle_id="20139" affix_id="9500111" />
    <data hurdle_id="20140" affix_id="9400103" />
    <data hurdle_id="20140" affix_id="9500101" />
    <data hurdle_id="20140" affix_id="9500111" />
    <data hurdle_id="20141" affix_id="9400103" />
    <data hurdle_id="20141" affix_id="9500101" />
    <data hurdle_id="20141" affix_id="9500111" />
    <data hurdle_id="20142" affix_id="9400103" />
    <data hurdle_id="20142" affix_id="9500101" />
    <data hurdle_id="20142" affix_id="9500111" />
    <data hurdle_id="20143" affix_id="9400103" />
    <data hurdle_id="20143" affix_id="9500101" />
    <data hurdle_id="20143" affix_id="9500111" />
    <data hurdle_id="20144" affix_id="9400103" />
    <data hurdle_id="20144" affix_id="9500101" />
    <data hurdle_id="20144" affix_id="9500111" />
    <data hurdle_id="20145" affix_id="9400103" />
    <data hurdle_id="20145" affix_id="9500101" />
    <data hurdle_id="20145" affix_id="9500111" />
    <data hurdle_id="20146" affix_id="9400103" />
    <data hurdle_id="20146" affix_id="9500101" />
    <data hurdle_id="20146" affix_id="9500111" />
    <data hurdle_id="20147" affix_id="9400103" />
    <data hurdle_id="20147" affix_id="9500101" />
    <data hurdle_id="20147" affix_id="9500111" />
    <data hurdle_id="20148" affix_id="9400103" />
    <data hurdle_id="20148" affix_id="9500101" />
    <data hurdle_id="20148" affix_id="9500111" />
    <data hurdle_id="20149" affix_id="9400103" />
    <data hurdle_id="20149" affix_id="9500101" />
    <data hurdle_id="20149" affix_id="9500111" />
    <data hurdle_id="20150" affix_id="9400103" />
    <data hurdle_id="20150" affix_id="9500101" />
    <data hurdle_id="20150" affix_id="9500111" />
    <data hurdle_id="30101" affix_id="9400104" />
    <data hurdle_id="30101" affix_id="9500101" />
    <data hurdle_id="30101" affix_id="9500111" />
    <data hurdle_id="30102" affix_id="9400104" />
    <data hurdle_id="30102" affix_id="9500101" />
    <data hurdle_id="30102" affix_id="9500111" />
    <data hurdle_id="30103" affix_id="9400104" />
    <data hurdle_id="30103" affix_id="9500101" />
    <data hurdle_id="30103" affix_id="9500111" />
    <data hurdle_id="30104" affix_id="9400104" />
    <data hurdle_id="30104" affix_id="9500101" />
    <data hurdle_id="30104" affix_id="9500111" />
    <data hurdle_id="30105" affix_id="9400104" />
    <data hurdle_id="30105" affix_id="9500101" />
    <data hurdle_id="30105" affix_id="9500111" />
    <data hurdle_id="30106" affix_id="9400104" />
    <data hurdle_id="30106" affix_id="9500101" />
    <data hurdle_id="30106" affix_id="9500111" />
    <data hurdle_id="30107" affix_id="9400104" />
    <data hurdle_id="30107" affix_id="9500101" />
    <data hurdle_id="30107" affix_id="9500111" />
    <data hurdle_id="30108" affix_id="9400104" />
    <data hurdle_id="30108" affix_id="9500101" />
    <data hurdle_id="30108" affix_id="9500111" />
    <data hurdle_id="30109" affix_id="9400104" />
    <data hurdle_id="30109" affix_id="9500101" />
    <data hurdle_id="30109" affix_id="9500111" />
    <data hurdle_id="30110" affix_id="9400104" />
    <data hurdle_id="30110" affix_id="9500101" />
    <data hurdle_id="30110" affix_id="9500111" />
    <data hurdle_id="30111" affix_id="9400104" />
    <data hurdle_id="30111" affix_id="9500101" />
    <data hurdle_id="30111" affix_id="9500111" />
    <data hurdle_id="30112" affix_id="9400104" />
    <data hurdle_id="30112" affix_id="9500101" />
    <data hurdle_id="30112" affix_id="9500111" />
    <data hurdle_id="30113" affix_id="9400104" />
    <data hurdle_id="30113" affix_id="9500101" />
    <data hurdle_id="30113" affix_id="9500111" />
    <data hurdle_id="30114" affix_id="9400104" />
    <data hurdle_id="30114" affix_id="9500101" />
    <data hurdle_id="30114" affix_id="9500111" />
    <data hurdle_id="30115" affix_id="9400104" />
    <data hurdle_id="30115" affix_id="9500101" />
    <data hurdle_id="30115" affix_id="9500111" />
    <data hurdle_id="30116" affix_id="9400104" />
    <data hurdle_id="30116" affix_id="9500101" />
    <data hurdle_id="30116" affix_id="9500111" />
    <data hurdle_id="30117" affix_id="9400104" />
    <data hurdle_id="30117" affix_id="9500101" />
    <data hurdle_id="30117" affix_id="9500111" />
    <data hurdle_id="30118" affix_id="9400104" />
    <data hurdle_id="30118" affix_id="9500101" />
    <data hurdle_id="30118" affix_id="9500111" />
    <data hurdle_id="30119" affix_id="9400104" />
    <data hurdle_id="30119" affix_id="9500101" />
    <data hurdle_id="30119" affix_id="9500111" />
    <data hurdle_id="30120" affix_id="9400104" />
    <data hurdle_id="30120" affix_id="9500101" />
    <data hurdle_id="30120" affix_id="9500111" />
    <data hurdle_id="30121" affix_id="9400104" />
    <data hurdle_id="30121" affix_id="9500101" />
    <data hurdle_id="30121" affix_id="9500111" />
    <data hurdle_id="30122" affix_id="9400104" />
    <data hurdle_id="30122" affix_id="9500101" />
    <data hurdle_id="30122" affix_id="9500111" />
    <data hurdle_id="30123" affix_id="9400104" />
    <data hurdle_id="30123" affix_id="9500101" />
    <data hurdle_id="30123" affix_id="9500111" />
    <data hurdle_id="30124" affix_id="9400104" />
    <data hurdle_id="30124" affix_id="9500101" />
    <data hurdle_id="30124" affix_id="9500111" />
    <data hurdle_id="30125" affix_id="9400104" />
    <data hurdle_id="30125" affix_id="9500101" />
    <data hurdle_id="30125" affix_id="9500111" />
    <data hurdle_id="30126" affix_id="9400104" />
    <data hurdle_id="30126" affix_id="9500101" />
    <data hurdle_id="30126" affix_id="9500111" />
    <data hurdle_id="30127" affix_id="9400104" />
    <data hurdle_id="30127" affix_id="9500101" />
    <data hurdle_id="30127" affix_id="9500111" />
    <data hurdle_id="30128" affix_id="9400104" />
    <data hurdle_id="30128" affix_id="9500101" />
    <data hurdle_id="30128" affix_id="9500111" />
    <data hurdle_id="30129" affix_id="9400104" />
    <data hurdle_id="30129" affix_id="9500101" />
    <data hurdle_id="30129" affix_id="9500111" />
    <data hurdle_id="30130" affix_id="9400104" />
    <data hurdle_id="30130" affix_id="9500101" />
    <data hurdle_id="30130" affix_id="9500111" />
    <data hurdle_id="30131" affix_id="9400104" />
    <data hurdle_id="30131" affix_id="9500101" />
    <data hurdle_id="30131" affix_id="9500111" />
    <data hurdle_id="30132" affix_id="9400104" />
    <data hurdle_id="30132" affix_id="9500101" />
    <data hurdle_id="30132" affix_id="9500111" />
    <data hurdle_id="30133" affix_id="9400104" />
    <data hurdle_id="30133" affix_id="9500101" />
    <data hurdle_id="30133" affix_id="9500111" />
    <data hurdle_id="30134" affix_id="9400104" />
    <data hurdle_id="30134" affix_id="9500101" />
    <data hurdle_id="30134" affix_id="9500111" />
    <data hurdle_id="30135" affix_id="9400104" />
    <data hurdle_id="30135" affix_id="9500101" />
    <data hurdle_id="30135" affix_id="9500111" />
    <data hurdle_id="30136" affix_id="9400104" />
    <data hurdle_id="30136" affix_id="9500101" />
    <data hurdle_id="30136" affix_id="9500111" />
    <data hurdle_id="30137" affix_id="9400104" />
    <data hurdle_id="30137" affix_id="9500101" />
    <data hurdle_id="30137" affix_id="9500111" />
    <data hurdle_id="30138" affix_id="9400104" />
    <data hurdle_id="30138" affix_id="9500101" />
    <data hurdle_id="30138" affix_id="9500111" />
    <data hurdle_id="30139" affix_id="9400104" />
    <data hurdle_id="30139" affix_id="9500101" />
    <data hurdle_id="30139" affix_id="9500111" />
    <data hurdle_id="30140" affix_id="9400104" />
    <data hurdle_id="30140" affix_id="9500101" />
    <data hurdle_id="30140" affix_id="9500111" />
    <data hurdle_id="30141" affix_id="9400104" />
    <data hurdle_id="30141" affix_id="9500101" />
    <data hurdle_id="30141" affix_id="9500111" />
    <data hurdle_id="30142" affix_id="9400104" />
    <data hurdle_id="30142" affix_id="9500101" />
    <data hurdle_id="30142" affix_id="9500111" />
    <data hurdle_id="30143" affix_id="9400104" />
    <data hurdle_id="30143" affix_id="9500101" />
    <data hurdle_id="30143" affix_id="9500111" />
    <data hurdle_id="30144" affix_id="9400104" />
    <data hurdle_id="30144" affix_id="9500101" />
    <data hurdle_id="30144" affix_id="9500111" />
    <data hurdle_id="30145" affix_id="9400104" />
    <data hurdle_id="30145" affix_id="9500101" />
    <data hurdle_id="30145" affix_id="9500111" />
    <data hurdle_id="30146" affix_id="9400104" />
    <data hurdle_id="30146" affix_id="9500101" />
    <data hurdle_id="30146" affix_id="9500111" />
    <data hurdle_id="30147" affix_id="9400104" />
    <data hurdle_id="30147" affix_id="9500101" />
    <data hurdle_id="30147" affix_id="9500111" />
    <data hurdle_id="30148" affix_id="9400104" />
    <data hurdle_id="30148" affix_id="9500101" />
    <data hurdle_id="30148" affix_id="9500111" />
    <data hurdle_id="30149" affix_id="9400104" />
    <data hurdle_id="30149" affix_id="9500101" />
    <data hurdle_id="30149" affix_id="9500111" />
    <data hurdle_id="30150" affix_id="9400104" />
    <data hurdle_id="30150" affix_id="9500101" />
    <data hurdle_id="30150" affix_id="9500111" />
    <data hurdle_id="40101" affix_id="9400101" />
    <data hurdle_id="40101" affix_id="9500101" />
    <data hurdle_id="40101" affix_id="9500111" />
    <data hurdle_id="40102" affix_id="9400101" />
    <data hurdle_id="40102" affix_id="9500101" />
    <data hurdle_id="40102" affix_id="9500111" />
    <data hurdle_id="40103" affix_id="9400101" />
    <data hurdle_id="40103" affix_id="9500101" />
    <data hurdle_id="40103" affix_id="9500111" />
    <data hurdle_id="40104" affix_id="9400101" />
    <data hurdle_id="40104" affix_id="9500101" />
    <data hurdle_id="40104" affix_id="9500111" />
    <data hurdle_id="40105" affix_id="9400101" />
    <data hurdle_id="40105" affix_id="9500101" />
    <data hurdle_id="40105" affix_id="9500111" />
    <data hurdle_id="40106" affix_id="9400101" />
    <data hurdle_id="40106" affix_id="9500101" />
    <data hurdle_id="40106" affix_id="9500111" />
    <data hurdle_id="40107" affix_id="9400101" />
    <data hurdle_id="40107" affix_id="9500101" />
    <data hurdle_id="40107" affix_id="9500111" />
    <data hurdle_id="40108" affix_id="9400101" />
    <data hurdle_id="40108" affix_id="9500101" />
    <data hurdle_id="40108" affix_id="9500111" />
    <data hurdle_id="40109" affix_id="9400101" />
    <data hurdle_id="40109" affix_id="9500101" />
    <data hurdle_id="40109" affix_id="9500111" />
    <data hurdle_id="40110" affix_id="9400101" />
    <data hurdle_id="40110" affix_id="9500101" />
    <data hurdle_id="40110" affix_id="9500111" />
    <data hurdle_id="40111" affix_id="9400101" />
    <data hurdle_id="40111" affix_id="9500101" />
    <data hurdle_id="40111" affix_id="9500111" />
    <data hurdle_id="40112" affix_id="9400101" />
    <data hurdle_id="40112" affix_id="9500101" />
    <data hurdle_id="40112" affix_id="9500111" />
    <data hurdle_id="40113" affix_id="9400101" />
    <data hurdle_id="40113" affix_id="9500101" />
    <data hurdle_id="40113" affix_id="9500111" />
    <data hurdle_id="40114" affix_id="9400101" />
    <data hurdle_id="40114" affix_id="9500101" />
    <data hurdle_id="40114" affix_id="9500111" />
    <data hurdle_id="40115" affix_id="9400101" />
    <data hurdle_id="40115" affix_id="9500101" />
    <data hurdle_id="40115" affix_id="9500111" />
    <data hurdle_id="40116" affix_id="9400101" />
    <data hurdle_id="40116" affix_id="9500101" />
    <data hurdle_id="40116" affix_id="9500111" />
    <data hurdle_id="40117" affix_id="9400101" />
    <data hurdle_id="40117" affix_id="9500101" />
    <data hurdle_id="40117" affix_id="9500111" />
    <data hurdle_id="40118" affix_id="9400101" />
    <data hurdle_id="40118" affix_id="9500101" />
    <data hurdle_id="40118" affix_id="9500111" />
    <data hurdle_id="40119" affix_id="9400101" />
    <data hurdle_id="40119" affix_id="9500101" />
    <data hurdle_id="40119" affix_id="9500111" />
    <data hurdle_id="40120" affix_id="9400101" />
    <data hurdle_id="40120" affix_id="9500101" />
    <data hurdle_id="40120" affix_id="9500111" />
    <data hurdle_id="40121" affix_id="9400101" />
    <data hurdle_id="40121" affix_id="9500101" />
    <data hurdle_id="40121" affix_id="9500111" />
    <data hurdle_id="40122" affix_id="9400101" />
    <data hurdle_id="40122" affix_id="9500101" />
    <data hurdle_id="40122" affix_id="9500111" />
    <data hurdle_id="40123" affix_id="9400101" />
    <data hurdle_id="40123" affix_id="9500101" />
    <data hurdle_id="40123" affix_id="9500111" />
    <data hurdle_id="40124" affix_id="9400101" />
    <data hurdle_id="40124" affix_id="9500101" />
    <data hurdle_id="40124" affix_id="9500111" />
    <data hurdle_id="40125" affix_id="9400101" />
    <data hurdle_id="40125" affix_id="9500101" />
    <data hurdle_id="40125" affix_id="9500111" />
    <data hurdle_id="40126" affix_id="9400101" />
    <data hurdle_id="40126" affix_id="9500101" />
    <data hurdle_id="40126" affix_id="9500111" />
    <data hurdle_id="40127" affix_id="9400101" />
    <data hurdle_id="40127" affix_id="9500101" />
    <data hurdle_id="40127" affix_id="9500111" />
    <data hurdle_id="40128" affix_id="9400101" />
    <data hurdle_id="40128" affix_id="9500101" />
    <data hurdle_id="40128" affix_id="9500111" />
    <data hurdle_id="40129" affix_id="9400101" />
    <data hurdle_id="40129" affix_id="9500101" />
    <data hurdle_id="40129" affix_id="9500111" />
    <data hurdle_id="40130" affix_id="9400101" />
    <data hurdle_id="40130" affix_id="9500101" />
    <data hurdle_id="40130" affix_id="9500111" />
    <data hurdle_id="40131" affix_id="9400101" />
    <data hurdle_id="40131" affix_id="9500101" />
    <data hurdle_id="40131" affix_id="9500111" />
    <data hurdle_id="40132" affix_id="9400101" />
    <data hurdle_id="40132" affix_id="9500101" />
    <data hurdle_id="40132" affix_id="9500111" />
    <data hurdle_id="40133" affix_id="9400101" />
    <data hurdle_id="40133" affix_id="9500101" />
    <data hurdle_id="40133" affix_id="9500111" />
    <data hurdle_id="40134" affix_id="9400101" />
    <data hurdle_id="40134" affix_id="9500101" />
    <data hurdle_id="40134" affix_id="9500111" />
    <data hurdle_id="40135" affix_id="9400101" />
    <data hurdle_id="40135" affix_id="9500101" />
    <data hurdle_id="40135" affix_id="9500111" />
    <data hurdle_id="40136" affix_id="9400101" />
    <data hurdle_id="40136" affix_id="9500101" />
    <data hurdle_id="40136" affix_id="9500111" />
    <data hurdle_id="40137" affix_id="9400101" />
    <data hurdle_id="40137" affix_id="9500101" />
    <data hurdle_id="40137" affix_id="9500111" />
    <data hurdle_id="40138" affix_id="9400101" />
    <data hurdle_id="40138" affix_id="9500101" />
    <data hurdle_id="40138" affix_id="9500111" />
    <data hurdle_id="40139" affix_id="9400101" />
    <data hurdle_id="40139" affix_id="9500101" />
    <data hurdle_id="40139" affix_id="9500111" />
    <data hurdle_id="40140" affix_id="9400101" />
    <data hurdle_id="40140" affix_id="9500101" />
    <data hurdle_id="40140" affix_id="9500111" />
    <data hurdle_id="40141" affix_id="9400101" />
    <data hurdle_id="40141" affix_id="9500101" />
    <data hurdle_id="40141" affix_id="9500111" />
    <data hurdle_id="40142" affix_id="9400101" />
    <data hurdle_id="40142" affix_id="9500101" />
    <data hurdle_id="40142" affix_id="9500111" />
    <data hurdle_id="40143" affix_id="9400101" />
    <data hurdle_id="40143" affix_id="9500101" />
    <data hurdle_id="40143" affix_id="9500111" />
    <data hurdle_id="40144" affix_id="9400101" />
    <data hurdle_id="40144" affix_id="9500101" />
    <data hurdle_id="40144" affix_id="9500111" />
    <data hurdle_id="40145" affix_id="9400101" />
    <data hurdle_id="40145" affix_id="9500101" />
    <data hurdle_id="40145" affix_id="9500111" />
    <data hurdle_id="40146" affix_id="9400101" />
    <data hurdle_id="40146" affix_id="9500101" />
    <data hurdle_id="40146" affix_id="9500111" />
    <data hurdle_id="40147" affix_id="9400101" />
    <data hurdle_id="40147" affix_id="9500101" />
    <data hurdle_id="40147" affix_id="9500111" />
    <data hurdle_id="40148" affix_id="9400101" />
    <data hurdle_id="40148" affix_id="9500101" />
    <data hurdle_id="40148" affix_id="9500111" />
    <data hurdle_id="40149" affix_id="9400101" />
    <data hurdle_id="40149" affix_id="9500101" />
    <data hurdle_id="40149" affix_id="9500111" />
    <data hurdle_id="40150" affix_id="9400101" />
    <data hurdle_id="40150" affix_id="9500101" />
    <data hurdle_id="40150" affix_id="9500111" />
    <data hurdle_id="50101" affix_id="9400102" />
    <data hurdle_id="50101" affix_id="9500101" />
    <data hurdle_id="50101" affix_id="9500111" />
    <data hurdle_id="50102" affix_id="9400102" />
    <data hurdle_id="50102" affix_id="9500101" />
    <data hurdle_id="50102" affix_id="9500111" />
    <data hurdle_id="50103" affix_id="9400102" />
    <data hurdle_id="50103" affix_id="9500101" />
    <data hurdle_id="50103" affix_id="9500111" />
    <data hurdle_id="50104" affix_id="9400102" />
    <data hurdle_id="50104" affix_id="9500101" />
    <data hurdle_id="50104" affix_id="9500111" />
    <data hurdle_id="50105" affix_id="9400102" />
    <data hurdle_id="50105" affix_id="9500101" />
    <data hurdle_id="50105" affix_id="9500111" />
    <data hurdle_id="50106" affix_id="9400102" />
    <data hurdle_id="50106" affix_id="9500101" />
    <data hurdle_id="50106" affix_id="9500111" />
    <data hurdle_id="50107" affix_id="9400102" />
    <data hurdle_id="50107" affix_id="9500101" />
    <data hurdle_id="50107" affix_id="9500111" />
    <data hurdle_id="50108" affix_id="9400102" />
    <data hurdle_id="50108" affix_id="9500101" />
    <data hurdle_id="50108" affix_id="9500111" />
    <data hurdle_id="50109" affix_id="9400102" />
    <data hurdle_id="50109" affix_id="9500101" />
    <data hurdle_id="50109" affix_id="9500111" />
    <data hurdle_id="50110" affix_id="9400102" />
    <data hurdle_id="50110" affix_id="9500101" />
    <data hurdle_id="50110" affix_id="9500111" />
    <data hurdle_id="50111" affix_id="9400102" />
    <data hurdle_id="50111" affix_id="9500101" />
    <data hurdle_id="50111" affix_id="9500111" />
    <data hurdle_id="50112" affix_id="9400102" />
    <data hurdle_id="50112" affix_id="9500101" />
    <data hurdle_id="50112" affix_id="9500111" />
    <data hurdle_id="50113" affix_id="9400102" />
    <data hurdle_id="50113" affix_id="9500101" />
    <data hurdle_id="50113" affix_id="9500111" />
    <data hurdle_id="50114" affix_id="9400102" />
    <data hurdle_id="50114" affix_id="9500101" />
    <data hurdle_id="50114" affix_id="9500111" />
    <data hurdle_id="50115" affix_id="9400102" />
    <data hurdle_id="50115" affix_id="9500101" />
    <data hurdle_id="50115" affix_id="9500111" />
    <data hurdle_id="50116" affix_id="9400102" />
    <data hurdle_id="50116" affix_id="9500101" />
    <data hurdle_id="50116" affix_id="9500111" />
    <data hurdle_id="50117" affix_id="9400102" />
    <data hurdle_id="50117" affix_id="9500101" />
    <data hurdle_id="50117" affix_id="9500111" />
    <data hurdle_id="50118" affix_id="9400102" />
    <data hurdle_id="50118" affix_id="9500101" />
    <data hurdle_id="50118" affix_id="9500111" />
    <data hurdle_id="50119" affix_id="9400102" />
    <data hurdle_id="50119" affix_id="9500101" />
    <data hurdle_id="50119" affix_id="9500111" />
    <data hurdle_id="50120" affix_id="9400102" />
    <data hurdle_id="50120" affix_id="9500101" />
    <data hurdle_id="50120" affix_id="9500111" />
    <data hurdle_id="50121" affix_id="9400102" />
    <data hurdle_id="50121" affix_id="9500101" />
    <data hurdle_id="50121" affix_id="9500111" />
    <data hurdle_id="50122" affix_id="9400102" />
    <data hurdle_id="50122" affix_id="9500101" />
    <data hurdle_id="50122" affix_id="9500111" />
    <data hurdle_id="50123" affix_id="9400102" />
    <data hurdle_id="50123" affix_id="9500101" />
    <data hurdle_id="50123" affix_id="9500111" />
    <data hurdle_id="50124" affix_id="9400102" />
    <data hurdle_id="50124" affix_id="9500101" />
    <data hurdle_id="50124" affix_id="9500111" />
    <data hurdle_id="50125" affix_id="9400102" />
    <data hurdle_id="50125" affix_id="9500101" />
    <data hurdle_id="50125" affix_id="9500111" />
    <data hurdle_id="50126" affix_id="9400102" />
    <data hurdle_id="50126" affix_id="9500101" />
    <data hurdle_id="50126" affix_id="9500111" />
    <data hurdle_id="50127" affix_id="9400102" />
    <data hurdle_id="50127" affix_id="9500101" />
    <data hurdle_id="50127" affix_id="9500111" />
    <data hurdle_id="50128" affix_id="9400102" />
    <data hurdle_id="50128" affix_id="9500101" />
    <data hurdle_id="50128" affix_id="9500111" />
    <data hurdle_id="50129" affix_id="9400102" />
    <data hurdle_id="50129" affix_id="9500101" />
    <data hurdle_id="50129" affix_id="9500111" />
    <data hurdle_id="50130" affix_id="9400102" />
    <data hurdle_id="50130" affix_id="9500101" />
    <data hurdle_id="50130" affix_id="9500111" />
    <data hurdle_id="50131" affix_id="9400102" />
    <data hurdle_id="50131" affix_id="9500101" />
    <data hurdle_id="50131" affix_id="9500111" />
    <data hurdle_id="50132" affix_id="9400102" />
    <data hurdle_id="50132" affix_id="9500101" />
    <data hurdle_id="50132" affix_id="9500111" />
    <data hurdle_id="50133" affix_id="9400102" />
    <data hurdle_id="50133" affix_id="9500101" />
    <data hurdle_id="50133" affix_id="9500111" />
    <data hurdle_id="50134" affix_id="9400102" />
    <data hurdle_id="50134" affix_id="9500101" />
    <data hurdle_id="50134" affix_id="9500111" />
    <data hurdle_id="50135" affix_id="9400102" />
    <data hurdle_id="50135" affix_id="9500101" />
    <data hurdle_id="50135" affix_id="9500111" />
    <data hurdle_id="50136" affix_id="9400102" />
    <data hurdle_id="50136" affix_id="9500101" />
    <data hurdle_id="50136" affix_id="9500111" />
    <data hurdle_id="50137" affix_id="9400102" />
    <data hurdle_id="50137" affix_id="9500101" />
    <data hurdle_id="50137" affix_id="9500111" />
    <data hurdle_id="50138" affix_id="9400102" />
    <data hurdle_id="50138" affix_id="9500101" />
    <data hurdle_id="50138" affix_id="9500111" />
    <data hurdle_id="50139" affix_id="9400102" />
    <data hurdle_id="50139" affix_id="9500101" />
    <data hurdle_id="50139" affix_id="9500111" />
    <data hurdle_id="50140" affix_id="9400102" />
    <data hurdle_id="50140" affix_id="9500101" />
    <data hurdle_id="50140" affix_id="9500111" />
    <data hurdle_id="50141" affix_id="9400102" />
    <data hurdle_id="50141" affix_id="9500101" />
    <data hurdle_id="50141" affix_id="9500111" />
    <data hurdle_id="50142" affix_id="9400102" />
    <data hurdle_id="50142" affix_id="9500101" />
    <data hurdle_id="50142" affix_id="9500111" />
    <data hurdle_id="50143" affix_id="9400102" />
    <data hurdle_id="50143" affix_id="9500101" />
    <data hurdle_id="50143" affix_id="9500111" />
    <data hurdle_id="50144" affix_id="9400102" />
    <data hurdle_id="50144" affix_id="9500101" />
    <data hurdle_id="50144" affix_id="9500111" />
    <data hurdle_id="50145" affix_id="9400102" />
    <data hurdle_id="50145" affix_id="9500101" />
    <data hurdle_id="50145" affix_id="9500111" />
    <data hurdle_id="50146" affix_id="9400102" />
    <data hurdle_id="50146" affix_id="9500101" />
    <data hurdle_id="50146" affix_id="9500111" />
    <data hurdle_id="50147" affix_id="9400102" />
    <data hurdle_id="50147" affix_id="9500101" />
    <data hurdle_id="50147" affix_id="9500111" />
    <data hurdle_id="50148" affix_id="9400102" />
    <data hurdle_id="50148" affix_id="9500101" />
    <data hurdle_id="50148" affix_id="9500111" />
    <data hurdle_id="50149" affix_id="9400102" />
    <data hurdle_id="50149" affix_id="9500101" />
    <data hurdle_id="50149" affix_id="9500111" />
    <data hurdle_id="50150" affix_id="9400102" />
    <data hurdle_id="50150" affix_id="9500101" />
    <data hurdle_id="50150" affix_id="9500111" />
    <data hurdle_id="60101" affix_id="9400106" />
    <data hurdle_id="60101" affix_id="9500101" />
    <data hurdle_id="60101" affix_id="9500111" />
    <data hurdle_id="60102" affix_id="9400106" />
    <data hurdle_id="60102" affix_id="9500101" />
    <data hurdle_id="60102" affix_id="9500111" />
    <data hurdle_id="60103" affix_id="9400106" />
    <data hurdle_id="60103" affix_id="9500101" />
    <data hurdle_id="60103" affix_id="9500111" />
    <data hurdle_id="60104" affix_id="9400106" />
    <data hurdle_id="60104" affix_id="9500101" />
    <data hurdle_id="60104" affix_id="9500111" />
    <data hurdle_id="60105" affix_id="9400106" />
    <data hurdle_id="60105" affix_id="9500101" />
    <data hurdle_id="60105" affix_id="9500111" />
    <data hurdle_id="60106" affix_id="9400106" />
    <data hurdle_id="60106" affix_id="9500101" />
    <data hurdle_id="60106" affix_id="9500111" />
    <data hurdle_id="60107" affix_id="9400106" />
    <data hurdle_id="60107" affix_id="9500101" />
    <data hurdle_id="60107" affix_id="9500111" />
    <data hurdle_id="60108" affix_id="9400106" />
    <data hurdle_id="60108" affix_id="9500101" />
    <data hurdle_id="60108" affix_id="9500111" />
    <data hurdle_id="60109" affix_id="9400106" />
    <data hurdle_id="60109" affix_id="9500101" />
    <data hurdle_id="60109" affix_id="9500111" />
    <data hurdle_id="60110" affix_id="9400106" />
    <data hurdle_id="60110" affix_id="9500101" />
    <data hurdle_id="60110" affix_id="9500111" />
    <data hurdle_id="60111" affix_id="9400106" />
    <data hurdle_id="60111" affix_id="9500101" />
    <data hurdle_id="60111" affix_id="9500111" />
    <data hurdle_id="60112" affix_id="9400106" />
    <data hurdle_id="60112" affix_id="9500101" />
    <data hurdle_id="60112" affix_id="9500111" />
    <data hurdle_id="60113" affix_id="9400106" />
    <data hurdle_id="60113" affix_id="9500101" />
    <data hurdle_id="60113" affix_id="9500111" />
    <data hurdle_id="60114" affix_id="9400106" />
    <data hurdle_id="60114" affix_id="9500101" />
    <data hurdle_id="60114" affix_id="9500111" />
    <data hurdle_id="60115" affix_id="9400106" />
    <data hurdle_id="60115" affix_id="9500101" />
    <data hurdle_id="60115" affix_id="9500111" />
    <data hurdle_id="60116" affix_id="9400106" />
    <data hurdle_id="60116" affix_id="9500101" />
    <data hurdle_id="60116" affix_id="9500111" />
    <data hurdle_id="60117" affix_id="9400106" />
    <data hurdle_id="60117" affix_id="9500101" />
    <data hurdle_id="60117" affix_id="9500111" />
    <data hurdle_id="60118" affix_id="9400106" />
    <data hurdle_id="60118" affix_id="9500101" />
    <data hurdle_id="60118" affix_id="9500111" />
    <data hurdle_id="60119" affix_id="9400106" />
    <data hurdle_id="60119" affix_id="9500101" />
    <data hurdle_id="60119" affix_id="9500111" />
    <data hurdle_id="60120" affix_id="9400106" />
    <data hurdle_id="60120" affix_id="9500101" />
    <data hurdle_id="60120" affix_id="9500111" />
    <data hurdle_id="60121" affix_id="9400106" />
    <data hurdle_id="60121" affix_id="9500101" />
    <data hurdle_id="60121" affix_id="9500111" />
    <data hurdle_id="60122" affix_id="9400106" />
    <data hurdle_id="60122" affix_id="9500101" />
    <data hurdle_id="60122" affix_id="9500111" />
    <data hurdle_id="60123" affix_id="9400106" />
    <data hurdle_id="60123" affix_id="9500101" />
    <data hurdle_id="60123" affix_id="9500111" />
    <data hurdle_id="60124" affix_id="9400106" />
    <data hurdle_id="60124" affix_id="9500101" />
    <data hurdle_id="60124" affix_id="9500111" />
    <data hurdle_id="60125" affix_id="9400106" />
    <data hurdle_id="60125" affix_id="9500101" />
    <data hurdle_id="60125" affix_id="9500111" />
    <data hurdle_id="60126" affix_id="9400106" />
    <data hurdle_id="60126" affix_id="9500101" />
    <data hurdle_id="60126" affix_id="9500111" />
    <data hurdle_id="60127" affix_id="9400106" />
    <data hurdle_id="60127" affix_id="9500101" />
    <data hurdle_id="60127" affix_id="9500111" />
    <data hurdle_id="60128" affix_id="9400106" />
    <data hurdle_id="60128" affix_id="9500101" />
    <data hurdle_id="60128" affix_id="9500111" />
    <data hurdle_id="60129" affix_id="9400106" />
    <data hurdle_id="60129" affix_id="9500101" />
    <data hurdle_id="60129" affix_id="9500111" />
    <data hurdle_id="60130" affix_id="9400106" />
    <data hurdle_id="60130" affix_id="9500101" />
    <data hurdle_id="60130" affix_id="9500111" />
    <data hurdle_id="60131" affix_id="9400106" />
    <data hurdle_id="60131" affix_id="9500101" />
    <data hurdle_id="60131" affix_id="9500111" />
    <data hurdle_id="60132" affix_id="9400106" />
    <data hurdle_id="60132" affix_id="9500101" />
    <data hurdle_id="60132" affix_id="9500111" />
    <data hurdle_id="60133" affix_id="9400106" />
    <data hurdle_id="60133" affix_id="9500101" />
    <data hurdle_id="60133" affix_id="9500111" />
    <data hurdle_id="60134" affix_id="9400106" />
    <data hurdle_id="60134" affix_id="9500101" />
    <data hurdle_id="60134" affix_id="9500111" />
    <data hurdle_id="60135" affix_id="9400106" />
    <data hurdle_id="60135" affix_id="9500101" />
    <data hurdle_id="60135" affix_id="9500111" />
    <data hurdle_id="60136" affix_id="9400106" />
    <data hurdle_id="60136" affix_id="9500101" />
    <data hurdle_id="60136" affix_id="9500111" />
    <data hurdle_id="60137" affix_id="9400106" />
    <data hurdle_id="60137" affix_id="9500101" />
    <data hurdle_id="60137" affix_id="9500111" />
    <data hurdle_id="60138" affix_id="9400106" />
    <data hurdle_id="60138" affix_id="9500101" />
    <data hurdle_id="60138" affix_id="9500111" />
    <data hurdle_id="60139" affix_id="9400106" />
    <data hurdle_id="60139" affix_id="9500101" />
    <data hurdle_id="60139" affix_id="9500111" />
    <data hurdle_id="60140" affix_id="9400106" />
    <data hurdle_id="60140" affix_id="9500101" />
    <data hurdle_id="60140" affix_id="9500111" />
    <data hurdle_id="60141" affix_id="9400106" />
    <data hurdle_id="60141" affix_id="9500101" />
    <data hurdle_id="60141" affix_id="9500111" />
    <data hurdle_id="60142" affix_id="9400106" />
    <data hurdle_id="60142" affix_id="9500101" />
    <data hurdle_id="60142" affix_id="9500111" />
    <data hurdle_id="60143" affix_id="9400106" />
    <data hurdle_id="60143" affix_id="9500101" />
    <data hurdle_id="60143" affix_id="9500111" />
    <data hurdle_id="60144" affix_id="9400106" />
    <data hurdle_id="60144" affix_id="9500101" />
    <data hurdle_id="60144" affix_id="9500111" />
    <data hurdle_id="60145" affix_id="9400106" />
    <data hurdle_id="60145" affix_id="9500101" />
    <data hurdle_id="60145" affix_id="9500111" />
    <data hurdle_id="60146" affix_id="9400106" />
    <data hurdle_id="60146" affix_id="9500101" />
    <data hurdle_id="60146" affix_id="9500111" />
    <data hurdle_id="60147" affix_id="9400106" />
    <data hurdle_id="60147" affix_id="9500101" />
    <data hurdle_id="60147" affix_id="9500111" />
    <data hurdle_id="60148" affix_id="9400106" />
    <data hurdle_id="60148" affix_id="9500101" />
    <data hurdle_id="60148" affix_id="9500111" />
    <data hurdle_id="60149" affix_id="9400106" />
    <data hurdle_id="60149" affix_id="9500101" />
    <data hurdle_id="60149" affix_id="9500111" />
    <data hurdle_id="60150" affix_id="9400106" />
    <data hurdle_id="60150" affix_id="9500101" />
    <data hurdle_id="60150" affix_id="9500111" />
</root>
